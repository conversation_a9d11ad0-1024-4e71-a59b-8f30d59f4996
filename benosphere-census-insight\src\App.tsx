
import { Toaster } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { navItems } from "./nav-items";
import GenerateProposal from "./pages/GenerateProposal";
import EmployerInsight from "./pages/EmployerInsight";
import NotFound from "./pages/NotFound";

const queryClient = new QueryClient();

const App = () => (
  <QueryClientProvider client={queryClient}>
    <TooltipProvider>
      <Toaster />
      <BrowserRouter>
        <Routes>
          {navItems.filter(item => item.to !== "*").map(({ to, page }) => (
            <Route key={to} path={to} element={page} />
          ))}
          <Route path="/generate-proposal/:id" element={<GenerateProposal />} />
          <Route path="/employer-insight/:id" element={<EmployerInsight />} />
          <Route path="*" element={<NotFound />} />
        </Routes>
      </BrowserRouter>
    </TooltipProvider>
  </QueryClientProvider>
);

export default App;
