
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle, DialogDescription } from "@/components/ui/dialog";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Tabs, Ta<PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { X, User, Building, Phone, Mail, DollarSign, Users, Shield, Heart, Baby, UserCheck } from "lucide-react";

interface Employee {
  id: number;
  name: string;
  age: number;
  department: string;
  plan: string;
  dependents: number;
  premium: string;
}

interface EmployeeDetailModalProps {
  employee: Employee | null;
  isOpen: boolean;
  onClose: () => void;
}

const EmployeeDetailModal = ({ employee, isOpen, onClose }: EmployeeDetailModalProps) => {
  if (!employee) return null;

  const planFitScore = employee.dependents > 0 ? 85 : 72;
  const planFitLevel = planFitScore >= 80 ? "Excellent Fit" : planFitScore >= 60 ? "Good Fit" : "Needs Review";
  const recommendedPlan = employee.dependents > 0 ? "Gold PPO + Family Dental + Disability" : "Standard PPO + Vision";

  // Mock dependent data based on employee
  const dependentDetails = employee.dependents > 0 ? [
    ...(employee.dependents >= 1 ? [{ 
      name: "Spouse", 
      relationship: "Spouse", 
      age: employee.age - 2, 
      coverage: "Full Health + Dental" 
    }] : []),
    ...(employee.dependents >= 2 ? [{ 
      name: "Child 1", 
      relationship: "Child", 
      age: 12, 
      coverage: "Full Health + Dental + Vision" 
    }] : []),
    ...(employee.dependents >= 3 ? [{ 
      name: "Child 2", 
      relationship: "Child", 
      age: 8, 
      coverage: "Full Health + Dental + Vision" 
    }] : [])
  ] : [];

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-2xl font-bold text-gray-900 flex items-center justify-between">
            Employee Detail: {employee.name}
            <Button variant="ghost" size="sm" onClick={onClose}>
              <X className="h-4 w-4" />
            </Button>
          </DialogTitle>
          <DialogDescription className="text-gray-600">
            Comprehensive plan analysis and recommendations for {employee.name}
          </DialogDescription>
        </DialogHeader>

        <Tabs defaultValue="overview" className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="overview">Overview & Analysis</TabsTrigger>
            <TabsTrigger value="dependents">Family Details</TabsTrigger>
            <TabsTrigger value="recommendations">Recommendations</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-6 mt-6">
            {/* Employee Overview */}
            <Card>
              <CardHeader>
                <CardTitle className="text-xl text-gray-900">Employee Overview</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
                  <div>
                    <p className="text-sm text-gray-600 mb-1">Department</p>
                    <p className="font-semibold text-gray-900">{employee.department}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600 mb-1">Age</p>
                    <p className="font-semibold text-gray-900">{employee.age}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600 mb-1">Salary</p>
                    <p className="font-semibold text-gray-900">$85,000</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600 mb-1">Dependents</p>
                    <p className="font-semibold text-gray-900">{employee.dependents}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Individual Plan Fit Summary */}
            <Card className="border-blue-200">
              <CardHeader>
                <CardTitle className="text-xl text-blue-900 flex items-center">
                  <Shield className="h-5 w-5 mr-2" />
                  Individual Plan Fit Summary
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="bg-green-50 border-l-4 border-green-500 p-4 mb-6">
                  <div className="flex items-center mb-2">
                    <User className="h-5 w-5 text-green-600 mr-2" />
                    <span className="font-semibold text-green-800">Recommended Plan:</span>
                  </div>
                  <p className="text-xl font-bold text-green-700">{recommendedPlan}</p>
                </div>

                <div className="space-y-4">
                  <h4 className="font-semibold text-gray-900">Plan Fit Analysis:</h4>
                  
                  <div className="flex items-center justify-between p-4 bg-green-50 rounded-lg border border-green-200">
                    <div>
                      <div className="w-4 h-4 bg-green-500 rounded-full inline-block mr-3"></div>
                      <span className="font-semibold text-green-800">Gold PPO</span>
                    </div>
                    <div className="text-right">
                      <span className="text-2xl font-bold text-green-600">{planFitScore}%</span>
                      <Badge variant="secondary" className="ml-2 bg-green-100 text-green-800">
                        {planFitLevel}
                      </Badge>
                    </div>
                  </div>
                  <p className="text-sm text-gray-600 ml-7">
                    {employee.dependents > 0 
                      ? "Family with dependents needs comprehensive coverage"
                      : "Individual coverage with good network access"}
                  </p>
                </div>
              </CardContent>
            </Card>

            {/* Current Plan Analysis */}
            <div className="grid md:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg text-gray-900">Current Plan Details</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex justify-between items-center">
                    <span className="text-gray-600">Plan Type:</span>
                    <Badge className="bg-blue-100 text-blue-800">{employee.plan}</Badge>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-gray-600">Monthly Premium:</span>
                    <span className="font-semibold text-green-600">{employee.premium}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-gray-600">Coverage Level:</span>
                    <Badge className="bg-purple-100 text-purple-800">
                      {employee.dependents > 0 ? "Family" : "Individual"}
                    </Badge>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-gray-600">Dependents:</span>
                    <span className="font-semibold">{employee.dependents}</span>
                  </div>
                </CardContent>
              </Card>

              {/* Cost Analysis */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg text-gray-900 flex items-center">
                    <DollarSign className="h-5 w-5 mr-2" />
                    Cost Analysis
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="p-3 bg-blue-50 rounded-lg border border-blue-200">
                      <h4 className="font-semibold text-blue-800 mb-1">Annual Premium Cost</h4>
                      <p className="text-xl font-bold text-blue-600">
                        ${parseInt(employee.premium.replace(/[^\d]/g, '')) * 12}
                      </p>
                      <p className="text-xs text-blue-700">Current plan cost</p>
                    </div>
                    <div className="p-3 bg-green-50 rounded-lg border border-green-200">
                      <h4 className="font-semibold text-green-800 mb-1">Potential Savings</h4>
                      <p className="text-xl font-bold text-green-600">$1,200</p>
                      <p className="text-xs text-green-700">With optimized plan</p>
                    </div>
                    <div className="p-3 bg-orange-50 rounded-lg border border-orange-200">
                      <h4 className="font-semibold text-orange-800 mb-1">Employer Contribution</h4>
                      <p className="text-xl font-bold text-orange-600">75%</p>
                      <p className="text-xs text-orange-700">Of total premium</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="dependents" className="space-y-6 mt-6">
            {employee.dependents > 0 ? (
              <>
                <Card>
                  <CardHeader>
                    <CardTitle className="text-xl text-gray-900 flex items-center">
                      <Users className="h-5 w-5 mr-2" />
                      Family Coverage Details
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid gap-4">
                      {dependentDetails.map((dependent, index) => (
                        <div key={index} className="p-4 bg-gray-50 rounded-lg border border-gray-200">
                          <div className="flex items-center justify-between mb-3">
                            <div className="flex items-center space-x-3">
                              <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                                {dependent.relationship === "Spouse" ? (
                                  <Heart className="h-5 w-5 text-blue-600" />
                                ) : (
                                  <Baby className="h-5 w-5 text-blue-600" />
                                )}
                              </div>
                              <div>
                                <h4 className="font-semibold text-gray-900">{dependent.name}</h4>
                                <p className="text-sm text-gray-600">{dependent.relationship}</p>
                              </div>
                            </div>
                            <Badge className="bg-green-100 text-green-800">
                              <UserCheck className="h-3 w-3 mr-1" />
                              Covered
                            </Badge>
                          </div>
                          <div className="grid grid-cols-2 gap-4 text-sm">
                            <div>
                              <span className="text-gray-600">Age:</span>
                              <span className="ml-2 font-medium">{dependent.age}</span>
                            </div>
                            <div>
                              <span className="text-gray-600">Coverage:</span>
                              <span className="ml-2 font-medium">{dependent.coverage}</span>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>

                <Card className="bg-blue-50 border-blue-200">
                  <CardContent className="p-6">
                    <h3 className="font-semibold text-blue-800 mb-3">Family Coverage Summary</h3>
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <span className="text-blue-700">Total Covered Members:</span>
                        <span className="ml-2 font-bold text-blue-900">{employee.dependents + 1}</span>
                      </div>
                      <div>
                        <span className="text-blue-700">Family Premium:</span>
                        <span className="ml-2 font-bold text-blue-900">{employee.premium}</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </>
            ) : (
              <Card>
                <CardContent className="p-8 text-center">
                  <div className="w-16 h-16 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center">
                    <User className="h-8 w-8 text-gray-400" />
                  </div>
                  <h3 className="text-lg font-semibold text-gray-700 mb-2">Individual Coverage</h3>
                  <p className="text-gray-600">This employee does not have any dependents on their plan.</p>
                </CardContent>
              </Card>
            )}
          </TabsContent>

          <TabsContent value="recommendations" className="space-y-6 mt-6">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg text-gray-900">Personalized Recommendations</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex items-start">
                    <span className="text-green-500 mr-2 mt-1">✓</span>
                    <span className="text-sm text-gray-700">
                      {employee.dependents > 0 
                        ? "Consider adding vision benefits for family coverage"
                        : "Current plan provides adequate coverage for individual needs"}
                    </span>
                  </div>
                  <div className="flex items-start">
                    <span className="text-green-500 mr-2 mt-1">✓</span>
                    <span className="text-sm text-gray-700">
                      Optimize HSA contributions for tax savings
                    </span>
                  </div>
                  <div className="flex items-start">
                    <span className="text-green-500 mr-2 mt-1">✓</span>
                    <span className="text-sm text-gray-700">
                      Consider wellness program participation for premium discounts
                    </span>
                  </div>
                  {employee.dependents > 0 && (
                    <div className="flex items-start">
                      <span className="text-green-500 mr-2 mt-1">✓</span>
                      <span className="text-sm text-gray-700">
                        Family dental benefits could provide additional savings
                      </span>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>
  );
};

export default EmployeeDetailModal;
