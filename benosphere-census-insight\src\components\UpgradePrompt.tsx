
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { useNavigate } from "react-router-dom";
import { Lock, Star, ArrowRight } from "lucide-react";

interface UpgradePromptProps {
  title?: string;
  description?: string;
  showPlanDetails?: boolean;
}

const UpgradePrompt = ({ 
  title = "Upgrade Required",
  description = "You've reached your free report limit. Upgrade to Pro for unlimited access to all reports and insights.",
  showPlanDetails = true
}: UpgradePromptProps) => {
  const navigate = useNavigate();

  return (
    <Card className="bg-gradient-to-br from-amber-50 to-orange-100 border-amber-200 shadow-lg">
      <CardHeader className="text-center pb-4">
        <div className="mx-auto mb-4 p-3 bg-amber-100 rounded-full w-fit">
          <Lock className="h-8 w-8 text-amber-600" />
        </div>
        <CardTitle className="text-xl text-amber-800">{title}</CardTitle>
      </CardHeader>
      <CardContent className="text-center space-y-4">
        <p className="text-amber-700">{description}</p>
        
        {showPlanDetails && (
          <div className="bg-white/50 rounded-lg p-4 space-y-2">
            <div className="flex items-center justify-center space-x-2 text-sm text-amber-700">
              <Star className="h-4 w-4 fill-current" />
              <span>Free Plan: 2 reports/month</span>
            </div>
            <div className="flex items-center justify-center space-x-2 text-sm text-green-700">
              <Star className="h-4 w-4 fill-current" />
              <span>Pro Plan: Unlimited reports</span>
            </div>
          </div>
        )}

        <div className="flex flex-col sm:flex-row gap-3 justify-center">
          <Button 
            onClick={() => navigate('/pricing')}
            className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white"
          >
            <ArrowRight className="h-4 w-4 mr-2" />
            Upgrade to Pro
          </Button>
          <Button 
            variant="outline"
            onClick={() => navigate('/dashboard')}
            className="border-amber-300 text-amber-700 hover:bg-amber-50"
          >
            Back to Dashboard
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};

export default UpgradePrompt;
