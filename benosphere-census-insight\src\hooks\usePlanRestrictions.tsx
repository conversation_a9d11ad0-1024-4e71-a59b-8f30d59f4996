
import { useState, useEffect } from 'react';

export interface PlanRestrictions {
  maxReports: number;
  viewedReports: number;
  canViewReport: (reportId: string) => boolean;
  trackReportView: (reportId: string) => void;
  isAtLimit: boolean;
  reportsRemaining: number;
}

export const usePlanRestrictions = (): PlanRestrictions => {
  const [viewedReports, setViewedReports] = useState<string[]>([]);
  
  // For now, we'll assume free plan with max 2 reports
  const maxReports = 2;

  useEffect(() => {
    // Load viewed reports from localStorage
    const saved = localStorage.getItem('viewedReports');
    if (saved) {
      try {
        setViewedReports(JSON.parse(saved));
      } catch (error) {
        console.error('Error loading viewed reports:', error);
      }
    }
  }, []);

  const canViewReport = (reportId: string) => {
    // Always allow access to reports already viewed
    if (viewedReports.includes(reportId)) {
      return true;
    }
    
    // Check if user has remaining free reports
    return viewedReports.length < maxReports;
  };

  const trackReportView = (reportId: string) => {
    if (!viewedReports.includes(reportId)) {
      const updatedReports = [...viewedReports, reportId];
      setViewedReports(updatedReports);
      localStorage.setItem('viewedReports', JSON.stringify(updatedReports));
    }
  };

  const isAtLimit = viewedReports.length >= maxReports;
  const reportsRemaining = Math.max(0, maxReports - viewedReports.length);

  return {
    maxReports,
    viewedReports: viewedReports.length,
    canViewReport,
    trackReportView,
    isAtLimit,
    reportsRemaining
  };
};
