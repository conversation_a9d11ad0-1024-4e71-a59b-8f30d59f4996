
import { HomeIcon, Building2, <PERSON>, FileText, <PERSON>tings, BarChart3, Upload, CreditCard, PlusCircle } from "lucide-react";
import Index from "./pages/Index";
import BrokerDashboard from "./pages/BrokerDashboard";
import EmployerInsight from "./pages/EmployerInsight";
import HRInsight from "./pages/HRInsight";
import UploadCensus from "./pages/UploadCensus";
import HRUpload from "./pages/HRUpload";
import EmployerInvite from "./pages/EmployerInvite";
import PreviewReport from "./pages/PreviewReport";
import Processing from "./pages/Processing";
import Pricing from "./pages/Pricing";
import Billing from "./pages/Billing";
import GenerateProposal from "./pages/GenerateProposal";
import LoginPrompt from "./pages/LoginPrompt";
import NotFound from "./pages/NotFound";
import FilePreview from "./pages/FilePreview";

export const navItems = [
  {
    title: "Home",
    to: "/",
    icon: <HomeIcon className="h-4 w-4" />,
    page: <Index />,
  },
  {
    title: "Dashboard",
    to: "/dashboard",
    icon: <BarChart3 className="h-4 w-4" />,
    page: <BrokerDashboard />,
  },
  {
    title: "Employer Insight",
    to: "/employer-insight",
    icon: <Building2 className="h-4 w-4" />,
    page: <EmployerInsight />,
  },
  {
    title: "HR Insight",
    to: "/hr-insight",
    icon: <Users className="h-4 w-4" />,
    page: <HRInsight />,
  },
  {
    title: "Upload Census",
    to: "/upload-census",
    icon: <Upload className="h-4 w-4" />,
    page: <UploadCensus />,
  },
  {
    title: "File Preview",
    to: "/file-preview",
    icon: <FileText className="h-4 w-4" />,
    page: <FilePreview />,
  },
  {
    title: "HR Upload",
    to: "/hr-upload",
    icon: <Upload className="h-4 w-4" />,
    page: <HRUpload />,
  },
  {
    title: "Employer Invite",
    to: "/employer-invite",
    icon: <Users className="h-4 w-4" />,
    page: <EmployerInvite />,
  },
  {
    title: "Preview Report",
    to: "/preview-report",
    icon: <FileText className="h-4 w-4" />,
    page: <PreviewReport />,
  },
  {
    title: "Processing",
    to: "/processing",
    icon: <Settings className="h-4 w-4" />,
    page: <Processing />,
  },
  {
    title: "Generate Proposal",
    to: "/generate-proposal",
    icon: <PlusCircle className="h-4 w-4" />,
    page: <GenerateProposal />,
  },
  {
    title: "Pricing",
    to: "/pricing",
    icon: <BarChart3 className="h-4 w-4" />,
    page: <Pricing />,
  },
  {
    title: "Billing",
    to: "/billing",
    icon: <CreditCard className="h-4 w-4" />,
    page: <Billing />,
  },
  {
    title: "Login",
    to: "/login-prompt",
    icon: <Users className="h-4 w-4" />,
    page: <LoginPrompt />,
  },
  {
    title: "Not Found",
    to: "*",
    icon: <FileText className="h-4 w-4" />,
    page: <NotFound />,
  },
];
