
import { Button } from "@/components/ui/button";
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from "@/components/ui/alert-dialog";
import { useNavigate } from "react-router-dom";
import { ArrowLeft, Check, CreditCard, Calendar, DollarSign, FileText, X, ArrowUp, ArrowDown } from "lucide-react";
import { useState } from "react";
import { toast } from "sonner";

const Billing = () => {
  const navigate = useNavigate();
  const [billingCycle, setBillingCycle] = useState<'monthly' | 'annual'>('monthly');
  
  // Mock data - in real app, this would come from your subscription management system
  const [hasActiveSubscription] = useState(true); // Changed to true to show subscription management view
  const [currentPlan] = useState('Pro');
  const [subscriptionStatus] = useState('active');
  const [nextBilling] = useState('2024-08-03');
  const [monthlyAmount] = useState(249);

  const plans = {
    monthly: {
      pro: { price: 249, savings: null },
      agency: { price: 499, savings: null }
    },
    annual: {
      pro: { price: 2490, monthly: 207.50, savings: 498 },
      agency: { price: 4990, monthly: 415.83, savings: 998 }
    }
  };

  const mockInvoices = [
    { id: 'inv_001', date: '2024-07-03', amount: 249, status: 'paid', pdf: '#' },
    { id: 'inv_002', date: '2024-06-03', amount: 249, status: 'paid', pdf: '#' },
    { id: 'inv_003', date: '2024-05-03', amount: 249, status: 'paid', pdf: '#' }
  ];

  const handleSubscribe = (plan: 'pro' | 'agency') => {
    const selectedPlan = plans[billingCycle][plan];
    console.log(`Subscribing to ${plan} plan (${billingCycle}):`, selectedPlan);
    
    // Show a toast notification instead of alert
    toast.success("Redirecting to secure checkout...", {
      description: `${plan.charAt(0).toUpperCase() + plan.slice(1)} plan - ${billingCycle} billing`
    });
    
    // Here you would integrate with your payment processor (Stripe, etc.)
    // For now, we'll just show the success message
    setTimeout(() => {
      toast.info("Payment integration coming soon!", {
        description: "This will redirect to Stripe checkout when connected to a payment processor."
      });
    }, 1500);
  };

  const handlePlanChange = (newPlan: 'pro' | 'agency') => {
    console.log(`Changing plan to ${newPlan}...`);
    toast.success(`Plan change initiated`, {
      description: `Switching to ${newPlan.charAt(0).toUpperCase() + newPlan.slice(1)} plan. Changes will take effect at your next billing cycle.`
    });
  };

  const handleCancelSubscription = () => {
    console.log('Cancelling subscription...');
    toast.success("Subscription cancelled", {
      description: "You will retain access until your current billing period ends."
    });
  };

  const handleUpdatePayment = () => {
    console.log('Updating payment method...');
    toast.info("Redirecting to payment update...", {
      description: "This will open your payment method settings."
    });
  };

  if (hasActiveSubscription) {
    // Subscription Management View
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
        <header className="border-b bg-white/80 backdrop-blur-sm">
          <div className="container mx-auto px-4 py-4 flex justify-between items-center">
            <div className="flex items-center space-x-4">
              <Button variant="ghost" onClick={() => navigate(-1)}>
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back
              </Button>
              <div className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                Billing & Subscription
              </div>
            </div>
          </div>
        </header>

        <main className="container mx-auto px-4 py-8 max-w-4xl">
          {/* Current Subscription */}
          <Card className="mb-8">
            <CardHeader>
              <CardTitle className="flex items-center">
                <CreditCard className="h-5 w-5 mr-2" />
                Current Subscription
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid md:grid-cols-3 gap-6">
                <div>
                  <h3 className="font-semibold text-gray-900">Plan</h3>
                  <p className="text-2xl font-bold text-blue-600">{currentPlan}</p>
                  <p className="text-sm text-gray-600">Monthly billing</p>
                </div>
                <div>
                  <h3 className="font-semibold text-gray-900">Status</h3>
                  <div className="flex items-center mt-1">
                    <div className="h-2 w-2 bg-green-500 rounded-full mr-2"></div>
                    <span className="text-green-600 capitalize font-medium">{subscriptionStatus}</span>
                  </div>
                </div>
                <div>
                  <h3 className="font-semibold text-gray-900">Next Billing</h3>
                  <p className="text-lg font-semibold">${monthlyAmount}</p>
                  <p className="text-sm text-gray-600">on {nextBilling}</p>
                </div>
              </div>
              <div className="flex gap-4 mt-6">
                <Button onClick={handleUpdatePayment} variant="outline">
                  <CreditCard className="h-4 w-4 mr-2" />
                  Update Payment Method
                </Button>
                <AlertDialog>
                  <AlertDialogTrigger asChild>
                    <Button variant="outline" className="text-red-600 border-red-200 hover:bg-red-50">
                      Cancel Subscription
                    </Button>
                  </AlertDialogTrigger>
                  <AlertDialogContent>
                    <AlertDialogHeader>
                      <AlertDialogTitle>Cancel Subscription</AlertDialogTitle>
                      <AlertDialogDescription>
                        Are you sure you want to cancel your subscription? You will lose access to Pro features at the end of your billing period ({nextBilling}). This action cannot be undone.
                      </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                      <AlertDialogCancel>Keep Subscription</AlertDialogCancel>
                      <AlertDialogAction 
                        onClick={handleCancelSubscription}
                        className="bg-red-600 hover:bg-red-700"
                      >
                        Cancel Subscription
                      </AlertDialogAction>
                    </AlertDialogFooter>
                  </AlertDialogContent>
                </AlertDialog>
              </div>
            </CardContent>
          </Card>

          {/* Plan Change Options */}
          <Card className="mb-8">
            <CardHeader>
              <CardTitle className="flex items-center">
                <ArrowUp className="h-5 w-5 mr-2" />
                Change Plan
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid md:grid-cols-2 gap-6">
                {/* Pro Plan */}
                <div className={`p-4 border rounded-lg ${currentPlan === 'Pro' ? 'border-blue-500 bg-blue-50' : 'border-gray-200'}`}>
                  <div className="flex items-center justify-between mb-4">
                    <div>
                      <h3 className="text-lg font-semibold">Pro Plan</h3>
                      <p className="text-2xl font-bold">$249<span className="text-sm font-normal text-gray-600">/month</span></p>
                    </div>
                    {currentPlan === 'Pro' && (
                      <div className="bg-blue-600 text-white px-3 py-1 rounded-full text-sm font-medium">
                        Current Plan
                      </div>
                    )}
                  </div>
                  <div className="space-y-2 text-sm mb-4">
                    <div className="flex items-center">
                      <Check className="h-4 w-4 text-green-600 mr-2" />
                      50 Census uploads/month
                    </div>
                    <div className="flex items-center">
                      <Check className="h-4 w-4 text-green-600 mr-2" />
                      Full enrichment & analytics
                    </div>
                    <div className="flex items-center">
                      <Check className="h-4 w-4 text-green-600 mr-2" />
                      Employer dashboard access
                    </div>
                  </div>
                  {currentPlan !== 'Pro' && (
                    <Button 
                      onClick={() => handlePlanChange('pro')} 
                      className="w-full"
                      variant="outline"
                    >
                      <ArrowDown className="h-4 w-4 mr-2" />
                      Downgrade to Pro
                    </Button>
                  )}
                </div>

                {/* Agency Plan */}
                <div className={`p-4 border rounded-lg ${currentPlan === 'Agency' ? 'border-blue-500 bg-blue-50' : 'border-gray-200'}`}>
                  <div className="flex items-center justify-between mb-4">
                    <div>
                      <h3 className="text-lg font-semibold">Agency Plan</h3>
                      <p className="text-2xl font-bold">$499<span className="text-sm font-normal text-gray-600">/month</span></p>
                    </div>
                    {currentPlan === 'Agency' && (
                      <div className="bg-blue-600 text-white px-3 py-1 rounded-full text-sm font-medium">
                        Current Plan
                      </div>
                    )}
                  </div>
                  <div className="space-y-2 text-sm mb-4">
                    <div className="flex items-center">
                      <Check className="h-4 w-4 text-green-600 mr-2" />
                      Unlimited census uploads
                    </div>
                    <div className="flex items-center">
                      <Check className="h-4 w-4 text-green-600 mr-2" />
                      Multi-user CRM access
                    </div>
                    <div className="flex items-center">
                      <Check className="h-4 w-4 text-green-600 mr-2" />
                      Priority support
                    </div>
                  </div>
                  {currentPlan !== 'Agency' && (
                    <Button 
                      onClick={() => handlePlanChange('agency')} 
                      className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white"
                    >
                      <ArrowUp className="h-4 w-4 mr-2" />
                      Upgrade to Agency
                    </Button>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Billing History */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <FileText className="h-5 w-5 mr-2" />
                Billing History
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {mockInvoices.map((invoice) => (
                  <div key={invoice.id} className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="flex items-center space-x-4">
                      <Calendar className="h-5 w-5 text-gray-400" />
                      <div>
                        <p className="font-medium">{invoice.date}</p>
                        <p className="text-sm text-gray-600">Invoice {invoice.id}</p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-4">
                      <div className="text-right">
                        <p className="font-semibold">${invoice.amount}</p>
                        <div className="flex items-center">
                          <Check className="h-4 w-4 text-green-500 mr-1" />
                          <span className="text-sm text-green-600 capitalize">{invoice.status}</span>
                        </div>
                      </div>
                      <Button variant="outline" size="sm">
                        <FileText className="h-4 w-4 mr-1" />
                        Download
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </main>
      </div>
    );
  }

  // Subscription Selection View
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
      <header className="border-b bg-white/80 backdrop-blur-sm">
        <div className="container mx-auto px-4 py-4 flex justify-between items-center">
          <div className="flex items-center space-x-4">
            <Button variant="ghost" onClick={() => navigate(-1)}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back
            </Button>
            <div className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
              Choose Your Plan
            </div>
          </div>
        </div>
      </header>

      <main className="container mx-auto px-4 py-8 max-w-4xl">
        {/* Billing Cycle Toggle */}
        <div className="text-center mb-8">
          <div className="inline-flex items-center bg-gray-100 rounded-lg p-1">
            <button
              onClick={() => setBillingCycle('monthly')}
              className={`px-4 py-2 rounded-md text-sm font-medium transition-all ${
                billingCycle === 'monthly'
                  ? 'bg-white text-gray-900 shadow-sm'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              Monthly
            </button>
            <button
              onClick={() => setBillingCycle('annual')}
              className={`px-4 py-2 rounded-md text-sm font-medium transition-all ${
                billingCycle === 'annual'
                  ? 'bg-white text-gray-900 shadow-sm'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              Annual
              <span className="ml-1 text-xs bg-green-100 text-green-700 px-1.5 py-0.5 rounded">
                Save 20%
              </span>
            </button>
          </div>
        </div>

        {/* Plan Cards */}
        <div className="grid md:grid-cols-2 gap-8">
          {/* Pro Plan */}
          <Card className="border-2 border-blue-500 shadow-xl relative">
            <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
              <div className="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-4 py-1 rounded-full text-sm font-medium">
                Most Popular
              </div>
            </div>
            <CardHeader className="text-center pb-4">
              <CardTitle className="text-2xl">Pro</CardTitle>
              <div className="mt-4">
                {billingCycle === 'monthly' ? (
                  <>
                    <span className="text-4xl font-bold text-gray-900">${plans.monthly.pro.price}</span>
                    <span className="text-gray-600">/month</span>
                  </>
                ) : (
                  <div>
                    <span className="text-4xl font-bold text-gray-900">${plans.annual.pro.price}</span>
                    <span className="text-gray-600">/year</span>
                    <div className="mt-2">
                      <span className="text-sm text-green-600 font-medium">
                        ${plans.annual.pro.monthly}/month • Save ${plans.annual.pro.savings}/year
                      </span>
                    </div>
                  </div>
                )}
              </div>
              <p className="text-gray-600 mt-2">For growing brokers</p>
            </CardHeader>
            <CardContent className="text-center">
              <Button 
                size="lg" 
                className="w-full mb-6 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white"
                onClick={() => handleSubscribe('pro')}
              >
                Subscribe to Pro
              </Button>
              <div className="text-left space-y-2 text-sm">
                <div className="flex items-center">
                  <Check className="h-4 w-4 text-green-600 mr-2" />
                  50 Census uploads/month
                </div>
                <div className="flex items-center">
                  <Check className="h-4 w-4 text-green-600 mr-2" />
                  Full enrichment & analytics
                </div>
                <div className="flex items-center">
                  <Check className="h-4 w-4 text-green-600 mr-2" />
                  Employer dashboard access
                </div>
                <div className="flex items-center">
                  <Check className="h-4 w-4 text-green-600 mr-2" />
                  AI-powered RFP builder
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Agency Plan */}
          <Card className="shadow-lg">
            <CardHeader className="text-center pb-4">
              <CardTitle className="text-2xl">Agency</CardTitle>
              <div className="mt-4">
                {billingCycle === 'monthly' ? (
                  <>
                    <span className="text-4xl font-bold text-gray-900">${plans.monthly.agency.price}</span>
                    <span className="text-gray-600">/month</span>
                  </>
                ) : (
                  <div>
                    <span className="text-4xl font-bold text-gray-900">${plans.annual.agency.price}</span>
                    <span className="text-gray-600">/year</span>
                    <div className="mt-2">
                      <span className="text-sm text-green-600 font-medium">
                        ${plans.annual.agency.monthly}/month • Save ${plans.annual.agency.savings}/year
                      </span>
                    </div>
                  </div>
                )}
              </div>
              <p className="text-gray-600 mt-2">For larger teams</p>
            </CardHeader>
            <CardContent className="text-center">
              <Button 
                size="lg" 
                variant="outline"
                className="w-full mb-6"
                onClick={() => handleSubscribe('agency')}
              >
                Subscribe to Agency
              </Button>
              <div className="text-left space-y-2 text-sm">
                <div className="flex items-center">
                  <Check className="h-4 w-4 text-green-600 mr-2" />
                  Unlimited census uploads
                </div>
                <div className="flex items-center">
                  <Check className="h-4 w-4 text-green-600 mr-2" />
                  Multi-user CRM access
                </div>
                <div className="flex items-center">
                  <Check className="h-4 w-4 text-green-600 mr-2" />
                  Bulk export capabilities
                </div>
                <div className="flex items-center">
                  <Check className="h-4 w-4 text-green-600 mr-2" />
                  Priority support
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Money Back Guarantee */}
        <div className="text-center mt-8">
          <p className="text-sm text-gray-600">
            💰 30-day money-back guarantee • 🔒 Secure payment • 📞 Cancel anytime
          </p>
        </div>
      </main>
    </div>
  );
};

export default Billing;
