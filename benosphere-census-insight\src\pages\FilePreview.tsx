import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import { useNavigate } from "react-router-dom";
import { Upload, FileText, ArrowLeft, AlertTriangle, CheckCircle, X } from "lucide-react";

interface ValidationError {
  type: 'missing_column' | 'missing_data' | 'invalid_format';
  message: string;
  row?: number;
  column?: string;
}

interface PreviewData {
  headers: string[];
  rows: string[][];
  errors: ValidationError[];
}

const REQUIRED_COLUMNS = [
  'Employee ID',
  'First Name', 
  'Last Name',
  'Date of Birth',
  'Gender',
  'Department',
  'Salary',
  'Hire Date'
];

const FilePreview = () => {
  const navigate = useNavigate();
  const [dragActive, setDragActive] = useState(false);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [previewData, setPreviewData] = useState<PreviewData | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);

  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true);
    } else if (e.type === "dragleave") {
      setDragActive(false);
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);
    
    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      const file = e.dataTransfer.files[0];
      setSelectedFile(file);
      processFile(file);
    }
  };

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];
      setSelectedFile(file);
      processFile(file);
    }
  };

  const processFile = async (file: File) => {
    setIsProcessing(true);
    
    // Simulate file processing - in real app, you'd parse CSV/Excel here
    setTimeout(() => {
      // Mock data for demonstration
      const mockHeaders = ['Employee ID', 'First Name', 'Last Name', 'Age', 'Department', 'Salary'];
      const mockRows = [
        ['001', 'John', 'Doe', '32', 'Engineering', '75000'],
        ['002', 'Jane', 'Smith', '', 'Marketing', '65000'],
        ['003', 'Bob', 'Johnson', '45', 'HR', ''],
        ['004', 'Alice', 'Brown', '28', 'Engineering', '80000'],
      ];

      // Validate data
      const errors: ValidationError[] = [];
      
      // Check for missing required columns
      REQUIRED_COLUMNS.forEach(requiredCol => {
        if (!mockHeaders.includes(requiredCol)) {
          errors.push({
            type: 'missing_column',
            message: `Missing required column: ${requiredCol}`,
            column: requiredCol
          });
        }
      });

      // Check for missing data in rows
      mockRows.forEach((row, rowIndex) => {
        row.forEach((cell, cellIndex) => {
          if (!cell || cell.trim() === '') {
            errors.push({
              type: 'missing_data',
              message: `Missing data in ${mockHeaders[cellIndex]}`,
              row: rowIndex + 1,
              column: mockHeaders[cellIndex]
            });
          }
        });
      });

      setPreviewData({
        headers: mockHeaders,
        rows: mockRows,
        errors
      });
      
      setIsProcessing(false);
    }, 1500);
  };

  const removeFile = () => {
    setSelectedFile(null);
    setPreviewData(null);
  };

  const proceedWithData = () => {
    navigate('/processing');
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
      {/* Header */}
      <header className="border-b bg-white/80 backdrop-blur-sm">
        <div className="container mx-auto px-4 py-4 flex justify-between items-center">
          <div className="flex items-center space-x-4">
            <Button variant="ghost" onClick={() => navigate('/upload-census')}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Upload
            </Button>
            <div className="text-2xl font-bold text-blue-600">BenOsphere</div>
          </div>
          <Button variant="outline">Sign In</Button>
        </div>
      </header>

      <main className="container mx-auto px-4 py-8 max-w-7xl">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            File Preview & Validation
          </h1>
          <p className="text-lg text-gray-600">
            Review your census data and fix any issues before processing
          </p>
        </div>

        {!selectedFile ? (
          <Card className="shadow-xl border-0 max-w-2xl mx-auto">
            <CardHeader>
              <CardTitle className="text-center text-xl">Upload Census File</CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div
                className={`border-2 border-dashed rounded-xl p-8 text-center transition-all ${
                  dragActive 
                    ? "border-blue-500 bg-blue-50" 
                    : "border-gray-300 hover:border-blue-400 hover:bg-gray-50"
                }`}
                onDragEnter={handleDrag}
                onDragLeave={handleDrag}
                onDragOver={handleDrag}
                onDrop={handleDrop}
              >
                <div className="space-y-4">
                  <div className="mx-auto w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                    <FileText className="h-6 w-6 text-blue-600" />
                  </div>
                  <div>
                    <p className="text-lg font-semibold text-gray-700 mb-2">
                      Drag & drop your census file
                    </p>
                    <p className="text-gray-500 mb-4">or</p>
                    <input
                      type="file"
                      id="file-upload"
                      className="hidden"
                      accept=".csv,.xlsx,.xls"
                      onChange={handleFileSelect}
                    />
                    <label htmlFor="file-upload">
                      <Button 
                        variant="outline" 
                        className="cursor-pointer bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white border-0" 
                        asChild
                      >
                        <span>
                          <Upload className="mr-2 h-4 w-4" />
                          Browse Files
                        </span>
                      </Button>
                    </label>
                  </div>
                </div>
              </div>
              <div className="text-center text-sm text-gray-500">
                <p>Supported formats: CSV, Excel (.xlsx, .xls)</p>
                <p>Maximum file size: 50MB</p>
              </div>
            </CardContent>
          </Card>
        ) : (
          <div className="space-y-6">
            {/* File Info */}
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <FileText className="h-8 w-8 text-blue-600" />
                    <div>
                      <p className="font-semibold">{selectedFile.name}</p>
                      <p className="text-sm text-gray-500">
                        {(selectedFile.size / 1024 / 1024).toFixed(2)} MB
                      </p>
                    </div>
                  </div>
                  <Button variant="outline" size="sm" onClick={removeFile}>
                    <X className="h-4 w-4 mr-2" />
                    Remove
                  </Button>
                </div>
              </CardContent>
            </Card>

            {isProcessing ? (
              <Card>
                <CardContent className="p-8 text-center">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
                  <p className="text-lg">Processing file...</p>
                </CardContent>
              </Card>
            ) : previewData && (
              <>
                {/* Validation Results */}
                {previewData.errors.length > 0 ? (
                  <Alert className="border-red-200 bg-red-50">
                    <AlertTriangle className="h-4 w-4" />
                    <AlertDescription>
                      <div className="space-y-2">
                        <p className="font-semibold text-red-800">
                          {previewData.errors.length} validation error(s) found:
                        </p>
                        <ul className="space-y-1">
                          {previewData.errors.map((error, index) => (
                            <li key={index} className="text-sm text-red-700">
                              • {error.message}
                              {error.row && ` (Row ${error.row})`}
                            </li>
                          ))}
                        </ul>
                      </div>
                    </AlertDescription>
                  </Alert>
                ) : (
                  <Alert className="border-green-200 bg-green-50">
                    <CheckCircle className="h-4 w-4" />
                    <AlertDescription className="text-green-800">
                      <p className="font-semibold">Validation passed!</p>
                      <p>Your census file looks good and is ready for processing.</p>
                    </AlertDescription>
                  </Alert>
                )}

                {/* Data Preview */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center justify-between">
                      Data Preview
                      <Badge variant="outline">
                        {previewData.rows.length} employees
                      </Badge>
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="rounded-md border max-h-96 overflow-auto">
                      <Table>
                        <TableHeader>
                          <TableRow>
                            {previewData.headers.map((header, index) => (
                              <TableHead key={index} className="whitespace-nowrap">
                                <div className="flex items-center space-x-2">
                                  <span>{header}</span>
                                  {REQUIRED_COLUMNS.includes(header) ? (
                                    <CheckCircle className="h-3 w-3 text-green-500" />
                                  ) : (
                                    <span className="h-3 w-3" />
                                  )}
                                </div>
                              </TableHead>
                            ))}
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {previewData.rows.map((row, rowIndex) => (
                            <TableRow key={rowIndex}>
                              {row.map((cell, cellIndex) => (
                                <TableCell 
                                  key={cellIndex} 
                                  className={`whitespace-nowrap ${
                                    !cell || cell.trim() === '' ? 'bg-red-50 text-red-700' : ''
                                  }`}
                                >
                                  {cell || <span className="text-red-500 italic">Missing</span>}
                                </TableCell>
                              ))}
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </div>
                  </CardContent>
                </Card>

                {/* Action Buttons */}
                <div className="flex justify-center space-x-4">
                  <Button variant="outline" onClick={removeFile}>
                    Upload Different File
                  </Button>
                  <Button 
                    onClick={proceedWithData}
                    disabled={previewData.errors.length > 0}
                    className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white"
                  >
                    <CheckCircle className="mr-2 h-4 w-4" />
                    Proceed with Data
                  </Button>
                </div>

                {previewData.errors.length > 0 && (
                  <p className="text-center text-sm text-gray-500">
                    Please fix all validation errors before proceeding
                  </p>
                )}
              </>
            )}
          </div>
        )}
      </main>
    </div>
  );
};

export default FilePreview;