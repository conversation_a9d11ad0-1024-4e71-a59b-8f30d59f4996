import { usePara<PERSON>, useNavigate } from "react-router-dom";
import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { ArrowLeft, Download, Calendar, Share2, RefreshCw, BarChart3, Users, AlertTriangle, CheckCircle, Target } from "lucide-react";
import { PieChart, Pie, Cell, ResponsiveContainer, BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip } from "recharts";

const GenerateProposal = () => {
  const { id } = useParams();
  const navigate = useNavigate();

  // Mock data - in real app this would come from API based on ID
  const companyData = {
    "1": {
      companyName: "TechCorp Solutions",
      employees: 43,
      averageAge: 36,
      riskScore: "6.2/10",
      currentPlanType: "PPO + HSA Combo",
      suggestedPlan: "Gold PPO with Disability Coverage",
      expectedSavings: "$127,500",
      analysisDate: "2024-01-15",
      planFitData: [
        { name: "PP<PERSON>", value: 67, color: "#3B82F6" },
        { name: "<PERSON><PERSON>", value: 21, color: "#10B981" },
        { name: "Family/Rx", value: 12, color: "#8B5CF6" }
      ],
      savingsData: [
        { category: "Current Cost", amount: 540000 },
        { category: "Projected Cost", amount: 412500 },
        { category: "Savings", amount: 127500 }
      ],
      keyInsights: [
        "Majority of employees fall in high-utilization age bracket (36 avg age)",
        "Disability coverage crucial due to technology industry risk profile",
        "67% of workforce suits comprehensive PPO coverage",
        "Current HSA utilization below industry average"
      ],
      recommendations: [
        {
          category: "Primary Plan Change",
          description: "Transition to Gold PPO with enhanced disability coverage",
          impact: "Reduce overall costs by 23% while improving coverage"
        },
        {
          category: "Voluntary Benefits",
          description: "Add dental and vision upgrade options",
          impact: "Increase employee satisfaction and retention"
        },
        {
          category: "Add-on Services",
          description: "Include telehealth and mental health support",
          impact: "Preventive care reducing long-term claims costs"
        }
      ],
      industryBenchmark: {
        currentPercentile: 45,
        projectedPercentile: 78,
        avgSavings: "$95,000"
      }
    }
  };

  // Handle case where no ID is provided
  if (!id) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50 flex items-center justify-center">
        <Card className="max-w-md mx-auto">
          <CardContent className="p-6 text-center">
            <h2 className="text-xl font-bold mb-2">No Company Selected</h2>
            <p className="text-gray-600 mb-4">Please select a company to generate a proposal for.</p>
            <Button onClick={() => navigate('/dashboard')}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Dashboard
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  const company = companyData[id as keyof typeof companyData];

  if (!company) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50 flex items-center justify-center">
        <Card className="max-w-md mx-auto">
          <CardContent className="p-6 text-center">
            <h2 className="text-xl font-bold mb-2">Company Not Found</h2>
            <p className="text-gray-600 mb-4">The requested company proposal could not be found.</p>
            <Button onClick={() => navigate('/dashboard')}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Dashboard
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  const COLORS = ['#3B82F6', '#10B981', '#8B5CF6'];

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50">
      {/* Header */}
      <header className="border-b bg-white/90 backdrop-blur-sm sticky top-0 z-50">
        <div className="container mx-auto px-3 sm:px-4 py-3 flex justify-between items-center">
          <div className="flex items-center space-x-4">
            <Button variant="ghost" size="sm" onClick={() => navigate('/dashboard')}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Dashboard
            </Button>
            <div className="text-xl sm:text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
              BenOsphere
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <Button variant="outline" size="sm">
              John Broker
            </Button>
          </div>
        </div>
      </header>

      <main className="container mx-auto px-3 sm:px-4 py-6 max-w-6xl">
        {/* Proposal Header */}
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-gray-900 mb-2">Benefits Proposal</h1>
          <h2 className="text-2xl text-blue-600 font-semibold">{company.companyName}</h2>
          <p className="text-gray-600 mt-2">Generated on {new Date(company.analysisDate).toLocaleDateString()}</p>
        </div>

        {/* 1. Client Overview */}
        <Card className="mb-6 shadow-lg border-0">
          <CardHeader>
            <CardTitle className="flex items-center">
              <div className="bg-blue-100 p-2 rounded-lg mr-3">
                <Users className="h-5 w-5 text-blue-600" />
              </div>
              Client Overview
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
              <div className="text-center">
                <h4 className="text-sm font-medium text-gray-500 mb-1">Total Employees</h4>
                <p className="text-2xl font-bold text-gray-900">{company.employees}</p>
              </div>
              <div className="text-center">
                <h4 className="text-sm font-medium text-gray-500 mb-1">Average Age</h4>
                <p className="text-2xl font-bold text-gray-900">{company.averageAge}</p>
              </div>
              <div className="text-center">
                <h4 className="text-sm font-medium text-gray-500 mb-1">Risk Score</h4>
                <p className="text-2xl font-bold text-gray-900">{company.riskScore}</p>
              </div>
              <div className="text-center">
                <h4 className="text-sm font-medium text-gray-500 mb-1">Current Plan</h4>
                <p className="text-lg font-semibold text-gray-900">{company.currentPlanType}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* 2. Suggested Plan */}
        <Card className="mb-6 shadow-lg border-0">
          <CardHeader>
            <CardTitle className="flex items-center">
              <div className="bg-green-100 p-2 rounded-lg mr-3">
                <Target className="h-5 w-5 text-green-600" />
              </div>
              Recommended Plan
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="bg-gradient-to-r from-yellow-50 to-orange-50 p-6 rounded-lg border-l-4 border-yellow-500 mb-4">
              <h3 className="text-2xl font-bold text-orange-800 mb-2">{company.suggestedPlan}</h3>
              <p className="text-gray-700 mb-4">
                This plan is recommended based on your workforce demographics, current utilization patterns, and industry risk profile. 
                The comprehensive PPO structure with enhanced disability coverage addresses the needs of your technology workforce.
              </p>
              <div className="flex items-center justify-between bg-white/50 p-4 rounded-lg">
                <span className="text-lg font-semibold text-gray-700">Expected Annual Savings</span>
                <span className="text-3xl font-bold text-green-600">{company.expectedSavings}</span>
              </div>
            </div>

            {/* Savings Visualization */}
            <div className="grid md:grid-cols-2 gap-6">
              <div>
                <h4 className="text-lg font-semibold text-gray-900 mb-3">Cost Comparison</h4>
                <ResponsiveContainer width="100%" height={250}>
                  <BarChart data={company.savingsData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="category" />
                    <YAxis />
                    <Tooltip formatter={(value) => [`$${value.toLocaleString()}`, 'Amount']} />
                    <Bar dataKey="amount" fill="#3B82F6" />
                  </BarChart>
                </ResponsiveContainer>
              </div>
              <div className="flex items-center justify-center">
                <div className="text-center">
                  <div className="text-4xl font-bold text-green-600 mb-2">23%</div>
                  <div className="text-lg text-gray-600">Cost Reduction</div>
                  <div className="text-sm text-gray-500 mt-2">vs. Current Plan</div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* 3. Plan Fit Breakdown */}
        <Card className="mb-6 shadow-lg border-0">
          <CardHeader>
            <CardTitle className="flex items-center">
              <div className="bg-purple-100 p-2 rounded-lg mr-3">
                <BarChart3 className="h-5 w-5 text-purple-600" />
              </div>
              Plan Fit Breakdown
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid md:grid-cols-2 gap-6">
              <div>
                <h4 className="text-lg font-semibold text-gray-900 mb-3">Employee Coverage Distribution</h4>
                <ResponsiveContainer width="100%" height={250}>
                  <PieChart>
                    <Pie
                      data={company.planFitData}
                      cx="50%"
                      cy="50%"
                      outerRadius={80}
                      dataKey="value"
                      label={({ name, value }) => `${name}: ${value}%`}
                    >
                      {company.planFitData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.color} />
                      ))}
                    </Pie>
                    <Tooltip />
                  </PieChart>
                </ResponsiveContainer>
              </div>
              <div className="space-y-4">
                {company.planFitData.map((item, index) => (
                  <div key={index} className="flex items-center justify-between p-3 rounded-lg" style={{ backgroundColor: `${item.color}10` }}>
                    <div className="flex items-center">
                      <div className="w-4 h-4 rounded mr-3" style={{ backgroundColor: item.color }}></div>
                      <span className="font-medium">{item.name} Coverage</span>
                    </div>
                    <span className="text-lg font-bold" style={{ color: item.color }}>{item.value}%</span>
                  </div>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* 4. Key Insights */}
        <Card className="mb-6 shadow-lg border-0">
          <CardHeader>
            <CardTitle className="flex items-center">
              <div className="bg-orange-100 p-2 rounded-lg mr-3">
                <AlertTriangle className="h-5 w-5 text-orange-600" />
              </div>
              Key Insights
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid md:grid-cols-2 gap-4">
              {company.keyInsights.map((insight, index) => (
                <div key={index} className="flex items-start p-4 bg-blue-50 rounded-lg">
                  <CheckCircle className="h-5 w-5 text-blue-600 mr-3 mt-0.5 flex-shrink-0" />
                  <p className="text-gray-700">{insight}</p>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* 5. Recommendation Summary */}
        <Card className="mb-6 shadow-lg border-0">
          <CardHeader>
            <CardTitle className="flex items-center">
              <div className="bg-green-100 p-2 rounded-lg mr-3">
                <CheckCircle className="h-5 w-5 text-green-600" />
              </div>
              Recommendation Summary
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {company.recommendations.map((rec, index) => (
                <Card key={index} className="border-l-4 border-l-green-500">
                  <CardContent className="p-4">
                    <h4 className="font-semibold text-gray-900 mb-2">{rec.category}</h4>
                    <p className="text-gray-700 mb-2">{rec.description}</p>
                    <p className="text-sm text-green-600 font-medium">{rec.impact}</p>
                  </CardContent>
                </Card>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Industry Benchmark (Optional Section) */}
        <Card className="mb-6 shadow-lg border-0">
          <CardHeader>
            <CardTitle className="flex items-center">
              <div className="bg-indigo-100 p-2 rounded-lg mr-3">
                <BarChart3 className="h-5 w-5 text-indigo-600" />
              </div>
              Industry Benchmark
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid md:grid-cols-3 gap-6 text-center">
              <div className="p-4 bg-red-50 rounded-lg">
                <h4 className="text-sm font-medium text-gray-500 mb-2">Current Position</h4>
                <p className="text-2xl font-bold text-red-600">{company.industryBenchmark.currentPercentile}th</p>
                <p className="text-sm text-gray-600">Percentile</p>
              </div>
              <div className="p-4 bg-green-50 rounded-lg">
                <h4 className="text-sm font-medium text-gray-500 mb-2">Projected Position</h4>
                <p className="text-2xl font-bold text-green-600">{company.industryBenchmark.projectedPercentile}th</p>
                <p className="text-sm text-gray-600">Percentile</p>
              </div>
              <div className="p-4 bg-blue-50 rounded-lg">
                <h4 className="text-sm font-medium text-gray-500 mb-2">Industry Average Savings</h4>
                <p className="text-2xl font-bold text-blue-600">{company.industryBenchmark.avgSavings}</p>
                <p className="text-sm text-gray-600">Annual</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* 6. Action Buttons */}
        <Card className="bg-gradient-to-r from-blue-100 to-purple-100 border-0 shadow-lg">
          <CardContent className="p-6">
            <h3 className="text-xl font-bold text-gray-900 mb-4 text-center">Next Steps</h3>
            <div className="grid sm:grid-cols-2 lg:grid-cols-4 gap-4">
              <Button className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700">
                <Download className="h-4 w-4 mr-2" />
                Generate PDF
              </Button>
              <Button variant="outline">
                <Calendar className="h-4 w-4 mr-2" />
                Schedule Presentation
              </Button>
              <Button variant="outline">
                <Share2 className="h-4 w-4 mr-2" />
                Share with Client
              </Button>
              <Button variant="outline">
                <RefreshCw className="h-4 w-4 mr-2" />
                Re-run Analysis
              </Button>
            </div>
          </CardContent>
        </Card>
      </main>
    </div>
  );
};

export default GenerateProposal;
