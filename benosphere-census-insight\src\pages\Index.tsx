
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";
import { useNavigate } from "react-router-dom";
import { Upload, Mail, Building, AlertTriangle, CheckCircle, Clock, TrendingDown, FileX, Plus, Calculator, Download } from "lucide-react";

const Index = () => {
  const navigate = useNavigate();
  
  const handleDownloadTemplate = () => {
    // Create a simple CSV template
    const csvContent = "Employee Name,Date of Birth,Gender,Coverage Tier,Annual Salary,Department\nJohn <PERSON>e,1985-06-15,Male,Employee + Spouse,65000,Marketing\nJ<PERSON>,1990-03-22,Female,Employee Only,58000,Sales\nMike Johnson,1982-11-08,Male,Employee + Family,72000,Engineering";
    const blob = new Blob([csvContent], {
      type: 'text/csv'
    });
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = 'census_template.csv';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50">
      {/* Compact Header */}
      <header className="border-b bg-white/90 backdrop-blur-sm sticky top-0 z-50">
        <div className="max-w-6xl mx-auto px-3 sm:px-4 py-3 flex justify-between items-center">
          <button onClick={() => navigate('/')} className="flex items-center space-x-2 hover:scale-105 transition-transform duration-200 cursor-pointer">
            <img src="/lovable-uploads/d04cb642-b8a7-4fa3-a77f-196c0b565a34.png" alt="BenOsphere Logo" className="w-8 h-8" />
            <span className="text-xl sm:text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
              BenOsphere
            </span>
          </button>
          <div className="flex items-center space-x-2 sm:space-x-4">
            <Button variant="ghost" size="sm" onClick={() => navigate('/pricing')} className="hidden sm:inline-flex">
              Pricing
            </Button>
            <Button variant="ghost" size="sm" onClick={() => navigate('/dashboard')} className="hidden sm:inline-flex">
              Dashboard
            </Button>
            <Button variant="outline" size="sm" onClick={() => navigate('/login-prompt')}>
              Sign In
            </Button>
          </div>
        </div>
      </header>

      {/* Hero Section with Border */}
      <section className="border-b border-gray-200/60 bg-gradient-to-br from-slate-50 to-blue-50 relative">
        {/* Modern Hero Section */}
        <main className="max-w-6xl mx-auto px-3 sm:px-4 py-12 sm:py-20">
          {/* Floating decorative elements */}
          <div className="absolute inset-0 overflow-hidden pointer-events-none">
            <div className="absolute top-20 left-10 w-2 h-2 bg-blue-400 rounded-full opacity-40"></div>
            <div className="absolute top-32 right-20 w-2 h-2 bg-purple-400 rounded-full opacity-40"></div>
            <div className="absolute bottom-40 left-20 w-2 h-2 bg-blue-400 rounded-full opacity-40"></div>
            <div className="absolute bottom-20 right-10 w-2 h-2 bg-purple-400 rounded-full opacity-40"></div>
            <div className="absolute top-40 left-1/4 opacity-20">
              <Plus className="w-4 h-4 text-gray-400" />
            </div>
            <div className="absolute top-60 right-1/4 opacity-20">
              <Plus className="w-4 h-4 text-gray-400" />
            </div>
            <div className="absolute bottom-60 left-1/3 opacity-20">
              <Plus className="w-4 h-4 text-gray-400" />
            </div>
            <div className="absolute bottom-40 right-1/3 opacity-20">
              <Plus className="w-4 h-4 text-gray-400" />
            </div>
          </div>

          {/* Main content */}
          <div className="text-center relative z-10 mb-16">
            {/* ICP Label */}
            <div className="mb-6">
              <span className="inline-block bg-blue-100 text-blue-800 text-sm font-medium px-4 py-2 rounded-full">Revenue Engine for SMB Employee Benefit Brokers</span>
            </div>

            <h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold text-gray-900 mb-6 leading-tight">
              Upload a Census.
              <br />
              <span className="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                Unlock Smart Benefits
              </span>
            </h1>
            
            <p className="text-xl sm:text-2xl text-gray-600 mb-8 max-w-3xl mx-auto leading-relaxed">
              BenOsphere transforms raw census files into intelligent, broker-ready reports in under 60 seconds.
            </p>
            
            {/* Primary CTA - Simplified */}
            <div className="flex flex-col items-center space-y-4 mb-8">
              <Button size="lg" className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-8 py-4 text-lg rounded-full shadow-lg hover:shadow-xl transition-all duration-200 hover:scale-105" onClick={() => navigate('/login-prompt')}>
                Upload Census & See Instant Insight
                <Upload className="ml-2 h-5 w-5" />
              </Button>
              <div className="text-center">
                <p className="text-sm text-gray-600 mb-2">Have the file? Give it a spin — it's free.</p>
                <Button variant="ghost" size="sm" className="text-blue-600 hover:text-blue-700 underline text-sm" onClick={handleDownloadTemplate}>
                  <Download className="mr-1 h-4 w-4" />
                  Download Template
                </Button>
              </div>
            </div>
          </div>

          {/* Feature cards with floating effect */}
          <div className="grid md:grid-cols-3 gap-8 relative z-10">
            {/* Left card */}
            <div className="relative">
              <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2">
                <div className="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center mb-4">
                  <Building className="w-6 h-6 text-blue-600" />
                </div>
                <h3 className="font-semibold text-gray-900 mb-2">Save Hours of Manual Work</h3>
                <p className="text-gray-600 text-sm">
                  No more reformatting spreadsheets or chasing employers for data
                </p>
              </div>
            </div>

            {/* Center card with emphasis */}
            <div className="relative">
              <div className="bg-gradient-to-br from-blue-50 to-purple-50 rounded-2xl p-8 shadow-xl border border-blue-100">
                <div className="text-center">
                  <div className="w-16 h-16 bg-gradient-to-r from-blue-600 to-purple-600 rounded-2xl flex items-center justify-center mx-auto mb-4">
                    <CheckCircle className="w-8 h-8 text-white" />
                  </div>
                  <h3 className="text-2xl font-bold text-gray-900 mb-2">
                    Instant Analysis
                  </h3>
                  <p className="text-gray-600">
                    AI-powered insights with demographics, plan recommendations, and risk analysis
                  </p>
                </div>
              </div>
            </div>

            {/* Right card */}
            <div className="relative">
              <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2">
                <div className="w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center mb-4">
                  <CheckCircle className="w-6 h-6 text-green-600" />
                </div>
                <h3 className="font-semibold text-gray-900 mb-2">Win More Deals</h3>
                <p className="text-gray-600 text-sm">
                  Faster turnaround with smart upsell opportunities and cost-saving insights
                </p>
              </div>
            </div>
          </div>
        </main>
      </section>

      {/* Secondary CTA Section - Now clearly separated */}
      <section className="bg-white py-16">
        <div className="max-w-6xl mx-auto px-3 sm:px-4">
          <Card className="bg-gradient-to-br from-purple-50 to-blue-50 border-purple-200 shadow-xl">
            <CardContent className="p-8 text-center">
              <div className="mb-6">
                <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Mail className="w-8 h-8 text-purple-600" />
                </div>
                <h2 className="text-2xl font-bold text-gray-900 mb-3">
                  Don't Have the Census File?
                </h2>
                <p className="text-lg text-gray-600 mb-6 max-w-2xl mx-auto">
                  Send a secure link to your client — they'll upload their census directly. 
                  You'll be notified when their enriched report is ready.
                </p>
              </div>
              
              <Button variant="outline" size="lg" className="px-8 py-4 text-lg rounded-full border-2 border-purple-300 hover:bg-purple-50 hover:border-purple-400 transition-all duration-200" onClick={() => navigate('/employer-invite')}>
                <Mail className="mr-2 h-5 w-5" />
                Invite Employer to Upload
              </Button>
              
              <p className="text-sm text-gray-500 mt-4">
                Secure, guided upload process • HIPAA compliant • Instant notifications
              </p>
            </CardContent>
          </Card>
        </div>
      </section>

      {/* Problem Statement Section */}
      <section className="bg-gray-50 py-12">
        <div className="max-w-6xl mx-auto px-3 sm:px-4">
          <Card className="shadow-lg mb-8 sm:mb-12 border-0 bg-gradient-to-r from-red-50 to-orange-50 border-red-100">
            <CardContent className="p-6 sm:p-8">
              <div className="text-center mb-6">
                <div className="flex items-center justify-center mb-4">
                  <AlertTriangle className="h-8 w-8 text-red-600 mr-3" />
                  <h3 className="text-2xl sm:text-3xl font-bold text-gray-900">
                    The Problem
                  </h3>
                </div>
                <p className="text-lg text-gray-700 mb-6">
                  Manual census workflows are broken.
                </p>
                <p className="text-gray-600 text-base leading-relaxed mb-6">
                  Brokers waste hours reformatting spreadsheets, chasing employers, and entering data by hand — only to miss insights that could win deals.
                </p>
              </div>

              <div className="space-y-4">
                <div className="flex items-center bg-white/70 rounded-lg p-4">
                  <div className="bg-red-100 w-12 h-12 rounded-full flex items-center justify-center mr-4 flex-shrink-0">
                    <Clock className="h-6 w-6 text-red-600" />
                  </div>
                  <div>
                    <h4 className="font-semibold text-red-800 mb-1">Time Drain</h4>
                    <p className="text-gray-600 text-sm">Hours spent on manual data entry and reformatting instead of selling</p>
                  </div>
                </div>
                
                <div className="flex items-center bg-white/70 rounded-lg p-4">
                  <div className="bg-orange-100 w-12 h-12 rounded-full flex items-center justify-center mr-4 flex-shrink-0">
                    <TrendingDown className="h-6 w-6 text-orange-600" />
                  </div>
                  <div>
                    <h4 className="font-semibold text-orange-800 mb-1">Lost Opportunities</h4>
                    <p className="text-gray-600 text-sm">Delays in turnaround mean competitors win the business</p>
                  </div>
                </div>
                
                <div className="flex items-center bg-white/70 rounded-lg p-4">
                  <div className="bg-yellow-100 w-12 h-12 rounded-full flex items-center justify-center mr-4 flex-shrink-0">
                    <FileX className="h-6 w-6 text-yellow-600" />
                  </div>
                  <div>
                    <h4 className="font-semibold text-yellow-800 mb-1">Missed Insights</h4>
                    <p className="text-gray-600 text-sm">Raw data without enrichment means no upsell opportunities</p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </section>

      {/* CensusIQ Capabilities Section */}
      <section className="bg-white py-16">
        <div className="max-w-6xl mx-auto px-3 sm:px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-4">
              CensusIQ Capabilities
            </h2>
            <p className="text-xl text-gray-600">
              Turn data dust into gold.
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
            <div className="bg-white rounded-xl p-6 shadow-lg border border-gray-200 hover:shadow-xl transition-all duration-300">
              <div className="flex items-start space-x-3">
                <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                  <TrendingDown className="w-4 h-4 text-blue-600" />
                </div>
                <div>
                  <h3 className="font-semibold text-gray-900 mb-2">Enrich census data with industry trends and health risk insights</h3>
                  <p className="text-gray-600 text-sm flex items-center">
                    <span className="mr-2">→</span>
                    See beyond basic demographics.
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-xl p-6 shadow-lg border border-gray-200 hover:shadow-xl transition-all duration-300">
              <div className="flex items-start space-x-3">
                <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                  <Plus className="w-4 h-4 text-green-600" />
                </div>
                <div>
                  <h3 className="font-semibold text-gray-900 mb-2">Identify hidden upsell opportunities</h3>
                  <p className="text-gray-600 text-sm flex items-center">
                    <span className="mr-2">→</span>
                    Stop missing revenue chances.
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-xl p-6 shadow-lg border border-gray-200 hover:shadow-xl transition-all duration-300">
              <div className="flex items-start space-x-3">
                <div className="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                  <Building className="w-4 h-4 text-purple-600" />
                </div>
                <div>
                  <h3 className="font-semibold text-gray-900 mb-2">Benchmark benefits against peers</h3>
                  <p className="text-gray-600 text-sm flex items-center">
                    <span className="mr-2">→</span>
                    Stay competitive in your market.
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-xl p-6 shadow-lg border border-gray-200 hover:shadow-xl transition-all duration-300">
              <div className="flex items-start space-x-3">
                <div className="w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                  <Calculator className="w-4 h-4 text-orange-600" />
                </div>
                <div>
                  <h3 className="font-semibold text-gray-900 mb-2">Tie insights to real-dollar savings</h3>
                  <p className="text-gray-600 text-sm flex items-center">
                    <span className="mr-2">→</span>
                    Prove the value of your strategies.
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-xl p-6 shadow-lg border border-gray-200 hover:shadow-xl transition-all duration-300">
              <div className="flex items-start space-x-3">
                <div className="w-8 h-8 bg-indigo-100 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                  <Clock className="w-4 h-4 text-indigo-600" />
                </div>
                <div>
                  <h3 className="font-semibold text-gray-900 mb-2">Keep your strategies fresh year-round</h3>
                  <p className="text-gray-600 text-sm flex items-center">
                    <span className="mr-2">→</span>
                    Avoid insights going stale after renewal.
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-xl p-6 shadow-lg border border-gray-200 hover:shadow-xl transition-all duration-300">
              <div className="flex items-start space-x-3">
                <div className="w-8 h-8 bg-pink-100 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                  <CheckCircle className="w-4 h-4 text-pink-600" />
                </div>
                <div>
                  <h3 className="font-semibold text-gray-900 mb-2">Turn raw data into clear recommendations</h3>
                  <p className="text-gray-600 text-sm flex items-center">
                    <span className="mr-2">→</span>
                    Eliminate guesswork and manual analysis.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Solution Section */}
      <section className="bg-white py-12">
        <div className="max-w-6xl mx-auto px-3 sm:px-4">
          <Card className="shadow-lg mb-8 sm:mb-12 border-0 bg-gradient-to-r from-green-50 to-emerald-50 border-green-100">
            <CardContent className="p-6 sm:p-8">
              <div className="text-center mb-8">
                <div className="flex items-center justify-center mb-4">
                  <CheckCircle className="h-8 w-8 text-green-600 mr-3" />
                  <h3 className="text-2xl sm:text-3xl font-bold text-gray-900">
                    How It Works: Smart Census, Instant Insights
                  </h3>
                </div>
                <p className="text-lg text-gray-700 mb-4">
                  Upload your census file and get powerful insights in under 5 minutes.
                </p>
              </div>

              <div className="grid lg:grid-cols-2 gap-8 items-center">
                {/* How It Works Steps - Left side */}
                <div className="space-y-4">
                  <div className="flex items-start bg-white/70 rounded-lg p-4">
                    <div className="bg-blue-100 w-10 h-10 rounded-full flex items-center justify-center mr-4 flex-shrink-0 mt-1">
                      <span className="text-lg">📤</span>
                    </div>
                    <div>
                      <h4 className="font-semibold mb-2">1. Upload Your Census File</h4>
                      <p className="text-sm text-gray-600">Simply drag and drop your census file or send a secure invitation link directly to your employer.</p>
                    </div>
                  </div>
                  
                  <div className="flex items-start bg-white/70 rounded-lg p-4">
                    <div className="bg-purple-100 w-10 h-10 rounded-full flex items-center justify-center mr-4 flex-shrink-0 mt-1">
                      <span className="text-lg">🤖</span>
                    </div>
                    <div>
                      <h4 className="font-semibold mb-2">2. Instant AI Analysis</h4>
                      <p className="text-sm text-gray-600">Our AI instantly processes and enriches your data with dependent analysis, age demographics, plan recommendations, and risk insights.</p>
                    </div>
                  </div>
                  
                  <div className="flex items-start bg-white/70 rounded-lg p-4">
                    <div className="bg-green-100 w-10 h-10 rounded-full flex items-center justify-center mr-4 flex-shrink-0 mt-1">
                      <span className="text-lg">👀</span>
                    </div>
                    <div>
                      <h4 className="font-semibold mb-2">3. Get Your Smart Preview</h4>
                      <p className="text-sm text-gray-600">View key insights instantly: group size, average age, family coverage patterns, and our recommended plan strategy.</p>
                    </div>
                  </div>
                  
                  <div className="flex items-start bg-white/70 rounded-lg p-4">
                    <div className="bg-orange-100 w-10 h-10 rounded-full flex items-center justify-center mr-4 flex-shrink-0 mt-1">
                      <span className="text-lg">🔓</span>
                    </div>
                    <div>
                      <h4 className="font-semibold mb-2">4. Access Full Report & Insights</h4>
                      <p className="text-sm text-gray-600">Sign in to unlock detailed cost-saving opportunities, contribution benchmarks, and tailored upsell recommendations — ready to present or quote.</p>
                    </div>
                  </div>
                </div>

                {/* Screenshot - Right side */}
                <div>
                  <div className="bg-white/80 rounded-xl p-4 shadow-inner">
                    <img src="/lovable-uploads/8a2481fb-5970-422a-baf1-d4f68dc99416.png" alt="BenOsphere dashboard showing enriched census data and insights" className="w-full h-auto rounded-lg shadow-lg hover:scale-105 transition-transform duration-300" />
                    <p className="text-center text-sm text-gray-500 mt-2">Live census analysis and insights dashboard</p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="bg-white py-16">
        <div className="max-w-6xl mx-auto px-3 sm:px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-4">
              CensusIQ – FAQs for Brokers
            </h2>
            <p className="text-xl text-gray-600">
              Everything you need to know about transforming census data into revenue opportunities.
            </p>
          </div>

          <div className="bg-white rounded-2xl shadow-lg border border-gray-200 p-6 sm:p-8 max-w-4xl mx-auto">
            <Accordion type="single" collapsible className="space-y-2">
              <AccordionItem value="item-1" className="border border-gray-200 rounded-lg">
                <AccordionTrigger className="text-left font-semibold text-gray-900 hover:text-blue-600 px-6 py-4">
                  How is CensusIQ different from tools like Employee Navigator or Nayya?
                </AccordionTrigger>
                <AccordionContent className="text-gray-600 leading-relaxed px-6 pb-4">
                  Employee Navigator stores and manages census data but doesn't analyze it in depth. Nayya helps employees choose benefits but focuses on the individual user experience. CensusIQ is built for brokers—it analyzes census data to uncover revenue opportunities, benchmark benefits, and show cost-saving strategies you can bring to clients.
                </AccordionContent>
              </AccordionItem>

              <AccordionItem value="item-2" className="border border-gray-200 rounded-lg">
                <AccordionTrigger className="text-left font-semibold text-gray-900 hover:text-blue-600 px-6 py-4">
                  I already have census data. Why do I need CensusIQ?
                </AccordionTrigger>
                <AccordionContent className="text-gray-600 leading-relaxed px-6 pb-4">
                  Spreadsheets alone don't drive revenue or prove value. CensusIQ transforms raw census data into insights and specific recommendations—so you can walk into client meetings with actionable ideas, not just data files.
                </AccordionContent>
              </AccordionItem>

              <AccordionItem value="item-3" className="border border-gray-200 rounded-lg">
                <AccordionTrigger className="text-left font-semibold text-gray-900 hover:text-blue-600 px-6 py-4">
                  How does CensusIQ help me save costs or make money?
                </AccordionTrigger>
                <AccordionContent className="text-gray-600 leading-relaxed px-6 pb-4">
                  CensusIQ shows where clients might be overspending and helps you recommend smarter plan designs that save them money. It also flags upsell opportunities, helping you grow revenue without adding more clients.
                </AccordionContent>
              </AccordionItem>

              <AccordionItem value="item-4" className="border border-gray-200 rounded-lg">
                <AccordionTrigger className="text-left font-semibold text-gray-900 hover:text-blue-600 px-6 py-4">
                  We're a small broker agency—why do we need this?
                </AccordionTrigger>
                <AccordionContent className="text-gray-600 leading-relaxed px-6 pb-4">
                  We built CensusIQ exactly for smaller agencies like yours. Big firms have expensive analytics teams and tools that smaller brokers can't afford. CensusIQ levels the playing field, helping you act like a consultant, not just a broker—and deliver insights that set you apart.
                </AccordionContent>
              </AccordionItem>

              <AccordionItem value="item-5" className="border border-gray-200 rounded-lg">
                <AccordionTrigger className="text-left font-semibold text-gray-900 hover:text-blue-600 px-6 py-4">
                  Is CensusIQ expensive or hard to implement?
                </AccordionTrigger>
                <AccordionContent className="text-gray-600 leading-relaxed px-6 pb-4">
                  No. CensusIQ is affordable for brokers of all sizes, with no complex integrations required. You simply upload census files and get insights back quickly.
                </AccordionContent>
              </AccordionItem>

              <AccordionItem value="item-6" className="border border-gray-200 rounded-lg">
                <AccordionTrigger className="text-left font-semibold text-gray-900 hover:text-blue-600 px-6 py-4">
                  Is this only useful during renewal season?
                </AccordionTrigger>
                <AccordionContent className="text-gray-600 leading-relaxed px-6 pb-4">
                  Not at all. CensusIQ gives you insights you can revisit every quarter, helping you stay engaged with clients year-round—and proving your ongoing value beyond just renewals.
                </AccordionContent>
              </AccordionItem>
            </Accordion>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-8">
        <div className="max-w-6xl mx-auto px-3 sm:px-4">
          <div className="flex flex-col sm:flex-row justify-between items-center space-y-4 sm:space-y-0">
            <div className="text-center sm:text-left">
              <button onClick={() => navigate('/')} className="flex items-center space-x-2 hover:scale-105 transition-transform duration-200 cursor-pointer mb-2">
                <span className="text-lg font-bold bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">
                  BenOsphere
                </span>
              </button>
              <p className="text-gray-400 text-sm">Built in Seattle</p>
            </div>
            <div className="flex flex-col sm:flex-row items-center space-y-2 sm:space-y-0 sm:space-x-6">
              <button className="text-gray-300 hover:text-white text-sm transition-colors">
                Privacy Policy
              </button>
              <button className="text-gray-300 hover:text-white text-sm transition-colors">
                Terms & Conditions
              </button>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default Index;
