import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { useNavigate } from "react-router-dom";
import { Check, X, ArrowLeft, Star, Users, FileText, Shield } from "lucide-react";

const Pricing = () => {
  const navigate = useNavigate();

  const features = [
    {
      icon: "📊",
      name: "Census Uploads/Month",
      free: "5",
      pro: "50",
      agency: "Unlimited"
    },
    {
      icon: "🔍",
      name: "Census Parsing & Enrichment",
      free: "Basic (Age, Dependents, Plan Fit)",
      pro: "Full Enrichment",
      agency: "Full Enrichment"
    },
    {
      icon: "📄",
      name: "Smart Group Summary (PDF)",
      free: "Branded Snapshot",
      pro: "Exportable",
      agency: "Bulk Export"
    },
    {
      icon: "🏷️",
      name: "Risk Tagging (e.g. Over 50, Families)",
      free: "Preview Only",
      pro: "Full Detail",
      agency: "Full Detail"
    },
    {
      icon: "💡",
      name: "Plan Fit Suggestion (HMO/PPO/HSA)",
      free: true,
      pro: true,
      agency: true
    },
    {
      icon: "🔖",
      name: "Smart Tags (e.g. Needs Vision, HSA Ready)",
      free: "Limited Tags",
      pro: "Advanced Tags",
      agency: "Advanced Tags"
    },
    {
      icon: "📧",
      name: "Send Report to Employer (Magic Link)",
      free: "2/mo",
      pro: "Unlimited",
      agency: "Unlimited"
    },
    {
      icon: "👥",
      name: "Employer Dashboard Access",
      free: false,
      pro: true,
      agency: true
    },
    {
      icon: "📊",
      name: "Benchmark vs. Similar Groups",
      free: false,
      pro: true,
      agency: true
    },
    {
      icon: "💰",
      name: "Contribution Modeling & Savings Insights",
      free: false,
      pro: true,
      agency: true
    },
    {
      icon: "🔄",
      name: "Renewal Tracking & Nudge Alerts",
      free: false,
      pro: true,
      agency: true
    },
    {
      icon: "📝",
      name: "Broker CRM (Tag + Notes + Group Status)",
      free: "Preview Only",
      pro: "Full CRM",
      agency: "Multi-user CRM"
    },
    {
      icon: "🤖",
      name: "AI-Powered RFP Builder",
      free: false,
      pro: true,
      agency: true
    },
    {
      icon: "💬",
      name: "Slack / MS Teams Integration",
      free: false,
      pro: true,
      agency: true
    }
  ];

  const plans = [
    {
      name: "Free",
      price: "$0",
      period: "",
      description: "Perfect for getting started",
      popular: false,
      buttonText: "Current Plan",
      buttonVariant: "outline" as const,
      onClick: () => {}
    },
    {
      name: "Pro",
      price: "$249",
      period: "/mo",
      description: "For growing brokers",
      popular: true,
      buttonText: "Upgrade to Pro",
      buttonVariant: "default" as const,
      onClick: () => navigate('/billing')
    },
    {
      name: "Agency",
      price: "$499",
      period: "/mo",
      description: "For larger teams",
      popular: false,
      buttonText: "Contact Sales",
      buttonVariant: "outline" as const,
      onClick: () => navigate('/billing')
    }
  ];

  const renderFeatureValue = (feature: any, plan: string) => {
    const value = feature[plan.toLowerCase()];
    
    if (typeof value === "boolean") {
      return value ? (
        <Check className="h-5 w-5 text-green-600 mx-auto" />
      ) : (
        <X className="h-5 w-5 text-red-500 mx-auto" />
      );
    }
    
    return <span className="text-sm text-center block">{value}</span>;
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
      {/* Header */}
      <header className="border-b bg-white/80 backdrop-blur-sm">
        <div className="container mx-auto px-4 py-4 flex justify-between items-center">
          <div className="flex items-center space-x-4">
            <Button variant="ghost" onClick={() => navigate(-1)}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back
            </Button>
            <div className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">BenOsphere</div>
          </div>
          <Button variant="outline">Sign In</Button>
        </div>
      </header>

      <main className="container mx-auto px-4 py-16 max-w-7xl">
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            💎 BenOsphere Broker Plans
          </h1>
          <p className="text-xl text-gray-600 mb-8">
            Choose the plan that fits your business needs
          </p>
        </div>

        {/* Plan Cards */}
        <div className="grid md:grid-cols-3 gap-8 mb-16">
          {plans.map((plan, index) => (
            <Card key={index} className={`relative ${plan.popular ? 'border-2 border-blue-500 shadow-xl' : 'shadow-lg'}`}>
              {plan.popular && (
                <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                  <div className="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-4 py-1 rounded-full text-sm font-medium flex items-center">
                    <Star className="h-4 w-4 mr-1" />
                    Most Popular
                  </div>
                </div>
              )}
              <CardHeader className="text-center pb-4">
                <CardTitle className="text-2xl">{plan.name}</CardTitle>
                <div className="mt-4">
                  <span className="text-4xl font-bold text-gray-900">{plan.price}</span>
                  <span className="text-gray-600">{plan.period}</span>
                </div>
                <p className="text-gray-600 mt-2">{plan.description}</p>
              </CardHeader>
              <CardContent className="text-center">
                <Button 
                  size="lg" 
                  className={`w-full mb-6 ${plan.popular ? 'bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white' : ''}`}
                  variant={plan.buttonVariant}
                  onClick={plan.onClick}
                >
                  {plan.buttonText}
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Feature Comparison Table */}
        <Card className="shadow-2xl">
          <CardHeader>
            <CardTitle className="text-center text-2xl">Feature Comparison</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b">
                    <th className="text-left py-4 px-4 font-semibold">Feature</th>
                    <th className="text-center py-4 px-4 font-semibold">Free</th>
                    <th className="text-center py-4 px-4 font-semibold">Pro ($249/mo)</th>
                    <th className="text-center py-4 px-4 font-semibold">Agency ($499/mo)</th>
                  </tr>
                </thead>
                <tbody>
                  {features.map((feature, index) => (
                    <tr key={index} className="border-b hover:bg-gray-50">
                      <td className="py-4 px-4">
                        <div className="flex items-center">
                          <span className="mr-2">{feature.icon}</span>
                          <span className="font-medium">{feature.name}</span>
                        </div>
                      </td>
                      <td className="py-4 px-4 text-center">
                        {renderFeatureValue(feature, "free")}
                      </td>
                      <td className="py-4 px-4 text-center">
                        {renderFeatureValue(feature, "pro")}
                      </td>
                      <td className="py-4 px-4 text-center">
                        {renderFeatureValue(feature, "agency")}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>

        {/* Upgrade Notice for Free Users */}
        <Card className="mt-8 bg-amber-50 border-amber-200">
          <CardContent className="p-6 text-center">
            <h3 className="text-xl font-bold text-amber-800 mb-2">
              ⚠️ Free Plan Limit Reached
            </h3>
            <p className="text-amber-700 mb-4">
              You've used 5/5 census uploads this month. Upgrade to Pro for unlimited uploads and advanced features.
            </p>
            <Button 
              className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white"
              onClick={() => navigate('/billing')}
            >
              Upgrade Now
            </Button>
          </CardContent>
        </Card>

        {/* FAQ Section */}
        <div className="mt-16 text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-8">
            Frequently Asked Questions
          </h2>
          <div className="grid md:grid-cols-2 gap-8">
            <Card>
              <CardContent className="p-6">
                <h3 className="font-semibold mb-2">Can I change plans anytime?</h3>
                <p className="text-gray-600 text-sm">
                  Yes, you can upgrade or downgrade your plan at any time. Changes take effect immediately.
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-6">
                <h3 className="font-semibold mb-2">Is there a setup fee?</h3>
                <p className="text-gray-600 text-sm">
                  No setup fees. Pay only the monthly subscription fee.
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </main>
    </div>
  );
};

export default Pricing;
