
import { useState } from "react";
import { Button } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { useNavigate } from "react-router-dom";
import { Upload, FileText, ArrowLeft } from "lucide-react";

const UploadCensus = () => {
  const navigate = useNavigate();
  const [dragActive, setDragActive] = useState(false);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);

  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true);
    } else if (e.type === "dragleave") {
      setDragActive(false);
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);
    
    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      setSelectedFile(e.dataTransfer.files[0]);
    }
  };

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      setSelectedFile(e.target.files[0]);
    }
  };

  const handleGenerateReport = () => {
    // Navigate to file preview page for validation
    navigate('/file-preview');
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
      {/* Header */}
      <header className="border-b bg-white/80 backdrop-blur-sm">
        <div className="container mx-auto px-4 py-4 flex justify-between items-center">
          <div className="flex items-center space-x-4">
            <Button variant="ghost" onClick={() => navigate('/')}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back
            </Button>
            <div className="text-2xl font-bold text-blue-600">BenOsphere</div>
          </div>
          <Button variant="outline">Sign In</Button>
        </div>
      </header>

      <main className="container mx-auto px-4 py-16 max-w-4xl">
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            📤 Upload Group Census to Get Instant Insights
          </h1>
          <p className="text-xl text-gray-600">
            Drop in a census file — our AI will scan, enrich, and generate smart benefit recommendations. No formatting required.
          </p>
        </div>

        <Card className="shadow-xl border-0">
          <CardHeader>
            <CardTitle className="text-center text-2xl">Upload Census File</CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Drag & Drop Area */}
            <div
              className={`border-2 border-dashed rounded-xl p-12 text-center transition-all ${
                dragActive 
                  ? "border-blue-500 bg-blue-50" 
                  : "border-gray-300 hover:border-blue-400 hover:bg-gray-50"
              }`}
              onDragEnter={handleDrag}
              onDragLeave={handleDrag}
              onDragOver={handleDrag}
              onDrop={handleDrop}
            >
              <div className="space-y-4">
                <div className="mx-auto w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center">
                  <FileText className="h-8 w-8 text-blue-600" />
                </div>
                
                {selectedFile ? (
                  <div>
                    <p className="text-lg font-semibold text-green-600">
                      ✅ {selectedFile.name}
                    </p>
                    <p className="text-sm text-gray-500">
                      {(selectedFile.size / 1024 / 1024).toFixed(2)} MB
                    </p>
                  </div>
                ) : (
                  <div>
                    <p className="text-xl font-semibold text-gray-700 mb-2">
                      📄 Drag & drop CSV, Excel, or PDF
                    </p>
                    <p className="text-gray-500 mb-4">or</p>
                    <input
                      type="file"
                      id="file-upload"
                      className="hidden"
                      accept=".csv,.xlsx,.xls,.pdf"
                      onChange={handleFileSelect}
                    />
                    <label htmlFor="file-upload">
                      <Button 
                        variant="outline" 
                        className="cursor-pointer bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white border-0" 
                        asChild
                      >
                        <span>
                          🔍 Browse Files
                        </span>
                      </Button>
                    </label>
                  </div>
                )}
              </div>
            </div>

            {/* File Type Support */}
            <div className="text-center text-sm text-gray-500">
              <p>Supported formats: CSV, Excel (.xlsx, .xls), PDF</p>
              <p>Maximum file size: 50MB</p>
            </div>

            {/* Generate Report Button */}
            <div className="text-center">
              <Button 
                size="lg" 
                className="bg-blue-600 hover:bg-blue-700 text-white px-12 py-4 text-lg"
                onClick={handleGenerateReport}
                disabled={!selectedFile}
              >
                <Upload className="mr-2 h-5 w-5" />
                ➡️ Preview & Validate
              </Button>
            </div>

            {!selectedFile && (
              <p className="text-center text-sm text-gray-400">
                Please select a file to generate your report
              </p>
            )}
          </CardContent>
        </Card>

        {/* Sample Data Info */}
        <Card className="mt-8 bg-blue-50 border-blue-200">
          <CardContent className="p-6">
            <h3 className="font-semibold text-blue-900 mb-2">💡 What happens next?</h3>
            <ul className="text-blue-800 space-y-1 text-sm">
              <li>• AI analyzes employee demographics and coverage patterns</li>
              <li>• Identifies cost-saving opportunities and risk factors</li>
              <li>• Generates benchmarking data vs. similar groups</li>
              <li>• Provides personalized plan recommendations</li>
            </ul>
          </CardContent>
        </Card>
      </main>
    </div>
  );
};

export default UploadCensus;
