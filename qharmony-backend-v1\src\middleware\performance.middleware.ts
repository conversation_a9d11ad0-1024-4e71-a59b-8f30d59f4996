import express from 'express';
import logger from '../utils/logger';

/**
 * Performance monitoring middleware
 * Logs request duration and identifies slow endpoints
 */
export const performanceMiddleware = (
  request: express.Request,
  response: express.Response,
  next: express.NextFunction
) => {
  const startTime = Date.now();
  const originalSend = response.send;

  // Override response.send to capture when response is sent
  response.send = function(data: any) {
    const endTime = Date.now();
    const duration = endTime - startTime;
    
    // Log performance metrics
    const logData = {
      method: request.method,
      url: request.originalUrl,
      duration: `${duration}ms`,
      status: response.statusCode,
      userAgent: request.get('User-Agent')?.substring(0, 50) || 'Unknown',
      ip: request.ip || request.connection.remoteAddress,
      userId: request.headers['user-id'] || 'Anonymous'
    };

    // Determine log level based on performance
    if (duration > 2000) {
      logger.error('🐌 VERY SLOW REQUEST', logData);
    } else if (duration > 1000) {
      logger.warn('⚠️  SLOW REQUEST', logData);
    } else if (duration > 500) {
      logger.info('🟡 MODERATE REQUEST', logData);
    } else {
      logger.debug('✅ FAST REQUEST', logData);
    }

    // Add performance headers for debugging
    response.setHeader('X-Response-Time', `${duration}ms`);
    response.setHeader('X-Timestamp', new Date().toISOString());

    // Call original send method
    return originalSend.call(this, data);
  };

  next();
};

/**
 * Specific performance middleware for cached endpoints
 * Adds cache performance metrics
 */
export const cachePerformanceMiddleware = (
  request: express.Request,
  response: express.Response,
  next: express.NextFunction
) => {
  const startTime = Date.now();
  const originalJson = response.json;

  response.json = function(data: any) {
    const endTime = Date.now();
    const duration = endTime - startTime;
    
    // Check if response indicates cache hit/miss
    const isCached = data && typeof data === 'object' && data.cached === true;
    
    const logData = {
      method: request.method,
      url: request.originalUrl,
      duration: `${duration}ms`,
      status: response.statusCode,
      cached: isCached ? 'HIT' : 'MISS',
      userId: request.headers['user-id'] || 'Anonymous'
    };

    // Log cache performance
    if (isCached && duration > 100) {
      logger.warn('🟡 SLOW CACHE HIT', logData);
    } else if (!isCached && duration > 1000) {
      logger.warn('🔴 SLOW CACHE MISS', logData);
    } else if (isCached) {
      logger.debug('⚡ FAST CACHE HIT', logData);
    } else {
      logger.info('💾 CACHE MISS', logData);
    }

    // Add cache headers
    response.setHeader('X-Cache-Status', isCached ? 'HIT' : 'MISS');
    response.setHeader('X-Response-Time', `${duration}ms`);

    return originalJson.call(this, data);
  };

  next();
};

export default performanceMiddleware;
