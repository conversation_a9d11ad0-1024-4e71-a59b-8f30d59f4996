import express from 'express';
import logger from '../utils/logger';

/**
 * Request timing middleware to track complete request lifecycle
 * This helps identify bottlenecks in the request pipeline
 */
export const requestTimingMiddleware = (
  request: express.Request,
  response: express.Response,
  next: express.NextFunction
) => {
  const requestStart = Date.now();
  const requestId = Math.random().toString(36).substring(7);
  
  // Add timing info to request object
  (request as any).timing = {
    start: requestStart,
    requestId: requestId
  };

  // Log request start
  logger.info(`🚀 REQ-${requestId} START: ${request.method} ${request.originalUrl} from ${request.ip}`);

  // Override response methods to capture timing
  const originalSend = response.send;
  const originalJson = response.json;

  response.send = function(data: any) {
    const responseTime = Date.now() - requestStart;
    logger.info(`📤 REQ-${requestId} SEND: ${response.statusCode} - ${responseTime}ms - ${typeof data === 'string' ? data.length : JSON.stringify(data).length} bytes`);
    return originalSend.call(this, data);
  };

  response.json = function(data: any) {
    const responseTime = Date.now() - requestStart;
    const dataSize = JSON.stringify(data).length;
    logger.info(`📤 REQ-${requestId} JSON: ${response.statusCode} - ${responseTime}ms - ${dataSize} bytes`);
    return originalJson.call(this, data);
  };

  // Track when response finishes
  response.on('finish', () => {
    const totalTime = Date.now() - requestStart;
    const logLevel = totalTime > 1000 ? 'warn' : totalTime > 500 ? 'info' : 'debug';
    logger[logLevel](`🏁 REQ-${requestId} FINISH: ${request.method} ${request.originalUrl} - ${response.statusCode} - ${totalTime}ms`);
  });

  // Track if response times out or errors
  response.on('close', () => {
    const totalTime = Date.now() - requestStart;
    if (!response.headersSent) {
      logger.warn(`🔌 REQ-${requestId} CLOSED: Connection closed before response sent - ${totalTime}ms`);
    }
  });

  next();
};

export default requestTimingMiddleware;
