import mongoose, { Document, Model, UpdateWriteOpResult } from 'mongoose';
import {
  PRE_ENROLLMENT_COVERAGE_TYPES,
  PRE_ENROLLMENT_COVERAGE_SUBTYPES,
  PLAN_TYPES,
  CARRIER_STATUSES,
  AM_BEST_RATING_CODES
} from '../../constants';
import { AM_BEST_RATINGS } from '../../constants/amBestRatings';

const { Schema } = mongoose;

// Contact Information Interface
export interface CarrierContactInfo {
  phone?: string;
  email?: string;
  website?: string;
  supportEmail?: string;
  claimsPhone?: string;
  memberServicesPhone?: string;
}

// API Integration Interface
export interface CarrierIntegration {
  ediCapable: boolean;
  apiEndpoint?: string;
  apiVersion?: string;
  authMethod?: string;
  dataFormat?: string; // "EDI", "JSON", "XML"
}

// Main Carrier Data Interface
export interface CarrierDataInterface {
  _id?: mongoose.Types.ObjectId;

  // Basic Information
  carrierName: string;
  carrierCode: string;          // Short code like "BCBS", "AETNA"
  displayName?: string;         // User-friendly display name

  // Ownership (Similar to Plan model)
  brokerId?: string;            // If broker-created, which broker owns it
  brokerageId?: string;         // If broker-created, which brokerage it belongs to
  isSystemCarrier: boolean;     // Whether this is a system-wide carrier (like isTemplate in Plan)

  // Contact Information
  contactInfo: CarrierContactInfo;

  // Supported Plan Types
  supportedPlanTypes: string[]; // ["PPO", "HMO", "HDHP", etc.]

  // Supported Coverage Types (Using Pre-Enrollment Insurance Categories)
  supportedCoverageTypes: string[]; // ["Health Insurance", "Ancillary Benefits", "Life & Disability Insurance", etc.]
  supportedCoverageSubTypes: string[]; // ["Medical", "Dental", "Vision", "Term Life", "Short-Term Disability", etc.]

  // Integration Capabilities
  integration: CarrierIntegration;

  // Business Information
  licenseStates: string[];      // States where carrier is licensed
  amRating: string;            // A.M. Best rating (defaults to 'NR' if not provided)
  networkName?: string;        // Provider network name (e.g., "Choice Plus", "Performance Network")

  // System Fields
  status: string;              // Status from CARRIER_STATUSES
  isActive: boolean;
  isActivated: boolean;        // Aligns with QHarmony pattern

  // Timestamps
  createdAt?: Date;
  updatedAt?: Date;
}

// Updatable fields interface
export interface UpdateableCarrierDataInterface {
  carrierName?: string;
  displayName?: string;
  contactInfo?: CarrierContactInfo;
  supportedPlanTypes?: string[];
  supportedCoverageTypes?: string[];
  supportedCoverageSubTypes?: string[];
  integration?: CarrierIntegration;
  licenseStates?: string[];
  amRating?: string;
  networkName?: string;
  status?: string;
  isActive?: boolean;
  isActivated?: boolean;
}

interface CarrierDocument extends Document, Omit<CarrierDataInterface, '_id'> {}

class CarrierModelClass {
  private static carrierModel: Model<CarrierDocument>;

  public static initializeModel() {
    const schema = new Schema({
      // Basic Information
      carrierName: { type: String, required: true },
      carrierCode: { type: String, required: true }, // 🎯 REMOVED: unique: true (broker-specific validation in controllers)
      displayName: { type: String },

      // Ownership (Similar to Plan model)
      brokerId: { type: String }, // If broker-created, which broker owns it
      brokerageId: { type: String }, // If broker-created, which brokerage it belongs to
      isSystemCarrier: { type: Boolean, default: false }, // Whether this is a system-wide carrier

      // Contact Information
      contactInfo: {
        phone: { type: String },
        email: { type: String },
        website: { type: String },
        supportEmail: { type: String },
        claimsPhone: { type: String },
        memberServicesPhone: { type: String }
      },

      // Supported Plan Types
      supportedPlanTypes: [{
        type: String,
        enum: PLAN_TYPES
      }],

      // Supported Coverage Types (Using Pre-Enrollment Insurance Categories)
      supportedCoverageTypes: [{
        type: String,
        enum: PRE_ENROLLMENT_COVERAGE_TYPES
      }],
      supportedCoverageSubTypes: [{
        type: String,
        enum: PRE_ENROLLMENT_COVERAGE_SUBTYPES
      }],

      // Integration Capabilities
      integration: {
        ediCapable: { type: Boolean, default: false },
        apiEndpoint: { type: String },
        apiVersion: { type: String },
        authMethod: { type: String, enum: ['API_KEY', 'OAUTH', 'BASIC_AUTH', 'CERTIFICATE'] },
        dataFormat: { type: String, enum: ['EDI', 'JSON', 'XML'], default: 'JSON' }
      },

      // Business Information
      licenseStates: [{ type: String }],
      amRating: {
        type: String,
        enum: AM_BEST_RATING_CODES,
        default: 'NR', // Default to "Not Rated" when null/undefined
        validate: {
          validator: function(value: string) {
            // Allow null/undefined (will be set to default 'NR')
            // Otherwise must be a valid rating code
            return !value || AM_BEST_RATING_CODES.includes(value as any);
          },
          message: 'Invalid A.M. Best rating. Must be one of: A++, A+, A, A-, B++, B+, B, B-, C++, C+, C, C-, D, E, F, or NR'
        },
        set: function(value: string | null | undefined) {
          // Convert null/undefined/empty string to 'NR' (Not Rated)
          if (!value || value.trim() === '') {
            return 'NR';
          }
          return value;
        }
      },
      networkName: { type: String },

      // System Fields
      status: {
        type: String,
        enum: CARRIER_STATUSES,
        default: CARRIER_STATUSES[0] // 'Active'
      },
      isActive: { type: Boolean, default: true },
      isActivated: { type: Boolean, default: true }
    }, {
      timestamps: true
    });

    // Add indexes for performance
    schema.index({ carrierCode: 1 });
    schema.index({ carrierName: 1 });
    schema.index({ isActive: 1 });
    schema.index({ supportedCoverageTypes: 1 });
    schema.index({ supportedCoverageSubTypes: 1 });
    schema.index({ brokerId: 1 });
    schema.index({ isSystemCarrier: 1 });

    // 🎯 NEW: Broker-specific uniqueness compound indexes
    schema.index({ brokerId: 1, carrierCode: 1 }, { unique: true });
    schema.index({ brokerId: 1, carrierName: 1 }, { unique: true });

    this.carrierModel = mongoose.model<CarrierDocument>('Carrier', schema);
  }

  // Create a new carrier
  public static async addData(data: CarrierDataInterface): Promise<CarrierDataInterface | null> {
    try {
      const carrier = await this.carrierModel.create(data);
      return carrier as unknown as CarrierDataInterface;
    } catch (error) {
      console.error('Error creating carrier:', error);
      return null;
    }
  }

  // Get all carriers (ADMIN ONLY - use getDataByBrokerId for brokers)
  public static async getAllData(): Promise<CarrierDataInterface[]> {
    try {
      const data = await this.carrierModel.find({ status: { $ne: CARRIER_STATUSES[2] } }) as CarrierDataInterface[]; // Exclude 'Archived'
      return data;
    } catch (error) {
      console.error('Error fetching all carriers:', error);
      return [];
    }
  }

  // Get carriers accessible by broker (system carriers + broker's own carriers)
  public static async getDataByBrokerId(brokerId: string): Promise<CarrierDataInterface[]> {
    try {
      const data = await this.carrierModel.find({
        status: { $ne: CARRIER_STATUSES[2] }, // Exclude 'Archived'
        $or: [
          { isSystemCarrier: true },  // System carriers visible to all brokers
          { brokerId }                // Broker's own carriers
        ]
      }) as CarrierDataInterface[];
      return data;
    } catch (error) {
      console.error('Error fetching carriers by broker ID:', error);
      return [];
    }
  }

  // Get carrier by ID (SECURE - requires brokerId for non-system carriers)
  public static async getDataById(
    id: string,
    brokerId?: string
  ): Promise<CarrierDataInterface | null> {
    try {
      // when brokerId not given implies superadmin is fetching. Super admin should fetch all.
      let query: any = { _id: id };

      // If brokerId provided, ensure broker can only access system carriers or their own
      if (brokerId) {
        query = {
          _id: id,
          status: { $ne: CARRIER_STATUSES[2] }, // Exclude 'Archived'
          $or: [
            { isSystemCarrier: true },  // System carriers accessible to all brokers
            { brokerId }                // Broker's own carriers
          ]
        };
      }

      const data = await this.carrierModel.findOne(query) as CarrierDataInterface;
      return data;
    } catch (error) {
      console.error('Error fetching carrier by ID:', error);
      return null;
    }
  }

  // Get carrier by code
  public static async getDataByCode(carrierCode: string): Promise<CarrierDataInterface | null> {
    try {
      const data = await this.carrierModel.findOne({
        carrierCode,
        status: { $ne: CARRIER_STATUSES[2] } // Exclude 'Archived'
      }) as CarrierDataInterface;
      return data;
    } catch (error) {
      console.error('Error fetching carrier by code:', error);
      return null;
    }
  }

  // Get carriers by coverage type (SECURE - requires brokerId)
  public static async getDataByCoverageType(
    coverageType: string,
    brokerId: string
  ): Promise<CarrierDataInterface[]> {
    try {
      const data = await this.carrierModel.find({
        supportedCoverageTypes: coverageType,
        status: { $ne: 'Archived' },
        $or: [
          { isSystemCarrier: true },  // System carriers accessible to all brokers
          { brokerId }                // Broker's own carriers
        ]
      }) as CarrierDataInterface[];
      return data;
    } catch (error) {
      console.error('Error fetching carriers by coverage type:', error);
      return [];
    }
  }

  // Get carriers by coverage subtype (SECURE - requires brokerId)
  public static async getDataByCoverageSubType(
    coverageSubType: string,
    brokerId: string
  ): Promise<CarrierDataInterface[]> {
    try {
      const data = await this.carrierModel.find({
        supportedCoverageSubTypes: coverageSubType,
        status: { $ne: 'Archived' },
        $or: [
          { isSystemCarrier: true },  // System carriers accessible to all brokers
          { brokerId }                // Broker's own carriers
        ]
      }) as CarrierDataInterface[];
      return data;
    } catch (error) {
      console.error('Error fetching carriers by coverage subtype:', error);
      return [];
    }
  }

  // Update carrier by ID
  public static async updateData({
    id,
    data,
  }: {
    id: string;
    data: Partial<UpdateableCarrierDataInterface>;
  }): Promise<UpdateWriteOpResult> {
    try {
      return await this.carrierModel.updateOne({ _id: id }, data);
    } catch (error) {
      console.error('Error updating carrier:', error);
      throw error;
    }
  }

  // Soft delete carrier
  public static async deleteData(id: string): Promise<UpdateWriteOpResult> {
    try {
      return await this.carrierModel.updateOne({ _id: id }, {
        status: 'Archived',
        isActive: false,
        isActivated: false
      });
    } catch (error) {
      console.error('Error deleting carrier:', error);
      throw error;
    }
  }

  // Get carriers with EDI capability
  public static async getEDICapableCarriers(): Promise<CarrierDataInterface[]> {
    try {
      const data = await this.carrierModel.find({
        'integration.ediCapable': true,
        status: { $ne: 'Archived' }
      }) as CarrierDataInterface[];
      return data;
    } catch (error) {
      console.error('Error fetching EDI capable carriers:', error);
      return [];
    }
  }

  // Get carriers by state (SECURE - requires brokerId)
  public static async getDataByState(
    state: string,
    brokerId: string
  ): Promise<CarrierDataInterface[]> {
    try {
      const data = await this.carrierModel.find({
        licenseStates: state,
        status: { $ne: 'Archived' },
        $or: [
          { isSystemCarrier: true },  // System carriers accessible to all brokers
          { brokerId }                // Broker's own carriers
        ]
      }) as CarrierDataInterface[];
      return data;
    } catch (error) {
      console.error('Error fetching carriers by state:', error);
      return [];
    }
  }

  // Get system carriers only
  public static async getSystemCarriers(): Promise<CarrierDataInterface[]> {
    try {
      const data = await this.carrierModel.find({
        isSystemCarrier: true,
        status: { $ne: 'Archived' }
      }) as CarrierDataInterface[];
      return data;
    } catch (error) {
      console.error('Error fetching system carriers:', error);
      return [];
    }
  }

  // Get broker's own carriers only
  public static async getBrokerCarriers(brokerId: string): Promise<CarrierDataInterface[]> {
    try {
      const data = await this.carrierModel.find({
        brokerId,
        isSystemCarrier: false,
        status: { $ne: 'Archived' }
      }) as CarrierDataInterface[];
      return data;
    } catch (error) {
      console.error('Error fetching broker carriers:', error);
      return [];
    }
  }

  // Get carriers available for plan assignment (Active status only)
  // Used when creating/updating plans - only Active carriers can be assigned
  public static async getAssignableCarriers(brokerId?: string): Promise<CarrierDataInterface[]> {
    try {
      const query: any = {
        status: CARRIER_STATUSES[0]  // 'Active' - Only Active carriers can be assigned to plans
      };

      // If brokerId provided, include broker scope (system carriers + broker's own)
      if (brokerId) {
        query.$or = [
          { isSystemCarrier: true },  // System carriers accessible to all brokers
          { brokerId }                // Broker's own carriers
        ];
      }

      const data = await this.carrierModel.find(query) as CarrierDataInterface[];
      return data;
    } catch (error) {
      console.error('Error fetching assignable carriers:', error);
      return [];
    }
  }

  // Check if carrier exists and is accessible by broker
  public static async validateBrokerAccess(carrierId: string, brokerId: string): Promise<boolean> {
    try {
      const carrier = await this.carrierModel.findOne({
        _id: carrierId,
        status: { $ne: 'Archived' }, // Exclude archived carriers
        $or: [
          { isSystemCarrier: true },  // System carriers accessible to all brokers
          { brokerId }                // Broker's own carriers
        ]
      });
      return !!carrier;
    } catch (error) {
      console.error('Error validating broker access:', error);
      return false;
    }
  }

  // Get carrier count by broker
  public static async getCountByBrokerId(brokerId: string): Promise<number> {
    try {
      return await this.carrierModel.countDocuments({
        brokerId,
        status: { $ne: 'Archived' } // Count all non-archived carriers
      });
    } catch (error) {
      console.error('Error getting carrier count by broker:', error);
      return 0;
    }
  }

  // Validate plan-carrier compatibility
  public static async validatePlanCarrierCompatibility(
    carrierId: string,
    planType?: string,
    coverageType?: string,
    coverageSubTypes?: string[]
  ): Promise<{ isCompatible: boolean; errors: string[] }> {
    try {
      const carrier = await this.carrierModel.findOne({ _id: carrierId });
      if (!carrier) {
        return { isCompatible: false, errors: ['Carrier not found'] };
      }

      const errors: string[] = [];

      // Check plan type compatibility
      if (planType && !carrier.supportedPlanTypes.includes(planType)) {
        errors.push(`Carrier "${carrier.carrierName}" does not support plan type "${planType}"`);
      }

      // Check coverage type compatibility
      if (coverageType && !carrier.supportedCoverageTypes.includes(coverageType)) {
        errors.push(`Carrier "${carrier.carrierName}" does not support coverage type "${coverageType}"`);
      }

      // Check coverage subtypes compatibility (multiple subtypes)
      if (coverageSubTypes && coverageSubTypes.length > 0) {
        for (const subType of coverageSubTypes) {
          if (!carrier.supportedCoverageSubTypes.includes(subType)) {
            errors.push(`Carrier "${carrier.carrierName}" does not support coverage subtype "${subType}"`);
          }
        }
      }

      return { isCompatible: errors.length === 0, errors };
    } catch (error) {
      console.error('Error validating plan-carrier compatibility:', error);
      return { isCompatible: false, errors: ['Error validating compatibility'] };
    }
  }

  // ===== A.M. BEST RATING METHODS =====

  // Get carriers by minimum A.M. Best rating
  public static async getCarriersByMinimumRating(
    minimumRating: string,
    brokerId?: string
  ): Promise<CarrierDataInterface[]> {
    try {
      const minimumScore = AM_BEST_RATINGS[minimumRating as keyof typeof AM_BEST_RATINGS]?.numericScore || 0;

      // Get all carriers first, then filter by rating
      const query: any = { status: { $ne: 'Archived' } };

      if (brokerId) {
        query.$or = [
          { isSystemCarrier: true },
          { brokerId }
        ];
      }

      const carriers = await this.carrierModel.find(query) as CarrierDataInterface[];

      // Filter by rating score
      return carriers.filter(carrier => {
        // Since amRating now defaults to 'NR', it should always exist
        // But we'll still check for safety and exclude 'NR' from minimum rating filters
        if (!carrier.amRating || carrier.amRating === 'NR') return false;
        const carrierScore = AM_BEST_RATINGS[carrier.amRating as keyof typeof AM_BEST_RATINGS]?.numericScore || 0;
        return carrierScore >= minimumScore;
      });

    } catch (error) {
      console.error('Error fetching carriers by minimum rating:', error);
      return [];
    }
  }

  // Validate A.M. Best rating
  public static validateAmBestRating(rating: string): boolean {
    return AM_BEST_RATING_CODES.includes(rating as any);
  }

  // Get carriers with investment grade ratings (A- and above)
  public static async getInvestmentGradeCarriers(brokerId?: string): Promise<CarrierDataInterface[]> {
    const investmentGradeRatings = ['A++', 'A+', 'A', 'A-', 'B++', 'B+'];

    try {
      const query: any = {
        status: { $ne: 'Archived' },
        amRating: { $in: investmentGradeRatings }
      };

      if (brokerId) {
        query.$or = [
          { isSystemCarrier: true },
          { brokerId }
        ];
      }

      const data = await this.carrierModel.find(query) as CarrierDataInterface[];
      return data;
    } catch (error) {
      console.error('Error fetching investment grade carriers:', error);
      return [];
    }
  }

  // ===== STATUS MANAGEMENT METHODS =====

  // Activate carrier
  public static async activateCarrier(
    carrierId: string,
    brokerId?: string
  ): Promise<{ success: boolean; message: string; carrier?: CarrierDataInterface }> {
    try {
      const carrier = await this.getDataById(carrierId, brokerId);
      if (!carrier) {
        return { success: false, message: 'Carrier not found or access denied' };
      }

      if (carrier.status === CARRIER_STATUSES[0]) { // 'Active'
        return { success: false, message: 'Carrier is already active' };
      }

      const updateResult = await this.carrierModel.updateOne(
        { _id: carrierId },
        {
          status: CARRIER_STATUSES[0], // 'Active'
          isActivated: true,
          isActive: true,
          updatedAt: new Date()
        }
      );

      if (updateResult.modifiedCount === 0) {
        return { success: false, message: 'Failed to activate carrier' };
      }

      const updatedCarrier = await this.getDataById(carrierId, brokerId);
      return {
        success: true,
        message: 'Carrier activated successfully',
        carrier: updatedCarrier || undefined
      };

    } catch (error) {
      console.error('Error activating carrier:', error);
      return { success: false, message: 'Internal error activating carrier' };
    }
  }

  // Deactivate carrier (Active -> Inactive)
  public static async deactivateCarrier(
    carrierId: string,
    brokerId?: string
  ): Promise<{ success: boolean; message: string; carrier?: CarrierDataInterface }> {
    try {
      const carrier = await this.getDataById(carrierId, brokerId);
      if (!carrier) {
        return { success: false, message: 'Carrier not found or access denied' };
      }

      if (carrier.status !== CARRIER_STATUSES[0]) { // 'Active'
        return { success: false, message: `Cannot deactivate carrier with status "${carrier.status}". Only Active carriers can be deactivated.` };
      }

      const updateResult = await this.carrierModel.updateOne(
        { _id: carrierId },
        {
          status: CARRIER_STATUSES[1], // 'Inactive'
          isActivated: false,
          isActive: false,
          updatedAt: new Date()
        }
      );

      if (updateResult.modifiedCount === 0) {
        return { success: false, message: 'Failed to deactivate carrier' };
      }

      const updatedCarrier = await this.getDataById(carrierId, brokerId);
      return {
        success: true,
        message: 'Carrier deactivated successfully',
        carrier: updatedCarrier || undefined
      };

    } catch (error) {
      console.error('Error deactivating carrier:', error);
      return { success: false, message: 'Internal error deactivating carrier' };
    }
  }

  // Archive carrier (Any status -> Archived)
  public static async archiveCarrier(
    carrierId: string,
    brokerId?: string
  ): Promise<{ success: boolean; message: string; carrier?: CarrierDataInterface }> {
    try {
      const carrier = await this.getDataById(carrierId, brokerId);
      if (!carrier) {
        return { success: false, message: 'Carrier not found or access denied' };
      }

      if (carrier.status === 'Archived') {
        return { success: false, message: 'Carrier is already archived' };
      }

      const updateResult = await this.carrierModel.updateOne(
        { _id: carrierId },
        {
          status: 'Archived',
          isActivated: false,
          isActive: false,
          updatedAt: new Date()
        }
      );

      if (updateResult.modifiedCount === 0) {
        return { success: false, message: 'Failed to archive carrier' };
      }

      const updatedCarrier = await this.getDataById(carrierId, brokerId);
      return {
        success: true,
        message: 'Carrier archived successfully',
        carrier: updatedCarrier || undefined
      };

    } catch (error) {
      console.error('Error archiving carrier:', error);
      return { success: false, message: 'Internal error archiving carrier' };
    }
  }

  // Get carriers by status (SECURE - requires brokerId)
  public static async getDataByStatus(
    status: string,
    brokerId: string
  ): Promise<CarrierDataInterface[]> {
    try {
      const data = await this.carrierModel.find({
        status,
        $or: [
          { isSystemCarrier: true },  // System carriers visible to all brokers
          { brokerId }                // Broker's own carriers
        ]
      }) as CarrierDataInterface[];
      return data;
    } catch (error) {
      console.error('Error fetching carriers by status:', error);
      return [];
    }
  }

  // Get active carriers only (SECURE - requires brokerId)
  public static async getActiveCarriers(brokerId: string): Promise<CarrierDataInterface[]> {
    return this.getDataByStatus('Active', brokerId);
  }

  // Get inactive carriers only (SECURE - requires brokerId)
  public static async getInactiveCarriers(brokerId: string): Promise<CarrierDataInterface[]> {
    return this.getDataByStatus('Inactive', brokerId);
  }

  // ===== BROKER-SPECIFIC UNIQUENESS VALIDATION =====

  // 🎯 NEW: Validate carrier code uniqueness within broker scope
  public static async validateBrokerUniqueCarrierCode(
    brokerId: string,
    carrierCode: string,
    excludeId?: string
  ): Promise<{ isUnique: boolean; message?: string }> {
    try {
      const query: any = {
        brokerId,
        carrierCode,
        status: { $ne: 'Archived' }
      };

      if (excludeId) {
        query._id = { $ne: excludeId };
      }

      const existingCarrier = await this.carrierModel.findOne(query);

      if (existingCarrier) {
        return {
          isUnique: false,
          message: `Carrier code '${carrierCode}' already exists for this broker`
        };
      }

      return { isUnique: true };
    } catch (error) {
      console.error('Error validating broker unique carrier code:', error);
      return { isUnique: false, message: 'Error validating carrier code uniqueness' };
    }
  }

  // 🎯 NEW: Validate carrier name uniqueness within broker scope
  public static async validateBrokerUniqueCarrierName(
    brokerId: string,
    carrierName: string,
    excludeId?: string
  ): Promise<{ isUnique: boolean; message?: string }> {
    try {
      const query: any = {
        brokerId,
        carrierName,
        status: { $ne: 'Archived' }
      };

      if (excludeId) {
        query._id = { $ne: excludeId };
      }

      const existingCarrier = await this.carrierModel.findOne(query);

      if (existingCarrier) {
        return {
          isUnique: false,
          message: `Carrier name '${carrierName}' already exists for this broker`
        };
      }

      return { isUnique: true };
    } catch (error) {
      console.error('Error validating broker unique carrier name:', error);
      return { isUnique: false, message: 'Error validating carrier name uniqueness' };
    }
  }

  // Get archived carriers only (SECURE - requires brokerId)
  public static async getArchivedCarriers(brokerId: string): Promise<CarrierDataInterface[]> {
    return this.getDataByStatus('Archived', brokerId);
  }

  // Hard delete carrier (for cleanup)
  public static async hardDeleteData(id: string): Promise<void> {
    try {
      await this.carrierModel.deleteOne({ _id: id });
    } catch (error) {
      console.error('Error hard deleting carrier:', error);
      throw error;
    }
  }

  // ===== DEPENDENCY CHAIN REFERENCE COUNTING METHODS =====

  // Get plans that reference this carrier
  public static async getPlansReferencingCarrier(carrierId: string): Promise<any[]> {
    try {
      // Direct query to avoid circular dependency
      const planModel = mongoose.model('Plan');
      const plans = await planModel.find({
        carrierId,
        status: { $ne: 'Archived' } // Exclude archived plans
      });
      return plans;
    } catch (error) {
      console.error('Error getting plans referencing carrier:', error);
      return [];
    }
  }

  // Check if carrier can be edited (not referenced by multiple plans)
  public static async canEditCarrier(carrierId: string): Promise<{ canEdit: boolean; referencedBy: string[]; referenceCount: number }> {
    try {
      const referencingPlans = await this.getPlansReferencingCarrier(carrierId);
      const referenceCount = referencingPlans.length;
      const referencedBy = referencingPlans.map(plan => plan._id?.toString() || '');

      // Can edit if referenced by 0 or 1 plan
      const canEdit = referenceCount <= 1;

      return { canEdit, referencedBy, referenceCount };
    } catch (error) {
      console.error('Error checking if carrier can be edited:', error);
      return { canEdit: false, referencedBy: [], referenceCount: 0 };
    }
  }

  // Check if carrier can be deleted (not referenced by any plans)
  public static async canDeleteCarrier(carrierId: string): Promise<{ canDelete: boolean; referencedBy: string[]; referenceCount: number }> {
    try {
      const referencingPlans = await this.getPlansReferencingCarrier(carrierId);
      const referenceCount = referencingPlans.length;
      const referencedBy = referencingPlans.map(plan => plan._id?.toString() || '');

      // Can delete only if not referenced by any plans
      const canDelete = referenceCount === 0;

      return { canDelete, referencedBy, referenceCount };
    } catch (error) {
      console.error('Error checking if carrier can be deleted:', error);
      return { canDelete: false, referencedBy: [], referenceCount: 0 };
    }
  }

  // Enhanced delete method with reference checking
  public static async deleteDataWithReferenceCheck(carrierId: string): Promise<{ success: boolean; message: string; referencedBy?: string[] }> {
    try {
      const deleteCheck = await this.canDeleteCarrier(carrierId);

      if (!deleteCheck.canDelete) {
        return {
          success: false,
          message: `Cannot delete carrier. It is referenced by ${deleteCheck.referenceCount} plan(s).`,
          referencedBy: deleteCheck.referencedBy
        };
      }

      // Proceed with soft delete
      const deleteResult = await this.deleteData(carrierId);

      if (deleteResult.modifiedCount === 0) {
        return { success: false, message: 'Failed to delete carrier' };
      }

      return { success: true, message: 'Carrier deleted successfully' };
    } catch (error) {
      console.error('Error deleting carrier with reference check:', error);
      return { success: false, message: 'Error deleting carrier' };
    }
  }


}

// Initialize the model
CarrierModelClass.initializeModel();

export default CarrierModelClass;