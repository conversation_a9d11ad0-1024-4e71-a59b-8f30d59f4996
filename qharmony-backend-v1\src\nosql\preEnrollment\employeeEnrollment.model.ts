import mongoose, { Document, Model, UpdateWriteOpResult } from 'mongoose';
import {
  PRE_ENROLLMENT_COVERAGE_TYPES,
  PRE_ENROLLMENT_COVERAGE_SUBTYPES,
  ENROLLMENT_STATUSES,
  EMPLOYEE_CLASS_TYPES,
  ENROLLMENT_PERIOD_TYPES,
  QUALIFYING_LIFE_EVENT_TYPES,
  COVERAGE_TIER_REQUIREMENTS
} from '../../constants';

import UserModelClass from '../user.model';
import CompanyModelClass from '../company.model';
import PlanAssignmentModelClass from './planAssignment.model';
import { CostCalculationService } from '../../services/costCalculationService';

const { Schema } = mongoose;

// 🎯 NEW: Enrolled Dependent Interface (Reference-based with enrollment metadata)
export interface EnrolledDependent {
  dependentId: string;              // Reference to User.details.dependents._id
  enrollmentDate: Date;             // When this dependent was enrolled in THIS plan
  effectiveDate: Date;              // When coverage starts for this dependent
  terminationDate?: Date;           // When coverage ends (if applicable)
  carrierMemberId?: string;         // Insurance carrier member ID
  isActive: boolean;                // Whether this dependent is active in THIS enrollment

  // 🎯 SNAPSHOT: Critical data at enrollment time (for historical accuracy)
  enrollmentSnapshot: {
    name: string;                   // Name at time of enrollment
    dateOfBirth: Date;             // DOB at time of enrollment
    relationship: string;          // Relationship at time of enrollment
    gender: string;                // Gender at time of enrollment
  };
}



// Contribution Interface
export interface Contribution {
  employeeAmount: number;    // What employee pays per month
  employerAmount: number;    // What employer pays per month
  totalAmount: number;       // Total premium per month
}

// Main EmployeeEnrollment Data Interface
export interface EmployeeEnrollmentDataInterface {
  _id?: mongoose.Types.ObjectId;

  // 🎯 CORE RELATIONSHIPS (One enrollment per employee per plan assignment)
  planAssignmentId: string;    // Reference to PlanAssignment (contains all pricing) - refs PlanAssignment._id
  employeeId: string;          // Employee user ID - refs User._id
  companyId: string;           // Company ID (for faster queries) - refs Company._id

  // 🎯 COVERAGE DETAILS (Multiple subtypes in single enrollment)
  coverageType: string;        // Coverage type (e.g., "Your Health") - from Plan
  coverageSubTypes: string[];  // ALL coverage subtypes from Plan (e.g., ["Medical", "Dental", "Vision"])

  // 🎯 EMPLOYEE INFORMATION
  employeeClassType: string;   // "Full-Time", "Part-Time", "Contractor", etc. (from constants)
  // NOTE: employeeAge and employeeSalary moved to User model (single source of truth)

  // 🎯 COVERAGE SELECTION
  coverageTier: string;        // Selected coverage tier (e.g., "Employee Only", "Family")

  // 🎯 CONTRIBUTION DETAILS (Total for ALL coverage subtypes)
  contribution: Contribution;  // Total cost for all subtypes combined

  // 🎯 DEPENDENTS - Reference-based with enrollment metadata
  enrolledDependents: EnrolledDependent[];

  // 🎯 ENROLLMENT STATUS
  status: string;              // "Enrolled", "Waived", "Pending", "Terminated" (from constants)
  enrolledUnder: string;       // "Open Enrollment", "New Hire", "Qualifying Life Event"
  waiveReason?: string;        // Reason for waiving coverage or termination reason
  waiveDate?: Date;            // Date when enrollment was waived

  // 🎯 DATES
  enrollmentDate: Date;        // When the employee enrolled
  effectiveDate: Date;         // When coverage begins
  terminationDate?: Date;      // When coverage ends (if terminated)

  // 🎯 PLAN REFERENCE FIELDS (from Plan Assignment)
  planYear: number;            // Plan year (from plan assignment)
  planEndDate: Date;           // Plan end date (from plan assignment) - determines expiry

  // 🎯 QUALIFYING LIFE EVENT DATA (only if enrolledUnder = "Qualifying Life Event")
  qualifyingLifeEvent?: {
    eventType: string;
    eventDate: Date;
    documentationUrl?: string;
    allowedEnrollmentWindow: {
      start: Date;
      end: Date;
    };
    processedBy?: string;
    processedAt?: Date;
  };

  // 🎯 CARRIER INFORMATION
  carrierMemberId?: string;    // Member ID assigned by carrier

  // 🎯 TIMESTAMPS
  createdAt?: Date;
  updatedAt?: Date;
}

// Updatable fields interface
export interface UpdateableEmployeeEnrollmentDataInterface {
  employeeClassType?: string;
  // NOTE: employeeAge and employeeSalary removed - now in User model
  coverageTier?: string;
  contribution?: Contribution;

  // 🎯 Reference-based dependents
  enrolledDependents?: EnrolledDependent[];

  status?: string;
  enrolledUnder?: string;
  waiveReason?: string;
  waiveDate?: Date;
  effectiveDate?: Date;
  terminationDate?: Date;
  qualifyingLifeEvent?: {
    eventType: string;
    eventDate: Date;
    documentationUrl?: string;
    allowedEnrollmentWindow: {
      start: Date;
      end: Date;
    };
    processedBy?: string;
    processedAt?: Date;
  };
  lastModifiedBy?: string;
  lastModifiedAt?: Date;
  carrierMemberId?: string;
  coverageType?: string;
  coverageSubTypes?: string[];
}

interface EmployeeEnrollmentDocument extends Document, Omit<EmployeeEnrollmentDataInterface, '_id'> {}

class EmployeeEnrollmentModelClass {
  private static employeeEnrollmentModel: Model<EmployeeEnrollmentDocument>;

  public static initializeModel() {
    const schema = new Schema({
      planAssignmentId: {
        type: String,
        ref: 'PlanAssignment',
        required: true
      },
      employeeId: {
        type: String,
        ref: 'User',
        required: true
      },
      companyId: {
        type: String,
        ref: 'Company',
        required: true
      },

      // 🎯 COVERAGE DETAILS (Multiple subtypes in single enrollment)
      coverageType: {
        type: String,
        enum: PRE_ENROLLMENT_COVERAGE_TYPES, // Pre-enrollment coverage types
        required: true
      },
      coverageSubTypes: [{
        type: String,
        enum: PRE_ENROLLMENT_COVERAGE_SUBTYPES, // Pre-enrollment coverage subtypes
        required: true
      }],



      // Employee Information
      employeeClassType: {
        type: String,
        enum: EMPLOYEE_CLASS_TYPES, // Imported from constants.ts
        required: true
      },
      // NOTE: employeeAge and employeeSalary removed - now stored in User model

      coverageTier: { type: String, required: true },

      // Contribution Details
      contribution: {
        employeeAmount: { type: Number, required: true, min: 0 },
        employerAmount: { type: Number, required: true, min: 0 },
        totalAmount: { type: Number, required: true, min: 0 }
      },

      // 🎯 NEW: Reference-based enrolled dependents
      enrolledDependents: [{
        dependentId: { type: String, required: true },  // Reference to User.details.dependents._id
        enrollmentDate: { type: Date, required: true, default: Date.now },
        effectiveDate: { type: Date, required: true },
        terminationDate: { type: Date },
        carrierMemberId: { type: String },
        isActive: { type: Boolean, required: true, default: true },

        // 🎯 SNAPSHOT: Critical data at enrollment time
        enrollmentSnapshot: {
          name: { type: String, required: true },
          dateOfBirth: { type: Date, required: true },
          relationship: {
            type: String,
            enum: ['Spouse', 'Child', 'Domestic Partner', 'Stepchild', 'Adopted Child', 'Other'],
            required: true
          },
          gender: {
            type: String,
            enum: ['Male', 'Female', 'Other', 'Prefer not to say'],
            required: true
          }
        }
      }],



      status: {
        type: String,
        enum: ENROLLMENT_STATUSES, // Imported from constants.ts
        default: 'Pending',
        required: true
      },
      enrolledUnder: {
        type: String,
        enum: ENROLLMENT_PERIOD_TYPES,
        default: 'Open Enrollment',
        required: true
      },
      waiveReason: { type: String },
      waiveDate: { type: Date },

      enrollmentDate: { type: Date, default: Date.now },
      effectiveDate: { type: Date, required: true },
      terminationDate: { type: Date },

      // 🎯 PLAN REFERENCE FIELDS (from Plan Assignment)
      planYear: { type: Number, required: true },
      planEndDate: { type: Date, required: true },

      // 🎯 QUALIFYING LIFE EVENT DATA (only if enrolledUnder = "Qualifying Life Event")
      qualifyingLifeEvent: {
        eventType: {
          type: String,
          enum: QUALIFYING_LIFE_EVENT_TYPES
        },
        eventDate: { type: Date },
        documentationUrl: { type: String },
        allowedEnrollmentWindow: {
          start: { type: Date },
          end: { type: Date }
        },
        processedBy: { type: String },
        processedAt: { type: Date }
      },

      // 🎯 AUDIT TRAIL
      lastModifiedBy: { type: String },
      lastModifiedAt: { type: Date },

      carrierMemberId: { type: String }
    }, {
      timestamps: true // Automatically adds createdAt and updatedAt
    });

    // Add indexes for performance
    schema.index({ employeeId: 1, status: 1 });
    schema.index({ planAssignmentId: 1 });
    schema.index({ companyId: 1 });
    schema.index({ coverageType: 1 });
    schema.index({ coverageSubTypes: 1 });
    schema.index({ coverageType: 1, coverageSubTypes: 1 });
    schema.index({ enrollmentDate: 1 });
    schema.index({ effectiveDate: 1 });
    schema.index({ planYear: 1 });
    schema.index({ planEndDate: 1 });
    schema.index({ status: 1, planEndDate: 1 }); // For expiry queries
    // 🎯 UNIQUE CONSTRAINT: One enrollment per employee per plan assignment
    schema.index({ employeeId: 1, planAssignmentId: 1 }, { unique: true });

    this.employeeEnrollmentModel = mongoose.model<EmployeeEnrollmentDocument>('EmployeeEnrollment', schema);
  }

  // ===== CORE CRUD OPERATIONS =====

  // Create employee enrollment
  public static async addData(data: EmployeeEnrollmentDataInterface): Promise<EmployeeEnrollmentDataInterface | null> {
    try {
      // Ensure plan reference fields are populated
      const enrichedData = await this.enrichWithPlanData(data);
      const enrollment = await this.employeeEnrollmentModel.create(enrichedData);
      return enrollment as unknown as EmployeeEnrollmentDataInterface;
    } catch (error) {
      console.error('Error creating employee enrollment:', error);
      return null;
    }
  }

  /**
   * Enrich enrollment data with plan reference fields from plan assignment
   */
  private static async enrichWithPlanData(data: EmployeeEnrollmentDataInterface): Promise<EmployeeEnrollmentDataInterface> {
    try {
      // If plan reference fields are already provided, use them
      if (data.planYear && data.planEndDate) {
        return data;
      }

      // Get plan assignment to extract plan reference data
      const planAssignment = await PlanAssignmentModelClass.getDataById(data.planAssignmentId);
      if (!planAssignment) {
        throw new Error('Plan assignment not found');
      }

      return {
        ...data,
        planYear: planAssignment.assignmentYear,
        planEndDate: new Date(planAssignment.planEndDate)
      };
    } catch (error) {
      console.error('Error enriching enrollment with plan data:', error);
      // Return original data if enrichment fails
      return data;
    }
  }

  // Get all employee enrollments (ADMIN ONLY) - excludes expired by default
  public static async getAllData(): Promise<EmployeeEnrollmentDataInterface[]> {
    try {
      const data = await this.employeeEnrollmentModel.find({
        status: { $ne: 'Expired' }
      }) as EmployeeEnrollmentDataInterface[];
      return data;
    } catch (error) {
      console.error('Error fetching all employee enrollments:', error);
      return [];
    }
  }

  // Get all employee enrollments including expired (ADMIN ONLY)
  public static async getAllDataIncludeExpired(): Promise<EmployeeEnrollmentDataInterface[]> {
    try {
      const data = await this.employeeEnrollmentModel.find() as EmployeeEnrollmentDataInterface[];
      return data;
    } catch (error) {
      console.error('Error fetching all employee enrollments (include expired):', error);
      return [];
    }
  }

  // Get employee enrollment by ID
  public static async getDataById(id: string): Promise<EmployeeEnrollmentDataInterface | null> {
    try {
      const data = await this.employeeEnrollmentModel.findOne({ _id: id }) as EmployeeEnrollmentDataInterface;
      return data;
    } catch (error) {
      console.error('Error fetching employee enrollment by ID:', error);
      return null;
    }
  }

  // Get enrollments by employee ID - excludes expired by default
  public static async getDataByEmployeeId(employeeId: string): Promise<EmployeeEnrollmentDataInterface[]> {
    try {
      const data = await this.employeeEnrollmentModel.find({
        employeeId,
        status: { $ne: 'Expired' }
      }) as EmployeeEnrollmentDataInterface[];
      return data;
    } catch (error) {
      console.error('Error fetching enrollments by employee ID:', error);
      return [];
    }
  }

  // Get enrollments by company ID - excludes expired by default
  public static async getDataByCompanyId(companyId: string): Promise<EmployeeEnrollmentDataInterface[]> {
    try {
      const data = await this.employeeEnrollmentModel.find({
        companyId,
        status: { $ne: 'Expired' }
      }) as EmployeeEnrollmentDataInterface[];
      return data;
    } catch (error) {
      console.error('Error fetching enrollments by company ID:', error);
      return [];
    }
  }

  // Get enrollments by plan assignment ID - excludes expired by default
  public static async getDataByPlanAssignmentId(planAssignmentId: string): Promise<EmployeeEnrollmentDataInterface[]> {
    try {
      const data = await this.employeeEnrollmentModel.find({
        planAssignmentId,
        status: { $ne: 'Expired' }
      }) as EmployeeEnrollmentDataInterface[];
      return data;
    } catch (error) {
      console.error('Error fetching enrollments by plan assignment ID:', error);
      return [];
    }
  }

  // Get enrollment by employee and plan assignment
  public static async getDataByEmployeeAndPlan(
    employeeId: string,
    planAssignmentId: string
  ): Promise<EmployeeEnrollmentDataInterface | null> {
    try {
      const data = await this.employeeEnrollmentModel.findOne({
        employeeId,
        planAssignmentId
      }) as EmployeeEnrollmentDataInterface;
      return data;
    } catch (error) {
      console.error('Error fetching enrollment by employee and plan:', error);
      return null;
    }
  }

  // Update employee enrollment
  public static async updateData({
    id,
    data,
  }: {
    id: string;
    data: Partial<UpdateableEmployeeEnrollmentDataInterface>;
  }): Promise<UpdateWriteOpResult> {
    try {
      return await this.employeeEnrollmentModel.updateOne({ _id: id }, data);
    } catch (error) {
      console.error('Error updating employee enrollment:', error);
      throw error;
    }
  }

  // Delete employee enrollment (hard delete - enrollments are typically not soft deleted)
  public static async deleteData(id: string): Promise<void> {
    try {
      await this.employeeEnrollmentModel.deleteOne({ _id: id });
    } catch (error) {
      console.error('Error deleting employee enrollment:', error);
      throw error;
    }
  }

  // ===== BUSINESS LOGIC METHODS =====

  /**
   * 🎯 REUSABLE: Compute effective date based on enrollment type and business rules
   */
  public static computeEffectiveDate({
    enrollmentType,
    planAssignment,
    employee,
    qualifyingLifeEvent,
    newHireDate
  }: {
    enrollmentType: string;
    planAssignment: any;
    employee: any;
    qualifyingLifeEvent?: { eventDate: Date | string };
    newHireDate?: Date | string;
  }): { success: boolean; effectiveDate?: Date; error?: string } {
    try {
      let employeeEligibilityDate: Date;
      let computedEffectiveDate: Date;

      if (enrollmentType === 'New Hire') {
        // Employee becomes eligible after hire date + waiting period
        const hireDate = newHireDate ? new Date(newHireDate) : (employee.details?.hireDate ? new Date(employee.details.hireDate) : null);
        if (!hireDate) {
          return { success: false, error: 'Employee hire date required for New Hire enrollment type' };
        }

        const waitingDays = planAssignment.waitingPeriod?.days || 0;
        employeeEligibilityDate = new Date(hireDate);
        employeeEligibilityDate.setDate(employeeEligibilityDate.getDate() + waitingDays);

      } else if (enrollmentType === 'Qualifying Life Event') {
        // Employee becomes eligible immediately after QLE event
        if (!qualifyingLifeEvent?.eventDate) {
          return { success: false, error: 'QLE event date required for Qualifying Life Event enrollment type' };
        }
        employeeEligibilityDate = new Date(qualifyingLifeEvent.eventDate);

      } else {
        // For open enrollment, employee is eligible during enrollment period
        employeeEligibilityDate = new Date(); // Eligible now (during enrollment period)
      }

      // Determine when coverage actually starts
      const planEffectiveDate = new Date(planAssignment.planEffectiveDate);

      if (enrollmentType === 'Open Enrollment') {
        // Open enrollment always starts on plan effective date
        computedEffectiveDate = planEffectiveDate;
      } else {
        // For New Hire and QLE: coverage starts when eligible OR when plan starts, whichever is later
        computedEffectiveDate = employeeEligibilityDate > planEffectiveDate
          ? employeeEligibilityDate
          : planEffectiveDate;
      }

      return { success: true, effectiveDate: computedEffectiveDate };

    } catch (error) {
      console.error('Error computing effective date:', error);
      return { success: false, error: 'Failed to compute effective date' };
    }
  }

  /**
   * 🎯 REUSABLE: Validate dependent count against coverage tier requirements
   */
  public static validateDependentCount(coverageTier: string, dependentCount: number): {
    isValid: boolean;
    error?: string;
    requirement?: { min: number; max: number };
  } {
    const requirement = COVERAGE_TIER_REQUIREMENTS[coverageTier];
    if (!requirement) {
      return { isValid: true }; // Unknown tier, let it pass
    }

    if (dependentCount < requirement.min || dependentCount > requirement.max) {
      return {
        isValid: false,
        error: `Invalid dependent count for ${coverageTier}`,
        requirement
      };
    }

    return { isValid: true, requirement };
  }

  /**
   * 🎯 REUSABLE: Validate QLE event type against plan assignment
   */
  public static validateQLEEventType(eventType: string, planAssignment: any): {
    isValid: boolean;
    error?: string;
    allowedEvents?: string[];
  } {
    const allowedEvents = planAssignment.qualifyingLifeEventWindow?.allowedEvents || [];
    if (!allowedEvents.includes(eventType)) {
      return {
        isValid: false,
        error: `Event type '${eventType}' not allowed for this plan`,
        allowedEvents
      };
    }
    return { isValid: true, allowedEvents };
  }

  /**
   * 🎯 REUSABLE: Validate user access to employee data
   */
  public static async validateEmployeeAccess(
    userId: string,
    targetEmployeeId: string,
    requiredAction: 'read' | 'write'
  ): Promise<{ hasAccess: boolean; user?: any; employee?: any; reason?: string }> {
    try {
      // Use imported classes directly

      const user = await UserModelClass.getDataById(userId);
      if (!user) {
        return { hasAccess: false, reason: 'User not found' };
      }

      const employee = await UserModelClass.getDataById(targetEmployeeId);
      if (!employee) {
        return { hasAccess: false, reason: 'Employee not found' };
      }

      // SuperAdmin can access any employee
      if (user.isSuperAdmin) {
        return { hasAccess: true, user, employee };
      }

      // Employee can only access their own data
      if (userId === targetEmployeeId) {
        return { hasAccess: true, user, employee };
      }

      // Employer can access their company employees (includes brokers accessing their own company)
      if (user.isAdmin && user.companyId === employee.companyId) {
        return { hasAccess: true, user, employee };
      }

      // Broker can read (but not write) their client company employees
      if (user.isBroker && requiredAction === 'read') {
        const employeeCompany = await CompanyModelClass.getDataById(employee.companyId);
        if (employeeCompany && employeeCompany.brokerId === user._id.toString()) {
          return { hasAccess: true, user, employee };
        }
      }

      // Brokers can write to their own company employees (self-enrollment)
      if (user.isBroker && requiredAction === 'write' && user.companyId === employee.companyId) {
        return { hasAccess: true, user, employee };
      }

      return { hasAccess: false, reason: 'Access denied to employee data' };
    } catch (error) {
      console.error('Error validating employee access:', error);
      return { hasAccess: false, reason: 'Error validating access' };
    }
  }

  /**
   * 🎯 REUSABLE: Check if current date is within enrollment period
   */
  public static async isWithinEnrollmentPeriod(planAssignmentId: string): Promise<{
    isWithin: boolean;
    planAssignment?: any;
    reason?: string
  }> {
    try {
      // Use imported PlanAssignmentModelClass directly
      const planAssignment = await PlanAssignmentModelClass.getDataById(planAssignmentId);
      if (!planAssignment) {
        return { isWithin: false, reason: 'Plan assignment not found' };
      }

      const now = new Date();
      const enrollmentStart = new Date(planAssignment.enrollmentStartDate);
      const enrollmentEnd = new Date(planAssignment.enrollmentEndDate);

      if (now < enrollmentStart) {
        return {
          isWithin: false,
          planAssignment,
          reason: `Enrollment period has not started yet. Starts on ${enrollmentStart.toDateString()}`
        };
      }

      if (now > enrollmentEnd) {
        return {
          isWithin: false,
          planAssignment,
          reason: `Enrollment period has ended. Ended on ${enrollmentEnd.toDateString()}`
        };
      }

      return { isWithin: true, planAssignment };
    } catch (error) {
      console.error('Error checking enrollment period:', error);
      return { isWithin: false, reason: 'Error checking enrollment period' };
    }
  }

  /**
   * 🎯 SERVICE-STYLE: Complete enrollment eligibility check
   */
  public static async checkEnrollmentEligibility({
    userId,
    employeeId,
    planAssignmentId,
    enrollmentType,
    qualifyingLifeEvent,
    newHireDate
  }: {
    userId: string;
    employeeId: string;
    planAssignmentId: string;
    enrollmentType?: string;
    qualifyingLifeEvent?: any;
    newHireDate?: string;
  }): Promise<{
    success: boolean;
    isEligible: boolean;
    user?: any;
    employee?: any;
    planAssignment?: any;
    enrollmentType?: string;
    profileValidation?: any;
    enrollmentContext?: any;
    eligibilityDetails?: any;
    error?: string;
    reason?: string;
  }> {
    try {
      // 1. Validate user access
      const accessCheck = await this.validateEmployeeAccess(userId, employeeId, 'read');
      if (!accessCheck.hasAccess) {
        return { success: false, isEligible: false, error: accessCheck.reason };
      }

      // 2. Get employee data for comprehensive validation
      const employee = await UserModelClass.getDataById(employeeId);
      if (!employee) {
        return { success: false, isEligible: false, error: 'Employee not found' };
      }

      // 3. Get plan assignment
      const planAssignment = await PlanAssignmentModelClass.getDataById(planAssignmentId);
      if (!planAssignment) {
        return { success: false, isEligible: false, error: 'Plan assignment not found' };
      }

      // 4. Check for existing enrollment
      const existingEnrollment = await this.getDataByEmployeeAndPlan(employeeId, planAssignmentId);
      if (existingEnrollment) {
        return {
          success: true,
          isEligible: false,
          user: accessCheck.user,
          employee: accessCheck.employee,
          planAssignment,
          reason: 'Employee already enrolled in this plan'
        };
      }

      // 5. Perform comprehensive eligibility validation
      const eligibilityResult = await this.validateComprehensiveEligibility({
        employeeId,
        planAssignmentId,
        operation: 'enrollment',
        userId
      });

      // 6. Validate employee profile completeness for enrollment
      const profileValidation = await this.validateEmployeeProfileForEnrollment(employee, planAssignment);

      // 7. Determine enrollment type
      const determinedType = enrollmentType || 'Open Enrollment';

      // 8. Check enrollment period (unless SuperAdmin override)
      const periodCheck = await this.isWithinEnrollmentPeriod(planAssignmentId);
      const periodEligible = periodCheck.isWithin || accessCheck.user?.isSuperAdmin;

      // 9. Overall eligibility determination
      const isEligible = eligibilityResult.isEligible && periodEligible;

      // 10. Compile all reasons for ineligibility
      const allReasons: string[] = [];
      if (!eligibilityResult.isEligible) {
        allReasons.push(...eligibilityResult.reasons);
      }
      if (!periodEligible) {
        allReasons.push(periodCheck.reason || 'Not within enrollment period');
      }

      // 11. Enrollment context with detailed information
      const enrollmentContext = {
        type: determinedType,
        eligibilityChecks: eligibilityResult.checks,
        enrollmentPeriod: {
          isWithin: periodCheck.isWithin,
          reason: periodCheck.reason
        }
      };

      return {
        success: true,
        isEligible,
        user: accessCheck.user,
        employee: {
          employeeId: employee._id,
          name: employee.name || 'Unknown Employee',
          companyId: employee.companyId,
          employeeClass: employee.details?.employeeClassType,
          age: employee.details?.dateOfBirth ?
            Math.floor((Date.now() - new Date(employee.details.dateOfBirth).getTime()) / (365.25 * 24 * 60 * 60 * 1000)) :
            undefined,
          annualSalary: employee.details?.annualSalary
        },
        planAssignment,
        enrollmentType: determinedType,
        profileValidation,
        enrollmentContext,
        eligibilityDetails: {
          checks: eligibilityResult.checks,
          reasons: allReasons,
          warnings: eligibilityResult.warnings
        },
        reason: isEligible ? undefined : allReasons.join('; ')
      };

    } catch (error) {
      console.error('Error checking enrollment eligibility:', error);
      return { success: false, isEligible: false, error: 'Error checking eligibility' };
    }
  }

  /**
   * 🎯 SERVICE-STYLE: Complete cost calculation with validation
   */
  public static async calculateEnrollmentCost({
    userId,
    employeeId,
    planAssignmentId,
    coverageTier,
    dependentIds = [],
    enrollmentType,
    qualifyingLifeEvent,
    newHireDate
  }: {
    userId: string;
    employeeId: string;
    planAssignmentId: string;
    coverageTier: string;
    dependentIds?: string[];
    enrollmentType?: string;
    qualifyingLifeEvent?: any;
    newHireDate?: string;
  }): Promise<{
    success: boolean;
    calculatedCost?: any;
    planAssignment?: any;
    profileValidation?: any;
    eligibility?: any;
    employee?: any;
    calculationDetails?: any;
    error?: string;
  }> {
    try {
      // 1. Validate user access
      const accessCheck = await this.validateEmployeeAccess(userId, employeeId, 'read');
      if (!accessCheck.hasAccess) {
        return { success: false, error: accessCheck.reason };
      }

      // 2. Get employee data for cost calculation
      const employee = await UserModelClass.getDataById(employeeId);
      if (!employee) {
        return { success: false, error: 'Employee not found' };
      }

      // 3. Validate dependent count
      const dependentValidation = this.validateDependentCount(coverageTier, dependentIds.length);
      if (!dependentValidation.isValid) {
        return { success: false, error: dependentValidation.error };
      }

      // 4. Get plan assignment
      const planAssignment = await PlanAssignmentModelClass.getDataById(planAssignmentId);
      if (!planAssignment) {
        return { success: false, error: 'Plan assignment not found' };
      }

      // 5. Extract employee demographic data for cost calculation
      const employeeAge = employee.details?.dateOfBirth ?
        Math.floor((Date.now() - new Date(employee.details.dateOfBirth).getTime()) / (365.25 * 24 * 60 * 60 * 1000)) :
        undefined;
      const employeeSalary = employee.details?.annualSalary;

      // 6. Validate employee profile completeness for enrollment
      const profileValidation = await this.validateEmployeeProfileForEnrollment(employee, planAssignment);

      // 7. Perform comprehensive eligibility validation
      const eligibilityResult = await this.validateComprehensiveEligibility({
        employeeId,
        planAssignmentId,
        operation: 'enrollment',
        userId
      });

      // 8. Get company settings for payroll frequency (fallback to Monthly)
      const company = await CompanyModelClass.getDataById(employee.companyId);
      const payrollFrequency = 'Monthly'; // Default payroll frequency

      // 9. Calculate actual cost using CostCalculationService
      const costResult = CostCalculationService.calculateEnrollmentCost({
        planAssignment,
        employeeAge,
        selectedTier: coverageTier,
        employeeSalary,
        payrollFrequency
      });

      if (!costResult.success) {
        return {
          success: false,
          error: costResult.error || 'Cost calculation failed',
          profileValidation,
          eligibility: eligibilityResult
        };
      }

      // 10. Build comprehensive response
      const calculatedCost = {
        employeeAmount: costResult.cost?.monthlyEmployeeAmount || 0,
        employerAmount: costResult.cost?.monthlyEmployerAmount || 0,
        totalAmount: costResult.cost?.monthlyTotalAmount || 0,
        payrollDeduction: costResult.cost?.monthlyEmployeeAmount || 0, // Same as employee amount
        coverageTier,
        dependentCount: dependentIds.length,
        payrollFrequency,
        rateStructure: planAssignment.rateStructure || 'Unknown'
      };

      return {
        success: true,
        calculatedCost,
        planAssignment,
        profileValidation,
        eligibility: eligibilityResult,
        employee: {
          employeeId: employee._id,
          name: employee.name || 'Unknown Employee',
          age: employeeAge,
          annualSalary: employeeSalary,
          companyId: employee.companyId
        },
        calculationDetails: {
          rateStructure: planAssignment.rateStructure,
          payrollFrequency,
          calculatedAt: new Date().toISOString(),
          costBreakdown: {
            monthlyEmployeeAmount: costResult.cost?.monthlyEmployeeAmount || 0,
            monthlyEmployerAmount: costResult.cost?.monthlyEmployerAmount || 0,
            monthlyTotalAmount: costResult.cost?.monthlyTotalAmount || 0,
            annualEmployeeAmount: costResult.cost?.annualEmployeeAmount || 0,
            annualEmployerAmount: costResult.cost?.annualEmployerAmount || 0,
            annualTotalAmount: costResult.cost?.annualTotalAmount || 0
          }
        }
      };

    } catch (error) {
      console.error('Error calculating enrollment cost:', error);
      return { success: false, error: 'Internal error calculating cost' };
    }
  }

  // ===== STATUS MANAGEMENT METHODS =====

  // ========================================
  // 🎯 STATUS MANAGEMENT SECTION
  // ========================================

  /**
   * Status Transition Map - Defines valid status transitions
   */
  private static readonly STATUS_TRANSITION_MAP: Record<string, string[]> = {
    [ENROLLMENT_STATUSES[2]]: [ENROLLMENT_STATUSES[0], ENROLLMENT_STATUSES[1], ENROLLMENT_STATUSES[3]], // 'Pending': ['Enrolled', 'Waived', 'Terminated']
    [ENROLLMENT_STATUSES[0]]: [ENROLLMENT_STATUSES[1], ENROLLMENT_STATUSES[3]], // 'Enrolled': ['Waived', 'Terminated']
    [ENROLLMENT_STATUSES[1]]: [ENROLLMENT_STATUSES[2], ENROLLMENT_STATUSES[0]], // 'Waived': ['Pending', 'Enrolled']
    [ENROLLMENT_STATUSES[3]]: [ENROLLMENT_STATUSES[2], ENROLLMENT_STATUSES[0]], // 'Terminated': ['Pending', 'Enrolled']
    [ENROLLMENT_STATUSES[4]]: [] // 'Expired': No transitions allowed from Expired status
  };

  /**
   * Get valid status transitions for current status
   */
  public static getValidStatusTransitions(currentStatus: string): string[] {
    return this.STATUS_TRANSITION_MAP[currentStatus] || [];
  }

  /**
   * Validate if status transition is allowed
   */
  public static validateStatusTransition(currentStatus: string, newStatus: string): boolean {
    return this.getValidStatusTransitions(currentStatus).includes(newStatus);
  }

  /**
   * Get status transition requirements
   */
  public static getStatusTransitionRequirements(fromStatus: string, toStatus: string): {
    isValid: boolean;
    requiredFields: string[];
    businessRules: string[];
  } {
    const isValid = this.validateStatusTransition(fromStatus, toStatus);
    const requiredFields: string[] = [];
    const businessRules: string[] = [];

    if (!isValid) {
      return { isValid: false, requiredFields: [], businessRules: ['Invalid status transition'] };
    }

    // Block all transitions from Expired status
    if (fromStatus === ENROLLMENT_STATUSES[4]) { // 'Expired'
      return { isValid: false, requiredFields: [], businessRules: ['Expired enrollments cannot be modified'] };
    }

    // Define requirements for each transition
    if (toStatus === ENROLLMENT_STATUSES[1]) { // 'Waived'
      requiredFields.push('waiveReason');
      businessRules.push('Must provide reason for waiving coverage');
    }

    if (toStatus === ENROLLMENT_STATUSES[3]) { // 'Terminated'
      requiredFields.push('terminationDate', 'terminationReason');
      businessRules.push('Must provide termination date and reason');
      businessRules.push('Termination date cannot be before effective date');
    }

    if (toStatus === ENROLLMENT_STATUSES[0]) { // 'Enrolled'
      businessRules.push('Must be within enrollment period or have admin override');
      businessRules.push('Employee must meet all eligibility requirements');
    }

    if (fromStatus === ENROLLMENT_STATUSES[1] || fromStatus === ENROLLMENT_STATUSES[3]) { // 'Waived' || 'Terminated'
      businessRules.push('Reinstatement requires admin approval or qualifying life event');
    }

    return { isValid: true, requiredFields, businessRules };
  }

  // ===== EXPIRY MANAGEMENT METHODS =====

  /**
   * Check if enrollment is expired based on planEndDate
   */
  public static isEnrollmentExpired(enrollment: EmployeeEnrollmentDataInterface, referenceDate?: Date): boolean {
    const now = referenceDate || new Date();
    return new Date(enrollment.planEndDate) < now;
  }

  /**
   * Get enrollment status including expiry check
   */
  public static getEnrollmentStatus(enrollment: EmployeeEnrollmentDataInterface, referenceDate?: Date): string {
    // If already marked as expired, return expired
    if (enrollment.status === ENROLLMENT_STATUSES[4]) { // 'Expired'
      return ENROLLMENT_STATUSES[4]; // 'Expired'
    }

    // Check if should be expired based on planEndDate
    if (this.isEnrollmentExpired(enrollment, referenceDate)) {
      return ENROLLMENT_STATUSES[4]; // 'Expired'
    }

    // Return current status
    return enrollment.status;
  }

  /**
   * Check and update expired enrollments in database
   * Only Pending and Enrolled statuses can transition to Expired
   * Waived and Terminated enrollments remain unchanged for historical tracking
   */
  public static async checkExpiredEnrollments(): Promise<{ expiredCount: number; updatedEnrollments: string[] }> {
    try {
      const now = new Date();

      // Find enrollments that should be expired but aren't marked as such
      // Only Pending and Enrolled statuses can transition to Expired
      // Waived and Terminated enrollments remain unchanged for historical tracking
      const expiredEnrollments = await this.employeeEnrollmentModel.find({
        planEndDate: { $lt: now },
        status: { $in: [ENROLLMENT_STATUSES[2], ENROLLMENT_STATUSES[0]] } // Only 'Pending' and 'Enrolled' statuses can become expired
      });

      const updatedEnrollments: string[] = [];

      // Update each expired enrollment
      for (const enrollment of expiredEnrollments) {
        await this.employeeEnrollmentModel.updateOne(
          { _id: enrollment._id },
          {
            status: ENROLLMENT_STATUSES[4], // 'Expired'
            lastModifiedAt: now,
            lastModifiedBy: 'system-expiry-check'
          }
        );
        updatedEnrollments.push(enrollment._id.toString());
      }

      return {
        expiredCount: expiredEnrollments.length,
        updatedEnrollments
      };
    } catch (error) {
      console.error('Error checking expired enrollments:', error);
      return { expiredCount: 0, updatedEnrollments: [] };
    }
  }

  /**
   * Get expired enrollments (includes expiry check and update)
   */
  public static async getExpiredEnrollments(): Promise<EmployeeEnrollmentDataInterface[]> {
    try {
      // First, check and update any newly expired enrollments
      await this.checkExpiredEnrollments();

      // Then return all expired enrollments
      const data = await this.employeeEnrollmentModel.find({
        status: 'Expired'
      }) as EmployeeEnrollmentDataInterface[];

      return data;
    } catch (error) {
      console.error('Error fetching expired enrollments:', error);
      return [];
    }
  }

  /**
   * Get expired enrollments by employee ID (includes expiry check and update)
   */
  public static async getExpiredEnrollmentsByEmployeeId(employeeId: string): Promise<EmployeeEnrollmentDataInterface[]> {
    try {
      // First, check and update any newly expired enrollments
      await this.checkExpiredEnrollments();

      // Then return expired enrollments for the specific employee
      const data = await this.employeeEnrollmentModel.find({
        employeeId: employeeId,
        status: 'Expired'
      }) as EmployeeEnrollmentDataInterface[];

      return data;
    } catch (error) {
      console.error('Error fetching expired enrollments by employee ID:', error);
      return [];
    }
  }

  /**
   * Get expired enrollments by plan assignment IDs (includes expiry check and update)
   */
  public static async getExpiredEnrollmentsByPlanAssignmentIds(planAssignmentIds: string[]): Promise<EmployeeEnrollmentDataInterface[]> {
    try {
      // First, check and update any newly expired enrollments
      await this.checkExpiredEnrollments();

      // Then return expired enrollments for the specified plan assignments
      const data = await this.employeeEnrollmentModel.find({
        planAssignmentId: { $in: planAssignmentIds },
        status: 'Expired'
      }) as EmployeeEnrollmentDataInterface[];

      return data;
    } catch (error) {
      console.error('Error fetching expired enrollments by plan assignment IDs:', error);
      return [];
    }
  }

  /**
   * Get enrollments by employee ID with status filtering
   */
  public static async getDataByEmployeeIdWithStatusFilter(employeeId: string, allowedStatuses: string[]): Promise<EmployeeEnrollmentDataInterface[]> {
    try {
      const data = await this.employeeEnrollmentModel.find({
        employeeId: employeeId,
        status: { $in: allowedStatuses }
      }) as EmployeeEnrollmentDataInterface[];

      return data;
    } catch (error) {
      console.error('Error fetching enrollments by employee ID with status filter:', error);
      return [];
    }
  }



  /**
   * Waive enrollment - Employee explicitly declines coverage
   */
  public static async waiveEnrollment({
    enrollmentId,
    waiveReason,
    userId,
    waiveDate = new Date()
  }: {
    enrollmentId: string;
    waiveReason: string;
    userId: string;
    waiveDate?: Date;
  }): Promise<{ success: boolean; message: string; enrollment?: any }> {
    try {
      // Get existing enrollment
      const enrollment = await this.getDataById(enrollmentId);
      if (!enrollment) {
        return { success: false, message: 'Enrollment not found' };
      }

      // Validate status transition
      if (!this.validateStatusTransition(enrollment.status, 'Waived')) {
        return {
          success: false,
          message: `Cannot waive enrollment with status: ${enrollment.status}`,
        };
      }

      // Validate required fields
      if (!waiveReason?.trim()) {
        return { success: false, message: 'Waive reason is required' };
      }

      // Update enrollment
      const updateData = {
        status: 'Waived',
        waiveReason: waiveReason.trim(),
        waiveDate,
        lastModifiedBy: userId,
        lastModifiedAt: new Date()
      };

      const result = await this.employeeEnrollmentModel.updateOne(
        { _id: enrollmentId },
        updateData
      );

      if (result.modifiedCount === 0) {
        return { success: false, message: 'Failed to waive enrollment' };
      }

      // Get updated enrollment
      const updatedEnrollment = await this.getDataById(enrollmentId);

      return {
        success: true,
        message: 'Enrollment waived successfully',
        enrollment: updatedEnrollment
      };
    } catch (error) {
      console.error('Error waiving enrollment:', error);
      return { success: false, message: 'Internal error waiving enrollment' };
    }
  }

  /**
   * Terminate enrollment - End coverage before plan year end
   */
  public static async terminateEnrollment({
    enrollmentId,
    terminationDate,
    terminationReason,
    userId
  }: {
    enrollmentId: string;
    terminationDate: Date;
    terminationReason: string;
    userId: string;
  }): Promise<{ success: boolean; message: string; enrollment?: any }> {
    try {
      // Get existing enrollment
      const enrollment = await this.getDataById(enrollmentId);
      if (!enrollment) {
        return { success: false, message: 'Enrollment not found' };
      }

      // Validate status transition
      if (!this.validateStatusTransition(enrollment.status, 'Terminated')) {
        return {
          success: false,
          message: `Cannot terminate enrollment with status: ${enrollment.status}`,
        };
      }

      // Validate required fields
      if (!terminationReason?.trim()) {
        return { success: false, message: 'Termination reason is required' };
      }

      // Validate termination date
      const termDate = new Date(terminationDate);
      const effectiveDate = new Date(enrollment.effectiveDate);

      if (termDate < effectiveDate) {
        return {
          success: false,
          message: 'Termination date cannot be before effective date'
        };
      }

      // Update enrollment
      const updateData = {
        status: 'Terminated',
        terminationDate: termDate,
        waiveReason: terminationReason.trim(), // Using waiveReason field for termination reason
        lastModifiedBy: userId,
        lastModifiedAt: new Date()
      };

      const result = await this.employeeEnrollmentModel.updateOne(
        { _id: enrollmentId },
        updateData
      );

      if (result.modifiedCount === 0) {
        return { success: false, message: 'Failed to terminate enrollment' };
      }

      // Get updated enrollment
      const updatedEnrollment = await this.getDataById(enrollmentId);

      return {
        success: true,
        message: 'Enrollment terminated successfully',
        enrollment: updatedEnrollment
      };
    } catch (error) {
      console.error('Error terminating enrollment:', error);
      return { success: false, message: 'Internal error terminating enrollment' };
    }
  }

  /**
   * Reinstate enrollment - Restore waived or terminated enrollment
   */
  public static async reinstateEnrollment({
    enrollmentId,
    newStatus,
    reinstateReason,
    userId,
    newEffectiveDate
  }: {
    enrollmentId: string;
    newStatus: 'Pending' | 'Enrolled';
    reinstateReason: string;
    userId: string;
    newEffectiveDate?: Date;
  }): Promise<{ success: boolean; message: string; enrollment?: any }> {
    try {
      // Get existing enrollment
      const enrollment = await this.getDataById(enrollmentId);
      if (!enrollment) {
        return { success: false, message: 'Enrollment not found' };
      }

      // Validate status transition
      if (!this.validateStatusTransition(enrollment.status, newStatus)) {
        return {
          success: false,
          message: `Cannot reinstate enrollment from ${enrollment.status} to ${newStatus}`,
        };
      }

      // Validate required fields
      if (!reinstateReason?.trim()) {
        return { success: false, message: 'Reinstatement reason is required' };
      }

      // Prepare update data
      const updateData: any = {
        status: newStatus,
        waiveReason: reinstateReason.trim(),
        lastModifiedBy: userId,
        lastModifiedAt: new Date(),
        // Clear previous termination data
        terminationDate: null
      };

      // Set new effective date if provided
      if (newEffectiveDate) {
        updateData.effectiveDate = new Date(newEffectiveDate);
      }

      const result = await this.employeeEnrollmentModel.updateOne(
        { _id: enrollmentId },
        updateData
      );

      if (result.modifiedCount === 0) {
        return { success: false, message: 'Failed to reinstate enrollment' };
      }

      // Get updated enrollment
      const updatedEnrollment = await this.getDataById(enrollmentId);

      return {
        success: true,
        message: `Enrollment reinstated to ${newStatus} status successfully`,
        enrollment: updatedEnrollment
      };
    } catch (error) {
      console.error('Error reinstating enrollment:', error);
      return { success: false, message: 'Internal error reinstating enrollment' };
    }
  }

  /**
   * Activate enrollment - Move from Pending to Enrolled
   */
  public static async activateEnrollment({
    enrollmentId,
    userId,
    activationDate = new Date()
  }: {
    enrollmentId: string;
    userId: string;
    activationDate?: Date;
  }): Promise<{ success: boolean; message: string; enrollment?: any }> {
    try {
      // Get existing enrollment
      const enrollment = await this.getDataById(enrollmentId);
      if (!enrollment) {
        return { success: false, message: 'Enrollment not found' };
      }

      // Validate status transition
      if (!this.validateStatusTransition(enrollment.status, 'Enrolled')) {
        return {
          success: false,
          message: `Cannot activate enrollment with status: ${enrollment.status}`,
        };
      }

      // Update enrollment
      const updateData = {
        status: 'Enrolled',
        effectiveDate: new Date(activationDate),
        lastModifiedBy: userId,
        lastModifiedAt: new Date()
      };

      const result = await this.employeeEnrollmentModel.updateOne(
        { _id: enrollmentId },
        updateData
      );

      if (result.modifiedCount === 0) {
        return { success: false, message: 'Failed to activate enrollment' };
      }

      // Get updated enrollment
      const updatedEnrollment = await this.getDataById(enrollmentId);

      return {
        success: true,
        message: 'Enrollment activated successfully',
        enrollment: updatedEnrollment
      };
    } catch (error) {
      console.error('Error activating enrollment:', error);
      return { success: false, message: 'Internal error activating enrollment' };
    }
  }

  // Get enrollments by status
  public static async getDataByStatus(status: string): Promise<EmployeeEnrollmentDataInterface[]> {
    try {
      const data = await this.employeeEnrollmentModel.find({ status }) as EmployeeEnrollmentDataInterface[];
      return data;
    } catch (error) {
      console.error('Error fetching enrollments by status:', error);
      return [];
    }
  }

  // Get active enrollments (Enrolled status and effective)
  public static async getActiveEnrollments(companyId?: string): Promise<EmployeeEnrollmentDataInterface[]> {
    try {
      const now = new Date();
      const query: any = {
        status: 'Enrolled',
        effectiveDate: { $lte: now },
        $or: [
          { terminationDate: { $gte: now } },
          { terminationDate: null }
        ]
      };

      if (companyId) {
        query.companyId = companyId;
      }

      const data = await this.employeeEnrollmentModel.find(query) as EmployeeEnrollmentDataInterface[];
      return data;
    } catch (error) {
      console.error('Error fetching active enrollments:', error);
      return [];
    }
  }

  // ===== DEPENDENT MANAGEMENT METHODS =====

  // 🎯 NEW: Convert dependent IDs to enrolled dependents with snapshots
  public static async createEnrolledDependentsFromIds(
    employeeId: string,
    dependentIds: string[],
    effectiveDate: Date
  ): Promise<EnrolledDependent[]> {
    try {
      if (!dependentIds || dependentIds.length === 0) {
        return [];
      }

      // Get employee's dependents from User model
      const employee = await UserModelClass.getDataById(employeeId);
      if (!employee || !employee.details?.dependents) {
        throw new Error('Employee or dependents not found');
      }

      const enrolledDependents: EnrolledDependent[] = [];
      const now = new Date();

      for (const dependentId of dependentIds) {
        // Find the dependent in the employee's dependents array
        const dependent = employee.details.dependents.find(
          (dep: any) => dep._id?.toString() === dependentId
        );

        if (!dependent) {
          throw new Error(`Dependent with ID ${dependentId} not found for employee ${employeeId}`);
        }

        // Create enrolled dependent with snapshot
        const enrolledDependent: EnrolledDependent = {
          dependentId,
          enrollmentDate: now,
          effectiveDate,
          isActive: true,
          enrollmentSnapshot: {
            name: dependent.name,
            dateOfBirth: dependent.dateOfBirth,
            relationship: dependent.relationship,
            gender: dependent.gender
          }
        };

        enrolledDependents.push(enrolledDependent);
      }

      return enrolledDependents;
    } catch (error) {
      console.error('Error creating enrolled dependents from IDs:', error);
      throw error;
    }
  }

  // 🎯 NEW: Populate dependent data for enrolled dependents
  public static async populateEnrolledDependents(
    enrollment: EmployeeEnrollmentDataInterface
  ): Promise<EmployeeEnrollmentDataInterface> {
    try {
      if (!enrollment.enrolledDependents || enrollment.enrolledDependents.length === 0) {
        return enrollment;
      }

      // Get current employee data
      const employee = await UserModelClass.getDataById(enrollment.employeeId);
      if (!employee || !employee.details?.dependents) {
        return enrollment; // Return as-is if employee data unavailable
      }

      // Enrich enrolled dependents with current data
      const enrichedDependents = enrollment.enrolledDependents.map(enrolledDep => {
        const currentDependent = employee.details!.dependents!.find(
          (dep: any) => dep._id?.toString() === enrolledDep.dependentId
        );

        return {
          ...enrolledDep,
          // Add current dependent data for reference
          currentData: currentDependent ? {
            name: currentDependent.name,
            dateOfBirth: currentDependent.dateOfBirth,
            relationship: currentDependent.relationship,
            gender: currentDependent.gender,
            isActive: currentDependent.isActive
          } : null
        };
      });

      return {
        ...enrollment,
        enrolledDependents: enrichedDependents as EnrolledDependent[]
      };
    } catch (error) {
      console.error('Error populating enrolled dependents:', error);
      return enrollment; // Return original enrollment if population fails
    }
  }

  // 🎯 NEW: Validate dependent IDs belong to employee
  public static async validateDependentIds(
    employeeId: string,
    dependentIds: string[]
  ): Promise<{ isValid: boolean; errors: string[]; validIds: string[] }> {
    try {
      const errors: string[] = [];
      const validIds: string[] = [];

      if (!dependentIds || dependentIds.length === 0) {
        return { isValid: true, errors: [], validIds: [] };
      }

      // Get employee's dependents
      const employee = await UserModelClass.getDataById(employeeId);
      if (!employee) {
        errors.push('Employee not found');
        return { isValid: false, errors, validIds: [] };
      }

      if (!employee.details?.dependents || employee.details.dependents.length === 0) {
        errors.push('Employee has no dependents registered');
        return { isValid: false, errors, validIds: [] };
      }

      // Validate each dependent ID
      for (const dependentId of dependentIds) {
        const dependent = employee.details.dependents.find(
          (dep: any) => dep._id?.toString() === dependentId
        );

        if (!dependent) {
          errors.push(`Dependent ID ${dependentId} not found for employee`);
        } else if (!dependent.isActive) {
          errors.push(`Dependent ${dependent.name} is not active`);
        } else {
          validIds.push(dependentId);
        }
      }

      return {
        isValid: errors.length === 0,
        errors,
        validIds
      };
    } catch (error) {
      console.error('Error validating dependent IDs:', error);
      return {
        isValid: false,
        errors: ['Internal error validating dependent IDs'],
        validIds: []
      };
    }
  }

  // ===== BUSINESS LOGIC METHODS =====

  // 🎯 NEW: Calculate employee eligibility date based on hire date and waiting period
  public static calculateEligibilityDate(
    hireDate: Date,
    waitingPeriod: { enabled: boolean; days: number; rule: string }
  ): Date {
    if (!waitingPeriod.enabled || waitingPeriod.rule === 'Immediate') {
      return hireDate;
    }

    const eligibilityDate = new Date(hireDate);

    switch (waitingPeriod.rule) {
      case 'Days from hire date':
        eligibilityDate.setDate(eligibilityDate.getDate() + waitingPeriod.days);
        break;

      case 'First of month after X days':
        // Add waiting period days first
        eligibilityDate.setDate(eligibilityDate.getDate() + waitingPeriod.days);
        // Then move to first of next month
        eligibilityDate.setMonth(eligibilityDate.getMonth() + 1);
        eligibilityDate.setDate(1);
        break;

      default:
        // Default to immediate eligibility for unknown rules
        break;
    }

    return eligibilityDate;
  }

  // 🎯 NEW: Check if employee is eligible based on hire date and waiting period
  public static isEmployeeEligibleByHireDate(
    employee: any,
    planAssignment: any,
    referenceDate?: Date
  ): {
    isEligible: boolean;
    eligibilityDate: Date;
    reason?: string;
    daysUntilEligible?: number;
  } {
    const now = referenceDate || new Date();

    // Check if hire date exists
    if (!employee.details?.hireDate) {
      return {
        isEligible: false,
        eligibilityDate: now,
        reason: 'Hire date is required for eligibility calculation'
      };
    }

    const hireDate = new Date(employee.details.hireDate);
    const eligibilityDate = this.calculateEligibilityDate(hireDate, planAssignment.waitingPeriod);

    if (now >= eligibilityDate) {
      return {
        isEligible: true,
        eligibilityDate
      };
    } else {
      const daysUntilEligible = Math.ceil((eligibilityDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));
      return {
        isEligible: false,
        eligibilityDate,
        reason: `Employee must wait ${daysUntilEligible} more days before becoming eligible`,
        daysUntilEligible
      };
    }
  }

  /**
   * Comprehensive eligibility validation for enrollment operations
   */
  public static async validateComprehensiveEligibility({
    employeeId,
    planAssignmentId,
    operation = 'enrollment',
    userId
  }: {
    employeeId: string;
    planAssignmentId: string;
    operation?: 'enrollment' | 'reinstatement' | 'activation';
    userId?: string;
  }): Promise<{
    isEligible: boolean;
    reasons: string[];
    checks: {
      planAssignmentActive: boolean;
      enrollmentPeriod: boolean;
      employeeClass: boolean;
      waitingPeriod: boolean;
      profileComplete: boolean;
      noDuplicates: boolean;
      adminOverride: boolean;
    };
    warnings: string[];
  }> {
    const reasons: string[] = [];
    const warnings: string[] = [];
    const checks = {
      planAssignmentActive: false,
      enrollmentPeriod: false,
      employeeClass: false,
      waitingPeriod: false,
      profileComplete: false,
      noDuplicates: false,
      adminOverride: false
    };

    try {
      // Get required data
      const [employee, planAssignment, existingEnrollments] = await Promise.all([
        UserModelClass.getDataById(employeeId),
        PlanAssignmentModelClass.getDataById(planAssignmentId),
        this.getDataByEmployeeId(employeeId)
      ]);

      if (!employee) {
        reasons.push('Employee not found');
        return { isEligible: false, reasons, checks, warnings };
      }

      if (!planAssignment) {
        reasons.push('Plan assignment not found');
        return { isEligible: false, reasons, checks, warnings };
      }

      // Check if user is SuperAdmin for override capabilities
      let isAdminOverride = false;
      if (userId) {
        const user = await UserModelClass.getDataById(userId);
        isAdminOverride = user?.isSuperAdmin || false;
        checks.adminOverride = isAdminOverride;
      }

      // 1. Plan Assignment Status Check
      checks.planAssignmentActive = planAssignment.status === 'Active';
      if (!checks.planAssignmentActive) {
        reasons.push(`Plan assignment is ${planAssignment.status}. Only Active assignments allow enrollment.`);
      }

      // 2. Enrollment Period Check (can be overridden by SuperAdmin)
      const now = new Date();
      const enrollmentStart = new Date(planAssignment.enrollmentStartDate);
      const enrollmentEnd = new Date(planAssignment.enrollmentEndDate);

      checks.enrollmentPeriod = now >= enrollmentStart && now <= enrollmentEnd;
      if (!checks.enrollmentPeriod && !isAdminOverride) {
        if (now < enrollmentStart) {
          reasons.push(`Enrollment period has not started yet. Starts: ${enrollmentStart.toLocaleDateString()}`);
        } else {
          reasons.push(`Enrollment period has ended. Ended: ${enrollmentEnd.toLocaleDateString()}`);
        }
      } else if (!checks.enrollmentPeriod && isAdminOverride) {
        warnings.push('Enrollment period validation bypassed by SuperAdmin override');
      }

      // 3. Employee Class Eligibility Check
      const classEligibility = this.isEmployeeClassEligible(employee, planAssignment);
      checks.employeeClass = classEligibility.isEligible;
      if (!checks.employeeClass) {
        reasons.push(classEligibility.reason || 'Employee class not eligible');
      }

      // 4. Waiting Period Check
      const hireDateEligibility = this.isEmployeeEligibleByHireDate(employee, planAssignment);
      checks.waitingPeriod = hireDateEligibility.isEligible;
      if (!checks.waitingPeriod) {
        reasons.push(hireDateEligibility.reason || 'Employee has not satisfied waiting period requirements');
      }

      // 5. Profile Completeness Check
      const profileValidation = await this.validateEmployeeProfileForEnrollment(employee, planAssignment);
      checks.profileComplete = profileValidation.isValid;
      if (!checks.profileComplete) {
        reasons.push(...profileValidation.errors);
      }
      if (profileValidation.warnings.length > 0) {
        warnings.push(...profileValidation.warnings);
      }

      // 6. Duplicate Enrollment Check (only for new enrollments)
      if (operation === 'enrollment') {
        const activeEnrollments = existingEnrollments.filter((e: EmployeeEnrollmentDataInterface) =>
          e.planAssignmentId === planAssignmentId &&
          ['Enrolled', 'Pending'].includes(e.status)
        );
        checks.noDuplicates = activeEnrollments.length === 0;
        if (!checks.noDuplicates) {
          reasons.push('Employee already has an active enrollment for this plan assignment');
        }
      } else {
        checks.noDuplicates = true; // Not applicable for reinstatement/activation
      }

      // 7. Company Membership Check
      if (employee.companyId !== planAssignment.companyId) {
        reasons.push('Employee does not belong to the company assigned to this plan');
      }

      // Determine overall eligibility
      const requiredChecks = operation === 'enrollment'
        ? [checks.planAssignmentActive, checks.employeeClass, checks.waitingPeriod, checks.profileComplete, checks.noDuplicates]
        : [checks.planAssignmentActive, checks.employeeClass, checks.profileComplete]; // Less strict for reinstatement

      // Enrollment period can be bypassed by admin
      const enrollmentPeriodRequired = !isAdminOverride;
      if (enrollmentPeriodRequired) {
        requiredChecks.push(checks.enrollmentPeriod);
      }

      const isEligible = requiredChecks.every(check => check === true) && employee.companyId === planAssignment.companyId;

      return {
        isEligible,
        reasons,
        checks,
        warnings
      };

    } catch (error) {
      console.error('Error validating comprehensive eligibility:', error);
      return {
        isEligible: false,
        reasons: ['Internal error during eligibility validation'],
        checks,
        warnings
      };
    }
  }

  // 🎯 NEW: Check if employee class type is eligible for the plan assignment
  public static isEmployeeClassEligible(
    employee: any,
    planAssignment: any
  ): {
    isEligible: boolean;
    reason?: string;
    employeeClass?: string;
    eligibleClasses?: string[];
  } {
    // Check if employee has employeeClassType
    if (!employee.details?.employeeClassType) {
      return {
        isEligible: false,
        reason: 'Employee class type is required for eligibility validation',
        eligibleClasses: planAssignment.eligibleEmployeeClasses || []
      };
    }

    // Check if plan assignment has eligibility rules
    if (!planAssignment.eligibleEmployeeClasses || planAssignment.eligibleEmployeeClasses.length === 0) {
      // If no restrictions specified, all employee classes are eligible
      return {
        isEligible: true,
        employeeClass: employee.details.employeeClassType
      };
    }

    // Check if employee's class is in the eligible list
    const isEligible = planAssignment.eligibleEmployeeClasses.includes(employee.details.employeeClassType);

    if (isEligible) {
      return {
        isEligible: true,
        employeeClass: employee.details.employeeClassType,
        eligibleClasses: planAssignment.eligibleEmployeeClasses
      };
    } else {
      return {
        isEligible: false,
        reason: `Employee class '${employee.details.employeeClassType}' is not eligible for this plan. Eligible classes: ${planAssignment.eligibleEmployeeClasses.join(', ')}`,
        employeeClass: employee.details.employeeClassType,
        eligibleClasses: planAssignment.eligibleEmployeeClasses
      };
    }
  }

  // 🎯 NEW: Validate employee profile completeness for enrollment
  public static async validateEmployeeProfileForEnrollment(
    employee: any,
    planAssignment: any
  ): Promise<{
    isValid: boolean;
    missingFields: string[];
    errors: string[];
    warnings: string[];
  }> {
    const missingFields: string[] = [];
    const errors: string[] = [];
    const warnings: string[] = [];

    // ✅ REQUIRED FIELDS FOR ALL ENROLLMENTS

    // 1. Date of Birth (required for age-banded pricing)
    if (!employee.details?.dateOfBirth) {
      missingFields.push('details.dateOfBirth');
      errors.push('Date of birth is required for age-based cost calculations');
    }

    // 2. Employee Class Type (required for plan eligibility)
    if (!employee.details?.employeeClassType) {
      missingFields.push('details.employeeClassType');
      errors.push('Employee class type is required (Full-Time, Part-Time, etc.)');
    }

    // 3. Hire Date (required for waiting period validation)
    if (!employee.details?.hireDate) {
      missingFields.push('details.hireDate');
      errors.push('Hire date is required for eligibility and waiting period validation');
    }

    // ✅ CONDITIONAL REQUIREMENTS BASED ON PLAN TYPE

    // 4. Annual Salary (required for salary-based rate structures)
    if (planAssignment?.rateStructure === 'Salary-Based') {
      if (!employee.details?.annualSalary || employee.details.annualSalary <= 0) {
        missingFields.push('details.annualSalary');
        errors.push('Annual salary is required for salary-based plan cost calculations');
      }
    } else if (!employee.details?.annualSalary) {
      warnings.push('Annual salary missing - may be needed for some cost calculations');
    }

    // 5. SSN (required for enrollment processing)
    if (!employee.details?.ssn) {
      missingFields.push('details.ssn');
      warnings.push('SSN missing - may be required for carrier enrollment processing');
    }

    // 6. Address (required for enrollment processing)
    if (!employee.details?.address?.street1 || !employee.details?.address?.city ||
        !employee.details?.address?.state || !employee.details?.address?.zipCode) {
      missingFields.push('details.address');
      warnings.push('Complete address missing - may be required for carrier enrollment processing');
    }

    // ✅ ROLE-SPECIFIC REQUIREMENTS

    // 7. EIN (required for employers/brokers enrolling in their own plans)
    if (employee.isAdmin) { // Both Employers and Brokers are admins of their companies
      // Get company data to check EIN (moved from user to company model)
      const company = await CompanyModelClass.getDataById(employee.companyId);
      if (!company?.ein) {
        missingFields.push('company.ein');
        warnings.push('Company EIN (Employer Identification Number) missing - may be required for employer/broker enrollment');
      }
    }

    // 8. Emergency Contact (recommended for all enrollments)
    if (!employee.details?.emergencyContact?.name || !employee.details?.emergencyContact?.phoneNumber) {
      warnings.push('Emergency contact information incomplete - recommended for enrollment');
    }

    // ✅ VALIDATION RESULT
    const isValid = errors.length === 0; // Only errors block enrollment, warnings are informational

    return {
      isValid,
      missingFields,
      errors,
      warnings
    };
  }

  // Check if employee is enrolled in specific plan assignment
  public static async isEmployeeEnrolledInPlan(
    employeeId: string,
    planAssignmentId: string
  ): Promise<boolean> {
    try {
      const enrollment = await this.employeeEnrollmentModel.findOne({
        employeeId,
        planAssignmentId,
        status: { $in: ['Enrolled', 'Pending'] }
      });
      return !!enrollment;
    } catch (error) {
      console.error('Error checking employee plan enrollment:', error);
      return false;
    }
  }

  // Get employee's enrollments for a specific plan assignment
  public static async getEmployeePlanEnrollments(
    employeeId: string,
    planAssignmentId: string
  ): Promise<EmployeeEnrollmentDataInterface[]> {
    try {
      const data = await this.employeeEnrollmentModel.find({
        employeeId,
        planAssignmentId
      }) as EmployeeEnrollmentDataInterface[];
      return data;
    } catch (error) {
      console.error('Error fetching employee plan enrollments:', error);
      return [];
    }
  }

  // Get enrollment count by company
  public static async getCountByCompanyId(companyId: string): Promise<number> {
    try {
      return await this.employeeEnrollmentModel.countDocuments({ companyId });
    } catch (error) {
      console.error('Error getting enrollment count by company:', error);
      return 0;
    }
  }

  // Get enrollment count by status
  public static async getCountByStatus(status: string, companyId?: string): Promise<number> {
    try {
      const query: any = { status };
      if (companyId) {
        query.companyId = companyId;
      }
      return await this.employeeEnrollmentModel.countDocuments(query);
    } catch (error) {
      console.error('Error getting enrollment count by status:', error);
      return 0;
    }
  }

  // Validate enrollment dates
  public static validateEnrollmentDates(
    enrollmentDate: Date,
    effectiveDate: Date,
    terminationDate?: Date
  ): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (effectiveDate < enrollmentDate) {
      errors.push('Effective date cannot be before enrollment date');
    }

    if (terminationDate && terminationDate <= effectiveDate) {
      errors.push('Termination date must be after effective date');
    }

    return { isValid: errors.length === 0, errors };
  }

  // Validate dependent age based on relationship (using User model dependent)
  public static validateDependentAge(
    dependent: any // User model dependent interface
  ): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];
    const now = new Date();
    const age = now.getFullYear() - new Date(dependent.dateOfBirth).getFullYear();

    if (dependent.relationship.toLowerCase() === 'spouse' && age < 18) {
      errors.push('Spouse must be at least 18 years old');
    }

    if (dependent.relationship.toLowerCase() === 'child' && age >= 26) {
      errors.push('Child dependents must be under 26 years old');
    }

    return { isValid: errors.length === 0, errors };
  }

  // 🎯 NEW: Validate coverage tier selection against dependent IDs
  public static async validateCoverageTierSelection(
    coverageTier: string,
    employeeId: string,
    dependentIds: string[]
  ): Promise<{ isValid: boolean; warnings: string[]; suggestions: string[] }> {
    const warnings: string[] = [];
    const suggestions: string[] = [];

    try {
      // Get actual dependent data from User model
      const employee = await UserModelClass.getDataById(employeeId);
      if (!employee || !employee.details?.dependents) {
        return { isValid: true, warnings: [], suggestions: [] };
      }

      // Filter dependents by provided IDs
      const selectedDependents = employee.details.dependents.filter((dep: any) =>
        dependentIds.includes(dep._id?.toString())
      );

      const dependentCount = selectedDependents.length;
      const hasSpouse = selectedDependents.some((d: any) =>
        d.relationship.toLowerCase().includes('spouse') ||
        d.relationship.toLowerCase().includes('partner')
      );
      const hasChildren = selectedDependents.some((d: any) =>
        d.relationship.toLowerCase().includes('child')
      );

    // Validation logic based on real-world insurance practices
    if (dependentCount === 0 && !coverageTier.includes('Employee Only')) {
      warnings.push(`Employee has no dependents but selected '${coverageTier}' tier`);
      suggestions.push("Consider 'Employee Only' tier for potential cost savings");
    }

    if (dependentCount > 0 && coverageTier.includes('Employee Only')) {
      warnings.push(`Employee has ${dependentCount} dependents but selected 'Employee Only' tier`);
      suggestions.push("Dependents will not be covered under 'Employee Only' tier");
    }

    if (hasSpouse && hasChildren && !coverageTier.includes('Family')) {
      suggestions.push("Consider 'Family' tier to cover both spouse and children");
    }

    if (hasSpouse && !hasChildren && coverageTier.includes('Family')) {
      suggestions.push("Consider 'Employee + Spouse' tier for potential cost savings");
    }

    if (!hasSpouse && hasChildren && coverageTier.includes('Employee + Spouse')) {
      warnings.push("Selected 'Employee + Spouse' tier but no spouse listed as dependent");
      suggestions.push("Consider 'Employee + Child(ren)' tier instead");
    }

      return {
        isValid: warnings.length === 0,
        warnings,
        suggestions
      };
    } catch (error) {
      console.error('Error validating coverage tier selection:', error);
      return {
        isValid: false,
        warnings: ['Error validating coverage tier selection'],
        suggestions: []
      };
    }
  }

  // 🎯 MOVED TO SERVICE: Use EmployeeEnrollmentService.createEnrollmentWithCostCalculation instead
  // 🎯 MOVED TO SERVICE: Use EmployeeEnrollmentService.enrollEmployeeInMultiplePlans instead

  // ===== COVERAGE TYPE/SUBTYPE QUERY METHODS =====

  // Get enrollments by coverage type
  public static async getDataByCoverageType(coverageType: string, companyId?: string): Promise<EmployeeEnrollmentDataInterface[]> {
    try {
      const query: any = { coverageType };
      if (companyId) {
        query.companyId = companyId;
      }
      const data = await this.employeeEnrollmentModel.find(query) as EmployeeEnrollmentDataInterface[];
      return data;
    } catch (error) {
      console.error('Error fetching enrollments by coverage type:', error);
      return [];
    }
  }

  // Get enrollments by coverage subtype (searches in coverageSubTypes array)
  public static async getDataByCoverageSubType(coverageSubType: string, companyId?: string): Promise<EmployeeEnrollmentDataInterface[]> {
    try {
      const query: any = { coverageSubTypes: { $in: [coverageSubType] } };
      if (companyId) {
        query.companyId = companyId;
      }
      const data = await this.employeeEnrollmentModel.find(query) as EmployeeEnrollmentDataInterface[];
      return data;
    } catch (error) {
      console.error('Error fetching enrollments by coverage subtype:', error);
      return [];
    }
  }

  // Get enrollments by coverage type and subtype
  public static async getDataByCoverageTypeAndSubType(
    coverageType: string,
    coverageSubType: string,
    companyId?: string
  ): Promise<EmployeeEnrollmentDataInterface[]> {
    try {
      const query: any = {
        coverageType,
        coverageSubTypes: { $in: [coverageSubType] }
      };
      if (companyId) {
        query.companyId = companyId;
      }
      const data = await this.employeeEnrollmentModel.find(query) as EmployeeEnrollmentDataInterface[];
      return data;
    } catch (error) {
      console.error('Error fetching enrollments by coverage type and subtype:', error);
      return [];
    }
  }

  // Get enrollment count by coverage type
  public static async getCountByCoverageType(coverageType: string, companyId?: string): Promise<number> {
    try {
      const query: any = { coverageType };
      if (companyId) {
        query.companyId = companyId;
      }
      return await this.employeeEnrollmentModel.countDocuments(query);
    } catch (error) {
      console.error('Error getting enrollment count by coverage type:', error);
      return 0;
    }
  }

  // 🎯 NEW: Get employee's total monthly cost across all enrollments
  public static async getEmployeeTotalMonthlyCost(employeeId: string): Promise<{ totalEmployeeCost: number; totalEmployerCost: number; totalCost: number }> {
    try {
      const enrollments = await this.employeeEnrollmentModel.find({
        employeeId,
        status: 'Enrolled'
      });

      let totalEmployeeCost = 0;
      let totalEmployerCost = 0;
      let totalCost = 0;

      for (const enrollment of enrollments) {
        totalEmployeeCost += enrollment.contribution.employeeAmount;
        totalEmployerCost += enrollment.contribution.employerAmount;
        totalCost += enrollment.contribution.totalAmount;
      }

      return {
        totalEmployeeCost: Math.round(totalEmployeeCost * 100) / 100,
        totalEmployerCost: Math.round(totalEmployerCost * 100) / 100,
        totalCost: Math.round(totalCost * 100) / 100
      };
    } catch (error) {
      console.error('Error calculating employee total monthly cost:', error);
      return { totalEmployeeCost: 0, totalEmployerCost: 0, totalCost: 0 };
    }
  }

  // 🎯 NEW: Get company's total monthly cost across all enrollments
  public static async getCompanyTotalMonthlyCost(companyId: string): Promise<{ totalEmployeeCost: number; totalEmployerCost: number; totalCost: number; enrollmentCount: number }> {
    try {
      const enrollments = await this.employeeEnrollmentModel.find({
        companyId,
        status: 'Enrolled'
      });

      let totalEmployeeCost = 0;
      let totalEmployerCost = 0;
      let totalCost = 0;

      for (const enrollment of enrollments) {
        totalEmployeeCost += enrollment.contribution.employeeAmount;
        totalEmployerCost += enrollment.contribution.employerAmount;
        totalCost += enrollment.contribution.totalAmount;
      }

      return {
        totalEmployeeCost: Math.round(totalEmployeeCost * 100) / 100,
        totalEmployerCost: Math.round(totalEmployerCost * 100) / 100,
        totalCost: Math.round(totalCost * 100) / 100,
        enrollmentCount: enrollments.length
      };
    } catch (error) {
      console.error('Error calculating company total monthly cost:', error);
      return { totalEmployeeCost: 0, totalEmployerCost: 0, totalCost: 0, enrollmentCount: 0 };
    }
  }
}

// Initialize the model
EmployeeEnrollmentModelClass.initializeModel();

export default EmployeeEnrollmentModelClass;