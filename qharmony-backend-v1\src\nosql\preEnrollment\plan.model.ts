import mongoose, { Document, Model, UpdateWriteOpResult } from 'mongoose';
import {
  PRE_ENROLLMENT_COVERAGE_TYPES,
  PRE_ENROLLMENT_COVERAGE_SUBTYPES,
  PLAN_TYPES,
  RATE_STRUCTURES,
  PLAN_STATUSES,
  METAL_TIERS,
  COINSURANCE_OPTIONS,
  isValidPreEnrollmentCombination
} from '../../constants';

const { Schema } = mongoose;

// Note: Coverage tiers moved to PlanAssignment model for company-specific pricing

// Note: Pricing interfaces moved to PlanAssignment model
// Plan model focuses only on benefit structure and metadata

// Carrier Reference Interface (normalized approach)
export interface CarrierReference {
  carrierId?: string;        // Reference to Carrier model - refs Carrier._id
  carrierPlanId?: string;    // Carrier's internal ID for this plan
  groupNumber?: string;      // Group number assigned by carrier
}

// Benefit Details Interface (informational only - doesn't affect cost calculation)
export interface BenefitDetails {
  // Deductibles
  deductibleIndividual?: number;    // Annual individual deductible amount
  deductibleFamily?: number;        // Annual family deductible amount

  // Copays (fixed dollar amounts)
  pcpCopay?: number;               // Primary Care Provider visit copay
  specialistCopay?: number;        // Specialist visit copay
  emergencyRoomCopay?: number;     // Emergency room visit copay
  urgentCareCopay?: number;        // Urgent care visit copay

  // Coinsurance (percentage after deductible)
  coinsurance?: string;            // e.g., "20%" (employee pays 20%, insurance pays 80%)

  // Out-of-pocket maximums
  outOfPocketMaxIndividual?: number;  // Annual individual out-of-pocket maximum
  outOfPocketMaxFamily?: number;      // Annual family out-of-pocket maximum

  // Preventive care
  preventiveCareCoinsurance?: string; // Usually "0%" (covered 100%)

  // Prescription coverage (optional)
  prescriptionCoverage?: {
    generic?: number;              // Generic drug copay
    brandName?: number;            // Brand name drug copay
    specialty?: number;            // Specialty drug copay
  };

  // Additional benefit details
  additionalBenefits?: string[];   // Array of additional benefit descriptions
}

// Main Plan Data Interface
export interface PlanDataInterface {
  _id?: mongoose.Types.ObjectId;

  // Identification
  planName: string;
  planCode?: string;

  // Ownership (Similar to Carrier model)
  brokerId?: string;           // If broker-created, which broker owns it - refs User._id
  brokerageId?: string;        // If broker-created, which brokerage it belongs to - refs Company._id
  isTemplate: boolean;         // Whether this is a template (system) or actual plan (broker)

  // Classification (Using Pre-Enrollment Insurance Categories)
  coverageType: string;           // Single coverage type (e.g., "Health Insurance", "Ancillary Benefits", "Life & Disability Insurance")
  coverageSubTypes: string[];     // Multiple coverage subtypes (e.g., ["Term Life", "Short-Term Disability", "Long-Term Disability"])
  planType?: string;
  metalTier?: string;             // ACA metal tier: "Bronze", "Silver", "Gold", "Platinum", "Catastrophic"

  // Description
  description: string;
  highlights?: string[];

  // Informative Links (broker can edit anytime)
  informativeLinks?: string[];    // URLs to additional resources, plan details, carrier info, etc.

  // Benefit Details (informational only - doesn't affect cost calculation)
  benefitDetails?: BenefitDetails;

  // Note: ALL time-related fields (planEffectiveDate, planEndDate, enrollmentStartDate, enrollmentEndDate)
  // moved to PlanAssignment model for clear separation of concerns

  // Note: Financial structure (rateStructure, baseCoverageTiers, ageBandedRates)
  // moved to PlanAssignment model for company-specific pricing flexibility

  // Documents (Using Current Azure Blob Naming Convention)
  documentIds: string[];

  // Carrier Reference (Normalized approach)
  carrierId?: string;         // Reference to Carrier model - refs Carrier._id
  carrierPlanId?: string;     // Carrier's internal ID for this plan
  // Note: groupNumber moved to PlanAssignment model (company-specific)

  // Status
  status: string;
  isActivated: boolean;

  // Timestamps
  createdAt?: Date;
  updatedAt?: Date;
}

// Updatable fields interface (excludes non-updatable fields)
export interface UpdateablePlanDataInterface {
  planName?: string;
  planCode?: string;
  description?: string;
  highlights?: string[];
  informativeLinks?: string[];
  benefitDetails?: BenefitDetails;
  planType?: string;
  metalTier?: string;
  // Note: ALL time-related fields moved to PlanAssignment model
  documentIds?: string[];
  carrierId?: string;         // Reference to Carrier model - refs Carrier._id
  carrierPlanId?: string;
  // Note: groupNumber moved to PlanAssignment model (company-specific)
  coverageSubTypes?: string[];
  status?: string;
  isActivated?: boolean;
}

interface PlanDocument extends Document, Omit<PlanDataInterface, '_id'> {}

class PlanModelClass {
  private static planModel: Model<PlanDocument>;

  public static initializeModel() {
    const schema = new Schema({
      // Identification
      planName: { type: String, required: true },
      planCode: { type: String }, // Non-unique, optional - brokers can use same codes

      // Ownership (Similar to Carrier model)
      brokerId: { type: String, ref: 'User' }, // If broker-created, which broker owns it
      brokerageId: { type: String, ref: 'Company' }, // If broker-created, which brokerage it belongs to
      isTemplate: { type: Boolean, default: false, required: true }, // Whether this is a template (system) or actual plan (broker)

      // Classification (Using Pre-Enrollment Insurance Categories)
      coverageType: {
        type: String,
        enum: PRE_ENROLLMENT_COVERAGE_TYPES,
        required: true
      },
      coverageSubTypes: [{
        type: String,
        enum: PRE_ENROLLMENT_COVERAGE_SUBTYPES,
        required: true
      }],
      planType: {
        type: String,
        enum: PLAN_TYPES
      },
      metalTier: {
        type: String,
        enum: METAL_TIERS
      },

      // Description
      description: { type: String, required: true },
      highlights: [{ type: String }],

      // Informative Links (broker can edit anytime)
      informativeLinks: [{
        type: String,
        validate: {
          validator: function(url: string) {
            // Basic URL validation
            try {
              new URL(url);
              return true;
            } catch {
              return false;
            }
          },
          message: 'Invalid URL format'
        }
      }],

      // Benefit Details (informational only - doesn't affect cost calculation)
      benefitDetails: {
        // Deductibles
        deductibleIndividual: { type: Number },
        deductibleFamily: { type: Number },

        // Copays
        pcpCopay: { type: Number },
        specialistCopay: { type: Number },
        emergencyRoomCopay: { type: Number },
        urgentCareCopay: { type: Number },

        // Coinsurance
        coinsurance: {
          type: String,
          enum: COINSURANCE_OPTIONS
        },

        // Out-of-pocket maximums
        outOfPocketMaxIndividual: { type: Number },
        outOfPocketMaxFamily: { type: Number },

        // Preventive care
        preventiveCareCoinsurance: {
          type: String,
          enum: COINSURANCE_OPTIONS,
          default: '0%'
        },

        // Prescription coverage
        prescriptionCoverage: {
          generic: { type: Number },
          brandName: { type: Number },
          specialty: { type: Number }
        },

        // Additional benefits
        additionalBenefits: [{ type: String }]
      },

      // Note: ALL time-related fields (planEffectiveDate, planEndDate, enrollmentStartDate, enrollmentEndDate)
      // moved to PlanAssignment model for clear separation of concerns

      // Note: Financial structure (rateStructure, baseCoverageTiers, ageBandedRates)
      // moved to PlanAssignment model for company-specific pricing flexibility

      // Documents using current Azure Blob naming convention (same as benefit.imageS3Urls)
      documentIds: { type: [String], default: [] },

      // Carrier reference (normalized approach)
      carrierId: { type: String, ref: 'Carrier' },
      carrierPlanId: { type: String },
      // Note: groupNumber moved to PlanAssignment model (company-specific)

      // Status
      status: {
        type: String,
        enum: PLAN_STATUSES,
        default: function() { return this.isTemplate ? PLAN_STATUSES[3] : PLAN_STATUSES[1]; } // 'Template' : 'Active'
      },
      isActivated: { type: Boolean, default: false }
    }, {
      timestamps: true // Automatically adds createdAt and updatedAt
    });

    // Add validation for coverage type and subtypes combination (Pre-Enrollment)
    schema.pre('save', function(next) {
      if (this.coverageType && this.coverageSubTypes && this.coverageSubTypes.length > 0) {
        for (const subType of this.coverageSubTypes) {
          if (!isValidPreEnrollmentCombination(this.coverageType, subType)) {
            return next(new Error(`Invalid combination: coverageSubType "${subType}" is not valid for coverageType "${this.coverageType}"`));
          }
        }
      }
      next();
    });

    // Add indexes for performance
    schema.index({ brokerId: 1 });
    schema.index({ brokerageId: 1 });
    schema.index({ coverageType: 1, coverageSubTypes: 1 });
    schema.index({ status: 1 });
    schema.index({ isTemplate: 1 });
    schema.index({ planCode: 1 }); // Non-unique index for faster lookups

    // 🎯 NEW: Broker-specific uniqueness compound indexes
    schema.index({ brokerId: 1, planCode: 1 }, { unique: true });
    schema.index({ brokerId: 1, planName: 1 }, { unique: true });

    this.planModel = mongoose.model<PlanDocument>('Plan', schema);
  }

  // Create a new plan
  public static async addData(data: PlanDataInterface): Promise<PlanDataInterface | null> {
    try {
      const plan = await this.planModel.create(data);
      return plan as unknown as PlanDataInterface;
    } catch (error) {
      console.error('Error creating plan:', error);
      return null;
    }
  }

  // Get all plans (ADMIN ONLY - use getDataByBrokerId for brokers)
  public static async getAllData(): Promise<PlanDataInterface[]> {
    try {
      const data = await this.planModel.find() as PlanDataInterface[];
      return data;
    } catch (error) {
      console.error('Error fetching all plans:', error);
      return [];
    }
  }

  // 🎯 NEW: Get all plans with pagination (ADMIN ONLY)
  public static async getAllDataPaginated(page: number, limit: number): Promise<{
    plans: PlanDataInterface[];
    totalCount: number;
    totalPages: number;
  }> {
    try {
      const skip = (page - 1) * limit;

      // Get total count and paginated data in parallel
      const [totalCount, plans] = await Promise.all([
        this.planModel.countDocuments(),
        this.planModel.find()
          .skip(skip)
          .limit(limit)
          .sort({ createdAt: -1 }) // Consistent ordering
      ]);

      return {
        plans: plans as PlanDataInterface[],
        totalCount,
        totalPages: Math.ceil(totalCount / limit)
      };
    } catch (error) {
      console.error('Error fetching paginated plans:', error);
      return { plans: [], totalCount: 0, totalPages: 0 };
    }
  }

  // Get plan by ID (SECURE - requires brokerId for non-templates)
  public static async getDataById(
    id: string,
    brokerId?: string
  ): Promise<PlanDataInterface | null> {
    try {
      let query: any = { _id: id };

      // If brokerId provided, ensure broker can only access their own plans
      if (brokerId) {
        query = { _id: id, $or: [{ brokerId }, { isTemplate: true }] };
      }

      const data = await this.planModel.findOne(query) as PlanDataInterface;
      return data;
    } catch (error) {
      console.error('Error fetching plan by ID:', error);
      return null;
    }
  }

  // Get plans accessible by broker (templates + broker's own plans)
  public static async getDataByBrokerId(brokerId: string): Promise<PlanDataInterface[]> {
    try {
      const data = await this.planModel.find({
        $or: [
          { isTemplate: true },  // Templates accessible to all brokers
          { brokerId }           // Broker's own plans
        ]
      }) as PlanDataInterface[];
      return data;
    } catch (error) {
      console.error('Error fetching plans by broker ID:', error);
      return [];
    }
  }

  // 🎯 NEW: Get plans accessible by broker with pagination
  public static async getDataByBrokerIdPaginated(brokerId: string, page: number, limit: number): Promise<{
    plans: PlanDataInterface[];
    totalCount: number;
    totalPages: number;
  }> {
    try {
      const skip = (page - 1) * limit;
      const query = {
        $or: [
          { isTemplate: true },  // Templates accessible to all brokers
          { brokerId }           // Broker's own plans
        ]
      };

      // Get total count and paginated data in parallel
      const [totalCount, plans] = await Promise.all([
        this.planModel.countDocuments(query),
        this.planModel.find(query)
          .skip(skip)
          .limit(limit)
          .sort({ createdAt: -1 }) // Consistent ordering
      ]);

      return {
        plans: plans as PlanDataInterface[],
        totalCount,
        totalPages: Math.ceil(totalCount / limit)
      };
    } catch (error) {
      console.error('Error fetching paginated plans by broker ID:', error);
      return { plans: [], totalCount: 0, totalPages: 0 };
    }
  }

  // 🎯 NEW: Optimized query with filtering and pagination at database level
  public static async getPlansOptimized(query: any, pagination?: { page: number; limit: number }): Promise<{
    plans: PlanDataInterface[];
    totalCount: number;
    totalPages?: number;
  }> {
    try {
      if (pagination) {
        const skip = (pagination.page - 1) * pagination.limit;

        const [totalCount, plans] = await Promise.all([
          this.planModel.countDocuments(query),
          this.planModel.find(query)
            .skip(skip)
            .limit(pagination.limit)
            .sort({ createdAt: -1 })
        ]);

        return {
          plans: plans as PlanDataInterface[],
          totalCount,
          totalPages: Math.ceil(totalCount / pagination.limit)
        };
      } else {
        const plans = await this.planModel.find(query).sort({ createdAt: -1 });
        return {
          plans: plans as PlanDataInterface[],
          totalCount: plans.length
        };
      }
    } catch (error) {
      console.error('Error getting optimized plans:', error);
      return { plans: [], totalCount: 0 };
    }
  }

  // Get plans by coverage type (SECURE - requires brokerId)
  public static async getDataByCoverageType(
    coverageType: string,
    brokerId: string
  ): Promise<PlanDataInterface[]> {
    try {
      const data = await this.planModel.find({
        coverageType,
        $or: [
          { isTemplate: true },  // Templates accessible to all brokers
          { brokerId }           // Broker's own plans
        ]
      }) as PlanDataInterface[];
      return data;
    } catch (error) {
      console.error('Error fetching plans by coverage type:', error);
      return [];
    }
  }

  // Get plans by coverage subtype (SECURE - requires brokerId)
  public static async getDataByCoverageSubType(
    coverageSubType: string,
    brokerId: string
  ): Promise<PlanDataInterface[]> {
    try {
      const data = await this.planModel.find({
        coverageSubTypes: { $in: [coverageSubType] },  // Check if subtype is in the array
        $or: [
          { isTemplate: true },  // Templates accessible to all brokers
          { brokerId }           // Broker's own plans
        ]
      }) as PlanDataInterface[];
      return data;
    } catch (error) {
      console.error('Error fetching plans by coverage subtype:', error);
      return [];
    }
  }

  // Get template plans
  public static async getTemplates(): Promise<PlanDataInterface[]> {
    try {
      const data = await this.planModel.find({ isTemplate: true }) as PlanDataInterface[];
      return data;
    } catch (error) {
      console.error('Error fetching template plans:', error);
      return [];
    }
  }

  // 🎯 NEW: Get template plans with pagination
  public static async getTemplatesPaginated(page: number, limit: number): Promise<{
    plans: PlanDataInterface[];
    totalCount: number;
    totalPages: number;
  }> {
    try {
      const skip = (page - 1) * limit;
      const query = { isTemplate: true };

      // Get total count and paginated data in parallel
      const [totalCount, plans] = await Promise.all([
        this.planModel.countDocuments(query),
        this.planModel.find(query)
          .skip(skip)
          .limit(limit)
          .sort({ createdAt: -1 }) // Consistent ordering
      ]);

      return {
        plans: plans as PlanDataInterface[],
        totalCount,
        totalPages: Math.ceil(totalCount / limit)
      };
    } catch (error) {
      console.error('Error fetching paginated template plans:', error);
      return { plans: [], totalCount: 0, totalPages: 0 };
    }
  }

  // Get assignable plans (Active plans only - excludes Draft, Archived, and Templates)
  // Used for plan assignment operations where only Active plans should be available
  public static async getAssignablePlans(brokerId?: string): Promise<PlanDataInterface[]> {
    try {
      const query: any = {
        status: PLAN_STATUSES[1], // 'Active'
        isTemplate: false  // Templates are not assignable directly
      };

      // If brokerId provided, include broker scope (broker's own plans only)
      if (brokerId) {
        query.brokerId = brokerId;
      }

      const data = await this.planModel.find(query) as PlanDataInterface[];
      return data;
    } catch (error) {
      console.error('Error fetching assignable plans:', error);
      return [];
    }
  }

  // Update plan by ID
  public static async updateData({
    id,
    data,
  }: {
    id: string;
    data: Partial<UpdateablePlanDataInterface>;
  }): Promise<UpdateWriteOpResult> {
    try {
      return await this.planModel.updateOne({ _id: id }, data);
    } catch (error) {
      console.error('Error updating plan:', error);
      throw error;
    }
  }

  // Delete plan by ID (soft delete by setting status to 'Archived')
  public static async deleteData(id: string): Promise<UpdateWriteOpResult> {
    try {
      return await this.planModel.updateOne({ _id: id }, { status: PLAN_STATUSES[2] }); // 'Archived'
    } catch (error) {
      console.error('Error deleting plan:', error);
      throw error;
    }
  }

  // Hard delete plan by ID (for templates or cleanup)
  public static async hardDeleteData(id: string): Promise<void> {
    try {
      // Import services dynamically to avoid circular dependencies
      const AzureBlobService = require('../../services/azure').default;
      const AzureNamespaceService = require('../../services/azureNamespace.service').default;
      const logger = require('../../utils/logger').default;

      // Delete associated Azure container if it exists
      try {
        const containerName = AzureNamespaceService.getPlanContainer(id);
        const containerExists = await AzureBlobService.containerExists(containerName);

        if (containerExists) {
          await AzureBlobService.deleteContainer(containerName);
          logger.info(`Deleted Azure container for plan: ${containerName}`);
        }
      } catch (containerError) {
        logger.warn(`Failed to delete Azure container for plan ${id}:`, containerError);
        // Continue with plan deletion even if container deletion fails
      }

      await this.planModel.deleteOne({ _id: id });
    } catch (error) {
      console.error('Error hard deleting plan:', error);
      throw error;
    }
  }

  // ===== DEPENDENCY CHAIN REFERENCE COUNTING METHODS =====

  // Get plan assignments that reference this plan
  public static async getAssignmentsReferencingPlan(planId: string): Promise<any[]> {
    try {
      // Direct query to avoid circular dependency
      const planAssignmentModel = mongoose.model('PlanAssignment');
      const assignments = await planAssignmentModel.find({
        planId,
        isActive: true // Only active assignments
      });
      return assignments;
    } catch (error) {
      console.error('Error getting assignments referencing plan:', error);
      return [];
    }
  }

  // Check if plan can be edited (not referenced by multiple assignments)
  public static async canEditPlan(planId: string): Promise<{ canEdit: boolean; referencedBy: string[]; referenceCount: number }> {
    try {
      const referencingAssignments = await this.getAssignmentsReferencingPlan(planId);
      const referenceCount = referencingAssignments.length;
      const referencedBy = referencingAssignments.map(assignment => assignment._id?.toString() || '');

      // Can edit if referenced by 0 or 1 assignment
      const canEdit = referenceCount <= 1;

      return { canEdit, referencedBy, referenceCount };
    } catch (error) {
      console.error('Error checking if plan can be edited:', error);
      return { canEdit: false, referencedBy: [], referenceCount: 0 };
    }
  }

  // Check if plan can be deleted (not referenced by any assignments)
  public static async canDeletePlan(planId: string): Promise<{ canDelete: boolean; referencedBy: string[]; referenceCount: number }> {
    try {
      const referencingAssignments = await this.getAssignmentsReferencingPlan(planId);
      const referenceCount = referencingAssignments.length;
      const referencedBy = referencingAssignments.map(assignment => assignment._id?.toString() || '');

      // Can delete only if not referenced by any assignments
      const canDelete = referenceCount === 0;

      return { canDelete, referencedBy, referenceCount };
    } catch (error) {
      console.error('Error checking if plan can be deleted:', error);
      return { canDelete: false, referencedBy: [], referenceCount: 0 };
    }
  }

  // Get plans with filters (SECURE - always requires brokerId unless admin)
  public static async getDataWithFilters(filters: {
    brokerId: string;  // REQUIRED for security
    brokerageId?: string;
    coverageType?: string;
    coverageSubTypes?: string[];  // Updated to array
    status?: string;
    isTemplate?: boolean;
    planType?: string;
  }): Promise<PlanDataInterface[]> {
    try {
      // Build query with special handling for coverageSubTypes array
      const query: any = {};

      // Add non-array filters
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && key !== 'coverageSubTypes') {
          query[key] = value;
        }
      });

      // Handle coverageSubTypes array filter
      if (filters.coverageSubTypes && filters.coverageSubTypes.length > 0) {
        query.coverageSubTypes = { $in: filters.coverageSubTypes };
      }

      const data = await this.planModel.find(query) as PlanDataInterface[];
      return data;
    } catch (error) {
      console.error('Error fetching plans with filters:', error);
      return [];
    }
  }

  // ADMIN ONLY: Get plans with filters (no brokerId restriction)
  public static async getDataWithFiltersAdmin(filters: {
    brokerId?: string;
    brokerageId?: string;
    coverageType?: string;
    coverageSubTypes?: string[];  // Updated to array
    status?: string;
    isTemplate?: boolean;
    planType?: string;
  }): Promise<PlanDataInterface[]> {
    try {
      // Build query with special handling for coverageSubTypes array
      const query: any = {};

      // Add non-array filters
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && key !== 'coverageSubTypes') {
          query[key] = value;
        }
      });

      // Handle coverageSubTypes array filter
      if (filters.coverageSubTypes && filters.coverageSubTypes.length > 0) {
        query.coverageSubTypes = { $in: filters.coverageSubTypes };
      }

      const data = await this.planModel.find(query) as PlanDataInterface[];
      return data;
    } catch (error) {
      console.error('Error fetching plans with filters (admin):', error);
      return [];
    }
  }

  // Update plan documents (for document upload/removal)
  public static async updateDocuments({
    planId,
    documentIds,
  }: {
    planId: string;
    documentIds: string[];
  }): Promise<UpdateWriteOpResult> {
    try {
      return await this.planModel.updateOne(
        { _id: planId },
        { documentIds }
      );
    } catch (error) {
      console.error('Error updating plan documents:', error);
      throw error;
    }
  }

  // Update plan informative links (broker can edit anytime)
  public static async updateInformativeLinks({
    planId,
    informativeLinks,
    brokerId,
  }: {
    planId: string;
    informativeLinks: string[];
    brokerId?: string;
  }): Promise<{ success: boolean; message: string; plan?: PlanDataInterface }> {
    try {
      // Validate broker access if brokerId provided
      if (brokerId) {
        const hasAccess = await this.validateBrokerAccess(planId, brokerId);
        if (!hasAccess) {
          return { success: false, message: 'Plan not found or access denied' };
        }
      }

      // Validate URLs
      for (const url of informativeLinks) {
        try {
          new URL(url);
        } catch {
          return { success: false, message: `Invalid URL format: ${url}` };
        }
      }

      const updateResult = await this.planModel.updateOne(
        { _id: planId },
        {
          informativeLinks,
          updatedAt: new Date()
        }
      );

      if (updateResult.modifiedCount === 0) {
        return { success: false, message: 'Failed to update informative links' };
      }

      const updatedPlan = await this.getDataById(planId, brokerId);
      return {
        success: true,
        message: 'Informative links updated successfully',
        plan: updatedPlan || undefined
      };
    } catch (error) {
      console.error('Error updating plan informative links:', error);
      return { success: false, message: 'Internal error updating informative links' };
    }
  }

  // Add document to plan
  public static async addDocument({
    planId,
    documentId,
  }: {
    planId: string;
    documentId: string;
  }): Promise<UpdateWriteOpResult> {
    try {
      return await this.planModel.updateOne(
        { _id: planId },
        { $push: { documentIds: documentId } }
      );
    } catch (error) {
      console.error('Error adding document to plan:', error);
      throw error;
    }
  }

  // Remove document from plan
  public static async removeDocument({
    planId,
    documentId,
  }: {
    planId: string;
    documentId: string;
  }): Promise<UpdateWriteOpResult> {
    try {
      return await this.planModel.updateOne(
        { _id: planId },
        { $pull: { documentIds: documentId } }
      );
    } catch (error) {
      console.error('Error removing document from plan:', error);
      throw error;
    }
  }

  // Check if plan exists and is accessible by broker
  public static async validateBrokerAccess(planId: string, brokerId: string): Promise<boolean> {
    try {
      const plan = await this.planModel.findOne({
        _id: planId,
        $or: [
          { isTemplate: true },  // Templates accessible to all brokers
          { brokerId }           // Broker's own plans
        ]
      });
      return !!plan;
    } catch (error) {
      console.error('Error validating broker access:', error);
      return false;
    }
  }

  // Get plan count by broker (only broker's own plans, not templates)
  public static async getCountByBrokerId(brokerId: string): Promise<number> {
    try {
      return await this.planModel.countDocuments({ brokerId, isTemplate: false });
    } catch (error) {
      console.error('Error getting plan count by broker:', error);
      return 0;
    }
  }

  // ===== BROKER-SPECIFIC UNIQUENESS VALIDATION =====

  // 🎯 NEW: Validate plan code uniqueness within broker scope
  public static async validateBrokerUniquePlanCode(
    brokerId: string,
    planCode: string,
    excludeId?: string
  ): Promise<{ isUnique: boolean; message?: string }> {
    try {
      // Skip validation if planCode is empty (it's optional)
      if (!planCode || planCode.trim() === '') {
        return { isUnique: true };
      }

      const query: any = {
        brokerId,
        planCode,
        isTemplate: false  // Only check broker's own plans, not templates
      };

      if (excludeId) {
        query._id = { $ne: excludeId };
      }

      const existingPlan = await this.planModel.findOne(query);

      if (existingPlan) {
        return {
          isUnique: false,
          message: `Plan code '${planCode}' already exists for this broker`
        };
      }

      return { isUnique: true };
    } catch (error) {
      console.error('Error validating broker unique plan code:', error);
      return { isUnique: false, message: 'Error validating plan code uniqueness' };
    }
  }

  // 🎯 NEW: Validate plan name uniqueness within broker scope
  public static async validateBrokerUniquePlanName(
    brokerId: string,
    planName: string,
    excludeId?: string
  ): Promise<{ isUnique: boolean; message?: string }> {
    try {
      const query: any = {
        brokerId,
        planName,
        isTemplate: false  // Only check broker's own plans, not templates
      };

      if (excludeId) {
        query._id = { $ne: excludeId };
      }

      const existingPlan = await this.planModel.findOne(query);

      if (existingPlan) {
        return {
          isUnique: false,
          message: `Plan name '${planName}' already exists for this broker`
        };
      }

      return { isUnique: true };
    } catch (error) {
      console.error('Error validating broker unique plan name:', error);
      return { isUnique: false, message: 'Error validating plan name uniqueness' };
    }
  }

  // Get plans by carrier ID (for dependency checking)
  public static async getDataByCarrierId(carrierId: string): Promise<PlanDataInterface[]> {
    try {
      const data = await this.planModel.find({
        carrierId: carrierId,
        status: { $ne: PLAN_STATUSES[2] } // Exclude archived plans
      }) as PlanDataInterface[];
      return data;
    } catch (error) {
      console.error('Error fetching plans by carrier ID:', error);
      return [];
    }
  }

  // Get broker's own plans only (not templates)
  public static async getBrokerPlans(brokerId: string): Promise<PlanDataInterface[]> {
    try {
      const data = await this.planModel.find({
        brokerId,
        isTemplate: false
      }) as PlanDataInterface[];
      return data;
    } catch (error) {
      console.error('Error fetching broker plans:', error);
      return [];
    }
  }



  // Clone plan (for creating from template or duplicating)
  public static async clonePlan({
    sourceId,
    newData,
  }: {
    sourceId: string;
    newData: Partial<PlanDataInterface>;
  }): Promise<PlanDataInterface | null> {
    try {
      const sourcePlan = await this.planModel.findOne({ _id: sourceId });
      if (!sourcePlan) {
        throw new Error('Source plan not found');
      }

      const clonedData = {
        ...sourcePlan.toObject(),
        ...newData,
        _id: undefined, // Remove _id to create new document
        createdAt: undefined,
        updatedAt: undefined,
        documentIds: [], // Start with empty documents for new plan
        status: PLAN_STATUSES[1], // 'Active' - Always start cloned plans as Active
      };

      const clonedPlan = await this.planModel.create(clonedData);
      return clonedPlan as unknown as PlanDataInterface;
    } catch (error) {
      console.error('Error cloning plan:', error);
      return null;
    }
  }

  // ===== STATUS MANAGEMENT METHODS =====

  /**
   * Plan Status Transition Map - Defines valid status transitions
   */
  private static readonly PLAN_STATUS_TRANSITION_MAP: Record<string, string[]> = {
    [PLAN_STATUSES[0]]: [PLAN_STATUSES[1], PLAN_STATUSES[2]], // 'Draft': ['Active', 'Archived']
    [PLAN_STATUSES[1]]: [PLAN_STATUSES[0], PLAN_STATUSES[2]], // 'Active': ['Draft', 'Archived']
    [PLAN_STATUSES[2]]: [PLAN_STATUSES[1]], // 'Archived': ['Active'] - Allow reactivation
    [PLAN_STATUSES[3]]: [PLAN_STATUSES[2]] // 'Template': ['Archived'] - Templates can only be archived
  };

  /**
   * Validate status transition
   */
  public static validatePlanStatusTransition(fromStatus: string, toStatus: string): boolean {
    const validTransitions = this.PLAN_STATUS_TRANSITION_MAP[fromStatus] || [];
    return validTransitions.includes(toStatus);
  }

  /**
   * Get valid status transitions for current status
   */
  public static getValidPlanStatusTransitions(currentStatus: string): string[] {
    return this.PLAN_STATUS_TRANSITION_MAP[currentStatus] || [];
  }

  // Activate plan (Draft -> Active, Archived -> Active)
  public static async activatePlan(
    planId: string,
    brokerId?: string
  ): Promise<{ success: boolean; message: string; plan?: PlanDataInterface }> {
    try {
      // First, get the plan with security check
      const plan = await this.getDataById(planId, brokerId);
      if (!plan) {
        return { success: false, message: 'Plan not found or access denied' };
      }

      // Validate status transition
      if (!this.validatePlanStatusTransition(plan.status, PLAN_STATUSES[1])) { // 'Active'
        const validTransitions = this.getValidPlanStatusTransitions(plan.status);
        return {
          success: false,
          message: `Cannot activate plan with status "${plan.status}". Valid transitions: ${validTransitions.join(', ')}`
        };
      }

      // Validate plan completeness (business rules)
      const validation = await this.validatePlanForActivation(plan);
      if (!validation.isValid) {
        return { success: false, message: `Plan validation failed: ${validation.errors.join(', ')}` };
      }

      // Update status
      const updateResult = await this.planModel.updateOne(
        { _id: planId },
        {
          status: PLAN_STATUSES[1], // 'Active'
          isActivated: true,
          updatedAt: new Date()
        }
      );

      if (updateResult.modifiedCount === 0) {
        return { success: false, message: 'Failed to activate plan' };
      }

      // Get updated plan
      const updatedPlan = await this.getDataById(planId, brokerId);
      return {
        success: true,
        message: 'Plan activated successfully',
        plan: updatedPlan || undefined
      };

    } catch (error) {
      console.error('Error activating plan:', error);
      return { success: false, message: 'Internal error activating plan' };
    }
  }

  // Deactivate plan (Active -> Archived) - Plans don't have "Inactive" status
  public static async deactivatePlan(
    planId: string,
    brokerId?: string
  ): Promise<{ success: boolean; message: string; plan?: PlanDataInterface }> {
    try {
      const plan = await this.getDataById(planId, brokerId);
      if (!plan) {
        return { success: false, message: 'Plan not found or access denied' };
      }

      // Validate status transition
      if (!this.validatePlanStatusTransition(plan.status, PLAN_STATUSES[2])) { // 'Archived'
        const validTransitions = this.getValidPlanStatusTransitions(plan.status);
        return {
          success: false,
          message: `Cannot deactivate plan with status "${plan.status}". Valid transitions: ${validTransitions.join(', ')}`
        };
      }

      const updateResult = await this.planModel.updateOne(
        { _id: planId },
        {
          status: PLAN_STATUSES[2], // 'Archived' - Plans go directly to Archived when deactivated
          isActivated: false,
          updatedAt: new Date()
        }
      );

      if (updateResult.modifiedCount === 0) {
        return { success: false, message: 'Failed to deactivate plan' };
      }

      const updatedPlan = await this.getDataById(planId, brokerId);
      return {
        success: true,
        message: 'Plan deactivated and archived successfully',
        plan: updatedPlan || undefined
      };

    } catch (error) {
      console.error('Error deactivating plan:', error);
      return { success: false, message: 'Internal error deactivating plan' };
    }
  }

  // Convert plan to draft (Active -> Draft)
  public static async convertToDraft(
    planId: string,
    brokerId?: string
  ): Promise<{ success: boolean; message: string; plan?: PlanDataInterface }> {
    try {
      const plan = await this.getDataById(planId, brokerId);
      if (!plan) {
        return { success: false, message: 'Plan not found or access denied' };
      }

      // Validate status transition
      if (!this.validatePlanStatusTransition(plan.status, PLAN_STATUSES[0])) { // 'Draft'
        const validTransitions = this.getValidPlanStatusTransitions(plan.status);
        return {
          success: false,
          message: `Cannot convert plan with status "${plan.status}" to draft. Valid transitions: ${validTransitions.join(', ')}`
        };
      }

      const updateResult = await this.planModel.updateOne(
        { _id: planId },
        {
          status: PLAN_STATUSES[0], // 'Draft'
          isActivated: false,
          updatedAt: new Date()
        }
      );

      if (updateResult.modifiedCount === 0) {
        return { success: false, message: 'Failed to convert plan to draft' };
      }

      const updatedPlan = await this.getDataById(planId, brokerId);
      return {
        success: true,
        message: 'Plan converted to draft successfully',
        plan: updatedPlan || undefined
      };

    } catch (error) {
      console.error('Error converting plan to draft:', error);
      return { success: false, message: 'Internal error converting plan to draft' };
    }
  }

  // Archive plan (Any status -> Archived)
  public static async archivePlan(
    planId: string,
    brokerId?: string
  ): Promise<{ success: boolean; message: string; plan?: PlanDataInterface }> {
    try {
      const plan = await this.getDataById(planId, brokerId);
      if (!plan) {
        return { success: false, message: 'Plan not found or access denied' };
      }

      // Check if already archived
      if (plan.status === 'Archived') {
        return { success: false, message: 'Plan is already archived' };
      }

      // Validate status transition (archive is allowed from any status except Archived)
      if (!this.validatePlanStatusTransition(plan.status, 'Archived')) {
        const validTransitions = this.getValidPlanStatusTransitions(plan.status);
        return {
          success: false,
          message: `Cannot archive plan with status "${plan.status}". Valid transitions: ${validTransitions.join(', ')}`
        };
      }

      const updateResult = await this.planModel.updateOne(
        { _id: planId },
        {
          status: 'Archived',
          isActivated: false,
          updatedAt: new Date()
        }
      );

      if (updateResult.modifiedCount === 0) {
        return { success: false, message: 'Failed to archive plan' };
      }

      const updatedPlan = await this.getDataById(planId, brokerId);
      return {
        success: true,
        message: 'Plan archived successfully',
        plan: updatedPlan || undefined
      };

    } catch (error) {
      console.error('Error archiving plan:', error);
      return { success: false, message: 'Internal error archiving plan' };
    }
  }

  // Get plans by status (SECURE - requires brokerId)
  public static async getDataByStatus(
    status: string,
    brokerId: string
  ): Promise<PlanDataInterface[]> {
    try {
      const data = await this.planModel.find({
        status,
        $or: [
          { isTemplate: true },  // Templates accessible to all brokers
          { brokerId }           // Broker's own plans
        ]
      }) as PlanDataInterface[];
      return data;
    } catch (error) {
      console.error('Error fetching plans by status:', error);
      return [];
    }
  }

  // Get active plans only (SECURE - requires brokerId)
  public static async getActivePlans(brokerId: string): Promise<PlanDataInterface[]> {
    return this.getDataByStatus('Active', brokerId);
  }

  // Get draft plans only (SECURE - requires brokerId)
  public static async getDraftPlans(brokerId: string): Promise<PlanDataInterface[]> {
    return this.getDataByStatus('Draft', brokerId);
  }

  // Validate plan for activation (business rules)
  private static async validatePlanForActivation(plan: PlanDataInterface): Promise<{ isValid: boolean; errors: string[] }> {
    const errors: string[] = [];

    // Required fields validation
    if (!plan.planName || plan.planName.trim() === '') {
      errors.push('Plan name is required');
    }

    if (!plan.description || plan.description.trim() === '') {
      errors.push('Plan description is required');
    }

    if (!plan.coverageType) {
      errors.push('Coverage type is required');
    }

    if (!plan.coverageSubTypes || plan.coverageSubTypes.length === 0) {
      errors.push('At least one coverage subtype is required');
    }

    // Carrier validation (if carrierId is provided)
    if (plan.carrierId) {
      try {
        // Validate string format (basic check)
        if (!plan.carrierId || plan.carrierId.trim().length === 0) {
          errors.push('Invalid carrier ID format');
        } else {
          // Note: Carrier status validation should be done at the controller level
          // to avoid circular dependencies. This is a placeholder for ObjectId validation.
          // The actual carrier status validation is handled in the plan controller.
        }
      } catch (error) {
        console.error('Error validating carrier:', error);
        errors.push('Error validating associated carrier');
      }
    }

    // Note: Coverage tier validation moved to PlanAssignment model

    return { isValid: errors.length === 0, errors };
  }


}

// Initialize the model
PlanModelClass.initializeModel();

export default PlanModelClass;