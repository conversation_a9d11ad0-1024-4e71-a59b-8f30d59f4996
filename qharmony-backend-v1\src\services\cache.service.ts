import logger from '../utils/logger';

// Cache entry interface
interface CacheEntry {
  data: any;
  timestamp: number;
  ttl: number; // Time to live in milliseconds
}

/**
 * Centralized Cache Service
 * Provides in-memory caching with TTL support and automatic cleanup
 */
export class CacheService {
  private cache = new Map<string, CacheEntry>();
  private cleanupInterval: NodeJS.Timeout;
  private defaultTTL: number;

  constructor() {
    // Get default TTL from environment variable (in seconds), default to 5 minutes
    const defaultTTLSeconds = parseInt(process.env.CACHE_DEFAULT_TTL || '300');
    this.defaultTTL = defaultTTLSeconds * 1000; // Convert to milliseconds
    
    // Start cleanup interval (every 10 minutes by default)
    const cleanupIntervalMinutes = parseInt(process.env.CACHE_CLEANUP_INTERVAL || '10');
    this.cleanupInterval = setInterval(() => {
      this.cleanup();
    }, cleanupIntervalMinutes * 60 * 1000);

    logger.info(`Cache service initialized with default TTL: ${defaultTTLSeconds}s, cleanup interval: ${cleanupIntervalMinutes}m`);
  }

  /**
   * Set a value in cache with optional TTL
   * @param key Cache key
   * @param data Data to cache
   * @param ttlSeconds TTL in seconds (optional, uses default if not provided)
   */
  set(key: string, data: any, ttlSeconds?: number): void {
    const ttl = ttlSeconds ? ttlSeconds * 1000 : this.defaultTTL;
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl
    });
    
    logger.debug(`Cache SET: ${key} (TTL: ${ttl / 1000}s)`);
  }

  /**
   * Get a value from cache
   * @param key Cache key
   * @returns Cached data or null if not found/expired
   */
  get(key: string): any | null {
    const entry = this.cache.get(key);
    if (!entry) {
      logger.debug(`Cache MISS: ${key}`);
      return null;
    }

    // Check if expired
    if (Date.now() - entry.timestamp > entry.ttl) {
      this.cache.delete(key);
      logger.debug(`Cache EXPIRED: ${key}`);
      return null;
    }

    logger.debug(`Cache HIT: ${key}`);
    return entry.data;
  }

  /**
   * Delete a specific cache entry
   * @param key Cache key to delete
   */
  delete(key: string): void {
    const deleted = this.cache.delete(key);
    if (deleted) {
      logger.debug(`Cache DELETE: ${key}`);
    }
  }

  /**
   * Delete multiple cache entries by pattern
   * @param pattern String pattern to match keys (simple string matching)
   */
  deleteByPattern(pattern: string): number {
    let deletedCount = 0;
    for (const key of this.cache.keys()) {
      if (key.includes(pattern)) {
        this.cache.delete(key);
        deletedCount++;
      }
    }
    
    if (deletedCount > 0) {
      logger.debug(`Cache DELETE BY PATTERN: ${pattern} (${deletedCount} entries)`);
    }
    
    return deletedCount;
  }

  /**
   * Clear all cache entries
   */
  clear(): void {
    const size = this.cache.size;
    this.cache.clear();
    logger.info(`Cache CLEAR: Removed ${size} entries`);
  }

  /**
   * Clean up expired entries
   */
  cleanup(): void {
    const now = Date.now();
    let cleanedCount = 0;
    
    for (const [key, entry] of this.cache.entries()) {
      if (now - entry.timestamp > entry.ttl) {
        this.cache.delete(key);
        cleanedCount++;
      }
    }
    
    if (cleanedCount > 0) {
      logger.debug(`Cache CLEANUP: Removed ${cleanedCount} expired entries`);
    }
  }

  /**
   * Get cache statistics
   */
  getStats(): {
    totalEntries: number;
    expiredEntries: number;
    memoryUsage: string;
  } {
    const now = Date.now();
    let expiredCount = 0;
    
    for (const entry of this.cache.values()) {
      if (now - entry.timestamp > entry.ttl) {
        expiredCount++;
      }
    }

    return {
      totalEntries: this.cache.size,
      expiredEntries: expiredCount,
      memoryUsage: `${Math.round(JSON.stringify([...this.cache.entries()]).length / 1024)}KB`
    };
  }

  /**
   * Get or set pattern - if key exists return it, otherwise execute function and cache result
   * @param key Cache key
   * @param fetchFunction Function to execute if cache miss
   * @param ttlSeconds Optional TTL in seconds
   */
  async getOrSet<T>(key: string, fetchFunction: () => Promise<T>, ttlSeconds?: number): Promise<T> {
    const cached = this.get(key);
    if (cached !== null) {
      return cached;
    }

    const data = await fetchFunction();
    this.set(key, data, ttlSeconds);
    return data;
  }

  /**
   * Destroy the cache service and cleanup intervals
   */
  destroy(): void {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
    }
    this.clear();
    logger.info('Cache service destroyed');
  }
}

// Create singleton instance
export const cacheService = new CacheService();

// Cache key generators for common patterns
export const CacheKeys = {
  adminCompanies: (adminId: string) => `admin_companies_${adminId}`,
  employeeCompanyDetails: (employeeId: string) => `employee_company_${employeeId}`,
  userProfile: (userId: string) => `user_profile_${userId}`,
  planAssignments: (companyId: string) => `plan_assignments_${companyId}`,
  planAssignmentsList: (userId: string, queryParams: any) => {
    // Create a stable cache key from user ID and query parameters
    const sortedParams = Object.keys(queryParams || {})
      .sort()
      .map(key => `${key}:${queryParams[key]}`)
      .join('|');
    return `plan_assignments_list_${userId}_${sortedParams}`;
  },

  // Pattern generators for bulk operations
  patterns: {
    adminCompanies: 'admin_companies_',
    employeeCompany: 'employee_company_',
    userProfile: 'user_profile_',
    planAssignments: 'plan_assignments_',
    planAssignmentsList: 'plan_assignments_list_'
  }
};

export default cacheService;
