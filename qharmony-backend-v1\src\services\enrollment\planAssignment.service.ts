import mongoose from 'mongoose';
import logger from '../../utils/logger';
import PlanAssignmentModelClass, { PlanAssignmentDataInterface, UpdateablePlanAssignmentDataInterface } from '../../nosql/preEnrollment/planAssignment.model';
import PlanModelClass from '../../nosql/preEnrollment/plan.model';
import CompanyModelClass from '../../nosql/company.model';
import UserModelClass from '../../nosql/user.model';
import { isValidObjectId, safeParseDateFields } from '../../utils/validation';
import { QUALIFYING_LIFE_EVENT_TYPES, PLAN_STATUSES, PLAN_ASSIGNMENT_STATUSES, ENROLLMENT_TYPES } from '../../constants';

/**
 * Plan Assignment Service
 * Modular service layer for plan assignment operations
 * Maintains all business logic while providing clean separation of concerns
 */
export class PlanAssignmentService {
  
  /**
   * Validate user access and return user details
   */
  static async validateUserAccess(userId: string) {
    try {
      const user = await UserModelClass.getDataById(userId);
      return {
        hasAccess: !!user,
        user
      };
    } catch (error) {
      logger.error('Error validating user access:', error);
      return { hasAccess: false, user: null };
    }
  }

  /**
   * Get plan assignment with access control validation
   */
  static async getPlanAssignmentWithAccess(assignmentId: string, userId: string, user: any) {
    try {
      if (!isValidObjectId(assignmentId)) {
        return { hasAccess: false, reason: 'Invalid assignment ID format', assignment: null };
      }

      const assignment = await PlanAssignmentModelClass.getDataById(assignmentId);
      if (!assignment) {
        return { hasAccess: false, reason: 'Plan assignment not found', assignment: null };
      }

      // Check access permissions
      let hasAccess = false;

      if (UserModelClass.isSuperAdmin(user)) {
        // SuperAdmins can view all assignments
        hasAccess = true;
      } else if (user.isBroker) {
        // Brokers can view assignments they created OR assignments for their own company
        const isAssignmentCreatedByBroker = assignment.assignedBy === userId;
        const isOwnCompanyAssignment = user.companyId && user.companyId.toString() === assignment.companyId.toString();
        hasAccess = isAssignmentCreatedByBroker || isOwnCompanyAssignment;
      } else if (user.isAdmin) {
        // Employer admins can only view their own company's assignments
        const userCompany = await CompanyModelClass.getDataByAdminEmail({ adminEmail: user.email });
        hasAccess = userCompany && userCompany._id.toString() === assignment.companyId.toString();
      } else {
        // Employees can only view their own company's assignments
        hasAccess = user.companyId && user.companyId.toString() === assignment.companyId.toString();
      }

      if (!hasAccess) {
        return { hasAccess: false, reason: 'Access denied to this assignment', assignment: null };
      }

      return { hasAccess: true, reason: null, assignment };
    } catch (error) {
      logger.error('Error getting plan assignment with access:', error);
      return { hasAccess: false, reason: 'Internal server error', assignment: null };
    }
  }

  /**
   * Validate plan assignment creation data
   */
  static async validatePlanAssignmentCreation(assignmentData: any, userId: string, user: any) {
    const errors: string[] = [];

    // Required fields validation
    const requiredFields = ['planId', 'companyId', 'rateStructure', 'coverageTiers', 'planEffectiveDate', 'planEndDate', 'enrollmentStartDate', 'enrollmentEndDate'];
    for (const field of requiredFields) {
      if (!assignmentData[field]) {
        errors.push(`${field} is required`);
      }
    }

    // ObjectId format validation
    if (assignmentData.planId && !isValidObjectId(assignmentData.planId)) {
      errors.push('Invalid plan ID format');
    }

    if (assignmentData.companyId && !isValidObjectId(assignmentData.companyId)) {
      errors.push('Invalid company ID format');
    }

    // Access control: Only Brokers can create plan assignments
    if (!user.isBroker) {
      errors.push('Only Brokers can create plan assignments. SuperAdmins manage system resources only.');
    }

    // Validate plan exists and is Active
    if (assignmentData.planId && isValidObjectId(assignmentData.planId)) {
      const plan = await PlanModelClass.getDataById(assignmentData.planId);
      if (!plan) {
        errors.push('Plan not found');
      } else {
        if (plan.status !== PLAN_STATUSES[1]) { // 'Active'
          errors.push(`Cannot assign ${plan.status} plan. Plan must be Active.`);
        }

        if (plan.isTemplate) {
          errors.push('Cannot assign template plans. Please duplicate the template first.');
        }

        // Brokers can only assign their own plans
        if (plan.brokerId !== userId) {
          errors.push('Brokers can only assign their own plans');
        }
      }
    }

    // Validate company exists
    if (assignmentData.companyId && isValidObjectId(assignmentData.companyId)) {
      const company = await CompanyModelClass.getDataById(assignmentData.companyId);
      if (!company) {
        errors.push('Company not found');
      }
    }

    // Parse and validate assignment dates
    if (assignmentData.planEffectiveDate && assignmentData.planEndDate && assignmentData.enrollmentStartDate && assignmentData.enrollmentEndDate) {
      const parsedDates = safeParseDateFields(
        {
          planEffectiveDate: assignmentData.planEffectiveDate,
          planEndDate: assignmentData.planEndDate,
          enrollmentStartDate: assignmentData.enrollmentStartDate,
          enrollmentEndDate: assignmentData.enrollmentEndDate
        },
        ['planEffectiveDate', 'planEndDate', 'enrollmentStartDate', 'enrollmentEndDate']
      );

      const dateValidation = PlanAssignmentModelClass.validateAssignmentDates(
        parsedDates.planEffectiveDate,
        parsedDates.planEndDate,
        parsedDates.enrollmentStartDate,
        parsedDates.enrollmentEndDate
      );

      if (!dateValidation.isValid) {
        errors.push('Invalid assignment dates: ' + dateValidation.errors.join(', '));
      }
    }

    return { isValid: errors.length === 0, errors };
  }

  /**
   * Create a new plan assignment
   */
  static async createPlanAssignment(assignmentData: any, userId: string, user: any) {
    try {
      // Validate assignment data
      const validation = await this.validatePlanAssignmentCreation(assignmentData, userId, user);
      if (!validation.isValid) {
        return { success: false, error: validation.errors.join(', '), assignment: null };
      }

      // Parse dates
      const parsedDates = safeParseDateFields(
        assignmentData,
        ['planEffectiveDate', 'planEndDate', 'enrollmentStartDate', 'enrollmentEndDate']
      );

      // Prepare assignment data with defaults
      const newAssignmentData = {
        planId: assignmentData.planId,
        companyId: assignmentData.companyId,
        groupNumber: assignmentData.groupNumber,
        waitingPeriod: assignmentData.waitingPeriod || { enabled: false, days: 0, rule: 'Immediate' },
        eligibleEmployeeClasses: assignmentData.eligibleEmployeeClasses || ['Full-Time'],
        enrollmentType: assignmentData.enrollmentType || ENROLLMENT_TYPES[0], // 'Active'
        qualifyingLifeEventWindow: assignmentData.qualifyingLifeEventWindow || {
          enabled: true,
          windowDays: 30,
          allowedEvents: [...QUALIFYING_LIFE_EVENT_TYPES],
          description: 'Qualifying life event enrollment window'
        },
        rateStructure: assignmentData.rateStructure,
        coverageTiers: assignmentData.coverageTiers,
        planEffectiveDate: parsedDates.planEffectiveDate,
        planEndDate: parsedDates.planEndDate,
        enrollmentStartDate: parsedDates.enrollmentStartDate,
        enrollmentEndDate: parsedDates.enrollmentEndDate,
        employerContribution: assignmentData.employerContribution || { contributionType: 'Percentage', contributionAmount: 80 },
        employeeContribution: assignmentData.employeeContribution || { contributionType: 'Percentage', contributionAmount: 20 },
        ageBandedRates: assignmentData.ageBandedRates || [],
        salaryBasedRates: assignmentData.salaryBasedRates || [],
        planCustomizations: assignmentData.planCustomizations || {},
        assignedDate: new Date(),
        assignedBy: userId,
        isActive: true,
        status: PLAN_ASSIGNMENT_STATUSES[0] // 'Active'
      };

      // Validate rate structure configuration
      const rateValidation = PlanAssignmentModelClass.validateRateStructure(newAssignmentData as PlanAssignmentDataInterface);
      if (!rateValidation.isValid) {
        return { success: false, error: 'Invalid rate structure configuration: ' + rateValidation.errors.join(', '), assignment: null };
      }

      // Validate tier-specific costs if provided
      if (newAssignmentData.coverageTiers && newAssignmentData.coverageTiers.length > 0) {
        const tierCostValidation = PlanAssignmentModelClass.validateTierCosts(newAssignmentData as PlanAssignmentDataInterface);
        if (!tierCostValidation.isValid) {
          return { success: false, error: 'Invalid tier cost configuration: ' + tierCostValidation.errors.join(', '), assignment: null };
        }

        // Log warnings if any
        if (tierCostValidation.warnings.length > 0) {
          logger.warn('Plan assignment tier cost warnings:', tierCostValidation.warnings);
        }
      }

      // Create plan assignment
      const newAssignment = await PlanAssignmentModelClass.addData(newAssignmentData);
      if (!newAssignment) {
        return { success: false, error: 'Failed to create plan assignment', assignment: null };
      }

      return { success: true, error: null, assignment: newAssignment };
    } catch (error) {
      logger.error('Error in createPlanAssignment service:', error);
      return { success: false, error: 'Internal server error during plan assignment creation', assignment: null };
    }
  }

  /**
   * Get plan assignments for user based on access control
   */
  static async getPlanAssignmentsForUser(userId: string, user: any, filters: any = {}) {
    try {
      let assignments: PlanAssignmentDataInterface[] = [];

      // 🎯 UPDATED: Use new optimized methods instead of legacy methods
      if (UserModelClass.isSuperAdmin(user)) {
        // SuperAdmins can view all assignments system-wide with plan data
        assignments = await PlanAssignmentModelClass.getAssignmentsWithPlanData({});
      } else if (user.isBroker) {
        // Brokers can view assignments for their own plans with plan data
        // Use broker aggregation for access control
        const result = await PlanAssignmentModelClass.getAssignmentsWithBrokerFilter(userId, {}, undefined, true);
        assignments = result.assignments;
      } else {
        // Employers and Employees should use the company-specific endpoint
        return { success: false, error: 'Use company-specific endpoint to view company assignments', assignments: [] };
      }

      // Apply targeted expiry check to fetched assignments only (OPTIMIZED)
      const expiryResult = await PlanAssignmentModelClass.checkTargetedExpiry(assignments);
      assignments = expiryResult.updatedData;

      // Apply filters if provided
      if (filters && Object.keys(filters).length > 0) {
        const { filteredAssignments } = this.filterPlanAssignments(assignments, filters);
        assignments = filteredAssignments;
      }

      return { success: true, error: null, assignments, expiryInfo: expiryResult };
    } catch (error) {
      logger.error('Error getting plan assignments for user:', error);
      return { success: false, error: 'Internal server error', assignments: [] };
    }
  }

  /**
   * 🎯 UNIFIED: Check access control for company-specific requests
   */
  static async checkCompanyAccess(userId: string, user: any, companyId: string): Promise<{
    hasAccess: boolean;
    error?: string;
    accessType: 'superadmin' | 'broker' | 'company' | 'denied';
  }> {
    try {
      if (UserModelClass.isSuperAdmin(user)) {
        return { hasAccess: true, accessType: 'superadmin' };
      }

      if (user.isBroker) {
        // Brokers can view assignments they created OR assignments for their own company
        const brokerAssignments = await PlanAssignmentModelClass.getBrokerAssignmentsForCompany(userId, companyId);
        const isOwnCompany = user.companyId && user.companyId.toString() === companyId;
        const hasAccess = brokerAssignments.length > 0 || isOwnCompany;

        return {
          hasAccess,
          accessType: hasAccess ? 'broker' : 'denied',
          error: hasAccess ? undefined : 'Access denied to this company\'s assignments'
        };
      }

      if (user.isAdmin) {
        // Employer admins can only view their own company's assignments
        const userCompany = await CompanyModelClass.getDataByAdminEmail({ adminEmail: user.email });
        const hasAccess = userCompany && userCompany._id.toString() === companyId;

        return {
          hasAccess,
          accessType: hasAccess ? 'company' : 'denied',
          error: hasAccess ? undefined : 'Access denied to this company\'s assignments'
        };
      }

      // Employees can only view their own company's assignments
      const hasAccess = user.companyId && user.companyId.toString() === companyId;
      return {
        hasAccess,
        accessType: hasAccess ? 'company' : 'denied',
        error: hasAccess ? undefined : 'Access denied to this company\'s assignments'
      };
    } catch (error) {
      logger.error('Error checking company access:', error);
      return { hasAccess: false, accessType: 'denied', error: 'Internal server error' };
    }
  }

  /**
   * 🎯 NEW: Build MongoDB query with access control and filters (database-level optimization)
   */
  static buildPlanAssignmentQuery(userId: string, user: any, filters: any): {
    query: any;
    appliedFilters: string[];
    requiresBrokerAggregation: boolean;
  } {
    let baseQuery: any = {};
    const appliedFilters: string[] = [];
    let requiresBrokerAggregation = false;

    // 1. Access Control (preserve existing logic exactly)
    if (UserModelClass.isSuperAdmin(user)) {
      // SuperAdmins see all assignments (no additional filter)
      appliedFilters.push('superadmin-all-access');
    } else if (user.isBroker) {
      // Brokers need aggregation to filter by plan ownership
      requiresBrokerAggregation = true;
      appliedFilters.push('broker-accessible');
    } else {
      // 🎯 CONSISTENT: Company users should use company-specific endpoints
      // This maintains the same business rule as the original method
      throw new Error('Use company-specific endpoint to view company assignments');
    }

    // 2. Active status filter (preserve existing logic exactly)
    if (filters.includeInactive !== 'true') {
      baseQuery.isActive = true;
      appliedFilters.push('activeOnly');
    } else {
      appliedFilters.push('includeInactive');
    }

    // 3. Status filter
    if (filters.status) {
      baseQuery.status = filters.status;
      appliedFilters.push(`status:${filters.status}`);
    }

    // 4. Company filter (with ObjectId validation)
    if (filters.companyId) {
      if (isValidObjectId(filters.companyId)) {
        baseQuery.companyId = filters.companyId;
        appliedFilters.push(`companyId:${filters.companyId}`);
      } else {
        throw new Error('Invalid companyId format');
      }
    }

    // 5. Plan filter (with ObjectId validation)
    if (filters.planId) {
      if (isValidObjectId(filters.planId)) {
        baseQuery.planId = filters.planId;
        appliedFilters.push(`planId:${filters.planId}`);
      } else {
        throw new Error('Invalid planId format');
      }
    }

    // 6. Assignment year filter
    if (filters.assignmentYear) {
      baseQuery.assignmentYear = parseInt(filters.assignmentYear);
      appliedFilters.push(`assignmentYear:${filters.assignmentYear}`);
    }

    // 7. NEW: Broker filter (for SuperAdmin queries)
    if (filters.brokerId && UserModelClass.isSuperAdmin(user)) {
      if (isValidObjectId(filters.brokerId)) {
        // This will require aggregation to filter by plan.brokerId
        requiresBrokerAggregation = true;
        appliedFilters.push(`brokerId:${filters.brokerId}`);
      } else {
        throw new Error('Invalid brokerId format');
      }
    }

    // 8. NEW: Rate structure filter
    if (filters.rateStructure) {
      baseQuery.rateStructure = filters.rateStructure;
      appliedFilters.push(`rateStructure:${filters.rateStructure}`);
    }

    // 9. NEW: Date range filters
    if (filters.effectiveDateStart || filters.effectiveDateEnd) {
      const dateFilter: any = {};
      if (filters.effectiveDateStart) {
        dateFilter.$gte = new Date(filters.effectiveDateStart);
        appliedFilters.push(`effectiveDateStart:${filters.effectiveDateStart}`);
      }
      if (filters.effectiveDateEnd) {
        dateFilter.$lte = new Date(filters.effectiveDateEnd);
        appliedFilters.push(`effectiveDateEnd:${filters.effectiveDateEnd}`);
      }
      baseQuery.planEffectiveDate = dateFilter;
    }

    if (filters.enrollmentDateStart || filters.enrollmentDateEnd) {
      const enrollmentStartFilter: any = {};
      const enrollmentEndFilter: any = {};

      if (filters.enrollmentDateStart) {
        enrollmentStartFilter.$gte = new Date(filters.enrollmentDateStart);
        appliedFilters.push(`enrollmentDateStart:${filters.enrollmentDateStart}`);
      }
      if (filters.enrollmentDateEnd) {
        enrollmentEndFilter.$lte = new Date(filters.enrollmentDateEnd);
        appliedFilters.push(`enrollmentDateEnd:${filters.enrollmentDateEnd}`);
      }

      if (Object.keys(enrollmentStartFilter).length > 0) {
        baseQuery.enrollmentStartDate = enrollmentStartFilter;
      }
      if (Object.keys(enrollmentEndFilter).length > 0) {
        baseQuery.enrollmentEndDate = enrollmentEndFilter;
      }
    }

    // 10. NEW: Sort options
    if (filters.sortBy) {
      // Store sort options for later use in model layer
      appliedFilters.push(`sortBy:${filters.sortBy}`);
      if (filters.sortOrder) {
        appliedFilters.push(`sortOrder:${filters.sortOrder}`);
      }
    }

    // 11. Time-based filters (database-level optimization)
    if (filters.referenceDate) {
      const refDate = new Date(filters.referenceDate);
      appliedFilters.push(`referenceDate:${filters.referenceDate}`);

      // Apply time-based filters at database level for performance
      if (filters.enrollmentPeriodOnly === 'true') {
        baseQuery.enrollmentStartDate = { $lte: refDate };
        baseQuery.enrollmentEndDate = { $gte: refDate };
        appliedFilters.push('enrollmentPeriodOnly');
      } else if (filters.effectiveOnly === 'true') {
        baseQuery.planEffectiveDate = { $lte: refDate };
        baseQuery.planEndDate = { $gte: refDate };
        appliedFilters.push('effectiveOnly');
      } else if (filters.futureOnly === 'true') {
        baseQuery.planEffectiveDate = { $gt: refDate };
        appliedFilters.push('futureOnly');
      }
    } else {
      // Handle time-based filters without reference date (use current date)
      if (filters.enrollmentPeriodOnly === 'true' || filters.effectiveOnly === 'true' || filters.futureOnly === 'true') {
        const now = new Date();

        if (filters.enrollmentPeriodOnly === 'true') {
          baseQuery.enrollmentStartDate = { $lte: now };
          baseQuery.enrollmentEndDate = { $gte: now };
          appliedFilters.push('enrollmentPeriodOnly');
        } else if (filters.effectiveOnly === 'true') {
          baseQuery.planEffectiveDate = { $lte: now };
          baseQuery.planEndDate = { $gte: now };
          appliedFilters.push('effectiveOnly');
        } else if (filters.futureOnly === 'true') {
          baseQuery.planEffectiveDate = { $gt: now };
          appliedFilters.push('futureOnly');
        }
      }
    }

    return { query: baseQuery, appliedFilters, requiresBrokerAggregation };
  }

  /**
   * 🎯 UNIFIED: Build query for company-specific endpoints with FULL filtering support
   * Now supports ALL the same parameters as the general API
   *
   * NOTE: Company API has different default behavior - includes inactive by default
   */
  static buildCompanySpecificQuery(filters: any): {
    query: any;
    appliedFilters: string[];
  } {
    let baseQuery: any = {};
    const appliedFilters: string[] = [];

    // 🎯 UNIFIED: Company API now defaults to active only (same as general API)
    // Consistent behavior across both APIs

    // 1. Active status filter (unified default behavior)
    if (filters.includeInactive !== 'true') {
      // Default behavior: show active assignments only
      baseQuery.isActive = true;
      appliedFilters.push('activeOnly');
    } else {
      // Explicitly include inactive assignments when requested
      appliedFilters.push('includeInactive');
    }

    // 2. Status filter (NOW SUPPORTED - was missing before)
    if (filters.status) {
      baseQuery.status = filters.status;
      appliedFilters.push(`status:${filters.status}`);
    }

    // 3. Company filter (always present for company endpoints, with validation)
    if (filters.companyId) {
      if (isValidObjectId(filters.companyId)) {
        baseQuery.companyId = filters.companyId;
        appliedFilters.push(`companyId:${filters.companyId}`);
      } else {
        throw new Error('Invalid companyId format');
      }
    }

    // 4. Plan filter (with ObjectId validation)
    if (filters.planId) {
      if (isValidObjectId(filters.planId)) {
        baseQuery.planId = filters.planId;
        appliedFilters.push(`planId:${filters.planId}`);
      } else {
        throw new Error('Invalid planId format');
      }
    }

    // 5. Assignment year filter
    if (filters.assignmentYear) {
      baseQuery.assignmentYear = parseInt(filters.assignmentYear);
      appliedFilters.push(`assignmentYear:${filters.assignmentYear}`);
    }

    // 6. NEW: Rate structure filter
    if (filters.rateStructure) {
      baseQuery.rateStructure = filters.rateStructure;
      appliedFilters.push(`rateStructure:${filters.rateStructure}`);
    }

    // 7. NEW: Date range filters (same as general API)
    if (filters.effectiveDateStart || filters.effectiveDateEnd) {
      const dateFilter: any = {};
      if (filters.effectiveDateStart) {
        dateFilter.$gte = new Date(filters.effectiveDateStart);
        appliedFilters.push(`effectiveDateStart:${filters.effectiveDateStart}`);
      }
      if (filters.effectiveDateEnd) {
        dateFilter.$lte = new Date(filters.effectiveDateEnd);
        appliedFilters.push(`effectiveDateEnd:${filters.effectiveDateEnd}`);
      }
      baseQuery.planEffectiveDate = dateFilter;
    }

    if (filters.enrollmentDateStart || filters.enrollmentDateEnd) {
      const enrollmentStartFilter: any = {};
      const enrollmentEndFilter: any = {};

      if (filters.enrollmentDateStart) {
        enrollmentStartFilter.$gte = new Date(filters.enrollmentDateStart);
        appliedFilters.push(`enrollmentDateStart:${filters.enrollmentDateStart}`);
      }
      if (filters.enrollmentDateEnd) {
        enrollmentEndFilter.$lte = new Date(filters.enrollmentDateEnd);
        appliedFilters.push(`enrollmentDateEnd:${filters.enrollmentDateEnd}`);
      }

      if (Object.keys(enrollmentStartFilter).length > 0) {
        baseQuery.enrollmentStartDate = enrollmentStartFilter;
      }
      if (Object.keys(enrollmentEndFilter).length > 0) {
        baseQuery.enrollmentEndDate = enrollmentEndFilter;
      }
    }

    // 8. Rate structure filter (NEW - for consistency with general API)
    if (filters.rateStructure) {
      baseQuery.rateStructure = filters.rateStructure;
      appliedFilters.push(`rateStructure:${filters.rateStructure}`);
    }

    // 9. NEW: Sort options
    if (filters.sortBy) {
      appliedFilters.push(`sortBy:${filters.sortBy}`);
      if (filters.sortOrder) {
        appliedFilters.push(`sortOrder:${filters.sortOrder}`);
      }
    }

    // 9. Time-based filters (database-level optimization - same as general API)
    if (filters.referenceDate) {
      const refDate = new Date(filters.referenceDate);
      appliedFilters.push(`referenceDate:${filters.referenceDate}`);

      // Apply time-based filters at database level for performance
      if (filters.enrollmentPeriodOnly === 'true') {
        baseQuery.enrollmentStartDate = { $lte: refDate };
        baseQuery.enrollmentEndDate = { $gte: refDate };
        appliedFilters.push('enrollmentPeriodOnly');
      } else if (filters.effectiveOnly === 'true') {
        baseQuery.planEffectiveDate = { $lte: refDate };
        baseQuery.planEndDate = { $gte: refDate };
        appliedFilters.push('effectiveOnly');
      } else if (filters.futureOnly === 'true') {
        baseQuery.planEffectiveDate = { $gt: refDate };
        appliedFilters.push('futureOnly');
      }
    } else {
      // Handle time-based filters without reference date (use current date)
      if (filters.enrollmentPeriodOnly === 'true' || filters.effectiveOnly === 'true' || filters.futureOnly === 'true') {
        const now = new Date();

        if (filters.enrollmentPeriodOnly === 'true') {
          baseQuery.enrollmentStartDate = { $lte: now };
          baseQuery.enrollmentEndDate = { $gte: now };
          appliedFilters.push('enrollmentPeriodOnly');
        } else if (filters.effectiveOnly === 'true') {
          baseQuery.planEffectiveDate = { $lte: now };
          baseQuery.planEndDate = { $gte: now };
          appliedFilters.push('effectiveOnly');
        } else if (filters.futureOnly === 'true') {
          baseQuery.planEffectiveDate = { $gt: now };
          appliedFilters.push('futureOnly');
        }
      }
    }

    return { query: baseQuery, appliedFilters };
  }



  /**
   * 🎯 NEW: Get plan assignments with database-level filtering and pagination
   */
  static async getPlanAssignmentsOptimized(
    userId: string,
    user: any,
    filters: any = {},
    pagination?: { page: number; limit: number }
  ): Promise<{
    assignments: PlanAssignmentDataInterface[];
    totalCount: number;
    totalPages?: number;
    appliedFilters: string[];
    expiryInfo?: any;
  }> {
    try {
      // Build optimized query (preserves all existing filter logic)
      const { query, appliedFilters, requiresBrokerAggregation } = this.buildPlanAssignmentQuery(userId, user, filters);

      // 🎯 STANDARDIZED: Always include plan carrier data by default
      const populatePlanData = filters.includePlanData !== 'false';

      // Get assignments using appropriate method
      let result: any;
      if (requiresBrokerAggregation) {
        // Use broker-specific aggregation for access control
        result = await PlanAssignmentModelClass.getAssignmentsWithBrokerFilter(
          userId,
          query,
          pagination,
          populatePlanData
        );
      } else {
        // Use standard optimized query
        result = await PlanAssignmentModelClass.getPlanAssignmentsOptimized(
          query,
          pagination,
          populatePlanData
        );
      }

      // Apply targeted expiry check only to fetched assignments (preserves existing logic)
      const expiryResult = await PlanAssignmentModelClass.checkTargetedExpiry(result.assignments);

      return {
        assignments: expiryResult.updatedData,
        totalCount: result.totalCount,
        totalPages: result.totalPages,
        appliedFilters,
        expiryInfo: expiryResult
      };

    } catch (error) {
      // Handle specific business rule errors
      if (error instanceof Error && error.message.includes('company-specific endpoint')) {
        logger.warn('Company user attempted to use general endpoint:', error.message);
        return {
          assignments: [],
          totalCount: 0,
          appliedFilters: ['error:company-access-denied']
        };
      }

      logger.error('Error getting optimized plan assignments:', error);
      return { assignments: [], totalCount: 0, appliedFilters: [] };
    }
  }

  /**
   * Filter plan assignments based on query parameters (EXISTING METHOD - kept for compatibility)
   */
  static filterPlanAssignments(assignments: PlanAssignmentDataInterface[], filters: any) {
    let filteredAssignments = [...assignments];
    const appliedFilters: string[] = [];

    // Filter by active status (default behavior: exclude inactive unless explicitly requested)
    if (filters.includeInactive !== 'true') {
      filteredAssignments = filteredAssignments.filter(a => a.isActive === true);
      appliedFilters.push('activeOnly');
    } else {
      appliedFilters.push('includeInactive');
    }

    if (filters.status) {
      filteredAssignments = filteredAssignments.filter(a => a.status === filters.status);
      appliedFilters.push(`status:${filters.status}`);
    }

    if (filters.companyId) {
      filteredAssignments = filteredAssignments.filter(a => a.companyId.toString() === filters.companyId);
      appliedFilters.push(`companyId:${filters.companyId}`);
    }

    if (filters.planId) {
      filteredAssignments = filteredAssignments.filter(a => a.planId.toString() === filters.planId);
      appliedFilters.push(`planId:${filters.planId}`);
    }

    if (filters.assignmentYear) {
      const year = Number(filters.assignmentYear);
      filteredAssignments = filteredAssignments.filter(a => a.assignmentYear === year);
      appliedFilters.push(`assignmentYear:${year}`);
    }

    // NEW: Enhanced time-based filtering
    if (filters.referenceDate) {
      const refDate = new Date(filters.referenceDate);
      appliedFilters.push(`referenceDate:${filters.referenceDate}`);

      if (filters.enrollmentPeriodOnly === 'true') {
        filteredAssignments = filteredAssignments.filter(a =>
          a.enrollmentStartDate <= refDate && a.enrollmentEndDate >= refDate
        );
        appliedFilters.push('enrollmentPeriodOnly');
      } else if (filters.effectiveOnly === 'true') {
        filteredAssignments = filteredAssignments.filter(a =>
          a.planEffectiveDate <= refDate && a.planEndDate >= refDate
        );
        appliedFilters.push('effectiveOnly');
      } else if (filters.futureOnly === 'true') {
        filteredAssignments = filteredAssignments.filter(a =>
          a.planEffectiveDate > refDate
        );
        appliedFilters.push('futureOnly');
      } else {
        // Default behavior: filter assignments that are effective on the reference date
        filteredAssignments = filteredAssignments.filter(a =>
          a.planEffectiveDate <= refDate && a.planEndDate >= refDate
        );
      }
    }

    // NEW: Rate structure filter
    if (filters.rateStructure) {
      filteredAssignments = filteredAssignments.filter(a => a.rateStructure === filters.rateStructure);
      appliedFilters.push(`rateStructure:${filters.rateStructure}`);
    }

    // NEW: Date range filters
    if (filters.effectiveDateStart) {
      const startDate = new Date(filters.effectiveDateStart);
      filteredAssignments = filteredAssignments.filter(a => a.planEffectiveDate >= startDate);
      appliedFilters.push(`effectiveDateStart:${filters.effectiveDateStart}`);
    }

    if (filters.effectiveDateEnd) {
      const endDate = new Date(filters.effectiveDateEnd);
      filteredAssignments = filteredAssignments.filter(a => a.planEffectiveDate <= endDate);
      appliedFilters.push(`effectiveDateEnd:${filters.effectiveDateEnd}`);
    }

    if (filters.enrollmentDateStart) {
      const startDate = new Date(filters.enrollmentDateStart);
      filteredAssignments = filteredAssignments.filter(a => a.enrollmentStartDate >= startDate);
      appliedFilters.push(`enrollmentDateStart:${filters.enrollmentDateStart}`);
    }

    if (filters.enrollmentDateEnd) {
      const endDate = new Date(filters.enrollmentDateEnd);
      filteredAssignments = filteredAssignments.filter(a => a.enrollmentEndDate <= endDate);
      appliedFilters.push(`enrollmentDateEnd:${filters.enrollmentDateEnd}`);
    }

    // NEW: Carrier data filtering (if plan data is populated)
    if (filters.carrierId && filteredAssignments.length > 0 && (filteredAssignments[0] as any).planData) {
      filteredAssignments = filteredAssignments.filter(a => {
        const planData = (a as any).planData;
        return planData && planData.carrierId && planData.carrierId.toString() === filters.carrierId;
      });
      appliedFilters.push(`carrierId:${filters.carrierId}`);
    }

    return { filteredAssignments, appliedFilters };
  }

  /**
   * Get plan assignments by company with access control - UNIFIED
   */
  static async getPlanAssignmentsByCompany(
    companyId: string,
    userId: string,
    user: any,
    filters: any = {},
    pagination?: { page: number; limit: number }
  ): Promise<{
    assignments: PlanAssignmentDataInterface[];
    totalCount: number;
    totalPages?: number;
    appliedFilters: string[];
    expiryInfo?: any;
  }> {
    try {
      // 🎯 UNIFIED: Use centralized access control
      console.log('filters', filters);
      const accessCheck = await this.checkCompanyAccess(userId, user, companyId);
      if (!accessCheck.hasAccess) {
        return {
          assignments: [],
          totalCount: 0,
          appliedFilters: ['error:access-denied']
        };
      }

      // 🎯 UNIFIED: Support backward compatibility for includeExpired parameter
      if (filters.includeExpired !== undefined && filters.includeInactive === undefined) {
        filters.includeInactive = filters.includeExpired;
      }

      // 🎯 UNIFIED: Add company-specific filters to the standard filter set
      const companyFilters = {
        ...filters,
        companyId: companyId // Ensure company filtering
      };

      // 🎯 UNIFIED: Build query using the same logic as general API
      const { query: baseQuery, appliedFilters: queryFilters } = this.buildCompanySpecificQuery(companyFilters);

      // 🎯 UNIFIED: Always include plan carrier data by default (same as general API)
      const populatePlanData = filters.includePlanData !== 'false';

      // 🎯 UNIFIED: Use the same optimized approach as the general API
      let result: any;

      // Check if broker aggregation is needed (for brokerId filtering by SuperAdmins)
      const needsBrokerAggregation = filters.brokerId && UserModelClass.isSuperAdmin(user);

      if (accessCheck.accessType === 'broker' || needsBrokerAggregation) {
        // Use broker-specific aggregation for access control or brokerId filtering
        const brokerIdForFilter = needsBrokerAggregation ? filters.brokerId : userId;
        console.log("Broker Filtering with brokerId", brokerIdForFilter);
        result = await PlanAssignmentModelClass.getAssignmentsWithBrokerFilter(
          brokerIdForFilter,
          baseQuery,
          pagination,
          populatePlanData
        );
      } else {
        // Use standard optimized query for SuperAdmins, company admins, and employees
        result = await PlanAssignmentModelClass.getPlanAssignmentsOptimized(
          baseQuery,
          pagination,
          populatePlanData
        );
      }

      // 🎯 UNIFIED: Apply targeted expiry check (same as general API)
      const expiryResult = await PlanAssignmentModelClass.checkTargetedExpiry(result.assignments);

      // 🎯 UNIFIED: Return same format as general API
      return {
        assignments: expiryResult.updatedData,
        totalCount: result.totalCount,
        totalPages: result.totalPages,
        appliedFilters: queryFilters, // Use filters from query builder only
        expiryInfo: expiryResult
      };

    } catch (error) {
      // Handle specific validation errors
      if (error instanceof Error && error.message.includes('Invalid') && error.message.includes('format')) {
        logger.warn('Invalid parameter format in company API:', error.message);
        return {
          assignments: [],
          totalCount: 0,
          appliedFilters: ['error:invalid-parameter-format']
        };
      }

      logger.error('Error getting plan assignments by company:', error);
      return {
        assignments: [],
        totalCount: 0,
        appliedFilters: ['error:internal-server-error']
      };
    }
  }

  /**
   * Get plan assignment by ID with access control
   */
  static async getPlanAssignmentById(assignmentId: string, userId: string, user: any) {
    try {
      // Get assignment with plan data in single query
      const assignment = await PlanAssignmentModelClass.getDataByIdWithPlanData(assignmentId);

      if (!assignment) {
        return { success: false, error: 'Plan assignment not found', assignment: null };
      }

      // Access control validation
      const accessCheck = await this.getPlanAssignmentWithAccess(assignmentId, userId, user);
      if (!accessCheck.hasAccess) {
        return { success: false, error: accessCheck.reason, assignment: null };
      }

      // Get current status based on time constraints
      const currentStatus = PlanAssignmentModelClass.getAssignmentStatus(assignment);

      return {
        success: true,
        error: null,
        assignment: {
          ...assignment,
          currentStatus
        }
      };
    } catch (error) {
      logger.error('Error getting plan assignment by ID:', error);
      return { success: false, error: 'Internal server error', assignment: null };
    }
  }

  /**
   * Update plan assignment with validation
   */
  static async updatePlanAssignment(assignmentId: string, updateData: any, userId: string, user: any) {
    try {
      const accessCheck = await this.getPlanAssignmentWithAccess(assignmentId, userId, user);
      if (!accessCheck.hasAccess) {
        return { success: false, error: accessCheck.reason, assignment: null };
      }

      const assignment = accessCheck.assignment!;

      // Check if assignment can be edited (0-1 employee enrollments)
      const editCheck = await PlanAssignmentModelClass.canEditAssignment(assignmentId);
      if (!editCheck.canEdit) {
        return {
          success: false,
          error: `Cannot edit plan assignment with active enrollments. Reference count: ${editCheck.referenceCount}`,
          assignment: null
        };
      }

      // Extract only fields that exist in the UpdateablePlanAssignmentDataInterface
      const validUpdateData: UpdateablePlanAssignmentDataInterface = {};
      Object.keys(updateData).forEach(key => {
        const typedKey = key as keyof UpdateablePlanAssignmentDataInterface;
        if (updateData[typedKey] !== undefined) {
          (validUpdateData as any)[typedKey] = updateData[typedKey];
        }
      });

      // Validate dates if being updated
      if (validUpdateData.planEffectiveDate || validUpdateData.planEndDate || validUpdateData.enrollmentStartDate || validUpdateData.enrollmentEndDate) {
        const effectiveDate = validUpdateData.planEffectiveDate ? new Date(validUpdateData.planEffectiveDate) : assignment.planEffectiveDate;
        const endDate = validUpdateData.planEndDate ? new Date(validUpdateData.planEndDate) : assignment.planEndDate;
        const enrollStart = validUpdateData.enrollmentStartDate ? new Date(validUpdateData.enrollmentStartDate) : assignment.enrollmentStartDate;
        const enrollEnd = validUpdateData.enrollmentEndDate ? new Date(validUpdateData.enrollmentEndDate) : assignment.enrollmentEndDate;

        const dateValidation = PlanAssignmentModelClass.validateAssignmentDates(
          effectiveDate, endDate, enrollStart, enrollEnd
        );

        if (!dateValidation.isValid) {
          return { success: false, error: 'Invalid assignment dates: ' + dateValidation.errors.join(', '), assignment: null };
        }
      }

      // Validate rate structure if being updated
      if (validUpdateData.rateStructure || validUpdateData.coverageTiers || validUpdateData.ageBandedRates) {
        const updatedAssignment = { ...assignment, ...validUpdateData };
        const rateValidation = PlanAssignmentModelClass.validateRateStructure(updatedAssignment);
        if (!rateValidation.isValid) {
          return { success: false, error: 'Invalid rate structure configuration: ' + rateValidation.errors.join(', '), assignment: null };
        }
      }

      // Validate tier-specific costs if coverage tiers are being updated
      if (validUpdateData.coverageTiers) {
        const updatedAssignment = { ...assignment, ...validUpdateData };
        const tierCostValidation = PlanAssignmentModelClass.validateTierCosts(updatedAssignment);
        if (!tierCostValidation.isValid) {
          return { success: false, error: 'Invalid tier cost configuration: ' + tierCostValidation.errors.join(', '), assignment: null };
        }

        // Log warnings if any
        if (tierCostValidation.warnings.length > 0) {
          logger.warn('Plan assignment tier cost warnings:', tierCostValidation.warnings);
        }
      }

      // Update assignment
      const result = await PlanAssignmentModelClass.updateData({ id: assignmentId, data: validUpdateData });
      if (result.modifiedCount === 0) {
        return { success: false, error: 'No changes made to plan assignment', assignment: null };
      }

      // Get updated assignment
      const updatedAssignment = await PlanAssignmentModelClass.getDataById(assignmentId);
      return { success: true, error: null, assignment: updatedAssignment };

    } catch (error) {
      logger.error('Error in updatePlanAssignment service:', error);
      return { success: false, error: 'Internal server error during plan assignment update', assignment: null };
    }
  }

  /**
   * Modify plan assignment status (activate, deactivate)
   */
  static async modifyPlanAssignmentStatus(assignmentId: string, action: 'activate' | 'deactivate', userId: string, user: any) {
    try {
      const accessCheck = await this.getPlanAssignmentWithAccess(assignmentId, userId, user);
      if (!accessCheck.hasAccess) {
        return { success: false, error: accessCheck.reason };
      }

      // Delegate to model layer for status validation and transition logic
      let result: any;
      switch (action) {
        case 'activate':
          result = await PlanAssignmentModelClass.activateAssignment(assignmentId);
          break;
        case 'deactivate':
          result = await PlanAssignmentModelClass.deactivateAssignment(assignmentId);
          break;
        default:
          return { success: false, error: 'Invalid action' };
      }

      if (!result.success) {
        return { success: false, error: result.message };
      }

      return { success: true, error: null, message: result.message };

    } catch (error) {
      logger.error(`Error in modifyPlanAssignmentStatus service (${action}):`, error);
      return { success: false, error: `Internal server error during plan assignment ${action}` };
    }
  }

  /**
   * Clone plan assignment with smart date handling
   * Can clone any assignment regardless of status to create new editable assignment
   */
  static async clonePlanAssignment(assignmentId: string, overrides: any, userId: string, user: any) {
    try {
      const accessCheck = await this.getPlanAssignmentWithAccess(assignmentId, userId, user);
      if (!accessCheck.hasAccess) {
        return { success: false, error: accessCheck.reason, assignment: null };
      }

      const assignment = accessCheck.assignment!;
      const currentYear = new Date().getFullYear();

      // Smart date handling: preserve month/day, update years appropriately
      const originalEnrollmentStart = new Date(assignment.enrollmentStartDate);
      const originalEnrollmentEnd = new Date(assignment.enrollmentEndDate);
      const originalPlanEffective = new Date(assignment.planEffectiveDate);
      const originalPlanEnd = new Date(assignment.planEndDate);

      // Parse any date overrides from user input
      const dateFields = ['planEffectiveDate', 'planEndDate', 'enrollmentStartDate', 'enrollmentEndDate'];
      const parsedOverrides = overrides && Object.keys(overrides).some(key => dateFields.includes(key))
        ? safeParseDateFields(overrides, dateFields.filter(field => overrides[field]))
        : overrides;

      // Convert Mongoose document to plain object to avoid internal properties
      const assignmentData = assignment.toObject ? assignment.toObject() : assignment;

      const clonedData = {
        ...assignmentData,
        // Smart date handling: enrollment dates use current year, plan dates use next year
        enrollmentStartDate: parsedOverrides.enrollmentStartDate || new Date(currentYear, originalEnrollmentStart.getMonth(), originalEnrollmentStart.getDate()),
        enrollmentEndDate: parsedOverrides.enrollmentEndDate || new Date(currentYear, originalEnrollmentEnd.getMonth(), originalEnrollmentEnd.getDate()),
        planEffectiveDate: parsedOverrides.planEffectiveDate || new Date(currentYear + 1, originalPlanEffective.getMonth(), originalPlanEffective.getDate()),
        planEndDate: parsedOverrides.planEndDate || new Date(currentYear + 1, originalPlanEnd.getMonth(), originalPlanEnd.getDate()),
        ...parsedOverrides,
        assignedDate: new Date(),
        assignedBy: userId,
        // Cloned assignments are created as ACTIVE by default
        isActive: true,
        status: PLAN_ASSIGNMENT_STATUSES[0] // 'Active'
      };

      // Remove fields that shouldn't be cloned
      delete clonedData._id;
      delete clonedData.assignmentYear;
      delete clonedData.assignmentExpiry;
      delete clonedData.generatedBenefitIds;
      delete clonedData.createdAt;
      delete clonedData.updatedAt;
      delete clonedData.__v;

      const newAssignment = await PlanAssignmentModelClass.addData(clonedData);
      if (!newAssignment) {
        return { success: false, error: 'Failed to clone plan assignment', assignment: null };
      }

      return {
        success: true,
        error: null,
        assignment: newAssignment,
        originalId: assignmentId,
        requiresActivation: false,
        activationEndpoint: null,
        message: 'Plan assignment cloned successfully. Assignment is active and ready for use.',
        dateHandling: {
          enrollmentYear: currentYear,
          planYear: currentYear + 1,
          preservedMonthDay: true
        }
      };

    } catch (error) {
      logger.error('Error in clonePlanAssignment service:', error);
      return { success: false, error: 'Internal server error during plan assignment cloning', assignment: null };
    }
  }

  /**
   * Delete plan assignment with validation
   */
  static async deletePlanAssignment(assignmentId: string, userId: string, user: any) {
    try {
      const accessCheck = await this.getPlanAssignmentWithAccess(assignmentId, userId, user);
      if (!accessCheck.hasAccess) {
        return { success: false, error: accessCheck.reason };
      }

      // Check if assignment can be deleted (no enrollments reference it)
      const deleteCheck = await PlanAssignmentModelClass.canDeleteAssignment(assignmentId);
      if (!deleteCheck.canDelete) {
        return {
          success: false,
          error: `Cannot delete plan assignment with enrollment references. Reference count: ${deleteCheck.referenceCount}`
        };
      }

      const result = await PlanAssignmentModelClass.deleteData(assignmentId);
      if (result.modifiedCount === 0) {
        return { success: false, error: 'Failed to delete plan assignment' };
      }

      return { success: true, error: null };

    } catch (error) {
      logger.error('Error in deletePlanAssignment service:', error);
      return { success: false, error: 'Internal server error during plan assignment deletion' };
    }
  }

  /**
   * Check if plan assignment can be edited
   */
  static async canEditPlanAssignment(assignmentId: string, userId: string, user: any) {
    try {
      const accessCheck = await this.getPlanAssignmentWithAccess(assignmentId, userId, user);
      if (!accessCheck.hasAccess) {
        return { success: false, error: accessCheck.reason, result: null };
      }

      const editCheck = await PlanAssignmentModelClass.canEditAssignment(assignmentId);

      const result = {
        canEdit: editCheck.canEdit,
        referenceCount: editCheck.referenceCount,
        referencedBy: editCheck.referencedBy,
        message: editCheck.canEdit ? 'Assignment can be edited' : 'Assignment cannot be edited due to active enrollments'
      };

      return { success: true, error: null, result };

    } catch (error) {
      logger.error('Error in canEditPlanAssignment service:', error);
      return { success: false, error: 'Internal server error during edit check', result: null };
    }
  }

  /**
   * Check if plan assignment can be deleted
   */
  static async canDeletePlanAssignment(assignmentId: string, userId: string, user: any) {
    try {
      const accessCheck = await this.getPlanAssignmentWithAccess(assignmentId, userId, user);
      if (!accessCheck.hasAccess) {
        return { success: false, error: accessCheck.reason, result: null };
      }

      const deleteCheck = await PlanAssignmentModelClass.canDeleteAssignment(assignmentId);

      const result = {
        canDelete: deleteCheck.canDelete,
        referenceCount: deleteCheck.referenceCount,
        referencedBy: deleteCheck.referencedBy,
        message: deleteCheck.canDelete ? 'Assignment can be deleted' : 'Assignment cannot be deleted due to enrollment references'
      };

      return { success: true, error: null, result };

    } catch (error) {
      logger.error('Error in canDeletePlanAssignment service:', error);
      return { success: false, error: 'Internal server error during delete check', result: null };
    }
  }

  /**
   * Get enrollment references for plan assignment
   */
  static async getEnrollmentReferences(assignmentId: string, userId: string, user: any) {
    try {
      const accessCheck = await this.getPlanAssignmentWithAccess(assignmentId, userId, user);
      if (!accessCheck.hasAccess) {
        return { success: false, error: accessCheck.reason, result: null };
      }

      const enrollments = await PlanAssignmentModelClass.getEnrollmentsReferencingAssignment(assignmentId);

      const result = {
        enrollments,
        count: enrollments.length
      };

      return { success: true, error: null, result };

    } catch (error) {
      logger.error('Error in getEnrollmentReferences service:', error);
      return { success: false, error: 'Internal server error during enrollment references check', result: null };
    }
  }

  /**
   * Check expired assignments (SuperAdmin only)
   * Optimized to only check last 3 years by default for performance
   */
  static async checkExpiredAssignments(userId: string, user: any, yearsBack: number = 3) {
    try {
      // Only SuperAdmins can manually trigger expiry checks
      if (!UserModelClass.isSuperAdmin(user)) {
        return { success: false, error: 'Only SuperAdmins can manually check for expired assignments' };
      }

      // Use optimized method with year limit
      const result = await PlanAssignmentModelClass.checkExpiredAssignmentsWithYearLimit(yearsBack);

      // Log the operation for audit trail with optimization details
      logger.info(`Expired assignments check completed by user: ${userId}, expired: ${result.expiredCount}, year range: ${result.yearRange.from}-${result.yearRange.to}${result.skippedHistoricalCount ? `, skipped historical: ${result.skippedHistoricalCount}` : ''}`);

      return {
        success: true,
        error: null,
        expiredCount: result.expiredCount,
        updatedAssignments: result.updatedAssignments,
        yearRange: result.yearRange,
        skippedHistoricalCount: result.skippedHistoricalCount,
        optimizationNote: yearsBack < 10 ? `Optimized: Only checked last ${yearsBack} years for performance` : undefined
      };

    } catch (error) {
      logger.error('Error in checkExpiredAssignments service:', error);
      return { success: false, error: 'Internal server error during expired assignments check' };
    }
  }

  /**
   * 🎯 REFACTORED: Get effective assignments using optimized approach
   */
  static async getEffectiveAssignments(userId: string, user: any, filters: any = {}) {
    try {
      // Parse reference date (default to today)
      const referenceDate = filters.date ? new Date(filters.date) : new Date();
      if (filters.date && isNaN(referenceDate.getTime())) {
        return { success: false, error: 'Invalid date format. Use ISO date string', assignments: [] };
      }

      // 🎯 UNIFIED: Use optimized approach with effective date filtering
      const optimizedFilters = {
        ...filters,
        effectiveOnly: 'true',
        referenceDate: referenceDate.toISOString(),
        includePlanData: 'true' // Always include plan carrier data
      };

      // Use the optimized method with effective filtering
      const result = await this.getPlanAssignmentsOptimized(userId, user, optimizedFilters);

      return {
        success: true,
        error: null,
        assignments: result.assignments,
        referenceDate: referenceDate.toISOString(),
        companyId: filters.companyId || 'all',
        expiryInfo: result.expiryInfo,
        appliedFilters: result.appliedFilters
      };

    } catch (error) {
      logger.error('Error in getEffectiveAssignments service:', error);
      return { success: false, error: 'Internal server error', assignments: [] };
    }
  }

  /**
   * 🎯 REFACTORED: Get enrollment period assignments using optimized approach
   */
  static async getEnrollmentPeriodAssignments(userId: string, user: any, filters: any = {}) {
    try {
      // Parse reference date (default to today)
      const referenceDate = filters.date ? new Date(filters.date) : new Date();
      if (filters.date && isNaN(referenceDate.getTime())) {
        return { success: false, error: 'Invalid date format. Use ISO date string', assignments: [] };
      }

      // 🎯 UNIFIED: Use optimized approach with enrollment period filtering
      const optimizedFilters = {
        ...filters,
        enrollmentPeriodOnly: 'true',
        referenceDate: referenceDate.toISOString(),
        includePlanData: 'true' // Always include plan carrier data
      };

      // Use the optimized method with enrollment period filtering
      const result = await this.getPlanAssignmentsOptimized(userId, user, optimizedFilters);

      return {
        success: true,
        error: null,
        assignments: result.assignments,
        referenceDate: referenceDate.toISOString(),
        companyId: filters.companyId || 'all',
        expiryInfo: result.expiryInfo,
        appliedFilters: result.appliedFilters
      };

    } catch (error) {
      logger.error('Error in getEnrollmentPeriodAssignments service:', error);
      return { success: false, error: 'Internal server error', assignments: [] };
    }
  }

  /**
   * Reassign plan with proper validation
   */
  static async reassignPlan(assignmentId: string, newPlanId: string, userId: string, user: any) {
    try {
      const accessCheck = await this.getPlanAssignmentWithAccess(assignmentId, userId, user);
      if (!accessCheck.hasAccess) {
        return { success: false, error: accessCheck.reason, assignment: null };
      }

      // Check if assignment can be edited (≤1 enrollment references it)
      const editCheck = await PlanAssignmentModelClass.canEditAssignment(assignmentId);
      if (!editCheck.canEdit) {
        return {
          success: false,
          error: `Cannot reassign plan with active enrollments. Reference count: ${editCheck.referenceCount}`,
          assignment: null
        };
      }

      // Validate new plan exists and check status
      const newPlan = await PlanModelClass.getDataById(newPlanId);
      if (!newPlan) {
        return { success: false, error: 'New plan not found', assignment: null };
      }

      // Only allow Active plans for reassignment
      if (newPlan.status !== PLAN_STATUSES[1]) { // 'Active'
        return {
          success: false,
          error: `Cannot reassign to ${newPlan.status} plan. Only Active plans can be assigned.`,
          assignment: null
        };
      }

      // Block template plans
      if (newPlan.isTemplate) {
        return { success: false, error: 'Cannot reassign to template plans. Please duplicate the template first.', assignment: null };
      }

      // Only brokers can reassign, and they can only reassign to their own plans
      if (!user.isBroker) {
        return { success: false, error: 'Only Brokers can reassign plan assignments', assignment: null };
      }

      if (newPlan.brokerId !== userId) {
        return { success: false, error: 'Brokers can only reassign to their own plans', assignment: null };
      }

      // Store previous plan ID for response
      const assignment = await PlanAssignmentModelClass.getDataById(assignmentId);
      const previousPlanId = assignment?.planId;

      // Update only the planId in the database
      const result = await PlanAssignmentModelClass.updateData({
        id: assignmentId,
        data: { planId: newPlanId }
      });

      if (result.modifiedCount === 0) {
        return { success: false, error: 'Failed to reassign plan', assignment: null };
      }

      // Get updated assignment
      const updatedAssignment = await PlanAssignmentModelClass.getDataById(assignmentId);
      const reassignedAt = new Date();

      return {
        success: true,
        error: null,
        message: 'Plan reassigned successfully',
        assignment: {
          ...updatedAssignment,
          previousPlanId: previousPlanId,
          reassignedAt: reassignedAt
        }
      };

    } catch (error) {
      logger.error('Error in reassignPlan service:', error);
      return { success: false, error: 'Internal server error during plan reassignment', assignment: null };
    }
  }

  /**
   * Update time constraints
   */
  static async updateTimeConstraints(assignmentId: string, timeData: any, userId: string, user: any) {
    try {
      const accessCheck = await this.getPlanAssignmentWithAccess(assignmentId, userId, user);
      if (!accessCheck.hasAccess) {
        return { success: false, error: accessCheck.reason, assignment: null };
      }

      const assignment = accessCheck.assignment!;

      // Check if assignment can be edited (≤1 enrollment references it)
      const editCheck = await PlanAssignmentModelClass.canEditAssignment(assignmentId);
      if (!editCheck.canEdit) {
        return {
          success: false,
          error: `Cannot update time constraints with active enrollments. Reference count: ${editCheck.referenceCount}`,
          assignment: null
        };
      }

      // Parse and validate new dates with existing dates as fallback
      const parsedDates = safeParseDateFields(
        timeData,
        ['planEffectiveDate', 'planEndDate', 'enrollmentStartDate', 'enrollmentEndDate']
      );

      const effectiveDate = parsedDates.planEffectiveDate || assignment.planEffectiveDate;
      const endDate = parsedDates.planEndDate || assignment.planEndDate;
      const enrollStart = parsedDates.enrollmentStartDate || assignment.enrollmentStartDate;
      const enrollEnd = parsedDates.enrollmentEndDate || assignment.enrollmentEndDate;

      // Validate the complete set of dates
      const dateValidation = PlanAssignmentModelClass.validateAssignmentDates(
        effectiveDate, endDate, enrollStart, enrollEnd
      );

      if (!dateValidation.isValid) {
        return { success: false, error: 'Invalid time constraints: ' + dateValidation.errors.join(', '), assignment: null };
      }

      // Prepare update data (only include provided fields)
      const updateData: any = {};
      if (timeData.planEffectiveDate) updateData.planEffectiveDate = parsedDates.planEffectiveDate;
      if (timeData.planEndDate) updateData.planEndDate = parsedDates.planEndDate;
      if (timeData.enrollmentStartDate) updateData.enrollmentStartDate = parsedDates.enrollmentStartDate;
      if (timeData.enrollmentEndDate) updateData.enrollmentEndDate = parsedDates.enrollmentEndDate;

      // Update assignment
      const result = await PlanAssignmentModelClass.updateData({ id: assignmentId, data: updateData });
      if (result.modifiedCount === 0) {
        return { success: false, error: 'No changes made to time constraints', assignment: null };
      }

      // Get updated assignment
      const updatedAssignment = await PlanAssignmentModelClass.getDataById(assignmentId);
      return { success: true, error: null, assignment: updatedAssignment };

    } catch (error) {
      logger.error('Error in updateTimeConstraints service:', error);
      return { success: false, error: 'Internal server error during time constraints update', assignment: null };
    }
  }
}
