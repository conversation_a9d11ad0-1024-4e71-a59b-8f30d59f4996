#!/usr/bin/env python3
"""
Analyze SBS-Census-Final.csv issues without running full pipeline
"""

import pandas as pd
import os
from collections import Counter

def analyze_sbs_census():
    """Analyze the SBS census file for potential issues"""
    
    print("🔍 SBS-Census-Final.csv Issue Analysis")
    print("=" * 50)
    
    # Load the file
    csv_file = "scripts/test_csv_files/SBS-Census-Final (1).csv"
    
    if not os.path.exists(csv_file):
        print(f"❌ File not found: {csv_file}")
        return
    
    df = pd.read_csv(csv_file)
    
    print(f"📊 Basic File Info:")
    print(f"   - Total rows: {len(df)}")
    print(f"   - Total columns: {len(df.columns)}")
    print(f"   - Columns: {list(df.columns)}")
    
    # Analyze relationships
    print(f"\n👥 Relationship Analysis:")
    rel_counts = df['Relationship'].value_counts()
    print(f"   - Employees: {rel_counts.get('Employee', 0)}")
    print(f"   - Spouses: {rel_counts.get('Spouse', 0)}")
    print(f"   - Children: {rel_counts.get('Child', 0)}")
    
    # Analyze mandatory fields
    print(f"\n📋 Mandatory Field Analysis:")
    mandatory_fields = ['First Name', 'Last Name', 'Sex', 'Zip']
    
    for field in mandatory_fields:
        if field in df.columns:
            missing = df[field].isna().sum()
            empty = (df[field] == '').sum()
            print(f"   - {field}: {missing} missing, {empty} empty")
        else:
            print(f"   - {field}: ❌ COLUMN NOT FOUND")
    
    # Analyze marital status
    print(f"\n💑 Marital Status Analysis:")
    marital_counts = df['Marital Status'].value_counts(dropna=False)
    print(f"   - Distribution: {dict(marital_counts)}")
    
    # Analyze employees vs dependents
    employees = df[df['Relationship'] == 'Employee']
    dependents = df[df['Relationship'].isin(['Spouse', 'Child'])]
    
    print(f"\n👨‍💼 Employee Data Quality:")
    emp_marital_missing = employees['Marital Status'].isna().sum()
    print(f"   - Employees with missing marital status: {emp_marital_missing}")
    
    print(f"\n👨‍👩‍👧‍👦 Dependent Data Quality:")
    dep_marital_missing = dependents['Marital Status'].isna().sum()
    print(f"   - Dependents with missing marital status: {dep_marital_missing}")
    
    # Analyze address grouping potential
    print(f"\n🏠 Address Grouping Analysis:")
    address_groups = df.groupby(['Address 1', 'City', 'State', 'Zip']).size()
    family_sizes = address_groups[address_groups > 1]
    print(f"   - Addresses with multiple people: {len(family_sizes)}")
    print(f"   - Family sizes: {dict(family_sizes)}")
    
    # Analyze benefit codes
    print(f"\n💼 Benefit Code Analysis:")
    benefit_cols = ['Vision', 'Dental', 'Safe Harbor', 'Group Life', 'Group AD&D']
    
    for col in benefit_cols:
        if col in df.columns:
            unique_values = df[col].value_counts(dropna=False)
            print(f"   - {col}: {dict(unique_values)}")
    
    # Analyze date formats
    print(f"\n📅 Date Format Analysis:")
    sample_dates = df['DOB'].dropna().head(5).tolist()
    print(f"   - Sample DOB formats: {sample_dates}")
    
    # Analyze states for marketplace coverage
    print(f"\n🗺️ State Coverage Analysis:")
    states = df['State'].value_counts()
    print(f"   - States present: {dict(states)}")
    
    # Federal marketplace states
    federal_states = {
        "AL", "AK", "AZ", "AR", "DE", "FL", "GA", "HI", "IL", "IN", "IA", "KS", "LA",
        "MI", "MS", "MO", "MT", "NE", "NH", "NC", "ND", "OH", "OK", "SC", "SD", "TN",
        "TX", "UT", "VA", "WV", "WI", "WY"
    }
    
    file_states = set(df['State'].dropna().unique())
    supported_states = file_states.intersection(federal_states)
    unsupported_states = file_states - federal_states
    
    print(f"   - Supported by federal marketplace: {supported_states}")
    print(f"   - NOT supported by federal marketplace: {unsupported_states}")
    
    # Identify potential processing issues
    print(f"\n⚠️ Potential Processing Issues:")
    
    issues = []
    
    # Issue 1: No employee ID
    if 'Employee ID' not in df.columns and 'employee_id' not in df.columns:
        issues.append("❌ No employee ID field - will need address-based grouping")
    
    # Issue 2: Row-based format
    if len(df[df['Relationship'] == 'Employee']) < len(df):
        issues.append("❌ Row-based format detected - needs structure conversion")
    
    # Issue 3: Missing marital status for employees
    if emp_marital_missing > 0:
        issues.append(f"❌ {emp_marital_missing} employees missing marital status")
    
    # Issue 4: Benefit codes need mapping
    benefit_codes = set()
    for col in benefit_cols:
        if col in df.columns:
            benefit_codes.update(df[col].dropna().unique())
    
    if any(code in benefit_codes for code in ['EE', 'ES', 'EF', 'EC']):
        issues.append("❌ Benefit codes (EE, ES, EF, EC) need standardization")
    
    # Issue 5: Date format
    if any('/' in str(date) for date in sample_dates):
        issues.append("❌ Date format MM/DD/YYYY needs conversion to YYYY-MM-DD")
    
    # Issue 6: Unsupported states
    if unsupported_states:
        issues.append(f"❌ Unsupported states for health plans: {unsupported_states}")
    
    for i, issue in enumerate(issues, 1):
        print(f"   {i}. {issue}")
    
    if not issues:
        print("   ✅ No major issues detected")
    
    print(f"\n📊 Summary:")
    print(f"   - Total issues identified: {len(issues)}")
    print(f"   - Processing complexity: {'HIGH' if len(issues) > 3 else 'MEDIUM' if len(issues) > 1 else 'LOW'}")
    print(f"   - Recommended approach: Address-based grouping with benefit code mapping")

if __name__ == "__main__":
    analyze_sbs_census()
