from typing import Dict, List, Union, Optional
from pydantic import BaseModel, Field

class UserInput(BaseModel):
    """Pydantic model for user input validation."""

    user_message: str = Field(..., description="The message sent by the user")
    user_id: str = Field(..., description="Unique identifier for the user")
    team_id: str = Field(..., description="Identifier for the user's team")

class TeamsUserInput(BaseModel):
    """Pydantic model for user input validation."""

    user_message: str = Field(..., description="The message sent by the user")
    user_email: str = Field(..., description="Unique identifier for the user")

class PineconeInput(BaseModel):
    """Pydantic model for Pinecone index update validation."""
    team_id: str = Field(..., description="Identifier for the user's team")
    object_keys: Union[List[str], str] = Field(..., description="List of object keys or a single object key to update the index")

class PlanProcessingInput(BaseModel):
    """Pydantic model for plan document processing validation."""
    plan_id: str = Field(..., description="Identifier for the plan to process documents from")

class DocumentUpload(BaseModel):
    """Model for individual document upload."""
    filename: str = Field(..., description="Name of the document file")
    content: str = Field(..., description="Text content of the document")
    file_type: str = Field(..., description="Type of file (pdf, txt, etc.)")

class PlanProcessingDirectInput(BaseModel):
    """Pydantic model for plan document processing with direct document upload."""
    plan_id: Optional[str] = Field(None, description="Optional identifier for the plan")
    documents: List[DocumentUpload] = Field(..., description="List of documents with their content")
