"""
Census Controller - Main orchestrator for census file processing with corrected flow.
"""

import pandas as pd
import io
import logging
from typing import Dict, Any, Optional
from fastapi import UploadFile, HTTPException
from datetime import datetime, timezone

from .universalFieldMapper import <PERSON><PERSON>ieldMapper
from .patternIdentifier import PatternIdentifier, PatternType
from .dataPreprocessor import DataPreprocessor
from .dataEnrichment import DataEnrichmentEngine
from .modelPredictor import CensusModelPredictor
from .dataModels import (
    CensusProcessingResponse, ValidationIssue, FileInfo,
    PatternIdentificationResult, PreprocessingSummary,
    ValidationResult, ProcessingSummary, OutputDataInfo,
    InsufficientDataError, PatternUnrecognizedError,
    ValidationFailedError, ProcessingFailedError
)
from ...DataModels.ResponseModel import ResponseModel

logger = logging.getLogger(__name__)


class CensusResponseBuilder:
    """Builder for standardized Census API responses using data models."""

    @staticmethod
    def success_response(census_data: CensusProcessingResponse) -> ResponseModel:
        """Build success response using standard ResponseModel."""
        return ResponseModel(
            success=True,
            status_code=200,
            data=census_data.model_dump(),
            message="Census file processed successfully"
        )

    @staticmethod
    def error_response(error_code: str, message: str, status_code: int = 400,
                      details: Optional[Dict[str, Any]] = None) -> ResponseModel:
        """Build error response using standard ResponseModel."""
        error_data = {
            "error_code": error_code,
            "error_message": message
        }
        if details:
            error_data.update(details)

        return ResponseModel(
            success=False,
            status_code=status_code,
            error=error_code,
            message=message,
            data=error_data
        )

    @staticmethod
    def validation_error_response(validation_errors: list, details: Optional[Dict[str, Any]] = None) -> ResponseModel:
        """Build validation error response using ValidationFailedError model."""
        validation_issues = [
            ValidationIssue(row=0, field="unknown", issue=error)
            for error in validation_errors
        ]

        error_model = ValidationFailedError(
            validation_errors=validation_errors,
            error_rows=validation_issues
        )

        return ResponseModel(
            success=False,
            status_code=422,
            error="validation_failed",
            message=f"Validation failed with {len(validation_errors)} error(s)",
            data=error_model.model_dump()
        )


class CensusController:
    """Census controller with corrected flow: Universal Mapping → Pattern ID → Processing."""
    
    def __init__(self):
        self.universal_mapper = UniversalFieldMapper()
        self.pattern_identifier = PatternIdentifier()
        self.data_preprocessor = DataPreprocessor()
        self.data_enrichment_engine = DataEnrichmentEngine()
        self.model_predictor = CensusModelPredictor(models_path="app/models/Census")
        self.supported_formats = ['.csv', '.xlsx', '.xls']

        # Log model initialization status
        model_info = self.model_predictor.get_model_info()
        if model_info['models_loaded']:
            logger.info(f"Census models loaded successfully from app/models/Census")
            logger.info(f"Plan type classes: {model_info['plan_type_classes']}")
            logger.info(f"Valid benefit columns: {len(model_info['valid_benefit_columns'])}")
        else:
            logger.warning("Census models not loaded - will use rule-based fallback")
    
    async def process_census_file(self, file: UploadFile) -> Dict[str, Any]:
        """
        Process uploaded census file with CORRECTED flow.
        
        Flow: Upload → Universal Mapping → Pattern ID → Preprocessing → Validation → Output
        
        Args:
            file: Uploaded census file
            
        Returns:
            Dict with processing results or error information
        """
        logger.info(f"Starting census file processing for: {file.filename}")
        
        try:
            # Step 1: File validation and loading
            logger.info("Step 1: File validation and loading")
            file_result = await self._load_and_validate_file(file)
            if not file_result["success"]:
                return CensusResponseBuilder.error_response(
                    error_code=file_result["error"],
                    message=file_result["message"],
                    status_code=400
                ).model_dump()
            
            df : pd.DataFrame = file_result["dataframe"]
            file_info = file_result["file_info"]
            
            # Step 2: Universal field mapping (FIRST!)
            logger.info("Step 2: Universal field mapping - mapping ALL columns to system fields")
            mapping_result = await self.universal_mapper.map_all_fields(df.columns.tolist(), df.head(10))
            
            if not mapping_result["success"]:
                return CensusResponseBuilder.error_response(
                    error_code="field_mapping_failed",
                    message=mapping_result.get("message", "Field mapping failed"),
                    status_code=422,
                    details={
                        "step": "universal_field_mapping",
                        "file_info": file_info,
                        **mapping_result
                    }
                ).model_dump()
            
            # Apply mapping to DataFrame and remove unmapped columns
            mapped_df = self.universal_mapper.apply_mapping_to_dataframe(df, mapping_result["mapping"])
            
            logger.info(f"Mapped DataFrame: {len(mapped_df.columns)} standardized columns: {list(mapped_df.columns)}")
            
            # Step 3: Pattern identification using standardized field names
            logger.info("Step 3: Pattern identification using standardized field names")
            pattern_type, pattern_info = self.pattern_identifier.identify_pattern(mapped_df)
            
            if pattern_type == PatternType.UNKNOWN:
                return {
                    "success": False,
                    "error": "pattern_unrecognized",
                    "message": "Unable to identify census file pattern from standardized fields",
                    "standardized_fields": list(mapped_df.columns),
                    "file_info": file_info,
                    "field_mapping": mapping_result["mapping"]
                }
            
            # Step 4: Data preprocessing and validation
            logger.info(f"Step 4: Data preprocessing for pattern {pattern_type.value}")
            
            # Create identity mapping since columns are already standardized
            identity_mapping = {col: col for col in mapped_df.columns}
            preprocessing_result = await self.data_preprocessor.preprocess_data(mapped_df, identity_mapping, pattern_type.value)
            
            if not preprocessing_result["success"]:
                if preprocessing_result.get("error") == "validation_failed":
                    return CensusResponseBuilder.validation_error_response(
                        validation_errors=preprocessing_result.get("validation_errors", []),
                        details={
                            "step": "preprocessing",
                            "pattern_type": pattern_type.value,
                            "field_mapping": mapping_result["mapping"],
                            "error_rows": preprocessing_result.get("error_rows", [])
                        }
                    ).model_dump()
                else:
                    return CensusResponseBuilder.error_response(
                        error_code=preprocessing_result.get("error", "preprocessing_failed"),
                        message=preprocessing_result.get("message", "Data preprocessing failed"),
                        status_code=422,
                        details={
                            "step": "preprocessing",
                            "pattern_type": pattern_type.value,
                            "field_mapping": mapping_result["mapping"]
                        }
                    ).model_dump()
            
            # Step 5: Data Enrichment and Model Prediction
            logger.info("Step 5: Data enrichment and model prediction")

            # Convert DataFrame to CSV string for enrichment step
            preprocessed_df = preprocessing_result["preprocessed_data"]
            preprocessed_csv = preprocessed_df.to_csv(index=False)

            enrichment_result = await self._enrich_and_predict(
                preprocessed_csv,
                pattern_type,
                mapping_result,
                preprocessing_result
            )

            if not enrichment_result["success"]:
                return CensusResponseBuilder.error_response(
                    error_code=enrichment_result.get("error", "enrichment_failed"),
                    message=enrichment_result.get("message", "Data enrichment and prediction failed"),
                    status_code=422,
                    details={
                        "step": "enrichment_and_prediction",
                        "pattern_type": pattern_type.value,
                        "field_mapping": mapping_result["mapping"]
                    }
                ).model_dump()

            # Step 6: Generate final output
            logger.info("Step 6: Generating final output")
            final_result = self._generate_final_output(
                enrichment_result["enriched_data"],  # Now a DataFrame
                pattern_type,
                mapping_result,
                preprocessing_result,
                file_info,
                pattern_info,
                enrichment_result
            )
            
            logger.info("Census file processing completed successfully")
            # Convert final_result to CensusProcessingResponse model
            census_response = CensusProcessingResponse(**final_result)
            return CensusResponseBuilder.success_response(census_response).model_dump()

        except Exception as e:
            logger.error(f"Census processing failed: {str(e)}")
            return CensusResponseBuilder.error_response(
                error_code="internal_processing_error",
                message=f"Internal processing error: {str(e)}",
                status_code=500
            ).model_dump()
    
    async def _load_and_validate_file(self, file: UploadFile) -> Dict[str, Any]:
        """Load and validate uploaded file."""
        
        # Validate file format
        filename = file.filename or "unknown"
        extension = f".{filename.split('.')[-1].lower()}" if '.' in filename else ""
        
        if extension not in self.supported_formats:
            return CensusResponseBuilder.error_response(
                error_code="unsupported_format",
                message=f"Unsupported file format: {extension}. Supported: {self.supported_formats}",
                status_code=400,
                details={"provided_format": extension, "supported_formats": self.supported_formats}
            ).model_dump()
        
        try:
            # Read file content
            content = await file.read()
            
            # Parse based on file type
            if extension == '.csv':
                df = self._parse_csv(content)
            elif extension in ['.xlsx', '.xls']:
                df = self._parse_excel(content)
            else:
                raise ValueError(f"Unsupported format: {extension}")
            
            # Basic validation
            if df.empty:
                return CensusResponseBuilder.error_response(
                    error_code="empty_file",
                    message="File is empty or contains no data",
                    status_code=400,
                    details={"filename": filename, "file_size": len(content)}
                ).model_dump()

            if len(df.columns) < 4:
                return CensusResponseBuilder.error_response(
                    error_code="insufficient_columns",
                    message=f"File has only {len(df.columns)} columns, minimum 4 required",
                    status_code=400,
                    details={"columns_found": len(df.columns), "minimum_required": 4, "column_names": df.columns.tolist()}
                ).model_dump()

            # Pre-validation null value check (before field mapping)
            pre_validation_result = self._pre_validate_null_values(df)
            if not pre_validation_result["is_valid"]:
                return CensusResponseBuilder.validation_error_response(
                    validation_errors=pre_validation_result["errors"],
                    details={
                        "null_analysis": pre_validation_result["null_analysis"],
                        "recommendations": pre_validation_result["recommendations"],
                        "warnings": pre_validation_result.get("warnings", [])
                    }
                ).model_dump()
            
            file_info = {
                "filename": filename,
                "extension": extension,
                "size": len(content),
                "rows": len(df),
                "columns": len(df.columns),
                "original_column_names": df.columns.tolist()
            }
            
            return {
                "success": True,
                "dataframe": df,
                "file_info": file_info
            }
            
        except Exception as e:
            return CensusResponseBuilder.error_response(
                error_code="file_parsing_failed",
                message=f"Failed to parse file: {str(e)}",
                status_code=400,
                details={"filename": filename, "file_extension": extension}
            ).model_dump()
    
    def _parse_csv(self, content: bytes) -> pd.DataFrame:
        """Parse CSV content with multiple encoding attempts and proper CSV handling."""
        for encoding in ['utf-8', 'latin-1', 'cp1252']:
            try:
                csv_string = content.decode(encoding)
                # Use proper CSV parsing with quoting support
                return pd.read_csv(
                    io.StringIO(csv_string),
                    quotechar='"',
                    skipinitialspace=True,
                    na_values=['', 'NULL', 'null', 'N/A', 'n/a'],
                    keep_default_na=True
                )
            except UnicodeDecodeError:
                continue
        raise ValueError("Unable to decode CSV file with any supported encoding")
    
    def _parse_excel(self, content: bytes) -> pd.DataFrame:
        """Parse Excel content."""
        return pd.read_excel(io.BytesIO(content))
    
    def _generate_final_output(self, enriched_data_df: pd.DataFrame, pattern_type: PatternType,
                              mapping_result: Dict, preprocessing_result: Dict,
                              file_info: Dict, pattern_info: Dict,
                              enrichment_result: Optional[Dict] = None) -> Dict[str, Any]:
        """Generate final output with enriched data as primary content and processing metadata separate."""

        # Use the enriched DataFrame directly
        enriched_df = enriched_data_df

        # Extract comprehensive statistics from enrichment result
        comprehensive_stats = enrichment_result.get("comprehensive_statistics", {}) if enrichment_result else {}

        # Primary data output
        data_output = {
            "enriched_data_csv": enriched_df.to_csv(index=False),
            "comprehensive_statistics": comprehensive_stats,
            "prediction_summary": enrichment_result.get("prediction_summary", {}) if enrichment_result else {},
            "total_employees": len(enriched_df),
            "total_columns": len(enriched_df.columns),
            "has_predictions": any(col.startswith('predicted_') for col in enriched_df.columns),
            "prediction_columns": [col for col in enriched_df.columns if col.startswith('predicted_') or col.endswith('_confidence')]
        }

        # Processing metadata (consolidated to avoid duplication)
        processing_metadata = {
            "file_processing": {
                "original_filename": file_info.get("filename"),
                "original_rows": file_info.get("rows", len(enriched_df)),
                "original_columns": len(file_info.get("original_column_names", [])),
                "file_size": file_info.get("size"),
                "processing_time": file_info.get("processing_time")
            },
            "pattern_analysis": {
                "pattern_type": pattern_type.value,
                "pattern_description": self.pattern_identifier.get_pattern_description(pattern_type),
                "confidence": pattern_info.get("confidence", 0),
                "detection_reason": pattern_info.get("reason", "")
            },
            "field_mapping": {
                "total_original_columns": len(file_info.get("original_column_names", [])),
                "mapped_columns": len(mapping_result["mapping"]),
                "unmapped_columns": len(mapping_result.get("unmapped_fields", [])),
                "mapping_success_rate": round(len(mapping_result["mapping"]) / len(file_info.get("original_column_names", [1])) * 100, 2),
                "unmapped_fields": mapping_result.get("unmapped_fields", [])
            },
            "data_processing": {
                "validation_passed": preprocessing_result["validation_result"]["is_valid"],
                "validation_errors": len(preprocessing_result["validation_result"]["errors"]),
                "data_quality_score": self._calculate_data_quality_score(preprocessing_result),
                "processing_summary": preprocessing_result["summary"]
            },
            "enrichment_and_prediction": {
                "prediction_method": enrichment_result.get("prediction_method", "unknown") if enrichment_result else "none",
                "enrichment_summary": enrichment_result.get("enrichment_summary", {}) if enrichment_result else {},
                "model_predictions": enrichment_result.get("model_predictions", {}) if enrichment_result else {},
                "prediction_summary": enrichment_result.get("prediction_summary", {}) if enrichment_result else {},
                "feature_validation": enrichment_result.get("feature_validation", {}) if enrichment_result else {}
            }
        }

        return {
            "success": True,
            "data": data_output,
            "metadata": processing_metadata
        }
    
    def _calculate_data_quality_score(self, preprocessing_result: Dict) -> float:
        """Calculate overall data quality score (0-1)."""
        summary = preprocessing_result["summary"]
        validation = preprocessing_result["validation_result"]
        
        # Base score from validation
        validation_score = 1.0 if validation["is_valid"] else 0.5
        
        # Completeness score
        total_rows = summary["processed_rows"]
        complete_rows = summary["data_quality"]["complete_records"]
        completeness_score = complete_rows / total_rows if total_rows > 0 else 0
        
        # Processing success score
        processing_score = 1.0 if summary["fields_processed"]["name_fields"] else 0.8
        if summary["fields_processed"]["age_converted"]:
            processing_score += 0.1
        if summary["fields_processed"]["gender_standardized"]:
            processing_score += 0.1
        
        processing_score = min(1.0, processing_score)
        
        # Weighted average
        quality_score = (validation_score * 0.4 + completeness_score * 0.4 + processing_score * 0.2)
        
        return round(quality_score, 3)

    def _pre_validate_null_values(self, df: pd.DataFrame) -> Dict:
        """
        Simplified pre-validation: Only check critical fields (DOB, age, name, address, zipcode) for null values.
        """
        logger.info("Performing simplified pre-validation null value check")

        errors = []
        warnings = []
        null_analysis = {}
        recommendations = []

        # Define critical field patterns (case-insensitive) - only core employee fields
        critical_patterns = {
            'dob': ['dob', 'birth', 'date_of_birth', 'birthdate', 'birth_date'],
            'age': ['age'],
            'name': ['name', 'first_name', 'last_name', 'fname', 'lname', 'firstname', 'lastname'],
            'zipcode': ['zip', 'zipcode', 'postal', 'postal_code', 'zip_code']
        }

        # Find columns that match critical patterns (exclude dependent columns)
        critical_columns = []
        for col in df.columns:
            col_lower = col.lower()

            # Skip dependent columns (dept_1, dept_2, etc.) as they can legitimately be empty
            if 'dept_' in col_lower or 'dependent' in col_lower:
                continue

            for field_type, patterns in critical_patterns.items():
                if any(pattern in col_lower for pattern in patterns):
                    critical_columns.append({
                        'column': col,
                        'field_type': field_type,
                        'pattern_matched': next(p for p in patterns if p in col_lower)
                    })
                    break

        # Check critical columns for null values
        total_rows = len(df)
        for col_info in critical_columns:
            col_name = col_info['column']
            field_type = col_info['field_type']

            # Count null values (including empty strings and "None")
            null_count = df[col_name].isna().sum() + (df[col_name] == "").sum() + (df[col_name] == "None").sum()

            if null_count > 0:
                null_percentage = (null_count / total_rows) * 100
                errors.append(f"Critical field '{col_name}' ({field_type}) has {null_count} null values ({null_percentage:.1f}% of {total_rows} rows)")

                null_analysis[col_name] = {
                    "field_type": field_type,
                    "null_count": null_count,
                    "null_percentage": round(null_percentage, 2),
                    "total_rows": total_rows
                }

        # Generate recommendations only if errors found
        if errors:
            recommendations.append("Clean or fill missing values in critical fields (DOB, age, name, address, zipcode)")
            recommendations.append("Ensure data export includes all required demographic fields")

        # Prepare simplified null analysis summary
        summary_analysis = {
            "total_columns": len(df.columns),
            "critical_columns_checked": len(critical_columns),
            "critical_columns_with_nulls": len(null_analysis),
            "critical_field_details": null_analysis
        }

        # Determine if validation passes
        is_valid = len(errors) == 0

        if is_valid:
            logger.info("Pre-validation null check passed")
        else:
            logger.warning(f"Pre-validation failed with {len(errors)} critical errors")

        return {
            "is_valid": is_valid,
            "errors": errors,
            "warnings": warnings,
            "null_analysis": summary_analysis,
            "recommendations": recommendations
        }

    def _identify_likely_important_columns(self, column_names: list) -> list:
        """
        Identify likely important columns using heuristics before field mapping.
        Returns list of dicts with column info and importance level.
        """
        likely_columns = []

        # Define patterns for different field types with importance levels
        field_patterns = {
            # Critical fields (must have low null rates)
            "critical": {
                "employee_id": ["id", "emp_id", "employee_id", "empno", "employee_number", "staff_id", "member_id"],
                "name": ["name", "first_name", "fname", "firstname", "last_name", "lname", "lastname", "full_name", "employee_name"],
                "gender": ["gender", "sex", "male_female", "m_f"],
                "zipcode": ["zip", "zipcode", "postal_code", "zip_code", "postcode"]
            },
            # High importance fields
            "high": {
                "marital_status": ["marital", "marital_status", "married", "marriage_status", "spouse"],
                "dob": ["dob", "birth_date", "birthdate", "date_of_birth", "birth", "age"],
                "address": ["address", "address1", "street", "home_address", "mailing_address"],
                "city": ["city", "town", "municipality"],
                "state": ["state", "province", "region"],
                "employment": ["employment", "emp_type", "employment_type", "status", "job_status"]
            },
            # Medium importance fields
            "medium": {
                "salary": ["salary", "wage", "income", "pay", "compensation", "annual_salary"],
                "hire_date": ["hire_date", "start_date", "employment_date", "join_date"],
                "department": ["department", "dept", "division", "team", "unit"],
                "phone": ["phone", "telephone", "mobile", "cell", "contact"],
                "email": ["email", "e_mail", "email_address", "contact_email"]
            }
        }

        # Check each column against patterns
        for col in column_names:
            col_lower = col.lower().strip()

            found = False
            for importance, field_types in field_patterns.items():
                if found:
                    break

                for field_type, patterns in field_types.items():
                    if any(pattern in col_lower for pattern in patterns):
                        likely_columns.append({
                            "column": col,
                            "likely_field": field_type,
                            "importance": importance,
                            "matched_pattern": next(p for p in patterns if p in col_lower)
                        })
                        found = True
                        break

        return likely_columns

    async def _enrich_and_predict(self, preprocessed_data_csv: str, pattern_type: PatternType,
                                 mapping_result: Dict, preprocessing_result: Dict) -> Dict[str, Any]:
        """
        Step 5: Enrich data with missing fields and predict plan types and benefits.

        Args:
            preprocessed_data_csv: CSV string of preprocessed data
            pattern_type: Detected pattern type
            mapping_result: Field mapping results
            preprocessing_result: Preprocessing results

        Returns:
            Dictionary containing enrichment and prediction results
        """
        try:
            logger.info("Starting data enrichment and model prediction")

            # Convert CSV string to DataFrame
            from io import StringIO
            df = pd.read_csv(StringIO(preprocessed_data_csv))

            logger.info(f"Loaded {len(df)} employees for enrichment and prediction")

            # Step 5.1: Data Enrichment
            logger.info("Step 5.1: Enriching data with missing fields")
            original_df = df.copy()
            enriched_df, comprehensive_stats = self.data_enrichment_engine.enrich_dataframe(df)

            # Get enrichment summary
            enrichment_summary = self.data_enrichment_engine.get_enrichment_summary(original_df, enriched_df)

            # Step 5.2: Validate features for prediction
            logger.info("Step 5.2: Validating features for model prediction")
            feature_validation = self.model_predictor.validate_features(enriched_df)

            if not feature_validation["ready_for_prediction"]:
                logger.warning(f"Features not ready for prediction. Completion rate: {feature_validation['overall_completion_rate']:.1f}%")

                # If models are not available or features are incomplete, use fallback
                if not self.model_predictor.models_loaded:
                    logger.info("Models not loaded, using rule-based fallback")
                    enriched_df = self._apply_rule_based_predictions(enriched_df)

                    return {
                        "success": True,
                        "enriched_data": enriched_df,  # Return DataFrame directly
                        "enrichment_summary": enrichment_summary,
                        "comprehensive_statistics": comprehensive_stats,  # Add comprehensive statistics
                        "feature_validation": feature_validation,
                        "prediction_method": "rule_based_fallback",
                        "model_predictions": None,
                        "prediction_summary": {
                            "total_employees": len(enriched_df),
                            "method_used": "rule_based",
                            "reason": "Models not available or features incomplete"
                        }
                    }

            # Step 5.3: Plan Type Prediction
            logger.info("Step 5.3: Predicting health plan types")
            try:
                enriched_df = self.model_predictor.predict_plan_types(enriched_df, top_k=3)
            except Exception as e:
                logger.warning(f"Plan type prediction failed: {str(e)}, using fallback")
                # Add fallback plan columns
                enriched_df['predicted_plan_type'] = ['PPO'] * len(enriched_df)
                enriched_df['plan_confidence'] = [0.5] * len(enriched_df)
                enriched_df['plan_reason'] = ['Fallback prediction due to model error'] * len(enriched_df)
                enriched_df['top_3_plans'] = [['PPO', 'HMO', 'HDHP']] * len(enriched_df)
                enriched_df['top_3_plan_confidences'] = [[0.5, 0.3, 0.2]] * len(enriched_df)

            # Step 5.4: Benefits Prediction
            logger.info("Step 5.4: Predicting benefits assignment")
            try:
                enriched_df = self.model_predictor.predict_benefits(enriched_df, top_k=3)
            except Exception as e:
                logger.warning(f"Benefits prediction failed: {str(e)}, using fallback")
                # Add fallback benefits columns
                enriched_df['predicted_benefits'] = [['Dental', 'Vision']] * len(enriched_df)
                enriched_df['benefits_confidence'] = [0.5] * len(enriched_df)
                enriched_df['benefits_reason'] = ['Fallback prediction due to model error'] * len(enriched_df)
                enriched_df['top_3_benefits'] = [['Dental', 'Vision', 'Term Life']] * len(enriched_df)
                enriched_df['top_3_benefits_confidences'] = [[0.5, 0.4, 0.3]] * len(enriched_df)

            # Step 5.5: Generate prediction summary
            prediction_summary = self._generate_prediction_summary(enriched_df)

            logger.info("Data enrichment and model prediction completed successfully")

            return {
                "success": True,
                "enriched_data": enriched_df,  # Return DataFrame directly
                "enrichment_summary": enrichment_summary,
                "comprehensive_statistics": comprehensive_stats,  # Add comprehensive statistics
                "feature_validation": feature_validation,
                "prediction_method": "ml_models",
                "model_predictions": {
                    "plan_types": {
                        "total_predictions": len(enriched_df),
                        "average_confidence": enriched_df['plan_confidence'].mean() if 'plan_confidence' in enriched_df.columns else 0,
                        "unique_plans": enriched_df['predicted_plan_type'].nunique() if 'predicted_plan_type' in enriched_df.columns else 0
                    },
                    "benefits": {
                        "total_predictions": len(enriched_df),
                        "average_confidence": enriched_df['benefits_confidence'].mean() if 'benefits_confidence' in enriched_df.columns else 0,
                        "average_benefits_per_employee": enriched_df['predicted_benefits'].apply(len).mean() if 'predicted_benefits' in enriched_df.columns else 0
                    }
                },
                "prediction_summary": prediction_summary
            }

        except Exception as e:
            logger.error(f"Error in data enrichment and prediction: {str(e)}")
            import traceback
            logger.error(f"Full traceback: {traceback.format_exc()}")
            return {
                "success": False,
                "error": "enrichment_prediction_failed",
                "message": f"Data enrichment and prediction failed: {str(e)}"
            }

    def _apply_rule_based_predictions(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Apply rule-based predictions when ML models are not available.
        Uses the standardized functions from standarize.py
        """
        try:

            logger.info("Applying rule-based predictions using standardized functions")

            # Apply plan type prediction with reasoning
            plan_results = df.apply(lambda row: self.model_predictor.assign_health_plan_with_reason(row, reason=True), axis=1)
            df['predicted_plan_type'] = [result[0] for result in plan_results]
            df['plan_reason'] = [result[1] for result in plan_results]
            df['plan_confidence'] = 0.75  # Default confidence for rule-based
            df['top_3_plans'] = df['predicted_plan_type'].apply(lambda x: [x, 'PPO', 'HMO'])
            df['top_3_plan_confidences'] = [[0.75, 0.15, 0.10]] * len(df)

            # Apply benefits prediction with reasoning
            benefits_results = df.apply(lambda row: self.model_predictor.assign_benefits_with_reason(row, reason=True), axis=1)
            df['predicted_benefits'] = [result[0] for result in benefits_results]
            benefits_reasons = [result[1] for result in benefits_results]

            # Create benefits reasoning text
            df['benefits_reason'] = []
            for i, reasons_dict in enumerate(benefits_reasons):
                benefits_list = df.iloc[i]['predicted_benefits']
                reasoning_text = []
                for benefit in benefits_list:
                    if benefit in reasons_dict:
                        reasoning_text.append(f"{benefit}: {reasons_dict[benefit]}")
                df.at[i, 'benefits_reason'] = "; ".join(reasoning_text)

            df['benefits_confidence'] = 0.70  # Default confidence for rule-based
            df['top_3_benefits'] = df['predicted_benefits'].apply(lambda x: x[:3] if len(x) >= 3 else x + ['Dental', 'Vision'][:3-len(x)])
            df['top_3_benefits_confidences'] = df['predicted_benefits'].apply(lambda x: [0.70] * min(3, len(x)) + [0.30] * max(0, 3-len(x)))

            return df

        except Exception as e:
            logger.error(f"Error in rule-based predictions: {str(e)}")
            # Return dataframe with minimal prediction data
            df['predicted_plan_type'] = 'PPO'
            df['plan_confidence'] = 0.50
            df['predicted_benefits'] = [['Dental', 'Vision']] * len(df)
            df['benefits_confidence'] = 0.50
            return df

    def _generate_prediction_summary(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Generate comprehensive summary statistics for predictions."""
        summary = {
            "total_employees": len(df),
            "plan_type_distribution": {},
            "benefits_distribution": {},
            "confidence_metrics": {},
            "prediction_success_rates": {},
            "top_3_plan_probabilities": []
        }

        # Plan type distribution (include failed predictions)
        if 'predicted_plan_type' in df.columns:
            # Count all values including NaN/None
            plan_counts = df['predicted_plan_type'].value_counts(dropna=False).to_dict()

            # Separate successful vs failed predictions
            successful_predictions = df['predicted_plan_type'].notna().sum()
            failed_predictions = df['predicted_plan_type'].isna().sum()

            summary["plan_type_distribution"] = {
                "successful_predictions": dict(df['predicted_plan_type'].value_counts().to_dict()),
                "failed_predictions": failed_predictions,
                "success_rate": round(successful_predictions / len(df) * 100, 2),
                "total_predicted": successful_predictions
            }

        # Benefits distribution
        if 'predicted_benefits' in df.columns:
            all_benefits = []
            successful_benefit_predictions = 0

            for benefits_list in df['predicted_benefits']:
                if isinstance(benefits_list, list) and len(benefits_list) > 0:
                    all_benefits.extend(benefits_list)
                    successful_benefit_predictions += 1

            from collections import Counter
            benefits_counts = Counter(all_benefits)
            summary["benefits_distribution"] = {
                "benefit_counts": dict(benefits_counts),
                "successful_predictions": successful_benefit_predictions,
                "success_rate": round(successful_benefit_predictions / len(df) * 100, 2)
            }

        # Confidence metrics
        if 'plan_confidence' in df.columns:
            valid_plan_confidences = df['plan_confidence'].dropna()
            summary["confidence_metrics"]["plan_confidence"] = {
                "mean": round(valid_plan_confidences.mean(), 3) if len(valid_plan_confidences) > 0 else 0,
                "min": round(valid_plan_confidences.min(), 3) if len(valid_plan_confidences) > 0 else 0,
                "max": round(valid_plan_confidences.max(), 3) if len(valid_plan_confidences) > 0 else 0,
                "count": len(valid_plan_confidences)
            }

        if 'benefits_confidence' in df.columns:
            valid_benefit_confidences = df['benefits_confidence'].dropna()
            summary["confidence_metrics"]["benefits_confidence"] = {
                "mean": round(valid_benefit_confidences.mean(), 3) if len(valid_benefit_confidences) > 0 else 0,
                "min": round(valid_benefit_confidences.min(), 3) if len(valid_benefit_confidences) > 0 else 0,
                "max": round(valid_benefit_confidences.max(), 3) if len(valid_benefit_confidences) > 0 else 0,
                "count": len(valid_benefit_confidences)
            }

        # Top 3 plan probabilities for each employee
        if 'top_3_plans' in df.columns and 'top_3_plan_confidences' in df.columns:
            for idx, row in df.iterrows():
                employee_id = row.get('employee_id', f'Employee_{idx}')
                top_plans = row.get('top_3_plans', [])
                top_confidences = row.get('top_3_plan_confidences', [])

                if isinstance(top_plans, list) and isinstance(top_confidences, list):
                    employee_probs = {
                        "employee_id": employee_id,
                        "top_3_predictions": []
                    }

                    for i, (plan, conf) in enumerate(zip(top_plans, top_confidences)):
                        employee_probs["top_3_predictions"].append({
                            "rank": i + 1,
                            "plan_type": plan,
                            "probability": round(float(conf), 3) if conf is not None else 0
                        })

                    summary["top_3_plan_probabilities"].append(employee_probs)

        if 'benefits_confidence' in df.columns:
            summary["confidence_metrics"]["benefits_confidence"] = {
                "mean": df['benefits_confidence'].mean(),
                "min": df['benefits_confidence'].min(),
                "max": df['benefits_confidence'].max()
            }

        return summary

    def get_supported_patterns(self) -> Dict[str, str]:
        """Get list of supported patterns with descriptions."""
        return {
            pattern_type.value: self.pattern_identifier.get_pattern_description(pattern_type)
            for pattern_type in PatternType if pattern_type != PatternType.UNKNOWN
        }
    
    def get_field_requirements(self) -> Dict:
        """Get field mapping requirements."""
        return {
            "mandatory_fields": self.universal_mapper.mandatory_fields,
            "all_system_fields": list(self.universal_mapper.system_fields.keys()),
            "field_descriptions": {k: v["description"] for k, v in self.universal_mapper.system_fields.items()}
        }
