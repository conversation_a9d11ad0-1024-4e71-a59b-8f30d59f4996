"""
Refactored Census Controller - Clean, modular orchestrator for census file processing.

This refactored version separates concerns into dedicated services:
- FileService: File upload, validation, and parsing
- ValidationService: Data validation logic
- ResponseService: Response building and formatting
- OrchestrationService: Main business logic orchestration

Benefits:
- Reduced complexity (from 840 lines to ~100 lines)
- Clear separation of concerns
- Improved testability
- Better maintainability
- Eliminated redundant code
"""

import logging
from typing import Dict, Any
from fastapi import UploadFile

from .services.orchestrationService import OrchestrationService

logger = logging.getLogger(__name__)


class CensusController:
    """
    Refactored Census Controller - Clean and modular.
    
    This controller now acts as a thin facade over the OrchestrationService,
    providing a clean API interface while delegating all business logic
    to specialized services.
    """
    
    def __init__(self):
        """Initialize the controller with orchestration service."""
        self.orchestration_service = OrchestrationService()
        logger.info("CensusController initialized with modular services")
    
    async def process_census_file(self, file: UploadFile) -> Dict[str, Any]:
        """
        Process uploaded census file.
        
        This method now simply delegates to the orchestration service,
        which handles the entire processing pipeline through specialized services.
        
        Args:
            file: Uploaded census file
            
        Returns:
            Dict with processing results or error information
            
        Pipeline:
            1. File Loading & Validation (FileService)
            2. Field Mapping (UniversalFieldMapper)
            3. Pattern Identification (PatternIdentifier)
            4. Data Preprocessing (DataPreprocessor)
            5. Data Enrichment & Prediction (DataEnrichmentEngine)
            6. Response Building (ResponseService)
        """
        logger.info(f"Processing census file: {file.filename}")
        
        # Delegate entire processing to orchestration service
        result = await self.orchestration_service.process_census_file(file)
        
        # Log final result
        success = result.get("success", False)
        logger.info(f"Census processing {'completed successfully' if success else 'failed'}")
        
        return result
    
    def get_processing_info(self) -> Dict[str, Any]:
        """
        Get information about the processing capabilities.
        
        Returns:
            Dict with processing information
        """
        return {
            "supported_formats": ['.csv', '.xlsx', '.xls'],
            "max_file_size_mb": 50,
            "processing_steps": [
                "File Loading & Validation",
                "Universal Field Mapping", 
                "Pattern Identification",
                "Data Preprocessing",
                "Data Enrichment",
                "Model Prediction",
                "Response Generation"
            ],
            "pattern_types_supported": [
                "column_based_single_row",
                "row_based_member_level"
            ],
            "models_loaded": self.orchestration_service.model_predictor.get_model_info()['models_loaded']
        }
    
    def get_health_status(self) -> Dict[str, Any]:
        """
        Get health status of all processing components.
        
        Returns:
            Dict with health status information
        """
        try:
            model_info = self.orchestration_service.model_predictor.get_model_info()
            
            return {
                "status": "healthy",
                "components": {
                    "file_service": "operational",
                    "validation_service": "operational", 
                    "field_mapper": "operational",
                    "pattern_identifier": "operational",
                    "data_preprocessor": "operational",
                    "enrichment_engine": "operational",
                    "model_predictor": "operational" if model_info['models_loaded'] else "fallback_mode",
                    "response_service": "operational"
                },
                "models": {
                    "loaded": model_info['models_loaded'],
                    "plan_types": len(model_info.get('plan_type_classes', [])),
                    "benefit_columns": len(model_info.get('valid_benefit_columns', []))
                }
            }
        except Exception as e:
            logger.error(f"Health check failed: {str(e)}")
            return {
                "status": "degraded",
                "error": str(e),
                "components": {
                    "overall": "error"
                }
            }


# Legacy compatibility - maintain the same interface
class CensusResponseBuilder:
    """
    Legacy compatibility class.
    
    This class is maintained for backward compatibility but now delegates
    to the new ResponseService.
    """
    
    @staticmethod
    def success_response(data: Dict[str, Any]) -> Dict[str, Any]:
        """Build success response - delegates to ResponseService."""
        from .services.responseService import ResponseService
        return ResponseService.success_response(data)
    
    @staticmethod
    def error_response(error_code: str, message: str, status_code: int = 400,
                      details: Dict[str, Any] = None) -> Dict[str, Any]:
        """Build error response - delegates to ResponseService."""
        from .services.responseService import ResponseService
        return ResponseService.error_response(error_code, message, status_code, details)
    
    @staticmethod
    def validation_error_response(validation_errors: list, 
                                details: Dict[str, Any] = None) -> Dict[str, Any]:
        """Build validation error response - delegates to ResponseService."""
        from .services.responseService import ResponseService
        return ResponseService.validation_error_response(validation_errors, details=details)


# Module-level convenience functions for backward compatibility
async def process_census_file(file: UploadFile) -> Dict[str, Any]:
    """
    Convenience function for processing census files.
    
    This maintains backward compatibility while using the new modular structure.
    """
    controller = CensusController()
    return await controller.process_census_file(file)


def get_supported_formats() -> list:
    """Get list of supported file formats."""
    return ['.csv', '.xlsx', '.xls']


def get_processing_capabilities() -> Dict[str, Any]:
    """Get processing capabilities information."""
    controller = CensusController()
    return controller.get_processing_info()


# Export the main classes for external use
__all__ = [
    'CensusController',
    'CensusResponseBuilder',  # Legacy compatibility
    'process_census_file',    # Convenience function
    'get_supported_formats',  # Utility function
    'get_processing_capabilities'  # Info function
]
