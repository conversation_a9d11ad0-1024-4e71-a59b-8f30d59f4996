"""
Data Enrichment Module for Census Processing
Enriches preprocessed census data with missing fields required for ML model prediction
"""

import pandas as pd
import numpy as np
import logging
from typing import Dict, List, Any
from datetime import datetime
import random

from .services.zipCodeLocationService import ZipCodeLocationService

logger = logging.getLogger(__name__)



class DataEnrichmentEngine:
    """
    Enriches census data with missing fields required for plan type and benefits prediction.
    Uses intelligent heuristics and rules-based inference to fill missing data.
    """
    
    def __init__(self):
        """Initialize the data enrichment engine."""

        # Initialize ZIP code location service
        self.zip_service = ZipCodeLocationService()

        # Required features for ML models
        self.required_features = [
            'age', 'gender', 'marital_status', 'dept_count', 'job_type', 'employment_type',
            'department', 'region', 'health_condition', 'chronic_condition', 'prescription_use',
            'income_tier', 'risk_tolerance', 'lifestyle', 'travel_frequency', 'hsa_familiarity',
            'pregnancy_status', 'tobacco_use', 'mental_health_needs'
        ]
        
        # Default value mappings for enrichment
        self.default_mappings = {
            'job_type': {
                'default': 'Desk',
                'rules': {
                    'Engineering': 'Desk',
                    'Manufacturing': 'Field',
                    'Sales': 'Field',
                    'Information Technology': 'Desk',
                    'Finance': 'Desk'
                }
            },
            'region': {
                'default': 'Urban',
                'state_mapping': {
                    'CA': 'Urban', 'NY': 'Urban', 'TX': 'Urban', 'FL': 'Urban',
                    'WA': 'Urban', 'IL': 'Urban', 'MA': 'Urban', 'NJ': 'Urban',
                    'MT': 'Rural', 'WY': 'Rural', 'ND': 'Rural', 'SD': 'Rural',
                    'VT': 'Rural', 'AK': 'Rural', 'ID': 'Rural'
                }
            },
            'health_condition': {
                'default': 'Good',
                'age_based': {
                    (18, 30): ['Excellent', 'Good', 'Good', 'Fair'],  # Weighted towards better health
                    (31, 45): ['Good', 'Good', 'Fair', 'Excellent'],
                    (46, 60): ['Good', 'Fair', 'Fair', 'Good'],
                    (61, 100): ['Fair', 'Fair', 'Good', 'Poor']
                }
            },
            'chronic_condition': {
                'default': 'N',
                'age_probability': {
                    (18, 30): 0.05,  # 5% chance
                    (31, 45): 0.15,  # 15% chance
                    (46, 60): 0.30,  # 30% chance
                    (61, 100): 0.50  # 50% chance
                }
            },
            'prescription_use': {
                'default': 'None',
                'chronic_based': {
                    'Y': ['Regular', 'Occasional', 'Regular'],  # Higher chance if chronic condition
                    'N': ['None', 'None', 'Occasional', 'None']
                }
            },
            'income_tier': {
                'default': 'Medium ($50K–$100K)',
                'salary_mapping': {
                    (0, 40000): 'Low (<$50K)',
                    (40000, 75000): 'Medium ($50K–$100K)',
                    (75000, float('inf')): 'High (>$100K)'
                }
            },
            'risk_tolerance': {
                'default': 'Medium',
                'age_income_based': {
                    'young_high': ['High', 'Medium', 'High'],  # Young + High income
                    'young_low': ['Medium', 'Low', 'Medium'],   # Young + Low income
                    'old_high': ['Medium', 'Low', 'Medium'],    # Old + High income
                    'old_low': ['Low', 'Low', 'Medium']         # Old + Low income
                }
            },
            'lifestyle': {
                'default': 'Moderate',
                'age_based': {
                    (18, 35): ['Active', 'Active', 'Moderate'],
                    (36, 55): ['Moderate', 'Active', 'Moderate'],
                    (56, 100): ['Moderate', 'Sedentary', 'Moderate']
                }
            },
            'travel_frequency': {
                'default': 'Rare',
                'job_department_based': {
                    'Sales': ['Frequent', 'Occasional', 'Frequent'],
                    'Information Technology': ['Rare', 'Occasional', 'Rare'],
                    'Engineering': ['Occasional', 'Rare', 'Occasional'],
                    'Finance': ['Rare', 'Occasional', 'Rare'],
                    'Manufacturing': ['Rare', 'Rare', 'Occasional']
                }
            },
            'hsa_familiarity': {
                'default': 'N',
                'income_education_based': {
                    'high_income': 0.7,  # 70% familiar if high income
                    'medium_income': 0.4,  # 40% familiar if medium income
                    'low_income': 0.2     # 20% familiar if low income
                }
            },
            'pregnancy_status': {
                'default': 'N',
                'gender_age_based': {
                    'female_childbearing': 0.15,  # 15% chance for women 20-40
                    'other': 0.0
                }
            },
            'tobacco_use': {
                'default': 'N',
                'general_probability': 0.15  # 15% general population rate
            },
            'mental_health_needs': {
                'default': 'N',
                'age_stress_based': {
                    'young_professional': 0.25,  # Higher stress in young professionals
                    'middle_age': 0.20,
                    'older': 0.15
                }
            }
        }
    
    def enrich_dataframe(self, df: pd.DataFrame) -> tuple[pd.DataFrame, Dict[str, Any]]:
        """
        Enrich the entire dataframe with missing fields required for ML models.

        Args:
            df: Preprocessed census dataframe

        Returns:
            Tuple of (enriched_dataframe, comprehensive_statistics)
        """
        logger.info(f"Starting data enrichment for {len(df)} employees")

        # Create a copy to avoid modifying original
        enriched_df = df.copy()

        # Track enrichment statistics
        enrichment_stats = {
            'total_employees': len(df),
            'fields_enriched': {},
            'enrichment_methods': {}
        }

        # Step 1: Ensure employee_id exists for all records
        enriched_df = self._ensure_employee_id(enriched_df)

        # Step 2: Enrich location data from ZIP codes (must be done first)
        enriched_df = self._enrich_location_from_zipcode(enriched_df)

        # Step 3: Enrich each required feature
        for feature in self.required_features:
            if feature not in enriched_df.columns or enriched_df[feature].isna().any():
                logger.info(f"Enriching feature: {feature}")
                enriched_df, stats = self._enrich_feature(enriched_df, feature)
                enrichment_stats['fields_enriched'][feature] = stats

        # Generate comprehensive statistics
        comprehensive_stats = self.generate_comprehensive_statistics(enriched_df)

        # Log enrichment summary
        logger.info(f"Data enrichment completed. Enriched {len(enrichment_stats['fields_enriched'])} features")

        return enriched_df, comprehensive_stats

    def _enrich_location_from_zipcode(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Enrich location data (city, state, region) from ZIP codes.
        This is done first as it provides foundational location data for other enrichments.

        Args:
            df: DataFrame with zipcode column

        Returns:
            DataFrame with enriched location data
        """
        logger.info("Enriching location data from ZIP codes")

        if 'zipcode' not in df.columns:
            logger.warning("No zipcode column found - cannot enrich location data")
            return df

        enriched_df = df.copy()

        # Initialize location columns if they don't exist
        for col in ['city', 'state', 'region']:
            if col not in enriched_df.columns:
                enriched_df[col] = None

        # Track enrichment statistics
        enriched_cities = 0
        enriched_states = 0
        enriched_regions = 0
        api_calls = 0

        # Process each row with zipcode
        for idx, row in enriched_df.iterrows():
            zipcode = row.get('zipcode')

            if pd.isna(zipcode) or not zipcode:
                continue

            # Get location data from ZIP code
            location_data = self.zip_service.get_location_from_zipcode(str(zipcode))
            api_calls += 1

            # Enrich city if missing
            if pd.isna(row.get('city')) and location_data.get('city'):
                enriched_df.at[idx, 'city'] = location_data['city']
                enriched_cities += 1

            # Enrich state if missing
            if pd.isna(row.get('state')) and location_data.get('state'):
                enriched_df.at[idx, 'state'] = location_data['state']
                enriched_states += 1

            # Enrich region (always update for consistency)
            if location_data.get('region'):
                enriched_df.at[idx, 'region'] = location_data['region']
                enriched_regions += 1

        logger.info(f"Location enrichment completed: {enriched_cities} cities, {enriched_states} states, {enriched_regions} regions enriched from {api_calls} ZIP lookups")

        return enriched_df

    def _enrich_feature(self, df: pd.DataFrame, feature: str) -> tuple[pd.DataFrame, Dict]:
        """
        Enrich a specific feature using intelligent rules and heuristics.
        
        Args:
            df: Dataframe to enrich
            feature: Feature name to enrich
            
        Returns:
            Tuple of (enriched_dataframe, enrichment_statistics)
        """
        
        # Initialize feature column if it doesn't exist
        if feature not in df.columns:
            df[feature] = None

        # Ensure proper data type for the column
        if feature in ['age']:
            df[feature] = pd.to_numeric(df[feature], errors='coerce')
        else:
            df[feature] = df[feature].astype('object')
        
        # Count missing values before enrichment
        missing_before = df[feature].isna().sum()
        
        # Apply feature-specific enrichment logic
        if feature == 'age':
            df = self._enrich_age(df)
        elif feature == 'gender':
            df = self._enrich_gender(df)
        elif feature == 'marital_status':
            df = self._enrich_marital_status(df)
        elif feature == 'dept_count':
            df = self._enrich_dept_count(df)
        elif feature == 'job_type':
            df = self._enrich_job_type(df)
        elif feature == 'employment_type':
            df = self._enrich_employment_type(df)
        elif feature == 'department':
            df = self._enrich_department(df)
        elif feature == 'region':
            df = self._enrich_region(df)
        elif feature == 'health_condition':
            df = self._enrich_health_condition(df)
        elif feature == 'chronic_condition':
            df = self._enrich_chronic_condition(df)
        elif feature == 'prescription_use':
            df = self._enrich_prescription_use(df)
        elif feature == 'income_tier':
            df = self._enrich_income_tier(df)
        elif feature == 'risk_tolerance':
            df = self._enrich_risk_tolerance(df)
        elif feature == 'lifestyle':
            df = self._enrich_lifestyle(df)
        elif feature == 'travel_frequency':
            df = self._enrich_travel_frequency(df)
        elif feature == 'hsa_familiarity':
            df = self._enrich_hsa_familiarity(df)
        elif feature == 'pregnancy_status':
            df = self._enrich_pregnancy_status(df)
        elif feature == 'tobacco_use':
            df = self._enrich_tobacco_use(df)
        elif feature == 'mental_health_needs':
            df = self._enrich_mental_health_needs(df)
        
        # Count missing values after enrichment
        missing_after = df[feature].isna().sum()
        
        # Return statistics
        stats = {
            'missing_before': missing_before,
            'missing_after': missing_after,
            'enriched_count': missing_before - missing_after,
            'enrichment_rate': ((missing_before - missing_after) / missing_before * 100) if missing_before > 0 else 0
        }
        
        return df, stats

    def _ensure_employee_id(self, df: pd.DataFrame) -> pd.DataFrame:
        """Ensure all employee records have a unique employee_id with consistent format."""
        logger.info("Ensuring employee_id exists for all records with consistent format")

        # Check if employee_id column exists and has missing values
        if 'employee_id' not in df.columns:
            df['employee_id'] = None
            logger.info("Added missing employee_id column")

        # Find rows with missing employee_id
        missing_mask = df['employee_id'].isna() | (df['employee_id'] == '') | (df['employee_id'] == 'None')
        missing_count = missing_mask.sum()

        if missing_count > 0:
            logger.info(f"Generating employee_id for {missing_count} records")

            # Find the highest existing employee number to continue sequence
            existing_ids = df[~missing_mask]['employee_id'].astype(str)
            max_num = 0

            for emp_id in existing_ids:
                # Extract number from existing IDs (handle E001, E1, EMP_0001, etc.)
                if emp_id.startswith('E') and len(emp_id) > 1:
                    # Extract numeric part after 'E'
                    numeric_part = emp_id[1:]
                    if numeric_part.isdigit():
                        max_num = max(max_num, int(numeric_part))
                elif emp_id.startswith('EMP_') and len(emp_id) > 4:
                    # Handle EMP_XXXX format
                    numeric_part = emp_id[4:]
                    if numeric_part.isdigit():
                        max_num = max(max_num, int(numeric_part))

            logger.debug(f"Highest existing employee number: {max_num}")

            # Generate IDs continuing the sequence in E### format
            current_num = max_num + 1
            for idx in df[missing_mask].index:
                generated_id = f"E{current_num:03d}"  # E002, E003, E004, etc.
                df.loc[idx, 'employee_id'] = generated_id
                logger.debug(f"Generated employee_id: {generated_id} for row {idx}")
                current_num += 1

            logger.info(f"Successfully generated {missing_count} employee IDs in E### format")
        else:
            logger.info("All records already have employee_id")

        return df

    def _enrich_age(self, df: pd.DataFrame) -> pd.DataFrame:
        """Enrich age field using DOB if available."""
        mask = df['age'].isna()
        if mask.any() and 'dob' in df.columns:
            # Calculate age from DOB
            current_year = datetime.now().year
            df.loc[mask, 'age'] = df.loc[mask, 'dob'].apply(
                lambda x: current_year - pd.to_datetime(x).year if pd.notna(x) else np.random.randint(25, 65)
            )
        elif mask.any():
            # Random age between 25-65 if no DOB
            df.loc[mask, 'age'] = np.random.randint(25, 65, size=mask.sum())
        return df

    def _enrich_gender(self, df: pd.DataFrame) -> pd.DataFrame:
        """Enrich gender field with random assignment."""
        mask = df['gender'].isna()
        if mask.any():
            df.loc[mask, 'gender'] = np.random.choice(['Male', 'Female'], size=mask.sum())
        return df

    def _enrich_marital_status(self, df: pd.DataFrame) -> pd.DataFrame:
        """Enrich marital status based on age and dependents."""
        mask = df['marital_status'].isna()
        if mask.any():
            for idx in df[mask].index:
                age = df.loc[idx, 'age'] if 'age' in df.columns else 35
                dept_count = df.loc[idx, 'dept_count'] if 'dept_count' in df.columns else 0

                # Higher probability of married if older or has dependents
                if age > 30 or (dept_count and int(dept_count) > 0):
                    df.loc[idx, 'marital_status'] = np.random.choice(['Married', 'Not Married'], p=[0.7, 0.3])
                else:
                    df.loc[idx, 'marital_status'] = np.random.choice(['Married', 'Not Married'], p=[0.3, 0.7])
        return df

    def _enrich_dept_count(self, df: pd.DataFrame) -> pd.DataFrame:
        """Enrich dependent count based on marital status and age."""
        mask = df['dept_count'].isna()
        if mask.any():
            for idx in df[mask].index:
                marital = df.loc[idx, 'marital_status'] if 'marital_status' in df.columns else 'Not Married'
                age = df.loc[idx, 'age'] if 'age' in df.columns else 35

                if marital == 'Married' and age > 25:
                    # Married people more likely to have dependents
                    df.loc[idx, 'dept_count'] = np.random.choice(['0', '1', '2', '3+'], p=[0.2, 0.3, 0.4, 0.1])
                else:
                    # Single people less likely to have dependents
                    df.loc[idx, 'dept_count'] = np.random.choice(['0', '1', '2', '3+'], p=[0.7, 0.2, 0.08, 0.02])
        return df

    def _enrich_job_type(self, df: pd.DataFrame) -> pd.DataFrame:
        """Enrich job type based on department."""
        mask = df['job_type'].isna()
        if mask.any():
            for idx in df[mask].index:
                department = df.loc[idx, 'department'] if 'department' in df.columns else None

                if department and department in self.default_mappings['job_type']['rules']:
                    df.loc[idx, 'job_type'] = self.default_mappings['job_type']['rules'][department]
                else:
                    df.loc[idx, 'job_type'] = self.default_mappings['job_type']['default']
        return df

    def _enrich_employment_type(self, df: pd.DataFrame) -> pd.DataFrame:
        """Enrich employment type with weighted random assignment."""
        mask = df['employment_type'].isna()
        if mask.any():
            # Most employees are full-time
            df.loc[mask, 'employment_type'] = np.random.choice(
                ['Full-Time', 'Part-Time', 'Contract'],
                size=mask.sum(),
                p=[0.8, 0.15, 0.05]
            )
        return df

    def _enrich_department(self, df: pd.DataFrame) -> pd.DataFrame:
        """Enrich department with common department names."""
        mask = df['department'].isna()
        if mask.any():
            departments = ['Information Technology', 'Sales', 'Finance', 'Engineering', 'Manufacturing']
            df.loc[mask, 'department'] = np.random.choice(departments, size=mask.sum())
        return df

    def _enrich_region(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Enrich region based on ZIP code, state, or fallback logic.
        Note: This method is called after _enrich_location_from_zipcode, so most regions should already be set.
        """
        mask = df['region'].isna()
        if mask.any():
            for idx in df[mask].index:
                # Try ZIP code first (most accurate)
                zipcode = df.loc[idx, 'zipcode'] if 'zipcode' in df.columns else None
                if zipcode:
                    location_data = self.zip_service.get_location_from_zipcode(str(zipcode))
                    if location_data.get('region'):
                        df.loc[idx, 'region'] = location_data['region']
                        continue

                # Fallback to state mapping
                state = df.loc[idx, 'state'] if 'state' in df.columns else None
                if state and state in self.default_mappings['region']['state_mapping']:
                    df.loc[idx, 'region'] = self.default_mappings['region']['state_mapping'][state]
                else:
                    # Final fallback - weighted random
                    df.loc[idx, 'region'] = np.random.choice(['Urban', 'Suburban', 'Rural'], p=[0.5, 0.3, 0.2])
        return df

    def _enrich_health_condition(self, df: pd.DataFrame) -> pd.DataFrame:
        """Enrich health condition based on age."""
        mask = df['health_condition'].isna()
        if mask.any():
            for idx in df[mask].index:
                age = df.loc[idx, 'age'] if 'age' in df.columns else 35

                # Find age bracket
                for age_range, options in self.default_mappings['health_condition']['age_based'].items():
                    if age_range[0] <= age <= age_range[1]:
                        df.loc[idx, 'health_condition'] = np.random.choice(options)
                        break
                else:
                    df.loc[idx, 'health_condition'] = self.default_mappings['health_condition']['default']
        return df

    def _enrich_chronic_condition(self, df: pd.DataFrame) -> pd.DataFrame:
        """Enrich chronic condition based on age probability."""
        mask = df['chronic_condition'].isna()
        if mask.any():
            for idx in df[mask].index:
                age = df.loc[idx, 'age'] if 'age' in df.columns else 35

                # Find age bracket and probability
                probability = 0.15  # default
                for age_range, prob in self.default_mappings['chronic_condition']['age_probability'].items():
                    if age_range[0] <= age <= age_range[1]:
                        probability = prob
                        break

                df.loc[idx, 'chronic_condition'] = 'Y' if np.random.random() < probability else 'N'
        return df

    def _enrich_prescription_use(self, df: pd.DataFrame) -> pd.DataFrame:
        """Enrich prescription use based on chronic condition."""
        mask = df['prescription_use'].isna()
        if mask.any():
            for idx in df[mask].index:
                chronic = df.loc[idx, 'chronic_condition'] if 'chronic_condition' in df.columns else 'N'

                if chronic in self.default_mappings['prescription_use']['chronic_based']:
                    options = self.default_mappings['prescription_use']['chronic_based'][chronic]
                    df.loc[idx, 'prescription_use'] = np.random.choice(options)
                else:
                    df.loc[idx, 'prescription_use'] = self.default_mappings['prescription_use']['default']
        return df

    def _enrich_income_tier(self, df: pd.DataFrame) -> pd.DataFrame:
        """Enrich income tier based on salary if available."""
        mask = df['income_tier'].isna()
        if mask.any():
            for idx in df[mask].index:
                salary = df.loc[idx, 'salary'] if 'salary' in df.columns else None

                if salary and pd.notna(salary):
                    try:
                        salary_num = float(salary)
                        for salary_range, tier in self.default_mappings['income_tier']['salary_mapping'].items():
                            if salary_range[0] <= salary_num < salary_range[1]:
                                df.loc[idx, 'income_tier'] = tier
                                break
                    except (ValueError, TypeError):
                        df.loc[idx, 'income_tier'] = self.default_mappings['income_tier']['default']
                else:
                    # Random assignment if no salary
                    df.loc[idx, 'income_tier'] = np.random.choice(
                        ['Low (<$50K)', 'Medium ($50K–$100K)', 'High (>$100K)'],
                        p=[0.3, 0.5, 0.2]
                    )
        return df

    def _enrich_risk_tolerance(self, df: pd.DataFrame) -> pd.DataFrame:
        """Enrich risk tolerance based on age and income."""
        mask = df['risk_tolerance'].isna()
        if mask.any():
            for idx in df[mask].index:
                age = df.loc[idx, 'age'] if 'age' in df.columns else 35
                income = df.loc[idx, 'income_tier'] if 'income_tier' in df.columns else 'Medium ($50K–$100K)'

                # Determine category
                is_young = age < 40
                is_high_income = 'High' in income

                if is_young and is_high_income:
                    category = 'young_high'
                elif is_young and not is_high_income:
                    category = 'young_low'
                elif not is_young and is_high_income:
                    category = 'old_high'
                else:
                    category = 'old_low'

                options = self.default_mappings['risk_tolerance']['age_income_based'][category]
                df.loc[idx, 'risk_tolerance'] = np.random.choice(options)
        return df

    def _enrich_lifestyle(self, df: pd.DataFrame) -> pd.DataFrame:
        """Enrich lifestyle based on age."""
        mask = df['lifestyle'].isna()
        if mask.any():
            for idx in df[mask].index:
                age = df.loc[idx, 'age'] if 'age' in df.columns else 35

                # Find age bracket
                for age_range, options in self.default_mappings['lifestyle']['age_based'].items():
                    if age_range[0] <= age <= age_range[1]:
                        df.loc[idx, 'lifestyle'] = np.random.choice(options)
                        break
                else:
                    df.loc[idx, 'lifestyle'] = self.default_mappings['lifestyle']['default']
        return df

    def _enrich_travel_frequency(self, df: pd.DataFrame) -> pd.DataFrame:
        """Enrich travel frequency based on department."""
        mask = df['travel_frequency'].isna()
        if mask.any():
            for idx in df[mask].index:
                department = df.loc[idx, 'department'] if 'department' in df.columns else None

                if department and department in self.default_mappings['travel_frequency']['job_department_based']:
                    options = self.default_mappings['travel_frequency']['job_department_based'][department]
                    df.loc[idx, 'travel_frequency'] = np.random.choice(options)
                else:
                    df.loc[idx, 'travel_frequency'] = np.random.choice(['Rare', 'Occasional', 'Frequent'], p=[0.6, 0.3, 0.1])
        return df

    def _enrich_hsa_familiarity(self, df: pd.DataFrame) -> pd.DataFrame:
        """Enrich HSA familiarity based on income tier."""
        mask = df['hsa_familiarity'].isna()
        if mask.any():
            for idx in df[mask].index:
                income = df.loc[idx, 'income_tier'] if 'income_tier' in df.columns else 'Medium ($50K–$100K)'

                if 'High' in income:
                    probability = self.default_mappings['hsa_familiarity']['income_education_based']['high_income']
                elif 'Medium' in income:
                    probability = self.default_mappings['hsa_familiarity']['income_education_based']['medium_income']
                else:
                    probability = self.default_mappings['hsa_familiarity']['income_education_based']['low_income']

                df.loc[idx, 'hsa_familiarity'] = 'Y' if np.random.random() < probability else 'N'
        return df

    def _enrich_pregnancy_status(self, df: pd.DataFrame) -> pd.DataFrame:
        """Enrich pregnancy status based on gender and age."""
        mask = df['pregnancy_status'].isna()
        if mask.any():
            for idx in df[mask].index:
                gender = df.loc[idx, 'gender'] if 'gender' in df.columns else 'Male'
                age = df.loc[idx, 'age'] if 'age' in df.columns else 35

                if gender == 'Female' and 20 <= age <= 40:
                    probability = self.default_mappings['pregnancy_status']['gender_age_based']['female_childbearing']
                else:
                    probability = self.default_mappings['pregnancy_status']['gender_age_based']['other']

                df.loc[idx, 'pregnancy_status'] = 'Y' if np.random.random() < probability else 'N'
        return df

    def _enrich_tobacco_use(self, df: pd.DataFrame) -> pd.DataFrame:
        """Enrich tobacco use with general population probability."""
        mask = df['tobacco_use'].isna()
        if mask.any():
            probability = self.default_mappings['tobacco_use']['general_probability']
            for idx in df[mask].index:
                df.loc[idx, 'tobacco_use'] = 'Y' if np.random.random() < probability else 'N'
        return df

    def _enrich_mental_health_needs(self, df: pd.DataFrame) -> pd.DataFrame:
        """Enrich mental health needs based on age and stress factors."""
        mask = df['mental_health_needs'].isna()
        if mask.any():
            for idx in df[mask].index:
                age = df.loc[idx, 'age'] if 'age' in df.columns else 35
                department = df.loc[idx, 'department'] if 'department' in df.columns else None

                # Determine stress category
                if age < 35 and department in ['Information Technology', 'Sales']:
                    category = 'young_professional'
                elif 35 <= age <= 55:
                    category = 'middle_age'
                else:
                    category = 'older'

                probability = self.default_mappings['mental_health_needs']['age_stress_based'][category]
                df.loc[idx, 'mental_health_needs'] = 'Y' if np.random.random() < probability else 'N'
        return df

    def get_enrichment_summary(self, original_df: pd.DataFrame, enriched_df: pd.DataFrame) -> Dict[str, Any]:
        """
        Generate a summary of the enrichment process.

        Args:
            original_df: Original dataframe before enrichment
            enriched_df: Dataframe after enrichment

        Returns:
            Dictionary containing enrichment summary statistics
        """
        summary = {
            'total_employees': len(original_df),
            'features_analyzed': len(self.required_features),
            'enrichment_details': {},
            'data_quality_improvement': {}
        }

        for feature in self.required_features:
            original_missing = original_df[feature].isna().sum() if feature in original_df.columns else len(original_df)
            final_missing = enriched_df[feature].isna().sum() if feature in enriched_df.columns else len(enriched_df)

            summary['enrichment_details'][feature] = {
                'original_missing': original_missing,
                'final_missing': final_missing,
                'enriched_count': original_missing - final_missing,
                'completion_rate': ((len(original_df) - final_missing) / len(original_df) * 100) if len(original_df) > 0 else 0
            }

        # Overall data quality metrics
        total_original_missing = sum(detail['original_missing'] for detail in summary['enrichment_details'].values())
        total_final_missing = sum(detail['final_missing'] for detail in summary['enrichment_details'].values())

        summary['data_quality_improvement'] = {
            'total_missing_before': total_original_missing,
            'total_missing_after': total_final_missing,
            'total_enriched': total_original_missing - total_final_missing,
            'overall_completion_rate': ((len(self.required_features) * len(original_df) - total_final_missing) /
                                      (len(self.required_features) * len(original_df)) * 100) if len(original_df) > 0 else 0
        }

        return summary

    def generate_comprehensive_statistics(self, df: pd.DataFrame) -> Dict[str, Any]:
        """
        Generate comprehensive statistics for the enriched census data.

        Args:
            df: Enriched census dataframe

        Returns:
            Dictionary containing comprehensive statistics and demographics
        """
        logger.info("Generating comprehensive statistics for census data")

        stats = {
            'basic_demographics': self._calculate_basic_demographics(df),
            'dependent_analysis': self._analyze_dependents(df),
            'employment_demographics': self._analyze_employment_demographics(df),
            'health_and_lifestyle': self._analyze_health_lifestyle(df),
            'coverage_analysis': self._analyze_coverage_tiers(df),
            'geographic_distribution': self._analyze_geographic_distribution(df),
            'financial_demographics': self._analyze_financial_demographics(df),
            'risk_assessment': self._calculate_group_risk_assessment(df),
            'data_quality_metrics': self._calculate_data_quality_metrics(df)
        }

        return stats

    def _calculate_basic_demographics(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Calculate basic demographic statistics."""
        demographics = {}

        # Age statistics
        if 'age' in df.columns:
            demographics['age_statistics'] = {
                'average_age': float(df['age'].mean()),
                'median_age': float(df['age'].median()),
                'age_range': {
                    'min': int(df['age'].min()),
                    'max': int(df['age'].max())
                },
                'age_distribution': {
                    '18-30': len(df[(df['age'] >= 18) & (df['age'] <= 30)]),
                    '31-45': len(df[(df['age'] >= 31) & (df['age'] <= 45)]),
                    '46-60': len(df[(df['age'] >= 46) & (df['age'] <= 60)]),
                    '60+': len(df[df['age'] > 60])
                }
            }

        # Gender composition
        if 'gender' in df.columns:
            gender_counts = df['gender'].value_counts().to_dict()
            total = len(df)
            demographics['gender_composition'] = {
                'counts': gender_counts,
                'percentages': {k: round(v/total*100, 2) for k, v in gender_counts.items()}
            }

        # Marital status
        if 'marital_status' in df.columns:
            marital_counts = df['marital_status'].value_counts().to_dict()
            demographics['marital_status_distribution'] = {
                'counts': marital_counts,
                'percentages': {k: round(v/len(df)*100, 2) for k, v in marital_counts.items()}
            }

        return demographics

    def _analyze_dependents(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Analyze dependent demographics and statistics."""
        dependent_stats = {}

        # Basic dependent count analysis
        if 'dept_count' in df.columns:
            # Convert dept_count to numeric for analysis
            dept_counts = pd.to_numeric(df['dept_count'].replace({'3+': '3'}), errors='coerce')
            dependent_stats['dependent_count_distribution'] = {
                'average_dependents': float(dept_counts.mean()),
                'median_dependents': float(dept_counts.median()),
                'distribution': df['dept_count'].value_counts().to_dict(),
                'employees_with_dependents': len(df[dept_counts > 0]),
                'percentage_with_dependents': round(len(df[dept_counts > 0]) / len(df) * 100, 2)
            }

        # Analyze individual dependents
        dependent_ages = []
        dependents_over_26 = 0
        total_dependents = 0

        # Check for dependent columns (dept_1, dept_2, etc.)
        for i in range(1, 6):  # Check up to 5 dependents
            age_col = f'dept_{i}_age'
            relationship_col = f'relationship_type_{i}'

            if age_col in df.columns:
                valid_ages = pd.to_numeric(df[age_col], errors='coerce').dropna()
                dependent_ages.extend(valid_ages.tolist())
                total_dependents += len(valid_ages)

                # Count dependents over 26 who are children
                if relationship_col in df.columns:
                    # Ensure relationship column is string type before using .str accessor
                    relationship_series = df[relationship_col].astype(str)
                    child_over_26 = df[
                        (pd.to_numeric(df[age_col], errors='coerce') > 26) &
                        (relationship_series.str.contains('Child', case=False, na=False))
                    ]
                    dependents_over_26 += len(child_over_26)

        if dependent_ages:
            dependent_stats['dependent_age_analysis'] = {
                'total_dependents_found': total_dependents,
                'average_dependent_age': round(np.mean(dependent_ages), 2),
                'median_dependent_age': round(np.median(dependent_ages), 2),
                'dependents_over_26_as_children': dependents_over_26,
                'age_distribution': {
                    '0-5': len([age for age in dependent_ages if 0 <= age <= 5]),
                    '6-12': len([age for age in dependent_ages if 6 <= age <= 12]),
                    '13-18': len([age for age in dependent_ages if 13 <= age <= 18]),
                    '19-26': len([age for age in dependent_ages if 19 <= age <= 26]),
                    '26+': len([age for age in dependent_ages if age > 26])
                }
            }

        return dependent_stats

    def _analyze_employment_demographics(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Analyze employment-related demographics."""
        employment_stats = {}

        # Department distribution
        if 'department' in df.columns:
            dept_counts = df['department'].value_counts().to_dict()
            employment_stats['department_distribution'] = {
                'counts': dept_counts,
                'percentages': {k: round(v/len(df)*100, 2) for k, v in dept_counts.items()}
            }

        # Employment type distribution
        if 'employment_type' in df.columns:
            emp_type_counts = df['employment_type'].value_counts().to_dict()
            employment_stats['employment_type_distribution'] = {
                'counts': emp_type_counts,
                'percentages': {k: round(v/len(df)*100, 2) for k, v in emp_type_counts.items()}
            }

        # Job type distribution
        if 'job_type' in df.columns:
            job_type_counts = df['job_type'].value_counts().to_dict()
            employment_stats['job_type_distribution'] = {
                'counts': job_type_counts,
                'percentages': {k: round(v/len(df)*100, 2) for k, v in job_type_counts.items()}
            }

        # Employee class distribution (if available)
        if 'employee_class' in df.columns:
            class_counts = df['employee_class'].value_counts().to_dict()
            employment_stats['employee_class_distribution'] = {
                'counts': class_counts,
                'percentages': {k: round(v/len(df)*100, 2) for k, v in class_counts.items()}
            }

        # Salary analysis (if available)
        if 'salary' in df.columns:
            salary_numeric = pd.to_numeric(df['salary'], errors='coerce').dropna()
            if len(salary_numeric) > 0:
                employment_stats['salary_analysis'] = {
                    'average_salary': round(salary_numeric.mean(), 2),
                    'median_salary': round(salary_numeric.median(), 2),
                    'salary_range': {
                        'min': round(salary_numeric.min(), 2),
                        'max': round(salary_numeric.max(), 2)
                    },
                    'salary_distribution': {
                        'under_40k': len(salary_numeric[salary_numeric < 40000]),
                        '40k_75k': len(salary_numeric[(salary_numeric >= 40000) & (salary_numeric < 75000)]),
                        '75k_100k': len(salary_numeric[(salary_numeric >= 75000) & (salary_numeric < 100000)]),
                        'over_100k': len(salary_numeric[salary_numeric >= 100000])
                    }
                }

        return employment_stats

    def _analyze_health_lifestyle(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Analyze health and lifestyle demographics."""
        health_stats = {}

        # Health condition distribution
        if 'health_condition' in df.columns:
            health_counts = df['health_condition'].value_counts().to_dict()
            health_stats['health_condition_distribution'] = {
                'counts': health_counts,
                'percentages': {k: round(v/len(df)*100, 2) for k, v in health_counts.items()}
            }

        # Chronic condition analysis
        if 'chronic_condition' in df.columns:
            chronic_counts = df['chronic_condition'].value_counts().to_dict()
            health_stats['chronic_condition_distribution'] = {
                'counts': chronic_counts,
                'percentages': {k: round(v/len(df)*100, 2) for k, v in chronic_counts.items()}
            }

        # Tobacco use analysis
        if 'tobacco_use' in df.columns:
            tobacco_counts = df['tobacco_use'].value_counts().to_dict()
            health_stats['tobacco_use_distribution'] = {
                'counts': tobacco_counts,
                'percentages': {k: round(v/len(df)*100, 2) for k, v in tobacco_counts.items()}
            }

        # Lifestyle distribution
        if 'lifestyle' in df.columns:
            lifestyle_counts = df['lifestyle'].value_counts().to_dict()
            health_stats['lifestyle_distribution'] = {
                'counts': lifestyle_counts,
                'percentages': {k: round(v/len(df)*100, 2) for k, v in lifestyle_counts.items()}
            }

        # Prescription use analysis
        if 'prescription_use' in df.columns:
            prescription_counts = df['prescription_use'].value_counts().to_dict()
            health_stats['prescription_use_distribution'] = {
                'counts': prescription_counts,
                'percentages': {k: round(v/len(df)*100, 2) for k, v in prescription_counts.items()}
            }

        return health_stats

    def _analyze_coverage_tiers(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Analyze coverage tier distribution if present."""
        coverage_stats = {}

        if 'coverage_tier' in df.columns:
            tier_counts = df['coverage_tier'].value_counts().to_dict()
            coverage_stats['coverage_tier_distribution'] = {
                'counts': tier_counts,
                'percentages': {k: round(v/len(df)*100, 2) for k, v in tier_counts.items()}
            }

            # Analyze coverage tier by family size
            if 'dept_count' in df.columns:
                tier_by_family = df.groupby('dept_count')['coverage_tier'].value_counts().unstack(fill_value=0)
                coverage_stats['coverage_tier_by_family_size'] = tier_by_family.to_dict()

        # Analyze existing plan selections if available
        plan_columns = ['medical_plan', 'dental_plan', 'vision_plan', 'life_plan']
        for plan_col in plan_columns:
            if plan_col in df.columns:
                plan_counts = df[plan_col].value_counts().to_dict()
                coverage_stats[f'{plan_col}_distribution'] = {
                    'counts': plan_counts,
                    'percentages': {k: round(v/len(df)*100, 2) for k, v in plan_counts.items()}
                }

        return coverage_stats

    def _analyze_geographic_distribution(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Analyze geographic distribution."""
        geo_stats = {}

        # State distribution
        if 'state' in df.columns:
            state_counts = df['state'].value_counts().to_dict()
            geo_stats['state_distribution'] = {
                'counts': state_counts,
                'percentages': {k: round(v/len(df)*100, 2) for k, v in state_counts.items()}
            }

        # Region distribution (if enriched)
        if 'region' in df.columns:
            region_counts = df['region'].value_counts().to_dict()
            geo_stats['region_distribution'] = {
                'counts': region_counts,
                'percentages': {k: round(v/len(df)*100, 2) for k, v in region_counts.items()}
            }

        # City distribution (top 10)
        if 'city' in df.columns:
            city_counts = df['city'].value_counts().head(10).to_dict()
            geo_stats['top_cities'] = {
                'counts': city_counts,
                'percentages': {k: round(v/len(df)*100, 2) for k, v in city_counts.items()}
            }

        return geo_stats

    def _analyze_financial_demographics(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Analyze financial demographics."""
        financial_stats = {}

        # Income tier distribution (if enriched)
        if 'income_tier' in df.columns:
            income_counts = df['income_tier'].value_counts().to_dict()
            financial_stats['income_tier_distribution'] = {
                'counts': income_counts,
                'percentages': {k: round(v/len(df)*100, 2) for k, v in income_counts.items()}
            }

        # Risk tolerance distribution
        if 'risk_tolerance' in df.columns:
            risk_counts = df['risk_tolerance'].value_counts().to_dict()
            financial_stats['risk_tolerance_distribution'] = {
                'counts': risk_counts,
                'percentages': {k: round(v/len(df)*100, 2) for k, v in risk_counts.items()}
            }

        # HSA familiarity
        if 'hsa_familiarity' in df.columns:
            hsa_counts = df['hsa_familiarity'].value_counts().to_dict()
            financial_stats['hsa_familiarity_distribution'] = {
                'counts': hsa_counts,
                'percentages': {k: round(v/len(df)*100, 2) for k, v in hsa_counts.items()}
            }

        return financial_stats

    def _calculate_data_quality_metrics(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Calculate data quality metrics."""
        quality_metrics = {}

        # Overall completeness
        total_cells = len(df) * len(df.columns)
        missing_cells = df.isnull().sum().sum()
        quality_metrics['overall_completeness'] = {
            'total_cells': total_cells,
            'missing_cells': missing_cells,
            'completeness_percentage': round((total_cells - missing_cells) / total_cells * 100, 2)
        }

        # Column-wise completeness
        column_completeness = {}
        for col in df.columns:
            missing_count = df[col].isnull().sum()
            column_completeness[col] = {
                'missing_count': missing_count,
                'completeness_percentage': round((len(df) - missing_count) / len(df) * 100, 2)
            }
        quality_metrics['column_completeness'] = column_completeness

        # Critical field completeness (mandatory fields)
        critical_fields = ['name', 'gender', 'zipcode', 'marital_status']
        critical_completeness = {}
        for field in critical_fields:
            if field in df.columns:
                missing_count = df[field].isnull().sum()
                critical_completeness[field] = {
                    'missing_count': missing_count,
                    'completeness_percentage': round((len(df) - missing_count) / len(df) * 100, 2)
                }
        quality_metrics['critical_field_completeness'] = critical_completeness

        return quality_metrics

    def _calculate_group_risk_assessment(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Calculate group risk assessment based on individual risk factors."""
        risk_assessment = {}

        # Individual risk scores (0-100 scale)
        individual_risks = []

        for idx, row in df.iterrows():
            risk_score = 0
            risk_factors = []

            # Age risk (0-30 points)
            age = row.get('age', 35)
            if age < 30:
                age_risk = 5
            elif age < 40:
                age_risk = 10
            elif age < 50:
                age_risk = 15
            elif age < 60:
                age_risk = 25
            else:
                age_risk = 30
            risk_score += age_risk
            if age_risk > 20:
                risk_factors.append(f"Age {age} (high risk)")

            # Health condition risk (0-25 points)
            health = row.get('health_condition', 'Good')
            health_risk_map = {'Excellent': 0, 'Good': 5, 'Fair': 15, 'Poor': 25}
            health_risk = health_risk_map.get(health, 10)
            risk_score += health_risk
            if health_risk > 10:
                risk_factors.append(f"Health condition: {health}")

            # Chronic condition risk (0-20 points)
            chronic = row.get('chronic_condition', 'N')
            if chronic == 'Y':
                risk_score += 20
                risk_factors.append("Has chronic condition")

            # Tobacco use risk (0-15 points)
            tobacco = row.get('tobacco_use', 'N')
            if tobacco == 'Y':
                risk_score += 15
                risk_factors.append("Tobacco user")

            # Lifestyle risk (0-10 points)
            lifestyle = row.get('lifestyle', 'Moderate')
            lifestyle_risk_map = {'Active': 0, 'Moderate': 3, 'Sedentary': 10}
            lifestyle_risk = lifestyle_risk_map.get(lifestyle, 5)
            risk_score += lifestyle_risk
            if lifestyle_risk > 5:
                risk_factors.append(f"Lifestyle: {lifestyle}")

            # Store individual risk
            individual_risks.append({
                'employee_id': row.get('employee_id', f'Employee_{idx}'),
                'risk_score': min(risk_score, 100),  # Cap at 100
                'risk_level': 'Low' if risk_score < 30 else 'Medium' if risk_score < 60 else 'High',
                'risk_factors': risk_factors
            })

        # Calculate group statistics
        risk_scores = [r['risk_score'] for r in individual_risks]

        risk_assessment = {
            'group_risk_score': round(np.mean(risk_scores), 2),
            'group_risk_level': 'Low' if np.mean(risk_scores) < 30 else 'Medium' if np.mean(risk_scores) < 60 else 'High',
            'risk_distribution': {
                'low_risk': len([r for r in individual_risks if r['risk_level'] == 'Low']),
                'medium_risk': len([r for r in individual_risks if r['risk_level'] == 'Medium']),
                'high_risk': len([r for r in individual_risks if r['risk_level'] == 'High'])
            },
            'risk_statistics': {
                'min_risk_score': min(risk_scores),
                'max_risk_score': max(risk_scores),
                'median_risk_score': round(np.median(risk_scores), 2),
                'std_risk_score': round(np.std(risk_scores), 2)
            },
            'top_risk_factors': self._get_top_risk_factors(individual_risks),
            'individual_risks': individual_risks[:10]  # Limit to top 10 for API response
        }

        return risk_assessment

    def _get_top_risk_factors(self, individual_risks: List[Dict]) -> Dict[str, int]:
        """Get the most common risk factors across the group."""
        factor_counts = {}

        for person in individual_risks:
            for factor in person['risk_factors']:
                # Extract the main factor type
                if 'Age' in factor:
                    factor_type = 'Advanced Age'
                elif 'Health condition' in factor:
                    factor_type = 'Poor Health Condition'
                elif 'chronic condition' in factor:
                    factor_type = 'Chronic Conditions'
                elif 'Tobacco' in factor:
                    factor_type = 'Tobacco Use'
                elif 'Lifestyle' in factor:
                    factor_type = 'Sedentary Lifestyle'
                else:
                    factor_type = factor

                factor_counts[factor_type] = factor_counts.get(factor_type, 0) + 1

        # Return top 5 risk factors
        sorted_factors = sorted(factor_counts.items(), key=lambda x: x[1], reverse=True)
        return dict(sorted_factors[:5])
