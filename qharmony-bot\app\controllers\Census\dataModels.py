"""
Data models for Census processing.
"""

from typing import List, Dict, Optional, Any
from pydantic import BaseModel, Field
from enum import Enum


class PatternType(Enum):
    ROW_BASED_MEMBER_LEVEL = "row_based_member_level"
    COLUMN_BASED_SINGLE_ROW = "column_based_single_row"
    MIXED_MULTI_FILE = "mixed_multi_file"
    DEPENDENT_COUNT_ONLY = "dependent_count_only"
    BENEFITS_CENTRIC = "benefits_centric"
    HIERARCHICAL_NESTED = "hierarchical_nested"
    ADDRESS_BASED_GROUPING = "address_based_grouping"
    UNKNOWN = "unknown"


class ValidationIssue(BaseModel):
    row: int
    field: str
    issue: str
    value: Optional[str] = None


class FileInfo(BaseModel):
    filename: str
    extension: str
    size: int
    rows: int
    columns: int
    column_names: List[str]


class PatternIdentificationResult(BaseModel):
    pattern_type: str
    pattern_description: str
    confidence: Optional[float] = None
    reason: Optional[str] = None


class FieldMappingResult(BaseModel):
    total_fields_mapped: int
    mapping_details: Dict[str, str]
    validation_result: Dict[str, Any]


class DataQualityInfo(BaseModel):
    complete_records: int
    missing_data_rows: int


class FieldsProcessedInfo(BaseModel):
    name_fields: bool
    age_converted: bool
    gender_standardized: bool
    addresses_cleaned: bool
    zipcode_validated: bool


class PreprocessingSummary(BaseModel):
    original_rows: int
    original_columns: int
    processed_rows: int
    processed_columns: int
    validation_passed: bool
    validation_errors: int
    error_rows: int
    fields_processed: FieldsProcessedInfo
    data_quality: DataQualityInfo


class ValidationResult(BaseModel):
    is_valid: bool
    errors: List[str]
    error_rows: List[ValidationIssue]
    total_errors: int


class PreprocessingResult(BaseModel):
    summary: PreprocessingSummary
    validation_result: ValidationResult


class OutputDataInfo(BaseModel):
    rows: int
    columns: int
    column_names: List[str]


class ProcessingSummary(BaseModel):
    file_info: Optional[Dict[str, Any]] = None  # More flexible
    pattern_identification: Optional[Dict[str, Any]] = None  # More flexible
    field_mapping: Optional[Dict[str, Any]] = None  # More flexible
    preprocessing: Optional[Dict[str, Any]] = None  # More flexible
    output_data: Optional[Dict[str, Any]] = None  # More flexible
    enrichment_and_prediction: Optional[Dict[str, Any]] = None
    universal_field_mapping: Optional[Dict[str, Any]] = None


class CensusProcessingResponse(BaseModel):
    success: bool
    pattern_type: Optional[str] = None
    field_mapping: Optional[Dict[str, str]] = None
    preprocessed_data_csv: Optional[str] = None
    processing_summary: Optional[ProcessingSummary] = None
    validation_passed: Optional[bool] = None
    data_quality_score: Optional[float] = None
    
    # Error fields
    error: Optional[str] = None
    message: Optional[str] = None
    step: Optional[str] = None
    validation_errors: Optional[List[ValidationIssue]] = None
    missing_fields: Optional[List[str]] = None
    available_fields: Optional[List[str]] = None


class CensusProcessingRequest(BaseModel):
    """Request model for census processing (for documentation)."""
    file_description: str = Field(
        default="CSV or XLSX file containing employee census data",
        description="Upload a census file in CSV or XLSX format"
    )
    supported_patterns: List[str] = Field(
        default=[
            "row_based_member_level",
            "column_based_single_row", 
            "dependent_count_only",
            "benefits_centric",
            "address_based_grouping"
        ],
        description="Supported census file patterns"
    )
    required_fields: List[str] = Field(
        default=["name", "gender", "zipcode", "marital_status"],
        description="Mandatory fields that must be mappable from the file"
    )


class SupportedPatternsResponse(BaseModel):
    patterns: Dict[str, str] = Field(
        description="Dictionary of pattern types and their descriptions"
    )


class FieldRequirementsResponse(BaseModel):
    mandatory_fields: Dict[str, Dict[str, Any]]
    optional_fields: Dict[str, Dict[str, Any]]
    validation_rules: Dict[str, str]


# Error response models
class InsufficientDataError(BaseModel):
    error: str = "insufficient_data"
    message: str
    missing_fields: List[str]
    available_fields: List[str]
    recommendation: str = "Please ensure file contains mandatory fields: name, gender, zipcode, marital_status"


class PatternUnrecognizedError(BaseModel):
    error: str = "pattern_unrecognized"
    message: str = "Unable to identify census file pattern"
    detected_columns: List[str]
    recommendation: str = "File may not be a standard census format"


class ValidationFailedError(BaseModel):
    error: str = "validation_failed"
    message: str = "Mandatory field validation failed"
    validation_errors: List[str]
    error_rows: List[ValidationIssue]


class ProcessingFailedError(BaseModel):
    error: str = "processing_failed"
    message: str


# Sample data models for testing
class SampleDataInfo(BaseModel):
    pattern_type: str
    description: str
    sample_headers: List[str]
    sample_data_rows: int
    key_features: List[str]


class TestDatasetResponse(BaseModel):
    available_patterns: List[SampleDataInfo]
    usage_instructions: str = "Use these datasets to test pattern identification and field mapping"
