"""
Refactored Data Preprocessor - Clean, modular, and well-structured.

This refactored version organizes the preprocessing pipeline into clear sections:
1. Main preprocessing orchestration
2. Data structure conversion (row-based to column-based)
3. Field standardization (gender, dates, categorical, numeric)
4. Data validation and quality checks
5. Utility functions

Benefits:
- Clear separation of concerns
- Reduced complexity
- Better maintainability
- Improved readability
- Logical function grouping
"""

import pandas as pd
import numpy as np
import logging
import time
import json
from typing import Dict, List, Any, Optional
from datetime import datetime
import google.generativeai as genai

from .patternIdentifier import PatternType

logger = logging.getLogger(__name__)


class DataPreprocessor:
    """
    Refactored Data Preprocessor with clean, modular structure.
    
    Main sections:
    1. Configuration and initialization
    2. Main preprocessing pipeline
    3. Data structure conversion
    4. Field standardization
    5. Data validation
    6. Utility functions
    """
    
    def __init__(self):
        """Initialize the preprocessor with system configurations."""
        self._setup_configurations()
        self._setup_llm_model()
        
    def _setup_configurations(self):
        """Setup system field configurations and validation rules."""
        # Mandatory fields for validation
        self.mandatory_fields = ["name", "gender", "zipcode", "marital_status"]
        
        # Date parsing formats
        self.date_formats = [
            "%Y-%m-%d", "%m/%d/%Y", "%d/%m/%Y", "%Y/%m/%d",
            "%m-%d-%Y", "%d-%m-%Y", "%B %d, %Y", "%b %d, %Y",
            "%Y-%m-%d %H:%M:%S", "%m/%d/%Y %H:%M:%S", "%d/%m/%Y %H:%M:%S"
        ]
        
        # System field configuration
        self.system_field_config = {
            "categorical": {
                "gender": ["Male", "Female"],
                "marital_status": ["Married", "Not Married"],
                "tobacco_use": ["Y", "N"],
                "pregnancy_status": ["Y", "N"],
                "employment_type": ["Full-Time", "Part-Time", "Temporary", "Seasonal", "Contract"],
                "employee_class": ["Salaried", "Hourly", "Executive", "Management", "Union", "Non-Union"],
                "department": ["Human Resources", "Finance", "Legal", "Executive", "Administration", 
                             "Engineering", "Information Technology", "Data Science", "DevOps", 
                             "Quality Assurance", "Sales", "Marketing", "Customer Service", 
                             "Operations", "Business Development", "Research & Development", 
                             "Manufacturing", "Healthcare", "Education", "Consulting"],
                "record_type": ["Employee", "Dependent"],
                "relationship": ["Employee", "Spouse", "Child"],
                "medical_plan": ["Y", "N"],
                "dental_plan": ["Y", "N"],
                "vision_plan": ["Y", "N"],
                "life_plan": ["Y", "N"],
                "add_plan": ["Y", "N"],
                "coverage_tier": ["Employee Only", "Employee + Spouse", "Employee + Child(ren)", "Family"]
            },
            "numeric": ["salary", "age"],
            "date": ["dob", "hire_date"],
            "text": ["first_name", "middle_name", "last_name", "city", "state", "employee_id"],
            "address": ["address1", "address2"],
            "sensitive": ["ssn"],
            "count": ["dept_count"]
        }
        
        # Add dependent fields dynamically
        self._add_dependent_categorical_fields()
        
        # For backward compatibility
        self.system_field_values = self.system_field_config["categorical"]
    
    def _add_dependent_categorical_fields(self):
        """Add dependent fields to categorical configuration."""
        for i in range(1, 21):  # Support up to 20 dependents
            self.system_field_config["categorical"][f"dept_{i}_gender"] = ["Male", "Female"]
            self.system_field_config["categorical"][f"relationship_type_{i}"] = ["Employee", "Spouse", "Child"]
            
            # Add dependent plan fields
            for plan_type in ["medical_plan", "dental_plan", "vision_plan", "life_plan", "add_plan"]:
                self.system_field_config["categorical"][f"dept_{i}_{plan_type}"] = ["Y", "N"]
    
    def _setup_llm_model(self):
        """Setup Google Gemini model for value mapping."""
        # Initialize Google Gemini
        import os
        from dotenv import load_dotenv
        load_dotenv()

        google_api_key = os.environ.get("GOOGLE_API_KEY")
        if google_api_key:
            try:
                genai.configure(api_key=google_api_key)
                self.model = genai.GenerativeModel('gemini-2.5-flash')
                logger.info("Google Gemini model initialized successfully")
            except Exception as e:
                logger.warning(f"Failed to initialize Google AI model: {e}")
                self.model = None
        else:
            logger.warning("GOOGLE_API_KEY not found. LLM functionality will not be available.")
            self.model = None

    # ============================================================================
    # SECTION 1: MAIN PREPROCESSING PIPELINE
    # ============================================================================
    
    async def preprocess_data(self, df: pd.DataFrame, field_mapping: Dict, pattern_type: str = None) -> Dict:
        """
        Main preprocessing pipeline - orchestrates all preprocessing steps.
        
        Pipeline:
        1. Apply field mapping
        2. Convert structure (row-based → column-based if needed)
        3. Standardize all field values
        4. Validate data quality
        5. Generate summary
        """
        try:
            logger.info("Starting data preprocessing pipeline")
            start_time = time.time()
            
            # Step 1: Apply field mapping
            mapped_df = self._apply_field_mapping(df, field_mapping)
            
            # Step 2: Convert structure if needed
            if pattern_type and ("row_based" in pattern_type or "address_based_grouping" in pattern_type):
                logger.info("Converting row-based data to column-based structure")
                converted_df = await self._convert_row_to_column_based(mapped_df)
                logger.info(f"Converted to column-based: {len(converted_df)} employee records")
            else:
                converted_df = mapped_df
            
            # Step 3: Standardize all values
            standardized_df = await self._standardize_all_values(converted_df)

            # Step 4: Add all system columns for consistency
            standardized_df = self._add_all_system_columns(standardized_df)

            # Step 5: Validate data
            validation_result = self._validate_column_based_data(standardized_df)

            if not validation_result["is_valid"]:
                return {
                    "success": False,
                    "error": "validation_failed",
                    "message": "Mandatory field validation failed",
                    "validation_errors": validation_result["errors"],
                    "error_rows": validation_result["error_rows"]
                }

            # Step 6: Generate processing summary
            processing_time = time.time() - start_time
            summary = self._generate_processing_summary(
                df, standardized_df, validation_result, processing_time
            )
            
            logger.info(f"Preprocessing completed successfully in {processing_time:.2f} seconds")
            
            return {
                "success": True,
                "preprocessed_data": standardized_df,
                "validation": validation_result,
                "summary": summary
            }
            
        except Exception as e:
            logger.error(f"Preprocessing failed: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "message": f"Data preprocessing failed: {str(e)}"
            }
    
    def _apply_field_mapping(self, df: pd.DataFrame, field_mapping: Dict) -> pd.DataFrame:
        """Apply field mapping to rename columns to system field names."""
        logger.info("Applying field mapping")
        
        # Create mapping for existing columns only
        valid_mapping = {k: v for k, v in field_mapping.items() if k in df.columns}
        
        # Apply mapping
        mapped_df = df.rename(columns=valid_mapping)
        
        logger.info(f"Applied mapping to {len(valid_mapping)} columns")
        return mapped_df

    # ============================================================================
    # SECTION 2: DATA STRUCTURE CONVERSION
    # ============================================================================

    async def _convert_row_to_column_based(self, df: pd.DataFrame) -> pd.DataFrame:
        """Convert row-based data to column-based structure using integrated logic."""
        logger.info("Converting row-based data to column-based structure")

        try:
            # Step 1: Group rows by employee
            employee_groups = self._group_by_employee(df)

            # Step 2: Map dependent fields using LLM
            print("mapping dependent fields with llm.")
            dependent_mapping = await self._map_dependent_fields_with_llm(df.columns.tolist())

            # Step 3: Convert structure
            column_based_df = self._convert_structure(df, employee_groups, dependent_mapping)

            return column_based_df

        except Exception as e:
            logger.error(f"Row-to-column conversion failed: {str(e)}")
            return df  # Return original on error

    def _convert_structure(self, df: pd.DataFrame, employee_groups: Dict[str, List[int]],
                          dependent_mapping: Dict[str, str]) -> pd.DataFrame:
        """Convert the actual data structure from row-based to column-based."""
        logger.info("Converting data structure")

        converted_rows = []

        for group_key, row_indices in employee_groups.items():
            # Find the employee row (relationship = Employee)
            employee_row = None
            dependent_rows = []

            for idx in row_indices:
                row = df.iloc[idx]
                relationship = str(row.get("relationship", "")).lower()

                if relationship in ["employee", "self", "primary", "subscriber", "member"]:
                    employee_row = row
                else:
                    dependent_rows.append(row)

            # If no explicit employee found, use first row
            if employee_row is None and row_indices:
                employee_row = df.iloc[row_indices[0]]
                dependent_rows = [df.iloc[idx] for idx in row_indices[1:]]

            if employee_row is not None:
                # Start with employee data
                converted_row = employee_row.to_dict()

                # Add dependent data as columns using the LLM mapping
                for i, dep_row in enumerate(dependent_rows, 1):
                    # Use the dependent mapping to map fields correctly
                    for original_field, mapped_field_template in dependent_mapping.items():
                        if original_field in dep_row and pd.notna(dep_row[original_field]):
                            # Replace {i} with the actual dependent number
                            mapped_field = mapped_field_template.replace("{i}", str(i))
                            converted_row[mapped_field] = dep_row[original_field]

                    # Fallback for common fields if not in mapping
                    if f"dept_{i}" not in converted_row:
                        # Add basic dependent info
                        if "first_name" in dep_row and "last_name" in dep_row:
                            converted_row[f"dept_{i}"] = f"{dep_row['first_name']} {dep_row['last_name']}"
                        elif "first_name" in dep_row:
                            converted_row[f"dept_{i}"] = dep_row["first_name"]
                        elif "name" in dep_row:
                            converted_row[f"dept_{i}"] = dep_row["name"]

                    # Fallback for other common fields if not mapped
                    for field in ["dob", "age", "gender"]:
                        if field in dep_row and f"dept_{i}_{field}" not in converted_row:
                            converted_row[f"dept_{i}_{field}"] = dep_row[field]

                    # Add relationship if not mapped
                    if "relationship" in dep_row and f"relationship_type_{i}" not in converted_row:
                        converted_row[f"relationship_type_{i}"] = dep_row["relationship"]

                    # Add benefit fields if not mapped
                    for benefit in ["medical_plan", "dental_plan", "vision_plan", "life_plan", "add_plan"]:
                        if benefit in dep_row and f"dept_{i}_{benefit}" not in converted_row:
                            converted_row[f"dept_{i}_{benefit}"] = dep_row[benefit]

                # Set dependent count
                converted_row["dept_count"] = len(dependent_rows)

                converted_rows.append(converted_row)

        # Create new DataFrame
        converted_df = pd.DataFrame(converted_rows)

        logger.info(f"Converted to {len(converted_df)} employee records with dependent columns")
        return converted_df

    def _add_all_system_columns(self, df: pd.DataFrame) -> pd.DataFrame:
        """Add all system columns to ensure consistent output structure."""
        logger.info("Adding all system columns for consistent output")

        # Create a copy to avoid modifying the original
        standardized_df = df.copy()

        # Define all possible system columns (excluding dynamic dependent columns)
        base_system_columns = [
            # Core Identity
            "employee_id", "name",

            # Demographics
            "gender", "dob", "age",

            # Address
            "address1", "address2", "city", "state", "zipcode",

            # Family/Relationship
            "marital_status", "relationship", "record_type",

            # Employment
            "salary", "employment_type", "employee_class", "department", "hire_date",

            # Health
            "tobacco_use", "pregnancy_status",

            # Benefits
            "medical_plan", "dental_plan", "vision_plan", "life_plan", "add_plan", "coverage_tier",

            # Dependents
            "dept_count",

            # Sensitive
            "ssn"
        ]

        # Add missing base columns with None values
        for column in base_system_columns:
            if column not in standardized_df.columns:
                standardized_df[column] = None
                logger.debug(f"Added missing system column: {column}")

        # For dependent columns, add based on existing pattern or dept_count
        max_dependents = 0

        # Check if dept_count exists and get max value
        if "dept_count" in standardized_df.columns:
            max_dependents = max(standardized_df["dept_count"].fillna(0).astype(int).max(), max_dependents)

        # Check existing dependent columns to determine max
        existing_dept_cols = [col for col in standardized_df.columns if col.startswith("dept_") and col[5:].split("_")[0].isdigit()]
        if existing_dept_cols:
            dept_numbers = [int(col.split("_")[1]) for col in existing_dept_cols if col.split("_")[1].isdigit()]
            if dept_numbers:
                max_dependents = max(max(dept_numbers), max_dependents)

        # Ensure at least 20 dependent slots for consistency
        max_dependents = max(max_dependents, 20)

        # Add dependent columns up to max_dependents
        dependent_column_types = ["", "_dob", "_age", "_gender"]
        relationship_types = ["relationship_type_"]

        # Create a dictionary to hold all new columns
        new_columns = {}

        for i in range(1, max_dependents + 1):
            # Add basic dependent columns
            for col_type in dependent_column_types:
                col_name = f"dept_{i}{col_type}"
                if col_name not in standardized_df.columns:
                    new_columns[col_name] = [None] * len(standardized_df)
                    logger.debug(f"Added missing dependent column: {col_name}")

            # Add relationship type columns
            for rel_type in relationship_types:
                col_name = f"{rel_type}{i}"
                if col_name not in standardized_df.columns:
                    new_columns[col_name] = [None] * len(standardized_df)
                    logger.debug(f"Added missing relationship column: {col_name}")

        # Add all new columns at once using pd.concat
        if new_columns:
            new_df = pd.DataFrame(new_columns, index=standardized_df.index)
            standardized_df = pd.concat([standardized_df, new_df], axis=1)

        logger.info(f"Standardized output with {len(standardized_df.columns)} total columns")
        return standardized_df

    # ============================================================================
    # SECTION 3: FIELD STANDARDIZATION
    # ============================================================================
    
    async def _standardize_all_values(self, df: pd.DataFrame) -> pd.DataFrame:
        """Standardize all data values using systematic approach."""
        logger.info("Standardizing all data values")
        
        standardized_df = df.copy()
        
        # Step 1: Handle name fields
        standardized_df = self._standardize_name_fields(standardized_df)
        
        # Step 2: Convert DOB to age (employee + dependents)
        standardized_df = self._standardize_date_fields(standardized_df)
        
        # Step 3: Standardize gender (employee + dependents)
        standardized_df = self._standardize_gender_fields(standardized_df)
        
        # Step 4: Standardize all categorical fields
        standardized_df = await self._standardize_categorical_fields(standardized_df)
        
        # Step 5: Standardize numeric fields
        standardized_df = self._standardize_numeric_fields(standardized_df)
        
        logger.info("Completed systematic value standardization")
        return standardized_df

    def _standardize_name_fields(self, df: pd.DataFrame) -> pd.DataFrame:
        """Standardize name fields by combining all name components into single 'name' field and removing individual fields."""

        # Create the combined name field
        if "first_name" in df.columns and "last_name" in df.columns:
            # Combine first_name and last_name
            df["name"] = df["first_name"].astype(str) + " " + df["last_name"].astype(str)
            df["name"] = df["name"].str.replace("nan ", "").str.replace(" nan", "").str.strip()
            logger.info("Combined first_name and last_name into name field")

            # Add middle_name if present
            if "middle_name" in df.columns:
                # Insert middle name between first and last
                first_names = df["first_name"].astype(str).str.strip()
                middle_names = df["middle_name"].astype(str).str.strip()
                last_names = df["last_name"].astype(str).str.strip()

                # Only add middle name if it's not null/nan
                df["name"] = first_names + " " + middle_names.where(
                    (middle_names != "nan") & (middle_names != "") & (middle_names.notna()), ""
                ).str.strip() + " " + last_names
                df["name"] = df["name"].str.replace("  ", " ").str.strip()  # Remove double spaces
                logger.info("Added middle_name to combined name field")

        elif "name" not in df.columns:
            # Try to create name from any available name fields
            if "first_name" in df.columns:
                df["name"] = df["first_name"].astype(str).str.strip()
                logger.info("Used first_name as name field")
            elif "last_name" in df.columns:
                df["name"] = df["last_name"].astype(str).str.strip()
                logger.info("Used last_name as name field")

        # Remove individual name fields after combining
        name_fields_to_remove = ["first_name", "last_name", "middle_name"]
        for field in name_fields_to_remove:
            if field in df.columns:
                df = df.drop(columns=[field])
                logger.info(f"Removed {field} field after combining into name")

        return df

    def _standardize_date_fields(self, df: pd.DataFrame) -> pd.DataFrame:
        """Convert DOB fields to age for employee and all dependents."""
        logger.info("Converting DOB fields to age")

        # Find all DOB columns (employee + dependents)
        dob_columns = [col for col in df.columns if col == "dob" or col.endswith("_dob")]

        total_conversions = 0
        for dob_col in dob_columns:
            age_col = dob_col.replace("_dob", "_age") if dob_col != "dob" else "age"

            # Convert DOB to age
            converted_count = 0
            for idx, dob_value in df[dob_col].items():
                if pd.notna(dob_value):
                    age = self._calculate_age_from_dob(dob_value)
                    if age is not None:
                        df.at[idx, age_col] = age
                        converted_count += 1

            if converted_count > 0:
                logger.info(f"Converted {converted_count} {dob_col} values to {age_col}")
                total_conversions += converted_count

        logger.info(f"Total DOB to age conversions: {total_conversions}")
        return df

    def _standardize_gender_fields(self, df: pd.DataFrame) -> pd.DataFrame:
        """Standardize gender values to Male/Female for employee and all dependents."""

        def standardize_gender_value(value):
            if pd.isna(value) or value == "":
                return None

            value_str = str(value).strip().upper()

            if value_str in ["M", "MALE", "MAN", "BOY", "1"]:
                return "Male"
            elif value_str in ["F", "FEMALE", "WOMAN", "GIRL", "2"]:
                return "Female"
            else:
                logger.warning(f"Unknown gender value: {value}")
                return None

        # Find all gender columns (employee + dependents)
        gender_columns = [col for col in df.columns if col == "gender" or col.endswith("_gender")]

        if not gender_columns:
            logger.warning("No gender columns found")
            return df

        logger.info(f"Standardizing gender values for columns: {gender_columns}")

        total_standardized = 0
        for col in gender_columns:
            df[col] = df[col].apply(standardize_gender_value)
            valid_count = df[col].notna().sum()
            male_count = (df[col] == "Male").sum()
            female_count = (df[col] == "Female").sum()

            total_standardized += valid_count
            logger.info(f"Standardized {col}: {valid_count} values ({male_count} Male, {female_count} Female)")

        logger.info(f"Total gender standardization: {total_standardized} values")
        return df

    async def _standardize_categorical_fields(self, df: pd.DataFrame) -> pd.DataFrame:
        """Standardize all categorical fields using LLM batch processing."""
        logger.info("Standardizing categorical fields")

        categorical_fields = self.system_field_config.get("categorical", {})

        # Collect fields that exist in the dataframe
        fields_to_process = {}
        for field, allowed_values in categorical_fields.items():
            if field in df.columns:
                unique_values = df[field].dropna().unique().tolist()
                unique_values = [str(val) for val in unique_values if str(val).lower() not in ['nan', 'none', '']]
                if unique_values:
                    fields_to_process[field] = {
                        'unique_values': unique_values,
                        'allowed_values': allowed_values
                    }

        if not fields_to_process:
            logger.info("No categorical fields to process")
            return df

        # Generate batch mapping using LLM
        batch_mapping = await self._generate_batch_value_mapping(fields_to_process)

        # Apply mappings
        for field, mapping in batch_mapping.items():
            if field in df.columns:
                logger.info(f"Applying mapping to {field}: {mapping}")
                df[field] = df[field].map(mapping).fillna(df[field])

        return df

    def _standardize_numeric_fields(self, df: pd.DataFrame) -> pd.DataFrame:
        """Standardize numeric fields by removing currency symbols and converting to float."""
        numeric_fields = self.system_field_config.get("numeric", [])

        for field in numeric_fields:
            if field in df.columns:
                # Remove currency symbols and convert to numeric
                df[field] = df[field].astype(str).str.replace(r'[$,]', '', regex=True)
                df[field] = pd.to_numeric(df[field], errors='coerce')
                logger.info(f"Standardized numeric field: {field}")

        return df

    # ============================================================================
    # SECTION 4: DATA VALIDATION
    # ============================================================================

    def _validate_column_based_data(self, df: pd.DataFrame) -> Dict:
        """Validate column-based data structure (simplified validation)."""
        logger.info("Validating column-based data")

        errors = []
        error_rows = []

        # Check mandatory fields for all rows (since all are employees now)
        for field in self.mandatory_fields:
            if field not in df.columns:
                errors.append(f"Mandatory field '{field}' not found in data")
                continue

            # Find rows with missing values
            missing_mask = df[field].isna() | (df[field] == "") | (df[field] == "None")
            missing_rows = df.index[missing_mask].tolist()

            if missing_rows:
                errors.append(f"Field '{field}' has missing values in {len(missing_rows)} rows")
                error_rows.extend([{"row": row + 1, "field": field, "issue": "missing_value"}
                                 for row in missing_rows])

        return {
            "is_valid": len(errors) == 0,
            "errors": errors,
            "error_rows": error_rows,
            "total_errors": len(errors)
        }

    # ============================================================================
    # SECTION 5: UTILITY FUNCTIONS
    # ============================================================================

    def _calculate_age_from_dob(self, dob_value) -> Optional[int]:
        """Calculate age from date of birth."""
        if pd.isna(dob_value):
            return None

        try:
            # Try to parse the date
            for date_format in self.date_formats:
                try:
                    birth_date = datetime.strptime(str(dob_value), date_format)
                    today = datetime.now()
                    age = today.year - birth_date.year - ((today.month, today.day) < (birth_date.month, birth_date.day))
                    return age if 0 <= age <= 120 else None
                except ValueError:
                    continue

            logger.warning(f"Could not parse date: {dob_value}")
            return None

        except Exception as e:
            logger.warning(f"Error calculating age from {dob_value}: {str(e)}")
            return None

    async def _map_dependent_fields_with_llm(self, columns: List[str]) -> Dict[str, str]:
        """Use LLM to map dependent fields to system structure."""
        if not self.model:
            return self._fallback_dependent_mapping(columns)

        try:
            prompt = f"""
            Map these census file columns to standardized dependent field format:

            Columns: {columns}

            Map to patterns like:
            - dept_{{i}} for dependent names
            - dept_{{i}}_dob for dependent birth dates
            - dept_{{i}}_gender for dependent gender
            - relationship_type_{{i}} for relationships

            Return JSON mapping like: {{"original_field": "dept_field_with_{{i}}"}}
            """

            response = self.model.generate_content(prompt)
            mapping_text = response.text.strip()

            # Extract JSON
            if mapping_text.startswith("```json"):
                mapping_text = mapping_text.split("```json")[1].split("```")[0].strip()
            elif mapping_text.startswith("```"):
                mapping_text = mapping_text.split("```")[1].split("```")[0].strip()

            return json.loads(mapping_text)

        except Exception as e:
            logger.warning(f"LLM dependent mapping failed: {str(e)}")
            return self._fallback_dependent_mapping(columns)

    def _fallback_dependent_mapping(self, columns: List[str]) -> Dict[str, str]:
        """Fallback mapping for dependent fields."""
        mapping = {}
        for col in columns:
            col_lower = col.lower()
            if "dep" in col_lower and "name" in col_lower:
                mapping[col] = "dept_{i}"
            elif "dep" in col_lower and "dob" in col_lower:
                mapping[col] = "dept_{i}_dob"
            elif "dep" in col_lower and "gender" in col_lower:
                mapping[col] = "dept_{i}_gender"
            elif "dep" in col_lower and "relationship" in col_lower:
                mapping[col] = "relationship_type_{i}"
        return mapping

    def _group_by_employee(self, df: pd.DataFrame) -> Dict[str, List[int]]:
        """Group rows by employee to identify families."""
        logger.info("Grouping rows by employee")

        employee_groups = {}
        processed_rows = set()

        # Strategy 1: Group by employee_id if present
        if "employee_id" in df.columns:
            for emp_id in df["employee_id"].unique():
                if pd.notna(emp_id):
                    group_rows = df[df["employee_id"] == emp_id].index.tolist()
                    employee_groups[str(emp_id)] = group_rows
                    processed_rows.update(group_rows)
                    logger.debug(f"Grouped {len(group_rows)} rows by employee_id: {emp_id}")

        # Strategy 2: Group remaining rows by address + last name combination
        remaining_rows = [idx for idx in df.index if idx not in processed_rows]
        if remaining_rows and "address1" in df.columns:
            remaining_df = df.loc[remaining_rows]

            # Group by address + last name combination for better family identification
            for address in remaining_df["address1"].unique():
                if pd.notna(address) and str(address).strip():
                    address_rows = remaining_df[remaining_df["address1"] == address]

                    # Further group by last name within the same address
                    for idx in address_rows.index:
                        if idx in processed_rows:
                            continue

                        row = df.iloc[idx]
                        last_name = self._extract_last_name(row)

                        if last_name:
                            # Find all rows with same address + last name
                            same_family_mask = (
                                (remaining_df["address1"] == address) &
                                remaining_df.apply(lambda r: self._extract_last_name(r) == last_name, axis=1)
                            )
                            group_rows = remaining_df[same_family_mask].index.tolist()

                            # Only process if not already processed
                            group_rows = [r for r in group_rows if r not in processed_rows]

                            if group_rows:
                                group_key = f"{last_name}_Family_{address.replace(' ', '_')}"
                                employee_groups[group_key] = group_rows
                                processed_rows.update(group_rows)
                                logger.debug(f"Grouped {len(group_rows)} rows by address + last name: {address} + {last_name}")
                        else:
                            # Fallback: single row without clear last name
                            if idx not in processed_rows:
                                name_key = self._get_name_for_grouping(row)
                                group_key = f"{name_key}_Individual_{idx}"
                                employee_groups[group_key] = [idx]
                                processed_rows.add(idx)
                                logger.debug(f"Individual row (no last name): {group_key}")

        # Strategy 3: Handle remaining individual rows
        remaining_rows = [idx for idx in df.index if idx not in processed_rows]
        for idx in remaining_rows:
            row = df.iloc[idx]
            name_key = self._get_name_for_grouping(row)
            group_key = f"{name_key}_Individual_{idx}"
            employee_groups[group_key] = [idx]
            logger.debug(f"Individual row: {group_key}")

        logger.info(f"Created {len(employee_groups)} employee groups from {len(df)} rows")
        return employee_groups

    def _get_name_for_grouping(self, row: pd.Series) -> str:
        """Extract name for grouping, handling different name field formats."""
        # Try different name field combinations
        if "name" in row and pd.notna(row["name"]) and str(row["name"]).strip():
            return str(row["name"]).replace(" ", "_")
        elif "first_name" in row and "last_name" in row:
            first = str(row.get("first_name", "")).strip()
            last = str(row.get("last_name", "")).strip()
            if first or last:
                return f"{first}_{last}".strip("_")
        elif "first_name" in row and pd.notna(row["first_name"]):
            return str(row["first_name"]).replace(" ", "_")
        elif "last_name" in row and pd.notna(row["last_name"]):
            return str(row["last_name"]).replace(" ", "_")

        # Fallback to Unknown
        return "Unknown"

    def _extract_last_name(self, row: pd.Series) -> str:
        """Extract last name for family grouping."""
        # Try last_name field first
        if "last_name" in row and pd.notna(row["last_name"]) and str(row["last_name"]).strip():
            return str(row["last_name"]).strip()

        # Try to extract from full name field
        elif "name" in row and pd.notna(row["name"]) and str(row["name"]).strip():
            name_parts = str(row["name"]).strip().split()
            if len(name_parts) >= 2:
                return name_parts[-1]  # Last part is usually last name
            elif len(name_parts) == 1:
                return name_parts[0]  # Single name

        # Try first_name as fallback (better than nothing for grouping)
        elif "first_name" in row and pd.notna(row["first_name"]) and str(row["first_name"]).strip():
            return str(row["first_name"]).strip()

        # No usable name found
        return ""

    def _convert_employee_group_to_row(self, df: pd.DataFrame, row_indices: List[int],
                                     dependent_mapping: Dict[str, str]) -> Optional[Dict]:
        """Convert a group of rows (employee + dependents) to a single row."""
        if not row_indices:
            return None

        # Find employee row and dependent rows
        employee_row = None
        dependent_rows = []

        for idx in row_indices:
            row = df.iloc[idx]
            relationship = row.get("relationship", "Employee")

            if relationship == "Employee":
                employee_row = row
            else:
                dependent_rows.append(row)

        if employee_row is None:
            employee_row = df.iloc[row_indices[0]]  # Use first row as employee

        # Start with employee data
        converted_row = employee_row.to_dict()

        # Add dependent data using mapping
        for i, dep_row in enumerate(dependent_rows, 1):
            for original_field, mapped_field_template in dependent_mapping.items():
                if original_field in dep_row and pd.notna(dep_row[original_field]):
                    mapped_field = mapped_field_template.replace("{i}", str(i))
                    converted_row[mapped_field] = dep_row[original_field]

        # Set dependent count
        converted_row["dept_count"] = len(dependent_rows)

        return converted_row

    async def _generate_batch_value_mapping(self, fields_data: Dict) -> Dict[str, Dict[str, str]]:
        """Generate value mappings for all categorical fields in one API call."""
        if not self.model:
            return {field: self._fallback_value_mapping(field, data['unique_values'], data['allowed_values'])
                   for field, data in fields_data.items()}

        try:
            # Create batch prompt
            prompt = "Standardize these field values to system standards:\n\n"

            for field, data in fields_data.items():
                prompt += f"Field: {field}\n"
                prompt += f"Current values: {data['unique_values']}\n"
                prompt += f"Standard values: {data['allowed_values']}\n"
                prompt += f"Map each current value to the most appropriate standard value.\n\n"

            prompt += "Return JSON format: {\"field_name\": {\"current_value\": \"standard_value\"}}"

            response = self.model.generate_content(prompt)
            mapping_text = response.text.strip()

            # Extract JSON
            if mapping_text.startswith("```json"):
                mapping_text = mapping_text.split("```json")[1].split("```")[0].strip()
            elif mapping_text.startswith("```"):
                mapping_text = mapping_text.split("```")[1].split("```")[0].strip()

            return json.loads(mapping_text)

        except Exception as e:
            logger.warning(f"LLM batch mapping failed: {str(e)}")
            return {field: self._fallback_value_mapping(field, data['unique_values'], data['allowed_values'])
                   for field, data in fields_data.items()}

    def _fallback_value_mapping(self, field: str, unique_values: List[str], allowed_values: List[str]) -> Dict[str, str]:
        """Fallback value mapping using rule-based approach."""
        mapping = {}

        for value in unique_values:
            value_str = str(value).strip().lower()

            # Gender mapping
            if field.endswith("_gender") or field == "gender":
                if value_str in ["m", "male", "man", "boy"]:
                    mapping[value] = "Male"
                elif value_str in ["f", "female", "woman", "girl"]:
                    mapping[value] = "Female"
                else:
                    mapping[value] = value  # Keep original if unknown

            # Marital status mapping
            elif field == "marital_status":
                if value_str in ["married", "m", "yes", "y"]:
                    mapping[value] = "Married"
                elif value_str in ["single", "divorced", "widowed", "s", "d", "w", "no", "n"]:
                    mapping[value] = "Not Married"
                else:
                    mapping[value] = value

            # Plan fields mapping
            elif field.endswith("_plan"):
                if value_str in ["yes", "y", "true", "1"]:
                    mapping[value] = "Y"
                elif value_str in ["no", "n", "false", "0"]:
                    mapping[value] = "N"
                else:
                    mapping[value] = value

            # Default: keep original value
            else:
                mapping[value] = value

        return mapping

    def _generate_processing_summary(self, original_df: pd.DataFrame, processed_df: pd.DataFrame,
                                   validation_result: Dict, processing_time: float) -> Dict:
        """Generate comprehensive processing summary."""
        return {
            "original_rows": len(original_df),
            "original_columns": len(original_df.columns),
            "processed_rows": len(processed_df),
            "processed_columns": len(processed_df.columns),
            "processing_time_seconds": round(processing_time, 2),
            "validation_passed": validation_result["is_valid"],
            "validation_errors": validation_result["total_errors"],
            "error_rows": len(validation_result["error_rows"]),
            "fields_processed": {
                "name_fields": "name" in processed_df.columns,
                "age_converted": "age" in processed_df.columns,
                "gender_standardized": "gender" in processed_df.columns,
                "addresses_cleaned": any(field in processed_df.columns for field in ["address1", "city", "state"]),
                "zipcode_validated": "zipcode" in processed_df.columns
            },
            "data_quality": {
                "complete_records": len(processed_df.dropna(subset=self.mandatory_fields)),
                "missing_data_rows": len(processed_df) - len(processed_df.dropna(subset=self.mandatory_fields))
            }
        }
