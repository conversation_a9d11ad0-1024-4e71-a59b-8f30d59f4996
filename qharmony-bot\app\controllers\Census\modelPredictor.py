"""
Model Predictor Module for Census Processing
Handles loading and prediction using trained ML models for health plan and benefits assignment
"""

import pandas as pd
import numpy as np
import joblib
import logging
import os
from typing import Dict, List, Any, Optional, Tuple
from pathlib import Path

logger = logging.getLogger(__name__)


class CensusModelPredictor:
    """
    Handles ML model predictions for health plan types and benefits assignment.
    Loads trained models and provides prediction with confidence scores.
    """

    def __init__(self, models_path: str = "app/models/Census"):
        """
        Initialize the model predictor.

        Args:
            models_path: Path to the directory containing trained models
        """
        self.models_path = Path(models_path)
        self.plan_type_model = None
        self.benefits_model = None
        self.label_encoder = None
        self.metadata = None
        self.models_loaded = False

        # Required features for prediction (must match training exactly)
        self.required_features = [
            'age', 'gender', 'marital_status', 'dept_count', 'job_type', 'employment_type',
            'department', 'region', 'health_condition', 'chronic_condition', 'prescription_use',
            'income_tier', 'risk_tolerance', 'lifestyle', 'travel_frequency', 'hsa_familiarity',
            'pregnancy_status', 'tobacco_use', 'mental_health_needs'
        ]

        # Benefit columns from training (these are NOT features, they are targets)
        self.ancillary_benefits = ['Dental', 'Vision']
        self.voluntary_benefits = ['Hospital Indemnity', 'Accident', 'Critical Illness', 'Cancer Insurance',
                                 'Gap Insurance', 'Term Life', 'Whole Life', 'AD&D', 'Group Life',
                                 'STD', 'LTD', 'Supplemental Life']
        self.financial_benefits = ['HSA', 'FSA', 'Commuter Benefits', 'Wellness Programs', 'Legal/ID Theft',
                                 'Employee Assistance']
        self.all_benefit_columns = self.ancillary_benefits + self.voluntary_benefits + self.financial_benefits
        
        # Load models on initialization
        self._load_models()
    
    def _load_models(self) -> bool:
        """
        Load trained models from disk.
        
        Returns:
            True if models loaded successfully, False otherwise
        """
        try:
            # Check if model files exist
            plan_type_path = self.models_path / "plan_type_model.pkl"
            benefits_path = self.models_path / "benefits_model.pkl"
            encoder_path = self.models_path / "plan_type_label_encoder.pkl"
            metadata_path = self.models_path / "model_metadata.pkl"
            
            if not all(path.exists() for path in [plan_type_path, benefits_path, encoder_path, metadata_path]):
                logger.warning(f"Model files not found in {self.models_path}")
                return False
            
            # Load models
            self.plan_type_model = joblib.load(plan_type_path)
            self.benefits_model = joblib.load(benefits_path)
            self.label_encoder = joblib.load(encoder_path)
            self.metadata = joblib.load(metadata_path)

            # Validate loaded metadata
            if 'features' in self.metadata:
                loaded_features = self.metadata['features']
                if loaded_features != self.required_features:
                    logger.warning(f"Feature mismatch! Expected: {self.required_features}, Got: {loaded_features}")

            # Get valid benefit columns from metadata
            self.valid_benefit_columns = self.metadata.get('valid_benefit_columns', self.all_benefit_columns)

            self.models_loaded = True
            logger.info(f"Models loaded successfully. Features: {len(self.required_features)}, Benefits: {len(self.valid_benefit_columns)}")
            return True

        except Exception as e:
            logger.error(f"Error loading models: {e}")
            self.models_loaded = False
            return False
    
    def predict_plan_types(self, df: pd.DataFrame, top_k: int = 3) -> pd.DataFrame:
        """
        Predict health plan types for all employees with confidence scores.
        
        Args:
            df: Enriched dataframe with all required features
            top_k: Number of top predictions to return per employee
            
        Returns:
            Dataframe with plan type predictions and confidence scores
        """
        if not self.models_loaded:
            logger.error("Models not loaded. Cannot make predictions.")
            return df
        
        logger.info(f"Predicting plan types for {len(df)} employees")
        
        try:
            # Prepare features for prediction
            feature_df = self._prepare_features(df)
            
            # Get predictions and probabilities
            predictions = self.plan_type_model.predict(feature_df)
            probabilities = self.plan_type_model.predict_proba(feature_df)
            
            # Decode predictions
            predicted_plans = self.label_encoder.inverse_transform(predictions)
            
            # Get top-k predictions with confidence and reasoning
            plan_predictions = []
            for i, (pred_plan, probs) in enumerate(zip(predicted_plans, probabilities)):
                # Get top-k indices
                top_indices = np.argsort(probs)[-top_k:][::-1]
                top_plans = self.label_encoder.inverse_transform(top_indices)
                top_confidences = probs[top_indices]

                # Get reasoning for the predicted plan
                try:
                    employee_row = df.iloc[i]
                    _, plan_reason = self.assign_health_plan_with_reason(employee_row, reason=True)
                except Exception as e:
                    logger.warning(f"Could not generate plan reasoning for employee {i}: {e}")
                    plan_reason = f"ML model prediction: {pred_plan}"

                prediction_data = {
                    'employee_index': i,
                    'predicted_plan_type': pred_plan,
                    'plan_confidence': top_confidences[0],
                    'plan_reason': plan_reason,
                    'top_3_plans': list(top_plans),
                    'top_3_confidences': list(top_confidences)
                }
                plan_predictions.append(prediction_data)
            
            # Add predictions to dataframe
            result_df = df.copy()
            result_df['predicted_plan_type'] = [p['predicted_plan_type'] for p in plan_predictions]
            result_df['plan_confidence'] = [p['plan_confidence'] for p in plan_predictions]
            result_df['plan_reason'] = [p['plan_reason'] for p in plan_predictions]
            result_df['top_3_plans'] = [p['top_3_plans'] for p in plan_predictions]
            result_df['top_3_plan_confidences'] = [p['top_3_confidences'] for p in plan_predictions]
            
            logger.info("Plan type prediction completed successfully")
            return result_df
            
        except Exception as e:
            logger.error(f"Error in plan type prediction: {e}")
            return df
    
    def predict_benefits(self, df: pd.DataFrame, top_k: int = 3) -> pd.DataFrame:
        """
        Predict benefits assignment for all employees with confidence scores.

        Args:
            df: Enriched dataframe with all required features
            top_k: Number of top benefit predictions to return per employee

        Returns:
            Dataframe with benefits predictions and confidence scores
        """
        if not self.models_loaded:
            logger.error("Models not loaded. Cannot make predictions.")
            return df

        logger.info(f"Predicting benefits for {len(df)} employees")

        try:
            # Prepare features for prediction
            feature_df = self._prepare_features(df)
            logger.info(f"Prepared features: {len(feature_df)} rows, {len(feature_df.columns)} columns")

            # Debug: Check if feature preparation changed the number of rows
            if len(feature_df) != len(df):
                logger.warning(f"Feature preparation changed row count: {len(df)} -> {len(feature_df)}")

            # Get predictions and probabilities
            predictions = self.benefits_model.predict(feature_df)
            logger.info(f"Model predictions shape: {predictions.shape if hasattr(predictions, 'shape') else len(predictions)}")

            # Debug: Check if model prediction changed the number of predictions
            pred_length = predictions.shape[0] if hasattr(predictions, 'shape') else len(predictions)
            if pred_length != len(feature_df):
                logger.warning(f"Model prediction changed row count: {len(feature_df)} -> {pred_length}")

            # For multi-label classification, we need to handle probabilities differently
            try:
                if hasattr(self.benefits_model, 'predict_proba'):
                    # Single output case
                    probabilities = self.benefits_model.predict_proba(feature_df)
                elif hasattr(self.benefits_model, 'estimators_'):
                    # Multi-output case - get probabilities for each classifier
                    probabilities = []
                    for estimator in self.benefits_model.estimators_:
                        if hasattr(estimator, 'predict_proba'):
                            probs = estimator.predict_proba(feature_df)[:, 1]  # Probability of positive class
                        else:
                            # If no predict_proba, use decision function or predictions
                            probs = estimator.predict(feature_df).astype(float)
                        probabilities.append(probs)
                    probabilities = np.array(probabilities).T
                else:
                    # Fallback: use predictions as probabilities
                    probabilities = predictions.astype(float)
            except Exception as e:
                logger.warning(f"Error getting benefit probabilities: {e}, using predictions as probabilities")
                probabilities = predictions.astype(float)

            logger.info(f"Probabilities shape: {probabilities.shape if hasattr(probabilities, 'shape') else len(probabilities)}")

            # Get benefit names from metadata
            valid_benefit_columns = self.metadata.get('valid_benefit_columns', [])
            logger.info(f"Valid benefit columns: {len(valid_benefit_columns)}")

            # Process predictions for each employee
            benefits_predictions = []
            logger.info(f"Processing predictions for {len(predictions)} employees")
            for i, (pred_benefits, probs) in enumerate(zip(predictions, probabilities)):
                try:
                    # Ensure we have the right number of benefit columns
                    if isinstance(pred_benefits, np.ndarray):
                        # Flatten multi-dimensional arrays
                        pred_benefits_array = pred_benefits.flatten()
                    else:
                        pred_benefits_array = np.array(pred_benefits).flatten()

                    num_benefits = min(len(valid_benefit_columns), len(pred_benefits_array))

                    # Get predicted benefits (where prediction = 1)
                    predicted_benefits = []
                    benefit_confidences = []

                    for j in range(num_benefits):
                        try:
                            # Get the value and ensure it's a scalar
                            value = pred_benefits_array[j]

                            # Convert to scalar - handle all possible types
                            if hasattr(value, '__len__') and not isinstance(value, (str, bytes)):
                                # It's array-like but not string
                                if len(value) == 1:
                                    scalar_value = float(value[0])
                                else:
                                    scalar_value = float(np.mean(value))
                            else:
                                # It's already a scalar
                                scalar_value = float(value)

                            # Now do scalar comparison safely
                            if scalar_value > 0.5:
                                predicted_benefits.append(valid_benefit_columns[j])
                                benefit_confidences.append(scalar_value)

                        except Exception as inner_e:
                            logger.warning(f"Error processing benefit {j} for employee {i}: {inner_e}")
                            # Skip this benefit and continue
                            continue

                    # Get top-k benefits by probability
                    try:
                        # Safely check if probs has enough elements
                        probs_len = len(probs) if hasattr(probs, '__len__') else 0
                        if probs_len >= num_benefits:
                            # Convert probs to a flat array of scalars first
                            prob_values = []
                            for j in range(min(probs_len, num_benefits)):
                                try:
                                    p_val = probs[j]
                                    # Handle array-like probabilities
                                    if hasattr(p_val, '__len__') and not isinstance(p_val, (str, bytes)):
                                        if len(p_val) == 1:
                                            prob_values.append(float(p_val[0]))
                                        else:
                                            prob_values.append(float(np.mean(p_val)))
                                    else:
                                        prob_values.append(float(p_val))
                                except Exception as e:
                                    logger.warning(f"Error processing probability {j} for employee {i}: {e}")
                                    prob_values.append(0.5)  # Default fallback

                            # Now sort the indices based on the scalar values
                            if prob_values:
                                top_indices = np.argsort(prob_values)[-top_k:][::-1]
                                top_benefits = [valid_benefit_columns[j] for j in top_indices if j < len(valid_benefit_columns)]
                                top_confidences = [prob_values[j] for j in top_indices if j < len(prob_values)]
                            else:
                                top_benefits = predicted_benefits[:top_k] if predicted_benefits else valid_benefit_columns[:top_k]
                                top_confidences = [0.5] * len(top_benefits)
                        else:
                            # Fallback if probabilities don't match
                            top_benefits = predicted_benefits[:top_k] if predicted_benefits else valid_benefit_columns[:top_k]
                            top_confidences = [0.5] * len(top_benefits)
                    except Exception as e:
                        logger.warning(f"Error processing top benefits for employee {i}: {e}")
                        top_benefits = predicted_benefits[:top_k] if predicted_benefits else valid_benefit_columns[:top_k]
                        top_confidences = [0.5] * len(top_benefits)

                    # Calculate average confidence for predicted benefits
                    if predicted_benefits and benefit_confidences:
                        avg_confidence = float(np.mean(benefit_confidences))
                    else:
                        avg_confidence = 0.0

                    # Get reasoning for the predicted benefits
                    try:
                        # Create a copy of the row to avoid modifying the original
                        employee_row = df.iloc[i].copy()

                        # Convert any array-like values to scalars before passing to assign_benefits_with_reason
                        for col in employee_row.index:
                            val = employee_row[col]
                            if hasattr(val, '__len__') and not isinstance(val, (str, bytes)):
                                try:
                                    if len(val) == 1:
                                        employee_row[col] = val[0]
                                    elif len(val) > 1:
                                        # For arrays, use the first value
                                        employee_row[col] = val[0]
                                    else:
                                        # Empty array, use default
                                        employee_row[col] = None
                                except:
                                    # If any error, set to None
                                    employee_row[col] = None

                        _, benefits_reasons = self.assign_benefits_with_reason(employee_row, reason=True)

                        # Create reasoning text for predicted benefits
                        benefits_reasoning = []
                        for benefit in predicted_benefits:
                            if benefit in benefits_reasons:
                                benefits_reasoning.append(f"{benefit}: {benefits_reasons[benefit]}")
                        benefits_reason_text = "; ".join(benefits_reasoning) if benefits_reasoning else "ML model prediction"
                    except Exception as e:
                        logger.warning(f"Could not generate benefits reasoning for employee {i}: {e}")
                        benefits_reason_text = f"ML model prediction: {len(predicted_benefits)} benefits assigned"

                    prediction_data = {
                        'employee_index': i,
                        'predicted_benefits': predicted_benefits,
                        'benefits_confidence': avg_confidence,
                        'benefits_reason': benefits_reason_text,
                        'top_3_benefits': top_benefits[:top_k],
                        'top_3_benefits_confidences': top_confidences[:top_k]
                    }
                    benefits_predictions.append(prediction_data)

                except Exception as e:
                    logger.warning(f"Error processing benefits for employee {i}: {e}")
                    # Fallback prediction with all required keys
                    fallback_benefits = valid_benefit_columns[:2] if len(valid_benefit_columns) >= 2 else valid_benefit_columns
                    prediction_data = {
                        'employee_index': i,
                        'predicted_benefits': fallback_benefits,
                        'benefits_confidence': 0.5,
                        'benefits_reason': f"Fallback prediction due to error: {str(e)[:100]}",
                        'top_3_benefits': valid_benefit_columns[:top_k],
                        'top_3_benefits_confidences': [0.5] * min(top_k, len(valid_benefit_columns))
                    }
                    benefits_predictions.append(prediction_data)
            
            # Ensure we have predictions for all employees
            if len(benefits_predictions) != len(df):
                logger.warning(f"Benefits predictions length ({len(benefits_predictions)}) doesn't match DataFrame length ({len(df)})")
                logger.info(f"Creating fallback predictions for missing employees")

                # Create a complete predictions list with exact length match
                complete_predictions = []
                for i in range(len(df)):
                    if i < len(benefits_predictions):
                        # Use existing prediction
                        complete_predictions.append(benefits_predictions[i])
                    else:
                        # Create fallback prediction for missing employees
                        fallback_benefits = ['Dental', 'Vision'] if len(valid_benefit_columns) >= 2 else valid_benefit_columns[:2]
                        complete_predictions.append({
                            'employee_index': i,
                            'predicted_benefits': fallback_benefits,
                            'benefits_confidence': 0.5,
                            'benefits_reason': "Default benefits assignment due to prediction error",
                            'top_3_benefits': ['Dental', 'Vision', 'Hospital Indemnity'],
                            'top_3_benefits_confidences': [0.5, 0.5, 0.5]
                        })

                benefits_predictions = complete_predictions
                logger.info(f"Created {len(complete_predictions)} total predictions (including {len(df) - len(benefits_predictions) if len(benefits_predictions) < len(df) else 0} fallbacks)")

            # Add predictions to dataframe with safe key access
            result_df = df.copy()

            # Handle length mismatch between predictions and dataframe
            if len(benefits_predictions) != len(result_df):
                logger.warning(f"Benefits predictions length ({len(benefits_predictions)}) doesn't match DataFrame length ({len(result_df)})")

                # Create a mapping from prediction index to dataframe index
                # This ensures we apply predictions to the correct rows
                if len(benefits_predictions) < len(result_df):
                    # Create default predictions for all rows
                    default_predictions = []
                    for i in range(len(result_df)):
                        default_predictions.append({
                            'employee_index': i,
                            'predicted_benefits': ['Dental', 'Vision'],
                            'benefits_confidence': 0.5,
                            'benefits_reason': 'Default prediction',
                            'top_3_benefits': ['Dental', 'Vision', 'Term Life'],
                            'top_3_benefits_confidences': [0.5, 0.4, 0.3]
                        })

                    # Update default predictions with actual predictions where available
                    for pred in benefits_predictions:
                        idx = pred.get('employee_index', 0)
                        if 0 <= idx < len(default_predictions):
                            default_predictions[idx] = pred

                    # Use the complete set of predictions
                    benefits_predictions = default_predictions
                else:
                    # If we have more predictions than rows, truncate
                    benefits_predictions = benefits_predictions[:len(result_df)]

            # Apply predictions safely
            result_df['predicted_benefits'] = [p.get('predicted_benefits', ['Dental', 'Vision']) for p in benefits_predictions]
            result_df['benefits_confidence'] = [p.get('benefits_confidence', 0.5) for p in benefits_predictions]
            result_df['benefits_reason'] = [p.get('benefits_reason', 'Default prediction') for p in benefits_predictions]
            result_df['top_3_benefits'] = [p.get('top_3_benefits', ['Dental', 'Vision', 'Term Life']) for p in benefits_predictions]
            result_df['top_3_benefits_confidences'] = [p.get('top_3_benefits_confidences', [0.5, 0.4, 0.3]) for p in benefits_predictions]
            
            logger.info("Benefits prediction completed successfully")
            return result_df
            
        except Exception as e:
            logger.error(f"Error in benefits prediction: {e}")
            return df
    
    def _prepare_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Prepare features for model prediction matching the training approach.
        Models expect features in exact order and format as during training.

        Args:
            df: Input dataframe

        Returns:
            Dataframe with only required features in correct order and format
        """
        # Check for missing required features and handle gracefully
        missing_features = [f for f in self.required_features if f not in df.columns]
        if missing_features:
            logger.warning(f"Missing required features: {missing_features}. Will fill with defaults.")

        # Create feature dataframe with ALL required features in EXACT order from training
        feature_df = pd.DataFrame(index=df.index)

        for feature in self.required_features:
            if feature in df.columns:
                feature_df[feature] = df[feature].copy()
            else:
                # Fill missing features with appropriate defaults based on training data patterns
                if feature == 'age':
                    feature_df[feature] = 35  # Default age
                elif feature == 'dept_count':
                    feature_df[feature] = '0'   # Default no dependents (string format)
                elif feature == 'gender':
                    feature_df[feature] = 'Male'  # Default gender
                elif feature == 'marital_status':
                    feature_df[feature] = 'Not Married'  # Default marital status
                elif feature == 'job_type':
                    feature_df[feature] = 'Desk'  # Default job type
                elif feature == 'employment_type':
                    feature_df[feature] = 'Full-Time'  # Default employment type
                elif feature == 'department':
                    feature_df[feature] = 'Information Technology'  # Default department
                elif feature == 'region':
                    feature_df[feature] = 'Urban'  # Default region
                elif feature == 'health_condition':
                    feature_df[feature] = 'Good'  # Default health condition
                elif feature in ['chronic_condition', 'tobacco_use', 'pregnancy_status', 'mental_health_needs', 'hsa_familiarity']:
                    feature_df[feature] = 'N'  # Default Y/N fields
                elif feature == 'prescription_use':
                    feature_df[feature] = 'None'  # Default prescription use
                elif feature == 'income_tier':
                    feature_df[feature] = 'Medium ($50K–$100K)'  # Default income tier
                elif feature == 'risk_tolerance':
                    feature_df[feature] = 'Medium'  # Default risk tolerance
                elif feature == 'lifestyle':
                    feature_df[feature] = 'Moderate'  # Default lifestyle
                elif feature == 'travel_frequency':
                    feature_df[feature] = 'Rare'  # Default travel frequency
                else:
                    feature_df[feature] = 'Unknown'  # Fallback for any other features

        # Handle any remaining missing values with proper data types
        for col in feature_df.columns:
            if feature_df[col].isna().any():
                logger.warning(f"Found missing values in feature {col}, filling with defaults")
                if col == 'age':
                    # Numeric columns
                    feature_df[col] = pd.to_numeric(feature_df[col], errors='coerce').fillna(35)
                elif col == 'dept_count':
                    # Dept count should be string format to match training
                    feature_df[col] = feature_df[col].fillna('0').astype(str)
                else:
                    # Categorical columns - keep as strings (models expect this)
                    feature_df[col] = feature_df[col].astype('object').fillna('Unknown')
            else:
                # Ensure proper data types
                if col == 'age':
                    feature_df[col] = pd.to_numeric(feature_df[col], errors='coerce')
                elif col == 'dept_count':
                    # Ensure dept_count is string format to match training
                    feature_df[col] = feature_df[col].astype(str)
                else:
                    # Keep categorical as object/string (models handle encoding internally)
                    feature_df[col] = feature_df[col].astype('object')

        # Final validation - ensure all required features are present
        missing_final = [f for f in self.required_features if f not in feature_df.columns]
        if missing_final:
            logger.error(f"Critical error: Missing features after preparation: {missing_final}")

        logger.debug(f"Prepared features for {len(feature_df)} employees with {len(feature_df.columns)} features")
        return feature_df



    def get_model_info(self) -> Dict[str, Any]:
        """
        Get information about loaded models.
        
        Returns:
            Dictionary containing model information
        """
        if not self.models_loaded:
            return {"models_loaded": False, "error": "Models not loaded"}
        
        info = {
            "models_loaded": True,
            "models_path": str(self.models_path),
            "required_features": self.required_features,
            "plan_type_classes": list(self.label_encoder.classes_) if self.label_encoder else [],
            "valid_benefit_columns": self.metadata.get('valid_benefit_columns', []) if self.metadata else [],
            "model_metadata": self.metadata if self.metadata else {}
        }
        
        return info
    
    def validate_features(self, df: pd.DataFrame) -> Dict[str, Any]:
        """
        Validate that dataframe has all required features for prediction.
        
        Args:
            df: Dataframe to validate
            
        Returns:
            Validation result dictionary
        """
        missing_features = [f for f in self.required_features if f not in df.columns]
        
        feature_completeness = {}
        for feature in self.required_features:
            if feature in df.columns:
                missing_count = df[feature].isna().sum()
                feature_completeness[feature] = {
                    "present": True,
                    "missing_values": missing_count,
                    "completion_rate": ((len(df) - missing_count) / len(df) * 100) if len(df) > 0 else 0
                }
            else:
                feature_completeness[feature] = {
                    "present": False,
                    "missing_values": len(df),
                    "completion_rate": 0
                }
        
        overall_completion = sum(
            fc["completion_rate"] for fc in feature_completeness.values()
        ) / len(self.required_features) if self.required_features else 0
        
        return {
            "is_valid": len(missing_features) == 0,
            "missing_features": missing_features,
            "feature_completeness": feature_completeness,
            "overall_completion_rate": overall_completion,
            "ready_for_prediction": len(missing_features) == 0 and overall_completion > 95
        }

    # ==================== REASONING FUNCTIONS FROM STANDARIZE.PY ====================

    def assign_health_plan_with_reason(self, row, reason=False):
        """
        Assign health plan with reasoning logic from standarize.py
        This provides the reasoning for plan assignments to augment ML predictions.
        """
        reason_text = []

        # 1. Indemnity: High income, excellent health, high risk tolerance - Standardized Y/N values
        if row['income_tier'] == 'High (>$100K)' and row['health_condition'] == 'Excellent' and row['risk_tolerance'] == 'High' and row['chronic_condition'] == 'N':
            if row['age'] < 50 and row['dept_count'] == '0':
                reason_text.append("High income, excellent health, high risk tolerance, young with no dependents - ideal for Indemnity.")
                plan = 'Indemnity'
            elif row['age'] >= 50:
                reason_text.append("High income, excellent health, high risk tolerance, but older age - PPO for better network access.")
                plan = 'PPO'
            else:
                reason_text.append("High income, excellent health, high risk tolerance with dependents - PPO for family coverage.")
                plan = 'PPO'

        # 2. HDHP + HSA: High income, good health, HSA familiarity - Standardized Y/N values
        elif row['income_tier'] == 'High (>$100K)' and row['health_condition'] in ['Good', 'Excellent'] and row['hsa_familiarity'] == 'Y' and row['chronic_condition'] == 'N':
            reason_text.append("High income, good health, HSA familiar, no chronic conditions - HDHP + HSA for tax benefits.")
            plan = 'HDHP + HSA'

        # 3. HMO: Budget-conscious, predictable care needs
        elif row['income_tier'] == 'Low (<$50K)' and row['health_condition'] in ['Fair', 'Poor'] and row['chronic_condition'] == 'Y':
            reason_text.append("Low income, fair/poor health, chronic conditions - HMO for cost-effective managed care.")
            plan = 'HMO'

        # 4. PPO: Balanced choice for most scenarios
        elif row['income_tier'] == 'Medium ($50K–$100K)' and row['health_condition'] == 'Good':
            reason_text.append("Medium income, good health - PPO for balanced cost and flexibility.")
            plan = 'PPO'

        # 5. DHMO: Dental-focused, budget-conscious
        elif row['income_tier'] == 'Low (<$50K)' and row['age'] < 35:
            reason_text.append("Low income, young age - DHMO for basic dental coverage.")
            plan = 'DHMO'

        # 6. EPO: Network-based, moderate cost
        elif row['income_tier'] == 'Medium ($50K–$100K)' and row['region'] == 'Urban':
            reason_text.append("Medium income, urban area - EPO for network-based care.")
            plan = 'EPO'

        # 7. POS: Point of Service for coordinated care
        elif row['chronic_condition'] == 'Y' and row['age'] > 45:
            reason_text.append("Chronic conditions, older age - POS for coordinated care management.")
            plan = 'POS'

        # 8. MEC: Minimum Essential Coverage
        elif row['income_tier'] == 'Low (<$50K)' and row['employment_type'] == 'Part-Time':
            reason_text.append("Low income, part-time employment - MEC for minimum coverage compliance.")
            plan = 'MEC'

        # Default fallback logic
        else:
            if row['age'] < 30:
                reason_text.append("Young age - PPO for flexibility and future needs.")
                plan = 'PPO'
            elif row['age'] >= 30 and row['age'] < 50:
                if row['dept_count'] != '0':
                    reason_text.append("Middle age with dependents - PPO for family coverage.")
                    plan = 'PPO'
                else:
                    reason_text.append("Middle age, no dependents - HMO for cost efficiency.")
                    plan = 'HMO'
            else:  # age >= 50
                reason_text.append("Older age - PPO for comprehensive coverage and provider choice.")
                plan = 'PPO'

        if reason:
            return plan, " ".join(reason_text)
        return plan

    def assign_benefits_with_reason(self, row, reason=False):
        """
        Assign benefits with reasoning logic from standarize.py
        This provides the reasoning for benefits assignments to augment ML predictions.
        """
        benefits = []
        reasons = {}

        # Safely extract values with type conversion
        try:
            # Convert age to int or float
            age_val = row.get('age', 35)
            if isinstance(age_val, (list, np.ndarray)):
                age = float(age_val[0]) if len(age_val) > 0 else 35
            else:
                age = float(age_val) if pd.notna(age_val) else 35

            # Extract string values with fallbacks
            health = str(row.get('health_condition', 'Good')) if pd.notna(row.get('health_condition')) else 'Good'

            # Handle chronic condition (Y/N)
            chronic_val = row.get('chronic_condition', 'N')
            if isinstance(chronic_val, (list, np.ndarray)):
                chronic = 'Y' if len(chronic_val) > 0 and str(chronic_val[0]).upper() == 'Y' else 'N'
            else:
                chronic = 'Y' if pd.notna(chronic_val) and str(chronic_val).upper() == 'Y' else 'N'

            # Extract income tier
            income = str(row.get('income_tier', 'Medium ($50K–$100K)')) if pd.notna(row.get('income_tier')) else 'Medium ($50K–$100K)'

            # Handle dependent count - convert to string for comparison
            dept_count_val = row.get('dept_count', '0')
            if isinstance(dept_count_val, (list, np.ndarray)):
                dept_count = str(dept_count_val[0]) if len(dept_count_val) > 0 else '0'
            else:
                dept_count = str(dept_count_val) if pd.notna(dept_count_val) else '0'

            # Extract lifestyle
            lifestyle = str(row.get('lifestyle', 'Moderate')) if pd.notna(row.get('lifestyle')) else 'Moderate'

            # Extract travel frequency
            travel = str(row.get('travel_frequency', 'Rare')) if pd.notna(row.get('travel_frequency')) else 'Rare'

            # Handle pregnancy status (Y/N)
            pregnancy_val = row.get('pregnancy_status', 'N')
            if isinstance(pregnancy_val, (list, np.ndarray)):
                pregnancy = 'Y' if len(pregnancy_val) > 0 and str(pregnancy_val[0]).upper() == 'Y' else 'N'
            else:
                pregnancy = 'Y' if pd.notna(pregnancy_val) and str(pregnancy_val).upper() == 'Y' else 'N'

            # Handle tobacco use (Y/N)
            tobacco_val = row.get('tobacco_use', 'N')
            if isinstance(tobacco_val, (list, np.ndarray)):
                tobacco = 'Y' if len(tobacco_val) > 0 and str(tobacco_val[0]).upper() == 'Y' else 'N'
            else:
                tobacco = 'Y' if pd.notna(tobacco_val) and str(tobacco_val).upper() == 'Y' else 'N'

            # Handle mental health needs (Y/N)
            mental_health_val = row.get('mental_health_needs', 'N')
            if isinstance(mental_health_val, (list, np.ndarray)):
                mental_health = 'Y' if len(mental_health_val) > 0 and str(mental_health_val[0]).upper() == 'Y' else 'N'
            else:
                mental_health = 'Y' if pd.notna(mental_health_val) and str(mental_health_val).upper() == 'Y' else 'N'

        except Exception as e:
            # Log the error and use default values
            logger.warning(f"Error extracting values for benefits reasoning: {e}")
            age = 35
            health = 'Good'
            chronic = 'N'
            income = 'Medium ($50K–$100K)'
            dept_count = '0'
            lifestyle = 'Moderate'
            travel = 'Rare'
            pregnancy = 'N'
            tobacco = 'N'
            mental_health = 'N'

        # 1. Dental - Universal need
        benefits.append('Dental')
        reasons['Dental'] = "Essential oral health coverage for all employees."

        # 2. Vision - Universal need
        benefits.append('Vision')
        reasons['Vision'] = "Eye care coverage for all employees."

        # 3. Hospital Indemnity - Based on health and age
        if health in ['Fair', 'Poor'] or age > 50 or chronic == 'Y':
            benefits.append('Hospital Indemnity')
            reasons['Hospital Indemnity'] = f"Recommended due to {health.lower()} health condition, age {int(age)}, or chronic conditions."

        # 4. Accident Insurance - Based on lifestyle and job type
        if lifestyle == 'Active' or travel == 'Frequent':
            benefits.append('Accident')
            reasons['Accident'] = f"Recommended for {lifestyle.lower()} lifestyle and {travel.lower()} travel frequency."

        # 5. Critical Illness - Based on age and health
        if age > 40 or chronic == 'Y' or health in ['Fair', 'Poor']:
            benefits.append('Critical Illness')
            reasons['Critical Illness'] = f"Important protection for age {int(age)} with {health.lower()} health status."

        # 6. Term Life - Based on dependents and age
        if dept_count != '0' or age > 30:
            benefits.append('Term Life')
            if dept_count != '0':
                reasons['Term Life'] = f"Essential protection for {dept_count} dependents."
            else:
                reasons['Term Life'] = f"Important coverage at age {int(age)}."

        # 7. AD&D - Based on lifestyle and travel
        if lifestyle == 'Active' or travel == 'Frequent':
            benefits.append('AD&D')
            reasons['AD&D'] = f"Additional protection for {lifestyle.lower()} lifestyle and {travel.lower()} travel."

        # 8. Short Term Disability - Based on income and health
        if income in ['Medium ($50K–$100K)', 'High (>$100K)'] or health in ['Fair', 'Poor']:
            benefits.append('STD')
            reasons['STD'] = f"Income protection for {income} earners or {health.lower()} health status."

        # 9. Long Term Disability - Based on income and age
        if income == 'High (>$100K)' or age > 35:
            benefits.append('LTD')
            reasons['LTD'] = f"Long-term income protection for {income} earners or age {int(age)}+."

        # 10. Whole Life - Based on high income and dependents
        if income == 'High (>$100K)' and dept_count != '0':
            benefits.append('Whole Life')
            reasons['Whole Life'] = f"Investment-grade life insurance for high income earners with {dept_count} dependents."

        # 11. Flexible Spending Account (FSA) - Based on health needs
        if chronic == 'Y' or health in ['Fair', 'Poor'] or pregnancy == 'Y':
            benefits.append('FSA')
            reasons['FSA'] = "Tax-advantaged account for medical expenses due to health conditions or pregnancy."

        if reason:
            return benefits, reasons
        return benefits
