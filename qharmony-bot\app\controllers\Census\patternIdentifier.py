"""
Pattern Identifier - Identifies patterns using STANDARDIZED field names after mapping.
"""

import pandas as pd
from typing import Dict, List, Tuple
from enum import Enum
import logging

logger = logging.getLogger(__name__)


class PatternType(Enum):
    ROW_BASED_MEMBER_LEVEL = "row_based_member_level"  # Pattern 1
    COLUMN_BASED_SINGLE_ROW = "column_based_single_row"  # Pattern 2
    DEPENDENT_COUNT_ONLY = "dependent_count_only"  # Pattern 4
    BENEFITS_CENTRIC = "benefits_centric"  # Pattern 5
    ADDRESS_BASED_GROUPING = "address_based_grouping"  # Pattern 7
    UNKNOWN = "unknown"


class PatternIdentifier:
    """Identifies census file patterns using STANDARDIZED field names after universal mapping."""
    
    def __init__(self):
        self.pattern_detectors = [
            self._detect_row_based_member_level,
            self._detect_column_based_single_row,
            self._detect_dependent_count_only,
            self._detect_benefits_centric,
            self._detect_address_based_grouping
        ]
    
    def identify_pattern(self, df: pd.DataFrame) -> <PERSON><PERSON>[PatternType, Dict]:
        """
        Identify pattern using STANDARDIZED field names.
        
        Args:
            df: DataFrame with STANDARDIZED system field names
            
        Returns:
            Tuple of (PatternType, confidence_info)
        """
        logger.info(f"Identifying pattern for DataFrame with standardized fields: {list(df.columns)}")
        
        # Run all pattern detectors
        pattern_scores = {}
        
        for detector in self.pattern_detectors:
            try:
                pattern_type, confidence, reason = detector(df)
                pattern_scores[pattern_type] = {
                    "confidence": confidence,
                    "reason": reason
                }
                logger.debug(f"{pattern_type.value}: confidence={confidence:.2f}, reason={reason}")
            except Exception as e:
                logger.warning(f"Pattern detector failed: {e}")
                continue
        
        # Select pattern with highest confidence
        if pattern_scores:
            best_pattern = max(pattern_scores.keys(), key=lambda p: pattern_scores[p]["confidence"])
            
            # Require minimum confidence threshold
            if pattern_scores[best_pattern]["confidence"] >= 0.6:
                logger.info(f"Identified pattern: {best_pattern.value} with confidence {pattern_scores[best_pattern]['confidence']:.2f}")
                return best_pattern, pattern_scores[best_pattern]
        
        logger.warning("Unable to identify pattern with sufficient confidence")
        return PatternType.UNKNOWN, {"confidence": 0.0, "reason": "No pattern matched with sufficient confidence"}
    
    def _detect_row_based_member_level(self, df: pd.DataFrame) -> Tuple[PatternType, float, str]:
        """Detect Pattern 1: Row-Based Format using standardized fields."""
        confidence = 0.0
        reasons = []
        
        # Look for standardized relationship field
        if "relationship" in df.columns:
            confidence += 0.4
            reasons.append("Found 'relationship' field")
            
            # Check for employee/dependent values
            unique_relationships = df["relationship"].str.lower().unique()
            employee_values = ["self", "employee", "emp", "ee"]
            dependent_values = ["spouse", "child", "dependent", "dep"]
            
            has_employees = any(val in employee_values for val in unique_relationships if pd.notna(val))
            has_dependents = any(val in dependent_values for val in unique_relationships if pd.notna(val))
            
            if has_employees and has_dependents:
                confidence += 0.4
                reasons.append("Found both employee and dependent relationship values")
            elif has_employees:
                confidence += 0.2
                reasons.append("Found employee relationship values")
        
        # Look for record_type field
        if "record_type" in df.columns:
            confidence += 0.3
            reasons.append("Found 'record_type' field")
            
            # Check for Employee/Dependent values
            unique_types = df["record_type"].str.lower().unique()
            if any(val in ["employee", "dependent", "emp", "dep"] for val in unique_types if pd.notna(val)):
                confidence += 0.2
                reasons.append("Found Employee/Dependent record types")
        
        # Look for duplicate employee_ids (indicating family grouping)
        if "employee_id" in df.columns:
            if df["employee_id"].duplicated().any():
                confidence += 0.3
                reasons.append("Found duplicate employee_ids (family grouping)")
        
        return PatternType.ROW_BASED_MEMBER_LEVEL, confidence, "; ".join(reasons)
    
    def _detect_column_based_single_row(self, df: pd.DataFrame) -> Tuple[PatternType, float, str]:
        """Detect Pattern 2: Column-Based Format using standardized fields."""
        confidence = 0.0
        reasons = []
        
        # Look for dependent column patterns (dept_1, dept_2, etc.) - more flexible matching
        dependent_columns = []
        for col in df.columns:
            if col.startswith("dept_"):
                # Extract number part after dept_
                parts = col.split("_")
                if len(parts) >= 2 and parts[1].isdigit():
                    dependent_columns.append(col)
        
        if dependent_columns:
            confidence += 0.5
            reasons.append(f"Found {len(dependent_columns)} dependent columns")
            
            # Check for numbered patterns - extract all dependent numbers
            dependent_numbers = set()
            for col in dependent_columns:
                try:
                    parts = col.split("_")
                    if len(parts) >= 2 and parts[1].isdigit():
                        num = int(parts[1])
                        dependent_numbers.add(num)
                except (IndexError, ValueError):
                    continue
            
            if len(dependent_numbers) >= 2:
                confidence += 0.3
                reasons.append(f"Found numbered dependent patterns: {sorted(dependent_numbers)}")
        
        # Look for relationship_type columns
        relationship_type_columns = [col for col in df.columns if col.startswith("relationship_type_")]
        
        if relationship_type_columns:
            confidence += 0.3
            reasons.append(f"Found {len(relationship_type_columns)} relationship type columns")
        
        # Look for dept_count column
        if "dept_count" in df.columns:
            confidence += 0.2
            reasons.append("Found 'dept_count' column")
        
        # Check that we don't have duplicate employee IDs (would indicate row-based)
        if "employee_id" in df.columns and not df["employee_id"].duplicated().any():
            confidence += 0.1
            reasons.append("No duplicate employee_ids (consistent with single row per employee)")
        
        return PatternType.COLUMN_BASED_SINGLE_ROW, confidence, "; ".join(reasons)
    
    def _detect_dependent_count_only(self, df: pd.DataFrame) -> Tuple[PatternType, float, str]:
        """Detect Pattern 4: Dependent Count Only using standardized fields."""
        confidence = 0.0
        reasons = []
        
        # Look for dept_count column
        if "dept_count" in df.columns:
            confidence += 0.5
            reasons.append("Found 'dept_count' column")
            
            # Check that there are NO detailed dependent columns
            detailed_dep_columns = [col for col in df.columns 
                                  if col.startswith("dept_") and col != "dept_count"]
            
            if not detailed_dep_columns:
                confidence += 0.4
                reasons.append("No detailed dependent columns found")
            else:
                confidence -= 0.2
                reasons.append(f"Found detailed dependent columns: {detailed_dep_columns}")
            
            # Check for employee-only fields
            employee_fields = ["first_name", "last_name", "employee_id"]
            found_employee_fields = [f for f in employee_fields if f in df.columns]
            
            if len(found_employee_fields) >= 2:
                confidence += 0.2
                reasons.append(f"Found employee fields: {found_employee_fields}")
        
        return PatternType.DEPENDENT_COUNT_ONLY, confidence, "; ".join(reasons)
    
    def _detect_benefits_centric(self, df: pd.DataFrame) -> Tuple[PatternType, float, str]:
        """Detect Pattern 5: Benefits-Centric Format using standardized fields."""
        confidence = 0.0
        reasons = []
        
        # Look for coverage_tier column
        if "coverage_tier" in df.columns:
            confidence += 0.4
            reasons.append("Found 'coverage_tier' column")
            
            # Check for typical tier values
            unique_values = df["coverage_tier"].str.lower().unique()
            tier_keywords = ["employee", "family", "spouse", "child", "single"]
            if any(any(keyword in str(val) for keyword in tier_keywords) 
                   for val in unique_values if pd.notna(val)):
                confidence += 0.3
                reasons.append("Found coverage tier values")
        
        # Look for benefit plan columns
        benefit_columns = [col for col in df.columns 
                          if col in ["medical_plan", "dental_plan", "vision_plan"]]
        
        if len(benefit_columns) >= 2:
            confidence += 0.3
            reasons.append(f"Found {len(benefit_columns)} benefit plan columns")
        
        return PatternType.BENEFITS_CENTRIC, confidence, "; ".join(reasons)
    
    def _detect_address_based_grouping(self, df: pd.DataFrame) -> Tuple[PatternType, float, str]:
        """Detect Pattern 7: Address-Based Grouping using standardized fields."""
        confidence = 0.0
        reasons = []
        
        # Look for address columns
        if "address1" in df.columns:
            confidence += 0.3
            reasons.append("Found 'address1' column")
            
            # Check for duplicate addresses (indicating family grouping)
            if df["address1"].duplicated().any():
                confidence += 0.3
                reasons.append("Found duplicate addresses")
        
        # Check for absence of explicit relationship/ID columns
        explicit_columns = ["employee_id", "relationship", "record_type"]
        found_explicit = [col for col in explicit_columns if col in df.columns]
        
        if not found_explicit:
            confidence += 0.3
            reasons.append("No explicit relationship/ID columns found")
        else:
            confidence -= 0.2
            reasons.append(f"Found explicit columns: {found_explicit}")
        
        # Look for name columns
        name_columns = [col for col in df.columns if col in ["first_name", "last_name"]]
        
        if len(name_columns) >= 2:
            confidence += 0.2
            reasons.append("Found name columns")
        
        return PatternType.ADDRESS_BASED_GROUPING, confidence, "; ".join(reasons)
    
    def get_pattern_description(self, pattern_type: PatternType) -> str:
        """Get human-readable description of pattern type."""
        descriptions = {
            PatternType.ROW_BASED_MEMBER_LEVEL: "Each person (employee + dependents) has separate row, linked by Employee_ID",
            PatternType.COLUMN_BASED_SINGLE_ROW: "Each employee has one row with dependent information in additional columns",
            PatternType.DEPENDENT_COUNT_ONLY: "Contains only employee information with dependent count",
            PatternType.BENEFITS_CENTRIC: "Focus on benefit elections with family structure implied",
            PatternType.ADDRESS_BASED_GROUPING: "No explicit relationships, grouped by address and demographics"
        }
        return descriptions.get(pattern_type, "Unknown pattern type")
    
    def get_pattern_requirements(self, pattern_type: PatternType) -> Dict:
        """Get the specific requirements for each pattern type."""
        requirements = {
            PatternType.ROW_BASED_MEMBER_LEVEL: {
                "required_fields": ["employee_id", "relationship"],
                "optional_fields": ["record_type", "first_name", "last_name"],
                "processing_notes": "Group by employee_id, distinguish by relationship"
            },
            PatternType.COLUMN_BASED_SINGLE_ROW: {
                "required_fields": ["employee_id"],
                "optional_fields": ["dept_count", "dept_1", "dept_2"],
                "processing_notes": "Extract dependent info from column patterns"
            },
            PatternType.DEPENDENT_COUNT_ONLY: {
                "required_fields": ["employee_id", "dept_count"],
                "optional_fields": ["first_name", "last_name"],
                "processing_notes": "Only count information available"
            },
            PatternType.BENEFITS_CENTRIC: {
                "required_fields": ["coverage_tier"],
                "optional_fields": ["medical_plan", "dental_plan"],
                "processing_notes": "Infer family structure from coverage tier"
            },
            PatternType.ADDRESS_BASED_GROUPING: {
                "required_fields": ["address1", "first_name", "last_name"],
                "optional_fields": ["dob", "gender"],
                "processing_notes": "Group by address and infer relationships"
            }
        }
        
        return requirements.get(pattern_type, {
            "required_fields": [],
            "optional_fields": [],
            "processing_notes": "Unknown pattern requirements"
        })
