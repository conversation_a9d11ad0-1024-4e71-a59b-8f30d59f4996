"""
CMS API Service for Census Processing

Service for integrating with Health.gov CMS Marketplace API to fetch actual health plans
based on employee demographics and predictions.
"""

import requests
import json
import os
from typing import Dict, List, Optional, Any, Union
from urllib.parse import urljoin
import logging
import pandas as pd
from dotenv import load_dotenv
load_dotenv()
logger = logging.getLogger(__name__)


class CMSAPIService:
    """
    CMS Marketplace API service for fetching health plans based on employee data.
    Integrates with the census processing pipeline to provide real marketplace plans.
    """

    def __init__(self, api_key: str = None, base_url: str = "https://marketplace.api.healthcare.gov/api/v1"):
        # Use provided API key or environment variable
        if api_key is None:
            api_key = os.getenv('CMS_API_KEY')

        if not api_key:
            raise ValueError("API key must be provided either as parameter or CMS_API_KEY environment variable")

        self.api_key = api_key
        self.base_url = base_url
        self.session = requests.Session()

        # Federal marketplace states (states that use HealthCare.gov)
        self.FEDERAL_MARKETPLACE_STATES = {
            "AL", "AK", "AZ", "AR", "DE", "FL", "GA", "HI", "IL", "IN", "IA", "KS", "LA",
            "MI", "MS", "MO", "MT", "NE", "NH", "NC", "ND", "OH", "OK", "SC", "SD", "TN",
            "TX", "UT", "VA", "WV", "WI", "WY"
        }

        # County lookup cache for performance optimization
        # Maps ZIP code to county FIPS data to avoid repeated API calls
        self._county_cache = {}

    def _safe_numeric(self, value, default=0):
        """Safely convert value to numeric, handling categorical strings."""
        if value is None:
            return default
        try:
            # Handle categorical dept_count values
            if str(value) == "3+":
                return 3
            return float(value)
        except (ValueError, TypeError):
            return default

    def make_request(self, endpoint: str, method: str = "GET", params: Optional[Dict[str, Any]] = None,
                     data: Optional[Dict[str, Any]] = None, timeout: int = 30) -> Dict[str, Any]:
        """Make HTTP request to CMS API with proper error handling."""
        url = urljoin(self.base_url + "/", endpoint)
        if params is None:
            params = {}
        params['apikey'] = self.api_key
        
        headers = {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'User-Agent': 'Census-CMS-API-Client/1.0'
        }
        
        try:
            logger.debug(f"Making {method} request to: {endpoint}")
            logger.debug(f"Query params: {params}")
            logger.debug(f"Request data: {data}")
            
            if method.upper() == "GET":
                response = self.session.get(url, params=params, headers=headers, timeout=timeout)
            elif method.upper() == "POST":
                response = self.session.post(url, params=params, json=data, headers=headers, timeout=timeout)
            else:
                raise ValueError(f"Unsupported HTTP method: {method}")
            
            response.raise_for_status()
            result = response.json()
            logger.debug(f"Request successful. Status: {response.status_code}")
            
            return {
                'success': True,
                'status_code': response.status_code,
                'data': result,
                'url': url,
                'params': params,
                'endpoint': endpoint
            }
        except requests.exceptions.RequestException as e:
            logger.error(f"Request failed: {e}")
            error_data = e.response.text if hasattr(e, 'response') else None
            logger.error(f"API error response: {error_data}")
            
            return {
                'success': False,
                'error': str(e),
                'error_data': error_data,
                'status_code': getattr(e.response, 'status_code', None) if hasattr(e, 'response') else None,
                'url': url,
                'params': params,
                'endpoint': endpoint
            }
        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse JSON response: {e}")
            return {
                'success': False,
                'error': f"Invalid JSON response: {e}",
                'status_code': response.status_code if 'response' in locals() else None,
                'url': url,
                'params': params,
                'endpoint': endpoint
            }

    def get_location_info(self, zipcode: str) -> Dict[str, Any]:
        """Get comprehensive location information for a ZIP code with caching."""
        # Check cache first
        if zipcode in self._county_cache:
            logger.debug(f"Using cached county data for ZIP {zipcode}")
            return {
                "success": True,
                "data": {"counties": [self._county_cache[zipcode]]},
                "cached": True
            }

        # Make API call if not cached
        result = self.make_request(f"counties/by/zip/{zipcode}")

        # Cache successful results
        if result.get("success") and result.get("data", {}).get("counties"):
            counties = result["data"]["counties"]
            if counties:
                # Cache the first (primary) county
                self._county_cache[zipcode] = counties[0]
                logger.debug(f"Cached county data for ZIP {zipcode}: {counties[0].get('name')}")

        return result

    def get_cached_county_fips(self, zipcode: str) -> Dict[str, Any]:
        """Get county FIPS from cache or API call."""
        result = self.get_location_info(zipcode)

        if result.get("success"):
            counties = result.get("data", {}).get("counties", [])
            if counties:
                county = counties[0]
                return {
                    "success": True,
                    "fips": county.get("fips"),
                    "state": county.get("state"),
                    "name": county.get("name"),
                    "cached": result.get("cached", False)
                }

        return {
            "success": False,
            "error": result.get("error", "Failed to get county information"),
            "cached": False
        }

    def batch_lookup_counties(self, zipcodes: List[str]) -> Dict[str, Dict[str, Any]]:
        """Batch lookup counties for multiple ZIP codes with caching."""
        results = {}
        uncached_zips = []

        # Check cache first
        for zipcode in zipcodes:
            if zipcode in self._county_cache:
                results[zipcode] = {
                    "success": True,
                    "fips": self._county_cache[zipcode].get("fips"),
                    "state": self._county_cache[zipcode].get("state"),
                    "name": self._county_cache[zipcode].get("name"),
                    "cached": True
                }
            else:
                uncached_zips.append(zipcode)

        # Lookup uncached ZIP codes
        for zipcode in uncached_zips:
            county_result = self.get_cached_county_fips(zipcode)
            results[zipcode] = county_result

        logger.info(f"County lookup: {len(zipcodes)} requested, {len(uncached_zips)} API calls, {len(zipcodes) - len(uncached_zips)} cached")

        return results

    def _map_income_tier_to_amount(self, income_tier: str) -> int:
        """
        Map income tier strings to dollar amounts for CMS API.
        
        Args:
            income_tier: Income tier like "High (>100K)", "Medium (50K-100K)", etc.
            
        Returns:
            Integer income amount
        """
        if not income_tier or pd.isna(income_tier):
            return 50000  # Default middle income
            
        income_tier = str(income_tier).upper()
        
        # Extract income ranges and map to representative values
        if "HIGH" in income_tier or ">100K" in income_tier or ">$100" in income_tier:
            return 120000
        elif "MEDIUM" in income_tier or "50K-100K" in income_tier or "50-100" in income_tier:
            return 75000
        elif "LOW" in income_tier or "<50K" in income_tier or "<$50" in income_tier:
            return 35000
        elif "VERY HIGH" in income_tier or ">150K" in income_tier:
            return 180000
        elif "VERY LOW" in income_tier or "<30K" in income_tier:
            return 25000
        else:
            # Try to extract numeric values
            import re
            numbers = re.findall(r'\d+', income_tier)
            if numbers:
                # Take the first number and assume it's in thousands
                return int(numbers[0]) * 1000
            return 50000  # Default fallback

    def _build_person_data(self, employee_row: pd.Series) -> List[Dict[str, Any]]:
        """
        Build person data structure for CMS API from employee census data.
        
        Args:
            employee_row: Pandas Series containing employee data
            
        Returns:
            List of person dictionaries for CMS API
        """
        people = []
        
        # Main employee
        age = employee_row.get('age', 30)
        if pd.isna(age):
            age = 30
        
        gender = employee_row.get('gender', 'Male')
        if pd.isna(gender):
            gender = 'Male'
        elif str(gender).upper() in ['F', 'FEMALE']:
            gender = 'Female'
        else:
            gender = 'Male'
            
        tobacco_use = employee_row.get('tobacco_use', 'N')
        uses_tobacco = str(tobacco_use).upper() in ['Y', 'YES', 'TRUE', '1']
        
        # Calculate income for APTC eligibility (400% of federal poverty level ≈ $60,000 for individual)
        income = self._map_income_tier_to_amount(employee_row.get('income_tier', 'Medium'))
        aptc_eligible = income <= 400000  # CMS uses annual income
        
        employee_person = {
            "age": int(age),
            "aptc_eligible": aptc_eligible,
            "uses_tobacco": uses_tobacco,
            "gender": gender,
            "is_pregnant": False,  # Default, could be enhanced later
            "is_parent": False,    # Default, could be enhanced later
            "utilization_level": "Low",  # Default
            "dob": f"{2025 - int(age)}-01-01"  # Approximate DOB
        }
        people.append(employee_person)
        
        # Add dependents if available
        dept_count = employee_row.get('dept_count', 0)
        # Convert categorical dept_count to numeric
        dept_count_numeric = self._safe_numeric(dept_count, 0)
        if not pd.isna(dept_count) and dept_count_numeric > 0:
            for i in range(1, int(dept_count_numeric) + 1):
                dept_age_col = f'dept_{i}_age'
                dept_gender_col = f'dept_{i}_gender'
                
                if dept_age_col in employee_row and not pd.isna(employee_row[dept_age_col]):
                    dept_age = int(employee_row[dept_age_col])
                    dept_gender = employee_row.get(dept_gender_col, 'Male')
                    
                    if pd.isna(dept_gender):
                        dept_gender = 'Male'
                    elif str(dept_gender).upper() in ['F', 'FEMALE']:
                        dept_gender = 'Female'
                    else:
                        dept_gender = 'Male'
                    
                    dependent_person = {
                        "age": dept_age,
                        "aptc_eligible": aptc_eligible,
                        "uses_tobacco": False,  # Assume dependents don't use tobacco
                        "gender": dept_gender,
                        "is_pregnant": False,
                        "is_parent": False,
                        "utilization_level": "Low",
                        "dob": f"{2025 - dept_age}-01-01"
                    }
                    people.append(dependent_person)
        
        return people

    def search_plans_for_employee(self, employee_row: pd.Series,
                                  market: str = "Individual",
                                  year: int = 2025,
                                  use_minimal_request: bool = True) -> Dict[str, Any]:
        """
        Search marketplace plans for a specific employee based on their census data.
        
        Args:
            employee_row: Pandas Series containing employee census data
            market: Marketplace type (Individual, Small Group, etc.)
            year: Plan year
            
        Returns:
            Dictionary containing plan search results
        """
        zipcode = str(employee_row.get('zipcode', ''))
        if not zipcode or len(zipcode) != 5 or not zipcode.isdigit():
            return {
                "success": False,
                "error": f"Invalid ZIP code: {zipcode}",
                "employee_id": employee_row.get('employee_id', 'unknown')
            }
        
        # Get income and build household
        income = self._map_income_tier_to_amount(employee_row.get('income_tier', 'Medium'))
        people = self._build_person_data(employee_row)
        
        # Get location info first
        county_response = self.get_location_info(zipcode)
        if not county_response["success"]:
            return {
                "success": False,
                "error": f"Failed to retrieve county data for ZIP {zipcode}: {county_response['error']}",
                "employee_id": employee_row.get('employee_id', 'unknown')
            }

        counties = county_response["data"].get("counties", [])
        if not counties:
            return {
                "success": False,
                "error": f"No county data found for ZIP {zipcode}",
                "employee_id": employee_row.get('employee_id', 'unknown')
            }

        # Process each county (usually just one)
        county_results = []
        for county in counties:
            county_info = {
                "fips": county.get("fips"),
                "name": county.get("name"),
                "state": county.get("state")
            }
            state = county.get("state")

            # Check if the state uses federal marketplace
            if state not in self.FEDERAL_MARKETPLACE_STATES:
                county_results.append({
                    "county": county_info,
                    "plans": {
                        "success": False,
                        "error": f"State {state} uses its own marketplace. Federal marketplace not available.",
                        "status_code": 400
                    }
                })
                continue

            # Build search request
            place = {
                "countyfips": county.get("fips"),
                "state": state,
                "zipcode": zipcode
            }
            
            # Build optimized request data
            if use_minimal_request:
                search_data = self._build_minimal_request(employee_row, county.get("fips"), state, zipcode, income, market, year)
            else:
                search_data = {
                    "household": {
                        "income": income,
                        "people": people,
                        "has_married_couple": employee_row.get('marital_status', '').upper() == 'MARRIED'
                    },
                    "place": place,
                    "market": market,
                    "year": year
                }

            # Make the plan search request
            plan_result = self.make_request(
                "plans/search",
                method="POST",
                data=search_data
            )
            
            county_results.append({
                "county": county_info,
                "plans": plan_result
            })

        return {
            "success": True,
            "employee_id": employee_row.get('employee_id', 'unknown'),
            "zipcode": zipcode,
            "income": income,
            "household_size": len(people),
            "results": county_results
        }







    def _build_minimal_request(self, employee_row: pd.Series, county_fips: str, state: str,
                              zipcode: str, income: int, market: str, year: int) -> Dict[str, Any]:
        """
        Build minimal optimized request based on CMS API research findings.

        Research shows that minimal required fields are:
        - age, income, location, tobacco use, aptc_eligible
        - Gender is optional but may affect results
        - Complex household data not needed for basic plan search
        """
        # Get basic employee data
        age = int(employee_row.get('age', 30))
        uses_tobacco = self._get_tobacco_use(employee_row)
        gender = employee_row.get('gender', 'Male')

        # Build minimal person data
        person_data = {
            "age": age,
            "aptc_eligible": True,  # Always True for subsidies
            "uses_tobacco": uses_tobacco
        }

        # Add gender if available (optional but may help)
        if gender and gender.strip():
            person_data["gender"] = gender

        # Add pregnancy status for females if available
        if gender and gender.lower() == 'female':
            is_pregnant = employee_row.get('is_pregnant', False)
            if is_pregnant:
                person_data["is_pregnant"] = True

        # Add parent status if has dependents
        dept_count = employee_row.get('dept_count', 0)
        dept_count_numeric = self._safe_numeric(dept_count, 0)
        if dept_count and dept_count_numeric > 0:
            person_data["is_parent"] = True

        # Build minimal request
        request_data = {
            "household": {
                "income": income,
                "people": [person_data]
            },
            "place": {
                "countyfips": county_fips,
                "state": state,
                "zipcode": zipcode
            },
            "market": market,
            "year": year
        }

        # Add married couple flag if married (optional)
        marital_status = employee_row.get('marital_status', '')
        if marital_status and marital_status.upper() == 'MARRIED':
            request_data["household"]["has_married_couple"] = True

        return request_data

    def _get_tobacco_use(self, employee_row: pd.Series) -> bool:
        """Extract tobacco use from employee data with fallback logic."""
        # Check various possible tobacco fields
        tobacco_fields = ['uses_tobacco', 'tobacco_use', 'smoker', 'tobacco']

        for field in tobacco_fields:
            value = employee_row.get(field)
            if value is not None:
                if isinstance(value, bool):
                    return value
                elif isinstance(value, str):
                    value_lower = value.lower().strip()
                    if value_lower in ['yes', 'y', 'true', '1', 'often', 'sometimes']:
                        return True
                    elif value_lower in ['no', 'n', 'false', '0', 'never', 'rarely']:
                        return False

        # Default to False if no tobacco data found
        return False



    def extract_detailed_plan_data(self, plan_results: Dict[str, Any], top_n: int = 3) -> List[Dict[str, Any]]:
        """
        Extract detailed plan data for top N plans from API results.

        Args:
            plan_results: Results from search_plans_for_employee
            top_n: Number of top plans to extract (default: 3)

        Returns:
            List of detailed plan dictionaries with essential information
        """
        if not plan_results.get('success', False):
            return []

        all_plans = []
        for county_result in plan_results.get('results', []):
            if not county_result.get('plans', {}).get('success'):
                continue

            plans_data = county_result.get('plans', {}).get('data', {})
            if 'plans' in plans_data:
                all_plans.extend(plans_data['plans'])

        if not all_plans:
            return []

        # Sort plans by premium (ascending) and quality rating (descending)
        def plan_sort_key(plan):
            premium = plan.get('premium', 999999)
            quality = plan.get('quality_rating', {}).get('global_rating', 0)
            return (premium, -quality)  # Lower premium first, higher quality first

        sorted_plans = sorted(all_plans, key=plan_sort_key)[:top_n]

        detailed_plans = []
        for plan in sorted_plans:
            # Extract essential plan information
            plan_data = {
                'id': plan.get('id'),
                'name': plan.get('name'),
                'issuer': {
                    'id': plan.get('issuer', {}).get('id'),
                    'name': plan.get('issuer', {}).get('name'),
                    'phone': plan.get('issuer', {}).get('toll_free'),
                    'website': plan.get('issuer', {}).get('individual_url')
                },
                'premium': plan.get('premium'),
                'premium_with_credit': plan.get('premium_w_credit'),
                'metal_level': plan.get('metal_level'),
                'type': plan.get('type'),
                'hsa_eligible': plan.get('hsa_eligible', False),

                # Cost structure
                'deductible': self._extract_deductible(plan.get('deductibles', [])),
                'max_out_of_pocket': self._extract_moop(plan.get('moops', [])),
                'out_of_pocket_cost': plan.get('oopc'),

                # Network and features
                'has_national_network': plan.get('has_national_network', False),
                'specialist_referral_required': plan.get('specialist_referral_required', False),

                # Quality rating
                'quality_rating': {
                    'global_rating': plan.get('quality_rating', {}).get('global_rating', 0),
                    'available': plan.get('quality_rating', {}).get('available', False)
                },

                # Key benefits
                'key_benefits': self._extract_key_benefits(plan.get('benefits', [])),

                # URLs for more information
                'urls': {
                    'benefits': plan.get('benefits_url'),
                    'brochure': plan.get('brochure_url'),
                    'formulary': plan.get('formulary_url'),
                    'network': plan.get('network_url')
                }
            }

            detailed_plans.append(plan_data)

        return detailed_plans

    def _extract_deductible(self, deductibles: List[Dict]) -> Optional[float]:
        """Extract the primary deductible amount."""
        if not deductibles:
            return None

        # Look for individual in-network deductible first
        for deductible in deductibles:
            if (deductible.get('network_tier') == 'In-Network' and
                deductible.get('individual', False)):
                return deductible.get('amount')

        # Fallback to first deductible
        return deductibles[0].get('amount')

    def _extract_moop(self, moops: List[Dict]) -> Optional[float]:
        """Extract the maximum out-of-pocket amount."""
        if not moops:
            return None

        # Look for individual in-network MOOP first
        for moop in moops:
            if (moop.get('network_tier') == 'In-Network' and
                moop.get('individual', False)):
                return moop.get('amount')

        # Fallback to first MOOP
        return moops[0].get('amount')

    def _extract_key_benefits(self, benefits: List[Dict]) -> Dict[str, str]:
        """Extract key benefits with cost sharing information."""
        key_benefits = {}

        # Map of benefit types we care about
        benefit_mapping = {
            'EMERGENCY_ROOM_SERVICES': 'emergency_room',
            'PRIMARY_CARE_VISIT_TO_TREAT_AN_INJURY_OR_ILLNESS': 'primary_care',
            'SPECIALIST_VISIT': 'specialist_care',
            'GENERIC_DRUGS': 'prescription_drugs',
            'MENTAL_HEALTH_OUTPATIENT_SERVICES': 'mental_health',
            'MATERNITY_CARE': 'maternity_care',
            'PREVENTIVE_CARE': 'preventive_care'
        }

        for benefit in benefits:
            benefit_type = benefit.get('type', '')
            if benefit_type in benefit_mapping:
                key = benefit_mapping[benefit_type]

                # Extract in-network cost sharing
                cost_sharings = benefit.get('cost_sharings', [])
                in_network_cost = 'Not Available'

                for cost_sharing in cost_sharings:
                    if cost_sharing.get('network_tier') == 'In-Network':
                        in_network_cost = cost_sharing.get('display_string', 'Coverage Available')
                        break

                key_benefits[key] = in_network_cost

        return key_benefits
