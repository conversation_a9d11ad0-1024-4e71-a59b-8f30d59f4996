"""
Data Preprocessor Service
Helper methods for data preprocessing operations.
"""

import pandas as pd
import logging
from typing import Dict, List, Any
import time

logger = logging.getLogger(__name__)


class DataPreprocessorService:
    """Service class for data preprocessing helper methods."""
    
    def __init__(self):
        self.system_field_config = self._load_system_field_config()
    
    def _load_system_field_config(self) -> Dict:
        """Load system field configuration."""
        return {
            "core_identity": ["employee_id", "first_name", "middle_name", "last_name", "name"],
            "demographics": ["dob", "gender", "age"],
            "address": ["address1", "address2", "city", "state", "zipcode"],
            "family": ["marital_status", "relationship", "record_type"],
            "employment": ["salary", "employment_type", "employee_class", "department", "hire_date"],
            "health": ["tobacco_use", "pregnancy_status"],
            "dependents": ["dept_count"],
            "benefits": ["medical_plan", "dental_plan", "vision_plan", "life_plan", "add_plan", "coverage_tier"],
            "sensitive": ["ssn"]
        }
    
    def consolidate_name_fields(self, df: pd.DataFrame) -> pd.DataFrame:
        """Consolidate separate name fields into a single 'name' field."""
        logger.info("Consolidating name fields")
        
        df_copy = df.copy()
        
        # If we don't have a 'name' field but have separate name components, create it
        if 'name' not in df_copy.columns:
            name_components = []
            
            for idx in df_copy.index:
                components = []
                
                # Collect name components in order
                for field in ['first_name', 'middle_name', 'last_name']:
                    if field in df_copy.columns:
                        value = df_copy.loc[idx, field]
                        if pd.notna(value) and str(value).strip():
                            components.append(str(value).strip())
                
                # Join components with spaces
                full_name = ' '.join(components) if components else ''
                name_components.append(full_name)
            
            df_copy['name'] = name_components
            logger.info(f"Created 'name' field from separate name components")
        
        return df_copy
    
    def calculate_age_dob_conversions(self, df: pd.DataFrame) -> pd.DataFrame:
        """Calculate missing age from dob or vice versa."""
        logger.info("Calculating age/dob conversions")
        
        df_copy = df.copy()
        current_year = pd.Timestamp.now().year
        
        # If we have dob but no age, calculate age
        if 'dob' in df_copy.columns and 'age' not in df_copy.columns:
            df_copy['age'] = None
            
            for idx in df_copy.index:
                dob_value = df_copy.loc[idx, 'dob']
                if pd.notna(dob_value):
                    try:
                        dob_date = pd.to_datetime(dob_value)
                        age = current_year - dob_date.year
                        df_copy.loc[idx, 'age'] = age
                    except:
                        pass
            
            logger.info("Calculated age from dob")
        
        # If we have age but no dob, estimate dob (approximate)
        elif 'age' in df_copy.columns and 'dob' not in df_copy.columns:
            df_copy['dob'] = None
            
            for idx in df_copy.index:
                age_value = df_copy.loc[idx, 'age']
                if pd.notna(age_value):
                    try:
                        age = int(float(age_value))
                        estimated_birth_year = current_year - age
                        estimated_dob = f"{estimated_birth_year}-01-01"
                        df_copy.loc[idx, 'dob'] = estimated_dob
                    except:
                        pass
            
            logger.info("Estimated dob from age")
        
        return df_copy
    
    def calculate_dept_count(self, df: pd.DataFrame) -> pd.DataFrame:
        """Calculate dept_count based on actual dependent data present in the DataFrame."""
        logger.info("Calculating dept_count based on actual dependent data")
        
        df_copy = df.copy()
        
        # Initialize dept_count column if it doesn't exist
        if 'dept_count' not in df_copy.columns:
            df_copy['dept_count'] = 0
        
        # For each row, count actual dependents
        for idx in df_copy.index:
            dependent_count = 0
            
            # Check for dependent data in numbered columns (dept_1, dept_2, etc.)
            for i in range(1, 21):  # Check up to 20 dependents
                # Check if any dependent field has data
                has_dependent_data = False
                
                for field_suffix in ['', '_name', '_age', '_dob', '_gender']:
                    col_name = f'dept_{i}{field_suffix}'
                    if col_name in df_copy.columns:
                        value = df_copy.loc[idx, col_name]
                        if pd.notna(value) and str(value).strip() != '':
                            has_dependent_data = True
                            break
                
                if has_dependent_data:
                    dependent_count = i  # Set to highest numbered dependent with data
            
            # Update dept_count
            df_copy.loc[idx, 'dept_count'] = dependent_count
        
        # Standardize dept_count to categorical values
        df_copy['dept_count'] = df_copy['dept_count'].apply(self.standardize_count_value)
        
        logger.info(f"Calculated dept_count for {len(df_copy)} records")
        return df_copy
    
    def standardize_count_value(self, value):
        """Standardize count values to categorical format."""
        if pd.isna(value) or value == "":
            return "0"
        
        try:
            count = int(float(str(value)))
            if count <= 0:
                return "0"
            elif count == 1:
                return "1"
            elif count == 2:
                return "2"
            else:
                return "3+"
        except (ValueError, TypeError):
            # Handle non-numeric values
            value_str = str(value).lower().strip()
            if value_str in ["none", "0", "zero"]:
                return "0"
            elif value_str in ["one", "1"]:
                return "1"
            elif value_str in ["two", "2"]:
                return "2"
            elif "3" in value_str or "more" in value_str or "+" in value_str:
                return "3+"
            else:
                return "0"  # Default fallback
    
    def validate_mandatory_fields(self, df: pd.DataFrame) -> Dict:
        """Validate that mandatory fields are present and have data."""
        logger.info("Validating mandatory fields")
        
        mandatory_fields = ['name', 'gender', 'zipcode', 'marital_status']
        missing_fields = []
        empty_fields = []
        
        for field in mandatory_fields:
            if field not in df.columns:
                missing_fields.append(field)
            else:
                # Check if field has any non-null, non-empty values
                non_empty_count = df[field].dropna().astype(str).str.strip().ne('').sum()
                if non_empty_count == 0:
                    empty_fields.append(field)
        
        if missing_fields or empty_fields:
            error_msg = []
            if missing_fields:
                error_msg.append(f"Missing mandatory fields: {missing_fields}")
            if empty_fields:
                error_msg.append(f"Empty mandatory fields: {empty_fields}")
            
            return {
                "success": False,
                "error": "mandatory_fields_missing",
                "message": "; ".join(error_msg),
                "missing_fields": missing_fields,
                "empty_fields": empty_fields
            }
        
        logger.info("All mandatory fields validated successfully")
        return {"success": True}
    
    def add_system_columns_optimized(self, df: pd.DataFrame) -> pd.DataFrame:
        """Add system columns optimized based on actual dept_count values."""
        logger.info("Adding system columns (optimized)")
        
        df_copy = df.copy()
        
        # Define base system columns
        base_system_columns = []
        for category_fields in self.system_field_config.values():
            base_system_columns.extend(category_fields)
        
        # Add missing base columns
        for column in base_system_columns:
            if column not in df_copy.columns:
                df_copy[column] = None
        
        # Determine max dependents needed based on actual dept_count values
        max_dependents_needed = 0
        if 'dept_count' in df_copy.columns:
            for idx in df_copy.index:
                dept_count_val = df_copy.loc[idx, 'dept_count']
                if pd.notna(dept_count_val):
                    try:
                        # Convert categorical dept_count back to number for column creation
                        if str(dept_count_val) == "3+":
                            count = 3
                        else:
                            count = int(float(str(dept_count_val)))
                        max_dependents_needed = max(max_dependents_needed, count)
                    except:
                        pass
        
        # Also check existing dependent columns
        existing_dept_cols = [col for col in df_copy.columns if col.startswith("dept_") and col[5:].split("_")[0].isdigit()]
        if existing_dept_cols:
            dept_numbers = [int(col.split("_")[1]) for col in existing_dept_cols if col.split("_")[1].isdigit()]
            if dept_numbers:
                max_dependents_needed = max(max_dependents_needed, max(dept_numbers))
        
        # Ensure minimum of 3 dependent slots for flexibility
        max_dependents_needed = max(max_dependents_needed, 3)
        
        # Add dependent columns only up to what's needed
        dependent_suffixes = ["", "_dob", "_age", "_gender"]
        
        for i in range(1, max_dependents_needed + 1):
            # Add basic dependent columns
            for suffix in dependent_suffixes:
                col_name = f"dept_{i}{suffix}"
                if col_name not in df_copy.columns:
                    df_copy[col_name] = None
            
            # Add relationship type column
            rel_col_name = f"relationship_type_{i}"
            if rel_col_name not in df_copy.columns:
                df_copy[rel_col_name] = None
        
        logger.info(f"Added system columns with {max_dependents_needed} dependent slots")
        return df_copy
    
    def generate_processing_summary(self, original_df: pd.DataFrame, processed_df: pd.DataFrame, 
                                  validation_result: Dict, processing_time: float) -> Dict:
        """Generate processing summary."""
        return {
            "original_rows": len(original_df),
            "original_columns": len(original_df.columns),
            "processed_rows": len(processed_df),
            "processed_columns": len(processed_df.columns),
            "processing_time": processing_time,
            "validation_passed": validation_result.get("is_valid", True),
            "mandatory_fields_present": all(field in processed_df.columns for field in ['name', 'gender', 'zipcode', 'marital_status']),
            "dept_count_distribution": processed_df['dept_count'].value_counts().to_dict() if 'dept_count' in processed_df.columns else {}
        }
