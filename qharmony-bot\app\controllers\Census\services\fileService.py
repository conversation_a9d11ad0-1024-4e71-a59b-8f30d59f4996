"""
File Service - Handles file upload, validation, and parsing operations.
"""

import pandas as pd
import io
import logging
from typing import Dict, Any, Tuple
from fastapi import UploadFile

logger = logging.getLogger(__name__)


class FileService:
    """Service for handling file operations in census processing."""
    
    SUPPORTED_FORMATS = ['.csv', '.xlsx', '.xls']
    MAX_FILE_SIZE = 50 * 1024 * 1024  # 50MB
    
    async def load_and_validate_file(self, file: UploadFile) -> Dict[str, Any]:
        """Load and validate uploaded file."""
        try:
            # Validate file format
            filename = file.filename or "unknown"
            extension = f".{filename.split('.')[-1].lower()}" if '.' in filename else ""
            
            if extension not in self.SUPPORTED_FORMATS:
                return {
                    "success": False,
                    "error": "unsupported_format",
                    "message": f"Unsupported file format: {extension}. Supported formats: {', '.join(self.SUPPORTED_FORMATS)}"
                }
            
            # Read file content
            content = await file.read()
            
            # Validate file size
            if len(content) > self.MAX_FILE_SIZE:
                return {
                    "success": False,
                    "error": "file_too_large",
                    "message": f"File size exceeds maximum limit of {self.MAX_FILE_SIZE // (1024*1024)}MB"
                }
            
            # Parse file based on extension
            if extension == '.csv':
                df = self._parse_csv(content)
            else:  # Excel files
                df = self._parse_excel(content)
            
            # Validate DataFrame
            if df.empty:
                return {
                    "success": False,
                    "error": "empty_file",
                    "message": "File is empty or contains no valid data"
                }
            
            if len(df.columns) < 4:
                return {
                    "success": False,
                    "error": "insufficient_columns",
                    "message": f"File must have at least 3 columns, found {len(df.columns)}"
                }
            
            if len(df) < 4:
                return {
                    "success": False,
                    "error": "insufficient_rows",
                    "message": f"File must have at least 2 data rows, found {len(df)}"
                }
            
            # Generate file info
            file_info = {
                "filename": filename,
                "size": len(content),
                "rows": len(df),
                "columns": len(df.columns),
                "original_column_names": df.columns.tolist()
            }
            
            logger.info(f"File loaded successfully: {filename} ({len(df)} rows, {len(df.columns)} columns)")
            
            return {
                "success": True,
                "dataframe": df,
                "file_info": file_info
            }
            
        except Exception as e:
            logger.error(f"File loading failed: {str(e)}")
            return {
                "success": False,
                "error": "file_parsing_failed",
                "message": f"Failed to parse file: {str(e)}"
            }
    
    def _parse_csv(self, content: bytes) -> pd.DataFrame:
        """Parse CSV content with multiple encoding attempts."""
        for encoding in ['utf-8', 'latin-1', 'cp1252']:
            try:
                csv_string = content.decode(encoding)
                return pd.read_csv(
                    io.StringIO(csv_string),
                    quoting=1,  # QUOTE_ALL
                    skipinitialspace=True,
                    na_values=['', 'NULL', 'null', 'N/A', 'n/a', 'NA', 'na']
                )
            except UnicodeDecodeError:
                continue
        raise ValueError("Unable to decode CSV file with any supported encoding")
    
    def _parse_excel(self, content: bytes) -> pd.DataFrame:
        """Parse Excel content."""
        return pd.read_excel(io.BytesIO(content))
    
    def validate_data_structure(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Perform basic data structure validation."""
        validation_results = {
            "is_valid": True,
            "warnings": [],
            "errors": [],
            "recommendations": []
        }
        
        # Check for completely empty columns
        empty_columns = df.columns[df.isnull().all()].tolist()
        if empty_columns:
            validation_results["warnings"].append(f"Empty columns found: {empty_columns}")
        
        # Check for duplicate column names
        duplicate_columns = df.columns[df.columns.duplicated()].tolist()
        if duplicate_columns:
            validation_results["errors"].append(f"Duplicate column names: {duplicate_columns}")
            validation_results["is_valid"] = False
        
        # Check data types
        numeric_columns = df.select_dtypes(include=['number']).columns.tolist()
        text_columns = df.select_dtypes(include=['object']).columns.tolist()
        
        if len(numeric_columns) == 0:
            validation_results["warnings"].append("No numeric columns detected")
        
        if len(text_columns) == 0:
            validation_results["warnings"].append("No text columns detected")
        
        # Check for reasonable data distribution
        if len(df) > 5000:
            validation_results["recommendations"].append("Large dataset detected - consider processing in batches")
        
        return validation_results
