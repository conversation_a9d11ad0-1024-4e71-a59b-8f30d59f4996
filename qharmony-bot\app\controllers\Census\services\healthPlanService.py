"""
Health Plan Service for Census Processing

Service that integrates Health.gov CMS API data with census processing pipeline.
This service acts as step 6 in the pipeline, between enrichment and prediction.
"""

import pandas as pd
import logging
from typing import Dict, List, Any
import json

from .cmsApiService import CMSAPIService

logger = logging.getLogger(__name__)


class HealthPlanService:
    """
    Service for integrating Health.gov marketplace plans into census processing.
    
    This service:
    1. Takes enriched employee data from step 5
    2. Calls CMS API to fetch real marketplace plans
    3. Enriches employee records with plan options
    4. Prepares data for step 7 (model prediction)
    """
    
    def __init__(self, api_key: str = None):
        """Initialize the health plan service."""
        try:
            self.cms_api = CMSAPIService(api_key=api_key)
            self.api_available = True
            logger.info("CMS API service initialized successfully")
        except Exception as e:
            logger.warning(f"CMS API service initialization failed: {e}")
            self.cms_api = None
            self.api_available = False
    



    def enrich_dataframe_with_detailed_plans(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Enrich dataframe with comprehensive health plan information for each employee.
        This method extracts detailed plan data including top recommendations and benefits.
        """
        try:
            # Enhanced health plan columns for individual employee data
            enhanced_plan_columns = [
                # Basic Plan Availability
                'marketplace_plans_available',
                'plan_count',
                'available_plan_types',
                'available_metal_levels',
                'estimated_premium_range',
                'marketplace_state_supported',

                # Top Recommended Plan (based on ML prediction)
                'recommended_plan_id',
                'recommended_plan_name',
                'recommended_plan_issuer',
                'recommended_plan_premium',
                'recommended_plan_metal_level',
                'recommended_plan_type',
                'recommended_plan_deductible',
                'recommended_plan_moop',
                'recommended_plan_hsa_eligible',
                'recommended_plan_quality_rating',

                # Top 3 Plan Options (JSON format)
                'top_3_available_plans',

                # Key Benefits Coverage (for recommended plan)
                'benefits_emergency_room',
                'benefits_primary_care',
                'benefits_specialist_care',
                'benefits_prescription_drugs',
                'benefits_mental_health',
                'benefits_maternity_care',
                'benefits_preventive_care',

                # Network Information
                'network_has_national',
                'network_referral_required',

                # API Processing Info
                'api_processing_status',
                'api_error_message'
            ]

            # Initialize all columns
            for col in enhanced_plan_columns:
                if col not in df.columns:
                    df[col] = None

            # Process each employee with detailed plan extraction
            for idx, row in df.iterrows():
                employee_id = row.get('employee_id', f'employee_{idx}')
                predicted_plan_type = row.get('predicted_plan_type')

                try:
                    # Get detailed plans for this employee
                    detailed_plans = self._get_detailed_employee_plans(row, predicted_plan_type)

                    if detailed_plans['success'] and detailed_plans['plans']:
                        # Extract top recommended plan (already detailed format)
                        top_plan = detailed_plans['plans'][0]  # First plan is best match

                        # Populate recommended plan data
                        df.at[idx, 'recommended_plan_id'] = top_plan.get('id')
                        df.at[idx, 'recommended_plan_name'] = top_plan.get('name')
                        df.at[idx, 'recommended_plan_issuer'] = top_plan.get('issuer', {}).get('name')
                        df.at[idx, 'recommended_plan_premium'] = top_plan.get('premium')
                        df.at[idx, 'recommended_plan_metal_level'] = top_plan.get('metal_level')
                        df.at[idx, 'recommended_plan_type'] = top_plan.get('type')
                        df.at[idx, 'recommended_plan_hsa_eligible'] = top_plan.get('hsa_eligible')
                        df.at[idx, 'recommended_plan_quality_rating'] = top_plan.get('quality_rating', {}).get('global_rating')

                        # Extract cost information (already processed)
                        df.at[idx, 'recommended_plan_deductible'] = top_plan.get('deductible')
                        df.at[idx, 'recommended_plan_moop'] = top_plan.get('max_out_of_pocket')

                        # Extract network information
                        df.at[idx, 'network_has_national'] = top_plan.get('has_national_network')
                        df.at[idx, 'network_referral_required'] = top_plan.get('specialist_referral_required')

                        # Extract key benefits (already processed)
                        key_benefits = top_plan.get('key_benefits', {})
                        for benefit_type, coverage in key_benefits.items():
                            df.at[idx, f'benefits_{benefit_type}'] = coverage

                        # Store top 3 plans as JSON (already in detailed format)
                        top_3_plans = self._format_detailed_top_3_plans(detailed_plans['plans'][:3])
                        df.at[idx, 'top_3_available_plans'] = str(top_3_plans)

                        # Update basic availability info
                        df.at[idx, 'marketplace_plans_available'] = True
                        df.at[idx, 'plan_count'] = len(detailed_plans['plans'])
                        df.at[idx, 'api_processing_status'] = 'success'

                    else:
                        # Handle no plans found or API error
                        df.at[idx, 'marketplace_plans_available'] = False
                        df.at[idx, 'plan_count'] = 0
                        df.at[idx, 'api_processing_status'] = 'no_plans_found'
                        df.at[idx, 'api_error_message'] = detailed_plans.get('error', 'No plans available')

                except Exception as e:
                    logger.error(f"Error processing detailed plans for {employee_id}: {str(e)}")
                    df.at[idx, 'api_processing_status'] = 'error'
                    df.at[idx, 'api_error_message'] = str(e)

            logger.info(f"DataFrame enriched with detailed plan information for {len(df)} employees")
            return df

        except Exception as e:
            logger.error(f"Error in detailed plan enrichment: {str(e)}")
            return df

    def _get_detailed_employee_plans(self, employee_row: pd.Series, predicted_plan_type: str) -> Dict[str, Any]:
        """Get detailed plan information for a specific employee using enhanced CMS API extraction."""
        try:
            # Search for plans using CMS API
            plan_result = self.cms_api.search_plans_for_employee(employee_row)

            if not plan_result['success']:
                return {
                    'success': False,
                    'error': plan_result.get('error', 'API call failed'),
                    'plans': []
                }

            # Use enhanced detailed plan extraction
            detailed_plans = self.cms_api.extract_detailed_plan_data(plan_result, top_n=3)

            if not detailed_plans:
                return {
                    'success': True,
                    'plans': [],
                    'message': 'No plans found for this location'
                }

            # Filter plans based on ML prediction if available
            if predicted_plan_type:
                filtered_plans = self._filter_detailed_plans_by_prediction(detailed_plans, predicted_plan_type)
                if filtered_plans:
                    detailed_plans = filtered_plans

            # Rank plans by relevance to employee profile
            ranked_plans = self._rank_detailed_plans_by_relevance(detailed_plans, employee_row)

            return {
                'success': True,
                'plans': ranked_plans,
                'total_found': len(detailed_plans)
            }

        except Exception as e:
            logger.error(f"Error getting detailed employee plans: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'plans': []
            }

    def _filter_detailed_plans_by_prediction(self, plans: List[Dict], predicted_plan_type: str) -> List[Dict]:
        """Filter detailed plans based on ML prediction."""
        if not predicted_plan_type:
            return plans

        # Map prediction to plan type
        prediction_mapping = {
            'PPO': 'PPO',
            'HMO': 'HMO',
            'EPO': 'EPO',
            'POS': 'POS',
            'HDHP + HSA': ['HDHP', 'HSA'],
            'HDHP': 'HDHP'
        }

        target_types = prediction_mapping.get(predicted_plan_type, [predicted_plan_type])
        if isinstance(target_types, str):
            target_types = [target_types]

        filtered = []
        for plan in plans:
            plan_type = plan.get('type', '').upper()
            hsa_eligible = plan.get('hsa_eligible', False)

            # Check plan type match
            type_match = any(target_type.upper() in plan_type for target_type in target_types)

            # Special handling for HSA plans
            if 'HSA' in predicted_plan_type.upper() and hsa_eligible:
                type_match = True

            if type_match:
                filtered.append(plan)

        return filtered if filtered else plans  # Return all if no matches

    def _rank_detailed_plans_by_relevance(self, plans: List[Dict], employee_row: pd.Series) -> List[Dict]:
        """Enhanced ranking of detailed plans by relevance to employee profile."""

        def get_preferred_metal_levels(income_tier: str) -> List[str]:
            """Get preferred metal levels based on income tier."""
            preferences = {
                "High (>$100K)": ["PLATINUM", "GOLD", "SILVER", "BRONZE"],
                "Medium ($50K–$100K)": ["GOLD", "SILVER", "BRONZE", "PLATINUM"],
                "Low (<$50K)": ["BRONZE", "SILVER", "GOLD", "PLATINUM"]
            }
            return preferences.get(income_tier, ["SILVER", "BRONZE", "GOLD", "PLATINUM"])

        def plan_score(plan):
            score = 0

            # Plan type match bonus (highest priority)
            predicted_type = employee_row.get('predicted_plan_type', '')
            plan_type = plan.get('type', '')
            if predicted_type.upper() == plan_type.upper():
                score += 200
            elif predicted_type == "HDHP + HSA" and (plan_type.upper() == "HDHP" or plan.get('hsa_eligible', False)):
                score += 180  # Close match for HSA plans

            # Metal level preference (income-based)
            income_tier = employee_row.get('income_tier', '')
            preferred_metals = get_preferred_metal_levels(income_tier)
            metal_level = plan.get('metal_level', '').upper()

            if metal_level in preferred_metals:
                metal_index = preferred_metals.index(metal_level)
                score += (4 - metal_index) * 50  # Higher score for more preferred metals

            # Premium affordability (normalized by income)
            premium = plan.get('premium', 999999)
            if 'HIGH' in income_tier.upper():
                # High earners can afford higher premiums for better coverage
                if premium < 800:
                    score += 40
                elif premium < 1200:
                    score += 30
            elif 'MEDIUM' in income_tier.upper():
                # Medium earners prefer moderate premiums
                if premium < 500:
                    score += 40
                elif premium < 700:
                    score += 30
                elif premium < 900:
                    score += 20
            else:  # Low income
                # Low earners need affordable premiums
                if premium < 300:
                    score += 40
                elif premium < 500:
                    score += 30
                elif premium < 600:
                    score += 10

            # Quality rating score
            quality = plan.get('quality_rating', {}).get('global_rating', 0)
            score += quality * 20  # Increased weight for quality

            # HSA eligibility bonus for high earners
            if 'HIGH' in income_tier.upper() and plan.get('hsa_eligible'):
                score += 100  # Increased HSA bonus for high earners

            # Deductible preference (enhanced family considerations)
            dept_count = employee_row.get('dept_count', 0)
            deductible = plan.get('deductible', 999999)

            if dept_count > 0:  # Has dependents
                if deductible < 2000:
                    score += 80  # Very low deductible great for families
                elif deductible < 4000:
                    score += 60  # Moderate deductible acceptable
                elif deductible < 6000:
                    score += 30  # Higher deductible less ideal
                # High deductibles get no bonus for families
            else:  # No dependents
                if deductible < 3000:
                    score += 40  # Low deductible nice but not critical
                elif deductible < 6000:
                    score += 30  # Moderate deductible acceptable
                elif deductible < 10000:
                    score += 20  # Higher deductible OK for singles

            # Out-of-pocket maximum preference
            max_oop = plan.get('max_out_of_pocket', 999999)
            if max_oop < 5000:
                score += 30
            elif max_oop < 8000:
                score += 20
            elif max_oop < 10000:
                score += 10

            # Network preference (enhanced)
            if plan.get('has_national_network'):
                score += 25  # Increased network bonus

            # Age-based preferences
            age = employee_row.get('age', 30)
            if age > 50:
                # Older employees may prefer comprehensive coverage
                if metal_level in ['GOLD', 'PLATINUM']:
                    score += 30
            elif age < 35:
                # Younger employees may prefer lower premiums
                if metal_level in ['BRONZE', 'SILVER'] and premium < 400:
                    score += 20

            return score

        return sorted(plans, key=plan_score, reverse=True)

    def _extract_key_benefits(self, benefits: List[Dict]) -> Dict[str, str]:
        """Extract key benefits information from plan benefits."""
        key_benefits = {
            'emergency_room': 'Not Available',
            'primary_care': 'Not Available',
            'specialist_care': 'Not Available',
            'prescription_drugs': 'Not Available',
            'mental_health': 'Not Available',
            'maternity_care': 'Not Available',
            'preventive_care': 'Not Available'
        }

        # Map benefit types to our key categories
        benefit_mapping = {
            'EMERGENCY_ROOM_SERVICES': 'emergency_room',
            'PRIMARY_CARE_VISIT_TO_TREAT_AN_INJURY_OR_ILLNESS': 'primary_care',
            'SPECIALIST_VISIT': 'specialist_care',
            'GENERIC_DRUGS': 'prescription_drugs',
            'MENTAL_HEALTH_OUTPATIENT_SERVICES': 'mental_health',
            'MATERNITY_CARE': 'maternity_care',
            'PREVENTIVE_CARE': 'preventive_care'
        }

        for benefit in benefits:
            benefit_type = benefit.get('type', '')
            if benefit_type in benefit_mapping:
                key = benefit_mapping[benefit_type]

                # Extract cost sharing information
                cost_sharings = benefit.get('cost_sharings', [])
                if cost_sharings:
                    # Get in-network cost sharing
                    in_network = next((cs for cs in cost_sharings if cs.get('network_tier') == 'In-Network'), cost_sharings[0])
                    display_string = in_network.get('display_string', 'Coverage Available')
                    key_benefits[key] = display_string
                else:
                    key_benefits[key] = 'Coverage Available' if benefit.get('covered') else 'Not Covered'

        return key_benefits

    def _format_detailed_top_3_plans(self, plans: List[Dict]) -> List[Dict]:
        """Format top 3 detailed plans for JSON storage."""
        formatted_plans = []

        for plan in plans[:3]:
            formatted_plan = {
                'id': plan.get('id'),
                'name': plan.get('name'),
                'issuer': plan.get('issuer', {}).get('name'),
                'premium': plan.get('premium'),
                'premium_with_credit': plan.get('premium_with_credit'),
                'metal_level': plan.get('metal_level'),
                'type': plan.get('type'),
                'deductible': plan.get('deductible'),
                'max_out_of_pocket': plan.get('max_out_of_pocket'),
                'hsa_eligible': plan.get('hsa_eligible'),
                'quality_rating': plan.get('quality_rating', {}).get('global_rating'),
                'has_national_network': plan.get('has_national_network'),
                'key_benefits': plan.get('key_benefits', {}),
                'issuer_phone': plan.get('issuer', {}).get('phone'),
                'urls': plan.get('urls', {})
            }
            formatted_plans.append(formatted_plan)

        return formatted_plans


    

    

    

    

