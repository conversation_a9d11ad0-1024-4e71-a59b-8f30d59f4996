"""
LLM Response Processor Service

Centralized service for processing and parsing LLM responses with robust error handling.
Handles malformed JSON, extracts valid content, and provides detailed parsing information.
"""

import json
import logging
import re
from typing import Dict, Any, Optional, List, Tuple
from dataclasses import dataclass

logger = logging.getLogger(__name__)


@dataclass
class LLMParsingResult:
    """Result of LLM response parsing with detailed information."""
    success: bool
    parsed_data: Optional[Dict[str, Any]]
    original_response: str
    cleaned_response: str
    parsing_method: str
    skipped_lines: List[str]
    error_message: Optional[str]
    confidence_score: float


class LLMResponseProcessor:
    """
    Centralized processor for handling LLM responses with robust JSON parsing.
    
    Features:
    - Multiple parsing strategies
    - Malformed JSON recovery
    - Content filtering and cleaning
    - Detailed parsing information retention
    - Confidence scoring
    """
    
    def __init__(self):
        self.parsing_strategies = [
            self._strategy_direct_json,
            self._strategy_code_block_extraction,
            self._strategy_brace_boundary_extraction,
            self._strategy_line_by_line_cleaning,
            self._strategy_regex_extraction
        ]
    
    def process_llm_response(self, response_text: str, expected_keys: Optional[List[str]] = None) -> LLMParsingResult:
        """
        Process LLM response with multiple parsing strategies.
        
        Args:
            response_text: Raw LLM response text
            expected_keys: Optional list of expected keys for validation
            
        Returns:
            LLMParsingResult with parsing details and extracted data
        """
        logger.info("Processing LLM response with robust parsing")
        
        if not response_text or not response_text.strip():
            return LLMParsingResult(
                success=False,
                parsed_data=None,
                original_response=response_text,
                cleaned_response="",
                parsing_method="empty_response",
                skipped_lines=[],
                error_message="Empty or null response",
                confidence_score=0.0
            )
        
        original_response = response_text
        skipped_lines = []
        
        # Try each parsing strategy in order
        for i, strategy in enumerate(self.parsing_strategies):
            try:
                result = strategy(response_text)
                if result['success']:
                    parsed_data = result['data']
                    
                    # Validate expected keys if provided
                    confidence = self._calculate_confidence_score(
                        parsed_data, expected_keys, result.get('skipped_lines', [])
                    )
                    
                    return LLMParsingResult(
                        success=True,
                        parsed_data=parsed_data,
                        original_response=original_response,
                        cleaned_response=result['cleaned_text'],
                        parsing_method=f"strategy_{i+1}_{strategy.__name__}",
                        skipped_lines=result.get('skipped_lines', []),
                        error_message=None,
                        confidence_score=confidence
                    )
                    
            except Exception as e:
                logger.debug(f"Strategy {strategy.__name__} failed: {str(e)}")
                continue
        
        # All strategies failed
        return LLMParsingResult(
            success=False,
            parsed_data=None,
            original_response=original_response,
            cleaned_response=response_text,
            parsing_method="all_strategies_failed",
            skipped_lines=skipped_lines,
            error_message="All parsing strategies failed",
            confidence_score=0.0
        )
    
    def _strategy_direct_json(self, text: str) -> Dict[str, Any]:
        """Strategy 1: Direct JSON parsing."""
        cleaned_text = text.strip()
        data = json.loads(cleaned_text)
        return {
            'success': True,
            'data': data,
            'cleaned_text': cleaned_text,
            'skipped_lines': []
        }
    
    def _strategy_code_block_extraction(self, text: str) -> Dict[str, Any]:
        """Strategy 2: Extract JSON from code blocks (```json or ```)."""
        cleaned_text = text.strip()
        
        # Try ```json first
        if "```json" in cleaned_text:
            start_marker = "```json"
            end_marker = "```"
            start_idx = cleaned_text.find(start_marker) + len(start_marker)
            end_idx = cleaned_text.find(end_marker, start_idx)
            if end_idx > start_idx:
                json_content = cleaned_text[start_idx:end_idx].strip()
        # Try generic ``` blocks
        elif cleaned_text.startswith("```") and cleaned_text.count("```") >= 2:
            parts = cleaned_text.split("```")
            if len(parts) >= 3:
                json_content = parts[1].strip()
            else:
                raise ValueError("Invalid code block format")
        else:
            raise ValueError("No code blocks found")
        
        data = json.loads(json_content)
        return {
            'success': True,
            'data': data,
            'cleaned_text': json_content,
            'skipped_lines': []
        }
    
    def _strategy_brace_boundary_extraction(self, text: str) -> Dict[str, Any]:
        """Strategy 3: Extract JSON by finding matching braces."""
        start_idx = text.find('{')
        if start_idx == -1:
            raise ValueError("No opening brace found")
        
        # Find matching closing brace
        brace_count = 0
        end_idx = start_idx
        
        for i, char in enumerate(text[start_idx:], start_idx):
            if char == '{':
                brace_count += 1
            elif char == '}':
                brace_count -= 1
                if brace_count == 0:
                    end_idx = i + 1
                    break
        
        if brace_count != 0:
            raise ValueError("Unmatched braces")
        
        json_content = text[start_idx:end_idx]
        data = json.loads(json_content)
        
        return {
            'success': True,
            'data': data,
            'cleaned_text': json_content,
            'skipped_lines': []
        }
    
    def _strategy_line_by_line_cleaning(self, text: str) -> Dict[str, Any]:
        """Strategy 4: Clean line by line, removing non-JSON content."""
        # First try to find JSON boundaries
        start_idx = text.find('{')
        if start_idx == -1:
            raise ValueError("No JSON structure found")
        
        # Extract potential JSON section
        brace_count = 0
        end_idx = start_idx
        for i, char in enumerate(text[start_idx:], start_idx):
            if char == '{':
                brace_count += 1
            elif char == '}':
                brace_count -= 1
                if brace_count == 0:
                    end_idx = i + 1
                    break
        
        if end_idx <= start_idx:
            raise ValueError("Invalid JSON boundaries")
        
        potential_json = text[start_idx:end_idx]
        lines = potential_json.split('\n')
        cleaned_lines = []
        skipped_lines = []
        
        for line in lines:
            stripped = line.strip()
            # Keep lines that look like JSON structure
            if self._is_json_like_line(stripped):
                cleaned_lines.append(line)
            elif stripped:  # Non-empty line that doesn't look like JSON
                skipped_lines.append(stripped)
                logger.debug(f"Skipping non-JSON line: {stripped}")
        
        cleaned_text = '\n'.join(cleaned_lines)
        data = json.loads(cleaned_text)
        
        return {
            'success': True,
            'data': data,
            'cleaned_text': cleaned_text,
            'skipped_lines': skipped_lines
        }
    
    def _strategy_regex_extraction(self, text: str) -> Dict[str, Any]:
        """Strategy 5: Use regex to extract JSON-like patterns."""
        # Pattern to match JSON objects
        json_pattern = r'\{[^{}]*(?:\{[^{}]*\}[^{}]*)*\}'
        matches = re.findall(json_pattern, text, re.DOTALL)
        
        if not matches:
            raise ValueError("No JSON patterns found")
        
        # Try each match
        for match in matches:
            try:
                data = json.loads(match)
                return {
                    'success': True,
                    'data': data,
                    'cleaned_text': match,
                    'skipped_lines': []
                }
            except json.JSONDecodeError:
                continue
        
        raise ValueError("No valid JSON found in regex matches")
    
    def _is_json_like_line(self, line: str) -> bool:
        """Check if a line looks like it belongs to JSON structure."""
        if not line:
            return True  # Empty lines are OK
        
        json_indicators = [
            line.startswith('{'),
            line.startswith('}'),
            line.startswith('"'),
            line.endswith(','),
            line.endswith('{'),
            line.endswith('}'),
            '"' in line and ':' in line,
            line in ['{', '}', ',']
        ]
        
        return any(json_indicators)
    
    def _calculate_confidence_score(self, parsed_data: Dict[str, Any], 
                                  expected_keys: Optional[List[str]], 
                                  skipped_lines: List[str]) -> float:
        """Calculate confidence score for parsed data."""
        if not parsed_data:
            return 0.0
        
        score = 0.8  # Base score for successful parsing
        
        # Bonus for expected keys presence
        if expected_keys:
            present_keys = sum(1 for key in expected_keys if key in parsed_data)
            key_score = present_keys / len(expected_keys) * 0.2
            score += key_score
        
        # Penalty for skipped lines (indicates malformed response)
        if skipped_lines:
            penalty = min(len(skipped_lines) * 0.05, 0.3)
            score -= penalty
        
        # Bonus for well-structured data
        if isinstance(parsed_data, dict) and len(parsed_data) > 0:
            structure_bonus = 0.1
            score += structure_bonus
        
        return min(1.0, max(0.0, score))
    
    def get_parsing_statistics(self, results: List[LLMParsingResult]) -> Dict[str, Any]:
        """Get statistics from multiple parsing results."""
        if not results:
            return {}
        
        total = len(results)
        successful = sum(1 for r in results if r.success)
        
        methods_used = {}
        for result in results:
            method = result.parsing_method
            methods_used[method] = methods_used.get(method, 0) + 1
        
        avg_confidence = sum(r.confidence_score for r in results) / total
        total_skipped_lines = sum(len(r.skipped_lines) for r in results)
        
        return {
            'total_requests': total,
            'successful_parses': successful,
            'success_rate': successful / total,
            'average_confidence': avg_confidence,
            'methods_used': methods_used,
            'total_skipped_lines': total_skipped_lines,
            'most_common_method': max(methods_used.items(), key=lambda x: x[1])[0] if methods_used else None
        }
