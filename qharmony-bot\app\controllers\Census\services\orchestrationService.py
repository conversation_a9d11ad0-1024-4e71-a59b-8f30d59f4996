"""
Clean Orchestration Service - Streamlined 6-step census processing pipeline.
"""

import logging
import pandas as pd
from typing import Dict, Any, List
from fastapi import UploadFile

from .fileService import FileService
from .validationService import ValidationService
from .healthPlanService import HealthPlanService
from .responseService import ResponseService
from ..universalFieldMapper import Universal<PERSON><PERSON><PERSON>apper
from ..patternIdentifier import PatternIdentifier
from ..dataPreprocessor import DataPreprocessor
from ..dataEnrichment import DataEnrichmentEngine
from ..modelPredictor import CensusModelPredictor
from ..dataModels import PatternType

logger = logging.getLogger(__name__)


class OrchestrationService:
    """Clean service that orchestrates the 6-step census processing pipeline."""
    
    def __init__(self):
        # Initialize core processing components
        self.file_service = FileService()
        self.universal_mapper = UniversalFieldMapper()
        self.pattern_identifier = PatternIdentifier()
        self.data_preprocessor = DataPreprocessor()
        self.enrichment_engine = DataEnrichmentEngine()
        self.health_plan_service = HealthPlanService()
        self.model_predictor = CensusModelPredictor(models_path="app/models/Census")
        
        logger.info("OrchestrationService initialized with 6-step pipeline")

    async def process_census_file(self, file: UploadFile, return_dataframe: bool = True) -> Dict[str, Any]:
        """
        Main orchestration method for 6-step census file processing.
        
        Args:
            file: Uploaded census file
            return_dataframe: If False, excludes enriched CSV data from response (default: True)
        
        Returns:
            Standardized response matching documentation format
        """
        try:
            # Step 1: File Parsing & Validation
            file_result = await self.file_service.load_and_validate_file(file)
            if not file_result["success"]:
                return ResponseService.error_response(
                    error_code="file_processing_failed",
                    message=file_result.get("message", "File processing failed"),
                    status_code=400,
                    details=file_result
                )
            
            # Step 2: Universal Field Mapping
            mapping_result = await self.universal_mapper.map_all_fields(
                file_result["dataframe"].columns.tolist(),
                file_result["dataframe"].head(10)
            )
            if not mapping_result.get("success"):
                return ResponseService.error_response(
                    error_code="field_mapping_failed",
                    message="Field mapping failed",
                    status_code=422,
                    details=mapping_result
                )
            
            # Step 3: Pattern Identification
            # Apply mapping to get mapped DataFrame
            mapped_df = self.universal_mapper.apply_mapping_to_dataframe(
                file_result["dataframe"], mapping_result["mapping"]
            )
            pattern_type, pattern_info = self.pattern_identifier.identify_pattern(mapped_df)
            
            # Step 4: Data Preprocessing & Standardization
            identity_mapping = {col: col for col in mapped_df.columns}
            preprocessing_result = await self.data_preprocessor.preprocess_data(
                mapped_df, identity_mapping, pattern_type.value
            )
            if not preprocessing_result.get("success"):
                return ResponseService.error_response(
                    error_code="preprocessing_failed",
                    message="Data preprocessing failed",
                    status_code=422,
                    details=preprocessing_result
                )
            
            # Step 5-6: Enrichment, Prediction & Health Plan Integration
            enrichment_result = await self._process_enrichment_and_prediction(
                preprocessing_result["preprocessed_data"], pattern_type, mapping_result, preprocessing_result
            )
            
            # Check if we have enriched data
            if "enriched_data" not in enrichment_result or enrichment_result["enriched_data"] is None:
                return ResponseService.error_response(
                    error_code="enrichment_failed",
                    message="Data enrichment and prediction failed",
                    status_code=422,
                    details={
                        "step": "enrichment_and_prediction",
                        "available_keys": list(enrichment_result.keys())
                    }
                )
            
            # Add processing metadata
            enrichment_result["file_info"] = file_result.get("file_info", {})
            enrichment_result["pattern_info"] = pattern_info
            enrichment_result["mapping_result"] = mapping_result
            enrichment_result["preprocessing_result"] = preprocessing_result
            enrichment_result["return_dataframe"] = return_dataframe
            
            logger.info("Census file processing completed successfully")
            return ResponseService.success_response(enrichment_result)
            
        except Exception as e:
            logger.error(f"Orchestration error: {str(e)}")
            return ResponseService.error_response(
                error_code="internal_processing_error",
                message=f"Internal processing error: {str(e)}",
                status_code=500
            )

    async def _process_enrichment_and_prediction(self, preprocessed_df: pd.DataFrame,
                                               pattern_type: PatternType, mapping_result: Dict,
                                               preprocessing_result: Dict) -> Dict[str, Any]:
        """Process data enrichment, model prediction, and health plan integration."""
        logger.info("Step 5-6: Data enrichment, model prediction, and health plan integration")

        try:
            # Step 5a: Data Enrichment
            enriched_df, comprehensive_stats = self.enrichment_engine.enrich_dataframe(preprocessed_df)
            if enriched_df is None:
                logger.error("Data enrichment failed")
                return {"enriched_data": None, "error": "enrichment_failed"}
            
            # Step 5b: Model Prediction
            # First predict plan types
            plan_predicted_df = self.model_predictor.predict_plan_types(enriched_df)

            # Then predict benefits
            predicted_df = self.model_predictor.predict_benefits(plan_predicted_df)

            # Generate prediction summary
            prediction_summary = self._generate_prediction_summary(predicted_df)
            prediction_result = {
                "success": True,
                "predicted_data": predicted_df,
                "prediction_summary": prediction_summary,
                "prediction_method": "ml_models" if self.model_predictor.models_loaded else "rule_based"
            }

            # Step 6: Health Plan Integration (OPTIMIZED - no redundant API calls)
            final_df = self.health_plan_service.enrich_dataframe_with_detailed_plans(predicted_df)

            # Extract statistics from the enrichment process (no additional API calls)
            health_plan_result = {
                "success": True,
                "plan_statistics": self._extract_plan_statistics_from_dataframe(final_df),
                "processing_notes": self._extract_processing_notes_from_dataframe(final_df)
            }

            # Generate enrichment summary
            enrichment_summary = {
                "total_employees": len(preprocessed_df),
                "features_analyzed": len(self.enrichment_engine.required_features),
                "data_quality_improvement": {
                    "overall_completion_rate": 100.0,  # Placeholder
                    "total_enriched": len(predicted_df.columns) - len(preprocessed_df.columns)
                }
            }

            # Combine all results using ResponseService format
            combined_data = {
                "enriched_data": final_df,
                "individual_employee_data": self._extract_individual_employee_data(final_df),
                "comprehensive_statistics": comprehensive_stats,
                "enrichment_summary": enrichment_summary,
                "prediction_summary": prediction_result.get("prediction_summary", {}),
                "model_predictions": prediction_result.get("model_predictions", {}),
                "feature_validation": prediction_result.get("feature_validation", {}),
                "plan_statistics": health_plan_result.get("plan_statistics", {}),
                "health_plan_processing_notes": health_plan_result.get("processing_notes", []),
                "health_plan_errors": self._extract_health_plan_errors(health_plan_result),
                "metadata": {
                    "pipeline_version": "2.0_with_health_plans",
                    "total_steps": 6,
                    "steps_completed": ["parsing", "mapping", "pattern_identification", "preprocessing", "enrichment_prediction", "health_plan_integration"],
                    "prediction_method": prediction_result.get("prediction_method", "unknown"),
                    "health_plan_integration_success": health_plan_result.get("success", False),
                    "final_dataframe_shape": [len(final_df), len(final_df.columns)]
                }
            }

            return combined_data

        except Exception as e:
            logger.error(f"Enrichment and prediction error: {str(e)}")
            return {"enriched_data": None, "error": str(e)}
    
    def _extract_health_plan_errors(self, health_plan_result: Dict[str, Any]) -> Dict[str, Any]:
        """Extract health plan error details."""
        if health_plan_result.get("success", False):
            return {}
        
        return {
            "error_code": health_plan_result.get("error", "unknown_error"),
            "error_message": health_plan_result.get("message", "Health plan integration failed"),
            "fallback_applied": health_plan_result.get("fallback_applied", False)
        }

    def _extract_individual_employee_data(self, df: pd.DataFrame) -> List[Dict[str, Any]]:
        """Extract structured individual employee data for API response."""
        individual_data = []

        for _, row in df.iterrows():
            employee_data = {
                # Basic employee information
                "employee_id": row.get("employee_id"),
                "name": row.get("name"),
                "age": row.get("age"),
                "gender": row.get("gender"),
                "marital_status": row.get("marital_status"),
                "zipcode": row.get("zipcode"),
                "city": row.get("city"),
                "state": row.get("state"),
                "income_tier": row.get("income_tier"),
                "employment_type": row.get("employment_type"),
                "dept_count": row.get("dept_count"),
                
                # ML Predictions
                "predicted_plan_type": row.get("predicted_plan_type"),
                "plan_confidence": row.get("plan_confidence"),
                "plan_reason": row.get("plan_reason"),
                "predicted_benefits": row.get("predicted_benefits"),
                "benefits_confidence": row.get("benefits_confidence"),
                "benefits_reason": row.get("benefits_reason"),
                
                # Health Plan Recommendations
                "marketplace_plans_available": row.get("marketplace_plans_available", False),
                "plan_count": row.get("plan_count", 0),
                "recommended_plan": {
                    "id": row.get("recommended_plan_id"),
                    "name": row.get("recommended_plan_name"),
                    "issuer": row.get("recommended_plan_issuer"),
                    "premium": row.get("recommended_plan_premium"),
                    "metal_level": row.get("recommended_plan_metal_level"),
                    "type": row.get("recommended_plan_type"),
                    "deductible": row.get("recommended_plan_deductible"),
                    "max_out_of_pocket": row.get("recommended_plan_moop"),
                    "hsa_eligible": row.get("recommended_plan_hsa_eligible"),
                    "quality_rating": row.get("recommended_plan_quality_rating")
                } if row.get("recommended_plan_id") else None,
                
                # Benefits Coverage Details
                "benefits_coverage": {
                    "emergency_room": row.get("benefits_emergency_room"),
                    "primary_care": row.get("benefits_primary_care"),
                    "specialist_care": row.get("benefits_specialist_care"),
                    "prescription_drugs": row.get("benefits_prescription_drugs"),
                    "mental_health": row.get("benefits_mental_health"),
                    "maternity_care": row.get("benefits_maternity_care"),
                    "preventive_care": row.get("benefits_preventive_care")
                },
                
                # Top 3 Plan Options
                "top_3_available_plans": row.get("top_3_available_plans"),
                
                # Processing Status
                "api_processing_status": row.get("api_processing_status", "unknown")
            }
            
            # Remove None values for cleaner response
            employee_data = {k: v for k, v in employee_data.items() if v is not None}
            individual_data.append(employee_data)

        return individual_data

    def _generate_prediction_summary(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Generate comprehensive summary statistics for predictions."""
        summary = {
            "total_employees": len(df),
            "plan_type_distribution": {},
            "benefits_distribution": {},
            "confidence_metrics": {}
        }

        # Plan type distribution
        if 'predicted_plan_type' in df.columns:
            plan_counts = df['predicted_plan_type'].value_counts().to_dict()
            summary["plan_type_distribution"] = {
                "counts": plan_counts,
                "percentages": {k: round(v/len(df)*100, 2) for k, v in plan_counts.items()}
            }

        # Benefits distribution
        if 'predicted_benefits' in df.columns:
            all_benefits = []
            for benefits_list in df['predicted_benefits'].dropna():
                if isinstance(benefits_list, list):
                    all_benefits.extend(benefits_list)

            if all_benefits:
                from collections import Counter
                benefit_counts = Counter(all_benefits)
                summary["benefits_distribution"] = {
                    "counts": dict(benefit_counts),
                    "most_common": benefit_counts.most_common(5)
                }

        # Confidence metrics
        if 'plan_confidence' in df.columns:
            plan_confidences = df['plan_confidence'].dropna()
            if len(plan_confidences) > 0:
                summary["confidence_metrics"]["plan_confidence"] = {
                    "mean": float(plan_confidences.mean()),
                    "min": float(plan_confidences.min()),
                    "max": float(plan_confidences.max()),
                    "std": float(plan_confidences.std())
                }

        if 'benefits_confidence' in df.columns:
            benefits_confidences = df['benefits_confidence'].dropna()
            if len(benefits_confidences) > 0:
                summary["confidence_metrics"]["benefits_confidence"] = {
                    "mean": float(benefits_confidences.mean()),
                    "min": float(benefits_confidences.min()),
                    "max": float(benefits_confidences.max()),
                    "std": float(benefits_confidences.std())
                }

        return summary

    def _extract_plan_statistics_from_dataframe(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Extract comprehensive plan statistics from enriched DataFrame without additional API calls."""
        try:
            statistics = {
                "total_employees": len(df),
                "employees_with_plans": 0,
                "employees_without_plans": 0,
                "api_errors": 0,
                "unsupported_states": set(),
                "supported_employees": 0,
                "total_plans_found": 0,
                "unique_plan_types": set(),
                "unique_metal_levels": set(),
                "unique_issuers": set(),
                "plan_type_distribution": {},
                "metal_level_distribution": {},
                "premium_statistics": {
                    "min_premium": None,
                    "max_premium": None,
                    "avg_premium": None
                },
                "hsa_eligible_plans": 0,
                "quality_ratings": {
                    "plans_with_ratings": 0,
                    "avg_rating": None
                }
            }

            premiums = []
            quality_ratings = []

            # Analyze each employee's plan data
            for _, row in df.iterrows():
                # Check if employee has marketplace plans available
                has_plans = row.get('marketplace_plans_available', False)
                plan_count = row.get('plan_count', 0)
                api_status = row.get('api_processing_status', 'unknown')

                if has_plans and plan_count > 0:
                    statistics["employees_with_plans"] += 1
                    statistics["total_plans_found"] += plan_count
                    statistics["supported_employees"] += 1

                    # Extract plan type information
                    available_types = row.get('available_plan_types', '') or ''
                    if available_types and isinstance(available_types, str):
                        types = [t.strip() for t in available_types.split(',') if t.strip()]
                        statistics["unique_plan_types"].update(types)

                    # Extract metal level information
                    available_metals = row.get('available_metal_levels', '') or ''
                    if available_metals and isinstance(available_metals, str):
                        metals = [m.strip() for m in available_metals.split(',') if m.strip()]
                        statistics["unique_metal_levels"].update(metals)

                    # Extract recommended plan details
                    rec_plan_type = row.get('recommended_plan_type')
                    if rec_plan_type:
                        statistics["plan_type_distribution"][rec_plan_type] = statistics["plan_type_distribution"].get(rec_plan_type, 0) + 1

                    rec_metal_level = row.get('recommended_plan_metal_level')
                    if rec_metal_level:
                        statistics["metal_level_distribution"][rec_metal_level] = statistics["metal_level_distribution"].get(rec_metal_level, 0) + 1

                    # Extract premium information
                    rec_premium = row.get('recommended_plan_premium')
                    if rec_premium is not None and isinstance(rec_premium, (int, float)) and rec_premium > 0:
                        premiums.append(rec_premium)

                    # Extract issuer information
                    rec_issuer = row.get('recommended_plan_issuer')
                    if rec_issuer:
                        statistics["unique_issuers"].add(rec_issuer)

                    # Check HSA eligibility
                    hsa_eligible = row.get('recommended_plan_hsa_eligible', False)
                    if hsa_eligible:
                        statistics["hsa_eligible_plans"] += 1

                    # Extract quality rating
                    quality_rating = row.get('recommended_plan_quality_rating')
                    if quality_rating and isinstance(quality_rating, (int, float)) and quality_rating > 0:
                        quality_ratings.append(quality_rating)

                elif api_status == 'error':
                    statistics["api_errors"] += 1
                    statistics["employees_without_plans"] += 1

                    # Check for unsupported state
                    error_msg = row.get('api_error_message', '')
                    if 'marketplace state' in error_msg or 'own marketplace' in error_msg:
                        state = row.get('state', '')
                        if state:
                            statistics["unsupported_states"].add(state)
                else:
                    statistics["employees_without_plans"] += 1
                    if api_status not in ['error']:
                        statistics["supported_employees"] += 1

            # Calculate premium statistics
            if premiums:
                statistics["premium_statistics"] = {
                    "min_premium": min(premiums),
                    "max_premium": max(premiums),
                    "avg_premium": sum(premiums) / len(premiums)
                }

            # Calculate quality rating statistics
            if quality_ratings:
                statistics["quality_ratings"] = {
                    "plans_with_ratings": len(quality_ratings),
                    "avg_rating": sum(quality_ratings) / len(quality_ratings)
                }

            # Convert sets to lists for JSON serialization
            statistics["unique_plan_types"] = list(statistics["unique_plan_types"])
            statistics["unique_metal_levels"] = list(statistics["unique_metal_levels"])
            statistics["unique_issuers"] = list(statistics["unique_issuers"])
            statistics["unsupported_states"] = list(statistics["unsupported_states"])

            return statistics

        except Exception as e:
            logger.error(f"Error extracting plan statistics from DataFrame: {str(e)}")
            return {
                "total_employees": len(df) if df is not None else 0,
                "error": f"Failed to extract statistics: {str(e)}"
            }

    def _extract_processing_notes_from_dataframe(self, df: pd.DataFrame) -> List[str]:
        """Extract processing notes and warnings from enriched DataFrame."""
        try:
            notes = []

            # Count different processing statuses
            status_counts = {}
            error_messages = []
            unsupported_states = set()

            for _, row in df.iterrows():
                api_status = row.get('api_processing_status', 'unknown')
                status_counts[api_status] = status_counts.get(api_status, 0) + 1

                # Collect error messages
                error_msg = row.get('api_error_message', '') or ''
                if error_msg and error_msg not in error_messages:
                    error_messages.append(error_msg)

                # Track unsupported states
                if error_msg and ('marketplace state' in error_msg or 'own marketplace' in error_msg):
                    state = row.get('state', '') or ''
                    if state:
                        unsupported_states.add(state)

            # Generate summary notes
            total_employees = len(df)

            # Success rate note
            successful = status_counts.get('success', 0)
            if successful > 0:
                success_rate = (successful / total_employees) * 100
                notes.append(f"Health plan integration successful for {successful}/{total_employees} employees ({success_rate:.1f}%)")

            # Error summary
            errors = status_counts.get('error', 0)
            if errors > 0:
                notes.append(f"API errors encountered for {errors} employees")

            # No plans found
            no_plans = status_counts.get('no_plans_found', 0)
            if no_plans > 0:
                notes.append(f"No marketplace plans found for {no_plans} employees")

            # Unsupported states
            if unsupported_states:
                states_list = ', '.join(sorted(unsupported_states))
                notes.append(f"Unsupported marketplace states detected: {states_list}")

            # Plan availability summary
            employees_with_plans = sum(1 for _, row in df.iterrows() if row.get('marketplace_plans_available', False))
            if employees_with_plans > 0:
                notes.append(f"Marketplace plans available for {employees_with_plans} employees")

            # Premium information
            premiums = [row.get('recommended_plan_premium') for _, row in df.iterrows()
                       if row.get('recommended_plan_premium') and isinstance(row.get('recommended_plan_premium'), (int, float))]
            if premiums:
                avg_premium = sum(premiums) / len(premiums)
                notes.append(f"Average recommended plan premium: ${avg_premium:.2f}")

            # HSA eligible plans
            hsa_count = sum(1 for _, row in df.iterrows() if row.get('recommended_plan_hsa_eligible', False))
            if hsa_count > 0:
                notes.append(f"HSA-eligible plans recommended for {hsa_count} employees")

            # Quality ratings
            quality_ratings = [row.get('recommended_plan_quality_rating') for _, row in df.iterrows()
                             if row.get('recommended_plan_quality_rating') and isinstance(row.get('recommended_plan_quality_rating'), (int, float))]
            if quality_ratings:
                avg_rating = sum(quality_ratings) / len(quality_ratings)
                notes.append(f"Average plan quality rating: {avg_rating:.1f}/5.0 ({len(quality_ratings)} plans rated)")

            # Add specific error details if any
            if error_messages:
                unique_errors = list(set(error_messages))[:3]  # Limit to top 3 unique errors
                for error in unique_errors:
                    if len(error) < 100:  # Only include short error messages
                        notes.append(f"API Error: {error}")

            # If no notes generated, add default
            if not notes:
                notes.append("Health plan integration completed with no specific issues to report")

            return notes

        except Exception as e:
            logger.error(f"Error extracting processing notes from DataFrame: {str(e)}")
            return [f"Error generating processing notes: {str(e)}"]
