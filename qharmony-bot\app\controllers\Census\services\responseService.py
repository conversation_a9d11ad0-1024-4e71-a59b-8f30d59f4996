"""
Clean Response Service - Standardized response building for census processing.
"""

import logging
from typing import Dict, Any, Optional, List

logger = logging.getLogger(__name__)


class ResponseService:
    """Clean service for building standardized census processing responses."""
    
    @staticmethod
    def success_response(data: Dict[str, Any], message: str = "Census file processed successfully") -> Dict[str, Any]:
        """Build standardized success response matching documented format exactly."""
        return ResponseService._build_standardized_response(data, message)

    @staticmethod
    def _build_standardized_response(data: Dict[str, Any], message: str) -> Dict[str, Any]:
        """Build standardized response structure matching documentation."""
        
        # Extract key components
        individual_employee_data = data.get("individual_employee_data", [])
        comprehensive_statistics = data.get("comprehensive_statistics", {})
        plan_statistics = data.get("plan_statistics", {})
        metadata = data.get("metadata", {})
        
        # Check if we should include dataframe data
        return_dataframe = data.get("return_dataframe", True)
        
        # Build standardized response matching documentation
        response_data = {
            # Summary Section - High-level processing results
            "summary": {
                "total_employees": len(individual_employee_data) if individual_employee_data else 0,
                "processing_success": True,
                "data_quality_score": ResponseService._calculate_data_quality_score(data),
                "api_integration_status": {
                    "health_plans_available": metadata.get("health_plan_integration_success", False),
                    "prediction_method": metadata.get("prediction_method", "unknown"),
                    "total_plans_found": plan_statistics.get("total_plans_found", 0)
                }
            },
            
            # Statistics Section - Aggregate analysis
            "statistics": {
                "demographics": comprehensive_statistics.get("basic_demographics", {}),
                "employment": comprehensive_statistics.get("employment_demographics", {}),
                "dependents": comprehensive_statistics.get("dependent_analysis", {}),
                "health_plans": plan_statistics,
                "predictions": data.get("prediction_summary", {}),
                "risk_analysis": comprehensive_statistics.get("risk_analysis", {})
            },
            
            # Individual Employee Data Section (FIXED: moved from metadata to main data)
            "employees": individual_employee_data,
            
            # Processing Information Section
            "processing_info": {
                "enrichment_summary": data.get("enrichment_summary", {}),
                "feature_validation": data.get("feature_validation", {}),
                "health_plan_errors": data.get("health_plan_errors", {}),
                "processing_notes": data.get("health_plan_processing_notes", [])
            }
        }
        
        # Conditionally add enriched CSV data based on return_dataframe parameter
        if return_dataframe and "enriched_data" in data:
            enriched_df = data["enriched_data"]
            if enriched_df is not None:
                response_data["enriched_data_csv"] = enriched_df.to_csv(index=False)
                response_data["total_employees"] = len(enriched_df)
                response_data["total_columns"] = len(enriched_df.columns)
                response_data["has_predictions"] = any('predicted_' in col for col in enriched_df.columns)
                response_data["prediction_columns"] = [col for col in enriched_df.columns if 'predicted_' in col or '_confidence' in col]
        
        # Technical Metadata Section (processing information only)
        response_metadata = {
            "pipeline_version": metadata.get("pipeline_version", "2.0_with_health_plans"),
            "total_steps": 6,  # Standardized to 6 steps
            "steps_completed": metadata.get("steps_completed", []),
            "processing_order": ["step_1_parsing", "step_2_mapping", "step_3_pattern", "step_4_preprocessing", "step_5_enrichment_prediction", "step_6_health_plans"],
            "prediction_method": metadata.get("prediction_method", "unknown"),
            "health_plan_integration_success": metadata.get("health_plan_integration_success", False),
            "final_dataframe_shape": metadata.get("final_dataframe_shape", [0, 0]),
            "processing_time_seconds": metadata.get("processing_time_seconds", 0),
            "file_info": data.get("file_info", {}),
            "pattern_info": data.get("pattern_info", {}),
            "mapping_result": data.get("mapping_result", {}),
            "preprocessing_result": data.get("preprocessing_result", {}),
            "return_dataframe": return_dataframe
        }
        
        return {
            "success": True,
            "status_code": 200,
            "message": message,
            "data": response_data,
            "metadata": response_metadata
        }

    @staticmethod
    def _calculate_data_quality_score(data: Dict[str, Any]) -> float:
        """Calculate data quality score based on processing results."""
        try:
            score = 0.0
            
            # Base score for successful processing
            score += 0.3
            
            # Score based on enrichment quality
            enrichment_summary = data.get("enrichment_summary", {})
            if enrichment_summary:
                completion_rate = enrichment_summary.get("data_quality_improvement", {}).get("overall_completion_rate", 0)
                score += (completion_rate / 100.0) * 0.4
            
            # Score based on prediction confidence
            prediction_summary = data.get("prediction_summary", {})
            if prediction_summary:
                confidence_metrics = prediction_summary.get("confidence_metrics", {})
                if confidence_metrics:
                    plan_confidence = confidence_metrics.get("plan_confidence", {})
                    if plan_confidence and "mean" in plan_confidence:
                        try:
                            mean_confidence = float(plan_confidence["mean"])
                            score += mean_confidence * 0.3
                        except (ValueError, TypeError):
                            pass
            
            return min(1.0, max(0.0, score))
            
        except Exception as e:
            logger.error(f"Error calculating data quality score: {str(e)}")
            return 0.5  # Default score

    @staticmethod
    def error_response(error_code: str, message: str, status_code: int = 400,
                      details: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """Build standardized error response."""
        error_data = {
            "error_code": error_code,
            "error_message": message
        }
        if details:
            error_data.update(details)
        
        return {
            "success": False,
            "status_code": status_code,
            "error": error_code,
            "message": message,
            "data": error_data
        }
    
    @staticmethod
    def validation_error_response(validation_errors: List[str], 
                                error_rows: Optional[List[int]] = None) -> Dict[str, Any]:
        """Build standardized validation error response."""
        return {
            "success": False,
            "status_code": 422,
            "error": "validation_failed",
            "message": f"Validation failed with {len(validation_errors)} error(s)",
            "data": {
                "error_code": "validation_failed",
                "error_message": f"Validation failed with {len(validation_errors)} error(s)",
                "validation_errors": validation_errors,
                "error_rows": error_rows or []
            }
        }
