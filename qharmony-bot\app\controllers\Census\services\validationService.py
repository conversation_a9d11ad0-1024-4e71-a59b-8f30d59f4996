"""
Validation Service - Centralized validation logic for census processing.
"""

import pandas as pd
import logging
from typing import Dict, Any, List

logger = logging.getLogger(__name__)


class ValidationService:
    """Service for handling validation operations in census processing."""
    
    # Critical fields that should not have null values
    CRITICAL_FIELDS = [
        'name', 'first_name', 'last_name', 'full_name',  # Name fields
        'dob', 'age', 'birth_date',  # Age/DOB fields
        'address1', 'address', 'street_address',  # Address fields
        'zipcode', 'zip', 'postal_code'  # Location fields
    ]
    
    def pre_validate_data(self, df: pd.DataFrame) -> Dict[str, Any]:
        """
        Perform pre-validation checks on raw data.
        Focus on critical fields that would cause processing failures.
        """
        logger.info("Performing pre-validation checks")
        
        validation_result = {
            "is_valid": True,
            "errors": [],
            "warnings": [],
            "null_analysis": {},
            "recommendations": []
        }
        
        # Check for critical null values
        critical_nulls = self._check_critical_nulls(df)
        if critical_nulls["has_critical_nulls"]:
            validation_result["errors"].extend(critical_nulls["errors"])
            validation_result["is_valid"] = False
        
        validation_result["null_analysis"] = critical_nulls["analysis"]
        
        # Check data completeness
        completeness = self._analyze_data_completeness(df)
        validation_result["warnings"].extend(completeness["warnings"])
        validation_result["recommendations"].extend(completeness["recommendations"])
        
        # Check for duplicate rows
        duplicate_check = self._check_duplicates(df)
        if duplicate_check["has_duplicates"]:
            validation_result["warnings"].extend(duplicate_check["warnings"])
        
        logger.info(f"Pre-validation completed: {'PASSED' if validation_result['is_valid'] else 'FAILED'}")
        
        return validation_result
    
    def _check_critical_nulls(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Check for null values in critical fields."""
        analysis = {}
        errors = []
        has_critical_nulls = False
        
        # Find critical columns that exist in the DataFrame
        existing_critical_columns = [col for col in df.columns 
                                   if any(critical in col.lower() for critical in self.CRITICAL_FIELDS)]
        
        for column in existing_critical_columns:
            null_count = df[column].isnull().sum()
            total_count = len(df)
            null_percentage = (null_count / total_count) * 100 if total_count > 0 else 0
            
            analysis[column] = {
                "null_count": int(null_count),
                "total_count": int(total_count),
                "null_percentage": round(null_percentage, 2),
                "is_critical": True
            }
            
            # Flag as error if critical field has too many nulls
            if null_percentage > 50:  # More than 50% null values
                errors.append(f"Critical field '{column}' has {null_percentage:.1f}% null values")
                has_critical_nulls = True
        
        return {
            "has_critical_nulls": has_critical_nulls,
            "errors": errors,
            "analysis": analysis
        }
    
    def _analyze_data_completeness(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Analyze overall data completeness."""
        warnings = []
        recommendations = []
        
        # Overall null percentage
        total_cells = df.size
        null_cells = df.isnull().sum().sum()
        overall_null_percentage = (null_cells / total_cells) * 100 if total_cells > 0 else 0
        
        if overall_null_percentage > 30:
            warnings.append(f"High percentage of missing data: {overall_null_percentage:.1f}%")
            recommendations.append("Consider data cleaning before processing")
        elif overall_null_percentage > 15:
            warnings.append(f"Moderate missing data detected: {overall_null_percentage:.1f}%")
        
        # Check for completely empty rows
        empty_rows = df.isnull().all(axis=1).sum()
        if empty_rows > 0:
            warnings.append(f"Found {empty_rows} completely empty rows")
            recommendations.append("Remove empty rows before processing")
        
        # Check for columns with all null values
        empty_columns = df.columns[df.isnull().all()].tolist()
        if empty_columns:
            warnings.append(f"Columns with no data: {empty_columns}")
            recommendations.append("Consider removing empty columns")
        
        return {
            "warnings": warnings,
            "recommendations": recommendations,
            "overall_null_percentage": overall_null_percentage,
            "empty_rows": int(empty_rows),
            "empty_columns": empty_columns
        }
    
    def _check_duplicates(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Check for duplicate rows."""
        duplicate_rows = df.duplicated().sum()
        has_duplicates = duplicate_rows > 0
        
        warnings = []
        if has_duplicates:
            duplicate_percentage = (duplicate_rows / len(df)) * 100
            warnings.append(f"Found {duplicate_rows} duplicate rows ({duplicate_percentage:.1f}%)")
        
        return {
            "has_duplicates": has_duplicates,
            "duplicate_count": int(duplicate_rows),
            "warnings": warnings
        }
    
    def validate_processed_data(self, df: pd.DataFrame, mandatory_fields: List[str]) -> Dict[str, Any]:
        """Validate processed data against mandatory field requirements."""
        logger.info("Validating processed data")
        
        validation_result = {
            "is_valid": True,
            "errors": [],
            "error_rows": [],
            "total_errors": 0
        }
        
        error_rows = set()
        
        for field in mandatory_fields:
            if field in df.columns:
                # Check for null or empty values
                null_mask = df[field].isna() | (df[field] == "") | (df[field] == "None")
                null_rows = df[null_mask].index.tolist()
                
                if null_rows:
                    validation_result["errors"].append({
                        "field": field,
                        "error_type": "missing_value",
                        "affected_rows": len(null_rows),
                        "message": f"Missing values in mandatory field: {field}"
                    })
                    error_rows.update(null_rows)
            else:
                validation_result["errors"].append({
                    "field": field,
                    "error_type": "missing_field",
                    "affected_rows": len(df),
                    "message": f"Mandatory field missing: {field}"
                })
                error_rows.update(df.index.tolist())
        
        validation_result["error_rows"] = list(error_rows)
        validation_result["total_errors"] = len(validation_result["errors"])
        validation_result["is_valid"] = validation_result["total_errors"] == 0
        
        logger.info(f"Processed data validation: {'PASSED' if validation_result['is_valid'] else 'FAILED'} "
                   f"({validation_result['total_errors']} errors)")
        
        return validation_result
    
    def calculate_data_quality_score(self, preprocessing_result: Dict) -> float:
        """Calculate overall data quality score (0-1)."""
        summary = preprocessing_result.get("summary", {})
        validation = preprocessing_result.get("validation_result", {})
        
        # Base score from validation
        validation_score = 1.0 if validation.get("is_valid", False) else 0.5
        
        # Completeness score
        original_rows = summary.get("original_rows", 1)
        processed_rows = summary.get("processed_rows", 0)
        completeness_score = min(processed_rows / original_rows, 1.0) if original_rows > 0 else 0.0
        
        # Processing success score
        fields_processed = summary.get("fields_processed", {})
        processing_success_count = sum(1 for success in fields_processed.values() if success)
        total_processing_steps = len(fields_processed) if fields_processed else 1
        processing_score = processing_success_count / total_processing_steps
        
        # Weighted average
        quality_score = (validation_score * 0.4 + completeness_score * 0.4 + processing_score * 0.2)
        
        return round(quality_score, 3)
