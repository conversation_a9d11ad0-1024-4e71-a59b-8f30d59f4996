"""
ZIP Code Location Service for Census Processing

Service for enriching location data based on ZIP codes.
Uses multiple data sources and fallback strategies for comprehensive location enrichment.
"""

import logging
import requests
from typing import Dict, Optional
from functools import lru_cache

logger = logging.getLogger(__name__)


class ZipCodeLocationService:
    """
    Service for enriching location data based on ZIP codes.
    Uses multiple data sources and fallback strategies for comprehensive location enrichment.
    """

    def __init__(self):
        """Initialize the ZIP code location service."""

        # Cache for ZIP code lookups to avoid repeated API calls
        self._zip_cache = {}

        # Comprehensive ZIP code to region mapping based on population density and urbanization
        self._zip_region_mapping = self._build_zip_region_mapping()

        # State to region mapping as fallback
        self._state_region_mapping = {
            # Urban states (major metropolitan areas)
            'CA': 'Urban', 'NY': 'Urban', 'TX': 'Urban', 'FL': 'Urban',
            'WA': 'Urban', 'IL': 'Urban', 'MA': 'Urban', 'NJ': 'Urban',
            'CT': 'Urban', 'MD': 'Urban', 'VA': 'Urban', 'CO': 'Urban',
            'AZ': 'Urban', 'NC': 'Urban', 'GA': 'Urban', 'OH': 'Urban',
            'PA': 'Urban', 'MI': 'Urban', 'IN': 'Urban', 'TN': 'Urban',

            # Suburban states (mixed urban/suburban)
            'OR': 'Suburban', 'NV': 'Suburban', 'UT': 'Suburban', 'NM': 'Suburban',
            'KS': 'Suburban', 'OK': 'Suburban', 'AR': 'Suburban', 'LA': 'Suburban',
            'MS': 'Suburban', 'AL': 'Suburban', 'SC': 'Suburban', 'KY': 'Suburban',
            'WV': 'Suburban', 'MO': 'Suburban', 'IA': 'Suburban', 'MN': 'Suburban',
            'WI': 'Suburban', 'NE': 'Suburban', 'NH': 'Suburban', 'ME': 'Suburban',
            'RI': 'Suburban', 'VT': 'Suburban', 'DE': 'Suburban',

            # Rural states (predominantly rural)
            'MT': 'Rural', 'WY': 'Rural', 'ND': 'Rural', 'SD': 'Rural',
            'ID': 'Rural', 'AK': 'Rural', 'HI': 'Suburban'  # Hawaii is unique
        }

    def _build_zip_region_mapping(self) -> Dict[str, str]:
        """
        Build comprehensive ZIP code to region mapping.
        This uses ZIP code ranges and known metropolitan areas.
        """
        zip_mapping = {}

        # Major Urban ZIP code ranges (major metropolitan areas)
        urban_zip_ranges = [
            # New York Metro (10001-10299, 11201-11256, 07001-07199)
            (10001, 10299), (11201, 11256), (7001, 7199),
            # Los Angeles Metro (90001-90899, 91001-91899)
            (90001, 90899), (91001, 91899),
            # Chicago Metro (60601-60699)
            (60601, 60699),
            # Houston Metro (77001-77299)
            (77001, 77299),
            # Phoenix Metro (85001-85099)
            (85001, 85099),
            # Philadelphia Metro (19101-19199)
            (19101, 19199),
            # San Antonio Metro (78201-78299)
            (78201, 78299),
            # San Diego Metro (92101-92199)
            (92101, 92199),
            # Dallas Metro (75201-75299)
            (75201, 75299),
            # San Jose Metro (95101-95199)
            (95101, 95199),
            # Austin Metro (78701-78799)
            (78701, 78799),
            # Jacksonville Metro (32201-32299)
            (32201, 32299),
            # San Francisco Metro (94101-94199)
            (94101, 94199),
            # Columbus Metro (43201-43299)
            (43201, 43299),
            # Fort Worth Metro (76101-76199)
            (76101, 76199),
            # Charlotte Metro (28201-28299)
            (28201, 28299),
            # Seattle Metro (98101-98199)
            (98101, 98199),
            # Denver Metro (80201-80299)
            (80201, 80299),
            # Washington DC Metro (20001-20099)
            (20001, 20099),
            # Boston Metro (02101-02199)
            (2101, 2199),
            # Nashville Metro (37201-37299)
            (37201, 37299),
            # Baltimore Metro (21201-21299)
            (21201, 21299),
            # Louisville Metro (40201-40299)
            (40201, 40299),
            # Portland Metro (97201-97299)
            (97201, 97299),
            # Las Vegas Metro (89101-89199)
            (89101, 89199),
            # Milwaukee Metro (53201-53299)
            (53201, 53299),
            # Albuquerque Metro (87101-87199)
            (87101, 87199),
            # Tucson Metro (85701-85799)
            (85701, 85799),
            # Fresno Metro (93701-93799)
            (93701, 93799),
            # Sacramento Metro (95801-95899)
            (95801, 95899),
            # Mesa Metro (85201-85299)
            (85201, 85299),
            # Kansas City Metro (64101-64199)
            (64101, 64199),
            # Atlanta Metro (30301-30399)
            (30301, 30399),
            # Long Beach Metro (90801-90899)
            (90801, 90899),
            # Colorado Springs Metro (80901-80999)
            (80901, 80999),
            # Raleigh Metro (27601-27699)
            (27601, 27699),
            # Miami Metro (33101-33199)
            (33101, 33199),
            # Virginia Beach Metro (23451-23499)
            (23451, 23499),
            # Omaha Metro (68101-68199)
            (68101, 68199),
            # Oakland Metro (94601-94699)
            (94601, 94699),
            # Minneapolis Metro (55401-55499)
            (55401, 55499),
            # Tulsa Metro (74101-74199)
            (74101, 74199),
            # Arlington Metro (76001-76099)
            (76001, 76099),
            # Tampa Metro (33601-33699)
            (33601, 33699),
            # New Orleans Metro (70112-70199)
            (70112, 70199),
            # Wichita Metro (67201-67299)
            (67201, 67299),
            # Cleveland Metro (44101-44199)
            (44101, 44199),
            # Bakersfield Metro (93301-93399)
            (93301, 93399),
            # Aurora Metro (80010-80099)
            (80010, 80099),
            # Anaheim Metro (92801-92899)
            (92801, 92899),
            # Honolulu Metro (96801-96899)
            (96801, 96899),
            # Santa Ana Metro (92701-92799)
            (92701, 92799),
            # Corpus Christi Metro (78401-78499)
            (78401, 78499),
            # Riverside Metro (92501-92599)
            (92501, 92599),
            # Lexington Metro (40501-40599)
            (40501, 40599),
            # Stockton Metro (95201-95299)
            (95201, 95299),
            # St. Louis Metro (63101-63199)
            (63101, 63199),
            # St. Paul Metro (55101-55199)
            (55101, 55199),
            # Cincinnati Metro (45201-45299)
            (45201, 45299),
            # Anchorage Metro (99501-99599)
            (99501, 99599),
            # Henderson Metro (89001-89099)
            (89001, 89099),
            # Greensboro Metro (27401-27499)
            (27401, 27499),
            # Plano Metro (75023-75099)
            (75023, 75099),
            # Newark Metro (07101-07199)
            (7101, 7199),
            # Lincoln Metro (68501-68599)
            (68501, 68599),
            # Buffalo Metro (14201-14299)
            (14201, 14299),
            # Jersey City Metro (07301-07399)
            (7301, 7399),
            # Chula Vista Metro (91901-91999)
            (91901, 91999),
            # Orlando Metro (32801-32899)
            (32801, 32899),
            # Norfolk Metro (23501-23599)
            (23501, 23599),
            # Chandler Metro (85224-85299)
            (85224, 85299),
            # Laredo Metro (78040-78099)
            (78040, 78099),
            # Madison Metro (53701-53799)
            (53701, 53799),
            # Durham Metro (27701-27799)
            (27701, 27799),
            # Lubbock Metro (79401-79499)
            (79401, 79499),
            # Winston-Salem Metro (27101-27199)
            (27101, 27199),
            # Garland Metro (75040-75099)
            (75040, 75099),
            # Glendale Metro (85301-85399)
            (85301, 85399),
            # Hialeah Metro (33010-33099)
            (33010, 33099),
            # Reno Metro (89501-89599)
            (89501, 89599),
            # Baton Rouge Metro (70801-70899)
            (70801, 70899),
            # Irvine Metro (92602-92699)
            (92602, 92699),
            # Chesapeake Metro (23320-23399)
            (23320, 23399),
            # Irving Metro (75061-75099)
            (75061, 75099),
            # Scottsdale Metro (85250-85299)
            (85250, 85299),
            # North Las Vegas Metro (89030-89099)
            (89030, 89099),
            # Fremont Metro (94536-94599)
            (94536, 94599),
            # Gilbert Metro (85233-85299)
            (85233, 85299),
            # San Bernardino Metro (92401-92499)
            (92401, 92499),
            # Boise Metro (83701-83799)
            (83701, 83799),
            # Birmingham Metro (35201-35299)
            (35201, 35299)
        ]

        # Add urban ZIP codes
        for start_zip, end_zip in urban_zip_ranges:
            for zip_code in range(start_zip, end_zip + 1):
                zip_mapping[f"{zip_code:05d}"] = "Urban"

        # Rural ZIP code ranges (typically lower population density areas)
        rural_zip_ranges = [
            # Montana
            (59001, 59999),
            # Wyoming
            (82001, 83199),
            # North Dakota
            (58001, 58999),
            # South Dakota
            (57001, 57999),
            # Idaho (excluding Boise metro)
            (83200, 83999),
            # Alaska (excluding Anchorage metro)
            (99600, 99999),
            # Vermont
            (5001, 5999),
            # Maine (rural areas)
            (4001, 4999),
            # New Hampshire (rural areas)
            (3001, 3999),
            # West Virginia
            (24701, 26999),
            # Rural Nevada
            (89400, 89500), (89600, 89999),
            # Rural Utah
            (84001, 84999),
            # Rural New Mexico
            (87001, 88999),
            # Rural Colorado
            (80424, 80999), (81001, 81999),
            # Rural Arizona
            (85501, 86999),
            # Rural Texas
            (75501, 79999),
            # Rural California
            (93001, 96199),
            # Rural Oregon
            (97001, 97999),
            # Rural Washington
            (98001, 99499),
            # Rural Florida
            (32001, 34999),
            # Rural Georgia
            (30001, 31999),
            # Rural North Carolina
            (27001, 28999),
            # Rural South Carolina
            (29001, 29999),
            # Rural Tennessee
            (37001, 38999),
            # Rural Kentucky
            (40001, 42999),
            # Rural Virginia
            (22001, 24999),
            # Rural Maryland
            (20601, 21999),
            # Rural Pennsylvania
            (15001, 19999),
            # Rural New York
            (10001, 14999),
            # Rural Ohio
            (43001, 45999),
            # Rural Michigan
            (48001, 49999),
            # Rural Indiana
            (46001, 47999),
            # Rural Illinois
            (60001, 62999),
            # Rural Wisconsin
            (53001, 54999),
            # Rural Minnesota
            (55001, 56999),
            # Rural Iowa
            (50001, 52999),
            # Rural Missouri
            (63001, 65999),
            # Rural Arkansas
            (71601, 72999),
            # Rural Louisiana
            (70001, 71999),
            # Rural Mississippi
            (38601, 39999),
            # Rural Alabama
            (35001, 36999),
            # Rural Kansas
            (66001, 67999),
            # Rural Oklahoma
            (73001, 74999),
            # Rural Nebraska
            (68001, 69999)
        ]

        # Add rural ZIP codes
        for start_zip, end_zip in rural_zip_ranges:
            for zip_code in range(start_zip, end_zip + 1):
                zip_str = f"{zip_code:05d}"
                if zip_str not in zip_mapping:  # Don't override urban mappings
                    zip_mapping[zip_str] = "Rural"

        return zip_mapping

    @lru_cache(maxsize=1000)
    def get_location_from_zipcode(self, zipcode: str) -> Dict[str, Optional[str]]:
        """
        Get location information from ZIP code using multiple strategies.

        Args:
            zipcode: ZIP code string (5 digits or ZIP+4)

        Returns:
            Dict with city, state, and region information
        """
        if not zipcode or not isinstance(zipcode, str):
            return {"city": None, "state": None, "region": "Urban"}

        # Clean and normalize ZIP code
        clean_zip = zipcode.strip().replace("-", "")[:5]

        if not clean_zip.isdigit() or len(clean_zip) != 5:
            logger.warning(f"Invalid ZIP code format: {zipcode}")
            return {"city": None, "state": None, "region": "Urban"}

        # Check cache first
        if clean_zip in self._zip_cache:
            return self._zip_cache[clean_zip]

        # Try to get location data
        location_data = self._lookup_zip_location(clean_zip)

        # Cache the result
        self._zip_cache[clean_zip] = location_data

        return location_data

    def _lookup_zip_location(self, zipcode: str) -> Dict[str, Optional[str]]:
        """
        Lookup ZIP code location using multiple strategies.

        Args:
            zipcode: 5-digit ZIP code string

        Returns:
            Dict with city, state, and region
        """
        # Strategy 1: Use built-in ZIP to region mapping
        region = self._zip_region_mapping.get(zipcode, None)

        # Strategy 2: Try free ZIP code API (zippopotam.us)
        city, state = self._try_free_zip_api(zipcode)

        # Strategy 3: Use ZIP code range analysis for state
        if not state:
            state = self._get_state_from_zip_range(zipcode)

        # Strategy 4: Use state to region mapping if no direct ZIP mapping
        if not region and state:
            region = self._state_region_mapping.get(state, "Suburban")

        # Strategy 5: Default fallback based on ZIP code patterns
        if not region:
            region = self._get_region_from_zip_pattern(zipcode)

        return {
            "city": city,
            "state": state,
            "region": region or "Urban"  # Default to Urban if all else fails
        }

    def _try_free_zip_api(self, zipcode: str) -> tuple[Optional[str], Optional[str]]:
        """
        Try to get city and state from free ZIP code API.

        Args:
            zipcode: 5-digit ZIP code

        Returns:
            Tuple of (city, state) or (None, None) if failed
        """
        try:
            # Use zippopotam.us free API
            response = requests.get(f"http://api.zippopotam.us/us/{zipcode}", timeout=2)

            if response.status_code == 200:
                data = response.json()
                city = data.get("places", [{}])[0].get("place name")
                state = data.get("places", [{}])[0].get("state abbreviation")

                if city and state:
                    logger.debug(f"ZIP {zipcode} -> {city}, {state} (API)")
                    return city, state

        except Exception as e:
            logger.debug(f"ZIP API lookup failed for {zipcode}: {e}")

        return None, None

    def _get_state_from_zip_range(self, zipcode: str) -> Optional[str]:
        """
        Get state from ZIP code using known ZIP code ranges.

        Args:
            zipcode: 5-digit ZIP code

        Returns:
            State abbreviation or None
        """
        zip_int = int(zipcode)

        # ZIP code ranges by state (first 3 digits)
        zip_state_ranges = {
            (10001, 14999): "NY", (6001, 6999): "CT", (7001, 8999): "NJ",
            (1001, 2999): "MA", (3001, 3999): "NH", (4001, 4999): "ME",
            (5001, 5999): "VT", (2801, 2999): "RI", (19701, 19999): "DE",
            (20001, 20599): "DC", (20600, 21999): "MD", (22001, 24699): "VA",
            (24700, 26999): "WV", (15001, 19699): "PA", (27001, 28999): "NC",
            (29001, 29999): "SC", (30001, 31999): "GA", (32001, 34999): "FL",
            (35001, 36999): "AL", (37001, 38999): "TN", (39001, 39999): "MS",
            (40001, 42999): "KY", (43001, 45999): "OH", (46001, 47999): "IN",
            (48001, 49999): "MI", (50001, 52999): "IA", (53001, 54999): "WI",
            (55001, 56999): "MN", (57001, 57999): "SD", (58001, 58999): "ND",
            (59001, 59999): "MT", (60001, 62999): "IL", (63001, 65999): "MO",
            (66001, 67999): "KS", (68001, 69999): "NE", (70001, 71999): "LA",
            (71601, 72999): "AR", (73001, 74999): "OK", (75001, 79999): "TX",
            (80001, 81999): "CO", (82001, 83199): "WY", (83200, 83999): "ID",
            (84001, 84999): "UT", (85001, 86999): "AZ", (87001, 88999): "NM",
            (89001, 89999): "NV", (90001, 96199): "CA", (97001, 97999): "OR",
            (98001, 99499): "WA", (99500, 99999): "AK", (96701, 96999): "HI"
        }

        for (start, end), state in zip_state_ranges.items():
            if start <= zip_int <= end:
                logger.debug(f"ZIP {zipcode} -> {state} (range)")
                return state

        return None

    def _get_region_from_zip_pattern(self, zipcode: str) -> str:
        """
        Get region from ZIP code patterns as final fallback.

        Args:
            zipcode: 5-digit ZIP code

        Returns:
            Region classification
        """
        zip_int = int(zipcode)

        # General patterns based on ZIP code ranges
        if zip_int < 20000:  # Northeast
            return "Urban"
        elif zip_int < 40000:  # Southeast
            return "Suburban"
        elif zip_int < 60000:  # Great Lakes
            return "Urban"
        elif zip_int < 80000:  # Plains/Midwest
            return "Rural"
        elif zip_int < 90000:  # Mountain West
            return "Rural"
        else:  # Pacific
            return "Urban"
