"""
Universal Field Mapper - Maps ANY CSV headers to system fields FIRST, before pattern identification.
"""

import json
import os
import logging
from typing import Dict, List, Optional, Tuple
import google.generativeai as genai
import pandas as pd
import time
from dotenv import load_dotenv
logger = logging.getLogger(__name__)
load_dotenv()

class UniversalFieldMapper:
    """Universal field mapper that maps ANY CSV headers to system fields first."""
    
    def __init__(self):
        # Try to initialize Google AI model, fallback to None if no API key
        google_api_key = os.environ.get("GOOGLE_API_KEY")
        if google_api_key:
            try:
                genai.configure(api_key=google_api_key)
                self.model = genai.GenerativeModel('gemini-2.5-flash')
            except Exception as e:
                logger.warning(f"Failed to initialize Google AI model: {e}")
                self.model = None
        else:
            logger.warning("GOOGLE_API_KEY not found. LLM field mapping will not be available.")
            self.model = None
        
        # Define ALL possible system fields (mandatory + optional)
        self.system_fields = {
            # Core Identity Fields
            "employee_id": {
                "variations": ["employee_id", "emp_id", "id", "empid", "employee_number", "ee_id"],
                "description": "Unique employee identifier"
            },
            "first_name": {
                "variations": ["first_name", "fname", "firstname", "given_name", "first"],
                "description": "Employee first name"
            },
            "last_name": {
                "variations": ["last_name", "lname", "lastname", "surname", "family_name", "last"],
                "description": "Employee last name"
            },
            "middle_name": {
                "variations": ["middle_name", "mname", "middlename", "middle_initial", "mi", "middle"],
                "description": "Employee middle name or initial"
            },
            "full_name": {
                "variations": ["full_name", "employee_name", "fullname", "complete_name"],
                "description": "Complete employee name"
            },
            "name": {
                "variations": ["name", "employee_name", "person_name", "full_name"],
                "description": "Single name field (when first/last not separate)"
            },
            
            # Demographics
            "dob": {
                "variations": ["dob", "date_of_birth", "birth_date", "birthdate", "d_o_b", "dateofbirth"],
                "description": "Date of birth"
            },
            "gender": {
                "variations": ["gender", "sex", "m_f", "male_female"],
                "description": "Gender/Sex"
            },
            
            # Address Fields
            "address1": {
                "variations": ["address1", "address", "addr1", "street", "address_line1", "home_address"],
                "description": "Primary address line"
            },
            "address2": {
                "variations": ["address2", "addr2", "apartment", "unit", "address_line2"],
                "description": "Secondary address line"
            },
            "city": {
                "variations": ["city", "town", "municipality"],
                "description": "City name"
            },
            "state": {
                "variations": ["state", "province", "region", "st"],
                "description": "State/Province"
            },
            "zipcode": {
                "variations": ["zipcode", "zip", "postal_code", "zip_code", "postcode"],
                "description": "Postal/ZIP code"
            },
            
            # Family/Relationship Fields
            "marital_status": {
                "variations": ["marital_status", "marital", "marriage_status", "married"],
                "description": "Marital status: Married, Not Married (Single, Divorced, Widowed → Not Married)"
            },
            "relationship": {
                "variations": ["relationship", "relation", "relationship_type", "member_type", "rel"],
                "description": "Relationship to employee: Employee (self), Spouse, Child (or Wife, Husband, Son, Daughter)"
            },
            "record_type": {
                "variations": ["record_type", "type", "member_category", "person_type", "record", "user_type"],
                "description": "Type of record: Employee, Dependent (or EE, DEP, Emp, Dept)"
            },
            
            # Dependent Fields (Column-based patterns) - Base count field
            "dept_count": {
                "variations": ["num_dependents", "dependent_count", "dep_count", "number_of_dependents"],
                "description": "Number of dependents"
            },
            
            # Benefits Fields
            "medical_plan": {
                "variations": ["medical_plan", "medical", "health_plan", "health", "med", "on_medical"],
                "description": "Medical plan election"
            },
            "dental_plan": {
                "variations": ["dental_plan", "dental", "dent", "on_dental"],
                "description": "Dental plan election"
            },
            "vision_plan": {
                "variations": ["vision_plan", "vision", "vis", "eye_care"],
                "description": "Vision plan election"
            },
            "life_plan": {
                "variations": ["life_plan", "life", "group_life", "life_insurance", "group_life_insurance"],
                "description": "Life insurance plan election"
            },
            "add_plan": {
                "variations": ["add_plan", "add", "ad&d", "add_insurance", "group_add", "accidental_death", "group_ad&d", "adnd_plan"],
                "description": "Accidental Death & Dismemberment plan election"
            },
            "coverage_tier": {
                "variations": ["coverage_tier", "tier", "coverage_type", "family_tier"],
                "description": "Coverage tier (Employee Only, Family, etc.)"
            },

            # Additional Census Fields
            "salary": {
                "variations": ["salary", "annual_salary", "yearly_salary", "income", "annual_income", "pay", "compensation", "earnings", "monthly_salary", "wage", "wages"],
                "description": "Annual salary (numeric value only, remove $ and commas)"
            },
            "tobacco_use": {
                "variations": ["tobacco", "tobacco_use", "smoker", "smoking", "nicotine", "tobacco_user", "cigarette", "tobacco_status", "smoke"],
                "description": "Tobacco use status: Y/N (Often/Sometimes → Y, Rarely/Never → N)"
            },
            "pregnancy_status": {
                "variations": ["pregnancy", "pregnant", "pregnancy_status", "expecting", "maternity", "prenatal"],
                "description": "Pregnancy status: Y/N (Pregnant/Expecting → Y, Not Pregnant → N)"
            },
            "employment_type": {
                "variations": ["employment_type", "employment_status", "emp_type", "emp_status", "status", "type", "employee_type", "employee_status", "work_type", "work_status", "job_type", "job_status"],
                "description": "Employment type: Full-Time, Part-Time, Temporary, Seasonal, Contract"
            },
            "employee_class": {
                "variations": ["employee_class", "class", "emp_class", "classification", "employee_type", "worker_class", "job_class"],
                "description": "Employee classification: Salaried, Hourly, Executive, Management, Union, Non-Union"
            },
            "department": {
                "variations": ["department", "dept", "division", "team", "unit", "group", "section", "area", "function", "business_unit"],
                "description": "Department/Division: Human Resources, Finance, Engineering, Sales, Marketing, Operations, etc."
            },
            "hire_date": {
                "variations": ["hire_date", "date_of_hire", "employment_date", "start_date", "date_hired", "join_date"],
                "description": "Date of hire (date format)"
            },
            "ssn": {
                "variations": ["ssn", "social_security", "social_security_number", "tax_id", "social", "social_security_no"],
                "description": "Social Security Number (sensitive data)"
            }
        }
        
        # Generate dynamic dependent fields (supports up to 20 dependents)
        self._generate_dependent_fields()

        # Define mandatory fields that MUST be mapped (relaxed to allow single name field)
        self.mandatory_fields = ["gender", "zipcode", "marital_status"]  # Name field checked separately

    def _generate_dependent_fields(self):
        """Generate dependent field definitions dynamically for up to 20 dependents."""

        # Generate dependent fields for numbers 1-20
        for i in range(1, 21):
            # Dependent name field
            self.system_fields[f"dept_{i}"] = {
                "variations": [
                    f"dept_{i}",  # Add the exact field name first
                    f"dep{i}_name", f"dependent{i}_name", f"dep_{i}_name", f"dependent_{i}_name",
                    f"dep{i}name", f"dependent{i}name", f"dep_{i}name", f"dependent_{i}name"
                ],
                "description": f"Dependent {i} name"
            }

            # Dependent relationship field
            self.system_fields[f"relationship_type_{i}"] = {
                "variations": [
                    f"relationship_type_{i}",  # Add the exact field name first
                    f"dep{i}_relationship", f"dependent{i}_relationship", f"dep_{i}_relationship", f"dependent_{i}_relationship",
                    f"dep{i}_relation", f"dependent{i}_relation", f"dep_{i}_relation", f"dependent_{i}_relation",
                    f"dep{i}relationship", f"dependent{i}relationship", f"dep_{i}relationship", f"dependent_{i}relationship"
                ],
                "description": f"Dependent {i} relationship to employee"
            }

            # Dependent DOB field
            self.system_fields[f"dept_{i}_dob"] = {
                "variations": [
                    f"dept_{i}_dob",  # Add the exact field name first
                    f"dep{i}_dob", f"dependent{i}_dob", f"dep_{i}_dob", f"dependent_{i}_dob",
                    f"dep{i}_birth_date", f"dependent{i}_birth_date", f"dep_{i}_birth_date", f"dependent_{i}_birth_date",
                    f"dep{i}_birthdate", f"dependent{i}_birthdate", f"dep_{i}_birthdate", f"dependent_{i}_birthdate",
                    f"dep{i}dob", f"dependent{i}dob", f"dep_{i}dob", f"dependent_{i}dob"
                ],
                "description": f"Dependent {i} date of birth"
            }

            # Dependent age field
            self.system_fields[f"dept_{i}_age"] = {
                "variations": [
                    f"dept_{i}_age",  # Add the exact field name first
                    f"dep{i}_age", f"dependent{i}_age", f"dep_{i}_age", f"dependent_{i}_age",
                    f"dep{i}age", f"dependent{i}age", f"dep_{i}age", f"dependent_{i}age"
                ],
                "description": f"Dependent {i} age"
            }

            # Dependent gender field
            self.system_fields[f"dept_{i}_gender"] = {
                "variations": [
                    f"dept_{i}_gender",  # Add the exact field name first
                    f"dep{i}_gender", f"dependent{i}_gender", f"dep_{i}_gender", f"dependent_{i}_gender",
                    f"dep{i}_sex", f"dependent{i}_sex", f"dep_{i}_sex", f"dependent_{i}_sex",
                    f"dep{i}gender", f"dependent{i}gender", f"dep_{i}gender", f"dependent_{i}gender",
                    f"dep{i}sex", f"dependent{i}sex", f"dep_{i}sex", f"dependent_{i}sex"
                ],
                "description": f"Dependent {i} gender"
            }

    async def map_all_fields(self, csv_headers: List[str], sample_rows: pd.DataFrame) -> Dict:
        """
        Map ALL CSV headers to system fields universally.
        
        Args:
            csv_headers: List of CSV column headers
            
        Returns:
            Dict with mapping result or error
        """
        logger.info(f"Universal mapping for {len(csv_headers)} fields")
        
        try:
            # Check if LLM is available
            if self.model is None:
                # Use fallback mapping when LLM is not available
                mapping_result = self._fallback_field_mapping(csv_headers)
            else:
                # Generate universal mapping prompt
                prompt = self._generate_universal_mapping_prompt(csv_headers, sample_rows)

                # Call LLM
                start=time.time()
                response = self.model.generate_content(prompt)
                end=time.time()
                print(f"LLM mapping fields time: {end-start}")

                # Parse response
                mapping_result = self._parse_llm_response(response.text)
            
            # Validate mapping completeness
            validation_result = self._validate_universal_mapping(mapping_result, csv_headers)
            
            if validation_result["is_valid"]:
                # Filter to only mapped fields and remove unmapped columns
                clean_mapping = {k: v for k, v in mapping_result.items()
                               if v != "unmapped" and v in self.system_fields}

                # Add exact matches that LLM might have missed
                for header in csv_headers:
                    if header not in clean_mapping and header in self.system_fields:
                        clean_mapping[header] = header
                        logger.info(f"Added exact match mapping: {header} → {header}")

                return {
                    "success": True,
                    "mapping": clean_mapping,
                    "mapped_fields": list(clean_mapping.values()),
                    "unmapped_fields": [h for h in csv_headers if h not in clean_mapping],
                    "validation": validation_result
                }
            else:
                return {
                    "success": False,
                    "error": "insufficient_mandatory_fields",
                    "message": validation_result["error_message"],
                    "missing_mandatory": validation_result["missing_mandatory"],
                    "available_fields": csv_headers
                }
                
        except Exception as e:
            logger.error(f"Universal field mapping failed: {str(e)}")
            return {
                "success": False,
                "error": "mapping_failed",
                "message": f"Failed to map fields: {str(e)}"
            }

    def _analyze_column_values(self, sample_rows: pd.DataFrame) -> Dict[str, List[str]]:
        """Analyze unique values in each column for value mapping."""
        column_values = {}

        for column in sample_rows.columns:
            # Get unique non-null values
            unique_values = sample_rows[column].dropna().astype(str).unique()
            # Limit to reasonable number of unique values for categorical fields
            if len(unique_values) <= 20:  # Only analyze categorical-like fields
                column_values[column] = unique_values.tolist()
            else:
                column_values[column] = ["<numeric_or_text_field>"]  # Skip high-cardinality fields

        return column_values

    def _generate_universal_mapping_prompt(self, headers: List[str], sample_rows: pd.DataFrame) -> str:
        """Generate universal mapping prompt for ALL possible fields."""
        
        # Format system fields for prompt
        system_fields_desc = self._format_system_fields_for_prompt()
        
        prompt = f"""
        You are an expert data analyst specializing in employee census data standardization.

        MISSION: Transform raw CSV data into a standardized system schema by creating precise, context-aware field mappings.

        YOUR TASK: Analyze the provided CSV structure and map each column header to the most appropriate system field based on both naming patterns and actual data content.

        DATA TO ANALYZE:
        • CSV Headers: {headers}
        • Sample Data Context:
        {sample_rows.head(10).to_string()}

        AVAILABLE TARGET SYSTEM FIELDS:
        {system_fields_desc}

        MAPPING OBJECTIVE: Create accurate mappings that preserve data meaning while enabling consistent processing. Consider the business context of employee census data (demographics, employment, benefits, family information) when making mapping decisions.

        MANDATORY FIELDS (MUST be mapped or return error):
        - NAME FIELDS: At least one name-related field must be mapped
        - gender (gender/sex identification field)
        - zipcode (postal/zip code field)
        - marital_status (marital status field)

        NAME FIELD MAPPING STRATEGY:
        Your goal is to map name fields to their most specific and accurate system equivalents:

        - If you see "First_Name", "FirstName", "fname" → map to "first_name"
        - If you see "Last_Name", "LastName", "lname", "Surname" → map to "last_name"
        - If you see "Middle_Name", "MiddleName", "mname", "MI" → map to "middle_name"
        - If you see a single comprehensive name field like "Name", "Full_Name", "Employee_Name", "Complete_Name" → map to "name"

        MAPPING PRINCIPLE: Choose the system field that best represents the actual data structure in the CSV.
        - Separate name components should map to separate system fields (first_name, last_name, middle_name)
        - Combined name fields should map to the unified "name" field
        - NEVER use "full_name" as a target - always use "name" for combined name fields

        DEPENDENT MAPPING RULES:
        - ALL dependents (spouse, children, etc.) should map to dept_1, dept_2, dept_3, etc.
        - Spouse birth date → dept_1_dob (spouse is typically first dependent)
        - Children birth dates → dept_2_dob, dept_3_dob, etc.
        - "Spouse Covered?" → use for marital_status inference
        - "# of Children Covered" → dept_count
        - "Childrens Birth Date" (comma-separated) → split to dept_2_dob, dept_3_dob, etc.
        - Do NOT create special spouse/children fields - use standard dept_X pattern

        CATEGORICAL FIELD VALUES (LLM must understand these standard values):
        - record_type: "Employee", "Dependent" (or variations like "EE", "DEP", "Emp", "Dept")
        - relationship: "Employee" (for self), "Spouse", "Child" (or variations like "Wife", "Husband", "Son", "Daughter")
        - marital_status: "Married", "Not Married" (or variations like "Single", "Divorced", "Widowed" → "Not Married")
        - gender: "Male", "Female" (or variations like "M"→"Male", "F"→"Female")
        - tobacco_use: "Y", "N" (or variations like "Yes/No", "Smoker/Non-Smoker", "Often/Sometimes" → "Y", "Rarely/Never" → "N")
        - pregnancy_status: "Y", "N" (or variations like "Yes/No", "Pregnant/Not Pregnant", "Expecting/Not Expecting")
        - employment_type: "Full-Time", "Part-Time", "Temporary", "Seasonal", "Contract"
        - employee_class: "Salaried", "Hourly", "Executive", "Management", "Union", "Non-Union"

        CONTEXT-DRIVEN MAPPING METHODOLOGY:

        STEP 1 - DATA CONTEXT ANALYSIS:
        • Examine column names AND sample data values together
        • Understand the business context (employee census, benefits enrollment, demographic survey)
        • Identify data patterns and relationships between columns

        STEP 2 - SEMANTIC FIELD MATCHING:
        • Prioritize meaning over naming similarity
        • Consider field purpose within the broader data context
        • Look for logical groupings (employee info, dependent info, benefits, etc.)

        STEP 3 - PRECISION MAPPING RULES:
        1. EXACT MATCHES: If CSV header matches system field exactly → direct mapping
        2. SEMANTIC MATCHES: Map to system field that best represents the data purpose
        3. SPECIFICITY PRIORITY: Choose most specific applicable system field
        4. MANDATORY COVERAGE: Ensure all mandatory fields have valid mappings
        5. DEPENDENT STRUCTURE: Map family data to numbered dept_X pattern consistently
        6. BENEFIT CATEGORIZATION: Map benefit fields to specific plan types
        7. UNMAPPABLE LAST RESORT: Use "unmapped" only when no reasonable system field exists

        MAPPING VALIDATION:
        • Does this mapping accurately represent the data's business purpose?
        • Will the mapped field support the intended data processing and analysis?
        • Are all mandatory fields covered with appropriate mappings?

        SUCCESS OUTPUT FORMAT:
        {{"csv_header": "system_field", "csv_header2": "system_field2"}}

        ERROR OUTPUT FORMAT (if mandatory fields missing):
        {{
            "error": "insufficient_mandatory_fields",
            "message": "Cannot map required mandatory fields",
            "missing_mandatory": ["field1", "field2"]
        }}

        Examples of GOOD mappings:
        - "First Name" → "first_name"
        - "D_O_B" → "dob"
        - "ZIP" → "zipcode"
        - "Sex" → "gender"
        - "Dep1_Name" → "dept_1"
        - "Employee_ID" → "employee_id"

        CSV Headers: {headers}

        Return mapping as JSON:
        """
        return prompt

    def _fallback_field_mapping(self, csv_headers: List[str]) -> Dict:
        """Fallback field mapping using simple string matching when LLM is not available."""
        logger.info("Using fallback field mapping (no LLM)")

        mapping = {}

        # First pass: identify name fields to apply smart logic
        name_related_headers = []
        for header in csv_headers:
            header_lower = header.lower().replace("_", "").replace(" ", "")
            if any(name_word in header_lower for name_word in ["name", "first", "last", "fname", "lname"]):
                name_related_headers.append(header)

        # Apply name field logic
        has_separate_names = any("first" in h.lower() for h in name_related_headers) and any("last" in h.lower() for h in name_related_headers)

        for header in csv_headers:
            header_lower = header.lower().replace("_", "").replace(" ", "")
            best_match = None

            # Special handling for name fields
            if header in name_related_headers:
                if has_separate_names:
                    # Map to first_name/last_name if both exist
                    if "first" in header_lower:
                        best_match = "first_name"
                    elif "last" in header_lower:
                        best_match = "last_name"
                else:
                    # Map to single "name" field if only one name field
                    if "name" in header_lower:
                        best_match = "name"

            # If not handled by name logic, use standard matching
            if not best_match:
                # Try to find exact matches first
                for system_field, field_info in self.system_fields.items():
                    for variation in field_info["variations"]:
                        variation_clean = variation.lower().replace("_", "").replace(" ", "")
                        if header_lower == variation_clean:
                            best_match = system_field
                            break
                    if best_match:
                        break

                # If no exact match, try partial matches
                if not best_match:
                    for system_field, field_info in self.system_fields.items():
                        for variation in field_info["variations"]:
                            variation_clean = variation.lower().replace("_", "").replace(" ", "")
                            if variation_clean in header_lower or header_lower in variation_clean:
                                best_match = system_field
                                break
                        if best_match:
                            break

            if best_match:
                mapping[header] = best_match
            else:
                mapping[header] = "unmapped"

        return mapping

    def _format_system_fields_for_prompt(self) -> str:
        """Format all system fields for LLM prompt."""
        formatted = []
        
        # Generate dependent fields dynamically for up to 20 dependents
        dependent_fields = ["dept_count"]
        for i in range(1, 21):  # 1 to 20 dependents
            dependent_fields.extend([
                f"dept_{i}",
                f"dept_{i}_dob",
                f"dept_{i}_gender",
                f"dept_{i}_age",
                f"relationship_type_{i}",
                f"dept_{i}_medical_plan",
                f"dept_{i}_dental_plan",
                f"dept_{i}_vision_plan",
                f"dept_{i}_life_plan",
                f"dept_{i}_add_plan"
            ])

        categories = {
            "CORE IDENTITY": ["employee_id", "first_name", "middle_name", "last_name", "name"],
            "DEMOGRAPHICS": ["dob", "gender", "age"],
            "ADDRESS": ["address1", "address2", "city", "state", "zipcode"],
            "FAMILY": ["marital_status", "relationship", "record_type"],
            "EMPLOYMENT": ["salary", "employment_type", "employee_class", "department", "hire_date"],
            "HEALTH": ["tobacco_use", "pregnancy_status"],
            "DEPENDENTS": dependent_fields,
            "BENEFITS": ["medical_plan", "dental_plan", "vision_plan", "life_plan", "add_plan", "coverage_tier"],
            "SENSITIVE": ["ssn"]
        }
        
        for category, fields in categories.items():
            formatted.append(f"\n{category}:")
            for field in fields:
                if field in self.system_fields:
                    info = self.system_fields[field]
                    variations = ", ".join(info['variations'][:5])  # Limit variations shown
                    formatted.append(f"  - {field}: {info['description']} (e.g., {variations})")
        
        return "\n".join(formatted)
    
    def _parse_llm_response(self, response_content: str) -> Dict:
        """Parse LLM response and handle both success and error formats."""
        try:
            cleaned_content = response_content.strip()
            start_idx = cleaned_content.find('{')
            end_idx = cleaned_content.rfind('}') + 1
            
            if start_idx == -1 or end_idx == 0:
                raise ValueError("No JSON found in response")
            
            json_str = cleaned_content[start_idx:end_idx]
            result = json.loads(json_str)
            
            return result
            
        except Exception as e:
            logger.error(f"Failed to parse LLM response: {str(e)}")
            raise ValueError(f"Failed to parse LLM response: {str(e)}")
    
    def _validate_universal_mapping(self, mapping_result: Dict, csv_headers: List[str]) -> Dict:
        """Validate that all mandatory fields are mapped."""
        
        # If LLM returned an error, pass it through
        if "error" in mapping_result:
            return {
                "is_valid": False,
                "error_message": mapping_result.get("message", "Mapping error"),
                "missing_mandatory": mapping_result.get("missing_mandatory", [])
            }
        
        # Check mandatory field coverage
        mapped_system_fields = set(mapping_result.values())
        missing_mandatory = []
        
        # Check name fields (need first_name+last_name OR full_name OR name)
        name_fields = ["first_name", "last_name", "full_name", "name"]
        mapped_name_fields = [f for f in name_fields if f in mapped_system_fields]

        if not mapped_name_fields:
            missing_mandatory.append("name_fields")
        else:
            # Validate name field combinations
            has_first_name = "first_name" in mapped_name_fields
            has_last_name = "last_name" in mapped_name_fields
            has_full_name = "full_name" in mapped_name_fields
            has_name = "name" in mapped_name_fields

            # Valid combinations:
            # 1. first_name + last_name
            # 2. full_name alone
            # 3. name alone
            # Invalid: first_name without last_name (unless full_name or name exists)

            if has_first_name and not has_last_name and not has_full_name and not has_name:
                missing_mandatory.append("last_name")  # first_name alone is not sufficient
        
        # Check other mandatory fields
        other_mandatory = ["gender", "zipcode", "marital_status"]
        for field in other_mandatory:
            if field not in mapped_system_fields:
                missing_mandatory.append(field)
        
        if missing_mandatory:
            return {
                "is_valid": False,
                "error_message": f"Mandatory fields not mappable: {', '.join(missing_mandatory)}",
                "missing_mandatory": missing_mandatory
            }
        
        return {
            "is_valid": True,
            "mapped_count": len([v for v in mapping_result.values() if v != "unmapped"]),
            "unmapped_count": len([v for v in mapping_result.values() if v == "unmapped"])
        }
    
    def apply_mapping_to_dataframe(self, df: pd.DataFrame, mapping: Dict) -> pd.DataFrame:
        """Apply field mapping to DataFrame and remove unmapped columns."""
        
        # Create new DataFrame with only mapped columns
        mapped_df = pd.DataFrame()
        
        for csv_header, system_field in mapping.items():
            if csv_header in df.columns and system_field != "unmapped":
                mapped_df[system_field] = df[csv_header]
        
        logger.info(f"Applied mapping: {len(df.columns)} → {len(mapped_df.columns)} columns")
        return mapped_df
