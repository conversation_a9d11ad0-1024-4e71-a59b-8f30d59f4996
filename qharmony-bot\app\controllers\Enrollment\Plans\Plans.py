"""
Plans controller for processing plan documents and extracting highlights using OpenAI.
"""

import json
import logging
import time
import io, os
from typing import Dict, Any, Optional, List

from langchain_openai.chat_models import ChatOpenAI
from langchain_google_genai.chat_models import ChatGoogleGenerativeA<PERSON>
from fastapi import UploadFile
import fitz  # PyMuPDF for PDF processing

from app.DataModels.ResponseModel import ResponseModel
from config.config import config
from .dataModels import PlanDetailsResponse
from .planPrompts import plan_details_extraction_prompt

logger = logging.getLogger(__name__)



class Plans:
    """Controller for plan document processing and analysis."""
    
    def __init__(self):
        self.config = config

        # Initialize OpenAI model (similar to Wellness controller)
        # self.model = ChatOpenAI(
        #     model=self.config.openai_model_name,
        #     temperature=0.3,  # Lower temperature for more consistent extraction
        #     api_key=self.config.openai_key
        # )
        self.model = ChatGoogleGenerativeAI(
            model="gemini-2.5-flash",
            temperature=0.3,  # Lower temperature for more consistent extraction
            api_key=os.getenv("GOOGLE_API_KEY")
        )

        logger.info("Plans controller initialized with OpenAI integration")

    def _extract_text_from_upload_file(self, file: UploadFile, file_content: bytes) -> str:
        """
        Extract text content from an uploaded file buffer.

        Args:
            file (UploadFile): FastAPI UploadFile object with metadata
            file_content (bytes): Binary content of the file

        Returns:
            str: Extracted text content
        """
        try:
            logger.info(f"Extracting text from {file.filename} (type: {file.content_type}, size: {len(file_content)} bytes)")

            if file.content_type == "application/pdf":
                return self._extract_text_from_pdf_bytes(file_content)
            elif file.content_type == "text/plain":
                return file_content.decode('utf-8')
            elif file.content_type in [
                "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
                "application/msword"
            ]:
                return self._extract_text_from_word_bytes(file_content)
            else:
                logger.warning(f"Unsupported file type for text extraction: {file.content_type}")
                return ""

        except Exception as e:
            logger.error(f"Error extracting text from {file.filename}: {str(e)}")
            return ""

    def _extract_text_from_pdf_bytes(self, pdf_bytes: bytes) -> str:
        """Extract comprehensive content from PDF bytes using PyMuPDF."""
        try:
            # Check if we have valid PDF bytes
            if not pdf_bytes or len(pdf_bytes) == 0:
                logger.warning("PDF bytes are empty")
                return ""

            # Create a file-like object from bytes
            pdf_stream = io.BytesIO(pdf_bytes)

            # Open PDF from bytes
            doc = fitz.open(stream=pdf_stream, filetype="pdf")

            # Check if document opened successfully
            if doc is None:
                logger.error("Failed to open PDF document")
                return ""

            # Check if document has pages
            if len(doc) == 0:
                logger.warning("PDF document has no pages")
                doc.close()
                return ""

            # Extract document metadata
            metadata = doc.metadata
            all_content = []

            # Add document metadata if available
            if metadata:
                all_content.append("=== DOCUMENT METADATA ===")
                for key, value in metadata.items():
                    if value:
                        all_content.append(f"{key}: {value}")
                all_content.append("\n")

            # Process each page comprehensively
            for page_num in range(len(doc)):
                try:
                    page = doc.load_page(page_num)
                    all_content.append(f"=== PAGE {page_num + 1} ===")

                    # 1. Extract main text content
                    try:
                        text_content = page.get_text()
                        if text_content.strip():
                            all_content.append("--- TEXT CONTENT ---")
                            all_content.append(text_content)
                            all_content.append("")
                    except Exception as text_error:
                        logger.error(f"Error extracting text from page {page_num + 1}: {str(text_error)}")
                        all_content.append("--- TEXT CONTENT (ERROR) ---")
                        all_content.append(f"Error: {str(text_error)}")
                        all_content.append("")

                except Exception as page_error:
                    logger.error(f"Error processing page {page_num + 1}: {str(page_error)}")
                    all_content.append(f"=== PAGE {page_num + 1} (ERROR) ===")
                    all_content.append(f"Error extracting content: {str(page_error)}")
                    continue

                # 2. Extract links and URLs
                links = page.get_links()
                if links:
                    all_content.append("--- LINKS AND URLS ---")
                    for link in links:
                        if 'uri' in link:
                            all_content.append(f"Link: {link['uri']}")
                        elif 'page' in link:
                            all_content.append(f"Internal link to page: {link['page']}")
                    all_content.append("")

                # 3. Extract images and their context
                image_list = page.get_images()
                if image_list:
                    all_content.append("--- IMAGES ---")
                    for img_index, img in enumerate(image_list):
                        all_content.append(f"Image {img_index + 1}: {img[7]} (xref: {img[0]})")

                        # Try to get image context (text around images)
                        try:
                            # Get image rectangle
                            img_rect = page.get_image_bbox(img)
                            if img_rect:
                                # Get text near the image
                                expanded_rect = fitz.Rect(
                                    img_rect.x0 - 50, img_rect.y0 - 20,
                                    img_rect.x1 + 50, img_rect.y1 + 20
                                )
                                nearby_text = page.get_textbox(expanded_rect)
                                if nearby_text.strip():
                                    all_content.append(f"  Context: {nearby_text.strip()}")
                        except:
                            pass
                    all_content.append("")

                # 4. Extract tables (if any structured content)
                try:
                    # Get text with layout preservation for tables
                    dict_content = page.get_text("dict")
                    tables_found = self._extract_table_content(dict_content)
                    if tables_found:
                        all_content.append("--- TABLES ---")
                        all_content.append(tables_found)
                        all_content.append("")
                except:
                    pass

                # 5. Extract annotations and comments
                annotations = page.annots()
                if annotations:
                    all_content.append("--- ANNOTATIONS ---")
                    for annot in annotations:
                        annot_content = annot.info.get("content", "")
                        annot_type = annot.type[1]  # Get annotation type name
                        if annot_content:
                            all_content.append(f"{annot_type}: {annot_content}")
                    all_content.append("")

                # 6. Extract form fields
                widgets = page.widgets()
                if widgets:
                    all_content.append("--- FORM FIELDS ---")
                    for widget in widgets:
                        field_name = widget.field_name
                        field_value = widget.field_value
                        field_type = widget.field_type_string
                        all_content.append(f"{field_type} '{field_name}': {field_value}")
                    all_content.append("")

                all_content.append("\n")

            # Combine all extracted content before closing document
            final_content = "\n".join(all_content)

            # Get stats before closing
            page_count = len(doc)
            total_links = sum(len(doc[i].get_links()) for i in range(len(doc)))
            total_images = sum(len(doc[i].get_images()) for i in range(len(doc)))

            # Close document
            doc.close()

            logger.info(f"Extracted {len(final_content)} characters from PDF (comprehensive)")
            logger.info(f"  - Pages: {page_count}")
            logger.info(f"  - Links found: {total_links}")
            logger.info(f"  - Images found: {total_images}")

            return final_content.strip()

        except Exception as e:
            logger.error(f"Error extracting comprehensive content from PDF bytes: {str(e)}")

            # Fallback to basic text extraction
            try:
                logger.info("Attempting fallback basic text extraction...")
                pdf_stream = io.BytesIO(pdf_bytes)
                doc = fitz.open(stream=pdf_stream, filetype="pdf")

                basic_text = ""
                for page_num in range(len(doc)):
                    try:
                        page = doc.load_page(page_num)
                        page_text = page.get_text()
                        if page_text.strip():
                            basic_text += f"=== PAGE {page_num + 1} ===\n"
                            basic_text += page_text
                            basic_text += "\n\n"
                    except:
                        continue

                doc.close()

                if basic_text.strip():
                    logger.info(f"Fallback extraction successful: {len(basic_text)} characters")
                    return basic_text.strip()
                else:
                    logger.error("Fallback extraction also failed")
                    return ""

            except Exception as fallback_error:
                logger.error(f"Fallback extraction failed: {str(fallback_error)}")
                return ""

    def _extract_table_content(self, dict_content: dict) -> str:
        """Extract table-like content from PDF dictionary structure."""
        try:
            table_content = []

            # Look for blocks that might represent tables
            for block in dict_content.get("blocks", []):
                if "lines" in block:
                    # Check if this block has table-like structure
                    lines = block["lines"]
                    if len(lines) > 1:
                        # Look for aligned text that might be tabular
                        line_texts = []
                        for line in lines:
                            line_text = ""
                            for span in line.get("spans", []):
                                line_text += span.get("text", "")
                            if line_text.strip():
                                line_texts.append(line_text.strip())

                        # If we have multiple lines with similar structure, treat as table
                        if len(line_texts) > 1:
                            # Check for common table indicators
                            table_indicators = ["$", "%", ":", "|", "\t"]
                            has_table_chars = any(
                                any(indicator in line for indicator in table_indicators)
                                for line in line_texts
                            )

                            if has_table_chars:
                                table_content.append("Table content:")
                                for line in line_texts:
                                    table_content.append(f"  {line}")
                                table_content.append("")

            return "\n".join(table_content) if table_content else ""

        except Exception as e:
            logger.error(f"Error extracting table content: {str(e)}")
            return ""

    def _extract_text_from_word_bytes(self, word_bytes: bytes) -> str:
        """Extract text from Word document bytes."""
        try:
            # For now, return a placeholder - would need python-docx for full implementation
            logger.warning("Word document text extraction not fully implemented - returning placeholder")
            return f"[Word document content - {len(word_bytes)} bytes - text extraction requires python-docx library]"

        except Exception as e:
            logger.error(f"Error extracting text from Word bytes: {str(e)}")
            return ""

    def _extract_plan_details_with_openai(self, combined_content: str) -> Optional[PlanDetailsResponse]:
        """
        Extract plan details using OpenAI API.

        Args:
            combined_content (str): Combined text content from all plan documents

        Returns:
            Optional[PlanDetailsResponse]: Extracted plan details or None if failed
        """
        try:
            logger.info(f"Extracting plan details from {len(combined_content)} characters of content")

            # Prepare the prompt with the document content
            prompt = plan_details_extraction_prompt.format(
                plan_documents=combined_content
            )
            
            # Call OpenAI API
            start_time = time.time()
            response = self.model.invoke(prompt)
            api_time = time.time() - start_time

            logger.info(f"OpenAI API call completed in {api_time:.2f} seconds")

            # Extract the content
            response_content = response.content.strip()
            logger.info(f"Raw OpenAI response length: {len(response_content)} characters")
            logger.debug(f"Raw response first 200 chars: {response_content[:200]}")

            # Clean the response content more aggressively
            response_content = response_content.strip()

            # Remove any markdown code blocks if present
            if response_content.startswith('```json'):
                response_content = response_content.replace('```json', '').replace('```', '').strip()
            elif response_content.startswith('```'):
                response_content = response_content.replace('```', '').strip()

            # Remove any leading/trailing whitespace and newlines
            response_content = response_content.strip('\n\r\t ')

            # Find the JSON object boundaries more reliably
            start_idx = response_content.find('{')
            end_idx = response_content.rfind('}') + 1

            if start_idx >= 0 and end_idx > start_idx:
                response_content = response_content[start_idx:end_idx]
                logger.info(f"Extracted JSON boundaries: {len(response_content)} characters")

            logger.debug(f"Cleaned response first 200 chars: {response_content[:200]}")

            # Try to parse as JSON
            try:
                logger.info("Attempting to parse JSON response")
                details_dict = json.loads(response_content)
                logger.info(f"JSON parsing successful, keys: {list(details_dict.keys())}")

                # Validate and create PlanDetailsResponse object
                logger.info("Creating PlanDetailsResponse object from parsed JSON")
                plan_details = PlanDetailsResponse(**details_dict)

                logger.info(f"Successfully extracted {len(plan_details.plans)} plan(s)")
                for i, plan in enumerate(plan_details.plans):
                    logger.info(f"Plan {i+1}: {plan.plan_name} ({plan.coverage_type})")
                return plan_details

            except Exception as pydantic_error:
                logger.error(f"Pydantic validation error: {str(pydantic_error)}")
                logger.error(f"Error type: {type(pydantic_error)}")
                if hasattr(pydantic_error, 'errors'):
                    logger.error(f"Validation errors: {pydantic_error.errors()}")
                # Continue to JSON parsing error handling

            except json.JSONDecodeError as e:
                logger.error(f"Failed to parse OpenAI response as JSON: {str(e)}")
                logger.error(f"Response content length: {len(response_content)}")
                logger.error(f"First 200 chars: {response_content[:200]}")
                logger.error(f"Last 200 chars: {response_content[-200:]}")

                # Try to fix common JSON issues
                try:
                    # Remove any leading/trailing non-JSON content
                    start_idx = response_content.find('{')
                    end_idx = response_content.rfind('}') + 1

                    if start_idx >= 0 and end_idx > start_idx:
                        cleaned_content = response_content[start_idx:end_idx]
                        logger.info(f"Attempting to parse cleaned JSON: {cleaned_content[:100]}...")

                        details_dict = json.loads(cleaned_content)
                        logger.info("JSON parsing successful, creating PlanDetailsResponse object")
                        plan_details = PlanDetailsResponse(**details_dict)

                        logger.info(f"Successfully parsed cleaned JSON with {len(plan_details.plans)} plan(s)")
                        return plan_details
                    else:
                        logger.error("Could not find valid JSON structure in response")
                        return None

                except Exception as cleanup_error:
                    logger.error(f"Failed to parse cleaned JSON: {str(cleanup_error)}")

                    # Last resort: try to create a minimal valid response
                    logger.info("Attempting to create fallback response")
                    try:
                        fallback_details = {
                            "plans": [
                                {
                                    "coverage_category": "Ancillary Benefits",
                                    "coverage_type": "Dental",
                                    "plan_name": "Document Processing Result",
                                    "plan_code": "DOC_PROC_001",
                                    "plan_type": "DHMO",
                                    "metal_tier": "Not specified",
                                    "coverage_period": "Not specified",
                                    "eligibility": {
                                        "employee_class_types": ["Full-Time"],
                                        "dependent_coverage": "Not specified",
                                        "waiting_period": "Not specified",
                                        "effective_date": "Not specified"
                                    },
                                    "coverage_tiers": [
                                        {
                                            "tier_name": "Employee Only",
                                            "total_premium": "Not specified",
                                            "employee_premium": "Not specified",
                                            "employer_premium": "Not specified",
                                            "deductible": "Not specified",
                                            "out_of_pocket_max": "Not specified"
                                        }
                                    ],
                                    "benefits": [
                                        {
                                            "benefit_category": "Claims Processing",
                                            "benefit_description": "Real-time claims submission and tracking",
                                            "coverage_details": "Provider tools available for claims management",
                                            "coverage_limit": "Not specified"
                                        }
                                    ],
                                    "cost_details": {
                                        "copayments": [],
                                        "coinsurance": "Not specified",
                                        "prescription_coverage": "Not specified"
                                    },
                                    "network_info": {
                                        "provider_network": "Delta Dental network",
                                        "in_network_coverage": "Not specified",
                                        "out_of_network_coverage": "Not specified",
                                        "restrictions": "Not specified"
                                    },
                                    "important_details": [
                                        {
                                            "category": "Claims Submission",
                                            "details": "Real-time claims processing available through provider tools"
                                        }
                                    ],
                                    "key_dates": [],
                                    "special_features": [
                                        {
                                            "feature": "Provider Tools",
                                            "description": "Comprehensive tools for claims management and processing"
                                        }
                                    ],
                                    "contact_info": {
                                        "customer_service": "Not specified",
                                        "website": "deltadentalins.com",
                                        "email": "Not specified",
                                        "additional_contacts": "Not specified"
                                    }
                                }
                            ]
                        }

                        plan_details = PlanDetailsResponse(**fallback_details)
                        logger.info("Successfully created fallback response")
                        return plan_details

                    except Exception as fallback_error:
                        logger.error(f"Failed to create fallback response: {str(fallback_error)}")
                        return None

            except Exception as e:
                logger.error(f"Failed to create PlanDetailsResponse object: {str(e)}")
                logger.error(f"Details dict keys: {list(details_dict.keys()) if 'details_dict' in locals() else 'N/A'}")
                return None

        except Exception as e:
            logger.error(f"Error in OpenAI plan details extraction: {str(e)}")
            logger.error(f"Error type: {type(e)}")
            logger.error(f"Full response content: {response_content if 'response_content' in locals() else 'Not available'}")
            import traceback
            logger.error(f"Full traceback: {traceback.format_exc()}")
            return None
    

    


    async def process_uploaded_files(self, files: List[UploadFile], plan_id: Optional[str] = None) -> ResponseModel:
        """
        Process uploaded files with binary buffers and extract plan highlights using OpenAI.

        Args:
            files (List[UploadFile]): List of uploaded files with binary content
            plan_id (Optional[str]): Optional plan identifier

        Returns:
            ResponseModel: Processing results with extracted highlights
        """
        start_time = time.time()
        plan_id = plan_id or "uploaded_files"

        logger.info(f"Processing {len(files)} uploaded files for plan ID: {plan_id}")

        try:
            # Step 1: Process uploaded files
            logger.info("Step 1: Processing uploaded files")
            extracted_content = []
            extracted_texts = []  # Store actual text content separately
            successful_processing = 0

            for file in files:
                logger.info(f"Processing file: {file.filename} (type: {file.content_type})")

                # Read file content as bytes
                file_content = await file.read()

                # Extract text from the binary content
                extracted_text = self._extract_text_from_upload_file(file, file_content)

                if extracted_text and len(extracted_text.strip()) > 0:
                    extracted_content.append({
                        "filename": file.filename,
                        "content_type": file.content_type,
                        "content_length": len(extracted_text),
                        "file_size_bytes": len(file_content),
                        "status": "success"
                    })
                    extracted_texts.append({
                        "filename": file.filename,
                        "content": extracted_text
                    })
                    successful_processing += 1
                    logger.info(f"Successfully processed '{file.filename}' - extracted {len(extracted_text)} characters")
                else:
                    extracted_content.append({
                        "filename": file.filename,
                        "content_type": file.content_type,
                        "content_length": 0,
                        "file_size_bytes": len(file_content),
                        "status": "extraction_failed"
                    })
                    logger.warning(f"Failed to extract text from '{file.filename}'")

            if successful_processing == 0:
                return ResponseModel(
                    success=False,
                    status_code=400,
                    message="No content could be extracted from any of the uploaded files",
                    data={
                        "plan_id": plan_id,
                        "status": "no_content_extracted",
                        "files_processed": len(files),
                        "successful_extractions": 0
                    }
                )

            # Step 2: Combine content from all files
            logger.info("Step 2: Combining content from all files")
            combined_content = []
            for doc in extracted_texts:
                combined_content.append(f"=== Document: {doc['filename']} ===\n")
                combined_content.append(doc["content"])
                combined_content.append("\n\n")

            combined_text = "".join(combined_content)

            if not combined_text or len(combined_text.strip()) == 0:
                return ResponseModel(
                    success=False,
                    status_code=400,
                    message="No content could be combined from the uploaded files",
                    data={
                        "plan_id": plan_id,
                        "status": "no_combined_content",
                        "files_processed": len(files),
                        "successful_extractions": successful_processing
                    }
                )

            # Step 3: Extract plan details using OpenAI
            logger.info("Step 3: Extracting plan details using OpenAI")
            plan_details = self._extract_plan_details_with_openai(combined_text)

            if not plan_details:
                logger.error("Failed to extract plan details using OpenAI")
                return ResponseModel(
                    success=False,
                    status_code=500,
                    message="Failed to extract plan details using OpenAI",
                    data={
                        "plan_id": plan_id,
                        "status": "openai_extraction_failed",
                        "files_processed": len(files),
                        "successful_extractions": successful_processing,
                        "content_length": len(combined_text)
                    }
                )

            # Step 4: Return successful response
            processing_time = time.time() - start_time
            logger.info(f"Successfully processed uploaded files for plan {plan_id} in {processing_time:.2f} seconds")

            return ResponseModel(
                data={
                    "plan_id": plan_id,
                    "status": "success",
                    "files_processed": len(files),
                    "successful_extractions": successful_processing,
                    "plan_details": plan_details.model_dump(),
                    "raw_content_length": len(combined_text),
                    "processing_time": processing_time,
                    "file_details": extracted_content
                },
                message=f"Successfully processed {successful_processing} out of {len(files)} files and extracted plan details for {len(plan_details.plans)} plan(s)"
            )

        except Exception as e:
            processing_time = time.time() - start_time
            logger.error(f"Error processing uploaded files for plan {plan_id}: {str(e)}")

            return ResponseModel(
                success=False,
                status_code=500,
                message=f"Error processing uploaded files: {str(e)}",
                data={
                    "plan_id": plan_id,
                    "status": "error",
                    "error": str(e),
                    "processing_time": processing_time
                }
            )
