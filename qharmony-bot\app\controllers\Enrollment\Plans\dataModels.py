"""
Data models for Plans controller.
"""

from typing import List, Dict, Optional, Any
from pydantic import BaseModel, Field
from fastapi import UploadFile


class PlanProcessingRequest(BaseModel):
    """Request model for plan document processing."""
    plan_id: str = Field(..., description="Identifier for the plan to process")


class Eligibility(BaseModel):
    """Eligibility information for a plan."""
    employee_class_types: List[str] = Field(default_factory=list, description="Types of employees eligible")
    dependent_coverage: str = Field(default="Not specified", description="Dependent coverage rules")
    waiting_period: str = Field(default="Not specified", description="Waiting period for coverage")
    effective_date: str = Field(default="Not specified", description="Coverage effective date")


class PlanHighlights(BaseModel):
    """Plan highlights and key information."""
    key_features: List[str] = Field(default_factory=list, description="Key features of the plan")
    main_benefits: List[str] = Field(default_factory=list, description="Main benefits offered")
    coverage_summary: str = Field(default="Not specified", description="Summary of coverage")
    important_notes: List[str] = Field(default_factory=list, description="Important notes and restrictions")


class CoverageTier(BaseModel):
    """Coverage tier with costs."""
    tier_name: str = Field(..., description="Name of the coverage tier")
    total_premium: str = Field(default="Not specified", description="Total premium cost")
    employee_premium: str = Field(default="Not specified", description="Employee premium contribution")
    employer_premium: str = Field(default="Not specified", description="Employer premium contribution")


class Benefit(BaseModel):
    """Individual benefit details."""
    benefit_category: str = Field(..., description="Category of the benefit")
    benefit_description: str = Field(..., description="Description of the benefit")
    coverage_details: str = Field(default="Not specified", description="Detailed coverage information")
    coverage_limit: str = Field(default="Not specified", description="Coverage limits or maximums")


class Copayment(BaseModel):
    """Copayment information."""
    service_type: str = Field(..., description="Type of service")
    copay_amount: str = Field(..., description="Copayment amount")


class DeductibleInfo(BaseModel):
    """Deductible information."""
    individual: str = Field(default="Not specified", description="Individual deductible amount")
    family: str = Field(default="Not specified", description="Family deductible amount")


class OutOfPocketMax(BaseModel):
    """Out-of-pocket maximum information."""
    individual: str = Field(default="Not specified", description="Individual out-of-pocket maximum")
    family: str = Field(default="Not specified", description="Family out-of-pocket maximum")


class CostDetails(BaseModel):
    """Detailed cost information."""
    deductible: DeductibleInfo = Field(default_factory=DeductibleInfo, description="Deductible information")
    out_of_pocket_max: OutOfPocketMax = Field(default_factory=OutOfPocketMax, description="Out-of-pocket maximum")
    copayments: List[Copayment] = Field(default_factory=list, description="List of copayments")
    coinsurance: str = Field(default="Not specified", description="Coinsurance percentage")
    prescription_coverage: str = Field(default="Not specified", description="Prescription drug coverage details")


class NetworkInfo(BaseModel):
    """Network and provider information."""
    provider_network: str = Field(default="Not specified", description="Provider network name")
    in_network_coverage: str = Field(default="Not specified", description="In-network coverage details")
    out_of_network_coverage: str = Field(default="Not specified", description="Out-of-network coverage details")
    restrictions: str = Field(default="Not specified", description="Network restrictions")


class ImportantDetail(BaseModel):
    """Important plan details."""
    category: str = Field(..., description="Category of the detail")
    details: str = Field(..., description="Detailed information")


class KeyDate(BaseModel):
    """Important dates."""
    event: str = Field(..., description="Event or deadline")
    date: str = Field(..., description="Date of the event")


class SpecialFeature(BaseModel):
    """Special features or programs."""
    feature: str = Field(..., description="Feature name")
    description: str = Field(..., description="Feature description")


class ContactInfo(BaseModel):
    """Contact information."""
    customer_service: str = Field(default="Not specified", description="Customer service contact")
    website: str = Field(default="Not specified", description="Website URL")
    email: str = Field(default="Not specified", description="Email contact")
    additional_contacts: str = Field(default="Not specified", description="Additional contact information")


class PlanDetail(BaseModel):
    """Detailed information for a single plan coverage type."""
    coverage_category: str = Field(..., description="Coverage category (Health Insurance, Ancillary Benefits, etc.)")
    coverage_type: str = Field(..., description="Coverage type (Medical, Dental, Vision, etc.)")
    plan_name: str = Field(..., description="Name of the plan")
    plan_code: str = Field(default="Not specified", description="Plan code or identifier")
    plan_type: str = Field(default="Not specified", description="Type of plan (PPO, HMO, etc.)")
    metal_tier: str = Field(default="Not specified", description="Metal tier (Bronze, Silver, Gold, etc.)")
    coverage_period: str = Field(default="Not specified", description="Coverage period")
    plan_highlights: PlanHighlights = Field(default_factory=PlanHighlights, description="Plan highlights and key information")
    eligibility: Eligibility = Field(default_factory=Eligibility, description="Eligibility information")
    coverage_tiers: List[CoverageTier] = Field(default_factory=list, description="Available coverage tiers")
    benefits: List[Benefit] = Field(default_factory=list, description="Plan benefits")
    cost_details: CostDetails = Field(default_factory=CostDetails, description="Cost details")
    network_info: NetworkInfo = Field(default_factory=NetworkInfo, description="Network information")
    important_details: List[ImportantDetail] = Field(default_factory=list, description="Important details")
    key_dates: List[KeyDate] = Field(default_factory=list, description="Key dates")
    special_features: List[SpecialFeature] = Field(default_factory=list, description="Special features")
    contact_info: ContactInfo = Field(default_factory=ContactInfo, description="Contact information")


class PlanDetailsResponse(BaseModel):
    """Response model containing extracted plan details."""
    plans: List[PlanDetail] = Field(..., description="List of extracted plan details")


class PlanProcessingResponse(BaseModel):
    """Response model for plan processing."""
    plan_id: str = Field(..., description="Plan identifier")
    status: str = Field(..., description="Processing status")
    message: str = Field(..., description="Processing message")
    documents_processed: int = Field(..., description="Number of documents processed")
    plan_details: Optional[PlanDetailsResponse] = Field(None, description="Extracted plan details")
    raw_content_length: int = Field(default=0, description="Total length of extracted content")
    processing_time: float = Field(default=0.0, description="Processing time in seconds")
