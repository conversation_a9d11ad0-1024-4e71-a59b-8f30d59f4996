"""
Prompts for plan document analysis using OpenAI.
"""

plan_details_extraction_prompt = """
You are an expert benefits analyst with strict analytical standards. Your task is to carefully analyze plan documents and extract ONLY the distinct, separate insurance plans that are clearly defined in the document.

PLAN DOCUMENTS:
{plan_documents}

**CRITICAL ANALYSIS REQUIREMENTS:**

🔍 **STEP 1: THOROUGH DOCUMENT ANALYSIS**
Before extracting any plans, you MUST:
1. Read the ENTIRE document carefully
2. Identify distinct plan sections or coverage areas
3. Distinguish between separate plans vs. components of the same plan
4. Verify each plan has its own coverage details, costs, and structure

⚠️ **AVOID FALSE POSITIVES:**
- DO NOT create separate plans for components that belong to the same plan
- DO NOT split a single plan into multiple plans based on benefit categories
- DO NOT create plans for brief mentions or references without full plan details
- DO NOT assume similar coverage types are separate plans without clear evidence

**COVERAGE CATEGORY TO TYPE MAPPING:**
Map coverage types EXACTLY to these predefined categories:

**Health Insurance**: Medical
**Ancillary Benefits**: Dental, Vision
**Life & Disability Insurance**: Term Life, Supplemental Life Insurance, Short-Term Disability, Long-Term Disability, Whole Life, Group (Employer) Life, Accidental Death & Dismemberment (AD&D)
**Voluntary Benefits**: Hospital Indemnity, Accident Insurance, Critical Illness Insurance, Cancer Insurance, Gap Insurance, Legal Insurance, Identity Theft Protection, Accident & Illness (Pets), Nursing Care / Custodial Care
**Wellness & Mental Health**: Wellness Programs, Employee Assistance Program, Gym Membership
**Spending & Savings Accounts**: Health Savings Account, Flexible Savings Accounts, Commuter Benefits, Technology Stipend
**Financial Benefits**: Pay & Bonus, Stock Options, Student Loan Assistance
**Retirement Benefits**: 401(k), 403(b), Pension Plan
**Time Off & Leave**: Paid Time Off (PTO), Parental Leave, Family and Medical Leave, Paid Volunteer Time
**Family & Caregiver Support**: On-site Child Care
**Career & Development**: Employee Training & Development, Tuition Reimbursement, Employee Recognition, Performance Goals & Process
**Workplace Environment**: Pet-friendly Workplace, Ergonomic Workplace, Company Handbook
**Life Events**: Marriage or Divorce, New Baby or Adoption, Loss of Insurance

**STRICT COVERAGE TYPE IDENTIFICATION EXAMPLES:**
- If document says "dental coverage" → map to "Dental"
- If document says "vision benefits" → map to "Vision"
- If document says "life insurance" → analyze context to determine "Term Life", "Group (Employer) Life", etc.
- If document says "accident protection" → map to "Accident Insurance"
- If document says "critical illness" → map to "Critical Illness Insurance"
- If unclear, choose the CLOSEST match from the predefined list

**PLAN TYPES:** PPO, HMO, HDHP, MEC, EPO, POS, Indemnity, DHMO, Vision, Hospital Indemnity, Accident, Critical Illness, Term Life, Whole Life, STD, LTD
**COVERAGE TIERS (extract costs for these 4 standard tiers):**
1. Employee Only
2. Employee + Spouse
3. Employee + Child(ren)
4. Family

Note: Extract premium costs and employee/employer breakdown for each applicable tier. Use "Not specified" if a tier is not available.
**METAL TIERS:** Bronze, Silver, Gold, Platinum, Catastrophic
**EMPLOYEE CLASS TYPES:** Full-Time, Part-Time, Contractor, Temporary, Seasonal

🔍 **STEP 2: PLAN QUALIFICATION CRITERIA**
A section qualifies as a SEPARATE PLAN only if it has ALL of these:
1. **Distinct Coverage Type**: Clear identification of what is covered (Medical, Dental, Vision, etc.)
2. **Separate Cost Structure**: Its own premiums, deductibles, or cost information
3. **Independent Benefits**: Specific benefits that are not part of another plan
4. **Standalone Operation**: Can function independently from other coverage
5. **Unique Plan Code**: Different plan codes indicate separate plans (same code = same plan)
6. **Independent Enrollment**: Can be enrolled in separately from other coverage

❌ **DO NOT CREATE SEPARATE PLANS FOR:**
- Benefit categories within a single plan (e.g., preventive vs. major services in dental)
- Different service types under one coverage (e.g., office visits vs. prescriptions in medical)
- Administrative sections or general information
- Brief mentions without full plan details
- Features or benefits that are part of a larger plan (e.g., accelerated death benefit is a feature, not a separate plan)
- Different benefit levels of the same coverage type (e.g., basic vs. enhanced dental)

✅ **CREATE SEPARATE PLANS ONLY FOR:**
- Completely different insurance products (Medical vs. Dental vs. Vision vs. Term Life vs. AD&D)
- Plans with different carriers or administrators
- Plans with completely separate enrollment and cost structures
- Voluntary vs. employer-provided versions of the same coverage type
- Different coverage types from our predefined mapping (Term Life and AD&D are separate coverage types)

🔍 **STEP 3: LOGICAL ANALYSIS PROCESS**
For each potential plan you identify:

1. **Evidence Check**: What specific evidence proves this is a separate plan?
2. **Cost Verification**: Does it have its own distinct pricing structure?
3. **Benefit Verification**: Are the benefits unique and not part of another plan?
4. **Independence Test**: Could an employee enroll in this separately from other plans?
5. **Plan Code Analysis**: Check if different coverage types share the same plan code (if so, they're one plan)
6. **Bundle Analysis**: Are multiple coverage types sold together as one insurance product?

**🏷️ PLAN CODE LOGIC:**
- Different plan codes = Different plans (each code represents a separate insurance product)
- Same plan code = Same plan (but verify coverage types match our mapping)
- No plan code = Use coverage type and cost structure to determine separation

**DETAILED EXTRACTION REQUIREMENTS:**

For each QUALIFIED plan, extract:

1. **Coverage Classification** (MANDATORY):
   - Coverage category from predefined list
   - Coverage type mapped exactly to our conventions
   - Plan type (PPO, HMO, etc.) based on actual document content
   - Metal tier only if explicitly mentioned

2. **Plan Identification** (REQUIRED):
   - Use exact plan name from document OR create logical name
   - Use exact plan code from document OR generate based on coverage type
   - Ensure names clearly distinguish between different plans

3. **Plan Highlights** (REQUIRED):
   - Extract 3-5 key features that make this plan unique
   - List 3-5 main benefits offered by the plan
   - Provide a concise coverage summary (1-2 sentences)
   - Include important notes, restrictions, or special conditions

4. **Financial Structure** (CRITICAL):
   - Extract premium costs for the 4 standard coverage tiers.
   - Sometimes one or more coverage tiers might be mentioned in the document.
   - Show total premium and employee/employer breakdown for each tier
   - Place deductibles and out-of-pocket maximums in cost_details section
   - Include copays, coinsurance, and prescription coverage details

4. **Benefit Details** (COMPREHENSIVE):
   - List specific covered services
   - Include coverage percentages and limits
   - Note any exclusions or restrictions
   - Identify network requirements

5. **Administrative Details**:
   - Eligibility requirements
   - Enrollment periods and effective dates
   - Contact information for the specific plan
   - Claims and appeals processes

Return this exact JSON structure (array of plan details):

{{
    "plans": [
        {{
            "coverage_category": "string",
            "coverage_type": "string",
            "plan_name": "string",
            "plan_code": "string",
            "plan_type": "string",
            "metal_tier": "string",
            "coverage_period": "string",
            "plan_highlights": {{
                "key_features": ["string"],
                "main_benefits": ["string"],
                "coverage_summary": "string",
                "important_notes": ["string"]
            }},
            "eligibility": {{
                "employee_class_types": ["string"],
                "dependent_coverage": "string",
                "waiting_period": "string",
                "effective_date": "string"
            }},
            "coverage_tiers": [
                {{
                    "tier_name": "Employee Only",
                    "total_premium": "string",
                    "employee_premium": "string",
                    "employer_premium": "string"
                }},
                {{
                    "tier_name": "Employee + Spouse",
                    "total_premium": "string",
                    "employee_premium": "string",
                    "employer_premium": "string"
                }},
                {{
                    "tier_name": "Employee + Child(ren)",
                    "total_premium": "string",
                    "employee_premium": "string",
                    "employer_premium": "string"
                }},
                {{
                    "tier_name": "Family",
                    "total_premium": "string",
                    "employee_premium": "string",
                    "employer_premium": "string"
                }}
            ],
            "benefits": [
                {{
                    "benefit_category": "string",
                    "benefit_description": "string",
                    "coverage_details": "string",
                    "coverage_limit": "string"
                }}
            ],
            "cost_details": {{
                "deductible": {{
                    "individual": "string",
                    "family": "string"
                }},
                "out_of_pocket_max": {{
                    "individual": "string",
                    "family": "string"
                }},
                "copayments": [
                    {{
                        "service_type": "string",
                        "copay_amount": "string"
                    }}
                ],
                "coinsurance": "string",
                "prescription_coverage": "string"
            }},
            "network_info": {{
                "provider_network": "string",
                "in_network_coverage": "string",
                "out_of_network_coverage": "string",
                "restrictions": "string"
            }},
            "important_details": [
                {{
                    "category": "string",
                    "details": "string"
                }}
            ],
            "key_dates": [
                {{
                    "event": "string",
                    "date": "string"
                }}
            ],
            "special_features": [
                {{
                    "feature": "string",
                    "description": "string"
                }}
            ],
            "contact_info": {{
                "customer_service": "string",
                "website": "string",
                "email": "string",
                "additional_contacts": "string"
            }}
        }}
    ]
}}

**🚨 FINAL VALIDATION CHECKLIST:**

Before creating each plan object, verify:
1. ✅ This plan has its own distinct coverage area
2. ✅ This plan has separate cost/premium information
3. ✅ This plan can be enrolled in independently
4. ✅ This plan is not just a benefit category of another plan
5. ✅ The coverage type maps exactly to our predefined list

**STRICT QUALITY CONTROLS:**

1. **Plan Count Accuracy**:
   - If document mentions "4 plans available" but you find 6, RE-ANALYZE
   - Look for evidence of what constitutes separate plans vs. plan components
   - Verify each plan meets the qualification criteria above

2. **Coverage Type Precision**:
   - Map "dental coverage" → "Dental" (not "Dental Coverage")
   - Map "vision benefits" → "Vision" (not "Vision Benefits")
   - Map "group life" → "Group (Employer) Life" (exact match from list)
   - Map "accident protection" → "Accident Insurance" (exact match)

3. **Plan Name Logic**:
   - Use document's exact plan name if clearly stated
   - If unclear, create: "[Company] [Coverage Type] [Plan Type]"
   - Example: "ABC Company Dental PPO" not "Dental Plan"

4. **Cost Data Integrity**:
   - Include currency symbols ($) with amounts
   - Specify time periods (monthly, annual) for premiums
   - Use "Not specified" only when truly unavailable
   - Extract exact amounts, not ranges unless document provides ranges

5. **JSON Structure Compliance**:
   - Return ONLY valid JSON with no additional text
   - Start with {{ and end with }}
   - Each plan must have all required fields
   - Use "Not specified" for missing optional information

**COMMON MISTAKES TO AVOID:**
❌ Creating separate plans for "Basic" and "Major" dental services (these are benefit categories)
❌ Splitting life insurance into "Death Benefit" and "Accelerated Benefit" plans (these are features)
❌ Making separate plans for different coverage tiers (these are enrollment options)
❌ Creating plans for administrative sections or contact information
❌ Combining different coverage types that have separate plan codes
❌ Ignoring the predefined coverage type mapping

**ANALYSIS EXAMPLES:**
✅ CORRECT: Document has Medical, Dental, Vision, Term Life, AD&D → 5 separate plans
❌ INCORRECT: Document has Medical with preventive/major services → 2 plans (should be 1)
✅ CORRECT: Document has Term Life and AD&D with different plan codes → 2 separate plans
✅ CORRECT: Document has Group Life and Voluntary Life → 2 separate plans
❌ INCORRECT: Document has Dental with basic/major services → 2 plans (should be 1)

**FINAL INSTRUCTION:**
Analyze the document thoroughly. Count the actual distinct insurance products. Extract ONLY those that meet all qualification criteria. Return precise, accurate JSON with no false positives.

Extract plan details as JSON:
"""
