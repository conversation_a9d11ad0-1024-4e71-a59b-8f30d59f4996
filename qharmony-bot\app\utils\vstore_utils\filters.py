from typing import List
from config.config import config

def metadata_similarity_filter(k: int = config.k_filter,metadata_key:str=None, ids: List[str]=[]):
    return {"filter": {metadata_key: {"$in": ids}}, "k":k}

def faq_document_type_filter(k: int = config.k_filter, document_type: str = "QnA", category: str = "QnA"):
    """
    Filter for FAQ documents by document type and category.

    Args:
        k (int): Number of results to return
        document_type (str): Type of document (default: "faq")
        category (str): Category of document (default: "enrollment_benefits")

    Returns:
        dict: Filter configuration for FAQ documents
    """
    return {
        "filter": {
            "document_type": document_type,
            "category": category
        },
        "k": k
    }

def faq_subcategory_filter(k: int = config.k_filter, subcategories: List[str] = None):
    """
    Filter for FAQ documents by specific subcategories.

    Args:
        k (int): Number of results to return
        subcategories (List[str]): List of subcategories to filter by

    Returns:
        dict: Filter configuration for FAQ subcategories
    """
    if subcategories is None:
        subcategories = ["health_insurance", "hsa_fsa", "eligibility_enrollment"]

    return {
        "filter": {
            "document_type": "faq",
            "subcategory": {"$in": subcategories}
        },
        "k": k
    }

def faq_question_type_filter(k: int = config.k_filter, question_types: List[str] = None):
    """
    Filter for FAQ documents by question types.

    Args:
        k (int): Number of results to return
        question_types (List[str]): List of question types to filter by

    Returns:
        dict: Filter configuration for FAQ question types
    """
    if question_types is None:
        question_types = ["definitional", "procedural"]

    return {
        "filter": {
            "document_type": "faq",
            "question_type": {"$in": question_types}
        },
        "k": k
    }

def faq_combined_filter(k: int = config.k_filter, subcategories: List[str] = None,
                       question_types: List[str] = None, complexity_levels: List[str] = None):
    """
    Combined filter for FAQ documents with multiple criteria.

    Args:
        k (int): Number of results to return
        subcategories (List[str]): List of subcategories to filter by
        question_types (List[str]): List of question types to filter by
        complexity_levels (List[str]): List of complexity levels to filter by

    Returns:
        dict: Filter configuration for combined FAQ criteria
    """
    filter_dict = {
        "document_type": "faq",
        "category": "enrollment_benefits"
    }

    if subcategories:
        filter_dict["subcategory"] = {"$in": subcategories}

    if question_types:
        filter_dict["question_type"] = {"$in": question_types}

    if complexity_levels:
        filter_dict["complexity_level"] = {"$in": complexity_levels}

    return {
        "filter": filter_dict,
        "k": k
    }