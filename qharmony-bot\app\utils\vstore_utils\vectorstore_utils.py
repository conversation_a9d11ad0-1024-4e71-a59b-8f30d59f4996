
from pymongo import Mongo<PERSON>lient
from bson import ObjectId
from typing import List, Dict, Optional
import logging
from app.Tools.mongodbTools import get_mongo_collection,get_mongo_db
from langchain.text_splitter import RecursiveCharacterTextSplitter
# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)
from azure.storage.blob import BlobServiceClient
import uuid
import os
from typing import Dict
from langchain.schema import Document
from azure.storage.blob import BlobServiceClient
import uuid
from app.Tools.vectorStore import VectorStore
from pydantic import BaseModel, Field
from typing import Union, List
from app.DataModels.dataModels import PineconeInput
from app.utils.Cache.lru_cache import async_lru_cache
from config.config import config

@async_lru_cache(maxsize=128, ttl=120)
async def fetch_user_data(user_identity: str, mongo_client: MongoClient, db_name: str = config.mongo_db, by: str="email") -> Dict[str, any]:
    """ 
    Fetch user data from MongoDB by different identifiers.
    
    Args:
        user_identity (str): The user identifier (email, _id, or name)
        mongo_client (MongoClient): MongoDB client instance
        db_name (str): Database name
        by (str): Identifier type - "email", "id", or "name"
    
    Returns:
        Dict[str, any]: User data or ValueError if user not found
    """
    user_collection = get_mongo_collection(mongo_client=mongo_client, collection_name="users", db_name=db_name)
    
    if by == "email":
        user_data = user_collection.find_one({"email": user_identity.lower()})
        if not user_data:
            logger.warning(f"No user found with email: {user_identity}")
            return ValueError(f"No user found with email: {user_identity}")
        print("user_data >>", user_data)
    
    elif by == "id":
        try:
            user_data = user_collection.find_one({"_id": ObjectId(user_identity)})
            if not user_data:
                logger.warning(f"No user found with _id: {user_identity}")
                return ValueError(f"No user found with _id: {user_identity}")
            print("user_data >>", user_data)
        except Exception as e:
            logger.error(f"Error finding user by ID: {str(e)}")
            return ValueError(f"Invalid user ID format: {user_identity}")
    
    elif by == "name":
        # Using case-insensitive regex search for name
        user_data = user_collection.find_one({"name": {"$regex": f"^{user_identity}$", "$options": "i"}})
        if not user_data:
            logger.warning(f"No user found with name: {user_identity}")
            return ValueError(f"No user found with name: {user_identity}")
        print("user_data >>", user_data)
    
    else:
        logger.error(f"Invalid 'by' parameter: {by}")
        return ValueError(f"Invalid 'by' parameter: {by}. Must be 'email', 'id', or 'name'")
    
    return user_data


@async_lru_cache(maxsize=128, ttl=120)
async def fetch_document_ids(user_id: int, team_id: int,vector_store: VectorStore, mongo_client: MongoClient, mongo_db_name: str = config.mongo_db) -> List[Document]:
    document_ids = []
    group_data={}
    index_name=config.index_name
    namespace=f"{config.namespace_prefix}-{team_id}"
    # print(index_name,namespace)
    vector_store.load_vector_store(index_name=index_name,namespace=namespace)
    # print(vector_store.vector_store.index_params)
    user_collection = get_mongo_collection(mongo_client,"users", db_name=mongo_db_name)
    user_data = user_collection.find_one({"_id": ObjectId(user_id)})
    try:
        if not user_data:
            raise ValueError(f"No user found with _id: {user_id}")
        else:
            groupIds = user_data["groupIds"] if user_data.get("groupIds") else []
            group_collection = get_mongo_collection(mongo_client,"groups", db_name=mongo_db_name)

    
            if "groups" not in get_mongo_db(mongo_client).list_collection_names():
                logger.warning("The 'groups' collection does not exist.")
                group_data = {}  # Return an empty list if the collection does not exist
            else:
                group_data = list(group_collection.find({"_id": {"$in": [ObjectId(group_id) for group_id in groupIds]}}))
            if len(groupIds) > 0:
                for group in group_data:
                    document_ids.extend(group.get("document_ids", []))
            else:
                document_ids = []
            print("document_ids >>", document_ids)
            return document_ids
    except Exception as e:
        logger.error(f"Error fetching user and group data from MongoDB: {str(e)}")
        raise e


@async_lru_cache(maxsize=config.lru_cache_maxsize, ttl=120)
async def load_vector_store_cache(vector_store: VectorStore, index_name: str, namespace: str):
    return vector_store.load_vector_store(index_name=index_name, namespace=namespace)


async def get_retriever(vector_store: VectorStore, search_kwargs:dict,search_type:str="similarity"):
    return await vector_store.makeretriever(search_kwargs=search_kwargs, search_type=search_type, vector_store=vector_store.vector_store)

async def update_pinecone_index_specific(
    pinecone_input: PineconeInput,
    vector_store: VectorStore,
    blob_service_client: BlobServiceClient
) -> Dict[str, str]:
    """
    Update the Pinecone index with new documents from Azure Blob Storage, using the generalized VectorStore.

    Args:
        pinecone_input (PineconeInput): Validated input containing team_id and object_keys.
        vector_store (VectorStore): Instance of the generalized VectorStore class.
        blob_service_client (BlobServiceClient): Initialized BlobServiceClient for Azure Blob Storage.

    Returns:
        Dict[str, str]: Status of the update operation.
    """
    logger.info(f"Updating Pinecone index for team_id: {pinecone_input.team_id}")
    index_name = config.index_name
    namespace = f"{config.namespace_prefix}-{pinecone_input.team_id}"
    logger.info(f"index_name {index_name} namespace {namespace}")
    container_name = f"{config.namespace_prefix}-{pinecone_input.team_id}"
    # print(index_name,namespace,container_name)
    object_keys = pinecone_input.object_keys if isinstance(pinecone_input.object_keys, list) else [pinecone_input.object_keys]
    # print("object_keys >>", object_keys)
    try:
        # Get container client
        container_client = blob_service_client.get_container_client(container_name)

        local_file_paths = []

        # Download blobs and create a mapping of file paths to blob names
        file_path_to_blob_name = {}

        for blob_name in object_keys:
            if not blob_name.endswith('.pdf'):
                continue
            local_file_path = os.path.join('MAS', 'tmp', blob_name)
            blob_client = container_client.get_blob_client(blob_name)
            with open(local_file_path, "wb") as file:
                blob_data = blob_client.download_blob()
                file.write(blob_data.readall())
            local_file_paths.append(local_file_path)
            file_path_to_blob_name[local_file_path] = blob_name
            logger.info(f"Downloaded: {blob_name} -> {local_file_path}")

        # Generate documents using create_embeddings
        _, documents = vector_store.create_embeddings_from_files(local_file_paths)

        # Split documents into smaller chunks
        text_splitter = RecursiveCharacterTextSplitter(chunk_size=config.chunk_size, chunk_overlap=config.chunk_overlap)
        updated_documents = []
        logger.info("adding metadata------------------------------------------------------------------")

        for doc in documents:
            # Get the correct blob_name from the document's source metadata
            file_path = doc.metadata.get("source", "")
            blob_name = file_path_to_blob_name.get(file_path, "unknown")

            logger.info(f"Processing document: file_path={file_path}, blob_name={blob_name}")

            # Split the page_content
            chunks = text_splitter.split_text(doc.page_content)
            for i, chunk in enumerate(chunks):
                doc_id = str(uuid.uuid4())
                metadata = {
                    "file_path": file_path,
                    "id": doc_id,
                    "file_key": blob_name,
                    "chunk_index": i
                }
                logger.info(f"Checking metadata: ------------------------------------ {metadata}")
                updated_documents.append(Document(
                    page_content=chunk,
                    metadata=metadata,
                    id=doc_id
                ))

        # Clean up temporary files
        for local_file_path in local_file_paths:
            os.remove(local_file_path)

        logger.info("added metadata----------------------------------------------------------------------------------")

        # Update Pinecone
        status = vector_store.update_vector_store(
            documents=updated_documents,
            index_name=index_name,
            namespace=namespace
        )
        return status

    except Exception as e:
        logger.error(f"Error updating index: {str(e)}")
        return {"status": f"Error updating index: {str(e)}"}
async def delete_from_pinecone_index_specific(
    pinecone_input: PineconeInput,
    vector_store: VectorStore
) -> Dict[str, str]:
    """
    Delete documents from the Pinecone index based on the object key metadata, using the generalized VectorStore.

    Args:
        pinecone_input (PineconeInput): Validated input containing team_id and object_keys.
        vector_store (VectorStore): Instance of the generalized VectorStore class.

    Returns:
        Dict[str, str]: Status of the deletion operation.
    """
    logger.info(f"Deleting documents from Pinecone index for team_id: {pinecone_input.team_id}")
    index_name=config.index_name
    namespace=f"{config.namespace_prefix}-{pinecone_input.team_id}"

    object_keys = pinecone_input.object_keys if isinstance(pinecone_input.object_keys, list) else [pinecone_input.object_keys]

    vector_store.load_vector_store(index_name=index_name, namespace=namespace)
    try:
        # Delete documents using the generalized VectorStore
        status = vector_store.delete_from_vector_store(
            metadata_filter={"file_key": object_keys},
            index_name=index_name,
            namespace=namespace
        )
        return status

    except Exception as e:
        logger.error(f"Error deleting documents: {str(e)}")
        return {"status": f"Error deleting documents: {str(e)}"}


# Global VectorStore with Async Cache (Long TTL)
@async_lru_cache(maxsize=32, ttl=3600)  # 1 hour TTL, supports multiple namespaces
async def get_global_vector_store(index_name: str = "qharmony", namespace: str = "QnA") -> VectorStore:
    """
    Get or create a global VectorStore instance with async caching.

    This function loads a VectorStore on first call and caches it with a long TTL.
    Multiple namespaces can be cached simultaneously. Perfect for chat sessions
    where everyone can access the same cached instance.

    Args:
        index_name (str): Pinecone index name (default: "qharmony")
        namespace (str): Namespace for the vector store (default: "EnrollmentQnA")

    Returns:
        VectorStore: Cached VectorStore instance (reused globally)
    """
    logger.info(f"🚀 Initializing global VectorStore: index={index_name}, namespace={namespace}")

    try:
        # Create VectorStore with OpenAI embeddings
        vector_store = VectorStore(
            vector_db_type="pinecone",
            embedding_available_option="openai_default"
        )

        logger.info(f"📋 Loading VectorStore: index={index_name}, namespace={namespace}")

        # Load the vector store with specific index and namespace
        vector_store.load_vector_store(
            index_name=index_name,
            namespace=namespace
        )

        logger.info(f"✅ Global VectorStore initialized and cached: {index_name}/{namespace}")

        return vector_store

    except Exception as e:
        logger.error(f"❌ Error initializing global VectorStore: {str(e)}")
        raise e


# Convenience function for QnA (maintains backward compatibility)
async def get_qna_global_vstore() -> VectorStore:
    """
    Get the QnA global VectorStore instance (async cached).

    This is a convenience function that calls get_global_vector_store
    with QnA-specific parameters.

    Returns:
        VectorStore: QnA VectorStore instance (cached and reused)
    """
    return await get_global_vector_store(index_name="qharmony", namespace="QnA")

