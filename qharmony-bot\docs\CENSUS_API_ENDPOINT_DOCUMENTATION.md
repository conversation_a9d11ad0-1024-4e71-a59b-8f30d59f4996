# Census Processor API Endpoint Documentation

## 🎯 **Overview**

The Census Processor API provides a comprehensive solution for processing employee census data with ML-powered predictions and real-time health plan integration from the federal marketplace.

## 📋 **API Endpoint**

### **POST** `/api/census/processor/v1`

**Description**: Process uploaded census file with optional enriched dataframe return

**Content-Type**: `multipart/form-data`

**Authentication**: Required (API key or session-based)

## 📥 **Request Parameters**

| Parameter | Type | Required | Default | Description |
|-----------|------|----------|---------|-------------|
| `file` | File | ✅ Yes | - | Census file (CSV, XLSX, XLS format) |
| `return_dataframe` | Boolean | ❌ No | `true` | Include enriched dataframe data in response |

### **Supported File Formats**
- **CSV** (`.csv`) - Comma-separated values
- **Excel** (`.xlsx`, `.xls`) - Microsoft Excel formats
- **Maximum file size**: 50MB
- **Maximum rows**: 10,000 employees

## 🔧 **Request Examples**

### **cURL - With Dataframe (Default)**
```bash
curl -X POST "https://api.qharmony.com/api/census/processor/v1?return_dataframe=true" \
  -H "Authorization: Bearer YOUR_API_KEY" \
  -H "Content-Type: multipart/form-data" \
  -F "file=@employee_census.csv"
```

### **cURL - Without Dataframe (Faster Response)**
```bash
curl -X POST "https://api.qharmony.com/api/census/processor/v1?return_dataframe=false" \
  -H "Authorization: Bearer YOUR_API_KEY" \
  -H "Content-Type: multipart/form-data" \
  -F "file=@employee_census.csv"
```

### **JavaScript/Fetch**
```javascript
const processCensusFile = async (file, includeDataframe = true) => {
  const formData = new FormData();
  formData.append('file', file);

  try {
    const response = await fetch(`/api/census/processor/v1?return_dataframe=${includeDataframe}`, {
      method: 'POST',
      headers: {
        'Authorization': 'Bearer YOUR_API_KEY'
      },
      body: formData
    });

    const result = await response.json();
    
    if (result.success) {
      console.log(`Processed ${result.data.summary.total_employees} employees`);
      return result.data;
    } else {
      throw new Error(result.message);
    }
  } catch (error) {
    console.error('Census processing failed:', error);
    throw error;
  }
};

// Usage
const fileInput = document.getElementById('census-file');
const result = await processCensusFile(fileInput.files[0], true);
```

### **Python/Requests**
```python
import requests

def process_census_file(file_path, return_dataframe=True):
    url = f"https://api.qharmony.com/api/census/processor/v1?return_dataframe={str(return_dataframe).lower()}"

    headers = {
        "Authorization": "Bearer YOUR_API_KEY"
    }

    files = {
        'file': open(file_path, 'rb')
    }

    try:
        response = requests.post(url, headers=headers, files=files)
        response.raise_for_status()
        
        result = response.json()
        
        if result['success']:
            print(f"Processed {result['data']['summary']['total_employees']} employees")
            return result['data']
        else:
            raise Exception(result['message'])
            
    except requests.exceptions.RequestException as e:
        print(f"Census processing failed: {e}")
        raise
    finally:
        files['file'].close()

# Usage
result = process_census_file('employee_census.csv', return_dataframe=True)
```

## 📤 **Response Structure**

### **Success Response (200)**

```json
{
  "success": true,
  "status_code": 200,
  "message": "Census file processed successfully",
  "data": {
    "summary": {
      "total_employees": 150,
      "processing_success": true,
      "data_quality_score": 0.87,
      "api_integration_status": {
        "health_plans_available": true,
        "prediction_method": "ml_models",
        "total_plans_found": 450
      }
    },
    "statistics": {
      "demographics": {
        "average_age": 42.3,
        "gender_distribution": {"Male": 78, "Female": 72},
        "marital_status_distribution": {"Married": 98, "Single": 52}
      },
      "health_plans": {
        "employees_with_plans": 148,
        "employees_without_plans": 2,
        "total_plans_found": 450,
        "average_plans_per_employee": 3.04,
        "plan_type_distribution": {"HMO": 89, "PPO": 34, "EPO": 25},
        "metal_level_distribution": {"Bronze": 267, "Silver": 183}
      },
      "predictions": {
        "plan_prediction_success_rate": 98.7,
        "benefits_prediction_success_rate": 94.3,
        "average_plan_confidence": 0.84
      }
    },
    "employees": [
      {
        "employee_id": "E001",
        "name": "John Smith",
        "age": 45,
        "gender": "Male",
        "marital_status": "Married",
        "zipcode": "75201",
        "city": "Dallas",
        "state": "TX",
        "income_tier": "High (>100K)",
        "dept_count": 2,
        "predicted_plan_type": "PPO",
        "plan_confidence": 0.92,
        "plan_reason": "Middle age with dependents - PPO for family coverage",
        "predicted_benefits": ["Dental", "Vision", "Term Life", "LTD", "FSA"],
        "benefits_confidence": 0.87,
        "marketplace_plans_available": true,
        "plan_count": 15,
        "recommended_plan": {
          "id": "33602TX0461117",
          "name": "MyBlue Health Bronze™ 402",
          "issuer": "Blue Cross and Blue Shield of Texas",
          "premium": 771.35,
          "premium_with_credit": 563.35,
          "metal_level": "Bronze",
          "type": "HMO",
          "deductible": 14800,
          "max_out_of_pocket": 9200,
          "hsa_eligible": false,
          "quality_rating": 3
        },
        "benefits_coverage": {
          "emergency_room": "$950 Copay with deductible",
          "primary_care": "No Charge",
          "specialist_care": "50% Coinsurance after deductible",
          "prescription_drugs": "$10 Copay",
          "mental_health": "50% Coinsurance after deductible",
          "maternity_care": "50% Coinsurance after deductible",
          "preventive_care": "No Charge"
        },
        "top_3_available_plans": "[{\"id\":\"33602TX0461117\",\"name\":\"MyBlue Health Bronze™ 402\",\"premium\":771.35,\"type\":\"HMO\"},{...},{...}]",
        "api_processing_status": "success"
      }
    ],
    "processing_info": {
      "enrichment_summary": {
        "total_employees": 150,
        "features_analyzed": 19,
        "data_quality_improvement": {
          "overall_completion_rate": 94.5,
          "total_enriched": 287
        }
      },
      "health_plan_errors": {
        "unsupported_states": ["CA", "NY"],
        "api_failures": 2,
        "fallback_applied": true
      }
    },
    "metadata": {
      "pipeline_version": "2.0_with_health_plans",
      "total_steps": 6,
      "steps_completed": ["parsing", "mapping", "pattern_identification", "preprocessing", "enrichment_prediction", "health_plan_integration"],
      "prediction_method": "ml_models",
      "health_plan_integration_success": true,
      "final_dataframe_shape": [150, 177],
      "processing_time_seconds": 68.4
    },
    
    // Conditional fields (only when return_dataframe=true)
    "enriched_dataframe": [
      {
        "employee_id": "E001",
        "name": "John Smith",
        "age": 45,
        "gender": "Male",
        "predicted_plan_type": "PPO",
        "recommended_plan_type": "HMO",
        "recommended_plan_name": "MyBlue Health Bronze™ 402",
        "marketplace_plans_available": true,
        "api_processing_status": "success"
      }
    ],
    "total_employees": 150,
    "total_columns": 177,
    "has_predictions": true,
    "prediction_columns": ["predicted_plan_type", "plan_confidence", "predicted_benefits", "benefits_confidence"]
  }
}
```

### **Error Response (400/500)**

```json
{
  "success": false,
  "status_code": 400,
  "error": "census_processing_failed",
  "message": "Census processing failed: Invalid file format",
  "details": {
    "error_type": "validation_error",
    "processing_stage": "file_upload",
    "timestamp": "2024-01-15T10:30:00Z"
  }
}
```

## 🔍 **Field Definitions**

### **API Processing Status Values**

| Status | Description |
|--------|-------------|
| `success` | Health plans successfully retrieved and processed |
| `no_plans_found` | API call successful but no plans available for location |
| `unsupported_state` | Employee in state with own marketplace (not federal) |
| `api_error` | CMS API call failed (network, authentication, etc.) |
| `processing_error` | Internal processing error during plan integration |

### **Top 3 Plans Usage**

The `top_3_available_plans` field contains a JSON string that should be parsed:

```javascript
// Parse the JSON string
const top3Plans = JSON.parse(employee.top_3_available_plans);

// Access individual plans
top3Plans.forEach((plan, index) => {
  console.log(`Plan ${index + 1}: ${plan.name} - $${plan.premium}`);
});
```

### **Prediction vs Recommendation**

- **`predicted_plan_type`**: ML model prediction based on demographics
- **`recommended_plan_type`**: Actual marketplace plan type (may differ)
- **Match scenarios**:
  - ✅ Perfect match: Predicted PPO → Recommended PPO
  - ⚠️ Market limitation: Predicted PPO → Recommended HMO (PPO unavailable)

## ⚡ **Performance Considerations**

### **Response Size Optimization**

| Parameter | Response Size | Use Case |
|-----------|---------------|----------|
| `return_dataframe=true` | ~2-5MB | Full data analysis, export functionality |
| `return_dataframe=false` | ~200-500KB | Dashboard display, summary views |

### **Processing Time**

- **Small files** (< 100 employees): 5-15 seconds
- **Medium files** (100-1000 employees): 15-45 seconds  
- **Large files** (1000+ employees): 45-120 seconds

### **Rate Limits**

- **Requests per minute**: 10
- **Concurrent requests**: 3
- **Daily quota**: 1000 requests

## 🚨 **Error Codes**

| Code | HTTP Status | Description |
|------|-------------|-------------|
| `file_too_large` | 413 | File exceeds 50MB limit |
| `invalid_file_format` | 400 | Unsupported file format |
| `processing_timeout` | 408 | Processing exceeded time limit |
| `api_quota_exceeded` | 429 | Daily API quota exceeded |
| `census_processing_failed` | 500 | Internal processing error |

## 🔐 **Authentication**

### **API Key Authentication**
```bash
curl -H "Authorization: Bearer YOUR_API_KEY" ...
```

### **Session-Based Authentication**
```bash
curl -H "Cookie: session_id=YOUR_SESSION_ID" ...
```

## 📊 **Usage Examples**

### **Dashboard Integration**
```javascript
// For dashboard - use without dataframe for faster response
const dashboardData = await processCensusFile(file, false);
const summary = dashboardData.summary;
const employees = dashboardData.employees;

// Display summary statistics
document.getElementById('total-employees').textContent = summary.total_employees;
document.getElementById('data-quality').textContent = `${summary.data_quality_score * 100}%`;
```

### **Data Export**
```javascript
// For export - include dataframe for complete data
const exportData = await processCensusFile(file, true);
const dataframeRecords = exportData.enriched_dataframe;

// Convert to CSV for download
const csvData = convertToCsv(dataframeRecords);
const blob = new Blob([csvData], { type: 'text/csv' });
const url = URL.createObjectURL(blob);
const a = document.createElement('a');
a.href = url;
a.download = 'enriched_census_data.csv';
a.click();
```

### **Plan Analysis**
```javascript
// Analyze plan recommendations
const employees = result.employees;
const planAnalysis = employees.reduce((acc, emp) => {
  if (emp.recommended_plan) {
    const planType = emp.recommended_plan.type;
    acc[planType] = (acc[planType] || 0) + 1;
  }
  return acc;
}, {});

console.log('Plan type distribution:', planAnalysis);
```

This API endpoint provides a complete solution for census processing with ML predictions and real-time health plan integration! 🚀
