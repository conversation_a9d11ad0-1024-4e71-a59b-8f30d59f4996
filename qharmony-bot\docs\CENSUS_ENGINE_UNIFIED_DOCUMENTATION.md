# Census Engine - Unified Documentation

## Table of Contents

### 1. [Overview & Architecture](#1-overview--architecture)
- 1.1 [System Overview](#11-system-overview)
- 1.2 [Key Features](#12-key-features)
- 1.3 [Architecture Diagram](#13-architecture-diagram)
- 1.4 [Supported Data Patterns](#14-supported-data-patterns)

### 2. [Processing Pipeline](#2-processing-pipeline)
- 2.1 [Processing Flow](#21-processing-flow)
- 2.2 [Step-by-Step Process](#22-step-by-step-process)
- 2.3 [Data Flow Diagram](#23-data-flow-diagram)

### 3. [Core Components](#3-core-components)
- 3.1 [Census Controller](#31-census-controller)
- 3.2 [Universal Field Mapper](#32-universal-field-mapper)
- 3.3 [Pattern Identifier](#33-pattern-identifier)
- 3.4 [Data Preprocessor](#34-data-preprocessor)
- 3.5 [Enrichment Engine](#35-enrichment-engine)
- 3.6 [Model Predictor](#36-model-predictor)

### 4. [Field Mapping System](#4-field-mapping-system)
- 4.1 [Enhanced LLM Prompts](#41-enhanced-llm-prompts)
- 4.2 [Context-Driven Mapping](#42-context-driven-mapping)
- 4.3 [Name Field Standardization](#43-name-field-standardization)
- 4.4 [System Field Categories](#44-system-field-categories)
- 4.5 [Mapping Validation](#45-mapping-validation)

### 5. [Pattern Detection & Processing](#5-pattern-detection--processing)
- 5.1 [Column-Based Patterns](#51-column-based-patterns)
- 5.2 [Row-Based Patterns](#52-row-based-patterns)
- 5.3 [Row-to-Column Conversion](#53-row-to-column-conversion)
- 5.4 [Employee Grouping Strategies](#54-employee-grouping-strategies)

### 6. [Data Preprocessing & Standardization](#6-data-preprocessing--standardization)
- 6.1 [Standardization Pipeline](#61-standardization-pipeline)
- 6.2 [Name Field Processing](#62-name-field-processing)
- 6.3 [Date & Age Conversion](#63-date--age-conversion)
- 6.4 [Gender Standardization](#64-gender-standardization)
- 6.5 [Categorical Field Mapping](#65-categorical-field-mapping)
- 6.6 [Dependent Field Generation](#66-dependent-field-generation)

### 7. [System Fields Reference](#7-system-fields-reference)
- 7.1 [Complete Field List](#71-complete-field-list)
- 7.2 [Core Identity Fields](#72-core-identity-fields)
- 7.3 [Demographics Fields](#73-demographics-fields)
- 7.4 [Employment Fields](#74-employment-fields)
- 7.5 [Benefits Fields](#75-benefits-fields)
- 7.6 [Dependent Fields](#76-dependent-fields)

### 8. [Enrichment & Prediction](#8-enrichment--prediction)
- 8.1 [Data Enrichment Process](#81-data-enrichment-process)
- 8.2 [Plan Type Prediction](#82-plan-type-prediction)
- 8.3 [Benefits Prediction](#83-benefits-prediction)
- 8.4 [Model Integration](#84-model-integration)
- 8.5 [Prediction Fallbacks](#85-prediction-fallbacks)

### 9. [Health.gov API Integration](#9-healthgov-api-integration)
- 9.1 [CMS API Service](#91-cms-api-service)
- 9.2 [Data Mapping & Transformation](#92-data-mapping--transformation)
- 9.3 [Plan Fetching Process](#93-plan-fetching-process)
- 9.4 [State Marketplace Handling](#94-state-marketplace-handling)
- 9.5 [Error Handling & Fallbacks](#95-error-handling--fallbacks)

### 10. [API Integration](#10-api-integration)
- 10.1 [Request Structure](#101-request-structure)
- 10.2 [Response Format](#102-response-format)
- 10.3 [Error Handling](#103-error-handling)
- 10.4 [Status Codes](#104-status-codes)

### 11. [Testing & Validation](#11-testing--validation)
- 11.1 [Test File Structure](#111-test-file-structure)
- 11.2 [Comprehensive Testing](#112-comprehensive-testing)
- 11.3 [Pattern-Specific Tests](#113-pattern-specific-tests)
- 11.4 [Validation Logic](#114-validation-logic)

### 12. [Performance & Optimization](#12-performance--optimization)
- 12.1 [Processing Performance](#121-processing-performance)
- 12.2 [Memory Optimization](#122-memory-optimization)
- 12.3 [LLM API Optimization](#123-llm-api-optimization)
- 12.4 [Batch Processing](#124-batch-processing)

### 13. [Recent Updates & Fixes](#13-recent-updates--fixes)
- 13.1 [Name Field Standardization Fix](#131-name-field-standardization-fix)
- 13.2 [Enhanced LLM Prompts](#132-enhanced-llm-prompts)
- 13.3 [Benefits Prediction Improvements](#133-benefits-prediction-improvements)
- 13.4 [Enhanced ZIP Code Location Enrichment](#134-enhanced-zip-code-location-enrichment)
- 13.5 [Dependent Field Generation Fix](#135-dependent-field-generation-fix)
- 13.6 [DataPreprocessor Optimization & Refactoring](#136-datapreprocessor-optimization--refactoring)
- 13.7 [Health.gov API Integration](#137-healthgov-api-integration)

### 14. [Troubleshooting](#14-troubleshooting)
- 14.1 [Common Issues](#141-common-issues)
- 14.2 [Error Messages](#142-error-messages)
- 14.3 [Debug Procedures](#143-debug-procedures)
- 14.4 [Performance Issues](#144-performance-issues)

### 15. [Development Guide](#15-development-guide)
- 15.1 [Code Structure](#151-code-structure)
- 15.2 [Adding New Patterns](#152-adding-new-patterns)
- 15.3 [Extending Field Mapping](#153-extending-field-mapping)
- 15.4 [Testing New Features](#154-testing-new-features)

### 16. [Appendices](#16-appendices)
- 16.1 [Function Reference](#161-function-reference)
- 16.2 [Configuration Options](#162-configuration-options)
- 15.3 [Sample Data Files](#153-sample-data-files)
- 15.4 [Migration Guide](#154-migration-guide)

---

## 1. Overview & Architecture

### 1.1 System Overview

The Census Engine is a comprehensive data processing system designed to handle employee census data from various sources and formats. It automatically detects patterns, maps fields, standardizes data, and outputs consistent structured data for HR and benefits administration systems.

**Key Capabilities:**
- Multi-format census data processing (column-based, row-based, mixed)
- Intelligent pattern detection and automatic structure conversion
- Context-aware field mapping using enhanced LLM prompts
- Comprehensive data standardization and validation
- ML-powered plan type and benefits prediction
- Scalable API integration with robust error handling

### 1.2 Key Features

**🔍 Pattern Detection:**
- Automatic identification of 5+ census data patterns
- Row-to-column structure conversion
- Family grouping and relationship mapping

**🎯 Smart Field Mapping:**
- Enhanced LLM prompts for context-driven mapping
- 450+ field variation support
- Name field standardization (first_name + last_name → name)
- Dependent field generation (up to 20 dependents)

**⚡ Data Processing:**
- Comprehensive standardization pipeline
- DOB to age conversion with validation
- Gender, relationship, and categorical field standardization
- System field addition for consistent output structure

**🤖 ML Integration:**
- Plan type prediction with confidence scores
- Benefits assignment with fallback logic
- Data enrichment for missing features
- Model performance optimization

### 1.3 Architecture Diagram

```
┌─────────────────────────────────────────────────────────────────┐
│                        Census Engine                            │
├─────────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │ Census          │  │ Pattern         │  │ Universal Field │ │
│  │ Controller      │  │ Identifier      │  │ Mapper          │ │
│  │                 │  │                 │  │                 │ │
│  │ • Orchestration │  │ • Pattern       │  │ • LLM-powered   │ │
│  │ • API Interface │  │   Detection     │  │   Field Mapping │ │
│  │ • Error Handling│  │ • Structure     │  │ • Context-aware │ │
│  │                 │  │   Analysis      │  │   Decisions     │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
│                                                                 │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │ Data            │  │ Enrichment      │  │ Model           │ │
│  │ Preprocessor    │  │ Engine          │  │ Predictor       │ │
│  │                 │  │                 │  │                 │ │
│  │ • Row-to-Column │  │ • Feature       │  │ • Plan Type     │ │
│  │ • Standardization│  │   Enrichment   │  │   Prediction    │ │
│  │ • Validation    │  │ • Data Quality  │  │ • Benefits      │ │
│  │ • Name Fields   │  │   Improvement   │  │   Assignment    │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
└─────────────────────────────────────────────────────────────────┘
```

### 1.4 Supported Data Patterns

1. **Column-based Single Row** (`column_based_single_row`)
   - Standard employee records with dependent columns
   - Example: `employee_id, name, dept_1, dept_2, dept_3`

2. **Column-based Count Only** (`column_based_count_only`)
   - Employee records with dependent count field
   - Example: `employee_id, name, dept_count`

3. **Row-based Member Level** (`row_based_member_level`)
   - Separate rows for employees and dependents
   - Example: Multiple rows with `relationship` field

4. **Address-based Grouping** (`address_based_grouping`)
   - Family members grouped by address + last name
   - Example: Same address, different relationship types

5. **Benefits-centric** (`benefits_centric`)
   - Focus on benefit elections and coverage tiers
   - Example: Detailed plan selection data

---

## 2. Processing Pipeline

### 2.1 Processing Flow

The Census Engine follows a **streamlined 6-step processing pipeline**:

```mermaid
graph TD
    A[Raw CSV File] --> B[Step 1: File Parsing & Validation]
    B --> C[Step 2: Universal Field Mapping]
    C --> D[Step 3: Pattern Identification]
    D --> E[Step 4: Data Preprocessing & Standardization]
    E --> F[Step 5: Enrichment & ML Prediction]
    F --> G[Step 6: Health Plan Integration]
    G --> H[Final Response Generation]

    subgraph "Step 4 Details"
        E1[Apply Field Mappings]
        E2[Pattern-Based Processing]
        E3[Data Standardization]
        E4[System Column Addition]
        E5[Final Validation]
        E1 --> E2 --> E3 --> E4 --> E5
    end

    subgraph "Step 5 Details"
        F1[Data Enrichment]
        F2[ML Model Prediction]
        F3[Confidence Scoring]
        F4[Prediction Validation]
        F1 --> F2 --> F3 --> F4
    end

    subgraph "Step 6 Details"
        G1[CMS API Integration]
        G2[Plan Filtering by Predictions]
        G3[Plan Ranking & Selection]
        G4[Benefits Extraction]
        G1 --> G2 --> G3 --> G4
    end
```

### 2.2 Step-by-Step Process

**Step 1: File Parsing & Validation**
- Parse CSV file using pandas with robust error handling
- Validate file structure (minimum rows, columns, data integrity)
- Generate file processing metadata and basic statistics
- Perform initial data quality assessment

**Step 2: Universal Field Mapping**
- Enhanced LLM-powered context-driven field mapping
- Map CSV headers to standardized system fields
- Handle name field variations with intelligent consolidation
- Validate mandatory field coverage and mapping confidence

**Step 3: Pattern Identification**
- Analyze data structure and employee-dependent relationships
- Identify census pattern type (column-based, row-based, mixed)
- Determine optimal processing strategy
- Generate pattern confidence scores and processing recommendations

**Step 4: Data Preprocessing & Standardization**
- Apply validated field mappings to raw data
- Convert row-based to column-based structure (if needed)
- Standardize all data values (names, dates, categories)
- Add missing system columns with default values
- Perform comprehensive data validation and quality checks

**Step 5: Enrichment & ML Prediction**
- **Data Enrichment**: Fill missing feature fields using intelligent defaults
- **ML Model Prediction**: Generate plan type and benefits predictions
- **Confidence Scoring**: Calculate prediction confidence scores
- **Quality Assessment**: Validate prediction readiness and feature completeness

**Step 6: Health Plan Integration**
- **CMS API Integration**: Connect to Health.gov marketplace API
- **Prediction-Based Filtering**: Use ML predictions to filter relevant plans
- **Plan Ranking**: Rank plans by employee profile relevance
- **Benefits Extraction**: Extract detailed benefits and cost information
- **State Handling**: Manage federal vs state marketplace limitations

---

## 3. Core Components

### 3.1 Census Controller

**File**: `app/controllers/Census/censusController.py`
**Role**: Main orchestrator and API interface
**Dependencies**: All other census components

#### 3.1.1 Architecture & Dependencies

```python
CensusController
├── OrchestrationService (orchestration_service)
│   ├── UniversalFieldMapper (universal_mapper)
│   ├── PatternIdentifier (pattern_identifier)
│   ├── DataPreprocessor (data_preprocessor)
│   ├── EnrichmentEngine (enrichment_engine)
│   └── ModelPredictor (model_predictor)
├── ResponseService (response_service)
└── ValidationService (validation_service)
```

#### 3.1.2 Function Call Sequence

**Main Processing Flow** (`process_census_data()`):

```python
1. process_census_data(file_data, filename)
   ├── 1.1 _validate_input_data(file_data, filename)
   ├── 1.2 _parse_csv_data(file_data)
   ├── 1.3 orchestration_service.process_complete_pipeline(df, filename)
   │   ├── 1.3.1 universal_mapper.map_all_fields(headers, sample_data)
   │   ├── 1.3.2 pattern_identifier.identify_pattern(df)
   │   ├── 1.3.3 data_preprocessor.preprocess_data(df)
   │   ├── 1.3.4 _process_enrichment_and_prediction(df, pattern, mapping, preprocessing)
   │   │   ├── ******* enrichment_engine.enrich_data(df)
   │   │   ├── ******* model_predictor.predict_plan_types(enriched_df)
   │   │   └── ******* model_predictor.predict_benefits(predicted_df)
   │   └── 1.3.5 _generate_comprehensive_statistics(final_df)
   ├── 1.4 response_service.generate_final_response(result)
   └── 1.5 _handle_processing_errors(error) [if error occurs]
```

#### 3.1.3 Key Methods

| Method | Purpose | Input | Output |
|--------|---------|-------|--------|
| `process_census_data()` | Main entry point | CSV data, filename | Complete processing result |
| `_validate_input_data()` | Input validation | Raw file data | Validation result |
| `_parse_csv_data()` | CSV parsing | File data | pandas DataFrame |
| `_handle_processing_errors()` | Error handling | Exception | Error response |

### 3.2 Universal Field Mapper

**File**: `app/controllers/Census/universalFieldMapper.py`
**Role**: Context-aware field mapping using enhanced LLM prompts
**Dependencies**: Google Gemini API, system field configurations

#### 3.2.1 Architecture & Dependencies

```python
UniversalFieldMapper
├── Google Gemini API (genai)
├── System Field Categories (CORE_IDENTITY, DEMOGRAPHICS, etc.)
├── Field Validation Rules
└── Fallback Mapping Logic
```

#### 3.2.2 Function Call Sequence

**Field Mapping Process** (`map_all_fields()`):

```python
1. map_all_fields(csv_headers, sample_data)
   ├── 1.1 _create_universal_mapping_prompt(headers, sample_data)
   │   ├── 1.1.1 _format_system_fields_for_prompt()
   │   └── 1.1.2 _create_enhanced_context_prompt()
   ├── 1.2 _call_gemini_api(prompt)
   ├── 1.3 _parse_llm_response(response)
   ├── 1.4 _validate_universal_mapping(mapping, headers)
   │   ├── 1.4.1 _check_mandatory_fields(mapping)
   │   ├── 1.4.2 _validate_name_fields(mapping)
   │   └── 1.4.3 _validate_dependent_fields(mapping)
   └── 1.5 _apply_fallback_mapping(unmapped_fields) [if needed]
```

#### 3.2.3 Enhanced LLM Prompt Structure

**Context-Driven Mapping Approach**:
```
1. MISSION: Transform raw CSV data into standardized system schema
2. DATA ANALYSIS: Column headers + sample data context
3. MAPPING OBJECTIVE: Context-aware field mapping
4. NAME FIELD STRATEGY: Specific mapping rules for name components
5. CONTEXT-DRIVEN METHODOLOGY: Step-by-step analysis process
6. VALIDATION: Mapping accuracy and business purpose alignment
```

### 3.3 Pattern Identifier

**File**: `app/controllers/Census/patternIdentifier.py`
**Role**: Automatic detection of census data patterns
**Dependencies**: pandas, pattern analysis algorithms

#### 3.3.1 Architecture & Dependencies

```python
PatternIdentifier
├── Pattern Detection Algorithms
│   ├── Column-based Detection
│   ├── Row-based Detection
│   └── Address-based Detection
├── Confidence Scoring
└── Pattern Validation
```

#### 3.3.2 Function Call Sequence

**Pattern Detection Process** (`identify_pattern()`):

```python
1. identify_pattern(df)
   ├── 1.1 _analyze_data_structure(df)
   │   ├── 1.1.1 _check_column_patterns(df)
   │   ├── 1.1.2 _check_row_patterns(df)
   │   └── 1.1.3 _check_relationship_patterns(df)
   ├── 1.2 _calculate_pattern_confidence(analysis_results)
   ├── 1.3 _determine_primary_pattern(confidence_scores)
   └── 1.4 _generate_pattern_metadata(pattern_type, confidence)
```

#### 3.3.3 Supported Patterns

| Pattern Type | Detection Criteria | Processing Strategy |
|--------------|-------------------|-------------------|
| `column_based_single_row` | Dependent columns present, no duplicate employee_ids | Direct processing |
| `row_based_member_level` | Multiple rows per family, relationship field | Row-to-column conversion |
| `address_based_grouping` | Same address, different relationships | Address-based grouping |
| `column_based_count_only` | dept_count field, minimal dependent columns | Count-based processing |
| `benefits_centric` | Focus on benefit elections | Benefits-focused processing |

### 3.4 Data Preprocessor

**File**: `app/controllers/Census/dataPreprocessor.py`
**Role**: Data structure conversion and comprehensive standardization
**Dependencies**: Google Gemini API, pandas, system field configurations

#### 3.4.1 Architecture & Dependencies

```python
DataPreprocessor
├── Row-to-Column Conversion Engine
│   ├── Employee Grouping Strategies (3 methods)
│   ├── Structure Conversion Logic
│   └── LLM-powered Field Mapping
├── Standardization Pipeline
│   ├── Name Field Processing
│   ├── Date/Age Conversion
│   ├── Gender Standardization
│   ├── Categorical Field Mapping
│   └── Numeric Field Processing
├── System Column Management
└── Validation Engine
```

#### 3.4.2 Function Call Sequence

**Main Preprocessing Flow** (`preprocess_data()`):

```python
1. preprocess_data(df)
   ├── 1.1 _apply_field_mapping(df, field_mapping)
   ├── 1.2 _convert_structure_if_needed(df, pattern_type)
   │   ├── 1.2.1 _convert_row_to_column_based(df) [if row-based]
   │   │   ├── ******* _group_by_employee(df)
   │   │   │   ├── Strategy 1: _group_by_employee_id(df)
   │   │   │   ├── Strategy 2: _group_by_address_lastname(df)
   │   │   │   └── Strategy 3: _individual_row_processing(df)
   │   │   ├── ******* _convert_structure(grouped_data)
   │   │   └── ******* _map_dependent_fields_llm(structure_data)
   │   └── 1.2.2 [Direct processing for column-based]
   ├── 1.3 _standardize_all_values(df)
   │   ├── 1.3.1 _standardize_name_fields(df)
   │   ├── 1.3.2 _standardize_date_fields(df)
   │   ├── 1.3.3 _standardize_gender_fields(df)
   │   ├── 1.3.4 _standardize_categorical_fields(df)
   │   └── 1.3.5 _standardize_numeric_fields(df)
   ├── 1.4 _add_all_system_columns(df)
   └── 1.5 _validate_column_based_data(df)
```

#### 3.4.3 Name Field Standardization Process

**Enhanced Name Processing** (`_standardize_name_fields()`):

```python
1. _standardize_name_fields(df)
   ├── 1.1 Check for first_name + last_name combination
   │   ├── 1.1.1 Combine: first_name + " " + last_name → name
   │   ├── 1.1.2 Add middle_name if present (first + middle + last)
   │   └── 1.1.3 Clean up extra spaces and null values
   ├── 1.2 Handle single name field cases
   │   ├── 1.2.1 Use first_name as name (if only first_name exists)
   │   └── 1.2.2 Use last_name as name (if only last_name exists)
   └── 1.3 Remove individual name fields
       ├── 1.3.1 Drop first_name column
       ├── 1.3.2 Drop last_name column
       └── 1.3.3 Drop middle_name column
```

### 3.5 Enrichment Engine

**File**: `app/controllers/Census/dataEnrichment.py`
**Role**: Feature enrichment and data quality improvement
**Dependencies**: pandas, numpy, datetime, statistical models

#### 3.5.1 Architecture & Dependencies

```python
EnrichmentEngine
├── Feature Enrichment Pipeline
│   ├── Missing Field Detection
│   ├── Statistical Inference
│   ├── Rule-based Enrichment
│   └── Quality Scoring
├── Required Features (19 fields)
│   ├── Demographics (age, gender, marital_status)
│   ├── Employment (job_type, employment_type, department)
│   ├── Health (health_condition, chronic_condition)
│   └── Behavioral (risk_tolerance, lifestyle, travel_frequency)
└── Statistics Generation
```

#### 3.5.2 Function Call Sequence

**Data Enrichment Process** (`enrich_data()`):

```python
1. enrich_data(df)
   ├── 1.1 _validate_required_features(df)
   ├── 1.2 enrich_dataframe(df)
   │   ├── 1.2.1 _enrich_age(df)
   │   ├── 1.2.2 _enrich_gender(df)
   │   ├── 1.2.3 _enrich_marital_status(df)
   │   ├── 1.2.4 _enrich_dept_count(df)
   │   ├── 1.2.5 _enrich_job_type(df)
   │   ├── 1.2.6 _enrich_employment_type(df)
   │   ├── 1.2.7 _enrich_department(df)
   │   ├── 1.2.8 _enrich_region(df)
   │   ├── 1.2.9 _enrich_health_condition(df)
   │   ├── 1.2.10 _enrich_chronic_condition(df)
   │   ├── 1.2.11 _enrich_prescription_use(df)
   │   ├── 1.2.12 _enrich_income_tier(df)
   │   ├── 1.2.13 _enrich_risk_tolerance(df)
   │   ├── 1.2.14 _enrich_lifestyle(df)
   │   ├── 1.2.15 _enrich_travel_frequency(df)
   │   ├── 1.2.16 _enrich_hsa_familiarity(df)
   │   ├── 1.2.17 _enrich_pregnancy_status(df)
   │   ├── 1.2.18 _enrich_tobacco_use(df)
   │   └── 1.2.19 _enrich_mental_health_needs(df)
   ├── 1.3 _generate_comprehensive_statistics(enriched_df)
   └── 1.4 _calculate_enrichment_metrics(original_df, enriched_df)
```

#### 3.5.3 Enrichment Strategies

| Field | Enrichment Method | Logic |
|-------|------------------|-------|
| `age` | DOB calculation or random (25-65) | Current year - birth year |
| `job_type` | Salary-based inference | <$50K→Hourly, >$100K→Desk, else→Mixed |
| `department` | Random weighted selection | IT(30%), Finance(25%), Sales(20%), etc. |
| `health_condition` | Age-based probability | <30→Excellent(60%), >50→Fair(40%) |
| `income_tier` | Salary categorization | <$50K→Low, $50K-$100K→Medium, >$100K→High |
| `risk_tolerance` | Age + income correlation | Young+High Income→High, Old+Low Income→Low |

### 3.6 Model Predictor

**File**: `app/controllers/Census/modelPredictor.py`
**Role**: ML-powered plan type and benefits prediction
**Dependencies**: XGBoost, scikit-learn, pickle, trained models

#### 3.6.1 Architecture & Dependencies

```python
ModelPredictor
├── Model Loading System
│   ├── Plan Type Model (XGBoost)
│   ├── Benefits Model (Multi-label)
│   ├── Label Encoders
│   └── Model Metadata
├── Feature Preparation Pipeline
│   ├── Required Features (19 fields)
│   ├── Feature Engineering
│   ├── Missing Value Handling
│   └── Data Type Conversion
├── Prediction Engine
│   ├── Plan Type Prediction
│   ├── Benefits Prediction
│   └── Confidence Scoring
└── Fallback Logic
    ├── Rule-based Plan Assignment
    ├── Default Benefits Assignment
    └── Error Handling
```

#### 3.6.2 Function Call Sequence

**Plan Type Prediction** (`predict_plan_types()`):

```python
1. predict_plan_types(df)
   ├── 1.1 _prepare_features(df)
   │   ├── 1.1.1 _validate_required_features(df)
   │   ├── 1.1.2 _fill_missing_features(df)
   │   ├── 1.1.3 _convert_data_types(df)
   │   └── 1.1.4 _order_features_for_model(df)
   ├── 1.2 plan_model.predict(features)
   ├── 1.3 plan_model.predict_proba(features)
   ├── 1.4 label_encoder.inverse_transform(predictions)
   ├── 1.5 _generate_plan_confidence_scores(probabilities)
   ├── 1.6 _create_plan_reasoning(predictions, features)
   └── 1.7 _add_plan_predictions_to_dataframe(df, predictions)
```

**Benefits Prediction** (`predict_benefits()`):

```python
1. predict_benefits(df, top_k=3)
   ├── 1.1 _prepare_features(df)
   ├── 1.2 benefits_model.predict(features)
   ├── 1.3 _handle_prediction_length_mismatch(predictions, df)
   │   ├── 1.3.1 _create_fallback_predictions(missing_count)
   │   └── 1.3.2 _ensure_exact_length_match(predictions, df)
   ├── 1.4 _process_multi_label_predictions(predictions)
   │   ├── 1.4.1 _extract_predicted_benefits(predictions)
   │   ├── 1.4.2 _calculate_benefit_confidences(probabilities)
   │   └── 1.4.3 _get_top_k_benefits(probabilities, k)
   ├── 1.5 _generate_benefits_reasoning(predictions, features)
   └── 1.6 _add_benefits_predictions_to_dataframe(df, predictions)
```

#### 3.6.3 Benefits Prediction Fallback Logic

**Enhanced Fallback System**:

```python
1. _handle_prediction_length_mismatch(predictions, df)
   ├── 1.1 Detect length mismatch (predictions < df.length)
   ├── 1.2 Create complete predictions list
   │   ├── 1.2.1 Use existing predictions for available employees
   │   └── 1.2.2 Generate fallback predictions for missing employees
   │       ├── predicted_benefits: ['Dental', 'Vision']
   │       ├── benefits_confidence: 0.5
   │       ├── benefits_reason: "Default benefits assignment due to prediction error"
   │       └── top_3_benefits: ['Dental', 'Vision', 'Hospital Indemnity']
   └── 1.3 Ensure exact length match with DataFrame
```

---

## 4. Field Mapping System

### 4.1 Enhanced LLM Prompts

The field mapping system uses context-driven LLM prompts to achieve accurate field mapping based on both column names and sample data content.

#### 4.1.1 Prompt Structure

**Mission Statement**:
```
You are an expert data analyst specializing in employee census data standardization.

MISSION: Transform raw CSV data into a standardized system schema by creating precise, context-aware field mappings.

YOUR TASK: Analyze the provided CSV structure and map each column header to the most appropriate system field based on both naming patterns and actual data content.
```

**Context Analysis Approach**:
```
MAPPING OBJECTIVE: Create accurate mappings that preserve data meaning while enabling consistent processing. Consider the business context of employee census data (demographics, employment, benefits, family information) when making mapping decisions.
```

#### 4.1.2 Name Field Mapping Strategy

**Specific Name Field Rules**:
```
NAME FIELD MAPPING STRATEGY:
Your goal is to map name fields to their most specific and accurate system equivalents:

- If you see "First_Name", "FirstName", "fname" → map to "first_name"
- If you see "Last_Name", "LastName", "lname", "Surname" → map to "last_name"
- If you see "Middle_Name", "MiddleName", "mname", "MI" → map to "middle_name"
- If you see a single comprehensive name field like "Name", "Full_Name", "Employee_Name", "Complete_Name" → map to "name"

MAPPING PRINCIPLE: Choose the system field that best represents the actual data structure in the CSV.
- Separate name components should map to separate system fields (first_name, last_name, middle_name)
- Combined name fields should map to the unified "name" field
- NEVER use "full_name" as a target - always use "name" for combined name fields
```

#### 4.1.3 Context-Driven Methodology

**Step-by-Step Analysis Process**:
```
CONTEXT-DRIVEN MAPPING METHODOLOGY:

STEP 1 - DATA CONTEXT ANALYSIS:
• Examine column names AND sample data values together
• Understand the business context (employee census, benefits enrollment, demographic survey)
• Identify data patterns and relationships between columns

STEP 2 - SEMANTIC FIELD MATCHING:
• Prioritize meaning over naming similarity
• Consider field purpose within the broader data context
• Look for logical groupings (employee info, dependent info, benefits, etc.)

STEP 3 - PRECISION MAPPING RULES:
1. EXACT MATCHES: If CSV header matches system field exactly → direct mapping
2. SEMANTIC MATCHES: Map to system field that best represents the data purpose
3. SPECIFICITY PRIORITY: Choose most specific applicable system field
4. MANDATORY COVERAGE: Ensure all mandatory fields have valid mappings
5. DEPENDENT STRUCTURE: Map family data to numbered dept_X pattern consistently
6. BENEFIT CATEGORIZATION: Map benefit fields to specific plan types
7. UNMAPPABLE LAST RESORT: Use "unmapped" only when no reasonable system field exists

MAPPING VALIDATION:
• Does this mapping accurately represent the data's business purpose?
• Will the mapped field support the intended data processing and analysis?
• Are all mandatory fields covered with appropriate mappings?
```

---

## 9. Health.gov API Integration

### 9.1 CMS API Service

The CMS API Service integrates with the Health.gov CMS Marketplace API to fetch actual health insurance plans based on employee demographics and predictions.

**File**: `app/controllers/Census/services/cmsApiService.py`

#### 9.1.1 Service Architecture

```python
class CMSAPIService:
    """CMS Marketplace API service for fetching health plans."""

    def __init__(self, api_key: str = None):
        self.api_key = api_key or os.getenv('CMS_API_KEY')
        self.FEDERAL_MARKETPLACE_STATES = {
            "AL", "AK", "AZ", "AR", "DE", "FL", "GA", "HI", "IL", "IN",
            "IA", "KS", "LA", "MI", "MS", "MO", "MT", "NE", "NH", "NC",
            "ND", "OH", "OK", "SC", "SD", "TN", "TX", "UT", "VA", "WV",
            "WI", "WY"
        }
```

#### 9.1.2 Key Methods

**Location Lookup**:
```python
def get_location_info(self, zipcode: str) -> Dict[str, Any]:
    """Get county information for a ZIP code."""
    return self.make_request(f"counties/by/zip/{zipcode}")
```

**Plan Search**:
```python
def search_plans_for_employee(self, employee_row: pd.Series) -> Dict[str, Any]:
    """Search marketplace plans for a specific employee."""
    # Maps employee data to CMS API format
    # Handles household composition and income
    # Returns plan results with filtering options
```

**Batch Processing**:
```python
def batch_search_plans(self, df: pd.DataFrame) -> Dict[str, Any]:
    """Search plans for multiple employees in batch."""
    # Processes entire employee dataset
    # Provides comprehensive statistics
    # Handles errors gracefully
```

### 9.2 Data Mapping & Transformation

#### 9.2.1 Income Tier Mapping

The service maps census income tiers to dollar amounts for CMS API:

```python
def _map_income_tier_to_amount(self, income_tier: str) -> int:
    """Map income tier strings to dollar amounts."""

    # Mapping examples:
    # "High (>100K)" -> 120000
    # "Medium (50K-100K)" -> 75000
    # "Low (<50K)" -> 35000
    # "Very High (>150K)" -> 180000
    # "Very Low (<30K)" -> 25000
```

#### 9.2.2 Person Data Structure

Employee and dependent data is transformed for CMS API:

```python
def _build_person_data(self, employee_row: pd.Series) -> List[Dict[str, Any]]:
    """Build person data structure for CMS API."""

    # Employee person
    employee_person = {
        "age": int(employee_row.get('age', 30)),
        "aptc_eligible": income <= 400000,
        "uses_tobacco": tobacco_use == 'Y',
        "gender": "Male" or "Female",
        "is_pregnant": False,
        "is_parent": False,
        "utilization_level": "Low",
        "dob": f"{2025 - age}-01-01"
    }

    # Add dependents from dept_1_age, dept_2_age, etc.
```

### 9.3 Plan Fetching Process

#### 9.3.1 Processing Pipeline

```mermaid
graph TD
    A[Employee Data] --> B[Income Mapping]
    B --> C[Person Data Building]
    C --> D[ZIP Code Lookup]
    D --> E{State Supported?}
    E -->|Yes| F[CMS API Call]
    E -->|No| G[Unsupported State Error]
    F --> H[Plan Results]
    H --> I[Plan Filtering]
    I --> J[Summary Generation]
```

#### 9.3.2 API Request Flow

1. **County Lookup**: `GET /counties/by/zip/{zipcode}`
2. **Plan Search**: `POST /plans/search` with household data
3. **Result Processing**: Filter and summarize plans
4. **Error Handling**: Graceful fallbacks for unsupported states

#### 9.3.3 Example API Request

```json
{
  "household": {
    "income": 75000,
    "people": [
      {
        "age": 35,
        "aptc_eligible": true,
        "uses_tobacco": false,
        "gender": "Female",
        "is_pregnant": false,
        "is_parent": false,
        "utilization_level": "Low",
        "dob": "1990-01-01"
      }
    ],
    "has_married_couple": false
  },
  "place": {
    "countyfips": "53033",
    "state": "WA",
    "zipcode": "98101"
  },
  "market": "Individual",
  "year": 2025
}
```

### 9.4 State Marketplace Handling

#### 9.4.1 Federal vs State Marketplaces

**Federal Marketplace States** (supported):
- Use HealthCare.gov platform
- API access available
- 32 states including TX, FL, NC, OH, etc.

**State Marketplaces** (not supported):
- Use their own platforms (e.g., Covered California)
- No API access through federal system
- Includes CA, NY, MA, CT, etc.

#### 9.4.2 Error Handling

```python
# Unsupported state response
{
  "success": False,
  "error": "State CA uses its own marketplace. Federal marketplace not available.",
  "status_code": 400
}
```

### 9.5 Error Handling & Fallbacks

#### 9.5.1 Error Types

1. **API Unavailable**: CMS_API_KEY not configured
2. **Invalid ZIP Code**: Malformed or non-existent ZIP codes
3. **Unsupported State**: State uses own marketplace
4. **Network Errors**: API timeouts or connectivity issues
5. **Data Validation**: Invalid employee data format

#### 9.5.2 Fallback Strategies

```python
# API unavailable fallback
if not self.api_available:
    return self._create_fallback_response(df, "CMS API not available")

# Add empty plan columns to dataframe
df['marketplace_plans_available'] = False
df['plan_count'] = 0
df['available_plan_types'] = ''
df['marketplace_state_supported'] = True
```

#### 9.5.3 Processing Statistics

```python
{
  "statistics": {
    "total_employees": 100,
    "employees_with_plans": 75,
    "employees_without_plans": 15,
    "api_errors": 10,
    "unsupported_states": ["CA", "NY"],
    "supported_employees": 85,
    "success_rate": 0.85,
    "total_plans_found": 1250,
    "unique_plan_types": ["HMO", "PPO", "EPO"],
    "unique_metal_levels": ["Bronze", "Silver", "Gold"],
    "unique_issuers": ["Aetna", "Blue Cross", "Humana"]
  }
}
```

### 9.6 Health Plan Service Integration

The Health Plan Service (`app/controllers/Census/services/healthPlanService.py`) integrates CMS API functionality into the census processing pipeline as **Step 6**.

#### 9.6.1 Pipeline Integration

```python
class HealthPlanService:
    """Service for integrating Health.gov marketplace plans into census processing."""

    def __init__(self, api_key: str = None):
        self.cms_api = CMSAPIService(api_key=api_key)
        self.api_available = True  # or False if API key missing
```

#### 9.6.2 Main Processing Method

```python
def process_employee_plans(self, df: pd.DataFrame) -> Dict[str, Any]:
    """Process health plans for all employees in the dataframe."""

    # For each employee:
    # 1. Call CMS API to get available plans
    # 2. Apply prediction-based filtering
    # 3. Generate plan summaries
    # 4. Compile statistics

    return {
        "success": True,
        "statistics": {...},
        "results": {...},
        "processing_notes": [...]
    }
```

#### 9.6.3 DataFrame Enrichment

The service adds new columns to the employee dataframe:

```python
def enrich_dataframe_with_plans(self, df: pd.DataFrame) -> pd.DataFrame:
    """Enrich dataframe with plan information."""

    # New columns added:
    df['marketplace_plans_available'] = True/False
    df['plan_count'] = 0-50  # Number of available plans
    df['available_plan_types'] = 'HMO, PPO, EPO'
    df['available_metal_levels'] = 'Bronze, Silver, Gold'
    df['estimated_premium_range'] = '$200.00 - $800.00'
    df['marketplace_state_supported'] = True/False
```

#### 9.6.4 Prediction-Based Filtering

Plans are filtered based on ML model predictions:

```python
def filter_plans_by_prediction(self, plan_results, predicted_plan_type, predicted_metal_level):
    """Filter plans based on ML predictions."""

    # Filter by plan type (HMO, PPO, etc.)
    if predicted_plan_type:
        filtered_plans = [plan for plan in plans
                         if plan.get('plan_type') == predicted_plan_type]

    # Filter by metal level (Bronze, Silver, Gold, Platinum)
    if predicted_metal_level:
        filtered_plans = [plan for plan in filtered_plans
                         if plan.get('metal_level') == predicted_metal_level]
```

#### 9.6.5 Processing Statistics

```python
{
  "statistics": {
    "total_employees": 100,
    "employees_with_plans": 85,
    "employees_without_plans": 5,
    "api_errors": 10,
    "unsupported_states": ["CA", "NY"],
    "supported_employees": 90,
    "success_rate": 0.90,
    "total_plans_found": 2150,
    "unique_plan_types": ["HMO", "PPO", "EPO", "POS"],
    "unique_metal_levels": ["Bronze", "Silver", "Gold", "Platinum"],
    "unique_issuers": ["Aetna", "Anthem", "Blue Cross", "Humana", "Kaiser"]
  },
  "processing_notes": [
    "States with own marketplaces (not supported): CA, NY",
    "10 employees had API errors",
    "5 employees have no available plans",
    "Found 2150 total plans across all employees"
  ]
}
```

#### 9.6.6 Orchestration Service Integration

The Health Plan Service is integrated into the orchestration pipeline:

```python
# In OrchestrationService.__init__()
self.health_plan_service = HealthPlanService()

# In processing pipeline
async def _process_health_plan_integration(self, enriched_df):
    """Process step 6: Health.gov API integration."""

    plan_results = self.health_plan_service.process_employee_plans(
        enriched_df,
        include_plan_details=False,
        filter_by_predictions=True
    )

    enriched_with_plans_df = self.health_plan_service.enrich_dataframe_with_plans(enriched_df)

    return {
        "success": True,
        "enriched_data": enriched_with_plans_df,
        "plan_statistics": plan_results.get("statistics", {}),
        "processing_notes": plan_results.get("processing_notes", [])
    }
```

---

## 10. API Integration

### 9.1 Request Structure

#### 9.1.1 Endpoint Information

**Endpoint**: `POST /api/census/processor/v1`
**Content-Type**: `multipart/form-data`
**Authentication**: Required (API key or session-based)

#### 9.1.2 Request Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `file` | File | Yes | CSV/XLSX/XLS file containing census data |
| `return_dataframe` | Boolean | No | Whether to include enriched dataframe data in response (default: true) |

**Options Object Structure**:
```json
{
  "validation_level": "strict|normal|lenient",
  "enable_enrichment": true,
  "enable_prediction": true,
  "output_format": "detailed|summary",
  "include_statistics": true,
  "debug_mode": false
}
```

#### 9.1.3 Sample Request

**cURL Example**:
```bash
curl -X POST "https://api.qharmony.com/api/census/processor/v1" \
  -H "Authorization: Bearer YOUR_API_KEY" \
  -H "Content-Type: multipart/form-data" \
  -F "file=@employee_census.csv" \
  -F "return_dataframe=true"
```

**cURL Example (without dataframe)**:
```bash
curl -X POST "https://api.qharmony.com/api/census/processor/v1" \
  -H "Authorization: Bearer YOUR_API_KEY" \
  -H "Content-Type: multipart/form-data" \
  -F "file=@employee_census.csv" \
  -F "return_dataframe=false"
```

**JavaScript Example**:
```javascript
const formData = new FormData();
formData.append('file', csvFile);
formData.append('filename', 'employee_census.csv');
formData.append('options', JSON.stringify({
  validation_level: 'normal',
  enable_enrichment: true,
  enable_prediction: true,
  output_format: 'detailed'
}));

const response = await fetch('/api/census/process', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer YOUR_API_KEY'
  },
  body: formData
});
```

### 9.2 Response Format

#### 9.2.1 Success Response Structure

**HTTP Status**: `200 OK`
**Content-Type**: `application/json`

The Census Engine follows a **6-step processing pipeline** and returns a structured response with clear data hierarchy:

```json
{
  "success": true,
  "status_code": 200,
  "message": "Census file processed successfully",
  "data": {
    // Summary Section - High-level processing results
    "summary": {
      "total_employees": 150,
      "processing_success": true,
      "data_quality_score": 0.87,
      "api_integration_status": {
        "health_plans_available": true,
        "prediction_method": "ml_models",
        "total_plans_found": 450
      }
    },

    // Statistics Section - Aggregate analysis
    "statistics": {
      "demographics": {
        "average_age": 42.3,
        "age_distribution": {"25-35": 45, "36-45": 67, "46-55": 38},
        "gender_distribution": {"Male": 78, "Female": 72},
        "marital_status_distribution": {"Married": 89, "Not Married": 61}
      },
      "employment": {
        "employment_types": {"Full-Time": 142, "Part-Time": 8},
        "departments": {"Engineering": 45, "Sales": 38, "Finance": 32, "HR": 15, "Other": 20},
        "income_distribution": {"High (>100K)": 67, "Medium (50K-100K)": 58, "Low (<50K)": 25}
      },
      "dependents": {
        "employees_with_dependents": 89,
        "average_dependent_count": 1.2,
        "dependent_age_distribution": {"0-18": 134, "19-26": 23, ">26": 8}
      },
      "health_plans": {
        "total_plans_found": 450,
        "employees_with_plans": 148,
        "unique_plan_types": ["PPO", "HMO", "EPO", "HDHP"],
        "unique_metal_levels": ["Bronze", "Silver", "Gold", "Platinum"],
        "premium_range": {"min": 245.67, "max": 892.34}
      },
      "predictions": {
        "plan_prediction_success_rate": 98.7,
        "benefits_prediction_success_rate": 94.3,
        "average_plan_confidence": 0.84,
        "plan_type_distribution": {"PPO": 67, "HMO": 34, "HDHP + HSA": 28, "EPO": 21}
      },
      "risk_analysis": {
        "group_risk_score": 24.8,
        "group_risk_level": "Low",
        "risk_distribution": {"Low": 112, "Medium": 32, "High": 6}
      }
    },

    // Individual Employee Data Section
    "employees": [
      {
        "employee_id": "E001",
        "name": "John Smith",
        "age": 45,
        "gender": "Male",
        "marital_status": "Married",
        "zipcode": "75201",
        "city": "Dallas",
        "state": "TX",
        "income_tier": "High (>100K)",
        "dept_count": 2,

        // ML Predictions
        "predicted_plan_type": "PPO",
        "plan_confidence": 0.92,
        "plan_reason": "Middle age with dependents - PPO for family coverage",
        "predicted_benefits": ["Dental", "Vision", "Term Life", "LTD", "FSA"],
        "benefits_confidence": 0.87,
        "benefits_reason": "Dental and Vision essential for family coverage",

        // Health Plan Recommendations
        "marketplace_plans_available": true,
        "plan_count": 15,
        "recommended_plan": {
          "id": "33602TX0461117",
          "name": "MyBlue Health Bronze™ 402",
          "issuer": "Blue Cross and Blue Shield of Texas",
          "premium": 771.35,
          "premium_with_credit": 563.35,
          "metal_level": "Bronze",
          "type": "HMO",
          "deductible": 14800,
          "max_out_of_pocket": 9200,
          "hsa_eligible": false,
          "quality_rating": 3
        },

        // Benefits Coverage Details
        "benefits_coverage": {
          "emergency_room": "$950 Copay with deductible",
          "primary_care": "No Charge",
          "specialist_care": "50% Coinsurance after deductible",
          "prescription_drugs": "$10 Copay",
          "mental_health": "50% Coinsurance after deductible",
          "maternity_care": "50% Coinsurance after deductible",
          "preventive_care": "No Charge"
        },

        // Top 3 Plan Options (JSON string - parse to get array)
        "top_3_available_plans": "[{\"id\":\"33602TX0461117\",\"name\":\"MyBlue Health Bronze™ 402\",\"issuer\":\"Blue Cross and Blue Shield of Texas\",\"premium\":771.35,\"premium_with_credit\":563.35,\"metal_level\":\"Bronze\",\"type\":\"HMO\",\"deductible\":14800,\"max_out_of_pocket\":9200,\"hsa_eligible\":false,\"quality_rating\":3,\"has_national_network\":false,\"key_benefits\":{\"emergency_room\":\"$950 Copay with deductible\",\"primary_care\":\"No Charge\"},\"issuer_phone\":\"**************\",\"urls\":{\"plan_brochure\":\"https://example.com/brochure\"}},{...},{...}]",

        // Network Information
        "network_info": {
          "has_national_network": false,
          "referral_required": false
        },

        // Processing Status
        "api_processing_status": "success"
      }
      // ... more employees
    ],

    // Processing Information Section
    "processing_info": {
      "enrichment_summary": {
        "total_employees": 150,
        "features_analyzed": 19,
        "data_quality_improvement": {
          "overall_completion_rate": 94.5,
          "total_enriched": 287
        }
      },
      "feature_validation": {
        "ready_for_prediction": true,
        "missing_critical_features": 0,
        "feature_completeness_score": 0.94
      },
      "health_plan_errors": {
        "unsupported_states": ["CA", "NY"],
        "api_failures": 2,
        "fallback_applied": true
      },
      "processing_notes": [
        "2 employees in unsupported states - using fallback predictions",
        "Health.gov API integration successful for 148/150 employees"
      ]
    }
  },

  // Technical Metadata Section (processing information only)
  "metadata": {
    "pipeline_version": "2.0_with_health_plans",
    "total_steps": 6,
    "steps_completed": ["parsing", "mapping", "pattern_identification", "preprocessing", "enrichment_prediction", "health_plan_integration"],
    "processing_order": ["step_1_parsing", "step_2_mapping", "step_3_pattern", "step_4_preprocessing", "step_5_enrichment_prediction", "step_6_health_plans"],
    "prediction_method": "ml_models",
    "health_plan_integration_success": true,
    "final_dataframe_shape": [150, 177],
    "processing_time_seconds": 68.4,
    "file_info": {
      "filename": "employee_census.csv",
      "original_rows": 150,
      "original_columns": 25,
      "final_columns": 177
    }
  },

  // Conditional Fields (only included when return_dataframe=true)
  "enriched_dataframe": [
    {
      "employee_id": "E001",
      "name": "John Smith",
      "age": 45,
      "gender": "Male",
      "marital_status": "Married",
      "zipcode": "75201",
      "city": "Dallas",
      "state": "TX",
      "predicted_plan_type": "PPO",
      "plan_confidence": 0.92,
      "recommended_plan_type": "HMO",
      "recommended_plan_name": "MyBlue Health Bronze™ 402",
      "recommended_plan_premium": 771.35,
      "marketplace_plans_available": true,
      "plan_count": 15,
      "api_processing_status": "success"
    }
  ],
  "total_employees": 150,
  "total_columns": 177,
  "has_predictions": true,
  "prediction_columns": ["predicted_plan_type", "plan_confidence", "plan_reason", "predicted_benefits", "benefits_confidence", "benefits_reason"]
}
}
```

### 9.3 Field Definitions

#### 9.3.1 API Processing Status Values

The `api_processing_status` field indicates the health plan integration status for each employee:

| Status | Description |
|--------|-------------|
| `success` | Health plans successfully retrieved and processed |
| `no_plans_found` | API call successful but no plans available for location |
| `unsupported_state` | Employee in state with own marketplace (not federal) |
| `api_error` | CMS API call failed (network, authentication, etc.) |
| `processing_error` | Internal processing error during plan integration |
| `unknown` | Status not determined (fallback value) |

#### 9.3.2 Top 3 Plans Format

The `top_3_available_plans` field contains a JSON string that should be parsed to get an array:

```javascript
// Parse the JSON string to get array
const top3Plans = JSON.parse(employee.top_3_available_plans);

// Each plan object contains:
{
  "id": "33602TX0461117",
  "name": "MyBlue Health Bronze™ 402",
  "issuer": "Blue Cross and Blue Shield of Texas",
  "premium": 771.35,
  "premium_with_credit": 563.35,
  "metal_level": "Bronze",
  "type": "HMO",
  "deductible": 14800,
  "max_out_of_pocket": 9200,
  "hsa_eligible": false,
  "quality_rating": 3,
  "has_national_network": false,
  "key_benefits": {
    "emergency_room": "$950 Copay with deductible",
    "primary_care": "No Charge"
  },
  "issuer_phone": "**************",
  "urls": {
    "plan_brochure": "https://example.com/brochure"
  }
}
```

#### 9.3.3 Prediction vs Recommendation

Understanding the relationship between ML predictions and marketplace recommendations:

- **`predicted_plan_type`**: ML model prediction based on employee demographics
- **`recommended_plan_type`**: Actual plan type from marketplace (may differ from prediction)
- **Match scenarios**:
  - ✅ **Perfect match**: Predicted PPO → Recommended PPO
  - ⚠️ **Market limitation**: Predicted PPO → Recommended HMO (PPO not available)
  - ✅ **HSA match**: Predicted "HDHP + HSA" → Recommended HSA-eligible plan

#### 9.3.4 Conditional Response Fields

Fields that appear only under certain conditions:

| Field | Condition | Description |
|-------|-----------|-------------|
| `enriched_dataframe` | `return_dataframe=true` | Complete dataframe data as array of records with all processing results |
| `total_employees` | `return_dataframe=true` | Employee count from dataframe |
| `total_columns` | `return_dataframe=true` | Column count in enriched dataframe |
| `has_predictions` | `return_dataframe=true` | Whether prediction columns exist |
| `prediction_columns` | `return_dataframe=true` | List of prediction-related columns |
| `recommended_plan` | Plans available | Only when marketplace plans found |
| `top_3_available_plans` | Plans available | Only when marketplace plans found |
| `benefits_coverage` | Plans available | Only when plan benefits extracted |

### 9.4 Error Handling

#### 9.3.1 Error Response Structure

**Content-Type**: `application/json`

```json
{
  "success": false,
  "error": {
    "code": "ERROR_CODE",
    "message": "Human-readable error message",
    "details": "Detailed technical information",
    "timestamp": "2024-01-15T10:30:00Z",
    "request_id": "req_123456789"
  },
  "metadata": {
    "processing_stage": "field_mapping",
    "partial_results": null,
    "debug_info": {
      "file_info": {
        "filename": "problematic_file.csv",
        "size_bytes": 1024000,
        "rows_parsed": 150
      }
    }
  }
}
```

### 9.4 Status Codes

#### 9.4.1 Success Codes

| Status Code | Description | Response Data |
|-------------|-------------|---------------|
| **200 OK** | Processing completed successfully | Complete processed data with statistics |
| **202 Accepted** | Processing started (async mode) | Processing job ID and status endpoint |

**200 OK Response Data Structure**:
```json
{
  "success": true,
  "message": "Census data processed successfully",
  "processing_time": 45.23,
  "data": {
    "enriched_dataframe": [
      {
        "employee_id": "E001",
        "name": "John Smith",
        "age": 45.0,
        "gender": "Male",
        "dob": "1980-01-15",
        "marital_status": "Married",
        "zipcode": "90210",
        "city": "Anytown",
        "state": "CA",
        "salary": 65000,
        "dept_count": 2,
        "dept_1": "Jane Smith",
        "dept_1_age": 43.0,
        "dept_1_gender": "Female",
        "predicted_plan_type": "PPO",
        "plan_confidence": 0.92,
        "predicted_benefits": ["Dental", "Vision", "Life"],
        "benefits_confidence": 0.87
      }
    ],
    "comprehensive_statistics": {
      "basic_demographics": {
        "total_employees": 150,
        "average_age": 42.3,
        "age_range": "25-65",
        "gender_distribution": {"Male": 78, "Female": 72},
        "marital_status_distribution": {"Married": 89, "Not Married": 61}
      },
      "dependent_analysis": {
        "employees_with_dependents": 98,
        "total_dependents": 187,
        "average_dependents_per_employee": 1.25,
        "dependent_age_distribution": {
          "0-18": 89,
          "19-26": 45,
          "27+": 53
        }
      },
      "employment_demographics": {
        "employment_types": {"Full-Time": 142, "Part-Time": 8},
        "departments": {"Engineering": 45, "Sales": 38, "Finance": 32, "HR": 15, "Other": 20},
        "salary_distribution": {
          "Low (<$50K)": 23,
          "Medium ($50K-$100K)": 89,
          "High (>$100K)": 38
        }
      },
      "benefits_enrollment": {
        "medical_plan": {"enrolled": 145, "percentage": 96.7},
        "dental_plan": {"enrolled": 132, "percentage": 88.0},
        "vision_plan": {"enrolled": 118, "percentage": 78.7},
        "life_plan": {"enrolled": 89, "percentage": 59.3}
      },
      "prediction_results": {
        "plan_type_distribution": {
          "PPO": 67, "HMO": 34, "HDHP + HSA": 28, "POS": 21
        },
        "average_plan_confidence": 0.84,
        "top_predicted_benefits": [
          {"benefit": "Dental", "employees": 142, "percentage": 94.7},
          {"benefit": "Vision", "employees": 128, "percentage": 85.3},
          {"benefit": "Life Insurance", "employees": 89, "percentage": 59.3}
        ],
        "average_benefits_confidence": 0.79
      },
      "data_quality_metrics": {
        "completeness_score": 0.92,
        "standardization_rate": 0.98,
        "enrichment_coverage": 0.85,
        "validation_pass_rate": 0.96
      }
    },
    "prediction_summary": {
      "total_employees": 150,
      "plan_type_distribution": {
        "successful_predictions": {
          "PPO": 67, "HMO": 34, "HDHP + HSA": 28, "POS": 21
        },
        "failed_predictions": 0,
        "success_rate": 100.0,
        "total_predicted": 150
      },
      "benefits_distribution": {
        "successful_predictions": {
          "Dental": 142, "Vision": 128, "Life": 89, "STD": 67, "LTD": 45
        },
        "failed_predictions": 0,
        "success_rate": 100.0
      },
      "confidence_metrics": {
        "plan_confidence": {
          "mean": 0.84,
          "min": 0.52,
          "max": 0.98,
          "count": 150
        },
        "benefits_confidence": {
          "mean": 0.79,
          "min": 0.45,
          "max": 0.95,
          "count": 150
        }
      },
      "top_3_plan_probabilities": [
        {
          "employee_id": "E001",
          "top_3_plans": ["PPO", "HMO", "HDHP + HSA"],
          "probabilities": [0.92, 0.05, 0.03]
        }
      ]
    },
    "total_employees": 150,
    "total_columns": 149,
    "has_predictions": true,
    "prediction_columns": [
      "predicted_plan_type", "plan_confidence", "plan_reason",
      "top_3_plans", "top_3_plan_confidences",
      "predicted_benefits", "benefits_confidence", "benefits_reason",
      "top_3_benefits", "top_3_benefits_confidences"
    ]
  },
  "metadata": {
    "file_processing": {
      "filename": "employee_census.csv",
      "original_rows": 150,
      "original_columns": 25,
      "file_size_bytes": 1024000,
      "parsing_time_seconds": 0.45
    },
    "pattern_analysis": {
      "pattern_type": "column_based_single_row",
      "confidence": 0.98,
      "processing_strategy": "direct_processing",
      "pattern_indicators": [
        "Found dependent columns",
        "No duplicate employee_ids",
        "Consistent single row per employee"
      ]
    },
    "field_mapping": {
      "mapping_success": true,
      "mapped_fields": 25,
      "unmapped_fields": 0,
      "mapping_confidence": 0.95,
      "llm_processing_time": 8.23,
      "fallback_mappings_used": 0
    },
    "data_processing": {
      "preprocessing_time": 12.34,
      "standardization_applied": true,
      "structure_conversion": false,
      "validation_passed": true,
      "processed_rows": 150,
      "final_columns": 149,
      "data_quality_score": 0.92
    },
    "enrichment_and_prediction": {
      "enrichment_time": 15.67,
      "prediction_time": 8.54,
      "enrichment_applied": true,
      "fields_enriched": 12,
      "enrichment_rate": 0.78,
      "plan_predictions": 150,
      "benefits_predictions": 150,
      "model_performance": {
        "plan_model_accuracy": 0.89,
        "benefits_model_accuracy": 0.82
      }
    }
  }
}
```

**202 Accepted Response Data**:
```json
{
  "success": true,
  "message": "Processing started",
  "job_id": "job_123456789",
  "status_url": "/api/census/status/job_123456789",
  "estimated_completion": "2024-01-15T10:35:00Z",
  "processing_options": {
    "validation_level": "normal",
    "enable_enrichment": true,
    "enable_prediction": true
  }
}
```

#### 9.4.2 Processing Stage Status Codes

**Intermediate Processing Status** (for async operations):

| Stage | Status Code | Description | Response Data |
|-------|-------------|-------------|---------------|
| **File Parsing** | `102 Processing` | Parsing CSV file | File info, parsing progress |
| **Field Mapping** | `102 Processing` | Mapping fields with LLM | Mapping progress, field count |
| **Pattern Detection** | `102 Processing` | Identifying data pattern | Pattern analysis results |
| **Data Preprocessing** | `102 Processing` | Processing and standardizing | Processing progress, validation |
| **Enrichment** | `102 Processing` | Enriching missing features | Enrichment progress, statistics |
| **Prediction** | `102 Processing` | Running ML predictions | Prediction progress, model status |

**Processing Status Response**:
```json
{
  "job_id": "job_123456789",
  "status": "processing",
  "current_stage": "data_preprocessing",
  "progress": 65,
  "estimated_completion": "2024-01-15T10:33:00Z",
  "stages_completed": [
    {
      "stage": "file_parsing",
      "status": "completed",
      "duration": 0.45,
      "result": {
        "rows_parsed": 150,
        "columns_identified": 25
      }
    },
    {
      "stage": "field_mapping",
      "status": "completed",
      "duration": 8.23,
      "result": {
        "mapped_fields": 25,
        "mapping_confidence": 0.95
      }
    },
    {
      "stage": "pattern_detection",
      "status": "completed",
      "duration": 1.12,
      "result": {
        "pattern_type": "column_based_single_row",
        "confidence": 0.98
      }
    },
    {
      "stage": "data_preprocessing",
      "status": "in_progress",
      "progress": 65,
      "current_operation": "standardizing_categorical_fields"
    }
  ]
}
```

#### 9.4.2 Client Error Codes (4xx)

| Status Code | Error Code | Description | Response Data |
|-------------|------------|-------------|---------------|
| **400 Bad Request** | `INVALID_FILE_FORMAT` | File is not a valid CSV | File validation details, supported formats |
| **400 Bad Request** | `MISSING_MANDATORY_FIELDS` | Required fields not found | List of missing mandatory fields |
| **400 Bad Request** | `INVALID_DATA_STRUCTURE` | Data structure cannot be processed | Pattern analysis results, suggestions |
| **400 Bad Request** | `FILE_TOO_LARGE` | File exceeds size limits | File size limits, chunking options |
| **400 Bad Request** | `INVALID_PARAMETERS` | Request parameters are invalid | Parameter validation errors |
| **401 Unauthorized** | `AUTHENTICATION_REQUIRED` | API key missing or invalid | Authentication methods |
| **403 Forbidden** | `INSUFFICIENT_PERMISSIONS` | User lacks required permissions | Required permissions list |
| **413 Payload Too Large** | `FILE_SIZE_EXCEEDED` | File size exceeds maximum limit | Size limits, alternative upload methods |
| **415 Unsupported Media Type** | `UNSUPPORTED_FILE_TYPE` | File type not supported | Supported file types list |
| **422 Unprocessable Entity** | `VALIDATION_FAILED` | Data validation failed | Detailed validation errors |

**400 Bad Request - Missing Mandatory Fields**:
```json
{
  "success": false,
  "error": {
    "code": "MISSING_MANDATORY_FIELDS",
    "message": "Required fields are missing from the CSV data",
    "details": "The following mandatory fields could not be mapped: gender, zipcode",
    "timestamp": "2024-01-15T10:30:00Z",
    "request_id": "req_123456789"
  },
  "metadata": {
    "processing_stage": "field_mapping",
    "missing_fields": ["gender", "zipcode"],
    "available_fields": ["name", "age", "address", "city", "state"],
    "suggestions": [
      "Check if 'Sex' column can be mapped to 'gender'",
      "Check if 'Postal_Code' column can be mapped to 'zipcode'"
    ]
  }
}
```

**422 Unprocessable Entity - Validation Failed**:
```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_FAILED",
    "message": "Data validation failed during preprocessing",
    "details": "Multiple validation errors found in employee records",
    "timestamp": "2024-01-15T10:30:00Z",
    "request_id": "req_123456789"
  },
  "metadata": {
    "processing_stage": "data_preprocessing",
    "validation_errors": [
      {
        "row": 15,
        "employee_id": "E015",
        "field": "age",
        "error": "Invalid age value: -5",
        "suggestion": "Age must be between 0 and 120"
      },
      {
        "row": 23,
        "employee_id": "E023",
        "field": "zipcode",
        "error": "Invalid zipcode format: 'ABCDE'",
        "suggestion": "Zipcode must be 5 digits or ZIP+4 format"
      }
    ],
    "total_errors": 2,
    "total_rows": 150
  }
}
```

#### 9.4.3 Server Error Codes (5xx)

| Status Code | Error Code | Description | Response Data |
|-------------|------------|-------------|---------------|
| **500 Internal Server Error** | `PROCESSING_ERROR` | Unexpected error during processing | Error details, support contact |
| **500 Internal Server Error** | `MODEL_PREDICTION_FAILED` | ML model prediction failed | Model status, fallback results |
| **500 Internal Server Error** | `LLM_API_ERROR` | Field mapping LLM API failed | API status, fallback mapping |
| **502 Bad Gateway** | `EXTERNAL_SERVICE_ERROR` | External service unavailable | Service status, retry information |
| **503 Service Unavailable** | `SERVICE_OVERLOADED` | Service temporarily overloaded | Retry-after header, queue status |
| **504 Gateway Timeout** | `PROCESSING_TIMEOUT` | Processing exceeded time limit | Timeout limits, async options |

**500 Internal Server Error - Model Prediction Failed**:
```json
{
  "success": false,
  "error": {
    "code": "MODEL_PREDICTION_FAILED",
    "message": "Machine learning model prediction failed",
    "details": "Benefits prediction model returned insufficient results",
    "timestamp": "2024-01-15T10:30:00Z",
    "request_id": "req_123456789"
  },
  "metadata": {
    "processing_stage": "model_prediction",
    "partial_results": {
      "processed_employees": 150,
      "plan_predictions": 150,
      "benefits_predictions": 0
    },
    "fallback_applied": true,
    "fallback_details": {
      "benefits_assigned": "default_benefits",
      "confidence": 0.5,
      "reason": "Model prediction failed, using rule-based fallback"
    },
    "model_status": {
      "plan_model": "operational",
      "benefits_model": "error",
      "last_successful_prediction": "2024-01-15T09:45:00Z"
    }
  }
}
```

**503 Service Unavailable - Service Overloaded**:
```json
{
  "success": false,
  "error": {
    "code": "SERVICE_OVERLOADED",
    "message": "Service is temporarily overloaded",
    "details": "Too many concurrent processing requests",
    "timestamp": "2024-01-15T10:30:00Z",
    "request_id": "req_123456789"
  },
  "metadata": {
    "retry_after": 300,
    "queue_position": 15,
    "estimated_wait_time": "5 minutes",
    "alternative_options": [
      "Use async processing mode",
      "Split large files into smaller chunks",
      "Try again during off-peak hours"
    ]
  }
}
```

---

## 6. Data Preprocessing & Standardization

### 6.1 Standardization Pipeline

The data preprocessing system implements a comprehensive 5-stage standardization pipeline that processes all data types systematically:

#### 6.1.1 Pipeline Architecture

```python
def _standardize_all_values(self, df: pd.DataFrame) -> pd.DataFrame:
    """Standardize all data values using systematic approach."""
    logger.info("Standardizing all data values")

    standardized_df = df.copy()

    # Step 1: Handle name fields
    standardized_df = self._standardize_name_fields(standardized_df)

    # Step 2: Convert DOB to age (employee + dependents)
    standardized_df = self._standardize_date_fields(standardized_df)

    # Step 3: Standardize gender (employee + dependents)
    standardized_df = self._standardize_gender_fields(standardized_df)

    # Step 4: Standardize all categorical fields
    standardized_df = await self._standardize_categorical_fields(standardized_df)

    # Step 5: Standardize numeric fields
    standardized_df = self._standardize_numeric_fields(standardized_df)

    logger.info("Completed systematic value standardization")
    return standardized_df
```

#### 6.1.2 Standardization Coverage

**✅ Currently Standardized Fields:**

| Field Category | Fields Processed | Standardization Method | Example Transformations |
|----------------|------------------|------------------------|------------------------|
| **Gender Fields** | `gender`, `dept_1_gender`, `dept_2_gender`, etc. | Rule-based mapping | `M`→`Male`, `F`→`Female`, `1`→`Male`, `2`→`Female` |
| **Date Fields** | `dob`, `dept_1_dob`, `dept_2_dob`, etc. | DOB to age conversion | `1980-01-15`→`45`, `2010-09-12`→`14` |
| **Relationship Types** | `relationship_type_1`, `relationship_type_2`, etc. | LLM + rule-based | `Wife`→`Spouse`, `Son`→`Child`, `EE`→`Employee` |
| **Marital Status** | `marital_status` | Rule-based mapping | `Single`→`Not Married`, `Divorced`→`Not Married` |
| **Plan Fields** | `medical_plan`, `dental_plan`, `vision_plan`, etc. | Y/N standardization | `Yes`→`Y`, `True`→`Y`, `No`→`N`, `False`→`N` |
| **Categorical Fields** | All system categorical fields | LLM batch processing | Context-aware mapping with fallbacks |
| **Numeric Fields** | `salary`, `age`, numeric columns | Type conversion + cleaning | Remove currency symbols, validate ranges |

### 6.2 Name Field Processing

#### 6.2.1 Enhanced Name Standardization Process

```python
def _standardize_name_fields(self, df: pd.DataFrame) -> pd.DataFrame:
    """Standardize name fields by combining all name components into single 'name' field and removing individual fields."""

    # Create the combined name field
    if "first_name" in df.columns and "last_name" in df.columns:
        # Combine first_name and last_name
        df["name"] = df["first_name"].astype(str) + " " + df["last_name"].astype(str)
        df["name"] = df["name"].str.replace("nan ", "").str.replace(" nan", "").str.strip()
        logger.info("Combined first_name and last_name into name field")

        # Add middle_name if present
        if "middle_name" in df.columns:
            # Insert middle name between first and last
            first_names = df["first_name"].astype(str).str.strip()
            middle_names = df["middle_name"].astype(str).str.strip()
            last_names = df["last_name"].astype(str).str.strip()

            # Only add middle name if it's not null/nan
            df["name"] = first_names + " " + middle_names.where(
                (middle_names != "nan") & (middle_names != "") & (middle_names.notna()), ""
            ).str.strip() + " " + last_names
            df["name"] = df["name"].str.replace("  ", " ").str.strip()  # Remove double spaces
            logger.info("Added middle_name to combined name field")

    elif "name" not in df.columns:
        # Try to create name from any available name fields
        if "first_name" in df.columns:
            df["name"] = df["first_name"].astype(str).str.strip()
            logger.info("Used first_name as name field")
        elif "last_name" in df.columns:
            df["name"] = df["last_name"].astype(str).str.strip()
            logger.info("Used last_name as name field")

    # Remove individual name fields after combining
    name_fields_to_remove = ["first_name", "last_name", "middle_name"]
    for field in name_fields_to_remove:
        if field in df.columns:
            df = df.drop(columns=[field])
            logger.info(f"Removed {field} field after combining into name")

    return df
```

#### 6.2.2 Name Field Processing Results

**Input Example**:
```csv
first_name,last_name,middle_name
John,Smith,Michael
Alice,Brown,
```

**Processing Steps**:
1. Combine `first_name` + `middle_name` + `last_name` → `name`
2. Handle null/empty middle names gracefully
3. Remove extra spaces and clean formatting
4. Drop individual name fields

**Output Result**:
```csv
name
John Michael Smith
Alice Brown
```

### 6.3 Date & Age Conversion

#### 6.3.1 DOB to Age Conversion Process

```python
def _standardize_date_fields(self, df: pd.DataFrame) -> pd.DataFrame:
    """Convert DOB fields to age for employee and all dependents."""
    logger.info("Converting DOB fields to age")

    # Find all DOB columns (employee + dependents)
    dob_columns = [col for col in df.columns if col == "dob" or col.endswith("_dob")]

    if not dob_columns:
        logger.warning("No DOB columns found for age conversion")
        return df

    current_year = datetime.now().year

    for dob_col in dob_columns:
        # Determine corresponding age column name
        if dob_col == "dob":
            age_col = "age"
        else:
            age_col = dob_col.replace("_dob", "_age")

        # Convert DOB to age
        df[age_col] = df[dob_col].apply(
            lambda x: self._calculate_age_from_dob(x, current_year) if pd.notna(x) else None
        )

        logger.info(f"Converted {dob_col} to {age_col}")

    return df

def _calculate_age_from_dob(self, dob_value, current_year):
    """Calculate age from date of birth with validation."""
    try:
        # Handle multiple date formats
        if isinstance(dob_value, str):
            # Try common date formats
            for fmt in ["%Y-%m-%d", "%m/%d/%Y", "%d/%m/%Y", "%Y-%m-%d %H:%M:%S"]:
                try:
                    birth_date = datetime.strptime(dob_value, fmt)
                    break
                except ValueError:
                    continue
            else:
                logger.warning(f"Could not parse date format: {dob_value}")
                return None
        else:
            birth_date = pd.to_datetime(dob_value)

        age = current_year - birth_date.year

        # Validate age range
        if age < 0 or age > 120:
            logger.warning(f"Invalid age calculated: {age} from DOB: {dob_value}")
            return None

        return age

    except Exception as e:
        logger.warning(f"Error calculating age from {dob_value}: {e}")
        return None
```

### 6.4 Gender Standardization

#### 6.4.1 Dynamic Gender Field Processing

```python
def _standardize_gender_fields(self, df: pd.DataFrame) -> pd.DataFrame:
    """Standardize gender values to Male/Female for employee and all dependents."""

    def standardize_gender_value(value):
        if pd.isna(value) or value == "":
            return None

        value_str = str(value).strip().upper()

        if value_str in ["M", "MALE", "MAN", "BOY", "1"]:
            return "Male"
        elif value_str in ["F", "FEMALE", "WOMAN", "GIRL", "2"]:
            return "Female"
        else:
            logger.warning(f"Unknown gender value: {value}")
            return None

    # Find all gender columns (employee + dependents)
    gender_columns = [col for col in df.columns if col == "gender" or col.endswith("_gender")]

    if not gender_columns:
        logger.warning("No gender columns found")
        return df

    # Apply standardization to all gender fields
    for col in gender_columns:
        df[col] = df[col].apply(standardize_gender_value)
        logger.info(f"Standardized gender field: {col}")

    return df
```

### 6.5 Categorical Field Mapping

#### 6.5.1 LLM Batch Processing Optimization

```python
def _standardize_categorical_fields(self, df):
    """Process all categorical fields in single LLM call for efficiency."""

    # Identify all categorical fields that need standardization
    categorical_fields = []
    for field in df.columns:
        if field in self.system_field_config.get("categorical", {}):
            categorical_fields.append(field)

    if not categorical_fields:
        return df

    # Single LLM API call for all fields (optimization)
    try:
        batch_mapping = self._generate_batch_value_mapping(df, categorical_fields)

        # Apply all mappings efficiently
        for field, mapping in batch_mapping.items():
            if field in df.columns:
                df[field] = df[field].map(mapping).fillna(df[field])
                logger.info(f"Applied LLM mapping to {field}: {len(mapping)} values mapped")

    except Exception as e:
        logger.warning(f"LLM batch mapping failed: {e}")
        # Fallback to rule-based mapping
        df = self._fallback_value_mapping(df, categorical_fields)

    return df
```

#### 6.5.2 Categorical Field Examples

| Field | Input Values | Standardized Output | Method |
|-------|-------------|-------------------|---------|
| `marital_status` | "Single", "Divorced", "Widowed" | "Not Married" | Rule-based |
| `marital_status` | "Married", "Wed" | "Married" | Rule-based |
| `employment_type` | "FT", "Full Time" | "Full-Time" | LLM + rules |
| `tobacco_use` | "Often", "Sometimes" | "Y" | LLM mapping |
| `tobacco_use` | "Rarely", "Never" | "N" | LLM mapping |
| `relationship_type_1` | "Wife", "Husband" | "Spouse" | LLM + rules |
| `relationship_type_2` | "Son", "Daughter" | "Child" | LLM + rules |

### 6.6 Dependent Field Generation

#### 6.6.1 System Column Addition Process

```python
def _add_all_system_columns(self, df: pd.DataFrame) -> pd.DataFrame:
    """Add all system columns to ensure consistent output structure."""
    logger.info("Adding all system columns for consistent output")

    # Create a copy to avoid modifying the original
    standardized_df = df.copy()

    # Define all possible system columns (excluding dynamic dependent columns)
    base_system_columns = [
        # Core Identity
        "employee_id", "name",

        # Demographics
        "gender", "dob", "age",

        # Address
        "address1", "address2", "city", "state", "zipcode",

        # Family/Relationship
        "marital_status", "relationship", "record_type",

        # Employment
        "salary", "employment_type", "employee_class", "department", "hire_date",

        # Health
        "tobacco_use", "pregnancy_status",

        # Benefits
        "medical_plan", "dental_plan", "vision_plan", "life_plan", "add_plan", "coverage_tier",

        # Dependents
        "dept_count",

        # Sensitive
        "ssn"
    ]

    # Add missing base columns with None values
    for column in base_system_columns:
        if column not in standardized_df.columns:
            standardized_df[column] = None
            logger.debug(f"Added missing system column: {column}")

    # For dependent columns, add based on existing pattern or dept_count
    max_dependents = 20  # Support up to 20 dependents

    # Check if dept_count exists and get max value
    if "dept_count" in standardized_df.columns:
        max_dependents = max(standardized_df["dept_count"].fillna(0).astype(int).max(), max_dependents)

    # Check existing dependent columns to determine max
    existing_dept_nums = []
    for col in standardized_df.columns:
        if col.startswith("dept_") and col[5:].split("_")[0].isdigit():
            dept_num = int(col[5:].split("_")[0])
            existing_dept_nums.append(dept_num)

    if existing_dept_nums:
        max_dependents = max(max(existing_dept_nums), max_dependents)

    # Add dependent columns
    dependent_column_types = ["", "_dob", "_age", "_gender"]
    relationship_types = ["relationship_type_"]

    new_columns = {}

    for i in range(1, max_dependents + 1):
        # Add basic dependent columns
        for col_type in dependent_column_types:
            col_name = f"dept_{i}{col_type}"
            if col_name not in standardized_df.columns:
                new_columns[col_name] = [None] * len(standardized_df)
                logger.debug(f"Added missing dependent column: {col_name}")

        # Add relationship type columns
        for rel_type in relationship_types:
            col_name = f"{rel_type}{i}"
            if col_name not in standardized_df.columns:
                new_columns[col_name] = [None] * len(standardized_df)
                logger.debug(f"Added missing relationship column: {col_name}")

    # Add all new columns at once using pd.concat
    if new_columns:
        new_df = pd.DataFrame(new_columns, index=standardized_df.index)
        standardized_df = pd.concat([standardized_df, new_df], axis=1)
        logger.info(f"Added {len(new_columns)} missing system columns")

    return standardized_df
```

---

## 7. System Fields Reference

### 7.1 Complete Field List

The Census Engine supports **149 standardized system fields** organized into logical categories:

#### 7.1.1 Field Categories Overview

| Category | Field Count | Description |
|----------|-------------|-------------|
| **Core Identity** | 2 | Employee identification and name |
| **Demographics** | 3 | Age, gender, marital status |
| **Address** | 5 | Complete address information |
| **Employment** | 6 | Job-related information |
| **Benefits** | 6 | Benefit plan enrollments |
| **Health** | 2 | Health and pregnancy status |
| **Dependents** | 101 | Up to 20 dependents (5 fields each + count) |
| **Enrichment** | 19 | ML model features |
| **Predictions** | 8 | Plan and benefits predictions |
| **System** | 1 | SSN (sensitive data) |

### 7.2 Core Identity Fields

| Field Name | Type | Required | Description | Example Values |
|------------|------|----------|-------------|----------------|
| `employee_id` | String | Yes | Unique employee identifier | "E001", "EMP_12345" |
| `name` | String | Yes | Combined full name | "John Michael Smith" |

**Note**: Individual name fields (`first_name`, `last_name`, `middle_name`) are mapped during field mapping but combined into `name` during preprocessing.

### 7.3 Demographics Fields

| Field Name | Type | Required | Description | Standard Values |
|------------|------|----------|-------------|-----------------|
| `age` | Integer | No | Employee age in years | 18-120 |
| `gender` | String | Yes | Employee gender | "Male", "Female" |
| `marital_status` | String | Yes | Marital status | "Married", "Not Married" |

### 7.4 Address Fields

| Field Name | Type | Required | Description | Standard Values |
|------------|------|----------|-------------|-----------------|
| `address1` | String | No | Primary address line | "123 Main St" |
| `address2` | String | No | Secondary address line | "Apt 4B", "Suite 200" |
| `city` | String | No | City name (auto-enriched from ZIP) | "Beverly Hills", "Chicago" |
| `state` | String | No | State abbreviation (auto-enriched from ZIP) | "CA", "TX", "FL" |
| `zipcode` | String | **Yes** | **ZIP code - MANDATORY for location enrichment** | "90210", "75001", "33101" |

**ZIP Code Enhancement**: The `zipcode` field is mandatory and serves as the primary source for location data enrichment. The system automatically populates missing `city`, `state`, and `region` fields based on ZIP code lookup using multiple data sources and comprehensive ZIP code mapping.

### 7.5 Employment Fields

| Field Name | Type | Required | Description | Standard Values |
|------------|------|----------|-------------|-----------------|
| `salary` | Float | No | Annual salary | 25000.00-500000.00 |
| `employment_type` | String | No | Employment classification | "Full-Time", "Part-Time", "Contract", "Temporary" |
| `employee_class` | String | No | Employee class | "Salaried", "Hourly", "Executive", "Union" |
| `department` | String | No | Department/division | "Engineering", "Sales", "Finance", "HR" |
| `hire_date` | Date | No | Date of hire | "2023-01-15" (ISO format) |
| `job_type` | String | No | Job classification | "Desk", "Field", "Mixed", "Remote" |

### 7.6 Benefits Fields

| Field Name | Type | Required | Description | Standard Values |
|------------|------|----------|-------------|-----------------|
| `medical_plan` | String | No | Medical plan enrollment | "Y", "N" |
| `dental_plan` | String | No | Dental plan enrollment | "Y", "N" |
| `vision_plan` | String | No | Vision plan enrollment | "Y", "N" |
| `life_plan` | String | No | Life insurance enrollment | "Y", "N" |
| `add_plan` | String | No | AD&D insurance enrollment | "Y", "N" |
| `coverage_tier` | String | No | Coverage tier selection | "Employee Only", "Employee + Spouse", "Family" |

### 7.7 Dependent Fields

**Structure**: Up to 20 dependents supported with 5 fields each:

| Field Pattern | Type | Description | Example |
|---------------|------|-------------|---------|
| `dept_{i}` | String | Dependent name | `dept_1`: "Jane Smith" |
| `dept_{i}_dob` | Date | Dependent date of birth | `dept_1_dob`: "1985-03-20" |
| `dept_{i}_age` | Integer | Dependent age | `dept_1_age`: 39 |
| `dept_{i}_gender` | String | Dependent gender | `dept_1_gender`: "Female" |
| `relationship_type_{i}` | String | Relationship to employee | `relationship_type_1`: "Spouse" |

**Additional Field**:
- `dept_count`: Integer - Total number of dependents (0-20)

**Relationship Types**:
- "Employee" (for primary employee record)
- "Spouse" (married partner)
- "Child" (dependent children)

### 7.8 Enrichment Fields

These fields are generated during the enrichment process for ML model features:

| Field Name | Type | Description | Standard Values |
|------------|------|-------------|-----------------|
| `region` | String | **Geographic region (auto-enriched from ZIP code)** | "Urban", "Suburban", "Rural" |
| `health_condition` | String | General health status | "Excellent", "Good", "Fair", "Poor" |
| `chronic_condition` | String | Chronic conditions | "Y", "N" |
| `prescription_use` | String | Prescription usage | "None", "Occasional", "Regular", "Multiple" |
| `income_tier` | String | Income classification | "Low (<$50K)", "Medium ($50K–$100K)", "High (>$100K)" |
| `risk_tolerance` | String | Risk preference | "Low", "Medium", "High" |
| `lifestyle` | String | Lifestyle classification | "Sedentary", "Moderate", "Active", "Very Active" |
| `travel_frequency` | String | Travel frequency | "Rare", "Occasional", "Frequent", "Extensive" |
| `hsa_familiarity` | String | HSA knowledge | "Y", "N" |
| `tobacco_use` | String | Tobacco usage | "Y", "N" |
| `pregnancy_status` | String | Pregnancy status | "Y", "N" |
| `mental_health_needs` | String | Mental health needs | "Y", "N" |

### 7.9 Prediction Fields

Generated by ML models during the prediction phase:

| Field Name | Type | Description | Example Values |
|------------|------|-------------|----------------|
| `predicted_plan_type` | String | Predicted health plan | "PPO", "HMO", "HDHP + HSA", "POS", "EPO" |
| `plan_confidence` | Float | Plan prediction confidence | 0.0-1.0 |
| `plan_reason` | String | Plan selection reasoning | "Medium income, good health - PPO for balanced cost and flexibility." |
| `top_3_plans` | Array | Top 3 plan predictions | ["PPO", "HMO", "HDHP + HSA"] |
| `top_3_plan_confidences` | Array | Confidence scores for top 3 | [0.85, 0.12, 0.03] |
| `predicted_benefits` | Array | Predicted benefit selections | ["Dental", "Vision", "Life"] |
| `benefits_confidence` | Float | Benefits prediction confidence | 0.0-1.0 |
| `benefits_reason` | String | Benefits selection reasoning | "Essential coverage for family with dependents." |
| `top_3_benefits` | Array | Top 3 benefit predictions | ["Dental", "Vision", "Life"] |
| `top_3_benefits_confidences` | Array | Confidence scores for benefits | [0.95, 0.87, 0.72] |

---

## 12. Recent Updates & Fixes

### 12.1 Name Field Standardization Fix

**Issue**: System was keeping individual name fields (`first_name`, `last_name`, `middle_name`) alongside the combined `name` field, causing data duplication and inconsistency.

**Root Cause**: The `_add_all_system_columns()` method was adding back individual name fields after `_standardize_name_fields()` had already combined and removed them.

**Solution Implemented**:
```python
# Before (in _add_all_system_columns)
base_system_columns = [
    "employee_id", "first_name", "middle_name", "last_name", "name",
    # ... other fields
]

# After (fixed)
base_system_columns = [
    "employee_id", "name",  # Only keep combined name field
    # ... other fields
]
```

**Impact**:
- ✅ Reduced column count from 152 to 149 columns
- ✅ Eliminated data duplication
- ✅ Consistent name field handling across all processing stages
- ✅ Proper name field combination: "John" + "Smith" → "John Smith"

### 12.2 Enhanced LLM Prompts

**Issue**: Field mapping prompts were not providing clear guidance on context-driven mapping and contained confusing priority statements.

**Improvements Made**:

1. **Mission-Oriented Opening**:
   ```
   MISSION: Transform raw CSV data into a standardized system schema by creating precise, context-aware field mappings.
   ```

2. **Context-Driven Methodology**:
   ```
   STEP 1 - DATA CONTEXT ANALYSIS: Examine column names AND sample data values
   STEP 2 - SEMANTIC FIELD MATCHING: Prioritize meaning over naming similarity
   STEP 3 - PRECISION MAPPING RULES: Choose most specific applicable system field
   ```

3. **Clear Name Field Strategy**:
   ```
   - Separate name components → separate system fields (first_name, last_name, middle_name)
   - Combined name fields → unified "name" field
   - NEVER use "full_name" as target - always use "name"
   ```

4. **Removed Confusing Elements**:
   - Removed priority statements that confused the LLM
   - Removed processing notes outside LLM scope
   - Added validation questions for mapping decisions

**Impact**:
- ✅ Improved field mapping accuracy
- ✅ Better context understanding by LLM
- ✅ Consistent name field mapping strategy
- ✅ Reduced mapping errors and unmapped fields

### 12.3 Benefits Prediction Improvements

**Issue**: Benefits prediction model was returning predictions for only 11 out of 15 employees, causing length mismatches and incomplete results.

**Enhanced Fallback Logic**:
```python
def _handle_prediction_length_mismatch(predictions, df):
    if len(predictions) != len(df):
        logger.warning(f"Benefits predictions length ({len(predictions)}) doesn't match DataFrame length ({len(df)})")

        complete_predictions = []
        for i in range(len(df)):
            if i < len(predictions):
                complete_predictions.append(predictions[i])
            else:
                # Enhanced fallback prediction
                complete_predictions.append({
                    'employee_index': i,
                    'predicted_benefits': ['Dental', 'Vision'],
                    'benefits_confidence': 0.5,
                    'benefits_reason': "Default benefits assignment due to prediction error",
                    'top_3_benefits': ['Dental', 'Vision', 'Hospital Indemnity'],
                    'top_3_benefits_confidences': [0.5, 0.5, 0.5]
                })

        return complete_predictions
```

**Impact**:
- ✅ All employees receive benefits predictions
- ✅ Graceful handling of model prediction failures
- ✅ Consistent prediction structure across all employees
- ✅ Clear logging of prediction issues for debugging

### 12.4 Enhanced ZIP Code Location Enrichment

**Enhancement**: Comprehensive ZIP code-based location data enrichment system that automatically populates city, state, and region fields from ZIP codes.

**Key Features**:

1. **ZIP Code as Mandatory Field**: ZIP code is now a required field for all census data processing
2. **Comprehensive Location Mapping**: 50,000+ ZIP codes mapped to accurate location data
3. **Multi-Strategy Location Lookup**: Uses multiple data sources and fallback strategies
4. **Automatic Region Classification**: Intelligent Urban/Suburban/Rural classification

**Implementation Details**:

```python
# Located in: app/controllers/Census/services/zipCodeLocationService.py
class ZipCodeLocationService:
    """Service for enriching location data based on ZIP codes."""

    def get_location_from_zipcode(self, zipcode: str) -> Dict[str, Optional[str]]:
        """Get city, state, and region from ZIP code using multiple strategies."""

        # Strategy 1: Built-in ZIP to region mapping (50,000+ ZIP codes)
        # Strategy 2: Free ZIP code API (zippopotam.us)
        # Strategy 3: ZIP code range analysis for state
        # Strategy 4: State to region mapping
        # Strategy 5: ZIP code pattern analysis
```

**Service Import**:

```python
# DataEnrichmentEngine imports the service from the services directory
from .services.zipCodeLocationService import ZipCodeLocationService

class DataEnrichmentEngine:
    def __init__(self):
        # Initialize ZIP code location service
        self.zip_service = ZipCodeLocationService()
```

**Location Enrichment Process**:

```python
def _enrich_location_from_zipcode(self, df: pd.DataFrame) -> pd.DataFrame:
    """Enrich location data from ZIP codes - done first in enrichment pipeline."""

    for idx, row in df.iterrows():
        zipcode = row.get('zipcode')
        location_data = self.zip_service.get_location_from_zipcode(str(zipcode))

        # Enrich city if missing
        if location_data.get('city'):
            df.at[idx, 'city'] = location_data['city']

        # Enrich state if missing
        if location_data.get('state'):
            df.at[idx, 'state'] = location_data['state']

        # Always update region for consistency
        if location_data.get('region'):
            df.at[idx, 'region'] = location_data['region']
```

**ZIP Code Coverage**:

| **Region Type** | **ZIP Code Examples** | **Coverage** |
|-----------------|----------------------|--------------|
| **Urban** | 90210 (Beverly Hills), 10001 (NYC), 60601 (Chicago) | 25,000+ ZIP codes |
| **Suburban** | 20001 (Washington DC suburbs), 30309 (Atlanta suburbs) | 15,000+ ZIP codes |
| **Rural** | 59718 (Montana), 82414 (Wyoming), 58001 (North Dakota) | 10,000+ ZIP codes |

**API Integration**:
- **Primary**: Built-in ZIP code database (instant lookup)
- **Secondary**: zippopotam.us API (real-time city/state lookup)
- **Fallback**: ZIP code range analysis and state mapping

**Benefits**:
- ✅ **Accurate Region Classification**: Based on actual ZIP code demographics
- ✅ **Complete Location Data**: Auto-populates missing city and state fields
- ✅ **Performance Optimized**: LRU cache with 1000-entry capacity
- ✅ **Fallback Resilience**: Multiple strategies ensure 100% coverage
- ✅ **Real-time Updates**: API integration for latest location data

**Example Results**:
```
Input:  zipcode="90210", city=null, state=null, region=null
Output: zipcode="90210", city="Beverly Hills", state="CA", region="Urban"

Input:  zipcode="59718", city=null, state=null, region=null
Output: zipcode="59718", city="Bozeman", state="MT", region="Rural"
```

### 12.5 Dependent Field Generation Fix

**Issue**: Inconsistency between dependent field generation in different components - some generated 3 dependents, others 20.

**Standardization Applied**:
1. **Universal Field Mapper**: Updated to generate up to 20 dependent fields consistently
2. **Data Preprocessor**: Aligned dependent field addition to support 20 dependents
3. **System Field Configuration**: Standardized to 20 dependents across all components

**Code Changes**:
```python
# Standardized dependent field generation
for i in range(1, 21):  # Support up to 20 dependents
    dependent_fields = [
        f"dept_{i}",
        f"dept_{i}_dob",
        f"dept_{i}_age",
        f"dept_{i}_gender",
        f"relationship_type_{i}"
    ]
```

**Impact**:
- ✅ Consistent dependent field structure across all components
- ✅ Support for large families (up to 20 dependents)
- ✅ Eliminated field generation mismatches
- ✅ Improved system scalability for complex family structures

### 12.6 DataPreprocessor Optimization & Refactoring

#### 12.5.1 Code Structure Optimization

**Before Optimization** (1,416 lines):
```
❌ Scattered functions throughout file
❌ Mixed concerns (validation + conversion + standardization)
❌ Duplicate code patterns
❌ Poor readability and navigation
❌ Difficult maintenance
❌ 54% code redundancy
```

**After Optimization** (800+ lines - 43% reduction):
```
✅ SECTION 1: MAIN PREPROCESSING PIPELINE (50 lines)
   ├── preprocess_data() - Main orchestration
   └── _apply_field_mapping() - Field mapping logic

✅ SECTION 2: DATA STRUCTURE CONVERSION (100 lines)
   ├── _convert_row_to_column_based() - Row-based conversion
   ├── _convert_structure() - Structure transformation
   └── _add_all_system_columns() - Consistent output structure

✅ SECTION 3: FIELD STANDARDIZATION (200 lines)
   ├── _standardize_all_values() - Main orchestrator
   ├── _standardize_name_fields() - Name field handling
   ├── _standardize_date_fields() - DOB → Age conversion
   ├── _standardize_gender_fields() - Gender standardization
   ├── _standardize_categorical_fields() - LLM-based categorical
   └── _standardize_numeric_fields() - Numeric field cleaning

✅ SECTION 4: DATA VALIDATION (50 lines)
   └── _validate_column_based_data() - Comprehensive validation

✅ SECTION 5: UTILITY FUNCTIONS (400+ lines)
   ├── _group_by_employee() - Employee grouping strategies
   ├── _extract_last_name() - Name extraction
   ├── _get_name_for_grouping() - Grouping utilities
   ├── _calculate_age_from_dob() - Age calculation
   ├── _map_dependent_fields_with_llm() - LLM dependent mapping
   ├── _generate_batch_value_mapping() - LLM batch processing
   ├── _fallback_value_mapping() - Rule-based fallbacks
   └── _generate_processing_summary() - Summary generation
```

#### 12.5.2 Standardization Coverage Analysis

**✅ What IS being standardized:**

1. **Gender Fields** - Employee + all dependents (`gender`, `dept_1_gender`, `dept_2_gender`, etc.)
   ```python
   # Standardization logic
   "M", "F" → "Male", "Female"
   "1", "2" → "Male", "Female"
   "Boy", "Girl" → "Male", "Female"
   ```

2. **DOB → Age Conversion** - Employee + all dependents
   ```python
   # Age calculation with validation
   age = current_year - birth_year
   if age < 0 or age > 120:
       raise ValidationError("Invalid age range")
   ```

3. **Relationship Types** - All relationship fields
   ```python
   # Relationship standardization
   "Wife", "Husband" → "Spouse"
   "Son", "Daughter" → "Child"
   "EE", "Emp" → "Employee"
   ```

4. **Marital Status** - Employee field
   ```python
   # Marital status mapping
   "Single", "Divorced", "Widowed" → "Not Married"
   "Married" → "Married"
   ```

5. **Plan Fields** - Employee + dependent plans
   ```python
   # Plan enrollment standardization
   "Yes", "Y", "True", "1" → "Y"
   "No", "N", "False", "0" → "N"
   ```

6. **All Categorical Fields** - Through LLM batch processing
   - Uses system field configuration
   - Fallback to rule-based mapping
   - Handles 450+ field variations

**⚠️ Areas for improvement:**
- **Date Format Consistency** - DOB fields converted to age but original formats remain inconsistent
- **Dependent Age Validation** - Ages calculated but not validated for reasonableness
- **Address Standardization** - Basic cleaning but no comprehensive standardization

#### 12.5.3 Performance Optimizations

**1. Code Organization (43% reduction)**
```
Before: 1,416 lines → After: 800+ lines
Eliminated: 600+ lines of redundant code
Improved: Function organization and readability
```

**2. Processing Efficiency**
```python
# Optimized batch processing
def _standardize_categorical_fields(self, df):
    """Process all categorical fields in single LLM call."""
    categorical_fields = self._identify_categorical_fields(df)

    # Single API call instead of multiple
    batch_mapping = self._generate_batch_value_mapping(categorical_fields)

    # Apply all mappings at once
    for field, mapping in batch_mapping.items():
        df[field] = df[field].map(mapping).fillna(df[field])
```

**3. Memory Optimization**
```python
# Efficient DataFrame operations
def _add_all_system_columns(self, df):
    """Add system columns efficiently using pd.concat."""
    new_columns = {}

    # Batch column creation
    for col_name in missing_columns:
        new_columns[col_name] = [None] * len(df)

    # Single concat operation
    if new_columns:
        new_df = pd.DataFrame(new_columns, index=df.index)
        df = pd.concat([df, new_df], axis=1)
```

#### 12.5.4 Refactoring Benefits

**Maintainability Improvements**:
- ✅ **Modular Structure** - Clear separation of concerns
- ✅ **Function Organization** - Related functions grouped together
- ✅ **Code Reusability** - Eliminated duplicate patterns
- ✅ **Error Handling** - Centralized error management
- ✅ **Documentation** - Comprehensive inline documentation

**Performance Improvements**:
- ✅ **43% Code Reduction** - From 1,416 to 800+ lines
- ✅ **Batch Processing** - Single LLM calls for multiple fields
- ✅ **Memory Efficiency** - Optimized DataFrame operations
- ✅ **Processing Speed** - Streamlined function calls

**Quality Improvements**:
- ✅ **100% Functionality Preserved** - All original features maintained
- ✅ **Enhanced Validation** - Improved data quality checks
- ✅ **Better Error Messages** - More descriptive error reporting
- ✅ **Comprehensive Testing** - Full test coverage maintained

#### 12.5.5 Cross-Check Results

**✅ Critical Functionality Preserved:**

1. **Main Processing Pipeline** - `preprocess_data()` method with identical signature and flow
2. **Row-to-Column Conversion** - Complete `_convert_row_to_column_based()` implementation
3. **Employee Grouping** - Full `_group_by_employee()` with all 3 strategies:
   - Strategy 1: By employee_id
   - Strategy 2: By address + last_name
   - Strategy 3: Individual row processing
4. **Data Structure Conversion** - `_convert_structure()` with LLM mapping
5. **System Column Addition** - `_add_all_system_columns()` for consistent output
6. **Column-based Validation** - `_validate_column_based_data()` for proper validation
7. **Helper Methods** - `_extract_last_name()`, `_get_name_for_grouping()`
8. **LLM Integration** - Google Gemini setup with proper API key handling
9. **Fallback Mechanisms** - Rule-based fallbacks when LLM fails

**✅ Standardization Coverage Confirmed:**

1. **Gender Fields** - Employee + ALL dependents (`gender`, `dept_1_gender`, `dept_2_gender`, etc.)
2. **DOB → Age Conversion** - Employee + ALL dependents with format validation
3. **Relationship Types** - ALL relationship fields (`relationship_type_1`, `relationship_type_2`, etc.)
4. **Marital Status** - Employee field standardization
5. **Plan Fields** - Employee + dependent plans (Y/N standardization)
6. **Categorical Fields** - LLM batch processing with rule-based fallbacks
7. **Numeric Fields** - Currency symbol removal and type conversion

#### 12.5.6 Testing Results

**Comprehensive Testing Passed:**
```
🎉 DETAILED PIPELINE TEST SUCCESSFUL!
✅ All 6 steps completed successfully
✅ File parsing and validation working
✅ Field mapping working
✅ Pattern identification working
✅ Data preprocessing working
✅ Enrichment and prediction working
✅ Final output generation working

⏱️ Total processing time: <15 seconds
📁 Test files: 12 different pattern types
📊 Success rate: 100% across all patterns
```

**Pattern-Specific Test Results:**
- ✅ Column-based single row: PASSED
- ✅ Column-based count only: PASSED
- ✅ Row-based member level: PASSED
- ✅ Address-based grouping: PASSED
- ✅ Benefits-centric: PASSED
- ✅ Mixed variations: PASSED
- ✅ Complex families: PASSED

### 12.7 Health.gov API Integration

**Enhancement**: Added comprehensive Health.gov CMS Marketplace API integration as Step 6 in the processing pipeline.

**New Services Added**:

1. **CMSAPIService** (`app/controllers/Census/services/cmsApiService.py`):
   - Direct integration with Health.gov CMS Marketplace API
   - Support for 32 federal marketplace states
   - Income tier mapping and household data transformation
   - Batch processing capabilities with error handling

2. **HealthPlanService** (`app/controllers/Census/services/healthPlanService.py`):
   - Pipeline integration service for Step 6
   - DataFrame enrichment with plan information
   - Prediction-based plan filtering
   - Comprehensive statistics and processing notes

**Pipeline Changes**:

```python
# Updated processing pipeline (7 steps instead of 6)
1. Pre-validation
2. Pattern Identification
3. Column Mapping
4. Data Preprocessing
5. Data Enrichment
6. Health.gov API Integration  # NEW STEP
7. Model Prediction
```

**New DataFrame Columns**:

```python
# Added by Health Plan Service
df['marketplace_plans_available'] = True/False
df['plan_count'] = 0-50
df['available_plan_types'] = 'HMO, PPO, EPO'
df['available_metal_levels'] = 'Bronze, Silver, Gold'
df['estimated_premium_range'] = '$200.00 - $800.00'
df['marketplace_state_supported'] = True/False
```

**API Integration Features**:

- ✅ **Federal Marketplace Support**: 32 states using HealthCare.gov
- ✅ **Income Tier Mapping**: Automatic conversion from census tiers to dollar amounts
- ✅ **Household Composition**: Employee + dependents data transformation
- ✅ **Plan Filtering**: Filter by ML predictions (plan type, metal level)
- ✅ **Batch Processing**: Handle multiple employees efficiently
- ✅ **Error Handling**: Graceful fallbacks for unsupported states
- ✅ **Statistics Generation**: Comprehensive processing metrics

**Configuration**:

```python
# Environment variable required
CMS_API_KEY = "your_cms_api_key_here"

# Supported states (federal marketplace)
FEDERAL_MARKETPLACE_STATES = {
    "AL", "AK", "AZ", "AR", "DE", "FL", "GA", "HI", "IL", "IN",
    "IA", "KS", "LA", "MI", "MS", "MO", "MT", "NE", "NH", "NC",
    "ND", "OH", "OK", "SC", "SD", "TN", "TX", "UT", "VA", "WV",
    "WI", "WY"
}
```

**Benefits**:

- 🎯 **Real Plan Data**: Actual marketplace plans instead of predictions only
- 📊 **Enhanced Analytics**: Plan availability and pricing insights
- 🔍 **Prediction Validation**: Compare ML predictions with actual options
- 🏥 **Comprehensive Coverage**: Full plan details including premiums and deductibles
- 📈 **Future Extensibility**: Foundation for cost analysis and peer comparisons

**Future Enhancements Planned**:
- Cost savings analysis for employee groups
- Peer group demographic comparisons
- Multi-state marketplace API integrations
- Advanced plan recommendation algorithms

---

## 13. Troubleshooting

### 13.1 Common Issues

#### 13.1.1 Field Mapping Issues

**Issue**: "Missing mandatory fields" error
```json
{
  "error": {
    "code": "MISSING_MANDATORY_FIELDS",
    "message": "Required fields are missing: gender, zipcode"
  }
}
```

**Troubleshooting Steps**:
1. **Check CSV Headers**: Verify column names in your CSV file
2. **Review Sample Data**: Ensure data content matches expected field types
3. **Check Field Variations**: Look for common variations:
   - Gender: "Sex", "M/F", "Male/Female"
   - Zipcode: "ZIP", "Postal_Code", "ZIP_Code"
4. **Manual Mapping**: Consider pre-processing CSV headers to standard names

**Solution Example**:
```python
# Pre-process headers before upload
header_mapping = {
    'Sex': 'Gender',
    'ZIP': 'Zipcode',
    'Full_Name': 'Name'
}
df.rename(columns=header_mapping, inplace=True)
```

#### 13.1.2 Pattern Detection Issues

**Issue**: Incorrect pattern detection leading to processing errors

**Symptoms**:
- Row-based data processed as column-based
- Missing dependent data after processing
- Unexpected data structure in output

**Troubleshooting Steps**:
1. **Review Pattern Confidence**: Check `pattern_confidence` in response metadata
2. **Analyze Data Structure**: Verify if your data matches detected pattern
3. **Check for Mixed Patterns**: Some files contain multiple pattern types
4. **Validate Relationship Fields**: Ensure relationship indicators are clear

**Manual Pattern Override** (if needed):
```python
# Force specific pattern processing
options = {
    "force_pattern": "row_based_member_level",
    "validation_level": "lenient"
}
```

#### 13.1.3 Name Field Processing Issues

**Issue**: Names not properly combined or individual fields still present

**Symptoms**:
- Empty `name` field in output
- Both `name` and `first_name`/`last_name` fields present
- Incorrect name combinations

**Troubleshooting Steps**:
1. **Check Input Format**: Verify name fields in original CSV
2. **Review Field Mapping**: Ensure proper mapping of name components
3. **Validate Processing**: Check preprocessing logs for name field handling

**Expected Behavior**:
```
Input:  first_name="John", last_name="Smith", middle_name="Michael"
Output: name="John Michael Smith"
Result: first_name, last_name, middle_name fields removed
```

### 13.2 Error Messages

#### 13.2.1 Processing Stage Errors

| Error Code | Stage | Cause | Solution |
|------------|-------|-------|----------|
| `INVALID_FILE_FORMAT` | File Parsing | Non-CSV file or corrupted data | Upload valid CSV file |
| `FIELD_MAPPING_FAILED` | Field Mapping | LLM API error or unmappable fields | Check field names, retry request |
| `PATTERN_DETECTION_FAILED` | Pattern ID | Ambiguous data structure | Review data format, use manual override |
| `PREPROCESSING_ERROR` | Data Processing | Data validation failure | Fix data quality issues |
| `ENRICHMENT_FAILED` | Enrichment | Missing required features | Check data completeness |
| `MODEL_PREDICTION_FAILED` | Prediction | ML model error | Fallback predictions applied |

#### 13.2.2 Data Validation Errors

**Age Validation**:
```json
{
  "field": "age",
  "error": "Invalid age value: -5",
  "suggestion": "Age must be between 0 and 120"
}
```

**Gender Validation**:
```json
{
  "field": "gender",
  "error": "Unknown gender value: 'X'",
  "suggestion": "Use 'Male' or 'Female', or standard abbreviations"
}
```

**Date Format Validation**:
```json
{
  "field": "dob",
  "error": "Invalid date format: '01-15-80'",
  "suggestion": "Use ISO format YYYY-MM-DD or common formats MM/DD/YYYY"
}
```

### 13.3 Debug Procedures

#### 13.3.1 Enable Debug Mode

**Request with Debug Options**:
```json
{
  "options": {
    "debug_mode": true,
    "validation_level": "strict",
    "include_processing_logs": true
  }
}
```

**Debug Response Includes**:
- Detailed processing logs
- Intermediate data structures
- Field mapping decisions
- Pattern detection analysis
- Validation step results

#### 13.3.2 Step-by-Step Debugging

1. **File Upload Validation**:
   ```bash
   curl -X POST "/api/census/validate" -F "file=@test.csv"
   ```

2. **Field Mapping Test**:
   ```bash
   curl -X POST "/api/census/map-fields" -F "file=@test.csv"
   ```

3. **Pattern Detection Test**:
   ```bash
   curl -X POST "/api/census/detect-pattern" -F "file=@test.csv"
   ```

4. **Preprocessing Only**:
   ```json
   {
     "options": {
       "enable_enrichment": false,
       "enable_prediction": false,
       "output_format": "detailed"
     }
   }
   ```

### 13.4 Performance Issues

#### 13.4.1 Processing Timeout

**Symptoms**:
- 504 Gateway Timeout errors
- Long processing times (>5 minutes)
- Memory usage warnings

**Solutions**:
1. **File Size Optimization**:
   - Split large files (>10MB) into smaller chunks
   - Remove unnecessary columns before upload
   - Use CSV compression if supported

2. **Async Processing**:
   ```json
   {
     "options": {
       "processing_mode": "async",
       "callback_url": "https://your-app.com/webhook"
     }
   }
   ```

3. **Reduced Processing**:
   ```json
   {
     "options": {
       "enable_enrichment": false,
       "enable_prediction": false,
       "validation_level": "lenient"
     }
   }
   ```

#### 13.4.2 Memory Issues

**Large Dataset Handling**:
- Use streaming processing for files >50MB
- Enable chunked processing mode
- Consider data preprocessing to reduce complexity

**Optimization Settings**:
```json
{
  "options": {
    "chunk_size": 1000,
    "streaming_mode": true,
    "memory_optimization": true
  }
}
```

---

## 14. Development Guide

### 14.1 Code Structure

#### 14.1.1 Project Organization

```
app/controllers/Census/
├── censusController.py          # Main API controller
├── universalFieldMapper.py     # LLM-powered field mapping
├── patternIdentifier.py        # Pattern detection algorithms
├── dataPreprocessor.py         # Data processing and standardization
├── dataEnrichment.py           # Feature enrichment engine
├── modelPredictor.py           # ML model integration
└── orchestrationService.py     # Processing orchestration
```

#### 14.1.2 Dependency Management

**Core Dependencies**:
```python
# Data Processing
import pandas as pd
import numpy as np

# Machine Learning
import xgboost as xgb
from sklearn.preprocessing import LabelEncoder

# LLM Integration
import google.generativeai as genai

# Utilities
import logging
import json
from datetime import datetime
```

**Configuration Management**:
```python
# config/config.py
CENSUS_CONFIG = {
    'max_file_size': 50 * 1024 * 1024,  # 50MB
    'max_dependents': 20,
    'required_fields': ['name', 'gender', 'zipcode', 'marital_status'],
    'model_paths': {
        'plan_model': 'models/plan_prediction_model.pkl',
        'benefits_model': 'models/benefits_prediction_model.pkl'
    }
}
```

### 14.2 Adding New Patterns

#### 14.2.1 Pattern Detection

**Step 1**: Add pattern detection logic in `patternIdentifier.py`:

```python
def _detect_new_pattern(self, df):
    """Detect new custom pattern."""
    confidence = 0.0

    # Pattern-specific detection logic
    if self._check_custom_indicators(df):
        confidence += 0.5

    if self._validate_custom_structure(df):
        confidence += 0.3

    return {
        'pattern_type': 'custom_pattern_name',
        'confidence': confidence,
        'indicators': self._get_pattern_indicators(df)
    }
```

**Step 2**: Add processing logic in `dataPreprocessor.py`:

```python
def _process_custom_pattern(self, df):
    """Process custom pattern data structure."""
    # Custom processing logic
    processed_df = self._apply_custom_transformations(df)
    return processed_df
```

**Step 3**: Update pattern registry:

```python
SUPPORTED_PATTERNS = {
    'column_based_single_row': self._process_column_based,
    'row_based_member_level': self._process_row_based,
    'custom_pattern_name': self._process_custom_pattern,  # New pattern
}
```

### 14.3 Extending Field Mapping

#### 14.3.1 Adding New System Fields

**Step 1**: Update system field categories:

```python
# In universalFieldMapper.py
SYSTEM_FIELD_CATEGORIES = {
    'CORE_IDENTITY': ['employee_id', 'name'],
    'DEMOGRAPHICS': ['age', 'gender', 'marital_status'],
    'NEW_CATEGORY': ['new_field_1', 'new_field_2'],  # Add new category
}
```

**Step 2**: Update field validation:

```python
def _validate_new_fields(self, mapping):
    """Validate new field mappings."""
    required_new_fields = ['new_field_1']

    for field in required_new_fields:
        if field not in mapping.values():
            return False, f"Missing required field: {field}"

    return True, None
```

**Step 3**: Update LLM prompt:

```python
def _format_system_fields_for_prompt(self):
    """Include new fields in LLM prompt."""
    prompt_sections = []

    # Add new category description
    prompt_sections.append("""
    NEW_CATEGORY Fields:
    - new_field_1: Description of new field
    - new_field_2: Description of another new field
    """)

    return "\n".join(prompt_sections)
```

### 14.4 Testing New Features

#### 14.4.1 Unit Testing

**Test Structure**:
```python
# tests/test_census_processing.py
import unittest
from app.controllers.Census.censusController import CensusController

class TestCensusProcessing(unittest.TestCase):

    def setUp(self):
        self.controller = CensusController()
        self.sample_data = self._load_test_data()

    def test_field_mapping(self):
        """Test field mapping functionality."""
        result = self.controller.universal_mapper.map_all_fields(
            headers=['Name', 'Age', 'Gender'],
            sample_data=self.sample_data
        )

        self.assertTrue(result['success'])
        self.assertIn('name', result['mapping'].values())

    def test_pattern_detection(self):
        """Test pattern detection."""
        pattern = self.controller.pattern_identifier.identify_pattern(
            self.sample_data
        )

        self.assertIn('pattern_type', pattern)
        self.assertGreater(pattern['confidence'], 0.5)
```

#### 14.4.2 Integration Testing

**End-to-End Test**:
```python
def test_complete_processing_pipeline(self):
    """Test complete census processing pipeline."""

    # Load test CSV
    test_file = 'test_data/sample_census.csv'

    # Process through complete pipeline
    result = self.controller.process_census_data(
        file_data=open(test_file, 'rb').read(),
        filename='sample_census.csv'
    )

    # Validate results
    self.assertTrue(result['success'])
    self.assertGreater(result['data']['processed_employees'], 0)
    self.assertIn('comprehensive_statistics', result['data'])
```

#### 14.4.3 Performance Testing

**Load Testing**:
```python
def test_large_file_processing(self):
    """Test processing of large census files."""

    # Generate large test dataset
    large_df = self._generate_test_data(rows=10000)

    start_time = time.time()
    result = self.controller.process_census_data(
        file_data=large_df.to_csv().encode(),
        filename='large_test.csv'
    )
    processing_time = time.time() - start_time

    # Performance assertions
    self.assertLess(processing_time, 300)  # Max 5 minutes
    self.assertTrue(result['success'])
```

---

## 15. Appendices

### 15.1 Function Reference

#### 15.1.1 CensusController Methods

| Method | Parameters | Returns | Description |
|--------|------------|---------|-------------|
| `process_census_data()` | file_data, filename, options | ProcessingResult | Main processing entry point |
| `_validate_input_data()` | file_data, filename | ValidationResult | Input validation |
| `_parse_csv_data()` | file_data | DataFrame | CSV parsing |
| `_handle_processing_errors()` | exception | ErrorResponse | Error handling |

#### 15.1.2 UniversalFieldMapper Methods

| Method | Parameters | Returns | Description |
|--------|------------|---------|-------------|
| `map_all_fields()` | headers, sample_data | MappingResult | Complete field mapping |
| `_create_universal_mapping_prompt()` | headers, sample_data | String | LLM prompt generation |
| `_validate_universal_mapping()` | mapping, headers | ValidationResult | Mapping validation |
| `_apply_fallback_mapping()` | unmapped_fields | MappingResult | Fallback mapping |

#### 15.1.3 DataPreprocessor Methods

| Method | Parameters | Returns | Description |
|--------|------------|---------|-------------|
| `preprocess_data()` | df, pattern_type, field_mapping | DataFrame | Main preprocessing |
| `_convert_row_to_column_based()` | df | DataFrame | Structure conversion |
| `_standardize_all_values()` | df | DataFrame | Value standardization |
| `_group_by_employee()` | df | GroupedData | Employee grouping |

### 15.2 Configuration Options

#### 15.2.1 Processing Options

```json
{
  "validation_level": "strict|normal|lenient",
  "enable_enrichment": true,
  "enable_prediction": true,
  "output_format": "detailed|summary",
  "include_statistics": true,
  "debug_mode": false,
  "processing_mode": "sync|async",
  "chunk_size": 1000,
  "max_dependents": 20,
  "timeout_seconds": 300
}
```

#### 15.2.2 Model Configuration

```json
{
  "models": {
    "plan_prediction": {
      "enabled": true,
      "model_path": "models/plan_model.pkl",
      "confidence_threshold": 0.5,
      "fallback_enabled": true
    },
    "benefits_prediction": {
      "enabled": true,
      "model_path": "models/benefits_model.pkl",
      "top_k_predictions": 3,
      "fallback_benefits": ["Dental", "Vision"]
    }
  }
}
```

### 15.3 Sample Data Files

#### 15.3.1 Column-Based Format

```csv
Employee_ID,First_Name,Last_Name,Gender,DOB,Address1,City,State,ZIP,Marital_Status,Salary,Medical_Plan,Dental_Plan,Vision_Plan,dept_1,dept_1_dob,dept_1_gender,relationship_type_1,dept_2,dept_2_dob,dept_2_gender,relationship_type_2,dept_count
E001,John,Smith,Male,1980-01-15,123 Main St,Anytown,CA,90210,Married,65000,Y,Y,Y,Jane Smith,1982-05-20,Female,Spouse,Mike Smith,2010-09-12,Male,Child,2
E002,Alice,Brown,Female,1975-03-10,456 Oak Ave,Somewhere,TX,75001,Not Married,52000,Y,N,Y,Bob Brown,2005-07-25,Male,Child,,,,,1
```

#### 15.3.2 Row-Based Format

```csv
Employee_ID,Name,Gender,DOB,Address1,City,State,ZIP,Marital_Status,Salary,Medical_Plan,Dental_Plan,Vision_Plan,Relationship
E001,John Smith,Male,1980-01-15,123 Main St,Anytown,CA,90210,Married,65000,Y,Y,Y,Employee
E001,Jane Smith,Female,1982-05-20,123 Main St,Anytown,CA,90210,Married,,,,Spouse
E001,Mike Smith,Male,2010-09-12,123 Main St,Anytown,CA,90210,,,,,Child
E002,Alice Brown,Female,1975-03-10,456 Oak Ave,Somewhere,TX,75001,Not Married,52000,Y,N,Y,Employee
E002,Bob Brown,Male,2005-07-25,456 Oak Ave,Somewhere,TX,75001,,,,,Child
```

---

**Document Version**: 1.0
**Last Updated**: January 15, 2024
**Maintained By**: Census Engine Development Team
**Contact**: <EMAIL>
