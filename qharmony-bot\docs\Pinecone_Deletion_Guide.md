# Pinecone Deletion Guide

## 🗑️ **Complete Guide to Deleting Pinecone Data**

This guide covers all available options for deleting data from Pinecone, from individual companies to entire indices.

## 📋 **Deletion Hierarchy**

```
Pinecone Account
├── Index 1 (e.g., "qharmony")
│   ├── Namespace 1 (e.g., "employer-company1")
│   │   ├── Document 1 vectors
│   │   ├── Document 2 vectors
│   │   └── Document N vectors
│   ├── Namespace 2 (e.g., "employer-company2")
│   └── Namespace N
├── Index 2
└── Index N
```

## 🔧 **Available Deletion Options**

### **1. Delete Specific Company (Namespace Level)**
Deletes all documents for one company within an index.

```bash
python scripts/update_pinecone_from_groups.py --mode delete --company-id 67bed0f250bad0a4b3d7fe9b
```

**What it does:**
- Finds ALL groups with the specified `company_id` in MongoDB
- Collects ALL `document_ids` from ALL groups for that company
- Deletes all vectors from namespace `employer-{company_id}`
- Keeps other companies' data intact

**Example:**
- Deletes: `employer-67bed0f250bad0a4b3d7fe9b` namespace
- Keeps: All other namespaces in the index

### **2. Bulk Delete ALL Companies (All Namespaces)**
Deletes all documents for ALL companies, but keeps the index structure.

```bash
python scripts/update_pinecone_from_groups.py --mode bulk-delete --confirm-bulk-delete
```

**What it does:**
- Processes ALL companies found in MongoDB groups
- Deletes ALL namespaces in the index
- Keeps the index itself (empty but existing)
- Requires typing 'DELETE ALL' for confirmation

**Example:**
- Deletes: All `employer-*` namespaces
- Keeps: The index structure (empty index)

### **3. List All Indices**
Shows all available Pinecone indices.

```bash
python scripts/update_pinecone_from_groups.py --mode list-indices
```

**Output example:**
```
📋 Available Pinecone Indices:
==================================================
1. qharmony
   Dimension: 1536
   Metric: cosine
   Status: Ready

2. test-index
   Dimension: 1536
   Metric: cosine
   Status: Ready
```

### **4. Delete Specific Index**
Deletes an entire index and ALL its data.

```bash
python scripts/update_pinecone_from_groups.py --mode delete-index --index-name qharmony --confirm-index-delete
```

**What it does:**
- Deletes the ENTIRE index
- Removes ALL namespaces in that index
- Removes ALL documents and vectors
- Index must be recreated to use again
- Requires typing the index name for confirmation

**⚠️ WARNING**: This is irreversible and deletes everything in the index!

### **5. Delete ALL Indices**
Deletes ALL indices in your Pinecone account.

```bash
python scripts/update_pinecone_from_groups.py --mode delete-all-indices --confirm-delete-all-indices
```

**What it does:**
- Lists all indices first
- Deletes EVERY index in your Pinecone account
- Removes ALL data permanently
- Requires typing 'DELETE ALL INDICES' for confirmation

**⚠️ EXTREME WARNING**: This deletes EVERYTHING in your Pinecone account!

## 🚨 **Safety Confirmations**

### **Confirmation Requirements**
- **Company deletion**: No additional confirmation (relatively safe)
- **Bulk company deletion**: Type 'DELETE ALL'
- **Index deletion**: Type the exact index name
- **All indices deletion**: Type 'DELETE ALL INDICES'

### **Safety Levels**
1. 🟢 **Safe**: Delete specific company (reversible by re-uploading)
2. 🟡 **Caution**: Bulk delete companies (can be restored from MongoDB)
3. 🟠 **Dangerous**: Delete specific index (requires index recreation)
4. 🔴 **EXTREME**: Delete all indices (complete data loss)

## 📊 **Use Cases**

### **When to Use Each Option**

#### **Delete Specific Company**
- Remove a test company's data
- Clean up after a company leaves
- Fix corrupted data for one company

#### **Bulk Delete All Companies**
- Reset all company data while keeping index structure
- Clean slate for testing
- Prepare for full data reload

#### **Delete Specific Index**
- Remove a test index
- Clean up unused indices
- Start fresh with new index configuration

#### **Delete All Indices**
- Complete environment reset
- Switch to new Pinecone project
- Emergency cleanup

## 🔄 **Recovery Options**

### **After Company Deletion**
```bash
# Re-upload specific company
python scripts/update_pinecone_from_groups.py --mode update --company-id 67bed0f250bad0a4b3d7fe9b
```

### **After Bulk Company Deletion**
```bash
# Re-upload all companies
python scripts/update_pinecone_from_groups.py --mode update
```

### **After Index Deletion**
1. Index will be automatically recreated on next upload
2. All data must be re-uploaded from MongoDB

### **After All Indices Deletion**
1. All indices will be recreated on next upload
2. Complete data reload required

## 📝 **Examples**

### **Complete Cleanup Workflow**
```bash
# 1. List what exists
python scripts/update_pinecone_from_groups.py --mode list-indices

# 2. Delete all data but keep index
python scripts/update_pinecone_from_groups.py --mode bulk-delete --confirm-bulk-delete

# 3. Or delete everything completely
python scripts/update_pinecone_from_groups.py --mode delete-all-indices --confirm-delete-all-indices

# 4. Rebuild from scratch
python scripts/update_pinecone_from_groups.py --mode update
```

### **Selective Cleanup**
```bash
# Remove specific problematic company
python scripts/update_pinecone_from_groups.py --mode delete --company-id 67bed0f250bad0a4b3d7fe9b

# Re-upload just that company
python scripts/update_pinecone_from_groups.py --mode update --company-id 67bed0f250bad0a4b3d7fe9b
```

## ⚡ **Quick Reference**

| Goal | Command | Safety Level | Reversible |
|------|---------|--------------|------------|
| Remove one company | `--mode delete --company-id X` | 🟢 Safe | ✅ Yes |
| Remove all companies | `--mode bulk-delete --confirm-bulk-delete` | 🟡 Caution | ✅ Yes |
| List indices | `--mode list-indices` | 🟢 Safe | N/A |
| Remove one index | `--mode delete-index --index-name X --confirm-index-delete` | 🟠 Dangerous | ⚠️ Partial |
| Remove all indices | `--mode delete-all-indices --confirm-delete-all-indices` | 🔴 EXTREME | ❌ No |

## 🎯 **Best Practices**

1. **Always list first**: Use `--mode list-indices` to see what exists
2. **Start small**: Delete specific companies before bulk operations
3. **Backup strategy**: Ensure MongoDB data is intact before major deletions
4. **Test environment**: Practice on test data first
5. **Double-check**: Verify company IDs and index names before deletion

The enhanced script now provides complete control over Pinecone data deletion at every level! 🚀
