# CMS API Enhancement Plan

## 🔍 **Current Issues Identified**

### 1. **Plan Type Filtering Fixed ✅**
- **Issue**: ML predicts "PPO" but CMS API returns only "HMO" plans
- **Root Cause**: The filtering logic was looking for `plan.get('plan_type')` but API returns `plan.get('type')`
- **Status**: **FIXED** - Updated to use correct field name
- **Impact**: Filtering now works correctly, but limited plan diversity remains

### 2. **API Response Structure Fixed ✅**
- **Issue**: Code expected `plan_type` field but API returns `type` field
- **Status**: **FIXED** - Enhanced filtering with fuzzy matching implemented
- **Impact**: Filtering logic now works properly

### 3. **Plan Type Mapping Enhanced ✅**
- **Issue**: ML predictions use compound types like "HDHP + HSA" but API uses separate fields
- **Status**: **ENHANCED** - Added mapping table and HSA eligibility checking
- **Examples**:
  - ML: "HDHP + HSA" → API: `type: "HDHP"` + `hsa_eligible: true` ✅
  - ML: "PPO" → API: `type: "PPO"` (when available) ✅
- **Impact**: Better matching for complex prediction types

### 4. **Limited Plan Availability (Market Reality)**
- **Issue**: CMS API returns limited plan diversity
- **Evidence**:
  - Dallas, TX: Only HMO Bronze plans
  - Miami, FL: HMO + EPO Bronze plans
  - Columbus, OH: Only HMO Bronze plans
- **Root Cause**: **Market reality** - CMS API reflects actual marketplace limitations
- **Impact**: Even perfect filtering can't create plans that don't exist

### 5. **Metal Level Limitations (API Constraint)**
- **Issue**: API only returns Bronze-level plans for test scenarios
- **Evidence**: All tested locations show only Bronze plans
- **Root Cause**: API parameters or market conditions limiting metal level diversity
- **Impact**: Income-based metal preferences can't be applied if only Bronze available

### 6. **Enhanced Ranking Implemented ✅**
- **Status**: **ENHANCED** - Improved ranking algorithm with income-based preferences
- **Features**: Plan type matching, metal level preferences, family considerations
- **Impact**: Better recommendations when plan diversity exists

## 🚀 **Enhancement Strategy**

### Phase 1: Fix Critical Filtering Issues (Immediate)

#### 1.1 Fix Field Name Mismatch
```python
# CURRENT (BROKEN)
if plan.get('plan_type', '').upper() == predicted_plan_type.upper()

# FIXED
if plan.get('type', '').upper() == predicted_plan_type.upper()
```

#### 1.2 Implement Plan Type Mapping
```python
PLAN_TYPE_MAPPING = {
    "HDHP + HSA": ["HDHP", "HSA"],  # Match either HDHP type OR HSA eligible
    "PPO": ["PPO", "PPO-HMO"],      # Include PPO variants
    "HMO": ["HMO", "HMO-POS"],      # Include HMO variants
    "POS": ["POS", "HMO-POS"],      # Include POS variants
    "EPO": ["EPO"]                   # Direct match
}
```

#### 1.3 Enhanced Filtering Logic
```python
def enhanced_plan_filtering(plans, predicted_type):
    # Direct type matching
    direct_matches = [p for p in plans if p.get('type') == predicted_type]
    if direct_matches:
        return direct_matches
    
    # Fuzzy matching with mapping
    mapped_types = PLAN_TYPE_MAPPING.get(predicted_type, [predicted_type])
    fuzzy_matches = [p for p in plans if p.get('type') in mapped_types]
    if fuzzy_matches:
        return fuzzy_matches
    
    # HSA special case
    if "HSA" in predicted_type:
        hsa_matches = [p for p in plans if p.get('hsa_eligible', False)]
        if hsa_matches:
            return hsa_matches
    
    # Return all if no matches (fallback)
    return plans
```

### Phase 2: Improve Plan Ranking (Short-term)

#### 2.1 Income-Based Metal Level Preferences
```python
def get_preferred_metal_levels(income_tier):
    preferences = {
        "High (>$100K)": ["Platinum", "Gold", "Silver", "Bronze"],
        "Medium ($50K–$100K)": ["Gold", "Silver", "Bronze", "Platinum"],
        "Low (<$50K)": ["Bronze", "Silver", "Gold", "Platinum"]
    }
    return preferences.get(income_tier, ["Silver", "Bronze", "Gold", "Platinum"])
```

#### 2.2 Enhanced Ranking Algorithm
```python
def calculate_plan_score(plan, employee_profile):
    score = 0
    
    # Base premium score (lower is better)
    premium = plan.get('premium', 999999)
    score += max(0, 1000 - premium/10)  # Normalize premium
    
    # Metal level preference (income-based)
    preferred_metals = get_preferred_metal_levels(employee_profile.get('income_tier'))
    metal_level = plan.get('metal_level', '')
    if metal_level in preferred_metals:
        metal_index = preferred_metals.index(metal_level)
        score += (4 - metal_index) * 100  # Higher score for preferred metals
    
    # Plan type match bonus
    if plan.get('type') == employee_profile.get('predicted_plan_type'):
        score += 200
    
    # HSA eligibility for high earners
    if employee_profile.get('income_tier') == "High (>$100K)" and plan.get('hsa_eligible'):
        score += 150
    
    # Quality rating
    quality = plan.get('quality_rating', {}).get('global_rating', 0)
    score += quality * 20
    
    # Family-friendly features
    if employee_profile.get('dept_count', 0) > 0:
        deductible = plan.get('deductible', 999999)
        if deductible < 5000:  # Lower deductible for families
            score += 100
    
    return score
```

### Phase 3: API Parameter Optimization (Medium-term)

#### 3.1 Enhanced Household Building
```python
def build_enhanced_household(employee_row):
    # Include all dependents with proper ages
    # Add pregnancy status for applicable members
    # Include tobacco use for all household members
    # Set appropriate utilization levels based on age/health
```

#### 3.2 Market and Year Optimization
```python
# Test different market types
markets = ["Individual", "Small Group"]

# Use current year + 1 for open enrollment
current_year = datetime.now().year
plan_year = current_year + 1 if datetime.now().month >= 10 else current_year
```

### Phase 4: State Coverage Expansion (Long-term)

#### 4.1 State-Based Marketplace Integration
- Research state-specific APIs for CA, NY, WA, etc.
- Implement fallback data sources
- Create unified response format

#### 4.2 Fallback Plan Database
- Maintain database of common plans by state
- Use when API is unavailable
- Regular updates from public sources

## 🛠️ **Implementation Priority**

### **Immediate (This Week)**
1. Fix field name mismatch (`plan_type` → `type`)
2. Implement basic plan type mapping
3. Add income-based metal level preferences
4. Test with actual data

### **Short-term (Next 2 Weeks)**
1. Enhanced ranking algorithm
2. Comprehensive plan type mapping
3. HSA eligibility handling
4. Family-specific optimizations

### **Medium-term (Next Month)**
1. API parameter optimization
2. Household building improvements
3. Market type testing
4. Performance optimizations

### **Long-term (Next Quarter)**
1. State-based marketplace APIs
2. Fallback plan database
3. Advanced personalization
4. Cost analysis features

## 📊 **Success Metrics**

### **Target Improvements**
- **Plan Type Match Rate**: 0% → 80%+
- **Income-Appropriate Recommendations**: Current poor → 90%+
- **API Success Rate**: Current 95% → 98%+
- **User Satisfaction**: Measure through feedback

### **Testing Strategy**
1. Unit tests for each enhancement
2. Integration tests with real employee data
3. A/B testing for ranking improvements
4. Performance benchmarking

## 🔧 **Technical Implementation Notes**

### **Code Changes Required**
1. `cmsApiService.py`: Fix filtering logic
2. `healthPlanService.py`: Enhanced ranking
3. New mapping tables and constants
4. Comprehensive test suite

### **Configuration Updates**
1. Plan type mapping configuration
2. Metal level preferences by income
3. State-specific API endpoints
4. Fallback data sources

This enhancement plan addresses the root causes of the prediction-recommendation mismatch and provides a roadmap for significant improvements to the CMS API integration.
