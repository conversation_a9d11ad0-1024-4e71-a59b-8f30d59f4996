{"file_info": {"original_filename": "address_lastname_grouping_test.csv", "processing_timestamp": "2025-07-15 15:42:56.736752", "total_processing_time_seconds": 15.137294292449951}, "final_response": {"success": true, "data": {"enriched_data_csv": "employee_id,first_name,last_name,gender,dob,address1,city,state,zipcode,marital_status,relationship,salary,medical_plan,dental_plan,vision_plan,dept_i,dept_i_gender,dept_i_dob,relationship_type_i,dept_i_medical_plan,dept_i_dental_plan,dept_i_vision_plan,dept_1,dept_1_dob,dept_1_gender,relationship_type_1,dept_1_medical_plan,dept_1_dental_plan,dept_1_vision_plan,dept_2,dept_2_dob,dept_2_gender,relationship_type_2,dept_2_medical_plan,dept_2_dental_plan,dept_2_vision_plan,dept_count,name,age,dept_1_age,dept_2_age,middle_name,address2,record_type,employment_type,employee_class,department,hire_date,tobacco_use,pregnancy_status,life_plan,add_plan,coverage_tier,ssn,dept_3,dept_3_dob,dept_3_age,dept_3_gender,relationship_type_3,dept_4,dept_4_dob,dept_4_age,dept_4_gender,relationship_type_4,dept_5,dept_5_dob,dept_5_age,dept_5_gender,relationship_type_5,dept_6,dept_6_dob,dept_6_age,dept_6_gender,relationship_type_6,dept_7,dept_7_dob,dept_7_age,dept_7_gender,relationship_type_7,dept_8,dept_8_dob,dept_8_age,dept_8_gender,relationship_type_8,dept_9,dept_9_dob,dept_9_age,dept_9_gender,relationship_type_9,dept_10,dept_10_dob,dept_10_age,dept_10_gender,relationship_type_10,dept_11,dept_11_dob,dept_11_age,dept_11_gender,relationship_type_11,dept_12,dept_12_dob,dept_12_age,dept_12_gender,relationship_type_12,dept_13,dept_13_dob,dept_13_age,dept_13_gender,relationship_type_13,dept_14,dept_14_dob,dept_14_age,dept_14_gender,relationship_type_14,dept_15,dept_15_dob,dept_15_age,dept_15_gender,relationship_type_15,dept_16,dept_16_dob,dept_16_age,dept_16_gender,relationship_type_16,dept_17,dept_17_dob,dept_17_age,dept_17_gender,relationship_type_17,dept_18,dept_18_dob,dept_18_age,dept_18_gender,relationship_type_18,dept_19,dept_19_dob,dept_19_age,dept_19_gender,relationship_type_19,dept_20,dept_20_dob,dept_20_age,dept_20_gender,relationship_type_20,job_type,region,health_condition,chronic_condition,prescription_use,income_tier,risk_tolerance,lifestyle,travel_frequency,hsa_familiarity,mental_health_needs,predicted_plan_type,plan_confidence,plan_reason,top_3_plans,top_3_plan_confidences,predicted_benefits,benefits_confidence,benefits_reason,top_3_benefits,top_3_benefits_confidences\r\nE001,John,Smith,Male,1980-01-15,123 Main St,Anytown,CA,90210,Married,Employee,65000,Y,Y,Y,Smith,Male,2010-09-12,Child,PPO,Y,Y,Jane Smith,1982-05-20,Female,Spouse,PPO,Y,Y,Mike Smith,2010-09-12,Male,Child,PPO,Y,Y,2,John Smith,45,43.0,14.0,,,,Full-Time,,Sales,,N,N,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Desk,Urban,Good,N,None,Medium ($50K–$100K),Low,Moderate,Frequent,N,N,POS,0.9083523,\"Medium income, good health - PPO for balanced cost and flexibility.\",\"['POS', 'HMO', 'PPO']\",\"[0.9083523, 0.*********, 0.02987497]\",\"['Dental', 'Vision']\",1.0,Dental: Essential oral health coverage for all employees.; Vision: Eye care coverage for all employees.,\"['Employee Assistance', 'Wellness Programs', 'FSA']\",\"[0.5, 0.5, 0.5]\"\r\nE002,Charlie,Johnson,Male,1978-02-28,456 Oak Ave,Somewhere,TX,75001,Married,Employee,78500,Y,Y,Y,Johnson,Female,1980-08-15,Spouse,HDHP,Y,Y,Bob Johnson,2005-07-25,Male,Child,HMO,N,Y,Diana Johnson,1980-08-15,Female,Spouse,HDHP,Y,Y,2,Charlie Johnson,47,19.0,44.0,,,,Full-Time,,Sales,,N,N,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Desk,Urban,Fair,N,None,High (>$100K),Medium,Moderate,Frequent,Y,Y,HDHP + HSA,0.6828363,Middle age with dependents - PPO for family coverage.,\"['HDHP + HSA', 'PPO', 'POS']\",\"[0.6828363, 0.1688195, 0.********]\",\"['Dental', 'Vision', 'Term Life', 'LTD', 'FSA', 'Employee Assistance']\",1.0,Dental: Essential oral health coverage for all employees.; Vision: Eye care coverage for all employees.; Term Life: Essential protection for 2 dependents.; LTD: Long-term income protection for High (>$100K) earners or age 47+.; FSA: Tax-advantaged account for medical expenses due to health conditions or pregnancy.,\"['Employee Assistance', 'Wellness Programs', 'FSA']\",\"[0.5, 0.5, 0.5]\"\r\nE003,Grace,Wilson,Female,1985-11-05,456 Oak Ave,Somewhere,TX,75001,Not Married,Employee,45000,Y,Y,N,Wilson,Male,2012-04-18,Child,EPO,Y,N,Henry Wilson,2012-04-18,Male,Child,EPO,Y,N,,,,,,,,1,Grace Wilson,39,13.0,,,,,Full-Time,,Manufacturing,,Y,N,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Desk,Urban,Fair,N,None,Medium ($50K–$100K),Medium,Active,Occasional,Y,N,HDHP + HSA,0.********,\"Medium income, urban area - EPO for network-based care.\",\"['HDHP + HSA', 'POS', 'HDHP']\",\"[0.********, 0.*********, 0.********]\",\"['Dental', 'Vision', 'Accident', 'STD']\",1.0,Dental: Essential oral health coverage for all employees.; Vision: Eye care coverage for all employees.; Accident: Recommended for active lifestyle and occasional travel frequency.; STD: Income protection for Medium ($50K–$100K) earners or fair health status.,\"['Employee Assistance', 'Wellness Programs', 'FSA']\",\"[0.5, 0.5, 0.5]\"\r\nE004,Isaac,Brown,Male,1990-06-30,789 Pine Rd,Elsewhere,FL,33101,Married,Employee,95000,Y,Y,Y,Brown,Male,2015-03-22,Child,PPO,Y,Y,Lisa Brown,1992-12-01,Female,Spouse,PPO,Y,Y,Tom Brown,2015-03-22,Male,Child,PPO,Y,Y,2,Isaac Brown,35,32.0,10.0,,,,Full-Time,,Information Technology,,N,N,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Desk,Urban,Fair,N,None,High (>$100K),Medium,Active,Rare,Y,N,HDHP + HSA,0.6931619,Middle age with dependents - PPO for family coverage.,\"['HDHP + HSA', 'PPO', 'HDHP']\",\"[0.6931619, 0.********, 0.********]\",\"['Dental', 'Vision', 'Term Life', 'LTD', 'FSA']\",1.0,Dental: Essential oral health coverage for all employees.; Vision: Eye care coverage for all employees.; Term Life: Essential protection for 2 dependents.; LTD: Long-term income protection for High (>$100K) earners or age 35+.; FSA: Tax-advantaged account for medical expenses due to health conditions or pregnancy.,\"['Employee Assistance', 'Wellness Programs', 'FSA']\",\"[0.5, 0.5, 0.5]\"\r\nE005,Nancy,Davis,Female,1987-07-12,789 Pine Rd,Elsewhere,FL,33101,Married,Employee,72000,Y,Y,Y,Davis,Female,2013-08-10,Child,HDHP,Y,Y,Paul Davis,1985-04-20,Male,Spouse,HDHP,Y,Y,Emma Davis,2013-08-10,Female,Child,HDHP,Y,Y,2,Nancy Davis,38,40.0,11.0,,,,Full-Time,,Finance,,N,N,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Desk,Urban,Fair,N,None,Medium ($50K–$100K),Medium,Active,Rare,Y,Y,HDHP + HSA,0.********,\"Medium income, urban area - EPO for network-based care.\",\"['HDHP + HSA', 'HDHP', 'PPO']\",\"[0.********, 0.********, 0.********]\",\"['Dental', 'Vision', 'Employee Assistance']\",1.0,Dental: Essential oral health coverage for all employees.; Vision: Eye care coverage for all employees.,\"['Employee Assistance', 'Wellness Programs', 'FSA']\",\"[0.5, 0.5, 0.5]\"\r\nE006,Quinn,Martinez,Male,1979-10-17,321 Elm St,Nowhere,NY,10001,Not Married,Employee,58000,Y,Y,Y,Martinez,Female,2010-03-14,Child,PPO,Y,Y,Alex Martinez,2007-12-25,Male,Child,PPO,Y,Y,Zoe Martinez,2010-03-14,Female,Child,PPO,Y,Y,2,Quinn Martinez,45,17.0,15.0,,,,Full-Time,,Manufacturing,,N,N,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Desk,Urban,Excellent,N,None,Medium ($50K–$100K),Medium,Moderate,Rare,N,N,POS,0.7206716,\"Medium income, urban area - EPO for network-based care.\",\"['POS', 'HMO', 'PPO']\",\"[0.7206716, 0.1216515, 0.090712465]\",\"['Dental', 'Vision', 'Accident', 'STD']\",1.0,Dental: Essential oral health coverage for all employees.; Vision: Eye care coverage for all employees.; STD: Income protection for Medium ($50K–$100K) earners or excellent health status.,\"['Employee Assistance', 'Wellness Programs', 'FSA']\",\"[0.5, 0.5, 0.5]\"\r\nE007,Tina,Garcia,Female,1981-03-24,321 Elm St,Nowhere,NY,10001,Married,Employee,67000,Y,Y,Y,Garcia,Female,2009-11-30,Child,HMO,Y,Y,Carlos Garcia,1979-07-08,Male,Spouse,HMO,Y,Y,Maya Garcia,2009-11-30,Female,Child,HMO,Y,Y,2,Tina Garcia,44,46.0,15.0,,,,Part-Time,,Finance,,N,N,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Desk,Urban,Good,N,None,Medium ($50K–$100K),Low,Moderate,Rare,Y,N,HDHP + HSA,0.79131466,\"Medium income, good health - PPO for balanced cost and flexibility.\",\"['HDHP + HSA', 'POS', 'HMO']\",\"[0.79131466, 0.18061617, 0.010884565]\",\"['Dental', 'Vision']\",1.0,Dental: Essential oral health coverage for all employees.; Vision: Eye care coverage for all employees.,\"['Employee Assistance', 'Wellness Programs', 'FSA']\",\"[0.5, 0.5, 0.5]\"\r\nE008,Xavier,Rodriguez,Male,1986-09-09,987 Cedar Ln,Someplace,OR,97001,Not Married,Employee,41000,Y,N,N,,,,,,,,,,,,,,,,,,,,,,0,Xavier Rodriguez,38,,,,,,Full-Time,,Finance,,N,N,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Desk,Urban,Excellent,N,None,Medium ($50K–$100K),Medium,Moderate,Rare,N,N,POS,0.31479773,\"Medium income, urban area - EPO for network-based care.\",\"['POS', 'HDHP', 'HMO']\",\"[0.31479773, 0.30657175, 0.17489885]\",[],0.0,ML model prediction,\"['Employee Assistance', 'Wellness Programs', 'FSA']\",\"[0.5, 0.5, 0.5]\"\r\nE009,Yvonne,Lee,Female,1984-12-18,987 Cedar Ln,Someplace,OR,97001,Married,Employee,63000,Y,Y,Y,Lee,Male,1982-02-14,Spouse,PPO,Y,Y,David Lee,1982-02-14,Male,Spouse,PPO,Y,Y,,,,,,,,1,Yvonne Lee,40,43.0,,,,,Full-Time,,Finance,,N,N,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Desk,Suburban,Good,N,None,Medium ($50K–$100K),Medium,Active,Occasional,Y,N,HDHP + HSA,0.77176094,\"Medium income, good health - PPO for balanced cost and flexibility.\",\"['HDHP + HSA', 'POS', 'PPO']\",\"[0.77176094, 0.20286268, 0.011132505]\",\"['Dental', 'Vision']\",1.0,Dental: Essential oral health coverage for all employees.; Vision: Eye care coverage for all employees.,\"['Employee Assistance', 'Wellness Programs', 'FSA']\",\"[0.5, 0.5, 0.5]\"\r\nE010,Aaron,White,Male,1977-04-07,,Yourtown,ID,83001,Not Married,Employee,49000,Y,Y,Y,,,,,,,,,,,,,,,,,,,,,,0,Aaron White,48,,,,,,Part-Time,,Engineering,,N,N,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Desk,Rural,Fair,N,None,Medium ($50K–$100K),Low,Active,Rare,N,N,PPO,0.76978266,Middle age with dependents - PPO for family coverage.,\"['PPO', 'POS', 'HMO']\",\"[0.76978266, 0.*********, 0.*********]\",\"['Accident', 'STD']\",1.0,Accident: Recommended for active lifestyle and rare travel frequency.; STD: Income protection for Medium ($50K–$100K) earners or fair health status.,\"['Employee Assistance', 'Wellness Programs', 'FSA']\",\"[0.5, 0.5, 0.5]\"\r\nE011,Kelly,White,Female,2006-05-22,,Yourtown,ID,83001,Not Married,Child,0,Y,Y,Y,,,,,,,,,,,,,,,,,,,,,,0,Kelly White,19,,,,,,Full-Time,,Manufacturing,,N,N,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Desk,Rural,Fair,N,None,Medium ($50K–$100K),Medium,Moderate,Occasional,Y,N,HDHP + HSA,0.8191391,Young age - PPO for flexibility and future needs.,\"['HDHP + HSA', 'PPO', 'HDHP']\",\"[0.8191391, 0.09687783, 0.06701801]\",\"['Accident', 'STD']\",1.0,STD: Income protection for Medium ($50K–$100K) earners or fair health status.,\"['Employee Assistance', 'Wellness Programs', 'FSA']\",\"[0.5, 0.5, 0.5]\"\r\nE012,Ryan,White,Male,2009-10-11,,Yourtown,ID,83001,Not Married,Child,0,Y,Y,Y,,,,,,,,,,,,,,,,,,,,,,0,Ryan White,15,,,,,,Full-Time,,Finance,,Y,N,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Desk,Rural,Good,N,None,Medium ($50K–$100K),Low,Moderate,Rare,Y,N,HDHP + HSA,0.7566702,\"Medium income, good health - PPO for balanced cost and flexibility.\",\"['HDHP + HSA', 'HDHP', 'HMO']\",\"[0.7566702, 0.22109824, 0.00977206]\",\"['Dental', 'Vision']\",0.5,Default benefits assignment due to prediction error,\"['Dental', 'Vision', 'Hospital Indemnity']\",\"[0.5, 0.5, 0.5]\"\r\nE013,Brooke,Harris,Female,1989-08-21,,Mytown,WY,82001,Not Married,Employee,35000,Y,N,N,,,,,,,,,,,,,,,,,,,,,,0,Brooke Harris,35,,,,,,Full-Time,,Information Technology,,N,N,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Desk,Rural,Good,N,None,Low (<$50K),Low,Active,Rare,N,N,HMO,0.83525723,Middle age with dependents - PPO for family coverage.,\"['HMO', 'PPO', 'POS']\",\"[0.83525723, 0.08598737, 0.049331944]\",\"['Dental', 'Vision']\",0.5,Default benefits assignment due to prediction error,\"['Dental', 'Vision', 'Hospital Indemnity']\",\"[0.5, 0.5, 0.5]\"\r\nE014,Connor,Clark,Male,1982-01-26,,Ourtown,ND,58001,Married,Employee,85000,Y,Y,Y,,,,,,,,,,,,,,,,,,,,,,0,Connor Clark,43,,,,,,Full-Time,,Finance,,Y,N,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Desk,Rural,Excellent,N,Occasional,High (>$100K),Medium,Moderate,Rare,N,N,PPO,0.36536977,Middle age with dependents - PPO for family coverage.,\"['PPO', 'POS', 'Indemnity']\",\"[0.36536977, 0.24221893, 0.20186205]\",\"['Dental', 'Vision']\",0.5,Default benefits assignment due to prediction error,\"['Dental', 'Vision', 'Hospital Indemnity']\",\"[0.5, 0.5, 0.5]\"\r\nE015,Rachel,Clark,Female,1984-06-18,,Ourtown,ND,58001,Married,Spouse,0,Y,Y,Y,,,,,,,,,,,,,,,,,,,,,,0,Rachel Clark,41,,,,,,Contract,,Manufacturing,,N,N,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Desk,Rural,Good,N,None,Medium ($50K–$100K),Medium,Active,Occasional,Y,N,HDHP + HSA,0.7499423,\"Medium income, good health - PPO for balanced cost and flexibility.\",\"['HDHP + HSA', 'POS', 'PPO']\",\"[0.7499423, 0.22563146, 0.009710918]\",\"['Dental', 'Vision']\",0.5,Default benefits assignment due to prediction error,\"['Dental', 'Vision', 'Hospital Indemnity']\",\"[0.5, 0.5, 0.5]\"\r\nE016,Jake,Clark,Male,2012-01-05,,Ourtown,ND,58001,Not Married,Child,0,Y,Y,Y,,,,,,,,,,,,,,,,,,,,,,0,Jake Clark,13,,,,,,Full-Time,,Information Technology,,N,N,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Desk,Rural,Good,N,None,Low (<$50K),Low,Moderate,Rare,N,Y,HMO,0.8719066,\"Low income, young age - DHMO for basic dental coverage.\",\"['HMO', 'PPO', 'HDHP']\",\"[0.8719066, 0.080098204, 0.028052783]\",\"['Dental', 'Vision']\",0.5,Default benefits assignment due to prediction error,\"['Dental', 'Vision', 'Hospital Indemnity']\",\"[0.5, 0.5, 0.5]\"\r\nE017,Lily,Clark,Female,2014-07-12,,Ourtown,ND,58001,Not Married,Child,0,Y,Y,Y,,,,,,,,,,,,,,,,,,,,,,0,Lily Clark,11,,,,,,Full-Time,,Engineering,,N,N,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Desk,Rural,Good,N,Occasional,Medium ($50K–$100K),Medium,Moderate,Occasional,N,N,HDHP,0.3938287,\"Medium income, good health - PPO for balanced cost and flexibility.\",\"['HDHP', 'HMO', 'PPO']\",\"[0.3938287, 0.3278765, 0.17925362]\",\"['Dental', 'Vision']\",0.5,Default benefits assignment due to prediction error,\"['Dental', 'Vision', 'Hospital Indemnity']\",\"[0.5, 0.5, 0.5]\"\r\nE018,Destiny,Lewis,Female,1986-05-13,,Theirtown,SD,57001,Not Married,Employee,44000,Y,Y,Y,,,,,,,,,,,,,,,,,,,,,,0,Destiny Lewis,39,,,,,,Full-Time,,Finance,,N,N,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Desk,Rural,Fair,N,None,Medium ($50K–$100K),Medium,Moderate,Rare,N,Y,PPO,0.7738694,Middle age with dependents - PPO for family coverage.,\"['PPO', 'POS', 'HMO']\",\"[0.7738694, 0.0969907, 0.06508199]\",\"['Dental', 'Vision']\",0.5,Default benefits assignment due to prediction error,\"['Dental', 'Vision', 'Hospital Indemnity']\",\"[0.5, 0.5, 0.5]\"\r\nE019,Noah,Lewis,Male,2011-09-28,,Theirtown,SD,57001,Not Married,Child,0,Y,Y,Y,,,,,,,,,,,,,,,,,,,,,,0,Noah Lewis,13,,,,,,Contract,,Information Technology,,N,N,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Desk,Rural,Good,N,None,Medium ($50K–$100K),Low,Moderate,Occasional,N,N,POS,0.57680446,\"Medium income, good health - PPO for balanced cost and flexibility.\",\"['POS', 'HDHP', 'HMO']\",\"[0.57680446, 0.2213614, 0.09808987]\",\"['Dental', 'Vision']\",0.5,Default benefits assignment due to prediction error,\"['Dental', 'Vision', 'Hospital Indemnity']\",\"[0.5, 0.5, 0.5]\"\r\n", "comprehensive_statistics": {"basic_demographics": {"age_statistics": {"average_age": 34.10526315789474, "median_age": 39.0, "age_range": {"min": 11, "max": 48}, "age_distribution": {"18-30": 1, "31-45": 12, "46-60": 2, "60+": 0}}, "gender_composition": {"counts": {"Male": 10, "Female": 9}, "percentages": {"Male": 52.63, "Female": 47.37}}, "marital_status_distribution": {"counts": {"Not Married": 11, "Married": 8}, "percentages": {"Not Married": 57.89, "Married": 42.11}}}, "dependent_analysis": {"dependent_count_distribution": {"average_dependents": 0.7368421052631579, "median_dependents": 0.0, "distribution": {"0": 11, "2": 6, "1": 2}, "employees_with_dependents": 8, "percentage_with_dependents": 42.11}, "dependent_age_analysis": {"total_dependents_found": 14, "average_dependent_age": 25.86, "median_dependent_age": 18.0, "dependents_over_26_as_children": 0, "age_distribution": {"0-5": 0, "6-12": 2, "13-18": 5, "19-26": 1, "26+": 6}}}, "employment_demographics": {"department_distribution": {"counts": {"Finance": 7, "Manufacturing": 4, "Information Technology": 4, "Sales": 2, "Engineering": 2}, "percentages": {"Finance": 36.84, "Manufacturing": 21.05, "Information Technology": 21.05, "Sales": 10.53, "Engineering": 10.53}}, "employment_type_distribution": {"counts": {"Full-Time": 15, "Part-Time": 2, "Contract": 2}, "percentages": {"Full-Time": 78.95, "Part-Time": 10.53, "Contract": 10.53}}, "job_type_distribution": {"counts": {"Desk": 19}, "percentages": {"Desk": 100.0}}, "employee_class_distribution": {"counts": {}, "percentages": {}}, "salary_analysis": {"average_salary": 41973.68, "median_salary": 45000.0, "salary_range": {"min": "0", "max": "95000"}, "salary_distribution": {"under_40k": 7, "40k_75k": 9, "75k_100k": 3, "over_100k": 0}}}, "health_and_lifestyle": {"health_condition_distribution": {"counts": {"Good": 9, "Fair": 7, "Excellent": 3}, "percentages": {"Good": 47.37, "Fair": 36.84, "Excellent": 15.79}}, "chronic_condition_distribution": {"counts": {"N": 19}, "percentages": {"N": 100.0}}, "tobacco_use_distribution": {"counts": {"N": 16, "Y": 3}, "percentages": {"N": 84.21, "Y": 15.79}}, "lifestyle_distribution": {"counts": {"Moderate": 12, "Active": 7}, "percentages": {"Moderate": 63.16, "Active": 36.84}}, "prescription_use_distribution": {"counts": {"None": 17, "Occasional": 2}, "percentages": {"None": 89.47, "Occasional": 10.53}}}, "coverage_analysis": {"coverage_tier_distribution": {"counts": {}, "percentages": {}}, "coverage_tier_by_family_size": {}, "medical_plan_distribution": {"counts": {"Y": 19}, "percentages": {"Y": 100.0}}, "dental_plan_distribution": {"counts": {"Y": 17, "N": 2}, "percentages": {"Y": 89.47, "N": 10.53}}, "vision_plan_distribution": {"counts": {"Y": 16, "N": 3}, "percentages": {"Y": 84.21, "N": 15.79}}, "life_plan_distribution": {"counts": {}, "percentages": {}}}, "geographic_distribution": {"state_distribution": {"counts": {"ND": 4, "ID": 3, "TX": 2, "FL": 2, "NY": 2, "OR": 2, "SD": 2, "CA": 1, "WY": 1}, "percentages": {"ND": 21.05, "ID": 15.79, "TX": 10.53, "FL": 10.53, "NY": 10.53, "OR": 10.53, "SD": 10.53, "CA": 5.26, "WY": 5.26}}, "region_distribution": {"counts": {"Rural": 10, "Urban": 8, "Suburban": 1}, "percentages": {"Rural": 52.63, "Urban": 42.11, "Suburban": 5.26}}, "top_cities": {"counts": {"Ourtown": 4, "Yourtown": 3, "Somewhere": 2, "Elsewhere": 2, "Nowhere": 2, "Someplace": 2, "Theirtown": 2, "Anytown": 1, "Mytown": 1}, "percentages": {"Ourtown": 21.05, "Yourtown": 15.79, "Somewhere": 10.53, "Elsewhere": 10.53, "Nowhere": 10.53, "Someplace": 10.53, "Theirtown": 10.53, "Anytown": 5.26, "Mytown": 5.26}}}, "financial_demographics": {"income_tier_distribution": {"counts": {"Medium ($50K–$100K)": 14, "High (>$100K)": 3, "Low (<$50K)": 2}, "percentages": {"Medium ($50K–$100K)": 73.68, "High (>$100K)": 15.79, "Low (<$50K)": 10.53}}, "risk_tolerance_distribution": {"counts": {"Medium": 12, "Low": 7}, "percentages": {"Medium": 63.16, "Low": 36.84}}, "hsa_familiarity_distribution": {"counts": {"N": 10, "Y": 9}, "percentages": {"N": 52.63, "Y": 47.37}}}, "risk_assessment": {"group_risk_score": 22.95, "group_risk_level": "Low", "risk_distribution": {"low_risk": 15, "medium_risk": 4, "high_risk": 0}, "risk_statistics": {"min_risk_score": 13, "max_risk_score": 40, "median_risk_score": 23.0, "std_risk_score": 7.62}, "top_risk_factors": {"Poor Health Condition": 7, "Tobacco Use": 3}, "individual_risks": [{"employee_id": "E001", "risk_score": 23, "risk_level": "Low", "risk_factors": []}, {"employee_id": "E002", "risk_score": 33, "risk_level": "Medium", "risk_factors": ["Health condition: Fair"]}, {"employee_id": "E003", "risk_score": 40, "risk_level": "Medium", "risk_factors": ["Health condition: Fair", "Tobacco user"]}, {"employee_id": "E004", "risk_score": 25, "risk_level": "Low", "risk_factors": ["Health condition: Fair"]}, {"employee_id": "E005", "risk_score": 25, "risk_level": "Low", "risk_factors": ["Health condition: Fair"]}, {"employee_id": "E006", "risk_score": 18, "risk_level": "Low", "risk_factors": []}, {"employee_id": "E007", "risk_score": 23, "risk_level": "Low", "risk_factors": []}, {"employee_id": "E008", "risk_score": 13, "risk_level": "Low", "risk_factors": []}, {"employee_id": "E009", "risk_score": 20, "risk_level": "Low", "risk_factors": []}, {"employee_id": "E010", "risk_score": 30, "risk_level": "Medium", "risk_factors": ["Health condition: Fair"]}]}, "data_quality_metrics": {"overall_completeness": {"total_cells": 2945, "missing_cells": "2160", "completeness_percentage": 26.66}, "column_completeness": {"employee_id": {"missing_count": "0", "completeness_percentage": 100.0}, "first_name": {"missing_count": "0", "completeness_percentage": 100.0}, "last_name": {"missing_count": "0", "completeness_percentage": 100.0}, "gender": {"missing_count": "0", "completeness_percentage": 100.0}, "dob": {"missing_count": "0", "completeness_percentage": 100.0}, "address1": {"missing_count": "10", "completeness_percentage": 47.37}, "city": {"missing_count": "0", "completeness_percentage": 100.0}, "state": {"missing_count": "0", "completeness_percentage": 100.0}, "zipcode": {"missing_count": "0", "completeness_percentage": 100.0}, "marital_status": {"missing_count": "0", "completeness_percentage": 100.0}, "relationship": {"missing_count": "0", "completeness_percentage": 100.0}, "salary": {"missing_count": "0", "completeness_percentage": 100.0}, "medical_plan": {"missing_count": "0", "completeness_percentage": 100.0}, "dental_plan": {"missing_count": "0", "completeness_percentage": 100.0}, "vision_plan": {"missing_count": "0", "completeness_percentage": 100.0}, "dept_i": {"missing_count": "11", "completeness_percentage": 42.11}, "dept_i_gender": {"missing_count": "11", "completeness_percentage": 42.11}, "dept_i_dob": {"missing_count": "11", "completeness_percentage": 42.11}, "relationship_type_i": {"missing_count": "11", "completeness_percentage": 42.11}, "dept_i_medical_plan": {"missing_count": "11", "completeness_percentage": 42.11}, "dept_i_dental_plan": {"missing_count": "11", "completeness_percentage": 42.11}, "dept_i_vision_plan": {"missing_count": "11", "completeness_percentage": 42.11}, "dept_1": {"missing_count": "11", "completeness_percentage": 42.11}, "dept_1_dob": {"missing_count": "11", "completeness_percentage": 42.11}, "dept_1_gender": {"missing_count": "11", "completeness_percentage": 42.11}, "relationship_type_1": {"missing_count": "11", "completeness_percentage": 42.11}, "dept_1_medical_plan": {"missing_count": "11", "completeness_percentage": 42.11}, "dept_1_dental_plan": {"missing_count": "11", "completeness_percentage": 42.11}, "dept_1_vision_plan": {"missing_count": "11", "completeness_percentage": 42.11}, "dept_2": {"missing_count": "13", "completeness_percentage": 31.58}, "dept_2_dob": {"missing_count": "13", "completeness_percentage": 31.58}, "dept_2_gender": {"missing_count": "13", "completeness_percentage": 31.58}, "relationship_type_2": {"missing_count": "13", "completeness_percentage": 31.58}, "dept_2_medical_plan": {"missing_count": "13", "completeness_percentage": 31.58}, "dept_2_dental_plan": {"missing_count": "13", "completeness_percentage": 31.58}, "dept_2_vision_plan": {"missing_count": "13", "completeness_percentage": 31.58}, "dept_count": {"missing_count": "0", "completeness_percentage": 100.0}, "name": {"missing_count": "0", "completeness_percentage": 100.0}, "age": {"missing_count": "0", "completeness_percentage": 100.0}, "dept_1_age": {"missing_count": "11", "completeness_percentage": 42.11}, "dept_2_age": {"missing_count": "13", "completeness_percentage": 31.58}, "middle_name": {"missing_count": "19", "completeness_percentage": 0.0}, "address2": {"missing_count": "19", "completeness_percentage": 0.0}, "record_type": {"missing_count": "19", "completeness_percentage": 0.0}, "employment_type": {"missing_count": "0", "completeness_percentage": 100.0}, "employee_class": {"missing_count": "19", "completeness_percentage": 0.0}, "department": {"missing_count": "0", "completeness_percentage": 100.0}, "hire_date": {"missing_count": "19", "completeness_percentage": 0.0}, "tobacco_use": {"missing_count": "0", "completeness_percentage": 100.0}, "pregnancy_status": {"missing_count": "0", "completeness_percentage": 100.0}, "life_plan": {"missing_count": "19", "completeness_percentage": 0.0}, "add_plan": {"missing_count": "19", "completeness_percentage": 0.0}, "coverage_tier": {"missing_count": "19", "completeness_percentage": 0.0}, "ssn": {"missing_count": "19", "completeness_percentage": 0.0}, "dept_3": {"missing_count": "19", "completeness_percentage": 0.0}, "dept_3_dob": {"missing_count": "19", "completeness_percentage": 0.0}, "dept_3_age": {"missing_count": "19", "completeness_percentage": 0.0}, "dept_3_gender": {"missing_count": "19", "completeness_percentage": 0.0}, "relationship_type_3": {"missing_count": "19", "completeness_percentage": 0.0}, "dept_4": {"missing_count": "19", "completeness_percentage": 0.0}, "dept_4_dob": {"missing_count": "19", "completeness_percentage": 0.0}, "dept_4_age": {"missing_count": "19", "completeness_percentage": 0.0}, "dept_4_gender": {"missing_count": "19", "completeness_percentage": 0.0}, "relationship_type_4": {"missing_count": "19", "completeness_percentage": 0.0}, "dept_5": {"missing_count": "19", "completeness_percentage": 0.0}, "dept_5_dob": {"missing_count": "19", "completeness_percentage": 0.0}, "dept_5_age": {"missing_count": "19", "completeness_percentage": 0.0}, "dept_5_gender": {"missing_count": "19", "completeness_percentage": 0.0}, "relationship_type_5": {"missing_count": "19", "completeness_percentage": 0.0}, "dept_6": {"missing_count": "19", "completeness_percentage": 0.0}, "dept_6_dob": {"missing_count": "19", "completeness_percentage": 0.0}, "dept_6_age": {"missing_count": "19", "completeness_percentage": 0.0}, "dept_6_gender": {"missing_count": "19", "completeness_percentage": 0.0}, "relationship_type_6": {"missing_count": "19", "completeness_percentage": 0.0}, "dept_7": {"missing_count": "19", "completeness_percentage": 0.0}, "dept_7_dob": {"missing_count": "19", "completeness_percentage": 0.0}, "dept_7_age": {"missing_count": "19", "completeness_percentage": 0.0}, "dept_7_gender": {"missing_count": "19", "completeness_percentage": 0.0}, "relationship_type_7": {"missing_count": "19", "completeness_percentage": 0.0}, "dept_8": {"missing_count": "19", "completeness_percentage": 0.0}, "dept_8_dob": {"missing_count": "19", "completeness_percentage": 0.0}, "dept_8_age": {"missing_count": "19", "completeness_percentage": 0.0}, "dept_8_gender": {"missing_count": "19", "completeness_percentage": 0.0}, "relationship_type_8": {"missing_count": "19", "completeness_percentage": 0.0}, "dept_9": {"missing_count": "19", "completeness_percentage": 0.0}, "dept_9_dob": {"missing_count": "19", "completeness_percentage": 0.0}, "dept_9_age": {"missing_count": "19", "completeness_percentage": 0.0}, "dept_9_gender": {"missing_count": "19", "completeness_percentage": 0.0}, "relationship_type_9": {"missing_count": "19", "completeness_percentage": 0.0}, "dept_10": {"missing_count": "19", "completeness_percentage": 0.0}, "dept_10_dob": {"missing_count": "19", "completeness_percentage": 0.0}, "dept_10_age": {"missing_count": "19", "completeness_percentage": 0.0}, "dept_10_gender": {"missing_count": "19", "completeness_percentage": 0.0}, "relationship_type_10": {"missing_count": "19", "completeness_percentage": 0.0}, "dept_11": {"missing_count": "19", "completeness_percentage": 0.0}, "dept_11_dob": {"missing_count": "19", "completeness_percentage": 0.0}, "dept_11_age": {"missing_count": "19", "completeness_percentage": 0.0}, "dept_11_gender": {"missing_count": "19", "completeness_percentage": 0.0}, "relationship_type_11": {"missing_count": "19", "completeness_percentage": 0.0}, "dept_12": {"missing_count": "19", "completeness_percentage": 0.0}, "dept_12_dob": {"missing_count": "19", "completeness_percentage": 0.0}, "dept_12_age": {"missing_count": "19", "completeness_percentage": 0.0}, "dept_12_gender": {"missing_count": "19", "completeness_percentage": 0.0}, "relationship_type_12": {"missing_count": "19", "completeness_percentage": 0.0}, "dept_13": {"missing_count": "19", "completeness_percentage": 0.0}, "dept_13_dob": {"missing_count": "19", "completeness_percentage": 0.0}, "dept_13_age": {"missing_count": "19", "completeness_percentage": 0.0}, "dept_13_gender": {"missing_count": "19", "completeness_percentage": 0.0}, "relationship_type_13": {"missing_count": "19", "completeness_percentage": 0.0}, "dept_14": {"missing_count": "19", "completeness_percentage": 0.0}, "dept_14_dob": {"missing_count": "19", "completeness_percentage": 0.0}, "dept_14_age": {"missing_count": "19", "completeness_percentage": 0.0}, "dept_14_gender": {"missing_count": "19", "completeness_percentage": 0.0}, "relationship_type_14": {"missing_count": "19", "completeness_percentage": 0.0}, "dept_15": {"missing_count": "19", "completeness_percentage": 0.0}, "dept_15_dob": {"missing_count": "19", "completeness_percentage": 0.0}, "dept_15_age": {"missing_count": "19", "completeness_percentage": 0.0}, "dept_15_gender": {"missing_count": "19", "completeness_percentage": 0.0}, "relationship_type_15": {"missing_count": "19", "completeness_percentage": 0.0}, "dept_16": {"missing_count": "19", "completeness_percentage": 0.0}, "dept_16_dob": {"missing_count": "19", "completeness_percentage": 0.0}, "dept_16_age": {"missing_count": "19", "completeness_percentage": 0.0}, "dept_16_gender": {"missing_count": "19", "completeness_percentage": 0.0}, "relationship_type_16": {"missing_count": "19", "completeness_percentage": 0.0}, "dept_17": {"missing_count": "19", "completeness_percentage": 0.0}, "dept_17_dob": {"missing_count": "19", "completeness_percentage": 0.0}, "dept_17_age": {"missing_count": "19", "completeness_percentage": 0.0}, "dept_17_gender": {"missing_count": "19", "completeness_percentage": 0.0}, "relationship_type_17": {"missing_count": "19", "completeness_percentage": 0.0}, "dept_18": {"missing_count": "19", "completeness_percentage": 0.0}, "dept_18_dob": {"missing_count": "19", "completeness_percentage": 0.0}, "dept_18_age": {"missing_count": "19", "completeness_percentage": 0.0}, "dept_18_gender": {"missing_count": "19", "completeness_percentage": 0.0}, "relationship_type_18": {"missing_count": "19", "completeness_percentage": 0.0}, "dept_19": {"missing_count": "19", "completeness_percentage": 0.0}, "dept_19_dob": {"missing_count": "19", "completeness_percentage": 0.0}, "dept_19_age": {"missing_count": "19", "completeness_percentage": 0.0}, "dept_19_gender": {"missing_count": "19", "completeness_percentage": 0.0}, "relationship_type_19": {"missing_count": "19", "completeness_percentage": 0.0}, "dept_20": {"missing_count": "19", "completeness_percentage": 0.0}, "dept_20_dob": {"missing_count": "19", "completeness_percentage": 0.0}, "dept_20_age": {"missing_count": "19", "completeness_percentage": 0.0}, "dept_20_gender": {"missing_count": "19", "completeness_percentage": 0.0}, "relationship_type_20": {"missing_count": "19", "completeness_percentage": 0.0}, "job_type": {"missing_count": "0", "completeness_percentage": 100.0}, "region": {"missing_count": "0", "completeness_percentage": 100.0}, "health_condition": {"missing_count": "0", "completeness_percentage": 100.0}, "chronic_condition": {"missing_count": "0", "completeness_percentage": 100.0}, "prescription_use": {"missing_count": "0", "completeness_percentage": 100.0}, "income_tier": {"missing_count": "0", "completeness_percentage": 100.0}, "risk_tolerance": {"missing_count": "0", "completeness_percentage": 100.0}, "lifestyle": {"missing_count": "0", "completeness_percentage": 100.0}, "travel_frequency": {"missing_count": "0", "completeness_percentage": 100.0}, "hsa_familiarity": {"missing_count": "0", "completeness_percentage": 100.0}, "mental_health_needs": {"missing_count": "0", "completeness_percentage": 100.0}}, "critical_field_completeness": {"name": {"missing_count": "0", "completeness_percentage": 100.0}, "gender": {"missing_count": "0", "completeness_percentage": 100.0}, "zipcode": {"missing_count": "0", "completeness_percentage": 100.0}, "marital_status": {"missing_count": "0", "completeness_percentage": 100.0}}}}, "prediction_summary": {"total_employees": 19, "plan_type_distribution": {"successful_predictions": {"HDHP + HSA": 9, "POS": 4, "PPO": 3, "HMO": 2, "HDHP": 1}, "failed_predictions": "0", "success_rate": 100.0, "total_predicted": "19"}, "benefits_distribution": {}, "confidence_metrics": {"plan_confidence": {"mean": "0.701", "min": "0.315", "max": "0.908", "count": 19}}, "prediction_success_rates": {}, "top_3_plan_probabilities": []}, "total_employees": 19, "total_columns": 165, "has_predictions": true, "prediction_columns": ["predicted_plan_type", "plan_confidence"]}, "metadata": {"file_processing": {"filename": "address_lastname_grouping_test.csv", "size": 3000, "original_column_names": ["Employee_ID", "First_Name", "Last_Name", "Gender", "DOB", "Address1", "City", "State", "ZIP", "Marital_Status", "Relationship", "Salary", "Medical_Plan", "Dental_Plan", "Vision_Plan"]}, "pattern_analysis": {"pattern_type": "row_based_member_level", "pattern_confidence": 1.1, "pattern_reason": "Found 'relationship' field; Found both employee and dependent relationship values; Found duplicate employee_ids (family grouping)", "analysis_details": {"confidence": 1.1, "reason": "Found 'relationship' field; Found both employee and dependent relationship values; Found duplicate employee_ids (family grouping)"}}, "field_mapping": {"total_fields_mapped": 15, "mapping_success": true, "unmapped_fields": [], "mapping_confidence": 0, "field_mapping_details": {"Employee_ID": "employee_id", "First_Name": "first_name", "Last_Name": "last_name", "Gender": "gender", "DOB": "dob", "Address1": "address1", "City": "city", "State": "state", "ZIP": "zipcode", "Marital_Status": "marital_status", "Relationship": "relationship", "Salary": "salary", "Medical_Plan": "medical_plan", "Dental_Plan": "dental_plan", "Vision_Plan": "vision_plan"}}, "data_processing": {"validation_passed": true, "processing_summary": {"original_rows": 34, "original_columns": 15, "processed_rows": 19, "processed_columns": 144, "validation_passed": true, "validation_errors": 0, "error_rows": 0, "fields_processed": {"name_fields": true, "age_converted": true, "gender_standardized": true, "addresses_cleaned": true, "zipcode_validated": true}, "data_quality": {"complete_records": 19, "missing_data_rows": 0}}, "data_quality_score": 0.824, "processing_details": {"success": true, "preprocessed_data": "   employee_id first_name  last_name  gender         dob      address1       city  ... dept_19_gender  relationship_type_19 dept_20 dept_20_dob  dept_20_age dept_20_gender relationship_type_20\n0         E001       <PERSON>  1980-01-15   123 Main St    Anytown  ...           None                  None    None        None         None           None                 None\n1          Na<PERSON>  1978-02-28   456 Oak Ave  Somewhere  ...           None                  None    None        None         None           None                 None\n2          Na<PERSON>  1985-11-05   456 Oak Ave  Somewhere  ...           None                  None    None        None         None           None                 None\n3          Na<PERSON>  1990-06-30   789 Pine Rd  Elsewhere  ...           None                  None    None        None         None           None                 None\n4          NaN      <PERSON>  Female  1987-07-12   789 Pine Rd  Elsewhere  ...           None                  None    None        None         None           None                 None\n5          Na<PERSON>  1979-10-17    321 Elm St    Nowhere  ...           None                  None    None        None         None           None                 None\n6          Na<PERSON>  Female  1981-03-24    321 Elm St    Nowhere  ...           None                  None    None        None         None           None                 None\n7          Na<PERSON>  1986-09-09  987 Cedar Ln  Someplace  ...           None                  None    None        None         None           None                 None\n8          Na<PERSON>  1984-12-18  987 Cedar Ln  Someplace  ...           None                  None    None        None         None           None                 None\n9          <PERSON><PERSON>  1977-04-07           NaN   Yourtown  ...           None                  None    None        None         None           None                 None\n10         <PERSON><PERSON>  2006-05-22           NaN   <PERSON>town  ...           None                  None    None        None         None           None                 None\n11         <PERSON><PERSON>  2009-10-11           NaN   <PERSON>town  ...           None                  None    None        None         None           None                 None\n12         Na<PERSON>  1989-08-21           NaN     Mytown  ...           None                  None    None        None         None           None                 None\n13         NaN     Connor      Clark    Male  1982-01-26           NaN    Ourtown  ...           None                  None    None        None         None           None                 None\n14         NaN     Rachel      Clark  Female  1984-06-18           NaN    Ourtown  ...           None                  None    None        None         None           None                 None\n15         NaN       <PERSON>      Clark    Male  2012-01-05           NaN    Ourtown  ...           None                  None    None        None         None           None                 None\n16         NaN       Lily      <PERSON>  Female  2014-07-12           NaN    Ourtown  ...           None                  None    None        None         None           None                 None\n17         NaN    Destiny      <PERSON>  Female  1986-05-13           NaN  Theirtown  ...           None                  None    None        None         None           None                 None\n18         NaN       Noah      Lewis    Male  2011-09-28           NaN  Theirtown  ...           None                  None    None        None         None           None                 None\n\n[19 rows x 144 columns]", "summary": {"original_rows": 34, "original_columns": 15, "processed_rows": 19, "processed_columns": 144, "validation_passed": true, "validation_errors": 0, "error_rows": 0, "fields_processed": {"name_fields": true, "age_converted": true, "gender_standardized": true, "addresses_cleaned": true, "zipcode_validated": true}, "data_quality": {"complete_records": 19, "missing_data_rows": 0}}, "validation_result": {"is_valid": true, "errors": [], "error_rows": [], "total_errors": 0}}}, "enrichment_and_prediction": {"success": true, "enriched_data": "   employee_id first_name  last_name  ...                                    benefits_reason                                 top_3_benefits top_3_benefits_confidences\n0         E001       <PERSON>  ...  Dental: Essential oral health coverage for all...  [Employee Assistance, Wellness Programs, FSA]            [0.5, 0.5, 0.5]\n1         E002    <PERSON>  ...  Dental: Essential oral health coverage for all...  [Employee Assistance, Wellness Programs, FSA]            [0.5, 0.5, 0.5]\n2         E003      <PERSON>  ...  Dental: Essential oral health coverage for all...  [Employee Assistance, Wellness Programs, FSA]            [0.5, 0.5, 0.5]\n3         E004      <PERSON>  ...  Dental: Essential oral health coverage for all...  [Employee Assistance, Wellness Programs, FSA]            [0.5, 0.5, 0.5]\n4         E005      <PERSON>  ...  Dental: Essential oral health coverage for all...  [Employee Assistance, Wellness Programs, FSA]            [0.5, 0.5, 0.5]\n5         E006      <PERSON>  ...  Dental: Essential oral health coverage for all...  [Employee Assistance, Wellness Programs, FSA]            [0.5, 0.5, 0.5]\n6         E007       <PERSON>  ...  Dental: Essential oral health coverage for all...  [Employee Assistance, Wellness Programs, FSA]            [0.5, 0.5, 0.5]\n7         E008     Xavier  Rodriguez  ...                                ML model prediction  [Employee Assistance, Wellness Programs, FSA]            [0.5, 0.5, 0.5]\n8         E009     Yvonne        Lee  ...  Dental: Essential oral health coverage for all...  [Employee Assistance, Wellness Programs, FSA]            [0.5, 0.5, 0.5]\n9         E010      Aaron      White  ...  Accident: Recommended for active lifestyle and...  [Employee Assistance, Wellness Programs, FSA]            [0.5, 0.5, 0.5]\n10        E011      Kelly      White  ...  STD: Income protection for Medium ($50K–$100K)...  [Employee Assistance, Wellness Programs, FSA]            [0.5, 0.5, 0.5]\n11        E012       Ryan      White  ...  Default benefits assignment due to prediction ...           [Dental, Vision, Hospital Indemnity]            [0.5, 0.5, 0.5]\n12        E013     Brooke     Harris  ...  Default benefits assignment due to prediction ...           [Dental, Vision, Hospital Indemnity]            [0.5, 0.5, 0.5]\n13        E014     Connor      Clark  ...  Default benefits assignment due to prediction ...           [Dental, Vision, Hospital Indemnity]            [0.5, 0.5, 0.5]\n14        E015     Rachel      Clark  ...  Default benefits assignment due to prediction ...           [Dental, Vision, Hospital Indemnity]            [0.5, 0.5, 0.5]\n15        E016       Jake      Clark  ...  Default benefits assignment due to prediction ...           [Dental, Vision, Hospital Indemnity]            [0.5, 0.5, 0.5]\n16        E017       Lily      Clark  ...  Default benefits assignment due to prediction ...           [Dental, Vision, Hospital Indemnity]            [0.5, 0.5, 0.5]\n17        E018    Destiny      Lewis  ...  Default benefits assignment due to prediction ...           [Dental, Vision, Hospital Indemnity]            [0.5, 0.5, 0.5]\n18        E019       Noah      Lewis  ...  Default benefits assignment due to prediction ...           [Dental, Vision, Hospital Indemnity]            [0.5, 0.5, 0.5]\n\n[19 rows x 165 columns]", "enrichment_summary": {"total_employees": 19, "features_analyzed": 19, "enrichment_details": {"age": {"original_missing": "0", "final_missing": "0", "enriched_count": "0", "completion_rate": 100.0}, "gender": {"original_missing": "0", "final_missing": "0", "enriched_count": "0", "completion_rate": 100.0}, "marital_status": {"original_missing": "0", "final_missing": "0", "enriched_count": "0", "completion_rate": 100.0}, "dept_count": {"original_missing": "0", "final_missing": "0", "enriched_count": "0", "completion_rate": 100.0}, "job_type": {"original_missing": 19, "final_missing": "0", "enriched_count": "19", "completion_rate": 100.0}, "employment_type": {"original_missing": "19", "final_missing": "0", "enriched_count": "19", "completion_rate": 100.0}, "department": {"original_missing": "19", "final_missing": "0", "enriched_count": "19", "completion_rate": 100.0}, "region": {"original_missing": 19, "final_missing": "0", "enriched_count": "19", "completion_rate": 100.0}, "health_condition": {"original_missing": 19, "final_missing": "0", "enriched_count": "19", "completion_rate": 100.0}, "chronic_condition": {"original_missing": 19, "final_missing": "0", "enriched_count": "19", "completion_rate": 100.0}, "prescription_use": {"original_missing": 19, "final_missing": "0", "enriched_count": "19", "completion_rate": 100.0}, "income_tier": {"original_missing": 19, "final_missing": "0", "enriched_count": "19", "completion_rate": 100.0}, "risk_tolerance": {"original_missing": 19, "final_missing": "0", "enriched_count": "19", "completion_rate": 100.0}, "lifestyle": {"original_missing": 19, "final_missing": "0", "enriched_count": "19", "completion_rate": 100.0}, "travel_frequency": {"original_missing": 19, "final_missing": "0", "enriched_count": "19", "completion_rate": 100.0}, "hsa_familiarity": {"original_missing": 19, "final_missing": "0", "enriched_count": "19", "completion_rate": 100.0}, "pregnancy_status": {"original_missing": "19", "final_missing": "0", "enriched_count": "19", "completion_rate": 100.0}, "tobacco_use": {"original_missing": "19", "final_missing": "0", "enriched_count": "19", "completion_rate": 100.0}, "mental_health_needs": {"original_missing": 19, "final_missing": "0", "enriched_count": "19", "completion_rate": 100.0}}, "data_quality_improvement": {"total_missing_before": "285", "total_missing_after": "0", "total_enriched": "285", "overall_completion_rate": 100.0}}, "comprehensive_statistics": {"basic_demographics": {"age_statistics": {"average_age": 34.10526315789474, "median_age": 39.0, "age_range": {"min": 11, "max": 48}, "age_distribution": {"18-30": 1, "31-45": 12, "46-60": 2, "60+": 0}}, "gender_composition": {"counts": {"Male": 10, "Female": 9}, "percentages": {"Male": 52.63, "Female": 47.37}}, "marital_status_distribution": {"counts": {"Not Married": 11, "Married": 8}, "percentages": {"Not Married": 57.89, "Married": 42.11}}}, "dependent_analysis": {"dependent_count_distribution": {"average_dependents": 0.7368421052631579, "median_dependents": 0.0, "distribution": {"0": 11, "2": 6, "1": 2}, "employees_with_dependents": 8, "percentage_with_dependents": 42.11}, "dependent_age_analysis": {"total_dependents_found": 14, "average_dependent_age": 25.86, "median_dependent_age": 18.0, "dependents_over_26_as_children": 0, "age_distribution": {"0-5": 0, "6-12": 2, "13-18": 5, "19-26": 1, "26+": 6}}}, "employment_demographics": {"department_distribution": {"counts": {"Finance": 7, "Manufacturing": 4, "Information Technology": 4, "Sales": 2, "Engineering": 2}, "percentages": {"Finance": 36.84, "Manufacturing": 21.05, "Information Technology": 21.05, "Sales": 10.53, "Engineering": 10.53}}, "employment_type_distribution": {"counts": {"Full-Time": 15, "Part-Time": 2, "Contract": 2}, "percentages": {"Full-Time": 78.95, "Part-Time": 10.53, "Contract": 10.53}}, "job_type_distribution": {"counts": {"Desk": 19}, "percentages": {"Desk": 100.0}}, "employee_class_distribution": {"counts": {}, "percentages": {}}, "salary_analysis": {"average_salary": 41973.68, "median_salary": 45000.0, "salary_range": {"min": "0", "max": "95000"}, "salary_distribution": {"under_40k": 7, "40k_75k": 9, "75k_100k": 3, "over_100k": 0}}}, "health_and_lifestyle": {"health_condition_distribution": {"counts": {"Good": 9, "Fair": 7, "Excellent": 3}, "percentages": {"Good": 47.37, "Fair": 36.84, "Excellent": 15.79}}, "chronic_condition_distribution": {"counts": {"N": 19}, "percentages": {"N": 100.0}}, "tobacco_use_distribution": {"counts": {"N": 16, "Y": 3}, "percentages": {"N": 84.21, "Y": 15.79}}, "lifestyle_distribution": {"counts": {"Moderate": 12, "Active": 7}, "percentages": {"Moderate": 63.16, "Active": 36.84}}, "prescription_use_distribution": {"counts": {"None": 17, "Occasional": 2}, "percentages": {"None": 89.47, "Occasional": 10.53}}}, "coverage_analysis": {"coverage_tier_distribution": {"counts": {}, "percentages": {}}, "coverage_tier_by_family_size": {}, "medical_plan_distribution": {"counts": {"Y": 19}, "percentages": {"Y": 100.0}}, "dental_plan_distribution": {"counts": {"Y": 17, "N": 2}, "percentages": {"Y": 89.47, "N": 10.53}}, "vision_plan_distribution": {"counts": {"Y": 16, "N": 3}, "percentages": {"Y": 84.21, "N": 15.79}}, "life_plan_distribution": {"counts": {}, "percentages": {}}}, "geographic_distribution": {"state_distribution": {"counts": {"ND": 4, "ID": 3, "TX": 2, "FL": 2, "NY": 2, "OR": 2, "SD": 2, "CA": 1, "WY": 1}, "percentages": {"ND": 21.05, "ID": 15.79, "TX": 10.53, "FL": 10.53, "NY": 10.53, "OR": 10.53, "SD": 10.53, "CA": 5.26, "WY": 5.26}}, "region_distribution": {"counts": {"Rural": 10, "Urban": 8, "Suburban": 1}, "percentages": {"Rural": 52.63, "Urban": 42.11, "Suburban": 5.26}}, "top_cities": {"counts": {"Ourtown": 4, "Yourtown": 3, "Somewhere": 2, "Elsewhere": 2, "Nowhere": 2, "Someplace": 2, "Theirtown": 2, "Anytown": 1, "Mytown": 1}, "percentages": {"Ourtown": 21.05, "Yourtown": 15.79, "Somewhere": 10.53, "Elsewhere": 10.53, "Nowhere": 10.53, "Someplace": 10.53, "Theirtown": 10.53, "Anytown": 5.26, "Mytown": 5.26}}}, "financial_demographics": {"income_tier_distribution": {"counts": {"Medium ($50K–$100K)": 14, "High (>$100K)": 3, "Low (<$50K)": 2}, "percentages": {"Medium ($50K–$100K)": 73.68, "High (>$100K)": 15.79, "Low (<$50K)": 10.53}}, "risk_tolerance_distribution": {"counts": {"Medium": 12, "Low": 7}, "percentages": {"Medium": 63.16, "Low": 36.84}}, "hsa_familiarity_distribution": {"counts": {"N": 10, "Y": 9}, "percentages": {"N": 52.63, "Y": 47.37}}}, "risk_assessment": {"group_risk_score": 22.95, "group_risk_level": "Low", "risk_distribution": {"low_risk": 15, "medium_risk": 4, "high_risk": 0}, "risk_statistics": {"min_risk_score": 13, "max_risk_score": 40, "median_risk_score": 23.0, "std_risk_score": 7.62}, "top_risk_factors": {"Poor Health Condition": 7, "Tobacco Use": 3}, "individual_risks": [{"employee_id": "E001", "risk_score": 23, "risk_level": "Low", "risk_factors": []}, {"employee_id": "E002", "risk_score": 33, "risk_level": "Medium", "risk_factors": ["Health condition: Fair"]}, {"employee_id": "E003", "risk_score": 40, "risk_level": "Medium", "risk_factors": ["Health condition: Fair", "Tobacco user"]}, {"employee_id": "E004", "risk_score": 25, "risk_level": "Low", "risk_factors": ["Health condition: Fair"]}, {"employee_id": "E005", "risk_score": 25, "risk_level": "Low", "risk_factors": ["Health condition: Fair"]}, {"employee_id": "E006", "risk_score": 18, "risk_level": "Low", "risk_factors": []}, {"employee_id": "E007", "risk_score": 23, "risk_level": "Low", "risk_factors": []}, {"employee_id": "E008", "risk_score": 13, "risk_level": "Low", "risk_factors": []}, {"employee_id": "E009", "risk_score": 20, "risk_level": "Low", "risk_factors": []}, {"employee_id": "E010", "risk_score": 30, "risk_level": "Medium", "risk_factors": ["Health condition: Fair"]}]}, "data_quality_metrics": {"overall_completeness": {"total_cells": 2945, "missing_cells": "2160", "completeness_percentage": 26.66}, "column_completeness": {"employee_id": {"missing_count": "0", "completeness_percentage": 100.0}, "first_name": {"missing_count": "0", "completeness_percentage": 100.0}, "last_name": {"missing_count": "0", "completeness_percentage": 100.0}, "gender": {"missing_count": "0", "completeness_percentage": 100.0}, "dob": {"missing_count": "0", "completeness_percentage": 100.0}, "address1": {"missing_count": "10", "completeness_percentage": 47.37}, "city": {"missing_count": "0", "completeness_percentage": 100.0}, "state": {"missing_count": "0", "completeness_percentage": 100.0}, "zipcode": {"missing_count": "0", "completeness_percentage": 100.0}, "marital_status": {"missing_count": "0", "completeness_percentage": 100.0}, "relationship": {"missing_count": "0", "completeness_percentage": 100.0}, "salary": {"missing_count": "0", "completeness_percentage": 100.0}, "medical_plan": {"missing_count": "0", "completeness_percentage": 100.0}, "dental_plan": {"missing_count": "0", "completeness_percentage": 100.0}, "vision_plan": {"missing_count": "0", "completeness_percentage": 100.0}, "dept_i": {"missing_count": "11", "completeness_percentage": 42.11}, "dept_i_gender": {"missing_count": "11", "completeness_percentage": 42.11}, "dept_i_dob": {"missing_count": "11", "completeness_percentage": 42.11}, "relationship_type_i": {"missing_count": "11", "completeness_percentage": 42.11}, "dept_i_medical_plan": {"missing_count": "11", "completeness_percentage": 42.11}, "dept_i_dental_plan": {"missing_count": "11", "completeness_percentage": 42.11}, "dept_i_vision_plan": {"missing_count": "11", "completeness_percentage": 42.11}, "dept_1": {"missing_count": "11", "completeness_percentage": 42.11}, "dept_1_dob": {"missing_count": "11", "completeness_percentage": 42.11}, "dept_1_gender": {"missing_count": "11", "completeness_percentage": 42.11}, "relationship_type_1": {"missing_count": "11", "completeness_percentage": 42.11}, "dept_1_medical_plan": {"missing_count": "11", "completeness_percentage": 42.11}, "dept_1_dental_plan": {"missing_count": "11", "completeness_percentage": 42.11}, "dept_1_vision_plan": {"missing_count": "11", "completeness_percentage": 42.11}, "dept_2": {"missing_count": "13", "completeness_percentage": 31.58}, "dept_2_dob": {"missing_count": "13", "completeness_percentage": 31.58}, "dept_2_gender": {"missing_count": "13", "completeness_percentage": 31.58}, "relationship_type_2": {"missing_count": "13", "completeness_percentage": 31.58}, "dept_2_medical_plan": {"missing_count": "13", "completeness_percentage": 31.58}, "dept_2_dental_plan": {"missing_count": "13", "completeness_percentage": 31.58}, "dept_2_vision_plan": {"missing_count": "13", "completeness_percentage": 31.58}, "dept_count": {"missing_count": "0", "completeness_percentage": 100.0}, "name": {"missing_count": "0", "completeness_percentage": 100.0}, "age": {"missing_count": "0", "completeness_percentage": 100.0}, "dept_1_age": {"missing_count": "11", "completeness_percentage": 42.11}, "dept_2_age": {"missing_count": "13", "completeness_percentage": 31.58}, "middle_name": {"missing_count": "19", "completeness_percentage": 0.0}, "address2": {"missing_count": "19", "completeness_percentage": 0.0}, "record_type": {"missing_count": "19", "completeness_percentage": 0.0}, "employment_type": {"missing_count": "0", "completeness_percentage": 100.0}, "employee_class": {"missing_count": "19", "completeness_percentage": 0.0}, "department": {"missing_count": "0", "completeness_percentage": 100.0}, "hire_date": {"missing_count": "19", "completeness_percentage": 0.0}, "tobacco_use": {"missing_count": "0", "completeness_percentage": 100.0}, "pregnancy_status": {"missing_count": "0", "completeness_percentage": 100.0}, "life_plan": {"missing_count": "19", "completeness_percentage": 0.0}, "add_plan": {"missing_count": "19", "completeness_percentage": 0.0}, "coverage_tier": {"missing_count": "19", "completeness_percentage": 0.0}, "ssn": {"missing_count": "19", "completeness_percentage": 0.0}, "dept_3": {"missing_count": "19", "completeness_percentage": 0.0}, "dept_3_dob": {"missing_count": "19", "completeness_percentage": 0.0}, "dept_3_age": {"missing_count": "19", "completeness_percentage": 0.0}, "dept_3_gender": {"missing_count": "19", "completeness_percentage": 0.0}, "relationship_type_3": {"missing_count": "19", "completeness_percentage": 0.0}, "dept_4": {"missing_count": "19", "completeness_percentage": 0.0}, "dept_4_dob": {"missing_count": "19", "completeness_percentage": 0.0}, "dept_4_age": {"missing_count": "19", "completeness_percentage": 0.0}, "dept_4_gender": {"missing_count": "19", "completeness_percentage": 0.0}, "relationship_type_4": {"missing_count": "19", "completeness_percentage": 0.0}, "dept_5": {"missing_count": "19", "completeness_percentage": 0.0}, "dept_5_dob": {"missing_count": "19", "completeness_percentage": 0.0}, "dept_5_age": {"missing_count": "19", "completeness_percentage": 0.0}, "dept_5_gender": {"missing_count": "19", "completeness_percentage": 0.0}, "relationship_type_5": {"missing_count": "19", "completeness_percentage": 0.0}, "dept_6": {"missing_count": "19", "completeness_percentage": 0.0}, "dept_6_dob": {"missing_count": "19", "completeness_percentage": 0.0}, "dept_6_age": {"missing_count": "19", "completeness_percentage": 0.0}, "dept_6_gender": {"missing_count": "19", "completeness_percentage": 0.0}, "relationship_type_6": {"missing_count": "19", "completeness_percentage": 0.0}, "dept_7": {"missing_count": "19", "completeness_percentage": 0.0}, "dept_7_dob": {"missing_count": "19", "completeness_percentage": 0.0}, "dept_7_age": {"missing_count": "19", "completeness_percentage": 0.0}, "dept_7_gender": {"missing_count": "19", "completeness_percentage": 0.0}, "relationship_type_7": {"missing_count": "19", "completeness_percentage": 0.0}, "dept_8": {"missing_count": "19", "completeness_percentage": 0.0}, "dept_8_dob": {"missing_count": "19", "completeness_percentage": 0.0}, "dept_8_age": {"missing_count": "19", "completeness_percentage": 0.0}, "dept_8_gender": {"missing_count": "19", "completeness_percentage": 0.0}, "relationship_type_8": {"missing_count": "19", "completeness_percentage": 0.0}, "dept_9": {"missing_count": "19", "completeness_percentage": 0.0}, "dept_9_dob": {"missing_count": "19", "completeness_percentage": 0.0}, "dept_9_age": {"missing_count": "19", "completeness_percentage": 0.0}, "dept_9_gender": {"missing_count": "19", "completeness_percentage": 0.0}, "relationship_type_9": {"missing_count": "19", "completeness_percentage": 0.0}, "dept_10": {"missing_count": "19", "completeness_percentage": 0.0}, "dept_10_dob": {"missing_count": "19", "completeness_percentage": 0.0}, "dept_10_age": {"missing_count": "19", "completeness_percentage": 0.0}, "dept_10_gender": {"missing_count": "19", "completeness_percentage": 0.0}, "relationship_type_10": {"missing_count": "19", "completeness_percentage": 0.0}, "dept_11": {"missing_count": "19", "completeness_percentage": 0.0}, "dept_11_dob": {"missing_count": "19", "completeness_percentage": 0.0}, "dept_11_age": {"missing_count": "19", "completeness_percentage": 0.0}, "dept_11_gender": {"missing_count": "19", "completeness_percentage": 0.0}, "relationship_type_11": {"missing_count": "19", "completeness_percentage": 0.0}, "dept_12": {"missing_count": "19", "completeness_percentage": 0.0}, "dept_12_dob": {"missing_count": "19", "completeness_percentage": 0.0}, "dept_12_age": {"missing_count": "19", "completeness_percentage": 0.0}, "dept_12_gender": {"missing_count": "19", "completeness_percentage": 0.0}, "relationship_type_12": {"missing_count": "19", "completeness_percentage": 0.0}, "dept_13": {"missing_count": "19", "completeness_percentage": 0.0}, "dept_13_dob": {"missing_count": "19", "completeness_percentage": 0.0}, "dept_13_age": {"missing_count": "19", "completeness_percentage": 0.0}, "dept_13_gender": {"missing_count": "19", "completeness_percentage": 0.0}, "relationship_type_13": {"missing_count": "19", "completeness_percentage": 0.0}, "dept_14": {"missing_count": "19", "completeness_percentage": 0.0}, "dept_14_dob": {"missing_count": "19", "completeness_percentage": 0.0}, "dept_14_age": {"missing_count": "19", "completeness_percentage": 0.0}, "dept_14_gender": {"missing_count": "19", "completeness_percentage": 0.0}, "relationship_type_14": {"missing_count": "19", "completeness_percentage": 0.0}, "dept_15": {"missing_count": "19", "completeness_percentage": 0.0}, "dept_15_dob": {"missing_count": "19", "completeness_percentage": 0.0}, "dept_15_age": {"missing_count": "19", "completeness_percentage": 0.0}, "dept_15_gender": {"missing_count": "19", "completeness_percentage": 0.0}, "relationship_type_15": {"missing_count": "19", "completeness_percentage": 0.0}, "dept_16": {"missing_count": "19", "completeness_percentage": 0.0}, "dept_16_dob": {"missing_count": "19", "completeness_percentage": 0.0}, "dept_16_age": {"missing_count": "19", "completeness_percentage": 0.0}, "dept_16_gender": {"missing_count": "19", "completeness_percentage": 0.0}, "relationship_type_16": {"missing_count": "19", "completeness_percentage": 0.0}, "dept_17": {"missing_count": "19", "completeness_percentage": 0.0}, "dept_17_dob": {"missing_count": "19", "completeness_percentage": 0.0}, "dept_17_age": {"missing_count": "19", "completeness_percentage": 0.0}, "dept_17_gender": {"missing_count": "19", "completeness_percentage": 0.0}, "relationship_type_17": {"missing_count": "19", "completeness_percentage": 0.0}, "dept_18": {"missing_count": "19", "completeness_percentage": 0.0}, "dept_18_dob": {"missing_count": "19", "completeness_percentage": 0.0}, "dept_18_age": {"missing_count": "19", "completeness_percentage": 0.0}, "dept_18_gender": {"missing_count": "19", "completeness_percentage": 0.0}, "relationship_type_18": {"missing_count": "19", "completeness_percentage": 0.0}, "dept_19": {"missing_count": "19", "completeness_percentage": 0.0}, "dept_19_dob": {"missing_count": "19", "completeness_percentage": 0.0}, "dept_19_age": {"missing_count": "19", "completeness_percentage": 0.0}, "dept_19_gender": {"missing_count": "19", "completeness_percentage": 0.0}, "relationship_type_19": {"missing_count": "19", "completeness_percentage": 0.0}, "dept_20": {"missing_count": "19", "completeness_percentage": 0.0}, "dept_20_dob": {"missing_count": "19", "completeness_percentage": 0.0}, "dept_20_age": {"missing_count": "19", "completeness_percentage": 0.0}, "dept_20_gender": {"missing_count": "19", "completeness_percentage": 0.0}, "relationship_type_20": {"missing_count": "19", "completeness_percentage": 0.0}, "job_type": {"missing_count": "0", "completeness_percentage": 100.0}, "region": {"missing_count": "0", "completeness_percentage": 100.0}, "health_condition": {"missing_count": "0", "completeness_percentage": 100.0}, "chronic_condition": {"missing_count": "0", "completeness_percentage": 100.0}, "prescription_use": {"missing_count": "0", "completeness_percentage": 100.0}, "income_tier": {"missing_count": "0", "completeness_percentage": 100.0}, "risk_tolerance": {"missing_count": "0", "completeness_percentage": 100.0}, "lifestyle": {"missing_count": "0", "completeness_percentage": 100.0}, "travel_frequency": {"missing_count": "0", "completeness_percentage": 100.0}, "hsa_familiarity": {"missing_count": "0", "completeness_percentage": 100.0}, "mental_health_needs": {"missing_count": "0", "completeness_percentage": 100.0}}, "critical_field_completeness": {"name": {"missing_count": "0", "completeness_percentage": 100.0}, "gender": {"missing_count": "0", "completeness_percentage": 100.0}, "zipcode": {"missing_count": "0", "completeness_percentage": 100.0}, "marital_status": {"missing_count": "0", "completeness_percentage": 100.0}}}}, "feature_validation": {"is_valid": true, "missing_features": [], "feature_completeness": {"age": {"present": true, "missing_values": "0", "completion_rate": 100.0}, "gender": {"present": true, "missing_values": "0", "completion_rate": 100.0}, "marital_status": {"present": true, "missing_values": "0", "completion_rate": 100.0}, "dept_count": {"present": true, "missing_values": "0", "completion_rate": 100.0}, "job_type": {"present": true, "missing_values": "0", "completion_rate": 100.0}, "employment_type": {"present": true, "missing_values": "0", "completion_rate": 100.0}, "department": {"present": true, "missing_values": "0", "completion_rate": 100.0}, "region": {"present": true, "missing_values": "0", "completion_rate": 100.0}, "health_condition": {"present": true, "missing_values": "0", "completion_rate": 100.0}, "chronic_condition": {"present": true, "missing_values": "0", "completion_rate": 100.0}, "prescription_use": {"present": true, "missing_values": "0", "completion_rate": 100.0}, "income_tier": {"present": true, "missing_values": "0", "completion_rate": 100.0}, "risk_tolerance": {"present": true, "missing_values": "0", "completion_rate": 100.0}, "lifestyle": {"present": true, "missing_values": "0", "completion_rate": 100.0}, "travel_frequency": {"present": true, "missing_values": "0", "completion_rate": 100.0}, "hsa_familiarity": {"present": true, "missing_values": "0", "completion_rate": 100.0}, "pregnancy_status": {"present": true, "missing_values": "0", "completion_rate": 100.0}, "tobacco_use": {"present": true, "missing_values": "0", "completion_rate": 100.0}, "mental_health_needs": {"present": true, "missing_values": "0", "completion_rate": 100.0}}, "overall_completion_rate": 100.0, "ready_for_prediction": "True"}, "prediction_method": "ml_models", "model_predictions": {"plan_types": {"total_predictions": 19, "average_confidence": "0.7007808", "unique_plans": 5}}, "prediction_summary": {"total_employees": 19, "plan_type_distribution": {"successful_predictions": {"HDHP + HSA": 9, "POS": 4, "PPO": 3, "HMO": 2, "HDHP": 1}, "failed_predictions": "0", "success_rate": 100.0, "total_predicted": "19"}, "benefits_distribution": {}, "confidence_metrics": {"plan_confidence": {"mean": "0.701", "min": "0.315", "max": "0.908", "count": 19}}, "prediction_success_rates": {}, "top_3_plan_probabilities": ["E001: [0.9083523, 0.*********, 0.02987497]", "E002: [0.6828363, 0.1688195, 0.********]", "E003: [0.********, 0.*********, 0.********]", "E004: [0.6931619, 0.********, 0.********]", "E005: [0.********, 0.********, 0.********]", "E006: [0.7206716, 0.1216515, 0.090712465]", "E007: [0.79131466, 0.18061617, 0.010884565]", "E008: [0.31479773, 0.30657175, 0.17489885]", "E009: [0.77176094, 0.20286268, 0.011132505]", "E010: [0.76978266, 0.*********, 0.*********]", "E011: [0.8191391, 0.09687783, 0.06701801]", "E012: [0.7566702, 0.22109824, 0.00977206]", "E013: [0.83525723, 0.08598737, 0.049331944]", "E014: [0.36536977, 0.24221893, 0.20186205]", "E015: [0.7499423, 0.22563146, 0.009710918]", "E016: [0.8719066, 0.080098204, 0.028052783]", "E017: [0.3938287, 0.3278765, 0.17925362]", "E018: [0.7738694, 0.0969907, 0.06508199]", "E019: [0.57680446, 0.2213614, 0.09808987]"]}}}}}