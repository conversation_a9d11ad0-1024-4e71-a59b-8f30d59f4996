{"success": true, "status_code": 200, "data": "{'enriched_data_csv': 'employee_id,first_name,last_name,gender,dob,address1,city,state,zipcode,marital_status,relationship,salary,medical_plan,dental_plan,vision_plan,dept_i_gender,dept_i_dob,relationship_type_i,dept_i_medical_plan,dept_i_dental_plan,dept_i_vision_plan,dept_1,dept_1_dob,dept_1_gender,relationship_type_1,dept_1_medical_plan,dept_1_dental_plan,dept_1_vision_plan,dept_2,dept_2_dob,dept_2_gender,relationship_type_2,dept_2_medical_plan,dept_2_dental_plan,dept_2_vision_plan,dept_count,name,age,dept_1_age,dept_2_age,middle_name,address2,record_type,employment_type,employee_class,department,hire_date,tobacco_use,pregnancy_status,life_plan,add_plan,coverage_tier,ssn,dept_3,dept_3_dob,dept_3_age,dept_3_gender,relationship_type_3,dept_4,dept_4_dob,dept_4_age,dept_4_gender,relationship_type_4,dept_5,dept_5_dob,dept_5_age,dept_5_gender,relationship_type_5,dept_6,dept_6_dob,dept_6_age,dept_6_gender,relationship_type_6,dept_7,dept_7_dob,dept_7_age,dept_7_gender,relationship_type_7,dept_8,dept_8_dob,dept_8_age,dept_8_gender,relationship_type_8,dept_9,dept_9_dob,dept_9_age,dept_9_gender,relationship_type_9,dept_10,dept_10_dob,dept_10_age,dept_10_gender,relationship_type_10,dept_11,dept_11_dob,dept_11_age,dept_11_gender,relationship_type_11,dept_12,dept_12_dob,dept_12_age,dept_12_gender,relationship_type_12,dept_13,dept_13_dob,dept_13_age,dept_13_gender,relationship_type_13,dept_14,dept_14_dob,dept_14_age,dept_14_gender,relationship_type_14,dept_15,dept_15_dob,dept_15_age,dept_15_gender,relationship_type_15,dept_16,dept_16_dob,dept_16_age,dept_16_gender,relationship_type_16,dept_17,dept_17_dob,dept_17_age,dept_17_gender,relationship_type_17,dept_18,dept_18_dob,dept_18_age,dept_18_gender,relationship_type_18,dept_19,dept_19_dob,dept_19_age,dept_19_gender,relationship_type_19,dept_20,dept_20_dob,dept_20_age,dept_20_gender,relationship_type_20,job_type,region,health_condition,chronic_condition,prescription_use,income_tier,risk_tolerance,lifestyle,travel_frequency,hsa_familiarity,mental_health_needs,predicted_plan_type,plan_confidence,plan_reason,top_3_plans,top_3_plan_confidences,predicted_benefits,benefits_confidence,benefits_reason,top_3_benefits,top_3_benefits_confidences\\r\\nE001,John,Smith,Male,1980-01-15,123 Main St,Anytown,CA,90210,Married,Employee,65000,Y,Y,Y,Male,2010-09-12,Child,PPO,Y,Y,Jane Smith,1982-05-20,Female,Spouse,PPO,Y,Y,Mike Smith,2010-09-12,Male,Child,PPO,Y,Y,2,John Smith,45,43.0,14.0,,,,Full-Time,,Information Technology,,N,N,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Desk,Urban,Good,N,None,Medium ($50K–$100K),Low,Active,Rare,N,N,POS,0.8793909,\"Medium income, good health - PPO for balanced cost and flexibility.\",\"[\\'POS\\', \\'HMO\\', \\'PPO\\']\",\"[0.8793909, 0.*********, 0.*********]\",\"[\\'Dental\\', \\'Vision\\']\",1.0,Dental: Essential oral health coverage for all employees.; Vision: Eye care coverage for all employees.,\"[\\'Employee Assistance\\', \\'Wellness Programs\\', \\'FSA\\']\",\"[0.5, 0.5, 0.5]\"\\r\\nE002,Charlie,Johnson,Male,1978-02-28,456 Oak Ave,Somewhere,TX,75001,Married,Employee,78500,Y,Y,Y,Female,1980-08-15,Spouse,HDHP,Y,Y,Bob Johnson,2005-07-25,Male,Child,HMO,N,Y,Diana Johnson,1980-08-15,Female,Spouse,HDHP,Y,Y,2,Charlie Johnson,47,19.0,44.0,,,,Full-Time,,Engineering,,N,N,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Desk,Urban,Fair,N,None,High (>$100K),Low,Moderate,Occasional,Y,N,HDHP + HSA,0.********,Middle age with dependents - PPO for family coverage.,\"[\\'HDHP + HSA\\', \\'PPO\\', \\'POS\\']\",\"[0.********, 0.1545966, 0.********]\",\"[\\'Dental\\', \\'Vision\\', \\'Accident\\', \\'Term Life\\', \\'STD\\', \\'LTD\\', \\'FSA\\']\",1.0,Dental: Essential oral health coverage for all employees.; Vision: Eye care coverage for all employees.; Term Life: Essential protection for 2 dependents.; STD: Income protection for High (>$100K) earners or fair health status.; LTD: Long-term income protection for High (>$100K) earners or age 47+.; FSA: Tax-advantaged account for medical expenses due to health conditions or pregnancy.,\"[\\'Employee Assistance\\', \\'Wellness Programs\\', \\'FSA\\']\",\"[0.5, 0.5, 0.5]\"\\r\\nE003,Grace,Wilson,Female,1985-11-05,456 Oak Ave,Somewhere,TX,75001,Not Married,Employee,45000,Y,Y,N,Male,2012-04-18,Child,EPO,Y,N,Henry Wilson,2012-04-18,Male,Child,EPO,Y,N,,,,,,,,1,Grace Wilson,39,13.0,,,,,Full-Time,,Engineering,,N,N,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Desk,Urban,Fair,Y,Regular,Medium ($50K–$100K),Medium,Active,Occasional,N,N,PPO,0.********,\"Medium income, urban area - EPO for network-based care.\",\"[\\'PPO\\', \\'POS\\', \\'HMO\\']\",\"[0.********, 0.********, 0.********]\",\"[\\'Dental\\', \\'Vision\\', \\'Accident\\', \\'Critical Illness\\', \\'STD\\']\",1.0,Dental: Essential oral health coverage for all employees.; Vision: Eye care coverage for all employees.; Accident: Recommended for active lifestyle and occasional travel frequency.; Critical Illness: Important protection for age 39 with fair health status.; STD: Income protection for Medium ($50K–$100K) earners or fair health status.,\"[\\'Employee Assistance\\', \\'Wellness Programs\\', \\'FSA\\']\",\"[0.5, 0.5, 0.5]\"\\r\\nE004,Isaac,Brown,Male,1990-06-30,789 Pine Rd,Elsewhere,FL,33101,Married,Employee,95000,Y,Y,Y,Male,2015-03-22,Child,PPO,Y,Y,Lisa Brown,1992-12-01,Female,Spouse,PPO,Y,Y,Tom Brown,2015-03-22,Male,Child,PPO,Y,Y,2,Isaac Brown,35,32.0,10.0,,,,Full-Time,,Information Technology,,N,N,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Desk,Urban,Excellent,N,None,High (>$100K),Medium,Active,Occasional,Y,N,HDHP + HSA,0.5499488,\"High income, good health, HSA familiar, no chronic conditions - HDHP + HSA for tax benefits.\",\"[\\'HDHP + HSA\\', \\'HDHP\\', \\'POS\\']\",\"[0.5499488, 0.3317793, 0.07842726]\",\"[\\'Dental\\', \\'Vision\\', \\'Term Life\\', \\'LTD\\', \\'Wellness Programs\\']\",1.0,Dental: Essential oral health coverage for all employees.; Vision: Eye care coverage for all employees.; Term Life: Essential protection for 2 dependents.; LTD: Long-term income protection for High (>$100K) earners or age 35+.,\"[\\'Employee Assistance\\', \\'Wellness Programs\\', \\'FSA\\']\",\"[0.5, 0.5, 0.5]\"\\r\\nE005,Nancy,Davis,Female,1987-07-12,789 Pine Rd,Elsewhere,FL,33101,Married,Employee,72000,Y,Y,Y,Female,2013-08-10,Child,HDHP,Y,Y,Paul Davis,1985-04-20,Male,Spouse,HDHP,Y,Y,Emma Davis,2013-08-10,Female,Child,HDHP,Y,Y,2,Nancy Davis,38,40.0,11.0,,,,Full-Time,,Finance,,N,Y,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Desk,Urban,Good,N,None,Medium ($50K–$100K),Medium,Active,Occasional,N,N,HMO,0.7060442,\"Medium income, good health - PPO for balanced cost and flexibility.\",\"[\\'HMO\\', \\'POS\\', \\'PPO\\']\",\"[0.7060442, 0.2778735, 0.008555227]\",\"[\\'Dental\\', \\'Vision\\', \\'Hospital Indemnity\\', \\'STD\\']\",1.0,Dental: Essential oral health coverage for all employees.; Vision: Eye care coverage for all employees.; STD: Income protection for Medium ($50K–$100K) earners or good health status.,\"[\\'Employee Assistance\\', \\'Wellness Programs\\', \\'FSA\\']\",\"[0.5, 0.5, 0.5]\"\\r\\nE006,Quinn,Martinez,Male,1979-10-17,321 Elm St,Nowhere,NY,10001,Not Married,Employee,58000,Y,Y,Y,Female,2010-03-14,Child,PPO,Y,Y,Alex Martinez,2007-12-25,Male,Child,PPO,Y,Y,Zoe Martinez,2010-03-14,Female,Child,PPO,Y,Y,2,Quinn Martinez,45,17.0,15.0,,,,Full-Time,,Sales,,N,N,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Desk,Urban,Fair,N,None,Medium ($50K–$100K),Low,Moderate,Frequent,N,N,POS,0.4979183,\"Medium income, urban area - EPO for network-based care.\",\"[\\'POS\\', \\'PPO\\', \\'HMO\\']\",\"[0.4979183, 0.44675368, 0.034974735]\",\"[\\'Dental\\', \\'Vision\\']\",1.0,Dental: Essential oral health coverage for all employees.; Vision: Eye care coverage for all employees.,\"[\\'Employee Assistance\\', \\'Wellness Programs\\', \\'FSA\\']\",\"[0.5, 0.5, 0.5]\"\\r\\nE007,Tina,Garcia,Female,1981-03-24,321 Elm St,Nowhere,NY,10001,Married,Employee,67000,Y,Y,Y,Female,2009-11-30,Child,HMO,Y,Y,Carlos Garcia,1979-07-08,Male,Spouse,HMO,Y,Y,Maya Garcia,2009-11-30,Female,Child,HMO,Y,Y,2,Tina Garcia,44,46.0,15.0,,,,Full-Time,,Sales,,N,N,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Desk,Urban,Good,N,None,Medium ($50K–$100K),Low,Moderate,Frequent,Y,N,HDHP + HSA,0.73374826,\"Medium income, good health - PPO for balanced cost and flexibility.\",\"[\\'HDHP + HSA\\', \\'POS\\', \\'HMO\\']\",\"[0.73374826, 0.24187718, 0.00961504]\",\"[\\'Dental\\', \\'Vision\\']\",1.0,Dental: Essential oral health coverage for all employees.; Vision: Eye care coverage for all employees.,\"[\\'Employee Assistance\\', \\'Wellness Programs\\', \\'FSA\\']\",\"[0.5, 0.5, 0.5]\"\\r\\nE008,Xavier,Rodriguez,Male,1986-09-09,987 Cedar Ln,Someplace,OR,97001,Not Married,Employee,41000,Y,N,N,,,,,,,,,,,,,,,,,,,,,0,Xavier Rodriguez,38,,,,,,Part-Time,,Finance,,N,N,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Desk,Urban,Fair,N,None,Medium ($50K–$100K),Medium,Active,Rare,N,N,PPO,0.6204762,\"Medium income, urban area - EPO for network-based care.\",\"[\\'PPO\\', \\'POS\\', \\'HMO\\']\",\"[0.6204762, 0.16480184, 0.09309252]\",[],0.0,ML model prediction,\"[\\'Employee Assistance\\', \\'Wellness Programs\\', \\'FSA\\']\",\"[0.5, 0.5, 0.5]\"\\r\\nE009,Yvonne,Lee,Female,1984-12-18,987 Cedar Ln,Someplace,OR,97001,Married,Employee,63000,Y,Y,Y,Male,1982-02-14,Spouse,PPO,Y,Y,David Lee,1982-02-14,Male,Spouse,PPO,Y,Y,,,,,,,,1,Yvonne Lee,40,43.0,,,,,Full-Time,,Sales,,N,N,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Desk,Suburban,Fair,Y,Regular,Medium ($50K–$100K),Low,Moderate,Frequent,Y,N,HDHP + HSA,0.7251465,Middle age with dependents - PPO for family coverage.,\"[\\'HDHP + HSA\\', \\'PPO\\', \\'POS\\']\",\"[0.7251465, 0.21620469, 0.049998656]\",\"[\\'Dental\\', \\'Vision\\', \\'Critical Illness\\']\",1.0,Dental: Essential oral health coverage for all employees.; Vision: Eye care coverage for all employees.; Critical Illness: Important protection for age 40 with fair health status.,\"[\\'Employee Assistance\\', \\'Wellness Programs\\', \\'FSA\\']\",\"[0.5, 0.5, 0.5]\"\\r\\nE010,Aaron,White,Male,1977-04-07,,Yourtown,ID,83001,Not Married,Employee,49000,Y,Y,Y,,,,,,,,,,,,,,,,,,,,,0,Aaron White,48,,,,,,Full-Time,,Manufacturing,,N,N,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Desk,Rural,Good,Y,Regular,Medium ($50K–$100K),Medium,Moderate,Rare,Y,Y,HDHP + HSA,0.8802786,\"Medium income, good health - PPO for balanced cost and flexibility.\",\"[\\'HDHP + HSA\\', \\'PPO\\', \\'POS\\']\",\"[0.8802786, 0.0966078, 0.01192658]\",\"[\\'Accident\\', \\'Critical Illness\\', \\'STD\\', \\'Employee Assistance\\']\",1.0,Critical Illness: Important protection for age 48 with good health status.; STD: Income protection for Medium ($50K–$100K) earners or good health status.,\"[\\'Employee Assistance\\', \\'Wellness Programs\\', \\'FSA\\']\",\"[0.5, 0.5, 0.5]\"\\r\\nE011,Kelly,White,Female,2006-05-22,,Yourtown,ID,83001,Not Married,Child,0,Y,Y,Y,,,,,,,,,,,,,,,,,,,,,0,Kelly White,19,,,,,,Full-Time,,Engineering,,Y,N,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Desk,Rural,Excellent,N,Occasional,Low (<$50K),Low,Active,Occasional,N,N,HMO,0.68080395,\"Low income, young age - DHMO for basic dental coverage.\",\"[\\'HMO\\', \\'PPO\\', \\'HDHP\\']\",\"[0.68080395, 0.16520661, 0.06760093]\",\"[\\'Accident\\', \\'STD\\']\",1.0,Accident: Recommended for active lifestyle and occasional travel frequency.,\"[\\'Employee Assistance\\', \\'Wellness Programs\\', \\'FSA\\']\",\"[0.5, 0.5, 0.5]\"\\r\\nE012,Ryan,White,Male,2009-10-11,,Yourtown,ID,83001,Not Married,Child,0,Y,Y,Y,,,,,,,,,,,,,,,,,,,,,0,Ryan White,15,,,,,,Full-Time,,Finance,,Y,N,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Desk,Rural,Good,N,None,Medium ($50K–$100K),Low,Moderate,Occasional,N,N,HDHP,0.47879255,\"Medium income, good health - PPO for balanced cost and flexibility.\",\"[\\'HDHP\\', \\'HMO\\', \\'PPO\\']\",\"[0.47879255, 0.20883918, 0.14838484]\",\"[\\'Dental\\', \\'Vision\\']\",0.5,Default benefits assignment due to prediction error,\"[\\'Dental\\', \\'Vision\\', \\'Hospital Indemnity\\']\",\"[0.5, 0.5, 0.5]\"\\r\\nE013,Brooke,Harris,Female,1989-08-21,,Mytown,WY,82001,Not Married,Employee,35000,Y,N,N,,,,,,,,,,,,,,,,,,,,,0,Brooke Harris,35,,,,,,Part-Time,,Finance,,N,N,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Desk,Rural,Good,Y,Regular,Low (<$50K),Low,Active,Rare,N,N,EPO,0.9950523,\"Low income, part-time employment - MEC for minimum coverage compliance.\",\"[\\'EPO\\', \\'PPO\\', \\'HMO\\']\",\"[0.9950523, 0.**********, 0.**********]\",\"[\\'Dental\\', \\'Vision\\']\",0.5,Default benefits assignment due to prediction error,\"[\\'Dental\\', \\'Vision\\', \\'Hospital Indemnity\\']\",\"[0.5, 0.5, 0.5]\"\\r\\nE014,Connor,Clark,Male,1982-01-26,,Ourtown,ND,58001,Married,Employee,85000,Y,Y,Y,,,,,,,,,,,,,,,,,,,,,0,Connor Clark,43,,,,,,Full-Time,,Engineering,,N,N,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Desk,Rural,Good,N,None,High (>$100K),Low,Moderate,Occasional,Y,N,HDHP + HSA,0.909899,\"High income, good health, HSA familiar, no chronic conditions - HDHP + HSA for tax benefits.\",\"[\\'HDHP + HSA\\', \\'POS\\', \\'PPO\\']\",\"[0.909899, 0.04981334, 0.*********]\",\"[\\'Dental\\', \\'Vision\\']\",0.5,Default benefits assignment due to prediction error,\"[\\'Dental\\', \\'Vision\\', \\'Hospital Indemnity\\']\",\"[0.5, 0.5, 0.5]\"\\r\\nE015,Rachel,Clark,Female,1984-06-18,,Ourtown,ND,58001,Married,Spouse,0,Y,Y,Y,,,,,,,,,,,,,,,,,,,,,0,Rachel Clark,41,,,,,,Full-Time,,Information Technology,,N,N,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Desk,Rural,Excellent,N,None,Medium ($50K–$100K),Medium,Moderate,Rare,Y,N,HDHP + HSA,0.93751526,Middle age with dependents - PPO for family coverage.,\"[\\'HDHP + HSA\\', \\'POS\\', \\'PPO\\']\",\"[0.93751526, 0.*********, 0.*********]\",\"[\\'Dental\\', \\'Vision\\']\",0.5,Default benefits assignment due to prediction error,\"[\\'Dental\\', \\'Vision\\', \\'Hospital Indemnity\\']\",\"[0.5, 0.5, 0.5]\"\\r\\nE016,Jake,Clark,Male,2012-01-05,,Ourtown,ND,58001,Not Married,Child,0,Y,Y,Y,,,,,,,,,,,,,,,,,,,,,0,Jake Clark,13,,,,,,Full-Time,,Manufacturing,,N,N,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Desk,Rural,Good,Y,Regular,Medium ($50K–$100K),Low,Moderate,Rare,Y,N,HDHP + HSA,0.88035166,\"Medium income, good health - PPO for balanced cost and flexibility.\",\"[\\'HDHP + HSA\\', \\'PPO\\', \\'HDHP\\']\",\"[0.88035166, 0.08738911, 0.02090504]\",\"[\\'Dental\\', \\'Vision\\']\",0.5,Default benefits assignment due to prediction error,\"[\\'Dental\\', \\'Vision\\', \\'Hospital Indemnity\\']\",\"[0.5, 0.5, 0.5]\"\\r\\nE017,Lily,Clark,Female,2014-07-12,,Ourtown,ND,58001,Not Married,Child,0,Y,Y,Y,,,,,,,,,,,,,,,,,,,,,0,Lily Clark,11,,,,,,Full-Time,,Engineering,,N,N,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Desk,Rural,Good,N,None,Low (<$50K),Medium,Moderate,Occasional,N,N,HMO,0.84568,\"Low income, young age - DHMO for basic dental coverage.\",\"[\\'HMO\\', \\'PPO\\', \\'HDHP\\']\",\"[0.84568, 0.10174242, 0.019797586]\",\"[\\'Dental\\', \\'Vision\\']\",0.5,Default benefits assignment due to prediction error,\"[\\'Dental\\', \\'Vision\\', \\'Hospital Indemnity\\']\",\"[0.5, 0.5, 0.5]\"\\r\\nE018,Destiny,Lewis,Female,1986-05-13,,Theirtown,SD,57001,Not Married,Employee,44000,Y,Y,Y,,,,,,,,,,,,,,,,,,,,,0,Destiny Lewis,39,,,,,,Full-Time,,Manufacturing,,N,N,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Desk,Rural,Good,N,None,Medium ($50K–$100K),Medium,Moderate,Rare,N,Y,POS,0.48443404,\"Medium income, good health - PPO for balanced cost and flexibility.\",\"[\\'POS\\', \\'HMO\\', \\'PPO\\']\",\"[0.48443404, 0.2011655, 0.13652067]\",\"[\\'Dental\\', \\'Vision\\']\",0.5,Default benefits assignment due to prediction error,\"[\\'Dental\\', \\'Vision\\', \\'Hospital Indemnity\\']\",\"[0.5, 0.5, 0.5]\"\\r\\nE019,Noah,Lewis,Male,2011-09-28,,Theirtown,SD,57001,Not Married,Child,0,Y,Y,Y,,,,,,,,,,,,,,,,,,,,,0,Noah Lewis,13,,,,,,Part-Time,,Manufacturing,,Y,N,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Desk,Rural,Good,N,None,Medium ($50K–$100K),Medium,Moderate,Rare,Y,N,HDHP + HSA,0.7787332,\"Medium income, good health - PPO for balanced cost and flexibility.\",\"[\\'HDHP + HSA\\', \\'HDHP\\', \\'HMO\\']\",\"[0.7787332, 0.19619194, 0.00963435]\",\"[\\'Dental\\', \\'Vision\\']\",0.5,Default benefits assignment due to prediction error,\"[\\'Dental\\', \\'Vision\\', \\'Hospital Indemnity\\']\",\"[0.5, 0.5, 0.5]\"\\r\\n', 'comprehensive_statistics': {'basic_demographics': {'age_statistics': {'average_age': 34.10526315789474, 'median_age': 39.0, 'age_range': {'min': 11, 'max': 48}, 'age_distribution': {'18-30': 1, '31-45': 12, '46-60': 2, '60+': 0}}, 'gender_composition': {'counts': {'Male': 10, 'Female': 9}, 'percentages': {'Male': 52.63, 'Female': 47.37}}, 'marital_status_distribution': {'counts': {'Not Married': 11, 'Married': 8}, 'percentages': {'Not Married': 57.89, 'Married': 42.11}}}, 'dependent_analysis': {'dependent_count_distribution': {'average_dependents': 0.7368421052631579, 'median_dependents': 0.0, 'distribution': {0: 11, 2: 6, 1: 2}, 'employees_with_dependents': 8, 'percentage_with_dependents': 42.11}, 'dependent_age_analysis': {'total_dependents_found': 14, 'average_dependent_age': 25.86, 'median_dependent_age': 18.0, 'dependents_over_26_as_children': 0, 'age_distribution': {'0-5': 0, '6-12': 2, '13-18': 5, '19-26': 1, '26+': 6}}}, 'employment_demographics': {'department_distribution': {'counts': {'Engineering': 5, 'Finance': 4, 'Manufacturing': 4, 'Information Technology': 3, 'Sales': 3}, 'percentages': {'Engineering': 26.32, 'Finance': 21.05, 'Manufacturing': 21.05, 'Information Technology': 15.79, 'Sales': 15.79}}, 'employment_type_distribution': {'counts': {'Full-Time': 16, 'Part-Time': 3}, 'percentages': {'Full-Time': 84.21, 'Part-Time': 15.79}}, 'job_type_distribution': {'counts': {'Desk': 19}, 'percentages': {'Desk': 100.0}}, 'employee_class_distribution': {'counts': {}, 'percentages': {}}, 'salary_analysis': {'average_salary': 41973.68, 'median_salary': 45000.0, 'salary_range': {'min': 0, 'max': 95000}, 'salary_distribution': {'under_40k': 7, '40k_75k': 9, '75k_100k': 3, 'over_100k': 0}}}, 'health_and_lifestyle': {'health_condition_distribution': {'counts': {'Good': 11, 'Fair': 5, 'Excellent': 3}, 'percentages': {'Good': 57.89, 'Fair': 26.32, 'Excellent': 15.79}}, 'chronic_condition_distribution': {'counts': {'N': 14, 'Y': 5}, 'percentages': {'N': 73.68, 'Y': 26.32}}, 'tobacco_use_distribution': {'counts': {'N': 16, 'Y': 3}, 'percentages': {'N': 84.21, 'Y': 15.79}}, 'lifestyle_distribution': {'counts': {'Moderate': 12, 'Active': 7}, 'percentages': {'Moderate': 63.16, 'Active': 36.84}}, 'prescription_use_distribution': {'counts': {'None': 13, 'Regular': 5, 'Occasional': 1}, 'percentages': {'None': 68.42, 'Regular': 26.32, 'Occasional': 5.26}}}, 'coverage_analysis': {'coverage_tier_distribution': {'counts': {}, 'percentages': {}}, 'coverage_tier_by_family_size': {}, 'medical_plan_distribution': {'counts': {'Y': 19}, 'percentages': {'Y': 100.0}}, 'dental_plan_distribution': {'counts': {'Y': 17, 'N': 2}, 'percentages': {'Y': 89.47, 'N': 10.53}}, 'vision_plan_distribution': {'counts': {'Y': 16, 'N': 3}, 'percentages': {'Y': 84.21, 'N': 15.79}}, 'life_plan_distribution': {'counts': {}, 'percentages': {}}}, 'geographic_distribution': {'state_distribution': {'counts': {'ND': 4, 'ID': 3, 'TX': 2, 'FL': 2, 'NY': 2, 'OR': 2, 'SD': 2, 'CA': 1, 'WY': 1}, 'percentages': {'ND': 21.05, 'ID': 15.79, 'TX': 10.53, 'FL': 10.53, 'NY': 10.53, 'OR': 10.53, 'SD': 10.53, 'CA': 5.26, 'WY': 5.26}}, 'region_distribution': {'counts': {'Rural': 10, 'Urban': 8, 'Suburban': 1}, 'percentages': {'Rural': 52.63, 'Urban': 42.11, 'Suburban': 5.26}}, 'top_cities': {'counts': {'Ourtown': 4, 'Yourtown': 3, 'Somewhere': 2, 'Elsewhere': 2, 'Nowhere': 2, 'Someplace': 2, 'Theirtown': 2, 'Anytown': 1, 'Mytown': 1}, 'percentages': {'Ourtown': 21.05, 'Yourtown': 15.79, 'Somewhere': 10.53, 'Elsewhere': 10.53, 'Nowhere': 10.53, 'Someplace': 10.53, 'Theirtown': 10.53, 'Anytown': 5.26, 'Mytown': 5.26}}}, 'financial_demographics': {'income_tier_distribution': {'counts': {'Medium ($50K–$100K)': 13, 'High (>$100K)': 3, 'Low (<$50K)': 3}, 'percentages': {'Medium ($50K–$100K)': 68.42, 'High (>$100K)': 15.79, 'Low (<$50K)': 15.79}}, 'risk_tolerance_distribution': {'counts': {'Low': 10, 'Medium': 9}, 'percentages': {'Low': 52.63, 'Medium': 47.37}}, 'hsa_familiarity_distribution': {'counts': {'N': 10, 'Y': 9}, 'percentages': {'N': 52.63, 'Y': 47.37}}}, 'risk_assessment': {'group_risk_score': 27.16, 'group_risk_level': 'Low', 'risk_distribution': {'low_risk': 12, 'medium_risk': 7, 'high_risk': 0}, 'risk_statistics': {'min_risk_score': 10, 'max_risk_score': 53, 'median_risk_score': 25.0, 'std_risk_score': 11.13}, 'top_risk_factors': {'Poor Health Condition': 5, 'Chronic Conditions': 5, 'Tobacco Use': 3}, 'individual_risks': [{'employee_id': 'E001', 'risk_score': 20, 'risk_level': 'Low', 'risk_factors': []}, {'employee_id': 'E002', 'risk_score': 33, 'risk_level': 'Medium', 'risk_factors': ['Health condition: Fair']}, {'employee_id': 'E003', 'risk_score': 45, 'risk_level': 'Medium', 'risk_factors': ['Health condition: Fair', 'Has chronic condition']}, {'employee_id': 'E004', 'risk_score': 10, 'risk_level': 'Low', 'risk_factors': []}, {'employee_id': 'E005', 'risk_score': 15, 'risk_level': 'Low', 'risk_factors': []}, {'employee_id': 'E006', 'risk_score': 33, 'risk_level': 'Medium', 'risk_factors': ['Health condition: Fair']}, {'employee_id': 'E007', 'risk_score': 23, 'risk_level': 'Low', 'risk_factors': []}, {'employee_id': 'E008', 'risk_score': 25, 'risk_level': 'Low', 'risk_factors': ['Health condition: Fair']}, {'employee_id': 'E009', 'risk_score': 53, 'risk_level': 'Medium', 'risk_factors': ['Health condition: Fair', 'Has chronic condition']}, {'employee_id': 'E010', 'risk_score': 43, 'risk_level': 'Medium', 'risk_factors': ['Has chronic condition']}]}, 'data_quality_metrics': {'overall_completeness': {'total_cells': 2926, 'missing_cells': 2149, 'completeness_percentage': 26.56}, 'column_completeness': {'employee_id': {'missing_count': 0, 'completeness_percentage': 100.0}, 'first_name': {'missing_count': 0, 'completeness_percentage': 100.0}, 'last_name': {'missing_count': 0, 'completeness_percentage': 100.0}, 'gender': {'missing_count': 0, 'completeness_percentage': 100.0}, 'dob': {'missing_count': 0, 'completeness_percentage': 100.0}, 'address1': {'missing_count': 10, 'completeness_percentage': 47.37}, 'city': {'missing_count': 0, 'completeness_percentage': 100.0}, 'state': {'missing_count': 0, 'completeness_percentage': 100.0}, 'zipcode': {'missing_count': 0, 'completeness_percentage': 100.0}, 'marital_status': {'missing_count': 0, 'completeness_percentage': 100.0}, 'relationship': {'missing_count': 0, 'completeness_percentage': 100.0}, 'salary': {'missing_count': 0, 'completeness_percentage': 100.0}, 'medical_plan': {'missing_count': 0, 'completeness_percentage': 100.0}, 'dental_plan': {'missing_count': 0, 'completeness_percentage': 100.0}, 'vision_plan': {'missing_count': 0, 'completeness_percentage': 100.0}, 'dept_i_gender': {'missing_count': 11, 'completeness_percentage': 42.11}, 'dept_i_dob': {'missing_count': 11, 'completeness_percentage': 42.11}, 'relationship_type_i': {'missing_count': 11, 'completeness_percentage': 42.11}, 'dept_i_medical_plan': {'missing_count': 11, 'completeness_percentage': 42.11}, 'dept_i_dental_plan': {'missing_count': 11, 'completeness_percentage': 42.11}, 'dept_i_vision_plan': {'missing_count': 11, 'completeness_percentage': 42.11}, 'dept_1': {'missing_count': 11, 'completeness_percentage': 42.11}, 'dept_1_dob': {'missing_count': 11, 'completeness_percentage': 42.11}, 'dept_1_gender': {'missing_count': 11, 'completeness_percentage': 42.11}, 'relationship_type_1': {'missing_count': 11, 'completeness_percentage': 42.11}, 'dept_1_medical_plan': {'missing_count': 11, 'completeness_percentage': 42.11}, 'dept_1_dental_plan': {'missing_count': 11, 'completeness_percentage': 42.11}, 'dept_1_vision_plan': {'missing_count': 11, 'completeness_percentage': 42.11}, 'dept_2': {'missing_count': 13, 'completeness_percentage': 31.58}, 'dept_2_dob': {'missing_count': 13, 'completeness_percentage': 31.58}, 'dept_2_gender': {'missing_count': 13, 'completeness_percentage': 31.58}, 'relationship_type_2': {'missing_count': 13, 'completeness_percentage': 31.58}, 'dept_2_medical_plan': {'missing_count': 13, 'completeness_percentage': 31.58}, 'dept_2_dental_plan': {'missing_count': 13, 'completeness_percentage': 31.58}, 'dept_2_vision_plan': {'missing_count': 13, 'completeness_percentage': 31.58}, 'dept_count': {'missing_count': 0, 'completeness_percentage': 100.0}, 'name': {'missing_count': 0, 'completeness_percentage': 100.0}, 'age': {'missing_count': 0, 'completeness_percentage': 100.0}, 'dept_1_age': {'missing_count': 11, 'completeness_percentage': 42.11}, 'dept_2_age': {'missing_count': 13, 'completeness_percentage': 31.58}, 'middle_name': {'missing_count': 19, 'completeness_percentage': 0.0}, 'address2': {'missing_count': 19, 'completeness_percentage': 0.0}, 'record_type': {'missing_count': 19, 'completeness_percentage': 0.0}, 'employment_type': {'missing_count': 0, 'completeness_percentage': 100.0}, 'employee_class': {'missing_count': 19, 'completeness_percentage': 0.0}, 'department': {'missing_count': 0, 'completeness_percentage': 100.0}, 'hire_date': {'missing_count': 19, 'completeness_percentage': 0.0}, 'tobacco_use': {'missing_count': 0, 'completeness_percentage': 100.0}, 'pregnancy_status': {'missing_count': 0, 'completeness_percentage': 100.0}, 'life_plan': {'missing_count': 19, 'completeness_percentage': 0.0}, 'add_plan': {'missing_count': 19, 'completeness_percentage': 0.0}, 'coverage_tier': {'missing_count': 19, 'completeness_percentage': 0.0}, 'ssn': {'missing_count': 19, 'completeness_percentage': 0.0}, 'dept_3': {'missing_count': 19, 'completeness_percentage': 0.0}, 'dept_3_dob': {'missing_count': 19, 'completeness_percentage': 0.0}, 'dept_3_age': {'missing_count': 19, 'completeness_percentage': 0.0}, 'dept_3_gender': {'missing_count': 19, 'completeness_percentage': 0.0}, 'relationship_type_3': {'missing_count': 19, 'completeness_percentage': 0.0}, 'dept_4': {'missing_count': 19, 'completeness_percentage': 0.0}, 'dept_4_dob': {'missing_count': 19, 'completeness_percentage': 0.0}, 'dept_4_age': {'missing_count': 19, 'completeness_percentage': 0.0}, 'dept_4_gender': {'missing_count': 19, 'completeness_percentage': 0.0}, 'relationship_type_4': {'missing_count': 19, 'completeness_percentage': 0.0}, 'dept_5': {'missing_count': 19, 'completeness_percentage': 0.0}, 'dept_5_dob': {'missing_count': 19, 'completeness_percentage': 0.0}, 'dept_5_age': {'missing_count': 19, 'completeness_percentage': 0.0}, 'dept_5_gender': {'missing_count': 19, 'completeness_percentage': 0.0}, 'relationship_type_5': {'missing_count': 19, 'completeness_percentage': 0.0}, 'dept_6': {'missing_count': 19, 'completeness_percentage': 0.0}, 'dept_6_dob': {'missing_count': 19, 'completeness_percentage': 0.0}, 'dept_6_age': {'missing_count': 19, 'completeness_percentage': 0.0}, 'dept_6_gender': {'missing_count': 19, 'completeness_percentage': 0.0}, 'relationship_type_6': {'missing_count': 19, 'completeness_percentage': 0.0}, 'dept_7': {'missing_count': 19, 'completeness_percentage': 0.0}, 'dept_7_dob': {'missing_count': 19, 'completeness_percentage': 0.0}, 'dept_7_age': {'missing_count': 19, 'completeness_percentage': 0.0}, 'dept_7_gender': {'missing_count': 19, 'completeness_percentage': 0.0}, 'relationship_type_7': {'missing_count': 19, 'completeness_percentage': 0.0}, 'dept_8': {'missing_count': 19, 'completeness_percentage': 0.0}, 'dept_8_dob': {'missing_count': 19, 'completeness_percentage': 0.0}, 'dept_8_age': {'missing_count': 19, 'completeness_percentage': 0.0}, 'dept_8_gender': {'missing_count': 19, 'completeness_percentage': 0.0}, 'relationship_type_8': {'missing_count': 19, 'completeness_percentage': 0.0}, 'dept_9': {'missing_count': 19, 'completeness_percentage': 0.0}, 'dept_9_dob': {'missing_count': 19, 'completeness_percentage': 0.0}, 'dept_9_age': {'missing_count': 19, 'completeness_percentage': 0.0}, 'dept_9_gender': {'missing_count': 19, 'completeness_percentage': 0.0}, 'relationship_type_9': {'missing_count': 19, 'completeness_percentage': 0.0}, 'dept_10': {'missing_count': 19, 'completeness_percentage': 0.0}, 'dept_10_dob': {'missing_count': 19, 'completeness_percentage': 0.0}, 'dept_10_age': {'missing_count': 19, 'completeness_percentage': 0.0}, 'dept_10_gender': {'missing_count': 19, 'completeness_percentage': 0.0}, 'relationship_type_10': {'missing_count': 19, 'completeness_percentage': 0.0}, 'dept_11': {'missing_count': 19, 'completeness_percentage': 0.0}, 'dept_11_dob': {'missing_count': 19, 'completeness_percentage': 0.0}, 'dept_11_age': {'missing_count': 19, 'completeness_percentage': 0.0}, 'dept_11_gender': {'missing_count': 19, 'completeness_percentage': 0.0}, 'relationship_type_11': {'missing_count': 19, 'completeness_percentage': 0.0}, 'dept_12': {'missing_count': 19, 'completeness_percentage': 0.0}, 'dept_12_dob': {'missing_count': 19, 'completeness_percentage': 0.0}, 'dept_12_age': {'missing_count': 19, 'completeness_percentage': 0.0}, 'dept_12_gender': {'missing_count': 19, 'completeness_percentage': 0.0}, 'relationship_type_12': {'missing_count': 19, 'completeness_percentage': 0.0}, 'dept_13': {'missing_count': 19, 'completeness_percentage': 0.0}, 'dept_13_dob': {'missing_count': 19, 'completeness_percentage': 0.0}, 'dept_13_age': {'missing_count': 19, 'completeness_percentage': 0.0}, 'dept_13_gender': {'missing_count': 19, 'completeness_percentage': 0.0}, 'relationship_type_13': {'missing_count': 19, 'completeness_percentage': 0.0}, 'dept_14': {'missing_count': 19, 'completeness_percentage': 0.0}, 'dept_14_dob': {'missing_count': 19, 'completeness_percentage': 0.0}, 'dept_14_age': {'missing_count': 19, 'completeness_percentage': 0.0}, 'dept_14_gender': {'missing_count': 19, 'completeness_percentage': 0.0}, 'relationship_type_14': {'missing_count': 19, 'completeness_percentage': 0.0}, 'dept_15': {'missing_count': 19, 'completeness_percentage': 0.0}, 'dept_15_dob': {'missing_count': 19, 'completeness_percentage': 0.0}, 'dept_15_age': {'missing_count': 19, 'completeness_percentage': 0.0}, 'dept_15_gender': {'missing_count': 19, 'completeness_percentage': 0.0}, 'relationship_type_15': {'missing_count': 19, 'completeness_percentage': 0.0}, 'dept_16': {'missing_count': 19, 'completeness_percentage': 0.0}, 'dept_16_dob': {'missing_count': 19, 'completeness_percentage': 0.0}, 'dept_16_age': {'missing_count': 19, 'completeness_percentage': 0.0}, 'dept_16_gender': {'missing_count': 19, 'completeness_percentage': 0.0}, 'relationship_type_16': {'missing_count': 19, 'completeness_percentage': 0.0}, 'dept_17': {'missing_count': 19, 'completeness_percentage': 0.0}, 'dept_17_dob': {'missing_count': 19, 'completeness_percentage': 0.0}, 'dept_17_age': {'missing_count': 19, 'completeness_percentage': 0.0}, 'dept_17_gender': {'missing_count': 19, 'completeness_percentage': 0.0}, 'relationship_type_17': {'missing_count': 19, 'completeness_percentage': 0.0}, 'dept_18': {'missing_count': 19, 'completeness_percentage': 0.0}, 'dept_18_dob': {'missing_count': 19, 'completeness_percentage': 0.0}, 'dept_18_age': {'missing_count': 19, 'completeness_percentage': 0.0}, 'dept_18_gender': {'missing_count': 19, 'completeness_percentage': 0.0}, 'relationship_type_18': {'missing_count': 19, 'completeness_percentage': 0.0}, 'dept_19': {'missing_count': 19, 'completeness_percentage': 0.0}, 'dept_19_dob': {'missing_count': 19, 'completeness_percentage': 0.0}, 'dept_19_age': {'missing_count': 19, 'completeness_percentage': 0.0}, 'dept_19_gender': {'missing_count': 19, 'completeness_percentage': 0.0}, 'relationship_type_19': {'missing_count': 19, 'completeness_percentage': 0.0}, 'dept_20': {'missing_count': 19, 'completeness_percentage': 0.0}, 'dept_20_dob': {'missing_count': 19, 'completeness_percentage': 0.0}, 'dept_20_age': {'missing_count': 19, 'completeness_percentage': 0.0}, 'dept_20_gender': {'missing_count': 19, 'completeness_percentage': 0.0}, 'relationship_type_20': {'missing_count': 19, 'completeness_percentage': 0.0}, 'job_type': {'missing_count': 0, 'completeness_percentage': 100.0}, 'region': {'missing_count': 0, 'completeness_percentage': 100.0}, 'health_condition': {'missing_count': 0, 'completeness_percentage': 100.0}, 'chronic_condition': {'missing_count': 0, 'completeness_percentage': 100.0}, 'prescription_use': {'missing_count': 0, 'completeness_percentage': 100.0}, 'income_tier': {'missing_count': 0, 'completeness_percentage': 100.0}, 'risk_tolerance': {'missing_count': 0, 'completeness_percentage': 100.0}, 'lifestyle': {'missing_count': 0, 'completeness_percentage': 100.0}, 'travel_frequency': {'missing_count': 0, 'completeness_percentage': 100.0}, 'hsa_familiarity': {'missing_count': 0, 'completeness_percentage': 100.0}, 'mental_health_needs': {'missing_count': 0, 'completeness_percentage': 100.0}}, 'critical_field_completeness': {'name': {'missing_count': 0, 'completeness_percentage': 100.0}, 'gender': {'missing_count': 0, 'completeness_percentage': 100.0}, 'zipcode': {'missing_count': 0, 'completeness_percentage': 100.0}, 'marital_status': {'missing_count': 0, 'completeness_percentage': 100.0}}}}, 'prediction_summary': {'total_employees': 19, 'plan_type_distribution': {'successful_predictions': {'HDHP + HSA': 9, 'POS': 3, 'HMO': 3, 'PPO': 2, 'HDHP': 1, 'EPO': 1}, 'failed_predictions': '0', 'success_rate': 100.0, 'total_predicted': '19'}, 'benefits_distribution': {}, 'confidence_metrics': {'plan_confidence': {'mean': '0.743', 'min': '0.479', 'max': '0.995', 'count': 19}}, 'prediction_success_rates': {}, 'top_3_plan_probabilities': []}, 'total_employees': 19, 'total_columns': 164, 'has_predictions': True, 'prediction_columns': ['predicted_plan_type', 'plan_confidence']}", "message": "Census file processed successfully"}