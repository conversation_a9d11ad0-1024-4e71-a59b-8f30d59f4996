{"success": true, "status_code": 200, "data": "{'enriched_data_csv': 'employee_id,first_name,last_name,gender,dob,address1,city,state,zipcode,marital_status,salary,medical_plan,dental_plan,vision_plan,dept_1,dept_1_dob,dept_1_gender,relationship_type_1,dept_2,dept_2_dob,dept_2_gender,relationship_type_2,dept_3,dept_3_dob,dept_3_gender,relationship_type_3,dept_count,name,age,dept_1_age,dept_2_age,dept_3_age,middle_name,address2,relationship,record_type,employment_type,employee_class,department,hire_date,tobacco_use,pregnancy_status,life_plan,add_plan,coverage_tier,ssn,dept_4,dept_4_dob,dept_4_age,dept_4_gender,relationship_type_4,dept_5,dept_5_dob,dept_5_age,dept_5_gender,relationship_type_5,dept_6,dept_6_dob,dept_6_age,dept_6_gender,relationship_type_6,dept_7,dept_7_dob,dept_7_age,dept_7_gender,relationship_type_7,dept_8,dept_8_dob,dept_8_age,dept_8_gender,relationship_type_8,dept_9,dept_9_dob,dept_9_age,dept_9_gender,relationship_type_9,dept_10,dept_10_dob,dept_10_age,dept_10_gender,relationship_type_10,dept_11,dept_11_dob,dept_11_age,dept_11_gender,relationship_type_11,dept_12,dept_12_dob,dept_12_age,dept_12_gender,relationship_type_12,dept_13,dept_13_dob,dept_13_age,dept_13_gender,relationship_type_13,dept_14,dept_14_dob,dept_14_age,dept_14_gender,relationship_type_14,dept_15,dept_15_dob,dept_15_age,dept_15_gender,relationship_type_15,dept_16,dept_16_dob,dept_16_age,dept_16_gender,relationship_type_16,dept_17,dept_17_dob,dept_17_age,dept_17_gender,relationship_type_17,dept_18,dept_18_dob,dept_18_age,dept_18_gender,relationship_type_18,dept_19,dept_19_dob,dept_19_age,dept_19_gender,relationship_type_19,dept_20,dept_20_dob,dept_20_age,dept_20_gender,relationship_type_20,job_type,region,health_condition,chronic_condition,prescription_use,income_tier,risk_tolerance,lifestyle,travel_frequency,hsa_familiarity,mental_health_needs,predicted_plan_type,plan_confidence,plan_reason,top_3_plans,top_3_plan_confidences,predicted_benefits,benefits_confidence,benefits_reason,top_3_benefits,top_3_benefits_confidences\\r\\nE001,John,Smith,Male,1980-01-15,123 Main St,Anytown,CA,90210,Married,65000,Y,Y,Y,Jane Smith,1982-05-20,Female,Spouse,Mike Smith,2010-09-12,Male,Child,,,,,2,John Smith,45.0,43.0,14.0,,,,,,Full-Time,,Information Technology,,N,N,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Desk,Urban,Good,N,None,Medium ($50K–$100K),Medium,Moderate,Occasional,N,Y,POS,0.8885244,\"Medium income, good health - PPO for balanced cost and flexibility.\",\"[\\'POS\\', \\'HMO\\', \\'PPO\\']\",\"[0.8885244, 0.05689501, 0.*********]\",\"[\\'Dental\\', \\'Vision\\', \\'Employee Assistance\\']\",1.0,Dental: Essential oral health coverage for all employees.; Vision: Eye care coverage for all employees.,\"[\\'Employee Assistance\\', \\'Wellness Programs\\', \\'FSA\\']\",\"[0.5, 0.5, 0.5]\"\\r\\nE002,Alice,Brown,Female,1975-03-10,456 Oak Ave,Somewhere,TX,75001,Not Married,52000,Y,N,Y,Bob Brown,2005-07-25,Male,Child,,,,,,,,,1,Alice Brown,50.0,19.0,,,,,,,Full-Time,,Engineering,,Y,N,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Desk,Urban,Fair,Y,Regular,Medium ($50K–$100K),Medium,Active,Occasional,Y,N,HDHP + HSA,0.8703137,\"Medium income, urban area - EPO for network-based care.\",\"[\\'HDHP + HSA\\', \\'PPO\\', \\'POS\\']\",\"[0.8703137, 0.*********, 0.*********]\",\"[\\'Dental\\', \\'Vision\\', \\'Accident\\', \\'Critical Illness\\', \\'STD\\']\",1.0,Dental: Essential oral health coverage for all employees.; Vision: Eye care coverage for all employees.; Accident: Recommended for active lifestyle and occasional travel frequency.; Critical Illness: Important protection for age 50 with fair health status.; STD: Income protection for Medium ($50K–$100K) earners or fair health status.,\"[\\'Employee Assistance\\', \\'Wellness Programs\\', \\'FSA\\']\",\"[0.5, 0.5, 0.5]\"\\r\\nE003,Charlie,Davis,Male,1990-06-30,789 Pine Rd,Elsewhere,FL,33101,Married,78500,Y,Y,Y,Diana Davis,1992-08-15,Female,Spouse,Evan Davis,2015-12-01,Male,Child,Fiona Davis,2018-03-22,Female,Child,3,Charlie Davis,35.0,32.0,9.0,7.0,,,,,Part-Time,,Engineering,,Y,N,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Desk,Urban,Good,N,None,High (>$100K),Medium,Active,Occasional,N,N,POS,0.6304605,Middle age with dependents - PPO for family coverage.,\"[\\'POS\\', \\'PPO\\', \\'HMO\\']\",\"[0.6304605, 0.13224517, 0.11548659]\",\"[\\'Accident\\', \\'STD\\', \\'Wellness Programs\\']\",1.0,Accident: Recommended for active lifestyle and occasional travel frequency.; STD: Income protection for High (>$100K) earners or good health status.,\"[\\'Employee Assistance\\', \\'Wellness Programs\\', \\'FSA\\']\",\"[0.5, 0.5, 0.5]\"\\r\\nE004,Grace,Wilson,Female,1985-11-05,321 Elm St,Nowhere,NY,10001,Not Married,45000,Y,Y,N,Henry Wilson,2012-04-18,Male,Child,,,,,,,,,1,Grace Wilson,39.0,13.0,,,,,,,Full-Time,,Finance,,N,N,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Desk,Urban,Good,Y,Regular,Medium ($50K–$100K),Medium,Active,Rare,N,N,PPO,0.42401284,\"Medium income, good health - PPO for balanced cost and flexibility.\",\"[\\'PPO\\', \\'POS\\', \\'HMO\\']\",\"[0.42401284, 0.41290563, 0.*********]\",\"[\\'Dental\\', \\'Vision\\', \\'Critical Illness\\']\",1.0,Dental: Essential oral health coverage for all employees.; Vision: Eye care coverage for all employees.; Critical Illness: Important protection for age 39 with good health status.,\"[\\'Employee Assistance\\', \\'Wellness Programs\\', \\'FSA\\']\",\"[0.5, 0.5, 0.5]\"\\r\\nE005,Isaac,Johnson,Male,1978-02-28,654 Maple Dr,Anywhere,WA,98001,Married,95000,Y,Y,Y,Lisa Johnson,1980-11-12,Female,Spouse,Tom Johnson,2008-06-15,Male,Child,Sara Johnson,2011-09-03,Female,Child,3,Isaac Johnson,47.0,44.0,17.0,13.0,,,,,Full-Time,,Manufacturing,,Y,N,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Desk,Urban,Fair,N,None,High (>$100K),Low,Moderate,Rare,Y,N,HDHP + HSA,0.7473009,Middle age with dependents - PPO for family coverage.,\"[\\'HDHP + HSA\\', \\'PPO\\', \\'Indemnity\\']\",\"[0.7473009, 0.********, 0.*********]\",\"[\\'Accident\\', \\'STD\\', \\'FSA\\']\",1.0,STD: Income protection for High (>$100K) earners or fair health status.; FSA: Tax-advantaged account for medical expenses due to health conditions or pregnancy.,\"[\\'Employee Assistance\\', \\'Wellness Programs\\', \\'FSA\\']\",\"[0.5, 0.5, 0.5]\"\\r\\nE006,Michael,Anderson,Male,1983-07-12,987 Cedar Ln,Someplace,OR,97001,Not Married,38000,N,N,N,,,,,,,,,,,,,0,Michael Anderson,42.0,,,,,,,,Full-Time,,Finance,,N,N,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Desk,Suburban,Good,N,None,Low (<$50K),Low,Active,Rare,Y,N,HMO,0.5939828,Middle age with dependents - PPO for family coverage.,\"[\\'HMO\\', \\'HDHP + HSA\\', \\'PPO\\']\",\"[0.5939828, 0.169408, 0.1004401]\",[],0.0,ML model prediction,\"[\\'Employee Assistance\\', \\'Wellness Programs\\', \\'FSA\\']\",\"[0.5, 0.5, 0.5]\"\\r\\nE007,Nancy,Taylor,Female,1987-12-03,147 Birch Ave,Othertown,AZ,85001,Married,72000,Y,Y,Y,Paul Taylor,1985-04-20,Male,Spouse,Emma Taylor,2013-08-10,Female,Child,,,,,2,Nancy Taylor,37.0,40.0,11.0,,,,,,Part-Time,,Sales,,N,N,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Desk,Suburban,Excellent,N,None,Medium ($50K–$100K),Medium,Moderate,Frequent,Y,N,HDHP + HSA,0.56800634,Middle age with dependents - PPO for family coverage.,\"[\\'HDHP + HSA\\', \\'HDHP\\', \\'POS\\']\",\"[0.56800634, 0.27052328, 0.14308807]\",\"[\\'Dental\\', \\'Vision\\']\",1.0,Dental: Essential oral health coverage for all employees.; Vision: Eye care coverage for all employees.,\"[\\'Employee Assistance\\', \\'Wellness Programs\\', \\'FSA\\']\",\"[0.5, 0.5, 0.5]\"\\r\\nE008,Quinn,Martinez,Male,1979-10-17,258 Spruce St,Newplace,CO,80001,Not Married,58000,Y,Y,Y,Alex Martinez,2007-12-25,Male,Child,Zoe Martinez,2010-03-14,Female,Child,,,,,2,Quinn Martinez,45.0,17.0,15.0,,,,,,Full-Time,,Information Technology,,N,N,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Desk,Suburban,Good,N,None,Medium ($50K–$100K),Low,Moderate,Rare,N,N,POS,0.86140525,\"Medium income, good health - PPO for balanced cost and flexibility.\",\"[\\'POS\\', \\'PPO\\', \\'HMO\\']\",\"[0.86140525, 0.062455606, 0.04994324]\",\"[\\'Dental\\', \\'Vision\\']\",1.0,Dental: Essential oral health coverage for all employees.; Vision: Eye care coverage for all employees.,\"[\\'Employee Assistance\\', \\'Wellness Programs\\', \\'FSA\\']\",\"[0.5, 0.5, 0.5]\"\\r\\nE009,Tina,Garcia,Female,1981-03-24,369 Willow Way,Lastplace,NV,89001,Married,67000,Y,Y,Y,Carlos Garcia,1979-07-08,Male,Spouse,Maya Garcia,2009-11-30,Female,Child,,,,,2,Tina Garcia,44.0,46.0,15.0,,,,,,Full-Time,,Information Technology,,Y,N,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Desk,Urban,Good,N,Occasional,Medium ($50K–$100K),Low,Active,Rare,Y,Y,POS,0.77405035,\"Medium income, good health - PPO for balanced cost and flexibility.\",\"[\\'POS\\', \\'HDHP + HSA\\', \\'HMO\\']\",\"[0.77405035, 0.11897419, 0.04217901]\",\"[\\'Dental\\', \\'Vision\\', \\'Employee Assistance\\']\",1.0,Dental: Essential oral health coverage for all employees.; Vision: Eye care coverage for all employees.,\"[\\'Employee Assistance\\', \\'Wellness Programs\\', \\'FSA\\']\",\"[0.5, 0.5, 0.5]\"\\r\\nE010,Xavier,Rodriguez,Male,1986-09-09,741 Poplar Pl,Finaltown,UT,84001,Not Married,41000,N,N,N,,,,,,,,,,,,,0,Xavier Rodriguez,38.0,,,,,,,,Full-Time,,Sales,,N,N,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Desk,Urban,Good,N,Occasional,Medium ($50K–$100K),Medium,Active,Occasional,N,Y,POS,0.72313046,\"Medium income, good health - PPO for balanced cost and flexibility.\",\"[\\'POS\\', \\'HMO\\', \\'HDHP\\']\",\"[0.72313046, 0.11456908, 0.097843625]\",[\\'Employee Assistance\\'],1.0,ML model prediction,\"[\\'Employee Assistance\\', \\'Wellness Programs\\', \\'FSA\\']\",\"[0.5, 0.5, 0.5]\"\\r\\nE011,Yvonne,Lee,Female,1984-12-18,852 Ash Ct,Hometown,MT,59001,Married,63000,Y,Y,Y,David Lee,1982-02-14,Male,Spouse,,,,,,,,,1,Yvonne Lee,40.0,43.0,,,,,,,Part-Time,,Sales,,N,N,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Desk,Rural,Good,Y,Regular,Medium ($50K–$100K),Medium,Moderate,Frequent,N,N,POS,0.7844137,\"Medium income, good health - PPO for balanced cost and flexibility.\",\"[\\'POS\\', \\'PPO\\', \\'HMO\\']\",\"[0.7844137, 0.16871762, 0.026434403]\",\"[\\'Dental\\', \\'Vision\\', \\'Critical Illness\\']\",1.0,Dental: Essential oral health coverage for all employees.; Vision: Eye care coverage for all employees.; Critical Illness: Important protection for age 40 with good health status.,\"[\\'Employee Assistance\\', \\'Wellness Programs\\', \\'FSA\\']\",\"[0.5, 0.5, 0.5]\"\\r\\nE012,Aaron,White,Male,1977-04-07,963 Beech St,Yourtown,ID,83001,Not Married,49000,Y,Y,Y,Kelly White,2006-05-22,Female,Child,Ryan White,2009-10-11,Male,Child,,,,,2,Aaron White,48.0,19.0,15.0,,,,,,Full-Time,,Sales,,N,N,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Desk,Rural,Fair,Y,Regular,Medium ($50K–$100K),Medium,Moderate,Frequent,N,N,PPO,0.8217366,\"Chronic conditions, older age - POS for coordinated care management.\",\"[\\'PPO\\', \\'POS\\', \\'HMO\\']\",\"[0.8217366, 0.136021, 0.026474979]\",\"[\\'Dental\\', \\'Vision\\']\",0.5,Default benefits assignment due to prediction error,\"[\\'Dental\\', \\'Vision\\', \\'Hospital Indemnity\\']\",\"[0.5, 0.5, 0.5]\"\\r\\nE013,Brooke,Harris,Female,1989-08-21,159 Cherry Ave,Mytown,WY,82001,Not Married,35000,N,N,N,,,,,,,,,,,,,0,Brooke Harris,35.0,,,,,,,,Full-Time,,Sales,,N,N,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Desk,Rural,Good,N,None,Low (<$50K),Medium,Active,Frequent,N,N,HMO,0.7373842,Middle age with dependents - PPO for family coverage.,\"[\\'HMO\\', \\'POS\\', \\'PPO\\']\",\"[0.7373842, 0.13506323, 0.10222582]\",\"[\\'Dental\\', \\'Vision\\']\",0.5,Default benefits assignment due to prediction error,\"[\\'Dental\\', \\'Vision\\', \\'Hospital Indemnity\\']\",\"[0.5, 0.5, 0.5]\"\\r\\nE014,Connor,Clark,Male,1982-01-26,357 Dogwood Dr,Ourtown,ND,58001,Married,85000,Y,Y,Y,Rachel Clark,1984-06-18,Female,Spouse,Jake Clark,2012-01-05,Male,Child,Lily Clark,2014-07-12,Female,Child,3,Connor Clark,43.0,41.0,13.0,11.0,,,,,Full-Time,,Finance,,N,N,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Desk,Rural,Excellent,N,None,High (>$100K),Medium,Moderate,Occasional,Y,N,HDHP + HSA,0.815689,\"High income, good health, HSA familiar, no chronic conditions - HDHP + HSA for tax benefits.\",\"[\\'HDHP + HSA\\', \\'POS\\', \\'PPO\\']\",\"[0.815689, 0.*********, 0.*********]\",\"[\\'Dental\\', \\'Vision\\']\",0.5,Default benefits assignment due to prediction error,\"[\\'Dental\\', \\'Vision\\', \\'Hospital Indemnity\\']\",\"[0.5, 0.5, 0.5]\"\\r\\nE015,Destiny,Lewis,Female,1986-05-13,468 Fir Ln,Theirtown,SD,57001,Not Married,44000,Y,Y,Y,Noah Lewis,2011-09-28,Male,Child,,,,,,,,,1,Destiny Lewis,39.0,13.0,,,,,,,Full-Time,,Sales,,Y,N,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Desk,Rural,Good,N,None,Medium ($50K–$100K),Low,Moderate,Frequent,Y,N,HDHP + HSA,0.56222314,\"Medium income, good health - PPO for balanced cost and flexibility.\",\"[\\'HDHP + HSA\\', \\'HDHP\\', \\'POS\\']\",\"[0.56222314, 0.32271606, 0.0976987]\",\"[\\'Dental\\', \\'Vision\\']\",0.5,Default benefits assignment due to prediction error,\"[\\'Dental\\', \\'Vision\\', \\'Hospital Indemnity\\']\",\"[0.5, 0.5, 0.5]\"\\r\\n', 'comprehensive_statistics': {'basic_demographics': {'age_statistics': {'average_age': 41.8, 'median_age': 42.0, 'age_range': {'min': 35, 'max': 50}, 'age_distribution': {'18-30': 0, '31-45': 12, '46-60': 3, '60+': 0}}, 'gender_composition': {'counts': {'Male': 8, 'Female': 7}, 'percentages': {'Male': 53.33, 'Female': 46.67}}, 'marital_status_distribution': {'counts': {'Not Married': 8, 'Married': 7}, 'percentages': {'Not Married': 53.33, 'Married': 46.67}}}, 'dependent_analysis': {'dependent_count_distribution': {'average_dependents': 1.5333333333333334, 'median_dependents': 2.0, 'distribution': {2: 5, 1: 4, 3: 3, 0: 3}, 'employees_with_dependents': 12, 'percentage_with_dependents': 80.0}, 'dependent_age_analysis': {'total_dependents_found': 23, 'average_dependent_age': 22.17, 'median_dependent_age': 15.0, 'dependents_over_26_as_children': 0, 'age_distribution': {'0-5': 0, '6-12': 4, '13-18': 10, '19-26': 2, '26+': 7}}}, 'employment_demographics': {'department_distribution': {'counts': {'Sales': 6, 'Information Technology': 3, 'Finance': 3, 'Engineering': 2, 'Manufacturing': 1}, 'percentages': {'Sales': 40.0, 'Information Technology': 20.0, 'Finance': 20.0, 'Engineering': 13.33, 'Manufacturing': 6.67}}, 'employment_type_distribution': {'counts': {'Full-Time': 12, 'Part-Time': 3}, 'percentages': {'Full-Time': 80.0, 'Part-Time': 20.0}}, 'job_type_distribution': {'counts': {'Desk': 15}, 'percentages': {'Desk': 100.0}}, 'employee_class_distribution': {'counts': {}, 'percentages': {}}, 'salary_analysis': {'average_salary': 59166.67, 'median_salary': 58000.0, 'salary_range': {'min': 35000, 'max': 95000}, 'salary_distribution': {'under_40k': 2, '40k_75k': 10, '75k_100k': 3, 'over_100k': 0}}}, 'health_and_lifestyle': {'health_condition_distribution': {'counts': {'Good': 10, 'Fair': 3, 'Excellent': 2}, 'percentages': {'Good': 66.67, 'Fair': 20.0, 'Excellent': 13.33}}, 'chronic_condition_distribution': {'counts': {'N': 11, 'Y': 4}, 'percentages': {'N': 73.33, 'Y': 26.67}}, 'tobacco_use_distribution': {'counts': {'N': 10, 'Y': 5}, 'percentages': {'N': 66.67, 'Y': 33.33}}, 'lifestyle_distribution': {'counts': {'Moderate': 8, 'Active': 7}, 'percentages': {'Moderate': 53.33, 'Active': 46.67}}, 'prescription_use_distribution': {'counts': {'None': 9, 'Regular': 4, 'Occasional': 2}, 'percentages': {'None': 60.0, 'Regular': 26.67, 'Occasional': 13.33}}}, 'coverage_analysis': {'coverage_tier_distribution': {'counts': {}, 'percentages': {}}, 'coverage_tier_by_family_size': {}, 'medical_plan_distribution': {'counts': {'Y': 12, 'N': 3}, 'percentages': {'Y': 80.0, 'N': 20.0}}, 'dental_plan_distribution': {'counts': {'Y': 11, 'N': 4}, 'percentages': {'Y': 73.33, 'N': 26.67}}, 'vision_plan_distribution': {'counts': {'Y': 11, 'N': 4}, 'percentages': {'Y': 73.33, 'N': 26.67}}, 'life_plan_distribution': {'counts': {}, 'percentages': {}}}, 'geographic_distribution': {'state_distribution': {'counts': {'CA': 1, 'TX': 1, 'FL': 1, 'NY': 1, 'WA': 1, 'OR': 1, 'AZ': 1, 'CO': 1, 'NV': 1, 'UT': 1, 'MT': 1, 'ID': 1, 'WY': 1, 'ND': 1, 'SD': 1}, 'percentages': {'CA': 6.67, 'TX': 6.67, 'FL': 6.67, 'NY': 6.67, 'WA': 6.67, 'OR': 6.67, 'AZ': 6.67, 'CO': 6.67, 'NV': 6.67, 'UT': 6.67, 'MT': 6.67, 'ID': 6.67, 'WY': 6.67, 'ND': 6.67, 'SD': 6.67}}, 'region_distribution': {'counts': {'Urban': 7, 'Rural': 5, 'Suburban': 3}, 'percentages': {'Urban': 46.67, 'Rural': 33.33, 'Suburban': 20.0}}, 'top_cities': {'counts': {'Anytown': 1, 'Somewhere': 1, 'Elsewhere': 1, 'Nowhere': 1, 'Anywhere': 1, 'Someplace': 1, 'Othertown': 1, 'Newplace': 1, 'Lastplace': 1, 'Finaltown': 1}, 'percentages': {'Anytown': 6.67, 'Somewhere': 6.67, 'Elsewhere': 6.67, 'Nowhere': 6.67, 'Anywhere': 6.67, 'Someplace': 6.67, 'Othertown': 6.67, 'Newplace': 6.67, 'Lastplace': 6.67, 'Finaltown': 6.67}}}, 'financial_demographics': {'income_tier_distribution': {'counts': {'Medium ($50K–$100K)': 10, 'High (>$100K)': 3, 'Low (<$50K)': 2}, 'percentages': {'Medium ($50K–$100K)': 66.67, 'High (>$100K)': 20.0, 'Low (<$50K)': 13.33}}, 'risk_tolerance_distribution': {'counts': {'Medium': 10, 'Low': 5}, 'percentages': {'Medium': 66.67, 'Low': 33.33}}, 'hsa_familiarity_distribution': {'counts': {'N': 8, 'Y': 7}, 'percentages': {'N': 53.33, 'Y': 46.67}}}, 'risk_assessment': {'group_risk_score': 31.93, 'group_risk_level': 'Medium', 'risk_distribution': {'low_risk': 7, 'medium_risk': 7, 'high_risk': 1}, 'risk_statistics': {'min_risk_score': 13, 'max_risk_score': 75, 'median_risk_score': 30.0, 'std_risk_score': 16.6}, 'top_risk_factors': {'Tobacco Use': 5, 'Chronic Conditions': 4, 'Poor Health Condition': 3, 'Advanced Age': 1}, 'individual_risks': [{'employee_id': 'E001', 'risk_score': 23, 'risk_level': 'Low', 'risk_factors': []}, {'employee_id': 'E002', 'risk_score': 75, 'risk_level': 'High', 'risk_factors': ['Age 50.0 (high risk)', 'Health condition: Fair', 'Has chronic condition', 'Tobacco user']}, {'employee_id': 'E003', 'risk_score': 30, 'risk_level': 'Medium', 'risk_factors': ['Tobacco user']}, {'employee_id': 'E004', 'risk_score': 35, 'risk_level': 'Medium', 'risk_factors': ['Has chronic condition']}, {'employee_id': 'E005', 'risk_score': 48, 'risk_level': 'Medium', 'risk_factors': ['Health condition: Fair', 'Tobacco user']}, {'employee_id': 'E006', 'risk_score': 20, 'risk_level': 'Low', 'risk_factors': []}, {'employee_id': 'E007', 'risk_score': 13, 'risk_level': 'Low', 'risk_factors': []}, {'employee_id': 'E008', 'risk_score': 23, 'risk_level': 'Low', 'risk_factors': []}, {'employee_id': 'E009', 'risk_score': 35, 'risk_level': 'Medium', 'risk_factors': ['Tobacco user']}, {'employee_id': 'E010', 'risk_score': 15, 'risk_level': 'Low', 'risk_factors': []}]}, 'data_quality_metrics': {'overall_completeness': {'total_cells': 2130, 'missing_cells': 1535, 'completeness_percentage': 27.93}, 'column_completeness': {'employee_id': {'missing_count': 0, 'completeness_percentage': 100.0}, 'first_name': {'missing_count': 0, 'completeness_percentage': 100.0}, 'last_name': {'missing_count': 0, 'completeness_percentage': 100.0}, 'gender': {'missing_count': 0, 'completeness_percentage': 100.0}, 'dob': {'missing_count': 0, 'completeness_percentage': 100.0}, 'address1': {'missing_count': 0, 'completeness_percentage': 100.0}, 'city': {'missing_count': 0, 'completeness_percentage': 100.0}, 'state': {'missing_count': 0, 'completeness_percentage': 100.0}, 'zipcode': {'missing_count': 0, 'completeness_percentage': 100.0}, 'marital_status': {'missing_count': 0, 'completeness_percentage': 100.0}, 'salary': {'missing_count': 0, 'completeness_percentage': 100.0}, 'medical_plan': {'missing_count': 0, 'completeness_percentage': 100.0}, 'dental_plan': {'missing_count': 0, 'completeness_percentage': 100.0}, 'vision_plan': {'missing_count': 0, 'completeness_percentage': 100.0}, 'dept_1': {'missing_count': 3, 'completeness_percentage': 80.0}, 'dept_1_dob': {'missing_count': 3, 'completeness_percentage': 80.0}, 'dept_1_gender': {'missing_count': 3, 'completeness_percentage': 80.0}, 'relationship_type_1': {'missing_count': 3, 'completeness_percentage': 80.0}, 'dept_2': {'missing_count': 7, 'completeness_percentage': 53.33}, 'dept_2_dob': {'missing_count': 7, 'completeness_percentage': 53.33}, 'dept_2_gender': {'missing_count': 7, 'completeness_percentage': 53.33}, 'relationship_type_2': {'missing_count': 7, 'completeness_percentage': 53.33}, 'dept_3': {'missing_count': 12, 'completeness_percentage': 20.0}, 'dept_3_dob': {'missing_count': 12, 'completeness_percentage': 20.0}, 'dept_3_gender': {'missing_count': 12, 'completeness_percentage': 20.0}, 'relationship_type_3': {'missing_count': 12, 'completeness_percentage': 20.0}, 'dept_count': {'missing_count': 0, 'completeness_percentage': 100.0}, 'name': {'missing_count': 0, 'completeness_percentage': 100.0}, 'age': {'missing_count': 0, 'completeness_percentage': 100.0}, 'dept_1_age': {'missing_count': 3, 'completeness_percentage': 80.0}, 'dept_2_age': {'missing_count': 7, 'completeness_percentage': 53.33}, 'dept_3_age': {'missing_count': 12, 'completeness_percentage': 20.0}, 'middle_name': {'missing_count': 15, 'completeness_percentage': 0.0}, 'address2': {'missing_count': 15, 'completeness_percentage': 0.0}, 'relationship': {'missing_count': 15, 'completeness_percentage': 0.0}, 'record_type': {'missing_count': 15, 'completeness_percentage': 0.0}, 'employment_type': {'missing_count': 0, 'completeness_percentage': 100.0}, 'employee_class': {'missing_count': 15, 'completeness_percentage': 0.0}, 'department': {'missing_count': 0, 'completeness_percentage': 100.0}, 'hire_date': {'missing_count': 15, 'completeness_percentage': 0.0}, 'tobacco_use': {'missing_count': 0, 'completeness_percentage': 100.0}, 'pregnancy_status': {'missing_count': 0, 'completeness_percentage': 100.0}, 'life_plan': {'missing_count': 15, 'completeness_percentage': 0.0}, 'add_plan': {'missing_count': 15, 'completeness_percentage': 0.0}, 'coverage_tier': {'missing_count': 15, 'completeness_percentage': 0.0}, 'ssn': {'missing_count': 15, 'completeness_percentage': 0.0}, 'dept_4': {'missing_count': 15, 'completeness_percentage': 0.0}, 'dept_4_dob': {'missing_count': 15, 'completeness_percentage': 0.0}, 'dept_4_age': {'missing_count': 15, 'completeness_percentage': 0.0}, 'dept_4_gender': {'missing_count': 15, 'completeness_percentage': 0.0}, 'relationship_type_4': {'missing_count': 15, 'completeness_percentage': 0.0}, 'dept_5': {'missing_count': 15, 'completeness_percentage': 0.0}, 'dept_5_dob': {'missing_count': 15, 'completeness_percentage': 0.0}, 'dept_5_age': {'missing_count': 15, 'completeness_percentage': 0.0}, 'dept_5_gender': {'missing_count': 15, 'completeness_percentage': 0.0}, 'relationship_type_5': {'missing_count': 15, 'completeness_percentage': 0.0}, 'dept_6': {'missing_count': 15, 'completeness_percentage': 0.0}, 'dept_6_dob': {'missing_count': 15, 'completeness_percentage': 0.0}, 'dept_6_age': {'missing_count': 15, 'completeness_percentage': 0.0}, 'dept_6_gender': {'missing_count': 15, 'completeness_percentage': 0.0}, 'relationship_type_6': {'missing_count': 15, 'completeness_percentage': 0.0}, 'dept_7': {'missing_count': 15, 'completeness_percentage': 0.0}, 'dept_7_dob': {'missing_count': 15, 'completeness_percentage': 0.0}, 'dept_7_age': {'missing_count': 15, 'completeness_percentage': 0.0}, 'dept_7_gender': {'missing_count': 15, 'completeness_percentage': 0.0}, 'relationship_type_7': {'missing_count': 15, 'completeness_percentage': 0.0}, 'dept_8': {'missing_count': 15, 'completeness_percentage': 0.0}, 'dept_8_dob': {'missing_count': 15, 'completeness_percentage': 0.0}, 'dept_8_age': {'missing_count': 15, 'completeness_percentage': 0.0}, 'dept_8_gender': {'missing_count': 15, 'completeness_percentage': 0.0}, 'relationship_type_8': {'missing_count': 15, 'completeness_percentage': 0.0}, 'dept_9': {'missing_count': 15, 'completeness_percentage': 0.0}, 'dept_9_dob': {'missing_count': 15, 'completeness_percentage': 0.0}, 'dept_9_age': {'missing_count': 15, 'completeness_percentage': 0.0}, 'dept_9_gender': {'missing_count': 15, 'completeness_percentage': 0.0}, 'relationship_type_9': {'missing_count': 15, 'completeness_percentage': 0.0}, 'dept_10': {'missing_count': 15, 'completeness_percentage': 0.0}, 'dept_10_dob': {'missing_count': 15, 'completeness_percentage': 0.0}, 'dept_10_age': {'missing_count': 15, 'completeness_percentage': 0.0}, 'dept_10_gender': {'missing_count': 15, 'completeness_percentage': 0.0}, 'relationship_type_10': {'missing_count': 15, 'completeness_percentage': 0.0}, 'dept_11': {'missing_count': 15, 'completeness_percentage': 0.0}, 'dept_11_dob': {'missing_count': 15, 'completeness_percentage': 0.0}, 'dept_11_age': {'missing_count': 15, 'completeness_percentage': 0.0}, 'dept_11_gender': {'missing_count': 15, 'completeness_percentage': 0.0}, 'relationship_type_11': {'missing_count': 15, 'completeness_percentage': 0.0}, 'dept_12': {'missing_count': 15, 'completeness_percentage': 0.0}, 'dept_12_dob': {'missing_count': 15, 'completeness_percentage': 0.0}, 'dept_12_age': {'missing_count': 15, 'completeness_percentage': 0.0}, 'dept_12_gender': {'missing_count': 15, 'completeness_percentage': 0.0}, 'relationship_type_12': {'missing_count': 15, 'completeness_percentage': 0.0}, 'dept_13': {'missing_count': 15, 'completeness_percentage': 0.0}, 'dept_13_dob': {'missing_count': 15, 'completeness_percentage': 0.0}, 'dept_13_age': {'missing_count': 15, 'completeness_percentage': 0.0}, 'dept_13_gender': {'missing_count': 15, 'completeness_percentage': 0.0}, 'relationship_type_13': {'missing_count': 15, 'completeness_percentage': 0.0}, 'dept_14': {'missing_count': 15, 'completeness_percentage': 0.0}, 'dept_14_dob': {'missing_count': 15, 'completeness_percentage': 0.0}, 'dept_14_age': {'missing_count': 15, 'completeness_percentage': 0.0}, 'dept_14_gender': {'missing_count': 15, 'completeness_percentage': 0.0}, 'relationship_type_14': {'missing_count': 15, 'completeness_percentage': 0.0}, 'dept_15': {'missing_count': 15, 'completeness_percentage': 0.0}, 'dept_15_dob': {'missing_count': 15, 'completeness_percentage': 0.0}, 'dept_15_age': {'missing_count': 15, 'completeness_percentage': 0.0}, 'dept_15_gender': {'missing_count': 15, 'completeness_percentage': 0.0}, 'relationship_type_15': {'missing_count': 15, 'completeness_percentage': 0.0}, 'dept_16': {'missing_count': 15, 'completeness_percentage': 0.0}, 'dept_16_dob': {'missing_count': 15, 'completeness_percentage': 0.0}, 'dept_16_age': {'missing_count': 15, 'completeness_percentage': 0.0}, 'dept_16_gender': {'missing_count': 15, 'completeness_percentage': 0.0}, 'relationship_type_16': {'missing_count': 15, 'completeness_percentage': 0.0}, 'dept_17': {'missing_count': 15, 'completeness_percentage': 0.0}, 'dept_17_dob': {'missing_count': 15, 'completeness_percentage': 0.0}, 'dept_17_age': {'missing_count': 15, 'completeness_percentage': 0.0}, 'dept_17_gender': {'missing_count': 15, 'completeness_percentage': 0.0}, 'relationship_type_17': {'missing_count': 15, 'completeness_percentage': 0.0}, 'dept_18': {'missing_count': 15, 'completeness_percentage': 0.0}, 'dept_18_dob': {'missing_count': 15, 'completeness_percentage': 0.0}, 'dept_18_age': {'missing_count': 15, 'completeness_percentage': 0.0}, 'dept_18_gender': {'missing_count': 15, 'completeness_percentage': 0.0}, 'relationship_type_18': {'missing_count': 15, 'completeness_percentage': 0.0}, 'dept_19': {'missing_count': 15, 'completeness_percentage': 0.0}, 'dept_19_dob': {'missing_count': 15, 'completeness_percentage': 0.0}, 'dept_19_age': {'missing_count': 15, 'completeness_percentage': 0.0}, 'dept_19_gender': {'missing_count': 15, 'completeness_percentage': 0.0}, 'relationship_type_19': {'missing_count': 15, 'completeness_percentage': 0.0}, 'dept_20': {'missing_count': 15, 'completeness_percentage': 0.0}, 'dept_20_dob': {'missing_count': 15, 'completeness_percentage': 0.0}, 'dept_20_age': {'missing_count': 15, 'completeness_percentage': 0.0}, 'dept_20_gender': {'missing_count': 15, 'completeness_percentage': 0.0}, 'relationship_type_20': {'missing_count': 15, 'completeness_percentage': 0.0}, 'job_type': {'missing_count': 0, 'completeness_percentage': 100.0}, 'region': {'missing_count': 0, 'completeness_percentage': 100.0}, 'health_condition': {'missing_count': 0, 'completeness_percentage': 100.0}, 'chronic_condition': {'missing_count': 0, 'completeness_percentage': 100.0}, 'prescription_use': {'missing_count': 0, 'completeness_percentage': 100.0}, 'income_tier': {'missing_count': 0, 'completeness_percentage': 100.0}, 'risk_tolerance': {'missing_count': 0, 'completeness_percentage': 100.0}, 'lifestyle': {'missing_count': 0, 'completeness_percentage': 100.0}, 'travel_frequency': {'missing_count': 0, 'completeness_percentage': 100.0}, 'hsa_familiarity': {'missing_count': 0, 'completeness_percentage': 100.0}, 'mental_health_needs': {'missing_count': 0, 'completeness_percentage': 100.0}}, 'critical_field_completeness': {'name': {'missing_count': 0, 'completeness_percentage': 100.0}, 'gender': {'missing_count': 0, 'completeness_percentage': 100.0}, 'zipcode': {'missing_count': 0, 'completeness_percentage': 100.0}, 'marital_status': {'missing_count': 0, 'completeness_percentage': 100.0}}}}, 'prediction_summary': {'total_employees': 15, 'plan_type_distribution': {'successful_predictions': {'POS': 6, 'HDHP + HSA': 5, 'PPO': 2, 'HMO': 2}, 'failed_predictions': '0', 'success_rate': 100.0, 'total_predicted': '15'}, 'benefits_distribution': {}, 'confidence_metrics': {'plan_confidence': {'mean': '0.720', 'min': '0.424', 'max': '0.889', 'count': 15}}, 'prediction_success_rates': {}, 'top_3_plan_probabilities': []}, 'total_employees': 15, 'total_columns': 152, 'has_predictions': True, 'prediction_columns': ['predicted_plan_type', 'plan_confidence']}", "message": "Census file processed successfully"}