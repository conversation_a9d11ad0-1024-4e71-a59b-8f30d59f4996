{"file_info": {"original_filename": "col2_mixed_variations.csv", "processing_timestamp": "2025-07-15 17:27:56.370882", "total_processing_time_seconds": 17.809857845306396}, "final_response": {"success": true, "data": {"enriched_data_csv": "employee_id,first_name,last_name,gender,dob,address1,city,state,zipcode,marital_status,salary,medical_plan,dental_plan,vision_plan,dept_1,dept_1_dob,dept_1_gender,relationship_type_1,dept_2,dept_2_dob,dept_2_gender,relationship_type_2,dept_3,dept_3_dob,dept_3_gender,relationship_type_3,dept_count,name,age,dept_1_age,dept_2_age,dept_3_age,middle_name,address2,relationship,record_type,employment_type,employee_class,department,hire_date,tobacco_use,pregnancy_status,life_plan,add_plan,coverage_tier,ssn,dept_4,dept_4_dob,dept_4_age,dept_4_gender,relationship_type_4,dept_5,dept_5_dob,dept_5_age,dept_5_gender,relationship_type_5,dept_6,dept_6_dob,dept_6_age,dept_6_gender,relationship_type_6,dept_7,dept_7_dob,dept_7_age,dept_7_gender,relationship_type_7,dept_8,dept_8_dob,dept_8_age,dept_8_gender,relationship_type_8,dept_9,dept_9_dob,dept_9_age,dept_9_gender,relationship_type_9,dept_10,dept_10_dob,dept_10_age,dept_10_gender,relationship_type_10,dept_11,dept_11_dob,dept_11_age,dept_11_gender,relationship_type_11,dept_12,dept_12_dob,dept_12_age,dept_12_gender,relationship_type_12,dept_13,dept_13_dob,dept_13_age,dept_13_gender,relationship_type_13,dept_14,dept_14_dob,dept_14_age,dept_14_gender,relationship_type_14,dept_15,dept_15_dob,dept_15_age,dept_15_gender,relationship_type_15,dept_16,dept_16_dob,dept_16_age,dept_16_gender,relationship_type_16,dept_17,dept_17_dob,dept_17_age,dept_17_gender,relationship_type_17,dept_18,dept_18_dob,dept_18_age,dept_18_gender,relationship_type_18,dept_19,dept_19_dob,dept_19_age,dept_19_gender,relationship_type_19,dept_20,dept_20_dob,dept_20_age,dept_20_gender,relationship_type_20,job_type,region,health_condition,chronic_condition,prescription_use,income_tier,risk_tolerance,lifestyle,travel_frequency,hsa_familiarity,mental_health_needs,predicted_plan_type,plan_confidence,plan_reason,top_3_plans,top_3_plan_confidences,predicted_benefits,benefits_confidence,benefits_reason,top_3_benefits,top_3_benefits_confidences\r\nEMP001,Jonathan,Williams,Male,15/01/1980,123 Oak Street,Springfield,IL,62701,Married,68000,Y,Y,Y,Sarah Williams,20/05/1982,Female,Spouse,Michael Williams,12/09/2010,Male,Child,,,,,2,Jonathan Williams,45.0,43.0,14.0,,,,,,Full-Time,,Manufacturing,,Y,N,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Desk,Urban,Good,N,None,Medium ($50K–$100K),Low,Moderate,Occasional,N,N,POS,0.90829504,\"Medium income, good health - PPO for balanced cost and flexibility.\",\"['POS', 'HMO', 'PPO']\",\"[0.90829504, 0.*********, 0.*********]\",\"['Dental', 'Vision', 'Accident', 'STD']\",1.0,Dental: Essential oral health coverage for all employees.; Vision: Eye care coverage for all employees.; STD: Income protection for Medium ($50K–$100K) earners or good health status.,\"['Employee Assistance', 'Wellness Programs', 'FSA']\",\"[0.5, 0.5, 0.5]\"\r\nEMP002,Amanda,Johnson,Female,10/03/1975,456 Pine Avenue,Madison,WI,53703,Not Married,54000,Y,N,N,Emma Johnson,25/07/2005,Female,Child,,,,,,,,,1,Amanda Johnson,49.0,19.0,,,,,,,Part-Time,,Engineering,,N,N,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Desk,Suburban,Good,N,Occasional,Medium ($50K–$100K),Medium,Moderate,Occasional,Y,N,POS,0.8381619,\"Medium income, good health - PPO for balanced cost and flexibility.\",\"['POS', 'HDHP + HSA', 'PPO']\",\"[0.8381619, 0.053493556, 0.052143473]\",\"['Dental', 'Vision', 'Accident', 'STD']\",1.0,Dental: Essential oral health coverage for all employees.; Vision: Eye care coverage for all employees.; STD: Income protection for Medium ($50K–$100K) earners or good health status.,\"['Employee Assistance', 'Wellness Programs', 'FSA']\",\"[0.5, 0.5, 0.5]\"\r\nEMP003,Christopher,Brown,Male,30/06/1990,789 Maple Road,Austin,TX,73301,Married,82000,Y,Y,Y,Jennifer Brown,15/08/1992,Female,Spouse,Ethan Brown,01/12/2015,Male,Child,Olivia Brown,22/03/2018,Female,Child,3,Christopher Brown,35.0,32.0,10.0,7.0,,,,,Full-Time,,Information Technology,,N,N,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Desk,Urban,Good,N,None,High (>$100K),High,Moderate,Rare,N,N,POS,0.34228057,Middle age with dependents - PPO for family coverage.,\"['POS', 'PPO', 'Indemnity']\",\"[0.34228057, 0.22125721, 0.15578382]\",['Wellness Programs'],1.0,ML model prediction,\"['Employee Assistance', 'Wellness Programs', 'FSA']\",\"[0.5, 0.5, 0.5]\"\r\nEMP004,Michelle,Davis,Female,05/11/1985,321 Elm Drive,Portland,OR,97201,Not Married,47000,Y,Y,N,Lucas Davis,18/04/2012,Male,Child,,,,,,,,,1,Michelle Davis,40.0,13.0,,,,,,,Full-Time,,Manufacturing,,N,Y,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Desk,Urban,Excellent,N,None,Medium ($50K–$100K),Low,Active,Occasional,N,Y,HMO,0.70652986,\"Medium income, urban area - EPO for network-based care.\",\"['HMO', 'POS', 'PPO']\",\"[0.70652986, 0.26228836, 0.*********]\",\"['Dental', 'Vision', 'Hospital Indemnity', 'Accident', 'STD', 'Employee Assistance']\",1.0,Dental: Essential oral health coverage for all employees.; Vision: Eye care coverage for all employees.; Accident: Recommended for active lifestyle and occasional travel frequency.; STD: Income protection for Medium ($50K–$100K) earners or excellent health status.,\"['Employee Assistance', 'Wellness Programs', 'FSA']\",\"[0.5, 0.5, 0.5]\"\r\nEMP005,Robert,Miller,Male,28/02/1978,654 Cedar Lane,Denver,CO,80201,Married,91000,Y,Y,Y,Patricia Miller,12/11/1980,Female,Spouse,Timothy Miller,15/06/2008,Male,Child,Samantha Miller,03/09/2011,Female,Child,3,Robert Miller,47.0,44.0,17.0,14.0,,,,,Full-Time,,Sales,,N,N,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Desk,Urban,Good,N,None,High (>$100K),Low,Moderate,Occasional,Y,N,HDHP + HSA,0.8763457,\"High income, good health, HSA familiar, no chronic conditions - HDHP + HSA for tax benefits.\",\"['HDHP + HSA', 'POS', 'PPO']\",\"[0.8763457, 0.07689335, 0.*********]\",['Wellness Programs'],1.0,ML model prediction,\"['Employee Assistance', 'Wellness Programs', 'FSA']\",\"[0.5, 0.5, 0.5]\"\r\nEMP006,Jessica,Wilson,Female,12/07/1983,987 Birch Court,Phoenix,AZ,85001,Not Married,39000,N,N,N,,,,,,,,,,,,,0,Jessica Wilson,41.0,,,,,,,,Contract,,Engineering,,N,N,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Desk,Suburban,Excellent,N,None,Low (<$50K),Low,Moderate,Rare,N,N,HMO,0.48716465,Middle age with dependents - PPO for family coverage.,\"['HMO', 'PPO', 'Indemnity']\",\"[0.48716465, 0.17091762, 0.1201785]\",\"['Accident', 'STD']\",1.0,ML model prediction,\"['Employee Assistance', 'Wellness Programs', 'FSA']\",\"[0.5, 0.5, 0.5]\"\r\nEMP007,Daniel,Moore,Male,03/12/1987,147 Spruce Way,Seattle,WA,98101,Married,74000,Y,Y,Y,Rebecca Moore,20/04/1985,Female,Spouse,Grace Moore,10/08/2013,Female,Child,,,,,2,Daniel Moore,38.0,40.0,11.0,,,,,,Full-Time,,Manufacturing,,N,N,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Desk,Urban,Fair,Y,Occasional,Medium ($50K–$100K),Medium,Active,Rare,N,Y,PPO,0.84879845,\"Medium income, urban area - EPO for network-based care.\",\"['PPO', 'POS', 'HMO']\",\"[0.84879845, 0.08694499, 0.*********]\",\"['Dental', 'Vision', 'Accident', 'Critical Illness', 'STD', 'Employee Assistance']\",1.0,Dental: Essential oral health coverage for all employees.; Vision: Eye care coverage for all employees.; Accident: Recommended for active lifestyle and rare travel frequency.; Critical Illness: Important protection for age 38 with fair health status.; STD: Income protection for Medium ($50K–$100K) earners or fair health status.,\"['Employee Assistance', 'Wellness Programs', 'FSA']\",\"[0.5, 0.5, 0.5]\"\r\nEMP008,Lisa,Taylor,Female,17/10/1979,258 Willow Street,Miami,FL,33101,Not Married,61000,Y,Y,Y,Alexander Taylor,25/12/2007,Male,Child,Sophia Taylor,14/03/2010,Female,Child,,,,,2,Lisa Taylor,45.0,17.0,15.0,,,,,,Full-Time,,Sales,,N,N,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Desk,Urban,Excellent,N,None,Medium ($50K–$100K),Medium,Active,Frequent,Y,Y,HDHP + HSA,0.8004089,\"Medium income, urban area - EPO for network-based care.\",\"['HDHP + HSA', 'POS', 'PPO']\",\"[0.8004089, 0.17146602, 0.*********]\",\"['Dental', 'Vision', 'Employee Assistance']\",1.0,Dental: Essential oral health coverage for all employees.; Vision: Eye care coverage for all employees.,\"['Employee Assistance', 'Wellness Programs', 'FSA']\",\"[0.5, 0.5, 0.5]\"\r\nEMP009,Kevin,Anderson,Male,24/03/1981,369 Poplar Avenue,Boston,MA,2101,Married,69000,Y,Y,Y,Maria Anderson,08/07/1979,Female,Spouse,Isabella Anderson,30/11/2009,Female,Child,,,,,2,Kevin Anderson,44.0,45.0,15.0,,,,,,Full-Time,,Engineering,,Y,N,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Desk,Urban,Excellent,N,None,Medium ($50K–$100K),Low,Moderate,Occasional,N,Y,POS,0.7610917,\"Medium income, urban area - EPO for network-based care.\",\"['POS', 'PPO', 'HMO']\",\"[0.7610917, 0.11455104, 0.093424566]\",\"['Dental', 'Vision', 'Accident', 'STD', 'Employee Assistance']\",1.0,Dental: Essential oral health coverage for all employees.; Vision: Eye care coverage for all employees.; STD: Income protection for Medium ($50K–$100K) earners or excellent health status.,\"['Employee Assistance', 'Wellness Programs', 'FSA']\",\"[0.5, 0.5, 0.5]\"\r\nEMP010,Rachel,Thomas,Female,09/09/1986,741 Ash Road,Atlanta,GA,30301,Not Married,43000,N,N,N,,,,,,,,,,,,,0,Rachel Thomas,38.0,,,,,,,,Full-Time,,Engineering,,N,Y,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Desk,Rural,Fair,N,None,Medium ($50K–$100K),Medium,Active,Occasional,N,N,HMO,0.86440706,Middle age with dependents - PPO for family coverage.,\"['HMO', 'PPO', 'POS']\",\"[0.86440706, 0.09522333, 0.03558072]\",\"['Hospital Indemnity', 'Accident', 'STD']\",1.0,\"Hospital Indemnity: Recommended due to fair health condition, age 38, or chronic conditions.; Accident: Recommended for active lifestyle and occasional travel frequency.; STD: Income protection for Medium ($50K–$100K) earners or fair health status.\",\"['Employee Assistance', 'Wellness Programs', 'FSA']\",\"[0.5, 0.5, 0.5]\"\r\nEMP011,Steven,Jackson,Male,18/12/1984,852 Hickory Drive,Nashville,TN,37201,Married,65000,Y,Y,Y,Catherine Jackson,14/02/1982,Female,Spouse,,,,,,,,,1,Steven Jackson,40.0,43.0,,,,,,,Part-Time,,Manufacturing,,Y,N,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Desk,Urban,Fair,Y,Regular,Medium ($50K–$100K),Low,Moderate,Rare,N,N,PPO,0.67414844,\"Medium income, urban area - EPO for network-based care.\",\"['PPO', 'POS', 'HMO']\",\"[0.67414844, 0.22350316, 0.06022502]\",\"['Dental', 'Vision', 'Accident', 'Critical Illness', 'STD']\",1.0,Dental: Essential oral health coverage for all employees.; Vision: Eye care coverage for all employees.; Critical Illness: Important protection for age 40 with fair health status.; STD: Income protection for Medium ($50K–$100K) earners or fair health status.,\"['Employee Assistance', 'Wellness Programs', 'FSA']\",\"[0.5, 0.5, 0.5]\"\r\nEMP012,Nicole,White,Female,07/04/1977,963 Dogwood Lane,Charlotte,NC,28201,Not Married,51000,Y,Y,Y,Madison White,22/05/2006,Female,Child,Jacob White,11/10/2009,Male,Child,,,,,2,Nicole White,48.0,19.0,15.0,,,,,,Full-Time,,Manufacturing,,N,N,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Desk,Urban,Good,N,None,Medium ($50K–$100K),Medium,Active,Occasional,N,N,POS,0.88392097,\"Medium income, good health - PPO for balanced cost and flexibility.\",\"['POS', 'HMO', 'PPO']\",\"[0.88392097, 0.05343069, 0.043168128]\",\"['Dental', 'Vision']\",0.5,Default benefits assignment due to prediction error,\"['Dental', 'Vision', 'Hospital Indemnity']\",\"[0.5, 0.5, 0.5]\"\r\nEMP013,Brian,Harris,Male,21/08/1989,159 Fir Court,Orlando,FL,32801,Not Married,37000,N,N,N,,,,,,,,,,,,,0,Brian Harris,35.0,,,,,,,,Contract,,Information Technology,,N,N,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Desk,Urban,Good,N,None,Low (<$50K),Low,Active,Rare,N,Y,HMO,0.80960244,Middle age with dependents - PPO for family coverage.,\"['HMO', 'POS', 'MEC']\",\"[0.80960244, 0.08367366, 0.04964098]\",\"['Dental', 'Vision']\",0.5,Default benefits assignment due to prediction error,\"['Dental', 'Vision', 'Hospital Indemnity']\",\"[0.5, 0.5, 0.5]\"\r\nEMP014,Ashley,Martin,Female,26/01/1982,357 Gum Street,Las Vegas,NV,89101,Married,87000,Y,Y,Y,Matthew Martin,18/06/1984,Male,Spouse,Chloe Martin,05/01/2012,Female,Child,Aiden Martin,12/07/2014,Male,Child,3,Ashley Martin,43.0,41.0,13.0,10.0,,,,,Full-Time,,Sales,,N,N,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Desk,Suburban,Excellent,N,None,High (>$100K),Medium,Moderate,Frequent,Y,N,HDHP + HSA,0.8172309,\"High income, good health, HSA familiar, no chronic conditions - HDHP + HSA for tax benefits.\",\"['HDHP + HSA', 'POS', 'PPO']\",\"[0.8172309, 0.*********, 0.05131444]\",\"['Dental', 'Vision']\",0.5,Default benefits assignment due to prediction error,\"['Dental', 'Vision', 'Hospital Indemnity']\",\"[0.5, 0.5, 0.5]\"\r\nEMP015,Joshua,Thompson,Male,13/05/1986,468 Juniper Way,Salt Lake City,UT,84101,Not Married,46000,Y,Y,Y,Logan Thompson,28/09/2011,Male,Child,,,,,,,,,1,Joshua Thompson,39.0,13.0,,,,,,,Part-Time,,Engineering,,N,N,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Desk,Suburban,Good,N,None,Medium ($50K–$100K),Low,Active,Occasional,N,Y,POS,0.8694688,\"Medium income, good health - PPO for balanced cost and flexibility.\",\"['POS', 'PPO', 'HMO']\",\"[0.8694688, 0.*********, 0.*********]\",\"['Dental', 'Vision']\",0.5,Default benefits assignment due to prediction error,\"['Dental', 'Vision', 'Hospital Indemnity']\",\"[0.5, 0.5, 0.5]\"\r\n", "comprehensive_statistics": {"basic_demographics": {"age_statistics": {"average_age": 41.8, "median_age": 41.0, "age_range": {"min": 35, "max": 49}, "age_distribution": {"18-30": 0, "31-45": 12, "46-60": 3, "60+": 0}}, "gender_composition": {"counts": {"Male": 8, "Female": 7}, "percentages": {"Male": 53.33, "Female": 46.67}}, "marital_status_distribution": {"counts": {"Not Married": 8, "Married": 7}, "percentages": {"Not Married": 53.33, "Married": 46.67}}}, "dependent_analysis": {"dependent_count_distribution": {"average_dependents": 1.5333333333333334, "median_dependents": 2.0, "distribution": {"2": 5, "1": 4, "3": 3, "0": 3}, "employees_with_dependents": 12, "percentage_with_dependents": 80.0}, "dependent_age_analysis": {"total_dependents_found": 23, "average_dependent_age": 22.17, "median_dependent_age": 15.0, "dependents_over_26_as_children": 0, "age_distribution": {"0-5": 0, "6-12": 4, "13-18": 10, "19-26": 2, "26+": 7}}}, "employment_demographics": {"department_distribution": {"counts": {"Manufacturing": 5, "Engineering": 5, "Sales": 3, "Information Technology": 2}, "percentages": {"Manufacturing": 33.33, "Engineering": 33.33, "Sales": 20.0, "Information Technology": 13.33}}, "employment_type_distribution": {"counts": {"Full-Time": 10, "Part-Time": 3, "Contract": 2}, "percentages": {"Full-Time": 66.67, "Part-Time": 20.0, "Contract": 13.33}}, "job_type_distribution": {"counts": {"Desk": 15}, "percentages": {"Desk": 100.0}}, "employee_class_distribution": {"counts": {}, "percentages": {}}, "salary_analysis": {"average_salary": 60933.33, "median_salary": 61000.0, "salary_range": {"min": "37000", "max": "91000"}, "salary_distribution": {"under_40k": 2, "40k_75k": 10, "75k_100k": 3, "over_100k": 0}}}, "health_and_lifestyle": {"health_condition_distribution": {"counts": {"Good": 7, "Excellent": 5, "Fair": 3}, "percentages": {"Good": 46.67, "Excellent": 33.33, "Fair": 20.0}}, "chronic_condition_distribution": {"counts": {"N": 13, "Y": 2}, "percentages": {"N": 86.67, "Y": 13.33}}, "tobacco_use_distribution": {"counts": {"N": 12, "Y": 3}, "percentages": {"N": 80.0, "Y": 20.0}}, "lifestyle_distribution": {"counts": {"Moderate": 8, "Active": 7}, "percentages": {"Moderate": 53.33, "Active": 46.67}}, "prescription_use_distribution": {"counts": {"None": 12, "Occasional": 2, "Regular": 1}, "percentages": {"None": 80.0, "Occasional": 13.33, "Regular": 6.67}}}, "coverage_analysis": {"coverage_tier_distribution": {"counts": {}, "percentages": {}}, "coverage_tier_by_family_size": {}, "medical_plan_distribution": {"counts": {"Y": 12, "N": 3}, "percentages": {"Y": 80.0, "N": 20.0}}, "dental_plan_distribution": {"counts": {"Y": 11, "N": 4}, "percentages": {"Y": 73.33, "N": 26.67}}, "vision_plan_distribution": {"counts": {"Y": 10, "N": 5}, "percentages": {"Y": 66.67, "N": 33.33}}, "life_plan_distribution": {"counts": {}, "percentages": {}}}, "geographic_distribution": {"state_distribution": {"counts": {"FL": 2, "IL": 1, "WI": 1, "TX": 1, "OR": 1, "CO": 1, "AZ": 1, "WA": 1, "MA": 1, "GA": 1, "TN": 1, "NC": 1, "NV": 1, "UT": 1}, "percentages": {"FL": 13.33, "IL": 6.67, "WI": 6.67, "TX": 6.67, "OR": 6.67, "CO": 6.67, "AZ": 6.67, "WA": 6.67, "MA": 6.67, "GA": 6.67, "TN": 6.67, "NC": 6.67, "NV": 6.67, "UT": 6.67}}, "region_distribution": {"counts": {"Urban": 10, "Suburban": 4, "Rural": 1}, "percentages": {"Urban": 66.67, "Suburban": 26.67, "Rural": 6.67}}, "top_cities": {"counts": {"Springfield": 1, "Madison": 1, "Austin": 1, "Portland": 1, "Denver": 1, "Phoenix": 1, "Seattle": 1, "Miami": 1, "Boston": 1, "Atlanta": 1}, "percentages": {"Springfield": 6.67, "Madison": 6.67, "Austin": 6.67, "Portland": 6.67, "Denver": 6.67, "Phoenix": 6.67, "Seattle": 6.67, "Miami": 6.67, "Boston": 6.67, "Atlanta": 6.67}}}, "financial_demographics": {"income_tier_distribution": {"counts": {"Medium ($50K–$100K)": 10, "High (>$100K)": 3, "Low (<$50K)": 2}, "percentages": {"Medium ($50K–$100K)": 66.67, "High (>$100K)": 20.0, "Low (<$50K)": 13.33}}, "risk_tolerance_distribution": {"counts": {"Low": 8, "Medium": 6, "High": 1}, "percentages": {"Low": 53.33, "Medium": 40.0, "High": 6.67}}, "hsa_familiarity_distribution": {"counts": {"N": 11, "Y": 4}, "percentages": {"N": 73.33, "Y": 26.67}}}, "risk_assessment": {"group_risk_score": 25.93, "group_risk_level": "Low", "risk_distribution": {"low_risk": 11, "medium_risk": 3, "high_risk": 1}, "risk_statistics": {"min_risk_score": 15, "max_risk_score": 68, "median_risk_score": 20.0, "std_risk_score": 14.26}, "top_risk_factors": {"Tobacco Use": 3, "Poor Health Condition": 3, "Chronic Conditions": 2}, "individual_risks": [{"employee_id": "EMP001", "risk_score": 38, "risk_level": "Medium", "risk_factors": ["Tobacco user"]}, {"employee_id": "EMP002", "risk_score": 23, "risk_level": "Low", "risk_factors": []}, {"employee_id": "EMP003", "risk_score": 18, "risk_level": "Low", "risk_factors": []}, {"employee_id": "EMP004", "risk_score": 15, "risk_level": "Low", "risk_factors": []}, {"employee_id": "EMP005", "risk_score": 23, "risk_level": "Low", "risk_factors": []}, {"employee_id": "EMP006", "risk_score": 18, "risk_level": "Low", "risk_factors": []}, {"employee_id": "EMP007", "risk_score": 45, "risk_level": "Medium", "risk_factors": ["Health condition: Fair", "Has chronic condition"]}, {"employee_id": "EMP008", "risk_score": 15, "risk_level": "Low", "risk_factors": []}, {"employee_id": "EMP009", "risk_score": 33, "risk_level": "Medium", "risk_factors": ["Tobacco user"]}, {"employee_id": "EMP010", "risk_score": 25, "risk_level": "Low", "risk_factors": ["Health condition: Fair"]}]}, "data_quality_metrics": {"overall_completeness": {"total_cells": 2130, "missing_cells": "1535", "completeness_percentage": 27.93}, "column_completeness": {"employee_id": {"missing_count": "0", "completeness_percentage": 100.0}, "first_name": {"missing_count": "0", "completeness_percentage": 100.0}, "last_name": {"missing_count": "0", "completeness_percentage": 100.0}, "gender": {"missing_count": "0", "completeness_percentage": 100.0}, "dob": {"missing_count": "0", "completeness_percentage": 100.0}, "address1": {"missing_count": "0", "completeness_percentage": 100.0}, "city": {"missing_count": "0", "completeness_percentage": 100.0}, "state": {"missing_count": "0", "completeness_percentage": 100.0}, "zipcode": {"missing_count": "0", "completeness_percentage": 100.0}, "marital_status": {"missing_count": "0", "completeness_percentage": 100.0}, "salary": {"missing_count": "0", "completeness_percentage": 100.0}, "medical_plan": {"missing_count": "0", "completeness_percentage": 100.0}, "dental_plan": {"missing_count": "0", "completeness_percentage": 100.0}, "vision_plan": {"missing_count": "0", "completeness_percentage": 100.0}, "dept_1": {"missing_count": "3", "completeness_percentage": 80.0}, "dept_1_dob": {"missing_count": "3", "completeness_percentage": 80.0}, "dept_1_gender": {"missing_count": "3", "completeness_percentage": 80.0}, "relationship_type_1": {"missing_count": "3", "completeness_percentage": 80.0}, "dept_2": {"missing_count": "7", "completeness_percentage": 53.33}, "dept_2_dob": {"missing_count": "7", "completeness_percentage": 53.33}, "dept_2_gender": {"missing_count": "7", "completeness_percentage": 53.33}, "relationship_type_2": {"missing_count": "7", "completeness_percentage": 53.33}, "dept_3": {"missing_count": "12", "completeness_percentage": 20.0}, "dept_3_dob": {"missing_count": "12", "completeness_percentage": 20.0}, "dept_3_gender": {"missing_count": "12", "completeness_percentage": 20.0}, "relationship_type_3": {"missing_count": "12", "completeness_percentage": 20.0}, "dept_count": {"missing_count": "0", "completeness_percentage": 100.0}, "name": {"missing_count": "0", "completeness_percentage": 100.0}, "age": {"missing_count": "0", "completeness_percentage": 100.0}, "dept_1_age": {"missing_count": "3", "completeness_percentage": 80.0}, "dept_2_age": {"missing_count": "7", "completeness_percentage": 53.33}, "dept_3_age": {"missing_count": "12", "completeness_percentage": 20.0}, "middle_name": {"missing_count": "15", "completeness_percentage": 0.0}, "address2": {"missing_count": "15", "completeness_percentage": 0.0}, "relationship": {"missing_count": "15", "completeness_percentage": 0.0}, "record_type": {"missing_count": "15", "completeness_percentage": 0.0}, "employment_type": {"missing_count": "0", "completeness_percentage": 100.0}, "employee_class": {"missing_count": "15", "completeness_percentage": 0.0}, "department": {"missing_count": "0", "completeness_percentage": 100.0}, "hire_date": {"missing_count": "15", "completeness_percentage": 0.0}, "tobacco_use": {"missing_count": "0", "completeness_percentage": 100.0}, "pregnancy_status": {"missing_count": "0", "completeness_percentage": 100.0}, "life_plan": {"missing_count": "15", "completeness_percentage": 0.0}, "add_plan": {"missing_count": "15", "completeness_percentage": 0.0}, "coverage_tier": {"missing_count": "15", "completeness_percentage": 0.0}, "ssn": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_4": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_4_dob": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_4_age": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_4_gender": {"missing_count": "15", "completeness_percentage": 0.0}, "relationship_type_4": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_5": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_5_dob": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_5_age": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_5_gender": {"missing_count": "15", "completeness_percentage": 0.0}, "relationship_type_5": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_6": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_6_dob": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_6_age": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_6_gender": {"missing_count": "15", "completeness_percentage": 0.0}, "relationship_type_6": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_7": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_7_dob": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_7_age": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_7_gender": {"missing_count": "15", "completeness_percentage": 0.0}, "relationship_type_7": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_8": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_8_dob": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_8_age": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_8_gender": {"missing_count": "15", "completeness_percentage": 0.0}, "relationship_type_8": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_9": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_9_dob": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_9_age": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_9_gender": {"missing_count": "15", "completeness_percentage": 0.0}, "relationship_type_9": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_10": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_10_dob": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_10_age": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_10_gender": {"missing_count": "15", "completeness_percentage": 0.0}, "relationship_type_10": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_11": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_11_dob": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_11_age": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_11_gender": {"missing_count": "15", "completeness_percentage": 0.0}, "relationship_type_11": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_12": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_12_dob": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_12_age": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_12_gender": {"missing_count": "15", "completeness_percentage": 0.0}, "relationship_type_12": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_13": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_13_dob": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_13_age": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_13_gender": {"missing_count": "15", "completeness_percentage": 0.0}, "relationship_type_13": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_14": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_14_dob": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_14_age": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_14_gender": {"missing_count": "15", "completeness_percentage": 0.0}, "relationship_type_14": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_15": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_15_dob": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_15_age": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_15_gender": {"missing_count": "15", "completeness_percentage": 0.0}, "relationship_type_15": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_16": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_16_dob": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_16_age": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_16_gender": {"missing_count": "15", "completeness_percentage": 0.0}, "relationship_type_16": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_17": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_17_dob": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_17_age": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_17_gender": {"missing_count": "15", "completeness_percentage": 0.0}, "relationship_type_17": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_18": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_18_dob": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_18_age": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_18_gender": {"missing_count": "15", "completeness_percentage": 0.0}, "relationship_type_18": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_19": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_19_dob": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_19_age": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_19_gender": {"missing_count": "15", "completeness_percentage": 0.0}, "relationship_type_19": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_20": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_20_dob": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_20_age": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_20_gender": {"missing_count": "15", "completeness_percentage": 0.0}, "relationship_type_20": {"missing_count": "15", "completeness_percentage": 0.0}, "job_type": {"missing_count": "0", "completeness_percentage": 100.0}, "region": {"missing_count": "0", "completeness_percentage": 100.0}, "health_condition": {"missing_count": "0", "completeness_percentage": 100.0}, "chronic_condition": {"missing_count": "0", "completeness_percentage": 100.0}, "prescription_use": {"missing_count": "0", "completeness_percentage": 100.0}, "income_tier": {"missing_count": "0", "completeness_percentage": 100.0}, "risk_tolerance": {"missing_count": "0", "completeness_percentage": 100.0}, "lifestyle": {"missing_count": "0", "completeness_percentage": 100.0}, "travel_frequency": {"missing_count": "0", "completeness_percentage": 100.0}, "hsa_familiarity": {"missing_count": "0", "completeness_percentage": 100.0}, "mental_health_needs": {"missing_count": "0", "completeness_percentage": 100.0}}, "critical_field_completeness": {"name": {"missing_count": "0", "completeness_percentage": 100.0}, "gender": {"missing_count": "0", "completeness_percentage": 100.0}, "zipcode": {"missing_count": "0", "completeness_percentage": 100.0}, "marital_status": {"missing_count": "0", "completeness_percentage": 100.0}}}}, "prediction_summary": {"total_employees": 15, "plan_type_distribution": {"successful_predictions": {"POS": 6, "HMO": 4, "HDHP + HSA": 3, "PPO": 2}, "failed_predictions": "0", "success_rate": 100.0, "total_predicted": "15"}, "benefits_distribution": {}, "confidence_metrics": {"plan_confidence": {"mean": "0.766", "min": "0.342", "max": "0.908", "count": 15}}, "prediction_success_rates": {}, "top_3_plan_probabilities": []}, "total_employees": 15, "total_columns": 152, "has_predictions": true, "prediction_columns": ["predicted_plan_type", "plan_confidence"]}, "metadata": {"file_processing": {"filename": "col2_mixed_variations.csv", "size": 2723, "original_column_names": ["EmpID", "FirstName", "LastName", "Sex", "DateOfBirth", "HomeAddress", "Town", "Province", "PostalCode", "MaritalStat", "AnnualSalary", "HealthPlan", "DentalCoverage", "VisionBenefit", "Dependent1Name", "Dep1DOB", "Dep1Sex", "Dep1Relation", "Dependent2Name", "Dep2DOB", "Dep2Sex", "Dep2Relation", "Dependent3Name", "Dep3DOB", "Dep3Sex", "Dep3Relation", "NumberOfDependents"]}, "pattern_analysis": {"pattern_type": "column_based_single_row", "pattern_confidence": 1.4000000000000001, "pattern_reason": "Found 9 dependent columns; Found numbered dependent patterns: [1, 2, 3]; Found 3 relationship type columns; Found 'dept_count' column; No duplicate employee_ids (consistent with single row per employee)", "analysis_details": {"confidence": 1.4000000000000001, "reason": "Found 9 dependent columns; Found numbered dependent patterns: [1, 2, 3]; Found 3 relationship type columns; Found 'dept_count' column; No duplicate employee_ids (consistent with single row per employee)"}}, "field_mapping": {"total_fields_mapped": 27, "mapping_success": true, "unmapped_fields": [], "mapping_confidence": 0, "field_mapping_details": {"EmpID": "employee_id", "FirstName": "first_name", "LastName": "last_name", "Sex": "gender", "DateOfBirth": "dob", "HomeAddress": "address1", "Town": "city", "Province": "state", "PostalCode": "zipcode", "MaritalStat": "marital_status", "AnnualSalary": "salary", "HealthPlan": "medical_plan", "DentalCoverage": "dental_plan", "VisionBenefit": "vision_plan", "Dependent1Name": "dept_1", "Dep1DOB": "dept_1_dob", "Dep1Sex": "dept_1_gender", "Dep1Relation": "relationship_type_1", "Dependent2Name": "dept_2", "Dep2DOB": "dept_2_dob", "Dep2Sex": "dept_2_gender", "Dep2Relation": "relationship_type_2", "Dependent3Name": "dept_3", "Dep3DOB": "dept_3_dob", "Dep3Sex": "dept_3_gender", "Dep3Relation": "relationship_type_3", "NumberOfDependents": "dept_count"}}, "data_processing": {"validation_passed": false, "processing_summary": {"original_rows": 15, "original_columns": 27, "processed_rows": 15, "processed_columns": 131, "processing_time_seconds": 7.14, "validation_passed": true, "validation_errors": 0, "error_rows": 0, "fields_processed": {"name_fields": true, "age_converted": true, "gender_standardized": true, "addresses_cleaned": true, "zipcode_validated": true}, "data_quality": {"complete_records": 15, "missing_data_rows": 0}}, "data_quality_score": 0.8, "processing_details": {"success": true, "preprocessed_data": "   employee_id   first_name last_name  gender         dob           address1  ... relationship_type_19 dept_20  dept_20_dob dept_20_age  dept_20_gender relationship_type_20\n0       EMP001     <PERSON>    Male  15/01/1980     123 Oak Street  ...                 None    None         None        None            None                 None\n1       EMP002       <PERSON>  Female  10/03/1975    456 Pine Avenue  ...                 None    None         None        None            None                 None\n2       EMP003  <PERSON>    Male  30/06/1990     789 Maple Road  ...                 None    None         None        None            None                 None\n3       EMP004     <PERSON>  05/11/1985      321 Elm Drive  ...                 None    None         None        None            None                 None\n4       EMP005       <PERSON>  28/02/1978     654 Cedar Lane  ...                 None    None         None        None            None                 None\n5       EMP006      <PERSON>  Female  12/07/1983    987 Birch Court  ...                 None    None         None        None            None                 None\n6       EMP007       <PERSON>    Male  03/12/1987     147 Spruce Way  ...                 None    None         None        None            None                 None\n7       EMP008         <PERSON>  Female  17/10/1979  258 Willow Street  ...                 None    None         None        None            None                 None\n8       EMP009        <PERSON>    Male  24/03/1981  369 Poplar Avenue  ...                 None    None         None        None            None                 None\n9       EMP010       <PERSON>  09/09/1986       741 Ash Road  ...                 None    None         None        None            None                 None\n10      EMP011       <PERSON>    Male  18/12/1984  852 Hickory Drive  ...                 None    None         None        None            None                 None\n11      EMP012       <PERSON>  Female  07/04/1977   963 Dogwood Lane  ...                 None    None         None        None            None                 None\n12      EMP013        <PERSON>  21/08/1989      159 Fir Court  ...                 None    None         None        None            None                 None\n13      EMP014       <PERSON>  Female  26/01/1982     357 Gum Street  ...                 None    None         None        None            None                 None\n14      EMP015       Joshua  Thompson    Male  13/05/1986    468 Juniper Way  ...                 None    None         None        None            None                 None\n\n[15 rows x 131 columns]", "validation": {"is_valid": true, "errors": [], "error_rows": [], "total_errors": 0}, "summary": {"original_rows": 15, "original_columns": 27, "processed_rows": 15, "processed_columns": 131, "processing_time_seconds": 7.14, "validation_passed": true, "validation_errors": 0, "error_rows": 0, "fields_processed": {"name_fields": true, "age_converted": true, "gender_standardized": true, "addresses_cleaned": true, "zipcode_validated": true}, "data_quality": {"complete_records": 15, "missing_data_rows": 0}}}}, "enrichment_and_prediction": {"success": true, "enriched_data": "   employee_id   first_name last_name  ...                                    benefits_reason                                 top_3_benefits top_3_benefits_confidences\n0       EMP001     <PERSON>  ...  Dental: Essential oral health coverage for all...  [Employee Assistance, Wellness Programs, FSA]            [0.5, 0.5, 0.5]\n1       EMP002       <PERSON>  ...  Dental: Essential oral health coverage for all...  [Employee Assistance, Wellness Programs, FSA]            [0.5, 0.5, 0.5]\n2       EMP003  <PERSON>  ...                                ML model prediction  [Employee Assistance, Wellness Programs, FSA]            [0.5, 0.5, 0.5]\n3       EMP004     <PERSON>  ...  Dental: Essential oral health coverage for all...  [Employee Assistance, Wellness Programs, FSA]            [0.5, 0.5, 0.5]\n4       EMP005       <PERSON>  ...                                ML model prediction  [Employee Assistance, Wellness Programs, FSA]            [0.5, 0.5, 0.5]\n5       EMP006      <PERSON>  ...                                ML model prediction  [Employee Assistance, Wellness Programs, FSA]            [0.5, 0.5, 0.5]\n6       EMP007       <PERSON>  ...  Dental: Essential oral health coverage for all...  [Employee Assistance, Wellness Programs, FSA]            [0.5, 0.5, 0.5]\n7       EMP008         <PERSON>  ...  Dental: Essential oral health coverage for all...  [Employee Assistance, Wellness Programs, FSA]            [0.5, 0.5, 0.5]\n8       EMP009        Kevin  <PERSON>  ...  Dental: Essential oral health coverage for all...  [Employee Assistance, Wellness Programs, FSA]            [0.5, 0.5, 0.5]\n9       EMP010       Rachel    Thomas  ...  Hospital Indemnity: Recommended due to fair he...  [Employee Assistance, Wellness Programs, FSA]            [0.5, 0.5, 0.5]\n10      EMP011       Steven   Jackson  ...  Dental: Essential oral health coverage for all...  [Employee Assistance, Wellness Programs, FSA]            [0.5, 0.5, 0.5]\n11      EMP012       Nicole     White  ...  Default benefits assignment due to prediction ...           [Dental, Vision, Hospital Indemnity]            [0.5, 0.5, 0.5]\n12      EMP013        Brian    Harris  ...  Default benefits assignment due to prediction ...           [Dental, Vision, Hospital Indemnity]            [0.5, 0.5, 0.5]\n13      EMP014       Ashley    Martin  ...  Default benefits assignment due to prediction ...           [Dental, Vision, Hospital Indemnity]            [0.5, 0.5, 0.5]\n14      EMP015       Joshua  Thompson  ...  Default benefits assignment due to prediction ...           [Dental, Vision, Hospital Indemnity]            [0.5, 0.5, 0.5]\n\n[15 rows x 152 columns]", "enrichment_summary": {"total_employees": 15, "features_analyzed": 19, "enrichment_details": {"age": {"original_missing": "0", "final_missing": "0", "enriched_count": "0", "completion_rate": 100.0}, "gender": {"original_missing": "0", "final_missing": "0", "enriched_count": "0", "completion_rate": 100.0}, "marital_status": {"original_missing": "0", "final_missing": "0", "enriched_count": "0", "completion_rate": 100.0}, "dept_count": {"original_missing": "0", "final_missing": "0", "enriched_count": "0", "completion_rate": 100.0}, "job_type": {"original_missing": 15, "final_missing": "0", "enriched_count": "15", "completion_rate": 100.0}, "employment_type": {"original_missing": "15", "final_missing": "0", "enriched_count": "15", "completion_rate": 100.0}, "department": {"original_missing": "15", "final_missing": "0", "enriched_count": "15", "completion_rate": 100.0}, "region": {"original_missing": 15, "final_missing": "0", "enriched_count": "15", "completion_rate": 100.0}, "health_condition": {"original_missing": 15, "final_missing": "0", "enriched_count": "15", "completion_rate": 100.0}, "chronic_condition": {"original_missing": 15, "final_missing": "0", "enriched_count": "15", "completion_rate": 100.0}, "prescription_use": {"original_missing": 15, "final_missing": "0", "enriched_count": "15", "completion_rate": 100.0}, "income_tier": {"original_missing": 15, "final_missing": "0", "enriched_count": "15", "completion_rate": 100.0}, "risk_tolerance": {"original_missing": 15, "final_missing": "0", "enriched_count": "15", "completion_rate": 100.0}, "lifestyle": {"original_missing": 15, "final_missing": "0", "enriched_count": "15", "completion_rate": 100.0}, "travel_frequency": {"original_missing": 15, "final_missing": "0", "enriched_count": "15", "completion_rate": 100.0}, "hsa_familiarity": {"original_missing": 15, "final_missing": "0", "enriched_count": "15", "completion_rate": 100.0}, "pregnancy_status": {"original_missing": "15", "final_missing": "0", "enriched_count": "15", "completion_rate": 100.0}, "tobacco_use": {"original_missing": "15", "final_missing": "0", "enriched_count": "15", "completion_rate": 100.0}, "mental_health_needs": {"original_missing": 15, "final_missing": "0", "enriched_count": "15", "completion_rate": 100.0}}, "data_quality_improvement": {"total_missing_before": "225", "total_missing_after": "0", "total_enriched": "225", "overall_completion_rate": 100.0}}, "comprehensive_statistics": {"basic_demographics": {"age_statistics": {"average_age": 41.8, "median_age": 41.0, "age_range": {"min": 35, "max": 49}, "age_distribution": {"18-30": 0, "31-45": 12, "46-60": 3, "60+": 0}}, "gender_composition": {"counts": {"Male": 8, "Female": 7}, "percentages": {"Male": 53.33, "Female": 46.67}}, "marital_status_distribution": {"counts": {"Not Married": 8, "Married": 7}, "percentages": {"Not Married": 53.33, "Married": 46.67}}}, "dependent_analysis": {"dependent_count_distribution": {"average_dependents": 1.5333333333333334, "median_dependents": 2.0, "distribution": {"2": 5, "1": 4, "3": 3, "0": 3}, "employees_with_dependents": 12, "percentage_with_dependents": 80.0}, "dependent_age_analysis": {"total_dependents_found": 23, "average_dependent_age": 22.17, "median_dependent_age": 15.0, "dependents_over_26_as_children": 0, "age_distribution": {"0-5": 0, "6-12": 4, "13-18": 10, "19-26": 2, "26+": 7}}}, "employment_demographics": {"department_distribution": {"counts": {"Manufacturing": 5, "Engineering": 5, "Sales": 3, "Information Technology": 2}, "percentages": {"Manufacturing": 33.33, "Engineering": 33.33, "Sales": 20.0, "Information Technology": 13.33}}, "employment_type_distribution": {"counts": {"Full-Time": 10, "Part-Time": 3, "Contract": 2}, "percentages": {"Full-Time": 66.67, "Part-Time": 20.0, "Contract": 13.33}}, "job_type_distribution": {"counts": {"Desk": 15}, "percentages": {"Desk": 100.0}}, "employee_class_distribution": {"counts": {}, "percentages": {}}, "salary_analysis": {"average_salary": 60933.33, "median_salary": 61000.0, "salary_range": {"min": "37000", "max": "91000"}, "salary_distribution": {"under_40k": 2, "40k_75k": 10, "75k_100k": 3, "over_100k": 0}}}, "health_and_lifestyle": {"health_condition_distribution": {"counts": {"Good": 7, "Excellent": 5, "Fair": 3}, "percentages": {"Good": 46.67, "Excellent": 33.33, "Fair": 20.0}}, "chronic_condition_distribution": {"counts": {"N": 13, "Y": 2}, "percentages": {"N": 86.67, "Y": 13.33}}, "tobacco_use_distribution": {"counts": {"N": 12, "Y": 3}, "percentages": {"N": 80.0, "Y": 20.0}}, "lifestyle_distribution": {"counts": {"Moderate": 8, "Active": 7}, "percentages": {"Moderate": 53.33, "Active": 46.67}}, "prescription_use_distribution": {"counts": {"None": 12, "Occasional": 2, "Regular": 1}, "percentages": {"None": 80.0, "Occasional": 13.33, "Regular": 6.67}}}, "coverage_analysis": {"coverage_tier_distribution": {"counts": {}, "percentages": {}}, "coverage_tier_by_family_size": {}, "medical_plan_distribution": {"counts": {"Y": 12, "N": 3}, "percentages": {"Y": 80.0, "N": 20.0}}, "dental_plan_distribution": {"counts": {"Y": 11, "N": 4}, "percentages": {"Y": 73.33, "N": 26.67}}, "vision_plan_distribution": {"counts": {"Y": 10, "N": 5}, "percentages": {"Y": 66.67, "N": 33.33}}, "life_plan_distribution": {"counts": {}, "percentages": {}}}, "geographic_distribution": {"state_distribution": {"counts": {"FL": 2, "IL": 1, "WI": 1, "TX": 1, "OR": 1, "CO": 1, "AZ": 1, "WA": 1, "MA": 1, "GA": 1, "TN": 1, "NC": 1, "NV": 1, "UT": 1}, "percentages": {"FL": 13.33, "IL": 6.67, "WI": 6.67, "TX": 6.67, "OR": 6.67, "CO": 6.67, "AZ": 6.67, "WA": 6.67, "MA": 6.67, "GA": 6.67, "TN": 6.67, "NC": 6.67, "NV": 6.67, "UT": 6.67}}, "region_distribution": {"counts": {"Urban": 10, "Suburban": 4, "Rural": 1}, "percentages": {"Urban": 66.67, "Suburban": 26.67, "Rural": 6.67}}, "top_cities": {"counts": {"Springfield": 1, "Madison": 1, "Austin": 1, "Portland": 1, "Denver": 1, "Phoenix": 1, "Seattle": 1, "Miami": 1, "Boston": 1, "Atlanta": 1}, "percentages": {"Springfield": 6.67, "Madison": 6.67, "Austin": 6.67, "Portland": 6.67, "Denver": 6.67, "Phoenix": 6.67, "Seattle": 6.67, "Miami": 6.67, "Boston": 6.67, "Atlanta": 6.67}}}, "financial_demographics": {"income_tier_distribution": {"counts": {"Medium ($50K–$100K)": 10, "High (>$100K)": 3, "Low (<$50K)": 2}, "percentages": {"Medium ($50K–$100K)": 66.67, "High (>$100K)": 20.0, "Low (<$50K)": 13.33}}, "risk_tolerance_distribution": {"counts": {"Low": 8, "Medium": 6, "High": 1}, "percentages": {"Low": 53.33, "Medium": 40.0, "High": 6.67}}, "hsa_familiarity_distribution": {"counts": {"N": 11, "Y": 4}, "percentages": {"N": 73.33, "Y": 26.67}}}, "risk_assessment": {"group_risk_score": 25.93, "group_risk_level": "Low", "risk_distribution": {"low_risk": 11, "medium_risk": 3, "high_risk": 1}, "risk_statistics": {"min_risk_score": 15, "max_risk_score": 68, "median_risk_score": 20.0, "std_risk_score": 14.26}, "top_risk_factors": {"Tobacco Use": 3, "Poor Health Condition": 3, "Chronic Conditions": 2}, "individual_risks": [{"employee_id": "EMP001", "risk_score": 38, "risk_level": "Medium", "risk_factors": ["Tobacco user"]}, {"employee_id": "EMP002", "risk_score": 23, "risk_level": "Low", "risk_factors": []}, {"employee_id": "EMP003", "risk_score": 18, "risk_level": "Low", "risk_factors": []}, {"employee_id": "EMP004", "risk_score": 15, "risk_level": "Low", "risk_factors": []}, {"employee_id": "EMP005", "risk_score": 23, "risk_level": "Low", "risk_factors": []}, {"employee_id": "EMP006", "risk_score": 18, "risk_level": "Low", "risk_factors": []}, {"employee_id": "EMP007", "risk_score": 45, "risk_level": "Medium", "risk_factors": ["Health condition: Fair", "Has chronic condition"]}, {"employee_id": "EMP008", "risk_score": 15, "risk_level": "Low", "risk_factors": []}, {"employee_id": "EMP009", "risk_score": 33, "risk_level": "Medium", "risk_factors": ["Tobacco user"]}, {"employee_id": "EMP010", "risk_score": 25, "risk_level": "Low", "risk_factors": ["Health condition: Fair"]}]}, "data_quality_metrics": {"overall_completeness": {"total_cells": 2130, "missing_cells": "1535", "completeness_percentage": 27.93}, "column_completeness": {"employee_id": {"missing_count": "0", "completeness_percentage": 100.0}, "first_name": {"missing_count": "0", "completeness_percentage": 100.0}, "last_name": {"missing_count": "0", "completeness_percentage": 100.0}, "gender": {"missing_count": "0", "completeness_percentage": 100.0}, "dob": {"missing_count": "0", "completeness_percentage": 100.0}, "address1": {"missing_count": "0", "completeness_percentage": 100.0}, "city": {"missing_count": "0", "completeness_percentage": 100.0}, "state": {"missing_count": "0", "completeness_percentage": 100.0}, "zipcode": {"missing_count": "0", "completeness_percentage": 100.0}, "marital_status": {"missing_count": "0", "completeness_percentage": 100.0}, "salary": {"missing_count": "0", "completeness_percentage": 100.0}, "medical_plan": {"missing_count": "0", "completeness_percentage": 100.0}, "dental_plan": {"missing_count": "0", "completeness_percentage": 100.0}, "vision_plan": {"missing_count": "0", "completeness_percentage": 100.0}, "dept_1": {"missing_count": "3", "completeness_percentage": 80.0}, "dept_1_dob": {"missing_count": "3", "completeness_percentage": 80.0}, "dept_1_gender": {"missing_count": "3", "completeness_percentage": 80.0}, "relationship_type_1": {"missing_count": "3", "completeness_percentage": 80.0}, "dept_2": {"missing_count": "7", "completeness_percentage": 53.33}, "dept_2_dob": {"missing_count": "7", "completeness_percentage": 53.33}, "dept_2_gender": {"missing_count": "7", "completeness_percentage": 53.33}, "relationship_type_2": {"missing_count": "7", "completeness_percentage": 53.33}, "dept_3": {"missing_count": "12", "completeness_percentage": 20.0}, "dept_3_dob": {"missing_count": "12", "completeness_percentage": 20.0}, "dept_3_gender": {"missing_count": "12", "completeness_percentage": 20.0}, "relationship_type_3": {"missing_count": "12", "completeness_percentage": 20.0}, "dept_count": {"missing_count": "0", "completeness_percentage": 100.0}, "name": {"missing_count": "0", "completeness_percentage": 100.0}, "age": {"missing_count": "0", "completeness_percentage": 100.0}, "dept_1_age": {"missing_count": "3", "completeness_percentage": 80.0}, "dept_2_age": {"missing_count": "7", "completeness_percentage": 53.33}, "dept_3_age": {"missing_count": "12", "completeness_percentage": 20.0}, "middle_name": {"missing_count": "15", "completeness_percentage": 0.0}, "address2": {"missing_count": "15", "completeness_percentage": 0.0}, "relationship": {"missing_count": "15", "completeness_percentage": 0.0}, "record_type": {"missing_count": "15", "completeness_percentage": 0.0}, "employment_type": {"missing_count": "0", "completeness_percentage": 100.0}, "employee_class": {"missing_count": "15", "completeness_percentage": 0.0}, "department": {"missing_count": "0", "completeness_percentage": 100.0}, "hire_date": {"missing_count": "15", "completeness_percentage": 0.0}, "tobacco_use": {"missing_count": "0", "completeness_percentage": 100.0}, "pregnancy_status": {"missing_count": "0", "completeness_percentage": 100.0}, "life_plan": {"missing_count": "15", "completeness_percentage": 0.0}, "add_plan": {"missing_count": "15", "completeness_percentage": 0.0}, "coverage_tier": {"missing_count": "15", "completeness_percentage": 0.0}, "ssn": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_4": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_4_dob": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_4_age": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_4_gender": {"missing_count": "15", "completeness_percentage": 0.0}, "relationship_type_4": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_5": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_5_dob": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_5_age": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_5_gender": {"missing_count": "15", "completeness_percentage": 0.0}, "relationship_type_5": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_6": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_6_dob": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_6_age": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_6_gender": {"missing_count": "15", "completeness_percentage": 0.0}, "relationship_type_6": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_7": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_7_dob": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_7_age": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_7_gender": {"missing_count": "15", "completeness_percentage": 0.0}, "relationship_type_7": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_8": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_8_dob": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_8_age": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_8_gender": {"missing_count": "15", "completeness_percentage": 0.0}, "relationship_type_8": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_9": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_9_dob": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_9_age": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_9_gender": {"missing_count": "15", "completeness_percentage": 0.0}, "relationship_type_9": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_10": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_10_dob": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_10_age": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_10_gender": {"missing_count": "15", "completeness_percentage": 0.0}, "relationship_type_10": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_11": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_11_dob": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_11_age": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_11_gender": {"missing_count": "15", "completeness_percentage": 0.0}, "relationship_type_11": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_12": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_12_dob": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_12_age": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_12_gender": {"missing_count": "15", "completeness_percentage": 0.0}, "relationship_type_12": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_13": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_13_dob": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_13_age": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_13_gender": {"missing_count": "15", "completeness_percentage": 0.0}, "relationship_type_13": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_14": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_14_dob": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_14_age": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_14_gender": {"missing_count": "15", "completeness_percentage": 0.0}, "relationship_type_14": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_15": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_15_dob": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_15_age": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_15_gender": {"missing_count": "15", "completeness_percentage": 0.0}, "relationship_type_15": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_16": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_16_dob": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_16_age": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_16_gender": {"missing_count": "15", "completeness_percentage": 0.0}, "relationship_type_16": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_17": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_17_dob": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_17_age": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_17_gender": {"missing_count": "15", "completeness_percentage": 0.0}, "relationship_type_17": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_18": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_18_dob": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_18_age": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_18_gender": {"missing_count": "15", "completeness_percentage": 0.0}, "relationship_type_18": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_19": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_19_dob": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_19_age": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_19_gender": {"missing_count": "15", "completeness_percentage": 0.0}, "relationship_type_19": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_20": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_20_dob": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_20_age": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_20_gender": {"missing_count": "15", "completeness_percentage": 0.0}, "relationship_type_20": {"missing_count": "15", "completeness_percentage": 0.0}, "job_type": {"missing_count": "0", "completeness_percentage": 100.0}, "region": {"missing_count": "0", "completeness_percentage": 100.0}, "health_condition": {"missing_count": "0", "completeness_percentage": 100.0}, "chronic_condition": {"missing_count": "0", "completeness_percentage": 100.0}, "prescription_use": {"missing_count": "0", "completeness_percentage": 100.0}, "income_tier": {"missing_count": "0", "completeness_percentage": 100.0}, "risk_tolerance": {"missing_count": "0", "completeness_percentage": 100.0}, "lifestyle": {"missing_count": "0", "completeness_percentage": 100.0}, "travel_frequency": {"missing_count": "0", "completeness_percentage": 100.0}, "hsa_familiarity": {"missing_count": "0", "completeness_percentage": 100.0}, "mental_health_needs": {"missing_count": "0", "completeness_percentage": 100.0}}, "critical_field_completeness": {"name": {"missing_count": "0", "completeness_percentage": 100.0}, "gender": {"missing_count": "0", "completeness_percentage": 100.0}, "zipcode": {"missing_count": "0", "completeness_percentage": 100.0}, "marital_status": {"missing_count": "0", "completeness_percentage": 100.0}}}}, "feature_validation": {"is_valid": true, "missing_features": [], "feature_completeness": {"age": {"present": true, "missing_values": "0", "completion_rate": 100.0}, "gender": {"present": true, "missing_values": "0", "completion_rate": 100.0}, "marital_status": {"present": true, "missing_values": "0", "completion_rate": 100.0}, "dept_count": {"present": true, "missing_values": "0", "completion_rate": 100.0}, "job_type": {"present": true, "missing_values": "0", "completion_rate": 100.0}, "employment_type": {"present": true, "missing_values": "0", "completion_rate": 100.0}, "department": {"present": true, "missing_values": "0", "completion_rate": 100.0}, "region": {"present": true, "missing_values": "0", "completion_rate": 100.0}, "health_condition": {"present": true, "missing_values": "0", "completion_rate": 100.0}, "chronic_condition": {"present": true, "missing_values": "0", "completion_rate": 100.0}, "prescription_use": {"present": true, "missing_values": "0", "completion_rate": 100.0}, "income_tier": {"present": true, "missing_values": "0", "completion_rate": 100.0}, "risk_tolerance": {"present": true, "missing_values": "0", "completion_rate": 100.0}, "lifestyle": {"present": true, "missing_values": "0", "completion_rate": 100.0}, "travel_frequency": {"present": true, "missing_values": "0", "completion_rate": 100.0}, "hsa_familiarity": {"present": true, "missing_values": "0", "completion_rate": 100.0}, "pregnancy_status": {"present": true, "missing_values": "0", "completion_rate": 100.0}, "tobacco_use": {"present": true, "missing_values": "0", "completion_rate": 100.0}, "mental_health_needs": {"present": true, "missing_values": "0", "completion_rate": 100.0}}, "overall_completion_rate": 100.0, "ready_for_prediction": "True"}, "prediction_method": "ml_models", "model_predictions": {"plan_types": {"total_predictions": 15, "average_confidence": "0.76585704", "unique_plans": 4}}, "prediction_summary": {"total_employees": 15, "plan_type_distribution": {"successful_predictions": {"POS": 6, "HMO": 4, "HDHP + HSA": 3, "PPO": 2}, "failed_predictions": "0", "success_rate": 100.0, "total_predicted": "15"}, "benefits_distribution": {}, "confidence_metrics": {"plan_confidence": {"mean": "0.766", "min": "0.342", "max": "0.908", "count": 15}}, "prediction_success_rates": {}, "top_3_plan_probabilities": ["EMP001: [0.90829504, 0.*********, 0.*********]", "EMP002: [0.8381619, 0.053493556, 0.052143473]", "EMP003: [0.34228057, 0.22125721, 0.15578382]", "EMP004: [0.70652986, 0.26228836, 0.*********]", "EMP005: [0.8763457, 0.07689335, 0.*********]", "EMP006: [0.48716465, 0.17091762, 0.1201785]", "EMP007: [0.84879845, 0.08694499, 0.*********]", "EMP008: [0.8004089, 0.17146602, 0.*********]", "EMP009: [0.7610917, 0.11455104, 0.093424566]", "EMP010: [0.86440706, 0.09522333, 0.03558072]", "EMP011: [0.67414844, 0.22350316, 0.06022502]", "EMP012: [0.88392097, 0.05343069, 0.043168128]", "EMP013: [0.80960244, 0.08367366, 0.04964098]", "EMP014: [0.8172309, 0.*********, 0.05131444]", "EMP015: [0.8694688, 0.*********, 0.*********]"]}}}}}