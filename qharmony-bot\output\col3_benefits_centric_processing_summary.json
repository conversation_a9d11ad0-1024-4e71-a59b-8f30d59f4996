{"file_info": {"original_filename": "col3_benefits_centric.csv", "processing_timestamp": "2025-07-15 17:30:09.313643", "total_processing_time_seconds": 16.14430809020996}, "final_response": {"success": true, "data": {"enriched_data_csv": "employee_id,first_name,last_name,gender,dob,address1,city,state,zipcode,marital_status,salary,employment_type,hire_date,medical_plan,dental_plan,vision_plan,life_plan,add_plan,coverage_tier,tobacco_use,pregnancy_status,dept_count,name,age,middle_name,address2,relationship,record_type,employee_class,department,ssn,dept_1,dept_1_dob,dept_1_age,dept_1_gender,relationship_type_1,dept_2,dept_2_dob,dept_2_age,dept_2_gender,relationship_type_2,dept_3,dept_3_dob,dept_3_age,dept_3_gender,relationship_type_3,dept_4,dept_4_dob,dept_4_age,dept_4_gender,relationship_type_4,dept_5,dept_5_dob,dept_5_age,dept_5_gender,relationship_type_5,dept_6,dept_6_dob,dept_6_age,dept_6_gender,relationship_type_6,dept_7,dept_7_dob,dept_7_age,dept_7_gender,relationship_type_7,dept_8,dept_8_dob,dept_8_age,dept_8_gender,relationship_type_8,dept_9,dept_9_dob,dept_9_age,dept_9_gender,relationship_type_9,dept_10,dept_10_dob,dept_10_age,dept_10_gender,relationship_type_10,dept_11,dept_11_dob,dept_11_age,dept_11_gender,relationship_type_11,dept_12,dept_12_dob,dept_12_age,dept_12_gender,relationship_type_12,dept_13,dept_13_dob,dept_13_age,dept_13_gender,relationship_type_13,dept_14,dept_14_dob,dept_14_age,dept_14_gender,relationship_type_14,dept_15,dept_15_dob,dept_15_age,dept_15_gender,relationship_type_15,dept_16,dept_16_dob,dept_16_age,dept_16_gender,relationship_type_16,dept_17,dept_17_dob,dept_17_age,dept_17_gender,relationship_type_17,dept_18,dept_18_dob,dept_18_age,dept_18_gender,relationship_type_18,dept_19,dept_19_dob,dept_19_age,dept_19_gender,relationship_type_19,dept_20,dept_20_dob,dept_20_age,dept_20_gender,relationship_type_20,job_type,region,health_condition,chronic_condition,prescription_use,income_tier,risk_tolerance,lifestyle,travel_frequency,hsa_familiarity,mental_health_needs,predicted_plan_type,plan_confidence,plan_reason,top_3_plans,top_3_plan_confidences,predicted_benefits,benefits_confidence,benefits_reason,top_3_benefits,top_3_benefits_confidences\r\nE001,John,Smith,Male,1980-01-15,123 Main St,Anytown,CA,90210,Married,65000,Full-Time,2020-01-15,PPO Gold,Premium Dental,Vision Plus,Y,Y,Employee + Family,N,N,2,John Smith,45.0,,,,,,Manufacturing,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Desk,Urban,Excellent,N,Occasional,Medium ($50K–$100K),Low,Active,Rare,N,N,POS,0.799975,\"Medium income, urban area - EPO for network-based care.\",\"['POS', 'HMO', 'PPO']\",\"[0.799975, 0.09293648, 0.*********]\",\"['Dental', 'Vision', 'Accident', 'STD']\",1.0,Dental: Essential oral health coverage for all employees.; Vision: Eye care coverage for all employees.; Accident: Recommended for active lifestyle and rare travel frequency.; STD: Income protection for Medium ($50K–$100K) earners or excellent health status.,\"['Employee Assistance', 'Wellness Programs', 'FSA']\",\"[0.5, 0.5, 0.5]\"\r\nE002,Alice,Brown,Female,1975-03-10,456 Oak Ave,Somewhere,TX,75001,Not Married,52000,Full-Time,2019-05-20,HMO Silver,Basic Dental,Basic Vision,Y,N,Employee + Child,N,N,1,Alice Brown,50.0,,,,,,Manufacturing,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Desk,Urban,Good,Y,Occasional,Medium ($50K–$100K),Low,Moderate,Rare,N,N,PPO,0.519858,\"Medium income, good health - PPO for balanced cost and flexibility.\",\"['PPO', 'POS', 'HMO']\",\"[0.519858, 0.38336724, 0.08059388]\",\"['Dental', 'Vision', 'Accident', 'Critical Illness', 'STD']\",1.0,Dental: Essential oral health coverage for all employees.; Vision: Eye care coverage for all employees.; Critical Illness: Important protection for age 50 with good health status.; STD: Income protection for Medium ($50K–$100K) earners or good health status.,\"['Employee Assistance', 'Wellness Programs', 'FSA']\",\"[0.5, 0.5, 0.5]\"\r\nE003,Charlie,Davis,Male,1990-06-30,789 Pine Rd,Elsewhere,FL,33101,Married,78500,Full-Time,2021-03-10,HDHP Bronze,Premium Dental,Vision Plus,Y,Y,Employee + Family,Y,N,3,Charlie Davis,35.0,,,,,,Sales,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Desk,Urban,Excellent,N,Occasional,High (>$100K),Medium,Active,Occasional,Y,Y,HDHP,0.7276175,\"High income, good health, HSA familiar, no chronic conditions - HDHP + HSA for tax benefits.\",\"['HDHP', 'POS', 'PPO']\",\"[0.7276175, 0.*********, 0.07436879]\",\"['FSA', 'Employee Assistance']\",1.0,ML model prediction,\"['Employee Assistance', 'Wellness Programs', 'FSA']\",\"[0.5, 0.5, 0.5]\"\r\nE004,Grace,Wilson,Female,1985-11-05,321 Elm St,Nowhere,NY,10001,Not Married,45000,Part-Time,2018-08-12,PPO Silver,Basic Dental,Basic Vision,N,N,Employee + Child,N,Y,1,Grace Wilson,39.0,,,,,,Finance,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Desk,Urban,Excellent,N,None,Medium ($50K–$100K),Medium,Moderate,Occasional,N,N,HMO,0.6415711,\"Medium income, urban area - EPO for network-based care.\",\"['HMO', 'POS', 'PPO']\",\"[0.6415711, 0.33364284, 0.*********]\",\"['Dental', 'Vision', 'Hospital Indemnity', 'STD']\",1.0,Dental: Essential oral health coverage for all employees.; Vision: Eye care coverage for all employees.; STD: Income protection for Medium ($50K–$100K) earners or excellent health status.,\"['Employee Assistance', 'Wellness Programs', 'FSA']\",\"[0.5, 0.5, 0.5]\"\r\nE005,Isaac,Johnson,Male,1978-02-28,654 Maple Dr,Anywhere,WA,98001,Married,95000,Full-Time,2017-11-30,PPO Platinum,Premium Dental,Vision Plus,Y,Y,Employee + Family,N,N,3,Isaac Johnson,47.0,,,,,,Engineering,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Desk,Urban,Fair,N,Occasional,High (>$100K),Medium,Moderate,Occasional,N,N,PPO,0.7437587,Middle age with dependents - PPO for family coverage.,\"['PPO', 'POS', 'HMO']\",\"[0.7437587, 0.********, 0.*********]\",\"['Accident', 'STD', 'FSA']\",1.0,STD: Income protection for High (>$100K) earners or fair health status.; FSA: Tax-advantaged account for medical expenses due to health conditions or pregnancy.,\"['Employee Assistance', 'Wellness Programs', 'FSA']\",\"[0.5, 0.5, 0.5]\"\r\nE006,Michael,Anderson,Male,1983-07-12,987 Cedar Ln,Someplace,OR,97001,Not Married,38000,Part-Time,2022-02-14,HMO Basic,Waived,Declined,N,N,Employee Only,Y,N,0,Michael Anderson,42.0,,,,,,Sales,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Desk,Urban,Fair,N,None,Low (<$50K),Low,Moderate,Frequent,N,Y,HMO,0.5326413,\"Low income, part-time employment - MEC for minimum coverage compliance.\",\"['HMO', 'POS', 'PPO']\",\"[0.5326413, 0.********, 0.********]\",['Employee Assistance'],1.0,ML model prediction,\"['Employee Assistance', 'Wellness Programs', 'FSA']\",\"[0.5, 0.5, 0.5]\"\r\nE007,Nancy,Taylor,Female,1987-12-03,147 Birch Ave,Othertown,AZ,85001,Married,72000,Full-Time,2019-09-05,PPO Gold,Premium Dental,Vision Plus,Y,Y,Employee + Spouse,N,Y,1,Nancy Taylor,37.0,,,,,,Information Technology,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Desk,Urban,Excellent,N,Occasional,Medium ($50K–$100K),Medium,Moderate,Occasional,N,N,HMO,0.8127314,\"Medium income, urban area - EPO for network-based care.\",\"['HMO', 'POS', 'HDHP']\",\"[0.8127314, 0.16222602, 0.010559998]\",\"['Dental', 'Vision', 'Hospital Indemnity', 'STD']\",1.0,Dental: Essential oral health coverage for all employees.; Vision: Eye care coverage for all employees.; STD: Income protection for Medium ($50K–$100K) earners or excellent health status.,\"['Employee Assistance', 'Wellness Programs', 'FSA']\",\"[0.5, 0.5, 0.5]\"\r\nE008,Quinn,Martinez,Male,1979-10-17,258 Spruce St,Newplace,CO,80001,Not Married,58000,Full-Time,2020-06-18,HMO Silver,Basic Dental,Basic Vision,Y,N,Employee + Children,Y,N,2,Quinn Martinez,45.0,,,,,,Sales,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Desk,Suburban,Excellent,N,Occasional,Medium ($50K–$100K),Low,Moderate,Frequent,N,N,POS,0.8527868,Middle age with dependents - PPO for family coverage.,\"['POS', 'PPO', 'HMO']\",\"[0.8527868, 0.08081805, 0.04412484]\",\"['Dental', 'Vision']\",1.0,Dental: Essential oral health coverage for all employees.; Vision: Eye care coverage for all employees.,\"['Employee Assistance', 'Wellness Programs', 'FSA']\",\"[0.5, 0.5, 0.5]\"\r\nE009,Tina,Garcia,Female,1981-03-24,369 Willow Way,Lastplace,NV,89001,Married,67000,Full-Time,2018-12-01,HDHP Bronze,Premium Dental,Vision Plus,Y,Y,Employee + Family,N,N,2,Tina Garcia,44.0,,,,,,Information Technology,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Desk,Urban,Excellent,N,None,Medium ($50K–$100K),Low,Moderate,Rare,N,N,POS,0.7863343,\"Medium income, urban area - EPO for network-based care.\",\"['POS', 'HMO', 'PPO']\",\"[0.7863343, 0.08495092, 0.07835027]\",\"['Dental', 'Vision']\",1.0,Dental: Essential oral health coverage for all employees.; Vision: Eye care coverage for all employees.,\"['Employee Assistance', 'Wellness Programs', 'FSA']\",\"[0.5, 0.5, 0.5]\"\r\nE010,Xavier,Rodriguez,Male,1986-09-09,741 Poplar Pl,Finaltown,UT,84001,Not Married,41000,Contract,2021-07-22,HMO Basic,Waived,Declined,N,N,Employee Only,Y,N,0,Xavier Rodriguez,38.0,,,,,,Finance,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Desk,Suburban,Good,Y,Occasional,Medium ($50K–$100K),Medium,Active,Rare,N,N,PPO,0.5232488,\"Medium income, good health - PPO for balanced cost and flexibility.\",\"['PPO', 'POS', 'HMO']\",\"[0.5232488, 0.30636552, 0.11209288]\",['Critical Illness'],1.0,Critical Illness: Important protection for age 38 with good health status.,\"['Employee Assistance', 'Wellness Programs', 'FSA']\",\"[0.5, 0.5, 0.5]\"\r\nE011,Yvonne,Lee,Female,1984-12-18,852 Ash Ct,Hometown,MT,59001,Married,63000,Full-Time,2019-03-15,PPO Silver,Premium Dental,Vision Plus,Y,Y,Employee + Spouse,N,Y,1,Yvonne Lee,40.0,,,,,,Finance,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Desk,Rural,Fair,Y,Regular,Medium ($50K–$100K),Low,Active,Rare,N,N,HMO,0.82086664,Middle age with dependents - PPO for family coverage.,\"['HMO', 'PPO', 'POS']\",\"[0.82086664, 0.14504951, 0.02822561]\",\"['Dental', 'Vision', 'Hospital Indemnity', 'Critical Illness', 'STD']\",1.0,\"Dental: Essential oral health coverage for all employees.; Vision: Eye care coverage for all employees.; Hospital Indemnity: Recommended due to fair health condition, age 40, or chronic conditions.; Critical Illness: Important protection for age 40 with fair health status.; STD: Income protection for Medium ($50K–$100K) earners or fair health status.\",\"['Employee Assistance', 'Wellness Programs', 'FSA']\",\"[0.5, 0.5, 0.5]\"\r\nE012,Aaron,White,Male,1977-04-07,963 Beech St,Yourtown,ID,83001,Not Married,49000,Full-Time,2020-10-08,HMO Silver,Basic Dental,Basic Vision,Y,N,Employee + Children,Y,N,2,Aaron White,48.0,,,,,,Finance,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Desk,Rural,Fair,Y,Regular,Medium ($50K–$100K),Low,Moderate,Occasional,N,Y,PPO,0.8665187,\"Chronic conditions, older age - POS for coordinated care management.\",\"['PPO', 'POS', 'HMO']\",\"[0.8665187, 0.09582562, 0.023971833]\",\"['Dental', 'Vision']\",0.5,Default benefits assignment due to prediction error,\"['Dental', 'Vision', 'Hospital Indemnity']\",\"[0.5, 0.5, 0.5]\"\r\nE013,Brooke,Harris,Female,1989-08-21,159 Cherry Ave,Mytown,WY,82001,Not Married,35000,Part-Time,2022-05-30,HMO Basic,Waived,Declined,N,N,Employee Only,N,N,0,Brooke Harris,35.0,,,,,,Engineering,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Desk,Rural,Good,N,None,Low (<$50K),Medium,Active,Occasional,Y,Y,EPO,0.9845563,\"Low income, part-time employment - MEC for minimum coverage compliance.\",\"['EPO', 'HMO', 'HDHP + HSA']\",\"[0.9845563, 0.005368719, 0.004275845]\",\"['Dental', 'Vision']\",0.5,Default benefits assignment due to prediction error,\"['Dental', 'Vision', 'Hospital Indemnity']\",\"[0.5, 0.5, 0.5]\"\r\nE014,Connor,Clark,Male,1982-01-26,357 Dogwood Dr,Ourtown,ND,58001,Married,85000,Full-Time,2018-04-12,PPO Platinum,Premium Dental,Vision Plus,Y,Y,Employee + Family,N,N,3,Connor Clark,43.0,,,,,,Information Technology,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Desk,Rural,Fair,N,None,High (>$100K),Medium,Moderate,Occasional,N,Y,PPO,0.7689586,Middle age with dependents - PPO for family coverage.,\"['PPO', 'POS', 'HMO']\",\"[0.7689586, 0.16842997, 0.03366874]\",\"['Dental', 'Vision']\",0.5,Default benefits assignment due to prediction error,\"['Dental', 'Vision', 'Hospital Indemnity']\",\"[0.5, 0.5, 0.5]\"\r\nE015,Destiny,Lewis,Female,1986-05-13,468 Fir Ln,Theirtown,SD,57001,Not Married,44000,Full-Time,2021-11-20,PPO Silver,Basic Dental,Basic Vision,Y,N,Employee + Child,N,Y,1,Destiny Lewis,39.0,,,,,,Engineering,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Desk,Rural,Good,N,None,Medium ($50K–$100K),Medium,Active,Occasional,N,N,HMO,0.8606728,\"Medium income, good health - PPO for balanced cost and flexibility.\",\"['HMO', 'POS', 'PPO']\",\"[0.8606728, 0.12565333, 0.009386095]\",\"['Dental', 'Vision']\",0.5,Default benefits assignment due to prediction error,\"['Dental', 'Vision', 'Hospital Indemnity']\",\"[0.5, 0.5, 0.5]\"\r\nE016,Ethan,Robinson,Male,1979-09-28,579 Gum Tree Rd,Alltown,NE,68001,Married,92000,Full-Time,2017-08-05,PPO Platinum,Premium Dental,Vision Plus,Y,Y,Employee + Family,Y,N,3,Ethan Robinson,45.0,,,,,,Engineering,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Desk,Urban,Good,N,None,High (>$100K),Medium,Active,Rare,Y,N,HDHP + HSA,0.93564117,\"High income, good health, HSA familiar, no chronic conditions - HDHP + HSA for tax benefits.\",\"['HDHP + HSA', 'POS', 'PPO']\",\"[0.93564117, 0.*********, 0.*********]\",\"['Dental', 'Vision']\",0.5,Default benefits assignment due to prediction error,\"['Dental', 'Vision', 'Hospital Indemnity']\",\"[0.5, 0.5, 0.5]\"\r\nE017,Fiona,Walker,Female,1983-02-14,680 Hickory Way,Notown,KS,66001,Not Married,56000,Full-Time,2019-12-18,HMO Silver,Premium Dental,Basic Vision,Y,Y,Employee + Children,Y,N,2,Fiona Walker,42.0,,,,,,Manufacturing,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Desk,Urban,Good,N,None,Medium ($50K–$100K),Low,Moderate,Occasional,N,N,POS,0.9043829,\"Medium income, good health - PPO for balanced cost and flexibility.\",\"['POS', 'HMO', 'PPO']\",\"[0.9043829, 0.04808292, 0.03058851]\",\"['Dental', 'Vision']\",0.5,Default benefits assignment due to prediction error,\"['Dental', 'Vision', 'Hospital Indemnity']\",\"[0.5, 0.5, 0.5]\"\r\nE018,Gabriel,Hall,Male,1985-06-09,791 Ivy Pl,Everytown,OK,73001,Not Married,39000,Contract,2022-01-10,HMO Basic,Waived,Declined,N,N,Employee Only,N,N,0,Gabriel Hall,40.0,,,,,,Finance,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Desk,Urban,Fair,Y,Occasional,Low (<$50K),Low,Moderate,Rare,Y,N,HMO,0.5681863,\"Low income, fair/poor health, chronic conditions - HMO for cost-effective managed care.\",\"['HMO', 'PPO', 'POS']\",\"[0.5681863, 0.16995806, 0.08870534]\",\"['Dental', 'Vision']\",0.5,Default benefits assignment due to prediction error,\"['Dental', 'Vision', 'Hospital Indemnity']\",\"[0.5, 0.5, 0.5]\"\r\nE019,Hannah,Allen,Female,1980-10-22,802 Juniper St,Anyplace,AR,72001,Married,71000,Full-Time,2018-07-25,PPO Gold,Premium Dental,Vision Plus,Y,Y,Employee + Children,N,Y,2,Hannah Allen,44.0,,,,,,Information Technology,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Desk,Urban,Good,N,None,Medium ($50K–$100K),Low,Active,Rare,N,N,HMO,0.7341789,\"Medium income, good health - PPO for balanced cost and flexibility.\",\"['HMO', 'POS', 'PPO']\",\"[0.7341789, 0.24794313, 0.012737661]\",\"['Dental', 'Vision']\",0.5,Default benefits assignment due to prediction error,\"['Dental', 'Vision', 'Hospital Indemnity']\",\"[0.5, 0.5, 0.5]\"\r\nE020,Ian,Young,Male,1987-03-16,913 Koa Ave,Somewhere,LA,70001,Not Married,47000,Full-Time,2020-09-14,HMO Silver,Basic Dental,Basic Vision,Y,N,Employee Only,Y,N,1,Ian Young,38.0,,,,,,Sales,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Desk,Urban,Good,N,None,Medium ($50K–$100K),Low,Moderate,Frequent,N,Y,POS,0.8677581,\"Medium income, good health - PPO for balanced cost and flexibility.\",\"['POS', 'HDHP', 'HMO']\",\"[0.8677581, 0.046706192, 0.041549932]\",\"['Dental', 'Vision']\",0.5,Default benefits assignment due to prediction error,\"['Dental', 'Vision', 'Hospital Indemnity']\",\"[0.5, 0.5, 0.5]\"\r\n", "comprehensive_statistics": {"basic_demographics": {"age_statistics": {"average_age": 41.8, "median_age": 42.0, "age_range": {"min": 35, "max": 50}, "age_distribution": {"18-30": 0, "31-45": 17, "46-60": 3, "60+": 0}}, "gender_composition": {"counts": {"Male": 11, "Female": 9}, "percentages": {"Male": 55.0, "Female": 45.0}}, "marital_status_distribution": {"counts": {"Not Married": 11, "Married": 9}, "percentages": {"Not Married": 55.0, "Married": 45.0}}}, "dependent_analysis": {"dependent_count_distribution": {"average_dependents": 1.5, "median_dependents": 1.5, "distribution": {"2": 6, "1": 6, "3": 4, "0": 4}, "employees_with_dependents": 16, "percentage_with_dependents": 80.0}}, "employment_demographics": {"department_distribution": {"counts": {"Finance": 5, "Sales": 4, "Engineering": 4, "Information Technology": 4, "Manufacturing": 3}, "percentages": {"Finance": 25.0, "Sales": 20.0, "Engineering": 20.0, "Information Technology": 20.0, "Manufacturing": 15.0}}, "employment_type_distribution": {"counts": {"Full-Time": 15, "Part-Time": 3, "Contract": 2}, "percentages": {"Full-Time": 75.0, "Part-Time": 15.0, "Contract": 10.0}}, "job_type_distribution": {"counts": {"Desk": 20}, "percentages": {"Desk": 100.0}}, "employee_class_distribution": {"counts": {}, "percentages": {}}, "salary_analysis": {"average_salary": 59625.0, "median_salary": 57000.0, "salary_range": {"min": "35000", "max": "95000"}, "salary_distribution": {"under_40k": 3, "40k_75k": 13, "75k_100k": 4, "over_100k": 0}}}, "health_and_lifestyle": {"health_condition_distribution": {"counts": {"Good": 8, "Excellent": 6, "Fair": 6}, "percentages": {"Good": 40.0, "Excellent": 30.0, "Fair": 30.0}}, "chronic_condition_distribution": {"counts": {"N": 15, "Y": 5}, "percentages": {"N": 75.0, "Y": 25.0}}, "tobacco_use_distribution": {"counts": {"N": 12, "Y": 8}, "percentages": {"N": 60.0, "Y": 40.0}}, "lifestyle_distribution": {"counts": {"Moderate": 12, "Active": 8}, "percentages": {"Moderate": 60.0, "Active": 40.0}}, "prescription_use_distribution": {"counts": {"None": 10, "Occasional": 8, "Regular": 2}, "percentages": {"None": 50.0, "Occasional": 40.0, "Regular": 10.0}}}, "coverage_analysis": {"coverage_tier_distribution": {"counts": {"Employee + Family": 6, "Employee Only": 5, "Employee + Children": 4, "Employee + Child": 3, "Employee + Spouse": 2}, "percentages": {"Employee + Family": 30.0, "Employee Only": 25.0, "Employee + Children": 20.0, "Employee + Child": 15.0, "Employee + Spouse": 10.0}}, "coverage_tier_by_family_size": {"Employee + Child": {"0": 0, "1": 3, "2": 0, "3": 0}, "Employee + Children": {"0": 0, "1": 0, "2": 4, "3": 0}, "Employee + Family": {"0": 0, "1": 0, "2": 2, "3": 4}, "Employee + Spouse": {"0": 0, "1": 2, "2": 0, "3": 0}, "Employee Only": {"0": 4, "1": 1, "2": 0, "3": 0}}, "medical_plan_distribution": {"counts": {"HMO Silver": 5, "HMO Basic": 4, "PPO Gold": 3, "PPO Silver": 3, "PPO Platinum": 3, "HDHP Bronze": 2}, "percentages": {"HMO Silver": 25.0, "HMO Basic": 20.0, "PPO Gold": 15.0, "PPO Silver": 15.0, "PPO Platinum": 15.0, "HDHP Bronze": 10.0}}, "dental_plan_distribution": {"counts": {"Premium Dental": 10, "Basic Dental": 6, "Waived": 4}, "percentages": {"Premium Dental": 50.0, "Basic Dental": 30.0, "Waived": 20.0}}, "vision_plan_distribution": {"counts": {"Vision Plus": 9, "Basic Vision": 7, "Declined": 4}, "percentages": {"Vision Plus": 45.0, "Basic Vision": 35.0, "Declined": 20.0}}, "life_plan_distribution": {"counts": {"Y": 15, "N": 5}, "percentages": {"Y": 75.0, "N": 25.0}}}, "geographic_distribution": {"state_distribution": {"counts": {"CA": 1, "TX": 1, "AR": 1, "OK": 1, "KS": 1, "NE": 1, "SD": 1, "ND": 1, "WY": 1, "ID": 1, "MT": 1, "UT": 1, "NV": 1, "CO": 1, "AZ": 1, "OR": 1, "WA": 1, "NY": 1, "FL": 1, "LA": 1}, "percentages": {"CA": 5.0, "TX": 5.0, "AR": 5.0, "OK": 5.0, "KS": 5.0, "NE": 5.0, "SD": 5.0, "ND": 5.0, "WY": 5.0, "ID": 5.0, "MT": 5.0, "UT": 5.0, "NV": 5.0, "CO": 5.0, "AZ": 5.0, "OR": 5.0, "WA": 5.0, "NY": 5.0, "FL": 5.0, "LA": 5.0}}, "region_distribution": {"counts": {"Urban": 13, "Rural": 5, "Suburban": 2}, "percentages": {"Urban": 65.0, "Rural": 25.0, "Suburban": 10.0}}, "top_cities": {"counts": {"Somewhere": 2, "Anytown": 1, "Hometown": 1, "Everytown": 1, "Notown": 1, "Alltown": 1, "Theirtown": 1, "Ourtown": 1, "Mytown": 1, "Yourtown": 1}, "percentages": {"Somewhere": 10.0, "Anytown": 5.0, "Hometown": 5.0, "Everytown": 5.0, "Notown": 5.0, "Alltown": 5.0, "Theirtown": 5.0, "Ourtown": 5.0, "Mytown": 5.0, "Yourtown": 5.0}}}, "financial_demographics": {"income_tier_distribution": {"counts": {"Medium ($50K–$100K)": 13, "High (>$100K)": 4, "Low (<$50K)": 3}, "percentages": {"Medium ($50K–$100K)": 65.0, "High (>$100K)": 20.0, "Low (<$50K)": 15.0}}, "risk_tolerance_distribution": {"counts": {"Low": 11, "Medium": 9}, "percentages": {"Low": 55.0, "Medium": 45.0}}, "hsa_familiarity_distribution": {"counts": {"N": 16, "Y": 4}, "percentages": {"N": 80.0, "Y": 20.0}}}, "risk_assessment": {"group_risk_score": 33.05, "group_risk_level": "Medium", "risk_distribution": {"low_risk": 8, "medium_risk": 11, "high_risk": 1}, "risk_statistics": {"min_risk_score": 13, "max_risk_score": 68, "median_risk_score": 33.0, "std_risk_score": 15.95}, "top_risk_factors": {"Tobacco Use": 8, "Poor Health Condition": 6, "Chronic Conditions": 5, "Advanced Age": 1}, "individual_risks": [{"employee_id": "E001", "risk_score": 15, "risk_level": "Low", "risk_factors": []}, {"employee_id": "E002", "risk_score": 53, "risk_level": "Medium", "risk_factors": ["Age 50.0 (high risk)", "Has chronic condition"]}, {"employee_id": "E003", "risk_score": 25, "risk_level": "Low", "risk_factors": ["Tobacco user"]}, {"employee_id": "E004", "risk_score": 13, "risk_level": "Low", "risk_factors": []}, {"employee_id": "E005", "risk_score": 33, "risk_level": "Medium", "risk_factors": ["Health condition: Fair"]}, {"employee_id": "E006", "risk_score": 48, "risk_level": "Medium", "risk_factors": ["Health condition: Fair", "Tobacco user"]}, {"employee_id": "E007", "risk_score": 13, "risk_level": "Low", "risk_factors": []}, {"employee_id": "E008", "risk_score": 33, "risk_level": "Medium", "risk_factors": ["Tobacco user"]}, {"employee_id": "E009", "risk_score": 18, "risk_level": "Low", "risk_factors": []}, {"employee_id": "E010", "risk_score": 50, "risk_level": "Medium", "risk_factors": ["Has chronic condition", "Tobacco user"]}]}, "data_quality_metrics": {"overall_completeness": {"total_cells": 2840, "missing_cells": "2120", "completeness_percentage": 25.35}, "column_completeness": {"employee_id": {"missing_count": "0", "completeness_percentage": 100.0}, "first_name": {"missing_count": "0", "completeness_percentage": 100.0}, "last_name": {"missing_count": "0", "completeness_percentage": 100.0}, "gender": {"missing_count": "0", "completeness_percentage": 100.0}, "dob": {"missing_count": "0", "completeness_percentage": 100.0}, "address1": {"missing_count": "0", "completeness_percentage": 100.0}, "city": {"missing_count": "0", "completeness_percentage": 100.0}, "state": {"missing_count": "0", "completeness_percentage": 100.0}, "zipcode": {"missing_count": "0", "completeness_percentage": 100.0}, "marital_status": {"missing_count": "0", "completeness_percentage": 100.0}, "salary": {"missing_count": "0", "completeness_percentage": 100.0}, "employment_type": {"missing_count": "0", "completeness_percentage": 100.0}, "hire_date": {"missing_count": "0", "completeness_percentage": 100.0}, "medical_plan": {"missing_count": "0", "completeness_percentage": 100.0}, "dental_plan": {"missing_count": "0", "completeness_percentage": 100.0}, "vision_plan": {"missing_count": "0", "completeness_percentage": 100.0}, "life_plan": {"missing_count": "0", "completeness_percentage": 100.0}, "add_plan": {"missing_count": "0", "completeness_percentage": 100.0}, "coverage_tier": {"missing_count": "0", "completeness_percentage": 100.0}, "tobacco_use": {"missing_count": "0", "completeness_percentage": 100.0}, "pregnancy_status": {"missing_count": "0", "completeness_percentage": 100.0}, "dept_count": {"missing_count": "0", "completeness_percentage": 100.0}, "name": {"missing_count": "0", "completeness_percentage": 100.0}, "age": {"missing_count": "0", "completeness_percentage": 100.0}, "middle_name": {"missing_count": "20", "completeness_percentage": 0.0}, "address2": {"missing_count": "20", "completeness_percentage": 0.0}, "relationship": {"missing_count": "20", "completeness_percentage": 0.0}, "record_type": {"missing_count": "20", "completeness_percentage": 0.0}, "employee_class": {"missing_count": "20", "completeness_percentage": 0.0}, "department": {"missing_count": "0", "completeness_percentage": 100.0}, "ssn": {"missing_count": "20", "completeness_percentage": 0.0}, "dept_1": {"missing_count": "20", "completeness_percentage": 0.0}, "dept_1_dob": {"missing_count": "20", "completeness_percentage": 0.0}, "dept_1_age": {"missing_count": "20", "completeness_percentage": 0.0}, "dept_1_gender": {"missing_count": "20", "completeness_percentage": 0.0}, "relationship_type_1": {"missing_count": "20", "completeness_percentage": 0.0}, "dept_2": {"missing_count": "20", "completeness_percentage": 0.0}, "dept_2_dob": {"missing_count": "20", "completeness_percentage": 0.0}, "dept_2_age": {"missing_count": "20", "completeness_percentage": 0.0}, "dept_2_gender": {"missing_count": "20", "completeness_percentage": 0.0}, "relationship_type_2": {"missing_count": "20", "completeness_percentage": 0.0}, "dept_3": {"missing_count": "20", "completeness_percentage": 0.0}, "dept_3_dob": {"missing_count": "20", "completeness_percentage": 0.0}, "dept_3_age": {"missing_count": "20", "completeness_percentage": 0.0}, "dept_3_gender": {"missing_count": "20", "completeness_percentage": 0.0}, "relationship_type_3": {"missing_count": "20", "completeness_percentage": 0.0}, "dept_4": {"missing_count": "20", "completeness_percentage": 0.0}, "dept_4_dob": {"missing_count": "20", "completeness_percentage": 0.0}, "dept_4_age": {"missing_count": "20", "completeness_percentage": 0.0}, "dept_4_gender": {"missing_count": "20", "completeness_percentage": 0.0}, "relationship_type_4": {"missing_count": "20", "completeness_percentage": 0.0}, "dept_5": {"missing_count": "20", "completeness_percentage": 0.0}, "dept_5_dob": {"missing_count": "20", "completeness_percentage": 0.0}, "dept_5_age": {"missing_count": "20", "completeness_percentage": 0.0}, "dept_5_gender": {"missing_count": "20", "completeness_percentage": 0.0}, "relationship_type_5": {"missing_count": "20", "completeness_percentage": 0.0}, "dept_6": {"missing_count": "20", "completeness_percentage": 0.0}, "dept_6_dob": {"missing_count": "20", "completeness_percentage": 0.0}, "dept_6_age": {"missing_count": "20", "completeness_percentage": 0.0}, "dept_6_gender": {"missing_count": "20", "completeness_percentage": 0.0}, "relationship_type_6": {"missing_count": "20", "completeness_percentage": 0.0}, "dept_7": {"missing_count": "20", "completeness_percentage": 0.0}, "dept_7_dob": {"missing_count": "20", "completeness_percentage": 0.0}, "dept_7_age": {"missing_count": "20", "completeness_percentage": 0.0}, "dept_7_gender": {"missing_count": "20", "completeness_percentage": 0.0}, "relationship_type_7": {"missing_count": "20", "completeness_percentage": 0.0}, "dept_8": {"missing_count": "20", "completeness_percentage": 0.0}, "dept_8_dob": {"missing_count": "20", "completeness_percentage": 0.0}, "dept_8_age": {"missing_count": "20", "completeness_percentage": 0.0}, "dept_8_gender": {"missing_count": "20", "completeness_percentage": 0.0}, "relationship_type_8": {"missing_count": "20", "completeness_percentage": 0.0}, "dept_9": {"missing_count": "20", "completeness_percentage": 0.0}, "dept_9_dob": {"missing_count": "20", "completeness_percentage": 0.0}, "dept_9_age": {"missing_count": "20", "completeness_percentage": 0.0}, "dept_9_gender": {"missing_count": "20", "completeness_percentage": 0.0}, "relationship_type_9": {"missing_count": "20", "completeness_percentage": 0.0}, "dept_10": {"missing_count": "20", "completeness_percentage": 0.0}, "dept_10_dob": {"missing_count": "20", "completeness_percentage": 0.0}, "dept_10_age": {"missing_count": "20", "completeness_percentage": 0.0}, "dept_10_gender": {"missing_count": "20", "completeness_percentage": 0.0}, "relationship_type_10": {"missing_count": "20", "completeness_percentage": 0.0}, "dept_11": {"missing_count": "20", "completeness_percentage": 0.0}, "dept_11_dob": {"missing_count": "20", "completeness_percentage": 0.0}, "dept_11_age": {"missing_count": "20", "completeness_percentage": 0.0}, "dept_11_gender": {"missing_count": "20", "completeness_percentage": 0.0}, "relationship_type_11": {"missing_count": "20", "completeness_percentage": 0.0}, "dept_12": {"missing_count": "20", "completeness_percentage": 0.0}, "dept_12_dob": {"missing_count": "20", "completeness_percentage": 0.0}, "dept_12_age": {"missing_count": "20", "completeness_percentage": 0.0}, "dept_12_gender": {"missing_count": "20", "completeness_percentage": 0.0}, "relationship_type_12": {"missing_count": "20", "completeness_percentage": 0.0}, "dept_13": {"missing_count": "20", "completeness_percentage": 0.0}, "dept_13_dob": {"missing_count": "20", "completeness_percentage": 0.0}, "dept_13_age": {"missing_count": "20", "completeness_percentage": 0.0}, "dept_13_gender": {"missing_count": "20", "completeness_percentage": 0.0}, "relationship_type_13": {"missing_count": "20", "completeness_percentage": 0.0}, "dept_14": {"missing_count": "20", "completeness_percentage": 0.0}, "dept_14_dob": {"missing_count": "20", "completeness_percentage": 0.0}, "dept_14_age": {"missing_count": "20", "completeness_percentage": 0.0}, "dept_14_gender": {"missing_count": "20", "completeness_percentage": 0.0}, "relationship_type_14": {"missing_count": "20", "completeness_percentage": 0.0}, "dept_15": {"missing_count": "20", "completeness_percentage": 0.0}, "dept_15_dob": {"missing_count": "20", "completeness_percentage": 0.0}, "dept_15_age": {"missing_count": "20", "completeness_percentage": 0.0}, "dept_15_gender": {"missing_count": "20", "completeness_percentage": 0.0}, "relationship_type_15": {"missing_count": "20", "completeness_percentage": 0.0}, "dept_16": {"missing_count": "20", "completeness_percentage": 0.0}, "dept_16_dob": {"missing_count": "20", "completeness_percentage": 0.0}, "dept_16_age": {"missing_count": "20", "completeness_percentage": 0.0}, "dept_16_gender": {"missing_count": "20", "completeness_percentage": 0.0}, "relationship_type_16": {"missing_count": "20", "completeness_percentage": 0.0}, "dept_17": {"missing_count": "20", "completeness_percentage": 0.0}, "dept_17_dob": {"missing_count": "20", "completeness_percentage": 0.0}, "dept_17_age": {"missing_count": "20", "completeness_percentage": 0.0}, "dept_17_gender": {"missing_count": "20", "completeness_percentage": 0.0}, "relationship_type_17": {"missing_count": "20", "completeness_percentage": 0.0}, "dept_18": {"missing_count": "20", "completeness_percentage": 0.0}, "dept_18_dob": {"missing_count": "20", "completeness_percentage": 0.0}, "dept_18_age": {"missing_count": "20", "completeness_percentage": 0.0}, "dept_18_gender": {"missing_count": "20", "completeness_percentage": 0.0}, "relationship_type_18": {"missing_count": "20", "completeness_percentage": 0.0}, "dept_19": {"missing_count": "20", "completeness_percentage": 0.0}, "dept_19_dob": {"missing_count": "20", "completeness_percentage": 0.0}, "dept_19_age": {"missing_count": "20", "completeness_percentage": 0.0}, "dept_19_gender": {"missing_count": "20", "completeness_percentage": 0.0}, "relationship_type_19": {"missing_count": "20", "completeness_percentage": 0.0}, "dept_20": {"missing_count": "20", "completeness_percentage": 0.0}, "dept_20_dob": {"missing_count": "20", "completeness_percentage": 0.0}, "dept_20_age": {"missing_count": "20", "completeness_percentage": 0.0}, "dept_20_gender": {"missing_count": "20", "completeness_percentage": 0.0}, "relationship_type_20": {"missing_count": "20", "completeness_percentage": 0.0}, "job_type": {"missing_count": "0", "completeness_percentage": 100.0}, "region": {"missing_count": "0", "completeness_percentage": 100.0}, "health_condition": {"missing_count": "0", "completeness_percentage": 100.0}, "chronic_condition": {"missing_count": "0", "completeness_percentage": 100.0}, "prescription_use": {"missing_count": "0", "completeness_percentage": 100.0}, "income_tier": {"missing_count": "0", "completeness_percentage": 100.0}, "risk_tolerance": {"missing_count": "0", "completeness_percentage": 100.0}, "lifestyle": {"missing_count": "0", "completeness_percentage": 100.0}, "travel_frequency": {"missing_count": "0", "completeness_percentage": 100.0}, "hsa_familiarity": {"missing_count": "0", "completeness_percentage": 100.0}, "mental_health_needs": {"missing_count": "0", "completeness_percentage": 100.0}}, "critical_field_completeness": {"name": {"missing_count": "0", "completeness_percentage": 100.0}, "gender": {"missing_count": "0", "completeness_percentage": 100.0}, "zipcode": {"missing_count": "0", "completeness_percentage": 100.0}, "marital_status": {"missing_count": "0", "completeness_percentage": 100.0}}}}, "prediction_summary": {"total_employees": 20, "plan_type_distribution": {"successful_predictions": {"HMO": 7, "POS": 5, "PPO": 5, "HDHP": 1, "EPO": 1, "HDHP + HSA": 1}, "failed_predictions": "0", "success_rate": 100.0, "total_predicted": "20"}, "benefits_distribution": {}, "confidence_metrics": {"plan_confidence": {"mean": "0.763", "min": "0.520", "max": "0.985", "count": 20}}, "prediction_success_rates": {}, "top_3_plan_probabilities": []}, "total_employees": 20, "total_columns": 152, "has_predictions": true, "prediction_columns": ["predicted_plan_type", "plan_confidence"]}, "metadata": {"file_processing": {"filename": "col3_benefits_centric.csv", "size": 3542, "original_column_names": ["Employee_ID", "First_Name", "Last_Name", "Gender", "DOB", "Address1", "City", "State", "ZIP", "Marital_Status", "Salary", "Employment_Type", "Hire_Date", "Medical_Plan", "Dental_Plan", "Vision_Plan", "Life_Plan", "ADD_Plan", "Coverage_Tier", "Tobacco_Use", "Pregnancy_Status", "dept_count"]}, "pattern_analysis": {"pattern_type": "dependent_count_only", "pattern_confidence": 1.1, "pattern_reason": "Found 'dept_count' column; No detailed dependent columns found; Found employee fields: ['first_name', 'last_name', 'employee_id']", "analysis_details": {"confidence": 1.1, "reason": "Found 'dept_count' column; No detailed dependent columns found; Found employee fields: ['first_name', 'last_name', 'employee_id']"}}, "field_mapping": {"total_fields_mapped": 22, "mapping_success": true, "unmapped_fields": [], "mapping_confidence": 0, "field_mapping_details": {"Employee_ID": "employee_id", "First_Name": "first_name", "Last_Name": "last_name", "Gender": "gender", "DOB": "dob", "Address1": "address1", "City": "city", "State": "state", "ZIP": "zipcode", "Marital_Status": "marital_status", "Salary": "salary", "Employment_Type": "employment_type", "Hire_Date": "hire_date", "Medical_Plan": "medical_plan", "Dental_Plan": "dental_plan", "Vision_Plan": "vision_plan", "Life_Plan": "life_plan", "ADD_Plan": "add_plan", "Coverage_Tier": "coverage_tier", "Tobacco_Use": "tobacco_use", "Pregnancy_Status": "pregnancy_status", "dept_count": "dept_count"}}, "data_processing": {"validation_passed": false, "processing_summary": {"original_rows": 20, "original_columns": 22, "processed_rows": 20, "processed_columns": 131, "processing_time_seconds": 6.87, "validation_passed": true, "validation_errors": 0, "error_rows": 0, "fields_processed": {"name_fields": true, "age_converted": true, "gender_standardized": true, "addresses_cleaned": true, "zipcode_validated": true}, "data_quality": {"complete_records": 20, "missing_data_rows": 0}}, "data_quality_score": 0.8, "processing_details": {"success": true, "preprocessed_data": "   employee_id first_name  last_name  gender         dob         address1       city  ... dept_19_gender  relationship_type_19 dept_20  dept_20_dob dept_20_age dept_20_gender relationship_type_20\n0         E001       <PERSON>    Male  1980-01-15      123 Main St    Anytown  ...           None                  None    None         None        None           None                 None\n1         E002      <PERSON>  Female  1975-03-10      456 Oak Ave  Somewhere  ...           None                  None    None         None        None           None                 None\n2         E003    <PERSON>  1990-06-30      789 Pine Rd  Elsewhere  ...           None                  None    None         None        None           None                 None\n3         E004      <PERSON>  1985-11-05       321 Elm St    Nowhere  ...           None                  None    None         None        None           None                 None\n4         E005      <PERSON>  1978-02-28     654 Maple Dr   Anywhere  ...           None                  None    None         None        None           None                 None\n5         E006    <PERSON>  1983-07-12     987 Cedar Ln  Someplace  ...           None                  None    None         None        None           None                 None\n6         E007      <PERSON>  1987-12-03    147 Birch Ave  Othertown  ...           None                  None    None         None        None           None                 None\n7         E008      <PERSON>  1979-10-17    258 Spruce St   Newplace  ...           None                  None    None         None        None           None                 None\n8         E009       <PERSON>  1981-03-24   369 <PERSON>  Lastplace  ...           None                  None    None         None        None           None                 None\n9         E010     <PERSON>  1986-09-09    741 Poplar Pl  Finaltown  ...           None                  None    None         None        None           None                 None\n10        E011     <PERSON>  1984-12-18       852 Ash Ct   Hometown  ...           None                  None    None         None        None           None                 None\n11        E012      <PERSON>  1977-04-07     963 <PERSON><PERSON>   Yourtown  ...           None                  None    None         None        None           None                 None\n12        E013     Brooke     Harris  Female  1989-08-21   159 <PERSON> Ave     Mytown  ...           None                  None    None         None        None           None                 None\n13        E014     Connor      Clark    Male  1982-01-26   357 Dogwood Dr    Ourtown  ...           None                  None    None         None        None           None                 None\n14        E015    Destiny      <PERSON>  Female  1986-05-13       468 Fir Ln  Theirtown  ...           None                  None    None         None        None           None                 None\n15        E016      Ethan   Robinson    Male  1979-09-28  579 Gum Tree Rd    Alltown  ...           None                  None    None         None        None           None                 None\n16        E017      Fiona     Walker  Female  1983-02-14  680 Hickory Way     Notown  ...           None                  None    None         None        None           None                 None\n17        E018    Gabriel       Hall    Male  1985-06-09       791 Ivy Pl  Everytown  ...           None                  None    None         None        None           None                 None\n18        E019     Hannah      Allen  Female  1980-10-22   802 Juniper St   Anyplace  ...           None                  None    None         None        None           None                 None\n19        E020        Ian      Young    Male  1987-03-16      913 Koa Ave  Somewhere  ...           None                  None    None         None        None           None                 None\n\n[20 rows x 131 columns]", "validation": {"is_valid": true, "errors": [], "error_rows": [], "total_errors": 0}, "summary": {"original_rows": 20, "original_columns": 22, "processed_rows": 20, "processed_columns": 131, "processing_time_seconds": 6.87, "validation_passed": true, "validation_errors": 0, "error_rows": 0, "fields_processed": {"name_fields": true, "age_converted": true, "gender_standardized": true, "addresses_cleaned": true, "zipcode_validated": true}, "data_quality": {"complete_records": 20, "missing_data_rows": 0}}}}, "enrichment_and_prediction": {"success": true, "enriched_data": "   employee_id first_name  last_name  ...                                    benefits_reason                                 top_3_benefits top_3_benefits_confidences\n0         E001       <PERSON>  ...  Dental: Essential oral health coverage for all...  [Employee Assistance, Wellness Programs, FSA]            [0.5, 0.5, 0.5]\n1         E002      <PERSON>  ...  Dental: Essential oral health coverage for all...  [Employee Assistance, Wellness Programs, FSA]            [0.5, 0.5, 0.5]\n2         E003    <PERSON>  ...                                ML model prediction  [Employee Assistance, Wellness Programs, FSA]            [0.5, 0.5, 0.5]\n3         E004      <PERSON>  ...  Dental: Essential oral health coverage for all...  [Employee Assistance, Wellness Programs, FSA]            [0.5, 0.5, 0.5]\n4         E005      <PERSON>  ...  STD: Income protection for High (>$100K) earne...  [Employee Assistance, Wellness Programs, FSA]            [0.5, 0.5, 0.5]\n5         E006    <PERSON>  ...                                ML model prediction  [Employee Assistance, Wellness Programs, FSA]            [0.5, 0.5, 0.5]\n6         E007      <PERSON>  ...  Dental: Essential oral health coverage for all...  [Employee Assistance, Wellness Programs, FSA]            [0.5, 0.5, 0.5]\n7         E008      Quinn   Martinez  ...  Dental: Essential oral health coverage for all...  [Employee Assistance, Wellness Programs, FSA]            [0.5, 0.5, 0.5]\n8         E009       Tina     Garcia  ...  Dental: Essential oral health coverage for all...  [Employee Assistance, Wellness Programs, FSA]            [0.5, 0.5, 0.5]\n9         E010     Xavier  Rodriguez  ...  Critical Illness: Important protection for age...  [Employee Assistance, Wellness Programs, FSA]            [0.5, 0.5, 0.5]\n10        E011     Yvonne        Lee  ...  Dental: Essential oral health coverage for all...  [Employee Assistance, Wellness Programs, FSA]            [0.5, 0.5, 0.5]\n11        E012      Aaron      White  ...  Default benefits assignment due to prediction ...           [Dental, Vision, Hospital Indemnity]            [0.5, 0.5, 0.5]\n12        E013     Brooke     Harris  ...  Default benefits assignment due to prediction ...           [Dental, Vision, Hospital Indemnity]            [0.5, 0.5, 0.5]\n13        E014     Connor      Clark  ...  Default benefits assignment due to prediction ...           [Dental, Vision, Hospital Indemnity]            [0.5, 0.5, 0.5]\n14        E015    Destiny      Lewis  ...  Default benefits assignment due to prediction ...           [Dental, Vision, Hospital Indemnity]            [0.5, 0.5, 0.5]\n15        E016      Ethan   Robinson  ...  Default benefits assignment due to prediction ...           [Dental, Vision, Hospital Indemnity]            [0.5, 0.5, 0.5]\n16        E017      Fiona     Walker  ...  Default benefits assignment due to prediction ...           [Dental, Vision, Hospital Indemnity]            [0.5, 0.5, 0.5]\n17        E018    Gabriel       Hall  ...  Default benefits assignment due to prediction ...           [Dental, Vision, Hospital Indemnity]            [0.5, 0.5, 0.5]\n18        E019     Hannah      Allen  ...  Default benefits assignment due to prediction ...           [Dental, Vision, Hospital Indemnity]            [0.5, 0.5, 0.5]\n19        E020        Ian      Young  ...  Default benefits assignment due to prediction ...           [Dental, Vision, Hospital Indemnity]            [0.5, 0.5, 0.5]\n\n[20 rows x 152 columns]", "enrichment_summary": {"total_employees": 20, "features_analyzed": 19, "enrichment_details": {"age": {"original_missing": "0", "final_missing": "0", "enriched_count": "0", "completion_rate": 100.0}, "gender": {"original_missing": "0", "final_missing": "0", "enriched_count": "0", "completion_rate": 100.0}, "marital_status": {"original_missing": "0", "final_missing": "0", "enriched_count": "0", "completion_rate": 100.0}, "dept_count": {"original_missing": "0", "final_missing": "0", "enriched_count": "0", "completion_rate": 100.0}, "job_type": {"original_missing": 20, "final_missing": "0", "enriched_count": "20", "completion_rate": 100.0}, "employment_type": {"original_missing": "0", "final_missing": "0", "enriched_count": "0", "completion_rate": 100.0}, "department": {"original_missing": "20", "final_missing": "0", "enriched_count": "20", "completion_rate": 100.0}, "region": {"original_missing": 20, "final_missing": "0", "enriched_count": "20", "completion_rate": 100.0}, "health_condition": {"original_missing": 20, "final_missing": "0", "enriched_count": "20", "completion_rate": 100.0}, "chronic_condition": {"original_missing": 20, "final_missing": "0", "enriched_count": "20", "completion_rate": 100.0}, "prescription_use": {"original_missing": 20, "final_missing": "0", "enriched_count": "20", "completion_rate": 100.0}, "income_tier": {"original_missing": 20, "final_missing": "0", "enriched_count": "20", "completion_rate": 100.0}, "risk_tolerance": {"original_missing": 20, "final_missing": "0", "enriched_count": "20", "completion_rate": 100.0}, "lifestyle": {"original_missing": 20, "final_missing": "0", "enriched_count": "20", "completion_rate": 100.0}, "travel_frequency": {"original_missing": 20, "final_missing": "0", "enriched_count": "20", "completion_rate": 100.0}, "hsa_familiarity": {"original_missing": 20, "final_missing": "0", "enriched_count": "20", "completion_rate": 100.0}, "pregnancy_status": {"original_missing": "0", "final_missing": "0", "enriched_count": "0", "completion_rate": 100.0}, "tobacco_use": {"original_missing": "0", "final_missing": "0", "enriched_count": "0", "completion_rate": 100.0}, "mental_health_needs": {"original_missing": 20, "final_missing": "0", "enriched_count": "20", "completion_rate": 100.0}}, "data_quality_improvement": {"total_missing_before": "240", "total_missing_after": "0", "total_enriched": "240", "overall_completion_rate": 100.0}}, "comprehensive_statistics": {"basic_demographics": {"age_statistics": {"average_age": 41.8, "median_age": 42.0, "age_range": {"min": 35, "max": 50}, "age_distribution": {"18-30": 0, "31-45": 17, "46-60": 3, "60+": 0}}, "gender_composition": {"counts": {"Male": 11, "Female": 9}, "percentages": {"Male": 55.0, "Female": 45.0}}, "marital_status_distribution": {"counts": {"Not Married": 11, "Married": 9}, "percentages": {"Not Married": 55.0, "Married": 45.0}}}, "dependent_analysis": {"dependent_count_distribution": {"average_dependents": 1.5, "median_dependents": 1.5, "distribution": {"2": 6, "1": 6, "3": 4, "0": 4}, "employees_with_dependents": 16, "percentage_with_dependents": 80.0}}, "employment_demographics": {"department_distribution": {"counts": {"Finance": 5, "Sales": 4, "Engineering": 4, "Information Technology": 4, "Manufacturing": 3}, "percentages": {"Finance": 25.0, "Sales": 20.0, "Engineering": 20.0, "Information Technology": 20.0, "Manufacturing": 15.0}}, "employment_type_distribution": {"counts": {"Full-Time": 15, "Part-Time": 3, "Contract": 2}, "percentages": {"Full-Time": 75.0, "Part-Time": 15.0, "Contract": 10.0}}, "job_type_distribution": {"counts": {"Desk": 20}, "percentages": {"Desk": 100.0}}, "employee_class_distribution": {"counts": {}, "percentages": {}}, "salary_analysis": {"average_salary": 59625.0, "median_salary": 57000.0, "salary_range": {"min": "35000", "max": "95000"}, "salary_distribution": {"under_40k": 3, "40k_75k": 13, "75k_100k": 4, "over_100k": 0}}}, "health_and_lifestyle": {"health_condition_distribution": {"counts": {"Good": 8, "Excellent": 6, "Fair": 6}, "percentages": {"Good": 40.0, "Excellent": 30.0, "Fair": 30.0}}, "chronic_condition_distribution": {"counts": {"N": 15, "Y": 5}, "percentages": {"N": 75.0, "Y": 25.0}}, "tobacco_use_distribution": {"counts": {"N": 12, "Y": 8}, "percentages": {"N": 60.0, "Y": 40.0}}, "lifestyle_distribution": {"counts": {"Moderate": 12, "Active": 8}, "percentages": {"Moderate": 60.0, "Active": 40.0}}, "prescription_use_distribution": {"counts": {"None": 10, "Occasional": 8, "Regular": 2}, "percentages": {"None": 50.0, "Occasional": 40.0, "Regular": 10.0}}}, "coverage_analysis": {"coverage_tier_distribution": {"counts": {"Employee + Family": 6, "Employee Only": 5, "Employee + Children": 4, "Employee + Child": 3, "Employee + Spouse": 2}, "percentages": {"Employee + Family": 30.0, "Employee Only": 25.0, "Employee + Children": 20.0, "Employee + Child": 15.0, "Employee + Spouse": 10.0}}, "coverage_tier_by_family_size": {"Employee + Child": {"0": 0, "1": 3, "2": 0, "3": 0}, "Employee + Children": {"0": 0, "1": 0, "2": 4, "3": 0}, "Employee + Family": {"0": 0, "1": 0, "2": 2, "3": 4}, "Employee + Spouse": {"0": 0, "1": 2, "2": 0, "3": 0}, "Employee Only": {"0": 4, "1": 1, "2": 0, "3": 0}}, "medical_plan_distribution": {"counts": {"HMO Silver": 5, "HMO Basic": 4, "PPO Gold": 3, "PPO Silver": 3, "PPO Platinum": 3, "HDHP Bronze": 2}, "percentages": {"HMO Silver": 25.0, "HMO Basic": 20.0, "PPO Gold": 15.0, "PPO Silver": 15.0, "PPO Platinum": 15.0, "HDHP Bronze": 10.0}}, "dental_plan_distribution": {"counts": {"Premium Dental": 10, "Basic Dental": 6, "Waived": 4}, "percentages": {"Premium Dental": 50.0, "Basic Dental": 30.0, "Waived": 20.0}}, "vision_plan_distribution": {"counts": {"Vision Plus": 9, "Basic Vision": 7, "Declined": 4}, "percentages": {"Vision Plus": 45.0, "Basic Vision": 35.0, "Declined": 20.0}}, "life_plan_distribution": {"counts": {"Y": 15, "N": 5}, "percentages": {"Y": 75.0, "N": 25.0}}}, "geographic_distribution": {"state_distribution": {"counts": {"CA": 1, "TX": 1, "AR": 1, "OK": 1, "KS": 1, "NE": 1, "SD": 1, "ND": 1, "WY": 1, "ID": 1, "MT": 1, "UT": 1, "NV": 1, "CO": 1, "AZ": 1, "OR": 1, "WA": 1, "NY": 1, "FL": 1, "LA": 1}, "percentages": {"CA": 5.0, "TX": 5.0, "AR": 5.0, "OK": 5.0, "KS": 5.0, "NE": 5.0, "SD": 5.0, "ND": 5.0, "WY": 5.0, "ID": 5.0, "MT": 5.0, "UT": 5.0, "NV": 5.0, "CO": 5.0, "AZ": 5.0, "OR": 5.0, "WA": 5.0, "NY": 5.0, "FL": 5.0, "LA": 5.0}}, "region_distribution": {"counts": {"Urban": 13, "Rural": 5, "Suburban": 2}, "percentages": {"Urban": 65.0, "Rural": 25.0, "Suburban": 10.0}}, "top_cities": {"counts": {"Somewhere": 2, "Anytown": 1, "Hometown": 1, "Everytown": 1, "Notown": 1, "Alltown": 1, "Theirtown": 1, "Ourtown": 1, "Mytown": 1, "Yourtown": 1}, "percentages": {"Somewhere": 10.0, "Anytown": 5.0, "Hometown": 5.0, "Everytown": 5.0, "Notown": 5.0, "Alltown": 5.0, "Theirtown": 5.0, "Ourtown": 5.0, "Mytown": 5.0, "Yourtown": 5.0}}}, "financial_demographics": {"income_tier_distribution": {"counts": {"Medium ($50K–$100K)": 13, "High (>$100K)": 4, "Low (<$50K)": 3}, "percentages": {"Medium ($50K–$100K)": 65.0, "High (>$100K)": 20.0, "Low (<$50K)": 15.0}}, "risk_tolerance_distribution": {"counts": {"Low": 11, "Medium": 9}, "percentages": {"Low": 55.0, "Medium": 45.0}}, "hsa_familiarity_distribution": {"counts": {"N": 16, "Y": 4}, "percentages": {"N": 80.0, "Y": 20.0}}}, "risk_assessment": {"group_risk_score": 33.05, "group_risk_level": "Medium", "risk_distribution": {"low_risk": 8, "medium_risk": 11, "high_risk": 1}, "risk_statistics": {"min_risk_score": 13, "max_risk_score": 68, "median_risk_score": 33.0, "std_risk_score": 15.95}, "top_risk_factors": {"Tobacco Use": 8, "Poor Health Condition": 6, "Chronic Conditions": 5, "Advanced Age": 1}, "individual_risks": [{"employee_id": "E001", "risk_score": 15, "risk_level": "Low", "risk_factors": []}, {"employee_id": "E002", "risk_score": 53, "risk_level": "Medium", "risk_factors": ["Age 50.0 (high risk)", "Has chronic condition"]}, {"employee_id": "E003", "risk_score": 25, "risk_level": "Low", "risk_factors": ["Tobacco user"]}, {"employee_id": "E004", "risk_score": 13, "risk_level": "Low", "risk_factors": []}, {"employee_id": "E005", "risk_score": 33, "risk_level": "Medium", "risk_factors": ["Health condition: Fair"]}, {"employee_id": "E006", "risk_score": 48, "risk_level": "Medium", "risk_factors": ["Health condition: Fair", "Tobacco user"]}, {"employee_id": "E007", "risk_score": 13, "risk_level": "Low", "risk_factors": []}, {"employee_id": "E008", "risk_score": 33, "risk_level": "Medium", "risk_factors": ["Tobacco user"]}, {"employee_id": "E009", "risk_score": 18, "risk_level": "Low", "risk_factors": []}, {"employee_id": "E010", "risk_score": 50, "risk_level": "Medium", "risk_factors": ["Has chronic condition", "Tobacco user"]}]}, "data_quality_metrics": {"overall_completeness": {"total_cells": 2840, "missing_cells": "2120", "completeness_percentage": 25.35}, "column_completeness": {"employee_id": {"missing_count": "0", "completeness_percentage": 100.0}, "first_name": {"missing_count": "0", "completeness_percentage": 100.0}, "last_name": {"missing_count": "0", "completeness_percentage": 100.0}, "gender": {"missing_count": "0", "completeness_percentage": 100.0}, "dob": {"missing_count": "0", "completeness_percentage": 100.0}, "address1": {"missing_count": "0", "completeness_percentage": 100.0}, "city": {"missing_count": "0", "completeness_percentage": 100.0}, "state": {"missing_count": "0", "completeness_percentage": 100.0}, "zipcode": {"missing_count": "0", "completeness_percentage": 100.0}, "marital_status": {"missing_count": "0", "completeness_percentage": 100.0}, "salary": {"missing_count": "0", "completeness_percentage": 100.0}, "employment_type": {"missing_count": "0", "completeness_percentage": 100.0}, "hire_date": {"missing_count": "0", "completeness_percentage": 100.0}, "medical_plan": {"missing_count": "0", "completeness_percentage": 100.0}, "dental_plan": {"missing_count": "0", "completeness_percentage": 100.0}, "vision_plan": {"missing_count": "0", "completeness_percentage": 100.0}, "life_plan": {"missing_count": "0", "completeness_percentage": 100.0}, "add_plan": {"missing_count": "0", "completeness_percentage": 100.0}, "coverage_tier": {"missing_count": "0", "completeness_percentage": 100.0}, "tobacco_use": {"missing_count": "0", "completeness_percentage": 100.0}, "pregnancy_status": {"missing_count": "0", "completeness_percentage": 100.0}, "dept_count": {"missing_count": "0", "completeness_percentage": 100.0}, "name": {"missing_count": "0", "completeness_percentage": 100.0}, "age": {"missing_count": "0", "completeness_percentage": 100.0}, "middle_name": {"missing_count": "20", "completeness_percentage": 0.0}, "address2": {"missing_count": "20", "completeness_percentage": 0.0}, "relationship": {"missing_count": "20", "completeness_percentage": 0.0}, "record_type": {"missing_count": "20", "completeness_percentage": 0.0}, "employee_class": {"missing_count": "20", "completeness_percentage": 0.0}, "department": {"missing_count": "0", "completeness_percentage": 100.0}, "ssn": {"missing_count": "20", "completeness_percentage": 0.0}, "dept_1": {"missing_count": "20", "completeness_percentage": 0.0}, "dept_1_dob": {"missing_count": "20", "completeness_percentage": 0.0}, "dept_1_age": {"missing_count": "20", "completeness_percentage": 0.0}, "dept_1_gender": {"missing_count": "20", "completeness_percentage": 0.0}, "relationship_type_1": {"missing_count": "20", "completeness_percentage": 0.0}, "dept_2": {"missing_count": "20", "completeness_percentage": 0.0}, "dept_2_dob": {"missing_count": "20", "completeness_percentage": 0.0}, "dept_2_age": {"missing_count": "20", "completeness_percentage": 0.0}, "dept_2_gender": {"missing_count": "20", "completeness_percentage": 0.0}, "relationship_type_2": {"missing_count": "20", "completeness_percentage": 0.0}, "dept_3": {"missing_count": "20", "completeness_percentage": 0.0}, "dept_3_dob": {"missing_count": "20", "completeness_percentage": 0.0}, "dept_3_age": {"missing_count": "20", "completeness_percentage": 0.0}, "dept_3_gender": {"missing_count": "20", "completeness_percentage": 0.0}, "relationship_type_3": {"missing_count": "20", "completeness_percentage": 0.0}, "dept_4": {"missing_count": "20", "completeness_percentage": 0.0}, "dept_4_dob": {"missing_count": "20", "completeness_percentage": 0.0}, "dept_4_age": {"missing_count": "20", "completeness_percentage": 0.0}, "dept_4_gender": {"missing_count": "20", "completeness_percentage": 0.0}, "relationship_type_4": {"missing_count": "20", "completeness_percentage": 0.0}, "dept_5": {"missing_count": "20", "completeness_percentage": 0.0}, "dept_5_dob": {"missing_count": "20", "completeness_percentage": 0.0}, "dept_5_age": {"missing_count": "20", "completeness_percentage": 0.0}, "dept_5_gender": {"missing_count": "20", "completeness_percentage": 0.0}, "relationship_type_5": {"missing_count": "20", "completeness_percentage": 0.0}, "dept_6": {"missing_count": "20", "completeness_percentage": 0.0}, "dept_6_dob": {"missing_count": "20", "completeness_percentage": 0.0}, "dept_6_age": {"missing_count": "20", "completeness_percentage": 0.0}, "dept_6_gender": {"missing_count": "20", "completeness_percentage": 0.0}, "relationship_type_6": {"missing_count": "20", "completeness_percentage": 0.0}, "dept_7": {"missing_count": "20", "completeness_percentage": 0.0}, "dept_7_dob": {"missing_count": "20", "completeness_percentage": 0.0}, "dept_7_age": {"missing_count": "20", "completeness_percentage": 0.0}, "dept_7_gender": {"missing_count": "20", "completeness_percentage": 0.0}, "relationship_type_7": {"missing_count": "20", "completeness_percentage": 0.0}, "dept_8": {"missing_count": "20", "completeness_percentage": 0.0}, "dept_8_dob": {"missing_count": "20", "completeness_percentage": 0.0}, "dept_8_age": {"missing_count": "20", "completeness_percentage": 0.0}, "dept_8_gender": {"missing_count": "20", "completeness_percentage": 0.0}, "relationship_type_8": {"missing_count": "20", "completeness_percentage": 0.0}, "dept_9": {"missing_count": "20", "completeness_percentage": 0.0}, "dept_9_dob": {"missing_count": "20", "completeness_percentage": 0.0}, "dept_9_age": {"missing_count": "20", "completeness_percentage": 0.0}, "dept_9_gender": {"missing_count": "20", "completeness_percentage": 0.0}, "relationship_type_9": {"missing_count": "20", "completeness_percentage": 0.0}, "dept_10": {"missing_count": "20", "completeness_percentage": 0.0}, "dept_10_dob": {"missing_count": "20", "completeness_percentage": 0.0}, "dept_10_age": {"missing_count": "20", "completeness_percentage": 0.0}, "dept_10_gender": {"missing_count": "20", "completeness_percentage": 0.0}, "relationship_type_10": {"missing_count": "20", "completeness_percentage": 0.0}, "dept_11": {"missing_count": "20", "completeness_percentage": 0.0}, "dept_11_dob": {"missing_count": "20", "completeness_percentage": 0.0}, "dept_11_age": {"missing_count": "20", "completeness_percentage": 0.0}, "dept_11_gender": {"missing_count": "20", "completeness_percentage": 0.0}, "relationship_type_11": {"missing_count": "20", "completeness_percentage": 0.0}, "dept_12": {"missing_count": "20", "completeness_percentage": 0.0}, "dept_12_dob": {"missing_count": "20", "completeness_percentage": 0.0}, "dept_12_age": {"missing_count": "20", "completeness_percentage": 0.0}, "dept_12_gender": {"missing_count": "20", "completeness_percentage": 0.0}, "relationship_type_12": {"missing_count": "20", "completeness_percentage": 0.0}, "dept_13": {"missing_count": "20", "completeness_percentage": 0.0}, "dept_13_dob": {"missing_count": "20", "completeness_percentage": 0.0}, "dept_13_age": {"missing_count": "20", "completeness_percentage": 0.0}, "dept_13_gender": {"missing_count": "20", "completeness_percentage": 0.0}, "relationship_type_13": {"missing_count": "20", "completeness_percentage": 0.0}, "dept_14": {"missing_count": "20", "completeness_percentage": 0.0}, "dept_14_dob": {"missing_count": "20", "completeness_percentage": 0.0}, "dept_14_age": {"missing_count": "20", "completeness_percentage": 0.0}, "dept_14_gender": {"missing_count": "20", "completeness_percentage": 0.0}, "relationship_type_14": {"missing_count": "20", "completeness_percentage": 0.0}, "dept_15": {"missing_count": "20", "completeness_percentage": 0.0}, "dept_15_dob": {"missing_count": "20", "completeness_percentage": 0.0}, "dept_15_age": {"missing_count": "20", "completeness_percentage": 0.0}, "dept_15_gender": {"missing_count": "20", "completeness_percentage": 0.0}, "relationship_type_15": {"missing_count": "20", "completeness_percentage": 0.0}, "dept_16": {"missing_count": "20", "completeness_percentage": 0.0}, "dept_16_dob": {"missing_count": "20", "completeness_percentage": 0.0}, "dept_16_age": {"missing_count": "20", "completeness_percentage": 0.0}, "dept_16_gender": {"missing_count": "20", "completeness_percentage": 0.0}, "relationship_type_16": {"missing_count": "20", "completeness_percentage": 0.0}, "dept_17": {"missing_count": "20", "completeness_percentage": 0.0}, "dept_17_dob": {"missing_count": "20", "completeness_percentage": 0.0}, "dept_17_age": {"missing_count": "20", "completeness_percentage": 0.0}, "dept_17_gender": {"missing_count": "20", "completeness_percentage": 0.0}, "relationship_type_17": {"missing_count": "20", "completeness_percentage": 0.0}, "dept_18": {"missing_count": "20", "completeness_percentage": 0.0}, "dept_18_dob": {"missing_count": "20", "completeness_percentage": 0.0}, "dept_18_age": {"missing_count": "20", "completeness_percentage": 0.0}, "dept_18_gender": {"missing_count": "20", "completeness_percentage": 0.0}, "relationship_type_18": {"missing_count": "20", "completeness_percentage": 0.0}, "dept_19": {"missing_count": "20", "completeness_percentage": 0.0}, "dept_19_dob": {"missing_count": "20", "completeness_percentage": 0.0}, "dept_19_age": {"missing_count": "20", "completeness_percentage": 0.0}, "dept_19_gender": {"missing_count": "20", "completeness_percentage": 0.0}, "relationship_type_19": {"missing_count": "20", "completeness_percentage": 0.0}, "dept_20": {"missing_count": "20", "completeness_percentage": 0.0}, "dept_20_dob": {"missing_count": "20", "completeness_percentage": 0.0}, "dept_20_age": {"missing_count": "20", "completeness_percentage": 0.0}, "dept_20_gender": {"missing_count": "20", "completeness_percentage": 0.0}, "relationship_type_20": {"missing_count": "20", "completeness_percentage": 0.0}, "job_type": {"missing_count": "0", "completeness_percentage": 100.0}, "region": {"missing_count": "0", "completeness_percentage": 100.0}, "health_condition": {"missing_count": "0", "completeness_percentage": 100.0}, "chronic_condition": {"missing_count": "0", "completeness_percentage": 100.0}, "prescription_use": {"missing_count": "0", "completeness_percentage": 100.0}, "income_tier": {"missing_count": "0", "completeness_percentage": 100.0}, "risk_tolerance": {"missing_count": "0", "completeness_percentage": 100.0}, "lifestyle": {"missing_count": "0", "completeness_percentage": 100.0}, "travel_frequency": {"missing_count": "0", "completeness_percentage": 100.0}, "hsa_familiarity": {"missing_count": "0", "completeness_percentage": 100.0}, "mental_health_needs": {"missing_count": "0", "completeness_percentage": 100.0}}, "critical_field_completeness": {"name": {"missing_count": "0", "completeness_percentage": 100.0}, "gender": {"missing_count": "0", "completeness_percentage": 100.0}, "zipcode": {"missing_count": "0", "completeness_percentage": 100.0}, "marital_status": {"missing_count": "0", "completeness_percentage": 100.0}}}}, "feature_validation": {"is_valid": true, "missing_features": [], "feature_completeness": {"age": {"present": true, "missing_values": "0", "completion_rate": 100.0}, "gender": {"present": true, "missing_values": "0", "completion_rate": 100.0}, "marital_status": {"present": true, "missing_values": "0", "completion_rate": 100.0}, "dept_count": {"present": true, "missing_values": "0", "completion_rate": 100.0}, "job_type": {"present": true, "missing_values": "0", "completion_rate": 100.0}, "employment_type": {"present": true, "missing_values": "0", "completion_rate": 100.0}, "department": {"present": true, "missing_values": "0", "completion_rate": 100.0}, "region": {"present": true, "missing_values": "0", "completion_rate": 100.0}, "health_condition": {"present": true, "missing_values": "0", "completion_rate": 100.0}, "chronic_condition": {"present": true, "missing_values": "0", "completion_rate": 100.0}, "prescription_use": {"present": true, "missing_values": "0", "completion_rate": 100.0}, "income_tier": {"present": true, "missing_values": "0", "completion_rate": 100.0}, "risk_tolerance": {"present": true, "missing_values": "0", "completion_rate": 100.0}, "lifestyle": {"present": true, "missing_values": "0", "completion_rate": 100.0}, "travel_frequency": {"present": true, "missing_values": "0", "completion_rate": 100.0}, "hsa_familiarity": {"present": true, "missing_values": "0", "completion_rate": 100.0}, "pregnancy_status": {"present": true, "missing_values": "0", "completion_rate": 100.0}, "tobacco_use": {"present": true, "missing_values": "0", "completion_rate": 100.0}, "mental_health_needs": {"present": true, "missing_values": "0", "completion_rate": 100.0}}, "overall_completion_rate": 100.0, "ready_for_prediction": "True"}, "prediction_method": "ml_models", "model_predictions": {"plan_types": {"total_predictions": 20, "average_confidence": "0.76261204", "unique_plans": 6}}, "prediction_summary": {"total_employees": 20, "plan_type_distribution": {"successful_predictions": {"HMO": 7, "POS": 5, "PPO": 5, "HDHP": 1, "EPO": 1, "HDHP + HSA": 1}, "failed_predictions": "0", "success_rate": 100.0, "total_predicted": "20"}, "benefits_distribution": {}, "confidence_metrics": {"plan_confidence": {"mean": "0.763", "min": "0.520", "max": "0.985", "count": 20}}, "prediction_success_rates": {}, "top_3_plan_probabilities": ["E001: [0.799975, 0.09293648, 0.*********]", "E002: [0.519858, 0.38336724, 0.08059388]", "E003: [0.7276175, 0.*********, 0.07436879]", "E004: [0.6415711, 0.33364284, 0.*********]", "E005: [0.7437587, 0.********, 0.*********]", "E006: [0.5326413, 0.********, 0.********]", "E007: [0.8127314, 0.16222602, 0.010559998]", "E008: [0.8527868, 0.08081805, 0.04412484]", "E009: [0.7863343, 0.08495092, 0.07835027]", "E010: [0.5232488, 0.30636552, 0.11209288]", "E011: [0.82086664, 0.14504951, 0.02822561]", "E012: [0.8665187, 0.09582562, 0.023971833]", "E013: [0.9845563, 0.005368719, 0.004275845]", "E014: [0.7689586, 0.16842997, 0.03366874]", "E015: [0.8606728, 0.12565333, 0.009386095]", "E016: [0.93564117, 0.*********, 0.*********]", "E017: [0.9043829, 0.04808292, 0.03058851]", "E018: [0.5681863, 0.16995806, 0.08870534]", "E019: [0.7341789, 0.24794313, 0.012737661]"]}}}}}