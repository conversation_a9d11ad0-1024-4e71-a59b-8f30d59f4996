{"file_info": {"original_filename": "col4_count_only.csv", "processing_timestamp": "2025-07-15 17:30:43.677222", "total_processing_time_seconds": 9.519044399261475}, "final_response": {"success": true, "data": {"enriched_data_csv": "employee_id,first_name,last_name,gender,dob,address1,city,state,zipcode,marital_status,salary,medical_plan,dental_plan,vision_plan,coverage_tier,dept_count,name,age,middle_name,address2,relationship,record_type,employment_type,employee_class,department,hire_date,tobacco_use,pregnancy_status,life_plan,add_plan,ssn,dept_1,dept_1_dob,dept_1_age,dept_1_gender,relationship_type_1,dept_2,dept_2_dob,dept_2_age,dept_2_gender,relationship_type_2,dept_3,dept_3_dob,dept_3_age,dept_3_gender,relationship_type_3,dept_4,dept_4_dob,dept_4_age,dept_4_gender,relationship_type_4,dept_5,dept_5_dob,dept_5_age,dept_5_gender,relationship_type_5,dept_6,dept_6_dob,dept_6_age,dept_6_gender,relationship_type_6,dept_7,dept_7_dob,dept_7_age,dept_7_gender,relationship_type_7,dept_8,dept_8_dob,dept_8_age,dept_8_gender,relationship_type_8,dept_9,dept_9_dob,dept_9_age,dept_9_gender,relationship_type_9,dept_10,dept_10_dob,dept_10_age,dept_10_gender,relationship_type_10,dept_11,dept_11_dob,dept_11_age,dept_11_gender,relationship_type_11,dept_12,dept_12_dob,dept_12_age,dept_12_gender,relationship_type_12,dept_13,dept_13_dob,dept_13_age,dept_13_gender,relationship_type_13,dept_14,dept_14_dob,dept_14_age,dept_14_gender,relationship_type_14,dept_15,dept_15_dob,dept_15_age,dept_15_gender,relationship_type_15,dept_16,dept_16_dob,dept_16_age,dept_16_gender,relationship_type_16,dept_17,dept_17_dob,dept_17_age,dept_17_gender,relationship_type_17,dept_18,dept_18_dob,dept_18_age,dept_18_gender,relationship_type_18,dept_19,dept_19_dob,dept_19_age,dept_19_gender,relationship_type_19,dept_20,dept_20_dob,dept_20_age,dept_20_gender,relationship_type_20,job_type,region,health_condition,chronic_condition,prescription_use,income_tier,risk_tolerance,lifestyle,travel_frequency,hsa_familiarity,mental_health_needs,predicted_plan_type,plan_confidence,plan_reason,top_3_plans,top_3_plan_confidences,predicted_benefits,benefits_confidence,benefits_reason,top_3_benefits,top_3_benefits_confidences\r\nE001,John,Smith,Male,1980-01-15,123 Main St,Anytown,CA,90210,Married,65000,Y,Y,Y,Family,2,John Smith,45.0,,,,,Part-Time,,Finance,,N,N,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Desk,Urban,Excellent,N,None,Medium ($50K–$100K),Low,Moderate,Rare,Y,N,HDHP + HSA,0.8389241,\"Medium income, urban area - EPO for network-based care.\",\"['HDHP + HSA', 'POS', 'PPO']\",\"[0.8389241, 0.12671588, 0.*********]\",\"['Dental', 'Vision']\",1.0,Dental: Essential oral health coverage for all employees.; Vision: Eye care coverage for all employees.,\"['Employee Assistance', 'Wellness Programs', 'FSA']\",\"[0.5, 0.5, 0.5]\"\r\nE002,Alice,Brown,Female,1975-03-10,456 Oak Ave,Somewhere,TX,75001,Not Married,52000,Y,N,Y,Employee + Child(ren),1,Alice Brown,50.0,,,,,Full-Time,,Manufacturing,,N,N,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Desk,Urban,Good,Y,Regular,Medium ($50K–$100K),Low,Moderate,Occasional,N,N,POS,0.4627546,\"Medium income, good health - PPO for balanced cost and flexibility.\",\"['POS', 'PPO', 'HMO']\",\"[0.4627546, 0.44171563, 0.06324112]\",\"['Dental', 'Vision', 'Accident', 'Critical Illness', 'STD']\",1.0,Dental: Essential oral health coverage for all employees.; Vision: Eye care coverage for all employees.; Critical Illness: Important protection for age 50 with good health status.; STD: Income protection for Medium ($50K–$100K) earners or good health status.,\"['Employee Assistance', 'Wellness Programs', 'FSA']\",\"[0.5, 0.5, 0.5]\"\r\nE003,Charlie,Davis,Male,1990-06-30,789 Pine Rd,Elsewhere,FL,33101,Married,78500,Y,Y,Y,Family,3,Charlie Davis,35.0,,,,,Full-Time,,Information Technology,,N,N,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Desk,Urban,Fair,N,None,High (>$100K),Medium,Moderate,Rare,Y,N,HDHP + HSA,0.********,Middle age with dependents - PPO for family coverage.,\"['HDHP + HSA', 'PPO', 'HDHP']\",\"[0.********, 0.********, 0.*********]\",['FSA'],1.0,FSA: Tax-advantaged account for medical expenses due to health conditions or pregnancy.,\"['Employee Assistance', 'Wellness Programs', 'FSA']\",\"[0.5, 0.5, 0.5]\"\r\nE004,Grace,Wilson,Female,1985-11-05,321 Elm St,Nowhere,NY,10001,Not Married,45000,Y,Y,N,Employee + Child(ren),1,Grace Wilson,39.0,,,,,Contract,,Sales,,N,N,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Desk,Urban,Good,N,None,Medium ($50K–$100K),Medium,Moderate,Occasional,N,Y,POS,0.********,\"Medium income, good health - PPO for balanced cost and flexibility.\",\"['POS', 'HMO', 'HDHP']\",\"[0.********, 0.*********, 0.*********]\",\"['Dental', 'Vision', 'Employee Assistance']\",1.0,Dental: Essential oral health coverage for all employees.; Vision: Eye care coverage for all employees.,\"['Employee Assistance', 'Wellness Programs', 'FSA']\",\"[0.5, 0.5, 0.5]\"\r\nE005,Isaac,Johnson,Male,1978-02-28,654 Maple Dr,Anywhere,WA,98001,Married,95000,Y,Y,Y,Family,3,Isaac Johnson,47.0,,,,,Part-Time,,Engineering,,N,N,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Desk,Urban,Fair,N,None,High (>$100K),Low,Moderate,Rare,Y,N,HDHP + HSA,0.********,Middle age with dependents - PPO for family coverage.,\"['HDHP + HSA', 'PPO', 'Indemnity']\",\"[0.********, 0.********, 0.*********]\",\"['Accident', 'STD', 'FSA']\",1.0,STD: Income protection for High (>$100K) earners or fair health status.; FSA: Tax-advantaged account for medical expenses due to health conditions or pregnancy.,\"['Employee Assistance', 'Wellness Programs', 'FSA']\",\"[0.5, 0.5, 0.5]\"\r\nE006,Michael,Anderson,Male,1983-07-12,987 Cedar Ln,Someplace,OR,97001,Not Married,38000,N,N,N,Employee Only,0,Michael Anderson,42.0,,,,,Full-Time,,Information Technology,,N,N,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Desk,Urban,Good,N,None,Low (<$50K),Medium,Moderate,Occasional,N,N,HMO,0.********,Middle age with dependents - PPO for family coverage.,\"['HMO', 'POS', 'PPO']\",\"[0.********, 0.*********, 0.*********]\",[],0.0,ML model prediction,\"['Employee Assistance', 'Wellness Programs', 'FSA']\",\"[0.5, 0.5, 0.5]\"\r\nE007,Nancy,Taylor,Female,1987-12-03,147 Birch Ave,Othertown,AZ,85001,Married,72000,Y,Y,Y,Employee + Spouse,1,Nancy Taylor,37.0,,,,,Part-Time,,Engineering,,N,N,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Desk,Urban,Good,N,None,Medium ($50K–$100K),Low,Moderate,Rare,N,N,POS,0.8320051,\"Medium income, good health - PPO for balanced cost and flexibility.\",\"['POS', 'HMO', 'HDHP']\",\"[0.8320051, 0.06180143, 0.05192994]\",\"['Dental', 'Vision', 'Accident', 'STD']\",1.0,Dental: Essential oral health coverage for all employees.; Vision: Eye care coverage for all employees.; STD: Income protection for Medium ($50K–$100K) earners or good health status.,\"['Employee Assistance', 'Wellness Programs', 'FSA']\",\"[0.5, 0.5, 0.5]\"\r\nE008,Quinn,Martinez,Male,1979-10-17,258 Spruce St,Newplace,CO,80001,Not Married,58000,Y,Y,Y,Employee + Child(ren),2,Quinn Martinez,45.0,,,,,Full-Time,,Finance,,N,N,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Desk,Suburban,Good,N,None,Medium ($50K–$100K),Low,Moderate,Rare,Y,N,HDHP + HSA,0.7829865,\"Medium income, good health - PPO for balanced cost and flexibility.\",\"['HDHP + HSA', 'POS', 'PPO']\",\"[0.7829865, 0.18524686, 0.013601141]\",\"['Dental', 'Vision']\",1.0,Dental: Essential oral health coverage for all employees.; Vision: Eye care coverage for all employees.,\"['Employee Assistance', 'Wellness Programs', 'FSA']\",\"[0.5, 0.5, 0.5]\"\r\nE009,Tina,Garcia,Female,1981-03-24,369 Willow Way,Lastplace,NV,89001,Married,67000,Y,Y,Y,Family,2,Tina Garcia,44.0,,,,,Full-Time,,Finance,,N,N,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Desk,Rural,Good,N,None,Medium ($50K–$100K),Low,Active,Rare,N,N,POS,0.864179,\"Medium income, good health - PPO for balanced cost and flexibility.\",\"['POS', 'HMO', 'PPO']\",\"[0.864179, 0.06393268, 0.052214127]\",\"['Dental', 'Vision']\",1.0,Dental: Essential oral health coverage for all employees.; Vision: Eye care coverage for all employees.,\"['Employee Assistance', 'Wellness Programs', 'FSA']\",\"[0.5, 0.5, 0.5]\"\r\nE010,Xavier,Rodriguez,Male,1986-09-09,741 Poplar Pl,Finaltown,UT,84001,Not Married,41000,N,N,N,Employee Only,0,Xavier Rodriguez,38.0,,,,,Full-Time,,Finance,,N,N,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Desk,Rural,Good,N,None,Medium ($50K–$100K),Low,Moderate,Occasional,N,N,POS,0.6398547,\"Medium income, good health - PPO for balanced cost and flexibility.\",\"['POS', 'HMO', 'HDHP']\",\"[0.6398547, 0.115738586, 0.109267496]\",[],0.0,ML model prediction,\"['Employee Assistance', 'Wellness Programs', 'FSA']\",\"[0.5, 0.5, 0.5]\"\r\nE011,Yvonne,Lee,Female,1984-12-18,852 Ash Ct,Hometown,MT,59001,Married,63000,Y,Y,Y,Employee + Spouse,1,Yvonne Lee,40.0,,,,,Full-Time,,Sales,,N,N,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Desk,Rural,Good,N,Occasional,Medium ($50K–$100K),Medium,Active,Frequent,Y,N,POS,0.8354502,\"Medium income, good health - PPO for balanced cost and flexibility.\",\"['POS', 'HDHP + HSA', 'PPO']\",\"[0.8354502, 0.062074736, 0.040318042]\",\"['Dental', 'Vision']\",1.0,Dental: Essential oral health coverage for all employees.; Vision: Eye care coverage for all employees.,\"['Employee Assistance', 'Wellness Programs', 'FSA']\",\"[0.5, 0.5, 0.5]\"\r\nE012,Aaron,White,Male,1977-04-07,963 Beech St,Yourtown,ID,83001,Not Married,49000,Y,Y,Y,Employee + Child(ren),2,Aaron White,48.0,,,,,Full-Time,,Engineering,,Y,N,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Desk,Rural,Fair,Y,Regular,Medium ($50K–$100K),Low,Moderate,Rare,Y,N,HDHP + HSA,0.7843826,\"Chronic conditions, older age - POS for coordinated care management.\",\"['HDHP + HSA', 'PPO', 'POS']\",\"[0.7843826, 0.1928217, 0.**********]\",\"['Dental', 'Vision']\",0.5,Default benefits assignment due to prediction error,\"['Dental', 'Vision', 'Hospital Indemnity']\",\"[0.5, 0.5, 0.5]\"\r\nE013,Brooke,Harris,Female,1989-08-21,159 Cherry Ave,Mytown,WY,82001,Not Married,35000,N,N,N,Employee Only,0,Brooke Harris,35.0,,,,,Full-Time,,Engineering,,N,N,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Desk,Rural,Excellent,Y,Regular,Low (<$50K),Medium,Active,Occasional,N,N,PPO,0.6925033,Middle age with dependents - PPO for family coverage.,\"['PPO', 'HMO', 'POS']\",\"[0.6925033, 0.2408892, 0.04169866]\",\"['Dental', 'Vision']\",0.5,Default benefits assignment due to prediction error,\"['Dental', 'Vision', 'Hospital Indemnity']\",\"[0.5, 0.5, 0.5]\"\r\nE014,Connor,Clark,Male,1982-01-26,357 Dogwood Dr,Ourtown,ND,58001,Married,85000,Y,Y,Y,Family,3,Connor Clark,43.0,,,,,Full-Time,,Sales,,Y,N,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Desk,Rural,Good,N,None,High (>$100K),Medium,Moderate,Occasional,Y,N,HDHP + HSA,0.87886167,\"High income, good health, HSA familiar, no chronic conditions - HDHP + HSA for tax benefits.\",\"['HDHP + HSA', 'POS', 'PPO']\",\"[0.87886167, 0.07075396, 0.*********]\",\"['Dental', 'Vision']\",0.5,Default benefits assignment due to prediction error,\"['Dental', 'Vision', 'Hospital Indemnity']\",\"[0.5, 0.5, 0.5]\"\r\nE015,Destiny,Lewis,Female,1986-05-13,468 Fir Ln,Theirtown,SD,57001,Not Married,44000,Y,Y,Y,Employee + Child(ren),1,Destiny Lewis,39.0,,,,,Full-Time,,Manufacturing,,Y,N,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Desk,Rural,Good,N,None,Medium ($50K–$100K),Medium,Active,Rare,N,N,POS,0.79829973,\"Medium income, good health - PPO for balanced cost and flexibility.\",\"['POS', 'HMO', 'HDHP']\",\"[0.79829973, 0.07172966, 0.*********]\",\"['Dental', 'Vision']\",0.5,Default benefits assignment due to prediction error,\"['Dental', 'Vision', 'Hospital Indemnity']\",\"[0.5, 0.5, 0.5]\"\r\nE016,Ethan,Robinson,Male,1979-09-28,579 Gum Tree Rd,Alltown,NE,68001,Married,92000,Y,Y,Y,Family,3,Ethan Robinson,45.0,,,,,Full-Time,,Finance,,N,N,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Desk,Suburban,Excellent,N,None,High (>$100K),Medium,Active,Rare,Y,Y,HDHP + HSA,0.8945745,\"High income, good health, HSA familiar, no chronic conditions - HDHP + HSA for tax benefits.\",\"['HDHP + HSA', 'PPO', 'POS']\",\"[0.8945745, 0.*********, 0.*********]\",\"['Dental', 'Vision']\",0.5,Default benefits assignment due to prediction error,\"['Dental', 'Vision', 'Hospital Indemnity']\",\"[0.5, 0.5, 0.5]\"\r\nE017,Fiona,Walker,Female,1983-02-14,680 Hickory Way,Notown,KS,66001,Not Married,56000,Y,Y,Y,Employee + Child(ren),2,Fiona Walker,42.0,,,,,Full-Time,,Manufacturing,,N,N,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Desk,Suburban,Good,N,None,Medium ($50K–$100K),Low,Active,Rare,N,N,POS,0.8653487,\"Medium income, good health - PPO for balanced cost and flexibility.\",\"['POS', 'PPO', 'HMO']\",\"[0.8653487, 0.*********, 0.*********]\",\"['Dental', 'Vision']\",0.5,Default benefits assignment due to prediction error,\"['Dental', 'Vision', 'Hospital Indemnity']\",\"[0.5, 0.5, 0.5]\"\r\nE018,Gabriel,Hall,Male,1985-06-09,791 Ivy Pl,Everytown,OK,73001,Not Married,39000,N,N,N,Employee Only,0,Gabriel Hall,40.0,,,,,Full-Time,,Information Technology,,N,N,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Desk,Urban,Good,N,None,Low (<$50K),Low,Moderate,Occasional,N,N,HMO,0.91217333,Middle age with dependents - PPO for family coverage.,\"['HMO', 'POS', 'PPO']\",\"[0.91217333, 0.05060343, 0.02713377]\",\"['Dental', 'Vision']\",0.5,Default benefits assignment due to prediction error,\"['Dental', 'Vision', 'Hospital Indemnity']\",\"[0.5, 0.5, 0.5]\"\r\nE019,Hannah,Allen,Female,1980-10-22,802 Juniper St,Anyplace,AR,72001,Married,71000,Y,Y,Y,Employee + Child(ren),2,Hannah Allen,44.0,,,,,Full-Time,,Manufacturing,,N,N,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Desk,Urban,Excellent,Y,Regular,Medium ($50K–$100K),Low,Active,Rare,N,N,PPO,0.58611363,\"Medium income, urban area - EPO for network-based care.\",\"['PPO', 'POS', 'HMO']\",\"[0.58611363, 0.25714287, 0.100834146]\",\"['Dental', 'Vision']\",0.5,Default benefits assignment due to prediction error,\"['Dental', 'Vision', 'Hospital Indemnity']\",\"[0.5, 0.5, 0.5]\"\r\nE020,Ian,Young,Male,1987-03-16,913 Koa Ave,Somewhere,LA,70001,Not Married,47000,Y,Y,Y,Employee Only,1,Ian Young,38.0,,,,,Full-Time,,Sales,,N,N,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Desk,Rural,Good,N,Occasional,Medium ($50K–$100K),Medium,Moderate,Frequent,Y,Y,HDHP,0.75444376,\"Medium income, good health - PPO for balanced cost and flexibility.\",\"['HDHP', 'POS', 'HDHP + HSA']\",\"[0.75444376, 0.18242283, 0.029167632]\",\"['Dental', 'Vision']\",0.5,Default benefits assignment due to prediction error,\"['Dental', 'Vision', 'Hospital Indemnity']\",\"[0.5, 0.5, 0.5]\"\r\nE021,Jessica,King,Female,1992-07-20,124 Pine St,Newtown,TX,75002,Not Married,55000,Y,Y,Y,Employee Only,0,Jessica King,32.0,,,,,Full-Time,,Sales,,N,N,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Desk,Urban,Good,N,None,Medium ($50K–$100K),Low,Active,Frequent,Y,N,HDHP + HSA,0.74112797,\"Medium income, good health - PPO for balanced cost and flexibility.\",\"['HDHP + HSA', 'HDHP', 'POS']\",\"[0.74112797, 0.21655683, 0.027647566]\",\"['Dental', 'Vision']\",0.5,Default benefits assignment due to prediction error,\"['Dental', 'Vision', 'Hospital Indemnity']\",\"[0.5, 0.5, 0.5]\"\r\nE022,Kevin,Scott,Male,1988-11-14,235 Oak Dr,Oldtown,CA,90211,Not Married,48000,N,N,N,Employee Only,1,Kevin Scott,36.0,,,,,Full-Time,,Sales,,Y,N,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Desk,Urban,Good,Y,Regular,Medium ($50K–$100K),Low,Moderate,Frequent,Y,N,HDHP + HSA,0.84857947,\"Medium income, good health - PPO for balanced cost and flexibility.\",\"['HDHP + HSA', 'PPO', 'POS']\",\"[0.84857947, 0.058724277, 0.04506003]\",\"['Dental', 'Vision']\",0.5,Default benefits assignment due to prediction error,\"['Dental', 'Vision', 'Hospital Indemnity']\",\"[0.5, 0.5, 0.5]\"\r\nE023,Laura,Green,Female,1984-05-08,346 Elm Ave,Midtown,FL,33102,Married,69000,Y,Y,Y,Employee + Spouse,2,Laura Green,41.0,,,,,Full-Time,,Sales,,N,N,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Desk,Urban,Fair,N,None,Medium ($50K–$100K),Medium,Moderate,Frequent,N,N,POS,0.60377824,\"Medium income, urban area - EPO for network-based care.\",\"['POS', 'PPO', 'HMO']\",\"[0.60377824, 0.33814672, 0.036496077]\",\"['Dental', 'Vision']\",0.5,Default benefits assignment due to prediction error,\"['Dental', 'Vision', 'Hospital Indemnity']\",\"[0.5, 0.5, 0.5]\"\r\nE024,Mark,Adams,Male,1976-09-12,457 Maple Ln,Uptown,NY,10002,Married,82000,Y,Y,Y,Family,4,Mark Adams,48.0,,,,,Full-Time,,Manufacturing,,N,N,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Desk,Urban,Good,N,None,High (>$100K),Medium,Moderate,Occasional,Y,N,HDHP + HSA,0.8798985,\"High income, good health, HSA familiar, no chronic conditions - HDHP + HSA for tax benefits.\",\"['HDHP + HSA', 'POS', 'PPO']\",\"[0.8798985, 0.06927904, 0.03261057]\",\"['Dental', 'Vision']\",0.5,Default benefits assignment due to prediction error,\"['Dental', 'Vision', 'Hospital Indemnity']\",\"[0.5, 0.5, 0.5]\"\r\nE025,Nina,Baker,Female,1990-12-25,568 Cedar St,Downtown,WA,98002,Not Married,41000,Y,Y,Y,Employee Only,0,Nina Baker,34.0,,,,,Full-Time,,Manufacturing,,N,N,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Desk,Urban,Excellent,N,None,Medium ($50K–$100K),Low,Moderate,Occasional,Y,N,HDHP + HSA,0.73361194,\"Medium income, urban area - EPO for network-based care.\",\"['HDHP + HSA', 'HDHP', 'POS']\",\"[0.73361194, 0.21436065, 0.03394962]\",\"['Dental', 'Vision']\",0.5,Default benefits assignment due to prediction error,\"['Dental', 'Vision', 'Hospital Indemnity']\",\"[0.5, 0.5, 0.5]\"\r\n", "comprehensive_statistics": {"basic_demographics": {"age_statistics": {"average_age": 41.08, "median_age": 41.0, "age_range": {"min": 32, "max": 50}, "age_distribution": {"18-30": 0, "31-45": 21, "46-60": 4, "60+": 0}}, "gender_composition": {"counts": {"Male": 13, "Female": 12}, "percentages": {"Male": 52.0, "Female": 48.0}}, "marital_status_distribution": {"counts": {"Not Married": 14, "Married": 11}, "percentages": {"Not Married": 56.0, "Married": 44.0}}}, "dependent_analysis": {"dependent_count_distribution": {"average_dependents": 1.48, "median_dependents": 1.0, "distribution": {"2": 7, "1": 7, "0": 6, "3": 4, "4": 1}, "employees_with_dependents": 19, "percentage_with_dependents": 76.0}}, "employment_demographics": {"department_distribution": {"counts": {"Sales": 7, "Manufacturing": 6, "Finance": 5, "Engineering": 4, "Information Technology": 3}, "percentages": {"Sales": 28.0, "Manufacturing": 24.0, "Finance": 20.0, "Engineering": 16.0, "Information Technology": 12.0}}, "employment_type_distribution": {"counts": {"Full-Time": 21, "Part-Time": 3, "Contract": 1}, "percentages": {"Full-Time": 84.0, "Part-Time": 12.0, "Contract": 4.0}}, "job_type_distribution": {"counts": {"Desk": 25}, "percentages": {"Desk": 100.0}}, "employee_class_distribution": {"counts": {}, "percentages": {}}, "salary_analysis": {"average_salary": 59500.0, "median_salary": 56000.0, "salary_range": {"min": "35000", "max": "95000"}, "salary_distribution": {"under_40k": 3, "40k_75k": 17, "75k_100k": 5, "over_100k": 0}}}, "health_and_lifestyle": {"health_condition_distribution": {"counts": {"Good": 16, "Excellent": 5, "Fair": 4}, "percentages": {"Good": 64.0, "Excellent": 20.0, "Fair": 16.0}}, "chronic_condition_distribution": {"counts": {"N": 20, "Y": 5}, "percentages": {"N": 80.0, "Y": 20.0}}, "tobacco_use_distribution": {"counts": {"N": 21, "Y": 4}, "percentages": {"N": 84.0, "Y": 16.0}}, "lifestyle_distribution": {"counts": {"Moderate": 17, "Active": 8}, "percentages": {"Moderate": 68.0, "Active": 32.0}}, "prescription_use_distribution": {"counts": {"None": 18, "Regular": 5, "Occasional": 2}, "percentages": {"None": 72.0, "Regular": 20.0, "Occasional": 8.0}}}, "coverage_analysis": {"coverage_tier_distribution": {"counts": {"Employee Only": 8, "Family": 7, "Employee + Child(ren)": 7, "Employee + Spouse": 3}, "percentages": {"Employee Only": 32.0, "Family": 28.0, "Employee + Child(ren)": 28.0, "Employee + Spouse": 12.0}}, "coverage_tier_by_family_size": {"Employee + Child(ren)": {"0": 0, "1": 3, "2": 4, "3": 0, "4": 0}, "Employee + Spouse": {"0": 0, "1": 2, "2": 1, "3": 0, "4": 0}, "Employee Only": {"0": 6, "1": 2, "2": 0, "3": 0, "4": 0}, "Family": {"0": 0, "1": 0, "2": 2, "3": 4, "4": 1}}, "medical_plan_distribution": {"counts": {"Y": 20, "N": 5}, "percentages": {"Y": 80.0, "N": 20.0}}, "dental_plan_distribution": {"counts": {"Y": 19, "N": 6}, "percentages": {"Y": 76.0, "N": 24.0}}, "vision_plan_distribution": {"counts": {"Y": 19, "N": 6}, "percentages": {"Y": 76.0, "N": 24.0}}, "life_plan_distribution": {"counts": {}, "percentages": {}}}, "geographic_distribution": {"state_distribution": {"counts": {"CA": 2, "FL": 2, "NY": 2, "WA": 2, "TX": 2, "WY": 1, "AR": 1, "OK": 1, "KS": 1, "NE": 1, "SD": 1, "ND": 1, "MT": 1, "ID": 1, "UT": 1, "NV": 1, "CO": 1, "AZ": 1, "OR": 1, "LA": 1}, "percentages": {"CA": 8.0, "FL": 8.0, "NY": 8.0, "WA": 8.0, "TX": 8.0, "WY": 4.0, "AR": 4.0, "OK": 4.0, "KS": 4.0, "NE": 4.0, "SD": 4.0, "ND": 4.0, "MT": 4.0, "ID": 4.0, "UT": 4.0, "NV": 4.0, "CO": 4.0, "AZ": 4.0, "OR": 4.0, "LA": 4.0}}, "region_distribution": {"counts": {"Urban": 14, "Rural": 8, "Suburban": 3}, "percentages": {"Urban": 56.0, "Rural": 32.0, "Suburban": 12.0}}, "top_cities": {"counts": {"Somewhere": 2, "Anytown": 1, "Uptown": 1, "Midtown": 1, "Oldtown": 1, "Newtown": 1, "Anyplace": 1, "Everytown": 1, "Notown": 1, "Alltown": 1}, "percentages": {"Somewhere": 8.0, "Anytown": 4.0, "Uptown": 4.0, "Midtown": 4.0, "Oldtown": 4.0, "Newtown": 4.0, "Anyplace": 4.0, "Everytown": 4.0, "Notown": 4.0, "Alltown": 4.0}}}, "financial_demographics": {"income_tier_distribution": {"counts": {"Medium ($50K–$100K)": 17, "High (>$100K)": 5, "Low (<$50K)": 3}, "percentages": {"Medium ($50K–$100K)": 68.0, "High (>$100K)": 20.0, "Low (<$50K)": 12.0}}, "risk_tolerance_distribution": {"counts": {"Low": 14, "Medium": 11}, "percentages": {"Low": 56.0, "Medium": 44.0}}, "hsa_familiarity_distribution": {"counts": {"Y": 13, "N": 12}, "percentages": {"Y": 52.0, "N": 48.0}}}, "risk_assessment": {"group_risk_score": 27.44, "group_risk_level": "Low", "risk_distribution": {"low_risk": 16, "medium_risk": 8, "high_risk": 1}, "risk_statistics": {"min_risk_score": 13, "max_risk_score": 68, "median_risk_score": 23.0, "std_risk_score": 13.27}, "top_risk_factors": {"Chronic Conditions": 5, "Poor Health Condition": 4, "Tobacco Use": 4, "Advanced Age": 1}, "individual_risks": [{"employee_id": "E001", "risk_score": 18, "risk_level": "Low", "risk_factors": []}, {"employee_id": "E002", "risk_score": 53, "risk_level": "Medium", "risk_factors": ["Age 50.0 (high risk)", "Has chronic condition"]}, {"employee_id": "E003", "risk_score": 28, "risk_level": "Low", "risk_factors": ["Health condition: Fair"]}, {"employee_id": "E004", "risk_score": 18, "risk_level": "Low", "risk_factors": []}, {"employee_id": "E005", "risk_score": 33, "risk_level": "Medium", "risk_factors": ["Health condition: Fair"]}, {"employee_id": "E006", "risk_score": 23, "risk_level": "Low", "risk_factors": []}, {"employee_id": "E007", "risk_score": 18, "risk_level": "Low", "risk_factors": []}, {"employee_id": "E008", "risk_score": 23, "risk_level": "Low", "risk_factors": []}, {"employee_id": "E009", "risk_score": 20, "risk_level": "Low", "risk_factors": []}, {"employee_id": "E010", "risk_score": 18, "risk_level": "Low", "risk_factors": []}]}, "data_quality_metrics": {"overall_completeness": {"total_cells": 3550, "missing_cells": "2725", "completeness_percentage": 23.24}, "column_completeness": {"employee_id": {"missing_count": "0", "completeness_percentage": 100.0}, "first_name": {"missing_count": "0", "completeness_percentage": 100.0}, "last_name": {"missing_count": "0", "completeness_percentage": 100.0}, "gender": {"missing_count": "0", "completeness_percentage": 100.0}, "dob": {"missing_count": "0", "completeness_percentage": 100.0}, "address1": {"missing_count": "0", "completeness_percentage": 100.0}, "city": {"missing_count": "0", "completeness_percentage": 100.0}, "state": {"missing_count": "0", "completeness_percentage": 100.0}, "zipcode": {"missing_count": "0", "completeness_percentage": 100.0}, "marital_status": {"missing_count": "0", "completeness_percentage": 100.0}, "salary": {"missing_count": "0", "completeness_percentage": 100.0}, "medical_plan": {"missing_count": "0", "completeness_percentage": 100.0}, "dental_plan": {"missing_count": "0", "completeness_percentage": 100.0}, "vision_plan": {"missing_count": "0", "completeness_percentage": 100.0}, "coverage_tier": {"missing_count": "0", "completeness_percentage": 100.0}, "dept_count": {"missing_count": "0", "completeness_percentage": 100.0}, "name": {"missing_count": "0", "completeness_percentage": 100.0}, "age": {"missing_count": "0", "completeness_percentage": 100.0}, "middle_name": {"missing_count": "25", "completeness_percentage": 0.0}, "address2": {"missing_count": "25", "completeness_percentage": 0.0}, "relationship": {"missing_count": "25", "completeness_percentage": 0.0}, "record_type": {"missing_count": "25", "completeness_percentage": 0.0}, "employment_type": {"missing_count": "0", "completeness_percentage": 100.0}, "employee_class": {"missing_count": "25", "completeness_percentage": 0.0}, "department": {"missing_count": "0", "completeness_percentage": 100.0}, "hire_date": {"missing_count": "25", "completeness_percentage": 0.0}, "tobacco_use": {"missing_count": "0", "completeness_percentage": 100.0}, "pregnancy_status": {"missing_count": "0", "completeness_percentage": 100.0}, "life_plan": {"missing_count": "25", "completeness_percentage": 0.0}, "add_plan": {"missing_count": "25", "completeness_percentage": 0.0}, "ssn": {"missing_count": "25", "completeness_percentage": 0.0}, "dept_1": {"missing_count": "25", "completeness_percentage": 0.0}, "dept_1_dob": {"missing_count": "25", "completeness_percentage": 0.0}, "dept_1_age": {"missing_count": "25", "completeness_percentage": 0.0}, "dept_1_gender": {"missing_count": "25", "completeness_percentage": 0.0}, "relationship_type_1": {"missing_count": "25", "completeness_percentage": 0.0}, "dept_2": {"missing_count": "25", "completeness_percentage": 0.0}, "dept_2_dob": {"missing_count": "25", "completeness_percentage": 0.0}, "dept_2_age": {"missing_count": "25", "completeness_percentage": 0.0}, "dept_2_gender": {"missing_count": "25", "completeness_percentage": 0.0}, "relationship_type_2": {"missing_count": "25", "completeness_percentage": 0.0}, "dept_3": {"missing_count": "25", "completeness_percentage": 0.0}, "dept_3_dob": {"missing_count": "25", "completeness_percentage": 0.0}, "dept_3_age": {"missing_count": "25", "completeness_percentage": 0.0}, "dept_3_gender": {"missing_count": "25", "completeness_percentage": 0.0}, "relationship_type_3": {"missing_count": "25", "completeness_percentage": 0.0}, "dept_4": {"missing_count": "25", "completeness_percentage": 0.0}, "dept_4_dob": {"missing_count": "25", "completeness_percentage": 0.0}, "dept_4_age": {"missing_count": "25", "completeness_percentage": 0.0}, "dept_4_gender": {"missing_count": "25", "completeness_percentage": 0.0}, "relationship_type_4": {"missing_count": "25", "completeness_percentage": 0.0}, "dept_5": {"missing_count": "25", "completeness_percentage": 0.0}, "dept_5_dob": {"missing_count": "25", "completeness_percentage": 0.0}, "dept_5_age": {"missing_count": "25", "completeness_percentage": 0.0}, "dept_5_gender": {"missing_count": "25", "completeness_percentage": 0.0}, "relationship_type_5": {"missing_count": "25", "completeness_percentage": 0.0}, "dept_6": {"missing_count": "25", "completeness_percentage": 0.0}, "dept_6_dob": {"missing_count": "25", "completeness_percentage": 0.0}, "dept_6_age": {"missing_count": "25", "completeness_percentage": 0.0}, "dept_6_gender": {"missing_count": "25", "completeness_percentage": 0.0}, "relationship_type_6": {"missing_count": "25", "completeness_percentage": 0.0}, "dept_7": {"missing_count": "25", "completeness_percentage": 0.0}, "dept_7_dob": {"missing_count": "25", "completeness_percentage": 0.0}, "dept_7_age": {"missing_count": "25", "completeness_percentage": 0.0}, "dept_7_gender": {"missing_count": "25", "completeness_percentage": 0.0}, "relationship_type_7": {"missing_count": "25", "completeness_percentage": 0.0}, "dept_8": {"missing_count": "25", "completeness_percentage": 0.0}, "dept_8_dob": {"missing_count": "25", "completeness_percentage": 0.0}, "dept_8_age": {"missing_count": "25", "completeness_percentage": 0.0}, "dept_8_gender": {"missing_count": "25", "completeness_percentage": 0.0}, "relationship_type_8": {"missing_count": "25", "completeness_percentage": 0.0}, "dept_9": {"missing_count": "25", "completeness_percentage": 0.0}, "dept_9_dob": {"missing_count": "25", "completeness_percentage": 0.0}, "dept_9_age": {"missing_count": "25", "completeness_percentage": 0.0}, "dept_9_gender": {"missing_count": "25", "completeness_percentage": 0.0}, "relationship_type_9": {"missing_count": "25", "completeness_percentage": 0.0}, "dept_10": {"missing_count": "25", "completeness_percentage": 0.0}, "dept_10_dob": {"missing_count": "25", "completeness_percentage": 0.0}, "dept_10_age": {"missing_count": "25", "completeness_percentage": 0.0}, "dept_10_gender": {"missing_count": "25", "completeness_percentage": 0.0}, "relationship_type_10": {"missing_count": "25", "completeness_percentage": 0.0}, "dept_11": {"missing_count": "25", "completeness_percentage": 0.0}, "dept_11_dob": {"missing_count": "25", "completeness_percentage": 0.0}, "dept_11_age": {"missing_count": "25", "completeness_percentage": 0.0}, "dept_11_gender": {"missing_count": "25", "completeness_percentage": 0.0}, "relationship_type_11": {"missing_count": "25", "completeness_percentage": 0.0}, "dept_12": {"missing_count": "25", "completeness_percentage": 0.0}, "dept_12_dob": {"missing_count": "25", "completeness_percentage": 0.0}, "dept_12_age": {"missing_count": "25", "completeness_percentage": 0.0}, "dept_12_gender": {"missing_count": "25", "completeness_percentage": 0.0}, "relationship_type_12": {"missing_count": "25", "completeness_percentage": 0.0}, "dept_13": {"missing_count": "25", "completeness_percentage": 0.0}, "dept_13_dob": {"missing_count": "25", "completeness_percentage": 0.0}, "dept_13_age": {"missing_count": "25", "completeness_percentage": 0.0}, "dept_13_gender": {"missing_count": "25", "completeness_percentage": 0.0}, "relationship_type_13": {"missing_count": "25", "completeness_percentage": 0.0}, "dept_14": {"missing_count": "25", "completeness_percentage": 0.0}, "dept_14_dob": {"missing_count": "25", "completeness_percentage": 0.0}, "dept_14_age": {"missing_count": "25", "completeness_percentage": 0.0}, "dept_14_gender": {"missing_count": "25", "completeness_percentage": 0.0}, "relationship_type_14": {"missing_count": "25", "completeness_percentage": 0.0}, "dept_15": {"missing_count": "25", "completeness_percentage": 0.0}, "dept_15_dob": {"missing_count": "25", "completeness_percentage": 0.0}, "dept_15_age": {"missing_count": "25", "completeness_percentage": 0.0}, "dept_15_gender": {"missing_count": "25", "completeness_percentage": 0.0}, "relationship_type_15": {"missing_count": "25", "completeness_percentage": 0.0}, "dept_16": {"missing_count": "25", "completeness_percentage": 0.0}, "dept_16_dob": {"missing_count": "25", "completeness_percentage": 0.0}, "dept_16_age": {"missing_count": "25", "completeness_percentage": 0.0}, "dept_16_gender": {"missing_count": "25", "completeness_percentage": 0.0}, "relationship_type_16": {"missing_count": "25", "completeness_percentage": 0.0}, "dept_17": {"missing_count": "25", "completeness_percentage": 0.0}, "dept_17_dob": {"missing_count": "25", "completeness_percentage": 0.0}, "dept_17_age": {"missing_count": "25", "completeness_percentage": 0.0}, "dept_17_gender": {"missing_count": "25", "completeness_percentage": 0.0}, "relationship_type_17": {"missing_count": "25", "completeness_percentage": 0.0}, "dept_18": {"missing_count": "25", "completeness_percentage": 0.0}, "dept_18_dob": {"missing_count": "25", "completeness_percentage": 0.0}, "dept_18_age": {"missing_count": "25", "completeness_percentage": 0.0}, "dept_18_gender": {"missing_count": "25", "completeness_percentage": 0.0}, "relationship_type_18": {"missing_count": "25", "completeness_percentage": 0.0}, "dept_19": {"missing_count": "25", "completeness_percentage": 0.0}, "dept_19_dob": {"missing_count": "25", "completeness_percentage": 0.0}, "dept_19_age": {"missing_count": "25", "completeness_percentage": 0.0}, "dept_19_gender": {"missing_count": "25", "completeness_percentage": 0.0}, "relationship_type_19": {"missing_count": "25", "completeness_percentage": 0.0}, "dept_20": {"missing_count": "25", "completeness_percentage": 0.0}, "dept_20_dob": {"missing_count": "25", "completeness_percentage": 0.0}, "dept_20_age": {"missing_count": "25", "completeness_percentage": 0.0}, "dept_20_gender": {"missing_count": "25", "completeness_percentage": 0.0}, "relationship_type_20": {"missing_count": "25", "completeness_percentage": 0.0}, "job_type": {"missing_count": "0", "completeness_percentage": 100.0}, "region": {"missing_count": "0", "completeness_percentage": 100.0}, "health_condition": {"missing_count": "0", "completeness_percentage": 100.0}, "chronic_condition": {"missing_count": "0", "completeness_percentage": 100.0}, "prescription_use": {"missing_count": "0", "completeness_percentage": 100.0}, "income_tier": {"missing_count": "0", "completeness_percentage": 100.0}, "risk_tolerance": {"missing_count": "0", "completeness_percentage": 100.0}, "lifestyle": {"missing_count": "0", "completeness_percentage": 100.0}, "travel_frequency": {"missing_count": "0", "completeness_percentage": 100.0}, "hsa_familiarity": {"missing_count": "0", "completeness_percentage": 100.0}, "mental_health_needs": {"missing_count": "0", "completeness_percentage": 100.0}}, "critical_field_completeness": {"name": {"missing_count": "0", "completeness_percentage": 100.0}, "gender": {"missing_count": "0", "completeness_percentage": 100.0}, "zipcode": {"missing_count": "0", "completeness_percentage": 100.0}, "marital_status": {"missing_count": "0", "completeness_percentage": 100.0}}}}, "prediction_summary": {"total_employees": 25, "plan_type_distribution": {"successful_predictions": {"HDHP + HSA": 11, "POS": 9, "HMO": 2, "PPO": 2, "HDHP": 1}, "failed_predictions": "0", "success_rate": 100.0, "total_predicted": "25"}, "benefits_distribution": {}, "confidence_metrics": {"plan_confidence": {"mean": "0.787", "min": "0.463", "max": "0.960", "count": 25}}, "prediction_success_rates": {}, "top_3_plan_probabilities": []}, "total_employees": 25, "total_columns": 152, "has_predictions": true, "prediction_columns": ["predicted_plan_type", "plan_confidence"]}, "metadata": {"file_processing": {"filename": "col4_count_only.csv", "size": 2811, "original_column_names": ["Employee_ID", "First_Name", "Last_Name", "Gender", "DOB", "Address1", "City", "State", "ZIP", "Marital_Status", "Salary", "Medical_Plan", "Dental_Plan", "Vision_Plan", "Coverage_Tier", "Num_Dependents"]}, "pattern_analysis": {"pattern_type": "dependent_count_only", "pattern_confidence": 1.1, "pattern_reason": "Found 'dept_count' column; No detailed dependent columns found; Found employee fields: ['first_name', 'last_name', 'employee_id']", "analysis_details": {"confidence": 1.1, "reason": "Found 'dept_count' column; No detailed dependent columns found; Found employee fields: ['first_name', 'last_name', 'employee_id']"}}, "field_mapping": {"total_fields_mapped": 16, "mapping_success": true, "unmapped_fields": [], "mapping_confidence": 0, "field_mapping_details": {"Employee_ID": "employee_id", "First_Name": "first_name", "Last_Name": "last_name", "Gender": "gender", "DOB": "dob", "Address1": "address1", "City": "city", "State": "state", "ZIP": "zipcode", "Marital_Status": "marital_status", "Salary": "salary", "Medical_Plan": "medical_plan", "Dental_Plan": "dental_plan", "Vision_Plan": "vision_plan", "Coverage_Tier": "coverage_tier", "Num_Dependents": "dept_count"}}, "data_processing": {"validation_passed": false, "processing_summary": {"original_rows": 25, "original_columns": 16, "processed_rows": 25, "processed_columns": 131, "processing_time_seconds": 3.63, "validation_passed": true, "validation_errors": 0, "error_rows": 0, "fields_processed": {"name_fields": true, "age_converted": true, "gender_standardized": true, "addresses_cleaned": true, "zipcode_validated": true}, "data_quality": {"complete_records": 25, "missing_data_rows": 0}}, "data_quality_score": 0.8, "processing_details": {"success": true, "preprocessed_data": "   employee_id first_name  last_name  gender         dob         address1       city  ... dept_19_gender  relationship_type_19 dept_20  dept_20_dob dept_20_age dept_20_gender relationship_type_20\n0         E001       <PERSON>    Male  1980-01-15      123 Main St    Anytown  ...           None                  None    None         None        None           None                 None\n1         E002      <PERSON>  Female  1975-03-10      456 Oak Ave  Somewhere  ...           None                  None    None         None        None           None                 None\n2         E003    <PERSON>  1990-06-30      789 Pine Rd  Elsewhere  ...           None                  None    None         None        None           None                 None\n3         E004      <PERSON>  1985-11-05       321 Elm St    Nowhere  ...           None                  None    None         None        None           None                 None\n4         E005      <PERSON>  1978-02-28     654 Maple Dr   Anywhere  ...           None                  None    None         None        None           None                 None\n5         E006    <PERSON>  1983-07-12     987 Cedar Ln  Someplace  ...           None                  None    None         None        None           None                 None\n6         E007      <PERSON>  1987-12-03    147 Birch Ave  Othertown  ...           None                  None    None         None        None           None                 None\n7         E008      <PERSON>  1979-10-17    258 Spruce St   Newplace  ...           None                  None    None         None        None           None                 None\n8         E009       <PERSON>  1981-03-24   369 <PERSON>  Lastplace  ...           None                  None    None         None        None           None                 None\n9         E010     <PERSON>  1986-09-09    741 Poplar Pl  Finaltown  ...           None                  None    None         None        None           None                 None\n10        E011     <PERSON>  1984-12-18       852 Ash Ct   Hometown  ...           None                  None    None         None        None           None                 None\n11        E012      <PERSON>  1977-04-07     963 <PERSON><PERSON>   Yourtown  ...           None                  None    None         None        None           None                 None\n12        E013     Brooke     Harris  Female  1989-08-21   159 <PERSON> Ave     Mytown  ...           None                  None    None         None        None           None                 None\n13        E014     Connor      Clark    Male  1982-01-26   357 Dogwood Dr    Ourtown  ...           None                  None    None         None        None           None                 None\n14        E015    Destiny      <PERSON>  Female  1986-05-13       468 Fir Ln  Theirtown  ...           None                  None    None         None        None           None                 None\n15        E016      Ethan   Robinson    Male  1979-09-28  579 Gum Tree Rd    Alltown  ...           None                  None    None         None        None           None                 None\n16        E017      Fiona     Walker  Female  1983-02-14  680 Hickory Way     Notown  ...           None                  None    None         None        None           None                 None\n17        E018    Gabriel       Hall    Male  1985-06-09       791 Ivy Pl  Everytown  ...           None                  None    None         None        None           None                 None\n18        E019     Hannah      Allen  Female  1980-10-22   802 Juniper St   Anyplace  ...           None                  None    None         None        None           None                 None\n19        E020        Ian      Young    Male  1987-03-16      913 Koa Ave  Somewhere  ...           None                  None    None         None        None           None                 None\n20        E021    Jessica       King  Female  1992-07-20      124 Pine St    Newtown  ...           None                  None    None         None        None           None                 None\n21        E022      Kevin      Scott    Male  1988-11-14       235 Oak Dr    Oldtown  ...           None                  None    None         None        None           None                 None\n22        E023      Laura      Green  Female  1984-05-08      346 Elm Ave    Midtown  ...           None                  None    None         None        None           None                 None\n23        E024       Mark      Adams    Male  1976-09-12     457 Maple Ln     Uptown  ...           None                  None    None         None        None           None                 None\n24        E025       Nina      Baker  Female  1990-12-25     568 Cedar St   Downtown  ...           None                  None    None         None        None           None                 None\n\n[25 rows x 131 columns]", "validation": {"is_valid": true, "errors": [], "error_rows": [], "total_errors": 0}, "summary": {"original_rows": 25, "original_columns": 16, "processed_rows": 25, "processed_columns": 131, "processing_time_seconds": 3.63, "validation_passed": true, "validation_errors": 0, "error_rows": 0, "fields_processed": {"name_fields": true, "age_converted": true, "gender_standardized": true, "addresses_cleaned": true, "zipcode_validated": true}, "data_quality": {"complete_records": 25, "missing_data_rows": 0}}}}, "enrichment_and_prediction": {"success": true, "enriched_data": "   employee_id first_name  last_name  ...                                    benefits_reason                                 top_3_benefits top_3_benefits_confidences\n0         E001       <PERSON>  ...  Dental: Essential oral health coverage for all...  [Employee Assistance, Wellness Programs, FSA]            [0.5, 0.5, 0.5]\n1         E002      <PERSON>  ...  Dental: Essential oral health coverage for all...  [Employee Assistance, Wellness Programs, FSA]            [0.5, 0.5, 0.5]\n2         E003    <PERSON>  ...  FSA: Tax-advantaged account for medical expens...  [Employee Assistance, Wellness Programs, FSA]            [0.5, 0.5, 0.5]\n3         E004      <PERSON>  ...  Dental: Essential oral health coverage for all...  [Employee Assistance, Wellness Programs, FSA]            [0.5, 0.5, 0.5]\n4         E005      <PERSON>  ...  STD: Income protection for High (>$100K) earne...  [Employee Assistance, Wellness Programs, FSA]            [0.5, 0.5, 0.5]\n5         E006    <PERSON>  ...                                ML model prediction  [Employee Assistance, Wellness Programs, FSA]            [0.5, 0.5, 0.5]\n6         E007      <PERSON>  ...  Dental: Essential oral health coverage for all...  [Employee Assistance, Wellness Programs, FSA]            [0.5, 0.5, 0.5]\n7         E008      <PERSON>  ...  Dental: Essential oral health coverage for all...  [Employee Assistance, Wellness Programs, FSA]            [0.5, 0.5, 0.5]\n8         E009       Tina     Garcia  ...  Dental: Essential oral health coverage for all...  [Employee Assistance, Wellness Programs, FSA]            [0.5, 0.5, 0.5]\n9         E010     Xavier  Rodriguez  ...                                ML model prediction  [Employee Assistance, Wellness Programs, FSA]            [0.5, 0.5, 0.5]\n10        E011     Yvonne        Lee  ...  Dental: Essential oral health coverage for all...  [Employee Assistance, Wellness Programs, FSA]            [0.5, 0.5, 0.5]\n11        E012      Aaron      White  ...  Default benefits assignment due to prediction ...           [Dental, Vision, Hospital Indemnity]            [0.5, 0.5, 0.5]\n12        E013     Brooke     Harris  ...  Default benefits assignment due to prediction ...           [Dental, Vision, Hospital Indemnity]            [0.5, 0.5, 0.5]\n13        E014     Connor      Clark  ...  Default benefits assignment due to prediction ...           [Dental, Vision, Hospital Indemnity]            [0.5, 0.5, 0.5]\n14        E015    Destiny      Lewis  ...  Default benefits assignment due to prediction ...           [Dental, Vision, Hospital Indemnity]            [0.5, 0.5, 0.5]\n15        E016      Ethan   Robinson  ...  Default benefits assignment due to prediction ...           [Dental, Vision, Hospital Indemnity]            [0.5, 0.5, 0.5]\n16        E017      Fiona     Walker  ...  Default benefits assignment due to prediction ...           [Dental, Vision, Hospital Indemnity]            [0.5, 0.5, 0.5]\n17        E018    Gabriel       Hall  ...  Default benefits assignment due to prediction ...           [Dental, Vision, Hospital Indemnity]            [0.5, 0.5, 0.5]\n18        E019     Hannah      Allen  ...  Default benefits assignment due to prediction ...           [Dental, Vision, Hospital Indemnity]            [0.5, 0.5, 0.5]\n19        E020        Ian      Young  ...  Default benefits assignment due to prediction ...           [Dental, Vision, Hospital Indemnity]            [0.5, 0.5, 0.5]\n20        E021    Jessica       King  ...  Default benefits assignment due to prediction ...           [Dental, Vision, Hospital Indemnity]            [0.5, 0.5, 0.5]\n21        E022      Kevin      Scott  ...  Default benefits assignment due to prediction ...           [Dental, Vision, Hospital Indemnity]            [0.5, 0.5, 0.5]\n22        E023      Laura      Green  ...  Default benefits assignment due to prediction ...           [Dental, Vision, Hospital Indemnity]            [0.5, 0.5, 0.5]\n23        E024       Mark      Adams  ...  Default benefits assignment due to prediction ...           [Dental, Vision, Hospital Indemnity]            [0.5, 0.5, 0.5]\n24        E025       Nina      Baker  ...  Default benefits assignment due to prediction ...           [Dental, Vision, Hospital Indemnity]            [0.5, 0.5, 0.5]\n\n[25 rows x 152 columns]", "enrichment_summary": {"total_employees": 25, "features_analyzed": 19, "enrichment_details": {"age": {"original_missing": "0", "final_missing": "0", "enriched_count": "0", "completion_rate": 100.0}, "gender": {"original_missing": "0", "final_missing": "0", "enriched_count": "0", "completion_rate": 100.0}, "marital_status": {"original_missing": "0", "final_missing": "0", "enriched_count": "0", "completion_rate": 100.0}, "dept_count": {"original_missing": "0", "final_missing": "0", "enriched_count": "0", "completion_rate": 100.0}, "job_type": {"original_missing": 25, "final_missing": "0", "enriched_count": "25", "completion_rate": 100.0}, "employment_type": {"original_missing": "25", "final_missing": "0", "enriched_count": "25", "completion_rate": 100.0}, "department": {"original_missing": "25", "final_missing": "0", "enriched_count": "25", "completion_rate": 100.0}, "region": {"original_missing": 25, "final_missing": "0", "enriched_count": "25", "completion_rate": 100.0}, "health_condition": {"original_missing": 25, "final_missing": "0", "enriched_count": "25", "completion_rate": 100.0}, "chronic_condition": {"original_missing": 25, "final_missing": "0", "enriched_count": "25", "completion_rate": 100.0}, "prescription_use": {"original_missing": 25, "final_missing": "0", "enriched_count": "25", "completion_rate": 100.0}, "income_tier": {"original_missing": 25, "final_missing": "0", "enriched_count": "25", "completion_rate": 100.0}, "risk_tolerance": {"original_missing": 25, "final_missing": "0", "enriched_count": "25", "completion_rate": 100.0}, "lifestyle": {"original_missing": 25, "final_missing": "0", "enriched_count": "25", "completion_rate": 100.0}, "travel_frequency": {"original_missing": 25, "final_missing": "0", "enriched_count": "25", "completion_rate": 100.0}, "hsa_familiarity": {"original_missing": 25, "final_missing": "0", "enriched_count": "25", "completion_rate": 100.0}, "pregnancy_status": {"original_missing": "25", "final_missing": "0", "enriched_count": "25", "completion_rate": 100.0}, "tobacco_use": {"original_missing": "25", "final_missing": "0", "enriched_count": "25", "completion_rate": 100.0}, "mental_health_needs": {"original_missing": 25, "final_missing": "0", "enriched_count": "25", "completion_rate": 100.0}}, "data_quality_improvement": {"total_missing_before": "375", "total_missing_after": "0", "total_enriched": "375", "overall_completion_rate": 100.0}}, "comprehensive_statistics": {"basic_demographics": {"age_statistics": {"average_age": 41.08, "median_age": 41.0, "age_range": {"min": 32, "max": 50}, "age_distribution": {"18-30": 0, "31-45": 21, "46-60": 4, "60+": 0}}, "gender_composition": {"counts": {"Male": 13, "Female": 12}, "percentages": {"Male": 52.0, "Female": 48.0}}, "marital_status_distribution": {"counts": {"Not Married": 14, "Married": 11}, "percentages": {"Not Married": 56.0, "Married": 44.0}}}, "dependent_analysis": {"dependent_count_distribution": {"average_dependents": 1.48, "median_dependents": 1.0, "distribution": {"2": 7, "1": 7, "0": 6, "3": 4, "4": 1}, "employees_with_dependents": 19, "percentage_with_dependents": 76.0}}, "employment_demographics": {"department_distribution": {"counts": {"Sales": 7, "Manufacturing": 6, "Finance": 5, "Engineering": 4, "Information Technology": 3}, "percentages": {"Sales": 28.0, "Manufacturing": 24.0, "Finance": 20.0, "Engineering": 16.0, "Information Technology": 12.0}}, "employment_type_distribution": {"counts": {"Full-Time": 21, "Part-Time": 3, "Contract": 1}, "percentages": {"Full-Time": 84.0, "Part-Time": 12.0, "Contract": 4.0}}, "job_type_distribution": {"counts": {"Desk": 25}, "percentages": {"Desk": 100.0}}, "employee_class_distribution": {"counts": {}, "percentages": {}}, "salary_analysis": {"average_salary": 59500.0, "median_salary": 56000.0, "salary_range": {"min": "35000", "max": "95000"}, "salary_distribution": {"under_40k": 3, "40k_75k": 17, "75k_100k": 5, "over_100k": 0}}}, "health_and_lifestyle": {"health_condition_distribution": {"counts": {"Good": 16, "Excellent": 5, "Fair": 4}, "percentages": {"Good": 64.0, "Excellent": 20.0, "Fair": 16.0}}, "chronic_condition_distribution": {"counts": {"N": 20, "Y": 5}, "percentages": {"N": 80.0, "Y": 20.0}}, "tobacco_use_distribution": {"counts": {"N": 21, "Y": 4}, "percentages": {"N": 84.0, "Y": 16.0}}, "lifestyle_distribution": {"counts": {"Moderate": 17, "Active": 8}, "percentages": {"Moderate": 68.0, "Active": 32.0}}, "prescription_use_distribution": {"counts": {"None": 18, "Regular": 5, "Occasional": 2}, "percentages": {"None": 72.0, "Regular": 20.0, "Occasional": 8.0}}}, "coverage_analysis": {"coverage_tier_distribution": {"counts": {"Employee Only": 8, "Family": 7, "Employee + Child(ren)": 7, "Employee + Spouse": 3}, "percentages": {"Employee Only": 32.0, "Family": 28.0, "Employee + Child(ren)": 28.0, "Employee + Spouse": 12.0}}, "coverage_tier_by_family_size": {"Employee + Child(ren)": {"0": 0, "1": 3, "2": 4, "3": 0, "4": 0}, "Employee + Spouse": {"0": 0, "1": 2, "2": 1, "3": 0, "4": 0}, "Employee Only": {"0": 6, "1": 2, "2": 0, "3": 0, "4": 0}, "Family": {"0": 0, "1": 0, "2": 2, "3": 4, "4": 1}}, "medical_plan_distribution": {"counts": {"Y": 20, "N": 5}, "percentages": {"Y": 80.0, "N": 20.0}}, "dental_plan_distribution": {"counts": {"Y": 19, "N": 6}, "percentages": {"Y": 76.0, "N": 24.0}}, "vision_plan_distribution": {"counts": {"Y": 19, "N": 6}, "percentages": {"Y": 76.0, "N": 24.0}}, "life_plan_distribution": {"counts": {}, "percentages": {}}}, "geographic_distribution": {"state_distribution": {"counts": {"CA": 2, "FL": 2, "NY": 2, "WA": 2, "TX": 2, "WY": 1, "AR": 1, "OK": 1, "KS": 1, "NE": 1, "SD": 1, "ND": 1, "MT": 1, "ID": 1, "UT": 1, "NV": 1, "CO": 1, "AZ": 1, "OR": 1, "LA": 1}, "percentages": {"CA": 8.0, "FL": 8.0, "NY": 8.0, "WA": 8.0, "TX": 8.0, "WY": 4.0, "AR": 4.0, "OK": 4.0, "KS": 4.0, "NE": 4.0, "SD": 4.0, "ND": 4.0, "MT": 4.0, "ID": 4.0, "UT": 4.0, "NV": 4.0, "CO": 4.0, "AZ": 4.0, "OR": 4.0, "LA": 4.0}}, "region_distribution": {"counts": {"Urban": 14, "Rural": 8, "Suburban": 3}, "percentages": {"Urban": 56.0, "Rural": 32.0, "Suburban": 12.0}}, "top_cities": {"counts": {"Somewhere": 2, "Anytown": 1, "Uptown": 1, "Midtown": 1, "Oldtown": 1, "Newtown": 1, "Anyplace": 1, "Everytown": 1, "Notown": 1, "Alltown": 1}, "percentages": {"Somewhere": 8.0, "Anytown": 4.0, "Uptown": 4.0, "Midtown": 4.0, "Oldtown": 4.0, "Newtown": 4.0, "Anyplace": 4.0, "Everytown": 4.0, "Notown": 4.0, "Alltown": 4.0}}}, "financial_demographics": {"income_tier_distribution": {"counts": {"Medium ($50K–$100K)": 17, "High (>$100K)": 5, "Low (<$50K)": 3}, "percentages": {"Medium ($50K–$100K)": 68.0, "High (>$100K)": 20.0, "Low (<$50K)": 12.0}}, "risk_tolerance_distribution": {"counts": {"Low": 14, "Medium": 11}, "percentages": {"Low": 56.0, "Medium": 44.0}}, "hsa_familiarity_distribution": {"counts": {"Y": 13, "N": 12}, "percentages": {"Y": 52.0, "N": 48.0}}}, "risk_assessment": {"group_risk_score": 27.44, "group_risk_level": "Low", "risk_distribution": {"low_risk": 16, "medium_risk": 8, "high_risk": 1}, "risk_statistics": {"min_risk_score": 13, "max_risk_score": 68, "median_risk_score": 23.0, "std_risk_score": 13.27}, "top_risk_factors": {"Chronic Conditions": 5, "Poor Health Condition": 4, "Tobacco Use": 4, "Advanced Age": 1}, "individual_risks": [{"employee_id": "E001", "risk_score": 18, "risk_level": "Low", "risk_factors": []}, {"employee_id": "E002", "risk_score": 53, "risk_level": "Medium", "risk_factors": ["Age 50.0 (high risk)", "Has chronic condition"]}, {"employee_id": "E003", "risk_score": 28, "risk_level": "Low", "risk_factors": ["Health condition: Fair"]}, {"employee_id": "E004", "risk_score": 18, "risk_level": "Low", "risk_factors": []}, {"employee_id": "E005", "risk_score": 33, "risk_level": "Medium", "risk_factors": ["Health condition: Fair"]}, {"employee_id": "E006", "risk_score": 23, "risk_level": "Low", "risk_factors": []}, {"employee_id": "E007", "risk_score": 18, "risk_level": "Low", "risk_factors": []}, {"employee_id": "E008", "risk_score": 23, "risk_level": "Low", "risk_factors": []}, {"employee_id": "E009", "risk_score": 20, "risk_level": "Low", "risk_factors": []}, {"employee_id": "E010", "risk_score": 18, "risk_level": "Low", "risk_factors": []}]}, "data_quality_metrics": {"overall_completeness": {"total_cells": 3550, "missing_cells": "2725", "completeness_percentage": 23.24}, "column_completeness": {"employee_id": {"missing_count": "0", "completeness_percentage": 100.0}, "first_name": {"missing_count": "0", "completeness_percentage": 100.0}, "last_name": {"missing_count": "0", "completeness_percentage": 100.0}, "gender": {"missing_count": "0", "completeness_percentage": 100.0}, "dob": {"missing_count": "0", "completeness_percentage": 100.0}, "address1": {"missing_count": "0", "completeness_percentage": 100.0}, "city": {"missing_count": "0", "completeness_percentage": 100.0}, "state": {"missing_count": "0", "completeness_percentage": 100.0}, "zipcode": {"missing_count": "0", "completeness_percentage": 100.0}, "marital_status": {"missing_count": "0", "completeness_percentage": 100.0}, "salary": {"missing_count": "0", "completeness_percentage": 100.0}, "medical_plan": {"missing_count": "0", "completeness_percentage": 100.0}, "dental_plan": {"missing_count": "0", "completeness_percentage": 100.0}, "vision_plan": {"missing_count": "0", "completeness_percentage": 100.0}, "coverage_tier": {"missing_count": "0", "completeness_percentage": 100.0}, "dept_count": {"missing_count": "0", "completeness_percentage": 100.0}, "name": {"missing_count": "0", "completeness_percentage": 100.0}, "age": {"missing_count": "0", "completeness_percentage": 100.0}, "middle_name": {"missing_count": "25", "completeness_percentage": 0.0}, "address2": {"missing_count": "25", "completeness_percentage": 0.0}, "relationship": {"missing_count": "25", "completeness_percentage": 0.0}, "record_type": {"missing_count": "25", "completeness_percentage": 0.0}, "employment_type": {"missing_count": "0", "completeness_percentage": 100.0}, "employee_class": {"missing_count": "25", "completeness_percentage": 0.0}, "department": {"missing_count": "0", "completeness_percentage": 100.0}, "hire_date": {"missing_count": "25", "completeness_percentage": 0.0}, "tobacco_use": {"missing_count": "0", "completeness_percentage": 100.0}, "pregnancy_status": {"missing_count": "0", "completeness_percentage": 100.0}, "life_plan": {"missing_count": "25", "completeness_percentage": 0.0}, "add_plan": {"missing_count": "25", "completeness_percentage": 0.0}, "ssn": {"missing_count": "25", "completeness_percentage": 0.0}, "dept_1": {"missing_count": "25", "completeness_percentage": 0.0}, "dept_1_dob": {"missing_count": "25", "completeness_percentage": 0.0}, "dept_1_age": {"missing_count": "25", "completeness_percentage": 0.0}, "dept_1_gender": {"missing_count": "25", "completeness_percentage": 0.0}, "relationship_type_1": {"missing_count": "25", "completeness_percentage": 0.0}, "dept_2": {"missing_count": "25", "completeness_percentage": 0.0}, "dept_2_dob": {"missing_count": "25", "completeness_percentage": 0.0}, "dept_2_age": {"missing_count": "25", "completeness_percentage": 0.0}, "dept_2_gender": {"missing_count": "25", "completeness_percentage": 0.0}, "relationship_type_2": {"missing_count": "25", "completeness_percentage": 0.0}, "dept_3": {"missing_count": "25", "completeness_percentage": 0.0}, "dept_3_dob": {"missing_count": "25", "completeness_percentage": 0.0}, "dept_3_age": {"missing_count": "25", "completeness_percentage": 0.0}, "dept_3_gender": {"missing_count": "25", "completeness_percentage": 0.0}, "relationship_type_3": {"missing_count": "25", "completeness_percentage": 0.0}, "dept_4": {"missing_count": "25", "completeness_percentage": 0.0}, "dept_4_dob": {"missing_count": "25", "completeness_percentage": 0.0}, "dept_4_age": {"missing_count": "25", "completeness_percentage": 0.0}, "dept_4_gender": {"missing_count": "25", "completeness_percentage": 0.0}, "relationship_type_4": {"missing_count": "25", "completeness_percentage": 0.0}, "dept_5": {"missing_count": "25", "completeness_percentage": 0.0}, "dept_5_dob": {"missing_count": "25", "completeness_percentage": 0.0}, "dept_5_age": {"missing_count": "25", "completeness_percentage": 0.0}, "dept_5_gender": {"missing_count": "25", "completeness_percentage": 0.0}, "relationship_type_5": {"missing_count": "25", "completeness_percentage": 0.0}, "dept_6": {"missing_count": "25", "completeness_percentage": 0.0}, "dept_6_dob": {"missing_count": "25", "completeness_percentage": 0.0}, "dept_6_age": {"missing_count": "25", "completeness_percentage": 0.0}, "dept_6_gender": {"missing_count": "25", "completeness_percentage": 0.0}, "relationship_type_6": {"missing_count": "25", "completeness_percentage": 0.0}, "dept_7": {"missing_count": "25", "completeness_percentage": 0.0}, "dept_7_dob": {"missing_count": "25", "completeness_percentage": 0.0}, "dept_7_age": {"missing_count": "25", "completeness_percentage": 0.0}, "dept_7_gender": {"missing_count": "25", "completeness_percentage": 0.0}, "relationship_type_7": {"missing_count": "25", "completeness_percentage": 0.0}, "dept_8": {"missing_count": "25", "completeness_percentage": 0.0}, "dept_8_dob": {"missing_count": "25", "completeness_percentage": 0.0}, "dept_8_age": {"missing_count": "25", "completeness_percentage": 0.0}, "dept_8_gender": {"missing_count": "25", "completeness_percentage": 0.0}, "relationship_type_8": {"missing_count": "25", "completeness_percentage": 0.0}, "dept_9": {"missing_count": "25", "completeness_percentage": 0.0}, "dept_9_dob": {"missing_count": "25", "completeness_percentage": 0.0}, "dept_9_age": {"missing_count": "25", "completeness_percentage": 0.0}, "dept_9_gender": {"missing_count": "25", "completeness_percentage": 0.0}, "relationship_type_9": {"missing_count": "25", "completeness_percentage": 0.0}, "dept_10": {"missing_count": "25", "completeness_percentage": 0.0}, "dept_10_dob": {"missing_count": "25", "completeness_percentage": 0.0}, "dept_10_age": {"missing_count": "25", "completeness_percentage": 0.0}, "dept_10_gender": {"missing_count": "25", "completeness_percentage": 0.0}, "relationship_type_10": {"missing_count": "25", "completeness_percentage": 0.0}, "dept_11": {"missing_count": "25", "completeness_percentage": 0.0}, "dept_11_dob": {"missing_count": "25", "completeness_percentage": 0.0}, "dept_11_age": {"missing_count": "25", "completeness_percentage": 0.0}, "dept_11_gender": {"missing_count": "25", "completeness_percentage": 0.0}, "relationship_type_11": {"missing_count": "25", "completeness_percentage": 0.0}, "dept_12": {"missing_count": "25", "completeness_percentage": 0.0}, "dept_12_dob": {"missing_count": "25", "completeness_percentage": 0.0}, "dept_12_age": {"missing_count": "25", "completeness_percentage": 0.0}, "dept_12_gender": {"missing_count": "25", "completeness_percentage": 0.0}, "relationship_type_12": {"missing_count": "25", "completeness_percentage": 0.0}, "dept_13": {"missing_count": "25", "completeness_percentage": 0.0}, "dept_13_dob": {"missing_count": "25", "completeness_percentage": 0.0}, "dept_13_age": {"missing_count": "25", "completeness_percentage": 0.0}, "dept_13_gender": {"missing_count": "25", "completeness_percentage": 0.0}, "relationship_type_13": {"missing_count": "25", "completeness_percentage": 0.0}, "dept_14": {"missing_count": "25", "completeness_percentage": 0.0}, "dept_14_dob": {"missing_count": "25", "completeness_percentage": 0.0}, "dept_14_age": {"missing_count": "25", "completeness_percentage": 0.0}, "dept_14_gender": {"missing_count": "25", "completeness_percentage": 0.0}, "relationship_type_14": {"missing_count": "25", "completeness_percentage": 0.0}, "dept_15": {"missing_count": "25", "completeness_percentage": 0.0}, "dept_15_dob": {"missing_count": "25", "completeness_percentage": 0.0}, "dept_15_age": {"missing_count": "25", "completeness_percentage": 0.0}, "dept_15_gender": {"missing_count": "25", "completeness_percentage": 0.0}, "relationship_type_15": {"missing_count": "25", "completeness_percentage": 0.0}, "dept_16": {"missing_count": "25", "completeness_percentage": 0.0}, "dept_16_dob": {"missing_count": "25", "completeness_percentage": 0.0}, "dept_16_age": {"missing_count": "25", "completeness_percentage": 0.0}, "dept_16_gender": {"missing_count": "25", "completeness_percentage": 0.0}, "relationship_type_16": {"missing_count": "25", "completeness_percentage": 0.0}, "dept_17": {"missing_count": "25", "completeness_percentage": 0.0}, "dept_17_dob": {"missing_count": "25", "completeness_percentage": 0.0}, "dept_17_age": {"missing_count": "25", "completeness_percentage": 0.0}, "dept_17_gender": {"missing_count": "25", "completeness_percentage": 0.0}, "relationship_type_17": {"missing_count": "25", "completeness_percentage": 0.0}, "dept_18": {"missing_count": "25", "completeness_percentage": 0.0}, "dept_18_dob": {"missing_count": "25", "completeness_percentage": 0.0}, "dept_18_age": {"missing_count": "25", "completeness_percentage": 0.0}, "dept_18_gender": {"missing_count": "25", "completeness_percentage": 0.0}, "relationship_type_18": {"missing_count": "25", "completeness_percentage": 0.0}, "dept_19": {"missing_count": "25", "completeness_percentage": 0.0}, "dept_19_dob": {"missing_count": "25", "completeness_percentage": 0.0}, "dept_19_age": {"missing_count": "25", "completeness_percentage": 0.0}, "dept_19_gender": {"missing_count": "25", "completeness_percentage": 0.0}, "relationship_type_19": {"missing_count": "25", "completeness_percentage": 0.0}, "dept_20": {"missing_count": "25", "completeness_percentage": 0.0}, "dept_20_dob": {"missing_count": "25", "completeness_percentage": 0.0}, "dept_20_age": {"missing_count": "25", "completeness_percentage": 0.0}, "dept_20_gender": {"missing_count": "25", "completeness_percentage": 0.0}, "relationship_type_20": {"missing_count": "25", "completeness_percentage": 0.0}, "job_type": {"missing_count": "0", "completeness_percentage": 100.0}, "region": {"missing_count": "0", "completeness_percentage": 100.0}, "health_condition": {"missing_count": "0", "completeness_percentage": 100.0}, "chronic_condition": {"missing_count": "0", "completeness_percentage": 100.0}, "prescription_use": {"missing_count": "0", "completeness_percentage": 100.0}, "income_tier": {"missing_count": "0", "completeness_percentage": 100.0}, "risk_tolerance": {"missing_count": "0", "completeness_percentage": 100.0}, "lifestyle": {"missing_count": "0", "completeness_percentage": 100.0}, "travel_frequency": {"missing_count": "0", "completeness_percentage": 100.0}, "hsa_familiarity": {"missing_count": "0", "completeness_percentage": 100.0}, "mental_health_needs": {"missing_count": "0", "completeness_percentage": 100.0}}, "critical_field_completeness": {"name": {"missing_count": "0", "completeness_percentage": 100.0}, "gender": {"missing_count": "0", "completeness_percentage": 100.0}, "zipcode": {"missing_count": "0", "completeness_percentage": 100.0}, "marital_status": {"missing_count": "0", "completeness_percentage": 100.0}}}}, "feature_validation": {"is_valid": true, "missing_features": [], "feature_completeness": {"age": {"present": true, "missing_values": "0", "completion_rate": 100.0}, "gender": {"present": true, "missing_values": "0", "completion_rate": 100.0}, "marital_status": {"present": true, "missing_values": "0", "completion_rate": 100.0}, "dept_count": {"present": true, "missing_values": "0", "completion_rate": 100.0}, "job_type": {"present": true, "missing_values": "0", "completion_rate": 100.0}, "employment_type": {"present": true, "missing_values": "0", "completion_rate": 100.0}, "department": {"present": true, "missing_values": "0", "completion_rate": 100.0}, "region": {"present": true, "missing_values": "0", "completion_rate": 100.0}, "health_condition": {"present": true, "missing_values": "0", "completion_rate": 100.0}, "chronic_condition": {"present": true, "missing_values": "0", "completion_rate": 100.0}, "prescription_use": {"present": true, "missing_values": "0", "completion_rate": 100.0}, "income_tier": {"present": true, "missing_values": "0", "completion_rate": 100.0}, "risk_tolerance": {"present": true, "missing_values": "0", "completion_rate": 100.0}, "lifestyle": {"present": true, "missing_values": "0", "completion_rate": 100.0}, "travel_frequency": {"present": true, "missing_values": "0", "completion_rate": 100.0}, "hsa_familiarity": {"present": true, "missing_values": "0", "completion_rate": 100.0}, "pregnancy_status": {"present": true, "missing_values": "0", "completion_rate": 100.0}, "tobacco_use": {"present": true, "missing_values": "0", "completion_rate": 100.0}, "mental_health_needs": {"present": true, "missing_values": "0", "completion_rate": 100.0}}, "overall_completion_rate": 100.0, "ready_for_prediction": "True"}, "prediction_method": "ml_models", "model_predictions": {"plan_types": {"total_predictions": 25, "average_confidence": "0.7865912", "unique_plans": 5}}, "prediction_summary": {"total_employees": 25, "plan_type_distribution": {"successful_predictions": {"HDHP + HSA": 11, "POS": 9, "HMO": 2, "PPO": 2, "HDHP": 1}, "failed_predictions": "0", "success_rate": 100.0, "total_predicted": "25"}, "benefits_distribution": {}, "confidence_metrics": {"plan_confidence": {"mean": "0.787", "min": "0.463", "max": "0.960", "count": 25}}, "prediction_success_rates": {}, "top_3_plan_probabilities": ["E001: [0.8389241, 0.12671588, 0.*********]", "E002: [0.4627546, 0.44171563, 0.06324112]", "E003: [0.********, 0.********, 0.*********]", "E004: [0.********, 0.*********, 0.*********]", "E005: [0.********, 0.********, 0.*********]", "E006: [0.********, 0.*********, 0.*********]", "E007: [0.8320051, 0.06180143, 0.05192994]", "E008: [0.7829865, 0.18524686, 0.013601141]", "E009: [0.864179, 0.06393268, 0.052214127]", "E010: [0.6398547, 0.115738586, 0.109267496]", "E011: [0.8354502, 0.062074736, 0.040318042]", "E012: [0.7843826, 0.1928217, 0.**********]", "E013: [0.6925033, 0.2408892, 0.04169866]", "E014: [0.87886167, 0.07075396, 0.*********]", "E015: [0.79829973, 0.07172966, 0.*********]", "E016: [0.8945745, 0.*********, 0.*********]", "E017: [0.8653487, 0.*********, 0.*********]", "E018: [0.91217333, 0.05060343, 0.02713377]", "E019: [0.58611363, 0.25714287, 0.100834146]"]}}}}}