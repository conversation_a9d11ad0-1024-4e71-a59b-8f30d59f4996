{"file_info": {"original_filename": "col5_comprehensive_all.csv", "processing_timestamp": "2025-07-15 17:45:44.865950", "total_processing_time_seconds": 27.545291662216187}, "final_response": {"success": true, "data": {"enriched_data_csv": "employee_id,first_name,last_name,gender,dob,address1,address2,city,state,zipcode,marital_status,salary,tobacco_use,pregnancy_status,employment_type,employee_class,hire_date,medical_plan,dental_plan,vision_plan,life_plan,add_plan,coverage_tier,ssn,dept_1,dept_1_dob,dept_1_age,dept_1_gender,relationship_type_1,dept_1_medical_plan,dept_1_dental_plan,dept_1_vision_plan,dept_2,dept_2_dob,dept_2_age,dept_2_gender,relationship_type_2,dept_2_medical_plan,dept_2_dental_plan,dept_2_vision_plan,dept_3,dept_3_dob,dept_3_age,dept_3_gender,relationship_type_3,dept_3_medical_plan,dept_3_dental_plan,dept_3_vision_plan,dept_count,name,age,middle_name,relationship,record_type,department,dept_4,dept_4_dob,dept_4_age,dept_4_gender,relationship_type_4,dept_5,dept_5_dob,dept_5_age,dept_5_gender,relationship_type_5,dept_6,dept_6_dob,dept_6_age,dept_6_gender,relationship_type_6,dept_7,dept_7_dob,dept_7_age,dept_7_gender,relationship_type_7,dept_8,dept_8_dob,dept_8_age,dept_8_gender,relationship_type_8,dept_9,dept_9_dob,dept_9_age,dept_9_gender,relationship_type_9,dept_10,dept_10_dob,dept_10_age,dept_10_gender,relationship_type_10,dept_11,dept_11_dob,dept_11_age,dept_11_gender,relationship_type_11,dept_12,dept_12_dob,dept_12_age,dept_12_gender,relationship_type_12,dept_13,dept_13_dob,dept_13_age,dept_13_gender,relationship_type_13,dept_14,dept_14_dob,dept_14_age,dept_14_gender,relationship_type_14,dept_15,dept_15_dob,dept_15_age,dept_15_gender,relationship_type_15,dept_16,dept_16_dob,dept_16_age,dept_16_gender,relationship_type_16,dept_17,dept_17_dob,dept_17_age,dept_17_gender,relationship_type_17,dept_18,dept_18_dob,dept_18_age,dept_18_gender,relationship_type_18,dept_19,dept_19_dob,dept_19_age,dept_19_gender,relationship_type_19,dept_20,dept_20_dob,dept_20_age,dept_20_gender,relationship_type_20,job_type,region,health_condition,chronic_condition,prescription_use,income_tier,risk_tolerance,lifestyle,travel_frequency,hsa_familiarity,mental_health_needs,predicted_plan_type,plan_confidence,plan_reason,top_3_plans,top_3_plan_confidences,predicted_benefits,benefits_confidence,benefits_reason,top_3_benefits,top_3_benefits_confidences\r\nE001,John,Smith,Male,1980-01-15,123 Main St,Apt 1A,Anytown,CA,90210,Married,65000,Y,N,Full-Time,Salaried,2020-01-15,Y,Y,Y,Y,Y,Family,123-45-6789,Jane Smith,1982-05-20,43.0,Female,Spouse,Y,Y,Y,Mike Smith,2010-09-12,14.0,Male,Child,Y,Y,Y,,,,,,,,,2,John Smith,45.0,,,,Engineering,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Desk,Urban,Good,N,None,Medium ($50K–$100K),Low,Moderate,Occasional,Y,Y,HDHP + HSA,0.8101897,\"Medium income, good health - PPO for balanced cost and flexibility.\",\"['HDHP + HSA', 'POS', 'HMO']\",\"[0.8101897, 0.16297188, 0.*********]\",\"['Dental', 'Vision', 'Accident', 'STD', 'Employee Assistance']\",1.0,Dental: Essential oral health coverage for all employees.; Vision: Eye care coverage for all employees.; STD: Income protection for Medium ($50K–$100K) earners or good health status.,\"['Dental', 'Vision', 'Accident']\",\"[0.5, 0.5, 0.5]\"\r\nE002,Alice,Brown,Female,1975-03-10,456 Oak Ave,,Somewhere,TX,75001,Not Married,52000,N,N,Part-Time,Hourly,2019-05-20,Y,N,N,N,N,Employee + Child(ren),987-65-4321,Bob Brown,2005-07-25,19.0,Male,Child,Y,N,Y,,,,,,,,,,,,,,,,,1,Alice Brown,50.0,,,,Information Technology,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Desk,Urban,Good,N,None,Medium ($50K–$100K),Low,Moderate,Occasional,N,N,POS,0.90889263,\"Medium income, good health - PPO for balanced cost and flexibility.\",\"['POS', 'PPO', 'HMO']\",\"[0.90889263, 0.049560472, 0.026334891]\",\"['Dental', 'Vision']\",1.0,Dental: Essential oral health coverage for all employees.; Vision: Eye care coverage for all employees.,\"['Dental', 'Vision']\",\"[0.5, 0.5]\"\r\nE003,Charlie,Davis,Male,1990-06-30,789 Pine Rd,Unit 5B,Elsewhere,FL,33101,Married,78500,Y,N,Full-Time,Salaried,2021-03-10,Y,Y,Y,Y,Y,Family,555-12-3456,Diana Davis,1992-08-15,32.0,Female,Spouse,Y,Y,Y,Evan Davis,2015-12-01,9.0,Male,Child,Y,Y,Y,Fiona Davis,2018-03-22,7.0,Female,Child,Y,Y,Y,3,Charlie Davis,35.0,,,,Sales,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Desk,Urban,Fair,N,None,High (>$100K),High,Active,Occasional,N,Y,PPO,0.7811365,Middle age with dependents - PPO for family coverage.,\"['PPO', 'Indemnity', 'POS']\",\"[0.7811365, 0.********, 0.*********]\",\"['FSA', 'Employee Assistance']\",1.0,FSA: Tax-advantaged account for medical expenses due to health conditions or pregnancy.,\"['FSA', 'Employee Assistance']\",\"[0.5, 0.5]\"\r\nE004,Grace,Wilson,Female,1985-11-05,321 Elm St,,Nowhere,NY,10001,Not Married,45000,Y,Y,Full-Time,Hourly,2018-08-12,Y,Y,N,N,N,Employee Only,444-55-6677,Henry Wilson,2012-04-18,13.0,Male,Child,Y,Y,N,,,,,,,,,,,,,,,,,1,Grace Wilson,39.0,,,,Manufacturing,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Desk,Urban,Good,N,None,Medium ($50K–$100K),Low,Moderate,Rare,Y,N,HMO,0.7520252,\"Medium income, good health - PPO for balanced cost and flexibility.\",\"['HMO', 'POS', 'HDHP + HSA']\",\"[0.7520252, 0.0847589, 0.********]\",\"['Dental', 'Vision', 'Hospital Indemnity', 'Accident', 'STD']\",1.0,Dental: Essential oral health coverage for all employees.; Vision: Eye care coverage for all employees.; STD: Income protection for Medium ($50K–$100K) earners or good health status.,\"['Dental', 'Vision', 'Hospital Indemnity']\",\"[0.5, 0.5, 0.5]\"\r\nE005,Isaac,Johnson,Male,1978-02-28,654 Maple Dr,Suite 200,Anywhere,WA,98001,Married,95000,N,N,Full-Time,Executive,2017-11-30,Y,Y,Y,Y,Y,Family,333-44-5566,Lisa Johnson,1980-11-12,44.0,Female,Spouse,Y,Y,Y,Tom Johnson,2008-06-15,17.0,Male,Child,Y,Y,Y,Sara Johnson,2011-09-03,13.0,Female,Child,Y,Y,Y,3,Isaac Johnson,47.0,,,,Information Technology,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Desk,Urban,Good,Y,Occasional,High (>$100K),Medium,Moderate,Rare,Y,N,PPO,0.6883096,\"Chronic conditions, older age - POS for coordinated care management.\",\"['PPO', 'HDHP + HSA', 'POS']\",\"[0.6883096, 0.15991065, 0.083892204]\",\"['Critical Illness', 'Wellness Programs']\",1.0,Critical Illness: Important protection for age 47 with good health status.,\"['Critical Illness', 'Wellness Programs']\",\"[0.5, 0.5]\"\r\nE006,Michael,Anderson,Male,1983-07-12,987 Cedar Ln,,Someplace,OR,97001,Not Married,38000,Y,N,Part-Time,Hourly,2022-02-14,N,N,N,N,N,Employee Only,222-33-4455,,,,,,,,,,,,,,,,,,,,,,,,,0,Michael Anderson,42.0,,,,Information Technology,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Desk,Urban,Fair,N,None,Low (<$50K),Medium,Moderate,Rare,Y,N,HMO,0.62563807,\"Low income, part-time employment - MEC for minimum coverage compliance.\",\"['HMO', 'HDHP + HSA', 'PPO']\",\"[0.62563807, 0.12084466, 0.11903042]\",[],0.0,ML model prediction,\"['Dental', 'Vision', 'Hospital Indemnity']\",\"[0.5, 0.5, 0.5]\"\r\nE007,Nancy,Taylor,Female,1987-12-03,147 Birch Ave,Apt 3C,Othertown,AZ,85001,Married,72000,N,Y,Full-Time,Management,2019-09-05,Y,Y,Y,Y,Y,Employee + Spouse,111-22-3344,Paul Taylor,1985-04-20,40.0,Male,Spouse,Y,Y,Y,,,,,,,,,,,,,,,,,1,Nancy Taylor,37.0,,,,Information Technology,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Desk,Urban,Good,N,Occasional,Medium ($50K–$100K),Medium,Moderate,Rare,Y,N,HMO,0.8533228,\"Medium income, good health - PPO for balanced cost and flexibility.\",\"['HMO', 'HDHP', 'POS']\",\"[0.8533228, 0.07770858, 0.056844845]\",\"['Dental', 'Vision', 'Hospital Indemnity', 'STD']\",1.0,Dental: Essential oral health coverage for all employees.; Vision: Eye care coverage for all employees.; STD: Income protection for Medium ($50K–$100K) earners or good health status.,\"['Dental', 'Vision', 'Hospital Indemnity']\",\"[0.5, 0.5, 0.5]\"\r\nE008,Quinn,Martinez,Male,1979-10-17,258 Spruce St,,Newplace,CO,80001,Not Married,58000,Y,N,Full-Time,Union,2020-06-18,Y,Y,Y,Y,N,Employee + Spouse,666-77-8899,Alex Martinez,2007-12-25,17.0,Male,Child,Y,Y,Y,Zoe Martinez,2010-03-14,15.0,Female,Child,Y,Y,Y,,,,,,,,,2,Quinn Martinez,45.0,,,,Manufacturing,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Desk,Urban,Good,N,None,Medium ($50K–$100K),Medium,Moderate,Rare,Y,Y,HDHP + HSA,0.8666599,\"Medium income, good health - PPO for balanced cost and flexibility.\",\"['HDHP + HSA', 'POS', 'HMO']\",\"[0.8666599, 0.10609808, 0.**********]\",\"['Dental', 'Vision', 'Accident', 'STD', 'Employee Assistance']\",1.0,Dental: Essential oral health coverage for all employees.; Vision: Eye care coverage for all employees.; STD: Income protection for Medium ($50K–$100K) earners or good health status.,\"['Dental', 'Vision', 'Accident']\",\"[0.5, 0.5, 0.5]\"\r\nE009,Tina,Garcia,Female,1981-03-24,369 Willow Way,Floor 2,Lastplace,NV,89001,Married,67000,N,N,Full-Time,Salaried,2018-12-01,Y,Y,Y,Y,Y,Family,777-88-9900,Carlos Garcia,1979-07-08,46.0,Male,Spouse,Y,Y,Y,Maya Garcia,2009-11-30,15.0,Female,Child,Y,Y,Y,,,,,,,,,2,Tina Garcia,44.0,,,,Information Technology,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Desk,Rural,Fair,Y,Regular,Medium ($50K–$100K),Low,Moderate,Rare,N,N,PPO,0.8966832,Middle age with dependents - PPO for family coverage.,\"['PPO', 'POS', 'HMO']\",\"[0.8966832, 0.059428215, 0.027713045]\",\"['Dental', 'Vision', 'Critical Illness']\",1.0,Dental: Essential oral health coverage for all employees.; Vision: Eye care coverage for all employees.; Critical Illness: Important protection for age 44 with fair health status.,\"['Dental', 'Vision', 'Critical Illness']\",\"[0.5, 0.5, 0.5]\"\r\nE010,Xavier,Rodriguez,Male,1986-09-09,741 Poplar Pl,,Finaltown,UT,84001,Not Married,41000,Y,N,Temporary,Non-Union,2021-07-22,N,N,N,N,N,Employee Only,888-99-0011,,,,,,,,,,,,,,,,,,,,,,,,,0,Xavier Rodriguez,38.0,,,,Manufacturing,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Desk,Rural,Excellent,N,None,Medium ($50K–$100K),Low,Active,Rare,Y,N,HMO,0.4478133,Middle age with dependents - PPO for family coverage.,\"['HMO', 'HDHP + HSA', 'HDHP']\",\"[0.4478133, 0.37112904, 0.16094308]\",\"['Accident', 'STD']\",1.0,Accident: Recommended for active lifestyle and rare travel frequency.; STD: Income protection for Medium ($50K–$100K) earners or excellent health status.,\"['Accident', 'STD']\",\"[0.5, 0.5]\"\r\n", "comprehensive_statistics": {"basic_demographics": {"age_statistics": {"average_age": 42.2, "median_age": 43.0, "age_range": {"min": 35, "max": 50}, "age_distribution": {"18-30": 0, "31-45": 8, "46-60": 2, "60+": 0}}, "gender_composition": {"counts": {"Male": 6, "Female": 4}, "percentages": {"Male": 60.0, "Female": 40.0}}, "marital_status_distribution": {"counts": {"Married": 5, "Not Married": 5}, "percentages": {"Married": 50.0, "Not Married": 50.0}}}, "dependent_analysis": {"dependent_count_distribution": {"average_dependents": 1.5, "median_dependents": 1.5, "distribution": {"2": 3, "1": 3, "3": 2, "0": 2}, "employees_with_dependents": 8, "percentage_with_dependents": 80.0}, "dependent_age_analysis": {"total_dependents_found": 15, "average_dependent_age": 22.93, "median_dependent_age": 17.0, "dependents_over_26_as_children": 0, "age_distribution": {"0-5": 0, "6-12": 2, "13-18": 7, "19-26": 1, "26+": 5}}}, "employment_demographics": {"department_distribution": {"counts": {"Information Technology": 5, "Manufacturing": 3, "Engineering": 1, "Sales": 1}, "percentages": {"Information Technology": 50.0, "Manufacturing": 30.0, "Engineering": 10.0, "Sales": 10.0}}, "employment_type_distribution": {"counts": {"Full-Time": 7, "Part-Time": 2, "Temporary": 1}, "percentages": {"Full-Time": 70.0, "Part-Time": 20.0, "Temporary": 10.0}}, "job_type_distribution": {"counts": {"Desk": 10}, "percentages": {"Desk": 100.0}}, "employee_class_distribution": {"counts": {"Salaried": 3, "Hourly": 3, "Executive": 1, "Management": 1, "Union": 1, "Non-Union": 1}, "percentages": {"Salaried": 30.0, "Hourly": 30.0, "Executive": 10.0, "Management": 10.0, "Union": 10.0, "Non-Union": 10.0}}, "salary_analysis": {"average_salary": 61150.0, "median_salary": 61500.0, "salary_range": {"min": "38000", "max": "95000"}, "salary_distribution": {"under_40k": 1, "40k_75k": 7, "75k_100k": 2, "over_100k": 0}}}, "health_and_lifestyle": {"health_condition_distribution": {"counts": {"Good": 6, "Fair": 3, "Excellent": 1}, "percentages": {"Good": 60.0, "Fair": 30.0, "Excellent": 10.0}}, "chronic_condition_distribution": {"counts": {"N": 8, "Y": 2}, "percentages": {"N": 80.0, "Y": 20.0}}, "tobacco_use_distribution": {"counts": {"Y": 6, "N": 4}, "percentages": {"Y": 60.0, "N": 40.0}}, "lifestyle_distribution": {"counts": {"Moderate": 8, "Active": 2}, "percentages": {"Moderate": 80.0, "Active": 20.0}}, "prescription_use_distribution": {"counts": {"None": 7, "Occasional": 2, "Regular": 1}, "percentages": {"None": 70.0, "Occasional": 20.0, "Regular": 10.0}}}, "coverage_analysis": {"coverage_tier_distribution": {"counts": {"Family": 4, "Employee Only": 3, "Employee + Spouse": 2, "Employee + Child(ren)": 1}, "percentages": {"Family": 40.0, "Employee Only": 30.0, "Employee + Spouse": 20.0, "Employee + Child(ren)": 10.0}}, "coverage_tier_by_family_size": {"Employee + Child(ren)": {"0": 0, "1": 1, "2": 0, "3": 0}, "Employee + Spouse": {"0": 0, "1": 1, "2": 1, "3": 0}, "Employee Only": {"0": 2, "1": 1, "2": 0, "3": 0}, "Family": {"0": 0, "1": 0, "2": 2, "3": 2}}, "medical_plan_distribution": {"counts": {"Y": 8, "N": 2}, "percentages": {"Y": 80.0, "N": 20.0}}, "dental_plan_distribution": {"counts": {"Y": 7, "N": 3}, "percentages": {"Y": 70.0, "N": 30.0}}, "vision_plan_distribution": {"counts": {"Y": 6, "N": 4}, "percentages": {"Y": 60.0, "N": 40.0}}, "life_plan_distribution": {"counts": {"Y": 6, "N": 4}, "percentages": {"Y": 60.0, "N": 40.0}}}, "geographic_distribution": {"state_distribution": {"counts": {"CA": 1, "TX": 1, "FL": 1, "NY": 1, "WA": 1, "OR": 1, "AZ": 1, "CO": 1, "NV": 1, "UT": 1}, "percentages": {"CA": 10.0, "TX": 10.0, "FL": 10.0, "NY": 10.0, "WA": 10.0, "OR": 10.0, "AZ": 10.0, "CO": 10.0, "NV": 10.0, "UT": 10.0}}, "region_distribution": {"counts": {"Urban": 8, "Rural": 2}, "percentages": {"Urban": 80.0, "Rural": 20.0}}, "top_cities": {"counts": {"Anytown": 1, "Somewhere": 1, "Elsewhere": 1, "Nowhere": 1, "Anywhere": 1, "Someplace": 1, "Othertown": 1, "Newplace": 1, "Lastplace": 1, "Finaltown": 1}, "percentages": {"Anytown": 10.0, "Somewhere": 10.0, "Elsewhere": 10.0, "Nowhere": 10.0, "Anywhere": 10.0, "Someplace": 10.0, "Othertown": 10.0, "Newplace": 10.0, "Lastplace": 10.0, "Finaltown": 10.0}}}, "financial_demographics": {"income_tier_distribution": {"counts": {"Medium ($50K–$100K)": 7, "High (>$100K)": 2, "Low (<$50K)": 1}, "percentages": {"Medium ($50K–$100K)": 70.0, "High (>$100K)": 20.0, "Low (<$50K)": 10.0}}, "risk_tolerance_distribution": {"counts": {"Low": 5, "Medium": 4, "High": 1}, "percentages": {"Low": 50.0, "Medium": 40.0, "High": 10.0}}, "hsa_familiarity_distribution": {"counts": {"Y": 7, "N": 3}, "percentages": {"Y": 70.0, "N": 30.0}}}, "risk_assessment": {"group_risk_score": 36.9, "group_risk_level": "Medium", "risk_distribution": {"low_risk": 2, "medium_risk": 8, "high_risk": 0}, "risk_statistics": {"min_risk_score": 18, "max_risk_score": 53, "median_risk_score": 38.0, "std_risk_score": 9.8}, "top_risk_factors": {"Tobacco Use": 6, "Poor Health Condition": 3, "Chronic Conditions": 2, "Advanced Age": 1}, "individual_risks": [{"employee_id": "E001", "risk_score": 38, "risk_level": "Medium", "risk_factors": ["Tobacco user"]}, {"employee_id": "E002", "risk_score": 33, "risk_level": "Medium", "risk_factors": ["Age 50.0 (high risk)"]}, {"employee_id": "E003", "risk_score": 40, "risk_level": "Medium", "risk_factors": ["Health condition: Fair", "Tobacco user"]}, {"employee_id": "E004", "risk_score": 33, "risk_level": "Medium", "risk_factors": ["Tobacco user"]}, {"employee_id": "E005", "risk_score": 43, "risk_level": "Medium", "risk_factors": ["Has chronic condition"]}, {"employee_id": "E006", "risk_score": 48, "risk_level": "Medium", "risk_factors": ["Health condition: Fair", "Tobacco user"]}, {"employee_id": "E007", "risk_score": 18, "risk_level": "Low", "risk_factors": []}, {"employee_id": "E008", "risk_score": 38, "risk_level": "Medium", "risk_factors": ["Tobacco user"]}, {"employee_id": "E009", "risk_score": 53, "risk_level": "Medium", "risk_factors": ["Health condition: Fair", "Has chronic condition"]}, {"employee_id": "E010", "risk_score": 25, "risk_level": "Low", "risk_factors": ["Tobacco user"]}]}, "data_quality_metrics": {"overall_completeness": {"total_cells": 1510, "missing_cells": "1005", "completeness_percentage": 33.44}, "column_completeness": {"employee_id": {"missing_count": "0", "completeness_percentage": 100.0}, "first_name": {"missing_count": "0", "completeness_percentage": 100.0}, "last_name": {"missing_count": "0", "completeness_percentage": 100.0}, "gender": {"missing_count": "0", "completeness_percentage": 100.0}, "dob": {"missing_count": "0", "completeness_percentage": 100.0}, "address1": {"missing_count": "0", "completeness_percentage": 100.0}, "address2": {"missing_count": "5", "completeness_percentage": 50.0}, "city": {"missing_count": "0", "completeness_percentage": 100.0}, "state": {"missing_count": "0", "completeness_percentage": 100.0}, "zipcode": {"missing_count": "0", "completeness_percentage": 100.0}, "marital_status": {"missing_count": "0", "completeness_percentage": 100.0}, "salary": {"missing_count": "0", "completeness_percentage": 100.0}, "tobacco_use": {"missing_count": "0", "completeness_percentage": 100.0}, "pregnancy_status": {"missing_count": "0", "completeness_percentage": 100.0}, "employment_type": {"missing_count": "0", "completeness_percentage": 100.0}, "employee_class": {"missing_count": "0", "completeness_percentage": 100.0}, "hire_date": {"missing_count": "0", "completeness_percentage": 100.0}, "medical_plan": {"missing_count": "0", "completeness_percentage": 100.0}, "dental_plan": {"missing_count": "0", "completeness_percentage": 100.0}, "vision_plan": {"missing_count": "0", "completeness_percentage": 100.0}, "life_plan": {"missing_count": "0", "completeness_percentage": 100.0}, "add_plan": {"missing_count": "0", "completeness_percentage": 100.0}, "coverage_tier": {"missing_count": "0", "completeness_percentage": 100.0}, "ssn": {"missing_count": "0", "completeness_percentage": 100.0}, "dept_1": {"missing_count": "2", "completeness_percentage": 80.0}, "dept_1_dob": {"missing_count": "2", "completeness_percentage": 80.0}, "dept_1_age": {"missing_count": "2", "completeness_percentage": 80.0}, "dept_1_gender": {"missing_count": "2", "completeness_percentage": 80.0}, "relationship_type_1": {"missing_count": "2", "completeness_percentage": 80.0}, "dept_1_medical_plan": {"missing_count": "2", "completeness_percentage": 80.0}, "dept_1_dental_plan": {"missing_count": "2", "completeness_percentage": 80.0}, "dept_1_vision_plan": {"missing_count": "2", "completeness_percentage": 80.0}, "dept_2": {"missing_count": "5", "completeness_percentage": 50.0}, "dept_2_dob": {"missing_count": "5", "completeness_percentage": 50.0}, "dept_2_age": {"missing_count": "5", "completeness_percentage": 50.0}, "dept_2_gender": {"missing_count": "5", "completeness_percentage": 50.0}, "relationship_type_2": {"missing_count": "5", "completeness_percentage": 50.0}, "dept_2_medical_plan": {"missing_count": "5", "completeness_percentage": 50.0}, "dept_2_dental_plan": {"missing_count": "5", "completeness_percentage": 50.0}, "dept_2_vision_plan": {"missing_count": "5", "completeness_percentage": 50.0}, "dept_3": {"missing_count": "8", "completeness_percentage": 20.0}, "dept_3_dob": {"missing_count": "8", "completeness_percentage": 20.0}, "dept_3_age": {"missing_count": "8", "completeness_percentage": 20.0}, "dept_3_gender": {"missing_count": "8", "completeness_percentage": 20.0}, "relationship_type_3": {"missing_count": "8", "completeness_percentage": 20.0}, "dept_3_medical_plan": {"missing_count": "8", "completeness_percentage": 20.0}, "dept_3_dental_plan": {"missing_count": "8", "completeness_percentage": 20.0}, "dept_3_vision_plan": {"missing_count": "8", "completeness_percentage": 20.0}, "dept_count": {"missing_count": "0", "completeness_percentage": 100.0}, "name": {"missing_count": "0", "completeness_percentage": 100.0}, "age": {"missing_count": "0", "completeness_percentage": 100.0}, "middle_name": {"missing_count": "10", "completeness_percentage": 0.0}, "relationship": {"missing_count": "10", "completeness_percentage": 0.0}, "record_type": {"missing_count": "10", "completeness_percentage": 0.0}, "department": {"missing_count": "0", "completeness_percentage": 100.0}, "dept_4": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_4_dob": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_4_age": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_4_gender": {"missing_count": "10", "completeness_percentage": 0.0}, "relationship_type_4": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_5": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_5_dob": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_5_age": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_5_gender": {"missing_count": "10", "completeness_percentage": 0.0}, "relationship_type_5": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_6": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_6_dob": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_6_age": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_6_gender": {"missing_count": "10", "completeness_percentage": 0.0}, "relationship_type_6": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_7": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_7_dob": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_7_age": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_7_gender": {"missing_count": "10", "completeness_percentage": 0.0}, "relationship_type_7": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_8": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_8_dob": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_8_age": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_8_gender": {"missing_count": "10", "completeness_percentage": 0.0}, "relationship_type_8": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_9": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_9_dob": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_9_age": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_9_gender": {"missing_count": "10", "completeness_percentage": 0.0}, "relationship_type_9": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_10": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_10_dob": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_10_age": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_10_gender": {"missing_count": "10", "completeness_percentage": 0.0}, "relationship_type_10": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_11": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_11_dob": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_11_age": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_11_gender": {"missing_count": "10", "completeness_percentage": 0.0}, "relationship_type_11": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_12": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_12_dob": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_12_age": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_12_gender": {"missing_count": "10", "completeness_percentage": 0.0}, "relationship_type_12": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_13": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_13_dob": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_13_age": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_13_gender": {"missing_count": "10", "completeness_percentage": 0.0}, "relationship_type_13": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_14": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_14_dob": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_14_age": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_14_gender": {"missing_count": "10", "completeness_percentage": 0.0}, "relationship_type_14": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_15": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_15_dob": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_15_age": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_15_gender": {"missing_count": "10", "completeness_percentage": 0.0}, "relationship_type_15": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_16": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_16_dob": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_16_age": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_16_gender": {"missing_count": "10", "completeness_percentage": 0.0}, "relationship_type_16": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_17": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_17_dob": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_17_age": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_17_gender": {"missing_count": "10", "completeness_percentage": 0.0}, "relationship_type_17": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_18": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_18_dob": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_18_age": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_18_gender": {"missing_count": "10", "completeness_percentage": 0.0}, "relationship_type_18": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_19": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_19_dob": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_19_age": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_19_gender": {"missing_count": "10", "completeness_percentage": 0.0}, "relationship_type_19": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_20": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_20_dob": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_20_age": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_20_gender": {"missing_count": "10", "completeness_percentage": 0.0}, "relationship_type_20": {"missing_count": "10", "completeness_percentage": 0.0}, "job_type": {"missing_count": "0", "completeness_percentage": 100.0}, "region": {"missing_count": "0", "completeness_percentage": 100.0}, "health_condition": {"missing_count": "0", "completeness_percentage": 100.0}, "chronic_condition": {"missing_count": "0", "completeness_percentage": 100.0}, "prescription_use": {"missing_count": "0", "completeness_percentage": 100.0}, "income_tier": {"missing_count": "0", "completeness_percentage": 100.0}, "risk_tolerance": {"missing_count": "0", "completeness_percentage": 100.0}, "lifestyle": {"missing_count": "0", "completeness_percentage": 100.0}, "travel_frequency": {"missing_count": "0", "completeness_percentage": 100.0}, "hsa_familiarity": {"missing_count": "0", "completeness_percentage": 100.0}, "mental_health_needs": {"missing_count": "0", "completeness_percentage": 100.0}}, "critical_field_completeness": {"name": {"missing_count": "0", "completeness_percentage": 100.0}, "gender": {"missing_count": "0", "completeness_percentage": 100.0}, "zipcode": {"missing_count": "0", "completeness_percentage": 100.0}, "marital_status": {"missing_count": "0", "completeness_percentage": 100.0}}}}, "prediction_summary": {"total_employees": 10, "plan_type_distribution": {"successful_predictions": {"HMO": 4, "PPO": 3, "HDHP + HSA": 2, "POS": 1}, "failed_predictions": "0", "success_rate": 100.0, "total_predicted": "10"}, "benefits_distribution": {}, "confidence_metrics": {"plan_confidence": {"mean": "0.763", "min": "0.448", "max": "0.909", "count": 10}}, "prediction_success_rates": {}, "top_3_plan_probabilities": []}, "total_employees": 10, "total_columns": 161, "has_predictions": true, "prediction_columns": ["predicted_plan_type", "plan_confidence"]}, "metadata": {"file_processing": {"filename": "col5_comprehensive_all.csv", "size": 3076, "original_column_names": ["Employee_ID", "First_Name", "Last_Name", "Gender", "DOB", "Address1", "Address2", "City", "State", "ZIP", "Marital_Status", "Salary", "Tobacco_Use", "Pregnancy_Status", "Employment_Type", "Employee_Class", "Hire_Date", "Medical_Plan", "Dental_Plan", "Vision_Plan", "Life_Plan", "ADD_Plan", "Coverage_Tier", "SSN", "dept_1", "dept_1_dob", "dept_1_age", "dept_1_gender", "relationship_type_1", "dept_1_medical_plan", "dept_1_dental_plan", "dept_1_vision_plan", "dept_2", "dept_2_dob", "dept_2_age", "dept_2_gender", "relationship_type_2", "dept_2_medical_plan", "dept_2_dental_plan", "dept_2_vision_plan", "dept_3", "dept_3_dob", "dept_3_age", "dept_3_gender", "relationship_type_3", "dept_3_medical_plan", "dept_3_dental_plan", "dept_3_vision_plan", "dept_count"]}, "pattern_analysis": {"pattern_type": "column_based_single_row", "pattern_confidence": 1.4000000000000001, "pattern_reason": "Found 21 dependent columns; Found numbered dependent patterns: [1, 2, 3]; Found 3 relationship type columns; Found 'dept_count' column; No duplicate employee_ids (consistent with single row per employee)", "analysis_details": {"confidence": 1.4000000000000001, "reason": "Found 21 dependent columns; Found numbered dependent patterns: [1, 2, 3]; Found 3 relationship type columns; Found 'dept_count' column; No duplicate employee_ids (consistent with single row per employee)"}}, "field_mapping": {"total_fields_mapped": 40, "mapping_success": true, "unmapped_fields": ["dept_1_medical_plan", "dept_1_dental_plan", "dept_1_vision_plan", "dept_2_medical_plan", "dept_2_dental_plan", "dept_2_vision_plan", "dept_3_medical_plan", "dept_3_dental_plan", "dept_3_vision_plan"], "mapping_confidence": 0, "field_mapping_details": {"Employee_ID": "employee_id", "First_Name": "first_name", "Last_Name": "last_name", "Gender": "gender", "DOB": "dob", "Address1": "address1", "Address2": "address2", "City": "city", "State": "state", "ZIP": "zipcode", "Marital_Status": "marital_status", "Salary": "salary", "Tobacco_Use": "tobacco_use", "Pregnancy_Status": "pregnancy_status", "Employment_Type": "employment_type", "Employee_Class": "employee_class", "Hire_Date": "hire_date", "Medical_Plan": "medical_plan", "Dental_Plan": "dental_plan", "Vision_Plan": "vision_plan", "Life_Plan": "life_plan", "ADD_Plan": "add_plan", "Coverage_Tier": "coverage_tier", "SSN": "ssn", "dept_1": "dept_1", "dept_1_dob": "dept_1_dob", "dept_1_age": "dept_1_age", "dept_1_gender": "dept_1_gender", "relationship_type_1": "relationship_type_1", "dept_2": "dept_2", "dept_2_dob": "dept_2_dob", "dept_2_age": "dept_2_age", "dept_2_gender": "dept_2_gender", "relationship_type_2": "relationship_type_2", "dept_3": "dept_3", "dept_3_dob": "dept_3_dob", "dept_3_age": "dept_3_age", "dept_3_gender": "dept_3_gender", "relationship_type_3": "relationship_type_3", "dept_count": "dept_count"}}, "data_processing": {"validation_passed": false, "processing_summary": {"original_rows": 10, "original_columns": 49, "processed_rows": 10, "processed_columns": 140, "processing_time_seconds": 12.49, "validation_passed": true, "validation_errors": 0, "error_rows": 0, "fields_processed": {"name_fields": true, "age_converted": true, "gender_standardized": true, "addresses_cleaned": true, "zipcode_validated": true}, "data_quality": {"complete_records": 10, "missing_data_rows": 0}}, "data_quality_score": 0.8, "processing_details": {"success": true, "preprocessed_data": "  employee_id first_name  last_name  gender         dob        address1   address2  ... dept_19_gender relationship_type_19  dept_20 dept_20_dob  dept_20_age dept_20_gender relationship_type_20\n0        E001       <PERSON>  1980-01-15     123 Main St     Apt 1A  ...           None                 None     None        None         None           None                 None\n1        E002      <PERSON>  Female  1975-03-10     456 Oak Ave        NaN  ...           None                 None     None        None         None           None                 None\n2        E003    <PERSON>  1990-06-30     789 Pine Rd    Unit 5B  ...           None                 None     None        None         None           None                 None\n3        E004      <PERSON>  Female  1985-11-05      321 Elm St        NaN  ...           None                 None     None        None         None           None                 None\n4        E005      <PERSON>  1978-02-28    654 Maple Dr  Suite 200  ...           None                 None     None        None         None           None                 None\n5        E006    <PERSON>  1983-07-12    987 Cedar Ln        NaN  ...           None                 None     None        None         None           None                 None\n6        E007      <PERSON>  1987-12-03   147 Birch Ave     Apt 3C  ...           None                 None     None        None         None           None                 None\n7        E008      <PERSON>  1979-10-17   258 Spruce St        NaN  ...           None                 None     None        None         None           None                 None\n8        E009       <PERSON>  1981-03-24  369 <PERSON>    Floor 2  ...           None                 None     None        None         None           None                 None\n9        E010     <PERSON>  1986-09-09   741 Poplar Pl        NaN  ...           None                 None     None        None         None           None                 None\n\n[10 rows x 140 columns]", "validation": {"is_valid": true, "errors": [], "error_rows": [], "total_errors": 0}, "summary": {"original_rows": 10, "original_columns": 49, "processed_rows": 10, "processed_columns": 140, "processing_time_seconds": 12.49, "validation_passed": true, "validation_errors": 0, "error_rows": 0, "fields_processed": {"name_fields": true, "age_converted": true, "gender_standardized": true, "addresses_cleaned": true, "zipcode_validated": true}, "data_quality": {"complete_records": 10, "missing_data_rows": 0}}}}, "enrichment_and_prediction": {"success": true, "enriched_data": "  employee_id first_name  last_name  gender  ... benefits_confidence                                    benefits_reason                         top_3_benefits top_3_benefits_confidences\n0        E001       <PERSON>    Male  ...                 1.0  Dental: Essential oral health coverage for all...             [Dental, Vision, Accident]            [0.5, 0.5, 0.5]\n1        E002      <PERSON>  Female  ...                 1.0  Dental: Essential oral health coverage for all...                       [Dental, Vision]                 [0.5, 0.5]\n2        E003    <PERSON>    Male  ...                 1.0  FSA: Tax-advantaged account for medical expens...             [FSA, Employee Assistance]                 [0.5, 0.5]\n3        E004      <PERSON>  Female  ...                 1.0  Dental: Essential oral health coverage for all...   [Dental, Vision, Hospital Indemnity]            [0.5, 0.5, 0.5]\n4        E005      <PERSON>    Male  ...                 1.0  Critical Illness: Important protection for age...  [Critical Illness, Wellness Programs]                 [0.5, 0.5]\n5        E006    <PERSON>    Male  ...                 0.0                                ML model prediction   [Dental, Vision, Hospital Indemnity]            [0.5, 0.5, 0.5]\n6        E007      <PERSON>  ...                 1.0  Dental: Essential oral health coverage for all...   [Dental, Vision, Hospital Indemnity]            [0.5, 0.5, 0.5]\n7        E008      <PERSON>    Male  ...                 1.0  Dental: Essential oral health coverage for all...             [Dental, Vision, Accident]            [0.5, 0.5, 0.5]\n8        E009       <PERSON>  Female  ...                 1.0  Dental: Essential oral health coverage for all...     [Dental, Vision, Critical Illness]            [0.5, 0.5, 0.5]\n9        E010     Xavier  Rodriguez    Male  ...                 1.0  Accident: Recommended for active lifestyle and...                        [Accident, STD]                 [0.5, 0.5]\n\n[10 rows x 161 columns]", "enrichment_summary": {"total_employees": 10, "features_analyzed": 19, "enrichment_details": {"age": {"original_missing": "0", "final_missing": "0", "enriched_count": "0", "completion_rate": 100.0}, "gender": {"original_missing": "0", "final_missing": "0", "enriched_count": "0", "completion_rate": 100.0}, "marital_status": {"original_missing": "0", "final_missing": "0", "enriched_count": "0", "completion_rate": 100.0}, "dept_count": {"original_missing": "0", "final_missing": "0", "enriched_count": "0", "completion_rate": 100.0}, "job_type": {"original_missing": 10, "final_missing": "0", "enriched_count": "10", "completion_rate": 100.0}, "employment_type": {"original_missing": "0", "final_missing": "0", "enriched_count": "0", "completion_rate": 100.0}, "department": {"original_missing": "10", "final_missing": "0", "enriched_count": "10", "completion_rate": 100.0}, "region": {"original_missing": 10, "final_missing": "0", "enriched_count": "10", "completion_rate": 100.0}, "health_condition": {"original_missing": 10, "final_missing": "0", "enriched_count": "10", "completion_rate": 100.0}, "chronic_condition": {"original_missing": 10, "final_missing": "0", "enriched_count": "10", "completion_rate": 100.0}, "prescription_use": {"original_missing": 10, "final_missing": "0", "enriched_count": "10", "completion_rate": 100.0}, "income_tier": {"original_missing": 10, "final_missing": "0", "enriched_count": "10", "completion_rate": 100.0}, "risk_tolerance": {"original_missing": 10, "final_missing": "0", "enriched_count": "10", "completion_rate": 100.0}, "lifestyle": {"original_missing": 10, "final_missing": "0", "enriched_count": "10", "completion_rate": 100.0}, "travel_frequency": {"original_missing": 10, "final_missing": "0", "enriched_count": "10", "completion_rate": 100.0}, "hsa_familiarity": {"original_missing": 10, "final_missing": "0", "enriched_count": "10", "completion_rate": 100.0}, "pregnancy_status": {"original_missing": "0", "final_missing": "0", "enriched_count": "0", "completion_rate": 100.0}, "tobacco_use": {"original_missing": "0", "final_missing": "0", "enriched_count": "0", "completion_rate": 100.0}, "mental_health_needs": {"original_missing": 10, "final_missing": "0", "enriched_count": "10", "completion_rate": 100.0}}, "data_quality_improvement": {"total_missing_before": "120", "total_missing_after": "0", "total_enriched": "120", "overall_completion_rate": 100.0}}, "comprehensive_statistics": {"basic_demographics": {"age_statistics": {"average_age": 42.2, "median_age": 43.0, "age_range": {"min": 35, "max": 50}, "age_distribution": {"18-30": 0, "31-45": 8, "46-60": 2, "60+": 0}}, "gender_composition": {"counts": {"Male": 6, "Female": 4}, "percentages": {"Male": 60.0, "Female": 40.0}}, "marital_status_distribution": {"counts": {"Married": 5, "Not Married": 5}, "percentages": {"Married": 50.0, "Not Married": 50.0}}}, "dependent_analysis": {"dependent_count_distribution": {"average_dependents": 1.5, "median_dependents": 1.5, "distribution": {"2": 3, "1": 3, "3": 2, "0": 2}, "employees_with_dependents": 8, "percentage_with_dependents": 80.0}, "dependent_age_analysis": {"total_dependents_found": 15, "average_dependent_age": 22.93, "median_dependent_age": 17.0, "dependents_over_26_as_children": 0, "age_distribution": {"0-5": 0, "6-12": 2, "13-18": 7, "19-26": 1, "26+": 5}}}, "employment_demographics": {"department_distribution": {"counts": {"Information Technology": 5, "Manufacturing": 3, "Engineering": 1, "Sales": 1}, "percentages": {"Information Technology": 50.0, "Manufacturing": 30.0, "Engineering": 10.0, "Sales": 10.0}}, "employment_type_distribution": {"counts": {"Full-Time": 7, "Part-Time": 2, "Temporary": 1}, "percentages": {"Full-Time": 70.0, "Part-Time": 20.0, "Temporary": 10.0}}, "job_type_distribution": {"counts": {"Desk": 10}, "percentages": {"Desk": 100.0}}, "employee_class_distribution": {"counts": {"Salaried": 3, "Hourly": 3, "Executive": 1, "Management": 1, "Union": 1, "Non-Union": 1}, "percentages": {"Salaried": 30.0, "Hourly": 30.0, "Executive": 10.0, "Management": 10.0, "Union": 10.0, "Non-Union": 10.0}}, "salary_analysis": {"average_salary": 61150.0, "median_salary": 61500.0, "salary_range": {"min": "38000", "max": "95000"}, "salary_distribution": {"under_40k": 1, "40k_75k": 7, "75k_100k": 2, "over_100k": 0}}}, "health_and_lifestyle": {"health_condition_distribution": {"counts": {"Good": 6, "Fair": 3, "Excellent": 1}, "percentages": {"Good": 60.0, "Fair": 30.0, "Excellent": 10.0}}, "chronic_condition_distribution": {"counts": {"N": 8, "Y": 2}, "percentages": {"N": 80.0, "Y": 20.0}}, "tobacco_use_distribution": {"counts": {"Y": 6, "N": 4}, "percentages": {"Y": 60.0, "N": 40.0}}, "lifestyle_distribution": {"counts": {"Moderate": 8, "Active": 2}, "percentages": {"Moderate": 80.0, "Active": 20.0}}, "prescription_use_distribution": {"counts": {"None": 7, "Occasional": 2, "Regular": 1}, "percentages": {"None": 70.0, "Occasional": 20.0, "Regular": 10.0}}}, "coverage_analysis": {"coverage_tier_distribution": {"counts": {"Family": 4, "Employee Only": 3, "Employee + Spouse": 2, "Employee + Child(ren)": 1}, "percentages": {"Family": 40.0, "Employee Only": 30.0, "Employee + Spouse": 20.0, "Employee + Child(ren)": 10.0}}, "coverage_tier_by_family_size": {"Employee + Child(ren)": {"0": 0, "1": 1, "2": 0, "3": 0}, "Employee + Spouse": {"0": 0, "1": 1, "2": 1, "3": 0}, "Employee Only": {"0": 2, "1": 1, "2": 0, "3": 0}, "Family": {"0": 0, "1": 0, "2": 2, "3": 2}}, "medical_plan_distribution": {"counts": {"Y": 8, "N": 2}, "percentages": {"Y": 80.0, "N": 20.0}}, "dental_plan_distribution": {"counts": {"Y": 7, "N": 3}, "percentages": {"Y": 70.0, "N": 30.0}}, "vision_plan_distribution": {"counts": {"Y": 6, "N": 4}, "percentages": {"Y": 60.0, "N": 40.0}}, "life_plan_distribution": {"counts": {"Y": 6, "N": 4}, "percentages": {"Y": 60.0, "N": 40.0}}}, "geographic_distribution": {"state_distribution": {"counts": {"CA": 1, "TX": 1, "FL": 1, "NY": 1, "WA": 1, "OR": 1, "AZ": 1, "CO": 1, "NV": 1, "UT": 1}, "percentages": {"CA": 10.0, "TX": 10.0, "FL": 10.0, "NY": 10.0, "WA": 10.0, "OR": 10.0, "AZ": 10.0, "CO": 10.0, "NV": 10.0, "UT": 10.0}}, "region_distribution": {"counts": {"Urban": 8, "Rural": 2}, "percentages": {"Urban": 80.0, "Rural": 20.0}}, "top_cities": {"counts": {"Anytown": 1, "Somewhere": 1, "Elsewhere": 1, "Nowhere": 1, "Anywhere": 1, "Someplace": 1, "Othertown": 1, "Newplace": 1, "Lastplace": 1, "Finaltown": 1}, "percentages": {"Anytown": 10.0, "Somewhere": 10.0, "Elsewhere": 10.0, "Nowhere": 10.0, "Anywhere": 10.0, "Someplace": 10.0, "Othertown": 10.0, "Newplace": 10.0, "Lastplace": 10.0, "Finaltown": 10.0}}}, "financial_demographics": {"income_tier_distribution": {"counts": {"Medium ($50K–$100K)": 7, "High (>$100K)": 2, "Low (<$50K)": 1}, "percentages": {"Medium ($50K–$100K)": 70.0, "High (>$100K)": 20.0, "Low (<$50K)": 10.0}}, "risk_tolerance_distribution": {"counts": {"Low": 5, "Medium": 4, "High": 1}, "percentages": {"Low": 50.0, "Medium": 40.0, "High": 10.0}}, "hsa_familiarity_distribution": {"counts": {"Y": 7, "N": 3}, "percentages": {"Y": 70.0, "N": 30.0}}}, "risk_assessment": {"group_risk_score": 36.9, "group_risk_level": "Medium", "risk_distribution": {"low_risk": 2, "medium_risk": 8, "high_risk": 0}, "risk_statistics": {"min_risk_score": 18, "max_risk_score": 53, "median_risk_score": 38.0, "std_risk_score": 9.8}, "top_risk_factors": {"Tobacco Use": 6, "Poor Health Condition": 3, "Chronic Conditions": 2, "Advanced Age": 1}, "individual_risks": [{"employee_id": "E001", "risk_score": 38, "risk_level": "Medium", "risk_factors": ["Tobacco user"]}, {"employee_id": "E002", "risk_score": 33, "risk_level": "Medium", "risk_factors": ["Age 50.0 (high risk)"]}, {"employee_id": "E003", "risk_score": 40, "risk_level": "Medium", "risk_factors": ["Health condition: Fair", "Tobacco user"]}, {"employee_id": "E004", "risk_score": 33, "risk_level": "Medium", "risk_factors": ["Tobacco user"]}, {"employee_id": "E005", "risk_score": 43, "risk_level": "Medium", "risk_factors": ["Has chronic condition"]}, {"employee_id": "E006", "risk_score": 48, "risk_level": "Medium", "risk_factors": ["Health condition: Fair", "Tobacco user"]}, {"employee_id": "E007", "risk_score": 18, "risk_level": "Low", "risk_factors": []}, {"employee_id": "E008", "risk_score": 38, "risk_level": "Medium", "risk_factors": ["Tobacco user"]}, {"employee_id": "E009", "risk_score": 53, "risk_level": "Medium", "risk_factors": ["Health condition: Fair", "Has chronic condition"]}, {"employee_id": "E010", "risk_score": 25, "risk_level": "Low", "risk_factors": ["Tobacco user"]}]}, "data_quality_metrics": {"overall_completeness": {"total_cells": 1510, "missing_cells": "1005", "completeness_percentage": 33.44}, "column_completeness": {"employee_id": {"missing_count": "0", "completeness_percentage": 100.0}, "first_name": {"missing_count": "0", "completeness_percentage": 100.0}, "last_name": {"missing_count": "0", "completeness_percentage": 100.0}, "gender": {"missing_count": "0", "completeness_percentage": 100.0}, "dob": {"missing_count": "0", "completeness_percentage": 100.0}, "address1": {"missing_count": "0", "completeness_percentage": 100.0}, "address2": {"missing_count": "5", "completeness_percentage": 50.0}, "city": {"missing_count": "0", "completeness_percentage": 100.0}, "state": {"missing_count": "0", "completeness_percentage": 100.0}, "zipcode": {"missing_count": "0", "completeness_percentage": 100.0}, "marital_status": {"missing_count": "0", "completeness_percentage": 100.0}, "salary": {"missing_count": "0", "completeness_percentage": 100.0}, "tobacco_use": {"missing_count": "0", "completeness_percentage": 100.0}, "pregnancy_status": {"missing_count": "0", "completeness_percentage": 100.0}, "employment_type": {"missing_count": "0", "completeness_percentage": 100.0}, "employee_class": {"missing_count": "0", "completeness_percentage": 100.0}, "hire_date": {"missing_count": "0", "completeness_percentage": 100.0}, "medical_plan": {"missing_count": "0", "completeness_percentage": 100.0}, "dental_plan": {"missing_count": "0", "completeness_percentage": 100.0}, "vision_plan": {"missing_count": "0", "completeness_percentage": 100.0}, "life_plan": {"missing_count": "0", "completeness_percentage": 100.0}, "add_plan": {"missing_count": "0", "completeness_percentage": 100.0}, "coverage_tier": {"missing_count": "0", "completeness_percentage": 100.0}, "ssn": {"missing_count": "0", "completeness_percentage": 100.0}, "dept_1": {"missing_count": "2", "completeness_percentage": 80.0}, "dept_1_dob": {"missing_count": "2", "completeness_percentage": 80.0}, "dept_1_age": {"missing_count": "2", "completeness_percentage": 80.0}, "dept_1_gender": {"missing_count": "2", "completeness_percentage": 80.0}, "relationship_type_1": {"missing_count": "2", "completeness_percentage": 80.0}, "dept_1_medical_plan": {"missing_count": "2", "completeness_percentage": 80.0}, "dept_1_dental_plan": {"missing_count": "2", "completeness_percentage": 80.0}, "dept_1_vision_plan": {"missing_count": "2", "completeness_percentage": 80.0}, "dept_2": {"missing_count": "5", "completeness_percentage": 50.0}, "dept_2_dob": {"missing_count": "5", "completeness_percentage": 50.0}, "dept_2_age": {"missing_count": "5", "completeness_percentage": 50.0}, "dept_2_gender": {"missing_count": "5", "completeness_percentage": 50.0}, "relationship_type_2": {"missing_count": "5", "completeness_percentage": 50.0}, "dept_2_medical_plan": {"missing_count": "5", "completeness_percentage": 50.0}, "dept_2_dental_plan": {"missing_count": "5", "completeness_percentage": 50.0}, "dept_2_vision_plan": {"missing_count": "5", "completeness_percentage": 50.0}, "dept_3": {"missing_count": "8", "completeness_percentage": 20.0}, "dept_3_dob": {"missing_count": "8", "completeness_percentage": 20.0}, "dept_3_age": {"missing_count": "8", "completeness_percentage": 20.0}, "dept_3_gender": {"missing_count": "8", "completeness_percentage": 20.0}, "relationship_type_3": {"missing_count": "8", "completeness_percentage": 20.0}, "dept_3_medical_plan": {"missing_count": "8", "completeness_percentage": 20.0}, "dept_3_dental_plan": {"missing_count": "8", "completeness_percentage": 20.0}, "dept_3_vision_plan": {"missing_count": "8", "completeness_percentage": 20.0}, "dept_count": {"missing_count": "0", "completeness_percentage": 100.0}, "name": {"missing_count": "0", "completeness_percentage": 100.0}, "age": {"missing_count": "0", "completeness_percentage": 100.0}, "middle_name": {"missing_count": "10", "completeness_percentage": 0.0}, "relationship": {"missing_count": "10", "completeness_percentage": 0.0}, "record_type": {"missing_count": "10", "completeness_percentage": 0.0}, "department": {"missing_count": "0", "completeness_percentage": 100.0}, "dept_4": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_4_dob": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_4_age": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_4_gender": {"missing_count": "10", "completeness_percentage": 0.0}, "relationship_type_4": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_5": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_5_dob": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_5_age": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_5_gender": {"missing_count": "10", "completeness_percentage": 0.0}, "relationship_type_5": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_6": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_6_dob": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_6_age": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_6_gender": {"missing_count": "10", "completeness_percentage": 0.0}, "relationship_type_6": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_7": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_7_dob": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_7_age": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_7_gender": {"missing_count": "10", "completeness_percentage": 0.0}, "relationship_type_7": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_8": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_8_dob": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_8_age": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_8_gender": {"missing_count": "10", "completeness_percentage": 0.0}, "relationship_type_8": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_9": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_9_dob": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_9_age": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_9_gender": {"missing_count": "10", "completeness_percentage": 0.0}, "relationship_type_9": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_10": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_10_dob": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_10_age": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_10_gender": {"missing_count": "10", "completeness_percentage": 0.0}, "relationship_type_10": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_11": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_11_dob": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_11_age": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_11_gender": {"missing_count": "10", "completeness_percentage": 0.0}, "relationship_type_11": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_12": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_12_dob": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_12_age": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_12_gender": {"missing_count": "10", "completeness_percentage": 0.0}, "relationship_type_12": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_13": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_13_dob": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_13_age": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_13_gender": {"missing_count": "10", "completeness_percentage": 0.0}, "relationship_type_13": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_14": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_14_dob": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_14_age": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_14_gender": {"missing_count": "10", "completeness_percentage": 0.0}, "relationship_type_14": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_15": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_15_dob": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_15_age": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_15_gender": {"missing_count": "10", "completeness_percentage": 0.0}, "relationship_type_15": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_16": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_16_dob": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_16_age": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_16_gender": {"missing_count": "10", "completeness_percentage": 0.0}, "relationship_type_16": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_17": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_17_dob": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_17_age": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_17_gender": {"missing_count": "10", "completeness_percentage": 0.0}, "relationship_type_17": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_18": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_18_dob": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_18_age": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_18_gender": {"missing_count": "10", "completeness_percentage": 0.0}, "relationship_type_18": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_19": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_19_dob": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_19_age": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_19_gender": {"missing_count": "10", "completeness_percentage": 0.0}, "relationship_type_19": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_20": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_20_dob": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_20_age": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_20_gender": {"missing_count": "10", "completeness_percentage": 0.0}, "relationship_type_20": {"missing_count": "10", "completeness_percentage": 0.0}, "job_type": {"missing_count": "0", "completeness_percentage": 100.0}, "region": {"missing_count": "0", "completeness_percentage": 100.0}, "health_condition": {"missing_count": "0", "completeness_percentage": 100.0}, "chronic_condition": {"missing_count": "0", "completeness_percentage": 100.0}, "prescription_use": {"missing_count": "0", "completeness_percentage": 100.0}, "income_tier": {"missing_count": "0", "completeness_percentage": 100.0}, "risk_tolerance": {"missing_count": "0", "completeness_percentage": 100.0}, "lifestyle": {"missing_count": "0", "completeness_percentage": 100.0}, "travel_frequency": {"missing_count": "0", "completeness_percentage": 100.0}, "hsa_familiarity": {"missing_count": "0", "completeness_percentage": 100.0}, "mental_health_needs": {"missing_count": "0", "completeness_percentage": 100.0}}, "critical_field_completeness": {"name": {"missing_count": "0", "completeness_percentage": 100.0}, "gender": {"missing_count": "0", "completeness_percentage": 100.0}, "zipcode": {"missing_count": "0", "completeness_percentage": 100.0}, "marital_status": {"missing_count": "0", "completeness_percentage": 100.0}}}}, "feature_validation": {"is_valid": true, "missing_features": [], "feature_completeness": {"age": {"present": true, "missing_values": "0", "completion_rate": 100.0}, "gender": {"present": true, "missing_values": "0", "completion_rate": 100.0}, "marital_status": {"present": true, "missing_values": "0", "completion_rate": 100.0}, "dept_count": {"present": true, "missing_values": "0", "completion_rate": 100.0}, "job_type": {"present": true, "missing_values": "0", "completion_rate": 100.0}, "employment_type": {"present": true, "missing_values": "0", "completion_rate": 100.0}, "department": {"present": true, "missing_values": "0", "completion_rate": 100.0}, "region": {"present": true, "missing_values": "0", "completion_rate": 100.0}, "health_condition": {"present": true, "missing_values": "0", "completion_rate": 100.0}, "chronic_condition": {"present": true, "missing_values": "0", "completion_rate": 100.0}, "prescription_use": {"present": true, "missing_values": "0", "completion_rate": 100.0}, "income_tier": {"present": true, "missing_values": "0", "completion_rate": 100.0}, "risk_tolerance": {"present": true, "missing_values": "0", "completion_rate": 100.0}, "lifestyle": {"present": true, "missing_values": "0", "completion_rate": 100.0}, "travel_frequency": {"present": true, "missing_values": "0", "completion_rate": 100.0}, "hsa_familiarity": {"present": true, "missing_values": "0", "completion_rate": 100.0}, "pregnancy_status": {"present": true, "missing_values": "0", "completion_rate": 100.0}, "tobacco_use": {"present": true, "missing_values": "0", "completion_rate": 100.0}, "mental_health_needs": {"present": true, "missing_values": "0", "completion_rate": 100.0}}, "overall_completion_rate": 100.0, "ready_for_prediction": "True"}, "prediction_method": "ml_models", "model_predictions": {"plan_types": {"total_predictions": 10, "average_confidence": "0.7630671", "unique_plans": 4}}, "prediction_summary": {"total_employees": 10, "plan_type_distribution": {"successful_predictions": {"HMO": 4, "PPO": 3, "HDHP + HSA": 2, "POS": 1}, "failed_predictions": "0", "success_rate": 100.0, "total_predicted": "10"}, "benefits_distribution": {}, "confidence_metrics": {"plan_confidence": {"mean": "0.763", "min": "0.448", "max": "0.909", "count": 10}}, "prediction_success_rates": {}, "top_3_plan_probabilities": ["E001: [0.8101897, 0.16297188, 0.*********]", "E002: [0.90889263, 0.049560472, 0.026334891]", "E003: [0.7811365, 0.********, 0.*********]", "E004: [0.7520252, 0.0847589, 0.********]", "E005: [0.6883096, 0.15991065, 0.083892204]", "E006: [0.62563807, 0.12084466, 0.11903042]", "E007: [0.8533228, 0.07770858, 0.056844845]", "E008: [0.8666599, 0.10609808, 0.**********]", "E009: [0.8966832, 0.059428215, 0.027713045]", "E010: [0.4478133, 0.37112904, 0.16094308]"]}}}}}