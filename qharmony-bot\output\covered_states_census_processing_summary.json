{"file_info": {"original_filename": "covered_states_census.csv", "processing_timestamp": "2025-07-16 13:55:59.433277", "total_processing_time_seconds": 109.13225936889648}, "final_response": {"success": true, "status_code": 200, "message": "Census file processed successfully", "data": {"summary": {"total_employees": 20, "processing_success": true, "data_quality_score": 0.9434593200683593, "api_integration_status": {"health_plans_available": true, "prediction_method": "ml_models", "total_plans_found": 53}}, "statistics": {"demographics": {"age_statistics": {"average_age": 46.75, "median_age": 47.0, "age_range": {"min": 29, "max": 63}, "age_distribution": {"18-30": 2, "31-45": 8, "46-60": 7, "60+": 3}}, "gender_composition": {"counts": {"Male": 10, "Female": 10}, "percentages": {"Male": 50.0, "Female": 50.0}}, "marital_status_distribution": {"counts": {"Married": 10, "Not Married": 10}, "percentages": {"Married": 50.0, "Not Married": 50.0}}}, "employment": {"department_distribution": {"counts": {"Engineering": 8, "Finance": 4, "Sales": 3, "Manufacturing": 3, "Information Technology": 2}, "percentages": {"Engineering": 40.0, "Finance": 20.0, "Sales": 15.0, "Manufacturing": 15.0, "Information Technology": 10.0}}, "employment_type_distribution": {"counts": {"Full-time": 18, "Part-time": 2}, "percentages": {"Full-time": 90.0, "Part-time": 10.0}}, "job_type_distribution": {"counts": {"Desk": 20}, "percentages": {"Desk": 100.0}}, "employee_class_distribution": {"counts": {}, "percentages": {}}}, "dependents": {"dependent_count_distribution": {"average_dependents": 1.0, "median_dependents": 1.0, "distribution": {"2": 8, "0": 8, "1": 4}, "employees_with_dependents": 12, "percentage_with_dependents": 60.0}, "dependent_age_analysis": {"total_dependents_found": 20, "average_dependent_age": 11.25, "median_dependent_age": 11.5, "dependents_over_26_as_children": 0, "age_distribution": {"0-5": 1, "6-12": 11, "13-18": 8, "19-26": 0, "26+": 0}}}, "health_plans": {"total_employees": 20, "employees_with_plans": 19, "employees_without_plans": 1, "api_errors": 0, "unsupported_states": [], "supported_employees": 20, "total_plans_found": 53, "unique_plan_types": [], "unique_metal_levels": [], "unique_issuers": ["Ambetter from Sunflower Health Plan", "Blue Cross Blue Shield of Wyoming", "Ambetter from Arizona Complete Health ", "Ambetter Health of Delaware", "Molina Healthcare", "Ambetter of Oklahoma", "Blue Cross and Blue Shield of Montana", "BlueCross BlueShield of Tennessee", "Blue Cross and Blue Shield of Texas", "Blue Cross and Blue Shield of NC", "Aetna CVS Health", "Anthem Blue Cross and Blue Shield", "Oscar Health Insurance", "Blue Cross and Blue Shield of Alabama", "Primewell Health Services of Mississippi", "Blue Care Network of Michigan", "Am<PERSON><PERSON> from Home State Health", "Health Advantage", "<PERSON><PERSON><PERSON> from Nebraska Total Care"], "plan_type_distribution": {"HMO": 8, "EPO": 5, "PPO": 3, "POS": 3}, "metal_level_distribution": {"Bronze": 19}, "premium_statistics": {"min_premium": 281.69, "max_premium": 1544.05, "avg_premium": 580.6}, "hsa_eligible_plans": 3, "quality_ratings": {"plans_with_ratings": 16, "avg_rating": 3.0}}, "predictions": {"total_employees": 20, "plan_type_distribution": {"counts": {"POS": 7, "PPO": 7, "HMO": 3, "HDHP + HSA": 3}, "percentages": {"POS": 35.0, "PPO": 35.0, "HMO": 15.0, "HDHP + HSA": 15.0}}, "benefits_distribution": {"counts": {"Dental": 17, "Vision": 16, "Hospital Indemnity": 2, "Critical Illness": 4, "Accident": 6, "STD": 6, "Term Life": 4, "LTD": 4, "Wellness Programs": 3, "Employee Assistance": 3, "FSA": 2}, "most_common": [["Dental", 17], ["Vision", 16], ["Accident", 6], ["STD", 6], ["Critical Illness", 4]]}, "confidence_metrics": {"plan_confidence": {"mean": 0.8115310668945312, "min": 0.4981239140033722, "max": 0.9841006994247437, "std": 0.1442637**********}, "benefits_confidence": {"mean": 0.675, "min": 0.0, "max": 1.0, "std": 0.33541019662496846}}}, "risk_analysis": {}}, "employees": [{"employee_id": "EMP_001", "name": "<PERSON>", "age": 61.0, "gender": "Male", "marital_status": "Married", "zipcode": 75201, "city": "Dallas", "state": "TX", "income_tier": "Low (<$50K)", "employment_type": "Full-time", "dept_count": 2, "predicted_plan_type": "POS", "plan_confidence": 0.9586076736450195, "plan_reason": "Older age - PPO for comprehensive coverage and provider choice.", "predicted_benefits": ["Dental", "Vision", "Hospital Indemnity", "Critical Illness"], "benefits_confidence": 1.0, "benefits_reason": "Dental: Essential oral health coverage for all employees.; Vision: Eye care coverage for all employees.; Hospital Indemnity: Recommended due to fair health condition, age 61, or chronic conditions.; Critical Illness: Important protection for age 61 with fair health status.", "marketplace_plans_available": true, "plan_count": 3, "recommended_plan": {"id": "33602TX0461117", "name": "MyBlue Health Bronze℠ 402", "issuer": "Blue Cross and Blue Shield of Texas", "premium": 787.61, "metal_level": "Bronze", "type": "HMO", "deductible": 7400, "max_out_of_pocket": 9200, "hsa_eligible": false, "quality_rating": 3}, "benefits_coverage": {"emergency_room": "$950 Copay with deductible/50% Coinsurance after deductible", "primary_care": "No Charge", "specialist_care": "50% Coinsurance after deductible", "prescription_drugs": "$10", "mental_health": null, "maternity_care": null, "preventive_care": null}, "top_3_available_plans": "[{'id': '33602TX0461117', 'name': 'MyBlue Health Bronze℠ 402', 'issuer': 'Blue Cross and Blue Shield of Texas', 'premium': 787.61, 'premium_with_credit': 0, 'metal_level': 'Bronze', 'type': 'HMO', 'deductible': 7400, 'max_out_of_pocket': 9200, 'hsa_eligible': False, 'quality_rating': 3, 'has_national_network': False, 'key_benefits': {'prescription_drugs': '$10', 'specialist_care': '50% Coinsurance after deductible', 'emergency_room': '$950 Copay with deductible/50% Coinsurance after deductible', 'primary_care': 'No Charge'}, 'issuer_phone': '**************', 'urls': {'benefits': 'https://www.bcbstx.com/sbc/ind/sbc-bhsa01bftitxp-tx-2025.pdf', 'brochure': 'https://www.bcbstx.com/plan-docs/ind/brochure-tx-2025.pdf', 'formulary': 'https://www.myprime.com/content/dam/prime/memberportal/WebDocs/2025/Formularies/HIM/2025_TX_6T_HIM.pdf', 'network': 'https://my.providerfinderonline.com/?ci=tx-myblue-health&corp_code=TX'}}, {'id': '33602TX0461159', 'name': 'MyBlue Health Bronze℠ Standard', 'issuer': 'Blue Cross and Blue Shield of Texas', 'premium': 796.64, 'premium_with_credit': 0, 'metal_level': 'Bronze', 'type': 'HMO', 'deductible': 7500, 'max_out_of_pocket': 9200, 'hsa_eligible': False, 'quality_rating': 3, 'has_national_network': False, 'key_benefits': {'primary_care': '$50', 'specialist_care': '$100', 'emergency_room': '50% Coinsurance after deductible', 'prescription_drugs': '$25'}, 'issuer_phone': '**************', 'urls': {'benefits': 'https://www.bcbstx.com/sbc/ind/sbc-bhsd42bftitxp-tx-2025.pdf', 'brochure': 'https://www.bcbstx.com/plan-docs/ind/brochure-tx-2025.pdf', 'formulary': 'https://www.myprime.com/content/dam/prime/memberportal/WebDocs/2025/Formularies/HIM/2025_TX_4T_HIM.pdf', 'network': 'https://my.providerfinderonline.com/?ci=tx-myblue-health&corp_code=TX'}}, {'id': '47501TX0040002', 'name': 'Wellpoint Essential Bronze 4000 HSA ($0 Virtual PCP + $0 Select Drugs + Incentives)', 'issuer': 'WellPoint', 'premium': 830.3, 'premium_with_credit': 0, 'metal_level': 'Bronze', 'type': 'HMO', 'deductible': 4000, 'max_out_of_pocket': 8250, 'hsa_eligible': True, 'quality_rating': 0, 'has_national_network': False, 'key_benefits': {'primary_care': '40% Coinsurance after deductible', 'specialist_care': '40% Coinsurance after deductible', 'emergency_room': '$500 Copay after deductible/40% Coinsurance after deductible', 'prescription_drugs': '20% Coinsurance after deductible'}, 'issuer_phone': '**************', 'urls': {'benefits': 'https://sbc.wellpoint.com/dpsdeeplink/deepLink/WellpointEssentialBronze4000HSA0VirtualPCP0SelectDrugsIncentives/7ZJG/English/DG166705513357.pdf', 'brochure': 'http://wellpoint.ly/IND-TX-EN-KIT-2025', 'formulary': 'https://www.wellpoint.com/TXSelectdrugtier4', 'network': 'https://www.wellpoint.com/find-care/?alphaprefix=3730'}}]", "api_processing_status": "success"}, {"employee_id": "EMP_002", "name": "<PERSON>", "age": 35.0, "gender": "Female", "marital_status": "Not Married", "zipcode": 33101, "city": "Miami", "state": "FL", "income_tier": "Medium ($50K–$100K)", "employment_type": "Full-time", "dept_count": 0, "predicted_plan_type": "HMO", "plan_confidence": 0.4981239140033722, "plan_reason": "Medium income, urban area - EPO for network-based care.", "predicted_benefits": [], "benefits_confidence": 0.0, "benefits_reason": "ML model prediction", "marketplace_plans_available": true, "plan_count": 3, "recommended_plan": {"id": "54172FL0010010", "name": "Bronze 8", "issuer": "Molina Healthcare", "premium": 400.05, "metal_level": "Bronze", "type": "HMO", "deductible": 7500, "max_out_of_pocket": 9200, "hsa_eligible": false, "quality_rating": 2}, "benefits_coverage": {"emergency_room": "50% Coinsurance after deductible", "primary_care": "$50", "specialist_care": "$100", "prescription_drugs": "$25", "mental_health": null, "maternity_care": null, "preventive_care": null}, "top_3_available_plans": "[{'id': '54172FL0010010', 'name': 'Bronze 8', 'issuer': 'Molina Healthcare', 'premium': 400.05, 'premium_with_credit': 400.05, 'metal_level': 'Bronze', 'type': 'HMO', 'deductible': 7500, 'max_out_of_pocket': 9200, 'hsa_eligible': False, 'quality_rating': 2, 'has_national_network': False, 'key_benefits': {'primary_care': '$50', 'specialist_care': '$100', 'emergency_room': '50% Coinsurance after deductible', 'prescription_drugs': '$25'}, 'issuer_phone': '1-************', 'urls': {'benefits': 'https://www.molinamarketplace.com/members/fl/en-US/PDF/Marketplace/2025/FL25SBCE_B8_1.pdf', 'brochure': 'https://www.molinamarketplace.com/marketplace/fl/en-us/-/media/Molina/PublicWebsite/PDF/members/common/en-us/FNLMBRBrochure2025.ashx', 'formulary': 'https://www.molinamarketplace.com/members/fl/en-US/PDF/Marketplace/2025/FLFormulary2025.pdf', 'network': 'https://molina.sapphirethreesixtyfive.com//?ci=fl-marketplace'}}, {'id': '67926FL0010001', 'name': 'AmeriHealth Caritas Next Bronze Essential + No Referrals  ', 'issuer': 'AmeriHealth Caritas Next', 'premium': 370.1, 'premium_with_credit': 370.1, 'metal_level': 'Bronze', 'type': 'HMO', 'deductible': 9200, 'max_out_of_pocket': 9200, 'hsa_eligible': False, 'quality_rating': 0, 'has_national_network': False, 'key_benefits': {'prescription_drugs': 'No Charge After Deductible', 'specialist_care': 'No Charge After Deductible', 'emergency_room': 'No Charge After Deductible', 'primary_care': 'No Charge After Deductible'}, 'issuer_phone': '1-************', 'urls': {'benefits': 'https://www.amerihealthcaritasnext.com/assets/pdf/fl/2025/member/plan-documents/summary-of-benefits-fl-bronze-essential-on.pdf', 'brochure': 'https://www.amerihealthcaritasnext.com/assets/pdf/fl/2025/member/plan-documents/plan-brochure.pdf', 'formulary': 'https://client.formularynavigator.com/Search.aspx?siteCode=**********', 'network': 'https://amerihealthcaritasnext.healthsparq.com/healthsparq/public/#/one/city=&state=&postalCode=&country=&insurerCode=ACNEXT_I&brandCode=ACNEXT&alphaPrefix=&bcbsaProductId=&productCode=FLEX'}}, {'id': '67926FL0010002', 'name': 'AmeriHealth Caritas Next Bronze Signature + No Referrals  ', 'issuer': 'AmeriHealth Caritas Next', 'premium': 398.11, 'premium_with_credit': 398.11, 'metal_level': 'Bronze', 'type': 'HMO', 'deductible': 7500, 'max_out_of_pocket': 9200, 'hsa_eligible': False, 'quality_rating': 0, 'has_national_network': False, 'key_benefits': {'primary_care': '$50', 'specialist_care': '$100', 'emergency_room': '50% Coinsurance after deductible', 'prescription_drugs': '$25'}, 'issuer_phone': '1-************', 'urls': {'benefits': 'https://www.amerihealthcaritasnext.com/assets/pdf/fl/2025/member/plan-documents/summary-of-benefits-fl-bronze-signature-on.pdf', 'brochure': 'https://www.amerihealthcaritasnext.com/assets/pdf/fl/2025/member/plan-documents/plan-brochure.pdf', 'formulary': 'https://client.formularynavigator.com/Search.aspx?siteCode=**********', 'network': 'https://amerihealthcaritasnext.healthsparq.com/healthsparq/public/#/one/city=&state=&postalCode=&country=&insurerCode=ACNEXT_I&brandCode=ACNEXT&alphaPrefix=&bcbsaProductId=&productCode=FLEX'}}]", "api_processing_status": "success"}, {"employee_id": "EMP_003", "name": "<PERSON>", "age": 53.0, "gender": "Male", "marital_status": "Married", "zipcode": 30301, "city": "Atlanta", "state": "GA", "income_tier": "Low (<$50K)", "employment_type": "Full-time", "dept_count": 2, "predicted_plan_type": "HMO", "plan_confidence": 0.7138794660568237, "plan_reason": "Chronic conditions, older age - POS for coordinated care management.", "predicted_benefits": ["Dental", "Vision", "Accident", "Critical Illness", "STD"], "benefits_confidence": 1.0, "benefits_reason": "Dental: Essential oral health coverage for all employees.; Vision: Eye care coverage for all employees.; Accident: Recommended for active lifestyle and occasional travel frequency.; Critical Illness: Important protection for age 53 with good health status.", "marketplace_plans_available": false, "plan_count": 0, "benefits_coverage": {"emergency_room": null, "primary_care": null, "specialist_care": null, "prescription_drugs": null, "mental_health": null, "maternity_care": null, "preventive_care": null}, "api_processing_status": "no_plans_found"}, {"employee_id": "EMP_004", "name": "<PERSON>", "age": 39.0, "gender": "Female", "marital_status": "Not Married", "zipcode": 43201, "city": "Columbus", "state": "OH", "income_tier": "High (>$100K)", "employment_type": "Full-time", "dept_count": 1, "predicted_plan_type": "PPO", "plan_confidence": 0.7784996628761292, "plan_reason": "High income, excellent health, high risk tolerance with dependents - PPO for family coverage.", "predicted_benefits": ["Dental", "Vision", "Accident", "Term Life", "STD", "LTD", "Wellness Programs", "Employee Assistance"], "benefits_confidence": 1.0, "benefits_reason": "Dental: Essential oral health coverage for all employees.; Vision: Eye care coverage for all employees.; Term Life: Essential protection for 1 dependents.; STD: Income protection for High (>$100K) earners or excellent health status.; LTD: Long-term income protection for High (>$100K) earners or age 39+.", "marketplace_plans_available": true, "plan_count": 3, "recommended_plan": {"id": "29341OH0100024", "name": "Bronze Classic 4700 (Select)", "issuer": "Oscar Health Insurance", "premium": 365.41, "metal_level": "Bronze", "type": "HMO", "deductible": 4700, "max_out_of_pocket": 9200, "hsa_eligible": false, "quality_rating": 3}, "benefits_coverage": {"emergency_room": "50% Coinsurance after deductible", "primary_care": "$60", "specialist_care": "$125", "prescription_drugs": "$3", "mental_health": null, "maternity_care": null, "preventive_care": null}, "top_3_available_plans": "[{'id': '29341OH0100024', 'name': 'Bronze Classic 4700 (Select)', 'issuer': 'Oscar Health Insurance', 'premium': 365.41, 'premium_with_credit': 365.41, 'metal_level': 'Bronze', 'type': 'HMO', 'deductible': 4700, 'max_out_of_pocket': 9200, 'hsa_eligible': False, 'quality_rating': 3, 'has_national_network': False, 'key_benefits': {'specialist_care': '$125', 'emergency_room': '50% Coinsurance after deductible', 'primary_care': '$60', 'prescription_drugs': '$3'}, 'issuer_phone': '1-************', 'urls': {'benefits': 'https://d3ul0st9g52g6o.cloudfront.net/2025/OH/sbc/2025_29341OH010002401.pdf', 'brochure': 'https://www.hioscar.com/asset/plan-brochure-columbus', 'formulary': 'https://www.hioscar.com/search-documents/drug-formularies/', 'network': 'https://www.hioscar.com/search/?networkId=020&year=2025'}}, {'id': '29341OH0100050', 'name': 'Bronze Classic Standard (Select)', 'issuer': 'Oscar Health Insurance', 'premium': 359.23, 'premium_with_credit': 359.23, 'metal_level': 'Bronze', 'type': 'HMO', 'deductible': 7500, 'max_out_of_pocket': 9200, 'hsa_eligible': False, 'quality_rating': 3, 'has_national_network': False, 'key_benefits': {'primary_care': '$50', 'specialist_care': '$100', 'emergency_room': '50% Coinsurance after deductible', 'prescription_drugs': '$25'}, 'issuer_phone': '1-************', 'urls': {'benefits': 'https://d3ul0st9g52g6o.cloudfront.net/2025/OH/sbc/2025_29341OH010005001.pdf', 'brochure': 'https://www.hioscar.com/asset/plan-brochure-columbus', 'formulary': 'https://www.hioscar.com/search-documents/drug-formularies/', 'network': 'https://www.hioscar.com/search/?networkId=020&year=2025'}}, {'id': '29341OH0100002', 'name': 'Bronze Classic PCP Saver (Select)', 'issuer': 'Oscar Health Insurance', 'premium': 364.73, 'premium_with_credit': 364.73, 'metal_level': 'Bronze', 'type': 'HMO', 'deductible': 7750, 'max_out_of_pocket': 9150, 'hsa_eligible': False, 'quality_rating': 3, 'has_national_network': False, 'key_benefits': {'specialist_care': '$90 Copay after deductible', 'emergency_room': '50% Coinsurance after deductible', 'primary_care': '$25', 'prescription_drugs': '$3'}, 'issuer_phone': '1-************', 'urls': {'benefits': 'https://d3ul0st9g52g6o.cloudfront.net/2025/OH/sbc/2025_29341OH010000201.pdf', 'brochure': 'https://www.hioscar.com/asset/plan-brochure-columbus', 'formulary': 'https://www.hioscar.com/search-documents/drug-formularies/', 'network': 'https://www.hioscar.com/search/?networkId=020&year=2025'}}]", "api_processing_status": "success"}, {"employee_id": "EMP_005", "name": "<PERSON>", "age": 37.0, "gender": "Male", "marital_status": "Married", "zipcode": 27601, "city": "Raleigh", "state": "NC", "income_tier": "High (>$100K)", "employment_type": "Full-time", "dept_count": 2, "predicted_plan_type": "PPO", "plan_confidence": 0.8993557691574097, "plan_reason": "High income, excellent health, high risk tolerance with dependents - PPO for family coverage.", "predicted_benefits": ["Dental", "Vision", "Accident", "Term Life", "STD", "LTD", "FSA"], "benefits_confidence": 1.0, "benefits_reason": "Dental: Essential oral health coverage for all employees.; Vision: Eye care coverage for all employees.; Accident: Recommended for active lifestyle and rare travel frequency.; Term Life: Essential protection for 2 dependents.; STD: Income protection for High (>$100K) earners or excellent health status.; LTD: Long-term income protection for High (>$100K) earners or age 37+.", "marketplace_plans_available": true, "plan_count": 3, "recommended_plan": {"id": "11512NC0390031", "name": "Blue Home Bronze Basic | 3 Free PCP | $20 Tier 1 Rx | Integrated | with UNC Health Alliance", "issuer": "Blue Cross and Blue Shield of NC", "premium": 416.55, "metal_level": "Bronze", "type": "EPO", "deductible": 7000, "max_out_of_pocket": 9200, "hsa_eligible": false, "quality_rating": 4}, "benefits_coverage": {"emergency_room": "50% Coinsurance after deductible", "primary_care": "$100", "specialist_care": "$150", "prescription_drugs": "$20", "mental_health": null, "maternity_care": null, "preventive_care": null}, "top_3_available_plans": "[{'id': '11512NC0390031', 'name': 'Blue Home Bronze Basic | 3 Free PCP | $20 Tier 1 Rx | Integrated | with UNC Health Alliance', 'issuer': 'Blue Cross and Blue Shield of NC', 'premium': 416.55, 'premium_with_credit': 416.55, 'metal_level': 'Bronze', 'type': 'EPO', 'deductible': 7000, 'max_out_of_pocket': 9200, 'hsa_eligible': False, 'quality_rating': 4, 'has_national_network': False, 'key_benefits': {'primary_care': '$100', 'specialist_care': '$150', 'emergency_room': '50% Coinsurance after deductible', 'prescription_drugs': '$20'}, 'issuer_phone': '1-************', 'urls': {'benefits': 'https://www.bcbsnc.com/assets/shopper/public/pdf/sbc/Blue_Home_Bronze_Basic_7000_with_UNC_Health_Alliance_2025.pdf', 'brochure': 'https://www.bluecrossnc.com/bhuncplanbrochure_2025', 'formulary': 'https://www.myprime.com/content/dam/prime/memberportal/WebDocs/2025/Formularies/HIM/2025_NC_5T_HealthInsuranceMarketplace.pdf', 'network': 'https://healthnav.bcbsnc.com/?ci=COMMERCIAL&network_id=25'}}, {'id': '11512NC0390034', 'name': 'Blue Home Bronze Standard | with UNC Health Alliance', 'issuer': 'Blue Cross and Blue Shield of NC', 'premium': 427.72, 'premium_with_credit': 427.72, 'metal_level': 'Bronze', 'type': 'EPO', 'deductible': 7500, 'max_out_of_pocket': 9200, 'hsa_eligible': False, 'quality_rating': 4, 'has_national_network': False, 'key_benefits': {'specialist_care': '$100', 'emergency_room': '50% Coinsurance after deductible', 'primary_care': '$50', 'prescription_drugs': '$25'}, 'issuer_phone': '1-************', 'urls': {'benefits': 'https://www.bcbsnc.com/assets/shopper/public/pdf/sbc/Blue_Home_Bronze_Standard_7500_with_UNC_Health_Alliance_2025.pdf', 'brochure': 'https://www.bluecrossnc.com/bhuncplanbrochure_2025', 'formulary': 'https://www.myprime.com/content/dam/prime/memberportal/WebDocs/2025/Formularies/HIM/2025_NC_4T_HealthInsuranceMarketplace.pdf', 'network': 'https://healthnav.bcbsnc.com/?ci=COMMERCIAL&network_id=25'}}, {'id': '54332NC0030030', 'name': 'UHC Bronze Standard (No Referrals)', 'issuer': 'UnitedHealthcare', 'premium': 437.47, 'premium_with_credit': 437.47, 'metal_level': 'Bronze', 'type': 'HMO', 'deductible': 7500, 'max_out_of_pocket': 9200, 'hsa_eligible': False, 'quality_rating': 3, 'has_national_network': False, 'key_benefits': {'prescription_drugs': '$25', 'specialist_care': '$100', 'emergency_room': '50% Coinsurance after deductible', 'primary_care': '$50'}, 'issuer_phone': '1-************', 'urls': {'benefits': 'https://www.uhc.com/ifp/sbc.54332NC0030030-01.en.2025.pdf', 'brochure': 'https://www.uhc.com/ncplanbrochure2025', 'formulary': 'https://www.uhc.com/xncdruglist2025', 'network': 'https://www.uhc.com/xncdocfindoa2025'}}]", "api_processing_status": "success"}, {"employee_id": "EMP_006", "name": "<PERSON>", "age": 62.0, "gender": "Female", "marital_status": "Not Married", "zipcode": 37201, "city": "Nashville", "state": "TN", "income_tier": "Medium ($50K–$100K)", "employment_type": "Part-time", "dept_count": 0, "predicted_plan_type": "PPO", "plan_confidence": 0.9841006994247437, "plan_reason": "Medium income, urban area - EPO for network-based care.", "predicted_benefits": ["Dental", "Hospital Indemnity", "Accident", "Critical Illness", "STD", "Employee Assistance"], "benefits_confidence": 1.0, "benefits_reason": "Dental: Essential oral health coverage for all employees.; Hospital Indemnity: Recommended due to fair health condition, age 62, or chronic conditions.; Critical Illness: Important protection for age 62 with fair health status.; STD: Income protection for Medium ($50K–$100K) earners or fair health status.", "marketplace_plans_available": true, "plan_count": 3, "recommended_plan": {"id": "14002TN0400239", "name": "BlueCross B15E $0 virtual care from Teladoc Health ®", "issuer": "BlueCross BlueShield of Tennessee", "premium": 958.06, "metal_level": "Bronze", "type": "EPO", "deductible": 9200, "max_out_of_pocket": 9200, "hsa_eligible": false, "quality_rating": 4}, "benefits_coverage": {"emergency_room": "$750 Copay with deductible", "primary_care": "No Charge After Deductible", "specialist_care": "No Charge After Deductible", "prescription_drugs": "No Charge After Deductible", "mental_health": null, "maternity_care": null, "preventive_care": null}, "top_3_available_plans": "[{'id': '14002TN0400239', 'name': 'BlueCross B15E $0 virtual care from Teladoc Health ®', 'issuer': 'BlueCross BlueShield of Tennessee', 'premium': 958.06, 'premium_with_credit': 254.06, 'metal_level': 'Bronze', 'type': 'EPO', 'deductible': 9200, 'max_out_of_pocket': 9200, 'hsa_eligible': False, 'quality_rating': 4, 'has_national_network': False, 'key_benefits': {'primary_care': 'No Charge After Deductible', 'specialist_care': 'No Charge After Deductible', 'emergency_room': '$750 Copay with deductible', 'prescription_drugs': 'No Charge After Deductible'}, 'issuer_phone': '1-************', 'urls': {'benefits': 'https://www.bcbst.com/sbc/2025/127600/B15E_SBC.pdf', 'brochure': 'https://www.bcbst.com/sbc/2025/pb/Network-E.pdf', 'formulary': 'https://www.bcbst.com/docs/providers/2025-essential-formulary.pdf', 'network': 'https://www.bcbst.com/network-e'}}, {'id': '14002TN0400253', 'name': 'BlueCross B16E $50 PCP Copay + $0 virtual care from Teladoc Health ®', 'issuer': 'BlueCross BlueShield of Tennessee', 'premium': 985.09, 'premium_with_credit': 281.09, 'metal_level': 'Bronze', 'type': 'EPO', 'deductible': 7500, 'max_out_of_pocket': 9200, 'hsa_eligible': False, 'quality_rating': 4, 'has_national_network': False, 'key_benefits': {'primary_care': '$50', 'emergency_room': '50% Coinsurance after deductible', 'specialist_care': '$100', 'prescription_drugs': '$25'}, 'issuer_phone': '1-************', 'urls': {'benefits': 'https://www.bcbst.com/sbc/2025/127600/B16E_SBC.pdf', 'brochure': 'https://www.bcbst.com/sbc/2025/pb/Network-E.pdf', 'formulary': 'https://www.bcbst.com/docs/providers/2025-essential-formulary.pdf', 'network': 'https://www.bcbst.com/network-e'}}, {'id': '14002TN0400313', 'name': 'BlueCross B17E $0 virtual care from Teladoc Health ® + Adult Dental', 'issuer': 'BlueCross BlueShield of Tennessee', 'premium': 988.08, 'premium_with_credit': 284.08, 'metal_level': 'Bronze', 'type': 'EPO', 'deductible': 9200, 'max_out_of_pocket': 9200, 'hsa_eligible': False, 'quality_rating': 4, 'has_national_network': False, 'key_benefits': {'prescription_drugs': 'No Charge After Deductible', 'specialist_care': 'No Charge After Deductible', 'emergency_room': '$750 Copay with deductible', 'primary_care': 'No Charge After Deductible'}, 'issuer_phone': '1-************', 'urls': {'benefits': 'https://www.bcbst.com/sbc/2025/127600/B17E_SBC.pdf', 'brochure': 'https://www.bcbst.com/sbc/2025/pb/Network-E.pdf', 'formulary': 'https://www.bcbst.com/docs/providers/2025-essential-formulary.pdf', 'network': 'https://www.bcbst.com/network-e'}}]", "api_processing_status": "success"}, {"employee_id": "EMP_007", "name": "<PERSON>", "age": 60.0, "gender": "Male", "marital_status": "Married", "zipcode": 35201, "city": "Birmingham", "state": "AL", "income_tier": "Medium ($50K–$100K)", "employment_type": "Full-time", "dept_count": 1, "predicted_plan_type": "HDHP + HSA", "plan_confidence": 0.7882767915725708, "plan_reason": "Medium income, good health - PPO for balanced cost and flexibility.", "predicted_benefits": ["Dental", "Vision"], "benefits_confidence": 1.0, "benefits_reason": "Dental: Essential oral health coverage for all employees.; Vision: Eye care coverage for all employees.", "marketplace_plans_available": true, "plan_count": 3, "recommended_plan": {"id": "46944AL0460001", "name": "Blue Saver Bronze", "issuer": "Blue Cross and Blue Shield of Alabama", "premium": 842.1, "metal_level": "Bronze", "type": "PPO", "deductible": 8500, "max_out_of_pocket": 9200, "hsa_eligible": false, "quality_rating": 3}, "benefits_coverage": {"emergency_room": "No Charge After Deductible", "primary_care": "$45", "specialist_care": "$90", "prescription_drugs": "$5", "mental_health": null, "maternity_care": null, "preventive_care": null}, "top_3_available_plans": "[{'id': '46944AL0460001', 'name': 'Blue Saver Bronze', 'issuer': 'Blue Cross and Blue Shield of Alabama', 'premium': 842.1, 'premium_with_credit': 206.1, 'metal_level': 'Bronze', 'type': 'PPO', 'deductible': 8500, 'max_out_of_pocket': 9200, 'hsa_eligible': False, 'quality_rating': 3, 'has_national_network': False, 'key_benefits': {'prescription_drugs': '$5', 'specialist_care': '$90', 'emergency_room': 'No Charge After Deductible', 'primary_care': '$45'}, 'issuer_phone': '1-************', 'urls': {'benefits': 'https://www.alabamablue.com/sb/2025sbi.pdf', 'brochure': 'https://www.alabamablue.com/pb/2025individual.pdf', 'formulary': 'https://www.myprime.com/content/dam/prime/memberportal/WebDocs/2025/Formularies/HIM/2025_AL_6T_Source+Rx_1.0.pdf', 'network': 'https://www.bcbsal.org/web/provider-finder'}}, {'id': '46944AL0750001', 'name': 'Blue Standardized Bronze', 'issuer': 'Blue Cross and Blue Shield of Alabama', 'premium': 845.85, 'premium_with_credit': 209.85, 'metal_level': 'Bronze', 'type': 'PPO', 'deductible': 7500, 'max_out_of_pocket': 9200, 'hsa_eligible': False, 'quality_rating': 3, 'has_national_network': False, 'key_benefits': {'prescription_drugs': '$25', 'specialist_care': '$100', 'emergency_room': '50% Coinsurance after deductible', 'primary_care': '$50'}, 'issuer_phone': '1-************', 'urls': {'benefits': 'https://www.alabamablue.com/sb/2025stb.pdf', 'brochure': 'https://www.alabamablue.com/pb/2025individual.pdf', 'formulary': 'https://www.myprime.com/content/dam/prime/memberportal/WebDocs/2025/Formularies/HIM/2025_AL_4T_Standardized_Source+Rx_1.0.pdf', 'network': 'https://www.bcbsal.org/web/provider-finder'}}, {'id': '69461AL0110018', 'name': 'UHC Bronze Standard (No Referrals)', 'issuer': 'UnitedHealthcare', 'premium': 849.59, 'premium_with_credit': 213.59, 'metal_level': 'Bronze', 'type': 'EPO', 'deductible': 7500, 'max_out_of_pocket': 9200, 'hsa_eligible': False, 'quality_rating': 2, 'has_national_network': False, 'key_benefits': {'primary_care': '$50', 'specialist_care': '$100', 'emergency_room': '50% Coinsurance after deductible', 'prescription_drugs': '$25'}, 'issuer_phone': '1-************', 'urls': {'benefits': 'https://www.uhc.com/ifp/sbc.69461AL0110018-01.en.2025.pdf', 'brochure': 'https://www.uhc.com/alplanbrochure2025', 'formulary': 'https://www.uhc.com/xaldruglist2025', 'network': 'https://www.uhc.com/xaldocfindoa2025'}}]", "api_processing_status": "success"}, {"employee_id": "EMP_008", "name": "<PERSON>", "age": 30.0, "gender": "Female", "marital_status": "Not Married", "zipcode": 73101, "city": "Oklahoma City", "state": "OK", "income_tier": "Medium ($50K–$100K)", "employment_type": "Full-time", "dept_count": 0, "predicted_plan_type": "POS", "plan_confidence": 0.8595268130302429, "plan_reason": "Middle age with dependents - PPO for family coverage.", "predicted_benefits": [], "benefits_confidence": 0.0, "benefits_reason": "ML model prediction", "marketplace_plans_available": true, "plan_count": 3, "recommended_plan": {"id": "62505OK0120019", "name": "Standard Expanded Bronze", "issuer": "Ambetter of Oklahoma", "premium": 306.34, "metal_level": "Bronze", "type": "PPO", "deductible": 7500, "max_out_of_pocket": 9200, "hsa_eligible": false, "quality_rating": 2}, "benefits_coverage": {"emergency_room": "50% Coinsurance after deductible", "primary_care": "$50", "specialist_care": "$100", "prescription_drugs": "$25", "mental_health": null, "maternity_care": null, "preventive_care": null}, "top_3_available_plans": "[{'id': '62505OK0120019', 'name': 'Standard Expanded Bronze', 'issuer': 'Ambetter of Oklahoma', 'premium': 306.34, 'premium_with_credit': 306.34, 'metal_level': 'Bronze', 'type': 'PPO', 'deductible': 7500, 'max_out_of_pocket': 9200, 'hsa_eligible': False, 'quality_rating': 2, 'has_national_network': False, 'key_benefits': {'prescription_drugs': '$25', 'primary_care': '$50', 'specialist_care': '$100', 'emergency_room': '50% Coinsurance after deductible'}, 'issuer_phone': '**************', 'urls': {'benefits': 'https://api.centene.com/SBC/2025/62505OK0120019-01.pdf', 'brochure': 'https://api.centene.com/brochures/2025/62505PremierOK.pdf', 'formulary': 'https://www.ambetterofoklahoma.com/resources/pharmacy-resources.html', 'network': 'https://www.ambetterofoklahoma.com/findadoc'}}, {'id': '62505OK0120002', 'name': 'Everyday Bronze', 'issuer': 'Ambetter of Oklahoma', 'premium': 311.97, 'premium_with_credit': 311.97, 'metal_level': 'Bronze', 'type': 'PPO', 'deductible': 8450, 'max_out_of_pocket': 9200, 'hsa_eligible': False, 'quality_rating': 2, 'has_national_network': False, 'key_benefits': {'primary_care': '$40', 'emergency_room': '50% Coinsurance after deductible', 'specialist_care': '$90', 'prescription_drugs': '$3'}, 'issuer_phone': '**************', 'urls': {'benefits': 'https://api.centene.com/SBC/2025/62505OK0120002-01.pdf', 'brochure': 'https://api.centene.com/brochures/2025/62505PremierOK.pdf', 'formulary': 'https://www.ambetterofoklahoma.com/resources/pharmacy-resources.html', 'network': 'https://www.ambetterofoklahoma.com/findadoc'}}, {'id': '21333OK0060073', 'name': 'Balance by Medica Expanded Bronze Standard', 'issuer': 'Medica', 'premium': 319.16, 'premium_with_credit': 319.16, 'metal_level': 'Bronze', 'type': 'PPO', 'deductible': 7500, 'max_out_of_pocket': 9200, 'hsa_eligible': False, 'quality_rating': 2, 'has_national_network': False, 'key_benefits': {'prescription_drugs': '$25', 'primary_care': '$50', 'specialist_care': '$100', 'emergency_room': '50% Coinsurance after deductible'}, 'issuer_phone': '**************', 'urls': {'benefits': 'https://portal.medica.com/visitor/sbcsearch/docdisplay?plancode=2025-IFBBLBESTOK&uid=FFM.pdf', 'brochure': 'https://www.Medica.com/OKPlans-2025', 'formulary': 'https://www.Medica.com/OKDrugList-2025', 'network': 'https://www.Medica.com/SearchBalanceNetwork-2025'}}]", "api_processing_status": "success"}, {"employee_id": "EMP_009", "name": "<PERSON>", "age": 37.0, "gender": "Male", "marital_status": "Married", "zipcode": 64101, "city": "Kansas City", "state": "MO", "income_tier": "High (>$100K)", "employment_type": "Full-time", "dept_count": 2, "predicted_plan_type": "HDHP + HSA", "plan_confidence": 0.7240247130393982, "plan_reason": "High income, excellent health, high risk tolerance with dependents - PPO for family coverage.", "predicted_benefits": ["Dental", "Vision", "Accident", "Term Life", "STD", "LTD", "Wellness Programs"], "benefits_confidence": 1.0, "benefits_reason": "Dental: Essential oral health coverage for all employees.; Vision: Eye care coverage for all employees.; Term Life: Essential protection for 2 dependents.; STD: Income protection for High (>$100K) earners or excellent health status.; LTD: Long-term income protection for High (>$100K) earners or age 37+.", "marketplace_plans_available": true, "plan_count": 3, "recommended_plan": {"id": "99723MO0090069", "name": "Standard Expanded Bronze", "issuer": "Am<PERSON><PERSON> from Home State Health", "premium": 354.97, "metal_level": "Bronze", "type": "EPO", "deductible": 7500, "max_out_of_pocket": 9200, "hsa_eligible": false, "quality_rating": 3}, "benefits_coverage": {"emergency_room": "50% Coinsurance after deductible", "primary_care": "$50", "specialist_care": "$100", "prescription_drugs": "$25", "mental_health": null, "maternity_care": null, "preventive_care": null}, "top_3_available_plans": "[{'id': '99723MO0090069', 'name': 'Standard Expanded Bronze', 'issuer': 'Ambetter from Home State Health', 'premium': 354.97, 'premium_with_credit': 354.97, 'metal_level': 'Bronze', 'type': 'EPO', 'deductible': 7500, 'max_out_of_pocket': 9200, 'hsa_eligible': False, 'quality_rating': 3, 'has_national_network': False, 'key_benefits': {'primary_care': '$50', 'specialist_care': '$100', 'emergency_room': '50% Coinsurance after deductible', 'prescription_drugs': '$25'}, 'issuer_phone': '1-************', 'urls': {'benefits': 'https://api.centene.com/SBC/2025/99723MO0090069-01.pdf', 'brochure': 'https://api.centene.com/brochures/2025/99723PremierMO.pdf', 'formulary': 'https://ambetter.homestatehealth.com/resources/pharmacy-resources.html', 'network': 'https://ambetter.homestatehealth.com/findadoc'}}, {'id': '99723MO0090018', 'name': 'Everyday Bronze', 'issuer': 'Ambetter from Home State Health', 'premium': 359.9, 'premium_with_credit': 359.9, 'metal_level': 'Bronze', 'type': 'EPO', 'deductible': 8450, 'max_out_of_pocket': 9200, 'hsa_eligible': False, 'quality_rating': 3, 'has_national_network': False, 'key_benefits': {'prescription_drugs': '$3', 'specialist_care': '$90', 'emergency_room': '50% Coinsurance after deductible', 'primary_care': '$40'}, 'issuer_phone': '1-************', 'urls': {'benefits': 'https://api.centene.com/SBC/2025/99723MO0090018-01.pdf', 'brochure': 'https://api.centene.com/brochures/2025/99723PremierMO.pdf', 'formulary': 'https://ambetter.homestatehealth.com/resources/pharmacy-resources.html', 'network': 'https://ambetter.homestatehealth.com/findadoc'}}, {'id': '99723MO0110070', 'name': 'Standard Expanded Bronze + Vision + Adult Dental', 'issuer': 'Ambetter from Home State Health', 'premium': 367.02, 'premium_with_credit': 367.02, 'metal_level': 'Bronze', 'type': 'EPO', 'deductible': 7500, 'max_out_of_pocket': 9200, 'hsa_eligible': False, 'quality_rating': 3, 'has_national_network': False, 'key_benefits': {'primary_care': '$50', 'specialist_care': '$100', 'emergency_room': '50% Coinsurance after deductible', 'prescription_drugs': '$25'}, 'issuer_phone': '1-************', 'urls': {'benefits': 'https://api.centene.com/SBC/2025/99723MO0110070-01.pdf', 'brochure': 'https://api.centene.com/brochures/2025/99723PremierMO.pdf', 'formulary': 'https://ambetter.homestatehealth.com/resources/pharmacy-resources.html', 'network': 'https://ambetter.homestatehealth.com/findadoc'}}]", "api_processing_status": "success"}, {"employee_id": "EMP_010", "name": "<PERSON>", "age": 37.0, "gender": "Female", "marital_status": "Not Married", "zipcode": 68101, "city": "Omaha", "state": "NE", "income_tier": "High (>$100K)", "employment_type": "Full-time", "dept_count": 0, "predicted_plan_type": "POS", "plan_confidence": 0.6296622157096863, "plan_reason": "High income, good health, HSA familiar, no chronic conditions - HDHP + HSA for tax benefits.", "predicted_benefits": ["Wellness Programs"], "benefits_confidence": 1.0, "benefits_reason": "ML model prediction", "marketplace_plans_available": true, "plan_count": 3, "recommended_plan": {"id": "26289NE0020039", "name": "Standard Expanded Bronze", "issuer": "<PERSON><PERSON><PERSON> from Nebraska Total Care", "premium": 422, "metal_level": "Bronze", "type": "HMO", "deductible": 7500, "max_out_of_pocket": 9200, "hsa_eligible": false, "quality_rating": 3}, "benefits_coverage": {"emergency_room": "50% Coinsurance after deductible", "primary_care": "$50", "specialist_care": "$100", "prescription_drugs": "$25", "mental_health": null, "maternity_care": null, "preventive_care": null}, "top_3_available_plans": "[{'id': '26289NE0020039', 'name': 'Standard Expanded Bronze', 'issuer': 'Ambetter from Nebraska Total Care', 'premium': 422, 'premium_with_credit': 422, 'metal_level': 'Bronze', 'type': 'HMO', 'deductible': 7500, 'max_out_of_pocket': 9200, 'hsa_eligible': False, 'quality_rating': 3, 'has_national_network': False, 'key_benefits': {'prescription_drugs': '$25', 'specialist_care': '$100', 'emergency_room': '50% Coinsurance after deductible', 'primary_care': '$50'}, 'issuer_phone': '1-************', 'urls': {'benefits': 'https://api.centene.com/SBC/2025/26289NE0020039-01.pdf', 'brochure': 'https://api.centene.com/brochures/2025/26289PremierNE.pdf', 'formulary': 'https://ambetter.nebraskatotalcare.com/resources/pharmacy-resources.html', 'network': 'https://ambetter.nebraskatotalcare.com/findadoc'}}, {'id': '20305NE0030073', 'name': 'Elevate by Medica Expanded Bronze Standard', 'issuer': 'Medica', 'premium': 433.35, 'premium_with_credit': 433.35, 'metal_level': 'Bronze', 'type': 'EPO', 'deductible': 7500, 'max_out_of_pocket': 9200, 'hsa_eligible': False, 'quality_rating': 3, 'has_national_network': False, 'key_benefits': {'prescription_drugs': '$25', 'specialist_care': '$100', 'emergency_room': '50% Coinsurance after deductible', 'primary_care': '$50'}, 'issuer_phone': '**************', 'urls': {'benefits': 'https://portal.medica.com/visitor/sbcsearch/docdisplay?plancode=2025-IFBEMBESTNE&uid=FFM.pdf', 'brochure': 'https://www.Medica.com/NEPlans-2025', 'formulary': 'https://www.Medica.com/NEDrugList-2025', 'network': 'https://www.Medica.com/SearchElevateNetwork-2025'}}, {'id': '26289NE0030015', 'name': 'Standard Expanded Bronze + Vision + Adult Dental', 'issuer': 'Ambetter from Nebraska Total Care', 'premium': 433.9, 'premium_with_credit': 433.9, 'metal_level': 'Bronze', 'type': 'HMO', 'deductible': 7500, 'max_out_of_pocket': 9200, 'hsa_eligible': False, 'quality_rating': 3, 'has_national_network': False, 'key_benefits': {'primary_care': '$50', 'specialist_care': '$100', 'emergency_room': '50% Coinsurance after deductible', 'prescription_drugs': '$25'}, 'issuer_phone': '1-************', 'urls': {'benefits': 'https://api.centene.com/SBC/2025/26289NE0030015-01.pdf', 'brochure': 'https://api.centene.com/brochures/2025/26289PremierNE.pdf', 'formulary': 'https://ambetter.nebraskatotalcare.com/resources/pharmacy-resources.html', 'network': 'https://ambetter.nebraskatotalcare.com/findadoc'}}]", "api_processing_status": "success"}, {"employee_id": "EMP_011", "name": "<PERSON>", "age": 44.0, "gender": "Male", "marital_status": "Married", "zipcode": 84101, "city": "Salt Lake City", "state": "UT", "income_tier": "High (>$100K)", "employment_type": "Full-time", "dept_count": 2, "predicted_plan_type": "POS", "plan_confidence": 0.9502061605453491, "plan_reason": "Middle age with dependents - PPO for family coverage.", "predicted_benefits": ["Dental", "Vision", "Accident", "Critical Illness", "Term Life", "STD", "LTD", "FSA", "Employee Assistance"], "benefits_confidence": 1.0, "benefits_reason": "Dental: Essential oral health coverage for all employees.; Vision: Eye care coverage for all employees.; Critical Illness: Important protection for age 44 with excellent health status.; Term Life: Essential protection for 2 dependents.; STD: Income protection for High (>$100K) earners or excellent health status.; LTD: Long-term income protection for High (>$100K) earners or age 44+.; FSA: Tax-advantaged account for medical expenses due to health conditions or pregnancy.", "marketplace_plans_available": true, "plan_count": 3, "recommended_plan": {"id": "38927UT0380001", "name": "Bronze 2 Advanced HSA: Aetna network of doctors & hospitals", "issuer": "Aetna CVS Health", "premium": 471.07, "metal_level": "Bronze", "type": "HMO", "deductible": 6200, "max_out_of_pocket": 7500, "hsa_eligible": true, "quality_rating": 0}, "benefits_coverage": {"emergency_room": "50% Coinsurance after deductible", "primary_care": "35% Coinsurance after deductible", "specialist_care": "35% Coinsurance after deductible", "prescription_drugs": "$3 Copay after deductible", "mental_health": null, "maternity_care": null, "preventive_care": null}, "top_3_available_plans": "[{'id': '38927UT0380001', 'name': 'Bronze 2 Advanced HSA: Aetna network of doctors & hospitals', 'issuer': 'Aetna CVS Health', 'premium': 471.07, 'premium_with_credit': 471.07, 'metal_level': 'Bronze', 'type': 'HMO', 'deductible': 6200, 'max_out_of_pocket': 7500, 'hsa_eligible': True, 'quality_rating': 0, 'has_national_network': False, 'key_benefits': {'primary_care': '35% Coinsurance after deductible', 'specialist_care': '35% Coinsurance after deductible', 'emergency_room': '50% Coinsurance after deductible', 'prescription_drugs': '$3 Copay after deductible'}, 'issuer_phone': '**************', 'urls': {'benefits': 'https://www.aetnacvshealth.com/documents/2025-IFP-777854_38927UT0380001-01_SBC.pdf', 'brochure': 'https://www.aetnacvshealth.com/documents/IFP-plan-brochure-utah-english.pdf', 'formulary': 'http://aet.na/utivl25', 'network': 'https://www.aetna.com/dsepublic/#/contentPage?page=providerSearchLanding&site_id=aetnaivlexchange'}}, {'id': '42261UT0060024', 'name': 'U Health Plus Bronze', 'issuer': 'University of Utah Health Plans', 'premium': 449.79, 'premium_with_credit': 449.79, 'metal_level': 'Bronze', 'type': 'EPO', 'deductible': 9000, 'max_out_of_pocket': 9200, 'hsa_eligible': False, 'quality_rating': 4, 'has_national_network': False, 'key_benefits': {'prescription_drugs': '$30', 'primary_care': 'No Charge', 'specialist_care': '$80 Copay after deductible', 'emergency_room': '50% Coinsurance after deductible'}, 'issuer_phone': '**************', 'urls': {'benefits': 'https://doc.uhealthplan.utah.edu/individual/2025/sbc/uhealthplus/2871148.pdf', 'brochure': 'https://doc.uhealthplan.utah.edu/individual/2025/plan-brochure/uhealthplus/2871148.pdf', 'formulary': 'https://cbg.adaptiverx.com/webSearch/index?key=8F02B26A288102C27BAC82D14C006C6FC54D480F80409B688983F58AD1B2EBAD', 'network': 'https://uuhip.healthtrioconnect.com/public-app/consumer/provdir/entry.page?xsesschk='}}, {'id': '42261UT0060027', 'name': 'U Health Plus Expanded Bronze Standard', 'issuer': 'University of Utah Health Plans', 'premium': 453.03, 'premium_with_credit': 453.03, 'metal_level': 'Bronze', 'type': 'EPO', 'deductible': 7500, 'max_out_of_pocket': 9200, 'hsa_eligible': False, 'quality_rating': 4, 'has_national_network': False, 'key_benefits': {'prescription_drugs': '$25', 'primary_care': '$50', 'specialist_care': '$100', 'emergency_room': '50% Coinsurance after deductible'}, 'issuer_phone': '**************', 'urls': {'benefits': 'https://doc.uhealthplan.utah.edu/individual/2025/sbc/uhealthplus/2871152.pdf', 'brochure': 'https://doc.uhealthplan.utah.edu/individual/2025/plan-brochure/uhealthplus/2871152.pdf', 'formulary': 'https://cbg.adaptiverx.com/webSearch/index?key=8F02B26A288102C27BAC82D14C006C6FC54D480F80409B688983F58AD1B2EBAD', 'network': 'https://uuhip.healthtrioconnect.com/public-app/consumer/provdir/entry.page?xsesschk='}}]", "api_processing_status": "success"}, {"employee_id": "EMP_012", "name": "<PERSON>", "age": 54.0, "gender": "Female", "marital_status": "Not Married", "zipcode": 85001, "city": "Phoenix", "state": "AZ", "income_tier": "Low (<$50K)", "employment_type": "Full-time", "dept_count": 1, "predicted_plan_type": "HMO", "plan_confidence": 0.8650479912757874, "plan_reason": "Older age - PPO for comprehensive coverage and provider choice.", "predicted_benefits": ["Dental", "Vision"], "benefits_confidence": 0.5, "benefits_reason": "Default benefits assignment due to prediction error", "marketplace_plans_available": true, "plan_count": 3, "recommended_plan": {"id": "91450AZ0080122", "name": "Standard Expanded Bronze", "issuer": "Ambetter from Arizona Complete Health ", "premium": 533.31, "metal_level": "Bronze", "type": "HMO", "deductible": 7500, "max_out_of_pocket": 9200, "hsa_eligible": false, "quality_rating": 4}, "benefits_coverage": {"emergency_room": "50% Coinsurance after deductible", "primary_care": "$50", "specialist_care": "$100", "prescription_drugs": "$25", "mental_health": null, "maternity_care": null, "preventive_care": null}, "top_3_available_plans": "[{'id': '91450AZ0080122', 'name': 'Standard Expanded Bronze', 'issuer': 'Ambetter from Arizona Complete Health ', 'premium': 533.31, 'premium_with_credit': 8.31, 'metal_level': 'Bronze', 'type': 'HMO', 'deductible': 7500, 'max_out_of_pocket': 9200, 'hsa_eligible': False, 'quality_rating': 4, 'has_national_network': False, 'key_benefits': {'prescription_drugs': '$25', 'primary_care': '$50', 'specialist_care': '$100', 'emergency_room': '50% Coinsurance after deductible'}, 'issuer_phone': '**************', 'urls': {'benefits': 'https://api.centene.com/SBC/2025/91450AZ0080122-01.pdf', 'brochure': 'https://api.centene.com/brochures/2025/91450PremierAZ.pdf', 'formulary': 'https://ambetter.azcompletehealth.com/resources/pharmacy-resources.html', 'network': 'https://ambetter.azcompletehealth.com/findadoc'}}, {'id': '40702AZ0060019', 'name': 'UHC Bronze Standard', 'issuer': 'UnitedHealthcare', 'premium': 526.34, 'premium_with_credit': 1.34, 'metal_level': 'Bronze', 'type': 'HMO', 'deductible': 7500, 'max_out_of_pocket': 9200, 'hsa_eligible': False, 'quality_rating': 3, 'has_national_network': False, 'key_benefits': {'prescription_drugs': '$25', 'specialist_care': '$100', 'emergency_room': '50% Coinsurance after deductible', 'primary_care': '$50'}, 'issuer_phone': '**************', 'urls': {'benefits': 'https://www.uhc.com/ifp/sbc.40702AZ0060019-01.en.2025.pdf', 'brochure': 'https://www.uhc.com/azplanbrochure2025', 'formulary': 'https://www.uhc.com/xazdruglist2025', 'network': 'https://www.uhc.com/xazdocfindg2025'}}, {'id': '85533AZ0010001', 'name': 'Imperial Standard Bronze', 'issuer': 'Imperial Insurance Companies, Inc.', 'premium': 522.04, 'premium_with_credit': 0, 'metal_level': 'Bronze', 'type': 'HMO', 'deductible': 7500, 'max_out_of_pocket': 9200, 'hsa_eligible': False, 'quality_rating': 0, 'has_national_network': False, 'key_benefits': {'specialist_care': '$100', 'emergency_room': '50% Coinsurance after deductible', 'primary_care': '$50', 'prescription_drugs': '$25'}, 'issuer_phone': '**************', 'urls': {'benefits': 'https://documents.imperialhealthplan.com/2025/Marketplace/Provider%20Directory/Arizona/Imperial%20Standard%20Bronze_85533AZ0010001-01_CY2025.pdf', 'brochure': 'https://exchange.imperialhealthplan.com/wp-content/uploads/2023/08/Plan-brochure-Arizona.pdf', 'formulary': 'https://client.formularynavigator.com/Search.aspx?siteCode=**********', 'network': 'https://exchange.imperialhealthplan.com/arizona/hmo-exchange/online-provider-directory/'}}]", "api_processing_status": "success"}, {"employee_id": "EMP_013", "name": "<PERSON>", "age": 56.0, "gender": "Male", "marital_status": "Married", "zipcode": 19701, "city": "Bear", "state": "DE", "income_tier": "High (>$100K)", "employment_type": "Full-time", "dept_count": 2, "predicted_plan_type": "POS", "plan_confidence": 0.8335018754005432, "plan_reason": "High income, good health, HSA familiar, no chronic conditions - HDHP + HSA for tax benefits.", "predicted_benefits": ["Dental", "Vision"], "benefits_confidence": 0.5, "benefits_reason": "Default benefits assignment due to prediction error", "marketplace_plans_available": true, "plan_count": 3, "recommended_plan": {"id": "64004DE0090001", "name": "Premier Bronze HSA", "issuer": "Ambetter Health of Delaware", "premium": 884.1, "metal_level": "Bronze", "type": "EPO", "deductible": 5200, "max_out_of_pocket": 8050, "hsa_eligible": true, "quality_rating": 0}, "benefits_coverage": {"emergency_room": "50% Coinsurance after deductible", "primary_care": "$60 Copay after deductible", "specialist_care": "$100 Copay after deductible", "prescription_drugs": "$3 Copay after deductible", "mental_health": null, "maternity_care": null, "preventive_care": null}, "top_3_available_plans": "[{'id': '64004DE0090001', 'name': 'Premier Bronze HSA', 'issuer': 'Ambetter Health of Delaware', 'premium': 884.1, 'premium_with_credit': 759.1, 'metal_level': 'Bronze', 'type': 'EPO', 'deductible': 5200, 'max_out_of_pocket': 8050, 'hsa_eligible': True, 'quality_rating': 0, 'has_national_network': False, 'key_benefits': {'prescription_drugs': '$3 Copay after deductible', 'specialist_care': '$100 Copay after deductible', 'emergency_room': '50% Coinsurance after deductible', 'primary_care': '$60 Copay after deductible'}, 'issuer_phone': '**************', 'urls': {'benefits': 'https://api.centene.com/SBC/2025/64004DE0090001-01.pdf', 'brochure': 'https://api.centene.com/brochures/2025/64004PremierDE.pdf', 'formulary': 'https://www.ambetterhealthofdelaware.com/resources/pharmacy-resources.html', 'network': 'https://www.ambetterhealthofdelaware.com/findadoc'}}, {'id': '64004DE0090004', 'name': 'Standard Expanded Bronze', 'issuer': 'Ambetter Health of Delaware', 'premium': 864.41, 'premium_with_credit': 739.41, 'metal_level': 'Bronze', 'type': 'EPO', 'deductible': 7500, 'max_out_of_pocket': 9200, 'hsa_eligible': False, 'quality_rating': 0, 'has_national_network': False, 'key_benefits': {'prescription_drugs': '$25', 'specialist_care': '$100', 'emergency_room': '50% Coinsurance after deductible', 'primary_care': '$50'}, 'issuer_phone': '**************', 'urls': {'benefits': 'https://api.centene.com/SBC/2025/64004DE0090004-01.pdf', 'brochure': 'https://api.centene.com/brochures/2025/64004PremierDE.pdf', 'formulary': 'https://www.ambetterhealthofdelaware.com/resources/pharmacy-resources.html', 'network': 'https://www.ambetterhealthofdelaware.com/findadoc'}}, {'id': '64004DE0090002', 'name': 'Everyday Bronze', 'issuer': 'Ambetter Health of Delaware', 'premium': 869.94, 'premium_with_credit': 744.94, 'metal_level': 'Bronze', 'type': 'EPO', 'deductible': 8450, 'max_out_of_pocket': 9200, 'hsa_eligible': False, 'quality_rating': 0, 'has_national_network': False, 'key_benefits': {'prescription_drugs': '$3', 'primary_care': '$40', 'specialist_care': '$90', 'emergency_room': '50% Coinsurance after deductible'}, 'issuer_phone': '**************', 'urls': {'benefits': 'https://api.centene.com/SBC/2025/64004DE0090002-01.pdf', 'brochure': 'https://api.centene.com/brochures/2025/64004PremierDE.pdf', 'formulary': 'https://www.ambetterhealthofdelaware.com/resources/pharmacy-resources.html', 'network': 'https://www.ambetterhealthofdelaware.com/findadoc'}}]", "api_processing_status": "success"}, {"employee_id": "EMP_014", "name": "<PERSON>", "age": 51.0, "gender": "Female", "marital_status": "Not Married", "zipcode": 59718, "city": "Bozeman", "state": "MT", "income_tier": "Medium ($50K–$100K)", "employment_type": "Full-time", "dept_count": 0, "predicted_plan_type": "POS", "plan_confidence": 0.7254355549812317, "plan_reason": "Older age - PPO for comprehensive coverage and provider choice.", "predicted_benefits": ["Dental", "Vision"], "benefits_confidence": 0.5, "benefits_reason": "Default benefits assignment due to prediction error", "marketplace_plans_available": true, "plan_count": 3, "recommended_plan": {"id": "30751MT0670012", "name": "Blue Focus Bronze POS℠ 205", "issuer": "Blue Cross and Blue Shield of Montana", "premium": 564.39, "metal_level": "Bronze", "type": "POS", "deductible": 4900, "max_out_of_pocket": 9200, "hsa_eligible": false, "quality_rating": 0}, "benefits_coverage": {"emergency_room": "$1000 Copay with deductible/50% Coinsurance after deductible", "primary_care": "$45", "specialist_care": "50% Coinsurance after deductible", "prescription_drugs": "No Charge After Deductible", "mental_health": null, "maternity_care": null, "preventive_care": null}, "top_3_available_plans": "[{'id': '30751MT0670012', 'name': 'Blue Focus Bronze POS℠ 205', 'issuer': 'Blue Cross and Blue Shield of Montana', 'premium': 564.39, 'premium_with_credit': 272.39, 'metal_level': 'Bronze', 'type': 'POS', 'deductible': 4900, 'max_out_of_pocket': 9200, 'hsa_eligible': False, 'quality_rating': 0, 'has_national_network': False, 'key_benefits': {'primary_care': '$45', 'specialist_care': '50% Coinsurance after deductible', 'emergency_room': '$1000 Copay with deductible/50% Coinsurance after deductible', 'prescription_drugs': 'No Charge After Deductible'}, 'issuer_phone': '1-************', 'urls': {'benefits': 'https://www.bcbsmt.com/sbc/ind/sbc-bosh31blcimtp-mt-2025.pdf', 'brochure': 'https://www.bcbsmt.com/plan-docs/ind/brochure-mt-2025.pdf', 'formulary': 'https://www.myprime.com/content/dam/prime/memberportal/WebDocs/2025/Formularies/HIM/2025_MT_6T_HIM.pdf', 'network': 'https://my.providerfinderonline.com/?ci=mt-bluefocuspos-retail&corp_code=MT'}}, {'id': '30751MT0670016', 'name': 'Blue Focus Bronze POS℠ 705', 'issuer': 'Blue Cross and Blue Shield of Montana', 'premium': 538.59, 'premium_with_credit': 246.59, 'metal_level': 'Bronze', 'type': 'POS', 'deductible': 9200, 'max_out_of_pocket': 9200, 'hsa_eligible': False, 'quality_rating': 0, 'has_national_network': False, 'key_benefits': {'prescription_drugs': 'No Charge After Deductible', 'primary_care': 'No Charge After Deductible', 'specialist_care': 'No Charge After Deductible', 'emergency_room': 'No Charge After Deductible'}, 'issuer_phone': '1-************', 'urls': {'benefits': 'https://www.bcbsmt.com/sbc/ind/sbc-bosa61blcimtp-mt-2025.pdf', 'brochure': 'https://www.bcbsmt.com/plan-docs/ind/brochure-mt-2025.pdf', 'formulary': 'https://www.myprime.com/content/dam/prime/memberportal/WebDocs/2025/Formularies/HIM/2025_MT_6T_HIM.pdf', 'network': 'https://my.providerfinderonline.com/?ci=mt-bluefocuspos-retail&corp_code=MT'}}, {'id': '30751MT0670018', 'name': 'Blue Focus Bronze POS℠ Standard', 'issuer': 'Blue Cross and Blue Shield of Montana', 'premium': 576.1, 'premium_with_credit': 284.1, 'metal_level': 'Bronze', 'type': 'POS', 'deductible': 7500, 'max_out_of_pocket': 9200, 'hsa_eligible': False, 'quality_rating': 0, 'has_national_network': False, 'key_benefits': {'prescription_drugs': '$25', 'primary_care': '$50', 'specialist_care': '$100', 'emergency_room': '50% Coinsurance after deductible'}, 'issuer_phone': '1-************', 'urls': {'benefits': 'https://www.bcbsmt.com/sbc/ind/sbc-bosa86blcimtp-mt-2025.pdf', 'brochure': 'https://www.bcbsmt.com/plan-docs/ind/brochure-mt-2025.pdf', 'formulary': 'https://www.myprime.com/content/dam/prime/memberportal/WebDocs/2025/Formularies/HIM/2025_MT_4T_HIM.pdf', 'network': 'https://my.providerfinderonline.com/?ci=mt-bluefocuspos-retail&corp_code=MT'}}]", "api_processing_status": "success"}, {"employee_id": "EMP_015", "name": "<PERSON>", "age": 63.0, "gender": "Male", "marital_status": "Married", "zipcode": 82414, "city": "<PERSON>", "state": "WY", "income_tier": "Medium ($50K–$100K)", "employment_type": "Full-time", "dept_count": 1, "predicted_plan_type": "PPO", "plan_confidence": 0.63481605052948, "plan_reason": "Medium income, good health - PPO for balanced cost and flexibility.", "predicted_benefits": ["Dental", "Vision"], "benefits_confidence": 0.5, "benefits_reason": "Default benefits assignment due to prediction error", "marketplace_plans_available": true, "plan_count": 1, "recommended_plan": {"id": "11269WY0070026", "name": "BlueSelect Bronze Basic", "issuer": "Blue Cross Blue Shield of Wyoming", "premium": 1544.05, "metal_level": "Bronze", "type": "PPO", "deductible": 9100, "max_out_of_pocket": 9100, "hsa_eligible": false, "quality_rating": 2}, "benefits_coverage": {"emergency_room": "No Charge After Deductible", "primary_care": "No Charge After Deductible", "specialist_care": "No Charge After Deductible", "prescription_drugs": "No Charge After Deductible", "mental_health": null, "maternity_care": null, "preventive_care": null}, "top_3_available_plans": "[{'id': '11269WY0070026', 'name': 'BlueSelect Bronze Basic', 'issuer': 'Blue Cross Blue Shield of Wyoming', 'premium': 1544.05, 'premium_with_credit': 37.05, 'metal_level': 'Bronze', 'type': 'PPO', 'deductible': 9100, 'max_out_of_pocket': 9100, 'hsa_eligible': False, 'quality_rating': 2, 'has_national_network': False, 'key_benefits': {'specialist_care': 'No Charge After Deductible', 'emergency_room': 'No Charge After Deductible', 'primary_care': 'No Charge After Deductible', 'prescription_drugs': 'No Charge After Deductible'}, 'issuer_phone': '1-************', 'urls': {'benefits': 'https://shop.yourwyoblue.com/content/sbcs/2025/WY/Individual/BlueSelectBronzeBasicIX.pdf', 'brochure': 'https://bcbswy.com/files/2025-BlueSelect-Ind-Fam.pdf', 'formulary': 'https://www.myprime.com/content/dam/prime/memberportal/WebDocs/2025/Formularies/HIM/2025_WY_4T_HealthInsuranceMarketplaceBlueSelect.pdf', 'network': 'https://bcbswy.sapphirecareselect.com'}}]", "api_processing_status": "success"}, {"employee_id": "EMP_016", "name": "<PERSON>", "age": 49.0, "gender": "Female", "marital_status": "Not Married", "zipcode": 46201, "city": "Indianapolis", "state": "IN", "income_tier": "Medium ($50K–$100K)", "employment_type": "Full-time", "dept_count": 0, "predicted_plan_type": "PPO", "plan_confidence": 0.9640781879425049, "plan_reason": "Medium income, good health - PPO for balanced cost and flexibility.", "predicted_benefits": ["Dental", "Vision"], "benefits_confidence": 0.5, "benefits_reason": "Default benefits assignment due to prediction error", "marketplace_plans_available": true, "plan_count": 3, "recommended_plan": {"id": "17575IN0700045", "name": "Anthem Bronze Essential 9200 (+ Incentives)", "issuer": "Anthem Blue Cross and Blue Shield", "premium": 440.18, "metal_level": "Bronze", "type": "HMO", "deductible": 9200, "max_out_of_pocket": 9200, "hsa_eligible": false, "quality_rating": 3}, "benefits_coverage": {"emergency_room": "No Charge After Deductible", "primary_care": "No Charge After Deductible", "specialist_care": "No Charge After Deductible", "prescription_drugs": "No Charge After Deductible", "mental_health": null, "maternity_care": null, "preventive_care": null}, "top_3_available_plans": "[{'id': '17575IN0700045', 'name': 'Anthem Bronze Essential 9200 (+ Incentives)', 'issuer': 'Anthem Blue Cross and Blue Shield', 'premium': 440.18, 'premium_with_credit': 440.18, 'metal_level': 'Bronze', 'type': 'HMO', 'deductible': 9200, 'max_out_of_pocket': 9200, 'hsa_eligible': False, 'quality_rating': 3, 'has_national_network': False, 'key_benefits': {'primary_care': 'No Charge After Deductible', 'specialist_care': 'No Charge After Deductible', 'emergency_room': 'No Charge After Deductible', 'prescription_drugs': 'No Charge After Deductible'}, 'issuer_phone': '1-************', 'urls': {'benefits': 'https://sbc.anthem.com/dpsdeeplink/deepLink/AnthemBronzeEssential9200Incentives/864B/English/DG166705364851.pdf', 'brochure': 'http://anthem.ly/IND-IN-EN-KIT-2025', 'formulary': 'https://www.anthem.com/INSelectdrugtier4', 'network': 'https://www.anthem.com/find-care/?alphaprefix=E7E'}}, {'id': '17575IN0700101', 'name': 'Anthem Bronze Essential 9200 Adult Dental/Vision (+ Incentives)', 'issuer': 'Anthem Blue Cross and Blue Shield', 'premium': 451.56, 'premium_with_credit': 451.56, 'metal_level': 'Bronze', 'type': 'HMO', 'deductible': 9200, 'max_out_of_pocket': 9200, 'hsa_eligible': False, 'quality_rating': 3, 'has_national_network': False, 'key_benefits': {'prescription_drugs': 'No Charge After Deductible', 'primary_care': 'No Charge After Deductible', 'specialist_care': 'No Charge After Deductible', 'emergency_room': 'No Charge After Deductible'}, 'issuer_phone': '1-************', 'urls': {'benefits': 'https://sbc.anthem.com/dpsdeeplink/deepLink/AnthemBronzeEssential9200AdultDentalVisionIncentives/864L/English/DG166705365415.pdf', 'brochure': 'http://anthem.ly/IND-IN-EN-KIT-2025', 'formulary': 'https://www.anthem.com/INSelectdrugtier4', 'network': 'https://www.anthem.com/find-care/?alphaprefix=E7E'}}, {'id': '17575IN0760005', 'name': 'Anthem Bronze Essential POS 5500 ($0 Virtual PCP + $0 Select Drugs + Incentives)', 'issuer': 'Anthem Blue Cross and Blue Shield', 'premium': 454.24, 'premium_with_credit': 454.24, 'metal_level': 'Bronze', 'type': 'POS', 'deductible': 5500, 'max_out_of_pocket': 9200, 'hsa_eligible': False, 'quality_rating': 2, 'has_national_network': False, 'key_benefits': {'specialist_care': '40% Coinsurance after deductible', 'emergency_room': '$500 Copay after deductible/40% Coinsurance after deductible', 'primary_care': '$50/40% Coinsurance after deductible', 'prescription_drugs': '40% Coinsurance after deductible'}, 'issuer_phone': '1-************', 'urls': {'benefits': 'https://sbc.anthem.com/dpsdeeplink/deepLink/AnthemBronzeEssentialPOS55000VirtualPCP0SelectDrugsIncentives/86BW/English/DG166705364852.pdf', 'brochure': 'http://anthem.ly/IND-IN-EN-KIT-2025', 'formulary': 'https://www.anthem.com/INSelectdrugtier4', 'network': 'https://www.anthem.com/find-care/?alphaprefix=E7E'}}]", "api_processing_status": "success"}, {"employee_id": "EMP_017", "name": "<PERSON>", "age": 39.0, "gender": "Male", "marital_status": "Married", "zipcode": 48201, "city": "Detroit", "state": "MI", "income_tier": "Low (<$50K)", "employment_type": "Full-time", "dept_count": 2, "predicted_plan_type": "PPO", "plan_confidence": 0.8825032114982605, "plan_reason": "Middle age with dependents - PPO for family coverage.", "predicted_benefits": ["Dental", "Vision"], "benefits_confidence": 0.5, "benefits_reason": "Default benefits assignment due to prediction error", "marketplace_plans_available": true, "plan_count": 3, "recommended_plan": {"id": "98185MI0180032", "name": "Blue Cross® Local HMO Bronze Secure", "issuer": "Blue Care Network of Michigan", "premium": 281.69, "metal_level": "Bronze", "type": "HMO", "deductible": 9200, "max_out_of_pocket": 9200, "hsa_eligible": false, "quality_rating": 4}, "benefits_coverage": {"emergency_room": "No Charge After Deductible", "primary_care": "No Charge After Deductible", "specialist_care": "No Charge After Deductible", "prescription_drugs": "No Charge After Deductible", "mental_health": null, "maternity_care": null, "preventive_care": null}, "top_3_available_plans": "[{'id': '98185MI0180032', 'name': 'Blue Cross® Local HMO Bronze Secure', 'issuer': 'Blue Care Network of Michigan', 'premium': 281.69, 'premium_with_credit': 21.69, 'metal_level': 'Bronze', 'type': 'HMO', 'deductible': 9200, 'max_out_of_pocket': 9200, 'hsa_eligible': False, 'quality_rating': 4, 'has_national_network': False, 'key_benefits': {'prescription_drugs': 'No Charge After Deductible', 'primary_care': 'No Charge After Deductible', 'specialist_care': 'No Charge After Deductible', 'emergency_room': 'No Charge After Deductible'}, 'issuer_phone': '1-************', 'urls': {'benefits': 'https://www.bcbsm.com/amslibs/content/dam/public/marketplace/2025-individual/sbc/local-bronze-secure-sbc.pdf', 'brochure': 'https://www.bcbsm.com/individuals/plans/medical/2025/bronze/hmo-secure/?network=local', 'formulary': 'https://www.bcbsm.com/2025-select-hmo-druglist', 'network': 'https://www.bcbsm.com/marketplace/local-hmo/'}}, {'id': '58594MI0030023', 'name': 'Standard Expanded Bronze', 'issuer': 'Ambetter from Meridian', 'premium': 283.11, 'premium_with_credit': 23.11, 'metal_level': 'Bronze', 'type': 'HMO', 'deductible': 7500, 'max_out_of_pocket': 9200, 'hsa_eligible': False, 'quality_rating': 3, 'has_national_network': False, 'key_benefits': {'primary_care': '$50', 'specialist_care': '$100', 'emergency_room': '50% Coinsurance after deductible', 'prescription_drugs': '$25'}, 'issuer_phone': '**************', 'urls': {'benefits': 'https://api.centene.com/SBC/2025/58594MI0030023-01.pdf', 'brochure': 'https://api.centene.com/brochures/2025/58594PremierMI.pdf', 'formulary': 'https://www.ambettermeridian.com/resources/pharmacy-resources.html', 'network': 'https://www.ambettermeridian.com/findadoc'}}, {'id': '58594MI0030013', 'name': 'Everyday Bronze', 'issuer': 'Ambetter from Meridian', 'premium': 288, 'premium_with_credit': 28, 'metal_level': 'Bronze', 'type': 'HMO', 'deductible': 8450, 'max_out_of_pocket': 9200, 'hsa_eligible': False, 'quality_rating': 3, 'has_national_network': False, 'key_benefits': {'prescription_drugs': '$3', 'primary_care': '$40', 'emergency_room': '50% Coinsurance after deductible', 'specialist_care': '$90'}, 'issuer_phone': '**************', 'urls': {'benefits': 'https://api.centene.com/SBC/2025/58594MI0030013-01.pdf', 'brochure': 'https://api.centene.com/brochures/2025/58594PremierMI.pdf', 'formulary': 'https://www.ambettermeridian.com/resources/pharmacy-resources.html', 'network': 'https://www.ambettermeridian.com/findadoc'}}]", "api_processing_status": "success"}, {"employee_id": "EMP_018", "name": "<PERSON>", "age": 45.0, "gender": "Female", "marital_status": "Not Married", "zipcode": 39201, "city": "<PERSON>", "state": "MS", "income_tier": "High (>$100K)", "employment_type": "Part-time", "dept_count": 0, "predicted_plan_type": "HDHP + HSA", "plan_confidence": 0.9690537452697754, "plan_reason": "Middle age with dependents - PPO for family coverage.", "predicted_benefits": ["Dental", "Vision"], "benefits_confidence": 0.5, "benefits_reason": "Default benefits assignment due to prediction error", "marketplace_plans_available": true, "plan_count": 1, "recommended_plan": {"id": "14624MS0010006", "name": "Savings Bronze 7700", "issuer": "Primewell Health Services of Mississippi", "premium": 503.7, "metal_level": "Bronze", "type": "POS", "deductible": 7700, "max_out_of_pocket": 7700, "hsa_eligible": true, "quality_rating": 3}, "benefits_coverage": {"emergency_room": "No Charge After Deductible", "primary_care": "No Charge After Deductible", "specialist_care": "No Charge After Deductible", "prescription_drugs": "No Charge After Deductible", "mental_health": null, "maternity_care": null, "preventive_care": null}, "top_3_available_plans": "[{'id': '14624MS0010006', 'name': 'Savings Bronze 7700', 'issuer': 'Primewell Health Services of Mississippi', 'premium': 503.7, 'premium_with_credit': 503.7, 'metal_level': 'Bronze', 'type': 'POS', 'deductible': 7700, 'max_out_of_pocket': 7700, 'hsa_eligible': True, 'quality_rating': 3, 'has_national_network': False, 'key_benefits': {'primary_care': 'No Charge After Deductible', 'specialist_care': 'No Charge After Deductible', 'emergency_room': 'No Charge After Deductible', 'prescription_drugs': 'No Charge After Deductible'}, 'issuer_phone': '1-************', 'urls': {'benefits': 'https://PrimewellHealth.com/documents/Marketplace/2025INDSavingsBronze7700SummaryOfBenefitsAndCoverage.pdf', 'brochure': 'https://PrimewellHealth.com/documents/Marketplace/2025MSMarketplaceINDPlanFinder.pdf', 'formulary': 'https://PrimewellHealth.com/documents/Marketplace/2025CommercialAndExchangeMemberListOfCoveredDrugs(Formulary).pdf', 'network': 'https://PrimewellHealth.com/provider/providersearch'}}]", "api_processing_status": "success"}, {"employee_id": "EMP_019", "name": "<PERSON>", "age": 29.0, "gender": "Male", "marital_status": "Married", "zipcode": 71601, "city": "Pine Bluff", "state": "AR", "income_tier": "Medium ($50K–$100K)", "employment_type": "Full-time", "dept_count": 2, "predicted_plan_type": "POS", "plan_confidence": 0.601260781288147, "plan_reason": "Medium income, good health - PPO for balanced cost and flexibility.", "predicted_benefits": ["Dental", "Vision"], "benefits_confidence": 0.5, "benefits_reason": "Default benefits assignment due to prediction error", "marketplace_plans_available": true, "plan_count": 3, "recommended_plan": {"id": "13262AR0230007", "name": "HA Bronze Exp Standardized", "issuer": "Health Advantage", "premium": 349.15, "metal_level": "Bronze", "type": "POS", "deductible": 7500, "max_out_of_pocket": 9200, "hsa_eligible": false, "quality_rating": 2}, "benefits_coverage": {"emergency_room": "50% Coinsurance after deductible", "primary_care": "$50", "specialist_care": "$100", "prescription_drugs": "$25", "mental_health": null, "maternity_care": null, "preventive_care": null}, "top_3_available_plans": "[{'id': '13262AR0230007', 'name': 'HA Bronze Exp Standardized', 'issuer': 'Health Advantage', 'premium': 349.15, 'premium_with_credit': 349.15, 'metal_level': 'Bronze', 'type': 'POS', 'deductible': 7500, 'max_out_of_pocket': 9200, 'hsa_eligible': False, 'quality_rating': 2, 'has_national_network': False, 'key_benefits': {'prescription_drugs': '$25', 'emergency_room': '50% Coinsurance after deductible', 'specialist_care': '$100', 'primary_care': '$50'}, 'issuer_phone': '1-************', 'urls': {'benefits': 'https://healthadvantage-hmo.com/docs/librariesprovider6/members/sbcpdffiles/2025/60030.pdf', 'brochure': 'https://www.healthadvantage-hmo.com/ha-plans-2025', 'formulary': 'https://www.healthadvantage-hmo.com/ha-formulary-2025', 'network': 'https://www.healthadvantage-hmo.com/members/network-selection'}}, {'id': '48772AR0010004', 'name': 'Octave Bronze Exp Standardized', 'issuer': 'Octave', 'premium': 340.2, 'premium_with_credit': 340.2, 'metal_level': 'Bronze', 'type': 'POS', 'deductible': 7500, 'max_out_of_pocket': 9200, 'hsa_eligible': False, 'quality_rating': 0, 'has_national_network': False, 'key_benefits': {'prescription_drugs': '$25', 'primary_care': '$50', 'specialist_care': '$100', 'emergency_room': '50% Coinsurance after deductible'}, 'issuer_phone': '1-************', 'urls': {'benefits': 'https://arkansasoctave.com/docs/arkansasoctavelibraries/members/sbcpdffiles/2025/70023.pdf', 'brochure': 'https://www.arkansasoctave.com/plans-2025', 'formulary': 'https://arkansasoctave.com/metallic-formulary-2025', 'network': 'https://www.arkansasoctave.com/members/network-selection/'}}, {'id': '48772AR0010004', 'name': 'Octave Bronze Exp Standardized', 'issuer': 'Octave', 'premium': 340.2, 'premium_with_credit': 340.2, 'metal_level': 'Bronze', 'type': 'POS', 'deductible': 7500, 'max_out_of_pocket': 9200, 'hsa_eligible': False, 'quality_rating': 0, 'has_national_network': False, 'key_benefits': {'prescription_drugs': '$25', 'primary_care': '$50', 'specialist_care': '$100', 'emergency_room': '50% Coinsurance after deductible'}, 'issuer_phone': '1-************', 'urls': {'benefits': 'https://arkansasoctave.com/docs/arkansasoctavelibraries/members/sbcpdffiles/2025/70023.pdf', 'brochure': 'https://www.arkansasoctave.com/plans-2025', 'formulary': 'https://arkansasoctave.com/metallic-formulary-2025', 'network': 'https://www.arkansasoctave.com/members/network-selection/'}}]", "api_processing_status": "success"}, {"employee_id": "EMP_020", "name": "<PERSON>", "age": 54.0, "gender": "Female", "marital_status": "Not Married", "zipcode": 66201, "city": "Shawnee Mission", "state": "KS", "income_tier": "High (>$100K)", "employment_type": "Full-time", "dept_count": 0, "predicted_plan_type": "PPO", "plan_confidence": 0.9706588983535767, "plan_reason": "Older age - PPO for comprehensive coverage and provider choice.", "predicted_benefits": ["Dental", "Vision"], "benefits_confidence": 0.5, "benefits_reason": "Default benefits assignment due to prediction error", "marketplace_plans_available": true, "plan_count": 3, "recommended_plan": {"id": "34368KS0110068", "name": "Standard Expanded Bronze", "issuer": "Ambetter from Sunflower Health Plan", "premium": 606.67, "metal_level": "Bronze", "type": "EPO", "deductible": 7500, "max_out_of_pocket": 9200, "hsa_eligible": false, "quality_rating": 3}, "benefits_coverage": {"emergency_room": "50% Coinsurance after deductible", "primary_care": "$50", "specialist_care": "$100", "prescription_drugs": "$25", "mental_health": null, "maternity_care": null, "preventive_care": null}, "top_3_available_plans": "[{'id': '34368KS0110068', 'name': 'Standard Expanded Bronze', 'issuer': 'Ambetter from Sunflower Health Plan', 'premium': 606.67, 'premium_with_credit': 606.67, 'metal_level': 'Bronze', 'type': 'EPO', 'deductible': 7500, 'max_out_of_pocket': 9200, 'hsa_eligible': False, 'quality_rating': 3, 'has_national_network': False, 'key_benefits': {'specialist_care': '$100', 'emergency_room': '50% Coinsurance after deductible', 'primary_care': '$50', 'prescription_drugs': '$25'}, 'issuer_phone': '1-************', 'urls': {'benefits': 'https://api.centene.com/SBC/2025/34368KS0110068-01.pdf', 'brochure': 'https://api.centene.com/brochures/2025/34368PremierKS.pdf', 'formulary': 'https://ambetter.sunflowerhealthplan.com/resources/pharmacy-resources.html', 'network': 'https://ambetter.sunflowerhealthplan.com/findadoc'}}, {'id': '34368KS0110043', 'name': 'Everyday Bronze', 'issuer': 'Ambetter from Sunflower Health Plan', 'premium': 612.21, 'premium_with_credit': 612.21, 'metal_level': 'Bronze', 'type': 'EPO', 'deductible': 8450, 'max_out_of_pocket': 9200, 'hsa_eligible': False, 'quality_rating': 3, 'has_national_network': False, 'key_benefits': {'prescription_drugs': '$3', 'primary_care': '$40', 'specialist_care': '$90', 'emergency_room': '50% Coinsurance after deductible'}, 'issuer_phone': '1-************', 'urls': {'benefits': 'https://api.centene.com/SBC/2025/34368KS0110043-01.pdf', 'brochure': 'https://api.centene.com/brochures/2025/34368PremierKS.pdf', 'formulary': 'https://ambetter.sunflowerhealthplan.com/resources/pharmacy-resources.html', 'network': 'https://ambetter.sunflowerhealthplan.com/findadoc'}}, {'id': '34368KS0120069', 'name': 'Standard Expanded Bronze + Vision + Adult Dental', 'issuer': 'Ambetter from Sunflower Health Plan', 'premium': 626.69, 'premium_with_credit': 626.69, 'metal_level': 'Bronze', 'type': 'EPO', 'deductible': 7500, 'max_out_of_pocket': 9200, 'hsa_eligible': False, 'quality_rating': 3, 'has_national_network': False, 'key_benefits': {'prescription_drugs': '$25', 'specialist_care': '$100', 'emergency_room': '50% Coinsurance after deductible', 'primary_care': '$50'}, 'issuer_phone': '1-************', 'urls': {'benefits': 'https://api.centene.com/SBC/2025/34368KS0120069-01.pdf', 'brochure': 'https://api.centene.com/brochures/2025/34368PremierKS.pdf', 'formulary': 'https://ambetter.sunflowerhealthplan.com/resources/pharmacy-resources.html', 'network': 'https://ambetter.sunflowerhealthplan.com/findadoc'}}]", "api_processing_status": "success"}], "processing_info": {"enrichment_summary": {"total_employees": 20, "features_analyzed": 19, "data_quality_improvement": {"overall_completion_rate": 100.0, "total_enriched": 49}}, "feature_validation": {}, "health_plan_errors": {}, "processing_notes": ["Health plan integration successful for 19/20 employees (95.0%)", "No marketplace plans found for 1 employees", "Marketplace plans available for 19 employees", "Average recommended plan premium: $580.60", "HSA-eligible plans recommended for 3 employees", "Average plan quality rating: 3.0/5.0 (16 plans rated)", "API Error: No plans available"]}, "enriched_data_csv": "employee_id,gender,zipcode,marital_status,dept_count,tobacco_use,employment_type,dept_1_age,dept_1_gender,dept_2_age,dept_2_gender,name,dob,age,address1,address2,city,state,relationship,record_type,salary,employee_class,department,hire_date,pregnancy_status,medical_plan,dental_plan,vision_plan,life_plan,add_plan,coverage_tier,ssn,dept_1,dept_1_dob,relationship_type_1,dept_2,dept_2_dob,relationship_type_2,dept_3,dept_3_dob,dept_3_age,dept_3_gender,relationship_type_3,dept_4,dept_4_dob,dept_4_age,dept_4_gender,relationship_type_4,dept_5,dept_5_dob,dept_5_age,dept_5_gender,relationship_type_5,dept_6,dept_6_dob,dept_6_age,dept_6_gender,relationship_type_6,dept_7,dept_7_dob,dept_7_age,dept_7_gender,relationship_type_7,dept_8,dept_8_dob,dept_8_age,dept_8_gender,relationship_type_8,dept_9,dept_9_dob,dept_9_age,dept_9_gender,relationship_type_9,dept_10,dept_10_dob,dept_10_age,dept_10_gender,relationship_type_10,dept_11,dept_11_dob,dept_11_age,dept_11_gender,relationship_type_11,dept_12,dept_12_dob,dept_12_age,dept_12_gender,relationship_type_12,dept_13,dept_13_dob,dept_13_age,dept_13_gender,relationship_type_13,dept_14,dept_14_dob,dept_14_age,dept_14_gender,relationship_type_14,dept_15,dept_15_dob,dept_15_age,dept_15_gender,relationship_type_15,dept_16,dept_16_dob,dept_16_age,dept_16_gender,relationship_type_16,dept_17,dept_17_dob,dept_17_age,dept_17_gender,relationship_type_17,dept_18,dept_18_dob,dept_18_age,dept_18_gender,relationship_type_18,dept_19,dept_19_dob,dept_19_age,dept_19_gender,relationship_type_19,dept_20,dept_20_dob,dept_20_age,dept_20_gender,relationship_type_20,region,job_type,health_condition,chronic_condition,prescription_use,income_tier,risk_tolerance,lifestyle,travel_frequency,hsa_familiarity,mental_health_needs,predicted_plan_type,plan_confidence,plan_reason,top_3_plans,top_3_plan_confidences,predicted_benefits,benefits_confidence,benefits_reason,top_3_benefits,top_3_benefits_confidences,marketplace_plans_available,plan_count,available_plan_types,available_metal_levels,estimated_premium_range,marketplace_state_supported,recommended_plan_id,recommended_plan_name,recommended_plan_issuer,recommended_plan_premium,recommended_plan_metal_level,recommended_plan_type,recommended_plan_deductible,recommended_plan_moop,recommended_plan_hsa_eligible,recommended_plan_quality_rating,top_3_available_plans,benefits_emergency_room,benefits_primary_care,benefits_specialist_care,benefits_prescription_drugs,benefits_mental_health,benefits_maternity_care,benefits_preventive_care,network_has_national,network_referral_required,api_processing_status,api_error_message\r\nEMP_001,Male,75201,Married,2,N,Full-time,8.0,Male,12.0,Female,John Smith,,61.0,,,Dallas,TX,,,,,Sales,,N,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Urban,Desk,Fair,N,Occasional,Low (<$50K),Low,Moderate,Frequent,N,N,POS,0.9586077,Older age - PPO for comprehensive coverage and provider choice.,\"['POS', 'HMO', 'PPO']\",\"[0.9586077, 0.039256983, 0.**********]\",\"['Dental', 'Vision', 'Hospital Indemnity', 'Critical Illness']\",1.0,\"Dental: Essential oral health coverage for all employees.; Vision: Eye care coverage for all employees.; Hospital Indemnity: Recommended due to fair health condition, age 61, or chronic conditions.; Critical Illness: Important protection for age 61 with fair health status.\",\"['Employee Assistance', 'Wellness Programs', 'FSA']\",\"[0.5, 0.5, 0.5]\",True,3,,,,,33602TX0461117,MyBlue Health Bronze℠ 402,Blue Cross and Blue Shield of Texas,787.61,Bronze,HMO,7400,9200,False,3,\"[{'id': '33602TX0461117', 'name': 'MyBlue Health Bronze℠ 402', 'issuer': 'Blue Cross and Blue Shield of Texas', 'premium': 787.61, 'premium_with_credit': 0, 'metal_level': 'Bronze', 'type': 'HMO', 'deductible': 7400, 'max_out_of_pocket': 9200, 'hsa_eligible': False, 'quality_rating': 3, 'has_national_network': False, 'key_benefits': {'prescription_drugs': '$10', 'specialist_care': '50% Coinsurance after deductible', 'emergency_room': '$950 Copay with deductible/50% Coinsurance after deductible', 'primary_care': 'No Charge'}, 'issuer_phone': '**************', 'urls': {'benefits': 'https://www.bcbstx.com/sbc/ind/sbc-bhsa01bftitxp-tx-2025.pdf', 'brochure': 'https://www.bcbstx.com/plan-docs/ind/brochure-tx-2025.pdf', 'formulary': 'https://www.myprime.com/content/dam/prime/memberportal/WebDocs/2025/Formularies/HIM/2025_TX_6T_HIM.pdf', 'network': 'https://my.providerfinderonline.com/?ci=tx-myblue-health&corp_code=TX'}}, {'id': '33602TX0461159', 'name': 'MyBlue Health Bronze℠ Standard', 'issuer': 'Blue Cross and Blue Shield of Texas', 'premium': 796.64, 'premium_with_credit': 0, 'metal_level': 'Bronze', 'type': 'HMO', 'deductible': 7500, 'max_out_of_pocket': 9200, 'hsa_eligible': False, 'quality_rating': 3, 'has_national_network': False, 'key_benefits': {'primary_care': '$50', 'specialist_care': '$100', 'emergency_room': '50% Coinsurance after deductible', 'prescription_drugs': '$25'}, 'issuer_phone': '**************', 'urls': {'benefits': 'https://www.bcbstx.com/sbc/ind/sbc-bhsd42bftitxp-tx-2025.pdf', 'brochure': 'https://www.bcbstx.com/plan-docs/ind/brochure-tx-2025.pdf', 'formulary': 'https://www.myprime.com/content/dam/prime/memberportal/WebDocs/2025/Formularies/HIM/2025_TX_4T_HIM.pdf', 'network': 'https://my.providerfinderonline.com/?ci=tx-myblue-health&corp_code=TX'}}, {'id': '47501TX0040002', 'name': 'Wellpoint Essential Bronze 4000 HSA ($0 Virtual PCP + $0 Select Drugs + Incentives)', 'issuer': 'WellPoint', 'premium': 830.3, 'premium_with_credit': 0, 'metal_level': 'Bronze', 'type': 'HMO', 'deductible': 4000, 'max_out_of_pocket': 8250, 'hsa_eligible': True, 'quality_rating': 0, 'has_national_network': False, 'key_benefits': {'primary_care': '40% Coinsurance after deductible', 'specialist_care': '40% Coinsurance after deductible', 'emergency_room': '$500 Copay after deductible/40% Coinsurance after deductible', 'prescription_drugs': '20% Coinsurance after deductible'}, 'issuer_phone': '**************', 'urls': {'benefits': 'https://sbc.wellpoint.com/dpsdeeplink/deepLink/WellpointEssentialBronze4000HSA0VirtualPCP0SelectDrugsIncentives/7ZJG/English/DG166705513357.pdf', 'brochure': 'http://wellpoint.ly/IND-TX-EN-KIT-2025', 'formulary': 'https://www.wellpoint.com/TXSelectdrugtier4', 'network': 'https://www.wellpoint.com/find-care/?alphaprefix=3730'}}]\",$950 Copay with deductible/50% Coinsurance after deductible,No Charge,50% Coinsurance after deductible,$10,,,,False,False,success,\r\nEMP_002,Female,33101,Not Married,0,N,Full-time,,,,,Sarah Johnson,,35.0,,,Miami,FL,,,,,Finance,,N,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Urban,Desk,Excellent,N,None,Medium ($50K–$100K),Medium,Active,Rare,N,N,HMO,0.4981239,\"Medium income, urban area - EPO for network-based care.\",\"['HMO', 'PPO', 'POS']\",\"[0.4981239, 0.372736, 0.05935199]\",[],0.0,ML model prediction,\"['Employee Assistance', 'Wellness Programs', 'FSA']\",\"[0.5, 0.5, 0.5]\",True,3,,,,,54172FL0010010,Bronze 8,Molina Healthcare,400.05,Bronze,HMO,7500,9200,False,2,\"[{'id': '54172FL0010010', 'name': 'Bronze 8', 'issuer': 'Molina Healthcare', 'premium': 400.05, 'premium_with_credit': 400.05, 'metal_level': 'Bronze', 'type': 'HMO', 'deductible': 7500, 'max_out_of_pocket': 9200, 'hsa_eligible': False, 'quality_rating': 2, 'has_national_network': False, 'key_benefits': {'primary_care': '$50', 'specialist_care': '$100', 'emergency_room': '50% Coinsurance after deductible', 'prescription_drugs': '$25'}, 'issuer_phone': '1-************', 'urls': {'benefits': 'https://www.molinamarketplace.com/members/fl/en-US/PDF/Marketplace/2025/FL25SBCE_B8_1.pdf', 'brochure': 'https://www.molinamarketplace.com/marketplace/fl/en-us/-/media/Molina/PublicWebsite/PDF/members/common/en-us/FNLMBRBrochure2025.ashx', 'formulary': 'https://www.molinamarketplace.com/members/fl/en-US/PDF/Marketplace/2025/FLFormulary2025.pdf', 'network': 'https://molina.sapphirethreesixtyfive.com//?ci=fl-marketplace'}}, {'id': '67926FL0010001', 'name': 'AmeriHealth Caritas Next Bronze Essential + No Referrals  ', 'issuer': 'AmeriHealth Caritas Next', 'premium': 370.1, 'premium_with_credit': 370.1, 'metal_level': 'Bronze', 'type': 'HMO', 'deductible': 9200, 'max_out_of_pocket': 9200, 'hsa_eligible': False, 'quality_rating': 0, 'has_national_network': False, 'key_benefits': {'prescription_drugs': 'No Charge After Deductible', 'specialist_care': 'No Charge After Deductible', 'emergency_room': 'No Charge After Deductible', 'primary_care': 'No Charge After Deductible'}, 'issuer_phone': '1-************', 'urls': {'benefits': 'https://www.amerihealthcaritasnext.com/assets/pdf/fl/2025/member/plan-documents/summary-of-benefits-fl-bronze-essential-on.pdf', 'brochure': 'https://www.amerihealthcaritasnext.com/assets/pdf/fl/2025/member/plan-documents/plan-brochure.pdf', 'formulary': 'https://client.formularynavigator.com/Search.aspx?siteCode=**********', 'network': 'https://amerihealthcaritasnext.healthsparq.com/healthsparq/public/#/one/city=&state=&postalCode=&country=&insurerCode=ACNEXT_I&brandCode=ACNEXT&alphaPrefix=&bcbsaProductId=&productCode=FLEX'}}, {'id': '67926FL0010002', 'name': 'AmeriHealth Caritas Next Bronze Signature + No Referrals  ', 'issuer': 'AmeriHealth Caritas Next', 'premium': 398.11, 'premium_with_credit': 398.11, 'metal_level': 'Bronze', 'type': 'HMO', 'deductible': 7500, 'max_out_of_pocket': 9200, 'hsa_eligible': False, 'quality_rating': 0, 'has_national_network': False, 'key_benefits': {'primary_care': '$50', 'specialist_care': '$100', 'emergency_room': '50% Coinsurance after deductible', 'prescription_drugs': '$25'}, 'issuer_phone': '1-************', 'urls': {'benefits': 'https://www.amerihealthcaritasnext.com/assets/pdf/fl/2025/member/plan-documents/summary-of-benefits-fl-bronze-signature-on.pdf', 'brochure': 'https://www.amerihealthcaritasnext.com/assets/pdf/fl/2025/member/plan-documents/plan-brochure.pdf', 'formulary': 'https://client.formularynavigator.com/Search.aspx?siteCode=**********', 'network': 'https://amerihealthcaritasnext.healthsparq.com/healthsparq/public/#/one/city=&state=&postalCode=&country=&insurerCode=ACNEXT_I&brandCode=ACNEXT&alphaPrefix=&bcbsaProductId=&productCode=FLEX'}}]\",50% Coinsurance after deductible,$50,$100,$25,,,,False,False,success,\r\nEMP_003,Male,30301,Married,2,N,Full-time,15.0,Female,10.0,Male,Michael Brown,,53.0,,,Atlanta,GA,,,,,Engineering,,N,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Urban,Desk,Good,Y,Occasional,Low (<$50K),Medium,Active,Occasional,Y,N,HMO,0.71387947,\"Chronic conditions, older age - POS for coordinated care management.\",\"['HMO', 'PPO', 'POS']\",\"[0.71387947, 0.27485994, 0.*********]\",\"['Dental', 'Vision', 'Accident', 'Critical Illness', 'STD']\",1.0,Dental: Essential oral health coverage for all employees.; Vision: Eye care coverage for all employees.; Accident: Recommended for active lifestyle and occasional travel frequency.; Critical Illness: Important protection for age 53 with good health status.,\"['Employee Assistance', 'Wellness Programs', 'FSA']\",\"[0.5, 0.5, 0.5]\",False,0,,,,,,,,,,,,,,,,,,,,,,,,,no_plans_found,No plans available\r\nEMP_004,Female,43201,Not Married,1,N,Full-time,3.0,Female,,,Lisa Davis,,39.0,,,Columbus,OH,,,,,Engineering,,N,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Urban,Desk,Excellent,N,None,High (>$100K),High,Moderate,Occasional,Y,Y,PPO,0.77849966,\"High income, excellent health, high risk tolerance with dependents - PPO for family coverage.\",\"['PPO', 'HDHP + HSA', 'HDHP']\",\"[0.77849966, 0.20950532, 0.*********]\",\"['Dental', 'Vision', 'Accident', 'Term Life', 'STD', 'LTD', 'Wellness Programs', 'Employee Assistance']\",1.0,Dental: Essential oral health coverage for all employees.; Vision: Eye care coverage for all employees.; Term Life: Essential protection for 1 dependents.; STD: Income protection for High (>$100K) earners or excellent health status.; LTD: Long-term income protection for High (>$100K) earners or age 39+.,\"['Employee Assistance', 'Wellness Programs', 'FSA']\",\"[0.5, 0.5, 0.5]\",True,3,,,,,29341OH0100024,Bronze Classic 4700 (Select),Oscar Health Insurance,365.41,Bronze,HMO,4700,9200,False,3,\"[{'id': '29341OH0100024', 'name': 'Bronze Classic 4700 (Select)', 'issuer': 'Oscar Health Insurance', 'premium': 365.41, 'premium_with_credit': 365.41, 'metal_level': 'Bronze', 'type': 'HMO', 'deductible': 4700, 'max_out_of_pocket': 9200, 'hsa_eligible': False, 'quality_rating': 3, 'has_national_network': False, 'key_benefits': {'specialist_care': '$125', 'emergency_room': '50% Coinsurance after deductible', 'primary_care': '$60', 'prescription_drugs': '$3'}, 'issuer_phone': '1-************', 'urls': {'benefits': 'https://d3ul0st9g52g6o.cloudfront.net/2025/OH/sbc/2025_29341OH010002401.pdf', 'brochure': 'https://www.hioscar.com/asset/plan-brochure-columbus', 'formulary': 'https://www.hioscar.com/search-documents/drug-formularies/', 'network': 'https://www.hioscar.com/search/?networkId=020&year=2025'}}, {'id': '29341OH0100050', 'name': 'Bronze Classic Standard (Select)', 'issuer': 'Oscar Health Insurance', 'premium': 359.23, 'premium_with_credit': 359.23, 'metal_level': 'Bronze', 'type': 'HMO', 'deductible': 7500, 'max_out_of_pocket': 9200, 'hsa_eligible': False, 'quality_rating': 3, 'has_national_network': False, 'key_benefits': {'primary_care': '$50', 'specialist_care': '$100', 'emergency_room': '50% Coinsurance after deductible', 'prescription_drugs': '$25'}, 'issuer_phone': '1-************', 'urls': {'benefits': 'https://d3ul0st9g52g6o.cloudfront.net/2025/OH/sbc/2025_29341OH010005001.pdf', 'brochure': 'https://www.hioscar.com/asset/plan-brochure-columbus', 'formulary': 'https://www.hioscar.com/search-documents/drug-formularies/', 'network': 'https://www.hioscar.com/search/?networkId=020&year=2025'}}, {'id': '29341OH0100002', 'name': 'Bronze Classic PCP Saver (Select)', 'issuer': 'Oscar Health Insurance', 'premium': 364.73, 'premium_with_credit': 364.73, 'metal_level': 'Bronze', 'type': 'HMO', 'deductible': 7750, 'max_out_of_pocket': 9150, 'hsa_eligible': False, 'quality_rating': 3, 'has_national_network': False, 'key_benefits': {'specialist_care': '$90 Copay after deductible', 'emergency_room': '50% Coinsurance after deductible', 'primary_care': '$25', 'prescription_drugs': '$3'}, 'issuer_phone': '1-************', 'urls': {'benefits': 'https://d3ul0st9g52g6o.cloudfront.net/2025/OH/sbc/2025_29341OH010000201.pdf', 'brochure': 'https://www.hioscar.com/asset/plan-brochure-columbus', 'formulary': 'https://www.hioscar.com/search-documents/drug-formularies/', 'network': 'https://www.hioscar.com/search/?networkId=020&year=2025'}}]\",50% Coinsurance after deductible,$60,$125,$3,,,,False,False,success,\r\nEMP_005,Male,27601,Married,2,Y,Full-time,17.0,Male,14.0,Female,Robert Wilson,,37.0,,,Raleigh,NC,,,,,Manufacturing,,N,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Urban,Desk,Excellent,N,None,High (>$100K),High,Active,Rare,N,N,PPO,0.89935577,\"High income, excellent health, high risk tolerance with dependents - PPO for family coverage.\",\"['PPO', 'POS', 'Indemnity']\",\"[0.89935577, 0.05893854, 0.*********]\",\"['Dental', 'Vision', 'Accident', 'Term Life', 'STD', 'LTD', 'FSA']\",1.0,Dental: Essential oral health coverage for all employees.; Vision: Eye care coverage for all employees.; Accident: Recommended for active lifestyle and rare travel frequency.; Term Life: Essential protection for 2 dependents.; STD: Income protection for High (>$100K) earners or excellent health status.; LTD: Long-term income protection for High (>$100K) earners or age 37+.,\"['Employee Assistance', 'Wellness Programs', 'FSA']\",\"[0.5, 0.5, 0.5]\",True,3,,,,,11512NC0390031,Blue Home Bronze Basic | 3 Free PCP | $20 Tier 1 Rx | Integrated | with UNC Health Alliance,Blue Cross and Blue Shield of NC,416.55,Bronze,EPO,7000,9200,False,4,\"[{'id': '11512NC0390031', 'name': 'Blue Home Bronze Basic | 3 Free PCP | $20 Tier 1 Rx | Integrated | with UNC Health Alliance', 'issuer': 'Blue Cross and Blue Shield of NC', 'premium': 416.55, 'premium_with_credit': 416.55, 'metal_level': 'Bronze', 'type': 'EPO', 'deductible': 7000, 'max_out_of_pocket': 9200, 'hsa_eligible': False, 'quality_rating': 4, 'has_national_network': False, 'key_benefits': {'primary_care': '$100', 'specialist_care': '$150', 'emergency_room': '50% Coinsurance after deductible', 'prescription_drugs': '$20'}, 'issuer_phone': '1-************', 'urls': {'benefits': 'https://www.bcbsnc.com/assets/shopper/public/pdf/sbc/Blue_Home_Bronze_Basic_7000_with_UNC_Health_Alliance_2025.pdf', 'brochure': 'https://www.bluecrossnc.com/bhuncplanbrochure_2025', 'formulary': 'https://www.myprime.com/content/dam/prime/memberportal/WebDocs/2025/Formularies/HIM/2025_NC_5T_HealthInsuranceMarketplace.pdf', 'network': 'https://healthnav.bcbsnc.com/?ci=COMMERCIAL&network_id=25'}}, {'id': '11512NC0390034', 'name': 'Blue Home Bronze Standard | with UNC Health Alliance', 'issuer': 'Blue Cross and Blue Shield of NC', 'premium': 427.72, 'premium_with_credit': 427.72, 'metal_level': 'Bronze', 'type': 'EPO', 'deductible': 7500, 'max_out_of_pocket': 9200, 'hsa_eligible': False, 'quality_rating': 4, 'has_national_network': False, 'key_benefits': {'specialist_care': '$100', 'emergency_room': '50% Coinsurance after deductible', 'primary_care': '$50', 'prescription_drugs': '$25'}, 'issuer_phone': '1-************', 'urls': {'benefits': 'https://www.bcbsnc.com/assets/shopper/public/pdf/sbc/Blue_Home_Bronze_Standard_7500_with_UNC_Health_Alliance_2025.pdf', 'brochure': 'https://www.bluecrossnc.com/bhuncplanbrochure_2025', 'formulary': 'https://www.myprime.com/content/dam/prime/memberportal/WebDocs/2025/Formularies/HIM/2025_NC_4T_HealthInsuranceMarketplace.pdf', 'network': 'https://healthnav.bcbsnc.com/?ci=COMMERCIAL&network_id=25'}}, {'id': '54332NC0030030', 'name': 'UHC Bronze Standard (No Referrals)', 'issuer': 'UnitedHealthcare', 'premium': 437.47, 'premium_with_credit': 437.47, 'metal_level': 'Bronze', 'type': 'HMO', 'deductible': 7500, 'max_out_of_pocket': 9200, 'hsa_eligible': False, 'quality_rating': 3, 'has_national_network': False, 'key_benefits': {'prescription_drugs': '$25', 'specialist_care': '$100', 'emergency_room': '50% Coinsurance after deductible', 'primary_care': '$50'}, 'issuer_phone': '1-************', 'urls': {'benefits': 'https://www.uhc.com/ifp/sbc.54332NC0030030-01.en.2025.pdf', 'brochure': 'https://www.uhc.com/ncplanbrochure2025', 'formulary': 'https://www.uhc.com/xncdruglist2025', 'network': 'https://www.uhc.com/xncdocfindoa2025'}}]\",50% Coinsurance after deductible,$100,$150,$20,,,,False,False,success,\r\nEMP_006,Female,37201,Not Married,0,N,Part-time,,,,,Jennifer Miller,,62.0,,,Nashville,TN,,,,,Engineering,,N,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Urban,Desk,Fair,N,None,Medium ($50K–$100K),Medium,Moderate,Occasional,N,Y,PPO,0.9841007,\"Medium income, urban area - EPO for network-based care.\",\"['PPO', 'POS', 'HMO']\",\"[0.9841007, 0.0139204, 0.**********]\",\"['Dental', 'Hospital Indemnity', 'Accident', 'Critical Illness', 'STD', 'Employee Assistance']\",1.0,\"Dental: Essential oral health coverage for all employees.; Hospital Indemnity: Recommended due to fair health condition, age 62, or chronic conditions.; Critical Illness: Important protection for age 62 with fair health status.; STD: Income protection for Medium ($50K–$100K) earners or fair health status.\",\"['Employee Assistance', 'Wellness Programs', 'FSA']\",\"[0.5, 0.5, 0.5]\",True,3,,,,,14002TN0400239,BlueCross B15E $0 virtual care from Teladoc Health ®,BlueCross BlueShield of Tennessee,958.06,Bronze,EPO,9200,9200,False,4,\"[{'id': '14002TN0400239', 'name': 'BlueCross B15E $0 virtual care from Teladoc Health ®', 'issuer': 'BlueCross BlueShield of Tennessee', 'premium': 958.06, 'premium_with_credit': 254.06, 'metal_level': 'Bronze', 'type': 'EPO', 'deductible': 9200, 'max_out_of_pocket': 9200, 'hsa_eligible': False, 'quality_rating': 4, 'has_national_network': False, 'key_benefits': {'primary_care': 'No Charge After Deductible', 'specialist_care': 'No Charge After Deductible', 'emergency_room': '$750 Copay with deductible', 'prescription_drugs': 'No Charge After Deductible'}, 'issuer_phone': '1-************', 'urls': {'benefits': 'https://www.bcbst.com/sbc/2025/127600/B15E_SBC.pdf', 'brochure': 'https://www.bcbst.com/sbc/2025/pb/Network-E.pdf', 'formulary': 'https://www.bcbst.com/docs/providers/2025-essential-formulary.pdf', 'network': 'https://www.bcbst.com/network-e'}}, {'id': '14002TN0400253', 'name': 'BlueCross B16E $50 PCP Copay + $0 virtual care from Teladoc Health ®', 'issuer': 'BlueCross BlueShield of Tennessee', 'premium': 985.09, 'premium_with_credit': 281.09, 'metal_level': 'Bronze', 'type': 'EPO', 'deductible': 7500, 'max_out_of_pocket': 9200, 'hsa_eligible': False, 'quality_rating': 4, 'has_national_network': False, 'key_benefits': {'primary_care': '$50', 'emergency_room': '50% Coinsurance after deductible', 'specialist_care': '$100', 'prescription_drugs': '$25'}, 'issuer_phone': '1-************', 'urls': {'benefits': 'https://www.bcbst.com/sbc/2025/127600/B16E_SBC.pdf', 'brochure': 'https://www.bcbst.com/sbc/2025/pb/Network-E.pdf', 'formulary': 'https://www.bcbst.com/docs/providers/2025-essential-formulary.pdf', 'network': 'https://www.bcbst.com/network-e'}}, {'id': '14002TN0400313', 'name': 'BlueCross B17E $0 virtual care from Teladoc Health ® + Adult Dental', 'issuer': 'BlueCross BlueShield of Tennessee', 'premium': 988.08, 'premium_with_credit': 284.08, 'metal_level': 'Bronze', 'type': 'EPO', 'deductible': 9200, 'max_out_of_pocket': 9200, 'hsa_eligible': False, 'quality_rating': 4, 'has_national_network': False, 'key_benefits': {'prescription_drugs': 'No Charge After Deductible', 'specialist_care': 'No Charge After Deductible', 'emergency_room': '$750 Copay with deductible', 'primary_care': 'No Charge After Deductible'}, 'issuer_phone': '1-************', 'urls': {'benefits': 'https://www.bcbst.com/sbc/2025/127600/B17E_SBC.pdf', 'brochure': 'https://www.bcbst.com/sbc/2025/pb/Network-E.pdf', 'formulary': 'https://www.bcbst.com/docs/providers/2025-essential-formulary.pdf', 'network': 'https://www.bcbst.com/network-e'}}]\",$750 Copay with deductible,No Charge After Deductible,No Charge After Deductible,No Charge After Deductible,,,,False,False,success,\r\nEMP_007,Male,35201,Married,1,N,Full-time,6.0,Male,,,David Garcia,,60.0,,,Birmingham,AL,,,,,Finance,,N,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Urban,Desk,Good,N,None,Medium ($50K–$100K),Low,Sedentary,Rare,Y,N,HDHP + HSA,0.7882768,\"Medium income, good health - PPO for balanced cost and flexibility.\",\"['HDHP + HSA', 'POS', 'PPO']\",\"[0.7882768, 0.18632592, 0.024724983]\",\"['Dental', 'Vision']\",1.0,Dental: Essential oral health coverage for all employees.; Vision: Eye care coverage for all employees.,\"['Employee Assistance', 'Wellness Programs', 'FSA']\",\"[0.5, 0.5, 0.5]\",True,3,,,,,46944AL0460001,Blue Saver Bronze,Blue Cross and Blue Shield of Alabama,842.1,Bronze,PPO,8500,9200,False,3,\"[{'id': '46944AL0460001', 'name': 'Blue Saver Bronze', 'issuer': 'Blue Cross and Blue Shield of Alabama', 'premium': 842.1, 'premium_with_credit': 206.1, 'metal_level': 'Bronze', 'type': 'PPO', 'deductible': 8500, 'max_out_of_pocket': 9200, 'hsa_eligible': False, 'quality_rating': 3, 'has_national_network': False, 'key_benefits': {'prescription_drugs': '$5', 'specialist_care': '$90', 'emergency_room': 'No Charge After Deductible', 'primary_care': '$45'}, 'issuer_phone': '1-************', 'urls': {'benefits': 'https://www.alabamablue.com/sb/2025sbi.pdf', 'brochure': 'https://www.alabamablue.com/pb/2025individual.pdf', 'formulary': 'https://www.myprime.com/content/dam/prime/memberportal/WebDocs/2025/Formularies/HIM/2025_AL_6T_Source+Rx_1.0.pdf', 'network': 'https://www.bcbsal.org/web/provider-finder'}}, {'id': '46944AL0750001', 'name': 'Blue Standardized Bronze', 'issuer': 'Blue Cross and Blue Shield of Alabama', 'premium': 845.85, 'premium_with_credit': 209.85, 'metal_level': 'Bronze', 'type': 'PPO', 'deductible': 7500, 'max_out_of_pocket': 9200, 'hsa_eligible': False, 'quality_rating': 3, 'has_national_network': False, 'key_benefits': {'prescription_drugs': '$25', 'specialist_care': '$100', 'emergency_room': '50% Coinsurance after deductible', 'primary_care': '$50'}, 'issuer_phone': '1-************', 'urls': {'benefits': 'https://www.alabamablue.com/sb/2025stb.pdf', 'brochure': 'https://www.alabamablue.com/pb/2025individual.pdf', 'formulary': 'https://www.myprime.com/content/dam/prime/memberportal/WebDocs/2025/Formularies/HIM/2025_AL_4T_Standardized_Source+Rx_1.0.pdf', 'network': 'https://www.bcbsal.org/web/provider-finder'}}, {'id': '69461AL0110018', 'name': 'UHC Bronze Standard (No Referrals)', 'issuer': 'UnitedHealthcare', 'premium': 849.59, 'premium_with_credit': 213.59, 'metal_level': 'Bronze', 'type': 'EPO', 'deductible': 7500, 'max_out_of_pocket': 9200, 'hsa_eligible': False, 'quality_rating': 2, 'has_national_network': False, 'key_benefits': {'primary_care': '$50', 'specialist_care': '$100', 'emergency_room': '50% Coinsurance after deductible', 'prescription_drugs': '$25'}, 'issuer_phone': '1-************', 'urls': {'benefits': 'https://www.uhc.com/ifp/sbc.69461AL0110018-01.en.2025.pdf', 'brochure': 'https://www.uhc.com/alplanbrochure2025', 'formulary': 'https://www.uhc.com/xaldruglist2025', 'network': 'https://www.uhc.com/xaldocfindoa2025'}}]\",No Charge After Deductible,$45,$90,$5,,,,False,False,success,\r\nEMP_008,Female,73101,Not Married,0,N,Full-time,,,,,Amanda Rodriguez,,30.0,,,Oklahoma City,OK,,,,,Sales,,N,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Rural,Desk,Fair,N,None,Medium ($50K–$100K),Low,Moderate,Occasional,N,N,POS,0.8595268,Middle age with dependents - PPO for family coverage.,\"['POS', 'PPO', 'HMO']\",\"[0.8595268, 0.13663445, 0.0032428198]\",[],0.0,ML model prediction,\"['Employee Assistance', 'Wellness Programs', 'FSA']\",\"[0.5, 0.5, 0.5]\",True,3,,,,,62505OK0120019,Standard Expanded Bronze,Ambetter of Oklahoma,306.34,Bronze,PPO,7500,9200,False,2,\"[{'id': '62505OK0120019', 'name': 'Standard Expanded Bronze', 'issuer': 'Ambetter of Oklahoma', 'premium': 306.34, 'premium_with_credit': 306.34, 'metal_level': 'Bronze', 'type': 'PPO', 'deductible': 7500, 'max_out_of_pocket': 9200, 'hsa_eligible': False, 'quality_rating': 2, 'has_national_network': False, 'key_benefits': {'prescription_drugs': '$25', 'primary_care': '$50', 'specialist_care': '$100', 'emergency_room': '50% Coinsurance after deductible'}, 'issuer_phone': '**************', 'urls': {'benefits': 'https://api.centene.com/SBC/2025/62505OK0120019-01.pdf', 'brochure': 'https://api.centene.com/brochures/2025/62505PremierOK.pdf', 'formulary': 'https://www.ambetterofoklahoma.com/resources/pharmacy-resources.html', 'network': 'https://www.ambetterofoklahoma.com/findadoc'}}, {'id': '62505OK0120002', 'name': 'Everyday Bronze', 'issuer': 'Ambetter of Oklahoma', 'premium': 311.97, 'premium_with_credit': 311.97, 'metal_level': 'Bronze', 'type': 'PPO', 'deductible': 8450, 'max_out_of_pocket': 9200, 'hsa_eligible': False, 'quality_rating': 2, 'has_national_network': False, 'key_benefits': {'primary_care': '$40', 'emergency_room': '50% Coinsurance after deductible', 'specialist_care': '$90', 'prescription_drugs': '$3'}, 'issuer_phone': '**************', 'urls': {'benefits': 'https://api.centene.com/SBC/2025/62505OK0120002-01.pdf', 'brochure': 'https://api.centene.com/brochures/2025/62505PremierOK.pdf', 'formulary': 'https://www.ambetterofoklahoma.com/resources/pharmacy-resources.html', 'network': 'https://www.ambetterofoklahoma.com/findadoc'}}, {'id': '21333OK0060073', 'name': 'Balance by Medica Expanded Bronze Standard', 'issuer': 'Medica', 'premium': 319.16, 'premium_with_credit': 319.16, 'metal_level': 'Bronze', 'type': 'PPO', 'deductible': 7500, 'max_out_of_pocket': 9200, 'hsa_eligible': False, 'quality_rating': 2, 'has_national_network': False, 'key_benefits': {'prescription_drugs': '$25', 'primary_care': '$50', 'specialist_care': '$100', 'emergency_room': '50% Coinsurance after deductible'}, 'issuer_phone': '**************', 'urls': {'benefits': 'https://portal.medica.com/visitor/sbcsearch/docdisplay?plancode=2025-IFBBLBESTOK&uid=FFM.pdf', 'brochure': 'https://www.Medica.com/OKPlans-2025', 'formulary': 'https://www.Medica.com/OKDrugList-2025', 'network': 'https://www.Medica.com/SearchBalanceNetwork-2025'}}]\",50% Coinsurance after deductible,$50,$100,$25,,,,False,False,success,\r\nEMP_009,Male,64101,Married,2,N,Full-time,16.0,Female,13.0,Male,Christopher Martinez,,37.0,,,Kansas City,MO,,,,,Engineering,,N,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Urban,Desk,Excellent,N,None,High (>$100K),High,Moderate,Occasional,Y,N,HDHP + HSA,0.7240247,\"High income, excellent health, high risk tolerance with dependents - PPO for family coverage.\",\"['HDHP + HSA', 'PPO', 'HDHP']\",\"[0.7240247, 0.22192124, 0.02475984]\",\"['Dental', 'Vision', 'Accident', 'Term Life', 'STD', 'LTD', 'Wellness Programs']\",1.0,Dental: Essential oral health coverage for all employees.; Vision: Eye care coverage for all employees.; Term Life: Essential protection for 2 dependents.; STD: Income protection for High (>$100K) earners or excellent health status.; LTD: Long-term income protection for High (>$100K) earners or age 37+.,\"['Employee Assistance', 'Wellness Programs', 'FSA']\",\"[0.5, 0.5, 0.5]\",True,3,,,,,99723MO0090069,Standard Expanded Bronze,Ambetter from Home State Health,354.97,Bronze,EPO,7500,9200,False,3,\"[{'id': '99723MO0090069', 'name': 'Standard Expanded Bronze', 'issuer': 'Ambetter from Home State Health', 'premium': 354.97, 'premium_with_credit': 354.97, 'metal_level': 'Bronze', 'type': 'EPO', 'deductible': 7500, 'max_out_of_pocket': 9200, 'hsa_eligible': False, 'quality_rating': 3, 'has_national_network': False, 'key_benefits': {'primary_care': '$50', 'specialist_care': '$100', 'emergency_room': '50% Coinsurance after deductible', 'prescription_drugs': '$25'}, 'issuer_phone': '1-************', 'urls': {'benefits': 'https://api.centene.com/SBC/2025/99723MO0090069-01.pdf', 'brochure': 'https://api.centene.com/brochures/2025/99723PremierMO.pdf', 'formulary': 'https://ambetter.homestatehealth.com/resources/pharmacy-resources.html', 'network': 'https://ambetter.homestatehealth.com/findadoc'}}, {'id': '99723MO0090018', 'name': 'Everyday Bronze', 'issuer': 'Ambetter from Home State Health', 'premium': 359.9, 'premium_with_credit': 359.9, 'metal_level': 'Bronze', 'type': 'EPO', 'deductible': 8450, 'max_out_of_pocket': 9200, 'hsa_eligible': False, 'quality_rating': 3, 'has_national_network': False, 'key_benefits': {'prescription_drugs': '$3', 'specialist_care': '$90', 'emergency_room': '50% Coinsurance after deductible', 'primary_care': '$40'}, 'issuer_phone': '1-************', 'urls': {'benefits': 'https://api.centene.com/SBC/2025/99723MO0090018-01.pdf', 'brochure': 'https://api.centene.com/brochures/2025/99723PremierMO.pdf', 'formulary': 'https://ambetter.homestatehealth.com/resources/pharmacy-resources.html', 'network': 'https://ambetter.homestatehealth.com/findadoc'}}, {'id': '99723MO0110070', 'name': 'Standard Expanded Bronze + Vision + Adult Dental', 'issuer': 'Ambetter from Home State Health', 'premium': 367.02, 'premium_with_credit': 367.02, 'metal_level': 'Bronze', 'type': 'EPO', 'deductible': 7500, 'max_out_of_pocket': 9200, 'hsa_eligible': False, 'quality_rating': 3, 'has_national_network': False, 'key_benefits': {'primary_care': '$50', 'specialist_care': '$100', 'emergency_room': '50% Coinsurance after deductible', 'prescription_drugs': '$25'}, 'issuer_phone': '1-************', 'urls': {'benefits': 'https://api.centene.com/SBC/2025/99723MO0110070-01.pdf', 'brochure': 'https://api.centene.com/brochures/2025/99723PremierMO.pdf', 'formulary': 'https://ambetter.homestatehealth.com/resources/pharmacy-resources.html', 'network': 'https://ambetter.homestatehealth.com/findadoc'}}]\",50% Coinsurance after deductible,$50,$100,$25,,,,False,False,success,\r\nEMP_010,Female,68101,Not Married,0,N,Full-time,,,,,Michelle Anderson,,37.0,,,Omaha,NE,,,,,Sales,,N,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Urban,Desk,Good,N,None,High (>$100K),High,Moderate,Frequent,Y,N,POS,0.6296622,\"High income, good health, HSA familiar, no chronic conditions - HDHP + HSA for tax benefits.\",\"['POS', 'HDHP + HSA', 'PPO']\",\"[0.6296622, 0.3532597, 0.*********]\",['Wellness Programs'],1.0,ML model prediction,\"['Employee Assistance', 'Wellness Programs', 'FSA']\",\"[0.5, 0.5, 0.5]\",True,3,,,,,26289NE0020039,Standard Expanded Bronze,Ambetter from Nebraska Total Care,422,Bronze,HMO,7500,9200,False,3,\"[{'id': '26289NE0020039', 'name': 'Standard Expanded Bronze', 'issuer': 'Ambetter from Nebraska Total Care', 'premium': 422, 'premium_with_credit': 422, 'metal_level': 'Bronze', 'type': 'HMO', 'deductible': 7500, 'max_out_of_pocket': 9200, 'hsa_eligible': False, 'quality_rating': 3, 'has_national_network': False, 'key_benefits': {'prescription_drugs': '$25', 'specialist_care': '$100', 'emergency_room': '50% Coinsurance after deductible', 'primary_care': '$50'}, 'issuer_phone': '1-************', 'urls': {'benefits': 'https://api.centene.com/SBC/2025/26289NE0020039-01.pdf', 'brochure': 'https://api.centene.com/brochures/2025/26289PremierNE.pdf', 'formulary': 'https://ambetter.nebraskatotalcare.com/resources/pharmacy-resources.html', 'network': 'https://ambetter.nebraskatotalcare.com/findadoc'}}, {'id': '20305NE0030073', 'name': 'Elevate by Medica Expanded Bronze Standard', 'issuer': 'Medica', 'premium': 433.35, 'premium_with_credit': 433.35, 'metal_level': 'Bronze', 'type': 'EPO', 'deductible': 7500, 'max_out_of_pocket': 9200, 'hsa_eligible': False, 'quality_rating': 3, 'has_national_network': False, 'key_benefits': {'prescription_drugs': '$25', 'specialist_care': '$100', 'emergency_room': '50% Coinsurance after deductible', 'primary_care': '$50'}, 'issuer_phone': '**************', 'urls': {'benefits': 'https://portal.medica.com/visitor/sbcsearch/docdisplay?plancode=2025-IFBEMBESTNE&uid=FFM.pdf', 'brochure': 'https://www.Medica.com/NEPlans-2025', 'formulary': 'https://www.Medica.com/NEDrugList-2025', 'network': 'https://www.Medica.com/SearchElevateNetwork-2025'}}, {'id': '26289NE0030015', 'name': 'Standard Expanded Bronze + Vision + Adult Dental', 'issuer': 'Ambetter from Nebraska Total Care', 'premium': 433.9, 'premium_with_credit': 433.9, 'metal_level': 'Bronze', 'type': 'HMO', 'deductible': 7500, 'max_out_of_pocket': 9200, 'hsa_eligible': False, 'quality_rating': 3, 'has_national_network': False, 'key_benefits': {'primary_care': '$50', 'specialist_care': '$100', 'emergency_room': '50% Coinsurance after deductible', 'prescription_drugs': '$25'}, 'issuer_phone': '1-************', 'urls': {'benefits': 'https://api.centene.com/SBC/2025/26289NE0030015-01.pdf', 'brochure': 'https://api.centene.com/brochures/2025/26289PremierNE.pdf', 'formulary': 'https://ambetter.nebraskatotalcare.com/resources/pharmacy-resources.html', 'network': 'https://ambetter.nebraskatotalcare.com/findadoc'}}]\",50% Coinsurance after deductible,$50,$100,$25,,,,False,False,success,\r\nEMP_011,Male,84101,Married,2,N,Full-time,18.0,Male,15.0,Female,James Taylor,,44.0,,,Salt Lake City,UT,,,,,Manufacturing,,N,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Rural,Desk,Excellent,Y,Occasional,High (>$100K),Medium,Moderate,Occasional,Y,Y,POS,0.95020616,Middle age with dependents - PPO for family coverage.,\"['POS', 'PPO', 'HMO']\",\"[0.95020616, 0.049228236, 0.00043628848]\",\"['Dental', 'Vision', 'Accident', 'Critical Illness', 'Term Life', 'STD', 'LTD', 'FSA', 'Employee Assistance']\",1.0,Dental: Essential oral health coverage for all employees.; Vision: Eye care coverage for all employees.; Critical Illness: Important protection for age 44 with excellent health status.; Term Life: Essential protection for 2 dependents.; STD: Income protection for High (>$100K) earners or excellent health status.; LTD: Long-term income protection for High (>$100K) earners or age 44+.; FSA: Tax-advantaged account for medical expenses due to health conditions or pregnancy.,\"['Employee Assistance', 'Wellness Programs', 'FSA']\",\"[0.5, 0.5, 0.5]\",True,3,,,,,38927UT0380001,Bronze 2 Advanced HSA: Aetna network of doctors & hospitals,Aetna CVS Health,471.07,Bronze,HMO,6200,7500,True,0,\"[{'id': '38927UT0380001', 'name': 'Bronze 2 Advanced HSA: Aetna network of doctors & hospitals', 'issuer': 'Aetna CVS Health', 'premium': 471.07, 'premium_with_credit': 471.07, 'metal_level': 'Bronze', 'type': 'HMO', 'deductible': 6200, 'max_out_of_pocket': 7500, 'hsa_eligible': True, 'quality_rating': 0, 'has_national_network': False, 'key_benefits': {'primary_care': '35% Coinsurance after deductible', 'specialist_care': '35% Coinsurance after deductible', 'emergency_room': '50% Coinsurance after deductible', 'prescription_drugs': '$3 Copay after deductible'}, 'issuer_phone': '**************', 'urls': {'benefits': 'https://www.aetnacvshealth.com/documents/2025-IFP-777854_38927UT0380001-01_SBC.pdf', 'brochure': 'https://www.aetnacvshealth.com/documents/IFP-plan-brochure-utah-english.pdf', 'formulary': 'http://aet.na/utivl25', 'network': 'https://www.aetna.com/dsepublic/#/contentPage?page=providerSearchLanding&site_id=aetnaivlexchange'}}, {'id': '42261UT0060024', 'name': 'U Health Plus Bronze', 'issuer': 'University of Utah Health Plans', 'premium': 449.79, 'premium_with_credit': 449.79, 'metal_level': 'Bronze', 'type': 'EPO', 'deductible': 9000, 'max_out_of_pocket': 9200, 'hsa_eligible': False, 'quality_rating': 4, 'has_national_network': False, 'key_benefits': {'prescription_drugs': '$30', 'primary_care': 'No Charge', 'specialist_care': '$80 Copay after deductible', 'emergency_room': '50% Coinsurance after deductible'}, 'issuer_phone': '**************', 'urls': {'benefits': 'https://doc.uhealthplan.utah.edu/individual/2025/sbc/uhealthplus/2871148.pdf', 'brochure': 'https://doc.uhealthplan.utah.edu/individual/2025/plan-brochure/uhealthplus/2871148.pdf', 'formulary': 'https://cbg.adaptiverx.com/webSearch/index?key=8F02B26A288102C27BAC82D14C006C6FC54D480F80409B688983F58AD1B2EBAD', 'network': 'https://uuhip.healthtrioconnect.com/public-app/consumer/provdir/entry.page?xsesschk='}}, {'id': '42261UT0060027', 'name': 'U Health Plus Expanded Bronze Standard', 'issuer': 'University of Utah Health Plans', 'premium': 453.03, 'premium_with_credit': 453.03, 'metal_level': 'Bronze', 'type': 'EPO', 'deductible': 7500, 'max_out_of_pocket': 9200, 'hsa_eligible': False, 'quality_rating': 4, 'has_national_network': False, 'key_benefits': {'prescription_drugs': '$25', 'primary_care': '$50', 'specialist_care': '$100', 'emergency_room': '50% Coinsurance after deductible'}, 'issuer_phone': '**************', 'urls': {'benefits': 'https://doc.uhealthplan.utah.edu/individual/2025/sbc/uhealthplus/2871152.pdf', 'brochure': 'https://doc.uhealthplan.utah.edu/individual/2025/plan-brochure/uhealthplus/2871152.pdf', 'formulary': 'https://cbg.adaptiverx.com/webSearch/index?key=8F02B26A288102C27BAC82D14C006C6FC54D480F80409B688983F58AD1B2EBAD', 'network': 'https://uuhip.healthtrioconnect.com/public-app/consumer/provdir/entry.page?xsesschk='}}]\",50% Coinsurance after deductible,35% Coinsurance after deductible,35% Coinsurance after deductible,$3 Copay after deductible,,,,False,False,success,\r\nEMP_012,Female,85001,Not Married,1,N,Full-time,7.0,Female,,,Jessica Thomas,,54.0,,,Phoenix,AZ,,,,,Engineering,,N,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Urban,Desk,Fair,N,None,Low (<$50K),Low,Moderate,Occasional,N,N,HMO,0.865048,Older age - PPO for comprehensive coverage and provider choice.,\"['HMO', 'PPO', 'POS']\",\"[0.865048, 0.11142743, 0.02025086]\",\"['Dental', 'Vision']\",0.5,Default benefits assignment due to prediction error,\"['Dental', 'Vision', 'Hospital Indemnity']\",\"[0.5, 0.5, 0.5]\",True,3,,,,,91450AZ0080122,Standard Expanded Bronze,Ambetter from Arizona Complete Health ,533.31,Bronze,HMO,7500,9200,False,4,\"[{'id': '91450AZ0080122', 'name': 'Standard Expanded Bronze', 'issuer': 'Ambetter from Arizona Complete Health ', 'premium': 533.31, 'premium_with_credit': 8.31, 'metal_level': 'Bronze', 'type': 'HMO', 'deductible': 7500, 'max_out_of_pocket': 9200, 'hsa_eligible': False, 'quality_rating': 4, 'has_national_network': False, 'key_benefits': {'prescription_drugs': '$25', 'primary_care': '$50', 'specialist_care': '$100', 'emergency_room': '50% Coinsurance after deductible'}, 'issuer_phone': '**************', 'urls': {'benefits': 'https://api.centene.com/SBC/2025/91450AZ0080122-01.pdf', 'brochure': 'https://api.centene.com/brochures/2025/91450PremierAZ.pdf', 'formulary': 'https://ambetter.azcompletehealth.com/resources/pharmacy-resources.html', 'network': 'https://ambetter.azcompletehealth.com/findadoc'}}, {'id': '40702AZ0060019', 'name': 'UHC Bronze Standard', 'issuer': 'UnitedHealthcare', 'premium': 526.34, 'premium_with_credit': 1.34, 'metal_level': 'Bronze', 'type': 'HMO', 'deductible': 7500, 'max_out_of_pocket': 9200, 'hsa_eligible': False, 'quality_rating': 3, 'has_national_network': False, 'key_benefits': {'prescription_drugs': '$25', 'specialist_care': '$100', 'emergency_room': '50% Coinsurance after deductible', 'primary_care': '$50'}, 'issuer_phone': '**************', 'urls': {'benefits': 'https://www.uhc.com/ifp/sbc.40702AZ0060019-01.en.2025.pdf', 'brochure': 'https://www.uhc.com/azplanbrochure2025', 'formulary': 'https://www.uhc.com/xazdruglist2025', 'network': 'https://www.uhc.com/xazdocfindg2025'}}, {'id': '85533AZ0010001', 'name': 'Imperial Standard Bronze', 'issuer': 'Imperial Insurance Companies, Inc.', 'premium': 522.04, 'premium_with_credit': 0, 'metal_level': 'Bronze', 'type': 'HMO', 'deductible': 7500, 'max_out_of_pocket': 9200, 'hsa_eligible': False, 'quality_rating': 0, 'has_national_network': False, 'key_benefits': {'specialist_care': '$100', 'emergency_room': '50% Coinsurance after deductible', 'primary_care': '$50', 'prescription_drugs': '$25'}, 'issuer_phone': '**************', 'urls': {'benefits': 'https://documents.imperialhealthplan.com/2025/Marketplace/Provider%20Directory/Arizona/Imperial%20Standard%20Bronze_85533AZ0010001-01_CY2025.pdf', 'brochure': 'https://exchange.imperialhealthplan.com/wp-content/uploads/2023/08/Plan-brochure-Arizona.pdf', 'formulary': 'https://client.formularynavigator.com/Search.aspx?siteCode=**********', 'network': 'https://exchange.imperialhealthplan.com/arizona/hmo-exchange/online-provider-directory/'}}]\",50% Coinsurance after deductible,$50,$100,$25,,,,False,False,success,\r\nEMP_013,Male,19701,Married,2,Y,Full-time,12.0,Male,8.0,Female,William Jackson,,56.0,,,Bear,DE,,,,,Information Technology,,N,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Rural,Desk,Good,N,None,High (>$100K),Low,Sedentary,Occasional,Y,Y,POS,0.8335019,\"High income, good health, HSA familiar, no chronic conditions - HDHP + HSA for tax benefits.\",\"['POS', 'HDHP + HSA', 'PPO']\",\"[0.8335019, 0.1459916, 0.*********]\",\"['Dental', 'Vision']\",0.5,Default benefits assignment due to prediction error,\"['Dental', 'Vision', 'Hospital Indemnity']\",\"[0.5, 0.5, 0.5]\",True,3,,,,,64004DE0090001,Premier Bronze HSA,Ambetter Health of Delaware,884.1,Bronze,EPO,5200,8050,True,0,\"[{'id': '64004DE0090001', 'name': 'Premier Bronze HSA', 'issuer': 'Ambetter Health of Delaware', 'premium': 884.1, 'premium_with_credit': 759.1, 'metal_level': 'Bronze', 'type': 'EPO', 'deductible': 5200, 'max_out_of_pocket': 8050, 'hsa_eligible': True, 'quality_rating': 0, 'has_national_network': False, 'key_benefits': {'prescription_drugs': '$3 Copay after deductible', 'specialist_care': '$100 Copay after deductible', 'emergency_room': '50% Coinsurance after deductible', 'primary_care': '$60 Copay after deductible'}, 'issuer_phone': '**************', 'urls': {'benefits': 'https://api.centene.com/SBC/2025/64004DE0090001-01.pdf', 'brochure': 'https://api.centene.com/brochures/2025/64004PremierDE.pdf', 'formulary': 'https://www.ambetterhealthofdelaware.com/resources/pharmacy-resources.html', 'network': 'https://www.ambetterhealthofdelaware.com/findadoc'}}, {'id': '64004DE0090004', 'name': 'Standard Expanded Bronze', 'issuer': 'Ambetter Health of Delaware', 'premium': 864.41, 'premium_with_credit': 739.41, 'metal_level': 'Bronze', 'type': 'EPO', 'deductible': 7500, 'max_out_of_pocket': 9200, 'hsa_eligible': False, 'quality_rating': 0, 'has_national_network': False, 'key_benefits': {'prescription_drugs': '$25', 'specialist_care': '$100', 'emergency_room': '50% Coinsurance after deductible', 'primary_care': '$50'}, 'issuer_phone': '**************', 'urls': {'benefits': 'https://api.centene.com/SBC/2025/64004DE0090004-01.pdf', 'brochure': 'https://api.centene.com/brochures/2025/64004PremierDE.pdf', 'formulary': 'https://www.ambetterhealthofdelaware.com/resources/pharmacy-resources.html', 'network': 'https://www.ambetterhealthofdelaware.com/findadoc'}}, {'id': '64004DE0090002', 'name': 'Everyday Bronze', 'issuer': 'Ambetter Health of Delaware', 'premium': 869.94, 'premium_with_credit': 744.94, 'metal_level': 'Bronze', 'type': 'EPO', 'deductible': 8450, 'max_out_of_pocket': 9200, 'hsa_eligible': False, 'quality_rating': 0, 'has_national_network': False, 'key_benefits': {'prescription_drugs': '$3', 'primary_care': '$40', 'specialist_care': '$90', 'emergency_room': '50% Coinsurance after deductible'}, 'issuer_phone': '**************', 'urls': {'benefits': 'https://api.centene.com/SBC/2025/64004DE0090002-01.pdf', 'brochure': 'https://api.centene.com/brochures/2025/64004PremierDE.pdf', 'formulary': 'https://www.ambetterhealthofdelaware.com/resources/pharmacy-resources.html', 'network': 'https://www.ambetterhealthofdelaware.com/findadoc'}}]\",50% Coinsurance after deductible,$60 Copay after deductible,$100 Copay after deductible,$3 Copay after deductible,,,,False,False,success,\r\nEMP_014,Female,59718,Not Married,0,N,Full-time,,,,,Ashley White,,51.0,,,Bozeman,MT,,,,,Finance,,N,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Rural,Desk,Fair,N,None,Medium ($50K–$100K),Low,Active,Occasional,Y,N,POS,0.72543555,Older age - PPO for comprehensive coverage and provider choice.,\"['POS', 'HDHP + HSA', 'PPO']\",\"[0.72543555, 0.26776546, 0.**********]\",\"['Dental', 'Vision']\",0.5,Default benefits assignment due to prediction error,\"['Dental', 'Vision', 'Hospital Indemnity']\",\"[0.5, 0.5, 0.5]\",True,3,,,,,30751MT0670012,Blue Focus Bronze POS℠ 205,Blue Cross and Blue Shield of Montana,564.39,Bronze,POS,4900,9200,False,0,\"[{'id': '30751MT0670012', 'name': 'Blue Focus Bronze POS℠ 205', 'issuer': 'Blue Cross and Blue Shield of Montana', 'premium': 564.39, 'premium_with_credit': 272.39, 'metal_level': 'Bronze', 'type': 'POS', 'deductible': 4900, 'max_out_of_pocket': 9200, 'hsa_eligible': False, 'quality_rating': 0, 'has_national_network': False, 'key_benefits': {'primary_care': '$45', 'specialist_care': '50% Coinsurance after deductible', 'emergency_room': '$1000 Copay with deductible/50% Coinsurance after deductible', 'prescription_drugs': 'No Charge After Deductible'}, 'issuer_phone': '1-************', 'urls': {'benefits': 'https://www.bcbsmt.com/sbc/ind/sbc-bosh31blcimtp-mt-2025.pdf', 'brochure': 'https://www.bcbsmt.com/plan-docs/ind/brochure-mt-2025.pdf', 'formulary': 'https://www.myprime.com/content/dam/prime/memberportal/WebDocs/2025/Formularies/HIM/2025_MT_6T_HIM.pdf', 'network': 'https://my.providerfinderonline.com/?ci=mt-bluefocuspos-retail&corp_code=MT'}}, {'id': '30751MT0670016', 'name': 'Blue Focus Bronze POS℠ 705', 'issuer': 'Blue Cross and Blue Shield of Montana', 'premium': 538.59, 'premium_with_credit': 246.59, 'metal_level': 'Bronze', 'type': 'POS', 'deductible': 9200, 'max_out_of_pocket': 9200, 'hsa_eligible': False, 'quality_rating': 0, 'has_national_network': False, 'key_benefits': {'prescription_drugs': 'No Charge After Deductible', 'primary_care': 'No Charge After Deductible', 'specialist_care': 'No Charge After Deductible', 'emergency_room': 'No Charge After Deductible'}, 'issuer_phone': '1-************', 'urls': {'benefits': 'https://www.bcbsmt.com/sbc/ind/sbc-bosa61blcimtp-mt-2025.pdf', 'brochure': 'https://www.bcbsmt.com/plan-docs/ind/brochure-mt-2025.pdf', 'formulary': 'https://www.myprime.com/content/dam/prime/memberportal/WebDocs/2025/Formularies/HIM/2025_MT_6T_HIM.pdf', 'network': 'https://my.providerfinderonline.com/?ci=mt-bluefocuspos-retail&corp_code=MT'}}, {'id': '30751MT0670018', 'name': 'Blue Focus Bronze POS℠ Standard', 'issuer': 'Blue Cross and Blue Shield of Montana', 'premium': 576.1, 'premium_with_credit': 284.1, 'metal_level': 'Bronze', 'type': 'POS', 'deductible': 7500, 'max_out_of_pocket': 9200, 'hsa_eligible': False, 'quality_rating': 0, 'has_national_network': False, 'key_benefits': {'prescription_drugs': '$25', 'primary_care': '$50', 'specialist_care': '$100', 'emergency_room': '50% Coinsurance after deductible'}, 'issuer_phone': '1-************', 'urls': {'benefits': 'https://www.bcbsmt.com/sbc/ind/sbc-bosa86blcimtp-mt-2025.pdf', 'brochure': 'https://www.bcbsmt.com/plan-docs/ind/brochure-mt-2025.pdf', 'formulary': 'https://www.myprime.com/content/dam/prime/memberportal/WebDocs/2025/Formularies/HIM/2025_MT_4T_HIM.pdf', 'network': 'https://my.providerfinderonline.com/?ci=mt-bluefocuspos-retail&corp_code=MT'}}]\",$1000 Copay with deductible/50% Coinsurance after deductible,$45,50% Coinsurance after deductible,No Charge After Deductible,,,,False,False,success,\r\nEMP_015,Male,82414,Married,1,N,Full-time,11.0,Male,,,Daniel Harris,,63.0,,,Cody,WY,,,,,Engineering,,N,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Rural,Desk,Good,N,None,Medium ($50K–$100K),Low,Moderate,Occasional,N,N,PPO,0.63481605,\"Medium income, good health - PPO for balanced cost and flexibility.\",\"['PPO', 'POS', 'HMO']\",\"[0.63481605, 0.3639259, 0.**********]\",\"['Dental', 'Vision']\",0.5,Default benefits assignment due to prediction error,\"['Dental', 'Vision', 'Hospital Indemnity']\",\"[0.5, 0.5, 0.5]\",True,1,,,,,11269WY0070026,BlueSelect Bronze Basic,Blue Cross Blue Shield of Wyoming,1544.05,Bronze,PPO,9100,9100,False,2,\"[{'id': '11269WY0070026', 'name': 'BlueSelect Bronze Basic', 'issuer': 'Blue Cross Blue Shield of Wyoming', 'premium': 1544.05, 'premium_with_credit': 37.05, 'metal_level': 'Bronze', 'type': 'PPO', 'deductible': 9100, 'max_out_of_pocket': 9100, 'hsa_eligible': False, 'quality_rating': 2, 'has_national_network': False, 'key_benefits': {'specialist_care': 'No Charge After Deductible', 'emergency_room': 'No Charge After Deductible', 'primary_care': 'No Charge After Deductible', 'prescription_drugs': 'No Charge After Deductible'}, 'issuer_phone': '1-************', 'urls': {'benefits': 'https://shop.yourwyoblue.com/content/sbcs/2025/WY/Individual/BlueSelectBronzeBasicIX.pdf', 'brochure': 'https://bcbswy.com/files/2025-BlueSelect-Ind-Fam.pdf', 'formulary': 'https://www.myprime.com/content/dam/prime/memberportal/WebDocs/2025/Formularies/HIM/2025_WY_4T_HealthInsuranceMarketplaceBlueSelect.pdf', 'network': 'https://bcbswy.sapphirecareselect.com'}}]\",No Charge After Deductible,No Charge After Deductible,No Charge After Deductible,No Charge After Deductible,,,,False,False,success,\r\nEMP_016,Female,46201,Not Married,0,N,Full-time,,,,,Stephanie Martin,,49.0,,,Indianapolis,IN,,,,,Engineering,,N,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Rural,Desk,Good,Y,Regular,Medium ($50K–$100K),Low,Active,Rare,N,N,PPO,0.9640782,\"Medium income, good health - PPO for balanced cost and flexibility.\",\"['PPO', 'HMO', 'HDHP + HSA']\",\"[0.9640782, 0.033901323, 0.**********]\",\"['Dental', 'Vision']\",0.5,Default benefits assignment due to prediction error,\"['Dental', 'Vision', 'Hospital Indemnity']\",\"[0.5, 0.5, 0.5]\",True,3,,,,,17575IN0700045,Anthem Bronze Essential 9200 (+ Incentives),Anthem Blue Cross and Blue Shield,440.18,Bronze,HMO,9200,9200,False,3,\"[{'id': '17575IN0700045', 'name': 'Anthem Bronze Essential 9200 (+ Incentives)', 'issuer': 'Anthem Blue Cross and Blue Shield', 'premium': 440.18, 'premium_with_credit': 440.18, 'metal_level': 'Bronze', 'type': 'HMO', 'deductible': 9200, 'max_out_of_pocket': 9200, 'hsa_eligible': False, 'quality_rating': 3, 'has_national_network': False, 'key_benefits': {'primary_care': 'No Charge After Deductible', 'specialist_care': 'No Charge After Deductible', 'emergency_room': 'No Charge After Deductible', 'prescription_drugs': 'No Charge After Deductible'}, 'issuer_phone': '1-************', 'urls': {'benefits': 'https://sbc.anthem.com/dpsdeeplink/deepLink/AnthemBronzeEssential9200Incentives/864B/English/DG166705364851.pdf', 'brochure': 'http://anthem.ly/IND-IN-EN-KIT-2025', 'formulary': 'https://www.anthem.com/INSelectdrugtier4', 'network': 'https://www.anthem.com/find-care/?alphaprefix=E7E'}}, {'id': '17575IN0700101', 'name': 'Anthem Bronze Essential 9200 Adult Dental/Vision (+ Incentives)', 'issuer': 'Anthem Blue Cross and Blue Shield', 'premium': 451.56, 'premium_with_credit': 451.56, 'metal_level': 'Bronze', 'type': 'HMO', 'deductible': 9200, 'max_out_of_pocket': 9200, 'hsa_eligible': False, 'quality_rating': 3, 'has_national_network': False, 'key_benefits': {'prescription_drugs': 'No Charge After Deductible', 'primary_care': 'No Charge After Deductible', 'specialist_care': 'No Charge After Deductible', 'emergency_room': 'No Charge After Deductible'}, 'issuer_phone': '1-************', 'urls': {'benefits': 'https://sbc.anthem.com/dpsdeeplink/deepLink/AnthemBronzeEssential9200AdultDentalVisionIncentives/864L/English/DG166705365415.pdf', 'brochure': 'http://anthem.ly/IND-IN-EN-KIT-2025', 'formulary': 'https://www.anthem.com/INSelectdrugtier4', 'network': 'https://www.anthem.com/find-care/?alphaprefix=E7E'}}, {'id': '17575IN0760005', 'name': 'Anthem Bronze Essential POS 5500 ($0 Virtual PCP + $0 Select Drugs + Incentives)', 'issuer': 'Anthem Blue Cross and Blue Shield', 'premium': 454.24, 'premium_with_credit': 454.24, 'metal_level': 'Bronze', 'type': 'POS', 'deductible': 5500, 'max_out_of_pocket': 9200, 'hsa_eligible': False, 'quality_rating': 2, 'has_national_network': False, 'key_benefits': {'specialist_care': '40% Coinsurance after deductible', 'emergency_room': '$500 Copay after deductible/40% Coinsurance after deductible', 'primary_care': '$50/40% Coinsurance after deductible', 'prescription_drugs': '40% Coinsurance after deductible'}, 'issuer_phone': '1-************', 'urls': {'benefits': 'https://sbc.anthem.com/dpsdeeplink/deepLink/AnthemBronzeEssentialPOS55000VirtualPCP0SelectDrugsIncentives/86BW/English/DG166705364852.pdf', 'brochure': 'http://anthem.ly/IND-IN-EN-KIT-2025', 'formulary': 'https://www.anthem.com/INSelectdrugtier4', 'network': 'https://www.anthem.com/find-care/?alphaprefix=E7E'}}]\",No Charge After Deductible,No Charge After Deductible,No Charge After Deductible,No Charge After Deductible,,,,False,False,success,\r\nEMP_017,Male,48201,Married,2,N,Full-time,9.0,Female,6.0,Male,Matthew Thompson,,39.0,,,Detroit,MI,,,,,Engineering,,N,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Rural,Desk,Excellent,N,None,Low (<$50K),Low,Moderate,Occasional,N,N,PPO,0.8825032,Middle age with dependents - PPO for family coverage.,\"['PPO', 'HMO', 'POS']\",\"[0.8825032, 0.056728434, 0.050993312]\",\"['Dental', 'Vision']\",0.5,Default benefits assignment due to prediction error,\"['Dental', 'Vision', 'Hospital Indemnity']\",\"[0.5, 0.5, 0.5]\",True,3,,,,,98185MI0180032,Blue Cross® Local HMO Bronze Secure,Blue Care Network of Michigan,281.69,Bronze,HMO,9200,9200,False,4,\"[{'id': '98185MI0180032', 'name': 'Blue Cross® Local HMO Bronze Secure', 'issuer': 'Blue Care Network of Michigan', 'premium': 281.69, 'premium_with_credit': 21.69, 'metal_level': 'Bronze', 'type': 'HMO', 'deductible': 9200, 'max_out_of_pocket': 9200, 'hsa_eligible': False, 'quality_rating': 4, 'has_national_network': False, 'key_benefits': {'prescription_drugs': 'No Charge After Deductible', 'primary_care': 'No Charge After Deductible', 'specialist_care': 'No Charge After Deductible', 'emergency_room': 'No Charge After Deductible'}, 'issuer_phone': '1-************', 'urls': {'benefits': 'https://www.bcbsm.com/amslibs/content/dam/public/marketplace/2025-individual/sbc/local-bronze-secure-sbc.pdf', 'brochure': 'https://www.bcbsm.com/individuals/plans/medical/2025/bronze/hmo-secure/?network=local', 'formulary': 'https://www.bcbsm.com/2025-select-hmo-druglist', 'network': 'https://www.bcbsm.com/marketplace/local-hmo/'}}, {'id': '58594MI0030023', 'name': 'Standard Expanded Bronze', 'issuer': 'Ambetter from Meridian', 'premium': 283.11, 'premium_with_credit': 23.11, 'metal_level': 'Bronze', 'type': 'HMO', 'deductible': 7500, 'max_out_of_pocket': 9200, 'hsa_eligible': False, 'quality_rating': 3, 'has_national_network': False, 'key_benefits': {'primary_care': '$50', 'specialist_care': '$100', 'emergency_room': '50% Coinsurance after deductible', 'prescription_drugs': '$25'}, 'issuer_phone': '**************', 'urls': {'benefits': 'https://api.centene.com/SBC/2025/58594MI0030023-01.pdf', 'brochure': 'https://api.centene.com/brochures/2025/58594PremierMI.pdf', 'formulary': 'https://www.ambettermeridian.com/resources/pharmacy-resources.html', 'network': 'https://www.ambettermeridian.com/findadoc'}}, {'id': '58594MI0030013', 'name': 'Everyday Bronze', 'issuer': 'Ambetter from Meridian', 'premium': 288, 'premium_with_credit': 28, 'metal_level': 'Bronze', 'type': 'HMO', 'deductible': 8450, 'max_out_of_pocket': 9200, 'hsa_eligible': False, 'quality_rating': 3, 'has_national_network': False, 'key_benefits': {'prescription_drugs': '$3', 'primary_care': '$40', 'emergency_room': '50% Coinsurance after deductible', 'specialist_care': '$90'}, 'issuer_phone': '**************', 'urls': {'benefits': 'https://api.centene.com/SBC/2025/58594MI0030013-01.pdf', 'brochure': 'https://api.centene.com/brochures/2025/58594PremierMI.pdf', 'formulary': 'https://www.ambettermeridian.com/resources/pharmacy-resources.html', 'network': 'https://www.ambettermeridian.com/findadoc'}}]\",No Charge After Deductible,No Charge After Deductible,No Charge After Deductible,No Charge After Deductible,,,,False,False,success,\r\nEMP_018,Female,39201,Not Married,0,N,Part-time,,,,,Nicole Garcia,,45.0,,,Jackson,MS,,,,,Finance,,N,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Rural,Desk,Fair,N,None,High (>$100K),Medium,Moderate,Rare,Y,N,HDHP + HSA,0.96905375,Middle age with dependents - PPO for family coverage.,\"['HDHP + HSA', 'PPO', 'HMO']\",\"[0.96905375, 0.026239464, 0.**********]\",\"['Dental', 'Vision']\",0.5,Default benefits assignment due to prediction error,\"['Dental', 'Vision', 'Hospital Indemnity']\",\"[0.5, 0.5, 0.5]\",True,1,,,,,14624MS0010006,Savings Bronze 7700,Primewell Health Services of Mississippi,503.7,Bronze,POS,7700,7700,True,3,\"[{'id': '14624MS0010006', 'name': 'Savings Bronze 7700', 'issuer': 'Primewell Health Services of Mississippi', 'premium': 503.7, 'premium_with_credit': 503.7, 'metal_level': 'Bronze', 'type': 'POS', 'deductible': 7700, 'max_out_of_pocket': 7700, 'hsa_eligible': True, 'quality_rating': 3, 'has_national_network': False, 'key_benefits': {'primary_care': 'No Charge After Deductible', 'specialist_care': 'No Charge After Deductible', 'emergency_room': 'No Charge After Deductible', 'prescription_drugs': 'No Charge After Deductible'}, 'issuer_phone': '1-************', 'urls': {'benefits': 'https://PrimewellHealth.com/documents/Marketplace/2025INDSavingsBronze7700SummaryOfBenefitsAndCoverage.pdf', 'brochure': 'https://PrimewellHealth.com/documents/Marketplace/2025MSMarketplaceINDPlanFinder.pdf', 'formulary': 'https://PrimewellHealth.com/documents/Marketplace/2025CommercialAndExchangeMemberListOfCoveredDrugs(Formulary).pdf', 'network': 'https://PrimewellHealth.com/provider/providersearch'}}]\",No Charge After Deductible,No Charge After Deductible,No Charge After Deductible,No Charge After Deductible,,,,False,False,success,\r\nEMP_019,Male,71601,Married,2,N,Full-time,14.0,Female,11.0,Male,Anthony Martinez,,29.0,,,Pine Bluff,AR,,,,,Information Technology,,N,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Rural,Desk,Good,N,None,Medium ($50K–$100K),Low,Active,Occasional,N,Y,POS,0.6012608,\"Medium income, good health - PPO for balanced cost and flexibility.\",\"['POS', 'PPO', 'HMO']\",\"[0.6012608, 0.36842278, 0.026105843]\",\"['Dental', 'Vision']\",0.5,Default benefits assignment due to prediction error,\"['Dental', 'Vision', 'Hospital Indemnity']\",\"[0.5, 0.5, 0.5]\",True,3,,,,,13262AR0230007,HA Bronze Exp Standardized,Health Advantage,349.15,Bronze,POS,7500,9200,False,2,\"[{'id': '13262AR0230007', 'name': 'HA Bronze Exp Standardized', 'issuer': 'Health Advantage', 'premium': 349.15, 'premium_with_credit': 349.15, 'metal_level': 'Bronze', 'type': 'POS', 'deductible': 7500, 'max_out_of_pocket': 9200, 'hsa_eligible': False, 'quality_rating': 2, 'has_national_network': False, 'key_benefits': {'prescription_drugs': '$25', 'emergency_room': '50% Coinsurance after deductible', 'specialist_care': '$100', 'primary_care': '$50'}, 'issuer_phone': '1-************', 'urls': {'benefits': 'https://healthadvantage-hmo.com/docs/librariesprovider6/members/sbcpdffiles/2025/60030.pdf', 'brochure': 'https://www.healthadvantage-hmo.com/ha-plans-2025', 'formulary': 'https://www.healthadvantage-hmo.com/ha-formulary-2025', 'network': 'https://www.healthadvantage-hmo.com/members/network-selection'}}, {'id': '48772AR0010004', 'name': 'Octave Bronze Exp Standardized', 'issuer': 'Octave', 'premium': 340.2, 'premium_with_credit': 340.2, 'metal_level': 'Bronze', 'type': 'POS', 'deductible': 7500, 'max_out_of_pocket': 9200, 'hsa_eligible': False, 'quality_rating': 0, 'has_national_network': False, 'key_benefits': {'prescription_drugs': '$25', 'primary_care': '$50', 'specialist_care': '$100', 'emergency_room': '50% Coinsurance after deductible'}, 'issuer_phone': '1-************', 'urls': {'benefits': 'https://arkansasoctave.com/docs/arkansasoctavelibraries/members/sbcpdffiles/2025/70023.pdf', 'brochure': 'https://www.arkansasoctave.com/plans-2025', 'formulary': 'https://arkansasoctave.com/metallic-formulary-2025', 'network': 'https://www.arkansasoctave.com/members/network-selection/'}}, {'id': '48772AR0010004', 'name': 'Octave Bronze Exp Standardized', 'issuer': 'Octave', 'premium': 340.2, 'premium_with_credit': 340.2, 'metal_level': 'Bronze', 'type': 'POS', 'deductible': 7500, 'max_out_of_pocket': 9200, 'hsa_eligible': False, 'quality_rating': 0, 'has_national_network': False, 'key_benefits': {'prescription_drugs': '$25', 'primary_care': '$50', 'specialist_care': '$100', 'emergency_room': '50% Coinsurance after deductible'}, 'issuer_phone': '1-************', 'urls': {'benefits': 'https://arkansasoctave.com/docs/arkansasoctavelibraries/members/sbcpdffiles/2025/70023.pdf', 'brochure': 'https://www.arkansasoctave.com/plans-2025', 'formulary': 'https://arkansasoctave.com/metallic-formulary-2025', 'network': 'https://www.arkansasoctave.com/members/network-selection/'}}]\",50% Coinsurance after deductible,$50,$100,$25,,,,False,False,success,\r\nEMP_020,Female,66201,Not Married,0,N,Full-time,,,,,Samantha Robinson,,54.0,,,Shawnee Mission,KS,,,,,Manufacturing,,N,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Rural,Desk,Fair,N,Occasional,High (>$100K),Medium,Moderate,Rare,Y,Y,PPO,0.9706589,Older age - PPO for comprehensive coverage and provider choice.,\"['PPO', 'HDHP + HSA', 'POS']\",\"[0.9706589, 0.018115649, 0.007327522]\",\"['Dental', 'Vision']\",0.5,Default benefits assignment due to prediction error,\"['Dental', 'Vision', 'Hospital Indemnity']\",\"[0.5, 0.5, 0.5]\",True,3,,,,,34368KS0110068,Standard Expanded Bronze,Ambetter from Sunflower Health Plan,606.67,Bronze,EPO,7500,9200,False,3,\"[{'id': '34368KS0110068', 'name': 'Standard Expanded Bronze', 'issuer': 'Ambetter from Sunflower Health Plan', 'premium': 606.67, 'premium_with_credit': 606.67, 'metal_level': 'Bronze', 'type': 'EPO', 'deductible': 7500, 'max_out_of_pocket': 9200, 'hsa_eligible': False, 'quality_rating': 3, 'has_national_network': False, 'key_benefits': {'specialist_care': '$100', 'emergency_room': '50% Coinsurance after deductible', 'primary_care': '$50', 'prescription_drugs': '$25'}, 'issuer_phone': '1-************', 'urls': {'benefits': 'https://api.centene.com/SBC/2025/34368KS0110068-01.pdf', 'brochure': 'https://api.centene.com/brochures/2025/34368PremierKS.pdf', 'formulary': 'https://ambetter.sunflowerhealthplan.com/resources/pharmacy-resources.html', 'network': 'https://ambetter.sunflowerhealthplan.com/findadoc'}}, {'id': '34368KS0110043', 'name': 'Everyday Bronze', 'issuer': 'Ambetter from Sunflower Health Plan', 'premium': 612.21, 'premium_with_credit': 612.21, 'metal_level': 'Bronze', 'type': 'EPO', 'deductible': 8450, 'max_out_of_pocket': 9200, 'hsa_eligible': False, 'quality_rating': 3, 'has_national_network': False, 'key_benefits': {'prescription_drugs': '$3', 'primary_care': '$40', 'specialist_care': '$90', 'emergency_room': '50% Coinsurance after deductible'}, 'issuer_phone': '1-************', 'urls': {'benefits': 'https://api.centene.com/SBC/2025/34368KS0110043-01.pdf', 'brochure': 'https://api.centene.com/brochures/2025/34368PremierKS.pdf', 'formulary': 'https://ambetter.sunflowerhealthplan.com/resources/pharmacy-resources.html', 'network': 'https://ambetter.sunflowerhealthplan.com/findadoc'}}, {'id': '34368KS0120069', 'name': 'Standard Expanded Bronze + Vision + Adult Dental', 'issuer': 'Ambetter from Sunflower Health Plan', 'premium': 626.69, 'premium_with_credit': 626.69, 'metal_level': 'Bronze', 'type': 'EPO', 'deductible': 7500, 'max_out_of_pocket': 9200, 'hsa_eligible': False, 'quality_rating': 3, 'has_national_network': False, 'key_benefits': {'prescription_drugs': '$25', 'specialist_care': '$100', 'emergency_room': '50% Coinsurance after deductible', 'primary_care': '$50'}, 'issuer_phone': '1-************', 'urls': {'benefits': 'https://api.centene.com/SBC/2025/34368KS0120069-01.pdf', 'brochure': 'https://api.centene.com/brochures/2025/34368PremierKS.pdf', 'formulary': 'https://ambetter.sunflowerhealthplan.com/resources/pharmacy-resources.html', 'network': 'https://ambetter.sunflowerhealthplan.com/findadoc'}}]\",50% Coinsurance after deductible,$50,$100,$25,,,,False,False,success,\r\n", "total_employees": 20, "total_columns": 177, "has_predictions": true, "prediction_columns": ["predicted_plan_type", "plan_confidence", "top_3_plan_confidences", "predicted_benefits", "benefits_confidence", "top_3_benefits_confidences"]}, "metadata": {"pipeline_version": "2.0_with_health_plans", "total_steps": 6, "steps_completed": ["parsing", "mapping", "pattern_identification", "preprocessing", "enrichment_prediction", "health_plan_integration"], "processing_order": ["step_1_parsing", "step_2_mapping", "step_3_pattern", "step_4_preprocessing", "step_5_enrichment_prediction", "step_6_health_plans"], "prediction_method": "ml_models", "health_plan_integration_success": true, "final_dataframe_shape": [20, 177], "processing_time_seconds": 0, "file_info": {"filename": "covered_states_census.csv", "size": 2070, "rows": 20, "columns": 16, "original_column_names": ["employee_id", "first_name", "last_name", "age", "gender", "zipcode", "marital_status", "income_tier", "dept_count", "tobacco_use", "employment_type", "job_type", "dept_1_age", "dept_1_gender", "dept_2_age", "dept_2_gender"]}, "pattern_info": {"confidence": 1.1, "reason": "Found 4 dependent columns; Found numbered dependent patterns: [1, 2]; Found 'dept_count' column; No duplicate employee_ids (consistent with single row per employee)"}, "mapping_result": {"success": true, "mapping": {"employee_id": "employee_id", "first_name": "first_name", "last_name": "last_name", "gender": "gender", "zipcode": "zipcode", "marital_status": "marital_status", "dept_count": "dept_count", "tobacco_use": "tobacco_use", "employment_type": "employment_type", "dept_1_age": "dept_1_age", "dept_1_gender": "dept_1_gender", "dept_2_age": "dept_2_age", "dept_2_gender": "dept_2_gender"}, "mapped_fields": ["employee_id", "first_name", "last_name", "gender", "zipcode", "marital_status", "dept_count", "tobacco_use", "employment_type", "dept_1_age", "dept_1_gender", "dept_2_age", "dept_2_gender"], "unmapped_fields": ["age", "income_tier", "job_type"], "validation": {"is_valid": true, "mapped_count": 13, "unmapped_count": 3}}, "preprocessing_result": {"success": true, "preprocessed_data": "   employee_id  gender  zipcode marital_status  dept_count tobacco_use employment_type  dept_1_age dept_1_gender  dept_2_age dept_2_gender  ... relationship_type_18 dept_19 dept_19_dob dept_19_age dept_19_gender relationship_type_19 dept_20 dept_20_dob dept_20_age dept_20_gender relationship_type_20\n0      EMP_001    Male    75201        Married           2           N       Full-time         8.0          Male        12.0        Female  ...                 None    None        None        None           None                 None    None        None        None           None                 None\n1      EMP_002  Female    33101    Not Married           0           N       Full-time         NaN          None         NaN          None  ...                 None    None        None        None           None                 None    None        None        None           None                 None\n2      EMP_003    Male    30301        Married           2           N       Full-time        15.0        Female        10.0          Male  ...                 None    None        None        None           None                 None    None        None        None           None                 None\n3      EMP_004  Female    43201    Not Married           1           N       Full-time         3.0        Female         NaN          None  ...                 None    None        None        None           None                 None    None        None        None           None                 None\n4      EMP_005    Male    27601        Married           2           Y       Full-time        17.0          Male        14.0        Female  ...                 None    None        None        None           None                 None    None        None        None           None                 None\n5      EMP_006  Female    37201    Not Married           0           N       Part-time         NaN          None         NaN          None  ...                 None    None        None        None           None                 None    None        None        None           None                 None\n6      EMP_007    Male    35201        Married           1           N       Full-time         6.0          Male         NaN          None  ...                 None    None        None        None           None                 None    None        None        None           None                 None\n7      EMP_008  Female    73101    Not Married           0           N       Full-time         NaN          None         NaN          None  ...                 None    None        None        None           None                 None    None        None        None           None                 None\n8      EMP_009    Male    64101        Married           2           N       Full-time        16.0        Female        13.0          Male  ...                 None    None        None        None           None                 None    None        None        None           None                 None\n9      EMP_010  Female    68101    Not Married           0           N       Full-time         NaN          None         NaN          None  ...                 None    None        None        None           None                 None    None        None        None           None                 None\n10     EMP_011    Male    84101        Married           2           N       Full-time        18.0          Male        15.0        Female  ...                 None    None        None        None           None                 None    None        None        None           None                 None\n11     EMP_012  Female    85001    Not Married           1           N       Full-time         7.0        Female         NaN          None  ...                 None    None        None        None           None                 None    None        None        None           None                 None\n12     EMP_013    Male    19701        Married           2           Y       Full-time        12.0          Male         8.0        Female  ...                 None    None        None        None           None                 None    None        None        None           None                 None\n13     EMP_014  Female    59718    Not Married           0           N       Full-time         NaN          None         NaN          None  ...                 None    None        None        None           None                 None    None        None        None           None                 None\n14     EMP_015    Male    82414        Married           1           N       Full-time        11.0          Male         NaN          None  ...                 None    None        None        None           None                 None    None        None        None           None                 None\n15     EMP_016  Female    46201    Not Married           0           N       Full-time         NaN          None         NaN          None  ...                 None    None        None        None           None                 None    None        None        None           None                 None\n16     EMP_017    Male    48201        Married           2           N       Full-time         9.0        Female         6.0          Male  ...                 None    None        None        None           None                 None    None        None        None           None                 None\n17     EMP_018  Female    39201    Not Married           0           N       Part-time         NaN          None         NaN          None  ...                 None    None        None        None           None                 None    None        None        None           None                 None\n18     EMP_019    Male    71601        Married           2           N       Full-time        14.0        Female        11.0          Male  ...                 None    None        None        None           None                 None    None        None        None           None                 None\n19     EMP_020  Female    66201    Not Married           0           N       Full-time         NaN          None         NaN          None  ...                 None    None        None        None           None                 None    None        None        None           None                 None\n\n[20 rows x 128 columns]", "validation": {"is_valid": true, "errors": [], "error_rows": [], "total_errors": 0}, "summary": {"original_rows": 20, "original_columns": 13, "processed_rows": 20, "processed_columns": 128, "processing_time_seconds": 3.4, "validation_passed": true, "validation_errors": 0, "error_rows": 0, "fields_processed": {"name_fields": true, "age_converted": true, "gender_standardized": true, "addresses_cleaned": true, "zipcode_validated": true}, "data_quality": {"complete_records": 20, "missing_data_rows": 0}}}, "return_dataframe": true}}}