{"file_info": {"original_filename": "row1_member_level.csv", "processing_timestamp": "2025-07-15 17:31:46.299490", "total_processing_time_seconds": 37.02042818069458}, "final_response": {"success": true, "data": {"enriched_data_csv": "employee_id,record_type,first_name,last_name,relationship,dob,gender,address1,city,state,zipcode,marital_status,medical_plan,dental_plan,vision_plan,dept_1_first_name,dept_1_last_name,relationship_type_1,dept_1_dob,dept_1_gender,dept_1_medical_plan,dept_1_dental_plan,dept_1_vision_plan,dept_1,dept_2_first_name,dept_2_last_name,relationship_type_2,dept_2_dob,dept_2_gender,dept_2_medical_plan,dept_2_dental_plan,dept_2_vision_plan,dept_2,dept_count,dept_3_first_name,dept_3_last_name,relationship_type_3,dept_3_dob,dept_3_gender,dept_3_medical_plan,dept_3_dental_plan,dept_3_vision_plan,dept_3,name,age,dept_1_age,dept_2_age,dept_3_age,middle_name,address2,salary,employment_type,employee_class,department,hire_date,tobacco_use,pregnancy_status,life_plan,add_plan,coverage_tier,ssn,dept_4,dept_4_dob,dept_4_age,dept_4_gender,relationship_type_4,dept_5,dept_5_dob,dept_5_age,dept_5_gender,relationship_type_5,dept_6,dept_6_dob,dept_6_age,dept_6_gender,relationship_type_6,dept_7,dept_7_dob,dept_7_age,dept_7_gender,relationship_type_7,dept_8,dept_8_dob,dept_8_age,dept_8_gender,relationship_type_8,dept_9,dept_9_dob,dept_9_age,dept_9_gender,relationship_type_9,dept_10,dept_10_dob,dept_10_age,dept_10_gender,relationship_type_10,dept_11,dept_11_dob,dept_11_age,dept_11_gender,relationship_type_11,dept_12,dept_12_dob,dept_12_age,dept_12_gender,relationship_type_12,dept_13,dept_13_dob,dept_13_age,dept_13_gender,relationship_type_13,dept_14,dept_14_dob,dept_14_age,dept_14_gender,relationship_type_14,dept_15,dept_15_dob,dept_15_age,dept_15_gender,relationship_type_15,dept_16,dept_16_dob,dept_16_age,dept_16_gender,relationship_type_16,dept_17,dept_17_dob,dept_17_age,dept_17_gender,relationship_type_17,dept_18,dept_18_dob,dept_18_age,dept_18_gender,relationship_type_18,dept_19,dept_19_dob,dept_19_age,dept_19_gender,relationship_type_19,dept_20,dept_20_dob,dept_20_age,dept_20_gender,relationship_type_20,job_type,region,health_condition,chronic_condition,prescription_use,income_tier,risk_tolerance,lifestyle,travel_frequency,hsa_familiarity,mental_health_needs,predicted_plan_type,plan_confidence,plan_reason,top_3_plans,top_3_plan_confidences,predicted_benefits,benefits_confidence,benefits_reason,top_3_benefits,top_3_benefits_confidences\r\nE001,Dependent,John,Smith,Employee,1980-01-15,Male,123 Main St,Anytown,CA,90210,Married,Y,Y,Y,Jane,Smith,Spouse,1982-05-20,Female,Y,Y,Y,Jane Smith,Mike,Smith,Child,2010-09-12,Male,Y,Y,Y,Mike Smith,2,,,,,,,,,,John Smith,45.0,43.0,14.0,,,,,Full-Time,,Finance,,Y,N,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Desk,Urban,Good,N,Occasional,Medium ($50K–$100K),Low,Active,Rare,N,Y,POS,0.8853227,\"Medium income, good health - PPO for balanced cost and flexibility.\",\"['POS', 'HMO', 'PPO']\",\"[0.8853227, 0.06517418, 0.*********]\",\"['Dental', 'Vision', 'Employee Assistance']\",1.0,Dental: Essential oral health coverage for all employees.; Vision: Eye care coverage for all employees.,\"['Employee Assistance', 'Wellness Programs', 'FSA']\",\"[0.5, 0.5, 0.5]\"\r\nE002,Dependent,Alice,Brown,Employee,1975-03-10,Female,456 Oak Ave,Somewhere,TX,75001,Not Married,Y,N,Y,Bob,Brown,Child,2005-07-25,Male,Y,N,Y,Bob Brown,,,,,,,,,,1,,,,,,,,,,Alice Brown,50.0,19.0,,,,,,Full-Time,,Sales,,N,N,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Desk,Urban,Fair,N,None,Medium ($50K–$100K),Low,Active,Occasional,N,N,PPO,0.511886,\"Medium income, urban area - EPO for network-based care.\",\"['PPO', 'POS', 'HMO']\",\"[0.511886, 0.43731162, 0.031950243]\",\"['Dental', 'Vision']\",1.0,Dental: Essential oral health coverage for all employees.; Vision: Eye care coverage for all employees.,\"['Employee Assistance', 'Wellness Programs', 'FSA']\",\"[0.5, 0.5, 0.5]\"\r\nE003,Dependent,Charlie,Davis,Employee,1990-06-30,Male,789 Pine Rd,Elsewhere,FL,33101,Married,Y,Y,Y,Diana,Davis,Spouse,1992-08-15,Female,Y,Y,Y,Diana Davis,Evan,Davis,Child,2015-12-01,Male,Y,Y,Y,Evan Davis,3,Fiona,Davis,Child,2018-03-22,Female,Y,Y,Y,Fiona Davis,Charlie Davis,35.0,32.0,9.0,7.0,,,,Full-Time,,Sales,,N,N,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Desk,Urban,Fair,N,None,Medium ($50K–$100K),Medium,Active,Occasional,N,Y,PPO,0.548272,\"Medium income, urban area - EPO for network-based care.\",\"['PPO', 'POS', 'HMO']\",\"[0.548272, 0.34528482, 0.04827972]\",['Employee Assistance'],1.0,ML model prediction,\"['Employee Assistance', 'Wellness Programs', 'FSA']\",\"[0.5, 0.5, 0.5]\"\r\nE004,Dependent,Grace,Wilson,Employee,1985-11-05,Female,321 Elm St,Nowhere,NY,10001,Not Married,Y,Y,N,Henry,Wilson,Child,2012-04-18,Male,Y,Y,N,Henry Wilson,,,,,,,,,,1,,,,,,,,,,Grace Wilson,39.0,13.0,,,,,,Contract,,Information Technology,,N,N,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Desk,Urban,Fair,N,Occasional,Medium ($50K–$100K),Medium,Active,Rare,N,N,POS,0.46327284,\"Medium income, urban area - EPO for network-based care.\",\"['POS', 'PPO', 'HMO']\",\"[0.46327284, 0.37467104, 0.0791984]\",\"['Dental', 'Vision']\",1.0,Dental: Essential oral health coverage for all employees.; Vision: Eye care coverage for all employees.,\"['Employee Assistance', 'Wellness Programs', 'FSA']\",\"[0.5, 0.5, 0.5]\"\r\nE005,Dependent,Isaac,Johnson,Employee,1978-02-28,Male,654 Maple Dr,Anywhere,WA,98001,Married,Y,Y,Y,Lisa,Johnson,Spouse,1980-11-12,Female,Y,Y,Y,Lisa Johnson,Tom,Johnson,Child,2008-06-15,Male,Y,Y,Y,Tom Johnson,3,Sara,Johnson,Child,2011-09-03,Female,Y,Y,Y,Sara Johnson,Isaac Johnson,47.0,44.0,17.0,13.0,,,,Full-Time,,Finance,,N,N,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Desk,Urban,Fair,N,None,Medium ($50K–$100K),Low,Moderate,Rare,N,Y,PPO,0.80616105,\"Medium income, urban area - EPO for network-based care.\",\"['PPO', 'POS', 'HMO']\",\"[0.80616105, 0.11888271, 0.048815764]\",['Employee Assistance'],1.0,ML model prediction,\"['Employee Assistance', 'Wellness Programs', 'FSA']\",\"[0.5, 0.5, 0.5]\"\r\nE006,Employee,Michael,Anderson,Employee,1983-07-12,Male,987 Cedar Ln,Someplace,OR,97001,Not Married,N,N,N,,,,,,,,,,,,,,,,,,,0,,,,,,,,,,Michael Anderson,42.0,,,,,,,Full-Time,,Information Technology,,Y,N,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Desk,Rural,Good,N,None,Low (<$50K),Low,Moderate,Rare,Y,N,HMO,0.7016206,Middle age with dependents - PPO for family coverage.,\"['HMO', 'HDHP + HSA', 'PPO']\",\"[0.7016206, 0.13456891, 0.080492966]\",[],0.0,ML model prediction,\"['Employee Assistance', 'Wellness Programs', 'FSA']\",\"[0.5, 0.5, 0.5]\"\r\nE007,Dependent,Nancy,Taylor,Employee,1987-12-03,Female,147 Birch Ave,Othertown,AZ,85001,Married,Y,Y,Y,Paul,Taylor,Spouse,1985-04-20,Male,Y,Y,Y,Paul Taylor,,,,,,,,,,1,,,,,,,,,,Nancy Taylor,37.0,40.0,,,,,,Full-Time,,Sales,,N,N,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Desk,Urban,Excellent,N,Occasional,Medium ($50K–$100K),Medium,Moderate,Frequent,N,N,POS,0.78172404,\"Medium income, urban area - EPO for network-based care.\",\"['POS', 'HDHP', 'HMO']\",\"[0.78172404, 0.13182461, 0.04555784]\",\"['Dental', 'Vision']\",1.0,Dental: Essential oral health coverage for all employees.; Vision: Eye care coverage for all employees.,\"['Employee Assistance', 'Wellness Programs', 'FSA']\",\"[0.5, 0.5, 0.5]\"\r\nE008,Dependent,Quinn,Martinez,Employee,1979-10-17,Male,258 Spruce St,Newplace,CO,80001,Not Married,Y,Y,Y,Alex,Martinez,Child,2007-12-25,Male,Y,Y,Y,Alex Martinez,Zoe,Martinez,Child,2010-03-14,Female,Y,Y,Y,Zoe Martinez,2,,,,,,,,,,Quinn Martinez,45.0,17.0,15.0,,,,,Full-Time,,Engineering,,N,N,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Desk,Suburban,Fair,N,Occasional,High (>$100K),Low,Moderate,Rare,Y,N,PPO,0.6454856,Middle age with dependents - PPO for family coverage.,\"['PPO', 'POS', 'HDHP + HSA']\",\"[0.6454856, 0.2221554, 0.********]\",\"['Dental', 'Vision', 'Accident', 'Term Life', 'STD', 'LTD', 'FSA']\",1.0,Dental: Essential oral health coverage for all employees.; Vision: Eye care coverage for all employees.; Term Life: Essential protection for 2 dependents.; STD: Income protection for High (>$100K) earners or fair health status.; LTD: Long-term income protection for High (>$100K) earners or age 45+.; FSA: Tax-advantaged account for medical expenses due to health conditions or pregnancy.,\"['Employee Assistance', 'Wellness Programs', 'FSA']\",\"[0.5, 0.5, 0.5]\"\r\nE009,Dependent,Tina,Garcia,Employee,1981-03-24,Female,369 Willow Way,Lastplace,NV,89001,Married,Y,Y,Y,Carlos,Garcia,Spouse,1979-07-08,Male,Y,Y,Y,Carlos Garcia,Maya,Garcia,Child,2009-11-30,Female,Y,Y,Y,Maya Garcia,2,,,,,,,,,,Tina Garcia,44.0,46.0,15.0,,,,,Full-Time,,Engineering,,N,N,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Desk,Suburban,Fair,N,Occasional,High (>$100K),Medium,Active,Occasional,Y,N,PPO,0.********,Middle age with dependents - PPO for family coverage.,\"['PPO', 'POS', 'HDHP + HSA']\",\"[0.********, 0.2992348, 0.********]\",\"['Dental', 'Vision', 'Accident', 'Term Life', 'STD', 'LTD', 'FSA']\",1.0,Dental: Essential oral health coverage for all employees.; Vision: Eye care coverage for all employees.; Accident: Recommended for active lifestyle and occasional travel frequency.; Term Life: Essential protection for 2 dependents.; STD: Income protection for High (>$100K) earners or fair health status.; LTD: Long-term income protection for High (>$100K) earners or age 44+.; FSA: Tax-advantaged account for medical expenses due to health conditions or pregnancy.,\"['Employee Assistance', 'Wellness Programs', 'FSA']\",\"[0.5, 0.5, 0.5]\"\r\nE010,Employee,Xavier,Rodriguez,Employee,1986-09-09,Male,741 Poplar Pl,Finaltown,UT,84001,Not Married,N,N,N,,,,,,,,,,,,,,,,,,,0,,,,,,,,,,Xavier Rodriguez,38.0,,,,,,,Contract,,Sales,,Y,N,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Desk,Suburban,Good,N,None,High (>$100K),High,Moderate,Occasional,Y,N,HDHP + HSA,0.********,\"High income, good health, HSA familiar, no chronic conditions - HDHP + HSA for tax benefits.\",\"['HDHP + HSA', 'POS', 'HDHP']\",\"[0.********, 0.0917779, 0.*********]\",['Wellness Programs'],1.0,ML model prediction,\"['Employee Assistance', 'Wellness Programs', 'FSA']\",\"[0.5, 0.5, 0.5]\"\r\nE011,Dependent,Yvonne,Lee,Employee,1984-12-18,Female,852 Ash Ct,Hometown,MT,59001,Married,Y,Y,Y,David,Lee,Spouse,1982-02-14,Male,Y,Y,Y,David Lee,,,,,,,,,,1,,,,,,,,,,Yvonne Lee,40.0,43.0,,,,,,Full-Time,,Sales,,Y,Y,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Desk,Rural,Good,N,None,Medium ($50K–$100K),Medium,Moderate,Occasional,Y,N,HMO,0.71773565,\"Medium income, good health - PPO for balanced cost and flexibility.\",\"['HMO', 'POS', 'HDHP + HSA']\",\"[0.71773565, 0.17901017, 0.*********]\",\"['Dental', 'Vision', 'Hospital Indemnity', 'STD']\",1.0,Dental: Essential oral health coverage for all employees.; Vision: Eye care coverage for all employees.; STD: Income protection for Medium ($50K–$100K) earners or good health status.,\"['Employee Assistance', 'Wellness Programs', 'FSA']\",\"[0.5, 0.5, 0.5]\"\r\nE012,Dependent,Aaron,White,Employee,1977-04-07,Male,963 Beech St,Yourtown,ID,83001,Not Married,Y,Y,Y,Kelly,White,Child,2006-05-22,Female,Y,Y,Y,Kelly White,Ryan,White,Child,2009-10-11,Male,Y,Y,Y,Ryan White,2,,,,,,,,,,Aaron White,48.0,19.0,15.0,,,,,Full-Time,,Engineering,,Y,N,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Desk,Rural,Good,N,None,Low (<$50K),Low,Moderate,Occasional,N,Y,HMO,0.7286423,Middle age with dependents - PPO for family coverage.,\"['HMO', 'PPO', 'POS']\",\"[0.7286423, 0.13679682, 0.12523285]\",\"['Dental', 'Vision']\",0.5,Default benefits assignment due to prediction error,\"['Dental', 'Vision', 'Hospital Indemnity']\",\"[0.5, 0.5, 0.5]\"\r\nE013,Employee,Brooke,Harris,Employee,1989-08-21,Female,159 Cherry Ave,Mytown,WY,82001,Not Married,N,N,N,,,,,,,,,,,,,,,,,,,0,,,,,,,,,,Brooke Harris,35.0,,,,,,,Part-Time,,Sales,,N,N,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Desk,Rural,Excellent,N,Occasional,Medium ($50K–$100K),Low,Active,Occasional,N,N,POS,0.5798526,Middle age with dependents - PPO for family coverage.,\"['POS', 'HDHP', 'PPO']\",\"[0.5798526, 0.23457597, 0.085046515]\",\"['Dental', 'Vision']\",0.5,Default benefits assignment due to prediction error,\"['Dental', 'Vision', 'Hospital Indemnity']\",\"[0.5, 0.5, 0.5]\"\r\nE014,Dependent,Connor,Clark,Employee,1982-01-26,Male,357 Dogwood Dr,Ourtown,ND,58001,Married,Y,Y,Y,Rachel,Clark,Spouse,1984-06-18,Female,Y,Y,Y,Rachel Clark,Jake,Clark,Child,2012-01-05,Male,Y,Y,Y,Jake Clark,3,Lily,Clark,Child,2014-07-12,Female,Y,Y,Y,Lily Clark,Connor Clark,43.0,41.0,13.0,11.0,,,,Full-Time,,Sales,,N,N,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Desk,Rural,Good,N,None,Medium ($50K–$100K),Medium,Moderate,Frequent,Y,N,HDHP + HSA,0.8955654,\"Medium income, good health - PPO for balanced cost and flexibility.\",\"['HDHP + HSA', 'POS', 'PPO']\",\"[0.8955654, 0.075947024, 0.01186101]\",\"['Dental', 'Vision']\",0.5,Default benefits assignment due to prediction error,\"['Dental', 'Vision', 'Hospital Indemnity']\",\"[0.5, 0.5, 0.5]\"\r\nE015,Dependent,Destiny,Lewis,Employee,1986-05-13,Female,468 Fir Ln,Theirtown,SD,57001,Not Married,Y,Y,Y,Noah,Lewis,Child,2011-09-28,Male,Y,Y,Y,Noah Lewis,,,,,,,,,,1,,,,,,,,,,Destiny Lewis,39.0,13.0,,,,,,Full-Time,,Information Technology,,N,Y,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Desk,Rural,Good,Y,Regular,Medium ($50K–$100K),Medium,Moderate,Rare,Y,N,HMO,0.83198637,\"Medium income, good health - PPO for balanced cost and flexibility.\",\"['HMO', 'HDHP + HSA', 'PPO']\",\"[0.83198637, 0.09131905, 0.048231896]\",\"['Dental', 'Vision']\",0.5,Default benefits assignment due to prediction error,\"['Dental', 'Vision', 'Hospital Indemnity']\",\"[0.5, 0.5, 0.5]\"\r\n", "comprehensive_statistics": {"basic_demographics": {"age_statistics": {"average_age": 41.8, "median_age": 42.0, "age_range": {"min": 35, "max": 50}, "age_distribution": {"18-30": 0, "31-45": 12, "46-60": 3, "60+": 0}}, "gender_composition": {"counts": {"Male": 8, "Female": 7}, "percentages": {"Male": 53.33, "Female": 46.67}}, "marital_status_distribution": {"counts": {"Not Married": 8, "Married": 7}, "percentages": {"Not Married": 53.33, "Married": 46.67}}}, "dependent_analysis": {"dependent_count_distribution": {"average_dependents": 1.4666666666666666, "median_dependents": 1.0, "distribution": {"1": 5, "2": 4, "3": 3, "0": 3}, "employees_with_dependents": 12, "percentage_with_dependents": 80.0}, "dependent_age_analysis": {"total_dependents_found": 22, "average_dependent_age": 22.68, "median_dependent_age": 16.0, "dependents_over_26_as_children": 0, "age_distribution": {"0-5": 0, "6-12": 3, "13-18": 10, "19-26": 2, "26+": 7}}}, "employment_demographics": {"department_distribution": {"counts": {"Sales": 7, "Information Technology": 3, "Engineering": 3, "Finance": 2}, "percentages": {"Sales": 46.67, "Information Technology": 20.0, "Engineering": 20.0, "Finance": 13.33}}, "employment_type_distribution": {"counts": {"Full-Time": 12, "Contract": 2, "Part-Time": 1}, "percentages": {"Full-Time": 80.0, "Contract": 13.33, "Part-Time": 6.67}}, "job_type_distribution": {"counts": {"Desk": 15}, "percentages": {"Desk": 100.0}}, "employee_class_distribution": {"counts": {}, "percentages": {}}}, "health_and_lifestyle": {"health_condition_distribution": {"counts": {"Good": 7, "Fair": 6, "Excellent": 2}, "percentages": {"Good": 46.67, "Fair": 40.0, "Excellent": 13.33}}, "chronic_condition_distribution": {"counts": {"N": 14, "Y": 1}, "percentages": {"N": 93.33, "Y": 6.67}}, "tobacco_use_distribution": {"counts": {"N": 10, "Y": 5}, "percentages": {"N": 66.67, "Y": 33.33}}, "lifestyle_distribution": {"counts": {"Moderate": 9, "Active": 6}, "percentages": {"Moderate": 60.0, "Active": 40.0}}, "prescription_use_distribution": {"counts": {"None": 8, "Occasional": 6, "Regular": 1}, "percentages": {"None": 53.33, "Occasional": 40.0, "Regular": 6.67}}}, "coverage_analysis": {"coverage_tier_distribution": {"counts": {}, "percentages": {}}, "coverage_tier_by_family_size": {}, "medical_plan_distribution": {"counts": {"Y": 12, "N": 3}, "percentages": {"Y": 80.0, "N": 20.0}}, "dental_plan_distribution": {"counts": {"Y": 11, "N": 4}, "percentages": {"Y": 73.33, "N": 26.67}}, "vision_plan_distribution": {"counts": {"Y": 11, "N": 4}, "percentages": {"Y": 73.33, "N": 26.67}}, "life_plan_distribution": {"counts": {}, "percentages": {}}}, "geographic_distribution": {"state_distribution": {"counts": {"CA": 1, "TX": 1, "FL": 1, "NY": 1, "WA": 1, "OR": 1, "AZ": 1, "CO": 1, "NV": 1, "UT": 1, "MT": 1, "ID": 1, "WY": 1, "ND": 1, "SD": 1}, "percentages": {"CA": 6.67, "TX": 6.67, "FL": 6.67, "NY": 6.67, "WA": 6.67, "OR": 6.67, "AZ": 6.67, "CO": 6.67, "NV": 6.67, "UT": 6.67, "MT": 6.67, "ID": 6.67, "WY": 6.67, "ND": 6.67, "SD": 6.67}}, "region_distribution": {"counts": {"Urban": 6, "Rural": 6, "Suburban": 3}, "percentages": {"Urban": 40.0, "Rural": 40.0, "Suburban": 20.0}}, "top_cities": {"counts": {"Anytown": 1, "Somewhere": 1, "Elsewhere": 1, "Nowhere": 1, "Anywhere": 1, "Someplace": 1, "Othertown": 1, "Newplace": 1, "Lastplace": 1, "Finaltown": 1}, "percentages": {"Anytown": 6.67, "Somewhere": 6.67, "Elsewhere": 6.67, "Nowhere": 6.67, "Anywhere": 6.67, "Someplace": 6.67, "Othertown": 6.67, "Newplace": 6.67, "Lastplace": 6.67, "Finaltown": 6.67}}}, "financial_demographics": {"income_tier_distribution": {"counts": {"Medium ($50K–$100K)": 10, "High (>$100K)": 3, "Low (<$50K)": 2}, "percentages": {"Medium ($50K–$100K)": 66.67, "High (>$100K)": 20.0, "Low (<$50K)": 13.33}}, "risk_tolerance_distribution": {"counts": {"Low": 7, "Medium": 7, "High": 1}, "percentages": {"Low": 46.67, "Medium": 46.67, "High": 6.67}}, "hsa_familiarity_distribution": {"counts": {"N": 8, "Y": 7}, "percentages": {"N": 53.33, "Y": 46.67}}}, "risk_assessment": {"group_risk_score": 30.13, "group_risk_level": "Medium", "risk_distribution": {"low_risk": 5, "medium_risk": 10, "high_risk": 0}, "risk_statistics": {"min_risk_score": 10, "max_risk_score": 40, "median_risk_score": 33.0, "std_risk_score": 8.93}, "top_risk_factors": {"Poor Health Condition": 6, "Tobacco Use": 5, "Advanced Age": 1, "Chronic Conditions": 1}, "individual_risks": [{"employee_id": "E001", "risk_score": 35, "risk_level": "Medium", "risk_factors": ["Tobacco user"]}, {"employee_id": "E002", "risk_score": 40, "risk_level": "Medium", "risk_factors": ["Age 50.0 (high risk)", "Health condition: Fair"]}, {"employee_id": "E003", "risk_score": 25, "risk_level": "Low", "risk_factors": ["Health condition: Fair"]}, {"employee_id": "E004", "risk_score": 25, "risk_level": "Low", "risk_factors": ["Health condition: Fair"]}, {"employee_id": "E005", "risk_score": 33, "risk_level": "Medium", "risk_factors": ["Health condition: Fair"]}, {"employee_id": "E006", "risk_score": 38, "risk_level": "Medium", "risk_factors": ["Tobacco user"]}, {"employee_id": "E007", "risk_score": 13, "risk_level": "Low", "risk_factors": []}, {"employee_id": "E008", "risk_score": 33, "risk_level": "Medium", "risk_factors": ["Health condition: Fair"]}, {"employee_id": "E009", "risk_score": 30, "risk_level": "Medium", "risk_factors": ["Health condition: Fair"]}, {"employee_id": "E010", "risk_score": 33, "risk_level": "Medium", "risk_factors": ["Tobacco user"]}]}, "data_quality_metrics": {"overall_completeness": {"total_cells": 2355, "missing_cells": "1640", "completeness_percentage": 30.36}, "column_completeness": {"employee_id": {"missing_count": "0", "completeness_percentage": 100.0}, "record_type": {"missing_count": "0", "completeness_percentage": 100.0}, "first_name": {"missing_count": "0", "completeness_percentage": 100.0}, "last_name": {"missing_count": "0", "completeness_percentage": 100.0}, "relationship": {"missing_count": "0", "completeness_percentage": 100.0}, "dob": {"missing_count": "0", "completeness_percentage": 100.0}, "gender": {"missing_count": "0", "completeness_percentage": 100.0}, "address1": {"missing_count": "0", "completeness_percentage": 100.0}, "city": {"missing_count": "0", "completeness_percentage": 100.0}, "state": {"missing_count": "0", "completeness_percentage": 100.0}, "zipcode": {"missing_count": "0", "completeness_percentage": 100.0}, "marital_status": {"missing_count": "0", "completeness_percentage": 100.0}, "medical_plan": {"missing_count": "0", "completeness_percentage": 100.0}, "dental_plan": {"missing_count": "0", "completeness_percentage": 100.0}, "vision_plan": {"missing_count": "0", "completeness_percentage": 100.0}, "dept_1_first_name": {"missing_count": "3", "completeness_percentage": 80.0}, "dept_1_last_name": {"missing_count": "3", "completeness_percentage": 80.0}, "relationship_type_1": {"missing_count": "3", "completeness_percentage": 80.0}, "dept_1_dob": {"missing_count": "3", "completeness_percentage": 80.0}, "dept_1_gender": {"missing_count": "3", "completeness_percentage": 80.0}, "dept_1_medical_plan": {"missing_count": "3", "completeness_percentage": 80.0}, "dept_1_dental_plan": {"missing_count": "3", "completeness_percentage": 80.0}, "dept_1_vision_plan": {"missing_count": "3", "completeness_percentage": 80.0}, "dept_1": {"missing_count": "3", "completeness_percentage": 80.0}, "dept_2_first_name": {"missing_count": "8", "completeness_percentage": 46.67}, "dept_2_last_name": {"missing_count": "8", "completeness_percentage": 46.67}, "relationship_type_2": {"missing_count": "8", "completeness_percentage": 46.67}, "dept_2_dob": {"missing_count": "8", "completeness_percentage": 46.67}, "dept_2_gender": {"missing_count": "8", "completeness_percentage": 46.67}, "dept_2_medical_plan": {"missing_count": "8", "completeness_percentage": 46.67}, "dept_2_dental_plan": {"missing_count": "8", "completeness_percentage": 46.67}, "dept_2_vision_plan": {"missing_count": "8", "completeness_percentage": 46.67}, "dept_2": {"missing_count": "8", "completeness_percentage": 46.67}, "dept_count": {"missing_count": "0", "completeness_percentage": 100.0}, "dept_3_first_name": {"missing_count": "12", "completeness_percentage": 20.0}, "dept_3_last_name": {"missing_count": "12", "completeness_percentage": 20.0}, "relationship_type_3": {"missing_count": "12", "completeness_percentage": 20.0}, "dept_3_dob": {"missing_count": "12", "completeness_percentage": 20.0}, "dept_3_gender": {"missing_count": "12", "completeness_percentage": 20.0}, "dept_3_medical_plan": {"missing_count": "12", "completeness_percentage": 20.0}, "dept_3_dental_plan": {"missing_count": "12", "completeness_percentage": 20.0}, "dept_3_vision_plan": {"missing_count": "12", "completeness_percentage": 20.0}, "dept_3": {"missing_count": "12", "completeness_percentage": 20.0}, "name": {"missing_count": "0", "completeness_percentage": 100.0}, "age": {"missing_count": "0", "completeness_percentage": 100.0}, "dept_1_age": {"missing_count": "3", "completeness_percentage": 80.0}, "dept_2_age": {"missing_count": "8", "completeness_percentage": 46.67}, "dept_3_age": {"missing_count": "12", "completeness_percentage": 20.0}, "middle_name": {"missing_count": "15", "completeness_percentage": 0.0}, "address2": {"missing_count": "15", "completeness_percentage": 0.0}, "salary": {"missing_count": "15", "completeness_percentage": 0.0}, "employment_type": {"missing_count": "0", "completeness_percentage": 100.0}, "employee_class": {"missing_count": "15", "completeness_percentage": 0.0}, "department": {"missing_count": "0", "completeness_percentage": 100.0}, "hire_date": {"missing_count": "15", "completeness_percentage": 0.0}, "tobacco_use": {"missing_count": "0", "completeness_percentage": 100.0}, "pregnancy_status": {"missing_count": "0", "completeness_percentage": 100.0}, "life_plan": {"missing_count": "15", "completeness_percentage": 0.0}, "add_plan": {"missing_count": "15", "completeness_percentage": 0.0}, "coverage_tier": {"missing_count": "15", "completeness_percentage": 0.0}, "ssn": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_4": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_4_dob": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_4_age": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_4_gender": {"missing_count": "15", "completeness_percentage": 0.0}, "relationship_type_4": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_5": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_5_dob": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_5_age": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_5_gender": {"missing_count": "15", "completeness_percentage": 0.0}, "relationship_type_5": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_6": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_6_dob": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_6_age": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_6_gender": {"missing_count": "15", "completeness_percentage": 0.0}, "relationship_type_6": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_7": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_7_dob": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_7_age": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_7_gender": {"missing_count": "15", "completeness_percentage": 0.0}, "relationship_type_7": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_8": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_8_dob": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_8_age": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_8_gender": {"missing_count": "15", "completeness_percentage": 0.0}, "relationship_type_8": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_9": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_9_dob": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_9_age": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_9_gender": {"missing_count": "15", "completeness_percentage": 0.0}, "relationship_type_9": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_10": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_10_dob": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_10_age": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_10_gender": {"missing_count": "15", "completeness_percentage": 0.0}, "relationship_type_10": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_11": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_11_dob": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_11_age": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_11_gender": {"missing_count": "15", "completeness_percentage": 0.0}, "relationship_type_11": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_12": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_12_dob": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_12_age": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_12_gender": {"missing_count": "15", "completeness_percentage": 0.0}, "relationship_type_12": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_13": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_13_dob": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_13_age": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_13_gender": {"missing_count": "15", "completeness_percentage": 0.0}, "relationship_type_13": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_14": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_14_dob": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_14_age": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_14_gender": {"missing_count": "15", "completeness_percentage": 0.0}, "relationship_type_14": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_15": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_15_dob": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_15_age": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_15_gender": {"missing_count": "15", "completeness_percentage": 0.0}, "relationship_type_15": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_16": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_16_dob": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_16_age": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_16_gender": {"missing_count": "15", "completeness_percentage": 0.0}, "relationship_type_16": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_17": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_17_dob": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_17_age": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_17_gender": {"missing_count": "15", "completeness_percentage": 0.0}, "relationship_type_17": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_18": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_18_dob": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_18_age": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_18_gender": {"missing_count": "15", "completeness_percentage": 0.0}, "relationship_type_18": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_19": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_19_dob": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_19_age": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_19_gender": {"missing_count": "15", "completeness_percentage": 0.0}, "relationship_type_19": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_20": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_20_dob": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_20_age": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_20_gender": {"missing_count": "15", "completeness_percentage": 0.0}, "relationship_type_20": {"missing_count": "15", "completeness_percentage": 0.0}, "job_type": {"missing_count": "0", "completeness_percentage": 100.0}, "region": {"missing_count": "0", "completeness_percentage": 100.0}, "health_condition": {"missing_count": "0", "completeness_percentage": 100.0}, "chronic_condition": {"missing_count": "0", "completeness_percentage": 100.0}, "prescription_use": {"missing_count": "0", "completeness_percentage": 100.0}, "income_tier": {"missing_count": "0", "completeness_percentage": 100.0}, "risk_tolerance": {"missing_count": "0", "completeness_percentage": 100.0}, "lifestyle": {"missing_count": "0", "completeness_percentage": 100.0}, "travel_frequency": {"missing_count": "0", "completeness_percentage": 100.0}, "hsa_familiarity": {"missing_count": "0", "completeness_percentage": 100.0}, "mental_health_needs": {"missing_count": "0", "completeness_percentage": 100.0}}, "critical_field_completeness": {"name": {"missing_count": "0", "completeness_percentage": 100.0}, "gender": {"missing_count": "0", "completeness_percentage": 100.0}, "zipcode": {"missing_count": "0", "completeness_percentage": 100.0}, "marital_status": {"missing_count": "0", "completeness_percentage": 100.0}}}}, "prediction_summary": {"total_employees": 15, "plan_type_distribution": {"successful_predictions": {"PPO": 5, "POS": 4, "HMO": 4, "HDHP + HSA": 2}, "failed_predictions": "0", "success_rate": 100.0, "total_predicted": "15"}, "benefits_distribution": {}, "confidence_metrics": {"plan_confidence": {"mean": "0.702", "min": "0.463", "max": "0.896", "count": 15}}, "prediction_success_rates": {}, "top_3_plan_probabilities": []}, "total_employees": 15, "total_columns": 167, "has_predictions": true, "prediction_columns": ["predicted_plan_type", "plan_confidence"]}, "metadata": {"file_processing": {"filename": "row1_member_level.csv", "size": 3515, "original_column_names": ["Employee_ID", "Record_Type", "First_Name", "Last_Name", "Relationship", "DOB", "Gender", "Address1", "City", "State", "ZIP", "Marital_Status", "Medical_Plan", "Dental_Plan", "Vision_Plan"]}, "pattern_analysis": {"pattern_type": "row_based_member_level", "pattern_confidence": 1.6, "pattern_reason": "Found 'relationship' field; Found both employee and dependent relationship values; Found 'record_type' field; Found Employee/Dependent record types; Found duplicate employee_ids (family grouping)", "analysis_details": {"confidence": 1.6, "reason": "Found 'relationship' field; Found both employee and dependent relationship values; Found 'record_type' field; Found Employee/Dependent record types; Found duplicate employee_ids (family grouping)"}}, "field_mapping": {"total_fields_mapped": 15, "mapping_success": true, "unmapped_fields": [], "mapping_confidence": 0, "field_mapping_details": {"Employee_ID": "employee_id", "Record_Type": "record_type", "First_Name": "first_name", "Last_Name": "last_name", "Relationship": "relationship", "DOB": "dob", "Gender": "gender", "Address1": "address1", "City": "city", "State": "state", "ZIP": "zipcode", "Marital_Status": "marital_status", "Medical_Plan": "medical_plan", "Dental_Plan": "dental_plan", "Vision_Plan": "vision_plan"}}, "data_processing": {"validation_passed": false, "processing_summary": {"original_rows": 37, "original_columns": 15, "processed_rows": 15, "processed_columns": 146, "processing_time_seconds": 28.78, "validation_passed": true, "validation_errors": 0, "error_rows": 0, "fields_processed": {"name_fields": true, "age_converted": true, "gender_standardized": true, "addresses_cleaned": true, "zipcode_validated": true}, "data_quality": {"complete_records": 15, "missing_data_rows": 0}}, "data_quality_score": 0.562, "processing_details": {"success": true, "preprocessed_data": "   employee_id record_type first_name  last_name relationship         dob  gender  ... dept_19_gender relationship_type_19 dept_20  dept_20_dob dept_20_age dept_20_gender relationship_type_20\n0         E001   Dependent       <PERSON>     Employee  1980-01-15    Male  ...           None                 None    None         None        None           None                 None\n1         E002   Dependent      <PERSON>     Employee  1975-03-10  Female  ...           None                 None    None         None        None           None                 None\n2         E003   Dependent    Charlie      <PERSON>     Employee  1990-06-30    Male  ...           None                 None    None         None        None           None                 None\n3         E004   Dependent      <PERSON>     Employee  1985-11-05  Female  ...           None                 None    None         None        None           None                 None\n4         E005   Dependent      <PERSON>     Employee  1978-02-28    Male  ...           None                 None    None         None        None           None                 None\n5         E006    Employee    Michael   <PERSON>     Employee  1983-07-12    Male  ...           None                 None    None         None        None           None                 None\n6         E007   Dependent      <PERSON>     Employee  1987-12-03  Female  ...           None                 None    None         None        None           None                 None\n7         E008   Dependent      Quinn   <PERSON>     Employee  1979-10-17    Male  ...           None                 None    None         None        None           None                 None\n8         E009   Dependent       <PERSON>loy<PERSON>  1981-03-24  Female  ...           None                 None    None         None        None           None                 None\n9         E010    Employee     <PERSON>loyee  1986-09-09    Male  ...           None                 None    None         None        None           None                 None\n10        E011   Dependent     Yvonne        <PERSON>loy<PERSON>  1984-12-18  Female  ...           None                 None    None         None        None           None                 None\n11        E012   Dependent      Aaron      White     Employee  1977-04-07    Male  ...           None                 None    None         None        None           None                 None\n12        E013    Employee     Brooke     Harris     Employee  1989-08-21  Female  ...           None                 None    None         None        None           None                 None\n13        E014   Dependent     Connor      Clark     Employee  1982-01-26    Male  ...           None                 None    None         None        None           None                 None\n14        E015   Dependent    Destiny      <PERSON>     Employee  1986-05-13  Female  ...           None                 None    None         None        None           None                 None\n\n[15 rows x 146 columns]", "validation": {"is_valid": true, "errors": [], "error_rows": [], "total_errors": 0}, "summary": {"original_rows": 37, "original_columns": 15, "processed_rows": 15, "processed_columns": 146, "processing_time_seconds": 28.78, "validation_passed": true, "validation_errors": 0, "error_rows": 0, "fields_processed": {"name_fields": true, "age_converted": true, "gender_standardized": true, "addresses_cleaned": true, "zipcode_validated": true}, "data_quality": {"complete_records": 15, "missing_data_rows": 0}}}}, "enrichment_and_prediction": {"success": true, "enriched_data": "   employee_id record_type first_name  ...                                    benefits_reason                                 top_3_benefits top_3_benefits_confidences\n0         E001   Dependent       John  ...  Dental: Essential oral health coverage for all...  [Employee Assistance, Wellness Programs, FSA]            [0.5, 0.5, 0.5]\n1         E002   Dependent      Alice  ...  Dental: Essential oral health coverage for all...  [Employee Assistance, Wellness Programs, FSA]            [0.5, 0.5, 0.5]\n2         E003   Dependent    Charlie  ...                                ML model prediction  [Employee Assistance, Wellness Programs, FSA]            [0.5, 0.5, 0.5]\n3         E004   Dependent      Grace  ...  Dental: Essential oral health coverage for all...  [Employee Assistance, Wellness Programs, FSA]            [0.5, 0.5, 0.5]\n4         E005   Dependent      Isaac  ...                                ML model prediction  [Employee Assistance, Wellness Programs, FSA]            [0.5, 0.5, 0.5]\n5         E006    Employee    Michael  ...                                ML model prediction  [Employee Assistance, Wellness Programs, FSA]            [0.5, 0.5, 0.5]\n6         E007   Dependent      Nancy  ...  Dental: Essential oral health coverage for all...  [Employee Assistance, Wellness Programs, FSA]            [0.5, 0.5, 0.5]\n7         E008   Dependent      Quinn  ...  Dental: Essential oral health coverage for all...  [Employee Assistance, Wellness Programs, FSA]            [0.5, 0.5, 0.5]\n8         E009   Dependent       Tina  ...  Dental: Essential oral health coverage for all...  [Employee Assistance, Wellness Programs, FSA]            [0.5, 0.5, 0.5]\n9         E010    Employee     Xavier  ...                                ML model prediction  [Employee Assistance, Wellness Programs, FSA]            [0.5, 0.5, 0.5]\n10        E011   Dependent     Yvonne  ...  Dental: Essential oral health coverage for all...  [Employee Assistance, Wellness Programs, FSA]            [0.5, 0.5, 0.5]\n11        E012   Dependent      Aaron  ...  Default benefits assignment due to prediction ...           [Dental, Vision, Hospital Indemnity]            [0.5, 0.5, 0.5]\n12        E013    Employee     Brooke  ...  Default benefits assignment due to prediction ...           [Dental, Vision, Hospital Indemnity]            [0.5, 0.5, 0.5]\n13        E014   Dependent     Connor  ...  Default benefits assignment due to prediction ...           [Dental, Vision, Hospital Indemnity]            [0.5, 0.5, 0.5]\n14        E015   Dependent    Destiny  ...  Default benefits assignment due to prediction ...           [Dental, Vision, Hospital Indemnity]            [0.5, 0.5, 0.5]\n\n[15 rows x 167 columns]", "enrichment_summary": {"total_employees": 15, "features_analyzed": 19, "enrichment_details": {"age": {"original_missing": "0", "final_missing": "0", "enriched_count": "0", "completion_rate": 100.0}, "gender": {"original_missing": "0", "final_missing": "0", "enriched_count": "0", "completion_rate": 100.0}, "marital_status": {"original_missing": "0", "final_missing": "0", "enriched_count": "0", "completion_rate": 100.0}, "dept_count": {"original_missing": "0", "final_missing": "0", "enriched_count": "0", "completion_rate": 100.0}, "job_type": {"original_missing": 15, "final_missing": "0", "enriched_count": "15", "completion_rate": 100.0}, "employment_type": {"original_missing": "15", "final_missing": "0", "enriched_count": "15", "completion_rate": 100.0}, "department": {"original_missing": "15", "final_missing": "0", "enriched_count": "15", "completion_rate": 100.0}, "region": {"original_missing": 15, "final_missing": "0", "enriched_count": "15", "completion_rate": 100.0}, "health_condition": {"original_missing": 15, "final_missing": "0", "enriched_count": "15", "completion_rate": 100.0}, "chronic_condition": {"original_missing": 15, "final_missing": "0", "enriched_count": "15", "completion_rate": 100.0}, "prescription_use": {"original_missing": 15, "final_missing": "0", "enriched_count": "15", "completion_rate": 100.0}, "income_tier": {"original_missing": 15, "final_missing": "0", "enriched_count": "15", "completion_rate": 100.0}, "risk_tolerance": {"original_missing": 15, "final_missing": "0", "enriched_count": "15", "completion_rate": 100.0}, "lifestyle": {"original_missing": 15, "final_missing": "0", "enriched_count": "15", "completion_rate": 100.0}, "travel_frequency": {"original_missing": 15, "final_missing": "0", "enriched_count": "15", "completion_rate": 100.0}, "hsa_familiarity": {"original_missing": 15, "final_missing": "0", "enriched_count": "15", "completion_rate": 100.0}, "pregnancy_status": {"original_missing": "15", "final_missing": "0", "enriched_count": "15", "completion_rate": 100.0}, "tobacco_use": {"original_missing": "15", "final_missing": "0", "enriched_count": "15", "completion_rate": 100.0}, "mental_health_needs": {"original_missing": 15, "final_missing": "0", "enriched_count": "15", "completion_rate": 100.0}}, "data_quality_improvement": {"total_missing_before": "225", "total_missing_after": "0", "total_enriched": "225", "overall_completion_rate": 100.0}}, "comprehensive_statistics": {"basic_demographics": {"age_statistics": {"average_age": 41.8, "median_age": 42.0, "age_range": {"min": 35, "max": 50}, "age_distribution": {"18-30": 0, "31-45": 12, "46-60": 3, "60+": 0}}, "gender_composition": {"counts": {"Male": 8, "Female": 7}, "percentages": {"Male": 53.33, "Female": 46.67}}, "marital_status_distribution": {"counts": {"Not Married": 8, "Married": 7}, "percentages": {"Not Married": 53.33, "Married": 46.67}}}, "dependent_analysis": {"dependent_count_distribution": {"average_dependents": 1.4666666666666666, "median_dependents": 1.0, "distribution": {"1": 5, "2": 4, "3": 3, "0": 3}, "employees_with_dependents": 12, "percentage_with_dependents": 80.0}, "dependent_age_analysis": {"total_dependents_found": 22, "average_dependent_age": 22.68, "median_dependent_age": 16.0, "dependents_over_26_as_children": 0, "age_distribution": {"0-5": 0, "6-12": 3, "13-18": 10, "19-26": 2, "26+": 7}}}, "employment_demographics": {"department_distribution": {"counts": {"Sales": 7, "Information Technology": 3, "Engineering": 3, "Finance": 2}, "percentages": {"Sales": 46.67, "Information Technology": 20.0, "Engineering": 20.0, "Finance": 13.33}}, "employment_type_distribution": {"counts": {"Full-Time": 12, "Contract": 2, "Part-Time": 1}, "percentages": {"Full-Time": 80.0, "Contract": 13.33, "Part-Time": 6.67}}, "job_type_distribution": {"counts": {"Desk": 15}, "percentages": {"Desk": 100.0}}, "employee_class_distribution": {"counts": {}, "percentages": {}}}, "health_and_lifestyle": {"health_condition_distribution": {"counts": {"Good": 7, "Fair": 6, "Excellent": 2}, "percentages": {"Good": 46.67, "Fair": 40.0, "Excellent": 13.33}}, "chronic_condition_distribution": {"counts": {"N": 14, "Y": 1}, "percentages": {"N": 93.33, "Y": 6.67}}, "tobacco_use_distribution": {"counts": {"N": 10, "Y": 5}, "percentages": {"N": 66.67, "Y": 33.33}}, "lifestyle_distribution": {"counts": {"Moderate": 9, "Active": 6}, "percentages": {"Moderate": 60.0, "Active": 40.0}}, "prescription_use_distribution": {"counts": {"None": 8, "Occasional": 6, "Regular": 1}, "percentages": {"None": 53.33, "Occasional": 40.0, "Regular": 6.67}}}, "coverage_analysis": {"coverage_tier_distribution": {"counts": {}, "percentages": {}}, "coverage_tier_by_family_size": {}, "medical_plan_distribution": {"counts": {"Y": 12, "N": 3}, "percentages": {"Y": 80.0, "N": 20.0}}, "dental_plan_distribution": {"counts": {"Y": 11, "N": 4}, "percentages": {"Y": 73.33, "N": 26.67}}, "vision_plan_distribution": {"counts": {"Y": 11, "N": 4}, "percentages": {"Y": 73.33, "N": 26.67}}, "life_plan_distribution": {"counts": {}, "percentages": {}}}, "geographic_distribution": {"state_distribution": {"counts": {"CA": 1, "TX": 1, "FL": 1, "NY": 1, "WA": 1, "OR": 1, "AZ": 1, "CO": 1, "NV": 1, "UT": 1, "MT": 1, "ID": 1, "WY": 1, "ND": 1, "SD": 1}, "percentages": {"CA": 6.67, "TX": 6.67, "FL": 6.67, "NY": 6.67, "WA": 6.67, "OR": 6.67, "AZ": 6.67, "CO": 6.67, "NV": 6.67, "UT": 6.67, "MT": 6.67, "ID": 6.67, "WY": 6.67, "ND": 6.67, "SD": 6.67}}, "region_distribution": {"counts": {"Urban": 6, "Rural": 6, "Suburban": 3}, "percentages": {"Urban": 40.0, "Rural": 40.0, "Suburban": 20.0}}, "top_cities": {"counts": {"Anytown": 1, "Somewhere": 1, "Elsewhere": 1, "Nowhere": 1, "Anywhere": 1, "Someplace": 1, "Othertown": 1, "Newplace": 1, "Lastplace": 1, "Finaltown": 1}, "percentages": {"Anytown": 6.67, "Somewhere": 6.67, "Elsewhere": 6.67, "Nowhere": 6.67, "Anywhere": 6.67, "Someplace": 6.67, "Othertown": 6.67, "Newplace": 6.67, "Lastplace": 6.67, "Finaltown": 6.67}}}, "financial_demographics": {"income_tier_distribution": {"counts": {"Medium ($50K–$100K)": 10, "High (>$100K)": 3, "Low (<$50K)": 2}, "percentages": {"Medium ($50K–$100K)": 66.67, "High (>$100K)": 20.0, "Low (<$50K)": 13.33}}, "risk_tolerance_distribution": {"counts": {"Low": 7, "Medium": 7, "High": 1}, "percentages": {"Low": 46.67, "Medium": 46.67, "High": 6.67}}, "hsa_familiarity_distribution": {"counts": {"N": 8, "Y": 7}, "percentages": {"N": 53.33, "Y": 46.67}}}, "risk_assessment": {"group_risk_score": 30.13, "group_risk_level": "Medium", "risk_distribution": {"low_risk": 5, "medium_risk": 10, "high_risk": 0}, "risk_statistics": {"min_risk_score": 10, "max_risk_score": 40, "median_risk_score": 33.0, "std_risk_score": 8.93}, "top_risk_factors": {"Poor Health Condition": 6, "Tobacco Use": 5, "Advanced Age": 1, "Chronic Conditions": 1}, "individual_risks": [{"employee_id": "E001", "risk_score": 35, "risk_level": "Medium", "risk_factors": ["Tobacco user"]}, {"employee_id": "E002", "risk_score": 40, "risk_level": "Medium", "risk_factors": ["Age 50.0 (high risk)", "Health condition: Fair"]}, {"employee_id": "E003", "risk_score": 25, "risk_level": "Low", "risk_factors": ["Health condition: Fair"]}, {"employee_id": "E004", "risk_score": 25, "risk_level": "Low", "risk_factors": ["Health condition: Fair"]}, {"employee_id": "E005", "risk_score": 33, "risk_level": "Medium", "risk_factors": ["Health condition: Fair"]}, {"employee_id": "E006", "risk_score": 38, "risk_level": "Medium", "risk_factors": ["Tobacco user"]}, {"employee_id": "E007", "risk_score": 13, "risk_level": "Low", "risk_factors": []}, {"employee_id": "E008", "risk_score": 33, "risk_level": "Medium", "risk_factors": ["Health condition: Fair"]}, {"employee_id": "E009", "risk_score": 30, "risk_level": "Medium", "risk_factors": ["Health condition: Fair"]}, {"employee_id": "E010", "risk_score": 33, "risk_level": "Medium", "risk_factors": ["Tobacco user"]}]}, "data_quality_metrics": {"overall_completeness": {"total_cells": 2355, "missing_cells": "1640", "completeness_percentage": 30.36}, "column_completeness": {"employee_id": {"missing_count": "0", "completeness_percentage": 100.0}, "record_type": {"missing_count": "0", "completeness_percentage": 100.0}, "first_name": {"missing_count": "0", "completeness_percentage": 100.0}, "last_name": {"missing_count": "0", "completeness_percentage": 100.0}, "relationship": {"missing_count": "0", "completeness_percentage": 100.0}, "dob": {"missing_count": "0", "completeness_percentage": 100.0}, "gender": {"missing_count": "0", "completeness_percentage": 100.0}, "address1": {"missing_count": "0", "completeness_percentage": 100.0}, "city": {"missing_count": "0", "completeness_percentage": 100.0}, "state": {"missing_count": "0", "completeness_percentage": 100.0}, "zipcode": {"missing_count": "0", "completeness_percentage": 100.0}, "marital_status": {"missing_count": "0", "completeness_percentage": 100.0}, "medical_plan": {"missing_count": "0", "completeness_percentage": 100.0}, "dental_plan": {"missing_count": "0", "completeness_percentage": 100.0}, "vision_plan": {"missing_count": "0", "completeness_percentage": 100.0}, "dept_1_first_name": {"missing_count": "3", "completeness_percentage": 80.0}, "dept_1_last_name": {"missing_count": "3", "completeness_percentage": 80.0}, "relationship_type_1": {"missing_count": "3", "completeness_percentage": 80.0}, "dept_1_dob": {"missing_count": "3", "completeness_percentage": 80.0}, "dept_1_gender": {"missing_count": "3", "completeness_percentage": 80.0}, "dept_1_medical_plan": {"missing_count": "3", "completeness_percentage": 80.0}, "dept_1_dental_plan": {"missing_count": "3", "completeness_percentage": 80.0}, "dept_1_vision_plan": {"missing_count": "3", "completeness_percentage": 80.0}, "dept_1": {"missing_count": "3", "completeness_percentage": 80.0}, "dept_2_first_name": {"missing_count": "8", "completeness_percentage": 46.67}, "dept_2_last_name": {"missing_count": "8", "completeness_percentage": 46.67}, "relationship_type_2": {"missing_count": "8", "completeness_percentage": 46.67}, "dept_2_dob": {"missing_count": "8", "completeness_percentage": 46.67}, "dept_2_gender": {"missing_count": "8", "completeness_percentage": 46.67}, "dept_2_medical_plan": {"missing_count": "8", "completeness_percentage": 46.67}, "dept_2_dental_plan": {"missing_count": "8", "completeness_percentage": 46.67}, "dept_2_vision_plan": {"missing_count": "8", "completeness_percentage": 46.67}, "dept_2": {"missing_count": "8", "completeness_percentage": 46.67}, "dept_count": {"missing_count": "0", "completeness_percentage": 100.0}, "dept_3_first_name": {"missing_count": "12", "completeness_percentage": 20.0}, "dept_3_last_name": {"missing_count": "12", "completeness_percentage": 20.0}, "relationship_type_3": {"missing_count": "12", "completeness_percentage": 20.0}, "dept_3_dob": {"missing_count": "12", "completeness_percentage": 20.0}, "dept_3_gender": {"missing_count": "12", "completeness_percentage": 20.0}, "dept_3_medical_plan": {"missing_count": "12", "completeness_percentage": 20.0}, "dept_3_dental_plan": {"missing_count": "12", "completeness_percentage": 20.0}, "dept_3_vision_plan": {"missing_count": "12", "completeness_percentage": 20.0}, "dept_3": {"missing_count": "12", "completeness_percentage": 20.0}, "name": {"missing_count": "0", "completeness_percentage": 100.0}, "age": {"missing_count": "0", "completeness_percentage": 100.0}, "dept_1_age": {"missing_count": "3", "completeness_percentage": 80.0}, "dept_2_age": {"missing_count": "8", "completeness_percentage": 46.67}, "dept_3_age": {"missing_count": "12", "completeness_percentage": 20.0}, "middle_name": {"missing_count": "15", "completeness_percentage": 0.0}, "address2": {"missing_count": "15", "completeness_percentage": 0.0}, "salary": {"missing_count": "15", "completeness_percentage": 0.0}, "employment_type": {"missing_count": "0", "completeness_percentage": 100.0}, "employee_class": {"missing_count": "15", "completeness_percentage": 0.0}, "department": {"missing_count": "0", "completeness_percentage": 100.0}, "hire_date": {"missing_count": "15", "completeness_percentage": 0.0}, "tobacco_use": {"missing_count": "0", "completeness_percentage": 100.0}, "pregnancy_status": {"missing_count": "0", "completeness_percentage": 100.0}, "life_plan": {"missing_count": "15", "completeness_percentage": 0.0}, "add_plan": {"missing_count": "15", "completeness_percentage": 0.0}, "coverage_tier": {"missing_count": "15", "completeness_percentage": 0.0}, "ssn": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_4": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_4_dob": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_4_age": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_4_gender": {"missing_count": "15", "completeness_percentage": 0.0}, "relationship_type_4": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_5": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_5_dob": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_5_age": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_5_gender": {"missing_count": "15", "completeness_percentage": 0.0}, "relationship_type_5": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_6": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_6_dob": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_6_age": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_6_gender": {"missing_count": "15", "completeness_percentage": 0.0}, "relationship_type_6": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_7": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_7_dob": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_7_age": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_7_gender": {"missing_count": "15", "completeness_percentage": 0.0}, "relationship_type_7": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_8": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_8_dob": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_8_age": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_8_gender": {"missing_count": "15", "completeness_percentage": 0.0}, "relationship_type_8": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_9": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_9_dob": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_9_age": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_9_gender": {"missing_count": "15", "completeness_percentage": 0.0}, "relationship_type_9": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_10": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_10_dob": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_10_age": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_10_gender": {"missing_count": "15", "completeness_percentage": 0.0}, "relationship_type_10": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_11": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_11_dob": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_11_age": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_11_gender": {"missing_count": "15", "completeness_percentage": 0.0}, "relationship_type_11": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_12": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_12_dob": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_12_age": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_12_gender": {"missing_count": "15", "completeness_percentage": 0.0}, "relationship_type_12": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_13": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_13_dob": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_13_age": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_13_gender": {"missing_count": "15", "completeness_percentage": 0.0}, "relationship_type_13": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_14": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_14_dob": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_14_age": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_14_gender": {"missing_count": "15", "completeness_percentage": 0.0}, "relationship_type_14": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_15": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_15_dob": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_15_age": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_15_gender": {"missing_count": "15", "completeness_percentage": 0.0}, "relationship_type_15": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_16": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_16_dob": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_16_age": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_16_gender": {"missing_count": "15", "completeness_percentage": 0.0}, "relationship_type_16": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_17": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_17_dob": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_17_age": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_17_gender": {"missing_count": "15", "completeness_percentage": 0.0}, "relationship_type_17": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_18": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_18_dob": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_18_age": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_18_gender": {"missing_count": "15", "completeness_percentage": 0.0}, "relationship_type_18": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_19": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_19_dob": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_19_age": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_19_gender": {"missing_count": "15", "completeness_percentage": 0.0}, "relationship_type_19": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_20": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_20_dob": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_20_age": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_20_gender": {"missing_count": "15", "completeness_percentage": 0.0}, "relationship_type_20": {"missing_count": "15", "completeness_percentage": 0.0}, "job_type": {"missing_count": "0", "completeness_percentage": 100.0}, "region": {"missing_count": "0", "completeness_percentage": 100.0}, "health_condition": {"missing_count": "0", "completeness_percentage": 100.0}, "chronic_condition": {"missing_count": "0", "completeness_percentage": 100.0}, "prescription_use": {"missing_count": "0", "completeness_percentage": 100.0}, "income_tier": {"missing_count": "0", "completeness_percentage": 100.0}, "risk_tolerance": {"missing_count": "0", "completeness_percentage": 100.0}, "lifestyle": {"missing_count": "0", "completeness_percentage": 100.0}, "travel_frequency": {"missing_count": "0", "completeness_percentage": 100.0}, "hsa_familiarity": {"missing_count": "0", "completeness_percentage": 100.0}, "mental_health_needs": {"missing_count": "0", "completeness_percentage": 100.0}}, "critical_field_completeness": {"name": {"missing_count": "0", "completeness_percentage": 100.0}, "gender": {"missing_count": "0", "completeness_percentage": 100.0}, "zipcode": {"missing_count": "0", "completeness_percentage": 100.0}, "marital_status": {"missing_count": "0", "completeness_percentage": 100.0}}}}, "feature_validation": {"is_valid": true, "missing_features": [], "feature_completeness": {"age": {"present": true, "missing_values": "0", "completion_rate": 100.0}, "gender": {"present": true, "missing_values": "0", "completion_rate": 100.0}, "marital_status": {"present": true, "missing_values": "0", "completion_rate": 100.0}, "dept_count": {"present": true, "missing_values": "0", "completion_rate": 100.0}, "job_type": {"present": true, "missing_values": "0", "completion_rate": 100.0}, "employment_type": {"present": true, "missing_values": "0", "completion_rate": 100.0}, "department": {"present": true, "missing_values": "0", "completion_rate": 100.0}, "region": {"present": true, "missing_values": "0", "completion_rate": 100.0}, "health_condition": {"present": true, "missing_values": "0", "completion_rate": 100.0}, "chronic_condition": {"present": true, "missing_values": "0", "completion_rate": 100.0}, "prescription_use": {"present": true, "missing_values": "0", "completion_rate": 100.0}, "income_tier": {"present": true, "missing_values": "0", "completion_rate": 100.0}, "risk_tolerance": {"present": true, "missing_values": "0", "completion_rate": 100.0}, "lifestyle": {"present": true, "missing_values": "0", "completion_rate": 100.0}, "travel_frequency": {"present": true, "missing_values": "0", "completion_rate": 100.0}, "hsa_familiarity": {"present": true, "missing_values": "0", "completion_rate": 100.0}, "pregnancy_status": {"present": true, "missing_values": "0", "completion_rate": 100.0}, "tobacco_use": {"present": true, "missing_values": "0", "completion_rate": 100.0}, "mental_health_needs": {"present": true, "missing_values": "0", "completion_rate": 100.0}}, "overall_completion_rate": 100.0, "ready_for_prediction": "True"}, "prediction_method": "ml_models", "model_predictions": {"plan_types": {"total_predictions": 15, "average_confidence": "0.70191187", "unique_plans": 4}}, "prediction_summary": {"total_employees": 15, "plan_type_distribution": {"successful_predictions": {"PPO": 5, "POS": 4, "HMO": 4, "HDHP + HSA": 2}, "failed_predictions": "0", "success_rate": 100.0, "total_predicted": "15"}, "benefits_distribution": {}, "confidence_metrics": {"plan_confidence": {"mean": "0.702", "min": "0.463", "max": "0.896", "count": 15}}, "prediction_success_rates": {}, "top_3_plan_probabilities": ["E001: [0.8853227, 0.06517418, 0.*********]", "E002: [0.511886, 0.43731162, 0.031950243]", "E003: [0.548272, 0.34528482, 0.04827972]", "E004: [0.46327284, 0.37467104, 0.0791984]", "E005: [0.80616105, 0.11888271, 0.048815764]", "E006: [0.7016206, 0.13456891, 0.080492966]", "E007: [0.78172404, 0.13182461, 0.04555784]", "E008: [0.6454856, 0.2221554, 0.********]", "E009: [0.********, 0.2992348, 0.********]", "E010: [0.********, 0.0917779, 0.*********]", "E011: [0.71773565, 0.17901017, 0.*********]", "E012: [0.7286423, 0.13679682, 0.12523285]", "E013: [0.5798526, 0.23457597, 0.085046515]", "E014: [0.8955654, 0.075947024, 0.01186101]", "E015: [0.83198637, 0.09131905, 0.048231896]"]}}}}}