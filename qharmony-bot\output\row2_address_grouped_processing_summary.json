{"file_info": {"original_filename": "row2_address_grouped.csv", "processing_timestamp": "2025-07-15 17:34:29.105791", "total_processing_time_seconds": 31.811763525009155}, "final_response": {"success": true, "data": {"enriched_data_csv": "first_name,last_name,gender,dob,address1,city,state,zipcode,marital_status,medical_plan,dental_plan,vision_plan,dept_1_first_name,dept_1_last_name,dept_1_gender,dept_1_dob,dept_1_address_line1,dept_1_city,dept_1_state,dept_1_zipcode,dept_1_marital_status,dept_1_medical_plan,dept_1_dental_plan,dept_1_vision_plan,dept_1,dept_2_first_name,dept_2_last_name,dept_2_gender,dept_2_dob,dept_2_address_line1,dept_2_city,dept_2_state,dept_2_zipcode,dept_2_marital_status,dept_2_medical_plan,dept_2_dental_plan,dept_2_vision_plan,dept_2,dept_count,dept_3_first_name,dept_3_last_name,dept_3_gender,dept_3_dob,dept_3_address_line1,dept_3_city,dept_3_state,dept_3_zipcode,dept_3_marital_status,dept_3_medical_plan,dept_3_dental_plan,dept_3_vision_plan,dept_3,name,age,dept_1_age,dept_2_age,dept_3_age,employee_id,middle_name,address2,relationship,record_type,salary,employment_type,employee_class,department,hire_date,tobacco_use,pregnancy_status,life_plan,add_plan,coverage_tier,ssn,relationship_type_1,relationship_type_2,relationship_type_3,dept_4,dept_4_dob,dept_4_age,dept_4_gender,relationship_type_4,dept_5,dept_5_dob,dept_5_age,dept_5_gender,relationship_type_5,dept_6,dept_6_dob,dept_6_age,dept_6_gender,relationship_type_6,dept_7,dept_7_dob,dept_7_age,dept_7_gender,relationship_type_7,dept_8,dept_8_dob,dept_8_age,dept_8_gender,relationship_type_8,dept_9,dept_9_dob,dept_9_age,dept_9_gender,relationship_type_9,dept_10,dept_10_dob,dept_10_age,dept_10_gender,relationship_type_10,dept_11,dept_11_dob,dept_11_age,dept_11_gender,relationship_type_11,dept_12,dept_12_dob,dept_12_age,dept_12_gender,relationship_type_12,dept_13,dept_13_dob,dept_13_age,dept_13_gender,relationship_type_13,dept_14,dept_14_dob,dept_14_age,dept_14_gender,relationship_type_14,dept_15,dept_15_dob,dept_15_age,dept_15_gender,relationship_type_15,dept_16,dept_16_dob,dept_16_age,dept_16_gender,relationship_type_16,dept_17,dept_17_dob,dept_17_age,dept_17_gender,relationship_type_17,dept_18,dept_18_dob,dept_18_age,dept_18_gender,relationship_type_18,dept_19,dept_19_dob,dept_19_age,dept_19_gender,relationship_type_19,dept_20,dept_20_dob,dept_20_age,dept_20_gender,relationship_type_20,job_type,region,health_condition,chronic_condition,prescription_use,income_tier,risk_tolerance,lifestyle,travel_frequency,hsa_familiarity,mental_health_needs,predicted_plan_type,plan_confidence,plan_reason,top_3_plans,top_3_plan_confidences,predicted_benefits,benefits_confidence,benefits_reason,top_3_benefits,top_3_benefits_confidences\r\nJohn,Smith,Male,1980-01-15,123 Main St,Anytown,CA,90210,Married,Y,Y,Y,Jane,Smith,Female,1982-05-20,123 Main St,Anytown,CA,90210.0,Married,Y,Y,Y,Jane Smith,Mike,Smith,Male,2010-09-12,123 Main St,Anytown,CA,90210.0,Married,Y,Y,Y,Mike Smith,2,,,,,,,,,,,,,,John Smith,45.0,43.0,14.0,,E001,,,,,,Contract,,Information Technology,,N,N,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Desk,Urban,Excellent,N,Occasional,Low (<$50K),Medium,Moderate,Rare,N,N,HMO,0.7136569,Middle age with dependents - PPO for family coverage.,\"['HMO', 'POS', 'MEC']\",\"[0.7136569, 0.16095912, 0.05143935]\",\"['Dental', 'Vision']\",1.0,Dental: Essential oral health coverage for all employees.; Vision: Eye care coverage for all employees.,\"['Employee Assistance', 'Wellness Programs', 'FSA']\",\"[0.5, 0.5, 0.5]\"\r\nAlice,Brown,Female,1975-03-10,456 Oak Ave,Somewhere,TX,75001,Not Married,Y,N,Y,Bob,Brown,Male,2005-07-25,456 Oak Ave,Somewhere,TX,75001.0,Single,Y,N,Y,Bob Brown,,,,,,,,,,,,,,1,,,,,,,,,,,,,,Alice Brown,50.0,19.0,,,E002,,,,,,Full-Time,,Sales,,N,N,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Desk,Urban,Good,N,None,High (>$100K),Medium,Active,Occasional,N,N,POS,0.7084166,Older age - PPO for comprehensive coverage and provider choice.,\"['POS', 'PPO', 'HMO']\",\"[0.7084166, 0.22614051, 0.04343438]\",\"['Dental', 'Vision', 'Term Life', 'LTD', 'Wellness Programs']\",1.0,Dental: Essential oral health coverage for all employees.; Vision: Eye care coverage for all employees.; Term Life: Essential protection for 1 dependents.; LTD: Long-term income protection for High (>$100K) earners or age 50+.,\"['Employee Assistance', 'Wellness Programs', 'FSA']\",\"[0.5, 0.5, 0.5]\"\r\nCharlie,Davis,Male,1990-06-30,789 Pine Rd,Elsewhere,FL,33101,Married,Y,Y,Y,Diana,Davis,Female,1992-08-15,789 Pine Rd,Elsewhere,FL,33101.0,Married,Y,Y,Y,Diana Davis,Evan,Davis,Male,2015-12-01,789 Pine Rd,Elsewhere,FL,33101.0,Married,Y,Y,Y,Evan Davis,3,Fiona,Davis,Female,2018-03-22,789 Pine Rd,Elsewhere,FL,33101.0,Married,Y,Y,Y,Fiona Davis,Charlie Davis,35.0,32.0,9.0,7.0,E003,,,,,,Full-Time,,Manufacturing,,N,N,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Desk,Urban,Fair,N,Occasional,Medium ($50K–$100K),Medium,Active,Rare,N,N,PPO,0.6678085,\"Medium income, urban area - EPO for network-based care.\",\"['PPO', 'POS', 'HMO']\",\"[0.6678085, 0.16203122, 0.08369441]\",\"['Accident', 'STD']\",1.0,Accident: Recommended for active lifestyle and rare travel frequency.; STD: Income protection for Medium ($50K–$100K) earners or fair health status.,\"['Employee Assistance', 'Wellness Programs', 'FSA']\",\"[0.5, 0.5, 0.5]\"\r\nGrace,Wilson,Female,1985-11-05,321 Elm St,Nowhere,NY,10001,Not Married,Y,Y,N,Henry,Wilson,Male,2012-04-18,321 Elm St,Nowhere,NY,10001.0,Divorced,Y,Y,N,Henry Wilson,,,,,,,,,,,,,,1,,,,,,,,,,,,,,Grace Wilson,39.0,13.0,,,E004,,,,,,Part-Time,,Information Technology,,Y,N,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Desk,Urban,Good,N,None,Low (<$50K),Low,Active,Rare,N,N,HMO,0.7506588,\"Low income, part-time employment - MEC for minimum coverage compliance.\",\"['HMO', 'POS', 'EPO']\",\"[0.7506588, 0.14031367, 0.05679907]\",\"['Dental', 'Vision']\",1.0,Dental: Essential oral health coverage for all employees.; Vision: Eye care coverage for all employees.,\"['Employee Assistance', 'Wellness Programs', 'FSA']\",\"[0.5, 0.5, 0.5]\"\r\nIsaac,Johnson,Male,1978-02-28,654 Maple Dr,Anywhere,WA,98001,Married,Y,Y,Y,Lisa,Johnson,Female,1980-11-12,654 Maple Dr,Anywhere,WA,98001.0,Married,Y,Y,Y,Lisa Johnson,Tom,Johnson,Male,2008-06-15,654 Maple Dr,Anywhere,WA,98001.0,Married,Y,Y,Y,Tom Johnson,3,Sara,Johnson,Female,2011-09-03,654 Maple Dr,Anywhere,WA,98001.0,Married,Y,Y,Y,Sara Johnson,Isaac Johnson,47.0,44.0,17.0,13.0,E005,,,,,,Full-Time,,Manufacturing,,N,N,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Desk,Urban,Good,Y,Regular,Medium ($50K–$100K),Low,Moderate,Rare,N,Y,PPO,0.7393914,\"Medium income, good health - PPO for balanced cost and flexibility.\",\"['PPO', 'POS', 'HMO']\",\"[0.7393914, 0.12662695, 0.094088875]\",\"['Accident', 'Critical Illness', 'STD', 'Employee Assistance']\",1.0,Critical Illness: Important protection for age 47 with good health status.; STD: Income protection for Medium ($50K–$100K) earners or good health status.,\"['Employee Assistance', 'Wellness Programs', 'FSA']\",\"[0.5, 0.5, 0.5]\"\r\nMichael,Anderson,Male,1983-07-12,987 Cedar Ln,Someplace,OR,97001,Not Married,N,N,N,,,,,,,,,,,,,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,Michael Anderson,42.0,,,,E006,,,,,,Full-Time,,Sales,,N,N,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Desk,Suburban,Fair,N,None,Medium ($50K–$100K),Low,Moderate,Frequent,N,N,PPO,0.7159531,Middle age with dependents - PPO for family coverage.,\"['PPO', 'POS', 'HMO']\",\"[0.7159531, 0.22471774, 0.030451266]\",[],0.0,ML model prediction,\"['Employee Assistance', 'Wellness Programs', 'FSA']\",\"[0.5, 0.5, 0.5]\"\r\nNancy,Taylor,Female,1987-12-03,147 Birch Ave,Othertown,AZ,85001,Married,Y,Y,Y,Paul,Taylor,Male,1985-04-20,147 Birch Ave,Othertown,AZ,85001.0,Married,Y,Y,Y,Paul Taylor,,,,,,,,,,,,,,1,,,,,,,,,,,,,,Nancy Taylor,37.0,40.0,,,E007,,,,,,Part-Time,,Engineering,,Y,N,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Desk,Urban,Fair,N,Occasional,Low (<$50K),Low,Moderate,Rare,N,N,HMO,0.6890726,\"Low income, part-time employment - MEC for minimum coverage compliance.\",\"['HMO', 'PPO', 'POS']\",\"[0.6890726, 0.14733845, 0.091393426]\",\"['Dental', 'Vision', 'Accident', 'STD']\",1.0,Dental: Essential oral health coverage for all employees.; Vision: Eye care coverage for all employees.; STD: Income protection for Low (<$50K) earners or fair health status.,\"['Employee Assistance', 'Wellness Programs', 'FSA']\",\"[0.5, 0.5, 0.5]\"\r\nQuinn,Martinez,Male,1979-10-17,258 Spruce St,Newplace,CO,80001,Not Married,Y,Y,Y,Alex,Martinez,Male,2007-12-25,258 Spruce St,Newplace,CO,80001.0,Divorced,Y,Y,Y,Alex Martinez,Zoe,Martinez,Female,2010-03-14,258 Spruce St,Newplace,CO,80001.0,Divorced,Y,Y,Y,Zoe Martinez,2,,,,,,,,,,,,,,Quinn Martinez,45.0,17.0,15.0,,E008,,,,,,Full-Time,,Sales,,N,N,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Desk,Rural,Fair,Y,Regular,Medium ($50K–$100K),Low,Active,Occasional,Y,N,HDHP + HSA,0.7555324,Middle age with dependents - PPO for family coverage.,\"['HDHP + HSA', 'PPO', 'POS']\",\"[0.7555324, 0.19199406, 0.04318231]\",\"['Dental', 'Vision', 'Critical Illness']\",1.0,Dental: Essential oral health coverage for all employees.; Vision: Eye care coverage for all employees.; Critical Illness: Important protection for age 45 with fair health status.,\"['Employee Assistance', 'Wellness Programs', 'FSA']\",\"[0.5, 0.5, 0.5]\"\r\nTina,Garcia,Female,1981-03-24,369 Willow Way,Lastplace,NV,89001,Married,Y,Y,Y,Carlos,Garcia,Male,1979-07-08,369 Willow Way,Lastplace,NV,89001.0,Married,Y,Y,Y,Carlos Garcia,Maya,Garcia,Female,2009-11-30,369 Willow Way,Lastplace,NV,89001.0,Married,Y,Y,Y,Maya Garcia,2,,,,,,,,,,,,,,Tina Garcia,44.0,46.0,15.0,,E009,,,,,,Full-Time,,Manufacturing,,N,N,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Desk,Rural,Good,N,None,Medium ($50K–$100K),Low,Moderate,Rare,N,Y,POS,0.86271435,\"Medium income, good health - PPO for balanced cost and flexibility.\",\"['POS', 'HMO', 'PPO']\",\"[0.86271435, 0.06526185, 0.05253908]\",\"['Dental', 'Vision', 'Accident', 'STD', 'Employee Assistance']\",1.0,Dental: Essential oral health coverage for all employees.; Vision: Eye care coverage for all employees.; STD: Income protection for Medium ($50K–$100K) earners or good health status.,\"['Employee Assistance', 'Wellness Programs', 'FSA']\",\"[0.5, 0.5, 0.5]\"\r\nXavier,Rodriguez,Male,1986-09-09,741 Poplar Pl,Finaltown,UT,84001,Not Married,N,N,N,,,,,,,,,,,,,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,Xavier Rodriguez,38.0,,,,E010,,,,,,Full-Time,,Information Technology,,N,N,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Desk,Suburban,Fair,N,Occasional,High (>$100K),High,Active,Rare,Y,N,Indemnity,0.7149105,Middle age with dependents - PPO for family coverage.,\"['Indemnity', 'PPO', 'HDHP + HSA']\",\"[0.7149105, 0.18115257, 0.07347947]\",['Wellness Programs'],1.0,ML model prediction,\"['Employee Assistance', 'Wellness Programs', 'FSA']\",\"[0.5, 0.5, 0.5]\"\r\nYvonne,Lee,Female,1984-12-18,852 Ash Ct,Hometown,MT,59001,Married,Y,Y,Y,David,Lee,Male,1982-02-14,852 Ash Ct,Hometown,MT,59001.0,Married,Y,Y,Y,David Lee,,,,,,,,,,,,,,1,,,,,,,,,,,,,,Yvonne Lee,40.0,43.0,,,E011,,,,,,Full-Time,,Information Technology,,N,Y,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Desk,Rural,Excellent,N,None,Medium ($50K–$100K),Low,Moderate,Rare,Y,N,HMO,0.7999597,Middle age with dependents - PPO for family coverage.,\"['HMO', 'POS', 'HDHP + HSA']\",\"[0.7999597, 0.10200254, 0.07758328]\",\"['Dental', 'Vision', 'Hospital Indemnity', 'STD']\",1.0,Dental: Essential oral health coverage for all employees.; Vision: Eye care coverage for all employees.; STD: Income protection for Medium ($50K–$100K) earners or excellent health status.,\"['Employee Assistance', 'Wellness Programs', 'FSA']\",\"[0.5, 0.5, 0.5]\"\r\nAaron,White,Male,1977-04-07,963 Beech St,Yourtown,ID,83001,Not Married,Y,Y,Y,Kelly,White,Female,2006-05-22,963 Beech St,Yourtown,ID,83001.0,Divorced,Y,Y,Y,Kelly White,Ryan,White,Male,2009-10-11,963 Beech St,Yourtown,ID,83001.0,Divorced,Y,Y,Y,Ryan White,2,,,,,,,,,,,,,,Aaron White,48.0,19.0,15.0,,E012,,,,,,Full-Time,,Engineering,,Y,N,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Desk,Rural,Fair,Y,Occasional,Low (<$50K),Medium,Moderate,Rare,N,Y,PPO,0.8112744,\"Low income, fair/poor health, chronic conditions - HMO for cost-effective managed care.\",\"['PPO', 'HMO', 'POS']\",\"[0.8112744, 0.17172891, 0.011501331]\",\"['Dental', 'Vision']\",0.5,Default benefits assignment due to prediction error,\"['Dental', 'Vision', 'Hospital Indemnity']\",\"[0.5, 0.5, 0.5]\"\r\nBrooke,Harris,Female,1989-08-21,159 Cherry Ave,Mytown,WY,82001,Not Married,N,N,N,,,,,,,,,,,,,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,Brooke Harris,35.0,,,,E013,,,,,,Full-Time,,Finance,,N,N,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Desk,Rural,Fair,N,None,Medium ($50K–$100K),Low,Moderate,Occasional,N,N,PPO,0.701719,Middle age with dependents - PPO for family coverage.,\"['PPO', 'POS', 'HMO']\",\"[0.701719, 0.20322482, 0.041865546]\",\"['Dental', 'Vision']\",0.5,Default benefits assignment due to prediction error,\"['Dental', 'Vision', 'Hospital Indemnity']\",\"[0.5, 0.5, 0.5]\"\r\nConnor,Clark,Male,1982-01-26,357 Dogwood Dr,Ourtown,ND,58001,Married,Y,Y,Y,Rachel,Clark,Female,1984-06-18,357 Dogwood Dr,Ourtown,ND,58001.0,Married,Y,Y,Y,Rachel Clark,Jake,Clark,Male,2012-01-05,357 Dogwood Dr,Ourtown,ND,58001.0,Married,Y,Y,Y,Jake Clark,3,Lily,Clark,Female,2014-07-12,357 Dogwood Dr,Ourtown,ND,58001.0,Married,Y,Y,Y,Lily Clark,Connor Clark,43.0,41.0,13.0,11.0,E014,,,,,,Full-Time,,Engineering,,N,N,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Desk,Rural,Good,N,None,Medium ($50K–$100K),Low,Active,Occasional,Y,N,HDHP + HSA,0.9138635,\"Medium income, good health - PPO for balanced cost and flexibility.\",\"['HDHP + HSA', 'POS', 'PPO']\",\"[0.9138635, 0.05788933, 0.012788059]\",\"['Dental', 'Vision']\",0.5,Default benefits assignment due to prediction error,\"['Dental', 'Vision', 'Hospital Indemnity']\",\"[0.5, 0.5, 0.5]\"\r\nDestiny,Lewis,Female,1986-05-13,468 Fir Ln,Theirtown,SD,57001,Not Married,Y,Y,Y,Noah,Lewis,Male,2011-09-28,468 Fir Ln,Theirtown,SD,57001.0,Single,Y,Y,Y,Noah Lewis,,,,,,,,,,,,,,1,,,,,,,,,,,,,,Destiny Lewis,39.0,13.0,,,E015,,,,,,Part-Time,,Information Technology,,N,N,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Desk,Rural,Excellent,N,None,High (>$100K),High,Moderate,Rare,Y,N,HDHP + HSA,0.69406104,\"High income, excellent health, high risk tolerance with dependents - PPO for family coverage.\",\"['HDHP + HSA', 'HDHP', 'Indemnity']\",\"[0.69406104, 0.104144596, 0.09539415]\",\"['Dental', 'Vision']\",0.5,Default benefits assignment due to prediction error,\"['Dental', 'Vision', 'Hospital Indemnity']\",\"[0.5, 0.5, 0.5]\"\r\n", "comprehensive_statistics": {"basic_demographics": {"age_statistics": {"average_age": 41.8, "median_age": 42.0, "age_range": {"min": 35, "max": 50}, "age_distribution": {"18-30": 0, "31-45": 12, "46-60": 3, "60+": 0}}, "gender_composition": {"counts": {"Male": 8, "Female": 7}, "percentages": {"Male": 53.33, "Female": 46.67}}, "marital_status_distribution": {"counts": {"Not Married": 8, "Married": 7}, "percentages": {"Not Married": 53.33, "Married": 46.67}}}, "dependent_analysis": {"dependent_count_distribution": {"average_dependents": 1.4666666666666666, "median_dependents": 1.0, "distribution": {"1": 5, "2": 4, "3": 3, "0": 3}, "employees_with_dependents": 12, "percentage_with_dependents": 80.0}, "dependent_age_analysis": {"total_dependents_found": 22, "average_dependent_age": 22.68, "median_dependent_age": 16.0, "dependents_over_26_as_children": 0, "age_distribution": {"0-5": 0, "6-12": 3, "13-18": 10, "19-26": 2, "26+": 7}}}, "employment_demographics": {"department_distribution": {"counts": {"Information Technology": 5, "Sales": 3, "Manufacturing": 3, "Engineering": 3, "Finance": 1}, "percentages": {"Information Technology": 33.33, "Sales": 20.0, "Manufacturing": 20.0, "Engineering": 20.0, "Finance": 6.67}}, "employment_type_distribution": {"counts": {"Full-Time": 11, "Part-Time": 3, "Contract": 1}, "percentages": {"Full-Time": 73.33, "Part-Time": 20.0, "Contract": 6.67}}, "job_type_distribution": {"counts": {"Desk": 15}, "percentages": {"Desk": 100.0}}, "employee_class_distribution": {"counts": {}, "percentages": {}}}, "health_and_lifestyle": {"health_condition_distribution": {"counts": {"Fair": 7, "Good": 5, "Excellent": 3}, "percentages": {"Fair": 46.67, "Good": 33.33, "Excellent": 20.0}}, "chronic_condition_distribution": {"counts": {"N": 12, "Y": 3}, "percentages": {"N": 80.0, "Y": 20.0}}, "tobacco_use_distribution": {"counts": {"N": 12, "Y": 3}, "percentages": {"N": 80.0, "Y": 20.0}}, "lifestyle_distribution": {"counts": {"Moderate": 9, "Active": 6}, "percentages": {"Moderate": 60.0, "Active": 40.0}}, "prescription_use_distribution": {"counts": {"None": 8, "Occasional": 5, "Regular": 2}, "percentages": {"None": 53.33, "Occasional": 33.33, "Regular": 13.33}}}, "coverage_analysis": {"coverage_tier_distribution": {"counts": {}, "percentages": {}}, "coverage_tier_by_family_size": {}, "medical_plan_distribution": {"counts": {"Y": 12, "N": 3}, "percentages": {"Y": 80.0, "N": 20.0}}, "dental_plan_distribution": {"counts": {"Y": 11, "N": 4}, "percentages": {"Y": 73.33, "N": 26.67}}, "vision_plan_distribution": {"counts": {"Y": 11, "N": 4}, "percentages": {"Y": 73.33, "N": 26.67}}, "life_plan_distribution": {"counts": {}, "percentages": {}}}, "geographic_distribution": {"state_distribution": {"counts": {"CA": 1, "TX": 1, "FL": 1, "NY": 1, "WA": 1, "OR": 1, "AZ": 1, "CO": 1, "NV": 1, "UT": 1, "MT": 1, "ID": 1, "WY": 1, "ND": 1, "SD": 1}, "percentages": {"CA": 6.67, "TX": 6.67, "FL": 6.67, "NY": 6.67, "WA": 6.67, "OR": 6.67, "AZ": 6.67, "CO": 6.67, "NV": 6.67, "UT": 6.67, "MT": 6.67, "ID": 6.67, "WY": 6.67, "ND": 6.67, "SD": 6.67}}, "region_distribution": {"counts": {"Rural": 7, "Urban": 6, "Suburban": 2}, "percentages": {"Rural": 46.67, "Urban": 40.0, "Suburban": 13.33}}, "top_cities": {"counts": {"Anytown": 1, "Somewhere": 1, "Elsewhere": 1, "Nowhere": 1, "Anywhere": 1, "Someplace": 1, "Othertown": 1, "Newplace": 1, "Lastplace": 1, "Finaltown": 1}, "percentages": {"Anytown": 6.67, "Somewhere": 6.67, "Elsewhere": 6.67, "Nowhere": 6.67, "Anywhere": 6.67, "Someplace": 6.67, "Othertown": 6.67, "Newplace": 6.67, "Lastplace": 6.67, "Finaltown": 6.67}}}, "financial_demographics": {"income_tier_distribution": {"counts": {"Medium ($50K–$100K)": 8, "Low (<$50K)": 4, "High (>$100K)": 3}, "percentages": {"Medium ($50K–$100K)": 53.33, "Low (<$50K)": 26.67, "High (>$100K)": 20.0}}, "risk_tolerance_distribution": {"counts": {"Low": 9, "Medium": 4, "High": 2}, "percentages": {"Low": 60.0, "Medium": 26.67, "High": 13.33}}, "hsa_familiarity_distribution": {"counts": {"N": 10, "Y": 5}, "percentages": {"N": 66.67, "Y": 33.33}}}, "risk_assessment": {"group_risk_score": 31.13, "group_risk_level": "Medium", "risk_distribution": {"low_risk": 8, "medium_risk": 6, "high_risk": 1}, "risk_statistics": {"min_risk_score": 13, "max_risk_score": 68, "median_risk_score": 28.0, "std_risk_score": 14.03}, "top_risk_factors": {"Poor Health Condition": 7, "Tobacco Use": 3, "Chronic Conditions": 3, "Advanced Age": 1}, "individual_risks": [{"employee_id": "E001", "risk_score": 18, "risk_level": "Low", "risk_factors": []}, {"employee_id": "E002", "risk_score": 30, "risk_level": "Medium", "risk_factors": ["Age 50.0 (high risk)"]}, {"employee_id": "E003", "risk_score": 25, "risk_level": "Low", "risk_factors": ["Health condition: Fair"]}, {"employee_id": "E004", "risk_score": 30, "risk_level": "Medium", "risk_factors": ["Tobacco user"]}, {"employee_id": "E005", "risk_score": 43, "risk_level": "Medium", "risk_factors": ["Has chronic condition"]}, {"employee_id": "E006", "risk_score": 33, "risk_level": "Medium", "risk_factors": ["Health condition: Fair"]}, {"employee_id": "E007", "risk_score": 43, "risk_level": "Medium", "risk_factors": ["Health condition: Fair", "Tobacco user"]}, {"employee_id": "E008", "risk_score": 50, "risk_level": "Medium", "risk_factors": ["Health condition: Fair", "Has chronic condition"]}, {"employee_id": "E009", "risk_score": 23, "risk_level": "Low", "risk_factors": []}, {"employee_id": "E010", "risk_score": 25, "risk_level": "Low", "risk_factors": ["Health condition: Fair"]}]}, "data_quality_metrics": {"overall_completeness": {"total_cells": 2580, "missing_cells": "1807", "completeness_percentage": 29.96}, "column_completeness": {"first_name": {"missing_count": "0", "completeness_percentage": 100.0}, "last_name": {"missing_count": "0", "completeness_percentage": 100.0}, "gender": {"missing_count": "0", "completeness_percentage": 100.0}, "dob": {"missing_count": "0", "completeness_percentage": 100.0}, "address1": {"missing_count": "0", "completeness_percentage": 100.0}, "city": {"missing_count": "0", "completeness_percentage": 100.0}, "state": {"missing_count": "0", "completeness_percentage": 100.0}, "zipcode": {"missing_count": "0", "completeness_percentage": 100.0}, "marital_status": {"missing_count": "0", "completeness_percentage": 100.0}, "medical_plan": {"missing_count": "0", "completeness_percentage": 100.0}, "dental_plan": {"missing_count": "0", "completeness_percentage": 100.0}, "vision_plan": {"missing_count": "0", "completeness_percentage": 100.0}, "dept_1_first_name": {"missing_count": "3", "completeness_percentage": 80.0}, "dept_1_last_name": {"missing_count": "3", "completeness_percentage": 80.0}, "dept_1_gender": {"missing_count": "3", "completeness_percentage": 80.0}, "dept_1_dob": {"missing_count": "3", "completeness_percentage": 80.0}, "dept_1_address_line1": {"missing_count": "3", "completeness_percentage": 80.0}, "dept_1_city": {"missing_count": "3", "completeness_percentage": 80.0}, "dept_1_state": {"missing_count": "3", "completeness_percentage": 80.0}, "dept_1_zipcode": {"missing_count": "3", "completeness_percentage": 80.0}, "dept_1_marital_status": {"missing_count": "3", "completeness_percentage": 80.0}, "dept_1_medical_plan": {"missing_count": "3", "completeness_percentage": 80.0}, "dept_1_dental_plan": {"missing_count": "3", "completeness_percentage": 80.0}, "dept_1_vision_plan": {"missing_count": "3", "completeness_percentage": 80.0}, "dept_1": {"missing_count": "3", "completeness_percentage": 80.0}, "dept_2_first_name": {"missing_count": "8", "completeness_percentage": 46.67}, "dept_2_last_name": {"missing_count": "8", "completeness_percentage": 46.67}, "dept_2_gender": {"missing_count": "8", "completeness_percentage": 46.67}, "dept_2_dob": {"missing_count": "8", "completeness_percentage": 46.67}, "dept_2_address_line1": {"missing_count": "8", "completeness_percentage": 46.67}, "dept_2_city": {"missing_count": "8", "completeness_percentage": 46.67}, "dept_2_state": {"missing_count": "8", "completeness_percentage": 46.67}, "dept_2_zipcode": {"missing_count": "8", "completeness_percentage": 46.67}, "dept_2_marital_status": {"missing_count": "8", "completeness_percentage": 46.67}, "dept_2_medical_plan": {"missing_count": "8", "completeness_percentage": 46.67}, "dept_2_dental_plan": {"missing_count": "8", "completeness_percentage": 46.67}, "dept_2_vision_plan": {"missing_count": "8", "completeness_percentage": 46.67}, "dept_2": {"missing_count": "8", "completeness_percentage": 46.67}, "dept_count": {"missing_count": "0", "completeness_percentage": 100.0}, "dept_3_first_name": {"missing_count": "12", "completeness_percentage": 20.0}, "dept_3_last_name": {"missing_count": "12", "completeness_percentage": 20.0}, "dept_3_gender": {"missing_count": "12", "completeness_percentage": 20.0}, "dept_3_dob": {"missing_count": "12", "completeness_percentage": 20.0}, "dept_3_address_line1": {"missing_count": "12", "completeness_percentage": 20.0}, "dept_3_city": {"missing_count": "12", "completeness_percentage": 20.0}, "dept_3_state": {"missing_count": "12", "completeness_percentage": 20.0}, "dept_3_zipcode": {"missing_count": "12", "completeness_percentage": 20.0}, "dept_3_marital_status": {"missing_count": "12", "completeness_percentage": 20.0}, "dept_3_medical_plan": {"missing_count": "12", "completeness_percentage": 20.0}, "dept_3_dental_plan": {"missing_count": "12", "completeness_percentage": 20.0}, "dept_3_vision_plan": {"missing_count": "12", "completeness_percentage": 20.0}, "dept_3": {"missing_count": "12", "completeness_percentage": 20.0}, "name": {"missing_count": "0", "completeness_percentage": 100.0}, "age": {"missing_count": "0", "completeness_percentage": 100.0}, "dept_1_age": {"missing_count": "3", "completeness_percentage": 80.0}, "dept_2_age": {"missing_count": "8", "completeness_percentage": 46.67}, "dept_3_age": {"missing_count": "12", "completeness_percentage": 20.0}, "employee_id": {"missing_count": "0", "completeness_percentage": 100.0}, "middle_name": {"missing_count": "15", "completeness_percentage": 0.0}, "address2": {"missing_count": "15", "completeness_percentage": 0.0}, "relationship": {"missing_count": "15", "completeness_percentage": 0.0}, "record_type": {"missing_count": "15", "completeness_percentage": 0.0}, "salary": {"missing_count": "15", "completeness_percentage": 0.0}, "employment_type": {"missing_count": "0", "completeness_percentage": 100.0}, "employee_class": {"missing_count": "15", "completeness_percentage": 0.0}, "department": {"missing_count": "0", "completeness_percentage": 100.0}, "hire_date": {"missing_count": "15", "completeness_percentage": 0.0}, "tobacco_use": {"missing_count": "0", "completeness_percentage": 100.0}, "pregnancy_status": {"missing_count": "0", "completeness_percentage": 100.0}, "life_plan": {"missing_count": "15", "completeness_percentage": 0.0}, "add_plan": {"missing_count": "15", "completeness_percentage": 0.0}, "coverage_tier": {"missing_count": "15", "completeness_percentage": 0.0}, "ssn": {"missing_count": "15", "completeness_percentage": 0.0}, "relationship_type_1": {"missing_count": "15", "completeness_percentage": 0.0}, "relationship_type_2": {"missing_count": "15", "completeness_percentage": 0.0}, "relationship_type_3": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_4": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_4_dob": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_4_age": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_4_gender": {"missing_count": "15", "completeness_percentage": 0.0}, "relationship_type_4": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_5": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_5_dob": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_5_age": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_5_gender": {"missing_count": "15", "completeness_percentage": 0.0}, "relationship_type_5": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_6": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_6_dob": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_6_age": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_6_gender": {"missing_count": "15", "completeness_percentage": 0.0}, "relationship_type_6": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_7": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_7_dob": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_7_age": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_7_gender": {"missing_count": "15", "completeness_percentage": 0.0}, "relationship_type_7": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_8": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_8_dob": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_8_age": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_8_gender": {"missing_count": "15", "completeness_percentage": 0.0}, "relationship_type_8": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_9": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_9_dob": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_9_age": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_9_gender": {"missing_count": "15", "completeness_percentage": 0.0}, "relationship_type_9": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_10": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_10_dob": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_10_age": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_10_gender": {"missing_count": "15", "completeness_percentage": 0.0}, "relationship_type_10": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_11": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_11_dob": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_11_age": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_11_gender": {"missing_count": "15", "completeness_percentage": 0.0}, "relationship_type_11": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_12": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_12_dob": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_12_age": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_12_gender": {"missing_count": "15", "completeness_percentage": 0.0}, "relationship_type_12": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_13": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_13_dob": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_13_age": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_13_gender": {"missing_count": "15", "completeness_percentage": 0.0}, "relationship_type_13": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_14": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_14_dob": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_14_age": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_14_gender": {"missing_count": "15", "completeness_percentage": 0.0}, "relationship_type_14": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_15": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_15_dob": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_15_age": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_15_gender": {"missing_count": "15", "completeness_percentage": 0.0}, "relationship_type_15": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_16": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_16_dob": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_16_age": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_16_gender": {"missing_count": "15", "completeness_percentage": 0.0}, "relationship_type_16": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_17": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_17_dob": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_17_age": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_17_gender": {"missing_count": "15", "completeness_percentage": 0.0}, "relationship_type_17": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_18": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_18_dob": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_18_age": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_18_gender": {"missing_count": "15", "completeness_percentage": 0.0}, "relationship_type_18": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_19": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_19_dob": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_19_age": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_19_gender": {"missing_count": "15", "completeness_percentage": 0.0}, "relationship_type_19": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_20": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_20_dob": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_20_age": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_20_gender": {"missing_count": "15", "completeness_percentage": 0.0}, "relationship_type_20": {"missing_count": "15", "completeness_percentage": 0.0}, "job_type": {"missing_count": "0", "completeness_percentage": 100.0}, "region": {"missing_count": "0", "completeness_percentage": 100.0}, "health_condition": {"missing_count": "0", "completeness_percentage": 100.0}, "chronic_condition": {"missing_count": "0", "completeness_percentage": 100.0}, "prescription_use": {"missing_count": "0", "completeness_percentage": 100.0}, "income_tier": {"missing_count": "0", "completeness_percentage": 100.0}, "risk_tolerance": {"missing_count": "0", "completeness_percentage": 100.0}, "lifestyle": {"missing_count": "0", "completeness_percentage": 100.0}, "travel_frequency": {"missing_count": "0", "completeness_percentage": 100.0}, "hsa_familiarity": {"missing_count": "0", "completeness_percentage": 100.0}, "mental_health_needs": {"missing_count": "0", "completeness_percentage": 100.0}}, "critical_field_completeness": {"name": {"missing_count": "0", "completeness_percentage": 100.0}, "gender": {"missing_count": "0", "completeness_percentage": 100.0}, "zipcode": {"missing_count": "0", "completeness_percentage": 100.0}, "marital_status": {"missing_count": "0", "completeness_percentage": 100.0}}}}, "prediction_summary": {"total_employees": 15, "plan_type_distribution": {"successful_predictions": {"PPO": 5, "HMO": 4, "HDHP + HSA": 3, "POS": 2, "Indemnity": 1}, "failed_predictions": "0", "success_rate": 100.0, "total_predicted": "15"}, "benefits_distribution": {}, "confidence_metrics": {"plan_confidence": {"mean": "0.749", "min": "0.668", "max": "0.914", "count": 15}}, "prediction_success_rates": {}, "top_3_plan_probabilities": []}, "total_employees": 15, "total_columns": 182, "has_predictions": true, "prediction_columns": ["predicted_plan_type", "plan_confidence"]}, "metadata": {"file_processing": {"filename": "row2_address_grouped.csv", "size": 2869, "original_column_names": ["First_Name", "Last_Name", "Gender", "DOB", "Address1", "City", "State", "ZIP", "Marital_Status", "Medical_Plan", "Dental_Plan", "Vision_Plan"]}, "pattern_analysis": {"pattern_type": "address_based_grouping", "pattern_confidence": 1.0999999999999999, "pattern_reason": "Found 'address1' column; Found duplicate addresses; No explicit relationship/ID columns found; Found name columns", "analysis_details": {"confidence": 1.0999999999999999, "reason": "Found 'address1' column; Found duplicate addresses; No explicit relationship/ID columns found; Found name columns"}}, "field_mapping": {"total_fields_mapped": 12, "mapping_success": true, "unmapped_fields": [], "mapping_confidence": 0, "field_mapping_details": {"First_Name": "first_name", "Last_Name": "last_name", "Gender": "gender", "DOB": "dob", "Address1": "address1", "City": "city", "State": "state", "ZIP": "zipcode", "Marital_Status": "marital_status", "Medical_Plan": "medical_plan", "Dental_Plan": "dental_plan", "Vision_Plan": "vision_plan"}}, "data_processing": {"validation_passed": false, "processing_summary": {"original_rows": 37, "original_columns": 12, "processed_rows": 15, "processed_columns": 161, "processing_time_seconds": 24.85, "validation_passed": true, "validation_errors": 0, "error_rows": 0, "fields_processed": {"name_fields": true, "age_converted": true, "gender_standardized": true, "addresses_cleaned": true, "zipcode_validated": true}, "data_quality": {"complete_records": 15, "missing_data_rows": 0}}, "data_quality_score": 0.562, "processing_details": {"success": true, "preprocessed_data": "   first_name  last_name  gender         dob        address1       city state  ...  dept_19_gender relationship_type_19 dept_20 dept_20_dob dept_20_age dept_20_gender relationship_type_20\n0        <PERSON>  1980-01-15     123 Main St    Anytown    CA  ...            None                 None    None        None        None           None                 None\n1       <PERSON>  Female  1975-03-10     456 Oak Ave  Somewhere    TX  ...            None                 None    None        None        None           None                 None\n2     <PERSON>  1990-06-30     789 Pine Rd  Elsewhere    FL  ...            None                 None    None        None        None           None                 None\n3       <PERSON>  1985-11-05      321 Elm St    Nowhere    NY  ...            None                 None    None        None        None           None                 None\n4       <PERSON>  1978-02-28    654 Maple Dr   Anywhere    WA  ...            None                 None    None        None        None           None                 None\n5     <PERSON>  1983-07-12    987 Cedar Ln  Someplace    OR  ...            None                 None    None        None        None           None                 None\n6       <PERSON>  1987-12-03   147 Birch Ave  Othertown    AZ  ...            None                 None    None        None        None           None                 None\n7       <PERSON>  1979-10-17   258 Spruce St   Newplace    CO  ...            None                 None    None        None        None           None                 None\n8        <PERSON>  1981-03-24  369 Willow Way  Lastplace    NV  ...            None                 None    None        None        None           None                 None\n9      <PERSON>  1986-09-09   741 Poplar Pl  Finaltown    UT  ...            None                 None    None        None        None           None                 None\n10     <PERSON>  1984-12-18      852 Ash Ct   Hometown    MT  ...            None                 None    None        None        None           None                 None\n11      <PERSON>  1977-04-07    963 Beech St   Yourtown    ID  ...            None                 None    None        None        None           None                 None\n12     <PERSON>  1989-08-21  159 <PERSON> Ave     Mytown    WY  ...            None                 None    None        None        None           None                 None\n13     Connor      Clark    Male  1982-01-26  357 Dogwood <PERSON>    Ourtown    ND  ...            None                 None    None        None        None           None                 None\n14    Destiny      Lewis  Female  1986-05-13      468 Fir Ln  Theirtown    SD  ...            None                 None    None        None        None           None                 None\n\n[15 rows x 161 columns]", "validation": {"is_valid": true, "errors": [], "error_rows": [], "total_errors": 0}, "summary": {"original_rows": 37, "original_columns": 12, "processed_rows": 15, "processed_columns": 161, "processing_time_seconds": 24.85, "validation_passed": true, "validation_errors": 0, "error_rows": 0, "fields_processed": {"name_fields": true, "age_converted": true, "gender_standardized": true, "addresses_cleaned": true, "zipcode_validated": true}, "data_quality": {"complete_records": 15, "missing_data_rows": 0}}}}, "enrichment_and_prediction": {"success": true, "enriched_data": "   first_name  last_name  gender  ...                                    benefits_reason                                 top_3_benefits top_3_benefits_confidences\n0        <PERSON>  ...  Dental: Essential oral health coverage for all...  [Employee Assistance, Wellness Programs, FSA]            [0.5, 0.5, 0.5]\n1       <PERSON>  Female  ...  Dental: Essential oral health coverage for all...  [Employee Assistance, Wellness Programs, FSA]            [0.5, 0.5, 0.5]\n2     <PERSON>    Male  ...  Accident: Recommended for active lifestyle and...  [Employee Assistance, Wellness Programs, FSA]            [0.5, 0.5, 0.5]\n3       <PERSON>  Female  ...  Dental: Essential oral health coverage for all...  [Employee Assistance, Wellness Programs, FSA]            [0.5, 0.5, 0.5]\n4       <PERSON>    Male  ...  Critical Illness: Important protection for age...  [Employee Assistance, Wellness Programs, FSA]            [0.5, 0.5, 0.5]\n5     <PERSON>    Male  ...                                ML model prediction  [Employee Assistance, Wellness Programs, FSA]            [0.5, 0.5, 0.5]\n6       <PERSON>  ...  Dental: Essential oral health coverage for all...  [Employee Assistance, Wellness Programs, FSA]            [0.5, 0.5, 0.5]\n7       Quinn   Martinez    Male  ...  Dental: Essential oral health coverage for all...  [Employee Assistance, Wellness Programs, FSA]            [0.5, 0.5, 0.5]\n8        <PERSON>  Female  ...  Dental: Essential oral health coverage for all...  [Employee Assistance, Wellness Programs, FSA]            [0.5, 0.5, 0.5]\n9      Xavier  Rodriguez    Male  ...                                ML model prediction  [Employee Assistance, Wellness Programs, FSA]            [0.5, 0.5, 0.5]\n10     <PERSON>        <PERSON>  ...  Dental: Essential oral health coverage for all...  [Employee Assistance, Wellness Programs, FSA]            [0.5, 0.5, 0.5]\n11      Aaron      White    Male  ...  Default benefits assignment due to prediction ...           [Dental, Vision, Hospital Indemnity]            [0.5, 0.5, 0.5]\n12     Brooke     Harris  Female  ...  Default benefits assignment due to prediction ...           [Dental, Vision, Hospital Indemnity]            [0.5, 0.5, 0.5]\n13     Connor      Clark    Male  ...  Default benefits assignment due to prediction ...           [Dental, Vision, Hospital Indemnity]            [0.5, 0.5, 0.5]\n14    Destiny      Lewis  Female  ...  Default benefits assignment due to prediction ...           [Dental, Vision, Hospital Indemnity]            [0.5, 0.5, 0.5]\n\n[15 rows x 182 columns]", "enrichment_summary": {"total_employees": 15, "features_analyzed": 19, "enrichment_details": {"age": {"original_missing": "0", "final_missing": "0", "enriched_count": "0", "completion_rate": 100.0}, "gender": {"original_missing": "0", "final_missing": "0", "enriched_count": "0", "completion_rate": 100.0}, "marital_status": {"original_missing": "0", "final_missing": "0", "enriched_count": "0", "completion_rate": 100.0}, "dept_count": {"original_missing": "0", "final_missing": "0", "enriched_count": "0", "completion_rate": 100.0}, "job_type": {"original_missing": 15, "final_missing": "0", "enriched_count": "15", "completion_rate": 100.0}, "employment_type": {"original_missing": "15", "final_missing": "0", "enriched_count": "15", "completion_rate": 100.0}, "department": {"original_missing": "15", "final_missing": "0", "enriched_count": "15", "completion_rate": 100.0}, "region": {"original_missing": 15, "final_missing": "0", "enriched_count": "15", "completion_rate": 100.0}, "health_condition": {"original_missing": 15, "final_missing": "0", "enriched_count": "15", "completion_rate": 100.0}, "chronic_condition": {"original_missing": 15, "final_missing": "0", "enriched_count": "15", "completion_rate": 100.0}, "prescription_use": {"original_missing": 15, "final_missing": "0", "enriched_count": "15", "completion_rate": 100.0}, "income_tier": {"original_missing": 15, "final_missing": "0", "enriched_count": "15", "completion_rate": 100.0}, "risk_tolerance": {"original_missing": 15, "final_missing": "0", "enriched_count": "15", "completion_rate": 100.0}, "lifestyle": {"original_missing": 15, "final_missing": "0", "enriched_count": "15", "completion_rate": 100.0}, "travel_frequency": {"original_missing": 15, "final_missing": "0", "enriched_count": "15", "completion_rate": 100.0}, "hsa_familiarity": {"original_missing": 15, "final_missing": "0", "enriched_count": "15", "completion_rate": 100.0}, "pregnancy_status": {"original_missing": "15", "final_missing": "0", "enriched_count": "15", "completion_rate": 100.0}, "tobacco_use": {"original_missing": "15", "final_missing": "0", "enriched_count": "15", "completion_rate": 100.0}, "mental_health_needs": {"original_missing": 15, "final_missing": "0", "enriched_count": "15", "completion_rate": 100.0}}, "data_quality_improvement": {"total_missing_before": "225", "total_missing_after": "0", "total_enriched": "225", "overall_completion_rate": 100.0}}, "comprehensive_statistics": {"basic_demographics": {"age_statistics": {"average_age": 41.8, "median_age": 42.0, "age_range": {"min": 35, "max": 50}, "age_distribution": {"18-30": 0, "31-45": 12, "46-60": 3, "60+": 0}}, "gender_composition": {"counts": {"Male": 8, "Female": 7}, "percentages": {"Male": 53.33, "Female": 46.67}}, "marital_status_distribution": {"counts": {"Not Married": 8, "Married": 7}, "percentages": {"Not Married": 53.33, "Married": 46.67}}}, "dependent_analysis": {"dependent_count_distribution": {"average_dependents": 1.4666666666666666, "median_dependents": 1.0, "distribution": {"1": 5, "2": 4, "3": 3, "0": 3}, "employees_with_dependents": 12, "percentage_with_dependents": 80.0}, "dependent_age_analysis": {"total_dependents_found": 22, "average_dependent_age": 22.68, "median_dependent_age": 16.0, "dependents_over_26_as_children": 0, "age_distribution": {"0-5": 0, "6-12": 3, "13-18": 10, "19-26": 2, "26+": 7}}}, "employment_demographics": {"department_distribution": {"counts": {"Information Technology": 5, "Sales": 3, "Manufacturing": 3, "Engineering": 3, "Finance": 1}, "percentages": {"Information Technology": 33.33, "Sales": 20.0, "Manufacturing": 20.0, "Engineering": 20.0, "Finance": 6.67}}, "employment_type_distribution": {"counts": {"Full-Time": 11, "Part-Time": 3, "Contract": 1}, "percentages": {"Full-Time": 73.33, "Part-Time": 20.0, "Contract": 6.67}}, "job_type_distribution": {"counts": {"Desk": 15}, "percentages": {"Desk": 100.0}}, "employee_class_distribution": {"counts": {}, "percentages": {}}}, "health_and_lifestyle": {"health_condition_distribution": {"counts": {"Fair": 7, "Good": 5, "Excellent": 3}, "percentages": {"Fair": 46.67, "Good": 33.33, "Excellent": 20.0}}, "chronic_condition_distribution": {"counts": {"N": 12, "Y": 3}, "percentages": {"N": 80.0, "Y": 20.0}}, "tobacco_use_distribution": {"counts": {"N": 12, "Y": 3}, "percentages": {"N": 80.0, "Y": 20.0}}, "lifestyle_distribution": {"counts": {"Moderate": 9, "Active": 6}, "percentages": {"Moderate": 60.0, "Active": 40.0}}, "prescription_use_distribution": {"counts": {"None": 8, "Occasional": 5, "Regular": 2}, "percentages": {"None": 53.33, "Occasional": 33.33, "Regular": 13.33}}}, "coverage_analysis": {"coverage_tier_distribution": {"counts": {}, "percentages": {}}, "coverage_tier_by_family_size": {}, "medical_plan_distribution": {"counts": {"Y": 12, "N": 3}, "percentages": {"Y": 80.0, "N": 20.0}}, "dental_plan_distribution": {"counts": {"Y": 11, "N": 4}, "percentages": {"Y": 73.33, "N": 26.67}}, "vision_plan_distribution": {"counts": {"Y": 11, "N": 4}, "percentages": {"Y": 73.33, "N": 26.67}}, "life_plan_distribution": {"counts": {}, "percentages": {}}}, "geographic_distribution": {"state_distribution": {"counts": {"CA": 1, "TX": 1, "FL": 1, "NY": 1, "WA": 1, "OR": 1, "AZ": 1, "CO": 1, "NV": 1, "UT": 1, "MT": 1, "ID": 1, "WY": 1, "ND": 1, "SD": 1}, "percentages": {"CA": 6.67, "TX": 6.67, "FL": 6.67, "NY": 6.67, "WA": 6.67, "OR": 6.67, "AZ": 6.67, "CO": 6.67, "NV": 6.67, "UT": 6.67, "MT": 6.67, "ID": 6.67, "WY": 6.67, "ND": 6.67, "SD": 6.67}}, "region_distribution": {"counts": {"Rural": 7, "Urban": 6, "Suburban": 2}, "percentages": {"Rural": 46.67, "Urban": 40.0, "Suburban": 13.33}}, "top_cities": {"counts": {"Anytown": 1, "Somewhere": 1, "Elsewhere": 1, "Nowhere": 1, "Anywhere": 1, "Someplace": 1, "Othertown": 1, "Newplace": 1, "Lastplace": 1, "Finaltown": 1}, "percentages": {"Anytown": 6.67, "Somewhere": 6.67, "Elsewhere": 6.67, "Nowhere": 6.67, "Anywhere": 6.67, "Someplace": 6.67, "Othertown": 6.67, "Newplace": 6.67, "Lastplace": 6.67, "Finaltown": 6.67}}}, "financial_demographics": {"income_tier_distribution": {"counts": {"Medium ($50K–$100K)": 8, "Low (<$50K)": 4, "High (>$100K)": 3}, "percentages": {"Medium ($50K–$100K)": 53.33, "Low (<$50K)": 26.67, "High (>$100K)": 20.0}}, "risk_tolerance_distribution": {"counts": {"Low": 9, "Medium": 4, "High": 2}, "percentages": {"Low": 60.0, "Medium": 26.67, "High": 13.33}}, "hsa_familiarity_distribution": {"counts": {"N": 10, "Y": 5}, "percentages": {"N": 66.67, "Y": 33.33}}}, "risk_assessment": {"group_risk_score": 31.13, "group_risk_level": "Medium", "risk_distribution": {"low_risk": 8, "medium_risk": 6, "high_risk": 1}, "risk_statistics": {"min_risk_score": 13, "max_risk_score": 68, "median_risk_score": 28.0, "std_risk_score": 14.03}, "top_risk_factors": {"Poor Health Condition": 7, "Tobacco Use": 3, "Chronic Conditions": 3, "Advanced Age": 1}, "individual_risks": [{"employee_id": "E001", "risk_score": 18, "risk_level": "Low", "risk_factors": []}, {"employee_id": "E002", "risk_score": 30, "risk_level": "Medium", "risk_factors": ["Age 50.0 (high risk)"]}, {"employee_id": "E003", "risk_score": 25, "risk_level": "Low", "risk_factors": ["Health condition: Fair"]}, {"employee_id": "E004", "risk_score": 30, "risk_level": "Medium", "risk_factors": ["Tobacco user"]}, {"employee_id": "E005", "risk_score": 43, "risk_level": "Medium", "risk_factors": ["Has chronic condition"]}, {"employee_id": "E006", "risk_score": 33, "risk_level": "Medium", "risk_factors": ["Health condition: Fair"]}, {"employee_id": "E007", "risk_score": 43, "risk_level": "Medium", "risk_factors": ["Health condition: Fair", "Tobacco user"]}, {"employee_id": "E008", "risk_score": 50, "risk_level": "Medium", "risk_factors": ["Health condition: Fair", "Has chronic condition"]}, {"employee_id": "E009", "risk_score": 23, "risk_level": "Low", "risk_factors": []}, {"employee_id": "E010", "risk_score": 25, "risk_level": "Low", "risk_factors": ["Health condition: Fair"]}]}, "data_quality_metrics": {"overall_completeness": {"total_cells": 2580, "missing_cells": "1807", "completeness_percentage": 29.96}, "column_completeness": {"first_name": {"missing_count": "0", "completeness_percentage": 100.0}, "last_name": {"missing_count": "0", "completeness_percentage": 100.0}, "gender": {"missing_count": "0", "completeness_percentage": 100.0}, "dob": {"missing_count": "0", "completeness_percentage": 100.0}, "address1": {"missing_count": "0", "completeness_percentage": 100.0}, "city": {"missing_count": "0", "completeness_percentage": 100.0}, "state": {"missing_count": "0", "completeness_percentage": 100.0}, "zipcode": {"missing_count": "0", "completeness_percentage": 100.0}, "marital_status": {"missing_count": "0", "completeness_percentage": 100.0}, "medical_plan": {"missing_count": "0", "completeness_percentage": 100.0}, "dental_plan": {"missing_count": "0", "completeness_percentage": 100.0}, "vision_plan": {"missing_count": "0", "completeness_percentage": 100.0}, "dept_1_first_name": {"missing_count": "3", "completeness_percentage": 80.0}, "dept_1_last_name": {"missing_count": "3", "completeness_percentage": 80.0}, "dept_1_gender": {"missing_count": "3", "completeness_percentage": 80.0}, "dept_1_dob": {"missing_count": "3", "completeness_percentage": 80.0}, "dept_1_address_line1": {"missing_count": "3", "completeness_percentage": 80.0}, "dept_1_city": {"missing_count": "3", "completeness_percentage": 80.0}, "dept_1_state": {"missing_count": "3", "completeness_percentage": 80.0}, "dept_1_zipcode": {"missing_count": "3", "completeness_percentage": 80.0}, "dept_1_marital_status": {"missing_count": "3", "completeness_percentage": 80.0}, "dept_1_medical_plan": {"missing_count": "3", "completeness_percentage": 80.0}, "dept_1_dental_plan": {"missing_count": "3", "completeness_percentage": 80.0}, "dept_1_vision_plan": {"missing_count": "3", "completeness_percentage": 80.0}, "dept_1": {"missing_count": "3", "completeness_percentage": 80.0}, "dept_2_first_name": {"missing_count": "8", "completeness_percentage": 46.67}, "dept_2_last_name": {"missing_count": "8", "completeness_percentage": 46.67}, "dept_2_gender": {"missing_count": "8", "completeness_percentage": 46.67}, "dept_2_dob": {"missing_count": "8", "completeness_percentage": 46.67}, "dept_2_address_line1": {"missing_count": "8", "completeness_percentage": 46.67}, "dept_2_city": {"missing_count": "8", "completeness_percentage": 46.67}, "dept_2_state": {"missing_count": "8", "completeness_percentage": 46.67}, "dept_2_zipcode": {"missing_count": "8", "completeness_percentage": 46.67}, "dept_2_marital_status": {"missing_count": "8", "completeness_percentage": 46.67}, "dept_2_medical_plan": {"missing_count": "8", "completeness_percentage": 46.67}, "dept_2_dental_plan": {"missing_count": "8", "completeness_percentage": 46.67}, "dept_2_vision_plan": {"missing_count": "8", "completeness_percentage": 46.67}, "dept_2": {"missing_count": "8", "completeness_percentage": 46.67}, "dept_count": {"missing_count": "0", "completeness_percentage": 100.0}, "dept_3_first_name": {"missing_count": "12", "completeness_percentage": 20.0}, "dept_3_last_name": {"missing_count": "12", "completeness_percentage": 20.0}, "dept_3_gender": {"missing_count": "12", "completeness_percentage": 20.0}, "dept_3_dob": {"missing_count": "12", "completeness_percentage": 20.0}, "dept_3_address_line1": {"missing_count": "12", "completeness_percentage": 20.0}, "dept_3_city": {"missing_count": "12", "completeness_percentage": 20.0}, "dept_3_state": {"missing_count": "12", "completeness_percentage": 20.0}, "dept_3_zipcode": {"missing_count": "12", "completeness_percentage": 20.0}, "dept_3_marital_status": {"missing_count": "12", "completeness_percentage": 20.0}, "dept_3_medical_plan": {"missing_count": "12", "completeness_percentage": 20.0}, "dept_3_dental_plan": {"missing_count": "12", "completeness_percentage": 20.0}, "dept_3_vision_plan": {"missing_count": "12", "completeness_percentage": 20.0}, "dept_3": {"missing_count": "12", "completeness_percentage": 20.0}, "name": {"missing_count": "0", "completeness_percentage": 100.0}, "age": {"missing_count": "0", "completeness_percentage": 100.0}, "dept_1_age": {"missing_count": "3", "completeness_percentage": 80.0}, "dept_2_age": {"missing_count": "8", "completeness_percentage": 46.67}, "dept_3_age": {"missing_count": "12", "completeness_percentage": 20.0}, "employee_id": {"missing_count": "0", "completeness_percentage": 100.0}, "middle_name": {"missing_count": "15", "completeness_percentage": 0.0}, "address2": {"missing_count": "15", "completeness_percentage": 0.0}, "relationship": {"missing_count": "15", "completeness_percentage": 0.0}, "record_type": {"missing_count": "15", "completeness_percentage": 0.0}, "salary": {"missing_count": "15", "completeness_percentage": 0.0}, "employment_type": {"missing_count": "0", "completeness_percentage": 100.0}, "employee_class": {"missing_count": "15", "completeness_percentage": 0.0}, "department": {"missing_count": "0", "completeness_percentage": 100.0}, "hire_date": {"missing_count": "15", "completeness_percentage": 0.0}, "tobacco_use": {"missing_count": "0", "completeness_percentage": 100.0}, "pregnancy_status": {"missing_count": "0", "completeness_percentage": 100.0}, "life_plan": {"missing_count": "15", "completeness_percentage": 0.0}, "add_plan": {"missing_count": "15", "completeness_percentage": 0.0}, "coverage_tier": {"missing_count": "15", "completeness_percentage": 0.0}, "ssn": {"missing_count": "15", "completeness_percentage": 0.0}, "relationship_type_1": {"missing_count": "15", "completeness_percentage": 0.0}, "relationship_type_2": {"missing_count": "15", "completeness_percentage": 0.0}, "relationship_type_3": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_4": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_4_dob": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_4_age": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_4_gender": {"missing_count": "15", "completeness_percentage": 0.0}, "relationship_type_4": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_5": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_5_dob": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_5_age": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_5_gender": {"missing_count": "15", "completeness_percentage": 0.0}, "relationship_type_5": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_6": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_6_dob": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_6_age": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_6_gender": {"missing_count": "15", "completeness_percentage": 0.0}, "relationship_type_6": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_7": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_7_dob": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_7_age": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_7_gender": {"missing_count": "15", "completeness_percentage": 0.0}, "relationship_type_7": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_8": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_8_dob": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_8_age": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_8_gender": {"missing_count": "15", "completeness_percentage": 0.0}, "relationship_type_8": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_9": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_9_dob": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_9_age": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_9_gender": {"missing_count": "15", "completeness_percentage": 0.0}, "relationship_type_9": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_10": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_10_dob": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_10_age": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_10_gender": {"missing_count": "15", "completeness_percentage": 0.0}, "relationship_type_10": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_11": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_11_dob": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_11_age": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_11_gender": {"missing_count": "15", "completeness_percentage": 0.0}, "relationship_type_11": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_12": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_12_dob": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_12_age": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_12_gender": {"missing_count": "15", "completeness_percentage": 0.0}, "relationship_type_12": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_13": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_13_dob": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_13_age": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_13_gender": {"missing_count": "15", "completeness_percentage": 0.0}, "relationship_type_13": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_14": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_14_dob": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_14_age": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_14_gender": {"missing_count": "15", "completeness_percentage": 0.0}, "relationship_type_14": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_15": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_15_dob": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_15_age": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_15_gender": {"missing_count": "15", "completeness_percentage": 0.0}, "relationship_type_15": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_16": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_16_dob": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_16_age": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_16_gender": {"missing_count": "15", "completeness_percentage": 0.0}, "relationship_type_16": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_17": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_17_dob": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_17_age": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_17_gender": {"missing_count": "15", "completeness_percentage": 0.0}, "relationship_type_17": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_18": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_18_dob": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_18_age": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_18_gender": {"missing_count": "15", "completeness_percentage": 0.0}, "relationship_type_18": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_19": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_19_dob": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_19_age": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_19_gender": {"missing_count": "15", "completeness_percentage": 0.0}, "relationship_type_19": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_20": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_20_dob": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_20_age": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_20_gender": {"missing_count": "15", "completeness_percentage": 0.0}, "relationship_type_20": {"missing_count": "15", "completeness_percentage": 0.0}, "job_type": {"missing_count": "0", "completeness_percentage": 100.0}, "region": {"missing_count": "0", "completeness_percentage": 100.0}, "health_condition": {"missing_count": "0", "completeness_percentage": 100.0}, "chronic_condition": {"missing_count": "0", "completeness_percentage": 100.0}, "prescription_use": {"missing_count": "0", "completeness_percentage": 100.0}, "income_tier": {"missing_count": "0", "completeness_percentage": 100.0}, "risk_tolerance": {"missing_count": "0", "completeness_percentage": 100.0}, "lifestyle": {"missing_count": "0", "completeness_percentage": 100.0}, "travel_frequency": {"missing_count": "0", "completeness_percentage": 100.0}, "hsa_familiarity": {"missing_count": "0", "completeness_percentage": 100.0}, "mental_health_needs": {"missing_count": "0", "completeness_percentage": 100.0}}, "critical_field_completeness": {"name": {"missing_count": "0", "completeness_percentage": 100.0}, "gender": {"missing_count": "0", "completeness_percentage": 100.0}, "zipcode": {"missing_count": "0", "completeness_percentage": 100.0}, "marital_status": {"missing_count": "0", "completeness_percentage": 100.0}}}}, "feature_validation": {"is_valid": true, "missing_features": [], "feature_completeness": {"age": {"present": true, "missing_values": "0", "completion_rate": 100.0}, "gender": {"present": true, "missing_values": "0", "completion_rate": 100.0}, "marital_status": {"present": true, "missing_values": "0", "completion_rate": 100.0}, "dept_count": {"present": true, "missing_values": "0", "completion_rate": 100.0}, "job_type": {"present": true, "missing_values": "0", "completion_rate": 100.0}, "employment_type": {"present": true, "missing_values": "0", "completion_rate": 100.0}, "department": {"present": true, "missing_values": "0", "completion_rate": 100.0}, "region": {"present": true, "missing_values": "0", "completion_rate": 100.0}, "health_condition": {"present": true, "missing_values": "0", "completion_rate": 100.0}, "chronic_condition": {"present": true, "missing_values": "0", "completion_rate": 100.0}, "prescription_use": {"present": true, "missing_values": "0", "completion_rate": 100.0}, "income_tier": {"present": true, "missing_values": "0", "completion_rate": 100.0}, "risk_tolerance": {"present": true, "missing_values": "0", "completion_rate": 100.0}, "lifestyle": {"present": true, "missing_values": "0", "completion_rate": 100.0}, "travel_frequency": {"present": true, "missing_values": "0", "completion_rate": 100.0}, "hsa_familiarity": {"present": true, "missing_values": "0", "completion_rate": 100.0}, "pregnancy_status": {"present": true, "missing_values": "0", "completion_rate": 100.0}, "tobacco_use": {"present": true, "missing_values": "0", "completion_rate": 100.0}, "mental_health_needs": {"present": true, "missing_values": "0", "completion_rate": 100.0}}, "overall_completion_rate": 100.0, "ready_for_prediction": "True"}, "prediction_method": "ml_models", "model_predictions": {"plan_types": {"total_predictions": 15, "average_confidence": "0.74926627", "unique_plans": 5}}, "prediction_summary": {"total_employees": 15, "plan_type_distribution": {"successful_predictions": {"PPO": 5, "HMO": 4, "HDHP + HSA": 3, "POS": 2, "Indemnity": 1}, "failed_predictions": "0", "success_rate": 100.0, "total_predicted": "15"}, "benefits_distribution": {}, "confidence_metrics": {"plan_confidence": {"mean": "0.749", "min": "0.668", "max": "0.914", "count": 15}}, "prediction_success_rates": {}, "top_3_plan_probabilities": ["E001: [0.7136569, 0.16095912, 0.05143935]", "E002: [0.7084166, 0.22614051, 0.04343438]", "E003: [0.6678085, 0.16203122, 0.08369441]", "E004: [0.7506588, 0.14031367, 0.05679907]", "E005: [0.7393914, 0.12662695, 0.094088875]", "E006: [0.7159531, 0.22471774, 0.030451266]", "E007: [0.6890726, 0.14733845, 0.091393426]", "E008: [0.7555324, 0.19199406, 0.04318231]", "E009: [0.86271435, 0.06526185, 0.05253908]", "E010: [0.7149105, 0.18115257, 0.07347947]", "E011: [0.7999597, 0.10200254, 0.07758328]", "E012: [0.8112744, 0.17172891, 0.011501331]", "E013: [0.701719, 0.20322482, 0.041865546]", "E014: [0.9138635, 0.05788933, 0.012788059]", "E015: [0.69406104, 0.104144596, 0.09539415]"]}}}}}