{"file_info": {"original_filename": "row3_mixed_relationships.csv", "processing_timestamp": "2025-07-15 17:41:51.788319", "total_processing_time_seconds": 26.49928641319275}, "final_response": {"success": true, "data": {"enriched_data_csv": "employee_id,record_type,full_name,relationship,dob,gender,address1,city,state,zipcode,marital_status,medical_plan,dental_plan,vision_plan,dept_1_full_name,dept_1_relationship_type,dept_1_dob,dept_1_gender,employee_address1,employee_city,employee_state,employee_zipcode,employee_marital_status,employee_medical_plan_enrollment,employee_dental_plan_enrollment,employee_vision_plan_enrollment,relationship_type_1,dept_1_medical_plan,dept_1_dental_plan,dept_1_vision_plan,dept_2_full_name,dept_2_relationship_type,dept_2_dob,dept_2_gender,relationship_type_2,dept_2_medical_plan,dept_2_dental_plan,dept_2_vision_plan,dept_count,dept_3_full_name,dept_3_relationship_type,dept_3_dob,dept_3_gender,relationship_type_3,dept_3_medical_plan,dept_3_dental_plan,dept_3_vision_plan,name,age,dept_1_age,dept_2_age,dept_3_age,first_name,middle_name,last_name,address2,salary,employment_type,employee_class,department,hire_date,tobacco_use,pregnancy_status,life_plan,add_plan,coverage_tier,ssn,dept_1,dept_2,dept_3,dept_4,dept_4_dob,dept_4_age,dept_4_gender,relationship_type_4,dept_5,dept_5_dob,dept_5_age,dept_5_gender,relationship_type_5,dept_6,dept_6_dob,dept_6_age,dept_6_gender,relationship_type_6,dept_7,dept_7_dob,dept_7_age,dept_7_gender,relationship_type_7,dept_8,dept_8_dob,dept_8_age,dept_8_gender,relationship_type_8,dept_9,dept_9_dob,dept_9_age,dept_9_gender,relationship_type_9,dept_10,dept_10_dob,dept_10_age,dept_10_gender,relationship_type_10,dept_11,dept_11_dob,dept_11_age,dept_11_gender,relationship_type_11,dept_12,dept_12_dob,dept_12_age,dept_12_gender,relationship_type_12,dept_13,dept_13_dob,dept_13_age,dept_13_gender,relationship_type_13,dept_14,dept_14_dob,dept_14_age,dept_14_gender,relationship_type_14,dept_15,dept_15_dob,dept_15_age,dept_15_gender,relationship_type_15,dept_16,dept_16_dob,dept_16_age,dept_16_gender,relationship_type_16,dept_17,dept_17_dob,dept_17_age,dept_17_gender,relationship_type_17,dept_18,dept_18_dob,dept_18_age,dept_18_gender,relationship_type_18,dept_19,dept_19_dob,dept_19_age,dept_19_gender,relationship_type_19,dept_20,dept_20_dob,dept_20_age,dept_20_gender,relationship_type_20,job_type,region,health_condition,chronic_condition,prescription_use,income_tier,risk_tolerance,lifestyle,travel_frequency,hsa_familiarity,mental_health_needs,predicted_plan_type,plan_confidence,plan_reason,top_3_plans,top_3_plan_confidences,predicted_benefits,benefits_confidence,benefits_reason,top_3_benefits,top_3_benefits_confidences\r\nEMP001,Secondary,John Smith,Employee,15/01/1980,Male,123 Oak Street,Springfield,IL,62701,Married,Active,Y,Y,Sarah Smith,Wife,20/05/1982,Female,123 Oak Street,Springfield,IL,62701.0,Married,Active,Yes,True,Wife,Active,Y,Y,Michael Smith,Son,12/09/2010,Male,Son,Active,Y,Y,2,,,,,,,,,John Smith,45.0,43.0,14.0,,,,,,,Full-Time,,Manufacturing,,N,N,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Desk,Urban,Good,N,Occasional,Medium ($50K–$100K),Low,Moderate,Rare,N,Y,POS,0.8853865,\"Medium income, good health - PPO for balanced cost and flexibility.\",\"['POS', 'HMO', 'PPO']\",\"[0.8853865, 0.06517888, 0.*********]\",\"['Dental', 'Vision', 'Accident', 'STD', 'Employee Assistance']\",1.0,Dental: Essential oral health coverage for all employees.; Vision: Eye care coverage for all employees.; STD: Income protection for Medium ($50K–$100K) earners or good health status.,\"['Employee Assistance', 'Wellness Programs', 'FSA']\",\"[0.5, 0.5, 0.5]\"\r\nEMP002,Secondary,Amanda Johnson,Employee,10/03/1975,Female,456 Pine Avenue,Madison,WI,53703,Not Married,Enrolled,N,N,Emma Johnson,Daughter,25/07/2005,Female,456 Pine Avenue,Madison,WI,53703.0,Single,Enrolled,No,False,Daughter,Enrolled,N,N,,,,,,,,,1,,,,,,,,,Amanda Johnson,49.0,19.0,,,,,,,,Full-Time,,Finance,,N,N,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Desk,Urban,Good,N,None,Low (<$50K),Medium,Moderate,Rare,N,N,HMO,0.9047229,Middle age with dependents - PPO for family coverage.,\"['HMO', 'POS', 'PPO']\",\"[0.9047229, 0.053544257, 0.0354696]\",\"['Dental', 'Vision']\",1.0,Dental: Essential oral health coverage for all employees.; Vision: Eye care coverage for all employees.,\"['Employee Assistance', 'Wellness Programs', 'FSA']\",\"[0.5, 0.5, 0.5]\"\r\nEMP003,Secondary,Christopher Brown,Self,30/06/1990,Male,789 Maple Road,Austin,TX,73301,Married,Y,Y,Y,Jennifer Brown,Spouse,15/08/1992,Female,789 Maple Road,Austin,TX,73301.0,Married,Y,Y,Y,Spouse,Y,Y,Y,Ethan Brown,Child,01/12/2015,Male,Child,Y,Y,Y,3,Olivia Brown,Child,22/03/2018,Female,Child,Y,Y,Y,Christopher Brown,35.0,32.0,10.0,7.0,,,,,,Full-Time,,Engineering,,N,N,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Desk,Urban,Fair,N,None,High (>$100K),High,Active,Rare,Y,N,HDHP + HSA,0.5086255,Middle age with dependents - PPO for family coverage.,\"['HDHP + HSA', 'Indemnity', 'PPO']\",\"[0.5086255, 0.3922061, 0.*********]\",\"['Accident', 'STD', 'FSA']\",1.0,Accident: Recommended for active lifestyle and rare travel frequency.; STD: Income protection for High (>$100K) earners or fair health status.; FSA: Tax-advantaged account for medical expenses due to health conditions or pregnancy.,\"['Employee Assistance', 'Wellness Programs', 'FSA']\",\"[0.5, 0.5, 0.5]\"\r\nEMP004,Secondary,Michelle Davis,Employee,05/11/1985,Female,321 Elm Drive,Portland,OR,97201,Not Married,On,On,Off,Lucas Davis,Son,18/04/2012,Male,321 Elm Drive,Portland,OR,97201.0,Divorced,On,On,Off,Son,On,On,Off,,,,,,,,,1,,,,,,,,,Michelle Davis,40.0,13.0,,,,,,,,Contract,,Finance,,N,N,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Desk,Urban,Excellent,N,None,High (>$100K),Medium,Moderate,Rare,Y,N,HDHP + HSA,0.8084086,\"High income, good health, HSA familiar, no chronic conditions - HDHP + HSA for tax benefits.\",\"['HDHP + HSA', 'POS', 'PPO']\",\"[0.8084086, 0.********, 0.********]\",\"['Dental', 'Vision', 'Term Life', 'LTD', 'Wellness Programs']\",1.0,Dental: Essential oral health coverage for all employees.; Vision: Eye care coverage for all employees.; Term Life: Essential protection for 1 dependents.; LTD: Long-term income protection for High (>$100K) earners or age 40+.,\"['Employee Assistance', 'Wellness Programs', 'FSA']\",\"[0.5, 0.5, 0.5]\"\r\nEMP005,Secondary,Robert Miller,Self,28/02/1978,Male,654 Cedar Lane,Denver,CO,80201,Married,Enrolled,Active,Enrolled,Patricia Miller,Spouse,12/11/1980,Female,654 Cedar Lane,Denver,CO,80201.0,Married,Enrolled,Active,Enrolled,Spouse,Enrolled,Active,Enrolled,Timothy Miller,Child,15/06/2008,Male,Child,Enrolled,Active,Enrolled,3,Samantha Miller,Child,03/09/2011,Female,Child,Enrolled,Active,Enrolled,Robert Miller,47.0,44.0,17.0,14.0,,,,,,Full-Time,,Engineering,,N,N,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Desk,Suburban,Fair,N,None,Medium ($50K–$100K),Low,Active,Occasional,N,N,PPO,0.7808568,Middle age with dependents - PPO for family coverage.,\"['PPO', 'POS', 'HMO']\",\"[0.7808568, 0.15637265, 0.03912832]\",\"['Accident', 'STD']\",1.0,Accident: Recommended for active lifestyle and occasional travel frequency.; STD: Income protection for Medium ($50K–$100K) earners or fair health status.,\"['Employee Assistance', 'Wellness Programs', 'FSA']\",\"[0.5, 0.5, 0.5]\"\r\nEMP006,Primary,Jessica Wilson,Employee,12/07/1983,Female,987 Birch Court,Phoenix,AZ,85001,Not Married,Declined,Waived,Declined,,,,,,,,,,,,,,,,,,,,,,,,,0,,,,,,,,,Jessica Wilson,41.0,,,,,,,,,Full-Time,,Engineering,,N,N,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Desk,Urban,Excellent,N,None,Medium ($50K–$100K),Medium,Moderate,Occasional,N,N,POS,0.6196561,\"Medium income, urban area - EPO for network-based care.\",\"['POS', 'HMO', 'PPO']\",\"[0.6196561, 0.18304129, 0.12304185]\",\"['Accident', 'STD']\",1.0,STD: Income protection for Medium ($50K–$100K) earners or excellent health status.,\"['Employee Assistance', 'Wellness Programs', 'FSA']\",\"[0.5, 0.5, 0.5]\"\r\nEMP007,Secondary,Daniel Moore,Self,03/12/1987,Male,147 Spruce Way,Seattle,WA,98101,Married,Y,Y,Y,Rebecca Moore,Wife,20/04/1985,Female,147 Spruce Way,Seattle,WA,98101.0,Married,Yes,Yes,Yes,Wife,Y,Y,Y,,,,,,,,,1,,,,,,,,,Daniel Moore,38.0,40.0,,,,,,,,Contract,,Manufacturing,,N,N,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Desk,Urban,Excellent,N,None,Medium ($50K–$100K),Medium,Active,Rare,N,Y,POS,0.705592,\"Medium income, urban area - EPO for network-based care.\",\"['POS', 'HDHP', 'HMO']\",\"[0.705592, 0.13772556, 0.07063187]\",\"['Dental', 'Vision', 'Accident', 'STD', 'Employee Assistance']\",1.0,Dental: Essential oral health coverage for all employees.; Vision: Eye care coverage for all employees.; Accident: Recommended for active lifestyle and rare travel frequency.; STD: Income protection for Medium ($50K–$100K) earners or excellent health status.,\"['Employee Assistance', 'Wellness Programs', 'FSA']\",\"[0.5, 0.5, 0.5]\"\r\nEMP008,Secondary,Lisa Taylor,Employee,17/10/1979,Female,258 Willow Street,Miami,FL,33101,Not Married,Active,Active,Active,Alexander Taylor,Son,25/12/2007,Male,258 Willow Street,Miami,FL,33101.0,Divorced,Active,Active,Active,Son,Active,Active,Active,Sophia Taylor,Daughter,14/03/2010,Female,Daughter,Active,Active,Active,2,,,,,,,,,Lisa Taylor,45.0,17.0,15.0,,,,,,,Full-Time,,Manufacturing,,N,N,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Desk,Urban,Good,N,None,High (>$100K),Low,Active,Rare,Y,N,HDHP + HSA,0.79628164,\"High income, good health, HSA familiar, no chronic conditions - HDHP + HSA for tax benefits.\",\"['HDHP + HSA', 'POS', 'PPO']\",\"[0.79628164, 0.16281657, 0.*********]\",\"['Dental', 'Vision', 'Accident', 'Term Life', 'STD', 'LTD', 'Wellness Programs']\",1.0,Dental: Essential oral health coverage for all employees.; Vision: Eye care coverage for all employees.; Accident: Recommended for active lifestyle and rare travel frequency.; Term Life: Essential protection for 2 dependents.; STD: Income protection for High (>$100K) earners or good health status.; LTD: Long-term income protection for High (>$100K) earners or age 45+.,\"['Employee Assistance', 'Wellness Programs', 'FSA']\",\"[0.5, 0.5, 0.5]\"\r\nEMP009,Secondary,Kevin Anderson,Self,24/03/1981,Male,369 Poplar Avenue,Boston,MA,2101,Married,Y,Y,Y,Maria Anderson,Spouse,08/07/1979,Female,369 Poplar Avenue,Boston,MA,2101.0,Married,Y,Y,Y,Spouse,Y,Y,Y,Isabella Anderson,Child,30/11/2009,Female,Child,Y,Y,Y,2,,,,,,,,,Kevin Anderson,44.0,45.0,15.0,,,,,,,Contract,,Information Technology,,N,N,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Desk,Urban,Good,N,None,Low (<$50K),Low,Moderate,Occasional,Y,N,POS,0.5364621,Middle age with dependents - PPO for family coverage.,\"['POS', 'HMO', 'HDHP + HSA']\",\"[0.5364621, 0.3285278, 0.07181457]\",\"['Dental', 'Vision']\",1.0,Dental: Essential oral health coverage for all employees.; Vision: Eye care coverage for all employees.,\"['Employee Assistance', 'Wellness Programs', 'FSA']\",\"[0.5, 0.5, 0.5]\"\r\nEMP010,Primary,Rachel Thomas,Employee,09/09/1986,Female,741 Ash Road,Atlanta,GA,30301,Not Married,N,N,N,,,,,,,,,,,,,,,,,,,,,,,,,0,,,,,,,,,Rachel Thomas,38.0,,,,,,,,,Full-Time,,Finance,,N,N,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Desk,Urban,Good,N,Occasional,Medium ($50K–$100K),Low,Moderate,Rare,N,N,POS,0.525606,\"Medium income, good health - PPO for balanced cost and flexibility.\",\"['POS', 'HDHP', 'HMO']\",\"[0.525606, 0.18719657, 0.18236935]\",[],0.0,ML model prediction,\"['Employee Assistance', 'Wellness Programs', 'FSA']\",\"[0.5, 0.5, 0.5]\"\r\nEMP011,Secondary,Steven Jackson,Self,18/12/1984,Male,852 Hickory Drive,Nashville,TN,37201,Married,Enrolled,Enrolled,Enrolled,Catherine Jackson,Spouse,14/02/1982,Female,852 Hickory Drive,Nashville,TN,37201.0,Married,Enrolled,Enrolled,Enrolled,Spouse,Enrolled,Enrolled,Enrolled,,,,,,,,,1,,,,,,,,,Steven Jackson,40.0,43.0,,,,,,,,Full-Time,,Manufacturing,,N,N,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Desk,Rural,Good,N,None,High (>$100K),Low,Moderate,Rare,Y,N,HDHP + HSA,0.7243671,\"High income, good health, HSA familiar, no chronic conditions - HDHP + HSA for tax benefits.\",\"['HDHP + HSA', 'POS', 'PPO']\",\"[0.7243671, 0.23427948, 0.02497729]\",\"['Dental', 'Vision', 'Accident', 'Term Life', 'STD', 'LTD', 'Wellness Programs']\",1.0,Dental: Essential oral health coverage for all employees.; Vision: Eye care coverage for all employees.; Term Life: Essential protection for 1 dependents.; STD: Income protection for High (>$100K) earners or good health status.; LTD: Long-term income protection for High (>$100K) earners or age 40+.,\"['Employee Assistance', 'Wellness Programs', 'FSA']\",\"[0.5, 0.5, 0.5]\"\r\nEMP012,Secondary,Nicole White,Employee,07/04/1977,Female,963 Dogwood Lane,Charlotte,NC,28201,Not Married,Y,Y,Y,Madison White,Daughter,22/05/2006,Female,963 Dogwood Lane,Charlotte,NC,28201.0,Divorced,Yes,Yes,Yes,Daughter,Y,Y,Y,Jacob White,Son,11/10/2009,Male,Son,Y,Y,Y,2,,,,,,,,,Nicole White,48.0,19.0,15.0,,,,,,,Full-Time,,Engineering,,N,N,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Desk,Rural,Fair,N,None,Low (<$50K),Low,Moderate,Occasional,N,N,PPO,0.52179396,Middle age with dependents - PPO for family coverage.,\"['PPO', 'HMO', 'POS']\",\"[0.52179396, 0.3922573, 0.07292019]\",\"['Dental', 'Vision']\",0.5,Default benefits assignment due to prediction error,\"['Dental', 'Vision', 'Hospital Indemnity']\",\"[0.5, 0.5, 0.5]\"\r\nEMP013,Primary,Brian Harris,Employee,21/08/1989,Male,159 Fir Court,Orlando,FL,32801,Not Married,Declined,Declined,Declined,,,,,,,,,,,,,,,,,,,,,,,,,0,,,,,,,,,Brian Harris,35.0,,,,,,,,,Part-Time,,Sales,,N,N,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Desk,Urban,Excellent,N,Occasional,Medium ($50K–$100K),Medium,Active,Frequent,Y,N,HDHP,0.69349563,\"Medium income, urban area - EPO for network-based care.\",\"['HDHP', 'POS', 'HDHP + HSA']\",\"[0.69349563, 0.20090692, 0.060490277]\",\"['Dental', 'Vision']\",0.5,Default benefits assignment due to prediction error,\"['Dental', 'Vision', 'Hospital Indemnity']\",\"[0.5, 0.5, 0.5]\"\r\nEMP014,Secondary,Ashley Martin,Self,26/01/1982,Female,357 Gum Street,Las Vegas,NV,89101,Married,Active,Active,Active,Matthew Martin,Husband,18/06/1984,Male,357 Gum Street,Las Vegas,NV,89101.0,Married,Active,Active,Active,Husband,Active,Active,Active,Chloe Martin,Daughter,05/01/2012,Female,Daughter,Active,Active,Active,3,Aiden Martin,Son,12/07/2014,Male,Son,Active,Active,Active,Ashley Martin,43.0,41.0,13.0,10.0,,,,,,Full-Time,,Information Technology,,N,N,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Desk,Urban,Good,Y,Occasional,Medium ($50K–$100K),Low,Moderate,Rare,N,N,PPO,0.6652446,\"Medium income, good health - PPO for balanced cost and flexibility.\",\"['PPO', 'POS', 'HMO']\",\"[0.6652446, 0.17657638, 0.13048217]\",\"['Dental', 'Vision']\",0.5,Default benefits assignment due to prediction error,\"['Dental', 'Vision', 'Hospital Indemnity']\",\"[0.5, 0.5, 0.5]\"\r\nEMP015,Secondary,Joshua Thompson,Employee,13/05/1986,Male,468 Juniper Way,Salt Lake City,UT,84101,Not Married,On,On,On,Logan Thompson,Son,28/09/2011,Male,468 Juniper Way,Salt Lake City,UT,84101.0,Single,On,On,On,Son,On,On,On,,,,,,,,,1,,,,,,,,,Joshua Thompson,39.0,13.0,,,,,,,,Full-Time,,Engineering,,N,N,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Desk,Suburban,Excellent,N,None,Medium ($50K–$100K),Medium,Moderate,Occasional,Y,N,HDHP + HSA,0.70330554,Middle age with dependents - PPO for family coverage.,\"['HDHP + HSA', 'HDHP', 'POS']\",\"[0.70330554, 0.22117762, 0.055065375]\",\"['Dental', 'Vision']\",0.5,Default benefits assignment due to prediction error,\"['Dental', 'Vision', 'Hospital Indemnity']\",\"[0.5, 0.5, 0.5]\"\r\n", "comprehensive_statistics": {"basic_demographics": {"age_statistics": {"average_age": 41.8, "median_age": 41.0, "age_range": {"min": 35, "max": 49}, "age_distribution": {"18-30": 0, "31-45": 12, "46-60": 3, "60+": 0}}, "gender_composition": {"counts": {"Male": 8, "Female": 7}, "percentages": {"Male": 53.33, "Female": 46.67}}, "marital_status_distribution": {"counts": {"Not Married": 8, "Married": 7}, "percentages": {"Not Married": 53.33, "Married": 46.67}}}, "dependent_analysis": {"dependent_count_distribution": {"average_dependents": 1.4666666666666666, "median_dependents": 1.0, "distribution": {"1": 5, "2": 4, "3": 3, "0": 3}, "employees_with_dependents": 12, "percentage_with_dependents": 80.0}, "dependent_age_analysis": {"total_dependents_found": 22, "average_dependent_age": 22.68, "median_dependent_age": 16.0, "dependents_over_26_as_children": 0, "age_distribution": {"0-5": 0, "6-12": 3, "13-18": 10, "19-26": 2, "26+": 7}}}, "employment_demographics": {"department_distribution": {"counts": {"Engineering": 5, "Manufacturing": 4, "Finance": 3, "Information Technology": 2, "Sales": 1}, "percentages": {"Engineering": 33.33, "Manufacturing": 26.67, "Finance": 20.0, "Information Technology": 13.33, "Sales": 6.67}}, "employment_type_distribution": {"counts": {"Full-Time": 11, "Contract": 3, "Part-Time": 1}, "percentages": {"Full-Time": 73.33, "Contract": 20.0, "Part-Time": 6.67}}, "job_type_distribution": {"counts": {"Desk": 15}, "percentages": {"Desk": 100.0}}, "employee_class_distribution": {"counts": {}, "percentages": {}}}, "health_and_lifestyle": {"health_condition_distribution": {"counts": {"Good": 7, "Excellent": 5, "Fair": 3}, "percentages": {"Good": 46.67, "Excellent": 33.33, "Fair": 20.0}}, "chronic_condition_distribution": {"counts": {"N": 14, "Y": 1}, "percentages": {"N": 93.33, "Y": 6.67}}, "tobacco_use_distribution": {"counts": {"N": 15}, "percentages": {"N": 100.0}}, "lifestyle_distribution": {"counts": {"Moderate": 10, "Active": 5}, "percentages": {"Moderate": 66.67, "Active": 33.33}}, "prescription_use_distribution": {"counts": {"None": 11, "Occasional": 4}, "percentages": {"None": 73.33, "Occasional": 26.67}}}, "coverage_analysis": {"coverage_tier_distribution": {"counts": {}, "percentages": {}}, "coverage_tier_by_family_size": {}, "medical_plan_distribution": {"counts": {"Y": 4, "Active": 3, "Enrolled": 3, "On": 2, "Declined": 2, "N": 1}, "percentages": {"Y": 26.67, "Active": 20.0, "Enrolled": 20.0, "On": 13.33, "Declined": 13.33, "N": 6.67}}, "dental_plan_distribution": {"counts": {"Y": 5, "Active": 3, "N": 2, "On": 2, "Waived": 1, "Enrolled": 1, "Declined": 1}, "percentages": {"Y": 33.33, "Active": 20.0, "N": 13.33, "On": 13.33, "Waived": 6.67, "Enrolled": 6.67, "Declined": 6.67}}, "vision_plan_distribution": {"counts": {"Y": 5, "N": 2, "Enrolled": 2, "Declined": 2, "Active": 2, "Off": 1, "On": 1}, "percentages": {"Y": 33.33, "N": 13.33, "Enrolled": 13.33, "Declined": 13.33, "Active": 13.33, "Off": 6.67, "On": 6.67}}, "life_plan_distribution": {"counts": {}, "percentages": {}}}, "geographic_distribution": {"state_distribution": {"counts": {"FL": 2, "IL": 1, "WI": 1, "TX": 1, "OR": 1, "CO": 1, "AZ": 1, "WA": 1, "MA": 1, "GA": 1, "TN": 1, "NC": 1, "NV": 1, "UT": 1}, "percentages": {"FL": 13.33, "IL": 6.67, "WI": 6.67, "TX": 6.67, "OR": 6.67, "CO": 6.67, "AZ": 6.67, "WA": 6.67, "MA": 6.67, "GA": 6.67, "TN": 6.67, "NC": 6.67, "NV": 6.67, "UT": 6.67}}, "region_distribution": {"counts": {"Urban": 11, "Suburban": 2, "Rural": 2}, "percentages": {"Urban": 73.33, "Suburban": 13.33, "Rural": 13.33}}, "top_cities": {"counts": {"Springfield": 1, "Madison": 1, "Austin": 1, "Portland": 1, "Denver": 1, "Phoenix": 1, "Seattle": 1, "Miami": 1, "Boston": 1, "Atlanta": 1}, "percentages": {"Springfield": 6.67, "Madison": 6.67, "Austin": 6.67, "Portland": 6.67, "Denver": 6.67, "Phoenix": 6.67, "Seattle": 6.67, "Miami": 6.67, "Boston": 6.67, "Atlanta": 6.67}}}, "financial_demographics": {"income_tier_distribution": {"counts": {"Medium ($50K–$100K)": 8, "High (>$100K)": 4, "Low (<$50K)": 3}, "percentages": {"Medium ($50K–$100K)": 53.33, "High (>$100K)": 26.67, "Low (<$50K)": 20.0}}, "risk_tolerance_distribution": {"counts": {"Low": 8, "Medium": 6, "High": 1}, "percentages": {"Low": 53.33, "Medium": 40.0, "High": 6.67}}, "hsa_familiarity_distribution": {"counts": {"N": 8, "Y": 7}, "percentages": {"N": 53.33, "Y": 46.67}}}, "risk_assessment": {"group_risk_score": 22.0, "group_risk_level": "Low", "risk_distribution": {"low_risk": 12, "medium_risk": 3, "high_risk": 0}, "risk_statistics": {"min_risk_score": 10, "max_risk_score": 43, "median_risk_score": 23.0, "std_risk_score": 8.41}, "top_risk_factors": {"Poor Health Condition": 3, "Chronic Conditions": 1}, "individual_risks": [{"employee_id": "EMP001", "risk_score": 23, "risk_level": "Low", "risk_factors": []}, {"employee_id": "EMP002", "risk_score": 23, "risk_level": "Low", "risk_factors": []}, {"employee_id": "EMP003", "risk_score": 25, "risk_level": "Low", "risk_factors": ["Health condition: Fair"]}, {"employee_id": "EMP004", "risk_score": 18, "risk_level": "Low", "risk_factors": []}, {"employee_id": "EMP005", "risk_score": 30, "risk_level": "Medium", "risk_factors": ["Health condition: Fair"]}, {"employee_id": "EMP006", "risk_score": 18, "risk_level": "Low", "risk_factors": []}, {"employee_id": "EMP007", "risk_score": 10, "risk_level": "Low", "risk_factors": []}, {"employee_id": "EMP008", "risk_score": 20, "risk_level": "Low", "risk_factors": []}, {"employee_id": "EMP009", "risk_score": 23, "risk_level": "Low", "risk_factors": []}, {"employee_id": "EMP010", "risk_score": 18, "risk_level": "Low", "risk_factors": []}]}, "data_quality_metrics": {"overall_completeness": {"total_cells": 2490, "missing_cells": "1716", "completeness_percentage": 31.08}, "column_completeness": {"employee_id": {"missing_count": "0", "completeness_percentage": 100.0}, "record_type": {"missing_count": "0", "completeness_percentage": 100.0}, "full_name": {"missing_count": "0", "completeness_percentage": 100.0}, "relationship": {"missing_count": "0", "completeness_percentage": 100.0}, "dob": {"missing_count": "0", "completeness_percentage": 100.0}, "gender": {"missing_count": "0", "completeness_percentage": 100.0}, "address1": {"missing_count": "0", "completeness_percentage": 100.0}, "city": {"missing_count": "0", "completeness_percentage": 100.0}, "state": {"missing_count": "0", "completeness_percentage": 100.0}, "zipcode": {"missing_count": "0", "completeness_percentage": 100.0}, "marital_status": {"missing_count": "0", "completeness_percentage": 100.0}, "medical_plan": {"missing_count": "0", "completeness_percentage": 100.0}, "dental_plan": {"missing_count": "0", "completeness_percentage": 100.0}, "vision_plan": {"missing_count": "0", "completeness_percentage": 100.0}, "dept_1_full_name": {"missing_count": "3", "completeness_percentage": 80.0}, "dept_1_relationship_type": {"missing_count": "3", "completeness_percentage": 80.0}, "dept_1_dob": {"missing_count": "3", "completeness_percentage": 80.0}, "dept_1_gender": {"missing_count": "3", "completeness_percentage": 80.0}, "employee_address1": {"missing_count": "3", "completeness_percentage": 80.0}, "employee_city": {"missing_count": "3", "completeness_percentage": 80.0}, "employee_state": {"missing_count": "3", "completeness_percentage": 80.0}, "employee_zipcode": {"missing_count": "3", "completeness_percentage": 80.0}, "employee_marital_status": {"missing_count": "3", "completeness_percentage": 80.0}, "employee_medical_plan_enrollment": {"missing_count": "3", "completeness_percentage": 80.0}, "employee_dental_plan_enrollment": {"missing_count": "3", "completeness_percentage": 80.0}, "employee_vision_plan_enrollment": {"missing_count": "3", "completeness_percentage": 80.0}, "relationship_type_1": {"missing_count": "3", "completeness_percentage": 80.0}, "dept_1_medical_plan": {"missing_count": "3", "completeness_percentage": 80.0}, "dept_1_dental_plan": {"missing_count": "3", "completeness_percentage": 80.0}, "dept_1_vision_plan": {"missing_count": "3", "completeness_percentage": 80.0}, "dept_2_full_name": {"missing_count": "8", "completeness_percentage": 46.67}, "dept_2_relationship_type": {"missing_count": "8", "completeness_percentage": 46.67}, "dept_2_dob": {"missing_count": "8", "completeness_percentage": 46.67}, "dept_2_gender": {"missing_count": "8", "completeness_percentage": 46.67}, "relationship_type_2": {"missing_count": "8", "completeness_percentage": 46.67}, "dept_2_medical_plan": {"missing_count": "8", "completeness_percentage": 46.67}, "dept_2_dental_plan": {"missing_count": "8", "completeness_percentage": 46.67}, "dept_2_vision_plan": {"missing_count": "8", "completeness_percentage": 46.67}, "dept_count": {"missing_count": "0", "completeness_percentage": 100.0}, "dept_3_full_name": {"missing_count": "12", "completeness_percentage": 20.0}, "dept_3_relationship_type": {"missing_count": "12", "completeness_percentage": 20.0}, "dept_3_dob": {"missing_count": "12", "completeness_percentage": 20.0}, "dept_3_gender": {"missing_count": "12", "completeness_percentage": 20.0}, "relationship_type_3": {"missing_count": "12", "completeness_percentage": 20.0}, "dept_3_medical_plan": {"missing_count": "12", "completeness_percentage": 20.0}, "dept_3_dental_plan": {"missing_count": "12", "completeness_percentage": 20.0}, "dept_3_vision_plan": {"missing_count": "12", "completeness_percentage": 20.0}, "name": {"missing_count": "0", "completeness_percentage": 100.0}, "age": {"missing_count": "0", "completeness_percentage": 100.0}, "dept_1_age": {"missing_count": "3", "completeness_percentage": 80.0}, "dept_2_age": {"missing_count": "8", "completeness_percentage": 46.67}, "dept_3_age": {"missing_count": "12", "completeness_percentage": 20.0}, "first_name": {"missing_count": "15", "completeness_percentage": 0.0}, "middle_name": {"missing_count": "15", "completeness_percentage": 0.0}, "last_name": {"missing_count": "15", "completeness_percentage": 0.0}, "address2": {"missing_count": "15", "completeness_percentage": 0.0}, "salary": {"missing_count": "15", "completeness_percentage": 0.0}, "employment_type": {"missing_count": "0", "completeness_percentage": 100.0}, "employee_class": {"missing_count": "15", "completeness_percentage": 0.0}, "department": {"missing_count": "0", "completeness_percentage": 100.0}, "hire_date": {"missing_count": "15", "completeness_percentage": 0.0}, "tobacco_use": {"missing_count": "0", "completeness_percentage": 100.0}, "pregnancy_status": {"missing_count": "0", "completeness_percentage": 100.0}, "life_plan": {"missing_count": "15", "completeness_percentage": 0.0}, "add_plan": {"missing_count": "15", "completeness_percentage": 0.0}, "coverage_tier": {"missing_count": "15", "completeness_percentage": 0.0}, "ssn": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_1": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_2": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_3": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_4": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_4_dob": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_4_age": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_4_gender": {"missing_count": "15", "completeness_percentage": 0.0}, "relationship_type_4": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_5": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_5_dob": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_5_age": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_5_gender": {"missing_count": "15", "completeness_percentage": 0.0}, "relationship_type_5": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_6": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_6_dob": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_6_age": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_6_gender": {"missing_count": "15", "completeness_percentage": 0.0}, "relationship_type_6": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_7": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_7_dob": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_7_age": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_7_gender": {"missing_count": "15", "completeness_percentage": 0.0}, "relationship_type_7": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_8": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_8_dob": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_8_age": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_8_gender": {"missing_count": "15", "completeness_percentage": 0.0}, "relationship_type_8": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_9": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_9_dob": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_9_age": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_9_gender": {"missing_count": "15", "completeness_percentage": 0.0}, "relationship_type_9": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_10": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_10_dob": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_10_age": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_10_gender": {"missing_count": "15", "completeness_percentage": 0.0}, "relationship_type_10": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_11": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_11_dob": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_11_age": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_11_gender": {"missing_count": "15", "completeness_percentage": 0.0}, "relationship_type_11": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_12": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_12_dob": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_12_age": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_12_gender": {"missing_count": "15", "completeness_percentage": 0.0}, "relationship_type_12": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_13": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_13_dob": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_13_age": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_13_gender": {"missing_count": "15", "completeness_percentage": 0.0}, "relationship_type_13": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_14": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_14_dob": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_14_age": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_14_gender": {"missing_count": "15", "completeness_percentage": 0.0}, "relationship_type_14": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_15": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_15_dob": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_15_age": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_15_gender": {"missing_count": "15", "completeness_percentage": 0.0}, "relationship_type_15": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_16": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_16_dob": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_16_age": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_16_gender": {"missing_count": "15", "completeness_percentage": 0.0}, "relationship_type_16": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_17": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_17_dob": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_17_age": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_17_gender": {"missing_count": "15", "completeness_percentage": 0.0}, "relationship_type_17": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_18": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_18_dob": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_18_age": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_18_gender": {"missing_count": "15", "completeness_percentage": 0.0}, "relationship_type_18": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_19": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_19_dob": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_19_age": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_19_gender": {"missing_count": "15", "completeness_percentage": 0.0}, "relationship_type_19": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_20": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_20_dob": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_20_age": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_20_gender": {"missing_count": "15", "completeness_percentage": 0.0}, "relationship_type_20": {"missing_count": "15", "completeness_percentage": 0.0}, "job_type": {"missing_count": "0", "completeness_percentage": 100.0}, "region": {"missing_count": "0", "completeness_percentage": 100.0}, "health_condition": {"missing_count": "0", "completeness_percentage": 100.0}, "chronic_condition": {"missing_count": "0", "completeness_percentage": 100.0}, "prescription_use": {"missing_count": "0", "completeness_percentage": 100.0}, "income_tier": {"missing_count": "0", "completeness_percentage": 100.0}, "risk_tolerance": {"missing_count": "0", "completeness_percentage": 100.0}, "lifestyle": {"missing_count": "0", "completeness_percentage": 100.0}, "travel_frequency": {"missing_count": "0", "completeness_percentage": 100.0}, "hsa_familiarity": {"missing_count": "0", "completeness_percentage": 100.0}, "mental_health_needs": {"missing_count": "0", "completeness_percentage": 100.0}}, "critical_field_completeness": {"name": {"missing_count": "0", "completeness_percentage": 100.0}, "gender": {"missing_count": "0", "completeness_percentage": 100.0}, "zipcode": {"missing_count": "0", "completeness_percentage": 100.0}, "marital_status": {"missing_count": "0", "completeness_percentage": 100.0}}}}, "prediction_summary": {"total_employees": 15, "plan_type_distribution": {"successful_predictions": {"POS": 5, "HDHP + HSA": 5, "PPO": 3, "HMO": 1, "HDHP": 1}, "failed_predictions": "0", "success_rate": 100.0, "total_predicted": "15"}, "benefits_distribution": {}, "confidence_metrics": {"plan_confidence": {"mean": "0.692", "min": "0.509", "max": "0.905", "count": 15}}, "prediction_success_rates": {}, "top_3_plan_probabilities": []}, "total_employees": 15, "total_columns": 176, "has_predictions": true, "prediction_columns": ["predicted_plan_type", "plan_confidence"]}, "metadata": {"file_processing": {"filename": "row3_mixed_relationships.csv", "size": 4193, "original_column_names": ["Employee_ID", "Member_Type", "Full_Name", "Relationship_Type", "Date_Of_Birth", "Sex", "Home_Address", "Town", "Province", "Postal_Code", "Marital_Status", "Health_Coverage", "Dental_Coverage", "Vision_Coverage"]}, "pattern_analysis": {"pattern_type": "row_based_member_level", "pattern_confidence": 1.4000000000000001, "pattern_reason": "Found 'relationship' field; Found both employee and dependent relationship values; Found 'record_type' field; Found duplicate employee_ids (family grouping)", "analysis_details": {"confidence": 1.4000000000000001, "reason": "Found 'relationship' field; Found both employee and dependent relationship values; Found 'record_type' field; Found duplicate employee_ids (family grouping)"}}, "field_mapping": {"total_fields_mapped": 14, "mapping_success": true, "unmapped_fields": [], "mapping_confidence": 0, "field_mapping_details": {"Employee_ID": "employee_id", "Member_Type": "record_type", "Full_Name": "full_name", "Relationship_Type": "relationship", "Date_Of_Birth": "dob", "Sex": "gender", "Home_Address": "address1", "Town": "city", "Province": "state", "Postal_Code": "zipcode", "Marital_Status": "marital_status", "Health_Coverage": "medical_plan", "Dental_Coverage": "dental_plan", "Vision_Coverage": "vision_plan"}}, "data_processing": {"validation_passed": false, "processing_summary": {"original_rows": 37, "original_columns": 14, "processed_rows": 15, "processed_columns": 155, "processing_time_seconds": 17.31, "validation_passed": true, "validation_errors": 0, "error_rows": 0, "fields_processed": {"name_fields": true, "age_converted": true, "gender_standardized": true, "addresses_cleaned": true, "zipcode_validated": true}, "data_quality": {"complete_records": 15, "missing_data_rows": 0}}, "data_quality_score": 0.562, "processing_details": {"success": true, "preprocessed_data": "   employee_id record_type          full_name relationship         dob  gender  ... relationship_type_19 dept_20 dept_20_dob  dept_20_age dept_20_gender relationship_type_20\n0       EMP001   Secondary         <PERSON>     Employee  15/01/1980    Male  ...                 None    None        None         None           None                 None\n1       EMP002   Secondary     <PERSON>     Employee  10/03/1975  Female  ...                 None    None        None         None           None                 None\n2       EMP003   Secondary  <PERSON>         Self  30/06/1990    Male  ...                 None    None        None         None           None                 None\n3       EMP004   Secondary     <PERSON>ployee  05/11/1985  Female  ...                 None    None        None         None           None                 None\n4       EMP005   Secondary      <PERSON>         Self  28/02/1978    Male  ...                 None    None        None         None           None                 None\n5       EMP006     Primary     <PERSON>     Employee  12/07/1983  Female  ...                 None    None        None         None           None                 None\n6       EMP007   Secondary       <PERSON>         Self  03/12/1987    Male  ...                 None    None        None         None           None                 None\n7       EMP008   Secondary        <PERSON>loy<PERSON>  17/10/1979  Female  ...                 None    None        None         None           None                 None\n8       EMP009   Secondary     <PERSON>         Self  24/03/1981    Male  ...                 None    None        None         None           None                 None\n9       EMP010     Primary      <PERSON>     Em<PERSON>loyee  09/09/1986  Female  ...                 None    None        None         None           None                 None\n10      EMP011   Secondary     <PERSON>         Self  18/12/1984    Male  ...                 None    None        None         None           None                 None\n11      EMP012   Secondary       <PERSON>     Employee  07/04/1977  Female  ...                 None    None        None         None           None                 None\n12      EMP013     Primary       <PERSON>     Employee  21/08/1989    Male  ...                 None    None        None         None           None                 None\n13      EMP014   Secondary      <PERSON> <PERSON>         Self  26/01/1982  Female  ...                 None    None        None         None           None                 None\n14      EMP015   Secondary    Joshua <PERSON>     Employee  13/05/1986    Male  ...                 None    None        None         None           None                 None\n\n[15 rows x 155 columns]", "validation": {"is_valid": true, "errors": [], "error_rows": [], "total_errors": 0}, "summary": {"original_rows": 37, "original_columns": 14, "processed_rows": 15, "processed_columns": 155, "processing_time_seconds": 17.31, "validation_passed": true, "validation_errors": 0, "error_rows": 0, "fields_processed": {"name_fields": true, "age_converted": true, "gender_standardized": true, "addresses_cleaned": true, "zipcode_validated": true}, "data_quality": {"complete_records": 15, "missing_data_rows": 0}}}}, "enrichment_and_prediction": {"success": true, "enriched_data": "   employee_id record_type          full_name  ...                                    benefits_reason                                 top_3_benefits top_3_benefits_confidences\n0       EMP001   Secondary         John Smith  ...  Dental: Essential oral health coverage for all...  [Employee Assistance, Wellness Programs, FSA]            [0.5, 0.5, 0.5]\n1       EMP002   Secondary     Amanda Johnson  ...  Dental: Essential oral health coverage for all...  [Employee Assistance, Wellness Programs, FSA]            [0.5, 0.5, 0.5]\n2       EMP003   Secondary  <PERSON>  ...  Accident: Recommended for active lifestyle and...  [Employee Assistance, Wellness Programs, FSA]            [0.5, 0.5, 0.5]\n3       EMP004   Secondary     Michelle Davis  ...  Dental: Essential oral health coverage for all...  [Employee Assistance, Wellness Programs, FSA]            [0.5, 0.5, 0.5]\n4       EMP005   Secondary      Robert Miller  ...  Accident: Recommended for active lifestyle and...  [Employee Assistance, Wellness Programs, FSA]            [0.5, 0.5, 0.5]\n5       EMP006     Primary     Jessica <PERSON>  ...  STD: Income protection for Medium ($50K–$100K)...  [Employee Assistance, Wellness Programs, FSA]            [0.5, 0.5, 0.5]\n6       EMP007   Secondary       Daniel Moore  ...  Dental: Essential oral health coverage for all...  [Employee Assistance, Wellness Programs, FSA]            [0.5, 0.5, 0.5]\n7       EMP008   Secondary        Lisa Taylor  ...  Dental: Essential oral health coverage for all...  [Employee Assistance, Wellness Programs, FSA]            [0.5, 0.5, 0.5]\n8       EMP009   Secondary     Kevin Anderson  ...  Dental: Essential oral health coverage for all...  [Employee Assistance, Wellness Programs, FSA]            [0.5, 0.5, 0.5]\n9       EMP010     Primary      Rachel Thomas  ...                                ML model prediction  [Employee Assistance, Wellness Programs, FSA]            [0.5, 0.5, 0.5]\n10      EMP011   Secondary     Steven Jackson  ...  Dental: Essential oral health coverage for all...  [Employee Assistance, Wellness Programs, FSA]            [0.5, 0.5, 0.5]\n11      EMP012   Secondary       Nicole White  ...  Default benefits assignment due to prediction ...           [Dental, Vision, Hospital Indemnity]            [0.5, 0.5, 0.5]\n12      EMP013     Primary       Brian Harris  ...  Default benefits assignment due to prediction ...           [Dental, Vision, Hospital Indemnity]            [0.5, 0.5, 0.5]\n13      EMP014   Secondary      Ashley Martin  ...  Default benefits assignment due to prediction ...           [Dental, Vision, Hospital Indemnity]            [0.5, 0.5, 0.5]\n14      EMP015   Secondary    Joshua Thompson  ...  Default benefits assignment due to prediction ...           [Dental, Vision, Hospital Indemnity]            [0.5, 0.5, 0.5]\n\n[15 rows x 176 columns]", "enrichment_summary": {"total_employees": 15, "features_analyzed": 19, "enrichment_details": {"age": {"original_missing": "0", "final_missing": "0", "enriched_count": "0", "completion_rate": 100.0}, "gender": {"original_missing": "0", "final_missing": "0", "enriched_count": "0", "completion_rate": 100.0}, "marital_status": {"original_missing": "0", "final_missing": "0", "enriched_count": "0", "completion_rate": 100.0}, "dept_count": {"original_missing": "0", "final_missing": "0", "enriched_count": "0", "completion_rate": 100.0}, "job_type": {"original_missing": 15, "final_missing": "0", "enriched_count": "15", "completion_rate": 100.0}, "employment_type": {"original_missing": "15", "final_missing": "0", "enriched_count": "15", "completion_rate": 100.0}, "department": {"original_missing": "15", "final_missing": "0", "enriched_count": "15", "completion_rate": 100.0}, "region": {"original_missing": 15, "final_missing": "0", "enriched_count": "15", "completion_rate": 100.0}, "health_condition": {"original_missing": 15, "final_missing": "0", "enriched_count": "15", "completion_rate": 100.0}, "chronic_condition": {"original_missing": 15, "final_missing": "0", "enriched_count": "15", "completion_rate": 100.0}, "prescription_use": {"original_missing": 15, "final_missing": "0", "enriched_count": "15", "completion_rate": 100.0}, "income_tier": {"original_missing": 15, "final_missing": "0", "enriched_count": "15", "completion_rate": 100.0}, "risk_tolerance": {"original_missing": 15, "final_missing": "0", "enriched_count": "15", "completion_rate": 100.0}, "lifestyle": {"original_missing": 15, "final_missing": "0", "enriched_count": "15", "completion_rate": 100.0}, "travel_frequency": {"original_missing": 15, "final_missing": "0", "enriched_count": "15", "completion_rate": 100.0}, "hsa_familiarity": {"original_missing": 15, "final_missing": "0", "enriched_count": "15", "completion_rate": 100.0}, "pregnancy_status": {"original_missing": "15", "final_missing": "0", "enriched_count": "15", "completion_rate": 100.0}, "tobacco_use": {"original_missing": "15", "final_missing": "0", "enriched_count": "15", "completion_rate": 100.0}, "mental_health_needs": {"original_missing": 15, "final_missing": "0", "enriched_count": "15", "completion_rate": 100.0}}, "data_quality_improvement": {"total_missing_before": "225", "total_missing_after": "0", "total_enriched": "225", "overall_completion_rate": 100.0}}, "comprehensive_statistics": {"basic_demographics": {"age_statistics": {"average_age": 41.8, "median_age": 41.0, "age_range": {"min": 35, "max": 49}, "age_distribution": {"18-30": 0, "31-45": 12, "46-60": 3, "60+": 0}}, "gender_composition": {"counts": {"Male": 8, "Female": 7}, "percentages": {"Male": 53.33, "Female": 46.67}}, "marital_status_distribution": {"counts": {"Not Married": 8, "Married": 7}, "percentages": {"Not Married": 53.33, "Married": 46.67}}}, "dependent_analysis": {"dependent_count_distribution": {"average_dependents": 1.4666666666666666, "median_dependents": 1.0, "distribution": {"1": 5, "2": 4, "3": 3, "0": 3}, "employees_with_dependents": 12, "percentage_with_dependents": 80.0}, "dependent_age_analysis": {"total_dependents_found": 22, "average_dependent_age": 22.68, "median_dependent_age": 16.0, "dependents_over_26_as_children": 0, "age_distribution": {"0-5": 0, "6-12": 3, "13-18": 10, "19-26": 2, "26+": 7}}}, "employment_demographics": {"department_distribution": {"counts": {"Engineering": 5, "Manufacturing": 4, "Finance": 3, "Information Technology": 2, "Sales": 1}, "percentages": {"Engineering": 33.33, "Manufacturing": 26.67, "Finance": 20.0, "Information Technology": 13.33, "Sales": 6.67}}, "employment_type_distribution": {"counts": {"Full-Time": 11, "Contract": 3, "Part-Time": 1}, "percentages": {"Full-Time": 73.33, "Contract": 20.0, "Part-Time": 6.67}}, "job_type_distribution": {"counts": {"Desk": 15}, "percentages": {"Desk": 100.0}}, "employee_class_distribution": {"counts": {}, "percentages": {}}}, "health_and_lifestyle": {"health_condition_distribution": {"counts": {"Good": 7, "Excellent": 5, "Fair": 3}, "percentages": {"Good": 46.67, "Excellent": 33.33, "Fair": 20.0}}, "chronic_condition_distribution": {"counts": {"N": 14, "Y": 1}, "percentages": {"N": 93.33, "Y": 6.67}}, "tobacco_use_distribution": {"counts": {"N": 15}, "percentages": {"N": 100.0}}, "lifestyle_distribution": {"counts": {"Moderate": 10, "Active": 5}, "percentages": {"Moderate": 66.67, "Active": 33.33}}, "prescription_use_distribution": {"counts": {"None": 11, "Occasional": 4}, "percentages": {"None": 73.33, "Occasional": 26.67}}}, "coverage_analysis": {"coverage_tier_distribution": {"counts": {}, "percentages": {}}, "coverage_tier_by_family_size": {}, "medical_plan_distribution": {"counts": {"Y": 4, "Active": 3, "Enrolled": 3, "On": 2, "Declined": 2, "N": 1}, "percentages": {"Y": 26.67, "Active": 20.0, "Enrolled": 20.0, "On": 13.33, "Declined": 13.33, "N": 6.67}}, "dental_plan_distribution": {"counts": {"Y": 5, "Active": 3, "N": 2, "On": 2, "Waived": 1, "Enrolled": 1, "Declined": 1}, "percentages": {"Y": 33.33, "Active": 20.0, "N": 13.33, "On": 13.33, "Waived": 6.67, "Enrolled": 6.67, "Declined": 6.67}}, "vision_plan_distribution": {"counts": {"Y": 5, "N": 2, "Enrolled": 2, "Declined": 2, "Active": 2, "Off": 1, "On": 1}, "percentages": {"Y": 33.33, "N": 13.33, "Enrolled": 13.33, "Declined": 13.33, "Active": 13.33, "Off": 6.67, "On": 6.67}}, "life_plan_distribution": {"counts": {}, "percentages": {}}}, "geographic_distribution": {"state_distribution": {"counts": {"FL": 2, "IL": 1, "WI": 1, "TX": 1, "OR": 1, "CO": 1, "AZ": 1, "WA": 1, "MA": 1, "GA": 1, "TN": 1, "NC": 1, "NV": 1, "UT": 1}, "percentages": {"FL": 13.33, "IL": 6.67, "WI": 6.67, "TX": 6.67, "OR": 6.67, "CO": 6.67, "AZ": 6.67, "WA": 6.67, "MA": 6.67, "GA": 6.67, "TN": 6.67, "NC": 6.67, "NV": 6.67, "UT": 6.67}}, "region_distribution": {"counts": {"Urban": 11, "Suburban": 2, "Rural": 2}, "percentages": {"Urban": 73.33, "Suburban": 13.33, "Rural": 13.33}}, "top_cities": {"counts": {"Springfield": 1, "Madison": 1, "Austin": 1, "Portland": 1, "Denver": 1, "Phoenix": 1, "Seattle": 1, "Miami": 1, "Boston": 1, "Atlanta": 1}, "percentages": {"Springfield": 6.67, "Madison": 6.67, "Austin": 6.67, "Portland": 6.67, "Denver": 6.67, "Phoenix": 6.67, "Seattle": 6.67, "Miami": 6.67, "Boston": 6.67, "Atlanta": 6.67}}}, "financial_demographics": {"income_tier_distribution": {"counts": {"Medium ($50K–$100K)": 8, "High (>$100K)": 4, "Low (<$50K)": 3}, "percentages": {"Medium ($50K–$100K)": 53.33, "High (>$100K)": 26.67, "Low (<$50K)": 20.0}}, "risk_tolerance_distribution": {"counts": {"Low": 8, "Medium": 6, "High": 1}, "percentages": {"Low": 53.33, "Medium": 40.0, "High": 6.67}}, "hsa_familiarity_distribution": {"counts": {"N": 8, "Y": 7}, "percentages": {"N": 53.33, "Y": 46.67}}}, "risk_assessment": {"group_risk_score": 22.0, "group_risk_level": "Low", "risk_distribution": {"low_risk": 12, "medium_risk": 3, "high_risk": 0}, "risk_statistics": {"min_risk_score": 10, "max_risk_score": 43, "median_risk_score": 23.0, "std_risk_score": 8.41}, "top_risk_factors": {"Poor Health Condition": 3, "Chronic Conditions": 1}, "individual_risks": [{"employee_id": "EMP001", "risk_score": 23, "risk_level": "Low", "risk_factors": []}, {"employee_id": "EMP002", "risk_score": 23, "risk_level": "Low", "risk_factors": []}, {"employee_id": "EMP003", "risk_score": 25, "risk_level": "Low", "risk_factors": ["Health condition: Fair"]}, {"employee_id": "EMP004", "risk_score": 18, "risk_level": "Low", "risk_factors": []}, {"employee_id": "EMP005", "risk_score": 30, "risk_level": "Medium", "risk_factors": ["Health condition: Fair"]}, {"employee_id": "EMP006", "risk_score": 18, "risk_level": "Low", "risk_factors": []}, {"employee_id": "EMP007", "risk_score": 10, "risk_level": "Low", "risk_factors": []}, {"employee_id": "EMP008", "risk_score": 20, "risk_level": "Low", "risk_factors": []}, {"employee_id": "EMP009", "risk_score": 23, "risk_level": "Low", "risk_factors": []}, {"employee_id": "EMP010", "risk_score": 18, "risk_level": "Low", "risk_factors": []}]}, "data_quality_metrics": {"overall_completeness": {"total_cells": 2490, "missing_cells": "1716", "completeness_percentage": 31.08}, "column_completeness": {"employee_id": {"missing_count": "0", "completeness_percentage": 100.0}, "record_type": {"missing_count": "0", "completeness_percentage": 100.0}, "full_name": {"missing_count": "0", "completeness_percentage": 100.0}, "relationship": {"missing_count": "0", "completeness_percentage": 100.0}, "dob": {"missing_count": "0", "completeness_percentage": 100.0}, "gender": {"missing_count": "0", "completeness_percentage": 100.0}, "address1": {"missing_count": "0", "completeness_percentage": 100.0}, "city": {"missing_count": "0", "completeness_percentage": 100.0}, "state": {"missing_count": "0", "completeness_percentage": 100.0}, "zipcode": {"missing_count": "0", "completeness_percentage": 100.0}, "marital_status": {"missing_count": "0", "completeness_percentage": 100.0}, "medical_plan": {"missing_count": "0", "completeness_percentage": 100.0}, "dental_plan": {"missing_count": "0", "completeness_percentage": 100.0}, "vision_plan": {"missing_count": "0", "completeness_percentage": 100.0}, "dept_1_full_name": {"missing_count": "3", "completeness_percentage": 80.0}, "dept_1_relationship_type": {"missing_count": "3", "completeness_percentage": 80.0}, "dept_1_dob": {"missing_count": "3", "completeness_percentage": 80.0}, "dept_1_gender": {"missing_count": "3", "completeness_percentage": 80.0}, "employee_address1": {"missing_count": "3", "completeness_percentage": 80.0}, "employee_city": {"missing_count": "3", "completeness_percentage": 80.0}, "employee_state": {"missing_count": "3", "completeness_percentage": 80.0}, "employee_zipcode": {"missing_count": "3", "completeness_percentage": 80.0}, "employee_marital_status": {"missing_count": "3", "completeness_percentage": 80.0}, "employee_medical_plan_enrollment": {"missing_count": "3", "completeness_percentage": 80.0}, "employee_dental_plan_enrollment": {"missing_count": "3", "completeness_percentage": 80.0}, "employee_vision_plan_enrollment": {"missing_count": "3", "completeness_percentage": 80.0}, "relationship_type_1": {"missing_count": "3", "completeness_percentage": 80.0}, "dept_1_medical_plan": {"missing_count": "3", "completeness_percentage": 80.0}, "dept_1_dental_plan": {"missing_count": "3", "completeness_percentage": 80.0}, "dept_1_vision_plan": {"missing_count": "3", "completeness_percentage": 80.0}, "dept_2_full_name": {"missing_count": "8", "completeness_percentage": 46.67}, "dept_2_relationship_type": {"missing_count": "8", "completeness_percentage": 46.67}, "dept_2_dob": {"missing_count": "8", "completeness_percentage": 46.67}, "dept_2_gender": {"missing_count": "8", "completeness_percentage": 46.67}, "relationship_type_2": {"missing_count": "8", "completeness_percentage": 46.67}, "dept_2_medical_plan": {"missing_count": "8", "completeness_percentage": 46.67}, "dept_2_dental_plan": {"missing_count": "8", "completeness_percentage": 46.67}, "dept_2_vision_plan": {"missing_count": "8", "completeness_percentage": 46.67}, "dept_count": {"missing_count": "0", "completeness_percentage": 100.0}, "dept_3_full_name": {"missing_count": "12", "completeness_percentage": 20.0}, "dept_3_relationship_type": {"missing_count": "12", "completeness_percentage": 20.0}, "dept_3_dob": {"missing_count": "12", "completeness_percentage": 20.0}, "dept_3_gender": {"missing_count": "12", "completeness_percentage": 20.0}, "relationship_type_3": {"missing_count": "12", "completeness_percentage": 20.0}, "dept_3_medical_plan": {"missing_count": "12", "completeness_percentage": 20.0}, "dept_3_dental_plan": {"missing_count": "12", "completeness_percentage": 20.0}, "dept_3_vision_plan": {"missing_count": "12", "completeness_percentage": 20.0}, "name": {"missing_count": "0", "completeness_percentage": 100.0}, "age": {"missing_count": "0", "completeness_percentage": 100.0}, "dept_1_age": {"missing_count": "3", "completeness_percentage": 80.0}, "dept_2_age": {"missing_count": "8", "completeness_percentage": 46.67}, "dept_3_age": {"missing_count": "12", "completeness_percentage": 20.0}, "first_name": {"missing_count": "15", "completeness_percentage": 0.0}, "middle_name": {"missing_count": "15", "completeness_percentage": 0.0}, "last_name": {"missing_count": "15", "completeness_percentage": 0.0}, "address2": {"missing_count": "15", "completeness_percentage": 0.0}, "salary": {"missing_count": "15", "completeness_percentage": 0.0}, "employment_type": {"missing_count": "0", "completeness_percentage": 100.0}, "employee_class": {"missing_count": "15", "completeness_percentage": 0.0}, "department": {"missing_count": "0", "completeness_percentage": 100.0}, "hire_date": {"missing_count": "15", "completeness_percentage": 0.0}, "tobacco_use": {"missing_count": "0", "completeness_percentage": 100.0}, "pregnancy_status": {"missing_count": "0", "completeness_percentage": 100.0}, "life_plan": {"missing_count": "15", "completeness_percentage": 0.0}, "add_plan": {"missing_count": "15", "completeness_percentage": 0.0}, "coverage_tier": {"missing_count": "15", "completeness_percentage": 0.0}, "ssn": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_1": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_2": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_3": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_4": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_4_dob": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_4_age": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_4_gender": {"missing_count": "15", "completeness_percentage": 0.0}, "relationship_type_4": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_5": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_5_dob": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_5_age": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_5_gender": {"missing_count": "15", "completeness_percentage": 0.0}, "relationship_type_5": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_6": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_6_dob": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_6_age": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_6_gender": {"missing_count": "15", "completeness_percentage": 0.0}, "relationship_type_6": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_7": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_7_dob": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_7_age": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_7_gender": {"missing_count": "15", "completeness_percentage": 0.0}, "relationship_type_7": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_8": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_8_dob": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_8_age": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_8_gender": {"missing_count": "15", "completeness_percentage": 0.0}, "relationship_type_8": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_9": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_9_dob": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_9_age": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_9_gender": {"missing_count": "15", "completeness_percentage": 0.0}, "relationship_type_9": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_10": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_10_dob": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_10_age": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_10_gender": {"missing_count": "15", "completeness_percentage": 0.0}, "relationship_type_10": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_11": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_11_dob": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_11_age": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_11_gender": {"missing_count": "15", "completeness_percentage": 0.0}, "relationship_type_11": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_12": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_12_dob": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_12_age": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_12_gender": {"missing_count": "15", "completeness_percentage": 0.0}, "relationship_type_12": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_13": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_13_dob": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_13_age": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_13_gender": {"missing_count": "15", "completeness_percentage": 0.0}, "relationship_type_13": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_14": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_14_dob": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_14_age": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_14_gender": {"missing_count": "15", "completeness_percentage": 0.0}, "relationship_type_14": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_15": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_15_dob": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_15_age": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_15_gender": {"missing_count": "15", "completeness_percentage": 0.0}, "relationship_type_15": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_16": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_16_dob": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_16_age": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_16_gender": {"missing_count": "15", "completeness_percentage": 0.0}, "relationship_type_16": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_17": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_17_dob": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_17_age": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_17_gender": {"missing_count": "15", "completeness_percentage": 0.0}, "relationship_type_17": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_18": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_18_dob": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_18_age": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_18_gender": {"missing_count": "15", "completeness_percentage": 0.0}, "relationship_type_18": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_19": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_19_dob": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_19_age": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_19_gender": {"missing_count": "15", "completeness_percentage": 0.0}, "relationship_type_19": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_20": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_20_dob": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_20_age": {"missing_count": "15", "completeness_percentage": 0.0}, "dept_20_gender": {"missing_count": "15", "completeness_percentage": 0.0}, "relationship_type_20": {"missing_count": "15", "completeness_percentage": 0.0}, "job_type": {"missing_count": "0", "completeness_percentage": 100.0}, "region": {"missing_count": "0", "completeness_percentage": 100.0}, "health_condition": {"missing_count": "0", "completeness_percentage": 100.0}, "chronic_condition": {"missing_count": "0", "completeness_percentage": 100.0}, "prescription_use": {"missing_count": "0", "completeness_percentage": 100.0}, "income_tier": {"missing_count": "0", "completeness_percentage": 100.0}, "risk_tolerance": {"missing_count": "0", "completeness_percentage": 100.0}, "lifestyle": {"missing_count": "0", "completeness_percentage": 100.0}, "travel_frequency": {"missing_count": "0", "completeness_percentage": 100.0}, "hsa_familiarity": {"missing_count": "0", "completeness_percentage": 100.0}, "mental_health_needs": {"missing_count": "0", "completeness_percentage": 100.0}}, "critical_field_completeness": {"name": {"missing_count": "0", "completeness_percentage": 100.0}, "gender": {"missing_count": "0", "completeness_percentage": 100.0}, "zipcode": {"missing_count": "0", "completeness_percentage": 100.0}, "marital_status": {"missing_count": "0", "completeness_percentage": 100.0}}}}, "feature_validation": {"is_valid": true, "missing_features": [], "feature_completeness": {"age": {"present": true, "missing_values": "0", "completion_rate": 100.0}, "gender": {"present": true, "missing_values": "0", "completion_rate": 100.0}, "marital_status": {"present": true, "missing_values": "0", "completion_rate": 100.0}, "dept_count": {"present": true, "missing_values": "0", "completion_rate": 100.0}, "job_type": {"present": true, "missing_values": "0", "completion_rate": 100.0}, "employment_type": {"present": true, "missing_values": "0", "completion_rate": 100.0}, "department": {"present": true, "missing_values": "0", "completion_rate": 100.0}, "region": {"present": true, "missing_values": "0", "completion_rate": 100.0}, "health_condition": {"present": true, "missing_values": "0", "completion_rate": 100.0}, "chronic_condition": {"present": true, "missing_values": "0", "completion_rate": 100.0}, "prescription_use": {"present": true, "missing_values": "0", "completion_rate": 100.0}, "income_tier": {"present": true, "missing_values": "0", "completion_rate": 100.0}, "risk_tolerance": {"present": true, "missing_values": "0", "completion_rate": 100.0}, "lifestyle": {"present": true, "missing_values": "0", "completion_rate": 100.0}, "travel_frequency": {"present": true, "missing_values": "0", "completion_rate": 100.0}, "hsa_familiarity": {"present": true, "missing_values": "0", "completion_rate": 100.0}, "pregnancy_status": {"present": true, "missing_values": "0", "completion_rate": 100.0}, "tobacco_use": {"present": true, "missing_values": "0", "completion_rate": 100.0}, "mental_health_needs": {"present": true, "missing_values": "0", "completion_rate": 100.0}}, "overall_completion_rate": 100.0, "ready_for_prediction": "True"}, "prediction_method": "ml_models", "model_predictions": {"plan_types": {"total_predictions": 15, "average_confidence": "0.69198704", "unique_plans": 5}}, "prediction_summary": {"total_employees": 15, "plan_type_distribution": {"successful_predictions": {"POS": 5, "HDHP + HSA": 5, "PPO": 3, "HMO": 1, "HDHP": 1}, "failed_predictions": "0", "success_rate": 100.0, "total_predicted": "15"}, "benefits_distribution": {}, "confidence_metrics": {"plan_confidence": {"mean": "0.692", "min": "0.509", "max": "0.905", "count": 15}}, "prediction_success_rates": {}, "top_3_plan_probabilities": ["EMP001: [0.8853865, 0.06517888, 0.*********]", "EMP002: [0.9047229, 0.053544257, 0.0354696]", "EMP003: [0.5086255, 0.3922061, 0.*********]", "EMP004: [0.8084086, 0.********, 0.********]", "EMP005: [0.7808568, 0.15637265, 0.03912832]", "EMP006: [0.6196561, 0.18304129, 0.12304185]", "EMP007: [0.705592, 0.13772556, 0.07063187]", "EMP008: [0.79628164, 0.16281657, 0.*********]", "EMP009: [0.5364621, 0.3285278, 0.07181457]", "EMP010: [0.525606, 0.18719657, 0.18236935]", "EMP011: [0.7243671, 0.23427948, 0.02497729]", "EMP012: [0.52179396, 0.3922573, 0.07292019]", "EMP013: [0.69349563, 0.20090692, 0.060490277]", "EMP014: [0.6652446, 0.17657638, 0.13048217]", "EMP015: [0.70330554, 0.22117762, 0.055065375]"]}}}}}