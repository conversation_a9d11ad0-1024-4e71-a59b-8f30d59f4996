{"file_info": {"original_filename": "row4_benefits_dependents.csv", "processing_timestamp": "2025-07-15 17:44:29.358246", "total_processing_time_seconds": 45.51504182815552}, "final_response": {"success": true, "data": {"enriched_data_csv": "employee_id,record_type,first_name,last_name,relationship,dob,gender,address1,city,state,zipcode,marital_status,salary,employment_type,hire_date,medical_plan,dental_plan,vision_plan,life_plan,add_plan,tobacco_use,pregnancy_status,dept_1_first_name,dept_1_last_name,relationship_type_1,dept_1_dob,dept_1_gender,dept_1_medical_plan,dept_1_dental_plan,dept_1_vision_plan,dept_1_life_plan,dept_1_add_plan,dept_1_tobacco_use,dept_1_pregnancy_status,dept_1,dept_2_first_name,dept_2_last_name,relationship_type_2,dept_2_dob,dept_2_gender,dept_2_medical_plan,dept_2_dental_plan,dept_2_vision_plan,dept_2_life_plan,dept_2_add_plan,dept_2_tobacco_use,dept_2_pregnancy_status,dept_2,dept_count,dept_3_first_name,dept_3_last_name,relationship_type_3,dept_3_dob,dept_3_gender,dept_3_medical_plan,dept_3_dental_plan,dept_3_vision_plan,dept_3_life_plan,dept_3_add_plan,dept_3_tobacco_use,dept_3_pregnancy_status,dept_3,name,age,dept_1_age,dept_2_age,dept_3_age,middle_name,address2,employee_class,department,coverage_tier,ssn,dept_4,dept_4_dob,dept_4_age,dept_4_gender,relationship_type_4,dept_5,dept_5_dob,dept_5_age,dept_5_gender,relationship_type_5,dept_6,dept_6_dob,dept_6_age,dept_6_gender,relationship_type_6,dept_7,dept_7_dob,dept_7_age,dept_7_gender,relationship_type_7,dept_8,dept_8_dob,dept_8_age,dept_8_gender,relationship_type_8,dept_9,dept_9_dob,dept_9_age,dept_9_gender,relationship_type_9,dept_10,dept_10_dob,dept_10_age,dept_10_gender,relationship_type_10,dept_11,dept_11_dob,dept_11_age,dept_11_gender,relationship_type_11,dept_12,dept_12_dob,dept_12_age,dept_12_gender,relationship_type_12,dept_13,dept_13_dob,dept_13_age,dept_13_gender,relationship_type_13,dept_14,dept_14_dob,dept_14_age,dept_14_gender,relationship_type_14,dept_15,dept_15_dob,dept_15_age,dept_15_gender,relationship_type_15,dept_16,dept_16_dob,dept_16_age,dept_16_gender,relationship_type_16,dept_17,dept_17_dob,dept_17_age,dept_17_gender,relationship_type_17,dept_18,dept_18_dob,dept_18_age,dept_18_gender,relationship_type_18,dept_19,dept_19_dob,dept_19_age,dept_19_gender,relationship_type_19,dept_20,dept_20_dob,dept_20_age,dept_20_gender,relationship_type_20,job_type,region,health_condition,chronic_condition,prescription_use,income_tier,risk_tolerance,lifestyle,travel_frequency,hsa_familiarity,mental_health_needs,predicted_plan_type,plan_confidence,plan_reason,top_3_plans,top_3_plan_confidences,predicted_benefits,benefits_confidence,benefits_reason,top_3_benefits,top_3_benefits_confidences\r\nE001,Employee,John,Smith,Employee,1980-01-15,Male,123 Main St,Anytown,CA,90210,Married,65000,Full-Time,2020-01-15,Y,Y,Y,Y,Y,N,N,Jane,Smith,Spouse,1982-05-20,Female,Y,Y,Y,Y,Y,N,N,Jane Smith,Mike,Smith,Child,2010-09-12,Male,Y,Y,Y,Y,Y,N,N,Mike Smith,2,,,,,,,,,,,,,,John Smith,45.0,43.0,14.0,,,,,Engineering,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Desk,Urban,Excellent,N,Occasional,Medium ($50K–$100K),Medium,Active,Occasional,N,N,POS,0.8186588,\"Medium income, urban area - EPO for network-based care.\",\"['POS', 'HMO', 'PPO']\",\"[0.8186588, 0.1020346, 0.*********]\",\"['Dental', 'Vision', 'Accident', 'STD']\",1.0,Dental: Essential oral health coverage for all employees.; Vision: Eye care coverage for all employees.; Accident: Recommended for active lifestyle and occasional travel frequency.; STD: Income protection for Medium ($50K–$100K) earners or excellent health status.,\"['Dental', 'Vision', 'Accident']\",\"[0.5, 0.5, 0.5]\"\r\nE002,Employee,Alice,Brown,Employee,1975-03-10,Female,456 Oak Ave,Somewhere,TX,75001,Not Married,52000,Part-Time,2019-05-20,Y,Y,Y,Y,N,N,N,Bob,Brown,Child,2005-07-25,Male,Y,Y,Y,Y,N,N,N,Bob Brown,,,,,,,,,,,,,,1,,,,,,,,,,,,,,Alice Brown,50.0,19.0,,,,,,Engineering,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Desk,Urban,Fair,N,None,Medium ($50K–$100K),Low,Moderate,Rare,N,N,PPO,0.5796278,\"Medium income, urban area - EPO for network-based care.\",\"['PPO', 'POS', 'HMO']\",\"[0.5796278, 0.33144376, 0.06203681]\",\"['Dental', 'Vision', 'Accident', 'STD']\",1.0,Dental: Essential oral health coverage for all employees.; Vision: Eye care coverage for all employees.; STD: Income protection for Medium ($50K–$100K) earners or fair health status.,\"['Dental', 'Vision', 'Accident']\",\"[0.5, 0.5, 0.5]\"\r\nE003,Employee,Charlie,Davis,Employee,1990-06-30,Male,789 Pine Rd,Elsewhere,FL,33101,Married,78500,Full-Time,2021-03-10,Y,Y,Y,Y,Y,Y,N,Diana,Davis,Spouse,1992-08-15,Female,Y,Y,Y,Y,Y,Y,N,Diana Davis,Evan,Davis,Child,2015-12-01,Male,Y,Y,Y,Y,Y,Y,N,Evan Davis,3,Fiona,Davis,Child,2018-03-22,Female,Y,Y,Y,Y,Y,Y,N,Fiona Davis,Charlie Davis,35.0,32.0,9.0,7.0,,,,Engineering,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Desk,Urban,Good,N,None,High (>$100K),High,Moderate,Rare,Y,N,HDHP + HSA,0.9196201,\"High income, good health, HSA familiar, no chronic conditions - HDHP + HSA for tax benefits.\",\"['HDHP + HSA', 'HDHP', 'PPO']\",\"[0.9196201, 0.*********, 0.*********]\",\"['Accident', 'STD']\",1.0,STD: Income protection for High (>$100K) earners or good health status.,\"['Accident', 'STD']\",\"[0.5, 0.5]\"\r\nE004,Employee,Grace,Wilson,Employee,1985-11-05,Female,321 Elm St,Nowhere,NY,10001,Not Married,45000,Full-Time,2018-08-12,Y,Y,Y,N,N,N,Y,Henry,Wilson,Child,2012-04-18,Male,Y,Y,Y,N,N,N,Y,Henry Wilson,,,,,,,,,,,,,,1,,,,,,,,,,,,,,Grace Wilson,39.0,13.0,,,,,,Sales,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Desk,Urban,Fair,Y,Regular,Medium ($50K–$100K),Low,Moderate,Occasional,N,N,HMO,0.79898655,\"Medium income, urban area - EPO for network-based care.\",\"['HMO', 'PPO', 'POS']\",\"[0.79898655, 0.12962236, 0.06365036]\",\"['Dental', 'Vision', 'Hospital Indemnity', 'Critical Illness', 'STD']\",1.0,\"Dental: Essential oral health coverage for all employees.; Vision: Eye care coverage for all employees.; Hospital Indemnity: Recommended due to fair health condition, age 39, or chronic conditions.; Critical Illness: Important protection for age 39 with fair health status.; STD: Income protection for Medium ($50K–$100K) earners or fair health status.\",\"['Dental', 'Vision', 'Hospital Indemnity']\",\"[0.5, 0.5, 0.5]\"\r\nE005,Employee,Isaac,Johnson,Employee,1978-02-28,Male,654 Maple Dr,Anywhere,WA,98001,Married,95000,Full-Time,2017-11-30,Y,Y,Y,Y,Y,N,N,Lisa,Johnson,Spouse,1980-11-12,Female,Y,Y,Y,Y,Y,N,N,Lisa Johnson,Tom,Johnson,Child,2008-06-15,Male,Y,Y,Y,Y,Y,N,N,Tom Johnson,3,Sara,Johnson,Child,2011-09-03,Female,Y,Y,Y,Y,Y,N,N,Sara Johnson,Isaac Johnson,47.0,44.0,17.0,13.0,,,,Manufacturing,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Desk,Urban,Fair,N,Occasional,High (>$100K),Medium,Moderate,Rare,N,N,PPO,0.********,Middle age with dependents - PPO for family coverage.,\"['PPO', 'POS', 'Indemnity']\",\"[0.********, 0.*********, 0.********]\",\"['Accident', 'STD', 'FSA']\",1.0,STD: Income protection for High (>$100K) earners or fair health status.; FSA: Tax-advantaged account for medical expenses due to health conditions or pregnancy.,\"['Accident', 'STD', 'FSA']\",\"[0.5, 0.5, 0.5]\"\r\nE006,Employee,Michael,Anderson,Employee,1983-07-12,Male,987 Cedar Ln,Someplace,OR,97001,Not Married,38000,Part-Time,2022-02-14,Y,N,N,N,N,Y,N,,,,,,,,,,,,,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,Michael Anderson,42.0,,,,,,,Engineering,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Desk,Suburban,Fair,N,None,Low (<$50K),Medium,Moderate,Occasional,N,Y,EPO,0.9876359,\"Low income, part-time employment - MEC for minimum coverage compliance.\",\"['EPO', 'PPO', 'HMO']\",\"[0.9876359, 0.*********, 0.**********]\",\"['Accident', 'STD', 'Employee Assistance']\",1.0,STD: Income protection for Low (<$50K) earners or fair health status.,\"['Accident', 'STD', 'Employee Assistance']\",\"[0.5, 0.5, 0.5]\"\r\nE007,Employee,Nancy,Taylor,Employee,1987-12-03,Female,147 Birch Ave,Othertown,AZ,85001,Married,72000,Full-Time,2019-09-05,Y,Y,Y,Y,Y,N,Y,Paul,Taylor,Spouse,1985-04-20,Male,Y,Y,Y,Y,Y,N,Y,Paul Taylor,,,,,,,,,,,,,,1,,,,,,,,,,,,,,Nancy Taylor,37.0,40.0,,,,,,Engineering,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Desk,Urban,Good,N,None,Medium ($50K–$100K),Low,Moderate,Rare,N,Y,HMO,0.8429751,\"Medium income, good health - PPO for balanced cost and flexibility.\",\"['HMO', 'POS', 'PPO']\",\"[0.8429751, 0.14130242, 0.009764536]\",\"['Dental', 'Vision', 'Hospital Indemnity', 'Accident', 'STD', 'Employee Assistance']\",1.0,Dental: Essential oral health coverage for all employees.; Vision: Eye care coverage for all employees.; STD: Income protection for Medium ($50K–$100K) earners or good health status.,\"['Dental', 'Vision', 'Hospital Indemnity']\",\"[0.5, 0.5, 0.5]\"\r\nE008,Employee,Quinn,Martinez,Employee,1979-10-17,Male,258 Spruce St,Newplace,CO,80001,Not Married,58000,Full-Time,2020-06-18,Y,Y,Y,Y,N,Y,N,Alex,Martinez,Child,2007-12-25,Male,Y,Y,Y,Y,N,Y,N,Alex Martinez,Zoe,Martinez,Child,2010-03-14,Female,Y,Y,Y,Y,N,Y,N,Zoe Martinez,2,,,,,,,,,,,,,,Quinn Martinez,45.0,17.0,15.0,,,,,Information Technology,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Desk,Urban,Fair,N,None,Medium ($50K–$100K),Medium,Moderate,Rare,N,N,PPO,0.5654933,\"Medium income, urban area - EPO for network-based care.\",\"['PPO', 'POS', 'HMO']\",\"[0.5654933, 0.336153, 0.062148143]\",\"['Dental', 'Vision']\",1.0,Dental: Essential oral health coverage for all employees.; Vision: Eye care coverage for all employees.,\"['Dental', 'Vision']\",\"[0.5, 0.5]\"\r\nE009,Employee,Tina,Garcia,Employee,1981-03-24,Female,369 Willow Way,Lastplace,NV,89001,Married,67000,Full-Time,2018-12-01,Y,Y,Y,Y,Y,N,N,Carlos,Garcia,Spouse,1979-07-08,Male,Y,Y,Y,Y,Y,N,N,Carlos Garcia,Maya,Garcia,Child,2009-11-30,Female,Y,Y,Y,Y,Y,N,N,Maya Garcia,2,,,,,,,,,,,,,,Tina Garcia,44.0,46.0,15.0,,,,,Sales,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Desk,Urban,Excellent,N,Occasional,Medium ($50K–$100K),Low,Active,Frequent,N,N,POS,0.8743215,\"Medium income, urban area - EPO for network-based care.\",\"['POS', 'HMO', 'PPO']\",\"[0.8743215, 0.0560645, 0.04962947]\",\"['Dental', 'Vision']\",1.0,Dental: Essential oral health coverage for all employees.; Vision: Eye care coverage for all employees.,\"['Dental', 'Vision']\",\"[0.5, 0.5]\"\r\nE010,Employee,Xavier,Rodriguez,Employee,1986-09-09,Male,741 Poplar Pl,Finaltown,UT,84001,Not Married,41000,Contract,2021-07-22,Y,N,N,N,N,Y,N,,,,,,,,,,,,,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,Xavier Rodriguez,38.0,,,,,,,Finance,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Desk,Rural,Good,N,Occasional,Medium ($50K–$100K),Medium,Moderate,Rare,Y,N,HDHP,0.793197,\"Medium income, good health - PPO for balanced cost and flexibility.\",\"['HDHP', 'POS', 'HDHP + HSA']\",\"[0.793197, 0.080642484, 0.06918674]\",[],0.0,ML model prediction,\"['Dental', 'Vision', 'Hospital Indemnity']\",\"[0.5, 0.5, 0.5]\"\r\n", "comprehensive_statistics": {"basic_demographics": {"age_statistics": {"average_age": 42.2, "median_age": 43.0, "age_range": {"min": 35, "max": 50}, "age_distribution": {"18-30": 0, "31-45": 8, "46-60": 2, "60+": 0}}, "gender_composition": {"counts": {"Male": 6, "Female": 4}, "percentages": {"Male": 60.0, "Female": 40.0}}, "marital_status_distribution": {"counts": {"Married": 5, "Not Married": 5}, "percentages": {"Married": 50.0, "Not Married": 50.0}}}, "dependent_analysis": {"dependent_count_distribution": {"average_dependents": 1.5, "median_dependents": 1.5, "distribution": {"2": 3, "1": 3, "3": 2, "0": 2}, "employees_with_dependents": 8, "percentage_with_dependents": 80.0}, "dependent_age_analysis": {"total_dependents_found": 15, "average_dependent_age": 22.93, "median_dependent_age": 17.0, "dependents_over_26_as_children": 0, "age_distribution": {"0-5": 0, "6-12": 2, "13-18": 7, "19-26": 1, "26+": 5}}}, "employment_demographics": {"department_distribution": {"counts": {"Engineering": 5, "Sales": 2, "Manufacturing": 1, "Information Technology": 1, "Finance": 1}, "percentages": {"Engineering": 50.0, "Sales": 20.0, "Manufacturing": 10.0, "Information Technology": 10.0, "Finance": 10.0}}, "employment_type_distribution": {"counts": {"Full-Time": 7, "Part-Time": 2, "Contract": 1}, "percentages": {"Full-Time": 70.0, "Part-Time": 20.0, "Contract": 10.0}}, "job_type_distribution": {"counts": {"Desk": 10}, "percentages": {"Desk": 100.0}}, "employee_class_distribution": {"counts": {}, "percentages": {}}, "salary_analysis": {"average_salary": 61150.0, "median_salary": 61500.0, "salary_range": {"min": "38000", "max": "95000"}, "salary_distribution": {"under_40k": 1, "40k_75k": 7, "75k_100k": 2, "over_100k": 0}}}, "health_and_lifestyle": {"health_condition_distribution": {"counts": {"Fair": 5, "Good": 3, "Excellent": 2}, "percentages": {"Fair": 50.0, "Good": 30.0, "Excellent": 20.0}}, "chronic_condition_distribution": {"counts": {"N": 9, "Y": 1}, "percentages": {"N": 90.0, "Y": 10.0}}, "tobacco_use_distribution": {"counts": {"N": 6, "Y": 4}, "percentages": {"N": 60.0, "Y": 40.0}}, "lifestyle_distribution": {"counts": {"Moderate": 8, "Active": 2}, "percentages": {"Moderate": 80.0, "Active": 20.0}}, "prescription_use_distribution": {"counts": {"None": 5, "Occasional": 4, "Regular": 1}, "percentages": {"None": 50.0, "Occasional": 40.0, "Regular": 10.0}}}, "coverage_analysis": {"coverage_tier_distribution": {"counts": {}, "percentages": {}}, "coverage_tier_by_family_size": {}, "medical_plan_distribution": {"counts": {"Y": 10}, "percentages": {"Y": 100.0}}, "dental_plan_distribution": {"counts": {"Y": 8, "N": 2}, "percentages": {"Y": 80.0, "N": 20.0}}, "vision_plan_distribution": {"counts": {"Y": 8, "N": 2}, "percentages": {"Y": 80.0, "N": 20.0}}, "life_plan_distribution": {"counts": {"Y": 7, "N": 3}, "percentages": {"Y": 70.0, "N": 30.0}}}, "geographic_distribution": {"state_distribution": {"counts": {"CA": 1, "TX": 1, "FL": 1, "NY": 1, "WA": 1, "OR": 1, "AZ": 1, "CO": 1, "NV": 1, "UT": 1}, "percentages": {"CA": 10.0, "TX": 10.0, "FL": 10.0, "NY": 10.0, "WA": 10.0, "OR": 10.0, "AZ": 10.0, "CO": 10.0, "NV": 10.0, "UT": 10.0}}, "region_distribution": {"counts": {"Urban": 8, "Suburban": 1, "Rural": 1}, "percentages": {"Urban": 80.0, "Suburban": 10.0, "Rural": 10.0}}, "top_cities": {"counts": {"Anytown": 1, "Somewhere": 1, "Elsewhere": 1, "Nowhere": 1, "Anywhere": 1, "Someplace": 1, "Othertown": 1, "Newplace": 1, "Lastplace": 1, "Finaltown": 1}, "percentages": {"Anytown": 10.0, "Somewhere": 10.0, "Elsewhere": 10.0, "Nowhere": 10.0, "Anywhere": 10.0, "Someplace": 10.0, "Othertown": 10.0, "Newplace": 10.0, "Lastplace": 10.0, "Finaltown": 10.0}}}, "financial_demographics": {"income_tier_distribution": {"counts": {"Medium ($50K–$100K)": 7, "High (>$100K)": 2, "Low (<$50K)": 1}, "percentages": {"Medium ($50K–$100K)": 70.0, "High (>$100K)": 20.0, "Low (<$50K)": 10.0}}, "risk_tolerance_distribution": {"counts": {"Medium": 5, "Low": 4, "High": 1}, "percentages": {"Medium": 50.0, "Low": 40.0, "High": 10.0}}, "hsa_familiarity_distribution": {"counts": {"N": 8, "Y": 2}, "percentages": {"N": 80.0, "Y": 20.0}}}, "risk_assessment": {"group_risk_score": 33.4, "group_risk_level": "Medium", "risk_distribution": {"low_risk": 3, "medium_risk": 7, "high_risk": 0}, "risk_statistics": {"min_risk_score": 15, "max_risk_score": 48, "median_risk_score": 33.0, "std_risk_score": 12.83}, "top_risk_factors": {"Poor Health Condition": 5, "Tobacco Use": 4, "Advanced Age": 1, "Chronic Conditions": 1}, "individual_risks": [{"employee_id": "E001", "risk_score": 15, "risk_level": "Low", "risk_factors": []}, {"employee_id": "E002", "risk_score": 43, "risk_level": "Medium", "risk_factors": ["Age 50.0 (high risk)", "Health condition: Fair"]}, {"employee_id": "E003", "risk_score": 33, "risk_level": "Medium", "risk_factors": ["Tobacco user"]}, {"employee_id": "E004", "risk_score": 48, "risk_level": "Medium", "risk_factors": ["Health condition: Fair", "Has chronic condition"]}, {"employee_id": "E005", "risk_score": 33, "risk_level": "Medium", "risk_factors": ["Health condition: Fair"]}, {"employee_id": "E006", "risk_score": 48, "risk_level": "Medium", "risk_factors": ["Health condition: Fair", "Tobacco user"]}, {"employee_id": "E007", "risk_score": 18, "risk_level": "Low", "risk_factors": []}, {"employee_id": "E008", "risk_score": 48, "risk_level": "Medium", "risk_factors": ["Health condition: Fair", "Tobacco user"]}, {"employee_id": "E009", "risk_score": 15, "risk_level": "Low", "risk_factors": []}, {"employee_id": "E010", "risk_score": 33, "risk_level": "Medium", "risk_factors": ["Tobacco user"]}]}, "data_quality_metrics": {"overall_completeness": {"total_cells": 1690, "missing_cells": "1110", "completeness_percentage": 34.32}, "column_completeness": {"employee_id": {"missing_count": "0", "completeness_percentage": 100.0}, "record_type": {"missing_count": "0", "completeness_percentage": 100.0}, "first_name": {"missing_count": "0", "completeness_percentage": 100.0}, "last_name": {"missing_count": "0", "completeness_percentage": 100.0}, "relationship": {"missing_count": "0", "completeness_percentage": 100.0}, "dob": {"missing_count": "0", "completeness_percentage": 100.0}, "gender": {"missing_count": "0", "completeness_percentage": 100.0}, "address1": {"missing_count": "0", "completeness_percentage": 100.0}, "city": {"missing_count": "0", "completeness_percentage": 100.0}, "state": {"missing_count": "0", "completeness_percentage": 100.0}, "zipcode": {"missing_count": "0", "completeness_percentage": 100.0}, "marital_status": {"missing_count": "0", "completeness_percentage": 100.0}, "salary": {"missing_count": "0", "completeness_percentage": 100.0}, "employment_type": {"missing_count": "0", "completeness_percentage": 100.0}, "hire_date": {"missing_count": "0", "completeness_percentage": 100.0}, "medical_plan": {"missing_count": "0", "completeness_percentage": 100.0}, "dental_plan": {"missing_count": "0", "completeness_percentage": 100.0}, "vision_plan": {"missing_count": "0", "completeness_percentage": 100.0}, "life_plan": {"missing_count": "0", "completeness_percentage": 100.0}, "add_plan": {"missing_count": "0", "completeness_percentage": 100.0}, "tobacco_use": {"missing_count": "0", "completeness_percentage": 100.0}, "pregnancy_status": {"missing_count": "0", "completeness_percentage": 100.0}, "dept_1_first_name": {"missing_count": "2", "completeness_percentage": 80.0}, "dept_1_last_name": {"missing_count": "2", "completeness_percentage": 80.0}, "relationship_type_1": {"missing_count": "2", "completeness_percentage": 80.0}, "dept_1_dob": {"missing_count": "2", "completeness_percentage": 80.0}, "dept_1_gender": {"missing_count": "2", "completeness_percentage": 80.0}, "dept_1_medical_plan": {"missing_count": "2", "completeness_percentage": 80.0}, "dept_1_dental_plan": {"missing_count": "2", "completeness_percentage": 80.0}, "dept_1_vision_plan": {"missing_count": "2", "completeness_percentage": 80.0}, "dept_1_life_plan": {"missing_count": "2", "completeness_percentage": 80.0}, "dept_1_add_plan": {"missing_count": "2", "completeness_percentage": 80.0}, "dept_1_tobacco_use": {"missing_count": "2", "completeness_percentage": 80.0}, "dept_1_pregnancy_status": {"missing_count": "2", "completeness_percentage": 80.0}, "dept_1": {"missing_count": "2", "completeness_percentage": 80.0}, "dept_2_first_name": {"missing_count": "5", "completeness_percentage": 50.0}, "dept_2_last_name": {"missing_count": "5", "completeness_percentage": 50.0}, "relationship_type_2": {"missing_count": "5", "completeness_percentage": 50.0}, "dept_2_dob": {"missing_count": "5", "completeness_percentage": 50.0}, "dept_2_gender": {"missing_count": "5", "completeness_percentage": 50.0}, "dept_2_medical_plan": {"missing_count": "5", "completeness_percentage": 50.0}, "dept_2_dental_plan": {"missing_count": "5", "completeness_percentage": 50.0}, "dept_2_vision_plan": {"missing_count": "5", "completeness_percentage": 50.0}, "dept_2_life_plan": {"missing_count": "5", "completeness_percentage": 50.0}, "dept_2_add_plan": {"missing_count": "5", "completeness_percentage": 50.0}, "dept_2_tobacco_use": {"missing_count": "5", "completeness_percentage": 50.0}, "dept_2_pregnancy_status": {"missing_count": "5", "completeness_percentage": 50.0}, "dept_2": {"missing_count": "5", "completeness_percentage": 50.0}, "dept_count": {"missing_count": "0", "completeness_percentage": 100.0}, "dept_3_first_name": {"missing_count": "8", "completeness_percentage": 20.0}, "dept_3_last_name": {"missing_count": "8", "completeness_percentage": 20.0}, "relationship_type_3": {"missing_count": "8", "completeness_percentage": 20.0}, "dept_3_dob": {"missing_count": "8", "completeness_percentage": 20.0}, "dept_3_gender": {"missing_count": "8", "completeness_percentage": 20.0}, "dept_3_medical_plan": {"missing_count": "8", "completeness_percentage": 20.0}, "dept_3_dental_plan": {"missing_count": "8", "completeness_percentage": 20.0}, "dept_3_vision_plan": {"missing_count": "8", "completeness_percentage": 20.0}, "dept_3_life_plan": {"missing_count": "8", "completeness_percentage": 20.0}, "dept_3_add_plan": {"missing_count": "8", "completeness_percentage": 20.0}, "dept_3_tobacco_use": {"missing_count": "8", "completeness_percentage": 20.0}, "dept_3_pregnancy_status": {"missing_count": "8", "completeness_percentage": 20.0}, "dept_3": {"missing_count": "8", "completeness_percentage": 20.0}, "name": {"missing_count": "0", "completeness_percentage": 100.0}, "age": {"missing_count": "0", "completeness_percentage": 100.0}, "dept_1_age": {"missing_count": "2", "completeness_percentage": 80.0}, "dept_2_age": {"missing_count": "5", "completeness_percentage": 50.0}, "dept_3_age": {"missing_count": "8", "completeness_percentage": 20.0}, "middle_name": {"missing_count": "10", "completeness_percentage": 0.0}, "address2": {"missing_count": "10", "completeness_percentage": 0.0}, "employee_class": {"missing_count": "10", "completeness_percentage": 0.0}, "department": {"missing_count": "0", "completeness_percentage": 100.0}, "coverage_tier": {"missing_count": "10", "completeness_percentage": 0.0}, "ssn": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_4": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_4_dob": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_4_age": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_4_gender": {"missing_count": "10", "completeness_percentage": 0.0}, "relationship_type_4": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_5": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_5_dob": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_5_age": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_5_gender": {"missing_count": "10", "completeness_percentage": 0.0}, "relationship_type_5": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_6": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_6_dob": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_6_age": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_6_gender": {"missing_count": "10", "completeness_percentage": 0.0}, "relationship_type_6": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_7": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_7_dob": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_7_age": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_7_gender": {"missing_count": "10", "completeness_percentage": 0.0}, "relationship_type_7": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_8": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_8_dob": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_8_age": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_8_gender": {"missing_count": "10", "completeness_percentage": 0.0}, "relationship_type_8": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_9": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_9_dob": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_9_age": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_9_gender": {"missing_count": "10", "completeness_percentage": 0.0}, "relationship_type_9": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_10": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_10_dob": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_10_age": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_10_gender": {"missing_count": "10", "completeness_percentage": 0.0}, "relationship_type_10": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_11": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_11_dob": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_11_age": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_11_gender": {"missing_count": "10", "completeness_percentage": 0.0}, "relationship_type_11": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_12": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_12_dob": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_12_age": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_12_gender": {"missing_count": "10", "completeness_percentage": 0.0}, "relationship_type_12": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_13": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_13_dob": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_13_age": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_13_gender": {"missing_count": "10", "completeness_percentage": 0.0}, "relationship_type_13": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_14": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_14_dob": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_14_age": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_14_gender": {"missing_count": "10", "completeness_percentage": 0.0}, "relationship_type_14": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_15": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_15_dob": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_15_age": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_15_gender": {"missing_count": "10", "completeness_percentage": 0.0}, "relationship_type_15": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_16": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_16_dob": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_16_age": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_16_gender": {"missing_count": "10", "completeness_percentage": 0.0}, "relationship_type_16": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_17": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_17_dob": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_17_age": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_17_gender": {"missing_count": "10", "completeness_percentage": 0.0}, "relationship_type_17": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_18": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_18_dob": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_18_age": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_18_gender": {"missing_count": "10", "completeness_percentage": 0.0}, "relationship_type_18": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_19": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_19_dob": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_19_age": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_19_gender": {"missing_count": "10", "completeness_percentage": 0.0}, "relationship_type_19": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_20": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_20_dob": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_20_age": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_20_gender": {"missing_count": "10", "completeness_percentage": 0.0}, "relationship_type_20": {"missing_count": "10", "completeness_percentage": 0.0}, "job_type": {"missing_count": "0", "completeness_percentage": 100.0}, "region": {"missing_count": "0", "completeness_percentage": 100.0}, "health_condition": {"missing_count": "0", "completeness_percentage": 100.0}, "chronic_condition": {"missing_count": "0", "completeness_percentage": 100.0}, "prescription_use": {"missing_count": "0", "completeness_percentage": 100.0}, "income_tier": {"missing_count": "0", "completeness_percentage": 100.0}, "risk_tolerance": {"missing_count": "0", "completeness_percentage": 100.0}, "lifestyle": {"missing_count": "0", "completeness_percentage": 100.0}, "travel_frequency": {"missing_count": "0", "completeness_percentage": 100.0}, "hsa_familiarity": {"missing_count": "0", "completeness_percentage": 100.0}, "mental_health_needs": {"missing_count": "0", "completeness_percentage": 100.0}}, "critical_field_completeness": {"name": {"missing_count": "0", "completeness_percentage": 100.0}, "gender": {"missing_count": "0", "completeness_percentage": 100.0}, "zipcode": {"missing_count": "0", "completeness_percentage": 100.0}, "marital_status": {"missing_count": "0", "completeness_percentage": 100.0}}}}, "prediction_summary": {"total_employees": 10, "plan_type_distribution": {"successful_predictions": {"PPO": 3, "POS": 2, "HMO": 2, "HDHP + HSA": 1, "EPO": 1, "HDHP": 1}, "failed_predictions": "0", "success_rate": 100.0, "total_predicted": "10"}, "benefits_distribution": {}, "confidence_metrics": {"plan_confidence": {"mean": "0.797", "min": "0.565", "max": "0.988", "count": 10}}, "prediction_success_rates": {}, "top_3_plan_probabilities": []}, "total_employees": 10, "total_columns": 179, "has_predictions": true, "prediction_columns": ["predicted_plan_type", "plan_confidence"]}, "metadata": {"file_processing": {"filename": "row4_benefits_dependents.csv", "size": 4263, "original_column_names": ["Employee_ID", "Record_Type", "First_Name", "Last_Name", "Relationship", "DOB", "Gender", "Address1", "City", "State", "ZIP", "Marital_Status", "Salary", "Employment_Type", "Hire_Date", "Medical_Plan", "Dental_Plan", "Vision_Plan", "Life_Plan", "ADD_Plan", "Tobacco_Use", "Pregnancy_Status"]}, "pattern_analysis": {"pattern_type": "row_based_member_level", "pattern_confidence": 1.6, "pattern_reason": "Found 'relationship' field; Found both employee and dependent relationship values; Found 'record_type' field; Found Employee/Dependent record types; Found duplicate employee_ids (family grouping)", "analysis_details": {"confidence": 1.6, "reason": "Found 'relationship' field; Found both employee and dependent relationship values; Found 'record_type' field; Found Employee/Dependent record types; Found duplicate employee_ids (family grouping)"}}, "field_mapping": {"total_fields_mapped": 22, "mapping_success": true, "unmapped_fields": [], "mapping_confidence": 0, "field_mapping_details": {"Employee_ID": "employee_id", "Record_Type": "record_type", "First_Name": "first_name", "Last_Name": "last_name", "Relationship": "relationship", "DOB": "dob", "Gender": "gender", "Address1": "address1", "City": "city", "State": "state", "ZIP": "zipcode", "Marital_Status": "marital_status", "Salary": "salary", "Employment_Type": "employment_type", "Hire_Date": "hire_date", "Medical_Plan": "medical_plan", "Dental_Plan": "dental_plan", "Vision_Plan": "vision_plan", "Life_Plan": "life_plan", "ADD_Plan": "add_plan", "Tobacco_Use": "tobacco_use", "Pregnancy_Status": "pregnancy_status"}}, "data_processing": {"validation_passed": false, "processing_summary": {"original_rows": 25, "original_columns": 22, "processed_rows": 10, "processed_columns": 158, "processing_time_seconds": 34.34, "validation_passed": true, "validation_errors": 0, "error_rows": 0, "fields_processed": {"name_fields": true, "age_converted": true, "gender_standardized": true, "addresses_cleaned": true, "zipcode_validated": true}, "data_quality": {"complete_records": 10, "missing_data_rows": 0}}, "data_quality_score": 0.56, "processing_details": {"success": true, "preprocessed_data": "  employee_id record_type first_name  last_name relationship         dob  gender  ... dept_19_gender relationship_type_19 dept_20  dept_20_dob dept_20_age  dept_20_gender relationship_type_20\n0        E001    Employee       <PERSON>     Employee  1980-01-15    Male  ...           None                 None    None         None        None            None                 None\n1        E002    Employee      <PERSON>     Employee  1975-03-10  Female  ...           None                 None    None         None        None            None                 None\n2        E003    Employee    Charlie      <PERSON>     Employee  1990-06-30    Male  ...           None                 None    None         None        None            None                 None\n3        E004    Employee      <PERSON>     Employee  1985-11-05  Female  ...           None                 None    None         None        None            None                 None\n4        E005    Employee      <PERSON>     Employee  1978-02-28    Male  ...           None                 None    None         None        None            None                 None\n5        E006    Employee    <PERSON>     Employee  1983-07-12    Male  ...           None                 None    None         None        None            None                 None\n6        E007    Employee      <PERSON>ployee  1987-12-03  Female  ...           None                 None    None         None        None            None                 None\n7        E008    Employee      Quinn   <PERSON>ployee  1979-10-17    Male  ...           None                 None    None         None        None            None                 None\n8        E009    Employee       <PERSON>loy<PERSON>  1981-03-24  Female  ...           None                 None    None         None        None            None                 None\n9        E010    Employee     <PERSON>loy<PERSON>  1986-09-09    Male  ...           None                 None    None         None        None            None                 None\n\n[10 rows x 158 columns]", "validation": {"is_valid": true, "errors": [], "error_rows": [], "total_errors": 0}, "summary": {"original_rows": 25, "original_columns": 22, "processed_rows": 10, "processed_columns": 158, "processing_time_seconds": 34.34, "validation_passed": true, "validation_errors": 0, "error_rows": 0, "fields_processed": {"name_fields": true, "age_converted": true, "gender_standardized": true, "addresses_cleaned": true, "zipcode_validated": true}, "data_quality": {"complete_records": 10, "missing_data_rows": 0}}}}, "enrichment_and_prediction": {"success": true, "enriched_data": "  employee_id record_type first_name  last_name  ... benefits_confidence                                    benefits_reason                        top_3_benefits top_3_benefits_confidences\n0        E001    Employee       <PERSON>  ...                 1.0  Dental: Essential oral health coverage for all...            [Dental, Vision, Accident]            [0.5, 0.5, 0.5]\n1        E002    Employee      <PERSON>  ...                 1.0  Dental: Essential oral health coverage for all...            [Dental, Vision, Accident]            [0.5, 0.5, 0.5]\n2        E003    Employee    <PERSON>  ...                 1.0  STD: Income protection for High (>$100K) earne...                       [Accident, STD]                 [0.5, 0.5]\n3        E004    Employee      <PERSON>  ...                 1.0  Dental: Essential oral health coverage for all...  [Dental, Vision, Hospital Indemnity]            [0.5, 0.5, 0.5]\n4        E005    Employee      <PERSON>  ...                 1.0  STD: Income protection for High (>$100K) earne...                  [Accident, STD, FSA]            [0.5, 0.5, 0.5]\n5        E006    Employee    <PERSON>  ...                 1.0  STD: Income protection for Low (<$50K) earners...  [Acci<PERSON>, STD, Employee Assistance]            [0.5, 0.5, 0.5]\n6        E007    Employee      Nancy     <PERSON>  ...                 1.0  Dental: Essential oral health coverage for all...  [Dental, Vision, Hospital Indemnity]            [0.5, 0.5, 0.5]\n7        E008    Employee      Quinn   Martinez  ...                 1.0  Dental: Essential oral health coverage for all...                      [Dental, Vision]                 [0.5, 0.5]\n8        E009    Employee       Tina     Garcia  ...                 1.0  Dental: Essential oral health coverage for all...                      [Dental, Vision]                 [0.5, 0.5]\n9        E010    Employee     Xavier  Rodriguez  ...                 0.0                                ML model prediction  [Dental, Vision, Hospital Indemnity]            [0.5, 0.5, 0.5]\n\n[10 rows x 179 columns]", "enrichment_summary": {"total_employees": 10, "features_analyzed": 19, "enrichment_details": {"age": {"original_missing": "0", "final_missing": "0", "enriched_count": "0", "completion_rate": 100.0}, "gender": {"original_missing": "0", "final_missing": "0", "enriched_count": "0", "completion_rate": 100.0}, "marital_status": {"original_missing": "0", "final_missing": "0", "enriched_count": "0", "completion_rate": 100.0}, "dept_count": {"original_missing": "0", "final_missing": "0", "enriched_count": "0", "completion_rate": 100.0}, "job_type": {"original_missing": 10, "final_missing": "0", "enriched_count": "10", "completion_rate": 100.0}, "employment_type": {"original_missing": "0", "final_missing": "0", "enriched_count": "0", "completion_rate": 100.0}, "department": {"original_missing": "10", "final_missing": "0", "enriched_count": "10", "completion_rate": 100.0}, "region": {"original_missing": 10, "final_missing": "0", "enriched_count": "10", "completion_rate": 100.0}, "health_condition": {"original_missing": 10, "final_missing": "0", "enriched_count": "10", "completion_rate": 100.0}, "chronic_condition": {"original_missing": 10, "final_missing": "0", "enriched_count": "10", "completion_rate": 100.0}, "prescription_use": {"original_missing": 10, "final_missing": "0", "enriched_count": "10", "completion_rate": 100.0}, "income_tier": {"original_missing": 10, "final_missing": "0", "enriched_count": "10", "completion_rate": 100.0}, "risk_tolerance": {"original_missing": 10, "final_missing": "0", "enriched_count": "10", "completion_rate": 100.0}, "lifestyle": {"original_missing": 10, "final_missing": "0", "enriched_count": "10", "completion_rate": 100.0}, "travel_frequency": {"original_missing": 10, "final_missing": "0", "enriched_count": "10", "completion_rate": 100.0}, "hsa_familiarity": {"original_missing": 10, "final_missing": "0", "enriched_count": "10", "completion_rate": 100.0}, "pregnancy_status": {"original_missing": "0", "final_missing": "0", "enriched_count": "0", "completion_rate": 100.0}, "tobacco_use": {"original_missing": "0", "final_missing": "0", "enriched_count": "0", "completion_rate": 100.0}, "mental_health_needs": {"original_missing": 10, "final_missing": "0", "enriched_count": "10", "completion_rate": 100.0}}, "data_quality_improvement": {"total_missing_before": "120", "total_missing_after": "0", "total_enriched": "120", "overall_completion_rate": 100.0}}, "comprehensive_statistics": {"basic_demographics": {"age_statistics": {"average_age": 42.2, "median_age": 43.0, "age_range": {"min": 35, "max": 50}, "age_distribution": {"18-30": 0, "31-45": 8, "46-60": 2, "60+": 0}}, "gender_composition": {"counts": {"Male": 6, "Female": 4}, "percentages": {"Male": 60.0, "Female": 40.0}}, "marital_status_distribution": {"counts": {"Married": 5, "Not Married": 5}, "percentages": {"Married": 50.0, "Not Married": 50.0}}}, "dependent_analysis": {"dependent_count_distribution": {"average_dependents": 1.5, "median_dependents": 1.5, "distribution": {"2": 3, "1": 3, "3": 2, "0": 2}, "employees_with_dependents": 8, "percentage_with_dependents": 80.0}, "dependent_age_analysis": {"total_dependents_found": 15, "average_dependent_age": 22.93, "median_dependent_age": 17.0, "dependents_over_26_as_children": 0, "age_distribution": {"0-5": 0, "6-12": 2, "13-18": 7, "19-26": 1, "26+": 5}}}, "employment_demographics": {"department_distribution": {"counts": {"Engineering": 5, "Sales": 2, "Manufacturing": 1, "Information Technology": 1, "Finance": 1}, "percentages": {"Engineering": 50.0, "Sales": 20.0, "Manufacturing": 10.0, "Information Technology": 10.0, "Finance": 10.0}}, "employment_type_distribution": {"counts": {"Full-Time": 7, "Part-Time": 2, "Contract": 1}, "percentages": {"Full-Time": 70.0, "Part-Time": 20.0, "Contract": 10.0}}, "job_type_distribution": {"counts": {"Desk": 10}, "percentages": {"Desk": 100.0}}, "employee_class_distribution": {"counts": {}, "percentages": {}}, "salary_analysis": {"average_salary": 61150.0, "median_salary": 61500.0, "salary_range": {"min": "38000", "max": "95000"}, "salary_distribution": {"under_40k": 1, "40k_75k": 7, "75k_100k": 2, "over_100k": 0}}}, "health_and_lifestyle": {"health_condition_distribution": {"counts": {"Fair": 5, "Good": 3, "Excellent": 2}, "percentages": {"Fair": 50.0, "Good": 30.0, "Excellent": 20.0}}, "chronic_condition_distribution": {"counts": {"N": 9, "Y": 1}, "percentages": {"N": 90.0, "Y": 10.0}}, "tobacco_use_distribution": {"counts": {"N": 6, "Y": 4}, "percentages": {"N": 60.0, "Y": 40.0}}, "lifestyle_distribution": {"counts": {"Moderate": 8, "Active": 2}, "percentages": {"Moderate": 80.0, "Active": 20.0}}, "prescription_use_distribution": {"counts": {"None": 5, "Occasional": 4, "Regular": 1}, "percentages": {"None": 50.0, "Occasional": 40.0, "Regular": 10.0}}}, "coverage_analysis": {"coverage_tier_distribution": {"counts": {}, "percentages": {}}, "coverage_tier_by_family_size": {}, "medical_plan_distribution": {"counts": {"Y": 10}, "percentages": {"Y": 100.0}}, "dental_plan_distribution": {"counts": {"Y": 8, "N": 2}, "percentages": {"Y": 80.0, "N": 20.0}}, "vision_plan_distribution": {"counts": {"Y": 8, "N": 2}, "percentages": {"Y": 80.0, "N": 20.0}}, "life_plan_distribution": {"counts": {"Y": 7, "N": 3}, "percentages": {"Y": 70.0, "N": 30.0}}}, "geographic_distribution": {"state_distribution": {"counts": {"CA": 1, "TX": 1, "FL": 1, "NY": 1, "WA": 1, "OR": 1, "AZ": 1, "CO": 1, "NV": 1, "UT": 1}, "percentages": {"CA": 10.0, "TX": 10.0, "FL": 10.0, "NY": 10.0, "WA": 10.0, "OR": 10.0, "AZ": 10.0, "CO": 10.0, "NV": 10.0, "UT": 10.0}}, "region_distribution": {"counts": {"Urban": 8, "Suburban": 1, "Rural": 1}, "percentages": {"Urban": 80.0, "Suburban": 10.0, "Rural": 10.0}}, "top_cities": {"counts": {"Anytown": 1, "Somewhere": 1, "Elsewhere": 1, "Nowhere": 1, "Anywhere": 1, "Someplace": 1, "Othertown": 1, "Newplace": 1, "Lastplace": 1, "Finaltown": 1}, "percentages": {"Anytown": 10.0, "Somewhere": 10.0, "Elsewhere": 10.0, "Nowhere": 10.0, "Anywhere": 10.0, "Someplace": 10.0, "Othertown": 10.0, "Newplace": 10.0, "Lastplace": 10.0, "Finaltown": 10.0}}}, "financial_demographics": {"income_tier_distribution": {"counts": {"Medium ($50K–$100K)": 7, "High (>$100K)": 2, "Low (<$50K)": 1}, "percentages": {"Medium ($50K–$100K)": 70.0, "High (>$100K)": 20.0, "Low (<$50K)": 10.0}}, "risk_tolerance_distribution": {"counts": {"Medium": 5, "Low": 4, "High": 1}, "percentages": {"Medium": 50.0, "Low": 40.0, "High": 10.0}}, "hsa_familiarity_distribution": {"counts": {"N": 8, "Y": 2}, "percentages": {"N": 80.0, "Y": 20.0}}}, "risk_assessment": {"group_risk_score": 33.4, "group_risk_level": "Medium", "risk_distribution": {"low_risk": 3, "medium_risk": 7, "high_risk": 0}, "risk_statistics": {"min_risk_score": 15, "max_risk_score": 48, "median_risk_score": 33.0, "std_risk_score": 12.83}, "top_risk_factors": {"Poor Health Condition": 5, "Tobacco Use": 4, "Advanced Age": 1, "Chronic Conditions": 1}, "individual_risks": [{"employee_id": "E001", "risk_score": 15, "risk_level": "Low", "risk_factors": []}, {"employee_id": "E002", "risk_score": 43, "risk_level": "Medium", "risk_factors": ["Age 50.0 (high risk)", "Health condition: Fair"]}, {"employee_id": "E003", "risk_score": 33, "risk_level": "Medium", "risk_factors": ["Tobacco user"]}, {"employee_id": "E004", "risk_score": 48, "risk_level": "Medium", "risk_factors": ["Health condition: Fair", "Has chronic condition"]}, {"employee_id": "E005", "risk_score": 33, "risk_level": "Medium", "risk_factors": ["Health condition: Fair"]}, {"employee_id": "E006", "risk_score": 48, "risk_level": "Medium", "risk_factors": ["Health condition: Fair", "Tobacco user"]}, {"employee_id": "E007", "risk_score": 18, "risk_level": "Low", "risk_factors": []}, {"employee_id": "E008", "risk_score": 48, "risk_level": "Medium", "risk_factors": ["Health condition: Fair", "Tobacco user"]}, {"employee_id": "E009", "risk_score": 15, "risk_level": "Low", "risk_factors": []}, {"employee_id": "E010", "risk_score": 33, "risk_level": "Medium", "risk_factors": ["Tobacco user"]}]}, "data_quality_metrics": {"overall_completeness": {"total_cells": 1690, "missing_cells": "1110", "completeness_percentage": 34.32}, "column_completeness": {"employee_id": {"missing_count": "0", "completeness_percentage": 100.0}, "record_type": {"missing_count": "0", "completeness_percentage": 100.0}, "first_name": {"missing_count": "0", "completeness_percentage": 100.0}, "last_name": {"missing_count": "0", "completeness_percentage": 100.0}, "relationship": {"missing_count": "0", "completeness_percentage": 100.0}, "dob": {"missing_count": "0", "completeness_percentage": 100.0}, "gender": {"missing_count": "0", "completeness_percentage": 100.0}, "address1": {"missing_count": "0", "completeness_percentage": 100.0}, "city": {"missing_count": "0", "completeness_percentage": 100.0}, "state": {"missing_count": "0", "completeness_percentage": 100.0}, "zipcode": {"missing_count": "0", "completeness_percentage": 100.0}, "marital_status": {"missing_count": "0", "completeness_percentage": 100.0}, "salary": {"missing_count": "0", "completeness_percentage": 100.0}, "employment_type": {"missing_count": "0", "completeness_percentage": 100.0}, "hire_date": {"missing_count": "0", "completeness_percentage": 100.0}, "medical_plan": {"missing_count": "0", "completeness_percentage": 100.0}, "dental_plan": {"missing_count": "0", "completeness_percentage": 100.0}, "vision_plan": {"missing_count": "0", "completeness_percentage": 100.0}, "life_plan": {"missing_count": "0", "completeness_percentage": 100.0}, "add_plan": {"missing_count": "0", "completeness_percentage": 100.0}, "tobacco_use": {"missing_count": "0", "completeness_percentage": 100.0}, "pregnancy_status": {"missing_count": "0", "completeness_percentage": 100.0}, "dept_1_first_name": {"missing_count": "2", "completeness_percentage": 80.0}, "dept_1_last_name": {"missing_count": "2", "completeness_percentage": 80.0}, "relationship_type_1": {"missing_count": "2", "completeness_percentage": 80.0}, "dept_1_dob": {"missing_count": "2", "completeness_percentage": 80.0}, "dept_1_gender": {"missing_count": "2", "completeness_percentage": 80.0}, "dept_1_medical_plan": {"missing_count": "2", "completeness_percentage": 80.0}, "dept_1_dental_plan": {"missing_count": "2", "completeness_percentage": 80.0}, "dept_1_vision_plan": {"missing_count": "2", "completeness_percentage": 80.0}, "dept_1_life_plan": {"missing_count": "2", "completeness_percentage": 80.0}, "dept_1_add_plan": {"missing_count": "2", "completeness_percentage": 80.0}, "dept_1_tobacco_use": {"missing_count": "2", "completeness_percentage": 80.0}, "dept_1_pregnancy_status": {"missing_count": "2", "completeness_percentage": 80.0}, "dept_1": {"missing_count": "2", "completeness_percentage": 80.0}, "dept_2_first_name": {"missing_count": "5", "completeness_percentage": 50.0}, "dept_2_last_name": {"missing_count": "5", "completeness_percentage": 50.0}, "relationship_type_2": {"missing_count": "5", "completeness_percentage": 50.0}, "dept_2_dob": {"missing_count": "5", "completeness_percentage": 50.0}, "dept_2_gender": {"missing_count": "5", "completeness_percentage": 50.0}, "dept_2_medical_plan": {"missing_count": "5", "completeness_percentage": 50.0}, "dept_2_dental_plan": {"missing_count": "5", "completeness_percentage": 50.0}, "dept_2_vision_plan": {"missing_count": "5", "completeness_percentage": 50.0}, "dept_2_life_plan": {"missing_count": "5", "completeness_percentage": 50.0}, "dept_2_add_plan": {"missing_count": "5", "completeness_percentage": 50.0}, "dept_2_tobacco_use": {"missing_count": "5", "completeness_percentage": 50.0}, "dept_2_pregnancy_status": {"missing_count": "5", "completeness_percentage": 50.0}, "dept_2": {"missing_count": "5", "completeness_percentage": 50.0}, "dept_count": {"missing_count": "0", "completeness_percentage": 100.0}, "dept_3_first_name": {"missing_count": "8", "completeness_percentage": 20.0}, "dept_3_last_name": {"missing_count": "8", "completeness_percentage": 20.0}, "relationship_type_3": {"missing_count": "8", "completeness_percentage": 20.0}, "dept_3_dob": {"missing_count": "8", "completeness_percentage": 20.0}, "dept_3_gender": {"missing_count": "8", "completeness_percentage": 20.0}, "dept_3_medical_plan": {"missing_count": "8", "completeness_percentage": 20.0}, "dept_3_dental_plan": {"missing_count": "8", "completeness_percentage": 20.0}, "dept_3_vision_plan": {"missing_count": "8", "completeness_percentage": 20.0}, "dept_3_life_plan": {"missing_count": "8", "completeness_percentage": 20.0}, "dept_3_add_plan": {"missing_count": "8", "completeness_percentage": 20.0}, "dept_3_tobacco_use": {"missing_count": "8", "completeness_percentage": 20.0}, "dept_3_pregnancy_status": {"missing_count": "8", "completeness_percentage": 20.0}, "dept_3": {"missing_count": "8", "completeness_percentage": 20.0}, "name": {"missing_count": "0", "completeness_percentage": 100.0}, "age": {"missing_count": "0", "completeness_percentage": 100.0}, "dept_1_age": {"missing_count": "2", "completeness_percentage": 80.0}, "dept_2_age": {"missing_count": "5", "completeness_percentage": 50.0}, "dept_3_age": {"missing_count": "8", "completeness_percentage": 20.0}, "middle_name": {"missing_count": "10", "completeness_percentage": 0.0}, "address2": {"missing_count": "10", "completeness_percentage": 0.0}, "employee_class": {"missing_count": "10", "completeness_percentage": 0.0}, "department": {"missing_count": "0", "completeness_percentage": 100.0}, "coverage_tier": {"missing_count": "10", "completeness_percentage": 0.0}, "ssn": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_4": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_4_dob": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_4_age": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_4_gender": {"missing_count": "10", "completeness_percentage": 0.0}, "relationship_type_4": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_5": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_5_dob": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_5_age": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_5_gender": {"missing_count": "10", "completeness_percentage": 0.0}, "relationship_type_5": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_6": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_6_dob": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_6_age": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_6_gender": {"missing_count": "10", "completeness_percentage": 0.0}, "relationship_type_6": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_7": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_7_dob": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_7_age": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_7_gender": {"missing_count": "10", "completeness_percentage": 0.0}, "relationship_type_7": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_8": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_8_dob": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_8_age": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_8_gender": {"missing_count": "10", "completeness_percentage": 0.0}, "relationship_type_8": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_9": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_9_dob": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_9_age": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_9_gender": {"missing_count": "10", "completeness_percentage": 0.0}, "relationship_type_9": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_10": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_10_dob": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_10_age": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_10_gender": {"missing_count": "10", "completeness_percentage": 0.0}, "relationship_type_10": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_11": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_11_dob": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_11_age": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_11_gender": {"missing_count": "10", "completeness_percentage": 0.0}, "relationship_type_11": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_12": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_12_dob": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_12_age": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_12_gender": {"missing_count": "10", "completeness_percentage": 0.0}, "relationship_type_12": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_13": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_13_dob": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_13_age": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_13_gender": {"missing_count": "10", "completeness_percentage": 0.0}, "relationship_type_13": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_14": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_14_dob": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_14_age": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_14_gender": {"missing_count": "10", "completeness_percentage": 0.0}, "relationship_type_14": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_15": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_15_dob": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_15_age": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_15_gender": {"missing_count": "10", "completeness_percentage": 0.0}, "relationship_type_15": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_16": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_16_dob": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_16_age": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_16_gender": {"missing_count": "10", "completeness_percentage": 0.0}, "relationship_type_16": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_17": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_17_dob": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_17_age": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_17_gender": {"missing_count": "10", "completeness_percentage": 0.0}, "relationship_type_17": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_18": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_18_dob": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_18_age": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_18_gender": {"missing_count": "10", "completeness_percentage": 0.0}, "relationship_type_18": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_19": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_19_dob": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_19_age": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_19_gender": {"missing_count": "10", "completeness_percentage": 0.0}, "relationship_type_19": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_20": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_20_dob": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_20_age": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_20_gender": {"missing_count": "10", "completeness_percentage": 0.0}, "relationship_type_20": {"missing_count": "10", "completeness_percentage": 0.0}, "job_type": {"missing_count": "0", "completeness_percentage": 100.0}, "region": {"missing_count": "0", "completeness_percentage": 100.0}, "health_condition": {"missing_count": "0", "completeness_percentage": 100.0}, "chronic_condition": {"missing_count": "0", "completeness_percentage": 100.0}, "prescription_use": {"missing_count": "0", "completeness_percentage": 100.0}, "income_tier": {"missing_count": "0", "completeness_percentage": 100.0}, "risk_tolerance": {"missing_count": "0", "completeness_percentage": 100.0}, "lifestyle": {"missing_count": "0", "completeness_percentage": 100.0}, "travel_frequency": {"missing_count": "0", "completeness_percentage": 100.0}, "hsa_familiarity": {"missing_count": "0", "completeness_percentage": 100.0}, "mental_health_needs": {"missing_count": "0", "completeness_percentage": 100.0}}, "critical_field_completeness": {"name": {"missing_count": "0", "completeness_percentage": 100.0}, "gender": {"missing_count": "0", "completeness_percentage": 100.0}, "zipcode": {"missing_count": "0", "completeness_percentage": 100.0}, "marital_status": {"missing_count": "0", "completeness_percentage": 100.0}}}}, "feature_validation": {"is_valid": true, "missing_features": [], "feature_completeness": {"age": {"present": true, "missing_values": "0", "completion_rate": 100.0}, "gender": {"present": true, "missing_values": "0", "completion_rate": 100.0}, "marital_status": {"present": true, "missing_values": "0", "completion_rate": 100.0}, "dept_count": {"present": true, "missing_values": "0", "completion_rate": 100.0}, "job_type": {"present": true, "missing_values": "0", "completion_rate": 100.0}, "employment_type": {"present": true, "missing_values": "0", "completion_rate": 100.0}, "department": {"present": true, "missing_values": "0", "completion_rate": 100.0}, "region": {"present": true, "missing_values": "0", "completion_rate": 100.0}, "health_condition": {"present": true, "missing_values": "0", "completion_rate": 100.0}, "chronic_condition": {"present": true, "missing_values": "0", "completion_rate": 100.0}, "prescription_use": {"present": true, "missing_values": "0", "completion_rate": 100.0}, "income_tier": {"present": true, "missing_values": "0", "completion_rate": 100.0}, "risk_tolerance": {"present": true, "missing_values": "0", "completion_rate": 100.0}, "lifestyle": {"present": true, "missing_values": "0", "completion_rate": 100.0}, "travel_frequency": {"present": true, "missing_values": "0", "completion_rate": 100.0}, "hsa_familiarity": {"present": true, "missing_values": "0", "completion_rate": 100.0}, "pregnancy_status": {"present": true, "missing_values": "0", "completion_rate": 100.0}, "tobacco_use": {"present": true, "missing_values": "0", "completion_rate": 100.0}, "mental_health_needs": {"present": true, "missing_values": "0", "completion_rate": 100.0}}, "overall_completion_rate": 100.0, "ready_for_prediction": "True"}, "prediction_method": "ml_models", "model_predictions": {"plan_types": {"total_predictions": 10, "average_confidence": "0.7972986", "unique_plans": 6}}, "prediction_summary": {"total_employees": 10, "plan_type_distribution": {"successful_predictions": {"PPO": 3, "POS": 2, "HMO": 2, "HDHP + HSA": 1, "EPO": 1, "HDHP": 1}, "failed_predictions": "0", "success_rate": 100.0, "total_predicted": "10"}, "benefits_distribution": {}, "confidence_metrics": {"plan_confidence": {"mean": "0.797", "min": "0.565", "max": "0.988", "count": 10}}, "prediction_success_rates": {}, "top_3_plan_probabilities": ["E001: [0.8186588, 0.1020346, 0.*********]", "E002: [0.5796278, 0.33144376, 0.06203681]", "E003: [0.9196201, 0.*********, 0.*********]", "E004: [0.79898655, 0.12962236, 0.06365036]", "E005: [0.********, 0.*********, 0.********]", "E006: [0.9876359, 0.*********, 0.**********]", "E007: [0.8429751, 0.14130242, 0.009764536]", "E008: [0.5654933, 0.336153, 0.062148143]", "E009: [0.8743215, 0.0560645, 0.04962947]", "E010: [0.793197, 0.080642484, 0.06918674]"]}}}}}