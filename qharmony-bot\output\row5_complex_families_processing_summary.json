{"file_info": {"original_filename": "row5_complex_families.csv", "processing_timestamp": "2025-07-15 17:47:36.481162", "total_processing_time_seconds": 34.376858949661255}, "final_response": {"success": true, "data": {"enriched_data_csv": "employee_id,record_type,name,relationship,dob,gender,address1,city,state,zipcode,marital_status,medical_plan,dental_plan,vision_plan,ssn,dept_1,relationship_type_1,dept_1_dob,dept_1_gender,employee_address1,employee_city,employee_state,employee_zipcode,employee_marital_status,dept_1_medical_plan,dept_1_dental_plan,dept_1_vision_plan,dept_1_ssn,dept_2,relationship_type_2,dept_2_dob,dept_2_gender,dept_2_medical_plan,dept_2_dental_plan,dept_2_vision_plan,dept_2_ssn,dept_3,relationship_type_3,dept_3_dob,dept_3_gender,dept_3_medical_plan,dept_3_dental_plan,dept_3_vision_plan,dept_3_ssn,dept_4,relationship_type_4,dept_4_dob,dept_4_gender,dept_4_medical_plan,dept_4_dental_plan,dept_4_vision_plan,dept_4_ssn,dept_count,dept_5,relationship_type_5,dept_5_dob,dept_5_gender,dept_5_medical_plan,dept_5_dental_plan,dept_5_vision_plan,dept_5_ssn,age,dept_1_age,dept_2_age,dept_3_age,dept_4_age,dept_5_age,first_name,middle_name,last_name,address2,salary,employment_type,employee_class,department,hire_date,tobacco_use,pregnancy_status,life_plan,add_plan,coverage_tier,dept_6,dept_6_dob,dept_6_age,dept_6_gender,relationship_type_6,dept_7,dept_7_dob,dept_7_age,dept_7_gender,relationship_type_7,dept_8,dept_8_dob,dept_8_age,dept_8_gender,relationship_type_8,dept_9,dept_9_dob,dept_9_age,dept_9_gender,relationship_type_9,dept_10,dept_10_dob,dept_10_age,dept_10_gender,relationship_type_10,dept_11,dept_11_dob,dept_11_age,dept_11_gender,relationship_type_11,dept_12,dept_12_dob,dept_12_age,dept_12_gender,relationship_type_12,dept_13,dept_13_dob,dept_13_age,dept_13_gender,relationship_type_13,dept_14,dept_14_dob,dept_14_age,dept_14_gender,relationship_type_14,dept_15,dept_15_dob,dept_15_age,dept_15_gender,relationship_type_15,dept_16,dept_16_dob,dept_16_age,dept_16_gender,relationship_type_16,dept_17,dept_17_dob,dept_17_age,dept_17_gender,relationship_type_17,dept_18,dept_18_dob,dept_18_age,dept_18_gender,relationship_type_18,dept_19,dept_19_dob,dept_19_age,dept_19_gender,relationship_type_19,dept_20,dept_20_dob,dept_20_age,dept_20_gender,relationship_type_20,job_type,region,health_condition,chronic_condition,prescription_use,income_tier,risk_tolerance,lifestyle,travel_frequency,hsa_familiarity,mental_health_needs,predicted_plan_type,plan_confidence,plan_reason,top_3_plans,top_3_plan_confidences,predicted_benefits,benefits_confidence,benefits_reason,top_3_benefits,top_3_benefits_confidences\r\nE001,Dependent,John Smith,Employee,1980-01-15,Male,123 Main St,Anytown,CA,90210,Married,Y,Y,Y,123-45-6789,Jane Smith,Spouse,1982-05-20,Female,123 Main St,Anytown,CA,90210.0,Married,Y,Y,Y,123-45-6790,Mike Smith,Child,2010-09-12,Male,Y,Y,Y,123-45-6791,Sarah Smith,Child,2012-03-08,Female,Y,Y,Y,123-45-6792,Emma Smith,Child,2015-11-22,Female,Y,Y,Y,123-45-6793,4,,,,,,,,,45.0,43.0,14.0,13.0,9.0,,,,,,,Full-Time,,Engineering,,Y,N,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Desk,Urban,Good,Y,Regular,High (>$100K),Medium,Moderate,Occasional,N,N,PPO,0.7154574,Middle age with dependents - PPO for family coverage.,\"['PPO', 'POS', 'HMO']\",\"[0.7154574, 0.14241491, 0.*********]\",\"['Accident', 'Critical Illness', 'STD', 'Wellness Programs']\",1.0,Critical Illness: Important protection for age 45 with good health status.; STD: Income protection for High (>$100K) earners or good health status.,\"['Accident', 'Critical Illness', 'STD']\",\"[0.5, 0.5, 0.5]\"\r\nE002,Dependent,Alice Brown,Employee,1975-03-10,Female,456 Oak Ave,Somewhere,TX,75001,Not Married,Y,N,Y,987-65-4321,Bob Brown,Child,2005-07-25,Male,456 Oak Ave,Somewhere,TX,75001.0,Single,Y,N,Y,987-65-4322,,,,,,,,,,,,,,,,,,,,,,,,,1,,,,,,,,,50.0,19.0,,,,,,,,,,Full-Time,,Sales,,N,N,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Desk,Urban,Good,N,None,Medium ($50K–$100K),Low,Moderate,Frequent,Y,N,HDHP + HSA,0.719042,\"Medium income, good health - PPO for balanced cost and flexibility.\",\"['HDHP + HSA', 'POS', 'PPO']\",\"[0.719042, 0.25063118, 0.015628254]\",\"['Dental', 'Vision']\",1.0,Dental: Essential oral health coverage for all employees.; Vision: Eye care coverage for all employees.,\"['Dental', 'Vision']\",\"[0.5, 0.5]\"\r\nE003,Dependent,Charlie Davis,Employee,1990-06-30,Male,789 Pine Rd,Elsewhere,FL,33101,Married,Y,Y,Y,555-12-3456,Diana Davis,Spouse,1992-08-15,Female,789 Pine Rd,Elsewhere,FL,33101.0,Married,Y,Y,Y,555-12-3457,Evan Davis,Child,2015-12-01,Male,Y,Y,Y,555-12-3458,Fiona Davis,Child,2018-03-22,Female,Y,Y,Y,555-12-3459,Grace Davis,Child,2020-08-14,Female,Y,Y,Y,555-12-3460,4,,,,,,,,,35.0,32.0,9.0,7.0,4.0,,,,,,,Part-Time,,Information Technology,,N,N,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Desk,Urban,Good,N,None,Medium ($50K–$100K),Low,Moderate,Rare,N,N,POS,0.4981735,\"Medium income, good health - PPO for balanced cost and flexibility.\",\"['POS', 'HDHP', 'HMO']\",\"[0.4981735, 0.17083637, 0.16148324]\",[],0.0,ML model prediction,\"['Dental', 'Vision', 'Hospital Indemnity']\",\"[0.5, 0.5, 0.5]\"\r\nE004,Dependent,Robert Wilson,Employee,1985-11-05,Male,321 Elm St,Nowhere,NY,10001,Not Married,Y,Y,N,444-55-6677,Henry Wilson,Child,2012-04-18,Male,321 Elm St,Nowhere,NY,10001.0,Divorced,Y,Y,N,444-55-6678,Lisa Wilson,Child,2014-09-30,Female,Y,Y,N,444-55-6679,,,,,,,,,,,,,,,,,2,,,,,,,,,39.0,13.0,10.0,,,,,,,,,Full-Time,,Information Technology,,N,N,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Desk,Urban,Excellent,Y,Regular,Low (<$50K),Medium,Active,Rare,N,N,HMO,0.7343685,Middle age with dependents - PPO for family coverage.,\"['HMO', 'PPO', 'POS']\",\"[0.7343685, 0.21616599, 0.029586911]\",\"['Dental', 'Vision', 'Critical Illness']\",1.0,Dental: Essential oral health coverage for all employees.; Vision: Eye care coverage for all employees.; Critical Illness: Important protection for age 39 with excellent health status.,\"['Dental', 'Vision', 'Critical Illness']\",\"[0.5, 0.5, 0.5]\"\r\nE005,Dependent,Maria Johnson,Employee,1978-02-28,Female,654 Maple Dr,Anywhere,WA,98001,Married,Y,Y,Y,333-44-5566,Carlos Johnson,Spouse,1976-11-12,Male,654 Maple Dr,Anywhere,WA,98001.0,Married,Y,Y,Y,333-44-5567,Tom Johnson,Child,2008-06-15,Male,Y,Y,Y,333-44-5568,Sara Johnson,Child,2011-09-03,Female,Y,Y,Y,333-44-5569,Alex Johnson,Child,2013-12-20,Male,Y,Y,Y,333-44-5570,5,Maya Johnson,Child,2016-04-07,Female,Y,Y,Y,333-44-5571,47.0,48.0,17.0,13.0,11.0,9.0,,,,,,Full-Time,,Information Technology,,N,N,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Desk,Urban,Good,Y,Occasional,Medium ($50K–$100K),Low,Moderate,Rare,Y,N,PPO,0.50470585,\"Medium income, good health - PPO for balanced cost and flexibility.\",\"['PPO', 'HDHP + HSA', 'POS']\",\"[0.50470585, 0.29781798, 0.12193069]\",['Critical Illness'],1.0,Critical Illness: Important protection for age 47 with good health status.,['Critical Illness'],[0.5]\r\nE006,Employee,Michael Anderson,Employee,1983-07-12,Male,987 Cedar Ln,Someplace,OR,97001,Not Married,N,N,N,222-33-4455,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,0,,,,,,,,,42.0,,,,,,,,,,,Part-Time,,Engineering,,N,N,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Desk,Urban,Good,N,None,Low (<$50K),Medium,Moderate,Occasional,N,Y,HMO,0.7596685,\"Low income, part-time employment - MEC for minimum coverage compliance.\",\"['HMO', 'POS', 'EPO']\",\"[0.7596685, 0.13966534, 0.047967672]\",\"['Accident', 'STD', 'Employee Assistance']\",1.0,ML model prediction,\"['Accident', 'STD', 'Employee Assistance']\",\"[0.5, 0.5, 0.5]\"\r\nE007,Dependent,Nancy Taylor,Employee,1987-12-03,Female,147 Birch Ave,Othertown,AZ,85001,Married,Y,Y,Y,111-22-3344,Paul Taylor,Spouse,1985-04-20,Male,147 Birch Ave,Othertown,AZ,85001.0,Married,Y,Y,Y,111-22-3345,Emma Taylor,Child,2013-08-10,Female,Y,Y,Y,111-22-3346,,,,,,,,,,,,,,,,,2,,,,,,,,,37.0,40.0,11.0,,,,,,,,,Full-Time,,Finance,,N,N,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Desk,Urban,Good,N,None,High (>$100K),Medium,Moderate,Rare,Y,N,HDHP + HSA,0.6143533,\"High income, good health, HSA familiar, no chronic conditions - HDHP + HSA for tax benefits.\",\"['HDHP + HSA', 'HDHP', 'POS']\",\"[0.6143533, 0.31570056, 0.*********]\",\"['Dental', 'Vision', 'Term Life', 'LTD', 'Wellness Programs']\",1.0,Dental: Essential oral health coverage for all employees.; Vision: Eye care coverage for all employees.; Term Life: Essential protection for 2 dependents.; LTD: Long-term income protection for High (>$100K) earners or age 37+.,\"['Dental', 'Vision', 'Term Life']\",\"[0.5, 0.5, 0.5]\"\r\nE008,Dependent,David Martinez,Employee,1979-10-17,Male,258 Spruce St,Newplace,CO,80001,Not Married,Y,Y,Y,666-77-8899,Alex Martinez,Child,2007-12-25,Male,258 Spruce St,Newplace,CO,80001.0,Divorced,Y,Y,Y,666-77-8900,Zoe Martinez,Child,2010-03-14,Female,Y,Y,Y,666-77-8901,Luna Martinez,Child,2012-07-08,Female,Y,Y,Y,666-77-8902,,,,,,,,,3,,,,,,,,,45.0,17.0,15.0,13.0,,,,,,,,Full-Time,,Sales,,N,N,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Desk,Urban,Fair,Y,Regular,High (>$100K),Medium,Moderate,Frequent,Y,N,HDHP + HSA,0.790921,Middle age with dependents - PPO for family coverage.,\"['HDHP + HSA', 'PPO', 'POS']\",\"[0.790921, 0.********, 0.*********]\",\"['Critical Illness', 'FSA']\",1.0,Critical Illness: Important protection for age 45 with fair health status.; FSA: Tax-advantaged account for medical expenses due to health conditions or pregnancy.,\"['Critical Illness', 'FSA']\",\"[0.5, 0.5]\"\r\nE009,Dependent,Jennifer Garcia,Employee,1981-03-24,Female,369 Willow Way,Lastplace,NV,89001,Married,Y,Y,Y,777-88-9900,Carlos Garcia,Spouse,1979-07-08,Male,369 Willow Way,Lastplace,NV,89001.0,Married,Y,Y,Y,777-88-9901,Maya Garcia,Child,2009-11-30,Female,Y,Y,Y,777-88-9902,Diego Garcia,Child,2011-05-16,Male,Y,Y,Y,777-88-9903,,,,,,,,,3,,,,,,,,,44.0,46.0,15.0,14.0,,,,,,,,Full-Time,,Engineering,,N,N,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Desk,Urban,Excellent,N,None,High (>$100K),Medium,Active,Occasional,Y,N,HDHP + HSA,0.8683186,\"High income, good health, HSA familiar, no chronic conditions - HDHP + HSA for tax benefits.\",\"['HDHP + HSA', 'POS', 'PPO']\",\"[0.8683186, 0.********, 0.*********]\",\"['Accident', 'STD', 'FSA']\",1.0,Accident: Recommended for active lifestyle and occasional travel frequency.; STD: Income protection for High (>$100K) earners or excellent health status.,\"['Accident', 'STD', 'FSA']\",\"[0.5, 0.5, 0.5]\"\r\nE010,Employee,Xavier Rodriguez,Employee,1986-09-09,Male,741 Poplar Pl,Finaltown,UT,84001,Not Married,N,N,N,888-99-0011,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,0,,,,,,,,,38.0,,,,,,,,,,,Full-Time,,Sales,,N,N,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Desk,Rural,Good,N,None,Medium ($50K–$100K),Low,Active,Frequent,N,N,POS,0.6398547,\"Medium income, good health - PPO for balanced cost and flexibility.\",\"['POS', 'HMO', 'HDHP']\",\"[0.6398547, 0.*********, 0.*********]\",[],0.0,ML model prediction,\"['Dental', 'Vision', 'Hospital Indemnity']\",\"[0.5, 0.5, 0.5]\"\r\n", "comprehensive_statistics": {"basic_demographics": {"age_statistics": {"average_age": 42.2, "median_age": 43.0, "age_range": {"min": 35, "max": 50}, "age_distribution": {"18-30": 0, "31-45": 8, "46-60": 2, "60+": 0}}, "gender_composition": {"counts": {"Male": 6, "Female": 4}, "percentages": {"Male": 60.0, "Female": 40.0}}, "marital_status_distribution": {"counts": {"Married": 5, "Not Married": 5}, "percentages": {"Married": 50.0, "Not Married": 50.0}}}, "dependent_analysis": {"dependent_count_distribution": {"average_dependents": 2.4, "median_dependents": 2.5, "distribution": {"4": 2, "2": 2, "0": 2, "3": 2, "1": 1, "5": 1}, "employees_with_dependents": 8, "percentage_with_dependents": 80.0}, "dependent_age_analysis": {"total_dependents_found": 24, "average_dependent_age": 18.42, "median_dependent_age": 13.5, "dependents_over_26_as_children": 0, "age_distribution": {"0-5": 1, "6-12": 7, "13-18": 10, "19-26": 1, "26+": 5}}}, "employment_demographics": {"department_distribution": {"counts": {"Engineering": 3, "Sales": 3, "Information Technology": 3, "Finance": 1}, "percentages": {"Engineering": 30.0, "Sales": 30.0, "Information Technology": 30.0, "Finance": 10.0}}, "employment_type_distribution": {"counts": {"Full-Time": 8, "Part-Time": 2}, "percentages": {"Full-Time": 80.0, "Part-Time": 20.0}}, "job_type_distribution": {"counts": {"Desk": 10}, "percentages": {"Desk": 100.0}}, "employee_class_distribution": {"counts": {}, "percentages": {}}}, "health_and_lifestyle": {"health_condition_distribution": {"counts": {"Good": 7, "Excellent": 2, "Fair": 1}, "percentages": {"Good": 70.0, "Excellent": 20.0, "Fair": 10.0}}, "chronic_condition_distribution": {"counts": {"N": 6, "Y": 4}, "percentages": {"N": 60.0, "Y": 40.0}}, "tobacco_use_distribution": {"counts": {"N": 9, "Y": 1}, "percentages": {"N": 90.0, "Y": 10.0}}, "lifestyle_distribution": {"counts": {"Moderate": 7, "Active": 3}, "percentages": {"Moderate": 70.0, "Active": 30.0}}, "prescription_use_distribution": {"counts": {"None": 6, "Regular": 3, "Occasional": 1}, "percentages": {"None": 60.0, "Regular": 30.0, "Occasional": 10.0}}}, "coverage_analysis": {"coverage_tier_distribution": {"counts": {}, "percentages": {}}, "coverage_tier_by_family_size": {}, "medical_plan_distribution": {"counts": {"Y": 8, "N": 2}, "percentages": {"Y": 80.0, "N": 20.0}}, "dental_plan_distribution": {"counts": {"Y": 7, "N": 3}, "percentages": {"Y": 70.0, "N": 30.0}}, "vision_plan_distribution": {"counts": {"Y": 7, "N": 3}, "percentages": {"Y": 70.0, "N": 30.0}}, "life_plan_distribution": {"counts": {}, "percentages": {}}}, "geographic_distribution": {"state_distribution": {"counts": {"CA": 1, "TX": 1, "FL": 1, "NY": 1, "WA": 1, "OR": 1, "AZ": 1, "CO": 1, "NV": 1, "UT": 1}, "percentages": {"CA": 10.0, "TX": 10.0, "FL": 10.0, "NY": 10.0, "WA": 10.0, "OR": 10.0, "AZ": 10.0, "CO": 10.0, "NV": 10.0, "UT": 10.0}}, "region_distribution": {"counts": {"Urban": 9, "Rural": 1}, "percentages": {"Urban": 90.0, "Rural": 10.0}}, "top_cities": {"counts": {"Anytown": 1, "Somewhere": 1, "Elsewhere": 1, "Nowhere": 1, "Anywhere": 1, "Someplace": 1, "Othertown": 1, "Newplace": 1, "Lastplace": 1, "Finaltown": 1}, "percentages": {"Anytown": 10.0, "Somewhere": 10.0, "Elsewhere": 10.0, "Nowhere": 10.0, "Anywhere": 10.0, "Someplace": 10.0, "Othertown": 10.0, "Newplace": 10.0, "Lastplace": 10.0, "Finaltown": 10.0}}}, "financial_demographics": {"income_tier_distribution": {"counts": {"High (>$100K)": 4, "Medium ($50K–$100K)": 4, "Low (<$50K)": 2}, "percentages": {"High (>$100K)": 40.0, "Medium ($50K–$100K)": 40.0, "Low (<$50K)": 20.0}}, "risk_tolerance_distribution": {"counts": {"Medium": 6, "Low": 4}, "percentages": {"Medium": 60.0, "Low": 40.0}}, "hsa_familiarity_distribution": {"counts": {"N": 5, "Y": 5}, "percentages": {"N": 50.0, "Y": 50.0}}}, "risk_assessment": {"group_risk_score": 30.6, "group_risk_level": "Medium", "risk_distribution": {"low_risk": 5, "medium_risk": 5, "high_risk": 0}, "risk_statistics": {"min_risk_score": 15, "max_risk_score": 58, "median_risk_score": 26.5, "std_risk_score": 15.08}, "top_risk_factors": {"Chronic Conditions": 4, "Tobacco Use": 1, "Advanced Age": 1, "Poor Health Condition": 1}, "individual_risks": [{"employee_id": "E001", "risk_score": 58, "risk_level": "Medium", "risk_factors": ["Has chronic condition", "Tobacco user"]}, {"employee_id": "E002", "risk_score": 33, "risk_level": "Medium", "risk_factors": ["Age 50.0 (high risk)"]}, {"employee_id": "E003", "risk_score": 18, "risk_level": "Low", "risk_factors": []}, {"employee_id": "E004", "risk_score": 30, "risk_level": "Medium", "risk_factors": ["Has chronic condition"]}, {"employee_id": "E005", "risk_score": 43, "risk_level": "Medium", "risk_factors": ["Has chronic condition"]}, {"employee_id": "E006", "risk_score": 23, "risk_level": "Low", "risk_factors": []}, {"employee_id": "E007", "risk_score": 18, "risk_level": "Low", "risk_factors": []}, {"employee_id": "E008", "risk_score": 53, "risk_level": "Medium", "risk_factors": ["Health condition: Fair", "Has chronic condition"]}, {"employee_id": "E009", "risk_score": 15, "risk_level": "Low", "risk_factors": []}, {"employee_id": "E010", "risk_score": 15, "risk_level": "Low", "risk_factors": []}]}, "data_quality_metrics": {"overall_completeness": {"total_cells": 1670, "missing_cells": "1094", "completeness_percentage": 34.49}, "column_completeness": {"employee_id": {"missing_count": "0", "completeness_percentage": 100.0}, "record_type": {"missing_count": "0", "completeness_percentage": 100.0}, "name": {"missing_count": "0", "completeness_percentage": 100.0}, "relationship": {"missing_count": "0", "completeness_percentage": 100.0}, "dob": {"missing_count": "0", "completeness_percentage": 100.0}, "gender": {"missing_count": "0", "completeness_percentage": 100.0}, "address1": {"missing_count": "0", "completeness_percentage": 100.0}, "city": {"missing_count": "0", "completeness_percentage": 100.0}, "state": {"missing_count": "0", "completeness_percentage": 100.0}, "zipcode": {"missing_count": "0", "completeness_percentage": 100.0}, "marital_status": {"missing_count": "0", "completeness_percentage": 100.0}, "medical_plan": {"missing_count": "0", "completeness_percentage": 100.0}, "dental_plan": {"missing_count": "0", "completeness_percentage": 100.0}, "vision_plan": {"missing_count": "0", "completeness_percentage": 100.0}, "ssn": {"missing_count": "0", "completeness_percentage": 100.0}, "dept_1": {"missing_count": "2", "completeness_percentage": 80.0}, "relationship_type_1": {"missing_count": "2", "completeness_percentage": 80.0}, "dept_1_dob": {"missing_count": "2", "completeness_percentage": 80.0}, "dept_1_gender": {"missing_count": "2", "completeness_percentage": 80.0}, "employee_address1": {"missing_count": "2", "completeness_percentage": 80.0}, "employee_city": {"missing_count": "2", "completeness_percentage": 80.0}, "employee_state": {"missing_count": "2", "completeness_percentage": 80.0}, "employee_zipcode": {"missing_count": "2", "completeness_percentage": 80.0}, "employee_marital_status": {"missing_count": "2", "completeness_percentage": 80.0}, "dept_1_medical_plan": {"missing_count": "2", "completeness_percentage": 80.0}, "dept_1_dental_plan": {"missing_count": "2", "completeness_percentage": 80.0}, "dept_1_vision_plan": {"missing_count": "2", "completeness_percentage": 80.0}, "dept_1_ssn": {"missing_count": "2", "completeness_percentage": 80.0}, "dept_2": {"missing_count": "3", "completeness_percentage": 70.0}, "relationship_type_2": {"missing_count": "3", "completeness_percentage": 70.0}, "dept_2_dob": {"missing_count": "3", "completeness_percentage": 70.0}, "dept_2_gender": {"missing_count": "3", "completeness_percentage": 70.0}, "dept_2_medical_plan": {"missing_count": "3", "completeness_percentage": 70.0}, "dept_2_dental_plan": {"missing_count": "3", "completeness_percentage": 70.0}, "dept_2_vision_plan": {"missing_count": "3", "completeness_percentage": 70.0}, "dept_2_ssn": {"missing_count": "3", "completeness_percentage": 70.0}, "dept_3": {"missing_count": "5", "completeness_percentage": 50.0}, "relationship_type_3": {"missing_count": "5", "completeness_percentage": 50.0}, "dept_3_dob": {"missing_count": "5", "completeness_percentage": 50.0}, "dept_3_gender": {"missing_count": "5", "completeness_percentage": 50.0}, "dept_3_medical_plan": {"missing_count": "5", "completeness_percentage": 50.0}, "dept_3_dental_plan": {"missing_count": "5", "completeness_percentage": 50.0}, "dept_3_vision_plan": {"missing_count": "5", "completeness_percentage": 50.0}, "dept_3_ssn": {"missing_count": "5", "completeness_percentage": 50.0}, "dept_4": {"missing_count": "7", "completeness_percentage": 30.0}, "relationship_type_4": {"missing_count": "7", "completeness_percentage": 30.0}, "dept_4_dob": {"missing_count": "7", "completeness_percentage": 30.0}, "dept_4_gender": {"missing_count": "7", "completeness_percentage": 30.0}, "dept_4_medical_plan": {"missing_count": "7", "completeness_percentage": 30.0}, "dept_4_dental_plan": {"missing_count": "7", "completeness_percentage": 30.0}, "dept_4_vision_plan": {"missing_count": "7", "completeness_percentage": 30.0}, "dept_4_ssn": {"missing_count": "7", "completeness_percentage": 30.0}, "dept_count": {"missing_count": "0", "completeness_percentage": 100.0}, "dept_5": {"missing_count": "9", "completeness_percentage": 10.0}, "relationship_type_5": {"missing_count": "9", "completeness_percentage": 10.0}, "dept_5_dob": {"missing_count": "9", "completeness_percentage": 10.0}, "dept_5_gender": {"missing_count": "9", "completeness_percentage": 10.0}, "dept_5_medical_plan": {"missing_count": "9", "completeness_percentage": 10.0}, "dept_5_dental_plan": {"missing_count": "9", "completeness_percentage": 10.0}, "dept_5_vision_plan": {"missing_count": "9", "completeness_percentage": 10.0}, "dept_5_ssn": {"missing_count": "9", "completeness_percentage": 10.0}, "age": {"missing_count": "0", "completeness_percentage": 100.0}, "dept_1_age": {"missing_count": "2", "completeness_percentage": 80.0}, "dept_2_age": {"missing_count": "3", "completeness_percentage": 70.0}, "dept_3_age": {"missing_count": "5", "completeness_percentage": 50.0}, "dept_4_age": {"missing_count": "7", "completeness_percentage": 30.0}, "dept_5_age": {"missing_count": "9", "completeness_percentage": 10.0}, "first_name": {"missing_count": "10", "completeness_percentage": 0.0}, "middle_name": {"missing_count": "10", "completeness_percentage": 0.0}, "last_name": {"missing_count": "10", "completeness_percentage": 0.0}, "address2": {"missing_count": "10", "completeness_percentage": 0.0}, "salary": {"missing_count": "10", "completeness_percentage": 0.0}, "employment_type": {"missing_count": "0", "completeness_percentage": 100.0}, "employee_class": {"missing_count": "10", "completeness_percentage": 0.0}, "department": {"missing_count": "0", "completeness_percentage": 100.0}, "hire_date": {"missing_count": "10", "completeness_percentage": 0.0}, "tobacco_use": {"missing_count": "0", "completeness_percentage": 100.0}, "pregnancy_status": {"missing_count": "0", "completeness_percentage": 100.0}, "life_plan": {"missing_count": "10", "completeness_percentage": 0.0}, "add_plan": {"missing_count": "10", "completeness_percentage": 0.0}, "coverage_tier": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_6": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_6_dob": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_6_age": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_6_gender": {"missing_count": "10", "completeness_percentage": 0.0}, "relationship_type_6": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_7": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_7_dob": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_7_age": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_7_gender": {"missing_count": "10", "completeness_percentage": 0.0}, "relationship_type_7": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_8": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_8_dob": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_8_age": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_8_gender": {"missing_count": "10", "completeness_percentage": 0.0}, "relationship_type_8": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_9": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_9_dob": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_9_age": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_9_gender": {"missing_count": "10", "completeness_percentage": 0.0}, "relationship_type_9": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_10": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_10_dob": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_10_age": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_10_gender": {"missing_count": "10", "completeness_percentage": 0.0}, "relationship_type_10": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_11": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_11_dob": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_11_age": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_11_gender": {"missing_count": "10", "completeness_percentage": 0.0}, "relationship_type_11": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_12": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_12_dob": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_12_age": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_12_gender": {"missing_count": "10", "completeness_percentage": 0.0}, "relationship_type_12": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_13": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_13_dob": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_13_age": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_13_gender": {"missing_count": "10", "completeness_percentage": 0.0}, "relationship_type_13": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_14": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_14_dob": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_14_age": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_14_gender": {"missing_count": "10", "completeness_percentage": 0.0}, "relationship_type_14": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_15": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_15_dob": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_15_age": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_15_gender": {"missing_count": "10", "completeness_percentage": 0.0}, "relationship_type_15": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_16": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_16_dob": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_16_age": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_16_gender": {"missing_count": "10", "completeness_percentage": 0.0}, "relationship_type_16": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_17": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_17_dob": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_17_age": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_17_gender": {"missing_count": "10", "completeness_percentage": 0.0}, "relationship_type_17": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_18": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_18_dob": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_18_age": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_18_gender": {"missing_count": "10", "completeness_percentage": 0.0}, "relationship_type_18": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_19": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_19_dob": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_19_age": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_19_gender": {"missing_count": "10", "completeness_percentage": 0.0}, "relationship_type_19": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_20": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_20_dob": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_20_age": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_20_gender": {"missing_count": "10", "completeness_percentage": 0.0}, "relationship_type_20": {"missing_count": "10", "completeness_percentage": 0.0}, "job_type": {"missing_count": "0", "completeness_percentage": 100.0}, "region": {"missing_count": "0", "completeness_percentage": 100.0}, "health_condition": {"missing_count": "0", "completeness_percentage": 100.0}, "chronic_condition": {"missing_count": "0", "completeness_percentage": 100.0}, "prescription_use": {"missing_count": "0", "completeness_percentage": 100.0}, "income_tier": {"missing_count": "0", "completeness_percentage": 100.0}, "risk_tolerance": {"missing_count": "0", "completeness_percentage": 100.0}, "lifestyle": {"missing_count": "0", "completeness_percentage": 100.0}, "travel_frequency": {"missing_count": "0", "completeness_percentage": 100.0}, "hsa_familiarity": {"missing_count": "0", "completeness_percentage": 100.0}, "mental_health_needs": {"missing_count": "0", "completeness_percentage": 100.0}}, "critical_field_completeness": {"name": {"missing_count": "0", "completeness_percentage": 100.0}, "gender": {"missing_count": "0", "completeness_percentage": 100.0}, "zipcode": {"missing_count": "0", "completeness_percentage": 100.0}, "marital_status": {"missing_count": "0", "completeness_percentage": 100.0}}}}, "prediction_summary": {"total_employees": 10, "plan_type_distribution": {"successful_predictions": {"HDHP + HSA": 4, "PPO": 2, "POS": 2, "HMO": 2}, "failed_predictions": "0", "success_rate": 100.0, "total_predicted": "10"}, "benefits_distribution": {}, "confidence_metrics": {"plan_confidence": {"mean": "0.684", "min": "0.498", "max": "0.868", "count": 10}}, "prediction_success_rates": {}, "top_3_plan_probabilities": []}, "total_employees": 10, "total_columns": 177, "has_predictions": true, "prediction_columns": ["predicted_plan_type", "plan_confidence"]}, "metadata": {"file_processing": {"filename": "row5_complex_families.csv", "size": 3766, "original_column_names": ["Employee_ID", "Record_Type", "Name", "Relationship", "DOB", "Gender", "Address1", "City", "State", "ZIP", "Marital_Status", "Medical_Plan", "Dental_Plan", "Vision_Plan", "SSN"]}, "pattern_analysis": {"pattern_type": "row_based_member_level", "pattern_confidence": 1.6, "pattern_reason": "Found 'relationship' field; Found both employee and dependent relationship values; Found 'record_type' field; Found Employee/Dependent record types; Found duplicate employee_ids (family grouping)", "analysis_details": {"confidence": 1.6, "reason": "Found 'relationship' field; Found both employee and dependent relationship values; Found 'record_type' field; Found Employee/Dependent record types; Found duplicate employee_ids (family grouping)"}}, "field_mapping": {"total_fields_mapped": 15, "mapping_success": true, "unmapped_fields": [], "mapping_confidence": 0, "field_mapping_details": {"Employee_ID": "employee_id", "Record_Type": "record_type", "Name": "name", "Relationship": "relationship", "DOB": "dob", "Gender": "gender", "Address1": "address1", "City": "city", "State": "state", "ZIP": "zipcode", "Marital_Status": "marital_status", "Medical_Plan": "medical_plan", "Dental_Plan": "dental_plan", "Vision_Plan": "vision_plan", "SSN": "ssn"}}, "data_processing": {"validation_passed": false, "processing_summary": {"original_rows": 34, "original_columns": 15, "processed_rows": 10, "processed_columns": 156, "processing_time_seconds": 25.29, "validation_passed": true, "validation_errors": 0, "error_rows": 0, "fields_processed": {"name_fields": true, "age_converted": true, "gender_standardized": true, "addresses_cleaned": true, "zipcode_validated": true}, "data_quality": {"complete_records": 10, "missing_data_rows": 0}}, "data_quality_score": 0.518, "processing_details": {"success": true, "preprocessed_data": "  employee_id record_type              name relationship         dob  gender  ... relationship_type_19 dept_20 dept_20_dob  dept_20_age dept_20_gender relationship_type_20\n0        E001   Dependent        <PERSON>     Employee  1980-01-15    Male  ...                 None    None        None         None           None                 None\n1        E002   Dependent       <PERSON>     Employee  1975-03-10  Female  ...                 None    None        None         None           None                 None\n2        E003   Dependent     <PERSON>     Employee  1990-06-30    Male  ...                 None    None        None         None           None                 None\n3        E004   Dependent     <PERSON>loyee  1985-11-05    Male  ...                 None    None        None         None           None                 None\n4        E005   Dependent     <PERSON>ployee  1978-02-28  Female  ...                 None    None        None         None           None                 None\n5        E006    Employee  <PERSON>loy<PERSON>  1983-07-12    Male  ...                 None    None        None         None           None                 None\n6        E007   Dependent      <PERSON>     Employee  1987-12-03  Female  ...                 None    None        None         None           None                 None\n7        E008   Dependent    <PERSON>     Employee  1979-10-17    Male  ...                 None    None        None         None           None                 None\n8        E009   Dependent   <PERSON>ployee  1981-03-24  Female  ...                 None    None        None         None           None                 None\n9        E010    Employee  <PERSON>loyee  1986-09-09    Male  ...                 None    None        None         None           None                 None\n\n[10 rows x 156 columns]", "validation": {"is_valid": true, "errors": [], "error_rows": [], "total_errors": 0}, "summary": {"original_rows": 34, "original_columns": 15, "processed_rows": 10, "processed_columns": 156, "processing_time_seconds": 25.29, "validation_passed": true, "validation_errors": 0, "error_rows": 0, "fields_processed": {"name_fields": true, "age_converted": true, "gender_standardized": true, "addresses_cleaned": true, "zipcode_validated": true}, "data_quality": {"complete_records": 10, "missing_data_rows": 0}}}}, "enrichment_and_prediction": {"success": true, "enriched_data": "  employee_id record_type              name  ...                                    benefits_reason                        top_3_benefits top_3_benefits_confidences\n0        E001   Dependent        <PERSON>  ...  Critical Illness: Important protection for age...     [Accident, Critical Illness, STD]            [0.5, 0.5, 0.5]\n1        E002   Dependent       Alice Brown  ...  Dental: Essential oral health coverage for all...                      [Dental, Vision]                 [0.5, 0.5]\n2        E003   Dependent     Charlie Davis  ...                                ML model prediction  [Dental, Vision, Hospital Indemnity]            [0.5, 0.5, 0.5]\n3        E004   Dependent     <PERSON>  ...  Dental: Essential oral health coverage for all...    [Dental, Vision, Critical Illness]            [0.5, 0.5, 0.5]\n4        E005   Dependent     Maria Johnson  ...  Critical Illness: Important protection for age...                    [Critical Illness]                      [0.5]\n5        E006    Employee  <PERSON>  ...                                ML model prediction  [Accident, STD, Employee Assistance]            [0.5, 0.5, 0.5]\n6        E007   Dependent      <PERSON>  ...  Dental: Essential oral health coverage for all...           [Dental, Vision, Term Life]            [0.5, 0.5, 0.5]\n7        E008   Dependent    <PERSON>  ...  Critical Illness: Important protection for age...               [Critical Illness, FSA]                 [0.5, 0.5]\n8        E009   Dependent   Jennifer Garcia  ...  Accident: Recommended for active lifestyle and...                  [Accident, STD, FSA]            [0.5, 0.5, 0.5]\n9        E010    Employee  Xavier Rodriguez  ...                                ML model prediction  [Dental, Vision, Hospital Indemnity]            [0.5, 0.5, 0.5]\n\n[10 rows x 177 columns]", "enrichment_summary": {"total_employees": 10, "features_analyzed": 19, "enrichment_details": {"age": {"original_missing": "0", "final_missing": "0", "enriched_count": "0", "completion_rate": 100.0}, "gender": {"original_missing": "0", "final_missing": "0", "enriched_count": "0", "completion_rate": 100.0}, "marital_status": {"original_missing": "0", "final_missing": "0", "enriched_count": "0", "completion_rate": 100.0}, "dept_count": {"original_missing": "0", "final_missing": "0", "enriched_count": "0", "completion_rate": 100.0}, "job_type": {"original_missing": 10, "final_missing": "0", "enriched_count": "10", "completion_rate": 100.0}, "employment_type": {"original_missing": "10", "final_missing": "0", "enriched_count": "10", "completion_rate": 100.0}, "department": {"original_missing": "10", "final_missing": "0", "enriched_count": "10", "completion_rate": 100.0}, "region": {"original_missing": 10, "final_missing": "0", "enriched_count": "10", "completion_rate": 100.0}, "health_condition": {"original_missing": 10, "final_missing": "0", "enriched_count": "10", "completion_rate": 100.0}, "chronic_condition": {"original_missing": 10, "final_missing": "0", "enriched_count": "10", "completion_rate": 100.0}, "prescription_use": {"original_missing": 10, "final_missing": "0", "enriched_count": "10", "completion_rate": 100.0}, "income_tier": {"original_missing": 10, "final_missing": "0", "enriched_count": "10", "completion_rate": 100.0}, "risk_tolerance": {"original_missing": 10, "final_missing": "0", "enriched_count": "10", "completion_rate": 100.0}, "lifestyle": {"original_missing": 10, "final_missing": "0", "enriched_count": "10", "completion_rate": 100.0}, "travel_frequency": {"original_missing": 10, "final_missing": "0", "enriched_count": "10", "completion_rate": 100.0}, "hsa_familiarity": {"original_missing": 10, "final_missing": "0", "enriched_count": "10", "completion_rate": 100.0}, "pregnancy_status": {"original_missing": "10", "final_missing": "0", "enriched_count": "10", "completion_rate": 100.0}, "tobacco_use": {"original_missing": "10", "final_missing": "0", "enriched_count": "10", "completion_rate": 100.0}, "mental_health_needs": {"original_missing": 10, "final_missing": "0", "enriched_count": "10", "completion_rate": 100.0}}, "data_quality_improvement": {"total_missing_before": "150", "total_missing_after": "0", "total_enriched": "150", "overall_completion_rate": 100.0}}, "comprehensive_statistics": {"basic_demographics": {"age_statistics": {"average_age": 42.2, "median_age": 43.0, "age_range": {"min": 35, "max": 50}, "age_distribution": {"18-30": 0, "31-45": 8, "46-60": 2, "60+": 0}}, "gender_composition": {"counts": {"Male": 6, "Female": 4}, "percentages": {"Male": 60.0, "Female": 40.0}}, "marital_status_distribution": {"counts": {"Married": 5, "Not Married": 5}, "percentages": {"Married": 50.0, "Not Married": 50.0}}}, "dependent_analysis": {"dependent_count_distribution": {"average_dependents": 2.4, "median_dependents": 2.5, "distribution": {"4": 2, "2": 2, "0": 2, "3": 2, "1": 1, "5": 1}, "employees_with_dependents": 8, "percentage_with_dependents": 80.0}, "dependent_age_analysis": {"total_dependents_found": 24, "average_dependent_age": 18.42, "median_dependent_age": 13.5, "dependents_over_26_as_children": 0, "age_distribution": {"0-5": 1, "6-12": 7, "13-18": 10, "19-26": 1, "26+": 5}}}, "employment_demographics": {"department_distribution": {"counts": {"Engineering": 3, "Sales": 3, "Information Technology": 3, "Finance": 1}, "percentages": {"Engineering": 30.0, "Sales": 30.0, "Information Technology": 30.0, "Finance": 10.0}}, "employment_type_distribution": {"counts": {"Full-Time": 8, "Part-Time": 2}, "percentages": {"Full-Time": 80.0, "Part-Time": 20.0}}, "job_type_distribution": {"counts": {"Desk": 10}, "percentages": {"Desk": 100.0}}, "employee_class_distribution": {"counts": {}, "percentages": {}}}, "health_and_lifestyle": {"health_condition_distribution": {"counts": {"Good": 7, "Excellent": 2, "Fair": 1}, "percentages": {"Good": 70.0, "Excellent": 20.0, "Fair": 10.0}}, "chronic_condition_distribution": {"counts": {"N": 6, "Y": 4}, "percentages": {"N": 60.0, "Y": 40.0}}, "tobacco_use_distribution": {"counts": {"N": 9, "Y": 1}, "percentages": {"N": 90.0, "Y": 10.0}}, "lifestyle_distribution": {"counts": {"Moderate": 7, "Active": 3}, "percentages": {"Moderate": 70.0, "Active": 30.0}}, "prescription_use_distribution": {"counts": {"None": 6, "Regular": 3, "Occasional": 1}, "percentages": {"None": 60.0, "Regular": 30.0, "Occasional": 10.0}}}, "coverage_analysis": {"coverage_tier_distribution": {"counts": {}, "percentages": {}}, "coverage_tier_by_family_size": {}, "medical_plan_distribution": {"counts": {"Y": 8, "N": 2}, "percentages": {"Y": 80.0, "N": 20.0}}, "dental_plan_distribution": {"counts": {"Y": 7, "N": 3}, "percentages": {"Y": 70.0, "N": 30.0}}, "vision_plan_distribution": {"counts": {"Y": 7, "N": 3}, "percentages": {"Y": 70.0, "N": 30.0}}, "life_plan_distribution": {"counts": {}, "percentages": {}}}, "geographic_distribution": {"state_distribution": {"counts": {"CA": 1, "TX": 1, "FL": 1, "NY": 1, "WA": 1, "OR": 1, "AZ": 1, "CO": 1, "NV": 1, "UT": 1}, "percentages": {"CA": 10.0, "TX": 10.0, "FL": 10.0, "NY": 10.0, "WA": 10.0, "OR": 10.0, "AZ": 10.0, "CO": 10.0, "NV": 10.0, "UT": 10.0}}, "region_distribution": {"counts": {"Urban": 9, "Rural": 1}, "percentages": {"Urban": 90.0, "Rural": 10.0}}, "top_cities": {"counts": {"Anytown": 1, "Somewhere": 1, "Elsewhere": 1, "Nowhere": 1, "Anywhere": 1, "Someplace": 1, "Othertown": 1, "Newplace": 1, "Lastplace": 1, "Finaltown": 1}, "percentages": {"Anytown": 10.0, "Somewhere": 10.0, "Elsewhere": 10.0, "Nowhere": 10.0, "Anywhere": 10.0, "Someplace": 10.0, "Othertown": 10.0, "Newplace": 10.0, "Lastplace": 10.0, "Finaltown": 10.0}}}, "financial_demographics": {"income_tier_distribution": {"counts": {"High (>$100K)": 4, "Medium ($50K–$100K)": 4, "Low (<$50K)": 2}, "percentages": {"High (>$100K)": 40.0, "Medium ($50K–$100K)": 40.0, "Low (<$50K)": 20.0}}, "risk_tolerance_distribution": {"counts": {"Medium": 6, "Low": 4}, "percentages": {"Medium": 60.0, "Low": 40.0}}, "hsa_familiarity_distribution": {"counts": {"N": 5, "Y": 5}, "percentages": {"N": 50.0, "Y": 50.0}}}, "risk_assessment": {"group_risk_score": 30.6, "group_risk_level": "Medium", "risk_distribution": {"low_risk": 5, "medium_risk": 5, "high_risk": 0}, "risk_statistics": {"min_risk_score": 15, "max_risk_score": 58, "median_risk_score": 26.5, "std_risk_score": 15.08}, "top_risk_factors": {"Chronic Conditions": 4, "Tobacco Use": 1, "Advanced Age": 1, "Poor Health Condition": 1}, "individual_risks": [{"employee_id": "E001", "risk_score": 58, "risk_level": "Medium", "risk_factors": ["Has chronic condition", "Tobacco user"]}, {"employee_id": "E002", "risk_score": 33, "risk_level": "Medium", "risk_factors": ["Age 50.0 (high risk)"]}, {"employee_id": "E003", "risk_score": 18, "risk_level": "Low", "risk_factors": []}, {"employee_id": "E004", "risk_score": 30, "risk_level": "Medium", "risk_factors": ["Has chronic condition"]}, {"employee_id": "E005", "risk_score": 43, "risk_level": "Medium", "risk_factors": ["Has chronic condition"]}, {"employee_id": "E006", "risk_score": 23, "risk_level": "Low", "risk_factors": []}, {"employee_id": "E007", "risk_score": 18, "risk_level": "Low", "risk_factors": []}, {"employee_id": "E008", "risk_score": 53, "risk_level": "Medium", "risk_factors": ["Health condition: Fair", "Has chronic condition"]}, {"employee_id": "E009", "risk_score": 15, "risk_level": "Low", "risk_factors": []}, {"employee_id": "E010", "risk_score": 15, "risk_level": "Low", "risk_factors": []}]}, "data_quality_metrics": {"overall_completeness": {"total_cells": 1670, "missing_cells": "1094", "completeness_percentage": 34.49}, "column_completeness": {"employee_id": {"missing_count": "0", "completeness_percentage": 100.0}, "record_type": {"missing_count": "0", "completeness_percentage": 100.0}, "name": {"missing_count": "0", "completeness_percentage": 100.0}, "relationship": {"missing_count": "0", "completeness_percentage": 100.0}, "dob": {"missing_count": "0", "completeness_percentage": 100.0}, "gender": {"missing_count": "0", "completeness_percentage": 100.0}, "address1": {"missing_count": "0", "completeness_percentage": 100.0}, "city": {"missing_count": "0", "completeness_percentage": 100.0}, "state": {"missing_count": "0", "completeness_percentage": 100.0}, "zipcode": {"missing_count": "0", "completeness_percentage": 100.0}, "marital_status": {"missing_count": "0", "completeness_percentage": 100.0}, "medical_plan": {"missing_count": "0", "completeness_percentage": 100.0}, "dental_plan": {"missing_count": "0", "completeness_percentage": 100.0}, "vision_plan": {"missing_count": "0", "completeness_percentage": 100.0}, "ssn": {"missing_count": "0", "completeness_percentage": 100.0}, "dept_1": {"missing_count": "2", "completeness_percentage": 80.0}, "relationship_type_1": {"missing_count": "2", "completeness_percentage": 80.0}, "dept_1_dob": {"missing_count": "2", "completeness_percentage": 80.0}, "dept_1_gender": {"missing_count": "2", "completeness_percentage": 80.0}, "employee_address1": {"missing_count": "2", "completeness_percentage": 80.0}, "employee_city": {"missing_count": "2", "completeness_percentage": 80.0}, "employee_state": {"missing_count": "2", "completeness_percentage": 80.0}, "employee_zipcode": {"missing_count": "2", "completeness_percentage": 80.0}, "employee_marital_status": {"missing_count": "2", "completeness_percentage": 80.0}, "dept_1_medical_plan": {"missing_count": "2", "completeness_percentage": 80.0}, "dept_1_dental_plan": {"missing_count": "2", "completeness_percentage": 80.0}, "dept_1_vision_plan": {"missing_count": "2", "completeness_percentage": 80.0}, "dept_1_ssn": {"missing_count": "2", "completeness_percentage": 80.0}, "dept_2": {"missing_count": "3", "completeness_percentage": 70.0}, "relationship_type_2": {"missing_count": "3", "completeness_percentage": 70.0}, "dept_2_dob": {"missing_count": "3", "completeness_percentage": 70.0}, "dept_2_gender": {"missing_count": "3", "completeness_percentage": 70.0}, "dept_2_medical_plan": {"missing_count": "3", "completeness_percentage": 70.0}, "dept_2_dental_plan": {"missing_count": "3", "completeness_percentage": 70.0}, "dept_2_vision_plan": {"missing_count": "3", "completeness_percentage": 70.0}, "dept_2_ssn": {"missing_count": "3", "completeness_percentage": 70.0}, "dept_3": {"missing_count": "5", "completeness_percentage": 50.0}, "relationship_type_3": {"missing_count": "5", "completeness_percentage": 50.0}, "dept_3_dob": {"missing_count": "5", "completeness_percentage": 50.0}, "dept_3_gender": {"missing_count": "5", "completeness_percentage": 50.0}, "dept_3_medical_plan": {"missing_count": "5", "completeness_percentage": 50.0}, "dept_3_dental_plan": {"missing_count": "5", "completeness_percentage": 50.0}, "dept_3_vision_plan": {"missing_count": "5", "completeness_percentage": 50.0}, "dept_3_ssn": {"missing_count": "5", "completeness_percentage": 50.0}, "dept_4": {"missing_count": "7", "completeness_percentage": 30.0}, "relationship_type_4": {"missing_count": "7", "completeness_percentage": 30.0}, "dept_4_dob": {"missing_count": "7", "completeness_percentage": 30.0}, "dept_4_gender": {"missing_count": "7", "completeness_percentage": 30.0}, "dept_4_medical_plan": {"missing_count": "7", "completeness_percentage": 30.0}, "dept_4_dental_plan": {"missing_count": "7", "completeness_percentage": 30.0}, "dept_4_vision_plan": {"missing_count": "7", "completeness_percentage": 30.0}, "dept_4_ssn": {"missing_count": "7", "completeness_percentage": 30.0}, "dept_count": {"missing_count": "0", "completeness_percentage": 100.0}, "dept_5": {"missing_count": "9", "completeness_percentage": 10.0}, "relationship_type_5": {"missing_count": "9", "completeness_percentage": 10.0}, "dept_5_dob": {"missing_count": "9", "completeness_percentage": 10.0}, "dept_5_gender": {"missing_count": "9", "completeness_percentage": 10.0}, "dept_5_medical_plan": {"missing_count": "9", "completeness_percentage": 10.0}, "dept_5_dental_plan": {"missing_count": "9", "completeness_percentage": 10.0}, "dept_5_vision_plan": {"missing_count": "9", "completeness_percentage": 10.0}, "dept_5_ssn": {"missing_count": "9", "completeness_percentage": 10.0}, "age": {"missing_count": "0", "completeness_percentage": 100.0}, "dept_1_age": {"missing_count": "2", "completeness_percentage": 80.0}, "dept_2_age": {"missing_count": "3", "completeness_percentage": 70.0}, "dept_3_age": {"missing_count": "5", "completeness_percentage": 50.0}, "dept_4_age": {"missing_count": "7", "completeness_percentage": 30.0}, "dept_5_age": {"missing_count": "9", "completeness_percentage": 10.0}, "first_name": {"missing_count": "10", "completeness_percentage": 0.0}, "middle_name": {"missing_count": "10", "completeness_percentage": 0.0}, "last_name": {"missing_count": "10", "completeness_percentage": 0.0}, "address2": {"missing_count": "10", "completeness_percentage": 0.0}, "salary": {"missing_count": "10", "completeness_percentage": 0.0}, "employment_type": {"missing_count": "0", "completeness_percentage": 100.0}, "employee_class": {"missing_count": "10", "completeness_percentage": 0.0}, "department": {"missing_count": "0", "completeness_percentage": 100.0}, "hire_date": {"missing_count": "10", "completeness_percentage": 0.0}, "tobacco_use": {"missing_count": "0", "completeness_percentage": 100.0}, "pregnancy_status": {"missing_count": "0", "completeness_percentage": 100.0}, "life_plan": {"missing_count": "10", "completeness_percentage": 0.0}, "add_plan": {"missing_count": "10", "completeness_percentage": 0.0}, "coverage_tier": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_6": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_6_dob": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_6_age": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_6_gender": {"missing_count": "10", "completeness_percentage": 0.0}, "relationship_type_6": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_7": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_7_dob": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_7_age": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_7_gender": {"missing_count": "10", "completeness_percentage": 0.0}, "relationship_type_7": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_8": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_8_dob": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_8_age": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_8_gender": {"missing_count": "10", "completeness_percentage": 0.0}, "relationship_type_8": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_9": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_9_dob": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_9_age": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_9_gender": {"missing_count": "10", "completeness_percentage": 0.0}, "relationship_type_9": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_10": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_10_dob": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_10_age": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_10_gender": {"missing_count": "10", "completeness_percentage": 0.0}, "relationship_type_10": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_11": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_11_dob": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_11_age": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_11_gender": {"missing_count": "10", "completeness_percentage": 0.0}, "relationship_type_11": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_12": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_12_dob": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_12_age": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_12_gender": {"missing_count": "10", "completeness_percentage": 0.0}, "relationship_type_12": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_13": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_13_dob": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_13_age": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_13_gender": {"missing_count": "10", "completeness_percentage": 0.0}, "relationship_type_13": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_14": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_14_dob": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_14_age": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_14_gender": {"missing_count": "10", "completeness_percentage": 0.0}, "relationship_type_14": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_15": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_15_dob": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_15_age": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_15_gender": {"missing_count": "10", "completeness_percentage": 0.0}, "relationship_type_15": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_16": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_16_dob": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_16_age": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_16_gender": {"missing_count": "10", "completeness_percentage": 0.0}, "relationship_type_16": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_17": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_17_dob": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_17_age": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_17_gender": {"missing_count": "10", "completeness_percentage": 0.0}, "relationship_type_17": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_18": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_18_dob": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_18_age": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_18_gender": {"missing_count": "10", "completeness_percentage": 0.0}, "relationship_type_18": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_19": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_19_dob": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_19_age": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_19_gender": {"missing_count": "10", "completeness_percentage": 0.0}, "relationship_type_19": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_20": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_20_dob": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_20_age": {"missing_count": "10", "completeness_percentage": 0.0}, "dept_20_gender": {"missing_count": "10", "completeness_percentage": 0.0}, "relationship_type_20": {"missing_count": "10", "completeness_percentage": 0.0}, "job_type": {"missing_count": "0", "completeness_percentage": 100.0}, "region": {"missing_count": "0", "completeness_percentage": 100.0}, "health_condition": {"missing_count": "0", "completeness_percentage": 100.0}, "chronic_condition": {"missing_count": "0", "completeness_percentage": 100.0}, "prescription_use": {"missing_count": "0", "completeness_percentage": 100.0}, "income_tier": {"missing_count": "0", "completeness_percentage": 100.0}, "risk_tolerance": {"missing_count": "0", "completeness_percentage": 100.0}, "lifestyle": {"missing_count": "0", "completeness_percentage": 100.0}, "travel_frequency": {"missing_count": "0", "completeness_percentage": 100.0}, "hsa_familiarity": {"missing_count": "0", "completeness_percentage": 100.0}, "mental_health_needs": {"missing_count": "0", "completeness_percentage": 100.0}}, "critical_field_completeness": {"name": {"missing_count": "0", "completeness_percentage": 100.0}, "gender": {"missing_count": "0", "completeness_percentage": 100.0}, "zipcode": {"missing_count": "0", "completeness_percentage": 100.0}, "marital_status": {"missing_count": "0", "completeness_percentage": 100.0}}}}, "feature_validation": {"is_valid": true, "missing_features": [], "feature_completeness": {"age": {"present": true, "missing_values": "0", "completion_rate": 100.0}, "gender": {"present": true, "missing_values": "0", "completion_rate": 100.0}, "marital_status": {"present": true, "missing_values": "0", "completion_rate": 100.0}, "dept_count": {"present": true, "missing_values": "0", "completion_rate": 100.0}, "job_type": {"present": true, "missing_values": "0", "completion_rate": 100.0}, "employment_type": {"present": true, "missing_values": "0", "completion_rate": 100.0}, "department": {"present": true, "missing_values": "0", "completion_rate": 100.0}, "region": {"present": true, "missing_values": "0", "completion_rate": 100.0}, "health_condition": {"present": true, "missing_values": "0", "completion_rate": 100.0}, "chronic_condition": {"present": true, "missing_values": "0", "completion_rate": 100.0}, "prescription_use": {"present": true, "missing_values": "0", "completion_rate": 100.0}, "income_tier": {"present": true, "missing_values": "0", "completion_rate": 100.0}, "risk_tolerance": {"present": true, "missing_values": "0", "completion_rate": 100.0}, "lifestyle": {"present": true, "missing_values": "0", "completion_rate": 100.0}, "travel_frequency": {"present": true, "missing_values": "0", "completion_rate": 100.0}, "hsa_familiarity": {"present": true, "missing_values": "0", "completion_rate": 100.0}, "pregnancy_status": {"present": true, "missing_values": "0", "completion_rate": 100.0}, "tobacco_use": {"present": true, "missing_values": "0", "completion_rate": 100.0}, "mental_health_needs": {"present": true, "missing_values": "0", "completion_rate": 100.0}}, "overall_completion_rate": 100.0, "ready_for_prediction": "True"}, "prediction_method": "ml_models", "model_predictions": {"plan_types": {"total_predictions": 10, "average_confidence": "0.6844863", "unique_plans": 4}}, "prediction_summary": {"total_employees": 10, "plan_type_distribution": {"successful_predictions": {"HDHP + HSA": 4, "PPO": 2, "POS": 2, "HMO": 2}, "failed_predictions": "0", "success_rate": 100.0, "total_predicted": "10"}, "benefits_distribution": {}, "confidence_metrics": {"plan_confidence": {"mean": "0.684", "min": "0.498", "max": "0.868", "count": 10}}, "prediction_success_rates": {}, "top_3_plan_probabilities": ["E001: [0.7154574, 0.14241491, 0.*********]", "E002: [0.719042, 0.25063118, 0.015628254]", "E003: [0.4981735, 0.17083637, 0.16148324]", "E004: [0.7343685, 0.21616599, 0.029586911]", "E005: [0.50470585, 0.29781798, 0.12193069]", "E006: [0.7596685, 0.13966534, 0.047967672]", "E007: [0.6143533, 0.31570056, 0.*********]", "E008: [0.790921, 0.********, 0.*********]", "E009: [0.8683186, 0.********, 0.*********]", "E010: [0.6398547, 0.*********, 0.*********]"]}}}}}