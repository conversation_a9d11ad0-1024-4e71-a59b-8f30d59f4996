{"file_info": {"original_filename": "row_grouping_comprehensive_test.csv", "processing_timestamp": "2025-07-14 23:22:40.123942", "total_processing_time_seconds": 21.862322568893433}, "final_response": {"success": true, "data": {"enriched_data_csv": "employee_id,gender,dob,address1,city,state,zipcode,marital_status,relationship,salary,medical_plan,dental_plan,vision_plan,dept_1,dept_1_dob,dept_1_gender,relationship_type_1,dept_1_medical_plan,dept_1_dental_plan,dept_1_vision_plan,dept_2,dept_2_dob,dept_2_gender,relationship_type_2,dept_2_medical_plan,dept_2_dental_plan,dept_2_vision_plan,dept_count,dept_3,dept_3_dob,dept_3_gender,relationship_type_3,dept_3_medical_plan,dept_3_dental_plan,dept_3_vision_plan,name,age,dept_1_age,dept_2_age,dept_3_age,first_name,middle_name,last_name,address2,record_type,employment_type,employee_class,department,hire_date,tobacco_use,pregnancy_status,life_plan,add_plan,coverage_tier,ssn,dept_4,dept_4_dob,dept_4_age,dept_4_gender,relationship_type_4,dept_5,dept_5_dob,dept_5_age,dept_5_gender,relationship_type_5,job_type,region,health_condition,chronic_condition,prescription_use,income_tier,risk_tolerance,lifestyle,travel_frequency,hsa_familiarity,mental_health_needs,predicted_plan_type,plan_confidence,plan_reason,top_3_plans,top_3_plan_confidences\r\nE001,Male,1980-01-15,123 Main St,Anytown,CA,90210,Married,Employee,65000,Y,Y,Y,Jane Smith,1982-05-20,Female,Spouse,PPO,Y,Y,Mike Smith,2010-09-12,Male,Child,PPO,Y,Y,2,,,,,,,,John Smith,45,43.0,14.0,,,,,,,Full-Time,,Finance,,N,N,,,,,,,,,,,,,,,Desk,Urban,Good,N,None,Medium ($50K–$100K),Medium,Moderate,Rare,N,N,POS,0.84217364,\"Medium income, good health - PPO for balanced cost and flexibility.\",\"['POS', 'HMO', 'PPO']\",\"[0.84217364, 0.*********, 0.04423529]\"\r\nE002,Female,1975-03-10,456 Oak Ave,Somewhere,TX,75001,Not Married,Employee,52000,Y,N,Y,Bob Brown,2005-07-25,Male,Child,HMO,N,Y,,,,,,,,1,,,,,,,,Alice Brown,50,19.0,,,,,,,,Full-Time,,Finance,,N,N,,,,,,,,,,,,,,,Desk,Urban,Good,N,None,Medium ($50K–$100K),Low,Moderate,Occasional,N,N,POS,0.8839468,\"Medium income, good health - PPO for balanced cost and flexibility.\",\"['POS', 'PPO', 'HMO']\",\"[0.8839468, 0.06380212, 0.03698681]\"\r\nE003,Male,1990-06-30,789 Pine Rd,Elsewhere,FL,33101,Married,Employee,78500,Y,Y,Y,Diana Davis,1992-08-15,Female,Spouse,HDHP,Y,Y,Evan Davis,2015-12-01,Male,Child,HDHP,Y,Y,3,Fiona Davis,2018-03-22,Female,Child,HDHP,Y,Y,Charlie Davis,35,32.0,9.0,7.0,,,,,,Full-Time,,Finance,,N,N,,,,,,,,,,,,,,,Desk,Urban,Good,N,None,High (>$100K),High,Active,Occasional,Y,Y,HDHP + HSA,0.8932355,\"High income, good health, HSA familiar, no chronic conditions - HDHP + HSA for tax benefits.\",\"['HDHP + HSA', 'HDHP', 'POS']\",\"[0.8932355, 0.*********, 0.*********]\"\r\nE008,Male,1979-10-17,258 Spruce St,Newplace,CO,80001,Not Married,Employee,58000,Y,Y,Y,Alex Martinez,2007-12-25,Male,Child,PPO,Y,Y,Zoe Martinez,2010-03-14,Female,Child,PPO,Y,Y,2,,,,,,,,Quinn Martinez,45,17.0,15.0,,,,,,,Full-Time,,Engineering,,N,N,,,,,,,,,,,,,,,Desk,Suburban,Good,N,None,Medium ($50K–$100K),Low,Moderate,Occasional,Y,N,HDHP + HSA,0.81601024,\"Medium income, good health - PPO for balanced cost and flexibility.\",\"['HDHP + HSA', 'POS', 'PPO']\",\"[0.81601024, 0.15540159, 0.*********]\"\r\nE009,Female,1981-03-24,369 Willow Way,Lastplace,NV,89001,Married,Employee,67000,Y,Y,Y,Carlos Garcia,1979-07-08,Male,Spouse,HMO,Y,Y,Maya Garcia,2009-11-30,Female,Child,HMO,Y,Y,2,,,,,,,,Tina Garcia,44,46.0,15.0,,,,,,,Full-Time,,Sales,,Y,N,,,,,,,,,,,,,,,Desk,Suburban,Good,N,None,Medium ($50K–$100K),Medium,Active,Occasional,N,N,POS,0.8947638,\"Medium income, good health - PPO for balanced cost and flexibility.\",\"['POS', 'PPO', 'HMO']\",\"[0.8947638, 0.047571197, 0.038102023]\"\r\n,Female,1985-11-05,321 Elm St,Nowhere,NY,10001,Not Married,Employee,45000,Y,Y,N,Henry Wilson,2012-04-18,Male,Child,EPO,Y,N,,,,,,,,1,,,,,,,,Grace Wilson,39,13.0,,,,,,,,Part-Time,,Finance,,N,N,,,,,,,,,,,,,,,Desk,Urban,Good,N,None,Medium ($50K–$100K),Medium,Moderate,Rare,N,N,POS,0.80953366,\"Medium income, good health - PPO for balanced cost and flexibility.\",\"['POS', 'HMO', 'HDHP']\",\"[0.80953366, 0.06405259, 0.058986124]\"\r\n,Male,1978-02-28,654 Maple Dr,Anywhere,WA,98001,Married,Employee,95000,Y,Y,Y,Lisa Johnson,1980-11-12,Female,Spouse,PPO,Y,Y,Tom Johnson,2008-06-15,Male,Child,PPO,Y,Y,3,Sara Johnson,2011-09-03,Female,Child,PPO,Y,Y,Isaac Johnson,47,44.0,17.0,13.0,,,,,,Part-Time,,Information Technology,,N,N,,,,,,,,,,,,,,,Desk,Urban,Fair,N,Occasional,High (>$100K),Low,Active,Rare,Y,N,PPO,0.4905445,Middle age with dependents - PPO for family coverage.,\"['PPO', 'Indemnity', 'HDHP + HSA']\",\"[0.4905445, 0.2192529, 0.14927131]\"\r\n,Male,1983-07-12,987 Cedar Ln,Someplace,OR,97001,Not Married,Employee,38000,Y,N,N,,,,,,,,,,,,,,,0,,,,,,,,Michael Anderson,42,,,,,,,,,Full-Time,,Sales,,N,N,,,,,,,,,,,,,,,Desk,Suburban,Excellent,N,Occasional,Low (<$50K),Low,Active,Frequent,N,N,HMO,0.52143735,Middle age with dependents - PPO for family coverage.,\"['HMO', 'PPO', 'POS']\",\"[0.52143735, 0.20841065, 0.16159827]\"\r\n,Female,1987-12-03,147 Birch Ave,Othertown,AZ,85001,Married,Employee,72000,Y,Y,Y,Paul Taylor,1985-04-20,Male,Spouse,HDHP,Y,Y,Emma Taylor,2013-08-10,Female,Child,HDHP,Y,Y,2,,,,,,,,Nancy Taylor,37,40.0,11.0,,,,,,,Full-Time,,Engineering,,N,N,,,,,,,,,,,,,,,Desk,Urban,Good,N,Occasional,Medium ($50K–$100K),Medium,Moderate,Occasional,N,N,POS,0.81460196,\"Medium income, good health - PPO for balanced cost and flexibility.\",\"['POS', 'HMO', 'HDHP']\",\"[0.81460196, 0.087325715, 0.058402594]\"\r\n,Male,1986-09-09,,Finaltown,UT,84001,Not Married,Employee,41000,Y,N,N,,,,,,,,,,,,,,,0,,,,,,,,Xavier Rodriguez,38,,,,,,,,,Full-Time,,Sales,,N,N,,,,,,,,,,,,,,,Desk,Rural,Fair,N,None,Medium ($50K–$100K),Medium,Moderate,Frequent,N,N,PPO,0.63443387,Middle age with dependents - PPO for family coverage.,\"['PPO', 'POS', 'HMO']\",\"[0.63443387, 0.2673437, 0.04532624]\"\r\n,Female,1984-12-18,,Hometown,MT,59001,Married,Employee,63000,Y,Y,Y,,,,,,,,,,,,,,,0,,,,,,,,Yvonne Lee,40,,,,,,,,,Part-Time,,Engineering,,N,N,,,,,,,,,,,,,,,Desk,Rural,Good,Y,Occasional,Medium ($50K–$100K),Low,Moderate,Occasional,N,N,PPO,0.5943701,\"Medium income, good health - PPO for balanced cost and flexibility.\",\"['PPO', 'POS', 'HMO']\",\"[0.5943701, 0.28272167, 0.1048758]\"\r\n,Male,1982-02-14,,Hometown,MT,59001,Married,Spouse,0,Y,Y,Y,,,,,,,,,,,,,,,0,,,,,,,,David Lee,43,,,,,,,,,Full-Time,,Manufacturing,,N,N,,,,,,,,,,,,,,,Desk,Rural,Excellent,N,None,Medium ($50K–$100K),Low,Moderate,Rare,N,N,POS,0.48381588,Middle age with dependents - PPO for family coverage.,\"['POS', 'PPO', 'HMO']\",\"[0.48381588, 0.22582185, 0.18016301]\"\r\n,Male,1977-04-07,,Yourtown,ID,83001,Not Married,Employee,49000,Y,Y,Y,,,,,,,,,,,,,,,0,,,,,,,,Aaron White,48,,,,,,,,,Part-Time,,Sales,,N,N,,,,,,,,,,,,,,,Desk,Rural,Good,N,None,Medium ($50K–$100K),Low,Active,Occasional,Y,N,HDHP + HSA,0.8391526,\"Medium income, good health - PPO for balanced cost and flexibility.\",\"['HDHP + HSA', 'POS', 'PPO']\",\"[0.8391526, 0.12962794, 0.014885119]\"\r\n,Female,2006-05-22,,Yourtown,ID,83001,Not Married,Child,0,Y,Y,Y,,,,,,,,,,,,,,,0,,,,,,,,Kelly White,19,,,,,,,,,Full-Time,,Information Technology,,Y,N,,,,,,,,,,,,,,,Desk,Rural,Fair,N,None,Medium ($50K–$100K),Medium,Active,Rare,Y,N,HDHP + HSA,0.8240351,Young age - PPO for flexibility and future needs.,\"['HDHP + HSA', 'PPO', 'HDHP']\",\"[0.8240351, 0.09662249, 0.067418575]\"\r\n,Male,2009-10-11,,Yourtown,ID,83001,Not Married,Child,0,Y,Y,Y,,,,,,,,,,,,,,,0,,,,,,,,Ryan White,15,,,,,,,,,Full-Time,,Information Technology,,N,N,,,,,,,,,,,,,,,Desk,Rural,Good,N,None,Low (<$50K),Medium,Moderate,Rare,N,N,HMO,0.87147534,\"Low income, young age - DHMO for basic dental coverage.\",\"['HMO', 'PPO', 'HDHP']\",\"[0.87147534, 0.084116764, 0.021577941]\"\r\n,Female,1989-08-21,,Mytown,WY,82001,Not Married,Employee,35000,Y,N,N,,,,,,,,,,,,,,,0,,,,,,,,Brooke Harris,35,,,,,,,,,Full-Time,,Finance,,Y,N,,,,,,,,,,,,,,,Desk,Rural,Fair,N,None,Low (<$50K),Low,Active,Rare,N,N,PPO,0.48606387,Middle age with dependents - PPO for family coverage.,\"['PPO', 'HMO', 'Indemnity']\",\"[0.48606387, 0.43464848, 0.04288435]\"\r\n,Male,1982-01-26,,Ourtown,ND,58001,Married,Employee,85000,Y,Y,Y,,,,,,,,,,,,,,,0,,,,,,,,Connor Clark,43,,,,,,,,,Full-Time,,Manufacturing,,N,N,,,,,,,,,,,,,,,Desk,Rural,Excellent,N,Occasional,High (>$100K),Medium,Moderate,Rare,Y,N,PPO,0.30643559,\"High income, good health, HSA familiar, no chronic conditions - HDHP + HSA for tax benefits.\",\"['PPO', 'Indemnity', 'POS']\",\"[0.30643559, 0.2207923, 0.18094002]\"\r\n,Female,1984-06-18,,Ourtown,ND,58001,Married,Spouse,0,Y,Y,Y,,,,,,,,,,,,,,,0,,,,,,,,Rachel Clark,41,,,,,,,,,Full-Time,,Engineering,,N,N,,,,,,,,,,,,,,,Desk,Rural,Good,N,None,Medium ($50K–$100K),Low,Active,Occasional,N,N,POS,0.61644197,\"Medium income, good health - PPO for balanced cost and flexibility.\",\"['POS', 'HMO', 'PPO']\",\"[0.61644197, 0.18801647, 0.14761715]\"\r\n,Male,2012-01-05,,Ourtown,ND,58001,Not Married,Child,0,Y,Y,Y,,,,,,,,,,,,,,,0,,,,,,,,Jake Clark,13,,,,,,,,,Full-Time,,Sales,,N,N,,,,,,,,,,,,,,,Desk,Rural,Good,Y,Regular,Medium ($50K–$100K),Low,Moderate,Occasional,Y,N,HDHP + HSA,0.8749703,\"Medium income, good health - PPO for balanced cost and flexibility.\",\"['HDHP + HSA', 'PPO', 'HDHP']\",\"[0.8749703, 0.09056778, 0.*********]\"\r\n,Female,2014-07-12,,Ourtown,ND,58001,Not Married,Child,0,Y,Y,Y,,,,,,,,,,,,,,,0,,,,,,,,Lily Clark,11,,,,,,,,,Full-Time,,Manufacturing,,N,N,,,,,,,,,,,,,,,Desk,Rural,Good,N,Occasional,Low (<$50K),Medium,Moderate,Occasional,N,N,HMO,0.8401945,\"Low income, young age - DHMO for basic dental coverage.\",\"['HMO', 'PPO', 'POS']\",\"[0.8401945, 0.08714539, 0.029943235]\"\r\n,Female,1986-05-13,,Theirtown,SD,57001,Not Married,Employee,44000,Y,Y,Y,,,,,,,,,,,,,,,0,,,,,,,,Destiny Lewis,39,,,,,,,,,Full-Time,,Manufacturing,,N,N,,,,,,,,,,,,,,,Desk,Rural,Excellent,Y,Occasional,Medium ($50K–$100K),Medium,Active,Occasional,Y,N,PPO,0.5810011,Middle age with dependents - PPO for family coverage.,\"['PPO', 'HDHP', 'HDHP + HSA']\",\"[0.5810011, 0.14648435, 0.12520333]\"\r\n,Male,2011-09-28,,Theirtown,SD,57001,Not Married,Child,0,Y,Y,Y,,,,,,,,,,,,,,,0,,,,,,,,Noah Lewis,13,,,,,,,,,Part-Time,,Engineering,,N,N,,,,,,,,,,,,,,,Desk,Rural,Good,N,None,Medium ($50K–$100K),Low,Moderate,Occasional,Y,Y,HDHP + HSA,0.78862435,\"Medium income, good health - PPO for balanced cost and flexibility.\",\"['HDHP + HSA', 'HDHP', 'POS']\",\"[0.78862435, 0.18126504, 0.010344903]\"\r\n", "comprehensive_statistics": {"basic_demographics": {"age_statistics": {"average_age": 35.54545454545455, "median_age": 39.5, "age_range": {"min": 11, "max": 50}, "age_distribution": {"18-30": 1, "31-45": 14, "46-60": 3, "60+": 0}}, "gender_composition": {"counts": {"Male": 12, "Female": 10}, "percentages": {"Male": 54.55, "Female": 45.45}}, "marital_status_distribution": {"counts": {"Not Married": 13, "Married": 9}, "percentages": {"Not Married": 59.09, "Married": 40.91}}}, "dependent_analysis": {"dependent_count_distribution": {"average_dependents": 0.7272727272727273, "median_dependents": 0.0, "distribution": {"0": 14, "2": 4, "1": 2, "3": 2}, "employees_with_dependents": 8, "percentage_with_dependents": 36.36}, "dependent_age_analysis": {"total_dependents_found": 16, "average_dependent_age": 22.19, "median_dependent_age": 16.0, "dependents_over_26_as_children": 0, "age_distribution": {"0-5": 0, "6-12": 3, "13-18": 7, "19-26": 1, "26+": 5}}}, "employment_demographics": {"department_distribution": {"counts": {"Finance": 5, "Engineering": 5, "Sales": 5, "Manufacturing": 4, "Information Technology": 3}, "percentages": {"Finance": 22.73, "Engineering": 22.73, "Sales": 22.73, "Manufacturing": 18.18, "Information Technology": 13.64}}, "employment_type_distribution": {"counts": {"Full-Time": 17, "Part-Time": 5}, "percentages": {"Full-Time": 77.27, "Part-Time": 22.73}}, "job_type_distribution": {"counts": {"Desk": 22}, "percentages": {"Desk": 100.0}}, "employee_class_distribution": {"counts": {}, "percentages": {}}, "salary_analysis": {"average_salary": 40340.91, "median_salary": 44500.0, "salary_range": {"min": "0", "max": "95000"}, "salary_distribution": {"under_40k": 9, "40k_75k": 10, "75k_100k": 3, "over_100k": 0}}}, "health_and_lifestyle": {"health_condition_distribution": {"counts": {"Good": 14, "Fair": 4, "Excellent": 4}, "percentages": {"Good": 63.64, "Fair": 18.18, "Excellent": 18.18}}, "chronic_condition_distribution": {"counts": {"N": 19, "Y": 3}, "percentages": {"N": 86.36, "Y": 13.64}}, "tobacco_use_distribution": {"counts": {"N": 19, "Y": 3}, "percentages": {"N": 86.36, "Y": 13.64}}, "lifestyle_distribution": {"counts": {"Moderate": 13, "Active": 9}, "percentages": {"Moderate": 59.09, "Active": 40.91}}, "prescription_use_distribution": {"counts": {"None": 14, "Occasional": 7, "Regular": 1}, "percentages": {"None": 63.64, "Occasional": 31.82, "Regular": 4.55}}}, "coverage_analysis": {"coverage_tier_distribution": {"counts": {}, "percentages": {}}, "coverage_tier_by_family_size": {}, "medical_plan_distribution": {"counts": {"Y": 22}, "percentages": {"Y": 100.0}}, "dental_plan_distribution": {"counts": {"Y": 18, "N": 4}, "percentages": {"Y": 81.82, "N": 18.18}}, "vision_plan_distribution": {"counts": {"Y": 18, "N": 4}, "percentages": {"Y": 81.82, "N": 18.18}}, "life_plan_distribution": {"counts": {}, "percentages": {}}}, "geographic_distribution": {"state_distribution": {"counts": {"ND": 4, "ID": 3, "MT": 2, "SD": 2, "CA": 1, "TX": 1, "FL": 1, "CO": 1, "NV": 1, "NY": 1, "WA": 1, "OR": 1, "AZ": 1, "UT": 1, "WY": 1}, "percentages": {"ND": 18.18, "ID": 13.64, "MT": 9.09, "SD": 9.09, "CA": 4.55, "TX": 4.55, "FL": 4.55, "CO": 4.55, "NV": 4.55, "NY": 4.55, "WA": 4.55, "OR": 4.55, "AZ": 4.55, "UT": 4.55, "WY": 4.55}}, "region_distribution": {"counts": {"Rural": 13, "Urban": 6, "Suburban": 3}, "percentages": {"Rural": 59.09, "Urban": 27.27, "Suburban": 13.64}}, "top_cities": {"counts": {"Ourtown": 4, "Yourtown": 3, "Hometown": 2, "Theirtown": 2, "Anytown": 1, "Somewhere": 1, "Elsewhere": 1, "Newplace": 1, "Lastplace": 1, "Nowhere": 1}, "percentages": {"Ourtown": 18.18, "Yourtown": 13.64, "Hometown": 9.09, "Theirtown": 9.09, "Anytown": 4.55, "Somewhere": 4.55, "Elsewhere": 4.55, "Newplace": 4.55, "Lastplace": 4.55, "Nowhere": 4.55}}}, "financial_demographics": {"income_tier_distribution": {"counts": {"Medium ($50K–$100K)": 15, "Low (<$50K)": 4, "High (>$100K)": 3}, "percentages": {"Medium ($50K–$100K)": 68.18, "Low (<$50K)": 18.18, "High (>$100K)": 13.64}}, "risk_tolerance_distribution": {"counts": {"Low": 11, "Medium": 10, "High": 1}, "percentages": {"Low": 50.0, "Medium": 45.45, "High": 4.55}}, "hsa_familiarity_distribution": {"counts": {"N": 13, "Y": 9}, "percentages": {"N": 59.09, "Y": 40.91}}}, "risk_assessment": {"group_risk_score": 24.27, "group_risk_level": "Low", "risk_distribution": {"low_risk": 14, "medium_risk": 8, "high_risk": 0}, "risk_statistics": {"min_risk_score": 13, "max_risk_score": 43, "median_risk_score": 21.5, "std_risk_score": 9.07}, "top_risk_factors": {"Poor Health Condition": 4, "Tobacco Use": 3, "Chronic Conditions": 3, "Advanced Age": 1}, "individual_risks": [{"employee_id": "E001", "risk_score": 23, "risk_level": "Low", "risk_factors": []}, {"employee_id": "E002", "risk_score": 33, "risk_level": "Medium", "risk_factors": ["Age 50 (high risk)"]}, {"employee_id": "E003", "risk_score": 15, "risk_level": "Low", "risk_factors": []}, {"employee_id": "E008", "risk_score": 23, "risk_level": "Low", "risk_factors": []}, {"employee_id": "E009", "risk_score": 35, "risk_level": "Medium", "risk_factors": ["Tobacco user"]}, {"employee_id": NaN, "risk_score": 18, "risk_level": "Low", "risk_factors": []}, {"employee_id": NaN, "risk_score": 30, "risk_level": "Medium", "risk_factors": ["Health condition: Fair"]}, {"employee_id": NaN, "risk_score": 15, "risk_level": "Low", "risk_factors": []}, {"employee_id": NaN, "risk_score": 18, "risk_level": "Low", "risk_factors": []}, {"employee_id": NaN, "risk_score": 28, "risk_level": "Low", "risk_factors": ["Health condition: Fair"]}]}, "data_quality_metrics": {"overall_completeness": {"total_cells": 1672, "missing_cells": "892", "completeness_percentage": 46.65}, "column_completeness": {"employee_id": {"missing_count": "17", "completeness_percentage": 22.73}, "gender": {"missing_count": "0", "completeness_percentage": 100.0}, "dob": {"missing_count": "0", "completeness_percentage": 100.0}, "address1": {"missing_count": "13", "completeness_percentage": 40.91}, "city": {"missing_count": "0", "completeness_percentage": 100.0}, "state": {"missing_count": "0", "completeness_percentage": 100.0}, "zipcode": {"missing_count": "0", "completeness_percentage": 100.0}, "marital_status": {"missing_count": "0", "completeness_percentage": 100.0}, "relationship": {"missing_count": "0", "completeness_percentage": 100.0}, "salary": {"missing_count": "0", "completeness_percentage": 100.0}, "medical_plan": {"missing_count": "0", "completeness_percentage": 100.0}, "dental_plan": {"missing_count": "0", "completeness_percentage": 100.0}, "vision_plan": {"missing_count": "0", "completeness_percentage": 100.0}, "dept_1": {"missing_count": "14", "completeness_percentage": 36.36}, "dept_1_dob": {"missing_count": "14", "completeness_percentage": 36.36}, "dept_1_gender": {"missing_count": "14", "completeness_percentage": 36.36}, "relationship_type_1": {"missing_count": "14", "completeness_percentage": 36.36}, "dept_1_medical_plan": {"missing_count": "14", "completeness_percentage": 36.36}, "dept_1_dental_plan": {"missing_count": "14", "completeness_percentage": 36.36}, "dept_1_vision_plan": {"missing_count": "14", "completeness_percentage": 36.36}, "dept_2": {"missing_count": "16", "completeness_percentage": 27.27}, "dept_2_dob": {"missing_count": "16", "completeness_percentage": 27.27}, "dept_2_gender": {"missing_count": "16", "completeness_percentage": 27.27}, "relationship_type_2": {"missing_count": "16", "completeness_percentage": 27.27}, "dept_2_medical_plan": {"missing_count": "16", "completeness_percentage": 27.27}, "dept_2_dental_plan": {"missing_count": "16", "completeness_percentage": 27.27}, "dept_2_vision_plan": {"missing_count": "16", "completeness_percentage": 27.27}, "dept_count": {"missing_count": "0", "completeness_percentage": 100.0}, "dept_3": {"missing_count": "20", "completeness_percentage": 9.09}, "dept_3_dob": {"missing_count": "20", "completeness_percentage": 9.09}, "dept_3_gender": {"missing_count": "20", "completeness_percentage": 9.09}, "relationship_type_3": {"missing_count": "20", "completeness_percentage": 9.09}, "dept_3_medical_plan": {"missing_count": "20", "completeness_percentage": 9.09}, "dept_3_dental_plan": {"missing_count": "20", "completeness_percentage": 9.09}, "dept_3_vision_plan": {"missing_count": "20", "completeness_percentage": 9.09}, "name": {"missing_count": "0", "completeness_percentage": 100.0}, "age": {"missing_count": "0", "completeness_percentage": 100.0}, "dept_1_age": {"missing_count": "14", "completeness_percentage": 36.36}, "dept_2_age": {"missing_count": "16", "completeness_percentage": 27.27}, "dept_3_age": {"missing_count": "20", "completeness_percentage": 9.09}, "first_name": {"missing_count": "22", "completeness_percentage": 0.0}, "middle_name": {"missing_count": "22", "completeness_percentage": 0.0}, "last_name": {"missing_count": "22", "completeness_percentage": 0.0}, "address2": {"missing_count": "22", "completeness_percentage": 0.0}, "record_type": {"missing_count": "22", "completeness_percentage": 0.0}, "employment_type": {"missing_count": "0", "completeness_percentage": 100.0}, "employee_class": {"missing_count": "22", "completeness_percentage": 0.0}, "department": {"missing_count": "0", "completeness_percentage": 100.0}, "hire_date": {"missing_count": "22", "completeness_percentage": 0.0}, "tobacco_use": {"missing_count": "0", "completeness_percentage": 100.0}, "pregnancy_status": {"missing_count": "0", "completeness_percentage": 100.0}, "life_plan": {"missing_count": "22", "completeness_percentage": 0.0}, "add_plan": {"missing_count": "22", "completeness_percentage": 0.0}, "coverage_tier": {"missing_count": "22", "completeness_percentage": 0.0}, "ssn": {"missing_count": "22", "completeness_percentage": 0.0}, "dept_4": {"missing_count": "22", "completeness_percentage": 0.0}, "dept_4_dob": {"missing_count": "22", "completeness_percentage": 0.0}, "dept_4_age": {"missing_count": "22", "completeness_percentage": 0.0}, "dept_4_gender": {"missing_count": "22", "completeness_percentage": 0.0}, "relationship_type_4": {"missing_count": "22", "completeness_percentage": 0.0}, "dept_5": {"missing_count": "22", "completeness_percentage": 0.0}, "dept_5_dob": {"missing_count": "22", "completeness_percentage": 0.0}, "dept_5_age": {"missing_count": "22", "completeness_percentage": 0.0}, "dept_5_gender": {"missing_count": "22", "completeness_percentage": 0.0}, "relationship_type_5": {"missing_count": "22", "completeness_percentage": 0.0}, "job_type": {"missing_count": "0", "completeness_percentage": 100.0}, "region": {"missing_count": "0", "completeness_percentage": 100.0}, "health_condition": {"missing_count": "0", "completeness_percentage": 100.0}, "chronic_condition": {"missing_count": "0", "completeness_percentage": 100.0}, "prescription_use": {"missing_count": "0", "completeness_percentage": 100.0}, "income_tier": {"missing_count": "0", "completeness_percentage": 100.0}, "risk_tolerance": {"missing_count": "0", "completeness_percentage": 100.0}, "lifestyle": {"missing_count": "0", "completeness_percentage": 100.0}, "travel_frequency": {"missing_count": "0", "completeness_percentage": 100.0}, "hsa_familiarity": {"missing_count": "0", "completeness_percentage": 100.0}, "mental_health_needs": {"missing_count": "0", "completeness_percentage": 100.0}}, "critical_field_completeness": {"name": {"missing_count": "0", "completeness_percentage": 100.0}, "gender": {"missing_count": "0", "completeness_percentage": 100.0}, "zipcode": {"missing_count": "0", "completeness_percentage": 100.0}, "marital_status": {"missing_count": "0", "completeness_percentage": 100.0}}}}, "prediction_summary": {"total_employees": 22, "plan_type_distribution": {"successful_predictions": {"POS": 7, "HDHP + HSA": 6, "PPO": 6, "HMO": 3}, "failed_predictions": "0", "success_rate": 100.0, "total_predicted": "22"}, "benefits_distribution": {}, "confidence_metrics": {"plan_confidence": {"mean": "0.714", "min": "0.306", "max": "0.895", "count": 22}}, "prediction_success_rates": {}, "top_3_plan_probabilities": [{"employee_id": "E001", "top_3_predictions": [{"rank": 1, "plan_type": "POS", "probability": 0.842}, {"rank": 2, "plan_type": "HMO", "probability": 0.085}, {"rank": 3, "plan_type": "PPO", "probability": 0.044}]}, {"employee_id": "E002", "top_3_predictions": [{"rank": 1, "plan_type": "POS", "probability": 0.884}, {"rank": 2, "plan_type": "PPO", "probability": 0.064}, {"rank": 3, "plan_type": "HMO", "probability": 0.037}]}, {"employee_id": "E003", "top_3_predictions": [{"rank": 1, "plan_type": "HDHP + HSA", "probability": 0.893}, {"rank": 2, "plan_type": "HDHP", "probability": 0.045}, {"rank": 3, "plan_type": "POS", "probability": 0.027}]}, {"employee_id": "E008", "top_3_predictions": [{"rank": 1, "plan_type": "HDHP + HSA", "probability": 0.816}, {"rank": 2, "plan_type": "POS", "probability": 0.155}, {"rank": 3, "plan_type": "PPO", "probability": 0.013}]}, {"employee_id": "E009", "top_3_predictions": [{"rank": 1, "plan_type": "POS", "probability": 0.895}, {"rank": 2, "plan_type": "PPO", "probability": 0.048}, {"rank": 3, "plan_type": "HMO", "probability": 0.038}]}, {"employee_id": NaN, "top_3_predictions": [{"rank": 1, "plan_type": "POS", "probability": 0.81}, {"rank": 2, "plan_type": "HMO", "probability": 0.064}, {"rank": 3, "plan_type": "HDHP", "probability": 0.059}]}, {"employee_id": NaN, "top_3_predictions": [{"rank": 1, "plan_type": "PPO", "probability": 0.491}, {"rank": 2, "plan_type": "Indemnity", "probability": 0.219}, {"rank": 3, "plan_type": "HDHP + HSA", "probability": 0.149}]}, {"employee_id": NaN, "top_3_predictions": [{"rank": 1, "plan_type": "HMO", "probability": 0.521}, {"rank": 2, "plan_type": "PPO", "probability": 0.208}, {"rank": 3, "plan_type": "POS", "probability": 0.162}]}, {"employee_id": NaN, "top_3_predictions": [{"rank": 1, "plan_type": "POS", "probability": 0.815}, {"rank": 2, "plan_type": "HMO", "probability": 0.087}, {"rank": 3, "plan_type": "HDHP", "probability": 0.058}]}, {"employee_id": NaN, "top_3_predictions": [{"rank": 1, "plan_type": "PPO", "probability": 0.634}, {"rank": 2, "plan_type": "POS", "probability": 0.267}, {"rank": 3, "plan_type": "HMO", "probability": 0.045}]}, {"employee_id": NaN, "top_3_predictions": [{"rank": 1, "plan_type": "PPO", "probability": 0.594}, {"rank": 2, "plan_type": "POS", "probability": 0.283}, {"rank": 3, "plan_type": "HMO", "probability": 0.105}]}, {"employee_id": NaN, "top_3_predictions": [{"rank": 1, "plan_type": "POS", "probability": 0.484}, {"rank": 2, "plan_type": "PPO", "probability": 0.226}, {"rank": 3, "plan_type": "HMO", "probability": 0.18}]}, {"employee_id": NaN, "top_3_predictions": [{"rank": 1, "plan_type": "HDHP + HSA", "probability": 0.839}, {"rank": 2, "plan_type": "POS", "probability": 0.13}, {"rank": 3, "plan_type": "PPO", "probability": 0.015}]}, {"employee_id": NaN, "top_3_predictions": [{"rank": 1, "plan_type": "HDHP + HSA", "probability": 0.824}, {"rank": 2, "plan_type": "PPO", "probability": 0.097}, {"rank": 3, "plan_type": "HDHP", "probability": 0.067}]}, {"employee_id": NaN, "top_3_predictions": [{"rank": 1, "plan_type": "HMO", "probability": 0.871}, {"rank": 2, "plan_type": "PPO", "probability": 0.084}, {"rank": 3, "plan_type": "HDHP", "probability": 0.022}]}, {"employee_id": NaN, "top_3_predictions": [{"rank": 1, "plan_type": "PPO", "probability": 0.486}, {"rank": 2, "plan_type": "HMO", "probability": 0.435}, {"rank": 3, "plan_type": "Indemnity", "probability": 0.043}]}, {"employee_id": NaN, "top_3_predictions": [{"rank": 1, "plan_type": "PPO", "probability": 0.306}, {"rank": 2, "plan_type": "Indemnity", "probability": 0.221}, {"rank": 3, "plan_type": "POS", "probability": 0.181}]}, {"employee_id": NaN, "top_3_predictions": [{"rank": 1, "plan_type": "POS", "probability": 0.616}, {"rank": 2, "plan_type": "HMO", "probability": 0.188}, {"rank": 3, "plan_type": "PPO", "probability": 0.148}]}, {"employee_id": NaN, "top_3_predictions": [{"rank": 1, "plan_type": "HDHP + HSA", "probability": 0.875}, {"rank": 2, "plan_type": "PPO", "probability": 0.091}, {"rank": 3, "plan_type": "HDHP", "probability": 0.021}]}, {"employee_id": NaN, "top_3_predictions": [{"rank": 1, "plan_type": "HMO", "probability": 0.84}, {"rank": 2, "plan_type": "PPO", "probability": 0.087}, {"rank": 3, "plan_type": "POS", "probability": 0.03}]}, {"employee_id": NaN, "top_3_predictions": [{"rank": 1, "plan_type": "PPO", "probability": 0.581}, {"rank": 2, "plan_type": "HDHP", "probability": 0.146}, {"rank": 3, "plan_type": "HDHP + HSA", "probability": 0.125}]}, {"employee_id": NaN, "top_3_predictions": [{"rank": 1, "plan_type": "HDHP + HSA", "probability": 0.789}, {"rank": 2, "plan_type": "HDHP", "probability": 0.181}, {"rank": 3, "plan_type": "POS", "probability": 0.01}]}]}, "total_employees": 22, "total_columns": 81, "has_predictions": true, "prediction_columns": ["predicted_plan_type", "plan_confidence"]}, "metadata": {"file_processing": {"original_filename": "row_grouping_comprehensive_test.csv", "original_rows": 22, "original_columns": 15, "file_size": 3392, "processing_time": null}, "pattern_analysis": {"pattern_type": "row_based_member_level", "pattern_description": "Each person (employee + dependents) has separate row, linked by Employee_ID", "confidence": 1.1, "detection_reason": "Found 'relationship' field; Found both employee and dependent relationship values; Found duplicate employee_ids (family grouping)"}, "field_mapping": {"total_original_columns": 15, "mapped_columns": 15, "unmapped_columns": 0, "mapping_success_rate": 100.0, "unmapped_fields": []}, "data_processing": {"validation_passed": true, "validation_errors": 0, "data_quality_score": 1.0, "processing_summary": {"original_rows": 38, "original_columns": 15, "processed_rows": 22, "processed_columns": 65, "validation_passed": true, "validation_errors": 0, "error_rows": 0, "fields_processed": {"name_fields": true, "age_converted": true, "gender_standardized": true, "addresses_cleaned": true, "zipcode_validated": true}, "data_quality": {"complete_records": 22, "missing_data_rows": 0}}}, "enrichment_and_prediction": {"prediction_method": "ml_models", "enrichment_summary": {"total_employees": 22, "features_analyzed": 19, "enrichment_details": {"age": {"original_missing": "0", "final_missing": "0", "enriched_count": "0", "completion_rate": 100.0}, "gender": {"original_missing": "0", "final_missing": "0", "enriched_count": "0", "completion_rate": 100.0}, "marital_status": {"original_missing": "0", "final_missing": "0", "enriched_count": "0", "completion_rate": 100.0}, "dept_count": {"original_missing": "0", "final_missing": "0", "enriched_count": "0", "completion_rate": 100.0}, "job_type": {"original_missing": 22, "final_missing": "0", "enriched_count": "22", "completion_rate": 100.0}, "employment_type": {"original_missing": "22", "final_missing": "0", "enriched_count": "22", "completion_rate": 100.0}, "department": {"original_missing": "22", "final_missing": "0", "enriched_count": "22", "completion_rate": 100.0}, "region": {"original_missing": 22, "final_missing": "0", "enriched_count": "22", "completion_rate": 100.0}, "health_condition": {"original_missing": 22, "final_missing": "0", "enriched_count": "22", "completion_rate": 100.0}, "chronic_condition": {"original_missing": 22, "final_missing": "0", "enriched_count": "22", "completion_rate": 100.0}, "prescription_use": {"original_missing": 22, "final_missing": "0", "enriched_count": "22", "completion_rate": 100.0}, "income_tier": {"original_missing": 22, "final_missing": "0", "enriched_count": "22", "completion_rate": 100.0}, "risk_tolerance": {"original_missing": 22, "final_missing": "0", "enriched_count": "22", "completion_rate": 100.0}, "lifestyle": {"original_missing": 22, "final_missing": "0", "enriched_count": "22", "completion_rate": 100.0}, "travel_frequency": {"original_missing": 22, "final_missing": "0", "enriched_count": "22", "completion_rate": 100.0}, "hsa_familiarity": {"original_missing": 22, "final_missing": "0", "enriched_count": "22", "completion_rate": 100.0}, "pregnancy_status": {"original_missing": "22", "final_missing": "0", "enriched_count": "22", "completion_rate": 100.0}, "tobacco_use": {"original_missing": "22", "final_missing": "0", "enriched_count": "22", "completion_rate": 100.0}, "mental_health_needs": {"original_missing": 22, "final_missing": "0", "enriched_count": "22", "completion_rate": 100.0}}, "data_quality_improvement": {"total_missing_before": "330", "total_missing_after": "0", "total_enriched": "330", "overall_completion_rate": 100.0}}, "model_predictions": {"plan_types": {"total_predictions": 22, "average_confidence": "0.7139665", "unique_plans": 4}, "benefits": {"total_predictions": 22, "average_confidence": 0, "average_benefits_per_employee": 0}}, "prediction_summary": {"total_employees": 22, "plan_type_distribution": {"successful_predictions": {"POS": 7, "HDHP + HSA": 6, "PPO": 6, "HMO": 3}, "failed_predictions": "0", "success_rate": 100.0, "total_predicted": "22"}, "benefits_distribution": {}, "confidence_metrics": {"plan_confidence": {"mean": "0.714", "min": "0.306", "max": "0.895", "count": 22}}, "prediction_success_rates": {}, "top_3_plan_probabilities": [{"employee_id": "E001", "top_3_predictions": [{"rank": 1, "plan_type": "POS", "probability": 0.842}, {"rank": 2, "plan_type": "HMO", "probability": 0.085}, {"rank": 3, "plan_type": "PPO", "probability": 0.044}]}, {"employee_id": "E002", "top_3_predictions": [{"rank": 1, "plan_type": "POS", "probability": 0.884}, {"rank": 2, "plan_type": "PPO", "probability": 0.064}, {"rank": 3, "plan_type": "HMO", "probability": 0.037}]}, {"employee_id": "E003", "top_3_predictions": [{"rank": 1, "plan_type": "HDHP + HSA", "probability": 0.893}, {"rank": 2, "plan_type": "HDHP", "probability": 0.045}, {"rank": 3, "plan_type": "POS", "probability": 0.027}]}, {"employee_id": "E008", "top_3_predictions": [{"rank": 1, "plan_type": "HDHP + HSA", "probability": 0.816}, {"rank": 2, "plan_type": "POS", "probability": 0.155}, {"rank": 3, "plan_type": "PPO", "probability": 0.013}]}, {"employee_id": "E009", "top_3_predictions": [{"rank": 1, "plan_type": "POS", "probability": 0.895}, {"rank": 2, "plan_type": "PPO", "probability": 0.048}, {"rank": 3, "plan_type": "HMO", "probability": 0.038}]}, {"employee_id": NaN, "top_3_predictions": [{"rank": 1, "plan_type": "POS", "probability": 0.81}, {"rank": 2, "plan_type": "HMO", "probability": 0.064}, {"rank": 3, "plan_type": "HDHP", "probability": 0.059}]}, {"employee_id": NaN, "top_3_predictions": [{"rank": 1, "plan_type": "PPO", "probability": 0.491}, {"rank": 2, "plan_type": "Indemnity", "probability": 0.219}, {"rank": 3, "plan_type": "HDHP + HSA", "probability": 0.149}]}, {"employee_id": NaN, "top_3_predictions": [{"rank": 1, "plan_type": "HMO", "probability": 0.521}, {"rank": 2, "plan_type": "PPO", "probability": 0.208}, {"rank": 3, "plan_type": "POS", "probability": 0.162}]}, {"employee_id": NaN, "top_3_predictions": [{"rank": 1, "plan_type": "POS", "probability": 0.815}, {"rank": 2, "plan_type": "HMO", "probability": 0.087}, {"rank": 3, "plan_type": "HDHP", "probability": 0.058}]}, {"employee_id": NaN, "top_3_predictions": [{"rank": 1, "plan_type": "PPO", "probability": 0.634}, {"rank": 2, "plan_type": "POS", "probability": 0.267}, {"rank": 3, "plan_type": "HMO", "probability": 0.045}]}, {"employee_id": NaN, "top_3_predictions": [{"rank": 1, "plan_type": "PPO", "probability": 0.594}, {"rank": 2, "plan_type": "POS", "probability": 0.283}, {"rank": 3, "plan_type": "HMO", "probability": 0.105}]}, {"employee_id": NaN, "top_3_predictions": [{"rank": 1, "plan_type": "POS", "probability": 0.484}, {"rank": 2, "plan_type": "PPO", "probability": 0.226}, {"rank": 3, "plan_type": "HMO", "probability": 0.18}]}, {"employee_id": NaN, "top_3_predictions": [{"rank": 1, "plan_type": "HDHP + HSA", "probability": 0.839}, {"rank": 2, "plan_type": "POS", "probability": 0.13}, {"rank": 3, "plan_type": "PPO", "probability": 0.015}]}, {"employee_id": NaN, "top_3_predictions": [{"rank": 1, "plan_type": "HDHP + HSA", "probability": 0.824}, {"rank": 2, "plan_type": "PPO", "probability": 0.097}, {"rank": 3, "plan_type": "HDHP", "probability": 0.067}]}, {"employee_id": NaN, "top_3_predictions": [{"rank": 1, "plan_type": "HMO", "probability": 0.871}, {"rank": 2, "plan_type": "PPO", "probability": 0.084}, {"rank": 3, "plan_type": "HDHP", "probability": 0.022}]}, {"employee_id": NaN, "top_3_predictions": [{"rank": 1, "plan_type": "PPO", "probability": 0.486}, {"rank": 2, "plan_type": "HMO", "probability": 0.435}, {"rank": 3, "plan_type": "Indemnity", "probability": 0.043}]}, {"employee_id": NaN, "top_3_predictions": [{"rank": 1, "plan_type": "PPO", "probability": 0.306}, {"rank": 2, "plan_type": "Indemnity", "probability": 0.221}, {"rank": 3, "plan_type": "POS", "probability": 0.181}]}, {"employee_id": NaN, "top_3_predictions": [{"rank": 1, "plan_type": "POS", "probability": 0.616}, {"rank": 2, "plan_type": "HMO", "probability": 0.188}, {"rank": 3, "plan_type": "PPO", "probability": 0.148}]}, {"employee_id": NaN, "top_3_predictions": [{"rank": 1, "plan_type": "HDHP + HSA", "probability": 0.875}, {"rank": 2, "plan_type": "PPO", "probability": 0.091}, {"rank": 3, "plan_type": "HDHP", "probability": 0.021}]}, {"employee_id": NaN, "top_3_predictions": [{"rank": 1, "plan_type": "HMO", "probability": 0.84}, {"rank": 2, "plan_type": "PPO", "probability": 0.087}, {"rank": 3, "plan_type": "POS", "probability": 0.03}]}, {"employee_id": NaN, "top_3_predictions": [{"rank": 1, "plan_type": "PPO", "probability": 0.581}, {"rank": 2, "plan_type": "HDHP", "probability": 0.146}, {"rank": 3, "plan_type": "HDHP + HSA", "probability": 0.125}]}, {"employee_id": NaN, "top_3_predictions": [{"rank": 1, "plan_type": "HDHP + HSA", "probability": 0.789}, {"rank": 2, "plan_type": "HDHP", "probability": 0.181}, {"rank": 3, "plan_type": "POS", "probability": 0.01}]}]}, "feature_validation": {"is_valid": true, "missing_features": [], "feature_completeness": {"age": {"present": true, "missing_values": "0", "completion_rate": 100.0}, "gender": {"present": true, "missing_values": "0", "completion_rate": 100.0}, "marital_status": {"present": true, "missing_values": "0", "completion_rate": 100.0}, "dept_count": {"present": true, "missing_values": "0", "completion_rate": 100.0}, "job_type": {"present": true, "missing_values": "0", "completion_rate": 100.0}, "employment_type": {"present": true, "missing_values": "0", "completion_rate": 100.0}, "department": {"present": true, "missing_values": "0", "completion_rate": 100.0}, "region": {"present": true, "missing_values": "0", "completion_rate": 100.0}, "health_condition": {"present": true, "missing_values": "0", "completion_rate": 100.0}, "chronic_condition": {"present": true, "missing_values": "0", "completion_rate": 100.0}, "prescription_use": {"present": true, "missing_values": "0", "completion_rate": 100.0}, "income_tier": {"present": true, "missing_values": "0", "completion_rate": 100.0}, "risk_tolerance": {"present": true, "missing_values": "0", "completion_rate": 100.0}, "lifestyle": {"present": true, "missing_values": "0", "completion_rate": 100.0}, "travel_frequency": {"present": true, "missing_values": "0", "completion_rate": 100.0}, "hsa_familiarity": {"present": true, "missing_values": "0", "completion_rate": 100.0}, "pregnancy_status": {"present": true, "missing_values": "0", "completion_rate": 100.0}, "tobacco_use": {"present": true, "missing_values": "0", "completion_rate": 100.0}, "mental_health_needs": {"present": true, "missing_values": "0", "completion_rate": 100.0}}, "overall_completion_rate": 100.0, "ready_for_prediction": "True"}}}}}