{"success": false, "status_code": 422, "error": "preprocessing_failed", "message": "Data preprocessing failed", "data": {"error_code": "preprocessing_failed", "error_message": "Data preprocessing failed", "success": false, "error": "validation_failed", "message": "Mandatory field validation failed", "validation_errors": ["Field 'marital_status' has missing values in 12 employee rows"], "error_rows": [{"row": 3, "field": "marital_status", "issue": "missing_value"}, {"row": 5, "field": "marital_status", "issue": "missing_value"}, {"row": 6, "field": "marital_status", "issue": "missing_value"}, {"row": 8, "field": "marital_status", "issue": "missing_value"}, {"row": 9, "field": "marital_status", "issue": "missing_value"}, {"row": 10, "field": "marital_status", "issue": "missing_value"}, {"row": 11, "field": "marital_status", "issue": "missing_value"}, {"row": 14, "field": "marital_status", "issue": "missing_value"}, {"row": 16, "field": "marital_status", "issue": "missing_value"}, {"row": 17, "field": "marital_status", "issue": "missing_value"}, {"row": 18, "field": "marital_status", "issue": "missing_value"}, {"row": 20, "field": "marital_status", "issue": "missing_value"}]}}