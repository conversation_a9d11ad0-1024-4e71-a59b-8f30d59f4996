{"success": true, "status_code": 200, "message": "Census file processed successfully", "data": {"summary": {"total_employees": 22, "processing_success": true, "data_quality_score": 0.9726419329643249, "api_integration_status": {"health_plans_available": true, "prediction_method": "ml_models", "total_plans_found": 6}}, "statistics": {"demographics": {"age_statistics": {"average_age": 40.68181818181818, "median_age": 41.0, "age_range": {"min": 21, "max": 64}, "age_distribution": {"18-30": 5, "31-45": 9, "46-60": 7, "60+": 1}}, "gender_composition": {"counts": {"Male": 14, "Female": 8}, "percentages": {"Male": 63.64, "Female": 36.36}}, "marital_status_distribution": {"counts": {"Married": 16, "Not Married": 6}, "percentages": {"Married": 72.73, "Not Married": 27.27}}}, "employment": {"department_distribution": {"counts": {"Information Technology": 6, "Finance": 6, "Manufacturing": 5, "Engineering": 5}, "percentages": {"Information Technology": 27.27, "Finance": 27.27, "Manufacturing": 22.73, "Engineering": 22.73}}, "employment_type_distribution": {"counts": {"Full-Time": 21, "Contract": 1}, "percentages": {"Full-Time": 95.45, "Contract": 4.55}}, "job_type_distribution": {"counts": {"Desk": 22}, "percentages": {"Desk": 100.0}}, "employee_class_distribution": {"counts": {}, "percentages": {}}}, "dependents": {"dependent_count_distribution": {"average_dependents": 0.5, "median_dependents": 0.0, "distribution": {"0": 15, "1": 5, "3+": 2}, "employees_with_dependents": 7, "percentage_with_dependents": 31.82}, "dependent_age_analysis": {"total_dependents_found": 12, "average_dependent_age": 21.67, "median_dependent_age": 17.5, "dependents_over_26_as_children": 0, "age_distribution": {"0-5": 4, "6-12": 1, "13-18": 1, "19-26": 2, "26+": 4}}}, "health_plans": {"total_employees": 22, "employees_with_plans": 2, "employees_without_plans": 20, "api_errors": 0, "unsupported_states": [], "supported_employees": 22, "total_plans_found": 6, "unique_plan_types": [], "unique_metal_levels": [], "unique_issuers": ["BlueCross BlueShield of South Carolina"], "plan_type_distribution": {"HMO": 2}, "metal_level_distribution": {"Bronze": 2}, "premium_statistics": {"min_premium": 660.47, "max_premium": 813.04, "avg_premium": 736.755}, "hsa_eligible_plans": 0, "quality_ratings": {"plans_with_ratings": 0, "avg_rating": null}}, "predictions": {"total_employees": 22, "plan_type_distribution": {"counts": {"PPO": 13, "HDHP + HSA": 4, "HMO": 4, "POS": 1}, "percentages": {"PPO": 59.09, "HDHP + HSA": 18.18, "HMO": 18.18, "POS": 4.55}}, "benefits_distribution": {"counts": {"Dental": 15, "Vision": 14, "Hospital Indemnity": 1, "Accident": 6, "Critical Illness": 5, "STD": 6, "Employee Assistance": 3}, "most_common": [["Dental", 15], ["Vision", 14], ["Accident", 6], ["STD", 6], ["Critical Illness", 5]]}, "confidence_metrics": {"plan_confidence": {"mean": 0.9088064432144165, "min": 0.6106691956520081, "max": 0.9986280202865601, "std": 0.11135075241327286}, "benefits_confidence": {"mean": 0.6590909090909091, "min": 0.0, "max": 1.0, "std": 0.3231674944007045}}}, "risk_analysis": {}}, "employees": [{"employee_id": "E001", "name": "<PERSON>", "age": 64.0, "gender": "Male", "marital_status": "Married", "zipcode": "29681", "city": "Simpsonville", "state": "SC", "income_tier": "Low (<$50K)", "employment_type": "Full-Time", "dept_count": "1", "predicted_plan_type": "PPO", "plan_confidence": 0.8249297738075256, "plan_reason": "Older age - PPO for comprehensive coverage and provider choice.", "predicted_benefits": ["Dental", "Vision", "Hospital Indemnity", "Accident", "Critical Illness", "STD", "Employee Assistance"], "benefits_confidence": 1.0, "benefits_reason": "Dental: Essential oral health coverage for all employees.; Vision: Eye care coverage for all employees.; Hospital Indemnity: Recommended due to good health condition, age 64, or chronic conditions.; Critical Illness: Important protection for age 64 with good health status.", "marketplace_plans_available": true, "plan_count": 3, "recommended_plan": {"id": "26065SC0710007", "name": "Blue Reedy Standard Expanded Bronze", "issuer": "BlueCross BlueShield of South Carolina", "premium": 813.04, "metal_level": "Bronze", "type": "HMO", "deductible": 7500, "max_out_of_pocket": 9200, "hsa_eligible": false, "quality_rating": 0}, "benefits_coverage": {"emergency_room": "50% Coinsurance after deductible", "primary_care": "$50", "specialist_care": "$100", "prescription_drugs": "$25", "mental_health": null, "maternity_care": null, "preventive_care": null}, "top_3_available_plans": "[{'id': '26065SC0710007', 'name': 'Blue Reedy Standard Expanded Bronze', 'issuer': 'BlueCross BlueShield of South Carolina', 'premium': 813.04, 'premium_with_credit': 0, 'metal_level': 'Bronze', 'type': 'HMO', 'deductible': 7500, 'max_out_of_pocket': 9200, 'hsa_eligible': False, 'quality_rating': 0, 'has_national_network': False, 'key_benefits': {'specialist_care': '$100', 'emergency_room': '50% Coinsurance after deductible', 'primary_care': '$50', 'prescription_drugs': '$25'}, 'issuer_phone': '**************', 'urls': {'benefits': 'https://www.southcarolinablues.com/web/nonsecure/sc/resources/40d7de36-f3d5-4c59-ad55-52297f2fbddd/BCBS+-+Blue+Reedy+Standard+Expanded+Bronze+2025.pdf', 'brochure': 'https://www.southcarolinablues.com/links/2025/brochure/bluereedy', 'formulary': 'https://www.southcarolinablues.com/links/2025/pharmacy/premiumrx', 'network': 'https://shoppingforcare.sapphirethreesixtyfive.com/?ci=CP089'}}, {'id': '26065SC0710010', 'name': 'Blue Reedy Bronze 2', 'issuer': 'BlueCross BlueShield of South Carolina', 'premium': 872.14, 'premium_with_credit': 0, 'metal_level': 'Bronze', 'type': 'HMO', 'deductible': 9200, 'max_out_of_pocket': 9200, 'hsa_eligible': False, 'quality_rating': 0, 'has_national_network': False, 'key_benefits': {'prescription_drugs': 'No Charge After Deductible', 'specialist_care': 'No Charge After Deductible', 'emergency_room': 'No Charge After Deductible', 'primary_care': 'No Charge After Deductible'}, 'issuer_phone': '**************', 'urls': {'benefits': 'https://www.southcarolinablues.com/web/nonsecure/sc/resources/96fb340c-7073-47df-a851-0dfc9f3f25d4/BCBS+-+Blue+Reedy+Bronze+2+2025.pdf', 'brochure': 'https://www.southcarolinablues.com/links/2025/brochure/bluereedy', 'formulary': 'https://www.southcarolinablues.com/links/2025/pharmacy/premiumrx', 'network': 'https://shoppingforcare.sapphirethreesixtyfive.com/?ci=CP089'}}, {'id': '26065SC0710007', 'name': 'Blue Reedy Standard Expanded Bronze', 'issuer': 'BlueCross BlueShield of South Carolina', 'premium': 890.92, 'premium_with_credit': 0, 'metal_level': 'Bronze', 'type': 'HMO', 'deductible': 7500, 'max_out_of_pocket': 9200, 'hsa_eligible': False, 'quality_rating': 0, 'has_national_network': False, 'key_benefits': {'specialist_care': '$100', 'emergency_room': '50% Coinsurance after deductible', 'primary_care': '$50', 'prescription_drugs': '$25'}, 'issuer_phone': '**************', 'urls': {'benefits': 'https://www.southcarolinablues.com/web/nonsecure/sc/resources/40d7de36-f3d5-4c59-ad55-52297f2fbddd/BCBS+-+Blue+Reedy+Standard+Expanded+Bronze+2025.pdf', 'brochure': 'https://www.southcarolinablues.com/links/2025/brochure/bluereedy', 'formulary': 'https://www.southcarolinablues.com/links/2025/pharmacy/premiumrx', 'network': 'https://shoppingforcare.sapphirethreesixtyfive.com/?ci=CP089'}}]", "api_processing_status": "success"}, {"employee_id": "E002", "name": "<PERSON>", "age": 34.0, "gender": "Male", "marital_status": "Married", "zipcode": "29642-9316", "city": "<PERSON><PERSON><PERSON>", "state": "SC", "income_tier": "Low (<$50K)", "employment_type": "Full-Time", "dept_count": "3+", "predicted_plan_type": "PPO", "plan_confidence": 0.9043065309524536, "plan_reason": "Low income, young age - DHMO for basic dental coverage.", "predicted_benefits": ["Dental", "Vision", "Accident", "STD"], "benefits_confidence": 1.0, "benefits_reason": "Dental: Essential oral health coverage for all employees.; Vision: Eye care coverage for all employees.", "marketplace_plans_available": false, "plan_count": 0, "benefits_coverage": {"emergency_room": null, "primary_care": null, "specialist_care": null, "prescription_drugs": null, "mental_health": null, "maternity_care": null, "preventive_care": null}, "api_processing_status": "no_plans_found"}, {"employee_id": "E003", "name": "<PERSON>", "age": 31.0, "gender": "Male", "marital_status": "Married", "zipcode": "29680-7299", "city": "Simpsonville", "state": "SC", "income_tier": "Low (<$50K)", "employment_type": "Full-Time", "dept_count": "0", "predicted_plan_type": "PPO", "plan_confidence": 0.9903061389923096, "plan_reason": "Low income, young age - DHMO for basic dental coverage.", "predicted_benefits": ["Critical Illness"], "benefits_confidence": 1.0, "benefits_reason": "Critical Illness: Important protection for age 31 with good health status.", "marketplace_plans_available": false, "plan_count": 0, "benefits_coverage": {"emergency_room": null, "primary_care": null, "specialist_care": null, "prescription_drugs": null, "mental_health": null, "maternity_care": null, "preventive_care": null}, "api_processing_status": "no_plans_found"}, {"employee_id": "E004", "name": "<PERSON>", "age": 37.0, "gender": "Male", "marital_status": "Married", "zipcode": "29388-8635", "city": "<PERSON><PERSON>", "state": "SC", "income_tier": "Medium ($50K–$100K)", "employment_type": "Full-Time", "dept_count": "0", "predicted_plan_type": "PPO", "plan_confidence": 0.9876225590705872, "plan_reason": "Middle age, no dependents - HMO for cost efficiency.", "predicted_benefits": ["Employee Assistance"], "benefits_confidence": 1.0, "benefits_reason": "ML model prediction", "marketplace_plans_available": false, "plan_count": 0, "benefits_coverage": {"emergency_room": null, "primary_care": null, "specialist_care": null, "prescription_drugs": null, "mental_health": null, "maternity_care": null, "preventive_care": null}, "api_processing_status": "no_plans_found"}, {"employee_id": "E005", "name": "<PERSON>", "age": 44.0, "gender": "Male", "marital_status": "Married", "zipcode": "29576-6242", "city": "<PERSON><PERSON><PERSON>", "state": "SC", "income_tier": "Medium ($50K–$100K)", "employment_type": "Full-Time", "dept_count": "0", "predicted_plan_type": "HDHP + HSA", "plan_confidence": 0.9931275844573975, "plan_reason": "Medium income, good health - PPO for balanced cost and flexibility.", "predicted_benefits": ["Accident", "Critical Illness", "STD"], "benefits_confidence": 1.0, "benefits_reason": "Critical Illness: Important protection for age 44 with good health status.; STD: Income protection for Medium ($50K–$100K) earners or good health status.", "marketplace_plans_available": false, "plan_count": 0, "benefits_coverage": {"emergency_room": null, "primary_care": null, "specialist_care": null, "prescription_drugs": null, "mental_health": null, "maternity_care": null, "preventive_care": null}, "api_processing_status": "no_plans_found"}, {"employee_id": "E006", "name": "<PERSON>", "age": 39.0, "gender": "Male", "marital_status": "Married", "zipcode": "29349-7843", "city": "Inman", "state": "SC", "income_tier": "Low (<$50K)", "employment_type": "Full-Time", "dept_count": "0", "predicted_plan_type": "PPO", "plan_confidence": 0.9963854551315308, "plan_reason": "Middle age, no dependents - HMO for cost efficiency.", "predicted_benefits": ["Accident", "Critical Illness", "STD"], "benefits_confidence": 1.0, "benefits_reason": "Critical Illness: Important protection for age 39 with excellent health status.", "marketplace_plans_available": false, "plan_count": 0, "benefits_coverage": {"emergency_room": null, "primary_care": null, "specialist_care": null, "prescription_drugs": null, "mental_health": null, "maternity_care": null, "preventive_care": null}, "api_processing_status": "no_plans_found"}, {"employee_id": "E007", "name": "<PERSON>", "age": 45.0, "gender": "Male", "marital_status": "Married", "zipcode": "29605-1910", "city": "Greenville", "state": "SC", "income_tier": "Medium ($50K–$100K)", "employment_type": "Full-Time", "dept_count": "3+", "predicted_plan_type": "PPO", "plan_confidence": 0.9630182981491089, "plan_reason": "Medium income, good health - PPO for balanced cost and flexibility.", "predicted_benefits": ["Dental", "Vision", "Critical Illness"], "benefits_confidence": 1.0, "benefits_reason": "Dental: Essential oral health coverage for all employees.; Vision: Eye care coverage for all employees.; Critical Illness: Important protection for age 45 with good health status.", "marketplace_plans_available": false, "plan_count": 0, "benefits_coverage": {"emergency_room": null, "primary_care": null, "specialist_care": null, "prescription_drugs": null, "mental_health": null, "maternity_care": null, "preventive_care": null}, "api_processing_status": "no_plans_found"}, {"employee_id": "E008", "name": "<PERSON>", "age": 43.0, "gender": "Male", "marital_status": "Married", "zipcode": "33542-3834", "city": "Zephyrhills", "state": "FL", "income_tier": "Low (<$50K)", "employment_type": "Full-Time", "dept_count": "0", "predicted_plan_type": "PPO", "plan_confidence": 0.8185616135597229, "plan_reason": "Middle age, no dependents - HMO for cost efficiency.", "predicted_benefits": [], "benefits_confidence": 0.0, "benefits_reason": "ML model prediction", "marketplace_plans_available": false, "plan_count": 0, "benefits_coverage": {"emergency_room": null, "primary_care": null, "specialist_care": null, "prescription_drugs": null, "mental_health": null, "maternity_care": null, "preventive_care": null}, "api_processing_status": "no_plans_found"}, {"employee_id": "E009", "name": "<PERSON>", "age": 33.0, "gender": "Male", "marital_status": "Not Married", "zipcode": "29388-8309", "city": "<PERSON><PERSON>", "state": "SC", "income_tier": "Medium ($50K–$100K)", "employment_type": "Full-Time", "dept_count": "0", "predicted_plan_type": "PPO", "plan_confidence": 0.9802271127700806, "plan_reason": "Middle age, no dependents - HMO for cost efficiency.", "predicted_benefits": [], "benefits_confidence": 0.0, "benefits_reason": "ML model prediction", "marketplace_plans_available": false, "plan_count": 0, "benefits_coverage": {"emergency_room": null, "primary_care": null, "specialist_care": null, "prescription_drugs": null, "mental_health": null, "maternity_care": null, "preventive_care": null}, "api_processing_status": "no_plans_found"}, {"employee_id": "E010", "name": "<PERSON>", "age": 23.0, "gender": "Female", "marital_status": "Not Married", "zipcode": "45243-2140", "city": "Cincinnati", "state": "OH", "income_tier": "High (>$100K)", "employment_type": "Full-Time", "dept_count": "0", "predicted_plan_type": "PPO", "plan_confidence": 0.8152409195899963, "plan_reason": "Young age - PPO for flexibility and future needs.", "predicted_benefits": ["Accident", "STD"], "benefits_confidence": 1.0, "benefits_reason": "Accident: Recommended for active lifestyle and rare travel frequency.; STD: Income protection for High (>$100K) earners or good health status.", "marketplace_plans_available": false, "plan_count": 0, "benefits_coverage": {"emergency_room": null, "primary_care": null, "specialist_care": null, "prescription_drugs": null, "mental_health": null, "maternity_care": null, "preventive_care": null}, "api_processing_status": "no_plans_found"}, {"employee_id": "E011", "name": "<PERSON>", "age": 51.0, "gender": "Male", "marital_status": "Married", "zipcode": "29642-8631", "city": "<PERSON><PERSON><PERSON>", "state": "SC", "income_tier": "Medium ($50K–$100K)", "employment_type": "Contract", "dept_count": "0", "predicted_plan_type": "POS", "plan_confidence": 0.9106675982475281, "plan_reason": "Medium income, good health - PPO for balanced cost and flexibility.", "predicted_benefits": ["Dental", "Accident", "STD", "Employee Assistance"], "benefits_confidence": 1.0, "benefits_reason": "Dental: Essential oral health coverage for all employees.; STD: Income protection for Medium ($50K–$100K) earners or good health status.", "marketplace_plans_available": false, "plan_count": 0, "benefits_coverage": {"emergency_room": null, "primary_care": null, "specialist_care": null, "prescription_drugs": null, "mental_health": null, "maternity_care": null, "preventive_care": null}, "api_processing_status": "no_plans_found"}, {"employee_id": "E012", "name": "<PERSON>", "age": 29.0, "gender": "Male", "marital_status": "Married", "zipcode": "30115-4910", "city": "Canton", "state": "GA", "income_tier": "High (>$100K)", "employment_type": "Full-Time", "dept_count": "1", "predicted_plan_type": "PPO", "plan_confidence": 0.6618258357048035, "plan_reason": "High income, excellent health, high risk tolerance with dependents - PPO for family coverage.", "predicted_benefits": ["Dental", "Vision"], "benefits_confidence": 0.5, "benefits_reason": "Default benefits assignment due to prediction error", "marketplace_plans_available": false, "plan_count": 0, "benefits_coverage": {"emergency_room": null, "primary_care": null, "specialist_care": null, "prescription_drugs": null, "mental_health": null, "maternity_care": null, "preventive_care": null}, "api_processing_status": "no_plans_found"}, {"employee_id": "E013", "name": "<PERSON>", "age": 26.0, "gender": "Female", "marital_status": "Married", "zipcode": "30115-4910", "city": "Canton", "state": "GA", "income_tier": "High (>$100K)", "employment_type": "Full-Time", "dept_count": "0", "predicted_plan_type": "HDHP + HSA", "plan_confidence": 0.8448315858840942, "plan_reason": "Young age - PPO for flexibility and future needs.", "predicted_benefits": ["Dental", "Vision"], "benefits_confidence": 0.5, "benefits_reason": "Default benefits assignment due to prediction error", "marketplace_plans_available": false, "plan_count": 0, "benefits_coverage": {"emergency_room": null, "primary_care": null, "specialist_care": null, "prescription_drugs": null, "mental_health": null, "maternity_care": null, "preventive_care": null}, "api_processing_status": "no_plans_found"}, {"employee_id": "E014", "name": "<PERSON>", "age": 57.0, "gender": "Female", "marital_status": "Not Married", "zipcode": "29644", "city": "Fountain Inn", "state": "SC", "income_tier": "Low (<$50K)", "employment_type": "Full-Time", "dept_count": "0", "predicted_plan_type": "HMO", "plan_confidence": 0.9720228910446167, "plan_reason": "Older age - PPO for comprehensive coverage and provider choice.", "predicted_benefits": ["Dental", "Vision"], "benefits_confidence": 0.5, "benefits_reason": "Default benefits assignment due to prediction error", "marketplace_plans_available": true, "plan_count": 3, "recommended_plan": {"id": "26065SC0710007", "name": "Blue Reedy Standard Expanded Bronze", "issuer": "BlueCross BlueShield of South Carolina", "premium": 660.47, "metal_level": "Bronze", "type": "HMO", "deductible": 7500, "max_out_of_pocket": 9200, "hsa_eligible": false, "quality_rating": 0}, "benefits_coverage": {"emergency_room": "50% Coinsurance after deductible", "primary_care": "$50", "specialist_care": "$100", "prescription_drugs": "$25", "mental_health": null, "maternity_care": null, "preventive_care": null}, "top_3_available_plans": "[{'id': '26065SC0710007', 'name': 'Blue Reedy Standard Expanded Bronze', 'issuer': 'BlueCross BlueShield of South Carolina', 'premium': 660.47, 'premium_with_credit': 0, 'metal_level': 'Bronze', 'type': 'HMO', 'deductible': 7500, 'max_out_of_pocket': 9200, 'hsa_eligible': False, 'quality_rating': 0, 'has_national_network': False, 'key_benefits': {'specialist_care': '$100', 'emergency_room': '50% Coinsurance after deductible', 'primary_care': '$50', 'prescription_drugs': '$25'}, 'issuer_phone': '**************', 'urls': {'benefits': 'https://www.southcarolinablues.com/web/nonsecure/sc/resources/40d7de36-f3d5-4c59-ad55-52297f2fbddd/BCBS+-+Blue+Reedy+Standard+Expanded+Bronze+2025.pdf', 'brochure': 'https://www.southcarolinablues.com/links/2025/brochure/bluereedy', 'formulary': 'https://www.southcarolinablues.com/links/2025/pharmacy/premiumrx', 'network': 'https://shoppingforcare.sapphirethreesixtyfive.com/?ci=CP089'}}, {'id': '26065SC0710010', 'name': 'Blue Reedy Bronze 2', 'issuer': 'BlueCross BlueShield of South Carolina', 'premium': 708.48, 'premium_with_credit': 0, 'metal_level': 'Bronze', 'type': 'HMO', 'deductible': 9200, 'max_out_of_pocket': 9200, 'hsa_eligible': False, 'quality_rating': 0, 'has_national_network': False, 'key_benefits': {'prescription_drugs': 'No Charge After Deductible', 'specialist_care': 'No Charge After Deductible', 'emergency_room': 'No Charge After Deductible', 'primary_care': 'No Charge After Deductible'}, 'issuer_phone': '**************', 'urls': {'benefits': 'https://www.southcarolinablues.com/web/nonsecure/sc/resources/96fb340c-7073-47df-a851-0dfc9f3f25d4/BCBS+-+Blue+Reedy+Bronze+2+2025.pdf', 'brochure': 'https://www.southcarolinablues.com/links/2025/brochure/bluereedy', 'formulary': 'https://www.southcarolinablues.com/links/2025/pharmacy/premiumrx', 'network': 'https://shoppingforcare.sapphirethreesixtyfive.com/?ci=CP089'}}, {'id': '26065SC0710007', 'name': 'Blue Reedy Standard Expanded Bronze', 'issuer': 'BlueCross BlueShield of South Carolina', 'premium': 723.73, 'premium_with_credit': 0, 'metal_level': 'Bronze', 'type': 'HMO', 'deductible': 7500, 'max_out_of_pocket': 9200, 'hsa_eligible': False, 'quality_rating': 0, 'has_national_network': False, 'key_benefits': {'specialist_care': '$100', 'emergency_room': '50% Coinsurance after deductible', 'primary_care': '$50', 'prescription_drugs': '$25'}, 'issuer_phone': '**************', 'urls': {'benefits': 'https://www.southcarolinablues.com/web/nonsecure/sc/resources/40d7de36-f3d5-4c59-ad55-52297f2fbddd/BCBS+-+Blue+Reedy+Standard+Expanded+Bronze+2025.pdf', 'brochure': 'https://www.southcarolinablues.com/links/2025/brochure/bluereedy', 'formulary': 'https://www.southcarolinablues.com/links/2025/pharmacy/premiumrx', 'network': 'https://shoppingforcare.sapphirethreesixtyfive.com/?ci=CP089'}}]", "api_processing_status": "success"}, {"employee_id": "E015", "name": "<PERSON>", "age": 51.0, "gender": "Female", "marital_status": "Married", "zipcode": "29334-8892", "city": "<PERSON>", "state": "SC", "income_tier": "Medium ($50K–$100K)", "employment_type": "Full-Time", "dept_count": "1", "predicted_plan_type": "HDHP + HSA", "plan_confidence": 0.9986280202865601, "plan_reason": "Chronic conditions, older age - POS for coordinated care management.", "predicted_benefits": ["Dental", "Vision"], "benefits_confidence": 0.5, "benefits_reason": "Default benefits assignment due to prediction error", "marketplace_plans_available": false, "plan_count": 0, "benefits_coverage": {"emergency_room": null, "primary_care": null, "specialist_care": null, "prescription_drugs": null, "mental_health": null, "maternity_care": null, "preventive_care": null}, "api_processing_status": "no_plans_found"}, {"employee_id": "E016", "name": "<PERSON>", "age": 47.0, "gender": "Female", "marital_status": "Not Married", "zipcode": "28173-7182", "city": "<PERSON><PERSON><PERSON>", "state": "NC", "income_tier": "High (>$100K)", "employment_type": "Full-Time", "dept_count": "0", "predicted_plan_type": "HDHP + HSA", "plan_confidence": 0.9976467490196228, "plan_reason": "Chronic conditions, older age - POS for coordinated care management.", "predicted_benefits": ["Dental", "Vision"], "benefits_confidence": 0.5, "benefits_reason": "Default benefits assignment due to prediction error", "marketplace_plans_available": false, "plan_count": 0, "benefits_coverage": {"emergency_room": null, "primary_care": null, "specialist_care": null, "prescription_drugs": null, "mental_health": null, "maternity_care": null, "preventive_care": null}, "api_processing_status": "no_plans_found"}, {"employee_id": "E017", "name": "<PERSON>", "age": 34.0, "gender": "Male", "marital_status": "Not Married", "zipcode": "30319", "city": "Atlanta", "state": "GA", "income_tier": "Low (<$50K)", "employment_type": "Full-Time", "dept_count": "0", "predicted_plan_type": "HMO", "plan_confidence": 0.8231197595596313, "plan_reason": "Low income, young age - DHMO for basic dental coverage.", "predicted_benefits": ["Dental", "Vision"], "benefits_confidence": 0.5, "benefits_reason": "Default benefits assignment due to prediction error", "marketplace_plans_available": false, "plan_count": 0, "benefits_coverage": {"emergency_room": null, "primary_care": null, "specialist_care": null, "prescription_drugs": null, "mental_health": null, "maternity_care": null, "preventive_care": null}, "api_processing_status": "no_plans_found"}, {"employee_id": "E018", "name": "<PERSON>", "age": 46.0, "gender": "Female", "marital_status": "Not Married", "zipcode": "20877-1801", "city": "Gaithersburg", "state": "MD", "income_tier": "Medium ($50K–$100K)", "employment_type": "Full-Time", "dept_count": "0", "predicted_plan_type": "PPO", "plan_confidence": 0.9887648820877075, "plan_reason": "Medium income, good health - PPO for balanced cost and flexibility.", "predicted_benefits": ["Dental", "Vision"], "benefits_confidence": 0.5, "benefits_reason": "Default benefits assignment due to prediction error", "marketplace_plans_available": false, "plan_count": 0, "benefits_coverage": {"emergency_room": null, "primary_care": null, "specialist_care": null, "prescription_drugs": null, "mental_health": null, "maternity_care": null, "preventive_care": null}, "api_processing_status": "no_plans_found"}, {"employee_id": "E019", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "age": 21.0, "gender": "Male", "marital_status": "Married", "zipcode": "20877-1801", "city": "Gaithersburg", "state": "MD", "income_tier": "Medium ($50K–$100K)", "employment_type": "Full-Time", "dept_count": "0", "predicted_plan_type": "HMO", "plan_confidence": 0.6106691956520081, "plan_reason": "Medium income, good health - PPO for balanced cost and flexibility.", "predicted_benefits": ["Dental", "Vision"], "benefits_confidence": 0.5, "benefits_reason": "Default benefits assignment due to prediction error", "marketplace_plans_available": false, "plan_count": 0, "benefits_coverage": {"emergency_room": null, "primary_care": null, "specialist_care": null, "prescription_drugs": null, "mental_health": null, "maternity_care": null, "preventive_care": null}, "api_processing_status": "no_plans_found"}, {"employee_id": "E020", "name": "<PERSON><PERSON>", "age": 27.0, "gender": "Female", "marital_status": "Married", "zipcode": "29644-1408", "city": "Fountain Inn", "state": "SC", "income_tier": "Medium ($50K–$100K)", "employment_type": "Full-Time", "dept_count": "1", "predicted_plan_type": "HMO", "plan_confidence": 0.9758399128913879, "plan_reason": "Medium income, good health - PPO for balanced cost and flexibility.", "predicted_benefits": ["Dental", "Vision"], "benefits_confidence": 0.5, "benefits_reason": "Default benefits assignment due to prediction error", "marketplace_plans_available": false, "plan_count": 0, "benefits_coverage": {"emergency_room": null, "primary_care": null, "specialist_care": null, "prescription_drugs": null, "mental_health": null, "maternity_care": null, "preventive_care": null}, "api_processing_status": "no_plans_found"}, {"employee_id": "E021", "name": "<PERSON>", "age": 57.0, "gender": "Female", "marital_status": "Married", "zipcode": "29617-6139", "city": "Greenville", "state": "SC", "income_tier": "Medium ($50K–$100K)", "employment_type": "Full-Time", "dept_count": "0", "predicted_plan_type": "PPO", "plan_confidence": 0.9399325847625732, "plan_reason": "Older age - PPO for comprehensive coverage and provider choice.", "predicted_benefits": ["Dental", "Vision"], "benefits_confidence": 0.5, "benefits_reason": "Default benefits assignment due to prediction error", "marketplace_plans_available": false, "plan_count": 0, "benefits_coverage": {"emergency_room": null, "primary_care": null, "specialist_care": null, "prescription_drugs": null, "mental_health": null, "maternity_care": null, "preventive_care": null}, "api_processing_status": "no_plans_found"}, {"employee_id": "E022", "name": "<PERSON>", "age": 56.0, "gender": "Male", "marital_status": "Married", "zipcode": "29617-6139", "city": "Greenville", "state": "SC", "income_tier": "Low (<$50K)", "employment_type": "Full-Time", "dept_count": "1", "predicted_plan_type": "PPO", "plan_confidence": 0.996066153049469, "plan_reason": "Chronic conditions, older age - POS for coordinated care management.", "predicted_benefits": ["Dental", "Vision"], "benefits_confidence": 0.5, "benefits_reason": "Default benefits assignment due to prediction error", "marketplace_plans_available": false, "plan_count": 0, "benefits_coverage": {"emergency_room": null, "primary_care": null, "specialist_care": null, "prescription_drugs": null, "mental_health": null, "maternity_care": null, "preventive_care": null}, "api_processing_status": "no_plans_found"}], "processing_info": {"enrichment_summary": {"total_employees": 22, "features_analyzed": 19, "data_quality_improvement": {"overall_completion_rate": 100.0, "total_enriched": 49}}, "feature_validation": {}, "health_plan_errors": {}, "processing_notes": ["Health plan integration successful for 2/22 employees (9.1%)", "No marketplace plans found for 20 employees", "Marketplace plans available for 2 employees", "Average recommended plan premium: $736.75", "API Error: Invalid ZIP code: 29642-9316", "API Error: No plans available", "API Error: Invalid ZIP code: 29617-6139"]}, "enriched_dataframe": [{"gender": "Male", "dob": "10/18/1960", "marital_status": "Married", "address1": "7 Regal Way", "city": "Simpsonville", "state": "SC", "zipcode": "29681", "vision_plan": "Y", "dental_plan": null, "life_plan": "Y", "add_plan": "Y", "relationship": "Employee", "dept_1_first_name": "<PERSON>", "dept_1_last_name": "<PERSON>", "dept_1_gender": "Female", "dept_1_dob": "1/31/1968", "dept_1_address1": "7 Regal Way", "dept_1_city": "Simpsonville", "dept_1_state": "SC", "dept_1_zipcode": "29681", "relationship_type_1": "Spouse", "dept_1": "<PERSON>", "dept_1_dental_plan": null, "dept_1_vision_plan": null, "dept_1_life_plan": null, "dept_1_add_plan": null, "dept_count": "1", "dept_2_first_name": null, "dept_2_last_name": null, "dept_2_gender": null, "dept_2_dob": null, "dept_2_address1": null, "dept_2_city": null, "dept_2_state": null, "dept_2_zipcode": null, "relationship_type_2": null, "dept_2": null, "dept_2_dental_plan": null, "dept_2_vision_plan": null, "dept_2_life_plan": null, "dept_2_add_plan": null, "dept_3_first_name": null, "dept_3_last_name": null, "dept_3_gender": null, "dept_3_dob": null, "dept_3_address1": null, "dept_3_city": null, "dept_3_state": null, "dept_3_zipcode": null, "relationship_type_3": null, "dept_3": null, "dept_3_dental_plan": null, "dept_3_vision_plan": null, "dept_3_life_plan": null, "dept_3_add_plan": null, "dept_4_first_name": null, "dept_4_last_name": null, "dept_4_gender": null, "dept_4_dob": null, "dept_4_address1": null, "dept_4_city": null, "dept_4_state": null, "dept_4_zipcode": null, "relationship_type_4": null, "dept_4": null, "dept_4_dental_plan": null, "dept_4_vision_plan": null, "dept_4_life_plan": null, "dept_4_add_plan": null, "name": "<PERSON>", "age": 64.0, "dept_1_age": 57.0, "dept_2_age": null, "dept_3_age": null, "dept_4_age": null, "employee_id": "E001", "first_name": null, "middle_name": null, "last_name": null, "address2": null, "record_type": null, "salary": null, "employment_type": "Full-Time", "employee_class": null, "department": "Manufacturing", "hire_date": null, "tobacco_use": "N", "pregnancy_status": "N", "medical_plan": null, "coverage_tier": null, "ssn": null, "region": "Rural", "job_type": "Desk", "health_condition": "Good", "chronic_condition": "N", "prescription_use": "None", "income_tier": "Low (<$50K)", "risk_tolerance": "Medium", "lifestyle": "Moderate", "travel_frequency": "Occasional", "hsa_familiarity": "N", "mental_health_needs": "Y", "predicted_plan_type": "PPO", "plan_confidence": 0.8249297738075256, "plan_reason": "Older age - PPO for comprehensive coverage and provider choice.", "top_3_plans": ["PPO", "HMO", "POS"], "top_3_plan_confidences": [0.8249297738075256, 0.16291671991348267, 0.006916375365108252], "predicted_benefits": ["Dental", "Vision", "Hospital Indemnity", "Accident", "Critical Illness", "STD", "Employee Assistance"], "benefits_confidence": 1.0, "benefits_reason": "Dental: Essential oral health coverage for all employees.; Vision: Eye care coverage for all employees.; Hospital Indemnity: Recommended due to good health condition, age 64, or chronic conditions.; Critical Illness: Important protection for age 64 with good health status.", "top_3_benefits": ["Employee Assistance", "Wellness Programs", "FSA"], "top_3_benefits_confidences": [0.5, 0.5, 0.5], "marketplace_plans_available": true, "plan_count": 3, "available_plan_types": null, "available_metal_levels": null, "estimated_premium_range": null, "marketplace_state_supported": null, "recommended_plan_id": "26065SC0710007", "recommended_plan_name": "Blue Reedy Standard Expanded Bronze", "recommended_plan_issuer": "BlueCross BlueShield of South Carolina", "recommended_plan_premium": 813.04, "recommended_plan_metal_level": "Bronze", "recommended_plan_type": "HMO", "recommended_plan_deductible": 7500, "recommended_plan_moop": 9200, "recommended_plan_hsa_eligible": false, "recommended_plan_quality_rating": 0, "top_3_available_plans": "[{'id': '26065SC0710007', 'name': 'Blue Reedy Standard Expanded Bronze', 'issuer': 'BlueCross BlueShield of South Carolina', 'premium': 813.04, 'premium_with_credit': 0, 'metal_level': 'Bronze', 'type': 'HMO', 'deductible': 7500, 'max_out_of_pocket': 9200, 'hsa_eligible': False, 'quality_rating': 0, 'has_national_network': False, 'key_benefits': {'specialist_care': '$100', 'emergency_room': '50% Coinsurance after deductible', 'primary_care': '$50', 'prescription_drugs': '$25'}, 'issuer_phone': '**************', 'urls': {'benefits': 'https://www.southcarolinablues.com/web/nonsecure/sc/resources/40d7de36-f3d5-4c59-ad55-52297f2fbddd/BCBS+-+Blue+Reedy+Standard+Expanded+Bronze+2025.pdf', 'brochure': 'https://www.southcarolinablues.com/links/2025/brochure/bluereedy', 'formulary': 'https://www.southcarolinablues.com/links/2025/pharmacy/premiumrx', 'network': 'https://shoppingforcare.sapphirethreesixtyfive.com/?ci=CP089'}}, {'id': '26065SC0710010', 'name': 'Blue Reedy Bronze 2', 'issuer': 'BlueCross BlueShield of South Carolina', 'premium': 872.14, 'premium_with_credit': 0, 'metal_level': 'Bronze', 'type': 'HMO', 'deductible': 9200, 'max_out_of_pocket': 9200, 'hsa_eligible': False, 'quality_rating': 0, 'has_national_network': False, 'key_benefits': {'prescription_drugs': 'No Charge After Deductible', 'specialist_care': 'No Charge After Deductible', 'emergency_room': 'No Charge After Deductible', 'primary_care': 'No Charge After Deductible'}, 'issuer_phone': '**************', 'urls': {'benefits': 'https://www.southcarolinablues.com/web/nonsecure/sc/resources/96fb340c-7073-47df-a851-0dfc9f3f25d4/BCBS+-+Blue+Reedy+Bronze+2+2025.pdf', 'brochure': 'https://www.southcarolinablues.com/links/2025/brochure/bluereedy', 'formulary': 'https://www.southcarolinablues.com/links/2025/pharmacy/premiumrx', 'network': 'https://shoppingforcare.sapphirethreesixtyfive.com/?ci=CP089'}}, {'id': '26065SC0710007', 'name': 'Blue Reedy Standard Expanded Bronze', 'issuer': 'BlueCross BlueShield of South Carolina', 'premium': 890.92, 'premium_with_credit': 0, 'metal_level': 'Bronze', 'type': 'HMO', 'deductible': 7500, 'max_out_of_pocket': 9200, 'hsa_eligible': False, 'quality_rating': 0, 'has_national_network': False, 'key_benefits': {'specialist_care': '$100', 'emergency_room': '50% Coinsurance after deductible', 'primary_care': '$50', 'prescription_drugs': '$25'}, 'issuer_phone': '**************', 'urls': {'benefits': 'https://www.southcarolinablues.com/web/nonsecure/sc/resources/40d7de36-f3d5-4c59-ad55-52297f2fbddd/BCBS+-+Blue+Reedy+Standard+Expanded+Bronze+2025.pdf', 'brochure': 'https://www.southcarolinablues.com/links/2025/brochure/bluereedy', 'formulary': 'https://www.southcarolinablues.com/links/2025/pharmacy/premiumrx', 'network': 'https://shoppingforcare.sapphirethreesixtyfive.com/?ci=CP089'}}]", "benefits_emergency_room": "50% Coinsurance after deductible", "benefits_primary_care": "$50", "benefits_specialist_care": "$100", "benefits_prescription_drugs": "$25", "benefits_mental_health": null, "benefits_maternity_care": null, "benefits_preventive_care": null, "network_has_national": false, "network_referral_required": false, "api_processing_status": "success", "api_error_message": null}, {"gender": "Male", "dob": "6/6/1991", "marital_status": "Married", "address1": "105 Campden Ct", "city": "<PERSON><PERSON><PERSON>", "state": "SC", "zipcode": "29642-9316", "vision_plan": null, "dental_plan": null, "life_plan": "Y", "add_plan": "Y", "relationship": "Employee", "dept_1_first_name": "<PERSON>", "dept_1_last_name": "<PERSON><PERSON>", "dept_1_gender": "Female", "dept_1_dob": "2/28/1997", "dept_1_address1": "105 Campden Ct", "dept_1_city": "<PERSON><PERSON><PERSON>", "dept_1_state": "SC", "dept_1_zipcode": "29642-9316", "relationship_type_1": "Spouse", "dept_1": "<PERSON>", "dept_1_dental_plan": null, "dept_1_vision_plan": null, "dept_1_life_plan": null, "dept_1_add_plan": null, "dept_count": "3+", "dept_2_first_name": "<PERSON><PERSON><PERSON>", "dept_2_last_name": "<PERSON><PERSON>", "dept_2_gender": "Female", "dept_2_dob": "2/18/2019", "dept_2_address1": "105 Campden Ct", "dept_2_city": "<PERSON><PERSON><PERSON>", "dept_2_state": "SC", "dept_2_zipcode": "29642-9316", "relationship_type_2": "Child", "dept_2": "<PERSON><PERSON><PERSON>", "dept_2_dental_plan": null, "dept_2_vision_plan": null, "dept_2_life_plan": null, "dept_2_add_plan": null, "dept_3_first_name": "<PERSON>", "dept_3_last_name": "<PERSON><PERSON>", "dept_3_gender": "Male", "dept_3_dob": "6/14/2021", "dept_3_address1": "105 Campden Ct", "dept_3_city": "<PERSON><PERSON><PERSON>", "dept_3_state": "SC", "dept_3_zipcode": "29642-9316", "relationship_type_3": "Child", "dept_3": "<PERSON>", "dept_3_dental_plan": null, "dept_3_vision_plan": null, "dept_3_life_plan": null, "dept_3_add_plan": null, "dept_4_first_name": "<PERSON>", "dept_4_last_name": "<PERSON><PERSON>", "dept_4_gender": "Male", "dept_4_dob": "6/17/2023", "dept_4_address1": "105 Campden Ct", "dept_4_city": "<PERSON><PERSON><PERSON>", "dept_4_state": "SC", "dept_4_zipcode": "29642-9316", "relationship_type_4": "Child", "dept_4": "<PERSON>", "dept_4_dental_plan": null, "dept_4_vision_plan": null, "dept_4_life_plan": null, "dept_4_add_plan": null, "name": "<PERSON>", "age": 34.0, "dept_1_age": 28.0, "dept_2_age": 6.0, "dept_3_age": 4.0, "dept_4_age": 2.0, "employee_id": "E002", "first_name": null, "middle_name": null, "last_name": null, "address2": null, "record_type": null, "salary": null, "employment_type": "Full-Time", "employee_class": null, "department": "Engineering", "hire_date": null, "tobacco_use": "N", "pregnancy_status": "N", "medical_plan": null, "coverage_tier": null, "ssn": null, "region": "Rural", "job_type": "Desk", "health_condition": "Good", "chronic_condition": "N", "prescription_use": "None", "income_tier": "Low (<$50K)", "risk_tolerance": "Low", "lifestyle": "Moderate", "travel_frequency": "Occasional", "hsa_familiarity": "N", "mental_health_needs": "N", "predicted_plan_type": "PPO", "plan_confidence": 0.9043065309524536, "plan_reason": "Low income, young age - DHMO for basic dental coverage.", "top_3_plans": ["PPO", "HMO", "POS"], "top_3_plan_confidences": [0.9043065309524536, 0.08535416424274445, 0.00827975943684578], "predicted_benefits": ["Dental", "Vision", "Accident", "STD"], "benefits_confidence": 1.0, "benefits_reason": "Dental: Essential oral health coverage for all employees.; Vision: Eye care coverage for all employees.", "top_3_benefits": ["Employee Assistance", "Wellness Programs", "FSA"], "top_3_benefits_confidences": [0.5, 0.5, 0.5], "marketplace_plans_available": false, "plan_count": 0, "available_plan_types": null, "available_metal_levels": null, "estimated_premium_range": null, "marketplace_state_supported": null, "recommended_plan_id": null, "recommended_plan_name": null, "recommended_plan_issuer": null, "recommended_plan_premium": null, "recommended_plan_metal_level": null, "recommended_plan_type": null, "recommended_plan_deductible": null, "recommended_plan_moop": null, "recommended_plan_hsa_eligible": null, "recommended_plan_quality_rating": null, "top_3_available_plans": null, "benefits_emergency_room": null, "benefits_primary_care": null, "benefits_specialist_care": null, "benefits_prescription_drugs": null, "benefits_mental_health": null, "benefits_maternity_care": null, "benefits_preventive_care": null, "network_has_national": null, "network_referral_required": null, "api_processing_status": "no_plans_found", "api_error_message": "Invalid ZIP code: 29642-9316"}, {"gender": "Male", "dob": "9/14/1993", "marital_status": "Married", "address1": "3 Billings Mill Ct", "city": "Simpsonville", "state": "SC", "zipcode": "29680-7299", "vision_plan": "Y", "dental_plan": "Y", "life_plan": "Y", "add_plan": "Y", "relationship": "Employee", "dept_1_first_name": null, "dept_1_last_name": null, "dept_1_gender": null, "dept_1_dob": null, "dept_1_address1": null, "dept_1_city": null, "dept_1_state": null, "dept_1_zipcode": null, "relationship_type_1": null, "dept_1": null, "dept_1_dental_plan": null, "dept_1_vision_plan": null, "dept_1_life_plan": null, "dept_1_add_plan": null, "dept_count": "0", "dept_2_first_name": null, "dept_2_last_name": null, "dept_2_gender": null, "dept_2_dob": null, "dept_2_address1": null, "dept_2_city": null, "dept_2_state": null, "dept_2_zipcode": null, "relationship_type_2": null, "dept_2": null, "dept_2_dental_plan": null, "dept_2_vision_plan": null, "dept_2_life_plan": null, "dept_2_add_plan": null, "dept_3_first_name": null, "dept_3_last_name": null, "dept_3_gender": null, "dept_3_dob": null, "dept_3_address1": null, "dept_3_city": null, "dept_3_state": null, "dept_3_zipcode": null, "relationship_type_3": null, "dept_3": null, "dept_3_dental_plan": null, "dept_3_vision_plan": null, "dept_3_life_plan": null, "dept_3_add_plan": null, "dept_4_first_name": null, "dept_4_last_name": null, "dept_4_gender": null, "dept_4_dob": null, "dept_4_address1": null, "dept_4_city": null, "dept_4_state": null, "dept_4_zipcode": null, "relationship_type_4": null, "dept_4": null, "dept_4_dental_plan": null, "dept_4_vision_plan": null, "dept_4_life_plan": null, "dept_4_add_plan": null, "name": "<PERSON>", "age": 31.0, "dept_1_age": null, "dept_2_age": null, "dept_3_age": null, "dept_4_age": null, "employee_id": "E003", "first_name": null, "middle_name": null, "last_name": null, "address2": null, "record_type": null, "salary": null, "employment_type": "Full-Time", "employee_class": null, "department": "Information Technology", "hire_date": null, "tobacco_use": "N", "pregnancy_status": "N", "medical_plan": null, "coverage_tier": null, "ssn": null, "region": "Rural", "job_type": "Desk", "health_condition": "Good", "chronic_condition": "Y", "prescription_use": "Regular", "income_tier": "Low (<$50K)", "risk_tolerance": "Medium", "lifestyle": "Active", "travel_frequency": "Rare", "hsa_familiarity": "N", "mental_health_needs": "N", "predicted_plan_type": "PPO", "plan_confidence": 0.9903061389923096, "plan_reason": "Low income, young age - DHMO for basic dental coverage.", "top_3_plans": ["PPO", "HMO", "MEC"], "top_3_plan_confidences": [0.9903061389923096, 0.005834364797919989, 0.0012845925521105528], "predicted_benefits": ["Critical Illness"], "benefits_confidence": 1.0, "benefits_reason": "Critical Illness: Important protection for age 31 with good health status.", "top_3_benefits": ["Employee Assistance", "Wellness Programs", "FSA"], "top_3_benefits_confidences": [0.5, 0.5, 0.5], "marketplace_plans_available": false, "plan_count": 0, "available_plan_types": null, "available_metal_levels": null, "estimated_premium_range": null, "marketplace_state_supported": null, "recommended_plan_id": null, "recommended_plan_name": null, "recommended_plan_issuer": null, "recommended_plan_premium": null, "recommended_plan_metal_level": null, "recommended_plan_type": null, "recommended_plan_deductible": null, "recommended_plan_moop": null, "recommended_plan_hsa_eligible": null, "recommended_plan_quality_rating": null, "top_3_available_plans": null, "benefits_emergency_room": null, "benefits_primary_care": null, "benefits_specialist_care": null, "benefits_prescription_drugs": null, "benefits_mental_health": null, "benefits_maternity_care": null, "benefits_preventive_care": null, "network_has_national": null, "network_referral_required": null, "api_processing_status": "no_plans_found", "api_error_message": "Invalid ZIP code: 29680-7299"}, {"gender": "Male", "dob": "5/29/1988", "marital_status": "Married", "address1": "1501 Davis Rd", "city": "<PERSON><PERSON>", "state": "SC", "zipcode": "29388-8635", "vision_plan": null, "dental_plan": "Y", "life_plan": "Y", "add_plan": "Y", "relationship": "Employee", "dept_1_first_name": null, "dept_1_last_name": null, "dept_1_gender": null, "dept_1_dob": null, "dept_1_address1": null, "dept_1_city": null, "dept_1_state": null, "dept_1_zipcode": null, "relationship_type_1": null, "dept_1": null, "dept_1_dental_plan": null, "dept_1_vision_plan": null, "dept_1_life_plan": null, "dept_1_add_plan": null, "dept_count": "0", "dept_2_first_name": null, "dept_2_last_name": null, "dept_2_gender": null, "dept_2_dob": null, "dept_2_address1": null, "dept_2_city": null, "dept_2_state": null, "dept_2_zipcode": null, "relationship_type_2": null, "dept_2": null, "dept_2_dental_plan": null, "dept_2_vision_plan": null, "dept_2_life_plan": null, "dept_2_add_plan": null, "dept_3_first_name": null, "dept_3_last_name": null, "dept_3_gender": null, "dept_3_dob": null, "dept_3_address1": null, "dept_3_city": null, "dept_3_state": null, "dept_3_zipcode": null, "relationship_type_3": null, "dept_3": null, "dept_3_dental_plan": null, "dept_3_vision_plan": null, "dept_3_life_plan": null, "dept_3_add_plan": null, "dept_4_first_name": null, "dept_4_last_name": null, "dept_4_gender": null, "dept_4_dob": null, "dept_4_address1": null, "dept_4_city": null, "dept_4_state": null, "dept_4_zipcode": null, "relationship_type_4": null, "dept_4": null, "dept_4_dental_plan": null, "dept_4_vision_plan": null, "dept_4_life_plan": null, "dept_4_add_plan": null, "name": "<PERSON>", "age": 37.0, "dept_1_age": null, "dept_2_age": null, "dept_3_age": null, "dept_4_age": null, "employee_id": "E004", "first_name": null, "middle_name": null, "last_name": null, "address2": null, "record_type": null, "salary": null, "employment_type": "Full-Time", "employee_class": null, "department": "Finance", "hire_date": null, "tobacco_use": "N", "pregnancy_status": "N", "medical_plan": null, "coverage_tier": null, "ssn": null, "region": "Rural", "job_type": "Desk", "health_condition": "Fair", "chronic_condition": "N", "prescription_use": "None", "income_tier": "Medium ($50K–$100K)", "risk_tolerance": "Medium", "lifestyle": "Moderate", "travel_frequency": "Rare", "hsa_familiarity": "N", "mental_health_needs": "Y", "predicted_plan_type": "PPO", "plan_confidence": 0.9876225590705872, "plan_reason": "Middle age, no dependents - HMO for cost efficiency.", "top_3_plans": ["PPO", "HMO", "POS"], "top_3_plan_confidences": [0.9876225590705872, 0.009441888891160488, 0.0011222773464396596], "predicted_benefits": ["Employee Assistance"], "benefits_confidence": 1.0, "benefits_reason": "ML model prediction", "top_3_benefits": ["Employee Assistance", "Wellness Programs", "FSA"], "top_3_benefits_confidences": [0.5, 0.5, 0.5], "marketplace_plans_available": false, "plan_count": 0, "available_plan_types": null, "available_metal_levels": null, "estimated_premium_range": null, "marketplace_state_supported": null, "recommended_plan_id": null, "recommended_plan_name": null, "recommended_plan_issuer": null, "recommended_plan_premium": null, "recommended_plan_metal_level": null, "recommended_plan_type": null, "recommended_plan_deductible": null, "recommended_plan_moop": null, "recommended_plan_hsa_eligible": null, "recommended_plan_quality_rating": null, "top_3_available_plans": null, "benefits_emergency_room": null, "benefits_primary_care": null, "benefits_specialist_care": null, "benefits_prescription_drugs": null, "benefits_mental_health": null, "benefits_maternity_care": null, "benefits_preventive_care": null, "network_has_national": null, "network_referral_required": null, "api_processing_status": "no_plans_found", "api_error_message": "Invalid ZIP code: 29388-8635"}, {"gender": "Male", "dob": "12/16/1980", "marital_status": "Married", "address1": "596 Collins Ave", "city": "<PERSON><PERSON><PERSON>", "state": "SC", "zipcode": "29576-6242", "vision_plan": null, "dental_plan": null, "life_plan": null, "add_plan": "Y", "relationship": "Employee", "dept_1_first_name": null, "dept_1_last_name": null, "dept_1_gender": null, "dept_1_dob": null, "dept_1_address1": null, "dept_1_city": null, "dept_1_state": null, "dept_1_zipcode": null, "relationship_type_1": null, "dept_1": null, "dept_1_dental_plan": null, "dept_1_vision_plan": null, "dept_1_life_plan": null, "dept_1_add_plan": null, "dept_count": "0", "dept_2_first_name": null, "dept_2_last_name": null, "dept_2_gender": null, "dept_2_dob": null, "dept_2_address1": null, "dept_2_city": null, "dept_2_state": null, "dept_2_zipcode": null, "relationship_type_2": null, "dept_2": null, "dept_2_dental_plan": null, "dept_2_vision_plan": null, "dept_2_life_plan": null, "dept_2_add_plan": null, "dept_3_first_name": null, "dept_3_last_name": null, "dept_3_gender": null, "dept_3_dob": null, "dept_3_address1": null, "dept_3_city": null, "dept_3_state": null, "dept_3_zipcode": null, "relationship_type_3": null, "dept_3": null, "dept_3_dental_plan": null, "dept_3_vision_plan": null, "dept_3_life_plan": null, "dept_3_add_plan": null, "dept_4_first_name": null, "dept_4_last_name": null, "dept_4_gender": null, "dept_4_dob": null, "dept_4_address1": null, "dept_4_city": null, "dept_4_state": null, "dept_4_zipcode": null, "relationship_type_4": null, "dept_4": null, "dept_4_dental_plan": null, "dept_4_vision_plan": null, "dept_4_life_plan": null, "dept_4_add_plan": null, "name": "<PERSON>", "age": 44.0, "dept_1_age": null, "dept_2_age": null, "dept_3_age": null, "dept_4_age": null, "employee_id": "E005", "first_name": null, "middle_name": null, "last_name": null, "address2": null, "record_type": null, "salary": null, "employment_type": "Full-Time", "employee_class": null, "department": "Engineering", "hire_date": null, "tobacco_use": "N", "pregnancy_status": "N", "medical_plan": null, "coverage_tier": null, "ssn": null, "region": "Rural", "job_type": "Desk", "health_condition": "Good", "chronic_condition": "Y", "prescription_use": "Regular", "income_tier": "Medium ($50K–$100K)", "risk_tolerance": "Medium", "lifestyle": "Moderate", "travel_frequency": "Occasional", "hsa_familiarity": "Y", "mental_health_needs": "N", "predicted_plan_type": "HDHP + HSA", "plan_confidence": 0.9931275844573975, "plan_reason": "Medium income, good health - PPO for balanced cost and flexibility.", "top_3_plans": ["HDHP + HSA", "PPO", "POS"], "top_3_plan_confidences": [0.9931275844573975, 0.004965318366885185, 0.0016337179113179445], "predicted_benefits": ["Accident", "Critical Illness", "STD"], "benefits_confidence": 1.0, "benefits_reason": "Critical Illness: Important protection for age 44 with good health status.; STD: Income protection for Medium ($50K–$100K) earners or good health status.", "top_3_benefits": ["Employee Assistance", "Wellness Programs", "FSA"], "top_3_benefits_confidences": [0.5, 0.5, 0.5], "marketplace_plans_available": false, "plan_count": 0, "available_plan_types": null, "available_metal_levels": null, "estimated_premium_range": null, "marketplace_state_supported": null, "recommended_plan_id": null, "recommended_plan_name": null, "recommended_plan_issuer": null, "recommended_plan_premium": null, "recommended_plan_metal_level": null, "recommended_plan_type": null, "recommended_plan_deductible": null, "recommended_plan_moop": null, "recommended_plan_hsa_eligible": null, "recommended_plan_quality_rating": null, "top_3_available_plans": null, "benefits_emergency_room": null, "benefits_primary_care": null, "benefits_specialist_care": null, "benefits_prescription_drugs": null, "benefits_mental_health": null, "benefits_maternity_care": null, "benefits_preventive_care": null, "network_has_national": null, "network_referral_required": null, "api_processing_status": "no_plans_found", "api_error_message": "Invalid ZIP code: 29576-6242"}, {"gender": "Male", "dob": "4/22/1986", "marital_status": "Married", "address1": "1311 Sloan Rd", "city": "Inman", "state": "SC", "zipcode": "29349-7843", "vision_plan": null, "dental_plan": null, "life_plan": null, "add_plan": null, "relationship": "Employee", "dept_1_first_name": null, "dept_1_last_name": null, "dept_1_gender": null, "dept_1_dob": null, "dept_1_address1": null, "dept_1_city": null, "dept_1_state": null, "dept_1_zipcode": null, "relationship_type_1": null, "dept_1": null, "dept_1_dental_plan": null, "dept_1_vision_plan": null, "dept_1_life_plan": null, "dept_1_add_plan": null, "dept_count": "0", "dept_2_first_name": null, "dept_2_last_name": null, "dept_2_gender": null, "dept_2_dob": null, "dept_2_address1": null, "dept_2_city": null, "dept_2_state": null, "dept_2_zipcode": null, "relationship_type_2": null, "dept_2": null, "dept_2_dental_plan": null, "dept_2_vision_plan": null, "dept_2_life_plan": null, "dept_2_add_plan": null, "dept_3_first_name": null, "dept_3_last_name": null, "dept_3_gender": null, "dept_3_dob": null, "dept_3_address1": null, "dept_3_city": null, "dept_3_state": null, "dept_3_zipcode": null, "relationship_type_3": null, "dept_3": null, "dept_3_dental_plan": null, "dept_3_vision_plan": null, "dept_3_life_plan": null, "dept_3_add_plan": null, "dept_4_first_name": null, "dept_4_last_name": null, "dept_4_gender": null, "dept_4_dob": null, "dept_4_address1": null, "dept_4_city": null, "dept_4_state": null, "dept_4_zipcode": null, "relationship_type_4": null, "dept_4": null, "dept_4_dental_plan": null, "dept_4_vision_plan": null, "dept_4_life_plan": null, "dept_4_add_plan": null, "name": "<PERSON>", "age": 39.0, "dept_1_age": null, "dept_2_age": null, "dept_3_age": null, "dept_4_age": null, "employee_id": "E006", "first_name": null, "middle_name": null, "last_name": null, "address2": null, "record_type": null, "salary": null, "employment_type": "Full-Time", "employee_class": null, "department": "Engineering", "hire_date": null, "tobacco_use": "N", "pregnancy_status": "N", "medical_plan": null, "coverage_tier": null, "ssn": null, "region": "Rural", "job_type": "Desk", "health_condition": "Excellent", "chronic_condition": "Y", "prescription_use": "Regular", "income_tier": "Low (<$50K)", "risk_tolerance": "Medium", "lifestyle": "Moderate", "travel_frequency": "Occasional", "hsa_familiarity": "N", "mental_health_needs": "N", "predicted_plan_type": "PPO", "plan_confidence": 0.9963854551315308, "plan_reason": "Middle age, no dependents - HMO for cost efficiency.", "top_3_plans": ["PPO", "HMO", "POS"], "top_3_plan_confidences": [0.9963854551315308, 0.002121742581948638, 0.000761263130698353], "predicted_benefits": ["Accident", "Critical Illness", "STD"], "benefits_confidence": 1.0, "benefits_reason": "Critical Illness: Important protection for age 39 with excellent health status.", "top_3_benefits": ["Employee Assistance", "Wellness Programs", "FSA"], "top_3_benefits_confidences": [0.5, 0.5, 0.5], "marketplace_plans_available": false, "plan_count": 0, "available_plan_types": null, "available_metal_levels": null, "estimated_premium_range": null, "marketplace_state_supported": null, "recommended_plan_id": null, "recommended_plan_name": null, "recommended_plan_issuer": null, "recommended_plan_premium": null, "recommended_plan_metal_level": null, "recommended_plan_type": null, "recommended_plan_deductible": null, "recommended_plan_moop": null, "recommended_plan_hsa_eligible": null, "recommended_plan_quality_rating": null, "top_3_available_plans": null, "benefits_emergency_room": null, "benefits_primary_care": null, "benefits_specialist_care": null, "benefits_prescription_drugs": null, "benefits_mental_health": null, "benefits_maternity_care": null, "benefits_preventive_care": null, "network_has_national": null, "network_referral_required": null, "api_processing_status": "no_plans_found", "api_error_message": "Invalid ZIP code: 29349-7843"}, {"gender": "Male", "dob": "8/11/1979", "marital_status": "Married", "address1": "207 Cammer Ave", "city": "Greenville", "state": "SC", "zipcode": "29605-1910", "vision_plan": null, "dental_plan": "Y", "life_plan": "Y", "add_plan": "Y", "relationship": "Employee", "dept_1_first_name": "<PERSON>", "dept_1_last_name": "Fox", "dept_1_gender": "Female", "dept_1_dob": "9/5/1976", "dept_1_address1": "207 Cammer Ave", "dept_1_city": "Greenville", "dept_1_state": "SC", "dept_1_zipcode": "29605-1910", "relationship_type_1": "Spouse", "dept_1": "<PERSON>", "dept_1_dental_plan": null, "dept_1_vision_plan": null, "dept_1_life_plan": null, "dept_1_add_plan": null, "dept_count": "3+", "dept_2_first_name": "Sienna", "dept_2_last_name": "Fox", "dept_2_gender": "Female", "dept_2_dob": "5/7/2006", "dept_2_address1": "207 Cammer Ave", "dept_2_city": "Greenville", "dept_2_state": "SC", "dept_2_zipcode": "29605-1910", "relationship_type_2": "Child", "dept_2": "<PERSON>", "dept_2_dental_plan": null, "dept_2_vision_plan": null, "dept_2_life_plan": null, "dept_2_add_plan": null, "dept_3_first_name": "Saddler", "dept_3_last_name": "Fox", "dept_3_gender": "Female", "dept_3_dob": "9/17/2008", "dept_3_address1": "207 Cammer Ave", "dept_3_city": "Greenville", "dept_3_state": "SC", "dept_3_zipcode": "29605-1910", "relationship_type_3": "Child", "dept_3": "Saddler <PERSON>", "dept_3_dental_plan": null, "dept_3_vision_plan": null, "dept_3_life_plan": null, "dept_3_add_plan": null, "dept_4_first_name": null, "dept_4_last_name": null, "dept_4_gender": null, "dept_4_dob": null, "dept_4_address1": null, "dept_4_city": null, "dept_4_state": null, "dept_4_zipcode": null, "relationship_type_4": null, "dept_4": null, "dept_4_dental_plan": null, "dept_4_vision_plan": null, "dept_4_life_plan": null, "dept_4_add_plan": null, "name": "<PERSON>", "age": 45.0, "dept_1_age": 48.0, "dept_2_age": 19.0, "dept_3_age": 16.0, "dept_4_age": null, "employee_id": "E007", "first_name": null, "middle_name": null, "last_name": null, "address2": null, "record_type": null, "salary": null, "employment_type": "Full-Time", "employee_class": null, "department": "Finance", "hire_date": null, "tobacco_use": "N", "pregnancy_status": "N", "medical_plan": null, "coverage_tier": null, "ssn": null, "region": "Rural", "job_type": "Desk", "health_condition": "Good", "chronic_condition": "Y", "prescription_use": "Regular", "income_tier": "Medium ($50K–$100K)", "risk_tolerance": "Medium", "lifestyle": "Moderate", "travel_frequency": "Rare", "hsa_familiarity": "N", "mental_health_needs": "N", "predicted_plan_type": "PPO", "plan_confidence": 0.9630182981491089, "plan_reason": "Medium income, good health - PPO for balanced cost and flexibility.", "top_3_plans": ["PPO", "POS", "HMO"], "top_3_plan_confidences": [0.9630182981491089, 0.02951892465353012, 0.006098940037190914], "predicted_benefits": ["Dental", "Vision", "Critical Illness"], "benefits_confidence": 1.0, "benefits_reason": "Dental: Essential oral health coverage for all employees.; Vision: Eye care coverage for all employees.; Critical Illness: Important protection for age 45 with good health status.", "top_3_benefits": ["Employee Assistance", "Wellness Programs", "FSA"], "top_3_benefits_confidences": [0.5, 0.5, 0.5], "marketplace_plans_available": false, "plan_count": 0, "available_plan_types": null, "available_metal_levels": null, "estimated_premium_range": null, "marketplace_state_supported": null, "recommended_plan_id": null, "recommended_plan_name": null, "recommended_plan_issuer": null, "recommended_plan_premium": null, "recommended_plan_metal_level": null, "recommended_plan_type": null, "recommended_plan_deductible": null, "recommended_plan_moop": null, "recommended_plan_hsa_eligible": null, "recommended_plan_quality_rating": null, "top_3_available_plans": null, "benefits_emergency_room": null, "benefits_primary_care": null, "benefits_specialist_care": null, "benefits_prescription_drugs": null, "benefits_mental_health": null, "benefits_maternity_care": null, "benefits_preventive_care": null, "network_has_national": null, "network_referral_required": null, "api_processing_status": "no_plans_found", "api_error_message": "Invalid ZIP code: 29605-1910"}, {"gender": "Male", "dob": "12/25/1981", "marital_status": "Married", "address1": "39051 12th Ave", "city": "Zephyrhills", "state": "FL", "zipcode": "33542-3834", "vision_plan": null, "dental_plan": null, "life_plan": null, "add_plan": "Y", "relationship": "Employee", "dept_1_first_name": null, "dept_1_last_name": null, "dept_1_gender": null, "dept_1_dob": null, "dept_1_address1": null, "dept_1_city": null, "dept_1_state": null, "dept_1_zipcode": null, "relationship_type_1": null, "dept_1": null, "dept_1_dental_plan": null, "dept_1_vision_plan": null, "dept_1_life_plan": null, "dept_1_add_plan": null, "dept_count": "0", "dept_2_first_name": null, "dept_2_last_name": null, "dept_2_gender": null, "dept_2_dob": null, "dept_2_address1": null, "dept_2_city": null, "dept_2_state": null, "dept_2_zipcode": null, "relationship_type_2": null, "dept_2": null, "dept_2_dental_plan": null, "dept_2_vision_plan": null, "dept_2_life_plan": null, "dept_2_add_plan": null, "dept_3_first_name": null, "dept_3_last_name": null, "dept_3_gender": null, "dept_3_dob": null, "dept_3_address1": null, "dept_3_city": null, "dept_3_state": null, "dept_3_zipcode": null, "relationship_type_3": null, "dept_3": null, "dept_3_dental_plan": null, "dept_3_vision_plan": null, "dept_3_life_plan": null, "dept_3_add_plan": null, "dept_4_first_name": null, "dept_4_last_name": null, "dept_4_gender": null, "dept_4_dob": null, "dept_4_address1": null, "dept_4_city": null, "dept_4_state": null, "dept_4_zipcode": null, "relationship_type_4": null, "dept_4": null, "dept_4_dental_plan": null, "dept_4_vision_plan": null, "dept_4_life_plan": null, "dept_4_add_plan": null, "name": "<PERSON>", "age": 43.0, "dept_1_age": null, "dept_2_age": null, "dept_3_age": null, "dept_4_age": null, "employee_id": "E008", "first_name": null, "middle_name": null, "last_name": null, "address2": null, "record_type": null, "salary": null, "employment_type": "Full-Time", "employee_class": null, "department": "Finance", "hire_date": null, "tobacco_use": "N", "pregnancy_status": "N", "medical_plan": null, "coverage_tier": null, "ssn": null, "region": "Rural", "job_type": "Desk", "health_condition": "Good", "chronic_condition": "N", "prescription_use": "None", "income_tier": "Low (<$50K)", "risk_tolerance": "Low", "lifestyle": "Active", "travel_frequency": "Rare", "hsa_familiarity": "N", "mental_health_needs": "N", "predicted_plan_type": "PPO", "plan_confidence": 0.8185616135597229, "plan_reason": "Middle age, no dependents - HMO for cost efficiency.", "top_3_plans": ["PPO", "HMO", "MEC"], "top_3_plan_confidences": [0.8185616135597229, 0.1707209050655365, 0.003604034660384059], "predicted_benefits": [], "benefits_confidence": 0.0, "benefits_reason": "ML model prediction", "top_3_benefits": ["Employee Assistance", "Wellness Programs", "FSA"], "top_3_benefits_confidences": [0.5, 0.5, 0.5], "marketplace_plans_available": false, "plan_count": 0, "available_plan_types": null, "available_metal_levels": null, "estimated_premium_range": null, "marketplace_state_supported": null, "recommended_plan_id": null, "recommended_plan_name": null, "recommended_plan_issuer": null, "recommended_plan_premium": null, "recommended_plan_metal_level": null, "recommended_plan_type": null, "recommended_plan_deductible": null, "recommended_plan_moop": null, "recommended_plan_hsa_eligible": null, "recommended_plan_quality_rating": null, "top_3_available_plans": null, "benefits_emergency_room": null, "benefits_primary_care": null, "benefits_specialist_care": null, "benefits_prescription_drugs": null, "benefits_mental_health": null, "benefits_maternity_care": null, "benefits_preventive_care": null, "network_has_national": null, "network_referral_required": null, "api_processing_status": "no_plans_found", "api_error_message": "Invalid ZIP code: 33542-3834"}, {"gender": "Male", "dob": "1/3/1992", "marital_status": "Not Married", "address1": "715 Oakview Farms Rd", "city": "<PERSON><PERSON>", "state": "SC", "zipcode": "29388-8309", "vision_plan": null, "dental_plan": "Y", "life_plan": "Y", "add_plan": "Y", "relationship": "Employee", "dept_1_first_name": null, "dept_1_last_name": null, "dept_1_gender": null, "dept_1_dob": null, "dept_1_address1": null, "dept_1_city": null, "dept_1_state": null, "dept_1_zipcode": null, "relationship_type_1": null, "dept_1": null, "dept_1_dental_plan": null, "dept_1_vision_plan": null, "dept_1_life_plan": null, "dept_1_add_plan": null, "dept_count": "0", "dept_2_first_name": null, "dept_2_last_name": null, "dept_2_gender": null, "dept_2_dob": null, "dept_2_address1": null, "dept_2_city": null, "dept_2_state": null, "dept_2_zipcode": null, "relationship_type_2": null, "dept_2": null, "dept_2_dental_plan": null, "dept_2_vision_plan": null, "dept_2_life_plan": null, "dept_2_add_plan": null, "dept_3_first_name": null, "dept_3_last_name": null, "dept_3_gender": null, "dept_3_dob": null, "dept_3_address1": null, "dept_3_city": null, "dept_3_state": null, "dept_3_zipcode": null, "relationship_type_3": null, "dept_3": null, "dept_3_dental_plan": null, "dept_3_vision_plan": null, "dept_3_life_plan": null, "dept_3_add_plan": null, "dept_4_first_name": null, "dept_4_last_name": null, "dept_4_gender": null, "dept_4_dob": null, "dept_4_address1": null, "dept_4_city": null, "dept_4_state": null, "dept_4_zipcode": null, "relationship_type_4": null, "dept_4": null, "dept_4_dental_plan": null, "dept_4_vision_plan": null, "dept_4_life_plan": null, "dept_4_add_plan": null, "name": "<PERSON>", "age": 33.0, "dept_1_age": null, "dept_2_age": null, "dept_3_age": null, "dept_4_age": null, "employee_id": "E009", "first_name": null, "middle_name": null, "last_name": null, "address2": null, "record_type": null, "salary": null, "employment_type": "Full-Time", "employee_class": null, "department": "Information Technology", "hire_date": null, "tobacco_use": "N", "pregnancy_status": "N", "medical_plan": null, "coverage_tier": null, "ssn": null, "region": "Rural", "job_type": "Desk", "health_condition": "Fair", "chronic_condition": "N", "prescription_use": "None", "income_tier": "Medium ($50K–$100K)", "risk_tolerance": "Medium", "lifestyle": "Active", "travel_frequency": "Rare", "hsa_familiarity": "N", "mental_health_needs": "N", "predicted_plan_type": "PPO", "plan_confidence": 0.9802271127700806, "plan_reason": "Middle age, no dependents - HMO for cost efficiency.", "top_3_plans": ["PPO", "HMO", "EPO"], "top_3_plan_confidences": [0.9802271127700806, 0.014874803833663464, 0.0019677348900586367], "predicted_benefits": [], "benefits_confidence": 0.0, "benefits_reason": "ML model prediction", "top_3_benefits": ["Employee Assistance", "Wellness Programs", "FSA"], "top_3_benefits_confidences": [0.5, 0.5, 0.5], "marketplace_plans_available": false, "plan_count": 0, "available_plan_types": null, "available_metal_levels": null, "estimated_premium_range": null, "marketplace_state_supported": null, "recommended_plan_id": null, "recommended_plan_name": null, "recommended_plan_issuer": null, "recommended_plan_premium": null, "recommended_plan_metal_level": null, "recommended_plan_type": null, "recommended_plan_deductible": null, "recommended_plan_moop": null, "recommended_plan_hsa_eligible": null, "recommended_plan_quality_rating": null, "top_3_available_plans": null, "benefits_emergency_room": null, "benefits_primary_care": null, "benefits_specialist_care": null, "benefits_prescription_drugs": null, "benefits_mental_health": null, "benefits_maternity_care": null, "benefits_preventive_care": null, "network_has_national": null, "network_referral_required": null, "api_processing_status": "no_plans_found", "api_error_message": "Invalid ZIP code: 29388-8309"}, {"gender": "Female", "dob": "7/22/2001", "marital_status": "Not Married", "address1": "7265 <PERSON>", "city": "Cincinnati", "state": "OH", "zipcode": "45243-2140", "vision_plan": null, "dental_plan": null, "life_plan": "Y", "add_plan": "Y", "relationship": "Employee", "dept_1_first_name": null, "dept_1_last_name": null, "dept_1_gender": null, "dept_1_dob": null, "dept_1_address1": null, "dept_1_city": null, "dept_1_state": null, "dept_1_zipcode": null, "relationship_type_1": null, "dept_1": null, "dept_1_dental_plan": null, "dept_1_vision_plan": null, "dept_1_life_plan": null, "dept_1_add_plan": null, "dept_count": "0", "dept_2_first_name": null, "dept_2_last_name": null, "dept_2_gender": null, "dept_2_dob": null, "dept_2_address1": null, "dept_2_city": null, "dept_2_state": null, "dept_2_zipcode": null, "relationship_type_2": null, "dept_2": null, "dept_2_dental_plan": null, "dept_2_vision_plan": null, "dept_2_life_plan": null, "dept_2_add_plan": null, "dept_3_first_name": null, "dept_3_last_name": null, "dept_3_gender": null, "dept_3_dob": null, "dept_3_address1": null, "dept_3_city": null, "dept_3_state": null, "dept_3_zipcode": null, "relationship_type_3": null, "dept_3": null, "dept_3_dental_plan": null, "dept_3_vision_plan": null, "dept_3_life_plan": null, "dept_3_add_plan": null, "dept_4_first_name": null, "dept_4_last_name": null, "dept_4_gender": null, "dept_4_dob": null, "dept_4_address1": null, "dept_4_city": null, "dept_4_state": null, "dept_4_zipcode": null, "relationship_type_4": null, "dept_4": null, "dept_4_dental_plan": null, "dept_4_vision_plan": null, "dept_4_life_plan": null, "dept_4_add_plan": null, "name": "<PERSON>", "age": 23.0, "dept_1_age": null, "dept_2_age": null, "dept_3_age": null, "dept_4_age": null, "employee_id": "E010", "first_name": null, "middle_name": null, "last_name": null, "address2": null, "record_type": null, "salary": null, "employment_type": "Full-Time", "employee_class": null, "department": "Manufacturing", "hire_date": null, "tobacco_use": "N", "pregnancy_status": "N", "medical_plan": null, "coverage_tier": null, "ssn": null, "region": "Urban", "job_type": "Desk", "health_condition": "Good", "chronic_condition": "N", "prescription_use": "None", "income_tier": "High (>$100K)", "risk_tolerance": "High", "lifestyle": "Active", "travel_frequency": "Rare", "hsa_familiarity": "N", "mental_health_needs": "N", "predicted_plan_type": "PPO", "plan_confidence": 0.8152409195899963, "plan_reason": "Young age - PPO for flexibility and future needs.", "top_3_plans": ["PPO", "HMO", "Indemnity"], "top_3_plan_confidences": [0.8152409195899963, 0.14311037957668304, 0.030340293422341347], "predicted_benefits": ["Accident", "STD"], "benefits_confidence": 1.0, "benefits_reason": "Accident: Recommended for active lifestyle and rare travel frequency.; STD: Income protection for High (>$100K) earners or good health status.", "top_3_benefits": ["Employee Assistance", "Wellness Programs", "FSA"], "top_3_benefits_confidences": [0.5, 0.5, 0.5], "marketplace_plans_available": false, "plan_count": 0, "available_plan_types": null, "available_metal_levels": null, "estimated_premium_range": null, "marketplace_state_supported": null, "recommended_plan_id": null, "recommended_plan_name": null, "recommended_plan_issuer": null, "recommended_plan_premium": null, "recommended_plan_metal_level": null, "recommended_plan_type": null, "recommended_plan_deductible": null, "recommended_plan_moop": null, "recommended_plan_hsa_eligible": null, "recommended_plan_quality_rating": null, "top_3_available_plans": null, "benefits_emergency_room": null, "benefits_primary_care": null, "benefits_specialist_care": null, "benefits_prescription_drugs": null, "benefits_mental_health": null, "benefits_maternity_care": null, "benefits_preventive_care": null, "network_has_national": null, "network_referral_required": null, "api_processing_status": "no_plans_found", "api_error_message": "Invalid ZIP code: 45243-2140"}, {"gender": "Male", "dob": "3/11/1974", "marital_status": "Married", "address1": "107 Culpepper Ln", "city": "<PERSON><PERSON><PERSON>", "state": "SC", "zipcode": "29642-8631", "vision_plan": null, "dental_plan": null, "life_plan": null, "add_plan": "Y", "relationship": "Employee", "dept_1_first_name": null, "dept_1_last_name": null, "dept_1_gender": null, "dept_1_dob": null, "dept_1_address1": null, "dept_1_city": null, "dept_1_state": null, "dept_1_zipcode": null, "relationship_type_1": null, "dept_1": null, "dept_1_dental_plan": null, "dept_1_vision_plan": null, "dept_1_life_plan": null, "dept_1_add_plan": null, "dept_count": "0", "dept_2_first_name": null, "dept_2_last_name": null, "dept_2_gender": null, "dept_2_dob": null, "dept_2_address1": null, "dept_2_city": null, "dept_2_state": null, "dept_2_zipcode": null, "relationship_type_2": null, "dept_2": null, "dept_2_dental_plan": null, "dept_2_vision_plan": null, "dept_2_life_plan": null, "dept_2_add_plan": null, "dept_3_first_name": null, "dept_3_last_name": null, "dept_3_gender": null, "dept_3_dob": null, "dept_3_address1": null, "dept_3_city": null, "dept_3_state": null, "dept_3_zipcode": null, "relationship_type_3": null, "dept_3": null, "dept_3_dental_plan": null, "dept_3_vision_plan": null, "dept_3_life_plan": null, "dept_3_add_plan": null, "dept_4_first_name": null, "dept_4_last_name": null, "dept_4_gender": null, "dept_4_dob": null, "dept_4_address1": null, "dept_4_city": null, "dept_4_state": null, "dept_4_zipcode": null, "relationship_type_4": null, "dept_4": null, "dept_4_dental_plan": null, "dept_4_vision_plan": null, "dept_4_life_plan": null, "dept_4_add_plan": null, "name": "<PERSON>", "age": 51.0, "dept_1_age": null, "dept_2_age": null, "dept_3_age": null, "dept_4_age": null, "employee_id": "E011", "first_name": null, "middle_name": null, "last_name": null, "address2": null, "record_type": null, "salary": null, "employment_type": "Contract", "employee_class": null, "department": "Manufacturing", "hire_date": null, "tobacco_use": "N", "pregnancy_status": "N", "medical_plan": null, "coverage_tier": null, "ssn": null, "region": "Rural", "job_type": "Desk", "health_condition": "Good", "chronic_condition": "N", "prescription_use": "None", "income_tier": "Medium ($50K–$100K)", "risk_tolerance": "Low", "lifestyle": "Moderate", "travel_frequency": "Occasional", "hsa_familiarity": "N", "mental_health_needs": "Y", "predicted_plan_type": "POS", "plan_confidence": 0.9106675982475281, "plan_reason": "Medium income, good health - PPO for balanced cost and flexibility.", "top_3_plans": ["POS", "PPO", "HMO"], "top_3_plan_confidences": [0.9106675982475281, 0.08890903741121292, 0.0002484130091033876], "predicted_benefits": ["Dental", "Accident", "STD", "Employee Assistance"], "benefits_confidence": 1.0, "benefits_reason": "Dental: Essential oral health coverage for all employees.; STD: Income protection for Medium ($50K–$100K) earners or good health status.", "top_3_benefits": ["Employee Assistance", "Wellness Programs", "FSA"], "top_3_benefits_confidences": [0.5, 0.5, 0.5], "marketplace_plans_available": false, "plan_count": 0, "available_plan_types": null, "available_metal_levels": null, "estimated_premium_range": null, "marketplace_state_supported": null, "recommended_plan_id": null, "recommended_plan_name": null, "recommended_plan_issuer": null, "recommended_plan_premium": null, "recommended_plan_metal_level": null, "recommended_plan_type": null, "recommended_plan_deductible": null, "recommended_plan_moop": null, "recommended_plan_hsa_eligible": null, "recommended_plan_quality_rating": null, "top_3_available_plans": null, "benefits_emergency_room": null, "benefits_primary_care": null, "benefits_specialist_care": null, "benefits_prescription_drugs": null, "benefits_mental_health": null, "benefits_maternity_care": null, "benefits_preventive_care": null, "network_has_national": null, "network_referral_required": null, "api_processing_status": "no_plans_found", "api_error_message": "Invalid ZIP code: 29642-8631"}, {"gender": "Male", "dob": "3/25/1996", "marital_status": "Married", "address1": "229 Crooked Creek Trl", "city": "Canton", "state": "GA", "zipcode": "30115-4910", "vision_plan": "Y", "dental_plan": "Y", "life_plan": "Y", "add_plan": "Y", "relationship": "Employee", "dept_1_first_name": "<PERSON>", "dept_1_last_name": "<PERSON><PERSON><PERSON>", "dept_1_gender": "Female", "dept_1_dob": "8/29/2024", "dept_1_address1": "229 Crooked Creek Trl", "dept_1_city": "Canton", "dept_1_state": "GA", "dept_1_zipcode": "30115-4910", "relationship_type_1": "Child", "dept_1": "<PERSON>", "dept_1_dental_plan": null, "dept_1_vision_plan": null, "dept_1_life_plan": null, "dept_1_add_plan": null, "dept_count": "1", "dept_2_first_name": null, "dept_2_last_name": null, "dept_2_gender": null, "dept_2_dob": null, "dept_2_address1": null, "dept_2_city": null, "dept_2_state": null, "dept_2_zipcode": null, "relationship_type_2": null, "dept_2": null, "dept_2_dental_plan": null, "dept_2_vision_plan": null, "dept_2_life_plan": null, "dept_2_add_plan": null, "dept_3_first_name": null, "dept_3_last_name": null, "dept_3_gender": null, "dept_3_dob": null, "dept_3_address1": null, "dept_3_city": null, "dept_3_state": null, "dept_3_zipcode": null, "relationship_type_3": null, "dept_3": null, "dept_3_dental_plan": null, "dept_3_vision_plan": null, "dept_3_life_plan": null, "dept_3_add_plan": null, "dept_4_first_name": null, "dept_4_last_name": null, "dept_4_gender": null, "dept_4_dob": null, "dept_4_address1": null, "dept_4_city": null, "dept_4_state": null, "dept_4_zipcode": null, "relationship_type_4": null, "dept_4": null, "dept_4_dental_plan": null, "dept_4_vision_plan": null, "dept_4_life_plan": null, "dept_4_add_plan": null, "name": "<PERSON>", "age": 29.0, "dept_1_age": 0.0, "dept_2_age": null, "dept_3_age": null, "dept_4_age": null, "employee_id": "E012", "first_name": null, "middle_name": null, "last_name": null, "address2": null, "record_type": null, "salary": null, "employment_type": "Full-Time", "employee_class": null, "department": "Finance", "hire_date": null, "tobacco_use": "Y", "pregnancy_status": "N", "medical_plan": null, "coverage_tier": null, "ssn": null, "region": "Rural", "job_type": "Desk", "health_condition": "Excellent", "chronic_condition": "N", "prescription_use": "Occasional", "income_tier": "High (>$100K)", "risk_tolerance": "High", "lifestyle": "Active", "travel_frequency": "Rare", "hsa_familiarity": "Y", "mental_health_needs": "Y", "predicted_plan_type": "PPO", "plan_confidence": 0.6618258357048035, "plan_reason": "High income, excellent health, high risk tolerance with dependents - PPO for family coverage.", "top_3_plans": ["PPO", "HDHP", "HDHP + HSA"], "top_3_plan_confidences": [0.6618258357048035, 0.3252224326133728, 0.009125342592597008], "predicted_benefits": ["Dental", "Vision"], "benefits_confidence": 0.5, "benefits_reason": "Default benefits assignment due to prediction error", "top_3_benefits": ["Dental", "Vision", "Hospital Indemnity"], "top_3_benefits_confidences": [0.5, 0.5, 0.5], "marketplace_plans_available": false, "plan_count": 0, "available_plan_types": null, "available_metal_levels": null, "estimated_premium_range": null, "marketplace_state_supported": null, "recommended_plan_id": null, "recommended_plan_name": null, "recommended_plan_issuer": null, "recommended_plan_premium": null, "recommended_plan_metal_level": null, "recommended_plan_type": null, "recommended_plan_deductible": null, "recommended_plan_moop": null, "recommended_plan_hsa_eligible": null, "recommended_plan_quality_rating": null, "top_3_available_plans": null, "benefits_emergency_room": null, "benefits_primary_care": null, "benefits_specialist_care": null, "benefits_prescription_drugs": null, "benefits_mental_health": null, "benefits_maternity_care": null, "benefits_preventive_care": null, "network_has_national": null, "network_referral_required": null, "api_processing_status": "no_plans_found", "api_error_message": "Invalid ZIP code: 30115-4910"}, {"gender": "Female", "dob": "9/5/1998", "marital_status": "Married", "address1": "229 Crooked Creek Trl", "city": "Canton", "state": "GA", "zipcode": "30115-4910", "vision_plan": null, "dental_plan": null, "life_plan": null, "add_plan": null, "relationship": "Spouse", "dept_1_first_name": null, "dept_1_last_name": null, "dept_1_gender": null, "dept_1_dob": null, "dept_1_address1": null, "dept_1_city": null, "dept_1_state": null, "dept_1_zipcode": null, "relationship_type_1": null, "dept_1": null, "dept_1_dental_plan": null, "dept_1_vision_plan": null, "dept_1_life_plan": null, "dept_1_add_plan": null, "dept_count": "0", "dept_2_first_name": null, "dept_2_last_name": null, "dept_2_gender": null, "dept_2_dob": null, "dept_2_address1": null, "dept_2_city": null, "dept_2_state": null, "dept_2_zipcode": null, "relationship_type_2": null, "dept_2": null, "dept_2_dental_plan": null, "dept_2_vision_plan": null, "dept_2_life_plan": null, "dept_2_add_plan": null, "dept_3_first_name": null, "dept_3_last_name": null, "dept_3_gender": null, "dept_3_dob": null, "dept_3_address1": null, "dept_3_city": null, "dept_3_state": null, "dept_3_zipcode": null, "relationship_type_3": null, "dept_3": null, "dept_3_dental_plan": null, "dept_3_vision_plan": null, "dept_3_life_plan": null, "dept_3_add_plan": null, "dept_4_first_name": null, "dept_4_last_name": null, "dept_4_gender": null, "dept_4_dob": null, "dept_4_address1": null, "dept_4_city": null, "dept_4_state": null, "dept_4_zipcode": null, "relationship_type_4": null, "dept_4": null, "dept_4_dental_plan": null, "dept_4_vision_plan": null, "dept_4_life_plan": null, "dept_4_add_plan": null, "name": "<PERSON>", "age": 26.0, "dept_1_age": null, "dept_2_age": null, "dept_3_age": null, "dept_4_age": null, "employee_id": "E013", "first_name": null, "middle_name": null, "last_name": null, "address2": null, "record_type": null, "salary": null, "employment_type": "Full-Time", "employee_class": null, "department": "Manufacturing", "hire_date": null, "tobacco_use": "Y", "pregnancy_status": "N", "medical_plan": null, "coverage_tier": null, "ssn": null, "region": "Rural", "job_type": "Desk", "health_condition": "Fair", "chronic_condition": "N", "prescription_use": "None", "income_tier": "High (>$100K)", "risk_tolerance": "High", "lifestyle": "Active", "travel_frequency": "Rare", "hsa_familiarity": "Y", "mental_health_needs": "N", "predicted_plan_type": "HDHP + HSA", "plan_confidence": 0.8448315858840942, "plan_reason": "Young age - PPO for flexibility and future needs.", "top_3_plans": ["HDHP + HSA", "Indemnity", "PPO"], "top_3_plan_confidences": [0.8448315858840942, 0.09262076020240784, 0.06244511157274246], "predicted_benefits": ["Dental", "Vision"], "benefits_confidence": 0.5, "benefits_reason": "Default benefits assignment due to prediction error", "top_3_benefits": ["Dental", "Vision", "Hospital Indemnity"], "top_3_benefits_confidences": [0.5, 0.5, 0.5], "marketplace_plans_available": false, "plan_count": 0, "available_plan_types": null, "available_metal_levels": null, "estimated_premium_range": null, "marketplace_state_supported": null, "recommended_plan_id": null, "recommended_plan_name": null, "recommended_plan_issuer": null, "recommended_plan_premium": null, "recommended_plan_metal_level": null, "recommended_plan_type": null, "recommended_plan_deductible": null, "recommended_plan_moop": null, "recommended_plan_hsa_eligible": null, "recommended_plan_quality_rating": null, "top_3_available_plans": null, "benefits_emergency_room": null, "benefits_primary_care": null, "benefits_specialist_care": null, "benefits_prescription_drugs": null, "benefits_mental_health": null, "benefits_maternity_care": null, "benefits_preventive_care": null, "network_has_national": null, "network_referral_required": null, "api_processing_status": "no_plans_found", "api_error_message": "Invalid ZIP code: 30115-4910"}, {"gender": "Female", "dob": "7/25/1967", "marital_status": "Not Married", "address1": "165 <PERSON>", "city": "Fountain Inn", "state": "SC", "zipcode": "29644", "vision_plan": null, "dental_plan": null, "life_plan": "Y", "add_plan": "Y", "relationship": "Employee", "dept_1_first_name": null, "dept_1_last_name": null, "dept_1_gender": null, "dept_1_dob": null, "dept_1_address1": null, "dept_1_city": null, "dept_1_state": null, "dept_1_zipcode": null, "relationship_type_1": null, "dept_1": null, "dept_1_dental_plan": null, "dept_1_vision_plan": null, "dept_1_life_plan": null, "dept_1_add_plan": null, "dept_count": "0", "dept_2_first_name": null, "dept_2_last_name": null, "dept_2_gender": null, "dept_2_dob": null, "dept_2_address1": null, "dept_2_city": null, "dept_2_state": null, "dept_2_zipcode": null, "relationship_type_2": null, "dept_2": null, "dept_2_dental_plan": null, "dept_2_vision_plan": null, "dept_2_life_plan": null, "dept_2_add_plan": null, "dept_3_first_name": null, "dept_3_last_name": null, "dept_3_gender": null, "dept_3_dob": null, "dept_3_address1": null, "dept_3_city": null, "dept_3_state": null, "dept_3_zipcode": null, "relationship_type_3": null, "dept_3": null, "dept_3_dental_plan": null, "dept_3_vision_plan": null, "dept_3_life_plan": null, "dept_3_add_plan": null, "dept_4_first_name": null, "dept_4_last_name": null, "dept_4_gender": null, "dept_4_dob": null, "dept_4_address1": null, "dept_4_city": null, "dept_4_state": null, "dept_4_zipcode": null, "relationship_type_4": null, "dept_4": null, "dept_4_dental_plan": null, "dept_4_vision_plan": null, "dept_4_life_plan": null, "dept_4_add_plan": null, "name": "<PERSON>", "age": 57.0, "dept_1_age": null, "dept_2_age": null, "dept_3_age": null, "dept_4_age": null, "employee_id": "E014", "first_name": null, "middle_name": null, "last_name": null, "address2": null, "record_type": null, "salary": null, "employment_type": "Full-Time", "employee_class": null, "department": "Manufacturing", "hire_date": null, "tobacco_use": "N", "pregnancy_status": "N", "medical_plan": null, "coverage_tier": null, "ssn": null, "region": "Rural", "job_type": "Desk", "health_condition": "Good", "chronic_condition": "N", "prescription_use": "Occasional", "income_tier": "Low (<$50K)", "risk_tolerance": "Low", "lifestyle": "Sedentary", "travel_frequency": "Rare", "hsa_familiarity": "N", "mental_health_needs": "N", "predicted_plan_type": "HMO", "plan_confidence": 0.9720228910446167, "plan_reason": "Older age - PPO for comprehensive coverage and provider choice.", "top_3_plans": ["HMO", "PPO", "POS"], "top_3_plan_confidences": [0.9720228910446167, 0.02499540150165558, 0.001601709984242916], "predicted_benefits": ["Dental", "Vision"], "benefits_confidence": 0.5, "benefits_reason": "Default benefits assignment due to prediction error", "top_3_benefits": ["Dental", "Vision", "Hospital Indemnity"], "top_3_benefits_confidences": [0.5, 0.5, 0.5], "marketplace_plans_available": true, "plan_count": 3, "available_plan_types": null, "available_metal_levels": null, "estimated_premium_range": null, "marketplace_state_supported": null, "recommended_plan_id": "26065SC0710007", "recommended_plan_name": "Blue Reedy Standard Expanded Bronze", "recommended_plan_issuer": "BlueCross BlueShield of South Carolina", "recommended_plan_premium": 660.47, "recommended_plan_metal_level": "Bronze", "recommended_plan_type": "HMO", "recommended_plan_deductible": 7500, "recommended_plan_moop": 9200, "recommended_plan_hsa_eligible": false, "recommended_plan_quality_rating": 0, "top_3_available_plans": "[{'id': '26065SC0710007', 'name': 'Blue Reedy Standard Expanded Bronze', 'issuer': 'BlueCross BlueShield of South Carolina', 'premium': 660.47, 'premium_with_credit': 0, 'metal_level': 'Bronze', 'type': 'HMO', 'deductible': 7500, 'max_out_of_pocket': 9200, 'hsa_eligible': False, 'quality_rating': 0, 'has_national_network': False, 'key_benefits': {'specialist_care': '$100', 'emergency_room': '50% Coinsurance after deductible', 'primary_care': '$50', 'prescription_drugs': '$25'}, 'issuer_phone': '**************', 'urls': {'benefits': 'https://www.southcarolinablues.com/web/nonsecure/sc/resources/40d7de36-f3d5-4c59-ad55-52297f2fbddd/BCBS+-+Blue+Reedy+Standard+Expanded+Bronze+2025.pdf', 'brochure': 'https://www.southcarolinablues.com/links/2025/brochure/bluereedy', 'formulary': 'https://www.southcarolinablues.com/links/2025/pharmacy/premiumrx', 'network': 'https://shoppingforcare.sapphirethreesixtyfive.com/?ci=CP089'}}, {'id': '26065SC0710010', 'name': 'Blue Reedy Bronze 2', 'issuer': 'BlueCross BlueShield of South Carolina', 'premium': 708.48, 'premium_with_credit': 0, 'metal_level': 'Bronze', 'type': 'HMO', 'deductible': 9200, 'max_out_of_pocket': 9200, 'hsa_eligible': False, 'quality_rating': 0, 'has_national_network': False, 'key_benefits': {'prescription_drugs': 'No Charge After Deductible', 'specialist_care': 'No Charge After Deductible', 'emergency_room': 'No Charge After Deductible', 'primary_care': 'No Charge After Deductible'}, 'issuer_phone': '**************', 'urls': {'benefits': 'https://www.southcarolinablues.com/web/nonsecure/sc/resources/96fb340c-7073-47df-a851-0dfc9f3f25d4/BCBS+-+Blue+Reedy+Bronze+2+2025.pdf', 'brochure': 'https://www.southcarolinablues.com/links/2025/brochure/bluereedy', 'formulary': 'https://www.southcarolinablues.com/links/2025/pharmacy/premiumrx', 'network': 'https://shoppingforcare.sapphirethreesixtyfive.com/?ci=CP089'}}, {'id': '26065SC0710007', 'name': 'Blue Reedy Standard Expanded Bronze', 'issuer': 'BlueCross BlueShield of South Carolina', 'premium': 723.73, 'premium_with_credit': 0, 'metal_level': 'Bronze', 'type': 'HMO', 'deductible': 7500, 'max_out_of_pocket': 9200, 'hsa_eligible': False, 'quality_rating': 0, 'has_national_network': False, 'key_benefits': {'specialist_care': '$100', 'emergency_room': '50% Coinsurance after deductible', 'primary_care': '$50', 'prescription_drugs': '$25'}, 'issuer_phone': '**************', 'urls': {'benefits': 'https://www.southcarolinablues.com/web/nonsecure/sc/resources/40d7de36-f3d5-4c59-ad55-52297f2fbddd/BCBS+-+Blue+Reedy+Standard+Expanded+Bronze+2025.pdf', 'brochure': 'https://www.southcarolinablues.com/links/2025/brochure/bluereedy', 'formulary': 'https://www.southcarolinablues.com/links/2025/pharmacy/premiumrx', 'network': 'https://shoppingforcare.sapphirethreesixtyfive.com/?ci=CP089'}}]", "benefits_emergency_room": "50% Coinsurance after deductible", "benefits_primary_care": "$50", "benefits_specialist_care": "$100", "benefits_prescription_drugs": "$25", "benefits_mental_health": null, "benefits_maternity_care": null, "benefits_preventive_care": null, "network_has_national": false, "network_referral_required": false, "api_processing_status": "success", "api_error_message": null}, {"gender": "Female", "dob": "7/22/1973", "marital_status": "Married", "address1": "659 Diamond Ridge Way", "city": "<PERSON>", "state": "SC", "zipcode": "29334-8892", "vision_plan": "Y", "dental_plan": "Y", "life_plan": "Y", "add_plan": "Y", "relationship": "Employee", "dept_1_first_name": "<PERSON>", "dept_1_last_name": "<PERSON>", "dept_1_gender": "Male", "dept_1_dob": "6/28/1969", "dept_1_address1": "659 Diamond Ridge Way", "dept_1_city": "<PERSON>", "dept_1_state": "SC", "dept_1_zipcode": "29334-8892", "relationship_type_1": "Spouse", "dept_1": "<PERSON>", "dept_1_dental_plan": null, "dept_1_vision_plan": null, "dept_1_life_plan": null, "dept_1_add_plan": null, "dept_count": "1", "dept_2_first_name": null, "dept_2_last_name": null, "dept_2_gender": null, "dept_2_dob": null, "dept_2_address1": null, "dept_2_city": null, "dept_2_state": null, "dept_2_zipcode": null, "relationship_type_2": null, "dept_2": null, "dept_2_dental_plan": null, "dept_2_vision_plan": null, "dept_2_life_plan": null, "dept_2_add_plan": null, "dept_3_first_name": null, "dept_3_last_name": null, "dept_3_gender": null, "dept_3_dob": null, "dept_3_address1": null, "dept_3_city": null, "dept_3_state": null, "dept_3_zipcode": null, "relationship_type_3": null, "dept_3": null, "dept_3_dental_plan": null, "dept_3_vision_plan": null, "dept_3_life_plan": null, "dept_3_add_plan": null, "dept_4_first_name": null, "dept_4_last_name": null, "dept_4_gender": null, "dept_4_dob": null, "dept_4_address1": null, "dept_4_city": null, "dept_4_state": null, "dept_4_zipcode": null, "relationship_type_4": null, "dept_4": null, "dept_4_dental_plan": null, "dept_4_vision_plan": null, "dept_4_life_plan": null, "dept_4_add_plan": null, "name": "<PERSON>", "age": 51.0, "dept_1_age": 56.0, "dept_2_age": null, "dept_3_age": null, "dept_4_age": null, "employee_id": "E015", "first_name": null, "middle_name": null, "last_name": null, "address2": null, "record_type": null, "salary": null, "employment_type": "Full-Time", "employee_class": null, "department": "Engineering", "hire_date": null, "tobacco_use": "N", "pregnancy_status": "N", "medical_plan": null, "coverage_tier": null, "ssn": null, "region": "Rural", "job_type": "Desk", "health_condition": "Fair", "chronic_condition": "Y", "prescription_use": "Regular", "income_tier": "Medium ($50K–$100K)", "risk_tolerance": "Low", "lifestyle": "Moderate", "travel_frequency": "Rare", "hsa_familiarity": "Y", "mental_health_needs": "N", "predicted_plan_type": "HDHP + HSA", "plan_confidence": 0.9986280202865601, "plan_reason": "Chronic conditions, older age - POS for coordinated care management.", "top_3_plans": ["HDHP + HSA", "PPO", "POS"], "top_3_plan_confidences": [0.9986280202865601, 0.0011985941091552377, 0.00015518852160312235], "predicted_benefits": ["Dental", "Vision"], "benefits_confidence": 0.5, "benefits_reason": "Default benefits assignment due to prediction error", "top_3_benefits": ["Dental", "Vision", "Hospital Indemnity"], "top_3_benefits_confidences": [0.5, 0.5, 0.5], "marketplace_plans_available": false, "plan_count": 0, "available_plan_types": null, "available_metal_levels": null, "estimated_premium_range": null, "marketplace_state_supported": null, "recommended_plan_id": null, "recommended_plan_name": null, "recommended_plan_issuer": null, "recommended_plan_premium": null, "recommended_plan_metal_level": null, "recommended_plan_type": null, "recommended_plan_deductible": null, "recommended_plan_moop": null, "recommended_plan_hsa_eligible": null, "recommended_plan_quality_rating": null, "top_3_available_plans": null, "benefits_emergency_room": null, "benefits_primary_care": null, "benefits_specialist_care": null, "benefits_prescription_drugs": null, "benefits_mental_health": null, "benefits_maternity_care": null, "benefits_preventive_care": null, "network_has_national": null, "network_referral_required": null, "api_processing_status": "no_plans_found", "api_error_message": "Invalid ZIP code: 29334-8892"}, {"gender": "Female", "dob": "6/21/1978", "marital_status": "Not Married", "address1": "2705 Lawson Dr", "city": "<PERSON><PERSON><PERSON>", "state": "NC", "zipcode": "28173-7182", "vision_plan": null, "dental_plan": null, "life_plan": "Y", "add_plan": "Y", "relationship": "Employee", "dept_1_first_name": null, "dept_1_last_name": null, "dept_1_gender": null, "dept_1_dob": null, "dept_1_address1": null, "dept_1_city": null, "dept_1_state": null, "dept_1_zipcode": null, "relationship_type_1": null, "dept_1": null, "dept_1_dental_plan": null, "dept_1_vision_plan": null, "dept_1_life_plan": null, "dept_1_add_plan": null, "dept_count": "0", "dept_2_first_name": null, "dept_2_last_name": null, "dept_2_gender": null, "dept_2_dob": null, "dept_2_address1": null, "dept_2_city": null, "dept_2_state": null, "dept_2_zipcode": null, "relationship_type_2": null, "dept_2": null, "dept_2_dental_plan": null, "dept_2_vision_plan": null, "dept_2_life_plan": null, "dept_2_add_plan": null, "dept_3_first_name": null, "dept_3_last_name": null, "dept_3_gender": null, "dept_3_dob": null, "dept_3_address1": null, "dept_3_city": null, "dept_3_state": null, "dept_3_zipcode": null, "relationship_type_3": null, "dept_3": null, "dept_3_dental_plan": null, "dept_3_vision_plan": null, "dept_3_life_plan": null, "dept_3_add_plan": null, "dept_4_first_name": null, "dept_4_last_name": null, "dept_4_gender": null, "dept_4_dob": null, "dept_4_address1": null, "dept_4_city": null, "dept_4_state": null, "dept_4_zipcode": null, "relationship_type_4": null, "dept_4": null, "dept_4_dental_plan": null, "dept_4_vision_plan": null, "dept_4_life_plan": null, "dept_4_add_plan": null, "name": "<PERSON>", "age": 47.0, "dept_1_age": null, "dept_2_age": null, "dept_3_age": null, "dept_4_age": null, "employee_id": "E016", "first_name": null, "middle_name": null, "last_name": null, "address2": null, "record_type": null, "salary": null, "employment_type": "Full-Time", "employee_class": null, "department": "Information Technology", "hire_date": null, "tobacco_use": "N", "pregnancy_status": "N", "medical_plan": null, "coverage_tier": null, "ssn": null, "region": "Rural", "job_type": "Desk", "health_condition": "Fair", "chronic_condition": "Y", "prescription_use": "Regular", "income_tier": "High (>$100K)", "risk_tolerance": "Low", "lifestyle": "Moderate", "travel_frequency": "Rare", "hsa_familiarity": "Y", "mental_health_needs": "N", "predicted_plan_type": "HDHP + HSA", "plan_confidence": 0.9976467490196228, "plan_reason": "Chronic conditions, older age - POS for coordinated care management.", "top_3_plans": ["HDHP + HSA", "PPO", "HMO"], "top_3_plan_confidences": [0.9976467490196228, 0.0019097768235951662, 0.00037758355028927326], "predicted_benefits": ["Dental", "Vision"], "benefits_confidence": 0.5, "benefits_reason": "Default benefits assignment due to prediction error", "top_3_benefits": ["Dental", "Vision", "Hospital Indemnity"], "top_3_benefits_confidences": [0.5, 0.5, 0.5], "marketplace_plans_available": false, "plan_count": 0, "available_plan_types": null, "available_metal_levels": null, "estimated_premium_range": null, "marketplace_state_supported": null, "recommended_plan_id": null, "recommended_plan_name": null, "recommended_plan_issuer": null, "recommended_plan_premium": null, "recommended_plan_metal_level": null, "recommended_plan_type": null, "recommended_plan_deductible": null, "recommended_plan_moop": null, "recommended_plan_hsa_eligible": null, "recommended_plan_quality_rating": null, "top_3_available_plans": null, "benefits_emergency_room": null, "benefits_primary_care": null, "benefits_specialist_care": null, "benefits_prescription_drugs": null, "benefits_mental_health": null, "benefits_maternity_care": null, "benefits_preventive_care": null, "network_has_national": null, "network_referral_required": null, "api_processing_status": "no_plans_found", "api_error_message": "Invalid ZIP code: 28173-7182"}, {"gender": "Male", "dob": "3/7/1991", "marital_status": "Not Married", "address1": "1445 Brawley Circle NE", "city": "Atlanta", "state": "GA", "zipcode": "30319", "vision_plan": "Y", "dental_plan": null, "life_plan": "Y", "add_plan": "Y", "relationship": "Employee", "dept_1_first_name": null, "dept_1_last_name": null, "dept_1_gender": null, "dept_1_dob": null, "dept_1_address1": null, "dept_1_city": null, "dept_1_state": null, "dept_1_zipcode": null, "relationship_type_1": null, "dept_1": null, "dept_1_dental_plan": null, "dept_1_vision_plan": null, "dept_1_life_plan": null, "dept_1_add_plan": null, "dept_count": "0", "dept_2_first_name": null, "dept_2_last_name": null, "dept_2_gender": null, "dept_2_dob": null, "dept_2_address1": null, "dept_2_city": null, "dept_2_state": null, "dept_2_zipcode": null, "relationship_type_2": null, "dept_2": null, "dept_2_dental_plan": null, "dept_2_vision_plan": null, "dept_2_life_plan": null, "dept_2_add_plan": null, "dept_3_first_name": null, "dept_3_last_name": null, "dept_3_gender": null, "dept_3_dob": null, "dept_3_address1": null, "dept_3_city": null, "dept_3_state": null, "dept_3_zipcode": null, "relationship_type_3": null, "dept_3": null, "dept_3_dental_plan": null, "dept_3_vision_plan": null, "dept_3_life_plan": null, "dept_3_add_plan": null, "dept_4_first_name": null, "dept_4_last_name": null, "dept_4_gender": null, "dept_4_dob": null, "dept_4_address1": null, "dept_4_city": null, "dept_4_state": null, "dept_4_zipcode": null, "relationship_type_4": null, "dept_4": null, "dept_4_dental_plan": null, "dept_4_vision_plan": null, "dept_4_life_plan": null, "dept_4_add_plan": null, "name": "<PERSON>", "age": 34.0, "dept_1_age": null, "dept_2_age": null, "dept_3_age": null, "dept_4_age": null, "employee_id": "E017", "first_name": null, "middle_name": null, "last_name": null, "address2": null, "record_type": null, "salary": null, "employment_type": "Full-Time", "employee_class": null, "department": "Engineering", "hire_date": null, "tobacco_use": "Y", "pregnancy_status": "N", "medical_plan": null, "coverage_tier": null, "ssn": null, "region": "Urban", "job_type": "Desk", "health_condition": "Good", "chronic_condition": "Y", "prescription_use": "Regular", "income_tier": "Low (<$50K)", "risk_tolerance": "Low", "lifestyle": "Active", "travel_frequency": "Occasional", "hsa_familiarity": "N", "mental_health_needs": "N", "predicted_plan_type": "HMO", "plan_confidence": 0.8231197595596313, "plan_reason": "Low income, young age - DHMO for basic dental coverage.", "top_3_plans": ["HMO", "PPO", "MEC"], "top_3_plan_confidences": [0.8231197595596313, 0.17608267068862915, 0.0003599920601118356], "predicted_benefits": ["Dental", "Vision"], "benefits_confidence": 0.5, "benefits_reason": "Default benefits assignment due to prediction error", "top_3_benefits": ["Dental", "Vision", "Hospital Indemnity"], "top_3_benefits_confidences": [0.5, 0.5, 0.5], "marketplace_plans_available": false, "plan_count": 0, "available_plan_types": null, "available_metal_levels": null, "estimated_premium_range": null, "marketplace_state_supported": null, "recommended_plan_id": null, "recommended_plan_name": null, "recommended_plan_issuer": null, "recommended_plan_premium": null, "recommended_plan_metal_level": null, "recommended_plan_type": null, "recommended_plan_deductible": null, "recommended_plan_moop": null, "recommended_plan_hsa_eligible": null, "recommended_plan_quality_rating": null, "top_3_available_plans": null, "benefits_emergency_room": null, "benefits_primary_care": null, "benefits_specialist_care": null, "benefits_prescription_drugs": null, "benefits_mental_health": null, "benefits_maternity_care": null, "benefits_preventive_care": null, "network_has_national": null, "network_referral_required": null, "api_processing_status": "no_plans_found", "api_error_message": "No plans available"}, {"gender": "Female", "dob": "8/16/1978", "marital_status": "Not Married", "address1": "10 <PERSON>", "city": "Gaithersburg", "state": "MD", "zipcode": "20877-1801", "vision_plan": null, "dental_plan": "Y", "life_plan": "Y", "add_plan": "Y", "relationship": "Employee", "dept_1_first_name": null, "dept_1_last_name": null, "dept_1_gender": null, "dept_1_dob": null, "dept_1_address1": null, "dept_1_city": null, "dept_1_state": null, "dept_1_zipcode": null, "relationship_type_1": null, "dept_1": null, "dept_1_dental_plan": null, "dept_1_vision_plan": null, "dept_1_life_plan": null, "dept_1_add_plan": null, "dept_count": "0", "dept_2_first_name": null, "dept_2_last_name": null, "dept_2_gender": null, "dept_2_dob": null, "dept_2_address1": null, "dept_2_city": null, "dept_2_state": null, "dept_2_zipcode": null, "relationship_type_2": null, "dept_2": null, "dept_2_dental_plan": null, "dept_2_vision_plan": null, "dept_2_life_plan": null, "dept_2_add_plan": null, "dept_3_first_name": null, "dept_3_last_name": null, "dept_3_gender": null, "dept_3_dob": null, "dept_3_address1": null, "dept_3_city": null, "dept_3_state": null, "dept_3_zipcode": null, "relationship_type_3": null, "dept_3": null, "dept_3_dental_plan": null, "dept_3_vision_plan": null, "dept_3_life_plan": null, "dept_3_add_plan": null, "dept_4_first_name": null, "dept_4_last_name": null, "dept_4_gender": null, "dept_4_dob": null, "dept_4_address1": null, "dept_4_city": null, "dept_4_state": null, "dept_4_zipcode": null, "relationship_type_4": null, "dept_4": null, "dept_4_dental_plan": null, "dept_4_vision_plan": null, "dept_4_life_plan": null, "dept_4_add_plan": null, "name": "<PERSON>", "age": 46.0, "dept_1_age": null, "dept_2_age": null, "dept_3_age": null, "dept_4_age": null, "employee_id": "E018", "first_name": null, "middle_name": null, "last_name": null, "address2": null, "record_type": null, "salary": null, "employment_type": "Full-Time", "employee_class": null, "department": "Information Technology", "hire_date": null, "tobacco_use": "N", "pregnancy_status": "N", "medical_plan": null, "coverage_tier": null, "ssn": null, "region": "Rural", "job_type": "Desk", "health_condition": "Good", "chronic_condition": "Y", "prescription_use": "Regular", "income_tier": "Medium ($50K–$100K)", "risk_tolerance": "Low", "lifestyle": "Moderate", "travel_frequency": "Rare", "hsa_familiarity": "N", "mental_health_needs": "N", "predicted_plan_type": "PPO", "plan_confidence": 0.9887648820877075, "plan_reason": "Medium income, good health - PPO for balanced cost and flexibility.", "top_3_plans": ["PPO", "HMO", "HDHP + HSA"], "top_3_plan_confidences": [0.9887648820877075, 0.00945413950830698, 0.0010018039029091597], "predicted_benefits": ["Dental", "Vision"], "benefits_confidence": 0.5, "benefits_reason": "Default benefits assignment due to prediction error", "top_3_benefits": ["Dental", "Vision", "Hospital Indemnity"], "top_3_benefits_confidences": [0.5, 0.5, 0.5], "marketplace_plans_available": false, "plan_count": 0, "available_plan_types": null, "available_metal_levels": null, "estimated_premium_range": null, "marketplace_state_supported": null, "recommended_plan_id": null, "recommended_plan_name": null, "recommended_plan_issuer": null, "recommended_plan_premium": null, "recommended_plan_metal_level": null, "recommended_plan_type": null, "recommended_plan_deductible": null, "recommended_plan_moop": null, "recommended_plan_hsa_eligible": null, "recommended_plan_quality_rating": null, "top_3_available_plans": null, "benefits_emergency_room": null, "benefits_primary_care": null, "benefits_specialist_care": null, "benefits_prescription_drugs": null, "benefits_mental_health": null, "benefits_maternity_care": null, "benefits_preventive_care": null, "network_has_national": null, "network_referral_required": null, "api_processing_status": "no_plans_found", "api_error_message": "Invalid ZIP code: 20877-1801"}, {"gender": "Male", "dob": "12/3/2003", "marital_status": "Married", "address1": "10 <PERSON>", "city": "Gaithersburg", "state": "MD", "zipcode": "20877-1801", "vision_plan": null, "dental_plan": null, "life_plan": null, "add_plan": null, "relationship": "Child", "dept_1_first_name": null, "dept_1_last_name": null, "dept_1_gender": null, "dept_1_dob": null, "dept_1_address1": null, "dept_1_city": null, "dept_1_state": null, "dept_1_zipcode": null, "relationship_type_1": null, "dept_1": null, "dept_1_dental_plan": null, "dept_1_vision_plan": null, "dept_1_life_plan": null, "dept_1_add_plan": null, "dept_count": "0", "dept_2_first_name": null, "dept_2_last_name": null, "dept_2_gender": null, "dept_2_dob": null, "dept_2_address1": null, "dept_2_city": null, "dept_2_state": null, "dept_2_zipcode": null, "relationship_type_2": null, "dept_2": null, "dept_2_dental_plan": null, "dept_2_vision_plan": null, "dept_2_life_plan": null, "dept_2_add_plan": null, "dept_3_first_name": null, "dept_3_last_name": null, "dept_3_gender": null, "dept_3_dob": null, "dept_3_address1": null, "dept_3_city": null, "dept_3_state": null, "dept_3_zipcode": null, "relationship_type_3": null, "dept_3": null, "dept_3_dental_plan": null, "dept_3_vision_plan": null, "dept_3_life_plan": null, "dept_3_add_plan": null, "dept_4_first_name": null, "dept_4_last_name": null, "dept_4_gender": null, "dept_4_dob": null, "dept_4_address1": null, "dept_4_city": null, "dept_4_state": null, "dept_4_zipcode": null, "relationship_type_4": null, "dept_4": null, "dept_4_dental_plan": null, "dept_4_vision_plan": null, "dept_4_life_plan": null, "dept_4_add_plan": null, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "age": 21.0, "dept_1_age": null, "dept_2_age": null, "dept_3_age": null, "dept_4_age": null, "employee_id": "E019", "first_name": null, "middle_name": null, "last_name": null, "address2": null, "record_type": null, "salary": null, "employment_type": "Full-Time", "employee_class": null, "department": "Information Technology", "hire_date": null, "tobacco_use": "Y", "pregnancy_status": "N", "medical_plan": null, "coverage_tier": null, "ssn": null, "region": "Rural", "job_type": "Desk", "health_condition": "Good", "chronic_condition": "N", "prescription_use": "Occasional", "income_tier": "Medium ($50K–$100K)", "risk_tolerance": "Medium", "lifestyle": "Moderate", "travel_frequency": "Occasional", "hsa_familiarity": "N", "mental_health_needs": "Y", "predicted_plan_type": "HMO", "plan_confidence": 0.6106691956520081, "plan_reason": "Medium income, good health - PPO for balanced cost and flexibility.", "top_3_plans": ["HMO", "PPO", "HDHP"], "top_3_plan_confidences": [0.6106691956520081, 0.27660879492759705, 0.0777752473950386], "predicted_benefits": ["Dental", "Vision"], "benefits_confidence": 0.5, "benefits_reason": "Default benefits assignment due to prediction error", "top_3_benefits": ["Dental", "Vision", "Hospital Indemnity"], "top_3_benefits_confidences": [0.5, 0.5, 0.5], "marketplace_plans_available": false, "plan_count": 0, "available_plan_types": null, "available_metal_levels": null, "estimated_premium_range": null, "marketplace_state_supported": null, "recommended_plan_id": null, "recommended_plan_name": null, "recommended_plan_issuer": null, "recommended_plan_premium": null, "recommended_plan_metal_level": null, "recommended_plan_type": null, "recommended_plan_deductible": null, "recommended_plan_moop": null, "recommended_plan_hsa_eligible": null, "recommended_plan_quality_rating": null, "top_3_available_plans": null, "benefits_emergency_room": null, "benefits_primary_care": null, "benefits_specialist_care": null, "benefits_prescription_drugs": null, "benefits_mental_health": null, "benefits_maternity_care": null, "benefits_preventive_care": null, "network_has_national": null, "network_referral_required": null, "api_processing_status": "no_plans_found", "api_error_message": "Invalid ZIP code: 20877-1801"}, {"gender": "Female", "dob": "1/20/1998", "marital_status": "Married", "address1": "328 Quail Run Cir", "city": "Fountain Inn", "state": "SC", "zipcode": "29644-1408", "vision_plan": null, "dental_plan": "Y", "life_plan": "Y", "add_plan": "Y", "relationship": "Employee", "dept_1_first_name": "<PERSON>", "dept_1_last_name": "<PERSON>", "dept_1_gender": "Male", "dept_1_dob": "5/14/2021", "dept_1_address1": "328 Quail Run Cir", "dept_1_city": "Fountain Inn", "dept_1_state": "SC", "dept_1_zipcode": "29644-1408", "relationship_type_1": "Child", "dept_1": "<PERSON>", "dept_1_dental_plan": null, "dept_1_vision_plan": null, "dept_1_life_plan": null, "dept_1_add_plan": null, "dept_count": "1", "dept_2_first_name": null, "dept_2_last_name": null, "dept_2_gender": null, "dept_2_dob": null, "dept_2_address1": null, "dept_2_city": null, "dept_2_state": null, "dept_2_zipcode": null, "relationship_type_2": null, "dept_2": null, "dept_2_dental_plan": null, "dept_2_vision_plan": null, "dept_2_life_plan": null, "dept_2_add_plan": null, "dept_3_first_name": null, "dept_3_last_name": null, "dept_3_gender": null, "dept_3_dob": null, "dept_3_address1": null, "dept_3_city": null, "dept_3_state": null, "dept_3_zipcode": null, "relationship_type_3": null, "dept_3": null, "dept_3_dental_plan": null, "dept_3_vision_plan": null, "dept_3_life_plan": null, "dept_3_add_plan": null, "dept_4_first_name": null, "dept_4_last_name": null, "dept_4_gender": null, "dept_4_dob": null, "dept_4_address1": null, "dept_4_city": null, "dept_4_state": null, "dept_4_zipcode": null, "relationship_type_4": null, "dept_4": null, "dept_4_dental_plan": null, "dept_4_vision_plan": null, "dept_4_life_plan": null, "dept_4_add_plan": null, "name": "<PERSON><PERSON>", "age": 27.0, "dept_1_age": 4.0, "dept_2_age": null, "dept_3_age": null, "dept_4_age": null, "employee_id": "E020", "first_name": null, "middle_name": null, "last_name": null, "address2": null, "record_type": null, "salary": null, "employment_type": "Full-Time", "employee_class": null, "department": "Information Technology", "hire_date": null, "tobacco_use": "N", "pregnancy_status": "Y", "medical_plan": null, "coverage_tier": null, "ssn": null, "region": "Rural", "job_type": "Desk", "health_condition": "Good", "chronic_condition": "N", "prescription_use": "None", "income_tier": "Medium ($50K–$100K)", "risk_tolerance": "Medium", "lifestyle": "Moderate", "travel_frequency": "Occasional", "hsa_familiarity": "N", "mental_health_needs": "N", "predicted_plan_type": "HMO", "plan_confidence": 0.9758399128913879, "plan_reason": "Medium income, good health - PPO for balanced cost and flexibility.", "top_3_plans": ["HMO", "PPO", "POS"], "top_3_plan_confidences": [0.9758399128913879, 0.023566551506519318, 0.00031473528360947967], "predicted_benefits": ["Dental", "Vision"], "benefits_confidence": 0.5, "benefits_reason": "Default benefits assignment due to prediction error", "top_3_benefits": ["Dental", "Vision", "Hospital Indemnity"], "top_3_benefits_confidences": [0.5, 0.5, 0.5], "marketplace_plans_available": false, "plan_count": 0, "available_plan_types": null, "available_metal_levels": null, "estimated_premium_range": null, "marketplace_state_supported": null, "recommended_plan_id": null, "recommended_plan_name": null, "recommended_plan_issuer": null, "recommended_plan_premium": null, "recommended_plan_metal_level": null, "recommended_plan_type": null, "recommended_plan_deductible": null, "recommended_plan_moop": null, "recommended_plan_hsa_eligible": null, "recommended_plan_quality_rating": null, "top_3_available_plans": null, "benefits_emergency_room": null, "benefits_primary_care": null, "benefits_specialist_care": null, "benefits_prescription_drugs": null, "benefits_mental_health": null, "benefits_maternity_care": null, "benefits_preventive_care": null, "network_has_national": null, "network_referral_required": null, "api_processing_status": "no_plans_found", "api_error_message": "Invalid ZIP code: 29644-1408"}, {"gender": "Female", "dob": "9/11/1967", "marital_status": "Married", "address1": "7 Mallard Ct", "city": "Greenville", "state": "SC", "zipcode": "29617-6139", "vision_plan": null, "dental_plan": "Y", "life_plan": "Y", "add_plan": "Y", "relationship": "Employee", "dept_1_first_name": null, "dept_1_last_name": null, "dept_1_gender": null, "dept_1_dob": null, "dept_1_address1": null, "dept_1_city": null, "dept_1_state": null, "dept_1_zipcode": null, "relationship_type_1": null, "dept_1": null, "dept_1_dental_plan": null, "dept_1_vision_plan": null, "dept_1_life_plan": null, "dept_1_add_plan": null, "dept_count": "0", "dept_2_first_name": null, "dept_2_last_name": null, "dept_2_gender": null, "dept_2_dob": null, "dept_2_address1": null, "dept_2_city": null, "dept_2_state": null, "dept_2_zipcode": null, "relationship_type_2": null, "dept_2": null, "dept_2_dental_plan": null, "dept_2_vision_plan": null, "dept_2_life_plan": null, "dept_2_add_plan": null, "dept_3_first_name": null, "dept_3_last_name": null, "dept_3_gender": null, "dept_3_dob": null, "dept_3_address1": null, "dept_3_city": null, "dept_3_state": null, "dept_3_zipcode": null, "relationship_type_3": null, "dept_3": null, "dept_3_dental_plan": null, "dept_3_vision_plan": null, "dept_3_life_plan": null, "dept_3_add_plan": null, "dept_4_first_name": null, "dept_4_last_name": null, "dept_4_gender": null, "dept_4_dob": null, "dept_4_address1": null, "dept_4_city": null, "dept_4_state": null, "dept_4_zipcode": null, "relationship_type_4": null, "dept_4": null, "dept_4_dental_plan": null, "dept_4_vision_plan": null, "dept_4_life_plan": null, "dept_4_add_plan": null, "name": "<PERSON>", "age": 57.0, "dept_1_age": null, "dept_2_age": null, "dept_3_age": null, "dept_4_age": null, "employee_id": "E021", "first_name": null, "middle_name": null, "last_name": null, "address2": null, "record_type": null, "salary": null, "employment_type": "Full-Time", "employee_class": null, "department": "Finance", "hire_date": null, "tobacco_use": "N", "pregnancy_status": "N", "medical_plan": null, "coverage_tier": null, "ssn": null, "region": "Rural", "job_type": "Desk", "health_condition": "Fair", "chronic_condition": "N", "prescription_use": "None", "income_tier": "Medium ($50K–$100K)", "risk_tolerance": "Medium", "lifestyle": "Sedentary", "travel_frequency": "Occasional", "hsa_familiarity": "N", "mental_health_needs": "N", "predicted_plan_type": "PPO", "plan_confidence": 0.9399325847625732, "plan_reason": "Older age - PPO for comprehensive coverage and provider choice.", "top_3_plans": ["PPO", "POS", "HDHP + HSA"], "top_3_plan_confidences": [0.9399325847625732, 0.056685566902160645, 0.002088191220536828], "predicted_benefits": ["Dental", "Vision"], "benefits_confidence": 0.5, "benefits_reason": "Default benefits assignment due to prediction error", "top_3_benefits": ["Dental", "Vision", "Hospital Indemnity"], "top_3_benefits_confidences": [0.5, 0.5, 0.5], "marketplace_plans_available": false, "plan_count": 0, "available_plan_types": null, "available_metal_levels": null, "estimated_premium_range": null, "marketplace_state_supported": null, "recommended_plan_id": null, "recommended_plan_name": null, "recommended_plan_issuer": null, "recommended_plan_premium": null, "recommended_plan_metal_level": null, "recommended_plan_type": null, "recommended_plan_deductible": null, "recommended_plan_moop": null, "recommended_plan_hsa_eligible": null, "recommended_plan_quality_rating": null, "top_3_available_plans": null, "benefits_emergency_room": null, "benefits_primary_care": null, "benefits_specialist_care": null, "benefits_prescription_drugs": null, "benefits_mental_health": null, "benefits_maternity_care": null, "benefits_preventive_care": null, "network_has_national": null, "network_referral_required": null, "api_processing_status": "no_plans_found", "api_error_message": "Invalid ZIP code: 29617-6139"}, {"gender": "Male", "dob": "10/26/1968", "marital_status": "Married", "address1": "7 Mallard Ct", "city": "Greenville", "state": "SC", "zipcode": "29617-6139", "vision_plan": null, "dental_plan": null, "life_plan": null, "add_plan": null, "relationship": "Spouse", "dept_1_first_name": "<PERSON>", "dept_1_last_name": "<PERSON><PERSON><PERSON>", "dept_1_gender": "Male", "dept_1_dob": "7/21/2004", "dept_1_address1": "7 Mallard Ct", "dept_1_city": "Greenville", "dept_1_state": "SC", "dept_1_zipcode": "29617-6139", "relationship_type_1": "Child", "dept_1": "<PERSON>", "dept_1_dental_plan": null, "dept_1_vision_plan": null, "dept_1_life_plan": null, "dept_1_add_plan": null, "dept_count": "1", "dept_2_first_name": null, "dept_2_last_name": null, "dept_2_gender": null, "dept_2_dob": null, "dept_2_address1": null, "dept_2_city": null, "dept_2_state": null, "dept_2_zipcode": null, "relationship_type_2": null, "dept_2": null, "dept_2_dental_plan": null, "dept_2_vision_plan": null, "dept_2_life_plan": null, "dept_2_add_plan": null, "dept_3_first_name": null, "dept_3_last_name": null, "dept_3_gender": null, "dept_3_dob": null, "dept_3_address1": null, "dept_3_city": null, "dept_3_state": null, "dept_3_zipcode": null, "relationship_type_3": null, "dept_3": null, "dept_3_dental_plan": null, "dept_3_vision_plan": null, "dept_3_life_plan": null, "dept_3_add_plan": null, "dept_4_first_name": null, "dept_4_last_name": null, "dept_4_gender": null, "dept_4_dob": null, "dept_4_address1": null, "dept_4_city": null, "dept_4_state": null, "dept_4_zipcode": null, "relationship_type_4": null, "dept_4": null, "dept_4_dental_plan": null, "dept_4_vision_plan": null, "dept_4_life_plan": null, "dept_4_add_plan": null, "name": "<PERSON>", "age": 56.0, "dept_1_age": 20.0, "dept_2_age": null, "dept_3_age": null, "dept_4_age": null, "employee_id": "E022", "first_name": null, "middle_name": null, "last_name": null, "address2": null, "record_type": null, "salary": null, "employment_type": "Full-Time", "employee_class": null, "department": "Finance", "hire_date": null, "tobacco_use": "N", "pregnancy_status": "N", "medical_plan": null, "coverage_tier": null, "ssn": null, "region": "Rural", "job_type": "Desk", "health_condition": "Good", "chronic_condition": "Y", "prescription_use": "Regular", "income_tier": "Low (<$50K)", "risk_tolerance": "Medium", "lifestyle": "Moderate", "travel_frequency": "Rare", "hsa_familiarity": "N", "mental_health_needs": "Y", "predicted_plan_type": "PPO", "plan_confidence": 0.996066153049469, "plan_reason": "Chronic conditions, older age - POS for coordinated care management.", "top_3_plans": ["PPO", "HMO", "MEC"], "top_3_plan_confidences": [0.996066153049469, 0.002259364817291498, 0.000644010491669178], "predicted_benefits": ["Dental", "Vision"], "benefits_confidence": 0.5, "benefits_reason": "Default benefits assignment due to prediction error", "top_3_benefits": ["Dental", "Vision", "Hospital Indemnity"], "top_3_benefits_confidences": [0.5, 0.5, 0.5], "marketplace_plans_available": false, "plan_count": 0, "available_plan_types": null, "available_metal_levels": null, "estimated_premium_range": null, "marketplace_state_supported": null, "recommended_plan_id": null, "recommended_plan_name": null, "recommended_plan_issuer": null, "recommended_plan_premium": null, "recommended_plan_metal_level": null, "recommended_plan_type": null, "recommended_plan_deductible": null, "recommended_plan_moop": null, "recommended_plan_hsa_eligible": null, "recommended_plan_quality_rating": null, "top_3_available_plans": null, "benefits_emergency_room": null, "benefits_primary_care": null, "benefits_specialist_care": null, "benefits_prescription_drugs": null, "benefits_mental_health": null, "benefits_maternity_care": null, "benefits_preventive_care": null, "network_has_national": null, "network_referral_required": null, "api_processing_status": "no_plans_found", "api_error_message": "Invalid ZIP code: 29617-6139"}], "total_employees": 22, "total_columns": 140, "has_predictions": true, "prediction_columns": ["predicted_plan_type", "plan_confidence", "top_3_plan_confidences", "predicted_benefits", "benefits_confidence", "top_3_benefits_confidences"]}, "metadata": {"pipeline_version": "2.0_with_health_plans", "total_steps": 6, "steps_completed": ["parsing", "mapping", "pattern_identification", "preprocessing", "enrichment_prediction", "health_plan_integration"], "processing_order": ["step_1_parsing", "step_2_mapping", "step_3_pattern", "step_4_preprocessing", "step_5_enrichment_prediction", "step_6_health_plans"], "prediction_method": "ml_models", "health_plan_integration_success": true, "final_dataframe_shape": [22, 140], "processing_time_seconds": 0, "file_info": {"filename": "SBS-Census-Final.csv", "size": 3172, "rows": 34, "columns": 15, "original_column_names": ["First Name", "Last Name", "Sex", "DOB", "Marital Status", "Address 1", "City", "State", "Zip", "Vision", "Dental", "Safe Harbor", "Group Life", "Group AD&D", "Relationship"]}, "pattern_info": {"confidence": 0.8, "reason": "Found 'relationship' field; Found both employee and dependent relationship values"}, "mapping_result": {"success": true, "mapping": {"First Name": "first_name", "Last Name": "last_name", "Sex": "gender", "DOB": "dob", "Marital Status": "marital_status", "Address 1": "address1", "City": "city", "State": "state", "Zip": "zipcode", "Vision": "vision_plan", "Dental": "dental_plan", "Group Life": "life_plan", "Group AD&D": "add_plan", "Relationship": "relationship"}, "mapped_fields": ["first_name", "last_name", "gender", "dob", "marital_status", "address1", "city", "state", "zipcode", "vision_plan", "dental_plan", "life_plan", "add_plan", "relationship"], "unmapped_fields": ["Safe Harbor"], "validation": {"is_valid": true, "mapped_count": 14, "unmapped_count": 1}}, "preprocessing_result": {"success": true, "processed_data": [{"gender": "Male", "dob": "10/18/1960", "marital_status": "Married", "address1": "7 Regal Way", "city": "Simpsonville", "state": "SC", "zipcode": "29681", "vision_plan": "Y", "dental_plan": null, "life_plan": "Y", "add_plan": "Y", "relationship": "Employee", "dept_1_first_name": "<PERSON>", "dept_1_last_name": "<PERSON>", "dept_1_gender": "Female", "dept_1_dob": "1/31/1968", "dept_1_address1": "7 Regal Way", "dept_1_city": "Simpsonville", "dept_1_state": "SC", "dept_1_zipcode": "29681", "relationship_type_1": "Spouse", "dept_1": "<PERSON>", "dept_1_dental_plan": null, "dept_1_vision_plan": null, "dept_1_life_plan": null, "dept_1_add_plan": null, "dept_count": "1", "dept_2_first_name": null, "dept_2_last_name": null, "dept_2_gender": null, "dept_2_dob": null, "dept_2_address1": null, "dept_2_city": null, "dept_2_state": null, "dept_2_zipcode": null, "relationship_type_2": null, "dept_2": null, "dept_2_dental_plan": null, "dept_2_vision_plan": null, "dept_2_life_plan": null, "dept_2_add_plan": null, "dept_3_first_name": null, "dept_3_last_name": null, "dept_3_gender": null, "dept_3_dob": null, "dept_3_address1": null, "dept_3_city": null, "dept_3_state": null, "dept_3_zipcode": null, "relationship_type_3": null, "dept_3": null, "dept_3_dental_plan": null, "dept_3_vision_plan": null, "dept_3_life_plan": null, "dept_3_add_plan": null, "dept_4_first_name": null, "dept_4_last_name": null, "dept_4_gender": null, "dept_4_dob": null, "dept_4_address1": null, "dept_4_city": null, "dept_4_state": null, "dept_4_zipcode": null, "relationship_type_4": null, "dept_4": null, "dept_4_dental_plan": null, "dept_4_vision_plan": null, "dept_4_life_plan": null, "dept_4_add_plan": null, "name": "<PERSON>", "age": 64.0, "dept_1_age": 57.0, "dept_2_age": null, "dept_3_age": null, "dept_4_age": null, "employee_id": null, "first_name": null, "middle_name": null, "last_name": null, "address2": null, "record_type": null, "salary": null, "employment_type": null, "employee_class": null, "department": null, "hire_date": null, "tobacco_use": null, "pregnancy_status": null, "medical_plan": null, "coverage_tier": null, "ssn": null}, {"gender": "Male", "dob": "6/6/1991", "marital_status": "Married", "address1": "105 Campden Ct", "city": "<PERSON><PERSON><PERSON>", "state": "SC", "zipcode": "29642-9316", "vision_plan": null, "dental_plan": null, "life_plan": "Y", "add_plan": "Y", "relationship": "Employee", "dept_1_first_name": "<PERSON>", "dept_1_last_name": "<PERSON><PERSON>", "dept_1_gender": "Female", "dept_1_dob": "2/28/1997", "dept_1_address1": "105 Campden Ct", "dept_1_city": "<PERSON><PERSON><PERSON>", "dept_1_state": "SC", "dept_1_zipcode": "29642-9316", "relationship_type_1": "Spouse", "dept_1": "<PERSON>", "dept_1_dental_plan": null, "dept_1_vision_plan": null, "dept_1_life_plan": null, "dept_1_add_plan": null, "dept_count": "3+", "dept_2_first_name": "<PERSON><PERSON><PERSON>", "dept_2_last_name": "<PERSON><PERSON>", "dept_2_gender": "Female", "dept_2_dob": "2/18/2019", "dept_2_address1": "105 Campden Ct", "dept_2_city": "<PERSON><PERSON><PERSON>", "dept_2_state": "SC", "dept_2_zipcode": "29642-9316", "relationship_type_2": "Child", "dept_2": "<PERSON><PERSON><PERSON>", "dept_2_dental_plan": null, "dept_2_vision_plan": null, "dept_2_life_plan": null, "dept_2_add_plan": null, "dept_3_first_name": "<PERSON>", "dept_3_last_name": "<PERSON><PERSON>", "dept_3_gender": "Male", "dept_3_dob": "6/14/2021", "dept_3_address1": "105 Campden Ct", "dept_3_city": "<PERSON><PERSON><PERSON>", "dept_3_state": "SC", "dept_3_zipcode": "29642-9316", "relationship_type_3": "Child", "dept_3": "<PERSON>", "dept_3_dental_plan": null, "dept_3_vision_plan": null, "dept_3_life_plan": null, "dept_3_add_plan": null, "dept_4_first_name": "<PERSON>", "dept_4_last_name": "<PERSON><PERSON>", "dept_4_gender": "Male", "dept_4_dob": "6/17/2023", "dept_4_address1": "105 Campden Ct", "dept_4_city": "<PERSON><PERSON><PERSON>", "dept_4_state": "SC", "dept_4_zipcode": "29642-9316", "relationship_type_4": "Child", "dept_4": "<PERSON>", "dept_4_dental_plan": null, "dept_4_vision_plan": null, "dept_4_life_plan": null, "dept_4_add_plan": null, "name": "<PERSON>", "age": 34.0, "dept_1_age": 28.0, "dept_2_age": 6.0, "dept_3_age": 4.0, "dept_4_age": 2.0, "employee_id": null, "first_name": null, "middle_name": null, "last_name": null, "address2": null, "record_type": null, "salary": null, "employment_type": null, "employee_class": null, "department": null, "hire_date": null, "tobacco_use": null, "pregnancy_status": null, "medical_plan": null, "coverage_tier": null, "ssn": null}, {"gender": "Male", "dob": "9/14/1993", "marital_status": null, "address1": "3 Billings Mill Ct", "city": "Simpsonville", "state": "SC", "zipcode": "29680-7299", "vision_plan": "Y", "dental_plan": "Y", "life_plan": "Y", "add_plan": "Y", "relationship": "Employee", "dept_1_first_name": null, "dept_1_last_name": null, "dept_1_gender": null, "dept_1_dob": null, "dept_1_address1": null, "dept_1_city": null, "dept_1_state": null, "dept_1_zipcode": null, "relationship_type_1": null, "dept_1": null, "dept_1_dental_plan": null, "dept_1_vision_plan": null, "dept_1_life_plan": null, "dept_1_add_plan": null, "dept_count": "0", "dept_2_first_name": null, "dept_2_last_name": null, "dept_2_gender": null, "dept_2_dob": null, "dept_2_address1": null, "dept_2_city": null, "dept_2_state": null, "dept_2_zipcode": null, "relationship_type_2": null, "dept_2": null, "dept_2_dental_plan": null, "dept_2_vision_plan": null, "dept_2_life_plan": null, "dept_2_add_plan": null, "dept_3_first_name": null, "dept_3_last_name": null, "dept_3_gender": null, "dept_3_dob": null, "dept_3_address1": null, "dept_3_city": null, "dept_3_state": null, "dept_3_zipcode": null, "relationship_type_3": null, "dept_3": null, "dept_3_dental_plan": null, "dept_3_vision_plan": null, "dept_3_life_plan": null, "dept_3_add_plan": null, "dept_4_first_name": null, "dept_4_last_name": null, "dept_4_gender": null, "dept_4_dob": null, "dept_4_address1": null, "dept_4_city": null, "dept_4_state": null, "dept_4_zipcode": null, "relationship_type_4": null, "dept_4": null, "dept_4_dental_plan": null, "dept_4_vision_plan": null, "dept_4_life_plan": null, "dept_4_add_plan": null, "name": "<PERSON>", "age": 31.0, "dept_1_age": null, "dept_2_age": null, "dept_3_age": null, "dept_4_age": null, "employee_id": null, "first_name": null, "middle_name": null, "last_name": null, "address2": null, "record_type": null, "salary": null, "employment_type": null, "employee_class": null, "department": null, "hire_date": null, "tobacco_use": null, "pregnancy_status": null, "medical_plan": null, "coverage_tier": null, "ssn": null}, {"gender": "Male", "dob": "5/29/1988", "marital_status": "Married", "address1": "1501 Davis Rd", "city": "<PERSON><PERSON>", "state": "SC", "zipcode": "29388-8635", "vision_plan": null, "dental_plan": "Y", "life_plan": "Y", "add_plan": "Y", "relationship": "Employee", "dept_1_first_name": null, "dept_1_last_name": null, "dept_1_gender": null, "dept_1_dob": null, "dept_1_address1": null, "dept_1_city": null, "dept_1_state": null, "dept_1_zipcode": null, "relationship_type_1": null, "dept_1": null, "dept_1_dental_plan": null, "dept_1_vision_plan": null, "dept_1_life_plan": null, "dept_1_add_plan": null, "dept_count": "0", "dept_2_first_name": null, "dept_2_last_name": null, "dept_2_gender": null, "dept_2_dob": null, "dept_2_address1": null, "dept_2_city": null, "dept_2_state": null, "dept_2_zipcode": null, "relationship_type_2": null, "dept_2": null, "dept_2_dental_plan": null, "dept_2_vision_plan": null, "dept_2_life_plan": null, "dept_2_add_plan": null, "dept_3_first_name": null, "dept_3_last_name": null, "dept_3_gender": null, "dept_3_dob": null, "dept_3_address1": null, "dept_3_city": null, "dept_3_state": null, "dept_3_zipcode": null, "relationship_type_3": null, "dept_3": null, "dept_3_dental_plan": null, "dept_3_vision_plan": null, "dept_3_life_plan": null, "dept_3_add_plan": null, "dept_4_first_name": null, "dept_4_last_name": null, "dept_4_gender": null, "dept_4_dob": null, "dept_4_address1": null, "dept_4_city": null, "dept_4_state": null, "dept_4_zipcode": null, "relationship_type_4": null, "dept_4": null, "dept_4_dental_plan": null, "dept_4_vision_plan": null, "dept_4_life_plan": null, "dept_4_add_plan": null, "name": "<PERSON>", "age": 37.0, "dept_1_age": null, "dept_2_age": null, "dept_3_age": null, "dept_4_age": null, "employee_id": null, "first_name": null, "middle_name": null, "last_name": null, "address2": null, "record_type": null, "salary": null, "employment_type": null, "employee_class": null, "department": null, "hire_date": null, "tobacco_use": null, "pregnancy_status": null, "medical_plan": null, "coverage_tier": null, "ssn": null}, {"gender": "Male", "dob": "12/16/1980", "marital_status": null, "address1": "596 Collins Ave", "city": "<PERSON><PERSON><PERSON>", "state": "SC", "zipcode": "29576-6242", "vision_plan": null, "dental_plan": null, "life_plan": null, "add_plan": "Y", "relationship": "Employee", "dept_1_first_name": null, "dept_1_last_name": null, "dept_1_gender": null, "dept_1_dob": null, "dept_1_address1": null, "dept_1_city": null, "dept_1_state": null, "dept_1_zipcode": null, "relationship_type_1": null, "dept_1": null, "dept_1_dental_plan": null, "dept_1_vision_plan": null, "dept_1_life_plan": null, "dept_1_add_plan": null, "dept_count": "0", "dept_2_first_name": null, "dept_2_last_name": null, "dept_2_gender": null, "dept_2_dob": null, "dept_2_address1": null, "dept_2_city": null, "dept_2_state": null, "dept_2_zipcode": null, "relationship_type_2": null, "dept_2": null, "dept_2_dental_plan": null, "dept_2_vision_plan": null, "dept_2_life_plan": null, "dept_2_add_plan": null, "dept_3_first_name": null, "dept_3_last_name": null, "dept_3_gender": null, "dept_3_dob": null, "dept_3_address1": null, "dept_3_city": null, "dept_3_state": null, "dept_3_zipcode": null, "relationship_type_3": null, "dept_3": null, "dept_3_dental_plan": null, "dept_3_vision_plan": null, "dept_3_life_plan": null, "dept_3_add_plan": null, "dept_4_first_name": null, "dept_4_last_name": null, "dept_4_gender": null, "dept_4_dob": null, "dept_4_address1": null, "dept_4_city": null, "dept_4_state": null, "dept_4_zipcode": null, "relationship_type_4": null, "dept_4": null, "dept_4_dental_plan": null, "dept_4_vision_plan": null, "dept_4_life_plan": null, "dept_4_add_plan": null, "name": "<PERSON>", "age": 44.0, "dept_1_age": null, "dept_2_age": null, "dept_3_age": null, "dept_4_age": null, "employee_id": null, "first_name": null, "middle_name": null, "last_name": null, "address2": null, "record_type": null, "salary": null, "employment_type": null, "employee_class": null, "department": null, "hire_date": null, "tobacco_use": null, "pregnancy_status": null, "medical_plan": null, "coverage_tier": null, "ssn": null}, {"gender": "Male", "dob": "4/22/1986", "marital_status": null, "address1": "1311 Sloan Rd", "city": "Inman", "state": "SC", "zipcode": "29349-7843", "vision_plan": null, "dental_plan": null, "life_plan": null, "add_plan": null, "relationship": "Employee", "dept_1_first_name": null, "dept_1_last_name": null, "dept_1_gender": null, "dept_1_dob": null, "dept_1_address1": null, "dept_1_city": null, "dept_1_state": null, "dept_1_zipcode": null, "relationship_type_1": null, "dept_1": null, "dept_1_dental_plan": null, "dept_1_vision_plan": null, "dept_1_life_plan": null, "dept_1_add_plan": null, "dept_count": "0", "dept_2_first_name": null, "dept_2_last_name": null, "dept_2_gender": null, "dept_2_dob": null, "dept_2_address1": null, "dept_2_city": null, "dept_2_state": null, "dept_2_zipcode": null, "relationship_type_2": null, "dept_2": null, "dept_2_dental_plan": null, "dept_2_vision_plan": null, "dept_2_life_plan": null, "dept_2_add_plan": null, "dept_3_first_name": null, "dept_3_last_name": null, "dept_3_gender": null, "dept_3_dob": null, "dept_3_address1": null, "dept_3_city": null, "dept_3_state": null, "dept_3_zipcode": null, "relationship_type_3": null, "dept_3": null, "dept_3_dental_plan": null, "dept_3_vision_plan": null, "dept_3_life_plan": null, "dept_3_add_plan": null, "dept_4_first_name": null, "dept_4_last_name": null, "dept_4_gender": null, "dept_4_dob": null, "dept_4_address1": null, "dept_4_city": null, "dept_4_state": null, "dept_4_zipcode": null, "relationship_type_4": null, "dept_4": null, "dept_4_dental_plan": null, "dept_4_vision_plan": null, "dept_4_life_plan": null, "dept_4_add_plan": null, "name": "<PERSON>", "age": 39.0, "dept_1_age": null, "dept_2_age": null, "dept_3_age": null, "dept_4_age": null, "employee_id": null, "first_name": null, "middle_name": null, "last_name": null, "address2": null, "record_type": null, "salary": null, "employment_type": null, "employee_class": null, "department": null, "hire_date": null, "tobacco_use": null, "pregnancy_status": null, "medical_plan": null, "coverage_tier": null, "ssn": null}, {"gender": "Male", "dob": "8/11/1979", "marital_status": "Married", "address1": "207 Cammer Ave", "city": "Greenville", "state": "SC", "zipcode": "29605-1910", "vision_plan": null, "dental_plan": "Y", "life_plan": "Y", "add_plan": "Y", "relationship": "Employee", "dept_1_first_name": "<PERSON>", "dept_1_last_name": "Fox", "dept_1_gender": "Female", "dept_1_dob": "9/5/1976", "dept_1_address1": "207 Cammer Ave", "dept_1_city": "Greenville", "dept_1_state": "SC", "dept_1_zipcode": "29605-1910", "relationship_type_1": "Spouse", "dept_1": "<PERSON>", "dept_1_dental_plan": null, "dept_1_vision_plan": null, "dept_1_life_plan": null, "dept_1_add_plan": null, "dept_count": "3+", "dept_2_first_name": "Sienna", "dept_2_last_name": "Fox", "dept_2_gender": "Female", "dept_2_dob": "5/7/2006", "dept_2_address1": "207 Cammer Ave", "dept_2_city": "Greenville", "dept_2_state": "SC", "dept_2_zipcode": "29605-1910", "relationship_type_2": "Child", "dept_2": "<PERSON>", "dept_2_dental_plan": null, "dept_2_vision_plan": null, "dept_2_life_plan": null, "dept_2_add_plan": null, "dept_3_first_name": "Saddler", "dept_3_last_name": "Fox", "dept_3_gender": "Female", "dept_3_dob": "9/17/2008", "dept_3_address1": "207 Cammer Ave", "dept_3_city": "Greenville", "dept_3_state": "SC", "dept_3_zipcode": "29605-1910", "relationship_type_3": "Child", "dept_3": "Saddler <PERSON>", "dept_3_dental_plan": null, "dept_3_vision_plan": null, "dept_3_life_plan": null, "dept_3_add_plan": null, "dept_4_first_name": null, "dept_4_last_name": null, "dept_4_gender": null, "dept_4_dob": null, "dept_4_address1": null, "dept_4_city": null, "dept_4_state": null, "dept_4_zipcode": null, "relationship_type_4": null, "dept_4": null, "dept_4_dental_plan": null, "dept_4_vision_plan": null, "dept_4_life_plan": null, "dept_4_add_plan": null, "name": "<PERSON>", "age": 45.0, "dept_1_age": 48.0, "dept_2_age": 19.0, "dept_3_age": 16.0, "dept_4_age": null, "employee_id": null, "first_name": null, "middle_name": null, "last_name": null, "address2": null, "record_type": null, "salary": null, "employment_type": null, "employee_class": null, "department": null, "hire_date": null, "tobacco_use": null, "pregnancy_status": null, "medical_plan": null, "coverage_tier": null, "ssn": null}, {"gender": "Male", "dob": "12/25/1981", "marital_status": null, "address1": "39051 12th Ave", "city": "Zephyrhills", "state": "FL", "zipcode": "33542-3834", "vision_plan": null, "dental_plan": null, "life_plan": null, "add_plan": "Y", "relationship": "Employee", "dept_1_first_name": null, "dept_1_last_name": null, "dept_1_gender": null, "dept_1_dob": null, "dept_1_address1": null, "dept_1_city": null, "dept_1_state": null, "dept_1_zipcode": null, "relationship_type_1": null, "dept_1": null, "dept_1_dental_plan": null, "dept_1_vision_plan": null, "dept_1_life_plan": null, "dept_1_add_plan": null, "dept_count": "0", "dept_2_first_name": null, "dept_2_last_name": null, "dept_2_gender": null, "dept_2_dob": null, "dept_2_address1": null, "dept_2_city": null, "dept_2_state": null, "dept_2_zipcode": null, "relationship_type_2": null, "dept_2": null, "dept_2_dental_plan": null, "dept_2_vision_plan": null, "dept_2_life_plan": null, "dept_2_add_plan": null, "dept_3_first_name": null, "dept_3_last_name": null, "dept_3_gender": null, "dept_3_dob": null, "dept_3_address1": null, "dept_3_city": null, "dept_3_state": null, "dept_3_zipcode": null, "relationship_type_3": null, "dept_3": null, "dept_3_dental_plan": null, "dept_3_vision_plan": null, "dept_3_life_plan": null, "dept_3_add_plan": null, "dept_4_first_name": null, "dept_4_last_name": null, "dept_4_gender": null, "dept_4_dob": null, "dept_4_address1": null, "dept_4_city": null, "dept_4_state": null, "dept_4_zipcode": null, "relationship_type_4": null, "dept_4": null, "dept_4_dental_plan": null, "dept_4_vision_plan": null, "dept_4_life_plan": null, "dept_4_add_plan": null, "name": "<PERSON>", "age": 43.0, "dept_1_age": null, "dept_2_age": null, "dept_3_age": null, "dept_4_age": null, "employee_id": null, "first_name": null, "middle_name": null, "last_name": null, "address2": null, "record_type": null, "salary": null, "employment_type": null, "employee_class": null, "department": null, "hire_date": null, "tobacco_use": null, "pregnancy_status": null, "medical_plan": null, "coverage_tier": null, "ssn": null}, {"gender": "Male", "dob": "1/3/1992", "marital_status": null, "address1": "715 Oakview Farms Rd", "city": "<PERSON><PERSON>", "state": "SC", "zipcode": "29388-8309", "vision_plan": null, "dental_plan": "Y", "life_plan": "Y", "add_plan": "Y", "relationship": "Employee", "dept_1_first_name": null, "dept_1_last_name": null, "dept_1_gender": null, "dept_1_dob": null, "dept_1_address1": null, "dept_1_city": null, "dept_1_state": null, "dept_1_zipcode": null, "relationship_type_1": null, "dept_1": null, "dept_1_dental_plan": null, "dept_1_vision_plan": null, "dept_1_life_plan": null, "dept_1_add_plan": null, "dept_count": "0", "dept_2_first_name": null, "dept_2_last_name": null, "dept_2_gender": null, "dept_2_dob": null, "dept_2_address1": null, "dept_2_city": null, "dept_2_state": null, "dept_2_zipcode": null, "relationship_type_2": null, "dept_2": null, "dept_2_dental_plan": null, "dept_2_vision_plan": null, "dept_2_life_plan": null, "dept_2_add_plan": null, "dept_3_first_name": null, "dept_3_last_name": null, "dept_3_gender": null, "dept_3_dob": null, "dept_3_address1": null, "dept_3_city": null, "dept_3_state": null, "dept_3_zipcode": null, "relationship_type_3": null, "dept_3": null, "dept_3_dental_plan": null, "dept_3_vision_plan": null, "dept_3_life_plan": null, "dept_3_add_plan": null, "dept_4_first_name": null, "dept_4_last_name": null, "dept_4_gender": null, "dept_4_dob": null, "dept_4_address1": null, "dept_4_city": null, "dept_4_state": null, "dept_4_zipcode": null, "relationship_type_4": null, "dept_4": null, "dept_4_dental_plan": null, "dept_4_vision_plan": null, "dept_4_life_plan": null, "dept_4_add_plan": null, "name": "<PERSON>", "age": 33.0, "dept_1_age": null, "dept_2_age": null, "dept_3_age": null, "dept_4_age": null, "employee_id": null, "first_name": null, "middle_name": null, "last_name": null, "address2": null, "record_type": null, "salary": null, "employment_type": null, "employee_class": null, "department": null, "hire_date": null, "tobacco_use": null, "pregnancy_status": null, "medical_plan": null, "coverage_tier": null, "ssn": null}, {"gender": "Female", "dob": "7/22/2001", "marital_status": null, "address1": "7265 <PERSON>", "city": "Cincinnati", "state": "OH", "zipcode": "45243-2140", "vision_plan": null, "dental_plan": null, "life_plan": "Y", "add_plan": "Y", "relationship": "Employee", "dept_1_first_name": null, "dept_1_last_name": null, "dept_1_gender": null, "dept_1_dob": null, "dept_1_address1": null, "dept_1_city": null, "dept_1_state": null, "dept_1_zipcode": null, "relationship_type_1": null, "dept_1": null, "dept_1_dental_plan": null, "dept_1_vision_plan": null, "dept_1_life_plan": null, "dept_1_add_plan": null, "dept_count": "0", "dept_2_first_name": null, "dept_2_last_name": null, "dept_2_gender": null, "dept_2_dob": null, "dept_2_address1": null, "dept_2_city": null, "dept_2_state": null, "dept_2_zipcode": null, "relationship_type_2": null, "dept_2": null, "dept_2_dental_plan": null, "dept_2_vision_plan": null, "dept_2_life_plan": null, "dept_2_add_plan": null, "dept_3_first_name": null, "dept_3_last_name": null, "dept_3_gender": null, "dept_3_dob": null, "dept_3_address1": null, "dept_3_city": null, "dept_3_state": null, "dept_3_zipcode": null, "relationship_type_3": null, "dept_3": null, "dept_3_dental_plan": null, "dept_3_vision_plan": null, "dept_3_life_plan": null, "dept_3_add_plan": null, "dept_4_first_name": null, "dept_4_last_name": null, "dept_4_gender": null, "dept_4_dob": null, "dept_4_address1": null, "dept_4_city": null, "dept_4_state": null, "dept_4_zipcode": null, "relationship_type_4": null, "dept_4": null, "dept_4_dental_plan": null, "dept_4_vision_plan": null, "dept_4_life_plan": null, "dept_4_add_plan": null, "name": "<PERSON>", "age": 23.0, "dept_1_age": null, "dept_2_age": null, "dept_3_age": null, "dept_4_age": null, "employee_id": null, "first_name": null, "middle_name": null, "last_name": null, "address2": null, "record_type": null, "salary": null, "employment_type": null, "employee_class": null, "department": null, "hire_date": null, "tobacco_use": null, "pregnancy_status": null, "medical_plan": null, "coverage_tier": null, "ssn": null}, {"gender": "Male", "dob": "3/11/1974", "marital_status": null, "address1": "107 Culpepper Ln", "city": "<PERSON><PERSON><PERSON>", "state": "SC", "zipcode": "29642-8631", "vision_plan": null, "dental_plan": null, "life_plan": null, "add_plan": "Y", "relationship": "Employee", "dept_1_first_name": null, "dept_1_last_name": null, "dept_1_gender": null, "dept_1_dob": null, "dept_1_address1": null, "dept_1_city": null, "dept_1_state": null, "dept_1_zipcode": null, "relationship_type_1": null, "dept_1": null, "dept_1_dental_plan": null, "dept_1_vision_plan": null, "dept_1_life_plan": null, "dept_1_add_plan": null, "dept_count": "0", "dept_2_first_name": null, "dept_2_last_name": null, "dept_2_gender": null, "dept_2_dob": null, "dept_2_address1": null, "dept_2_city": null, "dept_2_state": null, "dept_2_zipcode": null, "relationship_type_2": null, "dept_2": null, "dept_2_dental_plan": null, "dept_2_vision_plan": null, "dept_2_life_plan": null, "dept_2_add_plan": null, "dept_3_first_name": null, "dept_3_last_name": null, "dept_3_gender": null, "dept_3_dob": null, "dept_3_address1": null, "dept_3_city": null, "dept_3_state": null, "dept_3_zipcode": null, "relationship_type_3": null, "dept_3": null, "dept_3_dental_plan": null, "dept_3_vision_plan": null, "dept_3_life_plan": null, "dept_3_add_plan": null, "dept_4_first_name": null, "dept_4_last_name": null, "dept_4_gender": null, "dept_4_dob": null, "dept_4_address1": null, "dept_4_city": null, "dept_4_state": null, "dept_4_zipcode": null, "relationship_type_4": null, "dept_4": null, "dept_4_dental_plan": null, "dept_4_vision_plan": null, "dept_4_life_plan": null, "dept_4_add_plan": null, "name": "<PERSON>", "age": 51.0, "dept_1_age": null, "dept_2_age": null, "dept_3_age": null, "dept_4_age": null, "employee_id": null, "first_name": null, "middle_name": null, "last_name": null, "address2": null, "record_type": null, "salary": null, "employment_type": null, "employee_class": null, "department": null, "hire_date": null, "tobacco_use": null, "pregnancy_status": null, "medical_plan": null, "coverage_tier": null, "ssn": null}, {"gender": "Male", "dob": "3/25/1996", "marital_status": "Married", "address1": "229 Crooked Creek Trl", "city": "Canton", "state": "GA", "zipcode": "30115-4910", "vision_plan": "Y", "dental_plan": "Y", "life_plan": "Y", "add_plan": "Y", "relationship": "Employee", "dept_1_first_name": "<PERSON>", "dept_1_last_name": "<PERSON><PERSON><PERSON>", "dept_1_gender": "Female", "dept_1_dob": "8/29/2024", "dept_1_address1": "229 Crooked Creek Trl", "dept_1_city": "Canton", "dept_1_state": "GA", "dept_1_zipcode": "30115-4910", "relationship_type_1": "Child", "dept_1": "<PERSON>", "dept_1_dental_plan": null, "dept_1_vision_plan": null, "dept_1_life_plan": null, "dept_1_add_plan": null, "dept_count": "1", "dept_2_first_name": null, "dept_2_last_name": null, "dept_2_gender": null, "dept_2_dob": null, "dept_2_address1": null, "dept_2_city": null, "dept_2_state": null, "dept_2_zipcode": null, "relationship_type_2": null, "dept_2": null, "dept_2_dental_plan": null, "dept_2_vision_plan": null, "dept_2_life_plan": null, "dept_2_add_plan": null, "dept_3_first_name": null, "dept_3_last_name": null, "dept_3_gender": null, "dept_3_dob": null, "dept_3_address1": null, "dept_3_city": null, "dept_3_state": null, "dept_3_zipcode": null, "relationship_type_3": null, "dept_3": null, "dept_3_dental_plan": null, "dept_3_vision_plan": null, "dept_3_life_plan": null, "dept_3_add_plan": null, "dept_4_first_name": null, "dept_4_last_name": null, "dept_4_gender": null, "dept_4_dob": null, "dept_4_address1": null, "dept_4_city": null, "dept_4_state": null, "dept_4_zipcode": null, "relationship_type_4": null, "dept_4": null, "dept_4_dental_plan": null, "dept_4_vision_plan": null, "dept_4_life_plan": null, "dept_4_add_plan": null, "name": "<PERSON>", "age": 29.0, "dept_1_age": 0.0, "dept_2_age": null, "dept_3_age": null, "dept_4_age": null, "employee_id": null, "first_name": null, "middle_name": null, "last_name": null, "address2": null, "record_type": null, "salary": null, "employment_type": null, "employee_class": null, "department": null, "hire_date": null, "tobacco_use": null, "pregnancy_status": null, "medical_plan": null, "coverage_tier": null, "ssn": null}, {"gender": "Female", "dob": "9/5/1998", "marital_status": null, "address1": "229 Crooked Creek Trl", "city": "Canton", "state": "GA", "zipcode": "30115-4910", "vision_plan": null, "dental_plan": null, "life_plan": null, "add_plan": null, "relationship": "Spouse", "dept_1_first_name": null, "dept_1_last_name": null, "dept_1_gender": null, "dept_1_dob": null, "dept_1_address1": null, "dept_1_city": null, "dept_1_state": null, "dept_1_zipcode": null, "relationship_type_1": null, "dept_1": null, "dept_1_dental_plan": null, "dept_1_vision_plan": null, "dept_1_life_plan": null, "dept_1_add_plan": null, "dept_count": "0", "dept_2_first_name": null, "dept_2_last_name": null, "dept_2_gender": null, "dept_2_dob": null, "dept_2_address1": null, "dept_2_city": null, "dept_2_state": null, "dept_2_zipcode": null, "relationship_type_2": null, "dept_2": null, "dept_2_dental_plan": null, "dept_2_vision_plan": null, "dept_2_life_plan": null, "dept_2_add_plan": null, "dept_3_first_name": null, "dept_3_last_name": null, "dept_3_gender": null, "dept_3_dob": null, "dept_3_address1": null, "dept_3_city": null, "dept_3_state": null, "dept_3_zipcode": null, "relationship_type_3": null, "dept_3": null, "dept_3_dental_plan": null, "dept_3_vision_plan": null, "dept_3_life_plan": null, "dept_3_add_plan": null, "dept_4_first_name": null, "dept_4_last_name": null, "dept_4_gender": null, "dept_4_dob": null, "dept_4_address1": null, "dept_4_city": null, "dept_4_state": null, "dept_4_zipcode": null, "relationship_type_4": null, "dept_4": null, "dept_4_dental_plan": null, "dept_4_vision_plan": null, "dept_4_life_plan": null, "dept_4_add_plan": null, "name": "<PERSON>", "age": 26.0, "dept_1_age": null, "dept_2_age": null, "dept_3_age": null, "dept_4_age": null, "employee_id": null, "first_name": null, "middle_name": null, "last_name": null, "address2": null, "record_type": null, "salary": null, "employment_type": null, "employee_class": null, "department": null, "hire_date": null, "tobacco_use": null, "pregnancy_status": null, "medical_plan": null, "coverage_tier": null, "ssn": null}, {"gender": "Female", "dob": "7/25/1967", "marital_status": null, "address1": "165 <PERSON>", "city": "Fountain Inn", "state": "SC", "zipcode": "29644", "vision_plan": null, "dental_plan": null, "life_plan": "Y", "add_plan": "Y", "relationship": "Employee", "dept_1_first_name": null, "dept_1_last_name": null, "dept_1_gender": null, "dept_1_dob": null, "dept_1_address1": null, "dept_1_city": null, "dept_1_state": null, "dept_1_zipcode": null, "relationship_type_1": null, "dept_1": null, "dept_1_dental_plan": null, "dept_1_vision_plan": null, "dept_1_life_plan": null, "dept_1_add_plan": null, "dept_count": "0", "dept_2_first_name": null, "dept_2_last_name": null, "dept_2_gender": null, "dept_2_dob": null, "dept_2_address1": null, "dept_2_city": null, "dept_2_state": null, "dept_2_zipcode": null, "relationship_type_2": null, "dept_2": null, "dept_2_dental_plan": null, "dept_2_vision_plan": null, "dept_2_life_plan": null, "dept_2_add_plan": null, "dept_3_first_name": null, "dept_3_last_name": null, "dept_3_gender": null, "dept_3_dob": null, "dept_3_address1": null, "dept_3_city": null, "dept_3_state": null, "dept_3_zipcode": null, "relationship_type_3": null, "dept_3": null, "dept_3_dental_plan": null, "dept_3_vision_plan": null, "dept_3_life_plan": null, "dept_3_add_plan": null, "dept_4_first_name": null, "dept_4_last_name": null, "dept_4_gender": null, "dept_4_dob": null, "dept_4_address1": null, "dept_4_city": null, "dept_4_state": null, "dept_4_zipcode": null, "relationship_type_4": null, "dept_4": null, "dept_4_dental_plan": null, "dept_4_vision_plan": null, "dept_4_life_plan": null, "dept_4_add_plan": null, "name": "<PERSON>", "age": 57.0, "dept_1_age": null, "dept_2_age": null, "dept_3_age": null, "dept_4_age": null, "employee_id": null, "first_name": null, "middle_name": null, "last_name": null, "address2": null, "record_type": null, "salary": null, "employment_type": null, "employee_class": null, "department": null, "hire_date": null, "tobacco_use": null, "pregnancy_status": null, "medical_plan": null, "coverage_tier": null, "ssn": null}, {"gender": "Female", "dob": "7/22/1973", "marital_status": "Married", "address1": "659 Diamond Ridge Way", "city": "<PERSON>", "state": "SC", "zipcode": "29334-8892", "vision_plan": "Y", "dental_plan": "Y", "life_plan": "Y", "add_plan": "Y", "relationship": "Employee", "dept_1_first_name": "<PERSON>", "dept_1_last_name": "<PERSON>", "dept_1_gender": "Male", "dept_1_dob": "6/28/1969", "dept_1_address1": "659 Diamond Ridge Way", "dept_1_city": "<PERSON>", "dept_1_state": "SC", "dept_1_zipcode": "29334-8892", "relationship_type_1": "Spouse", "dept_1": "<PERSON>", "dept_1_dental_plan": null, "dept_1_vision_plan": null, "dept_1_life_plan": null, "dept_1_add_plan": null, "dept_count": "1", "dept_2_first_name": null, "dept_2_last_name": null, "dept_2_gender": null, "dept_2_dob": null, "dept_2_address1": null, "dept_2_city": null, "dept_2_state": null, "dept_2_zipcode": null, "relationship_type_2": null, "dept_2": null, "dept_2_dental_plan": null, "dept_2_vision_plan": null, "dept_2_life_plan": null, "dept_2_add_plan": null, "dept_3_first_name": null, "dept_3_last_name": null, "dept_3_gender": null, "dept_3_dob": null, "dept_3_address1": null, "dept_3_city": null, "dept_3_state": null, "dept_3_zipcode": null, "relationship_type_3": null, "dept_3": null, "dept_3_dental_plan": null, "dept_3_vision_plan": null, "dept_3_life_plan": null, "dept_3_add_plan": null, "dept_4_first_name": null, "dept_4_last_name": null, "dept_4_gender": null, "dept_4_dob": null, "dept_4_address1": null, "dept_4_city": null, "dept_4_state": null, "dept_4_zipcode": null, "relationship_type_4": null, "dept_4": null, "dept_4_dental_plan": null, "dept_4_vision_plan": null, "dept_4_life_plan": null, "dept_4_add_plan": null, "name": "<PERSON>", "age": 51.0, "dept_1_age": 56.0, "dept_2_age": null, "dept_3_age": null, "dept_4_age": null, "employee_id": null, "first_name": null, "middle_name": null, "last_name": null, "address2": null, "record_type": null, "salary": null, "employment_type": null, "employee_class": null, "department": null, "hire_date": null, "tobacco_use": null, "pregnancy_status": null, "medical_plan": null, "coverage_tier": null, "ssn": null}, {"gender": "Female", "dob": "6/21/1978", "marital_status": null, "address1": "2705 Lawson Dr", "city": "<PERSON><PERSON><PERSON>", "state": "NC", "zipcode": "28173-7182", "vision_plan": null, "dental_plan": null, "life_plan": "Y", "add_plan": "Y", "relationship": "Employee", "dept_1_first_name": null, "dept_1_last_name": null, "dept_1_gender": null, "dept_1_dob": null, "dept_1_address1": null, "dept_1_city": null, "dept_1_state": null, "dept_1_zipcode": null, "relationship_type_1": null, "dept_1": null, "dept_1_dental_plan": null, "dept_1_vision_plan": null, "dept_1_life_plan": null, "dept_1_add_plan": null, "dept_count": "0", "dept_2_first_name": null, "dept_2_last_name": null, "dept_2_gender": null, "dept_2_dob": null, "dept_2_address1": null, "dept_2_city": null, "dept_2_state": null, "dept_2_zipcode": null, "relationship_type_2": null, "dept_2": null, "dept_2_dental_plan": null, "dept_2_vision_plan": null, "dept_2_life_plan": null, "dept_2_add_plan": null, "dept_3_first_name": null, "dept_3_last_name": null, "dept_3_gender": null, "dept_3_dob": null, "dept_3_address1": null, "dept_3_city": null, "dept_3_state": null, "dept_3_zipcode": null, "relationship_type_3": null, "dept_3": null, "dept_3_dental_plan": null, "dept_3_vision_plan": null, "dept_3_life_plan": null, "dept_3_add_plan": null, "dept_4_first_name": null, "dept_4_last_name": null, "dept_4_gender": null, "dept_4_dob": null, "dept_4_address1": null, "dept_4_city": null, "dept_4_state": null, "dept_4_zipcode": null, "relationship_type_4": null, "dept_4": null, "dept_4_dental_plan": null, "dept_4_vision_plan": null, "dept_4_life_plan": null, "dept_4_add_plan": null, "name": "<PERSON>", "age": 47.0, "dept_1_age": null, "dept_2_age": null, "dept_3_age": null, "dept_4_age": null, "employee_id": null, "first_name": null, "middle_name": null, "last_name": null, "address2": null, "record_type": null, "salary": null, "employment_type": null, "employee_class": null, "department": null, "hire_date": null, "tobacco_use": null, "pregnancy_status": null, "medical_plan": null, "coverage_tier": null, "ssn": null}, {"gender": "Male", "dob": "3/7/1991", "marital_status": null, "address1": "1445 Brawley Circle NE", "city": "Atlanta", "state": "GA", "zipcode": "30319", "vision_plan": "Y", "dental_plan": null, "life_plan": "Y", "add_plan": "Y", "relationship": "Employee", "dept_1_first_name": null, "dept_1_last_name": null, "dept_1_gender": null, "dept_1_dob": null, "dept_1_address1": null, "dept_1_city": null, "dept_1_state": null, "dept_1_zipcode": null, "relationship_type_1": null, "dept_1": null, "dept_1_dental_plan": null, "dept_1_vision_plan": null, "dept_1_life_plan": null, "dept_1_add_plan": null, "dept_count": "0", "dept_2_first_name": null, "dept_2_last_name": null, "dept_2_gender": null, "dept_2_dob": null, "dept_2_address1": null, "dept_2_city": null, "dept_2_state": null, "dept_2_zipcode": null, "relationship_type_2": null, "dept_2": null, "dept_2_dental_plan": null, "dept_2_vision_plan": null, "dept_2_life_plan": null, "dept_2_add_plan": null, "dept_3_first_name": null, "dept_3_last_name": null, "dept_3_gender": null, "dept_3_dob": null, "dept_3_address1": null, "dept_3_city": null, "dept_3_state": null, "dept_3_zipcode": null, "relationship_type_3": null, "dept_3": null, "dept_3_dental_plan": null, "dept_3_vision_plan": null, "dept_3_life_plan": null, "dept_3_add_plan": null, "dept_4_first_name": null, "dept_4_last_name": null, "dept_4_gender": null, "dept_4_dob": null, "dept_4_address1": null, "dept_4_city": null, "dept_4_state": null, "dept_4_zipcode": null, "relationship_type_4": null, "dept_4": null, "dept_4_dental_plan": null, "dept_4_vision_plan": null, "dept_4_life_plan": null, "dept_4_add_plan": null, "name": "<PERSON>", "age": 34.0, "dept_1_age": null, "dept_2_age": null, "dept_3_age": null, "dept_4_age": null, "employee_id": null, "first_name": null, "middle_name": null, "last_name": null, "address2": null, "record_type": null, "salary": null, "employment_type": null, "employee_class": null, "department": null, "hire_date": null, "tobacco_use": null, "pregnancy_status": null, "medical_plan": null, "coverage_tier": null, "ssn": null}, {"gender": "Female", "dob": "8/16/1978", "marital_status": null, "address1": "10 <PERSON>", "city": "Gaithersburg", "state": "MD", "zipcode": "20877-1801", "vision_plan": null, "dental_plan": "Y", "life_plan": "Y", "add_plan": "Y", "relationship": "Employee", "dept_1_first_name": null, "dept_1_last_name": null, "dept_1_gender": null, "dept_1_dob": null, "dept_1_address1": null, "dept_1_city": null, "dept_1_state": null, "dept_1_zipcode": null, "relationship_type_1": null, "dept_1": null, "dept_1_dental_plan": null, "dept_1_vision_plan": null, "dept_1_life_plan": null, "dept_1_add_plan": null, "dept_count": "0", "dept_2_first_name": null, "dept_2_last_name": null, "dept_2_gender": null, "dept_2_dob": null, "dept_2_address1": null, "dept_2_city": null, "dept_2_state": null, "dept_2_zipcode": null, "relationship_type_2": null, "dept_2": null, "dept_2_dental_plan": null, "dept_2_vision_plan": null, "dept_2_life_plan": null, "dept_2_add_plan": null, "dept_3_first_name": null, "dept_3_last_name": null, "dept_3_gender": null, "dept_3_dob": null, "dept_3_address1": null, "dept_3_city": null, "dept_3_state": null, "dept_3_zipcode": null, "relationship_type_3": null, "dept_3": null, "dept_3_dental_plan": null, "dept_3_vision_plan": null, "dept_3_life_plan": null, "dept_3_add_plan": null, "dept_4_first_name": null, "dept_4_last_name": null, "dept_4_gender": null, "dept_4_dob": null, "dept_4_address1": null, "dept_4_city": null, "dept_4_state": null, "dept_4_zipcode": null, "relationship_type_4": null, "dept_4": null, "dept_4_dental_plan": null, "dept_4_vision_plan": null, "dept_4_life_plan": null, "dept_4_add_plan": null, "name": "<PERSON>", "age": 46.0, "dept_1_age": null, "dept_2_age": null, "dept_3_age": null, "dept_4_age": null, "employee_id": null, "first_name": null, "middle_name": null, "last_name": null, "address2": null, "record_type": null, "salary": null, "employment_type": null, "employee_class": null, "department": null, "hire_date": null, "tobacco_use": null, "pregnancy_status": null, "medical_plan": null, "coverage_tier": null, "ssn": null}, {"gender": "Male", "dob": "12/3/2003", "marital_status": null, "address1": "10 <PERSON>", "city": "Gaithersburg", "state": "MD", "zipcode": "20877-1801", "vision_plan": null, "dental_plan": null, "life_plan": null, "add_plan": null, "relationship": "Child", "dept_1_first_name": null, "dept_1_last_name": null, "dept_1_gender": null, "dept_1_dob": null, "dept_1_address1": null, "dept_1_city": null, "dept_1_state": null, "dept_1_zipcode": null, "relationship_type_1": null, "dept_1": null, "dept_1_dental_plan": null, "dept_1_vision_plan": null, "dept_1_life_plan": null, "dept_1_add_plan": null, "dept_count": "0", "dept_2_first_name": null, "dept_2_last_name": null, "dept_2_gender": null, "dept_2_dob": null, "dept_2_address1": null, "dept_2_city": null, "dept_2_state": null, "dept_2_zipcode": null, "relationship_type_2": null, "dept_2": null, "dept_2_dental_plan": null, "dept_2_vision_plan": null, "dept_2_life_plan": null, "dept_2_add_plan": null, "dept_3_first_name": null, "dept_3_last_name": null, "dept_3_gender": null, "dept_3_dob": null, "dept_3_address1": null, "dept_3_city": null, "dept_3_state": null, "dept_3_zipcode": null, "relationship_type_3": null, "dept_3": null, "dept_3_dental_plan": null, "dept_3_vision_plan": null, "dept_3_life_plan": null, "dept_3_add_plan": null, "dept_4_first_name": null, "dept_4_last_name": null, "dept_4_gender": null, "dept_4_dob": null, "dept_4_address1": null, "dept_4_city": null, "dept_4_state": null, "dept_4_zipcode": null, "relationship_type_4": null, "dept_4": null, "dept_4_dental_plan": null, "dept_4_vision_plan": null, "dept_4_life_plan": null, "dept_4_add_plan": null, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "age": 21.0, "dept_1_age": null, "dept_2_age": null, "dept_3_age": null, "dept_4_age": null, "employee_id": null, "first_name": null, "middle_name": null, "last_name": null, "address2": null, "record_type": null, "salary": null, "employment_type": null, "employee_class": null, "department": null, "hire_date": null, "tobacco_use": null, "pregnancy_status": null, "medical_plan": null, "coverage_tier": null, "ssn": null}, {"gender": "Female", "dob": "1/20/1998", "marital_status": null, "address1": "328 Quail Run Cir", "city": "Fountain Inn", "state": "SC", "zipcode": "29644-1408", "vision_plan": null, "dental_plan": "Y", "life_plan": "Y", "add_plan": "Y", "relationship": "Employee", "dept_1_first_name": "<PERSON>", "dept_1_last_name": "<PERSON>", "dept_1_gender": "Male", "dept_1_dob": "5/14/2021", "dept_1_address1": "328 Quail Run Cir", "dept_1_city": "Fountain Inn", "dept_1_state": "SC", "dept_1_zipcode": "29644-1408", "relationship_type_1": "Child", "dept_1": "<PERSON>", "dept_1_dental_plan": null, "dept_1_vision_plan": null, "dept_1_life_plan": null, "dept_1_add_plan": null, "dept_count": "1", "dept_2_first_name": null, "dept_2_last_name": null, "dept_2_gender": null, "dept_2_dob": null, "dept_2_address1": null, "dept_2_city": null, "dept_2_state": null, "dept_2_zipcode": null, "relationship_type_2": null, "dept_2": null, "dept_2_dental_plan": null, "dept_2_vision_plan": null, "dept_2_life_plan": null, "dept_2_add_plan": null, "dept_3_first_name": null, "dept_3_last_name": null, "dept_3_gender": null, "dept_3_dob": null, "dept_3_address1": null, "dept_3_city": null, "dept_3_state": null, "dept_3_zipcode": null, "relationship_type_3": null, "dept_3": null, "dept_3_dental_plan": null, "dept_3_vision_plan": null, "dept_3_life_plan": null, "dept_3_add_plan": null, "dept_4_first_name": null, "dept_4_last_name": null, "dept_4_gender": null, "dept_4_dob": null, "dept_4_address1": null, "dept_4_city": null, "dept_4_state": null, "dept_4_zipcode": null, "relationship_type_4": null, "dept_4": null, "dept_4_dental_plan": null, "dept_4_vision_plan": null, "dept_4_life_plan": null, "dept_4_add_plan": null, "name": "<PERSON><PERSON>", "age": 27.0, "dept_1_age": 4.0, "dept_2_age": null, "dept_3_age": null, "dept_4_age": null, "employee_id": null, "first_name": null, "middle_name": null, "last_name": null, "address2": null, "record_type": null, "salary": null, "employment_type": null, "employee_class": null, "department": null, "hire_date": null, "tobacco_use": null, "pregnancy_status": null, "medical_plan": null, "coverage_tier": null, "ssn": null}, {"gender": "Female", "dob": "9/11/1967", "marital_status": "Married", "address1": "7 Mallard Ct", "city": "Greenville", "state": "SC", "zipcode": "29617-6139", "vision_plan": null, "dental_plan": "Y", "life_plan": "Y", "add_plan": "Y", "relationship": "Employee", "dept_1_first_name": null, "dept_1_last_name": null, "dept_1_gender": null, "dept_1_dob": null, "dept_1_address1": null, "dept_1_city": null, "dept_1_state": null, "dept_1_zipcode": null, "relationship_type_1": null, "dept_1": null, "dept_1_dental_plan": null, "dept_1_vision_plan": null, "dept_1_life_plan": null, "dept_1_add_plan": null, "dept_count": "0", "dept_2_first_name": null, "dept_2_last_name": null, "dept_2_gender": null, "dept_2_dob": null, "dept_2_address1": null, "dept_2_city": null, "dept_2_state": null, "dept_2_zipcode": null, "relationship_type_2": null, "dept_2": null, "dept_2_dental_plan": null, "dept_2_vision_plan": null, "dept_2_life_plan": null, "dept_2_add_plan": null, "dept_3_first_name": null, "dept_3_last_name": null, "dept_3_gender": null, "dept_3_dob": null, "dept_3_address1": null, "dept_3_city": null, "dept_3_state": null, "dept_3_zipcode": null, "relationship_type_3": null, "dept_3": null, "dept_3_dental_plan": null, "dept_3_vision_plan": null, "dept_3_life_plan": null, "dept_3_add_plan": null, "dept_4_first_name": null, "dept_4_last_name": null, "dept_4_gender": null, "dept_4_dob": null, "dept_4_address1": null, "dept_4_city": null, "dept_4_state": null, "dept_4_zipcode": null, "relationship_type_4": null, "dept_4": null, "dept_4_dental_plan": null, "dept_4_vision_plan": null, "dept_4_life_plan": null, "dept_4_add_plan": null, "name": "<PERSON>", "age": 57.0, "dept_1_age": null, "dept_2_age": null, "dept_3_age": null, "dept_4_age": null, "employee_id": null, "first_name": null, "middle_name": null, "last_name": null, "address2": null, "record_type": null, "salary": null, "employment_type": null, "employee_class": null, "department": null, "hire_date": null, "tobacco_use": null, "pregnancy_status": null, "medical_plan": null, "coverage_tier": null, "ssn": null}, {"gender": "Male", "dob": "10/26/1968", "marital_status": null, "address1": "7 Mallard Ct", "city": "Greenville", "state": "SC", "zipcode": "29617-6139", "vision_plan": null, "dental_plan": null, "life_plan": null, "add_plan": null, "relationship": "Spouse", "dept_1_first_name": "<PERSON>", "dept_1_last_name": "<PERSON><PERSON><PERSON>", "dept_1_gender": "Male", "dept_1_dob": "7/21/2004", "dept_1_address1": "7 Mallard Ct", "dept_1_city": "Greenville", "dept_1_state": "SC", "dept_1_zipcode": "29617-6139", "relationship_type_1": "Child", "dept_1": "<PERSON>", "dept_1_dental_plan": null, "dept_1_vision_plan": null, "dept_1_life_plan": null, "dept_1_add_plan": null, "dept_count": "1", "dept_2_first_name": null, "dept_2_last_name": null, "dept_2_gender": null, "dept_2_dob": null, "dept_2_address1": null, "dept_2_city": null, "dept_2_state": null, "dept_2_zipcode": null, "relationship_type_2": null, "dept_2": null, "dept_2_dental_plan": null, "dept_2_vision_plan": null, "dept_2_life_plan": null, "dept_2_add_plan": null, "dept_3_first_name": null, "dept_3_last_name": null, "dept_3_gender": null, "dept_3_dob": null, "dept_3_address1": null, "dept_3_city": null, "dept_3_state": null, "dept_3_zipcode": null, "relationship_type_3": null, "dept_3": null, "dept_3_dental_plan": null, "dept_3_vision_plan": null, "dept_3_life_plan": null, "dept_3_add_plan": null, "dept_4_first_name": null, "dept_4_last_name": null, "dept_4_gender": null, "dept_4_dob": null, "dept_4_address1": null, "dept_4_city": null, "dept_4_state": null, "dept_4_zipcode": null, "relationship_type_4": null, "dept_4": null, "dept_4_dental_plan": null, "dept_4_vision_plan": null, "dept_4_life_plan": null, "dept_4_add_plan": null, "name": "<PERSON>", "age": 56.0, "dept_1_age": 20.0, "dept_2_age": null, "dept_3_age": null, "dept_4_age": null, "employee_id": null, "first_name": null, "middle_name": null, "last_name": null, "address2": null, "record_type": null, "salary": null, "employment_type": null, "employee_class": null, "department": null, "hire_date": null, "tobacco_use": null, "pregnancy_status": null, "medical_plan": null, "coverage_tier": null, "ssn": null}], "preprocessed_data": [{"gender": "Male", "dob": "10/18/1960", "marital_status": "Married", "address1": "7 Regal Way", "city": "Simpsonville", "state": "SC", "zipcode": "29681", "vision_plan": "Y", "dental_plan": null, "life_plan": "Y", "add_plan": "Y", "relationship": "Employee", "dept_1_first_name": "<PERSON>", "dept_1_last_name": "<PERSON>", "dept_1_gender": "Female", "dept_1_dob": "1/31/1968", "dept_1_address1": "7 Regal Way", "dept_1_city": "Simpsonville", "dept_1_state": "SC", "dept_1_zipcode": "29681", "relationship_type_1": "Spouse", "dept_1": "<PERSON>", "dept_1_dental_plan": null, "dept_1_vision_plan": null, "dept_1_life_plan": null, "dept_1_add_plan": null, "dept_count": "1", "dept_2_first_name": null, "dept_2_last_name": null, "dept_2_gender": null, "dept_2_dob": null, "dept_2_address1": null, "dept_2_city": null, "dept_2_state": null, "dept_2_zipcode": null, "relationship_type_2": null, "dept_2": null, "dept_2_dental_plan": null, "dept_2_vision_plan": null, "dept_2_life_plan": null, "dept_2_add_plan": null, "dept_3_first_name": null, "dept_3_last_name": null, "dept_3_gender": null, "dept_3_dob": null, "dept_3_address1": null, "dept_3_city": null, "dept_3_state": null, "dept_3_zipcode": null, "relationship_type_3": null, "dept_3": null, "dept_3_dental_plan": null, "dept_3_vision_plan": null, "dept_3_life_plan": null, "dept_3_add_plan": null, "dept_4_first_name": null, "dept_4_last_name": null, "dept_4_gender": null, "dept_4_dob": null, "dept_4_address1": null, "dept_4_city": null, "dept_4_state": null, "dept_4_zipcode": null, "relationship_type_4": null, "dept_4": null, "dept_4_dental_plan": null, "dept_4_vision_plan": null, "dept_4_life_plan": null, "dept_4_add_plan": null, "name": "<PERSON>", "age": 64.0, "dept_1_age": 57.0, "dept_2_age": null, "dept_3_age": null, "dept_4_age": null, "employee_id": null, "first_name": null, "middle_name": null, "last_name": null, "address2": null, "record_type": null, "salary": null, "employment_type": null, "employee_class": null, "department": null, "hire_date": null, "tobacco_use": null, "pregnancy_status": null, "medical_plan": null, "coverage_tier": null, "ssn": null}, {"gender": "Male", "dob": "6/6/1991", "marital_status": "Married", "address1": "105 Campden Ct", "city": "<PERSON><PERSON><PERSON>", "state": "SC", "zipcode": "29642-9316", "vision_plan": null, "dental_plan": null, "life_plan": "Y", "add_plan": "Y", "relationship": "Employee", "dept_1_first_name": "<PERSON>", "dept_1_last_name": "<PERSON><PERSON>", "dept_1_gender": "Female", "dept_1_dob": "2/28/1997", "dept_1_address1": "105 Campden Ct", "dept_1_city": "<PERSON><PERSON><PERSON>", "dept_1_state": "SC", "dept_1_zipcode": "29642-9316", "relationship_type_1": "Spouse", "dept_1": "<PERSON>", "dept_1_dental_plan": null, "dept_1_vision_plan": null, "dept_1_life_plan": null, "dept_1_add_plan": null, "dept_count": "3+", "dept_2_first_name": "<PERSON><PERSON><PERSON>", "dept_2_last_name": "<PERSON><PERSON>", "dept_2_gender": "Female", "dept_2_dob": "2/18/2019", "dept_2_address1": "105 Campden Ct", "dept_2_city": "<PERSON><PERSON><PERSON>", "dept_2_state": "SC", "dept_2_zipcode": "29642-9316", "relationship_type_2": "Child", "dept_2": "<PERSON><PERSON><PERSON>", "dept_2_dental_plan": null, "dept_2_vision_plan": null, "dept_2_life_plan": null, "dept_2_add_plan": null, "dept_3_first_name": "<PERSON>", "dept_3_last_name": "<PERSON><PERSON>", "dept_3_gender": "Male", "dept_3_dob": "6/14/2021", "dept_3_address1": "105 Campden Ct", "dept_3_city": "<PERSON><PERSON><PERSON>", "dept_3_state": "SC", "dept_3_zipcode": "29642-9316", "relationship_type_3": "Child", "dept_3": "<PERSON>", "dept_3_dental_plan": null, "dept_3_vision_plan": null, "dept_3_life_plan": null, "dept_3_add_plan": null, "dept_4_first_name": "<PERSON>", "dept_4_last_name": "<PERSON><PERSON>", "dept_4_gender": "Male", "dept_4_dob": "6/17/2023", "dept_4_address1": "105 Campden Ct", "dept_4_city": "<PERSON><PERSON><PERSON>", "dept_4_state": "SC", "dept_4_zipcode": "29642-9316", "relationship_type_4": "Child", "dept_4": "<PERSON>", "dept_4_dental_plan": null, "dept_4_vision_plan": null, "dept_4_life_plan": null, "dept_4_add_plan": null, "name": "<PERSON>", "age": 34.0, "dept_1_age": 28.0, "dept_2_age": 6.0, "dept_3_age": 4.0, "dept_4_age": 2.0, "employee_id": null, "first_name": null, "middle_name": null, "last_name": null, "address2": null, "record_type": null, "salary": null, "employment_type": null, "employee_class": null, "department": null, "hire_date": null, "tobacco_use": null, "pregnancy_status": null, "medical_plan": null, "coverage_tier": null, "ssn": null}, {"gender": "Male", "dob": "9/14/1993", "marital_status": null, "address1": "3 Billings Mill Ct", "city": "Simpsonville", "state": "SC", "zipcode": "29680-7299", "vision_plan": "Y", "dental_plan": "Y", "life_plan": "Y", "add_plan": "Y", "relationship": "Employee", "dept_1_first_name": null, "dept_1_last_name": null, "dept_1_gender": null, "dept_1_dob": null, "dept_1_address1": null, "dept_1_city": null, "dept_1_state": null, "dept_1_zipcode": null, "relationship_type_1": null, "dept_1": null, "dept_1_dental_plan": null, "dept_1_vision_plan": null, "dept_1_life_plan": null, "dept_1_add_plan": null, "dept_count": "0", "dept_2_first_name": null, "dept_2_last_name": null, "dept_2_gender": null, "dept_2_dob": null, "dept_2_address1": null, "dept_2_city": null, "dept_2_state": null, "dept_2_zipcode": null, "relationship_type_2": null, "dept_2": null, "dept_2_dental_plan": null, "dept_2_vision_plan": null, "dept_2_life_plan": null, "dept_2_add_plan": null, "dept_3_first_name": null, "dept_3_last_name": null, "dept_3_gender": null, "dept_3_dob": null, "dept_3_address1": null, "dept_3_city": null, "dept_3_state": null, "dept_3_zipcode": null, "relationship_type_3": null, "dept_3": null, "dept_3_dental_plan": null, "dept_3_vision_plan": null, "dept_3_life_plan": null, "dept_3_add_plan": null, "dept_4_first_name": null, "dept_4_last_name": null, "dept_4_gender": null, "dept_4_dob": null, "dept_4_address1": null, "dept_4_city": null, "dept_4_state": null, "dept_4_zipcode": null, "relationship_type_4": null, "dept_4": null, "dept_4_dental_plan": null, "dept_4_vision_plan": null, "dept_4_life_plan": null, "dept_4_add_plan": null, "name": "<PERSON>", "age": 31.0, "dept_1_age": null, "dept_2_age": null, "dept_3_age": null, "dept_4_age": null, "employee_id": null, "first_name": null, "middle_name": null, "last_name": null, "address2": null, "record_type": null, "salary": null, "employment_type": null, "employee_class": null, "department": null, "hire_date": null, "tobacco_use": null, "pregnancy_status": null, "medical_plan": null, "coverage_tier": null, "ssn": null}, {"gender": "Male", "dob": "5/29/1988", "marital_status": "Married", "address1": "1501 Davis Rd", "city": "<PERSON><PERSON>", "state": "SC", "zipcode": "29388-8635", "vision_plan": null, "dental_plan": "Y", "life_plan": "Y", "add_plan": "Y", "relationship": "Employee", "dept_1_first_name": null, "dept_1_last_name": null, "dept_1_gender": null, "dept_1_dob": null, "dept_1_address1": null, "dept_1_city": null, "dept_1_state": null, "dept_1_zipcode": null, "relationship_type_1": null, "dept_1": null, "dept_1_dental_plan": null, "dept_1_vision_plan": null, "dept_1_life_plan": null, "dept_1_add_plan": null, "dept_count": "0", "dept_2_first_name": null, "dept_2_last_name": null, "dept_2_gender": null, "dept_2_dob": null, "dept_2_address1": null, "dept_2_city": null, "dept_2_state": null, "dept_2_zipcode": null, "relationship_type_2": null, "dept_2": null, "dept_2_dental_plan": null, "dept_2_vision_plan": null, "dept_2_life_plan": null, "dept_2_add_plan": null, "dept_3_first_name": null, "dept_3_last_name": null, "dept_3_gender": null, "dept_3_dob": null, "dept_3_address1": null, "dept_3_city": null, "dept_3_state": null, "dept_3_zipcode": null, "relationship_type_3": null, "dept_3": null, "dept_3_dental_plan": null, "dept_3_vision_plan": null, "dept_3_life_plan": null, "dept_3_add_plan": null, "dept_4_first_name": null, "dept_4_last_name": null, "dept_4_gender": null, "dept_4_dob": null, "dept_4_address1": null, "dept_4_city": null, "dept_4_state": null, "dept_4_zipcode": null, "relationship_type_4": null, "dept_4": null, "dept_4_dental_plan": null, "dept_4_vision_plan": null, "dept_4_life_plan": null, "dept_4_add_plan": null, "name": "<PERSON>", "age": 37.0, "dept_1_age": null, "dept_2_age": null, "dept_3_age": null, "dept_4_age": null, "employee_id": null, "first_name": null, "middle_name": null, "last_name": null, "address2": null, "record_type": null, "salary": null, "employment_type": null, "employee_class": null, "department": null, "hire_date": null, "tobacco_use": null, "pregnancy_status": null, "medical_plan": null, "coverage_tier": null, "ssn": null}, {"gender": "Male", "dob": "12/16/1980", "marital_status": null, "address1": "596 Collins Ave", "city": "<PERSON><PERSON><PERSON>", "state": "SC", "zipcode": "29576-6242", "vision_plan": null, "dental_plan": null, "life_plan": null, "add_plan": "Y", "relationship": "Employee", "dept_1_first_name": null, "dept_1_last_name": null, "dept_1_gender": null, "dept_1_dob": null, "dept_1_address1": null, "dept_1_city": null, "dept_1_state": null, "dept_1_zipcode": null, "relationship_type_1": null, "dept_1": null, "dept_1_dental_plan": null, "dept_1_vision_plan": null, "dept_1_life_plan": null, "dept_1_add_plan": null, "dept_count": "0", "dept_2_first_name": null, "dept_2_last_name": null, "dept_2_gender": null, "dept_2_dob": null, "dept_2_address1": null, "dept_2_city": null, "dept_2_state": null, "dept_2_zipcode": null, "relationship_type_2": null, "dept_2": null, "dept_2_dental_plan": null, "dept_2_vision_plan": null, "dept_2_life_plan": null, "dept_2_add_plan": null, "dept_3_first_name": null, "dept_3_last_name": null, "dept_3_gender": null, "dept_3_dob": null, "dept_3_address1": null, "dept_3_city": null, "dept_3_state": null, "dept_3_zipcode": null, "relationship_type_3": null, "dept_3": null, "dept_3_dental_plan": null, "dept_3_vision_plan": null, "dept_3_life_plan": null, "dept_3_add_plan": null, "dept_4_first_name": null, "dept_4_last_name": null, "dept_4_gender": null, "dept_4_dob": null, "dept_4_address1": null, "dept_4_city": null, "dept_4_state": null, "dept_4_zipcode": null, "relationship_type_4": null, "dept_4": null, "dept_4_dental_plan": null, "dept_4_vision_plan": null, "dept_4_life_plan": null, "dept_4_add_plan": null, "name": "<PERSON>", "age": 44.0, "dept_1_age": null, "dept_2_age": null, "dept_3_age": null, "dept_4_age": null, "employee_id": null, "first_name": null, "middle_name": null, "last_name": null, "address2": null, "record_type": null, "salary": null, "employment_type": null, "employee_class": null, "department": null, "hire_date": null, "tobacco_use": null, "pregnancy_status": null, "medical_plan": null, "coverage_tier": null, "ssn": null}, {"gender": "Male", "dob": "4/22/1986", "marital_status": null, "address1": "1311 Sloan Rd", "city": "Inman", "state": "SC", "zipcode": "29349-7843", "vision_plan": null, "dental_plan": null, "life_plan": null, "add_plan": null, "relationship": "Employee", "dept_1_first_name": null, "dept_1_last_name": null, "dept_1_gender": null, "dept_1_dob": null, "dept_1_address1": null, "dept_1_city": null, "dept_1_state": null, "dept_1_zipcode": null, "relationship_type_1": null, "dept_1": null, "dept_1_dental_plan": null, "dept_1_vision_plan": null, "dept_1_life_plan": null, "dept_1_add_plan": null, "dept_count": "0", "dept_2_first_name": null, "dept_2_last_name": null, "dept_2_gender": null, "dept_2_dob": null, "dept_2_address1": null, "dept_2_city": null, "dept_2_state": null, "dept_2_zipcode": null, "relationship_type_2": null, "dept_2": null, "dept_2_dental_plan": null, "dept_2_vision_plan": null, "dept_2_life_plan": null, "dept_2_add_plan": null, "dept_3_first_name": null, "dept_3_last_name": null, "dept_3_gender": null, "dept_3_dob": null, "dept_3_address1": null, "dept_3_city": null, "dept_3_state": null, "dept_3_zipcode": null, "relationship_type_3": null, "dept_3": null, "dept_3_dental_plan": null, "dept_3_vision_plan": null, "dept_3_life_plan": null, "dept_3_add_plan": null, "dept_4_first_name": null, "dept_4_last_name": null, "dept_4_gender": null, "dept_4_dob": null, "dept_4_address1": null, "dept_4_city": null, "dept_4_state": null, "dept_4_zipcode": null, "relationship_type_4": null, "dept_4": null, "dept_4_dental_plan": null, "dept_4_vision_plan": null, "dept_4_life_plan": null, "dept_4_add_plan": null, "name": "<PERSON>", "age": 39.0, "dept_1_age": null, "dept_2_age": null, "dept_3_age": null, "dept_4_age": null, "employee_id": null, "first_name": null, "middle_name": null, "last_name": null, "address2": null, "record_type": null, "salary": null, "employment_type": null, "employee_class": null, "department": null, "hire_date": null, "tobacco_use": null, "pregnancy_status": null, "medical_plan": null, "coverage_tier": null, "ssn": null}, {"gender": "Male", "dob": "8/11/1979", "marital_status": "Married", "address1": "207 Cammer Ave", "city": "Greenville", "state": "SC", "zipcode": "29605-1910", "vision_plan": null, "dental_plan": "Y", "life_plan": "Y", "add_plan": "Y", "relationship": "Employee", "dept_1_first_name": "<PERSON>", "dept_1_last_name": "Fox", "dept_1_gender": "Female", "dept_1_dob": "9/5/1976", "dept_1_address1": "207 Cammer Ave", "dept_1_city": "Greenville", "dept_1_state": "SC", "dept_1_zipcode": "29605-1910", "relationship_type_1": "Spouse", "dept_1": "<PERSON>", "dept_1_dental_plan": null, "dept_1_vision_plan": null, "dept_1_life_plan": null, "dept_1_add_plan": null, "dept_count": "3+", "dept_2_first_name": "Sienna", "dept_2_last_name": "Fox", "dept_2_gender": "Female", "dept_2_dob": "5/7/2006", "dept_2_address1": "207 Cammer Ave", "dept_2_city": "Greenville", "dept_2_state": "SC", "dept_2_zipcode": "29605-1910", "relationship_type_2": "Child", "dept_2": "<PERSON>", "dept_2_dental_plan": null, "dept_2_vision_plan": null, "dept_2_life_plan": null, "dept_2_add_plan": null, "dept_3_first_name": "Saddler", "dept_3_last_name": "Fox", "dept_3_gender": "Female", "dept_3_dob": "9/17/2008", "dept_3_address1": "207 Cammer Ave", "dept_3_city": "Greenville", "dept_3_state": "SC", "dept_3_zipcode": "29605-1910", "relationship_type_3": "Child", "dept_3": "Saddler <PERSON>", "dept_3_dental_plan": null, "dept_3_vision_plan": null, "dept_3_life_plan": null, "dept_3_add_plan": null, "dept_4_first_name": null, "dept_4_last_name": null, "dept_4_gender": null, "dept_4_dob": null, "dept_4_address1": null, "dept_4_city": null, "dept_4_state": null, "dept_4_zipcode": null, "relationship_type_4": null, "dept_4": null, "dept_4_dental_plan": null, "dept_4_vision_plan": null, "dept_4_life_plan": null, "dept_4_add_plan": null, "name": "<PERSON>", "age": 45.0, "dept_1_age": 48.0, "dept_2_age": 19.0, "dept_3_age": 16.0, "dept_4_age": null, "employee_id": null, "first_name": null, "middle_name": null, "last_name": null, "address2": null, "record_type": null, "salary": null, "employment_type": null, "employee_class": null, "department": null, "hire_date": null, "tobacco_use": null, "pregnancy_status": null, "medical_plan": null, "coverage_tier": null, "ssn": null}, {"gender": "Male", "dob": "12/25/1981", "marital_status": null, "address1": "39051 12th Ave", "city": "Zephyrhills", "state": "FL", "zipcode": "33542-3834", "vision_plan": null, "dental_plan": null, "life_plan": null, "add_plan": "Y", "relationship": "Employee", "dept_1_first_name": null, "dept_1_last_name": null, "dept_1_gender": null, "dept_1_dob": null, "dept_1_address1": null, "dept_1_city": null, "dept_1_state": null, "dept_1_zipcode": null, "relationship_type_1": null, "dept_1": null, "dept_1_dental_plan": null, "dept_1_vision_plan": null, "dept_1_life_plan": null, "dept_1_add_plan": null, "dept_count": "0", "dept_2_first_name": null, "dept_2_last_name": null, "dept_2_gender": null, "dept_2_dob": null, "dept_2_address1": null, "dept_2_city": null, "dept_2_state": null, "dept_2_zipcode": null, "relationship_type_2": null, "dept_2": null, "dept_2_dental_plan": null, "dept_2_vision_plan": null, "dept_2_life_plan": null, "dept_2_add_plan": null, "dept_3_first_name": null, "dept_3_last_name": null, "dept_3_gender": null, "dept_3_dob": null, "dept_3_address1": null, "dept_3_city": null, "dept_3_state": null, "dept_3_zipcode": null, "relationship_type_3": null, "dept_3": null, "dept_3_dental_plan": null, "dept_3_vision_plan": null, "dept_3_life_plan": null, "dept_3_add_plan": null, "dept_4_first_name": null, "dept_4_last_name": null, "dept_4_gender": null, "dept_4_dob": null, "dept_4_address1": null, "dept_4_city": null, "dept_4_state": null, "dept_4_zipcode": null, "relationship_type_4": null, "dept_4": null, "dept_4_dental_plan": null, "dept_4_vision_plan": null, "dept_4_life_plan": null, "dept_4_add_plan": null, "name": "<PERSON>", "age": 43.0, "dept_1_age": null, "dept_2_age": null, "dept_3_age": null, "dept_4_age": null, "employee_id": null, "first_name": null, "middle_name": null, "last_name": null, "address2": null, "record_type": null, "salary": null, "employment_type": null, "employee_class": null, "department": null, "hire_date": null, "tobacco_use": null, "pregnancy_status": null, "medical_plan": null, "coverage_tier": null, "ssn": null}, {"gender": "Male", "dob": "1/3/1992", "marital_status": null, "address1": "715 Oakview Farms Rd", "city": "<PERSON><PERSON>", "state": "SC", "zipcode": "29388-8309", "vision_plan": null, "dental_plan": "Y", "life_plan": "Y", "add_plan": "Y", "relationship": "Employee", "dept_1_first_name": null, "dept_1_last_name": null, "dept_1_gender": null, "dept_1_dob": null, "dept_1_address1": null, "dept_1_city": null, "dept_1_state": null, "dept_1_zipcode": null, "relationship_type_1": null, "dept_1": null, "dept_1_dental_plan": null, "dept_1_vision_plan": null, "dept_1_life_plan": null, "dept_1_add_plan": null, "dept_count": "0", "dept_2_first_name": null, "dept_2_last_name": null, "dept_2_gender": null, "dept_2_dob": null, "dept_2_address1": null, "dept_2_city": null, "dept_2_state": null, "dept_2_zipcode": null, "relationship_type_2": null, "dept_2": null, "dept_2_dental_plan": null, "dept_2_vision_plan": null, "dept_2_life_plan": null, "dept_2_add_plan": null, "dept_3_first_name": null, "dept_3_last_name": null, "dept_3_gender": null, "dept_3_dob": null, "dept_3_address1": null, "dept_3_city": null, "dept_3_state": null, "dept_3_zipcode": null, "relationship_type_3": null, "dept_3": null, "dept_3_dental_plan": null, "dept_3_vision_plan": null, "dept_3_life_plan": null, "dept_3_add_plan": null, "dept_4_first_name": null, "dept_4_last_name": null, "dept_4_gender": null, "dept_4_dob": null, "dept_4_address1": null, "dept_4_city": null, "dept_4_state": null, "dept_4_zipcode": null, "relationship_type_4": null, "dept_4": null, "dept_4_dental_plan": null, "dept_4_vision_plan": null, "dept_4_life_plan": null, "dept_4_add_plan": null, "name": "<PERSON>", "age": 33.0, "dept_1_age": null, "dept_2_age": null, "dept_3_age": null, "dept_4_age": null, "employee_id": null, "first_name": null, "middle_name": null, "last_name": null, "address2": null, "record_type": null, "salary": null, "employment_type": null, "employee_class": null, "department": null, "hire_date": null, "tobacco_use": null, "pregnancy_status": null, "medical_plan": null, "coverage_tier": null, "ssn": null}, {"gender": "Female", "dob": "7/22/2001", "marital_status": null, "address1": "7265 <PERSON>", "city": "Cincinnati", "state": "OH", "zipcode": "45243-2140", "vision_plan": null, "dental_plan": null, "life_plan": "Y", "add_plan": "Y", "relationship": "Employee", "dept_1_first_name": null, "dept_1_last_name": null, "dept_1_gender": null, "dept_1_dob": null, "dept_1_address1": null, "dept_1_city": null, "dept_1_state": null, "dept_1_zipcode": null, "relationship_type_1": null, "dept_1": null, "dept_1_dental_plan": null, "dept_1_vision_plan": null, "dept_1_life_plan": null, "dept_1_add_plan": null, "dept_count": "0", "dept_2_first_name": null, "dept_2_last_name": null, "dept_2_gender": null, "dept_2_dob": null, "dept_2_address1": null, "dept_2_city": null, "dept_2_state": null, "dept_2_zipcode": null, "relationship_type_2": null, "dept_2": null, "dept_2_dental_plan": null, "dept_2_vision_plan": null, "dept_2_life_plan": null, "dept_2_add_plan": null, "dept_3_first_name": null, "dept_3_last_name": null, "dept_3_gender": null, "dept_3_dob": null, "dept_3_address1": null, "dept_3_city": null, "dept_3_state": null, "dept_3_zipcode": null, "relationship_type_3": null, "dept_3": null, "dept_3_dental_plan": null, "dept_3_vision_plan": null, "dept_3_life_plan": null, "dept_3_add_plan": null, "dept_4_first_name": null, "dept_4_last_name": null, "dept_4_gender": null, "dept_4_dob": null, "dept_4_address1": null, "dept_4_city": null, "dept_4_state": null, "dept_4_zipcode": null, "relationship_type_4": null, "dept_4": null, "dept_4_dental_plan": null, "dept_4_vision_plan": null, "dept_4_life_plan": null, "dept_4_add_plan": null, "name": "<PERSON>", "age": 23.0, "dept_1_age": null, "dept_2_age": null, "dept_3_age": null, "dept_4_age": null, "employee_id": null, "first_name": null, "middle_name": null, "last_name": null, "address2": null, "record_type": null, "salary": null, "employment_type": null, "employee_class": null, "department": null, "hire_date": null, "tobacco_use": null, "pregnancy_status": null, "medical_plan": null, "coverage_tier": null, "ssn": null}, {"gender": "Male", "dob": "3/11/1974", "marital_status": null, "address1": "107 Culpepper Ln", "city": "<PERSON><PERSON><PERSON>", "state": "SC", "zipcode": "29642-8631", "vision_plan": null, "dental_plan": null, "life_plan": null, "add_plan": "Y", "relationship": "Employee", "dept_1_first_name": null, "dept_1_last_name": null, "dept_1_gender": null, "dept_1_dob": null, "dept_1_address1": null, "dept_1_city": null, "dept_1_state": null, "dept_1_zipcode": null, "relationship_type_1": null, "dept_1": null, "dept_1_dental_plan": null, "dept_1_vision_plan": null, "dept_1_life_plan": null, "dept_1_add_plan": null, "dept_count": "0", "dept_2_first_name": null, "dept_2_last_name": null, "dept_2_gender": null, "dept_2_dob": null, "dept_2_address1": null, "dept_2_city": null, "dept_2_state": null, "dept_2_zipcode": null, "relationship_type_2": null, "dept_2": null, "dept_2_dental_plan": null, "dept_2_vision_plan": null, "dept_2_life_plan": null, "dept_2_add_plan": null, "dept_3_first_name": null, "dept_3_last_name": null, "dept_3_gender": null, "dept_3_dob": null, "dept_3_address1": null, "dept_3_city": null, "dept_3_state": null, "dept_3_zipcode": null, "relationship_type_3": null, "dept_3": null, "dept_3_dental_plan": null, "dept_3_vision_plan": null, "dept_3_life_plan": null, "dept_3_add_plan": null, "dept_4_first_name": null, "dept_4_last_name": null, "dept_4_gender": null, "dept_4_dob": null, "dept_4_address1": null, "dept_4_city": null, "dept_4_state": null, "dept_4_zipcode": null, "relationship_type_4": null, "dept_4": null, "dept_4_dental_plan": null, "dept_4_vision_plan": null, "dept_4_life_plan": null, "dept_4_add_plan": null, "name": "<PERSON>", "age": 51.0, "dept_1_age": null, "dept_2_age": null, "dept_3_age": null, "dept_4_age": null, "employee_id": null, "first_name": null, "middle_name": null, "last_name": null, "address2": null, "record_type": null, "salary": null, "employment_type": null, "employee_class": null, "department": null, "hire_date": null, "tobacco_use": null, "pregnancy_status": null, "medical_plan": null, "coverage_tier": null, "ssn": null}, {"gender": "Male", "dob": "3/25/1996", "marital_status": "Married", "address1": "229 Crooked Creek Trl", "city": "Canton", "state": "GA", "zipcode": "30115-4910", "vision_plan": "Y", "dental_plan": "Y", "life_plan": "Y", "add_plan": "Y", "relationship": "Employee", "dept_1_first_name": "<PERSON>", "dept_1_last_name": "<PERSON><PERSON><PERSON>", "dept_1_gender": "Female", "dept_1_dob": "8/29/2024", "dept_1_address1": "229 Crooked Creek Trl", "dept_1_city": "Canton", "dept_1_state": "GA", "dept_1_zipcode": "30115-4910", "relationship_type_1": "Child", "dept_1": "<PERSON>", "dept_1_dental_plan": null, "dept_1_vision_plan": null, "dept_1_life_plan": null, "dept_1_add_plan": null, "dept_count": "1", "dept_2_first_name": null, "dept_2_last_name": null, "dept_2_gender": null, "dept_2_dob": null, "dept_2_address1": null, "dept_2_city": null, "dept_2_state": null, "dept_2_zipcode": null, "relationship_type_2": null, "dept_2": null, "dept_2_dental_plan": null, "dept_2_vision_plan": null, "dept_2_life_plan": null, "dept_2_add_plan": null, "dept_3_first_name": null, "dept_3_last_name": null, "dept_3_gender": null, "dept_3_dob": null, "dept_3_address1": null, "dept_3_city": null, "dept_3_state": null, "dept_3_zipcode": null, "relationship_type_3": null, "dept_3": null, "dept_3_dental_plan": null, "dept_3_vision_plan": null, "dept_3_life_plan": null, "dept_3_add_plan": null, "dept_4_first_name": null, "dept_4_last_name": null, "dept_4_gender": null, "dept_4_dob": null, "dept_4_address1": null, "dept_4_city": null, "dept_4_state": null, "dept_4_zipcode": null, "relationship_type_4": null, "dept_4": null, "dept_4_dental_plan": null, "dept_4_vision_plan": null, "dept_4_life_plan": null, "dept_4_add_plan": null, "name": "<PERSON>", "age": 29.0, "dept_1_age": 0.0, "dept_2_age": null, "dept_3_age": null, "dept_4_age": null, "employee_id": null, "first_name": null, "middle_name": null, "last_name": null, "address2": null, "record_type": null, "salary": null, "employment_type": null, "employee_class": null, "department": null, "hire_date": null, "tobacco_use": null, "pregnancy_status": null, "medical_plan": null, "coverage_tier": null, "ssn": null}, {"gender": "Female", "dob": "9/5/1998", "marital_status": null, "address1": "229 Crooked Creek Trl", "city": "Canton", "state": "GA", "zipcode": "30115-4910", "vision_plan": null, "dental_plan": null, "life_plan": null, "add_plan": null, "relationship": "Spouse", "dept_1_first_name": null, "dept_1_last_name": null, "dept_1_gender": null, "dept_1_dob": null, "dept_1_address1": null, "dept_1_city": null, "dept_1_state": null, "dept_1_zipcode": null, "relationship_type_1": null, "dept_1": null, "dept_1_dental_plan": null, "dept_1_vision_plan": null, "dept_1_life_plan": null, "dept_1_add_plan": null, "dept_count": "0", "dept_2_first_name": null, "dept_2_last_name": null, "dept_2_gender": null, "dept_2_dob": null, "dept_2_address1": null, "dept_2_city": null, "dept_2_state": null, "dept_2_zipcode": null, "relationship_type_2": null, "dept_2": null, "dept_2_dental_plan": null, "dept_2_vision_plan": null, "dept_2_life_plan": null, "dept_2_add_plan": null, "dept_3_first_name": null, "dept_3_last_name": null, "dept_3_gender": null, "dept_3_dob": null, "dept_3_address1": null, "dept_3_city": null, "dept_3_state": null, "dept_3_zipcode": null, "relationship_type_3": null, "dept_3": null, "dept_3_dental_plan": null, "dept_3_vision_plan": null, "dept_3_life_plan": null, "dept_3_add_plan": null, "dept_4_first_name": null, "dept_4_last_name": null, "dept_4_gender": null, "dept_4_dob": null, "dept_4_address1": null, "dept_4_city": null, "dept_4_state": null, "dept_4_zipcode": null, "relationship_type_4": null, "dept_4": null, "dept_4_dental_plan": null, "dept_4_vision_plan": null, "dept_4_life_plan": null, "dept_4_add_plan": null, "name": "<PERSON>", "age": 26.0, "dept_1_age": null, "dept_2_age": null, "dept_3_age": null, "dept_4_age": null, "employee_id": null, "first_name": null, "middle_name": null, "last_name": null, "address2": null, "record_type": null, "salary": null, "employment_type": null, "employee_class": null, "department": null, "hire_date": null, "tobacco_use": null, "pregnancy_status": null, "medical_plan": null, "coverage_tier": null, "ssn": null}, {"gender": "Female", "dob": "7/25/1967", "marital_status": null, "address1": "165 <PERSON>", "city": "Fountain Inn", "state": "SC", "zipcode": "29644", "vision_plan": null, "dental_plan": null, "life_plan": "Y", "add_plan": "Y", "relationship": "Employee", "dept_1_first_name": null, "dept_1_last_name": null, "dept_1_gender": null, "dept_1_dob": null, "dept_1_address1": null, "dept_1_city": null, "dept_1_state": null, "dept_1_zipcode": null, "relationship_type_1": null, "dept_1": null, "dept_1_dental_plan": null, "dept_1_vision_plan": null, "dept_1_life_plan": null, "dept_1_add_plan": null, "dept_count": "0", "dept_2_first_name": null, "dept_2_last_name": null, "dept_2_gender": null, "dept_2_dob": null, "dept_2_address1": null, "dept_2_city": null, "dept_2_state": null, "dept_2_zipcode": null, "relationship_type_2": null, "dept_2": null, "dept_2_dental_plan": null, "dept_2_vision_plan": null, "dept_2_life_plan": null, "dept_2_add_plan": null, "dept_3_first_name": null, "dept_3_last_name": null, "dept_3_gender": null, "dept_3_dob": null, "dept_3_address1": null, "dept_3_city": null, "dept_3_state": null, "dept_3_zipcode": null, "relationship_type_3": null, "dept_3": null, "dept_3_dental_plan": null, "dept_3_vision_plan": null, "dept_3_life_plan": null, "dept_3_add_plan": null, "dept_4_first_name": null, "dept_4_last_name": null, "dept_4_gender": null, "dept_4_dob": null, "dept_4_address1": null, "dept_4_city": null, "dept_4_state": null, "dept_4_zipcode": null, "relationship_type_4": null, "dept_4": null, "dept_4_dental_plan": null, "dept_4_vision_plan": null, "dept_4_life_plan": null, "dept_4_add_plan": null, "name": "<PERSON>", "age": 57.0, "dept_1_age": null, "dept_2_age": null, "dept_3_age": null, "dept_4_age": null, "employee_id": null, "first_name": null, "middle_name": null, "last_name": null, "address2": null, "record_type": null, "salary": null, "employment_type": null, "employee_class": null, "department": null, "hire_date": null, "tobacco_use": null, "pregnancy_status": null, "medical_plan": null, "coverage_tier": null, "ssn": null}, {"gender": "Female", "dob": "7/22/1973", "marital_status": "Married", "address1": "659 Diamond Ridge Way", "city": "<PERSON>", "state": "SC", "zipcode": "29334-8892", "vision_plan": "Y", "dental_plan": "Y", "life_plan": "Y", "add_plan": "Y", "relationship": "Employee", "dept_1_first_name": "<PERSON>", "dept_1_last_name": "<PERSON>", "dept_1_gender": "Male", "dept_1_dob": "6/28/1969", "dept_1_address1": "659 Diamond Ridge Way", "dept_1_city": "<PERSON>", "dept_1_state": "SC", "dept_1_zipcode": "29334-8892", "relationship_type_1": "Spouse", "dept_1": "<PERSON>", "dept_1_dental_plan": null, "dept_1_vision_plan": null, "dept_1_life_plan": null, "dept_1_add_plan": null, "dept_count": "1", "dept_2_first_name": null, "dept_2_last_name": null, "dept_2_gender": null, "dept_2_dob": null, "dept_2_address1": null, "dept_2_city": null, "dept_2_state": null, "dept_2_zipcode": null, "relationship_type_2": null, "dept_2": null, "dept_2_dental_plan": null, "dept_2_vision_plan": null, "dept_2_life_plan": null, "dept_2_add_plan": null, "dept_3_first_name": null, "dept_3_last_name": null, "dept_3_gender": null, "dept_3_dob": null, "dept_3_address1": null, "dept_3_city": null, "dept_3_state": null, "dept_3_zipcode": null, "relationship_type_3": null, "dept_3": null, "dept_3_dental_plan": null, "dept_3_vision_plan": null, "dept_3_life_plan": null, "dept_3_add_plan": null, "dept_4_first_name": null, "dept_4_last_name": null, "dept_4_gender": null, "dept_4_dob": null, "dept_4_address1": null, "dept_4_city": null, "dept_4_state": null, "dept_4_zipcode": null, "relationship_type_4": null, "dept_4": null, "dept_4_dental_plan": null, "dept_4_vision_plan": null, "dept_4_life_plan": null, "dept_4_add_plan": null, "name": "<PERSON>", "age": 51.0, "dept_1_age": 56.0, "dept_2_age": null, "dept_3_age": null, "dept_4_age": null, "employee_id": null, "first_name": null, "middle_name": null, "last_name": null, "address2": null, "record_type": null, "salary": null, "employment_type": null, "employee_class": null, "department": null, "hire_date": null, "tobacco_use": null, "pregnancy_status": null, "medical_plan": null, "coverage_tier": null, "ssn": null}, {"gender": "Female", "dob": "6/21/1978", "marital_status": null, "address1": "2705 Lawson Dr", "city": "<PERSON><PERSON><PERSON>", "state": "NC", "zipcode": "28173-7182", "vision_plan": null, "dental_plan": null, "life_plan": "Y", "add_plan": "Y", "relationship": "Employee", "dept_1_first_name": null, "dept_1_last_name": null, "dept_1_gender": null, "dept_1_dob": null, "dept_1_address1": null, "dept_1_city": null, "dept_1_state": null, "dept_1_zipcode": null, "relationship_type_1": null, "dept_1": null, "dept_1_dental_plan": null, "dept_1_vision_plan": null, "dept_1_life_plan": null, "dept_1_add_plan": null, "dept_count": "0", "dept_2_first_name": null, "dept_2_last_name": null, "dept_2_gender": null, "dept_2_dob": null, "dept_2_address1": null, "dept_2_city": null, "dept_2_state": null, "dept_2_zipcode": null, "relationship_type_2": null, "dept_2": null, "dept_2_dental_plan": null, "dept_2_vision_plan": null, "dept_2_life_plan": null, "dept_2_add_plan": null, "dept_3_first_name": null, "dept_3_last_name": null, "dept_3_gender": null, "dept_3_dob": null, "dept_3_address1": null, "dept_3_city": null, "dept_3_state": null, "dept_3_zipcode": null, "relationship_type_3": null, "dept_3": null, "dept_3_dental_plan": null, "dept_3_vision_plan": null, "dept_3_life_plan": null, "dept_3_add_plan": null, "dept_4_first_name": null, "dept_4_last_name": null, "dept_4_gender": null, "dept_4_dob": null, "dept_4_address1": null, "dept_4_city": null, "dept_4_state": null, "dept_4_zipcode": null, "relationship_type_4": null, "dept_4": null, "dept_4_dental_plan": null, "dept_4_vision_plan": null, "dept_4_life_plan": null, "dept_4_add_plan": null, "name": "<PERSON>", "age": 47.0, "dept_1_age": null, "dept_2_age": null, "dept_3_age": null, "dept_4_age": null, "employee_id": null, "first_name": null, "middle_name": null, "last_name": null, "address2": null, "record_type": null, "salary": null, "employment_type": null, "employee_class": null, "department": null, "hire_date": null, "tobacco_use": null, "pregnancy_status": null, "medical_plan": null, "coverage_tier": null, "ssn": null}, {"gender": "Male", "dob": "3/7/1991", "marital_status": null, "address1": "1445 Brawley Circle NE", "city": "Atlanta", "state": "GA", "zipcode": "30319", "vision_plan": "Y", "dental_plan": null, "life_plan": "Y", "add_plan": "Y", "relationship": "Employee", "dept_1_first_name": null, "dept_1_last_name": null, "dept_1_gender": null, "dept_1_dob": null, "dept_1_address1": null, "dept_1_city": null, "dept_1_state": null, "dept_1_zipcode": null, "relationship_type_1": null, "dept_1": null, "dept_1_dental_plan": null, "dept_1_vision_plan": null, "dept_1_life_plan": null, "dept_1_add_plan": null, "dept_count": "0", "dept_2_first_name": null, "dept_2_last_name": null, "dept_2_gender": null, "dept_2_dob": null, "dept_2_address1": null, "dept_2_city": null, "dept_2_state": null, "dept_2_zipcode": null, "relationship_type_2": null, "dept_2": null, "dept_2_dental_plan": null, "dept_2_vision_plan": null, "dept_2_life_plan": null, "dept_2_add_plan": null, "dept_3_first_name": null, "dept_3_last_name": null, "dept_3_gender": null, "dept_3_dob": null, "dept_3_address1": null, "dept_3_city": null, "dept_3_state": null, "dept_3_zipcode": null, "relationship_type_3": null, "dept_3": null, "dept_3_dental_plan": null, "dept_3_vision_plan": null, "dept_3_life_plan": null, "dept_3_add_plan": null, "dept_4_first_name": null, "dept_4_last_name": null, "dept_4_gender": null, "dept_4_dob": null, "dept_4_address1": null, "dept_4_city": null, "dept_4_state": null, "dept_4_zipcode": null, "relationship_type_4": null, "dept_4": null, "dept_4_dental_plan": null, "dept_4_vision_plan": null, "dept_4_life_plan": null, "dept_4_add_plan": null, "name": "<PERSON>", "age": 34.0, "dept_1_age": null, "dept_2_age": null, "dept_3_age": null, "dept_4_age": null, "employee_id": null, "first_name": null, "middle_name": null, "last_name": null, "address2": null, "record_type": null, "salary": null, "employment_type": null, "employee_class": null, "department": null, "hire_date": null, "tobacco_use": null, "pregnancy_status": null, "medical_plan": null, "coverage_tier": null, "ssn": null}, {"gender": "Female", "dob": "8/16/1978", "marital_status": null, "address1": "10 <PERSON>", "city": "Gaithersburg", "state": "MD", "zipcode": "20877-1801", "vision_plan": null, "dental_plan": "Y", "life_plan": "Y", "add_plan": "Y", "relationship": "Employee", "dept_1_first_name": null, "dept_1_last_name": null, "dept_1_gender": null, "dept_1_dob": null, "dept_1_address1": null, "dept_1_city": null, "dept_1_state": null, "dept_1_zipcode": null, "relationship_type_1": null, "dept_1": null, "dept_1_dental_plan": null, "dept_1_vision_plan": null, "dept_1_life_plan": null, "dept_1_add_plan": null, "dept_count": "0", "dept_2_first_name": null, "dept_2_last_name": null, "dept_2_gender": null, "dept_2_dob": null, "dept_2_address1": null, "dept_2_city": null, "dept_2_state": null, "dept_2_zipcode": null, "relationship_type_2": null, "dept_2": null, "dept_2_dental_plan": null, "dept_2_vision_plan": null, "dept_2_life_plan": null, "dept_2_add_plan": null, "dept_3_first_name": null, "dept_3_last_name": null, "dept_3_gender": null, "dept_3_dob": null, "dept_3_address1": null, "dept_3_city": null, "dept_3_state": null, "dept_3_zipcode": null, "relationship_type_3": null, "dept_3": null, "dept_3_dental_plan": null, "dept_3_vision_plan": null, "dept_3_life_plan": null, "dept_3_add_plan": null, "dept_4_first_name": null, "dept_4_last_name": null, "dept_4_gender": null, "dept_4_dob": null, "dept_4_address1": null, "dept_4_city": null, "dept_4_state": null, "dept_4_zipcode": null, "relationship_type_4": null, "dept_4": null, "dept_4_dental_plan": null, "dept_4_vision_plan": null, "dept_4_life_plan": null, "dept_4_add_plan": null, "name": "<PERSON>", "age": 46.0, "dept_1_age": null, "dept_2_age": null, "dept_3_age": null, "dept_4_age": null, "employee_id": null, "first_name": null, "middle_name": null, "last_name": null, "address2": null, "record_type": null, "salary": null, "employment_type": null, "employee_class": null, "department": null, "hire_date": null, "tobacco_use": null, "pregnancy_status": null, "medical_plan": null, "coverage_tier": null, "ssn": null}, {"gender": "Male", "dob": "12/3/2003", "marital_status": null, "address1": "10 <PERSON>", "city": "Gaithersburg", "state": "MD", "zipcode": "20877-1801", "vision_plan": null, "dental_plan": null, "life_plan": null, "add_plan": null, "relationship": "Child", "dept_1_first_name": null, "dept_1_last_name": null, "dept_1_gender": null, "dept_1_dob": null, "dept_1_address1": null, "dept_1_city": null, "dept_1_state": null, "dept_1_zipcode": null, "relationship_type_1": null, "dept_1": null, "dept_1_dental_plan": null, "dept_1_vision_plan": null, "dept_1_life_plan": null, "dept_1_add_plan": null, "dept_count": "0", "dept_2_first_name": null, "dept_2_last_name": null, "dept_2_gender": null, "dept_2_dob": null, "dept_2_address1": null, "dept_2_city": null, "dept_2_state": null, "dept_2_zipcode": null, "relationship_type_2": null, "dept_2": null, "dept_2_dental_plan": null, "dept_2_vision_plan": null, "dept_2_life_plan": null, "dept_2_add_plan": null, "dept_3_first_name": null, "dept_3_last_name": null, "dept_3_gender": null, "dept_3_dob": null, "dept_3_address1": null, "dept_3_city": null, "dept_3_state": null, "dept_3_zipcode": null, "relationship_type_3": null, "dept_3": null, "dept_3_dental_plan": null, "dept_3_vision_plan": null, "dept_3_life_plan": null, "dept_3_add_plan": null, "dept_4_first_name": null, "dept_4_last_name": null, "dept_4_gender": null, "dept_4_dob": null, "dept_4_address1": null, "dept_4_city": null, "dept_4_state": null, "dept_4_zipcode": null, "relationship_type_4": null, "dept_4": null, "dept_4_dental_plan": null, "dept_4_vision_plan": null, "dept_4_life_plan": null, "dept_4_add_plan": null, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "age": 21.0, "dept_1_age": null, "dept_2_age": null, "dept_3_age": null, "dept_4_age": null, "employee_id": null, "first_name": null, "middle_name": null, "last_name": null, "address2": null, "record_type": null, "salary": null, "employment_type": null, "employee_class": null, "department": null, "hire_date": null, "tobacco_use": null, "pregnancy_status": null, "medical_plan": null, "coverage_tier": null, "ssn": null}, {"gender": "Female", "dob": "1/20/1998", "marital_status": null, "address1": "328 Quail Run Cir", "city": "Fountain Inn", "state": "SC", "zipcode": "29644-1408", "vision_plan": null, "dental_plan": "Y", "life_plan": "Y", "add_plan": "Y", "relationship": "Employee", "dept_1_first_name": "<PERSON>", "dept_1_last_name": "<PERSON>", "dept_1_gender": "Male", "dept_1_dob": "5/14/2021", "dept_1_address1": "328 Quail Run Cir", "dept_1_city": "Fountain Inn", "dept_1_state": "SC", "dept_1_zipcode": "29644-1408", "relationship_type_1": "Child", "dept_1": "<PERSON>", "dept_1_dental_plan": null, "dept_1_vision_plan": null, "dept_1_life_plan": null, "dept_1_add_plan": null, "dept_count": "1", "dept_2_first_name": null, "dept_2_last_name": null, "dept_2_gender": null, "dept_2_dob": null, "dept_2_address1": null, "dept_2_city": null, "dept_2_state": null, "dept_2_zipcode": null, "relationship_type_2": null, "dept_2": null, "dept_2_dental_plan": null, "dept_2_vision_plan": null, "dept_2_life_plan": null, "dept_2_add_plan": null, "dept_3_first_name": null, "dept_3_last_name": null, "dept_3_gender": null, "dept_3_dob": null, "dept_3_address1": null, "dept_3_city": null, "dept_3_state": null, "dept_3_zipcode": null, "relationship_type_3": null, "dept_3": null, "dept_3_dental_plan": null, "dept_3_vision_plan": null, "dept_3_life_plan": null, "dept_3_add_plan": null, "dept_4_first_name": null, "dept_4_last_name": null, "dept_4_gender": null, "dept_4_dob": null, "dept_4_address1": null, "dept_4_city": null, "dept_4_state": null, "dept_4_zipcode": null, "relationship_type_4": null, "dept_4": null, "dept_4_dental_plan": null, "dept_4_vision_plan": null, "dept_4_life_plan": null, "dept_4_add_plan": null, "name": "<PERSON><PERSON>", "age": 27.0, "dept_1_age": 4.0, "dept_2_age": null, "dept_3_age": null, "dept_4_age": null, "employee_id": null, "first_name": null, "middle_name": null, "last_name": null, "address2": null, "record_type": null, "salary": null, "employment_type": null, "employee_class": null, "department": null, "hire_date": null, "tobacco_use": null, "pregnancy_status": null, "medical_plan": null, "coverage_tier": null, "ssn": null}, {"gender": "Female", "dob": "9/11/1967", "marital_status": "Married", "address1": "7 Mallard Ct", "city": "Greenville", "state": "SC", "zipcode": "29617-6139", "vision_plan": null, "dental_plan": "Y", "life_plan": "Y", "add_plan": "Y", "relationship": "Employee", "dept_1_first_name": null, "dept_1_last_name": null, "dept_1_gender": null, "dept_1_dob": null, "dept_1_address1": null, "dept_1_city": null, "dept_1_state": null, "dept_1_zipcode": null, "relationship_type_1": null, "dept_1": null, "dept_1_dental_plan": null, "dept_1_vision_plan": null, "dept_1_life_plan": null, "dept_1_add_plan": null, "dept_count": "0", "dept_2_first_name": null, "dept_2_last_name": null, "dept_2_gender": null, "dept_2_dob": null, "dept_2_address1": null, "dept_2_city": null, "dept_2_state": null, "dept_2_zipcode": null, "relationship_type_2": null, "dept_2": null, "dept_2_dental_plan": null, "dept_2_vision_plan": null, "dept_2_life_plan": null, "dept_2_add_plan": null, "dept_3_first_name": null, "dept_3_last_name": null, "dept_3_gender": null, "dept_3_dob": null, "dept_3_address1": null, "dept_3_city": null, "dept_3_state": null, "dept_3_zipcode": null, "relationship_type_3": null, "dept_3": null, "dept_3_dental_plan": null, "dept_3_vision_plan": null, "dept_3_life_plan": null, "dept_3_add_plan": null, "dept_4_first_name": null, "dept_4_last_name": null, "dept_4_gender": null, "dept_4_dob": null, "dept_4_address1": null, "dept_4_city": null, "dept_4_state": null, "dept_4_zipcode": null, "relationship_type_4": null, "dept_4": null, "dept_4_dental_plan": null, "dept_4_vision_plan": null, "dept_4_life_plan": null, "dept_4_add_plan": null, "name": "<PERSON>", "age": 57.0, "dept_1_age": null, "dept_2_age": null, "dept_3_age": null, "dept_4_age": null, "employee_id": null, "first_name": null, "middle_name": null, "last_name": null, "address2": null, "record_type": null, "salary": null, "employment_type": null, "employee_class": null, "department": null, "hire_date": null, "tobacco_use": null, "pregnancy_status": null, "medical_plan": null, "coverage_tier": null, "ssn": null}, {"gender": "Male", "dob": "10/26/1968", "marital_status": null, "address1": "7 Mallard Ct", "city": "Greenville", "state": "SC", "zipcode": "29617-6139", "vision_plan": null, "dental_plan": null, "life_plan": null, "add_plan": null, "relationship": "Spouse", "dept_1_first_name": "<PERSON>", "dept_1_last_name": "<PERSON><PERSON><PERSON>", "dept_1_gender": "Male", "dept_1_dob": "7/21/2004", "dept_1_address1": "7 Mallard Ct", "dept_1_city": "Greenville", "dept_1_state": "SC", "dept_1_zipcode": "29617-6139", "relationship_type_1": "Child", "dept_1": "<PERSON>", "dept_1_dental_plan": null, "dept_1_vision_plan": null, "dept_1_life_plan": null, "dept_1_add_plan": null, "dept_count": "1", "dept_2_first_name": null, "dept_2_last_name": null, "dept_2_gender": null, "dept_2_dob": null, "dept_2_address1": null, "dept_2_city": null, "dept_2_state": null, "dept_2_zipcode": null, "relationship_type_2": null, "dept_2": null, "dept_2_dental_plan": null, "dept_2_vision_plan": null, "dept_2_life_plan": null, "dept_2_add_plan": null, "dept_3_first_name": null, "dept_3_last_name": null, "dept_3_gender": null, "dept_3_dob": null, "dept_3_address1": null, "dept_3_city": null, "dept_3_state": null, "dept_3_zipcode": null, "relationship_type_3": null, "dept_3": null, "dept_3_dental_plan": null, "dept_3_vision_plan": null, "dept_3_life_plan": null, "dept_3_add_plan": null, "dept_4_first_name": null, "dept_4_last_name": null, "dept_4_gender": null, "dept_4_dob": null, "dept_4_address1": null, "dept_4_city": null, "dept_4_state": null, "dept_4_zipcode": null, "relationship_type_4": null, "dept_4": null, "dept_4_dental_plan": null, "dept_4_vision_plan": null, "dept_4_life_plan": null, "dept_4_add_plan": null, "name": "<PERSON>", "age": 56.0, "dept_1_age": 20.0, "dept_2_age": null, "dept_3_age": null, "dept_4_age": null, "employee_id": null, "first_name": null, "middle_name": null, "last_name": null, "address2": null, "record_type": null, "salary": null, "employment_type": null, "employee_class": null, "department": null, "hire_date": null, "tobacco_use": null, "pregnancy_status": null, "medical_plan": null, "coverage_tier": null, "ssn": null}], "validation": {"is_valid": true, "errors": [], "error_rows": [], "total_errors": 0}, "summary": {"original_rows": 34, "original_columns": 14, "processed_rows": 22, "processed_columns": 91, "processing_time": 17.41736912727356, "validation_passed": true, "mandatory_fields_present": true, "dept_count_distribution": {"0": 15, "1": 5, "3+": 2}}}, "return_dataframe": true}}