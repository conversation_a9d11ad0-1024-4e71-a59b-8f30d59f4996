import time
import logging
from typing import Dict, List, Optional
from pymongo import MongoClient
from fastapi import FastAPI, Request, UploadFile, File, Form
from fastapi.responses import StreamingResponse
from app.DataModels.dataModels import UserInput, TeamsUserInput, PineconeInput
from app.utils.vstore_utils.vectorstore_utils import update_pinecone_index_specific, delete_from_pinecone_index_specific, get_retriever, fetch_document_ids, fetch_user_data
from app.utils.vstore_utils.filters import metadata_similarity_filter
from app.controllers.Wellness.Wellness import Wellness
from fastapi.middleware.cors import CORSMiddleware
from azure.storage.blob import BlobServiceClient

from app.DataModels.ResponseModel import ResponseModel
from app.Tools.vectorStore import VectorStore
from app.utils.SessionManager.session_manager import SessionManager
from app.controllers.chatSession.chatSession import ChatSession
from app.controllers.Enrollment.Plans import Plans
from app.controllers.Census.censusController import Census<PERSON><PERSON>roller

from config.config import config

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class BenoSphereAI:
    """Main class for the Benosphere AI application."""
    def __init__(self):
        self.config = config
        self.app = FastAPI(
            title="BENOSPHERE",
            version="1.0",
            description="Local Version of Benosphere APP",
        )
        self.blob_service_client = BlobServiceClient.from_connection_string(self.config.blob_connection_string)
        self.mongo_client = MongoClient(self.config.mongodb_uri)
        self.vector_store = VectorStore(
                vector_db_type="pinecone",
                embedding_available_option="openai_default"
            )
        self.wellness = Wellness(mongo_client=self.mongo_client, mongo_environment="prod", vector_store_manager=self.vector_store)
        self.plans = Plans()
        self.census = CensusController()
        self.setup_routes()
        self.setup_cors()

        return
    def setup_cors(self):
        """Set up CORS middleware."""
        self.app.add_middleware(
            CORSMiddleware,
            allow_origins=["*"],  # Adjust this to your needs
            allow_credentials=True,
            allow_methods=["*"],
            allow_headers=["*"],
            expose_headers=["Content-Type", "X-Accel-Buffering"],
        )
    
    def setup_routes(self):
        """Set up API routes."""
        self.app.get("/check", response_model=ResponseModel)(self.sanity_check)
        self.app.post("/chat")(self.chat_endpoint)
        self.app.post("/teams-chat")(self.teams_chat_endpoint)
        self.app.post('/update-pinecone-index', response_model=ResponseModel)(self.update_pinecone_index)
        self.app.post('/delete-from-pinecone-index',  response_model=ResponseModel)(self.delete_from_pinecone_index)
        self.app.post('/search-using-retriever', response_model=ResponseModel)(self.search_using_retriever)
        self.app.post('/process-plan-documents-upload', response_model=ResponseModel)(self.process_plan_documents_upload_endpoint)

        self.app.post('/wellness/predictions', response_model=ResponseModel)(self.wellness.initiate_wellness_app)

        # Census Processing API (no response_model to avoid double wrapping)
        self.app.post('/api/census/processor/v1')(self.process_census_endpoint)
        self.app.get('/wellness/questions', response_model=ResponseModel)(self.wellness.get_wellness_questions)

    async def sanity_check(self):
        """Endpoint to check if the AI backend is operational."""
        logger.info("Sanity check endpoint called")
        return ResponseModel(message="AI Backend is up and running")
    
    async def _initialize_session_manager(self):
        self.session_manager = await SessionManager(
            redis_url=self.config.redis_url,
            timeout_minutes=self.config.session_timeout,
            max_memory_sessions=self.config.max_memory_sessions,
            task_cleanup_time=self.config.task_cleanup_time,
            model_config={
                "model_name": self.config.chat_model_name,
                "model_category": self.config.chat_model_category
            }
        ).initialize()


    async def chat_endpoint(self, request: Request, user_input: UserInput):
        """
        Main endpoint for processing chat requests with session management.
        """
        logger.info(f"Received input: {user_input}")
        start_time = time.time()

        try:
            # Initialize session manager if not already done
            if not hasattr(self, 'session_manager'):
                await self._initialize_session_manager()
            
            # Get or create session for this user
            chat_session: ChatSession = await self.session_manager.get_session(
                user_id=user_input.user_id,
                team_id=user_input.team_id,
                vector_store=self.vector_store,
                mongo_client=self.mongo_client,
            )
            
            # Process the message and stream the response
            response_stream = chat_session.process_message(user_input.user_message)
            
            response_time = time.time() - start_time
            logger.info(f"Response time: {response_time:.2f} seconds")

            return StreamingResponse(
                response_stream,
                media_type='text/event-stream',
                headers={
                    'Cache-Control': 'no-cache',
                    'Connection': 'keep-alive',
                    'X-Accel-Buffering': 'no'
                }
            )

        except Exception as e:
            logger.error(f"Error processing request: {str(e)}")
            return self._generate_response(f"Error processing request: {str(e)}", start_time)        

    async def teams_chat_endpoint(self, request: Request, user_input: TeamsUserInput): # -> Dict[str, str]:
        """
        Main endpoint for processing chat requests.

        Args:
            request (Request): The FastAPI request object.
            user_input (UserInput): Validated user input containing message and identifiers.

        Returns:
            dict: A dictionary containing the AI-generated response.
        """
        logger.info(f"Received input: {user_input}")
        start_time = time.time()

        try:
            # Fetch user data using user_email
            user_data = await fetch_user_data(user_input.user_email, self.mongo_client)
            if not hasattr(self, 'session_manager'):
                await self._initialize_session_manager()
            
            # Get or create session for this user
            chat_session: ChatSession = await self.session_manager.get_session(
                user_id=str(user_data['_id']),
                team_id=user_data['companyId'],
                vector_store=self.vector_store,
                mongo_client=self.mongo_client,
            )
            
            # Process the message and stream the response
            response_stream = chat_session.process_message(user_input.user_message)
            
            response_time = time.time() - start_time
            logger.info(f"Response time: {response_time:.2f} seconds")

            return StreamingResponse(
                response_stream,
                media_type='text/event-stream',
                headers={
                    'Cache-Control': 'no-cache',
                    'Connection': 'keep-alive',
                    'X-Accel-Buffering': 'no'
            }
            )
        except Exception as e:
            logger.error(f"Error processing request: {str(e)}")
            return self._generate_response(f"Error processing request: {str(e)}", start_time)
    
    async def update_pinecone_index(self, pinecone_input: PineconeInput) -> Dict[str, str]:
        """
        Endpoint to update the Pinecone index with new documents from Azure Blob Storage.

        Args:
            pinecone_input (PineconeInput): Validated input containing team_id and object_keys.

        Returns:
            dict: A dictionary containing the status of the update.
        """
        logger.info(f"Updating Pinecone index for team_id: {pinecone_input.team_id}")
        try:
            await update_pinecone_index_specific(pinecone_input=pinecone_input,
                                           vector_store=self.vector_store,
                                           blob_service_client=self.blob_service_client)

            logger.info("Index updated successfully")
            
            await self._clear_session_cache(pinecone_input.team_id)

            return ResponseModel(status_code=201, message="Index updated successfully")

        except Exception as e:
            logger.error(f"Error updating index: {str(e)}")
            return ResponseModel(success=False,status_code=500, message=f"Error Updating Index {str(e)}")
        
    async def search_using_retriever(self, UserInput:UserInput):
        document_ids = await fetch_document_ids(user_id=UserInput.user_id,
                                            team_id=UserInput.team_id,
                                            vector_store=self.vector_store,
                                            mongo_client=self.mongo_client,
                                            mongo_db_name=self.config.mongo_db)
        retriever = await get_retriever(vector_store=self.vector_store,
                                        search_kwargs=metadata_similarity_filter(metadata_key="file_key",ids=document_ids)
                                        )
        # return {"documents":retriever.invoke(input=UserInput.user_message)}
        return ResponseModel(data={"documents":retriever.invoke(input=UserInput.user_message)}, message="success")



    async def process_plan_documents_upload_endpoint(
        self,
        files: List[UploadFile] = File(...),
        plan_id: Optional[str] = Form(None)
    ):
        """
        Endpoint to process uploaded files with binary buffers and extract highlights using OpenAI.

        Args:
            files (List[UploadFile]): List of uploaded files with binary content
            plan_id (Optional[str]): Optional plan identifier

        Returns:
            ResponseModel: Processing results with extracted plan highlights
        """
        logger.info(f"Processing {len(files)} uploaded files for plan ID: {plan_id}")

        # Use the Plans controller's upload processing function
        return await self.plans.process_uploaded_files(files, plan_id)

    async def delete_from_pinecone_index(self, pinecone_input: PineconeInput) -> Dict[str, str]:
        """
        Endpoint to delete documents from the Pinecone index based on the object key metadata.

        Args:
            team_id (str): The team identifier.
            object_key (str): The object key metadata to filter documents for deletion.

        Returns:
            dict: A dictionary containing the status of the deletion.
        """
        logger.info(f"Deleting documents from Pinecone index for team_id: {pinecone_input.team_id}")
        try:
            await delete_from_pinecone_index_specific(pinecone_input=pinecone_input, vector_store=self.vector_store)

            logger.info("Documents deleted successfully")

            await self._clear_session_cache(pinecone_input.team_id)

            return ResponseModel(status_code=201, message=f"Deleted Index")
        except Exception as e:
            logger.error(f"Error deleting documents: {str(e)}")
            return ResponseModel(success=False,status_code=500, message=f"Error Deleting Index {str(e)}")

    @staticmethod
    def _generate_response(response: str, start_time: float) -> Dict[str, str]:
        """Generate the final response with timing information."""
        response_time = time.time() - start_time
        logger.info(f"Response time: {response_time:.2f} seconds")
        return {"response": response}
    
    async def _clear_session_cache(self, team_id: str):
        """Clear cache for a specific team."""
        if not hasattr(self, 'session_manager'):
                await self._initialize_session_manager()
        await self.session_manager.cleanup_sessions_by_team(team_id)

    # Census processing endpoints
    async def process_census_endpoint(self,
                                     file: UploadFile = File(...),
                                     return_dataframe: bool = True):
        """
        Census Processing API Endpoint - /api/census/processor/v1

        Process uploaded census file with optional dataframe return.

        Args:
            file: Uploaded census file (CSV, XLSX, XLS)
            return_dataframe: Whether to include enriched dataframe in response (default: True)

        Returns:
            ResponseModel with census processing results
        """
        try:
            logger.info(f"Processing census file: {file.filename}, return_dataframe: {return_dataframe}")

            # Process census file using the controller
            result = await self.census.process_census_file(file, return_dataframe=return_dataframe)

            # Return result directly (it's already properly formatted by ResponseService)
            return result

        except Exception as e:
            logger.error(f"Census processing failed: {str(e)}")
            return {
                "success": False,
                "status_code": 500,
                "error": "census_processing_failed",
                "message": f"Census processing failed: {str(e)}"
            }




benosphere_app = BenoSphereAI()
app = benosphere_app.app


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="127.0.0.1", port=8000, timeout_keep_alive=300)