#!/usr/bin/env python3
"""
Test script to process SBS-Census-Final.csv and identify issues
"""

import asyncio
import sys
import os
import pandas as pd
import json
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent.absolute()
sys.path.insert(0, str(project_root))
print(f"Project root: {project_root}")
print(f"Current working directory: {os.getcwd()}")

# Change to project root
os.chdir(project_root)

from app.controllers.Census.services.orchestrationService import OrchestrationService
from app.controllers.Census.services.fileService import FileService
from fastapi import UploadFile
import io

async def test_sbs_census_processing():
    """Test processing of SBS-Census-Final.csv file"""
    
    print("🧪 Testing SBS-Census-Final.csv Processing")
    print("=" * 60)
    
    # File path
    csv_file_path = "scripts/test_csv_files/SBS-Census-Final (1).csv"
    
    if not os.path.exists(csv_file_path):
        print(f"❌ File not found: {csv_file_path}")
        return
    
    try:
        # Step 1: Load and examine the raw file
        print("\n📋 Step 1: Raw File Analysis")
        print("-" * 30)
        
        df_raw = pd.read_csv(csv_file_path)
        print(f"✅ File loaded successfully")
        print(f"   - Rows: {len(df_raw)}")
        print(f"   - Columns: {len(df_raw.columns)}")
        print(f"   - Column names: {list(df_raw.columns)}")
        
        # Show first few rows
        print(f"\n📊 First 5 rows:")
        print(df_raw.head().to_string())
        
        # Check data patterns
        print(f"\n🔍 Data Pattern Analysis:")
        print(f"   - Unique relationships: {df_raw['Relationship'].unique()}")
        print(f"   - Employee count: {len(df_raw[df_raw['Relationship'] == 'Employee'])}")
        print(f"   - Spouse count: {len(df_raw[df_raw['Relationship'] == 'Spouse'])}")
        print(f"   - Child count: {len(df_raw[df_raw['Relationship'] == 'Child'])}")
        
        # Step 2: Test file service loading
        print("\n📁 Step 2: File Service Loading")
        print("-" * 30)
        
        file_service = FileService()
        
        # Create UploadFile object
        with open(csv_file_path, 'rb') as f:
            file_content = f.read()
        
        upload_file = UploadFile(
            filename="SBS-Census-Final.csv",
            file=io.BytesIO(file_content)
        )
        
        file_result = await file_service.load_and_validate_file(upload_file)
        
        if file_result["success"]:
            print("✅ File service loading successful")
            print(f"   - Processed rows: {file_result['file_info']['rows']}")
            print(f"   - Processed columns: {file_result['file_info']['columns']}")
        else:
            print(f"❌ File service loading failed: {file_result.get('message', 'Unknown error')}")
            return
        
        # Step 3: Test full orchestration
        print("\n🎯 Step 3: Full Orchestration Processing")
        print("-" * 30)
        
        orchestration_service = OrchestrationService()
        
        # Reset file pointer
        upload_file.file.seek(0)
        
        result = await orchestration_service.process_census_file(upload_file, return_dataframe=True)
        
        print(f"\n📊 Processing Result:")
        print(f"   - Success: {result.get('success', False)}")
        print(f"   - Status Code: {result.get('status_code', 'N/A')}")
        print(f"   - Message: {result.get('message', 'N/A')}")
        
        if result.get('success'):
            data = result.get('data', {})
            summary = data.get('summary', {})
            
            print(f"\n✅ Processing Summary:")
            print(f"   - Total employees: {summary.get('total_employees', 'N/A')}")
            print(f"   - Processing success: {summary.get('processing_success', 'N/A')}")
            print(f"   - Data quality score: {summary.get('data_quality_score', 'N/A')}")
            
            # Check statistics
            stats = data.get('statistics', {})
            if stats:
                demographics = stats.get('demographics', {})
                print(f"\n📈 Demographics:")
                print(f"   - Average age: {demographics.get('average_age', 'N/A')}")
                print(f"   - Gender distribution: {demographics.get('gender_distribution', {})}")
                print(f"   - Marital status: {demographics.get('marital_status_distribution', {})}")
            
            # Check employees data
            employees = data.get('employees', [])
            if employees:
                print(f"\n👥 Employee Data (first 3):")
                for i, emp in enumerate(employees[:3]):
                    print(f"   Employee {i+1}:")
                    print(f"     - ID: {emp.get('employee_id', 'N/A')}")
                    print(f"     - Name: {emp.get('name', 'N/A')}")
                    print(f"     - Age: {emp.get('age', 'N/A')}")
                    print(f"     - Gender: {emp.get('gender', 'N/A')}")
                    print(f"     - Predicted plan: {emp.get('predicted_plan_type', 'N/A')}")
            
            # Check enriched dataframe
            enriched_df = data.get('enriched_dataframe', [])
            if enriched_df:
                print(f"\n📊 Enriched Dataframe:")
                print(f"   - Records: {len(enriched_df)}")
                if enriched_df:
                    print(f"   - Sample fields: {list(enriched_df[0].keys())[:10]}")
            
        else:
            print(f"\n❌ Processing Failed:")
            error_data = result.get('data', {})
            print(f"   - Error: {result.get('error', 'Unknown')}")
            print(f"   - Details: {error_data}")
            
            # Save error details for analysis
            with open('sbs_census_error_analysis.json', 'w') as f:
                json.dump(result, f, indent=2, default=str)
            print(f"   - Error details saved to: sbs_census_error_analysis.json")
        
        # Save full result for analysis
        with open('sbs_census_full_result.json', 'w') as f:
            json.dump(result, f, indent=2, default=str)
        print(f"\n💾 Full result saved to: sbs_census_full_result.json")
        
    except Exception as e:
        print(f"\n💥 Critical Error: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_sbs_census_processing())
