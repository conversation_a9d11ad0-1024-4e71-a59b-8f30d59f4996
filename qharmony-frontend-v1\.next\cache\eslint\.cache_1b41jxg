[{"C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\APILayer\\axios_helper.ts": "1", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\components\\AddNewGroupModal.tsx": "2", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\components\\LoadingOptimizer.tsx": "3", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\create-carrier\\page.tsx": "4", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\create-plan\\components\\CreatePlanOptimizer.tsx": "5", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\create-plan\\page.tsx": "6", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\create-plan\\services\\planApi.ts": "7", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\employee-enrol\\components\\AdditionalBenefitsPage.tsx": "8", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\employee-enrol\\components\\BenefitsEnrollmentBot.tsx": "9", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\employee-enrol\\components\\BotQuestion.tsx": "10", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\employee-enrol\\components\\ConfirmationPage.tsx": "11", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\employee-enrol\\components\\DentalPlanPage.tsx": "12", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\employee-enrol\\components\\DependentsConfirmationPage.tsx": "13", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\employee-enrol\\components\\MedicalPlanPage.tsx": "14", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\employee-enrol\\components\\PersonalizationPage.tsx": "15", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\employee-enrol\\components\\SummaryPage.tsx": "16", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\employee-enrol\\components\\ui\\badge.tsx": "17", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\employee-enrol\\components\\ui\\button.tsx": "18", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\employee-enrol\\components\\ui\\card.tsx": "19", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\employee-enrol\\components\\ui\\checkbox.tsx": "20", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\employee-enrol\\components\\ui\\label.tsx": "21", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\employee-enrol\\components\\ui\\radio-group.tsx": "22", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\employee-enrol\\components\\VisionPlanPage.tsx": "23", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\employee-enrol\\components\\WelcomePage.tsx": "24", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\employee-enrol\\lib\\utils.ts": "25", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\employee-enrol\\page.tsx": "26", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\employee-enrol\\pages\\MedicalPlanPage.tsx": "27", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\employee-enrol\\pages\\PersonalizationPage.tsx": "28", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\employee-enrol\\pages\\WelcomePage.tsx": "29", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\employee-enrol\\utils\\dateUtils.ts": "30", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\hooks\\usePerformanceMonitor.ts": "31", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\layout.tsx": "32", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\manage-groups\\company\\[companyId]\\confirmation\\page.tsx": "33", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\manage-groups\\company\\[companyId]\\enrollment-dates\\page.tsx": "34", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\manage-groups\\company\\[companyId]\\plans\\components\\CreatePlanForm.tsx": "35", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\manage-groups\\company\\[companyId]\\plans\\components\\PlanSelectionModal.tsx": "36", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\manage-groups\\company\\[companyId]\\plans\\page.tsx": "37", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\manage-groups\\company\\[companyId]\\plans\\[planId]\\confirmation\\page.tsx": "38", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\manage-groups\\company\\[companyId]\\plans\\[planId]\\review\\page.tsx": "39", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\manage-groups\\company\\[companyId]\\plans\\[planId]\\set-dates\\page.tsx": "40", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\manage-groups\\company\\[companyId]\\review\\page.tsx": "41", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\manage-groups\\company\\[companyId]\\set-dates\\page.tsx": "42", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\manage-groups\\components\\CompanyCard.tsx": "43", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\manage-groups\\page.tsx": "44", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\manage-groups\\select-company\\page.tsx": "45", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\manage-groups\\services\\planAssignmentApi.ts": "46", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\manage-groups\\[companyId]\\assign-plans\\page.tsx": "47", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\manage-groups\\[companyId]\\manage-plans\\add-plan\\page.tsx": "48", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\manage-groups\\[companyId]\\manage-plans\\configure-plan\\page.tsx": "49", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\manage-groups\\[companyId]\\manage-plans\\page.tsx": "50", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\manage-groups\\[companyId]\\page.tsx": "51", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\page.tsx": "52", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\plans\\page.tsx": "53", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\renewal\\page.tsx": "54", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\renewal\\[groupId]\\document-upload\\page.tsx": "55", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\renewal\\[groupId]\\export\\page.tsx": "56", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\renewal\\[groupId]\\finalize\\page.tsx": "57", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\renewal\\[groupId]\\page.tsx": "58", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\renewal\\[groupId]\\plan-configuration\\page.tsx": "59", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\renewal\\[groupId]\\renewal-options\\page.tsx": "60", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\renewal\\[groupId]\\validation\\page.tsx": "61", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\api\\proxy-pdf\\route.ts": "62", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\benefits\\coming-soon\\page.tsx": "63", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\consent_success\\page.tsx": "64", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\dashboard\\AI_enroller.tsx": "65", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\dashboard\\Career_assistant.tsx": "66", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\dashboard\\CompanyDetails.tsx": "67", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\dashboard\\edit_user_profile_popup.tsx": "68", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\dashboard\\enhanced_edit_user_profile_popup.tsx": "69", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\dashboard\\faq_help_section.tsx": "70", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\dashboard\\infinite_scroll_section.tsx": "71", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\dashboard\\page.tsx": "72", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\dashboard\\ProfileUpdateCard.tsx": "73", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\dashboard\\UserDetails.tsx": "74", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\editBenefit\\[benefitId]\\page.tsx": "75", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\group\\[groupId]\\page.tsx": "76", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\hipaa\\page.tsx": "77", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\layout.tsx": "78", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\login\\page.tsx": "79", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\manage-companies\\AddCompany.tsx": "80", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\manage-companies\\page.tsx": "81", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\manage-groups\\page.tsx": "82", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\manageBenefits\\page.tsx": "83", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\mobile\\dashboard\\mobile_sidebar.tsx": "84", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\mobile\\dashboard\\page.tsx": "85", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\mobile\\onboard\\page.tsx": "86", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\mobile\\qHarmonyBot\\page.tsx": "87", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\mobile\\viewBenefitDetails\\[benefitId]\\page.tsx": "88", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\mobile\\viewBenefitsByType\\[benefitType]\\page.tsx": "89", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\notification-history\\page.tsx": "90", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\notifications-analytics\\[notificationId]\\page.tsx": "91", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\onboard\\page.tsx": "92", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\page.tsx": "93", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\proactive-messaging\\page.tsx": "94", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\proactive-messaging\\ProactiveMessaging.tsx": "95", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\qHarmonyBot\\page.tsx": "96", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\team-members\\AddTeamMemberDialogue.tsx": "97", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\team-members\\page.tsx": "98", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\teams-landing\\page.tsx": "99", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\teamsauth\\authconfig.ts": "100", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\view-all-users\\page.tsx": "101", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\viewBenefitDetails\\[benefitId]\\page.tsx": "102", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\viewBenefitsByType\\[benefitType]\\page.tsx": "103", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\wellness\\page.tsx": "104", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\wellness\\results\\page.tsx": "105", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\components\\AuthContext.tsx": "106", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\components\\benefit_vector_map.tsx": "107", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\components\\Footer.tsx": "108", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\components\\mobile_edge_fill.tsx": "109", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\components\\Navbar.tsx": "110", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\components\\ProtectedRoute.tsx": "111", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\components\\RightPanelOnlyComponent.tsx": "112", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\components\\ShareButtons.tsx": "113", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\components\\sidebar.tsx": "114", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\components\\topbar.tsx": "115", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\components\\withSidebar.tsx": "116", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\middleware\\benefits_middleware.ts": "117", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\middleware\\chatbot_middleware.ts": "118", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\middleware\\company_middleware.ts": "119", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\middleware\\group_middleware.tsx": "120", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\middleware\\user_middleware.ts": "121", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\models\\admin.ts": "122", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\models\\company.ts": "123", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\redux\\hooks.ts": "124", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\redux\\reducers\\benefitsSlice.ts": "125", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\redux\\reducers\\companySlice.ts": "126", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\redux\\reducers\\mobileSidebarSlice.ts": "127", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\redux\\reducers\\onboardingSlice.ts": "128", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\redux\\reducers\\qHarmonyBotSlice.ts": "129", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\redux\\reducers\\userSlice.ts": "130", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\redux\\slices\\companySlice.ts": "131", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\redux\\store.ts": "132", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\redux\\StoreProvider.tsx": "133", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\services\\wellness.service.ts": "134", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\theme.js": "135", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\utils\\env.ts": "136", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\utils\\firebase.js": "137", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\employee-enrol\\components\\ChatMessage.tsx": "138", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\employee-enrol\\components\\CompareModal.tsx": "139", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\employee-enrol\\components\\FloatingHelp.tsx": "140", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\employee-enrol\\components\\LifeInsurancePlanPage.tsx": "141", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\employee-enrol\\components\\VideoPlayer.tsx": "142", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\employee-enrol\\components\\EnrollmentProfileModal.tsx": "143", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\employee-enrol\\services\\enrollmentService.ts": "144", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\employee-enrol\\components\\ChatModal.tsx": "145", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\employee-enrol\\components\\EnrollmentHeader.tsx": "146", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\employee-enrol\\components\\FloatingChatButton.tsx": "147", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\employee-enrol\\components\\AskQuestionsButton.tsx": "148", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\employee-enrol\\components\\CustomModal.tsx": "149", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\employee-enrol\\services\\bulkWaiveApi.ts": "150", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\employee-enrol\\components\\ADDPlanPage.tsx": "151", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\employee-enrol\\components\\SignatureModal.tsx": "152", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\employee-enrol\\components\\SignatureViewer.tsx": "153", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\employee-enrol\\services\\bulkEnrollmentApi.ts": "154", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\employee-enrol\\services\\signatureApi.ts": "155", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\employee-enrol\\utils\\signatureUtils.ts": "156", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\employee-enrol\\signature-api-test\\page.tsx": "157", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\employee-enrol\\signature-test\\page.tsx": "158", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\employee-enrol\\components\\DynamicPlanPage.tsx": "159", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\employee-enrol\\utils\\planCategoryUtils.ts": "160", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\manage-groups\\company\\[companyId]\\plans\\components\\NotificationModal.tsx": "161", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\manage-groups\\company\\[companyId]\\plans\\hooks\\useNotification.tsx": "162", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\employee-enrol\\components\\CoverageTierSelector.tsx": "163", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\employee-enrol\\components\\DynamicSummarySection.tsx": "164", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\employee-enrol\\utils\\planStorageUtils.ts": "165", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\census\\components\\AskBrea.tsx": "166", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\census\\components\\EmpCensusApp.tsx": "167", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\census\\components\\EmployeeDetailModal.tsx": "168", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\census\\components\\NavigationDropdown.tsx": "169", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\census\\components\\NavigationProvider.tsx": "170", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\census\\components\\ProfileSettingsModal.tsx": "171", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\census\\components\\ui\\accordion.tsx": "172", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\census\\components\\ui\\alert-dialog.tsx": "173", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\census\\components\\ui\\alert.tsx": "174", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\census\\components\\ui\\aspect-ratio.tsx": "175", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\census\\components\\ui\\avatar.tsx": "176", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\census\\components\\ui\\badge.tsx": "177", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\census\\components\\ui\\breadcrumb.tsx": "178", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\census\\components\\ui\\button.tsx": "179", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\census\\components\\ui\\calendar.tsx": "180", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\census\\components\\ui\\card.tsx": "181", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\census\\components\\ui\\carousel.tsx": "182", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\census\\components\\ui\\chart.tsx": "183", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\census\\components\\ui\\checkbox.tsx": "184", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\census\\components\\ui\\collapsible.tsx": "185", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\census\\components\\ui\\command.tsx": "186", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\census\\components\\ui\\context-menu.tsx": "187", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\census\\components\\ui\\dialog.tsx": "188", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\census\\components\\ui\\drawer.tsx": "189", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\census\\components\\ui\\dropdown-menu.tsx": "190", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\census\\components\\ui\\form.tsx": "191", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\census\\components\\ui\\hover-card.tsx": "192", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\census\\components\\ui\\input-otp.tsx": "193", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\census\\components\\ui\\input.tsx": "194", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\census\\components\\ui\\label.tsx": "195", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\census\\components\\ui\\menubar.tsx": "196", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\census\\components\\ui\\navigation-menu.tsx": "197", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\census\\components\\ui\\pagination.tsx": "198", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\census\\components\\ui\\popover.tsx": "199", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\census\\components\\ui\\progress.tsx": "200", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\census\\components\\ui\\radio-group.tsx": "201", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\census\\components\\ui\\resizable.tsx": "202", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\census\\components\\ui\\scroll-area.tsx": "203", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\census\\components\\ui\\select.tsx": "204", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\census\\components\\ui\\separator.tsx": "205", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\census\\components\\ui\\sheet.tsx": "206", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\census\\components\\ui\\sidebar.tsx": "207", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\census\\components\\ui\\skeleton.tsx": "208", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\census\\components\\ui\\slider.tsx": "209", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\census\\components\\ui\\sonner.tsx": "210", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\census\\components\\ui\\switch.tsx": "211", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\census\\components\\ui\\table.tsx": "212", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\census\\components\\ui\\tabs.tsx": "213", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\census\\components\\ui\\textarea.tsx": "214", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\census\\components\\ui\\toast.tsx": "215", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\census\\components\\ui\\toaster.tsx": "216", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\census\\components\\ui\\toggle-group.tsx": "217", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\census\\components\\ui\\toggle.tsx": "218", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\census\\components\\ui\\tooltip.tsx": "219", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\census\\components\\ui\\use-toast.ts": "220", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\census\\components\\UpgradePrompt.tsx": "221", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\census\\hooks\\use-mobile.tsx": "222", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\census\\hooks\\use-toast.ts": "223", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\census\\hooks\\usePlanRestrictions.tsx": "224", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\census\\lib\\react-router-dom.tsx": "225", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\census\\lib\\utils.ts": "226", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\census\\nav-items.tsx": "227", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\census\\page.tsx": "228", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\census\\public\\Billing.tsx": "229", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\census\\public\\BrokerDashboard.tsx": "230", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\census\\public\\EmployerInsight.tsx": "231", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\census\\public\\EmployerInvite.tsx": "232", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\census\\public\\GenerateProposal.tsx": "233", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\census\\public\\HRInsight.tsx": "234", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\census\\public\\HRUpload.tsx": "235", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\census\\public\\Index.tsx": "236", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\census\\public\\LoginPrompt.tsx": "237", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\census\\public\\NotFound.tsx": "238", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\census\\public\\PreviewReport.tsx": "239", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\census\\public\\Pricing.tsx": "240", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\census\\public\\Processing.tsx": "241", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\census\\public\\UploadCensus.tsx": "242", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\dashboard\\CensusCard.tsx": "243", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\census\\components\\ProfileHandler.tsx": "244", "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\census\\public\\FilePreview.tsx": "245"}, {"size": 4342, "mtime": 1751454096138, "results": "246", "hashOfConfig": "247"}, {"size": 6773, "mtime": 1749492353949, "results": "248", "hashOfConfig": "247"}, {"size": 1381, "mtime": 1749119896404, "results": "249", "hashOfConfig": "247"}, {"size": 29866, "mtime": 1749648940857, "results": "250", "hashOfConfig": "247"}, {"size": 1782, "mtime": 1749120364870, "results": "251", "hashOfConfig": "247"}, {"size": 66567, "mtime": 1751568370655, "results": "252", "hashOfConfig": "247"}, {"size": 19094, "mtime": 1751453967840, "results": "253", "hashOfConfig": "247"}, {"size": 17130, "mtime": 1749143107053, "results": "254", "hashOfConfig": "247"}, {"size": 13387, "mtime": 1749488334219, "results": "255", "hashOfConfig": "247"}, {"size": 3665, "mtime": 1749717496012, "results": "256", "hashOfConfig": "247"}, {"size": 38695, "mtime": 1751646643999, "results": "257", "hashOfConfig": "247"}, {"size": 29801, "mtime": 1751659702694, "results": "258", "hashOfConfig": "247"}, {"size": 48660, "mtime": 1751704378851, "results": "259", "hashOfConfig": "247"}, {"size": 9808, "mtime": 1749719056493, "results": "260", "hashOfConfig": "247"}, {"size": 15309, "mtime": 1750184353660, "results": "261", "hashOfConfig": "247"}, {"size": 69739, "mtime": 1751658942129, "results": "262", "hashOfConfig": "247"}, {"size": 1130, "mtime": 1749493833444, "results": "263", "hashOfConfig": "247"}, {"size": 1839, "mtime": 1748774372282, "results": "264", "hashOfConfig": "247"}, {"size": 1881, "mtime": 1748774372282, "results": "265", "hashOfConfig": "247"}, {"size": 1060, "mtime": 1748774372282, "results": "266", "hashOfConfig": "247"}, {"size": 714, "mtime": 1748774372282, "results": "267", "hashOfConfig": "247"}, {"size": 1471, "mtime": 1748774372282, "results": "268", "hashOfConfig": "247"}, {"size": 26871, "mtime": 1750097859496, "results": "269", "hashOfConfig": "247"}, {"size": 10944, "mtime": 1750097725274, "results": "270", "hashOfConfig": "247"}, {"size": 166, "mtime": 1748774372282, "results": "271", "hashOfConfig": "247"}, {"size": 36786, "mtime": 1751704335217, "results": "272", "hashOfConfig": "247"}, {"size": 11652, "mtime": 1749494242404, "results": "273", "hashOfConfig": "247"}, {"size": 10230, "mtime": 1748863273881, "results": "274", "hashOfConfig": "247"}, {"size": 3720, "mtime": 1748862668044, "results": "275", "hashOfConfig": "247"}, {"size": 1615, "mtime": 1749204201389, "results": "276", "hashOfConfig": "247"}, {"size": 1287, "mtime": 1749492336866, "results": "277", "hashOfConfig": "247"}, {"size": 445, "mtime": 1749727916189, "results": "278", "hashOfConfig": "247"}, {"size": 14297, "mtime": 1749489883968, "results": "279", "hashOfConfig": "247"}, {"size": 12183, "mtime": 1751623520745, "results": "280", "hashOfConfig": "247"}, {"size": 63744, "mtime": 1751659738977, "results": "281", "hashOfConfig": "247"}, {"size": 36259, "mtime": 1751629869000, "results": "282", "hashOfConfig": "247"}, {"size": 64131, "mtime": 1751648758289, "results": "283", "hashOfConfig": "247"}, {"size": 5975, "mtime": 1748885067103, "results": "284", "hashOfConfig": "247"}, {"size": 11200, "mtime": 1749494110657, "results": "285", "hashOfConfig": "247"}, {"size": 7740, "mtime": 1748868348362, "results": "286", "hashOfConfig": "247"}, {"size": 27763, "mtime": 1751623724098, "results": "287", "hashOfConfig": "247"}, {"size": 29106, "mtime": 1751623678525, "results": "288", "hashOfConfig": "247"}, {"size": 2795, "mtime": 1749495024534, "results": "289", "hashOfConfig": "247"}, {"size": 11635, "mtime": 1751622947991, "results": "290", "hashOfConfig": "247"}, {"size": 13811, "mtime": 1751623039466, "results": "291", "hashOfConfig": "247"}, {"size": 20890, "mtime": 1751648759672, "results": "292", "hashOfConfig": "247"}, {"size": 11750, "mtime": 1749143879934, "results": "293", "hashOfConfig": "247"}, {"size": 8016, "mtime": 1749388493875, "results": "294", "hashOfConfig": "247"}, {"size": 12629, "mtime": 1749363661699, "results": "295", "hashOfConfig": "247"}, {"size": 15625, "mtime": 1751484130095, "results": "296", "hashOfConfig": "247"}, {"size": 9142, "mtime": 1751623157459, "results": "297", "hashOfConfig": "247"}, {"size": 17399, "mtime": 1751394203389, "results": "298", "hashOfConfig": "247"}, {"size": 32975, "mtime": 1751632830905, "results": "299", "hashOfConfig": "247"}, {"size": 4502, "mtime": 1748862668046, "results": "300", "hashOfConfig": "247"}, {"size": 8344, "mtime": 1748346626402, "results": "301", "hashOfConfig": "247"}, {"size": 9293, "mtime": 1748346626402, "results": "302", "hashOfConfig": "247"}, {"size": 7915, "mtime": 1748862696099, "results": "303", "hashOfConfig": "247"}, {"size": 10591, "mtime": 1748346626484, "results": "304", "hashOfConfig": "247"}, {"size": 7181, "mtime": 1748346626401, "results": "305", "hashOfConfig": "247"}, {"size": 7148, "mtime": 1748346626402, "results": "306", "hashOfConfig": "247"}, {"size": 8133, "mtime": 1748864260901, "results": "307", "hashOfConfig": "247"}, {"size": 943, "mtime": 1745640648820, "results": "308", "hashOfConfig": "247"}, {"size": 17716, "mtime": 1746127687953, "results": "309", "hashOfConfig": "247"}, {"size": 1466, "mtime": 1745640648820, "results": "310", "hashOfConfig": "247"}, {"size": 1988, "mtime": 1749203877901, "results": "311", "hashOfConfig": "247"}, {"size": 2375, "mtime": 1745640648821, "results": "312", "hashOfConfig": "247"}, {"size": 8515, "mtime": 1745640648822, "results": "313", "hashOfConfig": "247"}, {"size": 8116, "mtime": 1749653326632, "results": "314", "hashOfConfig": "247"}, {"size": 43155, "mtime": 1750075906110, "results": "315", "hashOfConfig": "247"}, {"size": 11397, "mtime": 1745640648823, "results": "316", "hashOfConfig": "247"}, {"size": 4006, "mtime": 1749363377416, "results": "317", "hashOfConfig": "247"}, {"size": 2722, "mtime": 1752217646980, "results": "318", "hashOfConfig": "247"}, {"size": 5305, "mtime": 1749576669082, "results": "319", "hashOfConfig": "247"}, {"size": 4956, "mtime": 1749570394169, "results": "320", "hashOfConfig": "247"}, {"size": 21915, "mtime": 1745640648826, "results": "321", "hashOfConfig": "247"}, {"size": 21810, "mtime": 1749363496387, "results": "322", "hashOfConfig": "247"}, {"size": 61519, "mtime": 1748015416291, "results": "323", "hashOfConfig": "247"}, {"size": 920, "mtime": 1751879890993, "results": "324", "hashOfConfig": "247"}, {"size": 3888, "mtime": 1745945160336, "results": "325", "hashOfConfig": "247"}, {"size": 7188, "mtime": 1745640648829, "results": "326", "hashOfConfig": "247"}, {"size": 4782, "mtime": 1745640648829, "results": "327", "hashOfConfig": "247"}, {"size": 8438, "mtime": 1749363495046, "results": "328", "hashOfConfig": "247"}, {"size": 9501, "mtime": 1745640648830, "results": "329", "hashOfConfig": "247"}, {"size": 7686, "mtime": 1745640648830, "results": "330", "hashOfConfig": "247"}, {"size": 2605, "mtime": 1745640648832, "results": "331", "hashOfConfig": "247"}, {"size": 18673, "mtime": 1745640648833, "results": "332", "hashOfConfig": "247"}, {"size": 11732, "mtime": 1745640648834, "results": "333", "hashOfConfig": "247"}, {"size": 12650, "mtime": 1745640648835, "results": "334", "hashOfConfig": "247"}, {"size": 7409, "mtime": 1745640648835, "results": "335", "hashOfConfig": "247"}, {"size": 4864, "mtime": 1745640648836, "results": "336", "hashOfConfig": "247"}, {"size": 7421, "mtime": 1745640648837, "results": "337", "hashOfConfig": "247"}, {"size": 20212, "mtime": 1749635263163, "results": "338", "hashOfConfig": "247"}, {"size": 7776, "mtime": 1749363412547, "results": "339", "hashOfConfig": "247"}, {"size": 1442, "mtime": 1745640648841, "results": "340", "hashOfConfig": "247"}, {"size": 13990, "mtime": 1745640648840, "results": "341", "hashOfConfig": "247"}, {"size": 11466, "mtime": 1749363416774, "results": "342", "hashOfConfig": "247"}, {"size": 10201, "mtime": 1749653326632, "results": "343", "hashOfConfig": "247"}, {"size": 15468, "mtime": 1745640648842, "results": "344", "hashOfConfig": "247"}, {"size": 2378, "mtime": 1745640648843, "results": "345", "hashOfConfig": "247"}, {"size": 1552, "mtime": 1749362052792, "results": "346", "hashOfConfig": "247"}, {"size": 1748, "mtime": 1745640648844, "results": "347", "hashOfConfig": "247"}, {"size": 14389, "mtime": 1745640648845, "results": "348", "hashOfConfig": "247"}, {"size": 7444, "mtime": 1745640648846, "results": "349", "hashOfConfig": "247"}, {"size": 6740, "mtime": 1746788280311, "results": "350", "hashOfConfig": "247"}, {"size": 7554, "mtime": 1748862668047, "results": "351", "hashOfConfig": "247"}, {"size": 4954, "mtime": 1749645906386, "results": "352", "hashOfConfig": "247"}, {"size": 4656, "mtime": 1745640648848, "results": "353", "hashOfConfig": "247"}, {"size": 3295, "mtime": 1746082092739, "results": "354", "hashOfConfig": "247"}, {"size": 2203, "mtime": 1745640648848, "results": "355", "hashOfConfig": "247"}, {"size": 7519, "mtime": 1752217679826, "results": "356", "hashOfConfig": "247"}, {"size": 3364, "mtime": 1749653461271, "results": "357", "hashOfConfig": "247"}, {"size": 5157, "mtime": 1745640648848, "results": "358", "hashOfConfig": "247"}, {"size": 5128, "mtime": 1746126971616, "results": "359", "hashOfConfig": "247"}, {"size": 14925, "mtime": 1745640648849, "results": "360", "hashOfConfig": "247"}, {"size": 5788, "mtime": 1750011283420, "results": "361", "hashOfConfig": "247"}, {"size": 795, "mtime": 1745640648850, "results": "362", "hashOfConfig": "247"}, {"size": 8666, "mtime": 1745640648850, "results": "363", "hashOfConfig": "247"}, {"size": 2052, "mtime": 1749487369534, "results": "364", "hashOfConfig": "247"}, {"size": 15799, "mtime": 1749926012437, "results": "365", "hashOfConfig": "247"}, {"size": 605, "mtime": 1745640648852, "results": "366", "hashOfConfig": "247"}, {"size": 1503, "mtime": 1745640648853, "results": "367", "hashOfConfig": "247"}, {"size": 157, "mtime": 1745640648853, "results": "368", "hashOfConfig": "247"}, {"size": 327, "mtime": 1745640648854, "results": "369", "hashOfConfig": "247"}, {"size": 277, "mtime": 1745640648854, "results": "370", "hashOfConfig": "247"}, {"size": 6447, "mtime": 1745640648856, "results": "371", "hashOfConfig": "247"}, {"size": 1775, "mtime": 1745640648856, "results": "372", "hashOfConfig": "247"}, {"size": 612, "mtime": 1745640648857, "results": "373", "hashOfConfig": "247"}, {"size": 1895, "mtime": 1745640648857, "results": "374", "hashOfConfig": "247"}, {"size": 1502, "mtime": 1745640648858, "results": "375", "hashOfConfig": "247"}, {"size": 5846, "mtime": 1749647378489, "results": "376", "hashOfConfig": "247"}, {"size": 1994, "mtime": 1748893803582, "results": "377", "hashOfConfig": "247"}, {"size": 787, "mtime": 1745640648859, "results": "378", "hashOfConfig": "247"}, {"size": 226, "mtime": 1745640648854, "results": "379", "hashOfConfig": "247"}, {"size": 2052, "mtime": 1749487381753, "results": "380", "hashOfConfig": "247"}, {"size": 2026, "mtime": 1745731018571, "results": "381", "hashOfConfig": "247"}, {"size": 4906, "mtime": 1751452030985, "results": "382", "hashOfConfig": "247"}, {"size": 1768, "mtime": 1749572544730, "results": "383", "hashOfConfig": "247"}, {"size": 2049, "mtime": 1749717439611, "results": "384", "hashOfConfig": "247"}, {"size": 12414, "mtime": 1749717903137, "results": "385", "hashOfConfig": "247"}, {"size": 12788, "mtime": 1749722122554, "results": "386", "hashOfConfig": "247"}, {"size": 30263, "mtime": 1750112783482, "results": "387", "hashOfConfig": "247"}, {"size": 5921, "mtime": 1749729882367, "results": "388", "hashOfConfig": "247"}, {"size": 17808, "mtime": 1749924832421, "results": "389", "hashOfConfig": "247"}, {"size": 8319, "mtime": 1750009480864, "results": "390", "hashOfConfig": "247"}, {"size": 11782, "mtime": 1750244182410, "results": "391", "hashOfConfig": "247"}, {"size": 6672, "mtime": 1751373445564, "results": "392", "hashOfConfig": "247"}, {"size": 2775, "mtime": 1750001437358, "results": "393", "hashOfConfig": "247"}, {"size": 987, "mtime": 1750104673936, "results": "394", "hashOfConfig": "247"}, {"size": 4315, "mtime": 1750077510158, "results": "395", "hashOfConfig": "247"}, {"size": 6339, "mtime": 1750172271904, "results": "396", "hashOfConfig": "247"}, {"size": 27004, "mtime": 1750190722699, "results": "397", "hashOfConfig": "247"}, {"size": 10534, "mtime": 1750259467415, "results": "398", "hashOfConfig": "247"}, {"size": 10165, "mtime": 1750254022410, "results": "399", "hashOfConfig": "247"}, {"size": 6800, "mtime": 1750251240374, "results": "400", "hashOfConfig": "247"}, {"size": 5559, "mtime": 1750259792117, "results": "401", "hashOfConfig": "247"}, {"size": 5851, "mtime": 1750254057321, "results": "402", "hashOfConfig": "247"}, {"size": 10935, "mtime": 1750939410241, "results": "403", "hashOfConfig": "247"}, {"size": 7600, "mtime": 1750939410241, "results": "404", "hashOfConfig": "247"}, {"size": 32940, "mtime": 1752480682673, "results": "405", "hashOfConfig": "247"}, {"size": 5918, "mtime": 1751362256775, "results": "406", "hashOfConfig": "247"}, {"size": 3649, "mtime": 1751483894525, "results": "407", "hashOfConfig": "247"}, {"size": 1724, "mtime": 1751281533818, "results": "408", "hashOfConfig": "247"}, {"size": 11099, "mtime": 1751701094386, "results": "409", "hashOfConfig": "247"}, {"size": 10509, "mtime": 1751659627770, "results": "410", "hashOfConfig": "247"}, {"size": 10262, "mtime": 1751646682832, "results": "411", "hashOfConfig": "247"}, {"size": 1449, "mtime": 1752598605165, "results": "412", "hashOfConfig": "247"}, {"size": 2380, "mtime": 1752588390553, "results": "413", "hashOfConfig": "247"}, {"size": 15675, "mtime": 1751876138502, "results": "414", "hashOfConfig": "247"}, {"size": 5876, "mtime": 1752589939537, "results": "415", "hashOfConfig": "247"}, {"size": 1316, "mtime": 1752218719258, "results": "416", "hashOfConfig": "247"}, {"size": 5742, "mtime": 1751876191751, "results": "417", "hashOfConfig": "247"}, {"size": 2037, "mtime": 1751876563610, "results": "418", "hashOfConfig": "247"}, {"size": 4549, "mtime": 1751876615384, "results": "419", "hashOfConfig": "247"}, {"size": 1647, "mtime": 1751876659336, "results": "420", "hashOfConfig": "247"}, {"size": 145, "mtime": 1751800628124, "results": "421", "hashOfConfig": "247"}, {"size": 1457, "mtime": 1751876691724, "results": "422", "hashOfConfig": "247"}, {"size": 1168, "mtime": 1751876715347, "results": "423", "hashOfConfig": "247"}, {"size": 2820, "mtime": 1751877129137, "results": "424", "hashOfConfig": "247"}, {"size": 1961, "mtime": 1751877163638, "results": "425", "hashOfConfig": "247"}, {"size": 2493, "mtime": 1751878611092, "results": "426", "hashOfConfig": "247"}, {"size": 1960, "mtime": 1751877236719, "results": "427", "hashOfConfig": "247"}, {"size": 6460, "mtime": 1751877274486, "results": "428", "hashOfConfig": "247"}, {"size": 10593, "mtime": 1751879166586, "results": "429", "hashOfConfig": "247"}, {"size": 1088, "mtime": 1751876748405, "results": "430", "hashOfConfig": "247"}, {"size": 324, "mtime": 1751800628129, "results": "431", "hashOfConfig": "247"}, {"size": 5022, "mtime": 1751877353845, "results": "432", "hashOfConfig": "247"}, {"size": 7448, "mtime": 1751877386438, "results": "433", "hashOfConfig": "247"}, {"size": 3959, "mtime": 1751877419532, "results": "434", "hashOfConfig": "247"}, {"size": 3127, "mtime": 1751877455370, "results": "435", "hashOfConfig": "247"}, {"size": 7497, "mtime": 1751877484792, "results": "436", "hashOfConfig": "247"}, {"size": 4251, "mtime": 1751877516399, "results": "437", "hashOfConfig": "247"}, {"size": 1215, "mtime": 1751876796886, "results": "438", "hashOfConfig": "247"}, {"size": 2227, "mtime": 1751876828873, "results": "439", "hashOfConfig": "247"}, {"size": 817, "mtime": 1751876853851, "results": "440", "hashOfConfig": "247"}, {"size": 738, "mtime": 1751876880095, "results": "441", "hashOfConfig": "247"}, {"size": 8212, "mtime": 1751877561872, "results": "442", "hashOfConfig": "247"}, {"size": 5178, "mtime": 1751877593597, "results": "443", "hashOfConfig": "247"}, {"size": 2896, "mtime": 1751878910540, "results": "444", "hashOfConfig": "247"}, {"size": 1263, "mtime": 1751876906177, "results": "445", "hashOfConfig": "247"}, {"size": 807, "mtime": 1751877657024, "results": "446", "hashOfConfig": "247"}, {"size": 1513, "mtime": 1751877687782, "results": "447", "hashOfConfig": "247"}, {"size": 1756, "mtime": 1751877713150, "results": "448", "hashOfConfig": "247"}, {"size": 1692, "mtime": 1751877743090, "results": "449", "hashOfConfig": "247"}, {"size": 5777, "mtime": 1751876932792, "results": "450", "hashOfConfig": "247"}, {"size": 789, "mtime": 1751877771147, "results": "451", "hashOfConfig": "247"}, {"size": 4385, "mtime": 1751877798416, "results": "452", "hashOfConfig": "247"}, {"size": 24089, "mtime": 1751879193942, "results": "453", "hashOfConfig": "247"}, {"size": 280, "mtime": 1751876957519, "results": "454", "hashOfConfig": "247"}, {"size": 1107, "mtime": 1751877874850, "results": "455", "hashOfConfig": "247"}, {"size": 923, "mtime": 1751800628145, "results": "456", "hashOfConfig": "247"}, {"size": 1170, "mtime": 1751876981528, "results": "457", "hashOfConfig": "247"}, {"size": 2886, "mtime": 1751877903079, "results": "458", "hashOfConfig": "247"}, {"size": 1940, "mtime": 1751877008418, "results": "459", "hashOfConfig": "247"}, {"size": 800, "mtime": 1751877037171, "results": "460", "hashOfConfig": "247"}, {"size": 4976, "mtime": 1751877934742, "results": "461", "hashOfConfig": "247"}, {"size": 800, "mtime": 1751878935248, "results": "462", "hashOfConfig": "247"}, {"size": 1848, "mtime": 1751879247272, "results": "463", "hashOfConfig": "247"}, {"size": 1482, "mtime": 1751877064493, "results": "464", "hashOfConfig": "247"}, {"size": 1177, "mtime": 1751877090990, "results": "465", "hashOfConfig": "247"}, {"size": 89, "mtime": 1751878045412, "results": "466", "hashOfConfig": "247"}, {"size": 2439, "mtime": 1751876219946, "results": "467", "hashOfConfig": "247"}, {"size": 584, "mtime": 1751800628150, "results": "468", "hashOfConfig": "247"}, {"size": 4087, "mtime": 1751808525094, "results": "469", "hashOfConfig": "247"}, {"size": 1701, "mtime": 1751800628151, "results": "470", "hashOfConfig": "247"}, {"size": 1356, "mtime": 1751878365099, "results": "471", "hashOfConfig": "247"}, {"size": 172, "mtime": 1751800628153, "results": "472", "hashOfConfig": "247"}, {"size": 2893, "mtime": 1752587459864, "results": "473", "hashOfConfig": "247"}, {"size": 1020, "mtime": 1752598550713, "results": "474", "hashOfConfig": "247"}, {"size": 20763, "mtime": 1751878113083, "results": "475", "hashOfConfig": "247"}, {"size": 21865, "mtime": 1752591062048, "results": "476", "hashOfConfig": "247"}, {"size": 67751, "mtime": 1752591802373, "results": "477", "hashOfConfig": "247"}, {"size": 10227, "mtime": 1752590996929, "results": "478", "hashOfConfig": "247"}, {"size": 16442, "mtime": 1752588705180, "results": "479", "hashOfConfig": "247"}, {"size": 21285, "mtime": 1752511199324, "results": "480", "hashOfConfig": "247"}, {"size": 7840, "mtime": 1752591372648, "results": "481", "hashOfConfig": "247"}, {"size": 31089, "mtime": 1752588451128, "results": "482", "hashOfConfig": "247"}, {"size": 9735, "mtime": 1752225970399, "results": "483", "hashOfConfig": "247"}, {"size": 1406, "mtime": 1752511113330, "results": "484", "hashOfConfig": "247"}, {"size": 8106, "mtime": 1752588542227, "results": "485", "hashOfConfig": "247"}, {"size": 12724, "mtime": 1752588645059, "results": "486", "hashOfConfig": "247"}, {"size": 5134, "mtime": 1752511180631, "results": "487", "hashOfConfig": "247"}, {"size": 9442, "mtime": 1752588361580, "results": "488", "hashOfConfig": "247"}, {"size": 3258, "mtime": 1752480671458, "results": "489", "hashOfConfig": "247"}, {"size": 4914, "mtime": 1752598571565, "results": "490", "hashOfConfig": "247"}, {"size": 11775, "mtime": 1752587434537, "results": "491", "hashOfConfig": "247"}, {"filePath": "492", "messages": "493", "suppressedMessages": "494", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "11aif0a", {"filePath": "495", "messages": "496", "suppressedMessages": "497", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "498", "messages": "499", "suppressedMessages": "500", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "501", "messages": "502", "suppressedMessages": "503", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "504", "messages": "505", "suppressedMessages": "506", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "507", "messages": "508", "suppressedMessages": "509", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "510", "messages": "511", "suppressedMessages": "512", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "513", "messages": "514", "suppressedMessages": "515", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "516", "messages": "517", "suppressedMessages": "518", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "519", "messages": "520", "suppressedMessages": "521", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "522", "messages": "523", "suppressedMessages": "524", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "525", "messages": "526", "suppressedMessages": "527", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "528", "messages": "529", "suppressedMessages": "530", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "531", "messages": "532", "suppressedMessages": "533", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "534", "messages": "535", "suppressedMessages": "536", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "537", "messages": "538", "suppressedMessages": "539", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "540", "messages": "541", "suppressedMessages": "542", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "543", "messages": "544", "suppressedMessages": "545", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "546", "messages": "547", "suppressedMessages": "548", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "549", "messages": "550", "suppressedMessages": "551", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "552", "messages": "553", "suppressedMessages": "554", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "555", "messages": "556", "suppressedMessages": "557", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "558", "messages": "559", "suppressedMessages": "560", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "561", "messages": "562", "suppressedMessages": "563", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "564", "messages": "565", "suppressedMessages": "566", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "567", "messages": "568", "suppressedMessages": "569", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "570", "messages": "571", "suppressedMessages": "572", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "573", "messages": "574", "suppressedMessages": "575", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "576", "messages": "577", "suppressedMessages": "578", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "579", "messages": "580", "suppressedMessages": "581", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "582", "messages": "583", "suppressedMessages": "584", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "585", "messages": "586", "suppressedMessages": "587", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "588", "messages": "589", "suppressedMessages": "590", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "591", "messages": "592", "suppressedMessages": "593", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "594", "messages": "595", "suppressedMessages": "596", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "597", "messages": "598", "suppressedMessages": "599", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "600", "messages": "601", "suppressedMessages": "602", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "603", "messages": "604", "suppressedMessages": "605", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "606", "messages": "607", "suppressedMessages": "608", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "609", "messages": "610", "suppressedMessages": "611", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "612", "messages": "613", "suppressedMessages": "614", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "615", "messages": "616", "suppressedMessages": "617", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "618", "messages": "619", "suppressedMessages": "620", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "621", "messages": "622", "suppressedMessages": "623", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "624", "messages": "625", "suppressedMessages": "626", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "627", "messages": "628", "suppressedMessages": "629", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "630", "messages": "631", "suppressedMessages": "632", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "633", "messages": "634", "suppressedMessages": "635", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "636", "messages": "637", "suppressedMessages": "638", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "639", "messages": "640", "suppressedMessages": "641", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "642", "messages": "643", "suppressedMessages": "644", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "645", "messages": "646", "suppressedMessages": "647", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "648", "messages": "649", "suppressedMessages": "650", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "651", "messages": "652", "suppressedMessages": "653", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "654", "messages": "655", "suppressedMessages": "656", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "657", "messages": "658", "suppressedMessages": "659", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "660", "messages": "661", "suppressedMessages": "662", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "663", "messages": "664", "suppressedMessages": "665", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "666", "messages": "667", "suppressedMessages": "668", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "669", "messages": "670", "suppressedMessages": "671", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "672", "messages": "673", "suppressedMessages": "674", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "675", "messages": "676", "suppressedMessages": "677", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "678", "messages": "679", "suppressedMessages": "680", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "681", "messages": "682", "suppressedMessages": "683", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "684", "messages": "685", "suppressedMessages": "686", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "687", "messages": "688", "suppressedMessages": "689", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "690", "messages": "691", "suppressedMessages": "692", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "693", "messages": "694", "suppressedMessages": "695", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "696", "messages": "697", "suppressedMessages": "698", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "699", "messages": "700", "suppressedMessages": "701", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "702", "messages": "703", "suppressedMessages": "704", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "705", "messages": "706", "suppressedMessages": "707", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "708", "messages": "709", "suppressedMessages": "710", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "711", "messages": "712", "suppressedMessages": "713", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "714", "messages": "715", "suppressedMessages": "716", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "717", "messages": "718", "suppressedMessages": "719", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "720", "messages": "721", "suppressedMessages": "722", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "723", "messages": "724", "suppressedMessages": "725", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "726", "messages": "727", "suppressedMessages": "728", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "729", "messages": "730", "suppressedMessages": "731", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "732", "messages": "733", "suppressedMessages": "734", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "735", "messages": "736", "suppressedMessages": "737", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "738", "messages": "739", "suppressedMessages": "740", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "741", "messages": "742", "suppressedMessages": "743", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "744", "messages": "745", "suppressedMessages": "746", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "747", "messages": "748", "suppressedMessages": "749", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "750", "messages": "751", "suppressedMessages": "752", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "753", "messages": "754", "suppressedMessages": "755", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "756", "messages": "757", "suppressedMessages": "758", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "759", "messages": "760", "suppressedMessages": "761", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "762", "messages": "763", "suppressedMessages": "764", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "765", "messages": "766", "suppressedMessages": "767", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "768", "messages": "769", "suppressedMessages": "770", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "771", "messages": "772", "suppressedMessages": "773", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "774", "messages": "775", "suppressedMessages": "776", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "777", "messages": "778", "suppressedMessages": "779", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "780", "messages": "781", "suppressedMessages": "782", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "783", "messages": "784", "suppressedMessages": "785", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "786", "messages": "787", "suppressedMessages": "788", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "789", "messages": "790", "suppressedMessages": "791", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "792", "messages": "793", "suppressedMessages": "794", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "795", "messages": "796", "suppressedMessages": "797", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "798", "messages": "799", "suppressedMessages": "800", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "801", "messages": "802", "suppressedMessages": "803", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "804", "messages": "805", "suppressedMessages": "806", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "807", "messages": "808", "suppressedMessages": "809", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "810", "messages": "811", "suppressedMessages": "812", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "813", "messages": "814", "suppressedMessages": "815", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "816", "messages": "817", "suppressedMessages": "818", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "819", "messages": "820", "suppressedMessages": "821", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "822", "messages": "823", "suppressedMessages": "824", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "825", "messages": "826", "suppressedMessages": "827", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "828", "messages": "829", "suppressedMessages": "830", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "831", "messages": "832", "suppressedMessages": "833", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "834", "messages": "835", "suppressedMessages": "836", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "837", "messages": "838", "suppressedMessages": "839", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "840", "messages": "841", "suppressedMessages": "842", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "843", "messages": "844", "suppressedMessages": "845", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "846", "messages": "847", "suppressedMessages": "848", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "849", "messages": "850", "suppressedMessages": "851", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "852", "messages": "853", "suppressedMessages": "854", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "855", "messages": "856", "suppressedMessages": "857", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "858", "messages": "859", "suppressedMessages": "860", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "861", "messages": "862", "suppressedMessages": "863", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "864", "messages": "865", "suppressedMessages": "866", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "867", "messages": "868", "suppressedMessages": "869", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "870", "messages": "871", "suppressedMessages": "872", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "873", "messages": "874", "suppressedMessages": "875", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "876", "messages": "877", "suppressedMessages": "878", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "879", "messages": "880", "suppressedMessages": "881", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "882", "messages": "883", "suppressedMessages": "884", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "885", "messages": "886", "suppressedMessages": "887", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "888", "messages": "889", "suppressedMessages": "890", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "891", "messages": "892", "suppressedMessages": "893", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "894", "messages": "895", "suppressedMessages": "896", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "897", "messages": "898", "suppressedMessages": "899", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "900", "messages": "901", "suppressedMessages": "902", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "903", "messages": "904", "suppressedMessages": "905", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "906", "messages": "907", "suppressedMessages": "908", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "909", "messages": "910", "suppressedMessages": "911", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "912", "messages": "913", "suppressedMessages": "914", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "915", "messages": "916", "suppressedMessages": "917", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "918", "messages": "919", "suppressedMessages": "920", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "921", "messages": "922", "suppressedMessages": "923", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "924", "messages": "925", "suppressedMessages": "926", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "927", "messages": "928", "suppressedMessages": "929", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "930", "messages": "931", "suppressedMessages": "932", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "933", "messages": "934", "suppressedMessages": "935", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "936", "messages": "937", "suppressedMessages": "938", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "939", "messages": "940", "suppressedMessages": "941", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "942", "messages": "943", "suppressedMessages": "944", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "945", "messages": "946", "suppressedMessages": "947", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "948", "messages": "949", "suppressedMessages": "950", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "951", "messages": "952", "suppressedMessages": "953", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "954", "messages": "955", "suppressedMessages": "956", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "957", "messages": "958", "suppressedMessages": "959", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "960", "messages": "961", "suppressedMessages": "962", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "963", "messages": "964", "suppressedMessages": "965", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "966", "messages": "967", "suppressedMessages": "968", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "969", "messages": "970", "suppressedMessages": "971", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "972", "messages": "973", "suppressedMessages": "974", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "975", "messages": "976", "suppressedMessages": "977", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "978", "messages": "979", "suppressedMessages": "980", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "981", "messages": "982", "suppressedMessages": "983", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "984", "messages": "985", "suppressedMessages": "986", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "987", "messages": "988", "suppressedMessages": "989", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "990", "messages": "991", "suppressedMessages": "992", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "993", "messages": "994", "suppressedMessages": "995", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "996", "messages": "997", "suppressedMessages": "998", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "999", "messages": "1000", "suppressedMessages": "1001", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1002", "messages": "1003", "suppressedMessages": "1004", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1005", "messages": "1006", "suppressedMessages": "1007", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1008", "messages": "1009", "suppressedMessages": "1010", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1011", "messages": "1012", "suppressedMessages": "1013", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1014", "messages": "1015", "suppressedMessages": "1016", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1017", "messages": "1018", "suppressedMessages": "1019", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1020", "messages": "1021", "suppressedMessages": "1022", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1023", "messages": "1024", "suppressedMessages": "1025", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1026", "messages": "1027", "suppressedMessages": "1028", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1029", "messages": "1030", "suppressedMessages": "1031", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1032", "messages": "1033", "suppressedMessages": "1034", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1035", "messages": "1036", "suppressedMessages": "1037", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1038", "messages": "1039", "suppressedMessages": "1040", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1041", "messages": "1042", "suppressedMessages": "1043", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1044", "messages": "1045", "suppressedMessages": "1046", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1047", "messages": "1048", "suppressedMessages": "1049", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1050", "messages": "1051", "suppressedMessages": "1052", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1053", "messages": "1054", "suppressedMessages": "1055", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1056", "messages": "1057", "suppressedMessages": "1058", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1059", "messages": "1060", "suppressedMessages": "1061", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1062", "messages": "1063", "suppressedMessages": "1064", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1065", "messages": "1066", "suppressedMessages": "1067", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1068", "messages": "1069", "suppressedMessages": "1070", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1071", "messages": "1072", "suppressedMessages": "1073", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1074", "messages": "1075", "suppressedMessages": "1076", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1077", "messages": "1078", "suppressedMessages": "1079", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1080", "messages": "1081", "suppressedMessages": "1082", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1083", "messages": "1084", "suppressedMessages": "1085", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1086", "messages": "1087", "suppressedMessages": "1088", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1089", "messages": "1090", "suppressedMessages": "1091", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1092", "messages": "1093", "suppressedMessages": "1094", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1095", "messages": "1096", "suppressedMessages": "1097", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1098", "messages": "1099", "suppressedMessages": "1100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1101", "messages": "1102", "suppressedMessages": "1103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1104", "messages": "1105", "suppressedMessages": "1106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1107", "messages": "1108", "suppressedMessages": "1109", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1110", "messages": "1111", "suppressedMessages": "1112", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1113", "messages": "1114", "suppressedMessages": "1115", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1116", "messages": "1117", "suppressedMessages": "1118", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1119", "messages": "1120", "suppressedMessages": "1121", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1122", "messages": "1123", "suppressedMessages": "1124", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1125", "messages": "1126", "suppressedMessages": "1127", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1128", "messages": "1129", "suppressedMessages": "1130", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1131", "messages": "1132", "suppressedMessages": "1133", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1134", "messages": "1135", "suppressedMessages": "1136", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1137", "messages": "1138", "suppressedMessages": "1139", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1140", "messages": "1141", "suppressedMessages": "1142", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1143", "messages": "1144", "suppressedMessages": "1145", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1146", "messages": "1147", "suppressedMessages": "1148", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1149", "messages": "1150", "suppressedMessages": "1151", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1152", "messages": "1153", "suppressedMessages": "1154", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1155", "messages": "1156", "suppressedMessages": "1157", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1158", "messages": "1159", "suppressedMessages": "1160", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1161", "messages": "1162", "suppressedMessages": "1163", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1164", "messages": "1165", "suppressedMessages": "1166", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1167", "messages": "1168", "suppressedMessages": "1169", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1170", "messages": "1171", "suppressedMessages": "1172", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1173", "messages": "1174", "suppressedMessages": "1175", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1176", "messages": "1177", "suppressedMessages": "1178", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1179", "messages": "1180", "suppressedMessages": "1181", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1182", "messages": "1183", "suppressedMessages": "1184", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1185", "messages": "1186", "suppressedMessages": "1187", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1188", "messages": "1189", "suppressedMessages": "1190", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1191", "messages": "1192", "suppressedMessages": "1193", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1194", "messages": "1195", "suppressedMessages": "1196", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1197", "messages": "1198", "suppressedMessages": "1199", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1200", "messages": "1201", "suppressedMessages": "1202", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1203", "messages": "1204", "suppressedMessages": "1205", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1206", "messages": "1207", "suppressedMessages": "1208", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1209", "messages": "1210", "suppressedMessages": "1211", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1212", "messages": "1213", "suppressedMessages": "1214", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1215", "messages": "1216", "suppressedMessages": "1217", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1218", "messages": "1219", "suppressedMessages": "1220", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1221", "messages": "1222", "suppressedMessages": "1223", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1224", "messages": "1225", "suppressedMessages": "1226", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\APILayer\\axios_helper.ts", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\components\\AddNewGroupModal.tsx", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\components\\LoadingOptimizer.tsx", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\create-carrier\\page.tsx", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\create-plan\\components\\CreatePlanOptimizer.tsx", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\create-plan\\page.tsx", ["1227"], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\create-plan\\services\\planApi.ts", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\employee-enrol\\components\\AdditionalBenefitsPage.tsx", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\employee-enrol\\components\\BenefitsEnrollmentBot.tsx", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\employee-enrol\\components\\BotQuestion.tsx", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\employee-enrol\\components\\ConfirmationPage.tsx", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\employee-enrol\\components\\DentalPlanPage.tsx", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\employee-enrol\\components\\DependentsConfirmationPage.tsx", [], ["1228"], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\employee-enrol\\components\\MedicalPlanPage.tsx", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\employee-enrol\\components\\PersonalizationPage.tsx", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\employee-enrol\\components\\SummaryPage.tsx", ["1229"], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\employee-enrol\\components\\ui\\badge.tsx", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\employee-enrol\\components\\ui\\button.tsx", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\employee-enrol\\components\\ui\\card.tsx", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\employee-enrol\\components\\ui\\checkbox.tsx", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\employee-enrol\\components\\ui\\label.tsx", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\employee-enrol\\components\\ui\\radio-group.tsx", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\employee-enrol\\components\\VisionPlanPage.tsx", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\employee-enrol\\components\\WelcomePage.tsx", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\employee-enrol\\lib\\utils.ts", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\employee-enrol\\page.tsx", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\employee-enrol\\pages\\MedicalPlanPage.tsx", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\employee-enrol\\pages\\PersonalizationPage.tsx", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\employee-enrol\\pages\\WelcomePage.tsx", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\employee-enrol\\utils\\dateUtils.ts", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\hooks\\usePerformanceMonitor.ts", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\layout.tsx", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\manage-groups\\company\\[companyId]\\confirmation\\page.tsx", ["1230", "1231"], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\manage-groups\\company\\[companyId]\\enrollment-dates\\page.tsx", ["1232"], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\manage-groups\\company\\[companyId]\\plans\\components\\CreatePlanForm.tsx", ["1233"], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\manage-groups\\company\\[companyId]\\plans\\components\\PlanSelectionModal.tsx", ["1234"], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\manage-groups\\company\\[companyId]\\plans\\page.tsx", ["1235", "1236"], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\manage-groups\\company\\[companyId]\\plans\\[planId]\\confirmation\\page.tsx", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\manage-groups\\company\\[companyId]\\plans\\[planId]\\review\\page.tsx", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\manage-groups\\company\\[companyId]\\plans\\[planId]\\set-dates\\page.tsx", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\manage-groups\\company\\[companyId]\\review\\page.tsx", ["1237"], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\manage-groups\\company\\[companyId]\\set-dates\\page.tsx", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\manage-groups\\components\\CompanyCard.tsx", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\manage-groups\\page.tsx", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\manage-groups\\select-company\\page.tsx", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\manage-groups\\services\\planAssignmentApi.ts", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\manage-groups\\[companyId]\\assign-plans\\page.tsx", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\manage-groups\\[companyId]\\manage-plans\\add-plan\\page.tsx", ["1238"], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\manage-groups\\[companyId]\\manage-plans\\configure-plan\\page.tsx", ["1239"], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\manage-groups\\[companyId]\\manage-plans\\page.tsx", ["1240", "1241"], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\manage-groups\\[companyId]\\page.tsx", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\page.tsx", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\plans\\page.tsx", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\renewal\\page.tsx", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\renewal\\[groupId]\\document-upload\\page.tsx", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\renewal\\[groupId]\\export\\page.tsx", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\renewal\\[groupId]\\finalize\\page.tsx", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\renewal\\[groupId]\\page.tsx", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\renewal\\[groupId]\\plan-configuration\\page.tsx", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\renewal\\[groupId]\\renewal-options\\page.tsx", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\renewal\\[groupId]\\validation\\page.tsx", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\api\\proxy-pdf\\route.ts", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\benefits\\coming-soon\\page.tsx", [], ["1242"], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\consent_success\\page.tsx", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\dashboard\\AI_enroller.tsx", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\dashboard\\Career_assistant.tsx", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\dashboard\\CompanyDetails.tsx", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\dashboard\\edit_user_profile_popup.tsx", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\dashboard\\enhanced_edit_user_profile_popup.tsx", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\dashboard\\faq_help_section.tsx", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\dashboard\\infinite_scroll_section.tsx", ["1243", "1244"], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\dashboard\\page.tsx", ["1245"], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\dashboard\\ProfileUpdateCard.tsx", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\dashboard\\UserDetails.tsx", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\editBenefit\\[benefitId]\\page.tsx", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\group\\[groupId]\\page.tsx", ["1246", "1247"], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\hipaa\\page.tsx", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\layout.tsx", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\login\\page.tsx", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\manage-companies\\AddCompany.tsx", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\manage-companies\\page.tsx", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\manage-groups\\page.tsx", ["1248"], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\manageBenefits\\page.tsx", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\mobile\\dashboard\\mobile_sidebar.tsx", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\mobile\\dashboard\\page.tsx", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\mobile\\onboard\\page.tsx", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\mobile\\qHarmonyBot\\page.tsx", ["1249"], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\mobile\\viewBenefitDetails\\[benefitId]\\page.tsx", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\mobile\\viewBenefitsByType\\[benefitType]\\page.tsx", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\notification-history\\page.tsx", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\notifications-analytics\\[notificationId]\\page.tsx", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\onboard\\page.tsx", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\page.tsx", ["1250"], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\proactive-messaging\\page.tsx", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\proactive-messaging\\ProactiveMessaging.tsx", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\qHarmonyBot\\page.tsx", ["1251"], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\team-members\\AddTeamMemberDialogue.tsx", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\team-members\\page.tsx", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\teams-landing\\page.tsx", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\teamsauth\\authconfig.ts", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\view-all-users\\page.tsx", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\viewBenefitDetails\\[benefitId]\\page.tsx", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\viewBenefitsByType\\[benefitType]\\page.tsx", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\wellness\\page.tsx", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\wellness\\results\\page.tsx", ["1252", "1253", "1254"], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\components\\AuthContext.tsx", ["1255"], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\components\\benefit_vector_map.tsx", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\components\\Footer.tsx", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\components\\mobile_edge_fill.tsx", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\components\\Navbar.tsx", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\components\\ProtectedRoute.tsx", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\components\\RightPanelOnlyComponent.tsx", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\components\\ShareButtons.tsx", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\components\\sidebar.tsx", ["1256"], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\components\\topbar.tsx", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\components\\withSidebar.tsx", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\middleware\\benefits_middleware.ts", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\middleware\\chatbot_middleware.ts", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\middleware\\company_middleware.ts", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\middleware\\group_middleware.tsx", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\middleware\\user_middleware.ts", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\models\\admin.ts", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\models\\company.ts", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\redux\\hooks.ts", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\redux\\reducers\\benefitsSlice.ts", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\redux\\reducers\\companySlice.ts", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\redux\\reducers\\mobileSidebarSlice.ts", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\redux\\reducers\\onboardingSlice.ts", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\redux\\reducers\\qHarmonyBotSlice.ts", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\redux\\reducers\\userSlice.ts", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\redux\\slices\\companySlice.ts", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\redux\\store.ts", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\redux\\StoreProvider.tsx", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\services\\wellness.service.ts", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\theme.js", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\utils\\env.ts", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\utils\\firebase.js", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\employee-enrol\\components\\ChatMessage.tsx", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\employee-enrol\\components\\CompareModal.tsx", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\employee-enrol\\components\\FloatingHelp.tsx", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\employee-enrol\\components\\LifeInsurancePlanPage.tsx", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\employee-enrol\\components\\VideoPlayer.tsx", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\employee-enrol\\components\\EnrollmentProfileModal.tsx", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\employee-enrol\\services\\enrollmentService.ts", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\employee-enrol\\components\\ChatModal.tsx", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\employee-enrol\\components\\EnrollmentHeader.tsx", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\employee-enrol\\components\\FloatingChatButton.tsx", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\employee-enrol\\components\\AskQuestionsButton.tsx", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\employee-enrol\\components\\CustomModal.tsx", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\employee-enrol\\services\\bulkWaiveApi.ts", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\employee-enrol\\components\\ADDPlanPage.tsx", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\employee-enrol\\components\\SignatureModal.tsx", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\employee-enrol\\components\\SignatureViewer.tsx", ["1257"], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\employee-enrol\\services\\bulkEnrollmentApi.ts", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\employee-enrol\\services\\signatureApi.ts", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\employee-enrol\\utils\\signatureUtils.ts", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\employee-enrol\\signature-api-test\\page.tsx", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\employee-enrol\\signature-test\\page.tsx", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\employee-enrol\\components\\DynamicPlanPage.tsx", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\employee-enrol\\utils\\planCategoryUtils.ts", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\manage-groups\\company\\[companyId]\\plans\\components\\NotificationModal.tsx", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\manage-groups\\company\\[companyId]\\plans\\hooks\\useNotification.tsx", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\employee-enrol\\components\\CoverageTierSelector.tsx", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\employee-enrol\\components\\DynamicSummarySection.tsx", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\employee-enrol\\utils\\planStorageUtils.ts", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\census\\components\\AskBrea.tsx", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\census\\components\\EmpCensusApp.tsx", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\census\\components\\EmployeeDetailModal.tsx", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\census\\components\\NavigationDropdown.tsx", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\census\\components\\NavigationProvider.tsx", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\census\\components\\ProfileSettingsModal.tsx", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\census\\components\\ui\\accordion.tsx", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\census\\components\\ui\\alert-dialog.tsx", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\census\\components\\ui\\alert.tsx", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\census\\components\\ui\\aspect-ratio.tsx", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\census\\components\\ui\\avatar.tsx", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\census\\components\\ui\\badge.tsx", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\census\\components\\ui\\breadcrumb.tsx", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\census\\components\\ui\\button.tsx", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\census\\components\\ui\\calendar.tsx", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\census\\components\\ui\\card.tsx", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\census\\components\\ui\\carousel.tsx", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\census\\components\\ui\\chart.tsx", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\census\\components\\ui\\checkbox.tsx", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\census\\components\\ui\\collapsible.tsx", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\census\\components\\ui\\command.tsx", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\census\\components\\ui\\context-menu.tsx", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\census\\components\\ui\\dialog.tsx", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\census\\components\\ui\\drawer.tsx", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\census\\components\\ui\\dropdown-menu.tsx", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\census\\components\\ui\\form.tsx", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\census\\components\\ui\\hover-card.tsx", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\census\\components\\ui\\input-otp.tsx", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\census\\components\\ui\\input.tsx", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\census\\components\\ui\\label.tsx", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\census\\components\\ui\\menubar.tsx", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\census\\components\\ui\\navigation-menu.tsx", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\census\\components\\ui\\pagination.tsx", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\census\\components\\ui\\popover.tsx", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\census\\components\\ui\\progress.tsx", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\census\\components\\ui\\radio-group.tsx", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\census\\components\\ui\\resizable.tsx", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\census\\components\\ui\\scroll-area.tsx", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\census\\components\\ui\\select.tsx", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\census\\components\\ui\\separator.tsx", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\census\\components\\ui\\sheet.tsx", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\census\\components\\ui\\sidebar.tsx", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\census\\components\\ui\\skeleton.tsx", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\census\\components\\ui\\slider.tsx", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\census\\components\\ui\\sonner.tsx", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\census\\components\\ui\\switch.tsx", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\census\\components\\ui\\table.tsx", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\census\\components\\ui\\tabs.tsx", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\census\\components\\ui\\textarea.tsx", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\census\\components\\ui\\toast.tsx", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\census\\components\\ui\\toaster.tsx", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\census\\components\\ui\\toggle-group.tsx", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\census\\components\\ui\\toggle.tsx", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\census\\components\\ui\\tooltip.tsx", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\census\\components\\ui\\use-toast.ts", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\census\\components\\UpgradePrompt.tsx", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\census\\hooks\\use-mobile.tsx", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\census\\hooks\\use-toast.ts", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\census\\hooks\\usePlanRestrictions.tsx", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\census\\lib\\react-router-dom.tsx", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\census\\lib\\utils.ts", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\census\\nav-items.tsx", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\census\\page.tsx", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\census\\public\\Billing.tsx", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\census\\public\\BrokerDashboard.tsx", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\census\\public\\EmployerInsight.tsx", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\census\\public\\EmployerInvite.tsx", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\census\\public\\GenerateProposal.tsx", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\census\\public\\HRInsight.tsx", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\census\\public\\HRUpload.tsx", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\census\\public\\Index.tsx", ["1258", "1259"], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\census\\public\\LoginPrompt.tsx", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\census\\public\\NotFound.tsx", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\census\\public\\PreviewReport.tsx", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\census\\public\\Pricing.tsx", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\census\\public\\Processing.tsx", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\census\\public\\UploadCensus.tsx", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\dashboard\\CensusCard.tsx", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\census\\components\\ProfileHandler.tsx", [], [], "C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\census\\public\\FilePreview.tsx", [], [], {"ruleId": "1260", "severity": 1, "message": "1261", "line": 293, "column": 6, "nodeType": "1262", "endLine": 293, "endColumn": 29, "suggestions": "1263"}, {"ruleId": "1260", "severity": 1, "message": "1264", "line": 167, "column": 6, "nodeType": "1262", "endLine": 167, "endColumn": 8, "suggestions": "1265", "suppressions": "1266"}, {"ruleId": "1260", "severity": 1, "message": "1267", "line": 914, "column": 6, "nodeType": "1262", "endLine": 914, "endColumn": 8, "suggestions": "1268"}, {"ruleId": "1260", "severity": 1, "message": "1269", "line": 157, "column": 6, "nodeType": "1262", "endLine": 157, "endColumn": 42, "suggestions": "1270"}, {"ruleId": "1260", "severity": 1, "message": "1271", "line": 157, "column": 18, "nodeType": "1272", "endLine": 157, "endColumn": 41}, {"ruleId": "1260", "severity": 1, "message": "1273", "line": 40, "column": 9, "nodeType": "1274", "endLine": 113, "endColumn": 4}, {"ruleId": "1260", "severity": 1, "message": "1275", "line": 205, "column": 6, "nodeType": "1262", "endLine": 205, "endColumn": 8, "suggestions": "1276"}, {"ruleId": "1260", "severity": 1, "message": "1277", "line": 109, "column": 6, "nodeType": "1262", "endLine": 109, "endColumn": 14, "suggestions": "1278"}, {"ruleId": "1260", "severity": 1, "message": "1279", "line": 360, "column": 6, "nodeType": "1262", "endLine": 360, "endColumn": 13, "suggestions": "1280"}, {"ruleId": "1260", "severity": 1, "message": "1281", "line": 368, "column": 6, "nodeType": "1262", "endLine": 368, "endColumn": 51, "suggestions": "1282"}, {"ruleId": "1260", "severity": 1, "message": "1283", "line": 285, "column": 6, "nodeType": "1262", "endLine": 285, "endColumn": 17, "suggestions": "1284"}, {"ruleId": "1260", "severity": 1, "message": "1285", "line": 109, "column": 6, "nodeType": "1262", "endLine": 109, "endColumn": 16, "suggestions": "1286"}, {"ruleId": "1260", "severity": 1, "message": "1287", "line": 78, "column": 9, "nodeType": "1274", "endLine": 110, "endColumn": 4}, {"ruleId": "1260", "severity": 1, "message": "1288", "line": 112, "column": 6, "nodeType": "1262", "endLine": 112, "endColumn": 17, "suggestions": "1289"}, {"ruleId": "1260", "severity": 1, "message": "1288", "line": 122, "column": 6, "nodeType": "1262", "endLine": 122, "endColumn": 17, "suggestions": "1290"}, {"ruleId": "1291", "severity": 2, "message": "1292", "line": 418, "column": 43, "nodeType": "1293", "messageId": "1294", "suggestions": "1295", "suppressions": "1296"}, {"ruleId": "1260", "severity": 1, "message": "1297", "line": 54, "column": 40, "nodeType": "1298", "endLine": 54, "endColumn": 47}, {"ruleId": "1260", "severity": 1, "message": "1299", "line": 57, "column": 6, "nodeType": "1262", "endLine": 57, "endColumn": 8, "suggestions": "1300"}, {"ruleId": "1260", "severity": 1, "message": "1301", "line": 54, "column": 6, "nodeType": "1262", "endLine": 54, "endColumn": 19, "suggestions": "1302"}, {"ruleId": "1260", "severity": 1, "message": "1303", "line": 91, "column": 6, "nodeType": "1262", "endLine": 91, "endColumn": 8, "suggestions": "1304"}, {"ruleId": "1260", "severity": 1, "message": "1305", "line": 99, "column": 6, "nodeType": "1262", "endLine": 99, "endColumn": 16, "suggestions": "1306"}, {"ruleId": "1260", "severity": 1, "message": "1307", "line": 45, "column": 6, "nodeType": "1262", "endLine": 45, "endColumn": 8, "suggestions": "1308"}, {"ruleId": "1260", "severity": 1, "message": "1309", "line": 94, "column": 6, "nodeType": "1262", "endLine": 94, "endColumn": 37, "suggestions": "1310"}, {"ruleId": "1260", "severity": 1, "message": "1301", "line": 31, "column": 6, "nodeType": "1262", "endLine": 31, "endColumn": 12, "suggestions": "1311"}, {"ruleId": "1260", "severity": 1, "message": "1309", "line": 89, "column": 6, "nodeType": "1262", "endLine": 89, "endColumn": 37, "suggestions": "1312"}, {"ruleId": "1313", "severity": 1, "message": "1314", "line": 132, "column": 11, "nodeType": "1315", "endLine": 132, "endColumn": 84}, {"ruleId": "1313", "severity": 1, "message": "1314", "line": 133, "column": 11, "nodeType": "1315", "endLine": 133, "endColumn": 86}, {"ruleId": "1313", "severity": 1, "message": "1314", "line": 134, "column": 11, "nodeType": "1315", "endLine": 134, "endColumn": 84}, {"ruleId": "1260", "severity": 1, "message": "1301", "line": 136, "column": 6, "nodeType": "1262", "endLine": 136, "endColumn": 8, "suggestions": "1316"}, {"ruleId": "1313", "severity": 1, "message": "1314", "line": 126, "column": 15, "nodeType": "1315", "endLine": 130, "endColumn": 17}, {"ruleId": "1313", "severity": 1, "message": "1314", "line": 214, "column": 15, "nodeType": "1315", "endLine": 223, "endColumn": 17}, {"ruleId": "1313", "severity": 1, "message": "1314", "line": 69, "column": 13, "nodeType": "1315", "endLine": 69, "endColumn": 84}, {"ruleId": "1313", "severity": 1, "message": "1314", "line": 453, "column": 21, "nodeType": "1315", "endLine": 453, "endColumn": 215}, "react-hooks/exhaustive-deps", "React Hook useEffect has missing dependencies: 'formData.coverageCategory' and 'formData.planName'. Either include them or remove the dependency array.", "ArrayExpression", ["1317"], "React Hook useEffect has a missing dependency: 'fetchUserDetails'. Either include it or remove the dependency array.", ["1318"], ["1319"], "React Hook useEffect has a missing dependency: 'fetchPlanAssignmentDetails'. Either include it or remove the dependency array.", ["1320"], "React Hook useEffect has a missing dependency: 'assignmentIds'. Either include it or remove the dependency array.", ["1321"], "React Hook useEffect has a complex expression in the dependency array. Extract it to a separate variable so it can be statically checked.", "CallExpression", "The 'fetchCompanyAndPlans' function makes the dependencies of useEffect Hook (at line 117) change on every render. Move it inside the useEffect callback. Alternatively, wrap the definition of 'fetchCompanyAndPlans' in its own useCallback() Hook.", "VariableDeclarator", "React Hook useEffect has a missing dependency: 'existingCarriers'. Either include it or remove the dependency array.", ["1322"], "React Hook useEffect has a missing dependency: 'fetchPlans'. Either include it or remove the dependency array.", ["1323"], "React Hook useEffect has a missing dependency: 'fetchAllPlanAssignmentDetails'. Either include it or remove the dependency array.", ["1324"], "React Hook useEffect has a missing dependency: 'calculateEstimatedMonthlyCost'. Either include it or remove the dependency array.", ["1325"], "React Hook useCallback has a missing dependency: 'selectedAssignmentIds'. Either include it or remove the dependency array.", ["1326"], "React Hook useEffect has a missing dependency: 'fetchAvailablePlans'. Either include it or remove the dependency array.", ["1327"], "The 'fetchPlanDetails' function makes the dependencies of useEffect Hook (at line 116) change on every render. Move it inside the useEffect callback. Alternatively, wrap the definition of 'fetchPlanDetails' in its own useCallback() Hook.", "React Hook useEffect has a missing dependency: 'fetchPlanAssignments'. Either include it or remove the dependency array.", ["1328"], ["1329"], "react/no-unescaped-entities", "`'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.", "JSXText", "unescapedEntityAlts", ["1330", "1331", "1332", "1333"], ["1334"], "The ref value 'observerRef.current' will likely have changed by the time this effect cleanup function runs. If this ref points to a node rendered by React, copy 'observerRef.current' to a variable inside the effect, and use that variable in the cleanup function.", "Identifier", "React Hook useEffect has a missing dependency: 'handleLoadMore'. Either include it or remove the dependency array.", ["1335"], "React Hook useEffect has a missing dependency: 'router'. Either include it or remove the dependency array.", ["1336"], "React Hook useEffect has missing dependencies: 'fetchGroupDetails' and 'fetchTeamMembers'. Either include them or remove the dependency array.", ["1337"], "React Hook useEffect has missing dependencies: 'fetchBenefitsData' and 'fetchTeamMembers'. Either include them or remove the dependency array.", ["1338"], "React Hook useEffect has a missing dependency: 'companyId'. Either include it or remove the dependency array.", ["1339"], "React Hook useEffect has a missing dependency: 'handleSendMessage'. Either include it or remove the dependency array.", ["1340"], ["1341"], ["1342"], "@next/next/no-img-element", "Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element", "JSXOpeningElement", ["1343"], {"desc": "1344", "fix": "1345"}, {"desc": "1346", "fix": "1347"}, {"kind": "1348", "justification": "1349"}, {"desc": "1350", "fix": "1351"}, {"desc": "1352", "fix": "1353"}, {"desc": "1354", "fix": "1355"}, {"desc": "1356", "fix": "1357"}, {"desc": "1358", "fix": "1359"}, {"desc": "1360", "fix": "1361"}, {"desc": "1362", "fix": "1363"}, {"desc": "1364", "fix": "1365"}, {"desc": "1366", "fix": "1367"}, {"desc": "1366", "fix": "1368"}, {"messageId": "1369", "data": "1370", "fix": "1371", "desc": "1372"}, {"messageId": "1369", "data": "1373", "fix": "1374", "desc": "1375"}, {"messageId": "1369", "data": "1376", "fix": "1377", "desc": "1378"}, {"messageId": "1369", "data": "1379", "fix": "1380", "desc": "1381"}, {"kind": "1348", "justification": "1349"}, {"desc": "1382", "fix": "1383"}, {"desc": "1384", "fix": "1385"}, {"desc": "1386", "fix": "1387"}, {"desc": "1388", "fix": "1389"}, {"desc": "1390", "fix": "1391"}, {"desc": "1392", "fix": "1393"}, {"desc": "1394", "fix": "1395"}, {"desc": "1392", "fix": "1396"}, {"desc": "1397", "fix": "1398"}, "Update the dependencies array to be: [router, constantsData, formData.planName, formData.coverageCategory]", {"range": "1399", "text": "1400"}, "Update the dependencies array to be: [fetchUserDetails]", {"range": "1401", "text": "1402"}, "directive", "", "Update the dependencies array to be: [fetchPlanAssignmentDetails]", {"range": "1403", "text": "1404"}, "Update the dependencies array to be: [assignmentIds, companyId]", {"range": "1405", "text": "1406"}, "Update the dependencies array to be: [existingCarriers]", {"range": "1407", "text": "1408"}, "Update the dependencies array to be: [fetchPlans, isOpen]", {"range": "1409", "text": "1410"}, "Update the dependencies array to be: [fetchAllPlanAssignmentDetails, plans]", {"range": "1411", "text": "1412"}, "Update the dependencies array to be: [planAssignmentDetails, companyEmployeeCount, calculateEstimatedMonthlyCost]", {"range": "1413", "text": "1414"}, "Update the dependencies array to be: [companyId, selectedAssignmentIds]", {"range": "1415", "text": "1416"}, "Update the dependencies array to be: [category, fetchAvailablePlans]", {"range": "1417", "text": "1418"}, "Update the dependencies array to be: [companyId, fetchPlanAssignments]", {"range": "1419", "text": "1420"}, {"range": "1421", "text": "1420"}, "replaceWithAlt", {"alt": "1422"}, {"range": "1423", "text": "1424"}, "Replace with `&apos;`.", {"alt": "1425"}, {"range": "1426", "text": "1427"}, "Replace with `&lsquo;`.", {"alt": "1428"}, {"range": "1429", "text": "1430"}, "Replace with `&#39;`.", {"alt": "1431"}, {"range": "1432", "text": "1433"}, "Replace with `&rsquo;`.", "Update the dependencies array to be: [handleLoadMore]", {"range": "1434", "text": "1435"}, "Update the dependencies array to be: [router, userDetails]", {"range": "1436", "text": "1437"}, "Update the dependencies array to be: [fetchGroupDetails, fetchTeamMembers]", {"range": "1438", "text": "1439"}, "Update the dependencies array to be: [fetchBenefitsData, fetchTeamMembers, tabIndex]", {"range": "1440", "text": "1441"}, "Update the dependencies array to be: [companyId]", {"range": "1442", "text": "1443"}, "Update the dependencies array to be: [selectedFAQQuestion, dispatch, handleSendMessage]", {"range": "1444", "text": "1445"}, "Update the dependencies array to be: [router, user]", {"range": "1446", "text": "1447"}, {"range": "1448", "text": "1445"}, "Update the dependencies array to be: [router]", {"range": "1449", "text": "1450"}, [8771, 8794], "[router, constantsData, formData.planName, formData.coverageCategory]", [5173, 5175], "[fetchUserDetails]", [36363, 36365], "[fetchPlanAssignmentDetails]", [5546, 5582], "[assignmentIds, companyId]", [6243, 6245], "[existingCarriers]", [4212, 4220], "[fetchPlans, isOpen]", [14816, 14823], "[fetchAllPlanAssignmentDetails, plans]", [15152, 15197], "[planAssignmentDetails, companyEmployeeCount, calculateEstimatedMonthlyCost]", [11015, 11026], "[companyId, selectedAssignmentIds]", [3381, 3391], "[category, fetchAvailablePlans]", [3545, 3556], "[companyId, fetchPlanAssignments]", [3840, 3851], "&apos;", [15679, 15728], "You&apos;re losing up to $11,000 annually on benefits.", "&lsquo;", [15679, 15728], "You&lsquo;re losing up to $11,000 annually on benefits.", "&#39;", [15679, 15728], "You&#39;re losing up to $11,000 annually on benefits.", "&rsquo;", [15679, 15728], "You&rsquo;re losing up to $11,000 annually on benefits.", [1687, 1689], "[handleLoadMore]", [1710, 1723], "[router, userDetails]", [2484, 2486], "[fetchGroupDetails, fetchTeamMembers]", [2641, 2651], "[fetchBenefitsData, fetchTeamMembers, tabIndex]", [1276, 1278], "[companyId]", [2960, 2991], "[selectedFAQQuestion, dispatch, handleSendMessage]", [916, 922], "[router, user]", [2728, 2759], [4599, 4601], "[router]"]