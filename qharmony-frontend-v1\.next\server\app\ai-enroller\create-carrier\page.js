(()=>{var e={};e.id=9417,e.ids=[9417],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},27790:e=>{"use strict";e.exports=require("assert")},84770:e=>{"use strict";e.exports=require("crypto")},17702:e=>{"use strict";e.exports=require("events")},92048:e=>{"use strict";e.exports=require("fs")},32615:e=>{"use strict";e.exports=require("http")},35240:e=>{"use strict";e.exports=require("https")},19801:e=>{"use strict";e.exports=require("os")},55315:e=>{"use strict";e.exports=require("path")},76162:e=>{"use strict";e.exports=require("stream")},74026:e=>{"use strict";e.exports=require("string_decoder")},74175:e=>{"use strict";e.exports=require("tty")},17360:e=>{"use strict";e.exports=require("url")},21764:e=>{"use strict";e.exports=require("util")},71568:e=>{"use strict";e.exports=require("zlib")},98061:e=>{"use strict";e.exports=require("node:assert")},92761:e=>{"use strict";e.exports=require("node:async_hooks")},72254:e=>{"use strict";e.exports=require("node:buffer")},40027:e=>{"use strict";e.exports=require("node:console")},6005:e=>{"use strict";e.exports=require("node:crypto")},65714:e=>{"use strict";e.exports=require("node:diagnostics_channel")},15673:e=>{"use strict";e.exports=require("node:events")},88849:e=>{"use strict";e.exports=require("node:http")},42725:e=>{"use strict";e.exports=require("node:http2")},87503:e=>{"use strict";e.exports=require("node:net")},38846:e=>{"use strict";e.exports=require("node:perf_hooks")},39630:e=>{"use strict";e.exports=require("node:querystring")},84492:e=>{"use strict";e.exports=require("node:stream")},31764:e=>{"use strict";e.exports=require("node:tls")},41041:e=>{"use strict";e.exports=require("node:url")},47261:e=>{"use strict";e.exports=require("node:util")},93746:e=>{"use strict";e.exports=require("node:util/types")},24086:e=>{"use strict";e.exports=require("node:worker_threads")},65628:e=>{"use strict";e.exports=require("node:zlib")},70218:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>i.a,__next_app__:()=>m,originalPathname:()=>u,pages:()=>d,routeModule:()=>p,tree:()=>l}),t(80853),t(6079),t(33709),t(35866);var a=t(23191),s=t(88716),n=t(37922),i=t.n(n),c=t(95231),o={};for(let e in c)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>c[e]);t.d(r,o);let l=["",{children:["ai-enroller",{children:["create-carrier",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,80853)),"C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\create-carrier\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,6079)),"C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,33709)),"C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,35866,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],d=["C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\create-carrier\\page.tsx"],u="/ai-enroller/create-carrier/page",m={require:t,loadChunk:()=>Promise.resolve()},p=new a.AppPageRouteModule({definition:{kind:s.x.APP_PAGE,page:"/ai-enroller/create-carrier/page",pathname:"/ai-enroller/create-carrier",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},18902:(e,r,t)=>{Promise.resolve().then(t.bind(t,81651))},40853:(e,r,t)=>{Promise.resolve().then(t.bind(t,78021))},6283:(e,r,t)=>{"use strict";t.d(r,{Z:()=>l});var a=t(96830),s=t(5028),n=t(5283),i=t(14750);let c=(0,t(71685).Z)("MuiBox",["root"]),o=(0,n.Z)(),l=(0,a.default)({themeId:i.Z,defaultTheme:o,defaultClassName:c.root,generateClassName:s.Z.generate})},98139:(e,r,t)=>{"use strict";t.d(r,{Z:()=>C});var a=t(17577),s=t(41135),n=t(88634),i=t(8106),c=t(91703),o=t(13643),l=t(2791),d=t(54641),u=t(40955),m=t(71685),p=t(97898);function x(e){return(0,p.ZP)("MuiCircularProgress",e)}(0,m.Z)("MuiCircularProgress",["root","determinate","indeterminate","colorPrimary","colorSecondary","svg","circle","circleDeterminate","circleIndeterminate","circleDisableShrink"]);var f=t(10326);let h=(0,i.F4)`
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
`,g=(0,i.F4)`
  0% {
    stroke-dasharray: 1px, 200px;
    stroke-dashoffset: 0;
  }

  50% {
    stroke-dasharray: 100px, 200px;
    stroke-dashoffset: -15px;
  }

  100% {
    stroke-dasharray: 1px, 200px;
    stroke-dashoffset: -126px;
  }
`,y="string"!=typeof h?(0,i.iv)`
        animation: ${h} 1.4s linear infinite;
      `:null,b="string"!=typeof g?(0,i.iv)`
        animation: ${g} 1.4s ease-in-out infinite;
      `:null,j=e=>{let{classes:r,variant:t,color:a,disableShrink:s}=e,i={root:["root",t,`color${(0,d.Z)(a)}`],svg:["svg"],circle:["circle",`circle${(0,d.Z)(t)}`,s&&"circleDisableShrink"]};return(0,n.Z)(i,x,r)},v=(0,c.default)("span",{name:"MuiCircularProgress",slot:"Root",overridesResolver:(e,r)=>{let{ownerState:t}=e;return[r.root,r[t.variant],r[`color${(0,d.Z)(t.color)}`]]}})((0,o.Z)(({theme:e})=>({display:"inline-block",variants:[{props:{variant:"determinate"},style:{transition:e.transitions.create("transform")}},{props:{variant:"indeterminate"},style:y||{animation:`${h} 1.4s linear infinite`}},...Object.entries(e.palette).filter((0,u.Z)()).map(([r])=>({props:{color:r},style:{color:(e.vars||e).palette[r].main}}))]}))),N=(0,c.default)("svg",{name:"MuiCircularProgress",slot:"Svg",overridesResolver:(e,r)=>r.svg})({display:"block"}),S=(0,c.default)("circle",{name:"MuiCircularProgress",slot:"Circle",overridesResolver:(e,r)=>{let{ownerState:t}=e;return[r.circle,r[`circle${(0,d.Z)(t.variant)}`],t.disableShrink&&r.circleDisableShrink]}})((0,o.Z)(({theme:e})=>({stroke:"currentColor",variants:[{props:{variant:"determinate"},style:{transition:e.transitions.create("stroke-dashoffset")}},{props:{variant:"indeterminate"},style:{strokeDasharray:"80px, 200px",strokeDashoffset:0}},{props:({ownerState:e})=>"indeterminate"===e.variant&&!e.disableShrink,style:b||{animation:`${g} 1.4s ease-in-out infinite`}}]}))),C=a.forwardRef(function(e,r){let t=(0,l.i)({props:e,name:"MuiCircularProgress"}),{className:a,color:n="primary",disableShrink:i=!1,size:c=40,style:o,thickness:d=3.6,value:u=0,variant:m="indeterminate",...p}=t,x={...t,color:n,disableShrink:i,size:c,thickness:d,value:u,variant:m},h=j(x),g={},y={},b={};if("determinate"===m){let e=2*Math.PI*((44-d)/2);g.strokeDasharray=e.toFixed(3),b["aria-valuenow"]=Math.round(u),g.strokeDashoffset=`${((100-u)/100*e).toFixed(3)}px`,y.transform="rotate(-90deg)"}return(0,f.jsx)(v,{className:(0,s.Z)(h.root,a),style:{width:c,height:c,...y,...o},ownerState:x,ref:r,role:"progressbar",...b,...p,children:(0,f.jsx)(N,{className:h.svg,ownerState:x,viewBox:"22 22 44 44",children:(0,f.jsx)(S,{className:h.circle,style:g,ownerState:x,cx:44,cy:44,r:(44-d)/2,fill:"none",strokeWidth:d})})})})},2791:(e,r,t)=>{"use strict";t.d(r,{i:()=>s}),t(17577);var a=t(51387);function s(e){return(0,a.i)(e)}t(10326)},54641:(e,r,t)=>{"use strict";t.d(r,{Z:()=>a});let a=t(96005).Z},40955:(e,r,t)=>{"use strict";function a(e=[]){return([,r])=>r&&function(e,r=[]){if("string"!=typeof e.main)return!1;for(let t of r)if(!e.hasOwnProperty(t)||"string"!=typeof e[t])return!1;return!0}(r,e)}t.d(r,{Z:()=>a})},13643:(e,r,t)=>{"use strict";t.d(r,{Z:()=>n});var a=t(15966);let s={theme:void 0},n=function(e){let r,t;return function(n){let i=r;return(void 0===i||n.theme!==t)&&(s.theme=n.theme,r=i=(0,a.Z)(e(s)),t=n.theme),i}}},8472:()=>{},51596:(e,r,t)=>{"use strict";t(8472);var a=t(17577),s=function(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}(a),n="undefined"!=typeof process&&process.env&&!0,i=function(e){return"[object String]"===Object.prototype.toString.call(e)},c=function(){function e(e){var r=void 0===e?{}:e,t=r.name,a=void 0===t?"stylesheet":t,s=r.optimizeForSpeed,c=void 0===s?n:s;o(i(a),"`name` must be a string"),this._name=a,this._deletedRulePlaceholder="#"+a+"-deleted-rule____{}",o("boolean"==typeof c,"`optimizeForSpeed` must be a boolean"),this._optimizeForSpeed=c,this._serverSheet=void 0,this._tags=[],this._injected=!1,this._rulesCount=0,this._nonce=null}var r=e.prototype;return r.setOptimizeForSpeed=function(e){o("boolean"==typeof e,"`setOptimizeForSpeed` accepts a boolean"),o(0===this._rulesCount,"optimizeForSpeed cannot be when rules have already been inserted"),this.flush(),this._optimizeForSpeed=e,this.inject()},r.isOptimizeForSpeed=function(){return this._optimizeForSpeed},r.inject=function(){var e=this;o(!this._injected,"sheet already injected"),this._injected=!0,this._serverSheet={cssRules:[],insertRule:function(r,t){return"number"==typeof t?e._serverSheet.cssRules[t]={cssText:r}:e._serverSheet.cssRules.push({cssText:r}),t},deleteRule:function(r){e._serverSheet.cssRules[r]=null}}},r.getSheetForTag=function(e){if(e.sheet)return e.sheet;for(var r=0;r<document.styleSheets.length;r++)if(document.styleSheets[r].ownerNode===e)return document.styleSheets[r]},r.getSheet=function(){return this.getSheetForTag(this._tags[this._tags.length-1])},r.insertRule=function(e,r){return o(i(e),"`insertRule` accepts only strings"),"number"!=typeof r&&(r=this._serverSheet.cssRules.length),this._serverSheet.insertRule(e,r),this._rulesCount++},r.replaceRule=function(e,r){this._optimizeForSpeed;var t=this._serverSheet;if(r.trim()||(r=this._deletedRulePlaceholder),!t.cssRules[e])return e;t.deleteRule(e);try{t.insertRule(r,e)}catch(a){n||console.warn("StyleSheet: illegal rule: \n\n"+r+"\n\nSee https://stackoverflow.com/q/20007992 for more info"),t.insertRule(this._deletedRulePlaceholder,e)}return e},r.deleteRule=function(e){this._serverSheet.deleteRule(e)},r.flush=function(){this._injected=!1,this._rulesCount=0,this._serverSheet.cssRules=[]},r.cssRules=function(){return this._serverSheet.cssRules},r.makeStyleTag=function(e,r,t){r&&o(i(r),"makeStyleTag accepts only strings as second parameter");var a=document.createElement("style");this._nonce&&a.setAttribute("nonce",this._nonce),a.type="text/css",a.setAttribute("data-"+e,""),r&&a.appendChild(document.createTextNode(r));var s=document.head||document.getElementsByTagName("head")[0];return t?s.insertBefore(a,t):s.appendChild(a),a},function(e,r){for(var t=0;t<r.length;t++){var a=r[t];a.enumerable=a.enumerable||!1,a.configurable=!0,"value"in a&&(a.writable=!0),Object.defineProperty(e,a.key,a)}}(e.prototype,[{key:"length",get:function(){return this._rulesCount}}]),e}();function o(e,r){if(!e)throw Error("StyleSheet: "+r+".")}var l=function(e){for(var r=5381,t=e.length;t;)r=33*r^e.charCodeAt(--t);return r>>>0},d={};function u(e,r){if(!r)return"jsx-"+e;var t=String(r),a=e+t;return d[a]||(d[a]="jsx-"+l(e+"-"+t)),d[a]}function m(e,r){var t=e+(r=r.replace(/\/style/gi,"\\/style"));return d[t]||(d[t]=r.replace(/__jsx-style-dynamic-selector/g,e)),d[t]}var p=a.createContext(null);p.displayName="StyleSheetContext",s.default.useInsertionEffect||s.default.useLayoutEffect;var x=void 0;function f(e){var r=x||a.useContext(p);return r&&r.add(e),null}f.dynamic=function(e){return e.map(function(e){return u(e[0],e[1])}).join(" ")},r.style=f},77626:(e,r,t)=>{"use strict";e.exports=t(51596).style},81651:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>f});var a=t(10326),s=t(77626),n=t.n(s),i=t(17577),c=t(35047),o=t(38492);t(23824);let l=["PPO","HMO","HDHP","MEC","EPO","POS","Indemnity","Term Life","Whole Life","STD","LTD"],d=["Health Insurance","Ancillary Benefits","Life & Disability Insurance","Voluntary Benefits","Wellness & Mental Health","Spending & Savings Accounts","Financial Benefits","Retirement Benefits","Time Off & Leave","Family & Caregiver Support","Career & Development","Workplace Environment","Life Events"],u=["Medical","Dental","Vision","Term Life","Supplemental Life Insurance","Short-Term Disability","Long-Term Disability","Whole Life","Group (Employer) Life","Hospital Indemnity","Accident Insurance","Critical Illness Insurance","Cancer Insurance","Gap Insurance","Legal Insurance","Identity Theft Protection","Accident & Illness (Pets)","Nursing Care / Custodial Care","Wellness Programs","Employee Assistance Program","Gym Membership","Health Savings Account","Flexible Savings Accounts","Commuter Benefits","Technology Stipend","Pay & Bonus","Stock Options","Student Loan Assistance","401(k)","403(b)","Pension Plan","Paid Time Off (PTO)","Parental Leave","Family and Medical Leave","Paid Volunteer Time","On-site Child Care","Employee Training & Development","Tuition Reimbursement","Employee Recognition","Performance Goals & Process","Pet-friendly Workplace","Ergonomic Workplace","Company Handbook","Marriage or Divorce","New Baby or Adoption","Loss of Insurance"],m=["AL","AK","AZ","AR","CA","CO","CT","DE","FL","GA","HI","ID","IL","IN","IA","KS","KY","LA","ME","MD","MA","MI","MN","MS","MO","MT","NE","NV","NH","NJ","NM","NY","NC","ND","OH","OK","OR","PA","RI","SC","SD","TN","TX","UT","VT","VA","WA","WV","WI","WY"],p=["API_KEY","OAUTH","BASIC_AUTH","CERTIFICATE"],x=["EDI","JSON","XML"],f=()=>{let e=(0,c.useRouter)(),[r,t]=(0,i.useState)(!1),[s,f]=(0,i.useState)(null),[h,g]=(0,i.useState)(!1),[y,b]=(0,i.useState)({carrierName:"",carrierCode:"",displayName:"",isSystemCarrier:!0,contactInfo:{phone:"",email:"",website:"",supportEmail:"",claimsPhone:"",memberServicesPhone:""},supportedPlanTypes:[],supportedCoverageTypes:[],supportedCoverageSubTypes:[],integration:{ediCapable:!1,apiEndpoint:"",apiVersion:"",authMethod:"API_KEY",dataFormat:"JSON"},licenseStates:[],amRating:"",networkName:""});(0,i.useEffect)(()=>{console.log("Create carrier page loaded - superadmin check disabled for testing")},[e]);let j=(e,r)=>{if(e.includes(".")){let[t,a]=e.split(".");b(e=>{let s=e[t];return"object"!=typeof s||null===s||Array.isArray(s)?e:{...e,[t]:{...s,[a]:r}}})}else b(t=>({...t,[e]:r}))},v=(e,r)=>{b(t=>{let a=t[e],s=a.includes(r)?a.filter(e=>e!==r):[...a,r];return{...t,[e]:s}})},N=(e,r)=>{b(t=>{let a=t[e],s=r.every(e=>a.includes(e));return{...t,[e]:s?[]:r}})},S=async e=>{e.preventDefault(),t(!0),f(null);try{throw Error("User ID not found. Please authenticate first.")}catch(e){f(e instanceof Error?e.message:"Failed to create carrier")}finally{t(!1)}};return h?a.jsx("div",{className:"min-h-screen bg-white flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center",children:[a.jsx("div",{className:"w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4",children:a.jsx(o.Sul,{className:"w-8 h-8 text-green-600"})}),a.jsx("h2",{className:"text-2xl font-bold text-gray-900 mb-2",children:"Carrier Created Successfully!"}),a.jsx("p",{className:"text-gray-600 mb-4",children:"The carrier has been created and is now available in the system."}),a.jsx("p",{className:"text-sm text-gray-500",children:"Redirecting to dashboard..."})]})}):(0,a.jsxs)("div",{className:"jsx-760cc8fa2520ad01 min-h-screen bg-white",children:[a.jsx(n(),{id:"760cc8fa2520ad01",children:"input.jsx-760cc8fa2520ad01,select.jsx-760cc8fa2520ad01,textarea.jsx-760cc8fa2520ad01{background-color:white!important;color:black!important}input.jsx-760cc8fa2520ad01::-webkit-input-placeholder{color:#9ca3af!important}input.jsx-760cc8fa2520ad01:-moz-placeholder{color:#9ca3af!important}input.jsx-760cc8fa2520ad01::-moz-placeholder{color:#9ca3af!important}input.jsx-760cc8fa2520ad01:-ms-input-placeholder{color:#9ca3af!important}input.jsx-760cc8fa2520ad01::-ms-input-placeholder{color:#9ca3af!important}input.jsx-760cc8fa2520ad01::placeholder{color:#9ca3af!important}"}),(0,a.jsxs)("div",{className:"jsx-760cc8fa2520ad01 max-w-4xl mx-auto py-8 px-4 sm:px-6 lg:px-8",children:[(0,a.jsxs)("div",{className:"jsx-760cc8fa2520ad01 mb-8",children:[(0,a.jsxs)("div",{className:"jsx-760cc8fa2520ad01 flex items-center gap-3 mb-4",children:[a.jsx("div",{className:"jsx-760cc8fa2520ad01 w-10 h-10 bg-gray-900 rounded-lg flex items-center justify-center",children:a.jsx(o.$xp,{className:"w-6 h-6 text-white"})}),(0,a.jsxs)("div",{className:"jsx-760cc8fa2520ad01",children:[a.jsx("h1",{style:{fontSize:"clamp(20px, 5vw, 24px)",fontWeight:"600",color:"#111827",fontFamily:"sans-serif"},className:"jsx-760cc8fa2520ad01",children:"Create New Carrier"}),a.jsx("p",{style:{fontSize:"clamp(12px, 3vw, 14px)",lineHeight:"1.4",color:"#6b7280",fontFamily:"sans-serif"},className:"jsx-760cc8fa2520ad01",children:"Add a new insurance carrier to the system"})]})]}),(0,a.jsxs)("nav",{className:"jsx-760cc8fa2520ad01 text-sm text-gray-500",children:[a.jsx("span",{onClick:()=>e.push("/ai-enroller"),className:"jsx-760cc8fa2520ad01 hover:text-gray-700 cursor-pointer",children:"Dashboard"}),a.jsx("span",{className:"jsx-760cc8fa2520ad01 mx-2",children:"/"}),a.jsx("span",{className:"jsx-760cc8fa2520ad01 text-gray-900",children:"Create Carrier"})]})]}),s&&a.jsx("div",{className:"jsx-760cc8fa2520ad01 mb-6 bg-red-50 border border-red-200 rounded-lg p-4",children:(0,a.jsxs)("div",{className:"jsx-760cc8fa2520ad01 flex items-center gap-2",children:[a.jsx(o.fMW,{className:"w-5 h-5 text-red-600"}),a.jsx("p",{className:"jsx-760cc8fa2520ad01 text-red-600 text-sm",children:s})]})}),(0,a.jsxs)("form",{onSubmit:S,className:"jsx-760cc8fa2520ad01 space-y-8",children:[(0,a.jsxs)("div",{className:"jsx-760cc8fa2520ad01 bg-gray-50 rounded-lg p-6",children:[a.jsx("h3",{style:{fontSize:"clamp(18px, 4vw, 20px)",fontWeight:"600",color:"#111827",marginBottom:"1rem",fontFamily:"sans-serif"},className:"jsx-760cc8fa2520ad01",children:"Basic Information"}),(0,a.jsxs)("div",{className:"jsx-760cc8fa2520ad01 grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{className:"jsx-760cc8fa2520ad01",children:[a.jsx("label",{className:"jsx-760cc8fa2520ad01 block text-sm font-medium text-gray-700 mb-2",children:"Carrier Name *"}),a.jsx("input",{type:"text",required:!0,value:y.carrierName,onChange:e=>j("carrierName",e.target.value),style:{backgroundColor:"white",color:"black"},placeholder:"e.g., Blue Cross Blue Shield",className:"jsx-760cc8fa2520ad01 w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"})]}),(0,a.jsxs)("div",{className:"jsx-760cc8fa2520ad01",children:[a.jsx("label",{className:"jsx-760cc8fa2520ad01 block text-sm font-medium text-gray-700 mb-2",children:"Carrier Code *"}),a.jsx("input",{type:"text",required:!0,value:y.carrierCode,onChange:e=>j("carrierCode",e.target.value.toUpperCase()),style:{backgroundColor:"white",color:"black"},placeholder:"e.g., BCBS",className:"jsx-760cc8fa2520ad01 w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"})]}),(0,a.jsxs)("div",{className:"jsx-760cc8fa2520ad01",children:[a.jsx("label",{className:"jsx-760cc8fa2520ad01 block text-sm font-medium text-gray-700 mb-2",children:"Display Name"}),a.jsx("input",{type:"text",value:y.displayName,onChange:e=>j("displayName",e.target.value),style:{backgroundColor:"white",color:"black"},placeholder:"User-friendly display name",className:"jsx-760cc8fa2520ad01 w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"})]}),(0,a.jsxs)("div",{className:"jsx-760cc8fa2520ad01 flex items-center",children:[a.jsx("input",{type:"checkbox",id:"isSystemCarrier",checked:y.isSystemCarrier,onChange:e=>j("isSystemCarrier",e.target.checked),className:"jsx-760cc8fa2520ad01 w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"}),a.jsx("label",{htmlFor:"isSystemCarrier",className:"jsx-760cc8fa2520ad01 ml-2 text-sm text-gray-700",children:"System Carrier (Available to all brokers)"})]})]})]}),(0,a.jsxs)("div",{className:"jsx-760cc8fa2520ad01 bg-gray-50 rounded-lg p-6",children:[a.jsx("h3",{style:{fontSize:"clamp(18px, 4vw, 20px)",fontWeight:"600",color:"#111827",marginBottom:"1rem",fontFamily:"sans-serif"},className:"jsx-760cc8fa2520ad01",children:"Contact Information"}),(0,a.jsxs)("div",{className:"jsx-760cc8fa2520ad01 grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{className:"jsx-760cc8fa2520ad01",children:[(0,a.jsxs)("label",{className:"jsx-760cc8fa2520ad01 block text-sm font-medium text-gray-700 mb-2",children:[a.jsx(o.PES,{className:"w-4 h-4 inline mr-1"}),"Phone"]}),a.jsx("input",{type:"tel",value:y.contactInfo.phone,onChange:e=>j("contactInfo.phone",e.target.value),placeholder:"(*************",className:"jsx-760cc8fa2520ad01 w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"})]}),(0,a.jsxs)("div",{className:"jsx-760cc8fa2520ad01",children:[(0,a.jsxs)("label",{className:"jsx-760cc8fa2520ad01 block text-sm font-medium text-gray-700 mb-2",children:[a.jsx(o.Zuw,{className:"w-4 h-4 inline mr-1"}),"Email"]}),a.jsx("input",{type:"email",value:y.contactInfo.email,onChange:e=>j("contactInfo.email",e.target.value),placeholder:"<EMAIL>",className:"jsx-760cc8fa2520ad01 w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"})]}),(0,a.jsxs)("div",{className:"jsx-760cc8fa2520ad01",children:[(0,a.jsxs)("label",{className:"jsx-760cc8fa2520ad01 block text-sm font-medium text-gray-700 mb-2",children:[a.jsx(o.goM,{className:"w-4 h-4 inline mr-1"}),"Website"]}),a.jsx("input",{type:"url",value:y.contactInfo.website,onChange:e=>j("contactInfo.website",e.target.value),placeholder:"https://www.carrier.com",className:"jsx-760cc8fa2520ad01 w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"})]}),(0,a.jsxs)("div",{className:"jsx-760cc8fa2520ad01",children:[a.jsx("label",{className:"jsx-760cc8fa2520ad01 block text-sm font-medium text-gray-700 mb-2",children:"Support Email"}),a.jsx("input",{type:"email",value:y.contactInfo.supportEmail,onChange:e=>j("contactInfo.supportEmail",e.target.value),placeholder:"<EMAIL>",className:"jsx-760cc8fa2520ad01 w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"})]}),(0,a.jsxs)("div",{className:"jsx-760cc8fa2520ad01",children:[a.jsx("label",{className:"jsx-760cc8fa2520ad01 block text-sm font-medium text-gray-700 mb-2",children:"Claims Phone"}),a.jsx("input",{type:"tel",value:y.contactInfo.claimsPhone,onChange:e=>j("contactInfo.claimsPhone",e.target.value),placeholder:"(*************",className:"jsx-760cc8fa2520ad01 w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"})]}),(0,a.jsxs)("div",{className:"jsx-760cc8fa2520ad01",children:[a.jsx("label",{className:"jsx-760cc8fa2520ad01 block text-sm font-medium text-gray-700 mb-2",children:"Member Services Phone"}),a.jsx("input",{type:"tel",value:y.contactInfo.memberServicesPhone,onChange:e=>j("contactInfo.memberServicesPhone",e.target.value),placeholder:"(*************",className:"jsx-760cc8fa2520ad01 w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"})]})]})]}),(0,a.jsxs)("div",{className:"jsx-760cc8fa2520ad01 bg-gray-50 rounded-lg p-6",children:[(0,a.jsxs)("div",{className:"jsx-760cc8fa2520ad01 flex items-center justify-between mb-4",children:[a.jsx("h3",{className:"jsx-760cc8fa2520ad01 text-lg font-semibold text-gray-900",children:"Supported Plan Types"}),a.jsx("button",{type:"button",onClick:()=>N("supportedPlanTypes",l),className:"jsx-760cc8fa2520ad01 px-3 py-1 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors",children:l.every(e=>y.supportedPlanTypes.includes(e))?"Deselect All":"Select All"})]}),a.jsx("div",{className:"jsx-760cc8fa2520ad01 grid grid-cols-2 md:grid-cols-4 gap-3",children:l.map(e=>(0,a.jsxs)("label",{className:"jsx-760cc8fa2520ad01 flex items-center space-x-2 cursor-pointer",children:[a.jsx("input",{type:"checkbox",checked:y.supportedPlanTypes.includes(e),onChange:()=>v("supportedPlanTypes",e),className:"jsx-760cc8fa2520ad01 w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"}),a.jsx("span",{className:"jsx-760cc8fa2520ad01 text-sm text-gray-700",children:e})]},e))})]}),(0,a.jsxs)("div",{className:"jsx-760cc8fa2520ad01 bg-gray-50 rounded-lg p-6",children:[(0,a.jsxs)("div",{className:"jsx-760cc8fa2520ad01 flex items-center justify-between mb-4",children:[a.jsx("h3",{className:"jsx-760cc8fa2520ad01 text-lg font-semibold text-gray-900",children:"Supported Coverage Types"}),a.jsx("button",{type:"button",onClick:()=>N("supportedCoverageTypes",d),className:"jsx-760cc8fa2520ad01 px-3 py-1 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors",children:d.every(e=>y.supportedCoverageTypes.includes(e))?"Deselect All":"Select All"})]}),a.jsx("div",{className:"jsx-760cc8fa2520ad01 grid grid-cols-1 md:grid-cols-2 gap-3",children:d.map(e=>(0,a.jsxs)("label",{className:"jsx-760cc8fa2520ad01 flex items-center space-x-2 cursor-pointer",children:[a.jsx("input",{type:"checkbox",checked:y.supportedCoverageTypes.includes(e),onChange:()=>v("supportedCoverageTypes",e),className:"jsx-760cc8fa2520ad01 w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"}),a.jsx("span",{className:"jsx-760cc8fa2520ad01 text-sm text-gray-700",children:e})]},e))})]}),(0,a.jsxs)("div",{className:"jsx-760cc8fa2520ad01 bg-gray-50 rounded-lg p-6",children:[(0,a.jsxs)("div",{className:"jsx-760cc8fa2520ad01 flex items-center justify-between mb-4",children:[a.jsx("h3",{className:"jsx-760cc8fa2520ad01 text-lg font-semibold text-gray-900",children:"Supported Coverage Subtypes"}),a.jsx("button",{type:"button",onClick:()=>N("supportedCoverageSubTypes",u),className:"jsx-760cc8fa2520ad01 px-3 py-1 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors",children:u.every(e=>y.supportedCoverageSubTypes.includes(e))?"Deselect All":"Select All"})]}),a.jsx("div",{className:"jsx-760cc8fa2520ad01 grid grid-cols-2 md:grid-cols-3 gap-3 max-h-60 overflow-y-auto",children:u.map(e=>(0,a.jsxs)("label",{className:"jsx-760cc8fa2520ad01 flex items-center space-x-2 cursor-pointer",children:[a.jsx("input",{type:"checkbox",checked:y.supportedCoverageSubTypes.includes(e),onChange:()=>v("supportedCoverageSubTypes",e),className:"jsx-760cc8fa2520ad01 w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"}),a.jsx("span",{className:"jsx-760cc8fa2520ad01 text-sm text-gray-700",children:e})]},e))})]}),(0,a.jsxs)("div",{className:"jsx-760cc8fa2520ad01 bg-gray-50 rounded-lg p-6",children:[a.jsx("h3",{className:"jsx-760cc8fa2520ad01 text-lg font-semibold text-gray-900 mb-4",children:"Integration Settings"}),(0,a.jsxs)("div",{className:"jsx-760cc8fa2520ad01 space-y-6",children:[(0,a.jsxs)("div",{className:"jsx-760cc8fa2520ad01 flex items-center",children:[a.jsx("input",{type:"checkbox",id:"ediCapable",checked:y.integration.ediCapable,onChange:e=>j("integration.ediCapable",e.target.checked),className:"jsx-760cc8fa2520ad01 w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"}),a.jsx("label",{htmlFor:"ediCapable",className:"jsx-760cc8fa2520ad01 ml-2 text-sm text-gray-700",children:"EDI Capable (Electronic Data Interchange)"})]}),(0,a.jsxs)("div",{className:"jsx-760cc8fa2520ad01 grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{className:"jsx-760cc8fa2520ad01",children:[a.jsx("label",{className:"jsx-760cc8fa2520ad01 block text-sm font-medium text-gray-700 mb-2",children:"API Endpoint"}),a.jsx("input",{type:"url",value:y.integration.apiEndpoint,onChange:e=>j("integration.apiEndpoint",e.target.value),placeholder:"https://api.carrier.com/v1",className:"jsx-760cc8fa2520ad01 w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"})]}),(0,a.jsxs)("div",{className:"jsx-760cc8fa2520ad01",children:[a.jsx("label",{className:"jsx-760cc8fa2520ad01 block text-sm font-medium text-gray-700 mb-2",children:"API Version"}),a.jsx("input",{type:"text",value:y.integration.apiVersion,onChange:e=>j("integration.apiVersion",e.target.value),placeholder:"v1.0",className:"jsx-760cc8fa2520ad01 w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"})]}),(0,a.jsxs)("div",{className:"jsx-760cc8fa2520ad01",children:[a.jsx("label",{className:"jsx-760cc8fa2520ad01 block text-sm font-medium text-gray-700 mb-2",children:"Authentication Method"}),a.jsx("select",{value:y.integration.authMethod,onChange:e=>j("integration.authMethod",e.target.value),className:"jsx-760cc8fa2520ad01 w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500",children:p.map(e=>a.jsx("option",{value:e,className:"jsx-760cc8fa2520ad01",children:e},e))})]}),(0,a.jsxs)("div",{className:"jsx-760cc8fa2520ad01",children:[a.jsx("label",{className:"jsx-760cc8fa2520ad01 block text-sm font-medium text-gray-700 mb-2",children:"Data Format"}),a.jsx("select",{value:y.integration.dataFormat,onChange:e=>j("integration.dataFormat",e.target.value),className:"jsx-760cc8fa2520ad01 w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500",children:x.map(e=>a.jsx("option",{value:e,className:"jsx-760cc8fa2520ad01",children:e},e))})]})]})]})]}),(0,a.jsxs)("div",{className:"jsx-760cc8fa2520ad01 bg-gray-50 rounded-lg p-6",children:[a.jsx("h3",{className:"jsx-760cc8fa2520ad01 text-lg font-semibold text-gray-900 mb-4",children:"Business Information"}),(0,a.jsxs)("div",{className:"jsx-760cc8fa2520ad01 grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{className:"jsx-760cc8fa2520ad01",children:[a.jsx("label",{className:"jsx-760cc8fa2520ad01 block text-sm font-medium text-gray-700 mb-2",children:"A.M. Best Rating"}),a.jsx("input",{type:"text",value:y.amRating,onChange:e=>j("amRating",e.target.value),placeholder:"e.g., A++",className:"jsx-760cc8fa2520ad01 w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"})]}),(0,a.jsxs)("div",{className:"jsx-760cc8fa2520ad01",children:[a.jsx("label",{className:"jsx-760cc8fa2520ad01 block text-sm font-medium text-gray-700 mb-2",children:"Network Name"}),a.jsx("input",{type:"text",value:y.networkName,onChange:e=>j("networkName",e.target.value),placeholder:"e.g., Choice Plus Network",className:"jsx-760cc8fa2520ad01 w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"})]})]}),(0,a.jsxs)("div",{className:"jsx-760cc8fa2520ad01 mt-6",children:[(0,a.jsxs)("div",{className:"jsx-760cc8fa2520ad01 flex items-center justify-between mb-3",children:[a.jsx("label",{className:"jsx-760cc8fa2520ad01 block text-sm font-medium text-gray-700",children:"Licensed States"}),a.jsx("button",{type:"button",onClick:()=>N("licenseStates",m),className:"jsx-760cc8fa2520ad01 px-2 py-1 text-xs bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors",children:m.every(e=>y.licenseStates.includes(e))?"Deselect All":"Select All"})]}),a.jsx("div",{className:"jsx-760cc8fa2520ad01 grid grid-cols-5 md:grid-cols-10 gap-2 max-h-40 overflow-y-auto",children:m.map(e=>(0,a.jsxs)("label",{className:"jsx-760cc8fa2520ad01 flex items-center space-x-1 cursor-pointer",children:[a.jsx("input",{type:"checkbox",checked:y.licenseStates.includes(e),onChange:()=>v("licenseStates",e),className:"jsx-760cc8fa2520ad01 w-3 h-3 text-blue-600 border-gray-300 rounded focus:ring-blue-500"}),a.jsx("span",{className:"jsx-760cc8fa2520ad01 text-xs text-gray-700",children:e})]},e))})]})]}),(0,a.jsxs)("div",{className:"jsx-760cc8fa2520ad01 flex justify-end space-x-4 pt-6 border-t border-gray-200",children:[a.jsx("button",{type:"button",onClick:()=>e.push("/ai-enroller"),className:"jsx-760cc8fa2520ad01 px-6 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors",children:"Cancel"}),a.jsx("button",{type:"submit",disabled:r,className:"jsx-760cc8fa2520ad01 px-6 py-2 bg-gray-900 text-white rounded-lg hover:bg-gray-800 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center gap-2",children:r?(0,a.jsxs)(a.Fragment,{children:[a.jsx("div",{className:"jsx-760cc8fa2520ad01 w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"}),"Creating..."]}):(0,a.jsxs)(a.Fragment,{children:[a.jsx(o.r7I,{className:"w-4 h-4"}),"Create Carrier"]})})]})]})]})]})}},78021:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>n});var a=t(10326);t(17577),t(23824),t(54658);var s=t(43058);function n({children:e}){return a.jsx(s.Z,{children:a.jsx("div",{className:"min-h-screen bg-white",style:{backgroundColor:"white",minHeight:"100vh"},children:e})})}},43058:(e,r,t)=>{"use strict";t.d(r,{Z:()=>u});var a=t(10326),s=t(17577),n=t(22758),i=t(35047),c=t(31870);t(32049),t(94638);var o=t(98139),l=t(6283);let d=()=>/Mobi|Android/i.test(navigator.userAgent),u=({children:e})=>{let{user:r,loading:t}=(0,n.a)(),u=(0,i.useRouter)(),m=(0,i.usePathname)(),p=(0,c.T)(),[x,f]=(0,s.useState)(!1),h=(0,c.C)(e=>e.user.userProfile);return((0,s.useEffect)(()=>{},[p,h.name]),(0,s.useEffect)(()=>{console.log("ProtectedRoute useEffect triggered"),console.log("Current user: ",r),console.log("Loading state: ",t),console.log("Current user details: ",h),t||r||(console.log("User not authenticated, redirecting to home"),f(!1),u.push("/")),!t&&h.companyId&&""===h.companyId&&(console.log("Waiting to retrieve company details"),f(!1)),!t&&h.companyId&&""!==h.companyId&&(console.log("User found, rendering children"),f(!0)),d()&&!m.startsWith("/mobile")&&(console.log(`Redirecting to mobile version of ${m}`),u.push(`/mobile${m}`))},[r,t,h,u,m]),x)?r?a.jsx(a.Fragment,{children:e}):null:a.jsx(l.Z,{sx:{display:"flex",justifyContent:"center",alignItems:"center",height:"100vh",bgcolor:"#f6f8fc"},children:a.jsx(o.Z,{})})}},94638:(e,r,t)=>{"use strict";t.d(r,{G9:()=>g,JZ:()=>y,M_:()=>x,N:()=>l,Nq:()=>p,TQ:()=>u,Ur:()=>c,aE:()=>d,aK:()=>v,dA:()=>o,gt:()=>b,mb:()=>h,qB:()=>j,yu:()=>m,zX:()=>f});var a=t(53148),s=t(39352),n=t(25748),i=t(32049);function c(e){switch(e){case"Our Culture & Workplace":return"Work Policies";case"Protecting Your Income":return"Income Security";default:return e}}function o(e){switch(e){case"On-site Amenities":return"Company Handbook";case"Protecting Your Income":return"Income Security";case"Got married/divorced":return"Marriage or Divorce";case"Had a baby or adopted a baby":return"New Baby or Adoption";case"Lost your insurance":return"Loss of Insurance";default:return e}}async function l(e,r){let t=await (0,a.A_)("/benefits/benefit-types",{companyId:r});return console.log("COMPANY BENEFIT TYPES RESPONSE: ",t.benefitTypes),e((0,s.x7)(t.benefitTypes)),t.benefitTypes}async function d(e,r){let t=await (0,a.A_)("/benefits/all-benefits",{companyId:r});console.log("COMPANY BENEFIT TYPES WITH BENEFITS RESPONSE: ",t),e((0,n.US)(t.benefitsPerType))}async function u(e){let r=await (0,a.A_)("/admin/all-employees");return console.log("COMPANY TEAM MEMBERS RESPONSE: ",r),e((0,s.Vv)(r.employees)),r.employees}async function m(e,r){return console.log("ADDING USERS: ",r),await (0,a.j0)("/admin/add/employees",{employeeList:r})}async function p(e,r,t){try{console.log("\uD83D\uDD0D Debug: User being updated:",r);let e={employeeId:r,updatedDetails:{name:t.name,email:t.email,details:{phoneNumber:t.phoneNumber||"",department:t.department||"",title:t.title||"",role:t.title||""}}};return t.dateOfBirth&&(e.updatedDetails.details.dateOfBirth=t.dateOfBirth),t.hireDate&&(e.updatedDetails.details.hireDate=t.hireDate),t.annualSalary&&(e.updatedDetails.details.annualSalary=t.annualSalary),t.employeeClassType&&(e.updatedDetails.details.employeeClassType=t.employeeClassType),t.workSchedule&&(e.updatedDetails.details.workSchedule=t.workSchedule),t.ssn&&(e.updatedDetails.details.ssn=t.ssn),t.employeeId&&(e.updatedDetails.details.employeeId=t.employeeId),t.workLocation&&(e.updatedDetails.details.workLocation=t.workLocation),t.address&&(e.updatedDetails.details.address=t.address),t.mailingAddress&&(e.updatedDetails.details.mailingAddress=t.mailingAddress),t.emergencyContact&&(e.updatedDetails.details.emergencyContact=t.emergencyContact),e.updatedDetails.details.dependents=t.dependents||[],console.log("Middleware - dependents being sent:",e.updatedDetails.details.dependents),console.log("Sending PUT request with cleaned data:",e),await (0,a.GH)("/admin/update/employee",e)}catch(e){throw console.error("Error in updateUser middleware:",e),e}}async function x(e,r){let t=await (0,a.A_)("/employee",{"user-id":r});return e((0,i.$l)({name:t.currentUser.name,email:t.currentUser.email,companyId:t.currentUser.companyId,role:t.currentUser.role,isAdmin:t.currentUser.isAdmin,isBroker:t.currentUser.isBroker,details:t.currentUser.details})),t}async function f(e,r,t){let s=await (0,a.j0)("/admin/onboard",{company:{name:r.name,adminEmail:r.adminEmail,adminRole:r.adminRole,companySize:r.companySize,industry:r.industry,location:r.location,website:r.website,howHeard:r.howHeard,brokerId:r.brokerId,brokerageId:r.brokerageId,isBrokerage:r.isBrokerage,isActivated:r.isActivated,referralSource:r.referralSource,details:{logo:""}},user:{email:t.email,name:t.name,role:t.role,isAdmin:t.isAdmin,isBroker:t.isBroker,isActivated:t.isActivated}}),n=s.data.userId,i=s.data.companyId;return localStorage.setItem("userid1",n),localStorage.setItem("companyId1",i),s}async function h(e,r){return console.log("SENDING LOGIN LINK TO EMPLOYEE: ",e,r),await (0,a.j0)("/admin/send-user-login-link",{userId:e,companyId:r})}async function g(e,r,t,s){let n=await (0,a.j0)("/admin/add/employer",{brokerId:e,companyName:r,companyAdminEmail:t,companyAdminName:s});return console.log("BROKER ADDS COMPANY RESPONSE: ",n),n}async function y(e,r){return 200===(await (0,a.j0)("/employee/offboard/",{userId:e,companyId:r})).status}async function b(e,r){return await (0,a.j0)("/employee/enable/",{userId:e,companyId:r})}async function j(e,r){try{let r=await (0,a.A_)("/admin/all-companies");console.log("CLIENT COMPANIES UNDER BROKER: ",r);let t=r.companies||[];try{let e=await (0,a.A_)("/employee/company-details");console.log("BROKER'S OWN COMPANY: ",e),e.company&&e.company.isBrokerage&&!t.some(r=>r._id===e.company._id)&&(t.unshift(e.company),console.log("Added broker's own company to the list"))}catch(e){console.log("Could not fetch broker's own company (this is normal if user is not a broker):",e)}return console.log("ALL COMPANIES (CLIENT + OWN): ",t),e((0,i.Ym)(t)),{...r,companies:t}}catch(r){return console.error("Error fetching companies:",r),e((0,i.Ym)([])),{companies:[]}}}async function v(e){let r=await (0,a.A_)("/employee/company-details");return e((0,s.sy)(r.company)),r.status}},31870:(e,r,t)=>{"use strict";t.d(r,{C:()=>n,T:()=>s});var a=t(25842);let s=()=>(0,a.I0)(),n=a.v9},80853:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>a});let a=(0,t(68570).createProxy)(String.raw`C:\beno\project_dev\qharmony-frontend-v1\src\app\ai-enroller\create-carrier\page.tsx#default`)},6079:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>a});let a=(0,t(68570).createProxy)(String.raw`C:\beno\project_dev\qharmony-frontend-v1\src\app\ai-enroller\layout.tsx#default`)},73881:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});var a=t(66621);let s=e=>[{type:"image/x-icon",sizes:"16x16",url:(0,a.fillMetadataSegment)(".",e.params,"favicon.ico")+""}]},54658:()=>{},23824:()=>{}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[8948,1183,6621,8492,576],()=>t(70218));module.exports=a})();