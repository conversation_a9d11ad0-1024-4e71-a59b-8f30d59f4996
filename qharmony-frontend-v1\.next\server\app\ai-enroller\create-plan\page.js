(()=>{var e={};e.id=1791,e.ids=[1791],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},27790:e=>{"use strict";e.exports=require("assert")},84770:e=>{"use strict";e.exports=require("crypto")},17702:e=>{"use strict";e.exports=require("events")},92048:e=>{"use strict";e.exports=require("fs")},32615:e=>{"use strict";e.exports=require("http")},35240:e=>{"use strict";e.exports=require("https")},19801:e=>{"use strict";e.exports=require("os")},55315:e=>{"use strict";e.exports=require("path")},76162:e=>{"use strict";e.exports=require("stream")},74026:e=>{"use strict";e.exports=require("string_decoder")},74175:e=>{"use strict";e.exports=require("tty")},17360:e=>{"use strict";e.exports=require("url")},21764:e=>{"use strict";e.exports=require("util")},71568:e=>{"use strict";e.exports=require("zlib")},98061:e=>{"use strict";e.exports=require("node:assert")},92761:e=>{"use strict";e.exports=require("node:async_hooks")},72254:e=>{"use strict";e.exports=require("node:buffer")},40027:e=>{"use strict";e.exports=require("node:console")},6005:e=>{"use strict";e.exports=require("node:crypto")},65714:e=>{"use strict";e.exports=require("node:diagnostics_channel")},15673:e=>{"use strict";e.exports=require("node:events")},88849:e=>{"use strict";e.exports=require("node:http")},42725:e=>{"use strict";e.exports=require("node:http2")},87503:e=>{"use strict";e.exports=require("node:net")},38846:e=>{"use strict";e.exports=require("node:perf_hooks")},39630:e=>{"use strict";e.exports=require("node:querystring")},84492:e=>{"use strict";e.exports=require("node:stream")},31764:e=>{"use strict";e.exports=require("node:tls")},41041:e=>{"use strict";e.exports=require("node:url")},47261:e=>{"use strict";e.exports=require("node:util")},93746:e=>{"use strict";e.exports=require("node:util/types")},24086:e=>{"use strict";e.exports=require("node:worker_threads")},65628:e=>{"use strict";e.exports=require("node:zlib")},12010:(e,a,t)=>{"use strict";t.r(a),t.d(a,{GlobalError:()=>l.a,__next_app__:()=>h,originalPathname:()=>p,pages:()=>d,routeModule:()=>m,tree:()=>c}),t(77267),t(6079),t(33709),t(35866);var r=t(23191),i=t(88716),s=t(37922),l=t.n(s),n=t(95231),o={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>n[e]);t.d(a,o);let c=["",{children:["ai-enroller",{children:["create-plan",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,77267)),"C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\create-plan\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,6079)),"C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,33709)),"C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,35866,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],d=["C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\create-plan\\page.tsx"],p="/ai-enroller/create-plan/page",h={require:t,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:i.x.APP_PAGE,page:"/ai-enroller/create-plan/page",pathname:"/ai-enroller/create-plan",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},63730:(e,a,t)=>{Promise.resolve().then(t.bind(t,85761))},40853:(e,a,t)=>{Promise.resolve().then(t.bind(t,78021))},14962:(e,a,t)=>{"use strict";t.d(a,{Z:()=>g});var r=t(17577),i=t(34526),s=t(83784),l=t(15763),n=t(23743),o=t(78029),c=t(37382),d=t(10326);function p(e){return`scale(${e}, ${e**2})`}let h={entering:{opacity:1,transform:p(1)},entered:{opacity:1,transform:"none"}},m="undefined"!=typeof navigator&&/^((?!chrome|android).)*(safari|mobile)/i.test(navigator.userAgent)&&/(os |version\/)15(.|_)4/i.test(navigator.userAgent),u=r.forwardRef(function(e,a){let{addEndListener:t,appear:u=!0,children:g,easing:x,in:v,onEnter:f,onEntered:j,onEntering:y,onExit:b,onExited:N,onExiting:w,style:C,timeout:P="auto",TransitionComponent:D=l.ZP,...k}=e,T=(0,i.Z)(),S=r.useRef(),z=(0,n.default)(),I=r.useRef(null),q=(0,c.Z)(I,(0,s.Z)(g),a),E=e=>a=>{if(e){let t=I.current;void 0===a?e(t):e(t,a)}},A=E(y),M=E((e,a)=>{let t;(0,o.n)(e);let{duration:r,delay:i,easing:s}=(0,o.C)({style:C,timeout:P,easing:x},{mode:"enter"});"auto"===P?(t=z.transitions.getAutoHeightDuration(e.clientHeight),S.current=t):t=r,e.style.transition=[z.transitions.create("opacity",{duration:t,delay:i}),z.transitions.create("transform",{duration:m?t:.666*t,delay:i,easing:s})].join(","),f&&f(e,a)}),F=E(j),_=E(w),O=E(e=>{let a;let{duration:t,delay:r,easing:i}=(0,o.C)({style:C,timeout:P,easing:x},{mode:"exit"});"auto"===P?(a=z.transitions.getAutoHeightDuration(e.clientHeight),S.current=a):a=t,e.style.transition=[z.transitions.create("opacity",{duration:a,delay:r}),z.transitions.create("transform",{duration:m?a:.666*a,delay:m?r:r||.333*a,easing:i})].join(","),e.style.opacity=0,e.style.transform=p(.75),b&&b(e)}),R=E(N);return(0,d.jsx)(D,{appear:u,in:v,nodeRef:I,onEnter:M,onEntered:F,onEntering:A,onExit:O,onExited:R,onExiting:_,addEndListener:e=>{"auto"===P&&T.start(S.current||0,e),t&&t(I.current,e)},timeout:"auto"===P?null:P,...k,children:(e,{ownerState:a,...t})=>r.cloneElement(g,{style:{opacity:0,transform:p(.75),visibility:"exited"!==e||v?void 0:"hidden",...h[e],...C,...g.props.style},ref:q,...t})})});u&&(u.muiSupportAuto=!0);let g=u},43227:(e,a,t)=>{"use strict";t.d(a,{Z:()=>i});var r=t(17577);let i=function({controlled:e,default:a,name:t,state:i="value"}){let{current:s}=r.useRef(void 0!==e),[l,n]=r.useState(a),o=r.useCallback(e=>{s||n(e)},[]);return[s?e:l,o]}},69800:(e,a,t)=>{"use strict";t.d(a,{Z:()=>n});var r=t(72823),i=t(6348),s=t(28606),l=t(32782);let n=function(e){let{elementType:a,externalSlotProps:t,ownerState:n,skipResolvingSlotProps:o=!1,...c}=e,d=o?{}:(0,l.Z)(t,n),{props:p,internalRef:h}=(0,s.Z)({...c,externalSlotProps:d}),m=(0,r.Z)(h,d?.ref,e.additionalProps?.ref);return(0,i.Z)(a,{...p,ref:m},n)}},85761:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>y});var r=t(10326),i=t(77626),s=t.n(i),l=t(17577),n=t(35047),o=t(90434),c=t(46226),d=t(43058),p=t(92371);let h=()=>{let e=(0,n.useRouter)();return(0,l.useEffect)(()=>{let a=["/ai-enroller","/ai-enroller/plans","/ai-enroller/manage-groups"],t=setTimeout(()=>{a.forEach(a=>{e.prefetch(a)})},200);return["/brea.png"].forEach(e=>{new Image().src=e}),window.formPatterns={planCode:/^[A-Z0-9-_]{3,20}$/i,email:/^[^\s@]+@[^\s@]+\.[^\s@]+$/,url:/^https?:\/\/.+/},(()=>{try{let e="ai-enroller-test";localStorage.setItem(e,"test"),localStorage.removeItem(e),window.storageAvailable=!0}catch(e){window.storageAvailable=!1,console.warn("localStorage not available")}})(),()=>{clearTimeout(t)}},[e]),null};var m=t(14326),u=t(74034);function g(e){return(0,u.w_)({tag:"svg",attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{d:"M256 56C145.72 56 56 145.72 56 256s89.72 200 200 200 200-89.72 200-200S366.28 56 256 56zm0 82a26 26 0 1 1-26 26 26 26 0 0 1 26-26zm48 226h-88a16 16 0 0 1 0-32h28v-88h-16a16 16 0 0 1 0-32h32a16 16 0 0 1 16 16v104h28a16 16 0 0 1 0 32z"},child:[]}]})(e)}var x=t(67925),v=t(38492),f=t(22509);t(91596);var j=t(59562);let y=()=>{let e=(0,n.useRouter)(),[a,t]=(0,l.useState)(1),[i,u]=(0,l.useState)(null),[y,b]=(0,l.useState)(!0),[N,w]=(0,l.useState)(null),[C,P]=(0,l.useState)({isChecking:!1,isDuplicate:!1}),[D,k]=(0,l.useState)({isChecking:!1,isDuplicate:!1});(0,p.$)("Create Plan Page");let[T,S]=(0,l.useState)({planName:"",planCode:"",carrier:"",planType:"",coverageCategory:"",coverageType:"",metalTier:"",videoUrl:"",documents:[],description:"",highlights:[""],effectiveDate:"",endDate:"",copay:"",deductible:""}),z=[{number:1,title:"Documents",subtitle:"Upload files",active:1===a,completed:a>1,tooltip:"Upload plan documents, brochures, and supporting materials (optional)"},{number:2,title:"Basic Info",subtitle:"Plan details",active:2===a,completed:a>2,tooltip:"Enter plan name, carrier, coverage type, and metal tier information"},{number:3,title:"Description",subtitle:"Details & video",active:3===a,completed:a>3,tooltip:"Add plan description, key highlights, and optional video content"},{number:4,title:"Preview",subtitle:"Review & create",active:4===a,completed:a>4,tooltip:"Review all plan details and create your new plan"},{number:5,title:"Success",subtitle:"Plan created",active:5===a,completed:!1,tooltip:"Plan successfully created and added to your catalog"}],I=(0,l.useMemo)(()=>(0,j.HP)(),[]);(0,l.useEffect)(()=>{localStorage.removeItem("ai-enroller-draft-plan"),e.prefetch("/ai-enroller"),e.prefetch("/ai-enroller/plans"),(async()=>{b(!0);try{let e=sessionStorage.getItem("ai-enroller-create-plan-data"),a=sessionStorage.getItem("ai-enroller-create-plan-cache-time"),t=a?Date.now()-parseInt(a):1/0;if(e&&t<3e5){let a=JSON.parse(e);w(a),b(!1);let t=localStorage.getItem("ai-enroller-draft-plan");if(t&&!T.planName&&!T.coverageCategory){let e=JSON.parse(t);console.log("Loading draft data from cache:",e),S(e)}return}let r=Promise.race([(0,j.ie)(),new Promise((e,a)=>setTimeout(()=>a(Error("Carriers API timeout")),3e3))]),i=await r.catch(e=>(console.warn("Carriers API failed or timed out:",e),{success:!1,error:e.message})),s={plans:[],templates:[],carriers:i.success&&i.data||[],planTypes:I.data?.planTypes||[],coverageCategories:I.data?.coverageCategories||[],coverageMap:I.data?.coverageMap||{},metalTiers:I.data?.metalTiers||[]};w(s),console.log("Data loaded successfully:",s),console.log("Coverage categories:",s.coverageCategories),console.log("Loaded carriers:",s.carriers),sessionStorage.setItem("ai-enroller-create-plan-data",JSON.stringify(s)),sessionStorage.setItem("ai-enroller-create-plan-cache-time",Date.now().toString());let l=localStorage.getItem("ai-enroller-draft-plan");if(l&&!T.planName&&!T.coverageCategory){let e=JSON.parse(l);console.log("Loading draft data from fresh load:",e),S(e)}}catch(e){console.error("Error loading data:",e),w({plans:[],templates:[],carriers:[],planTypes:I.data?.planTypes||[],coverageCategories:I.data?.coverageCategories||[],coverageMap:I.data?.coverageMap||{},metalTiers:I.data?.metalTiers||[]})}finally{b(!1)}})()},[e,I]);let q=(0,l.useMemo)(()=>{let e;return a=>{clearTimeout(e),e=setTimeout(()=>{localStorage.setItem("ai-enroller-draft-plan",JSON.stringify(a))},500)}},[]),E=(0,l.useMemo)(()=>{let e;return a=>{if(clearTimeout(e),!a.trim()){P({isChecking:!1,isDuplicate:!1});return}P(e=>({...e,isChecking:!0,error:void 0})),e=setTimeout(async()=>{try{let e=await (0,j.nR)(a);e.success&&e.data?P({isChecking:!1,isDuplicate:e.data.isDuplicate,existingPlan:e.data.existingPlan}):P({isChecking:!1,isDuplicate:!1,error:e.error||"Failed to check for duplicates"})}catch(e){P({isChecking:!1,isDuplicate:!1,error:"Error checking for duplicates"})}},800)}},[]),A=(0,l.useMemo)(()=>{let e;return a=>{if(clearTimeout(e),!a.trim()){k({isChecking:!1,isDuplicate:!1});return}k(e=>({...e,isChecking:!0,error:void 0})),e=setTimeout(async()=>{try{let e=await (0,j.yW)(a);e.success&&e.data?k({isChecking:!1,isDuplicate:e.data.isDuplicate,existingPlan:e.data.existingPlan}):k({isChecking:!1,isDuplicate:!1,error:e.error||"Failed to check for duplicates"})}catch(e){k({isChecking:!1,isDuplicate:!1,error:"Error checking for duplicates"})}},800)}},[]);(0,l.useEffect)(()=>{T.planName&&T.planName.trim()&&(console.log("\uD83D\uDD04 Form data changed: Checking plan name for duplicates"),E(T.planName))},[T.planName,E]),(0,l.useEffect)(()=>{T.planCode&&T.planCode.trim()&&(console.log("\uD83D\uDD04 Form data changed: Checking plan code for duplicates"),A(T.planCode))},[T.planCode,A]),(0,l.useEffect)(()=>{let e=null,a=(a,r)=>{t();let i=a.getBoundingClientRect(),s=i.left+i.width/2-140;s<10&&(s=10),s+280>window.innerWidth-10&&(s=window.innerWidth-280-10);let l=i.top,n=window.innerHeight-i.bottom,o=l<120&&n>120,c=document.createElement("div");c.className="custom-tooltip",c.textContent=r,c.style.cssText=`
        position: fixed;
        ${o?`top: ${i.bottom+4}px;`:`bottom: ${window.innerHeight-i.top+4}px;`}
        left: ${s}px;
        width: 280px;
        background: #1f2937;
        color: white;
        padding: 0.75rem 1rem;
        border-radius: 0.5rem;
        font-size: 0.8rem;
        font-weight: 400;
        line-height: 1.4;
        text-align: left;
        white-space: normal;
        word-wrap: break-word;
        hyphens: auto;
        box-shadow: 0 8px 16px rgba(0, 0, 0, 0.15);
        z-index: 99999;
        pointer-events: none;
        opacity: 0;
        transition: opacity 0.2s ease-in-out;
      `;let d=document.createElement("div");d.className="custom-tooltip-arrow",d.style.cssText=`
        position: fixed;
        ${o?`top: ${i.bottom-2}px;`:`bottom: ${window.innerHeight-i.top-2}px;`}
        left: ${i.left+i.width/2-6}px;
        width: 0;
        height: 0;
        border-left: 6px solid transparent;
        border-right: 6px solid transparent;
        ${o?"border-bottom: 6px solid #1f2937;":"border-top: 6px solid #1f2937;"}
        z-index: 100000;
        pointer-events: none;
        opacity: 0;
        transition: opacity 0.2s ease-in-out;
      `,document.body.appendChild(c),document.body.appendChild(d),requestAnimationFrame(()=>{c.style.opacity="1",d.style.opacity="1"}),(e=c).arrow=d},t=()=>{e&&(e.remove(),e.arrow&&e.arrow.remove(),e=null)},r=e=>{let t=e.target,r=t.getAttribute("data-tooltip");r&&a(t,r)},i=()=>{t()},s=document.querySelectorAll(".tooltip-icon[data-tooltip]");return s.forEach(e=>{e.addEventListener("mouseenter",r),e.addEventListener("mouseleave",i)}),()=>{t(),s.forEach(e=>{e.removeEventListener("mouseenter",r),e.removeEventListener("mouseleave",i)})}},[a]);let M=(e,a)=>{console.log("handleInputChange called:",{field:e,value:a,currentFormData:T});let t={...T,[e]:a};console.log("Updated form data:",t),S(t),setTimeout(()=>{console.log("Form data after setState (delayed):",T)},100),q(t)},F=e=>{if(e){let a=Array.from(e);S(e=>({...e,documents:[...e.documents,...a]}))}},_=e=>{S(a=>({...a,documents:a.documents.filter((a,t)=>t!==e)}))},O=()=>{a<5&&t(a+1)},R=()=>{a>1&&t(a-1)},H=async()=>{try{let e=T.coverageCategory,a={planName:T.planName,planCode:T.planCode,carrier:T.carrier,coverageType:e,coverageSubTypes:[T.coverageType],planType:T.planType,metalTier:T.metalTier,description:T.description,highlights:T.highlights.filter(e=>""!==e.trim()),informativeLinks:T.videoUrl?[T.videoUrl]:[],carrierId:T.carrier,isTemplate:!1,status:"Active"};console.log("Creating plan with data:",a),console.log("Form data mapping:"),console.log("- Coverage Category (formData.coverageCategory):",T.coverageCategory,"→ coverageType"),console.log("- Coverage Type (formData.coverageType):",T.coverageType,"→ coverageSubTypes"),console.log("- Carrier ID:",T.carrier),console.log("- Plan Type:",T.planType),console.log("- Available carriers:",N?.carriers);let r=await (0,j.he)(a);if(r.success&&r.data){let e=r.data.plan;if(T.documents.length>0){let a=await (0,j.Op)(e._id,T.documents);a.success||console.warn("Failed to upload some documents:",a.error)}u(e),localStorage.removeItem("ai-enroller-draft-plan"),t(5)}else throw Error(r.error||"Failed to create plan")}catch(e){alert(`Error creating plan: ${e instanceof Error?e.message:"Please try again."}`)}},U=()=>{S(e=>({...e,highlights:[...e.highlights,""]}))},B=(e,a)=>{S(t=>({...t,highlights:t.highlights.map((t,r)=>r===e?a:t)}))},W=e=>{T.highlights.length>1&&S(a=>({...a,highlights:a.highlights.filter((a,t)=>t!==e)}))};(0,l.useMemo)(()=>!0,[]);let Z=(0,l.useMemo)(()=>T.planName&&T.planCode&&T.carrier&&T.planType&&T.coverageCategory&&T.coverageType&&!C.isDuplicate&&!C.isChecking&&!D.isDuplicate&&!D.isChecking,[T.planName,T.planCode,T.carrier,T.planType,T.coverageCategory,T.coverageType,C.isDuplicate,C.isChecking,D.isDuplicate,D.isChecking]),$=(0,l.useMemo)(()=>T.description&&T.highlights.some(e=>""!==e.trim()),[T.description,T.highlights]),L=()=>(0,r.jsxs)("div",{className:"form-section",children:[r.jsx("div",{className:"form-header",children:(0,r.jsxs)("div",{className:"form-header-content",children:[r.jsx("div",{className:"gradient-icon",children:r.jsx(f.GJ3,{size:20})}),r.jsx("h3",{children:"Plan Documents"})]})}),r.jsx("div",{className:"form-content",children:(0,r.jsxs)("div",{className:"form-group",children:[(0,r.jsxs)("label",{children:["Plan Documents (Optional)",r.jsx(m.Z,{title:"Upload documents related to this plan such as brochures, benefit summaries, or plan details (PDF, DOC, TXT, or Image files)",placement:"top",arrow:!0,enterDelay:300,leaveDelay:200,slotProps:{tooltip:{sx:{bgcolor:"#374151",color:"white",fontSize:"0.75rem",maxWidth:"250px",zIndex:9999,"& .MuiTooltip-arrow":{color:"#374151"}}}},children:r.jsx("span",{children:r.jsx(g,{className:"form-tooltip-icon",size:16,title:"Upload documents related to this plan such as brochures, benefit summaries, or plan details (PDF, DOC, TXT, or Image files)"})})})]}),(0,r.jsxs)("div",{className:"file-upload-area",children:[r.jsx("input",{type:"file",id:"documents",multiple:!0,accept:".pdf,.doc,.docx,.txt,.jpg,.jpeg,.png",onChange:e=>F(e.target.files),className:"file-input"}),(0,r.jsxs)("label",{htmlFor:"documents",className:"file-upload-label",children:[r.jsx(v.qX3,{size:20}),r.jsx("span",{children:"Click to upload documents"}),r.jsx("small",{children:"PDF, DOC, TXT, or Image files"})]})]}),T.documents.length>0&&r.jsx("div",{className:"uploaded-files",children:T.documents.map((e,a)=>(0,r.jsxs)("div",{className:"uploaded-file",children:[r.jsx("div",{className:"gradient-icon-small",children:r.jsx(v.vrJ,{size:16})}),r.jsx("span",{className:"file-name",children:e.name}),(0,r.jsxs)("span",{className:"file-size",children:["(",(e.size/1024).toFixed(1)," KB)"]}),r.jsx("button",{type:"button",className:"remove-file",onClick:()=>_(a),title:"Remove this document",children:r.jsx(v.fMW,{size:14})})]},a))})]})}),r.jsx("div",{className:"form-navigation",style:{justifyContent:"flex-end"},children:r.jsx("button",{className:"nav-btn primary enabled",onClick:O,children:"Continue to Basic Info"})})]}),G=()=>(0,r.jsxs)("div",{className:"form-section",children:[r.jsx("div",{className:"form-header",children:(0,r.jsxs)("div",{className:"form-header-content",children:[r.jsx("div",{className:"gradient-icon",children:r.jsx(f.kAf,{size:20})}),r.jsx("h3",{children:"Basic Details"})]})}),(0,r.jsxs)("div",{className:"form-content",children:[(0,r.jsxs)("div",{className:"form-group",children:[(0,r.jsxs)("label",{htmlFor:"coverageCategory",children:["Coverage Category",r.jsx(m.Z,{title:"Select the main category of coverage this plan provides (e.g., Medical, Dental, Vision)",placement:"top",arrow:!0,enterDelay:300,leaveDelay:200,slotProps:{tooltip:{sx:{bgcolor:"#374151",color:"white",fontSize:"0.75rem",maxWidth:"250px",zIndex:9999,"& .MuiTooltip-arrow":{color:"#374151"}}}},children:r.jsx("span",{children:r.jsx(g,{className:"form-tooltip-icon",size:16,title:"Select the main category of coverage this plan provides (e.g., Medical, Dental, Vision)"})})})]}),(0,r.jsxs)("select",{id:"coverageCategory",value:T.coverageCategory,onChange:e=>{console.log("Coverage category dropdown changed:",e.target.value),console.log("Current formData.coverageCategory before change:",T.coverageCategory);let a={...T,coverageCategory:e.target.value,coverageType:""};console.log("Updated form data (combined):",a),S(a),q(a)},title:"Choose the main category of benefits",children:[r.jsx("option",{value:"",children:"Select coverage category"}),(N?.coverageCategories||[]).map(e=>(console.log("Rendering category option:",e),r.jsx("option",{value:e,children:e},e)))]})]}),(0,r.jsxs)("div",{className:"form-group",children:[(0,r.jsxs)("label",{htmlFor:"coverageType",children:["Coverage Type",r.jsx(m.Z,{title:"Select the specific type of coverage within the category (e.g., PPO, HMO, EPO for Medical)",placement:"top",arrow:!0,enterDelay:300,leaveDelay:200,slotProps:{tooltip:{sx:{bgcolor:"#374151",color:"white",fontSize:"0.75rem",maxWidth:"250px",zIndex:9999,"& .MuiTooltip-arrow":{color:"#374151"}}}},children:r.jsx("span",{children:r.jsx(g,{className:"form-tooltip-icon",size:16,title:"Select the specific type of coverage within the category (e.g., PPO, HMO, EPO for Medical)"})})})]}),(0,r.jsxs)("select",{id:"coverageType",value:T.coverageType,onChange:e=>M("coverageType",e.target.value),disabled:!T.coverageCategory,title:"Choose the specific type of benefits covered",style:{backgroundColor:T.coverageCategory?"white":"#f9fafb",cursor:T.coverageCategory?"pointer":"not-allowed"},children:[r.jsx("option",{value:"",children:"Select coverage type"}),T.coverageCategory&&N?.coverageMap?.[T.coverageCategory]?.map(e=>r.jsx("option",{value:e,children:e},e))||[]]})]}),(0,r.jsxs)("div",{className:"form-group",children:[(0,r.jsxs)("label",{htmlFor:"carrier",children:["Carrier",r.jsx(m.Z,{title:"Select the insurance carrier that provides this plan (e.g., Blue Shield, Kaiser)",placement:"top",arrow:!0,enterDelay:300,leaveDelay:200,slotProps:{tooltip:{sx:{bgcolor:"#374151",color:"white",fontSize:"0.75rem",maxWidth:"250px",zIndex:9999,"& .MuiTooltip-arrow":{color:"#374151"}}}},children:r.jsx("span",{children:r.jsx(g,{className:"form-tooltip-icon",size:16,title:"Select the insurance carrier that provides this plan (e.g., Blue Shield, Kaiser)"})})})]}),(0,r.jsxs)("select",{id:"carrier",value:T.carrier,onChange:e=>M("carrier",e.target.value),children:[r.jsx("option",{value:"",children:"Select carrier"}),N?.carriers?.map(e=>r.jsx("option",{value:e._id,children:e.displayName||e.carrierName},e._id))||[]]})]}),(0,r.jsxs)("div",{className:"form-group",children:[(0,r.jsxs)("label",{htmlFor:"planName",children:["Plan Name",r.jsx(m.Z,{title:"Enter a clear, descriptive name that helps identify this plan (e.g., Blue Shield PPO 500). We'll check for duplicates automatically.",placement:"top",arrow:!0,enterDelay:300,leaveDelay:200,slotProps:{tooltip:{sx:{bgcolor:"#374151",color:"white",fontSize:"0.75rem",maxWidth:"250px",zIndex:9999,"& .MuiTooltip-arrow":{color:"#374151"}}}},children:r.jsx("span",{children:r.jsx(g,{className:"form-tooltip-icon",size:16,title:"Enter a clear, descriptive name that helps identify this plan (e.g., Blue Shield PPO 500). We'll check for duplicates automatically."})})})]}),(0,r.jsxs)("div",{style:{position:"relative",width:"100%"},children:[r.jsx("input",{type:"text",id:"planName",placeholder:"e.g. Blue Shield PPO 500",value:T.planName,onChange:e=>M("planName",e.target.value),style:{width:"100%",borderColor:C.isDuplicate?"#ef4444":C.isChecking?"#f59e0b":T.planName&&!C.isDuplicate?"#10b981":"#d1d5db",paddingRight:"40px"}}),(0,r.jsxs)("div",{style:{position:"absolute",right:"12px",top:"50%",transform:"translateY(-50%)",display:"flex",alignItems:"center",fontSize:"14px",fontFamily:"sans-serif"},children:[C.isChecking&&r.jsx("div",{style:{width:"16px",height:"16px",border:"2px solid #f59e0b",borderTop:"2px solid transparent",borderRadius:"50%",animation:"spin 1s linear infinite"}}),!C.isChecking&&T.planName&&!C.isDuplicate&&r.jsx("span",{style:{color:"#10b981",fontWeight:"600",fontFamily:"sans-serif"},children:"✓"}),C.isDuplicate&&r.jsx("span",{style:{color:"#ef4444",fontWeight:"600",fontFamily:"sans-serif"},children:"✗"})]})]}),C.isDuplicate&&C.existingPlan&&(0,r.jsxs)("div",{style:{marginTop:"8px",padding:"8px 12px",backgroundColor:"#fef2f2",border:"1px solid #fecaca",borderRadius:"6px",fontSize:"13px",color:"#dc2626",lineHeight:"1.4",fontFamily:"sans-serif"},children:[r.jsx("strong",{children:"Plan name already exists:"}),' "',C.existingPlan.planName,'"',C.existingPlan.planCode&&` (${C.existingPlan.planCode})`,r.jsx("br",{}),r.jsx("span",{style:{fontSize:"12px",color:"#7f1d1d",fontFamily:"sans-serif"},children:"Please choose a different name."})]}),C.error&&(0,r.jsxs)("div",{style:{marginTop:"8px",padding:"8px 12px",backgroundColor:"#fef3cd",border:"1px solid #fde68a",borderRadius:"6px",fontSize:"13px",color:"#92400e",lineHeight:"1.4",fontFamily:"sans-serif"},children:[r.jsx("strong",{children:"Warning:"})," ",C.error]}),T.planName&&!C.isChecking&&!C.isDuplicate&&!C.error&&r.jsx("div",{style:{marginTop:"8px",padding:"8px 12px",backgroundColor:"#f0fdf4",border:"1px solid #bbf7d0",borderRadius:"6px",fontSize:"13px",color:"#166534",lineHeight:"1.4",fontFamily:"sans-serif"},children:"✓ Plan name is available"})]}),(0,r.jsxs)("div",{className:"form-group",children:[(0,r.jsxs)("label",{htmlFor:"planCode",children:["Plan Code",r.jsx(m.Z,{title:"Unique identifier for this plan used in systems and reports (e.g., BS-PPO-500). We'll check for duplicates automatically.",placement:"top",arrow:!0,enterDelay:300,leaveDelay:200,slotProps:{tooltip:{sx:{bgcolor:"#374151",color:"white",fontSize:"0.75rem",maxWidth:"250px",zIndex:9999,"& .MuiTooltip-arrow":{color:"#374151"}}}},children:r.jsx("span",{children:r.jsx(g,{className:"form-tooltip-icon",size:16,title:"Unique identifier for this plan used in systems and reports (e.g., BS-PPO-500). We'll check for duplicates automatically."})})})]}),(0,r.jsxs)("div",{style:{position:"relative",width:"100%"},children:[r.jsx("input",{type:"text",id:"planCode",placeholder:"e.g. BS-PPO-500",value:T.planCode,onChange:e=>M("planCode",e.target.value),style:{width:"100%",borderColor:D.isDuplicate?"#ef4444":D.isChecking?"#f59e0b":T.planCode&&!D.isDuplicate?"#10b981":"#d1d5db",paddingRight:"40px"}}),(0,r.jsxs)("div",{style:{position:"absolute",right:"12px",top:"50%",transform:"translateY(-50%)",display:"flex",alignItems:"center",fontSize:"14px",fontFamily:"sans-serif"},children:[D.isChecking&&r.jsx("div",{style:{width:"16px",height:"16px",border:"2px solid #f59e0b",borderTop:"2px solid transparent",borderRadius:"50%",animation:"spin 1s linear infinite"}}),!D.isChecking&&T.planCode&&!D.isDuplicate&&r.jsx("span",{style:{color:"#10b981",fontWeight:"600",fontFamily:"sans-serif"},children:"✓"}),D.isDuplicate&&r.jsx("span",{style:{color:"#ef4444",fontWeight:"600",fontFamily:"sans-serif"},children:"✗"})]})]}),D.isDuplicate&&D.existingPlan&&(0,r.jsxs)("div",{style:{marginTop:"8px",padding:"8px 12px",backgroundColor:"#fef2f2",border:"1px solid #fecaca",borderRadius:"6px",fontSize:"13px",color:"#dc2626",lineHeight:"1.4",fontFamily:"sans-serif"},children:[r.jsx("strong",{children:"Plan code already exists:"}),' "',D.existingPlan.planCode,'"',D.existingPlan.planName&&` (${D.existingPlan.planName})`,r.jsx("br",{}),r.jsx("span",{style:{fontSize:"12px",color:"#7f1d1d",fontFamily:"sans-serif"},children:"Please choose a different code."})]}),D.error&&(0,r.jsxs)("div",{style:{marginTop:"8px",padding:"8px 12px",backgroundColor:"#fef3cd",border:"1px solid #fde68a",borderRadius:"6px",fontSize:"13px",color:"#92400e",lineHeight:"1.4",fontFamily:"sans-serif"},children:[r.jsx("strong",{children:"Warning:"})," ",D.error]}),T.planCode&&!D.isChecking&&!D.isDuplicate&&!D.error&&r.jsx("div",{style:{marginTop:"8px",padding:"8px 12px",backgroundColor:"#f0fdf4",border:"1px solid #bbf7d0",borderRadius:"6px",fontSize:"13px",color:"#166534",lineHeight:"1.4",fontFamily:"sans-serif"},children:"✓ Plan code is available"})]}),(0,r.jsxs)("div",{className:"form-group",children:[(0,r.jsxs)("label",{htmlFor:"planType",children:["Plan Type",r.jsx(m.Z,{title:"Choose the type of health plan structure (PPO, HMO, EPO, POS, etc.)",placement:"top",arrow:!0,enterDelay:300,leaveDelay:200,slotProps:{tooltip:{sx:{bgcolor:"#374151",color:"white",fontSize:"0.75rem",maxWidth:"250px",zIndex:9999,"& .MuiTooltip-arrow":{color:"#374151"}}}},children:r.jsx("span",{children:r.jsx(g,{className:"form-tooltip-icon",size:16,title:"Choose the type of health plan structure (PPO, HMO, EPO, POS, etc.)"})})})]}),(0,r.jsxs)("select",{id:"planType",value:T.planType,onChange:e=>M("planType",e.target.value),title:"Select the plan structure (PPO, HMO, etc.)",children:[r.jsx("option",{value:"",children:"Select type"}),N?.planTypes?.map(e=>r.jsx("option",{value:e,children:e},e))||[]]})]}),(0,r.jsxs)("div",{className:"form-group",children:[(0,r.jsxs)("label",{htmlFor:"metalTier",children:["Metal Tier (Optional)",r.jsx(m.Z,{title:"Choose the coverage level (Bronze, Silver, Gold, Platinum). This field is optional and typically applies to medical plans.",placement:"top",arrow:!0,enterDelay:300,leaveDelay:200,slotProps:{tooltip:{sx:{bgcolor:"#374151",color:"white",fontSize:"0.75rem",maxWidth:"250px",zIndex:9999,"& .MuiTooltip-arrow":{color:"#374151"}}}},children:r.jsx("span",{children:r.jsx(g,{className:"form-tooltip-icon",size:16,title:"Choose the coverage level (Bronze, Silver, Gold, Platinum). This field is optional and typically applies to medical plans."})})})]}),(0,r.jsxs)("select",{id:"metalTier",value:T.metalTier,onChange:e=>M("metalTier",e.target.value),title:"Choose the coverage level (Bronze, Silver, Gold, Platinum) - Optional",children:[r.jsx("option",{value:"",children:"Select tier (optional)"}),N?.metalTiers?.map(e=>r.jsx("option",{value:e,children:e},e))||[]]})]})]}),(0,r.jsxs)("div",{className:"form-navigation",children:[r.jsx(o.default,{href:"/ai-enroller",prefetch:!0,children:(0,r.jsxs)("button",{className:"nav-btn secondary",children:[r.jsx(v.Tsu,{size:16}),"Back to Main"]})}),r.jsx("button",{className:`nav-btn primary ${Z?"enabled":"disabled"}`,onClick:O,disabled:!Z,title:"Continue to description and video",children:"Continue to Description"})]})]}),J=()=>(0,r.jsxs)("div",{className:"form-section",children:[r.jsx("div",{className:"form-header",children:(0,r.jsxs)("div",{className:"form-header-content",children:[r.jsx("div",{className:"gradient-icon",children:r.jsx(f.yMK,{size:20})}),r.jsx("h3",{children:"Description & Video"})]})}),(0,r.jsxs)("div",{className:"form-content",children:[(0,r.jsxs)("div",{className:"form-group",children:[(0,r.jsxs)("label",{htmlFor:"videoUrl",children:["Video URL (Optional)",r.jsx(m.Z,{title:"Add a YouTube or Vimeo URL to help explain plan benefits and features",placement:"top",arrow:!0,enterDelay:300,leaveDelay:200,slotProps:{tooltip:{sx:{bgcolor:"#374151",color:"white",fontSize:"0.75rem",maxWidth:"250px",zIndex:9999,"& .MuiTooltip-arrow":{color:"#374151"}}}},children:r.jsx("span",{children:r.jsx(g,{className:"form-tooltip-icon",size:16,title:"Add a YouTube or Vimeo URL to help explain plan benefits and features"})})})]}),r.jsx("input",{type:"url",id:"videoUrl",placeholder:"e.g. https://youtube.com/watch?v=...",value:T.videoUrl,onChange:e=>M("videoUrl",e.target.value)}),r.jsx("small",{className:"field-hint",children:"Add a video to help explain plan benefits and features"})]}),(0,r.jsxs)("div",{className:"form-group",children:[(0,r.jsxs)("label",{htmlFor:"description",children:["Plan Description",r.jsx(m.Z,{title:"Provide a detailed description of the plan benefits, coverage, and key features",placement:"top",arrow:!0,enterDelay:300,leaveDelay:200,slotProps:{tooltip:{sx:{bgcolor:"#374151",color:"white",fontSize:"0.75rem",maxWidth:"250px",zIndex:9999,"& .MuiTooltip-arrow":{color:"#374151"}}}},children:r.jsx("span",{children:r.jsx(g,{className:"form-tooltip-icon",size:16,title:"Provide a detailed description of the plan benefits, coverage, and key features"})})})]}),r.jsx("textarea",{id:"description",placeholder:"Describe the plan benefits and features...",value:T.description,onChange:e=>M("description",e.target.value),rows:4}),r.jsx("small",{className:"field-hint",children:"Describe the key benefits, coverage details, and what makes this plan unique"})]}),(0,r.jsxs)("div",{className:"form-group",children:[(0,r.jsxs)("label",{children:["Plan Highlights",r.jsx(m.Z,{title:"Add key selling points and highlights that make this plan attractive (e.g., Low deductible, Nationwide network, No referrals needed)",placement:"top",arrow:!0,enterDelay:300,leaveDelay:200,slotProps:{tooltip:{sx:{bgcolor:"#374151",color:"white",fontSize:"0.75rem",maxWidth:"250px",zIndex:9999,"& .MuiTooltip-arrow":{color:"#374151"}}}},children:r.jsx("span",{children:r.jsx(g,{className:"form-tooltip-icon",size:16,title:"Add key selling points and highlights that make this plan attractive (e.g., Low deductible, Nationwide network, No referrals needed)"})})})]}),r.jsx("small",{className:"field-hint",children:"Add the most important features that make this plan attractive"}),T.highlights.map((e,a)=>(0,r.jsxs)("div",{className:"highlight-input",children:[r.jsx("input",{type:"text",placeholder:"e.g. Low deductible, Nationwide network",value:e,onChange:e=>B(a,e.target.value),title:"Enter a key benefit or feature"}),T.highlights.length>1&&r.jsx("button",{type:"button",className:"remove-highlight",onClick:()=>W(a),title:"Remove this highlight",children:r.jsx(v.fMW,{size:14})})]},a)),(0,r.jsxs)("button",{type:"button",className:"add-highlight",onClick:U,title:"Add another highlight",children:[r.jsx(v.r7I,{size:16}),"Add Highlight"]})]})]}),(0,r.jsxs)("div",{className:"form-navigation",children:[(0,r.jsxs)("button",{className:"nav-btn secondary",onClick:R,title:"Go back to basic information",children:[r.jsx(v.Tsu,{size:16}),"Back"]}),r.jsx("button",{className:`nav-btn primary ${$?"enabled":"disabled"}`,onClick:O,disabled:!$,title:"Continue to preview your plan",children:"Preview Plan"})]})]}),V=()=>(0,r.jsxs)("div",{className:"form-section",children:[(0,r.jsxs)("div",{className:"form-header",children:[(0,r.jsxs)("div",{className:"form-header-content",children:[r.jsx("div",{className:"gradient-icon",children:r.jsx(f.GH$,{size:20})}),r.jsx("h3",{children:"AI-Powered Plan Preview"})]}),(0,r.jsxs)("div",{className:"ready-badge",title:"All required information has been provided",children:[r.jsx("div",{children:r.jsx(v.PjL,{size:16})}),"Ready to Create"]})]}),(0,r.jsxs)("div",{className:"review-content",children:[(0,r.jsxs)("div",{className:"review-section",children:[(0,r.jsxs)("div",{className:"review-section-header",children:[r.jsx("div",{className:"gradient-icon-small",children:r.jsx(f.kAf,{size:18})}),r.jsx("h4",{children:"Plan Information"})]}),(0,r.jsxs)("div",{className:"review-items",children:[(0,r.jsxs)("div",{className:"review-item",children:[r.jsx("span",{className:"review-label",title:"The name of this plan",children:"Plan Name:"}),r.jsx("span",{className:"review-value",children:T.planName})]}),(0,r.jsxs)("div",{className:"review-item",children:[r.jsx("span",{className:"review-label",title:"Unique identifier for this plan",children:"Plan Code:"}),r.jsx("span",{className:"review-value plan-code",children:T.planCode})]}),(0,r.jsxs)("div",{className:"review-item",children:[r.jsx("span",{className:"review-label",title:"Insurance carrier providing this plan",children:"Carrier:"}),r.jsx("span",{className:"review-value",children:N?.carriers?.find(e=>e._id===T.carrier)?.displayName||N?.carriers?.find(e=>e._id===T.carrier)?.carrierName||"Unknown"})]}),(0,r.jsxs)("div",{className:"review-item",children:[r.jsx("span",{className:"review-label",title:"Type of health plan structure",children:"Plan Type:"}),r.jsx("span",{className:"review-value",children:T.planType})]}),(0,r.jsxs)("div",{className:"review-item",children:[r.jsx("span",{className:"review-label",title:"Type of coverage provided",children:"Coverage Type:"}),r.jsx("span",{className:"review-value",children:T.coverageType})]}),(0,r.jsxs)("div",{className:"review-item",children:[r.jsx("span",{className:"review-label",title:"Metal tier level indicating coverage level",children:"Metal Tier:"}),r.jsx("span",{className:"review-value metal-tier",children:T.metalTier})]})]})]}),(T.videoUrl||T.documents.length>0)&&(0,r.jsxs)("div",{className:"review-section",children:[(0,r.jsxs)("div",{className:"review-section-header",children:[r.jsx("div",{className:"gradient-icon-small",children:r.jsx(v.vrJ,{size:18})}),r.jsx("h4",{children:"Media & Documents"})]}),(0,r.jsxs)("div",{className:"review-items",children:[T.videoUrl&&(0,r.jsxs)("div",{className:"review-item full-width",children:[r.jsx("span",{className:"review-label",title:"Video URL for plan explanation",children:"Video URL:"}),r.jsx("a",{href:T.videoUrl,target:"_blank",rel:"noopener noreferrer",className:"review-link",children:T.videoUrl})]}),T.documents.length>0&&(0,r.jsxs)("div",{className:"review-item full-width",children:[r.jsx("span",{className:"review-label",title:"Documents uploaded for this plan",children:"Documents:"}),r.jsx("div",{className:"review-documents",children:T.documents.map((e,a)=>(0,r.jsxs)("div",{className:"review-document",children:[r.jsx("div",{className:"gradient-icon-small",children:r.jsx(v.vrJ,{size:16})}),r.jsx("span",{children:e.name}),(0,r.jsxs)("small",{children:["(",(e.size/1024).toFixed(1)," KB)"]})]},a))})]})]})]}),(0,r.jsxs)("div",{className:"review-section",children:[(0,r.jsxs)("div",{className:"review-section-header",children:[r.jsx("div",{className:"gradient-icon-small",children:r.jsx(f.jNQ,{size:18})}),r.jsx("h4",{children:"Description & Highlights"})]}),(0,r.jsxs)("div",{className:"review-items",children:[(0,r.jsxs)("div",{className:"review-item full-width",children:[r.jsx("span",{className:"review-label",title:"Detailed plan description",children:"Description:"}),r.jsx("p",{className:"review-description",children:T.description})]}),(0,r.jsxs)("div",{className:"review-item full-width",children:[r.jsx("span",{className:"review-label",title:"Key plan features and benefits",children:"Highlights:"}),r.jsx("ul",{className:"review-highlights",children:T.highlights.filter(e=>e.trim()).map((e,a)=>r.jsx("li",{children:e},a))})]})]})]}),(0,r.jsxs)("div",{className:"create-confirmation",children:[r.jsx("div",{className:"confirmation-icon",children:r.jsx("div",{className:"gradient-icon",children:r.jsx(f.GH$,{size:24})})}),(0,r.jsxs)("div",{className:"confirmation-text",children:[r.jsx("h4",{children:"Ready to Create Plan"}),(0,r.jsxs)("p",{children:['Your new plan "',T.planName,'" will be added to your catalog and available for assignment to employer groups.']})]})]})]}),(0,r.jsxs)("div",{className:"form-navigation",children:[(0,r.jsxs)("button",{className:"nav-btn secondary",onClick:R,title:"Go back to description and video",children:[r.jsx(v.Tsu,{size:16}),"Back"]}),r.jsx("button",{className:"nav-btn primary enabled",onClick:H,title:"Create this plan and add it to your catalog",children:"Create Plan"})]})]}),Y=()=>r.jsx("div",{className:"form-section success-section",children:(0,r.jsxs)("div",{className:"success-content",children:[r.jsx("div",{className:"success-icon",children:r.jsx("div",{className:"gradient-icon-large",children:r.jsx(f.euf,{size:32})})}),r.jsx("h3",{children:"Plan Created Successfully!"}),(0,r.jsxs)("p",{children:["Your plan '",T.planName,"' is now available in your catalog."]}),i&&(0,r.jsxs)("div",{className:"plan-details-card",children:[(0,r.jsxs)("div",{className:"plan-details-header",children:[r.jsx("div",{className:"gradient-icon",children:r.jsx(f.kAf,{size:15})}),r.jsx("h4",{children:"Plan Details"})]}),(0,r.jsxs)("div",{className:"plan-details-content",children:[(0,r.jsxs)("div",{className:"plan-detail-item",children:[r.jsx("span",{className:"detail-label",children:"Plan ID:"}),r.jsx("span",{className:"detail-value plan-id",children:i._id})]}),(0,r.jsxs)("div",{className:"plan-detail-item",children:[r.jsx("span",{className:"detail-label",children:"Plan Code:"}),r.jsx("span",{className:"detail-value plan-code",children:i.planCode})]}),(0,r.jsxs)("div",{className:"plan-detail-item",children:[r.jsx("span",{className:"detail-label",children:"Status:"}),r.jsx("span",{className:"detail-value status-active",children:i.status||"Active"})]}),(0,r.jsxs)("div",{className:"plan-detail-item",children:[r.jsx("span",{className:"detail-label",children:"Created:"}),r.jsx("span",{className:"detail-value",children:i.createdAt?new Date(i.createdAt).toLocaleString():"Just now"})]}),(0,r.jsxs)("div",{className:"plan-detail-item",children:[r.jsx("span",{className:"detail-label",children:"Carrier:"}),r.jsx("span",{className:"detail-value",children:N?.carriers.find(e=>e._id===(i.carrierId||i.carrier))?.displayName||N?.carriers.find(e=>e._id===(i.carrierId||i.carrier))?.carrierName||"Unknown"})]})]})]}),(0,r.jsxs)("div",{className:"success-actions",children:[r.jsx(o.default,{href:"/ai-enroller/manage-groups",prefetch:!0,children:r.jsx("button",{className:"nav-btn primary",children:"Assign to Group Now"})}),r.jsx(o.default,{href:"/ai-enroller",prefetch:!0,children:r.jsx("button",{className:"nav-btn secondary",children:"Back to Main Menu"})})]})]})});if(y||!N)return r.jsx("div",{className:"create-plan-wrapper",children:r.jsx("div",{className:"create-plan-page",children:r.jsx(()=>(0,r.jsxs)("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",minHeight:"400px",flexDirection:"column",gap:"16px"},className:"jsx-ff161281ed666c63",children:[r.jsx("div",{style:{width:"40px",height:"40px",border:"3px solid #f3f4f6",borderTop:"3px solid #3b82f6",borderRadius:"50%",animation:"spin 1s linear infinite"},className:"jsx-ff161281ed666c63"}),r.jsx("p",{style:{color:"#6b7280",fontSize:"14px",lineHeight:"1.6",fontFamily:"sans-serif"},className:"jsx-ff161281ed666c63",children:"Loading plan data..."}),r.jsx(s(),{id:"ff161281ed666c63",children:"@-webkit-keyframes spin{0%{-webkit-transform:rotate(0deg);transform:rotate(0deg)}100%{-webkit-transform:rotate(360deg);transform:rotate(360deg)}}@-moz-keyframes spin{0%{-moz-transform:rotate(0deg);transform:rotate(0deg)}100%{-moz-transform:rotate(360deg);transform:rotate(360deg)}}@-o-keyframes spin{0%{-o-transform:rotate(0deg);transform:rotate(0deg)}100%{-o-transform:rotate(360deg);transform:rotate(360deg)}}@keyframes spin{0%{-webkit-transform:rotate(0deg);-moz-transform:rotate(0deg);-o-transform:rotate(0deg);transform:rotate(0deg)}100%{-webkit-transform:rotate(360deg);-moz-transform:rotate(360deg);-o-transform:rotate(360deg);transform:rotate(360deg)}}"})]}),{})})});let K=()=>{switch(a){case 1:return{title:"Hi! I'm Brea, your AI Benefits Assistant. Let's create an amazing plan together! \uD83D\uDE0A",subtitle:"I'll help you set up the basic plan information including name, carrier, and coverage details. This should only take a few minutes!"};case 2:return{title:"Great progress! Now let's add some media to make your plan shine ✨",subtitle:"You can upload videos, brochures, or any documents that help explain your plan. Don't worry, this step is completely optional!"};case 3:return{title:"Perfect! Now tell me what makes this plan special \uD83C\uDF1F",subtitle:"Help me understand the key benefits and highlights. I'll use this to create compelling descriptions that really sell your plan!"};case 4:return{title:"Almost there! Let's review everything before we launch your plan \uD83D\uDE80",subtitle:"Take a moment to review all the details. Once you're happy with everything, I'll create your plan and make it available immediately!"};case 5:return{title:"Congratulations! Your plan is now live and ready to go! \uD83C\uDF89",subtitle:"I've successfully created your plan and it's now available for assignment to employer groups. Great work!"};default:return{title:"Hi there! Ready to create something amazing? \uD83D\uDCAB",subtitle:"I'm here to help you build the perfect benefits plan. Let's get started!"}}},X=e=>{switch(e){case 1:return r.jsx("div",{className:"gradient-icon-nav",children:r.jsx(v.vrJ,{})});case 2:default:return r.jsx("div",{className:"gradient-icon-nav",children:r.jsx(f.kAf,{})});case 3:return r.jsx("div",{className:"gradient-icon-nav",children:r.jsx(f.jNQ,{})});case 4:return r.jsx("div",{className:"gradient-icon-nav",children:r.jsx(f.GH$,{})});case 5:return r.jsx("div",{className:"gradient-icon-nav",children:r.jsx(f.euf,{})})}};return r.jsx(d.Z,{children:(0,r.jsxs)("div",{className:"create-plan-wrapper",children:[r.jsx(x.Z,{}),r.jsx(h,{}),(0,r.jsxs)("div",{className:"create-plan-page",children:[r.jsx("div",{className:"progress-header-component",children:(0,r.jsxs)("div",{className:"progress-header",children:[(0,r.jsxs)("div",{className:"progress-title",children:[r.jsx("h1",{className:"page-title",children:"Plan Creation Progress"}),(0,r.jsxs)("span",{className:"subtitle-text",children:[a," of 5"]})]}),r.jsx("div",{className:"progress-bar-container",children:r.jsx("div",{className:"progress-bar-fill",style:{width:`${a/5*100}%`}})}),r.jsx("div",{className:"page-navigation",children:z.map(e=>(0,r.jsxs)("button",{className:`page-nav-item ${e.active?"active":""} ${e.completed?"completed":""}`,onClick:()=>a<5?t(e.number):null,disabled:e.number>a||5===a,children:[r.jsx("span",{className:"nav-icon",children:X(e.number)}),e.title]},e.number))})]})}),r.jsx("div",{className:"ai-assistant-message",children:(0,r.jsxs)("div",{className:"ai-message-content",children:[r.jsx("div",{className:"ai-avatar",children:r.jsx("div",{className:"avatar-circle",children:r.jsx(c.default,{src:"/brea.png",alt:"Brea - AI Assistant",className:"brea-avatar",width:48,height:48,priority:!0})})}),(0,r.jsxs)("div",{className:"chat-bubble",children:[r.jsx("div",{className:"chat-message",children:K().title}),r.jsx("div",{className:"chat-subtitle",children:K().subtitle})]})]})}),r.jsx("div",{className:"main-content",children:r.jsx("div",{className:"form-container",children:(()=>{switch(a){case 1:default:return L();case 2:return G();case 3:return J();case 4:return V();case 5:return Y()}})()})})]})]})})}},92371:(e,a,t)=>{"use strict";t.d(a,{$:()=>i});var r=t(17577);let i=e=>{(0,r.useEffect)(()=>{performance.now();let e=()=>{performance.now()};return"complete"===document.readyState?e():window.addEventListener("load",e),()=>{window.removeEventListener("load",e)}},[e])}},78021:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>s});var r=t(10326);t(17577),t(23824),t(54658);var i=t(43058);function s({children:e}){return r.jsx(i.Z,{children:r.jsx("div",{className:"min-h-screen bg-white",style:{backgroundColor:"white",minHeight:"100vh"},children:e})})}},89009:(e,a,t)=>{"use strict";t.d(a,{GU:()=>s,bR:()=>r,n5:()=>i});let r=()=>"http://localhost:8080",i=()=>{let e="userid1",a="userId",t=localStorage.getItem(e)||localStorage.getItem(a);return(console.log("\uD83D\uDD0D getUserId debug:",{primaryKey:e,altKey:a,primaryValue:localStorage.getItem(e),altValue:localStorage.getItem(a),finalUserId:t}),t)?t:(console.error("❌ User ID not found in localStorage"),"default-user")},s=()=>"https://bot.benosphere.com"},77267:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>r});let r=(0,t(68570).createProxy)(String.raw`C:\beno\project_dev\qharmony-frontend-v1\src\app\ai-enroller\create-plan\page.tsx#default`)},6079:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>r});let r=(0,t(68570).createProxy)(String.raw`C:\beno\project_dev\qharmony-frontend-v1\src\app\ai-enroller\layout.tsx#default`)},54658:()=>{},23824:()=>{}};var a=require("../../../webpack-runtime.js");a.C(e);var t=e=>a(a.s=e),r=a.X(0,[8948,1183,6621,9066,1999,8492,3253,928,434,3351,4326,576,6305,8618],()=>t(12010));module.exports=r})();