(()=>{var e={};e.id=6029,e.ids=[6029],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},27790:e=>{"use strict";e.exports=require("assert")},84770:e=>{"use strict";e.exports=require("crypto")},17702:e=>{"use strict";e.exports=require("events")},92048:e=>{"use strict";e.exports=require("fs")},32615:e=>{"use strict";e.exports=require("http")},35240:e=>{"use strict";e.exports=require("https")},19801:e=>{"use strict";e.exports=require("os")},55315:e=>{"use strict";e.exports=require("path")},76162:e=>{"use strict";e.exports=require("stream")},74026:e=>{"use strict";e.exports=require("string_decoder")},74175:e=>{"use strict";e.exports=require("tty")},17360:e=>{"use strict";e.exports=require("url")},21764:e=>{"use strict";e.exports=require("util")},71568:e=>{"use strict";e.exports=require("zlib")},98061:e=>{"use strict";e.exports=require("node:assert")},92761:e=>{"use strict";e.exports=require("node:async_hooks")},72254:e=>{"use strict";e.exports=require("node:buffer")},40027:e=>{"use strict";e.exports=require("node:console")},6005:e=>{"use strict";e.exports=require("node:crypto")},65714:e=>{"use strict";e.exports=require("node:diagnostics_channel")},15673:e=>{"use strict";e.exports=require("node:events")},88849:e=>{"use strict";e.exports=require("node:http")},42725:e=>{"use strict";e.exports=require("node:http2")},87503:e=>{"use strict";e.exports=require("node:net")},38846:e=>{"use strict";e.exports=require("node:perf_hooks")},39630:e=>{"use strict";e.exports=require("node:querystring")},84492:e=>{"use strict";e.exports=require("node:stream")},31764:e=>{"use strict";e.exports=require("node:tls")},41041:e=>{"use strict";e.exports=require("node:url")},47261:e=>{"use strict";e.exports=require("node:util")},93746:e=>{"use strict";e.exports=require("node:util/types")},24086:e=>{"use strict";e.exports=require("node:worker_threads")},65628:e=>{"use strict";e.exports=require("node:zlib")},81140:(e,t,o)=>{"use strict";o.r(t),o.d(t,{GlobalError:()=>i.a,__next_app__:()=>x,originalPathname:()=>c,pages:()=>p,routeModule:()=>g,tree:()=>d}),o(87616),o(6079),o(33709),o(35866);var n=o(23191),l=o(88716),r=o(37922),i=o.n(r),a=o(95231),s={};for(let e in a)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(s[e]=()=>a[e]);o.d(t,s);let d=["",{children:["ai-enroller",{children:["employee-enrol",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(o.bind(o,87616)),"C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\employee-enrol\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(o.bind(o,6079)),"C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(o.bind(o,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(o.bind(o,33709)),"C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(o.t.bind(o,35866,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(o.bind(o,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],p=["C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\employee-enrol\\page.tsx"],c="/ai-enroller/employee-enrol/page",x={require:o,loadChunk:()=>Promise.resolve()},g=new n.AppPageRouteModule({definition:{kind:l.x.APP_PAGE,page:"/ai-enroller/employee-enrol/page",pathname:"/ai-enroller/employee-enrol",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},16759:(e,t,o)=>{Promise.resolve().then(o.bind(o,74429))},82400:(e,t,o)=>{"use strict";o.d(t,{Z:()=>r});var n=o(51426),l=o(10326);let r=(0,n.Z)((0,l.jsx)("path",{d:"m17 7-1.41 1.41L18.17 11H8v2h10.17l-2.58 2.58L17 17l5-5zM4 5h8V3H4c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h8v-2H4z"}),"Logout")},48260:(e,t,o)=>{"use strict";o.d(t,{Z:()=>C});var n=o(17577),l=o(41135),r=o(88634),i=o(87816),a=o(44823),s=o(91703),d=o(13643),p=o(40955),c=o(2791),x=o(49006),g=o(98139),u=o(54641),f=o(71685),h=o(97898);function m(e){return(0,h.ZP)("MuiIconButton",e)}let y=(0,f.Z)("MuiIconButton",["root","disabled","colorInherit","colorPrimary","colorSecondary","colorError","colorInfo","colorSuccess","colorWarning","edgeStart","edgeEnd","sizeSmall","sizeMedium","sizeLarge","loading","loadingIndicator","loadingWrapper"]);var b=o(10326);let v=e=>{let{classes:t,disabled:o,color:n,edge:l,size:i,loading:a}=e,s={root:["root",a&&"loading",o&&"disabled","default"!==n&&`color${(0,u.Z)(n)}`,l&&`edge${(0,u.Z)(l)}`,`size${(0,u.Z)(i)}`],loadingIndicator:["loadingIndicator"],loadingWrapper:["loadingWrapper"]};return(0,r.Z)(s,m,t)},D=(0,s.default)(x.Z,{name:"MuiIconButton",slot:"Root",overridesResolver:(e,t)=>{let{ownerState:o}=e;return[t.root,o.loading&&t.loading,"default"!==o.color&&t[`color${(0,u.Z)(o.color)}`],o.edge&&t[`edge${(0,u.Z)(o.edge)}`],t[`size${(0,u.Z)(o.size)}`]]}})((0,d.Z)(({theme:e})=>({textAlign:"center",flex:"0 0 auto",fontSize:e.typography.pxToRem(24),padding:8,borderRadius:"50%",color:(e.vars||e).palette.action.active,transition:e.transitions.create("background-color",{duration:e.transitions.duration.shortest}),variants:[{props:e=>!e.disableRipple,style:{"--IconButton-hoverBg":e.vars?`rgba(${e.vars.palette.action.activeChannel} / ${e.vars.palette.action.hoverOpacity})`:(0,a.Fq)(e.palette.action.active,e.palette.action.hoverOpacity),"&:hover":{backgroundColor:"var(--IconButton-hoverBg)","@media (hover: none)":{backgroundColor:"transparent"}}}},{props:{edge:"start"},style:{marginLeft:-12}},{props:{edge:"start",size:"small"},style:{marginLeft:-3}},{props:{edge:"end"},style:{marginRight:-12}},{props:{edge:"end",size:"small"},style:{marginRight:-3}}]})),(0,d.Z)(({theme:e})=>({variants:[{props:{color:"inherit"},style:{color:"inherit"}},...Object.entries(e.palette).filter((0,p.Z)()).map(([t])=>({props:{color:t},style:{color:(e.vars||e).palette[t].main}})),...Object.entries(e.palette).filter((0,p.Z)()).map(([t])=>({props:{color:t},style:{"--IconButton-hoverBg":e.vars?`rgba(${(e.vars||e).palette[t].mainChannel} / ${e.vars.palette.action.hoverOpacity})`:(0,a.Fq)((e.vars||e).palette[t].main,e.palette.action.hoverOpacity)}})),{props:{size:"small"},style:{padding:5,fontSize:e.typography.pxToRem(18)}},{props:{size:"large"},style:{padding:12,fontSize:e.typography.pxToRem(28)}}],[`&.${y.disabled}`]:{backgroundColor:"transparent",color:(e.vars||e).palette.action.disabled},[`&.${y.loading}`]:{color:"transparent"}}))),j=(0,s.default)("span",{name:"MuiIconButton",slot:"LoadingIndicator",overridesResolver:(e,t)=>t.loadingIndicator})(({theme:e})=>({display:"none",position:"absolute",visibility:"visible",top:"50%",left:"50%",transform:"translate(-50%, -50%)",color:(e.vars||e).palette.action.disabled,variants:[{props:{loading:!0},style:{display:"flex"}}]})),C=n.forwardRef(function(e,t){let o=(0,c.i)({props:e,name:"MuiIconButton"}),{edge:n=!1,children:r,className:a,color:s="default",disabled:d=!1,disableFocusRipple:p=!1,size:x="medium",id:u,loading:f=null,loadingIndicator:h,...m}=o,y=(0,i.Z)(u),C=h??(0,b.jsx)(g.Z,{"aria-labelledby":y,color:"inherit",size:16}),S={...o,edge:n,color:s,disabled:d,disableFocusRipple:p,loading:f,loadingIndicator:C,size:x},w=v(S);return(0,b.jsxs)(D,{id:f?y:u,className:(0,l.Z)(w.root,a),centerRipple:!0,focusRipple:!p,disabled:d||f,ref:t,...m,ownerState:S,children:["boolean"==typeof f&&(0,b.jsx)("span",{className:w.loadingWrapper,style:{display:"contents"},children:(0,b.jsx)(j,{className:w.loadingIndicator,ownerState:S,children:f&&C})}),r]})})},41598:(e,t,o)=>{"use strict";o.d(t,{Z:()=>i,f:()=>r});var n=o(71685),l=o(97898);function r(e){return(0,l.ZP)("MuiListItemIcon",e)}let i=(0,n.Z)("MuiListItemIcon",["root","alignItemsFlexStart"])},63948:(e,t)=>{"use strict";var o=Symbol.for("react.element"),n=(Symbol.for("react.portal"),Symbol.for("react.fragment"),Symbol.for("react.strict_mode"),Symbol.for("react.profiler"),Symbol.for("react.provider"),Symbol.for("react.context"),Symbol.for("react.forward_ref"),Symbol.for("react.suspense"),Symbol.for("react.memo"),Symbol.for("react.lazy"),Symbol.iterator,{isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}}),l=Object.assign,r={};function i(e,t,o){this.props=e,this.context=t,this.refs=r,this.updater=o||n}function a(){}function s(e,t,o){this.props=e,this.context=t,this.refs=r,this.updater=o||n}i.prototype.isReactComponent={},i.prototype.setState=function(e,t){if("object"!=typeof e&&"function"!=typeof e&&null!=e)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")},i.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")},a.prototype=i.prototype;var d=s.prototype=new a;d.constructor=s,l(d,i.prototype),d.isPureReactComponent=!0;var p=Object.prototype.hasOwnProperty,c={current:null},x={key:!0,ref:!0,__self:!0,__source:!0};t.createElement=function(e,t,n){var l,r={},i=null,a=null;if(null!=t)for(l in void 0!==t.ref&&(a=t.ref),void 0!==t.key&&(i=""+t.key),t)p.call(t,l)&&!x.hasOwnProperty(l)&&(r[l]=t[l]);var s=arguments.length-2;if(1===s)r.children=n;else if(1<s){for(var d=Array(s),g=0;g<s;g++)d[g]=arguments[g+2];r.children=d}if(e&&e.defaultProps)for(l in s=e.defaultProps)void 0===r[l]&&(r[l]=s[l]);return{$$typeof:o,type:e,key:i,ref:a,props:r,_owner:c.current}}},95746:(e,t,o)=>{"use strict";e.exports=o(63948)},96103:(e,t,o)=>{"use strict";o.d(t,{Z:()=>p});var n=o(10326),l=o(17577),r=o(46226),i=o(25842),a=o(59028),s=o(7330),d=o(32049);let p=({isOpen:e,onClose:t})=>{let o=(0,i.I0)(),p=(0,l.useRef)(null),[c,x]=(0,l.useState)(""),g=(0,i.v9)(e=>(0,d.MP)(e)),u=(0,i.v9)(e=>e.user._id),f=(0,i.v9)(e=>e.user.userProfile),h=(0,i.v9)(e=>e.qHarmonyBot.chatHistory),m=(0,i.v9)(e=>e.qHarmonyBot.isLoading),y=e=>{if(""===e.trim())return;let t={sender:"user",message:e.replace(/\n/g,"<br/>"),timestamp:new Date().toISOString()};o((0,a.Hz)(t)),o((0,a.wt)(!0)),(0,s.b)(o,e,u,g),x("")},b=e=>{if(!e)return"";let[t,o]=e.split(" ");return`${t[0].toUpperCase()}${o?o[0].toUpperCase():""}`},v=()=>{p.current?.scrollIntoView({behavior:"smooth"})};(0,l.useEffect)(()=>{e&&0===h.length&&f.name&&(o((0,a.wt)(!0)),setTimeout(()=>{let e={sender:"bot",message:`Hey ${f.name}, how can I help you with your benefits enrollment today?`,timestamp:new Date().toISOString()};o((0,a.Hz)(e)),o((0,a.wt)(!1))},1e3))},[e,h.length,f.name,o]),(0,l.useEffect)(()=>{v()},[h]);let D=["Explain my plan options","Help me choose coverage","What are the costs?","Enrollment deadline"];return e?n.jsx("div",{style:{position:"fixed",top:0,left:0,right:0,bottom:0,backgroundColor:"rgba(0, 0, 0, 0.5)",zIndex:1e4,display:"flex",alignItems:"center",justifyContent:"center",padding:"20px"},children:(0,n.jsxs)("div",{style:{backgroundColor:"#f6f8fc",borderRadius:"12px",width:"100%",maxWidth:"800px",height:"600px",display:"flex",flexDirection:"column",boxShadow:"0 20px 60px rgba(0, 0, 0, 0.3)",overflow:"hidden"},children:[(0,n.jsxs)("div",{style:{backgroundColor:"#ffffff",padding:"16px 24px",borderBottom:"1px solid #e5e7eb",display:"flex",alignItems:"center",justifyContent:"space-between"},children:[(0,n.jsxs)("div",{style:{display:"flex",alignItems:"center",gap:"12px"},children:[n.jsx(r.default,{src:"/brea.png",alt:"Brea",width:40,height:40,style:{borderRadius:"50%"}}),(0,n.jsxs)("div",{children:[n.jsx("h3",{style:{margin:0,fontSize:"18px",fontWeight:"600",color:"#111827"},children:"Chat with Brea"}),n.jsx("p",{style:{margin:0,fontSize:"14px",color:"#6b7280"},children:"Your Benefits Specialist"})]})]}),n.jsx("button",{onClick:t,style:{background:"none",border:"none",fontSize:"24px",cursor:"pointer",color:"#6b7280",padding:"4px",borderRadius:"4px",display:"flex",alignItems:"center",justifyContent:"center"},children:"\xd7"})]}),(0,n.jsxs)("div",{style:{flex:1,overflow:"auto",padding:"16px",display:"flex",flexDirection:"column",gap:"12px"},children:[h.map((e,t)=>(0,n.jsxs)("div",{style:{display:"flex",flexDirection:"user"===e.sender?"row-reverse":"row",alignItems:"flex-start",gap:"8px"},children:[n.jsx("div",{style:{width:"32px",height:"32px",borderRadius:"50%",display:"flex",alignItems:"center",justifyContent:"center",backgroundColor:"user"===e.sender?"#000000":"transparent",color:"white",fontSize:"14px",fontWeight:"600",flexShrink:0},children:"user"===e.sender?b(f.name):n.jsx(r.default,{src:"/brea.png",alt:"Brea",width:32,height:32,style:{borderRadius:"50%"}})}),(0,n.jsxs)("div",{style:{maxWidth:"70%",backgroundColor:"user"===e.sender?"#000000":"#ffffff",color:"user"===e.sender?"#ffffff":"#000000",padding:"12px 16px",borderRadius:"user"===e.sender?"16px 16px 4px 16px":"16px 16px 16px 4px",fontSize:"14px",lineHeight:"1.5",boxShadow:"0 1px 3px rgba(0, 0, 0, 0.1)"},children:[n.jsx("div",{dangerouslySetInnerHTML:{__html:"bot"===e.sender?`${e.message}<br/><small style="color: #6b7280; font-size: 12px;">AI-generated content—verify before use.</small>`:e.message},style:{whiteSpace:"pre-wrap",wordBreak:"break-word"}}),"bot"===e.sender&&e.message.includes("how can I help you")&&t===h.length-1&&n.jsx("div",{style:{display:"flex",flexWrap:"wrap",gap:"8px",marginTop:"12px"},children:D.map(e=>n.jsx("button",{onClick:()=>y(e),style:{padding:"6px 12px",backgroundColor:"#f3f4f6",color:"#374151",border:"1px solid #d1d5db",borderRadius:"16px",fontSize:"12px",cursor:"pointer",transition:"all 0.2s"},onMouseOver:e=>{e.currentTarget.style.backgroundColor="#e5e7eb"},onMouseOut:e=>{e.currentTarget.style.backgroundColor="#f3f4f6"},children:e},e))})]})]},t)),m&&(0,n.jsxs)("div",{style:{display:"flex",alignItems:"flex-start",gap:"8px"},children:[n.jsx(r.default,{src:"/brea.png",alt:"Brea",width:32,height:32,style:{borderRadius:"50%"}}),n.jsx("div",{style:{backgroundColor:"#ffffff",padding:"12px 16px",borderRadius:"16px 16px 16px 4px",fontSize:"14px",color:"#6b7280",boxShadow:"0 1px 3px rgba(0, 0, 0, 0.1)"},children:"Brea is typing..."})]}),n.jsx("div",{ref:p})]}),n.jsx("div",{style:{backgroundColor:"#ffffff",padding:"16px",borderTop:"1px solid #e5e7eb"},children:(0,n.jsxs)("div",{style:{display:"flex",gap:"12px",alignItems:"flex-end"},children:[n.jsx("textarea",{value:c,onChange:e=>x(e.target.value),onKeyPress:e=>{"Enter"!==e.key||e.shiftKey||(e.preventDefault(),y(c))},placeholder:"Type your message...",style:{flex:1,padding:"12px",border:"1px solid #d1d5db",borderRadius:"8px",fontSize:"14px",resize:"none",minHeight:"44px",maxHeight:"120px",outline:"none",fontFamily:"inherit",color:"#000000",backgroundColor:"#ffffff"},rows:1}),n.jsx("button",{onClick:()=>y(c),disabled:!c.trim(),style:{padding:"12px 20px",backgroundColor:c.trim()?"#000000":"#e5e7eb",color:c.trim()?"#ffffff":"#9ca3af",border:"none",borderRadius:"8px",fontSize:"14px",fontWeight:"500",cursor:c.trim()?"pointer":"not-allowed",transition:"all 0.2s"},children:"Send"})]})})]})}):null}},67925:(e,t,o)=>{"use strict";o.d(t,{Z:()=>m});var n=o(10326),l=o(17577),r=o(6283),i=o(25609),a=o(33198),s=o(42265),d=o(82400),p=o(46226),c=o(22758),x=o(31870),g=o(25842),u=o(30656),f=o(88563),h=o(35047);let m=()=>{let e=(0,h.useRouter)(),{logout:t}=(0,c.a)(),o=(0,x.C)(e=>e.user.userProfile),[m,y]=(0,l.useState)(!1);(0,l.useEffect)(()=>{y("true"===localStorage.getItem("isTeamsApp1"))},[]);let b=(0,g.v9)(e=>e.user.userProfile.isAdmin),v=(0,g.v9)(e=>e.user.userProfile.isBroker);return n.jsx(r.Z,{sx:{bgcolor:"white",padding:2,textAlign:"center",height:"80px",borderBottom:"1px solid #D2D2D2",position:"sticky",top:0,zIndex:50,boxShadow:"0 1px 3px 0 rgba(0, 0, 0, 0.1)"},children:(0,n.jsxs)(r.Z,{sx:{display:"flex",alignItems:"center",justifyContent:"space-between"},children:[(0,n.jsxs)(r.Z,{sx:{display:"flex",alignItems:"center"},children:[(0,n.jsxs)(r.Z,{sx:{display:"flex",alignItems:"center",cursor:"pointer",mr:3},onClick:()=>{e.push("/dashboard")},children:[n.jsx(p.default,{src:u.Z,alt:"BenOsphere Logo",width:40,height:40}),n.jsx(i.Z,{sx:{fontWeight:800,fontSize:"1.5rem",ml:1,color:"#111827"},children:"BenOsphere"})]}),n.jsx(a.Z,{sx:{bgcolor:"black",color:"#ffffff",width:35,height:35,fontSize:"1.2rem",mr:1.5,ml:2,display:"flex",alignItems:"center",justifyContent:"center",paddingBottom:"1px",fontWeight:800},children:(e=>{if(!e)return"";let[t,o]=e.split(" ");return`${t[0].toUpperCase()}${o?o[0].toUpperCase():""}`})(o.name)}),n.jsx(i.Z,{variant:"h4",sx:{mb:0,fontWeight:"bold",display:"flex",alignItems:"center",justifyContent:"flex-start",textAlign:"flex-start",fontSize:"16px",mr:1.5,color:"#111827"},children:o.name.replace(/\b\w/g,e=>e.toUpperCase())}),b&&n.jsx(r.Z,{sx:{bgcolor:"rgba(0, 0, 0, 0.06)",borderRadius:"8px",padding:"4px 8px",display:"flex",alignItems:"center",justifyContent:"center",fontWeight:"bold",fontSize:"12px",color:"#333",mr:1.5},children:"ADMIN"}),v&&n.jsx(r.Z,{sx:{bgcolor:"#f5f5f5",borderRadius:"8px",padding:"2px 8px",display:"flex",alignItems:"center",justifyContent:"center",fontWeight:"bold",fontSize:"0.9rem",color:"#333",mr:1.5},children:"BROKER"})]}),(0,n.jsxs)(r.Z,{sx:{display:"flex",alignItems:"center"},children:[!m&&(0,n.jsxs)(s.Z,{sx:{backgroundColor:"transparent",color:"#333",textTransform:"none",padding:"8px 8px",display:"flex",alignItems:"center",justifyContent:"flex-start",gap:"2px","&:hover":{backgroundColor:"transparent",color:"#555"},boxShadow:"none"},children:[n.jsx(r.Z,{sx:{mt:.5,mr:.5},children:n.jsx(f.Z,{})}),n.jsx(i.Z,{sx:{fontWeight:500,fontSize:"14px",color:"#333"},children:"Guide"})]}),!m&&(0,n.jsxs)(s.Z,{onClick:t,sx:{backgroundColor:"transparent",color:"#333",marginLeft:1,textTransform:"none",padding:"8px 16px",display:"flex",alignItems:"center",justifyContent:"flex-start",gap:"8px","&:hover":{backgroundColor:"transparent",color:"#555"},boxShadow:"none"},children:[n.jsx(d.Z,{sx:{fontSize:"18px"}}),n.jsx(i.Z,{sx:{fontWeight:500,fontSize:"14px",color:"#333"},children:"Logout"})]})]})]})})}},74429:(e,t,o)=>{"use strict";o.r(t),o.d(t,{default:()=>eO});var n=o(10326),l=o(17577);o(70105);var r=o(9664);let i=(0,r.Z)("Bell",[["path",{d:"M10.268 21a2 2 0 0 0 3.464 0",key:"vwvbt9"}],["path",{d:"M3.262 15.326A1 1 0 0 0 4 17h16a1 1 0 0 0 .74-1.673C19.41 13.956 18 12.499 18 8A6 6 0 0 0 6 8c0 4.499-1.411 5.956-2.738 7.326",key:"11g9vi"}]]),a=(0,r.Z)("Brain",[["path",{d:"M12 5a3 3 0 1 0-5.997.125 4 4 0 0 0-2.526 5.77 4 4 0 0 0 .556 6.588A4 4 0 1 0 12 18Z",key:"l5xja"}],["path",{d:"M12 5a3 3 0 1 1 5.997.125 4 4 0 0 1 2.526 5.77 4 4 0 0 1-.556 6.588A4 4 0 1 1 12 18Z",key:"ep3f8r"}],["path",{d:"M15 13a4.5 4.5 0 0 1-3-4 4.5 4.5 0 0 1-3 4",key:"1p4c4q"}],["path",{d:"M17.599 6.5a3 3 0 0 0 .399-1.375",key:"tmeiqw"}],["path",{d:"M6.003 5.125A3 3 0 0 0 6.401 6.5",key:"105sqy"}],["path",{d:"M3.477 10.896a4 4 0 0 1 .585-.396",key:"ql3yin"}],["path",{d:"M19.938 10.5a4 4 0 0 1 .585.396",key:"1qfode"}],["path",{d:"M6 18a4 4 0 0 1-1.967-.516",key:"2e4loj"}],["path",{d:"M19.967 17.484A4 4 0 0 1 18 18",key:"159ez6"}]]),s=(0,r.Z)("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]]),d=(0,r.Z)("Smile",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M8 14s1.5 2 4 2 4-2 4-2",key:"1y1vjs"}],["line",{x1:"9",x2:"9.01",y1:"9",y2:"9",key:"yxxnd0"}],["line",{x1:"15",x2:"15.01",y1:"9",y2:"9",key:"1p4y9e"}]]),p=(0,r.Z)("Eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]),c=(0,r.Z)("Stethoscope",[["path",{d:"M11 2v2",key:"1539x4"}],["path",{d:"M5 2v2",key:"1yf1q8"}],["path",{d:"M5 3H4a2 2 0 0 0-2 2v4a6 6 0 0 0 12 0V5a2 2 0 0 0-2-2h-1",key:"rb5t3r"}],["path",{d:"M8 15a6 6 0 0 0 12 0v-3",key:"x18d4x"}],["circle",{cx:"20",cy:"10",r:"2",key:"ts1r5v"}]]),x=(0,r.Z)("Shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]]),g=(0,r.Z)("FileText",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]]),u=(0,r.Z)("CircleCheckBig",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]]);var f=o(9921),h=o(96885),m=o(46226);let y=(0,r.Z)("Play",[["polygon",{points:"6 3 20 12 6 21 6 3",key:"1oa8hb"}]]),b=(0,r.Z)("CircleHelp",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3",key:"1u773s"}],["path",{d:"M12 17h.01",key:"p32p05"}]]);var v=o(43020);let D=(0,r.Z)("Send",[["path",{d:"M14.536 21.686a.5.5 0 0 0 .937-.024l6.5-19a.496.496 0 0 0-.635-.635l-19 6.5a.5.5 0 0 0-.024.937l7.93 3.18a2 2 0 0 1 1.112 1.11z",key:"1ffxy3"}],["path",{d:"m21.854 2.147-10.94 10.939",key:"12cjpa"}]]),j=({onClose:e})=>{let[t,o]=(0,l.useState)(!!e),[r,i]=(0,l.useState)(""),[a,s]=(0,l.useState)([]),d=()=>{o(!1),e&&e()},p=e=>{let t={question:e.fullQuestion,answer:e.answer,isCustom:!1};s(e=>[...e,t])},c=()=>{if(r.trim()){let e={question:r,answer:"I'd be happy to help! For specific questions about plan details, costs, or coverage, please contact HR or the insurance carrier directly. You can also check the plan documents for detailed information.",isCustom:!0};s(t=>[...t,e]),i("")}};return t?(0,n.jsxs)(n.Fragment,{children:[n.jsx("div",{style:{position:"fixed",inset:0,backgroundColor:"rgba(0, 0, 0, 0.5)",zIndex:50},onClick:d}),(0,n.jsxs)("div",{style:{position:"fixed",top:"50%",left:"50%",transform:"translate(-50%, -50%)",width:"95vw",maxWidth:"800px",maxHeight:"85vh",backgroundColor:"white",borderRadius:"16px",boxShadow:"0 25px 50px rgba(0, 0, 0, 0.25)",zIndex:51,overflow:"hidden",display:"flex",flexDirection:"column"},children:[(0,n.jsxs)("div",{style:{display:"flex",alignItems:"center",justifyContent:"space-between",padding:"16px 20px",borderBottom:"1px solid #e5e7eb",backgroundColor:"#f8fafc"},children:[(0,n.jsxs)("div",{style:{display:"flex",alignItems:"center",gap:"8px"},children:[n.jsx("div",{style:{width:"24px",height:"24px",backgroundColor:"#3b82f6",borderRadius:"6px",display:"flex",alignItems:"center",justifyContent:"center"},children:n.jsx(b,{size:14,style:{color:"white"}})}),n.jsx("h2",{style:{fontSize:"16px",fontWeight:"600",color:"#111827",margin:0,fontFamily:"sans-serif"},children:"Benefits Q&A Assistant"})]}),n.jsx("button",{onClick:d,style:{padding:"4px",backgroundColor:"transparent",border:"none",borderRadius:"4px",cursor:"pointer",color:"#6b7280"},children:n.jsx(v.Z,{size:16})})]}),(0,n.jsxs)("div",{style:{flex:1,display:"flex",flexDirection:"column",overflow:"hidden"},children:[(0,n.jsxs)("div",{style:{padding:"16px 20px",borderBottom:"1px solid #e5e7eb"},children:[n.jsx("h3",{style:{fontSize:"14px",fontWeight:"600",color:"#111827",margin:"0 0 12px 0",fontFamily:"sans-serif"},children:"Common Questions:"}),n.jsx("div",{style:{display:"grid",gridTemplateColumns:"repeat(3, 1fr)",gridTemplateRows:"repeat(2, 1fr)",gap:"8px"},children:[{question:"What happens if I don't enroll...",fullQuestion:"What happens if I don't enroll in benefits?",answer:"If you don't enroll during open enrollment, you'll have no medical, dental, or vision coverage for 2025. You can only enroll outside of open enrollment if you have a qualifying life event."},{question:"Can I change my plan after enr...",fullQuestion:"Can I change my plan after enrollment?",answer:"You can only change your plan during the next open enrollment period or if you have a qualifying life event (marriage, birth of child, job loss, etc.)."},{question:"What's the difference between ...",fullQuestion:"What's the difference between HMO and PPO?",answer:"PPO plans offer more flexibility to see any doctor without referrals but cost more. HMO plans require you to choose a primary care physician and get referrals for specialists, but have lower costs."},{question:"How much will I pay per payche...",fullQuestion:"How much will I pay per paycheck?",answer:"Your paycheck deduction depends on the plan you choose and your family size. The costs shown during enrollment are per month - they'll be divided across your paychecks."},{question:"Are my current doctors covered...",fullQuestion:"Are my current doctors covered?",answer:"This depends on the plan's network. Check the provider directory for each plan to see if your doctors are covered. PPO plans typically have larger networks than HMO plans."}].map((e,t)=>n.jsx("button",{onClick:()=>p(e),style:{padding:"8px 12px",backgroundColor:"white",border:"1px solid #e2e8f0",borderRadius:"6px",fontSize:"13px",color:"#475569",textAlign:"left",cursor:"pointer",transition:"all 0.2s ease",fontFamily:"sans-serif",whiteSpace:"nowrap",overflow:"hidden",textOverflow:"ellipsis"},onMouseOver:e=>{e.currentTarget.style.backgroundColor="#f8fafc",e.currentTarget.style.borderColor="#cbd5e1"},onMouseOut:e=>{e.currentTarget.style.backgroundColor="white",e.currentTarget.style.borderColor="#e2e8f0"},children:e.question},t))})]}),n.jsx("div",{style:{flex:1,minHeight:"300px",padding:"20px",overflowY:"auto",display:"flex",flexDirection:"column",gap:"12px",backgroundColor:"#fafbfc"},children:0===a.length?n.jsx("div",{style:{display:"flex",alignItems:"center",justifyContent:"center",height:"100%",color:"#94a3b8",fontSize:"14px",fontFamily:"sans-serif"},children:"Click a question above or type your own question below"}):a.map((e,t)=>(0,n.jsxs)("div",{style:{display:"flex",flexDirection:"column",gap:"8px"},children:[n.jsx("div",{style:{padding:"12px 16px",backgroundColor:"#3b82f6",borderRadius:"16px",alignSelf:"flex-end",maxWidth:"80%"},children:n.jsx("p",{style:{color:"white",fontSize:"14px",margin:0,fontFamily:"sans-serif"},children:e.question})}),n.jsx("div",{style:{padding:"16px",backgroundColor:"#f1f5f9",borderRadius:"16px",alignSelf:"flex-start",maxWidth:"90%"},children:n.jsx("p",{style:{color:"#334155",fontSize:"14px",margin:0,lineHeight:"1.5",fontFamily:"sans-serif"},children:e.answer})})]},t))}),n.jsx("div",{style:{padding:"16px 20px",backgroundColor:"#f8fafc",borderTop:"1px solid #e5e7eb"},children:(0,n.jsxs)("div",{style:{display:"flex",gap:"8px",alignItems:"flex-end"},children:[n.jsx("input",{type:"text",value:r,onChange:e=>i(e.target.value),onKeyDown:e=>{"Enter"!==e.key||e.shiftKey||(e.preventDefault(),c())},placeholder:"Ask a question about your benefits...",style:{flex:1,padding:"12px 16px",border:"1px solid #d1d5db",borderRadius:"24px",fontSize:"14px",outline:"none",fontFamily:"sans-serif",backgroundColor:"white",color:"#111827"}}),n.jsx("button",{onClick:c,disabled:!r.trim(),style:{width:"40px",height:"40px",backgroundColor:r.trim()?"#1e293b":"#94a3b8",border:"none",borderRadius:"50%",display:"flex",alignItems:"center",justifyContent:"center",cursor:r.trim()?"pointer":"not-allowed",transition:"all 0.2s ease"},children:n.jsx(D,{size:16,style:{color:"white"}})})]})})]})]})]}):n.jsx("div",{style:{position:"fixed",bottom:"24px",right:"24px",zIndex:50},children:(0,n.jsxs)("button",{onClick:()=>o(!0),style:{display:"flex",alignItems:"center",gap:"8px",padding:"12px 20px",backgroundColor:"#2563eb",color:"white",border:"none",borderRadius:"50px",fontSize:"14px",fontWeight:"500",cursor:"pointer",boxShadow:"0 10px 25px rgba(0, 0, 0, 0.15)",transition:"all 0.2s ease",fontFamily:"sans-serif"},onMouseOver:e=>{e.currentTarget.style.backgroundColor="#1d4ed8",e.currentTarget.style.boxShadow="0 15px 35px rgba(0, 0, 0, 0.2)"},onMouseOut:e=>{e.currentTarget.style.backgroundColor="#2563eb",e.currentTarget.style.boxShadow="0 10px 25px rgba(0, 0, 0, 0.15)"},children:[n.jsx(b,{size:20}),"Need Help?"]})})},C=(0,r.Z)("Video",[["path",{d:"m16 13 5.223 3.482a.5.5 0 0 0 .777-.416V7.87a.5.5 0 0 0-.752-.432L16 10.5",key:"ftymec"}],["rect",{x:"2",y:"6",width:"14",height:"12",rx:"2",key:"158x01"}]]),S=({title:e,description:t,thumbnailUrl:o,videoUrl:r,planType:i,onClose:a})=>{let[s,d]=(0,l.useState)(!0),p=()=>{d(!1),a&&a()},c=(()=>{switch(i){case"medical":return{title:"Understanding Medical Plans: PPO vs HMO",description:"Learn the key differences between PPO and HMO plans, including costs, flexibility, and how to choose the right one for your needs.",duration:"3:24"};case"dental":return{title:"Dental Benefits Explained",description:"Discover how dental insurance works, what's covered, and how to maximize your benefits for routine and major dental work.",duration:"2:45"};case"vision":return{title:"Vision Benefits Overview",description:"Learn about vision coverage for eye exams, glasses, contacts, and how to use your benefits effectively.",duration:"2:15"};case"summary":return{title:"Benefits Summary Overview",description:"Review your benefits enrollment summary and understand your next steps in the enrollment process.",duration:"2:30"};default:return{title:e,description:t,duration:"3:00"}}})();return s?(0,n.jsxs)(n.Fragment,{children:[n.jsx("div",{style:{position:"fixed",inset:0,backgroundColor:"rgba(0, 0, 0, 0.5)",zIndex:50},onClick:p}),(0,n.jsxs)("div",{style:{position:"fixed",top:"50%",left:"50%",transform:"translate(-50%, -50%)",width:"90vw",maxWidth:"900px",backgroundColor:"white",borderRadius:"16px",boxShadow:"0 25px 50px rgba(0, 0, 0, 0.25)",zIndex:51,overflow:"hidden"},children:[(0,n.jsxs)("div",{style:{display:"flex",alignItems:"center",justifyContent:"space-between",padding:"20px 24px",borderBottom:"1px solid #e5e7eb"},children:[(0,n.jsxs)("div",{style:{display:"flex",alignItems:"center",gap:"8px"},children:[n.jsx(C,{size:20,style:{color:"#2563eb"}}),n.jsx("h2",{style:{fontSize:"18px",fontWeight:"600",color:"#111827",margin:0,fontFamily:"sans-serif"},children:c.title})]}),n.jsx("button",{onClick:p,style:{padding:"8px",backgroundColor:"transparent",border:"none",borderRadius:"8px",cursor:"pointer",color:"#6b7280"},children:n.jsx(v.Z,{size:20})})]}),(0,n.jsxs)("div",{style:{padding:"24px"},children:[n.jsx("div",{style:{aspectRatio:"16/9",borderRadius:"8px",overflow:"hidden",marginBottom:"16px"},children:n.jsx("iframe",{width:"100%",height:"100%",src:"https://www.youtube.com/embed/MPN66L_skBw",title:"Benefits Enrollment Video",frameBorder:"0",allow:"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share",allowFullScreen:!0,style:{border:"none",borderRadius:"8px"}})}),(0,n.jsxs)("div",{style:{backgroundColor:"white",border:"1px solid #e5e7eb",borderRadius:"8px",padding:"16px"},children:[n.jsx("h4",{style:{fontSize:"14px",fontWeight:"600",color:"#111827",margin:"0 0 8px 0",fontFamily:"sans-serif"},children:"What you'll learn:"}),n.jsx("p",{style:{fontSize:"14px",color:"#6b7280",margin:0,lineHeight:"1.5",fontFamily:"sans-serif"},children:c.description})]})]})]})]}):(0,n.jsxs)("button",{onClick:()=>d(!0),style:{display:"flex",alignItems:"center",gap:"8px",padding:"8px 16px",color:"#374151",border:"1px solid #d1d5db",borderRadius:"8px",backgroundColor:"white",cursor:"pointer",transition:"background-color 0.2s ease",fontSize:"14px",fontWeight:"500",fontFamily:"sans-serif"},onMouseOver:e=>e.currentTarget.style.backgroundColor="#f9fafb",onMouseOut:e=>e.currentTarget.style.backgroundColor="white",children:[n.jsx(y,{size:16,style:{color:"#6b7280"}}),"Watch Video"]})};var w=o(96103);let k=()=>"June 30, 2025",I=({onNext:e})=>{let[t,o]=(0,l.useState)(!1),[r,i]=(0,l.useState)(!1),[a,s]=(0,l.useState)(!1);return(0,n.jsxs)("div",{style:{display:"flex",flexDirection:"column",gap:"24px",fontFamily:'"SF Pro", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif'},children:[(0,n.jsxs)("div",{style:{display:"flex",gap:"16px"},children:[n.jsx("div",{style:{width:"40px",height:"40px",borderRadius:"50%",display:"flex",alignItems:"center",justifyContent:"center",flexShrink:0,overflow:"hidden"},children:n.jsx(m.default,{src:"/brea.png",alt:"Brea - AI Benefits Assistant",width:40,height:40,style:{borderRadius:"50%"}})}),(0,n.jsxs)("div",{style:{backgroundColor:"#f9fafb",borderRadius:"8px",padding:"16px",maxWidth:"1000px"},children:[n.jsx("p",{style:{color:"#374151",lineHeight:"1.6",margin:0},children:n.jsx("span",{style:{fontWeight:"600"},children:"\uD83D\uDC4B Hi there! Let's simplify your benefits enrollment."})}),n.jsx("p",{style:{color:"#374151",lineHeight:"1.6",margin:"8px 0 0 0"},children:"I'll ask a few quick questions and guide you to the best-fit plans — no stress, no guesswork."})]})]}),n.jsx("div",{style:{backgroundColor:"white",borderRadius:"8px",border:"1px solid #e5e7eb",padding:"24px",boxShadow:"0 1px 3px 0 rgba(0, 0, 0, 0.1)"},children:(0,n.jsxs)("div",{style:{display:"flex",flexDirection:"column",gap:"16px"},children:[n.jsx("p",{style:{color:"#374151",display:"flex",alignItems:"center",gap:"8px",margin:0,fontWeight:"600",fontSize:"16px",lineHeight:"1.6",fontFamily:'"SF Pro", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif'},children:"\uD83D\uDD14 Smarter Benefits Start Now"}),n.jsx("div",{style:{backgroundColor:"#fdf2f8",border:"1px solid #fce7f3",borderRadius:"8px",padding:"16px"},children:(0,n.jsxs)("p",{style:{color:"#be185d",fontWeight:"500",display:"flex",alignItems:"center",gap:"8px",margin:0,fontSize:"14px",lineHeight:"1.6",fontFamily:'"SF Pro", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif'},children:["\uD83D\uDCC5 ",n.jsx("span",{style:{fontWeight:"600"},children:"Enrollment Deadline:"})," ",k()]})}),(0,n.jsxs)("div",{style:{display:"flex",flexDirection:"column",gap:"12px"},children:[n.jsx("p",{style:{color:"#ec4899",fontWeight:"500",display:"flex",alignItems:"center",gap:"8px",margin:0,fontSize:"14px",lineHeight:"1.6",fontFamily:'"SF Pro", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif'},children:"\uD83E\uDDE0 I'm Brea, your AI benefits assistant. Here's how I'll help:"}),(0,n.jsxs)("ul",{style:{display:"flex",flexDirection:"column",gap:"8px",marginLeft:"24px",listStyle:"none",padding:0,margin:0},children:[(0,n.jsxs)("li",{style:{color:"#374151",display:"flex",alignItems:"flex-start",gap:"8px",fontSize:"14px",lineHeight:"1.6",fontFamily:'"SF Pro", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif'},children:[n.jsx("span",{style:{color:"#2563eb",marginTop:"4px"},children:"•"}),"Understand your needs in minutes"]}),(0,n.jsxs)("li",{style:{color:"#374151",display:"flex",alignItems:"flex-start",gap:"8px",fontSize:"14px",lineHeight:"1.6",fontFamily:'"SF Pro", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif'},children:[n.jsx("span",{style:{color:"#2563eb",marginTop:"4px"},children:"•"}),"Recommend plans tailored to you"]}),(0,n.jsxs)("li",{style:{color:"#374151",display:"flex",alignItems:"flex-start",gap:"8px",fontSize:"14px",lineHeight:"1.6",fontFamily:'"SF Pro", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif'},children:[n.jsx("span",{style:{color:"#2563eb",marginTop:"4px"},children:"•"}),"Show short, clear explainer videos"]}),(0,n.jsxs)("li",{style:{color:"#374151",display:"flex",alignItems:"flex-start",gap:"8px",fontSize:"14px",lineHeight:"1.6",fontFamily:'"SF Pro", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif'},children:[n.jsx("span",{style:{color:"#2563eb",marginTop:"4px"},children:"•"}),"Answer your questions on the spot"]}),(0,n.jsxs)("li",{style:{color:"#374151",display:"flex",alignItems:"flex-start",gap:"8px",fontSize:"14px",lineHeight:"1.6",fontFamily:'"SF Pro", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif'},children:[n.jsx("span",{style:{color:"#2563eb",marginTop:"4px"},children:"•"}),"Compare top plan choices side by side"]})]})]}),(0,n.jsxs)("div",{style:{display:"flex",gap:"12px",paddingTop:"16px"},children:[(0,n.jsxs)("button",{onClick:()=>s(!0),style:{display:"flex",alignItems:"center",gap:"8px",padding:"8px 16px",color:"#374151",border:"1px solid #d1d5db",borderRadius:"8px",backgroundColor:"white",cursor:"pointer",transition:"background-color 0.2s ease",fontSize:"14px",fontWeight:"500",fontFamily:'"SF Pro", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif'},onMouseOver:e=>e.currentTarget.style.backgroundColor="#f9fafb",onMouseOut:e=>e.currentTarget.style.backgroundColor="white",children:[n.jsx("span",{style:{color:"#2563eb"},children:"❓"}),"Ask Questions"]}),(0,n.jsxs)("button",{onClick:()=>i(!0),style:{display:"flex",alignItems:"center",gap:"8px",padding:"8px 16px",color:"#374151",border:"1px solid #d1d5db",borderRadius:"8px",backgroundColor:"white",cursor:"pointer",transition:"background-color 0.2s ease",fontSize:"14px",fontWeight:"500",fontFamily:'"SF Pro", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif'},onMouseOver:e=>e.currentTarget.style.backgroundColor="#f9fafb",onMouseOut:e=>e.currentTarget.style.backgroundColor="white",children:[n.jsx(y,{size:16,style:{color:"#6b7280"}}),"Watch Video"]})]}),n.jsx("button",{onClick:e,style:{width:"100%",backgroundColor:"#111827",color:"white",padding:"12px 24px",borderRadius:"8px",fontWeight:"600",border:"none",cursor:"pointer",transition:"background-color 0.2s ease",display:"flex",alignItems:"center",justifyContent:"center",gap:"8px",marginTop:"24px",fontSize:"14px",fontFamily:'"SF Pro", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif'},onMouseOver:e=>e.currentTarget.style.backgroundColor="#374151",onMouseOut:e=>e.currentTarget.style.backgroundColor="#111827",children:"\uD83D\uDE80 Start My Smart Enrollment"})]})}),t&&n.jsx(j,{onClose:()=>o(!1)}),r&&n.jsx(S,{title:"Benefits Enrollment Overview",description:"Learn how to navigate your benefits enrollment process",planType:"medical",onClose:()=>i(!1)}),a&&n.jsx(w.Z,{isOpen:a,onClose:()=>s(!1)})]})},P=(0,r.Z)("Activity",[["path",{d:"M22 12h-2.48a2 2 0 0 0-1.93 1.46l-2.35 8.36a.25.25 0 0 1-.48 0L9.24 2.18a.25.25 0 0 0-.48 0l-2.35 8.36A2 2 0 0 1 4.49 12H2",key:"169zse"}]]),W=(0,r.Z)("Heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]]),z=(0,r.Z)("DollarSign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]]),E=({onComplete:e,initialData:t})=>{let[o,r]=(0,l.useState)(t?.familyMembers||""),[i,a]=(0,l.useState)(t?.expectedMedicalUsage||""),[d,p]=(0,l.useState)(t?.budgetPreference||""),[c,x]=(0,l.useState)(t?.budgetAmount||100),[g,u]=(0,l.useState)(t?.healthConsiderations||[]),[f,h]=(0,l.useState)(!1),[b,v]=(0,l.useState)(!1),[D,C]=(0,l.useState)(!1),k=(e,t)=>{t?u(t=>[...t,e]):u(t=>t.filter(t=>t!==e))},I=o&&i&&d;return(0,n.jsxs)("div",{style:{display:"flex",flexDirection:"column",gap:"24px"},children:[(0,n.jsxs)("div",{style:{display:"flex",gap:"16px"},children:[n.jsx("div",{style:{width:"40px",height:"40px",borderRadius:"50%",display:"flex",alignItems:"center",justifyContent:"center",flexShrink:0,overflow:"hidden"},children:n.jsx(m.default,{src:"/brea.png",alt:"Brea - AI Benefits Assistant",width:40,height:40,style:{borderRadius:"50%"}})}),(0,n.jsxs)("div",{style:{backgroundColor:"#f9fafb",borderRadius:"8px",padding:"16px",maxWidth:"1000px"},children:[n.jsx("p",{style:{color:"#374151",lineHeight:"1.6",margin:0},children:"\uD83E\uDD14 Let me learn about you to give the best recommendations!"}),n.jsx("p",{style:{color:"#374151",lineHeight:"1.6",margin:"8px 0 0 0"},children:"I'll ask a few quick questions about your healthcare needs, budget preferences, and family situation."})]})]}),(0,n.jsxs)("div",{style:{backgroundColor:"white",borderRadius:"8px",border:"1px solid #e5e7eb",padding:"24px",boxShadow:"0 1px 3px 0 rgba(0, 0, 0, 0.1)"},children:[(0,n.jsxs)("div",{style:{display:"flex",alignItems:"center",gap:"8px",marginBottom:"24px"},children:[n.jsx("div",{style:{width:"24px",height:"24px",backgroundColor:"#f3e8ff",borderRadius:"4px",display:"flex",alignItems:"center",justifyContent:"center"},children:n.jsx(P,{style:{width:"16px",height:"16px",color:"#9333ea"}})}),n.jsx("h2",{style:{fontSize:"20px",fontWeight:"600",color:"#111827",margin:0},children:"AI-Powered Personalization ✨"})]}),n.jsx("p",{style:{color:"#6b7280",marginBottom:"24px",margin:0},children:"Let me learn about you to provide the most accurate recommendations:"}),(0,n.jsxs)("div",{style:{display:"flex",flexDirection:"column",gap:"24px"},children:[(0,n.jsxs)("div",{style:{display:"flex",flexDirection:"column",gap:"16px"},children:[(0,n.jsxs)("div",{style:{display:"flex",alignItems:"center",gap:"8px"},children:[n.jsx(W,{style:{width:"20px",height:"20px",color:"#ef4444"}}),n.jsx("h3",{style:{fontSize:"18px",fontWeight:"500",color:"#111827",margin:0},children:"Enhanced Health Profile"})]}),(0,n.jsxs)("div",{style:{display:"flex",flexDirection:"column",gap:"12px"},children:[(0,n.jsxs)("div",{style:{display:"flex",alignItems:"center",gap:"8px"},children:[n.jsx(s,{style:{width:"16px",height:"16px",color:"#6b7280"}}),n.jsx("label",{style:{fontWeight:"500",color:"#374151",margin:0},children:"Family Coverage Needed"})]}),n.jsx("div",{style:{display:"flex",flexDirection:"column",gap:"8px"},children:[{value:"employee-only",label:"Just me (Employee only)"},{value:"employee-spouse",label:"Me + Spouse/Partner"},{value:"employee-children",label:"Me + Child(ren)"},{value:"employee-family",label:"Me + Family (includes children)"}].map(e=>(0,n.jsxs)("label",{style:{display:"flex",alignItems:"center",gap:"12px",cursor:"pointer"},children:[n.jsx("input",{type:"radio",name:"familyMembers",value:e.value,checked:o===e.value,onChange:e=>r(e.target.value),style:{width:"16px",height:"16px",accentColor:"#000000"}}),n.jsx("span",{style:{color:"#374151"},children:e.label})]},e.value))})]}),(0,n.jsxs)("div",{style:{display:"flex",flexDirection:"column",gap:"12px"},children:[(0,n.jsxs)("div",{style:{display:"flex",alignItems:"center",gap:"8px"},children:[n.jsx(P,{style:{width:"16px",height:"16px",color:"#6b7280"}}),n.jsx("label",{style:{fontWeight:"500",color:"#374151",margin:0},children:"Expected Healthcare Usage"})]}),n.jsx("div",{style:{display:"flex",flexDirection:"column",gap:"8px"},children:[{value:"low",label:"Low - Just preventive care & checkups"},{value:"moderate",label:"Moderate - Occasional visits & some prescriptions"},{value:"high",label:"High - Regular specialists, procedures, or chronic conditions"}].map(e=>(0,n.jsxs)("label",{style:{display:"flex",alignItems:"center",gap:"12px",cursor:"pointer"},children:[n.jsx("input",{type:"radio",name:"expectedUsage",value:e.value,checked:i===e.value,onChange:e=>a(e.target.value),style:{width:"16px",height:"16px",accentColor:"#000000"}}),n.jsx("span",{style:{color:"#374151"},children:e.label})]},e.value))})]}),(0,n.jsxs)("div",{style:{display:"flex",flexDirection:"column",gap:"12px"},children:[(0,n.jsxs)("div",{style:{display:"flex",alignItems:"center",gap:"8px"},children:[n.jsx(z,{style:{width:"16px",height:"16px",color:"#6b7280"}}),n.jsx("label",{style:{fontWeight:"500",color:"#374151",margin:0},children:"Budget Preference"})]}),n.jsx("div",{style:{display:"flex",flexDirection:"column",gap:"8px"},children:[{value:"low-premium",label:"Lower monthly cost, higher deductible"},{value:"balanced",label:"Balanced monthly cost and deductible"},{value:"low-deductible",label:"Higher monthly cost, lower deductible"}].map(e=>(0,n.jsxs)("label",{style:{display:"flex",alignItems:"center",gap:"12px",cursor:"pointer"},children:[n.jsx("input",{type:"radio",name:"budgetPreference",value:e.value,checked:d===e.value,onChange:e=>p(e.target.value),style:{width:"16px",height:"16px",accentColor:"#000000"}}),n.jsx("span",{style:{color:"#374151"},children:e.label})]},e.value))})]}),(0,n.jsxs)("div",{style:{display:"flex",flexDirection:"column",gap:"12px"},children:[(0,n.jsxs)("label",{style:{fontWeight:"500",color:"#374151",margin:0},children:["Maximum monthly budget: $",c,"/paycheck"]}),n.jsx("input",{type:"range",min:"50",max:"200",value:c,onChange:e=>x(Number(e.target.value)),style:{width:"100%",height:"8px",backgroundColor:"#000000",borderRadius:"8px",appearance:"none",cursor:"pointer"}})]}),(0,n.jsxs)("div",{style:{display:"flex",flexDirection:"column",gap:"12px"},children:[n.jsx("label",{style:{fontWeight:"500",color:"#374151",margin:0},children:"Health Considerations"}),n.jsx("div",{style:{display:"flex",flexDirection:"column",gap:"8px"},children:[{value:"glasses-contacts",label:"I wear glasses or contacts"},{value:"dental-care",label:"I need regular dental care or have dental work planned"},{value:"ongoing-conditions",label:"I have ongoing health conditions requiring regular care"},{value:"prescription-medications",label:"I take regular prescription medications"},{value:"preferred-doctors",label:"I have preferred doctors I want to keep seeing"}].map(e=>(0,n.jsxs)("label",{style:{display:"flex",alignItems:"center",gap:"12px",cursor:"pointer"},children:[n.jsx("input",{type:"checkbox",value:e.value,checked:g.includes(e.value),onChange:t=>k(e.value,t.target.checked),style:{width:"16px",height:"16px",accentColor:"#000000"}}),n.jsx("span",{style:{color:"#374151"},children:e.label})]},e.value))})]})]}),n.jsx("button",{onClick:()=>{e({familyMembers:o,expectedMedicalUsage:i,budgetPreference:d,budgetAmount:c,healthConsiderations:g,chronicConditions:g.includes("ongoing-conditions"),prescriptionNeeds:g.includes("prescription-medications"),hasPreferredDoctors:g.includes("preferred-doctors")})},disabled:!I,style:{width:"100%",padding:"12px 24px",borderRadius:"8px",fontWeight:"500",border:"none",cursor:I?"pointer":"not-allowed",transition:"background-color 0.2s ease",backgroundColor:I?"#000000":"#d1d5db",color:I?"white":"#9ca3af"},onMouseOver:e=>{I&&(e.currentTarget.style.backgroundColor="#374151")},onMouseOut:e=>{I&&(e.currentTarget.style.backgroundColor="#000000")},children:"Get My Personalized Recommendations"})]}),(0,n.jsxs)("div",{style:{display:"flex",gap:"12px",paddingTop:"24px",borderTop:"1px solid #e5e7eb",marginTop:"24px"},children:[(0,n.jsxs)("button",{onClick:()=>C(!0),style:{display:"flex",alignItems:"center",gap:"8px",padding:"8px 16px",color:"#374151",border:"1px solid #d1d5db",borderRadius:"8px",backgroundColor:"white",cursor:"pointer",transition:"background-color 0.2s ease"},onMouseOver:e=>e.currentTarget.style.backgroundColor="#f9fafb",onMouseOut:e=>e.currentTarget.style.backgroundColor="white",children:[n.jsx("span",{style:{color:"#2563eb"},children:"❓"}),"Ask Questions"]}),(0,n.jsxs)("button",{onClick:()=>v(!0),style:{display:"flex",alignItems:"center",gap:"8px",padding:"8px 16px",color:"#374151",border:"1px solid #d1d5db",borderRadius:"8px",backgroundColor:"white",cursor:"pointer",transition:"background-color 0.2s ease"},onMouseOver:e=>e.currentTarget.style.backgroundColor="#f9fafb",onMouseOut:e=>e.currentTarget.style.backgroundColor="white",children:[n.jsx(y,{size:16,style:{color:"#6b7280"}}),"Watch Video"]})]})]}),f&&n.jsx(j,{onClose:()=>h(!1)}),b&&n.jsx(S,{title:"Personalization Guide",description:"Learn how to answer personalization questions for better recommendations",planType:"medical",onClose:()=>v(!1)}),D&&n.jsx(w.Z,{isOpen:D,onClose:()=>C(!1)})]})},T=(0,r.Z)("CircleAlert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]]),R=(0,r.Z)("UserCheck",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["polyline",{points:"16 11 18 13 22 9",key:"1pwet4"}]]),A=(0,r.Z)("Pen",[["path",{d:"M21.174 6.812a1 1 0 0 0-3.986-3.987L3.842 16.174a2 2 0 0 0-.5.83l-1.321 4.352a.5.5 0 0 0 .623.622l4.353-1.32a2 2 0 0 0 .83-.497z",key:"1a8usu"}]]),B=(0,r.Z)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]]),F=(0,r.Z)("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]]),N=(0,r.Z)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]]);var O=o(53148),M=o(72779);let $=(0,r.Z)("TriangleAlert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]]),L=({isOpen:e,onClose:t,onConfirm:o,title:l,message:r,type:i="alert",confirmText:a="OK",cancelText:s="Cancel"})=>{if(!e)return null;let d=()=>{switch(i){case"success":return"#10b981";case"error":return"#ef4444";case"confirm":return"#f59e0b";default:return"#3b82f6"}};return n.jsx("div",{style:{position:"fixed",top:0,left:0,right:0,bottom:0,backgroundColor:"rgba(0, 0, 0, 0.5)",display:"flex",alignItems:"center",justifyContent:"center",zIndex:1e3},children:(0,n.jsxs)("div",{style:{backgroundColor:"white",borderRadius:"12px",padding:"32px",maxWidth:"400px",width:"90%",boxShadow:"0 20px 25px -5px rgba(0, 0, 0, 0.1)",textAlign:"center",position:"relative"},children:[n.jsx("button",{onClick:t,style:{position:"absolute",top:"16px",right:"16px",background:"none",border:"none",cursor:"pointer",padding:"4px",borderRadius:"4px"},children:n.jsx(v.Z,{style:{width:"20px",height:"20px",color:"#6b7280"}})}),n.jsx("div",{style:{width:"60px",height:"60px",backgroundColor:d(),borderRadius:"50%",display:"flex",alignItems:"center",justifyContent:"center",margin:"0 auto 20px"},children:(()=>{switch(i){case"success":return n.jsx(u,{style:{width:"48px",height:"48px",color:"#10b981"}});case"error":return n.jsx($,{style:{width:"48px",height:"48px",color:"#ef4444"}});case"confirm":return n.jsx($,{style:{width:"48px",height:"48px",color:"#f59e0b"}});default:return n.jsx($,{style:{width:"48px",height:"48px",color:"#3b82f6"}})}})()}),n.jsx("h3",{style:{fontSize:"20px",fontWeight:"600",color:"#111827",margin:"0 0 12px 0"},children:l}),n.jsx("p",{style:{color:"#6b7280",margin:"0 0 24px 0",lineHeight:"1.5",whiteSpace:"pre-line"},children:r}),(0,n.jsxs)("div",{style:{display:"flex",gap:"12px",justifyContent:"center"},children:["confirm"===i&&n.jsx("button",{onClick:t,style:{padding:"12px 24px",backgroundColor:"white",border:"1px solid #d1d5db",borderRadius:"8px",color:"#374151",cursor:"pointer",fontWeight:"500",fontSize:"16px"},children:s}),n.jsx("button",{onClick:()=>{o&&o(),t()},style:{padding:"12px 24px",backgroundColor:d(),border:"none",borderRadius:"8px",color:"white",cursor:"pointer",fontWeight:"500",fontSize:"16px"},children:a})]})]})})},H=async()=>{try{let e=await (0,O.A_)("/employee/company-details");if(e&&e.company){let t=e.company;try{let e=await (0,O.A_)("/admin/all-employees");if(e&&e.employees){let o=e.employees.find(e=>e.email===t.adminEmail&&e.isAdmin);if(o?._id)return o._id}}catch(e){console.log("Admin endpoint not available, using fallback")}}return localStorage.getItem("userid1")||localStorage.getItem("userId")}catch(e){return console.log("Using fallback admin ID due to error:",e),localStorage.getItem("userid1")||localStorage.getItem("userId")}},q=({selectedDependents:e,onConfirm:t,onBack:o,onDependentsChange:r,onProfileUpdate:i})=>{let[a,d]=(0,l.useState)(null),[p,c]=(0,l.useState)([]),[x,g]=(0,l.useState)(null),[f,h]=(0,l.useState)(!1),[y,b]=(0,l.useState)(!1),[D,j]=(0,l.useState)(null),[C,S]=(0,l.useState)(!1),[w,k]=(0,l.useState)(!1),[I,P]=(0,l.useState)({isValid:!0,missingFields:[],errors:[]}),[z,E]=(0,l.useState)({firstName:"",middleName:"",lastName:"",relationship:"Spouse",dateOfBirth:"",gender:"Male",ssn:""}),[$,q]=(0,l.useState)({name:"",relationship:"Spouse",dateOfBirth:"",gender:"Male",ssn:""}),[V,Z]=(0,l.useState)(!1),[U,_]=(0,l.useState)({title:"",message:"",type:"alert",onConfirm:()=>{},confirmText:"OK",cancelText:"Cancel"}),Y=(e,t,o="alert")=>{_({title:e,message:t,type:o,onConfirm:()=>{},confirmText:"OK",cancelText:"Cancel"}),Z(!0)},G=(e,t,o,n="OK",l="Cancel")=>{_({title:e,message:t,type:"confirm",onConfirm:o,confirmText:n,cancelText:l}),Z(!0)},J=()=>localStorage.getItem("userid1")||localStorage.getItem("userId"),X=async()=>{try{let e=J();if(!e){console.error("User ID not found");return}let t=await (0,O.A_)("/employee",{"user-id":e});t?.currentUser&&d(t.currentUser)}catch(e){console.error("Error fetching user details:",e)}};(0,l.useEffect)(()=>{X()},[]),(0,l.useEffect)(()=>{r&&r(p)},[p,r]),(0,l.useEffect)(()=>{if(a?.details?.dependents&&Array.isArray(a.details.dependents)){let t=a.details.dependents.map((e,t)=>({_id:e._id||`existing_${t}_${Date.now()}`,name:e.name||"",relationship:e.relationship||"",dateOfBirth:e.dateOfBirth?new Date(e.dateOfBirth).toISOString().split("T")[0]:"",gender:e.gender||"",ssn:e.ssn||""}));console.log("\uD83D\uDD0D DependentsConfirmationPage - selectedDependents:",e),console.log("\uD83D\uDD0D DependentsConfirmationPage - formattedDependents:",t);let o=t.filter(t=>{let o=t.relationship.toLowerCase();return(console.log(`🔍 Checking dependent: ${t.name} with relationship: ${o}`),e.includes("spouse")&&("spouse"===o||"domestic partner"===o))?(console.log(`✅ Including spouse: ${t.name}`),!0):e.includes("children")&&("child"===o||"stepchild"===o||"adopted child"===o)?(console.log(`✅ Including child: ${t.name}`),!0):(console.log(`❌ Excluding dependent: ${t.name} (relationship: ${o})`),!1)});console.log("\uD83D\uDD0D DependentsConfirmationPage - filteredDependents:",o),c(o)}else c([])},[e,a]);let K=e=>{let t=[],o=[];return e?.details?.dateOfBirth||(t.push("Date of Birth"),o.push("Date of birth is required for age-based cost calculations")),e?.details?.hireDate||(t.push("Hire Date"),o.push("Hire date is required for waiting period eligibility")),e?.details?.employeeClassType||(t.push("Employee Class Type"),o.push("Employee class type is required for plan eligibility")),e?.details?.phoneNumber||(t.push("Phone Number"),o.push("Phone number is required for enrollment communications")),{isValid:0===t.length,missingFields:t,errors:o}};(0,l.useEffect)(()=>{a&&P(K(a))},[a]);let Q=e=>{j(e),q({name:e.name,relationship:e.relationship,dateOfBirth:e.dateOfBirth||"",gender:e.gender||"Male",ssn:e.ssn||""}),b(!0)},ee=async()=>{if(!D||!$.name.trim()){Y("Validation Error","Please fill in all required fields","error");return}S(!0);try{let e=(a?.details?.dependents||[]).map(e=>e._id===D._id?{...e,name:$.name.trim(),relationship:$.relationship,dateOfBirth:$.dateOfBirth?new Date($.dateOfBirth):e.dateOfBirth,gender:$.gender,ssn:$.ssn.trim()}:e);if(console.log("Updating dependent in API:",$),console.log("Updated dependents array:",e),!J())throw Error("User ID not found");let t=await H();if(!t)throw Error("Company admin ID not found");let o=await (0,O.GH)("/admin/update/employee",{adminId:t,updatedDetails:{name:a.name,email:a.email,details:{...a.details,dependents:e}}});if(o&&200===o.status)await X(),console.log("Dependent updated successfully"),b(!1),j(null);else throw Error("Failed to update dependent")}catch(e){console.error("Error updating dependent:",e),Y("Update Failed","Failed to update dependent. Please try again.","error")}finally{S(!1)}},et=async()=>{if(!z.firstName.trim()||!z.lastName.trim()||!z.dateOfBirth){Y("Validation Error","Please fill in all required fields (First Name, Last Name, Date of Birth)","error");return}S(!0);try{let e=`${z.firstName.trim()} ${z.middleName.trim()?z.middleName.trim()+" ":""}${z.lastName.trim()}`.trim(),t={_id:`temp_${Date.now()}_${Math.random().toString(36).substr(2,9)}`,name:e,relationship:z.relationship,dateOfBirth:z.dateOfBirth,gender:z.gender,ssn:z.ssn.trim()};c(e=>[...e,t]);let o=a?.details?.dependents||[],n={name:e,relationship:z.relationship,dateOfBirth:new Date(z.dateOfBirth).toISOString(),gender:z.gender,ssn:z.ssn.trim(),isStudent:!1,isDisabled:!1,isActive:!0},l=[...o,n];if(console.log("Adding dependent to API:",n),console.log("Updated dependents array:",l),!J())throw Error("User ID not found");let r=await H();if(!r)throw Error("Company admin ID not found");let i=await (0,O.GH)("/admin/update/employee",{adminId:r,updatedDetails:{name:a.name,email:a.email,details:{...a.details,dependents:l}}});if(i&&200===i.status)await X(),console.log("Dependent added successfully");else throw Error("Failed to update user");E({firstName:"",middleName:"",lastName:"",relationship:"Spouse",dateOfBirth:"",gender:"Male",ssn:""}),h(!1)}catch(e){console.error("Error adding dependent:",e),Y("Add Failed","Failed to add dependent. Please try again.","error"),c(e=>e.filter(e=>!e._id?.toString().startsWith("temp_")))}finally{S(!1)}},eo=async e=>{G("Remove Dependent","Are you sure you want to remove this dependent?",()=>en(e),"Remove","Cancel")},en=async e=>{S(!0);try{if(c(t=>t.filter(t=>t._id!==e)),e.toString().startsWith("temp_")){S(!1);return}let t=(a?.details?.dependents||[]).filter(t=>t._id!==e);if(console.log("Removing dependent from API, updated array:",t),!J())throw Error("User ID not found");let o=await H();if(!o)throw Error("Company admin ID not found");let n=await (0,O.GH)("/admin/update/employee",{adminId:o,updatedDetails:{name:a.name,email:a.email,details:{...a.details,dependents:t}}});if(n&&200===n.status)await X(),console.log("Dependent removed successfully");else throw Error("Failed to update user")}catch(o){console.error("Error removing dependent:",o),Y("Remove Failed","Failed to remove dependent. Please try again.","error");let t=a?.details?.dependents?.find(t=>t._id===e);if(t){let e={_id:t._id,name:t.name,relationship:t.relationship,dateOfBirth:t.dateOfBirth?new Date(t.dateOfBirth).toISOString().split("T")[0]:"",gender:t.gender};c(t=>[...t,e])}}finally{S(!1)}},el=()=>{E({firstName:"",middleName:"",lastName:"",relationship:"Spouse",dateOfBirth:"",gender:"Male",ssn:""}),h(!1)};return(0,n.jsxs)("div",{style:{display:"flex",flexDirection:"column",gap:"24px"},children:[(0,n.jsxs)("div",{style:{display:"flex",gap:"16px"},children:[n.jsx("div",{style:{width:"40px",height:"40px",borderRadius:"50%",display:"flex",alignItems:"center",justifyContent:"center",flexShrink:0,overflow:"hidden"},children:n.jsx(m.default,{src:"/brea.png",alt:"Brea - AI Benefits Assistant",width:40,height:40,style:{borderRadius:"50%"}})}),(0,n.jsxs)("div",{style:{backgroundColor:"#f9fafb",borderRadius:"8px",padding:"16px",maxWidth:"1000px"},children:[n.jsx("p",{style:{color:"#374151",lineHeight:"1.6",margin:0},children:"\uD83D\uDC68‍\uD83D\uDC69‍\uD83D\uDC67‍\uD83D\uDC66 Let's confirm your family members for coverage."}),(0,n.jsxs)("p",{style:{color:"#374151",lineHeight:"1.6",margin:"8px 0 0 0"},children:[e.includes("spouse")&&e.includes("children")?"I see you selected family coverage. Please review your spouse and children information below.":e.includes("spouse")?"I see you selected spouse coverage. Please review your spouse information below.":e.includes("children")?"I see you selected children coverage. Please review your children information below.":"I see you selected employee-only coverage. Let's make sure your profile is complete before proceeding."," ",e.length>0?"You can edit any details or add new family members if needed.":"You can add family members if your situation changes."]})]})]}),!I.isValid&&(0,n.jsxs)("div",{style:{backgroundColor:"#fef3c7",border:"1px solid #f59e0b",borderRadius:"8px",padding:"16px",marginBottom:"24px",display:"flex",alignItems:"flex-start",gap:"12px"},children:[n.jsx(T,{size:20,style:{color:"#f59e0b",marginTop:"2px",flexShrink:0}}),(0,n.jsxs)("div",{style:{flex:1},children:[n.jsx("h4",{style:{fontSize:"14px",fontWeight:"600",color:"#92400e",margin:"0 0 8px 0"},children:"Complete Your Profile to Continue"}),n.jsx("p",{style:{fontSize:"14px",color:"#92400e",margin:"0 0 12px 0",lineHeight:"1.5"},children:"The following required information is missing from your profile:"}),n.jsx("ul",{style:{margin:"0 0 16px 0",paddingLeft:"20px",color:"#92400e",fontSize:"14px"},children:I.missingFields.map((e,t)=>n.jsx("li",{style:{marginBottom:"4px"},children:e},t))}),(0,n.jsxs)("button",{onClick:()=>k(!0),style:{display:"flex",alignItems:"center",gap:"8px",padding:"8px 16px",backgroundColor:"#f59e0b",color:"white",border:"none",borderRadius:"6px",fontSize:"14px",fontWeight:"500",cursor:"pointer",transition:"background-color 0.2s"},onMouseOver:e=>e.currentTarget.style.backgroundColor="#d97706",onMouseOut:e=>e.currentTarget.style.backgroundColor="#f59e0b",children:[n.jsx(R,{size:16}),"Complete Profile"]})]})]}),I.isValid&&(0,n.jsxs)("div",{style:{backgroundColor:"#f0fdf4",border:"1px solid #16a34a",borderRadius:"8px",padding:"16px",marginBottom:"24px",display:"flex",alignItems:"center",gap:"12px"},children:[n.jsx(u,{size:20,style:{color:"#16a34a"}}),n.jsx("div",{children:n.jsx("p",{style:{fontSize:"14px",color:"#16a34a",margin:0,fontWeight:"500"},children:"Your profile is complete and ready for enrollment!"})})]}),(0,n.jsxs)("div",{style:{backgroundColor:"white",borderRadius:"8px",border:"1px solid #e5e7eb",padding:"24px",boxShadow:"0 1px 3px 0 rgba(0, 0, 0, 0.1)"},children:[(0,n.jsxs)("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",marginBottom:"24px"},children:[(0,n.jsxs)("div",{style:{display:"flex",alignItems:"center",gap:"8px"},children:[n.jsx(s,{style:{width:"24px",height:"24px",color:"#3b82f6"}}),n.jsx("h2",{style:{fontSize:"20px",fontWeight:"600",color:"#111827",margin:0},children:e.length>0?"Confirm Family Members":"Profile & Coverage Confirmation"})]}),(0,n.jsxs)("div",{style:{display:"flex",gap:"12px"},children:[(0,n.jsxs)("button",{onClick:()=>k(!0),style:{display:"flex",alignItems:"center",gap:"8px",padding:"8px 16px",backgroundColor:"white",color:"#374151",border:"1px solid #d1d5db",borderRadius:"8px",cursor:"pointer",fontSize:"14px",fontWeight:"500"},children:[n.jsx(A,{size:16}),"Edit Profile"]}),(0,n.jsxs)("button",{onClick:()=>h(!0),style:{display:"flex",alignItems:"center",gap:"8px",padding:"8px 16px",backgroundColor:"#000000",color:"white",border:"none",borderRadius:"8px",cursor:"pointer",fontSize:"14px",fontWeight:"500"},children:[n.jsx(B,{size:16}),"Add Dependent"]})]})]}),n.jsx("div",{style:{border:"1px solid #e5e7eb",borderRadius:"8px",padding:"16px",marginBottom:"16px",backgroundColor:"#f8fafc"},children:(0,n.jsxs)("div",{style:{display:"flex",alignItems:"center",gap:"12px"},children:[n.jsx(F,{style:{width:"20px",height:"20px",color:"#6b7280"}}),(0,n.jsxs)("div",{children:[(0,n.jsxs)("h3",{style:{fontSize:"16px",fontWeight:"600",color:"#111827",margin:0},children:[a?.name||"Employee"," (Myself)"]}),n.jsx("p",{style:{fontSize:"14px",color:"#6b7280",margin:"4px 0 0 0"},children:"Employee"})]})]})}),p.map(e=>(0,n.jsxs)("div",{style:{border:"1px solid #e5e7eb",borderRadius:"8px",padding:"16px",marginBottom:"16px",position:"relative"},children:[n.jsx("button",{onClick:()=>eo(e._id),disabled:C,style:{position:"absolute",top:"12px",right:"12px",background:"none",border:"none",cursor:C?"not-allowed":"pointer",padding:"4px",borderRadius:"4px",display:"flex",alignItems:"center",justifyContent:"center",opacity:C?.5:1},onMouseOver:e=>!C&&(e.currentTarget.style.backgroundColor="#fee2e2"),onMouseOut:e=>e.currentTarget.style.backgroundColor="transparent",children:n.jsx(N,{style:{width:"16px",height:"16px",color:"#dc2626"}})}),n.jsx("div",{style:{display:"flex",alignItems:"center",paddingRight:"32px"},children:(0,n.jsxs)("div",{style:{display:"flex",alignItems:"center",gap:"12px"},children:[n.jsx(W,{style:{width:"20px",height:"20px",color:"#ec4899"}}),(0,n.jsxs)("div",{children:[(0,n.jsxs)("h3",{style:{fontSize:"16px",fontWeight:"600",color:"#111827",margin:0,display:"flex",alignItems:"center",gap:"8px"},children:[e.name," (",e.relationship,")",n.jsx("button",{onClick:()=>Q(e),style:{background:"none",border:"none",cursor:"pointer",padding:"2px"},title:"Edit dependent details",children:n.jsx(A,{style:{width:"14px",height:"14px",color:"#6b7280"}})})]}),(0,n.jsxs)("p",{style:{fontSize:"14px",color:"#6b7280",margin:"4px 0 0 0"},children:[e.relationship,e.dateOfBirth&&` • Born ${new Date(e.dateOfBirth).toLocaleDateString()}`,e.gender&&` • ${e.gender}`]})]})]})})]},e._id))]}),f&&n.jsx("div",{style:{position:"fixed",top:0,left:0,right:0,bottom:0,backgroundColor:"rgba(0, 0, 0, 0.5)",display:"flex",alignItems:"center",justifyContent:"center",zIndex:1e3},children:(0,n.jsxs)("div",{style:{backgroundColor:"white",borderRadius:"12px",padding:"24px",width:"90%",maxWidth:"400px",boxShadow:"0 20px 25px -5px rgba(0, 0, 0, 0.1)",position:"relative"},children:[n.jsx("button",{onClick:el,style:{position:"absolute",top:"16px",right:"16px",background:"none",border:"none",cursor:"pointer",padding:"4px",borderRadius:"4px"},children:n.jsx(v.Z,{size:20,style:{color:"#6b7280"}})}),n.jsx("h3",{style:{fontSize:"18px",fontWeight:"600",color:"#111827",margin:"0 0 20px 0"},children:"Add Family Member"}),(0,n.jsxs)("div",{style:{display:"grid",gridTemplateColumns:"1fr 1fr 1fr",gap:"12px",marginBottom:"16px"},children:[(0,n.jsxs)("div",{children:[n.jsx("label",{style:{display:"block",fontSize:"14px",fontWeight:"500",color:"#374151",marginBottom:"6px"},children:"First Name *"}),n.jsx("input",{type:"text",value:z.firstName,onChange:e=>E(t=>({...t,firstName:e.target.value})),placeholder:"First name",style:{width:"100%",padding:"10px 12px",border:"1px solid #d1d5db",borderRadius:"6px",fontSize:"14px",outline:"none",boxSizing:"border-box",backgroundColor:"white",color:"#111827"},onFocus:e=>e.target.style.borderColor="#3b82f6",onBlur:e=>e.target.style.borderColor="#d1d5db"})]}),(0,n.jsxs)("div",{children:[n.jsx("label",{style:{display:"block",fontSize:"14px",fontWeight:"500",color:"#374151",marginBottom:"6px"},children:"Middle Name"}),n.jsx("input",{type:"text",value:z.middleName,onChange:e=>E(t=>({...t,middleName:e.target.value})),placeholder:"Middle name",style:{width:"100%",padding:"10px 12px",border:"1px solid #d1d5db",borderRadius:"6px",fontSize:"14px",outline:"none",boxSizing:"border-box",backgroundColor:"white",color:"#111827"},onFocus:e=>e.target.style.borderColor="#3b82f6",onBlur:e=>e.target.style.borderColor="#d1d5db"})]}),(0,n.jsxs)("div",{children:[n.jsx("label",{style:{display:"block",fontSize:"14px",fontWeight:"500",color:"#374151",marginBottom:"6px"},children:"Last Name *"}),n.jsx("input",{type:"text",value:z.lastName,onChange:e=>E(t=>({...t,lastName:e.target.value})),placeholder:"Last name",style:{width:"100%",padding:"10px 12px",border:"1px solid #d1d5db",borderRadius:"6px",fontSize:"14px",outline:"none",boxSizing:"border-box",backgroundColor:"white",color:"#111827"},onFocus:e=>e.target.style.borderColor="#3b82f6",onBlur:e=>e.target.style.borderColor="#d1d5db"})]})]}),(0,n.jsxs)("div",{style:{marginBottom:"16px"},children:[n.jsx("label",{style:{display:"block",fontSize:"14px",fontWeight:"500",color:"#374151",marginBottom:"6px"},children:"Relationship *"}),(0,n.jsxs)("select",{value:z.relationship,onChange:e=>E(t=>({...t,relationship:e.target.value})),style:{width:"100%",padding:"10px 12px",border:"1px solid #d1d5db",borderRadius:"6px",fontSize:"14px",outline:"none",backgroundColor:"white",color:"#111827",boxSizing:"border-box"},children:[n.jsx("option",{value:"Spouse",children:"Spouse"}),n.jsx("option",{value:"Domestic Partner",children:"Domestic Partner"}),n.jsx("option",{value:"Child",children:"Child"}),n.jsx("option",{value:"Stepchild",children:"Stepchild"}),n.jsx("option",{value:"Adopted Child",children:"Adopted Child"})]})]}),(0,n.jsxs)("div",{style:{marginBottom:"16px"},children:[n.jsx("label",{style:{display:"block",fontSize:"14px",fontWeight:"500",color:"#374151",marginBottom:"6px"},children:"Gender *"}),(0,n.jsxs)("select",{value:z.gender,onChange:e=>E(t=>({...t,gender:e.target.value})),style:{width:"100%",padding:"10px 12px",border:"1px solid #d1d5db",borderRadius:"6px",fontSize:"14px",outline:"none",backgroundColor:"white",color:"#111827",boxSizing:"border-box"},children:[n.jsx("option",{value:"Male",children:"Male"}),n.jsx("option",{value:"Female",children:"Female"}),n.jsx("option",{value:"Other",children:"Other"}),n.jsx("option",{value:"Prefer not to say",children:"Prefer not to say"})]})]}),(0,n.jsxs)("div",{style:{marginBottom:"24px"},children:[n.jsx("label",{style:{display:"block",fontSize:"14px",fontWeight:"500",color:"#374151",marginBottom:"6px"},children:"Date of Birth *"}),n.jsx("input",{type:"date",value:z.dateOfBirth,onChange:e=>E(t=>({...t,dateOfBirth:e.target.value})),style:{width:"100%",padding:"10px 12px",border:"1px solid #d1d5db",borderRadius:"6px",fontSize:"14px",outline:"none",boxSizing:"border-box",backgroundColor:"white",color:"#111827",colorScheme:"light",WebkitAppearance:"none",MozAppearance:"textfield"},onFocus:e=>e.target.style.borderColor="#3b82f6",onBlur:e=>e.target.style.borderColor="#d1d5db"})]}),(0,n.jsxs)("div",{style:{marginBottom:"24px"},children:[n.jsx("label",{style:{display:"block",fontSize:"14px",fontWeight:"500",color:"#374151",marginBottom:"6px"},children:"Social Security Number (Optional)"}),n.jsx("input",{type:"text",value:z.ssn,onChange:e=>E(t=>({...t,ssn:e.target.value})),placeholder:"XXX-XX-XXXX",maxLength:11,style:{width:"100%",padding:"10px 12px",border:"1px solid #d1d5db",borderRadius:"6px",fontSize:"14px",outline:"none",boxSizing:"border-box",backgroundColor:"white",color:"#111827"},onFocus:e=>e.target.style.borderColor="#3b82f6",onBlur:e=>e.target.style.borderColor="#d1d5db"})]}),(0,n.jsxs)("div",{style:{display:"flex",gap:"12px",justifyContent:"flex-end"},children:[n.jsx("button",{onClick:el,style:{padding:"10px 20px",backgroundColor:"white",border:"1px solid #d1d5db",borderRadius:"6px",color:"#374151",cursor:"pointer",fontSize:"14px",fontWeight:"500"},children:"Cancel"}),n.jsx("button",{onClick:et,disabled:!z.firstName.trim()||!z.lastName.trim()||!z.dateOfBirth||C,style:{padding:"10px 20px",backgroundColor:z.firstName.trim()&&z.lastName.trim()&&z.dateOfBirth&&!C?"#000000":"#d1d5db",border:"none",borderRadius:"6px",color:"white",cursor:z.firstName.trim()&&z.lastName.trim()&&z.dateOfBirth&&!C?"pointer":"not-allowed",fontSize:"14px",fontWeight:"500"},children:C?"Adding...":"Add Member"})]})]})}),y&&n.jsx("div",{style:{position:"fixed",top:0,left:0,right:0,bottom:0,backgroundColor:"rgba(0, 0, 0, 0.5)",display:"flex",alignItems:"center",justifyContent:"center",zIndex:1e3},children:(0,n.jsxs)("div",{style:{backgroundColor:"white",borderRadius:"12px",padding:"24px",width:"90%",maxWidth:"500px",maxHeight:"90vh",overflow:"auto",boxShadow:"0 20px 25px -5px rgba(0, 0, 0, 0.1)"},children:[(0,n.jsxs)("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",marginBottom:"20px"},children:[n.jsx("h3",{style:{fontSize:"18px",fontWeight:"600",color:"#111827",margin:0},children:"Edit Dependent Details"}),n.jsx("button",{onClick:()=>{b(!1),j(null)},style:{background:"none",border:"none",cursor:"pointer",padding:"4px"},children:n.jsx(v.Z,{style:{width:"20px",height:"20px",color:"#6b7280"}})})]}),(0,n.jsxs)("div",{style:{display:"flex",flexDirection:"column",gap:"16px"},children:[(0,n.jsxs)("div",{children:[n.jsx("label",{style:{display:"block",fontSize:"14px",fontWeight:"500",color:"#374151",marginBottom:"6px"},children:"Full Name *"}),n.jsx("input",{type:"text",value:$.name,onChange:e=>q(t=>({...t,name:e.target.value})),placeholder:"Enter full name",style:{width:"100%",padding:"10px 12px",border:"1px solid #d1d5db",borderRadius:"6px",fontSize:"14px",outline:"none",boxSizing:"border-box",backgroundColor:"white",color:"#111827"}})]}),(0,n.jsxs)("div",{children:[n.jsx("label",{style:{display:"block",fontSize:"14px",fontWeight:"500",color:"#374151",marginBottom:"6px"},children:"Relationship *"}),(0,n.jsxs)("select",{value:$.relationship,onChange:e=>q(t=>({...t,relationship:e.target.value})),style:{width:"100%",padding:"10px 12px",border:"1px solid #d1d5db",borderRadius:"6px",fontSize:"14px",outline:"none",boxSizing:"border-box",backgroundColor:"white",color:"#111827"},children:[n.jsx("option",{value:"Spouse",children:"Spouse"}),n.jsx("option",{value:"Domestic Partner",children:"Domestic Partner"}),n.jsx("option",{value:"Child",children:"Child"}),n.jsx("option",{value:"Stepchild",children:"Stepchild"}),n.jsx("option",{value:"Adopted Child",children:"Adopted Child"})]})]}),(0,n.jsxs)("div",{children:[n.jsx("label",{style:{display:"block",fontSize:"14px",fontWeight:"500",color:"#374151",marginBottom:"6px"},children:"Date of Birth"}),n.jsx("input",{type:"date",value:$.dateOfBirth,onChange:e=>q(t=>({...t,dateOfBirth:e.target.value})),style:{width:"100%",padding:"10px 12px",border:"1px solid #d1d5db",borderRadius:"6px",fontSize:"14px",outline:"none",boxSizing:"border-box",backgroundColor:"white",color:"#111827"}})]}),(0,n.jsxs)("div",{children:[n.jsx("label",{style:{display:"block",fontSize:"14px",fontWeight:"500",color:"#374151",marginBottom:"6px"},children:"Gender"}),(0,n.jsxs)("select",{value:$.gender,onChange:e=>q(t=>({...t,gender:e.target.value})),style:{width:"100%",padding:"10px 12px",border:"1px solid #d1d5db",borderRadius:"6px",fontSize:"14px",outline:"none",boxSizing:"border-box",backgroundColor:"white",color:"#111827"},children:[n.jsx("option",{value:"Male",children:"Male"}),n.jsx("option",{value:"Female",children:"Female"}),n.jsx("option",{value:"Other",children:"Other"}),n.jsx("option",{value:"Prefer not to say",children:"Prefer not to say"})]})]}),(0,n.jsxs)("div",{children:[n.jsx("label",{style:{display:"block",fontSize:"14px",fontWeight:"500",color:"#374151",marginBottom:"6px"},children:"Social Security Number (Optional)"}),n.jsx("input",{type:"text",value:$.ssn,onChange:e=>q(t=>({...t,ssn:e.target.value})),placeholder:"XXX-XX-XXXX",maxLength:11,style:{width:"100%",padding:"10px 12px",border:"1px solid #d1d5db",borderRadius:"6px",fontSize:"14px",outline:"none",boxSizing:"border-box",backgroundColor:"white",color:"#111827"}})]})]}),(0,n.jsxs)("div",{style:{display:"flex",gap:"12px",marginTop:"24px",justifyContent:"flex-end"},children:[n.jsx("button",{onClick:()=>{b(!1),j(null)},style:{padding:"10px 20px",backgroundColor:"white",border:"1px solid #d1d5db",borderRadius:"6px",fontSize:"14px",fontWeight:"500",color:"#374151",cursor:"pointer"},disabled:C,children:"Cancel"}),n.jsx("button",{onClick:ee,style:{padding:"10px 20px",backgroundColor:"#111827",border:"none",borderRadius:"6px",fontSize:"14px",fontWeight:"500",color:"white",cursor:C?"not-allowed":"pointer",opacity:C?.6:1},disabled:C,children:C?"Updating...":"Update Dependent"})]})]})}),w&&n.jsx(M.Z,{open:w,onClose:async()=>{k(!1),await X(),i&&i()}}),n.jsx(L,{isOpen:V,onClose:()=>Z(!1),onConfirm:U.onConfirm,title:U.title,message:U.message,type:U.type,confirmText:U.confirmText,cancelText:U.cancelText})]})};var V=o(18751);let Z=({plans:e,selectedPlans:t=[],onClose:o})=>{let[r,i]=(0,l.useState)(!0),[a,s]=(0,l.useState)(t.slice(0,3)),d=()=>{i(!1),o&&o()},p=e.length>0?e:[{id:"1",name:"HealthFirst PPO Gold",type:"medical",tier:"Gold",monthlyPremium:450,deductible:1e3,outOfPocketMax:5e3,features:["Nationwide network","No referrals needed","Prescription coverage","Mental health coverage"],network:"PPO Network"},{id:"2",name:"HealthFirst HMO Silver",type:"medical",tier:"Silver",monthlyPremium:320,deductible:2e3,outOfPocketMax:7e3,features:["Local network","Primary care referrals","Prescription coverage","Preventive care"],network:"HMO Network"},{id:"3",name:"DentalCare Plus",type:"dental",tier:"Gold",monthlyPremium:45,deductible:50,outOfPocketMax:1500,features:["Cleanings covered 100%","Major work 50% coverage","Orthodontics included","No waiting period"],network:"Dental Network"}],c=p.filter(e=>a.includes(e.id)),x=e=>{a.includes(e)?s(t=>t.filter(t=>t!==e)):a.length<3&&s(t=>[...t,e])};return r?(0,n.jsxs)(n.Fragment,{children:[n.jsx("div",{style:{position:"fixed",inset:0,backgroundColor:"rgba(0, 0, 0, 0.5)",zIndex:50},onClick:d}),(0,n.jsxs)("div",{style:{position:"fixed",top:"50%",left:"50%",transform:"translate(-50%, -50%)",width:"95vw",maxWidth:"1200px",maxHeight:"90vh",backgroundColor:"white",borderRadius:"16px",boxShadow:"0 25px 50px rgba(0, 0, 0, 0.25)",zIndex:51,overflow:"hidden"},children:[(0,n.jsxs)("div",{style:{display:"flex",alignItems:"center",justifyContent:"space-between",padding:"20px 24px",borderBottom:"1px solid #e5e7eb"},children:[(0,n.jsxs)("div",{style:{display:"flex",alignItems:"center",gap:"8px"},children:[n.jsx(V.Z,{size:20,style:{color:"#2563eb"}}),n.jsx("h2",{style:{fontSize:"18px",fontWeight:"600",color:"#111827",margin:0,fontFamily:"sans-serif"},children:"Compare Plans"})]}),n.jsx("button",{onClick:d,style:{padding:"8px",backgroundColor:"transparent",border:"none",borderRadius:"8px",cursor:"pointer",color:"#6b7280"},children:n.jsx(v.Z,{size:20})})]}),(0,n.jsxs)("div",{style:{padding:"16px 24px",backgroundColor:"#f9fafb",borderBottom:"1px solid #e5e7eb"},children:[(0,n.jsxs)("p",{style:{fontSize:"14px",color:"#6b7280",margin:"0 0 12px 0",fontFamily:"sans-serif"},children:["Select up to 3 plans to compare (",a.length,"/3 selected):"]}),n.jsx("div",{style:{display:"flex",gap:"8px",flexWrap:"wrap"},children:p.map(e=>n.jsx("button",{onClick:()=>x(e.id),disabled:!a.includes(e.id)&&a.length>=3,style:{padding:"6px 12px",backgroundColor:a.includes(e.id)?"#2563eb":"white",color:a.includes(e.id)?"white":"#374151",border:"1px solid #d1d5db",borderRadius:"6px",fontSize:"12px",fontWeight:"500",cursor:a.includes(e.id)||a.length<3?"pointer":"not-allowed",opacity:!a.includes(e.id)&&a.length>=3?.5:1,fontFamily:"sans-serif"},children:e.name},e.id))})]}),n.jsx("div",{style:{padding:"24px",overflowX:"auto",maxHeight:"500px",overflowY:"auto"},children:0===c.length?(0,n.jsxs)("div",{style:{textAlign:"center",padding:"40px",color:"#6b7280"},children:[n.jsx(V.Z,{size:48,style:{margin:"0 auto 16px",opacity:.5}}),n.jsx("p",{style:{fontSize:"16px",fontWeight:"500",margin:"0 0 8px 0",fontFamily:"sans-serif"},children:"Select plans to compare"}),n.jsx("p",{style:{fontSize:"14px",margin:0,fontFamily:"sans-serif"},children:"Choose up to 3 plans from the list above to see a detailed comparison."})]}):n.jsx("div",{style:{minWidth:"600px"},children:(0,n.jsxs)("table",{style:{width:"100%",borderCollapse:"collapse"},children:[n.jsx("thead",{children:(0,n.jsxs)("tr",{children:[n.jsx("th",{style:{padding:"12px",textAlign:"left",borderBottom:"2px solid #e5e7eb",fontSize:"14px",fontWeight:"600",color:"#111827",fontFamily:"sans-serif"},children:"Feature"}),c.map(e=>n.jsx("th",{style:{padding:"12px",textAlign:"center",borderBottom:"2px solid #e5e7eb",fontSize:"14px",fontWeight:"600",color:"#111827",fontFamily:"sans-serif"},children:e.name},e.id))]})}),(0,n.jsxs)("tbody",{children:[(0,n.jsxs)("tr",{children:[n.jsx("td",{style:{padding:"12px",borderBottom:"1px solid #e5e7eb",fontSize:"14px",fontWeight:"500",color:"#374151",fontFamily:"sans-serif"},children:"Monthly Premium"}),c.map(e=>(0,n.jsxs)("td",{style:{padding:"12px",textAlign:"center",borderBottom:"1px solid #e5e7eb",fontSize:"14px",color:"#111827",fontFamily:"sans-serif"},children:["$",e.monthlyPremium]},e.id))]}),(0,n.jsxs)("tr",{children:[n.jsx("td",{style:{padding:"12px",borderBottom:"1px solid #e5e7eb",fontSize:"14px",fontWeight:"500",color:"#374151",fontFamily:"sans-serif"},children:"Deductible"}),c.map(e=>(0,n.jsxs)("td",{style:{padding:"12px",textAlign:"center",borderBottom:"1px solid #e5e7eb",fontSize:"14px",color:"#111827",fontFamily:"sans-serif"},children:["$",e.deductible.toLocaleString()]},e.id))]}),(0,n.jsxs)("tr",{children:[n.jsx("td",{style:{padding:"12px",borderBottom:"1px solid #e5e7eb",fontSize:"14px",fontWeight:"500",color:"#374151",fontFamily:"sans-serif"},children:"Out-of-Pocket Max"}),c.map(e=>(0,n.jsxs)("td",{style:{padding:"12px",textAlign:"center",borderBottom:"1px solid #e5e7eb",fontSize:"14px",color:"#111827",fontFamily:"sans-serif"},children:["$",e.outOfPocketMax.toLocaleString()]},e.id))]}),(0,n.jsxs)("tr",{children:[n.jsx("td",{style:{padding:"12px",borderBottom:"1px solid #e5e7eb",fontSize:"14px",fontWeight:"500",color:"#374151",fontFamily:"sans-serif"},children:"Network"}),c.map(e=>n.jsx("td",{style:{padding:"12px",textAlign:"center",borderBottom:"1px solid #e5e7eb",fontSize:"14px",color:"#111827",fontFamily:"sans-serif"},children:e.network},e.id))]})]})]})})})]})]}):(0,n.jsxs)("button",{onClick:()=>i(!0),style:{display:"flex",alignItems:"center",gap:"8px",padding:"8px 16px",color:"#374151",border:"1px solid #d1d5db",borderRadius:"8px",backgroundColor:"white",cursor:"pointer",transition:"background-color 0.2s ease",fontSize:"14px",fontWeight:"500",fontFamily:"sans-serif"},onMouseOver:e=>e.currentTarget.style.backgroundColor="#f9fafb",onMouseOut:e=>e.currentTarget.style.backgroundColor="white",children:[n.jsx(V.Z,{size:16,style:{color:"#6b7280"}}),"Compare Plans"]})},U=({planName:e,availableTiers:t,preferredTier:o,onTierSelect:r,selectedTier:i,category:a})=>{let[s,d]=(0,l.useState)(""),[p,c]=(0,l.useState)(!1),x=t.some(e=>e.tierName===o),g=x?o:t.find(e=>"Employee Only"===e.tierName)?.tierName||t[0]?.tierName||"";(0,l.useEffect)(()=>{if(i)d(i);else if(g){d(g);let e=t.find(e=>e.tierName===g);e&&r(e)}!x&&o&&t.length>0&&c(!0)},[i,g,x,o,t,r]);let u=e=>{d(e),r(t.find(t=>t.tierName===e)||null),c(!1)};return 0===t.length?n.jsx("div",{style:{backgroundColor:"#fef2f2",border:"1px solid #fecaca",borderRadius:"8px",padding:"16px",marginTop:"12px"},children:n.jsx("p",{style:{color:"#dc2626",margin:0,fontSize:"14px"},children:"No coverage tiers available for this plan."})}):(0,n.jsxs)("div",{style:{marginTop:"16px"},children:[p&&n.jsx("div",{style:{backgroundColor:"#fef3c7",border:"1px solid #fbbf24",borderRadius:"8px",padding:"12px",marginBottom:"16px"},children:(0,n.jsxs)("p",{style:{color:"#92400e",margin:0,fontSize:"14px",fontWeight:"500"},children:["⚠️ Your preferred coverage tier “",o,"” is not available for this ",a.toLowerCase()," plan. Please select from the available options below:"]})}),(0,n.jsxs)("div",{style:{backgroundColor:"white",border:"1px solid #e5e7eb",borderRadius:"12px",padding:"20px",boxShadow:"0 1px 3px rgba(0, 0, 0, 0.1)"},children:[(0,n.jsxs)("h4",{style:{fontSize:"16px",fontWeight:"600",color:"#111827",margin:"0 0 16px 0",display:"flex",alignItems:"center",gap:"8px"},children:[n.jsx("span",{style:{width:"24px",height:"24px",backgroundColor:"#3b82f6",borderRadius:"6px",display:"flex",alignItems:"center",justifyContent:"center",fontSize:"12px",color:"white",fontWeight:"700"},children:"\uD83D\uDCB0"}),"Choose Coverage for ",e]}),n.jsx("div",{style:{display:"flex",flexDirection:"column",gap:"12px"},children:t.map(e=>{let t=e.tierName===o,l=s===e.tierName;return(0,n.jsxs)("div",{onClick:()=>u(e.tierName),style:{position:"relative",display:"flex",alignItems:"center",justifyContent:"space-between",padding:"16px 20px",backgroundColor:l?"#f0f9ff":"white",border:l?"2px solid #0ea5e9":t?"2px solid #10b981":"1px solid #e5e7eb",borderRadius:"12px",cursor:"pointer",transition:"all 0.3s ease",boxShadow:l?"0 4px 12px rgba(14, 165, 233, 0.15)":t?"0 4px 12px rgba(16, 185, 129, 0.15)":"0 1px 3px rgba(0, 0, 0, 0.05)",transform:l?"translateY(-2px)":"none"},onMouseEnter:e=>{l||(e.currentTarget.style.backgroundColor="#f8fafc",e.currentTarget.style.transform="translateY(-1px)",e.currentTarget.style.boxShadow="0 4px 12px rgba(0, 0, 0, 0.1)")},onMouseLeave:e=>{l||(e.currentTarget.style.backgroundColor="white",e.currentTarget.style.transform="none",e.currentTarget.style.boxShadow="0 1px 3px rgba(0, 0, 0, 0.05)")},children:[t&&n.jsx("div",{style:{position:"absolute",top:"-8px",right:"16px",backgroundColor:"#10b981",color:"white",fontSize:"11px",fontWeight:"600",padding:"4px 8px",borderRadius:"6px",textTransform:"uppercase",letterSpacing:"0.5px"},children:"⭐ Recommended"}),l&&n.jsx("div",{style:{position:"absolute",top:"12px",right:"12px",width:"24px",height:"24px",backgroundColor:"#0ea5e9",borderRadius:"50%",display:"flex",alignItems:"center",justifyContent:"center",color:"white",fontSize:"14px",fontWeight:"700"},children:"✓"}),(0,n.jsxs)("div",{style:{display:"flex",alignItems:"center",gap:"16px",flex:1},children:[n.jsx("div",{style:{width:"20px",height:"20px",borderRadius:"50%",border:l?"6px solid #0ea5e9":"2px solid #d1d5db",backgroundColor:l?"white":"transparent",transition:"all 0.2s ease"}}),(0,n.jsxs)("div",{style:{flex:1},children:[n.jsx("div",{style:{fontSize:"16px",fontWeight:"600",color:l?"#0ea5e9":"#111827",marginBottom:"4px"},children:e.tierName}),(0,n.jsxs)("div",{style:{fontSize:"13px",color:"#6b7280",display:"flex",gap:"16px"},children:[(0,n.jsxs)("span",{children:["Total: $",e.totalCost.toFixed(2)]}),(0,n.jsxs)("span",{children:["Employer: $",e.employerCost.toFixed(2)]})]})]})]}),(0,n.jsxs)("div",{style:{textAlign:"right",marginRight:l?"32px":"0"},children:[(0,n.jsxs)("div",{style:{fontSize:"20px",fontWeight:"700",color:l?"#0ea5e9":"#111827",marginBottom:"2px"},children:["$",e.employeeCost.toFixed(2)]}),n.jsx("div",{style:{fontSize:"12px",color:"#6b7280",fontWeight:"500"},children:"per paycheck"})]})]},e.tierName)})}),s&&n.jsx("div",{style:{marginTop:"16px",padding:"16px",background:"linear-gradient(135deg, #ecfdf5 0%, #f0fdf4 100%)",border:"1px solid #10b981",borderRadius:"12px",boxShadow:"0 2px 8px rgba(16, 185, 129, 0.1)"},children:(0,n.jsxs)("div",{style:{display:"flex",alignItems:"center",gap:"12px"},children:[n.jsx("div",{style:{width:"32px",height:"32px",backgroundColor:"#10b981",borderRadius:"50%",display:"flex",alignItems:"center",justifyContent:"center",color:"white",fontSize:"16px",fontWeight:"700"},children:"✓"}),(0,n.jsxs)("div",{style:{flex:1},children:[(0,n.jsxs)("div",{style:{fontSize:"16px",fontWeight:"600",color:"#065f46",marginBottom:"4px"},children:[s," Selected"]}),(0,n.jsxs)("div",{style:{fontSize:"14px",color:"#047857",fontWeight:"500"},children:["You pay $",t.find(e=>e.tierName===s)?.employeeCost.toFixed(2)," per paycheck"]})]})]})})]})]})},_={SELECTED_PLANS:"selectedPlansData",WAIVED_COVERAGES:"waivedCoveragesData",PREFERRED_TIER:"selectedCoverageTier",ENROLLMENT_SUMMARY:"enrollmentSummarySnapshot"},Y=e=>{try{let t=[...G().filter(t=>t.category!==e.category),e];localStorage.setItem(_.SELECTED_PLANS,JSON.stringify(t));let o=`selected${e.category.replace(/\s+/g,"")}Plan`;localStorage.setItem(o,JSON.stringify({id:e.planId,name:e.planName,cost:e.selectedTier.employeeCost,selectedTier:e.selectedTier,originalPlan:e.originalPlan})),console.log(`📦 Stored plan for ${e.category}:`,e)}catch(e){console.error("Error storing selected plan:",e)}},G=()=>{try{let e=localStorage.getItem(_.SELECTED_PLANS);return e?JSON.parse(e):[]}catch(e){return console.error("Error getting stored plans:",e),[]}},J=e=>G().find(t=>t.category===e)||null,X=e=>{try{let t=[...K().filter(t=>t.category!==e.category),e];localStorage.setItem(_.WAIVED_COVERAGES,JSON.stringify(t));let o=`${e.category.toLowerCase()}Waived`,n=`${e.category.toLowerCase()}WaiveReason`;localStorage.setItem(o,"true"),localStorage.setItem(n,e.waiveReason),ee(e.category),console.log(`🚫 Stored waive for ${e.category}:`,e)}catch(e){console.error("Error storing waived coverage:",e)}},K=()=>{try{let e=localStorage.getItem(_.WAIVED_COVERAGES);return e?JSON.parse(e):[]}catch(e){return console.error("Error getting stored waives:",e),[]}},Q=e=>K().some(t=>t.category===e),ee=e=>{try{let t=G().filter(t=>t.category!==e);localStorage.setItem(_.SELECTED_PLANS,JSON.stringify(t));let o=`selected${e.replace(/\s+/g,"")}Plan`;localStorage.removeItem(o),console.log(`🗑️ Removed plan for ${e}`)}catch(e){console.error("Error removing selected plan:",e)}},et=e=>{try{let t=K().filter(t=>t.category!==e);localStorage.setItem(_.WAIVED_COVERAGES,JSON.stringify(t));let o=`${e.toLowerCase()}Waived`,n=`${e.toLowerCase()}WaiveReason`;localStorage.removeItem(o),localStorage.removeItem(n),console.log(`🗑️ Removed waive for ${e}`)}catch(e){console.error("Error removing waived coverage:",e)}},eo=()=>localStorage.getItem(_.PREFERRED_TIER)||"Employee Only",en=()=>{let e=G(),t=K(),o={selectedCoverageTier:eo(),enrollmentDate:new Date().toISOString(),dependents:[],selectedPlans:e,waivedCoverages:t};return e.forEach(e=>{let t=e.category.toLowerCase().replace(/\s+/g,"");o[`${t}Plan`]={id:e.planId,name:e.planName,planName:e.planName,cost:e.selectedTier.employeeCost,selectedTier:e.selectedTier,originalPlan:e.originalPlan}}),t.forEach(e=>{let t=e.category.toLowerCase().replace(/\s+/g,"");o[`${t}Waived`]=!0,o[`${t}WaiveReason`]=e.waiveReason}),o},el=(e={})=>{let t={...en(),...e,snapshotDate:new Date().toISOString()};return localStorage.setItem(_.ENROLLMENT_SUMMARY,JSON.stringify(t)),console.log("\uD83D\uDCF8 Created enrollment snapshot:",t),t},er=[{primary:"#1E90FF",accent:"#E6F0FF",name:"Blue"},{primary:"#32CD32",accent:"#E6FFE6",name:"Green"},{primary:"#FF8C00",accent:"#FFF3E6",name:"Orange"},{primary:"#800080",accent:"#F5E6FF",name:"Purple"}],ei=({category:e,plans:t,selectedCoverageTier:o,themeIndex:r,onPlanSelect:i})=>{let[a,s]=(0,l.useState)(null),[d,p]=(0,l.useState)(null),[c,x]=(0,l.useState)(!1),[g,f]=(0,l.useState)(!1),[h,b]=(0,l.useState)(!1),[v,D]=(0,l.useState)(""),[j,C]=(0,l.useState)(!1),w=eo(),[k,I]=(0,l.useState)(!1),[P,W]=(0,l.useState)({title:"",message:"",type:"alert",onConfirm:()=>{},confirmText:"OK",cancelText:"Cancel"}),z=er[r%er.length],E=e=>({Dental:"\uD83E\uDDB7",Vision:"\uD83D\uDC41️","Life Insurance":"\uD83D\uDEE1️","AD&D Insurance":"⚡",Medical:"\uD83C\uDFE5","Disability Insurance":"\uD83E\uDD1D","Voluntary Benefits":"\uD83D\uDCCB"})[e]||"\uD83D\uDCC4",T=t=>{if(a===t){s(null),p(null),ee(e),et(e),i(null);return}s(t),p(null),et(e)},R=(t||[]).map(e=>({id:e.id||e._id,name:e.name||e.planName,cost:e.cost||0,features:e.features||[],recommended:e.recommended||!1,originalPlan:e,coverageTiers:e.coverageTiers||[]})),A=R.filter(e=>e.coverageTiers&&0!==e.coverageTiers.length?(e.coverageTiers.some(e=>e.tierName===w)?console.log(`✅ Plan ${e.name} has preferred tier ${w}`):console.log(`⚠️ Plan ${e.name} does not have preferred tier ${w}, but has other tiers:`,e.coverageTiers.map(e=>e.tierName)),!0):(console.log(`⚠️ Plan ${e.name} has no coverage tiers, excluding from display`),!1));return(0,l.useEffect)(()=>{let t=J(e);if(Q(e))s("WAIVE");else if(t)s(t.planId),p(t.selectedTier);else{let t=`selected${e.replace(/\s+/g,"")}Plan`,o=`${e.toLowerCase()}Waived`;if("true"===localStorage.getItem(o))s("WAIVE");else{let o=localStorage.getItem(t);if(o)try{let e=JSON.parse(o);s(e.id)}catch(t){console.error(`Error parsing saved ${e} plan:`,t)}}}},[e]),(0,n.jsxs)("div",{style:{display:"flex",flexDirection:"column",gap:"24px",fontFamily:'"SF Pro", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif'},children:[(0,n.jsxs)("div",{style:{display:"flex",gap:"16px"},children:[n.jsx("div",{style:{width:"40px",height:"40px",borderRadius:"50%",display:"flex",alignItems:"center",justifyContent:"center",flexShrink:0,overflow:"hidden"},children:n.jsx(m.default,{src:"/brea.png",alt:"Brea - AI Benefits Assistant",width:40,height:40,style:{borderRadius:"50%"}})}),(0,n.jsxs)("div",{style:{backgroundColor:"#f9fafb",borderRadius:"8px",padding:"16px",maxWidth:"1000px"},children:[n.jsx("p",{style:{color:"#374151",lineHeight:"1.6",margin:0},children:{Dental:"\uD83D\uDE01 Now let's take care of your smile! Which dental plan works best for you?",Vision:"\uD83D\uDC41️ Let's help you see clearly! Which vision plan fits your needs?","Life Insurance":"\uD83D\uDEE1️ Let's protect your family's future! Which life insurance plan is right for you?","AD&D Insurance":"⚡ Additional protection for unexpected events. Which AD&D plan would you like?",Medical:"\uD83C\uDFE5 Let's find the perfect medical plan for your healthcare needs!","Disability Insurance":"\uD83E\uDD1D Protect your income with disability coverage. Which plan works for you?","Voluntary Benefits":"\uD83D\uDCCB Explore additional voluntary benefits to enhance your coverage."}[e]||`Let's choose your ${e.toLowerCase()} plan!`}),n.jsx("p",{style:{color:"#374151",lineHeight:"1.6",margin:"8px 0 0 0"},children:{Dental:"Even if you don't need dental care now, having coverage can save you money later.",Vision:"Regular eye exams and vision care are important for your overall health.","Life Insurance":"Life insurance provides financial security for your loved ones.","AD&D Insurance":"Accidental Death & Dismemberment insurance provides additional protection.",Medical:"Choose a plan that fits your healthcare needs and budget.","Disability Insurance":"Disability insurance helps replace income if you can't work due to illness or injury.","Voluntary Benefits":"These optional benefits can provide additional peace of mind."}[e]||`Select the ${e.toLowerCase()} coverage that best fits your needs.`})]})]}),(0,n.jsxs)("div",{style:{backgroundColor:"white",borderRadius:"8px",border:"1px solid #e5e7eb",padding:"24px",boxShadow:"0 1px 3px 0 rgba(0, 0, 0, 0.1)"},children:[(0,n.jsxs)("div",{style:{display:"flex",alignItems:"center",gap:"8px",marginBottom:"16px"},children:[n.jsx("span",{style:{fontSize:"18px"},children:E(e)}),(0,n.jsxs)("h2",{style:{fontSize:"20px",fontWeight:"600",color:"#111827",margin:0},children:["Smart ",e," Plan Selection"]})]}),(0,n.jsxs)("p",{style:{color:"#6b7280",marginBottom:"24px",margin:0},children:["Now let's find your perfect ",e.toLowerCase()," plan:"]}),n.jsx("div",{style:{display:"flex",flexDirection:"column",gap:"16px"},children:0===A.length?(0,n.jsxs)("div",{style:{textAlign:"center",padding:"40px 20px",backgroundColor:"#f9fafb",borderRadius:"8px",border:"2px dashed #e5e7eb"},children:[n.jsx("div",{style:{fontSize:"48px",marginBottom:"16px"},children:E(e)}),(0,n.jsxs)("h3",{style:{fontSize:"18px",fontWeight:"600",color:"#374151",margin:"0 0 8px 0"},children:["No ",e," Plans Available"]}),(0,n.jsxs)("p",{style:{color:"#6b7280",fontSize:"14px",margin:0,lineHeight:"1.5"},children:[0===R.length?`Your company hasn't set up any ${e.toLowerCase()} plan assignments yet.`:`No ${e.toLowerCase()} plans are available with proper coverage tier configurations.`,n.jsx("br",{}),"Please contact your HR administrator for more information."]})]}):(0,n.jsxs)(n.Fragment,{children:[A.map(e=>{let t=a===e.id;return(0,n.jsxs)("div",{style:{border:t?`2px solid ${z.primary}`:e.recommended?"2px solid #f59e0b":"2px solid #e5e7eb",borderRadius:"8px",padding:"20px",backgroundColor:t?z.accent:e.recommended?"#fef3e2":"#f9fafb",cursor:"pointer",transition:"all 0.2s ease"},onClick:()=>T(e.id),children:[(0,n.jsxs)("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"flex-start",marginBottom:"12px"},children:[(0,n.jsxs)("div",{children:[n.jsx("h3",{style:{fontSize:"18px",fontWeight:"600",color:"#111827",margin:0},children:e.name}),n.jsx("div",{style:{display:"flex",flexDirection:"column",alignItems:"flex-start",marginTop:"4px"},children:(()=>{let t=e.coverageTiers?.find(e=>e.tierName===w),o=t?t.employeeCost:e.cost,l=!!t;return(0,n.jsxs)("div",{children:[(0,n.jsxs)("div",{style:{display:"flex",alignItems:"baseline",gap:"4px"},children:[(0,n.jsxs)("span",{style:{fontSize:"24px",fontWeight:"700",color:l?"#111827":"#6b7280"},children:["$",o.toFixed(2)]}),n.jsx("span",{style:{color:"#6b7280",fontSize:"14px"},children:"/paycheck"})]}),l&&(0,n.jsxs)("div",{style:{fontSize:"12px",color:"#10b981",fontWeight:"500",marginTop:"2px"},children:["✓ ",w," available"]}),!l&&e.coverageTiers?.length>0&&n.jsx("div",{style:{fontSize:"11px",color:"#f59e0b",fontWeight:"500",marginTop:"2px"},children:"Starting from (tier selection required)"})]})})()})]}),e.recommended&&n.jsx("div",{style:{backgroundColor:"#f59e0b",color:"white",padding:"4px 8px",borderRadius:"12px",fontSize:"12px",fontWeight:"500"},children:"Recommended"})]}),n.jsx("div",{style:{display:"flex",flexDirection:"column",gap:"8px",marginBottom:"16px"},children:e.features.map((e,t)=>(0,n.jsxs)("div",{style:{display:"flex",alignItems:"center",gap:"8px"},children:[n.jsx(u,{style:{width:"16px",height:"16px",color:"#10b981",flexShrink:0}}),n.jsx("span",{style:{color:"#374151",fontSize:"14px"},children:e})]},t))}),n.jsx("button",{style:{width:"100%",backgroundColor:t?"#000000":"#f3f4f6",color:t?"white":"#6b7280",padding:"8px 16px",borderRadius:"6px",fontWeight:"500",border:"none",cursor:"pointer",fontSize:"14px"},children:t?"✓ Selected":"Select This Plan"})]},e.id)}),a&&"WAIVE"!==a&&n.jsx(U,{planName:A.find(e=>e.id===a)?.name||"",availableTiers:A.find(e=>e.id===a)?.coverageTiers||[],preferredTier:w,onTierSelect:t=>{if(a&&"WAIVE"!==a&&(p(t),t)){let o=A.find(e=>e.id===a);o&&(Y({planId:a,planName:o.name,category:e,selectedTier:t,originalPlan:o.originalPlan,selectionDate:new Date().toISOString()}),i({...o,cost:t.employeeCost,selectedTier:t}))}},selectedTier:d?.tierName,category:e}),(0,n.jsxs)("div",{style:{border:"WAIVE"===a?"2px solid #ef4444":"2px solid #e5e7eb",borderRadius:"8px",padding:"20px",backgroundColor:"WAIVE"===a?"#fef2f2":"#f9fafb",cursor:"pointer",transition:"all 0.2s ease"},onClick:()=>{if("WAIVE"===a){s(null),p(null),et(e),i(null);return}b(!0)},children:[(0,n.jsxs)("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"flex-start",marginBottom:"12px"},children:[(0,n.jsxs)("div",{children:[(0,n.jsxs)("h3",{style:{fontSize:"18px",fontWeight:"600",color:"#111827",margin:0},children:["Waive ",e," Coverage"]}),(0,n.jsxs)("div",{style:{display:"flex",alignItems:"baseline",gap:"4px",marginTop:"4px"},children:[n.jsx("span",{style:{fontSize:"24px",fontWeight:"700",color:"#111827"},children:"$0.00"}),n.jsx("span",{style:{color:"#6b7280",fontSize:"14px"},children:"/paycheck"})]})]}),n.jsx("div",{style:{backgroundColor:"#ef4444",color:"white",padding:"4px 8px",borderRadius:"12px",fontSize:"12px",fontWeight:"500"},children:"No Coverage"})]}),(0,n.jsxs)("div",{style:{display:"flex",flexDirection:"column",gap:"8px",marginBottom:"16px"},children:[(0,n.jsxs)("div",{style:{display:"flex",alignItems:"center",gap:"8px"},children:[n.jsx("span",{style:{color:"#ef4444",fontSize:"16px"},children:"⚠️"}),(0,n.jsxs)("span",{style:{color:"#374151",fontSize:"14px"},children:["No ",e.toLowerCase()," coverage or benefits"]})]}),(0,n.jsxs)("div",{style:{display:"flex",alignItems:"center",gap:"8px"},children:[n.jsx("span",{style:{color:"#ef4444",fontSize:"16px"},children:"⚠️"}),(0,n.jsxs)("span",{style:{color:"#374151",fontSize:"14px"},children:["You'll pay full cost for ",e.toLowerCase()," care"]})]}),(0,n.jsxs)("div",{style:{display:"flex",alignItems:"center",gap:"8px"},children:[n.jsx("span",{style:{color:"#ef4444",fontSize:"16px"},children:"⚠️"}),n.jsx("span",{style:{color:"#374151",fontSize:"14px"},children:"Can only enroll during next open enrollment"})]})]}),n.jsx("button",{style:{width:"100%",backgroundColor:"WAIVE"===a?"#ef4444":"#f3f4f6",color:"WAIVE"===a?"white":"#6b7280",padding:"8px 16px",borderRadius:"6px",fontWeight:"500",border:"none",cursor:"pointer",fontSize:"14px"},children:"WAIVE"===a?"✓ Coverage Waived":"Waive Coverage"})]})]})}),a&&(0,n.jsxs)("div",{style:{backgroundColor:"WAIVE"===a?"#fef2f2":z.accent,border:"WAIVE"===a?"1px solid #fecaca":`1px solid ${z.primary}40`,borderRadius:"8px",padding:"16px",marginTop:"16px"},children:[n.jsx("h3",{style:{fontSize:"16px",fontWeight:"600",color:"WAIVE"===a?"#dc2626":z.primary,margin:"0 0 12px 0"},children:"WAIVE"===a?`${e} Coverage Waived`:`Selected ${e} Plan`}),n.jsx("div",{style:{display:"flex",flexDirection:"column",gap:"8px"},children:"WAIVE"===a?(0,n.jsxs)("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",backgroundColor:"white",padding:"12px",borderRadius:"6px"},children:[(0,n.jsxs)("span",{style:{fontWeight:"500",color:"#111827"},children:["No ",e," Coverage"]}),n.jsx("span",{style:{color:"#dc2626",fontWeight:"600"},children:"$0.00/paycheck"})]}):(()=>{let e=R.find(e=>e.id===a);return e?(0,n.jsxs)("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",backgroundColor:"white",padding:"12px",borderRadius:"6px"},children:[n.jsx("span",{style:{fontWeight:"500",color:"#111827"},children:e.name}),(0,n.jsxs)("span",{style:{color:z.primary,fontWeight:"600"},children:["$",(()=>{let t=e.coverageTiers?.find(e=>e.tierName===w);return t?t.employeeCost.toFixed(2):e.cost.toFixed(2)})(),"/paycheck"]})]}):null})()})]}),(0,n.jsxs)("div",{style:{display:"flex",gap:"12px",paddingTop:"24px",borderTop:"1px solid #e5e7eb",marginTop:"24px"},children:[A.length>0&&(0,n.jsxs)("button",{onClick:()=>f(!0),style:{display:"flex",alignItems:"center",gap:"8px",padding:"8px 16px",color:"#374151",border:"1px solid #d1d5db",borderRadius:"8px",backgroundColor:"white",cursor:"pointer",transition:"background-color 0.2s ease"},children:[n.jsx(y,{size:16,style:{color:"#6b7280"}}),"Watch Video"]}),R.length>1&&(0,n.jsxs)("button",{onClick:()=>C(!0),style:{display:"flex",alignItems:"center",gap:"8px",padding:"8px 16px",color:"#374151",border:"1px solid #d1d5db",borderRadius:"8px",backgroundColor:"white",cursor:"pointer",transition:"background-color 0.2s ease"},children:[n.jsx(V.Z,{size:16,style:{color:"#6b7280"}}),"Compare Plans"]})]})]}),h&&n.jsx("div",{style:{position:"fixed",top:0,left:0,right:0,bottom:0,backgroundColor:"rgba(0, 0, 0, 0.5)",display:"flex",alignItems:"center",justifyContent:"center",zIndex:1e3},children:(0,n.jsxs)("div",{style:{backgroundColor:"white",borderRadius:"12px",padding:"24px",maxWidth:"500px",width:"90%",maxHeight:"80vh",overflow:"auto"},children:[(0,n.jsxs)("h3",{style:{fontSize:"18px",fontWeight:"600",color:"#111827",margin:"0 0 16px 0"},children:["Waive ",e," Coverage"]}),(0,n.jsxs)("p",{style:{color:"#6b7280",marginBottom:"16px",lineHeight:"1.5"},children:["Please select a reason for waiving ",e.toLowerCase()," coverage:"]}),n.jsx("div",{style:{marginBottom:"24px"},children:["I have coverage through my spouse/partner","I have coverage through another employer","I don't need this coverage","Cost is too high","Other"].map(e=>(0,n.jsxs)("label",{style:{display:"flex",alignItems:"center",gap:"8px",padding:"8px 0",cursor:"pointer"},children:[n.jsx("input",{type:"radio",name:"waiveReason",value:e,checked:v===e,onChange:e=>D(e.target.value),style:{margin:0}}),n.jsx("span",{style:{fontSize:"14px",color:"#374151"},children:e})]},e))}),(0,n.jsxs)("div",{style:{display:"flex",gap:"12px",justifyContent:"flex-end"},children:[n.jsx("button",{onClick:()=>{b(!1),D("")},style:{padding:"8px 16px",backgroundColor:"#f3f4f6",color:"#374151",border:"none",borderRadius:"6px",cursor:"pointer",fontSize:"14px",fontWeight:"500"},children:"Cancel"}),n.jsx("button",{onClick:()=>{if(!v){alert("Please select a reason for waiving coverage.");return}s("WAIVE"),p(null),X({category:e,waiveReason:v,waiveDate:new Date().toISOString()}),b(!1),D(""),i(null)},disabled:!v,style:{padding:"8px 16px",backgroundColor:v?"#ef4444":"#d1d5db",color:"white",border:"none",borderRadius:"6px",cursor:v?"pointer":"not-allowed",fontSize:"14px",fontWeight:"500"},children:"Waive Coverage"})]})]})}),g&&n.jsx(S,{title:`${e} Plan Overview`,description:`Learn more about ${e.toLowerCase()} coverage options`,planType:e.toLowerCase(),onClose:()=>f(!1),videoUrl:"https://example.com/video"}),j&&A.length>1&&n.jsx(Z,{onClose:()=>C(!1),plans:A.map(t=>({id:t.id,name:t.name,type:e.toLowerCase(),tier:"Silver",monthlyPremium:t.cost,deductible:0,outOfPocketMax:0,features:t.features,network:"Standard Network"}))})]})};var ea=o(35047);let es=(0,r.Z)("SquarePen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]]);var ed=o(31870),ep=o(89009);let ec=(0,ep.bR)(),ex=()=>{let e=(0,ep.n5)();return console.log("\uD83D\uDD0D BULK WAIVE API - User ID for headers:",e),{"Content-Type":"application/json","user-id":e}},eg=async e=>{try{console.log("\uD83D\uDEAB BULK WAIVE API - Starting request..."),console.log("\uD83D\uDD0D Request details:",{employeeId:e.employeeId,planAssignmentIds:e.planAssignmentIds,waiveReason:e.waiveReason,enrollmentType:e.enrollmentType||"Open Enrollment",planAssignmentCount:e.planAssignmentIds.length});let t=eu(e);if(!t.isValid)return console.error("❌ Request validation failed:",t.errors),{success:!1,error:`Validation failed: ${t.errors.join(", ")}`};let o={employeeId:e.employeeId,planAssignmentIds:e.planAssignmentIds,waiveReason:e.waiveReason,waiveDate:e.waiveDate||new Date().toISOString(),enrollmentType:e.enrollmentType||"Open Enrollment"};console.log("\uD83D\uDD0D Request body being sent:",o),console.log("\uD83D\uDD0D API URL:",`${ec}/api/pre-enrollment/employee-enrollments/bulk-waive`),console.log("\uD83D\uDD0D Headers:",ex());let n=await fetch(`${ec}/api/pre-enrollment/employee-enrollments/bulk-waive`,{method:"POST",headers:ex(),body:JSON.stringify(o)});if(console.log("\uD83D\uDD0D Bulk waive API response status:",n.status),console.log("\uD83D\uDD0D Response headers:",Object.fromEntries(n.headers.entries())),!n.ok){let e=`HTTP error! status: ${n.status}`;try{let t=await n.json();console.error("❌ Bulk waive API error response:",t),e=t.error||t.message||e,t.errors&&console.error("❌ Individual plan assignment errors:",t.errors)}catch(o){console.log("⚠️ Could not parse error response as JSON");let t=await n.text();console.error("❌ Raw error response:",t),e=t||e}return{success:!1,error:e}}let l=await n.json();return console.log("✅ Bulk waive API successful response:",l),l.createdEnrollments&&console.log("✅ Created waived enrollments:",l.createdEnrollments),l.errors&&l.errors.length>0&&console.warn("⚠️ Some plan assignments failed:",l.errors),{success:!0,data:l}}catch(e){return console.error("❌ Network/fetch error in bulk waive enrollments:",e),{success:!1,error:e instanceof Error?e.message:"Failed to bulk waive enrollments"}}},eu=e=>{let t=[];return e.employeeId?.trim()||t.push("Employee ID is required"),e.planAssignmentIds&&0!==e.planAssignmentIds.length||t.push("At least one plan assignment ID is required"),e.waiveReason?.trim()||t.push("Waive reason is required"),{isValid:0===t.length,errors:t}},ef=()=>"http://localhost:8080",eh=()=>localStorage.getItem("userid1")||localStorage.getItem("userId")||"",em=()=>({"Content-Type":"application/json","user-id":eh()}),ey=async e=>{try{let t=ef(),o=eh();if(!o)throw Error("User ID not found. Please log in again.");console.log("\uD83D\uDE80 Bulk Enrollment API Request:",{url:`${t}/api/pre-enrollment/employee-enrollments/bulk`,userId:o,payload:e});let n=await fetch(`${t}/api/pre-enrollment/employee-enrollments/bulk`,{method:"POST",headers:em(),body:JSON.stringify(e)}),l=await n.json();if(n.ok)return console.log("✅ Bulk enrollment successful:",l),{success:!0,...l};return console.error("❌ Bulk enrollment failed:",{status:n.status,statusText:n.statusText,error:l}),{success:!1,error:l.error||"Bulk enrollment failed",failedPlan:l.failedPlan,details:l.details,rollbackPerformed:l.rollbackPerformed||!1}}catch(e){return console.error("❌ Bulk enrollment API error:",e),{success:!1,error:e instanceof Error?e.message:"Network error occurred",rollbackPerformed:!1}}},eb=(e,t,o=[])=>{let n=[],l=(e,l)=>{e?.id&&(n.push({planAssignmentId:e.id,coverageTier:t,dependentIds:o,enrollmentType:"Open Enrollment"}),console.log(`✅ Added ${l} plan to bulk enrollment:`,e.id))};return l(e.dentalPlan,"Dental"),l(e.visionPlan,"Vision"),l(e.lifePlan,"Life Insurance"),l(e.addPlan,"AD&D"),console.log("\uD83D\uDCCB Prepared plan selections for bulk enrollment:",n),n},ev=async(e,t)=>{if("Employee Only"===t)return[];let o=e.dependents||[];if(0===o.length){let e=localStorage.getItem("enrollmentDependents");if(e)try{let t=JSON.parse(e);o.push(...t)}catch(e){console.error("❌ Error parsing stored dependents:",e)}}let n=o.map(e=>e.id||e._id||e.dependentId).filter(Boolean);return console.log("\uD83D\uDC65 Dependents for bulk enrollment:",{coverageTier:t,dependentCount:o.length,dependentIds:n}),n},eD=e=>{let t=[];return e.employeeId||t.push("Employee ID is required"),e.companyId||t.push("Company ID is required"),e.planSelections&&0!==e.planSelections.length||t.push("At least one plan selection is required"),e.planSelections?.forEach((e,o)=>{e.planAssignmentId||t.push(`Plan assignment ID is required for selection ${o+1}`),e.coverageTier||t.push(`Coverage tier is required for selection ${o+1}`)}),{isValid:0===t.length,errors:t}},ej=e=>{if(!e.success)return`Enrollment failed: ${e.error}`;let t=e.summary;return t?`✅ Enrollment completed successfully!
📊 ${t.totalEnrollments} plan(s) enrolled
💰 Total monthly cost: $${t.totalMonthlyCost.toFixed(2)}
${t.hasWarnings?"⚠️ Some validation warnings occurred":""}`:"Enrollment completed successfully"};var eC=o(24935),eS=o(97183);let ew=({showCosts:e=!0,showWaived:t=!0,style:o="summary"})=>{let l=en(),r=l.selectedPlans||[],i=l.waivedCoverages||[],a=e=>{let t=e.toLowerCase();return t.includes("dental")?{bg:"#f0fdf4",text:"#166534",price:"#059669",icon:"\uD83E\uDDB7"}:t.includes("vision")?{bg:"#eff6ff",text:"#1e40af",price:"#2563eb",icon:"\uD83D\uDC53"}:t.includes("life")?{bg:"#faf5ff",text:"#6b21a8",price:"#7c3aed",icon:"\uD83D\uDEE1️"}:t.includes("ad&d")||t.includes("add")?{bg:"#fef3c7",text:"#92400e",price:"#d97706",icon:"\uD83D\uDEE1️"}:t.includes("medical")?{bg:"#fef2f2",text:"#991b1b",price:"#dc2626",icon:"\uD83C\uDFE5"}:{bg:"#f9fafb",text:"#374151",price:"#111827",icon:"\uD83D\uDCCB"}},s=r.map(e=>{let t=e.selectedTier;return{planName:e.planName,planType:e.category,employeeCost:t.employeeCost,employerCost:t.employerCost,totalCost:t.totalCost,coverageTier:t.tierName}}),d=s.reduce((e,t)=>e+t.employeeCost,0);return 0===r.length&&0===i.length?n.jsx("div",{style:{backgroundColor:"#f9fafb",borderRadius:"8px",padding:"24px",textAlign:"center",border:"1px solid #e5e7eb"},children:n.jsx("p",{style:{color:"#6b7280",margin:0,fontSize:"14px"},children:"No plans selected or waived yet."})}):(0,n.jsxs)("div",{children:[r.length>0&&(0,n.jsxs)("div",{style:{marginBottom:t&&i.length>0?"24px":"0"},children:[n.jsx("h3",{style:{fontSize:"16px",fontWeight:"600",color:"#111827",marginBottom:"16px",margin:"0 0 16px 0"},children:"Selected Plans"}),e?(0,n.jsxs)("div",{style:{display:"flex",flexDirection:"column",gap:"12px"},children:[s.map((e,t)=>{let o=a(e.planType);return n.jsx("div",{style:{backgroundColor:o.bg,borderRadius:"8px",padding:"16px",border:"1px solid #e5e7eb"},children:(0,n.jsxs)("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"flex-start"},children:[(0,n.jsxs)("div",{children:[(0,n.jsxs)("h3",{style:{fontSize:"16px",fontWeight:"600",color:"#111827",margin:"0 0 4px 0"},children:[o.icon," ",e.planType,": ",e.planName]}),n.jsx("p",{style:{fontSize:"14px",color:o.text,margin:"0 0 8px 0",fontWeight:"500"},children:e.coverageTier}),(0,n.jsxs)("div",{style:{fontSize:"12px",color:"#6b7280"},children:[(0,n.jsxs)("div",{children:["Total Cost: $",e.totalCost.toFixed(2),"/month"]}),(0,n.jsxs)("div",{children:["Employer Pays: $",e.employerCost.toFixed(2),"/month"]})]})]}),(0,n.jsxs)("div",{style:{textAlign:"right"},children:[(0,n.jsxs)("p",{style:{fontSize:"18px",fontWeight:"600",color:o.price,margin:0},children:["$",e.employeeCost.toFixed(2)]}),n.jsx("p",{style:{fontSize:"12px",color:"#6b7280",margin:"4px 0 0 0"},children:"per paycheck"})]})]})},t)}),s.length>0&&n.jsx("div",{style:{backgroundColor:"#f3f4f6",borderRadius:"8px",padding:"16px",border:"2px solid #d1d5db",marginTop:"8px"},children:(0,n.jsxs)("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center"},children:[n.jsx("h3",{style:{fontSize:"16px",fontWeight:"600",color:"#111827",margin:0},children:"Total Monthly Cost"}),(0,n.jsxs)("p",{style:{fontSize:"20px",fontWeight:"700",color:"#111827",margin:0},children:["$",d.toFixed(2)]})]})}),0===s.length&&i.length>0&&(0,n.jsxs)("div",{style:{backgroundColor:"#f3f4f6",borderRadius:"8px",padding:"16px",border:"2px solid #d1d5db",marginTop:"8px"},children:[(0,n.jsxs)("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center"},children:[n.jsx("h3",{style:{fontSize:"16px",fontWeight:"600",color:"#111827",margin:0},children:"Total Monthly Cost"}),n.jsx("p",{style:{fontSize:"20px",fontWeight:"700",color:"#111827",margin:0},children:"$0.00"})]}),n.jsx("p",{style:{fontSize:"12px",color:"#6b7280",margin:"4px 0 0 0",textAlign:"center"},children:"All coverages waived"})]})]}):n.jsx("div",{style:{display:"flex",flexDirection:"column",gap:"8px"},children:r.map((e,t)=>{let o=a(e.category);return(0,n.jsxs)("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center"},children:[(0,n.jsxs)("span",{style:{color:"#374151",fontSize:"14px",fontWeight:"500"},children:[o.icon," ",e.category,":"]}),n.jsx("span",{style:{color:"#111827",fontSize:"14px",fontWeight:"600"},children:e.planName})]},t)})})]}),t&&i.length>0&&(0,n.jsxs)("div",{children:[n.jsx("h3",{style:{fontSize:"16px",fontWeight:"600",color:"#111827",marginBottom:"16px",margin:"0 0 16px 0"},children:"Waived Coverages"}),n.jsx("div",{style:{display:"flex",flexDirection:"column",gap:"8px"},children:i.map((e,t)=>{let o=a(e.category);return(0,n.jsxs)("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center"},children:[(0,n.jsxs)("span",{style:{color:"#dc2626",fontSize:"14px",fontWeight:"500"},children:[o.icon," ",e.category,":"]}),n.jsx("span",{style:{color:"#dc2626",fontSize:"14px",fontWeight:"600"},children:"Waived"})]},t)})}),"confirmation"===o&&n.jsx("div",{style:{marginTop:"12px",fontSize:"12px",color:"#6b7280"},children:i.map((e,t)=>(0,n.jsxs)("div",{children:[e.category," waive reason: ",e.waiveReason]},t))})]})]})},ek=({enrollmentData:e,selectedCoverageTier:t,onMakeChanges:o,onConfirmEnrollment:r,planAssignments:i={}})=>{let a=(0,ea.useRouter)(),s=(0,ed.C)(e=>e.user.userProfile),[d,p]=(0,l.useState)({}),[c,x]=(0,l.useState)(!1),[g,f]=(0,l.useState)(!1),[h,y]=(0,l.useState)(!1),[b,v]=(0,l.useState)(""),[D,j]=(0,l.useState)(!1),[C,S]=(0,l.useState)(!1),w=()=>"http://localhost:8080",k=()=>localStorage.getItem("userid1")||localStorage.getItem("userId")||"6838677aef6db0212bcfdacd",I=async e=>{let t=w(),o=k();try{console.log("\uD83D\uDD0D Fetching plan assignment details for ID:",e);let n=await fetch(`${t}/api/pre-enrollment/plan-assignments/${e}`,{headers:{"Content-Type":"application/json","user-id":o}});if(!n.ok)return console.error("❌ Failed to fetch plan assignment details. Status:",n.status),null;{let e=await n.json();return console.log("✅ Plan assignment fetch response:",e),e.assignment._doc||e.assignment}}catch(e){return console.error("❌ Error fetching plan assignment:",e),null}},P=()=>{console.log("\uD83E\uDDF9 Clearing enrollment data from localStorage..."),["selectedCoverageTier","enrollmentDependents","userProfile","enrollmentRecommendation"].forEach(e=>{localStorage.removeItem(e),console.log(`  ✅ Removed: ${e}`)}),console.log("✅ Enrollment data cleared from localStorage"),console.log("\uD83D\uDD12 Preserved for confirmation page:",["selectedDentalPlan","selectedVisionPlan","selectedLifePlan","selectedADDPlan","dentalWaived","visionWaived","lifeWaived","addWaived","dentalWaiveReason","visionWaiveReason","lifeWaiveReason","addWaiveReason","enrollmentSummarySnapshot"])},W=async()=>{g||((0,eS.yd)()?(console.log("\uD83D\uDCDD Signature already exists in localStorage, proceeding with enrollment..."),z()):(console.log("\uD83D\uDCDD No signature found, showing signature modal..."),j(!0)))},z=async()=>{f(!0),console.log("\uD83C\uDFAF Starting BULK enrollment confirmation process...");try{let t=k(),o=s.companyId||localStorage.getItem("companyId")||"";if(!o)throw Error("Company ID not found. Please contact support.");let n=await ev(e,E),l=eb({dentalPlan:M,visionPlan:$,lifePlan:L,addPlan:H},E,n),d=null;if(l.length>0){let e={employeeId:t,companyId:o,employeeClassType:"Full-Time",planSelections:l},n=eD(e);if(!n.isValid)throw Error(`Bulk enrollment validation failed: ${n.errors.join(", ")}`);if(console.log("\uD83D\uDE80 Calling bulk enrollment API with validated request:",e),(d=await ey(e)).success)console.log("✅ Bulk enrollment successful:",d),console.log("\uD83D\uDCCA Enrollment summary:",ej(d));else throw console.error("❌ Bulk enrollment failed:",d),Error(d.error||"Bulk enrollment failed")}else console.log("ℹ️ No plans selected for enrollment, proceeding with waive operations only");let p=[],c=[];if(console.log("\uD83D\uDD0D BULK WAIVE DEBUG - Starting bulk waive process..."),console.log("\uD83D\uDD0D User ID:",t),console.log("\uD83D\uDD0D Plan assignments available:",Object.keys(i)),console.log("\uD83D\uDD0D Waive statuses:",{dentalWaived:R,visionWaived:A,lifeWaived:B}),R)try{console.log("\uD83D\uDEAB Processing dental waive...");let e=localStorage.getItem("dentalWaiveReason")||"Coverage not needed";console.log("\uD83D\uDD0D Dental waive reason:",e);let o=N("dental");if(0===o.length)console.log("⚠️ No dental plan assignments found for waiving"),console.log("\uD83D\uDD0D Available plan assignment keys:",Object.keys(i)),console.log("\uD83D\uDD0D Plan assignments structure:",i);else{let n=o.map(e=>e.assignment?._id||e._id||e.id).filter(Boolean);if(console.log("\uD83D\uDD0D Dental plan assignment IDs to waive:",n),n.length>0){console.log("\uD83D\uDEAB Calling bulk waive API for dental...");let o=await eg({employeeId:t,planAssignmentIds:n,waiveReason:e,enrollmentType:"Open Enrollment"});console.log("\uD83D\uDD0D Dental bulk waive API response:",o),o.success&&o.data?(console.log("✅ Dental bulk waive successful:",o.data),p.push({type:"Dental",result:o.data})):(console.error("❌ Dental bulk waive failed:",o.error),c.push({type:"Dental Waive",error:o.error||"Failed to waive dental coverage"}))}else console.log("⚠️ No valid dental plan assignment IDs found")}}catch(e){console.error("❌ Dental bulk waive error:",e),c.push({type:"Dental Waive",error:e instanceof Error?e.message:"Failed to waive dental coverage"})}if(A)try{console.log("\uD83D\uDEAB Processing vision waive...");let e=localStorage.getItem("visionWaiveReason")||"Coverage not needed";console.log("\uD83D\uDD0D Vision waive reason:",e);let o=N("vision");if(0===o.length)console.log("⚠️ No vision plan assignments found for waiving");else{let n=o.map(e=>e.assignment?._id||e._id||e.id).filter(Boolean);if(console.log("\uD83D\uDD0D Vision plan assignment IDs to waive:",n),n.length>0){console.log("\uD83D\uDEAB Calling bulk waive API for vision...");let o=await eg({employeeId:t,planAssignmentIds:n,waiveReason:e,enrollmentType:"Open Enrollment"});console.log("\uD83D\uDD0D Vision bulk waive API response:",o),o.success&&o.data?(console.log("✅ Vision bulk waive successful:",o.data),p.push({type:"Vision",result:o.data})):(console.error("❌ Vision bulk waive failed:",o.error),c.push({type:"Vision Waive",error:o.error||"Failed to waive vision coverage"}))}else console.log("⚠️ No valid vision plan assignment IDs found")}}catch(e){console.error("❌ Vision bulk waive error:",e),c.push({type:"Vision Waive",error:e instanceof Error?e.message:"Failed to waive vision coverage"})}if(B)try{console.log("\uD83D\uDEAB Processing life insurance waive...");let e=localStorage.getItem("lifeWaiveReason")||"Coverage not needed";console.log("\uD83D\uDD0D Life insurance waive reason:",e);let o=[];for(let e of["term life","life","life insurance","supplemental life insurance","group (employer) life"]){let t=N(e);t.length>0&&(o.push(...t),console.log(`🔍 Found life insurance plans in category '${e}':`,t))}if(0===o.length)console.log("⚠️ No life insurance plan assignments found for waiving");else{let n=o.map(e=>e.assignment?._id||e._id||e.id).filter(Boolean);if(console.log("\uD83D\uDD0D Life insurance plan assignment IDs to waive:",n),n.length>0){console.log("\uD83D\uDEAB Calling bulk waive API for life insurance...");let o=await eg({employeeId:t,planAssignmentIds:n,waiveReason:e,enrollmentType:"Open Enrollment"});console.log("\uD83D\uDD0D Life insurance bulk waive API response:",o),o.success&&o.data?(console.log("✅ Life insurance bulk waive successful:",o.data),p.push({type:"Life Insurance",result:o.data})):(console.error("❌ Life insurance bulk waive failed:",o.error),c.push({type:"Life Insurance Waive",error:o.error||"Failed to waive life insurance coverage"}))}else console.log("⚠️ No valid life insurance plan assignment IDs found")}}catch(e){console.error("❌ Life insurance bulk waive error:",e),c.push({type:"Life Insurance Waive",error:e instanceof Error?e.message:"Failed to waive life insurance coverage"})}if(F)try{console.log("\uD83D\uDEAB Processing AD&D waive...");let e=localStorage.getItem("addWaiveReason")||"Coverage not needed";console.log("\uD83D\uDD0D AD&D waive reason:",e);let o=[];for(let e of["accidental death & dismemberment (ad&d)","ad&d","add","accidental death and dismemberment"]){let t=N(e);t.length>0&&(o.push(...t),console.log(`🔍 Found AD&D plans in category '${e}':`,t))}if(0===o.length)console.log("⚠️ No AD&D plan assignments found for waiving");else{let n=o.map(e=>e.assignment?._id||e._id||e.id).filter(Boolean);if(console.log("\uD83D\uDD0D AD&D plan assignment IDs to waive:",n),n.length>0){console.log("\uD83D\uDEAB Calling bulk waive API for AD&D...");let o=await eg({employeeId:t,planAssignmentIds:n,waiveReason:e,enrollmentType:"Open Enrollment"});console.log("\uD83D\uDD0D AD&D bulk waive API response:",o),o.success&&o.data?(console.log("✅ AD&D bulk waive successful:",o.data),p.push({type:"AD&D",result:o.data})):(console.error("❌ AD&D bulk waive failed:",o.error),c.push({type:"AD&D Waive",error:o.error||"Failed to waive AD&D coverage"}))}else console.log("⚠️ No valid AD&D plan assignment IDs found")}}catch(e){console.error("❌ AD&D bulk waive error:",e),c.push({type:"AD&D Waive",error:e instanceof Error?e.message:"Failed to waive AD&D coverage"})}console.log("\uD83D\uDEAB Bulk waive results:",p);let x=d&&!d.success,g=c.length>0;if(x||g){console.error("❌ Some operations failed:",{bulkEnrollmentError:x&&d?d.error:null,waiveErrors:c});let e="❌ Enrollment Error\n\n";x&&d&&(e+=`Enrollment failed: ${d.error}

`),g&&(e+=`Waive errors:
${c.map(e=>e.error).join("\n")}`),v(e),y(!0)}else{console.log("✅ All operations completed successfully:",{bulkEnrollment:d,waiveResults:p}),console.log("\uD83D\uDD0D Pre-snapshot debug - Plan variables:",{dentalPlan:M,visionPlan:$,lifePlan:L,addPlan:H,addWaived:F});let t=el({dentalPlan:M,visionPlan:$,lifePlan:L,addPlan:H,dependents:e.dependents,selectedCoverageTier:E,dentalWaived:R,visionWaived:A,lifeWaived:B,addWaived:F,dentalWaiveReason:localStorage.getItem("dentalWaiveReason"),visionWaiveReason:localStorage.getItem("visionWaiveReason"),lifeWaiveReason:localStorage.getItem("lifeWaiveReason"),addWaiveReason:localStorage.getItem("addWaiveReason")});console.log("\uD83D\uDCF8 Saved enrollment summary snapshot:",t),console.log("\uD83D\uDEE1️ AD&D in snapshot specifically:",{addPlan:t.addPlan,addWaived:t.addWaived,addWaiveReason:t.addWaiveReason}),P(),console.log("\uD83C\uDF89 Enrollment completed successfully, navigating to confirmation page..."),r?r():a.push("/ai-enroller/employee-enrol/confirmation")}}catch(e){console.error("❌ Enrollment confirmation failed:",e),v("❌ Enrollment Error\n\nThere was an error in enrolling. Please contact HR or try again."),y(!0)}finally{f(!1)}},E=(()=>{if(console.log("\uD83D\uDD0D Checking coverage tier sources:"),console.log("  - selectedCoverageTier prop:",t),t)return console.log("  ✅ Using prop value:",t),t;let e=localStorage.getItem("selectedCoverageTier");return(console.log("  - localStorage value:",e),e)?(console.log("  ✅ Using localStorage value:",e),e):(console.log("  ⚠️ Using fallback: Employee Only"),"Employee Only")})();console.log("\uD83C\uDFAF Final coverage tier being used:",E);let T=(e,t)=>{if(console.log(`🔍 Getting cost breakdown for ${t}:`,e),console.log(`🎯 Selected coverage tier: ${E}`),!e)return console.log(`❌ No plan data for ${t}`),null;if(!e.coverageTiers||!Array.isArray(e.coverageTiers))return console.log(`⚠️ No coverage tiers found for ${t}, using fallback cost`),{planName:e.name||e.planName||"Unknown Plan",planType:t,employeeCost:e.cost||0,employerCost:0,totalCost:e.cost||0};console.log(`📊 Coverage tiers for ${t}:`,e.coverageTiers),e.coverageTiers.forEach((e,t)=>{console.log(`  Tier ${t}: ${e.tierName} - Employee: $${e.employeeCost}, Employer: $${e.employerCost}, Total: $${e.totalCost}`)});let o=e.coverageTiers.find(e=>e.tierName===E);if(!o){console.log(`⚠️ No matching tier for ${E}, trying Employee Only`);let o=e.coverageTiers.find(e=>"Employee Only"===e.tierName);if(!o){console.log(`⚠️ No Employee Only tier found, using first tier`);let o=e.coverageTiers[0];return o?{planName:e.name||e.planName||"Unknown Plan",planType:t,employeeCost:o.employeeCost||0,employerCost:o.employerCost||0,totalCost:o.totalCost||0}:null}return{planName:e.name||e.planName||"Unknown Plan",planType:t,employeeCost:o.employeeCost||0,employerCost:o.employerCost||0,totalCost:o.totalCost||0}}return console.log(`✅ Found matching tier for ${t}:`,o),{planName:e.name||e.planName||"Unknown Plan",planType:t,employeeCost:o.employeeCost||0,employerCost:o.employerCost||0,totalCost:o.totalCost||0}};console.log("\uD83D\uDCCB Enrollment data received:",e),console.log("\uD83D\uDC65 Dependents:",e.dependents),console.log("\uD83E\uDDB7 Dental plan:",e.dentalPlan),console.log("\uD83D\uDC53 Vision plan:",e.visionPlan),console.log("\uD83D\uDEE1️ Life plan:",e.lifePlan);let R="true"===localStorage.getItem("dentalWaived"),A="true"===localStorage.getItem("visionWaived"),B="true"===localStorage.getItem("lifeWaived"),F="true"===localStorage.getItem("addWaived");console.log("\uD83D\uDEAB Waived coverages:",{dentalWaived:R,visionWaived:A,lifeWaived:B,addWaived:F}),console.log("\uD83D\uDCCB Plan assignments for bulk waive:",i);let N=e=>{for(let t of[e.toLowerCase(),e,`${e} plan`,`${e.toLowerCase()} plan`,e.charAt(0).toUpperCase()+e.slice(1).toLowerCase(),`${e.charAt(0).toUpperCase()+e.slice(1).toLowerCase()} Plan`])if(i[t]&&i[t].length>0)return console.log(`🔍 Found ${e} plans under key '${t}':`,i[t]),i[t];return console.log(`⚠️ No ${e} plan assignments found. Available keys:`,Object.keys(i)),[]};(0,l.useEffect)(()=>{(async()=>{x(!0),console.log("\uD83D\uDD04 Fetching real plan assignment data for summary...");try{let e={},t=localStorage.getItem("selectedDentalPlan"),o=localStorage.getItem("selectedVisionPlan"),n=localStorage.getItem("selectedLifePlan"),l=localStorage.getItem("selectedADDPlan");if(t)try{let o=JSON.parse(t);if(o.id){console.log("\uD83E\uDDB7 Fetching real dental plan assignment:",o.id);let t=await I(o.id);t&&(e.dentalPlan={...o,...t,name:t.planName||o.name,coverageTiers:t.coverageTiers})}}catch(e){console.error("❌ Error parsing dental plan data:",e)}if(o)try{let t=JSON.parse(o);if(t.id){console.log("\uD83D\uDC53 Fetching real vision plan assignment:",t.id);let o=await I(t.id);o&&(e.visionPlan={...t,...o,name:o.planName||t.name,coverageTiers:o.coverageTiers})}}catch(e){console.error("❌ Error parsing vision plan data:",e)}if(n)try{let t=JSON.parse(n);if(t.id){console.log("\uD83D\uDEE1️ Fetching real life plan assignment:",t.id);let o=await I(t.id);o&&(e.lifePlan={...t,...o,name:o.planName||t.name,coverageTiers:o.coverageTiers})}}catch(e){console.error("❌ Error parsing life plan data:",e)}if(l)try{let t=JSON.parse(l);if(t.id){console.log("\uD83D\uDEE1️ Fetching real AD&D plan assignment:",t.id);let o=await I(t.id);o&&(e.addPlan={...t,...o,name:o.planName||t.name,coverageTiers:o.coverageTiers})}}catch(e){console.error("❌ Error parsing AD&D plan data:",e)}console.log("✅ Real plan data fetched:",e),p(e)}catch(e){console.error("❌ Error fetching real plan data:",e)}finally{x(!1)}})()},[]);let O=(e,t,o)=>{if(o)return console.log(`✅ Using real plan data for ${e}:`,o),o;if(t)return t;let n=localStorage.getItem(e);if(n)try{return JSON.parse(n)}catch(t){console.log(`❌ Error parsing ${e}:`,t)}return null},M=O("selectedDentalPlan",e.dentalPlan,d.dentalPlan),$=O("selectedVisionPlan",e.visionPlan,d.visionPlan),L=O("selectedLifePlan",e.lifePlan,d.lifePlan),H=O("selectedADDPlan",e.addPlan,d.addPlan);console.log("\uD83D\uDD0D Final plan data:",{dentalPlan:M,visionPlan:$,lifePlan:L,addPlan:H}),console.log("\uD83D\uDEE1️ AD&D Plan Debug:",{localStorage_selectedADDPlan:localStorage.getItem("selectedADDPlan"),enrollmentData_addPlan:e.addPlan,realPlanData_addPlan:d.addPlan,final_addPlan:H,addWaived:localStorage.getItem("addWaived")}),M?.coverageTiers&&console.log("\uD83E\uDDB7 Dental plan coverage tiers:",M.coverageTiers),$?.coverageTiers&&console.log("\uD83D\uDC53 Vision plan coverage tiers:",$.coverageTiers),L?.coverageTiers&&console.log("\uD83D\uDEE1️ Life plan coverage tiers:",L.coverageTiers),H?.coverageTiers&&console.log("\uD83D\uDEE1️ AD&D plan coverage tiers:",H.coverageTiers);let q=[],V=[];if(console.log("\uD83E\uDDB7 Dental insurance check:",{dentalPlan:M,dentalWaived:R}),M){console.log("\uD83E\uDDB7 Processing dental plan:",M);let e=T(M,"Dental");console.log("\uD83E\uDDB7 Dental cost breakdown result:",e),e?(q.push(e),console.log("\uD83E\uDDB7 Dental cost added to planCosts")):console.log("\uD83E\uDDB7 Dental cost breakdown returned null - plan will not be displayed")}else R?(console.log("\uD83E\uDDB7 Dental waived"),V.push("Dental")):console.log("\uD83E\uDDB7 No dental plan or waive status found");if(console.log("\uD83D\uDC53 Vision insurance check:",{visionPlan:$,visionWaived:A}),$){console.log("\uD83D\uDC53 Processing vision plan:",$);let e=T($,"Vision");console.log("\uD83D\uDC53 Vision cost breakdown result:",e),e?(q.push(e),console.log("\uD83D\uDC53 Vision cost added to planCosts")):console.log("\uD83D\uDC53 Vision cost breakdown returned null - plan will not be displayed")}else A?(console.log("\uD83D\uDC53 Vision waived"),V.push("Vision")):console.log("\uD83D\uDC53 No vision plan or waive status found");if(console.log("\uD83D\uDEE1️ Life insurance check:",{lifePlan:L,lifeWaived:B}),L){console.log("\uD83D\uDEE1️ Processing life insurance plan:",L);let e=T(L,"Life Insurance");console.log("\uD83D\uDEE1️ Life insurance cost breakdown:",e),e&&q.push(e)}else B?(console.log("\uD83D\uDEE1️ Life insurance waived"),V.push("Life Insurance")):console.log("\uD83D\uDEE1️ No life insurance plan or waive status found");if(console.log("\uD83D\uDEE1️ AD&D insurance check:",{addPlan:H,addWaived:F}),H){console.log("\uD83D\uDEE1️ Processing AD&D plan:",H);let e=T(H,"AD&D");console.log("\uD83D\uDEE1️ AD&D cost breakdown:",e),e?(q.push(e),console.log("\uD83D\uDEE1️ AD&D cost added to planCosts")):console.log("\uD83D\uDEE1️ AD&D cost breakdown returned null - plan will not be displayed")}else F?(console.log("\uD83D\uDEE1️ AD&D waived"),V.push("AD&D")):console.log("\uD83D\uDEE1️ No AD&D plan or waive status found");let Z=q.reduce((e,t)=>e+t.employeeCost,0),U=q.reduce((e,t)=>e+t.employerCost,0),_=q.reduce((e,t)=>e+t.totalCost,0);return console.log("\uD83D\uDCB0 Plan costs calculated:",q),console.log("\uD83D\uDCB0 Totals:",{totalEmployeeCost:Z,totalEmployerCost:U,grandTotal:_}),(0,n.jsxs)("div",{style:{display:"flex",flexDirection:"column",gap:"24px",fontFamily:'"SF Pro", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif'},children:[(0,n.jsxs)("div",{style:{display:"flex",gap:"16px"},children:[n.jsx("div",{style:{width:"40px",height:"40px",borderRadius:"50%",display:"flex",alignItems:"center",justifyContent:"center",flexShrink:0,overflow:"hidden"},children:n.jsx(m.default,{src:"/brea.png",alt:"Brea - AI Benefits Assistant",width:40,height:40,style:{borderRadius:"50%"}})}),(0,n.jsxs)("div",{style:{backgroundColor:"#f9fafb",borderRadius:"8px",padding:"16px",maxWidth:"1000px"},children:[n.jsx("p",{style:{color:"#374151",lineHeight:"1.6",margin:0},children:"\uD83D\uDCCB Perfect! Here's your personalized benefits package:"}),n.jsx("p",{style:{color:"#374151",lineHeight:"1.6",margin:"8px 0 0 0"},children:"Take a moment to review everything. You can always go back and make changes if needed."})]})]}),(0,n.jsxs)("div",{style:{backgroundColor:"white",borderRadius:"12px",border:"1px solid #e5e7eb",padding:"32px",boxShadow:"0 1px 3px 0 rgba(0, 0, 0, 0.1)"},children:[n.jsx("div",{style:{marginBottom:"24px"},children:(0,n.jsxs)("div",{style:{display:"flex",alignItems:"center",gap:"8px",marginBottom:"16px"},children:[n.jsx("span",{style:{fontSize:"24px"},children:"\uD83D\uDCC4"}),n.jsx("h1",{style:{fontSize:"24px",fontWeight:"600",color:"#111827",margin:0},children:"Enrollment Summary"})]})}),(()=>{let e=en();return e.selectedPlans&&e.selectedPlans.length>0||e.waivedCoverages&&e.waivedCoverages.length>0?n.jsx("div",{style:{backgroundColor:"#f8fffe",borderRadius:"12px",padding:"24px",marginBottom:"24px",border:"1px solid #d1fae5"},children:n.jsx(ew,{showCosts:!0,showWaived:!0,style:"summary"})}):null})(),(()=>{let e=en();return e.selectedPlans&&e.selectedPlans.length>0||e.waivedCoverages&&e.waivedCoverages.length>0?null:(0,n.jsxs)("div",{style:{backgroundColor:"#f8fffe",borderRadius:"12px",padding:"24px",marginBottom:"24px",border:"1px solid #d1fae5"},children:[(q.length>0||V.length>0)&&(0,n.jsxs)(n.Fragment,{children:[n.jsx("div",{style:{display:"flex",flexDirection:"column",gap:"16px",marginBottom:"24px"},children:q.map((e,t)=>{let o=(e=>{switch(e.toLowerCase()){case"medical":return{bg:"#fef2f2",text:"#991b1b",price:"#dc2626"};case"dental":return{bg:"#f0fdf4",text:"#166534",price:"#059669"};case"vision":return{bg:"#eff6ff",text:"#1e40af",price:"#2563eb"};case"life insurance":return{bg:"#faf5ff",text:"#6b21a8",price:"#7c3aed"};case"ad&d":return{bg:"#fef3c7",text:"#92400e",price:"#d97706"};default:return{bg:"#f9fafb",text:"#374151",price:"#111827"}}})(e.planType);return(0,n.jsxs)("div",{style:{backgroundColor:o.bg,borderRadius:"8px",padding:"16px",border:"1px solid #e5e7eb"},children:[(0,n.jsxs)("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"flex-start",marginBottom:"5px"},children:[(0,n.jsxs)("div",{children:[(0,n.jsxs)("h3",{style:{fontSize:"16px",fontWeight:"600",color:"#111827",margin:"0 0 4px 0"},children:[e.planType,": ",e.planName]}),n.jsx("p",{style:{fontSize:"14px",color:o.text,margin:0,fontWeight:"500"},children:"Medical"===e.planType?"HMO Plan":"Dental"===e.planType?"PPO Plan":"Vision"===e.planType?"Choice Plan":"Life Insurance"===e.planType?"Term Life":"AD&D"===e.planType?"Accident Coverage":"Coverage Plan"})]}),n.jsx("div",{style:{textAlign:"right"},children:(0,n.jsxs)("p",{style:{fontSize:"16px",fontWeight:"600",color:o.price,margin:0},children:["You pay: $",e.employeeCost.toFixed(2)]})})]}),n.jsx("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center"},children:(0,n.jsxs)("div",{style:{display:"flex",alignItems:"center",gap:"2px"},children:[n.jsx("span",{style:{fontSize:"14px",color:"#6b7280"}}),(0,n.jsxs)("span",{style:{fontSize:"14px",color:"#374151"},children:["Employer pays: $",e.employerCost.toFixed(2)]})]})})]},t)})}),(0,n.jsxs)("div",{style:{display:"flex",flexDirection:"column",gap:"12px",paddingTop:"16px",borderTop:"1px solid #e5e7eb"},children:[(0,n.jsxs)("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center"},children:[n.jsx("span",{style:{fontSize:"16px",fontWeight:"600",color:"#111827"},children:"Total Plan Cost:"}),(0,n.jsxs)("span",{style:{fontSize:"16px",fontWeight:"600",color:"#111827"},children:["$",_.toFixed(2)]})]}),(0,n.jsxs)("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center"},children:[n.jsx("span",{style:{fontSize:"16px",fontWeight:"600",color:"#111827"},children:"Employer Contribution:"}),(0,n.jsxs)("span",{style:{fontSize:"16px",fontWeight:"600",color:"#10b981"},children:["-$",U.toFixed(2)]})]}),(0,n.jsxs)("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",paddingTop:"8px",borderTop:"1px solid #e5e7eb"},children:[n.jsx("span",{style:{fontSize:"18px",fontWeight:"700",color:"#111827"},children:"Your Cost per Paycheck:"}),(0,n.jsxs)("span",{style:{fontSize:"24px",fontWeight:"700",color:"#2563eb"},children:["$",Z.toFixed(2)]})]})]}),V.map((e,t)=>(0,n.jsxs)("div",{style:{backgroundColor:"#fef2f2",borderRadius:"8px",padding:"16px",border:"1px solid #fecaca",marginTop:"16px"},children:[(0,n.jsxs)("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"flex-start",marginBottom:"8px"},children:[(0,n.jsxs)("div",{children:[(0,n.jsxs)("h3",{style:{fontSize:"16px",fontWeight:"600",color:"#111827",margin:"0 0 4px 0"},children:[e,": Waived"]}),n.jsx("p",{style:{fontSize:"14px",color:"#dc2626",margin:0,fontWeight:"500"},children:"No Coverage"})]}),n.jsx("div",{style:{textAlign:"right"},children:n.jsx("p",{style:{fontSize:"16px",fontWeight:"600",color:"#dc2626",margin:0},children:"You pay: $0.00"})})]}),(0,n.jsxs)("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center"},children:[(0,n.jsxs)("div",{style:{display:"flex",alignItems:"center",gap:"4px"},children:[n.jsx("span",{style:{fontSize:"14px",color:"#dc2626"},children:"⚠️"}),(0,n.jsxs)("span",{style:{fontSize:"14px",color:"#374151"},children:["No ",e.toLowerCase()," benefits"]})]}),n.jsx("div",{style:{fontSize:"14px",fontWeight:"600",color:"#dc2626"},children:"You pay: $0.00"})]})]},t))]}),0===q.length&&(M||$||L||H)&&(0,n.jsxs)("div",{style:{marginBottom:"24px"},children:[n.jsx("h3",{style:{fontSize:"16px",fontWeight:"600",color:"#111827",marginBottom:"16px"},children:"Selected Plans (Basic Info)"}),(0,n.jsxs)("div",{style:{display:"flex",flexDirection:"column",gap:"12px"},children:[M&&n.jsx("div",{style:{backgroundColor:"#f0fdf4",borderRadius:"8px",padding:"16px",border:"1px solid #e5e7eb"},children:(0,n.jsxs)("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"flex-start"},children:[(0,n.jsxs)("div",{children:[(0,n.jsxs)("h3",{style:{fontSize:"16px",fontWeight:"600",color:"#111827",margin:"0 0 4px 0"},children:["Dental: ",M.name||M.planName||"Selected Plan"]}),n.jsx("p",{style:{fontSize:"14px",color:"#166534",margin:0,fontWeight:"500"},children:"PPO Plan"})]}),n.jsx("div",{style:{textAlign:"right"},children:n.jsx("p",{style:{fontSize:"16px",fontWeight:"600",color:"#059669",margin:0},children:"Cost info unavailable"})})]})}),$&&n.jsx("div",{style:{backgroundColor:"#eff6ff",borderRadius:"8px",padding:"16px",border:"1px solid #e5e7eb"},children:(0,n.jsxs)("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"flex-start"},children:[(0,n.jsxs)("div",{children:[(0,n.jsxs)("h3",{style:{fontSize:"16px",fontWeight:"600",color:"#111827",margin:"0 0 4px 0"},children:["Vision: ",$.name||$.planName||"Selected Plan"]}),n.jsx("p",{style:{fontSize:"14px",color:"#1e40af",margin:0,fontWeight:"500"},children:"Choice Plan"})]}),n.jsx("div",{style:{textAlign:"right"},children:n.jsx("p",{style:{fontSize:"16px",fontWeight:"600",color:"#2563eb",margin:0},children:"Cost info unavailable"})})]})}),L&&n.jsx("div",{style:{backgroundColor:"#faf5ff",borderRadius:"8px",padding:"16px",border:"1px solid #e5e7eb"},children:(0,n.jsxs)("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"flex-start"},children:[(0,n.jsxs)("div",{children:[(0,n.jsxs)("h3",{style:{fontSize:"16px",fontWeight:"600",color:"#111827",margin:"0 0 4px 0"},children:["Life Insurance: ",L.name||L.planName||"Selected Plan"]}),n.jsx("p",{style:{fontSize:"14px",color:"#6b21a8",margin:0,fontWeight:"500"},children:"Term Life"})]}),n.jsx("div",{style:{textAlign:"right"},children:n.jsx("p",{style:{fontSize:"16px",fontWeight:"600",color:"#7c3aed",margin:0},children:"Cost info unavailable"})})]})}),H&&n.jsx("div",{style:{backgroundColor:"#fef3c7",borderRadius:"8px",padding:"16px",border:"1px solid #e5e7eb"},children:(0,n.jsxs)("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"flex-start"},children:[(0,n.jsxs)("div",{children:[(0,n.jsxs)("h3",{style:{fontSize:"16px",fontWeight:"600",color:"#111827",margin:"0 0 4px 0"},children:["AD&D: ",H.name||H.planName||"Selected Plan"]}),n.jsx("p",{style:{fontSize:"14px",color:"#92400e",margin:0,fontWeight:"500"},children:"Accident Coverage"})]}),n.jsx("div",{style:{textAlign:"right"},children:n.jsx("p",{style:{fontSize:"16px",fontWeight:"600",color:"#d97706",margin:0},children:"Cost info unavailable"})})]})})]}),n.jsx("div",{style:{backgroundColor:"#fef3c7",borderRadius:"6px",padding:"12px",marginTop:"16px",border:"1px solid #fbbf24"},children:n.jsx("p",{style:{fontSize:"14px",color:"#92400e",margin:0},children:"⚠️ Cost information is not available. Please contact your HR administrator for pricing details."})})]})]})})(),q.length>0&&(0,n.jsxs)("div",{style:{marginTop:"16px",paddingTop:"16px",borderTop:"1px solid #e5e7eb",display:"none"},children:[n.jsx("h3",{style:{fontSize:"16px",fontWeight:"600",color:"#111827",marginBottom:"12px"},children:"Family Contribution Breakdown:"}),(0,n.jsxs)("div",{style:{display:"flex",flexDirection:"column",gap:"8px"},children:[q.map((e,t)=>(0,n.jsxs)("div",{style:{backgroundColor:"#f9fafb",padding:"12px",borderRadius:"6px",border:"1px solid #e5e7eb"},children:[(0,n.jsxs)("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",marginBottom:"8px"},children:[(0,n.jsxs)("span",{style:{fontSize:"14px",fontWeight:"600",color:"#111827"},children:[e.planType,": ",e.planName]}),(0,n.jsxs)("span",{style:{fontSize:"14px",fontWeight:"600",color:"#2563eb"},children:["You pay: $",e.employeeCost.toFixed(2)]})]}),(0,n.jsxs)("div",{style:{display:"grid",gridTemplateColumns:"1fr 1fr",gap:"8px",fontSize:"12px"},children:[(0,n.jsxs)("div",{style:{display:"flex",alignItems:"center"},children:[n.jsx("span",{style:{color:"#6b7280"},children:"Employer pays: "}),(0,n.jsxs)("span",{style:{color:"#10b981",fontWeight:"500"},children:["$",e.employerCost.toFixed(2)]})]}),(0,n.jsxs)("div",{style:{display:"flex",alignItems:"center"},children:[n.jsx("span",{style:{color:"#6b7280"},children:"You pay: "}),(0,n.jsxs)("span",{style:{color:"#dc2626",fontWeight:"500"},children:["$",e.employeeCost.toFixed(2)]})]})]})]},t)),(0,n.jsxs)("div",{style:{backgroundColor:"#eff6ff",padding:"12px",borderRadius:"6px",border:"1px solid #bfdbfe",marginTop:"8px"},children:[(0,n.jsxs)("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",marginBottom:"8px"},children:[n.jsx("span",{style:{fontSize:"14px",fontWeight:"600",color:"#111827"},children:"Total Family Coverage"}),(0,n.jsxs)("span",{style:{fontSize:"14px",fontWeight:"600",color:"#2563eb"},children:["You pay: $",Z.toFixed(2)]})]}),(0,n.jsxs)("div",{style:{display:"grid",gridTemplateColumns:"1fr 1fr",gap:"8px",fontSize:"12px"},children:[(0,n.jsxs)("div",{style:{display:"flex",alignItems:"center"},children:[n.jsx("span",{style:{color:"#6b7280"},children:"Total employer contribution: "}),(0,n.jsxs)("span",{style:{color:"#10b981",fontWeight:"600"},children:["$",U.toFixed(2)]})]}),(0,n.jsxs)("div",{style:{display:"flex",alignItems:"center"},children:[n.jsx("span",{style:{color:"#6b7280"},children:"Total you pay: "}),(0,n.jsxs)("span",{style:{color:"#dc2626",fontWeight:"600"},children:["$",Z.toFixed(2)]})]})]})]})]})]}),(0,n.jsxs)("div",{style:{marginTop:"16px",paddingTop:"16px",borderTop:"1px solid #e5e7eb",display:"none"},children:[n.jsx("h3",{style:{fontSize:"16px",fontWeight:"600",color:"#111827",marginBottom:"12px"},children:e.dependents&&e.dependents.length>0?"Family Members Covered:":"Coverage For:"}),(0,n.jsxs)("div",{style:{display:"flex",flexDirection:"column",gap:"8px"},children:[(0,n.jsxs)("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",backgroundColor:"#f0f9ff",padding:"12px",borderRadius:"6px",border:"1px solid #bfdbfe"},children:[(0,n.jsxs)("div",{children:[n.jsx("span",{style:{fontSize:"14px",fontWeight:"600",color:"#111827"},children:s.name||"Myself"}),n.jsx("span",{style:{fontSize:"14px",color:"#6b7280",marginLeft:"8px"},children:"(Employee)"})]}),n.jsx("span",{style:{fontSize:"12px",color:"#10b981",fontWeight:"500"},children:"✓ Covered"})]}),e.dependents&&e.dependents.map((e,t)=>(0,n.jsxs)("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",backgroundColor:"#f9fafb",padding:"12px",borderRadius:"6px",border:"1px solid #e5e7eb"},children:[(0,n.jsxs)("div",{children:[n.jsx("span",{style:{fontSize:"14px",fontWeight:"500",color:"#111827"},children:e.name}),(0,n.jsxs)("span",{style:{fontSize:"14px",color:"#6b7280",marginLeft:"8px"},children:["(","Spouse"===e.relationship?"Spouse/Partner":"Child",")"]})]}),n.jsx("span",{style:{fontSize:"12px",color:"#10b981",fontWeight:"500"},children:"✓ Covered"})]},t))]})]}),(q.length>0||V.length>0)&&(0,n.jsxs)("div",{style:{display:"flex",gap:"12px",marginTop:"24px"},children:[(0,n.jsxs)("button",{onClick:o,style:{flex:1,display:"flex",alignItems:"center",justifyContent:"center",gap:"8px",padding:"12px 20px",backgroundColor:"white",border:"1px solid #d1d5db",borderRadius:"8px",color:"#374151",cursor:"pointer",fontWeight:"500",fontSize:"16px"},children:[n.jsx(es,{size:16}),"Make Changes"]}),(0,n.jsxs)("button",{onClick:W,disabled:g,style:{flex:2,display:"flex",alignItems:"center",justifyContent:"center",gap:"8px",padding:"12px 20px",backgroundColor:g?"#666666":"#1f2937",border:"none",borderRadius:"8px",color:"white",cursor:g?"not-allowed":"pointer",fontWeight:"500",fontSize:"16px",opacity:g?.7:1,transition:"all 0.2s ease"},children:[n.jsx(u,{size:16}),g?"Creating Enrollments...":"Confirm Enrollment"]})]}),0===q.length&&0===V.length&&(0,n.jsxs)("div",{style:{backgroundColor:"#fef3c7",borderRadius:"8px",padding:"20px",marginTop:"24px",border:"1px solid #fbbf24",textAlign:"center"},children:[n.jsx("h3",{style:{fontSize:"18px",fontWeight:"600",color:"#92400e",margin:"0 0 8px 0"},children:"No Plans Selected"}),n.jsx("p",{style:{color:"#92400e",margin:0},children:"You haven't selected any benefit plans or waived any coverages yet. Go back to make your selections."})]})]}),h&&n.jsx("div",{style:{position:"fixed",top:0,left:0,right:0,bottom:0,backgroundColor:"rgba(0, 0, 0, 0.5)",display:"flex",alignItems:"center",justifyContent:"center",zIndex:1e3},children:(0,n.jsxs)("div",{style:{backgroundColor:"white",borderRadius:"12px",padding:"32px",maxWidth:"600px",width:"90%",boxShadow:"0 20px 25px -5px rgba(0, 0, 0, 0.1)",textAlign:"center"},children:[n.jsx("div",{style:{width:"60px",height:"60px",backgroundColor:"#ef4444",borderRadius:"50%",display:"flex",alignItems:"center",justifyContent:"center",margin:"0 auto 20px"},children:n.jsx("span",{style:{fontSize:"32px",color:"white"},children:"⚠️"})}),n.jsx("h3",{style:{fontSize:"20px",fontWeight:"600",color:"#111827",margin:"0 0 12px 0"},children:"Enrollment Error"}),n.jsx("p",{style:{color:"#6b7280",margin:"0 0 24px 0",lineHeight:"1.5",whiteSpace:"pre-line"},children:b}),n.jsx("button",{onClick:()=>{y(!1)},style:{padding:"12px 24px",backgroundColor:"#dc2626",border:"none",borderRadius:"8px",color:"white",cursor:"pointer",fontWeight:"500",fontSize:"16px"},children:"Try Again"})]})}),n.jsx(eC.Z,{isOpen:D,onClose:()=>j(!1),onSignatureComplete:e=>{console.log("✅ Signature completed, proceeding with enrollment..."),S(!0),j(!1),localStorage.setItem("enrollmentSignatureRef",e),z()},employeeName:s.name||"Employee"})]})},eI=(0,r.Z)("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]]),eP=(0,r.Z)("House",[["path",{d:"M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8",key:"5wwlr5"}],["path",{d:"M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"1d0kgt"}]]),eW=(0,r.Z)("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]]),ez=(0,r.Z)("Printer",[["path",{d:"M6 18H4a2 2 0 0 1-2-2v-5a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2v5a2 2 0 0 1-2 2h-2",key:"143wyd"}],["path",{d:"M6 9V3a1 1 0 0 1 1-1h10a1 1 0 0 1 1 1v6",key:"1itne7"}],["rect",{x:"6",y:"14",width:"12",height:"8",rx:"1",key:"1ue0tg"}]]),eE=({enrollmentData:e,planAssignments:t={}})=>{let o=(0,ea.useRouter)(),[r,i]=(0,l.useState)(!1),[a,s]=(0,l.useState)(!1),[d,x]=(0,l.useState)(null),g=(0,ed.C)(e=>e.user.userProfile),u=()=>{try{let e=Object.values(t).flat();if(0===e.length)return console.log("\uD83D\uDD0D No plan assignments found, using default date"),"January 1, 2025";let o=[];for(let t of e){let e=t.assignment||t,n=e.startDate||e.effectiveDate||e.coverageStartDate;if(n){let e=new Date(n);isNaN(e.getTime())||o.push(e)}}if(0===o.length)return console.log("\uD83D\uDD0D No valid start dates found in plan assignments, using default"),"January 1, 2025";let n=new Date(Math.min(...o.map(e=>e.getTime()))).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"});return console.log("\uD83D\uDD0D Dynamic benefits start date calculated:",n),console.log("\uD83D\uDD0D Plan assignments used for date calculation:",e.length,"assignments"),n}catch(e){return console.error("❌ Error getting benefits start date:",e),"January 1, 2025"}};return(0,l.useEffect)(()=>{console.log("\uD83D\uDD0D ConfirmationPage - Plan assignments received:",t),console.log("\uD83D\uDD0D ConfirmationPage - Plan assignment keys:",Object.keys(t));let o=localStorage.getItem("enrollmentSummarySnapshot");if(console.log("\uD83D\uDD0D Checking for stored enrollment summary..."),o)try{let e=JSON.parse(o);if(x(e),console.log("\uD83D\uDCF8 Loaded enrollment summary snapshot:",e),console.log("\uD83D\uDEE1️ Life insurance data in summary:",{lifePlan:e.lifePlan,lifeWaived:e.lifeWaived,lifeWaiveReason:e.lifeWaiveReason,hasLifePlan:!!e.lifePlan,lifePlanKeys:e.lifePlan?Object.keys(e.lifePlan):"null"}),console.log("\uD83D\uDC53 Vision data in summary:",{visionPlan:e.visionPlan,visionWaived:e.visionWaived,visionWaiveReason:e.visionWaiveReason,hasVisionPlan:!!e.visionPlan,visionPlanKeys:e.visionPlan?Object.keys(e.visionPlan):"null"}),console.log("\uD83E\uDDB7 Dental data in summary:",{dentalPlan:e.dentalPlan,dentalWaived:e.dentalWaived,dentalWaiveReason:e.dentalWaiveReason,hasDentalPlan:!!e.dentalPlan}),console.log("\uD83D\uDEE1️ AD&D data in summary:",{addPlan:e.addPlan,addWaived:e.addWaived,addWaiveReason:e.addWaiveReason,hasADDPlan:!!e.addPlan,addPlanKeys:e.addPlan?Object.keys(e.addPlan):"null"}),console.log("\uD83C\uDFAF Plan display logic check:",{shouldShowDental:!!(e.dentalPlan&&!e.dentalWaived),shouldShowVision:!!(e.visionPlan&&!e.visionWaived),shouldShowLife:!!(e.lifePlan&&!e.lifeWaived),shouldShowADD:!!(e.addPlan&&!e.addWaived),shouldShowDentalWaived:!!e.dentalWaived,shouldShowVisionWaived:!!e.visionWaived,shouldShowLifeWaived:!!e.lifeWaived,shouldShowADDWaived:!!e.addWaived}),console.log("\uD83D\uDD0D Detailed plan analysis:"),console.log("  Dental Plan:",e.dentalPlan?"EXISTS":"NULL","| Waived:",e.dentalWaived?"YES":"NO"),console.log("  Vision Plan:",e.visionPlan?"EXISTS":"NULL","| Waived:",e.visionWaived?"YES":"NO"),console.log("  Life Plan:",e.lifePlan?"EXISTS":"NULL","| Waived:",e.lifeWaived?"YES":"NO"),console.log("  AD&D Plan:",e.addPlan?"EXISTS":"NULL","| Waived:",e.addWaived?"YES":"NO"),!e.addPlan&&!e.addWaived){console.log("\uD83D\uDD27 AD&D data missing from snapshot, checking localStorage directly...");let t=localStorage.getItem("selectedADDPlan"),o=localStorage.getItem("addWaived");if(t)try{let o=JSON.parse(t);console.log("\uD83D\uDD27 Found AD&D plan in localStorage:",o),e.addPlan=o}catch(e){console.error("❌ Error parsing AD&D plan from localStorage:",e)}"true"===o&&(console.log("\uD83D\uDD27 Found AD&D waived in localStorage"),e.addWaived=!0,e.addWaiveReason=localStorage.getItem("addWaiveReason")),x({...e}),console.log("\uD83D\uDD27 Updated enrollment summary with AD&D data:",e)}}catch(e){console.error("❌ Error parsing enrollment summary:",e)}else if(console.warn("⚠️ No enrollment summary snapshot found in localStorage"),e){console.log("\uD83D\uDD04 Attempting to use enrollmentData as fallback:",e);let t={dentalPlan:e.selectedDental,visionPlan:e.selectedVision,lifePlan:e.selectedLife,addPlan:e.selectedADD,dependents:e.dependents,selectedCoverageTier:e.familyMembers||"Employee Only",enrollmentDate:new Date().toISOString(),dentalWaived:"true"===localStorage.getItem("dentalWaived"),visionWaived:"true"===localStorage.getItem("visionWaived"),lifeWaived:"true"===localStorage.getItem("lifeWaived"),addWaived:"true"===localStorage.getItem("addWaived"),dentalWaiveReason:localStorage.getItem("dentalWaiveReason")||void 0,visionWaiveReason:localStorage.getItem("visionWaiveReason")||void 0,lifeWaiveReason:localStorage.getItem("lifeWaiveReason")||void 0,addWaiveReason:localStorage.getItem("addWaiveReason")||void 0};x(t),console.log("\uD83D\uDD04 Using fallback enrollment summary:",t)}},[e,t]),(0,n.jsxs)("div",{style:{display:"flex",flexDirection:"column",gap:"24px"},children:[(0,n.jsxs)("div",{style:{display:"flex",gap:"16px"},children:[n.jsx("div",{style:{width:"40px",height:"40px",borderRadius:"50%",display:"flex",alignItems:"center",justifyContent:"center",flexShrink:0,overflow:"hidden"},children:n.jsx(m.default,{src:"/brea.png",alt:"Brea - AI Benefits Assistant",width:40,height:40,style:{borderRadius:"50%"}})}),(0,n.jsxs)("div",{style:{backgroundColor:"#f9fafb",borderRadius:"8px",padding:"16px",maxWidth:"1000px"},children:[n.jsx("p",{style:{color:"#374151",lineHeight:"1.6",margin:0,fontWeight:"bold"},children:"\uD83C\uDF89 Congratulations—you're enrolled!"}),n.jsx("p",{style:{color:"#374151",lineHeight:"1.6",margin:"8px 0 0 0"},children:"Take a look at the tips and follow-up actions to make the most of your benefits."})]})]}),(0,n.jsxs)("div",{style:{backgroundColor:"white",borderRadius:"8px",border:"1px solid #e5e7eb",padding:"24px",boxShadow:"0 1px 3px 0 rgba(0, 0, 0, 0.1)"},children:[d&&(0,n.jsxs)("div",{style:{backgroundColor:"#f0f9ff",borderRadius:"8px",padding:"20px",marginBottom:"24px",border:"1px solid #bfdbfe"},children:[(0,n.jsxs)("div",{style:{display:"flex",alignItems:"center",gap:"8px",marginBottom:"16px"},children:[n.jsx("span",{style:{fontSize:"16px"},children:"\uD83D\uDCCB"}),n.jsx("h3",{style:{fontSize:"18px",fontWeight:"600",color:"#111827",margin:0},children:"Your Enrollment Summary:"})]}),(()=>{let e=en();return e.selectedPlans&&e.selectedPlans.length>0||e.waivedCoverages&&e.waivedCoverages.length>0?n.jsx(ew,{showCosts:!1,showWaived:!0,style:"confirmation"}):(0,n.jsxs)("div",{style:{display:"flex",flexDirection:"column",gap:"12px"},children:[d.dentalPlan&&!d.dentalWaived&&(0,n.jsxs)("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center"},children:[n.jsx("span",{style:{color:"#374151",fontSize:"14px",fontWeight:"500"},children:"\uD83E\uDDB7 Dental Plan:"}),n.jsx("span",{style:{color:"#111827",fontSize:"14px",fontWeight:"600"},children:d.dentalPlan.name})]}),d.visionPlan&&!d.visionWaived&&(0,n.jsxs)("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center"},children:[n.jsx("span",{style:{color:"#374151",fontSize:"14px",fontWeight:"500"},children:"\uD83D\uDC53 Vision Plan:"}),n.jsx("span",{style:{color:"#111827",fontSize:"14px",fontWeight:"600"},children:d.visionPlan.name})]}),d.lifePlan&&!d.lifeWaived&&(0,n.jsxs)("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center"},children:[n.jsx("span",{style:{color:"#374151",fontSize:"14px",fontWeight:"500"},children:"\uD83D\uDEE1️ Life Insurance:"}),n.jsx("span",{style:{color:"#111827",fontSize:"14px",fontWeight:"600"},children:d.lifePlan.name||d.lifePlan.planName||"Life Insurance Plan"})]}),d.addPlan&&!d.addWaived&&(0,n.jsxs)("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center"},children:[n.jsx("span",{style:{color:"#374151",fontSize:"14px",fontWeight:"500"},children:"\uD83D\uDEE1️ Accidental Death & Dismemberment (AD&D):"}),n.jsx("span",{style:{color:"#111827",fontSize:"14px",fontWeight:"600"},children:d.addPlan.name||d.addPlan.planName||"AD&D Plan"})]}),d.dentalWaived&&(0,n.jsxs)("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center"},children:[n.jsx("span",{style:{color:"#dc2626",fontSize:"14px",fontWeight:"500"},children:"\uD83E\uDDB7 Dental:"}),n.jsx("span",{style:{color:"#dc2626",fontSize:"14px",fontWeight:"600"},children:"Waived"})]}),d.visionWaived&&(0,n.jsxs)("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center"},children:[n.jsx("span",{style:{color:"#dc2626",fontSize:"14px",fontWeight:"500"},children:"\uD83D\uDC53 Vision:"}),n.jsx("span",{style:{color:"#dc2626",fontSize:"14px",fontWeight:"600"},children:"Waived"})]}),d.lifeWaived&&(0,n.jsxs)("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center"},children:[n.jsx("span",{style:{color:"#dc2626",fontSize:"14px",fontWeight:"500"},children:"\uD83D\uDEE1️ Life Insurance:"}),n.jsx("span",{style:{color:"#dc2626",fontSize:"14px",fontWeight:"600"},children:"Waived"})]}),d.addWaived&&(0,n.jsxs)("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center"},children:[n.jsx("span",{style:{color:"#dc2626",fontSize:"14px",fontWeight:"500"},children:"\uD83D\uDEE1️ Accidental Death & Dismemberment (AD&D):"}),n.jsx("span",{style:{color:"#dc2626",fontSize:"14px",fontWeight:"600"},children:"Waived"})]}),(0,n.jsxs)("div",{style:{marginTop:"8px",paddingTop:"12px",borderTop:"1px solid #e5e7eb"},children:[(0,n.jsxs)("span",{style:{color:"#374151",fontSize:"14px",fontWeight:"500",marginBottom:"8px",display:"block"},children:["\uD83D\uDC68‍\uD83D\uDC69‍\uD83D\uDC67‍\uD83D\uDC66 ",d.dependents&&d.dependents.length>0?"Family Members Covered:":"Coverage For:"]}),n.jsx("div",{style:{marginLeft:"16px",marginBottom:"4px"},children:(0,n.jsxs)("span",{style:{color:"#111827",fontSize:"14px",fontWeight:"600"},children:[g.name||"Myself"," (Employee)"]})}),d.dependents&&d.dependents.map((e,t)=>n.jsx("div",{style:{marginLeft:"16px",marginBottom:"4px"},children:(0,n.jsxs)("span",{style:{color:"#111827",fontSize:"14px"},children:[e.name," (",e.relationship,")"]})},t))]})]})})()]}),d&&!d.dentalPlan&&!d.dentalWaived&&!d.visionPlan&&!d.visionWaived&&!d.lifePlan&&!d.lifeWaived&&!d.addPlan&&!d.addWaived&&(0,n.jsxs)("div",{style:{backgroundColor:"#fef2f2",borderRadius:"8px",padding:"20px",marginBottom:"24px",border:"1px solid #fca5a5"},children:[(0,n.jsxs)("div",{style:{display:"flex",alignItems:"center",gap:"8px",marginBottom:"16px"},children:[n.jsx("span",{style:{fontSize:"16px"},children:"⚠️"}),n.jsx("h3",{style:{fontSize:"18px",fontWeight:"600",color:"#dc2626",margin:0},children:"No Enrollment Data Found"})]}),n.jsx("p",{style:{color:"#dc2626",fontSize:"14px",margin:0},children:"It appears no plan selections or waive decisions were recorded. Please contact HR or try enrolling again."})]}),(0,n.jsxs)("div",{style:{backgroundColor:"#f8fafc",borderRadius:"8px",padding:"20px",marginBottom:"24px"},children:[(0,n.jsxs)("div",{style:{display:"flex",alignItems:"center",gap:"8px",marginBottom:"16px"},children:[n.jsx("span",{style:{fontSize:"16px"},children:"\uD83D\uDCCB"}),n.jsx("h3",{style:{fontSize:"18px",fontWeight:"600",color:"#111827",margin:0},children:"Next Steps & Pro Tips:"})]}),(0,n.jsxs)("div",{style:{display:"flex",flexDirection:"column",gap:"12px"},children:[(0,n.jsxs)("div",{style:{display:"flex",alignItems:"flex-start",gap:"12px"},children:[n.jsx(c,{style:{width:"16px",height:"16px",color:"#6b7280",marginTop:"2px",flexShrink:0}}),n.jsx("span",{style:{color:"#374151",fontSize:"14px"},children:"Need a dentist? Find one near you in your member portal"})]}),(0,n.jsxs)("div",{style:{display:"flex",alignItems:"flex-start",gap:"12px"},children:[n.jsx(p,{style:{width:"16px",height:"16px",color:"#6b7280",marginTop:"2px",flexShrink:0}}),n.jsx("span",{style:{color:"#374151",fontSize:"14px"},children:"Use your vision benefit for a free exam & $150 frames"})]}),(0,n.jsxs)("div",{style:{display:"flex",alignItems:"flex-start",gap:"12px"},children:[n.jsx("span",{style:{fontSize:"14px",marginTop:"2px"},children:"\uD83D\uDCB0"}),n.jsx("span",{style:{color:"#374151",fontSize:"14px"},children:"Set up your HSA/FSA to save on taxes"})]}),(0,n.jsxs)("div",{style:{display:"flex",alignItems:"flex-start",gap:"12px"},children:[n.jsx(eI,{style:{width:"16px",height:"16px",color:"#6b7280",marginTop:"2px",flexShrink:0}}),n.jsx("span",{style:{color:"#374151",fontSize:"14px"},children:"Download your insurance apps for easy access"})]}),(0,n.jsxs)("div",{style:{display:"flex",alignItems:"flex-start",gap:"12px"},children:[n.jsx(c,{style:{width:"16px",height:"16px",color:"#6b7280",marginTop:"2px",flexShrink:0}}),n.jsx("span",{style:{color:"#374151",fontSize:"14px"},children:"Schedule your preventive care visits early in the year"})]})]})]}),(0,n.jsxs)("div",{style:{display:"flex",gap:"12px",justifyContent:"center",flexWrap:"wrap"},children:[(0,n.jsxs)("button",{onClick:()=>{console.log("\uD83E\uDDF9 Final cleanup - removing remaining enrollment data..."),["selectedDentalPlan","selectedVisionPlan","selectedLifePlan","selectedADDPlan","dentalWaived","visionWaived","lifeWaived","addWaived","dentalWaiveReason","visionWaiveReason","lifeWaiveReason","addWaiveReason","enrollmentSummarySnapshot"].forEach(e=>{localStorage.removeItem(e)}),console.log("✅ Final cleanup completed");let e=g?.role?.toLowerCase();"broker"===e||"admin"===e?o.push("/ai-enroller"):o.push("/dashboard")},style:{display:"flex",alignItems:"center",gap:"8px",padding:"12px 20px",backgroundColor:"#000000",border:"none",borderRadius:"8px",color:"white",cursor:"pointer",fontWeight:"600",fontSize:"14px",transition:"all 0.2s ease",boxShadow:"0 2px 4px rgba(0, 0, 0, 0.1)"},onMouseOver:e=>{e.currentTarget.style.backgroundColor="#374151",e.currentTarget.style.transform="translateY(-1px)",e.currentTarget.style.boxShadow="0 4px 8px rgba(0, 0, 0, 0.15)"},onMouseOut:e=>{e.currentTarget.style.backgroundColor="#000000",e.currentTarget.style.transform="translateY(0)",e.currentTarget.style.boxShadow="0 2px 4px rgba(0, 0, 0, 0.1)"},children:[n.jsx(eP,{size:16}),"Return to Home"]}),(0,n.jsxs)("button",{onClick:()=>{s(!0)},style:{display:"flex",alignItems:"center",gap:"8px",padding:"12px 20px",backgroundColor:"white",border:"1px solid #d1d5db",borderRadius:"8px",color:"#374151",cursor:"pointer",fontSize:"14px",fontWeight:"500"},onMouseOver:e=>e.currentTarget.style.backgroundColor="#f9fafb",onMouseOut:e=>e.currentTarget.style.backgroundColor="white",children:[n.jsx("span",{style:{color:"#2563eb"},children:"❓"}),"Ask Questions"]}),(0,n.jsxs)("button",{onClick:()=>{let e=encodeURIComponent("Benefits Enrollment Summary"),t=encodeURIComponent("Please find my benefits enrollment summary attached.");window.location.href=`mailto:?subject=${e}&body=${t}`},style:{display:"flex",alignItems:"center",gap:"8px",padding:"12px 20px",backgroundColor:"white",border:"1px solid #d1d5db",borderRadius:"8px",color:"#374151",cursor:"pointer",fontSize:"14px",fontWeight:"500"},onMouseOver:e=>e.currentTarget.style.backgroundColor="#f9fafb",onMouseOut:e=>e.currentTarget.style.backgroundColor="white",children:[n.jsx(eW,{size:16,style:{color:"#6b7280"}}),"Email Summary"]}),(0,n.jsxs)("button",{onClick:()=>{if(!d){console.warn("No enrollment summary data available for printing");return}let e=[],t=[],o=0,n=0,l=(e,t)=>{if(!e||!e.coverageTiers)return{employeeCost:0,employerCost:0,totalCost:0};let o=e.coverageTiers.find(e=>e.tierName===t);return o?{employeeCost:o.employeeCost||0,employerCost:o.employerCost||0,totalCost:o.totalCost||o.employeeCost+o.employerCost||0}:{employeeCost:e.cost||0,employerCost:0,totalCost:e.cost||0}};if(d.dentalWaived)t.push("Dental");else if(d.dentalPlan){let t=l(d.dentalPlan,d.selectedCoverageTier||"Employee Only");e.push({planName:d.dentalPlan.name||"Dental Plan",planType:"Dental",...t}),o+=t.employeeCost,n+=t.employerCost}if(d.visionWaived)t.push("Vision");else if(d.visionPlan){let t=l(d.visionPlan,d.selectedCoverageTier||"Employee Only");e.push({planName:d.visionPlan.name||"Vision Plan",planType:"Vision",...t}),o+=t.employeeCost,n+=t.employerCost}if(d.lifeWaived)t.push("Life Insurance");else if(d.lifePlan){let t=l(d.lifePlan,d.selectedCoverageTier||"Employee Only");e.push({planName:d.lifePlan.name||"Life Insurance Plan",planType:"Life Insurance",...t}),o+=t.employeeCost,n+=t.employerCost}if(d.addWaived)t.push("AD&D");else if(d.addPlan){let t=l(d.addPlan,d.selectedCoverageTier||"Employee Only");e.push({planName:d.addPlan.name||"AD&D Plan",planType:"AD&D",...t}),o+=t.employeeCost,n+=t.employerCost}let r=o+n,i=`
      <html>
        <head>
          <title>Benefits Enrollment Summary</title>
          <style>
            @import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap');

            * {
              margin: 0;
              padding: 0;
              box-sizing: border-box;
            }

            body {
              font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
              margin: 0;
              padding: 40px;
              color: #1f2937;
              line-height: 1.6;
              background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
              min-height: 100vh;
            }

            .container {
              max-width: 800px;
              margin: 0 auto;
              background: white;
              border-radius: 16px;
              box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
              overflow: hidden;
            }

            .header {
              background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
              color: white;
              text-align: center;
              padding: 40px 30px;
              position: relative;
            }

            .header h1 {
              font-size: 32px;
              font-weight: 700;
              margin-bottom: 8px;
            }

            .header p {
              font-size: 16px;
              opacity: 0.9;
            }

            .content {
              padding: 40px 30px;
            }

            .coverage-info {
              background: #f8fafc;
              padding: 20px;
              border-radius: 12px;
              margin-bottom: 30px;
              border-left: 4px solid #3b82f6;
            }

            .coverage-info h3 {
              color: #1e40af;
              font-size: 18px;
              font-weight: 600;
              margin-bottom: 8px;
            }

            .plan-section {
              margin: 20px 0;
              padding: 24px;
              border: 1px solid #e5e7eb;
              border-radius: 12px;
              background: white;
              box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            }

            .plan-title {
              font-weight: 600;
              font-size: 18px;
              margin-bottom: 16px;
              color: #1f2937;
              display: flex;
              align-items: center;
              gap: 8px;
            }

            .cost-breakdown {
              background: #f9fafb;
              padding: 16px;
              border-radius: 8px;
              border: 1px solid #f3f4f6;
            }

            .cost-breakdown p {
              margin: 8px 0;
              display: flex;
              justify-content: space-between;
              align-items: center;
            }

            .cost-breakdown p:last-child {
              text-align: right;
            }

            .total-section {
              margin-top: 30px;
              padding: 24px;
              background: linear-gradient(135deg, #10b981 0%, #059669 100%);
              color: white;
              border-radius: 12px;
              box-shadow: 0 4px 6px rgba(16, 185, 129, 0.25);
            }

            .total-section h3 {
              font-size: 20px;
              font-weight: 700;
              margin-bottom: 16px;
              text-align: center;
            }

            .total-section p {
              margin: 12px 0;
              display: flex;
              justify-content: space-between;
              align-items: center;
              font-size: 16px;
            }

            .waived {
              background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
              border-color: #fca5a5;
              color: #991b1b;
            }

            .dependents-section {
              margin-top: 30px;
              padding: 24px;
              background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
              border-radius: 12px;
              border: 1px solid #bfdbfe;
            }

            .dependents-section h3 {
              color: #1e40af;
              font-size: 18px;
              font-weight: 600;
              margin-bottom: 16px;
            }

            .next-steps {
              margin-top: 30px;
              padding: 24px;
              background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
              border-radius: 12px;
              border: 1px solid #f59e0b;
            }

            .next-steps .plan-title {
              color: #92400e;
            }

            .next-steps ul {
              list-style: none;
              padding: 0;
            }

            .next-steps li {
              margin: 12px 0;
              padding: 8px 0;
              border-bottom: 1px solid rgba(146, 64, 14, 0.1);
              color: #92400e;
              font-weight: 500;
            }

            .next-steps li:last-child {
              border-bottom: none;
            }

            @media print {
              body {
                background: white !important;
                padding: 20px !important;
              }

              .container {
                box-shadow: none !important;
                border: 1px solid #e5e7eb;
              }

              .header {
                background: #4f46e5 !important;
                -webkit-print-color-adjust: exact;
                color-adjust: exact;
              }

              .total-section {
                background: #10b981 !important;
                -webkit-print-color-adjust: exact;
                color-adjust: exact;
              }
            }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="header">
              <h1>🎉 Benefits Enrollment Summary</h1>
              <p><strong>Enrollment Date:</strong> ${new Date(d.enrollmentDate||Date.now()).toLocaleDateString()}</p>
              <p><strong>Coverage Effective:</strong> ${u()}</p>
            </div>

            <div class="content">
              <div class="coverage-info">
                <h3>📋 Coverage Information</h3>
                <p><strong>Coverage Tier:</strong> ${d.selectedCoverageTier||"Employee Only"}</p>
              </div>

          ${e.map(e=>`
            <div class="plan-section">
              <div class="plan-title">${e.planType}: ${e.planName}</div>
              <div class="cost-breakdown">
                <p><strong>Total Cost:</strong> $${e.totalCost.toFixed(2)}/paycheck</p>
                <p><strong>Employer Contribution:</strong> $${e.employerCost.toFixed(2)}</p>
                <p><strong>Your Cost:</strong> $${e.employeeCost.toFixed(2)}</p>
              </div>
            </div>
          `).join("")}

          ${t.map(e=>`
            <div class="plan-section waived">
              <div class="plan-title">${e}: Waived</div>
              <p>No coverage selected - $0.00/paycheck</p>
            </div>
          `).join("")}

          <div class="total-section">
            <h3>Total Summary</h3>
            <p><strong>Total Plan Cost: $${r.toFixed(2)}/paycheck</strong></p>
            <p><strong>Employer Contribution: -$${n.toFixed(2)}</strong></p>
            <p><strong>Your Cost per Paycheck: $${o.toFixed(2)}</strong></p>
          </div>

          <div class="dependents-section">
            <h3>${d.dependents&&d.dependents.length>0?"Family Members Covered":"Coverage For"}</h3>
            <p><strong>${g.name||"Myself"}</strong> (Employee)</p>
            ${d.dependents?d.dependents.map(e=>`
              <p><strong>${e.name}</strong> (${e.relationship})</p>
            `).join(""):""}
          </div>

              <div class="next-steps">
                <div class="plan-title">💡 Next Steps & Pro Tips</div>
                <ul>
                  <li>🦷 Find dentists in your member portal</li>
                  <li>👓 Use vision benefit for free exam & $150 frames</li>
                  <li>💰 Set up HSA/FSA to save on taxes</li>
                  <li>📱 Download insurance apps for easy access</li>
                  <li>🗓️ Schedule preventive care visits early</li>
                </ul>
              </div>
            </div>
          </div>
        </body>
      </html>
    `,a=window.open("","_blank");a&&(a.document.write(i),a.document.close(),a.print())},style:{display:"flex",alignItems:"center",gap:"8px",padding:"12px 20px",backgroundColor:"white",border:"1px solid #d1d5db",borderRadius:"8px",color:"#374151",cursor:"pointer",fontSize:"14px",fontWeight:"500"},onMouseOver:e=>e.currentTarget.style.backgroundColor="#f9fafb",onMouseOut:e=>e.currentTarget.style.backgroundColor="white",children:[n.jsx(ez,{size:16,style:{color:"#6b7280"}}),"Print Summary"]})]})]}),r&&n.jsx(j,{onClose:()=>i(!1)}),a&&n.jsx(w.Z,{isOpen:a,onClose:()=>s(!1)})]})},eT=()=>{let[e,t]=(0,l.useState)(!1);return(0,n.jsxs)(n.Fragment,{children:[n.jsx("div",{onClick:()=>{t(!0)},style:{position:"fixed",bottom:"24px",right:"24px",width:"60px",height:"60px",backgroundColor:"#000000",borderRadius:"50%",display:"flex",alignItems:"center",justifyContent:"center",cursor:"pointer",boxShadow:"0 4px 12px rgba(0, 0, 0, 0.15)",zIndex:1e3,transition:"all 0.3s ease",border:"2px solid #ffffff"},onMouseOver:e=>{e.currentTarget.style.transform="scale(1.1)",e.currentTarget.style.boxShadow="0 6px 20px rgba(0, 0, 0, 0.25)"},onMouseOut:e=>{e.currentTarget.style.transform="scale(1)",e.currentTarget.style.boxShadow="0 4px 12px rgba(0, 0, 0, 0.15)"},children:n.jsx(m.default,{src:"/brea.png",alt:"Chat with Brea",width:40,height:40,style:{borderRadius:"50%",objectFit:"cover"}})}),(0,n.jsxs)("div",{style:{position:"fixed",bottom:"32px",right:"92px",backgroundColor:"#000000",color:"white",padding:"8px 12px",borderRadius:"8px",fontSize:"14px",fontWeight:"500",zIndex:999,opacity:e?0:1,visibility:e?"hidden":"visible",transition:"all 0.3s ease",whiteSpace:"nowrap",boxShadow:"0 2px 8px rgba(0, 0, 0, 0.15)"},children:["Chat with Brea",n.jsx("div",{style:{position:"absolute",top:"50%",right:"-6px",transform:"translateY(-50%)",width:0,height:0,borderLeft:"6px solid #000000",borderTop:"6px solid transparent",borderBottom:"6px solid transparent"}})]}),e&&n.jsx(w.Z,{isOpen:e,onClose:()=>{t(!1)}})]})};var eR=o(67925);class eA{constructor(){this.apiBaseUrl="http://localhost:8080"}getHeaders(){let e={"Content-Type":"application/json"},t=localStorage.getItem("userid1")||localStorage.getItem("userId");return t&&(e["user-id"]=t),e}async getPlanById(e){try{let t=await fetch(`${this.apiBaseUrl}/api/pre-enrollment/plans/${e}`,{method:"GET",headers:this.getHeaders()});if(!t.ok)throw Error(`HTTP error! status: ${t.status}`);let o=await t.json();return{success:!0,data:o.plan}}catch(e){return console.error("Error fetching plan details:",e),{success:!1,error:"Failed to fetch plan details"}}}async getPlanAssignmentsByCompany(e){try{console.log("\uD83D\uDD0D Fetching plan assignments for company:",e);let t=new URLSearchParams;t.append("includeExpired","false");let o=await fetch(`${this.apiBaseUrl}/api/pre-enrollment/plan-assignments/company/${e}?${t.toString()}`,{method:"GET",headers:this.getHeaders()});if(!o.ok)throw Error(`HTTP error! status: ${o.status}`);let n=await o.json();console.log("\uD83D\uDCCB Plan assignments response:",n);let l=n.assignments||[];console.log("\uD83D\uDCCA Total assignments found:",l.length);let r=new Date,i=l.filter(e=>{let t=new Date(e.enrollmentStartDate),o=new Date(e.enrollmentEndDate),n=e.isActive&&t<=r&&o>=r;return n||console.log(`⏰ Skipping assignment ${e._id} - not in enrollment period:`,{enrollmentStart:t.toISOString(),enrollmentEnd:o.toISOString(),now:r.toISOString(),isActive:e.isActive}),n});console.log(`📊 Assignments in enrollment period: ${i.length} of ${l.length}`);let a={};for(let e of i){console.log("\uD83D\uDD0D Processing assignment:",e._id,"planId:",e.planId);let t=await this.getPlanById(e.planId);if(t.success&&t.data){let o=t.data;console.log("✅ Plan details:",{planName:o.planName,coverageSubTypes:o.coverageSubTypes});let n={...o,assignment:e,id:e._id,name:o.planName,planCode:o.planCode,carrierName:o.carrierName,coverageType:o.coverageType,coverageSubTypes:o.coverageSubTypes,cost:this.calculatePlanCost(e),features:this.extractPlanFeatures(o,e)};o.coverageSubTypes&&Array.isArray(o.coverageSubTypes)&&o.coverageSubTypes.forEach(e=>{let t=e.toLowerCase();a[t]||(a[t]=[]),a[t].push(n)})}else console.warn("⚠️ Failed to fetch plan details for:",e.planId)}return console.log("\uD83C\uDFAF Grouped plans by subtype:",Object.keys(a)),{success:!0,data:a}}catch(e){return console.error("❌ Error fetching plan assignments:",e),{success:!1,error:e instanceof Error?e.message:"Failed to fetch plan assignments"}}}calculatePlanCost(e,t){try{if(t&&e.coverageTiers){let o=e.coverageTiers.find(e=>e.tierName===t);if(o)return o.employeeCost||0}let o=e.coverageTiers?.find(e=>e.tierName?.toLowerCase().includes("employee only")||e.tierName?.toLowerCase()==="employee");if(o)return o.employeeCost||0;if(e.coverageTiers&&e.coverageTiers.length>0)return e.coverageTiers[0].employeeCost||0;return 0}catch(e){return console.warn("Error calculating plan cost:",e),0}}extractPlanFeatures(e,t){let o=[];return e.highlights&&Array.isArray(e.highlights)&&e.highlights.length>0?e.highlights:t.highlights&&Array.isArray(t.highlights)&&t.highlights.length>0?t.highlights:(e.metalTier&&o.push(`${e.metalTier} tier coverage`),e.carrierName&&o.push(`${e.carrierName} network`),t.coverageTiers&&t.coverageTiers.length>0&&o.push(`${t.coverageTiers.length} coverage tier options`),e.coverageType&&o.push(`${e.coverageType} benefits`),o.length>0?o:["Comprehensive coverage","Network benefits","Quality care"])}}new eA,o(43933);var eB=o(22758);let eF=[{primary:"#1E90FF",accent:"#E6F0FF",name:"Blue"},{primary:"#32CD32",accent:"#E6FFE6",name:"Green"},{primary:"#FF8C00",accent:"#FFF3E6",name:"Orange"},{primary:"#800080",accent:"#F5E6FF",name:"Purple"}],eN={medical:"Medical",health:"Medical","health insurance":"Medical",dental:"Dental","dental plan":"Dental",vision:"Vision","vision plan":"Vision","term life":"Life Insurance",life:"Life Insurance","life insurance":"Life Insurance","supplemental life insurance":"Life Insurance","whole life":"Life Insurance","group (employer) life":"Life Insurance","accidental death & dismemberment (ad&d)":"AD&D Insurance","ad&d":"AD&D Insurance",add:"AD&D Insurance","short-term disability":"Disability Insurance","long-term disability":"Disability Insurance",std:"Disability Insurance",ltd:"Disability Insurance","hospital indemnity":"Voluntary Benefits","accident insurance":"Voluntary Benefits","critical illness insurance":"Voluntary Benefits","cancer insurance":"Voluntary Benefits"};function eO(){let{logout:e}=(0,eB.a)();(0,ed.C)(e=>e.user.userProfile);let[t,o]=(0,l.useState)(0),[r,m]=(0,l.useState)({familyMembers:"",expectedMedicalUsage:"",budgetPreference:"",chronicConditions:!1,prescriptionNeeds:!1,hasPreferredDoctors:!1}),[y,b]=(0,l.useState)(null),[v,D]=(0,l.useState)(!1),[j,C]=(0,l.useState)([]),[S,w]=(0,l.useState)([]),[k,P]=(0,l.useState)([]),W=(0,l.useRef)(null),[z,T]=(0,l.useState)({}),[R,A]=(0,l.useState)(!1),[B,F]=(0,l.useState)(null),[N,M]=(0,l.useState)([]),[$,L]=(0,l.useState)([]),H=e=>{switch(e){case"employee-only":default:return"Employee Only";case"employee-spouse":return"Employee + Spouse";case"employee-children":return"Employee + Child(ren)";case"employee-family":return"Family"}},V=[{id:"kickoff",title:"Welcome & Kickoff",icon:i,completed:!1},{id:"personalization",title:"Smart Personalization",icon:a,completed:!1},{id:"dependents",title:"Family Members",icon:s,completed:!1},...N.map((e,t)=>({id:`plan-${t}`,title:`${e} Plan`,icon:"Dental"===e?d:"Vision"===e?p:"Life Insurance"===e?c:"AD&D Insurance"===e?x:g,completed:!1})),{id:"summary",title:"Summary Review",icon:g,completed:!1},{id:"confirmation",title:"Confirmation",icon:u,completed:!1}],Z=e=>{if(e<3)return!0;let t=e-3,o=N.length;if(t>=0&&t<o){let e=N[t],o={Medical:"medical",Dental:"dental",Vision:"vision","Life Insurance":"life","AD&D Insurance":"add","Disability Insurance":"disability","Voluntary Benefits":"voluntary"}[e]||e.toLowerCase().replace(/\s+/g,""),n=localStorage.getItem(`selected${e.replace(/\s+/g,"")}Plan`),l=localStorage.getItem(`${o}Waived`),r=null!==J(e),i=Q(e);return null!==n||"true"===l||r||i}return!0},U=e=>{let t=[],o=[];return e?.details?.dateOfBirth||(t.push("Date of Birth"),o.push("Date of birth is required for age-based cost calculations")),e?.details?.hireDate||(t.push("Hire Date"),o.push("Hire date is required for waiting period eligibility")),e?.details?.employeeClassType||(t.push("Employee Class Type"),o.push("Employee class type is required for plan eligibility")),e?.details?.phoneNumber||(t.push("Phone Number"),o.push("Phone number is required for enrollment communications")),{isValid:0===t.length,missingFields:t,errors:o}},_=()=>{if(2===t){let e=U(B);if(!e.isValid){let t=e.missingFields.join(", ");alert(`Please complete your profile before continuing. Missing required fields: ${t}

Click the "Complete Profile" button on the dependents page to update your information.`);return}console.log("Step 2 Next clicked, currentDependents:",k),X(k)}else t<V.length-1&&(Z(t)?o(t+1):alert("Please select a plan or choose to waive coverage before proceeding."))},Y=()=>{t>0&&o(t-1)},G=e=>{m(t=>({...t,...e}));let t=H(e.familyMembers||"employee-only");localStorage.setItem("selectedCoverageTier",t),console.log("\uD83C\uDFAF Stored coverage tier in localStorage:",t);let o=[];"employee-spouse"===e.familyMembers?o.push("spouse"):"employee-children"===e.familyMembers?o.push("children"):"employee-family"===e.familyMembers&&o.push("spouse","children"),C(o),b(eo(e)),_()},X=e=>{console.log("handleDependentsConfirm called with:",e),w(e),o(t+1)},K=()=>localStorage.getItem("userid1")||localStorage.getItem("userId"),ee=async()=>{try{let e=K();if(!e){console.error("User ID not found");return}let t=await (0,O.A_)("/employee",{"user-id":e});t?.currentUser&&(F(t.currentUser),console.log("✅ User details refreshed after profile update"))}catch(e){console.error("❌ Error refreshing user details:",e)}},et=()=>{Y()},eo=e=>{let{expectedMedicalUsage:t,budgetPreference:o}=e;return{plan:{name:"Anthem PPO 035",cost:82.9,deductible:2e3,features:["Balanced cost and coverage","Deductible: $2,000","Covers your PCP visits at $25","Good for moderate usage & predictable costs"]},reason:"This plan offers the best balance of monthly cost and coverage for someone with moderate healthcare needs."}},en=(e,t)=>{let o=`selected${e.charAt(0).toUpperCase()+e.slice(1)}`;console.log(`🔄 Setting ${o} with data:`,t),"life"===e&&console.log("\uD83D\uDEE1️ Life insurance plan selection details:",{planData:t,hasId:!!t?.id,hasName:!!t?.name,hasCoverageTiers:!!t?.coverageTiers,keys:t?Object.keys(t):"null"}),m(n=>{let l={...n,[o]:t};return console.log("\uD83D\uDCDD Updated user profile:",l),"life"===e&&console.log("\uD83D\uDEE1️ Life insurance in user profile:",l.selectedLife),l})},el=()=>{o(1)},er=()=>{console.log("\uD83C\uDFAF Enrollment confirmed! Moving to confirmation page..."),console.log("\uD83D\uDD0D Current user profile before creating summary:",r);let e=(e,t)=>{if(t)return console.log(`✅ Using userProfile data for ${e}:`,t),t;let o=localStorage.getItem(e);if(o)try{let t=JSON.parse(o);return console.log(`✅ Using localStorage data for ${e}:`,t),t}catch(t){console.error(`❌ Error parsing ${e} from localStorage:`,t)}return console.log(`⚠️ No data found for ${e}`),null},t={dentalPlan:e("selectedDentalPlan",r.selectedDental),visionPlan:e("selectedVisionPlan",r.selectedVision),lifePlan:e("selectedLifePlan",r.selectedLife),addPlan:e("selectedADDPlan",r.selectedADD),dependents:S,selectedCoverageTier:H(r.familyMembers||"employee-only"),enrollmentDate:new Date().toISOString(),dentalWaived:"true"===localStorage.getItem("dentalWaived"),visionWaived:"true"===localStorage.getItem("visionWaived"),lifeWaived:"true"===localStorage.getItem("lifeWaived"),addWaived:"true"===localStorage.getItem("addWaived"),dentalWaiveReason:localStorage.getItem("dentalWaiveReason"),visionWaiveReason:localStorage.getItem("visionWaiveReason"),lifeWaiveReason:localStorage.getItem("lifeWaiveReason"),addWaiveReason:localStorage.getItem("addWaiveReason")};console.log("\uD83D\uDD0D All plan data in enrollment summary:",{dentalPlan:t.dentalPlan,dentalWaived:t.dentalWaived,visionPlan:t.visionPlan,visionWaived:t.visionWaived,lifePlan:t.lifePlan,lifeWaived:t.lifeWaived,addPlan:t.addPlan,addWaived:t.addWaived,userProfileData:{selectedDental:r.selectedDental,selectedVision:r.selectedVision,selectedLife:r.selectedLife}}),localStorage.setItem("enrollmentSummarySnapshot",JSON.stringify(t)),console.log("\uD83D\uDCF8 Enrollment summary snapshot saved:",t),D(!0),_()};return(0,n.jsxs)("div",{className:"min-h-screen",style:{backgroundColor:"#f3f4f6",fontFamily:'"SF Pro", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif'},children:[n.jsx(eR.Z,{}),(0,n.jsxs)("div",{style:{maxWidth:"1024px",margin:"0 auto",padding:"clamp(16px, 4vw, 32px) clamp(12px, 3vw, 24px)",minHeight:"calc(100vh - 65px)"},children:[(0,n.jsxs)("div",{style:{display:"flex",alignItems:"center",justifyContent:"space-between",marginBottom:"24px",flexWrap:"wrap",gap:"16px"},children:[n.jsx("div",{style:{display:"flex",alignItems:"center",minWidth:"0",flex:"1"},children:(0,n.jsxs)("div",{style:{minWidth:"0"},children:[n.jsx("h1",{style:{fontSize:"clamp(20px, 5vw, 24px)",fontWeight:"600",color:"#111827",margin:0,fontFamily:"'SF Pro', 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif"},children:"Your Smart Benefits Assistant"}),n.jsx("p",{style:{fontSize:"clamp(12px, 3vw, 14px)",color:"#6b7280",margin:0,lineHeight:"1.4",fontFamily:"'SF Pro', 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif"},children:"Compare Plans. Ask Questions. Choose Smarter"})]})}),n.jsx("div",{style:{display:"flex",alignItems:"center",gap:"12px"}})]}),(0,n.jsxs)("div",{style:{backgroundColor:"white",borderRadius:"16px",border:"1px solid #e5e7eb",padding:"24px",marginBottom:"24px",boxShadow:"0 1px 3px rgba(0, 0, 0, 0.1)",marginLeft:"-20px",marginRight:"-20px",maxWidth:"calc(100% + 40px)"},children:[(0,n.jsxs)("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",marginBottom:"20px",flexWrap:"wrap",gap:"12px"},children:[n.jsx("h2",{style:{fontSize:"clamp(18px, 4vw, 20px)",fontWeight:"600",color:"#111827",margin:0,fontFamily:"sans-serif"},children:"Enrollment Progress"}),(0,n.jsxs)("span",{style:{fontSize:"clamp(12px, 3vw, 14px)",color:"#6b7280",backgroundColor:"#f3f4f6",padding:"6px 12px",borderRadius:"20px",fontWeight:"500",fontFamily:"sans-serif"},children:["Step ",t+1," of ",V.length]})]}),n.jsx("div",{style:{width:"100%",backgroundColor:"#e5e7eb",borderRadius:"12px",height:"8px",marginBottom:"24px"},children:n.jsx("div",{style:{backgroundColor:"#000000",height:"8px",borderRadius:"12px",transition:"all 0.4s ease",width:`${t/(V.length-1)*100}%`}})}),n.jsx("div",{className:"step-grid",style:{marginBottom:"8px"},children:V.map((e,o)=>{let l=e.icon,r=o===t,i=o<t,a=i?{backgroundColor:"#ddd6fe",color:"#6d28d9",border:"none"}:r?{backgroundColor:"#ede9fe",color:"#7c3aed",border:"none"}:{backgroundColor:"#f3f4f6",color:"#6b7280",border:"none"};return(0,n.jsxs)("div",{style:{display:"inline-flex",alignItems:"center",gap:"6px",padding:"8px 16px",borderRadius:"24px",fontSize:"13px",fontWeight:"450",transition:"all 0.2s ease",backgroundColor:a.backgroundColor,color:a.color,border:a.border,fontFamily:"sans-serif",width:"fit-content",justifyContent:"flex-start",whiteSpace:"nowrap",minWidth:"fit-content"},children:[n.jsx(l,{size:14}),n.jsx("span",{style:{lineHeight:"1.2"},children:e.title}),i&&n.jsx("div",{style:{display:"inline-flex",alignItems:"center",justifyContent:"center",width:"16px",height:"16px",backgroundColor:"#7c3aed",borderRadius:"50%",marginLeft:"8px",fontSize:"10px",fontWeight:"600",color:"white",lineHeight:"1"},children:"✓"})]},e.id)})})]}),(0,n.jsxs)("div",{children:[n.jsx("div",{ref:W,style:{width:"100%"},children:(()=>{switch(t){case 0:return n.jsx(I,{onNext:_});case 1:return n.jsx(E,{onComplete:G,initialData:r});case 2:let e=(()=>{let e=r?.familyMembers||"employee-only";switch(console.log("\uD83D\uDD0D Coverage tier selected:",e),e){case"employee-only":return[];case"employee-spouse":return["spouse"];case"employee-children":return["children"];case"employee-family":return["spouse","children"];default:return console.log("⚠️ Unknown family members selection:",e),[]}})();return console.log("\uD83D\uDC65 Filtered dependents for coverage tier:",e),n.jsx(q,{selectedDependents:e,onConfirm:X,onBack:et,onDependentsChange:P,onProfileUpdate:ee});default:let o=t-3,l=N.length;if(o>=0&&o<l){let e=N[o],t=function(e,t){let o=[];return Object.entries(eN).forEach(([n,l])=>{l===e&&t[n]&&o.push(...t[n])}),o}(e,z),l=function(e,t){let o=t.indexOf(e);return o>=0?o%eF.length:0}(e,N);return n.jsx(ei,{category:e,plans:t,selectedCoverageTier:H(r.familyMembers||"employee-only"),themeIndex:l,onPlanSelect:t=>{en({Medical:"medical",Dental:"dental",Vision:"vision","Life Insurance":"life","AD&D Insurance":"add"}[e]||"medical",t)}})}let i=3+l;if(t===i)return n.jsx(ek,{enrollmentData:{dentalPlan:r.selectedDental,visionPlan:r.selectedVision,lifePlan:r.selectedLife,addPlan:r.selectedADD,dependents:S},selectedCoverageTier:H(r.familyMembers||"employee-only"),onMakeChanges:el,onConfirmEnrollment:er,planAssignments:z});if(t===i+1)return n.jsx(eE,{enrollmentData:{...r,dependents:S},planAssignments:z});return n.jsx(I,{onNext:_})}})()}),t>0&&t<V.length-1&&(0,n.jsxs)("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",marginTop:"32px",paddingTop:"24px",borderTop:"1px solid #e5e7eb",gap:"16px"},children:[(0,n.jsxs)("button",{onClick:Y,style:{display:"flex",alignItems:"center",gap:"8px",padding:"12px 20px",backgroundColor:"white",border:"1px solid #e5e7eb",borderRadius:"8px",cursor:"pointer",fontSize:"14px",fontWeight:"500",color:"#374151",transition:"all 0.2s ease",fontFamily:"sans-serif",boxShadow:"0 1px 3px rgba(0, 0, 0, 0.1)"},onMouseOver:e=>{e.currentTarget.style.backgroundColor="#f9fafb",e.currentTarget.style.borderColor="#d1d5db"},onMouseOut:e=>{e.currentTarget.style.backgroundColor="white",e.currentTarget.style.borderColor="#e5e7eb"},children:[n.jsx(f.Z,{size:16}),"Back"]}),t<3+N.length&&(0,n.jsxs)("button",{onClick:_,style:{display:"flex",alignItems:"center",gap:"8px",padding:"12px 24px",backgroundColor:"#000000",color:"white",borderRadius:"8px",border:"none",cursor:"pointer",fontSize:"14px",fontWeight:"600",transition:"all 0.2s ease",fontFamily:"sans-serif",boxShadow:"0 2px 4px rgba(0, 0, 0, 0.1)"},onMouseOver:e=>{e.currentTarget.style.backgroundColor="#374151",e.currentTarget.style.transform="translateY(-1px)"},onMouseOut:e=>{e.currentTarget.style.backgroundColor="#000000",e.currentTarget.style.transform="translateY(0)"},children:["Next",n.jsx(h.Z,{size:16})]})]})]})]}),n.jsx(eT,{})]})}},7330:(e,t,o)=>{"use strict";o.d(t,{b:()=>r});var n=o(59028);let l=(0,o(89009).GU)();async function r(e,t,o,r){let i={user_id:o,user_message:t,team_id:r};try{console.log("Sending chat message:",i);let t=await fetch(`${l}/chat`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(i)});if(!t.body)throw Error("Readable stream not supported");let o=t.body.getReader(),r=new TextDecoder("utf-8"),a={sender:"bot",message:"",timestamp:new Date().toISOString()};for(e((0,n.wt)(!0));;){let{done:t,value:l}=await o.read();if(t)break;let i=r.decode(l,{stream:!0});e((0,n.wt)(!1)),console.log("Chunk:",i),a.message+=i,e((0,n.Hz)({sender:"bot",message:i,timestamp:new Date().toISOString()}))}}catch(o){console.error("Error sending chat message:",o);let t={sender:"bot",message:"Sorry, I encountered an error while processing your request.",timestamp:new Date().toISOString()};e((0,n.Hz)(t)),e((0,n.wt)(!1))}}},87616:(e,t,o)=>{"use strict";o.r(t),o.d(t,{default:()=>n});let n=(0,o(68570).createProxy)(String.raw`C:\beno\project_dev\qharmony-frontend-v1\src\app\ai-enroller\employee-enrol\page.tsx#default`)},70105:()=>{},9664:(e,t,o)=>{"use strict";o.d(t,{Z:()=>s});var n=o(17577);let l=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),r=(...e)=>e.filter((e,t,o)=>!!e&&""!==e.trim()&&o.indexOf(e)===t).join(" ").trim();var i={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let a=(0,n.forwardRef)(({color:e="currentColor",size:t=24,strokeWidth:o=2,absoluteStrokeWidth:l,className:a="",children:s,iconNode:d,...p},c)=>(0,n.createElement)("svg",{ref:c,...i,width:t,height:t,stroke:e,strokeWidth:l?24*Number(o)/Number(t):o,className:r("lucide",a),...p},[...d.map(([e,t])=>(0,n.createElement)(e,t)),...Array.isArray(s)?s:[s]])),s=(e,t)=>{let o=(0,n.forwardRef)(({className:o,...i},s)=>(0,n.createElement)(a,{ref:s,iconNode:t,className:r(`lucide-${l(e)}`,o),...i}));return o.displayName=`${e}`,o}},9921:(e,t,o)=>{"use strict";o.d(t,{Z:()=>n});let n=(0,o(9664).Z)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},96885:(e,t,o)=>{"use strict";o.d(t,{Z:()=>n});let n=(0,o(9664).Z)("ArrowRight",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])},18751:(e,t,o)=>{"use strict";o.d(t,{Z:()=>n});let n=(0,o(9664).Z)("ChartColumn",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]])},43020:(e,t,o)=>{"use strict";o.d(t,{Z:()=>n});let n=(0,o(9664).Z)("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])}};var t=require("../../../webpack-runtime.js");t.C(e);var o=e=>t(t.s=e),n=t.X(0,[8948,1183,6621,9066,1999,8492,3253,928,8097,6027,541,2142,5560,9434,310,576,6305,9902,2779,7982],()=>o(81140));module.exports=n})();