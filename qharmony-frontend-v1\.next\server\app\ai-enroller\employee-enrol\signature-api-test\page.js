(()=>{var e={};e.id=2887,e.ids=[2887],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},27790:e=>{"use strict";e.exports=require("assert")},84770:e=>{"use strict";e.exports=require("crypto")},17702:e=>{"use strict";e.exports=require("events")},92048:e=>{"use strict";e.exports=require("fs")},32615:e=>{"use strict";e.exports=require("http")},35240:e=>{"use strict";e.exports=require("https")},19801:e=>{"use strict";e.exports=require("os")},55315:e=>{"use strict";e.exports=require("path")},76162:e=>{"use strict";e.exports=require("stream")},74026:e=>{"use strict";e.exports=require("string_decoder")},74175:e=>{"use strict";e.exports=require("tty")},17360:e=>{"use strict";e.exports=require("url")},21764:e=>{"use strict";e.exports=require("util")},71568:e=>{"use strict";e.exports=require("zlib")},98061:e=>{"use strict";e.exports=require("node:assert")},92761:e=>{"use strict";e.exports=require("node:async_hooks")},72254:e=>{"use strict";e.exports=require("node:buffer")},40027:e=>{"use strict";e.exports=require("node:console")},6005:e=>{"use strict";e.exports=require("node:crypto")},65714:e=>{"use strict";e.exports=require("node:diagnostics_channel")},15673:e=>{"use strict";e.exports=require("node:events")},88849:e=>{"use strict";e.exports=require("node:http")},42725:e=>{"use strict";e.exports=require("node:http2")},87503:e=>{"use strict";e.exports=require("node:net")},38846:e=>{"use strict";e.exports=require("node:perf_hooks")},39630:e=>{"use strict";e.exports=require("node:querystring")},84492:e=>{"use strict";e.exports=require("node:stream")},31764:e=>{"use strict";e.exports=require("node:tls")},41041:e=>{"use strict";e.exports=require("node:url")},47261:e=>{"use strict";e.exports=require("node:util")},93746:e=>{"use strict";e.exports=require("node:util/types")},24086:e=>{"use strict";e.exports=require("node:worker_threads")},65628:e=>{"use strict";e.exports=require("node:zlib")},26166:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>p,originalPathname:()=>u,pages:()=>c,routeModule:()=>g,tree:()=>d}),r(6267),r(6079),r(33709),r(35866);var o=r(23191),a=r(88716),s=r(37922),n=r.n(s),i=r(95231),l={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);r.d(t,l);let d=["",{children:["ai-enroller",{children:["employee-enrol",{children:["signature-api-test",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,6267)),"C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\employee-enrol\\signature-api-test\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,6079)),"C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,33709)),"C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,35866,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],c=["C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\employee-enrol\\signature-api-test\\page.tsx"],u="/ai-enroller/employee-enrol/signature-api-test/page",p={require:r,loadChunk:()=>Promise.resolve()},g=new o.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/ai-enroller/employee-enrol/signature-api-test/page",pathname:"/ai-enroller/employee-enrol/signature-api-test",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},94834:(e,t,r)=>{Promise.resolve().then(r.bind(r,72750))},40853:(e,t,r)=>{Promise.resolve().then(r.bind(r,78021))},6283:(e,t,r)=>{"use strict";r.d(t,{Z:()=>d});var o=r(96830),a=r(5028),s=r(5283),n=r(14750);let i=(0,r(71685).Z)("MuiBox",["root"]),l=(0,s.Z)(),d=(0,o.default)({themeId:n.Z,defaultTheme:l,defaultClassName:i.root,generateClassName:a.Z.generate})},98139:(e,t,r)=>{"use strict";r.d(t,{Z:()=>A});var o=r(17577),a=r(41135),s=r(88634),n=r(8106),i=r(91703),l=r(13643),d=r(2791),c=r(54641),u=r(40955),p=r(71685),g=r(97898);function m(e){return(0,g.ZP)("MuiCircularProgress",e)}(0,p.Z)("MuiCircularProgress",["root","determinate","indeterminate","colorPrimary","colorSecondary","svg","circle","circleDeterminate","circleIndeterminate","circleDisableShrink"]);var y=r(10326);let h=(0,n.F4)`
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
`,x=(0,n.F4)`
  0% {
    stroke-dasharray: 1px, 200px;
    stroke-dashoffset: 0;
  }

  50% {
    stroke-dasharray: 100px, 200px;
    stroke-dashoffset: -15px;
  }

  100% {
    stroke-dasharray: 1px, 200px;
    stroke-dashoffset: -126px;
  }
`,f="string"!=typeof h?(0,n.iv)`
        animation: ${h} 1.4s linear infinite;
      `:null,b="string"!=typeof x?(0,n.iv)`
        animation: ${x} 1.4s ease-in-out infinite;
      `:null,v=e=>{let{classes:t,variant:r,color:o,disableShrink:a}=e,n={root:["root",r,`color${(0,c.Z)(o)}`],svg:["svg"],circle:["circle",`circle${(0,c.Z)(r)}`,a&&"circleDisableShrink"]};return(0,s.Z)(n,m,t)},S=(0,i.default)("span",{name:"MuiCircularProgress",slot:"Root",overridesResolver:(e,t)=>{let{ownerState:r}=e;return[t.root,t[r.variant],t[`color${(0,c.Z)(r.color)}`]]}})((0,l.Z)(({theme:e})=>({display:"inline-block",variants:[{props:{variant:"determinate"},style:{transition:e.transitions.create("transform")}},{props:{variant:"indeterminate"},style:f||{animation:`${h} 1.4s linear infinite`}},...Object.entries(e.palette).filter((0,u.Z)()).map(([t])=>({props:{color:t},style:{color:(e.vars||e).palette[t].main}}))]}))),D=(0,i.default)("svg",{name:"MuiCircularProgress",slot:"Svg",overridesResolver:(e,t)=>t.svg})({display:"block"}),w=(0,i.default)("circle",{name:"MuiCircularProgress",slot:"Circle",overridesResolver:(e,t)=>{let{ownerState:r}=e;return[t.circle,t[`circle${(0,c.Z)(r.variant)}`],r.disableShrink&&t.circleDisableShrink]}})((0,l.Z)(({theme:e})=>({stroke:"currentColor",variants:[{props:{variant:"determinate"},style:{transition:e.transitions.create("stroke-dashoffset")}},{props:{variant:"indeterminate"},style:{strokeDasharray:"80px, 200px",strokeDashoffset:0}},{props:({ownerState:e})=>"indeterminate"===e.variant&&!e.disableShrink,style:b||{animation:`${x} 1.4s ease-in-out infinite`}}]}))),A=o.forwardRef(function(e,t){let r=(0,d.i)({props:e,name:"MuiCircularProgress"}),{className:o,color:s="primary",disableShrink:n=!1,size:i=40,style:l,thickness:c=3.6,value:u=0,variant:p="indeterminate",...g}=r,m={...r,color:s,disableShrink:n,size:i,thickness:c,value:u,variant:p},h=v(m),x={},f={},b={};if("determinate"===p){let e=2*Math.PI*((44-c)/2);x.strokeDasharray=e.toFixed(3),b["aria-valuenow"]=Math.round(u),x.strokeDashoffset=`${((100-u)/100*e).toFixed(3)}px`,f.transform="rotate(-90deg)"}return(0,y.jsx)(S,{className:(0,a.Z)(h.root,o),style:{width:i,height:i,...f,...l},ownerState:m,ref:t,role:"progressbar",...b,...g,children:(0,y.jsx)(D,{className:h.svg,ownerState:m,viewBox:"22 22 44 44",children:(0,y.jsx)(w,{className:h.circle,style:x,ownerState:m,cx:44,cy:44,r:(44-c)/2,fill:"none",strokeWidth:c})})})})},2791:(e,t,r)=>{"use strict";r.d(t,{i:()=>a}),r(17577);var o=r(51387);function a(e){return(0,o.i)(e)}r(10326)},54641:(e,t,r)=>{"use strict";r.d(t,{Z:()=>o});let o=r(96005).Z},40955:(e,t,r)=>{"use strict";function o(e=[]){return([,t])=>t&&function(e,t=[]){if("string"!=typeof e.main)return!1;for(let r of t)if(!e.hasOwnProperty(r)||"string"!=typeof e[r])return!1;return!0}(t,e)}r.d(t,{Z:()=>o})},13643:(e,t,r)=>{"use strict";r.d(t,{Z:()=>s});var o=r(15966);let a={theme:void 0},s=function(e){let t,r;return function(s){let n=t;return(void 0===n||s.theme!==r)&&(a.theme=s.theme,t=n=(0,o.Z)(e(a)),r=s.theme),n}}},76924:(e,t,r)=>{"use strict";r.d(t,{C4:()=>i,YN:()=>l,we:()=>c});let o=()=>"http://localhost:8080",a=()=>localStorage.getItem("userid1")||localStorage.getItem("userId")||"",s=()=>({"Content-Type":"application/json","user-id":a()}),n=async e=>{try{let t=o(),r=a();if(!r)throw Error("User ID not found. Please log in again.");console.log("\uD83D\uDE80 Saving signature to database via API:",{endpoint:`${t}/admin/update/signature`,userId:r,signatureLength:e.length});let n=await fetch(`${t}/admin/update/signature`,{method:"POST",headers:s(),body:JSON.stringify({signatureData:e})});if(n.ok){let e=await n.json();return console.log("✅ Signature saved to database successfully:",e),{success:!0,message:e.message||"Signature saved successfully",signatureId:e.signatureId}}{let e=await n.text();return console.error("❌ Failed to save signature to database:",{status:n.status,statusText:n.statusText,error:e}),{success:!1,error:`Failed to save signature: ${e}`}}}catch(e){return console.error("❌ Signature API error:",e),{success:!1,error:e instanceof Error?e.message:"Network error occurred"}}},i=async()=>{console.log("ℹ️ No GET endpoint available - checking localStorage only");try{return!!localStorage.getItem("enrollmentSignature")}catch(e){return console.error("❌ Error checking signature existence:",e),!1}},l=e=>{if(!e||""===e.trim())return{isValid:!1,error:"Signature data is required"};try{atob(e)}catch(e){return{isValid:!1,error:"Invalid signature data format"}}return e.length>5242880?{isValid:!1,error:"Signature data too large"}:{isValid:!0}},d=async e=>{let t=l(e);return t.isValid?await n(e):{success:!1,error:t.error}},c=async(e,t=3)=>{let r="";for(let o=1;o<=t;o++){console.log(`🔄 Signature save attempt ${o}/${t}`);let a=await d(e);if(a.success)return console.log(`✅ Signature saved successfully on attempt ${o}`),a;if(console.warn(`⚠️ Attempt ${o} failed:`,r=a.error||"Unknown error"),o<t){let e=1e3*Math.pow(2,o);await new Promise(t=>setTimeout(t,e))}}return{success:!1,error:`Failed after ${t} attempts. Last error: ${r}`}}},72750:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});var o=r(10326),a=r(17577),s=r(76924);let n=()=>{let[e,t]=(0,a.useState)([]),[r,n]=(0,a.useState)(!1),i=e=>{t(t=>[...t,`${new Date().toLocaleTimeString()}: ${e}`])},l=()=>{t([])},d=async()=>{n(!0),i("\uD83E\uDDEA Testing signature save to database...");try{let e=btoa(JSON.stringify({signature:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==",timestamp:new Date().toISOString(),employeeName:"Test Employee",userAgent:navigator.userAgent,signatureHash:"test-hash-123"})),t=await (0,s.we)(e);t.success?(i("✅ Signature saved successfully to database"),i(`📋 Response: ${t.message}`),t.signatureId&&i(`🆔 Signature ID: ${t.signatureId}`)):i(`❌ Failed to save signature: ${t.error}`)}catch(e){i(`❌ Error during save test: ${e}`)}n(!1)},c=()=>{i("\uD83E\uDDEA Testing local signature storage...");try{let e=localStorage.getItem("enrollmentSignature");if(e){i("✅ Signature found in localStorage"),i(`📋 Signature data length: ${e.length} characters`);try{let t=JSON.parse(atob(e));i(`👤 Employee: ${t.employeeName||"Unknown"}`),i(`📅 Timestamp: ${t.timestamp||"Unknown"}`)}catch(e){i("⚠️ Could not decode signature metadata")}}else i("ℹ️ No signature found in localStorage")}catch(e){i(`❌ Error during localStorage test: ${e}`)}},u=async()=>{n(!0),i("\uD83E\uDDEA Testing signature existence check...");try{await (0,s.C4)()?i("✅ Signature exists in database"):i("ℹ️ No signature found in database")}catch(e){i(`❌ Error during existence check: ${e}`)}n(!1)},p=()=>{i("\uD83E\uDDEA Testing signature validation...");let e=btoa('{"test": "data"}'),t=(0,s.YN)(e);i(`✅ Valid signature test: ${t.isValid?"PASSED":"FAILED"}`);let r=(0,s.YN)("");i(`✅ Empty signature test: ${r.isValid?"FAILED":"PASSED"} - ${r.error}`);let o=(0,s.YN)("invalid-base64!@#");i(`✅ Invalid base64 test: ${o.isValid?"FAILED":"PASSED"} - ${o.error}`)},g=async()=>{l(),i("\uD83D\uDE80 Starting comprehensive API tests..."),p(),await u(),await d(),c(),i("\uD83C\uDFC1 All tests completed!")};return o.jsx("div",{style:{minHeight:"100vh",backgroundColor:"#f9fafb",padding:"40px 20px"},children:(0,o.jsxs)("div",{style:{maxWidth:"1000px",margin:"0 auto",backgroundColor:"white",borderRadius:"16px",padding:"40px",boxShadow:"0 10px 25px rgba(0, 0, 0, 0.1)"},children:[(0,o.jsxs)("div",{style:{textAlign:"center",marginBottom:"40px"},children:[o.jsx("h1",{style:{fontSize:"32px",fontWeight:"600",color:"#111827",margin:"0 0 16px 0"},children:"\uD83E\uDDEA Signature API Test Suite"}),(0,o.jsxs)("p",{style:{color:"#6b7280",fontSize:"16px",margin:0,lineHeight:"24px"},children:["Test the signature API integration with your backend endpoint:",o.jsx("br",{}),o.jsx("code",{style:{backgroundColor:"#f3f4f6",padding:"2px 6px",borderRadius:"4px"},children:"POST /admin/update/signature"})]})]}),(0,o.jsxs)("div",{style:{display:"grid",gridTemplateColumns:"repeat(auto-fit, minmax(200px, 1fr))",gap:"16px",marginBottom:"32px"},children:[o.jsx("button",{onClick:d,disabled:r,style:{padding:"16px 24px",backgroundColor:"#2563eb",border:"none",borderRadius:"8px",color:"white",cursor:r?"not-allowed":"pointer",fontWeight:"500",fontSize:"16px",opacity:r?.6:1},children:"\uD83D\uDCBE Test Save Signature"}),o.jsx("button",{onClick:c,style:{padding:"16px 24px",backgroundColor:"#059669",border:"none",borderRadius:"8px",color:"white",cursor:"pointer",fontWeight:"500",fontSize:"16px"},children:"\uD83D\uDCBE Test localStorage"}),o.jsx("button",{onClick:u,disabled:r,style:{padding:"16px 24px",backgroundColor:"#7c3aed",border:"none",borderRadius:"8px",color:"white",cursor:r?"not-allowed":"pointer",fontWeight:"500",fontSize:"16px",opacity:r?.6:1},children:"\uD83D\uDD0D Test Exists Check"}),o.jsx("button",{onClick:p,disabled:r,style:{padding:"16px 24px",backgroundColor:"#dc2626",border:"none",borderRadius:"8px",color:"white",cursor:r?"not-allowed":"pointer",fontWeight:"500",fontSize:"16px",opacity:r?.6:1},children:"✅ Test Validation"}),o.jsx("button",{onClick:g,disabled:r,style:{padding:"16px 24px",backgroundColor:"#059669",border:"none",borderRadius:"8px",color:"white",cursor:r?"not-allowed":"pointer",fontWeight:"500",fontSize:"16px",opacity:r?.6:1,gridColumn:"span 2"},children:"\uD83D\uDE80 Run All Tests"}),o.jsx("button",{onClick:l,style:{padding:"16px 24px",backgroundColor:"#6b7280",border:"none",borderRadius:"8px",color:"white",cursor:"pointer",fontWeight:"500",fontSize:"16px",gridColumn:"span 2"},children:"\uD83D\uDDD1️ Clear Results"})]}),(0,o.jsxs)("div",{style:{backgroundColor:"#f8fafc",border:"1px solid #e2e8f0",borderRadius:"8px",padding:"24px",minHeight:"300px"},children:[o.jsx("h3",{style:{fontSize:"18px",fontWeight:"600",color:"#111827",margin:"0 0 16px 0"},children:"Test Results:"}),0===e.length?o.jsx("p",{style:{color:"#6b7280",fontStyle:"italic"},children:"No tests run yet. Click a test button to start."}):o.jsx("div",{style:{backgroundColor:"#1f2937",color:"#f9fafb",padding:"16px",borderRadius:"6px",fontFamily:"monospace",fontSize:"14px",lineHeight:"20px",maxHeight:"400px",overflowY:"auto"},children:e.map((e,t)=>o.jsx("div",{style:{marginBottom:"4px"},children:e},t))})]}),(0,o.jsxs)("div",{style:{marginTop:"32px",padding:"16px",backgroundColor:"#fef3c7",border:"1px solid #f59e0b",borderRadius:"8px",fontSize:"14px",color:"#92400e"},children:[o.jsx("strong",{children:"\uD83D\uDCCB API Endpoint Details:"}),o.jsx("br",{}),o.jsx("code",{children:"POST /admin/update/signature"}),o.jsx("br",{}),o.jsx("strong",{children:"Body:"})," ",o.jsx("code",{children:'{ "signatureData": "base64string..." }'}),o.jsx("br",{}),o.jsx("strong",{children:"Headers:"})," ",o.jsx("code",{children:"Content-Type: application/json, user-id: [userId]"})]}),o.jsx("div",{style:{textAlign:"center",marginTop:"32px"},children:o.jsx("a",{href:"/ai-enroller/employee-enrol",style:{display:"inline-block",padding:"12px 24px",backgroundColor:"white",border:"2px solid #e5e7eb",borderRadius:"8px",color:"#374151",textDecoration:"none",fontWeight:"500",fontSize:"14px"},children:"← Back to Enrollment"})})]})})}},78021:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});var o=r(10326);r(17577),r(23824),r(54658);var a=r(43058);function s({children:e}){return o.jsx(a.Z,{children:o.jsx("div",{className:"min-h-screen bg-white",style:{backgroundColor:"white",minHeight:"100vh"},children:e})})}},43058:(e,t,r)=>{"use strict";r.d(t,{Z:()=>u});var o=r(10326),a=r(17577),s=r(22758),n=r(35047),i=r(31870);r(32049),r(94638);var l=r(98139),d=r(6283);let c=()=>/Mobi|Android/i.test(navigator.userAgent),u=({children:e})=>{let{user:t,loading:r}=(0,s.a)(),u=(0,n.useRouter)(),p=(0,n.usePathname)(),g=(0,i.T)(),[m,y]=(0,a.useState)(!1),h=(0,i.C)(e=>e.user.userProfile);return((0,a.useEffect)(()=>{},[g,h.name]),(0,a.useEffect)(()=>{console.log("ProtectedRoute useEffect triggered"),console.log("Current user: ",t),console.log("Loading state: ",r),console.log("Current user details: ",h),r||t||(console.log("User not authenticated, redirecting to home"),y(!1),u.push("/")),!r&&h.companyId&&""===h.companyId&&(console.log("Waiting to retrieve company details"),y(!1)),!r&&h.companyId&&""!==h.companyId&&(console.log("User found, rendering children"),y(!0)),c()&&!p.startsWith("/mobile")&&(console.log(`Redirecting to mobile version of ${p}`),u.push(`/mobile${p}`))},[t,r,h,u,p]),m)?t?o.jsx(o.Fragment,{children:e}):null:o.jsx(d.Z,{sx:{display:"flex",justifyContent:"center",alignItems:"center",height:"100vh",bgcolor:"#f6f8fc"},children:o.jsx(l.Z,{})})}},94638:(e,t,r)=>{"use strict";r.d(t,{G9:()=>x,JZ:()=>f,M_:()=>m,N:()=>d,Nq:()=>g,TQ:()=>u,Ur:()=>i,aE:()=>c,aK:()=>S,dA:()=>l,gt:()=>b,mb:()=>h,qB:()=>v,yu:()=>p,zX:()=>y});var o=r(53148),a=r(39352),s=r(25748),n=r(32049);function i(e){switch(e){case"Our Culture & Workplace":return"Work Policies";case"Protecting Your Income":return"Income Security";default:return e}}function l(e){switch(e){case"On-site Amenities":return"Company Handbook";case"Protecting Your Income":return"Income Security";case"Got married/divorced":return"Marriage or Divorce";case"Had a baby or adopted a baby":return"New Baby or Adoption";case"Lost your insurance":return"Loss of Insurance";default:return e}}async function d(e,t){let r=await (0,o.A_)("/benefits/benefit-types",{companyId:t});return console.log("COMPANY BENEFIT TYPES RESPONSE: ",r.benefitTypes),e((0,a.x7)(r.benefitTypes)),r.benefitTypes}async function c(e,t){let r=await (0,o.A_)("/benefits/all-benefits",{companyId:t});console.log("COMPANY BENEFIT TYPES WITH BENEFITS RESPONSE: ",r),e((0,s.US)(r.benefitsPerType))}async function u(e){let t=await (0,o.A_)("/admin/all-employees");return console.log("COMPANY TEAM MEMBERS RESPONSE: ",t),e((0,a.Vv)(t.employees)),t.employees}async function p(e,t){return console.log("ADDING USERS: ",t),await (0,o.j0)("/admin/add/employees",{employeeList:t})}async function g(e,t,r){try{console.log("\uD83D\uDD0D Debug: User being updated:",t);let e={employeeId:t,updatedDetails:{name:r.name,email:r.email,details:{phoneNumber:r.phoneNumber||"",department:r.department||"",title:r.title||"",role:r.title||""}}};return r.dateOfBirth&&(e.updatedDetails.details.dateOfBirth=r.dateOfBirth),r.hireDate&&(e.updatedDetails.details.hireDate=r.hireDate),r.annualSalary&&(e.updatedDetails.details.annualSalary=r.annualSalary),r.employeeClassType&&(e.updatedDetails.details.employeeClassType=r.employeeClassType),r.workSchedule&&(e.updatedDetails.details.workSchedule=r.workSchedule),r.ssn&&(e.updatedDetails.details.ssn=r.ssn),r.employeeId&&(e.updatedDetails.details.employeeId=r.employeeId),r.workLocation&&(e.updatedDetails.details.workLocation=r.workLocation),r.address&&(e.updatedDetails.details.address=r.address),r.mailingAddress&&(e.updatedDetails.details.mailingAddress=r.mailingAddress),r.emergencyContact&&(e.updatedDetails.details.emergencyContact=r.emergencyContact),e.updatedDetails.details.dependents=r.dependents||[],console.log("Middleware - dependents being sent:",e.updatedDetails.details.dependents),console.log("Sending PUT request with cleaned data:",e),await (0,o.GH)("/admin/update/employee",e)}catch(e){throw console.error("Error in updateUser middleware:",e),e}}async function m(e,t){let r=await (0,o.A_)("/employee",{"user-id":t});return e((0,n.$l)({name:r.currentUser.name,email:r.currentUser.email,companyId:r.currentUser.companyId,role:r.currentUser.role,isAdmin:r.currentUser.isAdmin,isBroker:r.currentUser.isBroker,details:r.currentUser.details})),r}async function y(e,t,r){let a=await (0,o.j0)("/admin/onboard",{company:{name:t.name,adminEmail:t.adminEmail,adminRole:t.adminRole,companySize:t.companySize,industry:t.industry,location:t.location,website:t.website,howHeard:t.howHeard,brokerId:t.brokerId,brokerageId:t.brokerageId,isBrokerage:t.isBrokerage,isActivated:t.isActivated,referralSource:t.referralSource,details:{logo:""}},user:{email:r.email,name:r.name,role:r.role,isAdmin:r.isAdmin,isBroker:r.isBroker,isActivated:r.isActivated}}),s=a.data.userId,n=a.data.companyId;return localStorage.setItem("userid1",s),localStorage.setItem("companyId1",n),a}async function h(e,t){return console.log("SENDING LOGIN LINK TO EMPLOYEE: ",e,t),await (0,o.j0)("/admin/send-user-login-link",{userId:e,companyId:t})}async function x(e,t,r,a){let s=await (0,o.j0)("/admin/add/employer",{brokerId:e,companyName:t,companyAdminEmail:r,companyAdminName:a});return console.log("BROKER ADDS COMPANY RESPONSE: ",s),s}async function f(e,t){return 200===(await (0,o.j0)("/employee/offboard/",{userId:e,companyId:t})).status}async function b(e,t){return await (0,o.j0)("/employee/enable/",{userId:e,companyId:t})}async function v(e,t){try{let t=await (0,o.A_)("/admin/all-companies");console.log("CLIENT COMPANIES UNDER BROKER: ",t);let r=t.companies||[];try{let e=await (0,o.A_)("/employee/company-details");console.log("BROKER'S OWN COMPANY: ",e),e.company&&e.company.isBrokerage&&!r.some(t=>t._id===e.company._id)&&(r.unshift(e.company),console.log("Added broker's own company to the list"))}catch(e){console.log("Could not fetch broker's own company (this is normal if user is not a broker):",e)}return console.log("ALL COMPANIES (CLIENT + OWN): ",r),e((0,n.Ym)(r)),{...t,companies:r}}catch(t){return console.error("Error fetching companies:",t),e((0,n.Ym)([])),{companies:[]}}}async function S(e){let t=await (0,o.A_)("/employee/company-details");return e((0,a.sy)(t.company)),t.status}},31870:(e,t,r)=>{"use strict";r.d(t,{C:()=>s,T:()=>a});var o=r(25842);let a=()=>(0,o.I0)(),s=o.v9},6267:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>o});let o=(0,r(68570).createProxy)(String.raw`C:\beno\project_dev\qharmony-frontend-v1\src\app\ai-enroller\employee-enrol\signature-api-test\page.tsx#default`)},6079:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>o});let o=(0,r(68570).createProxy)(String.raw`C:\beno\project_dev\qharmony-frontend-v1\src\app\ai-enroller\layout.tsx#default`)},73881:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});var o=r(66621);let a=e=>[{type:"image/x-icon",sizes:"16x16",url:(0,o.fillMetadataSegment)(".",e.params,"favicon.ico")+""}]},54658:()=>{},23824:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),o=t.X(0,[8948,1183,6621,576],()=>r(26166));module.exports=o})();