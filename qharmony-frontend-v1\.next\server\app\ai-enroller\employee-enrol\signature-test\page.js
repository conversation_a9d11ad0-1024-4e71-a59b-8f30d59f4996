(()=>{var e={};e.id=8792,e.ids=[8792],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},27790:e=>{"use strict";e.exports=require("assert")},84770:e=>{"use strict";e.exports=require("crypto")},17702:e=>{"use strict";e.exports=require("events")},92048:e=>{"use strict";e.exports=require("fs")},32615:e=>{"use strict";e.exports=require("http")},35240:e=>{"use strict";e.exports=require("https")},19801:e=>{"use strict";e.exports=require("os")},55315:e=>{"use strict";e.exports=require("path")},76162:e=>{"use strict";e.exports=require("stream")},74026:e=>{"use strict";e.exports=require("string_decoder")},74175:e=>{"use strict";e.exports=require("tty")},17360:e=>{"use strict";e.exports=require("url")},21764:e=>{"use strict";e.exports=require("util")},71568:e=>{"use strict";e.exports=require("zlib")},98061:e=>{"use strict";e.exports=require("node:assert")},92761:e=>{"use strict";e.exports=require("node:async_hooks")},72254:e=>{"use strict";e.exports=require("node:buffer")},40027:e=>{"use strict";e.exports=require("node:console")},6005:e=>{"use strict";e.exports=require("node:crypto")},65714:e=>{"use strict";e.exports=require("node:diagnostics_channel")},15673:e=>{"use strict";e.exports=require("node:events")},88849:e=>{"use strict";e.exports=require("node:http")},42725:e=>{"use strict";e.exports=require("node:http2")},87503:e=>{"use strict";e.exports=require("node:net")},38846:e=>{"use strict";e.exports=require("node:perf_hooks")},39630:e=>{"use strict";e.exports=require("node:querystring")},84492:e=>{"use strict";e.exports=require("node:stream")},31764:e=>{"use strict";e.exports=require("node:tls")},41041:e=>{"use strict";e.exports=require("node:url")},47261:e=>{"use strict";e.exports=require("node:util")},93746:e=>{"use strict";e.exports=require("node:util/types")},24086:e=>{"use strict";e.exports=require("node:worker_threads")},65628:e=>{"use strict";e.exports=require("node:zlib")},86670:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>s.a,__next_app__:()=>p,originalPathname:()=>u,pages:()=>c,routeModule:()=>g,tree:()=>d}),t(77183),t(6079),t(33709),t(35866);var o=t(23191),n=t(88716),i=t(37922),s=t.n(i),a=t(95231),l={};for(let e in a)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>a[e]);t.d(r,l);let d=["",{children:["ai-enroller",{children:["employee-enrol",{children:["signature-test",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,77183)),"C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\employee-enrol\\signature-test\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,6079)),"C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,33709)),"C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,35866,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],c=["C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\employee-enrol\\signature-test\\page.tsx"],u="/ai-enroller/employee-enrol/signature-test/page",p={require:t,loadChunk:()=>Promise.resolve()},g=new o.AppPageRouteModule({definition:{kind:n.x.APP_PAGE,page:"/ai-enroller/employee-enrol/signature-test/page",pathname:"/ai-enroller/employee-enrol/signature-test",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},84710:(e,r,t)=>{Promise.resolve().then(t.bind(t,46911))},40853:(e,r,t)=>{Promise.resolve().then(t.bind(t,78021))},6283:(e,r,t)=>{"use strict";t.d(r,{Z:()=>d});var o=t(96830),n=t(5028),i=t(5283),s=t(14750);let a=(0,t(71685).Z)("MuiBox",["root"]),l=(0,i.Z)(),d=(0,o.default)({themeId:s.Z,defaultTheme:l,defaultClassName:a.root,generateClassName:n.Z.generate})},98139:(e,r,t)=>{"use strict";t.d(r,{Z:()=>C});var o=t(17577),n=t(41135),i=t(88634),s=t(8106),a=t(91703),l=t(13643),d=t(2791),c=t(54641),u=t(40955),p=t(71685),g=t(97898);function x(e){return(0,g.ZP)("MuiCircularProgress",e)}(0,p.Z)("MuiCircularProgress",["root","determinate","indeterminate","colorPrimary","colorSecondary","svg","circle","circleDeterminate","circleIndeterminate","circleDisableShrink"]);var m=t(10326);let h=(0,s.F4)`
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
`,f=(0,s.F4)`
  0% {
    stroke-dasharray: 1px, 200px;
    stroke-dashoffset: 0;
  }

  50% {
    stroke-dasharray: 100px, 200px;
    stroke-dashoffset: -15px;
  }

  100% {
    stroke-dasharray: 1px, 200px;
    stroke-dashoffset: -126px;
  }
`,y="string"!=typeof h?(0,s.iv)`
        animation: ${h} 1.4s linear infinite;
      `:null,b="string"!=typeof f?(0,s.iv)`
        animation: ${f} 1.4s ease-in-out infinite;
      `:null,v=e=>{let{classes:r,variant:t,color:o,disableShrink:n}=e,s={root:["root",t,`color${(0,c.Z)(o)}`],svg:["svg"],circle:["circle",`circle${(0,c.Z)(t)}`,n&&"circleDisableShrink"]};return(0,i.Z)(s,x,r)},S=(0,a.default)("span",{name:"MuiCircularProgress",slot:"Root",overridesResolver:(e,r)=>{let{ownerState:t}=e;return[r.root,r[t.variant],r[`color${(0,c.Z)(t.color)}`]]}})((0,l.Z)(({theme:e})=>({display:"inline-block",variants:[{props:{variant:"determinate"},style:{transition:e.transitions.create("transform")}},{props:{variant:"indeterminate"},style:y||{animation:`${h} 1.4s linear infinite`}},...Object.entries(e.palette).filter((0,u.Z)()).map(([r])=>({props:{color:r},style:{color:(e.vars||e).palette[r].main}}))]}))),j=(0,a.default)("svg",{name:"MuiCircularProgress",slot:"Svg",overridesResolver:(e,r)=>r.svg})({display:"block"}),D=(0,a.default)("circle",{name:"MuiCircularProgress",slot:"Circle",overridesResolver:(e,r)=>{let{ownerState:t}=e;return[r.circle,r[`circle${(0,c.Z)(t.variant)}`],t.disableShrink&&r.circleDisableShrink]}})((0,l.Z)(({theme:e})=>({stroke:"currentColor",variants:[{props:{variant:"determinate"},style:{transition:e.transitions.create("stroke-dashoffset")}},{props:{variant:"indeterminate"},style:{strokeDasharray:"80px, 200px",strokeDashoffset:0}},{props:({ownerState:e})=>"indeterminate"===e.variant&&!e.disableShrink,style:b||{animation:`${f} 1.4s ease-in-out infinite`}}]}))),C=o.forwardRef(function(e,r){let t=(0,d.i)({props:e,name:"MuiCircularProgress"}),{className:o,color:i="primary",disableShrink:s=!1,size:a=40,style:l,thickness:c=3.6,value:u=0,variant:p="indeterminate",...g}=t,x={...t,color:i,disableShrink:s,size:a,thickness:c,value:u,variant:p},h=v(x),f={},y={},b={};if("determinate"===p){let e=2*Math.PI*((44-c)/2);f.strokeDasharray=e.toFixed(3),b["aria-valuenow"]=Math.round(u),f.strokeDashoffset=`${((100-u)/100*e).toFixed(3)}px`,y.transform="rotate(-90deg)"}return(0,m.jsx)(S,{className:(0,n.Z)(h.root,o),style:{width:a,height:a,...y,...l},ownerState:x,ref:r,role:"progressbar",...b,...g,children:(0,m.jsx)(j,{className:h.svg,ownerState:x,viewBox:"22 22 44 44",children:(0,m.jsx)(D,{className:h.circle,style:f,ownerState:x,cx:44,cy:44,r:(44-c)/2,fill:"none",strokeWidth:c})})})})},2791:(e,r,t)=>{"use strict";t.d(r,{i:()=>n}),t(17577);var o=t(51387);function n(e){return(0,o.i)(e)}t(10326)},54641:(e,r,t)=>{"use strict";t.d(r,{Z:()=>o});let o=t(96005).Z},40955:(e,r,t)=>{"use strict";function o(e=[]){return([,r])=>r&&function(e,r=[]){if("string"!=typeof e.main)return!1;for(let t of r)if(!e.hasOwnProperty(t)||"string"!=typeof e[t])return!1;return!0}(r,e)}t.d(r,{Z:()=>o})},13643:(e,r,t)=>{"use strict";t.d(r,{Z:()=>i});var o=t(15966);let n={theme:void 0},i=function(e){let r,t;return function(i){let s=r;return(void 0===s||i.theme!==t)&&(n.theme=i.theme,r=s=(0,o.Z)(e(n)),t=i.theme),s}}},46911:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>d});var o=t(10326),n=t(17577),i=t(24935),s=t(38492),a=t(97183);let l=({isOpen:e,onClose:r})=>{let[t,i]=(0,n.useState)(null),[l,d]=(0,n.useState)(null),[c,u]=(0,n.useState)(!1);(0,n.useEffect)(()=>{e&&p()},[e]);let p=()=>{u(!0);try{let e=(0,a.ni)(),r=(0,a.yd)();i(e),d(r),console.log("\uD83D\uDCDD Loaded signature data:",{signature:!!e,reference:r})}catch(e){console.error("Error loading signature data:",e)}finally{u(!1)}};return e?o.jsx("div",{style:{position:"fixed",top:0,left:0,right:0,bottom:0,backgroundColor:"rgba(0, 0, 0, 0.5)",zIndex:1e4,display:"flex",alignItems:"center",justifyContent:"center",padding:"20px"},children:(0,o.jsxs)("div",{style:{backgroundColor:"#ffffff",borderRadius:"16px",padding:"32px",maxWidth:"800px",width:"100%",maxHeight:"90vh",overflowY:"auto",boxShadow:"0 25px 50px rgba(0, 0, 0, 0.25)",position:"relative"},children:[o.jsx("button",{onClick:r,style:{position:"absolute",top:"16px",right:"16px",background:"none",border:"none",fontSize:"24px",cursor:"pointer",color:"#6b7280",padding:"4px"},children:o.jsx(s.fMW,{})}),(0,o.jsxs)("div",{style:{marginBottom:"24px"},children:[o.jsx("h2",{style:{fontSize:"24px",fontWeight:"600",color:"#111827",margin:"0 0 8px 0"},children:"\uD83D\uDCDD Enrollment Signature"}),o.jsx("p",{style:{color:"#6b7280",fontSize:"14px",margin:0,lineHeight:"21px"},children:"View and manage your stored enrollment signature"})]}),c?o.jsx("div",{style:{textAlign:"center",padding:"40px",color:"#6b7280"},children:"Loading signature data..."}):t?(0,o.jsxs)("div",{children:[(0,o.jsxs)("div",{style:{backgroundColor:"#f9fafb",border:"1px solid #e5e7eb",borderRadius:"8px",padding:"16px",marginBottom:"24px"},children:[o.jsx("h3",{style:{fontSize:"16px",fontWeight:"600",color:"#111827",margin:"0 0 12px 0"},children:"Signature Details"}),(0,o.jsxs)("div",{style:{display:"grid",gridTemplateColumns:"1fr 1fr",gap:"12px",fontSize:"14px"},children:[(0,o.jsxs)("div",{children:[o.jsx("strong",{children:"Employee:"})," ",t.employeeName]}),(0,o.jsxs)("div",{children:[o.jsx("strong",{children:"Signed:"})," ",(0,a.Fj)(t.timestamp)]}),(0,o.jsxs)("div",{children:[o.jsx("strong",{children:"Signature Hash:"})," ",t.signatureHash]}),(0,o.jsxs)("div",{children:[o.jsx("strong",{children:"Valid:"}),o.jsx("span",{style:{color:(0,a.uM)(t)?"#10b981":"#dc2626",fontWeight:"600",marginLeft:"4px"},children:(0,a.uM)(t)?"✅ Valid":"❌ Invalid"})]}),(0,o.jsxs)("div",{children:[o.jsx("strong",{children:"Format:"})," ",t.format||"PNG"]}),(0,o.jsxs)("div",{children:[o.jsx("strong",{children:"Quality:"})," ",t.quality||"Standard"]}),(0,o.jsxs)("div",{children:[o.jsx("strong",{children:"Vector Data:"})," ",t.signaturePadData?`${t.signaturePadData.length} strokes`:"Not available"]}),(0,o.jsxs)("div",{children:[o.jsx("strong",{children:"Data Size:"})," ",(t.signature.length/1024).toFixed(1)," KB"]})]})]}),(0,o.jsxs)("div",{style:{border:"2px solid #e5e7eb",borderRadius:"8px",padding:"16px",marginBottom:"24px",backgroundColor:"#ffffff",textAlign:"center"},children:[o.jsx("h3",{style:{fontSize:"16px",fontWeight:"600",color:"#111827",margin:"0 0 16px 0"},children:"Digital Signature"}),o.jsx("img",{src:t.signature,alt:"Digital Signature",style:{maxWidth:"100%",height:"auto",border:"1px solid #d1d5db",borderRadius:"4px"}})]}),(0,o.jsxs)("div",{style:{display:"flex",gap:"12px",justifyContent:"center",flexWrap:"wrap"},children:[(0,o.jsxs)("button",{onClick:()=>{if(t?.signature)try{let e=document.createElement("a");e.href=t.signature,e.download=`enrollment-signature-${t.employeeName}-${new Date(t.timestamp).toISOString().split("T")[0]}.png`,document.body.appendChild(e),e.click(),document.body.removeChild(e),console.log("\uD83D\uDCE5 Signature downloaded")}catch(e){console.error("Error downloading signature:",e)}},style:{padding:"12px 20px",backgroundColor:"#2563eb",border:"none",borderRadius:"8px",color:"white",cursor:"pointer",fontWeight:"500",fontSize:"14px",display:"flex",alignItems:"center",gap:"8px"},children:[o.jsx(s.yFZ,{}),"Download"]}),(0,o.jsxs)("button",{onClick:()=>{(0,a.Pe)()},style:{padding:"12px 20px",backgroundColor:"#6b7280",border:"none",borderRadius:"8px",color:"white",cursor:"pointer",fontWeight:"500",fontSize:"14px",display:"flex",alignItems:"center",gap:"8px"},children:[o.jsx(s.Vvo,{}),"Debug Info"]}),(0,o.jsxs)("button",{onClick:()=>{window.confirm("Are you sure you want to clear the stored signature? This action cannot be undone.")&&((0,a.td)(),i(null),d(null),console.log("\uD83D\uDDD1️ Signature cleared"))},style:{padding:"12px 20px",backgroundColor:"#dc2626",border:"none",borderRadius:"8px",color:"white",cursor:"pointer",fontWeight:"500",fontSize:"14px",display:"flex",alignItems:"center",gap:"8px"},children:[o.jsx(s.Bhs,{}),"Clear"]})]})]}):(0,o.jsxs)("div",{style:{textAlign:"center",padding:"40px",color:"#6b7280"},children:[o.jsx("div",{style:{fontSize:"48px",marginBottom:"16px"},children:"\uD83D\uDCDD"}),o.jsx("h3",{style:{fontSize:"18px",fontWeight:"600",color:"#111827",marginBottom:"8px"},children:"No Signature Found"}),o.jsx("p",{style:{margin:0,fontSize:"14px"},children:"Complete your enrollment to create a digital signature."})]}),o.jsx("div",{style:{marginTop:"24px",textAlign:"center"},children:o.jsx("button",{onClick:r,style:{padding:"12px 24px",backgroundColor:"white",border:"1px solid #d1d5db",borderRadius:"8px",color:"#374151",cursor:"pointer",fontWeight:"500",fontSize:"14px"},children:"Close"})})]})}):null},d=()=>{let[e,r]=(0,n.useState)(!1),[t,s]=(0,n.useState)(!1),[d,c]=(0,n.useState)("");return(0,o.jsxs)("div",{style:{minHeight:"100vh",backgroundColor:"#f9fafb",padding:"40px 20px"},children:[(0,o.jsxs)("div",{style:{maxWidth:"800px",margin:"0 auto",backgroundColor:"white",borderRadius:"16px",padding:"40px",boxShadow:"0 10px 25px rgba(0, 0, 0, 0.1)"},children:[(0,o.jsxs)("div",{style:{textAlign:"center",marginBottom:"40px"},children:[o.jsx("h1",{style:{fontSize:"32px",fontWeight:"600",color:"#111827",margin:"0 0 16px 0"},children:"\uD83D\uDCDD Signature Test Page"}),o.jsx("p",{style:{color:"#6b7280",fontSize:"16px",margin:0,lineHeight:"24px"},children:"Test the digital signature functionality for enrollment process"})]}),d&&(0,o.jsxs)("div",{style:{backgroundColor:"#f0fdf4",border:"1px solid #bbf7d0",borderRadius:"8px",padding:"16px",marginBottom:"32px",color:"#047857"},children:[o.jsx("strong",{children:"Status:"})," ",d]}),(0,o.jsxs)("div",{style:{display:"grid",gridTemplateColumns:"repeat(auto-fit, minmax(200px, 1fr))",gap:"16px",marginBottom:"32px"},children:[o.jsx("button",{onClick:()=>r(!0),style:{padding:"16px 24px",backgroundColor:"#2563eb",border:"none",borderRadius:"8px",color:"white",cursor:"pointer",fontWeight:"500",fontSize:"16px",textAlign:"center"},children:"\uD83D\uDD8A️ Create Signature"}),o.jsx("button",{onClick:()=>s(!0),style:{padding:"16px 24px",backgroundColor:"#059669",border:"none",borderRadius:"8px",color:"white",cursor:"pointer",fontWeight:"500",fontSize:"16px",textAlign:"center"},children:"\uD83D\uDC41️ View Signature"}),o.jsx("button",{onClick:()=>{let e=(0,a.yd)();e?c(`Signature found: ${e.employeeName} signed on ${new Date(e.timestamp).toLocaleString()}`):c("No signature found in storage")},style:{padding:"16px 24px",backgroundColor:"#7c3aed",border:"none",borderRadius:"8px",color:"white",cursor:"pointer",fontWeight:"500",fontSize:"16px",textAlign:"center"},children:"\uD83D\uDD0D Check Status"}),o.jsx("button",{onClick:()=>{(0,a.Pe)(),c("Debug info logged to console")},style:{padding:"16px 24px",backgroundColor:"#6b7280",border:"none",borderRadius:"8px",color:"white",cursor:"pointer",fontWeight:"500",fontSize:"16px",textAlign:"center"},children:"\uD83D\uDC1B Debug Info"})]}),(0,o.jsxs)("div",{style:{backgroundColor:"#f8fafc",border:"1px solid #e2e8f0",borderRadius:"8px",padding:"24px"},children:[o.jsx("h3",{style:{fontSize:"18px",fontWeight:"600",color:"#111827",margin:"0 0 16px 0"},children:"How it works:"}),(0,o.jsxs)("ul",{style:{color:"#4b5563",fontSize:"14px",lineHeight:"20px",margin:0,paddingLeft:"20px"},children:[(0,o.jsxs)("li",{children:[o.jsx("strong",{children:"Create Signature:"})," Opens the signature modal where you can draw your signature"]}),(0,o.jsxs)("li",{children:[o.jsx("strong",{children:"View Signature:"})," Shows the stored signature with metadata and verification"]}),(0,o.jsxs)("li",{children:[o.jsx("strong",{children:"Check Status:"})," Displays current signature status and basic info"]}),(0,o.jsxs)("li",{children:[o.jsx("strong",{children:"Debug Info:"})," Logs detailed signature information to browser console"]})]}),(0,o.jsxs)("div",{style:{marginTop:"16px",padding:"12px",backgroundColor:"#fef3c7",border:"1px solid #f59e0b",borderRadius:"6px",fontSize:"12px",color:"#92400e"},children:[o.jsx("strong",{children:"\uD83D\uDD12 Security Note:"})," Signatures are encrypted and stored locally in browser storage. In production, these would be sent to a secure backend server."]})]}),o.jsx("div",{style:{textAlign:"center",marginTop:"32px"},children:o.jsx("a",{href:"/ai-enroller/employee-enrol",style:{display:"inline-block",padding:"12px 24px",backgroundColor:"white",border:"2px solid #e5e7eb",borderRadius:"8px",color:"#374151",textDecoration:"none",fontWeight:"500",fontSize:"14px",transition:"all 0.2s"},onMouseOver:e=>{e.currentTarget.style.borderColor="#2563eb",e.currentTarget.style.color="#2563eb"},onMouseOut:e=>{e.currentTarget.style.borderColor="#e5e7eb",e.currentTarget.style.color="#374151"},children:"← Back to Enrollment"})})]}),o.jsx(i.Z,{isOpen:e,onClose:()=>r(!1),onSignatureComplete:e=>{console.log("✅ Signature completed!",e),c("Signature completed and stored successfully!"),r(!1)},employeeName:"Test Employee"}),o.jsx(l,{isOpen:t,onClose:()=>s(!1)})]})}},78021:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>i});var o=t(10326);t(17577),t(23824),t(54658);var n=t(43058);function i({children:e}){return o.jsx(n.Z,{children:o.jsx("div",{className:"min-h-screen bg-white",style:{backgroundColor:"white",minHeight:"100vh"},children:e})})}},43058:(e,r,t)=>{"use strict";t.d(r,{Z:()=>u});var o=t(10326),n=t(17577),i=t(22758),s=t(35047),a=t(31870);t(32049),t(94638);var l=t(98139),d=t(6283);let c=()=>/Mobi|Android/i.test(navigator.userAgent),u=({children:e})=>{let{user:r,loading:t}=(0,i.a)(),u=(0,s.useRouter)(),p=(0,s.usePathname)(),g=(0,a.T)(),[x,m]=(0,n.useState)(!1),h=(0,a.C)(e=>e.user.userProfile);return((0,n.useEffect)(()=>{},[g,h.name]),(0,n.useEffect)(()=>{console.log("ProtectedRoute useEffect triggered"),console.log("Current user: ",r),console.log("Loading state: ",t),console.log("Current user details: ",h),t||r||(console.log("User not authenticated, redirecting to home"),m(!1),u.push("/")),!t&&h.companyId&&""===h.companyId&&(console.log("Waiting to retrieve company details"),m(!1)),!t&&h.companyId&&""!==h.companyId&&(console.log("User found, rendering children"),m(!0)),c()&&!p.startsWith("/mobile")&&(console.log(`Redirecting to mobile version of ${p}`),u.push(`/mobile${p}`))},[r,t,h,u,p]),x)?r?o.jsx(o.Fragment,{children:e}):null:o.jsx(d.Z,{sx:{display:"flex",justifyContent:"center",alignItems:"center",height:"100vh",bgcolor:"#f6f8fc"},children:o.jsx(l.Z,{})})}},94638:(e,r,t)=>{"use strict";t.d(r,{G9:()=>f,JZ:()=>y,M_:()=>x,N:()=>d,Nq:()=>g,TQ:()=>u,Ur:()=>a,aE:()=>c,aK:()=>S,dA:()=>l,gt:()=>b,mb:()=>h,qB:()=>v,yu:()=>p,zX:()=>m});var o=t(53148),n=t(39352),i=t(25748),s=t(32049);function a(e){switch(e){case"Our Culture & Workplace":return"Work Policies";case"Protecting Your Income":return"Income Security";default:return e}}function l(e){switch(e){case"On-site Amenities":return"Company Handbook";case"Protecting Your Income":return"Income Security";case"Got married/divorced":return"Marriage or Divorce";case"Had a baby or adopted a baby":return"New Baby or Adoption";case"Lost your insurance":return"Loss of Insurance";default:return e}}async function d(e,r){let t=await (0,o.A_)("/benefits/benefit-types",{companyId:r});return console.log("COMPANY BENEFIT TYPES RESPONSE: ",t.benefitTypes),e((0,n.x7)(t.benefitTypes)),t.benefitTypes}async function c(e,r){let t=await (0,o.A_)("/benefits/all-benefits",{companyId:r});console.log("COMPANY BENEFIT TYPES WITH BENEFITS RESPONSE: ",t),e((0,i.US)(t.benefitsPerType))}async function u(e){let r=await (0,o.A_)("/admin/all-employees");return console.log("COMPANY TEAM MEMBERS RESPONSE: ",r),e((0,n.Vv)(r.employees)),r.employees}async function p(e,r){return console.log("ADDING USERS: ",r),await (0,o.j0)("/admin/add/employees",{employeeList:r})}async function g(e,r,t){try{console.log("\uD83D\uDD0D Debug: User being updated:",r);let e={employeeId:r,updatedDetails:{name:t.name,email:t.email,details:{phoneNumber:t.phoneNumber||"",department:t.department||"",title:t.title||"",role:t.title||""}}};return t.dateOfBirth&&(e.updatedDetails.details.dateOfBirth=t.dateOfBirth),t.hireDate&&(e.updatedDetails.details.hireDate=t.hireDate),t.annualSalary&&(e.updatedDetails.details.annualSalary=t.annualSalary),t.employeeClassType&&(e.updatedDetails.details.employeeClassType=t.employeeClassType),t.workSchedule&&(e.updatedDetails.details.workSchedule=t.workSchedule),t.ssn&&(e.updatedDetails.details.ssn=t.ssn),t.employeeId&&(e.updatedDetails.details.employeeId=t.employeeId),t.workLocation&&(e.updatedDetails.details.workLocation=t.workLocation),t.address&&(e.updatedDetails.details.address=t.address),t.mailingAddress&&(e.updatedDetails.details.mailingAddress=t.mailingAddress),t.emergencyContact&&(e.updatedDetails.details.emergencyContact=t.emergencyContact),e.updatedDetails.details.dependents=t.dependents||[],console.log("Middleware - dependents being sent:",e.updatedDetails.details.dependents),console.log("Sending PUT request with cleaned data:",e),await (0,o.GH)("/admin/update/employee",e)}catch(e){throw console.error("Error in updateUser middleware:",e),e}}async function x(e,r){let t=await (0,o.A_)("/employee",{"user-id":r});return e((0,s.$l)({name:t.currentUser.name,email:t.currentUser.email,companyId:t.currentUser.companyId,role:t.currentUser.role,isAdmin:t.currentUser.isAdmin,isBroker:t.currentUser.isBroker,details:t.currentUser.details})),t}async function m(e,r,t){let n=await (0,o.j0)("/admin/onboard",{company:{name:r.name,adminEmail:r.adminEmail,adminRole:r.adminRole,companySize:r.companySize,industry:r.industry,location:r.location,website:r.website,howHeard:r.howHeard,brokerId:r.brokerId,brokerageId:r.brokerageId,isBrokerage:r.isBrokerage,isActivated:r.isActivated,referralSource:r.referralSource,details:{logo:""}},user:{email:t.email,name:t.name,role:t.role,isAdmin:t.isAdmin,isBroker:t.isBroker,isActivated:t.isActivated}}),i=n.data.userId,s=n.data.companyId;return localStorage.setItem("userid1",i),localStorage.setItem("companyId1",s),n}async function h(e,r){return console.log("SENDING LOGIN LINK TO EMPLOYEE: ",e,r),await (0,o.j0)("/admin/send-user-login-link",{userId:e,companyId:r})}async function f(e,r,t,n){let i=await (0,o.j0)("/admin/add/employer",{brokerId:e,companyName:r,companyAdminEmail:t,companyAdminName:n});return console.log("BROKER ADDS COMPANY RESPONSE: ",i),i}async function y(e,r){return 200===(await (0,o.j0)("/employee/offboard/",{userId:e,companyId:r})).status}async function b(e,r){return await (0,o.j0)("/employee/enable/",{userId:e,companyId:r})}async function v(e,r){try{let r=await (0,o.A_)("/admin/all-companies");console.log("CLIENT COMPANIES UNDER BROKER: ",r);let t=r.companies||[];try{let e=await (0,o.A_)("/employee/company-details");console.log("BROKER'S OWN COMPANY: ",e),e.company&&e.company.isBrokerage&&!t.some(r=>r._id===e.company._id)&&(t.unshift(e.company),console.log("Added broker's own company to the list"))}catch(e){console.log("Could not fetch broker's own company (this is normal if user is not a broker):",e)}return console.log("ALL COMPANIES (CLIENT + OWN): ",t),e((0,s.Ym)(t)),{...r,companies:t}}catch(r){return console.error("Error fetching companies:",r),e((0,s.Ym)([])),{companies:[]}}}async function S(e){let r=await (0,o.A_)("/employee/company-details");return e((0,n.sy)(r.company)),r.status}},31870:(e,r,t)=>{"use strict";t.d(r,{C:()=>i,T:()=>n});var o=t(25842);let n=()=>(0,o.I0)(),i=o.v9},77183:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>o});let o=(0,t(68570).createProxy)(String.raw`C:\beno\project_dev\qharmony-frontend-v1\src\app\ai-enroller\employee-enrol\signature-test\page.tsx#default`)},6079:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>o});let o=(0,t(68570).createProxy)(String.raw`C:\beno\project_dev\qharmony-frontend-v1\src\app\ai-enroller\layout.tsx#default`)},73881:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>n});var o=t(66621);let n=e=>[{type:"image/x-icon",sizes:"16x16",url:(0,o.fillMetadataSegment)(".",e.params,"favicon.ico")+""}]},54658:()=>{},23824:()=>{}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),o=r.X(0,[8948,1183,6621,8492,310,576,7982],()=>t(86670));module.exports=o})();