(()=>{var e={};e.id=3711,e.ids=[3711],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},27790:e=>{"use strict";e.exports=require("assert")},84770:e=>{"use strict";e.exports=require("crypto")},17702:e=>{"use strict";e.exports=require("events")},92048:e=>{"use strict";e.exports=require("fs")},32615:e=>{"use strict";e.exports=require("http")},35240:e=>{"use strict";e.exports=require("https")},19801:e=>{"use strict";e.exports=require("os")},55315:e=>{"use strict";e.exports=require("path")},76162:e=>{"use strict";e.exports=require("stream")},74026:e=>{"use strict";e.exports=require("string_decoder")},74175:e=>{"use strict";e.exports=require("tty")},17360:e=>{"use strict";e.exports=require("url")},21764:e=>{"use strict";e.exports=require("util")},71568:e=>{"use strict";e.exports=require("zlib")},98061:e=>{"use strict";e.exports=require("node:assert")},92761:e=>{"use strict";e.exports=require("node:async_hooks")},72254:e=>{"use strict";e.exports=require("node:buffer")},40027:e=>{"use strict";e.exports=require("node:console")},6005:e=>{"use strict";e.exports=require("node:crypto")},65714:e=>{"use strict";e.exports=require("node:diagnostics_channel")},15673:e=>{"use strict";e.exports=require("node:events")},88849:e=>{"use strict";e.exports=require("node:http")},42725:e=>{"use strict";e.exports=require("node:http2")},87503:e=>{"use strict";e.exports=require("node:net")},38846:e=>{"use strict";e.exports=require("node:perf_hooks")},39630:e=>{"use strict";e.exports=require("node:querystring")},84492:e=>{"use strict";e.exports=require("node:stream")},31764:e=>{"use strict";e.exports=require("node:tls")},41041:e=>{"use strict";e.exports=require("node:url")},47261:e=>{"use strict";e.exports=require("node:util")},93746:e=>{"use strict";e.exports=require("node:util/types")},24086:e=>{"use strict";e.exports=require("node:worker_threads")},65628:e=>{"use strict";e.exports=require("node:zlib")},91566:(e,r,a)=>{"use strict";a.r(r),a.d(r,{GlobalError:()=>n.a,__next_app__:()=>u,originalPathname:()=>p,pages:()=>d,routeModule:()=>m,tree:()=>c}),a(47495),a(6079),a(33709),a(35866);var s=a(23191),t=a(88716),i=a(37922),n=a.n(i),o=a(95231),l={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);a.d(r,l);let c=["",{children:["ai-enroller",{children:["manage-groups",{children:["[companyId]",{children:["assign-plans",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,47495)),"C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\manage-groups\\[companyId]\\assign-plans\\page.tsx"]}]},{}]},{}]},{}]},{layout:[()=>Promise.resolve().then(a.bind(a,6079)),"C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(a.bind(a,33709)),"C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,35866,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],d=["C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\manage-groups\\[companyId]\\assign-plans\\page.tsx"],p="/ai-enroller/manage-groups/[companyId]/assign-plans/page",u={require:a,loadChunk:()=>Promise.resolve()},m=new s.AppPageRouteModule({definition:{kind:t.x.APP_PAGE,page:"/ai-enroller/manage-groups/[companyId]/assign-plans/page",pathname:"/ai-enroller/manage-groups/[companyId]/assign-plans",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},40853:(e,r,a)=>{Promise.resolve().then(a.bind(a,78021))},42817:(e,r,a)=>{Promise.resolve().then(a.bind(a,49378))},6283:(e,r,a)=>{"use strict";a.d(r,{Z:()=>c});var s=a(96830),t=a(5028),i=a(5283),n=a(14750);let o=(0,a(71685).Z)("MuiBox",["root"]),l=(0,i.Z)(),c=(0,s.default)({themeId:n.Z,defaultTheme:l,defaultClassName:o.root,generateClassName:t.Z.generate})},98139:(e,r,a)=>{"use strict";a.d(r,{Z:()=>w});var s=a(17577),t=a(41135),i=a(88634),n=a(8106),o=a(91703),l=a(13643),c=a(2791),d=a(54641),p=a(40955),u=a(71685),m=a(97898);function h(e){return(0,m.ZP)("MuiCircularProgress",e)}(0,u.Z)("MuiCircularProgress",["root","determinate","indeterminate","colorPrimary","colorSecondary","svg","circle","circleDeterminate","circleIndeterminate","circleDisableShrink"]);var g=a(10326);let y=(0,n.F4)`
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
`,v=(0,n.F4)`
  0% {
    stroke-dasharray: 1px, 200px;
    stroke-dashoffset: 0;
  }

  50% {
    stroke-dasharray: 100px, 200px;
    stroke-dashoffset: -15px;
  }

  100% {
    stroke-dasharray: 1px, 200px;
    stroke-dashoffset: -126px;
  }
`,f="string"!=typeof y?(0,n.iv)`
        animation: ${y} 1.4s linear infinite;
      `:null,x="string"!=typeof v?(0,n.iv)`
        animation: ${v} 1.4s ease-in-out infinite;
      `:null,N=e=>{let{classes:r,variant:a,color:s,disableShrink:t}=e,n={root:["root",a,`color${(0,d.Z)(s)}`],svg:["svg"],circle:["circle",`circle${(0,d.Z)(a)}`,t&&"circleDisableShrink"]};return(0,i.Z)(n,h,r)},b=(0,o.default)("span",{name:"MuiCircularProgress",slot:"Root",overridesResolver:(e,r)=>{let{ownerState:a}=e;return[r.root,r[a.variant],r[`color${(0,d.Z)(a.color)}`]]}})((0,l.Z)(({theme:e})=>({display:"inline-block",variants:[{props:{variant:"determinate"},style:{transition:e.transitions.create("transform")}},{props:{variant:"indeterminate"},style:f||{animation:`${y} 1.4s linear infinite`}},...Object.entries(e.palette).filter((0,p.Z)()).map(([r])=>({props:{color:r},style:{color:(e.vars||e).palette[r].main}}))]}))),j=(0,o.default)("svg",{name:"MuiCircularProgress",slot:"Svg",overridesResolver:(e,r)=>r.svg})({display:"block"}),P=(0,o.default)("circle",{name:"MuiCircularProgress",slot:"Circle",overridesResolver:(e,r)=>{let{ownerState:a}=e;return[r.circle,r[`circle${(0,d.Z)(a.variant)}`],a.disableShrink&&r.circleDisableShrink]}})((0,l.Z)(({theme:e})=>({stroke:"currentColor",variants:[{props:{variant:"determinate"},style:{transition:e.transitions.create("stroke-dashoffset")}},{props:{variant:"indeterminate"},style:{strokeDasharray:"80px, 200px",strokeDashoffset:0}},{props:({ownerState:e})=>"indeterminate"===e.variant&&!e.disableShrink,style:x||{animation:`${v} 1.4s ease-in-out infinite`}}]}))),w=s.forwardRef(function(e,r){let a=(0,c.i)({props:e,name:"MuiCircularProgress"}),{className:s,color:i="primary",disableShrink:n=!1,size:o=40,style:l,thickness:d=3.6,value:p=0,variant:u="indeterminate",...m}=a,h={...a,color:i,disableShrink:n,size:o,thickness:d,value:p,variant:u},y=N(h),v={},f={},x={};if("determinate"===u){let e=2*Math.PI*((44-d)/2);v.strokeDasharray=e.toFixed(3),x["aria-valuenow"]=Math.round(p),v.strokeDashoffset=`${((100-p)/100*e).toFixed(3)}px`,f.transform="rotate(-90deg)"}return(0,g.jsx)(b,{className:(0,t.Z)(y.root,s),style:{width:o,height:o,...f,...l},ownerState:h,ref:r,role:"progressbar",...x,...m,children:(0,g.jsx)(j,{className:y.svg,ownerState:h,viewBox:"22 22 44 44",children:(0,g.jsx)(P,{className:y.circle,style:v,ownerState:h,cx:44,cy:44,r:(44-d)/2,fill:"none",strokeWidth:d})})})})},2791:(e,r,a)=>{"use strict";a.d(r,{i:()=>t}),a(17577);var s=a(51387);function t(e){return(0,s.i)(e)}a(10326)},54641:(e,r,a)=>{"use strict";a.d(r,{Z:()=>s});let s=a(96005).Z},40955:(e,r,a)=>{"use strict";function s(e=[]){return([,r])=>r&&function(e,r=[]){if("string"!=typeof e.main)return!1;for(let a of r)if(!e.hasOwnProperty(a)||"string"!=typeof e[a])return!1;return!0}(r,e)}a.d(r,{Z:()=>s})},13643:(e,r,a)=>{"use strict";a.d(r,{Z:()=>i});var s=a(15966);let t={theme:void 0},i=function(e){let r,a;return function(i){let n=r;return(void 0===n||i.theme!==a)&&(t.theme=i.theme,r=n=(0,s.Z)(e(t)),a=i.theme),n}}},78021:(e,r,a)=>{"use strict";a.r(r),a.d(r,{default:()=>i});var s=a(10326);a(17577),a(23824),a(54658);var t=a(43058);function i({children:e}){return s.jsx(t.Z,{children:s.jsx("div",{className:"min-h-screen bg-white",style:{backgroundColor:"white",minHeight:"100vh"},children:e})})}},49378:(e,r,a)=>{"use strict";a.r(r),a.d(r,{default:()=>l});var s=a(10326),t=a(17577),i=a(35047),n=a(38492),o=a(25842);a(77695);let l=()=>{let e=(0,i.useRouter)(),r=(0,i.useParams)();(0,o.I0)();let a=r.companyId,[l,c]=(0,t.useState)([]),[d,p]=(0,t.useState)([]),[u,m]=(0,t.useState)(!0),[h,g]=(0,t.useState)(null),y=(0,o.v9)(e=>e.user.managedCompanies),v=y?.find(e=>e._id===a);(0,t.useEffect)(()=>{a&&f()},[a]);let f=async()=>{try{m(!0),g(null),await new Promise(e=>setTimeout(e,1e3)),p([{_id:"plan1",planName:"Blue Cross Blue Shield PPO",planCode:"BCBS-PPO-2024",coverageType:"Your Health",coverageSubTypes:["Medical"],planType:"PPO",metalTier:"Gold",carrierId:"carrier1",carrier:{carrierName:"Blue Cross Blue Shield"},description:"Comprehensive PPO plan with nationwide coverage",highlights:["Nationwide network","No referrals needed","Preventive care covered"]},{_id:"plan2",planName:"Aetna Better Health HMO",planCode:"AETNA-HMO-2024",coverageType:"Your Health",coverageSubTypes:["Medical"],planType:"HMO",metalTier:"Silver",carrierId:"carrier2",carrier:{carrierName:"Aetna"},description:"Cost-effective HMO plan with coordinated care",highlights:["Lower premiums","Coordinated care","Primary care focus"]},{_id:"plan3",planName:"Delta Dental PPO",planCode:"DELTA-PPO-2024",coverageType:"Dental",coverageSubTypes:["Dental"],planType:"PPO",carrierId:"carrier3",carrier:{carrierName:"Delta Dental"},description:"Comprehensive dental coverage with large network",highlights:["Large provider network","Preventive care covered","Orthodontics included"]},{_id:"plan4",planName:"Guardian Dental HMO",planCode:"GUARD-HMO-2024",coverageType:"Dental",coverageSubTypes:["Dental"],planType:"HMO",carrierId:"carrier4",carrier:{carrierName:"Guardian"},description:"Affordable dental HMO with quality care",highlights:["Lower cost option","Quality providers","Basic and major services"]},{_id:"plan5",planName:"VSP Vision Care",planCode:"VSP-2024",coverageType:"Vision",coverageSubTypes:["Vision"],planType:"Vision",carrierId:"carrier5",carrier:{carrierName:"VSP"},description:"Complete vision care with frame allowances",highlights:["Annual eye exams","Frame allowance","Contact lens coverage"]},{_id:"plan6",planName:"MetLife Life Insurance",planCode:"MET-LIFE-2024",coverageType:"Ancillary",coverageSubTypes:["Term Life"],planType:"Term Life",carrierId:"carrier6",carrier:{carrierName:"MetLife"},description:"Term life insurance for financial protection",highlights:["Competitive rates","Easy enrollment","Portable coverage"]}])}catch(e){console.error("Error fetching available plans:",e),g("Failed to load available plans")}finally{m(!1)}},x=()=>{e.push("/ai-enroller/manage-groups/select-company")},N=e=>{c(r=>r.includes(e)?r.filter(r=>r!==e):[...r,e])},b=e=>{switch(e.toLowerCase()){case"your health":case"medical":return s.jsx(n.wkn,{className:"plan-icon medical"});case"dental":return s.jsx(n.Q5u,{className:"plan-icon dental"});case"vision":return s.jsx(n.Vvo,{className:"plan-icon vision"});default:return s.jsx(n.Moc,{className:"plan-icon ancillary"})}},j={medical:[],dental:[],vision:[],ancillary:[]};return(d.forEach(e=>{let r=e.coverageType?.toLowerCase()||"";r.includes("health")||r.includes("medical")?j.medical.push(e):r.includes("dental")?j.dental.push(e):r.includes("vision")?j.vision.push(e):j.ancillary.push(e)}),u)?s.jsx("div",{className:"assign-plans-page",children:(0,s.jsxs)("div",{className:"loading-container",children:[s.jsx("div",{className:"loading-spinner"}),s.jsx("p",{children:"Loading available plans..."})]})}):h?s.jsx("div",{className:"assign-plans-page",children:(0,s.jsxs)("div",{className:"error-container",children:[s.jsx("p",{children:h}),(0,s.jsxs)("button",{onClick:x,className:"back-button",children:[s.jsx(n.Tsu,{size:20}),"Back to Company Selection"]})]})}):(0,s.jsxs)("div",{className:"assign-plans-page",children:[(0,s.jsxs)("div",{className:"progress-steps",children:[(0,s.jsxs)("div",{className:"step active",children:[s.jsx("div",{className:"step-number",children:"1"}),s.jsx("div",{className:"step-label",children:"Select Plans"})]}),s.jsx("div",{className:"step-connector"}),(0,s.jsxs)("div",{className:"step",children:[s.jsx("div",{className:"step-number",children:"2"}),s.jsx("div",{className:"step-label",children:"Configure Tiers"})]}),s.jsx("div",{className:"step-connector"}),(0,s.jsxs)("div",{className:"step",children:[s.jsx("div",{className:"step-number",children:"3"}),s.jsx("div",{className:"step-label",children:"Set Dates"})]}),s.jsx("div",{className:"step-connector"}),(0,s.jsxs)("div",{className:"step",children:[s.jsx("div",{className:"step-number",children:"4"}),s.jsx("div",{className:"step-label",children:"Review & Assign"})]})]}),(0,s.jsxs)("div",{className:"page-header",children:[(0,s.jsxs)("button",{onClick:x,className:"back-button",children:[s.jsx(n.Tsu,{size:20}),"Back"]}),(0,s.jsxs)("div",{className:"header-content",children:[s.jsx("h1",{children:"Select Plans to Assign"}),(0,s.jsxs)("p",{children:["Choose the benefit plans you want to assign to ",v?.companyName||"this company"]})]})]}),l.length>0&&s.jsx("div",{className:"selected-summary",children:(0,s.jsxs)("div",{className:"summary-content",children:[s.jsx(n.Sul,{size:20}),(0,s.jsxs)("span",{children:[l.length," plan",1!==l.length?"s":""," selected"]})]})}),Object.entries(j).map(([e,r])=>r.length>0&&(0,s.jsxs)("div",{className:"plan-category",children:[(0,s.jsxs)("div",{className:"category-header",children:[(0,s.jsxs)("h2",{children:[e.charAt(0).toUpperCase()+e.slice(1)," Plans"]}),(0,s.jsxs)("span",{className:"plan-count",children:[r.length," available"]})]}),s.jsx("div",{className:"plans-grid",children:r.map(e=>(0,s.jsxs)("div",{className:`plan-card ${l.includes(e._id)?"selected":""}`,onClick:()=>N(e._id),children:[(0,s.jsxs)("div",{className:"plan-header",children:[s.jsx("div",{className:"plan-icon-wrapper",children:b(e.coverageType)}),s.jsx("div",{className:"selection-indicator",children:l.includes(e._id)&&s.jsx(n.Sul,{size:16})})]}),(0,s.jsxs)("div",{className:"plan-content",children:[s.jsx("h3",{children:e.planName}),s.jsx("div",{className:"plan-carrier",children:e.carrier?.carrierName}),(0,s.jsxs)("div",{className:"plan-type",children:[e.planType," ",e.metalTier&&`• ${e.metalTier}`]}),e.description&&s.jsx("p",{className:"plan-description",children:e.description}),e.highlights&&e.highlights.length>0&&s.jsx("div",{className:"plan-highlights",children:e.highlights.slice(0,3).map((e,r)=>(0,s.jsxs)("div",{className:"highlight-item",children:[s.jsx(n.Sul,{size:12}),s.jsx("span",{children:e})]},r))})]})]},e._id))})]},e)),s.jsx("div",{className:"continue-section",children:(0,s.jsxs)("button",{className:`continue-btn ${0===l.length?"disabled":""}`,onClick:()=>{if(0===l.length){alert("Please select at least one plan to continue.");return}localStorage.setItem("selectedPlansForAssignment",JSON.stringify(l)),e.push(`/ai-enroller/manage-groups/${a}/assign-plans/configure`)},disabled:0===l.length,children:["Continue to Configure Tiers",s.jsx(n.mR2,{size:20})]})})]})}},43058:(e,r,a)=>{"use strict";a.d(r,{Z:()=>p});var s=a(10326),t=a(17577),i=a(22758),n=a(35047),o=a(31870);a(32049),a(94638);var l=a(98139),c=a(6283);let d=()=>/Mobi|Android/i.test(navigator.userAgent),p=({children:e})=>{let{user:r,loading:a}=(0,i.a)(),p=(0,n.useRouter)(),u=(0,n.usePathname)(),m=(0,o.T)(),[h,g]=(0,t.useState)(!1),y=(0,o.C)(e=>e.user.userProfile);return((0,t.useEffect)(()=>{},[m,y.name]),(0,t.useEffect)(()=>{console.log("ProtectedRoute useEffect triggered"),console.log("Current user: ",r),console.log("Loading state: ",a),console.log("Current user details: ",y),a||r||(console.log("User not authenticated, redirecting to home"),g(!1),p.push("/")),!a&&y.companyId&&""===y.companyId&&(console.log("Waiting to retrieve company details"),g(!1)),!a&&y.companyId&&""!==y.companyId&&(console.log("User found, rendering children"),g(!0)),d()&&!u.startsWith("/mobile")&&(console.log(`Redirecting to mobile version of ${u}`),p.push(`/mobile${u}`))},[r,a,y,p,u]),h)?r?s.jsx(s.Fragment,{children:e}):null:s.jsx(c.Z,{sx:{display:"flex",justifyContent:"center",alignItems:"center",height:"100vh",bgcolor:"#f6f8fc"},children:s.jsx(l.Z,{})})}},94638:(e,r,a)=>{"use strict";a.d(r,{G9:()=>v,JZ:()=>f,M_:()=>h,N:()=>c,Nq:()=>m,TQ:()=>p,Ur:()=>o,aE:()=>d,aK:()=>b,dA:()=>l,gt:()=>x,mb:()=>y,qB:()=>N,yu:()=>u,zX:()=>g});var s=a(53148),t=a(39352),i=a(25748),n=a(32049);function o(e){switch(e){case"Our Culture & Workplace":return"Work Policies";case"Protecting Your Income":return"Income Security";default:return e}}function l(e){switch(e){case"On-site Amenities":return"Company Handbook";case"Protecting Your Income":return"Income Security";case"Got married/divorced":return"Marriage or Divorce";case"Had a baby or adopted a baby":return"New Baby or Adoption";case"Lost your insurance":return"Loss of Insurance";default:return e}}async function c(e,r){let a=await (0,s.A_)("/benefits/benefit-types",{companyId:r});return console.log("COMPANY BENEFIT TYPES RESPONSE: ",a.benefitTypes),e((0,t.x7)(a.benefitTypes)),a.benefitTypes}async function d(e,r){let a=await (0,s.A_)("/benefits/all-benefits",{companyId:r});console.log("COMPANY BENEFIT TYPES WITH BENEFITS RESPONSE: ",a),e((0,i.US)(a.benefitsPerType))}async function p(e){let r=await (0,s.A_)("/admin/all-employees");return console.log("COMPANY TEAM MEMBERS RESPONSE: ",r),e((0,t.Vv)(r.employees)),r.employees}async function u(e,r){return console.log("ADDING USERS: ",r),await (0,s.j0)("/admin/add/employees",{employeeList:r})}async function m(e,r,a){try{console.log("\uD83D\uDD0D Debug: User being updated:",r);let e={employeeId:r,updatedDetails:{name:a.name,email:a.email,details:{phoneNumber:a.phoneNumber||"",department:a.department||"",title:a.title||"",role:a.title||""}}};return a.dateOfBirth&&(e.updatedDetails.details.dateOfBirth=a.dateOfBirth),a.hireDate&&(e.updatedDetails.details.hireDate=a.hireDate),a.annualSalary&&(e.updatedDetails.details.annualSalary=a.annualSalary),a.employeeClassType&&(e.updatedDetails.details.employeeClassType=a.employeeClassType),a.workSchedule&&(e.updatedDetails.details.workSchedule=a.workSchedule),a.ssn&&(e.updatedDetails.details.ssn=a.ssn),a.employeeId&&(e.updatedDetails.details.employeeId=a.employeeId),a.workLocation&&(e.updatedDetails.details.workLocation=a.workLocation),a.address&&(e.updatedDetails.details.address=a.address),a.mailingAddress&&(e.updatedDetails.details.mailingAddress=a.mailingAddress),a.emergencyContact&&(e.updatedDetails.details.emergencyContact=a.emergencyContact),e.updatedDetails.details.dependents=a.dependents||[],console.log("Middleware - dependents being sent:",e.updatedDetails.details.dependents),console.log("Sending PUT request with cleaned data:",e),await (0,s.GH)("/admin/update/employee",e)}catch(e){throw console.error("Error in updateUser middleware:",e),e}}async function h(e,r){let a=await (0,s.A_)("/employee",{"user-id":r});return e((0,n.$l)({name:a.currentUser.name,email:a.currentUser.email,companyId:a.currentUser.companyId,role:a.currentUser.role,isAdmin:a.currentUser.isAdmin,isBroker:a.currentUser.isBroker,details:a.currentUser.details})),a}async function g(e,r,a){let t=await (0,s.j0)("/admin/onboard",{company:{name:r.name,adminEmail:r.adminEmail,adminRole:r.adminRole,companySize:r.companySize,industry:r.industry,location:r.location,website:r.website,howHeard:r.howHeard,brokerId:r.brokerId,brokerageId:r.brokerageId,isBrokerage:r.isBrokerage,isActivated:r.isActivated,referralSource:r.referralSource,details:{logo:""}},user:{email:a.email,name:a.name,role:a.role,isAdmin:a.isAdmin,isBroker:a.isBroker,isActivated:a.isActivated}}),i=t.data.userId,n=t.data.companyId;return localStorage.setItem("userid1",i),localStorage.setItem("companyId1",n),t}async function y(e,r){return console.log("SENDING LOGIN LINK TO EMPLOYEE: ",e,r),await (0,s.j0)("/admin/send-user-login-link",{userId:e,companyId:r})}async function v(e,r,a,t){let i=await (0,s.j0)("/admin/add/employer",{brokerId:e,companyName:r,companyAdminEmail:a,companyAdminName:t});return console.log("BROKER ADDS COMPANY RESPONSE: ",i),i}async function f(e,r){return 200===(await (0,s.j0)("/employee/offboard/",{userId:e,companyId:r})).status}async function x(e,r){return await (0,s.j0)("/employee/enable/",{userId:e,companyId:r})}async function N(e,r){try{let r=await (0,s.A_)("/admin/all-companies");console.log("CLIENT COMPANIES UNDER BROKER: ",r);let a=r.companies||[];try{let e=await (0,s.A_)("/employee/company-details");console.log("BROKER'S OWN COMPANY: ",e),e.company&&e.company.isBrokerage&&!a.some(r=>r._id===e.company._id)&&(a.unshift(e.company),console.log("Added broker's own company to the list"))}catch(e){console.log("Could not fetch broker's own company (this is normal if user is not a broker):",e)}return console.log("ALL COMPANIES (CLIENT + OWN): ",a),e((0,n.Ym)(a)),{...r,companies:a}}catch(r){return console.error("Error fetching companies:",r),e((0,n.Ym)([])),{companies:[]}}}async function b(e){let r=await (0,s.A_)("/employee/company-details");return e((0,t.sy)(r.company)),r.status}},31870:(e,r,a)=>{"use strict";a.d(r,{C:()=>i,T:()=>t});var s=a(25842);let t=()=>(0,s.I0)(),i=s.v9},6079:(e,r,a)=>{"use strict";a.r(r),a.d(r,{default:()=>s});let s=(0,a(68570).createProxy)(String.raw`C:\beno\project_dev\qharmony-frontend-v1\src\app\ai-enroller\layout.tsx#default`)},47495:(e,r,a)=>{"use strict";a.r(r),a.d(r,{default:()=>s});let s=(0,a(68570).createProxy)(String.raw`C:\beno\project_dev\qharmony-frontend-v1\src\app\ai-enroller\manage-groups\[companyId]\assign-plans\page.tsx#default`)},73881:(e,r,a)=>{"use strict";a.r(r),a.d(r,{default:()=>t});var s=a(66621);let t=e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",e.params,"favicon.ico")+""}]},54658:()=>{},77695:()=>{},23824:()=>{}};var r=require("../../../../../webpack-runtime.js");r.C(e);var a=e=>r(r.s=e),s=r.X(0,[8948,1183,6621,8492,576],()=>a(91566));module.exports=s})();