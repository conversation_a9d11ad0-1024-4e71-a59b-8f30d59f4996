(()=>{var e={};e.id=4905,e.ids=[4905],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},27790:e=>{"use strict";e.exports=require("assert")},84770:e=>{"use strict";e.exports=require("crypto")},17702:e=>{"use strict";e.exports=require("events")},92048:e=>{"use strict";e.exports=require("fs")},32615:e=>{"use strict";e.exports=require("http")},35240:e=>{"use strict";e.exports=require("https")},19801:e=>{"use strict";e.exports=require("os")},55315:e=>{"use strict";e.exports=require("path")},76162:e=>{"use strict";e.exports=require("stream")},74026:e=>{"use strict";e.exports=require("string_decoder")},74175:e=>{"use strict";e.exports=require("tty")},17360:e=>{"use strict";e.exports=require("url")},21764:e=>{"use strict";e.exports=require("util")},71568:e=>{"use strict";e.exports=require("zlib")},98061:e=>{"use strict";e.exports=require("node:assert")},92761:e=>{"use strict";e.exports=require("node:async_hooks")},72254:e=>{"use strict";e.exports=require("node:buffer")},40027:e=>{"use strict";e.exports=require("node:console")},6005:e=>{"use strict";e.exports=require("node:crypto")},65714:e=>{"use strict";e.exports=require("node:diagnostics_channel")},15673:e=>{"use strict";e.exports=require("node:events")},88849:e=>{"use strict";e.exports=require("node:http")},42725:e=>{"use strict";e.exports=require("node:http2")},87503:e=>{"use strict";e.exports=require("node:net")},38846:e=>{"use strict";e.exports=require("node:perf_hooks")},39630:e=>{"use strict";e.exports=require("node:querystring")},84492:e=>{"use strict";e.exports=require("node:stream")},31764:e=>{"use strict";e.exports=require("node:tls")},41041:e=>{"use strict";e.exports=require("node:url")},47261:e=>{"use strict";e.exports=require("node:util")},93746:e=>{"use strict";e.exports=require("node:util/types")},24086:e=>{"use strict";e.exports=require("node:worker_threads")},65628:e=>{"use strict";e.exports=require("node:zlib")},54707:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>i.a,__next_app__:()=>u,originalPathname:()=>p,pages:()=>d,routeModule:()=>h,tree:()=>c}),r(27986),r(6079),r(33709),r(35866);var a=r(23191),t=r(88716),n=r(37922),i=r.n(n),l=r(95231),o={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);r.d(s,o);let c=["",{children:["ai-enroller",{children:["manage-groups",{children:["[companyId]",{children:["manage-plans",{children:["add-plan",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,27986)),"C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\manage-groups\\[companyId]\\manage-plans\\add-plan\\page.tsx"]}]},{}]},{}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,6079)),"C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,33709)),"C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,35866,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],d=["C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\manage-groups\\[companyId]\\manage-plans\\add-plan\\page.tsx"],p="/ai-enroller/manage-groups/[companyId]/manage-plans/add-plan/page",u={require:r,loadChunk:()=>Promise.resolve()},h=new a.AppPageRouteModule({definition:{kind:t.x.APP_PAGE,page:"/ai-enroller/manage-groups/[companyId]/manage-plans/add-plan/page",pathname:"/ai-enroller/manage-groups/[companyId]/manage-plans/add-plan",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},40853:(e,s,r)=>{Promise.resolve().then(r.bind(r,78021))},65428:(e,s,r)=>{Promise.resolve().then(r.bind(r,21776))},78021:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>n});var a=r(10326);r(17577),r(23824),r(54658);var t=r(43058);function n({children:e}){return a.jsx(t.Z,{children:a.jsx("div",{className:"min-h-screen bg-white",style:{backgroundColor:"white",minHeight:"100vh"},children:e})})}},21776:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>o});var a=r(10326),t=r(17577),n=r(35047),i=r(38492),l=r(25842);r(61813);let o=()=>{let e=(0,n.useRouter)(),s=(0,n.useParams)(),r=(0,n.useSearchParams)(),o=s.companyId,c=r.get("category")||"all",[d,p]=(0,t.useState)([]),[u,h]=(0,t.useState)(!0),[m,x]=(0,t.useState)(null),g=(0,l.v9)(e=>e.user.managedCompanies),v=g?.find(e=>e._id===o),j=async()=>{try{h(!0),x(null);let e=await fetch("http://localhost:8080/api/plans/broker/6838677aef6db0212bcfdacd",{method:"GET",headers:{"Content-Type":"application/json","user-id":localStorage.getItem("userid1")||localStorage.getItem("userId")||"6838677aef6db0212bcfdacd"}});if(e.ok){let s=await e.json();console.log("Available plans result:",s);let r=s.plans||[];"all"!==c&&(r=r.filter(e=>{let s=e.coverageType?.toLowerCase()||"";switch(c.toLowerCase()){case"medical":return s.includes("health")||s.includes("medical");case"dental":return s.includes("dental");case"vision":return s.includes("vision");case"ancillary":return!s.includes("health")&&!s.includes("medical")&&!s.includes("dental")&&!s.includes("vision");default:return!0}})),p(r)}else throw Error("Failed to fetch plans")}catch(e){console.error("Error fetching available plans:",e),x("Failed to load available plans")}finally{h(!1)}};(0,t.useEffect)(()=>{j()},[c]);let y=()=>{e.push(`/ai-enroller/manage-groups/${o}/manage-plans`)},q=s=>{e.push(`/ai-enroller/manage-groups/${o}/manage-plans/configure-plan?planId=${s._id}`)},b=e=>{switch(e.toLowerCase()){case"your health":case"medical":return a.jsx(i.wkn,{className:"plan-icon medical"});case"dental":return a.jsx(i.Q5u,{className:"plan-icon dental"});case"vision":return a.jsx(i.Vvo,{className:"plan-icon vision"});default:return a.jsx(i.Moc,{className:"plan-icon ancillary"})}};return u?a.jsx("div",{className:"manage-plans-page",children:(0,a.jsxs)("div",{className:"loading-container",children:[a.jsx("div",{className:"loading-spinner"}),a.jsx("p",{children:"Loading available plans..."})]})}):m?a.jsx("div",{className:"manage-plans-page",children:(0,a.jsxs)("div",{className:"error-container",children:[a.jsx("p",{children:m}),(0,a.jsxs)("button",{onClick:y,className:"back-button",children:[a.jsx(i.Tsu,{size:20}),"Back to Plans"]})]})}):(0,a.jsxs)("div",{className:"manage-plans-page",children:[(0,a.jsxs)("div",{className:"page-header",children:[(0,a.jsxs)("button",{onClick:y,className:"back-button",children:[a.jsx(i.Tsu,{size:20}),"Back to Plans"]}),(0,a.jsxs)("div",{className:"header-content",children:[(0,a.jsxs)("h1",{children:["Add ",c.charAt(0).toUpperCase()+c.slice(1)," Plan"]}),(0,a.jsxs)("p",{children:["Select a plan to add to ",v?.companyName||"this company"]})]})]}),(0,a.jsxs)("div",{className:"plan-category",children:[(0,a.jsxs)("div",{className:"category-header",children:[a.jsx("h2",{children:"Available Plans"}),(0,a.jsxs)("span",{className:"plan-count",children:[d.length," plan",1!==d.length?"s":""," available"]})]}),0===d.length?(0,a.jsxs)("div",{className:"no-plans",children:[a.jsx(i.Moc,{size:48}),a.jsx("h3",{children:"No plans available"}),(0,a.jsxs)("p",{children:["There are no ",c," plans available for assignment at this time."]})]}):a.jsx("div",{className:"plans-grid",children:d.map(e=>(0,a.jsxs)("div",{className:"plan-card selectable",onClick:()=>q(e),children:[(0,a.jsxs)("div",{className:"plan-header",children:[a.jsx("div",{className:"plan-icon-wrapper",children:b(e.coverageType)}),a.jsx("div",{className:"add-indicator",children:a.jsx(i.r7I,{size:16})})]}),(0,a.jsxs)("div",{className:"plan-content",children:[a.jsx("h3",{children:e.planName}),a.jsx("div",{className:"plan-carrier",children:e.carrier?.carrierName||"Unknown Carrier"}),(0,a.jsxs)("div",{className:"plan-type",children:[e.planType," ",e.metalTier&&`• ${e.metalTier}`]}),e.description&&a.jsx("p",{className:"plan-description",children:e.description}),e.planCode&&(0,a.jsxs)("div",{className:"plan-code",children:[a.jsx("span",{className:"code-label",children:"Plan Code:"}),a.jsx("span",{className:"code-value",children:e.planCode})]}),e.highlights&&e.highlights.length>0&&a.jsx("div",{className:"plan-highlights",children:e.highlights.slice(0,3).map((e,s)=>a.jsx("div",{className:"highlight-item",children:(0,a.jsxs)("span",{children:["• ",e]})},s))})]}),a.jsx("div",{className:"plan-actions",children:(0,a.jsxs)("button",{className:"select-plan-btn",children:[a.jsx(i.r7I,{size:16}),"Add Plan"]})})]},e._id))})]})]})}},6079:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>a});let a=(0,r(68570).createProxy)(String.raw`C:\beno\project_dev\qharmony-frontend-v1\src\app\ai-enroller\layout.tsx#default`)},27986:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>a});let a=(0,r(68570).createProxy)(String.raw`C:\beno\project_dev\qharmony-frontend-v1\src\app\ai-enroller\manage-groups\[companyId]\manage-plans\add-plan\page.tsx#default`)},54658:()=>{},23824:()=>{}};var s=require("../../../../../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),a=s.X(0,[8948,1183,6621,8492,576,889],()=>r(54707));module.exports=a})();