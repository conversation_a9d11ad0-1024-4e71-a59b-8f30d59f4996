(()=>{var e={};e.id=715,e.ids=[715],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},27790:e=>{"use strict";e.exports=require("assert")},84770:e=>{"use strict";e.exports=require("crypto")},17702:e=>{"use strict";e.exports=require("events")},92048:e=>{"use strict";e.exports=require("fs")},32615:e=>{"use strict";e.exports=require("http")},35240:e=>{"use strict";e.exports=require("https")},19801:e=>{"use strict";e.exports=require("os")},55315:e=>{"use strict";e.exports=require("path")},76162:e=>{"use strict";e.exports=require("stream")},74026:e=>{"use strict";e.exports=require("string_decoder")},74175:e=>{"use strict";e.exports=require("tty")},17360:e=>{"use strict";e.exports=require("url")},21764:e=>{"use strict";e.exports=require("util")},71568:e=>{"use strict";e.exports=require("zlib")},98061:e=>{"use strict";e.exports=require("node:assert")},92761:e=>{"use strict";e.exports=require("node:async_hooks")},72254:e=>{"use strict";e.exports=require("node:buffer")},40027:e=>{"use strict";e.exports=require("node:console")},6005:e=>{"use strict";e.exports=require("node:crypto")},65714:e=>{"use strict";e.exports=require("node:diagnostics_channel")},15673:e=>{"use strict";e.exports=require("node:events")},88849:e=>{"use strict";e.exports=require("node:http")},42725:e=>{"use strict";e.exports=require("node:http2")},87503:e=>{"use strict";e.exports=require("node:net")},38846:e=>{"use strict";e.exports=require("node:perf_hooks")},39630:e=>{"use strict";e.exports=require("node:querystring")},84492:e=>{"use strict";e.exports=require("node:stream")},31764:e=>{"use strict";e.exports=require("node:tls")},41041:e=>{"use strict";e.exports=require("node:url")},47261:e=>{"use strict";e.exports=require("node:util")},93746:e=>{"use strict";e.exports=require("node:util/types")},24086:e=>{"use strict";e.exports=require("node:worker_threads")},65628:e=>{"use strict";e.exports=require("node:zlib")},33297:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>l.a,__next_app__:()=>u,originalPathname:()=>p,pages:()=>d,routeModule:()=>m,tree:()=>c}),t(34930),t(6079),t(33709),t(35866);var a=t(23191),r=t(88716),n=t(37922),l=t.n(n),o=t(95231),i={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>o[e]);t.d(s,i);let c=["",{children:["ai-enroller",{children:["manage-groups",{children:["[companyId]",{children:["manage-plans",{children:["configure-plan",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,34930)),"C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\manage-groups\\[companyId]\\manage-plans\\configure-plan\\page.tsx"]}]},{}]},{}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,6079)),"C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,33709)),"C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,35866,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],d=["C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\manage-groups\\[companyId]\\manage-plans\\configure-plan\\page.tsx"],p="/ai-enroller/manage-groups/[companyId]/manage-plans/configure-plan/page",u={require:t,loadChunk:()=>Promise.resolve()},m=new a.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/ai-enroller/manage-groups/[companyId]/manage-plans/configure-plan/page",pathname:"/ai-enroller/manage-groups/[companyId]/manage-plans/configure-plan",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},40853:(e,s,t)=>{Promise.resolve().then(t.bind(t,78021))},92805:(e,s,t)=>{Promise.resolve().then(t.bind(t,4393))},78021:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>n});var a=t(10326);t(17577),t(23824),t(54658);var r=t(43058);function n({children:e}){return a.jsx(r.Z,{children:a.jsx("div",{className:"min-h-screen bg-white",style:{backgroundColor:"white",minHeight:"100vh"},children:e})})}},4393:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>i});var a=t(10326),r=t(17577),n=t(35047),l=t(38492),o=t(25842);t(61813);let i=()=>{let e=(0,n.useRouter)(),s=(0,n.useParams)(),t=(0,n.useSearchParams)(),i=s.companyId,c=t.get("planId"),[d,p]=(0,r.useState)(null),[u,m]=(0,r.useState)(!0),[h,x]=(0,r.useState)(null),[g,y]=(0,r.useState)(!1),[v,j]=(0,r.useState)("Percentage"),[N,C]=(0,r.useState)([{tierName:"Employee Only",totalCost:450,employeeCost:90,employerCost:360},{tierName:"Employee + Spouse",totalCost:890,employeeCost:178,employerCost:712},{tierName:"Employee + Children",totalCost:720,employeeCost:144,employerCost:576},{tierName:"Employee + Family",totalCost:1250,employeeCost:250,employerCost:1e3}]),b=(0,o.v9)(e=>e.user.managedCompanies);b?.find(e=>e._id===i);let f=async()=>{try{m(!0),x(null);let e=await fetch(`http://localhost:8080/api/plans/${c}`,{method:"GET",headers:{"Content-Type":"application/json","user-id":localStorage.getItem("userid1")||localStorage.getItem("userId")||"6838677aef6db0212bcfdacd"}});if(e.ok){let s=await e.json();console.log("Plan details result:",s),p(s.plan||s)}else throw Error("Failed to fetch plan details")}catch(e){console.error("Error fetching plan details:",e),x("Failed to load plan details")}finally{m(!1)}};(0,r.useEffect)(()=>{c&&f()},[c,f]);let q=()=>{e.push(`/ai-enroller/manage-groups/${i}/manage-plans`)},P=(e,s,t)=>{let a=[...N];if(a[e]={...a[e],[s]:"tierName"===s?t:parseFloat(t.toString())||0},"totalCost"===s||"employeeCost"===s){let s=a[e].totalCost,t=a[e].employeeCost;a[e].employerCost=s-t}C(a)},S=(e,s)=>{let t=[...N],a=t[e].totalCost,r=a*s/100;t[e]={...t[e],employerCost:r,employeeCost:a-r},C(t)},_=e=>{N.length>1&&C(N.filter((s,t)=>t!==e))},w=async()=>{try{y(!0);let s={planId:c,companyId:i,groupNumber:`POL-${Math.random().toString(36).substr(2,9).toUpperCase()}`,waitingPeriod:{enabled:!1,days:0,rule:"Immediate"},enrollmentType:"Active",rateStructure:"Flat",coverageTiers:N,planEffectiveDate:new Date().toISOString(),planEndDate:new Date(new Date().getFullYear()+1,11,31).toISOString(),enrollmentStartDate:new Date().toISOString(),enrollmentEndDate:new Date(new Date().getFullYear(),11,31).toISOString(),employerContribution:{contributionType:v,contributionAmount:80},employeeContribution:{contributionType:v,contributionAmount:20},ageBandedRates:[],salaryBasedRates:[],planCustomizations:{},isActive:!1,status:"Draft"},t=await fetch("http://localhost:8080/api/pre-enrollment/plan-assignments",{method:"POST",headers:{"Content-Type":"application/json","user-id":localStorage.getItem("userid1")||localStorage.getItem("userId")||"6838677aef6db0212bcfdacd"},body:JSON.stringify(s)});if(t.ok){let s=await t.json();console.log("Plan assignment created:",s),e.push(`/ai-enroller/manage-groups/${i}/manage-plans`)}else throw Error("Failed to create plan assignment")}catch(e){console.error("Error saving plan assignment:",e),alert("Failed to save plan configuration. Please try again.")}finally{y(!1)}};return u?a.jsx("div",{className:"manage-plans-page",children:(0,a.jsxs)("div",{className:"loading-container",children:[a.jsx("div",{className:"loading-spinner"}),a.jsx("p",{children:"Loading plan details..."})]})}):h||!d?a.jsx("div",{className:"manage-plans-page",children:(0,a.jsxs)("div",{className:"error-container",children:[a.jsx("p",{children:h||"Plan not found"}),(0,a.jsxs)("button",{onClick:()=>{e.push(`/ai-enroller/manage-groups/${i}/manage-plans/add-plan`)},className:"back-button",children:[a.jsx(l.Tsu,{size:20}),"Back to Plan Selection"]})]})}):(0,a.jsxs)("div",{className:"manage-plans-page",children:[a.jsx("div",{className:"modal-header",children:(0,a.jsxs)("div",{className:"modal-title",children:[a.jsx("h1",{children:"Edit Plan"}),a.jsx("button",{onClick:q,className:"close-button",children:a.jsx(l.fMW,{size:24})})]})}),(0,a.jsxs)("div",{className:"plan-info-section",children:[(0,a.jsxs)("div",{className:"plan-title",children:[a.jsx("h2",{children:d.planName}),a.jsx("span",{className:"status-badge active",children:"active"})]}),a.jsx("div",{className:"plan-carrier",children:d.carrier?.carrierName}),(0,a.jsxs)("div",{className:"plan-details-grid",children:[(0,a.jsxs)("div",{className:"detail-item",children:[a.jsx("span",{className:"label",children:"Type:"}),a.jsx("span",{className:"value",children:d.planType})]}),(0,a.jsxs)("div",{className:"detail-item",children:[a.jsx("span",{className:"label",children:"Plan Code:"}),a.jsx("span",{className:"value",children:d.planCode||"N/A"})]}),(0,a.jsxs)("div",{className:"detail-item",children:[a.jsx("span",{className:"label",children:"Category:"}),a.jsx("span",{className:"value",children:d.coverageType})]}),(0,a.jsxs)("div",{className:"detail-item",children:[a.jsx("span",{className:"label",children:"Policy #:"}),(0,a.jsxs)("span",{className:"value",children:["POL-",Math.random().toString(36).substr(2,9).toUpperCase()]})]})]})]}),(0,a.jsxs)("div",{className:"coverage-section",children:[(0,a.jsxs)("div",{className:"section-header",children:[a.jsx("h3",{children:"Coverage Tiers & Contributions"}),(0,a.jsxs)("div",{className:"contribution-type-selector",children:[(0,a.jsxs)("select",{value:v,onChange:e=>j(e.target.value),children:[a.jsx("option",{value:"Percentage",children:"Percentage"}),a.jsx("option",{value:"Fixed",children:"Fixed Amount"})]}),(0,a.jsxs)("button",{onClick:()=>{C([...N,{tierName:"New Tier",totalCost:0,employeeCost:0,employerCost:0}])},className:"add-tier-btn",children:[a.jsx(l.r7I,{size:16}),"Add"]})]})]}),(0,a.jsxs)("div",{className:"tiers-table",children:[(0,a.jsxs)("div",{className:"table-header",children:[a.jsx("div",{className:"col-tier",children:"Coverage Tier"}),a.jsx("div",{className:"col-premium",children:"Premium"}),a.jsx("div",{className:"col-employer",children:"Employer (%)"}),a.jsx("div",{className:"col-employer-pays",children:"Employer Pays"}),a.jsx("div",{className:"col-employee-pays",children:"Employee Pays"}),a.jsx("div",{className:"col-actions"})]}),N.map((e,s)=>(0,a.jsxs)("div",{className:"table-row",children:[a.jsx("div",{className:"col-tier",children:a.jsx("input",{type:"text",value:e.tierName,onChange:e=>P(s,"tierName",e.target.value),className:"tier-name-input"})}),a.jsx("div",{className:"col-premium",children:a.jsx("input",{type:"number",value:e.totalCost,onChange:e=>P(s,"totalCost",e.target.value),className:"cost-input",step:"0.01"})}),a.jsx("div",{className:"col-employer",children:a.jsx("input",{type:"number",value:e.totalCost>0?Math.round(e.employerCost/e.totalCost*100):0,onChange:e=>S(s,parseFloat(e.target.value)||0),className:"percentage-input",min:"0",max:"100"})}),a.jsx("div",{className:"col-employer-pays",children:(0,a.jsxs)("span",{className:"cost-display",children:["$",e.employerCost.toFixed(2)]})}),a.jsx("div",{className:"col-employee-pays",children:(0,a.jsxs)("span",{className:"cost-display",children:["$",e.employeeCost.toFixed(2)]})}),a.jsx("div",{className:"col-actions",children:N.length>1&&a.jsx("button",{onClick:()=>_(s),className:"remove-tier-btn",children:a.jsx(l.Bhs,{size:16})})})]},s))]})]}),(0,a.jsxs)("div",{className:"modal-actions",children:[a.jsx("button",{onClick:q,className:"cancel-btn",children:"Cancel"}),a.jsx("button",{onClick:w,className:"save-btn",disabled:g,children:g?"Saving...":"Save Changes"})]})]})}},6079:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});let a=(0,t(68570).createProxy)(String.raw`C:\beno\project_dev\qharmony-frontend-v1\src\app\ai-enroller\layout.tsx#default`)},34930:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});let a=(0,t(68570).createProxy)(String.raw`C:\beno\project_dev\qharmony-frontend-v1\src\app\ai-enroller\manage-groups\[companyId]\manage-plans\configure-plan\page.tsx#default`)},54658:()=>{},23824:()=>{}};var s=require("../../../../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),a=s.X(0,[8948,1183,6621,8492,576,889],()=>t(33297));module.exports=a})();