(()=>{var e={};e.id=7073,e.ids=[7073],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},27790:e=>{"use strict";e.exports=require("assert")},84770:e=>{"use strict";e.exports=require("crypto")},17702:e=>{"use strict";e.exports=require("events")},92048:e=>{"use strict";e.exports=require("fs")},32615:e=>{"use strict";e.exports=require("http")},35240:e=>{"use strict";e.exports=require("https")},19801:e=>{"use strict";e.exports=require("os")},55315:e=>{"use strict";e.exports=require("path")},76162:e=>{"use strict";e.exports=require("stream")},74026:e=>{"use strict";e.exports=require("string_decoder")},74175:e=>{"use strict";e.exports=require("tty")},17360:e=>{"use strict";e.exports=require("url")},21764:e=>{"use strict";e.exports=require("util")},71568:e=>{"use strict";e.exports=require("zlib")},98061:e=>{"use strict";e.exports=require("node:assert")},92761:e=>{"use strict";e.exports=require("node:async_hooks")},72254:e=>{"use strict";e.exports=require("node:buffer")},40027:e=>{"use strict";e.exports=require("node:console")},6005:e=>{"use strict";e.exports=require("node:crypto")},65714:e=>{"use strict";e.exports=require("node:diagnostics_channel")},15673:e=>{"use strict";e.exports=require("node:events")},88849:e=>{"use strict";e.exports=require("node:http")},42725:e=>{"use strict";e.exports=require("node:http2")},87503:e=>{"use strict";e.exports=require("node:net")},38846:e=>{"use strict";e.exports=require("node:perf_hooks")},39630:e=>{"use strict";e.exports=require("node:querystring")},84492:e=>{"use strict";e.exports=require("node:stream")},31764:e=>{"use strict";e.exports=require("node:tls")},41041:e=>{"use strict";e.exports=require("node:url")},47261:e=>{"use strict";e.exports=require("node:util")},93746:e=>{"use strict";e.exports=require("node:util/types")},24086:e=>{"use strict";e.exports=require("node:worker_threads")},65628:e=>{"use strict";e.exports=require("node:zlib")},46094:(e,s,a)=>{"use strict";a.r(s),a.d(s,{GlobalError:()=>t.a,__next_app__:()=>u,originalPathname:()=>p,pages:()=>d,routeModule:()=>m,tree:()=>o}),a(69241),a(6079),a(33709),a(35866);var r=a(23191),n=a(88716),l=a(37922),t=a.n(l),i=a(95231),c={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>i[e]);a.d(s,c);let o=["",{children:["ai-enroller",{children:["manage-groups",{children:["[companyId]",{children:["manage-plans",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,69241)),"C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\manage-groups\\[companyId]\\manage-plans\\page.tsx"]}]},{}]},{}]},{}]},{layout:[()=>Promise.resolve().then(a.bind(a,6079)),"C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(a.bind(a,33709)),"C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,35866,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],d=["C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\manage-groups\\[companyId]\\manage-plans\\page.tsx"],p="/ai-enroller/manage-groups/[companyId]/manage-plans/page",u={require:a,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:n.x.APP_PAGE,page:"/ai-enroller/manage-groups/[companyId]/manage-plans/page",pathname:"/ai-enroller/manage-groups/[companyId]/manage-plans",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},61854:(e,s,a)=>{Promise.resolve().then(a.bind(a,50299))},50299:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>c});var r=a(10326),n=a(17577),l=a(35047),t=a(38492);a(61813);var i=a(43933);let c=()=>{let e=(0,l.useRouter)(),s=(0,l.useParams)().companyId,[a,c]=(0,n.useState)(!1),[o,d]=(0,n.useState)([]),[p,u]=(0,n.useState)(!0),[m,h]=(0,n.useState)(null),x=async e=>{try{let s=await fetch(`http://localhost:8080/api/pre-enrollment/plans/${e}`,{headers:{"Content-Type":"application/json","user-id":(()=>{let e=localStorage.getItem("userid1")||localStorage.getItem("userId");if(!e)throw Error("User ID not found. Please authenticate first.");return e})()}});if(s.ok){let e=await s.json();return{planName:e.plan?.planName||"Unknown Plan",planCode:e.plan?.planCode||"N/A",planType:e.plan?.planType||"N/A",coverageType:e.plan?.coverageType||"Unknown",coverageSubTypes:e.plan?.coverageSubTypes||[],carrierName:e.carrier?.carrierName||"Unknown Carrier"}}}catch(s){console.error("Error fetching plan details for planId:",e,s)}return{planName:"Unknown Plan",planCode:"N/A",planType:"N/A",coverageType:"Unknown",coverageSubTypes:[],carrierName:"Unknown Carrier"}},g=async()=>{try{u(!0),h(null);let e=await (0,i.fH)(s,{includePlanData:!0});if(e.success&&e.data){let s=e.data.assignments;console.log("Fetched plan assignments:",s);let a=await Promise.all(s.map(async e=>{let s="string"==typeof e.planId?e.planId:e.planId._id,a=await x(s);return{...e,planDetails:a}}));console.log("Enriched assignments with plan details:",a),d(a)}else h(e.error||"Failed to fetch plan assignments")}catch(e){console.error("Error fetching plan assignments:",e),h("Failed to load plan assignments")}finally{u(!1)}};(0,n.useEffect)(()=>{g()},[s]),(0,n.useEffect)(()=>{let e=setInterval(()=>{console.log("Auto-refreshing plan assignments..."),g()},3e4);return()=>clearInterval(e)},[s]);let v=a=>{e.push(`/ai-enroller/manage-groups/${s}/manage-plans/add-plan?category=${a}`)},y=e=>{console.log("Edit plan:",e)},j=e=>e.planDetails?{name:e.planDetails.planName,code:e.planDetails.planCode,carrier:e.planDetails.carrierName,type:e.planDetails.planType,coverageType:e.planDetails.coverageType,coverageSubTypes:e.planDetails.coverageSubTypes||[]}:(console.warn("Plan assignment missing planDetails:",e._id),{name:"Unknown Plan",code:"N/A",carrier:"Unknown Carrier",type:"N/A",coverageType:"Unknown",coverageSubTypes:[]}),b=(e,s)=>{let a=new Date(e).toLocaleDateString(),r=new Date(s).toLocaleDateString();return`${a} - ${r}`},N=e=>e.coverageSubTypes&&e.coverageSubTypes.length>0?e.coverageSubTypes[0]:e.type||"N/A",f=(()=>{let e={};return o.forEach(s=>{let a=j(s);if(console.log("Categorizing plan:",a.name,"Coverage SubTypes:",a.coverageSubTypes),a.coverageSubTypes&&a.coverageSubTypes.length>0){let r=a.coverageSubTypes[0],n=r.toLowerCase();e[n]||(e[n]=[]),e[n].push(s),console.log(`Plan "${a.name}" assigned to category: ${r}`)}else{let r="other";e[r]||(e[r]=[]),e[r].push(s),console.log(`Plan "${a.name}" assigned to fallback category: ${r}`)}}),console.log("Final categorization:",Object.keys(e).reduce((s,a)=>(s[a]=e[a].length,s),{})),e})(),P=e=>({medical:"Medical Plans",dental:"Dental Plans",vision:"Vision Plans","term life":"Term Life Plans","supplemental life insurance":"Supplemental Life Plans","short-term disability":"Short-Term Disability Plans","long-term disability":"Long-Term Disability Plans","whole life":"Whole Life Plans","group (employer) life":"Group Life Plans",other:"Other Plans"})[e]||`${e.charAt(0).toUpperCase()+e.slice(1)} Plans`;return p?r.jsx("div",{className:"manage-plans-page",children:(0,r.jsxs)("div",{className:"loading-container",children:[r.jsx("div",{className:"loading-spinner"}),r.jsx("p",{children:"Loading plan assignments..."})]})}):m?r.jsx("div",{className:"manage-plans-page",children:(0,r.jsxs)("div",{className:"error-container",children:[r.jsx("p",{children:m}),r.jsx("button",{onClick:g,className:"retry-button",children:"Try Again"})]})}):(0,r.jsxs)("div",{className:"manage-plans-page",children:[(0,r.jsxs)("div",{className:"breadcrumb-nav",children:[(0,r.jsxs)("div",{className:"breadcrumb-item completed",children:[r.jsx(t.Sul,{size:16}),r.jsx("span",{children:"Home"})]}),(0,r.jsxs)("div",{className:"breadcrumb-item completed",children:[r.jsx(t.Sul,{size:16}),r.jsx("span",{children:"Select Company"})]}),(0,r.jsxs)("div",{className:"breadcrumb-item active",children:[r.jsx(t.Sul,{size:16}),r.jsx("span",{children:"View Plans"})]}),r.jsx("div",{className:"breadcrumb-item",children:r.jsx("span",{children:"Contributions"})}),r.jsx("div",{className:"breadcrumb-item",children:r.jsx("span",{children:"Review"})})]}),(0,r.jsxs)("div",{className:"page-header",children:[(0,r.jsxs)("div",{className:"header-content",children:[r.jsx("h1",{children:"Current Plan Overview"}),r.jsx("p",{className:"company-subtitle",children:"for TechCorp Inc."})]}),(0,r.jsxs)("div",{className:"header-actions",children:[(0,r.jsxs)("button",{className:"refresh-btn",onClick:()=>{console.log("Manual refresh triggered"),g()},title:"Refresh plan assignments",children:[r.jsx(t.zHJ,{size:20}),"Refresh"]}),(0,r.jsxs)("button",{className:"add-new-plan-btn",onClick:()=>{e.push(`/ai-enroller/manage-groups/${s}/manage-plans/add-plan`)},children:[r.jsx(t.r7I,{size:20}),"Add New Plan"]})]})]}),(0,r.jsxs)("div",{className:"summary-cards",children:[(0,r.jsxs)("div",{className:"summary-card purple",children:[r.jsx("div",{className:"summary-icon",children:r.jsx(t.Q5u,{size:24})}),(0,r.jsxs)("div",{className:"summary-content",children:[r.jsx("div",{className:"summary-label",children:"Total Plans"}),r.jsx("div",{className:"summary-value",children:o.length}),r.jsx("div",{className:"summary-subtitle",children:"Active benefit plans"})]})]}),(0,r.jsxs)("div",{className:"summary-card green",children:[r.jsx("div",{className:"summary-icon",children:r.jsx(t.Sul,{size:24})}),(0,r.jsxs)("div",{className:"summary-content",children:[r.jsx("div",{className:"summary-label",children:"Plan Year"}),r.jsx("div",{className:"summary-value",children:"2024"}),r.jsx("div",{className:"summary-subtitle",children:"January 1 - December 31"})]})]}),(0,r.jsxs)("div",{className:"summary-card orange",children:[r.jsx("div",{className:"summary-icon",children:r.jsx("span",{children:"$"})}),(0,r.jsxs)("div",{className:"summary-content",children:[r.jsx("div",{className:"summary-label",children:"Est. Monthly Cost"}),r.jsx("div",{className:"summary-value",children:"$3,032"}),r.jsx("div",{className:"summary-subtitle",children:"Employer contribution"})]})]})]}),r.jsx("div",{className:"select-all-section",children:(0,r.jsxs)("label",{className:"select-all-checkbox",children:[r.jsx("input",{type:"checkbox",checked:a,onChange:()=>{c(!a)}}),(0,r.jsxs)("span",{children:["Select All Plans (0 of ",o.length," selected)"]})]})}),(()=>{let e=Object.keys(f),s=[];for(let a of["medical","dental","vision","term life","supplemental life insurance","whole life","group (employer) life","short-term disability","long-term disability","other"])e.includes(a)&&s.push(a);for(let a of e)s.includes(a)||s.push(a);return s})().map(e=>{let s=f[e];return(0,r.jsxs)("div",{className:"plan-category",children:[(0,r.jsxs)("div",{className:"category-header",children:[r.jsx("h2",{children:P(e)}),(0,r.jsxs)("span",{className:"plan-count",children:[s.length," plans configured"]}),(0,r.jsxs)("button",{className:"add-plan-btn",onClick:()=>v(e),children:[r.jsx(t.r7I,{size:16}),"Add Plan"]})]}),r.jsx("div",{className:"plans-list",children:s.map(e=>{let s=j(e);return(0,r.jsxs)("div",{className:"plan-card",children:[(0,r.jsxs)("div",{className:"plan-card-header",children:[r.jsx("input",{type:"checkbox",className:"plan-checkbox"}),(0,r.jsxs)("div",{className:"plan-name-section",children:[r.jsx("h3",{children:s.name}),r.jsx("div",{className:"plan-carrier",children:s.carrier})]}),r.jsx("div",{className:`plan-status-badge ${e.status?.toLowerCase()||"active"}`,children:e.status||"Active"})]}),(0,r.jsxs)("div",{className:"plan-details-grid",children:[(0,r.jsxs)("div",{className:"plan-detail",children:[r.jsx("span",{className:"detail-label",children:"Type:"}),r.jsx("span",{className:"detail-value",children:N(s)})]}),(0,r.jsxs)("div",{className:"plan-detail",children:[r.jsx("span",{className:"detail-label",children:"Plan Code:"}),r.jsx("span",{className:"detail-value",children:s.code})]}),(0,r.jsxs)("div",{className:"plan-detail",children:[r.jsx("span",{className:"detail-label",children:"Period:"}),r.jsx("span",{className:"detail-value",children:b(e.planEffectiveDate,e.planEndDate)})]}),(0,r.jsxs)("div",{className:"plan-detail",children:[r.jsx("span",{className:"detail-label",children:"Group #:"}),r.jsx("span",{className:"detail-value",children:e.groupNumber||"N/A"})]})]}),r.jsx("div",{className:"plan-actions",children:(0,r.jsxs)("button",{className:"edit-btn",onClick:()=>y(e._id),children:[r.jsx(t._vs,{size:16}),"Edit"]})})]},e._id)})})]},e)})]})}},69241:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>r});let r=(0,a(68570).createProxy)(String.raw`C:\beno\project_dev\qharmony-frontend-v1\src\app\ai-enroller\manage-groups\[companyId]\manage-plans\page.tsx#default`)}};var s=require("../../../../../webpack-runtime.js");s.C(e);var a=e=>s(s.s=e),r=s.X(0,[8948,1183,6621,8492,576,9902,889],()=>a(46094));module.exports=r})();