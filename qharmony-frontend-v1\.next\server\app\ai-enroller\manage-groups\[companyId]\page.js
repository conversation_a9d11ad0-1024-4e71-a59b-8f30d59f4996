(()=>{var e={};e.id=5773,e.ids=[5773],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},27790:e=>{"use strict";e.exports=require("assert")},84770:e=>{"use strict";e.exports=require("crypto")},17702:e=>{"use strict";e.exports=require("events")},92048:e=>{"use strict";e.exports=require("fs")},32615:e=>{"use strict";e.exports=require("http")},35240:e=>{"use strict";e.exports=require("https")},19801:e=>{"use strict";e.exports=require("os")},55315:e=>{"use strict";e.exports=require("path")},76162:e=>{"use strict";e.exports=require("stream")},74026:e=>{"use strict";e.exports=require("string_decoder")},74175:e=>{"use strict";e.exports=require("tty")},17360:e=>{"use strict";e.exports=require("url")},21764:e=>{"use strict";e.exports=require("util")},71568:e=>{"use strict";e.exports=require("zlib")},98061:e=>{"use strict";e.exports=require("node:assert")},92761:e=>{"use strict";e.exports=require("node:async_hooks")},72254:e=>{"use strict";e.exports=require("node:buffer")},40027:e=>{"use strict";e.exports=require("node:console")},6005:e=>{"use strict";e.exports=require("node:crypto")},65714:e=>{"use strict";e.exports=require("node:diagnostics_channel")},15673:e=>{"use strict";e.exports=require("node:events")},88849:e=>{"use strict";e.exports=require("node:http")},42725:e=>{"use strict";e.exports=require("node:http2")},87503:e=>{"use strict";e.exports=require("node:net")},38846:e=>{"use strict";e.exports=require("node:perf_hooks")},39630:e=>{"use strict";e.exports=require("node:querystring")},84492:e=>{"use strict";e.exports=require("node:stream")},31764:e=>{"use strict";e.exports=require("node:tls")},41041:e=>{"use strict";e.exports=require("node:url")},47261:e=>{"use strict";e.exports=require("node:util")},93746:e=>{"use strict";e.exports=require("node:util/types")},24086:e=>{"use strict";e.exports=require("node:worker_threads")},65628:e=>{"use strict";e.exports=require("node:zlib")},16376:(e,r,s)=>{"use strict";s.r(r),s.d(r,{GlobalError:()=>o.a,__next_app__:()=>u,originalPathname:()=>p,pages:()=>d,routeModule:()=>m,tree:()=>c}),s(36397),s(6079),s(33709),s(35866);var t=s(23191),a=s(88716),n=s(37922),o=s.n(n),i=s(95231),l={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);s.d(r,l);let c=["",{children:["ai-enroller",{children:["manage-groups",{children:["[companyId]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,36397)),"C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\manage-groups\\[companyId]\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,6079)),"C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,33709)),"C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,35866,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],d=["C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\manage-groups\\[companyId]\\page.tsx"],p="/ai-enroller/manage-groups/[companyId]/page",u={require:s,loadChunk:()=>Promise.resolve()},m=new t.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/ai-enroller/manage-groups/[companyId]/page",pathname:"/ai-enroller/manage-groups/[companyId]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},40853:(e,r,s)=>{Promise.resolve().then(s.bind(s,78021))},27320:(e,r,s)=>{Promise.resolve().then(s.bind(s,75973))},82400:(e,r,s)=>{"use strict";s.d(r,{Z:()=>n});var t=s(51426),a=s(10326);let n=(0,t.Z)((0,a.jsx)("path",{d:"m17 7-1.41 1.41L18.17 11H8v2h10.17l-2.58 2.58L17 17l5-5zM4 5h8V3H4c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h8v-2H4z"}),"Logout")},33198:(e,r,s)=>{"use strict";s.d(r,{Z:()=>y});var t=s(17577),a=s(41135),n=s(88634),o=s(91703),i=s(13643),l=s(2791),c=s(51426),d=s(10326);let p=(0,c.Z)((0,d.jsx)("path",{d:"M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"}),"Person");var u=s(71685),m=s(97898);function x(e){return(0,m.ZP)("MuiAvatar",e)}(0,u.Z)("MuiAvatar",["root","colorDefault","circular","rounded","square","img","fallback"]);var h=s(31121);let f=e=>{let{classes:r,variant:s,colorDefault:t}=e;return(0,n.Z)({root:["root",s,t&&"colorDefault"],img:["img"],fallback:["fallback"]},x,r)},v=(0,o.default)("div",{name:"MuiAvatar",slot:"Root",overridesResolver:(e,r)=>{let{ownerState:s}=e;return[r.root,r[s.variant],s.colorDefault&&r.colorDefault]}})((0,i.Z)(({theme:e})=>({position:"relative",display:"flex",alignItems:"center",justifyContent:"center",flexShrink:0,width:40,height:40,fontFamily:e.typography.fontFamily,fontSize:e.typography.pxToRem(20),lineHeight:1,borderRadius:"50%",overflow:"hidden",userSelect:"none",variants:[{props:{variant:"rounded"},style:{borderRadius:(e.vars||e).shape.borderRadius}},{props:{variant:"square"},style:{borderRadius:0}},{props:{colorDefault:!0},style:{color:(e.vars||e).palette.background.default,...e.vars?{backgroundColor:e.vars.palette.Avatar.defaultBg}:{backgroundColor:e.palette.grey[400],...e.applyStyles("dark",{backgroundColor:e.palette.grey[600]})}}}]}))),g=(0,o.default)("img",{name:"MuiAvatar",slot:"Img",overridesResolver:(e,r)=>r.img})({width:"100%",height:"100%",textAlign:"center",objectFit:"cover",color:"transparent",textIndent:1e4}),j=(0,o.default)(p,{name:"MuiAvatar",slot:"Fallback",overridesResolver:(e,r)=>r.fallback})({width:"75%",height:"75%"}),y=t.forwardRef(function(e,r){let s=(0,l.i)({props:e,name:"MuiAvatar"}),{alt:n,children:o,className:i,component:c="div",slots:p={},slotProps:u={},imgProps:m,sizes:x,src:y,srcSet:b,variant:N="circular",...q}=s,S=null,w={...s,component:c,variant:N},k=function({crossOrigin:e,referrerPolicy:r,src:s,srcSet:a}){let[n,o]=t.useState(!1);return t.useEffect(()=>{if(!s&&!a)return;o(!1);let t=!0,n=new Image;return n.onload=()=>{t&&o("loaded")},n.onerror=()=>{t&&o("error")},n.crossOrigin=e,n.referrerPolicy=r,n.src=s,a&&(n.srcset=a),()=>{t=!1}},[e,r,s,a]),n}({...m,..."function"==typeof u.img?u.img(w):u.img,src:y,srcSet:b}),C=y||b,P=C&&"error"!==k;w.colorDefault=!P,delete w.ownerState;let _=f(w),[I,Z]=(0,h.Z)("img",{className:_.img,elementType:g,externalForwardedProps:{slots:p,slotProps:{img:{...m,...u.img}}},additionalProps:{alt:n,src:y,srcSet:b,sizes:x},ownerState:w});return S=P?(0,d.jsx)(I,{...Z}):o||0===o?o:C&&n?n[0]:(0,d.jsx)(j,{ownerState:w,className:_.fallback}),(0,d.jsx)(v,{as:c,className:(0,a.Z)(_.root,i),ref:r,...q,ownerState:w,children:S})})},63948:(e,r)=>{"use strict";var s=Symbol.for("react.element"),t=(Symbol.for("react.portal"),Symbol.for("react.fragment"),Symbol.for("react.strict_mode"),Symbol.for("react.profiler"),Symbol.for("react.provider"),Symbol.for("react.context"),Symbol.for("react.forward_ref"),Symbol.for("react.suspense"),Symbol.for("react.memo"),Symbol.for("react.lazy"),Symbol.iterator,{isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}}),a=Object.assign,n={};function o(e,r,s){this.props=e,this.context=r,this.refs=n,this.updater=s||t}function i(){}function l(e,r,s){this.props=e,this.context=r,this.refs=n,this.updater=s||t}o.prototype.isReactComponent={},o.prototype.setState=function(e,r){if("object"!=typeof e&&"function"!=typeof e&&null!=e)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,r,"setState")},o.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")},i.prototype=o.prototype;var c=l.prototype=new i;c.constructor=l,a(c,o.prototype),c.isPureReactComponent=!0;var d=Object.prototype.hasOwnProperty,p={current:null},u={key:!0,ref:!0,__self:!0,__source:!0};r.createElement=function(e,r,t){var a,n={},o=null,i=null;if(null!=r)for(a in void 0!==r.ref&&(i=r.ref),void 0!==r.key&&(o=""+r.key),r)d.call(r,a)&&!u.hasOwnProperty(a)&&(n[a]=r[a]);var l=arguments.length-2;if(1===l)n.children=t;else if(1<l){for(var c=Array(l),m=0;m<l;m++)c[m]=arguments[m+2];n.children=c}if(e&&e.defaultProps)for(a in l=e.defaultProps)void 0===n[a]&&(n[a]=l[a]);return{$$typeof:s,type:e,key:o,ref:i,props:n,_owner:p.current}}},95746:(e,r,s)=>{"use strict";e.exports=s(63948)},67925:(e,r,s)=>{"use strict";s.d(r,{Z:()=>v});var t=s(10326),a=s(17577),n=s(6283),o=s(25609),i=s(33198),l=s(42265),c=s(82400),d=s(46226),p=s(22758),u=s(31870),m=s(25842),x=s(30656),h=s(88563),f=s(35047);let v=()=>{let e=(0,f.useRouter)(),{logout:r}=(0,p.a)(),s=(0,u.C)(e=>e.user.userProfile),[v,g]=(0,a.useState)(!1);(0,a.useEffect)(()=>{g("true"===localStorage.getItem("isTeamsApp1"))},[]);let j=(0,m.v9)(e=>e.user.userProfile.isAdmin),y=(0,m.v9)(e=>e.user.userProfile.isBroker);return t.jsx(n.Z,{sx:{bgcolor:"white",padding:2,textAlign:"center",height:"80px",borderBottom:"1px solid #D2D2D2",position:"sticky",top:0,zIndex:50,boxShadow:"0 1px 3px 0 rgba(0, 0, 0, 0.1)"},children:(0,t.jsxs)(n.Z,{sx:{display:"flex",alignItems:"center",justifyContent:"space-between"},children:[(0,t.jsxs)(n.Z,{sx:{display:"flex",alignItems:"center"},children:[(0,t.jsxs)(n.Z,{sx:{display:"flex",alignItems:"center",cursor:"pointer",mr:3},onClick:()=>{e.push("/dashboard")},children:[t.jsx(d.default,{src:x.Z,alt:"BenOsphere Logo",width:40,height:40}),t.jsx(o.Z,{sx:{fontWeight:800,fontSize:"1.5rem",ml:1,color:"#111827"},children:"BenOsphere"})]}),t.jsx(i.Z,{sx:{bgcolor:"black",color:"#ffffff",width:35,height:35,fontSize:"1.2rem",mr:1.5,ml:2,display:"flex",alignItems:"center",justifyContent:"center",paddingBottom:"1px",fontWeight:800},children:(e=>{if(!e)return"";let[r,s]=e.split(" ");return`${r[0].toUpperCase()}${s?s[0].toUpperCase():""}`})(s.name)}),t.jsx(o.Z,{variant:"h4",sx:{mb:0,fontWeight:"bold",display:"flex",alignItems:"center",justifyContent:"flex-start",textAlign:"flex-start",fontSize:"16px",mr:1.5,color:"#111827"},children:s.name.replace(/\b\w/g,e=>e.toUpperCase())}),j&&t.jsx(n.Z,{sx:{bgcolor:"rgba(0, 0, 0, 0.06)",borderRadius:"8px",padding:"4px 8px",display:"flex",alignItems:"center",justifyContent:"center",fontWeight:"bold",fontSize:"12px",color:"#333",mr:1.5},children:"ADMIN"}),y&&t.jsx(n.Z,{sx:{bgcolor:"#f5f5f5",borderRadius:"8px",padding:"2px 8px",display:"flex",alignItems:"center",justifyContent:"center",fontWeight:"bold",fontSize:"0.9rem",color:"#333",mr:1.5},children:"BROKER"})]}),(0,t.jsxs)(n.Z,{sx:{display:"flex",alignItems:"center"},children:[!v&&(0,t.jsxs)(l.Z,{sx:{backgroundColor:"transparent",color:"#333",textTransform:"none",padding:"8px 8px",display:"flex",alignItems:"center",justifyContent:"flex-start",gap:"2px","&:hover":{backgroundColor:"transparent",color:"#555"},boxShadow:"none"},children:[t.jsx(n.Z,{sx:{mt:.5,mr:.5},children:t.jsx(h.Z,{})}),t.jsx(o.Z,{sx:{fontWeight:500,fontSize:"14px",color:"#333"},children:"Guide"})]}),!v&&(0,t.jsxs)(l.Z,{onClick:r,sx:{backgroundColor:"transparent",color:"#333",marginLeft:1,textTransform:"none",padding:"8px 16px",display:"flex",alignItems:"center",justifyContent:"flex-start",gap:"8px","&:hover":{backgroundColor:"transparent",color:"#555"},boxShadow:"none"},children:[t.jsx(c.Z,{sx:{fontSize:"18px"}}),t.jsx(o.Z,{sx:{fontWeight:500,fontSize:"14px",color:"#333"},children:"Logout"})]})]})]})})}},78021:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>n});var t=s(10326);s(17577),s(23824),s(54658);var a=s(43058);function n({children:e}){return t.jsx(a.Z,{children:t.jsx("div",{className:"min-h-screen bg-white",style:{backgroundColor:"white",minHeight:"100vh"},children:e})})}},75973:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>c});var t=s(10326),a=s(17577),n=s(35047),o=s(38492),i=s(25842),l=s(67925);s(29566);let c=()=>{let e=(0,n.useRouter)(),r=(0,n.useParams)();(0,i.I0)();let s=r.companyId,[c,d]=(0,a.useState)([]),[p,u]=(0,a.useState)(!0),[m,x]=(0,a.useState)("overview"),h=(0,i.v9)(e=>e.user.managedCompanies),f=h?.find(e=>e._id===s),v={totalEmployees:f?.companySize||127,avgAge:35,highRisk:"40%",withDependents:"60%"};(0,a.useEffect)(()=>{f&&g()},[f]),(0,a.useEffect)(()=>{d([{_id:"1",name:"John Doe",email:"<EMAIL>",role:"Manager",isActivated:!0},{_id:"2",name:"Jane Smith",email:"<EMAIL>",role:"Developer",isActivated:!0},{_id:"3",name:"Bob Johnson",email:"<EMAIL>",role:"Designer",isActivated:!1}]),u(!1)},[]);let g=async()=>{try{u(!1)}catch(e){console.error("Error fetching employees:",e),u(!1)}},j=()=>{e.push("/ai-enroller/manage-groups")},y=e=>{switch(e){case"medical":return t.jsx(o.wkn,{className:"plan-icon medical"});case"dental":return t.jsx(o.Q5u,{className:"plan-icon dental"});case"vision":return t.jsx(o.Vvo,{className:"plan-icon vision"});default:return t.jsx(o.Q5u,{className:"plan-icon"})}};return f?(0,t.jsxs)(t.Fragment,{children:[t.jsx(l.Z,{}),(0,t.jsxs)("div",{className:"company-detail-page",children:[(0,t.jsxs)("div",{className:"page-header",children:[(0,t.jsxs)("button",{onClick:j,className:"back-button",children:[t.jsx(o.Tsu,{size:20}),"Back"]}),t.jsx("button",{className:"edit-group-btn",children:"Edit Group"})]}),(0,t.jsxs)("div",{className:"company-overview",children:[t.jsx("h1",{children:"Company Overview"}),(0,t.jsxs)("div",{className:"overview-grid",children:[(0,t.jsxs)("div",{className:"overview-item",children:[t.jsx("span",{className:"label",children:"Industry"}),t.jsx("span",{className:"value",children:f.industry})]}),(0,t.jsxs)("div",{className:"overview-item",children:[t.jsx("span",{className:"label",children:"Employees"}),t.jsx("span",{className:"value",children:v.totalEmployees})]}),(0,t.jsxs)("div",{className:"overview-item",children:[t.jsx("span",{className:"label",children:"Location"}),t.jsx("span",{className:"value",children:f.location||"San Francisco, CA"})]}),(0,t.jsxs)("div",{className:"overview-item",children:[t.jsx("span",{className:"label",children:"Revenue"}),t.jsx("span",{className:"value",children:"$50M - $100M"})]})]})]}),(0,t.jsxs)("div",{className:"current-plans",children:[t.jsx("h2",{children:"Current Plans"}),t.jsx("div",{className:"plans-grid",children:[{id:"1",name:"Blue Cross Blue Shield",type:"medical",cost:"$450/mo"},{id:"2",name:"Delta Dental",type:"dental",cost:"$85/mo"},{id:"3",name:"VSP",type:"vision",cost:"$25/mo"}].map(e=>(0,t.jsxs)("div",{className:"plan-card",children:[y(e.type),(0,t.jsxs)("div",{className:"plan-info",children:[t.jsx("h3",{children:e.name}),t.jsx("span",{className:"plan-cost",children:e.cost})]})]},e.id))})]}),(0,t.jsxs)("div",{className:"statistics-grid",children:[(0,t.jsxs)("div",{className:"stat-card",children:[t.jsx("div",{className:"stat-number",children:v.totalEmployees}),t.jsx("div",{className:"stat-label",children:"Total Employees"})]}),(0,t.jsxs)("div",{className:"stat-card",children:[t.jsx("div",{className:"stat-number",children:v.avgAge}),t.jsx("div",{className:"stat-label",children:"Avg Age"})]}),(0,t.jsxs)("div",{className:"stat-card",children:[t.jsx("div",{className:"stat-number",children:v.highRisk}),t.jsx("div",{className:"stat-label",children:"High Risk"})]}),(0,t.jsxs)("div",{className:"stat-card",children:[t.jsx("div",{className:"stat-number",children:v.withDependents}),t.jsx("div",{className:"stat-label",children:"With Dependents"})]})]}),(0,t.jsxs)("div",{className:"feature-cards",children:[(0,t.jsxs)("div",{className:"feature-card",children:[t.jsx("div",{className:"feature-icon",children:t.jsx(o.r7I,{size:24})}),(0,t.jsxs)("div",{className:"feature-content",children:[t.jsx("h3",{children:"Enrichment"}),t.jsx("p",{children:"Coming Soon"})]})]}),(0,t.jsxs)("div",{className:"feature-card",children:[t.jsx("div",{className:"feature-icon",children:t.jsx(o.r7I,{size:24})}),(0,t.jsxs)("div",{className:"feature-content",children:[t.jsx("h3",{children:"Optimization"}),t.jsx("p",{children:"Coming Soon"})]})]}),(0,t.jsxs)("div",{className:"feature-card",children:[t.jsx("div",{className:"feature-icon",children:t.jsx(o.r7I,{size:24})}),(0,t.jsxs)("div",{className:"feature-content",children:[t.jsx("h3",{children:"Insights"}),t.jsx("p",{children:"Coming Soon"})]})]}),(0,t.jsxs)("div",{className:"feature-card clickable",onClick:()=>x("employees"),children:[t.jsx("div",{className:"feature-icon",children:t.jsx(o.Otr,{size:24})}),(0,t.jsxs)("div",{className:"feature-content",children:[t.jsx("h3",{children:"Employees"}),t.jsx("p",{children:"View employee list"})]})]})]}),(0,t.jsxs)("div",{className:"action-buttons-section",children:[(0,t.jsxs)("button",{className:"manage-plans-btn",onClick:()=>{e.push(`/ai-enroller/manage-groups/${s}/manage-plans`)},children:[t.jsx(o.Vvo,{size:20}),"Manage Plans"]}),(0,t.jsxs)("button",{className:"add-plans-btn",onClick:()=>{e.push(`/ai-enroller/manage-groups/${s}/add-plans`)},children:[t.jsx(o.r7I,{size:20}),"Add Plans"]})]}),"employees"===m&&(0,t.jsxs)("div",{className:"employees-section",children:[(0,t.jsxs)("div",{className:"employees-header",children:[(0,t.jsxs)("h2",{children:["Employees (",c.length,")"]}),t.jsx("button",{className:"close-employees",onClick:()=>x("overview"),children:"\xd7"})]}),t.jsx("div",{className:"employees-list",children:p?t.jsx("div",{className:"loading-spinner"}):0===c.length?t.jsx("p",{children:"No employees found"}):c.map(e=>(0,t.jsxs)("div",{className:"employee-card",children:[(0,t.jsxs)("div",{className:"employee-info",children:[t.jsx("h4",{children:e.name}),t.jsx("p",{children:e.email}),t.jsx("span",{className:"employee-role",children:e.role})]}),t.jsx("div",{className:`employee-status ${e.isActivated?"active":"inactive"}`,children:e.isActivated?"Active":"Inactive"})]},e._id))})]})]})]}):t.jsx("div",{className:"company-detail-page",children:(0,t.jsxs)("div",{className:"loading-container",children:[t.jsx("p",{children:"Company not found"}),(0,t.jsxs)("button",{onClick:j,className:"back-button",children:[t.jsx(o.Tsu,{size:20}),"Back to Companies"]})]})})}},6079:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>t});let t=(0,s(68570).createProxy)(String.raw`C:\beno\project_dev\qharmony-frontend-v1\src\app\ai-enroller\layout.tsx#default`)},36397:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>t});let t=(0,s(68570).createProxy)(String.raw`C:\beno\project_dev\qharmony-frontend-v1\src\app\ai-enroller\manage-groups\[companyId]\page.tsx#default`)},54658:()=>{},29566:()=>{},23824:()=>{}};var r=require("../../../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[8948,1183,6621,9066,1999,8492,3253,576,6305],()=>s(16376));module.exports=t})();