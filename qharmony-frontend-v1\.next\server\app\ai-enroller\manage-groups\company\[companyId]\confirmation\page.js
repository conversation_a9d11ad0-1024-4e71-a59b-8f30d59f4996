(()=>{var e={};e.id=8560,e.ids=[8560],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},27790:e=>{"use strict";e.exports=require("assert")},84770:e=>{"use strict";e.exports=require("crypto")},17702:e=>{"use strict";e.exports=require("events")},92048:e=>{"use strict";e.exports=require("fs")},32615:e=>{"use strict";e.exports=require("http")},35240:e=>{"use strict";e.exports=require("https")},19801:e=>{"use strict";e.exports=require("os")},55315:e=>{"use strict";e.exports=require("path")},76162:e=>{"use strict";e.exports=require("stream")},74026:e=>{"use strict";e.exports=require("string_decoder")},74175:e=>{"use strict";e.exports=require("tty")},17360:e=>{"use strict";e.exports=require("url")},21764:e=>{"use strict";e.exports=require("util")},71568:e=>{"use strict";e.exports=require("zlib")},98061:e=>{"use strict";e.exports=require("node:assert")},92761:e=>{"use strict";e.exports=require("node:async_hooks")},72254:e=>{"use strict";e.exports=require("node:buffer")},40027:e=>{"use strict";e.exports=require("node:console")},6005:e=>{"use strict";e.exports=require("node:crypto")},65714:e=>{"use strict";e.exports=require("node:diagnostics_channel")},15673:e=>{"use strict";e.exports=require("node:events")},88849:e=>{"use strict";e.exports=require("node:http")},42725:e=>{"use strict";e.exports=require("node:http2")},87503:e=>{"use strict";e.exports=require("node:net")},38846:e=>{"use strict";e.exports=require("node:perf_hooks")},39630:e=>{"use strict";e.exports=require("node:querystring")},84492:e=>{"use strict";e.exports=require("node:stream")},31764:e=>{"use strict";e.exports=require("node:tls")},41041:e=>{"use strict";e.exports=require("node:url")},47261:e=>{"use strict";e.exports=require("node:util")},93746:e=>{"use strict";e.exports=require("node:util/types")},24086:e=>{"use strict";e.exports=require("node:worker_threads")},65628:e=>{"use strict";e.exports=require("node:zlib")},70027:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>i.a,__next_app__:()=>u,originalPathname:()=>m,pages:()=>d,routeModule:()=>p,tree:()=>c}),s(41756),s(6079),s(33709),s(35866);var r=s(23191),a=s(88716),n=s(37922),i=s.n(n),o=s(95231),l={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);s.d(t,l);let c=["",{children:["ai-enroller",{children:["manage-groups",{children:["company",{children:["[companyId]",{children:["confirmation",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,41756)),"C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\manage-groups\\company\\[companyId]\\confirmation\\page.tsx"]}]},{}]},{}]},{}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,6079)),"C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,33709)),"C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,35866,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],d=["C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\manage-groups\\company\\[companyId]\\confirmation\\page.tsx"],m="/ai-enroller/manage-groups/company/[companyId]/confirmation/page",u={require:s,loadChunk:()=>Promise.resolve()},p=new r.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/ai-enroller/manage-groups/company/[companyId]/confirmation/page",pathname:"/ai-enroller/manage-groups/company/[companyId]/confirmation",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},38291:(e,t,s)=>{Promise.resolve().then(s.bind(s,48676))},6283:(e,t,s)=>{"use strict";s.d(t,{Z:()=>c});var r=s(96830),a=s(5028),n=s(5283),i=s(14750);let o=(0,s(71685).Z)("MuiBox",["root"]),l=(0,n.Z)(),c=(0,r.default)({themeId:i.Z,defaultTheme:l,defaultClassName:o.root,generateClassName:a.Z.generate})},98139:(e,t,s)=>{"use strict";s.d(t,{Z:()=>C});var r=s(17577),a=s(41135),n=s(88634),i=s(8106),o=s(91703),l=s(13643),c=s(2791),d=s(54641),m=s(40955),u=s(71685),p=s(97898);function x(e){return(0,p.ZP)("MuiCircularProgress",e)}(0,u.Z)("MuiCircularProgress",["root","determinate","indeterminate","colorPrimary","colorSecondary","svg","circle","circleDeterminate","circleIndeterminate","circleDisableShrink"]);var g=s(10326);let h=(0,i.F4)`
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
`,y=(0,i.F4)`
  0% {
    stroke-dasharray: 1px, 200px;
    stroke-dashoffset: 0;
  }

  50% {
    stroke-dasharray: 100px, 200px;
    stroke-dashoffset: -15px;
  }

  100% {
    stroke-dasharray: 1px, 200px;
    stroke-dashoffset: -126px;
  }
`,f="string"!=typeof h?(0,i.iv)`
        animation: ${h} 1.4s linear infinite;
      `:null,b="string"!=typeof y?(0,i.iv)`
        animation: ${y} 1.4s ease-in-out infinite;
      `:null,v=e=>{let{classes:t,variant:s,color:r,disableShrink:a}=e,i={root:["root",s,`color${(0,d.Z)(r)}`],svg:["svg"],circle:["circle",`circle${(0,d.Z)(s)}`,a&&"circleDisableShrink"]};return(0,n.Z)(i,x,t)},N=(0,o.default)("span",{name:"MuiCircularProgress",slot:"Root",overridesResolver:(e,t)=>{let{ownerState:s}=e;return[t.root,t[s.variant],t[`color${(0,d.Z)(s.color)}`]]}})((0,l.Z)(({theme:e})=>({display:"inline-block",variants:[{props:{variant:"determinate"},style:{transition:e.transitions.create("transform")}},{props:{variant:"indeterminate"},style:f||{animation:`${h} 1.4s linear infinite`}},...Object.entries(e.palette).filter((0,m.Z)()).map(([t])=>({props:{color:t},style:{color:(e.vars||e).palette[t].main}}))]}))),j=(0,o.default)("svg",{name:"MuiCircularProgress",slot:"Svg",overridesResolver:(e,t)=>t.svg})({display:"block"}),w=(0,o.default)("circle",{name:"MuiCircularProgress",slot:"Circle",overridesResolver:(e,t)=>{let{ownerState:s}=e;return[t.circle,t[`circle${(0,d.Z)(s.variant)}`],s.disableShrink&&t.circleDisableShrink]}})((0,l.Z)(({theme:e})=>({stroke:"currentColor",variants:[{props:{variant:"determinate"},style:{transition:e.transitions.create("stroke-dashoffset")}},{props:{variant:"indeterminate"},style:{strokeDasharray:"80px, 200px",strokeDashoffset:0}},{props:({ownerState:e})=>"indeterminate"===e.variant&&!e.disableShrink,style:b||{animation:`${y} 1.4s ease-in-out infinite`}}]}))),C=r.forwardRef(function(e,t){let s=(0,c.i)({props:e,name:"MuiCircularProgress"}),{className:r,color:n="primary",disableShrink:i=!1,size:o=40,style:l,thickness:d=3.6,value:m=0,variant:u="indeterminate",...p}=s,x={...s,color:n,disableShrink:i,size:o,thickness:d,value:m,variant:u},h=v(x),y={},f={},b={};if("determinate"===u){let e=2*Math.PI*((44-d)/2);y.strokeDasharray=e.toFixed(3),b["aria-valuenow"]=Math.round(m),y.strokeDashoffset=`${((100-m)/100*e).toFixed(3)}px`,f.transform="rotate(-90deg)"}return(0,g.jsx)(N,{className:(0,a.Z)(h.root,r),style:{width:o,height:o,...f,...l},ownerState:x,ref:t,role:"progressbar",...b,...p,children:(0,g.jsx)(j,{className:h.svg,ownerState:x,viewBox:"22 22 44 44",children:(0,g.jsx)(w,{className:h.circle,style:y,ownerState:x,cx:44,cy:44,r:(44-d)/2,fill:"none",strokeWidth:d})})})})},2791:(e,t,s)=>{"use strict";s.d(t,{i:()=>a}),s(17577);var r=s(51387);function a(e){return(0,r.i)(e)}s(10326)},54641:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});let r=s(96005).Z},40955:(e,t,s)=>{"use strict";function r(e=[]){return([,t])=>t&&function(e,t=[]){if("string"!=typeof e.main)return!1;for(let s of t)if(!e.hasOwnProperty(s)||"string"!=typeof e[s])return!1;return!0}(t,e)}s.d(t,{Z:()=>r})},13643:(e,t,s)=>{"use strict";s.d(t,{Z:()=>n});var r=s(15966);let a={theme:void 0},n=function(e){let t,s;return function(n){let i=t;return(void 0===i||n.theme!==s)&&(a.theme=n.theme,t=i=(0,r.Z)(e(a)),s=n.theme),i}}},48676:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>c});var r=s(10326),a=s(17577),n=s(35047),i=s(38492),o=s(43933),l=s(89009);function c(){let e=(0,n.useRouter)(),t=(0,n.useParams)(),s=(0,n.useSearchParams)(),c=t.companyId,d=s.get("assignments")?.split(",")||[],[m,u]=(0,a.useState)(null),[p,x]=(0,a.useState)([]),[g,h]=(0,a.useState)(!0);return((0,a.useEffect)(()=>{let e=async()=>{try{h(!0);let e=(0,l.bR)(),t=(0,l.n5)(),s=await fetch(`${e}/api/pre-enrollment/company-benefits-settings/company/${c}`,{headers:{"Content-Type":"application/json","user-id":t}});if(s.ok){let e=await s.json();u({_id:c,companyName:e.companyName||"Company Name",employeeCount:e.employeeCount||250})}else console.error("Failed to fetch company details:",s.status),u({_id:c,companyName:"Company Name",employeeCount:250});console.log("Fetching plan assignments for company:",c),console.log("Assignment IDs from URL:",d);let r=await (0,o.fH)(c);if(console.log("All plan assignments response:",r),!r.success||!r.data||!r.data.assignments||0===r.data.assignments.length){console.log("No plan assignments found, using fallback data"),x([]);return}let a=r.data.assignments.filter(e=>d.includes(e._id));console.log("Confirmed assignments:",a);let n=await Promise.all(a.map(async s=>{try{let r=await fetch(`${e}/api/pre-enrollment/plans/${s.planId}`,{headers:{"Content-Type":"application/json","user-id":t}});if(!r.ok)return console.error("Failed to fetch plan details for planId:",s.planId),s;{let e=await r.json();return{...s,planName:e.planName,carrier:e.carrier,planCode:e.planCode,coverageType:e.coverageType,coverageSubtype:e.coverageSubtype}}}catch(e){return console.error("Error fetching plan details:",e),s}}));x(n)}catch(e){console.error("Error fetching data:",e),u({_id:c,companyName:"Company Name",employeeCount:250}),x([])}finally{h(!1)}};c&&d.length>0?(console.log("Starting fetch with companyId:",c,"assignmentIds:",d),e()):(console.log("Skipping fetch - companyId:",c,"assignmentIds:",d),h(!1),u({_id:c,companyName:"Company Name",employeeCount:250}),x([]))},[c,d.join(",")]),g)?r.jsx("div",{className:"min-h-screen bg-white flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center",children:[r.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-black mx-auto"}),r.jsx("p",{className:"mt-4 text-gray-600",children:"Loading confirmation..."})]})}):(0,r.jsxs)("div",{className:"min-h-screen bg-white",children:[r.jsx("div",{className:"bg-white border-b border-gray-200 px-6 py-4",children:r.jsx("div",{className:"max-w-7xl mx-auto",children:(0,r.jsxs)("nav",{className:"flex items-center space-x-3",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[r.jsx("div",{className:"w-5 h-5 rounded-full bg-green-500 flex items-center justify-center",children:r.jsx("span",{className:"text-white text-xs",children:"✓"})}),r.jsx("span",{className:"text-green-600 text-sm",children:"Home"})]}),r.jsx("span",{className:"text-gray-400",children:"›"}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[r.jsx("div",{className:"w-5 h-5 rounded-full bg-green-500 flex items-center justify-center",children:r.jsx("span",{className:"text-white text-xs",children:"✓"})}),r.jsx("span",{className:"text-green-600 text-sm",children:"Select Company"})]}),r.jsx("span",{className:"text-gray-400",children:"›"}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[r.jsx("div",{className:"w-5 h-5 rounded-full bg-green-500 flex items-center justify-center",children:r.jsx("span",{className:"text-white text-xs",children:"✓"})}),r.jsx("span",{className:"text-green-600 text-sm",children:"View Plans"})]}),r.jsx("span",{className:"text-gray-400",children:"›"}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[r.jsx("div",{className:"w-5 h-5 rounded-full bg-green-500 flex items-center justify-center",children:r.jsx("span",{className:"text-white text-xs",children:"✓"})}),r.jsx("span",{className:"text-green-600 text-sm",children:"Contributions"})]}),r.jsx("span",{className:"text-gray-400",children:"›"}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[r.jsx("div",{className:"w-5 h-5 rounded-full bg-green-500 flex items-center justify-center",children:r.jsx("span",{className:"text-white text-xs",children:"✓"})}),r.jsx("span",{className:"text-green-600 text-sm",children:"Review"})]}),r.jsx("span",{className:"text-gray-400",children:"›"}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[r.jsx("div",{className:"w-5 h-5 rounded-full bg-green-500 flex items-center justify-center",children:r.jsx("span",{className:"text-white text-xs",children:"✓"})}),r.jsx("span",{className:"text-green-600 font-medium text-sm",children:"Confirmation"})]})]})})}),(0,r.jsxs)("div",{className:"max-w-4xl mx-auto px-6 py-12 bg-white text-center",children:[(0,r.jsxs)("div",{className:"mb-8",children:[r.jsx("div",{className:"w-20 h-20 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-6",children:r.jsx(i.PjL,{className:"w-12 h-12 text-white"})}),r.jsx("h1",{className:"text-3xl font-bold text-gray-900 mb-4",children:"Configuration Complete!"}),(0,r.jsxs)("p",{className:"text-lg text-gray-600",children:["All benefit plans have been successfully configured for ",m?.companyName,"."]})]}),r.jsx("div",{className:"bg-green-50 border border-green-200 rounded-xl p-4 mb-8 text-left",children:(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[r.jsx(i.PjL,{className:"text-green-600 w-5 h-5"}),r.jsx("span",{className:"text-green-800 font-medium",children:"Success!"}),r.jsx("span",{className:"text-green-700",children:"Your benefit plan configurations have been saved and are now active."})]})}),(0,r.jsxs)("div",{className:"bg-white border border-gray-200 rounded-xl p-6 mb-8 text-left",children:[(0,r.jsxs)("div",{className:"flex items-center gap-3 mb-6",children:[r.jsx("div",{className:"w-8 h-8 bg-gray-100 rounded-xl flex items-center justify-center",children:r.jsx("span",{className:"text-gray-600 text-lg",children:"\uD83D\uDCCB"})}),(0,r.jsxs)("div",{children:[r.jsx("h2",{className:"text-xl font-semibold text-gray-900",children:"Configuration Summary"}),(0,r.jsxs)("p",{className:"text-sm text-gray-600",children:[p.length," plan assignment",1!==p.length?"s":""," configured for ",m?.companyName]})]})]}),r.jsx("div",{className:"space-y-4",children:p.length>0?p.map(e=>(0,r.jsxs)("div",{className:"flex items-center justify-between p-4 bg-gray-50 rounded-xl",children:[(0,r.jsxs)("div",{children:[r.jsx("h3",{className:"font-semibold text-gray-900",children:e.planName||`Plan ID: ${e.planId}`}),(0,r.jsxs)("p",{className:"text-sm text-gray-600",children:[e.coverageType||e.type||"Coverage Type",e.coverageSubtype&&` - ${e.coverageSubtype}`]}),(0,r.jsxs)("p",{className:"text-xs text-gray-500",children:[e.carrier&&`Carrier: ${e.carrier}`,e.planCode&&` | Code: ${e.planCode}`]}),e.effectiveDate&&e.endDate&&(0,r.jsxs)("p",{className:"text-xs text-gray-500",children:["Effective: ",new Date(e.effectiveDate).toLocaleDateString()," - ",new Date(e.endDate).toLocaleDateString()]}),(0,r.jsxs)("p",{className:"text-xs text-blue-600 font-medium",children:["Status: ",e.status||"Active"]})]}),r.jsx("div",{className:"w-6 h-6 bg-green-500 rounded-full flex items-center justify-center",children:r.jsx("span",{className:"text-white text-xs",children:"✓"})})]},e._id)):(0,r.jsxs)("div",{className:"text-center py-8 text-gray-500",children:[r.jsx("p",{children:"No plan assignments found."}),(0,r.jsxs)("p",{className:"text-sm",children:["Assignment IDs: ",d.join(", ")]})]})})]}),(0,r.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-xl p-6 mb-8 text-left",children:[r.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"What's Next?"}),(0,r.jsxs)("ul",{className:"space-y-2 text-sm text-gray-700",children:[(0,r.jsxs)("li",{className:"flex items-start gap-2",children:[r.jsx("span",{className:"text-blue-600 mt-1",children:"•"}),r.jsx("span",{children:"Employee communications will be generated based on these configurations"})]}),(0,r.jsxs)("li",{className:"flex items-start gap-2",children:[r.jsx("span",{className:"text-blue-600 mt-1",children:"•"}),r.jsx("span",{children:"Plan documents will be updated with the new contribution rates"})]}),(0,r.jsxs)("li",{className:"flex items-start gap-2",children:[r.jsx("span",{className:"text-blue-600 mt-1",children:"•"}),r.jsx("span",{children:"Enrollment systems will be updated for the next plan year"})]})]})]}),(0,r.jsxs)("div",{className:"flex gap-4 justify-center",children:[(0,r.jsxs)("button",{onClick:()=>{alert("Configuration summary downloaded successfully!")},className:"px-6 py-3 border border-gray-300 text-gray-700 rounded-xl hover:bg-gray-50 transition-colors flex items-center gap-2",children:[r.jsx(i.yFZ,{className:"w-5 h-5"}),"Download Summary"]}),(0,r.jsxs)("button",{onClick:()=>{e.push("/ai-enroller")},className:"px-6 py-3 bg-black text-white rounded-xl hover:bg-gray-800 transition-colors flex items-center gap-2",children:[r.jsx(i.VRM,{className:"w-5 h-5"}),"Return to Dashboard"]})]})]})]})}},43058:(e,t,s)=>{"use strict";s.d(t,{Z:()=>m});var r=s(10326),a=s(17577),n=s(22758),i=s(35047),o=s(31870);s(32049),s(94638);var l=s(98139),c=s(6283);let d=()=>/Mobi|Android/i.test(navigator.userAgent),m=({children:e})=>{let{user:t,loading:s}=(0,n.a)(),m=(0,i.useRouter)(),u=(0,i.usePathname)(),p=(0,o.T)(),[x,g]=(0,a.useState)(!1),h=(0,o.C)(e=>e.user.userProfile);return((0,a.useEffect)(()=>{},[p,h.name]),(0,a.useEffect)(()=>{console.log("ProtectedRoute useEffect triggered"),console.log("Current user: ",t),console.log("Loading state: ",s),console.log("Current user details: ",h),s||t||(console.log("User not authenticated, redirecting to home"),g(!1),m.push("/")),!s&&h.companyId&&""===h.companyId&&(console.log("Waiting to retrieve company details"),g(!1)),!s&&h.companyId&&""!==h.companyId&&(console.log("User found, rendering children"),g(!0)),d()&&!u.startsWith("/mobile")&&(console.log(`Redirecting to mobile version of ${u}`),m.push(`/mobile${u}`))},[t,s,h,m,u]),x)?t?r.jsx(r.Fragment,{children:e}):null:r.jsx(c.Z,{sx:{display:"flex",justifyContent:"center",alignItems:"center",height:"100vh",bgcolor:"#f6f8fc"},children:r.jsx(l.Z,{})})}},94638:(e,t,s)=>{"use strict";s.d(t,{G9:()=>y,JZ:()=>f,M_:()=>x,N:()=>c,Nq:()=>p,TQ:()=>m,Ur:()=>o,aE:()=>d,aK:()=>N,dA:()=>l,gt:()=>b,mb:()=>h,qB:()=>v,yu:()=>u,zX:()=>g});var r=s(53148),a=s(39352),n=s(25748),i=s(32049);function o(e){switch(e){case"Our Culture & Workplace":return"Work Policies";case"Protecting Your Income":return"Income Security";default:return e}}function l(e){switch(e){case"On-site Amenities":return"Company Handbook";case"Protecting Your Income":return"Income Security";case"Got married/divorced":return"Marriage or Divorce";case"Had a baby or adopted a baby":return"New Baby or Adoption";case"Lost your insurance":return"Loss of Insurance";default:return e}}async function c(e,t){let s=await (0,r.A_)("/benefits/benefit-types",{companyId:t});return console.log("COMPANY BENEFIT TYPES RESPONSE: ",s.benefitTypes),e((0,a.x7)(s.benefitTypes)),s.benefitTypes}async function d(e,t){let s=await (0,r.A_)("/benefits/all-benefits",{companyId:t});console.log("COMPANY BENEFIT TYPES WITH BENEFITS RESPONSE: ",s),e((0,n.US)(s.benefitsPerType))}async function m(e){let t=await (0,r.A_)("/admin/all-employees");return console.log("COMPANY TEAM MEMBERS RESPONSE: ",t),e((0,a.Vv)(t.employees)),t.employees}async function u(e,t){return console.log("ADDING USERS: ",t),await (0,r.j0)("/admin/add/employees",{employeeList:t})}async function p(e,t,s){try{console.log("\uD83D\uDD0D Debug: User being updated:",t);let e={employeeId:t,updatedDetails:{name:s.name,email:s.email,details:{phoneNumber:s.phoneNumber||"",department:s.department||"",title:s.title||"",role:s.title||""}}};return s.dateOfBirth&&(e.updatedDetails.details.dateOfBirth=s.dateOfBirth),s.hireDate&&(e.updatedDetails.details.hireDate=s.hireDate),s.annualSalary&&(e.updatedDetails.details.annualSalary=s.annualSalary),s.employeeClassType&&(e.updatedDetails.details.employeeClassType=s.employeeClassType),s.workSchedule&&(e.updatedDetails.details.workSchedule=s.workSchedule),s.ssn&&(e.updatedDetails.details.ssn=s.ssn),s.employeeId&&(e.updatedDetails.details.employeeId=s.employeeId),s.workLocation&&(e.updatedDetails.details.workLocation=s.workLocation),s.address&&(e.updatedDetails.details.address=s.address),s.mailingAddress&&(e.updatedDetails.details.mailingAddress=s.mailingAddress),s.emergencyContact&&(e.updatedDetails.details.emergencyContact=s.emergencyContact),e.updatedDetails.details.dependents=s.dependents||[],console.log("Middleware - dependents being sent:",e.updatedDetails.details.dependents),console.log("Sending PUT request with cleaned data:",e),await (0,r.GH)("/admin/update/employee",e)}catch(e){throw console.error("Error in updateUser middleware:",e),e}}async function x(e,t){let s=await (0,r.A_)("/employee",{"user-id":t});return e((0,i.$l)({name:s.currentUser.name,email:s.currentUser.email,companyId:s.currentUser.companyId,role:s.currentUser.role,isAdmin:s.currentUser.isAdmin,isBroker:s.currentUser.isBroker,details:s.currentUser.details})),s}async function g(e,t,s){let a=await (0,r.j0)("/admin/onboard",{company:{name:t.name,adminEmail:t.adminEmail,adminRole:t.adminRole,companySize:t.companySize,industry:t.industry,location:t.location,website:t.website,howHeard:t.howHeard,brokerId:t.brokerId,brokerageId:t.brokerageId,isBrokerage:t.isBrokerage,isActivated:t.isActivated,referralSource:t.referralSource,details:{logo:""}},user:{email:s.email,name:s.name,role:s.role,isAdmin:s.isAdmin,isBroker:s.isBroker,isActivated:s.isActivated}}),n=a.data.userId,i=a.data.companyId;return localStorage.setItem("userid1",n),localStorage.setItem("companyId1",i),a}async function h(e,t){return console.log("SENDING LOGIN LINK TO EMPLOYEE: ",e,t),await (0,r.j0)("/admin/send-user-login-link",{userId:e,companyId:t})}async function y(e,t,s,a){let n=await (0,r.j0)("/admin/add/employer",{brokerId:e,companyName:t,companyAdminEmail:s,companyAdminName:a});return console.log("BROKER ADDS COMPANY RESPONSE: ",n),n}async function f(e,t){return 200===(await (0,r.j0)("/employee/offboard/",{userId:e,companyId:t})).status}async function b(e,t){return await (0,r.j0)("/employee/enable/",{userId:e,companyId:t})}async function v(e,t){try{let t=await (0,r.A_)("/admin/all-companies");console.log("CLIENT COMPANIES UNDER BROKER: ",t);let s=t.companies||[];try{let e=await (0,r.A_)("/employee/company-details");console.log("BROKER'S OWN COMPANY: ",e),e.company&&e.company.isBrokerage&&!s.some(t=>t._id===e.company._id)&&(s.unshift(e.company),console.log("Added broker's own company to the list"))}catch(e){console.log("Could not fetch broker's own company (this is normal if user is not a broker):",e)}return console.log("ALL COMPANIES (CLIENT + OWN): ",s),e((0,i.Ym)(s)),{...t,companies:s}}catch(t){return console.error("Error fetching companies:",t),e((0,i.Ym)([])),{companies:[]}}}async function N(e){let t=await (0,r.A_)("/employee/company-details");return e((0,a.sy)(t.company)),t.status}},31870:(e,t,s)=>{"use strict";s.d(t,{C:()=>n,T:()=>a});var r=s(25842);let a=()=>(0,r.I0)(),n=r.v9},41756:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(68570).createProxy)(String.raw`C:\beno\project_dev\qharmony-frontend-v1\src\app\ai-enroller\manage-groups\company\[companyId]\confirmation\page.tsx#default`)},73881:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});var r=s(66621);let a=e=>[{type:"image/x-icon",sizes:"16x16",url:(0,r.fillMetadataSegment)(".",e.params,"favicon.ico")+""}]}};var t=require("../../../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[8948,1183,6621,8492,576,9902],()=>s(70027));module.exports=r})();