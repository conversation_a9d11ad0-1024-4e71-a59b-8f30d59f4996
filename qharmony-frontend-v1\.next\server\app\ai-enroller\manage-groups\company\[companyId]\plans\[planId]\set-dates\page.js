(()=>{var e={};e.id=2948,e.ids=[2948],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},27790:e=>{"use strict";e.exports=require("assert")},84770:e=>{"use strict";e.exports=require("crypto")},17702:e=>{"use strict";e.exports=require("events")},92048:e=>{"use strict";e.exports=require("fs")},32615:e=>{"use strict";e.exports=require("http")},35240:e=>{"use strict";e.exports=require("https")},19801:e=>{"use strict";e.exports=require("os")},55315:e=>{"use strict";e.exports=require("path")},76162:e=>{"use strict";e.exports=require("stream")},74026:e=>{"use strict";e.exports=require("string_decoder")},74175:e=>{"use strict";e.exports=require("tty")},17360:e=>{"use strict";e.exports=require("url")},21764:e=>{"use strict";e.exports=require("util")},71568:e=>{"use strict";e.exports=require("zlib")},98061:e=>{"use strict";e.exports=require("node:assert")},92761:e=>{"use strict";e.exports=require("node:async_hooks")},72254:e=>{"use strict";e.exports=require("node:buffer")},40027:e=>{"use strict";e.exports=require("node:console")},6005:e=>{"use strict";e.exports=require("node:crypto")},65714:e=>{"use strict";e.exports=require("node:diagnostics_channel")},15673:e=>{"use strict";e.exports=require("node:events")},88849:e=>{"use strict";e.exports=require("node:http")},42725:e=>{"use strict";e.exports=require("node:http2")},87503:e=>{"use strict";e.exports=require("node:net")},38846:e=>{"use strict";e.exports=require("node:perf_hooks")},39630:e=>{"use strict";e.exports=require("node:querystring")},84492:e=>{"use strict";e.exports=require("node:stream")},31764:e=>{"use strict";e.exports=require("node:tls")},41041:e=>{"use strict";e.exports=require("node:url")},47261:e=>{"use strict";e.exports=require("node:util")},93746:e=>{"use strict";e.exports=require("node:util/types")},24086:e=>{"use strict";e.exports=require("node:worker_threads")},65628:e=>{"use strict";e.exports=require("node:zlib")},9332:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>i.a,__next_app__:()=>m,originalPathname:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>d}),t(22729),t(6079),t(33709),t(35866);var s=t(23191),a=t(88716),n=t(37922),i=t.n(n),o=t(95231),l={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);t.d(r,l);let d=["",{children:["ai-enroller",{children:["manage-groups",{children:["company",{children:["[companyId]",{children:["plans",{children:["[planId]",{children:["set-dates",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,22729)),"C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\manage-groups\\company\\[companyId]\\plans\\[planId]\\set-dates\\page.tsx"]}]},{}]},{}]},{}]},{}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,6079)),"C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,33709)),"C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,35866,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],c=["C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\manage-groups\\company\\[companyId]\\plans\\[planId]\\set-dates\\page.tsx"],u="/ai-enroller/manage-groups/company/[companyId]/plans/[planId]/set-dates/page",m={require:t,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/ai-enroller/manage-groups/company/[companyId]/plans/[planId]/set-dates/page",pathname:"/ai-enroller/manage-groups/company/[companyId]/plans/[planId]/set-dates",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},40853:(e,r,t)=>{Promise.resolve().then(t.bind(t,78021))},54053:(e,r,t)=>{Promise.resolve().then(t.bind(t,49237))},6283:(e,r,t)=>{"use strict";t.d(r,{Z:()=>d});var s=t(96830),a=t(5028),n=t(5283),i=t(14750);let o=(0,t(71685).Z)("MuiBox",["root"]),l=(0,n.Z)(),d=(0,s.default)({themeId:i.Z,defaultTheme:l,defaultClassName:o.root,generateClassName:a.Z.generate})},98139:(e,r,t)=>{"use strict";t.d(r,{Z:()=>I});var s=t(17577),a=t(41135),n=t(88634),i=t(8106),o=t(91703),l=t(13643),d=t(2791),c=t(54641),u=t(40955),m=t(71685),p=t(97898);function x(e){return(0,p.ZP)("MuiCircularProgress",e)}(0,m.Z)("MuiCircularProgress",["root","determinate","indeterminate","colorPrimary","colorSecondary","svg","circle","circleDeterminate","circleIndeterminate","circleDisableShrink"]);var g=t(10326);let h=(0,i.F4)`
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
`,y=(0,i.F4)`
  0% {
    stroke-dasharray: 1px, 200px;
    stroke-dashoffset: 0;
  }

  50% {
    stroke-dasharray: 100px, 200px;
    stroke-dashoffset: -15px;
  }

  100% {
    stroke-dasharray: 1px, 200px;
    stroke-dashoffset: -126px;
  }
`,f="string"!=typeof h?(0,i.iv)`
        animation: ${h} 1.4s linear infinite;
      `:null,b="string"!=typeof y?(0,i.iv)`
        animation: ${y} 1.4s ease-in-out infinite;
      `:null,v=e=>{let{classes:r,variant:t,color:s,disableShrink:a}=e,i={root:["root",t,`color${(0,c.Z)(s)}`],svg:["svg"],circle:["circle",`circle${(0,c.Z)(t)}`,a&&"circleDisableShrink"]};return(0,n.Z)(i,x,r)},j=(0,o.default)("span",{name:"MuiCircularProgress",slot:"Root",overridesResolver:(e,r)=>{let{ownerState:t}=e;return[r.root,r[t.variant],r[`color${(0,c.Z)(t.color)}`]]}})((0,l.Z)(({theme:e})=>({display:"inline-block",variants:[{props:{variant:"determinate"},style:{transition:e.transitions.create("transform")}},{props:{variant:"indeterminate"},style:f||{animation:`${h} 1.4s linear infinite`}},...Object.entries(e.palette).filter((0,u.Z)()).map(([r])=>({props:{color:r},style:{color:(e.vars||e).palette[r].main}}))]}))),N=(0,o.default)("svg",{name:"MuiCircularProgress",slot:"Svg",overridesResolver:(e,r)=>r.svg})({display:"block"}),w=(0,o.default)("circle",{name:"MuiCircularProgress",slot:"Circle",overridesResolver:(e,r)=>{let{ownerState:t}=e;return[r.circle,r[`circle${(0,c.Z)(t.variant)}`],t.disableShrink&&r.circleDisableShrink]}})((0,l.Z)(({theme:e})=>({stroke:"currentColor",variants:[{props:{variant:"determinate"},style:{transition:e.transitions.create("stroke-dashoffset")}},{props:{variant:"indeterminate"},style:{strokeDasharray:"80px, 200px",strokeDashoffset:0}},{props:({ownerState:e})=>"indeterminate"===e.variant&&!e.disableShrink,style:b||{animation:`${y} 1.4s ease-in-out infinite`}}]}))),I=s.forwardRef(function(e,r){let t=(0,d.i)({props:e,name:"MuiCircularProgress"}),{className:s,color:n="primary",disableShrink:i=!1,size:o=40,style:l,thickness:c=3.6,value:u=0,variant:m="indeterminate",...p}=t,x={...t,color:n,disableShrink:i,size:o,thickness:c,value:u,variant:m},h=v(x),y={},f={},b={};if("determinate"===m){let e=2*Math.PI*((44-c)/2);y.strokeDasharray=e.toFixed(3),b["aria-valuenow"]=Math.round(u),y.strokeDashoffset=`${((100-u)/100*e).toFixed(3)}px`,f.transform="rotate(-90deg)"}return(0,g.jsx)(j,{className:(0,a.Z)(h.root,s),style:{width:o,height:o,...f,...l},ownerState:x,ref:r,role:"progressbar",...b,...p,children:(0,g.jsx)(N,{className:h.svg,ownerState:x,viewBox:"22 22 44 44",children:(0,g.jsx)(w,{className:h.circle,style:y,ownerState:x,cx:44,cy:44,r:(44-c)/2,fill:"none",strokeWidth:c})})})})},2791:(e,r,t)=>{"use strict";t.d(r,{i:()=>a}),t(17577);var s=t(51387);function a(e){return(0,s.i)(e)}t(10326)},54641:(e,r,t)=>{"use strict";t.d(r,{Z:()=>s});let s=t(96005).Z},40955:(e,r,t)=>{"use strict";function s(e=[]){return([,r])=>r&&function(e,r=[]){if("string"!=typeof e.main)return!1;for(let t of r)if(!e.hasOwnProperty(t)||"string"!=typeof e[t])return!1;return!0}(r,e)}t.d(r,{Z:()=>s})},13643:(e,r,t)=>{"use strict";t.d(r,{Z:()=>n});var s=t(15966);let a={theme:void 0},n=function(e){let r,t;return function(n){let i=r;return(void 0===i||n.theme!==t)&&(a.theme=n.theme,r=i=(0,s.Z)(e(a)),t=n.theme),i}}},78021:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>n});var s=t(10326);t(17577),t(23824),t(54658);var a=t(43058);function n({children:e}){return s.jsx(a.Z,{children:s.jsx("div",{className:"min-h-screen bg-white",style:{backgroundColor:"white",minHeight:"100vh"},children:e})})}},49237:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>o});var s=t(10326),a=t(17577),n=t(35047),i=t(38492);function o(){let e=(0,n.useRouter)(),r=(0,n.useParams)(),t=r.companyId,o=r.planId,[l,d]=(0,a.useState)(!0),[c,u]=(0,a.useState)("0"),[m,p]=(0,a.useState)("30");return(0,s.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[s.jsx("div",{className:"bg-white border-b border-gray-200 px-4 py-3",children:s.jsx("div",{className:"max-w-7xl mx-auto",children:(0,s.jsxs)("nav",{className:"flex items-center space-x-2 text-sm text-gray-500",children:[s.jsx("button",{onClick:()=>e.push("/ai-enroller"),className:"hover:text-gray-700",children:"Home"}),s.jsx("span",{children:"›"}),s.jsx("button",{onClick:()=>e.push("/ai-enroller/manage-groups/select-company"),className:"hover:text-gray-700",children:"Select Company"}),s.jsx("span",{children:"›"}),s.jsx("button",{onClick:()=>e.push(`/ai-enroller/manage-groups/company/${t}/plans`),className:"hover:text-gray-700",children:"View Plans"}),s.jsx("span",{children:"›"}),s.jsx("span",{className:"text-gray-900",children:"Define Contributions"}),s.jsx("span",{children:"›"}),s.jsx("span",{className:"text-blue-600 font-medium",children:"Set Dates"}),s.jsx("span",{children:"›"}),s.jsx("span",{className:"text-gray-400",children:"Review"})]})})}),s.jsx("div",{className:"bg-white border-b border-gray-200 px-4 py-6",children:(0,s.jsxs)("div",{className:"max-w-4xl mx-auto flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[s.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"Set Enrollment & Active Dates"}),s.jsx("p",{className:"text-gray-600",children:"Configure dates for Blue Cross Blue Shield PPO for TechCorp Inc."})]}),(0,s.jsxs)("div",{className:"text-right",children:[s.jsx("div",{className:"text-sm text-gray-500",children:"Step 4 of 5"}),s.jsx("div",{className:"text-xs text-blue-600",children:"Set plan dates"})]})]})}),(0,s.jsxs)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[s.jsx("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4 mb-8",children:(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[s.jsx(i.if7,{className:"w-5 h-5 text-blue-600"}),s.jsx("span",{className:"font-medium text-blue-900",children:"Plan: Blue Cross Blue Shield PPO for TechCorp Inc."})]})}),(0,s.jsxs)("div",{className:"bg-white rounded-lg border border-gray-200 p-6 mb-8",children:[s.jsx("h2",{className:"text-lg font-semibold text-gray-900 mb-4",children:"\uD83D\uDCC5 Date Configuration"}),s.jsx("p",{className:"text-gray-600 mb-6",children:"Set enrollment and plan active dates for this specific plan"}),s.jsx("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6",children:(0,s.jsxs)("label",{className:"flex items-start gap-3 cursor-pointer",children:[s.jsx("input",{type:"checkbox",checked:l,onChange:e=>d(e.target.checked),className:"w-5 h-5 text-blue-600 rounded mt-0.5"}),(0,s.jsxs)("div",{children:[s.jsx("div",{className:"font-medium text-blue-900",children:"Use Global Company Dates"}),s.jsx("div",{className:"text-sm text-blue-700",children:"This plan will use the company-wide enrollment and plan dates"}),s.jsx("div",{className:"flex items-center gap-1 mt-1",children:s.jsx(i.if7,{className:"w-4 h-4 text-blue-600"})})]})]})}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 mb-6",children:[(0,s.jsxs)("div",{children:[s.jsx("h3",{className:"font-medium text-gray-900 mb-2",children:"Global Enrollment Period"}),s.jsx("div",{className:"text-sm text-gray-600",children:"November 1 - November 30, 2024"})]}),(0,s.jsxs)("div",{children:[s.jsx("h3",{className:"font-medium text-gray-900 mb-2",children:"Global Plan Period"}),s.jsx("div",{className:"text-sm text-gray-600",children:"January 1 - December 31, 2025"})]})]})]}),(0,s.jsxs)("div",{className:"bg-white rounded-lg border border-gray-200 p-6 mb-8",children:[s.jsx("h2",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Additional Settings"}),s.jsx("p",{className:"text-gray-600 mb-6",children:"Optional configuration for this plan"}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,s.jsxs)("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Waiting Period (days)"}),s.jsx("input",{type:"number",value:c,onChange:e=>u(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",min:"0",max:"365"})]}),(0,s.jsxs)("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Grace Period (days)"}),s.jsx("input",{type:"number",value:m,onChange:e=>p(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",min:"0",max:"90"})]})]})]}),s.jsx("div",{className:"bg-green-50 border border-green-200 rounded-lg p-4 mb-8",children:(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[s.jsx("div",{className:"w-5 h-5 bg-green-500 rounded-full flex items-center justify-center",children:s.jsx("span",{className:"text-white text-xs",children:"✓"})}),(0,s.jsxs)("div",{children:[s.jsx("div",{className:"font-medium text-green-900",children:"Plan Configuration Complete"}),s.jsx("div",{className:"text-sm text-green-700",children:"You can now return to configure other plans or continue the workflow"})]})]})}),(0,s.jsxs)("div",{className:"flex justify-between",children:[s.jsx("button",{onClick:()=>{e.push(`/ai-enroller/manage-groups/company/${t}/plans`)},className:"px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors",children:"Save & Return to Plans"}),(0,s.jsxs)("button",{onClick:()=>{e.push(`/ai-enroller/manage-groups/company/${t}/plans/${o}/review`)},className:"px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center gap-2",children:["Continue to Review",s.jsx("span",{children:"→"})]})]})]})]})}},43058:(e,r,t)=>{"use strict";t.d(r,{Z:()=>u});var s=t(10326),a=t(17577),n=t(22758),i=t(35047),o=t(31870);t(32049),t(94638);var l=t(98139),d=t(6283);let c=()=>/Mobi|Android/i.test(navigator.userAgent),u=({children:e})=>{let{user:r,loading:t}=(0,n.a)(),u=(0,i.useRouter)(),m=(0,i.usePathname)(),p=(0,o.T)(),[x,g]=(0,a.useState)(!1),h=(0,o.C)(e=>e.user.userProfile);return((0,a.useEffect)(()=>{},[p,h.name]),(0,a.useEffect)(()=>{console.log("ProtectedRoute useEffect triggered"),console.log("Current user: ",r),console.log("Loading state: ",t),console.log("Current user details: ",h),t||r||(console.log("User not authenticated, redirecting to home"),g(!1),u.push("/")),!t&&h.companyId&&""===h.companyId&&(console.log("Waiting to retrieve company details"),g(!1)),!t&&h.companyId&&""!==h.companyId&&(console.log("User found, rendering children"),g(!0)),c()&&!m.startsWith("/mobile")&&(console.log(`Redirecting to mobile version of ${m}`),u.push(`/mobile${m}`))},[r,t,h,u,m]),x)?r?s.jsx(s.Fragment,{children:e}):null:s.jsx(d.Z,{sx:{display:"flex",justifyContent:"center",alignItems:"center",height:"100vh",bgcolor:"#f6f8fc"},children:s.jsx(l.Z,{})})}},94638:(e,r,t)=>{"use strict";t.d(r,{G9:()=>y,JZ:()=>f,M_:()=>x,N:()=>d,Nq:()=>p,TQ:()=>u,Ur:()=>o,aE:()=>c,aK:()=>j,dA:()=>l,gt:()=>b,mb:()=>h,qB:()=>v,yu:()=>m,zX:()=>g});var s=t(53148),a=t(39352),n=t(25748),i=t(32049);function o(e){switch(e){case"Our Culture & Workplace":return"Work Policies";case"Protecting Your Income":return"Income Security";default:return e}}function l(e){switch(e){case"On-site Amenities":return"Company Handbook";case"Protecting Your Income":return"Income Security";case"Got married/divorced":return"Marriage or Divorce";case"Had a baby or adopted a baby":return"New Baby or Adoption";case"Lost your insurance":return"Loss of Insurance";default:return e}}async function d(e,r){let t=await (0,s.A_)("/benefits/benefit-types",{companyId:r});return console.log("COMPANY BENEFIT TYPES RESPONSE: ",t.benefitTypes),e((0,a.x7)(t.benefitTypes)),t.benefitTypes}async function c(e,r){let t=await (0,s.A_)("/benefits/all-benefits",{companyId:r});console.log("COMPANY BENEFIT TYPES WITH BENEFITS RESPONSE: ",t),e((0,n.US)(t.benefitsPerType))}async function u(e){let r=await (0,s.A_)("/admin/all-employees");return console.log("COMPANY TEAM MEMBERS RESPONSE: ",r),e((0,a.Vv)(r.employees)),r.employees}async function m(e,r){return console.log("ADDING USERS: ",r),await (0,s.j0)("/admin/add/employees",{employeeList:r})}async function p(e,r,t){try{console.log("\uD83D\uDD0D Debug: User being updated:",r);let e={employeeId:r,updatedDetails:{name:t.name,email:t.email,details:{phoneNumber:t.phoneNumber||"",department:t.department||"",title:t.title||"",role:t.title||""}}};return t.dateOfBirth&&(e.updatedDetails.details.dateOfBirth=t.dateOfBirth),t.hireDate&&(e.updatedDetails.details.hireDate=t.hireDate),t.annualSalary&&(e.updatedDetails.details.annualSalary=t.annualSalary),t.employeeClassType&&(e.updatedDetails.details.employeeClassType=t.employeeClassType),t.workSchedule&&(e.updatedDetails.details.workSchedule=t.workSchedule),t.ssn&&(e.updatedDetails.details.ssn=t.ssn),t.employeeId&&(e.updatedDetails.details.employeeId=t.employeeId),t.workLocation&&(e.updatedDetails.details.workLocation=t.workLocation),t.address&&(e.updatedDetails.details.address=t.address),t.mailingAddress&&(e.updatedDetails.details.mailingAddress=t.mailingAddress),t.emergencyContact&&(e.updatedDetails.details.emergencyContact=t.emergencyContact),e.updatedDetails.details.dependents=t.dependents||[],console.log("Middleware - dependents being sent:",e.updatedDetails.details.dependents),console.log("Sending PUT request with cleaned data:",e),await (0,s.GH)("/admin/update/employee",e)}catch(e){throw console.error("Error in updateUser middleware:",e),e}}async function x(e,r){let t=await (0,s.A_)("/employee",{"user-id":r});return e((0,i.$l)({name:t.currentUser.name,email:t.currentUser.email,companyId:t.currentUser.companyId,role:t.currentUser.role,isAdmin:t.currentUser.isAdmin,isBroker:t.currentUser.isBroker,details:t.currentUser.details})),t}async function g(e,r,t){let a=await (0,s.j0)("/admin/onboard",{company:{name:r.name,adminEmail:r.adminEmail,adminRole:r.adminRole,companySize:r.companySize,industry:r.industry,location:r.location,website:r.website,howHeard:r.howHeard,brokerId:r.brokerId,brokerageId:r.brokerageId,isBrokerage:r.isBrokerage,isActivated:r.isActivated,referralSource:r.referralSource,details:{logo:""}},user:{email:t.email,name:t.name,role:t.role,isAdmin:t.isAdmin,isBroker:t.isBroker,isActivated:t.isActivated}}),n=a.data.userId,i=a.data.companyId;return localStorage.setItem("userid1",n),localStorage.setItem("companyId1",i),a}async function h(e,r){return console.log("SENDING LOGIN LINK TO EMPLOYEE: ",e,r),await (0,s.j0)("/admin/send-user-login-link",{userId:e,companyId:r})}async function y(e,r,t,a){let n=await (0,s.j0)("/admin/add/employer",{brokerId:e,companyName:r,companyAdminEmail:t,companyAdminName:a});return console.log("BROKER ADDS COMPANY RESPONSE: ",n),n}async function f(e,r){return 200===(await (0,s.j0)("/employee/offboard/",{userId:e,companyId:r})).status}async function b(e,r){return await (0,s.j0)("/employee/enable/",{userId:e,companyId:r})}async function v(e,r){try{let r=await (0,s.A_)("/admin/all-companies");console.log("CLIENT COMPANIES UNDER BROKER: ",r);let t=r.companies||[];try{let e=await (0,s.A_)("/employee/company-details");console.log("BROKER'S OWN COMPANY: ",e),e.company&&e.company.isBrokerage&&!t.some(r=>r._id===e.company._id)&&(t.unshift(e.company),console.log("Added broker's own company to the list"))}catch(e){console.log("Could not fetch broker's own company (this is normal if user is not a broker):",e)}return console.log("ALL COMPANIES (CLIENT + OWN): ",t),e((0,i.Ym)(t)),{...r,companies:t}}catch(r){return console.error("Error fetching companies:",r),e((0,i.Ym)([])),{companies:[]}}}async function j(e){let r=await (0,s.A_)("/employee/company-details");return e((0,a.sy)(r.company)),r.status}},31870:(e,r,t)=>{"use strict";t.d(r,{C:()=>n,T:()=>a});var s=t(25842);let a=()=>(0,s.I0)(),n=s.v9},6079:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(68570).createProxy)(String.raw`C:\beno\project_dev\qharmony-frontend-v1\src\app\ai-enroller\layout.tsx#default`)},22729:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(68570).createProxy)(String.raw`C:\beno\project_dev\qharmony-frontend-v1\src\app\ai-enroller\manage-groups\company\[companyId]\plans\[planId]\set-dates\page.tsx#default`)},73881:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>a});var s=t(66621);let a=e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",e.params,"favicon.ico")+""}]},54658:()=>{},23824:()=>{}};var r=require("../../../../../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[8948,1183,6621,8492,576],()=>t(9332));module.exports=s})();