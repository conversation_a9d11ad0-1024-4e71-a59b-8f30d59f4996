(()=>{var e={};e.id=2717,e.ids=[2717],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},27790:e=>{"use strict";e.exports=require("assert")},84770:e=>{"use strict";e.exports=require("crypto")},17702:e=>{"use strict";e.exports=require("events")},92048:e=>{"use strict";e.exports=require("fs")},32615:e=>{"use strict";e.exports=require("http")},35240:e=>{"use strict";e.exports=require("https")},19801:e=>{"use strict";e.exports=require("os")},55315:e=>{"use strict";e.exports=require("path")},76162:e=>{"use strict";e.exports=require("stream")},74026:e=>{"use strict";e.exports=require("string_decoder")},74175:e=>{"use strict";e.exports=require("tty")},17360:e=>{"use strict";e.exports=require("url")},21764:e=>{"use strict";e.exports=require("util")},71568:e=>{"use strict";e.exports=require("zlib")},98061:e=>{"use strict";e.exports=require("node:assert")},92761:e=>{"use strict";e.exports=require("node:async_hooks")},72254:e=>{"use strict";e.exports=require("node:buffer")},40027:e=>{"use strict";e.exports=require("node:console")},6005:e=>{"use strict";e.exports=require("node:crypto")},65714:e=>{"use strict";e.exports=require("node:diagnostics_channel")},15673:e=>{"use strict";e.exports=require("node:events")},88849:e=>{"use strict";e.exports=require("node:http")},42725:e=>{"use strict";e.exports=require("node:http2")},87503:e=>{"use strict";e.exports=require("node:net")},38846:e=>{"use strict";e.exports=require("node:perf_hooks")},39630:e=>{"use strict";e.exports=require("node:querystring")},84492:e=>{"use strict";e.exports=require("node:stream")},31764:e=>{"use strict";e.exports=require("node:tls")},41041:e=>{"use strict";e.exports=require("node:url")},47261:e=>{"use strict";e.exports=require("node:util")},93746:e=>{"use strict";e.exports=require("node:util/types")},24086:e=>{"use strict";e.exports=require("node:worker_threads")},65628:e=>{"use strict";e.exports=require("node:zlib")},29441:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>l.a,__next_app__:()=>u,originalPathname:()=>m,pages:()=>d,routeModule:()=>p,tree:()=>c}),s(93250),s(6079),s(33709),s(35866);var a=s(23191),r=s(88716),n=s(37922),l=s.n(n),i=s(95231),o={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>i[e]);s.d(t,o);let c=["",{children:["ai-enroller",{children:["manage-groups",{children:["company",{children:["[companyId]",{children:["plans",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,93250)),"C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\manage-groups\\company\\[companyId]\\plans\\page.tsx"]}]},{}]},{}]},{}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,6079)),"C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,33709)),"C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,35866,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],d=["C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\manage-groups\\company\\[companyId]\\plans\\page.tsx"],m="/ai-enroller/manage-groups/company/[companyId]/plans/page",u={require:s,loadChunk:()=>Promise.resolve()},p=new a.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/ai-enroller/manage-groups/company/[companyId]/plans/page",pathname:"/ai-enroller/manage-groups/company/[companyId]/plans",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},85024:(e,t,s)=>{Promise.resolve().then(s.bind(s,35509))},35509:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>x});var a=s(10326),r=s(17577),n=s(35047),l=s(38492);s(23824);var i=s(78581);let o=({isOpen:e,onClose:t,type:s,title:r,message:n,confirmText:i="OK",onConfirm:o})=>{if(!e)return null;let c=(()=>{switch(s){case"success":return{bg:"bg-green-50",border:"border-green-200",button:"bg-green-600 hover:bg-green-700"};case"error":return{bg:"bg-red-50",border:"border-red-200",button:"bg-red-600 hover:bg-red-700"};case"warning":return{bg:"bg-yellow-50",border:"border-yellow-200",button:"bg-yellow-600 hover:bg-yellow-700"};default:return{bg:"bg-blue-50",border:"border-blue-200",button:"bg-blue-600 hover:bg-blue-700"}}})();return a.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-xl max-w-md w-full mx-4",children:[a.jsx("div",{className:`${c.bg} ${c.border} border-b px-6 py-4 rounded-t-lg`,children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(()=>{switch(s){case"success":return a.jsx(l.PjL,{className:"w-12 h-12 text-green-500"});case"error":return a.jsx(l.C9x,{className:"w-12 h-12 text-red-500"});case"warning":return a.jsx(l.baL,{className:"w-12 h-12 text-yellow-500"});default:return a.jsx(l.if7,{className:"w-12 h-12 text-blue-500"})}})(),a.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:r})]}),a.jsx("button",{onClick:t,className:"text-gray-400 hover:text-gray-600 transition-colors",children:a.jsx(l.apv,{className:"w-6 h-6"})})]})}),a.jsx("div",{className:"px-6 py-4",children:a.jsx("p",{className:"text-gray-700 leading-relaxed",children:n})}),(0,a.jsxs)("div",{className:"px-6 py-4 bg-gray-50 rounded-b-lg flex justify-end space-x-3",children:[a.jsx("button",{onClick:t,className:"px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors",children:"Cancel"}),a.jsx("button",{onClick:()=>{o&&o(),t()},className:`px-6 py-2 ${c.button} text-white rounded-lg transition-colors font-medium`,children:i})]})]})})},c=()=>{let[e,t]=(0,r.useState)({isOpen:!1,type:"info",title:"",message:""}),s=(e,s,a,r,n)=>{t({isOpen:!0,type:e,title:s,message:a,confirmText:r,onConfirm:n})};return{notification:e,showNotification:s,hideNotification:()=>{t(e=>({...e,isOpen:!1}))},showSuccess:(e,t,a,r)=>{s("success",e,t,a,r)},showError:(e,t,a,r)=>{s("error",e,t,a,r)},showWarning:(e,t,a,r)=>{s("warning",e,t,a,r)},showInfo:(e,t,a,r)=>{s("info",e,t,a,r)}}};var d=s(43933);let m=({isOpen:e,onClose:t,onSelectPlan:n,onCreatePlan:m,availablePlans:u=[],companyId:p,editMode:x=!1,editingPlan:g=null,editingCoverageTiers:h=[]})=>{let[y,b]=(0,r.useState)(""),[f,v]=(0,r.useState)(!1),[j,N]=(0,r.useState)("plan"),[w,D]=(0,r.useState)([]),[C,P]=(0,r.useState)(!1),[S,E]=(0,r.useState)(!1),[T,k]=(0,r.useState)(null),{notification:A,showSuccess:F,showError:$,showWarning:_,hideNotification:q}=c(),[L,I]=(0,r.useState)(""),[M,z]=(0,r.useState)(null),[R,O]=(0,r.useState)("All Statuses"),[B,U]=(0,r.useState)("All Carriers"),[G,W]=(0,r.useState)(x?"configure":"select"),[Y,V]=(0,r.useState)("percentage"),[H,K]=(0,r.useState)([{id:"1",tier:"Employee Only",premium:450,employerPercent:80,employerPays:360,employeePays:90},{id:"2",tier:"Employee + Spouse",premium:880,employerPercent:80,employerPays:704,employeePays:176},{id:"3",tier:"Employee + Children",premium:720,employerPercent:80,employerPays:576,employeePays:144},{id:"4",tier:"Employee + Family",premium:1250,employerPercent:80,employerPays:1e3,employeePays:250}]),[Z,X]=(0,r.useState)("2024-11-01"),[J,Q]=(0,r.useState)("2024-11-30"),[ee,et]=(0,r.useState)("2025-01-01"),[es,ea]=(0,r.useState)("2025-12-31"),er=async()=>{try{P(!0),k(null);let e=await (0,d.Pb)();e.success&&e.data?(D(e.data),e.data.length>0&&N(e.data[0].coverageType||"plan")):(k(e.error||"Failed to fetch assignable plans"),D(u))}catch(e){console.error("Error fetching assignable plans:",e),k("Failed to fetch assignable plans"),D(u)}finally{P(!1)}};if((0,r.useEffect)(()=>{e&&er()},[e]),(0,r.useEffect)(()=>{if(x&&g&&e){console.log("\uD83D\uDD27 Initializing edit mode with plan:",g),console.log("\uD83D\uDD27 Editing coverage tiers received:",h);let e=g.planId||g._id;if(z({_id:e,planName:g.planName,planCode:g.planCode,coverageType:g.coverageType||g.type,planType:g.type,metalTier:g.metalTier}),I(e),h&&h.length>0?(console.log("\uD83D\uDD27 Setting coverage tiers in modal:",h),K(h)):console.log("\uD83D\uDD27 No coverage tiers provided, using default"),g.enrollmentStartDate){let e=g.enrollmentStartDate.split("T")[0];console.log("\uD83D\uDD27 Setting enrollment start date:",e),X(e)}if(g.enrollmentEndDate){let e=g.enrollmentEndDate.split("T")[0];console.log("\uD83D\uDD27 Setting enrollment end date:",e),Q(e)}if(g.planEffectiveDate){let e=g.planEffectiveDate.split("T")[0];console.log("\uD83D\uDD27 Setting plan start date:",e),et(e)}if(g.planEndDate){let e=g.planEndDate.split("T")[0];console.log("\uD83D\uDD27 Setting plan end date:",e),ea(e)}W("configure")}},[x,g,h,e]),(0,r.useEffect)(()=>{!x&&e&&(console.log("\uD83D\uDD04 Resetting modal to default state (not edit mode)"),W("select"),z(null),I(""),K([{id:"1",tier:"Employee Only",premium:450,employerPercent:80,employerPays:360,employeePays:90},{id:"2",tier:"Employee + Spouse",premium:880,employerPercent:80,employerPays:704,employeePays:176},{id:"3",tier:"Employee + Children",premium:720,employerPercent:80,employerPays:576,employeePays:144},{id:"4",tier:"Employee + Family",premium:1250,employerPercent:80,employerPays:1e3,employeePays:250}]))},[x,e]),!e)return null;let en=w.filter(e=>{let t=""===y||(e.planName||"").toLowerCase().includes(y.toLowerCase())||(e.planCode||"").toLowerCase().includes(y.toLowerCase())||(e.planType||"").toLowerCase().includes(y.toLowerCase())||(e.coverageType||"").toLowerCase().includes(y.toLowerCase())||e.coverageSubTypes&&e.coverageSubTypes.some(e=>e.toLowerCase().includes(y.toLowerCase())),s="All Statuses"===R||(e.status||"Active")===R,a="All Carriers"===B||e.coverageType===B||e.coverageSubTypes&&e.coverageSubTypes.includes(B);return t&&s&&a}),el=e=>{console.log("\uD83D\uDD0D Plan Selection Debug:",e),console.log("\uD83D\uDD0D Plan _id:",e._id),console.log("\uD83D\uDD0D Plan keys:",Object.keys(e)),I(e._id),z(e),W("configure")},ei=(e,t,s)=>{K(a=>a.map(a=>{if(a.id===e){let e={...a,[t]:s};if("percentage"===Y){if("premium"===t||"employerPercent"===t){let a="premium"===t?s:e.premium,r="employerPercent"===t?s:e.employerPercent;e.employerPays=a*r/100,e.employeePays=a-e.employerPays}}else if("premium"===t||"employerPays"===t){let a="premium"===t?s:e.premium,r="employerPays"===t?s:e.employerPays;e.employerPays=r,e.employeePays=a-r,e.employerPercent=a>0?Math.round(r/a*100):0}return e}return a}))},eo=async()=>{if(!M||!p){$("Missing Information","Please ensure a plan is selected and company information is available.");return}if(S){console.log("\uD83D\uDEAB Already submitting, ignoring duplicate request");return}try{let e;E(!0),console.log("\uD83D\uDD0D Modal Debug - Selected Plan:",M),console.log("\uD83D\uDD0D Modal Debug - Company ID:",p),console.log("\uD83D\uDD0D Modal Debug - Coverage Tiers:",H),console.log("\uD83D\uDD0D Modal Debug - Dates:",{enrollmentStartDate:Z,enrollmentEndDate:J,planStartDate:ee,planEndDate:es});let a={planId:M._id,companyId:p,rateStructure:"Composite",coverageTiers:H.map(e=>({tierName:e.tier,totalCost:e.premium,employerCost:e.employerPays,employeeCost:e.employeePays})),enrollmentStartDate:new Date(Z).toISOString(),enrollmentEndDate:new Date(J).toISOString(),planEffectiveDate:new Date(ee).toISOString(),planEndDate:new Date(es).toISOString(),assignmentYear:new Date(es).getFullYear(),assignmentExpiry:new Date(es).toISOString(),employerContribution:{contributionType:"Percentage",contributionAmount:80},employeeContribution:{contributionType:"Percentage",contributionAmount:20},ageBandedRates:[],salaryBasedRates:[],planCustomizations:{},waitingPeriod:{enabled:!1,days:0,rule:"Immediate"},eligibleEmployeeClasses:["Full-Time"],enrollmentType:"Active",status:"Active",isActive:!0},{createPlanAssignment:r,updatePlanAssignment:n}=await Promise.resolve().then(s.bind(s,43933));x&&g?(console.log("\uD83D\uDD27 Updating plan assignment:",g._id),e=await n(g._id,a)):(console.log("\uD83C\uDD95 Creating new plan assignment"),e=await r(a)),e.success?F(x?"Plan Assignment Updated!":"Plan Assignment Created!",x?"The plan assignment has been successfully updated with your changes.":"The plan has been successfully assigned to the company with your configured settings.","Continue",()=>{window.location.reload(),t()}):$(x?"Update Failed":"Assignment Failed",`Failed to ${x?"update":"create"} plan assignment: ${e.error||"Unknown error occurred"}`)}catch(e){console.error("Error creating plan assignment:",e),$("Assignment Error","An unexpected error occurred while creating the plan assignment. Please try again.")}finally{E(!1)}};return(0,a.jsxs)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:[a.jsx("div",{className:"bg-white rounded-lg shadow-xl w-full max-w-7xl max-h-[95vh] overflow-y-auto",children:(0,a.jsxs)("div",{className:"p-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,a.jsxs)("div",{className:"flex items-center gap-4",children:["select"!==G&&a.jsx("button",{onClick:"dates"===G?()=>{W("configure")}:()=>{W("select"),I(""),z(null)},className:"text-gray-600 hover:text-gray-800 transition-colors",children:"← Back"}),a.jsx("h2",{className:"text-2xl font-bold text-gray-900",children:(()=>{let e=x?"Edit":"";switch(G){case"select":return f?"Create a new plan":"Select a plan";case"configure":return`${e} Configure Coverage Tiers - ${M?.planName}`;case"dates":return`${e} Set Enrollment & Coverage Dates - ${M?.planName}`;default:return"Select a plan"}})()})]}),(0,a.jsxs)("div",{className:"flex items-center gap-3",children:["select"===G&&!f&&(0,a.jsxs)("button",{onClick:()=>v(!0),className:"flex items-center gap-2 bg-gray-900 text-white px-4 py-2 rounded-lg hover:bg-gray-800 transition-colors",children:[a.jsx(l.r7I,{className:"w-4 h-4"}),"Create new plan"]}),a.jsx("button",{onClick:t,className:"text-gray-400 hover:text-gray-600 transition-colors",children:a.jsx(l.fMW,{className:"w-6 h-6"})})]})]}),a.jsx("div",{className:"flex items-center justify-center mb-8",children:(0,a.jsxs)("div",{className:"flex items-center gap-4",children:[a.jsx("div",{className:`flex items-center justify-center w-8 h-8 rounded-full ${"select"===G?"bg-blue-600 text-white":"bg-gray-200 text-gray-600"}`,children:"1"}),a.jsx("div",{className:"w-16 h-1 bg-gray-200"}),a.jsx("div",{className:`flex items-center justify-center w-8 h-8 rounded-full ${"configure"===G?"bg-blue-600 text-white":"bg-gray-200 text-gray-600"}`,children:"2"}),a.jsx("div",{className:"w-16 h-1 bg-gray-200"}),a.jsx("div",{className:`flex items-center justify-center w-8 h-8 rounded-full ${"dates"===G?"bg-blue-600 text-white":"bg-gray-200 text-gray-600"}`,children:"3"})]})}),"select"===G&&a.jsx(a.Fragment,{children:f?a.jsx(i.Z,{onCancel:()=>v(!1),onSubmit:e=>{console.log("\uD83C\uDFAF Plan created successfully, auto-selecting for configuration:",e),z(e),I(e._id),v(!1),W("configure"),D(t=>[e,...t])}}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 mb-4",children:[a.jsx(l.O6C,{className:"w-5 h-5 text-gray-400"}),a.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Search & Filter"})]}),(0,a.jsxs)("div",{className:"flex gap-4 items-center",children:[(0,a.jsxs)("div",{className:"relative flex-1",children:[a.jsx("input",{type:"text",placeholder:"Search by plan name, code, or coverage subtype...",className:"w-full px-4 py-2.5 pl-10 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-gray-900 bg-white",value:y,onChange:e=>b(e.target.value)}),a.jsx(l.O6C,{className:"absolute left-3 top-3 text-gray-400 w-5 h-5"})]}),(0,a.jsxs)("select",{className:"px-3 py-2.5 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white text-gray-700 min-w-[140px]",value:R,onChange:e=>O(e.target.value),children:[a.jsx("option",{children:"All Statuses"}),a.jsx("option",{children:"Active"}),a.jsx("option",{children:"Draft"}),a.jsx("option",{children:"Archived"})]}),(0,a.jsxs)("select",{className:"px-3 py-2.5 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white text-gray-700 min-w-[140px]",value:B,onChange:e=>U(e.target.value),children:[a.jsx("option",{children:"All Carriers"}),a.jsx("option",{children:"Your Health"}),a.jsx("option",{children:"Income Security"})]})]}),(0,a.jsxs)("div",{className:"mt-3 text-sm text-gray-600",children:["Showing ",en.length," of ",w.length," plans"]})]}),a.jsx("div",{className:"mb-6",children:a.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Plans List"})}),C&&(0,a.jsxs)("div",{className:"flex justify-center items-center py-8",children:[a.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600"}),a.jsx("span",{className:"ml-2 text-gray-600",children:"Loading plans..."})]}),T&&(0,a.jsxs)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4 mb-4",children:[a.jsx("p",{className:"text-red-600 text-sm",children:T}),a.jsx("button",{onClick:er,className:"mt-2 text-red-600 hover:text-red-800 text-sm font-medium",children:"Try again"})]}),!C&&!T&&(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"grid grid-cols-12 gap-6 px-6 py-3 bg-gray-50 rounded-lg text-sm font-medium text-gray-700",children:[a.jsx("div",{className:"col-span-3",children:"Plan Name"}),a.jsx("div",{className:"col-span-3",children:"Plan Code"}),a.jsx("div",{className:"col-span-2",children:"Coverage Subtype"}),a.jsx("div",{className:"col-span-1",children:"Status"}),a.jsx("div",{className:"col-span-1",children:"Metal Tier"}),a.jsx("div",{className:"col-span-2",children:"Action"})]}),0===en.length?a.jsx("div",{className:"text-center py-8 text-gray-500",children:y?"No plans found matching your search.":"No plans available."}):en.map(e=>(0,a.jsxs)("div",{className:`grid grid-cols-12 gap-6 px-6 py-4 bg-white border rounded-lg hover:shadow-md transition-all ${L===e._id?"border-blue-300 bg-blue-50 shadow-md":"border-gray-200 hover:border-gray-300"}`,children:[(0,a.jsxs)("div",{className:"col-span-3",children:[a.jsx("div",{className:"font-semibold text-gray-900",children:e.planName||"Unnamed Plan"}),e.description&&a.jsx("div",{className:"text-sm text-gray-500 truncate mt-1",children:e.description})]}),a.jsx("div",{className:"col-span-3",children:a.jsx("span",{className:"inline-flex items-center px-3 py-1 rounded-md text-sm font-medium bg-blue-100 text-blue-800",children:e.planCode||"No Code"})}),a.jsx("div",{className:"col-span-2",children:a.jsx("div",{className:"flex flex-wrap gap-1",children:e.coverageSubTypes&&e.coverageSubTypes.length>0?e.coverageSubTypes.map((e,t)=>a.jsx("span",{className:"inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-blue-50 text-blue-700",children:e},t)):a.jsx("span",{className:"inline-flex items-center px-3 py-1 rounded-md text-sm font-medium bg-gray-50 text-gray-700",children:e.coverageType})})}),a.jsx("div",{className:"col-span-1",children:a.jsx("span",{className:`inline-flex items-center px-2 py-1 rounded-md text-xs font-medium ${"Active"===e.status?"bg-green-100 text-green-800":"Draft"===e.status?"bg-yellow-100 text-yellow-800":"bg-gray-100 text-gray-800"}`,children:e.status||"Active"})}),a.jsx("div",{className:"col-span-1",children:a.jsx("span",{className:"text-sm text-gray-600 font-medium",children:e.metalTier||"-"})}),a.jsx("div",{className:"col-span-2",children:a.jsx("button",{onClick:()=>el(e),className:"px-4 py-2 rounded-lg text-sm font-medium transition-colors bg-blue-600 text-white hover:bg-blue-700",children:"Select Plan"})})]},e._id))]})]})}),"configure"===G&&M&&(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:[a.jsx("h3",{className:"font-semibold text-blue-900 mb-2",children:"Selected Plan"}),(0,a.jsxs)("div",{className:"text-sm text-blue-800",children:[(0,a.jsxs)("div",{children:[a.jsx("strong",{children:"Plan Name:"})," ",M.planName]}),(0,a.jsxs)("div",{children:[a.jsx("strong",{children:"Coverage Type:"})," ",M.coverageType]}),(0,a.jsxs)("div",{children:[a.jsx("strong",{children:"Plan Type:"})," ",M.planType]})]})]}),(0,a.jsxs)("div",{className:"bg-white rounded-xl border border-gray-200 p-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[a.jsx("h4",{className:"text-lg font-semibold text-gray-900",children:"Coverage Tiers & Contributions"}),(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[a.jsx("span",{className:"text-sm font-medium text-gray-700",children:"Contribution Type:"}),(0,a.jsxs)("select",{value:Y,onChange:e=>V(e.target.value),className:"px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white text-gray-900",children:[a.jsx("option",{value:"percentage",children:"Percentage"}),a.jsx("option",{value:"fixed",children:"Fixed Amount"})]})]})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"grid grid-cols-5 gap-4 text-sm font-medium text-gray-700 pb-2 border-b",children:[a.jsx("div",{children:"Coverage Tier"}),a.jsx("div",{children:"Premium"}),a.jsx("div",{children:"percentage"===Y?"Employer %":"Employer Pays"}),a.jsx("div",{children:"percentage"===Y?"Employer Pays":"Employee Pays"}),a.jsx("div",{children:"percentage"===Y?"Employee Pays":"Employer %"})]}),H.map(e=>(0,a.jsxs)("div",{className:"grid grid-cols-5 gap-4 items-center text-sm",children:[a.jsx("input",{type:"text",value:e.tier,onChange:t=>K(s=>s.map(s=>s.id===e.id?{...s,tier:t.target.value}:s)),className:"px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white text-gray-900"}),a.jsx("input",{type:"number",value:e.premium,onChange:t=>ei(e.id,"premium",parseFloat(t.target.value)||0),className:"px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white text-gray-900"}),"percentage"===Y?(0,a.jsxs)(a.Fragment,{children:[a.jsx("input",{type:"number",value:e.employerPercent,onChange:t=>ei(e.id,"employerPercent",parseFloat(t.target.value)||0),className:"px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white text-gray-900",min:"0",max:"100"}),(0,a.jsxs)("div",{className:"px-3 py-2 bg-gray-50 rounded-lg text-gray-700 font-medium",children:["$",e.employerPays.toFixed(2)]}),(0,a.jsxs)("div",{className:"px-3 py-2 bg-gray-50 rounded-lg text-gray-700 font-medium",children:["$",e.employeePays.toFixed(2)]})]}):(0,a.jsxs)(a.Fragment,{children:[a.jsx("input",{type:"number",value:e.employerPays,onChange:t=>ei(e.id,"employerPays",parseFloat(t.target.value)||0),className:"px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white text-gray-900",min:"0"}),(0,a.jsxs)("div",{className:"px-3 py-2 bg-gray-50 rounded-lg text-gray-700 font-medium",children:["$",e.employeePays.toFixed(2)]}),(0,a.jsxs)("div",{className:"px-3 py-2 bg-gray-50 rounded-lg text-gray-700 font-medium",children:[e.employerPercent.toFixed(1),"%"]})]})]},e.id))]})]}),a.jsx("div",{className:"flex justify-end",children:a.jsx("button",{onClick:()=>{W("dates")},className:"bg-gradient-to-r from-blue-600 to-purple-600 text-white px-6 py-2 rounded-lg hover:opacity-90 transition-all",children:"Continue to Dates"})})]}),"dates"===G&&M&&(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:[a.jsx("h3",{className:"font-semibold text-blue-900 mb-2",children:"Plan Assignment Summary"}),(0,a.jsxs)("div",{className:"text-sm text-blue-800",children:[(0,a.jsxs)("div",{children:[a.jsx("strong",{children:"Plan:"})," ",M.planName]}),(0,a.jsxs)("div",{children:[a.jsx("strong",{children:"Coverage Tiers:"})," ",H.length," configured"]})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{className:"space-y-4",children:[a.jsx("h4",{className:"text-lg font-semibold text-gray-900",children:"Enrollment Period"}),(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Enrollment Start Date"}),a.jsx("input",{type:"date",value:Z,onChange:e=>X(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white text-gray-900"})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Enrollment End Date"}),a.jsx("input",{type:"date",value:J,onChange:e=>Q(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white text-gray-900"})]})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[a.jsx("h4",{className:"text-lg font-semibold text-gray-900",children:"Coverage Period"}),(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Plan Effective Date"}),a.jsx("input",{type:"date",value:ee,onChange:e=>et(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white text-gray-900"})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Plan End Date"}),a.jsx("input",{type:"date",value:es,onChange:e=>ea(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white text-gray-900"})]})]})]}),a.jsx("div",{className:"flex justify-end",children:a.jsx("button",{onClick:eo,disabled:S,className:`px-6 py-2 rounded-lg transition-all ${S?"bg-gray-400 cursor-not-allowed":"bg-gradient-to-r from-blue-600 to-purple-600 hover:opacity-90"} text-white`,children:S?"Creating Assignment...":"Complete Plan Assignment"})})]})]})}),a.jsx(o,{isOpen:A.isOpen,onClose:q,type:A.type,title:A.title,message:A.message,confirmText:A.confirmText,onConfirm:A.onConfirm})]})};var u=s(89009),p=s(67925);let x=function(){let e=(0,n.useRouter)(),t=(0,n.useParams)().companyId,[s,i]=(0,r.useState)(null),[x,g]=(0,r.useState)([]),[h,y]=(0,r.useState)(!0),[b,f]=(0,r.useState)(null),[v,j]=(0,r.useState)([]),[N,w]=(0,r.useState)(null),[D,C]=(0,r.useState)(!1),[P,S]=(0,r.useState)("all"),[E,T]=(0,r.useState)("all"),[k,A]=(0,r.useState)(1),[F]=(0,r.useState)(10),[$,_]=(0,r.useState)("active"),[q,L]=(0,r.useState)(!1),[I,M]=(0,r.useState)("percentage"),[z,R]=(0,r.useState)([]),[O,B]=(0,r.useState)(null),[U,G]=(0,r.useState)(!1),{notification:W,showNotification:Y,showSuccess:V,showError:H,showWarning:K,hideNotification:Z}=c(),[X,J]=(0,r.useState)({}),[Q,ee]=(0,r.useState)(0),[et,es]=(0,r.useState)(0),ea=async e=>{let t=(0,u.bR)(),s=(0,u.n5)();try{let a=await fetch(`${t}/employee/company-details`,{headers:{"Content-Type":"application/json","user-id":s}});if(a.ok){let t=await a.json();if(t.company&&t.company.isBrokerage&&t.company._id===e)return console.log("Found broker's own company with employee count:",t.company.companySize),{_id:t.company._id,companyName:t.company.name||"Unknown Company",employeeCount:t.company.companySize||250}}let r=await fetch(`${t}/admin/all-companies`,{headers:{"Content-Type":"application/json","user-id":s}});if(r.ok){let t=await r.json(),s=t.companies?.find(t=>t._id===e);if(s)return console.log("Found client company with employee count:",s.companySize),{_id:s._id,companyName:s.name||"Unknown Company",employeeCount:s.companySize||250}}let n=await fetch(`${t}/api/pre-enrollment/companies/${e}`,{headers:{"Content-Type":"application/json","user-id":s}});if(n.ok){let t=await n.json();return{_id:t.company?._id||e,companyName:t.company?.companyName||"Unknown Company",employeeCount:t.company?.companySize||t.company?.employeeCount||250}}}catch(e){console.error("Error fetching company details:",e)}return{_id:e,companyName:"Unknown Company",employeeCount:250}},er=(0,r.useCallback)(async()=>{try{y(!0),f(null);let e=await ea(t);ee(e.employeeCount||250),console.log("Company employee count set to:",e.employeeCount||250);let s=await (0,d.fH)(t,{includePlanData:!0});if(s.success&&s.data){let t=s.data.assignments;if(console.log("Fetched plan assignments:",t),s.data.accessDeniedToExisting&&s.data.canCreateAssignments){console.log("\uD83D\uDD27 Broker can create new plan assignments for this company"),i(e),g([]);return}let a=await Promise.all(t.map(async e=>{console.log("Processing assignment with enriched data:",e._id,e);let t=e.planData||e.plan,s=e.carrierData,a="string"==typeof e.planId?e.planId:e.planId?._id||"",r=t?.planName||"Unknown Plan",n=t?.planCode||"N/A",l=s?.carrierName||"Unknown Carrier",i=t?.coverageType||"",o=t?.coverageSubTypes||[],c=t?.metalTier||"";console.log("Using enriched plan data:",{planName:r,planCode:n,carrierName:l,coverageType:i,coverageSubTypes:o,metalTier:c});let d="Medical";if(o&&o.length>0){let e=o[0].toLowerCase();d=e.includes("dental")?"Dental":e.includes("vision")?"Vision":e.includes("medical")||e.includes("health")?"Medical":"Ancillary"}else if(i){let e=i.toLowerCase();d=e.includes("dental")?"Dental":e.includes("vision")?"Vision":e.includes("medical")||e.includes("health")?"Medical":"Ancillary"}let m="Active"===e.status||"Draft"===e.status,u="Active"===e.status||"Draft"===e.status;return{_id:e._id,planName:r,planCode:n,carrier:l,type:d,metalTier:c,period:`${new Date(e.planEffectiveDate).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric"})} - ${new Date(e.planEndDate).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric"})}`,status:e.status,assignmentId:e._id,planId:a,canEdit:m,canDelete:u,coverageType:i,coverageTiers:e.coverageTiers||[],enrollmentStartDate:e.enrollmentStartDate,enrollmentEndDate:e.enrollmentEndDate,planEffectiveDate:e.planEffectiveDate,planEndDate:e.planEndDate}}));console.log("Final display plans:",a),i(e),g(a)}else f(s.error||"Failed to fetch plan assignments"),g([])}catch(e){console.error("Error fetching data:",e),f("Failed to fetch plan assignments"),g([])}finally{y(!1)}},[t]),en=()=>{L(!0)},el=async e=>{console.log("\uD83D\uDD04 Workflow 2: Creating fresh plan assignment from template:",e);try{let s=new Date().getFullYear(),a=s+1,r=`${a}-01-01`,n=`${a}-12-31`,l=`${s}-11-01`,i=`${s}-11-30`,o={planId:e._id,companyId:t,rateStructure:"Composite",coverageTiers:[{tierName:"Employee Only",totalCost:500,employerCost:400,employeeCost:100},{tierName:"Employee + Spouse",totalCost:1e3,employerCost:800,employeeCost:200},{tierName:"Employee + Child(ren)",totalCost:800,employerCost:640,employeeCost:160},{tierName:"Family",totalCost:1500,employerCost:1200,employeeCost:300}],planEffectiveDate:r,planEndDate:n,enrollmentStartDate:l,enrollmentEndDate:i,groupNumber:`GRP-${t}-${e._id.slice(-6)}`,waitingPeriod:{enabled:!1,days:0,rule:"Immediate"},enrollmentType:"Active",employerContribution:{contributionType:"Percentage",contributionAmount:80},employeeContribution:{contributionType:"Percentage",contributionAmount:20},ageBandedRates:[],salaryBasedRates:[],planCustomizations:{},status:"Draft"},c=await (0,d.createPlanAssignment)(o);c.success&&c.data?(console.log("✅ Fresh plan assignment created successfully:",c.data),await er()):(console.error("❌ Failed to create plan assignment:",c.error),H("Assignment Failed","Failed to assign plan to company: "+(c.error||"Unknown error")))}catch(e){console.error("Error creating plan assignment:",e),H("Assignment Error","Failed to assign plan to company. Please try again.")}},ei=async e=>{console.log("\uD83D\uDD04 Workflow 2: Creating fresh plan assignment for new plan:",e);try{let s=new Date().getFullYear(),a=s+1,r=`${a}-01-01`,n=`${a}-12-31`,l=`${s}-11-01`,i=`${s}-11-30`,o={planId:e._id,companyId:t,rateStructure:"Composite",coverageTiers:[{tierName:"Employee Only",totalCost:500,employerCost:400,employeeCost:100},{tierName:"Employee + Spouse",totalCost:1e3,employerCost:800,employeeCost:200},{tierName:"Employee + Child(ren)",totalCost:800,employerCost:640,employeeCost:160},{tierName:"Family",totalCost:1500,employerCost:1200,employeeCost:300}],planEffectiveDate:r,planEndDate:n,enrollmentStartDate:l,enrollmentEndDate:i,groupNumber:`GRP-${t}-${e._id.slice(-6)}`,waitingPeriod:{enabled:!1,days:0,rule:"Immediate"},enrollmentType:"Active",employerContribution:{contributionType:"Percentage",contributionAmount:80},employeeContribution:{contributionType:"Percentage",contributionAmount:20},ageBandedRates:[],salaryBasedRates:[],planCustomizations:{},status:"Draft"},c=await (0,d.createPlanAssignment)(o);c.success&&c.data?(await er(),V("Plan Created!","Plan created and assigned to company successfully!")):(console.error("❌ Failed to create plan assignment:",c.error),H("Assignment Failed","Plan created but failed to assign to company: "+(c.error||"Unknown error")))}catch(e){console.error("Error creating plan assignment:",e),H("Assignment Error","Plan created but failed to assign to company. Please try again.")}L(!1)},eo=async e=>{let t=(0,u.bR)(),s=(0,u.n5)();try{console.log("Fetching plan assignment details for ID:",e);let a=await fetch(`${t}/api/pre-enrollment/plan-assignments/${e}`,{headers:{"Content-Type":"application/json","user-id":s}});if(console.log("Plan assignment fetch response status:",a.status),a.ok){let e=await a.json();console.log("Plan assignment fetch response data:",e),console.log("Assignment object:",e.assignment);let t=e.assignment._doc||e.assignment;return console.log("Processed assignment:",t),console.log("Coverage tiers in assignment:",t?.coverageTiers),t}{console.error("Failed to fetch plan assignment details. Status:",a.status);let e=await a.text();console.error("Error response:",e)}}catch(e){console.error("Error fetching plan assignment details:",e)}return null},ec=async e=>{try{let t=new Date().getFullYear(),s=t+1,a=`${s}-12-31`,r={planEffectiveDate:`${s}-01-01`,planEndDate:a,enrollmentStartDate:`${t}-11-01`,enrollmentEndDate:`${t}-11-30`,status:"Draft",assignmentYear:s,assignmentExpiry:a,employerContribution:{contributionType:"Percentage",contributionAmount:80},employeeContribution:{contributionType:"Percentage",contributionAmount:20},eligibleEmployeeClasses:["Full-Time"],enrollmentType:"Active"},n=await (0,d.Uc)(e.assignmentId,r);n.success?(V("Plan Renewed Successfully",`Plan has been renewed for ${s}. The new assignment is in Draft status.`),await er()):H("Renewal Failed",n.error||"Failed to renew plan assignment")}catch(e){console.error("Error renewing plan:",e),H("Renewal Error","An unexpected error occurred while renewing the plan")}},ed=async e=>{if(!e.canEdit){K("Cannot Edit Plan","This plan cannot be edited due to its current status or active enrollments.");return}console.log("\uD83D\uDD27 Starting edit mode for plan:",e);try{let t=await eo(e.assignmentId);if(!t){H("Error","Failed to load plan assignment details for editing.");return}console.log("\uD83D\uDD27 Full assignment details for editing:",t);let s=[];t.coverageTiers&&t.coverageTiers.length>0&&(s=t.coverageTiers.map((e,t)=>{let s=e.employerContributionPercent||80;return{id:(t+1).toString(),tier:e.tierName||`Tier ${t+1}`,premium:e.totalCost||0,employerPercent:s,employerPays:e.employerCost||0,employeePays:e.employeeCost||0}}));let a={...e,...t,assignmentId:e.assignmentId,_id:e.assignmentId};console.log("\uD83D\uDD27 Enhanced plan for editing:",a),console.log("\uD83D\uDD27 Formatted tiers for editing:",s),w(a),R(s),L(!0)}catch(e){console.error("Error preparing edit mode:",e),H("Error","Failed to prepare plan assignment for editing.")}},em=async e=>{try{if(!e.canDelete){K("Cannot Delete Plan","This plan cannot be deleted due to its current status or active enrollments.");return}Y("warning","Confirm Deletion",`Are you sure you want to delete the plan assignment for "${e.planName}"? This action cannot be undone.`,"Delete",async()=>{let t=await (0,d.KE)(e.assignmentId);t.success?(V("Plan Deleted","Plan assignment has been successfully deleted."),await er()):H("Deletion Failed",t.error||"Failed to delete plan assignment")})}catch(e){console.error("Error deleting plan:",e),H("Deletion Error","An unexpected error occurred while deleting the plan")}},eu=e=>{console.log("\uD83D\uDD0D Viewing plan assignment:",e),B(e),G(!0)},ep=(e=>{let t=new Date;return e.reduce((e,s)=>{let a=new Date(s.planEffectiveDate||s.enrollmentStartDate||new Date),r=new Date(s.planEndDate||s.enrollmentEndDate||new Date);return a<=t&&t<=r?e.active.push(s):a>t?e.upcoming.push(s):r<t&&e.past.push(s),e},{active:[],upcoming:[],past:[]})})(x),ex=("active"===$?ep.active:"upcoming"===$?ep.upcoming:ep.past).reduce((e,t)=>e+(t.coverageTiers?.[0]?.employeeCost||0),0),eg=x.filter(e=>{let t="all"===P||e.status===P,s="all"===E||e.carrier===E,a=new Date,r=new Date(e.planEffectiveDate||e.enrollmentStartDate||new Date),n=new Date(e.planEndDate||e.enrollmentEndDate||new Date),l=!0;return"active"===$?l=r<=a&&a<=n:"upcoming"===$?l=r>a:"past"===$&&(l=n<a),t&&s&&l});eg.length;let eh=(k-1)*F;return(eg.slice(eh,eh+F).reduce((e,t)=>(e[t.type]||(e[t.type]=[]),e[t.type].push(t),e),{}),h)?a.jsx("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center",children:[a.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-black mx-auto"}),a.jsx("p",{className:"mt-4 text-gray-600",children:"Loading plan assignments..."})]})}):b?a.jsx("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:a.jsx("div",{className:"text-center",children:(0,a.jsxs)("div",{className:"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded-xl",children:[a.jsx("h3",{className:"font-bold",children:"Error Loading Plans"}),a.jsx("p",{children:b}),a.jsx("button",{onClick:er,className:"mt-2 bg-red-600 text-white px-4 py-2 rounded-xl hover:bg-red-700",children:"Try Again"})]})})}):(0,a.jsxs)(a.Fragment,{children:[a.jsx(p.Z,{}),(0,a.jsxs)("div",{className:"min-h-screen bg-white",children:[a.jsx("div",{className:"bg-white",children:a.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("button",{onClick:()=>e.push("/ai-enroller/manage-groups"),className:"inline-flex items-center gap-2 text-sm font-medium text-gray-600 hover:text-gray-900 transition-colors group",children:[a.jsx("svg",{className:"w-4 h-4 group-hover:-translate-x-1 transition-transform",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:a.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 19l-7-7 7-7"})}),"Back to Groups"]}),(0,a.jsxs)("button",{onClick:en,className:"inline-flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-blue-600 to-purple-600 text-white text-sm font-medium rounded-lg hover:opacity-90 transition-all shadow-sm hover:shadow-md",children:[a.jsx(l.r7I,{className:"w-4 h-4"}),"Add Plan"]})]})})}),a.jsx("div",{className:"bg-white ",children:(0,a.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6",children:[(0,a.jsxs)("div",{className:"flex items-center gap-6 mb-8",children:[(0,a.jsxs)("div",{className:"relative",children:[a.jsx("div",{className:"w-16 h-16 bg-gradient-to-br from-blue-600 to-purple-600 rounded-xl flex items-center justify-center shadow-lg",children:a.jsx(l.$xp,{className:"w-8 h-8 text-white"})}),a.jsx("div",{className:"absolute -bottom-1 -right-1 w-5 h-5 bg-green-500 rounded-full border-2 border-white flex items-center justify-center",children:a.jsx(l.PjL,{className:"w-3 h-3 text-white"})})]}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3 mb-2",children:[a.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:s?.companyName||"Loading..."}),a.jsx("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800",children:"Active"})]}),(0,a.jsxs)("div",{className:"flex items-center gap-6 text-sm text-gray-600",children:[(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[a.jsx(l.$xp,{className:"w-4 h-4"}),(0,a.jsxs)("span",{children:[s?.employeeCount||0," employees"]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[a.jsx(l.Bge,{className:"w-4 h-4"}),(0,a.jsxs)("span",{children:["Plan Year ",(()=>{let e=new Date().getFullYear();return`${e}-${e+1}`})()]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[(0,a.jsxs)("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:[a.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"}),a.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 11a3 3 0 11-6 0 3 3 0 016 0z"})]}),a.jsx("span",{children:"San Francisco, CA"})]})]})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[a.jsx("div",{className:"bg-white rounded-xl p-6 border border-green-400 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[a.jsx("p",{className:"text-sm font-medium text-green-600",children:"active"===$?"Active Plans":"upcoming"===$?"Upcoming Plans":"Past Plans"}),a.jsx("p",{className:"text-3xl font-bold mt-2 text-green-900",children:"active"===$?ep.active.length:"upcoming"===$?ep.upcoming.length:ep.past.length}),a.jsx("p",{className:"text-xs mt-1 text-green-600",children:"active"===$?"Currently active":"upcoming"===$?"Starting soon":"Previously active"})]}),a.jsx("div",{className:"w-12 h-12 bg-gradient-to-br from-green-50 to-green-100 rounded-xl flex items-center justify-center shadow-lg",children:a.jsx(l.GwR,{className:"w-6 h-6 text-green-600"})})]})}),a.jsx("div",{className:"bg-white rounded-xl p-6 border border-blue-400 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[a.jsx("p",{className:"text-sm text-blue-600 font-medium",children:"Total Premium"}),(0,a.jsxs)("p",{className:"text-3xl font-bold text-blue-900 mt-2",children:["$",ex.toLocaleString()]}),a.jsx("p",{className:"text-xs text-blue-600 mt-1",children:"active"===$?"Current monthly cost":"upcoming"===$?"Future monthly cost":"Previous monthly cost"})]}),a.jsx("div",{className:"w-12 h-12 bg-gradient-to-br from-blue-50 to-blue-100 rounded-xl flex items-center justify-center shadow-lg",children:a.jsx(l.Tue,{className:"w-6 h-6 text-blue-600"})})]})}),a.jsx("div",{className:"bg-white rounded-xl p-6 border border-purple-400 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[a.jsx("p",{className:"text-sm text-purple-600 font-medium",children:"Employees"}),a.jsx("p",{className:"text-3xl font-bold text-purple-900 mt-2",children:s?.employeeCount||0}),a.jsx("p",{className:"text-xs text-purple-600 mt-1",children:"Total covered"})]}),a.jsx("div",{className:"w-12 h-12 bg-gradient-to-br from-purple-50 to-purple-100 rounded-xl flex items-center justify-center shadow-lg",children:a.jsx(l.$xp,{className:"w-6 h-6 text-purple-600"})})]})}),a.jsx("div",{className:"bg-white rounded-xl p-6 border border-orange-400 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[a.jsx("p",{className:"text-sm text-orange-600 font-medium",children:"Per Employee"}),(0,a.jsxs)("p",{className:"text-3xl font-bold text-orange-900 mt-2",children:["$",s?.employeeCount?Math.round(ex/s.employeeCount):0]}),a.jsx("p",{className:"text-xs text-orange-600 mt-1",children:"active"===$?"Current avg cost":"upcoming"===$?"Future avg cost":"Previous avg cost"})]}),a.jsx("div",{className:"w-12 h-12 bg-gradient-to-br from-orange-50 to-orange-100 rounded-xl flex items-center justify-center shadow-lg",children:a.jsx(l.Tue,{className:"w-6 h-6 text-orange-600"})})]})})]})]})}),a.jsx("div",{className:"bg-white ",children:a.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6",children:(0,a.jsxs)("div",{className:"flex flex-col lg:flex-row lg:items-center gap-4",children:[(0,a.jsxs)("div",{className:"relative flex-1",children:[a.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:a.jsx("svg",{className:"h-5 w-5 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:a.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})})}),a.jsx("input",{type:"text",placeholder:"Search plans by name, carrier, or type...",className:"block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-lg text-sm text-gray-900 placeholder-gray-500 bg-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"})]}),(0,a.jsxs)("div",{className:"flex items-center gap-3 flex-shrink-0",children:[(0,a.jsxs)("select",{className:"px-3 py-3 border border-gray-300 rounded-lg text-sm text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white",children:[a.jsx("option",{value:"all",children:"All Status"}),a.jsx("option",{value:"active",children:"Active"}),a.jsx("option",{value:"draft",children:"Draft"}),a.jsx("option",{value:"inactive",children:"Inactive"})]}),(0,a.jsxs)("select",{className:"px-3 py-3 border border-gray-300 rounded-lg text-sm text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white",children:[a.jsx("option",{value:"all",children:"All Types"}),a.jsx("option",{value:"medical",children:"Medical"}),a.jsx("option",{value:"dental",children:"Dental"}),a.jsx("option",{value:"vision",children:"Vision"})]}),(0,a.jsxs)("button",{className:"inline-flex items-center gap-2 px-3 py-3 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors",children:[a.jsx("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:a.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.707A1 1 0 013 7V4z"})}),"Filter"]})]})]})})}),a.jsx("div",{className:"bg-white",children:a.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-0",children:a.jsx("div",{className:"flex items-center justify-center",children:(0,a.jsxs)("div",{className:"relative bg-gray-100 rounded-full p-1 flex items-center",style:{width:"280px"},children:[a.jsx("div",{className:"absolute top-1 bottom-1 rounded-full transition-all duration-500 ease-out shadow-lg",style:{width:"90px",left:"active"===$?"4px":"upcoming"===$?"95px":"186px",backgroundColor:"active"===$?"#10b981":"upcoming"===$?"#3b82f6":"#6b7280"}}),a.jsx("button",{onClick:()=>_("active"),className:`relative z-10 flex-1 py-3 text-sm font-medium rounded-full transition-all duration-300 ease-in-out ${"active"===$?"text-white":"text-gray-600 hover:text-green-600"}`,children:"Active"}),a.jsx("button",{onClick:()=>_("upcoming"),className:`relative z-10 flex-1 py-3 text-sm font-medium rounded-full transition-all duration-300 ease-in-out ${"upcoming"===$?"text-white":"text-gray-600 hover:text-blue-600"}`,children:"Upcoming"}),a.jsx("button",{onClick:()=>_("past"),className:`relative z-10 flex-1 py-3 text-sm font-medium rounded-full transition-all duration-300 ease-in-out ${"past"===$?"text-white":"text-gray-600 hover:text-gray-700"}`,children:"Past"})]})})})}),a.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:(0,a.jsxs)("div",{className:"bg-white rounded-2xl shadow-xl border border-gray-200 overflow-hidden",children:[a.jsx("div",{className:"px-6 py-4 bg-gradient-to-r from-gray-50 to-gray-100 border-b border-gray-200",children:(0,a.jsxs)("div",{className:"grid grid-cols-12 gap-4 text-sm font-semibold text-gray-700 uppercase tracking-wide",children:[a.jsx("div",{className:"col-span-3 text-left",children:"Plan Details"}),a.jsx("div",{className:"col-span-2 text-center",children:"Coverage Type"}),a.jsx("div",{className:"col-span-1 text-center",children:"Status"}),a.jsx("div",{className:"col-span-2 text-center",children:"Enrollment Period"}),a.jsx("div",{className:"col-span-2 text-center",children:"Employee Cost"}),a.jsx("div",{className:"col-span-2 text-center",children:"Actions"})]})}),a.jsx("div",{className:"divide-y divide-gray-200",children:0===eg.length?(0,a.jsxs)("div",{className:"px-6 py-16 text-center",children:[a.jsx("div",{className:"w-20 h-20 mx-auto bg-gray-100 rounded-full flex items-center justify-center mb-6",children:a.jsx(l.GwR,{className:"h-10 w-10 text-gray-400"})}),(0,a.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:["active"===$&&"No active plans","upcoming"===$&&"No upcoming plans","past"===$&&"No past plans"]}),(0,a.jsxs)("p",{className:"text-gray-500 mb-8 max-w-sm mx-auto",children:["active"===$&&"There are no plans currently active for this company. Add a new plan to get started.","upcoming"===$&&"There are no plans scheduled to start in the future for this company.","past"===$&&"There are no expired or past plans for this company."]}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("button",{onClick:en,className:"inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-lg text-white bg-gradient-to-r from-blue-600 to-purple-600 hover:opacity-90 transition-all shadow-lg hover:shadow-xl",children:[a.jsx(l.r7I,{className:"-ml-1 mr-2 h-5 w-5"}),"Add First Plan"]}),a.jsx("div",{className:"text-sm text-gray-400",children:"or browse available plans to assign"})]})]}):eg.map(e=>{let t=e.coverageTiers?.[0]?.employeeCost||0,s=e.enrollmentStartDate?new Date(e.enrollmentStartDate).toLocaleDateString():"N/A",r=e.enrollmentEndDate?new Date(e.enrollmentEndDate).toLocaleDateString():"N/A";return a.jsx("div",{className:"px-6 py-5 hover:bg-gray-50 transition-all duration-200",children:(0,a.jsxs)("div",{className:"grid grid-cols-12 gap-4 items-center",children:[a.jsx("div",{className:"col-span-3",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[a.jsx("div",{className:"flex-shrink-0",children:(e=>{switch(e?.toLowerCase()){case"medical":case"health":return a.jsx(l.GwR,{className:"w-5 h-5 text-blue-600"});case"dental":return a.jsx("svg",{className:"w-5 h-5 text-green-600",fill:"currentColor",viewBox:"0 0 20 20",children:a.jsx("path",{d:"M10 2C6.686 2 4 4.686 4 8c0 1.5.5 3 1.5 4.5L10 18l4.5-5.5C15.5 11 16 9.5 16 8c0-3.314-2.686-6-6-6z"})});case"vision":return(0,a.jsxs)("svg",{className:"w-5 h-5 text-purple-600",fill:"currentColor",viewBox:"0 0 20 20",children:[a.jsx("path",{d:"M10 12a2 2 0 100-4 2 2 0 000 4z"}),a.jsx("path",{fillRule:"evenodd",d:"M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z",clipRule:"evenodd"})]});default:return a.jsx(l.$xp,{className:"w-5 h-5 text-gray-600"})}})(e.coverageType)}),(0,a.jsxs)("div",{className:"min-w-0 flex-1",children:[a.jsx("div",{className:"font-semibold text-gray-700 truncate",children:e.planName}),(0,a.jsxs)("div",{className:"text-sm text-gray-500 truncate",children:[e.carrier," • ",e.planCode]})]})]})}),a.jsx("div",{className:"col-span-2 text-center",children:(0,a.jsxs)("div",{className:"flex flex-col gap-1 items-center",children:[a.jsx("span",{className:"text-sm font-medium text-gray-700",children:e.coverageType}),e.metalTier&&a.jsx("span",{className:"text-xs font-medium text-gray-600",children:e.metalTier})]})}),a.jsx("div",{className:"col-span-1 text-center",children:a.jsx("div",{className:"flex justify-center",children:(0,a.jsxs)("span",{className:`inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold ${(e=>{switch(e?.toLowerCase()){case"active":return"bg-green-100 text-green-800";case"draft":return"bg-yellow-100 text-yellow-800";case"inactive":return"bg-red-100 text-red-800";default:return"bg-gray-100 text-gray-800"}})(e.status)}`,children:[a.jsx("div",{className:`w-2 h-2 rounded-full mr-2 ${e.status?.toLowerCase()==="active"?"bg-green-500":e.status?.toLowerCase()==="draft"?"bg-yellow-500":"bg-red-500"}`}),e.status]})})}),(0,a.jsxs)("div",{className:"col-span-2 text-center",children:[a.jsx("div",{className:"text-sm font-medium text-gray-700",children:s}),(0,a.jsxs)("div",{className:"text-xs text-gray-500",children:["to ",r]})]}),(0,a.jsxs)("div",{className:"col-span-2 text-center",children:[(0,a.jsxs)("div",{className:"text-lg font-bold text-gray-700",children:["$",t]}),a.jsx("div",{className:"text-xs text-gray-500",children:"per month"})]}),a.jsx("div",{className:"col-span-2",children:(0,a.jsxs)("div",{className:"flex items-center justify-center space-x-2",children:[a.jsx("button",{onClick:()=>ec(e),className:"px-3 py-1 text-xs font-medium text-white bg-red-500 hover:bg-red-600 rounded transition-all duration-200",title:"Renew Plan for Next Year",children:"Renew"}),a.jsx("button",{onClick:()=>eu(e),className:"p-2 text-blue-600 hover:text-blue-800 hover:bg-blue-100 rounded-lg transition-all duration-200",title:"View Plan Assignment Details",children:a.jsx(l.Vvo,{className:"w-4 h-4"})}),e.canEdit&&a.jsx("button",{onClick:()=>ed(e),className:"p-2 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-lg transition-all duration-200",title:"Edit Plan Assignment",children:a.jsx(l._vs,{className:"w-4 h-4"})}),a.jsx("button",{onClick:()=>em(e),className:"p-2 text-red-600 hover:text-red-800 hover:bg-red-100 rounded-lg transition-all duration-200",title:"Delete Plan Assignment",children:a.jsx(l.Bhs,{className:"w-4 h-4"})})]})})]})},e._id)})})]})}),q&&a.jsx(m,{isOpen:q,onClose:()=>{L(!1),w(null),R([])},onSelectPlan:el,onCreatePlan:ei,companyId:t,editMode:!!N,editingPlan:N,editingCoverageTiers:z}),U&&O&&a.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg p-6 max-w-2xl w-full mx-4 max-h-[80vh] overflow-y-auto",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[a.jsx("h2",{className:"text-xl font-bold text-gray-900",children:"Plan Assignment Details"}),a.jsx("button",{onClick:()=>G(!1),className:"text-gray-400 hover:text-gray-600",children:a.jsx("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:a.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]}),a.jsx("div",{className:"space-y-4",children:(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700",children:"Plan Name"}),a.jsx("p",{className:"text-sm text-gray-900",children:O.planName})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700",children:"Plan Code"}),a.jsx("p",{className:"text-sm text-gray-900",children:O.planCode})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700",children:"Carrier"}),a.jsx("p",{className:"text-sm text-gray-900",children:O.carrier})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700",children:"Status"}),a.jsx("p",{className:"text-sm text-gray-900",children:O.status})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700",children:"Effective Date"}),a.jsx("p",{className:"text-sm text-gray-900",children:O.planEffectiveDate?new Date(O.planEffectiveDate).toLocaleDateString():"N/A"})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700",children:"End Date"}),a.jsx("p",{className:"text-sm text-gray-900",children:O.planEndDate?new Date(O.planEndDate).toLocaleDateString():"N/A"})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700",children:"Coverage Type"}),a.jsx("p",{className:"text-sm text-gray-900",children:O.coverageType||O.type})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700",children:"Assignment ID"}),a.jsx("p",{className:"text-sm text-gray-900 font-mono",children:O._id})]})]})}),a.jsx("div",{className:"mt-6 flex justify-end",children:a.jsx("button",{onClick:()=>G(!1),className:"px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600 transition-colors",children:"Close"})})]})}),a.jsx(o,{isOpen:W.isOpen,onClose:Z,type:W.type,title:W.title,message:W.message,confirmText:W.confirmText,onConfirm:W.onConfirm})]})]})}},93250:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});let a=(0,s(68570).createProxy)(String.raw`C:\beno\project_dev\qharmony-frontend-v1\src\app\ai-enroller\manage-groups\company\[companyId]\plans\page.tsx#default`)}};var t=require("../../../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),a=t.X(0,[8948,1183,6621,9066,1999,8492,3253,3351,576,6305,9902,8618,8581],()=>s(29441));module.exports=a})();