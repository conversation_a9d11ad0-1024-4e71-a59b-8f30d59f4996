(()=>{var e={};e.id=6574,e.ids=[6574],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},27790:e=>{"use strict";e.exports=require("assert")},84770:e=>{"use strict";e.exports=require("crypto")},17702:e=>{"use strict";e.exports=require("events")},92048:e=>{"use strict";e.exports=require("fs")},32615:e=>{"use strict";e.exports=require("http")},35240:e=>{"use strict";e.exports=require("https")},19801:e=>{"use strict";e.exports=require("os")},55315:e=>{"use strict";e.exports=require("path")},76162:e=>{"use strict";e.exports=require("stream")},74026:e=>{"use strict";e.exports=require("string_decoder")},74175:e=>{"use strict";e.exports=require("tty")},17360:e=>{"use strict";e.exports=require("url")},21764:e=>{"use strict";e.exports=require("util")},71568:e=>{"use strict";e.exports=require("zlib")},98061:e=>{"use strict";e.exports=require("node:assert")},92761:e=>{"use strict";e.exports=require("node:async_hooks")},72254:e=>{"use strict";e.exports=require("node:buffer")},40027:e=>{"use strict";e.exports=require("node:console")},6005:e=>{"use strict";e.exports=require("node:crypto")},65714:e=>{"use strict";e.exports=require("node:diagnostics_channel")},15673:e=>{"use strict";e.exports=require("node:events")},88849:e=>{"use strict";e.exports=require("node:http")},42725:e=>{"use strict";e.exports=require("node:http2")},87503:e=>{"use strict";e.exports=require("node:net")},38846:e=>{"use strict";e.exports=require("node:perf_hooks")},39630:e=>{"use strict";e.exports=require("node:querystring")},84492:e=>{"use strict";e.exports=require("node:stream")},31764:e=>{"use strict";e.exports=require("node:tls")},41041:e=>{"use strict";e.exports=require("node:url")},47261:e=>{"use strict";e.exports=require("node:util")},93746:e=>{"use strict";e.exports=require("node:util/types")},24086:e=>{"use strict";e.exports=require("node:worker_threads")},65628:e=>{"use strict";e.exports=require("node:zlib")},73605:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>o.a,__next_app__:()=>p,originalPathname:()=>m,pages:()=>d,routeModule:()=>u,tree:()=>c}),s(65453),s(6079),s(33709),s(35866);var r=s(23191),a=s(88716),n=s(37922),o=s.n(n),l=s(95231),i={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>l[e]);s.d(t,i);let c=["",{children:["ai-enroller",{children:["manage-groups",{children:["company",{children:["[companyId]",{children:["review",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,65453)),"C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\manage-groups\\company\\[companyId]\\review\\page.tsx"]}]},{}]},{}]},{}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,6079)),"C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,33709)),"C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,35866,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],d=["C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\manage-groups\\company\\[companyId]\\review\\page.tsx"],m="/ai-enroller/manage-groups/company/[companyId]/review/page",p={require:s,loadChunk:()=>Promise.resolve()},u=new r.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/ai-enroller/manage-groups/company/[companyId]/review/page",pathname:"/ai-enroller/manage-groups/company/[companyId]/review",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},37830:(e,t,s)=>{Promise.resolve().then(s.bind(s,3960))},6283:(e,t,s)=>{"use strict";s.d(t,{Z:()=>c});var r=s(96830),a=s(5028),n=s(5283),o=s(14750);let l=(0,s(71685).Z)("MuiBox",["root"]),i=(0,n.Z)(),c=(0,r.default)({themeId:o.Z,defaultTheme:i,defaultClassName:l.root,generateClassName:a.Z.generate})},98139:(e,t,s)=>{"use strict";s.d(t,{Z:()=>S});var r=s(17577),a=s(41135),n=s(88634),o=s(8106),l=s(91703),i=s(13643),c=s(2791),d=s(54641),m=s(40955),p=s(71685),u=s(97898);function x(e){return(0,u.ZP)("MuiCircularProgress",e)}(0,p.Z)("MuiCircularProgress",["root","determinate","indeterminate","colorPrimary","colorSecondary","svg","circle","circleDeterminate","circleIndeterminate","circleDisableShrink"]);var y=s(10326);let g=(0,o.F4)`
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
`,h=(0,o.F4)`
  0% {
    stroke-dasharray: 1px, 200px;
    stroke-dashoffset: 0;
  }

  50% {
    stroke-dasharray: 100px, 200px;
    stroke-dashoffset: -15px;
  }

  100% {
    stroke-dasharray: 1px, 200px;
    stroke-dashoffset: -126px;
  }
`,f="string"!=typeof g?(0,o.iv)`
        animation: ${g} 1.4s linear infinite;
      `:null,b="string"!=typeof h?(0,o.iv)`
        animation: ${h} 1.4s ease-in-out infinite;
      `:null,N=e=>{let{classes:t,variant:s,color:r,disableShrink:a}=e,o={root:["root",s,`color${(0,d.Z)(r)}`],svg:["svg"],circle:["circle",`circle${(0,d.Z)(s)}`,a&&"circleDisableShrink"]};return(0,n.Z)(o,x,t)},v=(0,l.default)("span",{name:"MuiCircularProgress",slot:"Root",overridesResolver:(e,t)=>{let{ownerState:s}=e;return[t.root,t[s.variant],t[`color${(0,d.Z)(s.color)}`]]}})((0,i.Z)(({theme:e})=>({display:"inline-block",variants:[{props:{variant:"determinate"},style:{transition:e.transitions.create("transform")}},{props:{variant:"indeterminate"},style:f||{animation:`${g} 1.4s linear infinite`}},...Object.entries(e.palette).filter((0,m.Z)()).map(([t])=>({props:{color:t},style:{color:(e.vars||e).palette[t].main}}))]}))),j=(0,l.default)("svg",{name:"MuiCircularProgress",slot:"Svg",overridesResolver:(e,t)=>t.svg})({display:"block"}),w=(0,l.default)("circle",{name:"MuiCircularProgress",slot:"Circle",overridesResolver:(e,t)=>{let{ownerState:s}=e;return[t.circle,t[`circle${(0,d.Z)(s.variant)}`],s.disableShrink&&t.circleDisableShrink]}})((0,i.Z)(({theme:e})=>({stroke:"currentColor",variants:[{props:{variant:"determinate"},style:{transition:e.transitions.create("stroke-dashoffset")}},{props:{variant:"indeterminate"},style:{strokeDasharray:"80px, 200px",strokeDashoffset:0}},{props:({ownerState:e})=>"indeterminate"===e.variant&&!e.disableShrink,style:b||{animation:`${h} 1.4s ease-in-out infinite`}}]}))),S=r.forwardRef(function(e,t){let s=(0,c.i)({props:e,name:"MuiCircularProgress"}),{className:r,color:n="primary",disableShrink:o=!1,size:l=40,style:i,thickness:d=3.6,value:m=0,variant:p="indeterminate",...u}=s,x={...s,color:n,disableShrink:o,size:l,thickness:d,value:m,variant:p},g=N(x),h={},f={},b={};if("determinate"===p){let e=2*Math.PI*((44-d)/2);h.strokeDasharray=e.toFixed(3),b["aria-valuenow"]=Math.round(m),h.strokeDashoffset=`${((100-m)/100*e).toFixed(3)}px`,f.transform="rotate(-90deg)"}return(0,y.jsx)(v,{className:(0,a.Z)(g.root,r),style:{width:l,height:l,...f,...i},ownerState:x,ref:t,role:"progressbar",...b,...u,children:(0,y.jsx)(j,{className:g.svg,ownerState:x,viewBox:"22 22 44 44",children:(0,y.jsx)(w,{className:g.circle,style:h,ownerState:x,cx:44,cy:44,r:(44-d)/2,fill:"none",strokeWidth:d})})})})},2791:(e,t,s)=>{"use strict";s.d(t,{i:()=>a}),s(17577);var r=s(51387);function a(e){return(0,r.i)(e)}s(10326)},54641:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});let r=s(96005).Z},40955:(e,t,s)=>{"use strict";function r(e=[]){return([,t])=>t&&function(e,t=[]){if("string"!=typeof e.main)return!1;for(let s of t)if(!e.hasOwnProperty(s)||"string"!=typeof e[s])return!1;return!0}(t,e)}s.d(t,{Z:()=>r})},13643:(e,t,s)=>{"use strict";s.d(t,{Z:()=>n});var r=s(15966);let a={theme:void 0},n=function(e){let t,s;return function(n){let o=t;return(void 0===o||n.theme!==s)&&(a.theme=n.theme,t=o=(0,r.Z)(e(a)),s=n.theme),o}}},3960:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>c});var r=s(10326),a=s(17577),n=s(35047),o=s(38492),l=s(43933),i=s(89009);function c(){let e=(0,n.useRouter)(),t=(0,n.useParams)(),s=(0,n.useSearchParams)(),c=t.companyId,d=s.get("assignments")?.split(",")||[],[m,p]=(0,a.useState)(null),[u,x]=(0,a.useState)([]),[y,g]=(0,a.useState)(!0),h=async e=>{try{let t=await fetch(`http://localhost:8080/api/pre-enrollment/plans/${e}`,{headers:{"Content-Type":"application/json","user-id":(()=>{let e=localStorage.getItem("userid1")||localStorage.getItem("userId");if(!e)throw Error("User ID not found. Please authenticate first.");return e})()}});if(t.ok){let e=await t.json();return{planName:e.plan?.planName||"Unknown Plan",planCode:e.plan?.planCode||"N/A",planType:e.plan?.planType||"N/A",coverageType:e.plan?.coverageType||"Unknown",coverageSubTypes:e.plan?.coverageSubTypes||[],metalTier:e.plan?.metalTier||"",carrierName:e.carrier?.carrierName||"Unknown Carrier"}}}catch(t){console.error("Error fetching plan details for planId:",e,t)}return{planName:"Unknown Plan",planCode:"N/A",planType:"N/A",coverageType:"Unknown",coverageSubTypes:[],metalTier:"",carrierName:"Unknown Carrier"}},f=async e=>{try{console.log("Fetching plan assignment details for ID:",e);let t=await fetch(`http://localhost:8080/api/pre-enrollment/plan-assignments/${e}`,{headers:{"Content-Type":"application/json","user-id":localStorage.getItem("userid1")||localStorage.getItem("userId")||"6838677aef6db0212bcfdacd"}});if(t.ok){let e=await t.json();return e.assignment._doc||e.assignment}console.error("Failed to fetch plan assignment details. Status:",t.status)}catch(e){console.error("Error fetching plan assignment details:",e)}return null},b=async e=>{let t=(0,i.bR)(),s=(0,i.n5)();try{let r=await fetch(`${t}/employee/company-details`,{headers:{"Content-Type":"application/json","user-id":s}});if(r.ok){let t=await r.json();if(t.company&&t.company.isBrokerage&&t.company._id===e)return console.log("Found broker's own company with employee count:",t.company.companySize),{_id:t.company._id,companyName:t.company.name||"Unknown Company",employeeCount:t.company.companySize||250}}let a=await fetch(`${t}/admin/all-companies`,{headers:{"Content-Type":"application/json","user-id":s}});if(a.ok){let t=await a.json(),s=t.companies?.find(t=>t._id===e);if(s)return console.log("Found client company with employee count:",s.companySize),{_id:s._id,companyName:s.name||"Unknown Company",employeeCount:s.companySize||250}}}catch(e){console.error("Error fetching company details:",e)}return{_id:e,companyName:"Unknown Company",employeeCount:250}};(0,a.useCallback)(async()=>{try{g(!0);let e=await b(c);p(e);let t=await (0,l.fH)(c,{includePlanData:!0});if(t.success&&t.data){let e=t.data.assignments;console.log("All assignments:",e.length),console.log("Selected assignment IDs from URL:",d);let s=e;d.length>0&&(s=e.filter(e=>d.includes(e._id))),console.log("Filtered assignments for review:",s.length);let r=await Promise.all(s.map(async e=>{let t="string"==typeof e.planId?e.planId:e.planId?._id||"";console.log("Processing assignment:",e._id,"with planId:",t);let s=null;t&&(s=await h(t),console.log("Fetched plan details:",s));let r=await f(e._id);console.log("Fetched assignment details:",r);let a=r?.coverageTiers||e.coverageTiers||[],n=a.find(e=>e.tierName?.toLowerCase().includes("employee only")||e.tierName?.toLowerCase()==="employee")||a[0]||{},o=n.totalCost||0,l=n.employerCost||0,i=n.employeeCost||o-l,c="Medical";if(s?.coverageSubTypes&&s.coverageSubTypes.length>0){let e=s.coverageSubTypes[0].toLowerCase();e.includes("dental")?c="Dental":e.includes("vision")&&(c="Vision")}else if(s?.coverageType){let e=s.coverageType.toLowerCase();e.includes("dental")?c="Dental":e.includes("vision")&&(c="Vision")}return{_id:e._id,planName:s?.planName||"Unknown Plan",carrier:s?.carrierName||"Unknown Carrier",type:c,planCode:s?.planCode||"N/A",totalMonthlyPremium:o,employerContribution:l,employeeContribution:i,isNew:"Draft"===e.status,isUpdated:"Active"===e.status,changes:"Draft"===e.status?["New plan added"]:["Plan configuration updated"]}}));console.log("Enhanced plans for review:",r),x(r)}else console.error("Failed to fetch plan assignments:",t.error),x([])}catch(e){console.error("Error fetching data:",e),p({_id:c,companyName:"TechCorp Inc.",employeeCount:250}),x([])}finally{g(!1)}},[c]);let N=m?.employeeCount||250,v=u.reduce((e,t)=>e+(t.employerContribution||0),0);u.reduce((e,t)=>e+(t.employeeContribution||0),0),u.reduce((e,t)=>e+(t.totalMonthlyPremium||0),0);let j=v*N;return(console.log("Cost Calculation Debug:"),console.log("Employee Count:",N),console.log("Sum of all plans employer cost (per employee):",v),console.log("Total company monthly employer cost:",j),console.log("Average cost per employee:",v),y)?r.jsx("div",{className:"min-h-screen bg-white flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center",children:[r.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-black mx-auto"}),r.jsx("p",{className:"mt-4 text-gray-600",children:"Loading review..."})]})}):(0,r.jsxs)("div",{className:"min-h-screen bg-white",children:[r.jsx("div",{className:"bg-white border-b border-gray-200 px-6 py-4",children:(0,r.jsxs)("div",{className:"max-w-7xl mx-auto",children:[(0,r.jsxs)("div",{className:"flex gap-3 overflow-x-auto",children:[(0,r.jsxs)("button",{onClick:()=>e.push("/ai-enroller"),className:"flex items-center gap-2 px-4 py-2 rounded-full text-sm font-medium transition-all bg-purple-100 text-purple-700 hover:bg-purple-200 whitespace-nowrap",style:{fontFamily:"'SF Pro', 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif",fontSize:"13px",fontWeight:"450",lineHeight:"1.2"},children:[r.jsx(o.VRM,{className:"w-4 h-4"}),"Home",r.jsx("span",{className:"flex items-center justify-center w-4 h-4 bg-purple-600 text-white rounded-full ml-2",children:r.jsx(o.PjL,{className:"w-5 h-5"})})]}),(0,r.jsxs)("button",{onClick:()=>e.push("/ai-enroller/manage-groups/select-company"),className:"flex items-center gap-2 px-4 py-2 rounded-full text-sm font-medium transition-all bg-purple-100 text-purple-700 hover:bg-purple-200 whitespace-nowrap",style:{fontFamily:"'SF Pro', 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif",fontSize:"13px",fontWeight:"450",lineHeight:"1.2"},children:[r.jsx(o.$xp,{className:"w-4 h-4"}),"Select Company",r.jsx("span",{className:"flex items-center justify-center w-4 h-4 bg-purple-600 text-white rounded-full ml-2",children:r.jsx(o.PjL,{className:"w-5 h-5"})})]}),(0,r.jsxs)("button",{onClick:()=>e.push(`/ai-enroller/manage-groups/company/${c}/plans`),className:"flex items-center gap-2 px-4 py-2 rounded-full text-sm font-medium transition-all bg-purple-100 text-purple-700 hover:bg-purple-200 whitespace-nowrap",style:{fontFamily:"'SF Pro', 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif",fontSize:"13px",fontWeight:"450",lineHeight:"1.2"},children:[r.jsx(o.GwR,{className:"w-4 h-4"}),"View Plans",r.jsx("span",{className:"flex items-center justify-center w-4 h-4 bg-purple-600 text-white rounded-full ml-2",children:r.jsx(o.PjL,{className:"w-5 h-5"})})]}),(0,r.jsxs)("button",{onClick:()=>e.push(`/ai-enroller/manage-groups/company/${c}/set-dates`),className:"flex items-center gap-2 px-4 py-2 rounded-full text-sm font-medium transition-all bg-purple-100 text-purple-700 hover:bg-purple-200 whitespace-nowrap",style:{fontFamily:"'SF Pro', 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif",fontSize:"13px",fontWeight:"450",lineHeight:"1.2"},children:[r.jsx(o.Bge,{className:"w-4 h-4"}),"Set Dates",r.jsx("span",{className:"flex items-center justify-center w-4 h-4 bg-purple-600 text-white rounded-full ml-2",children:r.jsx(o.PjL,{className:"w-5 h-5"})})]}),(0,r.jsxs)("div",{className:"flex items-center gap-2 px-4 py-2 rounded-full text-sm font-medium bg-purple-50 text-purple-600 whitespace-nowrap",style:{fontFamily:"'SF Pro', 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif",fontSize:"13px",fontWeight:"450",lineHeight:"1.2"},children:[r.jsx(o.PjL,{className:"w-4 h-4"}),"Review"]}),(0,r.jsxs)("div",{className:"flex items-center gap-2 px-4 py-2 rounded-full text-sm font-medium bg-gray-100 text-gray-600 whitespace-nowrap",style:{fontFamily:"'SF Pro', 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif",fontSize:"13px",fontWeight:"450",lineHeight:"1.2"},children:[r.jsx(o.PjL,{className:"w-4 h-4"}),"Confirmation"]})]}),r.jsx("div",{className:"mt-4",children:(0,r.jsxs)("div",{className:"text-right text-sm text-gray-500",children:["Step 5 of 6",r.jsx("br",{}),r.jsx("span",{className:"text-xs text-purple-600 font-medium",children:"Final review"})]})})]})}),(0,r.jsxs)("div",{className:"max-w-6xl mx-auto px-6 py-8 bg-white",children:[(0,r.jsxs)("div",{className:"mb-8",children:[r.jsx("h1",{className:"text-2xl font-bold text-gray-900 mb-2",children:"Final Review & Confirmation"}),(0,r.jsxs)("p",{className:"text-gray-600",children:["Review all changes before saving for ",m?.companyName]})]}),r.jsx("div",{className:"bg-green-50 border border-green-200 rounded-xl p-4 mb-8",children:(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[r.jsx(o.PjL,{className:"text-green-600 w-5 h-5"}),r.jsx("span",{className:"text-green-800 font-medium",children:"Ready to save!"}),r.jsx("span",{className:"text-green-700",children:"Review the changes below and confirm when ready."})]})}),(0,r.jsxs)("div",{className:"bg-white border border-gray-200 rounded-xl p-6 mb-8",children:[(0,r.jsxs)("div",{className:"flex items-center gap-3 mb-6",children:[r.jsx(o.PjL,{className:"text-gray-600 w-6 h-6"}),(0,r.jsxs)("div",{children:[r.jsx("h2",{className:"text-xl font-semibold text-gray-900",children:"Plan Summary"}),r.jsx("p",{className:"text-sm text-gray-600",children:"Side-by-side comparison of new vs. existing plans"})]})]}),r.jsx("div",{className:"space-y-6",children:u.map(e=>(0,r.jsxs)("div",{className:"border-l-4 border-blue-500 pl-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,r.jsxs)("div",{children:[r.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:e.planName}),(0,r.jsxs)("p",{className:"text-sm text-gray-600",children:[e.carrier," • ",e.type]})]}),(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[e.isUpdated&&r.jsx("span",{className:"bg-blue-100 text-blue-800 text-xs px-2.5 py-1 rounded-xl border border-blue-200 font-medium",children:"Updated"}),e.isNew&&r.jsx("span",{className:"bg-green-100 text-green-800 text-xs px-2.5 py-1 rounded-md border border-green-200 font-medium",children:"New"}),r.jsx("button",{className:"text-blue-600 hover:text-blue-700 p-1",children:r.jsx(o._vs,{className:"w-4 h-4"})})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-3 gap-4 mb-4",children:[(0,r.jsxs)("div",{className:"text-center p-3 bg-gray-50 rounded-xl",children:[r.jsx("p",{className:"text-xs text-gray-500 mb-1",children:"Total Monthly Premium"}),(0,r.jsxs)("p",{className:"text-lg font-semibold text-gray-900",children:["$",e.totalMonthlyPremium?.toFixed(2)]})]}),(0,r.jsxs)("div",{className:"text-center p-3 bg-green-50 rounded-xl",children:[r.jsx("p",{className:"text-xs text-gray-500 mb-1",children:"Employer Contribution"}),(0,r.jsxs)("p",{className:"text-lg font-semibold text-green-600",children:["$",e.employerContribution?.toFixed(2)]})]}),(0,r.jsxs)("div",{className:"text-center p-3 bg-blue-50 rounded-xl",children:[r.jsx("p",{className:"text-xs text-gray-500 mb-1",children:"Employee Contribution"}),(0,r.jsxs)("p",{className:"text-lg font-semibold text-blue-600",children:["$",e.employeeContribution?.toFixed(2)]})]})]}),e.changes&&e.changes.length>0&&(0,r.jsxs)("div",{className:"bg-blue-50 rounded-xl p-3",children:[r.jsx("p",{className:"text-sm font-medium text-blue-900 mb-2",children:"Changes Made:"}),r.jsx("ul",{className:"space-y-1",children:e.changes.map((e,t)=>(0,r.jsxs)("li",{className:"text-sm text-blue-800 flex items-center gap-2",children:[r.jsx("span",{className:"w-1 h-1 bg-blue-600 rounded-full"}),e]},t))})]})]},e._id))})]}),(0,r.jsxs)("div",{className:"bg-white border border-gray-200 rounded-xl p-6 mb-8",children:[(0,r.jsxs)("div",{className:"flex items-center gap-3 mb-6",children:[r.jsx(o.Otr,{className:"text-gray-600 w-6 h-6"}),(0,r.jsxs)("div",{children:[r.jsx("h2",{className:"text-xl font-semibold text-gray-900",children:"Company Impact Summary"}),(0,r.jsxs)("p",{className:"text-sm text-gray-600",children:["Total cost impact for ",m?.companyName]})]})]}),(0,r.jsxs)("div",{className:"bg-gray-50 rounded-xl p-4 mb-6 text-xs",children:[r.jsx("h4",{className:"font-medium text-gray-700 mb-2",children:"Calculation Breakdown:"}),(0,r.jsxs)("div",{className:"space-y-1 text-gray-600",children:[(0,r.jsxs)("p",{children:["• Company has ",N," employees"]}),(0,r.jsxs)("p",{children:["• Plans selected: ",u.length]}),u.map((e,t)=>(0,r.jsxs)("p",{children:["• ",e.planName,": $",e.employerContribution?.toFixed(2)||"0.00"," employer cost per employee"]},t)),(0,r.jsxs)("p",{className:"font-medium pt-2",children:["• Total per employee: $",v.toFixed(2)]}),(0,r.jsxs)("p",{className:"font-medium",children:["• Total for company: $",v.toFixed(2)," \xd7 ",N," = $",j.toFixed(2)]})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-3 gap-6",children:[(0,r.jsxs)("div",{className:"border-l-4 border-green-500 pl-4",children:[r.jsx("p",{className:"text-sm text-gray-600 mb-1",children:"Total Monthly Employer Cost"}),(0,r.jsxs)("p",{className:"text-2xl font-bold text-green-600",children:["$",j.toFixed(2)]}),(0,r.jsxs)("p",{className:"text-xs text-green-600",children:["$",v.toFixed(2)," per employee \xd7 ",N," employees"]})]}),(0,r.jsxs)("div",{className:"border-l-4 border-blue-500 pl-4",children:[r.jsx("p",{className:"text-sm text-gray-600 mb-1",children:"Total Annual Employer Cost"}),(0,r.jsxs)("p",{className:"text-2xl font-bold text-blue-600",children:["$",(12*j).toFixed(2)]}),r.jsx("p",{className:"text-xs text-blue-600",children:"Projected for full year"})]}),(0,r.jsxs)("div",{className:"border-l-4 border-orange-500 pl-4",children:[r.jsx("p",{className:"text-sm text-gray-600 mb-1",children:"Average Cost per Employee"}),(0,r.jsxs)("p",{className:"text-2xl font-bold text-orange-600",children:["$",v.toFixed(2)]}),r.jsx("p",{className:"text-xs text-orange-600",children:"Sum of all plans for Employee Only tier"})]})]})]}),r.jsx("div",{className:"bg-green-50 border border-green-200 rounded-xl p-4 mb-8",children:(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[r.jsx(o.PjL,{className:"text-green-600 w-5 h-5"}),r.jsx("span",{className:"text-green-800 font-medium",children:"Validation Check:"}),r.jsx("span",{className:"text-green-700",children:"All contribution amounts are within acceptable ranges. No issues detected."})]})}),(0,r.jsxs)("div",{className:"flex gap-4 mb-8",children:[(0,r.jsxs)("button",{onClick:()=>{e.push(`/ai-enroller/manage-groups/company/${c}/plans`)},className:"px-6 py-2 border border-gray-300 text-gray-700 rounded-xl hover:bg-gray-50 transition-colors flex items-center gap-2",children:[r.jsx(o.Vvo,{className:"w-4 h-4"}),"View Plans Page"]}),(0,r.jsxs)("button",{onClick:()=>{alert("Summary downloaded successfully!")},className:"px-6 py-2 border border-gray-300 text-gray-700 rounded-xl hover:bg-gray-50 transition-colors flex items-center gap-2",children:[r.jsx(o.yFZ,{className:"w-4 h-4"}),"Download Summary"]}),(0,r.jsxs)("button",{onClick:()=>{let t=u.map(e=>e._id);e.push(`/ai-enroller/manage-groups/company/${c}/confirmation?assignments=${t.join(",")}`)},className:"flex-1 px-6 py-2 bg-black text-white rounded-xl hover:bg-gray-800 transition-colors flex items-center justify-center gap-2",children:[r.jsx(o.PjL,{className:"w-4 h-4"}),"Confirm and Save"]})]}),r.jsx("div",{className:"bg-blue-50 border border-blue-200 rounded-xl p-4",children:(0,r.jsxs)("div",{className:"flex items-start gap-2",children:[r.jsx("div",{className:"w-5 h-5 bg-blue-500 rounded-full flex items-center justify-center mt-0.5",children:r.jsx("span",{className:"text-white text-xs",children:"!"})}),(0,r.jsxs)("div",{children:[r.jsx("span",{className:"font-medium text-blue-800",children:"Important:"}),(0,r.jsxs)("span",{className:"text-blue-700",children:[" Once confirmed, these changes will be applied to ",m?.companyName,"'s benefit plans. Plan documents and employee communications should be updated accordingly."]})]})]})})]})]})}},43058:(e,t,s)=>{"use strict";s.d(t,{Z:()=>m});var r=s(10326),a=s(17577),n=s(22758),o=s(35047),l=s(31870);s(32049),s(94638);var i=s(98139),c=s(6283);let d=()=>/Mobi|Android/i.test(navigator.userAgent),m=({children:e})=>{let{user:t,loading:s}=(0,n.a)(),m=(0,o.useRouter)(),p=(0,o.usePathname)(),u=(0,l.T)(),[x,y]=(0,a.useState)(!1),g=(0,l.C)(e=>e.user.userProfile);return((0,a.useEffect)(()=>{},[u,g.name]),(0,a.useEffect)(()=>{console.log("ProtectedRoute useEffect triggered"),console.log("Current user: ",t),console.log("Loading state: ",s),console.log("Current user details: ",g),s||t||(console.log("User not authenticated, redirecting to home"),y(!1),m.push("/")),!s&&g.companyId&&""===g.companyId&&(console.log("Waiting to retrieve company details"),y(!1)),!s&&g.companyId&&""!==g.companyId&&(console.log("User found, rendering children"),y(!0)),d()&&!p.startsWith("/mobile")&&(console.log(`Redirecting to mobile version of ${p}`),m.push(`/mobile${p}`))},[t,s,g,m,p]),x)?t?r.jsx(r.Fragment,{children:e}):null:r.jsx(c.Z,{sx:{display:"flex",justifyContent:"center",alignItems:"center",height:"100vh",bgcolor:"#f6f8fc"},children:r.jsx(i.Z,{})})}},94638:(e,t,s)=>{"use strict";s.d(t,{G9:()=>h,JZ:()=>f,M_:()=>x,N:()=>c,Nq:()=>u,TQ:()=>m,Ur:()=>l,aE:()=>d,aK:()=>v,dA:()=>i,gt:()=>b,mb:()=>g,qB:()=>N,yu:()=>p,zX:()=>y});var r=s(53148),a=s(39352),n=s(25748),o=s(32049);function l(e){switch(e){case"Our Culture & Workplace":return"Work Policies";case"Protecting Your Income":return"Income Security";default:return e}}function i(e){switch(e){case"On-site Amenities":return"Company Handbook";case"Protecting Your Income":return"Income Security";case"Got married/divorced":return"Marriage or Divorce";case"Had a baby or adopted a baby":return"New Baby or Adoption";case"Lost your insurance":return"Loss of Insurance";default:return e}}async function c(e,t){let s=await (0,r.A_)("/benefits/benefit-types",{companyId:t});return console.log("COMPANY BENEFIT TYPES RESPONSE: ",s.benefitTypes),e((0,a.x7)(s.benefitTypes)),s.benefitTypes}async function d(e,t){let s=await (0,r.A_)("/benefits/all-benefits",{companyId:t});console.log("COMPANY BENEFIT TYPES WITH BENEFITS RESPONSE: ",s),e((0,n.US)(s.benefitsPerType))}async function m(e){let t=await (0,r.A_)("/admin/all-employees");return console.log("COMPANY TEAM MEMBERS RESPONSE: ",t),e((0,a.Vv)(t.employees)),t.employees}async function p(e,t){return console.log("ADDING USERS: ",t),await (0,r.j0)("/admin/add/employees",{employeeList:t})}async function u(e,t,s){try{console.log("\uD83D\uDD0D Debug: User being updated:",t);let e={employeeId:t,updatedDetails:{name:s.name,email:s.email,details:{phoneNumber:s.phoneNumber||"",department:s.department||"",title:s.title||"",role:s.title||""}}};return s.dateOfBirth&&(e.updatedDetails.details.dateOfBirth=s.dateOfBirth),s.hireDate&&(e.updatedDetails.details.hireDate=s.hireDate),s.annualSalary&&(e.updatedDetails.details.annualSalary=s.annualSalary),s.employeeClassType&&(e.updatedDetails.details.employeeClassType=s.employeeClassType),s.workSchedule&&(e.updatedDetails.details.workSchedule=s.workSchedule),s.ssn&&(e.updatedDetails.details.ssn=s.ssn),s.employeeId&&(e.updatedDetails.details.employeeId=s.employeeId),s.workLocation&&(e.updatedDetails.details.workLocation=s.workLocation),s.address&&(e.updatedDetails.details.address=s.address),s.mailingAddress&&(e.updatedDetails.details.mailingAddress=s.mailingAddress),s.emergencyContact&&(e.updatedDetails.details.emergencyContact=s.emergencyContact),e.updatedDetails.details.dependents=s.dependents||[],console.log("Middleware - dependents being sent:",e.updatedDetails.details.dependents),console.log("Sending PUT request with cleaned data:",e),await (0,r.GH)("/admin/update/employee",e)}catch(e){throw console.error("Error in updateUser middleware:",e),e}}async function x(e,t){let s=await (0,r.A_)("/employee",{"user-id":t});return e((0,o.$l)({name:s.currentUser.name,email:s.currentUser.email,companyId:s.currentUser.companyId,role:s.currentUser.role,isAdmin:s.currentUser.isAdmin,isBroker:s.currentUser.isBroker,details:s.currentUser.details})),s}async function y(e,t,s){let a=await (0,r.j0)("/admin/onboard",{company:{name:t.name,adminEmail:t.adminEmail,adminRole:t.adminRole,companySize:t.companySize,industry:t.industry,location:t.location,website:t.website,howHeard:t.howHeard,brokerId:t.brokerId,brokerageId:t.brokerageId,isBrokerage:t.isBrokerage,isActivated:t.isActivated,referralSource:t.referralSource,details:{logo:""}},user:{email:s.email,name:s.name,role:s.role,isAdmin:s.isAdmin,isBroker:s.isBroker,isActivated:s.isActivated}}),n=a.data.userId,o=a.data.companyId;return localStorage.setItem("userid1",n),localStorage.setItem("companyId1",o),a}async function g(e,t){return console.log("SENDING LOGIN LINK TO EMPLOYEE: ",e,t),await (0,r.j0)("/admin/send-user-login-link",{userId:e,companyId:t})}async function h(e,t,s,a){let n=await (0,r.j0)("/admin/add/employer",{brokerId:e,companyName:t,companyAdminEmail:s,companyAdminName:a});return console.log("BROKER ADDS COMPANY RESPONSE: ",n),n}async function f(e,t){return 200===(await (0,r.j0)("/employee/offboard/",{userId:e,companyId:t})).status}async function b(e,t){return await (0,r.j0)("/employee/enable/",{userId:e,companyId:t})}async function N(e,t){try{let t=await (0,r.A_)("/admin/all-companies");console.log("CLIENT COMPANIES UNDER BROKER: ",t);let s=t.companies||[];try{let e=await (0,r.A_)("/employee/company-details");console.log("BROKER'S OWN COMPANY: ",e),e.company&&e.company.isBrokerage&&!s.some(t=>t._id===e.company._id)&&(s.unshift(e.company),console.log("Added broker's own company to the list"))}catch(e){console.log("Could not fetch broker's own company (this is normal if user is not a broker):",e)}return console.log("ALL COMPANIES (CLIENT + OWN): ",s),e((0,o.Ym)(s)),{...t,companies:s}}catch(t){return console.error("Error fetching companies:",t),e((0,o.Ym)([])),{companies:[]}}}async function v(e){let t=await (0,r.A_)("/employee/company-details");return e((0,a.sy)(t.company)),t.status}},31870:(e,t,s)=>{"use strict";s.d(t,{C:()=>n,T:()=>a});var r=s(25842);let a=()=>(0,r.I0)(),n=r.v9},65453:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(68570).createProxy)(String.raw`C:\beno\project_dev\qharmony-frontend-v1\src\app\ai-enroller\manage-groups\company\[companyId]\review\page.tsx#default`)},73881:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});var r=s(66621);let a=e=>[{type:"image/x-icon",sizes:"16x16",url:(0,r.fillMetadataSegment)(".",e.params,"favicon.ico")+""}]}};var t=require("../../../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[8948,1183,6621,8492,576,9902],()=>s(73605));module.exports=r})();