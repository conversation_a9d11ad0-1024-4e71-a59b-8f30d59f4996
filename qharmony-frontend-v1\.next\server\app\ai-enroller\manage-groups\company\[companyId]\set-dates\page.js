(()=>{var e={};e.id=5405,e.ids=[5405],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},27790:e=>{"use strict";e.exports=require("assert")},84770:e=>{"use strict";e.exports=require("crypto")},17702:e=>{"use strict";e.exports=require("events")},92048:e=>{"use strict";e.exports=require("fs")},32615:e=>{"use strict";e.exports=require("http")},35240:e=>{"use strict";e.exports=require("https")},19801:e=>{"use strict";e.exports=require("os")},55315:e=>{"use strict";e.exports=require("path")},76162:e=>{"use strict";e.exports=require("stream")},74026:e=>{"use strict";e.exports=require("string_decoder")},74175:e=>{"use strict";e.exports=require("tty")},17360:e=>{"use strict";e.exports=require("url")},21764:e=>{"use strict";e.exports=require("util")},71568:e=>{"use strict";e.exports=require("zlib")},98061:e=>{"use strict";e.exports=require("node:assert")},92761:e=>{"use strict";e.exports=require("node:async_hooks")},72254:e=>{"use strict";e.exports=require("node:buffer")},40027:e=>{"use strict";e.exports=require("node:console")},6005:e=>{"use strict";e.exports=require("node:crypto")},65714:e=>{"use strict";e.exports=require("node:diagnostics_channel")},15673:e=>{"use strict";e.exports=require("node:events")},88849:e=>{"use strict";e.exports=require("node:http")},42725:e=>{"use strict";e.exports=require("node:http2")},87503:e=>{"use strict";e.exports=require("node:net")},38846:e=>{"use strict";e.exports=require("node:perf_hooks")},39630:e=>{"use strict";e.exports=require("node:querystring")},84492:e=>{"use strict";e.exports=require("node:stream")},31764:e=>{"use strict";e.exports=require("node:tls")},41041:e=>{"use strict";e.exports=require("node:url")},47261:e=>{"use strict";e.exports=require("node:util")},93746:e=>{"use strict";e.exports=require("node:util/types")},24086:e=>{"use strict";e.exports=require("node:worker_threads")},65628:e=>{"use strict";e.exports=require("node:zlib")},65176:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>l.a,__next_app__:()=>m,originalPathname:()=>p,pages:()=>d,routeModule:()=>u,tree:()=>c}),r(37235),r(6079),r(33709),r(35866);var s=r(23191),a=r(88716),n=r(37922),l=r.n(n),o=r(95231),i={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>o[e]);r.d(t,i);let c=["",{children:["ai-enroller",{children:["manage-groups",{children:["company",{children:["[companyId]",{children:["set-dates",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,37235)),"C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\manage-groups\\company\\[companyId]\\set-dates\\page.tsx"]}]},{}]},{}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,6079)),"C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,33709)),"C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,35866,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],d=["C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\manage-groups\\company\\[companyId]\\set-dates\\page.tsx"],p="/ai-enroller/manage-groups/company/[companyId]/set-dates/page",m={require:r,loadChunk:()=>Promise.resolve()},u=new s.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/ai-enroller/manage-groups/company/[companyId]/set-dates/page",pathname:"/ai-enroller/manage-groups/company/[companyId]/set-dates",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},52638:(e,t,r)=>{Promise.resolve().then(r.bind(r,29586))},82400:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n});var s=r(51426),a=r(10326);let n=(0,s.Z)((0,a.jsx)("path",{d:"m17 7-1.41 1.41L18.17 11H8v2h10.17l-2.58 2.58L17 17l5-5zM4 5h8V3H4c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h8v-2H4z"}),"Logout")},33198:(e,t,r)=>{"use strict";r.d(t,{Z:()=>j});var s=r(17577),a=r(41135),n=r(88634),l=r(91703),o=r(13643),i=r(2791),c=r(51426),d=r(10326);let p=(0,c.Z)((0,d.jsx)("path",{d:"M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"}),"Person");var m=r(71685),u=r(97898);function x(e){return(0,u.ZP)("MuiAvatar",e)}(0,m.Z)("MuiAvatar",["root","colorDefault","circular","rounded","square","img","fallback"]);var g=r(31121);let h=e=>{let{classes:t,variant:r,colorDefault:s}=e;return(0,n.Z)({root:["root",r,s&&"colorDefault"],img:["img"],fallback:["fallback"]},x,t)},f=(0,l.default)("div",{name:"MuiAvatar",slot:"Root",overridesResolver:(e,t)=>{let{ownerState:r}=e;return[t.root,t[r.variant],r.colorDefault&&t.colorDefault]}})((0,o.Z)(({theme:e})=>({position:"relative",display:"flex",alignItems:"center",justifyContent:"center",flexShrink:0,width:40,height:40,fontFamily:e.typography.fontFamily,fontSize:e.typography.pxToRem(20),lineHeight:1,borderRadius:"50%",overflow:"hidden",userSelect:"none",variants:[{props:{variant:"rounded"},style:{borderRadius:(e.vars||e).shape.borderRadius}},{props:{variant:"square"},style:{borderRadius:0}},{props:{colorDefault:!0},style:{color:(e.vars||e).palette.background.default,...e.vars?{backgroundColor:e.vars.palette.Avatar.defaultBg}:{backgroundColor:e.palette.grey[400],...e.applyStyles("dark",{backgroundColor:e.palette.grey[600]})}}}]}))),y=(0,l.default)("img",{name:"MuiAvatar",slot:"Img",overridesResolver:(e,t)=>t.img})({width:"100%",height:"100%",textAlign:"center",objectFit:"cover",color:"transparent",textIndent:1e4}),b=(0,l.default)(p,{name:"MuiAvatar",slot:"Fallback",overridesResolver:(e,t)=>t.fallback})({width:"75%",height:"75%"}),j=s.forwardRef(function(e,t){let r=(0,i.i)({props:e,name:"MuiAvatar"}),{alt:n,children:l,className:o,component:c="div",slots:p={},slotProps:m={},imgProps:u,sizes:x,src:j,srcSet:v,variant:w="circular",...N}=r,S=null,k={...r,component:c,variant:w},C=function({crossOrigin:e,referrerPolicy:t,src:r,srcSet:a}){let[n,l]=s.useState(!1);return s.useEffect(()=>{if(!r&&!a)return;l(!1);let s=!0,n=new Image;return n.onload=()=>{s&&l("loaded")},n.onerror=()=>{s&&l("error")},n.crossOrigin=e,n.referrerPolicy=t,n.src=r,a&&(n.srcset=a),()=>{s=!1}},[e,t,r,a]),n}({...u,..."function"==typeof m.img?m.img(k):m.img,src:j,srcSet:v}),P=j||v,q=P&&"error"!==C;k.colorDefault=!q,delete k.ownerState;let D=h(k),[I,F]=(0,g.Z)("img",{className:D.img,elementType:y,externalForwardedProps:{slots:p,slotProps:{img:{...u,...m.img}}},additionalProps:{alt:n,src:j,srcSet:v,sizes:x},ownerState:k});return S=q?(0,d.jsx)(I,{...F}):l||0===l?l:P&&n?n[0]:(0,d.jsx)(b,{ownerState:k,className:D.fallback}),(0,d.jsx)(f,{as:c,className:(0,a.Z)(D.root,o),ref:t,...N,ownerState:k,children:S})})},63948:(e,t)=>{"use strict";var r=Symbol.for("react.element"),s=(Symbol.for("react.portal"),Symbol.for("react.fragment"),Symbol.for("react.strict_mode"),Symbol.for("react.profiler"),Symbol.for("react.provider"),Symbol.for("react.context"),Symbol.for("react.forward_ref"),Symbol.for("react.suspense"),Symbol.for("react.memo"),Symbol.for("react.lazy"),Symbol.iterator,{isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}}),a=Object.assign,n={};function l(e,t,r){this.props=e,this.context=t,this.refs=n,this.updater=r||s}function o(){}function i(e,t,r){this.props=e,this.context=t,this.refs=n,this.updater=r||s}l.prototype.isReactComponent={},l.prototype.setState=function(e,t){if("object"!=typeof e&&"function"!=typeof e&&null!=e)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")},l.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")},o.prototype=l.prototype;var c=i.prototype=new o;c.constructor=i,a(c,l.prototype),c.isPureReactComponent=!0;var d=Object.prototype.hasOwnProperty,p={current:null},m={key:!0,ref:!0,__self:!0,__source:!0};t.createElement=function(e,t,s){var a,n={},l=null,o=null;if(null!=t)for(a in void 0!==t.ref&&(o=t.ref),void 0!==t.key&&(l=""+t.key),t)d.call(t,a)&&!m.hasOwnProperty(a)&&(n[a]=t[a]);var i=arguments.length-2;if(1===i)n.children=s;else if(1<i){for(var c=Array(i),u=0;u<i;u++)c[u]=arguments[u+2];n.children=c}if(e&&e.defaultProps)for(a in i=e.defaultProps)void 0===n[a]&&(n[a]=i[a]);return{$$typeof:r,type:e,key:l,ref:o,props:n,_owner:p.current}}},95746:(e,t,r)=>{"use strict";e.exports=r(63948)},67925:(e,t,r)=>{"use strict";r.d(t,{Z:()=>f});var s=r(10326),a=r(17577),n=r(6283),l=r(25609),o=r(33198),i=r(42265),c=r(82400),d=r(46226),p=r(22758),m=r(31870),u=r(25842),x=r(30656),g=r(88563),h=r(35047);let f=()=>{let e=(0,h.useRouter)(),{logout:t}=(0,p.a)(),r=(0,m.C)(e=>e.user.userProfile),[f,y]=(0,a.useState)(!1);(0,a.useEffect)(()=>{y("true"===localStorage.getItem("isTeamsApp1"))},[]);let b=(0,u.v9)(e=>e.user.userProfile.isAdmin),j=(0,u.v9)(e=>e.user.userProfile.isBroker);return s.jsx(n.Z,{sx:{bgcolor:"white",padding:2,textAlign:"center",height:"80px",borderBottom:"1px solid #D2D2D2",position:"sticky",top:0,zIndex:50,boxShadow:"0 1px 3px 0 rgba(0, 0, 0, 0.1)"},children:(0,s.jsxs)(n.Z,{sx:{display:"flex",alignItems:"center",justifyContent:"space-between"},children:[(0,s.jsxs)(n.Z,{sx:{display:"flex",alignItems:"center"},children:[(0,s.jsxs)(n.Z,{sx:{display:"flex",alignItems:"center",cursor:"pointer",mr:3},onClick:()=>{e.push("/dashboard")},children:[s.jsx(d.default,{src:x.Z,alt:"BenOsphere Logo",width:40,height:40}),s.jsx(l.Z,{sx:{fontWeight:800,fontSize:"1.5rem",ml:1,color:"#111827"},children:"BenOsphere"})]}),s.jsx(o.Z,{sx:{bgcolor:"black",color:"#ffffff",width:35,height:35,fontSize:"1.2rem",mr:1.5,ml:2,display:"flex",alignItems:"center",justifyContent:"center",paddingBottom:"1px",fontWeight:800},children:(e=>{if(!e)return"";let[t,r]=e.split(" ");return`${t[0].toUpperCase()}${r?r[0].toUpperCase():""}`})(r.name)}),s.jsx(l.Z,{variant:"h4",sx:{mb:0,fontWeight:"bold",display:"flex",alignItems:"center",justifyContent:"flex-start",textAlign:"flex-start",fontSize:"16px",mr:1.5,color:"#111827"},children:r.name.replace(/\b\w/g,e=>e.toUpperCase())}),b&&s.jsx(n.Z,{sx:{bgcolor:"rgba(0, 0, 0, 0.06)",borderRadius:"8px",padding:"4px 8px",display:"flex",alignItems:"center",justifyContent:"center",fontWeight:"bold",fontSize:"12px",color:"#333",mr:1.5},children:"ADMIN"}),j&&s.jsx(n.Z,{sx:{bgcolor:"#f5f5f5",borderRadius:"8px",padding:"2px 8px",display:"flex",alignItems:"center",justifyContent:"center",fontWeight:"bold",fontSize:"0.9rem",color:"#333",mr:1.5},children:"BROKER"})]}),(0,s.jsxs)(n.Z,{sx:{display:"flex",alignItems:"center"},children:[!f&&(0,s.jsxs)(i.Z,{sx:{backgroundColor:"transparent",color:"#333",textTransform:"none",padding:"8px 8px",display:"flex",alignItems:"center",justifyContent:"flex-start",gap:"2px","&:hover":{backgroundColor:"transparent",color:"#555"},boxShadow:"none"},children:[s.jsx(n.Z,{sx:{mt:.5,mr:.5},children:s.jsx(g.Z,{})}),s.jsx(l.Z,{sx:{fontWeight:500,fontSize:"14px",color:"#333"},children:"Guide"})]}),!f&&(0,s.jsxs)(i.Z,{onClick:t,sx:{backgroundColor:"transparent",color:"#333",marginLeft:1,textTransform:"none",padding:"8px 16px",display:"flex",alignItems:"center",justifyContent:"flex-start",gap:"8px","&:hover":{backgroundColor:"transparent",color:"#555"},boxShadow:"none"},children:[s.jsx(c.Z,{sx:{fontSize:"18px"}}),s.jsx(l.Z,{sx:{fontWeight:500,fontSize:"14px",color:"#333"},children:"Logout"})]})]})]})})}},29586:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>c});var s=r(10326),a=r(17577),n=r(35047),l=r(38492),o=r(43933),i=r(67925);function c(){let e=(0,n.useRouter)(),t=(0,n.useParams)(),r=(0,n.useSearchParams)(),c=t.companyId,d=(0,a.useMemo)(()=>r.get("plans")?.split(",")||[],[r]),[p,m]=(0,a.useState)(null),[u,x]=(0,a.useState)([]),[g,h]=(0,a.useState)(!0),[f,y]=(0,a.useState)(!1),[b,j]=(0,a.useState)(!1),[v,w]=(0,a.useState)("2024-11-01"),[N,S]=(0,a.useState)("2024-11-30"),[k,C]=(0,a.useState)("2025-01-01"),[P,q]=(0,a.useState)("2025-12-31"),[D,I]=(0,a.useState)("0"),[F,_]=(0,a.useState)("30"),A=async e=>{try{let t=await fetch(`http://localhost:8080/api/pre-enrollment/plans/${e}`,{headers:{"Content-Type":"application/json","user-id":(()=>{let e=localStorage.getItem("userid1")||localStorage.getItem("userId");if(!e)throw Error("User ID not found. Please authenticate first.");return e})()}});if(t.ok){let e=await t.json();return{planName:e.plan?.planName||"Unknown Plan",planCode:e.plan?.planCode||"N/A",planType:e.plan?.planType||"N/A",coverageType:e.plan?.coverageType||"Unknown",coverageSubTypes:e.plan?.coverageSubTypes||[],metalTier:e.plan?.metalTier||"",carrierName:e.carrier?.carrierName||"Unknown Carrier"}}}catch(t){console.error("Error fetching plan details for planId:",e,t)}return{planName:"Unknown Plan",planCode:"N/A",planType:"N/A",coverageType:"Unknown",coverageSubTypes:[],metalTier:"",carrierName:"Unknown Carrier"}},E=async e=>{try{console.log("Fetching plan assignment details for ID:",e);let t=await fetch(`http://localhost:8080/api/pre-enrollment/plan-assignments/${e}`,{headers:{"Content-Type":"application/json","user-id":localStorage.getItem("userid1")||localStorage.getItem("userId")||"6838677aef6db0212bcfdacd"}});if(console.log("Plan assignment fetch response status:",t.status),t.ok){let e=await t.json();console.log("Plan assignment fetch response data:",e),console.log("Assignment object:",e.assignment);let r=e.assignment._doc||e.assignment;return console.log("Processed assignment:",r),console.log("Coverage tiers in assignment:",r?.coverageTiers),r}{console.error("Failed to fetch plan assignment details. Status:",t.status);let e=await t.text();console.error("Error response:",e)}}catch(e){console.error("Error fetching plan assignment details:",e)}return null};(0,a.useCallback)(async()=>{try{h(!0);let e=await fetch(`http://localhost:8080/api/pre-enrollment/company-benefits-settings/company/${c}`,{headers:{"Content-Type":"application/json","user-id":(()=>{let e=localStorage.getItem("userid1")||localStorage.getItem("userId");if(!e)throw Error("User ID not found. Please authenticate first.");return e})()}});if(e.ok){let t=await e.json();m({_id:c,companyName:t.companyName||"Company Name",employeeCount:t.employeeCount||250})}else console.error("Failed to fetch company details:",e.status),m({_id:c,companyName:"Company Name",employeeCount:250});let t=await (0,o.fH)(c);if(t.success&&t.data){let e=t.data.assignments;console.log("All assignments:",e.length),console.log("Selected assignment IDs from URL:",d),console.log("Assignment IDs in data:",e.map(e=>e._id)),console.log("Sample assignment structure:",e[0]);let r=e;d.length>0&&(r=e.filter(e=>d.includes(e._id))),console.log("Filtered assignments:",r.length);let s=await Promise.all(r.map(async e=>{let t="string"==typeof e.planId?e.planId:e.planId?._id||"";console.log("Processing assignment:",e._id,"with planId:",t);let r=null;t&&(r=await A(t),console.log("Fetched plan details:",r));let s=await E(e._id);return console.log("Fetched assignment details:",s),{...e,planDetails:r,assignmentDetails:s}}));if(x(s),s.length>0){let e=s[0];w(e.enrollmentStartDate?.split("T")[0]||"2024-11-01"),S(e.enrollmentEndDate?.split("T")[0]||"2024-11-30"),C(e.planEffectiveDate?.split("T")[0]||"2025-01-01"),q(e.planEndDate?.split("T")[0]||"2025-12-31")}}else console.error("Failed to fetch plan assignments:",t.error),x([])}catch(e){console.error("Error fetching data:",e),m({_id:c,companyName:"Company Name",employeeCount:250}),x([])}finally{h(!1)}},[c,d]);let R=async()=>{if(0===u.length)return!0;y(!0);try{let e=u.map(async e=>{let t=await (0,o.updatePlanAssignment)(e._id,{enrollmentStartDate:v,enrollmentEndDate:N,planEffectiveDate:k,planEndDate:P});if(!t.success)throw console.error(`Failed to update assignment ${e._id}:`,t.error),Error(`Failed to update ${e.plan?.planName||"plan"}: ${t.error}`);return t.data});return await Promise.all(e),console.log("All plan assignments updated successfully"),!0}catch(e){return console.error("Error saving date changes:",e),alert(`Error saving changes: ${e instanceof Error?e.message:"Unknown error"}`),!1}finally{y(!1)}},T=async()=>{if(await R()){let t=u.map(e=>e._id);e.push(`/ai-enroller/manage-groups/company/${c}/review?assignments=${t.join(",")}`)}},Z=async()=>{await R()&&e.push(`/ai-enroller/manage-groups/company/${c}/plans`)};return g?s.jsx("div",{className:"min-h-screen bg-white flex items-center justify-center",children:(0,s.jsxs)("div",{className:"text-center",children:[s.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-black mx-auto"}),s.jsx("p",{className:"mt-4 text-gray-600",children:"Loading..."})]})}):(0,s.jsxs)(s.Fragment,{children:[s.jsx(i.Z,{}),(0,s.jsxs)("div",{className:"min-h-screen bg-white",children:[s.jsx("div",{className:"bg-white border-b border-gray-200 px-6 py-4",children:(0,s.jsxs)("div",{className:"max-w-7xl mx-auto",children:[(0,s.jsxs)("div",{className:"flex gap-3 overflow-x-auto",children:[(0,s.jsxs)("button",{onClick:()=>e.push("/ai-enroller"),className:"flex items-center gap-2 px-4 py-2 rounded-full text-sm font-medium transition-all bg-purple-100 text-purple-700 hover:bg-purple-200 whitespace-nowrap",style:{fontFamily:"'SF Pro', 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif",fontSize:"13px",fontWeight:"450",lineHeight:"1.2"},children:[s.jsx(l.VRM,{className:"w-4 h-4"}),"Home",s.jsx("span",{className:"flex items-center justify-center w-4 h-4 bg-purple-600 rounded-full ml-2",children:s.jsx(l.PjL,{className:"w-5 h-5 text-white"})})]}),(0,s.jsxs)("button",{onClick:()=>e.push("/ai-enroller/manage-groups/select-company"),className:"flex items-center gap-2 px-4 py-2 rounded-full text-sm font-medium transition-all bg-purple-100 text-purple-700 hover:bg-purple-200 whitespace-nowrap",style:{fontFamily:"'SF Pro', 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif",fontSize:"13px",fontWeight:"450",lineHeight:"1.2"},children:[s.jsx(l.$xp,{className:"w-4 h-4"}),"Select Company",s.jsx("span",{className:"flex items-center justify-center w-4 h-4 bg-purple-600 text-white rounded-full ml-2",children:s.jsx(l.PjL,{className:"w-5 h-5"})})]}),(0,s.jsxs)("button",{onClick:()=>e.push(`/ai-enroller/manage-groups/company/${c}/plans`),className:"flex items-center gap-2 px-4 py-2 rounded-full text-sm font-medium transition-all bg-purple-100 text-purple-700 hover:bg-purple-200 whitespace-nowrap",style:{fontFamily:"'SF Pro', 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif",fontSize:"13px",fontWeight:"450",lineHeight:"1.2"},children:[s.jsx(l.GwR,{className:"w-4 h-4"}),"View Plans",s.jsx("span",{className:"flex items-center justify-center w-4 h-4 bg-purple-600 text-white rounded-full ml-2",children:s.jsx(l.PjL,{className:"w-5 h-5"})})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2 px-4 py-2 rounded-full text-sm font-medium bg-purple-50 text-purple-600 whitespace-nowrap",style:{fontFamily:"'SF Pro', 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif",fontSize:"13px",fontWeight:"450",lineHeight:"1.2"},children:[s.jsx(l.Bge,{className:"w-4 h-4"}),"Set Dates"]}),(0,s.jsxs)("div",{className:"flex items-center gap-2 px-4 py-2 rounded-full text-sm font-medium bg-gray-100 text-gray-600 whitespace-nowrap",style:{fontFamily:"'SF Pro', 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif",fontSize:"13px",fontWeight:"450",lineHeight:"1.2"},children:[s.jsx(l.PjL,{className:"w-4 h-4"}),"Review"]}),(0,s.jsxs)("div",{className:"flex items-center gap-2 px-4 py-2 rounded-full text-sm font-medium bg-gray-100 text-gray-600 whitespace-nowrap",style:{fontFamily:"'SF Pro', 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif",fontSize:"13px",fontWeight:"450",lineHeight:"1.2"},children:[s.jsx(l.PjL,{className:"w-4 h-4"}),"Confirmation"]})]}),s.jsx("div",{className:"mt-4",children:(0,s.jsxs)("div",{className:"text-right text-sm text-gray-500",children:["Step 4 of 6",s.jsx("br",{}),s.jsx("span",{className:"text-xs text-purple-600 font-medium",children:"Set plan dates"})]})})]})}),(0,s.jsxs)("div",{className:"max-w-4xl mx-auto px-6 py-8 bg-white",children:[(0,s.jsxs)("div",{className:"mb-8",children:[s.jsx("h1",{className:"text-2xl font-bold text-gray-900 mb-2",children:"Set Enrollment & Active Dates"}),(0,s.jsxs)("p",{className:"text-gray-600",children:["Configure dates for ",u.length," plan",1!==u.length?"s":"",u.length>0&&s.jsx("span",{className:"block text-sm text-gray-500 mt-1",children:u.map(e=>e.planDetails?.planName||"Unknown Plan").join(", ")})]})]}),0===u.length&&!g&&s.jsx("div",{className:"bg-yellow-50 border border-yellow-200 rounded-xl p-6 mb-6",children:(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[s.jsx("div",{className:"w-8 h-8 bg-yellow-100 rounded-xl flex items-center justify-center",children:s.jsx("span",{className:"text-yellow-600 text-lg",children:"⚠️"})}),(0,s.jsxs)("div",{children:[s.jsx("h3",{className:"font-semibold text-yellow-800",children:"No Plan Assignments Found"}),s.jsx("p",{className:"text-sm text-yellow-700",children:"No plan assignments were found for this company. Please create plan assignments first."})]})]})}),u.length>0&&(0,s.jsxs)("div",{className:"bg-white border border-gray-200 rounded-xl p-6 mb-6",children:[(0,s.jsxs)("div",{className:"flex items-center gap-3 mb-6",children:[s.jsx("div",{className:"w-8 h-8 bg-blue-100 rounded-xl flex items-center justify-center",children:s.jsx("span",{className:"text-blue-600 text-lg",children:"\uD83D\uDCCB"})}),(0,s.jsxs)("div",{children:[s.jsx("h2",{className:"text-xl font-semibold text-gray-900",children:"Selected Plans"}),s.jsx("p",{className:"text-sm text-gray-600",children:"Plans that will be updated with new dates"})]})]}),s.jsx("div",{className:"space-y-4",children:u.map(e=>{let t=e.planDetails?.planName||"Unknown Plan",r=e.planDetails?.planCode||"N/A",a=e.planDetails?.coverageType||"Unknown Coverage",n=e.planDetails?.carrierName||"Unknown Carrier",l=e.planDetails?.metalTier||"",o=e.assignmentDetails?.coverageTiers||e.coverageTiers||[],i=o[0]||{},c=i.totalCost||0,d=i.employerCost||0,p=i.employeeCost||c-d;return s.jsx("div",{className:"border-l-4 border-blue-500 pl-4 py-3 bg-gray-50 rounded-r-lg",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex-1",children:[s.jsx("h3",{className:"font-semibold text-gray-900",children:t}),(0,s.jsxs)("p",{className:"text-sm text-gray-600",children:[n," • ",a," ",l&&`• ${l}`," • ",r]}),(0,s.jsxs)("p",{className:"text-xs text-gray-500",children:["Current: ",e.enrollmentStartDate?.split("T")[0]||"N/A"," to ",e.enrollmentEndDate?.split("T")[0]||"N/A"]}),o.length>1&&(0,s.jsxs)("p",{className:"text-xs text-blue-600 mt-1",children:[o.length," coverage tiers available"]})]}),(0,s.jsxs)("div",{className:"text-right ml-4",children:[(0,s.jsxs)("p",{className:"text-sm font-medium text-gray-900",children:["$",c.toFixed(2),"/month"]}),(0,s.jsxs)("p",{className:"text-xs text-gray-500",children:["Employer: $",d.toFixed(2)]}),p>0&&(0,s.jsxs)("p",{className:"text-xs text-gray-500",children:["Employee: $",p.toFixed(2)]}),o.length>0&&s.jsx("p",{className:"text-xs text-gray-400 mt-1",children:i.tierName||"Employee Only"})]})]})},e._id)})})]}),(0,s.jsxs)("div",{className:"bg-white border border-gray-200 rounded-xl p-6 mb-6",children:[(0,s.jsxs)("div",{className:"flex items-center gap-3 mb-6",children:[s.jsx(l.Bge,{className:"text-gray-600 w-6 h-6"}),(0,s.jsxs)("div",{children:[s.jsx("h2",{className:"text-xl font-semibold text-gray-900",children:"Date Configuration"}),s.jsx("p",{className:"text-sm text-gray-600",children:"Set enrollment and plan active dates for these plans"})]})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8",children:[(0,s.jsxs)("div",{className:"border-l-4 border-blue-500 pl-6",children:[s.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"Open Enrollment Dates"}),s.jsx("p",{className:"text-sm text-gray-600 mb-4",children:"When employees can enroll in this plan"}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Enrollment Start Date"}),s.jsx("input",{type:"date",value:v,onChange:e=>w(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-black focus:border-black bg-white",style:{color:"#111827",backgroundColor:"white"}})]}),(0,s.jsxs)("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Enrollment End Date"}),s.jsx("input",{type:"date",value:N,onChange:e=>S(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-black focus:border-black bg-white",style:{color:"#111827",backgroundColor:"white"}})]})]})]}),(0,s.jsxs)("div",{className:"border-l-4 border-green-500 pl-6",children:[s.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"Plan Active Dates"}),s.jsx("p",{className:"text-sm text-gray-600 mb-4",children:"When this plan coverage is active"}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Plan Start Date"}),s.jsx("input",{type:"date",value:k,onChange:e=>C(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-black focus:border-black bg-white",style:{color:"#111827",backgroundColor:"white"}})]}),(0,s.jsxs)("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Plan End Date"}),s.jsx("input",{type:"date",value:P,onChange:e=>q(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-black focus:border-black bg-white",style:{color:"#111827",backgroundColor:"white"}})]})]})]})]})]}),(0,s.jsxs)("div",{className:"bg-white border border-gray-200 rounded-xl p-6 mb-6",children:[s.jsx("h2",{className:"text-lg font-semibold text-gray-900 mb-2",children:"Additional Settings"}),s.jsx("p",{className:"text-sm text-blue-600 mb-6",children:"Optional configuration for this plan"}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,s.jsxs)("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Waiting Period (days)"}),s.jsx("input",{type:"number",value:D,onChange:e=>I(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-black focus:border-black bg-white",style:{color:"#111827",backgroundColor:"white"},min:"0",max:"365"})]}),(0,s.jsxs)("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Grace Period (days)"}),s.jsx("input",{type:"number",value:F,onChange:e=>_(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-black focus:border-black bg-white",style:{color:"#111827",backgroundColor:"white"},min:"0",max:"90"})]})]})]}),s.jsx("div",{className:"bg-green-50 border border-green-200 rounded-xl p-4 mb-8",children:(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[s.jsx("div",{className:"w-5 h-5 bg-green-500 rounded-full flex items-center justify-center",children:s.jsx("span",{className:"text-white text-xs",children:"✓"})}),(0,s.jsxs)("div",{children:[s.jsx("span",{className:"font-medium text-green-800",children:"Plan Configuration Complete"}),s.jsx("p",{className:"text-green-700 text-sm",children:"You can now return to configure other plans or continue the workflow"})]})]})}),(0,s.jsxs)("div",{className:"flex gap-4",children:[s.jsx("button",{onClick:Z,disabled:f,className:"px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors disabled:opacity-50 disabled:cursor-not-allowed",children:f?"Saving...":"Save & Return to Plans"}),(0,s.jsxs)("button",{onClick:T,disabled:f,className:"flex-1 px-6 py-2 bg-black text-white rounded-lg hover:bg-gray-800 transition-colors flex items-center justify-center gap-2 cursor-pointer disabled:opacity-50 disabled:cursor-not-allowed",type:"button",children:[f?"Saving...":"Continue to Review",!f&&s.jsx("span",{children:"→"})]})]})]})]})]})}},37235:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(68570).createProxy)(String.raw`C:\beno\project_dev\qharmony-frontend-v1\src\app\ai-enroller\manage-groups\company\[companyId]\set-dates\page.tsx#default`)}};var t=require("../../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[8948,1183,6621,9066,1999,8492,3253,576,6305,9902],()=>r(65176));module.exports=s})();