(()=>{var e={};e.id=2250,e.ids=[2250],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},27790:e=>{"use strict";e.exports=require("assert")},84770:e=>{"use strict";e.exports=require("crypto")},17702:e=>{"use strict";e.exports=require("events")},92048:e=>{"use strict";e.exports=require("fs")},32615:e=>{"use strict";e.exports=require("http")},35240:e=>{"use strict";e.exports=require("https")},19801:e=>{"use strict";e.exports=require("os")},55315:e=>{"use strict";e.exports=require("path")},76162:e=>{"use strict";e.exports=require("stream")},74026:e=>{"use strict";e.exports=require("string_decoder")},74175:e=>{"use strict";e.exports=require("tty")},17360:e=>{"use strict";e.exports=require("url")},21764:e=>{"use strict";e.exports=require("util")},71568:e=>{"use strict";e.exports=require("zlib")},98061:e=>{"use strict";e.exports=require("node:assert")},92761:e=>{"use strict";e.exports=require("node:async_hooks")},72254:e=>{"use strict";e.exports=require("node:buffer")},40027:e=>{"use strict";e.exports=require("node:console")},6005:e=>{"use strict";e.exports=require("node:crypto")},65714:e=>{"use strict";e.exports=require("node:diagnostics_channel")},15673:e=>{"use strict";e.exports=require("node:events")},88849:e=>{"use strict";e.exports=require("node:http")},42725:e=>{"use strict";e.exports=require("node:http2")},87503:e=>{"use strict";e.exports=require("node:net")},38846:e=>{"use strict";e.exports=require("node:perf_hooks")},39630:e=>{"use strict";e.exports=require("node:querystring")},84492:e=>{"use strict";e.exports=require("node:stream")},31764:e=>{"use strict";e.exports=require("node:tls")},41041:e=>{"use strict";e.exports=require("node:url")},47261:e=>{"use strict";e.exports=require("node:util")},93746:e=>{"use strict";e.exports=require("node:util/types")},24086:e=>{"use strict";e.exports=require("node:worker_threads")},65628:e=>{"use strict";e.exports=require("node:zlib")},44739:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>n.a,__next_app__:()=>p,originalPathname:()=>u,pages:()=>d,routeModule:()=>x,tree:()=>l}),r(5646),r(6079),r(33709),r(35866);var t=r(23191),a=r(88716),i=r(37922),n=r.n(i),o=r(95231),c={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>o[e]);r.d(s,c);let l=["",{children:["ai-enroller",{children:["manage-groups",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,5646)),"C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\manage-groups\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,6079)),"C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,33709)),"C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,35866,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],d=["C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\manage-groups\\page.tsx"],u="/ai-enroller/manage-groups/page",p={require:r,loadChunk:()=>Promise.resolve()},x=new t.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/ai-enroller/manage-groups/page",pathname:"/ai-enroller/manage-groups",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},72556:(e,s,r)=>{Promise.resolve().then(r.bind(r,18878))},18878:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>u});var t=r(10326),a=r(17577),i=r(35047),n=r(38492),o=r(43058);r(94638);var c=r(25842),l=r(42419);r(43933);var d=r(67925);r(79762);let u=()=>{let e=(0,i.useRouter)(),s=(0,c.I0)(),[r,u]=(0,a.useState)(!0),[p,x]=(0,a.useState)(!1),h=(0,c.v9)(e=>e.user.managedCompanies),m=(0,a.useCallback)(async()=>{try{u(!1)}catch(e){console.error("Error fetching dashboard data:",e),u(!1)}},[s]);(0,a.useEffect)(()=>{m()},[m]);let[v,g]=(0,a.useState)(0),j=h?.length||0,b=h?.reduce((e,s)=>e+s.companySize,0)||0;return r?t.jsx("div",{className:"broker-dashboard",children:(0,t.jsxs)("div",{className:"loading-container",children:[t.jsx("div",{className:"loading-spinner"}),t.jsx("p",{children:"Loading dashboard..."})]})}):(0,t.jsxs)(o.Z,{children:[t.jsx(d.Z,{}),(0,t.jsxs)("div",{className:"broker-dashboard",children:[t.jsx("div",{className:"dashboard-header",children:(0,t.jsxs)("div",{className:"header-content",children:[t.jsx("h1",{className:"page-title",children:"Broker Dashboard"}),t.jsx("p",{className:"subtitle-text",children:"Manage employer groups and benefit plans with ease. Your one-stop solution for comprehensive benefits administration."})]})}),(0,t.jsxs)("div",{className:"stats-grid",children:[(0,t.jsxs)("div",{className:"stat-card",children:[(0,t.jsxs)("div",{className:"stat-content",children:[t.jsx("div",{className:"stat-number",children:j}),t.jsx("div",{className:"stat-label",children:"Active Companies"})]}),t.jsx("div",{className:"stat-icon",children:t.jsx(n.$xp,{size:24})})]}),(0,t.jsxs)("div",{className:"stat-card",children:[(0,t.jsxs)("div",{className:"stat-content",children:[t.jsx("div",{className:"stat-number",children:b.toLocaleString()}),t.jsx("div",{className:"stat-label",children:"Total Employees"})]}),t.jsx("div",{className:"stat-icon",children:t.jsx(n.Otr,{size:24})})]}),(0,t.jsxs)("div",{className:"stat-card",children:[(0,t.jsxs)("div",{className:"stat-content",children:[t.jsx("div",{className:"stat-number",children:v}),t.jsx("div",{className:"stat-label",children:"Plan Assignments"})]}),t.jsx("div",{className:"stat-icon",children:t.jsx(n.GwR,{size:24})})]})]}),(0,t.jsxs)("div",{className:"quick-actions",children:[t.jsx("h2",{className:"section-header",children:"Quick Actions"}),t.jsx("p",{className:"body-text",children:"Choose how you'd like to get started"}),(0,t.jsxs)("div",{className:"action-cards",children:[(0,t.jsxs)("div",{className:"action-card",onClick:()=>{x(!0)},children:[t.jsx("div",{className:"action-icon",children:t.jsx(n.r7I,{size:32})}),(0,t.jsxs)("div",{className:"action-content",children:[t.jsx("h3",{className:"section-header",style:{fontSize:"18px"},children:"New Group"}),t.jsx("p",{className:"body-text",children:"Setting up benefits for a new organization or group that hasn't had coverage before"}),(0,t.jsxs)("div",{className:"action-tags",children:[t.jsx("span",{className:"tag",children:"Fresh start"}),t.jsx("span",{className:"tag",children:"New enrollment"})]})]})]}),(0,t.jsxs)("div",{className:"action-card",onClick:()=>{e.push("/ai-enroller/manage-groups/select-company")},children:[t.jsx("div",{className:"action-icon",children:t.jsx(n.$xp,{size:32})}),(0,t.jsxs)("div",{className:"action-content",children:[t.jsx("h3",{className:"section-header",style:{fontSize:"18px"},children:"Existing Group"}),t.jsx("p",{className:"body-text",children:"Adding or modifying benefits for an organization that already has some coverage in place"}),(0,t.jsxs)("div",{className:"action-tags",children:[t.jsx("span",{className:"tag",children:"Active enrollment"}),t.jsx("span",{className:"tag",children:"Plan changes"})]})]})]})]})]}),t.jsx("div",{className:"back-button-container",children:(0,t.jsxs)("button",{className:"back-button",onClick:()=>{e.push("/ai-enroller")},children:[t.jsx(n.Tsu,{size:20}),"Back to Main"]})}),p&&t.jsx(l.Z,{isOpen:p,onClose:()=>x(!1),onSuccess:()=>{x(!1),m()}})]})]})}},5646:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>t});let t=(0,r(68570).createProxy)(String.raw`C:\beno\project_dev\qharmony-frontend-v1\src\app\ai-enroller\manage-groups\page.tsx#default`)}};var s=require("../../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),t=s.X(0,[8948,1183,6621,9066,1999,8492,3253,576,6305,9902,5528],()=>r(44739));module.exports=t})();