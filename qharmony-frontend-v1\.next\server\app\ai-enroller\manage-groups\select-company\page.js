(()=>{var e={};e.id=7790,e.ids=[7790],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},27790:e=>{"use strict";e.exports=require("assert")},84770:e=>{"use strict";e.exports=require("crypto")},17702:e=>{"use strict";e.exports=require("events")},92048:e=>{"use strict";e.exports=require("fs")},32615:e=>{"use strict";e.exports=require("http")},35240:e=>{"use strict";e.exports=require("https")},19801:e=>{"use strict";e.exports=require("os")},55315:e=>{"use strict";e.exports=require("path")},76162:e=>{"use strict";e.exports=require("stream")},74026:e=>{"use strict";e.exports=require("string_decoder")},74175:e=>{"use strict";e.exports=require("tty")},17360:e=>{"use strict";e.exports=require("url")},21764:e=>{"use strict";e.exports=require("util")},71568:e=>{"use strict";e.exports=require("zlib")},98061:e=>{"use strict";e.exports=require("node:assert")},92761:e=>{"use strict";e.exports=require("node:async_hooks")},72254:e=>{"use strict";e.exports=require("node:buffer")},40027:e=>{"use strict";e.exports=require("node:console")},6005:e=>{"use strict";e.exports=require("node:crypto")},65714:e=>{"use strict";e.exports=require("node:diagnostics_channel")},15673:e=>{"use strict";e.exports=require("node:events")},88849:e=>{"use strict";e.exports=require("node:http")},42725:e=>{"use strict";e.exports=require("node:http2")},87503:e=>{"use strict";e.exports=require("node:net")},38846:e=>{"use strict";e.exports=require("node:perf_hooks")},39630:e=>{"use strict";e.exports=require("node:querystring")},84492:e=>{"use strict";e.exports=require("node:stream")},31764:e=>{"use strict";e.exports=require("node:tls")},41041:e=>{"use strict";e.exports=require("node:url")},47261:e=>{"use strict";e.exports=require("node:util")},93746:e=>{"use strict";e.exports=require("node:util/types")},24086:e=>{"use strict";e.exports=require("node:worker_threads")},65628:e=>{"use strict";e.exports=require("node:zlib")},48162:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>i.a,__next_app__:()=>x,originalPathname:()=>p,pages:()=>d,routeModule:()=>u,tree:()=>c}),t(20467),t(6079),t(33709),t(35866);var r=t(23191),a=t(88716),n=t(37922),i=t.n(n),o=t(95231),l={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);t.d(s,l);let c=["",{children:["ai-enroller",{children:["manage-groups",{children:["select-company",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,20467)),"C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\manage-groups\\select-company\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,6079)),"C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,33709)),"C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,35866,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],d=["C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\manage-groups\\select-company\\page.tsx"],p="/ai-enroller/manage-groups/select-company/page",x={require:t,loadChunk:()=>Promise.resolve()},u=new r.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/ai-enroller/manage-groups/select-company/page",pathname:"/ai-enroller/manage-groups/select-company",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},35758:(e,s,t)=>{Promise.resolve().then(t.bind(t,49935))},49935:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>x});var r=t(10326),a=t(17577),n=t(35047),i=t(38492),o=t(43058),l=t(25842);t(94638);var c=t(43933),d=t(42419),p=t(67925);function x(){let e=(0,n.useRouter)(),s=(0,l.I0)(),[t,x]=(0,a.useState)(!0),[u,m]=(0,a.useState)(""),[h,g]=(0,a.useState)(0),[y,b]=(0,a.useState)(!1),v=(0,l.v9)(e=>e.user.managedCompanies),j=(0,a.useCallback)(async()=>{try{console.log("\uD83D\uDD0D Fetching plan assignments count for broker...");let e=await (0,c.T1)();if(e.success&&e.data){let s=e.data.count;console.log("✅ Total plan assignments managed by broker:",s),g(s)}else console.warn("❌ Failed to fetch broker plan assignments count:",e.error),g(0)}catch(e){console.error("❌ Error fetching plan assignments count:",e),g(0)}},[]),f=(0,a.useCallback)(async()=>{x(!1)},[s]),N=(v?.map((e,s)=>({_id:e._id,companyName:e.name,ein:e.ein||`12-${String(s+1).padStart(7,"0")}`,location:e.location||"San Francisco, CA",companySize:e.companySize||Math.floor(500*Math.random())+50,status:e.isActivated?"active":"pending",industry:e.industry||"Technology",plansAssigned:Math.floor(10*Math.random())+2,joinDate:e.createdAt?new Date(e.createdAt).toLocaleDateString():new Date().toLocaleDateString()}))||[]).filter(e=>e.companyName.toLowerCase().includes(u.toLowerCase())||e.ein.includes(u)||e.location.toLowerCase().includes(u.toLowerCase())),w=s=>{e.push(`/ai-enroller/manage-groups/company/${s}/plans`)},q=async()=>{b(!1),await f(),await j()},S=v?.length||0,_=v?.reduce((e,s)=>e+s.companySize,0)||0;return t?r.jsx("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center",children:[r.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-black mx-auto"}),r.jsx("p",{className:"mt-4 text-gray-600",children:"Loading companies..."})]})}):(0,r.jsxs)(o.Z,{children:[r.jsx(p.Z,{}),(0,r.jsxs)("div",{className:"min-h-screen bg-white",children:[r.jsx("div",{className:"bg-white border-b border-gray-200 px-6 py-4",children:r.jsx("div",{className:"max-w-6xl mx-auto",children:(0,r.jsxs)("nav",{className:"flex items-center space-x-2 text-sm",children:[r.jsx("button",{onClick:()=>e.push("/ai-enroller"),className:"text-gray-600 hover:text-gray-900 font-medium transition-colors",children:"Home"}),r.jsx("span",{className:"text-gray-400",children:"›"}),r.jsx("span",{className:"text-gray-900 font-medium",children:"Select Company"})]})})}),(0,r.jsxs)("div",{className:"max-w-6xl mx-auto px-6 py-8 bg-white",children:[(0,r.jsxs)("div",{className:"page-header",children:[(0,r.jsxs)("div",{className:"header-left",children:[r.jsx("div",{className:"page-icon",children:r.jsx(i.$xp,{size:24})}),r.jsx("h1",{children:"Manage Groups"})]}),(0,r.jsxs)("button",{className:"add-new-group-btn",onClick:()=>{b(!0)},style:{background:"linear-gradient(135deg, #2563eb 0%, #8b5cf6 100%)",border:"none"},children:[r.jsx("span",{children:"+"}),"Add New Group"]})]}),r.jsx("div",{className:"mb-6",children:r.jsx("p",{className:"text-gray-600",style:{fontSize:"16px",lineHeight:"1.6",fontFamily:"'SF Pro', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif"},children:"\xa0Oversee all employer groups and their benefit plan assignments"})}),(0,r.jsxs)("div",{className:"stats-grid",children:[(0,r.jsxs)("div",{className:"stat-card",children:[(0,r.jsxs)("div",{className:"stat-content",children:[r.jsx("div",{className:"stat-number",children:S}),r.jsx("div",{className:"stat-label",children:"Active Companies"})]}),r.jsx("div",{className:"stat-icon",children:r.jsx(i.$xp,{size:24})})]}),(0,r.jsxs)("div",{className:"stat-card",children:[(0,r.jsxs)("div",{className:"stat-content",children:[r.jsx("div",{className:"stat-number",children:_.toLocaleString()}),r.jsx("div",{className:"stat-label",children:"Total Employees"})]}),r.jsx("div",{className:"stat-icon",children:r.jsx(i.Otr,{size:24})})]}),(0,r.jsxs)("div",{className:"stat-card",children:[(0,r.jsxs)("div",{className:"stat-content",children:[r.jsx("div",{className:"stat-number",children:h}),r.jsx("div",{className:"stat-label",children:"Plan Assignments"})]}),r.jsx("div",{className:"stat-icon",children:r.jsx(i.GwR,{size:24})})]})]}),r.jsx("div",{className:"mb-6",children:(0,r.jsxs)("div",{className:"relative w-full",children:[r.jsx(i.O6C,{className:"absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5"}),r.jsx("input",{type:"text",placeholder:"Search by company name, EIN, or location...",value:u,onChange:e=>m(e.target.value),className:"w-full pl-12 pr-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm bg-white transition-all duration-200 hover:border-gray-400 text-gray-900",style:{fontFamily:"'SF Pro', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif",boxShadow:"0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)",color:"#111827"}})]})}),0===N.length?(0,r.jsxs)("div",{className:"text-center py-12",children:[r.jsx(i.$xp,{className:"mx-auto h-12 w-12 text-gray-400"}),r.jsx("h3",{className:"mt-2 text-sm font-medium text-gray-900",children:"No companies found"}),r.jsx("p",{className:"mt-1 text-sm text-gray-500",children:u?"Try adjusting your search terms.":"No companies available."})]}):r.jsx("div",{className:"space-y-3",children:N.map(e=>r.jsx("div",{className:"bg-white border border-gray-200 rounded-xl transition-all duration-300 hover:border-gray-300",style:{boxShadow:"0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)"},onMouseEnter:e=>{e.currentTarget.style.transform="scale(1.03)",e.currentTarget.style.boxShadow="0 0 0 rgba(0, 0, 0, 0.15)"},onMouseLeave:e=>{e.currentTarget.style.transform="scale(1)",e.currentTarget.style.boxShadow="0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)"},children:r.jsx("div",{className:"p-4",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center gap-4 flex-1",children:[r.jsx("div",{className:"w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center flex-shrink-0",children:r.jsx(i.$xp,{className:"w-5 h-5 text-white"})}),(0,r.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,r.jsxs)("div",{className:"flex items-center gap-3 mb-1",children:[r.jsx("h3",{className:"text-base font-semibold text-gray-900 truncate",children:e.companyName}),r.jsx("span",{className:`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${"active"===e.status?"bg-green-100 text-green-800":"pending"===e.status?"bg-yellow-100 text-yellow-800":"bg-red-100 text-red-800"}`,children:e.status})]}),(0,r.jsxs)("div",{className:"flex items-center gap-6 text-sm text-gray-500",children:[r.jsx("span",{className:"inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-gray-100 text-gray-700",children:e.industry}),(0,r.jsxs)("div",{className:"flex items-center gap-1",children:[r.jsx(i.Otr,{className:"w-4 h-4"}),(0,r.jsxs)("span",{children:[e.companySize," employees"]})]}),(0,r.jsxs)("div",{className:"flex items-center gap-1",children:[r.jsx(i.k9l,{className:"w-4 h-4"}),r.jsx("span",{children:e.location})]})]})]})]}),r.jsx("div",{className:"flex-shrink-0 ml-6",children:r.jsx("button",{onClick:()=>w(e._id),className:"px-4 py-2 bg-gradient-to-r from-blue-600 to-purple-600 text-white font-medium rounded-lg hover:shadow-lg transition-all duration-200 hover:-translate-y-0.5 text-sm",style:{background:"linear-gradient(135deg, #2563eb 0%, #8b5cf6 100%)",boxShadow:"0 2px 4px -1px rgba(0, 0, 0, 0.1)",fontFamily:"'SF Pro', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif"},children:"Manage Plans"})})]})})},e._id))}),y&&r.jsx(d.Z,{isOpen:y,onClose:()=>b(!1),onSuccess:q})]})]})]})}t(79762)},20467:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});let r=(0,t(68570).createProxy)(String.raw`C:\beno\project_dev\qharmony-frontend-v1\src\app\ai-enroller\manage-groups\select-company\page.tsx#default`)}};var s=require("../../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[8948,1183,6621,9066,1999,8492,3253,576,6305,9902,5528],()=>t(48162));module.exports=r})();