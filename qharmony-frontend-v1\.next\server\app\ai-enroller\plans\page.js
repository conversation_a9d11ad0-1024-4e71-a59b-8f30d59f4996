(()=>{var e={};e.id=2373,e.ids=[2373],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},27790:e=>{"use strict";e.exports=require("assert")},84770:e=>{"use strict";e.exports=require("crypto")},17702:e=>{"use strict";e.exports=require("events")},92048:e=>{"use strict";e.exports=require("fs")},32615:e=>{"use strict";e.exports=require("http")},35240:e=>{"use strict";e.exports=require("https")},19801:e=>{"use strict";e.exports=require("os")},55315:e=>{"use strict";e.exports=require("path")},76162:e=>{"use strict";e.exports=require("stream")},74026:e=>{"use strict";e.exports=require("string_decoder")},74175:e=>{"use strict";e.exports=require("tty")},17360:e=>{"use strict";e.exports=require("url")},21764:e=>{"use strict";e.exports=require("util")},71568:e=>{"use strict";e.exports=require("zlib")},98061:e=>{"use strict";e.exports=require("node:assert")},92761:e=>{"use strict";e.exports=require("node:async_hooks")},72254:e=>{"use strict";e.exports=require("node:buffer")},40027:e=>{"use strict";e.exports=require("node:console")},6005:e=>{"use strict";e.exports=require("node:crypto")},65714:e=>{"use strict";e.exports=require("node:diagnostics_channel")},15673:e=>{"use strict";e.exports=require("node:events")},88849:e=>{"use strict";e.exports=require("node:http")},42725:e=>{"use strict";e.exports=require("node:http2")},87503:e=>{"use strict";e.exports=require("node:net")},38846:e=>{"use strict";e.exports=require("node:perf_hooks")},39630:e=>{"use strict";e.exports=require("node:querystring")},84492:e=>{"use strict";e.exports=require("node:stream")},31764:e=>{"use strict";e.exports=require("node:tls")},41041:e=>{"use strict";e.exports=require("node:url")},47261:e=>{"use strict";e.exports=require("node:util")},93746:e=>{"use strict";e.exports=require("node:util/types")},24086:e=>{"use strict";e.exports=require("node:worker_threads")},65628:e=>{"use strict";e.exports=require("node:zlib")},35670:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>l.a,__next_app__:()=>u,originalPathname:()=>p,pages:()=>c,routeModule:()=>x,tree:()=>d}),t(60847),t(6079),t(33709),t(35866);var a=t(23191),r=t(88716),n=t(37922),l=t.n(n),o=t(95231),i={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>o[e]);t.d(s,i);let d=["",{children:["ai-enroller",{children:["plans",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,60847)),"C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\plans\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,6079)),"C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,33709)),"C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,35866,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],c=["C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\plans\\page.tsx"],p="/ai-enroller/plans/page",u={require:t,loadChunk:()=>Promise.resolve()},x=new a.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/ai-enroller/plans/page",pathname:"/ai-enroller/plans",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},40853:(e,s,t)=>{Promise.resolve().then(t.bind(t,78021))},25600:(e,s,t)=>{Promise.resolve().then(t.bind(t,73615))},96103:(e,s,t)=>{"use strict";t.d(s,{Z:()=>c});var a=t(10326),r=t(17577),n=t(46226),l=t(25842),o=t(59028),i=t(7330),d=t(32049);let c=({isOpen:e,onClose:s})=>{let t=(0,l.I0)(),c=(0,r.useRef)(null),[p,u]=(0,r.useState)(""),x=(0,l.v9)(e=>(0,d.MP)(e)),h=(0,l.v9)(e=>e.user._id),m=(0,l.v9)(e=>e.user.userProfile),g=(0,l.v9)(e=>e.qHarmonyBot.chatHistory),f=(0,l.v9)(e=>e.qHarmonyBot.isLoading),b=e=>{if(""===e.trim())return;let s={sender:"user",message:e.replace(/\n/g,"<br/>"),timestamp:new Date().toISOString()};t((0,o.Hz)(s)),t((0,o.wt)(!0)),(0,i.b)(t,e,h,x),u("")},j=e=>{if(!e)return"";let[s,t]=e.split(" ");return`${s[0].toUpperCase()}${t?t[0].toUpperCase():""}`},y=()=>{c.current?.scrollIntoView({behavior:"smooth"})};(0,r.useEffect)(()=>{e&&0===g.length&&m.name&&(t((0,o.wt)(!0)),setTimeout(()=>{let e={sender:"bot",message:`Hey ${m.name}, how can I help you with your benefits enrollment today?`,timestamp:new Date().toISOString()};t((0,o.Hz)(e)),t((0,o.wt)(!1))},1e3))},[e,g.length,m.name,t]),(0,r.useEffect)(()=>{y()},[g]);let v=["Explain my plan options","Help me choose coverage","What are the costs?","Enrollment deadline"];return e?a.jsx("div",{style:{position:"fixed",top:0,left:0,right:0,bottom:0,backgroundColor:"rgba(0, 0, 0, 0.5)",zIndex:1e4,display:"flex",alignItems:"center",justifyContent:"center",padding:"20px"},children:(0,a.jsxs)("div",{style:{backgroundColor:"#f6f8fc",borderRadius:"12px",width:"100%",maxWidth:"800px",height:"600px",display:"flex",flexDirection:"column",boxShadow:"0 20px 60px rgba(0, 0, 0, 0.3)",overflow:"hidden"},children:[(0,a.jsxs)("div",{style:{backgroundColor:"#ffffff",padding:"16px 24px",borderBottom:"1px solid #e5e7eb",display:"flex",alignItems:"center",justifyContent:"space-between"},children:[(0,a.jsxs)("div",{style:{display:"flex",alignItems:"center",gap:"12px"},children:[a.jsx(n.default,{src:"/brea.png",alt:"Brea",width:40,height:40,style:{borderRadius:"50%"}}),(0,a.jsxs)("div",{children:[a.jsx("h3",{style:{margin:0,fontSize:"18px",fontWeight:"600",color:"#111827"},children:"Chat with Brea"}),a.jsx("p",{style:{margin:0,fontSize:"14px",color:"#6b7280"},children:"Your Benefits Specialist"})]})]}),a.jsx("button",{onClick:s,style:{background:"none",border:"none",fontSize:"24px",cursor:"pointer",color:"#6b7280",padding:"4px",borderRadius:"4px",display:"flex",alignItems:"center",justifyContent:"center"},children:"\xd7"})]}),(0,a.jsxs)("div",{style:{flex:1,overflow:"auto",padding:"16px",display:"flex",flexDirection:"column",gap:"12px"},children:[g.map((e,s)=>(0,a.jsxs)("div",{style:{display:"flex",flexDirection:"user"===e.sender?"row-reverse":"row",alignItems:"flex-start",gap:"8px"},children:[a.jsx("div",{style:{width:"32px",height:"32px",borderRadius:"50%",display:"flex",alignItems:"center",justifyContent:"center",backgroundColor:"user"===e.sender?"#000000":"transparent",color:"white",fontSize:"14px",fontWeight:"600",flexShrink:0},children:"user"===e.sender?j(m.name):a.jsx(n.default,{src:"/brea.png",alt:"Brea",width:32,height:32,style:{borderRadius:"50%"}})}),(0,a.jsxs)("div",{style:{maxWidth:"70%",backgroundColor:"user"===e.sender?"#000000":"#ffffff",color:"user"===e.sender?"#ffffff":"#000000",padding:"12px 16px",borderRadius:"user"===e.sender?"16px 16px 4px 16px":"16px 16px 16px 4px",fontSize:"14px",lineHeight:"1.5",boxShadow:"0 1px 3px rgba(0, 0, 0, 0.1)"},children:[a.jsx("div",{dangerouslySetInnerHTML:{__html:"bot"===e.sender?`${e.message}<br/><small style="color: #6b7280; font-size: 12px;">AI-generated content—verify before use.</small>`:e.message},style:{whiteSpace:"pre-wrap",wordBreak:"break-word"}}),"bot"===e.sender&&e.message.includes("how can I help you")&&s===g.length-1&&a.jsx("div",{style:{display:"flex",flexWrap:"wrap",gap:"8px",marginTop:"12px"},children:v.map(e=>a.jsx("button",{onClick:()=>b(e),style:{padding:"6px 12px",backgroundColor:"#f3f4f6",color:"#374151",border:"1px solid #d1d5db",borderRadius:"16px",fontSize:"12px",cursor:"pointer",transition:"all 0.2s"},onMouseOver:e=>{e.currentTarget.style.backgroundColor="#e5e7eb"},onMouseOut:e=>{e.currentTarget.style.backgroundColor="#f3f4f6"},children:e},e))})]})]},s)),f&&(0,a.jsxs)("div",{style:{display:"flex",alignItems:"flex-start",gap:"8px"},children:[a.jsx(n.default,{src:"/brea.png",alt:"Brea",width:32,height:32,style:{borderRadius:"50%"}}),a.jsx("div",{style:{backgroundColor:"#ffffff",padding:"12px 16px",borderRadius:"16px 16px 16px 4px",fontSize:"14px",color:"#6b7280",boxShadow:"0 1px 3px rgba(0, 0, 0, 0.1)"},children:"Brea is typing..."})]}),a.jsx("div",{ref:c})]}),a.jsx("div",{style:{backgroundColor:"#ffffff",padding:"16px",borderTop:"1px solid #e5e7eb"},children:(0,a.jsxs)("div",{style:{display:"flex",gap:"12px",alignItems:"flex-end"},children:[a.jsx("textarea",{value:p,onChange:e=>u(e.target.value),onKeyPress:e=>{"Enter"!==e.key||e.shiftKey||(e.preventDefault(),b(p))},placeholder:"Type your message...",style:{flex:1,padding:"12px",border:"1px solid #d1d5db",borderRadius:"8px",fontSize:"14px",resize:"none",minHeight:"44px",maxHeight:"120px",outline:"none",fontFamily:"inherit",color:"#000000",backgroundColor:"#ffffff"},rows:1}),a.jsx("button",{onClick:()=>b(p),disabled:!p.trim(),style:{padding:"12px 20px",backgroundColor:p.trim()?"#000000":"#e5e7eb",color:p.trim()?"#ffffff":"#9ca3af",border:"none",borderRadius:"8px",fontSize:"14px",fontWeight:"500",cursor:p.trim()?"pointer":"not-allowed",transition:"all 0.2s"},children:"Send"})]})})]})}):null}},78021:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>n});var a=t(10326);t(17577),t(23824),t(54658);var r=t(43058);function n({children:e}){return a.jsx(r.Z,{children:a.jsx("div",{className:"min-h-screen bg-white",style:{backgroundColor:"white",minHeight:"100vh"},children:e})})}},73615:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>m});var a=t(10326),r=t(17577),n=t(35047),l=t(38492),o=t(22509),i=t(43058),d=t(59562),c=t(78581);t(55394);var p=t(89009),u=t(53148),x=t(96103),h=t(67925);(0,p.bR)();let m=()=>{let e=(0,n.useRouter)(),[s,t]=(0,r.useState)([]),[p,m]=(0,r.useState)(""),[g,f]=(0,r.useState)("all"),[b,j]=(0,r.useState)(null),[y,v]=(0,r.useState)(!0),[C,N]=(0,r.useState)(null),[w,S]=(0,r.useState)([]),[k,P]=(0,r.useState)(!1),[D,q]=(0,r.useState)(!1),[E,_]=(0,r.useState)(null),[I,z]=(0,r.useState)(!1),[$,A]=(0,r.useState)(1),[R]=(0,r.useState)(10),[T,M]=(0,r.useState)(!1),[L,H]=(0,r.useState)(null),[O,B]=(0,r.useState)(!1),[F,W]=(0,r.useState)(null),[U,G]=(0,r.useState)(!1),[V,Z]=(0,r.useState)(null);(0,r.useEffect)(()=>{console.log("\uD83D\uDD0D localStorage debug:",{userid1:localStorage.getItem("userid1"),userId:localStorage.getItem("userId"),allKeys:Object.keys(localStorage)}),Y()},[]);let Y=async()=>{try{v(!0),N(null);let[e,s]=await Promise.all([(0,d.qY)(),(0,d.ie)()]);if(e.success&&e.data&&e.data.plans){console.log("\uD83D\uDCE6 Plans loaded:",e.data.plans),console.log("\uD83D\uDCCA First plan structure:",e.data.plans[0]),t(e.data.plans);let s=e.data.plans.length;e.data.plans.filter(e=>"Active"===e.status).length;let a=e.data.plans.filter(e=>{if(!e.createdAt)return!1;let s=new Date(e.createdAt),t=new Date;return t.setDate(t.getDate()-7),s>t}),r=e.data.plans.reduce((e,s)=>{let t=s.status||"Unknown";return e[t]=(e[t]||0)+1,e},{});j({totalPlans:s,plansByStatus:r,recentPlans:a})}else N(e.error||"Failed to load plans");s.success&&s.data&&S(s.data)}catch(e){N("Failed to load plans"),console.error("Error loading plans:",e)}finally{v(!1)}},K=(s||[]).filter(e=>{let s=(e.planName||"").toLowerCase().includes(p.toLowerCase())||(e.planCode||"").toLowerCase().includes(p.toLowerCase())||(e.coverageSubTypes||[]).some(e=>e.toLowerCase().includes(p.toLowerCase())),t="all"===g||(e.planType||"").toLowerCase()===g.toLowerCase()||(e.status||"").toLowerCase()===g.toLowerCase();return s&&t}),J=Math.ceil(K.length/R),X=($-1)*R,Q=X+R,ee=K.slice(X,Q),es=e=>{A(e)},et=(e,s,t)=>{W({title:e,message:s,onClose:t}),B(!0)},ea=(e,s,t,a)=>{H({title:e,message:s,onConfirm:t,onCancel:a}),M(!0)},er=()=>{B(!1),F?.onClose&&F.onClose(),W(null)},en=()=>{M(!1),L?.onCancel&&L.onCancel(),H(null)},el=(e,s,t,a)=>{Z({title:e,fields:s,onSubmit:t,onCancel:a}),G(!0)},eo=()=>{G(!1),V?.onCancel&&V.onCancel(),Z(null)},ei=async e=>{console.log("\uD83D\uDD27 handleEditPlan called with planId:",e);try{let t=(await u.be.get(`/api/pre-enrollment/plans/${e}/can-edit`)).data;if(t.canEdit){let t=s.find(s=>s._id===e);t?(_(t),q(!0)):et("Error","Plan not found")}else et("Cannot Edit Plan",t.message)}catch(e){console.error("Error checking plan editability:",e),et("Error","Error checking plan editability")}},ed=async e=>{console.log("\uD83D\uDD27 handleCopyPlan called with planId:",e);try{let t=s.find(s=>s._id===e);if(!t){et("Error","Plan not found");return}el("Copy Plan",[{name:"planName",label:"Plan Name",placeholder:"Enter name for the copied plan",defaultValue:`${t.planName} (Copy)`,required:!0},{name:"planCode",label:"Plan Code (Optional)",placeholder:"Enter plan code for the copied plan",defaultValue:`${t.planCode||""}-COPY`,required:!1}],async s=>{let t=s.planName,a=s.planCode;try{await u.be.post(`/api/pre-enrollment/plans/${e}/duplicate`,{planName:t,planCode:a||void 0}),et("Success","Plan copied successfully!"),Y()}catch(s){console.error("Error copying plan:",s);let e=s.response?.data?.error||s.response?.data?.message||s.message||"Error copying plan";et("Error",`Error copying plan: ${e}`)}})}catch(e){console.error("Error copying plan:",e),et("Error","Error copying plan")}},ec=async e=>{try{let s=(await u.be.get(`/api/pre-enrollment/plans/${e}/can-delete`)).data;if(s.canDelete)ea("Delete Plan","Are you sure you want to delete this plan? This action cannot be undone.",async()=>{try{await u.be.delete(`/api/pre-enrollment/plans/${e}`),et("Success","Plan deleted successfully!"),Y()}catch(s){console.error("Error deleting plan:",s);let e=s.response?.data?.error||s.response?.data?.message||s.message||"Error deleting plan. Please try again.";et("Error",`Error deleting plan: ${e}`)}});else{let t=(await u.be.get(`/api/pre-enrollment/plans/${e}/dependent-assignments`)).data,a=t.dependentAssignments?.map(e=>`Assignment ${e._id}`).join(", ")||"Unknown assignments";et("Cannot Delete Plan",`${s.message}

This plan is referenced by ${t.count} assignment(s):
${a}`)}}catch(s){console.error("Error deleting plan:",s);let e=s.response?.data?.error||s.response?.data?.message||s.message||"Error deleting plan";et("Error",`Error deleting plan: ${e}`)}},ep=async e=>{console.log("\uD83D\uDD27 handleActivatePlan called with planId:",e);try{await u.be.post(`/api/pre-enrollment/plans/${e}/activate`),et("Success","Plan activated successfully!"),Y()}catch(s){console.error("Error activating plan:",s);let e=s.response?.data?.error||s.response?.data?.message||s.message||"Error activating plan. Please try again.";et("Error",`Error activating plan: ${e}`)}},eu=async e=>{try{ea("Convert to Draft","Are you sure you want to convert this plan to draft? It will no longer be available for new assignments.",async()=>{await u.be.post(`/api/pre-enrollment/plans/${e}/convert-to-draft`),et("Success","Plan converted to draft successfully!"),Y()})}catch(s){console.error("Error converting plan to draft:",s);let e=s.response?.data?.error||s.response?.data?.message||s.message||"Error converting plan to draft. Please try again.";et("Error",`Error converting plan to draft: ${e}`)}},ex=()=>{q(!1),_(null)};return(0,a.jsxs)(i.Z,{children:[a.jsx(h.Z,{}),a.jsx("div",{className:"plans-wrapper",children:(0,a.jsxs)("div",{className:"plans-page",children:[(0,a.jsxs)("div",{className:"plans-header",children:[a.jsx("div",{className:"header-left",children:(0,a.jsxs)("div",{className:"left-align-wrapper",children:[a.jsx("div",{className:"title-icon",children:a.jsx(l.vrJ,{size:24,style:{color:"#ffffff"}})}),(0,a.jsxs)("div",{className:"title-content",children:[a.jsx("h1",{children:"Manage Plans"}),a.jsx("p",{children:"Manage and view all insurance plans"})]})]})}),(0,a.jsxs)("div",{className:"header-actions",children:[(0,a.jsxs)("button",{className:"create-btn",onClick:()=>{_(null),q(!0)},children:[a.jsx(l.r7I,{size:16}),"Create New Plan"]}),(0,a.jsxs)("button",{className:"ask-questions-btn",onClick:()=>z(!0),children:[a.jsx(o.$Rt,{size:16}),"Ask Questions"]}),(0,a.jsxs)("button",{className:"dashboard-btn",onClick:()=>e.push("/ai-enroller"),children:[a.jsx(o.s0d,{size:16}),"Dashboard"]})]})]}),b&&(0,a.jsxs)("div",{className:"stats-grid",children:[(0,a.jsxs)("div",{className:"stat-card blue",children:[(0,a.jsxs)("div",{className:"stat-content",children:[a.jsx("div",{className:"stat-label",children:"Total Plans"}),a.jsx("div",{className:"stat-number",children:b.totalPlans})]}),a.jsx("div",{className:"stat-icon blue",children:a.jsx(o.s0d,{size:24})})]}),(0,a.jsxs)("div",{className:"stat-card green",children:[(0,a.jsxs)("div",{className:"stat-content",children:[a.jsx("div",{className:"stat-label",children:"Active Plans"}),a.jsx("div",{className:"stat-number",children:b.plansByStatus?.Active||0})]}),a.jsx("div",{className:"stat-icon green",children:a.jsx(o.euf,{size:24})})]}),(0,a.jsxs)("div",{className:"stat-card orange",children:[(0,a.jsxs)("div",{className:"stat-content",children:[a.jsx("div",{className:"stat-label",children:"Recent Plans"}),a.jsx("div",{className:"stat-number",children:b.recentPlans?.length||0})]}),a.jsx("div",{className:"stat-icon orange",children:a.jsx(o.qXL,{size:24})})]})]}),(0,a.jsxs)("div",{className:"search-filter-section",children:[(0,a.jsxs)("div",{className:"filter-icon",children:[a.jsx(l.O6C,{size:16}),a.jsx("span",{children:"Search & Filter"})]}),(0,a.jsxs)("div",{className:"search-controls",children:[a.jsx("input",{type:"text",placeholder:"Search by plan name, code, or carrier type...",value:p,onChange:e=>m(e.target.value),className:"search-input"}),(0,a.jsxs)("select",{className:"status-filter",value:g,onChange:e=>f(e.target.value),children:[a.jsx("option",{value:"all",children:"All Statuses"}),a.jsx("option",{value:"active",children:"Active"}),a.jsx("option",{value:"inactive",children:"Inactive"}),a.jsx("option",{value:"draft",children:"Draft"}),a.jsx("option",{value:"template",children:"Template"}),a.jsx("option",{value:"archived",children:"Archived"})]}),(0,a.jsxs)("select",{className:"carrier-filter",children:[a.jsx("option",{value:"all",children:"All Carriers"}),w.map(e=>a.jsx("option",{value:e._id,children:e.carrierName},e._id))]}),a.jsx("button",{className:"clear-filters-btn",onClick:()=>{m(""),f("all"),A(1)},children:"Clear Filters"})]}),(0,a.jsxs)("div",{className:"results-count",children:["Showing ",K.length," of ",s.length," plans"]})]}),y&&(0,a.jsxs)("div",{className:"loading-state",children:[a.jsx("div",{className:"loading-spinner"}),a.jsx("p",{children:"Loading plans..."})]}),C&&(0,a.jsxs)("div",{className:"error-state",children:[(0,a.jsxs)("p",{children:["Error: ",C]}),a.jsx("button",{onClick:Y,className:"retry-btn",children:"Retry"})]}),!y&&!C&&a.jsx("div",{className:"plans-table-container",children:0===K.length?(0,a.jsxs)("div",{className:"empty-state",children:[a.jsx(o.euf,{size:48}),a.jsx("h3",{children:"No Plans Found"}),a.jsx("p",{children:0===s.length?"You haven't created any plans yet. Create your first plan to get started.":"No plans match your search criteria. Try adjusting your filters."}),(0,a.jsxs)("button",{className:"create-first-plan-btn",onClick:()=>e.push("/ai-enroller/create-plan"),children:[a.jsx(l.r7I,{size:16}),"Create Your First Plan"]})]}):(0,a.jsxs)(a.Fragment,{children:[a.jsx("div",{className:"table-header",children:a.jsx("h3",{children:"Manage Plans"})}),a.jsx("div",{className:"table-wrapper",children:(0,a.jsxs)("table",{className:"plans-table",children:[a.jsx("thead",{children:(0,a.jsxs)("tr",{children:[a.jsx("th",{children:"Plan Name"}),a.jsx("th",{children:"Plan Code"}),a.jsx("th",{children:"Coverage Type"}),a.jsx("th",{children:"Status"}),a.jsx("th",{children:"Actions"})]})}),a.jsx("tbody",{children:ee.map(e=>(console.log("\uD83D\uDD0D Rendering plan:",e._id,e.planName),(0,a.jsxs)("tr",{children:[a.jsx("td",{className:"plan-name-cell",children:a.jsx("div",{className:"plan-name",children:e.planName})}),a.jsx("td",{className:"plan-code-cell",children:a.jsx("span",{className:"plan-code-badge",children:e.planCode})}),a.jsx("td",{className:"carrier-type-cell",children:a.jsx("span",{className:`carrier-type-badge ${(e.coverageSubTypes?.[0]||e.coverageType)?.toLowerCase().replace(" ","-")}`,children:e.coverageSubTypes?.[0]||e.coverageType})}),a.jsx("td",{className:"status-cell",children:a.jsx("span",{className:`status-badge ${(e.status||"unknown").toLowerCase()}`,children:e.status||"Unknown"})}),a.jsx("td",{className:"actions-cell",children:(0,a.jsxs)("div",{className:"action-buttons",children:[a.jsx("button",{className:"action-btn edit",onClick:()=>ei(e._id),title:"Edit Plan",children:a.jsx(l._vs,{size:16})}),a.jsx("button",{className:"action-btn copy",onClick:()=>ed(e._id),title:"Copy Plan",children:a.jsx(l.GS5,{size:16})}),"Active"===e.status?a.jsx("button",{className:"action-btn deactivate",onClick:()=>eu(e._id),title:"Convert to Draft",children:a.jsx(l.hvF,{size:16})}):a.jsx("button",{className:"action-btn activate",onClick:()=>ep(e._id),title:"Activate Plan",children:a.jsx(l.aL1,{size:16})}),a.jsx("button",{className:"action-btn delete",onClick:()=>ec(e._id),title:"Delete Plan",children:a.jsx(l.Bhs,{size:16})})]})})]},e._id)))})]})}),J>1&&(0,a.jsxs)("div",{className:"pagination-container",children:[(0,a.jsxs)("div",{className:"pagination-info",children:["Showing ",X+1,"-",Math.min(Q,K.length)," of ",K.length," plans"]}),(0,a.jsxs)("div",{className:"pagination-controls",children:[a.jsx("button",{className:"pagination-btn",onClick:()=>es($-1),disabled:1===$,children:"Previous"}),Array.from({length:J},(e,s)=>s+1).map(e=>a.jsx("button",{className:`pagination-btn ${e===$?"active":""}`,onClick:()=>es(e),children:e},e)),a.jsx("button",{className:"pagination-btn",onClick:()=>es($+1),disabled:$===J,children:"Next"})]})]})]})}),D&&a.jsx("div",{className:"modal-overlay",onClick:ex,children:(0,a.jsxs)("div",{className:"modal-content plan-modal-content",onClick:e=>e.stopPropagation(),children:[(0,a.jsxs)("div",{className:"modal-header",children:[a.jsx("h2",{children:E?"Edit Plan":"Create New Plan"}),a.jsx("button",{className:"modal-close",onClick:ex,children:a.jsx(l.fMW,{size:20})})]}),a.jsx("div",{className:"modal-body",children:a.jsx(c.Z,{initialData:E,onSubmit:e=>{q(!1),_(null),Y()},onCancel:ex,isModal:!0,existingCarriers:w})})]})}),a.jsx(x.Z,{isOpen:I,onClose:()=>z(!1)}),O&&F&&a.jsx("div",{className:"modal-overlay",onClick:er,children:(0,a.jsxs)("div",{className:"modal-content",onClick:e=>e.stopPropagation(),children:[(0,a.jsxs)("div",{className:"modal-header",children:[a.jsx("h2",{children:F.title}),a.jsx("button",{className:"modal-close",onClick:er,children:a.jsx(l.fMW,{size:20})})]}),a.jsx("div",{className:"modal-body",children:a.jsx("p",{style:{whiteSpace:"pre-line"},children:F.message})}),a.jsx("div",{className:"modal-footer",children:a.jsx("button",{className:"modal-btn primary",onClick:er,children:"OK"})})]})}),T&&L&&a.jsx("div",{className:"modal-overlay",onClick:en,children:(0,a.jsxs)("div",{className:"modal-content",onClick:e=>e.stopPropagation(),children:[(0,a.jsxs)("div",{className:"modal-header",children:[a.jsx("h2",{children:L.title}),a.jsx("button",{className:"modal-close",onClick:en,children:a.jsx(l.fMW,{size:20})})]}),a.jsx("div",{className:"modal-body",children:a.jsx("p",{style:{whiteSpace:"pre-line"},children:L.message})}),(0,a.jsxs)("div",{className:"modal-footer",children:[a.jsx("button",{className:"modal-btn secondary",onClick:en,children:"Cancel"}),a.jsx("button",{className:"modal-btn primary",onClick:()=>{L?.onConfirm&&L.onConfirm(),en()},children:"Confirm"})]})]})}),U&&V&&a.jsx("div",{className:"modal-overlay",onClick:eo,children:(0,a.jsxs)("div",{className:"modal-content",onClick:e=>e.stopPropagation(),children:[(0,a.jsxs)("div",{className:"modal-header",children:[a.jsx("h2",{children:V.title}),a.jsx("button",{className:"modal-close",onClick:eo,children:a.jsx(l.fMW,{size:20})})]}),(0,a.jsxs)("form",{onSubmit:e=>{e.preventDefault();let s=new FormData(e.target),t={};V.fields.forEach(e=>{t[e.name]=s.get(e.name)||""}),V.onSubmit(t),eo()},children:[a.jsx("div",{className:"modal-body",children:V.fields.map(e=>(0,a.jsxs)("div",{className:"form-group",style:{marginBottom:"1rem"},children:[(0,a.jsxs)("label",{htmlFor:e.name,style:{display:"block",marginBottom:"0.5rem",fontSize:"14px",lineHeight:"21px",fontWeight:"500",color:"#374151"},children:[e.label,e.required&&a.jsx("span",{style:{color:"#dc2626"},children:"*"})]}),a.jsx("input",{type:"text",id:e.name,name:e.name,placeholder:e.placeholder,defaultValue:e.defaultValue,required:e.required,style:{width:"100%",padding:"0.75rem",border:"1px solid #d1d5db",borderRadius:"0.5rem",fontSize:"14px",lineHeight:"21px",fontFamily:"'SF Pro', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif"}})]},e.name))}),(0,a.jsxs)("div",{className:"modal-footer",children:[a.jsx("button",{type:"button",className:"modal-btn secondary",onClick:eo,children:"Cancel"}),a.jsx("button",{type:"submit",className:"modal-btn primary",children:"Submit"})]})]})]})})]})})]})}},7330:(e,s,t)=>{"use strict";t.d(s,{b:()=>n});var a=t(59028);let r=(0,t(89009).GU)();async function n(e,s,t,n){let l={user_id:t,user_message:s,team_id:n};try{console.log("Sending chat message:",l);let s=await fetch(`${r}/chat`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(l)});if(!s.body)throw Error("Readable stream not supported");let t=s.body.getReader(),n=new TextDecoder("utf-8"),o={sender:"bot",message:"",timestamp:new Date().toISOString()};for(e((0,a.wt)(!0));;){let{done:s,value:r}=await t.read();if(s)break;let l=n.decode(r,{stream:!0});e((0,a.wt)(!1)),console.log("Chunk:",l),o.message+=l,e((0,a.Hz)({sender:"bot",message:l,timestamp:new Date().toISOString()}))}}catch(t){console.error("Error sending chat message:",t);let s={sender:"bot",message:"Sorry, I encountered an error while processing your request.",timestamp:new Date().toISOString()};e((0,a.Hz)(s)),e((0,a.wt)(!1))}}},89009:(e,s,t)=>{"use strict";t.d(s,{GU:()=>n,bR:()=>a,n5:()=>r});let a=()=>"http://localhost:8080",r=()=>{let e="userid1",s="userId",t=localStorage.getItem(e)||localStorage.getItem(s);return(console.log("\uD83D\uDD0D getUserId debug:",{primaryKey:e,altKey:s,primaryValue:localStorage.getItem(e),altValue:localStorage.getItem(s),finalUserId:t}),t)?t:(console.error("❌ User ID not found in localStorage"),"default-user")},n=()=>"https://bot.benosphere.com"},6079:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});let a=(0,t(68570).createProxy)(String.raw`C:\beno\project_dev\qharmony-frontend-v1\src\app\ai-enroller\layout.tsx#default`)},60847:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});let a=(0,t(68570).createProxy)(String.raw`C:\beno\project_dev\qharmony-frontend-v1\src\app\ai-enroller\plans\page.tsx#default`)},54658:()=>{},55394:()=>{},23824:()=>{}};var s=require("../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),a=s.X(0,[8948,1183,6621,9066,1999,8492,3253,3351,576,6305,8618,8581],()=>t(35670));module.exports=a})();