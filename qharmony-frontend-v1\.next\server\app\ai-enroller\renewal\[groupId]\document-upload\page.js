(()=>{var e={};e.id=6349,e.ids=[6349],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},27790:e=>{"use strict";e.exports=require("assert")},84770:e=>{"use strict";e.exports=require("crypto")},17702:e=>{"use strict";e.exports=require("events")},92048:e=>{"use strict";e.exports=require("fs")},32615:e=>{"use strict";e.exports=require("http")},35240:e=>{"use strict";e.exports=require("https")},19801:e=>{"use strict";e.exports=require("os")},55315:e=>{"use strict";e.exports=require("path")},76162:e=>{"use strict";e.exports=require("stream")},74026:e=>{"use strict";e.exports=require("string_decoder")},74175:e=>{"use strict";e.exports=require("tty")},17360:e=>{"use strict";e.exports=require("url")},21764:e=>{"use strict";e.exports=require("util")},71568:e=>{"use strict";e.exports=require("zlib")},98061:e=>{"use strict";e.exports=require("node:assert")},92761:e=>{"use strict";e.exports=require("node:async_hooks")},72254:e=>{"use strict";e.exports=require("node:buffer")},40027:e=>{"use strict";e.exports=require("node:console")},6005:e=>{"use strict";e.exports=require("node:crypto")},65714:e=>{"use strict";e.exports=require("node:diagnostics_channel")},15673:e=>{"use strict";e.exports=require("node:events")},88849:e=>{"use strict";e.exports=require("node:http")},42725:e=>{"use strict";e.exports=require("node:http2")},87503:e=>{"use strict";e.exports=require("node:net")},38846:e=>{"use strict";e.exports=require("node:perf_hooks")},39630:e=>{"use strict";e.exports=require("node:querystring")},84492:e=>{"use strict";e.exports=require("node:stream")},31764:e=>{"use strict";e.exports=require("node:tls")},41041:e=>{"use strict";e.exports=require("node:url")},47261:e=>{"use strict";e.exports=require("node:util")},93746:e=>{"use strict";e.exports=require("node:util/types")},24086:e=>{"use strict";e.exports=require("node:worker_threads")},65628:e=>{"use strict";e.exports=require("node:zlib")},4613:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>l.a,__next_app__:()=>p,originalPathname:()=>u,pages:()=>c,routeModule:()=>m,tree:()=>d}),t(67637),t(6079),t(33709),t(35866);var r=t(23191),i=t(88716),a=t(37922),l=t.n(a),n=t(95231),o={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>n[e]);t.d(s,o);let d=["",{children:["ai-enroller",{children:["renewal",{children:["[groupId]",{children:["document-upload",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,67637)),"C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\renewal\\[groupId]\\document-upload\\page.tsx"]}]},{}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,6079)),"C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,33709)),"C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,35866,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],c=["C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\renewal\\[groupId]\\document-upload\\page.tsx"],u="/ai-enroller/renewal/[groupId]/document-upload/page",p={require:t,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:i.x.APP_PAGE,page:"/ai-enroller/renewal/[groupId]/document-upload/page",pathname:"/ai-enroller/renewal/[groupId]/document-upload",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},77340:(e,s,t)=>{Promise.resolve().then(t.bind(t,43643))},43643:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>n});var r=t(10326),i=t(17577),a=t(35047),l=t(38492);t(19288),t(16039),t(9873);let n=()=>{let e=(0,a.useParams)(),s=(0,a.useRouter)(),[t,n]=(0,i.useState)(4),o="TechCorp Solutions",[d,c]=(0,i.useState)([{id:"sbc",title:"Summary of Benefits and Coverage (SBC)",description:"Upload updated plan documents for TechCorp Solutions. These documents will be tagged with the new plan year and made available to HR and employees.",required:!0,files:[],placeholder:"No summary of benefits and coverage (sbc) uploaded yet"},{id:"plan-docs",title:"Plan Documents",description:"",required:!1,files:[],placeholder:"No plan documents uploaded yet"},{id:"rate-sheets",title:"Rate Sheets",description:"",required:!1,files:[],placeholder:"No rate sheets uploaded yet"},{id:"carrier-contracts",title:"Carrier Contracts",description:"",required:!1,files:[],placeholder:"No carrier contracts uploaded yet"},{id:"compliance-docs",title:"Compliance Documents",description:"",required:!1,files:[],placeholder:"No compliance documents uploaded yet"}]),u=[{number:1,title:"Review Current Plans",subtitle:"View existing benefit plans",active:!1,completed:!0},{number:2,title:"Renewal Options",subtitle:"Choose renewal type",active:!1,completed:!0},{number:3,title:"Plan Configuration",subtitle:"Set dates and modifications",active:!1,completed:!0},{number:4,title:"Document Upload",subtitle:"Upload plan documents",active:4===t},{number:5,title:"Validation",subtitle:"Review and validate setup",active:!1},{number:6,title:"Finalize",subtitle:"Complete renewal process",active:!1},{number:7,title:"Export",subtitle:"Download and share data",active:!1}],p=(e,s)=>{s&&c(t=>t.map(t=>t.id===e?{...t,files:[...t.files,...Array.from(s)]}:t))};return(0,r.jsxs)("div",{className:"plan-renewal-detail",children:[(0,r.jsxs)("div",{className:"detail-header",children:[(0,r.jsxs)("button",{className:"back-btn",onClick:()=>s.push("/ai-enroller/renewal"),children:[r.jsx(l.Tsu,{size:20}),"Back to Dashboard"]}),(0,r.jsxs)("div",{className:"header-info",children:[r.jsx("h1",{children:"Plan Renewal"}),r.jsx("h2",{children:o}),(0,r.jsxs)("div",{className:"step-indicator",children:["Step ",t," of 7"]})]}),r.jsx("div",{className:"completion-status",children:"57% Complete"})]}),r.jsx("div",{className:"renewal-steps",children:u.map((e,s)=>(0,r.jsxs)("div",{className:`renewal-step ${e.active?"active":""} ${e.completed?"completed":""}`,children:[r.jsx("div",{className:"step-number",children:e.completed?"✓":e.number}),(0,r.jsxs)("div",{className:"step-content",children:[r.jsx("div",{className:"step-title",children:e.title}),r.jsx("div",{className:"step-subtitle",children:e.subtitle})]}),s<u.length-1&&r.jsx("div",{className:"step-connector"})]},e.number))}),(0,r.jsxs)("div",{className:"document-upload-section",children:[(0,r.jsxs)("div",{className:"upload-header",children:[(0,r.jsxs)("div",{className:"upload-title",children:[r.jsx(l.tw,{size:20}),r.jsx("h3",{children:"Plan Document Upload"})]}),(0,r.jsxs)("p",{children:["Upload updated plan documents for ",o,". These documents will be tagged with the new plan year and made available to HR and employees."]})]}),(0,r.jsxs)("div",{className:"upload-content",children:[d.map(e=>(0,r.jsxs)("div",{className:"upload-card",children:[(0,r.jsxs)("div",{className:"card-header",children:[(0,r.jsxs)("div",{className:"header-content",children:[(0,r.jsxs)("h4",{children:[e.title,e.required&&r.jsx("span",{className:"required-badge",children:"Required"})]}),e.description&&r.jsx("p",{children:e.description})]}),(0,r.jsxs)("label",{className:"upload-btn",children:[r.jsx(l.tw,{size:16}),"Upload Files",r.jsx("input",{type:"file",multiple:!0,accept:".pdf,.doc,.docx,.xls,.xlsx",onChange:s=>p(e.id,s.target.files),style:{display:"none"}})]})]}),r.jsx("div",{className:"upload-area",children:e.files.length>0?r.jsx("div",{className:"files-list",children:e.files.map((e,s)=>(0,r.jsxs)("div",{className:"file-item",children:[r.jsx(l.vrJ,{size:16}),r.jsx("span",{children:e.name}),(0,r.jsxs)("span",{className:"file-size",children:[(e.size/1024/1024).toFixed(2)," MB"]})]},s))}):(0,r.jsxs)("div",{className:"empty-state",children:[r.jsx(l.vrJ,{size:24}),r.jsx("span",{children:e.placeholder})]})})]},e.id)),(0,r.jsxs)("div",{className:"guidelines-card",children:[(0,r.jsxs)("div",{className:"card-header",children:[r.jsx(l.if7,{size:20}),r.jsx("h4",{children:"Document Guidelines"})]}),r.jsx("div",{className:"guidelines-content",children:(0,r.jsxs)("ul",{children:[r.jsx("li",{children:"Accepted formats: PDF, DOC, DOCX, XLS, XLSX"}),r.jsx("li",{children:"Maximum file size: 10MB per file"}),r.jsx("li",{children:"Documents will be automatically tagged with the plan year"}),r.jsx("li",{children:"SBC documents are required for compliance"}),r.jsx("li",{children:"All uploads will be available to HR admins immediately"})]})})]})]}),(0,r.jsxs)("div",{className:"navigation-section",children:[(0,r.jsxs)("button",{className:"nav-btn secondary",onClick:()=>{s.back()},children:[r.jsx(l.Tsu,{size:16}),"Previous"]}),(0,r.jsxs)("div",{className:"nav-actions",children:[r.jsx("button",{className:"nav-btn secondary",onClick:()=>{s.push(`/ai-enroller/renewal/${e.groupId}/validation`)},children:"Skip Upload"}),r.jsx("button",{className:"nav-btn primary enabled",onClick:()=>{s.push(`/ai-enroller/renewal/${e.groupId}/validation`)},children:"Continue"})]})]})]})]})}},67637:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});let r=(0,t(68570).createProxy)(String.raw`C:\beno\project_dev\qharmony-frontend-v1\src\app\ai-enroller\renewal\[groupId]\document-upload\page.tsx#default`)},9873:()=>{}};var s=require("../../../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[8948,1183,6621,8492,576,4437],()=>t(4613));module.exports=r})();