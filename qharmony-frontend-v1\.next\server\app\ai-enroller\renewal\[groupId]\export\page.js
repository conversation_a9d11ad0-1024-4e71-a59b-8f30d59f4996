(()=>{var e={};e.id=2721,e.ids=[2721],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},27790:e=>{"use strict";e.exports=require("assert")},84770:e=>{"use strict";e.exports=require("crypto")},17702:e=>{"use strict";e.exports=require("events")},92048:e=>{"use strict";e.exports=require("fs")},32615:e=>{"use strict";e.exports=require("http")},35240:e=>{"use strict";e.exports=require("https")},19801:e=>{"use strict";e.exports=require("os")},55315:e=>{"use strict";e.exports=require("path")},76162:e=>{"use strict";e.exports=require("stream")},74026:e=>{"use strict";e.exports=require("string_decoder")},74175:e=>{"use strict";e.exports=require("tty")},17360:e=>{"use strict";e.exports=require("url")},21764:e=>{"use strict";e.exports=require("util")},71568:e=>{"use strict";e.exports=require("zlib")},98061:e=>{"use strict";e.exports=require("node:assert")},92761:e=>{"use strict";e.exports=require("node:async_hooks")},72254:e=>{"use strict";e.exports=require("node:buffer")},40027:e=>{"use strict";e.exports=require("node:console")},6005:e=>{"use strict";e.exports=require("node:crypto")},65714:e=>{"use strict";e.exports=require("node:diagnostics_channel")},15673:e=>{"use strict";e.exports=require("node:events")},88849:e=>{"use strict";e.exports=require("node:http")},42725:e=>{"use strict";e.exports=require("node:http2")},87503:e=>{"use strict";e.exports=require("node:net")},38846:e=>{"use strict";e.exports=require("node:perf_hooks")},39630:e=>{"use strict";e.exports=require("node:querystring")},84492:e=>{"use strict";e.exports=require("node:stream")},31764:e=>{"use strict";e.exports=require("node:tls")},41041:e=>{"use strict";e.exports=require("node:url")},47261:e=>{"use strict";e.exports=require("node:util")},93746:e=>{"use strict";e.exports=require("node:util/types")},24086:e=>{"use strict";e.exports=require("node:worker_threads")},65628:e=>{"use strict";e.exports=require("node:zlib")},44060:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>n.a,__next_app__:()=>u,originalPathname:()=>p,pages:()=>d,routeModule:()=>m,tree:()=>c}),r(50751),r(6079),r(33709),r(35866);var t=r(23191),i=r(88716),a=r(37922),n=r.n(a),o=r(95231),l={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);r.d(s,l);let c=["",{children:["ai-enroller",{children:["renewal",{children:["[groupId]",{children:["export",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,50751)),"C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\renewal\\[groupId]\\export\\page.tsx"]}]},{}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,6079)),"C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,33709)),"C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,35866,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],d=["C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\renewal\\[groupId]\\export\\page.tsx"],p="/ai-enroller/renewal/[groupId]/export/page",u={require:r,loadChunk:()=>Promise.resolve()},m=new t.AppPageRouteModule({definition:{kind:i.x.APP_PAGE,page:"/ai-enroller/renewal/[groupId]/export/page",pathname:"/ai-enroller/renewal/[groupId]/export",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},12482:(e,s,r)=>{Promise.resolve().then(r.bind(r,46727))},46727:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>o});var t=r(10326),i=r(17577),a=r(35047),n=r(38492);r(19288),r(16039),r(7187);let o=()=>{(0,a.useParams)();let e=(0,a.useRouter)(),[s,r]=(0,i.useState)(7),o="Green Valley Manufacturing",l=[{number:1,title:"Review Current Plans",subtitle:"View existing benefit plans",active:!1,completed:!0},{number:2,title:"Renewal Options",subtitle:"Choose renewal type",active:!1,completed:!0},{number:3,title:"Plan Configuration",subtitle:"Set dates and modifications",active:!1,completed:!0},{number:4,title:"Document Upload",subtitle:"Upload plan documents",active:!1,completed:!0},{number:5,title:"Validation",subtitle:"Review and validate setup",active:!1,completed:!0},{number:6,title:"Finalize",subtitle:"Complete renewal process",active:!1,completed:!0},{number:7,title:"Export",subtitle:"Download and share data",active:7===s}],c=[{id:"csv",title:"CSV Export",description:"Spreadsheet format with plan details, rates, and employee data",filename:"Green_Valley_Manufacturing_renewal_2025.csv",bestFor:"Import into payroll systems, Excel analysis",icon:t.jsx(n.vrJ,{size:24})},{id:"json",title:"JSON Export",description:"Structured data format for API integration and system sync",filename:"Green_Valley_Manufacturing_renewal_2025.json",bestFor:"API integration, automated system sync",icon:t.jsx(n.oT$,{size:24})},{id:"summary",title:"Summary Report",description:"PDF report with renewal overview and plan comparisons",filename:"Green_Valley_Manufacturing_renewal_summary.pdf",bestFor:"Client presentations, internal reporting",icon:t.jsx(n.vrJ,{size:24})}],d={group:"Green Valley Manufacturing",effectiveDate:"1/1/2025",plansRenewed:2,employeesAffected:89,documentsUploaded:0,completed:"26/5/2025"},p=e=>{console.log(`Downloading ${e} export...`)};return(0,t.jsxs)("div",{className:"plan-renewal-detail",children:[(0,t.jsxs)("div",{className:"detail-header",children:[(0,t.jsxs)("button",{className:"back-btn",onClick:()=>e.push("/ai-enroller/renewal"),children:[t.jsx(n.Tsu,{size:20}),"Back to Dashboard"]}),(0,t.jsxs)("div",{className:"header-info",children:[t.jsx("h1",{children:"Plan Renewal"}),t.jsx("h2",{children:o}),(0,t.jsxs)("div",{className:"step-indicator",children:["Step ",s," of 7"]})]}),t.jsx("div",{className:"completion-status",children:"100% Complete"})]}),t.jsx("div",{className:"renewal-steps",children:l.map((e,s)=>(0,t.jsxs)("div",{className:`renewal-step ${e.active?"active":""} ${e.completed?"completed":""}`,children:[t.jsx("div",{className:"step-number",children:e.completed?"✓":e.number}),(0,t.jsxs)("div",{className:"step-content",children:[t.jsx("div",{className:"step-title",children:e.title}),t.jsx("div",{className:"step-subtitle",children:e.subtitle})]}),s<l.length-1&&t.jsx("div",{className:"step-connector"})]},e.number))}),(0,t.jsxs)("div",{className:"export-section",children:[(0,t.jsxs)("div",{className:"export-header",children:[(0,t.jsxs)("div",{className:"export-title",children:[t.jsx(n.yFZ,{size:20}),t.jsx("h3",{children:"Export & Share Renewal Data"})]}),t.jsx("p",{children:"Download renewal data in various formats or copy summary information for sharing with your team."})]}),(0,t.jsxs)("div",{className:"export-content",children:[(0,t.jsxs)("div",{className:"success-message",children:[t.jsx(n.PjL,{size:24}),(0,t.jsxs)("div",{children:[t.jsx("h4",{children:"Renewal Successfully Completed!"}),(0,t.jsxs)("p",{children:["All plans for ",o," have been renewed and are now active. Use the options below to export data or share the renewal summary."]})]})]}),t.jsx("div",{className:"export-options",children:c.map(e=>(0,t.jsxs)("div",{className:"export-card",children:[(0,t.jsxs)("div",{className:"export-card-header",children:[t.jsx("div",{className:"export-icon",children:e.icon}),(0,t.jsxs)("div",{className:"export-info",children:[t.jsx("h4",{children:e.title}),t.jsx("p",{children:e.description})]})]}),(0,t.jsxs)("div",{className:"export-details",children:[(0,t.jsxs)("div",{className:"filename",children:[t.jsx("strong",{children:"Filename:"})," ",e.filename]}),(0,t.jsxs)("div",{className:"best-for",children:[t.jsx("strong",{children:"Best for:"})," ",e.bestFor]})]}),(0,t.jsxs)("button",{className:"download-btn",onClick:()=>p(e.id),children:[t.jsx(n.yFZ,{size:16}),"Download ",e.title]})]},e.id))}),(0,t.jsxs)("div",{className:"quick-share",children:[t.jsx("h4",{children:"Quick Share Options"}),t.jsx("p",{children:"Quickly share renewal information with your team via common communication tools."}),(0,t.jsxs)("div",{className:"share-buttons",children:[(0,t.jsxs)("button",{className:"share-btn",onClick:()=>{let e=`Renewal Process Complete

Group: ${d.group}
Effective Date: ${d.effectiveDate}
Plans Renewed: ${d.plansRenewed}
Employees Affected: ${d.employeesAffected}
Completed: ${d.completed}`;navigator.clipboard.writeText(e),alert("Summary copied to clipboard!")},children:[t.jsx(n.zFu,{size:16}),"Copy to Clipboard",t.jsx("span",{className:"share-desc",children:"Summary text for Slack, Teams, or email"})]}),(0,t.jsxs)("button",{className:"share-btn",onClick:()=>{console.log("Sending email notification...")},children:[t.jsx(n.Zuw,{size:16}),"Send Email Notification",t.jsx("span",{className:"share-desc",children:"Quick email to stakeholders"})]})]})]}),(0,t.jsxs)("div",{className:"process-summary",children:[t.jsx("h4",{children:"Renewal Process Complete"}),(0,t.jsxs)("div",{className:"summary-grid",children:[(0,t.jsxs)("div",{className:"summary-item",children:[t.jsx("strong",{children:"Group:"})," ",d.group]}),(0,t.jsxs)("div",{className:"summary-item",children:[t.jsx("strong",{children:"Effective Date:"})," ",d.effectiveDate]}),(0,t.jsxs)("div",{className:"summary-item",children:[t.jsx("strong",{children:"Plans Renewed:"})," ",d.plansRenewed]}),(0,t.jsxs)("div",{className:"summary-item",children:[t.jsx("strong",{children:"Employees Affected:"})," ",d.employeesAffected]}),(0,t.jsxs)("div",{className:"summary-item",children:[t.jsx("strong",{children:"Documents Uploaded:"})," ",d.documentsUploaded]}),(0,t.jsxs)("div",{className:"summary-item",children:[t.jsx("strong",{children:"Completed:"})," ",d.completed]})]})]})]}),(0,t.jsxs)("div",{className:"navigation-section",children:[(0,t.jsxs)("button",{className:"nav-btn secondary",onClick:()=>e.back(),children:[t.jsx(n.Tsu,{size:16}),"Previous"]}),t.jsx("button",{className:"nav-btn primary enabled",onClick:()=>{e.push("/ai-enroller/renewal")},children:"Return to Dashboard"})]})]})]})}},50751:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>t});let t=(0,r(68570).createProxy)(String.raw`C:\beno\project_dev\qharmony-frontend-v1\src\app\ai-enroller\renewal\[groupId]\export\page.tsx#default`)},7187:()=>{}};var s=require("../../../../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),t=s.X(0,[8948,1183,6621,8492,576,4437],()=>r(44060));module.exports=t})();