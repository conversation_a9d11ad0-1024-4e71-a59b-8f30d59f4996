(()=>{var e={};e.id=123,e.ids=[123],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},27790:e=>{"use strict";e.exports=require("assert")},84770:e=>{"use strict";e.exports=require("crypto")},17702:e=>{"use strict";e.exports=require("events")},92048:e=>{"use strict";e.exports=require("fs")},32615:e=>{"use strict";e.exports=require("http")},35240:e=>{"use strict";e.exports=require("https")},19801:e=>{"use strict";e.exports=require("os")},55315:e=>{"use strict";e.exports=require("path")},76162:e=>{"use strict";e.exports=require("stream")},74026:e=>{"use strict";e.exports=require("string_decoder")},74175:e=>{"use strict";e.exports=require("tty")},17360:e=>{"use strict";e.exports=require("url")},21764:e=>{"use strict";e.exports=require("util")},71568:e=>{"use strict";e.exports=require("zlib")},98061:e=>{"use strict";e.exports=require("node:assert")},92761:e=>{"use strict";e.exports=require("node:async_hooks")},72254:e=>{"use strict";e.exports=require("node:buffer")},40027:e=>{"use strict";e.exports=require("node:console")},6005:e=>{"use strict";e.exports=require("node:crypto")},65714:e=>{"use strict";e.exports=require("node:diagnostics_channel")},15673:e=>{"use strict";e.exports=require("node:events")},88849:e=>{"use strict";e.exports=require("node:http")},42725:e=>{"use strict";e.exports=require("node:http2")},87503:e=>{"use strict";e.exports=require("node:net")},38846:e=>{"use strict";e.exports=require("node:perf_hooks")},39630:e=>{"use strict";e.exports=require("node:querystring")},84492:e=>{"use strict";e.exports=require("node:stream")},31764:e=>{"use strict";e.exports=require("node:tls")},41041:e=>{"use strict";e.exports=require("node:url")},47261:e=>{"use strict";e.exports=require("node:util")},93746:e=>{"use strict";e.exports=require("node:util/types")},24086:e=>{"use strict";e.exports=require("node:worker_threads")},65628:e=>{"use strict";e.exports=require("node:zlib")},91284:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>n.a,__next_app__:()=>p,originalPathname:()=>u,pages:()=>o,routeModule:()=>m,tree:()=>d}),r(72187),r(6079),r(33709),r(35866);var t=r(23191),i=r(88716),a=r(37922),n=r.n(a),l=r(95231),c={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>l[e]);r.d(s,c);let d=["",{children:["ai-enroller",{children:["renewal",{children:["[groupId]",{children:["finalize",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,72187)),"C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\renewal\\[groupId]\\finalize\\page.tsx"]}]},{}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,6079)),"C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,33709)),"C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,35866,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],o=["C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\renewal\\[groupId]\\finalize\\page.tsx"],u="/ai-enroller/renewal/[groupId]/finalize/page",p={require:r,loadChunk:()=>Promise.resolve()},m=new t.AppPageRouteModule({definition:{kind:i.x.APP_PAGE,page:"/ai-enroller/renewal/[groupId]/finalize/page",pathname:"/ai-enroller/renewal/[groupId]/finalize",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},94764:(e,s,r)=>{Promise.resolve().then(r.bind(r,76569))},76569:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>l});var t=r(10326),i=r(17577),a=r(35047),n=r(38492);r(19288),r(16039),r(3276);let l=()=>{let e=(0,a.useParams)(),s=(0,a.useRouter)(),[r,l]=(0,i.useState)(6),[c,d]=(0,i.useState)(!1),o="Green Valley Manufacturing",u=[{number:1,title:"Review Current Plans",subtitle:"View existing benefit plans",active:!1,completed:!0},{number:2,title:"Renewal Options",subtitle:"Choose renewal type",active:!1,completed:!0},{number:3,title:"Plan Configuration",subtitle:"Set dates and modifications",active:!1,completed:!0},{number:4,title:"Document Upload",subtitle:"Upload plan documents",active:!1,completed:!0},{number:5,title:"Validation",subtitle:"Review and validate setup",active:!1,completed:!0},{number:6,title:"Finalize",subtitle:"Complete renewal process",active:6===r},{number:7,title:"Export",subtitle:"Download and share data",active:!1}],p={plansRenewed:2,employeesAffected:89,effectiveDate:"1/1/2025",documentsUploaded:0},m=async()=>{d(!0),await new Promise(e=>setTimeout(e,3e3)),s.push(`/ai-enroller/renewal/${e.groupId}/export`)};return(0,t.jsxs)("div",{className:"plan-renewal-detail",children:[(0,t.jsxs)("div",{className:"detail-header",children:[(0,t.jsxs)("button",{className:"back-btn",onClick:()=>s.push("/ai-enroller/renewal"),children:[t.jsx(n.Tsu,{size:20}),"Back to Dashboard"]}),(0,t.jsxs)("div",{className:"header-info",children:[t.jsx("h1",{children:"Plan Renewal"}),t.jsx("h2",{children:o}),(0,t.jsxs)("div",{className:"step-indicator",children:["Step ",r," of 7"]})]}),t.jsx("div",{className:"completion-status",children:"86% Complete"})]}),t.jsx("div",{className:"renewal-steps",children:u.map((e,s)=>(0,t.jsxs)("div",{className:`renewal-step ${e.active?"active":""} ${e.completed?"completed":""}`,children:[t.jsx("div",{className:"step-number",children:e.completed?"✓":e.number}),(0,t.jsxs)("div",{className:"step-content",children:[t.jsx("div",{className:"step-title",children:e.title}),t.jsx("div",{className:"step-subtitle",children:e.subtitle})]}),s<u.length-1&&t.jsx("div",{className:"step-connector"})]},e.number))}),(0,t.jsxs)("div",{className:"finalize-section",children:[(0,t.jsxs)("div",{className:"finalize-header",children:[(0,t.jsxs)("div",{className:"finalize-title",children:[t.jsx(n.Moc,{size:20}),t.jsx("h3",{children:"Finalize Plan Renewal"})]}),(0,t.jsxs)("p",{children:["Complete the renewal process for ",o,". This will activate the new plans and archive the old ones."]})]}),(0,t.jsxs)("div",{className:"finalize-content",children:[(0,t.jsxs)("div",{className:"summary-cards",children:[(0,t.jsxs)("div",{className:"summary-card",children:[t.jsx("div",{className:"card-icon",children:t.jsx(n.vrJ,{size:24})}),(0,t.jsxs)("div",{className:"card-content",children:[t.jsx("div",{className:"card-number",children:p.plansRenewed}),t.jsx("div",{className:"card-label",children:"Plans Renewed"})]})]}),(0,t.jsxs)("div",{className:"summary-card",children:[t.jsx("div",{className:"card-icon",children:t.jsx(n.Otr,{size:24})}),(0,t.jsxs)("div",{className:"card-content",children:[t.jsx("div",{className:"card-number",children:p.employeesAffected}),t.jsx("div",{className:"card-label",children:"Employees Affected"})]})]}),(0,t.jsxs)("div",{className:"summary-card",children:[t.jsx("div",{className:"card-icon",children:t.jsx(n.Bge,{size:24})}),(0,t.jsxs)("div",{className:"card-content",children:[t.jsx("div",{className:"card-number",children:p.effectiveDate}),t.jsx("div",{className:"card-label",children:"Effective Date"})]})]}),(0,t.jsxs)("div",{className:"summary-card",children:[t.jsx("div",{className:"card-icon",children:t.jsx(n.tw,{size:24})}),(0,t.jsxs)("div",{className:"card-content",children:[t.jsx("div",{className:"card-number",children:p.documentsUploaded}),t.jsx("div",{className:"card-label",children:"Documents Uploaded"})]})]})]}),(0,t.jsxs)("div",{className:"finalize-steps",children:[t.jsx("h4",{children:"What happens when you finalize:"}),t.jsx("div",{className:"steps-list",children:[{number:1,title:"Archive Current Plans",description:'Existing plans will be marked as "Expired" and archived',completed:!1},{number:2,title:"Activate Renewed Plans",description:"New plans become active on the effective date",completed:!1},{number:3,title:"Notify HR Team",description:"HR administrators will receive renewal confirmation",completed:!1},{number:4,title:"Update Employee Portal",description:"Employees will see updated plan information",completed:!1}].map(e=>(0,t.jsxs)("div",{className:"finalize-step",children:[t.jsx("div",{className:"step-indicator",children:t.jsx("div",{className:"step-circle",children:e.number})}),(0,t.jsxs)("div",{className:"step-content",children:[t.jsx("h5",{children:e.title}),t.jsx("p",{children:e.description})]})]},e.number))})]}),(0,t.jsxs)("div",{className:"ready-notice",children:[t.jsx(n.PjL,{size:20}),(0,t.jsxs)("div",{children:[t.jsx("h4",{children:"Ready to Finalize"}),t.jsx("p",{children:'All validation checks have passed. Once you click "Finalize Renewal", the changes will be applied and cannot be undone without creating a new renewal.'})]})]})]}),(0,t.jsxs)("div",{className:"navigation-section",children:[(0,t.jsxs)("button",{className:"nav-btn secondary",onClick:()=>{s.back()},disabled:c,children:[t.jsx(n.Tsu,{size:16}),"Previous"]}),t.jsx("button",{className:`finalize-btn ${c?"processing":"enabled"}`,onClick:m,disabled:c,children:c?(0,t.jsxs)(t.Fragment,{children:[t.jsx("div",{className:"spinner"}),"Processing..."]}):(0,t.jsxs)(t.Fragment,{children:[t.jsx(n.Moc,{size:16}),"Finalize Renewal"]})})]})]})]})}},72187:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>t});let t=(0,r(68570).createProxy)(String.raw`C:\beno\project_dev\qharmony-frontend-v1\src\app\ai-enroller\renewal\[groupId]\finalize\page.tsx#default`)},3276:()=>{}};var s=require("../../../../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),t=s.X(0,[8948,1183,6621,8492,576,4437],()=>r(91284));module.exports=t})();