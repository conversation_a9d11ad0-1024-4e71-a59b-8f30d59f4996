(()=>{var e={};e.id=859,e.ids=[859],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},27790:e=>{"use strict";e.exports=require("assert")},84770:e=>{"use strict";e.exports=require("crypto")},17702:e=>{"use strict";e.exports=require("events")},92048:e=>{"use strict";e.exports=require("fs")},32615:e=>{"use strict";e.exports=require("http")},35240:e=>{"use strict";e.exports=require("https")},19801:e=>{"use strict";e.exports=require("os")},55315:e=>{"use strict";e.exports=require("path")},76162:e=>{"use strict";e.exports=require("stream")},74026:e=>{"use strict";e.exports=require("string_decoder")},74175:e=>{"use strict";e.exports=require("tty")},17360:e=>{"use strict";e.exports=require("url")},21764:e=>{"use strict";e.exports=require("util")},71568:e=>{"use strict";e.exports=require("zlib")},98061:e=>{"use strict";e.exports=require("node:assert")},92761:e=>{"use strict";e.exports=require("node:async_hooks")},72254:e=>{"use strict";e.exports=require("node:buffer")},40027:e=>{"use strict";e.exports=require("node:console")},6005:e=>{"use strict";e.exports=require("node:crypto")},65714:e=>{"use strict";e.exports=require("node:diagnostics_channel")},15673:e=>{"use strict";e.exports=require("node:events")},88849:e=>{"use strict";e.exports=require("node:http")},42725:e=>{"use strict";e.exports=require("node:http2")},87503:e=>{"use strict";e.exports=require("node:net")},38846:e=>{"use strict";e.exports=require("node:perf_hooks")},39630:e=>{"use strict";e.exports=require("node:querystring")},84492:e=>{"use strict";e.exports=require("node:stream")},31764:e=>{"use strict";e.exports=require("node:tls")},41041:e=>{"use strict";e.exports=require("node:url")},47261:e=>{"use strict";e.exports=require("node:util")},93746:e=>{"use strict";e.exports=require("node:util/types")},24086:e=>{"use strict";e.exports=require("node:worker_threads")},65628:e=>{"use strict";e.exports=require("node:zlib")},65353:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>a.a,__next_app__:()=>u,originalPathname:()=>p,pages:()=>d,routeModule:()=>m,tree:()=>c}),r(40650),r(6079),r(33709),r(35866);var t=r(23191),i=r(88716),l=r(37922),a=r.n(l),n=r(95231),o={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>n[e]);r.d(s,o);let c=["",{children:["ai-enroller",{children:["renewal",{children:["[groupId]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,40650)),"C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\renewal\\[groupId]\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,6079)),"C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,33709)),"C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,35866,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],d=["C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\renewal\\[groupId]\\page.tsx"],p="/ai-enroller/renewal/[groupId]/page",u={require:r,loadChunk:()=>Promise.resolve()},m=new t.AppPageRouteModule({definition:{kind:i.x.APP_PAGE,page:"/ai-enroller/renewal/[groupId]/page",pathname:"/ai-enroller/renewal/[groupId]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},43275:(e,s,r)=>{Promise.resolve().then(r.bind(r,75326))},75326:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>c});var t=r(10326),i=r(17577),l=r(35047),a=r(38492),n=r(44046);r(19288),r(16039);let o=[{id:"1",name:"Blue Cross Blue Shield PPO",carrier:"BCBS",type:"Medical",deductible:"$2,500",copay:"$30",monthlyPremiums:{employeeOnly:"$458.50",employeeSpouse:"$917.00",employeeChild:"$687.75",family:"$1,374.00"},enrolled:"142/145",expiration:"31/12/2024",selected:!1},{id:"2",name:"Delta Dental PPO",carrier:"Delta Dental",type:"Dental",deductible:"$75",copay:"$0",monthlyPremiums:{employeeOnly:"$42.50",employeeSpouse:"$85.00",employeeChild:"$63.75",family:"$127.50"},enrolled:"138/145",expiration:"31/12/2024",selected:!1},{id:"3",name:"VSP Vision Care",carrier:"VSP",type:"Vision",deductible:"$15",copay:"$15",monthlyPremiums:{employeeOnly:"$18.75",employeeSpouse:"$37.50",employeeChild:"$28.13",family:"$56.25"},enrolled:"124/145",expiration:"31/12/2024",selected:!1},{id:"4",name:"Basic Life Insurance",carrier:"MetLife",type:"Life",deductible:"N/A",copay:"N/A",monthlyPremiums:{employeeOnly:"$15.60",employeeSpouse:"$0.00",employeeChild:"$0.00",family:"$0.00"},enrolled:"145/145",expiration:"31/12/2024",selected:!1}],c=()=>{let e=(0,l.useParams)(),s=(0,l.useRouter)(),[r,c]=(0,i.useState)(o),[d,p]=(0,i.useState)(1),u="TechCorp Solutions",m=e=>{c(r.map(s=>s.id===e?{...s,selected:!s.selected}:s))},x=r.filter(e=>e.selected).length,h=e=>{switch(e){case"Medical":default:return t.jsx(a.wkn,{size:24});case"Dental":return t.jsx(n.sr,{size:24});case"Vision":return t.jsx(a.Vvo,{size:24});case"Life":return t.jsx(a.c3K,{size:24})}},j=[{number:1,title:"Review Current Plans",subtitle:"View existing benefit plans",active:1===d},{number:2,title:"Renewal Options",subtitle:"Choose renewal type",active:2===d},{number:3,title:"Plan Configuration",subtitle:"Set dates and modifications",active:3===d},{number:4,title:"Document Upload",subtitle:"Upload plan documents",active:4===d},{number:5,title:"Validation",subtitle:"Review and validate setup",active:5===d},{number:6,title:"Finalize",subtitle:"Complete renewal process",active:6===d},{number:7,title:"Export",subtitle:"Download and share data",active:7===d}];return(0,t.jsxs)("div",{className:"plan-renewal-detail",children:[(0,t.jsxs)("div",{className:"detail-header",children:[(0,t.jsxs)("button",{className:"back-btn",onClick:()=>s.back(),children:[t.jsx(a.Tsu,{size:20}),"Back to Dashboard"]}),(0,t.jsxs)("div",{className:"header-info",children:[t.jsx("h1",{children:"Plan Renewal"}),t.jsx("h2",{children:u}),(0,t.jsxs)("div",{className:"step-indicator",children:["Step ",d," of 7"]})]}),t.jsx("div",{className:"completion-status",children:"14% Complete"})]}),t.jsx("div",{className:"renewal-steps",children:j.map((e,s)=>(0,t.jsxs)("div",{className:`renewal-step ${e.active?"active":""}`,children:[t.jsx("div",{className:"step-number",children:e.number}),(0,t.jsxs)("div",{className:"step-content",children:[t.jsx("div",{className:"step-title",children:e.title}),t.jsx("div",{className:"step-subtitle",children:e.subtitle})]}),s<j.length-1&&t.jsx("div",{className:"step-connector"})]},e.number))}),(0,t.jsxs)("div",{className:"plan-portfolio",children:[(0,t.jsxs)("div",{className:"portfolio-header",children:[(0,t.jsxs)("div",{className:"portfolio-title",children:[t.jsx(a.wkn,{size:20}),t.jsx("h3",{children:"Current Plan Portfolio"})]}),(0,t.jsxs)("p",{children:["Review the existing benefit plans for ",u,". Select the plans you want to renew for the next plan year."]})]}),t.jsx("div",{className:"plans-grid",children:r.map(e=>(0,t.jsxs)("div",{className:`plan-card ${e.selected?"selected":""}`,children:[(0,t.jsxs)("div",{className:"plan-header",children:[t.jsx("div",{className:"plan-select",children:t.jsx("button",{className:`select-btn ${e.selected?"selected":""}`,onClick:()=>m(e.id),children:e.selected?t.jsx(a.PjL,{size:20}):t.jsx(a.C9x,{size:20})})}),(0,t.jsxs)("div",{className:"plan-info",children:[t.jsx("div",{className:"plan-icon",children:h(e.type)}),(0,t.jsxs)("div",{children:[t.jsx("h4",{children:e.name}),t.jsx("span",{className:"plan-carrier",children:e.carrier})]})]}),t.jsx("div",{className:"plan-type-badge",children:e.type})]}),(0,t.jsxs)("div",{className:"plan-details",children:[(0,t.jsxs)("div",{className:"detail-row",children:[t.jsx("span",{className:"label",children:"Deductible"}),t.jsx("span",{className:"value",children:e.deductible})]}),(0,t.jsxs)("div",{className:"detail-row",children:[t.jsx("span",{className:"label",children:"Copay"}),t.jsx("span",{className:"value",children:e.copay})]})]}),(0,t.jsxs)("div",{className:"premium-section",children:[t.jsx("h5",{children:"Monthly Premiums"}),(0,t.jsxs)("div",{className:"premium-grid",children:[(0,t.jsxs)("div",{className:"premium-item",children:[t.jsx("span",{className:"premium-label",children:"Employee Only:"}),t.jsx("span",{className:"premium-value",children:e.monthlyPremiums.employeeOnly})]}),(0,t.jsxs)("div",{className:"premium-item",children:[t.jsx("span",{className:"premium-label",children:"EE + Spouse:"}),t.jsx("span",{className:"premium-value",children:e.monthlyPremiums.employeeSpouse})]}),(0,t.jsxs)("div",{className:"premium-item",children:[t.jsx("span",{className:"premium-label",children:"EE + Child:"}),t.jsx("span",{className:"premium-value",children:e.monthlyPremiums.employeeChild})]}),(0,t.jsxs)("div",{className:"premium-item",children:[t.jsx("span",{className:"premium-label",children:"Family:"}),t.jsx("span",{className:"premium-value",children:e.monthlyPremiums.family})]})]})]}),(0,t.jsxs)("div",{className:"plan-footer",children:[(0,t.jsxs)("div",{className:"enrollment-info",children:[t.jsx(a.Otr,{size:16}),(0,t.jsxs)("span",{children:[e.enrolled," enrolled"]})]}),(0,t.jsxs)("div",{className:"expiration-info",children:[t.jsx(a.Bge,{size:16}),(0,t.jsxs)("span",{children:["Exp: ",e.expiration]})]})]})]},e.id))}),(0,t.jsxs)("div",{className:"quick-actions",children:[(0,t.jsxs)("div",{className:"actions-info",children:[t.jsx(a.PjL,{size:20}),(0,t.jsxs)("div",{children:[t.jsx("h4",{children:"Quick Actions"}),t.jsx("p",{children:"You can select all plans to renew with existing terms, or choose specific plans to modify. Individual plan modifications can be made in the next steps."})]})]}),(0,t.jsxs)("div",{className:"action-buttons",children:[t.jsx("button",{className:"action-btn secondary",onClick:()=>{let e=r.every(e=>e.selected);c(r.map(s=>({...s,selected:!e})))},children:"Select All Plans"}),t.jsx("button",{className:"action-btn secondary",onClick:()=>{c(r.map(e=>({...e,selected:!1})))},children:"Clear Selection"})]})]}),(0,t.jsxs)("div",{className:"continue-section",children:[(0,t.jsxs)("button",{className:`continue-btn ${x>0?"enabled":"disabled"}`,disabled:0===x,onClick:()=>{x>0&&s.push(`/ai-enroller/renewal/${e.groupId}/renewal-options`)},type:"button",children:["Continue with ",x," Plans"]}),0===x&&t.jsx("p",{style:{marginTop:"0.5rem",fontSize:"0.875rem",color:"#6b7280"},children:"Please select at least one plan to continue"})]})]})]})}},40650:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>t});let t=(0,r(68570).createProxy)(String.raw`C:\beno\project_dev\qharmony-frontend-v1\src\app\ai-enroller\renewal\[groupId]\page.tsx#default`)}};var s=require("../../../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),t=s.X(0,[8948,1183,6621,8492,4046,576,4437],()=>r(65353));module.exports=t})();