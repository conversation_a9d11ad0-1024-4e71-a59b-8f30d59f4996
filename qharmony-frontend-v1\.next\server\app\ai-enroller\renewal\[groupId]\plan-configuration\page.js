(()=>{var e={};e.id=925,e.ids=[925],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},27790:e=>{"use strict";e.exports=require("assert")},84770:e=>{"use strict";e.exports=require("crypto")},17702:e=>{"use strict";e.exports=require("events")},92048:e=>{"use strict";e.exports=require("fs")},32615:e=>{"use strict";e.exports=require("http")},35240:e=>{"use strict";e.exports=require("https")},19801:e=>{"use strict";e.exports=require("os")},55315:e=>{"use strict";e.exports=require("path")},76162:e=>{"use strict";e.exports=require("stream")},74026:e=>{"use strict";e.exports=require("string_decoder")},74175:e=>{"use strict";e.exports=require("tty")},17360:e=>{"use strict";e.exports=require("url")},21764:e=>{"use strict";e.exports=require("util")},71568:e=>{"use strict";e.exports=require("zlib")},98061:e=>{"use strict";e.exports=require("node:assert")},92761:e=>{"use strict";e.exports=require("node:async_hooks")},72254:e=>{"use strict";e.exports=require("node:buffer")},40027:e=>{"use strict";e.exports=require("node:console")},6005:e=>{"use strict";e.exports=require("node:crypto")},65714:e=>{"use strict";e.exports=require("node:diagnostics_channel")},15673:e=>{"use strict";e.exports=require("node:events")},88849:e=>{"use strict";e.exports=require("node:http")},42725:e=>{"use strict";e.exports=require("node:http2")},87503:e=>{"use strict";e.exports=require("node:net")},38846:e=>{"use strict";e.exports=require("node:perf_hooks")},39630:e=>{"use strict";e.exports=require("node:querystring")},84492:e=>{"use strict";e.exports=require("node:stream")},31764:e=>{"use strict";e.exports=require("node:tls")},41041:e=>{"use strict";e.exports=require("node:url")},47261:e=>{"use strict";e.exports=require("node:util")},93746:e=>{"use strict";e.exports=require("node:util/types")},24086:e=>{"use strict";e.exports=require("node:worker_threads")},65628:e=>{"use strict";e.exports=require("node:zlib")},77355:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>a.a,__next_app__:()=>p,originalPathname:()=>u,pages:()=>c,routeModule:()=>h,tree:()=>d}),s(46001),s(6079),s(33709),s(35866);var r=s(23191),i=s(88716),n=s(37922),a=s.n(n),o=s(95231),l={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);s.d(t,l);let d=["",{children:["ai-enroller",{children:["renewal",{children:["[groupId]",{children:["plan-configuration",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,46001)),"C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\renewal\\[groupId]\\plan-configuration\\page.tsx"]}]},{}]},{}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,6079)),"C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,33709)),"C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,35866,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],c=["C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\renewal\\[groupId]\\plan-configuration\\page.tsx"],u="/ai-enroller/renewal/[groupId]/plan-configuration/page",p={require:s,loadChunk:()=>Promise.resolve()},h=new r.AppPageRouteModule({definition:{kind:i.x.APP_PAGE,page:"/ai-enroller/renewal/[groupId]/plan-configuration/page",pathname:"/ai-enroller/renewal/[groupId]/plan-configuration",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},75189:(e,t,s)=>{Promise.resolve().then(s.bind(s,72467))},72467:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>o});var r=s(10326),i=s(17577),n=s(35047),a=s(38492);s(19288),s(16039),s(34014);let o=()=>{let e=(0,n.useParams)(),t=(0,n.useRouter)(),s=(0,n.useSearchParams)(),[o,l]=(0,i.useState)(3),[d,c]=(0,i.useState)("01/01/2025"),[u,p]=(0,i.useState)("31/12/2025"),h="TechCorp Solutions",x=s.get("option")||"renew-as-is",m=[{number:1,title:"Review Current Plans",subtitle:"View existing benefit plans",active:!1,completed:!0},{number:2,title:"Renewal Options",subtitle:"Choose renewal type",active:!1,completed:!0},{number:3,title:"Plan Configuration",subtitle:"Set dates and modifications",active:3===o},{number:4,title:"Document Upload",subtitle:"Upload plan documents",active:!1},{number:5,title:"Validation",subtitle:"Review and validate setup",active:!1},{number:6,title:"Finalize",subtitle:"Complete renewal process",active:!1},{number:7,title:"Export",subtitle:"Download and share data",active:!1}],v=e=>new Date(e).toLocaleDateString("en-US",{day:"2-digit",month:"2-digit",year:"numeric"});return(0,r.jsxs)("div",{className:"plan-renewal-detail",children:[(0,r.jsxs)("div",{className:"detail-header",children:[(0,r.jsxs)("button",{className:"back-btn",onClick:()=>t.push("/ai-enroller/renewal"),children:[r.jsx(a.Tsu,{size:20}),"Back to Dashboard"]}),(0,r.jsxs)("div",{className:"header-info",children:[r.jsx("h1",{children:"Plan Renewal"}),r.jsx("h2",{children:h}),(0,r.jsxs)("div",{className:"step-indicator",children:["Step ",o," of 7"]})]}),r.jsx("div",{className:"completion-status",children:"43% Complete"})]}),r.jsx("div",{className:"renewal-steps",children:m.map((e,t)=>(0,r.jsxs)("div",{className:`renewal-step ${e.active?"active":""} ${e.completed?"completed":""}`,children:[r.jsx("div",{className:"step-number",children:e.completed?"✓":e.number}),(0,r.jsxs)("div",{className:"step-content",children:[r.jsx("div",{className:"step-title",children:e.title}),r.jsx("div",{className:"step-subtitle",children:e.subtitle})]}),t<m.length-1&&r.jsx("div",{className:"step-connector"})]},e.number))}),(0,r.jsxs)("div",{className:"plan-configuration-section",children:[(0,r.jsxs)("div",{className:"config-header",children:[(0,r.jsxs)("div",{className:"config-title",children:[r.jsx(a.XlX,{size:20}),r.jsx("h3",{children:"Plan Configuration"})]}),(0,r.jsxs)("p",{children:["Set the new plan year dates and configure plan details for ",h,"."]})]}),(0,r.jsxs)("div",{className:"config-content",children:[(0,r.jsxs)("div",{className:"config-card",children:[(0,r.jsxs)("div",{className:"card-header",children:[r.jsx(a.Bge,{size:20}),r.jsx("h4",{children:"Plan Year Dates"})]}),(0,r.jsxs)("div",{className:"date-inputs",children:[(0,r.jsxs)("div",{className:"date-field",children:[r.jsx("label",{htmlFor:"start-date",children:"Plan Year Start Date"}),r.jsx("input",{id:"start-date",type:"date",value:d.split("/").reverse().join("-"),onChange:e=>c(e.target.value.split("-").reverse().join("/")),className:"date-input"}),r.jsx("span",{className:"date-help",children:"When the new plan year begins"})]}),(0,r.jsxs)("div",{className:"date-field",children:[r.jsx("label",{htmlFor:"end-date",children:"Plan Year End Date"}),r.jsx("input",{id:"end-date",type:"date",value:u.split("/").reverse().join("-"),onChange:e=>p(e.target.value.split("-").reverse().join("/")),className:"date-input"}),r.jsx("span",{className:"date-help",children:"When the plan year expires"})]})]})]}),(0,r.jsxs)("div",{className:"config-card summary-card",children:[(0,r.jsxs)("div",{className:"card-header",children:[r.jsx(a.if7,{size:20}),r.jsx("h4",{children:"Plan Year Summary"})]}),r.jsx("div",{className:"summary-content",children:(0,r.jsxs)("p",{children:["The new plan year will run from ",r.jsx("strong",{children:v(d)})," to ",r.jsx("strong",{children:v(u)}),", covering a total of ",(0,r.jsxs)("strong",{children:[(()=>{let e=new Date(d);return Math.ceil(Math.abs(new Date(u).getTime()-e.getTime())/864e5)})()," days"]}),"."]})})]}),"renew-as-is"!==x&&(0,r.jsxs)("div",{className:"config-card",children:[(0,r.jsxs)("div",{className:"card-header",children:[r.jsx(a.XlX,{size:20}),r.jsx("h4",{children:"Plan Modifications"})]}),r.jsx("div",{className:"modifications-content",children:(0,r.jsxs)("div",{className:"modification-notice",children:[r.jsx(a.if7,{size:20}),(0,r.jsxs)("p",{children:["Plan modifications will be configured in the next steps based on your selected renewal strategy:",r.jsx("strong",{children:"copy-modify"===x?"Copy & Modify Plans":"Major Plan Changes"})]})]})})]})]}),(0,r.jsxs)("div",{className:"navigation-section",children:[(0,r.jsxs)("button",{className:"nav-btn secondary",onClick:()=>{t.back()},children:[r.jsx(a.Tsu,{size:16}),"Previous"]}),r.jsx("button",{className:"nav-btn primary enabled",onClick:()=>{t.push(`/ai-enroller/renewal/${e.groupId}/document-upload`)},children:"Continue"})]})]})]})}},46001:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(68570).createProxy)(String.raw`C:\beno\project_dev\qharmony-frontend-v1\src\app\ai-enroller\renewal\[groupId]\plan-configuration\page.tsx#default`)},34014:()=>{}};var t=require("../../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[8948,1183,6621,8492,576,4437],()=>s(77355));module.exports=r})();