(()=>{var e={};e.id=7636,e.ids=[7636],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},27790:e=>{"use strict";e.exports=require("assert")},84770:e=>{"use strict";e.exports=require("crypto")},17702:e=>{"use strict";e.exports=require("events")},92048:e=>{"use strict";e.exports=require("fs")},32615:e=>{"use strict";e.exports=require("http")},35240:e=>{"use strict";e.exports=require("https")},19801:e=>{"use strict";e.exports=require("os")},55315:e=>{"use strict";e.exports=require("path")},76162:e=>{"use strict";e.exports=require("stream")},74026:e=>{"use strict";e.exports=require("string_decoder")},74175:e=>{"use strict";e.exports=require("tty")},17360:e=>{"use strict";e.exports=require("url")},21764:e=>{"use strict";e.exports=require("util")},71568:e=>{"use strict";e.exports=require("zlib")},98061:e=>{"use strict";e.exports=require("node:assert")},92761:e=>{"use strict";e.exports=require("node:async_hooks")},72254:e=>{"use strict";e.exports=require("node:buffer")},40027:e=>{"use strict";e.exports=require("node:console")},6005:e=>{"use strict";e.exports=require("node:crypto")},65714:e=>{"use strict";e.exports=require("node:diagnostics_channel")},15673:e=>{"use strict";e.exports=require("node:events")},88849:e=>{"use strict";e.exports=require("node:http")},42725:e=>{"use strict";e.exports=require("node:http2")},87503:e=>{"use strict";e.exports=require("node:net")},38846:e=>{"use strict";e.exports=require("node:perf_hooks")},39630:e=>{"use strict";e.exports=require("node:querystring")},84492:e=>{"use strict";e.exports=require("node:stream")},31764:e=>{"use strict";e.exports=require("node:tls")},41041:e=>{"use strict";e.exports=require("node:url")},47261:e=>{"use strict";e.exports=require("node:util")},93746:e=>{"use strict";e.exports=require("node:util/types")},24086:e=>{"use strict";e.exports=require("node:worker_threads")},65628:e=>{"use strict";e.exports=require("node:zlib")},3837:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>a.a,__next_app__:()=>p,originalPathname:()=>u,pages:()=>c,routeModule:()=>m,tree:()=>d}),t(72198),t(6079),t(33709),t(35866);var r=t(23191),i=t(88716),n=t(37922),a=t.n(n),o=t(95231),l={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);t.d(s,l);let d=["",{children:["ai-enroller",{children:["renewal",{children:["[groupId]",{children:["renewal-options",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,72198)),"C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\renewal\\[groupId]\\renewal-options\\page.tsx"]}]},{}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,6079)),"C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,33709)),"C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,35866,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],c=["C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\renewal\\[groupId]\\renewal-options\\page.tsx"],u="/ai-enroller/renewal/[groupId]/renewal-options/page",p={require:t,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:i.x.APP_PAGE,page:"/ai-enroller/renewal/[groupId]/renewal-options/page",pathname:"/ai-enroller/renewal/[groupId]/renewal-options",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},10285:(e,s,t)=>{Promise.resolve().then(t.bind(t,40072))},40072:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>o});var r=t(10326),i=t(17577),n=t(35047),a=t(38492);t(19288),t(16039),t(73700);let o=()=>{let e=(0,n.useParams)(),s=(0,n.useRouter)(),[t,o]=(0,i.useState)(""),[l,d]=(0,i.useState)(2),c="TechCorp Solutions",u=[{id:"renew-as-is",title:"Renew As-Is",description:"Keep all existing plan terms, benefits, and premium structures unchanged",icon:r.jsx(a.zHJ,{size:24}),recommended:!0,features:["Fastest processing time","Automatic rate rollover"],benefits:["No disruption to employees","Same plan codes and IDs"]},{id:"copy-modify",title:"Copy & Modify Plans",description:"Start with current plans but make adjustments to rates, benefits, or terms",icon:r.jsx(a.bnh,{size:24}),features:["Flexible rate adjustments","Modify plan names"],benefits:["Update deductibles/copays","Change carrier terms"]},{id:"major-changes",title:"Major Plan Changes",description:"Significant modifications including new carriers, plan designs, or benefit structures",icon:r.jsx(a.Moc,{size:24}),features:["New carrier integration","Benefit restructuring"],benefits:["Plan design overhaul","Custom plan creation"]}],p=[{number:1,title:"Review Current Plans",subtitle:"View existing benefit plans",active:!1},{number:2,title:"Renewal Options",subtitle:"Choose renewal type",active:2===l},{number:3,title:"Plan Configuration",subtitle:"Set dates and modifications",active:!1},{number:4,title:"Document Upload",subtitle:"Upload plan documents",active:!1},{number:5,title:"Validation",subtitle:"Review and validate setup",active:!1},{number:6,title:"Finalize",subtitle:"Complete renewal process",active:!1},{number:7,title:"Export",subtitle:"Download and share data",active:!1}],m=e=>{o(e)};return(0,r.jsxs)("div",{className:"plan-renewal-detail",children:[(0,r.jsxs)("div",{className:"detail-header",children:[(0,r.jsxs)("button",{className:"back-btn",onClick:()=>s.push("/ai-enroller/renewal"),children:[r.jsx(a.Tsu,{size:20}),"Back to Dashboard"]}),(0,r.jsxs)("div",{className:"header-info",children:[r.jsx("h1",{children:"Plan Renewal"}),r.jsx("h2",{children:c}),(0,r.jsxs)("div",{className:"step-indicator",children:["Step ",l," of 7"]})]}),r.jsx("div",{className:"completion-status",children:"29% Complete"})]}),r.jsx("div",{className:"renewal-steps",children:p.map((e,s)=>(0,r.jsxs)("div",{className:`renewal-step ${e.active?"active":""}`,children:[r.jsx("div",{className:"step-number",children:e.number}),(0,r.jsxs)("div",{className:"step-content",children:[r.jsx("div",{className:"step-title",children:e.title}),r.jsx("div",{className:"step-subtitle",children:e.subtitle})]}),s<p.length-1&&r.jsx("div",{className:"step-connector"})]},e.number))}),(0,r.jsxs)("div",{className:"renewal-options-section",children:[(0,r.jsxs)("div",{className:"options-header",children:[(0,r.jsxs)("div",{className:"options-title",children:[r.jsx(a.zHJ,{size:20}),r.jsx("h3",{children:"Plan Renewal Strategy"})]}),(0,r.jsxs)("p",{children:["Choose how you want to approach the renewal process for ",c,". This will determine the workflow and options available in subsequent steps."]})]}),r.jsx("div",{className:"options-grid",children:u.map(e=>(0,r.jsxs)("div",{className:`option-card ${t===e.id?"selected":""}`,onClick:()=>m(e.id),children:[e.recommended&&r.jsx("div",{className:"recommended-badge",children:"Recommended"}),(0,r.jsxs)("div",{className:"option-header",children:[r.jsx("div",{className:"option-select",children:r.jsx("div",{className:`radio-btn ${t===e.id?"selected":""}`,children:t===e.id&&r.jsx(a.PjL,{size:16})})}),r.jsx("div",{className:"option-icon",children:e.icon}),(0,r.jsxs)("div",{className:"option-info",children:[r.jsx("h4",{children:e.title}),r.jsx("p",{children:e.description})]})]}),(0,r.jsxs)("div",{className:"option-features",children:[(0,r.jsxs)("div",{className:"features-section",children:[r.jsx("h5",{children:"Key Features:"}),r.jsx("ul",{children:e.features.map((e,s)=>r.jsx("li",{children:e},s))})]}),r.jsx("div",{className:"benefits-section",children:r.jsx("ul",{children:e.benefits.map((e,s)=>r.jsx("li",{children:e},s))})})]})]},e.id))}),(0,r.jsxs)("div",{className:"navigation-section",children:[(0,r.jsxs)("button",{className:"nav-btn secondary",onClick:()=>{s.back()},children:[r.jsx(a.Tsu,{size:16}),"Previous"]}),r.jsx("button",{className:`nav-btn primary ${t?"enabled":"disabled"}`,onClick:()=>{t&&s.push(`/ai-enroller/renewal/${e.groupId}/plan-configuration?option=${t}`)},disabled:!t,children:"Continue"})]})]})]})}},72198:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});let r=(0,t(68570).createProxy)(String.raw`C:\beno\project_dev\qharmony-frontend-v1\src\app\ai-enroller\renewal\[groupId]\renewal-options\page.tsx#default`)},73700:()=>{}};var s=require("../../../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[8948,1183,6621,8492,576,4437],()=>t(3837));module.exports=r})();