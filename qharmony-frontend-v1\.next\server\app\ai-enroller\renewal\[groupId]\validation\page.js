(()=>{var e={};e.id=9988,e.ids=[9988],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},27790:e=>{"use strict";e.exports=require("assert")},84770:e=>{"use strict";e.exports=require("crypto")},17702:e=>{"use strict";e.exports=require("events")},92048:e=>{"use strict";e.exports=require("fs")},32615:e=>{"use strict";e.exports=require("http")},35240:e=>{"use strict";e.exports=require("https")},19801:e=>{"use strict";e.exports=require("os")},55315:e=>{"use strict";e.exports=require("path")},76162:e=>{"use strict";e.exports=require("stream")},74026:e=>{"use strict";e.exports=require("string_decoder")},74175:e=>{"use strict";e.exports=require("tty")},17360:e=>{"use strict";e.exports=require("url")},21764:e=>{"use strict";e.exports=require("util")},71568:e=>{"use strict";e.exports=require("zlib")},98061:e=>{"use strict";e.exports=require("node:assert")},92761:e=>{"use strict";e.exports=require("node:async_hooks")},72254:e=>{"use strict";e.exports=require("node:buffer")},40027:e=>{"use strict";e.exports=require("node:console")},6005:e=>{"use strict";e.exports=require("node:crypto")},65714:e=>{"use strict";e.exports=require("node:diagnostics_channel")},15673:e=>{"use strict";e.exports=require("node:events")},88849:e=>{"use strict";e.exports=require("node:http")},42725:e=>{"use strict";e.exports=require("node:http2")},87503:e=>{"use strict";e.exports=require("node:net")},38846:e=>{"use strict";e.exports=require("node:perf_hooks")},39630:e=>{"use strict";e.exports=require("node:querystring")},84492:e=>{"use strict";e.exports=require("node:stream")},31764:e=>{"use strict";e.exports=require("node:tls")},41041:e=>{"use strict";e.exports=require("node:url")},47261:e=>{"use strict";e.exports=require("node:util")},93746:e=>{"use strict";e.exports=require("node:util/types")},24086:e=>{"use strict";e.exports=require("node:worker_threads")},65628:e=>{"use strict";e.exports=require("node:zlib")},78455:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>n.a,__next_app__:()=>p,originalPathname:()=>u,pages:()=>c,routeModule:()=>m,tree:()=>d}),t(35987),t(6079),t(33709),t(35866);var i=t(23191),r=t(88716),a=t(37922),n=t.n(a),l=t(95231),o={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);t.d(s,o);let d=["",{children:["ai-enroller",{children:["renewal",{children:["[groupId]",{children:["validation",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,35987)),"C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\renewal\\[groupId]\\validation\\page.tsx"]}]},{}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,6079)),"C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,33709)),"C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,35866,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],c=["C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\renewal\\[groupId]\\validation\\page.tsx"],u="/ai-enroller/renewal/[groupId]/validation/page",p={require:t,loadChunk:()=>Promise.resolve()},m=new i.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/ai-enroller/renewal/[groupId]/validation/page",pathname:"/ai-enroller/renewal/[groupId]/validation",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},20308:(e,s,t)=>{Promise.resolve().then(t.bind(t,45294))},45294:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>l});var i=t(10326),r=t(17577),a=t(35047),n=t(38492);t(19288),t(16039),t(84820);let l=()=>{let e=(0,a.useParams)(),s=(0,a.useRouter)(),[t,l]=(0,r.useState)(5),o="Green Valley Manufacturing",d=[{id:"eligibility",title:"Eligibility",description:"All employee tiers and age bands configured correctly",details:"142 employees eligible for medical, 138 for dental",status:"passed"},{id:"premiums",title:"Premiums",description:"Medical premium increase detected",details:"Employee-only rate increased by 8.2% from previous year",status:"warning"},{id:"compliance",title:"Compliance",description:"All compliance requirements met",details:"SBC documents uploaded, ACA requirements satisfied",status:"passed"},{id:"data-integrity",title:"Data Integrity",description:"No missing data detected",details:"All required fields populated for each plan tier",status:"passed"},{id:"carrier-sync",title:"Carrier Sync",description:"Carrier rate verification pending",details:"Recommend confirming rates with BCBS before finalizing",status:"warning"}],c=[{number:1,title:"Review Current Plans",subtitle:"View existing benefit plans",active:!1,completed:!0},{number:2,title:"Renewal Options",subtitle:"Choose renewal type",active:!1,completed:!0},{number:3,title:"Plan Configuration",subtitle:"Set dates and modifications",active:!1,completed:!0},{number:4,title:"Document Upload",subtitle:"Upload plan documents",active:!1,completed:!0},{number:5,title:"Validation",subtitle:"Review and validate setup",active:5===t},{number:6,title:"Finalize",subtitle:"Complete renewal process",active:!1},{number:7,title:"Export",subtitle:"Download and share data",active:!1}],u=d.filter(e=>"passed"===e.status).length,p=d.filter(e=>"warning"===e.status).length,m=d.filter(e=>"failed"===e.status).length,h=e=>{switch(e){case"passed":return i.jsx(n.PjL,{size:20,className:"status-icon passed"});case"warning":return i.jsx(n.baL,{size:20,className:"status-icon warning"});case"failed":return i.jsx(n.baL,{size:20,className:"status-icon failed"});default:return null}};return(0,i.jsxs)("div",{className:"plan-renewal-detail",children:[(0,i.jsxs)("div",{className:"detail-header",children:[(0,i.jsxs)("button",{className:"back-btn",onClick:()=>s.push("/ai-enroller/renewal"),children:[i.jsx(n.Tsu,{size:20}),"Back to Dashboard"]}),(0,i.jsxs)("div",{className:"header-info",children:[i.jsx("h1",{children:"Plan Renewal"}),i.jsx("h2",{children:o}),(0,i.jsxs)("div",{className:"step-indicator",children:["Step ",t," of 7"]})]}),i.jsx("div",{className:"completion-status",children:"71% Complete"})]}),i.jsx("div",{className:"renewal-steps",children:c.map((e,s)=>(0,i.jsxs)("div",{className:`renewal-step ${e.active?"active":""} ${e.completed?"completed":""}`,children:[i.jsx("div",{className:"step-number",children:e.completed?"✓":e.number}),(0,i.jsxs)("div",{className:"step-content",children:[i.jsx("div",{className:"step-title",children:e.title}),i.jsx("div",{className:"step-subtitle",children:e.subtitle})]}),s<c.length-1&&i.jsx("div",{className:"step-connector"})]},e.number))}),(0,i.jsxs)("div",{className:"validation-section",children:[(0,i.jsxs)("div",{className:"validation-header",children:[(0,i.jsxs)("div",{className:"validation-title",children:[i.jsx(n.c3K,{size:20}),i.jsx("h3",{children:"Plan Setup Validation"})]}),(0,i.jsxs)("p",{children:["Reviewing plan configuration for ",o," to ensure everything is set up correctly before finalizing."]})]}),(0,i.jsxs)("div",{className:"validation-content",children:[i.jsx("div",{className:"validation-checks",children:d.map(e=>(0,i.jsxs)("div",{className:`validation-check ${e.status}`,children:[(0,i.jsxs)("div",{className:"check-header",children:[h(e.status),(0,i.jsxs)("div",{className:"check-info",children:[i.jsx("h4",{children:e.title}),i.jsx("p",{children:e.description})]}),i.jsx("div",{className:"check-status",children:e.status})]}),i.jsx("div",{className:"check-details",children:e.details})]},e.id))}),(0,i.jsxs)("div",{className:"validation-summary",children:[i.jsx("h4",{children:"Validation Summary"}),(0,i.jsxs)("div",{className:"summary-stats",children:[(0,i.jsxs)("div",{className:"stat-item passed",children:[i.jsx("div",{className:"stat-number",children:u}),i.jsx("div",{className:"stat-label",children:"Checks Passed"})]}),(0,i.jsxs)("div",{className:"stat-item warning",children:[i.jsx("div",{className:"stat-number",children:p}),i.jsx("div",{className:"stat-label",children:"Warnings"})]}),(0,i.jsxs)("div",{className:"stat-item failed",children:[i.jsx("div",{className:"stat-number",children:m}),i.jsx("div",{className:"stat-label",children:"Failed Checks"})]})]})]}),p>0&&(0,i.jsxs)("div",{className:"warnings-notice",children:[i.jsx(n.baL,{size:20}),(0,i.jsxs)("div",{children:[i.jsx("h4",{children:"Warnings Detected"}),i.jsx("p",{children:"Your plan setup has some warnings but no critical issues. You can proceed with the renewal, but we recommend reviewing the flagged items."})]})]})]}),(0,i.jsxs)("div",{className:"navigation-section",children:[(0,i.jsxs)("button",{className:"nav-btn secondary",onClick:()=>{s.back()},children:[i.jsx(n.Tsu,{size:16}),"Previous"]}),(0,i.jsxs)("div",{className:"nav-actions",children:[i.jsx("button",{className:"nav-btn secondary",onClick:()=>{console.log("Re-running validation...")},children:"Re-run Validation"}),i.jsx("button",{className:"nav-btn primary enabled",onClick:()=>{s.push(`/ai-enroller/renewal/${e.groupId}/finalize`)},children:"Continue"})]})]})]})]})}},35987:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>i});let i=(0,t(68570).createProxy)(String.raw`C:\beno\project_dev\qharmony-frontend-v1\src\app\ai-enroller\renewal\[groupId]\validation\page.tsx#default`)},84820:()=>{}};var s=require("../../../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),i=s.X(0,[8948,1183,6621,8492,576,4437],()=>t(78455));module.exports=i})();