(()=>{var e={};e.id=3134,e.ids=[3134],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},27790:e=>{"use strict";e.exports=require("assert")},84770:e=>{"use strict";e.exports=require("crypto")},17702:e=>{"use strict";e.exports=require("events")},92048:e=>{"use strict";e.exports=require("fs")},32615:e=>{"use strict";e.exports=require("http")},35240:e=>{"use strict";e.exports=require("https")},19801:e=>{"use strict";e.exports=require("os")},55315:e=>{"use strict";e.exports=require("path")},76162:e=>{"use strict";e.exports=require("stream")},74026:e=>{"use strict";e.exports=require("string_decoder")},74175:e=>{"use strict";e.exports=require("tty")},17360:e=>{"use strict";e.exports=require("url")},21764:e=>{"use strict";e.exports=require("util")},71568:e=>{"use strict";e.exports=require("zlib")},98061:e=>{"use strict";e.exports=require("node:assert")},92761:e=>{"use strict";e.exports=require("node:async_hooks")},72254:e=>{"use strict";e.exports=require("node:buffer")},40027:e=>{"use strict";e.exports=require("node:console")},6005:e=>{"use strict";e.exports=require("node:crypto")},65714:e=>{"use strict";e.exports=require("node:diagnostics_channel")},15673:e=>{"use strict";e.exports=require("node:events")},88849:e=>{"use strict";e.exports=require("node:http")},42725:e=>{"use strict";e.exports=require("node:http2")},87503:e=>{"use strict";e.exports=require("node:net")},38846:e=>{"use strict";e.exports=require("node:perf_hooks")},39630:e=>{"use strict";e.exports=require("node:querystring")},84492:e=>{"use strict";e.exports=require("node:stream")},31764:e=>{"use strict";e.exports=require("node:tls")},41041:e=>{"use strict";e.exports=require("node:url")},47261:e=>{"use strict";e.exports=require("node:util")},93746:e=>{"use strict";e.exports=require("node:util/types")},24086:e=>{"use strict";e.exports=require("node:worker_threads")},65628:e=>{"use strict";e.exports=require("node:zlib")},79268:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>i.a,__next_app__:()=>p,originalPathname:()=>u,pages:()=>c,routeModule:()=>m,tree:()=>d}),t(13658),t(6079),t(33709),t(35866);var o=t(23191),s=t(88716),n=t(37922),i=t.n(n),a=t(95231),l={};for(let e in a)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>a[e]);t.d(r,l);let d=["",{children:["ai-enroller",{children:["renewal",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,13658)),"C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\renewal\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,6079)),"C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,33709)),"C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,35866,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],c=["C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\ai-enroller\\renewal\\page.tsx"],u="/ai-enroller/renewal/page",p={require:t,loadChunk:()=>Promise.resolve()},m=new o.AppPageRouteModule({definition:{kind:s.x.APP_PAGE,page:"/ai-enroller/renewal/page",pathname:"/ai-enroller/renewal",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},40853:(e,r,t)=>{Promise.resolve().then(t.bind(t,78021))},30934:(e,r,t)=>{Promise.resolve().then(t.bind(t,53051))},6283:(e,r,t)=>{"use strict";t.d(r,{Z:()=>d});var o=t(96830),s=t(5028),n=t(5283),i=t(14750);let a=(0,t(71685).Z)("MuiBox",["root"]),l=(0,n.Z)(),d=(0,o.default)({themeId:i.Z,defaultTheme:l,defaultClassName:a.root,generateClassName:s.Z.generate})},98139:(e,r,t)=>{"use strict";t.d(r,{Z:()=>P});var o=t(17577),s=t(41135),n=t(88634),i=t(8106),a=t(91703),l=t(13643),d=t(2791),c=t(54641),u=t(40955),p=t(71685),m=t(97898);function g(e){return(0,m.ZP)("MuiCircularProgress",e)}(0,p.Z)("MuiCircularProgress",["root","determinate","indeterminate","colorPrimary","colorSecondary","svg","circle","circleDeterminate","circleIndeterminate","circleDisableShrink"]);var y=t(10326);let f=(0,i.F4)`
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
`,h=(0,i.F4)`
  0% {
    stroke-dasharray: 1px, 200px;
    stroke-dashoffset: 0;
  }

  50% {
    stroke-dasharray: 100px, 200px;
    stroke-dashoffset: -15px;
  }

  100% {
    stroke-dasharray: 1px, 200px;
    stroke-dashoffset: -126px;
  }
`,x="string"!=typeof f?(0,i.iv)`
        animation: ${f} 1.4s linear infinite;
      `:null,b="string"!=typeof h?(0,i.iv)`
        animation: ${h} 1.4s ease-in-out infinite;
      `:null,v=e=>{let{classes:r,variant:t,color:o,disableShrink:s}=e,i={root:["root",t,`color${(0,c.Z)(o)}`],svg:["svg"],circle:["circle",`circle${(0,c.Z)(t)}`,s&&"circleDisableShrink"]};return(0,n.Z)(i,g,r)},w=(0,a.default)("span",{name:"MuiCircularProgress",slot:"Root",overridesResolver:(e,r)=>{let{ownerState:t}=e;return[r.root,r[t.variant],r[`color${(0,c.Z)(t.color)}`]]}})((0,l.Z)(({theme:e})=>({display:"inline-block",variants:[{props:{variant:"determinate"},style:{transition:e.transitions.create("transform")}},{props:{variant:"indeterminate"},style:x||{animation:`${f} 1.4s linear infinite`}},...Object.entries(e.palette).filter((0,u.Z)()).map(([r])=>({props:{color:r},style:{color:(e.vars||e).palette[r].main}}))]}))),k=(0,a.default)("svg",{name:"MuiCircularProgress",slot:"Svg",overridesResolver:(e,r)=>r.svg})({display:"block"}),S=(0,a.default)("circle",{name:"MuiCircularProgress",slot:"Circle",overridesResolver:(e,r)=>{let{ownerState:t}=e;return[r.circle,r[`circle${(0,c.Z)(t.variant)}`],t.disableShrink&&r.circleDisableShrink]}})((0,l.Z)(({theme:e})=>({stroke:"currentColor",variants:[{props:{variant:"determinate"},style:{transition:e.transitions.create("stroke-dashoffset")}},{props:{variant:"indeterminate"},style:{strokeDasharray:"80px, 200px",strokeDashoffset:0}},{props:({ownerState:e})=>"indeterminate"===e.variant&&!e.disableShrink,style:b||{animation:`${h} 1.4s ease-in-out infinite`}}]}))),P=o.forwardRef(function(e,r){let t=(0,d.i)({props:e,name:"MuiCircularProgress"}),{className:o,color:n="primary",disableShrink:i=!1,size:a=40,style:l,thickness:c=3.6,value:u=0,variant:p="indeterminate",...m}=t,g={...t,color:n,disableShrink:i,size:a,thickness:c,value:u,variant:p},f=v(g),h={},x={},b={};if("determinate"===p){let e=2*Math.PI*((44-c)/2);h.strokeDasharray=e.toFixed(3),b["aria-valuenow"]=Math.round(u),h.strokeDashoffset=`${((100-u)/100*e).toFixed(3)}px`,x.transform="rotate(-90deg)"}return(0,y.jsx)(w,{className:(0,s.Z)(f.root,o),style:{width:a,height:a,...x,...l},ownerState:g,ref:r,role:"progressbar",...b,...m,children:(0,y.jsx)(k,{className:f.svg,ownerState:g,viewBox:"22 22 44 44",children:(0,y.jsx)(S,{className:f.circle,style:h,ownerState:g,cx:44,cy:44,r:(44-c)/2,fill:"none",strokeWidth:c})})})})},2791:(e,r,t)=>{"use strict";t.d(r,{i:()=>s}),t(17577);var o=t(51387);function s(e){return(0,o.i)(e)}t(10326)},54641:(e,r,t)=>{"use strict";t.d(r,{Z:()=>o});let o=t(96005).Z},40955:(e,r,t)=>{"use strict";function o(e=[]){return([,r])=>r&&function(e,r=[]){if("string"!=typeof e.main)return!1;for(let t of r)if(!e.hasOwnProperty(t)||"string"!=typeof e[t])return!1;return!0}(r,e)}t.d(r,{Z:()=>o})},13643:(e,r,t)=>{"use strict";t.d(r,{Z:()=>n});var o=t(15966);let s={theme:void 0},n=function(e){let r,t;return function(n){let i=r;return(void 0===i||n.theme!==t)&&(s.theme=n.theme,r=i=(0,o.Z)(e(s)),t=n.theme),i}}},78021:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>n});var o=t(10326);t(17577),t(23824),t(54658);var s=t(43058);function n({children:e}){return o.jsx(s.Z,{children:o.jsx("div",{className:"min-h-screen bg-white",style:{backgroundColor:"white",minHeight:"100vh"},children:e})})}},53051:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>i});var o=t(10326);t(17577);var s=t(35047),n=t(38492);let i=()=>{let e=(0,s.useRouter)(),r=()=>{e.push("/ai-enroller")};return o.jsx("div",{className:"renewal-wrapper",children:(0,o.jsxs)("div",{style:{maxWidth:"800px",margin:"0 auto",padding:"2rem",fontFamily:"SF Pro Text, -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, sans-serif"},children:[o.jsx("div",{style:{marginBottom:"2rem"},children:(0,o.jsxs)("button",{onClick:r,style:{display:"flex",alignItems:"center",gap:"0.5rem",background:"white",border:"1px solid #e5e7eb",padding:"0.5rem 1rem",borderRadius:"0.5rem",color:"#374151",cursor:"pointer",fontSize:"0.875rem",fontWeight:"500"},children:[o.jsx(n.Tsu,{size:16}),"Back to Main"]})}),(0,o.jsxs)("div",{style:{display:"flex",alignItems:"flex-start",gap:"1rem",background:"white",padding:"1.5rem",borderRadius:"1rem",boxShadow:"0 2px 8px rgba(0, 0, 0, 0.04)",border:"1px solid #e2e8f0",marginBottom:"2rem"},children:[o.jsx("div",{style:{width:"2.5rem",height:"2.5rem",background:"linear-gradient(135deg, #8b5cf6 0%, #a855f7 100%)",borderRadius:"50%",display:"flex",alignItems:"center",justifyContent:"center",fontSize:"1.25rem",flexShrink:0},children:"\uD83E\uDD16"}),o.jsx("div",{style:{flex:1,fontSize:"0.875rem",color:"#374151",lineHeight:1.6},children:"Welcome to the Group Plan Management section! This feature is coming soon. You'll be able to assign plans to employer groups and manage renewals here."})]}),(0,o.jsxs)("div",{style:{background:"white",border:"1px solid #e2e8f0",borderRadius:"1rem",padding:"3rem 2rem",textAlign:"center",boxShadow:"0 2px 8px rgba(0, 0, 0, 0.04)"},children:[o.jsx("div",{style:{fontSize:"3rem",marginBottom:"1rem"},children:"\uD83D\uDEA7"}),o.jsx("h2",{style:{fontSize:"1.5rem",fontWeight:"600",color:"#1e293b",marginBottom:"1rem"},children:"Group Plan Management"}),o.jsx("p",{style:{fontSize:"1rem",color:"#64748b",marginBottom:"2rem",maxWidth:"400px",margin:"0 auto 2rem auto"},children:"This section will allow you to assign insurance plans to employer groups and manage plan renewals."}),(0,o.jsxs)("div",{style:{background:"#f8fafc",border:"1px solid #e2e8f0",borderRadius:"0.75rem",padding:"1.5rem",marginBottom:"2rem"},children:[o.jsx("h3",{style:{fontSize:"1rem",fontWeight:"600",color:"#1e293b",marginBottom:"1rem"},children:"Coming Features:"}),(0,o.jsxs)("ul",{style:{textAlign:"left",color:"#374151",fontSize:"0.875rem",lineHeight:1.6,margin:0,paddingLeft:"1.5rem"},children:[o.jsx("li",{children:"Assign plans to employer groups"}),o.jsx("li",{children:"Manage plan renewals and updates"}),o.jsx("li",{children:"Track group enrollment status"}),o.jsx("li",{children:"Generate renewal reports"}),o.jsx("li",{children:"Automated renewal notifications"})]})]}),o.jsx("button",{onClick:r,style:{background:"#8b5cf6",color:"white",border:"none",padding:"0.75rem 1.5rem",borderRadius:"0.5rem",fontSize:"0.875rem",fontWeight:"600",cursor:"pointer"},children:"Back to Main Menu"})]})]})})}},43058:(e,r,t)=>{"use strict";t.d(r,{Z:()=>u});var o=t(10326),s=t(17577),n=t(22758),i=t(35047),a=t(31870);t(32049),t(94638);var l=t(98139),d=t(6283);let c=()=>/Mobi|Android/i.test(navigator.userAgent),u=({children:e})=>{let{user:r,loading:t}=(0,n.a)(),u=(0,i.useRouter)(),p=(0,i.usePathname)(),m=(0,a.T)(),[g,y]=(0,s.useState)(!1),f=(0,a.C)(e=>e.user.userProfile);return((0,s.useEffect)(()=>{},[m,f.name]),(0,s.useEffect)(()=>{console.log("ProtectedRoute useEffect triggered"),console.log("Current user: ",r),console.log("Loading state: ",t),console.log("Current user details: ",f),t||r||(console.log("User not authenticated, redirecting to home"),y(!1),u.push("/")),!t&&f.companyId&&""===f.companyId&&(console.log("Waiting to retrieve company details"),y(!1)),!t&&f.companyId&&""!==f.companyId&&(console.log("User found, rendering children"),y(!0)),c()&&!p.startsWith("/mobile")&&(console.log(`Redirecting to mobile version of ${p}`),u.push(`/mobile${p}`))},[r,t,f,u,p]),g)?r?o.jsx(o.Fragment,{children:e}):null:o.jsx(d.Z,{sx:{display:"flex",justifyContent:"center",alignItems:"center",height:"100vh",bgcolor:"#f6f8fc"},children:o.jsx(l.Z,{})})}},94638:(e,r,t)=>{"use strict";t.d(r,{G9:()=>h,JZ:()=>x,M_:()=>g,N:()=>d,Nq:()=>m,TQ:()=>u,Ur:()=>a,aE:()=>c,aK:()=>w,dA:()=>l,gt:()=>b,mb:()=>f,qB:()=>v,yu:()=>p,zX:()=>y});var o=t(53148),s=t(39352),n=t(25748),i=t(32049);function a(e){switch(e){case"Our Culture & Workplace":return"Work Policies";case"Protecting Your Income":return"Income Security";default:return e}}function l(e){switch(e){case"On-site Amenities":return"Company Handbook";case"Protecting Your Income":return"Income Security";case"Got married/divorced":return"Marriage or Divorce";case"Had a baby or adopted a baby":return"New Baby or Adoption";case"Lost your insurance":return"Loss of Insurance";default:return e}}async function d(e,r){let t=await (0,o.A_)("/benefits/benefit-types",{companyId:r});return console.log("COMPANY BENEFIT TYPES RESPONSE: ",t.benefitTypes),e((0,s.x7)(t.benefitTypes)),t.benefitTypes}async function c(e,r){let t=await (0,o.A_)("/benefits/all-benefits",{companyId:r});console.log("COMPANY BENEFIT TYPES WITH BENEFITS RESPONSE: ",t),e((0,n.US)(t.benefitsPerType))}async function u(e){let r=await (0,o.A_)("/admin/all-employees");return console.log("COMPANY TEAM MEMBERS RESPONSE: ",r),e((0,s.Vv)(r.employees)),r.employees}async function p(e,r){return console.log("ADDING USERS: ",r),await (0,o.j0)("/admin/add/employees",{employeeList:r})}async function m(e,r,t){try{console.log("\uD83D\uDD0D Debug: User being updated:",r);let e={employeeId:r,updatedDetails:{name:t.name,email:t.email,details:{phoneNumber:t.phoneNumber||"",department:t.department||"",title:t.title||"",role:t.title||""}}};return t.dateOfBirth&&(e.updatedDetails.details.dateOfBirth=t.dateOfBirth),t.hireDate&&(e.updatedDetails.details.hireDate=t.hireDate),t.annualSalary&&(e.updatedDetails.details.annualSalary=t.annualSalary),t.employeeClassType&&(e.updatedDetails.details.employeeClassType=t.employeeClassType),t.workSchedule&&(e.updatedDetails.details.workSchedule=t.workSchedule),t.ssn&&(e.updatedDetails.details.ssn=t.ssn),t.employeeId&&(e.updatedDetails.details.employeeId=t.employeeId),t.workLocation&&(e.updatedDetails.details.workLocation=t.workLocation),t.address&&(e.updatedDetails.details.address=t.address),t.mailingAddress&&(e.updatedDetails.details.mailingAddress=t.mailingAddress),t.emergencyContact&&(e.updatedDetails.details.emergencyContact=t.emergencyContact),e.updatedDetails.details.dependents=t.dependents||[],console.log("Middleware - dependents being sent:",e.updatedDetails.details.dependents),console.log("Sending PUT request with cleaned data:",e),await (0,o.GH)("/admin/update/employee",e)}catch(e){throw console.error("Error in updateUser middleware:",e),e}}async function g(e,r){let t=await (0,o.A_)("/employee",{"user-id":r});return e((0,i.$l)({name:t.currentUser.name,email:t.currentUser.email,companyId:t.currentUser.companyId,role:t.currentUser.role,isAdmin:t.currentUser.isAdmin,isBroker:t.currentUser.isBroker,details:t.currentUser.details})),t}async function y(e,r,t){let s=await (0,o.j0)("/admin/onboard",{company:{name:r.name,adminEmail:r.adminEmail,adminRole:r.adminRole,companySize:r.companySize,industry:r.industry,location:r.location,website:r.website,howHeard:r.howHeard,brokerId:r.brokerId,brokerageId:r.brokerageId,isBrokerage:r.isBrokerage,isActivated:r.isActivated,referralSource:r.referralSource,details:{logo:""}},user:{email:t.email,name:t.name,role:t.role,isAdmin:t.isAdmin,isBroker:t.isBroker,isActivated:t.isActivated}}),n=s.data.userId,i=s.data.companyId;return localStorage.setItem("userid1",n),localStorage.setItem("companyId1",i),s}async function f(e,r){return console.log("SENDING LOGIN LINK TO EMPLOYEE: ",e,r),await (0,o.j0)("/admin/send-user-login-link",{userId:e,companyId:r})}async function h(e,r,t,s){let n=await (0,o.j0)("/admin/add/employer",{brokerId:e,companyName:r,companyAdminEmail:t,companyAdminName:s});return console.log("BROKER ADDS COMPANY RESPONSE: ",n),n}async function x(e,r){return 200===(await (0,o.j0)("/employee/offboard/",{userId:e,companyId:r})).status}async function b(e,r){return await (0,o.j0)("/employee/enable/",{userId:e,companyId:r})}async function v(e,r){try{let r=await (0,o.A_)("/admin/all-companies");console.log("CLIENT COMPANIES UNDER BROKER: ",r);let t=r.companies||[];try{let e=await (0,o.A_)("/employee/company-details");console.log("BROKER'S OWN COMPANY: ",e),e.company&&e.company.isBrokerage&&!t.some(r=>r._id===e.company._id)&&(t.unshift(e.company),console.log("Added broker's own company to the list"))}catch(e){console.log("Could not fetch broker's own company (this is normal if user is not a broker):",e)}return console.log("ALL COMPANIES (CLIENT + OWN): ",t),e((0,i.Ym)(t)),{...r,companies:t}}catch(r){return console.error("Error fetching companies:",r),e((0,i.Ym)([])),{companies:[]}}}async function w(e){let r=await (0,o.A_)("/employee/company-details");return e((0,s.sy)(r.company)),r.status}},31870:(e,r,t)=>{"use strict";t.d(r,{C:()=>n,T:()=>s});var o=t(25842);let s=()=>(0,o.I0)(),n=o.v9},6079:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>o});let o=(0,t(68570).createProxy)(String.raw`C:\beno\project_dev\qharmony-frontend-v1\src\app\ai-enroller\layout.tsx#default`)},13658:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>o});let o=(0,t(68570).createProxy)(String.raw`C:\beno\project_dev\qharmony-frontend-v1\src\app\ai-enroller\renewal\page.tsx#default`)},73881:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});var o=t(66621);let s=e=>[{type:"image/x-icon",sizes:"16x16",url:(0,o.fillMetadataSegment)(".",e.params,"favicon.ico")+""}]},54658:()=>{},23824:()=>{}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),o=r.X(0,[8948,1183,6621,8492,576],()=>t(79268));module.exports=o})();