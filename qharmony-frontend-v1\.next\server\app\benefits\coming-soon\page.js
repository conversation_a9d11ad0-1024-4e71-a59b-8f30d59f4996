(()=>{var e={};e.id=6604,e.ids=[6604],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},27790:e=>{"use strict";e.exports=require("assert")},84770:e=>{"use strict";e.exports=require("crypto")},17702:e=>{"use strict";e.exports=require("events")},92048:e=>{"use strict";e.exports=require("fs")},32615:e=>{"use strict";e.exports=require("http")},35240:e=>{"use strict";e.exports=require("https")},19801:e=>{"use strict";e.exports=require("os")},55315:e=>{"use strict";e.exports=require("path")},76162:e=>{"use strict";e.exports=require("stream")},74026:e=>{"use strict";e.exports=require("string_decoder")},74175:e=>{"use strict";e.exports=require("tty")},17360:e=>{"use strict";e.exports=require("url")},21764:e=>{"use strict";e.exports=require("util")},71568:e=>{"use strict";e.exports=require("zlib")},98061:e=>{"use strict";e.exports=require("node:assert")},92761:e=>{"use strict";e.exports=require("node:async_hooks")},72254:e=>{"use strict";e.exports=require("node:buffer")},40027:e=>{"use strict";e.exports=require("node:console")},6005:e=>{"use strict";e.exports=require("node:crypto")},65714:e=>{"use strict";e.exports=require("node:diagnostics_channel")},15673:e=>{"use strict";e.exports=require("node:events")},88849:e=>{"use strict";e.exports=require("node:http")},42725:e=>{"use strict";e.exports=require("node:http2")},87503:e=>{"use strict";e.exports=require("node:net")},38846:e=>{"use strict";e.exports=require("node:perf_hooks")},39630:e=>{"use strict";e.exports=require("node:querystring")},84492:e=>{"use strict";e.exports=require("node:stream")},31764:e=>{"use strict";e.exports=require("node:tls")},41041:e=>{"use strict";e.exports=require("node:url")},47261:e=>{"use strict";e.exports=require("node:util")},93746:e=>{"use strict";e.exports=require("node:util/types")},24086:e=>{"use strict";e.exports=require("node:worker_threads")},65628:e=>{"use strict";e.exports=require("node:zlib")},98680:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>p,originalPathname:()=>u,pages:()=>d,routeModule:()=>x,tree:()=>c}),r(6247),r(33709),r(35866);var o=r(23191),n=r(88716),s=r(37922),i=r.n(s),l=r(95231),a={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(a[e]=()=>l[e]);r.d(t,a);let c=["",{children:["benefits",{children:["coming-soon",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,6247)),"C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\benefits\\coming-soon\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,33709)),"C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,35866,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],d=["C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\benefits\\coming-soon\\page.tsx"],u="/benefits/coming-soon/page",p={require:r,loadChunk:()=>Promise.resolve()},x=new o.AppPageRouteModule({definition:{kind:n.x.APP_PAGE,page:"/benefits/coming-soon/page",pathname:"/benefits/coming-soon",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},64153:(e,t,r)=>{Promise.resolve().then(r.bind(r,63605))},36690:(e,t,r)=>{"use strict";r.d(t,{Z:()=>s});var o=r(51426),n=r(10326);let s=(0,o.Z)((0,n.jsx)("path",{d:"M19 6.41 17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"}),"Close")},59351:(e,t,r)=>{"use strict";r.d(t,{Z:()=>F});var o=r(17577),n=r(41135),s=r(88634),i=r(44823),l=r(91703),a=r(13643),c=r(2791),d=r(31121),u=r(54641),p=r(40955),x=r(89178),m=r(71685),h=r(97898);function f(e){return(0,h.ZP)("MuiAlert",e)}let y=(0,m.Z)("MuiAlert",["root","action","icon","message","filled","colorSuccess","colorInfo","colorWarning","colorError","filledSuccess","filledInfo","filledWarning","filledError","outlined","outlinedSuccess","outlinedInfo","outlinedWarning","outlinedError","standard","standardSuccess","standardInfo","standardWarning","standardError"]);var g=r(48260),b=r(51426),v=r(10326);let j=(0,b.Z)((0,v.jsx)("path",{d:"M20,12A8,8 0 0,1 12,20A8,8 0 0,1 4,12A8,8 0 0,1 12,4C12.76,4 13.5,4.11 14.2, 4.31L15.77,2.74C14.61,2.26 13.34,2 12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0, 0 22,12M7.91,10.08L6.5,11.5L11,16L21,6L19.59,4.58L11,13.17L7.91,10.08Z"}),"SuccessOutlined"),S=(0,b.Z)((0,v.jsx)("path",{d:"M12 5.99L19.53 19H4.47L12 5.99M12 2L1 21h22L12 2zm1 14h-2v2h2v-2zm0-6h-2v4h2v-4z"}),"ReportProblemOutlined"),w=(0,b.Z)((0,v.jsx)("path",{d:"M11 15h2v2h-2zm0-8h2v6h-2zm.99-5C6.47 2 2 6.48 2 12s4.47 10 9.99 10C17.52 22 22 17.52 22 12S17.52 2 11.99 2zM12 20c-4.42 0-8-3.58-8-8s3.58-8 8-8 8 3.58 8 8-3.58 8-8 8z"}),"ErrorOutline"),C=(0,b.Z)((0,v.jsx)("path",{d:"M11,9H13V7H11M12,20C7.59,20 4,16.41 4,12C4,7.59 7.59,4 12,4C16.41,4 20,7.59 20, 12C20,16.41 16.41,20 12,20M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10, 10 0 0,0 12,2M11,17H13V11H11V17Z"}),"InfoOutlined"),Z=(0,b.Z)((0,v.jsx)("path",{d:"M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"}),"Close"),k=e=>{let{variant:t,color:r,severity:o,classes:n}=e,i={root:["root",`color${(0,u.Z)(r||o)}`,`${t}${(0,u.Z)(r||o)}`,`${t}`],icon:["icon"],message:["message"],action:["action"]};return(0,s.Z)(i,f,n)},P=(0,l.default)(x.Z,{name:"MuiAlert",slot:"Root",overridesResolver:(e,t)=>{let{ownerState:r}=e;return[t.root,t[r.variant],t[`${r.variant}${(0,u.Z)(r.color||r.severity)}`]]}})((0,a.Z)(({theme:e})=>{let t="light"===e.palette.mode?i._j:i.$n,r="light"===e.palette.mode?i.$n:i._j;return{...e.typography.body2,backgroundColor:"transparent",display:"flex",padding:"6px 16px",variants:[...Object.entries(e.palette).filter((0,p.Z)(["light"])).map(([o])=>({props:{colorSeverity:o,variant:"standard"},style:{color:e.vars?e.vars.palette.Alert[`${o}Color`]:t(e.palette[o].light,.6),backgroundColor:e.vars?e.vars.palette.Alert[`${o}StandardBg`]:r(e.palette[o].light,.9),[`& .${y.icon}`]:e.vars?{color:e.vars.palette.Alert[`${o}IconColor`]}:{color:e.palette[o].main}}})),...Object.entries(e.palette).filter((0,p.Z)(["light"])).map(([r])=>({props:{colorSeverity:r,variant:"outlined"},style:{color:e.vars?e.vars.palette.Alert[`${r}Color`]:t(e.palette[r].light,.6),border:`1px solid ${(e.vars||e).palette[r].light}`,[`& .${y.icon}`]:e.vars?{color:e.vars.palette.Alert[`${r}IconColor`]}:{color:e.palette[r].main}}})),...Object.entries(e.palette).filter((0,p.Z)(["dark"])).map(([t])=>({props:{colorSeverity:t,variant:"filled"},style:{fontWeight:e.typography.fontWeightMedium,...e.vars?{color:e.vars.palette.Alert[`${t}FilledColor`],backgroundColor:e.vars.palette.Alert[`${t}FilledBg`]}:{backgroundColor:"dark"===e.palette.mode?e.palette[t].dark:e.palette[t].main,color:e.palette.getContrastText(e.palette[t].main)}}}))]}})),M=(0,l.default)("div",{name:"MuiAlert",slot:"Icon",overridesResolver:(e,t)=>t.icon})({marginRight:12,padding:"7px 0",display:"flex",fontSize:22,opacity:.9}),$=(0,l.default)("div",{name:"MuiAlert",slot:"Message",overridesResolver:(e,t)=>t.message})({padding:"8px 0",minWidth:0,overflow:"auto"}),O=(0,l.default)("div",{name:"MuiAlert",slot:"Action",overridesResolver:(e,t)=>t.action})({display:"flex",alignItems:"flex-start",padding:"4px 0 0 16px",marginLeft:"auto",marginRight:-8}),A={success:(0,v.jsx)(j,{fontSize:"inherit"}),warning:(0,v.jsx)(S,{fontSize:"inherit"}),error:(0,v.jsx)(w,{fontSize:"inherit"}),info:(0,v.jsx)(C,{fontSize:"inherit"})},F=o.forwardRef(function(e,t){let r=(0,c.i)({props:e,name:"MuiAlert"}),{action:o,children:s,className:i,closeText:l="Close",color:a,components:u={},componentsProps:p={},icon:x,iconMapping:m=A,onClose:h,role:f="alert",severity:y="success",slotProps:b={},slots:j={},variant:S="standard",...w}=r,C={...r,color:a,severity:y,variant:S,colorSeverity:a||y},F=k(C),D={slots:{closeButton:u.CloseButton,closeIcon:u.CloseIcon,...j},slotProps:{...p,...b}},[z,q]=(0,d.Z)("root",{ref:t,shouldForwardComponentProp:!0,className:(0,n.Z)(F.root,i),elementType:P,externalForwardedProps:{...D,...w},ownerState:C,additionalProps:{role:f,elevation:0}}),[_,I]=(0,d.Z)("icon",{className:F.icon,elementType:M,externalForwardedProps:D,ownerState:C}),[W,T]=(0,d.Z)("message",{className:F.message,elementType:$,externalForwardedProps:D,ownerState:C}),[L,B]=(0,d.Z)("action",{className:F.action,elementType:O,externalForwardedProps:D,ownerState:C}),[E,R]=(0,d.Z)("closeButton",{elementType:g.Z,externalForwardedProps:D,ownerState:C}),[H,N]=(0,d.Z)("closeIcon",{elementType:Z,externalForwardedProps:D,ownerState:C});return(0,v.jsxs)(z,{...q,children:[!1!==x?(0,v.jsx)(_,{...I,children:x||m[y]||A[y]}):null,(0,v.jsx)(W,{...T,children:s}),null!=o?(0,v.jsx)(L,{...B,children:o}):null,null==o&&h?(0,v.jsx)(L,{...B,children:(0,v.jsx)(E,{size:"small","aria-label":l,title:l,color:"inherit",onClick:h,...R,children:(0,v.jsx)(H,{fontSize:"small",...N})})}):null]})})},55771:(e,t,r)=>{"use strict";r.d(t,{Z:()=>l});var o=r(3156),n=r(54641),s=r(91703),i=r(2791);let l=(0,o.Z)({createStyledComponent:(0,s.default)("div",{name:"MuiContainer",slot:"Root",overridesResolver:(e,t)=>{let{ownerState:r}=e;return[t.root,t[`maxWidth${(0,n.Z)(String(r.maxWidth))}`],r.fixed&&t.fixed,r.disableGutters&&t.disableGutters]}}),useThemeProps:e=>(0,i.i)({props:e,name:"MuiContainer"})})},52188:(e,t,r)=>{"use strict";r.d(t,{Z:()=>Z});var o=r(17577),n=r(41135),s=r(44823),i=r(88634),l=r(64263),a=r(54641),c=r(91703),d=r(23743),u=r(13643),p=r(40955),x=r(2791),m=r(25609),h=r(71685),f=r(97898);function y(e){return(0,f.ZP)("MuiLink",e)}let g=(0,h.Z)("MuiLink",["root","underlineNone","underlineHover","underlineAlways","button","focusVisible"]);var b=r(38482);let v=({theme:e,ownerState:t})=>{let r=t.color,o=(0,b.DW)(e,`palette.${r}.main`,!1)||(0,b.DW)(e,`palette.${r}`,!1)||t.color,n=(0,b.DW)(e,`palette.${r}.mainChannel`)||(0,b.DW)(e,`palette.${r}Channel`);return"vars"in e&&n?`rgba(${n} / 0.4)`:(0,s.Fq)(o,.4)};var j=r(10326);let S={primary:!0,secondary:!0,error:!0,info:!0,success:!0,warning:!0,textPrimary:!0,textSecondary:!0,textDisabled:!0},w=e=>{let{classes:t,component:r,focusVisible:o,underline:n}=e,s={root:["root",`underline${(0,a.Z)(n)}`,"button"===r&&"button",o&&"focusVisible"]};return(0,i.Z)(s,y,t)},C=(0,c.default)(m.Z,{name:"MuiLink",slot:"Root",overridesResolver:(e,t)=>{let{ownerState:r}=e;return[t.root,t[`underline${(0,a.Z)(r.underline)}`],"button"===r.component&&t.button]}})((0,u.Z)(({theme:e})=>({variants:[{props:{underline:"none"},style:{textDecoration:"none"}},{props:{underline:"hover"},style:{textDecoration:"none","&:hover":{textDecoration:"underline"}}},{props:{underline:"always"},style:{textDecoration:"underline","&:hover":{textDecorationColor:"inherit"}}},{props:({underline:e,ownerState:t})=>"always"===e&&"inherit"!==t.color,style:{textDecorationColor:"var(--Link-underlineColor)"}},...Object.entries(e.palette).filter((0,p.Z)()).map(([t])=>({props:{underline:"always",color:t},style:{"--Link-underlineColor":e.vars?`rgba(${e.vars.palette[t].mainChannel} / 0.4)`:(0,s.Fq)(e.palette[t].main,.4)}})),{props:{underline:"always",color:"textPrimary"},style:{"--Link-underlineColor":e.vars?`rgba(${e.vars.palette.text.primaryChannel} / 0.4)`:(0,s.Fq)(e.palette.text.primary,.4)}},{props:{underline:"always",color:"textSecondary"},style:{"--Link-underlineColor":e.vars?`rgba(${e.vars.palette.text.secondaryChannel} / 0.4)`:(0,s.Fq)(e.palette.text.secondary,.4)}},{props:{underline:"always",color:"textDisabled"},style:{"--Link-underlineColor":(e.vars||e).palette.text.disabled}},{props:{component:"button"},style:{position:"relative",WebkitTapHighlightColor:"transparent",backgroundColor:"transparent",outline:0,border:0,margin:0,borderRadius:0,padding:0,cursor:"pointer",userSelect:"none",verticalAlign:"middle",MozAppearance:"none",WebkitAppearance:"none","&::-moz-focus-inner":{borderStyle:"none"},[`&.${g.focusVisible}`]:{outline:"auto"}}}]}))),Z=o.forwardRef(function(e,t){let r=(0,x.i)({props:e,name:"MuiLink"}),s=(0,d.default)(),{className:i,color:a="primary",component:c="a",onBlur:u,onFocus:p,TypographyClasses:m,underline:h="always",variant:f="inherit",sx:y,...g}=r,[b,Z]=o.useState(!1),k={...r,color:a,component:c,focusVisible:b,underline:h,variant:f},P=w(k);return(0,j.jsx)(C,{color:a,className:(0,n.Z)(P.root,i),classes:m,component:c,onBlur:e=>{(0,l.Z)(e.target)||Z(!1),u&&u(e)},onFocus:e=>{(0,l.Z)(e.target)&&Z(!0),p&&p(e)},ref:t,ownerState:k,variant:f,...g,sx:[...void 0===S[a]?[{color:a}]:[],...Array.isArray(y)?y:[y]],style:{...g.style,..."always"===h&&"inherit"!==a&&!S[a]&&{"--Link-underlineColor":v({theme:s,ownerState:k})}}})})},48090:(e,t,r)=>{"use strict";r.d(t,{Z:()=>s});var o=r(54347),n=r(14750);let s=(0,o.unstable_createUseMediaQuery)({themeId:n.Z})},33265:(e,t,r)=>{"use strict";r.d(t,{default:()=>n.a});var o=r(43353),n=r.n(o)},43353:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return s}});let o=r(91174);r(10326),r(17577);let n=o._(r(77028));function s(e,t){var r;let o={loading:e=>{let{error:t,isLoading:r,pastDelay:o}=e;return null}};"function"==typeof e&&(o.loader=e);let s={...o,...t};return(0,n.default)({...s,modules:null==(r=s.loadableGenerated)?void 0:r.modules})}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},933:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"BailoutToCSR",{enumerable:!0,get:function(){return n}});let o=r(94129);function n(e){let{reason:t,children:r}=e;throw new o.BailoutToCSRError(t)}},77028:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return c}});let o=r(10326),n=r(17577),s=r(933),i=r(46618);function l(e){return{default:e&&"default"in e?e.default:e}}let a={loader:()=>Promise.resolve(l(()=>null)),loading:null,ssr:!0},c=function(e){let t={...a,...e},r=(0,n.lazy)(()=>t.loader().then(l)),c=t.loading;function d(e){let l=c?(0,o.jsx)(c,{isLoading:!0,pastDelay:!0,error:null}):null,a=t.ssr?(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)(i.PreloadCss,{moduleIds:t.modules}),(0,o.jsx)(r,{...e})]}):(0,o.jsx)(s.BailoutToCSR,{reason:"next/dynamic",children:(0,o.jsx)(r,{...e})});return(0,o.jsx)(n.Suspense,{fallback:l,children:a})}return d.displayName="LoadableComponent",d}},46618:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"PreloadCss",{enumerable:!0,get:function(){return s}});let o=r(10326),n=r(54580);function s(e){let{moduleIds:t}=e,r=(0,n.getExpectedRequestStore)("next/dynamic css"),s=[];if(r.reactLoadableManifest&&t){let e=r.reactLoadableManifest;for(let r of t){if(!e[r])continue;let t=e[r].files.filter(e=>e.endsWith(".css"));s.push(...t)}}return 0===s.length?null:(0,o.jsx)(o.Fragment,{children:s.map(e=>(0,o.jsx)("link",{precedence:"dynamic",rel:"stylesheet",href:r.assetPrefix+"/_next/"+encodeURI(e),as:"style"},e))})}},63605:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>T});var o=r(10326),n=r(33265),s=r(17577),i=r(23743),l=r(48090),a=r(6283),c=r(55771),d=r(25609),u=r(52188),p=r(90541),x=r(42265),m=r(53148),h=r(9426),f=r(87841),y=r(48260),g=r(19074),b=r(84979),v=r(71411),j=r(24003),S=r(25886),w=r(90434),C=r(46226),Z=r(6725),k=r(36690);let P=[{text:"Why BenOsphere\xae",href:"https://benosphere.com/#why"},{text:"Solutions",href:"https://benosphere.com/#benifits"},{text:"How It Works",href:"https://benosphere.com/#workflow"},{text:"Census",href:"/census"},{text:"Pricing",href:"https://benosphere.com/#section-pricing"},{text:"FAQ",href:"https://benosphere.com/#faq"},{text:"Blog",href:"https://benosphere.com/blog"}],M=({hideGetStarted:e=!1})=>{let[t,r]=(0,s.useState)(!1),[n,u]=(0,s.useState)(!1);(0,i.default)();let p=(0,l.Z)("(max-width:767px)",{noSsr:!0}),m=()=>{r(window.scrollY>10)};(0,s.useEffect)(()=>(window.addEventListener("scroll",m),()=>window.removeEventListener("scroll",m)),[]);let M=()=>{u(!n)};return(0,o.jsxs)(h.Z,{position:"fixed",elevation:t?4:0,sx:{backgroundColor:t?"rgba(0, 0, 0, 0.85)":"transparent",color:"#fff",transition:"background-color 0.3s ease",boxShadow:t?"0 0 8px rgba(0,0,0,0.3)":"none",backdropFilter:"blur(8px)",zIndex:1300},children:[o.jsx(c.Z,{maxWidth:"lg",children:(0,o.jsxs)(f.Z,{disableGutters:!0,sx:{display:"flex",justifyContent:"space-between",height:64},children:[o.jsx(a.Z,{sx:{display:"flex",alignItems:"center"},children:o.jsx(w.default,{href:"https://benosphere.com/",passHref:!0,style:{textDecoration:"none"},children:o.jsx(a.Z,{sx:{display:"flex",alignItems:"center",cursor:"pointer"},children:o.jsx(C.default,{src:"/logo1.png",alt:"Benosphere Logo",width:160,height:160})})})}),!p&&(0,o.jsxs)(a.Z,{sx:{display:"flex",alignItems:"center",gap:4},children:[P.map(e=>o.jsx(w.default,{href:e.href,passHref:!0,style:{textDecoration:"none"},children:o.jsx(d.Z,{sx:{color:"#fff",textDecoration:"none",fontSize:"1rem",cursor:"pointer","&:hover":{color:"#B88BFF"},transition:"color 0.2s ease"},children:e.text})},e.href)),!e&&o.jsx(w.default,{href:"/onboard",passHref:!0,style:{textDecoration:"none"},children:o.jsx(x.Z,{variant:"contained",sx:{background:"linear-gradient(to bottom, #6C42FF, #B88BFF)",color:"#fff",textTransform:"none",fontWeight:600,px:2.5,py:1,borderRadius:"8px","&:hover":{opacity:.9}},children:"Get Started for Free"})})]}),p&&o.jsx(y.Z,{edge:"end",color:"inherit","aria-label":"menu",onClick:M,sx:{ml:2},children:o.jsx(Z.Z,{})})]})}),o.jsx(g.ZP,{anchor:"right",open:n,onClose:M,PaperProps:{sx:{width:"250px",maxWidth:"80%",backgroundColor:"rgba(0, 0, 0, 0.95)",color:"#fff"}},children:(0,o.jsxs)(a.Z,{sx:{p:1.5},children:[" ",o.jsx(a.Z,{sx:{display:"flex",justifyContent:"flex-end"},children:o.jsx(y.Z,{color:"inherit",onClick:M,sx:{padding:"6px","& .MuiSvgIcon-root":{fontSize:"1.25rem"}},children:o.jsx(k.Z,{})})}),(0,o.jsxs)(b.Z,{children:[P.map(e=>o.jsx(w.default,{href:e.href,passHref:!0,style:{textDecoration:"none"},children:o.jsx(v.ZP,{disablePadding:!0,children:o.jsx(j.Z,{onClick:M,sx:{px:1.5,py:1,"&:hover":{backgroundColor:"rgba(255, 255, 255, 0.08)"}},children:o.jsx(S.Z,{primary:e.text,sx:{"& .MuiListItemText-primary":{color:"#fff",fontSize:"1rem",fontWeight:500}}})})})},e.href)),!e&&o.jsx(v.ZP,{sx:{mt:4},children:o.jsx(w.default,{href:"https://app.benosphere.com/onboard",passHref:!0,style:{width:"100%",textDecoration:"none"},children:o.jsx(x.Z,{fullWidth:!0,variant:"contained",onClick:M,sx:{background:"linear-gradient(to bottom, #6C42FF, #B88BFF)",color:"#fff",textTransform:"none",fontWeight:600,py:1.5,borderRadius:"8px","&:hover":{opacity:.9}},children:"Get Started for Free"})})})]})]})})]})},$=()=>{let e=(0,i.default)();return(0,l.Z)(e.breakpoints.down("sm")),o.jsx(a.Z,{component:"footer",sx:{bgcolor:"#000",color:"#f0f0f0",py:{xs:2,sm:3},px:{xs:1,sm:2},mt:{xs:6,sm:10}},children:o.jsx(c.Z,{maxWidth:"md",children:(0,o.jsxs)(a.Z,{sx:{display:"flex",flexDirection:{xs:"column",sm:"row"},justifyContent:"center",alignItems:"center",flexWrap:"wrap",textAlign:"center",gap:{xs:1,sm:2}},children:[o.jsx(d.Z,{variant:"body2",sx:{fontSize:{xs:"0.75rem",sm:"0.875rem"},order:{xs:1,sm:1},color:"#888"},children:"\xa9 BenOsphere 2025"}),(0,o.jsxs)(a.Z,{sx:{display:"flex",flexDirection:{xs:"column",sm:"row"},gap:{xs:1,sm:2},order:{xs:3,sm:2}},children:[o.jsx(u.Z,{href:"https://benosphere.com/terms-conditions",target:"_blank",rel:"noopener noreferrer",sx:{color:"#888",textDecoration:"none",fontSize:{xs:"0.75rem",sm:"0.875rem"},"&:hover":{textDecoration:"underline"}},children:"Terms of Service"}),o.jsx(u.Z,{href:"https://benosphere.com/privacy-policy",target:"_blank",rel:"noopener noreferrer",sx:{color:"#888",textDecoration:"none",fontSize:{xs:"0.75rem",sm:"0.875rem"},"&:hover":{textDecoration:"underline"}},children:"Privacy Policy"}),o.jsx(u.Z,{href:"mailto:<EMAIL>",sx:{color:"#888",textDecoration:"none",fontSize:{xs:"0.75rem",sm:"0.875rem"},"&:hover":{textDecoration:"underline"}},children:"<EMAIL>"})]}),o.jsx(d.Z,{variant:"body2",sx:{fontSize:{xs:"0.75rem",sm:"0.875rem"},order:{xs:2,sm:3},color:"#888"},children:"\uD83E\uDD0D With love from Seattle"})]})})})};var O=r(59351),A=r(35047),F=r(44046),D=r(74034);function z(e){return(0,D.w_)({tag:"svg",attr:{viewBox:"0 0 448 512"},child:[{tag:"path",attr:{d:"M64 32C28.7 32 0 60.7 0 96V416c0 35.3 28.7 64 64 64H384c35.3 0 64-28.7 64-64V96c0-35.3-28.7-64-64-64H64zm297.1 84L257.3 234.6 379.4 396H283.8L209 298.1 123.3 396H75.8l111-126.9L69.7 116h98l67.7 89.5L313.6 116h47.5zM323.3 367.6L153.4 142.9H125.1L296.9 367.6h26.3z"},child:[]}]})(e)}let q=({title:e})=>{let t=(0,A.usePathname)(),r=encodeURIComponent(`https://app.benosphere.com${t}`),n=encodeURIComponent(e),[i,l]=(0,s.useState)(!1),[a,c]=(0,s.useState)(!1),[d,u]=(0,s.useState)(!1);return(0,s.useEffect)(()=>{(()=>{let e=navigator.userAgent||navigator.vendor||window.opera||"";l(/android|iphone|ipad|ipod|blackberry|windows phone/i.test(e)),c(/iphone|ipad|ipod/i.test(e)),u(/android/i.test(e))})()},[]),(0,o.jsxs)("div",{style:{display:"flex",gap:"12px",alignItems:"center"},children:[o.jsx("a",{href:`https://twitter.com/intent/tweet?url=${r}&text=${n}`,target:"_blank",rel:"noopener noreferrer",title:"Share on X",style:{display:"inline-block",transition:"transform 0.3s ease"},onMouseOver:e=>{e.currentTarget.style.transform="scale(1.2)",e.currentTarget.style.opacity="0.8"},onMouseOut:e=>{e.currentTarget.style.transform="scale(1)",e.currentTarget.style.opacity="1"},children:o.jsx(z,{size:24,color:"#FFFFFF"})}),o.jsx("a",{href:`https://www.linkedin.com/sharing/share-offsite/?url=${r}&text=${n}`,target:"_blank",rel:"noopener noreferrer",title:"Share on LinkedIn",style:{display:"inline-block",transition:"transform 0.3s ease"},onMouseOver:e=>{e.currentTarget.style.transform="scale(1.2)",e.currentTarget.style.opacity="0.8"},onMouseOut:e=>{e.currentTarget.style.transform="scale(1)",e.currentTarget.style.opacity="1"},children:o.jsx(F.ltd,{size:24,color:"#0077B5"})}),o.jsx("a",{href:`https://api.whatsapp.com/send?text=${n}%20${r}`,target:"_blank",rel:"noopener noreferrer",title:"Share on WhatsApp",style:{display:"inline-block",transition:"transform 0.3s ease"},onMouseOver:e=>{e.currentTarget.style.transform="scale(1.2)",e.currentTarget.style.opacity="0.8"},onMouseOut:e=>{e.currentTarget.style.transform="scale(1)",e.currentTarget.style.opacity="1"},children:o.jsx(F.xpo,{size:24,color:"#25D366"})}),i&&o.jsx("a",{href:a?`sms:&body=${n}%20${r}`:`sms:?body=${n}%20${r}`,title:"Share via SMS",style:{display:"inline-block",transition:"transform 0.3s ease"},onMouseOver:e=>{e.currentTarget.style.transform="scale(1.2)",e.currentTarget.style.opacity="0.8"},onMouseOut:e=>{e.currentTarget.style.transform="scale(1)",e.currentTarget.style.opacity="1"},children:o.jsx(F.bJx,{size:24,color:"#FFC107"})}),(!i||!a&&!d)&&o.jsx("a",{href:`https://www.facebook.com/sharer/sharer.php?u=${r}`,target:"_blank",rel:"noopener noreferrer",title:"Share on Facebook",style:{display:"inline-block",transition:"transform 0.3s ease"},onMouseOver:e=>{e.currentTarget.style.transform="scale(1.2)",e.currentTarget.style.opacity="0.8"},onMouseOut:e=>{e.currentTarget.style.transform="scale(1)",e.currentTarget.style.opacity="1"},children:o.jsx(F.Am9,{size:24,color:"#1877F2"})})]})},_=["❌ 1 in 4 skip checkups yearly","❌ 55% of PTO goes unused yearly","❌ Up to $500 wellness perks unused yearly","❌ 70% don't know their benefits"],I=[["Preventive care","$300","$500","Skipped annual exams, delayed diagnosis (CDC)"],["Unused PTO","$600","$1,200","Time off expired or forfeited (U.S. Travel)"],["FSA/HSA funds","$300","$600","Forgotten or unused funds (WageWorks)"],["Wellness perks","$150","$400","Gym/wellness stipends unused (SHRM)"],["Mental health support","$100","$250","Low EAP utilization (NAMI)"],["Financial/legal assistance","$50","$150","Underused (MetLife)"],["Tuition/learning stipends","$100","$300","Missed career development (SHRM)"],["Caregiver/childcare support","$100","$300","Not utilized (Care.com)"],["Subtotal: Unused Benefits","$1,700","$3,700",""],["Chronic condition development","$1,000","$3,000","Diabetes, hypertension, etc. (CDC, ADA)"],["Emergency/hospital visits","$500","$2,000","ER visits, preventable hospitalization (KFF)"],["Productivity loss","$500","$1,000","Presenteeism (IBI)"],["Absenteeism/disability","$500","$1,000","Short-term disability or leave (SHRM)"],["Subtotal: Health & Productivity Loss","$2,500","$7,000",""],["Total Estimated Loss per Employee","$4,200","$11,000","Rounded for clarity"]],W=()=>{(0,s.useEffect)(()=>{document.title="BenOsphere: Stop Losing Your Benefits Money"},[]);let e=(0,i.default)();(0,l.Z)(e.breakpoints.down("sm"));let[t,r]=(0,s.useState)(""),[n,h]=(0,s.useState)(""),[f,y]=(0,s.useState)(!1),[g,b]=(0,s.useState)(""),[v,j]=(0,s.useState)(!1),[S,w]=(0,s.useState)(!1),[C,Z]=(0,s.useState)(0),[k,P]=(0,s.useState)(279),A=(0,s.useRef)(null);(0,s.useEffect)(()=>{(async()=>{try{let e=await (0,m.A_)("/test/waitlist/entries");if(console.log("Waitlist response:",e),e&&e.entries&&Array.isArray(e.entries)){let t=279+e.entries.length;P(t),console.log("Setting target counter to:",t)}}catch(e){console.error("Error fetching waitlist entries:",e)}})()},[]),(0,s.useEffect)(()=>{A.current&&clearInterval(A.current),Z(0);let e=Math.ceil(k/50);return A.current=setInterval(()=>{Z(t=>{let r=t+e;return r>=k?(clearInterval(A.current),k):r})},40),()=>{A.current&&clearInterval(A.current)}},[k]);let F=async e=>{if(e.preventDefault(),b(""),!t)return b("Email address is required.");if(!n)return b("Phone number is required.");if(10!==n.length||!/^\d{10}$/.test(n))return b("Phone number must be exactly 10 digits.");try{await (0,m.j0)("/test/waitlist/join",{email:t.toLowerCase(),phone:n,joinedAt:new Date().toISOString()}),P(e=>e+1),y(!0),w(!1)}catch(e){console.error("Waitlist join error:",e),b(e?.response?.data?.message||"Failed to join waitlist. Please check your connection and try again.")}};return(0,o.jsxs)(a.Z,{sx:{bgcolor:"#000",color:"#fff",minHeight:"99vh",display:"flex",flexDirection:"column",fontFamily:'"SF Pro", -apple-system, BlinkMacSystemFont, sans-serif'},children:[o.jsx(M,{hideGetStarted:!0}),o.jsx(a.Z,{component:"main",sx:{flex:1,display:"flex",alignItems:"center",justifyContent:"center",py:{xs:1,md:3},pt:{xs:4,md:9}},children:o.jsx(c.Z,{maxWidth:"md",sx:{px:{xs:1,sm:2,md:3},display:"flex",alignItems:"center",justifyContent:"center",height:"100%",pt:{xs:2,md:6}},children:(0,o.jsxs)(a.Z,{textAlign:"center",maxWidth:900,mx:"auto",sx:{width:"100%",display:"flex",flexDirection:"column",alignItems:"center",gap:{xs:1,sm:1.5},py:{xs:1,sm:2}},children:[o.jsx(d.Z,{variant:"h1",sx:{fontSize:{xs:"1.5rem",sm:"2rem",md:"2.5rem"},fontWeight:600,mb:{xs:1,sm:2},mt:{xs:4,sm:2},lineHeight:1.2},children:"You could be losing a third of your paycheck."}),o.jsx(d.Z,{sx:{color:"#ccc",mb:{xs:.5,sm:1},fontSize:{xs:"0.875rem",sm:"1rem",md:"1.25rem"}},children:"Feeling sick while debt piles up? Don't skip the care your family already has."}),(0,o.jsxs)(u.Z,{href:"#",onClick:e=>{e.preventDefault(),j(!0)},sx:{color:"#B983FF",fontSize:{xs:"0.875rem",sm:"1rem"},textDecoration:"none",mb:{xs:1,sm:2},display:"inline-block","&:hover":{color:"#D4A5FF"},fontFamily:'"SF Pro", -apple-system, BlinkMacSystemFont, sans-serif'},children:[o.jsx("span",{style:{textDecoration:"none"},children:"\uD83D\uDC49"})," ",o.jsx("span",{style:{textDecoration:"underline"},children:"Find your lost employee benefits"})]}),o.jsx(a.Z,{sx:{display:"flex",flexWrap:"wrap",justifyContent:"center",gap:{xs:"4px",sm:"8px"},mb:{xs:1,sm:2}},children:_.map((e,t)=>o.jsx(a.Z,{sx:{bgcolor:"#111",border:"1px solid #333",borderRadius:"8px",px:{xs:1.5,sm:2},py:{xs:.5,sm:.75},fontSize:{xs:"0.8rem",sm:"0.95rem"},fontFamily:'"SF Pro", -apple-system, BlinkMacSystemFont, sans-serif'},children:e},t))}),(0,o.jsxs)(d.Z,{className:"counter-text",sx:{fontSize:{xs:"0.875rem",sm:"1rem"},color:"#ccc",mt:{xs:.5,sm:1}},children:["\uD83D\uDD25 ",C," people have already joined the waitlist"]}),f?o.jsx(O.Z,{severity:"success",sx:{mt:{xs:2.5,sm:3.5},maxWidth:{xs:"300px",sm:"400px"},mx:"auto","& .MuiAlert-message":{fontSize:{xs:"0.875rem",sm:"1rem"}}},children:"Thanks for joining our waitlist! We'll let you know when we launch."}):S?(0,o.jsxs)(a.Z,{component:"form",onSubmit:F,sx:{width:"100%",maxWidth:{xs:"280px",sm:"400px"},mx:"auto",mt:{xs:2.5,sm:3.5},display:"flex",flexDirection:"column",gap:{xs:1.5,sm:2}},children:[o.jsx(p.Z,{type:"email",placeholder:"Email address",value:t,onChange:e=>r(e.target.value),fullWidth:!0,required:!0,sx:{"& .MuiInputBase-root":{height:{xs:"35px",sm:"45px"},color:"#fff",fontSize:{xs:"0.75rem",sm:"1rem"}},"& .MuiOutlinedInput-notchedOutline":{borderColor:"#444"},"&:hover .MuiOutlinedInput-notchedOutline":{borderColor:"#666"}}}),o.jsx(p.Z,{type:"tel",placeholder:"Phone number",value:n,onChange:e=>h(e.target.value),fullWidth:!0,required:!0,sx:{"& .MuiInputBase-root":{height:{xs:"35px",sm:"45px"},color:"#fff",fontSize:{xs:"0.75rem",sm:"1rem"}},"& .MuiOutlinedInput-notchedOutline":{borderColor:"#444"},"&:hover .MuiOutlinedInput-notchedOutline":{borderColor:"#666"}}}),g&&o.jsx(O.Z,{severity:"error",sx:{fontSize:{xs:"0.75rem",sm:"0.875rem"}},children:g}),o.jsx(x.Z,{type:"submit",fullWidth:!0,variant:"contained",sx:{height:{xs:"32px",sm:"45px"},borderRadius:"8px",fontWeight:600,fontSize:{xs:"0.75rem",sm:"1rem"},background:"#B983FF",color:"#000","&:hover":{backgroundColor:"#D4A5FF"},textTransform:"none",mt:{xs:.5,sm:1}},children:"Save Your Paycheck"})]}):o.jsx(x.Z,{onClick:()=>w(!0),variant:"contained",sx:{mt:{xs:2.5,sm:3.5},px:{xs:3,sm:4},py:{xs:1,sm:1.5},borderRadius:"8px",fontWeight:600,fontSize:{xs:"0.875rem",sm:"1rem"},background:"#B983FF",color:"#000","&:hover":{backgroundColor:"#D4A5FF"},textTransform:"none"},children:"Save Your Paycheck"}),(0,o.jsxs)(a.Z,{sx:{mt:{xs:2,sm:3},display:"flex",justifyContent:"center",alignItems:"center",flexDirection:"column",gap:1},children:[o.jsx(d.Z,{sx:{fontSize:{xs:"0.875rem",sm:"1rem"},color:"#ccc",mb:1},children:"Help a teammate stop losing money — share this."}),o.jsx(q,{title:"Saw this and thought it might be helpful — we might be losing thousands in employee benefits."})]})]})})}),v&&o.jsx(a.Z,{sx:{position:"fixed",inset:0,bgcolor:"rgba(0, 0, 0, 0.7)",zIndex:999,display:"flex",alignItems:"center",justifyContent:"center"},onClick:()=>j(!1),children:(0,o.jsxs)(a.Z,{sx:{bgcolor:"#111",color:"#fff",p:3,borderRadius:2,border:"1px solid #333",width:"90%",maxWidth:600,maxHeight:"80vh",overflowY:"auto",boxShadow:"0 10px 30px rgba(0,0,0,0.5)"},onClick:e=>e.stopPropagation(),children:[(0,o.jsxs)(a.Z,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",mb:2},children:[o.jsx(d.Z,{variant:"h6",children:"You're losing up to $11,000 annually on benefits."}),o.jsx(x.Z,{onClick:()=>j(!1),sx:{color:"#aaa",minWidth:"auto",p:.5},children:"X"})]}),o.jsx(a.Z,{sx:{width:"100%",overflowX:"auto"},children:(0,o.jsxs)(a.Z,{component:"table",sx:{width:"100%",borderCollapse:"collapse",fontSize:"0.9rem"},children:[o.jsx("thead",{children:(0,o.jsxs)("tr",{children:[o.jsx("th",{style:{textAlign:"left",padding:"8px 12px",borderBottom:"1px solid #444"},children:"Category"}),o.jsx("th",{style:{textAlign:"right",padding:"8px 12px",borderBottom:"1px solid #444"},children:"Low"}),o.jsx("th",{style:{textAlign:"right",padding:"8px 12px",borderBottom:"1px solid #444"},children:"High"}),o.jsx("th",{style:{textAlign:"left",padding:"8px 12px",borderBottom:"1px solid #444"},children:"Notes"})]})}),o.jsx("tbody",{children:I.map(([e,t,r,n],s)=>(0,o.jsxs)("tr",{style:{background:[8,13].includes(s)?"#222":14===s?"#333":"transparent",fontWeight:[8,13,14].includes(s)?"bold":"normal"},children:[o.jsx("td",{style:{padding:"8px 12px"},children:e}),o.jsx("td",{style:{padding:"8px 12px",textAlign:"right"},children:t}),o.jsx("td",{style:{padding:"8px 12px",textAlign:"right"},children:r}),o.jsx("td",{style:{padding:"8px 12px",color:"#ccc"},children:n})]},s))})]})}),o.jsx(d.Z,{sx:{fontSize:"0.8rem",mt:3,color:"#aaa",textAlign:"center"},children:"Disclaimer: National averages based on industry research. Actual losses vary per individual."})]})}),o.jsx($,{})]})},T=(0,n.default)(()=>Promise.resolve(W),{ssr:!1})},6247:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>o});let o=(0,r(68570).createProxy)(String.raw`C:\beno\project_dev\qharmony-frontend-v1\src\app\benefits\coming-soon\page.tsx#default`)},73881:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});var o=r(66621);let n=e=>[{type:"image/x-icon",sizes:"16x16",url:(0,o.fillMetadataSegment)(".",e.params,"favicon.ico")+""}]},74034:(e,t,r)=>{"use strict";r.d(t,{w_:()=>d});var o=r(17577),n={color:void 0,size:void 0,className:void 0,style:void 0,attr:void 0},s=o.createContext&&o.createContext(n),i=["attr","size","title"];function l(){return(l=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var o in r)Object.prototype.hasOwnProperty.call(r,o)&&(e[o]=r[o])}return e}).apply(this,arguments)}function a(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,o)}return r}function c(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?a(Object(r),!0).forEach(function(t){var o,n;o=t,n=r[t],(o=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var o=r.call(e,t||"default");if("object"!=typeof o)return o;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(o))in e?Object.defineProperty(e,o,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[o]=n}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):a(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function d(e){return t=>o.createElement(u,l({attr:c({},e.attr)},t),function e(t){return t&&t.map((t,r)=>o.createElement(t.tag,c({key:r},t.attr),e(t.child)))}(e.child))}function u(e){var t=t=>{var r,{attr:n,size:s,title:a}=e,d=function(e,t){if(null==e)return{};var r,o,n=function(e,t){if(null==e)return{};var r={};for(var o in e)if(Object.prototype.hasOwnProperty.call(e,o)){if(t.indexOf(o)>=0)continue;r[o]=e[o]}return r}(e,t);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(e);for(o=0;o<s.length;o++)r=s[o],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(n[r]=e[r])}return n}(e,i),u=s||t.size||"1em";return t.className&&(r=t.className),e.className&&(r=(r?r+" ":"")+e.className),o.createElement("svg",l({stroke:"currentColor",fill:"currentColor",strokeWidth:"0"},t.attr,n,d,{className:r,style:c(c({color:e.color||t.color},t.style),e.style),height:u,width:u,xmlns:"http://www.w3.org/2000/svg"}),a&&o.createElement("title",null,a),e.children)};return void 0!==s?o.createElement(s.Consumer,null,e=>t(e)):t(n)}}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),o=t.X(0,[8948,1183,6621,9066,1999,3253,928,8097,8522,541,8705,434,4046,576],()=>r(98680));module.exports=o})();