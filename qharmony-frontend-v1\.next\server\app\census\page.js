(()=>{var e={};e.id=9681,e.ids=[9681],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},27790:e=>{"use strict";e.exports=require("assert")},84770:e=>{"use strict";e.exports=require("crypto")},17702:e=>{"use strict";e.exports=require("events")},92048:e=>{"use strict";e.exports=require("fs")},32615:e=>{"use strict";e.exports=require("http")},35240:e=>{"use strict";e.exports=require("https")},19801:e=>{"use strict";e.exports=require("os")},55315:e=>{"use strict";e.exports=require("path")},76162:e=>{"use strict";e.exports=require("stream")},74026:e=>{"use strict";e.exports=require("string_decoder")},74175:e=>{"use strict";e.exports=require("tty")},17360:e=>{"use strict";e.exports=require("url")},21764:e=>{"use strict";e.exports=require("util")},71568:e=>{"use strict";e.exports=require("zlib")},98061:e=>{"use strict";e.exports=require("node:assert")},92761:e=>{"use strict";e.exports=require("node:async_hooks")},72254:e=>{"use strict";e.exports=require("node:buffer")},40027:e=>{"use strict";e.exports=require("node:console")},6005:e=>{"use strict";e.exports=require("node:crypto")},65714:e=>{"use strict";e.exports=require("node:diagnostics_channel")},15673:e=>{"use strict";e.exports=require("node:events")},88849:e=>{"use strict";e.exports=require("node:http")},42725:e=>{"use strict";e.exports=require("node:http2")},87503:e=>{"use strict";e.exports=require("node:net")},38846:e=>{"use strict";e.exports=require("node:perf_hooks")},39630:e=>{"use strict";e.exports=require("node:querystring")},84492:e=>{"use strict";e.exports=require("node:stream")},31764:e=>{"use strict";e.exports=require("node:tls")},41041:e=>{"use strict";e.exports=require("node:url")},47261:e=>{"use strict";e.exports=require("node:util")},93746:e=>{"use strict";e.exports=require("node:util/types")},24086:e=>{"use strict";e.exports=require("node:worker_threads")},65628:e=>{"use strict";e.exports=require("node:zlib")},51998:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>s.a,__next_app__:()=>f,originalPathname:()=>d,pages:()=>u,routeModule:()=>p,tree:()=>c}),r(82219),r(33709),r(35866);var n=r(23191),o=r(88716),i=r(37922),s=r.n(i),a=r(95231),l={};for(let e in a)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>a[e]);r.d(t,l);let c=["",{children:["census",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,82219)),"C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\census\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,33709)),"C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,35866,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],u=["C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\census\\page.tsx"],d="/census/page",f={require:r,loadChunk:()=>Promise.resolve()},p=new n.AppPageRouteModule({definition:{kind:o.x.APP_PAGE,page:"/census/page",pathname:"/census",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},31660:(e,t,r)=>{Promise.resolve().then(r.bind(r,68384))},33265:(e,t,r)=>{"use strict";r.d(t,{default:()=>o.a});var n=r(43353),o=r.n(n)},43353:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return i}});let n=r(91174);r(10326),r(17577);let o=n._(r(77028));function i(e,t){var r;let n={loading:e=>{let{error:t,isLoading:r,pastDelay:n}=e;return null}};"function"==typeof e&&(n.loader=e);let i={...n,...t};return(0,o.default)({...i,modules:null==(r=i.loadableGenerated)?void 0:r.modules})}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},933:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"BailoutToCSR",{enumerable:!0,get:function(){return o}});let n=r(94129);function o(e){let{reason:t,children:r}=e;throw new n.BailoutToCSRError(t)}},77028:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return c}});let n=r(10326),o=r(17577),i=r(933),s=r(46618);function a(e){return{default:e&&"default"in e?e.default:e}}let l={loader:()=>Promise.resolve(a(()=>null)),loading:null,ssr:!0},c=function(e){let t={...l,...e},r=(0,o.lazy)(()=>t.loader().then(a)),c=t.loading;function u(e){let a=c?(0,n.jsx)(c,{isLoading:!0,pastDelay:!0,error:null}):null,l=t.ssr?(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(s.PreloadCss,{moduleIds:t.modules}),(0,n.jsx)(r,{...e})]}):(0,n.jsx)(i.BailoutToCSR,{reason:"next/dynamic",children:(0,n.jsx)(r,{...e})});return(0,n.jsx)(o.Suspense,{fallback:a,children:l})}return u.displayName="LoadableComponent",u}},46618:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"PreloadCss",{enumerable:!0,get:function(){return i}});let n=r(10326),o=r(54580);function i(e){let{moduleIds:t}=e,r=(0,o.getExpectedRequestStore)("next/dynamic css"),i=[];if(r.reactLoadableManifest&&t){let e=r.reactLoadableManifest;for(let r of t){if(!e[r])continue;let t=e[r].files.filter(e=>e.endsWith(".css"));i.push(...t)}}return 0===i.length?null:(0,n.jsx)(n.Fragment,{children:i.map(e=>(0,n.jsx)("link",{precedence:"dynamic",rel:"stylesheet",href:r.assetPrefix+"/_next/"+encodeURI(e),as:"style"},e))})}},68384:(e,t,r)=>{"use strict";let n,o;r.r(t),r.d(t,{default:()=>nS});var i,s=r(10326),a=r(33265),l=r(17577),c=r.t(l,2);function u(e,t,{checkForDefaultPrevented:r=!0}={}){return function(n){if(e?.(n),!1===r||!n.defaultPrevented)return t?.(n)}}function d(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function f(...e){return t=>{let r=!1,n=e.map(e=>{let n=d(e,t);return r||"function"!=typeof n||(r=!0),n});if(r)return()=>{for(let t=0;t<n.length;t++){let r=n[t];"function"==typeof r?r():d(e[t],null)}}}}function p(...e){return l.useCallback(f(...e),e)}function m(e,t=[]){let r=[],n=()=>{let t=r.map(e=>l.createContext(e));return function(r){let n=r?.[e]||t;return l.useMemo(()=>({[`__scope${e}`]:{...r,[e]:n}}),[r,n])}};return n.scopeName=e,[function(t,n){let o=l.createContext(n),i=r.length;r=[...r,n];let a=t=>{let{scope:r,children:n,...a}=t,c=r?.[e]?.[i]||o,u=l.useMemo(()=>a,Object.values(a));return(0,s.jsx)(c.Provider,{value:u,children:n})};return a.displayName=t+"Provider",[a,function(r,s){let a=s?.[e]?.[i]||o,c=l.useContext(a);if(c)return c;if(void 0!==n)return n;throw Error(`\`${r}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let r=()=>{let r=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let n=r.reduce((t,{useScope:r,scopeName:n})=>{let o=r(e)[`__scope${n}`];return{...t,...o}},{});return l.useMemo(()=>({[`__scope${t.scopeName}`]:n}),[n])}};return r.scopeName=t.scopeName,r}(n,...t)]}var g=r(60962);function h(e){let t=function(e){let t=l.forwardRef((e,t)=>{let{children:r,...n}=e;if(l.isValidElement(r)){let e,o;let i=(e=Object.getOwnPropertyDescriptor(r.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?r.ref:(e=Object.getOwnPropertyDescriptor(r,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?r.props.ref:r.props.ref||r.ref,s=function(e,t){let r={...t};for(let n in t){let o=e[n],i=t[n];/^on[A-Z]/.test(n)?o&&i?r[n]=(...e)=>{let t=i(...e);return o(...e),t}:o&&(r[n]=o):"style"===n?r[n]={...o,...i}:"className"===n&&(r[n]=[o,i].filter(Boolean).join(" "))}return{...e,...r}}(n,r.props);return r.type!==l.Fragment&&(s.ref=t?f(t,i):i),l.cloneElement(r,s)}return l.Children.count(r)>1?l.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),r=l.forwardRef((e,r)=>{let{children:n,...o}=e,i=l.Children.toArray(n),a=i.find(x);if(a){let e=a.props.children,n=i.map(t=>t!==a?t:l.Children.count(e)>1?l.Children.only(null):l.isValidElement(e)?e.props.children:null);return(0,s.jsx)(t,{...o,ref:r,children:l.isValidElement(e)?l.cloneElement(e,void 0,n):null})}return(0,s.jsx)(t,{...o,ref:r,children:n})});return r.displayName=`${e}.Slot`,r}var b=Symbol("radix.slottable");function x(e){return l.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===b}var v=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let r=h(`Primitive.${t}`),n=l.forwardRef((e,n)=>{let{asChild:o,...i}=e,a=o?r:t;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,s.jsx)(a,{...i,ref:n})});return n.displayName=`Primitive.${t}`,{...e,[t]:n}},{});function y(e,t){e&&g.flushSync(()=>e.dispatchEvent(t))}function w(e){let t=l.useRef(e);return l.useEffect(()=>{t.current=e}),l.useMemo(()=>(...e)=>t.current?.(...e),[])}var k="dismissableLayer.update",E=l.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),T=l.forwardRef((e,t)=>{let{disableOutsidePointerEvents:r=!1,onEscapeKeyDown:n,onPointerDownOutside:o,onFocusOutside:a,onInteractOutside:c,onDismiss:d,...f}=e,m=l.useContext(E),[g,h]=l.useState(null),b=g?.ownerDocument??globalThis?.document,[,x]=l.useState({}),y=p(t,e=>h(e)),T=Array.from(m.layers),[R]=[...m.layersWithOutsidePointerEventsDisabled].slice(-1),C=T.indexOf(R),N=g?T.indexOf(g):-1,S=m.layersWithOutsidePointerEventsDisabled.size>0,A=N>=C,L=function(e,t=globalThis?.document){let r=w(e),n=l.useRef(!1),o=l.useRef(()=>{});return l.useEffect(()=>{let e=e=>{if(e.target&&!n.current){let n=function(){j("dismissableLayer.pointerDownOutside",r,i,{discrete:!0})},i={originalEvent:e};"touch"===e.pointerType?(t.removeEventListener("click",o.current),o.current=n,t.addEventListener("click",o.current,{once:!0})):n()}else t.removeEventListener("click",o.current);n.current=!1},i=window.setTimeout(()=>{t.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(i),t.removeEventListener("pointerdown",e),t.removeEventListener("click",o.current)}},[t,r]),{onPointerDownCapture:()=>n.current=!0}}(e=>{let t=e.target,r=[...m.branches].some(e=>e.contains(t));!A||r||(o?.(e),c?.(e),e.defaultPrevented||d?.())},b),O=function(e,t=globalThis?.document){let r=w(e),n=l.useRef(!1);return l.useEffect(()=>{let e=e=>{e.target&&!n.current&&j("dismissableLayer.focusOutside",r,{originalEvent:e},{discrete:!1})};return t.addEventListener("focusin",e),()=>t.removeEventListener("focusin",e)},[t,r]),{onFocusCapture:()=>n.current=!0,onBlurCapture:()=>n.current=!1}}(e=>{let t=e.target;[...m.branches].some(e=>e.contains(t))||(a?.(e),c?.(e),e.defaultPrevented||d?.())},b);return function(e,t=globalThis?.document){let r=w(e);l.useEffect(()=>{let e=e=>{"Escape"===e.key&&r(e)};return t.addEventListener("keydown",e,{capture:!0}),()=>t.removeEventListener("keydown",e,{capture:!0})},[r,t])}(e=>{N!==m.layers.size-1||(n?.(e),!e.defaultPrevented&&d&&(e.preventDefault(),d()))},b),l.useEffect(()=>{if(g)return r&&(0===m.layersWithOutsidePointerEventsDisabled.size&&(i=b.body.style.pointerEvents,b.body.style.pointerEvents="none"),m.layersWithOutsidePointerEventsDisabled.add(g)),m.layers.add(g),P(),()=>{r&&1===m.layersWithOutsidePointerEventsDisabled.size&&(b.body.style.pointerEvents=i)}},[g,b,r,m]),l.useEffect(()=>()=>{g&&(m.layers.delete(g),m.layersWithOutsidePointerEventsDisabled.delete(g),P())},[g,m]),l.useEffect(()=>{let e=()=>x({});return document.addEventListener(k,e),()=>document.removeEventListener(k,e)},[]),(0,s.jsx)(v.div,{...f,ref:y,style:{pointerEvents:S?A?"auto":"none":void 0,...e.style},onFocusCapture:u(e.onFocusCapture,O.onFocusCapture),onBlurCapture:u(e.onBlurCapture,O.onBlurCapture),onPointerDownCapture:u(e.onPointerDownCapture,L.onPointerDownCapture)})});T.displayName="DismissableLayer";var R=l.forwardRef((e,t)=>{let r=l.useContext(E),n=l.useRef(null),o=p(t,n);return l.useEffect(()=>{let e=n.current;if(e)return r.branches.add(e),()=>{r.branches.delete(e)}},[r.branches]),(0,s.jsx)(v.div,{...e,ref:o})});function P(){let e=new CustomEvent(k);document.dispatchEvent(e)}function j(e,t,r,{discrete:n}){let o=r.originalEvent.target,i=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:r});t&&o.addEventListener(e,t,{once:!0}),n?y(o,i):o.dispatchEvent(i)}R.displayName="DismissableLayerBranch";var C=globalThis?.document?l.useLayoutEffect:()=>{};c[" useId ".trim().toString()]||(()=>void 0);let N=["top","right","bottom","left"],S=Math.min,A=Math.max,L=Math.round,O=Math.floor,M=e=>({x:e,y:e}),_={left:"right",right:"left",bottom:"top",top:"bottom"},D={start:"end",end:"start"};function z(e,t){return"function"==typeof e?e(t):e}function I(e){return e.split("-")[0]}function F(e){return e.split("-")[1]}function q(e){return"x"===e?"y":"x"}function $(e){return"y"===e?"height":"width"}let W=new Set(["top","bottom"]);function B(e){return W.has(I(e))?"y":"x"}function H(e){return e.replace(/start|end/g,e=>D[e])}let V=["left","right"],G=["right","left"],U=["top","bottom"],K=["bottom","top"];function X(e){return e.replace(/left|right|bottom|top/g,e=>_[e])}function Y(e){return"number"!=typeof e?{top:0,right:0,bottom:0,left:0,...e}:{top:e,right:e,bottom:e,left:e}}function Z(e){let{x:t,y:r,width:n,height:o}=e;return{width:n,height:o,top:r,left:t,right:t+n,bottom:r+o,x:t,y:r}}function J(e,t,r){let n,{reference:o,floating:i}=e,s=B(t),a=q(B(t)),l=$(a),c=I(t),u="y"===s,d=o.x+o.width/2-i.width/2,f=o.y+o.height/2-i.height/2,p=o[l]/2-i[l]/2;switch(c){case"top":n={x:d,y:o.y-i.height};break;case"bottom":n={x:d,y:o.y+o.height};break;case"right":n={x:o.x+o.width,y:f};break;case"left":n={x:o.x-i.width,y:f};break;default:n={x:o.x,y:o.y}}switch(F(t)){case"start":n[a]-=p*(r&&u?-1:1);break;case"end":n[a]+=p*(r&&u?-1:1)}return n}let Q=async(e,t,r)=>{let{placement:n="bottom",strategy:o="absolute",middleware:i=[],platform:s}=r,a=i.filter(Boolean),l=await (null==s.isRTL?void 0:s.isRTL(t)),c=await s.getElementRects({reference:e,floating:t,strategy:o}),{x:u,y:d}=J(c,n,l),f=n,p={},m=0;for(let r=0;r<a.length;r++){let{name:i,fn:g}=a[r],{x:h,y:b,data:x,reset:v}=await g({x:u,y:d,initialPlacement:n,placement:f,strategy:o,middlewareData:p,rects:c,platform:s,elements:{reference:e,floating:t}});u=null!=h?h:u,d=null!=b?b:d,p={...p,[i]:{...p[i],...x}},v&&m<=50&&(m++,"object"==typeof v&&(v.placement&&(f=v.placement),v.rects&&(c=!0===v.rects?await s.getElementRects({reference:e,floating:t,strategy:o}):v.rects),{x:u,y:d}=J(c,f,l)),r=-1)}return{x:u,y:d,placement:f,strategy:o,middlewareData:p}};async function ee(e,t){var r;void 0===t&&(t={});let{x:n,y:o,platform:i,rects:s,elements:a,strategy:l}=e,{boundary:c="clippingAncestors",rootBoundary:u="viewport",elementContext:d="floating",altBoundary:f=!1,padding:p=0}=z(t,e),m=Y(p),g=a[f?"floating"===d?"reference":"floating":d],h=Z(await i.getClippingRect({element:null==(r=await (null==i.isElement?void 0:i.isElement(g)))||r?g:g.contextElement||await (null==i.getDocumentElement?void 0:i.getDocumentElement(a.floating)),boundary:c,rootBoundary:u,strategy:l})),b="floating"===d?{x:n,y:o,width:s.floating.width,height:s.floating.height}:s.reference,x=await (null==i.getOffsetParent?void 0:i.getOffsetParent(a.floating)),v=await (null==i.isElement?void 0:i.isElement(x))&&await (null==i.getScale?void 0:i.getScale(x))||{x:1,y:1},y=Z(i.convertOffsetParentRelativeRectToViewportRelativeRect?await i.convertOffsetParentRelativeRectToViewportRelativeRect({elements:a,rect:b,offsetParent:x,strategy:l}):b);return{top:(h.top-y.top+m.top)/v.y,bottom:(y.bottom-h.bottom+m.bottom)/v.y,left:(h.left-y.left+m.left)/v.x,right:(y.right-h.right+m.right)/v.x}}function et(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function er(e){return N.some(t=>e[t]>=0)}let en=new Set(["left","top"]);async function eo(e,t){let{placement:r,platform:n,elements:o}=e,i=await (null==n.isRTL?void 0:n.isRTL(o.floating)),s=I(r),a=F(r),l="y"===B(r),c=en.has(s)?-1:1,u=i&&l?-1:1,d=z(t,e),{mainAxis:f,crossAxis:p,alignmentAxis:m}="number"==typeof d?{mainAxis:d,crossAxis:0,alignmentAxis:null}:{mainAxis:d.mainAxis||0,crossAxis:d.crossAxis||0,alignmentAxis:d.alignmentAxis};return a&&"number"==typeof m&&(p="end"===a?-1*m:m),l?{x:p*u,y:f*c}:{x:f*c,y:p*u}}function ei(){return"undefined"!=typeof window}function es(e){return ec(e)?(e.nodeName||"").toLowerCase():"#document"}function ea(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function el(e){var t;return null==(t=(ec(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function ec(e){return!!ei()&&(e instanceof Node||e instanceof ea(e).Node)}function eu(e){return!!ei()&&(e instanceof Element||e instanceof ea(e).Element)}function ed(e){return!!ei()&&(e instanceof HTMLElement||e instanceof ea(e).HTMLElement)}function ef(e){return!!ei()&&"undefined"!=typeof ShadowRoot&&(e instanceof ShadowRoot||e instanceof ea(e).ShadowRoot)}let ep=new Set(["inline","contents"]);function em(e){let{overflow:t,overflowX:r,overflowY:n,display:o}=eR(e);return/auto|scroll|overlay|hidden|clip/.test(t+n+r)&&!ep.has(o)}let eg=new Set(["table","td","th"]),eh=[":popover-open",":modal"];function eb(e){return eh.some(t=>{try{return e.matches(t)}catch(e){return!1}})}let ex=["transform","translate","scale","rotate","perspective"],ev=["transform","translate","scale","rotate","perspective","filter"],ey=["paint","layout","strict","content"];function ew(e){let t=ek(),r=eu(e)?eR(e):e;return ex.some(e=>!!r[e]&&"none"!==r[e])||!!r.containerType&&"normal"!==r.containerType||!t&&!!r.backdropFilter&&"none"!==r.backdropFilter||!t&&!!r.filter&&"none"!==r.filter||ev.some(e=>(r.willChange||"").includes(e))||ey.some(e=>(r.contain||"").includes(e))}function ek(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}let eE=new Set(["html","body","#document"]);function eT(e){return eE.has(es(e))}function eR(e){return ea(e).getComputedStyle(e)}function eP(e){return eu(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function ej(e){if("html"===es(e))return e;let t=e.assignedSlot||e.parentNode||ef(e)&&e.host||el(e);return ef(t)?t.host:t}function eC(e,t,r){var n;void 0===t&&(t=[]),void 0===r&&(r=!0);let o=function e(t){let r=ej(t);return eT(r)?t.ownerDocument?t.ownerDocument.body:t.body:ed(r)&&em(r)?r:e(r)}(e),i=o===(null==(n=e.ownerDocument)?void 0:n.body),s=ea(o);if(i){let e=eN(s);return t.concat(s,s.visualViewport||[],em(o)?o:[],e&&r?eC(e):[])}return t.concat(o,eC(o,[],r))}function eN(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function eS(e){let t=eR(e),r=parseFloat(t.width)||0,n=parseFloat(t.height)||0,o=ed(e),i=o?e.offsetWidth:r,s=o?e.offsetHeight:n,a=L(r)!==i||L(n)!==s;return a&&(r=i,n=s),{width:r,height:n,$:a}}function eA(e){return eu(e)?e:e.contextElement}function eL(e){let t=eA(e);if(!ed(t))return M(1);let r=t.getBoundingClientRect(),{width:n,height:o,$:i}=eS(t),s=(i?L(r.width):r.width)/n,a=(i?L(r.height):r.height)/o;return s&&Number.isFinite(s)||(s=1),a&&Number.isFinite(a)||(a=1),{x:s,y:a}}let eO=M(0);function eM(e){let t=ea(e);return ek()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:eO}function e_(e,t,r,n){var o;void 0===t&&(t=!1),void 0===r&&(r=!1);let i=e.getBoundingClientRect(),s=eA(e),a=M(1);t&&(n?eu(n)&&(a=eL(n)):a=eL(e));let l=(void 0===(o=r)&&(o=!1),n&&(!o||n===ea(s))&&o)?eM(s):M(0),c=(i.left+l.x)/a.x,u=(i.top+l.y)/a.y,d=i.width/a.x,f=i.height/a.y;if(s){let e=ea(s),t=n&&eu(n)?ea(n):n,r=e,o=eN(r);for(;o&&n&&t!==r;){let e=eL(o),t=o.getBoundingClientRect(),n=eR(o),i=t.left+(o.clientLeft+parseFloat(n.paddingLeft))*e.x,s=t.top+(o.clientTop+parseFloat(n.paddingTop))*e.y;c*=e.x,u*=e.y,d*=e.x,f*=e.y,c+=i,u+=s,o=eN(r=ea(o))}}return Z({width:d,height:f,x:c,y:u})}function eD(e,t){let r=eP(e).scrollLeft;return t?t.left+r:e_(el(e)).left+r}function ez(e,t,r){void 0===r&&(r=!1);let n=e.getBoundingClientRect();return{x:n.left+t.scrollLeft-(r?0:eD(e,n)),y:n.top+t.scrollTop}}let eI=new Set(["absolute","fixed"]);function eF(e,t,r){let n;if("viewport"===t)n=function(e,t){let r=ea(e),n=el(e),o=r.visualViewport,i=n.clientWidth,s=n.clientHeight,a=0,l=0;if(o){i=o.width,s=o.height;let e=ek();(!e||e&&"fixed"===t)&&(a=o.offsetLeft,l=o.offsetTop)}return{width:i,height:s,x:a,y:l}}(e,r);else if("document"===t)n=function(e){let t=el(e),r=eP(e),n=e.ownerDocument.body,o=A(t.scrollWidth,t.clientWidth,n.scrollWidth,n.clientWidth),i=A(t.scrollHeight,t.clientHeight,n.scrollHeight,n.clientHeight),s=-r.scrollLeft+eD(e),a=-r.scrollTop;return"rtl"===eR(n).direction&&(s+=A(t.clientWidth,n.clientWidth)-o),{width:o,height:i,x:s,y:a}}(el(e));else if(eu(t))n=function(e,t){let r=e_(e,!0,"fixed"===t),n=r.top+e.clientTop,o=r.left+e.clientLeft,i=ed(e)?eL(e):M(1),s=e.clientWidth*i.x;return{width:s,height:e.clientHeight*i.y,x:o*i.x,y:n*i.y}}(t,r);else{let r=eM(e);n={x:t.x-r.x,y:t.y-r.y,width:t.width,height:t.height}}return Z(n)}function eq(e){return"static"===eR(e).position}function e$(e,t){if(!ed(e)||"fixed"===eR(e).position)return null;if(t)return t(e);let r=e.offsetParent;return el(e)===r&&(r=r.ownerDocument.body),r}function eW(e,t){var r;let n=ea(e);if(eb(e))return n;if(!ed(e)){let t=ej(e);for(;t&&!eT(t);){if(eu(t)&&!eq(t))return t;t=ej(t)}return n}let o=e$(e,t);for(;o&&(r=o,eg.has(es(r)))&&eq(o);)o=e$(o,t);return o&&eT(o)&&eq(o)&&!ew(o)?n:o||function(e){let t=ej(e);for(;ed(t)&&!eT(t);){if(ew(t))return t;if(eb(t))break;t=ej(t)}return null}(e)||n}let eB=async function(e){let t=this.getOffsetParent||eW,r=this.getDimensions,n=await r(e.floating);return{reference:function(e,t,r){let n=ed(t),o=el(t),i="fixed"===r,s=e_(e,!0,i,t),a={scrollLeft:0,scrollTop:0},l=M(0);if(n||!n&&!i){if(("body"!==es(t)||em(o))&&(a=eP(t)),n){let e=e_(t,!0,i,t);l.x=e.x+t.clientLeft,l.y=e.y+t.clientTop}else o&&(l.x=eD(o))}i&&!n&&o&&(l.x=eD(o));let c=!o||n||i?M(0):ez(o,a);return{x:s.left+a.scrollLeft-l.x-c.x,y:s.top+a.scrollTop-l.y-c.y,width:s.width,height:s.height}}(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:n.width,height:n.height}}},eH={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{elements:t,rect:r,offsetParent:n,strategy:o}=e,i="fixed"===o,s=el(n),a=!!t&&eb(t.floating);if(n===s||a&&i)return r;let l={scrollLeft:0,scrollTop:0},c=M(1),u=M(0),d=ed(n);if((d||!d&&!i)&&(("body"!==es(n)||em(s))&&(l=eP(n)),ed(n))){let e=e_(n);c=eL(n),u.x=e.x+n.clientLeft,u.y=e.y+n.clientTop}let f=!s||d||i?M(0):ez(s,l,!0);return{width:r.width*c.x,height:r.height*c.y,x:r.x*c.x-l.scrollLeft*c.x+u.x+f.x,y:r.y*c.y-l.scrollTop*c.y+u.y+f.y}},getDocumentElement:el,getClippingRect:function(e){let{element:t,boundary:r,rootBoundary:n,strategy:o}=e,i=[..."clippingAncestors"===r?eb(t)?[]:function(e,t){let r=t.get(e);if(r)return r;let n=eC(e,[],!1).filter(e=>eu(e)&&"body"!==es(e)),o=null,i="fixed"===eR(e).position,s=i?ej(e):e;for(;eu(s)&&!eT(s);){let t=eR(s),r=ew(s);r||"fixed"!==t.position||(o=null),(i?!r&&!o:!r&&"static"===t.position&&!!o&&eI.has(o.position)||em(s)&&!r&&function e(t,r){let n=ej(t);return!(n===r||!eu(n)||eT(n))&&("fixed"===eR(n).position||e(n,r))}(e,s))?n=n.filter(e=>e!==s):o=t,s=ej(s)}return t.set(e,n),n}(t,this._c):[].concat(r),n],s=i[0],a=i.reduce((e,r)=>{let n=eF(t,r,o);return e.top=A(n.top,e.top),e.right=S(n.right,e.right),e.bottom=S(n.bottom,e.bottom),e.left=A(n.left,e.left),e},eF(t,s,o));return{width:a.right-a.left,height:a.bottom-a.top,x:a.left,y:a.top}},getOffsetParent:eW,getElementRects:eB,getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){let{width:t,height:r}=eS(e);return{width:t,height:r}},getScale:eL,isElement:eu,isRTL:function(e){return"rtl"===eR(e).direction}};function eV(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}let eG=e=>({name:"arrow",options:e,async fn(t){let{x:r,y:n,placement:o,rects:i,platform:s,elements:a,middlewareData:l}=t,{element:c,padding:u=0}=z(e,t)||{};if(null==c)return{};let d=Y(u),f={x:r,y:n},p=q(B(o)),m=$(p),g=await s.getDimensions(c),h="y"===p,b=h?"clientHeight":"clientWidth",x=i.reference[m]+i.reference[p]-f[p]-i.floating[m],v=f[p]-i.reference[p],y=await (null==s.getOffsetParent?void 0:s.getOffsetParent(c)),w=y?y[b]:0;w&&await (null==s.isElement?void 0:s.isElement(y))||(w=a.floating[b]||i.floating[m]);let k=w/2-g[m]/2-1,E=S(d[h?"top":"left"],k),T=S(d[h?"bottom":"right"],k),R=w-g[m]-T,P=w/2-g[m]/2+(x/2-v/2),j=A(E,S(P,R)),C=!l.arrow&&null!=F(o)&&P!==j&&i.reference[m]/2-(P<E?E:T)-g[m]/2<0,N=C?P<E?P-E:P-R:0;return{[p]:f[p]+N,data:{[p]:j,centerOffset:P-j-N,...C&&{alignmentOffset:N}},reset:C}}}),eU=(e,t,r)=>{let n=new Map,o={platform:eH,...r},i={...o.platform,_c:n};return Q(e,t,{...o,platform:i})};var eK="undefined"!=typeof document?l.useLayoutEffect:function(){};function eX(e,t){let r,n,o;if(e===t)return!0;if(typeof e!=typeof t)return!1;if("function"==typeof e&&e.toString()===t.toString())return!0;if(e&&t&&"object"==typeof e){if(Array.isArray(e)){if((r=e.length)!==t.length)return!1;for(n=r;0!=n--;)if(!eX(e[n],t[n]))return!1;return!0}if((r=(o=Object.keys(e)).length)!==Object.keys(t).length)return!1;for(n=r;0!=n--;)if(!({}).hasOwnProperty.call(t,o[n]))return!1;for(n=r;0!=n--;){let r=o[n];if(("_owner"!==r||!e.$$typeof)&&!eX(e[r],t[r]))return!1}return!0}return e!=e&&t!=t}function eY(e){return"undefined"==typeof window?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function eZ(e,t){let r=eY(e);return Math.round(t*r)/r}function eJ(e){let t=l.useRef(e);return eK(()=>{t.current=e}),t}let eQ=e=>({name:"arrow",options:e,fn(t){let{element:r,padding:n}="function"==typeof e?e(t):e;return r&&({}).hasOwnProperty.call(r,"current")?null!=r.current?eG({element:r.current,padding:n}).fn(t):{}:r?eG({element:r,padding:n}).fn(t):{}}}),e0=(e,t)=>({...function(e){return void 0===e&&(e=0),{name:"offset",options:e,async fn(t){var r,n;let{x:o,y:i,placement:s,middlewareData:a}=t,l=await eo(t,e);return s===(null==(r=a.offset)?void 0:r.placement)&&null!=(n=a.arrow)&&n.alignmentOffset?{}:{x:o+l.x,y:i+l.y,data:{...l,placement:s}}}}}(e),options:[e,t]}),e1=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"shift",options:e,async fn(t){let{x:r,y:n,placement:o}=t,{mainAxis:i=!0,crossAxis:s=!1,limiter:a={fn:e=>{let{x:t,y:r}=e;return{x:t,y:r}}},...l}=z(e,t),c={x:r,y:n},u=await ee(t,l),d=B(I(o)),f=q(d),p=c[f],m=c[d];if(i){let e="y"===f?"top":"left",t="y"===f?"bottom":"right",r=p+u[e],n=p-u[t];p=A(r,S(p,n))}if(s){let e="y"===d?"top":"left",t="y"===d?"bottom":"right",r=m+u[e],n=m-u[t];m=A(r,S(m,n))}let g=a.fn({...t,[f]:p,[d]:m});return{...g,data:{x:g.x-r,y:g.y-n,enabled:{[f]:i,[d]:s}}}}}}(e),options:[e,t]}),e2=(e,t)=>({...function(e){return void 0===e&&(e={}),{options:e,fn(t){let{x:r,y:n,placement:o,rects:i,middlewareData:s}=t,{offset:a=0,mainAxis:l=!0,crossAxis:c=!0}=z(e,t),u={x:r,y:n},d=B(o),f=q(d),p=u[f],m=u[d],g=z(a,t),h="number"==typeof g?{mainAxis:g,crossAxis:0}:{mainAxis:0,crossAxis:0,...g};if(l){let e="y"===f?"height":"width",t=i.reference[f]-i.floating[e]+h.mainAxis,r=i.reference[f]+i.reference[e]-h.mainAxis;p<t?p=t:p>r&&(p=r)}if(c){var b,x;let e="y"===f?"width":"height",t=en.has(I(o)),r=i.reference[d]-i.floating[e]+(t&&(null==(b=s.offset)?void 0:b[d])||0)+(t?0:h.crossAxis),n=i.reference[d]+i.reference[e]+(t?0:(null==(x=s.offset)?void 0:x[d])||0)-(t?h.crossAxis:0);m<r?m=r:m>n&&(m=n)}return{[f]:p,[d]:m}}}}(e),options:[e,t]}),e6=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"flip",options:e,async fn(t){var r,n,o,i,s;let{placement:a,middlewareData:l,rects:c,initialPlacement:u,platform:d,elements:f}=t,{mainAxis:p=!0,crossAxis:m=!0,fallbackPlacements:g,fallbackStrategy:h="bestFit",fallbackAxisSideDirection:b="none",flipAlignment:x=!0,...v}=z(e,t);if(null!=(r=l.arrow)&&r.alignmentOffset)return{};let y=I(a),w=B(u),k=I(u)===u,E=await (null==d.isRTL?void 0:d.isRTL(f.floating)),T=g||(k||!x?[X(u)]:function(e){let t=X(e);return[H(e),t,H(t)]}(u)),R="none"!==b;!g&&R&&T.push(...function(e,t,r,n){let o=F(e),i=function(e,t,r){switch(e){case"top":case"bottom":if(r)return t?G:V;return t?V:G;case"left":case"right":return t?U:K;default:return[]}}(I(e),"start"===r,n);return o&&(i=i.map(e=>e+"-"+o),t&&(i=i.concat(i.map(H)))),i}(u,x,b,E));let P=[u,...T],j=await ee(t,v),C=[],N=(null==(n=l.flip)?void 0:n.overflows)||[];if(p&&C.push(j[y]),m){let e=function(e,t,r){void 0===r&&(r=!1);let n=F(e),o=q(B(e)),i=$(o),s="x"===o?n===(r?"end":"start")?"right":"left":"start"===n?"bottom":"top";return t.reference[i]>t.floating[i]&&(s=X(s)),[s,X(s)]}(a,c,E);C.push(j[e[0]],j[e[1]])}if(N=[...N,{placement:a,overflows:C}],!C.every(e=>e<=0)){let e=((null==(o=l.flip)?void 0:o.index)||0)+1,t=P[e];if(t&&(!("alignment"===m&&w!==B(t))||N.every(e=>e.overflows[0]>0&&B(e.placement)===w)))return{data:{index:e,overflows:N},reset:{placement:t}};let r=null==(i=N.filter(e=>e.overflows[0]<=0).sort((e,t)=>e.overflows[1]-t.overflows[1])[0])?void 0:i.placement;if(!r)switch(h){case"bestFit":{let e=null==(s=N.filter(e=>{if(R){let t=B(e.placement);return t===w||"y"===t}return!0}).map(e=>[e.placement,e.overflows.filter(e=>e>0).reduce((e,t)=>e+t,0)]).sort((e,t)=>e[1]-t[1])[0])?void 0:s[0];e&&(r=e);break}case"initialPlacement":r=u}if(a!==r)return{reset:{placement:r}}}return{}}}}(e),options:[e,t]}),e3=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"size",options:e,async fn(t){var r,n;let o,i;let{placement:s,rects:a,platform:l,elements:c}=t,{apply:u=()=>{},...d}=z(e,t),f=await ee(t,d),p=I(s),m=F(s),g="y"===B(s),{width:h,height:b}=a.floating;"top"===p||"bottom"===p?(o=p,i=m===(await (null==l.isRTL?void 0:l.isRTL(c.floating))?"start":"end")?"left":"right"):(i=p,o="end"===m?"top":"bottom");let x=b-f.top-f.bottom,v=h-f.left-f.right,y=S(b-f[o],x),w=S(h-f[i],v),k=!t.middlewareData.shift,E=y,T=w;if(null!=(r=t.middlewareData.shift)&&r.enabled.x&&(T=v),null!=(n=t.middlewareData.shift)&&n.enabled.y&&(E=x),k&&!m){let e=A(f.left,0),t=A(f.right,0),r=A(f.top,0),n=A(f.bottom,0);g?T=h-2*(0!==e||0!==t?e+t:A(f.left,f.right)):E=b-2*(0!==r||0!==n?r+n:A(f.top,f.bottom))}await u({...t,availableWidth:T,availableHeight:E});let R=await l.getDimensions(c.floating);return h!==R.width||b!==R.height?{reset:{rects:!0}}:{}}}}(e),options:[e,t]}),e5=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"hide",options:e,async fn(t){let{rects:r}=t,{strategy:n="referenceHidden",...o}=z(e,t);switch(n){case"referenceHidden":{let e=et(await ee(t,{...o,elementContext:"reference"}),r.reference);return{data:{referenceHiddenOffsets:e,referenceHidden:er(e)}}}case"escaped":{let e=et(await ee(t,{...o,altBoundary:!0}),r.floating);return{data:{escapedOffsets:e,escaped:er(e)}}}default:return{}}}}}(e),options:[e,t]}),e8=(e,t)=>({...eQ(e),options:[e,t]});var e4=l.forwardRef((e,t)=>{let{children:r,width:n=10,height:o=5,...i}=e;return(0,s.jsx)(v.svg,{...i,ref:t,width:n,height:o,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?r:(0,s.jsx)("polygon",{points:"0,0 30,0 15,10"})})});e4.displayName="Arrow";var e7="Popper",[e9,te]=m(e7),[tt,tr]=e9(e7),tn="PopperAnchor",to=l.forwardRef((e,t)=>{let{__scopePopper:r,virtualRef:n,...o}=e,i=tr(tn,r),a=l.useRef(null),c=p(t,a);return l.useEffect(()=>{i.onAnchorChange(n?.current||a.current)}),n?null:(0,s.jsx)(v.div,{...o,ref:c})});to.displayName=tn;var ti="PopperContent",[ts,ta]=e9(ti),tl=l.forwardRef((e,t)=>{let{__scopePopper:r,side:n="bottom",sideOffset:o=0,align:i="center",alignOffset:a=0,arrowPadding:c=0,avoidCollisions:u=!0,collisionBoundary:d=[],collisionPadding:f=0,sticky:m="partial",hideWhenDetached:h=!1,updatePositionStrategy:b="optimized",onPlaced:x,...y}=e,k=tr(ti,r),[E,T]=l.useState(null),R=p(t,e=>T(e)),[P,j]=l.useState(null),N=function(e){let[t,r]=l.useState(void 0);return C(()=>{if(e){r({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let n,o;if(!Array.isArray(t)||!t.length)return;let i=t[0];if("borderBoxSize"in i){let e=i.borderBoxSize,t=Array.isArray(e)?e[0]:e;n=t.inlineSize,o=t.blockSize}else n=e.offsetWidth,o=e.offsetHeight;r({width:n,height:o})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}r(void 0)},[e]),t}(P),L=N?.width??0,M=N?.height??0,_="number"==typeof f?f:{top:0,right:0,bottom:0,left:0,...f},D=Array.isArray(d)?d:[d],z=D.length>0,I={padding:_,boundary:D.filter(tf),altBoundary:z},{refs:F,floatingStyles:q,placement:$,isPositioned:W,middlewareData:B}=function(e){void 0===e&&(e={});let{placement:t="bottom",strategy:r="absolute",middleware:n=[],platform:o,elements:{reference:i,floating:s}={},transform:a=!0,whileElementsMounted:c,open:u}=e,[d,f]=l.useState({x:0,y:0,strategy:r,placement:t,middlewareData:{},isPositioned:!1}),[p,m]=l.useState(n);eX(p,n)||m(n);let[h,b]=l.useState(null),[x,v]=l.useState(null),y=l.useCallback(e=>{e!==T.current&&(T.current=e,b(e))},[]),w=l.useCallback(e=>{e!==R.current&&(R.current=e,v(e))},[]),k=i||h,E=s||x,T=l.useRef(null),R=l.useRef(null),P=l.useRef(d),j=null!=c,C=eJ(c),N=eJ(o),S=eJ(u),A=l.useCallback(()=>{if(!T.current||!R.current)return;let e={placement:t,strategy:r,middleware:p};N.current&&(e.platform=N.current),eU(T.current,R.current,e).then(e=>{let t={...e,isPositioned:!1!==S.current};L.current&&!eX(P.current,t)&&(P.current=t,g.flushSync(()=>{f(t)}))})},[p,t,r,N,S]);eK(()=>{!1===u&&P.current.isPositioned&&(P.current.isPositioned=!1,f(e=>({...e,isPositioned:!1})))},[u]);let L=l.useRef(!1);eK(()=>(L.current=!0,()=>{L.current=!1}),[]),eK(()=>{if(k&&(T.current=k),E&&(R.current=E),k&&E){if(C.current)return C.current(k,E,A);A()}},[k,E,A,C,j]);let O=l.useMemo(()=>({reference:T,floating:R,setReference:y,setFloating:w}),[y,w]),M=l.useMemo(()=>({reference:k,floating:E}),[k,E]),_=l.useMemo(()=>{let e={position:r,left:0,top:0};if(!M.floating)return e;let t=eZ(M.floating,d.x),n=eZ(M.floating,d.y);return a?{...e,transform:"translate("+t+"px, "+n+"px)",...eY(M.floating)>=1.5&&{willChange:"transform"}}:{position:r,left:t,top:n}},[r,a,M.floating,d.x,d.y]);return l.useMemo(()=>({...d,update:A,refs:O,elements:M,floatingStyles:_}),[d,A,O,M,_])}({strategy:"fixed",placement:n+("center"!==i?"-"+i:""),whileElementsMounted:(...e)=>(function(e,t,r,n){let o;void 0===n&&(n={});let{ancestorScroll:i=!0,ancestorResize:s=!0,elementResize:a="function"==typeof ResizeObserver,layoutShift:l="function"==typeof IntersectionObserver,animationFrame:c=!1}=n,u=eA(e),d=i||s?[...u?eC(u):[],...eC(t)]:[];d.forEach(e=>{i&&e.addEventListener("scroll",r,{passive:!0}),s&&e.addEventListener("resize",r)});let f=u&&l?function(e,t){let r,n=null,o=el(e);function i(){var e;clearTimeout(r),null==(e=n)||e.disconnect(),n=null}return function s(a,l){void 0===a&&(a=!1),void 0===l&&(l=1),i();let c=e.getBoundingClientRect(),{left:u,top:d,width:f,height:p}=c;if(a||t(),!f||!p)return;let m=O(d),g=O(o.clientWidth-(u+f)),h={rootMargin:-m+"px "+-g+"px "+-O(o.clientHeight-(d+p))+"px "+-O(u)+"px",threshold:A(0,S(1,l))||1},b=!0;function x(t){let n=t[0].intersectionRatio;if(n!==l){if(!b)return s();n?s(!1,n):r=setTimeout(()=>{s(!1,1e-7)},1e3)}1!==n||eV(c,e.getBoundingClientRect())||s(),b=!1}try{n=new IntersectionObserver(x,{...h,root:o.ownerDocument})}catch(e){n=new IntersectionObserver(x,h)}n.observe(e)}(!0),i}(u,r):null,p=-1,m=null;a&&(m=new ResizeObserver(e=>{let[n]=e;n&&n.target===u&&m&&(m.unobserve(t),cancelAnimationFrame(p),p=requestAnimationFrame(()=>{var e;null==(e=m)||e.observe(t)})),r()}),u&&!c&&m.observe(u),m.observe(t));let g=c?e_(e):null;return c&&function t(){let n=e_(e);g&&!eV(g,n)&&r(),g=n,o=requestAnimationFrame(t)}(),r(),()=>{var e;d.forEach(e=>{i&&e.removeEventListener("scroll",r),s&&e.removeEventListener("resize",r)}),null==f||f(),null==(e=m)||e.disconnect(),m=null,c&&cancelAnimationFrame(o)}})(...e,{animationFrame:"always"===b}),elements:{reference:k.anchor},middleware:[e0({mainAxis:o+M,alignmentAxis:a}),u&&e1({mainAxis:!0,crossAxis:!1,limiter:"partial"===m?e2():void 0,...I}),u&&e6({...I}),e3({...I,apply:({elements:e,rects:t,availableWidth:r,availableHeight:n})=>{let{width:o,height:i}=t.reference,s=e.floating.style;s.setProperty("--radix-popper-available-width",`${r}px`),s.setProperty("--radix-popper-available-height",`${n}px`),s.setProperty("--radix-popper-anchor-width",`${o}px`),s.setProperty("--radix-popper-anchor-height",`${i}px`)}}),P&&e8({element:P,padding:c}),tp({arrowWidth:L,arrowHeight:M}),h&&e5({strategy:"referenceHidden",...I})]}),[H,V]=tm($),G=w(x);C(()=>{W&&G?.()},[W,G]);let U=B.arrow?.x,K=B.arrow?.y,X=B.arrow?.centerOffset!==0,[Y,Z]=l.useState();return C(()=>{E&&Z(window.getComputedStyle(E).zIndex)},[E]),(0,s.jsx)("div",{ref:F.setFloating,"data-radix-popper-content-wrapper":"",style:{...q,transform:W?q.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:Y,"--radix-popper-transform-origin":[B.transformOrigin?.x,B.transformOrigin?.y].join(" "),...B.hide?.referenceHidden&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:(0,s.jsx)(ts,{scope:r,placedSide:H,onArrowChange:j,arrowX:U,arrowY:K,shouldHideArrow:X,children:(0,s.jsx)(v.div,{"data-side":H,"data-align":V,...y,ref:R,style:{...y.style,animation:W?void 0:"none"}})})})});tl.displayName=ti;var tc="PopperArrow",tu={top:"bottom",right:"left",bottom:"top",left:"right"},td=l.forwardRef(function(e,t){let{__scopePopper:r,...n}=e,o=ta(tc,r),i=tu[o.placedSide];return(0,s.jsx)("span",{ref:o.onArrowChange,style:{position:"absolute",left:o.arrowX,top:o.arrowY,[i]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[o.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[o.placedSide],visibility:o.shouldHideArrow?"hidden":void 0},children:(0,s.jsx)(e4,{...n,ref:t,style:{...n.style,display:"block"}})})});function tf(e){return null!==e}td.displayName=tc;var tp=e=>({name:"transformOrigin",options:e,fn(t){let{placement:r,rects:n,middlewareData:o}=t,i=o.arrow?.centerOffset!==0,s=i?0:e.arrowWidth,a=i?0:e.arrowHeight,[l,c]=tm(r),u={start:"0%",center:"50%",end:"100%"}[c],d=(o.arrow?.x??0)+s/2,f=(o.arrow?.y??0)+a/2,p="",m="";return"bottom"===l?(p=i?u:`${d}px`,m=`${-a}px`):"top"===l?(p=i?u:`${d}px`,m=`${n.floating.height+a}px`):"right"===l?(p=`${-a}px`,m=i?u:`${f}px`):"left"===l&&(p=`${n.floating.width+a}px`,m=i?u:`${f}px`),{data:{x:p,y:m}}}});function tm(e){let[t,r="center"]=e.split("-");return[t,r]}var tg=l.forwardRef((e,t)=>{let{container:r,...n}=e,[o,i]=l.useState(!1);C(()=>i(!0),[]);let a=r||o&&globalThis?.document?.body;return a?g.createPortal((0,s.jsx)(v.div,{...n,ref:t}),a):null});tg.displayName="Portal";var th=e=>{let{present:t,children:r}=e,n=function(e){var t,r;let[n,o]=l.useState(),i=l.useRef(null),s=l.useRef(e),a=l.useRef("none"),[c,u]=(t=e?"mounted":"unmounted",r={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},l.useReducer((e,t)=>r[e][t]??e,t));return l.useEffect(()=>{let e=tb(i.current);a.current="mounted"===c?e:"none"},[c]),C(()=>{let t=i.current,r=s.current;if(r!==e){let n=a.current,o=tb(t);e?u("MOUNT"):"none"===o||t?.display==="none"?u("UNMOUNT"):r&&n!==o?u("ANIMATION_OUT"):u("UNMOUNT"),s.current=e}},[e,u]),C(()=>{if(n){let e;let t=n.ownerDocument.defaultView??window,r=r=>{let o=tb(i.current).includes(r.animationName);if(r.target===n&&o&&(u("ANIMATION_END"),!s.current)){let r=n.style.animationFillMode;n.style.animationFillMode="forwards",e=t.setTimeout(()=>{"forwards"===n.style.animationFillMode&&(n.style.animationFillMode=r)})}},o=e=>{e.target===n&&(a.current=tb(i.current))};return n.addEventListener("animationstart",o),n.addEventListener("animationcancel",r),n.addEventListener("animationend",r),()=>{t.clearTimeout(e),n.removeEventListener("animationstart",o),n.removeEventListener("animationcancel",r),n.removeEventListener("animationend",r)}}u("ANIMATION_END")},[n,u]),{isPresent:["mounted","unmountSuspended"].includes(c),ref:l.useCallback(e=>{i.current=e?getComputedStyle(e):null,o(e)},[])}}(t),o="function"==typeof r?r({present:n.isPresent}):l.Children.only(r),i=p(n.ref,function(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,r=t&&"isReactWarning"in t&&t.isReactWarning;return r?e.ref:(r=(t=Object.getOwnPropertyDescriptor(e,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning)?e.props.ref:e.props.ref||e.ref}(o));return"function"==typeof r||n.isPresent?l.cloneElement(o,{ref:i}):null};function tb(e){return e?.animationName||"none"}th.displayName="Presence";var tx=c[" useInsertionEffect ".trim().toString()]||C;Symbol("RADIX:SYNC_STATE");var tv=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"}),ty=l.forwardRef((e,t)=>(0,s.jsx)(v.span,{...e,ref:t,style:{...tv,...e.style}}));ty.displayName="VisuallyHidden";var[tw,tk]=m("Tooltip",[te]),tE=te(),tT="TooltipProvider",tR="tooltip.open",[tP,tj]=tw(tT),tC=e=>{let{__scopeTooltip:t,delayDuration:r=700,skipDelayDuration:n=300,disableHoverableContent:o=!1,children:i}=e,a=l.useRef(!0),c=l.useRef(!1),u=l.useRef(0);return l.useEffect(()=>{let e=u.current;return()=>window.clearTimeout(e)},[]),(0,s.jsx)(tP,{scope:t,isOpenDelayedRef:a,delayDuration:r,onOpen:l.useCallback(()=>{window.clearTimeout(u.current),a.current=!1},[]),onClose:l.useCallback(()=>{window.clearTimeout(u.current),u.current=window.setTimeout(()=>a.current=!0,n)},[n]),isPointerInTransitRef:c,onPointerInTransitChange:l.useCallback(e=>{c.current=e},[]),disableHoverableContent:o,children:i})};tC.displayName=tT;var tN="Tooltip",[tS,tA]=tw(tN),tL="TooltipTrigger";l.forwardRef((e,t)=>{let{__scopeTooltip:r,...n}=e,o=tA(tL,r),i=tj(tL,r),a=tE(r),c=p(t,l.useRef(null),o.onTriggerChange),d=l.useRef(!1),f=l.useRef(!1),m=l.useCallback(()=>d.current=!1,[]);return l.useEffect(()=>()=>document.removeEventListener("pointerup",m),[m]),(0,s.jsx)(to,{asChild:!0,...a,children:(0,s.jsx)(v.button,{"aria-describedby":o.open?o.contentId:void 0,"data-state":o.stateAttribute,...n,ref:c,onPointerMove:u(e.onPointerMove,e=>{"touch"===e.pointerType||f.current||i.isPointerInTransitRef.current||(o.onTriggerEnter(),f.current=!0)}),onPointerLeave:u(e.onPointerLeave,()=>{o.onTriggerLeave(),f.current=!1}),onPointerDown:u(e.onPointerDown,()=>{o.open&&o.onClose(),d.current=!0,document.addEventListener("pointerup",m,{once:!0})}),onFocus:u(e.onFocus,()=>{d.current||o.onOpen()}),onBlur:u(e.onBlur,o.onClose),onClick:u(e.onClick,o.onClose)})})}).displayName=tL;var[tO,tM]=tw("TooltipPortal",{forceMount:void 0}),t_="TooltipContent",tD=l.forwardRef((e,t)=>{let r=tM(t_,e.__scopeTooltip),{forceMount:n=r.forceMount,side:o="top",...i}=e,a=tA(t_,e.__scopeTooltip);return(0,s.jsx)(th,{present:n||a.open,children:a.disableHoverableContent?(0,s.jsx)(t$,{side:o,...i,ref:t}):(0,s.jsx)(tz,{side:o,...i,ref:t})})}),tz=l.forwardRef((e,t)=>{let r=tA(t_,e.__scopeTooltip),n=tj(t_,e.__scopeTooltip),o=l.useRef(null),i=p(t,o),[a,c]=l.useState(null),{trigger:u,onClose:d}=r,f=o.current,{onPointerInTransitChange:m}=n,g=l.useCallback(()=>{c(null),m(!1)},[m]),h=l.useCallback((e,t)=>{let r=e.currentTarget,n={x:e.clientX,y:e.clientY},o=function(e,t){let r=Math.abs(t.top-e.y),n=Math.abs(t.bottom-e.y),o=Math.abs(t.right-e.x),i=Math.abs(t.left-e.x);switch(Math.min(r,n,o,i)){case i:return"left";case o:return"right";case r:return"top";case n:return"bottom";default:throw Error("unreachable")}}(n,r.getBoundingClientRect());c(function(e){let t=e.slice();return t.sort((e,t)=>e.x<t.x?-1:e.x>t.x?1:e.y<t.y?-1:e.y>t.y?1:0),function(e){if(e.length<=1)return e.slice();let t=[];for(let r=0;r<e.length;r++){let n=e[r];for(;t.length>=2;){let e=t[t.length-1],r=t[t.length-2];if((e.x-r.x)*(n.y-r.y)>=(e.y-r.y)*(n.x-r.x))t.pop();else break}t.push(n)}t.pop();let r=[];for(let t=e.length-1;t>=0;t--){let n=e[t];for(;r.length>=2;){let e=r[r.length-1],t=r[r.length-2];if((e.x-t.x)*(n.y-t.y)>=(e.y-t.y)*(n.x-t.x))r.pop();else break}r.push(n)}return(r.pop(),1===t.length&&1===r.length&&t[0].x===r[0].x&&t[0].y===r[0].y)?t:t.concat(r)}(t)}([...function(e,t,r=5){let n=[];switch(t){case"top":n.push({x:e.x-r,y:e.y+r},{x:e.x+r,y:e.y+r});break;case"bottom":n.push({x:e.x-r,y:e.y-r},{x:e.x+r,y:e.y-r});break;case"left":n.push({x:e.x+r,y:e.y-r},{x:e.x+r,y:e.y+r});break;case"right":n.push({x:e.x-r,y:e.y-r},{x:e.x-r,y:e.y+r})}return n}(n,o),...function(e){let{top:t,right:r,bottom:n,left:o}=e;return[{x:o,y:t},{x:r,y:t},{x:r,y:n},{x:o,y:n}]}(t.getBoundingClientRect())])),m(!0)},[m]);return l.useEffect(()=>()=>g(),[g]),l.useEffect(()=>{if(u&&f){let e=e=>h(e,f),t=e=>h(e,u);return u.addEventListener("pointerleave",e),f.addEventListener("pointerleave",t),()=>{u.removeEventListener("pointerleave",e),f.removeEventListener("pointerleave",t)}}},[u,f,h,g]),l.useEffect(()=>{if(a){let e=e=>{let t=e.target,r={x:e.clientX,y:e.clientY},n=u?.contains(t)||f?.contains(t),o=!function(e,t){let{x:r,y:n}=e,o=!1;for(let e=0,i=t.length-1;e<t.length;i=e++){let s=t[e],a=t[i],l=s.x,c=s.y,u=a.x,d=a.y;c>n!=d>n&&r<(u-l)*(n-c)/(d-c)+l&&(o=!o)}return o}(r,a);n?g():o&&(g(),d())};return document.addEventListener("pointermove",e),()=>document.removeEventListener("pointermove",e)}},[u,f,a,d,g]),(0,s.jsx)(t$,{...e,ref:i})}),[tI,tF]=tw(tN,{isInside:!1}),tq=function(e){let t=({children:e})=>(0,s.jsx)(s.Fragment,{children:e});return t.displayName=`${e}.Slottable`,t.__radixId=b,t}("TooltipContent"),t$=l.forwardRef((e,t)=>{let{__scopeTooltip:r,children:n,"aria-label":o,onEscapeKeyDown:i,onPointerDownOutside:a,...c}=e,u=tA(t_,r),d=tE(r),{onClose:f}=u;return l.useEffect(()=>(document.addEventListener(tR,f),()=>document.removeEventListener(tR,f)),[f]),l.useEffect(()=>{if(u.trigger){let e=e=>{let t=e.target;t?.contains(u.trigger)&&f()};return window.addEventListener("scroll",e,{capture:!0}),()=>window.removeEventListener("scroll",e,{capture:!0})}},[u.trigger,f]),(0,s.jsx)(T,{asChild:!0,disableOutsidePointerEvents:!1,onEscapeKeyDown:i,onPointerDownOutside:a,onFocusOutside:e=>e.preventDefault(),onDismiss:f,children:(0,s.jsxs)(tl,{"data-state":u.stateAttribute,...d,...c,ref:t,style:{...c.style,"--radix-tooltip-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-tooltip-content-available-width":"var(--radix-popper-available-width)","--radix-tooltip-content-available-height":"var(--radix-popper-available-height)","--radix-tooltip-trigger-width":"var(--radix-popper-anchor-width)","--radix-tooltip-trigger-height":"var(--radix-popper-anchor-height)"},children:[(0,s.jsx)(tq,{children:n}),(0,s.jsx)(tI,{scope:r,isInside:!0,children:(0,s.jsx)(ty,{id:u.contentId,role:"tooltip",children:o||n})})]})})});tD.displayName=t_;var tW="TooltipArrow";l.forwardRef((e,t)=>{let{__scopeTooltip:r,...n}=e,o=tE(r);return tF(tW,r).isInside?null:(0,s.jsx)(td,{...o,...n,ref:t})}).displayName=tW;var tB=r(41135);let tH=e=>{let t=tK(e),{conflictingClassGroups:r,conflictingClassGroupModifiers:n}=e;return{getClassGroupId:e=>{let r=e.split("-");return""===r[0]&&1!==r.length&&r.shift(),tV(r,t)||tU(e)},getConflictingClassGroupIds:(e,t)=>{let o=r[e]||[];return t&&n[e]?[...o,...n[e]]:o}}},tV=(e,t)=>{if(0===e.length)return t.classGroupId;let r=e[0],n=t.nextPart.get(r),o=n?tV(e.slice(1),n):void 0;if(o)return o;if(0===t.validators.length)return;let i=e.join("-");return t.validators.find(({validator:e})=>e(i))?.classGroupId},tG=/^\[(.+)\]$/,tU=e=>{if(tG.test(e)){let t=tG.exec(e)[1],r=t?.substring(0,t.indexOf(":"));if(r)return"arbitrary.."+r}},tK=e=>{let{theme:t,classGroups:r}=e,n={nextPart:new Map,validators:[]};for(let e in r)tX(r[e],n,e,t);return n},tX=(e,t,r,n)=>{e.forEach(e=>{if("string"==typeof e){(""===e?t:tY(t,e)).classGroupId=r;return}if("function"==typeof e){if(tZ(e)){tX(e(n),t,r,n);return}t.validators.push({validator:e,classGroupId:r});return}Object.entries(e).forEach(([e,o])=>{tX(o,tY(t,e),r,n)})})},tY=(e,t)=>{let r=e;return t.split("-").forEach(e=>{r.nextPart.has(e)||r.nextPart.set(e,{nextPart:new Map,validators:[]}),r=r.nextPart.get(e)}),r},tZ=e=>e.isThemeGetter,tJ=e=>{if(e<1)return{get:()=>void 0,set:()=>{}};let t=0,r=new Map,n=new Map,o=(o,i)=>{r.set(o,i),++t>e&&(t=0,n=r,r=new Map)};return{get(e){let t=r.get(e);return void 0!==t?t:void 0!==(t=n.get(e))?(o(e,t),t):void 0},set(e,t){r.has(e)?r.set(e,t):o(e,t)}}},tQ=e=>{let{prefix:t,experimentalParseClassName:r}=e,n=e=>{let t;let r=[],n=0,o=0,i=0;for(let s=0;s<e.length;s++){let a=e[s];if(0===n&&0===o){if(":"===a){r.push(e.slice(i,s)),i=s+1;continue}if("/"===a){t=s;continue}}"["===a?n++:"]"===a?n--:"("===a?o++:")"===a&&o--}let s=0===r.length?e:e.substring(i),a=t0(s);return{modifiers:r,hasImportantModifier:a!==s,baseClassName:a,maybePostfixModifierPosition:t&&t>i?t-i:void 0}};if(t){let e=t+":",r=n;n=t=>t.startsWith(e)?r(t.substring(e.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:t,maybePostfixModifierPosition:void 0}}if(r){let e=n;n=t=>r({className:t,parseClassName:e})}return n},t0=e=>e.endsWith("!")?e.substring(0,e.length-1):e.startsWith("!")?e.substring(1):e,t1=e=>{let t=Object.fromEntries(e.orderSensitiveModifiers.map(e=>[e,!0]));return e=>{if(e.length<=1)return e;let r=[],n=[];return e.forEach(e=>{"["===e[0]||t[e]?(r.push(...n.sort(),e),n=[]):n.push(e)}),r.push(...n.sort()),r}},t2=e=>({cache:tJ(e.cacheSize),parseClassName:tQ(e),sortModifiers:t1(e),...tH(e)}),t6=/\s+/,t3=(e,t)=>{let{parseClassName:r,getClassGroupId:n,getConflictingClassGroupIds:o,sortModifiers:i}=t,s=[],a=e.trim().split(t6),l="";for(let e=a.length-1;e>=0;e-=1){let t=a[e],{isExternal:c,modifiers:u,hasImportantModifier:d,baseClassName:f,maybePostfixModifierPosition:p}=r(t);if(c){l=t+(l.length>0?" "+l:l);continue}let m=!!p,g=n(m?f.substring(0,p):f);if(!g){if(!m||!(g=n(f))){l=t+(l.length>0?" "+l:l);continue}m=!1}let h=i(u).join(":"),b=d?h+"!":h,x=b+g;if(s.includes(x))continue;s.push(x);let v=o(g,m);for(let e=0;e<v.length;++e){let t=v[e];s.push(b+t)}l=t+(l.length>0?" "+l:l)}return l};function t5(){let e,t,r=0,n="";for(;r<arguments.length;)(e=arguments[r++])&&(t=t8(e))&&(n&&(n+=" "),n+=t);return n}let t8=e=>{let t;if("string"==typeof e)return e;let r="";for(let n=0;n<e.length;n++)e[n]&&(t=t8(e[n]))&&(r&&(r+=" "),r+=t);return r},t4=e=>{let t=t=>t[e]||[];return t.isThemeGetter=!0,t},t7=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,t9=/^\((?:(\w[\w-]*):)?(.+)\)$/i,re=/^\d+\/\d+$/,rt=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,rr=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,rn=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,ro=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,ri=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,rs=e=>re.test(e),ra=e=>!!e&&!Number.isNaN(Number(e)),rl=e=>!!e&&Number.isInteger(Number(e)),rc=e=>e.endsWith("%")&&ra(e.slice(0,-1)),ru=e=>rt.test(e),rd=()=>!0,rf=e=>rr.test(e)&&!rn.test(e),rp=()=>!1,rm=e=>ro.test(e),rg=e=>ri.test(e),rh=e=>!rx(e)&&!rT(e),rb=e=>rA(e,r_,rp),rx=e=>t7.test(e),rv=e=>rA(e,rD,rf),ry=e=>rA(e,rz,ra),rw=e=>rA(e,rO,rp),rk=e=>rA(e,rM,rg),rE=e=>rA(e,rF,rm),rT=e=>t9.test(e),rR=e=>rL(e,rD),rP=e=>rL(e,rI),rj=e=>rL(e,rO),rC=e=>rL(e,r_),rN=e=>rL(e,rM),rS=e=>rL(e,rF,!0),rA=(e,t,r)=>{let n=t7.exec(e);return!!n&&(n[1]?t(n[1]):r(n[2]))},rL=(e,t,r=!1)=>{let n=t9.exec(e);return!!n&&(n[1]?t(n[1]):r)},rO=e=>"position"===e||"percentage"===e,rM=e=>"image"===e||"url"===e,r_=e=>"length"===e||"size"===e||"bg-size"===e,rD=e=>"length"===e,rz=e=>"number"===e,rI=e=>"family-name"===e,rF=e=>"shadow"===e;Symbol.toStringTag;let rq=function(e,...t){let r,n,o;let i=function(a){return n=(r=t2(t.reduce((e,t)=>t(e),e()))).cache.get,o=r.cache.set,i=s,s(a)};function s(e){let t=n(e);if(t)return t;let i=t3(e,r);return o(e,i),i}return function(){return i(t5.apply(null,arguments))}}(()=>{let e=t4("color"),t=t4("font"),r=t4("text"),n=t4("font-weight"),o=t4("tracking"),i=t4("leading"),s=t4("breakpoint"),a=t4("container"),l=t4("spacing"),c=t4("radius"),u=t4("shadow"),d=t4("inset-shadow"),f=t4("text-shadow"),p=t4("drop-shadow"),m=t4("blur"),g=t4("perspective"),h=t4("aspect"),b=t4("ease"),x=t4("animate"),v=()=>["auto","avoid","all","avoid-page","page","left","right","column"],y=()=>["center","top","bottom","left","right","top-left","left-top","top-right","right-top","bottom-right","right-bottom","bottom-left","left-bottom"],w=()=>[...y(),rT,rx],k=()=>["auto","hidden","clip","visible","scroll"],E=()=>["auto","contain","none"],T=()=>[rT,rx,l],R=()=>[rs,"full","auto",...T()],P=()=>[rl,"none","subgrid",rT,rx],j=()=>["auto",{span:["full",rl,rT,rx]},rl,rT,rx],C=()=>[rl,"auto",rT,rx],N=()=>["auto","min","max","fr",rT,rx],S=()=>["start","end","center","between","around","evenly","stretch","baseline","center-safe","end-safe"],A=()=>["start","end","center","stretch","center-safe","end-safe"],L=()=>["auto",...T()],O=()=>[rs,"auto","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",...T()],M=()=>[e,rT,rx],_=()=>[...y(),rj,rw,{position:[rT,rx]}],D=()=>["no-repeat",{repeat:["","x","y","space","round"]}],z=()=>["auto","cover","contain",rC,rb,{size:[rT,rx]}],I=()=>[rc,rR,rv],F=()=>["","none","full",c,rT,rx],q=()=>["",ra,rR,rv],$=()=>["solid","dashed","dotted","double"],W=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],B=()=>[ra,rc,rj,rw],H=()=>["","none",m,rT,rx],V=()=>["none",ra,rT,rx],G=()=>["none",ra,rT,rx],U=()=>[ra,rT,rx],K=()=>[rs,"full",...T()];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[ru],breakpoint:[ru],color:[rd],container:[ru],"drop-shadow":[ru],ease:["in","out","in-out"],font:[rh],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[ru],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[ru],shadow:[ru],spacing:["px",ra],text:[ru],"text-shadow":[ru],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",rs,rx,rT,h]}],container:["container"],columns:[{columns:[ra,rx,rT,a]}],"break-after":[{"break-after":v()}],"break-before":[{"break-before":v()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:w()}],overflow:[{overflow:k()}],"overflow-x":[{"overflow-x":k()}],"overflow-y":[{"overflow-y":k()}],overscroll:[{overscroll:E()}],"overscroll-x":[{"overscroll-x":E()}],"overscroll-y":[{"overscroll-y":E()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:R()}],"inset-x":[{"inset-x":R()}],"inset-y":[{"inset-y":R()}],start:[{start:R()}],end:[{end:R()}],top:[{top:R()}],right:[{right:R()}],bottom:[{bottom:R()}],left:[{left:R()}],visibility:["visible","invisible","collapse"],z:[{z:[rl,"auto",rT,rx]}],basis:[{basis:[rs,"full","auto",a,...T()]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[ra,rs,"auto","initial","none",rx]}],grow:[{grow:["",ra,rT,rx]}],shrink:[{shrink:["",ra,rT,rx]}],order:[{order:[rl,"first","last","none",rT,rx]}],"grid-cols":[{"grid-cols":P()}],"col-start-end":[{col:j()}],"col-start":[{"col-start":C()}],"col-end":[{"col-end":C()}],"grid-rows":[{"grid-rows":P()}],"row-start-end":[{row:j()}],"row-start":[{"row-start":C()}],"row-end":[{"row-end":C()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":N()}],"auto-rows":[{"auto-rows":N()}],gap:[{gap:T()}],"gap-x":[{"gap-x":T()}],"gap-y":[{"gap-y":T()}],"justify-content":[{justify:[...S(),"normal"]}],"justify-items":[{"justify-items":[...A(),"normal"]}],"justify-self":[{"justify-self":["auto",...A()]}],"align-content":[{content:["normal",...S()]}],"align-items":[{items:[...A(),{baseline:["","last"]}]}],"align-self":[{self:["auto",...A(),{baseline:["","last"]}]}],"place-content":[{"place-content":S()}],"place-items":[{"place-items":[...A(),"baseline"]}],"place-self":[{"place-self":["auto",...A()]}],p:[{p:T()}],px:[{px:T()}],py:[{py:T()}],ps:[{ps:T()}],pe:[{pe:T()}],pt:[{pt:T()}],pr:[{pr:T()}],pb:[{pb:T()}],pl:[{pl:T()}],m:[{m:L()}],mx:[{mx:L()}],my:[{my:L()}],ms:[{ms:L()}],me:[{me:L()}],mt:[{mt:L()}],mr:[{mr:L()}],mb:[{mb:L()}],ml:[{ml:L()}],"space-x":[{"space-x":T()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":T()}],"space-y-reverse":["space-y-reverse"],size:[{size:O()}],w:[{w:[a,"screen",...O()]}],"min-w":[{"min-w":[a,"screen","none",...O()]}],"max-w":[{"max-w":[a,"screen","none","prose",{screen:[s]},...O()]}],h:[{h:["screen","lh",...O()]}],"min-h":[{"min-h":["screen","lh","none",...O()]}],"max-h":[{"max-h":["screen","lh",...O()]}],"font-size":[{text:["base",r,rR,rv]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[n,rT,ry]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",rc,rx]}],"font-family":[{font:[rP,rx,t]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[o,rT,rx]}],"line-clamp":[{"line-clamp":[ra,"none",rT,ry]}],leading:[{leading:[i,...T()]}],"list-image":[{"list-image":["none",rT,rx]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",rT,rx]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:M()}],"text-color":[{text:M()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...$(),"wavy"]}],"text-decoration-thickness":[{decoration:[ra,"from-font","auto",rT,rv]}],"text-decoration-color":[{decoration:M()}],"underline-offset":[{"underline-offset":[ra,"auto",rT,rx]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:T()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",rT,rx]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],wrap:[{wrap:["break-word","anywhere","normal"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",rT,rx]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:_()}],"bg-repeat":[{bg:D()}],"bg-size":[{bg:z()}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},rl,rT,rx],radial:["",rT,rx],conic:[rl,rT,rx]},rN,rk]}],"bg-color":[{bg:M()}],"gradient-from-pos":[{from:I()}],"gradient-via-pos":[{via:I()}],"gradient-to-pos":[{to:I()}],"gradient-from":[{from:M()}],"gradient-via":[{via:M()}],"gradient-to":[{to:M()}],rounded:[{rounded:F()}],"rounded-s":[{"rounded-s":F()}],"rounded-e":[{"rounded-e":F()}],"rounded-t":[{"rounded-t":F()}],"rounded-r":[{"rounded-r":F()}],"rounded-b":[{"rounded-b":F()}],"rounded-l":[{"rounded-l":F()}],"rounded-ss":[{"rounded-ss":F()}],"rounded-se":[{"rounded-se":F()}],"rounded-ee":[{"rounded-ee":F()}],"rounded-es":[{"rounded-es":F()}],"rounded-tl":[{"rounded-tl":F()}],"rounded-tr":[{"rounded-tr":F()}],"rounded-br":[{"rounded-br":F()}],"rounded-bl":[{"rounded-bl":F()}],"border-w":[{border:q()}],"border-w-x":[{"border-x":q()}],"border-w-y":[{"border-y":q()}],"border-w-s":[{"border-s":q()}],"border-w-e":[{"border-e":q()}],"border-w-t":[{"border-t":q()}],"border-w-r":[{"border-r":q()}],"border-w-b":[{"border-b":q()}],"border-w-l":[{"border-l":q()}],"divide-x":[{"divide-x":q()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":q()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:[...$(),"hidden","none"]}],"divide-style":[{divide:[...$(),"hidden","none"]}],"border-color":[{border:M()}],"border-color-x":[{"border-x":M()}],"border-color-y":[{"border-y":M()}],"border-color-s":[{"border-s":M()}],"border-color-e":[{"border-e":M()}],"border-color-t":[{"border-t":M()}],"border-color-r":[{"border-r":M()}],"border-color-b":[{"border-b":M()}],"border-color-l":[{"border-l":M()}],"divide-color":[{divide:M()}],"outline-style":[{outline:[...$(),"none","hidden"]}],"outline-offset":[{"outline-offset":[ra,rT,rx]}],"outline-w":[{outline:["",ra,rR,rv]}],"outline-color":[{outline:M()}],shadow:[{shadow:["","none",u,rS,rE]}],"shadow-color":[{shadow:M()}],"inset-shadow":[{"inset-shadow":["none",d,rS,rE]}],"inset-shadow-color":[{"inset-shadow":M()}],"ring-w":[{ring:q()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:M()}],"ring-offset-w":[{"ring-offset":[ra,rv]}],"ring-offset-color":[{"ring-offset":M()}],"inset-ring-w":[{"inset-ring":q()}],"inset-ring-color":[{"inset-ring":M()}],"text-shadow":[{"text-shadow":["none",f,rS,rE]}],"text-shadow-color":[{"text-shadow":M()}],opacity:[{opacity:[ra,rT,rx]}],"mix-blend":[{"mix-blend":[...W(),"plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":W()}],"mask-clip":[{"mask-clip":["border","padding","content","fill","stroke","view"]},"mask-no-clip"],"mask-composite":[{mask:["add","subtract","intersect","exclude"]}],"mask-image-linear-pos":[{"mask-linear":[ra]}],"mask-image-linear-from-pos":[{"mask-linear-from":B()}],"mask-image-linear-to-pos":[{"mask-linear-to":B()}],"mask-image-linear-from-color":[{"mask-linear-from":M()}],"mask-image-linear-to-color":[{"mask-linear-to":M()}],"mask-image-t-from-pos":[{"mask-t-from":B()}],"mask-image-t-to-pos":[{"mask-t-to":B()}],"mask-image-t-from-color":[{"mask-t-from":M()}],"mask-image-t-to-color":[{"mask-t-to":M()}],"mask-image-r-from-pos":[{"mask-r-from":B()}],"mask-image-r-to-pos":[{"mask-r-to":B()}],"mask-image-r-from-color":[{"mask-r-from":M()}],"mask-image-r-to-color":[{"mask-r-to":M()}],"mask-image-b-from-pos":[{"mask-b-from":B()}],"mask-image-b-to-pos":[{"mask-b-to":B()}],"mask-image-b-from-color":[{"mask-b-from":M()}],"mask-image-b-to-color":[{"mask-b-to":M()}],"mask-image-l-from-pos":[{"mask-l-from":B()}],"mask-image-l-to-pos":[{"mask-l-to":B()}],"mask-image-l-from-color":[{"mask-l-from":M()}],"mask-image-l-to-color":[{"mask-l-to":M()}],"mask-image-x-from-pos":[{"mask-x-from":B()}],"mask-image-x-to-pos":[{"mask-x-to":B()}],"mask-image-x-from-color":[{"mask-x-from":M()}],"mask-image-x-to-color":[{"mask-x-to":M()}],"mask-image-y-from-pos":[{"mask-y-from":B()}],"mask-image-y-to-pos":[{"mask-y-to":B()}],"mask-image-y-from-color":[{"mask-y-from":M()}],"mask-image-y-to-color":[{"mask-y-to":M()}],"mask-image-radial":[{"mask-radial":[rT,rx]}],"mask-image-radial-from-pos":[{"mask-radial-from":B()}],"mask-image-radial-to-pos":[{"mask-radial-to":B()}],"mask-image-radial-from-color":[{"mask-radial-from":M()}],"mask-image-radial-to-color":[{"mask-radial-to":M()}],"mask-image-radial-shape":[{"mask-radial":["circle","ellipse"]}],"mask-image-radial-size":[{"mask-radial":[{closest:["side","corner"],farthest:["side","corner"]}]}],"mask-image-radial-pos":[{"mask-radial-at":y()}],"mask-image-conic-pos":[{"mask-conic":[ra]}],"mask-image-conic-from-pos":[{"mask-conic-from":B()}],"mask-image-conic-to-pos":[{"mask-conic-to":B()}],"mask-image-conic-from-color":[{"mask-conic-from":M()}],"mask-image-conic-to-color":[{"mask-conic-to":M()}],"mask-mode":[{mask:["alpha","luminance","match"]}],"mask-origin":[{"mask-origin":["border","padding","content","fill","stroke","view"]}],"mask-position":[{mask:_()}],"mask-repeat":[{mask:D()}],"mask-size":[{mask:z()}],"mask-type":[{"mask-type":["alpha","luminance"]}],"mask-image":[{mask:["none",rT,rx]}],filter:[{filter:["","none",rT,rx]}],blur:[{blur:H()}],brightness:[{brightness:[ra,rT,rx]}],contrast:[{contrast:[ra,rT,rx]}],"drop-shadow":[{"drop-shadow":["","none",p,rS,rE]}],"drop-shadow-color":[{"drop-shadow":M()}],grayscale:[{grayscale:["",ra,rT,rx]}],"hue-rotate":[{"hue-rotate":[ra,rT,rx]}],invert:[{invert:["",ra,rT,rx]}],saturate:[{saturate:[ra,rT,rx]}],sepia:[{sepia:["",ra,rT,rx]}],"backdrop-filter":[{"backdrop-filter":["","none",rT,rx]}],"backdrop-blur":[{"backdrop-blur":H()}],"backdrop-brightness":[{"backdrop-brightness":[ra,rT,rx]}],"backdrop-contrast":[{"backdrop-contrast":[ra,rT,rx]}],"backdrop-grayscale":[{"backdrop-grayscale":["",ra,rT,rx]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[ra,rT,rx]}],"backdrop-invert":[{"backdrop-invert":["",ra,rT,rx]}],"backdrop-opacity":[{"backdrop-opacity":[ra,rT,rx]}],"backdrop-saturate":[{"backdrop-saturate":[ra,rT,rx]}],"backdrop-sepia":[{"backdrop-sepia":["",ra,rT,rx]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":T()}],"border-spacing-x":[{"border-spacing-x":T()}],"border-spacing-y":[{"border-spacing-y":T()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",rT,rx]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[ra,"initial",rT,rx]}],ease:[{ease:["linear","initial",b,rT,rx]}],delay:[{delay:[ra,rT,rx]}],animate:[{animate:["none",x,rT,rx]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[g,rT,rx]}],"perspective-origin":[{"perspective-origin":w()}],rotate:[{rotate:V()}],"rotate-x":[{"rotate-x":V()}],"rotate-y":[{"rotate-y":V()}],"rotate-z":[{"rotate-z":V()}],scale:[{scale:G()}],"scale-x":[{"scale-x":G()}],"scale-y":[{"scale-y":G()}],"scale-z":[{"scale-z":G()}],"scale-3d":["scale-3d"],skew:[{skew:U()}],"skew-x":[{"skew-x":U()}],"skew-y":[{"skew-y":U()}],transform:[{transform:[rT,rx,"","none","gpu","cpu"]}],"transform-origin":[{origin:w()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:K()}],"translate-x":[{"translate-x":K()}],"translate-y":[{"translate-y":K()}],"translate-z":[{"translate-z":K()}],"translate-none":["translate-none"],accent:[{accent:M()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:M()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",rT,rx]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":T()}],"scroll-mx":[{"scroll-mx":T()}],"scroll-my":[{"scroll-my":T()}],"scroll-ms":[{"scroll-ms":T()}],"scroll-me":[{"scroll-me":T()}],"scroll-mt":[{"scroll-mt":T()}],"scroll-mr":[{"scroll-mr":T()}],"scroll-mb":[{"scroll-mb":T()}],"scroll-ml":[{"scroll-ml":T()}],"scroll-p":[{"scroll-p":T()}],"scroll-px":[{"scroll-px":T()}],"scroll-py":[{"scroll-py":T()}],"scroll-ps":[{"scroll-ps":T()}],"scroll-pe":[{"scroll-pe":T()}],"scroll-pt":[{"scroll-pt":T()}],"scroll-pr":[{"scroll-pr":T()}],"scroll-pb":[{"scroll-pb":T()}],"scroll-pl":[{"scroll-pl":T()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",rT,rx]}],fill:[{fill:["none",...M()]}],"stroke-w":[{stroke:[ra,rR,rv,ry]}],stroke:[{stroke:["none",...M()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-x","border-w-y","border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-x","border-color-y","border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["*","**","after","backdrop","before","details-content","file","first-letter","first-line","marker","placeholder","selection"]}});function r$(...e){return rq((0,tB.W)(e))}l.forwardRef(({className:e,sideOffset:t=4,...r},n)=>s.jsx(tD,{ref:n,sideOffset:t,className:r$("z-50 overflow-hidden rounded-md border bg-popover px-3 py-1.5 text-sm text-popover-foreground shadow-md animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",e),...r})).displayName=tD.displayName;let rW=0,rB=new Map,rH=e=>{if(rB.has(e))return;let t=setTimeout(()=>{rB.delete(e),rK({type:"REMOVE_TOAST",toastId:e})},1e6);rB.set(e,t)},rV=(e,t)=>{switch(t.type){case"ADD_TOAST":return{...e,toasts:[t.toast,...e.toasts].slice(0,1)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(e=>e.id===t.toast.id?{...e,...t.toast}:e)};case"DISMISS_TOAST":{let{toastId:r}=t;return r?rH(r):e.toasts.forEach(e=>{rH(e.id)}),{...e,toasts:e.toasts.map(e=>e.id===r||void 0===r?{...e,open:!1}:e)}}case"REMOVE_TOAST":if(void 0===t.toastId)return{...e,toasts:[]};return{...e,toasts:e.toasts.filter(e=>e.id!==t.toastId)}}},rG=[],rU={toasts:[]};function rK(e){rU=rV(rU,e),rG.forEach(e=>{e(rU)})}function rX({...e}){let t=(rW=(rW+1)%Number.MAX_SAFE_INTEGER).toString(),r=()=>rK({type:"DISMISS_TOAST",toastId:t});return rK({type:"ADD_TOAST",toast:{...e,id:t,open:!0,onOpenChange:e=>{e||r()}}}),{id:t,dismiss:r,update:e=>rK({type:"UPDATE_TOAST",toast:{...e,id:t}})}}var rY="ToastProvider",[rZ,rJ,rQ]=function(e){let t=e+"CollectionProvider",[r,n]=m(t),[o,i]=r(t,{collectionRef:{current:null},itemMap:new Map}),a=e=>{let{scope:t,children:r}=e,n=l.useRef(null),i=l.useRef(new Map).current;return(0,s.jsx)(o,{scope:t,itemMap:i,collectionRef:n,children:r})};a.displayName=t;let c=e+"CollectionSlot",u=h(c),d=l.forwardRef((e,t)=>{let{scope:r,children:n}=e,o=p(t,i(c,r).collectionRef);return(0,s.jsx)(u,{ref:o,children:n})});d.displayName=c;let f=e+"CollectionItemSlot",g="data-radix-collection-item",b=h(f),x=l.forwardRef((e,t)=>{let{scope:r,children:n,...o}=e,a=l.useRef(null),c=p(t,a),u=i(f,r);return l.useEffect(()=>(u.itemMap.set(a,{ref:a,...o}),()=>void u.itemMap.delete(a))),(0,s.jsx)(b,{[g]:"",ref:c,children:n})});return x.displayName=f,[{Provider:a,Slot:d,ItemSlot:x},function(t){let r=i(e+"CollectionConsumer",t);return l.useCallback(()=>{let e=r.collectionRef.current;if(!e)return[];let t=Array.from(e.querySelectorAll(`[${g}]`));return Array.from(r.itemMap.values()).sort((e,r)=>t.indexOf(e.ref.current)-t.indexOf(r.ref.current))},[r.collectionRef,r.itemMap])},n]}("Toast"),[r0,r1]=m("Toast",[rQ]),[r2,r6]=r0(rY),r3=e=>{let{__scopeToast:t,label:r="Notification",duration:n=5e3,swipeDirection:o="right",swipeThreshold:i=50,children:a}=e,[c,u]=l.useState(null),[d,f]=l.useState(0),p=l.useRef(!1),m=l.useRef(!1);return r.trim()||console.error(`Invalid prop \`label\` supplied to \`${rY}\`. Expected non-empty \`string\`.`),(0,s.jsx)(rZ.Provider,{scope:t,children:(0,s.jsx)(r2,{scope:t,label:r,duration:n,swipeDirection:o,swipeThreshold:i,toastCount:d,viewport:c,onViewportChange:u,onToastAdd:l.useCallback(()=>f(e=>e+1),[]),onToastRemove:l.useCallback(()=>f(e=>e-1),[]),isFocusedToastEscapeKeyDownRef:p,isClosePausedRef:m,children:a})})};r3.displayName=rY;var r5="ToastViewport",r8=["F8"],r4="toast.viewportPause",r7="toast.viewportResume",r9=l.forwardRef((e,t)=>{let{__scopeToast:r,hotkey:n=r8,label:o="Notifications ({hotkey})",...i}=e,a=r6(r5,r),c=rJ(r),u=l.useRef(null),d=l.useRef(null),f=l.useRef(null),m=l.useRef(null),g=p(t,m,a.onViewportChange),h=n.join("+").replace(/Key/g,"").replace(/Digit/g,""),b=a.toastCount>0;l.useEffect(()=>{let e=e=>{0!==n.length&&n.every(t=>e[t]||e.code===t)&&m.current?.focus()};return document.addEventListener("keydown",e),()=>document.removeEventListener("keydown",e)},[n]),l.useEffect(()=>{let e=u.current,t=m.current;if(b&&e&&t){let r=()=>{if(!a.isClosePausedRef.current){let e=new CustomEvent(r4);t.dispatchEvent(e),a.isClosePausedRef.current=!0}},n=()=>{if(a.isClosePausedRef.current){let e=new CustomEvent(r7);t.dispatchEvent(e),a.isClosePausedRef.current=!1}},o=t=>{e.contains(t.relatedTarget)||n()},i=()=>{e.contains(document.activeElement)||n()};return e.addEventListener("focusin",r),e.addEventListener("focusout",o),e.addEventListener("pointermove",r),e.addEventListener("pointerleave",i),window.addEventListener("blur",r),window.addEventListener("focus",n),()=>{e.removeEventListener("focusin",r),e.removeEventListener("focusout",o),e.removeEventListener("pointermove",r),e.removeEventListener("pointerleave",i),window.removeEventListener("blur",r),window.removeEventListener("focus",n)}}},[b,a.isClosePausedRef]);let x=l.useCallback(({tabbingDirection:e})=>{let t=c().map(t=>{let r=t.ref.current,n=[r,...function(e){let t=[],r=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;r.nextNode();)t.push(r.currentNode);return t}(r)];return"forwards"===e?n:n.reverse()});return("forwards"===e?t.reverse():t).flat()},[c]);return l.useEffect(()=>{let e=m.current;if(e){let t=t=>{let r=t.altKey||t.ctrlKey||t.metaKey;if("Tab"===t.key&&!r){let r=document.activeElement,n=t.shiftKey;if(t.target===e&&n){d.current?.focus();return}let o=x({tabbingDirection:n?"backwards":"forwards"}),i=o.findIndex(e=>e===r);nb(o.slice(i+1))?t.preventDefault():n?d.current?.focus():f.current?.focus()}};return e.addEventListener("keydown",t),()=>e.removeEventListener("keydown",t)}},[c,x]),(0,s.jsxs)(R,{ref:u,role:"region","aria-label":o.replace("{hotkey}",h),tabIndex:-1,style:{pointerEvents:b?void 0:"none"},children:[b&&(0,s.jsx)(nt,{ref:d,onFocusFromOutsideViewport:()=>{nb(x({tabbingDirection:"forwards"}))}}),(0,s.jsx)(rZ.Slot,{scope:r,children:(0,s.jsx)(v.ol,{tabIndex:-1,...i,ref:g})}),b&&(0,s.jsx)(nt,{ref:f,onFocusFromOutsideViewport:()=>{nb(x({tabbingDirection:"backwards"}))}})]})});r9.displayName=r5;var ne="ToastFocusProxy",nt=l.forwardRef((e,t)=>{let{__scopeToast:r,onFocusFromOutsideViewport:n,...o}=e,i=r6(ne,r);return(0,s.jsx)(ty,{"aria-hidden":!0,tabIndex:0,...o,ref:t,style:{position:"fixed"},onFocus:e=>{let t=e.relatedTarget;i.viewport?.contains(t)||n()}})});nt.displayName=ne;var nr="Toast",nn=l.forwardRef((e,t)=>{let{forceMount:r,open:n,defaultOpen:o,onOpenChange:i,...a}=e,[c,d]=function({prop:e,defaultProp:t,onChange:r=()=>{},caller:n}){let[o,i,s]=function({defaultProp:e,onChange:t}){let[r,n]=l.useState(e),o=l.useRef(r),i=l.useRef(t);return tx(()=>{i.current=t},[t]),l.useEffect(()=>{o.current!==r&&(i.current?.(r),o.current=r)},[r,o]),[r,n,i]}({defaultProp:t,onChange:r}),a=void 0!==e,c=a?e:o;{let t=l.useRef(void 0!==e);l.useEffect(()=>{let e=t.current;if(e!==a){let t=a?"controlled":"uncontrolled";console.warn(`${n} is changing from ${e?"controlled":"uncontrolled"} to ${t}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}t.current=a},[a,n])}return[c,l.useCallback(t=>{if(a){let r="function"==typeof t?t(e):t;r!==e&&s.current?.(r)}else i(t)},[a,e,i,s])]}({prop:n,defaultProp:o??!0,onChange:i,caller:nr});return(0,s.jsx)(th,{present:r||c,children:(0,s.jsx)(ns,{open:c,...a,ref:t,onClose:()=>d(!1),onPause:w(e.onPause),onResume:w(e.onResume),onSwipeStart:u(e.onSwipeStart,e=>{e.currentTarget.setAttribute("data-swipe","start")}),onSwipeMove:u(e.onSwipeMove,e=>{let{x:t,y:r}=e.detail.delta;e.currentTarget.setAttribute("data-swipe","move"),e.currentTarget.style.setProperty("--radix-toast-swipe-move-x",`${t}px`),e.currentTarget.style.setProperty("--radix-toast-swipe-move-y",`${r}px`)}),onSwipeCancel:u(e.onSwipeCancel,e=>{e.currentTarget.setAttribute("data-swipe","cancel"),e.currentTarget.style.removeProperty("--radix-toast-swipe-move-x"),e.currentTarget.style.removeProperty("--radix-toast-swipe-move-y"),e.currentTarget.style.removeProperty("--radix-toast-swipe-end-x"),e.currentTarget.style.removeProperty("--radix-toast-swipe-end-y")}),onSwipeEnd:u(e.onSwipeEnd,e=>{let{x:t,y:r}=e.detail.delta;e.currentTarget.setAttribute("data-swipe","end"),e.currentTarget.style.removeProperty("--radix-toast-swipe-move-x"),e.currentTarget.style.removeProperty("--radix-toast-swipe-move-y"),e.currentTarget.style.setProperty("--radix-toast-swipe-end-x",`${t}px`),e.currentTarget.style.setProperty("--radix-toast-swipe-end-y",`${r}px`),d(!1)})})})});nn.displayName=nr;var[no,ni]=r0(nr,{onClose(){}}),ns=l.forwardRef((e,t)=>{let{__scopeToast:r,type:n="foreground",duration:o,open:i,onClose:a,onEscapeKeyDown:c,onPause:d,onResume:f,onSwipeStart:m,onSwipeMove:h,onSwipeCancel:b,onSwipeEnd:x,...y}=e,k=r6(nr,r),[E,R]=l.useState(null),P=p(t,e=>R(e)),j=l.useRef(null),C=l.useRef(null),N=o||k.duration,S=l.useRef(0),A=l.useRef(N),L=l.useRef(0),{onToastAdd:O,onToastRemove:M}=k,_=w(()=>{E?.contains(document.activeElement)&&k.viewport?.focus(),a()}),D=l.useCallback(e=>{e&&e!==1/0&&(window.clearTimeout(L.current),S.current=new Date().getTime(),L.current=window.setTimeout(_,e))},[_]);l.useEffect(()=>{let e=k.viewport;if(e){let t=()=>{D(A.current),f?.()},r=()=>{let e=new Date().getTime()-S.current;A.current=A.current-e,window.clearTimeout(L.current),d?.()};return e.addEventListener(r4,r),e.addEventListener(r7,t),()=>{e.removeEventListener(r4,r),e.removeEventListener(r7,t)}}},[k.viewport,N,d,f,D]),l.useEffect(()=>{i&&!k.isClosePausedRef.current&&D(N)},[i,N,k.isClosePausedRef,D]),l.useEffect(()=>(O(),()=>M()),[O,M]);let z=l.useMemo(()=>E?function e(t){let r=[];return Array.from(t.childNodes).forEach(t=>{if(t.nodeType===t.TEXT_NODE&&t.textContent&&r.push(t.textContent),t.nodeType===t.ELEMENT_NODE){let n=t.ariaHidden||t.hidden||"none"===t.style.display,o=""===t.dataset.radixToastAnnounceExclude;if(!n){if(o){let e=t.dataset.radixToastAnnounceAlt;e&&r.push(e)}else r.push(...e(t))}}}),r}(E):null,[E]);return k.viewport?(0,s.jsxs)(s.Fragment,{children:[z&&(0,s.jsx)(na,{__scopeToast:r,role:"status","aria-live":"foreground"===n?"assertive":"polite","aria-atomic":!0,children:z}),(0,s.jsx)(no,{scope:r,onClose:_,children:g.createPortal((0,s.jsx)(rZ.ItemSlot,{scope:r,children:(0,s.jsx)(T,{asChild:!0,onEscapeKeyDown:u(c,()=>{k.isFocusedToastEscapeKeyDownRef.current||_(),k.isFocusedToastEscapeKeyDownRef.current=!1}),children:(0,s.jsx)(v.li,{role:"status","aria-live":"off","aria-atomic":!0,tabIndex:0,"data-state":i?"open":"closed","data-swipe-direction":k.swipeDirection,...y,ref:P,style:{userSelect:"none",touchAction:"none",...e.style},onKeyDown:u(e.onKeyDown,e=>{"Escape"!==e.key||(c?.(e.nativeEvent),e.nativeEvent.defaultPrevented||(k.isFocusedToastEscapeKeyDownRef.current=!0,_()))}),onPointerDown:u(e.onPointerDown,e=>{0===e.button&&(j.current={x:e.clientX,y:e.clientY})}),onPointerMove:u(e.onPointerMove,e=>{if(!j.current)return;let t=e.clientX-j.current.x,r=e.clientY-j.current.y,n=!!C.current,o=["left","right"].includes(k.swipeDirection),i=["left","up"].includes(k.swipeDirection)?Math.min:Math.max,s=o?i(0,t):0,a=o?0:i(0,r),l="touch"===e.pointerType?10:2,c={x:s,y:a},u={originalEvent:e,delta:c};n?(C.current=c,ng("toast.swipeMove",h,u,{discrete:!1})):nh(c,k.swipeDirection,l)?(C.current=c,ng("toast.swipeStart",m,u,{discrete:!1}),e.target.setPointerCapture(e.pointerId)):(Math.abs(t)>l||Math.abs(r)>l)&&(j.current=null)}),onPointerUp:u(e.onPointerUp,e=>{let t=C.current,r=e.target;if(r.hasPointerCapture(e.pointerId)&&r.releasePointerCapture(e.pointerId),C.current=null,j.current=null,t){let r=e.currentTarget,n={originalEvent:e,delta:t};nh(t,k.swipeDirection,k.swipeThreshold)?ng("toast.swipeEnd",x,n,{discrete:!0}):ng("toast.swipeCancel",b,n,{discrete:!0}),r.addEventListener("click",e=>e.preventDefault(),{once:!0})}})})})}),k.viewport)})]}):null}),na=e=>{let{__scopeToast:t,children:r,...n}=e,o=r6(nr,t),[i,a]=l.useState(!1),[c,u]=l.useState(!1);return function(e=()=>{}){let t=w(e);C(()=>{let e=0,r=0;return e=window.requestAnimationFrame(()=>r=window.requestAnimationFrame(t)),()=>{window.cancelAnimationFrame(e),window.cancelAnimationFrame(r)}},[t])}(()=>a(!0)),l.useEffect(()=>{let e=window.setTimeout(()=>u(!0),1e3);return()=>window.clearTimeout(e)},[]),c?null:(0,s.jsx)(tg,{asChild:!0,children:(0,s.jsx)(ty,{...n,children:i&&(0,s.jsxs)(s.Fragment,{children:[o.label," ",r]})})})},nl=l.forwardRef((e,t)=>{let{__scopeToast:r,...n}=e;return(0,s.jsx)(v.div,{...n,ref:t})});nl.displayName="ToastTitle";var nc=l.forwardRef((e,t)=>{let{__scopeToast:r,...n}=e;return(0,s.jsx)(v.div,{...n,ref:t})});nc.displayName="ToastDescription";var nu="ToastAction",nd=l.forwardRef((e,t)=>{let{altText:r,...n}=e;return r.trim()?(0,s.jsx)(nm,{altText:r,asChild:!0,children:(0,s.jsx)(np,{...n,ref:t})}):(console.error(`Invalid prop \`altText\` supplied to \`${nu}\`. Expected non-empty \`string\`.`),null)});nd.displayName=nu;var nf="ToastClose",np=l.forwardRef((e,t)=>{let{__scopeToast:r,...n}=e,o=ni(nf,r);return(0,s.jsx)(nm,{asChild:!0,children:(0,s.jsx)(v.button,{type:"button",...n,ref:t,onClick:u(e.onClick,o.onClose)})})});np.displayName=nf;var nm=l.forwardRef((e,t)=>{let{__scopeToast:r,altText:n,...o}=e;return(0,s.jsx)(v.div,{"data-radix-toast-announce-exclude":"","data-radix-toast-announce-alt":n||void 0,...o,ref:t})});function ng(e,t,r,{discrete:n}){let o=r.originalEvent.currentTarget,i=new CustomEvent(e,{bubbles:!0,cancelable:!0,detail:r});t&&o.addEventListener(e,t,{once:!0}),n?y(o,i):o.dispatchEvent(i)}var nh=(e,t,r=0)=>{let n=Math.abs(e.x),o=Math.abs(e.y),i=n>o;return"left"===t||"right"===t?i&&n>r:!i&&o>r};function nb(e){let t=document.activeElement;return e.some(e=>e===t||(e.focus(),document.activeElement!==t))}let nx=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,nv=tB.W;var ny=r(43020);let nw=l.forwardRef(({className:e,...t},r)=>s.jsx(r9,{ref:r,className:r$("fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]",e),...t}));nw.displayName=r9.displayName;let nk=(n="group pointer-events-auto relative flex w-full items-center justify-between space-x-4 overflow-hidden rounded-md border p-6 pr-8 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full",o={variants:{variant:{default:"border bg-background text-foreground",destructive:"destructive group border-destructive bg-destructive text-destructive-foreground"}},defaultVariants:{variant:"default"}},e=>{var t;if((null==o?void 0:o.variants)==null)return nv(n,null==e?void 0:e.class,null==e?void 0:e.className);let{variants:r,defaultVariants:i}=o,s=Object.keys(r).map(t=>{let n=null==e?void 0:e[t],o=null==i?void 0:i[t];if(null===n)return null;let s=nx(n)||nx(o);return r[t][s]}),a=e&&Object.entries(e).reduce((e,t)=>{let[r,n]=t;return void 0===n||(e[r]=n),e},{});return nv(n,s,null==o?void 0:null===(t=o.compoundVariants)||void 0===t?void 0:t.reduce((e,t)=>{let{class:r,className:n,...o}=t;return Object.entries(o).every(e=>{let[t,r]=e;return Array.isArray(r)?r.includes({...i,...a}[t]):({...i,...a})[t]===r})?[...e,r,n]:e},[]),null==e?void 0:e.class,null==e?void 0:e.className)}),nE=l.forwardRef(({className:e,variant:t,...r},n)=>s.jsx(nn,{ref:n,className:r$(nk({variant:t}),e),...r}));nE.displayName=nn.displayName,l.forwardRef(({className:e,...t},r)=>s.jsx(nd,{ref:r,className:r$("inline-flex h-8 shrink-0 items-center justify-center rounded-md border bg-transparent px-3 text-sm font-medium ring-offset-background transition-colors hover:bg-secondary focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 group-[.destructive]:border-muted/40 group-[.destructive]:hover:border-destructive/30 group-[.destructive]:hover:bg-destructive group-[.destructive]:hover:text-destructive-foreground group-[.destructive]:focus:ring-destructive",e),...t})).displayName=nd.displayName;let nT=l.forwardRef(({className:e,...t},r)=>s.jsx(np,{ref:r,className:r$("absolute right-2 top-2 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-2 group-hover:opacity-100 group-[.destructive]:text-red-300 group-[.destructive]:hover:text-red-50 group-[.destructive]:focus:ring-red-400 group-[.destructive]:focus:ring-offset-red-600",e),"toast-close":"",...t,children:s.jsx(ny.Z,{className:"h-4 w-4"})}));nT.displayName=np.displayName;let nR=l.forwardRef(({className:e,...t},r)=>s.jsx(nl,{ref:r,className:r$("text-sm font-semibold",e),...t}));nR.displayName=nl.displayName;let nP=l.forwardRef(({className:e,...t},r)=>s.jsx(nc,{ref:r,className:r$("text-sm opacity-90",e),...t}));function nj(){let{toasts:e}=function(){let[e,t]=l.useState(rU);return l.useEffect(()=>(rG.push(t),()=>{let e=rG.indexOf(t);e>-1&&rG.splice(e,1)}),[e]),{...e,toast:rX,dismiss:e=>rK({type:"DISMISS_TOAST",toastId:e})}}();return(0,s.jsxs)(r3,{children:[e.map(function({id:e,title:t,description:r,action:n,...o}){return(0,s.jsxs)(nE,{...o,children:[(0,s.jsxs)("div",{className:"grid gap-1",children:[t&&s.jsx(nR,{children:t}),r&&s.jsx(nP,{children:r})]}),n,s.jsx(nT,{})]},e)}),s.jsx(nw,{})]})}nP.displayName=nc.displayName;let nC=(0,a.default)(async()=>{},{loadableGenerated:{modules:["app\\census\\page.tsx -> ./components/EmpCensusApp"]},ssr:!1,loading:()=>s.jsx(nN,{})});function nN(){return s.jsx("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 flex items-center justify-center",children:(0,s.jsxs)("div",{className:"text-center",children:[s.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"}),s.jsx("p",{className:"text-gray-600",children:"Loading census application..."})]})})}function nS(){return s.jsx("div",{"data-page":"census",children:(0,s.jsxs)(tC,{children:[s.jsx(nj,{}),s.jsx(nC,{})]})})}},82219:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});let n=(0,r(68570).createProxy)(String.raw`C:\beno\project_dev\qharmony-frontend-v1\src\app\census\page.tsx#default`)},73881:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>o});var n=r(66621);let o=e=>[{type:"image/x-icon",sizes:"16x16",url:(0,n.fillMetadataSegment)(".",e.params,"favicon.ico")+""}]},9664:(e,t,r)=>{"use strict";r.d(t,{Z:()=>l});var n=r(17577);let o=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),i=(...e)=>e.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim();var s={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let a=(0,n.forwardRef)(({color:e="currentColor",size:t=24,strokeWidth:r=2,absoluteStrokeWidth:o,className:a="",children:l,iconNode:c,...u},d)=>(0,n.createElement)("svg",{ref:d,...s,width:t,height:t,stroke:e,strokeWidth:o?24*Number(r)/Number(t):r,className:i("lucide",a),...u},[...c.map(([e,t])=>(0,n.createElement)(e,t)),...Array.isArray(l)?l:[l]])),l=(e,t)=>{let r=(0,n.forwardRef)(({className:r,...s},l)=>(0,n.createElement)(a,{ref:l,iconNode:t,className:i(`lucide-${o(e)}`,r),...s}));return r.displayName=`${e}`,r}},43020:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n});let n=(0,r(9664).Z)("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[8948,1183,6621,576],()=>r(51998));module.exports=n})();