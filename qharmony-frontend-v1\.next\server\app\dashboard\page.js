(()=>{var e={};e.id=7702,e.ids=[7702],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},27790:e=>{"use strict";e.exports=require("assert")},84770:e=>{"use strict";e.exports=require("crypto")},17702:e=>{"use strict";e.exports=require("events")},92048:e=>{"use strict";e.exports=require("fs")},32615:e=>{"use strict";e.exports=require("http")},35240:e=>{"use strict";e.exports=require("https")},19801:e=>{"use strict";e.exports=require("os")},55315:e=>{"use strict";e.exports=require("path")},76162:e=>{"use strict";e.exports=require("stream")},74026:e=>{"use strict";e.exports=require("string_decoder")},74175:e=>{"use strict";e.exports=require("tty")},17360:e=>{"use strict";e.exports=require("url")},21764:e=>{"use strict";e.exports=require("util")},71568:e=>{"use strict";e.exports=require("zlib")},98061:e=>{"use strict";e.exports=require("node:assert")},92761:e=>{"use strict";e.exports=require("node:async_hooks")},72254:e=>{"use strict";e.exports=require("node:buffer")},40027:e=>{"use strict";e.exports=require("node:console")},6005:e=>{"use strict";e.exports=require("node:crypto")},65714:e=>{"use strict";e.exports=require("node:diagnostics_channel")},15673:e=>{"use strict";e.exports=require("node:events")},88849:e=>{"use strict";e.exports=require("node:http")},42725:e=>{"use strict";e.exports=require("node:http2")},87503:e=>{"use strict";e.exports=require("node:net")},38846:e=>{"use strict";e.exports=require("node:perf_hooks")},39630:e=>{"use strict";e.exports=require("node:querystring")},84492:e=>{"use strict";e.exports=require("node:stream")},31764:e=>{"use strict";e.exports=require("node:tls")},41041:e=>{"use strict";e.exports=require("node:url")},47261:e=>{"use strict";e.exports=require("node:util")},93746:e=>{"use strict";e.exports=require("node:util/types")},24086:e=>{"use strict";e.exports=require("node:worker_threads")},65628:e=>{"use strict";e.exports=require("node:zlib")},88137:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>a.a,__next_app__:()=>u,originalPathname:()=>p,pages:()=>d,routeModule:()=>x,tree:()=>c}),r(38256),r(33709),r(35866);var o=r(23191),i=r(88716),s=r(37922),a=r.n(s),n=r(95231),l={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>n[e]);r.d(t,l);let c=["",{children:["dashboard",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,38256)),"C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\dashboard\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,33709)),"C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,35866,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],d=["C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\dashboard\\page.tsx"],p="/dashboard/page",u={require:r,loadChunk:()=>Promise.resolve()},x=new o.AppPageRouteModule({definition:{kind:i.x.APP_PAGE,page:"/dashboard/page",pathname:"/dashboard",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},93066:(e,t,r)=>{Promise.resolve().then(r.bind(r,63330))},48260:(e,t,r)=>{"use strict";r.d(t,{Z:()=>j});var o=r(17577),i=r(41135),s=r(88634),a=r(87816),n=r(44823),l=r(91703),c=r(13643),d=r(40955),p=r(2791),u=r(49006),x=r(98139),h=r(54641),f=r(71685),m=r(97898);function g(e){return(0,m.ZP)("MuiIconButton",e)}let b=(0,f.Z)("MuiIconButton",["root","disabled","colorInherit","colorPrimary","colorSecondary","colorError","colorInfo","colorSuccess","colorWarning","edgeStart","edgeEnd","sizeSmall","sizeMedium","sizeLarge","loading","loadingIndicator","loadingWrapper"]);var v=r(10326);let y=e=>{let{classes:t,disabled:r,color:o,edge:i,size:a,loading:n}=e,l={root:["root",n&&"loading",r&&"disabled","default"!==o&&`color${(0,h.Z)(o)}`,i&&`edge${(0,h.Z)(i)}`,`size${(0,h.Z)(a)}`],loadingIndicator:["loadingIndicator"],loadingWrapper:["loadingWrapper"]};return(0,s.Z)(l,g,t)},D=(0,l.default)(u.Z,{name:"MuiIconButton",slot:"Root",overridesResolver:(e,t)=>{let{ownerState:r}=e;return[t.root,r.loading&&t.loading,"default"!==r.color&&t[`color${(0,h.Z)(r.color)}`],r.edge&&t[`edge${(0,h.Z)(r.edge)}`],t[`size${(0,h.Z)(r.size)}`]]}})((0,c.Z)(({theme:e})=>({textAlign:"center",flex:"0 0 auto",fontSize:e.typography.pxToRem(24),padding:8,borderRadius:"50%",color:(e.vars||e).palette.action.active,transition:e.transitions.create("background-color",{duration:e.transitions.duration.shortest}),variants:[{props:e=>!e.disableRipple,style:{"--IconButton-hoverBg":e.vars?`rgba(${e.vars.palette.action.activeChannel} / ${e.vars.palette.action.hoverOpacity})`:(0,n.Fq)(e.palette.action.active,e.palette.action.hoverOpacity),"&:hover":{backgroundColor:"var(--IconButton-hoverBg)","@media (hover: none)":{backgroundColor:"transparent"}}}},{props:{edge:"start"},style:{marginLeft:-12}},{props:{edge:"start",size:"small"},style:{marginLeft:-3}},{props:{edge:"end"},style:{marginRight:-12}},{props:{edge:"end",size:"small"},style:{marginRight:-3}}]})),(0,c.Z)(({theme:e})=>({variants:[{props:{color:"inherit"},style:{color:"inherit"}},...Object.entries(e.palette).filter((0,d.Z)()).map(([t])=>({props:{color:t},style:{color:(e.vars||e).palette[t].main}})),...Object.entries(e.palette).filter((0,d.Z)()).map(([t])=>({props:{color:t},style:{"--IconButton-hoverBg":e.vars?`rgba(${(e.vars||e).palette[t].mainChannel} / ${e.vars.palette.action.hoverOpacity})`:(0,n.Fq)((e.vars||e).palette[t].main,e.palette.action.hoverOpacity)}})),{props:{size:"small"},style:{padding:5,fontSize:e.typography.pxToRem(18)}},{props:{size:"large"},style:{padding:12,fontSize:e.typography.pxToRem(28)}}],[`&.${b.disabled}`]:{backgroundColor:"transparent",color:(e.vars||e).palette.action.disabled},[`&.${b.loading}`]:{color:"transparent"}}))),w=(0,l.default)("span",{name:"MuiIconButton",slot:"LoadingIndicator",overridesResolver:(e,t)=>t.loadingIndicator})(({theme:e})=>({display:"none",position:"absolute",visibility:"visible",top:"50%",left:"50%",transform:"translate(-50%, -50%)",color:(e.vars||e).palette.action.disabled,variants:[{props:{loading:!0},style:{display:"flex"}}]})),j=o.forwardRef(function(e,t){let r=(0,p.i)({props:e,name:"MuiIconButton"}),{edge:o=!1,children:s,className:n,color:l="default",disabled:c=!1,disableFocusRipple:d=!1,size:u="medium",id:h,loading:f=null,loadingIndicator:m,...g}=r,b=(0,a.Z)(h),j=m??(0,v.jsx)(x.Z,{"aria-labelledby":b,color:"inherit",size:16}),Z={...r,edge:o,color:l,disabled:c,disableFocusRipple:d,loading:f,loadingIndicator:j,size:u},q=y(Z);return(0,v.jsxs)(D,{id:f?b:h,className:(0,i.Z)(q.root,n),centerRipple:!0,focusRipple:!d,disabled:c||f,ref:t,...g,ownerState:Z,children:["boolean"==typeof f&&(0,v.jsx)("span",{className:q.loadingWrapper,style:{display:"contents"},children:(0,v.jsx)(w,{className:q.loadingIndicator,ownerState:Z,children:f&&j})}),s]})})},63330:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>C});var o=r(10326),i=r(6283),s=r(25609),a=r(16027),n=r(12549),l=r(43058),c=r(17577),d=r(33198),p=r(42265),u=r(89223),x=r(46226),h=r(32049),f=r(25842),m=r(35047);let g=[{label:"What benefits are available to me?",icon:"\uD83D\uDD25",description:"Top question asked"},{label:"How do I enroll in benefits?",icon:"\uD83E\uDDD9‍♂️"},{label:"What benefits are available to me?",icon:"\uD83D\uDD25",description:"Top question asked"},{label:"How do I enroll in benefits?",icon:"\uD83E\uDDD9‍♂️"},{label:"What benefits are available to me?",icon:"\uD83D\uDD25",description:"Top question asked"},{label:"How do I enroll in benefits?",icon:"\uD83E\uDDD9‍♂️"},{label:"What benefits are available to me?",icon:"\uD83D\uDD25",description:"Top question asked"},{label:"How do I enroll in benefits?",icon:"\uD83E\uDDD9‍♂️"},{label:"What benefits are available to me?",icon:"\uD83D\uDD25",description:"Top question asked"},{label:"How do I enroll in benefits?",icon:"\uD83E\uDDD9‍♂️"}],b=[{label:"What is covered under my healthcare insurance plan?",icon:"\uD83C\uDFDD️",description:"Top question asked"},{label:"How can I apply for the family medical leave act (FMLA)?",icon:"\uD83D\uDCC4"},{label:"What is covered under my healthcare insurance plan?",icon:"\uD83C\uDFDD️",description:"Top question asked"},{label:"How can I apply for the family medical leave act (FMLA)?",icon:"\uD83D\uDCC4"},{label:"What is covered under my healthcare insurance plan?",icon:"\uD83C\uDFDD️",description:"Top question asked"},{label:"How can I apply for the family medical leave act (FMLA)?",icon:"\uD83D\uDCC4"},{label:"What is covered under my healthcare insurance plan?",icon:"\uD83C\uDFDD️",description:"Top question asked"},{label:"How can I apply for the family medical leave act (FMLA)?",icon:"\uD83D\uDCC4"},{label:"What is covered under my healthcare insurance plan?",icon:"\uD83C\uDFDD️",description:"Top question asked"},{label:"How can I apply for the family medical leave act (FMLA)?",icon:"\uD83D\uDCC4"}],v={"@keyframes scrollRight":{"0%":{transform:"translateX(0)"},"100%":{transform:"translateX(-100%)"}},"@keyframes scrollLeft":{"0%":{transform:"translateX(-100%)"},"100%":{transform:"translateX(0)"}}};function y(){let e=(0,f.I0)(),t=(0,m.useRouter)(),[r,a]=(0,c.useState)(""),[n,l]=(0,c.useState)(!1),y=r=>{e((0,h.CS)(r)),t.push("/qHarmonyBot")};return(0,o.jsxs)(i.Z,{sx:{backgroundColor:"#ffffff",paddingY:4,mt:0,borderRadius:"30px"},children:[!n&&(0,o.jsxs)(i.Z,{sx:{display:"flex",alignItems:"center",mb:3,paddingX:3},children:[o.jsx(d.Z,{sx:{width:60,height:60,mr:2,overflow:"hidden"},children:o.jsx(x.default,{src:u.Z,alt:"Chat Avatar",layout:"fill",objectFit:"cover"})}),(0,o.jsxs)(i.Z,{children:[o.jsx(s.Z,{sx:{fontWeight:800,fontSize:"28px"},children:"Chat with Brea – Your benefits specialist"}),o.jsx(s.Z,{sx:{fontWeight:500,fontSize:"14px",color:"rgba(0, 0, 0, 0.6)"},children:"24/7 available"})]})]}),(0,o.jsxs)(i.Z,{sx:{overflow:"hidden",position:"relative"},children:[o.jsx(i.Z,{sx:{display:"flex",whiteSpace:"nowrap",animation:"scrollRight 30s linear infinite",...v,marginBottom:2},children:g.concat(g).map((e,t)=>(0,o.jsxs)(i.Z,{onClick:()=>y(e.label),sx:{display:"flex",alignItems:"center",px:2,py:1,mx:1,borderRadius:"12px",bgcolor:"#F6F6F6",minWidth:"300px",cursor:"pointer",flexShrink:0,overflow:"hidden",textOverflow:"ellipsis","&:hover":{backgroundColor:"#e9ecef"}},children:[o.jsx(s.Z,{variant:"body1",sx:{fontSize:"40px",marginRight:"10px"},children:e.icon}),(0,o.jsxs)(i.Z,{sx:{display:"flex",flexDirection:"column"},children:[o.jsx(s.Z,{variant:"body1",sx:{fontWeight:800,fontSize:"16px",whiteSpace:"nowrap",overflow:"hidden",textOverflow:"ellipsis"},children:e.label}),e.description&&o.jsx(s.Z,{variant:"caption",sx:{fontWeight:500,color:"#6c757d",fontSize:"13px",whiteSpace:"nowrap",overflow:"hidden",textOverflow:"ellipsis"},children:e.description})]})]},t))}),o.jsx(i.Z,{sx:{display:"flex",whiteSpace:"nowrap",animation:"scrollLeft 30s linear infinite",...v},children:b.concat(b).map((e,t)=>(0,o.jsxs)(i.Z,{onClick:()=>y(e.label),sx:{display:"flex",alignItems:"center",px:2,py:1,mx:1,borderRadius:"12px",bgcolor:"#F6F6F6",minWidth:"300px",cursor:"pointer",flexShrink:0,overflow:"hidden",textOverflow:"ellipsis","&:hover":{backgroundColor:"#e9ecef"}},children:[o.jsx(s.Z,{variant:"body1",sx:{fontSize:"40px",marginRight:"10px"},children:e.icon}),(0,o.jsxs)(i.Z,{sx:{display:"flex",flexDirection:"column"},children:[o.jsx(s.Z,{variant:"body1",sx:{fontWeight:800,fontSize:"16px",whiteSpace:"nowrap",overflow:"hidden",textOverflow:"ellipsis"},children:e.label}),e.description&&o.jsx(s.Z,{variant:"caption",sx:{fontWeight:500,color:"#6c757d",fontSize:"13px",whiteSpace:"nowrap",overflow:"hidden",textOverflow:"ellipsis"},children:e.description})]})]},t))})]}),(0,o.jsxs)(i.Z,{sx:{display:"flex",alignItems:"center",justifyContent:"space-between",mt:4,bgcolor:"#f1f3f5",mx:3,px:2,py:1,borderRadius:"100px"},children:[o.jsx("input",{type:"text",value:r,onChange:e=>{a(e.target.value)},placeholder:"How can I assist you?",style:{flex:1,border:"none",outline:"none",backgroundColor:"transparent",color:"black",fontSize:"16px",marginRight:"10px"}}),o.jsx(p.Z,{variant:"contained",onClick:()=>{r.trim()&&(y(r),a(""))},sx:{bgcolor:"#000000",color:"#ffffff",borderRadius:"100px",textTransform:"none",px:3},children:"Send"})]})]})}var D=r(31870),w=r(50648),j=r(19551),Z=r(18751);function q(){let e=(0,m.useRouter)(),t=(0,D.C)(e=>e.user.userProfile);return o.jsx(i.Z,{onClick:()=>{let r=localStorage.getItem("userid1")||localStorage.getItem("userId"),o=localStorage.getItem("ssoDone1"),i=localStorage.getItem("userEmail1");console.log("Census Auth Check:",{userId:r,ssoDone:o,userEmail:i,userDetailsName:t?.name,userDetailsEmail:t?.email}),r&&("true"===o||t?.name||t?.email||i)?(console.log("User authenticated, going to upload census"),e.push("/census?page=upload-census")):(console.log("User not authenticated, going to login prompt"),e.push("/census?page=login-prompt"))},sx:{backgroundColor:"white",padding:2,display:"flex",alignItems:"center",justifyContent:"space-between",borderRadius:"30px",boxShadow:"none",maxWidth:"100%",mt:3,cursor:"pointer",transition:"all 0.2s ease","&:hover":{boxShadow:"0 4px 12px rgba(0, 0, 0, 0.1)",transform:"translateY(-2px)"}},children:(0,o.jsxs)(i.Z,{sx:{display:"flex",alignItems:"center",flexDirection:"row"},children:[o.jsx(d.Z,{sx:{width:50,height:50,mr:2,backgroundColor:"#2563eb",background:"linear-gradient(135deg, #2563eb 0%, #8b5cf6 100%)"},children:o.jsx(Z.Z,{size:28,color:"white",style:{filter:"drop-shadow(0 0 2px rgba(255,255,255,0.3))"}})}),(0,o.jsxs)(i.Z,{children:[o.jsx(i.Z,{sx:{display:"flex",alignItems:"center",flexDirection:"row"},children:o.jsx(s.Z,{sx:{fontWeight:700,fontSize:"24px",display:"flex",alignItems:"center"},children:"Census"})}),o.jsx(s.Z,{sx:{fontWeight:500,fontSize:"14px",color:"#6c757d"},children:"Upload and analyze employee census data"})]})]})})}let C=(0,n.Z)(()=>{let e=(0,m.useRouter)(),t=(0,D.C)(e=>e.user.userProfile),[r,n]=(0,c.useState)(!1);return(0,c.useEffect)(()=>{},[]),(0,c.useEffect)(()=>{n("true"===localStorage.getItem("isTeamsApp1"))},[]),(0,c.useEffect)(()=>{localStorage.getItem("firstTimeLogin1")||(localStorage.setItem("firstTimeLogin1","true"),e.refresh())},[t]),o.jsx(l.Z,{children:(0,o.jsxs)(i.Z,{component:"main",sx:{flexGrow:1,p:4,bgcolor:"#F5F6FA",minHeight:"95vh"},children:[(0,o.jsxs)(s.Z,{variant:"h4",sx:{fontWeight:"bold",mb:1},children:["Hey, ",t.name.replace(/\b\w/g,e=>e.toUpperCase()),"!"]}),o.jsx(s.Z,{variant:"body1",sx:{color:"#6c757d",mb:4},children:"Let's make the most of your benefits today!"}),(0,o.jsxs)(a.ZP,{container:!0,spacing:2,children:[(0,o.jsxs)(a.ZP,{item:!0,xs:12,md:8,children:[o.jsx(y,{}),!r&&o.jsx(q,{})]}),(0,o.jsxs)(a.ZP,{item:!0,xs:12,md:4,children:[o.jsx(w.Z,{}),o.jsx(j.Z,{})]})]})]})})})},38256:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>o});let o=(0,r(68570).createProxy)(String.raw`C:\beno\project_dev\qharmony-frontend-v1\src\app\dashboard\page.tsx#default`)},9664:(e,t,r)=>{"use strict";r.d(t,{Z:()=>l});var o=r(17577);let i=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),s=(...e)=>e.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim();var a={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let n=(0,o.forwardRef)(({color:e="currentColor",size:t=24,strokeWidth:r=2,absoluteStrokeWidth:i,className:n="",children:l,iconNode:c,...d},p)=>(0,o.createElement)("svg",{ref:p,...a,width:t,height:t,stroke:e,strokeWidth:i?24*Number(r)/Number(t):r,className:s("lucide",n),...d},[...c.map(([e,t])=>(0,o.createElement)(e,t)),...Array.isArray(l)?l:[l]])),l=(e,t)=>{let r=(0,o.forwardRef)(({className:r,...a},l)=>(0,o.createElement)(n,{ref:l,iconNode:t,className:s(`lucide-${i(e)}`,r),...a}));return r.displayName=`${e}`,r}},18751:(e,t,r)=>{"use strict";r.d(t,{Z:()=>o});let o=(0,r(9664).Z)("ChartColumn",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]])}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),o=t.X(0,[8948,1183,6621,9066,1999,3253,928,8097,8522,3141,6027,541,2142,5560,9434,576,6305,401,2549,2779,8512],()=>r(88137));module.exports=o})();