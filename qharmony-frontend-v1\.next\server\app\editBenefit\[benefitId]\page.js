(()=>{var e={};e.id=5190,e.ids=[5190],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},27790:e=>{"use strict";e.exports=require("assert")},84770:e=>{"use strict";e.exports=require("crypto")},17702:e=>{"use strict";e.exports=require("events")},92048:e=>{"use strict";e.exports=require("fs")},32615:e=>{"use strict";e.exports=require("http")},35240:e=>{"use strict";e.exports=require("https")},19801:e=>{"use strict";e.exports=require("os")},55315:e=>{"use strict";e.exports=require("path")},76162:e=>{"use strict";e.exports=require("stream")},74026:e=>{"use strict";e.exports=require("string_decoder")},74175:e=>{"use strict";e.exports=require("tty")},17360:e=>{"use strict";e.exports=require("url")},21764:e=>{"use strict";e.exports=require("util")},71568:e=>{"use strict";e.exports=require("zlib")},98061:e=>{"use strict";e.exports=require("node:assert")},92761:e=>{"use strict";e.exports=require("node:async_hooks")},72254:e=>{"use strict";e.exports=require("node:buffer")},40027:e=>{"use strict";e.exports=require("node:console")},6005:e=>{"use strict";e.exports=require("node:crypto")},65714:e=>{"use strict";e.exports=require("node:diagnostics_channel")},15673:e=>{"use strict";e.exports=require("node:events")},88849:e=>{"use strict";e.exports=require("node:http")},42725:e=>{"use strict";e.exports=require("node:http2")},87503:e=>{"use strict";e.exports=require("node:net")},38846:e=>{"use strict";e.exports=require("node:perf_hooks")},39630:e=>{"use strict";e.exports=require("node:querystring")},84492:e=>{"use strict";e.exports=require("node:stream")},31764:e=>{"use strict";e.exports=require("node:tls")},41041:e=>{"use strict";e.exports=require("node:url")},47261:e=>{"use strict";e.exports=require("node:util")},93746:e=>{"use strict";e.exports=require("node:util/types")},24086:e=>{"use strict";e.exports=require("node:worker_threads")},65628:e=>{"use strict";e.exports=require("node:zlib")},92483:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>a.a,__next_app__:()=>p,originalPathname:()=>u,pages:()=>c,routeModule:()=>f,tree:()=>d}),r(41530),r(33709),r(35866);var o=r(23191),n=r(88716),i=r(37922),a=r.n(i),s=r(95231),l={};for(let e in s)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>s[e]);r.d(t,l);let d=["",{children:["editBenefit",{children:["[benefitId]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,41530)),"C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\editBenefit\\[benefitId]\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,33709)),"C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,35866,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],c=["C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\editBenefit\\[benefitId]\\page.tsx"],u="/editBenefit/[benefitId]/page",p={require:r,loadChunk:()=>Promise.resolve()},f=new o.AppPageRouteModule({definition:{kind:n.x.APP_PAGE,page:"/editBenefit/[benefitId]/page",pathname:"/editBenefit/[benefitId]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},51956:(e,t,r)=>{Promise.resolve().then(r.bind(r,24794))},33790:(e,t,r)=>{"use strict";r.d(t,{Z:()=>i});var o=r(51426),n=r(10326);let i=(0,o.Z)((0,n.jsx)("path",{d:"M17.77 3.77 16 2 6 12l10 10 1.77-1.77L9.54 12z"}),"ArrowBackIosNew")},59351:(e,t,r)=>{"use strict";r.d(t,{Z:()=>P});var o=r(17577),n=r(41135),i=r(88634),a=r(44823),s=r(91703),l=r(13643),d=r(2791),c=r(31121),u=r(54641),p=r(40955),f=r(89178),g=r(71685),m=r(97898);function x(e){return(0,m.ZP)("MuiAlert",e)}let h=(0,g.Z)("MuiAlert",["root","action","icon","message","filled","colorSuccess","colorInfo","colorWarning","colorError","filledSuccess","filledInfo","filledWarning","filledError","outlined","outlinedSuccess","outlinedInfo","outlinedWarning","outlinedError","standard","standardSuccess","standardInfo","standardWarning","standardError"]);var b=r(48260),v=r(51426),y=r(10326);let Z=(0,v.Z)((0,y.jsx)("path",{d:"M20,12A8,8 0 0,1 12,20A8,8 0 0,1 4,12A8,8 0 0,1 12,4C12.76,4 13.5,4.11 14.2, 4.31L15.77,2.74C14.61,2.26 13.34,2 12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0, 0 22,12M7.91,10.08L6.5,11.5L11,16L21,6L19.59,4.58L11,13.17L7.91,10.08Z"}),"SuccessOutlined"),j=(0,v.Z)((0,y.jsx)("path",{d:"M12 5.99L19.53 19H4.47L12 5.99M12 2L1 21h22L12 2zm1 14h-2v2h2v-2zm0-6h-2v4h2v-4z"}),"ReportProblemOutlined"),k=(0,v.Z)((0,y.jsx)("path",{d:"M11 15h2v2h-2zm0-8h2v6h-2zm.99-5C6.47 2 2 6.48 2 12s4.47 10 9.99 10C17.52 22 22 17.52 22 12S17.52 2 11.99 2zM12 20c-4.42 0-8-3.58-8-8s3.58-8 8-8 8 3.58 8 8-3.58 8-8 8z"}),"ErrorOutline"),w=(0,v.Z)((0,y.jsx)("path",{d:"M11,9H13V7H11M12,20C7.59,20 4,16.41 4,12C4,7.59 7.59,4 12,4C16.41,4 20,7.59 20, 12C20,16.41 16.41,20 12,20M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10, 10 0 0,0 12,2M11,17H13V11H11V17Z"}),"InfoOutlined"),C=(0,v.Z)((0,y.jsx)("path",{d:"M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"}),"Close"),S=e=>{let{variant:t,color:r,severity:o,classes:n}=e,a={root:["root",`color${(0,u.Z)(r||o)}`,`${t}${(0,u.Z)(r||o)}`,`${t}`],icon:["icon"],message:["message"],action:["action"]};return(0,i.Z)(a,x,n)},E=(0,s.default)(f.Z,{name:"MuiAlert",slot:"Root",overridesResolver:(e,t)=>{let{ownerState:r}=e;return[t.root,t[r.variant],t[`${r.variant}${(0,u.Z)(r.color||r.severity)}`]]}})((0,l.Z)(({theme:e})=>{let t="light"===e.palette.mode?a._j:a.$n,r="light"===e.palette.mode?a.$n:a._j;return{...e.typography.body2,backgroundColor:"transparent",display:"flex",padding:"6px 16px",variants:[...Object.entries(e.palette).filter((0,p.Z)(["light"])).map(([o])=>({props:{colorSeverity:o,variant:"standard"},style:{color:e.vars?e.vars.palette.Alert[`${o}Color`]:t(e.palette[o].light,.6),backgroundColor:e.vars?e.vars.palette.Alert[`${o}StandardBg`]:r(e.palette[o].light,.9),[`& .${h.icon}`]:e.vars?{color:e.vars.palette.Alert[`${o}IconColor`]}:{color:e.palette[o].main}}})),...Object.entries(e.palette).filter((0,p.Z)(["light"])).map(([r])=>({props:{colorSeverity:r,variant:"outlined"},style:{color:e.vars?e.vars.palette.Alert[`${r}Color`]:t(e.palette[r].light,.6),border:`1px solid ${(e.vars||e).palette[r].light}`,[`& .${h.icon}`]:e.vars?{color:e.vars.palette.Alert[`${r}IconColor`]}:{color:e.palette[r].main}}})),...Object.entries(e.palette).filter((0,p.Z)(["dark"])).map(([t])=>({props:{colorSeverity:t,variant:"filled"},style:{fontWeight:e.typography.fontWeightMedium,...e.vars?{color:e.vars.palette.Alert[`${t}FilledColor`],backgroundColor:e.vars.palette.Alert[`${t}FilledBg`]}:{backgroundColor:"dark"===e.palette.mode?e.palette[t].dark:e.palette[t].main,color:e.palette.getContrastText(e.palette[t].main)}}}))]}})),I=(0,s.default)("div",{name:"MuiAlert",slot:"Icon",overridesResolver:(e,t)=>t.icon})({marginRight:12,padding:"7px 0",display:"flex",fontSize:22,opacity:.9}),M=(0,s.default)("div",{name:"MuiAlert",slot:"Message",overridesResolver:(e,t)=>t.message})({padding:"8px 0",minWidth:0,overflow:"auto"}),R=(0,s.default)("div",{name:"MuiAlert",slot:"Action",overridesResolver:(e,t)=>t.action})({display:"flex",alignItems:"flex-start",padding:"4px 0 0 16px",marginLeft:"auto",marginRight:-8}),z={success:(0,y.jsx)(Z,{fontSize:"inherit"}),warning:(0,y.jsx)(j,{fontSize:"inherit"}),error:(0,y.jsx)(k,{fontSize:"inherit"}),info:(0,y.jsx)(w,{fontSize:"inherit"})},P=o.forwardRef(function(e,t){let r=(0,d.i)({props:e,name:"MuiAlert"}),{action:o,children:i,className:a,closeText:s="Close",color:l,components:u={},componentsProps:p={},icon:f,iconMapping:g=z,onClose:m,role:x="alert",severity:h="success",slotProps:v={},slots:Z={},variant:j="standard",...k}=r,w={...r,color:l,severity:h,variant:j,colorSeverity:l||h},P=S(w),L={slots:{closeButton:u.CloseButton,closeIcon:u.CloseIcon,...Z},slotProps:{...p,...v}},[A,q]=(0,c.Z)("root",{ref:t,shouldForwardComponentProp:!0,className:(0,n.Z)(P.root,a),elementType:E,externalForwardedProps:{...L,...k},ownerState:w,additionalProps:{role:x,elevation:0}}),[T,O]=(0,c.Z)("icon",{className:P.icon,elementType:I,externalForwardedProps:L,ownerState:w}),[$,B]=(0,c.Z)("message",{className:P.message,elementType:M,externalForwardedProps:L,ownerState:w}),[_,F]=(0,c.Z)("action",{className:P.action,elementType:R,externalForwardedProps:L,ownerState:w}),[W,D]=(0,c.Z)("closeButton",{elementType:b.Z,externalForwardedProps:L,ownerState:w}),[N,H]=(0,c.Z)("closeIcon",{elementType:C,externalForwardedProps:L,ownerState:w});return(0,y.jsxs)(A,{...q,children:[!1!==f?(0,y.jsx)(T,{...O,children:f||g[h]||z[h]}):null,(0,y.jsx)($,{...B,children:i}),null!=o?(0,y.jsx)(_,{...F,children:o}):null,null==o&&m?(0,y.jsx)(_,{...F,children:(0,y.jsx)(W,{size:"small","aria-label":s,title:s,color:"inherit",onClick:m,...D,children:(0,y.jsx)(N,{fontSize:"small",...H})})}):null]})})},37104:(e,t,r)=>{"use strict";r.d(t,{Z:()=>x});var o=r(17577),n=r(41135),i=r(88634),a=r(27080),s=r(91703),l=r(2791),d=r(25609),c=r(71685),u=r(97898);function p(e){return(0,u.ZP)("MuiDialogContentText",e)}(0,c.Z)("MuiDialogContentText",["root"]);var f=r(10326);let g=e=>{let{classes:t}=e,r=(0,i.Z)({root:["root"]},p,t);return{...t,...r}},m=(0,s.default)(d.Z,{shouldForwardProp:e=>(0,a.Z)(e)||"classes"===e,name:"MuiDialogContentText",slot:"Root",overridesResolver:(e,t)=>t.root})({}),x=o.forwardRef(function(e,t){let r=(0,l.i)({props:e,name:"MuiDialogContentText"}),{children:o,className:i,...a}=r,s=g(a);return(0,f.jsx)(m,{component:"p",variant:"body1",color:"textSecondary",ref:t,ownerState:a,className:(0,n.Z)(s.root,i),...r,classes:s})})},48260:(e,t,r)=>{"use strict";r.d(t,{Z:()=>k});var o=r(17577),n=r(41135),i=r(88634),a=r(87816),s=r(44823),l=r(91703),d=r(13643),c=r(40955),u=r(2791),p=r(49006),f=r(98139),g=r(54641),m=r(71685),x=r(97898);function h(e){return(0,x.ZP)("MuiIconButton",e)}let b=(0,m.Z)("MuiIconButton",["root","disabled","colorInherit","colorPrimary","colorSecondary","colorError","colorInfo","colorSuccess","colorWarning","edgeStart","edgeEnd","sizeSmall","sizeMedium","sizeLarge","loading","loadingIndicator","loadingWrapper"]);var v=r(10326);let y=e=>{let{classes:t,disabled:r,color:o,edge:n,size:a,loading:s}=e,l={root:["root",s&&"loading",r&&"disabled","default"!==o&&`color${(0,g.Z)(o)}`,n&&`edge${(0,g.Z)(n)}`,`size${(0,g.Z)(a)}`],loadingIndicator:["loadingIndicator"],loadingWrapper:["loadingWrapper"]};return(0,i.Z)(l,h,t)},Z=(0,l.default)(p.Z,{name:"MuiIconButton",slot:"Root",overridesResolver:(e,t)=>{let{ownerState:r}=e;return[t.root,r.loading&&t.loading,"default"!==r.color&&t[`color${(0,g.Z)(r.color)}`],r.edge&&t[`edge${(0,g.Z)(r.edge)}`],t[`size${(0,g.Z)(r.size)}`]]}})((0,d.Z)(({theme:e})=>({textAlign:"center",flex:"0 0 auto",fontSize:e.typography.pxToRem(24),padding:8,borderRadius:"50%",color:(e.vars||e).palette.action.active,transition:e.transitions.create("background-color",{duration:e.transitions.duration.shortest}),variants:[{props:e=>!e.disableRipple,style:{"--IconButton-hoverBg":e.vars?`rgba(${e.vars.palette.action.activeChannel} / ${e.vars.palette.action.hoverOpacity})`:(0,s.Fq)(e.palette.action.active,e.palette.action.hoverOpacity),"&:hover":{backgroundColor:"var(--IconButton-hoverBg)","@media (hover: none)":{backgroundColor:"transparent"}}}},{props:{edge:"start"},style:{marginLeft:-12}},{props:{edge:"start",size:"small"},style:{marginLeft:-3}},{props:{edge:"end"},style:{marginRight:-12}},{props:{edge:"end",size:"small"},style:{marginRight:-3}}]})),(0,d.Z)(({theme:e})=>({variants:[{props:{color:"inherit"},style:{color:"inherit"}},...Object.entries(e.palette).filter((0,c.Z)()).map(([t])=>({props:{color:t},style:{color:(e.vars||e).palette[t].main}})),...Object.entries(e.palette).filter((0,c.Z)()).map(([t])=>({props:{color:t},style:{"--IconButton-hoverBg":e.vars?`rgba(${(e.vars||e).palette[t].mainChannel} / ${e.vars.palette.action.hoverOpacity})`:(0,s.Fq)((e.vars||e).palette[t].main,e.palette.action.hoverOpacity)}})),{props:{size:"small"},style:{padding:5,fontSize:e.typography.pxToRem(18)}},{props:{size:"large"},style:{padding:12,fontSize:e.typography.pxToRem(28)}}],[`&.${b.disabled}`]:{backgroundColor:"transparent",color:(e.vars||e).palette.action.disabled},[`&.${b.loading}`]:{color:"transparent"}}))),j=(0,l.default)("span",{name:"MuiIconButton",slot:"LoadingIndicator",overridesResolver:(e,t)=>t.loadingIndicator})(({theme:e})=>({display:"none",position:"absolute",visibility:"visible",top:"50%",left:"50%",transform:"translate(-50%, -50%)",color:(e.vars||e).palette.action.disabled,variants:[{props:{loading:!0},style:{display:"flex"}}]})),k=o.forwardRef(function(e,t){let r=(0,u.i)({props:e,name:"MuiIconButton"}),{edge:o=!1,children:i,className:s,color:l="default",disabled:d=!1,disableFocusRipple:c=!1,size:p="medium",id:g,loading:m=null,loadingIndicator:x,...h}=r,b=(0,a.Z)(g),k=x??(0,v.jsx)(f.Z,{"aria-labelledby":b,color:"inherit",size:16}),w={...r,edge:o,color:l,disabled:d,disableFocusRipple:c,loading:m,loadingIndicator:k,size:p},C=y(w);return(0,v.jsxs)(Z,{id:m?b:g,className:(0,n.Z)(C.root,s),centerRipple:!0,focusRipple:!c,disabled:d||m,ref:t,...h,ownerState:w,children:["boolean"==typeof m&&(0,v.jsx)("span",{className:C.loadingWrapper,style:{display:"contents"},children:(0,v.jsx)(j,{className:C.loadingIndicator,ownerState:w,children:m&&k})}),i]})})},24794:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>ef});var o=r(10326),n=r(17577),i=r(31870),a=r(69058),s=r(35047),l=r(6283),d=r(42265),c=r(25609),u=r(98139),p=r(48260),f=r(16027),g=r(14326),m=r(99207),x=r(88634),h=r(34526),b=r(11987),v=r(90010);let y=function(e={}){let{autoHideDuration:t=null,disableWindowBlurListener:r=!1,onClose:o,open:i,resumeHideDuration:a}=e,s=(0,h.Z)();n.useEffect(()=>{if(i)return document.addEventListener("keydown",e),()=>{document.removeEventListener("keydown",e)};function e(e){e.defaultPrevented||"Escape"!==e.key||o?.(e,"escapeKeyDown")}},[i,o]);let l=(0,b.Z)((e,t)=>{o?.(e,t)}),d=(0,b.Z)(e=>{o&&null!=e&&s.start(e,()=>{l(null,"timeout")})});n.useEffect(()=>(i&&d(t),s.clear),[i,t,d,s]);let c=s.clear,u=n.useCallback(()=>{null!=t&&d(null!=a?a:.5*t)},[t,a,d]),p=e=>t=>{let r=e.onBlur;r?.(t),u()},f=e=>t=>{let r=e.onFocus;r?.(t),c()},g=e=>t=>{let r=e.onMouseEnter;r?.(t),c()},m=e=>t=>{let r=e.onMouseLeave;r?.(t),u()};return n.useEffect(()=>{if(!r&&i)return window.addEventListener("focus",u),window.addEventListener("blur",c),()=>{window.removeEventListener("focus",u),window.removeEventListener("blur",c)}},[r,i,u,c]),{getRootProps:(t={})=>{let r={...(0,v.Z)(e),...(0,v.Z)(t)};return{role:"presentation",...t,...r,onBlur:p(r),onFocus:f(r),onMouseEnter:g(r),onMouseLeave:m(r)}},onClickAway:e=>{o?.(e,"clickaway")}}};var Z=r(72823),j=r(34963),k=r(83784);function w(e){return e.substring(2).toLowerCase()}function C(e){let{children:t,disableReactTree:r=!1,mouseEvent:o="onClick",onClickAway:i,touchEvent:a="onTouchEnd"}=e,s=n.useRef(!1),l=n.useRef(null),d=n.useRef(!1),c=n.useRef(!1);n.useEffect(()=>(setTimeout(()=>{d.current=!0},0),()=>{d.current=!1}),[]);let u=(0,Z.Z)((0,k.Z)(t),l),p=(0,b.Z)(e=>{let t=c.current;c.current=!1;let o=(0,j.Z)(l.current);if(d.current&&l.current&&(!("clientX"in e)||!(o.documentElement.clientWidth<e.clientX)&&!(o.documentElement.clientHeight<e.clientY))){if(s.current){s.current=!1;return}(e.composedPath?e.composedPath().includes(l.current):!o.documentElement.contains(e.target)||l.current.contains(e.target))||!r&&t||i(e)}}),f=e=>r=>{c.current=!0;let o=t.props[e];o&&o(r)},g={ref:u};return!1!==a&&(g[a]=f(a)),n.useEffect(()=>{if(!1!==a){let e=w(a),t=(0,j.Z)(l.current),r=()=>{s.current=!0};return t.addEventListener(e,p),t.addEventListener("touchmove",r),()=>{t.removeEventListener(e,p),t.removeEventListener("touchmove",r)}}},[p,a]),!1!==o&&(g[o]=f(o)),n.useEffect(()=>{if(!1!==o){let e=w(o),t=(0,j.Z)(l.current);return t.addEventListener(e,p),()=>{t.removeEventListener(e,p)}}},[p,o]),n.cloneElement(t,g)}var S=r(91703),E=r(23743),I=r(13643),M=r(2791),R=r(54641),z=r(14962),P=r(41135),L=r(44823),A=r(89178),q=r(71685),T=r(97898);function O(e){return(0,T.ZP)("MuiSnackbarContent",e)}(0,q.Z)("MuiSnackbarContent",["root","message","action"]);let $=e=>{let{classes:t}=e;return(0,x.Z)({root:["root"],action:["action"],message:["message"]},O,t)},B=(0,S.default)(A.Z,{name:"MuiSnackbarContent",slot:"Root",overridesResolver:(e,t)=>t.root})((0,I.Z)(({theme:e})=>{let t="light"===e.palette.mode?.8:.98,r=(0,L._4)(e.palette.background.default,t);return{...e.typography.body2,color:e.vars?e.vars.palette.SnackbarContent.color:e.palette.getContrastText(r),backgroundColor:e.vars?e.vars.palette.SnackbarContent.bg:r,display:"flex",alignItems:"center",flexWrap:"wrap",padding:"6px 16px",borderRadius:(e.vars||e).shape.borderRadius,flexGrow:1,[e.breakpoints.up("sm")]:{flexGrow:"initial",minWidth:288}}})),_=(0,S.default)("div",{name:"MuiSnackbarContent",slot:"Message",overridesResolver:(e,t)=>t.message})({padding:"8px 0"}),F=(0,S.default)("div",{name:"MuiSnackbarContent",slot:"Action",overridesResolver:(e,t)=>t.action})({display:"flex",alignItems:"center",marginLeft:"auto",paddingLeft:16,marginRight:-8}),W=n.forwardRef(function(e,t){let r=(0,M.i)({props:e,name:"MuiSnackbarContent"}),{action:n,className:i,message:a,role:s="alert",...l}=r,d=$(r);return(0,o.jsxs)(B,{role:s,square:!0,elevation:6,className:(0,P.Z)(d.root,i),ownerState:r,ref:t,...l,children:[(0,o.jsx)(_,{className:d.message,ownerState:r,children:a}),n?(0,o.jsx)(F,{className:d.action,ownerState:r,children:n}):null]})});function D(e){return(0,T.ZP)("MuiSnackbar",e)}(0,q.Z)("MuiSnackbar",["root","anchorOriginTopCenter","anchorOriginBottomCenter","anchorOriginTopRight","anchorOriginBottomRight","anchorOriginTopLeft","anchorOriginBottomLeft"]);var N=r(31121);let H=e=>{let{classes:t,anchorOrigin:r}=e,o={root:["root",`anchorOrigin${(0,R.Z)(r.vertical)}${(0,R.Z)(r.horizontal)}`]};return(0,x.Z)(o,D,t)},G=(0,S.default)("div",{name:"MuiSnackbar",slot:"Root",overridesResolver:(e,t)=>{let{ownerState:r}=e;return[t.root,t[`anchorOrigin${(0,R.Z)(r.anchorOrigin.vertical)}${(0,R.Z)(r.anchorOrigin.horizontal)}`]]}})((0,I.Z)(({theme:e})=>({zIndex:(e.vars||e).zIndex.snackbar,position:"fixed",display:"flex",left:8,right:8,justifyContent:"center",alignItems:"center",variants:[{props:({ownerState:e})=>"top"===e.anchorOrigin.vertical,style:{top:8,[e.breakpoints.up("sm")]:{top:24}}},{props:({ownerState:e})=>"top"!==e.anchorOrigin.vertical,style:{bottom:8,[e.breakpoints.up("sm")]:{bottom:24}}},{props:({ownerState:e})=>"left"===e.anchorOrigin.horizontal,style:{justifyContent:"flex-start",[e.breakpoints.up("sm")]:{left:24,right:"auto"}}},{props:({ownerState:e})=>"right"===e.anchorOrigin.horizontal,style:{justifyContent:"flex-end",[e.breakpoints.up("sm")]:{right:24,left:"auto"}}},{props:({ownerState:e})=>"center"===e.anchorOrigin.horizontal,style:{[e.breakpoints.up("sm")]:{left:"50%",right:"auto",transform:"translateX(-50%)"}}}]}))),K=n.forwardRef(function(e,t){let r=(0,M.i)({props:e,name:"MuiSnackbar"}),i=(0,E.default)(),a={enter:i.transitions.duration.enteringScreen,exit:i.transitions.duration.leavingScreen},{action:s,anchorOrigin:{vertical:l,horizontal:d}={vertical:"bottom",horizontal:"left"},autoHideDuration:c=null,children:u,className:p,ClickAwayListenerProps:f,ContentProps:g,disableWindowBlurListener:m=!1,message:x,onBlur:h,onClose:b,onFocus:v,onMouseEnter:Z,onMouseLeave:j,open:k,resumeHideDuration:w,slots:S={},slotProps:I={},TransitionComponent:R,transitionDuration:P=a,TransitionProps:{onEnter:L,onExited:A,...q}={},...T}=r,O={...r,anchorOrigin:{vertical:l,horizontal:d},autoHideDuration:c,disableWindowBlurListener:m,TransitionComponent:R,transitionDuration:P},$=H(O),{getRootProps:B,onClickAway:_}=y({...O}),[F,D]=n.useState(!0),K=e=>{D(!0),A&&A(e)},Y=(e,t)=>{D(!1),L&&L(e,t)},U={slots:{transition:R,...S},slotProps:{content:g,clickAwayListener:f,transition:q,...I}},[V,X]=(0,N.Z)("root",{ref:t,className:[$.root,p],elementType:G,getSlotProps:B,externalForwardedProps:{...U,...T},ownerState:O}),[J,{ownerState:Q,...ee}]=(0,N.Z)("clickAwayListener",{elementType:C,externalForwardedProps:U,getSlotProps:e=>({onClickAway:(...t)=>{e.onClickAway?.(...t),_(...t)}}),ownerState:O}),[et,er]=(0,N.Z)("content",{elementType:W,shouldForwardComponentProp:!0,externalForwardedProps:U,additionalProps:{message:x,action:s},ownerState:O}),[eo,en]=(0,N.Z)("transition",{elementType:z.Z,externalForwardedProps:U,getSlotProps:e=>({onEnter:(...t)=>{e.onEnter?.(...t),Y(...t)},onExited:(...t)=>{e.onExited?.(...t),K(...t)}}),additionalProps:{appear:!0,in:k,timeout:P,direction:"top"===l?"down":"up"},ownerState:O});return!k&&F?null:(0,o.jsx)(J,{...ee,...S.clickAwayListener&&{ownerState:Q},children:(0,o.jsx)(V,{...X,children:(0,o.jsx)(eo,{...en,children:u||(0,o.jsx)(et,{...er})})})})});var Y=r(59351),U=r(69995),V=r(98117),X=r(28591),J=r(37104),Q=r(90541),ee=r(10163),et=r(33790),er=r(51426);let eo=(0,er.Z)((0,o.jsx)("path",{d:"M12 2C6.47 2 2 6.47 2 12s4.47 10 10 10 10-4.47 10-10S17.53 2 12 2m5 13.59L15.59 17 12 13.41 8.41 17 7 15.59 10.59 12 7 8.41 8.41 7 12 10.59 15.59 7 17 8.41 13.41 12z"}),"Cancel"),en=(0,er.Z)((0,o.jsx)("path",{d:"M19 6.41 17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"}),"Clear");var ei=r(25842),ea=r(25748),es=r(32049),el=r(12549),ed=r(43058),ec=r(23371),eu=r(78074),ep=r(94638);let ef=(0,el.Z)(()=>{let{benefitId:e}=(0,s.useParams)(),t=(0,s.useRouter)(),r=(0,i.T)(),x=(0,n.useRef)(null),[h,b]=(0,n.useState)(""),[v,y]=(0,n.useState)(!1),[Z,j]=(0,n.useState)(null),[k,w]=(0,n.useState)(!1),[C,S]=(0,n.useState)(""),[E,I]=(0,n.useState)(null),[M,R]=(0,n.useState)(null),[z,P]=(0,n.useState)(!1),[L,A]=(0,n.useState)(null),[q,T]=(0,n.useState)(!1),O=(0,ei.v9)(e=>(0,es.MP)(e)),$=(0,i.C)(e=>e.benefits.documentsPerBenefit),B=(0,i.C)(e=>e.benefits.viewableDocuments),_=(0,i.C)(e=>e.benefits.loadingDocuments),F=(0,ei.v9)(t=>(0,ea.d8)(t,e)),W=(0,i.C)(e=>e.benefits.snackbarMessage);(0,n.useEffect)(()=>{ec.j2().then(()=>{ec.fw().then(e=>{e.app.host.name===eu.UB.teams&&T(!0)})})},[]),(0,n.useEffect)(()=>{console.log("documents:",$)},[$]),(0,n.useEffect)(()=>{O&&e&&(0,a.v0)(r,e,O,"edit_benefit")},[O,e,r]);let D=(e,t)=>{window.open(`https://api.benosphere.com/benefits/document?objectKey=${e}&companyId=${t}`,"_blank")},N=e=>{I(e),w(!0)},H=e=>{R(e),w(!0)},G=()=>{w(!1),S(""),I(null),R(null)},er=async()=>{"delete link"===C&&null!==M&&O&&e&&(A(M),G(),await (0,a.fH)(r,e,O,$.links[M]),A(null),R(null))},el=async()=>{h&&(P(!0),await (0,a.SS)(r,e,O,h),b(""),P(!1))},ef=()=>{r((0,ea.Sn)())};return o.jsx(ed.Z,{children:(0,o.jsxs)(l.Z,{sx:{bgcolor:"#F5F6F8",minHeight:"98vh",padding:"32px"},children:[o.jsx(l.Z,{sx:{display:"flex",alignItems:"center",mb:3},children:(0,o.jsxs)(d.Z,{startIcon:o.jsx(et.Z,{sx:{fontSize:16}}),onClick:()=>t.back(),sx:{color:"#6c757d",fontWeight:"normal",textTransform:"none",fontSize:"1.2rem","&:hover":{bgcolor:"transparent"}},children:[(0,ep.Ur)(F?.benefitType||"")," /",o.jsx("span",{style:{fontWeight:"bold",color:"#000000",marginLeft:5},children:(0,ep.dA)(F?.benefit?.subType||"")})]})}),(0,o.jsxs)(l.Z,{sx:{bgcolor:"#ffffff",borderRadius:"12px",padding:"32px",boxShadow:"0px 4px 12px rgba(0, 0, 0, 0.1)"},children:[o.jsx(c.Z,{variant:"h4",sx:{fontWeight:600,fontSize:"28px",mb:2},children:(0,ep.dA)(F?.benefit?.subType||"")}),o.jsx(c.Z,{variant:"body1",sx:{color:"#6c757d",mb:3},children:"You can find all your health insurance details here, including coverage options, policy documents, and claim information."}),(0,o.jsxs)(l.Z,{sx:{display:"flex",flexWrap:"wrap",gap:"35px",overflowY:"auto",maxHeight:"300px"},children:[$.documents.map((e,t)=>{let r=B.find(t=>t.documentObjectKey===e);console.log("viewable document >>>",r);let n=["linear-gradient(135deg, #6a11cb 0%, #2575fc 100%)","linear-gradient(135deg, #ff7e5f 0%, #feb47b 100%)","linear-gradient(135deg, #43cea2 0%, #185a9d 100%)","linear-gradient(135deg, #ffafbd 0%, #ffc3a0 100%)","linear-gradient(135deg, #00c6ff 0%, #0072ff 100%)"],i=n[t%n.length];return(0,o.jsxs)(l.Z,{sx:{position:"relative",width:"160px",height:"245px",display:"flex",flexDirection:"column",alignItems:"flex-start",justifyContent:"flex-start"},children:[o.jsx(l.Z,{sx:{width:"160px",minHeight:"215px",borderRadius:"12px",overflow:"hidden",boxShadow:"0px 4px 12px rgba(0, 0, 0, 0.1)",position:"relative",display:"flex",alignItems:"center",justifyContent:"center",background:i,color:"#ffffff",cursor:"pointer",textAlign:"center",padding:"8px"},onClick:()=>D(r.documentObjectKey,O),children:_.includes(e)||!r?o.jsx(u.Z,{}):o.jsx(c.Z,{sx:{fontSize:"16px",fontWeight:"bold",wordWrap:"break-word",overflow:"hidden",textOverflow:"ellipsis",maxHeight:"100%",display:"-webkit-box",WebkitLineClamp:3,WebkitBoxOrient:"vertical"},children:r?.originalFileName||"Document Preview"})}),o.jsx(p.Z,{className:"delete-icon",onClick:()=>N(e),sx:{position:"absolute",top:-2,right:-2,padding:"5px",color:"black","&:hover":{color:"red"}},children:Z===e?o.jsx(u.Z,{size:20}):o.jsx(eo,{sx:{color:"white"}})})]},e)}),(0,o.jsxs)(l.Z,{sx:{display:"flex",flexDirection:"column",alignItems:"center",marginLeft:"0px"},children:[o.jsx(l.Z,{onClick:()=>{x.current?.click()},sx:{border:"2px dashed #d3d3d3",bgcolor:"#f9f9f9",borderRadius:"12px",width:"160px",height:"215px",display:"flex",justifyContent:"center",alignItems:"center",color:"#d3d3d3",fontSize:"2.5rem","&:hover":{backgroundColor:"#f0f0f0"}},children:v?o.jsx(u.Z,{}):"+"}),o.jsx("input",{type:"file",ref:x,style:{display:"none"},onChange:t=>{let o=t.target.files;o&&o.length>0&&(Array.from(o).every(e=>"application/pdf"===e.type)?O&&e&&(y(!0),(0,a.$t)(r,e,O,Array.from(o)).finally(()=>{y(!1),x.current&&(x.current.value="")})):alert("Please select PDF files only."))},accept:"application/pdf"}),o.jsx(c.Z,{sx:{color:"#a9a9a9",fontSize:"0.875rem",mt:1},children:"Add up to 5 pdf files"})]})]}),(0,o.jsxs)(l.Z,{sx:{mt:8},children:[o.jsx(l.Z,{sx:{alignItems:"center",mb:3},children:(0,o.jsxs)(l.Z,{sx:{display:"flex",alignItems:"center",width:"40%"},children:[o.jsx("input",{type:"text",value:h,onChange:e=>{b(e.target.value)},onKeyDown:e=>{"Enter"===e.key&&el()},placeholder:"Enter Link To Add (eg. www.BenOsphere.com)",style:{padding:"15px 14px",borderRadius:"8px",border:"none",width:"70%",backgroundColor:"#f3f3f3",color:"#000000",fontSize:"0.9rem"}}),o.jsx(d.Z,{onClick:el,disabled:!h||z,sx:{display:"flex",alignItems:"center",justifyContent:"center",color:"#45a049",textTransform:"none",fontSize:"1rem",fontWeight:h?"bold":"normal",padding:0,backgroundColor:"transparent",border:"none",cursor:"pointer",marginLeft:"20px","&:disabled":{color:"#9E9E9E"}},children:z?o.jsx(u.Z,{size:20}):(0,o.jsxs)(o.Fragment,{children:[o.jsx("span",{style:{fontSize:"1.8rem",marginRight:"8px",fontWeight:"normal"},children:"+"}),"Add Link"]})})]})}),o.jsx(f.ZP,{container:!0,spacing:2,children:$.links.map((e,t)=>o.jsx(f.ZP,{item:!0,xs:12,children:(0,o.jsxs)(l.Z,{sx:{paddingLeft:1},children:[(0,o.jsxs)(l.Z,{sx:{display:"flex",alignItems:"center"},children:[o.jsx(c.Z,{component:"a",href:e.startsWith("http")?e:`https://${e}`,target:"_blank",rel:"noopener noreferrer",sx:{textDecoration:"none",color:"primary.main",display:"block",paddingBottom:"10px"},children:e||`Link ${t+1}`}),o.jsx(g.Z,{title:"Remove Link",children:o.jsx(p.Z,{onClick:()=>H(t),sx:{color:"gray",marginLeft:"8px",padding:"4px",marginBottom:"7px","&:hover":{color:"#ff4d4d"}},children:L===t?o.jsx(u.Z,{size:20}):o.jsx(en,{sx:{fontSize:"20px"}})})})]}),o.jsx(m.Z,{sx:{width:"10%"}})]})},t))})]})]}),o.jsx(K,{open:!!W,autoHideDuration:3e3,onClose:ef,children:o.jsx(Y.Z,{onClose:ef,severity:"success",sx:{width:"100%"},children:W})}),(0,o.jsxs)(U.Z,{open:k,onClose:G,PaperProps:{style:{borderRadius:"16px",boxShadow:"0px 10px 30px rgba(0, 0, 0, 0.1)"}},children:[o.jsx(V.Z,{sx:{fontWeight:"bold",fontSize:"1.5rem",color:"#000000"},children:"Confirm Deletion"}),(0,o.jsxs)(X.Z,{children:[(0,o.jsxs)(J.Z,{sx:{color:"#6c757d",fontSize:"1rem",mb:2},children:['To confirm deletion, please type "',o.jsx("strong",{style:{color:"black"},children:null!==M?"delete link":"delete document"}),'" in the box below.']}),o.jsx(Q.Z,{autoFocus:!0,fullWidth:!0,variant:"outlined",value:C,onChange:e=>S(e.target.value),InputProps:{style:{borderRadius:"12px",backgroundColor:"#f9f9f9"}}})]}),(0,o.jsxs)(ee.Z,{sx:{padding:"16px"},children:[o.jsx(d.Z,{onClick:G,sx:{color:"#6c757d",backgroundColor:"#ffffff",borderRadius:"8px",padding:"8px 16px",textTransform:"none",boxShadow:"none","&:hover":{backgroundColor:"#f0f0f0"}},children:"Cancel"}),o.jsx(d.Z,{onClick:null!==M?er:()=>{"delete document"===C&&E&&O&&e&&(j(E),(0,a.cd)(r,e,O,E).finally(()=>{j(null),G()}))},disabled:C!==(null!==M?"delete link":"delete document"),sx:{color:"#ffffff",backgroundColor:C===(null!==M?"delete link":"delete document")?"#000000":"#9E9E9E",borderRadius:"8px",padding:"8px 16px",textTransform:"none","&:hover":{backgroundColor:C===(null!==M?"delete link":"delete document")?"#333333":"#9E9E9E"}},children:Z||null!==L?o.jsx(u.Z,{size:20,sx:{color:"white"}}):"Confirm"})]})]})]})})})},69058:(e,t,r)=>{"use strict";r.d(t,{$t:()=>c,SS:()=>p,Y0:()=>a,cd:()=>u,fH:()=>f,mH:()=>g,ov:()=>d,v0:()=>s});var o=r(53148),n=r(25748),i=r(94638);async function a(e,t,r){try{let i=await (0,o.A_)("/benefits/benefit-by-type",{companyId:t,type:r});i&&i.benefits?(console.log("GET BENEFITS FOR TYPE RESPONSE: ",i.benefits),e((0,n.oQ)({benefitType:r,benefits:i.benefits})),e((0,n.nM)("Benefits fetched successfully"))):(console.error("Invalid response format:",i),e((0,n.nM)("Failed to fetch benefits")))}catch(t){console.error("Error fetching benefits:",t),e((0,n.nM)("Error fetching benefits"))}}async function s(e,t,r,i){let a={benefitId:t,page:i};console.log("data",a);let s=await (0,o.A_)("/benefits/one-benefit",a),d={...s,benefitId:t};for(let t of(e((0,n.F5)(d)),s.documents)){let o=decodeURIComponent(t.split("_____")[1]);l(e,t,r,o)}}async function l(e,t,r,i){let a={objectKey:t,companyId:r};console.log("data",a);let s=await (0,o.$R)("/benefits/document",a);if(console.log("VIEW BENEFIT RESPONSE: ",s),s){let r=new Blob([s],{type:"application/pdf"}),o=URL.createObjectURL(r);e((0,n.D7)([{documentObjectKey:t,document:o,originalFileName:i}]))}}let d=async(e,t,r,n,s)=>200===(await (0,o.j0)("/benefits/toggle-benefits/",{benefitId:r,companyId:t,isActivated:n})).status&&(await a(e,t,s),await (0,i.N)(e,t),!0);async function c(e,t,r,i){let a=new FormData;i.forEach(e=>a.append("documents",e)),a.append("companyId",r),a.append("benefitId",t);try{console.log("uploadDocument",a);let s=await (0,o.iG)("/benefits/add/document",a),d=s.data.objectKeys;if(console.log("newObjectKeys",d),200===s.status)return d.forEach((o,a)=>{let s=i[a].name;e((0,n.H_)({benefitId:t,document:o})),l(e,o,r,s)}),e((0,n.nM)("Document added successfully")),!0;return console.error("Error adding document:",s.data.error),e((0,n.nM)("Failed to add document")),!1}catch(t){return console.error("Error adding document:",t),e((0,n.nM)("Error adding document")),!1}}async function u(e,t,r,i){try{let a=await (0,o.j0)("/benefits/delete/document",{benefitId:t,companyId:r,objectKey:i});if(200===a.status)return e((0,n.iH)({benefitId:t,document:i})),e((0,n.nM)("Document deleted successfully")),!0;return console.error("Error deleting document:",a.data.error),e((0,n.nM)("Failed to delete document")),!1}catch(t){return console.error("Error deleting document:",t),e((0,n.nM)("Error deleting document")),!1}}async function p(e,t,r,i){try{let a=await (0,o.j0)("/benefits/add/links",{benefitId:t,companyId:r,urls:[i]});if(200===a.status)return e((0,n.MJ)({benefitId:t,link:i})),e((0,n.nM)("Link added successfully")),!0;return console.error("Error adding link:",a.data.error),e((0,n.nM)("Failed to add link")),!1}catch(t){return console.error("Error adding link:",t),e((0,n.nM)("Error adding link")),!1}}async function f(e,t,r,i){try{let a=await (0,o.j0)("/benefits/delete/link",{benefitId:t,companyId:r,urls:i});if(200===a.status)return e((0,n.Yw)({benefitId:t,link:i})),e((0,n.nM)("Link deleted successfully")),!0;return console.error("Error deleting link:",a.data.error),e((0,n.nM)("Failed to delete link")),!1}catch(t){return console.error("Error deleting link:",t),e((0,n.nM)("Error deleting link")),!1}}async function g(e,t){let r=new FormData;r.append("logoImage",t);try{console.log("uploading company logo",r);let t=await (0,o.iG)("/admin/update-company-logo",r);if(await (0,i.aK)(e),200===t.status)return console.log("Company logo updated successfully"),e((0,n.nM)("Company logo updated successfully")),!0;return console.error("Error updating company logo:",t.data.error),e((0,n.nM)("Failed to update company logo")),!1}catch(t){return console.error("Error updating company logo:",t),e((0,n.nM)("Error updating company logo")),!1}}},41530:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>o});let o=(0,r(68570).createProxy)(String.raw`C:\beno\project_dev\qharmony-frontend-v1\src\app\editBenefit\[benefitId]\page.tsx#default`)}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),o=t.X(0,[8948,1183,6621,9066,1999,3253,928,8097,8522,3141,6027,541,2142,4326,576,6305,401,2549],()=>r(92483));module.exports=o})();