(()=>{var e={};e.id=3558,e.ids=[3558],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},27790:e=>{"use strict";e.exports=require("assert")},84770:e=>{"use strict";e.exports=require("crypto")},17702:e=>{"use strict";e.exports=require("events")},92048:e=>{"use strict";e.exports=require("fs")},32615:e=>{"use strict";e.exports=require("http")},35240:e=>{"use strict";e.exports=require("https")},19801:e=>{"use strict";e.exports=require("os")},55315:e=>{"use strict";e.exports=require("path")},76162:e=>{"use strict";e.exports=require("stream")},74026:e=>{"use strict";e.exports=require("string_decoder")},74175:e=>{"use strict";e.exports=require("tty")},17360:e=>{"use strict";e.exports=require("url")},21764:e=>{"use strict";e.exports=require("util")},71568:e=>{"use strict";e.exports=require("zlib")},98061:e=>{"use strict";e.exports=require("node:assert")},92761:e=>{"use strict";e.exports=require("node:async_hooks")},72254:e=>{"use strict";e.exports=require("node:buffer")},40027:e=>{"use strict";e.exports=require("node:console")},6005:e=>{"use strict";e.exports=require("node:crypto")},65714:e=>{"use strict";e.exports=require("node:diagnostics_channel")},15673:e=>{"use strict";e.exports=require("node:events")},88849:e=>{"use strict";e.exports=require("node:http")},42725:e=>{"use strict";e.exports=require("node:http2")},87503:e=>{"use strict";e.exports=require("node:net")},38846:e=>{"use strict";e.exports=require("node:perf_hooks")},39630:e=>{"use strict";e.exports=require("node:querystring")},84492:e=>{"use strict";e.exports=require("node:stream")},31764:e=>{"use strict";e.exports=require("node:tls")},41041:e=>{"use strict";e.exports=require("node:url")},47261:e=>{"use strict";e.exports=require("node:util")},93746:e=>{"use strict";e.exports=require("node:util/types")},24086:e=>{"use strict";e.exports=require("node:worker_threads")},65628:e=>{"use strict";e.exports=require("node:zlib")},78187:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>n.a,__next_app__:()=>p,originalPathname:()=>u,pages:()=>d,routeModule:()=>x,tree:()=>c}),t(18164),t(33709),t(35866);var s=t(23191),o=t(88716),i=t(37922),n=t.n(i),a=t(95231),l={};for(let e in a)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>a[e]);t.d(r,l);let c=["",{children:["group",{children:["[groupId]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,18164)),"C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\group\\[groupId]\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,33709)),"C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,35866,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],d=["C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\group\\[groupId]\\page.tsx"],u="/group/[groupId]/page",p={require:t,loadChunk:()=>Promise.resolve()},x=new s.AppPageRouteModule({definition:{kind:o.x.APP_PAGE,page:"/group/[groupId]/page",pathname:"/group/[groupId]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},48019:(e,r,t)=>{Promise.resolve().then(t.bind(t,80214))},33198:(e,r,t)=>{"use strict";t.d(r,{Z:()=>v});var s=t(17577),o=t(41135),i=t(88634),n=t(91703),a=t(13643),l=t(2791),c=t(51426),d=t(10326);let u=(0,c.Z)((0,d.jsx)("path",{d:"M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"}),"Person");var p=t(71685),x=t(97898);function h(e){return(0,x.ZP)("MuiAvatar",e)}(0,p.Z)("MuiAvatar",["root","colorDefault","circular","rounded","square","img","fallback"]);var g=t(31121);let m=e=>{let{classes:r,variant:t,colorDefault:s}=e;return(0,i.Z)({root:["root",t,s&&"colorDefault"],img:["img"],fallback:["fallback"]},h,r)},f=(0,n.default)("div",{name:"MuiAvatar",slot:"Root",overridesResolver:(e,r)=>{let{ownerState:t}=e;return[r.root,r[t.variant],t.colorDefault&&r.colorDefault]}})((0,a.Z)(({theme:e})=>({position:"relative",display:"flex",alignItems:"center",justifyContent:"center",flexShrink:0,width:40,height:40,fontFamily:e.typography.fontFamily,fontSize:e.typography.pxToRem(20),lineHeight:1,borderRadius:"50%",overflow:"hidden",userSelect:"none",variants:[{props:{variant:"rounded"},style:{borderRadius:(e.vars||e).shape.borderRadius}},{props:{variant:"square"},style:{borderRadius:0}},{props:{colorDefault:!0},style:{color:(e.vars||e).palette.background.default,...e.vars?{backgroundColor:e.vars.palette.Avatar.defaultBg}:{backgroundColor:e.palette.grey[400],...e.applyStyles("dark",{backgroundColor:e.palette.grey[600]})}}}]}))),b=(0,n.default)("img",{name:"MuiAvatar",slot:"Img",overridesResolver:(e,r)=>r.img})({width:"100%",height:"100%",textAlign:"center",objectFit:"cover",color:"transparent",textIndent:1e4}),y=(0,n.default)(u,{name:"MuiAvatar",slot:"Fallback",overridesResolver:(e,r)=>r.fallback})({width:"75%",height:"75%"}),v=s.forwardRef(function(e,r){let t=(0,l.i)({props:e,name:"MuiAvatar"}),{alt:i,children:n,className:a,component:c="div",slots:u={},slotProps:p={},imgProps:x,sizes:h,src:v,srcSet:j,variant:Z="circular",...k}=t,w=null,_={...t,component:c,variant:Z},q=function({crossOrigin:e,referrerPolicy:r,src:t,srcSet:o}){let[i,n]=s.useState(!1);return s.useEffect(()=>{if(!t&&!o)return;n(!1);let s=!0,i=new Image;return i.onload=()=>{s&&n("loaded")},i.onerror=()=>{s&&n("error")},i.crossOrigin=e,i.referrerPolicy=r,i.src=t,o&&(i.srcset=o),()=>{s=!1}},[e,r,t,o]),i}({...x,..."function"==typeof p.img?p.img(_):p.img,src:v,srcSet:j}),S=v||j,P=S&&"error"!==q;_.colorDefault=!P,delete _.ownerState;let C=m(_),[B,E]=(0,g.Z)("img",{className:C.img,elementType:b,externalForwardedProps:{slots:u,slotProps:{img:{...x,...p.img}}},additionalProps:{alt:i,src:v,srcSet:j,sizes:h},ownerState:_});return w=P?(0,d.jsx)(B,{...E}):n||0===n?n:S&&i?i[0]:(0,d.jsx)(y,{ownerState:_,className:C.fallback}),(0,d.jsx)(f,{as:c,className:(0,o.Z)(C.root,a),ref:r,...k,ownerState:_,children:w})})},45011:(e,r,t)=>{"use strict";t.d(r,{Z:()=>s});let s=t(17577).createContext(void 0)},65656:(e,r,t)=>{"use strict";t.d(r,{Z:()=>i});var s=t(17577),o=t(45011);function i(){return s.useContext(o.Z)}},43227:(e,r,t)=>{"use strict";t.d(r,{Z:()=>o});var s=t(17577);let o=function({controlled:e,default:r,name:t,state:o="value"}){let{current:i}=s.useRef(void 0!==e),[n,a]=s.useState(r),l=s.useCallback(e=>{i||a(e)},[]);return[i?e:n,l]}},80214:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>j});var s=t(10326),o=t(17577),i=t(6283),n=t(25609),a=t(42265),l=t(89178),c=t(16027),d=t(76971),u=t(12549),p=t(43058),x=t(35047),h=t(25842),g=t(32049),m=t(53148),f=t(9921),b=t(96885),y=t(94638);let v=e=>{let r=e.split("_____");return r.length>1?r[1]:e},j=(0,u.Z)(()=>{let e=(0,x.useParams)(),r=(0,h.v9)(e=>(0,g.MP)(e)),[t,u]=(0,o.useState)(0),[j,Z]=(0,o.useState)(null),[k,w]=(0,o.useState)([]),[_,q]=(0,o.useState)([]),[S,P]=(0,o.useState)(!0),[C,B]=(0,o.useState)(!1),E=(0,x.useRouter)();(0,o.useEffect)(()=>{(async()=>{await A(),await R()})()},[]),(0,o.useEffect)(()=>{0===t?R():1===t&&I()},[t]),(0,o.useEffect)(()=>{j&&_.length>0&&B(j.employee_ids.length===_.length)},[j,_]);let A=async()=>{try{let r=await (0,m.A_)(`/group-detail/${e.groupId}`);Z(r.group)}catch(e){console.error("Error fetching group details:",e)}},I=async()=>{try{let e=(await (0,m.A_)("/benefits/all-benefits",{companyId:r})).benefitsPerType.reduce((e,r)=>{let t=r.benefits.filter(e=>e.isActivated);return t.length>0&&e.push({...r,benefits:t}),e},[]);w(e)}catch(e){console.error("Error fetching benefits data:",e)}},R=async()=>{try{let e=await (0,m.A_)("/admin/all-employees");if(q(e.employees),j&&e.employees.length>0){let r=e.employees.length===j.employee_ids.length;B(r)}}catch(e){console.error("Error fetching team members:",e)}},M=(e,r)=>{j&&(P(!1),Z(t=>{if(!t)return t;let s=t["employee"===r?"employee_ids":"document_ids"].includes(e)?t["employee"===r?"employee_ids":"document_ids"].filter(r=>r!==e):[...t["employee"===r?"employee_ids":"document_ids"],e];return{...t,["employee"===r?"employee_ids":"document_ids"]:s}}))},z=async()=>{let r={groupId:e.groupId,documentIds:j?.document_ids,employeeIds:j?.employee_ids};200===(await (0,m.j0)("/group/update-group",r)).status&&P(!0)},F=e=>{j&&(P(!1),Z(r=>{if(!r)return r;let t=e.imageS3Urls.every(e=>r.document_ids.includes(e))?r.document_ids.filter(r=>!e.imageS3Urls.includes(r)):[...new Set([...r.document_ids,...e.imageS3Urls])];return{...r,document_ids:t}}))};return s.jsx(p.Z,{children:(0,s.jsxs)(i.Z,{sx:{bgcolor:"#F5F6FA",py:2,width:"100%",height:"95vh",overflow:"auto"},children:[(0,s.jsxs)(i.Z,{sx:{px:4,display:"flex",justifyContent:"space-between",alignItems:"center"},children:[s.jsx(n.Z,{sx:{fontWeight:"500",fontSize:"28px",color:"black",textAlign:"left"},children:j?.name}),0===t?s.jsx(a.Z,{variant:"contained",sx:{textTransform:"none",borderRadius:"8px",bgcolor:"rgba(0, 0, 0, 0.06)",color:"black",boxShadow:"none",width:"15%",paddingY:"10px","&:hover":{backgroundColor:"rgba(0, 0, 0, 0.1)",boxShadow:"none"}},disabled:S,onClick:z,children:"Confirm Changes"}):""]}),(0,s.jsxs)(i.Z,{children:[0===t&&(0,s.jsxs)(s.Fragment,{children:[s.jsx(n.Z,{sx:{px:4,fontWeight:"500",fontSize:"17px",color:"black",textAlign:"left",mb:"40px"},children:"Select Users"}),(0,s.jsxs)(l.Z,{sx:{bgcolor:"#ffffff",borderRadius:"12px",marginBottom:9,boxShadow:"none",mx:4},children:[(0,s.jsxs)(c.ZP,{container:!0,sx:{borderBottom:"1px solid #E0E0E0",background:"#F0F0F0",px:3},children:[s.jsx(c.ZP,{item:!0,xs:1,children:s.jsx(d.Z,{checked:C,onChange:()=>{B(e=>!e),P(!1),Z(e=>{if(!e)return e;let r=C?[]:_.map(e=>e._id);return{...e,employee_ids:r}})},sx:{color:"#B0B0B0","&.Mui-checked":{color:"#B0B0B0"}}})}),s.jsx(c.ZP,{item:!0,xs:5,children:s.jsx(n.Z,{variant:"body2",sx:{fontWeight:500,color:"#B0B0B0",py:1},children:"NAME"})}),s.jsx(c.ZP,{item:!0,xs:6,borderLeft:"1px solid #E0E0E0",paddingLeft:"5px",children:s.jsx(n.Z,{variant:"body2",sx:{fontWeight:500,color:"#B0B0B0",py:1},children:"EMAIL"})})]}),_.map(e=>s.jsx(i.Z,{sx:{transition:"background-color 0.3s ease","&:hover":{bgcolor:"#f0f0f0",cursor:"pointer"},borderBottom:"1px solid #E0E0E0",px:3},children:(0,s.jsxs)(c.ZP,{container:!0,alignItems:"center",children:[s.jsx(c.ZP,{item:!0,xs:1,children:s.jsx(d.Z,{checked:C||j?.employee_ids.includes(e._id),onChange:()=>M(e._id,"employee"),sx:{color:"#B0B0B0","&.Mui-checked":{color:"#B0B0B0"}}})}),s.jsx(c.ZP,{item:!0,xs:5,children:s.jsx(n.Z,{sx:{fontWeight:"500",color:"black",py:2},children:e.name})}),s.jsx(c.ZP,{item:!0,xs:6,borderLeft:"1px solid #E0E0E0",paddingLeft:"5px",children:s.jsx(n.Z,{sx:{fontWeight:"500",color:"black",py:2},children:e.email})})]})},e._id))]}),(0,s.jsxs)(i.Z,{sx:{display:"flex",width:"100%",justifyContent:"center",gap:"10px"},children:[(0,s.jsxs)(a.Z,{variant:"contained",onClick:()=>E.push("/manage-groups"),sx:{alignSelf:"center",display:"flex",gap:"10px",textTransform:"none",borderRadius:"8px",bgcolor:"black",color:"white",boxShadow:"none"},children:[s.jsx(i.Z,{sx:{display:"flex",alignItems:"center"},children:s.jsx(f.Z,{color:"white",size:16})}),"Back"," "]}),(0,s.jsxs)(a.Z,{variant:"contained",onClick:()=>u(1),sx:{alignSelf:"center",display:"flex",gap:"10px",textTransform:"none",borderRadius:"8px",bgcolor:"black",color:"white",boxShadow:"none"},children:["Continue"," ",s.jsx(i.Z,{sx:{display:"flex",alignItems:"center"},children:s.jsx(b.Z,{color:"white",size:16})})]})]})]}),1===t&&(0,s.jsxs)(s.Fragment,{children:[s.jsx(n.Z,{sx:{px:4,fontWeight:"500",fontSize:"17px",color:"black",textAlign:"left",mb:"40px"},children:"Select Documents"}),s.jsx(i.Z,{sx:{display:"flex",flexDirection:"column"},children:(0,s.jsxs)(i.Z,{children:[k.map((e,r)=>(0,s.jsxs)(i.Z,{sx:{mb:3},children:[s.jsx(n.Z,{sx:{fontWeight:"500",fontSize:"22px",mb:1,px:4},children:(0,y.Ur)(e.benefitType)}),e.benefits.map((e,r)=>(0,s.jsxs)(l.Z,{sx:{bgcolor:"#ffffff",mb:2,mx:4},children:[s.jsx(n.Z,{sx:{fontWeight:"500",fontSize:"20px",mb:1,px:4,pt:2},children:(0,y.dA)(e.subType)}),e.imageS3Urls.length>0?(0,s.jsxs)(i.Z,{children:[(0,s.jsxs)(c.ZP,{container:!0,sx:{borderBottom:"1px solid #E0E0E0",background:"#F0F0F0",px:3},children:[s.jsx(c.ZP,{item:!0,xs:1,children:s.jsx(d.Z,{checked:e.imageS3Urls.every(e=>j?.document_ids.includes(e)),onChange:()=>F(e),sx:{color:"#B0B0B0","&.Mui-checked":{color:"#B0B0B0"}}})}),s.jsx(c.ZP,{item:!0,xs:5,children:s.jsx(n.Z,{variant:"body2",sx:{fontWeight:500,color:"#B0B0B0",py:1},children:"DOCUMENT NAME"})})]}),e.imageS3Urls.map((e,r)=>(0,s.jsxs)(c.ZP,{container:!0,sx:{px:3,py:1},alignItems:"center",children:[s.jsx(c.ZP,{item:!0,xs:1,children:s.jsx(d.Z,{checked:j?.document_ids.includes(e)||!1,onChange:()=>M(e,"document"),sx:{color:"green","&.Mui-checked":{color:"green"}}})}),s.jsx(c.ZP,{item:!0,xs:11,children:s.jsx(n.Z,{children:v(e)})})]},r))]}):s.jsx(i.Z,{children:s.jsx(n.Z,{sx:{fontSize:"16px",fontWeight:"400",color:"gray",ml:2,pb:3,textAlign:"center"},children:"There are no documents in this category."})})]},r))]},r)),(0,s.jsxs)(i.Z,{sx:{alignSelf:"center",display:"flex",gap:"10px",justifyContent:"center"},children:[(0,s.jsxs)(a.Z,{variant:"contained",onClick:()=>u(0),sx:{alignSelf:"center",display:"flex",gap:"10px",textTransform:"none",borderRadius:"8px",bgcolor:"black",color:"white",boxShadow:"none"},children:[s.jsx(i.Z,{sx:{display:"flex",alignItems:"center"},children:s.jsx(f.Z,{color:"white",size:16})}),"Back"," "]}),s.jsx(a.Z,{variant:"contained",sx:{textTransform:"none",borderRadius:"8px",bgcolor:"black",color:"white",boxShadow:"none",height:"36px",paddingY:"10px","&:hover":{backgroundColor:"rgba(0, 0, 0, 0.1)",boxShadow:"none"}},disabled:S,onClick:z,children:"Confirm Changes"})]})]})})]})]})]})})})},18164:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(68570).createProxy)(String.raw`C:\beno\project_dev\qharmony-frontend-v1\src\app\group\[groupId]\page.tsx#default`)},9664:(e,r,t)=>{"use strict";t.d(r,{Z:()=>l});var s=t(17577);let o=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),i=(...e)=>e.filter((e,r,t)=>!!e&&""!==e.trim()&&t.indexOf(e)===r).join(" ").trim();var n={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let a=(0,s.forwardRef)(({color:e="currentColor",size:r=24,strokeWidth:t=2,absoluteStrokeWidth:o,className:a="",children:l,iconNode:c,...d},u)=>(0,s.createElement)("svg",{ref:u,...n,width:r,height:r,stroke:e,strokeWidth:o?24*Number(t)/Number(r):t,className:i("lucide",a),...d},[...c.map(([e,r])=>(0,s.createElement)(e,r)),...Array.isArray(l)?l:[l]])),l=(e,r)=>{let t=(0,s.forwardRef)(({className:t,...n},l)=>(0,s.createElement)(a,{ref:l,iconNode:r,className:i(`lucide-${o(e)}`,t),...n}));return t.displayName=`${e}`,t}},9921:(e,r,t)=>{"use strict";t.d(r,{Z:()=>s});let s=(0,t(9664).Z)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},96885:(e,r,t)=>{"use strict";t.d(r,{Z:()=>s});let s=(0,t(9664).Z)("ArrowRight",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[8948,1183,6621,9066,1999,3253,928,8097,8522,3141,6027,6971,576,6305,401,2549],()=>t(78187));module.exports=s})();