(()=>{var e={};e.id=4401,e.ids=[4401],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},27790:e=>{"use strict";e.exports=require("assert")},84770:e=>{"use strict";e.exports=require("crypto")},17702:e=>{"use strict";e.exports=require("events")},92048:e=>{"use strict";e.exports=require("fs")},32615:e=>{"use strict";e.exports=require("http")},35240:e=>{"use strict";e.exports=require("https")},19801:e=>{"use strict";e.exports=require("os")},55315:e=>{"use strict";e.exports=require("path")},76162:e=>{"use strict";e.exports=require("stream")},74026:e=>{"use strict";e.exports=require("string_decoder")},74175:e=>{"use strict";e.exports=require("tty")},17360:e=>{"use strict";e.exports=require("url")},21764:e=>{"use strict";e.exports=require("util")},71568:e=>{"use strict";e.exports=require("zlib")},98061:e=>{"use strict";e.exports=require("node:assert")},92761:e=>{"use strict";e.exports=require("node:async_hooks")},72254:e=>{"use strict";e.exports=require("node:buffer")},40027:e=>{"use strict";e.exports=require("node:console")},6005:e=>{"use strict";e.exports=require("node:crypto")},65714:e=>{"use strict";e.exports=require("node:diagnostics_channel")},15673:e=>{"use strict";e.exports=require("node:events")},88849:e=>{"use strict";e.exports=require("node:http")},42725:e=>{"use strict";e.exports=require("node:http2")},87503:e=>{"use strict";e.exports=require("node:net")},38846:e=>{"use strict";e.exports=require("node:perf_hooks")},39630:e=>{"use strict";e.exports=require("node:querystring")},84492:e=>{"use strict";e.exports=require("node:stream")},31764:e=>{"use strict";e.exports=require("node:tls")},41041:e=>{"use strict";e.exports=require("node:url")},47261:e=>{"use strict";e.exports=require("node:util")},93746:e=>{"use strict";e.exports=require("node:util/types")},24086:e=>{"use strict";e.exports=require("node:worker_threads")},65628:e=>{"use strict";e.exports=require("node:zlib")},1636:(e,a,t)=>{"use strict";t.r(a),t.d(a,{GlobalError:()=>d.a,__next_app__:()=>m,originalPathname:()=>h,pages:()=>o,routeModule:()=>x,tree:()=>l}),t(27019),t(33709),t(35866);var s=t(23191),r=t(88716),c=t(37922),d=t.n(c),n=t(95231),i={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>n[e]);t.d(a,i);let l=["",{children:["hipaa",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,27019)),"C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\hipaa\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,33709)),"C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,35866,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],o=["C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\hipaa\\page.tsx"],h="/hipaa/page",m={require:t,loadChunk:()=>Promise.resolve()},x=new s.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/hipaa/page",pathname:"/hipaa",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},67898:(e,a,t)=>{Promise.resolve().then(t.bind(t,39565))},39565:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>n});var s=t(10326),r=t(17577),c=t(25676),d=t.n(c);function n(){let[e,a]=(0,r.useState)("All");return s.jsx(s.Fragment,{children:(0,s.jsxs)("div",{className:d().container,children:[(0,s.jsxs)("header",{className:d().header,children:[s.jsx("h1",{className:d().title,children:"\uD83D\uDD10 HIPAA Compliance at BenOsphere"}),(0,s.jsxs)("div",{className:d().infoBox,children:[(0,s.jsxs)("p",{className:d().infoText,children:["At BenOsphere, protecting employee health information is a top priority. As a platform trusted to handle sensitive benefits and wellness data, we are fully committed to maintaining HIPAA-compliant systems, policies, and processes. Our HIPAA compliance program has been reviewed and attested by the team at ",s.jsx("a",{href:"https://www.compliancy.com/",className:d().link,target:"_blank",rel:"noopener noreferrer",children:"Compliancy"}),", an industry-recognized third-party compliance partner."]}),s.jsx("p",{className:d().infoText,children:"We understand the responsibility that comes with managing protected health information (PHI), and we take that responsibility seriously. From secure infrastructure to strict access controls, every layer of BenOsphere is designed with privacy and compliance in mind."}),(0,s.jsxs)("p",{className:d().infoText,children:["Have questions about our HIPAA practices? \uD83D\uDCE9 ",s.jsx("a",{href:"mailto:<EMAIL>",className:d().link,children:"<EMAIL>"})]})]})]}),s.jsx("div",{className:d().filterContainer,children:["All","Product","Infrastructure","Organizational"].map(t=>s.jsx("button",{className:`${d().filterButton} ${e===t?d().filterButtonActive:""}`,onClick:()=>a(t),children:t},t))}),s.jsx("main",{children:s.jsx("div",{className:d().tableContainer,children:(0,s.jsxs)("table",{className:d().table,children:[s.jsx("thead",{children:(0,s.jsxs)("tr",{className:d().tr,children:[s.jsx("th",{className:d().th}),s.jsx("th",{className:d().th,children:"Control"}),s.jsx("th",{className:d().th,children:"Category"}),s.jsx("th",{className:d().th,children:"Description"})]})}),(0,s.jsxs)("tbody",{children:[(0,s.jsxs)("tr",{"data-category":"Product",children:[s.jsx("td",{className:`${d().td} ${d().checkmark}`,children:"✓"}),s.jsx("td",{className:d().td,children:"Customer Data Deletion Upon Termination"}),s.jsx("td",{className:`${d().td} ${d().category}`,children:"Product"}),s.jsx("td",{className:d().td,children:"Customer data is securely deleted when no longer needed or upon contract termination in accordance with data retention policies."})]}),(0,s.jsxs)("tr",{"data-category":"Product",children:[s.jsx("td",{className:`${d().td} ${d().checkmark}`,children:"✓"}),s.jsx("td",{className:d().td,children:"Data Classification"}),s.jsx("td",{className:`${d().td} ${d().category}`,children:"Product"}),s.jsx("td",{className:d().td,children:"Data is classified by sensitivity and handled accordingly to ensure appropriate levels of protection."})]}),(0,s.jsxs)("tr",{"data-category":"Organizational",children:[s.jsx("td",{className:`${d().td} ${d().checkmark}`,children:"✓"}),s.jsx("td",{className:d().td,children:"Designated Security Officials"}),s.jsx("td",{className:`${d().td} ${d().category}`,children:"Organizational"}),s.jsx("td",{className:d().td,children:"BenOsphere has implemented and verified control for: Designated Security Officials."})]}),(0,s.jsxs)("tr",{"data-category":"Organizational",children:[s.jsx("td",{className:`${d().td} ${d().checkmark}`,children:"✓"}),s.jsx("td",{className:d().td,children:"Contractor Requirements"}),s.jsx("td",{className:`${d().td} ${d().category}`,children:"Organizational"}),s.jsx("td",{className:d().td,children:"BenOsphere has implemented and verified control for: Contractor Requirements."})]}),(0,s.jsxs)("tr",{"data-category":"Infrastructure",children:[s.jsx("td",{className:`${d().td} ${d().checkmark}`,children:"✓"}),s.jsx("td",{className:d().td,children:"Credential Keys Managed"}),s.jsx("td",{className:`${d().td} ${d().category}`,children:"Infrastructure"}),s.jsx("td",{className:d().td,children:"BenOsphere has implemented and verified control for: Credential Keys Managed."})]}),(0,s.jsxs)("tr",{"data-category":"Infrastructure",children:[s.jsx("td",{className:`${d().td} ${d().checkmark}`,children:"✓"}),s.jsx("td",{className:d().td,children:"Cryptography Policies"}),s.jsx("td",{className:`${d().td} ${d().category}`,children:"Infrastructure"}),s.jsx("td",{className:d().td,children:"BenOsphere has implemented and verified control for: Cryptography Policies."})]}),(0,s.jsxs)("tr",{"data-category":"Infrastructure",children:[s.jsx("td",{className:`${d().td} ${d().checkmark}`,children:"✓"}),s.jsx("td",{className:d().td,children:"System Access Granted"}),s.jsx("td",{className:`${d().td} ${d().category}`,children:"Infrastructure"}),s.jsx("td",{className:d().td,children:"System access is managed using a role-based model and is revoked immediately upon employee or contractor termination."})]}),(0,s.jsxs)("tr",{"data-category":"Infrastructure",children:[s.jsx("td",{className:`${d().td} ${d().checkmark}`,children:"✓"}),s.jsx("td",{className:d().td,children:"Terminated Employee Access Revoked Within One Business Day"}),s.jsx("td",{className:`${d().td} ${d().category}`,children:"Infrastructure"}),s.jsx("td",{className:d().td,children:"System access is managed using a role-based model and is revoked immediately upon employee or contractor termination."})]}),(0,s.jsxs)("tr",{"data-category":"Infrastructure",children:[s.jsx("td",{className:`${d().td} ${d().checkmark}`,children:"✓"}),s.jsx("td",{className:d().td,children:"Unique Accounts Used"}),s.jsx("td",{className:`${d().td} ${d().category}`,children:"Infrastructure"}),s.jsx("td",{className:d().td,children:"BenOsphere has implemented and verified control for: Unique Accounts Used."})]}),(0,s.jsxs)("tr",{"data-category":"Infrastructure",children:[s.jsx("td",{className:`${d().td} ${d().checkmark}`,children:"✓"}),s.jsx("td",{className:d().td,children:"Unique SSH"}),s.jsx("td",{className:`${d().td} ${d().category}`,children:"Infrastructure"}),s.jsx("td",{className:d().td,children:"Public SSH access is disabled. Only authenticated and authorized users with unique credentials can access systems via secure protocols."})]}),(0,s.jsxs)("tr",{"data-category":"Product",children:[s.jsx("td",{className:`${d().td} ${d().checkmark}`,children:"✓"}),s.jsx("td",{className:d().td,children:"Users Can Access All Their Information"}),s.jsx("td",{className:`${d().td} ${d().category}`,children:"Product"}),s.jsx("td",{className:d().td,children:"Each user is provisioned with a unique account. Access is monitored and governed by least privilege principles."})]}),(0,s.jsxs)("tr",{"data-category":"Product",children:[s.jsx("td",{className:`${d().td} ${d().checkmark}`,children:"✓"}),s.jsx("td",{className:d().td,children:"Users Can Update their Information"}),s.jsx("td",{className:`${d().td} ${d().category}`,children:"Product"}),s.jsx("td",{className:d().td,children:"Users can view and update their information through a secure, authenticated interface in compliance with data accuracy standards."})]}),(0,s.jsxs)("tr",{"data-category":"Infrastructure",children:[s.jsx("td",{className:`${d().td} ${d().checkmark}`,children:"✓"}),s.jsx("td",{className:d().td,children:"VPN Required for Production Access"}),s.jsx("td",{className:`${d().td} ${d().category}`,children:"Infrastructure"}),s.jsx("td",{className:d().td,children:"Production systems are only accessible through a secure VPN to protect internal assets from unauthorized external access."})]}),(0,s.jsxs)("tr",{"data-category":"Infrastructure",children:[s.jsx("td",{className:`${d().td} ${d().checkmark}`,children:"✓"}),s.jsx("td",{className:d().td,children:"Vulnerability Management"}),s.jsx("td",{className:`${d().td} ${d().category}`,children:"Infrastructure"}),s.jsx("td",{className:d().td,children:"BenOsphere has implemented and verified control for: Vulnerability Management."})]}),(0,s.jsxs)("tr",{"data-category":"Product",children:[s.jsx("td",{className:`${d().td} ${d().checkmark}`,children:"✓"}),s.jsx("td",{className:d().td,children:"Inactivity and Browser Exit Logout"}),s.jsx("td",{className:`${d().td} ${d().category}`,children:"Product"}),s.jsx("td",{className:d().td,children:"System activity and security events are logged centrally and monitored for suspicious behavior with real-time alerts."})]}),(0,s.jsxs)("tr",{"data-category":"Product",children:[s.jsx("td",{className:`${d().td} ${d().checkmark}`,children:"✓"}),s.jsx("td",{className:d().td,children:"Least-Privileged Policy for Customer Data Access"}),s.jsx("td",{className:`${d().td} ${d().category}`,children:"Product"}),s.jsx("td",{className:d().td,children:"A formal Least-Privileged Policy for Customer Data Access is in place to govern consistent and secure operations across the organization."})]}),(0,s.jsxs)("tr",{"data-category":"Infrastructure",children:[s.jsx("td",{className:`${d().td} ${d().checkmark}`,children:"✓"}),s.jsx("td",{className:d().td,children:"Log Management System"}),s.jsx("td",{className:`${d().td} ${d().category}`,children:"Infrastructure"}),s.jsx("td",{className:d().td,children:"System activity and security events are logged centrally and monitored for suspicious behavior with real-time alerts."})]}),(0,s.jsxs)("tr",{"data-category":"Infrastructure",children:[s.jsx("td",{className:`${d().td} ${d().checkmark}`,children:"✓"}),s.jsx("td",{className:d().td,children:"Logging/Monitoring"}),s.jsx("td",{className:`${d().td} ${d().category}`,children:"Infrastructure"}),s.jsx("td",{className:d().td,children:"System activity and security events are logged centrally and monitored for suspicious behavior with real-time alerts."})]}),(0,s.jsxs)("tr",{"data-category":"Infrastructure",children:[s.jsx("td",{className:`${d().td} ${d().checkmark}`,children:"✓"}),s.jsx("td",{className:d().td,children:"Logs Centrally Stored"}),s.jsx("td",{className:`${d().td} ${d().category}`,children:"Infrastructure"}),s.jsx("td",{className:d().td,children:"System activity and security events are logged centrally and monitored for suspicious behavior with real-time alerts."})]}),(0,s.jsxs)("tr",{"data-category":"Infrastructure",children:[s.jsx("td",{className:`${d().td} ${d().checkmark}`,children:"✓"}),s.jsx("td",{className:d().td,children:"Malware Detection Software Installed"}),s.jsx("td",{className:`${d().td} ${d().category}`,children:"Infrastructure"}),s.jsx("td",{className:d().td,children:"BenOsphere has implemented and verified control for: Malware Detection Software Installed."})]}),(0,s.jsxs)("tr",{"data-category":"Infrastructure",children:[s.jsx("td",{className:`${d().td} ${d().checkmark}`,children:"✓"}),s.jsx("td",{className:d().td,children:"Multiple Availability Zones"}),s.jsx("td",{className:`${d().td} ${d().category}`,children:"Infrastructure"}),s.jsx("td",{className:d().td,children:"BenOsphere has implemented and verified control for: Multiple Availability Zones."})]}),(0,s.jsxs)("tr",{"data-category":"Infrastructure",children:[s.jsx("td",{className:`${d().td} ${d().checkmark}`,children:"✓"}),s.jsx("td",{className:d().td,children:"Network segmentation in place"}),s.jsx("td",{className:`${d().td} ${d().category}`,children:"Infrastructure"}),s.jsx("td",{className:d().td,children:"BenOsphere has implemented and verified control for: Network segmentation in place."})]}),(0,s.jsxs)("tr",{"data-category":"Organizational",children:[s.jsx("td",{className:`${d().td} ${d().checkmark}`,children:"✓"}),s.jsx("td",{className:d().td,children:"Operational Audit"}),s.jsx("td",{className:`${d().td} ${d().category}`,children:"Organizational"}),s.jsx("td",{className:d().td,children:"Security controls and data access are reviewed regularly to maintain regulatory compliance and identify improvement opportunities."})]}),(0,s.jsxs)("tr",{"data-category":"Organizational",children:[s.jsx("td",{className:`${d().td} ${d().checkmark}`,children:"✓"}),s.jsx("td",{className:d().td,children:"Oversight of Security Controls"}),s.jsx("td",{className:`${d().td} ${d().category}`,children:"Organizational"}),s.jsx("td",{className:d().td,children:"BenOsphere has implemented and verified control for: Oversight of Security Controls."})]}),(0,s.jsxs)("tr",{"data-category":"Infrastructure",children:[s.jsx("td",{className:`${d().td} ${d().checkmark}`,children:"✓"}),s.jsx("td",{className:d().td,children:"Password Manager"}),s.jsx("td",{className:`${d().td} ${d().category}`,children:"Infrastructure"}),s.jsx("td",{className:d().td,children:"BenOsphere has implemented and verified control for: Password Manager."})]}),(0,s.jsxs)("tr",{"data-category":"Organizational",children:[s.jsx("td",{className:`${d().td} ${d().checkmark}`,children:"✓"}),s.jsx("td",{className:d().td,children:"Password Policy"}),s.jsx("td",{className:`${d().td} ${d().category}`,children:"Organizational"}),s.jsx("td",{className:d().td,children:"A formal Password Policy is in place to govern consistent and secure operations across the organization."})]}),(0,s.jsxs)("tr",{"data-category":"Infrastructure",children:[s.jsx("td",{className:`${d().td} ${d().checkmark}`,children:"✓"}),s.jsx("td",{className:d().td,children:"Password Storage"}),s.jsx("td",{className:`${d().td} ${d().category}`,children:"Infrastructure"}),s.jsx("td",{className:d().td,children:"BenOsphere has implemented and verified control for: Password Storage."})]}),(0,s.jsxs)("tr",{"data-category":"Infrastructure",children:[s.jsx("td",{className:`${d().td} ${d().checkmark}`,children:"✓"}),s.jsx("td",{className:d().td,children:"Removable Media Device Encryption"}),s.jsx("td",{className:`${d().td} ${d().category}`,children:"Infrastructure"}),s.jsx("td",{className:d().td,children:"Data is encrypted both at rest and in transit using industry-standard protocols. Encryption keys are tightly controlled and accessible only to authorized personnel."})]}),(0,s.jsxs)("tr",{"data-category":"Product",children:[s.jsx("td",{className:`${d().td} ${d().checkmark}`,children:"✓"}),s.jsx("td",{className:d().td,children:"Require Authentication for Access"}),s.jsx("td",{className:`${d().td} ${d().category}`,children:"Product"}),s.jsx("td",{className:d().td,children:"System access is managed using a role-based model and is revoked immediately upon employee or contractor termination."})]}),(0,s.jsxs)("tr",{"data-category":"Infrastructure",children:[s.jsx("td",{className:`${d().td} ${d().checkmark}`,children:"✓"}),s.jsx("td",{className:d().td,children:"Require Encryption of Web-Based Admin Access"}),s.jsx("td",{className:`${d().td} ${d().category}`,children:"Infrastructure"}),s.jsx("td",{className:d().td,children:"Data is encrypted both at rest and in transit using industry-standard protocols. Encryption keys are tightly controlled and accessible only to authorized personnel."})]}),(0,s.jsxs)("tr",{"data-category":"Product",children:[s.jsx("td",{className:`${d().td} ${d().checkmark}`,children:"✓"}),s.jsx("td",{className:d().td,children:"Role-Based Security Implementation"}),s.jsx("td",{className:`${d().td} ${d().category}`,children:"Product"}),s.jsx("td",{className:d().td,children:"BenOsphere has implemented and verified control for: Role-Based Security Implementation."})]}),(0,s.jsxs)("tr",{"data-category":"Infrastructure",children:[s.jsx("td",{className:`${d().td} ${d().checkmark}`,children:"✓"}),s.jsx("td",{className:d().td,children:"Servers Monitored and Alarmed"}),s.jsx("td",{className:`${d().td} ${d().category}`,children:"Infrastructure"}),s.jsx("td",{className:d().td,children:"Critical infrastructure components are continuously monitored with alerts configured for anomalous behavior or system failures."})]}),(0,s.jsxs)("tr",{"data-category":"Infrastructure",children:[s.jsx("td",{className:`${d().td} ${d().checkmark}`,children:"✓"}),s.jsx("td",{className:d().td,children:"Session Lock"}),s.jsx("td",{className:`${d().td} ${d().category}`,children:"Infrastructure"}),s.jsx("td",{className:d().td,children:"BenOsphere has implemented and verified control for: Session Lock."})]}),(0,s.jsxs)("tr",{"data-category":"Infrastructure",children:[s.jsx("td",{className:`${d().td} ${d().checkmark}`,children:"✓"}),s.jsx("td",{className:d().td,children:"SSL/TLS Enforced"}),s.jsx("td",{className:`${d().td} ${d().category}`,children:"Infrastructure"}),s.jsx("td",{className:d().td,children:"BenOsphere has implemented and verified control for: SSL/TLS Enforced."})]}),(0,s.jsxs)("tr",{"data-category":"Organizational",children:[s.jsx("td",{className:`${d().td} ${d().checkmark}`,children:"✓"}),s.jsx("td",{className:d().td,children:"Activity Review"}),s.jsx("td",{className:`${d().td} ${d().category}`,children:"Organizational"}),s.jsx("td",{className:d().td,children:"Security controls and data access are reviewed regularly to maintain regulatory compliance and identify improvement opportunities."})]}),(0,s.jsxs)("tr",{"data-category":"Organizational",children:[s.jsx("td",{className:`${d().td} ${d().checkmark}`,children:"✓"}),s.jsx("td",{className:d().td,children:"Annual Access Control Review"}),s.jsx("td",{className:`${d().td} ${d().category}`,children:"Organizational"}),s.jsx("td",{className:d().td,children:"Security controls and data access are reviewed regularly to maintain regulatory compliance and identify improvement opportunities."})]}),(0,s.jsxs)("tr",{"data-category":"Organizational",children:[s.jsx("td",{className:`${d().td} ${d().checkmark}`,children:"✓"}),s.jsx("td",{className:d().td,children:"Annual Incident Response Test"}),s.jsx("td",{className:`${d().td} ${d().category}`,children:"Organizational"}),s.jsx("td",{className:d().td,children:"A comprehensive incident response plan exists and is tested annually to ensure swift action during security events."})]}),(0,s.jsxs)("tr",{"data-category":"Organizational",children:[s.jsx("td",{className:`${d().td} ${d().checkmark}`,children:"✓"}),s.jsx("td",{className:d().td,children:"Annual Penetration Tests"}),s.jsx("td",{className:`${d().td} ${d().category}`,children:"Organizational"}),s.jsx("td",{className:d().td,children:"BenOsphere has implemented and verified control for: Annual Penetration Tests."})]}),(0,s.jsxs)("tr",{"data-category":"Infrastructure",children:[s.jsx("td",{className:`${d().td} ${d().checkmark}`,children:"✓"}),s.jsx("td",{className:d().td,children:"Architectural Diagram"}),s.jsx("td",{className:`${d().td} ${d().category}`,children:"Infrastructure"}),s.jsx("td",{className:d().td,children:"BenOsphere has implemented and verified control for: Architectural Diagram."})]}),(0,s.jsxs)("tr",{"data-category":"Infrastructure",children:[s.jsx("td",{className:`${d().td} ${d().checkmark}`,children:"✓"}),s.jsx("td",{className:d().td,children:"Authentication Protocol"}),s.jsx("td",{className:`${d().td} ${d().category}`,children:"Infrastructure"}),s.jsx("td",{className:d().td,children:"BenOsphere has implemented and verified control for: Authentication Protocol."})]}),(0,s.jsxs)("tr",{"data-category":"Infrastructure",children:[s.jsx("td",{className:`${d().td} ${d().checkmark}`,children:"✓"}),s.jsx("td",{className:d().td,children:"Backup Integrity and Completeness"}),s.jsx("td",{className:`${d().td} ${d().category}`,children:"Infrastructure"}),s.jsx("td",{className:d().td,children:"Regular, automated backups are performed with integrity checks and monitored for successful completion to ensure recoverability in case of data loss."})]}),(0,s.jsxs)("tr",{"data-category":"Organizational",children:[s.jsx("td",{className:`${d().td} ${d().checkmark}`,children:"✓"}),s.jsx("td",{className:d().td,children:"Backup Policy"}),s.jsx("td",{className:`${d().td} ${d().category}`,children:"Organizational"}),s.jsx("td",{className:d().td,children:"Regular, automated backups are performed with integrity checks and monitored for successful completion to ensure recoverability in case of data loss."})]}),(0,s.jsxs)("tr",{"data-category":"Product",children:[s.jsx("td",{className:`${d().td} ${d().checkmark}`,children:"✓"}),s.jsx("td",{className:d().td,children:"Customer Data is Encrypted at Rest"}),s.jsx("td",{className:`${d().td} ${d().category}`,children:"Product"}),s.jsx("td",{className:d().td,children:"BenOsphere has implemented and verified control for: Customer Data is Encrypted at Rest."})]}),(0,s.jsxs)("tr",{"data-category":"Organizational",children:[s.jsx("td",{className:`${d().td} ${d().checkmark}`,children:"✓"}),s.jsx("td",{className:d().td,children:"Customer Data Policies"}),s.jsx("td",{className:`${d().td} ${d().category}`,children:"Organizational"}),s.jsx("td",{className:d().td,children:"BenOsphere has implemented and verified control for: Customer Data Policies."})]}),(0,s.jsxs)("tr",{"data-category":"Infrastructure",children:[s.jsx("td",{className:`${d().td} ${d().checkmark}`,children:"✓"}),s.jsx("td",{className:d().td,children:"Daily Backup Statuses Monitored"}),s.jsx("td",{className:`${d().td} ${d().category}`,children:"Infrastructure"}),s.jsx("td",{className:d().td,children:"Regular, automated backups are performed with integrity checks and monitored for successful completion to ensure recoverability in case of data loss."})]}),(0,s.jsxs)("tr",{"data-category":"Organizational",children:[s.jsx("td",{className:`${d().td} ${d().checkmark}`,children:"✓"}),s.jsx("td",{className:d().td,children:"Data Destruction Policy"}),s.jsx("td",{className:`${d().td} ${d().category}`,children:"Organizational"}),s.jsx("td",{className:d().td,children:"A formal Data Destruction Policy is in place to govern consistent and secure operations across the organization."})]}),(0,s.jsxs)("tr",{"data-category":"Organizational",children:[s.jsx("td",{className:`${d().td} ${d().checkmark}`,children:"✓"}),s.jsx("td",{className:d().td,children:"Data Retention Policy"}),s.jsx("td",{className:`${d().td} ${d().category}`,children:"Organizational"}),s.jsx("td",{className:d().td,children:"A formal Data Retention Policy is in place to govern consistent and secure operations across the organization."})]}),(0,s.jsxs)("tr",{"data-category":"Infrastructure",children:[s.jsx("td",{className:`${d().td} ${d().checkmark}`,children:"✓"}),s.jsx("td",{className:d().td,children:"Database Monitored and Alarmed"}),s.jsx("td",{className:`${d().td} ${d().category}`,children:"Infrastructure"}),s.jsx("td",{className:d().td,children:"Critical infrastructure components are continuously monitored with alerts configured for anomalous behavior or system failures."})]}),(0,s.jsxs)("tr",{"data-category":"Infrastructure",children:[s.jsx("td",{className:`${d().td} ${d().checkmark}`,children:"✓"}),s.jsx("td",{className:d().td,children:"Denial of Public SSH"}),s.jsx("td",{className:`${d().td} ${d().category}`,children:"Infrastructure"}),s.jsx("td",{className:d().td,children:"Public SSH access is disabled. Only authenticated and authorized users with unique credentials can access systems via secure protocols."})]}),(0,s.jsxs)("tr",{"data-category":"Organizational",children:[s.jsx("td",{className:`${d().td} ${d().checkmark}`,children:"✓"}),s.jsx("td",{className:d().td,children:"Disaster Recovery Plan"}),s.jsx("td",{className:`${d().td} ${d().category}`,children:"Organizational"}),s.jsx("td",{className:d().td,children:"Business continuity and disaster recovery plans are in place to minimize disruption during unforeseen incidents."})]}),(0,s.jsxs)("tr",{"data-category":"Infrastructure",children:[s.jsx("td",{className:`${d().td} ${d().checkmark}`,children:"✓"}),s.jsx("td",{className:d().td,children:"Disposal of Sensitive Data on Hardware"}),s.jsx("td",{className:`${d().td} ${d().category}`,children:"Infrastructure"}),s.jsx("td",{className:d().td,children:"BenOsphere has implemented and verified control for: Disposal of Sensitive Data on Hardware."})]}),(0,s.jsxs)("tr",{"data-category":"Infrastructure",children:[s.jsx("td",{className:`${d().td} ${d().checkmark}`,children:"✓"}),s.jsx("td",{className:d().td,children:"Disposal of Sensitive Data on Paper"}),s.jsx("td",{className:`${d().td} ${d().category}`,children:"Infrastructure"}),s.jsx("td",{className:d().td,children:"BenOsphere has implemented and verified control for: Disposal of Sensitive Data on Paper."})]}),(0,s.jsxs)("tr",{"data-category":"Organizational",children:[s.jsx("td",{className:`${d().td} ${d().checkmark}`,children:"✓"}),s.jsx("td",{className:d().td,children:"Encryption Policy"}),s.jsx("td",{className:`${d().td} ${d().category}`,children:"Organizational"}),s.jsx("td",{className:d().td,children:"Data is encrypted both at rest and in transit using industry-standard protocols. Encryption keys are tightly controlled and accessible only to authorized personnel."})]}),(0,s.jsxs)("tr",{"data-category":"Infrastructure",children:[s.jsx("td",{className:`${d().td} ${d().checkmark}`,children:"✓"}),s.jsx("td",{className:d().td,children:"Event Logging"}),s.jsx("td",{className:`${d().td} ${d().category}`,children:"Infrastructure"}),s.jsx("td",{className:d().td,children:"System activity and security events are logged centrally and monitored for suspicious behavior with real-time alerts."})]}),(0,s.jsxs)("tr",{"data-category":"Infrastructure",children:[s.jsx("td",{className:`${d().td} ${d().checkmark}`,children:"✓"}),s.jsx("td",{className:d().td,children:"Failed Backup Alert and Action"}),s.jsx("td",{className:`${d().td} ${d().category}`,children:"Infrastructure"}),s.jsx("td",{className:d().td,children:"Regular, automated backups are performed with integrity checks and monitored for successful completion to ensure recoverability in case of data loss."})]}),(0,s.jsxs)("tr",{"data-category":"Infrastructure",children:[s.jsx("td",{className:`${d().td} ${d().checkmark}`,children:"✓"}),s.jsx("td",{className:d().td,children:"FIM (File Integrity Monitoring) Software in Place"}),s.jsx("td",{className:`${d().td} ${d().category}`,children:"Infrastructure"}),s.jsx("td",{className:d().td,children:"Critical infrastructure components are continuously monitored with alerts configured for anomalous behavior or system failures."})]}),(0,s.jsxs)("tr",{"data-category":"Infrastructure",children:[s.jsx("td",{className:`${d().td} ${d().checkmark}`,children:"✓"}),s.jsx("td",{className:d().td,children:"Hard-Disk Encryption"}),s.jsx("td",{className:`${d().td} ${d().category}`,children:"Infrastructure"}),s.jsx("td",{className:d().td,children:"Data is encrypted both at rest and in transit using industry-standard protocols. Encryption keys are tightly controlled and accessible only to authorized personnel."})]}),(0,s.jsxs)("tr",{"data-category":"Infrastructure",children:[s.jsx("td",{className:`${d().td} ${d().checkmark}`,children:"✓"}),s.jsx("td",{className:d().td,children:"Hardening Standards in Place"}),s.jsx("td",{className:`${d().td} ${d().category}`,children:"Infrastructure"}),s.jsx("td",{className:d().td,children:"BenOsphere has implemented and verified control for: Hardening Standards in Place."})]}),(0,s.jsxs)("tr",{"data-category":"Organizational",children:[s.jsx("td",{className:`${d().td} ${d().checkmark}`,children:"✓"}),s.jsx("td",{className:d().td,children:"Document Retention Period"}),s.jsx("td",{className:`${d().td} ${d().category}`,children:"Organizational"}),s.jsx("td",{className:d().td,children:"Data retention is governed by clearly defined policies ensuring legal and operational requirements are met."})]}),(0,s.jsxs)("tr",{"data-category":"Organizational",children:[s.jsx("td",{className:`${d().td} ${d().checkmark}`,children:"✓"}),s.jsx("td",{className:d().td,children:"Employee Disclosure Process"}),s.jsx("td",{className:`${d().td} ${d().category}`,children:"Organizational"}),s.jsx("td",{className:d().td,children:"BenOsphere has implemented and verified control for: Employee Disclosure Process."})]}),(0,s.jsxs)("tr",{"data-category":"Organizational",children:[s.jsx("td",{className:`${d().td} ${d().checkmark}`,children:"✓"}),s.jsx("td",{className:d().td,children:"Follow-Ups Tracked"}),s.jsx("td",{className:`${d().td} ${d().category}`,children:"Organizational"}),s.jsx("td",{className:d().td,children:"BenOsphere has implemented and verified control for: Follow-Ups Tracked."})]}),(0,s.jsxs)("tr",{"data-category":"Organizational",children:[s.jsx("td",{className:`${d().td} ${d().checkmark}`,children:"✓"}),s.jsx("td",{className:d().td,children:"HIPAA Awareness Training"}),s.jsx("td",{className:`${d().td} ${d().category}`,children:"Organizational"}),s.jsx("td",{className:d().td,children:"All employees and contractors complete regular HIPAA and cybersecurity training to ensure awareness of responsibilities and threats."})]}),(0,s.jsxs)("tr",{"data-category":"Organizational",children:[s.jsx("td",{className:`${d().td} ${d().checkmark}`,children:"✓"}),s.jsx("td",{className:d().td,children:"Incident Response Team"}),s.jsx("td",{className:`${d().td} ${d().category}`,children:"Organizational"}),s.jsx("td",{className:d().td,children:"A comprehensive incident response plan exists and is tested annually to ensure swift action during security events."})]}),(0,s.jsxs)("tr",{"data-category":"Organizational",children:[s.jsx("td",{className:`${d().td} ${d().checkmark}`,children:"✓"}),s.jsx("td",{className:d().td,children:"Incident Response Plan"}),s.jsx("td",{className:`${d().td} ${d().category}`,children:"Organizational"}),s.jsx("td",{className:d().td,children:"A comprehensive incident response plan exists and is tested annually to ensure swift action during security events."})]}),(0,s.jsxs)("tr",{"data-category":"Organizational",children:[s.jsx("td",{className:`${d().td} ${d().checkmark}`,children:"✓"}),s.jsx("td",{className:d().td,children:"Termination/Offboarding Checklist"}),s.jsx("td",{className:`${d().td} ${d().category}`,children:"Organizational"}),s.jsx("td",{className:d().td,children:"BenOsphere has implemented and verified control for: Termination/Offboarding Checklist."})]}),(0,s.jsxs)("tr",{"data-category":"Organizational",children:[s.jsx("td",{className:`${d().td} ${d().checkmark}`,children:"✓"}),s.jsx("td",{className:d().td,children:"3rd Parties and Vendors Given Instructions on Breach Reporting"}),s.jsx("td",{className:`${d().td} ${d().category}`,children:"Organizational"}),s.jsx("td",{className:d().td,children:"Vendors are assessed for compliance, and agreements are maintained to ensure they meet BenOsphere’s security and privacy standards."})]}),(0,s.jsxs)("tr",{"data-category":"Organizational",children:[s.jsx("td",{className:`${d().td} ${d().checkmark}`,children:"✓"}),s.jsx("td",{className:d().td,children:"Acceptable Use Policy Employees Acknowledge"}),s.jsx("td",{className:`${d().td} ${d().category}`,children:"Organizational"}),s.jsx("td",{className:d().td,children:"A formal Acceptable Use Policy Employees Acknowledge is in place to govern consistent and secure operations across the organization."})]}),(0,s.jsxs)("tr",{"data-category":"Organizational",children:[s.jsx("td",{className:`${d().td} ${d().checkmark}`,children:"✓"}),s.jsx("td",{className:d().td,children:"Allowable Use and Disclosure"}),s.jsx("td",{className:`${d().td} ${d().category}`,children:"Organizational"}),s.jsx("td",{className:d().td,children:"BenOsphere has implemented and verified control for: Allowable Use and Disclosure."})]}),(0,s.jsxs)("tr",{"data-category":"Organizational",children:[s.jsx("td",{className:`${d().td} ${d().checkmark}`,children:"✓"}),s.jsx("td",{className:d().td,children:"Annual Review of Purposes"}),s.jsx("td",{className:`${d().td} ${d().category}`,children:"Organizational"}),s.jsx("td",{className:d().td,children:"Security controls and data access are reviewed regularly to maintain regulatory compliance and identify improvement opportunities."})]}),(0,s.jsxs)("tr",{"data-category":"Organizational",children:[s.jsx("td",{className:`${d().td} ${d().checkmark}`,children:"✓"}),s.jsx("td",{className:d().td,children:"Asset Management Policy"}),s.jsx("td",{className:`${d().td} ${d().category}`,children:"Organizational"}),s.jsx("td",{className:d().td,children:"A formal Asset Management Policy is in place to govern consistent and secure operations across the organization."})]}),(0,s.jsxs)("tr",{"data-category":"Organizational",children:[s.jsx("td",{className:`${d().td} ${d().checkmark}`,children:"✓"}),s.jsx("td",{className:d().td,children:"Background Checks"}),s.jsx("td",{className:`${d().td} ${d().category}`,children:"Organizational"}),s.jsx("td",{className:d().td,children:"BenOsphere has implemented and verified control for: Background Checks."})]}),(0,s.jsxs)("tr",{"data-category":"Organizational",children:[s.jsx("td",{className:`${d().td} ${d().checkmark}`,children:"✓"}),s.jsx("td",{className:d().td,children:"Business Associate Agreements"}),s.jsx("td",{className:`${d().td} ${d().category}`,children:"Organizational"}),s.jsx("td",{className:d().td,children:"BenOsphere has implemented and verified control for: Business Associate Agreements."})]}),(0,s.jsxs)("tr",{"data-category":"Organizational",children:[s.jsx("td",{className:`${d().td} ${d().checkmark}`,children:"✓"}),s.jsx("td",{className:d().td,children:"Business Continuity Plan"}),s.jsx("td",{className:`${d().td} ${d().category}`,children:"Organizational"}),s.jsx("td",{className:d().td,children:"Business continuity and disaster recovery plans are in place to minimize disruption during unforeseen incidents."})]}),(0,s.jsxs)("tr",{"data-category":"Organizational",children:[s.jsx("td",{className:`${d().td} ${d().checkmark}`,children:"✓"}),s.jsx("td",{className:d().td,children:"Business Impact Analysis"}),s.jsx("td",{className:`${d().td} ${d().category}`,children:"Organizational"}),s.jsx("td",{className:d().td,children:"BenOsphere has implemented and verified control for: Business Impact Analysis."})]}),(0,s.jsxs)("tr",{"data-category":"Organizational",children:[s.jsx("td",{className:`${d().td} ${d().checkmark}`,children:"✓"}),s.jsx("td",{className:d().td,children:"Breach Notification"}),s.jsx("td",{className:`${d().td} ${d().category}`,children:"Organizational"}),s.jsx("td",{className:d().td,children:"BenOsphere has implemented and verified control for: Breach Notification."})]}),(0,s.jsxs)("tr",{"data-category":"Organizational",children:[s.jsx("td",{className:`${d().td} ${d().checkmark}`,children:"✓"}),s.jsx("td",{className:d().td,children:"Board Oversight Briefings Conducted"}),s.jsx("td",{className:`${d().td} ${d().category}`,children:"Organizational"}),s.jsx("td",{className:d().td,children:"BenOsphere has implemented and verified control for: Board Oversight Briefings Conducted."})]}),(0,s.jsxs)("tr",{"data-category":"Organizational",children:[s.jsx("td",{className:`${d().td} ${d().checkmark}`,children:"✓"}),s.jsx("td",{className:d().td,children:"Code of Conduct"}),s.jsx("td",{className:`${d().td} ${d().category}`,children:"Organizational"}),s.jsx("td",{className:d().td,children:"BenOsphere has implemented and verified control for: Code of Conduct."})]}),(0,s.jsxs)("tr",{"data-category":"Product",children:[s.jsx("td",{className:`${d().td} ${d().checkmark}`,children:"✓"}),s.jsx("td",{className:d().td,children:"Commitments Explained to Customers"}),s.jsx("td",{className:`${d().td} ${d().category}`,children:"Product"}),s.jsx("td",{className:d().td,children:"BenOsphere has implemented and verified control for: Commitments Explained to Customers."})]}),(0,s.jsxs)("tr",{"data-category":"Organizational",children:[s.jsx("td",{className:`${d().td} ${d().checkmark}`,children:"✓"}),s.jsx("td",{className:d().td,children:"Communication to 3rd Parties"}),s.jsx("td",{className:`${d().td} ${d().category}`,children:"Organizational"}),s.jsx("td",{className:d().td,children:"BenOsphere has implemented and verified control for: Communication to 3rd Parties."})]}),(0,s.jsxs)("tr",{"data-category":"Organizational",children:[s.jsx("td",{className:`${d().td} ${d().checkmark}`,children:"✓"}),s.jsx("td",{className:d().td,children:"Conduct Control Self-Assessments"}),s.jsx("td",{className:`${d().td} ${d().category}`,children:"Organizational"}),s.jsx("td",{className:d().td,children:"BenOsphere has implemented and verified control for: Conduct Control Self-Assessments."})]}),(0,s.jsxs)("tr",{"data-category":"Organizational",children:[s.jsx("td",{className:`${d().td} ${d().checkmark}`,children:"✓"}),s.jsx("td",{className:d().td,children:"Continuous Control Monitoring"}),s.jsx("td",{className:`${d().td} ${d().category}`,children:"Organizational"}),s.jsx("td",{className:d().td,children:"Critical infrastructure components are continuously monitored with alerts configured for anomalous behavior or system failures."})]}),(0,s.jsxs)("tr",{"data-category":"Organizational",children:[s.jsx("td",{className:`${d().td} ${d().checkmark}`,children:"✓"}),s.jsx("td",{className:d().td,children:"Defined Management Roles & Responsibilities"}),s.jsx("td",{className:`${d().td} ${d().category}`,children:"Organizational"}),s.jsx("td",{className:d().td,children:"BenOsphere has implemented and verified control for: Defined Management Roles & Responsibilities."})]}),(0,s.jsxs)("tr",{"data-category":"Organizational",children:[s.jsx("td",{className:`${d().td} ${d().checkmark}`,children:"✓"}),s.jsx("td",{className:d().td,children:"Data Protection Policy"}),s.jsx("td",{className:`${d().td} ${d().category}`,children:"Organizational"}),s.jsx("td",{className:d().td,children:"A formal Data Protection Policy is in place to govern consistent and secure operations across the organization."})]}),(0,s.jsxs)("tr",{"data-category":"Infrastructure",children:[s.jsx("td",{className:`${d().td} ${d().checkmark}`,children:"✓"}),s.jsx("td",{className:d().td,children:"DLP (Data Loss Prevention) Software is Used"}),s.jsx("td",{className:`${d().td} ${d().category}`,children:"Infrastructure"}),s.jsx("td",{className:d().td,children:"BenOsphere has implemented and verified control for: DLP (Data Loss Prevention) Software is Used."})]}),(0,s.jsxs)("tr",{"data-category":"Organizational",children:[s.jsx("td",{className:`${d().td} ${d().checkmark}`,children:"✓"}),s.jsx("td",{className:d().td,children:"Disclosure with 3rd Parties"}),s.jsx("td",{className:`${d().td} ${d().category}`,children:"Organizational"}),s.jsx("td",{className:d().td,children:"BenOsphere has implemented and verified control for: Disclosure with 3rd Parties."})]}),(0,s.jsxs)("tr",{"data-category":"Organizational",children:[s.jsx("td",{className:`${d().td} ${d().checkmark}`,children:"✓"}),s.jsx("td",{className:d().td,children:"Disclosure Process for Customers"}),s.jsx("td",{className:`${d().td} ${d().category}`,children:"Organizational"}),s.jsx("td",{className:d().td,children:"BenOsphere has implemented and verified control for: Disclosure Process for Customers."})]}),(0,s.jsxs)("tr",{"data-category":"Organizational",children:[s.jsx("td",{className:`${d().td} ${d().checkmark}`,children:"✓"}),s.jsx("td",{className:d().td,children:"Information Security Policy"}),s.jsx("td",{className:`${d().td} ${d().category}`,children:"Organizational"}),s.jsx("td",{className:d().td,children:"A formal Information Security Policy is in place to govern consistent and secure operations across the organization."})]}),(0,s.jsxs)("tr",{"data-category":"Organizational",children:[s.jsx("td",{className:`${d().td} ${d().checkmark}`,children:"✓"}),s.jsx("td",{className:d().td,children:"Information Security Skills Matrix"}),s.jsx("td",{className:`${d().td} ${d().category}`,children:"Organizational"}),s.jsx("td",{className:d().td,children:"BenOsphere has implemented and verified control for: Information Security Skills Matrix."})]}),(0,s.jsxs)("tr",{"data-category":"Organizational",children:[s.jsx("td",{className:`${d().td} ${d().checkmark}`,children:"✓"}),s.jsx("td",{className:d().td,children:"Maintains a Privacy Policy"}),s.jsx("td",{className:`${d().td} ${d().category}`,children:"Organizational"}),s.jsx("td",{className:d().td,children:"A formal Maintains a Privacy Policy is in place to govern consistent and secure operations across the organization."})]}),(0,s.jsxs)("tr",{"data-category":"Organizational",children:[s.jsx("td",{className:`${d().td} ${d().checkmark}`,children:"✓"}),s.jsx("td",{className:d().td,children:"Maintains Asset Inventory"}),s.jsx("td",{className:`${d().td} ${d().category}`,children:"Organizational"}),s.jsx("td",{className:d().td,children:"BenOsphere has implemented and verified control for: Maintains Asset Inventory."})]}),(0,s.jsxs)("tr",{"data-category":"Infrastructure",children:[s.jsx("td",{className:`${d().td} ${d().checkmark}`,children:"✓"}),s.jsx("td",{className:d().td,children:"Messaging Queues Monitored and Alarmed"}),s.jsx("td",{className:`${d().td} ${d().category}`,children:"Infrastructure"}),s.jsx("td",{className:d().td,children:"Critical infrastructure components are continuously monitored with alerts configured for anomalous behavior or system failures."})]}),(0,s.jsxs)("tr",{"data-category":"Organizational",children:[s.jsx("td",{className:`${d().td} ${d().checkmark}`,children:"✓"}),s.jsx("td",{className:d().td,children:"Notice of Breach to Affected Users"}),s.jsx("td",{className:`${d().td} ${d().category}`,children:"Organizational"}),s.jsx("td",{className:d().td,children:"Each user is provisioned with a unique account. Access is monitored and governed by least privilege principles."})]}),(0,s.jsxs)("tr",{"data-category":"Organizational",children:[s.jsx("td",{className:`${d().td} ${d().checkmark}`,children:"✓"}),s.jsx("td",{className:d().td,children:"PII with 3rd Parties and Vendors"}),s.jsx("td",{className:`${d().td} ${d().category}`,children:"Organizational"}),s.jsx("td",{className:d().td,children:"Vendors are assessed for compliance, and agreements are maintained to ensure they meet BenOsphere’s security and privacy standards."})]}),(0,s.jsxs)("tr",{"data-category":"Organizational",children:[s.jsx("td",{className:`${d().td} ${d().checkmark}`,children:"✓"}),s.jsx("td",{className:d().td,children:"Privacy Policy Includes 3rd Party Vendors"}),s.jsx("td",{className:`${d().td} ${d().category}`,children:"Organizational"}),s.jsx("td",{className:d().td,children:"A formal Privacy Policy Includes 3rd Party Vendors is in place to govern consistent and secure operations across the organization."})]}),(0,s.jsxs)("tr",{"data-category":"Organizational",children:[s.jsx("td",{className:`${d().td} ${d().checkmark}`,children:"✓"}),s.jsx("td",{className:d().td,children:"Privacy Policy Publicly Available"}),s.jsx("td",{className:`${d().td} ${d().category}`,children:"Organizational"}),s.jsx("td",{className:d().td,children:"A formal Privacy Policy Publicly Available is in place to govern consistent and secure operations across the organization."})]}),(0,s.jsxs)("tr",{"data-category":"Organizational",children:[s.jsx("td",{className:`${d().td} ${d().checkmark}`,children:"✓"}),s.jsx("td",{className:d().td,children:"Privacy, Use, and Disclosure"}),s.jsx("td",{className:`${d().td} ${d().category}`,children:"Organizational"}),s.jsx("td",{className:d().td,children:"BenOsphere has implemented and verified control for: Privacy, Use, and Disclosure."})]}),(0,s.jsxs)("tr",{"data-category":"Organizational",children:[s.jsx("td",{className:`${d().td} ${d().checkmark}`,children:"✓"}),s.jsx("td",{className:d().td,children:"Provide Notice of Privacy Practices"}),s.jsx("td",{className:`${d().td} ${d().category}`,children:"Organizational"}),s.jsx("td",{className:d().td,children:"BenOsphere has implemented and verified control for: Provide Notice of Privacy Practices."})]}),(0,s.jsxs)("tr",{"data-category":"Organizational",children:[s.jsx("td",{className:`${d().td} ${d().checkmark}`,children:"✓"}),s.jsx("td",{className:d().td,children:"Quarterly Review of Privacy Compliance"}),s.jsx("td",{className:`${d().td} ${d().category}`,children:"Organizational"}),s.jsx("td",{className:d().td,children:"Security controls and data access are reviewed regularly to maintain regulatory compliance and identify improvement opportunities."})]}),(0,s.jsxs)("tr",{"data-category":"Organizational",children:[s.jsx("td",{className:`${d().td} ${d().checkmark}`,children:"✓"}),s.jsx("td",{className:d().td,children:"Remediation Plan"}),s.jsx("td",{className:`${d().td} ${d().category}`,children:"Organizational"}),s.jsx("td",{className:d().td,children:"BenOsphere has implemented and verified control for: Remediation Plan."})]}),(0,s.jsxs)("tr",{"data-category":"Organizational",children:[s.jsx("td",{className:`${d().td} ${d().checkmark}`,children:"✓"}),s.jsx("td",{className:d().td,children:"Review Privacy Notice Annually"}),s.jsx("td",{className:`${d().td} ${d().category}`,children:"Organizational"}),s.jsx("td",{className:d().td,children:"Security controls and data access are reviewed regularly to maintain regulatory compliance and identify improvement opportunities."})]}),(0,s.jsxs)("tr",{"data-category":"Organizational",children:[s.jsx("td",{className:`${d().td} ${d().checkmark}`,children:"✓"}),s.jsx("td",{className:d().td,children:"Risk Assessment Policy"}),s.jsx("td",{className:`${d().td} ${d().category}`,children:"Organizational"}),s.jsx("td",{className:d().td,children:"A formal Risk Assessment Policy is in place to govern consistent and secure operations across the organization."})]}),(0,s.jsxs)("tr",{"data-category":"Organizational",children:[s.jsx("td",{className:`${d().td} ${d().checkmark}`,children:"✓"}),s.jsx("td",{className:d().td,children:"Security Team Communicates in a Timely Manner"}),s.jsx("td",{className:`${d().td} ${d().category}`,children:"Organizational"}),s.jsx("td",{className:d().td,children:"A dedicated security team oversees compliance efforts and ensures accountability at the organizational level."})]}),(0,s.jsxs)("tr",{"data-category":"Organizational",children:[s.jsx("td",{className:`${d().td} ${d().checkmark}`,children:"✓"}),s.jsx("td",{className:d().td,children:"Security Team/Steering Committee"}),s.jsx("td",{className:`${d().td} ${d().category}`,children:"Organizational"}),s.jsx("td",{className:d().td,children:"A dedicated security team oversees compliance efforts and ensures accountability at the organizational level."})]}),(0,s.jsxs)("tr",{"data-category":"Organizational",children:[s.jsx("td",{className:`${d().td} ${d().checkmark}`,children:"✓"}),s.jsx("td",{className:d().td,children:"Security Training"}),s.jsx("td",{className:`${d().td} ${d().category}`,children:"Organizational"}),s.jsx("td",{className:d().td,children:"All employees and contractors complete regular HIPAA and cybersecurity training to ensure awareness of responsibilities and threats."})]}),(0,s.jsxs)("tr",{"data-category":"Infrastructure",children:[s.jsx("td",{className:`${d().td} ${d().checkmark}`,children:"✓"}),s.jsx("td",{className:d().td,children:"Security Updates"}),s.jsx("td",{className:`${d().td} ${d().category}`,children:"Infrastructure"}),s.jsx("td",{className:d().td,children:"BenOsphere has implemented and verified control for: Security Updates."})]}),(0,s.jsxs)("tr",{"data-category":"Organizational",children:[s.jsx("td",{className:`${d().td} ${d().checkmark}`,children:"✓"}),s.jsx("td",{className:d().td,children:"Software Development Life Cycle Policy"}),s.jsx("td",{className:`${d().td} ${d().category}`,children:"Organizational"}),s.jsx("td",{className:d().td,children:"A formal Software Development Life Cycle Policy is in place to govern consistent and secure operations across the organization."})]}),(0,s.jsxs)("tr",{"data-category":"Organizational",children:[s.jsx("td",{className:`${d().td} ${d().checkmark}`,children:"✓"}),s.jsx("td",{className:d().td,children:"Storage of Sensitive Data on Paper"}),s.jsx("td",{className:`${d().td} ${d().category}`,children:"Organizational"}),s.jsx("td",{className:d().td,children:"BenOsphere has implemented and verified control for: Storage of Sensitive Data on Paper."})]}),(0,s.jsxs)("tr",{"data-category":"Organizational",children:[s.jsx("td",{className:`${d().td} ${d().checkmark}`,children:"✓"}),s.jsx("td",{className:d().td,children:"System Access Control Policy"}),s.jsx("td",{className:`${d().td} ${d().category}`,children:"Organizational"}),s.jsx("td",{className:d().td,children:"A formal System Access Control Policy is in place to govern consistent and secure operations across the organization."})]}),(0,s.jsxs)("tr",{"data-category":"Organizational",children:[s.jsx("td",{className:`${d().td} ${d().checkmark}`,children:"✓"}),s.jsx("td",{className:d().td,children:"Unauthorized Disclosures by 3rd Parties"}),s.jsx("td",{className:`${d().td} ${d().category}`,children:"Organizational"}),s.jsx("td",{className:d().td,children:"BenOsphere has implemented and verified control for: Unauthorized Disclosures by 3rd Parties."})]}),(0,s.jsxs)("tr",{"data-category":"Organizational",children:[s.jsx("td",{className:`${d().td} ${d().checkmark}`,children:"✓"}),s.jsx("td",{className:d().td,children:"Vendor Agreements Maintained"}),s.jsx("td",{className:`${d().td} ${d().category}`,children:"Organizational"}),s.jsx("td",{className:d().td,children:"Vendors are assessed for compliance, and agreements are maintained to ensure they meet BenOsphere’s security and privacy standards."})]}),(0,s.jsxs)("tr",{"data-category":"Organizational",children:[s.jsx("td",{className:`${d().td} ${d().checkmark}`,children:"✓"}),s.jsx("td",{className:d().td,children:"Vendor Compliance Reports"}),s.jsx("td",{className:`${d().td} ${d().category}`,children:"Organizational"}),s.jsx("td",{className:d().td,children:"Vendors are assessed for compliance, and agreements are maintained to ensure they meet BenOsphere’s security and privacy standards."})]}),(0,s.jsxs)("tr",{"data-category":"Organizational",children:[s.jsx("td",{className:`${d().td} ${d().checkmark}`,children:"✓"}),s.jsx("td",{className:d().td,children:"Vendor Management Policy"}),s.jsx("td",{className:`${d().td} ${d().category}`,children:"Organizational"}),s.jsx("td",{className:d().td,children:"A formal Vendor Management Policy is in place to govern consistent and secure operations across the organization."})]}),(0,s.jsxs)("tr",{"data-category":"Organizational",children:[s.jsx("td",{className:`${d().td} ${d().checkmark}`,children:"✓"}),s.jsx("td",{className:d().td,children:"Vendors and PHI"}),s.jsx("td",{className:`${d().td} ${d().category}`,children:"Organizational"}),s.jsx("td",{className:d().td,children:"Vendors are assessed for compliance, and agreements are maintained to ensure they meet BenOsphere’s security and privacy standards."})]}),(0,s.jsxs)("tr",{"data-category":"Organizational",children:[s.jsx("td",{className:`${d().td} ${d().checkmark}`,children:"✓"}),s.jsx("td",{className:d().td,children:"Annual Risk Assessment"}),s.jsx("td",{className:`${d().td} ${d().category}`,children:"Organizational"}),s.jsx("td",{className:d().td,children:"BenOsphere has implemented and verified control for: Annual Risk Assessment."})]}),(0,s.jsxs)("tr",{"data-category":"Infrastructure",children:[s.jsx("td",{className:`${d().td} ${d().checkmark}`,children:"✓"}),s.jsx("td",{className:d().td,children:"Intrusion Detection System in Place"}),s.jsx("td",{className:`${d().td} ${d().category}`,children:"Infrastructure"}),s.jsx("td",{className:d().td,children:"BenOsphere has implemented and verified control for: Intrusion Detection System in Place."})]})]})]})})})]})})}},25676:e=>{e.exports={container:"benosphere_container__ikQpW",header:"benosphere_header__X0nC6",title:"benosphere_title__IT2vR",subtitle:"benosphere_subtitle__QQnHD",link:"benosphere_link__dOmks",tableContainer:"benosphere_tableContainer__aUKYv",filterContainer:"benosphere_filterContainer__6MXmY",filterButton:"benosphere_filterButton__xftuv",filterButtonActive:"benosphere_filterButtonActive__iEhmg",table:"benosphere_table__w3Gjt",th:"benosphere_th__0yscr",td:"benosphere_td__9_xQO",tr:"benosphere_tr__pthAH",checkmark:"benosphere_checkmark__R4DMJ",category:"benosphere_category__T8kJo",infoBox:"benosphere_infoBox__VCt9D",infoBoxHeader:"benosphere_infoBoxHeader__y2Q5k",infoText:"benosphere_infoText__5wMhH",infoBoxLogo:"benosphere_infoBoxLogo__xtwDn"}},27019:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>s});let s=(0,t(68570).createProxy)(String.raw`C:\beno\project_dev\qharmony-frontend-v1\src\app\hipaa\page.tsx#default`)},73881:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>r});var s=t(66621);let r=e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",e.params,"favicon.ico")+""}]}};var a=require("../../webpack-runtime.js");a.C(e);var t=e=>a(a.s=e),s=a.X(0,[8948,1183,6621,576],()=>t(1636));module.exports=s})();