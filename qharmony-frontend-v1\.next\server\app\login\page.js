(()=>{var e={};e.id=2626,e.ids=[2626],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},27790:e=>{"use strict";e.exports=require("assert")},84770:e=>{"use strict";e.exports=require("crypto")},17702:e=>{"use strict";e.exports=require("events")},92048:e=>{"use strict";e.exports=require("fs")},32615:e=>{"use strict";e.exports=require("http")},35240:e=>{"use strict";e.exports=require("https")},19801:e=>{"use strict";e.exports=require("os")},55315:e=>{"use strict";e.exports=require("path")},76162:e=>{"use strict";e.exports=require("stream")},74026:e=>{"use strict";e.exports=require("string_decoder")},74175:e=>{"use strict";e.exports=require("tty")},17360:e=>{"use strict";e.exports=require("url")},21764:e=>{"use strict";e.exports=require("util")},71568:e=>{"use strict";e.exports=require("zlib")},98061:e=>{"use strict";e.exports=require("node:assert")},92761:e=>{"use strict";e.exports=require("node:async_hooks")},72254:e=>{"use strict";e.exports=require("node:buffer")},40027:e=>{"use strict";e.exports=require("node:console")},6005:e=>{"use strict";e.exports=require("node:crypto")},65714:e=>{"use strict";e.exports=require("node:diagnostics_channel")},15673:e=>{"use strict";e.exports=require("node:events")},88849:e=>{"use strict";e.exports=require("node:http")},42725:e=>{"use strict";e.exports=require("node:http2")},87503:e=>{"use strict";e.exports=require("node:net")},38846:e=>{"use strict";e.exports=require("node:perf_hooks")},39630:e=>{"use strict";e.exports=require("node:querystring")},84492:e=>{"use strict";e.exports=require("node:stream")},31764:e=>{"use strict";e.exports=require("node:tls")},41041:e=>{"use strict";e.exports=require("node:url")},47261:e=>{"use strict";e.exports=require("node:util")},93746:e=>{"use strict";e.exports=require("node:util/types")},24086:e=>{"use strict";e.exports=require("node:worker_threads")},65628:e=>{"use strict";e.exports=require("node:zlib")},3565:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>o.a,__next_app__:()=>g,originalPathname:()=>d,pages:()=>u,routeModule:()=>p,tree:()=>c}),r(80974),r(33709),r(35866);var i=r(23191),s=r(88716),n=r(37922),o=r.n(n),a=r(95231),l={};for(let e in a)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>a[e]);r.d(t,l);let c=["",{children:["login",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,80974)),"C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\login\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,33709)),"C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,35866,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],u=["C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\login\\page.tsx"],d="/login/page",g={require:r,loadChunk:()=>Promise.resolve()},p=new i.AppPageRouteModule({definition:{kind:s.x.APP_PAGE,page:"/login/page",pathname:"/login",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},79958:(e,t,r)=>{Promise.resolve().then(r.bind(r,24146))},24146:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>h});var i=r(10326),s=r(17577),n=r(46791),o=r(26472),a=r(35047),l=r(6283),c=r(98139),u=r(25609),d=r(59423),g=r(62432),p=r(90434);let h=()=>{let e=(0,a.useRouter)(),[t,r]=(0,s.useState)(null),[h,x]=(0,s.useState)(null),[A,m]=(0,s.useState)(null),[f,b]=(0,s.useState)(!0),[v,y]=(0,s.useState)(null);return(0,s.useEffect)(()=>{(async()=>{let e=window.location.href,t=await (0,d.lY)(e),i=t.email,s=t.companyId,a=t.userId;if((0,n.JB)(o.I,e)){let e=window.localStorage.getItem("emailForSignIn1");e||t.email?(r(e||i),x(s),m(a)):y("Invalid sign-in link. Email not found.")}else y("Invalid sign-in link."),b(!1)})()},[]),(0,s.useEffect)(()=>{(async()=>{if(t)try{await (0,n.P6)(o.I,t,window.location.href),console.log("Sign-in successful"),window.localStorage.removeItem("emailForSignIn1"),localStorage.setItem("userid1",A),localStorage.setItem("companyId1",h),(0,d.Ig)(h,A),b(!1),e.push("/dashboard")}catch(e){console.error("Error signing in with email link:",e),y("Failed to sign in. Please try again."),b(!1)}})()},[t,h,A,e]),i.jsx(l.Z,{sx:{mt:5,textAlign:"center"},children:f?(0,i.jsxs)(i.Fragment,{children:[i.jsx(c.Z,{}),i.jsx(u.Z,{variant:"h6",sx:{mt:2},children:"Signing you in..."})]}):v?i.jsx(g.Z,{LeftComponent:(0,i.jsxs)(l.Z,{sx:{display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",color:"white",textAlign:"center",height:"100%"},children:[i.jsx(u.Z,{sx:{fontWeight:"bold",fontSize:"60px",mb:2},children:"❌ Magic Link Expired"}),(0,i.jsxs)(u.Z,{variant:"body1",sx:{fontSize:"20px",color:"#bbbbbb",mb:4},children:["You can sign in again by requesting a new link ",i.jsx(p.default,{href:"/",style:{color:"#B983FF",textDecoration:"underline"},children:"here"}),"."]})]})}):null})}},62432:(e,t,r)=>{"use strict";r.d(t,{Z:()=>d});var i=r(10326),s=r(16027),n=r(6283),o=r(25609),a=r(46226);let l=(0,r(51426).Z)((0,i.jsx)("path",{d:"M6 17h3l2-4V7H5v6h3zm8 0h3l2-4V7h-6v6h3z"}),"FormatQuote");var c=r(17577);let u=[{src:{src:"/_next/static/media/landing_page_image_1.883ba61a.png",height:1024,width:756,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAYAAAAICAIAAABVpBlvAAAAoUlEQVR42mOwNNRyMlRnYJRQUlNXNbISV9RgcHcwdrAwTPJ2ctVWFlTVkdPUZ7CxMw51MVvaklcb5cnAKqqoosGgbqrTmRe7e1ZbqZ+jnY6WgoEJg6CMUpify5UN07tivCU19O19fBiAoDjO/dzazqIoNwZ1CyklDYbGyTMO7N82rzwkkJchu6K6sr2D4eiV63suXl+3c09rU2Nt74TmKdMAG00wAXeqZ/wAAAAASUVORK5CYII=",blurWidth:6,blurHeight:8},text:"I get to keep track of all my benefits so I never miss out on the stuff that matters most.",name:"Troy Sivan",title:"Associate Manager"},{src:{src:"/_next/static/media/landing_page_image_2.59f16961.png",height:1024,width:756,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAYAAAAICAIAAABVpBlvAAAAo0lEQVR42mOYMakxwMdRRU/Lzs4sNtTTx9WGYeXS6TYOlik2GjGmKpbWJgWJgQwbN62M9rTbXJexsiTBWkXWydmWYcuurZO7G45NqluR6nfs+IkZq9cxHD1zatqS+Vsn1E4I0L3+4MH6nVsZZi9aFJWevXvn3i1rNtd2TQsMcmYAAgc3v8Ub989Zu6+kvgfEl5CWd/MJmrd8Y++0RYUVjQwMDACH00ArwNCIEAAAAABJRU5ErkJggg==",blurWidth:6,blurHeight:8},text:"Now I can track my benefits instantly without having to search through emails. It’s a game–changer!",name:"David Miller",title:"Software Engineer"},{src:{src:"/_next/static/media/landing_page_image_3.e9420572.png",height:1024,width:756,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAYAAAAICAIAAABVpBlvAAAAn0lEQVR42mNQE2YNsRJlYGAwlmL3UuUwFGNmcNbgXlNuNCkz0FtHTpydwUSSlUGBnaHYW29jc3pXoo8KA4ORNBeDOBODl7Ha3JyQQk8LLgYGYS4WBlN5PgYGhjgHw2wPM0FJGQYgsNeQZNbQj7HRzQlyU7WwU1VVZ3CyMrG2ssxxM9M2sTK0tFFW12IwsrF1MDN2tbPXs3TQN7MQk5ACAOjZG1OaugBXAAAAAElFTkSuQmCC",blurWidth:6,blurHeight:8},text:"This is by far the most convenient way to access my benefits. Everything I need is right at my fingertips.",name:"Emily Garcia",title:"HR Specialist"},{src:{src:"/_next/static/media/landing_page_image_4.135a5874.png",height:1024,width:756,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAYAAAAICAIAAABVpBlvAAAAo0lEQVR42gGYAGf/AIl9cqKWioB2bZSGenZuZlpVUQCgmZKMh4KGWD++oI5yaWRtbGkArK+snpmWkl9G0rWooZSGbXZxAH+Af5mbmnpRRLWVh4qGe1JnQgCTfm+Wk456Y1evmYqprrBhbWAAnZWUoaKjsKWg1c/M5O/zwczSALzFy8nT2rnDy9vn7NXh583Y3gCoq6/CzdXDzdTI09nR3ePU3+Q7J1hGmDSqYQAAAABJRU5ErkJggg==",blurWidth:6,blurHeight:8},text:"I love how I can get all my benefits info through Slack. It saves me so much time!",name:"Troy Edward",title:"Associate Manager"}],d=({LeftComponent:e})=>{let[t,r]=(0,c.useState)(0);(0,c.useEffect)(()=>{let e=setInterval(()=>{r(e=>(e+1)%u.length)},5e3);return()=>clearInterval(e)},[]);let d=u[t];return(0,i.jsxs)(s.ZP,{container:!0,style:{height:"95vh",width:"100%"},children:[i.jsx(s.ZP,{item:!0,xs:12,md:6,sx:{bgcolor:"#000000",color:"#ffffff",display:"flex",flexDirection:"column",justifyContent:"center",alignItems:"center",padding:"100px",height:"95vh",overflow:"auto"},children:e}),i.jsx(s.ZP,{item:!0,xs:12,md:6,sx:{display:"flex",justifyContent:"center",alignItems:"center",backgroundColor:"#f8f9fa",padding:"0",position:"relative",overflow:"hidden",height:"95vh"},children:(0,i.jsxs)(n.Z,{sx:{position:"relative",width:"100%",height:"100%",overflow:"hidden"},children:[i.jsx(a.default,{src:d.src,alt:"Profile",layout:"fill",objectFit:"cover",style:{objectPosition:"center"}}),i.jsx(n.Z,{sx:{position:"absolute",bottom:0,left:0,width:"100%",height:"100%",background:"linear-gradient(to top, rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0) 60%)"}}),(0,i.jsxs)(n.Z,{sx:{position:"absolute",bottom:"10%",left:"10%",color:"#ffffff",width:"550px"},children:[i.jsx(l,{sx:{fontSize:70,marginBottom:"10px",opacity:.5,marginLeft:-2}}),i.jsx(o.Z,{variant:"h5",sx:{fontSize:"32px",fontWeight:"bold",lineHeight:"1.5",mb:2},children:d.text}),i.jsx(o.Z,{variant:"h5",sx:{fontSize:"24px",fontWeight:"bold",lineHeight:"1.5",mt:4},children:d.name}),i.jsx(o.Z,{variant:"body2",sx:{fontSize:"20px",fontWeight:"light"},children:d.title})]})]})})]})}},80974:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i});let i=(0,r(68570).createProxy)(String.raw`C:\beno\project_dev\qharmony-frontend-v1\src\app\login\page.tsx#default`)},73881:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});var i=r(66621);let s=e=>[{type:"image/x-icon",sizes:"16x16",url:(0,i.fillMetadataSegment)(".",e.params,"favicon.ico")+""}]}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),i=t.X(0,[8948,1183,6621,9066,1999,6027,434,576],()=>r(3565));module.exports=i})();