(()=>{var e={};e.id=5300,e.ids=[5300],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},27790:e=>{"use strict";e.exports=require("assert")},84770:e=>{"use strict";e.exports=require("crypto")},17702:e=>{"use strict";e.exports=require("events")},92048:e=>{"use strict";e.exports=require("fs")},32615:e=>{"use strict";e.exports=require("http")},35240:e=>{"use strict";e.exports=require("https")},19801:e=>{"use strict";e.exports=require("os")},55315:e=>{"use strict";e.exports=require("path")},76162:e=>{"use strict";e.exports=require("stream")},74026:e=>{"use strict";e.exports=require("string_decoder")},74175:e=>{"use strict";e.exports=require("tty")},17360:e=>{"use strict";e.exports=require("url")},21764:e=>{"use strict";e.exports=require("util")},71568:e=>{"use strict";e.exports=require("zlib")},98061:e=>{"use strict";e.exports=require("node:assert")},92761:e=>{"use strict";e.exports=require("node:async_hooks")},72254:e=>{"use strict";e.exports=require("node:buffer")},40027:e=>{"use strict";e.exports=require("node:console")},6005:e=>{"use strict";e.exports=require("node:crypto")},65714:e=>{"use strict";e.exports=require("node:diagnostics_channel")},15673:e=>{"use strict";e.exports=require("node:events")},88849:e=>{"use strict";e.exports=require("node:http")},42725:e=>{"use strict";e.exports=require("node:http2")},87503:e=>{"use strict";e.exports=require("node:net")},38846:e=>{"use strict";e.exports=require("node:perf_hooks")},39630:e=>{"use strict";e.exports=require("node:querystring")},84492:e=>{"use strict";e.exports=require("node:stream")},31764:e=>{"use strict";e.exports=require("node:tls")},41041:e=>{"use strict";e.exports=require("node:url")},47261:e=>{"use strict";e.exports=require("node:util")},93746:e=>{"use strict";e.exports=require("node:util/types")},24086:e=>{"use strict";e.exports=require("node:worker_threads")},65628:e=>{"use strict";e.exports=require("node:zlib")},45386:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>a.a,__next_app__:()=>u,originalPathname:()=>c,pages:()=>p,routeModule:()=>m,tree:()=>d}),r(29801),r(33709),r(35866);var o=r(23191),s=r(88716),n=r(37922),a=r.n(n),i=r(95231),l={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);r.d(t,l);let d=["",{children:["manage-companies",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,29801)),"C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\manage-companies\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,33709)),"C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,35866,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],p=["C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\manage-companies\\page.tsx"],c="/manage-companies/page",u={require:r,loadChunk:()=>Promise.resolve()},m=new o.AppPageRouteModule({definition:{kind:s.x.APP_PAGE,page:"/manage-companies/page",pathname:"/manage-companies",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},25566:(e,t,r)=>{Promise.resolve().then(r.bind(r,36863))},75887:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n});var o=r(51426),s=r(10326);let n=(0,o.Z)((0,s.jsx)("path",{d:"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2m-2 15-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8z"}),"CheckCircle")},36690:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n});var o=r(51426),s=r(10326);let n=(0,o.Z)((0,s.jsx)("path",{d:"M19 6.41 17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"}),"Close")},48260:(e,t,r)=>{"use strict";r.d(t,{Z:()=>q});var o=r(17577),s=r(41135),n=r(88634),a=r(87816),i=r(44823),l=r(91703),d=r(13643),p=r(40955),c=r(2791),u=r(49006),m=r(98139),x=r(54641),g=r(71685),h=r(97898);function f(e){return(0,h.ZP)("MuiIconButton",e)}let y=(0,g.Z)("MuiIconButton",["root","disabled","colorInherit","colorPrimary","colorSecondary","colorError","colorInfo","colorSuccess","colorWarning","edgeStart","edgeEnd","sizeSmall","sizeMedium","sizeLarge","loading","loadingIndicator","loadingWrapper"]);var v=r(10326);let b=e=>{let{classes:t,disabled:r,color:o,edge:s,size:a,loading:i}=e,l={root:["root",i&&"loading",r&&"disabled","default"!==o&&`color${(0,x.Z)(o)}`,s&&`edge${(0,x.Z)(s)}`,`size${(0,x.Z)(a)}`],loadingIndicator:["loadingIndicator"],loadingWrapper:["loadingWrapper"]};return(0,n.Z)(l,f,t)},Z=(0,l.default)(u.Z,{name:"MuiIconButton",slot:"Root",overridesResolver:(e,t)=>{let{ownerState:r}=e;return[t.root,r.loading&&t.loading,"default"!==r.color&&t[`color${(0,x.Z)(r.color)}`],r.edge&&t[`edge${(0,x.Z)(r.edge)}`],t[`size${(0,x.Z)(r.size)}`]]}})((0,d.Z)(({theme:e})=>({textAlign:"center",flex:"0 0 auto",fontSize:e.typography.pxToRem(24),padding:8,borderRadius:"50%",color:(e.vars||e).palette.action.active,transition:e.transitions.create("background-color",{duration:e.transitions.duration.shortest}),variants:[{props:e=>!e.disableRipple,style:{"--IconButton-hoverBg":e.vars?`rgba(${e.vars.palette.action.activeChannel} / ${e.vars.palette.action.hoverOpacity})`:(0,i.Fq)(e.palette.action.active,e.palette.action.hoverOpacity),"&:hover":{backgroundColor:"var(--IconButton-hoverBg)","@media (hover: none)":{backgroundColor:"transparent"}}}},{props:{edge:"start"},style:{marginLeft:-12}},{props:{edge:"start",size:"small"},style:{marginLeft:-3}},{props:{edge:"end"},style:{marginRight:-12}},{props:{edge:"end",size:"small"},style:{marginRight:-3}}]})),(0,d.Z)(({theme:e})=>({variants:[{props:{color:"inherit"},style:{color:"inherit"}},...Object.entries(e.palette).filter((0,p.Z)()).map(([t])=>({props:{color:t},style:{color:(e.vars||e).palette[t].main}})),...Object.entries(e.palette).filter((0,p.Z)()).map(([t])=>({props:{color:t},style:{"--IconButton-hoverBg":e.vars?`rgba(${(e.vars||e).palette[t].mainChannel} / ${e.vars.palette.action.hoverOpacity})`:(0,i.Fq)((e.vars||e).palette[t].main,e.palette.action.hoverOpacity)}})),{props:{size:"small"},style:{padding:5,fontSize:e.typography.pxToRem(18)}},{props:{size:"large"},style:{padding:12,fontSize:e.typography.pxToRem(28)}}],[`&.${y.disabled}`]:{backgroundColor:"transparent",color:(e.vars||e).palette.action.disabled},[`&.${y.loading}`]:{color:"transparent"}}))),j=(0,l.default)("span",{name:"MuiIconButton",slot:"LoadingIndicator",overridesResolver:(e,t)=>t.loadingIndicator})(({theme:e})=>({display:"none",position:"absolute",visibility:"visible",top:"50%",left:"50%",transform:"translate(-50%, -50%)",color:(e.vars||e).palette.action.disabled,variants:[{props:{loading:!0},style:{display:"flex"}}]})),q=o.forwardRef(function(e,t){let r=(0,c.i)({props:e,name:"MuiIconButton"}),{edge:o=!1,children:n,className:i,color:l="default",disabled:d=!1,disableFocusRipple:p=!1,size:u="medium",id:x,loading:g=null,loadingIndicator:h,...f}=r,y=(0,a.Z)(x),q=h??(0,v.jsx)(m.Z,{"aria-labelledby":y,color:"inherit",size:16}),C={...r,edge:o,color:l,disabled:d,disableFocusRipple:p,loading:g,loadingIndicator:q,size:u},P=b(C);return(0,v.jsxs)(Z,{id:g?y:x,className:(0,s.Z)(P.root,i),centerRipple:!0,focusRipple:!p,disabled:d||g,ref:t,...f,ownerState:C,children:["boolean"==typeof g&&(0,v.jsx)("span",{className:P.loadingWrapper,style:{display:"contents"},children:(0,v.jsx)(j,{className:P.loadingIndicator,ownerState:C,children:g&&q})}),n]})})},36863:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>w});var o=r(10326),s=r(17577),n=r(6283),a=r(16027),i=r(25609),l=r(42265),d=r(98139),p=r(33198),c=r(99207),u=r(12549),m=r(94638),x=r(25842),g=r(43058),h=r(31870),f=r(69995),y=r(98117),v=r(48260),b=r(28591),Z=r(90541),j=r(10163),q=r(36690),C=r(75887),P=r(35047);let I=({open:e,onClose:t})=>{let r=(0,h.T)();(0,P.useRouter)();let n=(0,h.C)(e=>e.user._id),[i,p]=(0,s.useState)({companyName:"",companyAdminEmail:"",companyAdminName:""}),[c,u]=(0,s.useState)(!1),[x,g]=(0,s.useState)("");(0,s.useEffect)(()=>{(0,m.qB)(r,n)},[r,n]);let I=(e,t)=>{p({...i,[e]:t})},A=async()=>{if(u(!0),!i.companyName||!i.companyAdminEmail||!i.companyAdminName){alert("Please fill out all required fields."),u(!1);return}let e=await (0,m.G9)(n,i.companyName,i.companyAdminEmail,i.companyAdminName);u(!1),e&&200===e.status&&(g("Invite has been sent to the admin! They will show up in your team members list once they accept the invite."),p({companyName:"",companyAdminEmail:"",companyAdminName:""}))};return(0,o.jsxs)(f.Z,{open:e,onClose:t,PaperProps:{style:{borderRadius:"16px",boxShadow:"0px 10px 30px rgba(0, 0, 0, 0.1)",padding:"5px",width:"550px"}},children:[(0,o.jsxs)(y.Z,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",fontWeight:"bold",fontSize:"1.5rem"},children:["Add Company and Admin",o.jsx(v.Z,{onClick:t,children:o.jsx(q.Z,{})})]}),(0,o.jsxs)(b.Z,{children:[(0,o.jsxs)(a.ZP,{container:!0,spacing:3,sx:{marginBottom:"16px",marginTop:"0px"},children:[o.jsx(a.ZP,{item:!0,xs:12,children:o.jsx(Z.Z,{fullWidth:!0,required:!0,label:"Company Name",variant:"outlined",value:i.companyName,onChange:e=>I("companyName",e.target.value),InputProps:{style:{borderRadius:"8px",backgroundColor:"#f9f9f9",height:"48px"}},InputLabelProps:{shrink:!0,style:{fontSize:"1rem"}},sx:{alignItems:"flex-start"}})}),o.jsx(a.ZP,{item:!0,xs:12,children:o.jsx(Z.Z,{fullWidth:!0,required:!0,label:"Admin Email",variant:"outlined",value:i.companyAdminEmail,onChange:e=>I("companyAdminEmail",e.target.value),InputProps:{style:{borderRadius:"8px",backgroundColor:"#f9f9f9",height:"48px"}},InputLabelProps:{shrink:!0,style:{fontSize:"1rem"}},sx:{alignItems:"flex-start"}})}),o.jsx(a.ZP,{item:!0,xs:12,children:o.jsx(Z.Z,{fullWidth:!0,required:!0,label:"Admin Name",variant:"outlined",value:i.companyAdminName,onChange:e=>I("companyAdminName",e.target.value),InputProps:{style:{borderRadius:"8px",backgroundColor:"#f9f9f9",height:"48px"}},InputLabelProps:{shrink:!0,style:{fontSize:"1rem"}},sx:{alignItems:"flex-start"}})})]}),c&&o.jsx(d.Z,{}),x&&(0,o.jsxs)("div",{style:{color:"green",marginTop:"10px",display:"flex",alignItems:"start"},children:[o.jsx(C.Z,{style:{marginRight:"10px"}}),x]})]}),o.jsx(j.Z,{sx:{padding:"16px"},children:x?o.jsx(l.Z,{onClick:()=>{g(""),p({companyName:"",companyAdminEmail:"",companyAdminName:""})},sx:{color:"#ffffff",backgroundColor:"#000000",borderRadius:"12px",padding:"8px 24px",textTransform:"none",fontWeight:"bold","&:hover":{backgroundColor:"#333333"}},children:"Add Another Company"}):o.jsx(l.Z,{onClick:A,sx:{color:"#ffffff",backgroundColor:"#000000",borderRadius:"12px",padding:"8px 24px",textTransform:"none",fontWeight:"bold","&:hover":{backgroundColor:"#333333"}},disabled:c,children:c?"Adding...":"Confirm Details"})})]})},A=["#FFB6C1","#FF6347","#FFD700","#90EE90","#00CED1","#1E90FF","#BA55D3"],k=e=>{let[t,r]=e.split(" ");return`${t[0]}${r?r[0]:""}`},_=e=>A[e%A.length],w=(0,u.Z)(()=>{let e=(0,x.I0)(),t=(0,h.C)(e=>e.user._id),r=(0,x.v9)(e=>e.user.managedCompanies),[u,f]=(0,s.useState)(!1),[y,v]=(0,s.useState)(!0);return(0,s.useEffect)(()=>{(async()=>{v(!0),await (0,m.qB)(e,t),v(!1)})()},[e,t]),o.jsx(g.Z,{children:(0,o.jsxs)(n.Z,{sx:{bgcolor:"#F5F6FA",p:4,width:"100%",height:"100vh",overflow:"auto"},children:[(0,o.jsxs)(a.ZP,{container:!0,alignItems:"center",justifyContent:"space-between",sx:{mb:2},children:[o.jsx(a.ZP,{item:!0,children:o.jsx(i.Z,{variant:"h5",children:"Team Members"})}),o.jsx(a.ZP,{item:!0,children:o.jsx(l.Z,{variant:"contained",color:"primary",onClick:()=>{f(!0)},sx:{backgroundColor:"#000000",textTransform:"none",borderRadius:"6px"},children:"Add new company"})})]}),o.jsx(n.Z,{sx:{bgcolor:"#ffffff",borderRadius:"12px",width:"100%",p:2,boxShadow:"0px 4px 12px rgba(0, 0, 0, 0.1)"},children:y?o.jsx(n.Z,{sx:{display:"flex",justifyContent:"center",my:4},children:o.jsx(d.Z,{})}):0===r.length?o.jsx(i.Z,{variant:"body1",sx:{textAlign:"center",my:4},children:"Add your first company to get started"}):r.map((e,t)=>(0,o.jsxs)(n.Z,{children:[(0,o.jsxs)(a.ZP,{container:!0,alignItems:"center",spacing:2,children:[(0,o.jsxs)(a.ZP,{item:!0,xs:3,container:!0,alignItems:"center",children:[o.jsx(p.Z,{sx:{bgcolor:_(t),color:"#ffffff",width:48,height:48,fontSize:"1.2rem",mr:2},children:k(e.name)}),o.jsx(i.Z,{variant:"body1",sx:{fontWeight:"bold"},children:e.name})]}),o.jsx(a.ZP,{item:!0,xs:3,children:o.jsx(i.Z,{variant:"body2",children:e.adminEmail})})]}),t<r.length-1&&o.jsx(c.Z,{sx:{my:2}})]},t))}),o.jsx(I,{open:u,onClose:()=>f(!1)})]})})})},29801:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>o});let o=(0,r(68570).createProxy)(String.raw`C:\beno\project_dev\qharmony-frontend-v1\src\app\manage-companies\page.tsx#default`)}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),o=t.X(0,[8948,1183,6621,9066,1999,3253,928,8097,8522,3141,6027,541,2142,576,6305,401,2549],()=>r(45386));module.exports=o})();