(()=>{var e={};e.id=4469,e.ids=[4469],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},27790:e=>{"use strict";e.exports=require("assert")},84770:e=>{"use strict";e.exports=require("crypto")},17702:e=>{"use strict";e.exports=require("events")},92048:e=>{"use strict";e.exports=require("fs")},32615:e=>{"use strict";e.exports=require("http")},35240:e=>{"use strict";e.exports=require("https")},19801:e=>{"use strict";e.exports=require("os")},55315:e=>{"use strict";e.exports=require("path")},76162:e=>{"use strict";e.exports=require("stream")},74026:e=>{"use strict";e.exports=require("string_decoder")},74175:e=>{"use strict";e.exports=require("tty")},17360:e=>{"use strict";e.exports=require("url")},21764:e=>{"use strict";e.exports=require("util")},71568:e=>{"use strict";e.exports=require("zlib")},98061:e=>{"use strict";e.exports=require("node:assert")},92761:e=>{"use strict";e.exports=require("node:async_hooks")},72254:e=>{"use strict";e.exports=require("node:buffer")},40027:e=>{"use strict";e.exports=require("node:console")},6005:e=>{"use strict";e.exports=require("node:crypto")},65714:e=>{"use strict";e.exports=require("node:diagnostics_channel")},15673:e=>{"use strict";e.exports=require("node:events")},88849:e=>{"use strict";e.exports=require("node:http")},42725:e=>{"use strict";e.exports=require("node:http2")},87503:e=>{"use strict";e.exports=require("node:net")},38846:e=>{"use strict";e.exports=require("node:perf_hooks")},39630:e=>{"use strict";e.exports=require("node:querystring")},84492:e=>{"use strict";e.exports=require("node:stream")},31764:e=>{"use strict";e.exports=require("node:tls")},41041:e=>{"use strict";e.exports=require("node:url")},47261:e=>{"use strict";e.exports=require("node:util")},93746:e=>{"use strict";e.exports=require("node:util/types")},24086:e=>{"use strict";e.exports=require("node:worker_threads")},65628:e=>{"use strict";e.exports=require("node:zlib")},91198:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>a.a,__next_app__:()=>p,originalPathname:()=>c,pages:()=>d,routeModule:()=>x,tree:()=>u}),t(13110),t(33709),t(35866);var o=t(23191),s=t(88716),i=t(37922),a=t.n(i),n=t(95231),l={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>n[e]);t.d(r,l);let u=["",{children:["manage-groups",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,13110)),"C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\manage-groups\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,33709)),"C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,35866,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],d=["C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\manage-groups\\page.tsx"],c="/manage-groups/page",p={require:t,loadChunk:()=>Promise.resolve()},x=new o.AppPageRouteModule({definition:{kind:s.x.APP_PAGE,page:"/manage-groups/page",pathname:"/manage-groups",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:u}})},46719:(e,r,t)=>{Promise.resolve().then(t.bind(t,17115))},33198:(e,r,t)=>{"use strict";t.d(r,{Z:()=>y});var o=t(17577),s=t(41135),i=t(88634),a=t(91703),n=t(13643),l=t(2791),u=t(51426),d=t(10326);let c=(0,u.Z)((0,d.jsx)("path",{d:"M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"}),"Person");var p=t(71685),x=t(97898);function g(e){return(0,x.ZP)("MuiAvatar",e)}(0,p.Z)("MuiAvatar",["root","colorDefault","circular","rounded","square","img","fallback"]);var h=t(31121);let f=e=>{let{classes:r,variant:t,colorDefault:o}=e;return(0,i.Z)({root:["root",t,o&&"colorDefault"],img:["img"],fallback:["fallback"]},g,r)},m=(0,a.default)("div",{name:"MuiAvatar",slot:"Root",overridesResolver:(e,r)=>{let{ownerState:t}=e;return[r.root,r[t.variant],t.colorDefault&&r.colorDefault]}})((0,n.Z)(({theme:e})=>({position:"relative",display:"flex",alignItems:"center",justifyContent:"center",flexShrink:0,width:40,height:40,fontFamily:e.typography.fontFamily,fontSize:e.typography.pxToRem(20),lineHeight:1,borderRadius:"50%",overflow:"hidden",userSelect:"none",variants:[{props:{variant:"rounded"},style:{borderRadius:(e.vars||e).shape.borderRadius}},{props:{variant:"square"},style:{borderRadius:0}},{props:{colorDefault:!0},style:{color:(e.vars||e).palette.background.default,...e.vars?{backgroundColor:e.vars.palette.Avatar.defaultBg}:{backgroundColor:e.palette.grey[400],...e.applyStyles("dark",{backgroundColor:e.palette.grey[600]})}}}]}))),b=(0,a.default)("img",{name:"MuiAvatar",slot:"Img",overridesResolver:(e,r)=>r.img})({width:"100%",height:"100%",textAlign:"center",objectFit:"cover",color:"transparent",textIndent:1e4}),v=(0,a.default)(c,{name:"MuiAvatar",slot:"Fallback",overridesResolver:(e,r)=>r.fallback})({width:"75%",height:"75%"}),y=o.forwardRef(function(e,r){let t=(0,l.i)({props:e,name:"MuiAvatar"}),{alt:i,children:a,className:n,component:u="div",slots:c={},slotProps:p={},imgProps:x,sizes:g,src:y,srcSet:q,variant:j="circular",...Z}=t,w=null,k={...t,component:u,variant:j},P=function({crossOrigin:e,referrerPolicy:r,src:t,srcSet:s}){let[i,a]=o.useState(!1);return o.useEffect(()=>{if(!t&&!s)return;a(!1);let o=!0,i=new Image;return i.onload=()=>{o&&a("loaded")},i.onerror=()=>{o&&a("error")},i.crossOrigin=e,i.referrerPolicy=r,i.src=t,s&&(i.srcset=s),()=>{o=!1}},[e,r,t,s]),i}({...x,..."function"==typeof p.img?p.img(k):p.img,src:y,srcSet:q}),_=y||q,C=_&&"error"!==P;k.colorDefault=!C,delete k.ownerState;let R=f(k),[S,A]=(0,h.Z)("img",{className:R.img,elementType:b,externalForwardedProps:{slots:c,slotProps:{img:{...x,...p.img}}},additionalProps:{alt:i,src:y,srcSet:q,sizes:g},ownerState:k});return w=C?(0,d.jsx)(S,{...A}):a||0===a?a:_&&i?i[0]:(0,d.jsx)(v,{ownerState:k,className:R.fallback}),(0,d.jsx)(m,{as:u,className:(0,s.Z)(R.root,n),ref:r,...Z,ownerState:k,children:w})})},17115:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>y});var o=t(10326),s=t(17577),i=t(6283),a=t(25609),n=t(42265),l=t(89178),u=t(16027),d=t(9799),c=t(90541),p=t(12549),x=t(43058),g=t(35047),h=t(25842),f=t(32049),m=t(53148);async function b(e,r){let t=await (0,m.j0)("/group",{companyId:e,name:r});if(201===t.status)return t.data.groupId;console.error("Error Creating Group")}async function v(e){try{return(await (0,m.A_)(`/groups/${e}`)).groups}catch(e){console.error("Error getting Groups");return}}let y=(0,p.Z)(()=>{let e=(0,h.v9)(e=>(0,f.MP)(e)),[r,t]=(0,s.useState)(!1),[p,m]=(0,s.useState)([]),[y,q]=(0,s.useState)(""),j=(0,g.useRouter)();(0,s.useEffect)(()=>{(async()=>{let r=await v(e);console.log("data >>",r),m(r)})()},[]);let Z=()=>{t(!1),q("")},w=async()=>{let r=await b(e,y);j.push(`/group/${r}`),Z()};return(0,o.jsxs)(x.Z,{children:[(0,o.jsxs)(i.Z,{sx:{bgcolor:"#F5F6FA",px:4,py:2,width:"100%",height:"95vh",overflow:"auto"},children:[(0,o.jsxs)(i.Z,{sx:{display:"flex",gap:"32px",alignItems:"center",mb:4,mt:5},children:[o.jsx(a.Z,{sx:{fontWeight:600,fontSize:"34px",color:"black",lineHeight:"41px",textAlign:"left"},children:"Groups"}),o.jsx(n.Z,{variant:"contained",onClick:()=>t(!0),sx:{textTransform:"none",borderRadius:"6px",bgcolor:"white",color:"black",boxShadow:"none",width:"140px",paddingY:"10px",paddingX:"16px",border:"1px solid #D2D2D2","&:hover":{backgroundColor:"rgba(0, 0, 0, 0.1)",boxShadow:"none"}},children:"+ Create Group"})]}),(0,o.jsxs)(l.Z,{sx:{bgcolor:"#ffffff",borderRadius:"12px",marginBottom:9,boxShadow:"none"},children:[(0,o.jsxs)(u.ZP,{container:!0,sx:{borderBottom:"1px solid #E0E0E0",background:"#F0F0F0",borderTopLeftRadius:"12px",borderTopRightRadius:"12px"},children:[o.jsx(u.ZP,{item:!0,xs:2,children:o.jsx(a.Z,{variant:"body2",sx:{fontWeight:600,color:"#B0B0B0",py:1,px:3},children:"S NO"})}),o.jsx(u.ZP,{item:!0,xs:10,children:o.jsx(a.Z,{variant:"body2",sx:{fontWeight:600,color:"#B0B0B0",py:1},children:"GROUP NAME"})})]}),p&&p.length>0?p?.map((e,r)=>o.jsx(i.Z,{sx:{transition:"background-color 0.3s ease","&:hover":{bgcolor:"#f0f0f0",cursor:"pointer"},borderBottom:r===p.length-1?"none":"1px solid #E0E0E0"},onClick:()=>j.push(`/group/${e._id}`),children:o.jsxs(u.ZP,{container:!0,alignItems:"center",sx:{py:1,borderRadius:"8px"},children:[o.jsx(u.ZP,{item:!0,xs:2,sx:{py:1,px:3},children:o.jsx(a.Z,{sx:{fontWeight:"500",fontSize:"17px",color:"black"},children:r+1})}),o.jsx(u.ZP,{item:!0,xs:10,children:o.jsx(a.Z,{sx:{fontWeight:"500",fontSize:"17px",color:"black",textAlign:"left"},children:e.name})})]})},r)):o.jsx(i.Z,{sx:{display:"flex",justifyContent:"center",minWidth:"100%",py:2},children:o.jsx(a.Z,{children:"No Groups Found"})})]})]}),o.jsx(d.Z,{open:r,onClose:Z,"aria-labelledby":"modal-title","aria-describedby":"modal-description",children:(0,o.jsxs)(i.Z,{sx:{position:"absolute",top:"50%",left:"50%",transform:"translate(-50%, -50%)",bgcolor:"#F5F6FA",boxShadow:24,p:4,borderRadius:"12px",width:400},children:[o.jsx(a.Z,{id:"modal-title",variant:"h6",sx:{mb:2,color:"black"},children:"Create New Group"}),o.jsx(c.Z,{fullWidth:!0,label:"Enter Group Name",variant:"outlined",value:y,onChange:e=>q(e.target.value),sx:{mb:3,bgcolor:"white",borderRadius:"8px","& .MuiOutlinedInput-root":{"& fieldset":{borderColor:"rgba(0, 0, 0, 0.2)"},"&:hover fieldset":{borderColor:"rgba(0, 0, 0, 0.4)"}}}}),(0,o.jsxs)(i.Z,{sx:{display:"flex",justifyContent:"flex-end",gap:2},children:[o.jsx(n.Z,{onClick:Z,sx:{textTransform:"none",color:"black",bgcolor:"rgba(0, 0, 0, 0.06)",borderRadius:"8px","&:hover":{bgcolor:"rgba(0, 0, 0, 0.1)"}},children:"Cancel"}),o.jsx(n.Z,{variant:"contained",sx:{textTransform:"none",bgcolor:"rgba(0, 0, 0, 0.06)",color:"black",borderRadius:"8px","&:hover":{bgcolor:"rgba(0, 0, 0, 0.1)"}},onClick:w,children:"Save"})]})]})})]})})},13110:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>o});let o=(0,t(68570).createProxy)(String.raw`C:\beno\project_dev\qharmony-frontend-v1\src\app\manage-groups\page.tsx#default`)}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),o=r.X(0,[8948,1183,6621,9066,1999,3253,928,8097,8522,3141,6027,541,576,6305,401,2549],()=>t(91198));module.exports=o})();