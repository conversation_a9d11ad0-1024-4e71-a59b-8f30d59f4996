(()=>{var e={};e.id=6666,e.ids=[6666],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},27790:e=>{"use strict";e.exports=require("assert")},84770:e=>{"use strict";e.exports=require("crypto")},17702:e=>{"use strict";e.exports=require("events")},92048:e=>{"use strict";e.exports=require("fs")},32615:e=>{"use strict";e.exports=require("http")},35240:e=>{"use strict";e.exports=require("https")},19801:e=>{"use strict";e.exports=require("os")},55315:e=>{"use strict";e.exports=require("path")},76162:e=>{"use strict";e.exports=require("stream")},74026:e=>{"use strict";e.exports=require("string_decoder")},74175:e=>{"use strict";e.exports=require("tty")},17360:e=>{"use strict";e.exports=require("url")},21764:e=>{"use strict";e.exports=require("util")},71568:e=>{"use strict";e.exports=require("zlib")},98061:e=>{"use strict";e.exports=require("node:assert")},92761:e=>{"use strict";e.exports=require("node:async_hooks")},72254:e=>{"use strict";e.exports=require("node:buffer")},40027:e=>{"use strict";e.exports=require("node:console")},6005:e=>{"use strict";e.exports=require("node:crypto")},65714:e=>{"use strict";e.exports=require("node:diagnostics_channel")},15673:e=>{"use strict";e.exports=require("node:events")},88849:e=>{"use strict";e.exports=require("node:http")},42725:e=>{"use strict";e.exports=require("node:http2")},87503:e=>{"use strict";e.exports=require("node:net")},38846:e=>{"use strict";e.exports=require("node:perf_hooks")},39630:e=>{"use strict";e.exports=require("node:querystring")},84492:e=>{"use strict";e.exports=require("node:stream")},31764:e=>{"use strict";e.exports=require("node:tls")},41041:e=>{"use strict";e.exports=require("node:url")},47261:e=>{"use strict";e.exports=require("node:util")},93746:e=>{"use strict";e.exports=require("node:util/types")},24086:e=>{"use strict";e.exports=require("node:worker_threads")},65628:e=>{"use strict";e.exports=require("node:zlib")},42789:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>u,originalPathname:()=>p,pages:()=>c,routeModule:()=>h,tree:()=>d}),r(86764),r(33709),r(35866);var o=r(23191),i=r(88716),a=r(37922),n=r.n(a),s=r(95231),l={};for(let e in s)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>s[e]);r.d(t,l);let d=["",{children:["manageBenefits",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,86764)),"C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\manageBenefits\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,33709)),"C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,35866,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],c=["C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\manageBenefits\\page.tsx"],p="/manageBenefits/page",u={require:r,loadChunk:()=>Promise.resolve()},h=new o.AppPageRouteModule({definition:{kind:i.x.APP_PAGE,page:"/manageBenefits/page",pathname:"/manageBenefits",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},93758:(e,t,r)=>{Promise.resolve().then(r.bind(r,25846))},33198:(e,t,r)=>{"use strict";r.d(t,{Z:()=>y});var o=r(17577),i=r(41135),a=r(88634),n=r(91703),s=r(13643),l=r(2791),d=r(51426),c=r(10326);let p=(0,d.Z)((0,c.jsx)("path",{d:"M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"}),"Person");var u=r(71685),h=r(97898);function f(e){return(0,h.ZP)("MuiAvatar",e)}(0,u.Z)("MuiAvatar",["root","colorDefault","circular","rounded","square","img","fallback"]);var m=r(31121);let g=e=>{let{classes:t,variant:r,colorDefault:o}=e;return(0,a.Z)({root:["root",r,o&&"colorDefault"],img:["img"],fallback:["fallback"]},f,t)},b=(0,n.default)("div",{name:"MuiAvatar",slot:"Root",overridesResolver:(e,t)=>{let{ownerState:r}=e;return[t.root,t[r.variant],r.colorDefault&&t.colorDefault]}})((0,s.Z)(({theme:e})=>({position:"relative",display:"flex",alignItems:"center",justifyContent:"center",flexShrink:0,width:40,height:40,fontFamily:e.typography.fontFamily,fontSize:e.typography.pxToRem(20),lineHeight:1,borderRadius:"50%",overflow:"hidden",userSelect:"none",variants:[{props:{variant:"rounded"},style:{borderRadius:(e.vars||e).shape.borderRadius}},{props:{variant:"square"},style:{borderRadius:0}},{props:{colorDefault:!0},style:{color:(e.vars||e).palette.background.default,...e.vars?{backgroundColor:e.vars.palette.Avatar.defaultBg}:{backgroundColor:e.palette.grey[400],...e.applyStyles("dark",{backgroundColor:e.palette.grey[600]})}}}]}))),v=(0,n.default)("img",{name:"MuiAvatar",slot:"Img",overridesResolver:(e,t)=>t.img})({width:"100%",height:"100%",textAlign:"center",objectFit:"cover",color:"transparent",textIndent:1e4}),x=(0,n.default)(p,{name:"MuiAvatar",slot:"Fallback",overridesResolver:(e,t)=>t.fallback})({width:"75%",height:"75%"}),y=o.forwardRef(function(e,t){let r=(0,l.i)({props:e,name:"MuiAvatar"}),{alt:a,children:n,className:s,component:d="div",slots:p={},slotProps:u={},imgProps:h,sizes:f,src:y,srcSet:w,variant:k="circular",...Z}=r,S=null,j={...r,component:d,variant:k},P=function({crossOrigin:e,referrerPolicy:t,src:r,srcSet:i}){let[a,n]=o.useState(!1);return o.useEffect(()=>{if(!r&&!i)return;n(!1);let o=!0,a=new Image;return a.onload=()=>{o&&n("loaded")},a.onerror=()=>{o&&n("error")},a.crossOrigin=e,a.referrerPolicy=t,a.src=r,i&&(a.srcset=i),()=>{o=!1}},[e,t,r,i]),a}({...h,..."function"==typeof u.img?u.img(j):u.img,src:y,srcSet:w}),C=y||w,R=C&&"error"!==P;j.colorDefault=!R,delete j.ownerState;let M=g(j),[q,$]=(0,m.Z)("img",{className:M.img,elementType:v,externalForwardedProps:{slots:p,slotProps:{img:{...h,...u.img}}},additionalProps:{alt:a,src:y,srcSet:w,sizes:f},ownerState:j});return S=R?(0,c.jsx)(q,{...$}):n||0===n?n:C&&a?a[0]:(0,c.jsx)(x,{ownerState:j,className:M.fallback}),(0,c.jsx)(b,{as:d,className:(0,i.Z)(M.root,s),ref:t,...Z,ownerState:j,children:S})})},99207:(e,t,r)=>{"use strict";r.d(t,{Z:()=>g});var o=r(17577),i=r(41135),a=r(88634),n=r(44823),s=r(91703),l=r(13643),d=r(2791),c=r(73025),p=r(10326);let u=e=>{let{absolute:t,children:r,classes:o,flexItem:i,light:n,orientation:s,textAlign:l,variant:d}=e;return(0,a.Z)({root:["root",t&&"absolute",d,n&&"light","vertical"===s&&"vertical",i&&"flexItem",r&&"withChildren",r&&"vertical"===s&&"withChildrenVertical","right"===l&&"vertical"!==s&&"textAlignRight","left"===l&&"vertical"!==s&&"textAlignLeft"],wrapper:["wrapper","vertical"===s&&"wrapperVertical"]},c.V,o)},h=(0,s.default)("div",{name:"MuiDivider",slot:"Root",overridesResolver:(e,t)=>{let{ownerState:r}=e;return[t.root,r.absolute&&t.absolute,t[r.variant],r.light&&t.light,"vertical"===r.orientation&&t.vertical,r.flexItem&&t.flexItem,r.children&&t.withChildren,r.children&&"vertical"===r.orientation&&t.withChildrenVertical,"right"===r.textAlign&&"vertical"!==r.orientation&&t.textAlignRight,"left"===r.textAlign&&"vertical"!==r.orientation&&t.textAlignLeft]}})((0,l.Z)(({theme:e})=>({margin:0,flexShrink:0,borderWidth:0,borderStyle:"solid",borderColor:(e.vars||e).palette.divider,borderBottomWidth:"thin",variants:[{props:{absolute:!0},style:{position:"absolute",bottom:0,left:0,width:"100%"}},{props:{light:!0},style:{borderColor:e.vars?`rgba(${e.vars.palette.dividerChannel} / 0.08)`:(0,n.Fq)(e.palette.divider,.08)}},{props:{variant:"inset"},style:{marginLeft:72}},{props:{variant:"middle",orientation:"horizontal"},style:{marginLeft:e.spacing(2),marginRight:e.spacing(2)}},{props:{variant:"middle",orientation:"vertical"},style:{marginTop:e.spacing(1),marginBottom:e.spacing(1)}},{props:{orientation:"vertical"},style:{height:"100%",borderBottomWidth:0,borderRightWidth:"thin"}},{props:{flexItem:!0},style:{alignSelf:"stretch",height:"auto"}},{props:({ownerState:e})=>!!e.children,style:{display:"flex",textAlign:"center",border:0,borderTopStyle:"solid",borderLeftStyle:"solid","&::before, &::after":{content:'""',alignSelf:"center"}}},{props:({ownerState:e})=>e.children&&"vertical"!==e.orientation,style:{"&::before, &::after":{width:"100%",borderTop:`thin solid ${(e.vars||e).palette.divider}`,borderTopStyle:"inherit"}}},{props:({ownerState:e})=>"vertical"===e.orientation&&e.children,style:{flexDirection:"column","&::before, &::after":{height:"100%",borderLeft:`thin solid ${(e.vars||e).palette.divider}`,borderLeftStyle:"inherit"}}},{props:({ownerState:e})=>"right"===e.textAlign&&"vertical"!==e.orientation,style:{"&::before":{width:"90%"},"&::after":{width:"10%"}}},{props:({ownerState:e})=>"left"===e.textAlign&&"vertical"!==e.orientation,style:{"&::before":{width:"10%"},"&::after":{width:"90%"}}}]}))),f=(0,s.default)("span",{name:"MuiDivider",slot:"Wrapper",overridesResolver:(e,t)=>{let{ownerState:r}=e;return[t.wrapper,"vertical"===r.orientation&&t.wrapperVertical]}})((0,l.Z)(({theme:e})=>({display:"inline-block",paddingLeft:`calc(${e.spacing(1)} * 1.2)`,paddingRight:`calc(${e.spacing(1)} * 1.2)`,whiteSpace:"nowrap",variants:[{props:{orientation:"vertical"},style:{paddingTop:`calc(${e.spacing(1)} * 1.2)`,paddingBottom:`calc(${e.spacing(1)} * 1.2)`}}]}))),m=o.forwardRef(function(e,t){let r=(0,d.i)({props:e,name:"MuiDivider"}),{absolute:o=!1,children:a,className:n,orientation:s="horizontal",component:l=a||"vertical"===s?"div":"hr",flexItem:c=!1,light:m=!1,role:g="hr"!==l?"separator":void 0,textAlign:b="center",variant:v="fullWidth",...x}=r,y={...r,absolute:o,component:l,flexItem:c,light:m,orientation:s,role:g,textAlign:b,variant:v},w=u(y);return(0,p.jsx)(h,{as:l,className:(0,i.Z)(w.root,n),role:g,ref:t,ownerState:y,"aria-orientation":"separator"===g&&("hr"!==l||"vertical"===s)?s:void 0,...x,children:a?(0,p.jsx)(f,{className:w.wrapper,ownerState:y,children:a}):null})});m&&(m.muiSkipListHighlight=!0);let g=m},73025:(e,t,r)=>{"use strict";r.d(t,{V:()=>a,Z:()=>n});var o=r(71685),i=r(97898);function a(e){return(0,i.ZP)("MuiDivider",e)}let n=(0,o.Z)("MuiDivider",["root","absolute","fullWidth","inset","middle","flexItem","light","vertical","withChildren","withChildrenVertical","textAlignRight","textAlignLeft","wrapper","wrapperVertical"])},5394:(e,t,r)=>{"use strict";r.d(t,{Z:()=>k});var o=r(17577),i=r(41135),a=r(88634),n=r(65656),s=r(91703),l=r(13643),d=r(2791),c=r(25609),p=r(54641),u=r(71685),h=r(97898);function f(e){return(0,h.ZP)("MuiFormControlLabel",e)}let m=(0,u.Z)("MuiFormControlLabel",["root","labelPlacementStart","labelPlacementTop","labelPlacementBottom","disabled","label","error","required","asterisk"]);var g=r(39914),b=r(31121),v=r(10326);let x=e=>{let{classes:t,disabled:r,labelPlacement:o,error:i,required:n}=e,s={root:["root",r&&"disabled",`labelPlacement${(0,p.Z)(o)}`,i&&"error",n&&"required"],label:["label",r&&"disabled"],asterisk:["asterisk",i&&"error"]};return(0,a.Z)(s,f,t)},y=(0,s.default)("label",{name:"MuiFormControlLabel",slot:"Root",overridesResolver:(e,t)=>{let{ownerState:r}=e;return[{[`& .${m.label}`]:t.label},t.root,t[`labelPlacement${(0,p.Z)(r.labelPlacement)}`]]}})((0,l.Z)(({theme:e})=>({display:"inline-flex",alignItems:"center",cursor:"pointer",verticalAlign:"middle",WebkitTapHighlightColor:"transparent",marginLeft:-11,marginRight:16,[`&.${m.disabled}`]:{cursor:"default"},[`& .${m.label}`]:{[`&.${m.disabled}`]:{color:(e.vars||e).palette.text.disabled}},variants:[{props:{labelPlacement:"start"},style:{flexDirection:"row-reverse",marginRight:-11}},{props:{labelPlacement:"top"},style:{flexDirection:"column-reverse"}},{props:{labelPlacement:"bottom"},style:{flexDirection:"column"}},{props:({labelPlacement:e})=>"start"===e||"top"===e||"bottom"===e,style:{marginLeft:16}}]}))),w=(0,s.default)("span",{name:"MuiFormControlLabel",slot:"Asterisk",overridesResolver:(e,t)=>t.asterisk})((0,l.Z)(({theme:e})=>({[`&.${m.error}`]:{color:(e.vars||e).palette.error.main}}))),k=o.forwardRef(function(e,t){let r=(0,d.i)({props:e,name:"MuiFormControlLabel"}),{checked:a,className:s,componentsProps:l={},control:p,disabled:u,disableTypography:h,inputRef:f,label:m,labelPlacement:k="end",name:Z,onChange:S,required:j,slots:P={},slotProps:C={},value:R,...M}=r,q=(0,n.Z)(),$=u??p.props.disabled??q?.disabled,E=j??p.props.required,B={disabled:$,required:E};["checked","name","onChange","value","inputRef"].forEach(e=>{void 0===p.props[e]&&void 0!==r[e]&&(B[e]=r[e])});let A=(0,g.Z)({props:r,muiFormControl:q,states:["error"]}),F={...r,disabled:$,labelPlacement:k,required:E,error:A.error},I=x(F),_={slots:P,slotProps:{...l,...C}},[T,z]=(0,b.Z)("typography",{elementType:c.Z,externalForwardedProps:_,ownerState:F}),D=m;return null==D||D.type===c.Z||h||(D=(0,v.jsx)(T,{component:"span",...z,className:(0,i.Z)(I.label,z?.className),children:D})),(0,v.jsxs)(y,{className:(0,i.Z)(I.root,s),ownerState:F,ref:t,...M,children:[o.cloneElement(p,B),E?(0,v.jsxs)("div",{children:[D,(0,v.jsxs)(w,{ownerState:F,"aria-hidden":!0,className:I.asterisk,children:[" ","*"]})]}):D]})})},45011:(e,t,r)=>{"use strict";r.d(t,{Z:()=>o});let o=r(17577).createContext(void 0)},39914:(e,t,r)=>{"use strict";function o({props:e,states:t,muiFormControl:r}){return t.reduce((t,o)=>(t[o]=e[o],r&&void 0===e[o]&&(t[o]=r[o]),t),{})}r.d(t,{Z:()=>o})},65656:(e,t,r)=>{"use strict";r.d(t,{Z:()=>a});var o=r(17577),i=r(45011);function a(){return o.useContext(i.Z)}},33662:(e,t,r)=>{"use strict";r.d(t,{Z:()=>x});var o=r(17577),i=r(88634),a=r(54641),n=r(27080),s=r(91703),l=r(43227),d=r(65656),c=r(49006),p=r(71685),u=r(97898);function h(e){return(0,u.ZP)("PrivateSwitchBase",e)}(0,p.Z)("PrivateSwitchBase",["root","checked","disabled","input","edgeStart","edgeEnd"]);var f=r(31121),m=r(10326);let g=e=>{let{classes:t,checked:r,disabled:o,edge:n}=e,s={root:["root",r&&"checked",o&&"disabled",n&&`edge${(0,a.Z)(n)}`],input:["input"]};return(0,i.Z)(s,h,t)},b=(0,s.default)(c.Z)({padding:9,borderRadius:"50%",variants:[{props:{edge:"start",size:"small"},style:{marginLeft:-3}},{props:({edge:e,ownerState:t})=>"start"===e&&"small"!==t.size,style:{marginLeft:-12}},{props:{edge:"end",size:"small"},style:{marginRight:-3}},{props:({edge:e,ownerState:t})=>"end"===e&&"small"!==t.size,style:{marginRight:-12}}]}),v=(0,s.default)("input",{shouldForwardProp:n.Z})({cursor:"inherit",position:"absolute",opacity:0,width:"100%",height:"100%",top:0,left:0,margin:0,padding:0,zIndex:1}),x=o.forwardRef(function(e,t){let{autoFocus:r,checked:o,checkedIcon:i,defaultChecked:a,disabled:n,disableFocusRipple:s=!1,edge:c=!1,icon:p,id:u,inputProps:h,inputRef:x,name:y,onBlur:w,onChange:k,onFocus:Z,readOnly:S,required:j=!1,tabIndex:P,type:C,value:R,slots:M={},slotProps:q={},...$}=e,[E,B]=(0,l.Z)({controlled:o,default:!!a,name:"SwitchBase",state:"checked"}),A=(0,d.Z)(),F=e=>{Z&&Z(e),A&&A.onFocus&&A.onFocus(e)},I=e=>{w&&w(e),A&&A.onBlur&&A.onBlur(e)},_=e=>{if(e.nativeEvent.defaultPrevented)return;let t=e.target.checked;B(t),k&&k(e,t)},T=n;A&&void 0===T&&(T=A.disabled);let z="checkbox"===C||"radio"===C,D={...e,checked:E,disabled:T,disableFocusRipple:s,edge:c},L=g(D),N={slots:M,slotProps:{input:h,...q}},[W,O]=(0,f.Z)("root",{ref:t,elementType:b,className:L.root,shouldForwardComponentProp:!0,externalForwardedProps:{...N,component:"span",...$},getSlotProps:e=>({...e,onFocus:t=>{e.onFocus?.(t),F(t)},onBlur:t=>{e.onBlur?.(t),I(t)}}),ownerState:D,additionalProps:{centerRipple:!0,focusRipple:!s,disabled:T,role:void 0,tabIndex:null}}),[G,H]=(0,f.Z)("input",{ref:x,elementType:v,className:L.input,externalForwardedProps:N,getSlotProps:e=>({onChange:t=>{e.onChange?.(t),_(t)}}),ownerState:D,additionalProps:{autoFocus:r,checked:o,defaultChecked:a,disabled:T,id:z?u:void 0,name:y,readOnly:S,required:j,tabIndex:P,type:C,..."checkbox"===C&&void 0===R?{}:{value:R}}});return(0,m.jsxs)(W,{...O,children:[(0,m.jsx)(G,{...H}),E?i:p]})})},43227:(e,t,r)=>{"use strict";r.d(t,{Z:()=>i});var o=r(17577);let i=function({controlled:e,default:t,name:r,state:i="value"}){let{current:a}=o.useRef(void 0!==e),[n,s]=o.useState(t),l=o.useCallback(e=>{a||s(e)},[]);return[a?e:n,l]}},25846:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>L});var o=r(10326),i=r(17577),a=r(31870),n=r(94638),s=r(85560),l=r(6283),d=r(25609),c=r(16027),p=r(42265),u=r(5394),h=r(41135),f=r(88634),m=r(44823),g=r(54641),b=r(40955),v=r(33662),x=r(91703),y=r(13643),w=r(2791),k=r(71685),Z=r(97898);function S(e){return(0,Z.ZP)("MuiSwitch",e)}let j=(0,k.Z)("MuiSwitch",["root","edgeStart","edgeEnd","switchBase","colorPrimary","colorSecondary","sizeSmall","sizeMedium","checked","disabled","input","thumb","track"]);var P=r(31121);let C=e=>{let{classes:t,edge:r,size:o,color:i,checked:a,disabled:n}=e,s={root:["root",r&&`edge${(0,g.Z)(r)}`,`size${(0,g.Z)(o)}`],switchBase:["switchBase",`color${(0,g.Z)(i)}`,a&&"checked",n&&"disabled"],thumb:["thumb"],track:["track"],input:["input"]},l=(0,f.Z)(s,S,t);return{...t,...l}},R=(0,x.default)("span",{name:"MuiSwitch",slot:"Root",overridesResolver:(e,t)=>{let{ownerState:r}=e;return[t.root,r.edge&&t[`edge${(0,g.Z)(r.edge)}`],t[`size${(0,g.Z)(r.size)}`]]}})({display:"inline-flex",width:58,height:38,overflow:"hidden",padding:12,boxSizing:"border-box",position:"relative",flexShrink:0,zIndex:0,verticalAlign:"middle","@media print":{colorAdjust:"exact"},variants:[{props:{edge:"start"},style:{marginLeft:-8}},{props:{edge:"end"},style:{marginRight:-8}},{props:{size:"small"},style:{width:40,height:24,padding:7,[`& .${j.thumb}`]:{width:16,height:16},[`& .${j.switchBase}`]:{padding:4,[`&.${j.checked}`]:{transform:"translateX(16px)"}}}}]}),M=(0,x.default)(v.Z,{name:"MuiSwitch",slot:"SwitchBase",overridesResolver:(e,t)=>{let{ownerState:r}=e;return[t.switchBase,{[`& .${j.input}`]:t.input},"default"!==r.color&&t[`color${(0,g.Z)(r.color)}`]]}})((0,y.Z)(({theme:e})=>({position:"absolute",top:0,left:0,zIndex:1,color:e.vars?e.vars.palette.Switch.defaultColor:`${"light"===e.palette.mode?e.palette.common.white:e.palette.grey[300]}`,transition:e.transitions.create(["left","transform"],{duration:e.transitions.duration.shortest}),[`&.${j.checked}`]:{transform:"translateX(20px)"},[`&.${j.disabled}`]:{color:e.vars?e.vars.palette.Switch.defaultDisabledColor:`${"light"===e.palette.mode?e.palette.grey[100]:e.palette.grey[600]}`},[`&.${j.checked} + .${j.track}`]:{opacity:.5},[`&.${j.disabled} + .${j.track}`]:{opacity:e.vars?e.vars.opacity.switchTrackDisabled:`${"light"===e.palette.mode?.12:.2}`},[`& .${j.input}`]:{left:"-100%",width:"300%"}})),(0,y.Z)(({theme:e})=>({"&:hover":{backgroundColor:e.vars?`rgba(${e.vars.palette.action.activeChannel} / ${e.vars.palette.action.hoverOpacity})`:(0,m.Fq)(e.palette.action.active,e.palette.action.hoverOpacity),"@media (hover: none)":{backgroundColor:"transparent"}},variants:[...Object.entries(e.palette).filter((0,b.Z)(["light"])).map(([t])=>({props:{color:t},style:{[`&.${j.checked}`]:{color:(e.vars||e).palette[t].main,"&:hover":{backgroundColor:e.vars?`rgba(${e.vars.palette[t].mainChannel} / ${e.vars.palette.action.hoverOpacity})`:(0,m.Fq)(e.palette[t].main,e.palette.action.hoverOpacity),"@media (hover: none)":{backgroundColor:"transparent"}},[`&.${j.disabled}`]:{color:e.vars?e.vars.palette.Switch[`${t}DisabledColor`]:`${"light"===e.palette.mode?(0,m.$n)(e.palette[t].main,.62):(0,m._j)(e.palette[t].main,.55)}`}},[`&.${j.checked} + .${j.track}`]:{backgroundColor:(e.vars||e).palette[t].main}}}))]}))),q=(0,x.default)("span",{name:"MuiSwitch",slot:"Track",overridesResolver:(e,t)=>t.track})((0,y.Z)(({theme:e})=>({height:"100%",width:"100%",borderRadius:7,zIndex:-1,transition:e.transitions.create(["opacity","background-color"],{duration:e.transitions.duration.shortest}),backgroundColor:e.vars?e.vars.palette.common.onBackground:`${"light"===e.palette.mode?e.palette.common.black:e.palette.common.white}`,opacity:e.vars?e.vars.opacity.switchTrack:`${"light"===e.palette.mode?.38:.3}`}))),$=(0,x.default)("span",{name:"MuiSwitch",slot:"Thumb",overridesResolver:(e,t)=>t.thumb})((0,y.Z)(({theme:e})=>({boxShadow:(e.vars||e).shadows[1],backgroundColor:"currentColor",width:20,height:20,borderRadius:"50%"}))),E=i.forwardRef(function(e,t){let r=(0,w.i)({props:e,name:"MuiSwitch"}),{className:i,color:a="primary",edge:n=!1,size:s="medium",sx:l,slots:d={},slotProps:c={},...p}=r,u={...r,color:a,edge:n,size:s},f=C(u),m={slots:d,slotProps:c},[g,b]=(0,P.Z)("root",{className:(0,h.Z)(f.root,i),elementType:R,externalForwardedProps:m,ownerState:u,additionalProps:{sx:l}}),[v,x]=(0,P.Z)("thumb",{className:f.thumb,elementType:$,externalForwardedProps:m,ownerState:u}),y=(0,o.jsx)(v,{...x}),[k,Z]=(0,P.Z)("track",{className:f.track,elementType:q,externalForwardedProps:m,ownerState:u});return(0,o.jsxs)(g,{...b,children:[(0,o.jsx)(M,{type:"checkbox",icon:y,checkedIcon:y,ref:t,ownerState:u,...p,classes:{...f,root:f.switchBase},slots:{...d.switchBase&&{root:d.switchBase},...d.input&&{input:d.input}},slotProps:{...c.switchBase&&{root:"function"==typeof c.switchBase?c.switchBase(u):c.switchBase},...c.input&&{input:"function"==typeof c.input?c.input(u):c.input}}}),(0,o.jsx)(k,{...Z})]})});var B=r(98139),A=r(99207),F=r(35047),I=r(69058),_=r(25842),T=r(32049),z=r(12549),D=r(43058);let L=(0,z.Z)(()=>{let e=(0,a.T)(),t=(0,F.useRouter)(),r=(0,_.v9)(e=>(0,T.MP)(e)),h=(0,a.C)(e=>e.benefits.benefitsPerType),[f,m]=(0,i.useState)({});(0,i.useEffect)(()=>{r&&""!==r&&(0,n.aE)(e,r)},[e,r]);let g=async(t,o,i)=>{m(e=>({...e,[t]:!0}));try{await (0,I.ov)(e,r,t,!i,o)?(0,n.aE)(e,r):console.error("Failed to update benefit activation")}catch(e){console.error("Failed to update benefit activation:",e)}finally{m(e=>({...e,[t]:!1}))}},b=e=>{t.push(`/editBenefit/${e}`)},v=e=>e?o.jsx(s.Z,{label:"Available",sx:{bgcolor:"#67BA6B1F",color:"#67BA6B",borderRadius:"8px","& .MuiChip-label":{padding:1,fontWeight:"semibold",fontSize:"14px"}}}):o.jsx(s.Z,{label:"Disabled",sx:{bgcolor:"#f0f0f0",color:"black",borderRadius:"8px","& .MuiChip-label":{padding:1,fontWeight:"semibold",fontSize:"14px"}}});return o.jsx(D.Z,{children:o.jsx(l.Z,{sx:{bgcolor:"#F5F6FA",p:4,width:"100%",height:"95vh",overflow:"auto"},children:h.map(e=>(0,o.jsxs)(l.Z,{sx:{mb:4},children:[o.jsx(d.Z,{sx:{fontWeight:"500",fontSize:"28px",lineHeight:"20.8px",color:"black",textAlign:"left",marginBottom:4,marginTop:5},children:(0,n.Ur)(e.benefitType)}),(0,o.jsxs)(c.ZP,{container:!0,sx:{mb:2,px:2},children:[o.jsx(c.ZP,{item:!0,xs:4,children:o.jsx(d.Z,{variant:"body2",sx:{fontWeight:500,color:"#939496"},children:"TYPE"})}),o.jsx(c.ZP,{item:!0,xs:4,children:o.jsx(d.Z,{variant:"body2",sx:{fontWeight:500,color:"#939496"},children:"STATUS"})}),o.jsx(c.ZP,{item:!0,xs:4,sx:{display:"flex",justifyContent:"flex-end",pr:14},children:o.jsx(d.Z,{variant:"body2",sx:{fontWeight:500,color:"#939496"},children:"ACTION"})})]}),o.jsx(l.Z,{sx:{bgcolor:"#ffffff",py:1,px:3,borderRadius:"12px",marginBottom:9},children:e.benefits.map((t,r)=>(0,o.jsxs)("div",{children:[(0,o.jsxs)(c.ZP,{container:!0,alignItems:"center",sx:{py:1},children:[o.jsx(c.ZP,{item:!0,xs:4,children:o.jsx(d.Z,{sx:{fontWeight:"500",fontSize:"17px",lineHeight:"20.8px",color:"black",textAlign:"left"},children:(0,n.dA)(t.subType)})}),o.jsx(c.ZP,{item:!0,xs:4,children:v(t.isActivated)}),(0,o.jsxs)(c.ZP,{item:!0,xs:4,sx:{display:"flex",justifyContent:"flex-end",alignItems:"center",gap:2},children:[o.jsx(p.Z,{variant:"contained",onClick:()=>b(t._id),sx:{backgroundColor:"#f0f0f0",borderRadius:"8px",textTransform:"none",color:"#000",padding:"4px",marginRight:3,boxShadow:"none",border:"none","&:hover":{backgroundColor:"#E0E0E0",boxShadow:"none"}},children:"Edit"}),o.jsx(u.Z,{control:o.jsx(E,{checked:t.isActivated,onChange:()=>g(t._id,e.benefitType,t.isActivated),disabled:f[t._id],sx:{"& .MuiSwitch-switchBase.Mui-checked":{color:"#11C12D"},"& .MuiSwitch-switchBase.Mui-checked + .MuiSwitch-track":{backgroundColor:"#11C12D"}}}),label:f[t._id]?o.jsx(B.Z,{size:20}):""})]})]},t._id),r<e.benefits.length-1&&o.jsx(A.Z,{sx:{bgcolor:"black",my:1,opacity:.05}})]},t._id))})]},e.benefitType))})})})},69058:(e,t,r)=>{"use strict";r.d(t,{$t:()=>c,SS:()=>u,Y0:()=>n,cd:()=>p,fH:()=>h,mH:()=>f,ov:()=>d,v0:()=>s});var o=r(53148),i=r(25748),a=r(94638);async function n(e,t,r){try{let a=await (0,o.A_)("/benefits/benefit-by-type",{companyId:t,type:r});a&&a.benefits?(console.log("GET BENEFITS FOR TYPE RESPONSE: ",a.benefits),e((0,i.oQ)({benefitType:r,benefits:a.benefits})),e((0,i.nM)("Benefits fetched successfully"))):(console.error("Invalid response format:",a),e((0,i.nM)("Failed to fetch benefits")))}catch(t){console.error("Error fetching benefits:",t),e((0,i.nM)("Error fetching benefits"))}}async function s(e,t,r,a){let n={benefitId:t,page:a};console.log("data",n);let s=await (0,o.A_)("/benefits/one-benefit",n),d={...s,benefitId:t};for(let t of(e((0,i.F5)(d)),s.documents)){let o=decodeURIComponent(t.split("_____")[1]);l(e,t,r,o)}}async function l(e,t,r,a){let n={objectKey:t,companyId:r};console.log("data",n);let s=await (0,o.$R)("/benefits/document",n);if(console.log("VIEW BENEFIT RESPONSE: ",s),s){let r=new Blob([s],{type:"application/pdf"}),o=URL.createObjectURL(r);e((0,i.D7)([{documentObjectKey:t,document:o,originalFileName:a}]))}}let d=async(e,t,r,i,s)=>200===(await (0,o.j0)("/benefits/toggle-benefits/",{benefitId:r,companyId:t,isActivated:i})).status&&(await n(e,t,s),await (0,a.N)(e,t),!0);async function c(e,t,r,a){let n=new FormData;a.forEach(e=>n.append("documents",e)),n.append("companyId",r),n.append("benefitId",t);try{console.log("uploadDocument",n);let s=await (0,o.iG)("/benefits/add/document",n),d=s.data.objectKeys;if(console.log("newObjectKeys",d),200===s.status)return d.forEach((o,n)=>{let s=a[n].name;e((0,i.H_)({benefitId:t,document:o})),l(e,o,r,s)}),e((0,i.nM)("Document added successfully")),!0;return console.error("Error adding document:",s.data.error),e((0,i.nM)("Failed to add document")),!1}catch(t){return console.error("Error adding document:",t),e((0,i.nM)("Error adding document")),!1}}async function p(e,t,r,a){try{let n=await (0,o.j0)("/benefits/delete/document",{benefitId:t,companyId:r,objectKey:a});if(200===n.status)return e((0,i.iH)({benefitId:t,document:a})),e((0,i.nM)("Document deleted successfully")),!0;return console.error("Error deleting document:",n.data.error),e((0,i.nM)("Failed to delete document")),!1}catch(t){return console.error("Error deleting document:",t),e((0,i.nM)("Error deleting document")),!1}}async function u(e,t,r,a){try{let n=await (0,o.j0)("/benefits/add/links",{benefitId:t,companyId:r,urls:[a]});if(200===n.status)return e((0,i.MJ)({benefitId:t,link:a})),e((0,i.nM)("Link added successfully")),!0;return console.error("Error adding link:",n.data.error),e((0,i.nM)("Failed to add link")),!1}catch(t){return console.error("Error adding link:",t),e((0,i.nM)("Error adding link")),!1}}async function h(e,t,r,a){try{let n=await (0,o.j0)("/benefits/delete/link",{benefitId:t,companyId:r,urls:a});if(200===n.status)return e((0,i.Yw)({benefitId:t,link:a})),e((0,i.nM)("Link deleted successfully")),!0;return console.error("Error deleting link:",n.data.error),e((0,i.nM)("Failed to delete link")),!1}catch(t){return console.error("Error deleting link:",t),e((0,i.nM)("Error deleting link")),!1}}async function f(e,t){let r=new FormData;r.append("logoImage",t);try{console.log("uploading company logo",r);let t=await (0,o.iG)("/admin/update-company-logo",r);if(await (0,a.aK)(e),200===t.status)return console.log("Company logo updated successfully"),e((0,i.nM)("Company logo updated successfully")),!0;return console.error("Error updating company logo:",t.data.error),e((0,i.nM)("Failed to update company logo")),!1}catch(t){return console.error("Error updating company logo:",t),e((0,i.nM)("Error updating company logo")),!1}}},86764:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>o});let o=(0,r(68570).createProxy)(String.raw`C:\beno\project_dev\qharmony-frontend-v1\src\app\manageBenefits\page.tsx#default`)}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),o=t.X(0,[8948,1183,6621,9066,1999,3253,928,8097,8522,3141,6027,5560,576,6305,401,2549],()=>r(42789));module.exports=o})();