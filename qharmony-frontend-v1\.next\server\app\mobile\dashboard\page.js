(()=>{var e={};e.id=4668,e.ids=[4668],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},27790:e=>{"use strict";e.exports=require("assert")},84770:e=>{"use strict";e.exports=require("crypto")},17702:e=>{"use strict";e.exports=require("events")},92048:e=>{"use strict";e.exports=require("fs")},32615:e=>{"use strict";e.exports=require("http")},35240:e=>{"use strict";e.exports=require("https")},19801:e=>{"use strict";e.exports=require("os")},55315:e=>{"use strict";e.exports=require("path")},76162:e=>{"use strict";e.exports=require("stream")},74026:e=>{"use strict";e.exports=require("string_decoder")},74175:e=>{"use strict";e.exports=require("tty")},17360:e=>{"use strict";e.exports=require("url")},21764:e=>{"use strict";e.exports=require("util")},71568:e=>{"use strict";e.exports=require("zlib")},98061:e=>{"use strict";e.exports=require("node:assert")},92761:e=>{"use strict";e.exports=require("node:async_hooks")},72254:e=>{"use strict";e.exports=require("node:buffer")},40027:e=>{"use strict";e.exports=require("node:console")},6005:e=>{"use strict";e.exports=require("node:crypto")},65714:e=>{"use strict";e.exports=require("node:diagnostics_channel")},15673:e=>{"use strict";e.exports=require("node:events")},88849:e=>{"use strict";e.exports=require("node:http")},42725:e=>{"use strict";e.exports=require("node:http2")},87503:e=>{"use strict";e.exports=require("node:net")},38846:e=>{"use strict";e.exports=require("node:perf_hooks")},39630:e=>{"use strict";e.exports=require("node:querystring")},84492:e=>{"use strict";e.exports=require("node:stream")},31764:e=>{"use strict";e.exports=require("node:tls")},41041:e=>{"use strict";e.exports=require("node:url")},47261:e=>{"use strict";e.exports=require("node:util")},93746:e=>{"use strict";e.exports=require("node:util/types")},24086:e=>{"use strict";e.exports=require("node:worker_threads")},65628:e=>{"use strict";e.exports=require("node:zlib")},36085:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>n.a,__next_app__:()=>p,originalPathname:()=>l,pages:()=>c,routeModule:()=>x,tree:()=>d}),t(55855),t(33709),t(35866);var s=t(23191),o=t(88716),i=t(37922),n=t.n(i),a=t(95231),u={};for(let e in a)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(u[e]=()=>a[e]);t.d(r,u);let d=["",{children:["mobile",{children:["dashboard",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,55855)),"C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\mobile\\dashboard\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,33709)),"C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,35866,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],c=["C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\mobile\\dashboard\\page.tsx"],l="/mobile/dashboard/page",p={require:t,loadChunk:()=>Promise.resolve()},x=new s.AppPageRouteModule({definition:{kind:o.x.APP_PAGE,page:"/mobile/dashboard/page",pathname:"/mobile/dashboard",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},60869:(e,r,t)=>{Promise.resolve().then(t.bind(t,21957))},21957:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>h});var s=t(10326),o=t(19551),i=t(50648),n=t(43058),a=t(6283),u=t(42265),d=t(17577),c=t(41014),l=t(35047),p=t(25842),x=t(56608);let h=(0,c.Z)(()=>{let e=(0,l.useRouter)(),r=(0,p.I0)(),[t,c]=(0,d.useState)(!1);return(0,d.useEffect)(()=>{c("true"===localStorage.getItem("isTeamsApp1"))},[]),s.jsx(n.Z,{children:(0,s.jsxs)(a.Z,{sx:{height:"100%",width:"100vw",bgcolor:"#f6f8fc",display:"flex",flexDirection:"column",alignItems:"center"},children:[s.jsx(i.Z,{}),s.jsx(o.Z,{}),s.jsx(a.Z,{sx:{width:"100%",mt:2}}),s.jsx(u.Z,{variant:"contained",onClick:()=>r((0,x.FJ)()),sx:{textTransform:"none",borderRadius:"8px",bgcolor:"rgba(0, 0, 0, 0.06)",color:"black",boxShadow:"none",width:"90%",paddingY:"10px","&:hover":{backgroundColor:"rgba(0, 0, 0, 0.1)",boxShadow:"none"}},children:"View Benefits"}),s.jsx(a.Z,{sx:{width:"100%",mt:2}}),!t&&s.jsx(u.Z,{variant:"contained",onClick:()=>e.push("/qHarmonyBot"),sx:{textTransform:"none",borderRadius:"8px",bgcolor:"rgba(0, 0, 0, 0.06)",color:"black",boxShadow:"none",width:"90%",paddingY:"10px","&:hover":{backgroundColor:"rgba(0, 0, 0, 0.1)",boxShadow:"none"},mb:10},children:"Chat with Brea"})]})})})},43058:(e,r,t)=>{"use strict";t.d(r,{Z:()=>l});var s=t(10326),o=t(17577),i=t(22758),n=t(35047),a=t(31870);t(32049),t(94638);var u=t(98139),d=t(6283);let c=()=>/Mobi|Android/i.test(navigator.userAgent),l=({children:e})=>{let{user:r,loading:t}=(0,i.a)(),l=(0,n.useRouter)(),p=(0,n.usePathname)(),x=(0,a.T)(),[h,m]=(0,o.useState)(!1),g=(0,a.C)(e=>e.user.userProfile);return((0,o.useEffect)(()=>{},[x,g.name]),(0,o.useEffect)(()=>{console.log("ProtectedRoute useEffect triggered"),console.log("Current user: ",r),console.log("Loading state: ",t),console.log("Current user details: ",g),t||r||(console.log("User not authenticated, redirecting to home"),m(!1),l.push("/")),!t&&g.companyId&&""===g.companyId&&(console.log("Waiting to retrieve company details"),m(!1)),!t&&g.companyId&&""!==g.companyId&&(console.log("User found, rendering children"),m(!0)),c()&&!p.startsWith("/mobile")&&(console.log(`Redirecting to mobile version of ${p}`),l.push(`/mobile${p}`))},[r,t,g,l,p]),h)?r?s.jsx(s.Fragment,{children:e}):null:s.jsx(d.Z,{sx:{display:"flex",justifyContent:"center",alignItems:"center",height:"100vh",bgcolor:"#f6f8fc"},children:s.jsx(u.Z,{})})}},55855:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(68570).createProxy)(String.raw`C:\beno\project_dev\qharmony-frontend-v1\src\app\mobile\dashboard\page.tsx#default`)}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[8948,1183,6621,9066,1999,3253,928,8097,8522,3141,6027,541,8705,2142,5560,9434,576,401,43,2779,8512],()=>t(36085));module.exports=s})();