(()=>{var e={};e.id=3399,e.ids=[3399],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},27790:e=>{"use strict";e.exports=require("assert")},84770:e=>{"use strict";e.exports=require("crypto")},17702:e=>{"use strict";e.exports=require("events")},92048:e=>{"use strict";e.exports=require("fs")},32615:e=>{"use strict";e.exports=require("http")},35240:e=>{"use strict";e.exports=require("https")},19801:e=>{"use strict";e.exports=require("os")},55315:e=>{"use strict";e.exports=require("path")},76162:e=>{"use strict";e.exports=require("stream")},74026:e=>{"use strict";e.exports=require("string_decoder")},74175:e=>{"use strict";e.exports=require("tty")},17360:e=>{"use strict";e.exports=require("url")},21764:e=>{"use strict";e.exports=require("util")},71568:e=>{"use strict";e.exports=require("zlib")},98061:e=>{"use strict";e.exports=require("node:assert")},92761:e=>{"use strict";e.exports=require("node:async_hooks")},72254:e=>{"use strict";e.exports=require("node:buffer")},40027:e=>{"use strict";e.exports=require("node:console")},6005:e=>{"use strict";e.exports=require("node:crypto")},65714:e=>{"use strict";e.exports=require("node:diagnostics_channel")},15673:e=>{"use strict";e.exports=require("node:events")},88849:e=>{"use strict";e.exports=require("node:http")},42725:e=>{"use strict";e.exports=require("node:http2")},87503:e=>{"use strict";e.exports=require("node:net")},38846:e=>{"use strict";e.exports=require("node:perf_hooks")},39630:e=>{"use strict";e.exports=require("node:querystring")},84492:e=>{"use strict";e.exports=require("node:stream")},31764:e=>{"use strict";e.exports=require("node:tls")},41041:e=>{"use strict";e.exports=require("node:url")},47261:e=>{"use strict";e.exports=require("node:util")},93746:e=>{"use strict";e.exports=require("node:util/types")},24086:e=>{"use strict";e.exports=require("node:worker_threads")},65628:e=>{"use strict";e.exports=require("node:zlib")},43239:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>u,originalPathname:()=>p,pages:()=>c,routeModule:()=>h,tree:()=>d}),r(89049),r(33709),r(35866);var i=r(23191),a=r(88716),o=r(37922),n=r.n(o),s=r(95231),l={};for(let e in s)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>s[e]);r.d(t,l);let d=["",{children:["mobile",{children:["onboard",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,89049)),"C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\mobile\\onboard\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,33709)),"C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,35866,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],c=["C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\mobile\\onboard\\page.tsx"],p="/mobile/onboard/page",u={require:r,loadChunk:()=>Promise.resolve()},h=new i.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/mobile/onboard/page",pathname:"/mobile/onboard",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},56888:(e,t,r)=>{Promise.resolve().then(r.bind(r,4290))},99207:(e,t,r)=>{"use strict";r.d(t,{Z:()=>f});var i=r(17577),a=r(41135),o=r(88634),n=r(44823),s=r(91703),l=r(13643),d=r(2791),c=r(73025),p=r(10326);let u=e=>{let{absolute:t,children:r,classes:i,flexItem:a,light:n,orientation:s,textAlign:l,variant:d}=e;return(0,o.Z)({root:["root",t&&"absolute",d,n&&"light","vertical"===s&&"vertical",a&&"flexItem",r&&"withChildren",r&&"vertical"===s&&"withChildrenVertical","right"===l&&"vertical"!==s&&"textAlignRight","left"===l&&"vertical"!==s&&"textAlignLeft"],wrapper:["wrapper","vertical"===s&&"wrapperVertical"]},c.V,i)},h=(0,s.default)("div",{name:"MuiDivider",slot:"Root",overridesResolver:(e,t)=>{let{ownerState:r}=e;return[t.root,r.absolute&&t.absolute,t[r.variant],r.light&&t.light,"vertical"===r.orientation&&t.vertical,r.flexItem&&t.flexItem,r.children&&t.withChildren,r.children&&"vertical"===r.orientation&&t.withChildrenVertical,"right"===r.textAlign&&"vertical"!==r.orientation&&t.textAlignRight,"left"===r.textAlign&&"vertical"!==r.orientation&&t.textAlignLeft]}})((0,l.Z)(({theme:e})=>({margin:0,flexShrink:0,borderWidth:0,borderStyle:"solid",borderColor:(e.vars||e).palette.divider,borderBottomWidth:"thin",variants:[{props:{absolute:!0},style:{position:"absolute",bottom:0,left:0,width:"100%"}},{props:{light:!0},style:{borderColor:e.vars?`rgba(${e.vars.palette.dividerChannel} / 0.08)`:(0,n.Fq)(e.palette.divider,.08)}},{props:{variant:"inset"},style:{marginLeft:72}},{props:{variant:"middle",orientation:"horizontal"},style:{marginLeft:e.spacing(2),marginRight:e.spacing(2)}},{props:{variant:"middle",orientation:"vertical"},style:{marginTop:e.spacing(1),marginBottom:e.spacing(1)}},{props:{orientation:"vertical"},style:{height:"100%",borderBottomWidth:0,borderRightWidth:"thin"}},{props:{flexItem:!0},style:{alignSelf:"stretch",height:"auto"}},{props:({ownerState:e})=>!!e.children,style:{display:"flex",textAlign:"center",border:0,borderTopStyle:"solid",borderLeftStyle:"solid","&::before, &::after":{content:'""',alignSelf:"center"}}},{props:({ownerState:e})=>e.children&&"vertical"!==e.orientation,style:{"&::before, &::after":{width:"100%",borderTop:`thin solid ${(e.vars||e).palette.divider}`,borderTopStyle:"inherit"}}},{props:({ownerState:e})=>"vertical"===e.orientation&&e.children,style:{flexDirection:"column","&::before, &::after":{height:"100%",borderLeft:`thin solid ${(e.vars||e).palette.divider}`,borderLeftStyle:"inherit"}}},{props:({ownerState:e})=>"right"===e.textAlign&&"vertical"!==e.orientation,style:{"&::before":{width:"90%"},"&::after":{width:"10%"}}},{props:({ownerState:e})=>"left"===e.textAlign&&"vertical"!==e.orientation,style:{"&::before":{width:"10%"},"&::after":{width:"90%"}}}]}))),m=(0,s.default)("span",{name:"MuiDivider",slot:"Wrapper",overridesResolver:(e,t)=>{let{ownerState:r}=e;return[t.wrapper,"vertical"===r.orientation&&t.wrapperVertical]}})((0,l.Z)(({theme:e})=>({display:"inline-block",paddingLeft:`calc(${e.spacing(1)} * 1.2)`,paddingRight:`calc(${e.spacing(1)} * 1.2)`,whiteSpace:"nowrap",variants:[{props:{orientation:"vertical"},style:{paddingTop:`calc(${e.spacing(1)} * 1.2)`,paddingBottom:`calc(${e.spacing(1)} * 1.2)`}}]}))),x=i.forwardRef(function(e,t){let r=(0,d.i)({props:e,name:"MuiDivider"}),{absolute:i=!1,children:o,className:n,orientation:s="horizontal",component:l=o||"vertical"===s?"div":"hr",flexItem:c=!1,light:x=!1,role:f="hr"!==l?"separator":void 0,textAlign:g="center",variant:v="fullWidth",...b}=r,y={...r,absolute:i,component:l,flexItem:c,light:x,orientation:s,role:f,textAlign:g,variant:v},j=u(y);return(0,p.jsx)(h,{as:l,className:(0,a.Z)(j.root,n),role:f,ref:t,ownerState:y,"aria-orientation":"separator"===f&&("hr"!==l||"vertical"===s)?s:void 0,...b,children:o?(0,p.jsx)(m,{className:j.wrapper,ownerState:y,children:o}):null})});x&&(x.muiSkipListHighlight=!0);let f=x},73025:(e,t,r)=>{"use strict";r.d(t,{V:()=>o,Z:()=>n});var i=r(71685),a=r(97898);function o(e){return(0,a.ZP)("MuiDivider",e)}let n=(0,i.Z)("MuiDivider",["root","absolute","fullWidth","inset","middle","flexItem","light","vertical","withChildren","withChildrenVertical","textAlignRight","textAlignLeft","wrapper","wrapperVertical"])},37841:(e,t,r)=>{"use strict";r.d(t,{Z:()=>C});var i=r(17577),a=r(41135),o=r(88634),n=r(44823),s=r(27080),l=r(91703),d=r(13643),c=r(2791),p=r(92992),u=r(49006),h=r(69408),m=r(37382),x=r(73025),f=r(41598),g=r(25310),v=r(71685),b=r(97898);function y(e){return(0,b.ZP)("MuiMenuItem",e)}let j=(0,v.Z)("MuiMenuItem",["root","focusVisible","dense","disabled","divider","gutters","selected"]);var Z=r(10326);let S=e=>{let{disabled:t,dense:r,divider:i,disableGutters:a,selected:n,classes:s}=e,l=(0,o.Z)({root:["root",r&&"dense",t&&"disabled",!a&&"gutters",i&&"divider",n&&"selected"]},y,s);return{...s,...l}},w=(0,l.default)(u.Z,{shouldForwardProp:e=>(0,s.Z)(e)||"classes"===e,name:"MuiMenuItem",slot:"Root",overridesResolver:(e,t)=>{let{ownerState:r}=e;return[t.root,r.dense&&t.dense,r.divider&&t.divider,!r.disableGutters&&t.gutters]}})((0,d.Z)(({theme:e})=>({...e.typography.body1,display:"flex",justifyContent:"flex-start",alignItems:"center",position:"relative",textDecoration:"none",minHeight:48,paddingTop:6,paddingBottom:6,boxSizing:"border-box",whiteSpace:"nowrap","&:hover":{textDecoration:"none",backgroundColor:(e.vars||e).palette.action.hover,"@media (hover: none)":{backgroundColor:"transparent"}},[`&.${j.selected}`]:{backgroundColor:e.vars?`rgba(${e.vars.palette.primary.mainChannel} / ${e.vars.palette.action.selectedOpacity})`:(0,n.Fq)(e.palette.primary.main,e.palette.action.selectedOpacity),[`&.${j.focusVisible}`]:{backgroundColor:e.vars?`rgba(${e.vars.palette.primary.mainChannel} / calc(${e.vars.palette.action.selectedOpacity} + ${e.vars.palette.action.focusOpacity}))`:(0,n.Fq)(e.palette.primary.main,e.palette.action.selectedOpacity+e.palette.action.focusOpacity)}},[`&.${j.selected}:hover`]:{backgroundColor:e.vars?`rgba(${e.vars.palette.primary.mainChannel} / calc(${e.vars.palette.action.selectedOpacity} + ${e.vars.palette.action.hoverOpacity}))`:(0,n.Fq)(e.palette.primary.main,e.palette.action.selectedOpacity+e.palette.action.hoverOpacity),"@media (hover: none)":{backgroundColor:e.vars?`rgba(${e.vars.palette.primary.mainChannel} / ${e.vars.palette.action.selectedOpacity})`:(0,n.Fq)(e.palette.primary.main,e.palette.action.selectedOpacity)}},[`&.${j.focusVisible}`]:{backgroundColor:(e.vars||e).palette.action.focus},[`&.${j.disabled}`]:{opacity:(e.vars||e).palette.action.disabledOpacity},[`& + .${x.Z.root}`]:{marginTop:e.spacing(1),marginBottom:e.spacing(1)},[`& + .${x.Z.inset}`]:{marginLeft:52},[`& .${g.Z.root}`]:{marginTop:0,marginBottom:0},[`& .${g.Z.inset}`]:{paddingLeft:36},[`& .${f.Z.root}`]:{minWidth:36},variants:[{props:({ownerState:e})=>!e.disableGutters,style:{paddingLeft:16,paddingRight:16}},{props:({ownerState:e})=>e.divider,style:{borderBottom:`1px solid ${(e.vars||e).palette.divider}`,backgroundClip:"padding-box"}},{props:({ownerState:e})=>!e.dense,style:{[e.breakpoints.up("sm")]:{minHeight:"auto"}}},{props:({ownerState:e})=>e.dense,style:{minHeight:32,paddingTop:4,paddingBottom:4,...e.typography.body2,[`& .${f.Z.root} svg`]:{fontSize:"1.25rem"}}}]}))),C=i.forwardRef(function(e,t){let r;let o=(0,c.i)({props:e,name:"MuiMenuItem"}),{autoFocus:n=!1,component:s="li",dense:l=!1,divider:d=!1,disableGutters:u=!1,focusVisibleClassName:x,role:f="menuitem",tabIndex:g,className:v,...b}=o,y=i.useContext(p.Z),j=i.useMemo(()=>({dense:l||y.dense||!1,disableGutters:u}),[y.dense,l,u]),C=i.useRef(null);(0,h.Z)(()=>{n&&C.current&&C.current.focus()},[n]);let q={...o,dense:j.dense,divider:d,disableGutters:u},A=S(o),P=(0,m.Z)(C,t);return o.disabled||(r=void 0!==g?g:-1),(0,Z.jsx)(p.Z.Provider,{value:j,children:(0,Z.jsx)(w,{ref:P,role:f,tabIndex:r,component:s,focusVisibleClassName:(0,a.Z)(A.focusVisible,x),className:(0,a.Z)(A.root,v),...b,ownerState:q,classes:A})})})},4290:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>w});var i=r(10326),a=r(17577),o=r(46791),n=r(26472),s=r(53913),l=r(90943),d=r(90541),c=r(6283),p=r(25609),u=r(98139),h=r(42265),m=r(16027),x=r(37841),f=r(35047),g=r(59423),v=r(94638),b=r(31870),y=r(46226),j=r(30656),Z=r(41014);let S=({label:e,value:t,onChange:r,error:a,helperText:o,placeholder:n,select:c,children:p,readOnly:u})=>(0,i.jsxs)(s.Z,{fullWidth:!0,children:[i.jsx(l.Z,{sx:{color:"#ffffff",fontWeight:"500",mb:1,fontSize:"17px"},children:e}),i.jsx(d.Z,{variant:"outlined",value:t,onChange:r,error:!!a,helperText:o,placeholder:n,select:c,inputProps:{readOnly:u},sx:{borderRadius:"10px",backgroundColor:"#333333",mb:0,input:{color:"#ffffff"},"& .MuiOutlinedInput-root":{borderRadius:"10px","&:hover fieldset":{borderColor:a?"#ff0000":"#ffffff"},"&.Mui-focused fieldset":{borderColor:a?"#ff0000":"#ffffff"}},"& .MuiInputLabel-root":{color:"#ffffff"},"& .MuiSvgIcon-root":{color:"#ffffff"},"& .MuiSelect-select":{color:"#ffffff"}},children:p})]}),w=(0,Z.Z)(()=>{let e=(0,b.T)(),t=(0,f.useRouter)(),[r,s]=(0,a.useState)(!1),[l,d]=(0,a.useState)(null),[Z,w]=(0,a.useState)(!1),[C,q]=(0,a.useState)(!0),[A,P]=(0,a.useState)(!1),[R,k]=(0,a.useState)(!1),[I,B]=(0,a.useState)({email:"",name:"",role:"",isAdmin:!1,isBroker:!1,isActivated:!1}),[D,T]=(0,a.useState)({name:"",adminEmail:"",adminRole:"",companySize:0,industry:"",location:"",website:"",brokerId:"",brokerageId:"",isBrokerage:!1,isActivated:!1,howHeard:"",referralSource:""}),[M,O]=(0,a.useState)({isAdmin:!1}),[z,E]=(0,a.useState)({});(0,a.useEffect)(()=>{(async()=>{let e=window.location.href,t=await (0,g.lY)(e);if(t.companyDetails&&T(t.companyDetails),t.userDetails&&B(t.userDetails),t.additionalParams&&(O(t.additionalParams),t.additionalParams.email&&d(t.additionalParams.email)),(0,o.JB)(n.I,e)){let e=window.localStorage.getItem("emailForSignIn1");e||t.additionalParams.email?d(e||t.additionalParams.email):s(!0)}q(!1)})()},[]),(0,a.useEffect)(()=>{(async()=>{if(l)try{await (0,o.P6)(n.I,l,window.location.href),console.log("Sign-in successful"),window.localStorage.removeItem("emailForSignIn1"),w(!0)}catch(e){console.error("Error signing in with email link:",e),k(!0)}})()},[l]);let $=()=>{let e={},t=!0;return I.name||(e.name="Full Name is required",t=!1),M.isAdmin&&(D.name||(e.companyName="Company Name is required",t=!1),D.adminRole||(e.adminRole="Role/Title is required",t=!1),D.companySize||(e.companySize="Company Size is required",t=!1),D.industry||(e.industry="Industry is required",t=!1),D.location||(e.location="Country is required",t=!1),D.howHeard||(e.howHeard="How did you hear about us is required",t=!1),D.referralSource||(e.referralSource="Referral Source is required",t=!1)),E(e),t},F=async()=>{if(!$())return;P(!0);let r=await (0,v.zX)(e,D,I);r&&200===r.status&&t.push("/dashboard"),P(!1)},L=(e,t,r)=>{E({...z,[e]:""}),"userDetails"===r?B({...I,[e]:t}):T({...D,[e]:t})};return(0,i.jsxs)(c.Z,{sx:{backgroundColor:"#000000",minHeight:"98px",flexDirection:"column",alignItems:"center",justifyContent:"flex-start"},children:[(0,i.jsxs)(c.Z,{sx:{display:"flex",alignItems:"center",mb:5,cursor:"pointer",position:"absolute",top:"30px",left:"30px"},onClick:()=>console.log("Logo Clicked"),children:[i.jsx(y.default,{src:j.Z,alt:"BenOsphere Logo",width:40,height:40}),i.jsx(p.Z,{variant:"h6",sx:{ml:1,fontWeight:"800",color:"#ffffff"},children:"BenOsphere"})]}),C?i.jsx(u.Z,{color:"inherit"}):R?(0,i.jsxs)(c.Z,{sx:{display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",color:"white",textAlign:"center",height:"100%"},children:[i.jsx(p.Z,{sx:{fontWeight:"bold",fontSize:"60px",mb:2},children:"❌ Invalid Magic Link"}),i.jsx(p.Z,{variant:"body1",sx:{fontSize:"20px",color:"#bbbbbb",mb:4},children:"The magic link you used is invalid or has expired. Please try again."}),i.jsx(h.Z,{variant:"contained",onClick:()=>t.push("/"),sx:{textTransform:"none",background:"linear-gradient(90deg, #7206E6, #B54BFF)",color:"#ffffff",height:"54px",borderRadius:"10px",boxShadow:"none",fontSize:"17px","&:hover":{background:"linear-gradient(90deg, #7206E6, #B54BFF)"}},children:"Try Again"})]}):Z&&(0,i.jsxs)(c.Z,{sx:{width:"100%",maxWidth:"100%"},children:[i.jsx(p.Z,{variant:"h4",sx:{fontSize:"50px",fontWeight:"bold",lineHeight:"1.2",color:"#ffffff",mb:3},children:"Get Free Access"}),(0,i.jsxs)(m.ZP,{container:!0,spacing:3,sx:{marginBottom:"16px"},children:[i.jsx(m.ZP,{item:!0,xs:12,children:i.jsx(S,{label:"Full Name",value:I.name,onChange:e=>L("name",e.target.value,"userDetails"),error:z.name,helperText:z.name,placeholder:"John Doe"})}),i.jsx(m.ZP,{item:!0,xs:12,children:i.jsx(S,{label:"Email",value:I.email,onChange:()=>{},readOnly:!0})})]}),M.isAdmin&&i.jsx(c.Z,{sx:{mt:3},children:(0,i.jsxs)(m.ZP,{container:!0,spacing:3,sx:{marginBottom:"16px"},children:[i.jsx(m.ZP,{item:!0,xs:12,children:i.jsx(S,{label:"Company Name",value:D.name,onChange:e=>L("name",e.target.value,"companyDetails"),error:z.companyName,helperText:z.companyName,placeholder:"Doe LLC"})}),i.jsx(m.ZP,{item:!0,xs:12,children:(0,i.jsxs)(S,{label:"Role/Title",value:D.adminRole,onChange:e=>L("adminRole",e.target.value,"companyDetails"),error:z.adminRole,helperText:z.adminRole,select:!0,children:[i.jsx(x.Z,{value:"CEO",children:"CEO"}),i.jsx(x.Z,{value:"HR Manager",children:"HR Manager"}),i.jsx(x.Z,{value:"Benefits Specialist",children:"Benefits Specialist"}),i.jsx(x.Z,{value:"CFO",children:"CFO"}),i.jsx(x.Z,{value:"Other",children:"Other"})]})}),i.jsx(m.ZP,{item:!0,xs:12,children:(0,i.jsxs)(S,{label:"Company Size",value:D.companySize,onChange:e=>L("companySize",e.target.value,"companyDetails"),error:z.companySize,helperText:z.companySize,select:!0,children:[i.jsx(x.Z,{value:1,children:"1-10 employees"}),i.jsx(x.Z,{value:2,children:"11-50 employees"}),i.jsx(x.Z,{value:3,children:"51-200 employees"}),i.jsx(x.Z,{value:4,children:"201-500 employees"}),i.jsx(x.Z,{value:5,children:"500+ employees"})]})}),i.jsx(m.ZP,{item:!0,xs:12,children:(0,i.jsxs)(S,{label:"Industry",value:D.industry,onChange:e=>L("industry",e.target.value,"companyDetails"),error:z.industry,helperText:z.industry,select:!0,children:[i.jsx(x.Z,{value:"Technology",children:"Technology"}),i.jsx(x.Z,{value:"Healthcare",children:"Healthcare"}),i.jsx(x.Z,{value:"Finance",children:"Finance"}),i.jsx(x.Z,{value:"Education",children:"Education"}),i.jsx(x.Z,{value:"Insurance Broker",children:"Insurance Broker"}),i.jsx(x.Z,{value:"Other",children:"Other"})]})}),i.jsx(m.ZP,{item:!0,xs:12,children:i.jsx(S,{label:"Country",value:D.location,onChange:e=>L("location",e.target.value,"companyDetails"),error:z.location,helperText:z.location,select:!0,children:i.jsx(x.Z,{value:"United States",children:"United States"})})}),i.jsx(m.ZP,{item:!0,xs:12,children:(0,i.jsxs)(S,{label:"How did you hear about us",value:D.howHeard,onChange:e=>L("howHeard",e.target.value,"companyDetails"),error:z.howHeard,helperText:z.howHeard,select:!0,children:[i.jsx(x.Z,{value:"Referral",children:"Referral"}),i.jsx(x.Z,{value:"Social Media",children:"Social Media"}),i.jsx(x.Z,{value:"Search Engine",children:"Search Engine"}),i.jsx(x.Z,{value:"Advertisement",children:"Advertisement"}),i.jsx(x.Z,{value:"Other",children:"Other"})]})}),i.jsx(m.ZP,{item:!0,xs:12,children:i.jsx(S,{label:"Referral Source",value:D.referralSource,onChange:e=>L("referralSource",e.target.value,"companyDetails"),error:z.referralSource,helperText:z.referralSource,placeholder:"LinkedIn, Twitter, etc."})})]})}),i.jsx(c.Z,{sx:{mt:4},children:A?i.jsx(u.Z,{size:24,sx:{color:"#ffffff"}}):i.jsx(h.Z,{variant:"contained",color:"primary",fullWidth:!0,sx:{background:"linear-gradient(90deg, #7206E6, #B54BFF)",padding:"12px",fontSize:"16px",textTransform:"none",borderRadius:2,mb:10},onClick:F,children:"Confirm Details"})})]})]})})},89049:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i});let i=(0,r(68570).createProxy)(String.raw`C:\beno\project_dev\qharmony-frontend-v1\src\app\mobile\onboard\page.tsx#default`)},30656:(e,t,r)=>{"use strict";r.d(t,{Z:()=>i});let i={src:"/_next/static/media/logo.770bfeee.png",height:300,width:300,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAYAAADED76LAAAA9ElEQVR42i2PMUoDQRiF3z+zu7MbFTaxkfUC0St4ACWdhbbapvACCnaCpU0a09jYiQewFj2AIljY2AoJ7LKZnc3s/P6EPPiq9+DxESR359UgBHNrGxTWQTcOPwtrLwGU6n5c7ySxeQb4vWn9WS/lky50JVH6UlrVjzTFE2iebvbwmJkE37/zPLB79SHfzWIzUUph0LrqScB4qpFEdEhICxm9BeY9BYA9kxJwfTw7IEKfGUsAq06FgNlGtjUSoDTvS1mB3B/BDInoM/KhvQhd8lDb5RGz/pDLVFM+inVc1L49pbXmtmjeiOZQNCGaX4vGXQGY/wM1tG/NQnnUIwAAAABJRU5ErkJggg==",blurWidth:8,blurHeight:8}}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),i=t.X(0,[8948,1183,6621,9066,1999,3253,928,8097,8522,3141,6027,541,8705,576,401,43],()=>r(43239));module.exports=i})();