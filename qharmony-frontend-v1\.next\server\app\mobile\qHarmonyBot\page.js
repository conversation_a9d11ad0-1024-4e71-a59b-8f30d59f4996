(()=>{var e={};e.id=4359,e.ids=[4359],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},27790:e=>{"use strict";e.exports=require("assert")},84770:e=>{"use strict";e.exports=require("crypto")},17702:e=>{"use strict";e.exports=require("events")},92048:e=>{"use strict";e.exports=require("fs")},32615:e=>{"use strict";e.exports=require("http")},35240:e=>{"use strict";e.exports=require("https")},19801:e=>{"use strict";e.exports=require("os")},55315:e=>{"use strict";e.exports=require("path")},76162:e=>{"use strict";e.exports=require("stream")},74026:e=>{"use strict";e.exports=require("string_decoder")},74175:e=>{"use strict";e.exports=require("tty")},17360:e=>{"use strict";e.exports=require("url")},21764:e=>{"use strict";e.exports=require("util")},71568:e=>{"use strict";e.exports=require("zlib")},98061:e=>{"use strict";e.exports=require("node:assert")},92761:e=>{"use strict";e.exports=require("node:async_hooks")},72254:e=>{"use strict";e.exports=require("node:buffer")},40027:e=>{"use strict";e.exports=require("node:console")},6005:e=>{"use strict";e.exports=require("node:crypto")},65714:e=>{"use strict";e.exports=require("node:diagnostics_channel")},15673:e=>{"use strict";e.exports=require("node:events")},88849:e=>{"use strict";e.exports=require("node:http")},42725:e=>{"use strict";e.exports=require("node:http2")},87503:e=>{"use strict";e.exports=require("node:net")},38846:e=>{"use strict";e.exports=require("node:perf_hooks")},39630:e=>{"use strict";e.exports=require("node:querystring")},84492:e=>{"use strict";e.exports=require("node:stream")},31764:e=>{"use strict";e.exports=require("node:tls")},41041:e=>{"use strict";e.exports=require("node:url")},47261:e=>{"use strict";e.exports=require("node:util")},93746:e=>{"use strict";e.exports=require("node:util/types")},24086:e=>{"use strict";e.exports=require("node:worker_threads")},65628:e=>{"use strict";e.exports=require("node:zlib")},53645:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>u,originalPathname:()=>p,pages:()=>c,routeModule:()=>h,tree:()=>d}),r(44824),r(33709),r(35866);var o=r(23191),i=r(88716),s=r(37922),n=r.n(s),a=r(95231),l={};for(let e in a)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>a[e]);r.d(t,l);let d=["",{children:["mobile",{children:["qHarmonyBot",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,44824)),"C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\mobile\\qHarmonyBot\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,33709)),"C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,35866,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],c=["C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\mobile\\qHarmonyBot\\page.tsx"],p="/mobile/qHarmonyBot/page",u={require:r,loadChunk:()=>Promise.resolve()},h=new o.AppPageRouteModule({definition:{kind:i.x.APP_PAGE,page:"/mobile/qHarmonyBot/page",pathname:"/mobile/qHarmonyBot",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},11158:(e,t,r)=>{Promise.resolve().then(r.bind(r,97704))},33198:(e,t,r)=>{"use strict";r.d(t,{Z:()=>y});var o=r(17577),i=r(41135),s=r(88634),n=r(91703),a=r(13643),l=r(2791),d=r(51426),c=r(10326);let p=(0,d.Z)((0,c.jsx)("path",{d:"M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"}),"Person");var u=r(71685),h=r(97898);function g(e){return(0,h.ZP)("MuiAvatar",e)}(0,u.Z)("MuiAvatar",["root","colorDefault","circular","rounded","square","img","fallback"]);var f=r(31121);let m=e=>{let{classes:t,variant:r,colorDefault:o}=e;return(0,s.Z)({root:["root",r,o&&"colorDefault"],img:["img"],fallback:["fallback"]},g,t)},x=(0,n.default)("div",{name:"MuiAvatar",slot:"Root",overridesResolver:(e,t)=>{let{ownerState:r}=e;return[t.root,t[r.variant],r.colorDefault&&t.colorDefault]}})((0,a.Z)(({theme:e})=>({position:"relative",display:"flex",alignItems:"center",justifyContent:"center",flexShrink:0,width:40,height:40,fontFamily:e.typography.fontFamily,fontSize:e.typography.pxToRem(20),lineHeight:1,borderRadius:"50%",overflow:"hidden",userSelect:"none",variants:[{props:{variant:"rounded"},style:{borderRadius:(e.vars||e).shape.borderRadius}},{props:{variant:"square"},style:{borderRadius:0}},{props:{colorDefault:!0},style:{color:(e.vars||e).palette.background.default,...e.vars?{backgroundColor:e.vars.palette.Avatar.defaultBg}:{backgroundColor:e.palette.grey[400],...e.applyStyles("dark",{backgroundColor:e.palette.grey[600]})}}}]}))),v=(0,n.default)("img",{name:"MuiAvatar",slot:"Img",overridesResolver:(e,t)=>t.img})({width:"100%",height:"100%",textAlign:"center",objectFit:"cover",color:"transparent",textIndent:1e4}),b=(0,n.default)(p,{name:"MuiAvatar",slot:"Fallback",overridesResolver:(e,t)=>t.fallback})({width:"75%",height:"75%"}),y=o.forwardRef(function(e,t){let r=(0,l.i)({props:e,name:"MuiAvatar"}),{alt:s,children:n,className:a,component:d="div",slots:p={},slotProps:u={},imgProps:h,sizes:g,src:y,srcSet:w,variant:q="circular",...S}=r,j=null,I={...r,component:d,variant:q},Z=function({crossOrigin:e,referrerPolicy:t,src:r,srcSet:i}){let[s,n]=o.useState(!1);return o.useEffect(()=>{if(!r&&!i)return;n(!1);let o=!0,s=new Image;return s.onload=()=>{o&&n("loaded")},s.onerror=()=>{o&&n("error")},s.crossOrigin=e,s.referrerPolicy=t,s.src=r,i&&(s.srcset=i),()=>{o=!1}},[e,t,r,i]),s}({...h,..."function"==typeof u.img?u.img(I):u.img,src:y,srcSet:w}),R=y||w,k=R&&"error"!==Z;I.colorDefault=!k,delete I.ownerState;let C=m(I),[P,A]=(0,f.Z)("img",{className:C.img,elementType:v,externalForwardedProps:{slots:p,slotProps:{img:{...h,...u.img}}},additionalProps:{alt:s,src:y,srcSet:w,sizes:g},ownerState:I});return j=k?(0,c.jsx)(P,{...A}):n||0===n?n:R&&s?s[0]:(0,c.jsx)(b,{ownerState:I,className:C.fallback}),(0,c.jsx)(x,{as:d,className:(0,i.Z)(C.root,a),ref:t,...S,ownerState:I,children:j})})},99207:(e,t,r)=>{"use strict";r.d(t,{Z:()=>m});var o=r(17577),i=r(41135),s=r(88634),n=r(44823),a=r(91703),l=r(13643),d=r(2791),c=r(73025),p=r(10326);let u=e=>{let{absolute:t,children:r,classes:o,flexItem:i,light:n,orientation:a,textAlign:l,variant:d}=e;return(0,s.Z)({root:["root",t&&"absolute",d,n&&"light","vertical"===a&&"vertical",i&&"flexItem",r&&"withChildren",r&&"vertical"===a&&"withChildrenVertical","right"===l&&"vertical"!==a&&"textAlignRight","left"===l&&"vertical"!==a&&"textAlignLeft"],wrapper:["wrapper","vertical"===a&&"wrapperVertical"]},c.V,o)},h=(0,a.default)("div",{name:"MuiDivider",slot:"Root",overridesResolver:(e,t)=>{let{ownerState:r}=e;return[t.root,r.absolute&&t.absolute,t[r.variant],r.light&&t.light,"vertical"===r.orientation&&t.vertical,r.flexItem&&t.flexItem,r.children&&t.withChildren,r.children&&"vertical"===r.orientation&&t.withChildrenVertical,"right"===r.textAlign&&"vertical"!==r.orientation&&t.textAlignRight,"left"===r.textAlign&&"vertical"!==r.orientation&&t.textAlignLeft]}})((0,l.Z)(({theme:e})=>({margin:0,flexShrink:0,borderWidth:0,borderStyle:"solid",borderColor:(e.vars||e).palette.divider,borderBottomWidth:"thin",variants:[{props:{absolute:!0},style:{position:"absolute",bottom:0,left:0,width:"100%"}},{props:{light:!0},style:{borderColor:e.vars?`rgba(${e.vars.palette.dividerChannel} / 0.08)`:(0,n.Fq)(e.palette.divider,.08)}},{props:{variant:"inset"},style:{marginLeft:72}},{props:{variant:"middle",orientation:"horizontal"},style:{marginLeft:e.spacing(2),marginRight:e.spacing(2)}},{props:{variant:"middle",orientation:"vertical"},style:{marginTop:e.spacing(1),marginBottom:e.spacing(1)}},{props:{orientation:"vertical"},style:{height:"100%",borderBottomWidth:0,borderRightWidth:"thin"}},{props:{flexItem:!0},style:{alignSelf:"stretch",height:"auto"}},{props:({ownerState:e})=>!!e.children,style:{display:"flex",textAlign:"center",border:0,borderTopStyle:"solid",borderLeftStyle:"solid","&::before, &::after":{content:'""',alignSelf:"center"}}},{props:({ownerState:e})=>e.children&&"vertical"!==e.orientation,style:{"&::before, &::after":{width:"100%",borderTop:`thin solid ${(e.vars||e).palette.divider}`,borderTopStyle:"inherit"}}},{props:({ownerState:e})=>"vertical"===e.orientation&&e.children,style:{flexDirection:"column","&::before, &::after":{height:"100%",borderLeft:`thin solid ${(e.vars||e).palette.divider}`,borderLeftStyle:"inherit"}}},{props:({ownerState:e})=>"right"===e.textAlign&&"vertical"!==e.orientation,style:{"&::before":{width:"90%"},"&::after":{width:"10%"}}},{props:({ownerState:e})=>"left"===e.textAlign&&"vertical"!==e.orientation,style:{"&::before":{width:"10%"},"&::after":{width:"90%"}}}]}))),g=(0,a.default)("span",{name:"MuiDivider",slot:"Wrapper",overridesResolver:(e,t)=>{let{ownerState:r}=e;return[t.wrapper,"vertical"===r.orientation&&t.wrapperVertical]}})((0,l.Z)(({theme:e})=>({display:"inline-block",paddingLeft:`calc(${e.spacing(1)} * 1.2)`,paddingRight:`calc(${e.spacing(1)} * 1.2)`,whiteSpace:"nowrap",variants:[{props:{orientation:"vertical"},style:{paddingTop:`calc(${e.spacing(1)} * 1.2)`,paddingBottom:`calc(${e.spacing(1)} * 1.2)`}}]}))),f=o.forwardRef(function(e,t){let r=(0,d.i)({props:e,name:"MuiDivider"}),{absolute:o=!1,children:s,className:n,orientation:a="horizontal",component:l=s||"vertical"===a?"div":"hr",flexItem:c=!1,light:f=!1,role:m="hr"!==l?"separator":void 0,textAlign:x="center",variant:v="fullWidth",...b}=r,y={...r,absolute:o,component:l,flexItem:c,light:f,orientation:a,role:m,textAlign:x,variant:v},w=u(y);return(0,p.jsx)(h,{as:l,className:(0,i.Z)(w.root,n),role:m,ref:t,ownerState:y,"aria-orientation":"separator"===m&&("hr"!==l||"vertical"===a)?a:void 0,...b,children:s?(0,p.jsx)(g,{className:w.wrapper,ownerState:y,children:s}):null})});f&&(f.muiSkipListHighlight=!0);let m=f},73025:(e,t,r)=>{"use strict";r.d(t,{V:()=>s,Z:()=>n});var o=r(71685),i=r(97898);function s(e){return(0,i.ZP)("MuiDivider",e)}let n=(0,o.Z)("MuiDivider",["root","absolute","fullWidth","inset","middle","flexItem","light","vertical","withChildren","withChildrenVertical","textAlignRight","textAlignLeft","wrapper","wrapperVertical"])},97704:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>w});var o=r(10326),i=r(17577),s=r(25842),n=r(71411),a=r(25886),l=r(25609),d=r(6283),c=r(84979),p=r(33198),u=r(42265),h=r(90541),g=r(59028),f=r(7330),m=r(8106),x=r(32049),v=r(43058),b=r(89223),y=r(46226);let w=(0,r(41014).Z)(()=>{let e=(0,s.I0)(),t=(0,s.v9)(e=>(0,x.MP)(e)),r=(0,s.v9)(e=>e.user._id),w=(0,s.v9)(e=>e.user.userProfile),q=(0,s.v9)(e=>e.user.selectedFAQQuestion),S=(0,s.v9)(e=>e.qHarmonyBot.chatHistory),j=(0,s.v9)(e=>e.qHarmonyBot.isLoading),[I,Z]=(0,i.useState)(""),R=(0,i.useRef)(null),k=o=>{if(""===o.trim())return;let i={sender:"user",message:o.replace(/\n/g,"<br/>"),timestamp:new Date().toISOString()};e((0,g.Hz)(i)),e((0,g.wt)(!0)),(0,f.b)(e,o,r,t),Z("")},C=e=>{if(!e)return"";let[t,r]=e.split(" ");return`${t[0].toUpperCase()}${r?r[0].toUpperCase():""}`},P=()=>{R.current?.scrollIntoView({behavior:"smooth"})};(0,i.useEffect)(()=>{q&&(k(q),e((0,x.ki)()))},[q,e]),(0,i.useEffect)(()=>{0===S.length&&w.name&&!q&&(e((0,g.wt)(!0)),setTimeout(()=>{let t={sender:"bot",message:`Hey ${w.name}, how can I help you today?`,timestamp:new Date().toISOString()};e((0,g.Hz)(t)),e((0,g.wt)(!1))},2e3))},[S.length,w.name,e,q]),(0,i.useEffect)(()=>{P()},[S]);let A=(0,m.F4)`
    0% { content: ''; }
    25% { content: '.'; }
    50% { content: '..'; }
    75% { content: '...'; }
    100% { content: ''; }
  `,_=["View My Benefits","Enroll Now","Check Time Off","Update My Elections"];return o.jsx(v.Z,{children:(0,o.jsxs)(d.Z,{component:"main",sx:{flexGrow:1,display:"flex",flexDirection:"column",height:"95vh",bgcolor:"#f6f8fc"},children:[o.jsx(l.Z,{variant:"h5",sx:{fontWeight:"bold",p:2},children:"Brea - Your Round-the-Clock Benefits Expert"}),o.jsx(d.Z,{sx:{flexGrow:1,overflow:"auto"},children:(0,o.jsxs)(c.Z,{children:[S.map((e,t)=>(0,o.jsxs)(n.ZP,{sx:{display:"flex",flexDirection:"user"===e.sender?"row-reverse":"row",alignItems:"flex-start"},children:[o.jsx(p.Z,{sx:{bgcolor:"user"===e.sender?"#000":"transparent",mr:"user"===e.sender?0:2,ml:"user"===e.sender?2:0,mt:.5},children:"user"===e.sender?o.jsx(p.Z,{sx:{bgcolor:"black",color:"#ffffff",width:35,height:35,fontSize:"1.2rem",mr:1.5,ml:1.5,display:"flex",alignItems:"center",justifyContent:"center",paddingBottom:"1px",fontWeight:800},children:C(w.name)}):o.jsx(y.default,{src:b.Z,alt:"AI Assistant",style:{borderRadius:"50%",width:"40px",height:"40px"}})}),(0,o.jsxs)(d.Z,{sx:{maxWidth:"80%"},children:[o.jsx(a.Z,{primary:o.jsx("span",{dangerouslySetInnerHTML:{__html:"bot"===e.sender?`${e.message}<br/><small style="color: gray;">AI-generated content—verify before use.</small>`:e.message},style:{whiteSpace:"pre-wrap",wordBreak:"break-word"}}),sx:{bgcolor:"user"===e.sender?"#000":"#fff",color:"user"===e.sender?"#fff":"#000",borderRadius:"user"===e.sender?"16px 16px 4px 16px":"16px 16px 16px 4px",padding:"10px",mb:1}}),"bot"===e.sender&&e.message.includes("how can I help you today?")&&t===S.length-1&&o.jsx(d.Z,{sx:{display:"flex",mt:1},children:_.map(e=>o.jsx(u.Z,{variant:"contained",sx:{mr:1,textTransform:"none",fontSize:"12px",borderRadius:"6px",px:2,py:1,bgcolor:"black",color:"white"},onClick:()=>k(e),children:e},e))})]})]},t)),j&&o.jsx(()=>(0,o.jsxs)(n.ZP,{sx:{display:"flex",alignItems:"flex-start"},children:[o.jsx(y.default,{src:b.Z,alt:"AI Chat",style:{borderRadius:"50%",width:"40px",height:"40px",marginRight:"10px"}}),o.jsx(a.Z,{primary:o.jsx(l.Z,{component:"span",sx:{display:"inline-block",fontSize:"16px","&::after":{content:"''",animation:`${A} 1.5s infinite`,display:"inline-block",width:"16px"}},children:"Brea is typing"}),sx:{bgcolor:"#fff",borderRadius:"20px",p:1.5,mb:1,maxWidth:"80%",mt:-.5}})]}),{}),o.jsx("div",{ref:R})]})}),o.jsx(d.Z,{sx:{p:2,borderTop:"1px solid #e0e0e0"},children:(0,o.jsxs)(d.Z,{sx:{display:"flex",alignItems:"flex-start",bgcolor:"#fff",borderRadius:"20px",height:"150px",boxShadow:"0 1px 3px rgba(0, 0, 0, 0.1)"},children:[o.jsx(h.Z,{fullWidth:!0,variant:"outlined",placeholder:"Type your message...",value:I,onChange:e=>Z(e.target.value),onKeyPress:e=>{"Enter"!==e.key||e.shiftKey||(e.preventDefault(),k(I))},multiline:!0,rows:5,sx:{mr:2,borderRadius:"20px",fontSize:"16px",height:"150px","& .MuiOutlinedInput-root":{padding:"12px",border:"none",display:"flex",alignItems:"flex-start"},"& .MuiOutlinedInput-notchedOutline":{border:"none"}}}),o.jsx(u.Z,{variant:"contained",sx:{bgcolor:I?"#1073ff":"#e0e0e0",color:"#fff",borderRadius:"25px",m:2,p:0,fontSize:"16px",height:"45px",width:"60px",boxShadow:"none",textTransform:"none","&:hover":{bgcolor:I?"#005bb5":"#d0d0d0"}},onClick:()=>k(I),children:"Send"})]})})]})})})},43058:(e,t,r)=>{"use strict";r.d(t,{Z:()=>p});var o=r(10326),i=r(17577),s=r(22758),n=r(35047),a=r(31870);r(32049),r(94638);var l=r(98139),d=r(6283);let c=()=>/Mobi|Android/i.test(navigator.userAgent),p=({children:e})=>{let{user:t,loading:r}=(0,s.a)(),p=(0,n.useRouter)(),u=(0,n.usePathname)(),h=(0,a.T)(),[g,f]=(0,i.useState)(!1),m=(0,a.C)(e=>e.user.userProfile);return((0,i.useEffect)(()=>{},[h,m.name]),(0,i.useEffect)(()=>{console.log("ProtectedRoute useEffect triggered"),console.log("Current user: ",t),console.log("Loading state: ",r),console.log("Current user details: ",m),r||t||(console.log("User not authenticated, redirecting to home"),f(!1),p.push("/")),!r&&m.companyId&&""===m.companyId&&(console.log("Waiting to retrieve company details"),f(!1)),!r&&m.companyId&&""!==m.companyId&&(console.log("User found, rendering children"),f(!0)),c()&&!u.startsWith("/mobile")&&(console.log(`Redirecting to mobile version of ${u}`),p.push(`/mobile${u}`))},[t,r,m,p,u]),g)?t?o.jsx(o.Fragment,{children:e}):null:o.jsx(d.Z,{sx:{display:"flex",justifyContent:"center",alignItems:"center",height:"100vh",bgcolor:"#f6f8fc"},children:o.jsx(l.Z,{})})}},7330:(e,t,r)=>{"use strict";r.d(t,{b:()=>s});var o=r(59028);let i=(0,r(89009).GU)();async function s(e,t,r,s){let n={user_id:r,user_message:t,team_id:s};try{console.log("Sending chat message:",n);let t=await fetch(`${i}/chat`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(n)});if(!t.body)throw Error("Readable stream not supported");let r=t.body.getReader(),s=new TextDecoder("utf-8"),a={sender:"bot",message:"",timestamp:new Date().toISOString()};for(e((0,o.wt)(!0));;){let{done:t,value:i}=await r.read();if(t)break;let n=s.decode(i,{stream:!0});e((0,o.wt)(!1)),console.log("Chunk:",n),a.message+=n,e((0,o.Hz)({sender:"bot",message:n,timestamp:new Date().toISOString()}))}}catch(r){console.error("Error sending chat message:",r);let t={sender:"bot",message:"Sorry, I encountered an error while processing your request.",timestamp:new Date().toISOString()};e((0,o.Hz)(t)),e((0,o.wt)(!1))}}},89009:(e,t,r)=>{"use strict";r.d(t,{GU:()=>s,bR:()=>o,n5:()=>i});let o=()=>"http://localhost:8080",i=()=>{let e="userid1",t="userId",r=localStorage.getItem(e)||localStorage.getItem(t);return(console.log("\uD83D\uDD0D getUserId debug:",{primaryKey:e,altKey:t,primaryValue:localStorage.getItem(e),altValue:localStorage.getItem(t),finalUserId:r}),r)?r:(console.error("❌ User ID not found in localStorage"),"default-user")},s=()=>"https://bot.benosphere.com"},44824:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>o});let o=(0,r(68570).createProxy)(String.raw`C:\beno\project_dev\qharmony-frontend-v1\src\app\mobile\qHarmonyBot\page.tsx#default`)}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),o=t.X(0,[8948,1183,6621,9066,1999,3253,928,8097,8522,3141,541,8705,576,401,43],()=>r(53645));module.exports=o})();