(()=>{var e={};e.id=8717,e.ids=[8717],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},27790:e=>{"use strict";e.exports=require("assert")},84770:e=>{"use strict";e.exports=require("crypto")},17702:e=>{"use strict";e.exports=require("events")},92048:e=>{"use strict";e.exports=require("fs")},32615:e=>{"use strict";e.exports=require("http")},35240:e=>{"use strict";e.exports=require("https")},19801:e=>{"use strict";e.exports=require("os")},55315:e=>{"use strict";e.exports=require("path")},76162:e=>{"use strict";e.exports=require("stream")},74026:e=>{"use strict";e.exports=require("string_decoder")},74175:e=>{"use strict";e.exports=require("tty")},17360:e=>{"use strict";e.exports=require("url")},21764:e=>{"use strict";e.exports=require("util")},71568:e=>{"use strict";e.exports=require("zlib")},98061:e=>{"use strict";e.exports=require("node:assert")},92761:e=>{"use strict";e.exports=require("node:async_hooks")},72254:e=>{"use strict";e.exports=require("node:buffer")},40027:e=>{"use strict";e.exports=require("node:console")},6005:e=>{"use strict";e.exports=require("node:crypto")},65714:e=>{"use strict";e.exports=require("node:diagnostics_channel")},15673:e=>{"use strict";e.exports=require("node:events")},88849:e=>{"use strict";e.exports=require("node:http")},42725:e=>{"use strict";e.exports=require("node:http2")},87503:e=>{"use strict";e.exports=require("node:net")},38846:e=>{"use strict";e.exports=require("node:perf_hooks")},39630:e=>{"use strict";e.exports=require("node:querystring")},84492:e=>{"use strict";e.exports=require("node:stream")},31764:e=>{"use strict";e.exports=require("node:tls")},41041:e=>{"use strict";e.exports=require("node:url")},47261:e=>{"use strict";e.exports=require("node:util")},93746:e=>{"use strict";e.exports=require("node:util/types")},24086:e=>{"use strict";e.exports=require("node:worker_threads")},65628:e=>{"use strict";e.exports=require("node:zlib")},55686:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>s.a,__next_app__:()=>u,originalPathname:()=>p,pages:()=>c,routeModule:()=>f,tree:()=>d}),r(42253),r(33709),r(35866);var i=r(23191),n=r(88716),o=r(37922),s=r.n(o),a=r(95231),l={};for(let e in a)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>a[e]);r.d(t,l);let d=["",{children:["mobile",{children:["viewBenefitDetails",{children:["[benefitId]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,42253)),"C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\mobile\\viewBenefitDetails\\[benefitId]\\page.tsx"]}]},{}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,33709)),"C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,35866,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],c=["C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\mobile\\viewBenefitDetails\\[benefitId]\\page.tsx"],p="/mobile/viewBenefitDetails/[benefitId]/page",u={require:r,loadChunk:()=>Promise.resolve()},f=new i.AppPageRouteModule({definition:{kind:n.x.APP_PAGE,page:"/mobile/viewBenefitDetails/[benefitId]/page",pathname:"/mobile/viewBenefitDetails/[benefitId]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},90864:(e,t,r)=>{Promise.resolve().then(r.bind(r,21193))},76380:(e,t,r)=>{"use strict";r.d(t,{Z:()=>o});var i=r(51426),n=r(10326);let o=(0,i.Z)((0,n.jsx)("path",{d:"M19 19H5V5h7V3H5c-1.11 0-2 .9-2 2v14c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2v-7h-2zM14 3v2h3.59l-9.83 9.83 1.41 1.41L19 6.41V10h2V3z"}),"OpenInNew")},99207:(e,t,r)=>{"use strict";r.d(t,{Z:()=>x});var i=r(17577),n=r(41135),o=r(88634),s=r(44823),a=r(91703),l=r(13643),d=r(2791),c=r(73025),p=r(10326);let u=e=>{let{absolute:t,children:r,classes:i,flexItem:n,light:s,orientation:a,textAlign:l,variant:d}=e;return(0,o.Z)({root:["root",t&&"absolute",d,s&&"light","vertical"===a&&"vertical",n&&"flexItem",r&&"withChildren",r&&"vertical"===a&&"withChildrenVertical","right"===l&&"vertical"!==a&&"textAlignRight","left"===l&&"vertical"!==a&&"textAlignLeft"],wrapper:["wrapper","vertical"===a&&"wrapperVertical"]},c.V,i)},f=(0,a.default)("div",{name:"MuiDivider",slot:"Root",overridesResolver:(e,t)=>{let{ownerState:r}=e;return[t.root,r.absolute&&t.absolute,t[r.variant],r.light&&t.light,"vertical"===r.orientation&&t.vertical,r.flexItem&&t.flexItem,r.children&&t.withChildren,r.children&&"vertical"===r.orientation&&t.withChildrenVertical,"right"===r.textAlign&&"vertical"!==r.orientation&&t.textAlignRight,"left"===r.textAlign&&"vertical"!==r.orientation&&t.textAlignLeft]}})((0,l.Z)(({theme:e})=>({margin:0,flexShrink:0,borderWidth:0,borderStyle:"solid",borderColor:(e.vars||e).palette.divider,borderBottomWidth:"thin",variants:[{props:{absolute:!0},style:{position:"absolute",bottom:0,left:0,width:"100%"}},{props:{light:!0},style:{borderColor:e.vars?`rgba(${e.vars.palette.dividerChannel} / 0.08)`:(0,s.Fq)(e.palette.divider,.08)}},{props:{variant:"inset"},style:{marginLeft:72}},{props:{variant:"middle",orientation:"horizontal"},style:{marginLeft:e.spacing(2),marginRight:e.spacing(2)}},{props:{variant:"middle",orientation:"vertical"},style:{marginTop:e.spacing(1),marginBottom:e.spacing(1)}},{props:{orientation:"vertical"},style:{height:"100%",borderBottomWidth:0,borderRightWidth:"thin"}},{props:{flexItem:!0},style:{alignSelf:"stretch",height:"auto"}},{props:({ownerState:e})=>!!e.children,style:{display:"flex",textAlign:"center",border:0,borderTopStyle:"solid",borderLeftStyle:"solid","&::before, &::after":{content:'""',alignSelf:"center"}}},{props:({ownerState:e})=>e.children&&"vertical"!==e.orientation,style:{"&::before, &::after":{width:"100%",borderTop:`thin solid ${(e.vars||e).palette.divider}`,borderTopStyle:"inherit"}}},{props:({ownerState:e})=>"vertical"===e.orientation&&e.children,style:{flexDirection:"column","&::before, &::after":{height:"100%",borderLeft:`thin solid ${(e.vars||e).palette.divider}`,borderLeftStyle:"inherit"}}},{props:({ownerState:e})=>"right"===e.textAlign&&"vertical"!==e.orientation,style:{"&::before":{width:"90%"},"&::after":{width:"10%"}}},{props:({ownerState:e})=>"left"===e.textAlign&&"vertical"!==e.orientation,style:{"&::before":{width:"10%"},"&::after":{width:"90%"}}}]}))),h=(0,a.default)("span",{name:"MuiDivider",slot:"Wrapper",overridesResolver:(e,t)=>{let{ownerState:r}=e;return[t.wrapper,"vertical"===r.orientation&&t.wrapperVertical]}})((0,l.Z)(({theme:e})=>({display:"inline-block",paddingLeft:`calc(${e.spacing(1)} * 1.2)`,paddingRight:`calc(${e.spacing(1)} * 1.2)`,whiteSpace:"nowrap",variants:[{props:{orientation:"vertical"},style:{paddingTop:`calc(${e.spacing(1)} * 1.2)`,paddingBottom:`calc(${e.spacing(1)} * 1.2)`}}]}))),g=i.forwardRef(function(e,t){let r=(0,d.i)({props:e,name:"MuiDivider"}),{absolute:i=!1,children:o,className:s,orientation:a="horizontal",component:l=o||"vertical"===a?"div":"hr",flexItem:c=!1,light:g=!1,role:x="hr"!==l?"separator":void 0,textAlign:m="center",variant:b="fullWidth",...v}=r,y={...r,absolute:i,component:l,flexItem:c,light:g,orientation:a,role:x,textAlign:m,variant:b},w=u(y);return(0,p.jsx)(f,{as:l,className:(0,n.Z)(w.root,s),role:x,ref:t,ownerState:y,"aria-orientation":"separator"===x&&("hr"!==l||"vertical"===a)?a:void 0,...v,children:o?(0,p.jsx)(h,{className:w.wrapper,ownerState:y,children:o}):null})});g&&(g.muiSkipListHighlight=!0);let x=g},73025:(e,t,r)=>{"use strict";r.d(t,{V:()=>o,Z:()=>s});var i=r(71685),n=r(97898);function o(e){return(0,n.ZP)("MuiDivider",e)}let s=(0,i.Z)("MuiDivider",["root","absolute","fullWidth","inset","middle","flexItem","light","vertical","withChildren","withChildrenVertical","textAlignRight","textAlignLeft","wrapper","wrapperVertical"])},21193:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>y});var i=r(10326),n=r(17577),o=r(31870),s=r(69058),a=r(35047),l=r(6283),d=r(25609),c=r(16027),p=r(98139),u=r(42265),f=r(25842),h=r(25748),g=r(32049),x=r(76380),m=r(43058),b=r(41014),v=r(94638);let y=(0,b.Z)(()=>{(0,a.useRouter)();let e=(0,o.T)(),{benefitId:t}=(0,a.useParams)(),r=(0,f.v9)(e=>(0,g.MP)(e)),b=(0,o.C)(e=>e.benefits.documentsPerBenefit),y=(0,o.C)(e=>e.benefits.viewableDocuments),w=(0,f.v9)(e=>(0,h.d8)(e,t)),j=(0,o.C)(e=>e.benefits.loadingDocuments);(0,n.useEffect)(()=>{""!==t&&(0,s.v0)(e,t,r,"view_benefits")},[t,r,e]);let q=(e,t)=>{window.open(`https://api.benosphere.com/benefits/document?objectKey=${e}&companyId=${t}`,"_blank")};return i.jsx(m.Z,{children:(0,i.jsxs)(l.Z,{sx:{bgcolor:"#F5F6F8",height:"100vh",padding:"32px",overflow:"auto"},children:[i.jsx(d.Z,{sx:{fontWeight:800,fontSize:"42px",mb:0},children:(0,v.dA)(w?.benefit?.subType||"")}),i.jsx(d.Z,{variant:"body1",sx:{color:"#6c757d",mb:6,fontSize:"16px"},children:"You can find all your health insurance details here, including coverage options, policy documents, and claim information."}),(0,i.jsxs)(c.ZP,{container:!0,spacing:3,alignItems:"flex-start",children:[(0,i.jsxs)(c.ZP,{item:!0,xs:12,children:[i.jsx(d.Z,{sx:{mb:3,fontWeight:700,fontSize:"24px"},children:"☕ Documents"}),0===b.documents.length?i.jsx(l.Z,{sx:{display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",minHeight:"150px",borderRadius:"8px",border:"2px dashed #e0e0e0",bgcolor:"#f9f9f9",p:4,textAlign:"left",maxWidth:"400px"},children:i.jsx(d.Z,{variant:"body1",sx:{color:"#6c757d",fontSize:"1rem"},children:"No documents available at the moment."})}):i.jsx(l.Z,{sx:{display:"grid",gridTemplateColumns:"repeat(auto-fill, minmax(160px, 1fr))",gap:"20px"},children:b.documents.map((e,t)=>{let n=["linear-gradient(135deg, #6a11cb 0%, #2575fc 100%)","linear-gradient(135deg, #ff7e5f 0%, #feb47b 100%)","linear-gradient(135deg, #43cea2 0%, #185a9d 100%)","linear-gradient(135deg, #ffafbd 0%, #ffc3a0 100%)","linear-gradient(135deg, #00c6ff 0%, #0072ff 100%)"],o=n[t%n.length],s=y.find(t=>t.documentObjectKey===e);return i.jsx(l.Z,{sx:{position:"relative",width:"100%",height:"auto",display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center"},children:i.jsx(l.Z,{sx:{width:"100%",minHeight:"200px",borderRadius:"12px",overflow:"hidden",boxShadow:"0px 4px 12px rgba(0, 0, 0, 0.1)",position:"relative",display:"flex",alignItems:"center",justifyContent:"center",background:o,color:"#ffffff",cursor:"pointer"},onClick:()=>{s?.document&&(console.log("viewableDocument ===>",s),q(s.documentObjectKey,r))},children:j.includes(e)||!s?i.jsx(p.Z,{}):(0,i.jsxs)(l.Z,{children:[i.jsx(d.Z,{sx:{fontSize:"16px",fontWeight:"bold",textAlign:"center",padding:"8px"},children:s?.originalFileName||"Document Preview"}),i.jsx(x.Z,{sx:{position:"absolute",top:8,right:8,color:"#ffffff",cursor:"pointer",height:20,width:20},onClick:()=>q(s?.documentObjectKey,r)})]})})},e)})})]}),(0,i.jsxs)(c.ZP,{item:!0,xs:12,children:[i.jsx(d.Z,{sx:{mb:3,fontWeight:700,mt:10,fontSize:"17px"},children:"Other helpful links"}),i.jsx(l.Z,{sx:{borderRadius:"12px",width:"100%",maxWidth:"400px",mx:"auto"},children:0===b.links.length?i.jsx(l.Z,{sx:{display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",minHeight:"150px",borderRadius:"8px",border:"2px dashed #e0e0e0",bgcolor:"#f9f9f9",py:4,textAlign:"left",width:"100%",maxWidth:"400px",mx:"auto"},children:i.jsx(d.Z,{variant:"body1",sx:{color:"#6c757d",fontSize:"16px"},children:"No links available right now."})}):i.jsx(l.Z,{sx:{display:"flex",flexDirection:"column",gap:2},children:b.links.map((e,t)=>(0,i.jsxs)(l.Z,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",width:"100%"},children:[i.jsx(d.Z,{component:"a",href:e.startsWith("http")?e:`https://${e}`,target:"_blank",rel:"noopener noreferrer",sx:{color:"#1A7ECF",textDecoration:"none",fontWeight:500,fontSize:"16px",overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap",maxWidth:"80%"},children:e||`Link ${t+1}`}),i.jsx(u.Z,{onClick:()=>{console.log("link ===>",e),window.open(e.startsWith("http")?e:`https://${e}`,"_blank")},sx:{minWidth:0,padding:0},children:i.jsx(x.Z,{sx:{color:"#6c757d",marginLeft:2}})})]},t))})})]})]})]})})})},43058:(e,t,r)=>{"use strict";r.d(t,{Z:()=>p});var i=r(10326),n=r(17577),o=r(22758),s=r(35047),a=r(31870);r(32049),r(94638);var l=r(98139),d=r(6283);let c=()=>/Mobi|Android/i.test(navigator.userAgent),p=({children:e})=>{let{user:t,loading:r}=(0,o.a)(),p=(0,s.useRouter)(),u=(0,s.usePathname)(),f=(0,a.T)(),[h,g]=(0,n.useState)(!1),x=(0,a.C)(e=>e.user.userProfile);return((0,n.useEffect)(()=>{},[f,x.name]),(0,n.useEffect)(()=>{console.log("ProtectedRoute useEffect triggered"),console.log("Current user: ",t),console.log("Loading state: ",r),console.log("Current user details: ",x),r||t||(console.log("User not authenticated, redirecting to home"),g(!1),p.push("/")),!r&&x.companyId&&""===x.companyId&&(console.log("Waiting to retrieve company details"),g(!1)),!r&&x.companyId&&""!==x.companyId&&(console.log("User found, rendering children"),g(!0)),c()&&!u.startsWith("/mobile")&&(console.log(`Redirecting to mobile version of ${u}`),p.push(`/mobile${u}`))},[t,r,x,p,u]),h)?t?i.jsx(i.Fragment,{children:e}):null:i.jsx(d.Z,{sx:{display:"flex",justifyContent:"center",alignItems:"center",height:"100vh",bgcolor:"#f6f8fc"},children:i.jsx(l.Z,{})})}},69058:(e,t,r)=>{"use strict";r.d(t,{$t:()=>c,SS:()=>u,Y0:()=>s,cd:()=>p,fH:()=>f,mH:()=>h,ov:()=>d,v0:()=>a});var i=r(53148),n=r(25748),o=r(94638);async function s(e,t,r){try{let o=await (0,i.A_)("/benefits/benefit-by-type",{companyId:t,type:r});o&&o.benefits?(console.log("GET BENEFITS FOR TYPE RESPONSE: ",o.benefits),e((0,n.oQ)({benefitType:r,benefits:o.benefits})),e((0,n.nM)("Benefits fetched successfully"))):(console.error("Invalid response format:",o),e((0,n.nM)("Failed to fetch benefits")))}catch(t){console.error("Error fetching benefits:",t),e((0,n.nM)("Error fetching benefits"))}}async function a(e,t,r,o){let s={benefitId:t,page:o};console.log("data",s);let a=await (0,i.A_)("/benefits/one-benefit",s),d={...a,benefitId:t};for(let t of(e((0,n.F5)(d)),a.documents)){let i=decodeURIComponent(t.split("_____")[1]);l(e,t,r,i)}}async function l(e,t,r,o){let s={objectKey:t,companyId:r};console.log("data",s);let a=await (0,i.$R)("/benefits/document",s);if(console.log("VIEW BENEFIT RESPONSE: ",a),a){let r=new Blob([a],{type:"application/pdf"}),i=URL.createObjectURL(r);e((0,n.D7)([{documentObjectKey:t,document:i,originalFileName:o}]))}}let d=async(e,t,r,n,a)=>200===(await (0,i.j0)("/benefits/toggle-benefits/",{benefitId:r,companyId:t,isActivated:n})).status&&(await s(e,t,a),await (0,o.N)(e,t),!0);async function c(e,t,r,o){let s=new FormData;o.forEach(e=>s.append("documents",e)),s.append("companyId",r),s.append("benefitId",t);try{console.log("uploadDocument",s);let a=await (0,i.iG)("/benefits/add/document",s),d=a.data.objectKeys;if(console.log("newObjectKeys",d),200===a.status)return d.forEach((i,s)=>{let a=o[s].name;e((0,n.H_)({benefitId:t,document:i})),l(e,i,r,a)}),e((0,n.nM)("Document added successfully")),!0;return console.error("Error adding document:",a.data.error),e((0,n.nM)("Failed to add document")),!1}catch(t){return console.error("Error adding document:",t),e((0,n.nM)("Error adding document")),!1}}async function p(e,t,r,o){try{let s=await (0,i.j0)("/benefits/delete/document",{benefitId:t,companyId:r,objectKey:o});if(200===s.status)return e((0,n.iH)({benefitId:t,document:o})),e((0,n.nM)("Document deleted successfully")),!0;return console.error("Error deleting document:",s.data.error),e((0,n.nM)("Failed to delete document")),!1}catch(t){return console.error("Error deleting document:",t),e((0,n.nM)("Error deleting document")),!1}}async function u(e,t,r,o){try{let s=await (0,i.j0)("/benefits/add/links",{benefitId:t,companyId:r,urls:[o]});if(200===s.status)return e((0,n.MJ)({benefitId:t,link:o})),e((0,n.nM)("Link added successfully")),!0;return console.error("Error adding link:",s.data.error),e((0,n.nM)("Failed to add link")),!1}catch(t){return console.error("Error adding link:",t),e((0,n.nM)("Error adding link")),!1}}async function f(e,t,r,o){try{let s=await (0,i.j0)("/benefits/delete/link",{benefitId:t,companyId:r,urls:o});if(200===s.status)return e((0,n.Yw)({benefitId:t,link:o})),e((0,n.nM)("Link deleted successfully")),!0;return console.error("Error deleting link:",s.data.error),e((0,n.nM)("Failed to delete link")),!1}catch(t){return console.error("Error deleting link:",t),e((0,n.nM)("Error deleting link")),!1}}async function h(e,t){let r=new FormData;r.append("logoImage",t);try{console.log("uploading company logo",r);let t=await (0,i.iG)("/admin/update-company-logo",r);if(await (0,o.aK)(e),200===t.status)return console.log("Company logo updated successfully"),e((0,n.nM)("Company logo updated successfully")),!0;return console.error("Error updating company logo:",t.data.error),e((0,n.nM)("Failed to update company logo")),!1}catch(t){return console.error("Error updating company logo:",t),e((0,n.nM)("Error updating company logo")),!1}}},42253:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i});let i=(0,r(68570).createProxy)(String.raw`C:\beno\project_dev\qharmony-frontend-v1\src\app\mobile\viewBenefitDetails\[benefitId]\page.tsx#default`)}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),i=t.X(0,[8948,1183,6621,9066,1999,3253,928,8097,8522,3141,6027,8705,576,401,43],()=>r(55686));module.exports=i})();