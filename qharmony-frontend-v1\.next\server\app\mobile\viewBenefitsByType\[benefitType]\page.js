(()=>{var e={};e.id=5866,e.ids=[5866],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},27790:e=>{"use strict";e.exports=require("assert")},84770:e=>{"use strict";e.exports=require("crypto")},17702:e=>{"use strict";e.exports=require("events")},92048:e=>{"use strict";e.exports=require("fs")},32615:e=>{"use strict";e.exports=require("http")},35240:e=>{"use strict";e.exports=require("https")},19801:e=>{"use strict";e.exports=require("os")},55315:e=>{"use strict";e.exports=require("path")},76162:e=>{"use strict";e.exports=require("stream")},74026:e=>{"use strict";e.exports=require("string_decoder")},74175:e=>{"use strict";e.exports=require("tty")},17360:e=>{"use strict";e.exports=require("url")},21764:e=>{"use strict";e.exports=require("util")},71568:e=>{"use strict";e.exports=require("zlib")},98061:e=>{"use strict";e.exports=require("node:assert")},92761:e=>{"use strict";e.exports=require("node:async_hooks")},72254:e=>{"use strict";e.exports=require("node:buffer")},40027:e=>{"use strict";e.exports=require("node:console")},6005:e=>{"use strict";e.exports=require("node:crypto")},65714:e=>{"use strict";e.exports=require("node:diagnostics_channel")},15673:e=>{"use strict";e.exports=require("node:events")},88849:e=>{"use strict";e.exports=require("node:http")},42725:e=>{"use strict";e.exports=require("node:http2")},87503:e=>{"use strict";e.exports=require("node:net")},38846:e=>{"use strict";e.exports=require("node:perf_hooks")},39630:e=>{"use strict";e.exports=require("node:querystring")},84492:e=>{"use strict";e.exports=require("node:stream")},31764:e=>{"use strict";e.exports=require("node:tls")},41041:e=>{"use strict";e.exports=require("node:url")},47261:e=>{"use strict";e.exports=require("node:util")},93746:e=>{"use strict";e.exports=require("node:util/types")},24086:e=>{"use strict";e.exports=require("node:worker_threads")},65628:e=>{"use strict";e.exports=require("node:zlib")},40632:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>s.a,__next_app__:()=>p,originalPathname:()=>u,pages:()=>c,routeModule:()=>f,tree:()=>d}),r(43955),r(33709),r(35866);var i=r(23191),o=r(88716),n=r(37922),s=r.n(n),a=r(95231),l={};for(let e in a)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>a[e]);r.d(t,l);let d=["",{children:["mobile",{children:["viewBenefitsByType",{children:["[benefitType]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,43955)),"C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\mobile\\viewBenefitsByType\\[benefitType]\\page.tsx"]}]},{}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,33709)),"C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,35866,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],c=["C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\mobile\\viewBenefitsByType\\[benefitType]\\page.tsx"],u="/mobile/viewBenefitsByType/[benefitType]/page",p={require:r,loadChunk:()=>Promise.resolve()},f=new i.AppPageRouteModule({definition:{kind:o.x.APP_PAGE,page:"/mobile/viewBenefitsByType/[benefitType]/page",pathname:"/mobile/viewBenefitsByType/[benefitType]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},76359:(e,t,r)=>{Promise.resolve().then(r.bind(r,79438))},4766:(e,t,r)=>{"use strict";r.d(t,{Z:()=>h});var i=r(17577),o=r(41135),n=r(88634),s=r(91703),a=r(2791),l=r(71685),d=r(97898);function c(e){return(0,d.ZP)("MuiCardContent",e)}(0,l.Z)("MuiCardContent",["root"]);var u=r(10326);let p=e=>{let{classes:t}=e;return(0,n.Z)({root:["root"]},c,t)},f=(0,s.default)("div",{name:"MuiCardContent",slot:"Root",overridesResolver:(e,t)=>t.root})({padding:16,"&:last-child":{paddingBottom:24}}),h=i.forwardRef(function(e,t){let r=(0,a.i)({props:e,name:"MuiCardContent"}),{className:i,component:n="div",...s}=r,l={...r,component:n},d=p(l);return(0,u.jsx)(f,{as:n,className:(0,o.Z)(d.root,i),ownerState:l,ref:t,...s})})},34039:(e,t,r)=>{"use strict";r.d(t,{Z:()=>g});var i=r(17577),o=r(41135),n=r(88634),s=r(91703),a=r(2791),l=r(89178),d=r(71685),c=r(97898);function u(e){return(0,c.ZP)("MuiCard",e)}(0,d.Z)("MuiCard",["root"]);var p=r(10326);let f=e=>{let{classes:t}=e;return(0,n.Z)({root:["root"]},u,t)},h=(0,s.default)(l.Z,{name:"MuiCard",slot:"Root",overridesResolver:(e,t)=>t.root})({overflow:"hidden"}),g=i.forwardRef(function(e,t){let r=(0,a.i)({props:e,name:"MuiCard"}),{className:i,raised:n=!1,...s}=r,l={...r,raised:n},d=f(l);return(0,p.jsx)(h,{className:(0,o.Z)(d.root,i),elevation:n?8:void 0,ref:t,ownerState:l,...s})})},99207:(e,t,r)=>{"use strict";r.d(t,{Z:()=>m});var i=r(17577),o=r(41135),n=r(88634),s=r(44823),a=r(91703),l=r(13643),d=r(2791),c=r(73025),u=r(10326);let p=e=>{let{absolute:t,children:r,classes:i,flexItem:o,light:s,orientation:a,textAlign:l,variant:d}=e;return(0,n.Z)({root:["root",t&&"absolute",d,s&&"light","vertical"===a&&"vertical",o&&"flexItem",r&&"withChildren",r&&"vertical"===a&&"withChildrenVertical","right"===l&&"vertical"!==a&&"textAlignRight","left"===l&&"vertical"!==a&&"textAlignLeft"],wrapper:["wrapper","vertical"===a&&"wrapperVertical"]},c.V,i)},f=(0,a.default)("div",{name:"MuiDivider",slot:"Root",overridesResolver:(e,t)=>{let{ownerState:r}=e;return[t.root,r.absolute&&t.absolute,t[r.variant],r.light&&t.light,"vertical"===r.orientation&&t.vertical,r.flexItem&&t.flexItem,r.children&&t.withChildren,r.children&&"vertical"===r.orientation&&t.withChildrenVertical,"right"===r.textAlign&&"vertical"!==r.orientation&&t.textAlignRight,"left"===r.textAlign&&"vertical"!==r.orientation&&t.textAlignLeft]}})((0,l.Z)(({theme:e})=>({margin:0,flexShrink:0,borderWidth:0,borderStyle:"solid",borderColor:(e.vars||e).palette.divider,borderBottomWidth:"thin",variants:[{props:{absolute:!0},style:{position:"absolute",bottom:0,left:0,width:"100%"}},{props:{light:!0},style:{borderColor:e.vars?`rgba(${e.vars.palette.dividerChannel} / 0.08)`:(0,s.Fq)(e.palette.divider,.08)}},{props:{variant:"inset"},style:{marginLeft:72}},{props:{variant:"middle",orientation:"horizontal"},style:{marginLeft:e.spacing(2),marginRight:e.spacing(2)}},{props:{variant:"middle",orientation:"vertical"},style:{marginTop:e.spacing(1),marginBottom:e.spacing(1)}},{props:{orientation:"vertical"},style:{height:"100%",borderBottomWidth:0,borderRightWidth:"thin"}},{props:{flexItem:!0},style:{alignSelf:"stretch",height:"auto"}},{props:({ownerState:e})=>!!e.children,style:{display:"flex",textAlign:"center",border:0,borderTopStyle:"solid",borderLeftStyle:"solid","&::before, &::after":{content:'""',alignSelf:"center"}}},{props:({ownerState:e})=>e.children&&"vertical"!==e.orientation,style:{"&::before, &::after":{width:"100%",borderTop:`thin solid ${(e.vars||e).palette.divider}`,borderTopStyle:"inherit"}}},{props:({ownerState:e})=>"vertical"===e.orientation&&e.children,style:{flexDirection:"column","&::before, &::after":{height:"100%",borderLeft:`thin solid ${(e.vars||e).palette.divider}`,borderLeftStyle:"inherit"}}},{props:({ownerState:e})=>"right"===e.textAlign&&"vertical"!==e.orientation,style:{"&::before":{width:"90%"},"&::after":{width:"10%"}}},{props:({ownerState:e})=>"left"===e.textAlign&&"vertical"!==e.orientation,style:{"&::before":{width:"10%"},"&::after":{width:"90%"}}}]}))),h=(0,a.default)("span",{name:"MuiDivider",slot:"Wrapper",overridesResolver:(e,t)=>{let{ownerState:r}=e;return[t.wrapper,"vertical"===r.orientation&&t.wrapperVertical]}})((0,l.Z)(({theme:e})=>({display:"inline-block",paddingLeft:`calc(${e.spacing(1)} * 1.2)`,paddingRight:`calc(${e.spacing(1)} * 1.2)`,whiteSpace:"nowrap",variants:[{props:{orientation:"vertical"},style:{paddingTop:`calc(${e.spacing(1)} * 1.2)`,paddingBottom:`calc(${e.spacing(1)} * 1.2)`}}]}))),g=i.forwardRef(function(e,t){let r=(0,d.i)({props:e,name:"MuiDivider"}),{absolute:i=!1,children:n,className:s,orientation:a="horizontal",component:l=n||"vertical"===a?"div":"hr",flexItem:c=!1,light:g=!1,role:m="hr"!==l?"separator":void 0,textAlign:x="center",variant:b="fullWidth",...y}=r,v={...r,absolute:i,component:l,flexItem:c,light:g,orientation:a,role:m,textAlign:x,variant:b},w=p(v);return(0,u.jsx)(f,{as:l,className:(0,o.Z)(w.root,s),role:m,ref:t,ownerState:v,"aria-orientation":"separator"===m&&("hr"!==l||"vertical"===a)?a:void 0,...y,children:n?(0,u.jsx)(h,{className:w.wrapper,ownerState:v,children:n}):null})});g&&(g.muiSkipListHighlight=!0);let m=g},73025:(e,t,r)=>{"use strict";r.d(t,{V:()=>n,Z:()=>s});var i=r(71685),o=r(97898);function n(e){return(0,o.ZP)("MuiDivider",e)}let s=(0,i.Z)("MuiDivider",["root","absolute","fullWidth","inset","middle","flexItem","light","vertical","withChildren","withChildrenVertical","textAlignRight","textAlignLeft","wrapper","wrapperVertical"])},79438:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>j});var i=r(10326),o=r(17577),n=r(31870),s=r(69058),a=r(32049),l=r(25748),d=r(6283),c=r(25609),u=r(98139),p=r(16027),f=r(34039),h=r(4766),g=r(42265),m=r(35047),x=r(25842),b=r(43058),y=r(18742),v=r(41014),w=r(94638);let j=(0,v.Z)(()=>{let{benefitType:e}=(0,m.useParams)(),t=decodeURIComponent(e),r=(0,n.T)(),v=(0,m.useRouter)(),j=(0,x.v9)(e=>(0,a.MP)(e)),q=(0,n.C)(e=>e.user.userProfile),C=(0,x.v9)(e=>(0,l.oT)(e,t));(0,o.useEffect)(()=>{""!==t&&""!==j&&(r((0,l.Yb)()),(0,s.Y0)(r,j,t))},[t,j,r]);let E=e=>{r((0,a.Re)(e)),v.push(`/viewBenefitDetails/${e}`)};return i.jsx(b.Z,{children:(0,i.jsxs)(d.Z,{sx:{flexGrow:1,minHeight:"100vh",width:"100%",bgcolor:"#F5F6FA",padding:4},children:[(0,i.jsxs)(c.Z,{variant:"h4",sx:{fontWeight:"bold",mb:2},children:["Hey ",q.name.replace(/\b\w/g,e=>e.toUpperCase()),","]}),i.jsx(c.Z,{variant:"body1",sx:{color:"#6c757d",mb:4},children:"Explore your benefits now—tap to dive in!"}),i.jsx(d.Z,{sx:{mb:4},children:null===C?i.jsx(d.Z,{sx:{display:"flex",flexDirection:"column",alignItems:"flex-start",justifyContent:"center",height:"150px"},children:i.jsx(u.Z,{size:50,sx:{color:"#6c757d",mb:2}})}):0===C.benefits.filter(e=>e.isActivated).length?(0,i.jsxs)(d.Z,{sx:{display:"flex",flexDirection:"column",alignItems:"flex-start",justifyContent:"center",height:"150px",borderRadius:"12px",backgroundColor:"#F5F6FA",textAlign:"left"},children:[i.jsx(c.Z,{variant:"h6",sx:{fontWeight:"bold",color:"#6c757d",mb:2},children:"No benefits available"}),i.jsx(c.Z,{variant:"body2",sx:{color:"#9E9E9E"},children:"There are currently no active benefits for this type."})]}):i.jsx(p.ZP,{container:!0,spacing:3,children:C.benefits.filter(e=>e.isActivated).map((e,t)=>i.jsx(p.ZP,{item:!0,xs:12,children:(0,i.jsxs)(f.Z,{sx:{height:"100%",display:"flex",flexDirection:"column",alignItems:"flex-start",justifyContent:"flex-start",borderRadius:"16px",bgcolor:"#ffffff",padding:3,boxShadow:"none"},children:[(0,y.DD)(e.subType,{marginBottom:15,fontSize:35,color:"#606267"}),(0,i.jsxs)(h.Z,{sx:{flexGrow:1,padding:0,width:"100%"},children:[i.jsx(c.Z,{variant:"h6",sx:{fontWeight:600,textAlign:"left"},children:(0,w.dA)(e.subType)}),(0,i.jsxs)(c.Z,{variant:"body2",sx:{color:"#6c757d",marginTop:1,textAlign:"left"},children:["Check your ",(0,w.dA)(e.subType)," benefits"]})]}),i.jsx(d.Z,{sx:{paddingTop:2,width:"100%"},children:i.jsx(g.Z,{variant:"contained",fullWidth:!0,onClick:()=>E(e._id),sx:{textTransform:"none",borderRadius:"8px",bgcolor:"#e8e8e8",color:"black",boxShadow:"none",padding:"10px 0","&:hover":{backgroundColor:"#d8d8d8",boxShadow:"none"}},children:"View"})})]})},e._id))})})]})})})},43058:(e,t,r)=>{"use strict";r.d(t,{Z:()=>u});var i=r(10326),o=r(17577),n=r(22758),s=r(35047),a=r(31870);r(32049),r(94638);var l=r(98139),d=r(6283);let c=()=>/Mobi|Android/i.test(navigator.userAgent),u=({children:e})=>{let{user:t,loading:r}=(0,n.a)(),u=(0,s.useRouter)(),p=(0,s.usePathname)(),f=(0,a.T)(),[h,g]=(0,o.useState)(!1),m=(0,a.C)(e=>e.user.userProfile);return((0,o.useEffect)(()=>{},[f,m.name]),(0,o.useEffect)(()=>{console.log("ProtectedRoute useEffect triggered"),console.log("Current user: ",t),console.log("Loading state: ",r),console.log("Current user details: ",m),r||t||(console.log("User not authenticated, redirecting to home"),g(!1),u.push("/")),!r&&m.companyId&&""===m.companyId&&(console.log("Waiting to retrieve company details"),g(!1)),!r&&m.companyId&&""!==m.companyId&&(console.log("User found, rendering children"),g(!0)),c()&&!p.startsWith("/mobile")&&(console.log(`Redirecting to mobile version of ${p}`),u.push(`/mobile${p}`))},[t,r,m,u,p]),h)?t?i.jsx(i.Fragment,{children:e}):null:i.jsx(d.Z,{sx:{display:"flex",justifyContent:"center",alignItems:"center",height:"100vh",bgcolor:"#f6f8fc"},children:i.jsx(l.Z,{})})}},69058:(e,t,r)=>{"use strict";r.d(t,{$t:()=>c,SS:()=>p,Y0:()=>s,cd:()=>u,fH:()=>f,mH:()=>h,ov:()=>d,v0:()=>a});var i=r(53148),o=r(25748),n=r(94638);async function s(e,t,r){try{let n=await (0,i.A_)("/benefits/benefit-by-type",{companyId:t,type:r});n&&n.benefits?(console.log("GET BENEFITS FOR TYPE RESPONSE: ",n.benefits),e((0,o.oQ)({benefitType:r,benefits:n.benefits})),e((0,o.nM)("Benefits fetched successfully"))):(console.error("Invalid response format:",n),e((0,o.nM)("Failed to fetch benefits")))}catch(t){console.error("Error fetching benefits:",t),e((0,o.nM)("Error fetching benefits"))}}async function a(e,t,r,n){let s={benefitId:t,page:n};console.log("data",s);let a=await (0,i.A_)("/benefits/one-benefit",s),d={...a,benefitId:t};for(let t of(e((0,o.F5)(d)),a.documents)){let i=decodeURIComponent(t.split("_____")[1]);l(e,t,r,i)}}async function l(e,t,r,n){let s={objectKey:t,companyId:r};console.log("data",s);let a=await (0,i.$R)("/benefits/document",s);if(console.log("VIEW BENEFIT RESPONSE: ",a),a){let r=new Blob([a],{type:"application/pdf"}),i=URL.createObjectURL(r);e((0,o.D7)([{documentObjectKey:t,document:i,originalFileName:n}]))}}let d=async(e,t,r,o,a)=>200===(await (0,i.j0)("/benefits/toggle-benefits/",{benefitId:r,companyId:t,isActivated:o})).status&&(await s(e,t,a),await (0,n.N)(e,t),!0);async function c(e,t,r,n){let s=new FormData;n.forEach(e=>s.append("documents",e)),s.append("companyId",r),s.append("benefitId",t);try{console.log("uploadDocument",s);let a=await (0,i.iG)("/benefits/add/document",s),d=a.data.objectKeys;if(console.log("newObjectKeys",d),200===a.status)return d.forEach((i,s)=>{let a=n[s].name;e((0,o.H_)({benefitId:t,document:i})),l(e,i,r,a)}),e((0,o.nM)("Document added successfully")),!0;return console.error("Error adding document:",a.data.error),e((0,o.nM)("Failed to add document")),!1}catch(t){return console.error("Error adding document:",t),e((0,o.nM)("Error adding document")),!1}}async function u(e,t,r,n){try{let s=await (0,i.j0)("/benefits/delete/document",{benefitId:t,companyId:r,objectKey:n});if(200===s.status)return e((0,o.iH)({benefitId:t,document:n})),e((0,o.nM)("Document deleted successfully")),!0;return console.error("Error deleting document:",s.data.error),e((0,o.nM)("Failed to delete document")),!1}catch(t){return console.error("Error deleting document:",t),e((0,o.nM)("Error deleting document")),!1}}async function p(e,t,r,n){try{let s=await (0,i.j0)("/benefits/add/links",{benefitId:t,companyId:r,urls:[n]});if(200===s.status)return e((0,o.MJ)({benefitId:t,link:n})),e((0,o.nM)("Link added successfully")),!0;return console.error("Error adding link:",s.data.error),e((0,o.nM)("Failed to add link")),!1}catch(t){return console.error("Error adding link:",t),e((0,o.nM)("Error adding link")),!1}}async function f(e,t,r,n){try{let s=await (0,i.j0)("/benefits/delete/link",{benefitId:t,companyId:r,urls:n});if(200===s.status)return e((0,o.Yw)({benefitId:t,link:n})),e((0,o.nM)("Link deleted successfully")),!0;return console.error("Error deleting link:",s.data.error),e((0,o.nM)("Failed to delete link")),!1}catch(t){return console.error("Error deleting link:",t),e((0,o.nM)("Error deleting link")),!1}}async function h(e,t){let r=new FormData;r.append("logoImage",t);try{console.log("uploading company logo",r);let t=await (0,i.iG)("/admin/update-company-logo",r);if(await (0,n.aK)(e),200===t.status)return console.log("Company logo updated successfully"),e((0,o.nM)("Company logo updated successfully")),!0;return console.error("Error updating company logo:",t.data.error),e((0,o.nM)("Failed to update company logo")),!1}catch(t){return console.error("Error updating company logo:",t),e((0,o.nM)("Error updating company logo")),!1}}},43955:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i});let i=(0,r(68570).createProxy)(String.raw`C:\beno\project_dev\qharmony-frontend-v1\src\app\mobile\viewBenefitsByType\[benefitType]\page.tsx#default`)}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),i=t.X(0,[8948,1183,6621,9066,1999,3253,928,8097,8522,3141,6027,8705,576,401,43],()=>r(40632));module.exports=i})();