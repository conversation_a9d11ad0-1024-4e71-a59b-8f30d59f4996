(()=>{var e={};e.id=6042,e.ids=[6042],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},27790:e=>{"use strict";e.exports=require("assert")},84770:e=>{"use strict";e.exports=require("crypto")},17702:e=>{"use strict";e.exports=require("events")},92048:e=>{"use strict";e.exports=require("fs")},32615:e=>{"use strict";e.exports=require("http")},35240:e=>{"use strict";e.exports=require("https")},19801:e=>{"use strict";e.exports=require("os")},55315:e=>{"use strict";e.exports=require("path")},76162:e=>{"use strict";e.exports=require("stream")},74026:e=>{"use strict";e.exports=require("string_decoder")},74175:e=>{"use strict";e.exports=require("tty")},17360:e=>{"use strict";e.exports=require("url")},21764:e=>{"use strict";e.exports=require("util")},71568:e=>{"use strict";e.exports=require("zlib")},98061:e=>{"use strict";e.exports=require("node:assert")},92761:e=>{"use strict";e.exports=require("node:async_hooks")},72254:e=>{"use strict";e.exports=require("node:buffer")},40027:e=>{"use strict";e.exports=require("node:console")},6005:e=>{"use strict";e.exports=require("node:crypto")},65714:e=>{"use strict";e.exports=require("node:diagnostics_channel")},15673:e=>{"use strict";e.exports=require("node:events")},88849:e=>{"use strict";e.exports=require("node:http")},42725:e=>{"use strict";e.exports=require("node:http2")},87503:e=>{"use strict";e.exports=require("node:net")},38846:e=>{"use strict";e.exports=require("node:perf_hooks")},39630:e=>{"use strict";e.exports=require("node:querystring")},84492:e=>{"use strict";e.exports=require("node:stream")},31764:e=>{"use strict";e.exports=require("node:tls")},41041:e=>{"use strict";e.exports=require("node:url")},47261:e=>{"use strict";e.exports=require("node:util")},93746:e=>{"use strict";e.exports=require("node:util/types")},24086:e=>{"use strict";e.exports=require("node:worker_threads")},65628:e=>{"use strict";e.exports=require("node:zlib")},28787:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>p,originalPathname:()=>u,pages:()=>d,routeModule:()=>x,tree:()=>c}),r(48200),r(33709),r(35866);var s=r(23191),o=r(88716),i=r(37922),n=r.n(i),a=r(95231),l={};for(let e in a)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>a[e]);r.d(t,l);let c=["",{children:["notification-history",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,48200)),"C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\notification-history\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,33709)),"C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,35866,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],d=["C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\notification-history\\page.tsx"],u="/notification-history/page",p={require:r,loadChunk:()=>Promise.resolve()},x=new s.AppPageRouteModule({definition:{kind:o.x.APP_PAGE,page:"/notification-history/page",pathname:"/notification-history",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},79288:(e,t,r)=>{Promise.resolve().then(r.bind(r,93014))},33198:(e,t,r)=>{"use strict";r.d(t,{Z:()=>q});var s=r(17577),o=r(41135),i=r(88634),n=r(91703),a=r(13643),l=r(2791),c=r(51426),d=r(10326);let u=(0,c.Z)((0,d.jsx)("path",{d:"M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"}),"Person");var p=r(71685),x=r(97898);function h(e){return(0,x.ZP)("MuiAvatar",e)}(0,p.Z)("MuiAvatar",["root","colorDefault","circular","rounded","square","img","fallback"]);var f=r(31121);let g=e=>{let{classes:t,variant:r,colorDefault:s}=e;return(0,i.Z)({root:["root",r,s&&"colorDefault"],img:["img"],fallback:["fallback"]},h,t)},v=(0,n.default)("div",{name:"MuiAvatar",slot:"Root",overridesResolver:(e,t)=>{let{ownerState:r}=e;return[t.root,t[r.variant],r.colorDefault&&t.colorDefault]}})((0,a.Z)(({theme:e})=>({position:"relative",display:"flex",alignItems:"center",justifyContent:"center",flexShrink:0,width:40,height:40,fontFamily:e.typography.fontFamily,fontSize:e.typography.pxToRem(20),lineHeight:1,borderRadius:"50%",overflow:"hidden",userSelect:"none",variants:[{props:{variant:"rounded"},style:{borderRadius:(e.vars||e).shape.borderRadius}},{props:{variant:"square"},style:{borderRadius:0}},{props:{colorDefault:!0},style:{color:(e.vars||e).palette.background.default,...e.vars?{backgroundColor:e.vars.palette.Avatar.defaultBg}:{backgroundColor:e.palette.grey[400],...e.applyStyles("dark",{backgroundColor:e.palette.grey[600]})}}}]}))),m=(0,n.default)("img",{name:"MuiAvatar",slot:"Img",overridesResolver:(e,t)=>t.img})({width:"100%",height:"100%",textAlign:"center",objectFit:"cover",color:"transparent",textIndent:1e4}),y=(0,n.default)(u,{name:"MuiAvatar",slot:"Fallback",overridesResolver:(e,t)=>t.fallback})({width:"75%",height:"75%"}),q=s.forwardRef(function(e,t){let r=(0,l.i)({props:e,name:"MuiAvatar"}),{alt:i,children:n,className:a,component:c="div",slots:u={},slotProps:p={},imgProps:x,sizes:h,src:q,srcSet:b,variant:w="circular",...j}=r,Z=null,_={...r,component:c,variant:w},P=function({crossOrigin:e,referrerPolicy:t,src:r,srcSet:o}){let[i,n]=s.useState(!1);return s.useEffect(()=>{if(!r&&!o)return;n(!1);let s=!0,i=new Image;return i.onload=()=>{s&&n("loaded")},i.onerror=()=>{s&&n("error")},i.crossOrigin=e,i.referrerPolicy=t,i.src=r,o&&(i.srcset=o),()=>{s=!1}},[e,t,r,o]),i}({...x,..."function"==typeof p.img?p.img(_):p.img,src:q,srcSet:b}),k=q||b,S=k&&"error"!==P;_.colorDefault=!S,delete _.ownerState;let A=g(_),[C,R]=(0,f.Z)("img",{className:A.img,elementType:m,externalForwardedProps:{slots:u,slotProps:{img:{...x,...p.img}}},additionalProps:{alt:i,src:q,srcSet:b,sizes:h},ownerState:_});return Z=S?(0,d.jsx)(C,{...R}):n||0===n?n:k&&i?i[0]:(0,d.jsx)(y,{ownerState:_,className:A.fallback}),(0,d.jsx)(v,{as:c,className:(0,o.Z)(A.root,a),ref:t,...j,ownerState:_,children:Z})})},93014:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>m});var s=r(10326),o=r(17577),i=r(6283),n=r(25609),a=r(98956),l=r(89178),c=r(50219),d=r(23543),u=r(72163),p=r(36112),x=r(59777),h=r(12549),f=r(43058),g=r(35047),v=r(53148);let m=(0,h.Z)(()=>{let[e,t]=(0,o.useState)([]),[r,h]=(0,o.useState)(!1),m=(0,g.useRouter)();return(0,o.useEffect)(()=>{(async()=>{try{let e=await (0,v.A_)("/notifications");e.success&&(t(e.data),h(!1))}catch(e){e.response?.status===404?h(!0):console.error("Error fetching notifications:",e)}})()},[]),s.jsx(f.Z,{children:(0,s.jsxs)(i.Z,{sx:{bgcolor:"#F5F6FA",px:4,py:2,width:"100%",height:"100vh",overflow:"hidden"},children:[s.jsx(i.Z,{sx:{display:"flex",alignItems:"center",mb:4,mt:3},children:s.jsx(n.Z,{sx:{fontWeight:600,fontSize:"28px",color:"black",lineHeight:"34px",textAlign:"left"},children:"Notification History"})}),s.jsx(a.Z,{component:l.Z,sx:{maxHeight:"calc(100vh - 150px)",overflow:"auto"},children:(0,s.jsxs)(c.Z,{stickyHeader:!0,sx:{minWidth:650,tableLayout:"fixed"},"aria-label":"notification history table",children:[s.jsx(d.Z,{children:(0,s.jsxs)(u.Z,{children:[s.jsx(p.Z,{sx:{fontWeight:"bold",width:"10%"},children:"S.No"}),s.jsx(p.Z,{sx:{fontWeight:"bold",width:"20%"},children:"Notification ID"}),s.jsx(p.Z,{sx:{fontWeight:"bold",width:"50%"},children:"Message Text"}),s.jsx(p.Z,{sx:{fontWeight:"bold",width:"20%"},children:"Date"})]})}),s.jsx(x.Z,{children:r?s.jsx(u.Z,{children:s.jsx(p.Z,{colSpan:4,sx:{textAlign:"center",fontStyle:"italic"},children:"No notifications found"})}):e.map((e,t)=>(0,s.jsxs)(u.Z,{onClick:()=>m.push(`/notifications-analytics/${e._id}`),sx:{"&:last-child td, &:last-child th":{border:0},cursor:"pointer","&:hover":{backgroundColor:"rgba(0, 0, 0, 0.04)"}},children:[s.jsx(p.Z,{sx:{overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap"},children:t+1}),s.jsx(p.Z,{sx:{overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap"},children:e._id}),s.jsx(p.Z,{sx:{overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap"},children:e.message}),s.jsx(p.Z,{sx:{overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap"},children:new Date(e.createdAt).toLocaleDateString()})]},e._id))})]})})]})})})},48200:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(68570).createProxy)(String.raw`C:\beno\project_dev\qharmony-frontend-v1\src\app\notification-history\page.tsx#default`)}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[8948,1183,6621,9066,1999,3253,928,8097,8522,3141,2145,576,6305,401,2549],()=>r(28787));module.exports=s})();