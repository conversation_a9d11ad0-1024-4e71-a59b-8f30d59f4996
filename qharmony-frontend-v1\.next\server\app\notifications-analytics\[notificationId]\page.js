(()=>{var e={};e.id=1931,e.ids=[1931],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},27790:e=>{"use strict";e.exports=require("assert")},84770:e=>{"use strict";e.exports=require("crypto")},17702:e=>{"use strict";e.exports=require("events")},92048:e=>{"use strict";e.exports=require("fs")},32615:e=>{"use strict";e.exports=require("http")},35240:e=>{"use strict";e.exports=require("https")},19801:e=>{"use strict";e.exports=require("os")},55315:e=>{"use strict";e.exports=require("path")},76162:e=>{"use strict";e.exports=require("stream")},74026:e=>{"use strict";e.exports=require("string_decoder")},74175:e=>{"use strict";e.exports=require("tty")},17360:e=>{"use strict";e.exports=require("url")},21764:e=>{"use strict";e.exports=require("util")},71568:e=>{"use strict";e.exports=require("zlib")},98061:e=>{"use strict";e.exports=require("node:assert")},92761:e=>{"use strict";e.exports=require("node:async_hooks")},72254:e=>{"use strict";e.exports=require("node:buffer")},40027:e=>{"use strict";e.exports=require("node:console")},6005:e=>{"use strict";e.exports=require("node:crypto")},65714:e=>{"use strict";e.exports=require("node:diagnostics_channel")},15673:e=>{"use strict";e.exports=require("node:events")},88849:e=>{"use strict";e.exports=require("node:http")},42725:e=>{"use strict";e.exports=require("node:http2")},87503:e=>{"use strict";e.exports=require("node:net")},38846:e=>{"use strict";e.exports=require("node:perf_hooks")},39630:e=>{"use strict";e.exports=require("node:querystring")},84492:e=>{"use strict";e.exports=require("node:stream")},31764:e=>{"use strict";e.exports=require("node:tls")},41041:e=>{"use strict";e.exports=require("node:url")},47261:e=>{"use strict";e.exports=require("node:util")},93746:e=>{"use strict";e.exports=require("node:util/types")},24086:e=>{"use strict";e.exports=require("node:worker_threads")},65628:e=>{"use strict";e.exports=require("node:zlib")},99301:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>a.a,__next_app__:()=>p,originalPathname:()=>u,pages:()=>c,routeModule:()=>x,tree:()=>d}),r(262),r(33709),r(35866);var s=r(23191),i=r(88716),o=r(37922),a=r.n(o),n=r(95231),l={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>n[e]);r.d(t,l);let d=["",{children:["notifications-analytics",{children:["[notificationId]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,262)),"C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\notifications-analytics\\[notificationId]\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,33709)),"C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,35866,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],c=["C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\notifications-analytics\\[notificationId]\\page.tsx"],u="/notifications-analytics/[notificationId]/page",p={require:r,loadChunk:()=>Promise.resolve()},x=new s.AppPageRouteModule({definition:{kind:i.x.APP_PAGE,page:"/notifications-analytics/[notificationId]/page",pathname:"/notifications-analytics/[notificationId]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},29313:(e,t,r)=>{Promise.resolve().then(r.bind(r,15484))},33198:(e,t,r)=>{"use strict";r.d(t,{Z:()=>b});var s=r(17577),i=r(41135),o=r(88634),a=r(91703),n=r(13643),l=r(2791),d=r(51426),c=r(10326);let u=(0,d.Z)((0,c.jsx)("path",{d:"M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"}),"Person");var p=r(71685),x=r(97898);function h(e){return(0,x.ZP)("MuiAvatar",e)}(0,p.Z)("MuiAvatar",["root","colorDefault","circular","rounded","square","img","fallback"]);var f=r(31121);let m=e=>{let{classes:t,variant:r,colorDefault:s}=e;return(0,o.Z)({root:["root",r,s&&"colorDefault"],img:["img"],fallback:["fallback"]},h,t)},g=(0,a.default)("div",{name:"MuiAvatar",slot:"Root",overridesResolver:(e,t)=>{let{ownerState:r}=e;return[t.root,t[r.variant],r.colorDefault&&t.colorDefault]}})((0,n.Z)(({theme:e})=>({position:"relative",display:"flex",alignItems:"center",justifyContent:"center",flexShrink:0,width:40,height:40,fontFamily:e.typography.fontFamily,fontSize:e.typography.pxToRem(20),lineHeight:1,borderRadius:"50%",overflow:"hidden",userSelect:"none",variants:[{props:{variant:"rounded"},style:{borderRadius:(e.vars||e).shape.borderRadius}},{props:{variant:"square"},style:{borderRadius:0}},{props:{colorDefault:!0},style:{color:(e.vars||e).palette.background.default,...e.vars?{backgroundColor:e.vars.palette.Avatar.defaultBg}:{backgroundColor:e.palette.grey[400],...e.applyStyles("dark",{backgroundColor:e.palette.grey[600]})}}}]}))),v=(0,a.default)("img",{name:"MuiAvatar",slot:"Img",overridesResolver:(e,t)=>t.img})({width:"100%",height:"100%",textAlign:"center",objectFit:"cover",color:"transparent",textIndent:1e4}),y=(0,a.default)(u,{name:"MuiAvatar",slot:"Fallback",overridesResolver:(e,t)=>t.fallback})({width:"75%",height:"75%"}),b=s.forwardRef(function(e,t){let r=(0,l.i)({props:e,name:"MuiAvatar"}),{alt:o,children:a,className:n,component:d="div",slots:u={},slotProps:p={},imgProps:x,sizes:h,src:b,srcSet:w,variant:q="circular",...j}=r,Z=null,k={...r,component:d,variant:q},_=function({crossOrigin:e,referrerPolicy:t,src:r,srcSet:i}){let[o,a]=s.useState(!1);return s.useEffect(()=>{if(!r&&!i)return;a(!1);let s=!0,o=new Image;return o.onload=()=>{s&&a("loaded")},o.onerror=()=>{s&&a("error")},o.crossOrigin=e,o.referrerPolicy=t,o.src=r,i&&(o.srcset=i),()=>{s=!1}},[e,t,r,i]),o}({...x,..."function"==typeof p.img?p.img(k):p.img,src:b,srcSet:w}),P=b||w,A=P&&"error"!==_;k.colorDefault=!A,delete k.ownerState;let S=m(k),[R,C]=(0,f.Z)("img",{className:S.img,elementType:v,externalForwardedProps:{slots:u,slotProps:{img:{...x,...p.img}}},additionalProps:{alt:o,src:b,srcSet:w,sizes:h},ownerState:k});return Z=A?(0,c.jsx)(R,{...C}):a||0===a?a:P&&o?o[0]:(0,c.jsx)(y,{ownerState:k,className:S.fallback}),(0,c.jsx)(g,{as:d,className:(0,i.Z)(S.root,n),ref:t,...j,ownerState:k,children:Z})})},15484:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>w});var s=r(10326),i=r(17577),o=r(35047),a=r(6283),n=r(25609),l=r(42265),d=r(98956),c=r(89178),u=r(50219),p=r(23543),x=r(72163),h=r(36112),f=r(59777),m=r(43058),g=r(12549),v=r(53148),y=r(44099);let b=async e=>{try{let t=await y.Z.get(`${v._n}/download-notifications-analytics`,{params:{messageId:e},responseType:"blob"});if(t&&t.data){let e=new Blob([t.data],{type:"text/csv"}),r=document.createElement("a"),s=window.URL.createObjectURL(e);r.href=s,r.download="notification_analytics.csv",r.click(),window.URL.revokeObjectURL(s)}}catch(e){console.error("Failed to download CSV:",e)}},w=(0,g.Z)(()=>{(0,o.useRouter)();let e=(0,o.usePathname)().split("/").pop(),[t,r]=(0,i.useState)([]),[g,y]=(0,i.useState)([]),[w,q]=(0,i.useState)(!0);return(0,i.useEffect)(()=>{e&&(async()=>{q(!0);try{let t=await (0,v.A_)("/notifications-analytics",{messageId:e});if(t.success){let e=t.data.map(e=>({...e,date:new Date(e.date).toISOString().split("T")[0]})),s=new Set;e.forEach(e=>{Object.keys(e).forEach(e=>{["date","employer","message","messageDelivered","noActionTaken","total"].includes(e)||s.add(e)})}),y(Array.from(s)),r(e)}}catch(e){console.error("Failed to fetch analytics data:",e)}finally{q(!1)}})()},[e]),s.jsx(m.Z,{children:(0,s.jsxs)(a.Z,{sx:{bgcolor:"#F5F6FA",px:4,py:2,width:"100%",height:"100vh",overflow:"hidden"},children:[(0,s.jsxs)(a.Z,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",mb:3,mt:3},children:[s.jsx(n.Z,{sx:{fontWeight:600,fontSize:"28px",color:"black",lineHeight:"34px",textAlign:"left"},children:"Notification Analytics"}),s.jsx(l.Z,{variant:"contained",onClick:async()=>{e&&b(e)},sx:{textTransform:"none",borderRadius:"6px",bgcolor:"black",color:"white",boxShadow:"none",width:"140px",paddingY:"7px",paddingX:"16px",border:"1px solid #D2D2D2","&:hover":{backgroundColor:"rgba(0, 0, 0, 0.1)",boxShadow:"none"}},children:"Download CSV"})]}),s.jsx(d.Z,{component:c.Z,sx:{maxHeight:"calc(100vh - 140px)",overflow:"auto"},children:(0,s.jsxs)(u.Z,{stickyHeader:!0,sx:{minWidth:650},"aria-label":"analytics table",children:[s.jsx(p.Z,{children:(0,s.jsxs)(x.Z,{children:[s.jsx(h.Z,{sx:{fontWeight:"bold",width:"12%"},children:"Date"}),s.jsx(h.Z,{sx:{fontWeight:"bold",width:"15%"},children:"Employer"}),s.jsx(h.Z,{sx:{fontWeight:"bold",width:"25%"},children:"Message"}),s.jsx(h.Z,{sx:{fontWeight:"bold",width:"8%"},children:"Message Delivered"}),g.map(e=>s.jsx(h.Z,{sx:{fontWeight:"bold",width:"8%"},children:e},e)),s.jsx(h.Z,{sx:{fontWeight:"bold",width:"8%"},children:"No Action Taken"}),s.jsx(h.Z,{sx:{fontWeight:"bold",width:"8%"},children:"Total"})]})}),s.jsx(f.Z,{children:t.map((e,t)=>(0,s.jsxs)(x.Z,{sx:{"&:last-child td, &:last-child th":{border:0}},children:[s.jsx(h.Z,{sx:{width:"12%"},children:e.date}),s.jsx(h.Z,{sx:{width:"15%"},children:e.employer}),s.jsx(h.Z,{sx:{width:"25%"},children:e.message}),s.jsx(h.Z,{sx:{width:"8%"},children:e.messageDelivered}),g.map(t=>s.jsx(h.Z,{sx:{width:"8%"},children:e[t]||0},t)),s.jsx(h.Z,{sx:{width:"8%"},children:e.noActionTaken}),s.jsx(h.Z,{sx:{width:"8%"},children:e.total})]},t))})]})})]})})})},262:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(68570).createProxy)(String.raw`C:\beno\project_dev\qharmony-frontend-v1\src\app\notifications-analytics\[notificationId]\page.tsx#default`)}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[8948,1183,6621,9066,1999,3253,928,8097,8522,3141,2145,576,6305,401,2549],()=>r(99301));module.exports=s})();