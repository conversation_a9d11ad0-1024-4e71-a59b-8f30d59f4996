(()=>{var e={};e.id=7413,e.ids=[7413],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},27790:e=>{"use strict";e.exports=require("assert")},84770:e=>{"use strict";e.exports=require("crypto")},17702:e=>{"use strict";e.exports=require("events")},92048:e=>{"use strict";e.exports=require("fs")},32615:e=>{"use strict";e.exports=require("http")},35240:e=>{"use strict";e.exports=require("https")},19801:e=>{"use strict";e.exports=require("os")},55315:e=>{"use strict";e.exports=require("path")},76162:e=>{"use strict";e.exports=require("stream")},74026:e=>{"use strict";e.exports=require("string_decoder")},74175:e=>{"use strict";e.exports=require("tty")},17360:e=>{"use strict";e.exports=require("url")},21764:e=>{"use strict";e.exports=require("util")},71568:e=>{"use strict";e.exports=require("zlib")},98061:e=>{"use strict";e.exports=require("node:assert")},92761:e=>{"use strict";e.exports=require("node:async_hooks")},72254:e=>{"use strict";e.exports=require("node:buffer")},40027:e=>{"use strict";e.exports=require("node:console")},6005:e=>{"use strict";e.exports=require("node:crypto")},65714:e=>{"use strict";e.exports=require("node:diagnostics_channel")},15673:e=>{"use strict";e.exports=require("node:events")},88849:e=>{"use strict";e.exports=require("node:http")},42725:e=>{"use strict";e.exports=require("node:http2")},87503:e=>{"use strict";e.exports=require("node:net")},38846:e=>{"use strict";e.exports=require("node:perf_hooks")},39630:e=>{"use strict";e.exports=require("node:querystring")},84492:e=>{"use strict";e.exports=require("node:stream")},31764:e=>{"use strict";e.exports=require("node:tls")},41041:e=>{"use strict";e.exports=require("node:url")},47261:e=>{"use strict";e.exports=require("node:util")},93746:e=>{"use strict";e.exports=require("node:util/types")},24086:e=>{"use strict";e.exports=require("node:worker_threads")},65628:e=>{"use strict";e.exports=require("node:zlib")},52645:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>p,originalPathname:()=>u,pages:()=>c,routeModule:()=>m,tree:()=>d}),r(17822),r(33709),r(35866);var a=r(23191),i=r(88716),o=r(37922),n=r.n(o),s=r(95231),l={};for(let e in s)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>s[e]);r.d(t,l);let d=["",{children:["onboard",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,17822)),"C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\onboard\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,33709)),"C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,35866,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],c=["C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\onboard\\page.tsx"],u="/onboard/page",p={require:r,loadChunk:()=>Promise.resolve()},m=new a.AppPageRouteModule({definition:{kind:i.x.APP_PAGE,page:"/onboard/page",pathname:"/onboard",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},55444:(e,t,r)=>{Promise.resolve().then(r.bind(r,64141))},73025:(e,t,r)=>{"use strict";r.d(t,{V:()=>o,Z:()=>n});var a=r(71685),i=r(97898);function o(e){return(0,i.ZP)("MuiDivider",e)}let n=(0,a.Z)("MuiDivider",["root","absolute","fullWidth","inset","middle","flexItem","light","vertical","withChildren","withChildrenVertical","textAlignRight","textAlignLeft","wrapper","wrapperVertical"])},5394:(e,t,r)=>{"use strict";r.d(t,{Z:()=>w});var a=r(17577),i=r(41135),o=r(88634),n=r(65656),s=r(91703),l=r(13643),d=r(2791),c=r(25609),u=r(54641),p=r(71685),m=r(97898);function h(e){return(0,m.ZP)("MuiFormControlLabel",e)}let f=(0,p.Z)("MuiFormControlLabel",["root","labelPlacementStart","labelPlacementTop","labelPlacementBottom","disabled","label","error","required","asterisk"]);var g=r(39914),x=r(31121),y=r(10326);let b=e=>{let{classes:t,disabled:r,labelPlacement:a,error:i,required:n}=e,s={root:["root",r&&"disabled",`labelPlacement${(0,u.Z)(a)}`,i&&"error",n&&"required"],label:["label",r&&"disabled"],asterisk:["asterisk",i&&"error"]};return(0,o.Z)(s,h,t)},A=(0,s.default)("label",{name:"MuiFormControlLabel",slot:"Root",overridesResolver:(e,t)=>{let{ownerState:r}=e;return[{[`& .${f.label}`]:t.label},t.root,t[`labelPlacement${(0,u.Z)(r.labelPlacement)}`]]}})((0,l.Z)(({theme:e})=>({display:"inline-flex",alignItems:"center",cursor:"pointer",verticalAlign:"middle",WebkitTapHighlightColor:"transparent",marginLeft:-11,marginRight:16,[`&.${f.disabled}`]:{cursor:"default"},[`& .${f.label}`]:{[`&.${f.disabled}`]:{color:(e.vars||e).palette.text.disabled}},variants:[{props:{labelPlacement:"start"},style:{flexDirection:"row-reverse",marginRight:-11}},{props:{labelPlacement:"top"},style:{flexDirection:"column-reverse"}},{props:{labelPlacement:"bottom"},style:{flexDirection:"column"}},{props:({labelPlacement:e})=>"start"===e||"top"===e||"bottom"===e,style:{marginLeft:16}}]}))),v=(0,s.default)("span",{name:"MuiFormControlLabel",slot:"Asterisk",overridesResolver:(e,t)=>t.asterisk})((0,l.Z)(({theme:e})=>({[`&.${f.error}`]:{color:(e.vars||e).palette.error.main}}))),w=a.forwardRef(function(e,t){let r=(0,d.i)({props:e,name:"MuiFormControlLabel"}),{checked:o,className:s,componentsProps:l={},control:u,disabled:p,disableTypography:m,inputRef:h,label:f,labelPlacement:w="end",name:S,onChange:Z,required:j,slots:C={},slotProps:I={},value:k,...P}=r,E=(0,n.Z)(),R=p??u.props.disabled??E?.disabled,D=j??u.props.required,q={disabled:R,required:D};["checked","name","onChange","value","inputRef"].forEach(e=>{void 0===u.props[e]&&void 0!==r[e]&&(q[e]=r[e])});let N=(0,g.Z)({props:r,muiFormControl:E,states:["error"]}),M={...r,disabled:R,labelPlacement:w,required:D,error:N.error},B=b(M),O={slots:C,slotProps:{...l,...I}},[T,U]=(0,x.Z)("typography",{elementType:c.Z,externalForwardedProps:O,ownerState:M}),G=f;return null==G||G.type===c.Z||m||(G=(0,y.jsx)(T,{component:"span",...U,className:(0,i.Z)(B.label,U?.className),children:G})),(0,y.jsxs)(A,{className:(0,i.Z)(B.root,s),ownerState:M,ref:t,...P,children:[a.cloneElement(u,q),D?(0,y.jsxs)("div",{children:[G,(0,y.jsxs)(v,{ownerState:M,"aria-hidden":!0,className:B.asterisk,children:[" ","*"]})]}):G]})})},41598:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n,f:()=>o});var a=r(71685),i=r(97898);function o(e){return(0,i.ZP)("MuiListItemIcon",e)}let n=(0,a.Z)("MuiListItemIcon",["root","alignItemsFlexStart"])},37841:(e,t,r)=>{"use strict";r.d(t,{Z:()=>j});var a=r(17577),i=r(41135),o=r(88634),n=r(44823),s=r(27080),l=r(91703),d=r(13643),c=r(2791),u=r(92992),p=r(49006),m=r(69408),h=r(37382),f=r(73025),g=r(41598),x=r(25310),y=r(71685),b=r(97898);function A(e){return(0,b.ZP)("MuiMenuItem",e)}let v=(0,y.Z)("MuiMenuItem",["root","focusVisible","dense","disabled","divider","gutters","selected"]);var w=r(10326);let S=e=>{let{disabled:t,dense:r,divider:a,disableGutters:i,selected:n,classes:s}=e,l=(0,o.Z)({root:["root",r&&"dense",t&&"disabled",!i&&"gutters",a&&"divider",n&&"selected"]},A,s);return{...s,...l}},Z=(0,l.default)(p.Z,{shouldForwardProp:e=>(0,s.Z)(e)||"classes"===e,name:"MuiMenuItem",slot:"Root",overridesResolver:(e,t)=>{let{ownerState:r}=e;return[t.root,r.dense&&t.dense,r.divider&&t.divider,!r.disableGutters&&t.gutters]}})((0,d.Z)(({theme:e})=>({...e.typography.body1,display:"flex",justifyContent:"flex-start",alignItems:"center",position:"relative",textDecoration:"none",minHeight:48,paddingTop:6,paddingBottom:6,boxSizing:"border-box",whiteSpace:"nowrap","&:hover":{textDecoration:"none",backgroundColor:(e.vars||e).palette.action.hover,"@media (hover: none)":{backgroundColor:"transparent"}},[`&.${v.selected}`]:{backgroundColor:e.vars?`rgba(${e.vars.palette.primary.mainChannel} / ${e.vars.palette.action.selectedOpacity})`:(0,n.Fq)(e.palette.primary.main,e.palette.action.selectedOpacity),[`&.${v.focusVisible}`]:{backgroundColor:e.vars?`rgba(${e.vars.palette.primary.mainChannel} / calc(${e.vars.palette.action.selectedOpacity} + ${e.vars.palette.action.focusOpacity}))`:(0,n.Fq)(e.palette.primary.main,e.palette.action.selectedOpacity+e.palette.action.focusOpacity)}},[`&.${v.selected}:hover`]:{backgroundColor:e.vars?`rgba(${e.vars.palette.primary.mainChannel} / calc(${e.vars.palette.action.selectedOpacity} + ${e.vars.palette.action.hoverOpacity}))`:(0,n.Fq)(e.palette.primary.main,e.palette.action.selectedOpacity+e.palette.action.hoverOpacity),"@media (hover: none)":{backgroundColor:e.vars?`rgba(${e.vars.palette.primary.mainChannel} / ${e.vars.palette.action.selectedOpacity})`:(0,n.Fq)(e.palette.primary.main,e.palette.action.selectedOpacity)}},[`&.${v.focusVisible}`]:{backgroundColor:(e.vars||e).palette.action.focus},[`&.${v.disabled}`]:{opacity:(e.vars||e).palette.action.disabledOpacity},[`& + .${f.Z.root}`]:{marginTop:e.spacing(1),marginBottom:e.spacing(1)},[`& + .${f.Z.inset}`]:{marginLeft:52},[`& .${x.Z.root}`]:{marginTop:0,marginBottom:0},[`& .${x.Z.inset}`]:{paddingLeft:36},[`& .${g.Z.root}`]:{minWidth:36},variants:[{props:({ownerState:e})=>!e.disableGutters,style:{paddingLeft:16,paddingRight:16}},{props:({ownerState:e})=>e.divider,style:{borderBottom:`1px solid ${(e.vars||e).palette.divider}`,backgroundClip:"padding-box"}},{props:({ownerState:e})=>!e.dense,style:{[e.breakpoints.up("sm")]:{minHeight:"auto"}}},{props:({ownerState:e})=>e.dense,style:{minHeight:32,paddingTop:4,paddingBottom:4,...e.typography.body2,[`& .${g.Z.root} svg`]:{fontSize:"1.25rem"}}}]}))),j=a.forwardRef(function(e,t){let r;let o=(0,c.i)({props:e,name:"MuiMenuItem"}),{autoFocus:n=!1,component:s="li",dense:l=!1,divider:d=!1,disableGutters:p=!1,focusVisibleClassName:f,role:g="menuitem",tabIndex:x,className:y,...b}=o,A=a.useContext(u.Z),v=a.useMemo(()=>({dense:l||A.dense||!1,disableGutters:p}),[A.dense,l,p]),j=a.useRef(null);(0,m.Z)(()=>{n&&j.current&&j.current.focus()},[n]);let C={...o,dense:v.dense,divider:d,disableGutters:p},I=S(o),k=(0,h.Z)(j,t);return o.disabled||(r=void 0!==x?x:-1),(0,w.jsx)(u.Z.Provider,{value:v,children:(0,w.jsx)(Z,{ref:k,role:g,tabIndex:r,component:s,focusVisibleClassName:(0,i.Z)(I.focusVisible,f),className:(0,i.Z)(I.root,y),...b,ownerState:C,classes:I})})})},64141:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>k});var a=r(10326),i=r(17577),o=r(46791),n=r(26472),s=r(53913),l=r(90943),d=r(90541),c=r(6283),u=r(25609),p=r(98139),m=r(16027),h=r(37841),f=r(5394),g=r(76971),x=r(42265),y=r(35047),b=r(59423),A=r(94638),v=r(31870),w=r(46226),S=r(30656),Z=r(62432),j=r(90434);let C=({label:e,value:t,onChange:r,error:i,helperText:o,placeholder:n,select:c,children:u,readOnly:p})=>(0,a.jsxs)(s.Z,{fullWidth:!0,children:[a.jsx(l.Z,{sx:{color:"#ffffff",fontWeight:"500",mb:1,fontSize:"17px"},children:e}),a.jsx(d.Z,{variant:"outlined",value:t,onChange:r,error:!!i,helperText:o,placeholder:n,select:c,inputProps:{readOnly:p},sx:{borderRadius:"10px",backgroundColor:"#333333",mb:0,input:{color:"#ffffff"},"& .MuiOutlinedInput-root":{borderRadius:"10px","&:hover fieldset":{borderColor:i?"#ff0000":"#ffffff"},"&.Mui-focused fieldset":{borderColor:i?"#ff0000":"#ffffff"}},"& .MuiInputLabel-root":{color:"#ffffff"},"& .MuiSvgIcon-root":{color:"#ffffff"},"& .MuiSelect-select":{color:"#ffffff"}},children:u})]}),I=()=>{let e=(0,v.T)(),t=(0,y.useRouter)(),[r,s]=(0,i.useState)(!1),[l,d]=(0,i.useState)(null),[Z,I]=(0,i.useState)(!1),[k,P]=(0,i.useState)(!0),[E,R]=(0,i.useState)(!1),[D,q]=(0,i.useState)(!1),[N,M]=(0,i.useState)({email:"",name:"",role:"",isAdmin:!1,isBroker:!1,isActivated:!1}),[B,O]=(0,i.useState)({name:"",adminEmail:"",adminRole:"",companySize:0,industry:"",location:"",website:"",brokerId:"",brokerageId:"",isBrokerage:!1,isActivated:!1,howHeard:"",referralSource:""}),[T,U]=(0,i.useState)({isAdmin:!1}),[G,L]=(0,i.useState)({}),[_,F]=(0,i.useState)(!1),[W,z]=(0,i.useState)("");(0,i.useEffect)(()=>{(async()=>{let e=window.location.href,t=await (0,b.lY)(e);if(t.companyDetails&&O(t.companyDetails),t.userDetails&&M(t.userDetails),t.additionalParams&&(U(t.additionalParams),t.additionalParams.email&&d(t.additionalParams.email)),(0,o.JB)(n.I,e)){let e=window.localStorage.getItem("emailForSignIn1");e||t.additionalParams.email?d(e||t.additionalParams.email):s(!0)}P(!1)})()},[]),(0,i.useEffect)(()=>{(async()=>{if(l)try{await (0,o.P6)(n.I,l,window.location.href),console.log("Sign-in successful"),window.localStorage.removeItem("emailForSignIn1"),I(!0)}catch(e){console.error("Error signing in with email link:",e),q(!0)}})()},[l]);let Y=()=>{let e={},t=!0;return N.name||(e.name="Full Name is required",t=!1),_?z(""):(z("You must accept the Terms & Conditions and Privacy Policy to continue"),t=!1),T.isAdmin&&(B.name||(e.companyName="Company Name is required",t=!1),B.adminRole||(e.adminRole="Role/Title is required",t=!1),B.companySize||(e.companySize="Company Size is required",t=!1),B.industry||(e.industry="Industry is required",t=!1),B.location||(e.location="Country is required",t=!1),B.howHeard||(e.howHeard="How did you hear about us is required",t=!1),B.referralSource||(e.referralSource="Referral Source is required",t=!1)),L(e),t},H=async()=>{if(!Y())return;R(!0);let r=await (0,A.zX)(e,B,N);r&&200===r.status&&t.push("/dashboard"),R(!1)},V=(e,t,r)=>{L({...G,[e]:""}),"userDetails"===r?M({...N,[e]:t}):O({...B,[e]:t})};return(0,a.jsxs)(c.Z,{sx:{backgroundColor:"#000000",minHeight:"98px",flexDirection:"column",alignItems:"center",justifyContent:"flex-start"},children:[(0,a.jsxs)(c.Z,{sx:{display:"flex",alignItems:"center",mb:5,cursor:"pointer",position:"absolute",top:"30px",left:"30px"},onClick:()=>console.log("Logo Clicked"),children:[a.jsx(w.default,{src:S.Z,alt:"BenOsphere Logo",width:40,height:40}),a.jsx(u.Z,{variant:"h6",sx:{ml:1,fontWeight:"800",color:"#ffffff"},children:"BenOsphere"})]}),k?a.jsx(p.Z,{color:"inherit"}):D?(0,a.jsxs)(c.Z,{sx:{display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",color:"white",textAlign:"center",height:"100%"},children:[a.jsx(u.Z,{sx:{fontWeight:"bold",fontSize:"60px",mb:2},children:"❌ Invalid Magic Link"}),(0,a.jsxs)(u.Z,{variant:"body1",sx:{fontSize:"20px",color:"#bbbbbb",mb:4},children:["You can sign in again by requesting a new link ",a.jsx(j.default,{href:"/",style:{color:"#B983FF",textDecoration:"underline"},children:"here"}),"."]})]}):Z&&(0,a.jsxs)(c.Z,{sx:{width:"100%",maxWidth:"100%"},children:[a.jsx(u.Z,{variant:"h4",sx:{fontSize:"50px",fontWeight:"bold",lineHeight:"1.2",color:"#ffffff",mb:3},children:"Get Free Access"}),(0,a.jsxs)(m.ZP,{container:!0,spacing:3,sx:{marginBottom:"16px"},children:[a.jsx(m.ZP,{item:!0,xs:12,children:a.jsx(C,{label:"Full Name",value:N.name,onChange:e=>V("name",e.target.value,"userDetails"),error:G.name,helperText:G.name,placeholder:"John Doe"})}),a.jsx(m.ZP,{item:!0,xs:12,children:a.jsx(C,{label:"Email",value:N.email,onChange:()=>{},readOnly:!0})})]}),T.isAdmin&&a.jsx(c.Z,{sx:{mt:3},children:(0,a.jsxs)(m.ZP,{container:!0,spacing:3,sx:{marginBottom:"16px"},children:[a.jsx(m.ZP,{item:!0,xs:12,children:a.jsx(C,{label:"Company Name",value:B.name,onChange:e=>V("name",e.target.value,"companyDetails"),error:G.companyName,helperText:G.companyName,placeholder:"Doe LLC"})}),a.jsx(m.ZP,{item:!0,xs:12,children:(0,a.jsxs)(C,{label:"Role/Title",value:B.adminRole,onChange:e=>V("adminRole",e.target.value,"companyDetails"),error:G.adminRole,helperText:G.adminRole,select:!0,children:[a.jsx(h.Z,{value:"CEO",children:"CEO"}),a.jsx(h.Z,{value:"HR Manager",children:"HR Manager"}),a.jsx(h.Z,{value:"Benefits Specialist",children:"Benefits Specialist"}),a.jsx(h.Z,{value:"CFO",children:"CFO"}),a.jsx(h.Z,{value:"Other",children:"Other"})]})}),a.jsx(m.ZP,{item:!0,xs:12,children:(0,a.jsxs)(C,{label:"Company Size",value:B.companySize,onChange:e=>V("companySize",e.target.value,"companyDetails"),error:G.companySize,helperText:G.companySize,select:!0,children:[a.jsx(h.Z,{value:1,children:"1-10 employees"}),a.jsx(h.Z,{value:2,children:"11-50 employees"}),a.jsx(h.Z,{value:3,children:"51-200 employees"}),a.jsx(h.Z,{value:4,children:"201-500 employees"}),a.jsx(h.Z,{value:5,children:"500+ employees"})]})}),a.jsx(m.ZP,{item:!0,xs:12,children:(0,a.jsxs)(C,{label:"Industry",value:B.industry,onChange:e=>V("industry",e.target.value,"companyDetails"),error:G.industry,helperText:G.industry,select:!0,children:[a.jsx(h.Z,{value:"Technology",children:"Technology"}),a.jsx(h.Z,{value:"Healthcare",children:"Healthcare"}),a.jsx(h.Z,{value:"Finance",children:"Finance"}),a.jsx(h.Z,{value:"Education",children:"Education"}),a.jsx(h.Z,{value:"Insurance Broker",children:"Insurance Broker"}),a.jsx(h.Z,{value:"Other",children:"Other"})]})}),a.jsx(m.ZP,{item:!0,xs:12,children:a.jsx(C,{label:"Country",value:B.location,onChange:e=>V("location",e.target.value,"companyDetails"),error:G.location,helperText:G.location,select:!0,children:a.jsx(h.Z,{value:"United States",children:"United States"})})}),a.jsx(m.ZP,{item:!0,xs:12,children:(0,a.jsxs)(C,{label:"How did you hear about us",value:B.howHeard,onChange:e=>V("howHeard",e.target.value,"companyDetails"),error:G.howHeard,helperText:G.howHeard,select:!0,children:[a.jsx(h.Z,{value:"Referral",children:"Referral"}),a.jsx(h.Z,{value:"Social Media",children:"Social Media"}),a.jsx(h.Z,{value:"Search Engine",children:"Search Engine"}),a.jsx(h.Z,{value:"Advertisement",children:"Advertisement"}),a.jsx(h.Z,{value:"Other",children:"Other"})]})}),a.jsx(m.ZP,{item:!0,xs:12,children:a.jsx(C,{label:"Referral Source",value:B.referralSource,onChange:e=>V("referralSource",e.target.value,"companyDetails"),error:G.referralSource,helperText:G.referralSource,placeholder:"LinkedIn, Twitter, etc."})})]})}),(0,a.jsxs)(c.Z,{sx:{mt:4},children:[a.jsx(f.Z,{control:a.jsx(g.Z,{checked:_,onChange:e=>{F(e.target.checked),e.target.checked&&z("")},sx:{color:"#ffffff","&.Mui-checked":{color:"#B983FF"}}}),label:(0,a.jsxs)(u.Z,{sx:{color:"#ffffff",fontSize:"14px"},children:["I agree to the"," ",a.jsx(j.default,{href:"https://benosphere.com/terms-conditions",target:"_blank",rel:"noopener noreferrer",style:{color:"#B983FF",textDecoration:"underline"},children:"Terms & Conditions"})," ","and"," ",a.jsx(j.default,{href:"https://benosphere.com/privacy-policy",target:"_blank",rel:"noopener noreferrer",style:{color:"#B983FF",textDecoration:"underline"},children:"Privacy Policy"}),"."]})}),W&&a.jsx(u.Z,{sx:{color:"#ff4d4f",fontSize:"12px",mt:1},children:W}),E?a.jsx(p.Z,{size:24,sx:{color:"#ffffff"}}):a.jsx(x.Z,{variant:"contained",color:"primary",fullWidth:!0,sx:{background:"linear-gradient(90deg, #7206E6, #B54BFF)",padding:"12px",fontSize:"16px",textTransform:"none",borderRadius:2,mb:10},onClick:H,children:"Confirm Details"})]})]})]})},k=()=>a.jsx(Z.Z,{LeftComponent:a.jsx(I,{})})},62432:(e,t,r)=>{"use strict";r.d(t,{Z:()=>u});var a=r(10326),i=r(16027),o=r(6283),n=r(25609),s=r(46226);let l=(0,r(51426).Z)((0,a.jsx)("path",{d:"M6 17h3l2-4V7H5v6h3zm8 0h3l2-4V7h-6v6h3z"}),"FormatQuote");var d=r(17577);let c=[{src:{src:"/_next/static/media/landing_page_image_1.883ba61a.png",height:1024,width:756,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAYAAAAICAIAAABVpBlvAAAAoUlEQVR42mOwNNRyMlRnYJRQUlNXNbISV9RgcHcwdrAwTPJ2ctVWFlTVkdPUZ7CxMw51MVvaklcb5cnAKqqoosGgbqrTmRe7e1ZbqZ+jnY6WgoEJg6CMUpify5UN07tivCU19O19fBiAoDjO/dzazqIoNwZ1CyklDYbGyTMO7N82rzwkkJchu6K6sr2D4eiV63suXl+3c09rU2Nt74TmKdMAG00wAXeqZ/wAAAAASUVORK5CYII=",blurWidth:6,blurHeight:8},text:"I get to keep track of all my benefits so I never miss out on the stuff that matters most.",name:"Troy Sivan",title:"Associate Manager"},{src:{src:"/_next/static/media/landing_page_image_2.59f16961.png",height:1024,width:756,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAYAAAAICAIAAABVpBlvAAAAo0lEQVR42mOYMakxwMdRRU/Lzs4sNtTTx9WGYeXS6TYOlik2GjGmKpbWJgWJgQwbN62M9rTbXJexsiTBWkXWydmWYcuurZO7G45NqluR6nfs+IkZq9cxHD1zatqS+Vsn1E4I0L3+4MH6nVsZZi9aFJWevXvn3i1rNtd2TQsMcmYAAgc3v8Ub989Zu6+kvgfEl5CWd/MJmrd8Y++0RYUVjQwMDACH00ArwNCIEAAAAABJRU5ErkJggg==",blurWidth:6,blurHeight:8},text:"Now I can track my benefits instantly without having to search through emails. It’s a game–changer!",name:"David Miller",title:"Software Engineer"},{src:{src:"/_next/static/media/landing_page_image_3.e9420572.png",height:1024,width:756,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAYAAAAICAIAAABVpBlvAAAAn0lEQVR42mNQE2YNsRJlYGAwlmL3UuUwFGNmcNbgXlNuNCkz0FtHTpydwUSSlUGBnaHYW29jc3pXoo8KA4ORNBeDOBODl7Ha3JyQQk8LLgYGYS4WBlN5PgYGhjgHw2wPM0FJGQYgsNeQZNbQj7HRzQlyU7WwU1VVZ3CyMrG2ssxxM9M2sTK0tFFW12IwsrF1MDN2tbPXs3TQN7MQk5ACAOjZG1OaugBXAAAAAElFTkSuQmCC",blurWidth:6,blurHeight:8},text:"This is by far the most convenient way to access my benefits. Everything I need is right at my fingertips.",name:"Emily Garcia",title:"HR Specialist"},{src:{src:"/_next/static/media/landing_page_image_4.135a5874.png",height:1024,width:756,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAYAAAAICAIAAABVpBlvAAAAo0lEQVR42gGYAGf/AIl9cqKWioB2bZSGenZuZlpVUQCgmZKMh4KGWD++oI5yaWRtbGkArK+snpmWkl9G0rWooZSGbXZxAH+Af5mbmnpRRLWVh4qGe1JnQgCTfm+Wk456Y1evmYqprrBhbWAAnZWUoaKjsKWg1c/M5O/zwczSALzFy8nT2rnDy9vn7NXh583Y3gCoq6/CzdXDzdTI09nR3ePU3+Q7J1hGmDSqYQAAAABJRU5ErkJggg==",blurWidth:6,blurHeight:8},text:"I love how I can get all my benefits info through Slack. It saves me so much time!",name:"Troy Edward",title:"Associate Manager"}],u=({LeftComponent:e})=>{let[t,r]=(0,d.useState)(0);(0,d.useEffect)(()=>{let e=setInterval(()=>{r(e=>(e+1)%c.length)},5e3);return()=>clearInterval(e)},[]);let u=c[t];return(0,a.jsxs)(i.ZP,{container:!0,style:{height:"95vh",width:"100%"},children:[a.jsx(i.ZP,{item:!0,xs:12,md:6,sx:{bgcolor:"#000000",color:"#ffffff",display:"flex",flexDirection:"column",justifyContent:"center",alignItems:"center",padding:"100px",height:"95vh",overflow:"auto"},children:e}),a.jsx(i.ZP,{item:!0,xs:12,md:6,sx:{display:"flex",justifyContent:"center",alignItems:"center",backgroundColor:"#f8f9fa",padding:"0",position:"relative",overflow:"hidden",height:"95vh"},children:(0,a.jsxs)(o.Z,{sx:{position:"relative",width:"100%",height:"100%",overflow:"hidden"},children:[a.jsx(s.default,{src:u.src,alt:"Profile",layout:"fill",objectFit:"cover",style:{objectPosition:"center"}}),a.jsx(o.Z,{sx:{position:"absolute",bottom:0,left:0,width:"100%",height:"100%",background:"linear-gradient(to top, rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0) 60%)"}}),(0,a.jsxs)(o.Z,{sx:{position:"absolute",bottom:"10%",left:"10%",color:"#ffffff",width:"550px"},children:[a.jsx(l,{sx:{fontSize:70,marginBottom:"10px",opacity:.5,marginLeft:-2}}),a.jsx(n.Z,{variant:"h5",sx:{fontSize:"32px",fontWeight:"bold",lineHeight:"1.5",mb:2},children:u.text}),a.jsx(n.Z,{variant:"h5",sx:{fontSize:"24px",fontWeight:"bold",lineHeight:"1.5",mt:4},children:u.name}),a.jsx(n.Z,{variant:"body2",sx:{fontSize:"20px",fontWeight:"light"},children:u.title})]})]})})]})}},94638:(e,t,r)=>{"use strict";r.d(t,{G9:()=>x,JZ:()=>y,M_:()=>h,N:()=>d,Nq:()=>m,TQ:()=>u,Ur:()=>s,aE:()=>c,aK:()=>v,dA:()=>l,gt:()=>b,mb:()=>g,qB:()=>A,yu:()=>p,zX:()=>f});var a=r(53148),i=r(39352),o=r(25748),n=r(32049);function s(e){switch(e){case"Our Culture & Workplace":return"Work Policies";case"Protecting Your Income":return"Income Security";default:return e}}function l(e){switch(e){case"On-site Amenities":return"Company Handbook";case"Protecting Your Income":return"Income Security";case"Got married/divorced":return"Marriage or Divorce";case"Had a baby or adopted a baby":return"New Baby or Adoption";case"Lost your insurance":return"Loss of Insurance";default:return e}}async function d(e,t){let r=await (0,a.A_)("/benefits/benefit-types",{companyId:t});return console.log("COMPANY BENEFIT TYPES RESPONSE: ",r.benefitTypes),e((0,i.x7)(r.benefitTypes)),r.benefitTypes}async function c(e,t){let r=await (0,a.A_)("/benefits/all-benefits",{companyId:t});console.log("COMPANY BENEFIT TYPES WITH BENEFITS RESPONSE: ",r),e((0,o.US)(r.benefitsPerType))}async function u(e){let t=await (0,a.A_)("/admin/all-employees");return console.log("COMPANY TEAM MEMBERS RESPONSE: ",t),e((0,i.Vv)(t.employees)),t.employees}async function p(e,t){return console.log("ADDING USERS: ",t),await (0,a.j0)("/admin/add/employees",{employeeList:t})}async function m(e,t,r){try{console.log("\uD83D\uDD0D Debug: User being updated:",t);let e={employeeId:t,updatedDetails:{name:r.name,email:r.email,details:{phoneNumber:r.phoneNumber||"",department:r.department||"",title:r.title||"",role:r.title||""}}};return r.dateOfBirth&&(e.updatedDetails.details.dateOfBirth=r.dateOfBirth),r.hireDate&&(e.updatedDetails.details.hireDate=r.hireDate),r.annualSalary&&(e.updatedDetails.details.annualSalary=r.annualSalary),r.employeeClassType&&(e.updatedDetails.details.employeeClassType=r.employeeClassType),r.workSchedule&&(e.updatedDetails.details.workSchedule=r.workSchedule),r.ssn&&(e.updatedDetails.details.ssn=r.ssn),r.employeeId&&(e.updatedDetails.details.employeeId=r.employeeId),r.workLocation&&(e.updatedDetails.details.workLocation=r.workLocation),r.address&&(e.updatedDetails.details.address=r.address),r.mailingAddress&&(e.updatedDetails.details.mailingAddress=r.mailingAddress),r.emergencyContact&&(e.updatedDetails.details.emergencyContact=r.emergencyContact),e.updatedDetails.details.dependents=r.dependents||[],console.log("Middleware - dependents being sent:",e.updatedDetails.details.dependents),console.log("Sending PUT request with cleaned data:",e),await (0,a.GH)("/admin/update/employee",e)}catch(e){throw console.error("Error in updateUser middleware:",e),e}}async function h(e,t){let r=await (0,a.A_)("/employee",{"user-id":t});return e((0,n.$l)({name:r.currentUser.name,email:r.currentUser.email,companyId:r.currentUser.companyId,role:r.currentUser.role,isAdmin:r.currentUser.isAdmin,isBroker:r.currentUser.isBroker,details:r.currentUser.details})),r}async function f(e,t,r){let i=await (0,a.j0)("/admin/onboard",{company:{name:t.name,adminEmail:t.adminEmail,adminRole:t.adminRole,companySize:t.companySize,industry:t.industry,location:t.location,website:t.website,howHeard:t.howHeard,brokerId:t.brokerId,brokerageId:t.brokerageId,isBrokerage:t.isBrokerage,isActivated:t.isActivated,referralSource:t.referralSource,details:{logo:""}},user:{email:r.email,name:r.name,role:r.role,isAdmin:r.isAdmin,isBroker:r.isBroker,isActivated:r.isActivated}}),o=i.data.userId,n=i.data.companyId;return localStorage.setItem("userid1",o),localStorage.setItem("companyId1",n),i}async function g(e,t){return console.log("SENDING LOGIN LINK TO EMPLOYEE: ",e,t),await (0,a.j0)("/admin/send-user-login-link",{userId:e,companyId:t})}async function x(e,t,r,i){let o=await (0,a.j0)("/admin/add/employer",{brokerId:e,companyName:t,companyAdminEmail:r,companyAdminName:i});return console.log("BROKER ADDS COMPANY RESPONSE: ",o),o}async function y(e,t){return 200===(await (0,a.j0)("/employee/offboard/",{userId:e,companyId:t})).status}async function b(e,t){return await (0,a.j0)("/employee/enable/",{userId:e,companyId:t})}async function A(e,t){try{let t=await (0,a.A_)("/admin/all-companies");console.log("CLIENT COMPANIES UNDER BROKER: ",t);let r=t.companies||[];try{let e=await (0,a.A_)("/employee/company-details");console.log("BROKER'S OWN COMPANY: ",e),e.company&&e.company.isBrokerage&&!r.some(t=>t._id===e.company._id)&&(r.unshift(e.company),console.log("Added broker's own company to the list"))}catch(e){console.log("Could not fetch broker's own company (this is normal if user is not a broker):",e)}return console.log("ALL COMPANIES (CLIENT + OWN): ",r),e((0,n.Ym)(r)),{...t,companies:r}}catch(t){return console.error("Error fetching companies:",t),e((0,n.Ym)([])),{companies:[]}}}async function v(e){let t=await (0,a.A_)("/employee/company-details");return e((0,i.sy)(t.company)),t.status}},31870:(e,t,r)=>{"use strict";r.d(t,{C:()=>o,T:()=>i});var a=r(25842);let i=()=>(0,a.I0)(),o=a.v9},17822:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});let a=(0,r(68570).createProxy)(String.raw`C:\beno\project_dev\qharmony-frontend-v1\src\app\onboard\page.tsx#default`)},30656:(e,t,r)=>{"use strict";r.d(t,{Z:()=>a});let a={src:"/_next/static/media/logo.770bfeee.png",height:300,width:300,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAYAAADED76LAAAA9ElEQVR42i2PMUoDQRiF3z+zu7MbFTaxkfUC0St4ACWdhbbapvACCnaCpU0a09jYiQewFj2AIljY2AoJ7LKZnc3s/P6EPPiq9+DxESR359UgBHNrGxTWQTcOPwtrLwGU6n5c7ySxeQb4vWn9WS/lky50JVH6UlrVjzTFE2iebvbwmJkE37/zPLB79SHfzWIzUUph0LrqScB4qpFEdEhICxm9BeY9BYA9kxJwfTw7IEKfGUsAq06FgNlGtjUSoDTvS1mB3B/BDInoM/KhvQhd8lDb5RGz/pDLVFM+inVc1L49pbXmtmjeiOZQNCGaX4vGXQGY/wM1tG/NQnnUIwAAAABJRU5ErkJggg==",blurWidth:8,blurHeight:8}},73881:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i});var a=r(66621);let i=e=>[{type:"image/x-icon",sizes:"16x16",url:(0,a.fillMetadataSegment)(".",e.params,"favicon.ico")+""}]}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[8948,1183,6621,9066,1999,3253,928,8097,6027,541,434,6971,576],()=>r(52645));module.exports=a})();