(()=>{var e={};e.id=9979,e.ids=[9979],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},27790:e=>{"use strict";e.exports=require("assert")},84770:e=>{"use strict";e.exports=require("crypto")},17702:e=>{"use strict";e.exports=require("events")},92048:e=>{"use strict";e.exports=require("fs")},32615:e=>{"use strict";e.exports=require("http")},35240:e=>{"use strict";e.exports=require("https")},19801:e=>{"use strict";e.exports=require("os")},55315:e=>{"use strict";e.exports=require("path")},76162:e=>{"use strict";e.exports=require("stream")},74026:e=>{"use strict";e.exports=require("string_decoder")},74175:e=>{"use strict";e.exports=require("tty")},17360:e=>{"use strict";e.exports=require("url")},21764:e=>{"use strict";e.exports=require("util")},71568:e=>{"use strict";e.exports=require("zlib")},98061:e=>{"use strict";e.exports=require("node:assert")},92761:e=>{"use strict";e.exports=require("node:async_hooks")},72254:e=>{"use strict";e.exports=require("node:buffer")},40027:e=>{"use strict";e.exports=require("node:console")},6005:e=>{"use strict";e.exports=require("node:crypto")},65714:e=>{"use strict";e.exports=require("node:diagnostics_channel")},15673:e=>{"use strict";e.exports=require("node:events")},88849:e=>{"use strict";e.exports=require("node:http")},42725:e=>{"use strict";e.exports=require("node:http2")},87503:e=>{"use strict";e.exports=require("node:net")},38846:e=>{"use strict";e.exports=require("node:perf_hooks")},39630:e=>{"use strict";e.exports=require("node:querystring")},84492:e=>{"use strict";e.exports=require("node:stream")},31764:e=>{"use strict";e.exports=require("node:tls")},41041:e=>{"use strict";e.exports=require("node:url")},47261:e=>{"use strict";e.exports=require("node:util")},93746:e=>{"use strict";e.exports=require("node:util/types")},24086:e=>{"use strict";e.exports=require("node:worker_threads")},65628:e=>{"use strict";e.exports=require("node:zlib")},91080:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>h,originalPathname:()=>p,pages:()=>c,routeModule:()=>u,tree:()=>d}),r(35480),r(33709),r(35866);var i=r(23191),o=r(88716),s=r(37922),n=r.n(s),a=r(95231),l={};for(let e in a)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>a[e]);r.d(t,l);let d=["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,35480)),"C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\page.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,33709)),"C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,35866,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],c=["C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\page.tsx"],p="/page",h={require:r,loadChunk:()=>Promise.resolve()},u=new i.AppPageRouteModule({definition:{kind:o.x.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},68742:(e,t,r)=>{Promise.resolve().then(r.bind(r,54497))},99207:(e,t,r)=>{"use strict";r.d(t,{Z:()=>f});var i=r(17577),o=r(41135),s=r(88634),n=r(44823),a=r(91703),l=r(13643),d=r(2791),c=r(73025),p=r(10326);let h=e=>{let{absolute:t,children:r,classes:i,flexItem:o,light:n,orientation:a,textAlign:l,variant:d}=e;return(0,s.Z)({root:["root",t&&"absolute",d,n&&"light","vertical"===a&&"vertical",o&&"flexItem",r&&"withChildren",r&&"vertical"===a&&"withChildrenVertical","right"===l&&"vertical"!==a&&"textAlignRight","left"===l&&"vertical"!==a&&"textAlignLeft"],wrapper:["wrapper","vertical"===a&&"wrapperVertical"]},c.V,i)},u=(0,a.default)("div",{name:"MuiDivider",slot:"Root",overridesResolver:(e,t)=>{let{ownerState:r}=e;return[t.root,r.absolute&&t.absolute,t[r.variant],r.light&&t.light,"vertical"===r.orientation&&t.vertical,r.flexItem&&t.flexItem,r.children&&t.withChildren,r.children&&"vertical"===r.orientation&&t.withChildrenVertical,"right"===r.textAlign&&"vertical"!==r.orientation&&t.textAlignRight,"left"===r.textAlign&&"vertical"!==r.orientation&&t.textAlignLeft]}})((0,l.Z)(({theme:e})=>({margin:0,flexShrink:0,borderWidth:0,borderStyle:"solid",borderColor:(e.vars||e).palette.divider,borderBottomWidth:"thin",variants:[{props:{absolute:!0},style:{position:"absolute",bottom:0,left:0,width:"100%"}},{props:{light:!0},style:{borderColor:e.vars?`rgba(${e.vars.palette.dividerChannel} / 0.08)`:(0,n.Fq)(e.palette.divider,.08)}},{props:{variant:"inset"},style:{marginLeft:72}},{props:{variant:"middle",orientation:"horizontal"},style:{marginLeft:e.spacing(2),marginRight:e.spacing(2)}},{props:{variant:"middle",orientation:"vertical"},style:{marginTop:e.spacing(1),marginBottom:e.spacing(1)}},{props:{orientation:"vertical"},style:{height:"100%",borderBottomWidth:0,borderRightWidth:"thin"}},{props:{flexItem:!0},style:{alignSelf:"stretch",height:"auto"}},{props:({ownerState:e})=>!!e.children,style:{display:"flex",textAlign:"center",border:0,borderTopStyle:"solid",borderLeftStyle:"solid","&::before, &::after":{content:'""',alignSelf:"center"}}},{props:({ownerState:e})=>e.children&&"vertical"!==e.orientation,style:{"&::before, &::after":{width:"100%",borderTop:`thin solid ${(e.vars||e).palette.divider}`,borderTopStyle:"inherit"}}},{props:({ownerState:e})=>"vertical"===e.orientation&&e.children,style:{flexDirection:"column","&::before, &::after":{height:"100%",borderLeft:`thin solid ${(e.vars||e).palette.divider}`,borderLeftStyle:"inherit"}}},{props:({ownerState:e})=>"right"===e.textAlign&&"vertical"!==e.orientation,style:{"&::before":{width:"90%"},"&::after":{width:"10%"}}},{props:({ownerState:e})=>"left"===e.textAlign&&"vertical"!==e.orientation,style:{"&::before":{width:"10%"},"&::after":{width:"90%"}}}]}))),g=(0,a.default)("span",{name:"MuiDivider",slot:"Wrapper",overridesResolver:(e,t)=>{let{ownerState:r}=e;return[t.wrapper,"vertical"===r.orientation&&t.wrapperVertical]}})((0,l.Z)(({theme:e})=>({display:"inline-block",paddingLeft:`calc(${e.spacing(1)} * 1.2)`,paddingRight:`calc(${e.spacing(1)} * 1.2)`,whiteSpace:"nowrap",variants:[{props:{orientation:"vertical"},style:{paddingTop:`calc(${e.spacing(1)} * 1.2)`,paddingBottom:`calc(${e.spacing(1)} * 1.2)`}}]}))),x=i.forwardRef(function(e,t){let r=(0,d.i)({props:e,name:"MuiDivider"}),{absolute:i=!1,children:s,className:n,orientation:a="horizontal",component:l=s||"vertical"===a?"div":"hr",flexItem:c=!1,light:x=!1,role:f="hr"!==l?"separator":void 0,textAlign:A="center",variant:m="fullWidth",...b}=r,v={...r,absolute:i,component:l,flexItem:c,light:x,orientation:a,role:f,textAlign:A,variant:m},y=h(v);return(0,p.jsx)(u,{as:l,className:(0,o.Z)(y.root,n),role:f,ref:t,ownerState:v,"aria-orientation":"separator"===f&&("hr"!==l||"vertical"===a)?a:void 0,...b,children:s?(0,p.jsx)(g,{className:y.wrapper,ownerState:v,children:s}):null})});x&&(x.muiSkipListHighlight=!0);let f=x},73025:(e,t,r)=>{"use strict";r.d(t,{V:()=>s,Z:()=>n});var i=r(71685),o=r(97898);function s(e){return(0,o.ZP)("MuiDivider",e)}let n=(0,i.Z)("MuiDivider",["root","absolute","fullWidth","inset","middle","flexItem","light","vertical","withChildren","withChildrenVertical","textAlignRight","textAlignLeft","wrapper","wrapperVertical"])},48090:(e,t,r)=>{"use strict";r.d(t,{Z:()=>s});var i=r(54347),o=r(14750);let s=(0,i.unstable_createUseMediaQuery)({themeId:o.Z})},54497:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>b});var i=r(10326),o=r(6283),s=r(25609),n=r(90541),a=r(42265),l=r(98139),d=r(48090),c=r(46226),p=r(17577),h=r(30656),u=r(59423),g=r(62432),x=r(22758),f=r(35047),A=r(41014);let m=()=>{let{user:e}=(0,x.a)(),t=(0,f.useRouter)();(0,p.useEffect)(()=>{e&&t.push("/dashboard")},[e]);let[r,d]=(0,p.useState)(""),[g,A]=(0,p.useState)(!1),[m,b]=(0,p.useState)(!1),[v,y]=(0,p.useState)(""),[w,j]=(0,p.useState)(""),Z=async()=>{if(!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(r)){j("Please enter a valid email address.");return}A(!0),y(await (0,u.selfOnboard)(r)),A(!1),b(!0)};return(0,i.jsxs)(o.Z,{children:[(0,i.jsxs)(o.Z,{sx:{display:"flex",alignItems:"center",mb:5,cursor:"pointer",position:"absolute",top:"30px",left:"100px"},onClick:()=>{window.location.href="https://benosphere.com/"},children:[i.jsx(c.default,{src:h.Z,alt:"BenOsphere Logo",width:40,height:40}),i.jsx(s.Z,{variant:"h6",sx:{ml:1,fontWeight:"800",color:"#ffffff"},children:"BenOsphere"})]}),"ask_admin_to_add"===v?(0,i.jsxs)(o.Z,{sx:{display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",color:"white",textAlign:"center",height:"100%"},children:[i.jsx(s.Z,{sx:{fontWeight:"bold",fontSize:"60px",mb:2},children:"\uD83E\uDD14 No account yet"}),i.jsx(s.Z,{variant:"body1",sx:{fontSize:"20px",color:"#bbbbbb"},children:"Contact your company's admin HR to learn more on how to get access to BenOsphere. We will see you again shortly."})]}):"magic_link_sent"===v?(0,i.jsxs)(o.Z,{sx:{display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",color:"white",textAlign:"center",height:"100%"},children:[i.jsx(s.Z,{sx:{fontWeight:"bold",fontSize:"60px",mb:2},children:"✅ Magic Link Sent"}),i.jsx(s.Z,{variant:"body1",sx:{fontSize:"20px",color:"#bbbbbb"},children:"Please check your email for the magic link to access BenOsphere. If it's not in your inbox, please check your spam or junk folder."})]}):(0,i.jsxs)(o.Z,{sx:{maxWidth:"600px",width:"100%",mt:8},children:[i.jsx(s.Z,{variant:"h4",sx:{fontSize:"70px",fontWeight:"bold",mb:2,lineHeight:"1.2",color:"#ffffff"},children:"Maximize Your Employee Benefits"}),i.jsx(s.Z,{variant:"body1",sx:{mb:6,fontSize:"18px",color:"#bbbbbb"},children:"Enter your work email, and we'll send you a magic link to easily access all your benefits."}),(0,i.jsxs)(o.Z,{sx:{display:"flex",flexDirection:"column",alignItems:"stretch",mb:3},children:[i.jsx(n.Z,{fullWidth:!0,variant:"outlined",placeholder:"<EMAIL>",value:r,onChange:e=>{d(e.target.value),j("")},sx:{bgcolor:"#333333",input:{color:"#ffffff"},borderRadius:"10px","& .MuiOutlinedInput-root":{borderRadius:"10px","& fieldset":{borderColor:"#555555"},"&:hover fieldset":{borderColor:"#888888"}},mb:2}}),w&&i.jsx(s.Z,{variant:"body2",sx:{color:"red",mt:1},children:w}),i.jsx(a.Z,{variant:"contained",onClick:Z,sx:{textTransform:"none",background:"linear-gradient(90deg, #7206E6, #B54BFF)",color:"#ffffff",height:"54px",borderRadius:"10px",boxShadow:"none",fontSize:"17px",marginTop:"10px","&:hover":{background:"linear-gradient(90deg, #7206E6, #B54BFF)"}},disabled:g,children:g?i.jsx(l.Z,{size:24,sx:{color:"#ffffff"}}):"Send Magic Link"})]})]})]})},b=()=>{let e=(0,d.Z)("(max-width:600px)"),t=(0,A.Z)(m);return e?i.jsx(o.Z,{sx:{padding:2},children:i.jsx(t,{})}):i.jsx(g.Z,{LeftComponent:i.jsx(m,{})})}},62432:(e,t,r)=>{"use strict";r.d(t,{Z:()=>p});var i=r(10326),o=r(16027),s=r(6283),n=r(25609),a=r(46226);let l=(0,r(51426).Z)((0,i.jsx)("path",{d:"M6 17h3l2-4V7H5v6h3zm8 0h3l2-4V7h-6v6h3z"}),"FormatQuote");var d=r(17577);let c=[{src:{src:"/_next/static/media/landing_page_image_1.883ba61a.png",height:1024,width:756,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAYAAAAICAIAAABVpBlvAAAAoUlEQVR42mOwNNRyMlRnYJRQUlNXNbISV9RgcHcwdrAwTPJ2ctVWFlTVkdPUZ7CxMw51MVvaklcb5cnAKqqoosGgbqrTmRe7e1ZbqZ+jnY6WgoEJg6CMUpify5UN07tivCU19O19fBiAoDjO/dzazqIoNwZ1CyklDYbGyTMO7N82rzwkkJchu6K6sr2D4eiV63suXl+3c09rU2Nt74TmKdMAG00wAXeqZ/wAAAAASUVORK5CYII=",blurWidth:6,blurHeight:8},text:"I get to keep track of all my benefits so I never miss out on the stuff that matters most.",name:"Troy Sivan",title:"Associate Manager"},{src:{src:"/_next/static/media/landing_page_image_2.59f16961.png",height:1024,width:756,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAYAAAAICAIAAABVpBlvAAAAo0lEQVR42mOYMakxwMdRRU/Lzs4sNtTTx9WGYeXS6TYOlik2GjGmKpbWJgWJgQwbN62M9rTbXJexsiTBWkXWydmWYcuurZO7G45NqluR6nfs+IkZq9cxHD1zatqS+Vsn1E4I0L3+4MH6nVsZZi9aFJWevXvn3i1rNtd2TQsMcmYAAgc3v8Ub989Zu6+kvgfEl5CWd/MJmrd8Y++0RYUVjQwMDACH00ArwNCIEAAAAABJRU5ErkJggg==",blurWidth:6,blurHeight:8},text:"Now I can track my benefits instantly without having to search through emails. It’s a game–changer!",name:"David Miller",title:"Software Engineer"},{src:{src:"/_next/static/media/landing_page_image_3.e9420572.png",height:1024,width:756,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAYAAAAICAIAAABVpBlvAAAAn0lEQVR42mNQE2YNsRJlYGAwlmL3UuUwFGNmcNbgXlNuNCkz0FtHTpydwUSSlUGBnaHYW29jc3pXoo8KA4ORNBeDOBODl7Ha3JyQQk8LLgYGYS4WBlN5PgYGhjgHw2wPM0FJGQYgsNeQZNbQj7HRzQlyU7WwU1VVZ3CyMrG2ssxxM9M2sTK0tFFW12IwsrF1MDN2tbPXs3TQN7MQk5ACAOjZG1OaugBXAAAAAElFTkSuQmCC",blurWidth:6,blurHeight:8},text:"This is by far the most convenient way to access my benefits. Everything I need is right at my fingertips.",name:"Emily Garcia",title:"HR Specialist"},{src:{src:"/_next/static/media/landing_page_image_4.135a5874.png",height:1024,width:756,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAYAAAAICAIAAABVpBlvAAAAo0lEQVR42gGYAGf/AIl9cqKWioB2bZSGenZuZlpVUQCgmZKMh4KGWD++oI5yaWRtbGkArK+snpmWkl9G0rWooZSGbXZxAH+Af5mbmnpRRLWVh4qGe1JnQgCTfm+Wk456Y1evmYqprrBhbWAAnZWUoaKjsKWg1c/M5O/zwczSALzFy8nT2rnDy9vn7NXh583Y3gCoq6/CzdXDzdTI09nR3ePU3+Q7J1hGmDSqYQAAAABJRU5ErkJggg==",blurWidth:6,blurHeight:8},text:"I love how I can get all my benefits info through Slack. It saves me so much time!",name:"Troy Edward",title:"Associate Manager"}],p=({LeftComponent:e})=>{let[t,r]=(0,d.useState)(0);(0,d.useEffect)(()=>{let e=setInterval(()=>{r(e=>(e+1)%c.length)},5e3);return()=>clearInterval(e)},[]);let p=c[t];return(0,i.jsxs)(o.ZP,{container:!0,style:{height:"95vh",width:"100%"},children:[i.jsx(o.ZP,{item:!0,xs:12,md:6,sx:{bgcolor:"#000000",color:"#ffffff",display:"flex",flexDirection:"column",justifyContent:"center",alignItems:"center",padding:"100px",height:"95vh",overflow:"auto"},children:e}),i.jsx(o.ZP,{item:!0,xs:12,md:6,sx:{display:"flex",justifyContent:"center",alignItems:"center",backgroundColor:"#f8f9fa",padding:"0",position:"relative",overflow:"hidden",height:"95vh"},children:(0,i.jsxs)(s.Z,{sx:{position:"relative",width:"100%",height:"100%",overflow:"hidden"},children:[i.jsx(a.default,{src:p.src,alt:"Profile",layout:"fill",objectFit:"cover",style:{objectPosition:"center"}}),i.jsx(s.Z,{sx:{position:"absolute",bottom:0,left:0,width:"100%",height:"100%",background:"linear-gradient(to top, rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0) 60%)"}}),(0,i.jsxs)(s.Z,{sx:{position:"absolute",bottom:"10%",left:"10%",color:"#ffffff",width:"550px"},children:[i.jsx(l,{sx:{fontSize:70,marginBottom:"10px",opacity:.5,marginLeft:-2}}),i.jsx(n.Z,{variant:"h5",sx:{fontSize:"32px",fontWeight:"bold",lineHeight:"1.5",mb:2},children:p.text}),i.jsx(n.Z,{variant:"h5",sx:{fontSize:"24px",fontWeight:"bold",lineHeight:"1.5",mt:4},children:p.name}),i.jsx(n.Z,{variant:"body2",sx:{fontSize:"20px",fontWeight:"light"},children:p.title})]})]})})]})}},35480:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i});let i=(0,r(68570).createProxy)(String.raw`C:\beno\project_dev\qharmony-frontend-v1\src\app\page.tsx#default`)},30656:(e,t,r)=>{"use strict";r.d(t,{Z:()=>i});let i={src:"/_next/static/media/logo.770bfeee.png",height:300,width:300,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAYAAADED76LAAAA9ElEQVR42i2PMUoDQRiF3z+zu7MbFTaxkfUC0St4ACWdhbbapvACCnaCpU0a09jYiQewFj2AIljY2AoJ7LKZnc3s/P6EPPiq9+DxESR359UgBHNrGxTWQTcOPwtrLwGU6n5c7ySxeQb4vWn9WS/lky50JVH6UlrVjzTFE2iebvbwmJkE37/zPLB79SHfzWIzUUph0LrqScB4qpFEdEhICxm9BeY9BYA9kxJwfTw7IEKfGUsAq06FgNlGtjUSoDTvS1mB3B/BDInoM/KhvQhd8lDb5RGz/pDLVFM+inVc1L49pbXmtmjeiOZQNCGaX4vGXQGY/wM1tG/NQnnUIwAAAABJRU5ErkJggg==",blurWidth:8,blurHeight:8}}};var t=require("../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),i=t.X(0,[8948,1183,6621,9066,1999,3253,928,8097,8522,3141,6027,541,8705,576,401,43],()=>r(91080));module.exports=i})();