(()=>{var e={};e.id=6633,e.ids=[6633],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},27790:e=>{"use strict";e.exports=require("assert")},84770:e=>{"use strict";e.exports=require("crypto")},17702:e=>{"use strict";e.exports=require("events")},92048:e=>{"use strict";e.exports=require("fs")},32615:e=>{"use strict";e.exports=require("http")},35240:e=>{"use strict";e.exports=require("https")},19801:e=>{"use strict";e.exports=require("os")},55315:e=>{"use strict";e.exports=require("path")},76162:e=>{"use strict";e.exports=require("stream")},74026:e=>{"use strict";e.exports=require("string_decoder")},74175:e=>{"use strict";e.exports=require("tty")},17360:e=>{"use strict";e.exports=require("url")},21764:e=>{"use strict";e.exports=require("util")},71568:e=>{"use strict";e.exports=require("zlib")},98061:e=>{"use strict";e.exports=require("node:assert")},92761:e=>{"use strict";e.exports=require("node:async_hooks")},72254:e=>{"use strict";e.exports=require("node:buffer")},40027:e=>{"use strict";e.exports=require("node:console")},6005:e=>{"use strict";e.exports=require("node:crypto")},65714:e=>{"use strict";e.exports=require("node:diagnostics_channel")},15673:e=>{"use strict";e.exports=require("node:events")},88849:e=>{"use strict";e.exports=require("node:http")},42725:e=>{"use strict";e.exports=require("node:http2")},87503:e=>{"use strict";e.exports=require("node:net")},38846:e=>{"use strict";e.exports=require("node:perf_hooks")},39630:e=>{"use strict";e.exports=require("node:querystring")},84492:e=>{"use strict";e.exports=require("node:stream")},31764:e=>{"use strict";e.exports=require("node:tls")},41041:e=>{"use strict";e.exports=require("node:url")},47261:e=>{"use strict";e.exports=require("node:util")},93746:e=>{"use strict";e.exports=require("node:util/types")},24086:e=>{"use strict";e.exports=require("node:worker_threads")},65628:e=>{"use strict";e.exports=require("node:zlib")},85654:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>a.a,__next_app__:()=>p,originalPathname:()=>c,pages:()=>u,routeModule:()=>x,tree:()=>d}),t(1506),t(33709),t(35866);var s=t(23191),o=t(88716),n=t(37922),a=t.n(n),i=t(95231),l={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);t.d(r,l);let d=["",{children:["qHarmonyBot",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,1506)),"C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\qHarmonyBot\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,33709)),"C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,35866,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],u=["C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\qHarmonyBot\\page.tsx"],c="/qHarmonyBot/page",p={require:t,loadChunk:()=>Promise.resolve()},x=new s.AppPageRouteModule({definition:{kind:o.x.APP_PAGE,page:"/qHarmonyBot/page",pathname:"/qHarmonyBot",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},91736:(e,r,t)=>{Promise.resolve().then(t.bind(t,95471))},33198:(e,r,t)=>{"use strict";t.d(r,{Z:()=>v});var s=t(17577),o=t(41135),n=t(88634),a=t(91703),i=t(13643),l=t(2791),d=t(51426),u=t(10326);let c=(0,d.Z)((0,u.jsx)("path",{d:"M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"}),"Person");var p=t(71685),x=t(97898);function m(e){return(0,x.ZP)("MuiAvatar",e)}(0,p.Z)("MuiAvatar",["root","colorDefault","circular","rounded","square","img","fallback"]);var f=t(31121);let g=e=>{let{classes:r,variant:t,colorDefault:s}=e;return(0,n.Z)({root:["root",t,s&&"colorDefault"],img:["img"],fallback:["fallback"]},m,r)},h=(0,a.default)("div",{name:"MuiAvatar",slot:"Root",overridesResolver:(e,r)=>{let{ownerState:t}=e;return[r.root,r[t.variant],t.colorDefault&&r.colorDefault]}})((0,i.Z)(({theme:e})=>({position:"relative",display:"flex",alignItems:"center",justifyContent:"center",flexShrink:0,width:40,height:40,fontFamily:e.typography.fontFamily,fontSize:e.typography.pxToRem(20),lineHeight:1,borderRadius:"50%",overflow:"hidden",userSelect:"none",variants:[{props:{variant:"rounded"},style:{borderRadius:(e.vars||e).shape.borderRadius}},{props:{variant:"square"},style:{borderRadius:0}},{props:{colorDefault:!0},style:{color:(e.vars||e).palette.background.default,...e.vars?{backgroundColor:e.vars.palette.Avatar.defaultBg}:{backgroundColor:e.palette.grey[400],...e.applyStyles("dark",{backgroundColor:e.palette.grey[600]})}}}]}))),y=(0,a.default)("img",{name:"MuiAvatar",slot:"Img",overridesResolver:(e,r)=>r.img})({width:"100%",height:"100%",textAlign:"center",objectFit:"cover",color:"transparent",textIndent:1e4}),b=(0,a.default)(c,{name:"MuiAvatar",slot:"Fallback",overridesResolver:(e,r)=>r.fallback})({width:"75%",height:"75%"}),v=s.forwardRef(function(e,r){let t=(0,l.i)({props:e,name:"MuiAvatar"}),{alt:n,children:a,className:i,component:d="div",slots:c={},slotProps:p={},imgProps:x,sizes:m,src:v,srcSet:q,variant:w="circular",...j}=t,S=null,I={...t,component:d,variant:w},k=function({crossOrigin:e,referrerPolicy:r,src:t,srcSet:o}){let[n,a]=s.useState(!1);return s.useEffect(()=>{if(!t&&!o)return;a(!1);let s=!0,n=new Image;return n.onload=()=>{s&&a("loaded")},n.onerror=()=>{s&&a("error")},n.crossOrigin=e,n.referrerPolicy=r,n.src=t,o&&(n.srcset=o),()=>{s=!1}},[e,r,t,o]),n}({...x,..."function"==typeof p.img?p.img(I):p.img,src:v,srcSet:q}),Z=v||q,_=Z&&"error"!==k;I.colorDefault=!_,delete I.ownerState;let P=g(I),[R,C]=(0,f.Z)("img",{className:P.img,elementType:y,externalForwardedProps:{slots:c,slotProps:{img:{...x,...p.img}}},additionalProps:{alt:n,src:v,srcSet:q,sizes:m},ownerState:I});return S=_?(0,u.jsx)(R,{...C}):a||0===a?a:Z&&n?n[0]:(0,u.jsx)(b,{ownerState:I,className:P.fallback}),(0,u.jsx)(h,{as:d,className:(0,o.Z)(P.root,i),ref:r,...j,ownerState:I,children:S})})},95471:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>w});var s=t(10326),o=t(17577),n=t(25842),a=t(71411),i=t(25886),l=t(25609),d=t(6283),u=t(84979),c=t(33198),p=t(42265),x=t(90541),m=t(59028),f=t(7330),g=t(8106),h=t(46226),y=t(43058),b=t(89223),v=t(12549),q=t(32049);let w=(0,v.Z)(()=>{let e=(0,n.I0)(),r=(0,n.v9)(e=>(0,q.MP)(e)),t=(0,n.v9)(e=>e.user._id),v=(0,n.v9)(e=>e.user.userProfile),w=(0,n.v9)(e=>e.user.selectedFAQQuestion),j=(0,n.v9)(e=>e.qHarmonyBot.chatHistory),S=(0,n.v9)(e=>e.qHarmonyBot.isLoading),[I,k]=(0,o.useState)(""),Z=(0,o.useRef)(null),_=s=>{if(""===s.trim())return;let o={sender:"user",message:s.replace(/\n/g,"<br/>"),timestamp:new Date().toISOString()};e((0,m.Hz)(o)),e((0,m.wt)(!0)),(0,f.b)(e,s,t,r),k("")},P=e=>{if(!e)return"";let[r,t]=e.split(" ");return`${r[0].toUpperCase()}${t?t[0].toUpperCase():""}`},R=()=>{Z.current?.scrollIntoView({behavior:"smooth"})};(0,o.useEffect)(()=>{w&&(_(w),e((0,q.ki)()))},[w,e]),(0,o.useEffect)(()=>{0===j.length&&v.name&&!w&&(e((0,m.wt)(!0)),setTimeout(()=>{let r={sender:"bot",message:`Hey ${v.name}, how can I help you today?`,timestamp:new Date().toISOString()};e((0,m.Hz)(r)),e((0,m.wt)(!1))},2e3))},[j.length,v.name,e,w]),(0,o.useEffect)(()=>{R()},[j]);let C=(0,g.F4)`
    0% { content: ''; }
    25% { content: '.'; }
    50% { content: '..'; }
    75% { content: '...'; }
    100% { content: ''; }
  `,D=["View My Benefits","Enroll Now","Check Time Off","Update My Elections"];return s.jsx(y.Z,{children:(0,s.jsxs)(d.Z,{component:"main",sx:{flexGrow:1,display:"flex",flexDirection:"column",height:"95vh",bgcolor:"#f6f8fc"},children:[s.jsx(l.Z,{variant:"h5",sx:{fontWeight:"bold",p:2},children:"Brea - Your Round-the-Clock Benefits Expert"}),s.jsx(d.Z,{sx:{flexGrow:1,overflow:"auto",p:2},children:(0,s.jsxs)(u.Z,{children:[j.map((e,r)=>(0,s.jsxs)(a.ZP,{sx:{display:"flex",flexDirection:"user"===e.sender?"row-reverse":"row",alignItems:"flex-start"},children:[s.jsx(c.Z,{sx:{bgcolor:"user"===e.sender?"#000":"transparent",mr:"user"===e.sender?0:2,ml:"user"===e.sender?2:0,mt:.5},children:"user"===e.sender?s.jsx(c.Z,{sx:{bgcolor:"black",color:"#ffffff",width:35,height:35,fontSize:"1.2rem",mr:1.5,ml:1.5,display:"flex",alignItems:"center",justifyContent:"center",paddingBottom:"1px",fontWeight:800},children:P(v.name)}):s.jsx(h.default,{src:b.Z,alt:"AI Assistant",style:{borderRadius:"50%",width:"40px",height:"40px"}})}),(0,s.jsxs)(d.Z,{sx:{maxWidth:"80%"},children:[s.jsx(i.Z,{primary:s.jsx("span",{dangerouslySetInnerHTML:{__html:"bot"===e.sender?`${e.message}<br/><small style="color: gray;">AI-generated content—verify before use.</small>`:e.message},style:{whiteSpace:"pre-wrap",wordBreak:"break-word"}}),sx:{bgcolor:"user"===e.sender?"#000":"#fff",color:"user"===e.sender?"#fff":"#000",borderRadius:"user"===e.sender?"16px 16px 4px 16px":"16px 16px 16px 4px",padding:"10px",mb:1}}),"bot"===e.sender&&e.message.includes("how can I help you today?")&&r===j.length-1&&s.jsx(d.Z,{sx:{display:"flex",mt:1},children:D.map(e=>s.jsx(p.Z,{variant:"contained",sx:{mr:1,textTransform:"none",fontSize:"12px",borderRadius:"6px",px:2,py:1,bgcolor:"black",color:"white"},onClick:()=>_(e),children:e},e))})]})]},r)),S&&s.jsx(()=>(0,s.jsxs)(a.ZP,{sx:{display:"flex",alignItems:"flex-start"},children:[s.jsx(h.default,{src:b.Z,alt:"AI Chat",style:{borderRadius:"50%",width:"40px",height:"40px",marginRight:"10px"}}),s.jsx(i.Z,{primary:s.jsx(l.Z,{component:"span",sx:{display:"inline-block",fontSize:"16px","&::after":{content:"''",animation:`${C} 1.5s infinite`,display:"inline-block",width:"16px"}},children:"Brea is typing"}),sx:{bgcolor:"#fff",borderRadius:"20px",p:1.5,mb:1,maxWidth:"80%",mt:-.5}})]}),{}),s.jsx("div",{ref:Z})]})}),s.jsx(d.Z,{sx:{p:2,borderTop:"1px solid #e0e0e0"},children:(0,s.jsxs)(d.Z,{sx:{display:"flex",alignItems:"flex-start",bgcolor:"#fff",borderRadius:"20px",height:"150px",boxShadow:"0 1px 3px rgba(0, 0, 0, 0.1)"},children:[s.jsx(x.Z,{fullWidth:!0,variant:"outlined",placeholder:"Type your message...",value:I,onChange:e=>k(e.target.value),onKeyPress:e=>{"Enter"!==e.key||e.shiftKey||(e.preventDefault(),_(I))},multiline:!0,rows:5,sx:{mr:2,borderRadius:"20px",fontSize:"16px",height:"150px","& .MuiOutlinedInput-root":{padding:"12px",border:"none",display:"flex",alignItems:"flex-start"},"& .MuiOutlinedInput-notchedOutline":{border:"none"}}}),s.jsx(p.Z,{variant:"contained",sx:{bgcolor:I?"#1073ff":"#e0e0e0",color:"#fff",borderRadius:"25px",m:2,p:0,fontSize:"16px",height:"45px",width:"60px",boxShadow:"none",textTransform:"none","&:hover":{bgcolor:I?"#005bb5":"#d0d0d0"}},onClick:()=>_(I),children:"Send"})]})})]})})})},7330:(e,r,t)=>{"use strict";t.d(r,{b:()=>n});var s=t(59028);let o=(0,t(89009).GU)();async function n(e,r,t,n){let a={user_id:t,user_message:r,team_id:n};try{console.log("Sending chat message:",a);let r=await fetch(`${o}/chat`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(a)});if(!r.body)throw Error("Readable stream not supported");let t=r.body.getReader(),n=new TextDecoder("utf-8"),i={sender:"bot",message:"",timestamp:new Date().toISOString()};for(e((0,s.wt)(!0));;){let{done:r,value:o}=await t.read();if(r)break;let a=n.decode(o,{stream:!0});e((0,s.wt)(!1)),console.log("Chunk:",a),i.message+=a,e((0,s.Hz)({sender:"bot",message:a,timestamp:new Date().toISOString()}))}}catch(t){console.error("Error sending chat message:",t);let r={sender:"bot",message:"Sorry, I encountered an error while processing your request.",timestamp:new Date().toISOString()};e((0,s.Hz)(r)),e((0,s.wt)(!1))}}},89009:(e,r,t)=>{"use strict";t.d(r,{GU:()=>n,bR:()=>s,n5:()=>o});let s=()=>"http://localhost:8080",o=()=>{let e="userid1",r="userId",t=localStorage.getItem(e)||localStorage.getItem(r);return(console.log("\uD83D\uDD0D getUserId debug:",{primaryKey:e,altKey:r,primaryValue:localStorage.getItem(e),altValue:localStorage.getItem(r),finalUserId:t}),t)?t:(console.error("❌ User ID not found in localStorage"),"default-user")},n=()=>"https://bot.benosphere.com"},1506:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(68570).createProxy)(String.raw`C:\beno\project_dev\qharmony-frontend-v1\src\app\qHarmonyBot\page.tsx#default`)}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[8948,1183,6621,9066,1999,3253,928,8097,8522,3141,541,576,6305,401,2549],()=>t(85654));module.exports=s})();