(()=>{var e={};e.id=2649,e.ids=[2649],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},27790:e=>{"use strict";e.exports=require("assert")},84770:e=>{"use strict";e.exports=require("crypto")},17702:e=>{"use strict";e.exports=require("events")},92048:e=>{"use strict";e.exports=require("fs")},32615:e=>{"use strict";e.exports=require("http")},35240:e=>{"use strict";e.exports=require("https")},19801:e=>{"use strict";e.exports=require("os")},55315:e=>{"use strict";e.exports=require("path")},76162:e=>{"use strict";e.exports=require("stream")},74026:e=>{"use strict";e.exports=require("string_decoder")},74175:e=>{"use strict";e.exports=require("tty")},17360:e=>{"use strict";e.exports=require("url")},21764:e=>{"use strict";e.exports=require("util")},71568:e=>{"use strict";e.exports=require("zlib")},98061:e=>{"use strict";e.exports=require("node:assert")},92761:e=>{"use strict";e.exports=require("node:async_hooks")},72254:e=>{"use strict";e.exports=require("node:buffer")},40027:e=>{"use strict";e.exports=require("node:console")},6005:e=>{"use strict";e.exports=require("node:crypto")},65714:e=>{"use strict";e.exports=require("node:diagnostics_channel")},15673:e=>{"use strict";e.exports=require("node:events")},88849:e=>{"use strict";e.exports=require("node:http")},42725:e=>{"use strict";e.exports=require("node:http2")},87503:e=>{"use strict";e.exports=require("node:net")},38846:e=>{"use strict";e.exports=require("node:perf_hooks")},39630:e=>{"use strict";e.exports=require("node:querystring")},84492:e=>{"use strict";e.exports=require("node:stream")},31764:e=>{"use strict";e.exports=require("node:tls")},41041:e=>{"use strict";e.exports=require("node:url")},47261:e=>{"use strict";e.exports=require("node:util")},93746:e=>{"use strict";e.exports=require("node:util/types")},24086:e=>{"use strict";e.exports=require("node:worker_threads")},65628:e=>{"use strict";e.exports=require("node:zlib")},9412:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>a.a,__next_app__:()=>u,originalPathname:()=>c,pages:()=>p,routeModule:()=>x,tree:()=>d}),t(55102),t(33709),t(35866);var o=t(23191),s=t(88716),n=t(37922),a=t.n(n),i=t(95231),l={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);t.d(r,l);let d=["",{children:["team-members",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,55102)),"C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\team-members\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,33709)),"C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,35866,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],p=["C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\team-members\\page.tsx"],c="/team-members/page",u={require:t,loadChunk:()=>Promise.resolve()},x=new o.AppPageRouteModule({definition:{kind:s.x.APP_PAGE,page:"/team-members/page",pathname:"/team-members",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},57996:(e,r,t)=>{Promise.resolve().then(t.bind(t,49795))},75887:(e,r,t)=>{"use strict";t.d(r,{Z:()=>n});var o=t(51426),s=t(10326);let n=(0,o.Z)((0,s.jsx)("path",{d:"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2m-2 15-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8z"}),"CheckCircle")},36690:(e,r,t)=>{"use strict";t.d(r,{Z:()=>n});var o=t(51426),s=t(10326);let n=(0,o.Z)((0,s.jsx)("path",{d:"M19 6.41 17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"}),"Close")},37104:(e,r,t)=>{"use strict";t.d(r,{Z:()=>g});var o=t(17577),s=t(41135),n=t(88634),a=t(27080),i=t(91703),l=t(2791),d=t(25609),p=t(71685),c=t(97898);function u(e){return(0,c.ZP)("MuiDialogContentText",e)}(0,p.Z)("MuiDialogContentText",["root"]);var x=t(10326);let m=e=>{let{classes:r}=e,t=(0,n.Z)({root:["root"]},u,r);return{...r,...t}},f=(0,i.default)(d.Z,{shouldForwardProp:e=>(0,a.Z)(e)||"classes"===e,name:"MuiDialogContentText",slot:"Root",overridesResolver:(e,r)=>r.root})({}),g=o.forwardRef(function(e,r){let t=(0,l.i)({props:e,name:"MuiDialogContentText"}),{children:o,className:n,...a}=t,i=m(a);return(0,x.jsx)(f,{component:"p",variant:"body1",color:"textSecondary",ref:r,ownerState:a,className:(0,s.Z)(i.root,n),...t,classes:i})})},48260:(e,r,t)=>{"use strict";t.d(r,{Z:()=>C});var o=t(17577),s=t(41135),n=t(88634),a=t(87816),i=t(44823),l=t(91703),d=t(13643),p=t(40955),c=t(2791),u=t(49006),x=t(98139),m=t(54641),f=t(71685),g=t(97898);function h(e){return(0,g.ZP)("MuiIconButton",e)}let b=(0,f.Z)("MuiIconButton",["root","disabled","colorInherit","colorPrimary","colorSecondary","colorError","colorInfo","colorSuccess","colorWarning","edgeStart","edgeEnd","sizeSmall","sizeMedium","sizeLarge","loading","loadingIndicator","loadingWrapper"]);var v=t(10326);let y=e=>{let{classes:r,disabled:t,color:o,edge:s,size:a,loading:i}=e,l={root:["root",i&&"loading",t&&"disabled","default"!==o&&`color${(0,m.Z)(o)}`,s&&`edge${(0,m.Z)(s)}`,`size${(0,m.Z)(a)}`],loadingIndicator:["loadingIndicator"],loadingWrapper:["loadingWrapper"]};return(0,n.Z)(l,h,r)},Z=(0,l.default)(u.Z,{name:"MuiIconButton",slot:"Root",overridesResolver:(e,r)=>{let{ownerState:t}=e;return[r.root,t.loading&&r.loading,"default"!==t.color&&r[`color${(0,m.Z)(t.color)}`],t.edge&&r[`edge${(0,m.Z)(t.edge)}`],r[`size${(0,m.Z)(t.size)}`]]}})((0,d.Z)(({theme:e})=>({textAlign:"center",flex:"0 0 auto",fontSize:e.typography.pxToRem(24),padding:8,borderRadius:"50%",color:(e.vars||e).palette.action.active,transition:e.transitions.create("background-color",{duration:e.transitions.duration.shortest}),variants:[{props:e=>!e.disableRipple,style:{"--IconButton-hoverBg":e.vars?`rgba(${e.vars.palette.action.activeChannel} / ${e.vars.palette.action.hoverOpacity})`:(0,i.Fq)(e.palette.action.active,e.palette.action.hoverOpacity),"&:hover":{backgroundColor:"var(--IconButton-hoverBg)","@media (hover: none)":{backgroundColor:"transparent"}}}},{props:{edge:"start"},style:{marginLeft:-12}},{props:{edge:"start",size:"small"},style:{marginLeft:-3}},{props:{edge:"end"},style:{marginRight:-12}},{props:{edge:"end",size:"small"},style:{marginRight:-3}}]})),(0,d.Z)(({theme:e})=>({variants:[{props:{color:"inherit"},style:{color:"inherit"}},...Object.entries(e.palette).filter((0,p.Z)()).map(([r])=>({props:{color:r},style:{color:(e.vars||e).palette[r].main}})),...Object.entries(e.palette).filter((0,p.Z)()).map(([r])=>({props:{color:r},style:{"--IconButton-hoverBg":e.vars?`rgba(${(e.vars||e).palette[r].mainChannel} / ${e.vars.palette.action.hoverOpacity})`:(0,i.Fq)((e.vars||e).palette[r].main,e.palette.action.hoverOpacity)}})),{props:{size:"small"},style:{padding:5,fontSize:e.typography.pxToRem(18)}},{props:{size:"large"},style:{padding:12,fontSize:e.typography.pxToRem(28)}}],[`&.${b.disabled}`]:{backgroundColor:"transparent",color:(e.vars||e).palette.action.disabled},[`&.${b.loading}`]:{color:"transparent"}}))),j=(0,l.default)("span",{name:"MuiIconButton",slot:"LoadingIndicator",overridesResolver:(e,r)=>r.loadingIndicator})(({theme:e})=>({display:"none",position:"absolute",visibility:"visible",top:"50%",left:"50%",transform:"translate(-50%, -50%)",color:(e.vars||e).palette.action.disabled,variants:[{props:{loading:!0},style:{display:"flex"}}]})),C=o.forwardRef(function(e,r){let t=(0,c.i)({props:e,name:"MuiIconButton"}),{edge:o=!1,children:n,className:i,color:l="default",disabled:d=!1,disableFocusRipple:p=!1,size:u="medium",id:m,loading:f=null,loadingIndicator:g,...h}=t,b=(0,a.Z)(m),C=g??(0,v.jsx)(x.Z,{"aria-labelledby":b,color:"inherit",size:16}),S={...t,edge:o,color:l,disabled:d,disableFocusRipple:p,loading:f,loadingIndicator:C,size:u},P=y(S);return(0,v.jsxs)(Z,{id:f?b:m,className:(0,s.Z)(P.root,i),centerRipple:!0,focusRipple:!p,disabled:d||f,ref:r,...h,ownerState:S,children:["boolean"==typeof f&&(0,v.jsx)("span",{className:P.loadingWrapper,style:{display:"contents"},children:(0,v.jsx)(j,{className:P.loadingIndicator,ownerState:S,children:f&&C})}),n]})})},49795:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>E});var o=t(10326),s=t(17577),n=t(6283),a=t(16027),i=t(25609),l=t(42265),d=t(98139),p=t(33198),c=t(48260),u=t(99207),x=t(69995),m=t(98117),f=t(28591),g=t(37104),h=t(90541),b=t(10163),v=t(12549),y=t(25842),Z=t(94638),j=t(36690),C=t(75887);let S=({open:e,onClose:r,member:t})=>{let n=(0,y.I0)(),[i,p]=(0,s.useState)(""),[u,g]=(0,s.useState)(""),[v,S]=(0,s.useState)(""),[P,q]=(0,s.useState)(""),[k,I]=(0,s.useState)(""),[w,R]=(0,s.useState)(""),[E,z]=(0,s.useState)(""),[T,_]=(0,s.useState)(!1),[M,F]=(0,s.useState)(""),[L,N]=(0,s.useState)({firstName:!1,lastName:!1,email:!1,phoneNumber:!1}),W=()=>{let e={firstName:!i,lastName:!u,email:!v,phoneNumber:!P};return N(e),!Object.values(e).some(e=>e)};(0,s.useEffect)(()=>{t?(p(t.name.split(" ")[0]),g(t.name.split(" ")[1]),S(t.email),q(t.details?.phoneNumber||""),I(t.details?.department||""),R(t.details?.title||"")):(p(""),g(""),S(""),q(""),I(""),R(""))},[t]);let $=async()=>{if(!W()){F("Please fill out all required fields.");return}_(!0),F("");let e={name:`${i} ${u}`,email:v,phoneNumber:P,department:k,title:w};t?(console.log("Editing team member:",e),await (0,Z.Nq)(n,t._id,e)):(console.log("Adding team member:",e),await (0,Z.yu)(n,[e])),await (0,Z.TQ)(n),_(!1),z(t?"Team member updated successfully!":"Team member added successfully!"),setTimeout(()=>{t?(z(""),r()):(z(""),p(""),g(""),S(""),q(""),I(""),R(""))},1500)};return(0,o.jsxs)(x.Z,{open:e,onClose:r,PaperProps:{style:{borderRadius:"16px",boxShadow:"0px 10px 30px rgba(0, 0, 0, 0.1)",padding:"5px",width:"550px"}},children:[(0,o.jsxs)(m.Z,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",fontWeight:"bold",fontSize:"1.5rem"},children:[t?"Edit Team Member":"Add Team Member",o.jsx(c.Z,{onClick:r,children:o.jsx(j.Z,{})})]}),(0,o.jsxs)(f.Z,{children:[(0,o.jsxs)(a.ZP,{container:!0,spacing:3,sx:{marginBottom:"16px",marginTop:"0px"},children:[o.jsx(a.ZP,{item:!0,xs:12,children:o.jsx(h.Z,{fullWidth:!0,label:"First name",variant:"outlined",value:i,onChange:e=>p(e.target.value),required:!0,error:L.firstName,InputProps:{style:{borderRadius:"8px",backgroundColor:"#f9f9f9",height:"48px"}},InputLabelProps:{shrink:!0,style:{fontSize:"1rem"}},sx:{alignItems:"flex-start"}})}),o.jsx(a.ZP,{item:!0,xs:12,children:o.jsx(h.Z,{fullWidth:!0,label:"Last name",variant:"outlined",value:u,onChange:e=>g(e.target.value),required:!0,error:L.lastName,InputProps:{style:{borderRadius:"8px",backgroundColor:"#f9f9f9",height:"48px"}},InputLabelProps:{shrink:!0,style:{fontSize:"1rem"}},sx:{alignItems:"flex-start"}})}),o.jsx(a.ZP,{item:!0,xs:12,children:o.jsx(h.Z,{fullWidth:!0,label:"Email",variant:"outlined",value:v,onChange:e=>S(e.target.value),required:!0,error:L.email,InputProps:{style:{borderRadius:"8px",backgroundColor:"#f9f9f9",height:"48px"}},InputLabelProps:{shrink:!0,style:{fontSize:"1rem"}},sx:{alignItems:"flex-start"},disabled:!!t})}),o.jsx(a.ZP,{item:!0,xs:12,children:o.jsx(h.Z,{fullWidth:!0,label:"Phone number",variant:"outlined",value:P,onChange:e=>q(e.target.value),required:!0,error:L.phoneNumber,InputProps:{style:{borderRadius:"8px",backgroundColor:"#f9f9f9",height:"48px"}},InputLabelProps:{shrink:!0,style:{fontSize:"1rem"}},sx:{alignItems:"flex-start"}})}),o.jsx(a.ZP,{item:!0,xs:12,children:o.jsx(h.Z,{fullWidth:!0,label:"Department",variant:"outlined",value:k,onChange:e=>I(e.target.value),InputProps:{style:{borderRadius:"8px",backgroundColor:"#f9f9f9",height:"48px"}},InputLabelProps:{shrink:!0,style:{fontSize:"1rem"}},sx:{alignItems:"flex-start"}})}),o.jsx(a.ZP,{item:!0,xs:12,children:o.jsx(h.Z,{fullWidth:!0,label:"Title",variant:"outlined",value:w,onChange:e=>R(e.target.value),InputProps:{style:{borderRadius:"8px",backgroundColor:"#f9f9f9",height:"48px"}},InputLabelProps:{shrink:!0,style:{fontSize:"1rem"}},sx:{alignItems:"flex-start"}})})]}),T&&o.jsx(d.Z,{}),M&&o.jsx("div",{style:{color:"red",marginTop:"10px",display:"flex",alignItems:"center"},children:M}),E&&(0,o.jsxs)("div",{style:{color:"green",marginTop:"10px",display:"flex",alignItems:"center"},children:[o.jsx(C.Z,{style:{marginRight:"5px"}}),E]})]}),o.jsx(b.Z,{sx:{padding:"16px"},children:o.jsx(l.Z,{onClick:$,sx:{color:"#ffffff",backgroundColor:"#000000",borderRadius:"12px",padding:"8px 24px",textTransform:"none",fontWeight:"bold","&:hover":{backgroundColor:"#333333"}},disabled:T,children:T?t?"Saving...":"Adding...":t?"Save Changes":"Add"})})]})};var P=t(43058),q=t(31870);let k=(0,t(51426).Z)((0,o.jsx)("path",{d:"M14.59 8 12 10.59 9.41 8 8 9.41 10.59 12 8 14.59 9.41 16 12 13.41 14.59 16 16 14.59 13.41 12 16 9.41zM12 2C6.47 2 2 6.47 2 12s4.47 10 10 10 10-4.47 10-10S17.53 2 12 2m0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8"}),"HighlightOff"),I=["#FFB6C1","#FF6347","#FFD700","#90EE90","#00CED1","#1E90FF","#BA55D3"],w=e=>{let[r,t]=e.split(" ");return`${r[0]}${t?t[0]:""}`},R=e=>I[e%I.length],E=(0,v.Z)(()=>{let e=(0,y.I0)(),r=(0,q.C)(e=>e.user._id),t=(0,y.v9)(e=>e.company.companyTeamMembers),[v,j]=(0,s.useState)(!1),[C,I]=(0,s.useState)(null),[E,z]=(0,s.useState)(!1),[T,_]=(0,s.useState)(null),[M,F]=(0,s.useState)(""),[L,N]=(0,s.useState)(null),[W,$]=(0,s.useState)(!1),[A,B]=(0,s.useState)(!0),[D,O]=(0,s.useState)(null),G=()=>j(!0),Q=e=>{console.log("Editing team member:",e),I(e),G()},H=async(e,r)=>{N(e),console.log("Response from sendLoginLinkToEmployee:",await (0,Z.mb)(e,r)),N(null)},J=(e,r)=>{_({id:e,companyId:r}),z(!0)},X=()=>{z(!1),_(null),F("")},K=async()=>{T&&($(!0),await (0,Z.JZ)(T.id,T.companyId)?(await (0,Z.TQ)(e),X()):console.error("Failed to offboard employee"),$(!1))},U=async t=>{O(t),await (0,Z.gt)(t,r),await (0,Z.TQ)(e),O(null),X()};return(0,s.useEffect)(()=>{(async()=>{B(!0),await (0,Z.TQ)(e),B(!1)})()},[e]),o.jsx(P.Z,{children:(0,o.jsxs)(n.Z,{sx:{bgcolor:"#F5F6FA",p:4,width:"100%",height:"95vh",overflow:"auto"},children:[(0,o.jsxs)(a.ZP,{container:!0,alignItems:"center",justifyContent:"space-between",sx:{mb:2},children:[o.jsx(a.ZP,{item:!0,children:o.jsx(i.Z,{variant:"h5",children:"Team Members"})}),o.jsx(a.ZP,{item:!0,children:o.jsx(l.Z,{variant:"contained",color:"primary",onClick:()=>{G()},sx:{backgroundColor:"#000000",textTransform:"none",borderRadius:"6px"},children:"Add new member"})})]}),o.jsx(n.Z,{sx:{bgcolor:"#ffffff",borderRadius:"12px",width:"100%",p:2,boxShadow:"0px 4px 12px rgba(0, 0, 0, 0.1)"},children:A?o.jsx(n.Z,{sx:{display:"flex",justifyContent:"center",p:4},children:o.jsx(d.Z,{})}):t.map((e,s)=>(0,o.jsxs)(n.Z,{children:[(0,o.jsxs)(a.ZP,{container:!0,alignItems:"center",spacing:2,children:[(0,o.jsxs)(a.ZP,{item:!0,xs:3,container:!0,alignItems:"center",children:[o.jsx(p.Z,{sx:{bgcolor:R(s),color:"#ffffff",width:48,height:48,fontSize:"1.2rem",mr:2},children:w(e.name)}),(0,o.jsxs)(n.Z,{children:[o.jsx(i.Z,{variant:"body1",sx:{fontWeight:"bold"},children:e.name}),!e.isActivated&&o.jsx(i.Z,{variant:"body2",color:"error",children:"Pending Activation"})]})]}),o.jsx(a.ZP,{item:!0,xs:3,children:o.jsx(i.Z,{variant:"body2",children:e.role})}),o.jsx(a.ZP,{item:!0,xs:3,children:o.jsx(i.Z,{variant:"body2",children:e.email})}),o.jsx(a.ZP,{item:!0,xs:3,container:!0,justifyContent:"flex-end",children:e.isDisabled?(0,o.jsxs)(o.Fragment,{children:[o.jsx(i.Z,{variant:"body2",color:"error",sx:{mr:2},children:"Disabled"}),D===e._id?o.jsx(d.Z,{size:24}):o.jsx(l.Z,{variant:"contained",onClick:()=>U(e._id),sx:{backgroundColor:"#f0f0f0",borderRadius:"8px",textTransform:"none",color:"#000",padding:"4px",marginRight:3,boxShadow:"none",border:"none","&:hover":{backgroundColor:"#E0E0E0",boxShadow:"none"}},children:"Enable"})]}):(0,o.jsxs)(o.Fragment,{children:[o.jsx(l.Z,{variant:"contained",onClick:()=>Q(e),sx:{backgroundColor:"#f0f0f0",borderRadius:"8px",textTransform:"none",color:"#000",padding:"4px",marginRight:3,boxShadow:"none",border:"none","&:hover":{backgroundColor:"#E0E0E0",boxShadow:"none"}},children:"Edit"}),L===e._id?o.jsx(d.Z,{size:24}):o.jsx(l.Z,{variant:"contained",onClick:()=>H(e._id,e.companyId),sx:{backgroundColor:"#f0f0f0",borderRadius:"8px",textTransform:"none",color:"#000",padding:"4px",marginRight:3,boxShadow:"none",border:"none","&:hover":{backgroundColor:"#E0E0E0",boxShadow:"none"}},children:"Send Login Link"}),W&&T.id===e._id?o.jsx(d.Z,{size:24}):e._id!==r&&o.jsx(c.Z,{onClick:()=>J(e._id,e.companyId),sx:{backgroundColor:"#f0f0f0",borderRadius:"8px",textTransform:"none",color:"#000",padding:"4px",marginRight:3,boxShadow:"none",border:"none","&:hover":{backgroundColor:"#E0E0E0",boxShadow:"none"}},children:o.jsx(k,{})})]})})]}),s<t.length-1&&o.jsx(u.Z,{sx:{my:2}})]},s))}),o.jsx(S,{open:v,onClose:()=>{j(!1),I(null)},member:C}),(0,o.jsxs)(x.Z,{open:E,onClose:X,PaperProps:{style:{borderRadius:"16px",boxShadow:"0px 10px 30px rgba(0, 0, 0, 0.1)"}},children:[o.jsx(m.Z,{sx:{fontWeight:"bold",fontSize:"1.5rem",color:"#000000"},children:"Confirm Deletion"}),(0,o.jsxs)(f.Z,{children:[(0,o.jsxs)(g.Z,{sx:{color:"#6c757d",fontSize:"1rem",mb:2},children:['To confirm deletion, please type "',o.jsx("strong",{style:{color:"black"},children:"remove employee"}),'" in the box below.']}),o.jsx(h.Z,{autoFocus:!0,fullWidth:!0,variant:"outlined",value:M,onChange:e=>F(e.target.value),InputProps:{style:{borderRadius:"12px",backgroundColor:"#f9f9f9"}}})]}),(0,o.jsxs)(b.Z,{sx:{padding:"16px"},children:[o.jsx(l.Z,{onClick:X,sx:{color:"#6c757d",backgroundColor:"#ffffff",borderRadius:"8px",padding:"8px 16px",textTransform:"none",boxShadow:"none","&:hover":{backgroundColor:"#f0f0f0"}},children:"Cancel"}),o.jsx(l.Z,{onClick:K,disabled:"remove employee"!==M||W,sx:{color:"#ffffff",backgroundColor:"remove employee"===M?"#1073ff":"#9E9E9E",borderRadius:"8px",padding:"8px 16px",textTransform:"none","&:hover":{backgroundColor:"remove employee"===M?"#0d62d3":"#9E9E9E"}},children:W?o.jsx(d.Z,{size:24,sx:{color:"#ffffff"}}):"Confirm"})]})]})]})})})},55102:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>o});let o=(0,t(68570).createProxy)(String.raw`C:\beno\project_dev\qharmony-frontend-v1\src\app\team-members\page.tsx#default`)}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),o=r.X(0,[8948,1183,6621,9066,1999,3253,928,8097,8522,3141,6027,541,2142,576,6305,401,2549],()=>t(9412));module.exports=o})();