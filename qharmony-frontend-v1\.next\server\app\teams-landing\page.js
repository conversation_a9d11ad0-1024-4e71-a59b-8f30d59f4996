(()=>{var e={};e.id=4949,e.ids=[4949],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},27790:e=>{"use strict";e.exports=require("assert")},84770:e=>{"use strict";e.exports=require("crypto")},17702:e=>{"use strict";e.exports=require("events")},92048:e=>{"use strict";e.exports=require("fs")},32615:e=>{"use strict";e.exports=require("http")},35240:e=>{"use strict";e.exports=require("https")},19801:e=>{"use strict";e.exports=require("os")},55315:e=>{"use strict";e.exports=require("path")},76162:e=>{"use strict";e.exports=require("stream")},74026:e=>{"use strict";e.exports=require("string_decoder")},74175:e=>{"use strict";e.exports=require("tty")},17360:e=>{"use strict";e.exports=require("url")},21764:e=>{"use strict";e.exports=require("util")},71568:e=>{"use strict";e.exports=require("zlib")},98061:e=>{"use strict";e.exports=require("node:assert")},92761:e=>{"use strict";e.exports=require("node:async_hooks")},72254:e=>{"use strict";e.exports=require("node:buffer")},40027:e=>{"use strict";e.exports=require("node:console")},6005:e=>{"use strict";e.exports=require("node:crypto")},65714:e=>{"use strict";e.exports=require("node:diagnostics_channel")},15673:e=>{"use strict";e.exports=require("node:events")},88849:e=>{"use strict";e.exports=require("node:http")},42725:e=>{"use strict";e.exports=require("node:http2")},87503:e=>{"use strict";e.exports=require("node:net")},38846:e=>{"use strict";e.exports=require("node:perf_hooks")},39630:e=>{"use strict";e.exports=require("node:querystring")},84492:e=>{"use strict";e.exports=require("node:stream")},31764:e=>{"use strict";e.exports=require("node:tls")},41041:e=>{"use strict";e.exports=require("node:url")},47261:e=>{"use strict";e.exports=require("node:util")},93746:e=>{"use strict";e.exports=require("node:util/types")},24086:e=>{"use strict";e.exports=require("node:worker_threads")},65628:e=>{"use strict";e.exports=require("node:zlib")},18714:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>s.a,__next_app__:()=>d,originalPathname:()=>c,pages:()=>p,routeModule:()=>h,tree:()=>u}),r(87067),r(33709),r(35866);var n=r(23191),o=r(88716),i=r(37922),s=r.n(i),a=r(95231),l={};for(let e in a)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>a[e]);r.d(t,l);let u=["",{children:["teams-landing",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,87067)),"C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\teams-landing\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,33709)),"C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,35866,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],p=["C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\teams-landing\\page.tsx"],c="/teams-landing/page",d={require:r,loadChunk:()=>Promise.resolve()},h=new n.AppPageRouteModule({definition:{kind:o.x.APP_PAGE,page:"/teams-landing/page",pathname:"/teams-landing",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:u}})},30798:(e,t,r)=>{Promise.resolve().then(r.bind(r,41091))},6283:(e,t,r)=>{"use strict";r.d(t,{Z:()=>u});var n=r(96830),o=r(5028),i=r(5283),s=r(14750);let a=(0,r(71685).Z)("MuiBox",["root"]),l=(0,i.Z)(),u=(0,n.default)({themeId:s.Z,defaultTheme:l,defaultClassName:a.root,generateClassName:o.Z.generate})},2791:(e,t,r)=>{"use strict";r.d(t,{i:()=>o}),r(17577);var n=r(51387);function o(e){return(0,n.i)(e)}r(10326)},52188:(e,t,r)=>{"use strict";r.d(t,{Z:()=>C});var n=r(17577),o=r(41135),i=r(44823),s=r(88634),a=r(64263),l=r(54641),u=r(91703),p=r(23743),c=r(13643),d=r(40955),h=r(2791),m=r(25609),x=r(71685),y=r(97898);function f(e){return(0,y.ZP)("MuiLink",e)}let g=(0,x.Z)("MuiLink",["root","underlineNone","underlineHover","underlineAlways","button","focusVisible"]);var v=r(38482);let b=({theme:e,ownerState:t})=>{let r=t.color,n=(0,v.DW)(e,`palette.${r}.main`,!1)||(0,v.DW)(e,`palette.${r}`,!1)||t.color,o=(0,v.DW)(e,`palette.${r}.mainChannel`)||(0,v.DW)(e,`palette.${r}Channel`);return"vars"in e&&o?`rgba(${o} / 0.4)`:(0,i.Fq)(n,.4)};var Z=r(10326);let q={primary:!0,secondary:!0,error:!0,info:!0,success:!0,warning:!0,textPrimary:!0,textSecondary:!0,textDisabled:!0},j=e=>{let{classes:t,component:r,focusVisible:n,underline:o}=e,i={root:["root",`underline${(0,l.Z)(o)}`,"button"===r&&"button",n&&"focusVisible"]};return(0,s.Z)(i,f,t)},w=(0,u.default)(m.Z,{name:"MuiLink",slot:"Root",overridesResolver:(e,t)=>{let{ownerState:r}=e;return[t.root,t[`underline${(0,l.Z)(r.underline)}`],"button"===r.component&&t.button]}})((0,c.Z)(({theme:e})=>({variants:[{props:{underline:"none"},style:{textDecoration:"none"}},{props:{underline:"hover"},style:{textDecoration:"none","&:hover":{textDecoration:"underline"}}},{props:{underline:"always"},style:{textDecoration:"underline","&:hover":{textDecorationColor:"inherit"}}},{props:({underline:e,ownerState:t})=>"always"===e&&"inherit"!==t.color,style:{textDecorationColor:"var(--Link-underlineColor)"}},...Object.entries(e.palette).filter((0,d.Z)()).map(([t])=>({props:{underline:"always",color:t},style:{"--Link-underlineColor":e.vars?`rgba(${e.vars.palette[t].mainChannel} / 0.4)`:(0,i.Fq)(e.palette[t].main,.4)}})),{props:{underline:"always",color:"textPrimary"},style:{"--Link-underlineColor":e.vars?`rgba(${e.vars.palette.text.primaryChannel} / 0.4)`:(0,i.Fq)(e.palette.text.primary,.4)}},{props:{underline:"always",color:"textSecondary"},style:{"--Link-underlineColor":e.vars?`rgba(${e.vars.palette.text.secondaryChannel} / 0.4)`:(0,i.Fq)(e.palette.text.secondary,.4)}},{props:{underline:"always",color:"textDisabled"},style:{"--Link-underlineColor":(e.vars||e).palette.text.disabled}},{props:{component:"button"},style:{position:"relative",WebkitTapHighlightColor:"transparent",backgroundColor:"transparent",outline:0,border:0,margin:0,borderRadius:0,padding:0,cursor:"pointer",userSelect:"none",verticalAlign:"middle",MozAppearance:"none",WebkitAppearance:"none","&::-moz-focus-inner":{borderStyle:"none"},[`&.${g.focusVisible}`]:{outline:"auto"}}}]}))),C=n.forwardRef(function(e,t){let r=(0,h.i)({props:e,name:"MuiLink"}),i=(0,p.default)(),{className:s,color:l="primary",component:u="a",onBlur:c,onFocus:d,TypographyClasses:m,underline:x="always",variant:y="inherit",sx:f,...g}=r,[v,C]=n.useState(!1),k={...r,color:l,component:u,focusVisible:v,underline:x,variant:y},_=j(k);return(0,Z.jsx)(w,{color:l,className:(0,o.Z)(_.root,s),classes:m,component:u,onBlur:e=>{(0,a.Z)(e.target)||C(!1),c&&c(e)},onFocus:e=>{(0,a.Z)(e.target)&&C(!0),d&&d(e)},ref:t,ownerState:k,variant:y,...g,sx:[...void 0===q[l]?[{color:l}]:[],...Array.isArray(f)?f:[f]],style:{...g.style,..."always"===x&&"inherit"!==l&&!q[l]&&{"--Link-underlineColor":b({theme:i,ownerState:k})}}})})},25609:(e,t,r)=>{"use strict";r.d(t,{Z:()=>v});var n=r(17577),o=r(41135),i=r(88634),s=r(83158),a=r(91703),l=r(13643),u=r(2791),p=r(54641),c=r(40955),d=r(79986),h=r(10326);let m={primary:!0,secondary:!0,error:!0,info:!0,success:!0,warning:!0,textPrimary:!0,textSecondary:!0,textDisabled:!0},x=(0,s.u7)(),y=e=>{let{align:t,gutterBottom:r,noWrap:n,paragraph:o,variant:s,classes:a}=e,l={root:["root",s,"inherit"!==e.align&&`align${(0,p.Z)(t)}`,r&&"gutterBottom",n&&"noWrap",o&&"paragraph"]};return(0,i.Z)(l,d.f,a)},f=(0,a.default)("span",{name:"MuiTypography",slot:"Root",overridesResolver:(e,t)=>{let{ownerState:r}=e;return[t.root,r.variant&&t[r.variant],"inherit"!==r.align&&t[`align${(0,p.Z)(r.align)}`],r.noWrap&&t.noWrap,r.gutterBottom&&t.gutterBottom,r.paragraph&&t.paragraph]}})((0,l.Z)(({theme:e})=>({margin:0,variants:[{props:{variant:"inherit"},style:{font:"inherit",lineHeight:"inherit",letterSpacing:"inherit"}},...Object.entries(e.typography).filter(([e,t])=>"inherit"!==e&&t&&"object"==typeof t).map(([e,t])=>({props:{variant:e},style:t})),...Object.entries(e.palette).filter((0,c.Z)()).map(([t])=>({props:{color:t},style:{color:(e.vars||e).palette[t].main}})),...Object.entries(e.palette?.text||{}).filter(([,e])=>"string"==typeof e).map(([t])=>({props:{color:`text${(0,p.Z)(t)}`},style:{color:(e.vars||e).palette.text[t]}})),{props:({ownerState:e})=>"inherit"!==e.align,style:{textAlign:"var(--Typography-textAlign)"}},{props:({ownerState:e})=>e.noWrap,style:{overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap"}},{props:({ownerState:e})=>e.gutterBottom,style:{marginBottom:"0.35em"}},{props:({ownerState:e})=>e.paragraph,style:{marginBottom:16}}]}))),g={h1:"h1",h2:"h2",h3:"h3",h4:"h4",h5:"h5",h6:"h6",subtitle1:"h6",subtitle2:"h6",body1:"p",body2:"p",inherit:"p"},v=n.forwardRef(function(e,t){let{color:r,...n}=(0,u.i)({props:e,name:"MuiTypography"}),i=!m[r],s=x({...n,...i&&{color:r}}),{align:a="inherit",className:l,component:p,gutterBottom:c=!1,noWrap:d=!1,paragraph:v=!1,variant:b="body1",variantMapping:Z=g,...q}=s,j={...s,align:a,color:r,className:l,component:p,gutterBottom:c,noWrap:d,paragraph:v,variant:b,variantMapping:Z},w=p||(v?"p":Z[b]||g[b])||"span",C=y(j);return(0,h.jsx)(f,{as:w,ref:t,className:(0,o.Z)(C.root,l),...q,ownerState:j,style:{..."inherit"!==a&&{"--Typography-textAlign":a},...q.style}})})},79986:(e,t,r)=>{"use strict";r.d(t,{Z:()=>s,f:()=>i});var n=r(71685),o=r(97898);function i(e){return(0,o.ZP)("MuiTypography",e)}let s=(0,n.Z)("MuiTypography",["root","h1","h2","h3","h4","h5","h6","subtitle1","subtitle2","body1","body2","inherit","button","caption","overline","alignLeft","alignRight","alignCenter","alignJustify","noWrap","gutterBottom","paragraph"])},54641:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n});let n=r(96005).Z},40955:(e,t,r)=>{"use strict";function n(e=[]){return([,t])=>t&&function(e,t=[]){if("string"!=typeof e.main)return!1;for(let r of t)if(!e.hasOwnProperty(r)||"string"!=typeof e[r])return!1;return!0}(t,e)}r.d(t,{Z:()=>n})},13643:(e,t,r)=>{"use strict";r.d(t,{Z:()=>i});var n=r(15966);let o={theme:void 0},i=function(e){let t,r;return function(i){let s=t;return(void 0===s||i.theme!==r)&&(o.theme=i.theme,t=s=(0,n.Z)(e(o)),r=i.theme),s}}},83158:(e,t,r)=>{"use strict";r.d(t,{zY:()=>u,u7:()=>p}),r(17577);var n=r(70140),o=r(48843),i=r(41222),s=r(14750),a=r(10326);let l=function(e){return(0,a.jsx)(o.default,{...e,defaultTheme:i.Z,themeId:s.Z})};function u(e){return function(t){return(0,a.jsx)(l,{styles:"function"==typeof e?r=>e({theme:r,...t}):e})}}function p(){return n.Z}},64263:(e,t,r)=>{"use strict";function n(e){try{return e.matches(":focus-visible")}catch(e){}return!1}r.d(t,{Z:()=>n})},41091:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});var n=r(10326);r(17577);var o=r(6283),i=r(25609),s=r(52188);function a(){return n.jsx(o.Z,{sx:{minHeight:"100vh",backgroundColor:"#000000",display:"flex",justifyContent:"center",alignItems:"center",p:2},children:(0,n.jsxs)(o.Z,{sx:{maxWidth:600,width:"100%",textAlign:"left",color:"#ffffff"},children:[n.jsx(i.Z,{variant:"h4",gutterBottom:!0,children:"Welcome to BenOsphere! Elevate your employee experience."}),n.jsx(i.Z,{variant:"h6",gutterBottom:!0,children:"Getting started is easy:"}),(0,n.jsxs)(i.Z,{sx:{ml:2,mb:1},children:["1️⃣ Visit"," ",n.jsx(s.Z,{href:"https://benosphere.com",target:"_blank",rel:"noopener",underline:"hover",sx:{color:"#00bfff"},children:"benosphere.com"})," ",'and click "Get Started for Free."']}),n.jsx(i.Z,{sx:{ml:2,mb:1},children:"2️⃣ Enter your email to receive a magic link for setup."}),n.jsx(i.Z,{sx:{ml:2,mb:1},children:"3️⃣ Click the magic link and fill out your organization details."}),n.jsx(i.Z,{sx:{ml:2},children:"4️⃣ Once your account is set up:"}),n.jsx(i.Z,{sx:{ml:4,mt:1},children:"\uD83C\uDFE2 Add your company's benefits"}),n.jsx(i.Z,{sx:{ml:4,mt:1},children:"\uD83D\uDC65 Invite team members to join"}),n.jsx(i.Z,{sx:{ml:4,mt:1},children:"\uD83D\uDE80 Enhance the employee benefits experience!"}),n.jsx(i.Z,{sx:{ml:2,mb:1},children:"5️⃣ Reload this page after signing up"}),n.jsx(i.Z,{sx:{mt:3},children:"Enjoy seamless benefits management with BenOsphere!"})]})})}},87067:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});let n=(0,r(68570).createProxy)(String.raw`C:\beno\project_dev\qharmony-frontend-v1\src\app\teams-landing\page.tsx#default`)},73881:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>o});var n=r(66621);let o=e=>[{type:"image/x-icon",sizes:"16x16",url:(0,n.fillMetadataSegment)(".",e.params,"favicon.ico")+""}]}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[8948,1183,6621,576],()=>r(18714));module.exports=n})();