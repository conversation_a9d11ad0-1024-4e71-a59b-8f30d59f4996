(()=>{var e={};e.id=9312,e.ids=[9312],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},27790:e=>{"use strict";e.exports=require("assert")},84770:e=>{"use strict";e.exports=require("crypto")},17702:e=>{"use strict";e.exports=require("events")},92048:e=>{"use strict";e.exports=require("fs")},32615:e=>{"use strict";e.exports=require("http")},35240:e=>{"use strict";e.exports=require("https")},19801:e=>{"use strict";e.exports=require("os")},55315:e=>{"use strict";e.exports=require("path")},76162:e=>{"use strict";e.exports=require("stream")},74026:e=>{"use strict";e.exports=require("string_decoder")},74175:e=>{"use strict";e.exports=require("tty")},17360:e=>{"use strict";e.exports=require("url")},21764:e=>{"use strict";e.exports=require("util")},71568:e=>{"use strict";e.exports=require("zlib")},98061:e=>{"use strict";e.exports=require("node:assert")},92761:e=>{"use strict";e.exports=require("node:async_hooks")},72254:e=>{"use strict";e.exports=require("node:buffer")},40027:e=>{"use strict";e.exports=require("node:console")},6005:e=>{"use strict";e.exports=require("node:crypto")},65714:e=>{"use strict";e.exports=require("node:diagnostics_channel")},15673:e=>{"use strict";e.exports=require("node:events")},88849:e=>{"use strict";e.exports=require("node:http")},42725:e=>{"use strict";e.exports=require("node:http2")},87503:e=>{"use strict";e.exports=require("node:net")},38846:e=>{"use strict";e.exports=require("node:perf_hooks")},39630:e=>{"use strict";e.exports=require("node:querystring")},84492:e=>{"use strict";e.exports=require("node:stream")},31764:e=>{"use strict";e.exports=require("node:tls")},41041:e=>{"use strict";e.exports=require("node:url")},47261:e=>{"use strict";e.exports=require("node:util")},93746:e=>{"use strict";e.exports=require("node:util/types")},24086:e=>{"use strict";e.exports=require("node:worker_threads")},65628:e=>{"use strict";e.exports=require("node:zlib")},18870:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>i.a,__next_app__:()=>p,originalPathname:()=>u,pages:()=>c,routeModule:()=>m,tree:()=>d}),t(99335),t(33709),t(35866);var s=t(23191),a=t(88716),o=t(37922),i=t.n(o),n=t(95231),l={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>n[e]);t.d(r,l);let d=["",{children:["view-all-users",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,99335)),"C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\view-all-users\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,33709)),"C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,35866,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],c=["C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\view-all-users\\page.tsx"],u="/view-all-users/page",p={require:t,loadChunk:()=>Promise.resolve()},m=new s.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/view-all-users/page",pathname:"/view-all-users",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},13609:(e,r,t)=>{Promise.resolve().then(t.bind(t,62732))},6283:(e,r,t)=>{"use strict";t.d(r,{Z:()=>d});var s=t(96830),a=t(5028),o=t(5283),i=t(14750);let n=(0,t(71685).Z)("MuiBox",["root"]),l=(0,o.Z)(),d=(0,s.default)({themeId:i.Z,defaultTheme:l,defaultClassName:n.root,generateClassName:a.Z.generate})},98139:(e,r,t)=>{"use strict";t.d(r,{Z:()=>I});var s=t(17577),a=t(41135),o=t(88634),i=t(8106),n=t(91703),l=t(13643),d=t(2791),c=t(54641),u=t(40955),p=t(71685),m=t(97898);function y(e){return(0,m.ZP)("MuiCircularProgress",e)}(0,p.Z)("MuiCircularProgress",["root","determinate","indeterminate","colorPrimary","colorSecondary","svg","circle","circleDeterminate","circleIndeterminate","circleDisableShrink"]);var v=t(10326);let f=(0,i.F4)`
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
`,h=(0,i.F4)`
  0% {
    stroke-dasharray: 1px, 200px;
    stroke-dashoffset: 0;
  }

  50% {
    stroke-dasharray: 100px, 200px;
    stroke-dashoffset: -15px;
  }

  100% {
    stroke-dasharray: 1px, 200px;
    stroke-dashoffset: -126px;
  }
`,g="string"!=typeof f?(0,i.iv)`
        animation: ${f} 1.4s linear infinite;
      `:null,x="string"!=typeof h?(0,i.iv)`
        animation: ${h} 1.4s ease-in-out infinite;
      `:null,b=e=>{let{classes:r,variant:t,color:s,disableShrink:a}=e,i={root:["root",t,`color${(0,c.Z)(s)}`],svg:["svg"],circle:["circle",`circle${(0,c.Z)(t)}`,a&&"circleDisableShrink"]};return(0,o.Z)(i,y,r)},w=(0,n.default)("span",{name:"MuiCircularProgress",slot:"Root",overridesResolver:(e,r)=>{let{ownerState:t}=e;return[r.root,r[t.variant],r[`color${(0,c.Z)(t.color)}`]]}})((0,l.Z)(({theme:e})=>({display:"inline-block",variants:[{props:{variant:"determinate"},style:{transition:e.transitions.create("transform")}},{props:{variant:"indeterminate"},style:g||{animation:`${f} 1.4s linear infinite`}},...Object.entries(e.palette).filter((0,u.Z)()).map(([r])=>({props:{color:r},style:{color:(e.vars||e).palette[r].main}}))]}))),P=(0,n.default)("svg",{name:"MuiCircularProgress",slot:"Svg",overridesResolver:(e,r)=>r.svg})({display:"block"}),k=(0,n.default)("circle",{name:"MuiCircularProgress",slot:"Circle",overridesResolver:(e,r)=>{let{ownerState:t}=e;return[r.circle,r[`circle${(0,c.Z)(t.variant)}`],t.disableShrink&&r.circleDisableShrink]}})((0,l.Z)(({theme:e})=>({stroke:"currentColor",variants:[{props:{variant:"determinate"},style:{transition:e.transitions.create("stroke-dashoffset")}},{props:{variant:"indeterminate"},style:{strokeDasharray:"80px, 200px",strokeDashoffset:0}},{props:({ownerState:e})=>"indeterminate"===e.variant&&!e.disableShrink,style:x||{animation:`${h} 1.4s ease-in-out infinite`}}]}))),I=s.forwardRef(function(e,r){let t=(0,d.i)({props:e,name:"MuiCircularProgress"}),{className:s,color:o="primary",disableShrink:i=!1,size:n=40,style:l,thickness:c=3.6,value:u=0,variant:p="indeterminate",...m}=t,y={...t,color:o,disableShrink:i,size:n,thickness:c,value:u,variant:p},f=b(y),h={},g={},x={};if("determinate"===p){let e=2*Math.PI*((44-c)/2);h.strokeDasharray=e.toFixed(3),x["aria-valuenow"]=Math.round(u),h.strokeDashoffset=`${((100-u)/100*e).toFixed(3)}px`,g.transform="rotate(-90deg)"}return(0,v.jsx)(w,{className:(0,a.Z)(f.root,s),style:{width:n,height:n,...g,...l},ownerState:y,ref:r,role:"progressbar",...x,...m,children:(0,v.jsx)(P,{className:f.svg,ownerState:y,viewBox:"22 22 44 44",children:(0,v.jsx)(k,{className:f.circle,style:h,ownerState:y,cx:44,cy:44,r:(44-c)/2,fill:"none",strokeWidth:c})})})})},2791:(e,r,t)=>{"use strict";t.d(r,{i:()=>a}),t(17577);var s=t(51387);function a(e){return(0,s.i)(e)}t(10326)},89178:(e,r,t)=>{"use strict";t.d(r,{Z:()=>g});var s=t(17577),a=t(41135),o=t(88634),i=t(44823),n=t(91703),l=t(23743),d=t(13643),c=t(2791),u=t(80608),p=t(71685),m=t(97898);function y(e){return(0,m.ZP)("MuiPaper",e)}(0,p.Z)("MuiPaper",["root","rounded","outlined","elevation","elevation0","elevation1","elevation2","elevation3","elevation4","elevation5","elevation6","elevation7","elevation8","elevation9","elevation10","elevation11","elevation12","elevation13","elevation14","elevation15","elevation16","elevation17","elevation18","elevation19","elevation20","elevation21","elevation22","elevation23","elevation24"]);var v=t(10326);let f=e=>{let{square:r,elevation:t,variant:s,classes:a}=e,i={root:["root",s,!r&&"rounded","elevation"===s&&`elevation${t}`]};return(0,o.Z)(i,y,a)},h=(0,n.default)("div",{name:"MuiPaper",slot:"Root",overridesResolver:(e,r)=>{let{ownerState:t}=e;return[r.root,r[t.variant],!t.square&&r.rounded,"elevation"===t.variant&&r[`elevation${t.elevation}`]]}})((0,d.Z)(({theme:e})=>({backgroundColor:(e.vars||e).palette.background.paper,color:(e.vars||e).palette.text.primary,transition:e.transitions.create("box-shadow"),variants:[{props:({ownerState:e})=>!e.square,style:{borderRadius:e.shape.borderRadius}},{props:{variant:"outlined"},style:{border:`1px solid ${(e.vars||e).palette.divider}`}},{props:{variant:"elevation"},style:{boxShadow:"var(--Paper-shadow)",backgroundImage:"var(--Paper-overlay)"}}]}))),g=s.forwardRef(function(e,r){let t=(0,c.i)({props:e,name:"MuiPaper"}),s=(0,l.default)(),{className:o,component:n="div",elevation:d=1,square:p=!1,variant:m="elevation",...y}=t,g={...t,component:n,elevation:d,square:p,variant:m},x=f(g);return(0,v.jsx)(h,{as:n,ownerState:g,className:(0,a.Z)(x.root,o),ref:r,...y,style:{..."elevation"===m&&{"--Paper-shadow":(s.vars||s).shadows[d],...s.vars&&{"--Paper-overlay":s.vars.overlays?.[d]},...!s.vars&&"dark"===s.palette.mode&&{"--Paper-overlay":`linear-gradient(${(0,i.Fq)("#fff",(0,u.Z)(d))}, ${(0,i.Fq)("#fff",(0,u.Z)(d))})`}},...y.style}})})},54641:(e,r,t)=>{"use strict";t.d(r,{Z:()=>s});let s=t(96005).Z},40955:(e,r,t)=>{"use strict";function s(e=[]){return([,r])=>r&&function(e,r=[]){if("string"!=typeof e.main)return!1;for(let t of r)if(!e.hasOwnProperty(t)||"string"!=typeof e[t])return!1;return!0}(r,e)}t.d(r,{Z:()=>s})},13643:(e,r,t)=>{"use strict";t.d(r,{Z:()=>o});var s=t(15966);let a={theme:void 0},o=function(e){let r,t;return function(o){let i=r;return(void 0===i||o.theme!==t)&&(a.theme=o.theme,r=i=(0,s.Z)(e(a)),t=o.theme),i}}},62732:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>v});var s=t(10326),a=t(17577),o=t(6283),i=t(98956),n=t(89178),l=t(50219),d=t(23543),c=t(72163),u=t(36112),p=t(59777),m=t(43058),y=t(53148);let v=()=>{let[e,r]=(0,a.useState)([]);async function t(){let e=await (0,y.A_)("/users?apiKey=24$FrostySnow");console.log(e),r(e.users)}return(0,a.useEffect)(()=>{t()},[]),s.jsx(m.Z,{children:s.jsx(o.Z,{sx:{bgcolor:"#F5F6F8",height:"95vh",padding:"32px",overflow:"auto"},children:s.jsx(i.Z,{component:n.Z,children:(0,s.jsxs)(l.Z,{children:[s.jsx(d.Z,{children:(0,s.jsxs)(c.Z,{children:[s.jsx(u.Z,{children:"Name"}),s.jsx(u.Z,{children:"Email"}),s.jsx(u.Z,{children:"Is Broker"}),s.jsx(u.Z,{children:"Is Admin"})]})}),s.jsx(p.Z,{children:e.map((e,r)=>(0,s.jsxs)(c.Z,{children:[s.jsx(u.Z,{children:e.name}),s.jsx(u.Z,{children:e.email}),s.jsx(u.Z,{children:e.isBroker?"Yes":"No"}),s.jsx(u.Z,{children:e.isAdmin?"Yes":"No"})]},r))})]})})})})}},43058:(e,r,t)=>{"use strict";t.d(r,{Z:()=>u});var s=t(10326),a=t(17577),o=t(22758),i=t(35047),n=t(31870);t(32049),t(94638);var l=t(98139),d=t(6283);let c=()=>/Mobi|Android/i.test(navigator.userAgent),u=({children:e})=>{let{user:r,loading:t}=(0,o.a)(),u=(0,i.useRouter)(),p=(0,i.usePathname)(),m=(0,n.T)(),[y,v]=(0,a.useState)(!1),f=(0,n.C)(e=>e.user.userProfile);return((0,a.useEffect)(()=>{},[m,f.name]),(0,a.useEffect)(()=>{console.log("ProtectedRoute useEffect triggered"),console.log("Current user: ",r),console.log("Loading state: ",t),console.log("Current user details: ",f),t||r||(console.log("User not authenticated, redirecting to home"),v(!1),u.push("/")),!t&&f.companyId&&""===f.companyId&&(console.log("Waiting to retrieve company details"),v(!1)),!t&&f.companyId&&""!==f.companyId&&(console.log("User found, rendering children"),v(!0)),c()&&!p.startsWith("/mobile")&&(console.log(`Redirecting to mobile version of ${p}`),u.push(`/mobile${p}`))},[r,t,f,u,p]),y)?r?s.jsx(s.Fragment,{children:e}):null:s.jsx(d.Z,{sx:{display:"flex",justifyContent:"center",alignItems:"center",height:"100vh",bgcolor:"#f6f8fc"},children:s.jsx(l.Z,{})})}},94638:(e,r,t)=>{"use strict";t.d(r,{G9:()=>h,JZ:()=>g,M_:()=>y,N:()=>d,Nq:()=>m,TQ:()=>u,Ur:()=>n,aE:()=>c,aK:()=>w,dA:()=>l,gt:()=>x,mb:()=>f,qB:()=>b,yu:()=>p,zX:()=>v});var s=t(53148),a=t(39352),o=t(25748),i=t(32049);function n(e){switch(e){case"Our Culture & Workplace":return"Work Policies";case"Protecting Your Income":return"Income Security";default:return e}}function l(e){switch(e){case"On-site Amenities":return"Company Handbook";case"Protecting Your Income":return"Income Security";case"Got married/divorced":return"Marriage or Divorce";case"Had a baby or adopted a baby":return"New Baby or Adoption";case"Lost your insurance":return"Loss of Insurance";default:return e}}async function d(e,r){let t=await (0,s.A_)("/benefits/benefit-types",{companyId:r});return console.log("COMPANY BENEFIT TYPES RESPONSE: ",t.benefitTypes),e((0,a.x7)(t.benefitTypes)),t.benefitTypes}async function c(e,r){let t=await (0,s.A_)("/benefits/all-benefits",{companyId:r});console.log("COMPANY BENEFIT TYPES WITH BENEFITS RESPONSE: ",t),e((0,o.US)(t.benefitsPerType))}async function u(e){let r=await (0,s.A_)("/admin/all-employees");return console.log("COMPANY TEAM MEMBERS RESPONSE: ",r),e((0,a.Vv)(r.employees)),r.employees}async function p(e,r){return console.log("ADDING USERS: ",r),await (0,s.j0)("/admin/add/employees",{employeeList:r})}async function m(e,r,t){try{console.log("\uD83D\uDD0D Debug: User being updated:",r);let e={employeeId:r,updatedDetails:{name:t.name,email:t.email,details:{phoneNumber:t.phoneNumber||"",department:t.department||"",title:t.title||"",role:t.title||""}}};return t.dateOfBirth&&(e.updatedDetails.details.dateOfBirth=t.dateOfBirth),t.hireDate&&(e.updatedDetails.details.hireDate=t.hireDate),t.annualSalary&&(e.updatedDetails.details.annualSalary=t.annualSalary),t.employeeClassType&&(e.updatedDetails.details.employeeClassType=t.employeeClassType),t.workSchedule&&(e.updatedDetails.details.workSchedule=t.workSchedule),t.ssn&&(e.updatedDetails.details.ssn=t.ssn),t.employeeId&&(e.updatedDetails.details.employeeId=t.employeeId),t.workLocation&&(e.updatedDetails.details.workLocation=t.workLocation),t.address&&(e.updatedDetails.details.address=t.address),t.mailingAddress&&(e.updatedDetails.details.mailingAddress=t.mailingAddress),t.emergencyContact&&(e.updatedDetails.details.emergencyContact=t.emergencyContact),e.updatedDetails.details.dependents=t.dependents||[],console.log("Middleware - dependents being sent:",e.updatedDetails.details.dependents),console.log("Sending PUT request with cleaned data:",e),await (0,s.GH)("/admin/update/employee",e)}catch(e){throw console.error("Error in updateUser middleware:",e),e}}async function y(e,r){let t=await (0,s.A_)("/employee",{"user-id":r});return e((0,i.$l)({name:t.currentUser.name,email:t.currentUser.email,companyId:t.currentUser.companyId,role:t.currentUser.role,isAdmin:t.currentUser.isAdmin,isBroker:t.currentUser.isBroker,details:t.currentUser.details})),t}async function v(e,r,t){let a=await (0,s.j0)("/admin/onboard",{company:{name:r.name,adminEmail:r.adminEmail,adminRole:r.adminRole,companySize:r.companySize,industry:r.industry,location:r.location,website:r.website,howHeard:r.howHeard,brokerId:r.brokerId,brokerageId:r.brokerageId,isBrokerage:r.isBrokerage,isActivated:r.isActivated,referralSource:r.referralSource,details:{logo:""}},user:{email:t.email,name:t.name,role:t.role,isAdmin:t.isAdmin,isBroker:t.isBroker,isActivated:t.isActivated}}),o=a.data.userId,i=a.data.companyId;return localStorage.setItem("userid1",o),localStorage.setItem("companyId1",i),a}async function f(e,r){return console.log("SENDING LOGIN LINK TO EMPLOYEE: ",e,r),await (0,s.j0)("/admin/send-user-login-link",{userId:e,companyId:r})}async function h(e,r,t,a){let o=await (0,s.j0)("/admin/add/employer",{brokerId:e,companyName:r,companyAdminEmail:t,companyAdminName:a});return console.log("BROKER ADDS COMPANY RESPONSE: ",o),o}async function g(e,r){return 200===(await (0,s.j0)("/employee/offboard/",{userId:e,companyId:r})).status}async function x(e,r){return await (0,s.j0)("/employee/enable/",{userId:e,companyId:r})}async function b(e,r){try{let r=await (0,s.A_)("/admin/all-companies");console.log("CLIENT COMPANIES UNDER BROKER: ",r);let t=r.companies||[];try{let e=await (0,s.A_)("/employee/company-details");console.log("BROKER'S OWN COMPANY: ",e),e.company&&e.company.isBrokerage&&!t.some(r=>r._id===e.company._id)&&(t.unshift(e.company),console.log("Added broker's own company to the list"))}catch(e){console.log("Could not fetch broker's own company (this is normal if user is not a broker):",e)}return console.log("ALL COMPANIES (CLIENT + OWN): ",t),e((0,i.Ym)(t)),{...r,companies:t}}catch(r){return console.error("Error fetching companies:",r),e((0,i.Ym)([])),{companies:[]}}}async function w(e){let r=await (0,s.A_)("/employee/company-details");return e((0,a.sy)(r.company)),r.status}},31870:(e,r,t)=>{"use strict";t.d(r,{C:()=>o,T:()=>a});var s=t(25842);let a=()=>(0,s.I0)(),o=s.v9},99335:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(68570).createProxy)(String.raw`C:\beno\project_dev\qharmony-frontend-v1\src\app\view-all-users\page.tsx#default`)},73881:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>a});var s=t(66621);let a=e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",e.params,"favicon.ico")+""}]}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[8948,1183,6621,2145,576],()=>t(18870));module.exports=s})();