(()=>{var e={};e.id=5844,e.ids=[5844],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},27790:e=>{"use strict";e.exports=require("assert")},84770:e=>{"use strict";e.exports=require("crypto")},17702:e=>{"use strict";e.exports=require("events")},92048:e=>{"use strict";e.exports=require("fs")},32615:e=>{"use strict";e.exports=require("http")},35240:e=>{"use strict";e.exports=require("https")},19801:e=>{"use strict";e.exports=require("os")},55315:e=>{"use strict";e.exports=require("path")},76162:e=>{"use strict";e.exports=require("stream")},74026:e=>{"use strict";e.exports=require("string_decoder")},74175:e=>{"use strict";e.exports=require("tty")},17360:e=>{"use strict";e.exports=require("url")},21764:e=>{"use strict";e.exports=require("util")},71568:e=>{"use strict";e.exports=require("zlib")},98061:e=>{"use strict";e.exports=require("node:assert")},92761:e=>{"use strict";e.exports=require("node:async_hooks")},72254:e=>{"use strict";e.exports=require("node:buffer")},40027:e=>{"use strict";e.exports=require("node:console")},6005:e=>{"use strict";e.exports=require("node:crypto")},65714:e=>{"use strict";e.exports=require("node:diagnostics_channel")},15673:e=>{"use strict";e.exports=require("node:events")},88849:e=>{"use strict";e.exports=require("node:http")},42725:e=>{"use strict";e.exports=require("node:http2")},87503:e=>{"use strict";e.exports=require("node:net")},38846:e=>{"use strict";e.exports=require("node:perf_hooks")},39630:e=>{"use strict";e.exports=require("node:querystring")},84492:e=>{"use strict";e.exports=require("node:stream")},31764:e=>{"use strict";e.exports=require("node:tls")},41041:e=>{"use strict";e.exports=require("node:url")},47261:e=>{"use strict";e.exports=require("node:util")},93746:e=>{"use strict";e.exports=require("node:util/types")},24086:e=>{"use strict";e.exports=require("node:worker_threads")},65628:e=>{"use strict";e.exports=require("node:zlib")},7959:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>s.a,__next_app__:()=>p,originalPathname:()=>u,pages:()=>c,routeModule:()=>f,tree:()=>d}),r(81822),r(33709),r(35866);var n=r(23191),o=r(88716),i=r(37922),s=r.n(i),a=r(95231),l={};for(let e in a)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>a[e]);r.d(t,l);let d=["",{children:["viewBenefitDetails",{children:["[benefitId]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,81822)),"C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\viewBenefitDetails\\[benefitId]\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,33709)),"C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,35866,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],c=["C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\viewBenefitDetails\\[benefitId]\\page.tsx"],u="/viewBenefitDetails/[benefitId]/page",p={require:r,loadChunk:()=>Promise.resolve()},f=new n.AppPageRouteModule({definition:{kind:o.x.APP_PAGE,page:"/viewBenefitDetails/[benefitId]/page",pathname:"/viewBenefitDetails/[benefitId]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},45821:(e,t,r)=>{Promise.resolve().then(r.bind(r,18639))},33790:(e,t,r)=>{"use strict";r.d(t,{Z:()=>i});var n=r(51426),o=r(10326);let i=(0,n.Z)((0,o.jsx)("path",{d:"M17.77 3.77 16 2 6 12l10 10 1.77-1.77L9.54 12z"}),"ArrowBackIosNew")},76380:(e,t,r)=>{"use strict";r.d(t,{Z:()=>i});var n=r(51426),o=r(10326);let i=(0,n.Z)((0,o.jsx)("path",{d:"M19 19H5V5h7V3H5c-1.11 0-2 .9-2 2v14c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2v-7h-2zM14 3v2h3.59l-9.83 9.83 1.41 1.41L19 6.41V10h2V3z"}),"OpenInNew")},33198:(e,t,r)=>{"use strict";r.d(t,{Z:()=>v});var n=r(17577),o=r(41135),i=r(88634),s=r(91703),a=r(13643),l=r(2791),d=r(51426),c=r(10326);let u=(0,d.Z)((0,c.jsx)("path",{d:"M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"}),"Person");var p=r(71685),f=r(97898);function x(e){return(0,f.ZP)("MuiAvatar",e)}(0,p.Z)("MuiAvatar",["root","colorDefault","circular","rounded","square","img","fallback"]);var m=r(31121);let g=e=>{let{classes:t,variant:r,colorDefault:n}=e;return(0,i.Z)({root:["root",r,n&&"colorDefault"],img:["img"],fallback:["fallback"]},x,t)},h=(0,s.default)("div",{name:"MuiAvatar",slot:"Root",overridesResolver:(e,t)=>{let{ownerState:r}=e;return[t.root,t[r.variant],r.colorDefault&&t.colorDefault]}})((0,a.Z)(({theme:e})=>({position:"relative",display:"flex",alignItems:"center",justifyContent:"center",flexShrink:0,width:40,height:40,fontFamily:e.typography.fontFamily,fontSize:e.typography.pxToRem(20),lineHeight:1,borderRadius:"50%",overflow:"hidden",userSelect:"none",variants:[{props:{variant:"rounded"},style:{borderRadius:(e.vars||e).shape.borderRadius}},{props:{variant:"square"},style:{borderRadius:0}},{props:{colorDefault:!0},style:{color:(e.vars||e).palette.background.default,...e.vars?{backgroundColor:e.vars.palette.Avatar.defaultBg}:{backgroundColor:e.palette.grey[400],...e.applyStyles("dark",{backgroundColor:e.palette.grey[600]})}}}]}))),b=(0,s.default)("img",{name:"MuiAvatar",slot:"Img",overridesResolver:(e,t)=>t.img})({width:"100%",height:"100%",textAlign:"center",objectFit:"cover",color:"transparent",textIndent:1e4}),y=(0,s.default)(u,{name:"MuiAvatar",slot:"Fallback",overridesResolver:(e,t)=>t.fallback})({width:"75%",height:"75%"}),v=n.forwardRef(function(e,t){let r=(0,l.i)({props:e,name:"MuiAvatar"}),{alt:i,children:s,className:a,component:d="div",slots:u={},slotProps:p={},imgProps:f,sizes:x,src:v,srcSet:w,variant:j="circular",...k}=r,q=null,I={...r,component:d,variant:j},Z=function({crossOrigin:e,referrerPolicy:t,src:r,srcSet:o}){let[i,s]=n.useState(!1);return n.useEffect(()=>{if(!r&&!o)return;s(!1);let n=!0,i=new Image;return i.onload=()=>{n&&s("loaded")},i.onerror=()=>{n&&s("error")},i.crossOrigin=e,i.referrerPolicy=t,i.src=r,o&&(i.srcset=o),()=>{n=!1}},[e,t,r,o]),i}({...f,..."function"==typeof p.img?p.img(I):p.img,src:v,srcSet:w}),E=v||w,_=E&&"error"!==Z;I.colorDefault=!_,delete I.ownerState;let M=g(I),[P,D]=(0,m.Z)("img",{className:M.img,elementType:b,externalForwardedProps:{slots:u,slotProps:{img:{...f,...p.img}}},additionalProps:{alt:i,src:v,srcSet:w,sizes:x},ownerState:I});return q=_?(0,c.jsx)(P,{...D}):s||0===s?s:E&&i?i[0]:(0,c.jsx)(y,{ownerState:I,className:M.fallback}),(0,c.jsx)(h,{as:d,className:(0,o.Z)(M.root,a),ref:t,...k,ownerState:I,children:q})})},18639:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>k});var n=r(10326),o=r(17577),i=r(31870),s=r(69058),a=r(35047),l=r(6283),d=r(42265),c=r(25609),u=r(16027),p=r(98139),f=r(33790),x=r(25842),m=r(25748),g=r(32049),h=r(76380),b=r(12549),y=r(43058),v=r(23371),w=r(78074),j=r(94638);let k=(0,b.Z)(()=>{let e=(0,a.useRouter)(),t=(0,i.T)(),{benefitId:r}=(0,a.useParams)(),[b,k]=(0,o.useState)(!1),q=(0,x.v9)(e=>(0,g.MP)(e)),I=(0,i.C)(e=>e.benefits.documentsPerBenefit),Z=(0,i.C)(e=>e.benefits.viewableDocuments),E=(0,x.v9)(e=>(0,m.d8)(e,r)),_=(0,i.C)(e=>e.benefits.loadingDocuments);(0,o.useEffect)(()=>{v.j2().then(()=>{v.fw().then(e=>{e.app.host.name===w.UB.teams&&k(!0)})})},[]),(0,o.useEffect)(()=>{""!==r&&(0,s.v0)(t,r,q,"view_benefits")},[r,q,t]);let M=(e,t)=>{window.open(`https://api.benosphere.com/benefits/document?objectKey=${e}&companyId=${t}`,"_blank")};return n.jsx(y.Z,{children:(0,n.jsxs)(l.Z,{sx:{bgcolor:"#F5F6F8",height:"95vh",padding:"32px",overflow:"auto"},children:[n.jsx(l.Z,{sx:{display:"flex",alignItems:"center",mb:3},children:n.jsx(d.Z,{startIcon:n.jsx(f.Z,{sx:{fontSize:16}}),onClick:()=>{e.back()},sx:{color:"#6c757d",fontWeight:"normal",textTransform:"none",fontSize:"1.2rem","&:hover":{bgcolor:"transparent"}},children:E?(0,n.jsxs)(n.Fragment,{children:[(0,j.Ur)(E.benefitType)," /",n.jsx("span",{style:{fontWeight:"bold",color:"#000000",marginLeft:5},children:(0,j.dA)(E.benefit.subType)})]}):"Back"})}),n.jsx(c.Z,{sx:{fontWeight:800,fontSize:"42px",mb:0},children:(0,j.dA)(E?.benefit?.subType||"")}),n.jsx(c.Z,{variant:"body1",sx:{color:"#6c757d",mb:6,fontSize:"16px"},children:"You can find all your health insurance details here, including coverage options, policy documents, and claim information."}),(0,n.jsxs)(u.ZP,{container:!0,spacing:3,alignItems:"flex-start",children:[(0,n.jsxs)(u.ZP,{item:!0,xs:12,children:[n.jsx(c.Z,{sx:{mb:3,fontWeight:700,fontSize:"24px"},children:"☕ Documents"}),0===I.documents.length?n.jsx(l.Z,{sx:{display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",minHeight:"150px",borderRadius:"8px",border:"2px dashed #e0e0e0",bgcolor:"#f9f9f9",p:4,textAlign:"left",maxWidth:"400px"},children:n.jsx(c.Z,{variant:"body1",sx:{color:"#6c757d",fontSize:"1rem"},children:"No documents available at the moment."})}):n.jsx(l.Z,{sx:{display:"flex",flexWrap:"wrap",gap:"50px"},children:I.documents.map((e,t)=>{let r=Z.find(t=>t.documentObjectKey===e),o=["linear-gradient(135deg, #6a11cb 0%, #2575fc 100%)","linear-gradient(135deg, #ff7e5f 0%, #feb47b 100%)","linear-gradient(135deg, #43cea2 0%, #185a9d 100%)","linear-gradient(135deg, #ffafbd 0%, #ffc3a0 100%)","linear-gradient(135deg, #00c6ff 0%, #0072ff 100%)"],i=o[t%o.length];return n.jsx(l.Z,{sx:{position:"relative",width:"240px",height:"367.5px",display:"flex",flexDirection:"column",alignItems:"flex-start",justifyContent:"flex-start"},children:n.jsx(l.Z,{sx:{width:"240px",minHeight:"322.5px",borderRadius:"12px",overflow:"hidden",boxShadow:"0px 4px 12px rgba(0, 0, 0, 0.1)",position:"relative",display:"flex",alignItems:"center",justifyContent:"center",background:i,color:"#ffffff",cursor:"pointer"},onClick:()=>M(r.documentObjectKey,q),children:_.includes(e)||!r?n.jsx(p.Z,{}):(0,n.jsxs)(l.Z,{sx:{padding:"16px",display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",textAlign:"center",overflow:"hidden"},children:[n.jsx(c.Z,{sx:{fontSize:"27px",fontWeight:"bold",textAlign:"center",wordWrap:"break-word",wordBreak:"break-word",whiteSpace:"normal",overflow:"hidden",display:"-webkit-box",WebkitLineClamp:3,WebkitBoxOrient:"vertical"},children:r?.originalFileName||"Document Preview"}),n.jsx(h.Z,{sx:{position:"absolute",top:12,right:12,color:"#ffffff",cursor:"pointer",height:25,width:25},onClick:()=>window.open(r?.document,"_blank")})]})})},e)})})]}),(0,n.jsxs)(u.ZP,{item:!0,xs:12,children:[n.jsx(c.Z,{sx:{mb:3,fontWeight:700,mt:10,fontSize:"17px"},children:"Other helpful links"}),n.jsx(l.Z,{sx:{borderRadius:"12px",width:"400px"},children:0===I.links.length?n.jsx(l.Z,{sx:{display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",minHeight:"150px",borderRadius:"8px",border:"2px dashed #e0e0e0",bgcolor:"#f9f9f9",py:4,textAlign:"left",maxWidth:"400px"},children:n.jsx(c.Z,{variant:"body1",sx:{color:"#6c757d",fontSize:"16px"},children:"No links available right now."})}):n.jsx(l.Z,{sx:{display:"flex",flexDirection:"column",gap:2},children:I.links.map((e,t)=>(0,n.jsxs)(l.Z,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",width:"100%"},children:[n.jsx(c.Z,{component:"a",href:e.startsWith("http")?e:`https://${e}`,target:"_blank",rel:"noopener noreferrer",sx:{color:"#1A7ECF",textDecoration:"none",fontWeight:500,fontSize:"16px"},children:e||`Link ${t+1}`}),n.jsx(d.Z,{onClick:()=>window.open(e.startsWith("http")?e:`https://${e}`,"_blank"),sx:{minWidth:0,padding:0},children:n.jsx(h.Z,{sx:{color:"#6c757d",marginLeft:2}})})]},t))})})]})]})]})})})},69058:(e,t,r)=>{"use strict";r.d(t,{$t:()=>c,SS:()=>p,Y0:()=>s,cd:()=>u,fH:()=>f,mH:()=>x,ov:()=>d,v0:()=>a});var n=r(53148),o=r(25748),i=r(94638);async function s(e,t,r){try{let i=await (0,n.A_)("/benefits/benefit-by-type",{companyId:t,type:r});i&&i.benefits?(console.log("GET BENEFITS FOR TYPE RESPONSE: ",i.benefits),e((0,o.oQ)({benefitType:r,benefits:i.benefits})),e((0,o.nM)("Benefits fetched successfully"))):(console.error("Invalid response format:",i),e((0,o.nM)("Failed to fetch benefits")))}catch(t){console.error("Error fetching benefits:",t),e((0,o.nM)("Error fetching benefits"))}}async function a(e,t,r,i){let s={benefitId:t,page:i};console.log("data",s);let a=await (0,n.A_)("/benefits/one-benefit",s),d={...a,benefitId:t};for(let t of(e((0,o.F5)(d)),a.documents)){let n=decodeURIComponent(t.split("_____")[1]);l(e,t,r,n)}}async function l(e,t,r,i){let s={objectKey:t,companyId:r};console.log("data",s);let a=await (0,n.$R)("/benefits/document",s);if(console.log("VIEW BENEFIT RESPONSE: ",a),a){let r=new Blob([a],{type:"application/pdf"}),n=URL.createObjectURL(r);e((0,o.D7)([{documentObjectKey:t,document:n,originalFileName:i}]))}}let d=async(e,t,r,o,a)=>200===(await (0,n.j0)("/benefits/toggle-benefits/",{benefitId:r,companyId:t,isActivated:o})).status&&(await s(e,t,a),await (0,i.N)(e,t),!0);async function c(e,t,r,i){let s=new FormData;i.forEach(e=>s.append("documents",e)),s.append("companyId",r),s.append("benefitId",t);try{console.log("uploadDocument",s);let a=await (0,n.iG)("/benefits/add/document",s),d=a.data.objectKeys;if(console.log("newObjectKeys",d),200===a.status)return d.forEach((n,s)=>{let a=i[s].name;e((0,o.H_)({benefitId:t,document:n})),l(e,n,r,a)}),e((0,o.nM)("Document added successfully")),!0;return console.error("Error adding document:",a.data.error),e((0,o.nM)("Failed to add document")),!1}catch(t){return console.error("Error adding document:",t),e((0,o.nM)("Error adding document")),!1}}async function u(e,t,r,i){try{let s=await (0,n.j0)("/benefits/delete/document",{benefitId:t,companyId:r,objectKey:i});if(200===s.status)return e((0,o.iH)({benefitId:t,document:i})),e((0,o.nM)("Document deleted successfully")),!0;return console.error("Error deleting document:",s.data.error),e((0,o.nM)("Failed to delete document")),!1}catch(t){return console.error("Error deleting document:",t),e((0,o.nM)("Error deleting document")),!1}}async function p(e,t,r,i){try{let s=await (0,n.j0)("/benefits/add/links",{benefitId:t,companyId:r,urls:[i]});if(200===s.status)return e((0,o.MJ)({benefitId:t,link:i})),e((0,o.nM)("Link added successfully")),!0;return console.error("Error adding link:",s.data.error),e((0,o.nM)("Failed to add link")),!1}catch(t){return console.error("Error adding link:",t),e((0,o.nM)("Error adding link")),!1}}async function f(e,t,r,i){try{let s=await (0,n.j0)("/benefits/delete/link",{benefitId:t,companyId:r,urls:i});if(200===s.status)return e((0,o.Yw)({benefitId:t,link:i})),e((0,o.nM)("Link deleted successfully")),!0;return console.error("Error deleting link:",s.data.error),e((0,o.nM)("Failed to delete link")),!1}catch(t){return console.error("Error deleting link:",t),e((0,o.nM)("Error deleting link")),!1}}async function x(e,t){let r=new FormData;r.append("logoImage",t);try{console.log("uploading company logo",r);let t=await (0,n.iG)("/admin/update-company-logo",r);if(await (0,i.aK)(e),200===t.status)return console.log("Company logo updated successfully"),e((0,o.nM)("Company logo updated successfully")),!0;return console.error("Error updating company logo:",t.data.error),e((0,o.nM)("Failed to update company logo")),!1}catch(t){return console.error("Error updating company logo:",t),e((0,o.nM)("Error updating company logo")),!1}}},81822:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});let n=(0,r(68570).createProxy)(String.raw`C:\beno\project_dev\qharmony-frontend-v1\src\app\viewBenefitDetails\[benefitId]\page.tsx#default`)}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[8948,1183,6621,9066,1999,3253,928,8097,8522,3141,6027,576,6305,401,2549],()=>r(7959));module.exports=n})();