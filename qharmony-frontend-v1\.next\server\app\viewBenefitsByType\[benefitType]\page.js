(()=>{var e={};e.id=9767,e.ids=[9767],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},27790:e=>{"use strict";e.exports=require("assert")},84770:e=>{"use strict";e.exports=require("crypto")},17702:e=>{"use strict";e.exports=require("events")},92048:e=>{"use strict";e.exports=require("fs")},32615:e=>{"use strict";e.exports=require("http")},35240:e=>{"use strict";e.exports=require("https")},19801:e=>{"use strict";e.exports=require("os")},55315:e=>{"use strict";e.exports=require("path")},76162:e=>{"use strict";e.exports=require("stream")},74026:e=>{"use strict";e.exports=require("string_decoder")},74175:e=>{"use strict";e.exports=require("tty")},17360:e=>{"use strict";e.exports=require("url")},21764:e=>{"use strict";e.exports=require("util")},71568:e=>{"use strict";e.exports=require("zlib")},98061:e=>{"use strict";e.exports=require("node:assert")},92761:e=>{"use strict";e.exports=require("node:async_hooks")},72254:e=>{"use strict";e.exports=require("node:buffer")},40027:e=>{"use strict";e.exports=require("node:console")},6005:e=>{"use strict";e.exports=require("node:crypto")},65714:e=>{"use strict";e.exports=require("node:diagnostics_channel")},15673:e=>{"use strict";e.exports=require("node:events")},88849:e=>{"use strict";e.exports=require("node:http")},42725:e=>{"use strict";e.exports=require("node:http2")},87503:e=>{"use strict";e.exports=require("node:net")},38846:e=>{"use strict";e.exports=require("node:perf_hooks")},39630:e=>{"use strict";e.exports=require("node:querystring")},84492:e=>{"use strict";e.exports=require("node:stream")},31764:e=>{"use strict";e.exports=require("node:tls")},41041:e=>{"use strict";e.exports=require("node:url")},47261:e=>{"use strict";e.exports=require("node:util")},93746:e=>{"use strict";e.exports=require("node:util/types")},24086:e=>{"use strict";e.exports=require("node:worker_threads")},65628:e=>{"use strict";e.exports=require("node:zlib")},52583:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>p,originalPathname:()=>u,pages:()=>c,routeModule:()=>f,tree:()=>d}),r(30638),r(33709),r(35866);var o=r(23191),n=r(88716),s=r(37922),i=r.n(s),a=r(95231),l={};for(let e in a)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>a[e]);r.d(t,l);let d=["",{children:["viewBenefitsByType",{children:["[benefitType]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,30638)),"C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\viewBenefitsByType\\[benefitType]\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,33709)),"C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,35866,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],c=["C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\viewBenefitsByType\\[benefitType]\\page.tsx"],u="/viewBenefitsByType/[benefitType]/page",p={require:r,loadChunk:()=>Promise.resolve()},f=new o.AppPageRouteModule({definition:{kind:n.x.APP_PAGE,page:"/viewBenefitsByType/[benefitType]/page",pathname:"/viewBenefitsByType/[benefitType]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},85222:(e,t,r)=>{Promise.resolve().then(r.bind(r,80898))},33198:(e,t,r)=>{"use strict";r.d(t,{Z:()=>v});var o=r(17577),n=r(41135),s=r(88634),i=r(91703),a=r(13643),l=r(2791),d=r(51426),c=r(10326);let u=(0,d.Z)((0,c.jsx)("path",{d:"M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"}),"Person");var p=r(71685),f=r(97898);function m(e){return(0,f.ZP)("MuiAvatar",e)}(0,p.Z)("MuiAvatar",["root","colorDefault","circular","rounded","square","img","fallback"]);var x=r(31121);let g=e=>{let{classes:t,variant:r,colorDefault:o}=e;return(0,s.Z)({root:["root",r,o&&"colorDefault"],img:["img"],fallback:["fallback"]},m,t)},y=(0,i.default)("div",{name:"MuiAvatar",slot:"Root",overridesResolver:(e,t)=>{let{ownerState:r}=e;return[t.root,t[r.variant],r.colorDefault&&t.colorDefault]}})((0,a.Z)(({theme:e})=>({position:"relative",display:"flex",alignItems:"center",justifyContent:"center",flexShrink:0,width:40,height:40,fontFamily:e.typography.fontFamily,fontSize:e.typography.pxToRem(20),lineHeight:1,borderRadius:"50%",overflow:"hidden",userSelect:"none",variants:[{props:{variant:"rounded"},style:{borderRadius:(e.vars||e).shape.borderRadius}},{props:{variant:"square"},style:{borderRadius:0}},{props:{colorDefault:!0},style:{color:(e.vars||e).palette.background.default,...e.vars?{backgroundColor:e.vars.palette.Avatar.defaultBg}:{backgroundColor:e.palette.grey[400],...e.applyStyles("dark",{backgroundColor:e.palette.grey[600]})}}}]}))),h=(0,i.default)("img",{name:"MuiAvatar",slot:"Img",overridesResolver:(e,t)=>t.img})({width:"100%",height:"100%",textAlign:"center",objectFit:"cover",color:"transparent",textIndent:1e4}),b=(0,i.default)(u,{name:"MuiAvatar",slot:"Fallback",overridesResolver:(e,t)=>t.fallback})({width:"75%",height:"75%"}),v=o.forwardRef(function(e,t){let r=(0,l.i)({props:e,name:"MuiAvatar"}),{alt:s,children:i,className:a,component:d="div",slots:u={},slotProps:p={},imgProps:f,sizes:m,src:v,srcSet:w,variant:j="circular",...q}=r,E=null,M={...r,component:d,variant:j},Z=function({crossOrigin:e,referrerPolicy:t,src:r,srcSet:n}){let[s,i]=o.useState(!1);return o.useEffect(()=>{if(!r&&!n)return;i(!1);let o=!0,s=new Image;return s.onload=()=>{o&&i("loaded")},s.onerror=()=>{o&&i("error")},s.crossOrigin=e,s.referrerPolicy=t,s.src=r,n&&(s.srcset=n),()=>{o=!1}},[e,t,r,n]),s}({...f,..."function"==typeof p.img?p.img(M):p.img,src:v,srcSet:w}),k=v||w,C=k&&"error"!==Z;M.colorDefault=!C,delete M.ownerState;let _=g(M),[P,T]=(0,x.Z)("img",{className:_.img,elementType:h,externalForwardedProps:{slots:u,slotProps:{img:{...f,...p.img}}},additionalProps:{alt:s,src:v,srcSet:w,sizes:m},ownerState:M});return E=C?(0,c.jsx)(P,{...T}):i||0===i?i:k&&s?s[0]:(0,c.jsx)(b,{ownerState:M,className:_.fallback}),(0,c.jsx)(y,{as:d,className:(0,n.Z)(_.root,a),ref:t,...q,ownerState:M,children:E})})},4766:(e,t,r)=>{"use strict";r.d(t,{Z:()=>m});var o=r(17577),n=r(41135),s=r(88634),i=r(91703),a=r(2791),l=r(71685),d=r(97898);function c(e){return(0,d.ZP)("MuiCardContent",e)}(0,l.Z)("MuiCardContent",["root"]);var u=r(10326);let p=e=>{let{classes:t}=e;return(0,s.Z)({root:["root"]},c,t)},f=(0,i.default)("div",{name:"MuiCardContent",slot:"Root",overridesResolver:(e,t)=>t.root})({padding:16,"&:last-child":{paddingBottom:24}}),m=o.forwardRef(function(e,t){let r=(0,a.i)({props:e,name:"MuiCardContent"}),{className:o,component:s="div",...i}=r,l={...r,component:s},d=p(l);return(0,u.jsx)(f,{as:s,className:(0,n.Z)(d.root,o),ownerState:l,ref:t,...i})})},34039:(e,t,r)=>{"use strict";r.d(t,{Z:()=>x});var o=r(17577),n=r(41135),s=r(88634),i=r(91703),a=r(2791),l=r(89178),d=r(71685),c=r(97898);function u(e){return(0,c.ZP)("MuiCard",e)}(0,d.Z)("MuiCard",["root"]);var p=r(10326);let f=e=>{let{classes:t}=e;return(0,s.Z)({root:["root"]},u,t)},m=(0,i.default)(l.Z,{name:"MuiCard",slot:"Root",overridesResolver:(e,t)=>t.root})({overflow:"hidden"}),x=o.forwardRef(function(e,t){let r=(0,a.i)({props:e,name:"MuiCard"}),{className:o,raised:s=!1,...i}=r,l={...r,raised:s},d=f(l);return(0,p.jsx)(m,{className:(0,n.Z)(d.root,o),elevation:s?8:void 0,ref:t,ownerState:l,...i})})},80898:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>j});var o=r(10326),n=r(17577),s=r(31870),i=r(69058),a=r(32049),l=r(25748),d=r(6283),c=r(25609),u=r(98139),p=r(16027),f=r(34039),m=r(4766),x=r(42265),g=r(35047),y=r(25842),h=r(12549),b=r(43058),v=r(18742),w=r(94638);let j=(0,h.Z)(()=>{let{benefitType:e}=(0,g.useParams)(),t=decodeURIComponent(e),r=(0,s.T)(),h=(0,g.useRouter)(),j=(0,y.v9)(e=>(0,a.MP)(e)),q=(0,s.C)(e=>e.user.userProfile),E=(0,y.v9)(e=>(0,l.oT)(e,t));(0,n.useEffect)(()=>{""!==t&&""!==j&&(r((0,l.Yb)()),(0,i.Y0)(r,j,t))},[t,j,r]);let M=e=>{r((0,a.Re)(e)),h.push(`/viewBenefitDetails/${e}`)};return o.jsx(b.Z,{children:(0,o.jsxs)(d.Z,{sx:{flexGrow:1,minHeight:"95vh",width:"100%",bgcolor:"#F5F6FA",padding:4},children:[(0,o.jsxs)(c.Z,{variant:"h4",sx:{fontWeight:"bold",mb:2},children:["Hey ",q.name.replace(/\b\w/g,e=>e.toUpperCase()),","]}),o.jsx(c.Z,{variant:"body1",sx:{color:"#6c757d",mb:4},children:"Explore your benefits now—tap to dive in!"}),o.jsx(d.Z,{sx:{mb:4},children:null===E?o.jsx(d.Z,{sx:{display:"flex",flexDirection:"column",alignItems:"flex-start",justifyContent:"center",height:"150px"},children:o.jsx(u.Z,{size:50,sx:{color:"#6c757d",mb:2}})}):0===E.benefits.filter(e=>e.isActivated).length?(0,o.jsxs)(d.Z,{sx:{display:"flex",flexDirection:"column",alignItems:"flex-start",justifyContent:"center",height:"150px",borderRadius:"12px",backgroundColor:"#F5F6FA",textAlign:"left"},children:[o.jsx(c.Z,{variant:"h6",sx:{fontWeight:"bold",color:"#6c757d",mb:2},children:"No benefits available"}),o.jsx(c.Z,{variant:"body2",sx:{color:"#9E9E9E"},children:"There are currently no active benefits for this type."})]}):o.jsx(p.ZP,{container:!0,spacing:3,children:E.benefits.filter(e=>e.isActivated&&e.imageS3Urls.length>0).map((e,t)=>o.jsx(p.ZP,{item:!0,xs:12,sm:6,md:3.5,children:(0,o.jsxs)(f.Z,{sx:{height:"100%",display:"flex",flexDirection:"column",alignItems:"flex-start",justifyContent:"flex-start",borderRadius:"16px",bgcolor:"#ffffff",padding:3,boxShadow:"none"},children:[(0,v.DD)(e.subType,{marginBottom:15,fontSize:35,color:"#606267"}),(0,o.jsxs)(m.Z,{sx:{flexGrow:1,padding:0,width:"100%"},children:[o.jsx(c.Z,{variant:"h6",sx:{fontWeight:600,textAlign:"left"},children:(0,w.dA)(e.subType)}),(0,o.jsxs)(c.Z,{variant:"body2",sx:{color:"#6c757d",marginTop:1,textAlign:"left"},children:["Check your ",(0,w.dA)(e.subType)," benefits"]})]}),o.jsx(d.Z,{sx:{paddingTop:2,width:"100%"},children:o.jsx(x.Z,{variant:"contained",fullWidth:!0,onClick:()=>M(e._id),sx:{textTransform:"none",borderRadius:"8px",bgcolor:"#e8e8e8",color:"black",boxShadow:"none",padding:"10px 0","&:hover":{backgroundColor:"#d8d8d8",boxShadow:"none"}},children:"View"})})]})},e._id))})})]})})})},69058:(e,t,r)=>{"use strict";r.d(t,{$t:()=>c,SS:()=>p,Y0:()=>i,cd:()=>u,fH:()=>f,mH:()=>m,ov:()=>d,v0:()=>a});var o=r(53148),n=r(25748),s=r(94638);async function i(e,t,r){try{let s=await (0,o.A_)("/benefits/benefit-by-type",{companyId:t,type:r});s&&s.benefits?(console.log("GET BENEFITS FOR TYPE RESPONSE: ",s.benefits),e((0,n.oQ)({benefitType:r,benefits:s.benefits})),e((0,n.nM)("Benefits fetched successfully"))):(console.error("Invalid response format:",s),e((0,n.nM)("Failed to fetch benefits")))}catch(t){console.error("Error fetching benefits:",t),e((0,n.nM)("Error fetching benefits"))}}async function a(e,t,r,s){let i={benefitId:t,page:s};console.log("data",i);let a=await (0,o.A_)("/benefits/one-benefit",i),d={...a,benefitId:t};for(let t of(e((0,n.F5)(d)),a.documents)){let o=decodeURIComponent(t.split("_____")[1]);l(e,t,r,o)}}async function l(e,t,r,s){let i={objectKey:t,companyId:r};console.log("data",i);let a=await (0,o.$R)("/benefits/document",i);if(console.log("VIEW BENEFIT RESPONSE: ",a),a){let r=new Blob([a],{type:"application/pdf"}),o=URL.createObjectURL(r);e((0,n.D7)([{documentObjectKey:t,document:o,originalFileName:s}]))}}let d=async(e,t,r,n,a)=>200===(await (0,o.j0)("/benefits/toggle-benefits/",{benefitId:r,companyId:t,isActivated:n})).status&&(await i(e,t,a),await (0,s.N)(e,t),!0);async function c(e,t,r,s){let i=new FormData;s.forEach(e=>i.append("documents",e)),i.append("companyId",r),i.append("benefitId",t);try{console.log("uploadDocument",i);let a=await (0,o.iG)("/benefits/add/document",i),d=a.data.objectKeys;if(console.log("newObjectKeys",d),200===a.status)return d.forEach((o,i)=>{let a=s[i].name;e((0,n.H_)({benefitId:t,document:o})),l(e,o,r,a)}),e((0,n.nM)("Document added successfully")),!0;return console.error("Error adding document:",a.data.error),e((0,n.nM)("Failed to add document")),!1}catch(t){return console.error("Error adding document:",t),e((0,n.nM)("Error adding document")),!1}}async function u(e,t,r,s){try{let i=await (0,o.j0)("/benefits/delete/document",{benefitId:t,companyId:r,objectKey:s});if(200===i.status)return e((0,n.iH)({benefitId:t,document:s})),e((0,n.nM)("Document deleted successfully")),!0;return console.error("Error deleting document:",i.data.error),e((0,n.nM)("Failed to delete document")),!1}catch(t){return console.error("Error deleting document:",t),e((0,n.nM)("Error deleting document")),!1}}async function p(e,t,r,s){try{let i=await (0,o.j0)("/benefits/add/links",{benefitId:t,companyId:r,urls:[s]});if(200===i.status)return e((0,n.MJ)({benefitId:t,link:s})),e((0,n.nM)("Link added successfully")),!0;return console.error("Error adding link:",i.data.error),e((0,n.nM)("Failed to add link")),!1}catch(t){return console.error("Error adding link:",t),e((0,n.nM)("Error adding link")),!1}}async function f(e,t,r,s){try{let i=await (0,o.j0)("/benefits/delete/link",{benefitId:t,companyId:r,urls:s});if(200===i.status)return e((0,n.Yw)({benefitId:t,link:s})),e((0,n.nM)("Link deleted successfully")),!0;return console.error("Error deleting link:",i.data.error),e((0,n.nM)("Failed to delete link")),!1}catch(t){return console.error("Error deleting link:",t),e((0,n.nM)("Error deleting link")),!1}}async function m(e,t){let r=new FormData;r.append("logoImage",t);try{console.log("uploading company logo",r);let t=await (0,o.iG)("/admin/update-company-logo",r);if(await (0,s.aK)(e),200===t.status)return console.log("Company logo updated successfully"),e((0,n.nM)("Company logo updated successfully")),!0;return console.error("Error updating company logo:",t.data.error),e((0,n.nM)("Failed to update company logo")),!1}catch(t){return console.error("Error updating company logo:",t),e((0,n.nM)("Error updating company logo")),!1}}},30638:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>o});let o=(0,r(68570).createProxy)(String.raw`C:\beno\project_dev\qharmony-frontend-v1\src\app\viewBenefitsByType\[benefitType]\page.tsx#default`)}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),o=t.X(0,[8948,1183,6621,9066,1999,3253,928,8097,8522,3141,6027,576,6305,401,2549],()=>r(52583));module.exports=o})();