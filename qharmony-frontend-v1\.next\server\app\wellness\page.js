(()=>{var e={};e.id=3967,e.ids=[3967],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},27790:e=>{"use strict";e.exports=require("assert")},84770:e=>{"use strict";e.exports=require("crypto")},17702:e=>{"use strict";e.exports=require("events")},92048:e=>{"use strict";e.exports=require("fs")},32615:e=>{"use strict";e.exports=require("http")},35240:e=>{"use strict";e.exports=require("https")},19801:e=>{"use strict";e.exports=require("os")},55315:e=>{"use strict";e.exports=require("path")},76162:e=>{"use strict";e.exports=require("stream")},74026:e=>{"use strict";e.exports=require("string_decoder")},74175:e=>{"use strict";e.exports=require("tty")},17360:e=>{"use strict";e.exports=require("url")},21764:e=>{"use strict";e.exports=require("util")},71568:e=>{"use strict";e.exports=require("zlib")},98061:e=>{"use strict";e.exports=require("node:assert")},92761:e=>{"use strict";e.exports=require("node:async_hooks")},72254:e=>{"use strict";e.exports=require("node:buffer")},40027:e=>{"use strict";e.exports=require("node:console")},6005:e=>{"use strict";e.exports=require("node:crypto")},65714:e=>{"use strict";e.exports=require("node:diagnostics_channel")},15673:e=>{"use strict";e.exports=require("node:events")},88849:e=>{"use strict";e.exports=require("node:http")},42725:e=>{"use strict";e.exports=require("node:http2")},87503:e=>{"use strict";e.exports=require("node:net")},38846:e=>{"use strict";e.exports=require("node:perf_hooks")},39630:e=>{"use strict";e.exports=require("node:querystring")},84492:e=>{"use strict";e.exports=require("node:stream")},31764:e=>{"use strict";e.exports=require("node:tls")},41041:e=>{"use strict";e.exports=require("node:url")},47261:e=>{"use strict";e.exports=require("node:util")},93746:e=>{"use strict";e.exports=require("node:util/types")},24086:e=>{"use strict";e.exports=require("node:worker_threads")},65628:e=>{"use strict";e.exports=require("node:zlib")},26081:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>p,originalPathname:()=>d,pages:()=>c,routeModule:()=>x,tree:()=>u}),r(26035),r(33709),r(35866);var s=r(23191),o=r(88716),n=r(37922),i=r.n(n),a=r(95231),l={};for(let e in a)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>a[e]);r.d(t,l);let u=["",{children:["wellness",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,26035)),"C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\wellness\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,33709)),"C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,35866,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],c=["C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\wellness\\page.tsx"],d="/wellness/page",p={require:r,loadChunk:()=>Promise.resolve()},x=new s.AppPageRouteModule({definition:{kind:o.x.APP_PAGE,page:"/wellness/page",pathname:"/wellness",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:u}})},38733:(e,t,r)=>{Promise.resolve().then(r.bind(r,81602))},81602:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>u});var s=r(10326),o=r(17577),n=r(35047),i=r(44099);let a=(0,r(89009).GU)(),l={async getQuestions(){try{return(await i.Z.get(`${a}/wellness/questions`)).data.data.questions}catch(e){throw console.error("Error fetching wellness questions:",e),e}},async submitAnswers(e,t,r){try{let s={user_id:e,team_id:t,user_answer:{answers:r}};console.log("Sending payload to API:",s),console.log("Endpoint URL:",`${a}/wellness/predictions`);let o=await i.Z.post(`${a}/wellness/predictions`,s);return console.log("API response:",o.data),o.data.data}catch(e){throw console.error("Error submitting wellness answers:",e),i.Z.isAxiosError(e)&&(console.error("Response data:",e.response?.data),console.error("Status code:",e.response?.status)),e}}};function u(){let e=(0,n.useRouter)(),[t,r]=(0,o.useState)([]),[i,a]=(0,o.useState)(0),[u,c]=(0,o.useState)({}),[d,p]=(0,o.useState)(""),[x,m]=(0,o.useState)(!0),[g,h]=(0,o.useState)("123"),[q,w]=(0,o.useState)("456"),f=async()=>{try{m(!0),console.log("Submitting answers:",g,q,u);let t=await l.submitAnswers(g,q,u);console.log("Submission result:",t),localStorage.setItem("wellness_results",JSON.stringify(t)),localStorage.removeItem("wellness_user_answers"),e.push("/wellness/results")}catch(e){console.error("Error submitting wellness answers:",e),p("Failed to submit answers. Please try again.")}finally{m(!1)}},b=e=>{if(!t[i])return;let r=e.target.value;"numeric"===t[i].type&&isNaN(r=parseFloat(r))&&(r=""),c(e=>({...e,[t[i].id]:r}))},v=e=>{t[i]&&(c(r=>({...r,[t[i].id]:e})),p(""))};if(x)return s.jsx("div",{className:"loading",children:"Loading..."});if(!t||0===t.length)return s.jsx("div",{className:"error",children:"Failed to load questions. Please refresh the page."});let y=t[i];return(0,s.jsxs)("div",{className:"quiz-container",children:[(0,s.jsxs)("div",{className:"nav-header",children:[s.jsx("button",{className:"back-arrow",onClick:()=>a(e=>Math.max(e-1,0)),disabled:0===i,children:"\xab"}),s.jsx("div",{className:"progress-bar-wrapper",children:s.jsx("div",{className:"progress-bar-inner",style:{width:`${(i+1)/t.length*100}%`}})})]}),s.jsx("h2",{className:"question-text",children:y.text}),s.jsx("ul",{className:"options-wrapper",children:(()=>{if(!t[i])return null;let e=t[i];return"categorical"===e.type||"ordinal"===e.type?e.options?.map(t=>s.jsx("li",{onClick:()=>v(t),className:`option-card ${u[e.id]===t?"option-selected":""}`,children:t},t)):"boolean"===e.type?["Yes","No"].map(t=>s.jsx("li",{onClick:()=>v(t),className:`option-card ${u[e.id]===t?"option-selected":""}`,children:t},t)):"numeric"===e.type?s.jsx("input",{type:"number",onChange:b,value:u[e.id]||"",className:"numeric-input",placeholder:"Enter your answer"}):null})()}),d&&s.jsx("p",{className:"error",children:d}),s.jsx("div",{className:"btn-container",children:s.jsx("button",{onClick:()=>{if(t[i]){if(!["life_span_grandparents","avg_glucose_level"].includes(t[i].id)&&(!(t[i].id in u)||""===u[t[i].id])){p("Please answer before continuing.");return}p(""),i<t.length-1?a(e=>e+1):f()}},className:"next-btn",disabled:x,children:i===t.length-1?"Finish":"Next"})})]})}r(37842)},89009:(e,t,r)=>{"use strict";r.d(t,{GU:()=>n,bR:()=>s,n5:()=>o});let s=()=>"http://localhost:8080",o=()=>{let e="userid1",t="userId",r=localStorage.getItem(e)||localStorage.getItem(t);return(console.log("\uD83D\uDD0D getUserId debug:",{primaryKey:e,altKey:t,primaryValue:localStorage.getItem(e),altValue:localStorage.getItem(t),finalUserId:r}),r)?r:(console.error("❌ User ID not found in localStorage"),"default-user")},n=()=>"https://bot.benosphere.com"},26035:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(68570).createProxy)(String.raw`C:\beno\project_dev\qharmony-frontend-v1\src\app\wellness\page.tsx#default`)},73881:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>o});var s=r(66621);let o=e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",e.params,"favicon.ico")+""}]},37842:()=>{}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[8948,6401,6621,576],()=>r(26081));module.exports=s})();