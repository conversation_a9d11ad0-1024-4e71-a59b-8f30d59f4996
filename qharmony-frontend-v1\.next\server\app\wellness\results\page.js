(()=>{var e={};e.id=7675,e.ids=[7675],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},27790:e=>{"use strict";e.exports=require("assert")},84770:e=>{"use strict";e.exports=require("crypto")},17702:e=>{"use strict";e.exports=require("events")},92048:e=>{"use strict";e.exports=require("fs")},32615:e=>{"use strict";e.exports=require("http")},35240:e=>{"use strict";e.exports=require("https")},19801:e=>{"use strict";e.exports=require("os")},55315:e=>{"use strict";e.exports=require("path")},76162:e=>{"use strict";e.exports=require("stream")},74026:e=>{"use strict";e.exports=require("string_decoder")},74175:e=>{"use strict";e.exports=require("tty")},17360:e=>{"use strict";e.exports=require("url")},21764:e=>{"use strict";e.exports=require("util")},71568:e=>{"use strict";e.exports=require("zlib")},98061:e=>{"use strict";e.exports=require("node:assert")},92761:e=>{"use strict";e.exports=require("node:async_hooks")},72254:e=>{"use strict";e.exports=require("node:buffer")},40027:e=>{"use strict";e.exports=require("node:console")},6005:e=>{"use strict";e.exports=require("node:crypto")},65714:e=>{"use strict";e.exports=require("node:diagnostics_channel")},15673:e=>{"use strict";e.exports=require("node:events")},88849:e=>{"use strict";e.exports=require("node:http")},42725:e=>{"use strict";e.exports=require("node:http2")},87503:e=>{"use strict";e.exports=require("node:net")},38846:e=>{"use strict";e.exports=require("node:perf_hooks")},39630:e=>{"use strict";e.exports=require("node:querystring")},84492:e=>{"use strict";e.exports=require("node:stream")},31764:e=>{"use strict";e.exports=require("node:tls")},41041:e=>{"use strict";e.exports=require("node:url")},47261:e=>{"use strict";e.exports=require("node:util")},93746:e=>{"use strict";e.exports=require("node:util/types")},24086:e=>{"use strict";e.exports=require("node:worker_threads")},65628:e=>{"use strict";e.exports=require("node:zlib")},84383:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>o.a,__next_app__:()=>p,originalPathname:()=>u,pages:()=>d,routeModule:()=>h,tree:()=>c}),r(5e3),r(33709),r(35866);var t=r(23191),i=r(88716),n=r(37922),o=r.n(n),a=r(95231),l={};for(let e in a)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>a[e]);r.d(s,l);let c=["",{children:["wellness",{children:["results",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,5e3)),"C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\wellness\\results\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,33709)),"C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,35866,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],d=["C:\\beno\\project_dev\\qharmony-frontend-v1\\src\\app\\wellness\\results\\page.tsx"],u="/wellness/results/page",p={require:r,loadChunk:()=>Promise.resolve()},h=new t.AppPageRouteModule({definition:{kind:i.x.APP_PAGE,page:"/wellness/results/page",pathname:"/wellness/results",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},97322:(e,s,r)=>{Promise.resolve().then(r.bind(r,68910))},68910:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>o});var t=r(10326),i=r(17577),n=r(35047);function o(){let e=(0,n.useRouter)(),[s,r]=(0,i.useState)(null),[o,a]=(0,i.useState)(!0),[l,c]=(0,i.useState)(!1),d=()=>{alert("We’ll remind you! (Coming soon)")},u=()=>{alert(`This app provides an estimate of potential lifespan based on general statistical models and user-provided data.
    
It is not a medical diagnosis, prediction of death, or guarantee of future outcomes.

Consult a healthcare professional for accurate health assessments. We are not liable for decisions made based on this tool.`)},p=()=>{localStorage.removeItem("wellness_results"),e.push("/wellness")};if(o)return t.jsx("div",{className:"loading",children:"Loading results..."});if(!s)return t.jsx("div",{className:"wellness-body",children:(0,t.jsxs)("div",{className:"container error-container",children:[t.jsx("h2",{children:"No Results Found"}),t.jsx("p",{children:"We couldn't find your wellness assessment results."}),t.jsx("button",{className:"next-btn",onClick:p,children:"Take Assessment"})]})});let{predictions:h,recommendations:x,sources:m}=s,j=h.life_expectancy.final_adjusted_age.toFixed(1),f=h.heart_disease_probability.toFixed(1),g=h.stroke_prediction.stroke_probability.toFixed(1),v=(100*h.life_expectancy.survival_probability_past_100).toFixed(2);return(0,t.jsxs)("div",{className:"wellness-body",children:[l&&t.jsx("div",{className:"demo-banner",children:(0,t.jsxs)("p",{children:["Viewing demo results. ",t.jsx("button",{onClick:p,children:"Take the assessment"})," to get personalized results."]})}),(0,t.jsxs)("nav",{className:"wellness-nav",children:[t.jsx("a",{href:"#longevity",children:"Longevity"}),t.jsx("a",{href:"#health",children:"Health Risks"}),t.jsx("a",{href:"#recommendations",children:"Recommendations"}),t.jsx("a",{href:"#benefits",children:"Benefits"})]}),(0,t.jsxs)("section",{id:"longevity",className:"section container",children:[t.jsx("h2",{children:"Your Longevity Report \uD83C\uDF89"}),(0,t.jsxs)("p",{className:"highlight",children:["Expected Longevity: ",j," years",t.jsx("a",{href:"#",onClick:e=>{e.preventDefault(),u()},children:"(disclaimer)"})]}),t.jsx("p",{children:"Based on the data provided, you're likely to live around this age."}),(0,t.jsxs)("p",{children:[t.jsx("strong",{children:"Chance of Living Past 100:"})," ",v,"%"]}),(0,t.jsxs)("div",{className:"centered",children:[t.jsx("button",{className:"share-button",onClick:()=>{navigator.clipboard.writeText(window.location.href),alert("Link copied! Challenge your friends to check their longevity too!")},children:"Challenge My Friends \uD83D\uDCAA"}),t.jsx("button",{className:"reminder-button",onClick:d,children:"Send Me Reminders ⏰"})]}),(0,t.jsxs)("div",{className:"centered faces",children:[t.jsx("img",{src:"https://randomuser.me/api/portraits/men/75.jpg",alt:"User 1"}),t.jsx("img",{src:"https://randomuser.me/api/portraits/women/65.jpg",alt:"User 2"}),t.jsx("img",{src:"https://randomuser.me/api/portraits/men/35.jpg",alt:"User 3"}),t.jsx("p",{children:"You're in the top 25% of healthiest users!"})]})]}),(0,t.jsxs)("section",{id:"health",className:"section container",children:[t.jsx("h2",{children:"Health Risk Overview \uD83E\uDE7A"}),(0,t.jsxs)("p",{children:[t.jsx("strong",{children:"Heart Disease Risk:"})," ",f,"%"]}),(0,t.jsxs)("p",{children:[t.jsx("strong",{children:"Stroke Risk:"})," ",1>parseFloat(g)?"Very Low":"Moderate"," (",g,"%)"]}),(0,t.jsxs)("p",{children:[t.jsx("strong",{children:"Stroke Prediction:"})," ",h.stroke_prediction.stroke]})]}),(0,t.jsxs)("section",{id:"recommendations",className:"section container",children:[t.jsx("h2",{children:"Personalized Health Recommendations \uD83D\uDCA1"}),x.map((e,s)=>t.jsx("p",{children:e},s)),t.jsx("div",{className:"centered",children:t.jsx("button",{className:"reminder-button",onClick:d,children:"Send Me Reminders ⏰"})})]}),(0,t.jsxs)("section",{id:"benefits",className:"section container",children:[t.jsx("h2",{children:"Benefits + Resources \uD83D\uDCC4"}),t.jsx("div",{className:"benefits-container",children:m.length>0?m.map((e,s)=>(0,t.jsxs)("a",{href:e,target:"_blank",rel:"noopener noreferrer",className:"benefit-link",children:["Benefit Document ",s+1]},s)):t.jsx("div",{className:"no-benefits-message",children:"No specific benefit documents found for your health profile."})})]}),t.jsx("div",{className:"centered",children:t.jsx("button",{className:"share-button",onClick:p,children:"Retake Assessment"})}),(0,t.jsxs)("footer",{className:"wellness-footer",children:[(0,t.jsxs)("div",{className:"disclaimer container",children:[(0,t.jsxs)("p",{children:["⚠️ ",t.jsx("strong",{children:"Disclaimer:"})," This is not a medical diagnosis. It's a statistical estimate only."]}),t.jsx("p",{children:"\uD83E\uDE7A Consult a healthcare professional for accurate health assessments."}),t.jsx("p",{children:t.jsx("em",{children:"This is an estimate, not a prediction—talk to your doctor!"})})]}),t.jsx("p",{children:"\xa9 2025 BenOsphere. All rights reserved."})]})]})}r(65307)},5e3:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>t});let t=(0,r(68570).createProxy)(String.raw`C:\beno\project_dev\qharmony-frontend-v1\src\app\wellness\results\page.tsx#default`)},73881:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>i});var t=r(66621);let i=e=>[{type:"image/x-icon",sizes:"16x16",url:(0,t.fillMetadataSegment)(".",e.params,"favicon.ico")+""}]},65307:()=>{}};var s=require("../../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),t=s.X(0,[8948,6401,6621,576],()=>r(84383));module.exports=t})();