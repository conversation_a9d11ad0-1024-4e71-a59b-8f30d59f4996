"use strict";exports.id=2549,exports.ids=[2549],exports.modules={33198:(e,r,o)=>{o.d(r,{Z:()=>y});var t=o(17577),n=o(41135),a=o(88634),i=o(91703),s=o(13643),l=o(2791),d=o(51426),x=o(10326);let c=(0,d.Z)((0,x.jsx)("path",{d:"M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"}),"Person");var g=o(71685),p=o(97898);function f(e){return(0,p.ZP)("MuiAvatar",e)}(0,g.Z)("MuiAvatar",["root","colorDefault","circular","rounded","square","img","fallback"]);var h=o(31121);let u=e=>{let{classes:r,variant:o,colorDefault:t}=e;return(0,a.Z)({root:["root",o,t&&"colorDefault"],img:["img"],fallback:["fallback"]},f,r)},m=(0,i.default)("div",{name:"MuiAvatar",slot:"Root",overridesResolver:(e,r)=>{let{ownerState:o}=e;return[r.root,r[o.variant],o.colorDefault&&r.colorDefault]}})((0,s.Z)(({theme:e})=>({position:"relative",display:"flex",alignItems:"center",justifyContent:"center",flexShrink:0,width:40,height:40,fontFamily:e.typography.fontFamily,fontSize:e.typography.pxToRem(20),lineHeight:1,borderRadius:"50%",overflow:"hidden",userSelect:"none",variants:[{props:{variant:"rounded"},style:{borderRadius:(e.vars||e).shape.borderRadius}},{props:{variant:"square"},style:{borderRadius:0}},{props:{colorDefault:!0},style:{color:(e.vars||e).palette.background.default,...e.vars?{backgroundColor:e.vars.palette.Avatar.defaultBg}:{backgroundColor:e.palette.grey[400],...e.applyStyles("dark",{backgroundColor:e.palette.grey[600]})}}}]}))),b=(0,i.default)("img",{name:"MuiAvatar",slot:"Img",overridesResolver:(e,r)=>r.img})({width:"100%",height:"100%",textAlign:"center",objectFit:"cover",color:"transparent",textIndent:1e4}),v=(0,i.default)(c,{name:"MuiAvatar",slot:"Fallback",overridesResolver:(e,r)=>r.fallback})({width:"75%",height:"75%"}),y=t.forwardRef(function(e,r){let o=(0,l.i)({props:e,name:"MuiAvatar"}),{alt:a,children:i,className:s,component:d="div",slots:c={},slotProps:g={},imgProps:p,sizes:f,src:y,srcSet:A,variant:j="circular",...Z}=o,w=null,S={...o,component:d,variant:j},k=function({crossOrigin:e,referrerPolicy:r,src:o,srcSet:n}){let[a,i]=t.useState(!1);return t.useEffect(()=>{if(!o&&!n)return;i(!1);let t=!0,a=new Image;return a.onload=()=>{t&&i("loaded")},a.onerror=()=>{t&&i("error")},a.crossOrigin=e,a.referrerPolicy=r,a.src=o,n&&(a.srcset=n),()=>{t=!1}},[e,r,o,n]),a}({...p,..."function"==typeof g.img?g.img(S):g.img,src:y,srcSet:A}),C=y||A,I=C&&"error"!==k;S.colorDefault=!I,delete S.ownerState;let B=u(S),[R,T]=(0,h.Z)("img",{className:B.img,elementType:b,externalForwardedProps:{slots:c,slotProps:{img:{...p,...g.img}}},additionalProps:{alt:a,src:y,srcSet:A,sizes:f},ownerState:S});return w=I?(0,x.jsx)(R,{...T}):i||0===i?i:C&&a?a[0]:(0,x.jsx)(v,{ownerState:S,className:B.fallback}),(0,x.jsx)(m,{as:d,className:(0,n.Z)(B.root,s),ref:r,...Z,ownerState:S,children:w})})},12549:(e,r,o)=>{o.d(r,{Z:()=>P});var t=o(10326),n=o(6283),a=o(95148),i=o(19074),s=o(25609),l=o(84979),d=o(71411),x=o(24003),c=o(78969),g=o(25886),p=o(42265),f=o(46226),h=o(31870),u=o(17577),m=o(94638),b=o(32049),v=o(30656),y=o(35047),A=o(89223),j=o(25842),Z=o(18742);let w={src:"/_next/static/media/slack_logo.3849d8c9.png",height:1024,width:1024,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAYAAADED76LAAAA9ElEQVR42g3KMUoDURRA0fve/xOdISESkIBFChVRtBMsJG7ABSiWNi7Azg24B3dgbSNYiGAqC4tgkahosIk2YwwkIxnnP4fL7Y7sdEYOKIzQFZ1/zdLTb9HmBrBrFE4f2gvFVIWyK8z6SG0Lwko5bxYKbd+nnWoIXxF2HBu/cxSJR/4qZuebaNd3kqS3lOfRUEgsSlJGxQVSaZQmBun74fZe3YjvhGyijKV2MyCftg6D6KTC+Mlna+urGC/iwon13I8Gf2kS1YXQyKU5E4DB4gFW9Z+8y0ft+vHWZpEBZ6o5OmgdOWKHeHs2tIuEZWAfIITI/QMNTWKSnIjo2AAAAABJRU5ErkJggg==",blurWidth:8,blurHeight:8},S={src:"/_next/static/media/teams_logo.cb463aaf.png",height:512,width:512,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAYAAADED76LAAAA8ElEQVR42mNABjXNb9yqGl9urGl+rQIXDIzYw8PAsFAIyGSuqH++H6jgf23Lmzr3oKMLAqJOrWPwCtr1vKzq6Luq2qNvgiL2/yyuerS9Z/Jvee/Q4+3+kadaGAxttv9fufra/y3bbv83stn8P7fk8pX6tndX49IvX/IKPX6cQd102/+unlP/p8288FdWb9O/wvLr/+ta3/0HKvgPNOU/g5vfri+Jaft/pWTu/+/gvuFfbvGVX0AT/sWmXf4PNOEPQ1TCQVkGhmaBpMzznQVl1/+X1z3+D/TF95jUS/1Ah3oyIIP6tg8NQMnNVY0vPGBiAJjtfJ3TGjMgAAAAAElFTkSuQmCC",blurWidth:8,blurHeight:8};var k=o(53148);let C=()=>{let e=(0,h.T)(),r=(0,y.useRouter)(),o=(0,y.usePathname)(),C=(0,j.v9)(e=>e.user.userProfile.isAdmin),I=(0,j.v9)(e=>e.user.userProfile.isBroker),B=(0,j.v9)(e=>(0,b.MP)(e)),R=(0,j.v9)(e=>e.company?.companyDetails?.details?.logo),T=(0,h.C)(e=>e.company.companyBenefitTypes),D=(0,h.C)(e=>e.user.selectedBenefitType),[P,W]=(0,u.useState)(!1),z=(0,j.v9)(e=>e.user.userProfile.email);(0,u.useEffect)(()=>{W("true"===localStorage.getItem("isTeamsApp1"))},[]),(0,u.useEffect)(()=>{B&&(0,m.N)(e,B)},[B,e]);let E=o=>{e((0,b.v2)(o)),r.push(`/viewBenefitsByType/${o}`)};return(0,t.jsxs)(n.Z,{sx:{display:"flex",bgcolor:"#f6f8fc",minHeight:"100vh"},children:[t.jsx(a.ZP,{}),t.jsx(i.ZP,{sx:{width:240,flexShrink:0,"& .MuiDrawer-paper":{width:240,boxSizing:"border-box",bgcolor:"#ffffff",position:"relative",display:"flex",flexDirection:"column"}},variant:"permanent",anchor:"left",children:(0,t.jsxs)(n.Z,{sx:{padding:0,height:"100%",position:"relative",display:"flex",flexDirection:"column"},children:[t.jsx(n.Z,{sx:{height:"80px",borderBottom:"1px solid #D2D2D2",display:"flex",alignItems:"center",justifyContent:"center",cursor:"pointer"},onClick:()=>r.push("/dashboard"),children:R?t.jsx("img",{src:R,alt:"Company Logo",style:{height:"50px",width:"auto"}}):(0,t.jsxs)(n.Z,{sx:{display:"flex",alignItems:"center"},children:[t.jsx(f.default,{src:v.Z,alt:"BenOsphere Logo",width:40,height:40}),t.jsx(s.Z,{sx:{fontWeight:800,fontSize:"1.5rem",ml:1},children:"BenOsphere"})]})}),(0,t.jsxs)(n.Z,{sx:{flexGrow:1,overflow:"auto",paddingX:2.5},children:[t.jsx(s.Z,{sx:{fontWeight:860,fontSize:"14px",color:"rgba(0, 0, 0, 0.4)",marginBottom:"8px"},children:"MY BENEFITS"}),t.jsx(l.Z,{children:T.length>0?T.map(e=>t.jsx(d.ZP,{disablePadding:!0,children:(0,t.jsxs)(x.Z,{onClick:()=>E(e),sx:{borderRadius:2,"&:hover":{backgroundColor:"#f0f0f0"},bgcolor:D===e?"#f0f4ff":"inherit"},children:[t.jsx(c.Z,{sx:{minWidth:0,mr:2,color:"black"},children:(0,Z.RS)(e)}),t.jsx(g.Z,{primary:(0,m.Ur)(e),sx:{fontWeight:"medium",color:"#333"}})]})},e)):t.jsx(s.Z,{variant:"body1",sx:{color:"#999",padding:2.5},children:"No benefits available at the moment"})})]}),!["/qHarmonyBot","/proactive-messaging","/notification-history",/^\/notifications-analytics\/[^/]+$/].some(e=>"string"==typeof e?o.includes(e):e.test(o))&&!P&&(0,t.jsxs)(n.Z,{sx:{position:"fixed",bottom:"80px",right:"20px",display:"flex",alignItems:"center",bgcolor:"#ffffff",borderRadius:"30px",padding:"10px 20px",boxShadow:"0px 4px 12px rgba(0, 0, 0, 0.1)",cursor:"pointer"},onClick:()=>r.push("/qHarmonyBot"),children:[t.jsx(f.default,{src:A.Z,alt:"AI Chat",style:{borderRadius:"100%",width:"40px",height:"40px",marginRight:"10px"}}),(0,t.jsxs)(n.Z,{children:[t.jsx(s.Z,{variant:"body1",sx:{fontWeight:"bold"},children:"Chat with Brea"}),t.jsx(s.Z,{variant:"body2",sx:{color:"#6c757d"},children:"24/7 available"})]})]}),(0,t.jsxs)(n.Z,{sx:{position:"relative",marginTop:"auto",paddingBottom:"20px",display:"flex",flexDirection:"column",alignItems:"center",gap:"10px"},children:[C&&(0,t.jsxs)(t.Fragment,{children:[t.jsx(s.Z,{sx:{fontWeight:700,paddingY:1,fontSize:".7rem",color:"rgba(0, 0, 0, 0.4)"},children:"ADMIN HUB"}),t.jsx(p.Z,{variant:"contained",onClick:()=>r.push("/manageBenefits/"),sx:{textTransform:"none",borderRadius:"8px",bgcolor:"rgba(0, 0, 0, 0.06)",color:"black",boxShadow:"none",width:"90%",paddingY:"10px","&:hover":{backgroundColor:"rgba(0, 0, 0, 0.1)",boxShadow:"none"}},children:"Upload Benefits"}),t.jsx(p.Z,{variant:"contained",onClick:()=>r.push("/manage-groups/"),sx:{textTransform:"none",borderRadius:"8px",bgcolor:"rgba(0, 0, 0, 0.06)",color:"black",boxShadow:"none",width:"90%",paddingY:"10px","&:hover":{backgroundColor:"rgba(0, 0, 0, 0.1)",boxShadow:"none"}},children:"Assign Benefits"}),t.jsx(p.Z,{variant:"contained",onClick:()=>r.push("/team-members/"),sx:{textTransform:"none",borderRadius:"8px",bgcolor:"rgba(0, 0, 0, 0.06)",color:"black",boxShadow:"none",width:"90%",paddingY:"10px","&:hover":{backgroundColor:"rgba(0, 0, 0, 0.1)",boxShadow:"none"}},children:"Manage Team"}),k.BO.includes(z)&&t.jsx(p.Z,{variant:"contained",onClick:()=>r.push("/proactive-messaging/"),sx:{textTransform:"none",borderRadius:"8px",bgcolor:"rgba(0, 0, 0, 0.06)",color:"black",boxShadow:"none",width:"90%",paddingY:"10px","&:hover":{backgroundColor:"rgba(0, 0, 0, 0.1)",boxShadow:"none"}},children:"Broadcast Center"})]}),I&&t.jsx(t.Fragment,{children:t.jsx(p.Z,{variant:"contained",onClick:()=>r.push("/manage-companies/"),sx:{textTransform:"none",borderRadius:"8px",bgcolor:"rgba(0, 0, 0, 0.06)",color:"black",boxShadow:"none",width:"90%",paddingY:"10px","&:hover":{backgroundColor:"rgba(0, 0, 0, 0.1)",boxShadow:"none"}},children:"Add Employers"})}),!P&&(0,t.jsxs)(n.Z,{sx:{display:"flex",flexDirection:"column",gap:1.5,alignItems:"center",width:"90%"},children:[(0,t.jsxs)(p.Z,{variant:"outlined",sx:{display:"flex",justifyContent:"center",width:"100%",borderRadius:"12px",padding:"9px 16px",border:"1px solid rgba(0, 0, 0, 0.12)",color:"black",fontSize:"16px",fontWeight:500,textTransform:"none","&:hover":{backgroundColor:"rgba(0, 0, 0, 0.04)"}},children:[t.jsx(f.default,{src:w,alt:"Teams Logo",width:21,height:21,style:{marginRight:"8px"}}),"Add to Slack"]}),(0,t.jsxs)(p.Z,{variant:"outlined",sx:{display:"flex",justifyContent:"center",width:"100%",borderRadius:"12px",padding:"9px 16px",border:"1px solid rgba(0, 0, 0, 0.12)",color:"black",fontSize:"16px",fontWeight:500,textTransform:"none","&:hover":{backgroundColor:"rgba(0, 0, 0, 0.04)"}},children:[t.jsx(f.default,{src:S,alt:"Teams Logo",width:21,height:21,style:{marginRight:"8px"}}),"Add to Teams"]})]})]})]})})]})};var I=o(33198),B=o(82400),R=o(22758),T=o(88563);let D=()=>{let{logout:e}=(0,R.a)(),r=(0,h.C)(e=>e.user.userProfile),[o,a]=(0,u.useState)(!1);(0,u.useEffect)(()=>{a("true"===localStorage.getItem("isTeamsApp1"))},[]);let i=(0,j.v9)(e=>e.user.userProfile.isAdmin),l=(0,j.v9)(e=>e.user.userProfile.isBroker);return t.jsx(n.Z,{sx:{bgcolor:"white",padding:2,textAlign:"center",height:"80px",borderBottom:"1px solid #D2D2D2"},children:(0,t.jsxs)(n.Z,{sx:{display:"flex",alignItems:"center",justifyContent:"space-between"},children:[(0,t.jsxs)(n.Z,{sx:{display:"flex",alignItems:"center"},children:[t.jsx(I.Z,{sx:{bgcolor:"black",color:"#ffffff",width:35,height:35,fontSize:"1.2rem",mr:1.5,ml:2,display:"flex",alignItems:"center",justifyContent:"center",paddingBottom:"1px",fontWeight:800},children:(e=>{if(!e)return"";let[r,o]=e.split(" ");return`${r[0].toUpperCase()}${o?o[0].toUpperCase():""}`})(r.name)}),t.jsx(s.Z,{variant:"h4",sx:{mb:0,fontWeight:"bold",display:"flex",alignItems:"center",justifyContent:"flex-start",textAlign:"flex-start",fontSize:"16px",mr:1.5},children:r.name.replace(/\b\w/g,e=>e.toUpperCase())}),i&&t.jsx(n.Z,{sx:{bgcolor:"rgba(0, 0, 0, 0.06)",borderRadius:"8px",padding:"4px 8px",display:"flex",alignItems:"center",justifyContent:"center",fontWeight:"bold",fontSize:"12px",color:"#333"},children:"ADMIN"}),l&&t.jsx(n.Z,{sx:{bgcolor:"#f5f5f5",borderRadius:"8px",padding:"2px 8px",display:"flex",alignItems:"center",justifyContent:"center",fontWeight:"bold",fontSize:"0.9rem",color:"#333"},children:"BROKER"})]}),(0,t.jsxs)(n.Z,{sx:{display:"flex",alignItems:"center"},children:[!o&&(0,t.jsxs)(p.Z,{sx:{backgroundColor:"transparent",color:"#333",textTransform:"none",padding:"8px 8px",display:"flex",alignItems:"center",justifyContent:"flex-start",gap:"2px","&:hover":{backgroundColor:"transparent",color:"#555"},boxShadow:"none"},children:[t.jsx(n.Z,{sx:{mt:.5,mr:.5},children:t.jsx(T.Z,{})}),t.jsx(s.Z,{sx:{fontWeight:500,fontSize:"14px"},children:"Guide"})]}),!o&&(0,t.jsxs)(p.Z,{onClick:e,sx:{backgroundColor:"transparent",color:"#333",marginLeft:1,textTransform:"none",padding:"8px 16px",display:"flex",alignItems:"center",justifyContent:"flex-start",gap:"8px","&:hover":{backgroundColor:"transparent",color:"#555"},boxShadow:"none"},children:[t.jsx(B.Z,{sx:{fontSize:"18px"}}),t.jsx(s.Z,{sx:{fontWeight:500,fontSize:"14px"},children:"Logout"})]})]})]})})},P=e=>{let r=r=>(0,t.jsxs)(n.Z,{sx:{display:"flex",flexGrow:1},children:[t.jsx(C,{}),(0,t.jsxs)(n.Z,{component:"main",sx:{flexGrow:1,width:"calc(100% - 240px)"},children:[t.jsx(D,{}),t.jsx(e,{...r})]})]});return r.displayName=`WithSidebar(${e.displayName||e.name||"Component"})`,r}}};