"use strict";exports.id=2549,exports.ids=[2549],exports.modules={12549:(e,o,r)=>{r.d(o,{Z:()=>D});var t=r(10326),n=r(6283),i=r(95148),a=r(19074),s=r(25609),l=r(84979),d=r(71411),x=r(24003),c=r(78969),g=r(25886),p=r(42265),h=r(46226),f=r(31870),b=r(17577),m=r(94638),u=r(32049),A=r(30656),j=r(35047),y=r(89223),v=r(25842),Z=r(18742);let C={src:"/_next/static/media/slack_logo.3849d8c9.png",height:1024,width:1024,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAYAAADED76LAAAA9ElEQVR42g3KMUoDURRA0fve/xOdISESkIBFChVRtBMsJG7ABSiWNi7Azg24B3dgbSNYiGAqC4tgkahosIk2YwwkIxnnP4fL7Y7sdEYOKIzQFZ1/zdLTb9HmBrBrFE4f2gvFVIWyK8z6SG0Lwko5bxYKbd+nnWoIXxF2HBu/cxSJR/4qZuebaNd3kqS3lOfRUEgsSlJGxQVSaZQmBun74fZe3YjvhGyijKV2MyCftg6D6KTC+Mlna+urGC/iwon13I8Gf2kS1YXQyKU5E4DB4gFW9Z+8y0ft+vHWZpEBZ6o5OmgdOWKHeHs2tIuEZWAfIITI/QMNTWKSnIjo2AAAAABJRU5ErkJggg==",blurWidth:8,blurHeight:8},S={src:"/_next/static/media/teams_logo.cb463aaf.png",height:512,width:512,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAYAAADED76LAAAA8ElEQVR42mNABjXNb9yqGl9urGl+rQIXDIzYw8PAsFAIyGSuqH++H6jgf23Lmzr3oKMLAqJOrWPwCtr1vKzq6Luq2qNvgiL2/yyuerS9Z/Jvee/Q4+3+kadaGAxttv9fufra/y3bbv83stn8P7fk8pX6tndX49IvX/IKPX6cQd102/+unlP/p8288FdWb9O/wvLr/+ta3/0HKvgPNOU/g5vfri+Jaft/pWTu/+/gvuFfbvGVX0AT/sWmXf4PNOEPQ1TCQVkGhmaBpMzznQVl1/+X1z3+D/TF95jUS/1Ah3oyIIP6tg8NQMnNVY0vPGBiAJjtfJ3TGjMgAAAAAElFTkSuQmCC",blurWidth:8,blurHeight:8};var w=r(53148);let k=()=>{let e=(0,f.T)(),o=(0,j.useRouter)(),r=(0,j.usePathname)(),k=(0,v.v9)(e=>e.user.userProfile.isAdmin),I=(0,v.v9)(e=>e.user.userProfile.isBroker),B=(0,v.v9)(e=>(0,u.MP)(e)),R=(0,v.v9)(e=>e.company?.companyDetails?.details?.logo),T=(0,f.C)(e=>e.company.companyBenefitTypes),W=(0,f.C)(e=>e.user.selectedBenefitType),[D,E]=(0,b.useState)(!1),z=(0,v.v9)(e=>e.user.userProfile.email);(0,b.useEffect)(()=>{E("true"===localStorage.getItem("isTeamsApp1"))},[]),(0,b.useEffect)(()=>{B&&(0,m.N)(e,B)},[B,e]);let P=r=>{e((0,u.v2)(r)),o.push(`/viewBenefitsByType/${r}`)};return(0,t.jsxs)(n.Z,{sx:{display:"flex",bgcolor:"#f6f8fc",minHeight:"100vh"},children:[t.jsx(i.ZP,{}),t.jsx(a.ZP,{sx:{width:240,flexShrink:0,"& .MuiDrawer-paper":{width:240,boxSizing:"border-box",bgcolor:"#ffffff",position:"relative",display:"flex",flexDirection:"column"}},variant:"permanent",anchor:"left",children:(0,t.jsxs)(n.Z,{sx:{padding:0,height:"100%",position:"relative",display:"flex",flexDirection:"column"},children:[t.jsx(n.Z,{sx:{height:"80px",borderBottom:"1px solid #D2D2D2",display:"flex",alignItems:"center",justifyContent:"center",cursor:"pointer"},onClick:()=>o.push("/dashboard"),children:R?t.jsx("img",{src:R,alt:"Company Logo",style:{height:"50px",width:"auto"}}):(0,t.jsxs)(n.Z,{sx:{display:"flex",alignItems:"center"},children:[t.jsx(h.default,{src:A.Z,alt:"BenOsphere Logo",width:40,height:40}),t.jsx(s.Z,{sx:{fontWeight:800,fontSize:"1.5rem",ml:1},children:"BenOsphere"})]})}),(0,t.jsxs)(n.Z,{sx:{flexGrow:1,overflow:"auto",paddingX:2.5},children:[t.jsx(s.Z,{sx:{fontWeight:860,fontSize:"14px",color:"rgba(0, 0, 0, 0.4)",marginBottom:"8px"},children:"MY BENEFITS"}),t.jsx(l.Z,{children:T.length>0?T.map(e=>t.jsx(d.ZP,{disablePadding:!0,children:(0,t.jsxs)(x.Z,{onClick:()=>P(e),sx:{borderRadius:2,"&:hover":{backgroundColor:"#f0f0f0"},bgcolor:W===e?"#f0f4ff":"inherit"},children:[t.jsx(c.Z,{sx:{minWidth:0,mr:2,color:"black"},children:(0,Z.RS)(e)}),t.jsx(g.Z,{primary:(0,m.Ur)(e),sx:{fontWeight:"medium",color:"#333"}})]})},e)):t.jsx(s.Z,{variant:"body1",sx:{color:"#999",padding:2.5},children:"No benefits available at the moment"})})]}),!["/qHarmonyBot","/proactive-messaging","/notification-history",/^\/notifications-analytics\/[^/]+$/].some(e=>"string"==typeof e?r.includes(e):e.test(r))&&!D&&(0,t.jsxs)(n.Z,{sx:{position:"fixed",bottom:"80px",right:"20px",display:"flex",alignItems:"center",bgcolor:"#ffffff",borderRadius:"30px",padding:"10px 20px",boxShadow:"0px 4px 12px rgba(0, 0, 0, 0.1)",cursor:"pointer"},onClick:()=>o.push("/qHarmonyBot"),children:[t.jsx(h.default,{src:y.Z,alt:"AI Chat",style:{borderRadius:"100%",width:"40px",height:"40px",marginRight:"10px"}}),(0,t.jsxs)(n.Z,{children:[t.jsx(s.Z,{variant:"body1",sx:{fontWeight:"bold"},children:"Chat with Brea"}),t.jsx(s.Z,{variant:"body2",sx:{color:"#6c757d"},children:"24/7 available"})]})]}),(0,t.jsxs)(n.Z,{sx:{position:"relative",marginTop:"auto",paddingBottom:"20px",display:"flex",flexDirection:"column",alignItems:"center",gap:"10px"},children:[k&&(0,t.jsxs)(t.Fragment,{children:[t.jsx(s.Z,{sx:{fontWeight:700,paddingY:1,fontSize:".7rem",color:"rgba(0, 0, 0, 0.4)"},children:"ADMIN HUB"}),t.jsx(p.Z,{variant:"contained",onClick:()=>o.push("/manageBenefits/"),sx:{textTransform:"none",borderRadius:"8px",bgcolor:"rgba(0, 0, 0, 0.06)",color:"black",boxShadow:"none",width:"90%",paddingY:"10px","&:hover":{backgroundColor:"rgba(0, 0, 0, 0.1)",boxShadow:"none"}},children:"Upload Benefits"}),t.jsx(p.Z,{variant:"contained",onClick:()=>o.push("/manage-groups/"),sx:{textTransform:"none",borderRadius:"8px",bgcolor:"rgba(0, 0, 0, 0.06)",color:"black",boxShadow:"none",width:"90%",paddingY:"10px","&:hover":{backgroundColor:"rgba(0, 0, 0, 0.1)",boxShadow:"none"}},children:"Assign Benefits"}),t.jsx(p.Z,{variant:"contained",onClick:()=>o.push("/team-members/"),sx:{textTransform:"none",borderRadius:"8px",bgcolor:"rgba(0, 0, 0, 0.06)",color:"black",boxShadow:"none",width:"90%",paddingY:"10px","&:hover":{backgroundColor:"rgba(0, 0, 0, 0.1)",boxShadow:"none"}},children:"Manage Team"}),w.BO.includes(z)&&t.jsx(p.Z,{variant:"contained",onClick:()=>o.push("/proactive-messaging/"),sx:{textTransform:"none",borderRadius:"8px",bgcolor:"rgba(0, 0, 0, 0.06)",color:"black",boxShadow:"none",width:"90%",paddingY:"10px","&:hover":{backgroundColor:"rgba(0, 0, 0, 0.1)",boxShadow:"none"}},children:"Broadcast Center"})]}),I&&t.jsx(t.Fragment,{children:t.jsx(p.Z,{variant:"contained",onClick:()=>o.push("/manage-companies/"),sx:{textTransform:"none",borderRadius:"8px",bgcolor:"rgba(0, 0, 0, 0.06)",color:"black",boxShadow:"none",width:"90%",paddingY:"10px","&:hover":{backgroundColor:"rgba(0, 0, 0, 0.1)",boxShadow:"none"}},children:"Add Employers"})}),!D&&(0,t.jsxs)(n.Z,{sx:{display:"flex",flexDirection:"column",gap:1.5,alignItems:"center",width:"90%"},children:[(0,t.jsxs)(p.Z,{variant:"outlined",sx:{display:"flex",justifyContent:"center",width:"100%",borderRadius:"12px",padding:"9px 16px",border:"1px solid rgba(0, 0, 0, 0.12)",color:"black",fontSize:"16px",fontWeight:500,textTransform:"none","&:hover":{backgroundColor:"rgba(0, 0, 0, 0.04)"}},children:[t.jsx(h.default,{src:C,alt:"Teams Logo",width:21,height:21,style:{marginRight:"8px"}}),"Add to Slack"]}),(0,t.jsxs)(p.Z,{variant:"outlined",sx:{display:"flex",justifyContent:"center",width:"100%",borderRadius:"12px",padding:"9px 16px",border:"1px solid rgba(0, 0, 0, 0.12)",color:"black",fontSize:"16px",fontWeight:500,textTransform:"none","&:hover":{backgroundColor:"rgba(0, 0, 0, 0.04)"}},children:[t.jsx(h.default,{src:S,alt:"Teams Logo",width:21,height:21,style:{marginRight:"8px"}}),"Add to Teams"]})]})]})]})})]})};var I=r(33198),B=r(82400),R=r(22758),T=r(88563);let W=()=>{let{logout:e}=(0,R.a)(),o=(0,f.C)(e=>e.user.userProfile),[r,i]=(0,b.useState)(!1);(0,b.useEffect)(()=>{i("true"===localStorage.getItem("isTeamsApp1"))},[]);let a=(0,v.v9)(e=>e.user.userProfile.isAdmin),l=(0,v.v9)(e=>e.user.userProfile.isBroker);return t.jsx(n.Z,{sx:{bgcolor:"white",padding:2,textAlign:"center",height:"80px",borderBottom:"1px solid #D2D2D2"},children:(0,t.jsxs)(n.Z,{sx:{display:"flex",alignItems:"center",justifyContent:"space-between"},children:[(0,t.jsxs)(n.Z,{sx:{display:"flex",alignItems:"center"},children:[t.jsx(I.Z,{sx:{bgcolor:"black",color:"#ffffff",width:35,height:35,fontSize:"1.2rem",mr:1.5,ml:2,display:"flex",alignItems:"center",justifyContent:"center",paddingBottom:"1px",fontWeight:800},children:(e=>{if(!e)return"";let[o,r]=e.split(" ");return`${o[0].toUpperCase()}${r?r[0].toUpperCase():""}`})(o.name)}),t.jsx(s.Z,{variant:"h4",sx:{mb:0,fontWeight:"bold",display:"flex",alignItems:"center",justifyContent:"flex-start",textAlign:"flex-start",fontSize:"16px",mr:1.5},children:o.name.replace(/\b\w/g,e=>e.toUpperCase())}),a&&t.jsx(n.Z,{sx:{bgcolor:"rgba(0, 0, 0, 0.06)",borderRadius:"8px",padding:"4px 8px",display:"flex",alignItems:"center",justifyContent:"center",fontWeight:"bold",fontSize:"12px",color:"#333"},children:"ADMIN"}),l&&t.jsx(n.Z,{sx:{bgcolor:"#f5f5f5",borderRadius:"8px",padding:"2px 8px",display:"flex",alignItems:"center",justifyContent:"center",fontWeight:"bold",fontSize:"0.9rem",color:"#333"},children:"BROKER"})]}),(0,t.jsxs)(n.Z,{sx:{display:"flex",alignItems:"center"},children:[!r&&(0,t.jsxs)(p.Z,{sx:{backgroundColor:"transparent",color:"#333",textTransform:"none",padding:"8px 8px",display:"flex",alignItems:"center",justifyContent:"flex-start",gap:"2px","&:hover":{backgroundColor:"transparent",color:"#555"},boxShadow:"none"},children:[t.jsx(n.Z,{sx:{mt:.5,mr:.5},children:t.jsx(T.Z,{})}),t.jsx(s.Z,{sx:{fontWeight:500,fontSize:"14px"},children:"Guide"})]}),!r&&(0,t.jsxs)(p.Z,{onClick:e,sx:{backgroundColor:"transparent",color:"#333",marginLeft:1,textTransform:"none",padding:"8px 16px",display:"flex",alignItems:"center",justifyContent:"flex-start",gap:"8px","&:hover":{backgroundColor:"transparent",color:"#555"},boxShadow:"none"},children:[t.jsx(B.Z,{sx:{fontSize:"18px"}}),t.jsx(s.Z,{sx:{fontWeight:500,fontSize:"14px"},children:"Logout"})]})]})]})})},D=e=>{let o=o=>(0,t.jsxs)(n.Z,{sx:{display:"flex",flexGrow:1},children:[t.jsx(k,{}),(0,t.jsxs)(n.Z,{component:"main",sx:{flexGrow:1,width:"calc(100% - 240px)"},children:[t.jsx(W,{}),t.jsx(e,{...o})]})]});return o.displayName=`WithSidebar(${e.displayName||e.name||"Component"})`,o}}};