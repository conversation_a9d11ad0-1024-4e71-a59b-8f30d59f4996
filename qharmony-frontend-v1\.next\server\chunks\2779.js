"use strict";exports.id=2779,exports.ids=[2779],exports.modules={72779:(e,t,s)=>{s.d(t,{Z:()=>z});var a=s(10326),l=s(17577),i=s(25842),n=s(6283),r=s(69995),d=s(98117),o=s(48260),x=s(28591),h=s(87721),u=s(45112),c=s(16027),m=s(90541),g=s(53913),p=s(918),j=s(79266),f=s(37841),v=s(25609),Z=s(99207),b=s(42265),S=s(34039),C=s(4766),y=s(85560),P=s(8387),W=s(98139),N=s(10163),D=s(36690),A=s(75887),k=s(3423),w=s(67840),O=s(96889),I=s(94638);function T(e){let{children:t,value:s,index:l,...i}=e;return a.jsx("div",{role:"tabpanel",hidden:s!==l,id:`profile-tabpanel-${l}`,"aria-labelledby":`profile-tab-${l}`,...i,children:s===l&&a.jsx(n.Z,{sx:{p:3},children:t})})}let z=({open:e,onClose:t})=>{let s=(0,i.I0)(),z=(0,i.v9)(e=>e.user._id),B=(0,i.v9)(e=>e.user.userProfile),[_,X]=(0,l.useState)(0),[E,F]=(0,l.useState)(""),[$,R]=(0,l.useState)(""),[L,q]=(0,l.useState)(""),[U,H]=(0,l.useState)(""),[M,G]=(0,l.useState)(""),[J,Y]=(0,l.useState)(""),[K,Q]=(0,l.useState)(""),[V,ee]=(0,l.useState)(""),[et,es]=(0,l.useState)(""),[ea,el]=(0,l.useState)(""),[ei,en]=(0,l.useState)(""),[er,ed]=(0,l.useState)(""),[eo,ex]=(0,l.useState)(""),[eh,eu]=(0,l.useState)(""),[ec,em]=(0,l.useState)(""),[eg,ep]=(0,l.useState)(""),[ej,ef]=(0,l.useState)(""),[ev,eZ]=(0,l.useState)(""),[eb,eS]=(0,l.useState)("US"),[eC,ey]=(0,l.useState)(""),[eP,eW]=(0,l.useState)(""),[eN,eD]=(0,l.useState)(""),[eA,ek]=(0,l.useState)(""),[ew,eO]=(0,l.useState)(""),[eI,eT]=(0,l.useState)("US"),[ez,eB]=(0,l.useState)(""),[e_,eX]=(0,l.useState)(""),[eE,eF]=(0,l.useState)(""),[e$,eR]=(0,l.useState)(""),[eL,eq]=(0,l.useState)(""),[eU,eH]=(0,l.useState)([]),[eM,eG]=(0,l.useState)(!1),[eJ,eY]=(0,l.useState)(null),[eK,eQ]=(0,l.useState)({firstName:"",middleName:"",lastName:"",gender:"",dateOfBirth:"",relationship:"",ssn:"",isStudent:!1,isDisabled:!1,isActive:!0}),[eV,e0]=(0,l.useState)(""),[e2,e1]=(0,l.useState)(!1),e3=e=>{if(eY(e),!e.firstName&&e.name){let t=e.name.split(" "),s=t[0]||"",a=t.length>1?t[t.length-1]:"",l=t.length>2?t.slice(1,-1).join(" "):"";eQ({...e,firstName:s,middleName:l,lastName:a})}else eQ({...e});eG(!0)},e6=e=>{window.confirm("Are you sure you want to remove this dependent?")&&eH(t=>t.filter(t=>t._id!==e))};(0,l.useEffect)(()=>{if(B){let e=B.name.split(" ");if(F(e[0]||""),R(e.slice(1).join(" ")||""),q(B.email||""),H(B.details?.phoneNumber||""),G(B.details?.department||""),Y(B.details?.title||""),Q(B.details?.dateOfBirth?new Date(B.details.dateOfBirth).toISOString().split("T")[0]:""),ee(B.details?.hireDate?new Date(B.details.hireDate).toISOString().split("T")[0]:""),es(B.details?.annualSalary?.toString()||""),el(B.details?.employeeClassType||""),en(B.details?.workSchedule||""),ed(B.details?.ssn||""),ex(B.details?.employeeId||""),eu(B.details?.address?.street1||""),em(B.details?.address?.street2||""),ep(B.details?.address?.city||""),ef(B.details?.address?.state||""),eZ(B.details?.address?.zipCode||""),eS(B.details?.address?.country||"US"),ey(B.details?.mailingAddress?.street1||""),eW(B.details?.mailingAddress?.street2||""),eD(B.details?.mailingAddress?.city||""),ek(B.details?.mailingAddress?.state||""),eO(B.details?.mailingAddress?.zipCode||""),eT(B.details?.mailingAddress?.country||"US"),eB(B.details?.emergencyContact?.name||""),eX(B.details?.emergencyContact?.relationship||""),eF(B.details?.emergencyContact?.phoneNumber||""),eR(B.details?.emergencyContact?.email||""),eq(B.details?.workLocation||""),B.details?.dependents&&Array.isArray(B.details.dependents)){let e=B.details.dependents.map((e,t)=>{let s=(e.name||"").split(" "),a=s[0]||"",l=s.length>1?s[s.length-1]:"",i=s.length>2?s.slice(1,-1).join(" "):"";return{_id:e._id||`existing_${t}_${Date.now()}`,firstName:a,middleName:i,lastName:l,gender:e.gender||"",dateOfBirth:e.dateOfBirth?new Date(e.dateOfBirth).toISOString().split("T")[0]:"",relationship:e.relationship||"",ssn:e.ssn||"",isStudent:e.isStudent||!1,isDisabled:e.isDisabled||!1,isActive:!1!==e.isActive}});console.log("Loading existing dependents:",e),eH(e)}else console.log("No existing dependents found or dependents is not an array"),eH([])}},[B]);let e5=async()=>{e1(!0);try{let e={name:`${E} ${$}`.trim(),email:L,phoneNumber:U||"",department:M||"",title:J||""};K&&(e.dateOfBirth=new Date(K).toISOString()),V&&(e.hireDate=new Date(V).toISOString()),et&&!isNaN(parseFloat(et))&&(e.annualSalary=parseFloat(et)),ea&&(e.employeeClassType=ea),ei&&(e.workSchedule=ei),er&&(e.ssn=er),eo&&(e.employeeId=eo),eL&&(e.workLocation=eL),eh&&eg&&ej&&ev&&(e.address={street1:eh,city:eg,state:ej,zipCode:ev,country:eb||"US"},ec&&(e.address.street2=ec)),eC&&eN&&eA&&ew&&(e.mailingAddress={street1:eC,city:eN,state:eA,zipCode:ew,country:eI||"US"},eP&&(e.mailingAddress.street2=eP)),ez&&(e.emergencyContact={name:ez},e_&&(e.emergencyContact.relationship=e_),eE&&(e.emergencyContact.phoneNumber=eE),e$&&(e.emergencyContact.email=e$)),e.dependents=eU.map(e=>({_id:e._id&&!e._id.toString().startsWith("temp_")?e._id:void 0,name:`${e.firstName} ${e.middleName?e.middleName+" ":""}${e.lastName}`.trim(),gender:e.gender,dateOfBirth:new Date(e.dateOfBirth).toISOString(),relationship:e.relationship,ssn:e.ssn||void 0,isStudent:e.isStudent||!1,isDisabled:e.isDisabled||!1,isActive:!1!==e.isActive})),console.log("Dependents being sent:",e.dependents),console.log("Current dependents state:",eU),console.log("Sending update request with data:",e),console.log("Dependents in state before sending:",eU),console.log("Formatted dependents for backend:",e.dependents);let a=await (0,I.Nq)(s,z,e);if(a&&200===a.status)await (0,I.M_)(s,z),e0("Profile updated successfully!"),setTimeout(()=>{e0(""),t()},1500);else throw Error(a?.data?.error||"Update failed")}catch(t){console.error("Profile update error:",t),console.error("Error response:",t.response?.data),console.error("Error status:",t.response?.status);let e="Error updating profile. Please try again.";t.response?.data?.message?e=t.response.data.message:t.response?.status===400?e="Invalid data format. Please check all required fields.":t.response?.status===403&&(e="You don't have permission to edit this profile."),e0(e),setTimeout(()=>{e0("")},3e3)}finally{e1(!1)}};return(0,a.jsxs)(r.Z,{open:e,onClose:t,maxWidth:"md",fullWidth:!0,PaperProps:{style:{borderRadius:"16px",boxShadow:"0px 10px 30px rgba(0, 0, 0, 0.1)",padding:"5px",maxHeight:"90vh"}},children:[(0,a.jsxs)(d.Z,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",fontWeight:"bold",fontSize:"1.5rem",pb:1},children:["Edit Profile",a.jsx(o.Z,{onClick:t,children:a.jsx(D.Z,{})})]}),(0,a.jsxs)(x.Z,{sx:{p:0},children:[a.jsx(n.Z,{sx:{borderBottom:1,borderColor:"divider"},children:(0,a.jsxs)(h.Z,{value:_,onChange:(e,t)=>{X(t)},"aria-label":"profile tabs",children:[a.jsx(u.Z,{label:"Basic Info"}),a.jsx(u.Z,{label:"Employment"}),a.jsx(u.Z,{label:"Address"}),a.jsx(u.Z,{label:"Emergency Contact"}),a.jsx(u.Z,{label:"Dependents"})]})}),a.jsx(T,{value:_,index:0,children:(0,a.jsxs)(c.ZP,{container:!0,spacing:3,children:[a.jsx(c.ZP,{item:!0,xs:12,sm:6,children:a.jsx(m.Z,{fullWidth:!0,label:"First Name",value:E,onChange:e=>F(e.target.value),variant:"outlined",sx:{mb:2}})}),a.jsx(c.ZP,{item:!0,xs:12,sm:6,children:a.jsx(m.Z,{fullWidth:!0,label:"Last Name",value:$,onChange:e=>R(e.target.value),variant:"outlined",sx:{mb:2}})}),a.jsx(c.ZP,{item:!0,xs:12,children:a.jsx(m.Z,{fullWidth:!0,label:"Email",value:L,onChange:e=>q(e.target.value),variant:"outlined",sx:{mb:2}})}),a.jsx(c.ZP,{item:!0,xs:12,sm:6,children:a.jsx(m.Z,{fullWidth:!0,label:"Phone Number",value:U,onChange:e=>H(e.target.value),variant:"outlined",sx:{mb:2}})}),a.jsx(c.ZP,{item:!0,xs:12,sm:6,children:a.jsx(m.Z,{fullWidth:!0,label:"Department",value:M,onChange:e=>G(e.target.value),variant:"outlined",sx:{mb:2}})}),a.jsx(c.ZP,{item:!0,xs:12,children:a.jsx(m.Z,{fullWidth:!0,label:"Job Title",value:J,onChange:e=>Y(e.target.value),variant:"outlined",sx:{mb:2}})}),a.jsx(c.ZP,{item:!0,xs:12,sm:6,children:a.jsx(m.Z,{fullWidth:!0,label:"Date of Birth *",type:"date",value:K,onChange:e=>Q(e.target.value),variant:"outlined",InputLabelProps:{shrink:!0},sx:{mb:2},required:!0})}),a.jsx(c.ZP,{item:!0,xs:12,sm:6,children:a.jsx(m.Z,{fullWidth:!0,label:"SSN",value:er,onChange:e=>ed(e.target.value),variant:"outlined",placeholder:"XXX-XX-XXXX",sx:{mb:2}})})]})}),a.jsx(T,{value:_,index:1,children:(0,a.jsxs)(c.ZP,{container:!0,spacing:3,children:[a.jsx(c.ZP,{item:!0,xs:12,sm:6,children:a.jsx(m.Z,{fullWidth:!0,label:"Employee ID",value:eo,onChange:e=>ex(e.target.value),variant:"outlined",sx:{mb:2}})}),a.jsx(c.ZP,{item:!0,xs:12,sm:6,children:a.jsx(m.Z,{fullWidth:!0,label:"Hire Date *",type:"date",value:V,onChange:e=>ee(e.target.value),variant:"outlined",InputLabelProps:{shrink:!0},sx:{mb:2},required:!0})}),a.jsx(c.ZP,{item:!0,xs:12,sm:6,children:a.jsx(m.Z,{fullWidth:!0,label:"Annual Salary",type:"number",value:et,onChange:e=>es(e.target.value),variant:"outlined",sx:{mb:2}})}),a.jsx(c.ZP,{item:!0,xs:12,sm:6,children:(0,a.jsxs)(g.Z,{fullWidth:!0,sx:{mb:2},required:!0,children:[a.jsx(p.Z,{children:"Employee Class Type *"}),(0,a.jsxs)(j.Z,{value:ea,onChange:e=>el(e.target.value),label:"Employee Class Type *",required:!0,children:[a.jsx(f.Z,{value:"Full-Time",children:"Full-Time"}),a.jsx(f.Z,{value:"Part-Time",children:"Part-Time"}),a.jsx(f.Z,{value:"Contractor",children:"Contractor"}),a.jsx(f.Z,{value:"Temporary",children:"Temporary"})]})]})}),a.jsx(c.ZP,{item:!0,xs:12,sm:6,children:(0,a.jsxs)(g.Z,{fullWidth:!0,sx:{mb:2},children:[a.jsx(p.Z,{children:"Work Schedule"}),(0,a.jsxs)(j.Z,{value:ei,onChange:e=>en(e.target.value),label:"Work Schedule",children:[a.jsx(f.Z,{value:"Full-Time",children:"Full-Time"}),a.jsx(f.Z,{value:"Part-Time",children:"Part-Time"}),a.jsx(f.Z,{value:"Remote",children:"Remote"}),a.jsx(f.Z,{value:"Hybrid",children:"Hybrid"})]})]})}),a.jsx(c.ZP,{item:!0,xs:12,sm:6,children:a.jsx(m.Z,{fullWidth:!0,label:"Work Location",value:eL,onChange:e=>eq(e.target.value),variant:"outlined",sx:{mb:2}})})]})}),(0,a.jsxs)(T,{value:_,index:2,children:[a.jsx(v.Z,{variant:"h6",sx:{mb:2,fontWeight:"bold"},children:"Primary Address"}),(0,a.jsxs)(c.ZP,{container:!0,spacing:3,children:[a.jsx(c.ZP,{item:!0,xs:12,children:a.jsx(m.Z,{fullWidth:!0,label:"Street Address",value:eh,onChange:e=>eu(e.target.value),variant:"outlined",sx:{mb:2}})}),a.jsx(c.ZP,{item:!0,xs:12,children:a.jsx(m.Z,{fullWidth:!0,label:"Apartment, Suite, Unit (Optional)",value:ec,onChange:e=>em(e.target.value),variant:"outlined",sx:{mb:2}})}),a.jsx(c.ZP,{item:!0,xs:12,sm:6,children:a.jsx(m.Z,{fullWidth:!0,label:"City",value:eg,onChange:e=>ep(e.target.value),variant:"outlined",sx:{mb:2}})}),a.jsx(c.ZP,{item:!0,xs:12,sm:3,children:a.jsx(m.Z,{fullWidth:!0,label:"State",value:ej,onChange:e=>ef(e.target.value),variant:"outlined",sx:{mb:2}})}),a.jsx(c.ZP,{item:!0,xs:12,sm:3,children:a.jsx(m.Z,{fullWidth:!0,label:"ZIP Code",value:ev,onChange:e=>eZ(e.target.value),variant:"outlined",sx:{mb:2}})})]}),a.jsx(Z.Z,{sx:{my:3}}),a.jsx(v.Z,{variant:"h6",sx:{mb:2,fontWeight:"bold"},children:"Mailing Address (if different)"}),(0,a.jsxs)(c.ZP,{container:!0,spacing:3,children:[a.jsx(c.ZP,{item:!0,xs:12,children:a.jsx(m.Z,{fullWidth:!0,label:"Mailing Street Address",value:eC,onChange:e=>ey(e.target.value),variant:"outlined",sx:{mb:2}})}),a.jsx(c.ZP,{item:!0,xs:12,children:a.jsx(m.Z,{fullWidth:!0,label:"Apartment, Suite, Unit (Optional)",value:eP,onChange:e=>eW(e.target.value),variant:"outlined",sx:{mb:2}})}),a.jsx(c.ZP,{item:!0,xs:12,sm:6,children:a.jsx(m.Z,{fullWidth:!0,label:"City",value:eN,onChange:e=>eD(e.target.value),variant:"outlined",sx:{mb:2}})}),a.jsx(c.ZP,{item:!0,xs:12,sm:3,children:a.jsx(m.Z,{fullWidth:!0,label:"State",value:eA,onChange:e=>ek(e.target.value),variant:"outlined",sx:{mb:2}})}),a.jsx(c.ZP,{item:!0,xs:12,sm:3,children:a.jsx(m.Z,{fullWidth:!0,label:"ZIP Code",value:ew,onChange:e=>eO(e.target.value),variant:"outlined",sx:{mb:2}})})]})]}),a.jsx(T,{value:_,index:3,children:(0,a.jsxs)(c.ZP,{container:!0,spacing:3,children:[a.jsx(c.ZP,{item:!0,xs:12,sm:6,children:a.jsx(m.Z,{fullWidth:!0,label:"Emergency Contact Name",value:ez,onChange:e=>eB(e.target.value),variant:"outlined",sx:{mb:2}})}),a.jsx(c.ZP,{item:!0,xs:12,sm:6,children:(0,a.jsxs)(g.Z,{fullWidth:!0,sx:{mb:2},children:[a.jsx(p.Z,{children:"Relationship"}),(0,a.jsxs)(j.Z,{value:e_,onChange:e=>eX(e.target.value),label:"Relationship",children:[a.jsx(f.Z,{value:"Spouse",children:"Spouse"}),a.jsx(f.Z,{value:"Parent",children:"Parent"}),a.jsx(f.Z,{value:"Child",children:"Child"}),a.jsx(f.Z,{value:"Sibling",children:"Sibling"}),a.jsx(f.Z,{value:"Friend",children:"Friend"}),a.jsx(f.Z,{value:"Other",children:"Other"})]})]})}),a.jsx(c.ZP,{item:!0,xs:12,sm:6,children:a.jsx(m.Z,{fullWidth:!0,label:"Emergency Contact Phone",value:eE,onChange:e=>eF(e.target.value),variant:"outlined",sx:{mb:2}})}),a.jsx(c.ZP,{item:!0,xs:12,sm:6,children:a.jsx(m.Z,{fullWidth:!0,label:"Emergency Contact Email",value:e$,onChange:e=>eR(e.target.value),variant:"outlined",sx:{mb:2}})})]})}),(0,a.jsxs)(T,{value:_,index:4,children:[(0,a.jsxs)(n.Z,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",mb:3},children:[a.jsx(v.Z,{variant:"h6",sx:{fontWeight:"bold"},children:"Dependents"}),a.jsx(b.Z,{variant:"contained",startIcon:a.jsx(k.Z,{}),onClick:()=>{eY(null),eQ({firstName:"",middleName:"",lastName:"",gender:"",dateOfBirth:"",relationship:"",ssn:"",isStudent:!1,isDisabled:!1,isActive:!0}),eG(!0)},sx:{backgroundColor:"#000000",color:"#ffffff",borderRadius:"8px",textTransform:"none","&:hover":{backgroundColor:"#333333"}},children:"Add Dependent"})]}),(0,a.jsxs)(n.Z,{sx:{mb:3},children:[eU.map(e=>a.jsx(S.Z,{sx:{border:"1px solid #e0e0e0",borderRadius:"8px",mb:1.5,"&:hover":{boxShadow:"0 2px 8px rgba(0,0,0,0.1)",transition:"box-shadow 0.2s ease-in-out"}},children:a.jsx(C.Z,{sx:{p:2,"&:last-child":{pb:2}},children:(0,a.jsxs)(n.Z,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center"},children:[(0,a.jsxs)(n.Z,{sx:{display:"flex",alignItems:"center",flex:1},children:[(0,a.jsxs)(n.Z,{sx:{mr:4},children:[a.jsx(v.Z,{variant:"h6",sx:{fontWeight:"bold",mb:.25,fontSize:"1.1rem"},children:`${e.firstName} ${e.middleName?e.middleName+" ":""}${e.lastName}`.trim()}),a.jsx(v.Z,{variant:"body2",color:"text.secondary",sx:{fontSize:"0.875rem"},children:e.relationship})]}),(0,a.jsxs)(n.Z,{sx:{display:"flex",gap:3,alignItems:"center",flex:1},children:[(0,a.jsxs)(n.Z,{children:[a.jsx(v.Z,{variant:"caption",color:"text.secondary",sx:{display:"block",fontSize:"0.75rem",lineHeight:1.2},children:"Date of Birth"}),a.jsx(v.Z,{variant:"body2",sx:{fontWeight:500,fontSize:"0.875rem",mt:.25},children:new Date(e.dateOfBirth).toLocaleDateString()})]}),(0,a.jsxs)(n.Z,{children:[a.jsx(v.Z,{variant:"caption",color:"text.secondary",sx:{display:"block",fontSize:"0.75rem",lineHeight:1.2},children:"Gender"}),a.jsx(v.Z,{variant:"body2",sx:{fontWeight:500,fontSize:"0.875rem",mt:.25},children:e.gender})]}),(0,a.jsxs)(n.Z,{children:[a.jsx(v.Z,{variant:"caption",color:"text.secondary",sx:{display:"block",fontSize:"0.75rem",lineHeight:1.2},children:"Age"}),(0,a.jsxs)(v.Z,{variant:"body2",sx:{fontWeight:500,fontSize:"0.875rem",mt:.25},children:[Math.floor((new Date().getTime()-new Date(e.dateOfBirth).getTime())/31536e6)," years"]})]})]})]}),(0,a.jsxs)(n.Z,{sx:{display:"flex",alignItems:"center",gap:1.5},children:[(0,a.jsxs)(n.Z,{sx:{display:"flex",gap:.5},children:[e.isStudent&&a.jsx(y.Z,{label:"Student",size:"small",color:"primary",variant:"outlined",sx:{height:"24px",fontSize:"0.75rem"}}),e.isDisabled&&a.jsx(y.Z,{label:"Disabled",size:"small",color:"secondary",variant:"outlined",sx:{height:"24px",fontSize:"0.75rem"}}),a.jsx(y.Z,{label:e.isActive?"Active":"Inactive",size:"small",color:e.isActive?"success":"default",variant:e.isActive?"filled":"outlined",sx:{height:"24px",fontSize:"0.75rem"}})]}),(0,a.jsxs)(n.Z,{sx:{display:"flex",gap:.5},children:[a.jsx(o.Z,{size:"small",onClick:()=>e3(e),sx:{backgroundColor:"#f5f5f5",width:"32px",height:"32px","&:hover":{backgroundColor:"#e0e0e0"}},children:a.jsx(w.Z,{sx:{fontSize:"16px"}})}),a.jsx(o.Z,{size:"small",onClick:()=>e6(e._id),sx:{backgroundColor:"#ffebee",color:"#d32f2f",width:"32px",height:"32px","&:hover":{backgroundColor:"#ffcdd2"}},children:a.jsx(O.Z,{sx:{fontSize:"16px"}})})]})]})]})})},e._id)),0===eU.length&&(0,a.jsxs)(n.Z,{sx:{textAlign:"center",py:6,border:"2px dashed #e0e0e0",borderRadius:"12px",backgroundColor:"#fafafa"},children:[a.jsx(v.Z,{variant:"h6",color:"text.secondary",sx:{mb:1},children:"No dependents added yet"}),a.jsx(v.Z,{variant:"body2",color:"text.secondary",children:'Click "Add Dependent" to get started adding family members to your profile.'})]})]}),eM&&a.jsx(n.Z,{sx:{position:"fixed",top:0,left:0,right:0,bottom:0,backgroundColor:"rgba(0, 0, 0, 0.5)",display:"flex",alignItems:"center",justifyContent:"center",zIndex:9999},children:(0,a.jsxs)(S.Z,{sx:{width:"90%",maxWidth:"600px",maxHeight:"90vh",overflow:"auto"},children:[(0,a.jsxs)(C.Z,{children:[a.jsx(v.Z,{variant:"h6",sx:{mb:3,fontWeight:"bold"},children:eJ?"Edit Dependent":"Add New Dependent"}),(0,a.jsxs)(c.ZP,{container:!0,spacing:2,children:[a.jsx(c.ZP,{item:!0,xs:12,sm:4,children:a.jsx(m.Z,{fullWidth:!0,label:"First Name *",value:eK.firstName,onChange:e=>eQ(t=>({...t,firstName:e.target.value})),variant:"outlined",required:!0})}),a.jsx(c.ZP,{item:!0,xs:12,sm:4,children:a.jsx(m.Z,{fullWidth:!0,label:"Middle Name (Optional)",value:eK.middleName,onChange:e=>eQ(t=>({...t,middleName:e.target.value})),variant:"outlined"})}),a.jsx(c.ZP,{item:!0,xs:12,sm:4,children:a.jsx(m.Z,{fullWidth:!0,label:"Last Name *",value:eK.lastName,onChange:e=>eQ(t=>({...t,lastName:e.target.value})),variant:"outlined",required:!0})}),a.jsx(c.ZP,{item:!0,xs:12,sm:6,children:(0,a.jsxs)(g.Z,{fullWidth:!0,children:[a.jsx(p.Z,{children:"Gender *"}),(0,a.jsxs)(j.Z,{value:eK.gender,onChange:e=>eQ(t=>({...t,gender:e.target.value})),label:"Gender *",children:[a.jsx(f.Z,{value:"Male",children:"Male"}),a.jsx(f.Z,{value:"Female",children:"Female"}),a.jsx(f.Z,{value:"Other",children:"Other"}),a.jsx(f.Z,{value:"Prefer not to say",children:"Prefer not to say"})]})]})}),a.jsx(c.ZP,{item:!0,xs:12,sm:6,children:a.jsx(m.Z,{fullWidth:!0,label:"Date of Birth *",type:"date",value:eK.dateOfBirth,onChange:e=>eQ(t=>({...t,dateOfBirth:e.target.value})),variant:"outlined",InputLabelProps:{shrink:!0}})}),a.jsx(c.ZP,{item:!0,xs:12,children:(0,a.jsxs)(g.Z,{fullWidth:!0,children:[a.jsx(p.Z,{children:"Relationship *"}),(0,a.jsxs)(j.Z,{value:eK.relationship,onChange:e=>eQ(t=>({...t,relationship:e.target.value})),label:"Relationship *",children:[a.jsx(f.Z,{value:"Spouse",children:"Spouse"}),a.jsx(f.Z,{value:"Child",children:"Child"}),a.jsx(f.Z,{value:"Domestic Partner",children:"Domestic Partner"}),a.jsx(f.Z,{value:"Stepchild",children:"Stepchild"}),a.jsx(f.Z,{value:"Adopted Child",children:"Adopted Child"}),a.jsx(f.Z,{value:"Other",children:"Other"})]})]})}),a.jsx(c.ZP,{item:!0,xs:12,children:a.jsx(m.Z,{fullWidth:!0,label:"SSN (Optional)",value:eK.ssn,onChange:e=>eQ(t=>({...t,ssn:e.target.value})),variant:"outlined",placeholder:"XXX-XX-XXXX"})})]})]}),(0,a.jsxs)(P.Z,{sx:{justifyContent:"flex-end",p:2},children:[a.jsx(b.Z,{onClick:()=>{eG(!1),eY(null)},sx:{color:"#666",textTransform:"none"},children:"Cancel"}),(0,a.jsxs)(b.Z,{onClick:()=>{if(!eK.firstName||!eK.lastName||!eK.dateOfBirth||!eK.relationship){alert("Please fill in all required fields (First Name, Last Name, Date of Birth, Relationship)");return}if(eJ)eH(e=>e.map(e=>e._id===eJ._id?{...eK}:e));else{let e={...eK,_id:`temp_${Date.now()}_${Math.random().toString(36).substr(2,9)}`};eH(t=>[...t,e])}eG(!1),eY(null)},variant:"contained",sx:{backgroundColor:"#000000",color:"#ffffff",textTransform:"none","&:hover":{backgroundColor:"#333333"}},children:[eJ?"Update":"Add"," Dependent"]})]})]})})]}),e2&&a.jsx(n.Z,{sx:{display:"flex",justifyContent:"center",p:2},children:a.jsx(W.Z,{})}),eV&&(0,a.jsxs)(n.Z,{sx:{color:"green",p:2,display:"flex",alignItems:"center",justifyContent:"center"},children:[a.jsx(A.Z,{sx:{mr:1}}),eV]})]}),(0,a.jsxs)(N.Z,{sx:{padding:"16px"},children:[a.jsx(b.Z,{onClick:t,sx:{color:"#666",borderRadius:"12px",padding:"8px 24px",textTransform:"none",fontWeight:"bold",mr:1},children:"Cancel"}),a.jsx(b.Z,{onClick:e5,sx:{color:"#ffffff",backgroundColor:"#000000",borderRadius:"12px",padding:"8px 24px",textTransform:"none",fontWeight:"bold","&:hover":{backgroundColor:"#333333"}},disabled:e2,children:e2?"Saving...":"Save Changes"})]})]})}}};