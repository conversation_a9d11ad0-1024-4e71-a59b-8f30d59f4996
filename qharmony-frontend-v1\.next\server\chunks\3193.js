"use strict";exports.id=3193,exports.ids=[3193],exports.modules={41598:(e,t,r)=>{r.d(t,{Z:()=>d,f:()=>l});var a=r(71685),o=r(97898);function l(e){return(0,o.ZP)("MuiListItemIcon",e)}let d=(0,a.Z)("MuiListItemIcon",["root","alignItemsFlexStart"])},96103:(e,t,r)=>{r.d(t,{Z:()=>p});var a=r(10326),o=r(17577),l=r(46226),d=r(25842),n=r(59028),s=r(7330),i=r(32049);let p=({isOpen:e,onClose:t})=>{let r=(0,d.I0)(),p=(0,o.useRef)(null),[c,h]=(0,o.useState)(""),x=(0,d.v9)(e=>(0,i.MP)(e)),y=(0,d.v9)(e=>e.user._id),f=(0,d.v9)(e=>e.user.userProfile),g=(0,d.v9)(e=>e.qHarmonyBot.chatHistory),u=(0,d.v9)(e=>e.qHarmonyBot.isLoading),m=e=>{if(""===e.trim())return;let t={sender:"user",message:e.replace(/\n/g,"<br/>"),timestamp:new Date().toISOString()};r((0,n.Hz)(t)),r((0,n.wt)(!0)),(0,s.b)(r,e,y,x),h("")},k=e=>{if(!e)return"";let[t,r]=e.split(" ");return`${t[0].toUpperCase()}${r?r[0].toUpperCase():""}`},b=()=>{p.current?.scrollIntoView({behavior:"smooth"})};(0,o.useEffect)(()=>{e&&0===g.length&&f.name&&(r((0,n.wt)(!0)),setTimeout(()=>{let e={sender:"bot",message:`Hey ${f.name}, how can I help you with your benefits enrollment today?`,timestamp:new Date().toISOString()};r((0,n.Hz)(e)),r((0,n.wt)(!1))},1e3))},[e,g.length,f.name,r]),(0,o.useEffect)(()=>{b()},[g]);let v=["Explain my plan options","Help me choose coverage","What are the costs?","Enrollment deadline"];return e?a.jsx("div",{style:{position:"fixed",top:0,left:0,right:0,bottom:0,backgroundColor:"rgba(0, 0, 0, 0.5)",zIndex:1e4,display:"flex",alignItems:"center",justifyContent:"center",padding:"20px"},children:(0,a.jsxs)("div",{style:{backgroundColor:"#f6f8fc",borderRadius:"12px",width:"100%",maxWidth:"800px",height:"600px",display:"flex",flexDirection:"column",boxShadow:"0 20px 60px rgba(0, 0, 0, 0.3)",overflow:"hidden"},children:[(0,a.jsxs)("div",{style:{backgroundColor:"#ffffff",padding:"16px 24px",borderBottom:"1px solid #e5e7eb",display:"flex",alignItems:"center",justifyContent:"space-between"},children:[(0,a.jsxs)("div",{style:{display:"flex",alignItems:"center",gap:"12px"},children:[a.jsx(l.default,{src:"/brea.png",alt:"Brea",width:40,height:40,style:{borderRadius:"50%"}}),(0,a.jsxs)("div",{children:[a.jsx("h3",{style:{margin:0,fontSize:"18px",fontWeight:"600",color:"#111827"},children:"Chat with Brea"}),a.jsx("p",{style:{margin:0,fontSize:"14px",color:"#6b7280"},children:"Your Benefits Specialist"})]})]}),a.jsx("button",{onClick:t,style:{background:"none",border:"none",fontSize:"24px",cursor:"pointer",color:"#6b7280",padding:"4px",borderRadius:"4px",display:"flex",alignItems:"center",justifyContent:"center"},children:"\xd7"})]}),(0,a.jsxs)("div",{style:{flex:1,overflow:"auto",padding:"16px",display:"flex",flexDirection:"column",gap:"12px"},children:[g.map((e,t)=>(0,a.jsxs)("div",{style:{display:"flex",flexDirection:"user"===e.sender?"row-reverse":"row",alignItems:"flex-start",gap:"8px"},children:[a.jsx("div",{style:{width:"32px",height:"32px",borderRadius:"50%",display:"flex",alignItems:"center",justifyContent:"center",backgroundColor:"user"===e.sender?"#000000":"transparent",color:"white",fontSize:"14px",fontWeight:"600",flexShrink:0},children:"user"===e.sender?k(f.name):a.jsx(l.default,{src:"/brea.png",alt:"Brea",width:32,height:32,style:{borderRadius:"50%"}})}),(0,a.jsxs)("div",{style:{maxWidth:"70%",backgroundColor:"user"===e.sender?"#000000":"#ffffff",color:"user"===e.sender?"#ffffff":"#000000",padding:"12px 16px",borderRadius:"user"===e.sender?"16px 16px 4px 16px":"16px 16px 16px 4px",fontSize:"14px",lineHeight:"1.5",boxShadow:"0 1px 3px rgba(0, 0, 0, 0.1)"},children:[a.jsx("div",{dangerouslySetInnerHTML:{__html:"bot"===e.sender?`${e.message}<br/><small style="color: #6b7280; font-size: 12px;">AI-generated content—verify before use.</small>`:e.message},style:{whiteSpace:"pre-wrap",wordBreak:"break-word"}}),"bot"===e.sender&&e.message.includes("how can I help you")&&t===g.length-1&&a.jsx("div",{style:{display:"flex",flexWrap:"wrap",gap:"8px",marginTop:"12px"},children:v.map(e=>a.jsx("button",{onClick:()=>m(e),style:{padding:"6px 12px",backgroundColor:"#f3f4f6",color:"#374151",border:"1px solid #d1d5db",borderRadius:"16px",fontSize:"12px",cursor:"pointer",transition:"all 0.2s"},onMouseOver:e=>{e.currentTarget.style.backgroundColor="#e5e7eb"},onMouseOut:e=>{e.currentTarget.style.backgroundColor="#f3f4f6"},children:e},e))})]})]},t)),u&&(0,a.jsxs)("div",{style:{display:"flex",alignItems:"flex-start",gap:"8px"},children:[a.jsx(l.default,{src:"/brea.png",alt:"Brea",width:32,height:32,style:{borderRadius:"50%"}}),a.jsx("div",{style:{backgroundColor:"#ffffff",padding:"12px 16px",borderRadius:"16px 16px 16px 4px",fontSize:"14px",color:"#6b7280",boxShadow:"0 1px 3px rgba(0, 0, 0, 0.1)"},children:"Brea is typing..."})]}),a.jsx("div",{ref:p})]}),a.jsx("div",{style:{backgroundColor:"#ffffff",padding:"16px",borderTop:"1px solid #e5e7eb"},children:(0,a.jsxs)("div",{style:{display:"flex",gap:"12px",alignItems:"flex-end"},children:[a.jsx("textarea",{value:c,onChange:e=>h(e.target.value),onKeyPress:e=>{"Enter"!==e.key||e.shiftKey||(e.preventDefault(),m(c))},placeholder:"Type your message...",style:{flex:1,padding:"12px",border:"1px solid #d1d5db",borderRadius:"8px",fontSize:"14px",resize:"none",minHeight:"44px",maxHeight:"120px",outline:"none",fontFamily:"inherit",color:"#000000",backgroundColor:"#ffffff"},rows:1}),a.jsx("button",{onClick:()=>m(c),disabled:!c.trim(),style:{padding:"12px 20px",backgroundColor:c.trim()?"#000000":"#e5e7eb",color:c.trim()?"#ffffff":"#9ca3af",border:"none",borderRadius:"8px",fontSize:"14px",fontWeight:"500",cursor:c.trim()?"pointer":"not-allowed",transition:"all 0.2s"},children:"Send"})]})})]})}):null}},7330:(e,t,r)=>{r.d(t,{b:()=>l});var a=r(59028);let o=(0,r(89009).GU)();async function l(e,t,r,l){let d={user_id:r,user_message:t,team_id:l};try{console.log("Sending chat message:",d);let t=await fetch(`${o}/chat`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(d)});if(!t.body)throw Error("Readable stream not supported");let r=t.body.getReader(),l=new TextDecoder("utf-8"),n={sender:"bot",message:"",timestamp:new Date().toISOString()};for(e((0,a.wt)(!0));;){let{done:t,value:o}=await r.read();if(t)break;let d=l.decode(o,{stream:!0});e((0,a.wt)(!1)),console.log("Chunk:",d),n.message+=d,e((0,a.Hz)({sender:"bot",message:d,timestamp:new Date().toISOString()}))}}catch(r){console.error("Error sending chat message:",r);let t={sender:"bot",message:"Sorry, I encountered an error while processing your request.",timestamp:new Date().toISOString()};e((0,a.Hz)(t)),e((0,a.wt)(!1))}}},9664:(e,t,r)=>{r.d(t,{Z:()=>s});var a=r(17577);let o=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),l=(...e)=>e.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim();var d={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let n=(0,a.forwardRef)(({color:e="currentColor",size:t=24,strokeWidth:r=2,absoluteStrokeWidth:o,className:n="",children:s,iconNode:i,...p},c)=>(0,a.createElement)("svg",{ref:c,...d,width:t,height:t,stroke:e,strokeWidth:o?24*Number(r)/Number(t):r,className:l("lucide",n),...p},[...i.map(([e,t])=>(0,a.createElement)(e,t)),...Array.isArray(s)?s:[s]])),s=(e,t)=>{let r=(0,a.forwardRef)(({className:r,...d},s)=>(0,a.createElement)(n,{ref:s,iconNode:t,className:l(`lucide-${o(e)}`,r),...d}));return r.displayName=`${e}`,r}},9921:(e,t,r)=>{r.d(t,{Z:()=>a});let a=(0,r(9664).Z)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},18751:(e,t,r)=>{r.d(t,{Z:()=>a});let a=(0,r(9664).Z)("ChartColumn",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]])},5857:(e,t,r)=>{r.d(t,{Z:()=>a});let a=(0,r(9664).Z)("CircleCheckBig",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},291:(e,t,r)=>{r.d(t,{Z:()=>a});let a=(0,r(9664).Z)("DollarSign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]])},7506:(e,t,r)=>{r.d(t,{Z:()=>a});let a=(0,r(9664).Z)("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]])},77422:(e,t,r)=>{r.d(t,{Z:()=>a});let a=(0,r(9664).Z)("Eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},43022:(e,t,r)=>{r.d(t,{Z:()=>a});let a=(0,r(9664).Z)("FileText",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},30353:(e,t,r)=>{r.d(t,{Z:()=>a});let a=(0,r(9664).Z)("Heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]])},88090:(e,t,r)=>{r.d(t,{Z:()=>a});let a=(0,r(9664).Z)("House",[["path",{d:"M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8",key:"5wwlr5"}],["path",{d:"M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"1d0kgt"}]])},10036:(e,t,r)=>{r.d(t,{Z:()=>a});let a=(0,r(9664).Z)("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]])},71315:(e,t,r)=>{r.d(t,{Z:()=>a});let a=(0,r(9664).Z)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},42556:(e,t,r)=>{r.d(t,{Z:()=>a});let a=(0,r(9664).Z)("Send",[["path",{d:"M14.536 21.686a.5.5 0 0 0 .937-.024l6.5-19a.496.496 0 0 0-.635-.635l-19 6.5a.5.5 0 0 0-.024.937l7.93 3.18a2 2 0 0 1 1.112 1.11z",key:"1ffxy3"}],["path",{d:"m21.854 2.147-10.94 10.939",key:"12cjpa"}]])},43533:(e,t,r)=>{r.d(t,{Z:()=>a});let a=(0,r(9664).Z)("Shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]])},88114:(e,t,r)=>{r.d(t,{Z:()=>a});let a=(0,r(9664).Z)("TriangleAlert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},26371:(e,t,r)=>{r.d(t,{Z:()=>a});let a=(0,r(9664).Z)("UserCheck",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["polyline",{points:"16 11 18 13 22 9",key:"1pwet4"}]])},28096:(e,t,r)=>{r.d(t,{Z:()=>a});let a=(0,r(9664).Z)("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},31855:(e,t,r)=>{r.d(t,{Z:()=>a});let a=(0,r(9664).Z)("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]])},43020:(e,t,r)=>{r.d(t,{Z:()=>a});let a=(0,r(9664).Z)("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])}};