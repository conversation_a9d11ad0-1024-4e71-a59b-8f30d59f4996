"use strict";exports.id=43,exports.ids=[43],exports.modules={41014:(e,t,a)=>{a.d(t,{Z:()=>P});var o=a(10326),n=a(95148),r=a(9426),i=a(87841),s=a(48260),l=a(19074),d=a(6725),c=a(6283),m=a(42265),p=a(25609),u=a(99207),y=a(84979),f=a(71411),h=a(24003),g=a(78969),x=a(25886),b=a(46226),S=a(31870),I=a(17577),E=a(94638),w=a(32049),j=a(35047),A=a(89223),C=a(25842),k=a(18742);let v=(0,a(51426).Z)((0,o.jsx)("path",{d:"M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z"}),"Home");var N=a(22758),D=a(82400),Z=a(56608);let T="75vw",B=()=>{let e=(0,S.T)(),t=(0,j.useRouter)();(0,j.usePathname)();let{logout:a}=(0,N.a)(),n=(0,S.C)(e=>e.company.companyBenefitTypes);(0,S.C)(e=>e.user.selectedBenefitType);let r=(0,C.v9)(e=>(0,w.MP)(e));(0,I.useEffect)(()=>{r&&(0,E.N)(e,r)},[r,e]);let[i,s]=(0,I.useState)(!1);(0,I.useEffect)(()=>{s("true"===localStorage.getItem("isTeamsApp1"))},[]);let d=a=>{e((0,w.v2)(a)),t.push(`/viewBenefitsByType/${a}`)};return(0,o.jsxs)(l.ZP,{sx:{width:T,height:"100vh",flexShrink:0,"& .MuiDrawer-paper":{width:T,boxSizing:"border-box",bgcolor:"#ffffff",position:"relative"}},variant:"permanent",anchor:"left",children:[(0,o.jsxs)(c.Z,{sx:{padding:0,height:"100%",position:"relative",bgcolor:"#ffffff"},children:[o.jsx(c.Z,{sx:{mx:2,mt:2,px:1,py:.5,borderRadius:2,position:"relative","&:hover":{backgroundColor:"#f0f0f0"},bgcolor:"#F5F6FA"},children:(0,o.jsxs)(m.Z,{variant:"text",sx:{width:"100%",borderRadius:2,bgcolor:"#F5F6FA",color:"#333",fontWeight:"medium",fontSize:"1rem",textTransform:"none","&:hover":{backgroundColor:"#f0f0f0"},display:"flex",alignItems:"center",justifyContent:"flex-start"},onClick:()=>{t.push("/mobile/dashboard"),e((0,Z.dL)())},children:[o.jsx(v,{sx:{mr:1}}),"Home"]})}),o.jsx(p.Z,{sx:{mt:2,fontWeight:500,paddingX:2.5,fontSize:"1.2rem",color:"black"},children:"My Benefits"}),o.jsx(p.Z,{sx:{fontWeight:500,paddingX:2.5,paddingY:1,fontSize:".7rem",color:"rgba(0, 0, 0, 0.4)"},children:"SELECT ANY TO VIEW"}),o.jsx(u.Z,{sx:{my:1}}),o.jsx(y.Z,{children:n.length>0?n.map(t=>o.jsx(f.ZP,{disablePadding:!0,children:(0,o.jsxs)(h.Z,{onClick:()=>{d(t),e((0,Z.dL)())},sx:{borderRadius:2,position:"relative","&:hover":{backgroundColor:"#f0f0f0"},bgcolor:"#F5F6FA",mx:2,mt:2},children:[o.jsx(g.Z,{sx:{minWidth:0,mr:2,pt:.5},children:(0,k.RS)(t)}),o.jsx(x.Z,{primary:t,sx:{fontWeight:"medium",color:"#333",fontSize:"1rem"}})]})},t)):o.jsx(p.Z,{variant:"body1",sx:{color:"#999",padding:2.5},children:"No benefits available at the moment"})})]}),!i&&(0,o.jsxs)(c.Z,{sx:{display:"flex",alignItems:"center",justifyContent:"center",bgcolor:"#F5F6FA",borderRadius:"30px",padding:"10px 20px",cursor:"pointer",position:"absolute",bottom:"50px",left:"50%",transform:"translateX(-50%)",width:"calc(100% - 40px)"},onClick:()=>{t.push("/qHarmonyBot"),e((0,Z.dL)())},children:[o.jsx(b.default,{src:A.Z,alt:"AI Chat",style:{borderRadius:"100%",width:"40px",height:"40px",marginRight:"10px"}}),(0,o.jsxs)(c.Z,{children:[o.jsx(p.Z,{variant:"body1",sx:{fontWeight:"bold"},children:"Chat with Brea"}),o.jsx(p.Z,{variant:"body2",sx:{color:"#6c757d"},children:"24/7 available"})]})]}),(0,o.jsxs)(m.Z,{onClick:a,sx:{backgroundColor:"transparent",color:"#333",marginBottom:"5px",textTransform:"none",padding:"8px 16px",display:"flex",alignItems:"center",justifyContent:"center",gap:"8px","&:hover":{backgroundColor:"transparent",color:"#555"},boxShadow:"none"},children:[o.jsx(D.Z,{sx:{fontSize:"18px"}}),o.jsx(p.Z,{sx:{fontWeight:500,fontSize:"14px"},children:"Logout"})]})]})},P=e=>{let t=t=>{let a=(0,C.I0)(),m=(0,S.C)(e=>e.mobileSidebarToggle.isOpen),p=(0,j.usePathname)();return(0,o.jsxs)(c.Z,{children:[o.jsx(n.ZP,{}),!("/"===p||"/onboard"===p)&&(0,o.jsxs)(o.Fragment,{children:[o.jsx(r.Z,{position:"static",sx:{backgroundColor:"black"},children:o.jsx(i.Z,{sx:{mb:"/mobile/dashboard"===p?6:0},children:o.jsx(s.Z,{edge:"start",color:"inherit","aria-label":"menu",onClick:()=>a((0,Z.FJ)()),children:o.jsx(d.Z,{fontSize:"large"})})})}),o.jsx(l.ZP,{anchor:"left",open:m,onClose:()=>a((0,Z.dL)()),children:o.jsx(B,{})})]}),o.jsx(e,{...t})]})};return t.displayName=`WithMobileEdgeFill(${e.displayName||e.name||"Component"})`,t}},94638:(e,t,a)=>{a.d(t,{G9:()=>g,JZ:()=>x,M_:()=>y,N:()=>d,Nq:()=>u,TQ:()=>m,Ur:()=>s,aE:()=>c,aK:()=>I,dA:()=>l,gt:()=>b,mb:()=>h,qB:()=>S,yu:()=>p,zX:()=>f});var o=a(53148),n=a(39352),r=a(25748),i=a(32049);function s(e){switch(e){case"Our Culture & Workplace":return"Work Policies";case"Protecting Your Income":return"Income Security";default:return e}}function l(e){switch(e){case"On-site Amenities":return"Company Handbook";case"Protecting Your Income":return"Income Security";case"Got married/divorced":return"Marriage or Divorce";case"Had a baby or adopted a baby":return"New Baby or Adoption";case"Lost your insurance":return"Loss of Insurance";default:return e}}async function d(e,t){let a=await (0,o.A_)("/benefits/benefit-types",{companyId:t});return console.log("COMPANY BENEFIT TYPES RESPONSE: ",a.benefitTypes),e((0,n.x7)(a.benefitTypes)),a.benefitTypes}async function c(e,t){let a=await (0,o.A_)("/benefits/all-benefits",{companyId:t});console.log("COMPANY BENEFIT TYPES WITH BENEFITS RESPONSE: ",a),e((0,r.US)(a.benefitsPerType))}async function m(e){let t=await (0,o.A_)("/admin/all-employees");return console.log("COMPANY TEAM MEMBERS RESPONSE: ",t),e((0,n.Vv)(t.employees)),t.employees}async function p(e,t){return console.log("ADDING USERS: ",t),await (0,o.j0)("/admin/add/employees",{employeeList:t})}async function u(e,t,a){try{console.log("\uD83D\uDD0D Debug: User being updated:",t);let e={employeeId:t,updatedDetails:{name:a.name,email:a.email,details:{phoneNumber:a.phoneNumber||"",department:a.department||"",title:a.title||"",role:a.title||""}}};return a.dateOfBirth&&(e.updatedDetails.details.dateOfBirth=a.dateOfBirth),a.hireDate&&(e.updatedDetails.details.hireDate=a.hireDate),a.annualSalary&&(e.updatedDetails.details.annualSalary=a.annualSalary),a.employeeClassType&&(e.updatedDetails.details.employeeClassType=a.employeeClassType),a.workSchedule&&(e.updatedDetails.details.workSchedule=a.workSchedule),a.ssn&&(e.updatedDetails.details.ssn=a.ssn),a.employeeId&&(e.updatedDetails.details.employeeId=a.employeeId),a.workLocation&&(e.updatedDetails.details.workLocation=a.workLocation),a.address&&(e.updatedDetails.details.address=a.address),a.mailingAddress&&(e.updatedDetails.details.mailingAddress=a.mailingAddress),a.emergencyContact&&(e.updatedDetails.details.emergencyContact=a.emergencyContact),e.updatedDetails.details.dependents=a.dependents||[],console.log("Middleware - dependents being sent:",e.updatedDetails.details.dependents),console.log("Sending PUT request with cleaned data:",e),await (0,o.GH)("/admin/update/employee",e)}catch(e){throw console.error("Error in updateUser middleware:",e),e}}async function y(e,t){let a=await (0,o.A_)("/employee",{"user-id":t});return e((0,i.$l)({name:a.currentUser.name,email:a.currentUser.email,companyId:a.currentUser.companyId,role:a.currentUser.role,isAdmin:a.currentUser.isAdmin,isBroker:a.currentUser.isBroker,details:a.currentUser.details})),a}async function f(e,t,a){let n=await (0,o.j0)("/admin/onboard",{company:{name:t.name,adminEmail:t.adminEmail,adminRole:t.adminRole,companySize:t.companySize,industry:t.industry,location:t.location,website:t.website,howHeard:t.howHeard,brokerId:t.brokerId,brokerageId:t.brokerageId,isBrokerage:t.isBrokerage,isActivated:t.isActivated,referralSource:t.referralSource,details:{logo:""}},user:{email:a.email,name:a.name,role:a.role,isAdmin:a.isAdmin,isBroker:a.isBroker,isActivated:a.isActivated}}),r=n.data.userId,i=n.data.companyId;return localStorage.setItem("userid1",r),localStorage.setItem("companyId1",i),n}async function h(e,t){return console.log("SENDING LOGIN LINK TO EMPLOYEE: ",e,t),await (0,o.j0)("/admin/send-user-login-link",{userId:e,companyId:t})}async function g(e,t,a,n){let r=await (0,o.j0)("/admin/add/employer",{brokerId:e,companyName:t,companyAdminEmail:a,companyAdminName:n});return console.log("BROKER ADDS COMPANY RESPONSE: ",r),r}async function x(e,t){return 200===(await (0,o.j0)("/employee/offboard/",{userId:e,companyId:t})).status}async function b(e,t){return await (0,o.j0)("/employee/enable/",{userId:e,companyId:t})}async function S(e,t){try{let t=await (0,o.A_)("/admin/all-companies");console.log("CLIENT COMPANIES UNDER BROKER: ",t);let a=t.companies||[];try{let e=await (0,o.A_)("/employee/company-details");console.log("BROKER'S OWN COMPANY: ",e),e.company&&e.company.isBrokerage&&!a.some(t=>t._id===e.company._id)&&(a.unshift(e.company),console.log("Added broker's own company to the list"))}catch(e){console.log("Could not fetch broker's own company (this is normal if user is not a broker):",e)}return console.log("ALL COMPANIES (CLIENT + OWN): ",a),e((0,i.Ym)(a)),{...t,companies:a}}catch(t){return console.error("Error fetching companies:",t),e((0,i.Ym)([])),{companies:[]}}}async function I(e){let t=await (0,o.A_)("/employee/company-details");return e((0,n.sy)(t.company)),t.status}},31870:(e,t,a)=>{a.d(t,{C:()=>r,T:()=>n});var o=a(25842);let n=()=>(0,o.I0)(),r=o.v9},73881:(e,t,a)=>{a.r(t),a.d(t,{default:()=>n});var o=a(66621);let n=e=>[{type:"image/x-icon",sizes:"16x16",url:(0,o.fillMetadataSegment)(".",e.params,"favicon.ico")+""}]}};