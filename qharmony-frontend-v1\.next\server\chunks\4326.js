"use strict";exports.id=4326,exports.ids=[4326],exports.modules={14326:(e,t,o)=>{o.d(t,{Z:()=>eG});var r=o(17577),n=o(41135),i=o(34526),a=o(88634),p=o(44823),s=o(93244),l=o(64263),f=o(83784),c=o(91703),u=o(23743),d=o(13643),m=o(2791),h=o(54641),g=o(14962),v=o(72823),y=o(63212),b=o(34963);function w(e){if(null==e)return window;if("[object Window]"!==e.toString()){var t=e.ownerDocument;return t&&t.defaultView||window}return e}function x(e){var t=w(e).Element;return e instanceof t||e instanceof Element}function O(e){var t=w(e).HTMLElement;return e instanceof t||e instanceof HTMLElement}function R(e){if("undefined"==typeof ShadowRoot)return!1;var t=w(e).ShadowRoot;return e instanceof t||e instanceof ShadowRoot}var E=Math.max,T=Math.min,j=Math.round;function P(){var e=navigator.userAgentData;return null!=e&&e.brands&&Array.isArray(e.brands)?e.brands.map(function(e){return e.brand+"/"+e.version}).join(" "):navigator.userAgent}function M(){return!/^((?!chrome|android).)*safari/i.test(P())}function Z(e,t,o){void 0===t&&(t=!1),void 0===o&&(o=!1);var r=e.getBoundingClientRect(),n=1,i=1;t&&O(e)&&(n=e.offsetWidth>0&&j(r.width)/e.offsetWidth||1,i=e.offsetHeight>0&&j(r.height)/e.offsetHeight||1);var a=(x(e)?w(e):window).visualViewport,p=!M()&&o,s=(r.left+(p&&a?a.offsetLeft:0))/n,l=(r.top+(p&&a?a.offsetTop:0))/i,f=r.width/n,c=r.height/i;return{width:f,height:c,top:l,right:s+f,bottom:l+c,left:s,x:s,y:l}}function A(e){var t=w(e);return{scrollLeft:t.pageXOffset,scrollTop:t.pageYOffset}}function L(e){return e?(e.nodeName||"").toLowerCase():null}function S(e){return((x(e)?e.ownerDocument:e.document)||window.document).documentElement}function k(e){return Z(S(e)).left+A(e).scrollLeft}function W(e){return w(e).getComputedStyle(e)}function D(e){var t=W(e),o=t.overflow,r=t.overflowX,n=t.overflowY;return/auto|scroll|overlay|hidden/.test(o+n+r)}function B(e){var t=Z(e),o=e.offsetWidth,r=e.offsetHeight;return 1>=Math.abs(t.width-o)&&(o=t.width),1>=Math.abs(t.height-r)&&(r=t.height),{x:e.offsetLeft,y:e.offsetTop,width:o,height:r}}function $(e){return"html"===L(e)?e:e.assignedSlot||e.parentNode||(R(e)?e.host:null)||S(e)}function H(e,t){void 0===t&&(t=[]);var o,r=function e(t){return["html","body","#document"].indexOf(L(t))>=0?t.ownerDocument.body:O(t)&&D(t)?t:e($(t))}(e),n=r===(null==(o=e.ownerDocument)?void 0:o.body),i=w(r),a=n?[i].concat(i.visualViewport||[],D(r)?r:[]):r,p=t.concat(a);return n?p:p.concat(H($(a)))}function C(e){return O(e)&&"fixed"!==W(e).position?e.offsetParent:null}function N(e){for(var t=w(e),o=C(e);o&&["table","td","th"].indexOf(L(o))>=0&&"static"===W(o).position;)o=C(o);return o&&("html"===L(o)||"body"===L(o)&&"static"===W(o).position)?t:o||function(e){var t=/firefox/i.test(P());if(/Trident/i.test(P())&&O(e)&&"fixed"===W(e).position)return null;var o=$(e);for(R(o)&&(o=o.host);O(o)&&0>["html","body"].indexOf(L(o));){var r=W(o);if("none"!==r.transform||"none"!==r.perspective||"paint"===r.contain||-1!==["transform","perspective"].indexOf(r.willChange)||t&&"filter"===r.willChange||t&&r.filter&&"none"!==r.filter)return o;o=o.parentNode}return null}(e)||t}var I="bottom",F="right",V="left",q="auto",U=["top",I,F,V],z="start",X="viewport",Y="popper",_=U.reduce(function(e,t){return e.concat([t+"-"+z,t+"-end"])},[]),G=[].concat(U,[q]).reduce(function(e,t){return e.concat([t,t+"-"+z,t+"-end"])},[]),J=["beforeRead","read","afterRead","beforeMain","main","afterMain","beforeWrite","write","afterWrite"],K={placement:"bottom",modifiers:[],strategy:"absolute"};function Q(){for(var e=arguments.length,t=Array(e),o=0;o<e;o++)t[o]=arguments[o];return!t.some(function(e){return!(e&&"function"==typeof e.getBoundingClientRect)})}var ee={passive:!0};function et(e){return e.split("-")[0]}function eo(e){return e.split("-")[1]}function er(e){return["top","bottom"].indexOf(e)>=0?"x":"y"}function en(e){var t,o=e.reference,r=e.element,n=e.placement,i=n?et(n):null,a=n?eo(n):null,p=o.x+o.width/2-r.width/2,s=o.y+o.height/2-r.height/2;switch(i){case"top":t={x:p,y:o.y-r.height};break;case I:t={x:p,y:o.y+o.height};break;case F:t={x:o.x+o.width,y:s};break;case V:t={x:o.x-r.width,y:s};break;default:t={x:o.x,y:o.y}}var l=i?er(i):null;if(null!=l){var f="y"===l?"height":"width";switch(a){case z:t[l]=t[l]-(o[f]/2-r[f]/2);break;case"end":t[l]=t[l]+(o[f]/2-r[f]/2)}}return t}var ei={top:"auto",right:"auto",bottom:"auto",left:"auto"};function ea(e){var t,o,r,n,i,a,p,s=e.popper,l=e.popperRect,f=e.placement,c=e.variation,u=e.offsets,d=e.position,m=e.gpuAcceleration,h=e.adaptive,g=e.roundOffsets,v=e.isFixed,y=u.x,b=void 0===y?0:y,x=u.y,O=void 0===x?0:x,R="function"==typeof g?g({x:b,y:O}):{x:b,y:O};b=R.x,O=R.y;var E=u.hasOwnProperty("x"),T=u.hasOwnProperty("y"),P=V,M="top",Z=window;if(h){var A=N(s),L="clientHeight",k="clientWidth";A===w(s)&&"static"!==W(A=S(s)).position&&"absolute"===d&&(L="scrollHeight",k="scrollWidth"),("top"===f||(f===V||f===F)&&"end"===c)&&(M=I,O-=(v&&A===Z&&Z.visualViewport?Z.visualViewport.height:A[L])-l.height,O*=m?1:-1),(f===V||("top"===f||f===I)&&"end"===c)&&(P=F,b-=(v&&A===Z&&Z.visualViewport?Z.visualViewport.width:A[k])-l.width,b*=m?1:-1)}var D=Object.assign({position:d},h&&ei),B=!0===g?(t={x:b,y:O},o=w(s),r=t.x,n=t.y,{x:j(r*(i=o.devicePixelRatio||1))/i||0,y:j(n*i)/i||0}):{x:b,y:O};return(b=B.x,O=B.y,m)?Object.assign({},D,((p={})[M]=T?"0":"",p[P]=E?"0":"",p.transform=1>=(Z.devicePixelRatio||1)?"translate("+b+"px, "+O+"px)":"translate3d("+b+"px, "+O+"px, 0)",p)):Object.assign({},D,((a={})[M]=T?O+"px":"",a[P]=E?b+"px":"",a.transform="",a))}var ep={left:"right",right:"left",bottom:"top",top:"bottom"};function es(e){return e.replace(/left|right|bottom|top/g,function(e){return ep[e]})}var el={start:"end",end:"start"};function ef(e){return e.replace(/start|end/g,function(e){return el[e]})}function ec(e,t){var o=t.getRootNode&&t.getRootNode();if(e.contains(t))return!0;if(o&&R(o)){var r=t;do{if(r&&e.isSameNode(r))return!0;r=r.parentNode||r.host}while(r)}return!1}function eu(e){return Object.assign({},e,{left:e.x,top:e.y,right:e.x+e.width,bottom:e.y+e.height})}function ed(e,t,o){var r,n,i,a,p,s,l,f,c,u;return t===X?eu(function(e,t){var o=w(e),r=S(e),n=o.visualViewport,i=r.clientWidth,a=r.clientHeight,p=0,s=0;if(n){i=n.width,a=n.height;var l=M();(l||!l&&"fixed"===t)&&(p=n.offsetLeft,s=n.offsetTop)}return{width:i,height:a,x:p+k(e),y:s}}(e,o)):x(t)?((r=Z(t,!1,"fixed"===o)).top=r.top+t.clientTop,r.left=r.left+t.clientLeft,r.bottom=r.top+t.clientHeight,r.right=r.left+t.clientWidth,r.width=t.clientWidth,r.height=t.clientHeight,r.x=r.left,r.y=r.top,r):eu((n=S(e),a=S(n),p=A(n),s=null==(i=n.ownerDocument)?void 0:i.body,l=E(a.scrollWidth,a.clientWidth,s?s.scrollWidth:0,s?s.clientWidth:0),f=E(a.scrollHeight,a.clientHeight,s?s.scrollHeight:0,s?s.clientHeight:0),c=-p.scrollLeft+k(n),u=-p.scrollTop,"rtl"===W(s||a).direction&&(c+=E(a.clientWidth,s?s.clientWidth:0)-l),{width:l,height:f,x:c,y:u}))}function em(){return{top:0,right:0,bottom:0,left:0}}function eh(e){return Object.assign({},em(),e)}function eg(e,t){return t.reduce(function(t,o){return t[o]=e,t},{})}function ev(e,t){void 0===t&&(t={});var o,r,n,i,a,p,s,l,f=t,c=f.placement,u=void 0===c?e.placement:c,d=f.strategy,m=void 0===d?e.strategy:d,h=f.boundary,g=f.rootBoundary,v=f.elementContext,y=void 0===v?Y:v,b=f.altBoundary,w=f.padding,R=void 0===w?0:w,j=eh("number"!=typeof R?R:eg(R,U)),P=e.rects.popper,M=e.elements[void 0!==b&&b?y===Y?"reference":Y:y],A=(o=x(M)?M:M.contextElement||S(e.elements.popper),r=void 0===h?"clippingParents":h,n=void 0===g?X:g,s=(p=[].concat("clippingParents"===r?(i=H($(o)),x(a=["absolute","fixed"].indexOf(W(o).position)>=0&&O(o)?N(o):o)?i.filter(function(e){return x(e)&&ec(e,a)&&"body"!==L(e)}):[]):[].concat(r),[n]))[0],(l=p.reduce(function(e,t){var r=ed(o,t,m);return e.top=E(r.top,e.top),e.right=T(r.right,e.right),e.bottom=T(r.bottom,e.bottom),e.left=E(r.left,e.left),e},ed(o,s,m))).width=l.right-l.left,l.height=l.bottom-l.top,l.x=l.left,l.y=l.top,l),k=Z(e.elements.reference),D=en({reference:k,element:P,strategy:"absolute",placement:u}),B=eu(Object.assign({},P,D)),C=y===Y?B:k,V={top:A.top-C.top+j.top,bottom:C.bottom-A.bottom+j.bottom,left:A.left-C.left+j.left,right:C.right-A.right+j.right},q=e.modifiersData.offset;if(y===Y&&q){var z=q[u];Object.keys(V).forEach(function(e){var t=[F,I].indexOf(e)>=0?1:-1,o=["top",I].indexOf(e)>=0?"y":"x";V[e]+=z[o]*t})}return V}function ey(e,t,o){return E(e,T(t,o))}function eb(e,t,o){return void 0===o&&(o={x:0,y:0}),{top:e.top-t.height-o.y,right:e.right-t.width+o.x,bottom:e.bottom-t.height+o.y,left:e.left-t.width-o.x}}function ew(e){return["top",F,I,V].some(function(t){return e[t]>=0})}var ex=function(e){void 0===e&&(e={});var t=e,o=t.defaultModifiers,r=void 0===o?[]:o,n=t.defaultOptions,i=void 0===n?K:n;return function(e,t,o){void 0===o&&(o=i);var n,a,p={placement:"bottom",orderedModifiers:[],options:Object.assign({},K,i),modifiersData:{},elements:{reference:e,popper:t},attributes:{},styles:{}},s=[],l=!1,f={state:p,setOptions:function(o){var n,a,l,u,d,m="function"==typeof o?o(p.options):o;c(),p.options=Object.assign({},i,p.options,m),p.scrollParents={reference:x(e)?H(e):e.contextElement?H(e.contextElement):[],popper:H(t)};var h=(a=Object.keys(n=[].concat(r,p.options.modifiers).reduce(function(e,t){var o=e[t.name];return e[t.name]=o?Object.assign({},o,t,{options:Object.assign({},o.options,t.options),data:Object.assign({},o.data,t.data)}):t,e},{})).map(function(e){return n[e]}),l=new Map,u=new Set,d=[],a.forEach(function(e){l.set(e.name,e)}),a.forEach(function(e){u.has(e.name)||function e(t){u.add(t.name),[].concat(t.requires||[],t.requiresIfExists||[]).forEach(function(t){if(!u.has(t)){var o=l.get(t);o&&e(o)}}),d.push(t)}(e)}),J.reduce(function(e,t){return e.concat(d.filter(function(e){return e.phase===t}))},[]));return p.orderedModifiers=h.filter(function(e){return e.enabled}),p.orderedModifiers.forEach(function(e){var t=e.name,o=e.options,r=e.effect;if("function"==typeof r){var n=r({state:p,name:t,instance:f,options:void 0===o?{}:o});s.push(n||function(){})}}),f.update()},forceUpdate:function(){if(!l){var e,t,o,r,n,i,a,s,c,u,d,m,h=p.elements,g=h.reference,v=h.popper;if(Q(g,v)){p.rects={reference:(t=N(v),o="fixed"===p.options.strategy,r=O(t),s=O(t)&&(i=j((n=t.getBoundingClientRect()).width)/t.offsetWidth||1,a=j(n.height)/t.offsetHeight||1,1!==i||1!==a),c=S(t),u=Z(g,s,o),d={scrollLeft:0,scrollTop:0},m={x:0,y:0},(r||!r&&!o)&&(("body"!==L(t)||D(c))&&(d=(e=t)!==w(e)&&O(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:A(e)),O(t)?(m=Z(t,!0),m.x+=t.clientLeft,m.y+=t.clientTop):c&&(m.x=k(c))),{x:u.left+d.scrollLeft-m.x,y:u.top+d.scrollTop-m.y,width:u.width,height:u.height}),popper:B(v)},p.reset=!1,p.placement=p.options.placement,p.orderedModifiers.forEach(function(e){return p.modifiersData[e.name]=Object.assign({},e.data)});for(var y=0;y<p.orderedModifiers.length;y++){if(!0===p.reset){p.reset=!1,y=-1;continue}var b=p.orderedModifiers[y],x=b.fn,R=b.options,E=void 0===R?{}:R,T=b.name;"function"==typeof x&&(p=x({state:p,options:E,name:T,instance:f})||p)}}}},update:(n=function(){return new Promise(function(e){f.forceUpdate(),e(p)})},function(){return a||(a=new Promise(function(e){Promise.resolve().then(function(){a=void 0,e(n())})})),a}),destroy:function(){c(),l=!0}};if(!Q(e,t))return f;function c(){s.forEach(function(e){return e()}),s=[]}return f.setOptions(o).then(function(e){!l&&o.onFirstUpdate&&o.onFirstUpdate(e)}),f}}({defaultModifiers:[{name:"eventListeners",enabled:!0,phase:"write",fn:function(){},effect:function(e){var t=e.state,o=e.instance,r=e.options,n=r.scroll,i=void 0===n||n,a=r.resize,p=void 0===a||a,s=w(t.elements.popper),l=[].concat(t.scrollParents.reference,t.scrollParents.popper);return i&&l.forEach(function(e){e.addEventListener("scroll",o.update,ee)}),p&&s.addEventListener("resize",o.update,ee),function(){i&&l.forEach(function(e){e.removeEventListener("scroll",o.update,ee)}),p&&s.removeEventListener("resize",o.update,ee)}},data:{}},{name:"popperOffsets",enabled:!0,phase:"read",fn:function(e){var t=e.state,o=e.name;t.modifiersData[o]=en({reference:t.rects.reference,element:t.rects.popper,strategy:"absolute",placement:t.placement})},data:{}},{name:"computeStyles",enabled:!0,phase:"beforeWrite",fn:function(e){var t=e.state,o=e.options,r=o.gpuAcceleration,n=o.adaptive,i=o.roundOffsets,a=void 0===i||i,p={placement:et(t.placement),variation:eo(t.placement),popper:t.elements.popper,popperRect:t.rects.popper,gpuAcceleration:void 0===r||r,isFixed:"fixed"===t.options.strategy};null!=t.modifiersData.popperOffsets&&(t.styles.popper=Object.assign({},t.styles.popper,ea(Object.assign({},p,{offsets:t.modifiersData.popperOffsets,position:t.options.strategy,adaptive:void 0===n||n,roundOffsets:a})))),null!=t.modifiersData.arrow&&(t.styles.arrow=Object.assign({},t.styles.arrow,ea(Object.assign({},p,{offsets:t.modifiersData.arrow,position:"absolute",adaptive:!1,roundOffsets:a})))),t.attributes.popper=Object.assign({},t.attributes.popper,{"data-popper-placement":t.placement})},data:{}},{name:"applyStyles",enabled:!0,phase:"write",fn:function(e){var t=e.state;Object.keys(t.elements).forEach(function(e){var o=t.styles[e]||{},r=t.attributes[e]||{},n=t.elements[e];O(n)&&L(n)&&(Object.assign(n.style,o),Object.keys(r).forEach(function(e){var t=r[e];!1===t?n.removeAttribute(e):n.setAttribute(e,!0===t?"":t)}))})},effect:function(e){var t=e.state,o={popper:{position:t.options.strategy,left:"0",top:"0",margin:"0"},arrow:{position:"absolute"},reference:{}};return Object.assign(t.elements.popper.style,o.popper),t.styles=o,t.elements.arrow&&Object.assign(t.elements.arrow.style,o.arrow),function(){Object.keys(t.elements).forEach(function(e){var r=t.elements[e],n=t.attributes[e]||{},i=Object.keys(t.styles.hasOwnProperty(e)?t.styles[e]:o[e]).reduce(function(e,t){return e[t]="",e},{});O(r)&&L(r)&&(Object.assign(r.style,i),Object.keys(n).forEach(function(e){r.removeAttribute(e)}))})}},requires:["computeStyles"]},{name:"offset",enabled:!0,phase:"main",requires:["popperOffsets"],fn:function(e){var t=e.state,o=e.options,r=e.name,n=o.offset,i=void 0===n?[0,0]:n,a=G.reduce(function(e,o){var r,n,a,p,s,l;return e[o]=(r=t.rects,a=[V,"top"].indexOf(n=et(o))>=0?-1:1,s=(p="function"==typeof i?i(Object.assign({},r,{placement:o})):i)[0],l=p[1],s=s||0,l=(l||0)*a,[V,F].indexOf(n)>=0?{x:l,y:s}:{x:s,y:l}),e},{}),p=a[t.placement],s=p.x,l=p.y;null!=t.modifiersData.popperOffsets&&(t.modifiersData.popperOffsets.x+=s,t.modifiersData.popperOffsets.y+=l),t.modifiersData[r]=a}},{name:"flip",enabled:!0,phase:"main",fn:function(e){var t=e.state,o=e.options,r=e.name;if(!t.modifiersData[r]._skip){for(var n=o.mainAxis,i=void 0===n||n,a=o.altAxis,p=void 0===a||a,s=o.fallbackPlacements,l=o.padding,f=o.boundary,c=o.rootBoundary,u=o.altBoundary,d=o.flipVariations,m=void 0===d||d,h=o.allowedAutoPlacements,g=t.options.placement,v=et(g)===g,y=s||(v||!m?[es(g)]:function(e){if(et(e)===q)return[];var t=es(e);return[ef(e),t,ef(t)]}(g)),b=[g].concat(y).reduce(function(e,o){var r,n,i,a,p,s,u,d,g,v,y,b;return e.concat(et(o)===q?(n=(r={placement:o,boundary:f,rootBoundary:c,padding:l,flipVariations:m,allowedAutoPlacements:h}).placement,i=r.boundary,a=r.rootBoundary,p=r.padding,s=r.flipVariations,d=void 0===(u=r.allowedAutoPlacements)?G:u,0===(y=(v=(g=eo(n))?s?_:_.filter(function(e){return eo(e)===g}):U).filter(function(e){return d.indexOf(e)>=0})).length&&(y=v),Object.keys(b=y.reduce(function(e,o){return e[o]=ev(t,{placement:o,boundary:i,rootBoundary:a,padding:p})[et(o)],e},{})).sort(function(e,t){return b[e]-b[t]})):o)},[]),w=t.rects.reference,x=t.rects.popper,O=new Map,R=!0,E=b[0],T=0;T<b.length;T++){var j=b[T],P=et(j),M=eo(j)===z,Z=["top",I].indexOf(P)>=0,A=Z?"width":"height",L=ev(t,{placement:j,boundary:f,rootBoundary:c,altBoundary:u,padding:l}),S=Z?M?F:V:M?I:"top";w[A]>x[A]&&(S=es(S));var k=es(S),W=[];if(i&&W.push(L[P]<=0),p&&W.push(L[S]<=0,L[k]<=0),W.every(function(e){return e})){E=j,R=!1;break}O.set(j,W)}if(R)for(var D=m?3:1,B=function(e){var t=b.find(function(t){var o=O.get(t);if(o)return o.slice(0,e).every(function(e){return e})});if(t)return E=t,"break"},$=D;$>0&&"break"!==B($);$--);t.placement!==E&&(t.modifiersData[r]._skip=!0,t.placement=E,t.reset=!0)}},requiresIfExists:["offset"],data:{_skip:!1}},{name:"preventOverflow",enabled:!0,phase:"main",fn:function(e){var t=e.state,o=e.options,r=e.name,n=o.mainAxis,i=o.altAxis,a=o.boundary,p=o.rootBoundary,s=o.altBoundary,l=o.padding,f=o.tether,c=void 0===f||f,u=o.tetherOffset,d=void 0===u?0:u,m=ev(t,{boundary:a,rootBoundary:p,padding:l,altBoundary:s}),h=et(t.placement),g=eo(t.placement),v=!g,y=er(h),b="x"===y?"y":"x",w=t.modifiersData.popperOffsets,x=t.rects.reference,O=t.rects.popper,R="function"==typeof d?d(Object.assign({},t.rects,{placement:t.placement})):d,j="number"==typeof R?{mainAxis:R,altAxis:R}:Object.assign({mainAxis:0,altAxis:0},R),P=t.modifiersData.offset?t.modifiersData.offset[t.placement]:null,M={x:0,y:0};if(w){if(void 0===n||n){var Z,A="y"===y?"top":V,L="y"===y?I:F,S="y"===y?"height":"width",k=w[y],W=k+m[A],D=k-m[L],$=c?-O[S]/2:0,H=g===z?x[S]:O[S],C=g===z?-O[S]:-x[S],q=t.elements.arrow,U=c&&q?B(q):{width:0,height:0},X=t.modifiersData["arrow#persistent"]?t.modifiersData["arrow#persistent"].padding:em(),Y=X[A],_=X[L],G=ey(0,x[S],U[S]),J=v?x[S]/2-$-G-Y-j.mainAxis:H-G-Y-j.mainAxis,K=v?-x[S]/2+$+G+_+j.mainAxis:C+G+_+j.mainAxis,Q=t.elements.arrow&&N(t.elements.arrow),ee=Q?"y"===y?Q.clientTop||0:Q.clientLeft||0:0,en=null!=(Z=null==P?void 0:P[y])?Z:0,ei=ey(c?T(W,k+J-en-ee):W,k,c?E(D,k+K-en):D);w[y]=ei,M[y]=ei-k}if(void 0!==i&&i){var ea,ep,es="x"===y?"top":V,el="x"===y?I:F,ef=w[b],ec="y"===b?"height":"width",eu=ef+m[es],ed=ef-m[el],eh=-1!==["top",V].indexOf(h),eg=null!=(ep=null==P?void 0:P[b])?ep:0,eb=eh?eu:ef-x[ec]-O[ec]-eg+j.altAxis,ew=eh?ef+x[ec]+O[ec]-eg-j.altAxis:ed,ex=c&&eh?(ea=ey(eb,ef,ew))>ew?ew:ea:ey(c?eb:eu,ef,c?ew:ed);w[b]=ex,M[b]=ex-ef}t.modifiersData[r]=M}},requiresIfExists:["offset"]},{name:"arrow",enabled:!0,phase:"main",fn:function(e){var t,o,r=e.state,n=e.name,i=e.options,a=r.elements.arrow,p=r.modifiersData.popperOffsets,s=et(r.placement),l=er(s),f=[V,F].indexOf(s)>=0?"height":"width";if(a&&p){var c=eh("number"!=typeof(t="function"==typeof(t=i.padding)?t(Object.assign({},r.rects,{placement:r.placement})):t)?t:eg(t,U)),u=B(a),d="y"===l?"top":V,m="y"===l?I:F,h=r.rects.reference[f]+r.rects.reference[l]-p[l]-r.rects.popper[f],g=p[l]-r.rects.reference[l],v=N(a),y=v?"y"===l?v.clientHeight||0:v.clientWidth||0:0,b=c[d],w=y-u[f]-c[m],x=y/2-u[f]/2+(h/2-g/2),O=ey(b,x,w);r.modifiersData[n]=((o={})[l]=O,o.centerOffset=O-x,o)}},effect:function(e){var t=e.state,o=e.options.element,r=void 0===o?"[data-popper-arrow]":o;null!=r&&("string"!=typeof r||(r=t.elements.popper.querySelector(r)))&&ec(t.elements.popper,r)&&(t.elements.arrow=r)},requires:["popperOffsets"],requiresIfExists:["preventOverflow"]},{name:"hide",enabled:!0,phase:"main",requiresIfExists:["preventOverflow"],fn:function(e){var t=e.state,o=e.name,r=t.rects.reference,n=t.rects.popper,i=t.modifiersData.preventOverflow,a=ev(t,{elementContext:"reference"}),p=ev(t,{altBoundary:!0}),s=eb(a,r),l=eb(p,n,i),f=ew(s),c=ew(l);t.modifiersData[o]={referenceClippingOffsets:s,popperEscapeOffsets:l,isReferenceHidden:f,hasPopperEscaped:c},t.attributes.popper=Object.assign({},t.attributes.popper,{"data-popper-reference-hidden":f,"data-popper-escaped":c})}}]}),eO=o(69800),eR=o(99773),eE=o(71685),eT=o(97898);function ej(e){return(0,eT.ZP)("MuiPopper",e)}(0,eE.Z)("MuiPopper",["root"]);var eP=o(10326);function eM(e){return"function"==typeof e?e():e}let eZ=e=>{let{classes:t}=e;return(0,a.Z)({root:["root"]},ej,t)},eA={},eL=r.forwardRef(function(e,t){let{anchorEl:o,children:n,direction:i,disablePortal:a,modifiers:p,open:s,placement:l,popperOptions:f,popperRef:c,slotProps:u={},slots:d={},TransitionProps:m,ownerState:h,...g}=e,b=r.useRef(null),w=(0,v.Z)(b,t),x=r.useRef(null),O=(0,v.Z)(x,c),R=r.useRef(O);(0,y.Z)(()=>{R.current=O},[O]),r.useImperativeHandle(c,()=>x.current,[]);let E=function(e,t){if("ltr"===t)return e;switch(e){case"bottom-end":return"bottom-start";case"bottom-start":return"bottom-end";case"top-end":return"top-start";case"top-start":return"top-end";default:return e}}(l,i),[T,j]=r.useState(E),[P,M]=r.useState(eM(o));r.useEffect(()=>{x.current&&x.current.forceUpdate()}),r.useEffect(()=>{o&&M(eM(o))},[o]),(0,y.Z)(()=>{if(!P||!s)return;let e=e=>{j(e.placement)},t=[{name:"preventOverflow",options:{altBoundary:a}},{name:"flip",options:{altBoundary:a}},{name:"onUpdate",enabled:!0,phase:"afterWrite",fn:({state:t})=>{e(t)}}];null!=p&&(t=t.concat(p)),f&&null!=f.modifiers&&(t=t.concat(f.modifiers));let o=ex(P,b.current,{placement:E,...f,modifiers:t});return R.current(o),()=>{o.destroy(),R.current(null)}},[P,a,p,s,f,E]);let Z={placement:T};null!==m&&(Z.TransitionProps=m);let A=eZ(e),L=d.root??"div",S=(0,eO.Z)({elementType:L,externalSlotProps:u.root,externalForwardedProps:g,additionalProps:{role:"tooltip",ref:w},ownerState:e,className:A.root});return(0,eP.jsx)(L,{...S,children:"function"==typeof n?n(Z):n})}),eS=r.forwardRef(function(e,t){let o;let{anchorEl:n,children:i,container:a,direction:p="ltr",disablePortal:s=!1,keepMounted:l=!1,modifiers:f,open:c,placement:u="bottom",popperOptions:d=eA,popperRef:m,style:h,transition:g=!1,slotProps:v={},slots:y={},...w}=e,[x,O]=r.useState(!0);if(!l&&!c&&(!g||x))return null;if(a)o=a;else if(n){let e=eM(n);o=e&&void 0!==e.nodeType?(0,b.Z)(e).body:(0,b.Z)(null).body}let R=!c&&l&&(!g||x)?"none":void 0,E=g?{in:c,onEnter:()=>{O(!1)},onExited:()=>{O(!0)}}:void 0;return(0,eP.jsx)(eR.Z,{disablePortal:s,container:o,children:(0,eP.jsx)(eL,{anchorEl:n,direction:p,disablePortal:s,modifiers:f,ref:t,open:g?!x:c,placement:u,popperOptions:d,popperRef:m,slotProps:v,slots:y,...w,style:{position:"fixed",top:0,left:0,display:R,...h},TransitionProps:E,children:i})})}),ek=(0,c.default)(eS,{name:"MuiPopper",slot:"Root",overridesResolver:(e,t)=>t.root})({}),eW=r.forwardRef(function(e,t){let o=(0,s.useRtl)(),{anchorEl:r,component:n,components:i,componentsProps:a,container:p,disablePortal:l,keepMounted:f,modifiers:c,open:u,placement:d,popperOptions:h,popperRef:g,transition:v,slots:y,slotProps:b,...w}=(0,m.i)({props:e,name:"MuiPopper"}),x=y?.root??i?.Root,O={anchorEl:r,container:p,disablePortal:l,keepMounted:f,modifiers:c,open:u,placement:d,popperOptions:h,popperRef:g,transition:v,...w};return(0,eP.jsx)(ek,{as:n,direction:o?"rtl":"ltr",slots:{root:x},slotProps:b??a,...O,ref:t})});var eD=o(24533),eB=o(37382),e$=o(87816),eH=o(43227),eC=o(31121);function eN(e){return(0,eT.ZP)("MuiTooltip",e)}let eI=(0,eE.Z)("MuiTooltip",["popper","popperInteractive","popperArrow","popperClose","tooltip","tooltipArrow","touch","tooltipPlacementLeft","tooltipPlacementRight","tooltipPlacementTop","tooltipPlacementBottom","arrow"]),eF=e=>{let{classes:t,disableInteractive:o,arrow:r,touch:n,placement:i}=e,p={popper:["popper",!o&&"popperInteractive",r&&"popperArrow"],tooltip:["tooltip",r&&"tooltipArrow",n&&"touch",`tooltipPlacement${(0,h.Z)(i.split("-")[0])}`],arrow:["arrow"]};return(0,a.Z)(p,eN,t)},eV=(0,c.default)(eW,{name:"MuiTooltip",slot:"Popper",overridesResolver:(e,t)=>{let{ownerState:o}=e;return[t.popper,!o.disableInteractive&&t.popperInteractive,o.arrow&&t.popperArrow,!o.open&&t.popperClose]}})((0,d.Z)(({theme:e})=>({zIndex:(e.vars||e).zIndex.tooltip,pointerEvents:"none",variants:[{props:({ownerState:e})=>!e.disableInteractive,style:{pointerEvents:"auto"}},{props:({open:e})=>!e,style:{pointerEvents:"none"}},{props:({ownerState:e})=>e.arrow,style:{[`&[data-popper-placement*="bottom"] .${eI.arrow}`]:{top:0,marginTop:"-0.71em","&::before":{transformOrigin:"0 100%"}},[`&[data-popper-placement*="top"] .${eI.arrow}`]:{bottom:0,marginBottom:"-0.71em","&::before":{transformOrigin:"100% 0"}},[`&[data-popper-placement*="right"] .${eI.arrow}`]:{height:"1em",width:"0.71em","&::before":{transformOrigin:"100% 100%"}},[`&[data-popper-placement*="left"] .${eI.arrow}`]:{height:"1em",width:"0.71em","&::before":{transformOrigin:"0 0"}}}},{props:({ownerState:e})=>e.arrow&&!e.isRtl,style:{[`&[data-popper-placement*="right"] .${eI.arrow}`]:{left:0,marginLeft:"-0.71em"}}},{props:({ownerState:e})=>e.arrow&&!!e.isRtl,style:{[`&[data-popper-placement*="right"] .${eI.arrow}`]:{right:0,marginRight:"-0.71em"}}},{props:({ownerState:e})=>e.arrow&&!e.isRtl,style:{[`&[data-popper-placement*="left"] .${eI.arrow}`]:{right:0,marginRight:"-0.71em"}}},{props:({ownerState:e})=>e.arrow&&!!e.isRtl,style:{[`&[data-popper-placement*="left"] .${eI.arrow}`]:{left:0,marginLeft:"-0.71em"}}}]}))),eq=(0,c.default)("div",{name:"MuiTooltip",slot:"Tooltip",overridesResolver:(e,t)=>{let{ownerState:o}=e;return[t.tooltip,o.touch&&t.touch,o.arrow&&t.tooltipArrow,t[`tooltipPlacement${(0,h.Z)(o.placement.split("-")[0])}`]]}})((0,d.Z)(({theme:e})=>({backgroundColor:e.vars?e.vars.palette.Tooltip.bg:(0,p.Fq)(e.palette.grey[700],.92),borderRadius:(e.vars||e).shape.borderRadius,color:(e.vars||e).palette.common.white,fontFamily:e.typography.fontFamily,padding:"4px 8px",fontSize:e.typography.pxToRem(11),maxWidth:300,margin:2,wordWrap:"break-word",fontWeight:e.typography.fontWeightMedium,[`.${eI.popper}[data-popper-placement*="left"] &`]:{transformOrigin:"right center"},[`.${eI.popper}[data-popper-placement*="right"] &`]:{transformOrigin:"left center"},[`.${eI.popper}[data-popper-placement*="top"] &`]:{transformOrigin:"center bottom",marginBottom:"14px"},[`.${eI.popper}[data-popper-placement*="bottom"] &`]:{transformOrigin:"center top",marginTop:"14px"},variants:[{props:({ownerState:e})=>e.arrow,style:{position:"relative",margin:0}},{props:({ownerState:e})=>e.touch,style:{padding:"8px 16px",fontSize:e.typography.pxToRem(14),lineHeight:`${Math.round(16/14*1e5)/1e5}em`,fontWeight:e.typography.fontWeightRegular}},{props:({ownerState:e})=>!e.isRtl,style:{[`.${eI.popper}[data-popper-placement*="left"] &`]:{marginRight:"14px"},[`.${eI.popper}[data-popper-placement*="right"] &`]:{marginLeft:"14px"}}},{props:({ownerState:e})=>!e.isRtl&&e.touch,style:{[`.${eI.popper}[data-popper-placement*="left"] &`]:{marginRight:"24px"},[`.${eI.popper}[data-popper-placement*="right"] &`]:{marginLeft:"24px"}}},{props:({ownerState:e})=>!!e.isRtl,style:{[`.${eI.popper}[data-popper-placement*="left"] &`]:{marginLeft:"14px"},[`.${eI.popper}[data-popper-placement*="right"] &`]:{marginRight:"14px"}}},{props:({ownerState:e})=>!!e.isRtl&&e.touch,style:{[`.${eI.popper}[data-popper-placement*="left"] &`]:{marginLeft:"24px"},[`.${eI.popper}[data-popper-placement*="right"] &`]:{marginRight:"24px"}}},{props:({ownerState:e})=>e.touch,style:{[`.${eI.popper}[data-popper-placement*="top"] &`]:{marginBottom:"24px"}}},{props:({ownerState:e})=>e.touch,style:{[`.${eI.popper}[data-popper-placement*="bottom"] &`]:{marginTop:"24px"}}}]}))),eU=(0,c.default)("span",{name:"MuiTooltip",slot:"Arrow",overridesResolver:(e,t)=>t.arrow})((0,d.Z)(({theme:e})=>({overflow:"hidden",position:"absolute",width:"1em",height:"0.71em",boxSizing:"border-box",color:e.vars?e.vars.palette.Tooltip.bg:(0,p.Fq)(e.palette.grey[700],.9),"&::before":{content:'""',margin:"auto",display:"block",width:"100%",height:"100%",backgroundColor:"currentColor",transform:"rotate(45deg)"}}))),ez=!1,eX=new i.V,eY={x:0,y:0};function e_(e,t){return(o,...r)=>{t&&t(o,...r),e(o,...r)}}let eG=r.forwardRef(function(e,t){let o=(0,m.i)({props:e,name:"MuiTooltip"}),{arrow:a=!1,children:p,classes:c,components:d={},componentsProps:h={},describeChild:v=!1,disableFocusListener:y=!1,disableHoverListener:b=!1,disableInteractive:w=!1,disableTouchListener:x=!1,enterDelay:O=100,enterNextDelay:R=0,enterTouchDelay:E=700,followCursor:T=!1,id:j,leaveDelay:P=0,leaveTouchDelay:M=1500,onClose:Z,onOpen:A,open:L,placement:S="bottom",PopperComponent:k,PopperProps:W={},slotProps:D={},slots:B={},title:$,TransitionComponent:H,TransitionProps:C,...N}=o,I=r.isValidElement(p)?p:(0,eP.jsx)("span",{children:p}),F=(0,u.default)(),V=(0,s.useRtl)(),[q,U]=r.useState(),[z,X]=r.useState(null),Y=r.useRef(!1),_=w||T,G=(0,i.Z)(),J=(0,i.Z)(),K=(0,i.Z)(),Q=(0,i.Z)(),[ee,et]=(0,eH.Z)({controlled:L,default:!1,name:"Tooltip",state:"open"}),eo=ee,er=(0,e$.Z)(j),en=r.useRef(),ei=(0,eD.Z)(()=>{void 0!==en.current&&(document.body.style.WebkitUserSelect=en.current,en.current=void 0),Q.clear()});r.useEffect(()=>ei,[ei]);let ea=e=>{eX.clear(),ez=!0,et(!0),A&&!eo&&A(e)},ep=(0,eD.Z)(e=>{eX.start(800+P,()=>{ez=!1}),et(!1),Z&&eo&&Z(e),G.start(F.transitions.duration.shortest,()=>{Y.current=!1})}),es=e=>{Y.current&&"touchstart"!==e.type||(q&&q.removeAttribute("title"),J.clear(),K.clear(),O||ez&&R?J.start(ez?R:O,()=>{ea(e)}):ea(e))},el=e=>{J.clear(),K.start(P,()=>{ep(e)})},[,ef]=r.useState(!1),ec=e=>{(0,l.Z)(e.target)||(ef(!1),el(e))},eu=e=>{q||U(e.currentTarget),(0,l.Z)(e.target)&&(ef(!0),es(e))},ed=e=>{Y.current=!0;let t=I.props;t.onTouchStart&&t.onTouchStart(e)};r.useEffect(()=>{if(eo)return document.addEventListener("keydown",e),()=>{document.removeEventListener("keydown",e)};function e(e){"Escape"===e.key&&ep(e)}},[ep,eo]);let em=(0,eB.Z)((0,f.Z)(I),U,t);$||0===$||(eo=!1);let eh=r.useRef(),eg={},ev="string"==typeof $;v?(eg.title=eo||!ev||b?null:$,eg["aria-describedby"]=eo?er:null):(eg["aria-label"]=ev?$:null,eg["aria-labelledby"]=eo&&!ev?er:null);let ey={...eg,...N,...I.props,className:(0,n.Z)(N.className,I.props.className),onTouchStart:ed,ref:em,...T?{onMouseMove:e=>{let t=I.props;t.onMouseMove&&t.onMouseMove(e),eY={x:e.clientX,y:e.clientY},eh.current&&eh.current.update()}}:{}},eb={};x||(ey.onTouchStart=e=>{ed(e),K.clear(),G.clear(),ei(),en.current=document.body.style.WebkitUserSelect,document.body.style.WebkitUserSelect="none",Q.start(E,()=>{document.body.style.WebkitUserSelect=en.current,es(e)})},ey.onTouchEnd=e=>{I.props.onTouchEnd&&I.props.onTouchEnd(e),ei(),K.start(M,()=>{ep(e)})}),b||(ey.onMouseOver=e_(es,ey.onMouseOver),ey.onMouseLeave=e_(el,ey.onMouseLeave),_||(eb.onMouseOver=es,eb.onMouseLeave=el)),y||(ey.onFocus=e_(eu,ey.onFocus),ey.onBlur=e_(ec,ey.onBlur),_||(eb.onFocus=eu,eb.onBlur=ec));let ew={...o,isRtl:V,arrow:a,disableInteractive:_,placement:S,PopperComponentProp:k,touch:Y.current},ex="function"==typeof D.popper?D.popper(ew):D.popper,eO=r.useMemo(()=>{let e=[{name:"arrow",enabled:!!z,options:{element:z,padding:4}}];return W.popperOptions?.modifiers&&(e=e.concat(W.popperOptions.modifiers)),ex?.popperOptions?.modifiers&&(e=e.concat(ex.popperOptions.modifiers)),{...W.popperOptions,...ex?.popperOptions,modifiers:e}},[z,W.popperOptions,ex?.popperOptions]),eR=eF(ew),eE="function"==typeof D.transition?D.transition(ew):D.transition,eT={slots:{popper:d.Popper,transition:d.Transition??H,tooltip:d.Tooltip,arrow:d.Arrow,...B},slotProps:{arrow:D.arrow??h.arrow,popper:{...W,...ex??h.popper},tooltip:D.tooltip??h.tooltip,transition:{...C,...eE??h.transition}}},[ej,eM]=(0,eC.Z)("popper",{elementType:eV,externalForwardedProps:eT,ownerState:ew,className:(0,n.Z)(eR.popper,W?.className)}),[eZ,eA]=(0,eC.Z)("transition",{elementType:g.Z,externalForwardedProps:eT,ownerState:ew}),[eL,eS]=(0,eC.Z)("tooltip",{elementType:eq,className:eR.tooltip,externalForwardedProps:eT,ownerState:ew}),[ek,eN]=(0,eC.Z)("arrow",{elementType:eU,className:eR.arrow,externalForwardedProps:eT,ownerState:ew,ref:X});return(0,eP.jsxs)(r.Fragment,{children:[r.cloneElement(I,ey),(0,eP.jsx)(ej,{as:k??eW,placement:S,anchorEl:T?{getBoundingClientRect:()=>({top:eY.y,left:eY.x,right:eY.x,bottom:eY.y,width:0,height:0})}:q,popperRef:eh,open:!!q&&eo,id:er,transition:!0,...eb,...eM,popperOptions:eO,children:({TransitionProps:e})=>(0,eP.jsx)(eZ,{timeout:F.transitions.duration.shorter,...e,...eA,children:(0,eP.jsxs)(eL,{...eS,children:[$,a?(0,eP.jsx)(ek,{...eN}):null]})})})]})})}};