exports.id=5528,exports.ids=[5528],exports.modules={82400:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n});var o=r(51426),a=r(10326);let n=(0,o.Z)((0,a.jsx)("path",{d:"m17 7-1.41 1.41L18.17 11H8v2h10.17l-2.58 2.58L17 17l5-5zM4 5h8V3H4c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h8v-2H4z"}),"Logout")},33198:(e,t,r)=>{"use strict";r.d(t,{Z:()=>b});var o=r(17577),a=r(41135),n=r(88634),l=r(91703),i=r(13643),s=r(2791),c=r(51426),d=r(10326);let m=(0,c.Z)((0,d.jsx)("path",{d:"M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"}),"Person");var p=r(71685),u=r(97898);function f(e){return(0,u.ZP)("MuiAvatar",e)}(0,p.Z)("MuiAvatar",["root","colorDefault","circular","rounded","square","img","fallback"]);var h=r(31121);let x=e=>{let{classes:t,variant:r,colorDefault:o}=e;return(0,n.Z)({root:["root",r,o&&"colorDefault"],img:["img"],fallback:["fallback"]},f,t)},y=(0,l.default)("div",{name:"MuiAvatar",slot:"Root",overridesResolver:(e,t)=>{let{ownerState:r}=e;return[t.root,t[r.variant],r.colorDefault&&t.colorDefault]}})((0,i.Z)(({theme:e})=>({position:"relative",display:"flex",alignItems:"center",justifyContent:"center",flexShrink:0,width:40,height:40,fontFamily:e.typography.fontFamily,fontSize:e.typography.pxToRem(20),lineHeight:1,borderRadius:"50%",overflow:"hidden",userSelect:"none",variants:[{props:{variant:"rounded"},style:{borderRadius:(e.vars||e).shape.borderRadius}},{props:{variant:"square"},style:{borderRadius:0}},{props:{colorDefault:!0},style:{color:(e.vars||e).palette.background.default,...e.vars?{backgroundColor:e.vars.palette.Avatar.defaultBg}:{backgroundColor:e.palette.grey[400],...e.applyStyles("dark",{backgroundColor:e.palette.grey[600]})}}}]}))),g=(0,l.default)("img",{name:"MuiAvatar",slot:"Img",overridesResolver:(e,t)=>t.img})({width:"100%",height:"100%",textAlign:"center",objectFit:"cover",color:"transparent",textIndent:1e4}),v=(0,l.default)(m,{name:"MuiAvatar",slot:"Fallback",overridesResolver:(e,t)=>t.fallback})({width:"75%",height:"75%"}),b=o.forwardRef(function(e,t){let r=(0,s.i)({props:e,name:"MuiAvatar"}),{alt:n,children:l,className:i,component:c="div",slots:m={},slotProps:p={},imgProps:u,sizes:f,src:b,srcSet:j,variant:S="circular",...N}=r,C=null,Z={...r,component:c,variant:S},k=function({crossOrigin:e,referrerPolicy:t,src:r,srcSet:a}){let[n,l]=o.useState(!1);return o.useEffect(()=>{if(!r&&!a)return;l(!1);let o=!0,n=new Image;return n.onload=()=>{o&&l("loaded")},n.onerror=()=>{o&&l("error")},n.crossOrigin=e,n.referrerPolicy=t,n.src=r,a&&(n.srcset=a),()=>{o=!1}},[e,t,r,a]),n}({...u,..."function"==typeof p.img?p.img(Z):p.img,src:b,srcSet:j}),w=b||j,z=w&&"error"!==k;Z.colorDefault=!z,delete Z.ownerState;let A=x(Z),[E,R]=(0,h.Z)("img",{className:A.img,elementType:g,externalForwardedProps:{slots:m,slotProps:{img:{...u,...p.img}}},additionalProps:{alt:n,src:b,srcSet:j,sizes:f},ownerState:Z});return C=z?(0,d.jsx)(E,{...R}):l||0===l?l:w&&n?n[0]:(0,d.jsx)(v,{ownerState:Z,className:A.fallback}),(0,d.jsx)(y,{as:c,className:(0,a.Z)(A.root,i),ref:t,...N,ownerState:Z,children:C})})},63948:(e,t)=>{"use strict";var r=Symbol.for("react.element"),o=(Symbol.for("react.portal"),Symbol.for("react.fragment"),Symbol.for("react.strict_mode"),Symbol.for("react.profiler"),Symbol.for("react.provider"),Symbol.for("react.context"),Symbol.for("react.forward_ref"),Symbol.for("react.suspense"),Symbol.for("react.memo"),Symbol.for("react.lazy"),Symbol.iterator,{isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}}),a=Object.assign,n={};function l(e,t,r){this.props=e,this.context=t,this.refs=n,this.updater=r||o}function i(){}function s(e,t,r){this.props=e,this.context=t,this.refs=n,this.updater=r||o}l.prototype.isReactComponent={},l.prototype.setState=function(e,t){if("object"!=typeof e&&"function"!=typeof e&&null!=e)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")},l.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")},i.prototype=l.prototype;var c=s.prototype=new i;c.constructor=s,a(c,l.prototype),c.isPureReactComponent=!0;var d=Object.prototype.hasOwnProperty,m={current:null},p={key:!0,ref:!0,__self:!0,__source:!0};t.createElement=function(e,t,o){var a,n={},l=null,i=null;if(null!=t)for(a in void 0!==t.ref&&(i=t.ref),void 0!==t.key&&(l=""+t.key),t)d.call(t,a)&&!p.hasOwnProperty(a)&&(n[a]=t[a]);var s=arguments.length-2;if(1===s)n.children=o;else if(1<s){for(var c=Array(s),u=0;u<s;u++)c[u]=arguments[u+2];n.children=c}if(e&&e.defaultProps)for(a in s=e.defaultProps)void 0===n[a]&&(n[a]=s[a]);return{$$typeof:r,type:e,key:l,ref:i,props:n,_owner:m.current}}},95746:(e,t,r)=>{"use strict";e.exports=r(63948)},42419:(e,t,r)=>{"use strict";r.d(t,{Z:()=>l});var o=r(10326),a=r(17577),n=r(38492);r(94638),r(39613);let l=({isOpen:e,onClose:t,onSuccess:r})=>{let[l,i]=(0,a.useState)({companyName:"",adminName:"",adminEmail:"",industry:"",location:"",companySize:""}),[s,c]=(0,a.useState)(!1),[d,m]=(0,a.useState)(""),p=e=>{let{name:t,value:r}=e.target;i(e=>({...e,[t]:r})),m("")},u=async e=>{e.preventDefault(),c(!0),m("");try{throw Error("User not authenticated")}catch(e){m(e instanceof Error?e.message:"An error occurred")}finally{c(!1)}};return e?o.jsx("div",{className:"modal-overlay",children:(0,o.jsxs)("div",{className:"modal-container",children:[(0,o.jsxs)("div",{className:"modal-header",children:[(0,o.jsxs)("div",{className:"modal-title",children:[o.jsx(n.$xp,{size:24}),o.jsx("h2",{children:"Add New Employer Group"})]}),o.jsx("button",{className:"close-button",onClick:t,children:o.jsx(n.fMW,{size:24})})]}),(0,o.jsxs)("form",{onSubmit:u,className:"modal-form",children:[(0,o.jsxs)("div",{className:"form-grid",children:[(0,o.jsxs)("div",{className:"form-group",children:[o.jsx("label",{htmlFor:"companyName",children:"Company Name *"}),o.jsx("input",{type:"text",id:"companyName",name:"companyName",value:l.companyName,onChange:p,placeholder:"Enter company name",required:!0})]}),(0,o.jsxs)("div",{className:"form-group",children:[o.jsx("label",{htmlFor:"adminName",children:"Admin Name *"}),o.jsx("input",{type:"text",id:"adminName",name:"adminName",value:l.adminName,onChange:p,placeholder:"Enter admin full name",required:!0})]}),(0,o.jsxs)("div",{className:"form-group",children:[o.jsx("label",{htmlFor:"adminEmail",children:"Admin Email *"}),o.jsx("input",{type:"email",id:"adminEmail",name:"adminEmail",value:l.adminEmail,onChange:p,placeholder:"<EMAIL>",required:!0})]}),(0,o.jsxs)("div",{className:"form-group",children:[o.jsx("label",{htmlFor:"industry",children:"Industry"}),(0,o.jsxs)("select",{id:"industry",name:"industry",value:l.industry,onChange:p,children:[o.jsx("option",{value:"",children:"Select industry"}),["Technology","Healthcare","Finance","Manufacturing","Retail","Education","Construction","Real Estate","Transportation","Hospitality","Other"].map(e=>o.jsx("option",{value:e,children:e},e))]})]}),(0,o.jsxs)("div",{className:"form-group",children:[o.jsx("label",{htmlFor:"location",children:"Location"}),o.jsx("input",{type:"text",id:"location",name:"location",value:l.location,onChange:p,placeholder:"City, State"})]}),(0,o.jsxs)("div",{className:"form-group",children:[o.jsx("label",{htmlFor:"companySize",children:"Company Size"}),(0,o.jsxs)("select",{id:"companySize",name:"companySize",value:l.companySize,onChange:p,children:[o.jsx("option",{value:"",children:"Select company size"}),["1-10 employees","11-50 employees","51-200 employees","201-500 employees","501-1000 employees","1000+ employees"].map(e=>o.jsx("option",{value:e,children:e},e))]})]})]}),d&&o.jsx("div",{className:"error-message",children:d}),(0,o.jsxs)("div",{className:"form-actions",children:[o.jsx("button",{type:"button",className:"cancel-button",onClick:t,disabled:s,children:"Cancel"}),o.jsx("button",{type:"submit",className:"submit-button",disabled:!(l.companyName.trim()&&l.adminName.trim()&&l.adminEmail.trim()&&l.adminEmail.includes("@"))||s,children:s?"Adding...":"Add Company"})]})]}),o.jsx("div",{className:"modal-note",children:(0,o.jsxs)("p",{children:[o.jsx("strong",{children:"Note:"})," The admin will receive an email invitation to set up their account and complete the company onboarding process."]})})]})}):null}},67925:(e,t,r)=>{"use strict";r.d(t,{Z:()=>y});var o=r(10326),a=r(17577),n=r(6283),l=r(25609),i=r(33198),s=r(42265),c=r(82400),d=r(46226),m=r(22758),p=r(31870),u=r(25842),f=r(30656),h=r(88563),x=r(35047);let y=()=>{let e=(0,x.useRouter)(),{logout:t}=(0,m.a)(),r=(0,p.C)(e=>e.user.userProfile),[y,g]=(0,a.useState)(!1);(0,a.useEffect)(()=>{g("true"===localStorage.getItem("isTeamsApp1"))},[]);let v=(0,u.v9)(e=>e.user.userProfile.isAdmin),b=(0,u.v9)(e=>e.user.userProfile.isBroker);return o.jsx(n.Z,{sx:{bgcolor:"white",padding:2,textAlign:"center",height:"80px",borderBottom:"1px solid #D2D2D2",position:"sticky",top:0,zIndex:50,boxShadow:"0 1px 3px 0 rgba(0, 0, 0, 0.1)"},children:(0,o.jsxs)(n.Z,{sx:{display:"flex",alignItems:"center",justifyContent:"space-between"},children:[(0,o.jsxs)(n.Z,{sx:{display:"flex",alignItems:"center"},children:[(0,o.jsxs)(n.Z,{sx:{display:"flex",alignItems:"center",cursor:"pointer",mr:3},onClick:()=>{e.push("/dashboard")},children:[o.jsx(d.default,{src:f.Z,alt:"BenOsphere Logo",width:40,height:40}),o.jsx(l.Z,{sx:{fontWeight:800,fontSize:"1.5rem",ml:1,color:"#111827"},children:"BenOsphere"})]}),o.jsx(i.Z,{sx:{bgcolor:"black",color:"#ffffff",width:35,height:35,fontSize:"1.2rem",mr:1.5,ml:2,display:"flex",alignItems:"center",justifyContent:"center",paddingBottom:"1px",fontWeight:800},children:(e=>{if(!e)return"";let[t,r]=e.split(" ");return`${t[0].toUpperCase()}${r?r[0].toUpperCase():""}`})(r.name)}),o.jsx(l.Z,{variant:"h4",sx:{mb:0,fontWeight:"bold",display:"flex",alignItems:"center",justifyContent:"flex-start",textAlign:"flex-start",fontSize:"16px",mr:1.5,color:"#111827"},children:r.name.replace(/\b\w/g,e=>e.toUpperCase())}),v&&o.jsx(n.Z,{sx:{bgcolor:"rgba(0, 0, 0, 0.06)",borderRadius:"8px",padding:"4px 8px",display:"flex",alignItems:"center",justifyContent:"center",fontWeight:"bold",fontSize:"12px",color:"#333",mr:1.5},children:"ADMIN"}),b&&o.jsx(n.Z,{sx:{bgcolor:"#f5f5f5",borderRadius:"8px",padding:"2px 8px",display:"flex",alignItems:"center",justifyContent:"center",fontWeight:"bold",fontSize:"0.9rem",color:"#333",mr:1.5},children:"BROKER"})]}),(0,o.jsxs)(n.Z,{sx:{display:"flex",alignItems:"center"},children:[!y&&(0,o.jsxs)(s.Z,{sx:{backgroundColor:"transparent",color:"#333",textTransform:"none",padding:"8px 8px",display:"flex",alignItems:"center",justifyContent:"flex-start",gap:"2px","&:hover":{backgroundColor:"transparent",color:"#555"},boxShadow:"none"},children:[o.jsx(n.Z,{sx:{mt:.5,mr:.5},children:o.jsx(h.Z,{})}),o.jsx(l.Z,{sx:{fontWeight:500,fontSize:"14px",color:"#333"},children:"Guide"})]}),!y&&(0,o.jsxs)(s.Z,{onClick:t,sx:{backgroundColor:"transparent",color:"#333",marginLeft:1,textTransform:"none",padding:"8px 16px",display:"flex",alignItems:"center",justifyContent:"flex-start",gap:"8px","&:hover":{backgroundColor:"transparent",color:"#555"},boxShadow:"none"},children:[o.jsx(c.Z,{sx:{fontSize:"18px"}}),o.jsx(l.Z,{sx:{fontWeight:500,fontSize:"14px",color:"#333"},children:"Logout"})]})]})]})})}},39613:()=>{},79762:()=>{}};