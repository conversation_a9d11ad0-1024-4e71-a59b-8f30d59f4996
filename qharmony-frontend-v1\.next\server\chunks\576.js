exports.id=576,exports.ids=[576],exports.modules={12742:(e,t,o)=>{Promise.resolve().then(o.bind(o,91703)),Promise.resolve().then(o.bind(o,37180)),Promise.resolve().then(o.bind(o,97596)),Promise.resolve().then(o.bind(o,23743)),Promise.resolve().then(o.bind(o,54117)),Promise.resolve().then(o.bind(o,36990)),Promise.resolve().then(o.bind(o,42442)),Promise.resolve().then(o.bind(o,35785)),Promise.resolve().then(o.bind(o,94978)),Promise.resolve().then(o.bind(o,96830)),Promise.resolve().then(o.bind(o,90)),Promise.resolve().then(o.bind(o,48843)),Promise.resolve().then(o.bind(o,47782)),Promise.resolve().then(o.bind(o,93244)),Promise.resolve().then(o.bind(o,15657)),Promise.resolve().then(o.bind(o,53394)),Promise.resolve().then(o.bind(o,54347)),Promise.resolve().then(o.bind(o,88197)),Promise.resolve().then(o.bind(o,6333)),Promise.resolve().then(o.bind(o,15863)),Promise.resolve().then(o.bind(o,22758)),Promise.resolve().then(o.bind(o,78748)),Promise.resolve().then(o.bind(o,68186))},55759:(e,t,o)=>{Promise.resolve().then(o.t.bind(o,12994,23)),Promise.resolve().then(o.t.bind(o,96114,23)),Promise.resolve().then(o.t.bind(o,9727,23)),Promise.resolve().then(o.t.bind(o,79671,23)),Promise.resolve().then(o.t.bind(o,41868,23)),Promise.resolve().then(o.t.bind(o,84759,23))},53148:(e,t,o)=>{"use strict";o.d(t,{$R:()=>c,A_:()=>i,BO:()=>r,GH:()=>m,_n:()=>a,be:()=>s,iG:()=>d,j0:()=>l});var n=o(44099);let a="http://localhost:8080",r="<EMAIL>,<EMAIL>,<EMAIL>".split(",").map(e=>e.trim()),s=n.Z.create({baseURL:a});async function i(e,t,o){let n=new URL(o?`${o}${e}`:`${a}${e}`);return t&&Object.keys(t).forEach(e=>n.searchParams.append(e,t[e])),(await s.get(n.toString())).data}async function l(e,t,o){let n=o?`${o}${e}`:`${a}${e}`,r=await s.post(n,t,{headers:{"Content-Type":"application/json"}});return{status:r.status,data:r.data}}async function d(e,t,o){let n=o?`${o}${e}`:`${a}${e}`;console.log(`Document upload to: ${n}`);let r=await s.post(n,t,{headers:{"Content-Type":"multipart/form-data"}});return{status:r.status,data:r.data}}async function c(e,t,o){let n=new URL(o?`${o}${e}`:`${a}${e}`);return t&&Object.keys(t).forEach(e=>n.searchParams.append(e,t[e])),console.log(`GET Blob request to: ${n.toString()}`),(await s.get(n.toString(),{responseType:"blob"})).data}async function m(e,t,o){let n=o?`${o}${e}`:`${a}${e}`,r=await s.put(n,t,{headers:{"Content-Type":"application/json"}});return{status:r.status,data:r.data}}s.interceptors.request.use(e=>{let t=localStorage.getItem("userid1")||localStorage.getItem("userId");return t?e.headers["user-id"]=t:console.warn("No user ID found in localStorage for API request"),e})},22758:(e,t,o)=>{"use strict";o.d(t,{AuthProvider:()=>b,a:()=>f});var n=o(10326),a=o(23371),r=o(17577),s=o(26472),i=o(35957),l=o(35611),d=o(24047);let c={auth:{clientId:"08e8620a-5979-4a37-b279-b2a92a75f515",authority:"https://login.microsoftonline.com/ca41443d-acdd-4223-9c81-dcaeb58b3406",redirectUri:"https://app.benosphere.com/teams-landing",navigateToLoginRequestUrl:!1},cache:{cacheLocation:"sessionStorage",storeAuthStateInCookie:!1},system:{allowRedirectInIframe:!0,loggerOptions:{loggerCallback:(e,t)=>{console.log(t)},logLevel:l.i.Verbose}}},m=new d.Lx(c);var u=o(59423),p=o(35047);let g=(0,r.createContext)(void 0),b=({children:e})=>{let[t,o]=(0,r.useState)(null),[l,d]=(0,r.useState)(!0),c=(0,p.useRouter)(),b=()=>{Object.keys(localStorage).forEach(e=>{localStorage.removeItem(e)}),localStorage.removeItem("userid1"),localStorage.removeItem("userEmail1"),localStorage.removeItem("isTeamsApp1"),localStorage.removeItem("companyId1"),localStorage.removeItem("firstTimeLogin1"),localStorage.removeItem("wellness_results"),localStorage.removeItem("wellness_user_answers"),console.log("All localStorage items cleared")},f=()=>{document.cookie.split(";").forEach(e=>{let t=e.split("=")[0].trim();document.cookie=`${t}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;`}),console.log("All cookies cleared")};return(0,r.useEffect)(()=>{let e=(0,i.Aj)(s.I,e=>{if(e)console.log("Firebase user exists:",e),o(e);else{let e=localStorage.getItem("ssoDone1"),t=localStorage.getItem("userid1");console.log("isSSODone",e),console.log("userid1",t),a.j2().then(async()=>{let e=await a.fw();console.log("Current user teams context:",e.user?.loginHint);let t=e.user?.loginHint,n=e.user?.tenant?.id;localStorage.setItem("userEmail1",t),localStorage.setItem("isTeamsApp1","true");let r=await (0,u.N8)(t,n);if("login_user"===r.data){console.log("Onboarding successful:",r);let t=r.userId,n=r.companyId;localStorage.setItem("userid1",t),localStorage.setItem("companyId1",n),localStorage.setItem("ssoDone1","true"),o(e.user?.loginHint),c.push("/dashboard")}else c.push("/teams-landing")})}d(!1)});return()=>e()},[]),n.jsx(g.Provider,{value:{user:t,loading:l,logout:()=>{(0,i.w7)(s.I).then(()=>{console.log("Firebase user signed out"),o(null),b(),f(),c.push("/login")}).catch(e=>{console.error("Error signing out: ",e)}),m.logoutRedirect().catch(e=>{console.error("Error signing out from Microsoft: ",e)})},setUser:o},children:e})},f=()=>{let e=(0,r.useContext)(g);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}},59423:(e,t,o)=>{"use strict";o.d(t,{Ig:()=>i,N8:()=>s,lY:()=>a,selfOnboard:()=>r});var n=o(53148);async function a(e){return(await (0,n.j0)("/auth/parse-params",{link:e})).data}async function r(e){return(await (0,n.j0)("/user/self-onboard",{userEmail:e})).data.data}async function s(e,t){return(await (0,n.j0)("/teams/user/self-onboard",{userEmail:e,tenantId:t})).data}async function i(e,t){let o=await (0,n.j0)("/employee/onboard",{companyId:e,userId:t});return console.log("Response from onboardEmployee:",o),o.data}},78748:(e,t,o)=>{"use strict";o.d(t,{StoreProvider:()=>y});var n=o(10326),a=o(25842),r=o(8860),s=o(32049),i=o(39352),l=o(25748),d=o(59028);let c=(0,r.oM)({name:"onboarding",initialState:{additionalParams:{isAdmin:!1},userDetails:{email:"",name:"",role:"",isAdmin:!1,isBroker:!1,isActivated:!1},companyDetails:{name:"",adminEmail:"",adminRole:"",companySize:0,industry:"",location:"",website:"",howHeard:"",brokerId:"",brokerageId:"",isBrokerage:!1,isActivated:!1}},reducers:{setUserDetails:(e,t)=>{e.userDetails=t.payload},setCompanyDetails:(e,t)=>{e.companyDetails=t.payload},setAdditionalParams:(e,t)=>{e.additionalParams=t.payload}}}),{setUserDetails:m,setCompanyDetails:u,setAdditionalParams:p}=c.actions,g=c.reducer;var b=o(56608);let f=(0,r.xC)({reducer:{user:s.ZP,company:i.ZP,benefits:l.ZP,qHarmonyBot:d.ZP,onboarding:g,mobileSidebarToggle:b.ZP}});function y({children:e}){return n.jsx(a.zt,{store:f,children:e})}},25748:(e,t,o)=>{"use strict";o.d(t,{D7:()=>i,F5:()=>s,H_:()=>p,MJ:()=>b,Sn:()=>h,US:()=>a,Yb:()=>c,Yw:()=>f,ZP:()=>S,d8:()=>v,iH:()=>g,nM:()=>y,oQ:()=>r,oT:()=>P});let n=(0,o(8860).oM)({name:"benefits",initialState:{benefitsPerType:[],documentsPerBenefit:{benefitId:"",documents:[],links:[]},viewableDocuments:[],loadingDocuments:[],snackbarMessage:""},reducers:{setAllBenefitsPerType:(e,t)=>{e.benefitsPerType=t.payload},upsertBenefitsPerType:(e,t)=>{let{benefitType:o,benefits:n}=t.payload,a=e.benefitsPerType.findIndex(e=>e.benefitType===o);-1!==a?e.benefitsPerType[a]={benefitType:o,benefits:n}:e.benefitsPerType.push({benefitType:o,benefits:n})},setDocumentsPerBenefit:(e,t)=>{e.documentsPerBenefit=t.payload},setViewableDocuments:(e,t)=>{let o=t.payload.filter(t=>!e.viewableDocuments.some(e=>e.documentObjectKey===t.documentObjectKey));e.viewableDocuments=[...e.viewableDocuments,...o]},addViewableDocument:(e,t)=>{e.viewableDocuments.push(t.payload)},clearViewableDocuments:e=>{e.viewableDocuments=[]},clearBenefitsState:e=>{e.benefitsPerType=[],e.documentsPerBenefit={benefitId:"",documents:[],links:[]},e.viewableDocuments=[],e.loadingDocuments=[]},setLoadingDocument:(e,t)=>{e.loadingDocuments.push(t.payload)},clearLoadingDocument:(e,t)=>{e.loadingDocuments=e.loadingDocuments.filter(e=>e!==t.payload)},addDocument:(e,t)=>{let{benefitId:o,document:n}=t.payload;e.documentsPerBenefit.benefitId===o&&(e.documentsPerBenefit.documents=[...e.documentsPerBenefit.documents,n])},deleteDocument:(e,t)=>{let{benefitId:o,document:n}=t.payload;e.documentsPerBenefit.benefitId===o&&(e.documentsPerBenefit.documents=e.documentsPerBenefit.documents.filter(e=>e!==n),e.viewableDocuments=e.viewableDocuments.filter(e=>e.documentObjectKey!==n))},addLink:(e,t)=>{let{benefitId:o,link:n}=t.payload;e.documentsPerBenefit.benefitId===o&&(e.documentsPerBenefit.links=[...e.documentsPerBenefit.links,n])},deleteLink:(e,t)=>{let{benefitId:o,link:n}=t.payload;console.log("DELETE LINK REDUCER: ",e.documentsPerBenefit.benefitId),e.documentsPerBenefit.benefitId===o&&(e.documentsPerBenefit.links=e.documentsPerBenefit.links.filter(e=>e!==n))},setSnackbarMessage:(e,t)=>{e.snackbarMessage=t.payload},clearSnackbarMessage:e=>{e.snackbarMessage=""}}}),{setAllBenefitsPerType:a,upsertBenefitsPerType:r,setDocumentsPerBenefit:s,setViewableDocuments:i,addViewableDocument:l,clearViewableDocuments:d,clearBenefitsState:c,setLoadingDocument:m,clearLoadingDocument:u,addDocument:p,deleteDocument:g,addLink:b,deleteLink:f,setSnackbarMessage:y,clearSnackbarMessage:h}=n.actions,v=(e,t)=>{for(let{benefitType:o,benefits:n}of e.benefits.benefitsPerType){let e=n.find(e=>e._id===t);if(e)return{benefitType:o,benefit:e}}return null},P=(e,t)=>e.benefits.benefitsPerType.find(e=>e.benefitType===t)||null,S=n.reducer},39352:(e,t,o)=>{"use strict";o.d(t,{Vv:()=>r,ZP:()=>i,sy:()=>s,x7:()=>a});let n=(0,o(8860).oM)({name:"company",initialState:{companyBenefitTypes:[],companyTeamMembers:[],companyDetails:{_id:"",name:"",companySize:0,industry:"",location:"",website:"",adminEmail:"",adminRole:"",brokerId:"",brokerageId:"",isBrokerage:!1,isActivated:!1,howHeard:"",details:{logo:""},__v:0}},reducers:{setCompanyBenefitTypes:(e,t)=>{e.companyBenefitTypes=t.payload},setCompanyTeamMembers:(e,t)=>{e.companyTeamMembers=t.payload},setCompanyDetails:(e,t)=>{console.log("COMPANY DETAILS PAYLOAD: ",t.payload),e.companyDetails=t.payload}}}),{setCompanyBenefitTypes:a,setCompanyTeamMembers:r,setCompanyDetails:s}=n.actions,i=n.reducer},56608:(e,t,o)=>{"use strict";o.d(t,{FJ:()=>a,ZP:()=>i,dL:()=>r});let n=(0,o(8860).oM)({name:"drawer",initialState:{isOpen:!1},reducers:{openDrawer:e=>{e.isOpen=!0},closeDrawer:e=>{e.isOpen=!1},toggleDrawer:e=>{e.isOpen=!e.isOpen}}}),{openDrawer:a,closeDrawer:r,toggleDrawer:s}=n.actions,i=n.reducer},59028:(e,t,o)=>{"use strict";o.d(t,{Hz:()=>a,ZP:()=>i,wt:()=>s});let n=(0,o(8860).oM)({name:"qHarmonyBot",initialState:{chatHistory:[],isLoading:!1},reducers:{addMessage:(e,t)=>{let{sender:o,message:n,timestamp:a}=t.payload;if(console.log("Adding message:",t.payload),"bot"===o&&e.chatHistory.length>0){let t=e.chatHistory[e.chatHistory.length-1];if("bot"===t.sender&&!t.timestamp.includes("Done")){t.message+=n,t.timestamp=a;return}}e.chatHistory.push(t.payload)},clearChatHistory:e=>{e.chatHistory=[]},setIsLoading:(e,t)=>{e.isLoading=t.payload}}}),{addMessage:a,clearChatHistory:r,setIsLoading:s}=n.actions,i=n.reducer},32049:(e,t,o)=>{"use strict";o.d(t,{$l:()=>r,CS:()=>l,MP:()=>m,Re:()=>i,Ym:()=>c,ZP:()=>u,ki:()=>d,v2:()=>s});let n=(0,o(8860).oM)({name:"user",initialState:{_id:"",userProfile:{name:"",email:"",companyId:"",role:"",isAdmin:!1,isBroker:!1,details:{phoneNumber:void 0,department:void 0,title:void 0,role:void 0,dateOfBirth:void 0,hireDate:void 0,annualSalary:void 0,employeeClassType:"",customPayrollFrequency:"",ssn:"",address:void 0,mailingAddress:void 0,dependents:[],emergencyContact:void 0,employeeId:"",managerId:"",workLocation:"",workSchedule:"",ein:""}},selectedBenefitType:"",selectedBenefitId:"",selectedFAQQuestion:"",managedCompanies:[]},reducers:{setUserId:(e,t)=>{e._id=t.payload},setUserProfile:(e,t)=>{let{name:o,email:n,companyId:a,role:r,isAdmin:s,isBroker:i,details:l}=t.payload;e.userProfile={name:o,email:n,companyId:a,role:r,isAdmin:s,isBroker:i,details:l||e.userProfile.details}},setSelectedBenefitType:(e,t)=>{console.log("action.payload",t.payload),e.selectedBenefitType=t.payload},setSelectedBenefitId:(e,t)=>{e.selectedBenefitId=t.payload},setSelectedFAQQuestion:(e,t)=>{e.selectedFAQQuestion=t.payload},clearSelectedFAQQuestion:e=>{e.selectedFAQQuestion=""},setManagedCompanies:(e,t)=>{e.managedCompanies=t.payload}}}),{setUserId:a,setUserProfile:r,setSelectedBenefitType:s,setSelectedBenefitId:i,setSelectedFAQQuestion:l,clearSelectedFAQQuestion:d,setManagedCompanies:c}=n.actions,m=e=>e.user.userProfile.companyId,u=n.reducer},68186:(e,t,o)=>{"use strict";o.d(t,{default:()=>a});var n=o(5283);o(98367);let a=(0,n.Z)({breakpoints:{values:{xs:0,sm:600,md:768,lg:1200,xl:1536}},typography:{fontFamily:"SF Pro",logoTitle:{fontWeight:800,fontSize:"1.5rem",lineHeight:"1.2"},viewBenefitTypeSectionHeading:{fontWeight:"500",fontSize:"28px",lineHeight:"20.8px",color:"black",textAlign:"left",marginBottom:4,marginTop:5},toggleViewBenefitSubType:{fontWeight:"500",fontSize:"17px",lineHeight:"20.8px",color:"black",textAlign:"left"}},chip:{benefitStatusAvailableChip:{bgcolor:"#67BA6B1F",color:"#67BA6B",borderRadius:"8px","& .MuiChip-label":{padding:1,fontWeight:"semibold",fontSize:"14px"}},benefitStatusDisabledChip:{bgcolor:"#f0f0f0",color:"black",borderRadius:"8px","& .MuiChip-label":{padding:1,fontWeight:"semibold",fontSize:"14px"}}},button:{editBenefitButton:{backgroundColor:"#f0f0f0",borderRadius:"8px",textTransform:"none",color:"#000",padding:"4px",marginRight:3,boxShadow:"none",border:"none","&:hover":{backgroundColor:"#E0E0E0",boxShadow:"none"}}},palette:{backgroundBlue:{main:"#1073ff"},primary:{main:"#1073ff"},secondary:{main:"#ff4081"}}})},26472:(e,t,o)=>{"use strict";o.d(t,{I:()=>s});var n=o(42585),a=o(35957);let r=(0,n.C6)().length?(0,n.C6)()[0]:(0,n.ZF)({apiKey:"AIzaSyAOalm98qF_u74s3qQlIqQ5A6IpWyd0wKA",authDomain:"qharmony-dev.firebaseapp.com",projectId:"qharmony-dev",storageBucket:"qharmony-dev.appspot.com",messagingSenderId:"756187162353",appId:"1:756187162353:web:3fc7d63dee1c57bc9d6b50"}),s=(0,a.v0)(r)},33709:(e,t,o)=>{"use strict";o.r(t),o.d(t,{default:()=>c,metadata:()=>d});var n=o(19510),a=o(18537),r=o(68570);let s=(0,r.createProxy)(String.raw`C:\beno\project_dev\qharmony-frontend-v1\src\redux\StoreProvider.tsx#StoreProvider`),i=(0,r.createProxy)(String.raw`C:\beno\project_dev\qharmony-frontend-v1\src\theme.js#default`),l=(0,r.createProxy)(String.raw`C:\beno\project_dev\qharmony-frontend-v1\src\components\AuthContext.tsx#AuthProvider`);(0,r.createProxy)(String.raw`C:\beno\project_dev\qharmony-frontend-v1\src\components\AuthContext.tsx#useAuth`),o(5023);let d={title:"BenOsphere",description:"AI-Powered Benefits Experience Platform"};function c({children:e}){return n.jsx("html",{lang:"en",children:n.jsx("body",{style:{backgroundColor:"black"},children:n.jsx(a.Z,{theme:i,children:n.jsx(s,{children:n.jsx(l,{children:e})})})})})}},98367:()=>{},5023:()=>{}};