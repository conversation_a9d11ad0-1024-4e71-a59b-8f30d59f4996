"use strict";exports.id=6305,exports.ids=[6305],exports.modules={88563:(e,a,t)=>{t.d(a,{Z:()=>i});var n,o=t(95746);function r(){return(r=Object.assign?Object.assign.bind():function(e){for(var a=1;a<arguments.length;a++){var t=arguments[a];for(var n in t)({}).hasOwnProperty.call(t,n)&&(e[n]=t[n])}return e}).apply(null,arguments)}let i=function(e){return o.createElement("svg",r({xmlns:"http://www.w3.org/2000/svg",width:18,height:18,fill:"none",viewBox:"0 0 18 15"},e),n||(n=o.createElement("path",{fill:"#000",fillOpacity:.8,d:"M9 12.333q1 0 1.781-.594.782-.593 1.136-1.53H6.083a3.4 3.4 0 0 0 1.136 1.53q.78.594 1.781.594M6.917 9q.52 0 .885-.365t.365-.885-.365-.885a1.2 1.2 0 0 0-.885-.365q-.521 0-.886.365a1.2 1.2 0 0 0-.364.885q0 .52.364.885.365.365.886.365m4.166 0q.522 0 .886-.365t.364-.885q0-.52-.364-.885a1.2 1.2 0 0 0-.886-.365q-.52 0-.885.365a1.2 1.2 0 0 0-.365.885q0 .52.365.885.364.365.885.365M5.354 4.333l2.334-3.02q.25-.334.593-.49a1.72 1.72 0 0 1 1.438 0q.343.156.594.49l2.333 3.02 3.542 1.188q.54.166.854.614.312.448.312.99 0 .25-.073.5a1.6 1.6 0 0 1-.24.48l-2.29 3.25.082 3.416q.021.73-.479 1.229t-1.166.5q-.042 0-.459-.062L9 15.396l-3.73 1.041a.8.8 0 0 1-.228.052q-.125.011-.23.011-.666 0-1.166-.5a1.6 1.6 0 0 1-.48-1.23l.084-3.437L.98 8.104a1.6 1.6 0 0 1-.24-.479 1.8 1.8 0 0 1-.073-.5q0-.52.302-.969.301-.447.844-.635zm1.021 1.438L2.333 7.104l2.584 3.73-.084 3.979L9 13.667l4.167 1.166-.084-4 2.584-3.687-4.042-1.375L9 2.333z"})))}},43058:(e,a,t)=>{t.d(a,{Z:()=>u});var n=t(10326),o=t(17577),r=t(22758),i=t(35047),s=t(31870);t(32049),t(94638);var l=t(98139),d=t(6283);let c=()=>/Mobi|Android/i.test(navigator.userAgent),u=({children:e})=>{let{user:a,loading:t}=(0,r.a)(),u=(0,i.useRouter)(),m=(0,i.usePathname)(),p=(0,s.T)(),[y,g]=(0,o.useState)(!1),f=(0,s.C)(e=>e.user.userProfile);return((0,o.useEffect)(()=>{},[p,f.name]),(0,o.useEffect)(()=>{console.log("ProtectedRoute useEffect triggered"),console.log("Current user: ",a),console.log("Loading state: ",t),console.log("Current user details: ",f),t||a||(console.log("User not authenticated, redirecting to home"),g(!1),u.push("/")),!t&&f.companyId&&""===f.companyId&&(console.log("Waiting to retrieve company details"),g(!1)),!t&&f.companyId&&""!==f.companyId&&(console.log("User found, rendering children"),g(!0)),c()&&!m.startsWith("/mobile")&&(console.log(`Redirecting to mobile version of ${m}`),u.push(`/mobile${m}`))},[a,t,f,u,m]),y)?a?n.jsx(n.Fragment,{children:e}):null:n.jsx(d.Z,{sx:{display:"flex",justifyContent:"center",alignItems:"center",height:"100vh",bgcolor:"#f6f8fc"},children:n.jsx(l.Z,{})})}},94638:(e,a,t)=>{t.d(a,{G9:()=>A,JZ:()=>h,M_:()=>y,N:()=>d,Nq:()=>p,TQ:()=>u,Ur:()=>s,aE:()=>c,aK:()=>I,dA:()=>l,gt:()=>E,mb:()=>f,qB:()=>b,yu:()=>m,zX:()=>g});var n=t(53148),o=t(39352),r=t(25748),i=t(32049);function s(e){switch(e){case"Our Culture & Workplace":return"Work Policies";case"Protecting Your Income":return"Income Security";default:return e}}function l(e){switch(e){case"On-site Amenities":return"Company Handbook";case"Protecting Your Income":return"Income Security";case"Got married/divorced":return"Marriage or Divorce";case"Had a baby or adopted a baby":return"New Baby or Adoption";case"Lost your insurance":return"Loss of Insurance";default:return e}}async function d(e,a){let t=await (0,n.A_)("/benefits/benefit-types",{companyId:a});return console.log("COMPANY BENEFIT TYPES RESPONSE: ",t.benefitTypes),e((0,o.x7)(t.benefitTypes)),t.benefitTypes}async function c(e,a){let t=await (0,n.A_)("/benefits/all-benefits",{companyId:a});console.log("COMPANY BENEFIT TYPES WITH BENEFITS RESPONSE: ",t),e((0,r.US)(t.benefitsPerType))}async function u(e){let a=await (0,n.A_)("/admin/all-employees");return console.log("COMPANY TEAM MEMBERS RESPONSE: ",a),e((0,o.Vv)(a.employees)),a.employees}async function m(e,a){return console.log("ADDING USERS: ",a),await (0,n.j0)("/admin/add/employees",{employeeList:a})}async function p(e,a,t){try{console.log("\uD83D\uDD0D Debug: User being updated:",a);let e={employeeId:a,updatedDetails:{name:t.name,email:t.email,details:{phoneNumber:t.phoneNumber||"",department:t.department||"",title:t.title||"",role:t.title||""}}};return t.dateOfBirth&&(e.updatedDetails.details.dateOfBirth=t.dateOfBirth),t.hireDate&&(e.updatedDetails.details.hireDate=t.hireDate),t.annualSalary&&(e.updatedDetails.details.annualSalary=t.annualSalary),t.employeeClassType&&(e.updatedDetails.details.employeeClassType=t.employeeClassType),t.workSchedule&&(e.updatedDetails.details.workSchedule=t.workSchedule),t.ssn&&(e.updatedDetails.details.ssn=t.ssn),t.employeeId&&(e.updatedDetails.details.employeeId=t.employeeId),t.workLocation&&(e.updatedDetails.details.workLocation=t.workLocation),t.address&&(e.updatedDetails.details.address=t.address),t.mailingAddress&&(e.updatedDetails.details.mailingAddress=t.mailingAddress),t.emergencyContact&&(e.updatedDetails.details.emergencyContact=t.emergencyContact),e.updatedDetails.details.dependents=t.dependents||[],console.log("Middleware - dependents being sent:",e.updatedDetails.details.dependents),console.log("Sending PUT request with cleaned data:",e),await (0,n.GH)("/admin/update/employee",e)}catch(e){throw console.error("Error in updateUser middleware:",e),e}}async function y(e,a){let t=await (0,n.A_)("/employee",{"user-id":a});return e((0,i.$l)({name:t.currentUser.name,email:t.currentUser.email,companyId:t.currentUser.companyId,role:t.currentUser.role,isAdmin:t.currentUser.isAdmin,isBroker:t.currentUser.isBroker,details:t.currentUser.details})),t}async function g(e,a,t){let o=await (0,n.j0)("/admin/onboard",{company:{name:a.name,adminEmail:a.adminEmail,adminRole:a.adminRole,companySize:a.companySize,industry:a.industry,location:a.location,website:a.website,howHeard:a.howHeard,brokerId:a.brokerId,brokerageId:a.brokerageId,isBrokerage:a.isBrokerage,isActivated:a.isActivated,referralSource:a.referralSource,details:{logo:""}},user:{email:t.email,name:t.name,role:t.role,isAdmin:t.isAdmin,isBroker:t.isBroker,isActivated:t.isActivated}}),r=o.data.userId,i=o.data.companyId;return localStorage.setItem("userid1",r),localStorage.setItem("companyId1",i),o}async function f(e,a){return console.log("SENDING LOGIN LINK TO EMPLOYEE: ",e,a),await (0,n.j0)("/admin/send-user-login-link",{userId:e,companyId:a})}async function A(e,a,t,o){let r=await (0,n.j0)("/admin/add/employer",{brokerId:e,companyName:a,companyAdminEmail:t,companyAdminName:o});return console.log("BROKER ADDS COMPANY RESPONSE: ",r),r}async function h(e,a){return 200===(await (0,n.j0)("/employee/offboard/",{userId:e,companyId:a})).status}async function E(e,a){return await (0,n.j0)("/employee/enable/",{userId:e,companyId:a})}async function b(e,a){try{let a=await (0,n.A_)("/admin/all-companies");console.log("CLIENT COMPANIES UNDER BROKER: ",a);let t=a.companies||[];try{let e=await (0,n.A_)("/employee/company-details");console.log("BROKER'S OWN COMPANY: ",e),e.company&&e.company.isBrokerage&&!t.some(a=>a._id===e.company._id)&&(t.unshift(e.company),console.log("Added broker's own company to the list"))}catch(e){console.log("Could not fetch broker's own company (this is normal if user is not a broker):",e)}return console.log("ALL COMPANIES (CLIENT + OWN): ",t),e((0,i.Ym)(t)),{...a,companies:t}}catch(a){return console.error("Error fetching companies:",a),e((0,i.Ym)([])),{companies:[]}}}async function I(e){let a=await (0,n.A_)("/employee/company-details");return e((0,o.sy)(a.company)),a.status}},31870:(e,a,t)=>{t.d(a,{C:()=>r,T:()=>o});var n=t(25842);let o=()=>(0,n.I0)(),r=n.v9},30656:(e,a,t)=>{t.d(a,{Z:()=>n});let n={src:"/_next/static/media/logo.770bfeee.png",height:300,width:300,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAYAAADED76LAAAA9ElEQVR42i2PMUoDQRiF3z+zu7MbFTaxkfUC0St4ACWdhbbapvACCnaCpU0a09jYiQewFj2AIljY2AoJ7LKZnc3s/P6EPPiq9+DxESR359UgBHNrGxTWQTcOPwtrLwGU6n5c7ySxeQb4vWn9WS/lky50JVH6UlrVjzTFE2iebvbwmJkE37/zPLB79SHfzWIzUUph0LrqScB4qpFEdEhICxm9BeY9BYA9kxJwfTw7IEKfGUsAq06FgNlGtjUSoDTvS1mB3B/BDInoM/KhvQhd8lDb5RGz/pDLVFM+inVc1L49pbXmtmjeiOZQNCGaX4vGXQGY/wM1tG/NQnnUIwAAAABJRU5ErkJggg==",blurWidth:8,blurHeight:8}},73881:(e,a,t)=>{t.r(a),t.d(a,{default:()=>o});var n=t(66621);let o=e=>[{type:"image/x-icon",sizes:"16x16",url:(0,n.fillMetadataSegment)(".",e.params,"favicon.ico")+""}]}};