"use strict";exports.id=6971,exports.ids=[6971],exports.modules={76971:(e,t,o)=>{o.d(t,{Z:()=>$});var r=o(17577),a=o(41135),n=o(88634),i=o(44823),l=o(33662),s=o(51426),d=o(10326);let c=(0,s.Z)((0,d.jsx)("path",{d:"M19 5v14H5V5h14m0-2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2z"}),"CheckBoxOutlineBlank"),p=(0,s.Z)((0,d.jsx)("path",{d:"M19 3H5c-1.11 0-2 .9-2 2v14c0 1.1.89 2 2 2h14c1.11 0 2-.9 2-2V5c0-1.1-.89-2-2-2zm-9 14l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"}),"CheckBox"),u=(0,s.Z)((0,d.jsx)("path",{d:"M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-2 10H7v-2h10v2z"}),"IndeterminateCheckBox");var h=o(54641),m=o(27080),v=o(71685),Z=o(97898);function f(e){return(0,Z.ZP)("MuiCheckbox",e)}let b=(0,v.Z)("MuiCheckbox",["root","checked","disabled","indeterminate","colorPrimary","colorSecondary","sizeSmall","sizeMedium"]);var x=o(91703),g=o(13643),k=o(40955),y=o(2791),z=o(7467),P=o(31121);let C=e=>{let{classes:t,indeterminate:o,color:r,size:a}=e,i={root:["root",o&&"indeterminate",`color${(0,h.Z)(r)}`,`size${(0,h.Z)(a)}`]},l=(0,n.Z)(i,f,t);return{...t,...l}},w=(0,x.default)(l.Z,{shouldForwardProp:e=>(0,m.Z)(e)||"classes"===e,name:"MuiCheckbox",slot:"Root",overridesResolver:(e,t)=>{let{ownerState:o}=e;return[t.root,o.indeterminate&&t.indeterminate,t[`size${(0,h.Z)(o.size)}`],"default"!==o.color&&t[`color${(0,h.Z)(o.color)}`]]}})((0,g.Z)(({theme:e})=>({color:(e.vars||e).palette.text.secondary,variants:[{props:{color:"default",disableRipple:!1},style:{"&:hover":{backgroundColor:e.vars?`rgba(${e.vars.palette.action.activeChannel} / ${e.vars.palette.action.hoverOpacity})`:(0,i.Fq)(e.palette.action.active,e.palette.action.hoverOpacity)}}},...Object.entries(e.palette).filter((0,k.Z)()).map(([t])=>({props:{color:t,disableRipple:!1},style:{"&:hover":{backgroundColor:e.vars?`rgba(${e.vars.palette[t].mainChannel} / ${e.vars.palette.action.hoverOpacity})`:(0,i.Fq)(e.palette[t].main,e.palette.action.hoverOpacity)}}})),...Object.entries(e.palette).filter((0,k.Z)()).map(([t])=>({props:{color:t},style:{[`&.${b.checked}, &.${b.indeterminate}`]:{color:(e.vars||e).palette[t].main},[`&.${b.disabled}`]:{color:(e.vars||e).palette.action.disabled}}})),{props:{disableRipple:!1},style:{"&:hover":{"@media (hover: none)":{backgroundColor:"transparent"}}}}]}))),F=(0,d.jsx)(p,{}),R=(0,d.jsx)(c,{}),S=(0,d.jsx)(u,{}),$=r.forwardRef(function(e,t){let o=(0,y.i)({props:e,name:"MuiCheckbox"}),{checkedIcon:n=F,color:i="primary",icon:l=R,indeterminate:s=!1,indeterminateIcon:c=S,inputProps:p,size:u="medium",disableRipple:h=!1,className:m,slots:v={},slotProps:Z={},...f}=o,b=s?c:l,x=s?c:n,g={...o,disableRipple:h,color:i,indeterminate:s,size:u},k=C(g),$=Z.input??p,[j,B]=(0,P.Z)("root",{ref:t,elementType:w,className:(0,a.Z)(k.root,m),shouldForwardComponentProp:!0,externalForwardedProps:{slots:v,slotProps:Z,...f},ownerState:g,additionalProps:{type:"checkbox",icon:r.cloneElement(b,{fontSize:b.props.fontSize??u}),checkedIcon:r.cloneElement(x,{fontSize:x.props.fontSize??u}),disableRipple:h,slots:v,slotProps:{input:(0,z.Z)("function"==typeof $?$(g):$,{"data-indeterminate":s})}}});return(0,d.jsx)(j,{...B,classes:k})})},33662:(e,t,o)=>{o.d(t,{Z:()=>x});var r=o(17577),a=o(88634),n=o(54641),i=o(27080),l=o(91703),s=o(43227),d=o(65656),c=o(49006),p=o(71685),u=o(97898);function h(e){return(0,u.ZP)("PrivateSwitchBase",e)}(0,p.Z)("PrivateSwitchBase",["root","checked","disabled","input","edgeStart","edgeEnd"]);var m=o(31121),v=o(10326);let Z=e=>{let{classes:t,checked:o,disabled:r,edge:i}=e,l={root:["root",o&&"checked",r&&"disabled",i&&`edge${(0,n.Z)(i)}`],input:["input"]};return(0,a.Z)(l,h,t)},f=(0,l.default)(c.Z)({padding:9,borderRadius:"50%",variants:[{props:{edge:"start",size:"small"},style:{marginLeft:-3}},{props:({edge:e,ownerState:t})=>"start"===e&&"small"!==t.size,style:{marginLeft:-12}},{props:{edge:"end",size:"small"},style:{marginRight:-3}},{props:({edge:e,ownerState:t})=>"end"===e&&"small"!==t.size,style:{marginRight:-12}}]}),b=(0,l.default)("input",{shouldForwardProp:i.Z})({cursor:"inherit",position:"absolute",opacity:0,width:"100%",height:"100%",top:0,left:0,margin:0,padding:0,zIndex:1}),x=r.forwardRef(function(e,t){let{autoFocus:o,checked:r,checkedIcon:a,defaultChecked:n,disabled:i,disableFocusRipple:l=!1,edge:c=!1,icon:p,id:u,inputProps:h,inputRef:x,name:g,onBlur:k,onChange:y,onFocus:z,readOnly:P,required:C=!1,tabIndex:w,type:F,value:R,slots:S={},slotProps:$={},...j}=e,[B,M]=(0,s.Z)({controlled:r,default:!!n,name:"SwitchBase",state:"checked"}),O=(0,d.Z)(),H=e=>{z&&z(e),O&&O.onFocus&&O.onFocus(e)},I=e=>{k&&k(e),O&&O.onBlur&&O.onBlur(e)},E=e=>{if(e.nativeEvent.defaultPrevented)return;let t=e.target.checked;M(t),y&&y(e,t)},L=i;O&&void 0===L&&(L=O.disabled);let V="checkbox"===F||"radio"===F,N={...e,checked:B,disabled:L,disableFocusRipple:l,edge:c},T=Z(N),q={slots:S,slotProps:{input:h,...$}},[A,D]=(0,m.Z)("root",{ref:t,elementType:f,className:T.root,shouldForwardComponentProp:!0,externalForwardedProps:{...q,component:"span",...j},getSlotProps:e=>({...e,onFocus:t=>{e.onFocus?.(t),H(t)},onBlur:t=>{e.onBlur?.(t),I(t)}}),ownerState:N,additionalProps:{centerRipple:!0,focusRipple:!l,disabled:L,role:void 0,tabIndex:null}}),[G,J]=(0,m.Z)("input",{ref:x,elementType:b,className:T.input,externalForwardedProps:q,getSlotProps:e=>({onChange:t=>{e.onChange?.(t),E(t)}}),ownerState:N,additionalProps:{autoFocus:o,checked:r,defaultChecked:n,disabled:L,id:V?u:void 0,name:g,readOnly:P,required:C,tabIndex:w,type:F,..."checkbox"===F&&void 0===R?{}:{value:R}}});return(0,v.jsxs)(A,{...D,children:[(0,v.jsx)(G,{...J}),B?a:p]})})}};