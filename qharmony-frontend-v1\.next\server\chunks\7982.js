"use strict";exports.id=7982,exports.ids=[7982],exports.modules={24935:(e,t,r)=>{r.d(t,{Z:()=>l});var o=r(10326),n=r(17577),a=r(38492),i=r(10310),s=r(76924);let l=({isOpen:e,onClose:t,onSignatureComplete:r,employeeName:l="Employee"})=>{let d=(0,n.useRef)(null),u=(0,n.useRef)(null),[c,g]=(0,n.useState)(!1),[p,h]=(0,n.useState)(!1);(0,n.useEffect)(()=>{if(e&&d.current&&!u.current){let e=d.current;e.width=600,e.height=200;let t=new i.Z(e,{backgroundColor:"#ffffff",penColor:"#000000",minWidth:1,maxWidth:3,throttle:16,minDistance:5,dotSize:0,velocityFilterWeight:.7});t.addEventListener("beginStroke",()=>{g(!0)}),t.addEventListener("endStroke",()=>{g(!t.isEmpty())}),u.current=t;let r=e.getContext("2d");r&&(r.strokeStyle="#d1d5db",r.lineWidth=1,r.setLineDash([5,5]),r.beginPath(),r.moveTo(50,e.height-30),r.lineTo(e.width-50,e.height-30),r.stroke(),r.setLineDash([]))}return()=>{u.current&&!e&&(u.current.off(),u.current=null)}},[e]);let x=async()=>{if(c&&p&&u.current)try{let e=u.current.toDataURL("image/png",1),t={signature:e,timestamp:new Date().toISOString(),employeeName:l,userAgent:navigator.userAgent,ipAddress:"client-side",signatureHash:btoa(e).substring(0,32),signaturePadData:u.current.toData(),quality:1,format:"PNG"},o=btoa(Array.from(new TextEncoder().encode(JSON.stringify(t))).map(e=>String.fromCharCode(e)).join(""));localStorage.setItem("enrollmentSignature",o);let n=await (0,s.we)(o);n.success||console.warn("⚠️ Failed to save signature to database:",n.error),console.log("\uD83D\uDCDD Signature captured, stored locally and sent to database:",{timestamp:t.timestamp,employeeName:t.employeeName,signatureHash:t.signatureHash,dataSize:e.length,vectorPoints:t.signaturePadData.length}),r(o)}catch(e){console.error("❌ Error saving signature:",e),alert("Signature saved locally but failed to save to database. Please contact support if this persists.")}};return e?o.jsx("div",{style:{position:"fixed",top:0,left:0,right:0,bottom:0,backgroundColor:"rgba(0, 0, 0, 0.5)",zIndex:1e4,display:"flex",alignItems:"center",justifyContent:"center",padding:"20px"},children:(0,o.jsxs)("div",{style:{backgroundColor:"#ffffff",borderRadius:"16px",padding:"32px",maxWidth:"700px",width:"100%",maxHeight:"90vh",overflowY:"auto",boxShadow:"0 25px 50px rgba(0, 0, 0, 0.25)",position:"relative"},children:[o.jsx("button",{onClick:t,style:{position:"absolute",top:"16px",right:"16px",background:"none",border:"none",fontSize:"24px",cursor:"pointer",color:"#6b7280",padding:"4px"},children:o.jsx(a.fMW,{})}),(0,o.jsxs)("div",{style:{marginBottom:"24px"},children:[o.jsx("h2",{style:{fontSize:"24px",fontWeight:"600",color:"#111827",margin:"0 0 8px 0"},children:"Create your signature"}),o.jsx("p",{style:{color:"#6b7280",fontSize:"14px",margin:0,lineHeight:"21px"},children:"Some carriers require a hand-drawn signature. Please draw your signature in the box below."})]}),(0,o.jsxs)("div",{style:{border:"2px solid #e5e7eb",borderRadius:"8px",marginBottom:"16px",position:"relative",backgroundColor:"#ffffff"},children:[o.jsx("canvas",{ref:d,style:{width:"100%",height:"200px",cursor:"crosshair",display:"block",touchAction:"none"}}),o.jsx("button",{onClick:()=>{if(u.current){u.current.clear(),g(!1);let e=d.current;if(e){let t=e.getContext("2d");t&&(t.strokeStyle="#d1d5db",t.lineWidth=1,t.setLineDash([5,5]),t.beginPath(),t.moveTo(50,e.height-30),t.lineTo(e.width-50,e.height-30),t.stroke(),t.setLineDash([]))}}},style:{position:"absolute",top:"8px",right:"8px",background:"none",border:"none",color:"#6b7280",fontSize:"14px",cursor:"pointer",padding:"4px 8px",borderRadius:"4px",backgroundColor:"rgba(255, 255, 255, 0.9)"},children:"Clear"}),(0,o.jsxs)("div",{style:{position:"absolute",bottom:"8px",left:"50px",fontSize:"12px",color:"#9ca3af"},children:[o.jsx(a._vs,{style:{display:"inline",marginRight:"4px"}}),"Sign above this line"]})]}),o.jsx("div",{style:{backgroundColor:"#f0fdf4",border:"1px solid #bbf7d0",borderRadius:"8px",padding:"16px",marginBottom:"24px"},children:(0,o.jsxs)("label",{style:{display:"flex",alignItems:"flex-start",gap:"12px",cursor:"pointer"},children:[o.jsx("input",{type:"checkbox",checked:p,onChange:e=>h(e.target.checked),style:{marginTop:"2px",width:"16px",height:"16px"}}),(0,o.jsxs)("div",{children:[o.jsx("div",{style:{fontSize:"14px",fontWeight:"600",color:"#065f46",marginBottom:"4px"},children:"\uD83D\uDD12 SHA-256 with RSA Encryption"}),o.jsx("div",{style:{fontSize:"14px",color:"#047857",lineHeight:"20px"},children:"I understand this is a legal representation of my signature and confirms my enrollment selections."})]})]})}),(0,o.jsxs)("div",{style:{display:"flex",gap:"12px",justifyContent:"flex-end"},children:[o.jsx("button",{onClick:t,style:{padding:"12px 24px",backgroundColor:"white",border:"1px solid #d1d5db",borderRadius:"8px",color:"#374151",cursor:"pointer",fontWeight:"500",fontSize:"14px"},children:"Cancel"}),(0,o.jsxs)("button",{onClick:x,disabled:!c||!p,style:{padding:"12px 24px",backgroundColor:c&&p?"#2563eb":"#9ca3af",border:"none",borderRadius:"8px",color:"white",cursor:c&&p?"pointer":"not-allowed",fontWeight:"500",fontSize:"14px",display:"flex",alignItems:"center",gap:"8px"},children:["Next",o.jsx("span",{style:{fontSize:"14px"},children:"→"})]})]})]})}):null}},76924:(e,t,r)=>{r.d(t,{C4:()=>s,YN:()=>l,we:()=>u});let o=()=>"http://localhost:8080",n=()=>localStorage.getItem("userid1")||localStorage.getItem("userId")||"",a=()=>({"Content-Type":"application/json","user-id":n()}),i=async e=>{try{let t=o(),r=n();if(!r)throw Error("User ID not found. Please log in again.");console.log("\uD83D\uDE80 Saving signature to database via API:",{endpoint:`${t}/admin/update/signature`,userId:r,signatureLength:e.length});let i=await fetch(`${t}/admin/update/signature`,{method:"POST",headers:a(),body:JSON.stringify({signatureData:e})});if(i.ok){let e=await i.json();return console.log("✅ Signature saved to database successfully:",e),{success:!0,message:e.message||"Signature saved successfully",signatureId:e.signatureId}}{let e=await i.text();return console.error("❌ Failed to save signature to database:",{status:i.status,statusText:i.statusText,error:e}),{success:!1,error:`Failed to save signature: ${e}`}}}catch(e){return console.error("❌ Signature API error:",e),{success:!1,error:e instanceof Error?e.message:"Network error occurred"}}},s=async()=>{console.log("ℹ️ No GET endpoint available - checking localStorage only");try{return!!localStorage.getItem("enrollmentSignature")}catch(e){return console.error("❌ Error checking signature existence:",e),!1}},l=e=>{if(!e||""===e.trim())return{isValid:!1,error:"Signature data is required"};try{atob(e)}catch(e){return{isValid:!1,error:"Invalid signature data format"}}return e.length>5242880?{isValid:!1,error:"Signature data too large"}:{isValid:!0}},d=async e=>{let t=l(e);return t.isValid?await i(e):{success:!1,error:t.error}},u=async(e,t=3)=>{let r="";for(let o=1;o<=t;o++){console.log(`🔄 Signature save attempt ${o}/${t}`);let n=await d(e);if(n.success)return console.log(`✅ Signature saved successfully on attempt ${o}`),n;if(console.warn(`⚠️ Attempt ${o} failed:`,r=n.error||"Unknown error"),o<t){let e=1e3*Math.pow(2,o);await new Promise(t=>setTimeout(t,e))}}return{success:!1,error:`Failed after ${t} attempts. Last error: ${r}`}}},97183:(e,t,r)=>{r.d(t,{Fj:()=>l,Pe:()=>d,ni:()=>n,td:()=>i,uM:()=>s,yd:()=>a});let o=e=>{try{let t=atob(e),r=new Uint8Array(Array.from(t).map(e=>e.charCodeAt(0))),o=new TextDecoder().decode(r);return JSON.parse(o)}catch(e){throw console.error("Error decrypting signature:",e),Error("Failed to decrypt signature")}},n=()=>{try{let e=localStorage.getItem("enrollmentSignature");if(!e)return null;return o(e)}catch(e){return console.error("Error retrieving signature:",e),null}},a=()=>{try{let e=localStorage.getItem("enrollmentSignatureRef");if(!e)return null;return JSON.parse(e)}catch(e){return console.error("Error retrieving signature reference:",e),null}},i=()=>{try{localStorage.removeItem("enrollmentSignature"),localStorage.removeItem("enrollmentSignatureRef"),console.log("\uD83D\uDDD1️ Signature data cleared")}catch(e){console.error("Error clearing signature:",e)}},s=e=>{try{return btoa(e.signature).substring(0,32)===e.signatureHash}catch(e){return console.error("Error verifying signature:",e),!1}},l=e=>{try{return new Date(e).toLocaleString("en-US",{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit",second:"2-digit",timeZoneName:"short"})}catch(t){return e}},d=()=>{let e=n(),t=a();if(console.log("\uD83D\uDD0D Signature Debug Info:"),console.log("Reference:",t),e){console.log("Full Signature Data:",{timestamp:e.timestamp,employeeName:e.employeeName,signatureHash:e.signatureHash,userAgent:e.userAgent,signatureSize:e.signature.length,isValid:s(e)});let t=new Image;t.onload=()=>{console.log("\uD83D\uDCDD Signature Image:",t),console.log("Dimensions:",t.width,"x",t.height)},t.src=e.signature}else console.log("No signature found in storage")}}};