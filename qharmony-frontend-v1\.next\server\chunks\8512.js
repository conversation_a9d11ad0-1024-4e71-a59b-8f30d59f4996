"use strict";exports.id=8512,exports.ids=[8512],exports.modules={19551:(e,t,n)=>{n.d(t,{Z:()=>h});var o=n(10326),r=n(6283),i=n(89178),l=n(25609),s=n(48260),a=n(98139),d=n(16027);let c=(0,n(51426).Z)((0,o.jsx)("path",{d:"M19.35 10.04C18.67 6.59 15.64 4 12 4 9.11 4 6.6 5.64 5.35 8.04 2.34 8.36 0 10.91 0 14c0 3.31 2.69 6 6 6h13c2.76 0 5-2.24 5-5 0-2.64-2.05-4.78-4.65-4.96M14 13v4h-4v-4H7l5-5 5 5z"}),"CloudUpload");var f=n(31870),x=n(17577),p=n(67840),g=n(69058),u=n(46226);let h=()=>{let e=(0,f.T)(),t=(0,f.C)(e=>e.company.companyDetails),n=(0,x.useRef)(null),[h,m]=(0,x.useState)(!1),b=()=>{n.current&&n.current.click()};return o.jsx(r.Z,{sx:{display:"flex",flexDirection:"column",alignItems:"flex-start",mt:3},children:(0,o.jsxs)(i.Z,{sx:{display:"flex",flexDirection:"column",alignItems:"flex-start",justifyContent:"space-between",width:"100%",boxShadow:"none",borderRadius:"30px",bgcolor:"#ffffff",paddingBottom:"12px",paddingX:"12px"},children:[(0,o.jsxs)(r.Z,{sx:{display:"flex",alignItems:"center",justifyContent:"space-between",width:"100%",mb:2,paddingTop:"25px",paddingX:"12px"},children:[(0,o.jsxs)(r.Z,{children:[o.jsx(l.Z,{variant:"h5",sx:{fontWeight:"bold",mb:.5,fontSize:"24px"},children:t.name||"N/A"}),o.jsx(l.Z,{variant:"body2",sx:{color:"#6c757d",fontSize:"16px"},children:t.industry||"N/A"})]}),(0,o.jsxs)(r.Z,{sx:{position:"relative",ml:2},children:[o.jsx(r.Z,{onClick:b,sx:{width:100,height:100,borderRadius:"12px",overflow:"hidden",position:"relative",display:"flex",alignItems:"center",justifyContent:"center",bgcolor:"#f4f4f4",cursor:"pointer"},children:t.details?.logo?o.jsx(u.default,{src:t.details.logo,alt:"Company Logo",layout:"fill",objectFit:"contain"}):(0,o.jsxs)(r.Z,{sx:{display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",height:"100%",color:"#9e9e9e"},children:[o.jsx(c,{fontSize:"large"}),o.jsx(l.Z,{variant:"caption",sx:{mb:.5,fontWeight:600},children:"Logo"})]})}),o.jsx(s.Z,{onClick:b,sx:{position:"absolute",top:0,right:0,bgcolor:"#000000",color:"#ffffff",borderRadius:"50%",width:"24px",height:"24px",p:"2px"},children:h?o.jsx(a.Z,{size:16}):o.jsx(p.Z,{sx:{fontSize:"16px"}})})]})]}),o.jsx(i.Z,{sx:{borderRadius:"30px",p:2.5,boxShadow:"none",bgcolor:"rgba(245, 245, 245, 0.7)",width:"100%",mt:2},children:(0,o.jsxs)(d.ZP,{container:!0,spacing:2,children:[o.jsx(d.ZP,{item:!0,xs:6,children:o.jsx(l.Z,{variant:"body2",sx:{color:"#6c757d",fontSize:"14px",textAlign:"left"},children:"Company Size"})}),o.jsx(d.ZP,{item:!0,xs:6,children:o.jsx(l.Z,{variant:"body2",sx:{fontSize:"14px",textAlign:"right",fontWeight:600,wordBreak:"break-word",overflowWrap:"break-word"},children:t.companySize||"2"})}),o.jsx(d.ZP,{item:!0,xs:6,children:o.jsx(l.Z,{variant:"body2",sx:{color:"#6c757d",fontSize:"14px",textAlign:"left",wordBreak:"break-word",overflowWrap:"break-word"},children:"Website"})}),o.jsx(d.ZP,{item:!0,xs:6,children:o.jsx(l.Z,{variant:"body2",sx:{fontSize:"14px",textAlign:"right",fontWeight:600,wordBreak:"break-word",overflowWrap:"break-word"},children:t.website?o.jsx("a",{href:`http://${t.website}`,target:"_blank",rel:"noopener noreferrer",style:{textDecoration:"none",color:"#000"},children:t.website}):"BenOsphere.com"})}),o.jsx(d.ZP,{item:!0,xs:6,children:o.jsx(l.Z,{variant:"body2",sx:{color:"#6c757d",fontSize:"14px",textAlign:"left",wordBreak:"break-word",overflowWrap:"break-word"},children:"Admin Email"})}),o.jsx(d.ZP,{item:!0,xs:6,children:o.jsx(l.Z,{variant:"body2",sx:{fontSize:"14px",textAlign:"right",fontWeight:600,wordBreak:"break-word",overflowWrap:"break-word"},children:t.adminEmail||"<EMAIL>"})})]})}),o.jsx("input",{type:"file",accept:".png, .jpg",ref:n,style:{display:"none"},onChange:t=>{let n=t.target.files?.[0];n&&(m(!0),(0,g.mH)(e,n).finally(()=>{m(!1)}))}})]})})}},50648:(e,t,n)=>{n.d(t,{Z:()=>g});var o=n(10326),r=n(6283),i=n(33198),l=n(25609),s=n(48260),a=n(89178),d=n(16027),c=n(31870),f=n(17577),x=n(67840),p=n(72779);let g=()=>{let e=(0,c.C)(e=>e.user.userProfile),[t,n]=(0,f.useState)(!1);return(0,o.jsxs)(r.Z,{sx:{display:"flex",flexDirection:"column",alignItems:"center",bgcolor:"#ffffff",paddingTop:7,paddingBottom:"12px",paddingX:"12px",borderRadius:"30px",width:"100%",position:"relative"},children:[o.jsx(r.Z,{sx:{position:"absolute",top:-60,display:"flex",justifyContent:"center",width:"100%"},children:o.jsx(i.Z,{sx:{backgroundImage:"linear-gradient(180deg, #4BD1E9 0%, #1274CF 100%)",color:"#ffffff",width:130,height:130,border:"10px solid #ffffff",fontSize:"48px",fontWeight:800},children:(t=>{console.log("Getting initials for name: ",JSON.stringify(e));let[n,o]=t.split(" ");return`${n[0].toUpperCase()}${o?o[0].toUpperCase():""}`})(e.name)})}),(0,o.jsxs)(r.Z,{sx:{display:"flex",alignItems:"center",mt:4,mb:4,position:"relative"},children:[o.jsx(l.Z,{variant:"h5",sx:{fontWeight:"bold",fontSize:"32px",flexGrow:1},children:e.name.replace(/\b\w/g,e=>e.toUpperCase())||"N/A"}),o.jsx(s.Z,{onClick:()=>{n(!0)},sx:{bgcolor:"#000000",color:"#ffffff",borderRadius:"50%",width:"24px",height:"24px",p:"2px",ml:2},children:o.jsx(x.Z,{sx:{fontSize:"16px"}})})]}),o.jsx(a.Z,{sx:{borderRadius:"30px",p:2.5,boxShadow:"none",bgcolor:"rgba(245, 245, 245, 0.7)",width:"100%"},children:(0,o.jsxs)(d.ZP,{container:!0,spacing:2,children:[o.jsx(d.ZP,{item:!0,xs:6,children:o.jsx(l.Z,{sx:{color:"#6c757d",fontSize:"14px",textAlign:"left"},children:"Title"})}),o.jsx(d.ZP,{item:!0,xs:6,children:o.jsx(l.Z,{sx:{fontSize:"14px",textAlign:"right",fontWeight:600},children:e.details?.title||"N/A"})}),o.jsx(d.ZP,{item:!0,xs:6,children:o.jsx(l.Z,{variant:"body2",sx:{color:"#6c757d",fontSize:"14px",textAlign:"left"},children:"Department"})}),o.jsx(d.ZP,{item:!0,xs:6,children:o.jsx(l.Z,{variant:"body2",sx:{fontSize:"14px",textAlign:"right",fontWeight:600},children:e.details?.department||"N/A"})}),o.jsx(d.ZP,{item:!0,xs:6,children:o.jsx(l.Z,{variant:"body2",sx:{color:"#6c757d",fontSize:"14px",textAlign:"left"},children:"Employment Type"})}),o.jsx(d.ZP,{item:!0,xs:6,children:o.jsx(l.Z,{variant:"body2",sx:{fontSize:"14px",textAlign:"right",fontWeight:600},children:"Full Time"})})]})}),o.jsx(p.Z,{open:t,onClose:()=>{n(!1)}})]})}},69058:(e,t,n)=>{n.d(t,{$t:()=>c,SS:()=>x,Y0:()=>l,cd:()=>f,fH:()=>p,mH:()=>g,ov:()=>d,v0:()=>s});var o=n(53148),r=n(25748),i=n(94638);async function l(e,t,n){try{let i=await (0,o.A_)("/benefits/benefit-by-type",{companyId:t,type:n});i&&i.benefits?(console.log("GET BENEFITS FOR TYPE RESPONSE: ",i.benefits),e((0,r.oQ)({benefitType:n,benefits:i.benefits})),e((0,r.nM)("Benefits fetched successfully"))):(console.error("Invalid response format:",i),e((0,r.nM)("Failed to fetch benefits")))}catch(t){console.error("Error fetching benefits:",t),e((0,r.nM)("Error fetching benefits"))}}async function s(e,t,n,i){let l={benefitId:t,page:i};console.log("data",l);let s=await (0,o.A_)("/benefits/one-benefit",l),d={...s,benefitId:t};for(let t of(e((0,r.F5)(d)),s.documents)){let o=decodeURIComponent(t.split("_____")[1]);a(e,t,n,o)}}async function a(e,t,n,i){let l={objectKey:t,companyId:n};console.log("data",l);let s=await (0,o.$R)("/benefits/document",l);if(console.log("VIEW BENEFIT RESPONSE: ",s),s){let n=new Blob([s],{type:"application/pdf"}),o=URL.createObjectURL(n);e((0,r.D7)([{documentObjectKey:t,document:o,originalFileName:i}]))}}let d=async(e,t,n,r,s)=>200===(await (0,o.j0)("/benefits/toggle-benefits/",{benefitId:n,companyId:t,isActivated:r})).status&&(await l(e,t,s),await (0,i.N)(e,t),!0);async function c(e,t,n,i){let l=new FormData;i.forEach(e=>l.append("documents",e)),l.append("companyId",n),l.append("benefitId",t);try{console.log("uploadDocument",l);let s=await (0,o.iG)("/benefits/add/document",l),d=s.data.objectKeys;if(console.log("newObjectKeys",d),200===s.status)return d.forEach((o,l)=>{let s=i[l].name;e((0,r.H_)({benefitId:t,document:o})),a(e,o,n,s)}),e((0,r.nM)("Document added successfully")),!0;return console.error("Error adding document:",s.data.error),e((0,r.nM)("Failed to add document")),!1}catch(t){return console.error("Error adding document:",t),e((0,r.nM)("Error adding document")),!1}}async function f(e,t,n,i){try{let l=await (0,o.j0)("/benefits/delete/document",{benefitId:t,companyId:n,objectKey:i});if(200===l.status)return e((0,r.iH)({benefitId:t,document:i})),e((0,r.nM)("Document deleted successfully")),!0;return console.error("Error deleting document:",l.data.error),e((0,r.nM)("Failed to delete document")),!1}catch(t){return console.error("Error deleting document:",t),e((0,r.nM)("Error deleting document")),!1}}async function x(e,t,n,i){try{let l=await (0,o.j0)("/benefits/add/links",{benefitId:t,companyId:n,urls:[i]});if(200===l.status)return e((0,r.MJ)({benefitId:t,link:i})),e((0,r.nM)("Link added successfully")),!0;return console.error("Error adding link:",l.data.error),e((0,r.nM)("Failed to add link")),!1}catch(t){return console.error("Error adding link:",t),e((0,r.nM)("Error adding link")),!1}}async function p(e,t,n,i){try{let l=await (0,o.j0)("/benefits/delete/link",{benefitId:t,companyId:n,urls:i});if(200===l.status)return e((0,r.Yw)({benefitId:t,link:i})),e((0,r.nM)("Link deleted successfully")),!0;return console.error("Error deleting link:",l.data.error),e((0,r.nM)("Failed to delete link")),!1}catch(t){return console.error("Error deleting link:",t),e((0,r.nM)("Error deleting link")),!1}}async function g(e,t){let n=new FormData;n.append("logoImage",t);try{console.log("uploading company logo",n);let t=await (0,o.iG)("/admin/update-company-logo",n);if(await (0,i.aK)(e),200===t.status)return console.log("Company logo updated successfully"),e((0,r.nM)("Company logo updated successfully")),!0;return console.error("Error updating company logo:",t.data.error),e((0,r.nM)("Failed to update company logo")),!1}catch(t){return console.error("Error updating company logo:",t),e((0,r.nM)("Error updating company logo")),!1}}}};