"use strict";exports.id=8581,exports.ids=[8581],exports.modules={78581:(e,a,i)=>{i.d(a,{Z:()=>p});var t=i(10326),s=i(77626),l=i.n(s),r=i(17577),o=i.n(r),n=i(38492),c=i(22509),d=i(59562);i(91596);let p=(0,r.memo)(({onCancel:e,onSubmit:a,initialData:i,isModal:s=!1,existingCarriers:p})=>{let[x,m]=(0,r.useState)(1),[b,f]=(0,r.useState)(!1),[g,h]=(0,r.useState)(null),[u,j]=(0,r.useState)(!1),[y,v]=(0,r.useState)({isChecking:!1,isDuplicate:!1,error:null,existingPlan:null}),[w,N]=(0,r.useState)({isChecking:!1,isDuplicate:!1,error:null,existingPlan:null}),[k,C]=(0,r.useState)({documents:[],coverageCategory:"",coverageType:"",carrier:"",planName:i?.planName||"",planCode:i?.planCode||"",planType:i?.planType||"",metalTier:i?.metalTier||"",videoUrl:"",description:i?.description||"",highlights:i?.highlights||[""]}),z=[{number:1,title:"Documents",active:1===x,completed:x>1,tooltip:"Upload plan documents, brochures, and supporting materials (optional)"},{number:2,title:"Basic Details",active:2===x,completed:x>2,tooltip:"Enter plan name, carrier, coverage type, and metal tier information"},{number:3,title:"Description",active:3===x,completed:x>3,tooltip:"Add plan description, key highlights, and optional video content"},{number:4,title:"Preview",active:4===x,completed:x>4,tooltip:"Review all plan details and create your new plan"}];(0,r.useEffect)(()=>{(async()=>{try{let e=(0,d.HP)(),a=p||[],i=!0;if(p)console.log("⚡ Using existing carriers data, skipping API call");else{console.log("\uD83D\uDD04 Fetching carriers from API...");let e=await (0,d.ie)();a=e.data||[],i=e.success}if(e.success&&i){let i={plans:[],templates:[],carriers:a,planTypes:e.data?.planTypes||[],coverageCategories:e.data?.coverageCategories||[],coverageMap:e.data?.coverageMap||{},metalTiers:e.data?.metalTiers||[]};h(i)}else{console.error("Failed to load data:",{constants:e.success,carriers:i,carriersError:p?"Using existing carriers":"API call failed"});let t={plans:[],templates:[],carriers:a,planTypes:e.data?.planTypes||[],coverageCategories:e.data?.coverageCategories||[],coverageMap:e.data?.coverageMap||{},metalTiers:e.data?.metalTiers||[]};h(t)}}catch(e){console.error("Error loading data:",e),h({plans:[],templates:[],carriers:[],planTypes:[],coverageCategories:[],coverageMap:{},metalTiers:[]})}})()},[]);let T=o().useMemo(()=>{let e;return a=>{if(clearTimeout(e),!a.trim()){v({isChecking:!1,isDuplicate:!1,error:null,existingPlan:null});return}v(e=>({...e,isChecking:!0,error:null})),e=setTimeout(async()=>{try{let e=await (0,d.nR)(a,i?._id);e.success&&e.data?v({isChecking:!1,isDuplicate:e.data.isDuplicate,error:null,existingPlan:e.data.existingPlan||null}):v({isChecking:!1,isDuplicate:!1,error:e.error||"Failed to check for duplicates",existingPlan:null})}catch(e){v({isChecking:!1,isDuplicate:!1,error:"Network error while checking for duplicates",existingPlan:null})}},800)}},[i?._id]),S=o().useMemo(()=>{let e;return a=>{if(clearTimeout(e),!a.trim()){N({isChecking:!1,isDuplicate:!1,error:null,existingPlan:null});return}N(e=>({...e,isChecking:!0,error:null})),e=setTimeout(async()=>{try{let e=await (0,d.yW)(a,i?._id);e.success&&e.data?N({isChecking:!1,isDuplicate:e.data.isDuplicate,error:null,existingPlan:e.data.existingPlan||null}):N({isChecking:!1,isDuplicate:!1,error:e.error||"Failed to check for duplicates",existingPlan:null})}catch(e){N({isChecking:!1,isDuplicate:!1,error:"Network error while checking for duplicates",existingPlan:null})}},800)}},[i?._id]);(0,r.useEffect)(()=>{if(k.planName&&k.planName.trim()){if(i&&i.planName===k.planName){console.log("⏭️ Skipping duplicate name check - editing same plan");return}T(k.planName)}},[k.planName,T,i]),(0,r.useEffect)(()=>{if(k.planCode&&k.planCode.trim()){if(i&&i.planCode===k.planCode){console.log("⏭️ Skipping duplicate code check - editing same plan");return}S(k.planCode)}},[k.planCode,S,i]),(0,r.useEffect)(()=>{let e=null,a=(a,t)=>{i();let s=a.getBoundingClientRect(),l=s.left+s.width/2-140;l<10&&(l=10),l+280>window.innerWidth-10&&(l=window.innerWidth-280-10);let r=s.top,o=window.innerHeight-s.bottom,n=r<120&&o>120,c=document.createElement("div");c.className="custom-tooltip",c.textContent=t,c.style.cssText=`
        position: fixed;
        ${n?`top: ${s.bottom+4}px;`:`bottom: ${window.innerHeight-s.top+4}px;`}
        left: ${l}px;
        width: 280px;
        background: #1f2937;
        color: white;
        padding: 0.75rem 1rem;
        border-radius: 0.5rem;
        font-size: 0.8rem;
        font-weight: 400;
        line-height: 1.4;
        text-align: left;
        white-space: normal;
        word-wrap: break-word;
        hyphens: auto;
        box-shadow: 0 8px 16px rgba(0, 0, 0, 0.15);
        z-index: 99999;
        pointer-events: none;
        opacity: 0;
        transition: opacity 0.2s ease-in-out;
      `;let d=document.createElement("div");d.className="custom-tooltip-arrow",d.style.cssText=`
        position: fixed;
        ${n?`top: ${s.bottom-2}px;`:`bottom: ${window.innerHeight-s.top-2}px;`}
        left: ${s.left+s.width/2-6}px;
        width: 0;
        height: 0;
        border-left: 6px solid transparent;
        border-right: 6px solid transparent;
        ${n?"border-bottom: 6px solid #1f2937;":"border-top: 6px solid #1f2937;"}
        z-index: 100000;
        pointer-events: none;
        opacity: 0;
        transition: opacity 0.2s ease-in-out;
      `,document.body.appendChild(c),document.body.appendChild(d),requestAnimationFrame(()=>{c.style.opacity="1",d.style.opacity="1"}),(e=c).arrow=d},i=()=>{e&&(e.remove(),e.arrow&&e.arrow.remove(),e=null)},t=e=>{let i=e.target,t=i.getAttribute("data-tooltip");t&&a(i,t)},s=()=>{i()},l=document.querySelectorAll(".tooltip-icon[data-tooltip]");return l.forEach(e=>{e.addEventListener("mouseenter",t),e.addEventListener("mouseleave",s)}),()=>{i(),l.forEach(e=>{e.removeEventListener("mouseenter",t),e.removeEventListener("mouseleave",s)})}},[x]),(0,r.useEffect)(()=>{if(i&&g){console.log("\uD83D\uDD04 Prepopulating form with initialData:",i),console.log("\uD83D\uDD04 Available data:",g);let e="";if(i.carrierData?._id)console.log("\uD83C\uDFAF Using embedded carrierData._id:",e=i.carrierData._id);else if(i.carrierId)console.log("\uD83C\uDFAF Using carrierId:",e=i.carrierId);else if(i.carrier){let a=g.carriers.find(e=>e._id===i.carrier),t=g.carriers.find(e=>e.carrierName===i.carrier||e.displayName===i.carrier);console.log("\uD83C\uDFAF Using carrier matching fallback:",e=a?._id||t?._id||"")}let a="",t="";if(g.coverageMap&&i.coverageType){for(let[e,s]of Object.entries(g.coverageMap))if(s.includes(i.coverageType)){a=e,t=i.coverageType;break}}if(!t&&i.coverageSubTypes&&i.coverageSubTypes.length>0){for(let[e,s]of(t=i.coverageSubTypes[0],Object.entries(g.coverageMap||{})))if(s.includes(t)){a=e;break}}console.log("\uD83D\uDD04 Prepopulation results:",{planName:i.planName,planCode:i.planCode,carrierId:e,coverageCategory:a,coverageSubType:t,planType:i.planType,metalTier:i.metalTier}),C(s=>({...s,planName:i.planName||"",planCode:i.planCode||"",carrier:e,coverageCategory:a,coverageType:t,planType:i.planType||"",metalTier:i.metalTier||"",description:i.description||"",highlights:i.highlights||[""],videoUrl:i.informativeLinks?.[0]||""}))}},[i,g]);let P=(e,a)=>{C(i=>({...i,[e]:a}))},D=e=>{if(e){let a=Array.from(e);C(e=>({...e,documents:[...e.documents,...a]}))}},F=e=>{C(a=>({...a,documents:a.documents.filter((a,i)=>i!==e)}))},H=()=>{x<4&&m(x+1)},U=()=>{x>1&&m(x-1)},M=async()=>{j(!0);try{let e;let t=k.coverageCategory,s={planName:k.planName,planCode:k.planCode,carrier:k.carrier,coverageType:t,coverageSubTypes:[k.coverageType],planType:k.planType,metalTier:k.metalTier,description:k.description,highlights:k.highlights.filter(e=>""!==e.trim()),informativeLinks:k.videoUrl?[k.videoUrl]:[],carrierId:k.carrier,isTemplate:!1,status:"Active"};if((e=i?await (0,d.Vs)(i._id,s):await (0,d.he)(s)).success&&e.data){if(console.log(`Plan ${i?"updated":"created"} successfully:`,e.data),!i&&k.documents.length>0){let a=await (0,d.Op)(e.data.plan._id,k.documents);a.success||console.warn("Failed to upload some documents:",a.error)}let t={_id:e.data.plan._id,planName:e.data.plan.planName,planCode:e.data.plan.planCode,coverageType:e.data.plan.coverageType,coverageSubTypes:e.data.plan.coverageSubTypes||[],planType:e.data.plan.planType,metalTier:e.data.plan.metalTier||k.metalTier||"Bronze",description:e.data.plan.description,highlights:e.data.plan.highlights,carrier:e.data.plan.carrier||k.carrier};a(t)}else throw Error(e.error||`Failed to ${i?"update":"create"} plan`)}catch(e){console.error(`Error ${i?"updating":"creating"} plan:`,e),alert(`Error ${i?"updating":"creating"} plan: ${e instanceof Error?e.message:"Please try again."}`)}finally{j(!1)}},W=()=>{C(e=>({...e,highlights:[...e.highlights,""]}))},A=(e,a)=>{C(i=>({...i,highlights:i.highlights.map((i,t)=>t===e?a:i)}))},B=e=>{k.highlights.length>1&&C(a=>({...a,highlights:a.highlights.filter((a,i)=>i!==e)}))},I=()=>k.planName&&k.planCode&&k.carrier&&k.planType&&k.coverageCategory&&k.coverageType&&!y.isDuplicate&&!y.isChecking&&!w.isDuplicate&&!w.isChecking,$=()=>k.description&&k.highlights.some(e=>""!==e.trim());if(!g)return t.jsx("div",{className:"p-6 text-center",children:"Loading..."});console.log("CreatePlanForm data:",g),console.log("Coverage categories:",g.coverageCategories),console.log("Coverage map:",g.coverageMap);let E=()=>(0,t.jsxs)("div",{className:"jsx-756eb827991f8e8b space-y-6",children:[t.jsx(l(),{id:"756eb827991f8e8b",children:"input.jsx-756eb827991f8e8b,select.jsx-756eb827991f8e8b,textarea.jsx-756eb827991f8e8b{background-color:white!important;color:#374151!important}input.jsx-756eb827991f8e8b::-webkit-input-placeholder{color:#9ca3af!important}input.jsx-756eb827991f8e8b:-moz-placeholder{color:#9ca3af!important}input.jsx-756eb827991f8e8b::-moz-placeholder{color:#9ca3af!important}input.jsx-756eb827991f8e8b:-ms-input-placeholder{color:#9ca3af!important}input.jsx-756eb827991f8e8b::-ms-input-placeholder{color:#9ca3af!important}input.jsx-756eb827991f8e8b::placeholder{color:#9ca3af!important}"}),t.jsx("div",{className:"jsx-756eb827991f8e8b flex items-center justify-between mb-6",children:(0,t.jsxs)("div",{className:"jsx-756eb827991f8e8b flex items-center gap-3",children:[t.jsx(c.kAf,{size:20,className:"text-purple-600"}),t.jsx("h3",{style:{fontSize:"14px",fontWeight:"500",lineHeight:"21px",color:"#374151"},className:"jsx-756eb827991f8e8b",children:"Basic Details"})]})}),(0,t.jsxs)("div",{className:"jsx-756eb827991f8e8b space-y-2",children:[(0,t.jsxs)("div",{className:"jsx-756eb827991f8e8b",children:[(0,t.jsxs)("label",{htmlFor:"coverageCategory",style:{fontSize:"14px",fontWeight:"500",lineHeight:"21px"},className:"jsx-756eb827991f8e8b block text-gray-900 mb-1",children:["Coverage Category *",t.jsx("span",{"data-tooltip":"Select the main category of coverage this plan provides (e.g., Medical, Dental, Vision)",className:"jsx-756eb827991f8e8b tooltip-icon"})]}),(0,t.jsxs)("select",{id:"coverageCategory",value:k.coverageCategory,onChange:e=>{P("coverageCategory",e.target.value),P("coverageType","")},style:{backgroundColor:"white",color:"#374151"},className:"jsx-756eb827991f8e8b w-full px-4 py-2.5 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-gray-900 bg-white",children:[t.jsx("option",{value:"",className:"jsx-756eb827991f8e8b",children:"Select coverage category"}),g?.coverageCategories?.map(e=>t.jsx("option",{value:e,className:"jsx-756eb827991f8e8b",children:e},e))||[]]})]}),(0,t.jsxs)("div",{className:"jsx-756eb827991f8e8b",children:[(0,t.jsxs)("label",{htmlFor:"coverageType",style:{fontSize:"14px",fontWeight:"500",lineHeight:"21px"},className:"jsx-756eb827991f8e8b block text-gray-900 mb-1",children:["Coverage Type *",t.jsx("span",{"data-tooltip":"Select the specific type of coverage within the category (e.g., PPO, HMO, EPO for Medical)",className:"jsx-756eb827991f8e8b tooltip-icon"})]}),(0,t.jsxs)("select",{id:"coverageType",value:k.coverageType,onChange:e=>P("coverageType",e.target.value),disabled:!k.coverageCategory,style:{backgroundColor:k.coverageCategory?"white":"#f9fafb",color:"#374151"},className:"jsx-756eb827991f8e8b w-full px-4 py-2.5 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-gray-900 bg-white disabled:bg-gray-100 disabled:cursor-not-allowed",children:[t.jsx("option",{value:"",className:"jsx-756eb827991f8e8b",children:"Select coverage type"}),k.coverageCategory&&g?.coverageMap?.[k.coverageCategory]?.map(e=>t.jsx("option",{value:e,className:"jsx-756eb827991f8e8b",children:e},e))||[]]})]}),(0,t.jsxs)("div",{className:"jsx-756eb827991f8e8b",children:[(0,t.jsxs)("label",{htmlFor:"carrier",style:{fontSize:"14px",fontWeight:"500",lineHeight:"21px"},className:"jsx-756eb827991f8e8b block text-gray-900 mb-1",children:["Carrier *",t.jsx("span",{"data-tooltip":"Select the insurance carrier that provides this plan (e.g., Blue Shield, Kaiser)",className:"jsx-756eb827991f8e8b tooltip-icon"})]}),(0,t.jsxs)("select",{id:"carrier",value:k.carrier,onChange:e=>P("carrier",e.target.value),className:"jsx-756eb827991f8e8b w-full px-4 py-2.5 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-gray-900 bg-white",children:[t.jsx("option",{value:"",className:"jsx-756eb827991f8e8b",children:"Select carrier"}),g?.carriers?.map(e=>t.jsx("option",{value:e._id,className:"jsx-756eb827991f8e8b",children:e.displayName||e.carrierName},e._id))||[]]})]}),(0,t.jsxs)("div",{className:"jsx-756eb827991f8e8b",children:[(0,t.jsxs)("label",{htmlFor:"planName",style:{fontSize:"14px",fontWeight:"500",lineHeight:"21px"},className:"jsx-756eb827991f8e8b block text-gray-900 mb-1",children:["Plan Name *",t.jsx("span",{"data-tooltip":"Enter a clear, descriptive name that helps identify this plan (e.g., Blue Shield PPO 500). We'll check for duplicates automatically.",className:"jsx-756eb827991f8e8b tooltip-icon"})]}),(0,t.jsxs)("div",{className:"jsx-756eb827991f8e8b relative",children:[t.jsx("input",{type:"text",id:"planName",placeholder:"e.g. Blue Shield PPO 500",value:k.planName,onChange:e=>P("planName",e.target.value),style:{backgroundColor:"white",color:"#374151"},className:`jsx-756eb827991f8e8b w-full px-4 py-2.5 pr-12 border rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-gray-900 bg-white ${y.isDuplicate?"border-red-500":y.isChecking?"border-yellow-500":k.planName&&!y.isDuplicate?"border-green-500":"border-gray-300"}`}),(0,t.jsxs)("div",{className:"jsx-756eb827991f8e8b absolute right-3 top-1/2 transform -translate-y-1/2",children:[y.isChecking&&t.jsx("div",{className:"jsx-756eb827991f8e8b w-4 h-4 border-2 border-yellow-500 border-t-transparent rounded-full animate-spin"}),!y.isChecking&&k.planName&&!y.isDuplicate&&t.jsx("span",{className:"jsx-756eb827991f8e8b text-green-600 font-bold",children:"✓"}),y.isDuplicate&&t.jsx("span",{className:"jsx-756eb827991f8e8b text-red-600 font-bold",children:"✗"})]})]}),y.isDuplicate&&y.existingPlan&&(0,t.jsxs)("div",{className:"jsx-756eb827991f8e8b mt-2 p-2 bg-red-50 border border-red-200 rounded text-sm text-red-700",children:[t.jsx("strong",{className:"jsx-756eb827991f8e8b",children:"Plan name already exists:"}),' "',y.existingPlan.planName,'"',y.existingPlan.planCode&&` (${y.existingPlan.planCode})`]}),y.error&&(0,t.jsxs)("div",{className:"jsx-756eb827991f8e8b mt-2 p-2 bg-yellow-50 border border-yellow-200 rounded text-sm text-yellow-700",children:[t.jsx("strong",{className:"jsx-756eb827991f8e8b",children:"Warning:"})," ",y.error]}),k.planName&&!y.isChecking&&!y.isDuplicate&&!y.error&&t.jsx("div",{className:"jsx-756eb827991f8e8b mt-2 p-2 bg-green-50 border border-green-200 rounded text-sm text-green-700",children:"✓ Plan name is available"})]}),(0,t.jsxs)("div",{className:"jsx-756eb827991f8e8b",children:[(0,t.jsxs)("label",{htmlFor:"planCode",style:{fontSize:"14px",fontWeight:"500",lineHeight:"21px"},className:"jsx-756eb827991f8e8b block text-gray-900 mb-1",children:["Plan Code *",t.jsx("span",{"data-tooltip":"Unique identifier for this plan used in systems and reports (e.g., BS-PPO-500). We'll check for duplicates automatically.",className:"jsx-756eb827991f8e8b tooltip-icon"})]}),(0,t.jsxs)("div",{className:"jsx-756eb827991f8e8b relative",children:[t.jsx("input",{type:"text",id:"planCode",placeholder:"e.g. BS-PPO-500",value:k.planCode,onChange:e=>P("planCode",e.target.value),style:{backgroundColor:"white",color:"#374151"},className:`jsx-756eb827991f8e8b w-full px-4 py-2.5 pr-12 border rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-gray-900 bg-white ${w.isDuplicate?"border-red-500":w.isChecking?"border-yellow-500":k.planCode&&!w.isDuplicate?"border-green-500":"border-gray-300"}`}),(0,t.jsxs)("div",{className:"jsx-756eb827991f8e8b absolute right-3 top-1/2 transform -translate-y-1/2",children:[w.isChecking&&t.jsx("div",{className:"jsx-756eb827991f8e8b w-4 h-4 border-2 border-yellow-500 border-t-transparent rounded-full animate-spin"}),!w.isChecking&&k.planCode&&!w.isDuplicate&&t.jsx("span",{className:"jsx-756eb827991f8e8b text-green-600 font-bold",children:"✓"}),w.isDuplicate&&t.jsx("span",{className:"jsx-756eb827991f8e8b text-red-600 font-bold",children:"✗"})]})]}),w.isDuplicate&&w.existingPlan&&(0,t.jsxs)("div",{className:"jsx-756eb827991f8e8b mt-2 p-2 bg-red-50 border border-red-200 rounded text-sm text-red-700",children:[t.jsx("strong",{className:"jsx-756eb827991f8e8b",children:"Plan code already exists:"}),' "',w.existingPlan.planCode,'"',w.existingPlan.planName&&` (${w.existingPlan.planName})`]}),w.error&&(0,t.jsxs)("div",{className:"jsx-756eb827991f8e8b mt-2 p-2 bg-yellow-50 border border-yellow-200 rounded text-sm text-yellow-700",children:[t.jsx("strong",{className:"jsx-756eb827991f8e8b",children:"Warning:"})," ",w.error]}),k.planCode&&!w.isChecking&&!w.isDuplicate&&!w.error&&t.jsx("div",{className:"jsx-756eb827991f8e8b mt-2 p-2 bg-green-50 border border-green-200 rounded text-sm text-green-700",children:"✓ Plan code is available"})]}),(0,t.jsxs)("div",{className:"jsx-756eb827991f8e8b",children:[(0,t.jsxs)("label",{htmlFor:"planType",style:{fontSize:"14px",fontWeight:"500",lineHeight:"21px"},className:"jsx-756eb827991f8e8b block text-gray-900 mb-1",children:["Plan Type *",t.jsx("span",{"data-tooltip":"Choose the type of health plan structure (PPO, HMO, EPO, POS, etc.)",className:"jsx-756eb827991f8e8b tooltip-icon"})]}),(0,t.jsxs)("select",{id:"planType",value:k.planType,onChange:e=>P("planType",e.target.value),className:"jsx-756eb827991f8e8b w-full px-4 py-2.5 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-gray-900 bg-white",children:[t.jsx("option",{value:"",className:"jsx-756eb827991f8e8b",children:"Select type"}),g?.planTypes?.map(e=>t.jsx("option",{value:e,className:"jsx-756eb827991f8e8b",children:e},e))||[]]})]}),(0,t.jsxs)("div",{className:"jsx-756eb827991f8e8b",children:[(0,t.jsxs)("label",{htmlFor:"metalTier",style:{fontSize:"14px",fontWeight:"500",lineHeight:"21px"},className:"jsx-756eb827991f8e8b block text-gray-900 mb-1",children:["Metal Tier (Optional)",t.jsx("span",{"data-tooltip":"Choose the coverage level (Bronze, Silver, Gold, Platinum). This field is optional and typically applies to medical plans.",className:"jsx-756eb827991f8e8b tooltip-icon"})]}),(0,t.jsxs)("select",{id:"metalTier",value:k.metalTier,onChange:e=>P("metalTier",e.target.value),className:"jsx-756eb827991f8e8b w-full px-4 py-2.5 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-gray-900 bg-white",children:[t.jsx("option",{value:"",className:"jsx-756eb827991f8e8b",children:"Select tier"}),g?.metalTiers?.map(e=>t.jsx("option",{value:e,className:"jsx-756eb827991f8e8b",children:e},e))||[]]})]})]}),(0,t.jsxs)("div",{className:"jsx-756eb827991f8e8b flex justify-between pt-4",children:[(0,t.jsxs)("button",{type:"button",onClick:e,className:"jsx-756eb827991f8e8b flex items-center gap-2 px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50",children:[t.jsx(n.Tsu,{size:16}),"Back to Selection"]}),t.jsx("button",{type:"button",onClick:H,disabled:!I(),style:{fontSize:"14px",lineHeight:"21px",fontFamily:"'SF Pro', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif"},className:`jsx-756eb827991f8e8b px-4 py-2 rounded-md ${I()?"bg-gradient-to-r from-blue-600 to-purple-600 text-white hover:opacity-90":"bg-gray-300 text-gray-500 cursor-not-allowed"}`,children:"Continue to Basic Info"})]})]}),O=()=>(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"flex items-center gap-3 mb-6",children:[t.jsx(c.jNQ,{size:20,className:"text-purple-600"}),t.jsx("h3",{style:{fontSize:"14px",fontWeight:"500",lineHeight:"21px",color:"#374151"},children:"Description & Video"})]}),(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsxs)("label",{htmlFor:"videoUrl",style:{fontSize:"14px",fontWeight:"500",lineHeight:"21px"},className:"block text-gray-900 mb-1",children:["Video URL (Optional)",t.jsx("span",{className:"tooltip-icon","data-tooltip":"Add a YouTube or Vimeo URL to help explain plan benefits and features"})]}),(0,t.jsxs)("div",{className:"input-with-prefix",children:[t.jsx("span",{className:"input-prefix",children:"\uD83C\uDFA5"}),t.jsx("input",{type:"url",id:"videoUrl",placeholder:"https://youtube.com/watch?v=...",value:k.videoUrl,onChange:e=>P("videoUrl",e.target.value),style:{fontSize:"14px",fontWeight:"400",lineHeight:"21px"},className:"w-full px-4 py-2.5 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-gray-900 bg-white"})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsxs)("label",{htmlFor:"description",style:{fontSize:"14px",fontWeight:"500",lineHeight:"21px"},className:"block text-gray-900 mb-1",children:["Plan Description *",t.jsx("span",{className:"tooltip-icon","data-tooltip":"Provide a detailed description of the plan benefits, coverage, and key features"})]}),t.jsx("textarea",{id:"description",placeholder:"Describe the plan benefits and features...",value:k.description,onChange:e=>P("description",e.target.value),rows:4,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 text-gray-900 bg-white"}),t.jsx("small",{className:"text-gray-500 text-sm",children:"Describe the key benefits, coverage details, and what makes this plan unique"})]}),(0,t.jsxs)("div",{children:[(0,t.jsxs)("label",{style:{fontSize:"14px",fontWeight:"500",lineHeight:"21px"},className:"block text-gray-900 mb-1",children:["Plan Highlights *",t.jsx("span",{className:"tooltip-icon","data-tooltip":"Add key selling points and highlights that make this plan attractive (e.g., Low deductible, Nationwide network, No referrals needed)"})]}),t.jsx("small",{className:"text-gray-500 text-sm mb-3 block",children:"Add the most important features that make this plan attractive"}),t.jsx("div",{className:"space-y-3",children:k.highlights.map((e,a)=>(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[t.jsx("input",{type:"text",placeholder:"e.g. Low deductible, Nationwide network",value:e,onChange:e=>A(a,e.target.value),className:"flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 text-gray-900 bg-white"}),k.highlights.length>1&&t.jsx("button",{type:"button",onClick:()=>B(a),className:"text-red-500 hover:text-red-700 p-1",children:t.jsx(n.fMW,{size:16})})]},a))}),(0,t.jsxs)("button",{type:"button",onClick:W,className:"mt-3 flex items-center gap-2 text-purple-600 hover:text-purple-800 text-sm font-medium",children:[t.jsx(n.r7I,{size:16}),"Add Highlight"]})]})]}),(0,t.jsxs)("div",{className:"flex justify-between pt-4",children:[(0,t.jsxs)("button",{type:"button",onClick:U,className:"flex items-center gap-2 px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50",children:[t.jsx(n.Tsu,{size:16}),"Back"]}),t.jsx("button",{type:"button",onClick:H,disabled:!$(),className:`px-4 py-2 rounded-md ${$()?"bg-gradient-to-r from-blue-600 to-purple-600 text-white hover:opacity-90":"bg-gray-300 text-gray-500 cursor-not-allowed"}`,style:{fontSize:"14px",lineHeight:"21px",fontFamily:"'SF Pro', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif"},children:"Preview Plan"})]})]}),_=()=>(0,t.jsxs)("div",{className:"form-section",children:[(0,t.jsxs)("div",{className:"form-header",children:[(0,t.jsxs)("div",{className:"form-header-content",children:[t.jsx(c.GH$,{size:20}),t.jsx("h3",{style:{fontSize:"14px",fontWeight:"500",lineHeight:"21px",color:"#374151"},children:"AI-Powered Plan Preview"})]}),(0,t.jsxs)("div",{style:{display:"flex",alignItems:"center",gap:"0.5rem",padding:"0.375rem 0.75rem",borderRadius:"0.5rem",fontSize:"0.875rem",fontWeight:"500",background:"#dcfce7",color:"#166534"},children:[t.jsx(n.PjL,{size:16}),"Ready to Create"]})]}),(0,t.jsxs)("div",{className:"review-content",children:[(0,t.jsxs)("div",{className:"review-section",children:[(0,t.jsxs)("div",{className:"review-section-header",children:[t.jsx(c.kAf,{size:18}),t.jsx("h4",{children:"Plan Information"})]}),(0,t.jsxs)("div",{className:"review-items",children:[(0,t.jsxs)("div",{className:"review-item",children:[t.jsx("span",{className:"review-label",children:"Plan Name:"}),t.jsx("span",{className:"review-value",children:k.planName})]}),(0,t.jsxs)("div",{className:"review-item",children:[t.jsx("span",{className:"review-label",children:"Plan Code:"}),t.jsx("span",{className:"review-value font-mono",children:k.planCode})]}),(0,t.jsxs)("div",{className:"review-item",children:[t.jsx("span",{className:"review-label",children:"Carrier:"}),t.jsx("span",{className:"review-value",children:g?.carriers?.find(e=>e._id===k.carrier)?.displayName||g?.carriers?.find(e=>e._id===k.carrier)?.carrierName||"Unknown"})]}),(0,t.jsxs)("div",{className:"review-item",children:[t.jsx("span",{className:"review-label",children:"Plan Type:"}),t.jsx("span",{className:"review-value",children:k.planType})]}),(0,t.jsxs)("div",{className:"review-item",children:[t.jsx("span",{className:"review-label",children:"Coverage Type:"}),t.jsx("span",{className:"review-value",children:k.coverageType})]}),k.metalTier&&(0,t.jsxs)("div",{className:"review-item",children:[t.jsx("span",{className:"review-label",children:"Metal Tier:"}),t.jsx("span",{className:"review-value",children:k.metalTier})]})]})]}),(k.videoUrl||k.documents.length>0)&&(0,t.jsxs)("div",{className:"review-section",children:[(0,t.jsxs)("div",{className:"review-section-header",children:[t.jsx(n.vrJ,{size:18}),t.jsx("h4",{children:"Media & Documents"})]}),(0,t.jsxs)("div",{className:"review-items",children:[k.videoUrl&&(0,t.jsxs)("div",{className:"review-item",children:[t.jsx("span",{className:"review-label",children:"Video URL:"}),t.jsx("a",{href:k.videoUrl,target:"_blank",rel:"noopener noreferrer",className:"review-link",children:k.videoUrl})]}),k.documents.length>0&&(0,t.jsxs)("div",{className:"review-item full-width",children:[t.jsx("span",{className:"review-label",children:"Documents:"}),t.jsx("div",{className:"review-documents",children:k.documents.map((e,a)=>(0,t.jsxs)("div",{className:"review-document",children:[t.jsx(n.vrJ,{size:14}),t.jsx("span",{children:e.name}),(0,t.jsxs)("small",{children:[(e.size/1024/1024).toFixed(2)," MB"]})]},a))})]})]})]}),(0,t.jsxs)("div",{className:"review-section",children:[(0,t.jsxs)("div",{className:"review-section-header",children:[t.jsx(c.jNQ,{size:18}),t.jsx("h4",{children:"Description & Highlights"})]}),(0,t.jsxs)("div",{className:"review-items",children:[(0,t.jsxs)("div",{className:"review-item full-width",children:[t.jsx("span",{className:"review-label",children:"Description:"}),t.jsx("p",{className:"review-value",children:k.description})]}),(0,t.jsxs)("div",{className:"review-item full-width",children:[t.jsx("span",{className:"review-label",children:"Highlights:"}),t.jsx("ul",{className:"review-highlights",children:k.highlights.filter(e=>e.trim()).map((e,a)=>(0,t.jsxs)("li",{className:"review-highlight",children:[t.jsx("span",{className:"highlight-bullet"}),e]},a))})]})]})]})]}),(0,t.jsxs)("div",{className:"flex justify-between pt-4",children:[(0,t.jsxs)("button",{type:"button",onClick:U,className:"flex items-center gap-2 px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50",children:[t.jsx(n.Tsu,{size:16}),"Back"]}),t.jsx("button",{type:"button",onClick:M,disabled:u,className:`px-6 py-2 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-md hover:opacity-90 ${u?"opacity-70 cursor-not-allowed":""}`,style:{fontSize:"14px",lineHeight:"21px",fontFamily:"'SF Pro', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif"},children:u?i?"Updating Plan...":"Creating Plan...":i?"Update Plan":"Create Plan"})]})]}),R=()=>(0,t.jsxs)("div",{className:"jsx-756eb827991f8e8b space-y-6",children:[t.jsx(l(),{id:"756eb827991f8e8b",children:"input.jsx-756eb827991f8e8b,select.jsx-756eb827991f8e8b,textarea.jsx-756eb827991f8e8b{background-color:white!important;color:#374151!important}input.jsx-756eb827991f8e8b::-webkit-input-placeholder{color:#9ca3af!important}input.jsx-756eb827991f8e8b:-moz-placeholder{color:#9ca3af!important}input.jsx-756eb827991f8e8b::-moz-placeholder{color:#9ca3af!important}input.jsx-756eb827991f8e8b:-ms-input-placeholder{color:#9ca3af!important}input.jsx-756eb827991f8e8b::-ms-input-placeholder{color:#9ca3af!important}input.jsx-756eb827991f8e8b::placeholder{color:#9ca3af!important}"}),(0,t.jsxs)("div",{className:"jsx-756eb827991f8e8b flex items-center gap-3 mb-6",children:[t.jsx(n.vrJ,{size:20,className:"text-purple-600"}),t.jsx("h3",{style:{fontSize:"14px",fontWeight:"500",lineHeight:"21px",color:"#374151"},className:"jsx-756eb827991f8e8b",children:"Documents Upload"})]}),(0,t.jsxs)("div",{className:"jsx-756eb827991f8e8b space-y-6",children:[(0,t.jsxs)("div",{className:"jsx-756eb827991f8e8b",children:[(0,t.jsxs)("label",{style:{fontSize:"14px",fontWeight:"500",lineHeight:"21px"},className:"jsx-756eb827991f8e8b block text-gray-900 mb-2",children:["Plan Documents (Optional)",t.jsx("span",{"data-tooltip":"Upload documents related to this plan such as brochures, benefit summaries, or plan details (PDF, DOC, TXT, or Image files)",className:"jsx-756eb827991f8e8b tooltip-icon"})]}),(0,t.jsxs)("div",{className:"jsx-756eb827991f8e8b file-upload-area",children:[t.jsx("input",{type:"file",multiple:!0,accept:".pdf,.doc,.docx,.txt,.jpg,.jpeg,.png,.gif",onChange:e=>D(e.target.files),className:"jsx-756eb827991f8e8b file-input"}),(0,t.jsxs)("div",{className:"jsx-756eb827991f8e8b file-upload-label",children:[t.jsx(n.qX3,{size:48,className:"text-gray-400"}),t.jsx("span",{style:{fontSize:"14px",fontWeight:"500",lineHeight:"21px"},className:"jsx-756eb827991f8e8b",children:"Click to upload or drag and drop"}),t.jsx("small",{style:{fontSize:"12px",fontWeight:"400",lineHeight:"18px"},className:"jsx-756eb827991f8e8b text-gray-500",children:"PDF, DOC, TXT, or Image files (Max 10MB each)"})]})]})]}),k.documents.length>0&&(0,t.jsxs)("div",{className:"jsx-756eb827991f8e8b uploaded-files",children:[t.jsx("h4",{style:{fontSize:"14px",fontWeight:"500",lineHeight:"21px"},className:"jsx-756eb827991f8e8b",children:"Uploaded Files"}),k.documents.map((e,a)=>(0,t.jsxs)("div",{className:"jsx-756eb827991f8e8b uploaded-file",children:[t.jsx(n.vrJ,{size:16,className:"text-gray-500"}),t.jsx("span",{style:{fontSize:"14px",fontWeight:"400",lineHeight:"21px"},className:"jsx-756eb827991f8e8b file-name",children:e.name}),(0,t.jsxs)("span",{style:{fontSize:"12px",fontWeight:"400",lineHeight:"18px"},className:"jsx-756eb827991f8e8b file-size",children:[(e.size/1024/1024).toFixed(2)," MB"]}),t.jsx("button",{onClick:()=>F(a),title:"Remove file",className:"jsx-756eb827991f8e8b remove-file",children:t.jsx(n.fMW,{size:12})})]},a))]})]}),t.jsx("div",{className:"jsx-756eb827991f8e8b flex justify-end",children:t.jsx("button",{type:"button",onClick:H,style:{fontSize:"14px",lineHeight:"21px",fontFamily:"'SF Pro', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif"},className:"jsx-756eb827991f8e8b px-4 py-2 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-md hover:opacity-90",children:"Continue to Basic Info"})})]}),L=e=>{switch(e){case 1:default:return t.jsx(n.vrJ,{});case 2:return t.jsx(c.kAf,{});case 3:return t.jsx(c.jNQ,{});case 4:return t.jsx(c.GH$,{})}};return(0,t.jsxs)("div",{style:{fontSize:"14px",lineHeight:"21px",color:"#374151",fontFamily:"'SF Pro', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif"},className:"jsx-71927067afdf6a49 bg-white space-y-6",children:[t.jsx(l(),{id:"71927067afdf6a49",children:'.text-gray-900.jsx-71927067afdf6a49{color:#374151!important}input.jsx-71927067afdf6a49,select.jsx-71927067afdf6a49,textarea.jsx-71927067afdf6a49{color:#374151!important}.file-upload-area.jsx-71927067afdf6a49{position:relative;border:2px dashed#d1d5db;-webkit-border-radius:.75rem;-moz-border-radius:.75rem;border-radius:.75rem;padding:2rem;text-align:center;-webkit-transition:all.2s ease;-moz-transition:all.2s ease;-o-transition:all.2s ease;transition:all.2s ease;background:#fafafa}.file-upload-area.jsx-71927067afdf6a49:hover{border-color:#8b5cf6;background:#f8fafc}.file-input.jsx-71927067afdf6a49{position:absolute;inset:0;width:100%;height:100%;opacity:0;cursor:pointer}.file-upload-label.jsx-71927067afdf6a49{display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-orient:vertical;-webkit-box-direction:normal;-webkit-flex-direction:column;-moz-box-orient:vertical;-moz-box-direction:normal;-ms-flex-direction:column;flex-direction:column;-webkit-box-align:center;-webkit-align-items:center;-moz-box-align:center;-ms-flex-align:center;align-items:center;gap:.5rem;pointer-events:none}.uploaded-files.jsx-71927067afdf6a49{margin-top:1rem}.uploaded-file.jsx-71927067afdf6a49{display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-align:center;-webkit-align-items:center;-moz-box-align:center;-ms-flex-align:center;align-items:center;gap:.5rem;padding:.5rem;background:#f9fafb;border:1px solid#e5e7eb;-webkit-border-radius:.5rem;-moz-border-radius:.5rem;border-radius:.5rem;margin-bottom:.5rem}.file-name.jsx-71927067afdf6a49{-webkit-box-flex:1;-webkit-flex:1;-moz-box-flex:1;-ms-flex:1;flex:1}.file-size.jsx-71927067afdf6a49{color:#6b7280}.remove-file.jsx-71927067afdf6a49{padding:.25rem;background:#ef4444;color:white;border:none;-webkit-border-radius:.25rem;-moz-border-radius:.25rem;border-radius:.25rem;cursor:pointer}.input-with-prefix.jsx-71927067afdf6a49{position:relative}.input-prefix.jsx-71927067afdf6a49{position:absolute;left:.75rem;top:50%;-webkit-transform:translatey(-50%);-moz-transform:translatey(-50%);-ms-transform:translatey(-50%);-o-transform:translatey(-50%);transform:translatey(-50%);z-index:1}.input-with-prefix.jsx-71927067afdf6a49 input.jsx-71927067afdf6a49{padding-left:2.5rem}.form-section.jsx-71927067afdf6a49{background:white}.form-header.jsx-71927067afdf6a49{display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-align:center;-webkit-align-items:center;-moz-box-align:center;-ms-flex-align:center;align-items:center;-webkit-box-pack:justify;-webkit-justify-content:space-between;-moz-box-pack:justify;-ms-flex-pack:justify;justify-content:space-between;margin-bottom:1.5rem}.form-header-content.jsx-71927067afdf6a49{display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-align:center;-webkit-align-items:center;-moz-box-align:center;-ms-flex-align:center;align-items:center;gap:.75rem;color:#8b5cf6}.status-badge.jsx-71927067afdf6a49{display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-align:center;-webkit-align-items:center;-moz-box-align:center;-ms-flex-align:center;align-items:center;gap:.5rem;padding:.375rem .75rem;-webkit-border-radius:.5rem;-moz-border-radius:.5rem;border-radius:.5rem;font-size:.875rem;font-weight:500}.status-badge.ready.jsx-71927067afdf6a49{background:#dcfce7;color:#166534}.review-content.jsx-71927067afdf6a49{display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-orient:vertical;-webkit-box-direction:normal;-webkit-flex-direction:column;-moz-box-orient:vertical;-moz-box-direction:normal;-ms-flex-direction:column;flex-direction:column;gap:1.5rem}.review-section.jsx-71927067afdf6a49{background:#f8fafc;-webkit-border-radius:1rem;-moz-border-radius:1rem;border-radius:1rem;padding:1.5rem;border:1px solid#e2e8f0;-webkit-transition:all.3s ease;-moz-transition:all.3s ease;-o-transition:all.3s ease;transition:all.3s ease}.review-section.jsx-71927067afdf6a49:hover{-webkit-box-shadow:0 2px 8px rgba(0,0,0,.05);-moz-box-shadow:0 2px 8px rgba(0,0,0,.05);box-shadow:0 2px 8px rgba(0,0,0,.05)}.review-section-header.jsx-71927067afdf6a49{display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-align:center;-webkit-align-items:center;-moz-box-align:center;-ms-flex-align:center;align-items:center;gap:.75rem;margin-bottom:1.25rem;color:#3b82f6}.review-section-header.jsx-71927067afdf6a49 h4.jsx-71927067afdf6a49{font-size:1.125rem;font-weight:600;color:#1e293b;margin:0;font-family:"SF Pro",-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,sans-serif}.review-items.jsx-71927067afdf6a49{display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-orient:vertical;-webkit-box-direction:normal;-webkit-flex-direction:column;-moz-box-orient:vertical;-moz-box-direction:normal;-ms-flex-direction:column;flex-direction:column;gap:.75rem}.review-item.jsx-71927067afdf6a49{display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-pack:justify;-webkit-justify-content:space-between;-moz-box-pack:justify;-ms-flex-pack:justify;justify-content:space-between;-webkit-box-align:center;-webkit-align-items:center;-moz-box-align:center;-ms-flex-align:center;align-items:center}.review-item.full-width.jsx-71927067afdf6a49{-webkit-box-orient:vertical;-webkit-box-direction:normal;-webkit-flex-direction:column;-moz-box-orient:vertical;-moz-box-direction:normal;-ms-flex-direction:column;flex-direction:column;-webkit-box-align:start;-webkit-align-items:flex-start;-moz-box-align:start;-ms-flex-align:start;align-items:flex-start;gap:.5rem}.review-label.jsx-71927067afdf6a49{font-size:.875rem;color:#64748b;font-weight:500}.review-value.jsx-71927067afdf6a49{font-size:.875rem;color:#1e293b;font-weight:400}.review-link.jsx-71927067afdf6a49{color:#3b82f6;text-decoration:none;font-weight:500;word-break:break-all}.review-link.jsx-71927067afdf6a49:hover{text-decoration:underline}.review-documents.jsx-71927067afdf6a49{display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-orient:vertical;-webkit-box-direction:normal;-webkit-flex-direction:column;-moz-box-orient:vertical;-moz-box-direction:normal;-ms-flex-direction:column;flex-direction:column;gap:.5rem;margin-top:.5rem}.review-document.jsx-71927067afdf6a49{display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-align:center;-webkit-align-items:center;-moz-box-align:center;-ms-flex-align:center;align-items:center;gap:.5rem;padding:.5rem;background:white;border:1px solid#e5e7eb;-webkit-border-radius:.5rem;-moz-border-radius:.5rem;border-radius:.5rem}.review-document.jsx-71927067afdf6a49 span.jsx-71927067afdf6a49{font-size:.875rem;color:#374151;font-weight:500}.review-document.jsx-71927067afdf6a49 small.jsx-71927067afdf6a49{font-size:.75rem;color:#6b7280;margin-left:auto}.review-highlights.jsx-71927067afdf6a49{list-style:none;padding:0;margin:.5rem 0 0 0;display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-orient:vertical;-webkit-box-direction:normal;-webkit-flex-direction:column;-moz-box-orient:vertical;-moz-box-direction:normal;-ms-flex-direction:column;flex-direction:column;gap:.5rem}.review-highlight.jsx-71927067afdf6a49{display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-align:center;-webkit-align-items:center;-moz-box-align:center;-ms-flex-align:center;align-items:center;gap:.5rem;font-size:.875rem;color:#374151}.highlight-bullet.jsx-71927067afdf6a49{width:.375rem;height:.375rem;background:#8b5cf6;-webkit-border-radius:50%;-moz-border-radius:50%;border-radius:50%;-webkit-flex-shrink:0;-ms-flex-negative:0;flex-shrink:0}.page-nav-item.completed.jsx-71927067afdf6a49::after{content:"✓";display:-webkit-inline-box;display:-webkit-inline-flex;display:-moz-inline-box;display:-ms-inline-flexbox;display:inline-flex;-webkit-box-align:center;-webkit-align-items:center;-moz-box-align:center;-ms-flex-align:center;align-items:center;-webkit-box-pack:center;-webkit-justify-content:center;-moz-box-pack:center;-ms-flex-pack:center;justify-content:center;width:16px;height:16px;background:#7c3aed;-webkit-border-radius:50%;-moz-border-radius:50%;border-radius:50%;margin-left:6px;font-size:10px;font-weight:700;color:white;line-height:1;-webkit-flex-shrink:0;-ms-flex-negative:0;flex-shrink:0}'}),t.jsx("div",{className:"jsx-71927067afdf6a49 flex gap-3 mb-6",children:z.map(e=>(0,t.jsxs)("button",{style:{fontSize:"13px",lineHeight:"1.2",fontWeight:"450",fontFamily:"'SF Pro', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif",display:"inline-flex",alignItems:"center",gap:"6px",padding:"8px 16px",background:e.completed?"#ddd6fe":e.active?"#ede9fe":"#f3f4f6",border:"none",borderRadius:"24px",cursor:e.number<=x?"pointer":"not-allowed",color:e.completed?"#6d28d9":e.active?"#7c3aed":"#6b7280",transition:"all 0.2s ease",whiteSpace:"nowrap",minWidth:"fit-content",position:"relative"},onClick:()=>e.number<=x&&m(e.number),disabled:e.number>x,className:`jsx-71927067afdf6a49 page-nav-item ${e.completed?"completed":e.active?"active":""}`,children:[t.jsx("span",{className:"jsx-71927067afdf6a49 text-base",children:L(e.number)}),e.title]},e.number))}),(()=>{switch(x){case 1:default:return R();case 2:return E();case 3:return O();case 4:return _()}})(),b&&(0,t.jsxs)("div",{className:"jsx-71927067afdf6a49 fixed bottom-4 right-4 bg-gray-900 text-white p-4 rounded-lg shadow-lg flex items-center gap-3",children:[t.jsx(n.Moc,{size:20,className:"animate-spin"}),(0,t.jsxs)("div",{className:"jsx-71927067afdf6a49",children:[t.jsx("h4",{className:"jsx-71927067afdf6a49 font-medium",children:"AI Assist Applied"}),t.jsx("p",{className:"jsx-71927067afdf6a49 text-sm opacity-90",children:"Plan details have been pre-filled for you."})]})]})]})})}};