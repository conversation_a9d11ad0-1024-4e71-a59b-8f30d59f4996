exports.id=8618,exports.ids=[8618],exports.modules={59562:(e,t,r)=>{"use strict";r.d(t,{HP:()=>l,Op:()=>p,Vs:()=>u,he:()=>c,ie:()=>i,nR:()=>f,qY:()=>d,yW:()=>g});var a=r(89009),s=r(53148);let n=(0,a.bR)(),o=()=>({"Content-Type":"application/json","user-id":(0,a.n5)()}),l=()=>{let e={"Health Insurance":["Medical"],"Ancillary Benefits":["Dental","Vision"],"Life & Disability Insurance":["Term Life","Supplemental Life Insurance","Short-Term Disability","Long-Term Disability","Whole Life","Group (Employer) Life","Accidental Death & Dismemberment (AD&D)"],"Voluntary Benefits":["Hospital Indemnity","Accident Insurance","Critical Illness Insurance","Cancer Insurance","Gap Insurance","Legal Insurance","Identity Theft Protection","Accident & Illness (Pets)","Nursing Care / Custodial Care"],"Wellness & Mental Health":["Wellness Programs","Employee Assistance Program","Gym Membership"]};return{success:!0,data:{planTypes:["PPO","HMO","HDHP","MEC","EPO","POS","Indemnity","Term Life","Whole Life","STD","LTD"],coverageCategories:Object.keys(e),coverageMap:e,metalTiers:["Bronze","Silver","Gold","Platinum","Catastrophic"]}}},i=async()=>{try{let e=await fetch(`${n}/api/pre-enrollment/carriers/assignable`,{method:"GET",headers:o()});if(!e.ok){let t=await e.text();try{let e=await fetch(`${n}/api/pre-enrollment/carriers?isSystemCarrier=true`,{method:"GET",headers:o()});if(e.ok){let t=await e.json();return{success:!0,data:t.carriers||[]}}}catch(e){}throw Error(`HTTP error! status: ${e.status}, message: ${t}`)}let t=await e.json();return{success:!0,data:t.carriers||[]}}catch(e){return{success:!1,error:e instanceof Error?e.message:"Failed to fetch carriers"}}},c=async e=>{try{let t=await s.be.post("/api/pre-enrollment/plans",e);return{success:!0,data:t.data}}catch(e){return{success:!1,error:e.response?.data?.error||e.response?.data?.message||e.message||"Failed to create plan"}}},d=async e=>{try{let t=new URLSearchParams;e&&(console.log("\uD83D\uDD27 getPlans called with filters:",e),Object.entries(e).forEach(([e,r])=>{void 0!==r&&t.append(e,r.toString())}));let r=`/api/pre-enrollment/plans${t.toString()?`?${t.toString()}`:""}`;console.log("\uD83C\uDF10 Fetching plans from endpoint:",r);let a=(await s.be.get(r)).data;console.log("\uD83D\uDCE6 getPlans response:",a),console.log("\uD83D\uDCCA Plans returned:",a.plans?.length||0);let n=(a.plans||[]).map((e,t)=>{let r;if(console.log(`🔍 Plan ${t} raw data structure:`,{hasDoc:!!e._doc,hasId:!!e._id,docId:e._doc?._id,directId:e._id}),e._doc)r={...e._doc,_id:e._doc._id||e._id,...e.carrierData&&{carrierData:e.carrierData}},console.log(`📄 Plan ${t} extracted from _doc:`,r);else try{r=JSON.parse(JSON.stringify(e))}catch(a){console.warn(`⚠️ JSON transform failed for plan ${t}:`,a),r={...e}}return r._id?"object"==typeof r._id&&r._id.toString?r._id=r._id.toString():"string"!=typeof r._id&&(r._id=String(r._id)):console.warn(`⚠️ Plan ${t} still missing _id after transformation`),console.log(`✅ Plan ${t} final _id:`,r._id),r});return console.log("\uD83D\uDD04 Transformed plans sample:",n[0]),{success:!0,data:{plans:n,count:a.count,pagination:a.pagination}}}catch(e){return{success:!1,error:"Failed to fetch plans"}}},u=async(e,t)=>{try{let r=await s.be.put(`/api/pre-enrollment/plans/${e}`,t);return{success:!0,data:r.data}}catch(e){return{success:!1,error:e.response?.data?.error||e.response?.data?.message||e.message||"Failed to update plan"}}},p=async(e,t)=>{try{let r=new FormData;t.forEach(e=>{r.append("documents",e)});let s=await fetch(`${n}/api/pre-enrollment/plans/${e}/documents`,{method:"POST",headers:{"user-id":(0,a.n5)()},body:r});if(!s.ok){let e=await s.json();throw Error(e.error||`HTTP error! status: ${s.status}`)}let o=await s.json();return{success:!0,data:o}}catch(e){return{success:!1,error:e instanceof Error?e.message:"Failed to upload documents"}}},f=async(e,t)=>{try{console.log("\uD83D\uDD0D Checking for duplicate plan name:",e,t?`(excluding ${t})`:"");let r=await d({planName:e,strict:!0});if(console.log("\uD83D\uDCCA Plan name duplicate check result:",r),console.log("\uD83D\uDCCA Number of plans returned:",r.data?.plans?.length||0),!r.success)return console.log("❌ Plan name check failed:",r.error),{success:!1,error:r.error||"Failed to fetch plans for duplicate check"};let a=r.data?.plans||[],s=t?a.filter(e=>e._id!==t):a,n=s.length>0?s[0]:void 0;return console.log("\uD83C\uDFAF Duplicate plan found:",n?`YES - ${n.planName}`:"NO",t?`(excluded ${t})`:""),{success:!0,data:{isDuplicate:!!n,existingPlan:n}}}catch(e){return console.log("\uD83D\uDCA5 Plan name check error:",e),{success:!1,error:"Failed to check for duplicate plan name"}}},g=async(e,t)=>{try{console.log("\uD83D\uDD0D Checking for duplicate plan code:",e,t?`(excluding ${t})`:"");let r=await d({planCode:e,strict:!0});if(console.log("\uD83D\uDCCA Plan code duplicate check result:",r),console.log("\uD83D\uDCCA Number of plans returned:",r.data?.plans?.length||0),!r.success)return console.log("❌ Plan code check failed:",r.error),{success:!1,error:r.error||"Failed to fetch plans for duplicate check"};let a=r.data?.plans||[],s=t?a.filter(e=>e._id!==t):a,n=s.length>0?s[0]:void 0;return console.log("\uD83C\uDFAF Duplicate plan found:",n?`YES - ${n.planCode}`:"NO",t?`(excluded ${t})`:""),{success:!0,data:{isDuplicate:!!n,existingPlan:n}}}catch(e){return console.log("\uD83D\uDCA5 Plan code check error:",e),{success:!1,error:"Failed to check for duplicate plan code"}}}},67925:(e,t,r)=>{"use strict";r.d(t,{Z:()=>x});var a=r(10326),s=r(17577),n=r(6283),o=r(25609),l=r(33198),i=r(42265),c=r(82400),d=r(46226),u=r(22758),p=r(31870),f=r(25842),g=r(30656),h=r(88563),m=r(35047);let x=()=>{let e=(0,m.useRouter)(),{logout:t}=(0,u.a)(),r=(0,p.C)(e=>e.user.userProfile),[x,D]=(0,s.useState)(!1);(0,s.useEffect)(()=>{D("true"===localStorage.getItem("isTeamsApp1"))},[]);let y=(0,f.v9)(e=>e.user.userProfile.isAdmin),C=(0,f.v9)(e=>e.user.userProfile.isBroker);return a.jsx(n.Z,{sx:{bgcolor:"white",padding:2,textAlign:"center",height:"80px",borderBottom:"1px solid #D2D2D2",position:"sticky",top:0,zIndex:50,boxShadow:"0 1px 3px 0 rgba(0, 0, 0, 0.1)"},children:(0,a.jsxs)(n.Z,{sx:{display:"flex",alignItems:"center",justifyContent:"space-between"},children:[(0,a.jsxs)(n.Z,{sx:{display:"flex",alignItems:"center"},children:[(0,a.jsxs)(n.Z,{sx:{display:"flex",alignItems:"center",cursor:"pointer",mr:3},onClick:()=>{e.push("/dashboard")},children:[a.jsx(d.default,{src:g.Z,alt:"BenOsphere Logo",width:40,height:40}),a.jsx(o.Z,{sx:{fontWeight:800,fontSize:"1.5rem",ml:1,color:"#111827"},children:"BenOsphere"})]}),a.jsx(l.Z,{sx:{bgcolor:"black",color:"#ffffff",width:35,height:35,fontSize:"1.2rem",mr:1.5,ml:2,display:"flex",alignItems:"center",justifyContent:"center",paddingBottom:"1px",fontWeight:800},children:(e=>{if(!e)return"";let[t,r]=e.split(" ");return`${t[0].toUpperCase()}${r?r[0].toUpperCase():""}`})(r.name)}),a.jsx(o.Z,{variant:"h4",sx:{mb:0,fontWeight:"bold",display:"flex",alignItems:"center",justifyContent:"flex-start",textAlign:"flex-start",fontSize:"16px",mr:1.5,color:"#111827"},children:r.name.replace(/\b\w/g,e=>e.toUpperCase())}),y&&a.jsx(n.Z,{sx:{bgcolor:"rgba(0, 0, 0, 0.06)",borderRadius:"8px",padding:"4px 8px",display:"flex",alignItems:"center",justifyContent:"center",fontWeight:"bold",fontSize:"12px",color:"#333",mr:1.5},children:"ADMIN"}),C&&a.jsx(n.Z,{sx:{bgcolor:"#f5f5f5",borderRadius:"8px",padding:"2px 8px",display:"flex",alignItems:"center",justifyContent:"center",fontWeight:"bold",fontSize:"0.9rem",color:"#333",mr:1.5},children:"BROKER"})]}),(0,a.jsxs)(n.Z,{sx:{display:"flex",alignItems:"center"},children:[!x&&(0,a.jsxs)(i.Z,{sx:{backgroundColor:"transparent",color:"#333",textTransform:"none",padding:"8px 8px",display:"flex",alignItems:"center",justifyContent:"flex-start",gap:"2px","&:hover":{backgroundColor:"transparent",color:"#555"},boxShadow:"none"},children:[a.jsx(n.Z,{sx:{mt:.5,mr:.5},children:a.jsx(h.Z,{})}),a.jsx(o.Z,{sx:{fontWeight:500,fontSize:"14px",color:"#333"},children:"Guide"})]}),!x&&(0,a.jsxs)(i.Z,{onClick:t,sx:{backgroundColor:"transparent",color:"#333",marginLeft:1,textTransform:"none",padding:"8px 16px",display:"flex",alignItems:"center",justifyContent:"flex-start",gap:"8px","&:hover":{backgroundColor:"transparent",color:"#555"},boxShadow:"none"},children:[a.jsx(c.Z,{sx:{fontSize:"18px"}}),a.jsx(o.Z,{sx:{fontWeight:500,fontSize:"14px",color:"#333"},children:"Logout"})]})]})]})})}},91596:()=>{}};