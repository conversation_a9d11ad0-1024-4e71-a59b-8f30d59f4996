exports.id=889,exports.ids=[889],exports.modules={6283:(e,t,r)=>{"use strict";r.d(t,{Z:()=>d});var a=r(96830),o=r(5028),n=r(5283),i=r(14750);let s=(0,r(71685).Z)("MuiBox",["root"]),l=(0,n.Z)(),d=(0,a.default)({themeId:i.Z,defaultTheme:l,defaultClassName:s.root,generateClassName:o.Z.generate})},98139:(e,t,r)=>{"use strict";r.d(t,{Z:()=>E});var a=r(17577),o=r(41135),n=r(88634),i=r(8106),s=r(91703),l=r(13643),d=r(2791),c=r(54641),u=r(40955),m=r(71685),p=r(97898);function y(e){return(0,p.ZP)("MuiCircularProgress",e)}(0,m.Z)("MuiCircularProgress",["root","determinate","indeterminate","colorPrimary","colorSecondary","svg","circle","circleDeterminate","circleIndeterminate","circleDisableShrink"]);var f=r(10326);let g=(0,i.F4)`
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
`,h=(0,i.F4)`
  0% {
    stroke-dasharray: 1px, 200px;
    stroke-dashoffset: 0;
  }

  50% {
    stroke-dasharray: 100px, 200px;
    stroke-dashoffset: -15px;
  }

  100% {
    stroke-dasharray: 1px, 200px;
    stroke-dashoffset: -126px;
  }
`,v="string"!=typeof g?(0,i.iv)`
        animation: ${g} 1.4s linear infinite;
      `:null,I="string"!=typeof h?(0,i.iv)`
        animation: ${h} 1.4s ease-in-out infinite;
      `:null,b=e=>{let{classes:t,variant:r,color:a,disableShrink:o}=e,i={root:["root",r,`color${(0,c.Z)(a)}`],svg:["svg"],circle:["circle",`circle${(0,c.Z)(r)}`,o&&"circleDisableShrink"]};return(0,n.Z)(i,y,t)},S=(0,s.default)("span",{name:"MuiCircularProgress",slot:"Root",overridesResolver:(e,t)=>{let{ownerState:r}=e;return[t.root,t[r.variant],t[`color${(0,c.Z)(r.color)}`]]}})((0,l.Z)(({theme:e})=>({display:"inline-block",variants:[{props:{variant:"determinate"},style:{transition:e.transitions.create("transform")}},{props:{variant:"indeterminate"},style:v||{animation:`${g} 1.4s linear infinite`}},...Object.entries(e.palette).filter((0,u.Z)()).map(([t])=>({props:{color:t},style:{color:(e.vars||e).palette[t].main}}))]}))),k=(0,s.default)("svg",{name:"MuiCircularProgress",slot:"Svg",overridesResolver:(e,t)=>t.svg})({display:"block"}),w=(0,s.default)("circle",{name:"MuiCircularProgress",slot:"Circle",overridesResolver:(e,t)=>{let{ownerState:r}=e;return[t.circle,t[`circle${(0,c.Z)(r.variant)}`],r.disableShrink&&t.circleDisableShrink]}})((0,l.Z)(({theme:e})=>({stroke:"currentColor",variants:[{props:{variant:"determinate"},style:{transition:e.transitions.create("stroke-dashoffset")}},{props:{variant:"indeterminate"},style:{strokeDasharray:"80px, 200px",strokeDashoffset:0}},{props:({ownerState:e})=>"indeterminate"===e.variant&&!e.disableShrink,style:I||{animation:`${h} 1.4s ease-in-out infinite`}}]}))),E=a.forwardRef(function(e,t){let r=(0,d.i)({props:e,name:"MuiCircularProgress"}),{className:a,color:n="primary",disableShrink:i=!1,size:s=40,style:l,thickness:c=3.6,value:u=0,variant:m="indeterminate",...p}=r,y={...r,color:n,disableShrink:i,size:s,thickness:c,value:u,variant:m},g=b(y),h={},v={},I={};if("determinate"===m){let e=2*Math.PI*((44-c)/2);h.strokeDasharray=e.toFixed(3),I["aria-valuenow"]=Math.round(u),h.strokeDashoffset=`${((100-u)/100*e).toFixed(3)}px`,v.transform="rotate(-90deg)"}return(0,f.jsx)(S,{className:(0,o.Z)(g.root,a),style:{width:s,height:s,...v,...l},ownerState:y,ref:t,role:"progressbar",...I,...p,children:(0,f.jsx)(k,{className:g.svg,ownerState:y,viewBox:"22 22 44 44",children:(0,f.jsx)(w,{className:g.circle,style:h,ownerState:y,cx:44,cy:44,r:(44-c)/2,fill:"none",strokeWidth:c})})})})},2791:(e,t,r)=>{"use strict";r.d(t,{i:()=>o}),r(17577);var a=r(51387);function o(e){return(0,a.i)(e)}r(10326)},54641:(e,t,r)=>{"use strict";r.d(t,{Z:()=>a});let a=r(96005).Z},40955:(e,t,r)=>{"use strict";function a(e=[]){return([,t])=>t&&function(e,t=[]){if("string"!=typeof e.main)return!1;for(let r of t)if(!e.hasOwnProperty(r)||"string"!=typeof e[r])return!1;return!0}(t,e)}r.d(t,{Z:()=>a})},13643:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n});var a=r(15966);let o={theme:void 0},n=function(e){let t,r;return function(n){let i=t;return(void 0===i||n.theme!==r)&&(o.theme=n.theme,t=i=(0,a.Z)(e(o)),r=n.theme),i}}},43058:(e,t,r)=>{"use strict";r.d(t,{Z:()=>u});var a=r(10326),o=r(17577),n=r(22758),i=r(35047),s=r(31870);r(32049),r(94638);var l=r(98139),d=r(6283);let c=()=>/Mobi|Android/i.test(navigator.userAgent),u=({children:e})=>{let{user:t,loading:r}=(0,n.a)(),u=(0,i.useRouter)(),m=(0,i.usePathname)(),p=(0,s.T)(),[y,f]=(0,o.useState)(!1),g=(0,s.C)(e=>e.user.userProfile);return((0,o.useEffect)(()=>{},[p,g.name]),(0,o.useEffect)(()=>{console.log("ProtectedRoute useEffect triggered"),console.log("Current user: ",t),console.log("Loading state: ",r),console.log("Current user details: ",g),r||t||(console.log("User not authenticated, redirecting to home"),f(!1),u.push("/")),!r&&g.companyId&&""===g.companyId&&(console.log("Waiting to retrieve company details"),f(!1)),!r&&g.companyId&&""!==g.companyId&&(console.log("User found, rendering children"),f(!0)),c()&&!m.startsWith("/mobile")&&(console.log(`Redirecting to mobile version of ${m}`),u.push(`/mobile${m}`))},[t,r,g,u,m]),y)?t?a.jsx(a.Fragment,{children:e}):null:a.jsx(d.Z,{sx:{display:"flex",justifyContent:"center",alignItems:"center",height:"100vh",bgcolor:"#f6f8fc"},children:a.jsx(l.Z,{})})}},94638:(e,t,r)=>{"use strict";r.d(t,{G9:()=>h,JZ:()=>v,M_:()=>y,N:()=>d,Nq:()=>p,TQ:()=>u,Ur:()=>s,aE:()=>c,aK:()=>S,dA:()=>l,gt:()=>I,mb:()=>g,qB:()=>b,yu:()=>m,zX:()=>f});var a=r(53148),o=r(39352),n=r(25748),i=r(32049);function s(e){switch(e){case"Our Culture & Workplace":return"Work Policies";case"Protecting Your Income":return"Income Security";default:return e}}function l(e){switch(e){case"On-site Amenities":return"Company Handbook";case"Protecting Your Income":return"Income Security";case"Got married/divorced":return"Marriage or Divorce";case"Had a baby or adopted a baby":return"New Baby or Adoption";case"Lost your insurance":return"Loss of Insurance";default:return e}}async function d(e,t){let r=await (0,a.A_)("/benefits/benefit-types",{companyId:t});return console.log("COMPANY BENEFIT TYPES RESPONSE: ",r.benefitTypes),e((0,o.x7)(r.benefitTypes)),r.benefitTypes}async function c(e,t){let r=await (0,a.A_)("/benefits/all-benefits",{companyId:t});console.log("COMPANY BENEFIT TYPES WITH BENEFITS RESPONSE: ",r),e((0,n.US)(r.benefitsPerType))}async function u(e){let t=await (0,a.A_)("/admin/all-employees");return console.log("COMPANY TEAM MEMBERS RESPONSE: ",t),e((0,o.Vv)(t.employees)),t.employees}async function m(e,t){return console.log("ADDING USERS: ",t),await (0,a.j0)("/admin/add/employees",{employeeList:t})}async function p(e,t,r){try{console.log("\uD83D\uDD0D Debug: User being updated:",t);let e={employeeId:t,updatedDetails:{name:r.name,email:r.email,details:{phoneNumber:r.phoneNumber||"",department:r.department||"",title:r.title||"",role:r.title||""}}};return r.dateOfBirth&&(e.updatedDetails.details.dateOfBirth=r.dateOfBirth),r.hireDate&&(e.updatedDetails.details.hireDate=r.hireDate),r.annualSalary&&(e.updatedDetails.details.annualSalary=r.annualSalary),r.employeeClassType&&(e.updatedDetails.details.employeeClassType=r.employeeClassType),r.workSchedule&&(e.updatedDetails.details.workSchedule=r.workSchedule),r.ssn&&(e.updatedDetails.details.ssn=r.ssn),r.employeeId&&(e.updatedDetails.details.employeeId=r.employeeId),r.workLocation&&(e.updatedDetails.details.workLocation=r.workLocation),r.address&&(e.updatedDetails.details.address=r.address),r.mailingAddress&&(e.updatedDetails.details.mailingAddress=r.mailingAddress),r.emergencyContact&&(e.updatedDetails.details.emergencyContact=r.emergencyContact),e.updatedDetails.details.dependents=r.dependents||[],console.log("Middleware - dependents being sent:",e.updatedDetails.details.dependents),console.log("Sending PUT request with cleaned data:",e),await (0,a.GH)("/admin/update/employee",e)}catch(e){throw console.error("Error in updateUser middleware:",e),e}}async function y(e,t){let r=await (0,a.A_)("/employee",{"user-id":t});return e((0,i.$l)({name:r.currentUser.name,email:r.currentUser.email,companyId:r.currentUser.companyId,role:r.currentUser.role,isAdmin:r.currentUser.isAdmin,isBroker:r.currentUser.isBroker,details:r.currentUser.details})),r}async function f(e,t,r){let o=await (0,a.j0)("/admin/onboard",{company:{name:t.name,adminEmail:t.adminEmail,adminRole:t.adminRole,companySize:t.companySize,industry:t.industry,location:t.location,website:t.website,howHeard:t.howHeard,brokerId:t.brokerId,brokerageId:t.brokerageId,isBrokerage:t.isBrokerage,isActivated:t.isActivated,referralSource:t.referralSource,details:{logo:""}},user:{email:r.email,name:r.name,role:r.role,isAdmin:r.isAdmin,isBroker:r.isBroker,isActivated:r.isActivated}}),n=o.data.userId,i=o.data.companyId;return localStorage.setItem("userid1",n),localStorage.setItem("companyId1",i),o}async function g(e,t){return console.log("SENDING LOGIN LINK TO EMPLOYEE: ",e,t),await (0,a.j0)("/admin/send-user-login-link",{userId:e,companyId:t})}async function h(e,t,r,o){let n=await (0,a.j0)("/admin/add/employer",{brokerId:e,companyName:t,companyAdminEmail:r,companyAdminName:o});return console.log("BROKER ADDS COMPANY RESPONSE: ",n),n}async function v(e,t){return 200===(await (0,a.j0)("/employee/offboard/",{userId:e,companyId:t})).status}async function I(e,t){return await (0,a.j0)("/employee/enable/",{userId:e,companyId:t})}async function b(e,t){try{let t=await (0,a.A_)("/admin/all-companies");console.log("CLIENT COMPANIES UNDER BROKER: ",t);let r=t.companies||[];try{let e=await (0,a.A_)("/employee/company-details");console.log("BROKER'S OWN COMPANY: ",e),e.company&&e.company.isBrokerage&&!r.some(t=>t._id===e.company._id)&&(r.unshift(e.company),console.log("Added broker's own company to the list"))}catch(e){console.log("Could not fetch broker's own company (this is normal if user is not a broker):",e)}return console.log("ALL COMPANIES (CLIENT + OWN): ",r),e((0,i.Ym)(r)),{...t,companies:r}}catch(t){return console.error("Error fetching companies:",t),e((0,i.Ym)([])),{companies:[]}}}async function S(e){let t=await (0,a.A_)("/employee/company-details");return e((0,o.sy)(t.company)),t.status}},31870:(e,t,r)=>{"use strict";r.d(t,{C:()=>n,T:()=>o});var a=r(25842);let o=()=>(0,a.I0)(),n=a.v9},73881:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>o});var a=r(66621);let o=e=>[{type:"image/x-icon",sizes:"16x16",url:(0,a.fillMetadataSegment)(".",e.params,"favicon.ico")+""}]},61813:()=>{}};