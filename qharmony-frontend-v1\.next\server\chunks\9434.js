"use strict";exports.id=9434,exports.ids=[9434],exports.modules={3423:(e,t,r)=>{r.d(t,{Z:()=>n});var o=r(51426),l=r(10326);let n=(0,o.Z)((0,l.jsx)("path",{d:"M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6z"}),"Add")},75887:(e,t,r)=>{r.d(t,{Z:()=>n});var o=r(51426),l=r(10326);let n=(0,o.Z)((0,l.jsx)("path",{d:"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2m-2 15-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8z"}),"CheckCircle")},36690:(e,t,r)=>{r.d(t,{Z:()=>n});var o=r(51426),l=r(10326);let n=(0,o.Z)((0,l.jsx)("path",{d:"M19 6.41 17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"}),"Close")},96889:(e,t,r)=>{r.d(t,{Z:()=>n});var o=r(51426),l=r(10326);let n=(0,o.Z)((0,l.jsx)("path",{d:"M6 19c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2V7H6zM19 4h-3.5l-1-1h-5l-1 1H5v2h14z"}),"Delete")},67840:(e,t,r)=>{r.d(t,{Z:()=>n});var o=r(51426),l=r(10326);let n=(0,o.Z)((0,l.jsx)("path",{d:"M3 17.25V21h3.75L17.81 9.94l-3.75-3.75zM20.71 7.04c.39-.39.39-1.02 0-1.41l-2.34-2.34a.996.996 0 0 0-1.41 0l-1.83 1.83 3.75 3.75z"}),"Edit")},8387:(e,t,r)=>{r.d(t,{Z:()=>b});var o=r(17577),l=r(41135),n=r(88634),a=r(91703),i=r(2791),s=r(71685),c=r(97898);function d(e){return(0,c.ZP)("MuiCardActions",e)}(0,s.Z)("MuiCardActions",["root","spacing"]);var p=r(10326);let u=e=>{let{classes:t,disableSpacing:r}=e;return(0,n.Z)({root:["root",!r&&"spacing"]},d,t)},f=(0,a.default)("div",{name:"MuiCardActions",slot:"Root",overridesResolver:(e,t)=>{let{ownerState:r}=e;return[t.root,!r.disableSpacing&&t.spacing]}})({display:"flex",alignItems:"center",padding:8,variants:[{props:{disableSpacing:!1},style:{"& > :not(style) ~ :not(style)":{marginLeft:8}}}]}),b=o.forwardRef(function(e,t){let r=(0,i.i)({props:e,name:"MuiCardActions"}),{disableSpacing:o=!1,className:n,...a}=r,s={...r,disableSpacing:o},c=u(s);return(0,p.jsx)(f,{className:(0,l.Z)(c.root,n),ownerState:s,ref:t,...a})})},4766:(e,t,r)=>{r.d(t,{Z:()=>b});var o=r(17577),l=r(41135),n=r(88634),a=r(91703),i=r(2791),s=r(71685),c=r(97898);function d(e){return(0,c.ZP)("MuiCardContent",e)}(0,s.Z)("MuiCardContent",["root"]);var p=r(10326);let u=e=>{let{classes:t}=e;return(0,n.Z)({root:["root"]},d,t)},f=(0,a.default)("div",{name:"MuiCardContent",slot:"Root",overridesResolver:(e,t)=>t.root})({padding:16,"&:last-child":{paddingBottom:24}}),b=o.forwardRef(function(e,t){let r=(0,i.i)({props:e,name:"MuiCardContent"}),{className:o,component:n="div",...a}=r,s={...r,component:n},c=u(s);return(0,p.jsx)(f,{as:n,className:(0,l.Z)(c.root,o),ownerState:s,ref:t,...a})})},34039:(e,t,r)=>{r.d(t,{Z:()=>v});var o=r(17577),l=r(41135),n=r(88634),a=r(91703),i=r(2791),s=r(89178),c=r(71685),d=r(97898);function p(e){return(0,d.ZP)("MuiCard",e)}(0,c.Z)("MuiCard",["root"]);var u=r(10326);let f=e=>{let{classes:t}=e;return(0,n.Z)({root:["root"]},p,t)},b=(0,a.default)(s.Z,{name:"MuiCard",slot:"Root",overridesResolver:(e,t)=>t.root})({overflow:"hidden"}),v=o.forwardRef(function(e,t){let r=(0,i.i)({props:e,name:"MuiCard"}),{className:o,raised:n=!1,...a}=r,s={...r,raised:n},c=f(s);return(0,u.jsx)(b,{className:(0,l.Z)(c.root,o),elevation:n?8:void 0,ref:t,ownerState:s,...a})})},37841:(e,t,r)=>{r.d(t,{Z:()=>M});var o=r(17577),l=r(41135),n=r(88634),a=r(44823),i=r(27080),s=r(91703),c=r(13643),d=r(2791),p=r(92992),u=r(49006),f=r(69408),b=r(37382),v=r(73025),h=r(41598),m=r(25310),y=r(71685),g=r(97898);function x(e){return(0,g.ZP)("MuiMenuItem",e)}let Z=(0,y.Z)("MuiMenuItem",["root","focusVisible","dense","disabled","divider","gutters","selected"]);var S=r(10326);let w=e=>{let{disabled:t,dense:r,divider:o,disableGutters:l,selected:a,classes:i}=e,s=(0,n.Z)({root:["root",r&&"dense",t&&"disabled",!l&&"gutters",o&&"divider",a&&"selected"]},x,i);return{...i,...s}},C=(0,s.default)(u.Z,{shouldForwardProp:e=>(0,i.Z)(e)||"classes"===e,name:"MuiMenuItem",slot:"Root",overridesResolver:(e,t)=>{let{ownerState:r}=e;return[t.root,r.dense&&t.dense,r.divider&&t.divider,!r.disableGutters&&t.gutters]}})((0,c.Z)(({theme:e})=>({...e.typography.body1,display:"flex",justifyContent:"flex-start",alignItems:"center",position:"relative",textDecoration:"none",minHeight:48,paddingTop:6,paddingBottom:6,boxSizing:"border-box",whiteSpace:"nowrap","&:hover":{textDecoration:"none",backgroundColor:(e.vars||e).palette.action.hover,"@media (hover: none)":{backgroundColor:"transparent"}},[`&.${Z.selected}`]:{backgroundColor:e.vars?`rgba(${e.vars.palette.primary.mainChannel} / ${e.vars.palette.action.selectedOpacity})`:(0,a.Fq)(e.palette.primary.main,e.palette.action.selectedOpacity),[`&.${Z.focusVisible}`]:{backgroundColor:e.vars?`rgba(${e.vars.palette.primary.mainChannel} / calc(${e.vars.palette.action.selectedOpacity} + ${e.vars.palette.action.focusOpacity}))`:(0,a.Fq)(e.palette.primary.main,e.palette.action.selectedOpacity+e.palette.action.focusOpacity)}},[`&.${Z.selected}:hover`]:{backgroundColor:e.vars?`rgba(${e.vars.palette.primary.mainChannel} / calc(${e.vars.palette.action.selectedOpacity} + ${e.vars.palette.action.hoverOpacity}))`:(0,a.Fq)(e.palette.primary.main,e.palette.action.selectedOpacity+e.palette.action.hoverOpacity),"@media (hover: none)":{backgroundColor:e.vars?`rgba(${e.vars.palette.primary.mainChannel} / ${e.vars.palette.action.selectedOpacity})`:(0,a.Fq)(e.palette.primary.main,e.palette.action.selectedOpacity)}},[`&.${Z.focusVisible}`]:{backgroundColor:(e.vars||e).palette.action.focus},[`&.${Z.disabled}`]:{opacity:(e.vars||e).palette.action.disabledOpacity},[`& + .${v.Z.root}`]:{marginTop:e.spacing(1),marginBottom:e.spacing(1)},[`& + .${v.Z.inset}`]:{marginLeft:52},[`& .${m.Z.root}`]:{marginTop:0,marginBottom:0},[`& .${m.Z.inset}`]:{paddingLeft:36},[`& .${h.Z.root}`]:{minWidth:36},variants:[{props:({ownerState:e})=>!e.disableGutters,style:{paddingLeft:16,paddingRight:16}},{props:({ownerState:e})=>e.divider,style:{borderBottom:`1px solid ${(e.vars||e).palette.divider}`,backgroundClip:"padding-box"}},{props:({ownerState:e})=>!e.dense,style:{[e.breakpoints.up("sm")]:{minHeight:"auto"}}},{props:({ownerState:e})=>e.dense,style:{minHeight:32,paddingTop:4,paddingBottom:4,...e.typography.body2,[`& .${h.Z.root} svg`]:{fontSize:"1.25rem"}}}]}))),M=o.forwardRef(function(e,t){let r;let n=(0,d.i)({props:e,name:"MuiMenuItem"}),{autoFocus:a=!1,component:i="li",dense:s=!1,divider:c=!1,disableGutters:u=!1,focusVisibleClassName:v,role:h="menuitem",tabIndex:m,className:y,...g}=n,x=o.useContext(p.Z),Z=o.useMemo(()=>({dense:s||x.dense||!1,disableGutters:u}),[x.dense,s,u]),M=o.useRef(null);(0,f.Z)(()=>{a&&M.current&&M.current.focus()},[a]);let B={...n,dense:Z.dense,divider:c,disableGutters:u},R=w(n),I=(0,b.Z)(M,t);return n.disabled||(r=void 0!==m?m:-1),(0,S.jsx)(p.Z.Provider,{value:Z,children:(0,S.jsx)(C,{ref:I,role:h,tabIndex:r,component:i,focusVisibleClassName:(0,l.Z)(R.focusVisible,v),className:(0,l.Z)(R.root,y),...g,ownerState:B,classes:R})})})},45112:(e,t,r)=>{r.d(t,{Z:()=>y});var o=r(17577),l=r(41135),n=r(88634),a=r(49006),i=r(54641),s=r(91703),c=r(13643),d=r(2791),p=r(71685),u=r(97898);function f(e){return(0,u.ZP)("MuiTab",e)}let b=(0,p.Z)("MuiTab",["root","labelIcon","textColorInherit","textColorPrimary","textColorSecondary","selected","disabled","fullWidth","wrapped","iconWrapper","icon"]);var v=r(10326);let h=e=>{let{classes:t,textColor:r,fullWidth:o,wrapped:l,icon:a,label:s,selected:c,disabled:d}=e,p={root:["root",a&&s&&"labelIcon",`textColor${(0,i.Z)(r)}`,o&&"fullWidth",l&&"wrapped",c&&"selected",d&&"disabled"],icon:["iconWrapper","icon"]};return(0,n.Z)(p,f,t)},m=(0,s.default)(a.Z,{name:"MuiTab",slot:"Root",overridesResolver:(e,t)=>{let{ownerState:r}=e;return[t.root,r.label&&r.icon&&t.labelIcon,t[`textColor${(0,i.Z)(r.textColor)}`],r.fullWidth&&t.fullWidth,r.wrapped&&t.wrapped,{[`& .${b.iconWrapper}`]:t.iconWrapper},{[`& .${b.icon}`]:t.icon}]}})((0,c.Z)(({theme:e})=>({...e.typography.button,maxWidth:360,minWidth:90,position:"relative",minHeight:48,flexShrink:0,padding:"12px 16px",overflow:"hidden",whiteSpace:"normal",textAlign:"center",lineHeight:1.25,variants:[{props:({ownerState:e})=>e.label&&("top"===e.iconPosition||"bottom"===e.iconPosition),style:{flexDirection:"column"}},{props:({ownerState:e})=>e.label&&"top"!==e.iconPosition&&"bottom"!==e.iconPosition,style:{flexDirection:"row"}},{props:({ownerState:e})=>e.icon&&e.label,style:{minHeight:72,paddingTop:9,paddingBottom:9}},{props:({ownerState:e,iconPosition:t})=>e.icon&&e.label&&"top"===t,style:{[`& > .${b.icon}`]:{marginBottom:6}}},{props:({ownerState:e,iconPosition:t})=>e.icon&&e.label&&"bottom"===t,style:{[`& > .${b.icon}`]:{marginTop:6}}},{props:({ownerState:e,iconPosition:t})=>e.icon&&e.label&&"start"===t,style:{[`& > .${b.icon}`]:{marginRight:e.spacing(1)}}},{props:({ownerState:e,iconPosition:t})=>e.icon&&e.label&&"end"===t,style:{[`& > .${b.icon}`]:{marginLeft:e.spacing(1)}}},{props:{textColor:"inherit"},style:{color:"inherit",opacity:.6,[`&.${b.selected}`]:{opacity:1},[`&.${b.disabled}`]:{opacity:(e.vars||e).palette.action.disabledOpacity}}},{props:{textColor:"primary"},style:{color:(e.vars||e).palette.text.secondary,[`&.${b.selected}`]:{color:(e.vars||e).palette.primary.main},[`&.${b.disabled}`]:{color:(e.vars||e).palette.text.disabled}}},{props:{textColor:"secondary"},style:{color:(e.vars||e).palette.text.secondary,[`&.${b.selected}`]:{color:(e.vars||e).palette.secondary.main},[`&.${b.disabled}`]:{color:(e.vars||e).palette.text.disabled}}},{props:({ownerState:e})=>e.fullWidth,style:{flexShrink:1,flexGrow:1,flexBasis:0,maxWidth:"none"}},{props:({ownerState:e})=>e.wrapped,style:{fontSize:e.typography.pxToRem(12)}}]}))),y=o.forwardRef(function(e,t){let r=(0,d.i)({props:e,name:"MuiTab"}),{className:n,disabled:a=!1,disableFocusRipple:i=!1,fullWidth:s,icon:c,iconPosition:p="top",indicator:u,label:f,onChange:b,onClick:y,onFocus:g,selected:x,selectionFollowsFocus:Z,textColor:S="inherit",value:w,wrapped:C=!1,...M}=r,B={...r,disabled:a,disableFocusRipple:i,selected:x,icon:!!c,iconPosition:p,label:!!f,fullWidth:s,textColor:S,wrapped:C},R=h(B),I=c&&f&&o.isValidElement(c)?o.cloneElement(c,{className:(0,l.Z)(R.icon,c.props.className)}):c;return(0,v.jsxs)(m,{focusRipple:!i,className:(0,l.Z)(R.root,n),ref:t,role:"tab","aria-selected":x,disabled:a,onClick:e=>{!x&&b&&b(e,w),y&&y(e)},onFocus:e=>{Z&&!x&&b&&b(e,w),g&&g(e)},ownerState:B,tabIndex:x?0:-1,...M,children:["top"===p||"start"===p?(0,v.jsxs)(o.Fragment,{children:[I,f]}):(0,v.jsxs)(o.Fragment,{children:[f,I]}),u]})})},87721:(e,t,r)=>{r.d(t,{Z:()=>V});var o=r(17577),l=r(41135),n=r(88634),a=r(93244),i=r(69800),s=r(91703),c=r(23743),d=r(13643),p=r(2791),u=r(76731);function f(e){return(1+Math.sin(Math.PI*e-Math.PI/2))/2}var b=r(69408),v=r(22462),h=r(10326);let m={width:99,height:99,position:"absolute",top:-9999,overflow:"scroll"};var y=r(51426);let g=(0,y.Z)((0,h.jsx)("path",{d:"M15.41 16.09l-4.58-4.59 4.58-4.59L14 5.5l-6 6 6 6z"}),"KeyboardArrowLeft"),x=(0,y.Z)((0,h.jsx)("path",{d:"M8.59 16.34l4.58-4.59-4.58-4.59L10 5.75l6 6-6 6z"}),"KeyboardArrowRight");var Z=r(49006),S=r(71685),w=r(97898);function C(e){return(0,w.ZP)("MuiTabScrollButton",e)}let M=(0,S.Z)("MuiTabScrollButton",["root","vertical","horizontal","disabled"]),B=e=>{let{classes:t,orientation:r,disabled:o}=e;return(0,n.Z)({root:["root",r,o&&"disabled"]},C,t)},R=(0,s.default)(Z.Z,{name:"MuiTabScrollButton",slot:"Root",overridesResolver:(e,t)=>{let{ownerState:r}=e;return[t.root,r.orientation&&t[r.orientation]]}})({width:40,flexShrink:0,opacity:.8,[`&.${M.disabled}`]:{opacity:0},variants:[{props:{orientation:"vertical"},style:{width:"100%",height:40,"& svg":{transform:"var(--TabScrollButton-svgRotate)"}}}]}),I=o.forwardRef(function(e,t){let r=(0,p.i)({props:e,name:"MuiTabScrollButton"}),{className:o,slots:n={},slotProps:s={},direction:c,orientation:d,disabled:u,...f}=r,b=(0,a.useRtl)(),v={isRtl:b,...r},m=B(v),y=n.StartScrollButtonIcon??g,Z=n.EndScrollButtonIcon??x,S=(0,i.Z)({elementType:y,externalSlotProps:s.startScrollButtonIcon,additionalProps:{fontSize:"small"},ownerState:v}),w=(0,i.Z)({elementType:Z,externalSlotProps:s.endScrollButtonIcon,additionalProps:{fontSize:"small"},ownerState:v});return(0,h.jsx)(R,{component:"div",className:(0,l.Z)(m.root,o),ref:t,role:null,ownerState:v,tabIndex:null,...f,style:{...f.style,..."vertical"===d&&{"--TabScrollButton-svgRotate":`rotate(${b?-90:90}deg)`}},children:"left"===c?(0,h.jsx)(y,{...S}):(0,h.jsx)(Z,{...w})})});var $=r(24533);function T(e){return(0,w.ZP)("MuiTabs",e)}let E=(0,S.Z)("MuiTabs",["root","vertical","list","flexContainer","flexContainerVertical","centered","scroller","fixed","scrollableX","scrollableY","hideScrollbar","scrollButtons","scrollButtonsHideMobile","indicator"]);var P=r(3246),k=r(31121);let j=(e,t)=>e===t?e.firstChild:t&&t.nextElementSibling?t.nextElementSibling:e.firstChild,z=(e,t)=>e===t?e.lastChild:t&&t.previousElementSibling?t.previousElementSibling:e.lastChild,L=(e,t,r)=>{let o=!1,l=r(e,t);for(;l;){if(l===e.firstChild){if(o)return;o=!0}let t=l.disabled||"true"===l.getAttribute("aria-disabled");if(!l.hasAttribute("tabindex")||t)l=r(e,l);else{l.focus();return}}},W=e=>{let{vertical:t,fixed:r,hideScrollbar:o,scrollableX:l,scrollableY:a,centered:i,scrollButtonsHideMobile:s,classes:c}=e;return(0,n.Z)({root:["root",t&&"vertical"],scroller:["scroller",r&&"fixed",o&&"hideScrollbar",l&&"scrollableX",a&&"scrollableY"],list:["list","flexContainer",t&&"flexContainerVertical",t&&"vertical",i&&"centered"],indicator:["indicator"],scrollButtons:["scrollButtons",s&&"scrollButtonsHideMobile"],scrollableX:[l&&"scrollableX"],hideScrollbar:[o&&"hideScrollbar"]},T,c)},A=(0,s.default)("div",{name:"MuiTabs",slot:"Root",overridesResolver:(e,t)=>{let{ownerState:r}=e;return[{[`& .${E.scrollButtons}`]:t.scrollButtons},{[`& .${E.scrollButtons}`]:r.scrollButtonsHideMobile&&t.scrollButtonsHideMobile},t.root,r.vertical&&t.vertical]}})((0,d.Z)(({theme:e})=>({overflow:"hidden",minHeight:48,WebkitOverflowScrolling:"touch",display:"flex",variants:[{props:({ownerState:e})=>e.vertical,style:{flexDirection:"column"}},{props:({ownerState:e})=>e.scrollButtonsHideMobile,style:{[`& .${E.scrollButtons}`]:{[e.breakpoints.down("sm")]:{display:"none"}}}}]}))),O=(0,s.default)("div",{name:"MuiTabs",slot:"Scroller",overridesResolver:(e,t)=>{let{ownerState:r}=e;return[t.scroller,r.fixed&&t.fixed,r.hideScrollbar&&t.hideScrollbar,r.scrollableX&&t.scrollableX,r.scrollableY&&t.scrollableY]}})({position:"relative",display:"inline-block",flex:"1 1 auto",whiteSpace:"nowrap",variants:[{props:({ownerState:e})=>e.fixed,style:{overflowX:"hidden",width:"100%"}},{props:({ownerState:e})=>e.hideScrollbar,style:{scrollbarWidth:"none","&::-webkit-scrollbar":{display:"none"}}},{props:({ownerState:e})=>e.scrollableX,style:{overflowX:"auto",overflowY:"hidden"}},{props:({ownerState:e})=>e.scrollableY,style:{overflowY:"auto",overflowX:"hidden"}}]}),H=(0,s.default)("div",{name:"MuiTabs",slot:"List",overridesResolver:(e,t)=>{let{ownerState:r}=e;return[t.list,t.flexContainer,r.vertical&&t.flexContainerVertical,r.centered&&t.centered]}})({display:"flex",variants:[{props:({ownerState:e})=>e.vertical,style:{flexDirection:"column"}},{props:({ownerState:e})=>e.centered,style:{justifyContent:"center"}}]}),N=(0,s.default)("span",{name:"MuiTabs",slot:"Indicator",overridesResolver:(e,t)=>t.indicator})((0,d.Z)(({theme:e})=>({position:"absolute",height:2,bottom:0,width:"100%",transition:e.transitions.create(),variants:[{props:{indicatorColor:"primary"},style:{backgroundColor:(e.vars||e).palette.primary.main}},{props:{indicatorColor:"secondary"},style:{backgroundColor:(e.vars||e).palette.secondary.main}},{props:({ownerState:e})=>e.vertical,style:{height:"100%",width:2,right:0}}]}))),D=(0,s.default)(function(e){let{onChange:t,...r}=e,l=o.useRef(),n=o.useRef(null),a=()=>{l.current=n.current.offsetHeight-n.current.clientHeight};return(0,b.Z)(()=>{let e=(0,u.Z)(()=>{let e=l.current;a(),e!==l.current&&t(l.current)}),r=(0,v.Z)(n.current);return r.addEventListener("resize",e),()=>{e.clear(),r.removeEventListener("resize",e)}},[t]),o.useEffect(()=>{a(),t(l.current)},[t]),(0,h.jsx)("div",{style:m,...r,ref:n})})({overflowX:"auto",overflowY:"hidden",scrollbarWidth:"none","&::-webkit-scrollbar":{display:"none"}}),F={},V=o.forwardRef(function(e,t){let r=(0,p.i)({props:e,name:"MuiTabs"}),n=(0,c.default)(),s=(0,a.useRtl)(),{"aria-label":d,"aria-labelledby":b,action:m,centered:y=!1,children:g,className:x,component:Z="div",allowScrollButtonsMobile:S=!1,indicatorColor:w="primary",onChange:C,orientation:M="horizontal",ScrollButtonComponent:B,scrollButtons:R="auto",selectionFollowsFocus:T,slots:E={},slotProps:V={},TabIndicatorProps:X={},TabScrollButtonProps:Y={},textColor:K="primary",value:q,variant:G="standard",visibleScrollbar:U=!1,...J}=r,Q="scrollable"===G,_="vertical"===M,ee=_?"scrollTop":"scrollLeft",et=_?"top":"left",er=_?"bottom":"right",eo=_?"clientHeight":"clientWidth",el=_?"height":"width",en={...r,component:Z,allowScrollButtonsMobile:S,indicatorColor:w,orientation:M,vertical:_,scrollButtons:R,textColor:K,variant:G,visibleScrollbar:U,fixed:!Q,hideScrollbar:Q&&!U,scrollableX:Q&&!_,scrollableY:Q&&_,centered:y&&!Q,scrollButtonsHideMobile:!S},ea=W(en),ei=(0,i.Z)({elementType:E.StartScrollButtonIcon,externalSlotProps:V.startScrollButtonIcon,ownerState:en}),es=(0,i.Z)({elementType:E.EndScrollButtonIcon,externalSlotProps:V.endScrollButtonIcon,ownerState:en}),[ec,ed]=o.useState(!1),[ep,eu]=o.useState(F),[ef,eb]=o.useState(!1),[ev,eh]=o.useState(!1),[em,ey]=o.useState(!1),[eg,ex]=o.useState({overflow:"hidden",scrollbarWidth:0}),eZ=new Map,eS=o.useRef(null),ew=o.useRef(null),eC={slots:E,slotProps:{indicator:X,scrollButton:Y,...V}},eM=()=>{let e,t;let r=eS.current;if(r){let t=r.getBoundingClientRect();e={clientWidth:r.clientWidth,scrollLeft:r.scrollLeft,scrollTop:r.scrollTop,scrollWidth:r.scrollWidth,top:t.top,bottom:t.bottom,left:t.left,right:t.right}}if(r&&!1!==q){let e=ew.current.children;if(e.length>0){let r=e[eZ.get(q)];t=r?r.getBoundingClientRect():null}}return{tabsMeta:e,tabMeta:t}},eB=(0,$.Z)(()=>{let e;let{tabsMeta:t,tabMeta:r}=eM(),o=0;_?(e="top",r&&t&&(o=r.top-t.top+t.scrollTop)):(e=s?"right":"left",r&&t&&(o=(s?-1:1)*(r[e]-t[e]+t.scrollLeft)));let l={[e]:o,[el]:r?r[el]:0};if("number"!=typeof ep[e]||"number"!=typeof ep[el])eu(l);else{let t=Math.abs(ep[e]-l[e]),r=Math.abs(ep[el]-l[el]);(t>=1||r>=1)&&eu(l)}}),eR=(e,{animation:t=!0}={})=>{t?function(e,t,r,o={},l=()=>{}){let{ease:n=f,duration:a=300}=o,i=null,s=t[e],c=!1,d=o=>{if(c){l(Error("Animation cancelled"));return}null===i&&(i=o);let p=Math.min(1,(o-i)/a);if(t[e]=n(p)*(r-s)+s,p>=1){requestAnimationFrame(()=>{l(null)});return}requestAnimationFrame(d)};return s===r?l(Error("Element already at target position")):requestAnimationFrame(d),()=>{c=!0}}(ee,eS.current,e,{duration:n.transitions.duration.standard}):eS.current[ee]=e},eI=e=>{let t=eS.current[ee];_?t+=e:t+=e*(s?-1:1),eR(t)},e$=()=>{let e=eS.current[eo],t=0,r=Array.from(ew.current.children);for(let o=0;o<r.length;o+=1){let l=r[o];if(t+l[eo]>e){0===o&&(t=e);break}t+=l[eo]}return t},eT=()=>{eI(-1*e$())},eE=()=>{eI(e$())},[eP,{onChange:ek,...ej}]=(0,k.Z)("scrollbar",{className:(0,l.Z)(ea.scrollableX,ea.hideScrollbar),elementType:D,shouldForwardComponentProp:!0,externalForwardedProps:eC,ownerState:en}),ez=o.useCallback(e=>{ek?.(e),ex({overflow:null,scrollbarWidth:e})},[ek]),[eL,eW]=(0,k.Z)("scrollButtons",{className:(0,l.Z)(ea.scrollButtons,Y.className),elementType:I,externalForwardedProps:eC,ownerState:en,additionalProps:{orientation:M,slots:{StartScrollButtonIcon:E.startScrollButtonIcon||E.StartScrollButtonIcon,EndScrollButtonIcon:E.endScrollButtonIcon||E.EndScrollButtonIcon},slotProps:{startScrollButtonIcon:ei,endScrollButtonIcon:es}}}),eA=(0,$.Z)(e=>{let{tabsMeta:t,tabMeta:r}=eM();r&&t&&(r[et]<t[et]?eR(t[ee]+(r[et]-t[et]),{animation:e}):r[er]>t[er]&&eR(t[ee]+(r[er]-t[er]),{animation:e}))}),eO=(0,$.Z)(()=>{Q&&!1!==R&&ey(!em)});o.useEffect(()=>{let e,t;let r=(0,u.Z)(()=>{eS.current&&eB()}),o=(0,v.Z)(eS.current);return o.addEventListener("resize",r),"undefined"!=typeof ResizeObserver&&(e=new ResizeObserver(r),Array.from(ew.current.children).forEach(t=>{e.observe(t)})),"undefined"!=typeof MutationObserver&&(t=new MutationObserver(t=>{t.forEach(t=>{t.removedNodes.forEach(t=>{e?.unobserve(t)}),t.addedNodes.forEach(t=>{e?.observe(t)})}),r(),eO()})).observe(ew.current,{childList:!0}),()=>{r.clear(),o.removeEventListener("resize",r),t?.disconnect(),e?.disconnect()}},[eB,eO]),o.useEffect(()=>{let e=Array.from(ew.current.children),t=e.length;if("undefined"!=typeof IntersectionObserver&&t>0&&Q&&!1!==R){let r=e[0],o=e[t-1],l={root:eS.current,threshold:.99},n=new IntersectionObserver(e=>{eb(!e[0].isIntersecting)},l);n.observe(r);let a=new IntersectionObserver(e=>{eh(!e[0].isIntersecting)},l);return a.observe(o),()=>{n.disconnect(),a.disconnect()}}},[Q,R,em,g?.length]),o.useEffect(()=>{ed(!0)},[]),o.useEffect(()=>{eB()}),o.useEffect(()=>{eA(F!==ep)},[eA,ep]),o.useImperativeHandle(m,()=>({updateIndicator:eB,updateScrollButtons:eO}),[eB,eO]);let[eH,eN]=(0,k.Z)("indicator",{className:(0,l.Z)(ea.indicator,X.className),elementType:N,externalForwardedProps:eC,ownerState:en,additionalProps:{style:ep}}),eD=(0,h.jsx)(eH,{...eN}),eF=0,eV=o.Children.map(g,e=>{if(!o.isValidElement(e))return null;let t=void 0===e.props.value?eF:e.props.value;eZ.set(t,eF);let r=t===q;return eF+=1,o.cloneElement(e,{fullWidth:"fullWidth"===G,indicator:r&&!ec&&eD,selected:r,selectionFollowsFocus:T,onChange:C,textColor:K,value:t,...1!==eF||!1!==q||e.props.tabIndex?{}:{tabIndex:0}})}),eX=e=>{if(e.altKey||e.shiftKey||e.ctrlKey||e.metaKey)return;let t=ew.current,r=(0,P.Z)(t).activeElement;if("tab"!==r.getAttribute("role"))return;let o="horizontal"===M?"ArrowLeft":"ArrowUp",l="horizontal"===M?"ArrowRight":"ArrowDown";switch("horizontal"===M&&s&&(o="ArrowRight",l="ArrowLeft"),e.key){case o:e.preventDefault(),L(t,r,z);break;case l:e.preventDefault(),L(t,r,j);break;case"Home":e.preventDefault(),L(t,null,j);break;case"End":e.preventDefault(),L(t,null,z)}},eY=(()=>{let e={};e.scrollbarSizeListener=Q?(0,h.jsx)(eP,{...ej,onChange:ez}):null;let t=Q&&("auto"===R&&(ef||ev)||!0===R);return e.scrollButtonStart=t?(0,h.jsx)(eL,{direction:s?"right":"left",onClick:eT,disabled:!ef,...eW}):null,e.scrollButtonEnd=t?(0,h.jsx)(eL,{direction:s?"left":"right",onClick:eE,disabled:!ev,...eW}):null,e})(),[eK,eq]=(0,k.Z)("root",{ref:t,className:(0,l.Z)(ea.root,x),elementType:A,externalForwardedProps:{...eC,...J,component:Z},ownerState:en}),[eG,eU]=(0,k.Z)("scroller",{ref:eS,className:ea.scroller,elementType:O,externalForwardedProps:eC,ownerState:en,additionalProps:{style:{overflow:eg.overflow,[_?`margin${s?"Left":"Right"}`:"marginBottom"]:U?void 0:-eg.scrollbarWidth}}}),[eJ,eQ]=(0,k.Z)("list",{ref:ew,className:(0,l.Z)(ea.list,ea.flexContainer),elementType:H,externalForwardedProps:eC,ownerState:en,getSlotProps:e=>({...e,onKeyDown:t=>{eX(t),e.onKeyDown?.(t)}})});return(0,h.jsxs)(eK,{...eq,children:[eY.scrollButtonStart,eY.scrollbarSizeListener,(0,h.jsxs)(eG,{...eU,children:[(0,h.jsx)(eJ,{"aria-label":d,"aria-labelledby":b,"aria-orientation":"vertical"===M?"vertical":null,role:"tablist",...eQ,children:eV}),ec&&eD]}),eY.scrollButtonEnd]})})}};