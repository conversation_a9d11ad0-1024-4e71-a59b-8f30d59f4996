exports.id=9902,exports.ids=[9902],exports.modules={40853:(e,t,s)=>{Promise.resolve().then(s.bind(s,78021))},78021:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});var n=s(10326);s(17577),s(23824),s(54658);var r=s(43058);function a({children:e}){return n.jsx(r.Z,{children:n.jsx("div",{className:"min-h-screen bg-white",style:{backgroundColor:"white",minHeight:"100vh"},children:e})})}},43933:(e,t,s)=>{"use strict";s.d(t,{KE:()=>c,Pb:()=>d,T1:()=>g,Uc:()=>u,createPlanAssignment:()=>l,fH:()=>o,updatePlanAssignment:()=>i});var n=s(89009);let r=(0,n.bR)(),a=()=>({"Content-Type":"application/json","user-id":(0,n.n5)()}),o=async(e,t={},s)=>{try{let o=(0,n.n5)();console.log("\uD83D\uDD0D Plan Assignment API Debug:",{companyId:e,userId:o,filters:t,pagination:s,apiBaseUrl:r});let l=new URLSearchParams;t.status&&l.append("status",t.status),t.planId&&l.append("planId",t.planId),t.assignmentYear&&l.append("assignmentYear",t.assignmentYear.toString()),t.referenceDate&&l.append("referenceDate",t.referenceDate),void 0!==t.includePlanData&&l.append("includePlanData",t.includePlanData.toString()),t.enrollmentPeriodOnly&&l.append("enrollmentPeriodOnly","true"),t.effectiveOnly&&l.append("effectiveOnly","true"),t.futureOnly&&l.append("futureOnly","true"),t.includeInactive&&l.append("includeInactive","true"),t.includeExpired&&l.append("includeExpired","true"),t.brokerId&&l.append("brokerId",t.brokerId),s&&(l.append("page",s.page.toString()),l.append("limit",s.limit.toString()));let i=`${r}/api/pre-enrollment/plan-assignments/company/${e}${l.toString()?`?${l.toString()}`:""}`;console.log("\uD83D\uDCE1 Fetching plan assignments from optimized API:",i);let c=await fetch(i,{method:"GET",headers:a()});if(console.log("Plan assignments API response status:",c.status),!c.ok){let t=`HTTP error! status: ${c.status}`;if(403===c.status){let t=(0,n.n5)();return console.error("\uD83D\uDEAB 403 Forbidden Error Details:",{url:i,userId:t,companyId:e,userIdSource:localStorage.getItem("userid1")?"userid1":localStorage.getItem("userId")?"userId":"none"}),console.log("\uD83D\uDD27 Broker has no existing plan assignments for this company - returning empty result to allow plan creation"),{success:!0,data:{assignments:[],count:0,message:"No existing plan assignments. You can create new plan assignments for this company.",canCreateAssignments:!0,accessDeniedToExisting:!0}}}try{let e=await c.json();console.error("API Error Response:",e),t+=` - ${e.error||e.message||"Unknown error"}`}catch(e){console.log("No additional error details available")}throw Error(t)}let u=await c.json();return console.log("Plan assignments result:",u),console.log("First assignment details:",u.assignments[0]),{success:!0,data:u}}catch(e){return console.error("Error fetching plan assignments:",e),{success:!1,error:"Failed to fetch plan assignments"}}},l=async e=>{try{console.log("\uD83D\uDE80 Creating plan assignment with data:",e),console.log("\uD83D\uDD0D Required fields check:",{planId:e.planId,companyId:e.companyId,rateStructure:e.rateStructure,coverageTiers:e.coverageTiers,planEffectiveDate:e.planEffectiveDate,planEndDate:e.planEndDate,enrollmentStartDate:e.enrollmentStartDate,enrollmentEndDate:e.enrollmentEndDate}),console.log("\uD83D\uDCE1 Request URL:",`${r}/api/pre-enrollment/plan-assignments`),console.log("\uD83D\uDD11 Headers:",a());let t=await fetch(`${r}/api/pre-enrollment/plan-assignments`,{method:"POST",headers:a(),body:JSON.stringify(e)});if(!t.ok){let s=await t.json().catch(()=>({})),n=s.error||s.message||`HTTP error! status: ${t.status}`,r=s.details||s.required||[];return console.error("❌ Plan assignment creation failed:",{status:t.status,error:n,details:r,fullErrorData:s,sentData:e}),{success:!1,error:`${n}${r.length>0?` - ${r.join(", ")}`:""}`}}let s=await t.json();return{success:!0,data:s.assignment}}catch(e){return console.error("Error creating plan assignment:",e),{success:!1,error:e instanceof Error?e.message:"Failed to create plan assignment"}}},i=async(e,t)=>{try{console.log("Updating plan assignment:",e,"with data:",t);let s=await fetch(`${r}/api/pre-enrollment/plan-assignments/${e}`,{method:"PUT",headers:a(),body:JSON.stringify(t)});if(console.log("Update response status:",s.status),!s.ok){let e=await s.json().catch(()=>({})),n=e.error||e.message||`HTTP error! status: ${s.status}`,r=e.details||[];return console.error("Plan assignment update failed:",{status:s.status,error:n,details:r,updateData:t}),{success:!1,error:`${n}${r.length>0?` - ${r.join(", ")}`:""}`}}let n=await s.json();return console.log("Update successful:",n),{success:!0,data:n.assignment}}catch(e){return console.error("Error updating plan assignment:",e),{success:!1,error:e instanceof Error?e.message:"Failed to update plan assignment"}}},c=async e=>{try{let t=await fetch(`${r}/api/pre-enrollment/plan-assignments/${e}`,{method:"DELETE",headers:a()});if(!t.ok)throw Error(`HTTP error! status: ${t.status}`);let s=await t.json();return{success:!0,data:{message:s.message||"Plan assignment deleted successfully"}}}catch(e){return console.error("Error deleting plan assignment:",e),{success:!1,error:"Failed to delete plan assignment"}}},u=async(e,t)=>{try{console.log("Cloning plan assignment:",e,"with overrides:",t);let s=await fetch(`${r}/api/pre-enrollment/plan-assignments/${e}/clone`,{method:"POST",headers:a(),body:JSON.stringify(t||{})});if(console.log("Clone response status:",s.status),!s.ok){let e=await s.json().catch(()=>({})),n=e.error||e.message||`HTTP error! status: ${s.status}`,r=e.details||[];return console.error("Plan assignment clone failed:",{status:s.status,error:n,details:r,cloneData:t}),{success:!1,error:`${n}${r.length>0?` - ${r.join(", ")}`:""}`}}let n=await s.json();return console.log("Clone successful:",n),{success:!0,data:n.assignment}}catch(e){return console.error("Error cloning plan assignment:",e),{success:!1,error:e instanceof Error?e.message:"Failed to clone plan assignment"}}},d=async()=>{try{let e=await fetch(`${r}/api/pre-enrollment/plans/assignable`,{method:"GET",headers:a()});if(!e.ok)throw Error(`HTTP error! status: ${e.status}`);let t=((await e.json()).plans||[]).map((e,t)=>{console.log(`🔍 Plan ${t} raw structure:`,e),console.log(`🔍 Plan ${t} _id:`,e._id);let s=null;if(e._id&&("string"==typeof e._id?s=e._id:e._id.$oid?s=e._id.$oid:e._id.toString&&(s=e._id.toString())),e._doc){let n=e._doc._id?.$oid||e._doc._id?.toString()||e._doc._id,r={...e._doc,_id:s||n};return console.log(`🔍 Plan ${t} transformed:`,r),r}let n={...e,_id:s};return console.log(`🔍 Plan ${t} transformed:`,n),n});return{success:!0,data:t}}catch(e){return console.error("Error fetching assignable plans:",e),{success:!1,error:"Failed to fetch assignable plans"}}},g=async()=>{try{let e=(0,n.n5)();console.log("\uD83D\uDD0D Fetching broker plan assignments count for userId:",e);let t=`${r}/api/pre-enrollment/plan-assignments?includePlanData=false`;console.log("\uD83D\uDCE1 Fetching broker assignments count from:",t);let s=await fetch(t,{method:"GET",headers:a()});if(console.log("Broker assignments count API response status:",s.status),s.ok){let e=await s.json();return console.log("✅ Broker assignments count response:",e),{success:!0,data:{count:e.count||0}}}{let e=await s.text();return console.error("❌ Failed to fetch broker assignments count:",s.status,e),{success:!1,error:`Failed to fetch assignments count: ${s.status}`}}}catch(e){return console.error("❌ Error fetching broker assignments count:",e),{success:!1,error:"Network error while fetching assignments count"}}}},89009:(e,t,s)=>{"use strict";s.d(t,{GU:()=>a,bR:()=>n,n5:()=>r});let n=()=>"http://localhost:8080",r=()=>{let e="userid1",t="userId",s=localStorage.getItem(e)||localStorage.getItem(t);return(console.log("\uD83D\uDD0D getUserId debug:",{primaryKey:e,altKey:t,primaryValue:localStorage.getItem(e),altValue:localStorage.getItem(t),finalUserId:s}),s)?s:(console.error("❌ User ID not found in localStorage"),"default-user")},a=()=>"https://bot.benosphere.com"},6079:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>n});let n=(0,s(68570).createProxy)(String.raw`C:\beno\project_dev\qharmony-frontend-v1\src\app\ai-enroller\layout.tsx#default`)},54658:()=>{},23824:()=>{}};