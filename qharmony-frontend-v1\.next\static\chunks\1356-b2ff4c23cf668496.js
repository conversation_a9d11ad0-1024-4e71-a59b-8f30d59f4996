"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1356],{5407:function(e,o,t){var n,r=t(94746);function i(){return(i=Object.assign?Object.assign.bind():function(e){for(var o=1;o<arguments.length;o++){var t=arguments[o];for(var n in t)({}).hasOwnProperty.call(t,n)&&(e[n]=t[n])}return e}).apply(null,arguments)}o.Z=function(e){return r.createElement("svg",i({xmlns:"http://www.w3.org/2000/svg",width:18,height:18,fill:"none",viewBox:"0 0 18 15"},e),n||(n=r.createElement("path",{fill:"#000",fillOpacity:.8,d:"M9 12.333q1 0 1.781-.594.782-.593 1.136-1.53H6.083a3.4 3.4 0 0 0 1.136 1.53q.78.594 1.781.594M6.917 9q.52 0 .885-.365t.365-.885-.365-.885a1.2 1.2 0 0 0-.885-.365q-.521 0-.886.365a1.2 1.2 0 0 0-.364.885q0 .52.364.885.365.365.886.365m4.166 0q.522 0 .886-.365t.364-.885q0-.52-.364-.885a1.2 1.2 0 0 0-.886-.365q-.52 0-.885.365a1.2 1.2 0 0 0-.365.885q0 .52.365.885.364.365.885.365M5.354 4.333l2.334-3.02q.25-.334.593-.49a1.72 1.72 0 0 1 1.438 0q.343.156.594.49l2.333 3.02 3.542 1.188q.54.166.854.614.312.448.312.99 0 .25-.073.5a1.6 1.6 0 0 1-.24.48l-2.29 3.25.082 3.416q.021.73-.479 1.229t-1.166.5q-.042 0-.459-.062L9 15.396l-3.73 1.041a.8.8 0 0 1-.228.052q-.125.011-.23.011-.666 0-1.166-.5a1.6 1.6 0 0 1-.48-1.23l.084-3.437L.98 8.104a1.6 1.6 0 0 1-.24-.479 1.8 1.8 0 0 1-.073-.5q0-.52.302-.969.301-.447.844-.635zm1.021 1.438L2.333 7.104l2.584 3.73-.084 3.979L9 13.667l4.167 1.166-.084-4 2.584-3.687-4.042-1.375L9 2.333z"})))}},48223:function(e,o,t){var n=t(57437),r=t(2265),i=t(47369),a=t(99376),s=t(83337),l=t(70623),d=t(39547),c=t(35389),x=t(95656);let g=()=>/Mobi|Android/i.test(navigator.userAgent);o.Z=e=>{let{children:o}=e,{user:t,loading:h}=(0,i.a)(),p=(0,a.useRouter)(),f=(0,a.usePathname)(),u=(0,s.T)(),[b,m]=(0,r.useState)(!1),A=(0,s.C)(e=>e.user.userProfile);return((0,r.useEffect)(()=>{{let e=localStorage.getItem("userid1")||localStorage.getItem("userId");console.log("USER ID FROM LOCAL STORAGE: ",e),e&&!A.name&&(u((0,l.Iv)(e)),(async()=>{try{await (0,d.M_)(u,e),await (0,d.aK)(u)}catch(e){console.error("Error fetching user data in ProtectedRoute:",e)}})())}},[u,A.name]),(0,r.useEffect)(()=>{console.log("ProtectedRoute useEffect triggered"),console.log("Current user: ",t),console.log("Loading state: ",h),console.log("Current user details: ",A),h||t||(console.log("User not authenticated, redirecting to home"),m(!1),p.push("/")),!h&&A.companyId&&""===A.companyId&&(console.log("Waiting to retrieve company details"),m(!1)),!h&&A.companyId&&""!==A.companyId&&(console.log("User found, rendering children"),m(!0)),g()&&!f.startsWith("/mobile")&&(console.log("Redirecting to mobile version of ".concat(f)),p.push("/mobile".concat(f)))},[t,h,A,p,f]),b)?t?(0,n.jsx)(n.Fragment,{children:o}):null:(0,n.jsx)(x.Z,{sx:{display:"flex",justifyContent:"center",alignItems:"center",height:"100vh",bgcolor:"#f6f8fc"},children:(0,n.jsx)(c.Z,{})})}},13571:function(e,o,t){t.d(o,{Z:function(){return U}});var n=t(57437),r=t(95656),i=t(93062),a=t(92253),s=t(46387),l=t(15273),d=t(73261),c=t(11741),x=t(53431),g=t(67051),h=t(94013),p=t(33145),f=t(83337),u=t(2265),b=t(39547),m=t(70623),A=t(80944),j=t(99376),v=t(56336),y=t(68575),w=t(31175),C={src:"/_next/static/media/slack_logo.3849d8c9.png",height:1024,width:1024,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAYAAADED76LAAAA9ElEQVR42g3KMUoDURRA0fve/xOdISESkIBFChVRtBMsJG7ABSiWNi7Azg24B3dgbSNYiGAqC4tgkahosIk2YwwkIxnnP4fL7Y7sdEYOKIzQFZ1/zdLTb9HmBrBrFE4f2gvFVIWyK8z6SG0Lwko5bxYKbd+nnWoIXxF2HBu/cxSJR/4qZuebaNd3kqS3lOfRUEgsSlJGxQVSaZQmBun74fZe3YjvhGyijKV2MyCftg6D6KTC+Mlna+urGC/iwon13I8Gf2kS1YXQyKU5E4DB4gFW9Z+8y0ft+vHWZpEBZ6o5OmgdOWKHeHs2tIuEZWAfIITI/QMNTWKSnIjo2AAAAABJRU5ErkJggg==",blurWidth:8,blurHeight:8},S={src:"/_next/static/media/teams_logo.cb463aaf.png",height:512,width:512,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAYAAADED76LAAAA8ElEQVR42mNABjXNb9yqGl9urGl+rQIXDIzYw8PAsFAIyGSuqH++H6jgf23Lmzr3oKMLAqJOrWPwCtr1vKzq6Luq2qNvgiL2/yyuerS9Z/Jvee/Q4+3+kadaGAxttv9fufra/y3bbv83stn8P7fk8pX6tndX49IvX/IKPX6cQd102/+unlP/p8288FdWb9O/wvLr/+ta3/0HKvgPNOU/g5vfri+Jaft/pWTu/+/gvuFfbvGVX0AT/sWmXf4PNOEPQ1TCQVkGhmaBpMzznQVl1/+X1z3+D/TF95jUS/1Ah3oyIIP6tg8NQMnNVY0vPGBiAJjtfJ3TGjMgAAAAAElFTkSuQmCC",blurWidth:8,blurHeight:8},Z=t(40256),I=()=>{let e=(0,f.T)(),o=(0,j.useRouter)(),t=(0,j.usePathname)(),I=(0,y.v9)(e=>e.user.userProfile.isAdmin),k=(0,y.v9)(e=>e.user.userProfile.isBroker),B=(0,y.v9)(e=>(0,m.MP)(e)),E=(0,y.v9)(e=>{var o,t,n;return null===(n=e.company)||void 0===n?void 0:null===(t=n.companyDetails)||void 0===t?void 0:null===(o=t.details)||void 0===o?void 0:o.logo}),R=(0,f.C)(e=>e.company.companyBenefitTypes),T=(0,f.C)(e=>e.user.selectedBenefitType),[U,W]=(0,u.useState)(!1),D=(0,y.v9)(e=>e.user.userProfile.email);(0,u.useEffect)(()=>{W("true"===localStorage.getItem("isTeamsApp1"))},[]),(0,u.useEffect)(()=>{B&&(0,b.N)(e,B)},[B,e]);let L=t=>{e((0,m.v2)(t)),o.push("/viewBenefitsByType/".concat(t))};return(0,n.jsxs)(r.Z,{sx:{display:"flex",bgcolor:"#f6f8fc",minHeight:"100vh"},children:[(0,n.jsx)(i.ZP,{}),(0,n.jsx)(a.ZP,{sx:{width:240,flexShrink:0,"& .MuiDrawer-paper":{width:240,boxSizing:"border-box",bgcolor:"#ffffff",position:"relative",display:"flex",flexDirection:"column"}},variant:"permanent",anchor:"left",children:(0,n.jsxs)(r.Z,{sx:{padding:0,height:"100%",position:"relative",display:"flex",flexDirection:"column"},children:[(0,n.jsx)(r.Z,{sx:{height:"80px",borderBottom:"1px solid #D2D2D2",display:"flex",alignItems:"center",justifyContent:"center",cursor:"pointer"},onClick:()=>o.push("/dashboard"),children:E?(0,n.jsx)("img",{src:E,alt:"Company Logo",style:{height:"50px",width:"auto"}}):(0,n.jsxs)(r.Z,{sx:{display:"flex",alignItems:"center"},children:[(0,n.jsx)(p.default,{src:A.Z,alt:"BenOsphere Logo",width:40,height:40}),(0,n.jsx)(s.Z,{sx:{fontWeight:800,fontSize:"1.5rem",ml:1},children:"BenOsphere"})]})}),(0,n.jsxs)(r.Z,{sx:{flexGrow:1,overflow:"auto",paddingX:2.5},children:[(0,n.jsx)(s.Z,{sx:{fontWeight:860,fontSize:"14px",color:"rgba(0, 0, 0, 0.4)",marginBottom:"8px"},children:"MY BENEFITS"}),(0,n.jsx)(l.Z,{children:R.length>0?R.map(e=>(0,n.jsx)(d.ZP,{disablePadding:!0,children:(0,n.jsxs)(c.Z,{onClick:()=>L(e),sx:{borderRadius:2,"&:hover":{backgroundColor:"#f0f0f0"},bgcolor:T===e?"#f0f4ff":"inherit"},children:[(0,n.jsx)(x.Z,{sx:{minWidth:0,mr:2,color:"black"},children:(0,w.RS)(e)}),(0,n.jsx)(g.Z,{primary:(0,b.Ur)(e),sx:{fontWeight:"medium",color:"#333"}})]})},e)):(0,n.jsx)(s.Z,{variant:"body1",sx:{color:"#999",padding:2.5},children:"No benefits available at the moment"})})]}),!["/qHarmonyBot","/proactive-messaging","/notification-history",/^\/notifications-analytics\/[^/]+$/].some(e=>"string"==typeof e?t.includes(e):e.test(t))&&!U&&(0,n.jsxs)(r.Z,{sx:{position:"fixed",bottom:"80px",right:"20px",display:"flex",alignItems:"center",bgcolor:"#ffffff",borderRadius:"30px",padding:"10px 20px",boxShadow:"0px 4px 12px rgba(0, 0, 0, 0.1)",cursor:"pointer"},onClick:()=>o.push("/qHarmonyBot"),children:[(0,n.jsx)(p.default,{src:v.Z,alt:"AI Chat",style:{borderRadius:"100%",width:"40px",height:"40px",marginRight:"10px"}}),(0,n.jsxs)(r.Z,{children:[(0,n.jsx)(s.Z,{variant:"body1",sx:{fontWeight:"bold"},children:"Chat with Brea"}),(0,n.jsx)(s.Z,{variant:"body2",sx:{color:"#6c757d"},children:"24/7 available"})]})]}),(0,n.jsxs)(r.Z,{sx:{position:"relative",marginTop:"auto",paddingBottom:"20px",display:"flex",flexDirection:"column",alignItems:"center",gap:"10px"},children:[I&&(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(s.Z,{sx:{fontWeight:700,paddingY:1,fontSize:".7rem",color:"rgba(0, 0, 0, 0.4)"},children:"ADMIN HUB"}),(0,n.jsx)(h.Z,{variant:"contained",onClick:()=>o.push("/manageBenefits/"),sx:{textTransform:"none",borderRadius:"8px",bgcolor:"rgba(0, 0, 0, 0.06)",color:"black",boxShadow:"none",width:"90%",paddingY:"10px","&:hover":{backgroundColor:"rgba(0, 0, 0, 0.1)",boxShadow:"none"}},children:"Upload Benefits"}),(0,n.jsx)(h.Z,{variant:"contained",onClick:()=>o.push("/manage-groups/"),sx:{textTransform:"none",borderRadius:"8px",bgcolor:"rgba(0, 0, 0, 0.06)",color:"black",boxShadow:"none",width:"90%",paddingY:"10px","&:hover":{backgroundColor:"rgba(0, 0, 0, 0.1)",boxShadow:"none"}},children:"Assign Benefits"}),(0,n.jsx)(h.Z,{variant:"contained",onClick:()=>o.push("/team-members/"),sx:{textTransform:"none",borderRadius:"8px",bgcolor:"rgba(0, 0, 0, 0.06)",color:"black",boxShadow:"none",width:"90%",paddingY:"10px","&:hover":{backgroundColor:"rgba(0, 0, 0, 0.1)",boxShadow:"none"}},children:"Manage Team"}),Z.BO.includes(D)&&(0,n.jsx)(h.Z,{variant:"contained",onClick:()=>o.push("/proactive-messaging/"),sx:{textTransform:"none",borderRadius:"8px",bgcolor:"rgba(0, 0, 0, 0.06)",color:"black",boxShadow:"none",width:"90%",paddingY:"10px","&:hover":{backgroundColor:"rgba(0, 0, 0, 0.1)",boxShadow:"none"}},children:"Broadcast Center"})]}),k&&(0,n.jsx)(n.Fragment,{children:(0,n.jsx)(h.Z,{variant:"contained",onClick:()=>o.push("/manage-companies/"),sx:{textTransform:"none",borderRadius:"8px",bgcolor:"rgba(0, 0, 0, 0.06)",color:"black",boxShadow:"none",width:"90%",paddingY:"10px","&:hover":{backgroundColor:"rgba(0, 0, 0, 0.1)",boxShadow:"none"}},children:"Add Employers"})}),!U&&(0,n.jsxs)(r.Z,{sx:{display:"flex",flexDirection:"column",gap:1.5,alignItems:"center",width:"90%"},children:[(0,n.jsxs)(h.Z,{variant:"outlined",sx:{display:"flex",justifyContent:"center",width:"100%",borderRadius:"12px",padding:"9px 16px",border:"1px solid rgba(0, 0, 0, 0.12)",color:"black",fontSize:"16px",fontWeight:500,textTransform:"none","&:hover":{backgroundColor:"rgba(0, 0, 0, 0.04)"}},children:[(0,n.jsx)(p.default,{src:C,alt:"Teams Logo",width:21,height:21,style:{marginRight:"8px"}}),"Add to Slack"]}),(0,n.jsxs)(h.Z,{variant:"outlined",sx:{display:"flex",justifyContent:"center",width:"100%",borderRadius:"12px",padding:"9px 16px",border:"1px solid rgba(0, 0, 0, 0.12)",color:"black",fontSize:"16px",fontWeight:500,textTransform:"none","&:hover":{backgroundColor:"rgba(0, 0, 0, 0.04)"}},children:[(0,n.jsx)(p.default,{src:S,alt:"Teams Logo",width:21,height:21,style:{marginRight:"8px"}}),"Add to Teams"]})]})]})]})})]})},k=t(67116),B=t(15116),E=t(47369),R=t(5407),T=()=>{let{logout:e}=(0,E.a)(),o=(0,f.C)(e=>e.user.userProfile),[t,i]=(0,u.useState)(!1);(0,u.useEffect)(()=>{i("true"===localStorage.getItem("isTeamsApp1"))},[]);let a=(0,y.v9)(e=>e.user.userProfile.isAdmin),l=(0,y.v9)(e=>e.user.userProfile.isBroker);return(0,n.jsx)(r.Z,{sx:{bgcolor:"white",padding:2,textAlign:"center",height:"80px",borderBottom:"1px solid #D2D2D2"},children:(0,n.jsxs)(r.Z,{sx:{display:"flex",alignItems:"center",justifyContent:"space-between"},children:[(0,n.jsxs)(r.Z,{sx:{display:"flex",alignItems:"center"},children:[(0,n.jsx)(k.Z,{sx:{bgcolor:"black",color:"#ffffff",width:35,height:35,fontSize:"1.2rem",mr:1.5,ml:2,display:"flex",alignItems:"center",justifyContent:"center",paddingBottom:"1px",fontWeight:800},children:(e=>{if(!e)return"";let[o,t]=e.split(" ");return"".concat(o[0].toUpperCase()).concat(t?t[0].toUpperCase():"")})(o.name)}),(0,n.jsx)(s.Z,{variant:"h4",sx:{mb:0,fontWeight:"bold",display:"flex",alignItems:"center",justifyContent:"flex-start",textAlign:"flex-start",fontSize:"16px",mr:1.5},children:o.name.replace(/\b\w/g,e=>e.toUpperCase())}),a&&(0,n.jsx)(r.Z,{sx:{bgcolor:"rgba(0, 0, 0, 0.06)",borderRadius:"8px",padding:"4px 8px",display:"flex",alignItems:"center",justifyContent:"center",fontWeight:"bold",fontSize:"12px",color:"#333"},children:"ADMIN"}),l&&(0,n.jsx)(r.Z,{sx:{bgcolor:"#f5f5f5",borderRadius:"8px",padding:"2px 8px",display:"flex",alignItems:"center",justifyContent:"center",fontWeight:"bold",fontSize:"0.9rem",color:"#333"},children:"BROKER"})]}),(0,n.jsxs)(r.Z,{sx:{display:"flex",alignItems:"center"},children:[!t&&(0,n.jsxs)(h.Z,{sx:{backgroundColor:"transparent",color:"#333",textTransform:"none",padding:"8px 8px",display:"flex",alignItems:"center",justifyContent:"flex-start",gap:"2px","&:hover":{backgroundColor:"transparent",color:"#555"},boxShadow:"none"},children:[(0,n.jsx)(r.Z,{sx:{mt:.5,mr:.5},children:(0,n.jsx)(R.Z,{})}),(0,n.jsx)(s.Z,{sx:{fontWeight:500,fontSize:"14px"},children:"Guide"})]}),!t&&(0,n.jsxs)(h.Z,{onClick:e,sx:{backgroundColor:"transparent",color:"#333",marginLeft:1,textTransform:"none",padding:"8px 16px",display:"flex",alignItems:"center",justifyContent:"flex-start",gap:"8px","&:hover":{backgroundColor:"transparent",color:"#555"},boxShadow:"none"},children:[(0,n.jsx)(B.Z,{sx:{fontSize:"18px"}}),(0,n.jsx)(s.Z,{sx:{fontWeight:500,fontSize:"14px"},children:"Logout"})]})]})]})})},U=e=>{let o=o=>(0,n.jsxs)(r.Z,{sx:{display:"flex",flexGrow:1},children:[(0,n.jsx)(I,{}),(0,n.jsxs)(r.Z,{component:"main",sx:{flexGrow:1,width:"calc(100% - 240px)"},children:[(0,n.jsx)(T,{}),(0,n.jsx)(e,{...o})]})]});return o.displayName="WithSidebar(".concat(e.displayName||e.name||"Component",")"),o}},80944:function(e,o){o.Z={src:"/_next/static/media/logo.770bfeee.png",height:300,width:300,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAYAAADED76LAAAA9ElEQVR42i2PMUoDQRiF3z+zu7MbFTaxkfUC0St4ACWdhbbapvACCnaCpU0a09jYiQewFj2AIljY2AoJ7LKZnc3s/P6EPPiq9+DxESR359UgBHNrGxTWQTcOPwtrLwGU6n5c7ySxeQb4vWn9WS/lky50JVH6UlrVjzTFE2iebvbwmJkE37/zPLB79SHfzWIzUUph0LrqScB4qpFEdEhICxm9BeY9BYA9kxJwfTw7IEKfGUsAq06FgNlGtjUSoDTvS1mB3B/BDInoM/KhvQhd8lDb5RGz/pDLVFM+inVc1L49pbXmtmjeiOZQNCGaX4vGXQGY/wM1tG/NQnnUIwAAAABJRU5ErkJggg==",blurWidth:8,blurHeight:8}}}]);