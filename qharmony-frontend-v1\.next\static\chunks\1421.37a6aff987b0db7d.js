(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1421],{1421:function(e,t,a){"use strict";a.r(t),a.d(t,{default:function(){return h}});var s=a(57437),l=a(13571),o=a(2265),n=a(11975),i=a(5600),r=a.n(i);a(98527),a(42603);var c=a(48223),d=a(40256),u=a(45464),p=a(94242);a(2648);var m=JSON.parse('{"scheduler-nudge":{"type":"AdaptiveCard","body":[{"type":"ColumnSet","columns":[{"type":"Column","items":[{"type":"Image","style":"Person","url":"https://firebasestorage.googleapis.com/v0/b/qharmony-dev.firebasestorage.app/o/ai_assistant.png?alt=media&token=e8aea1ee-bce5-4308-9880-0a7c6691f8ea","altText":"Brea - Your 24/7 Benefits Specialist","size":"Small"}],"width":"auto"},{"type":"Column","items":[{"type":"TextBlock","weight":"Bolder","text":"Brea - Your 24/7 Benefits Specialist","wrap":true}],"width":"stretch"}]},{"type":"TextBlock","text":"{{ADAPTIVECARD_TEXT}}","wrap":true},{"type":"ActionSet","actions":[{"type":"Action.Execute","title":"✅ I Did It","data":{"messageId":"{{MESSAGE_ID}}","messageType":"I Did It"}},{"type":"Action.Execute","title":"\uD83D\uDC4D Good Reminder","data":{"messageId":"{{MESSAGE_ID}}","messageType":"Good Reminder"}},{"type":"Action.Execute","title":"\uD83D\uDCC5 Scheduling Soon","data":{"messageId":"{{MESSAGE_ID}}","messageType":"Scheduling Soon"}}]},{"type":"TextBlock","text":"--------------------","wrap":true,"weight":"Bolder"},{"type":"TextBlock","text":"AI-generated content—verify before use.","wrap":true,"weight":"Bolder"}],"$schema":"http://adaptivecards.io/schemas/adaptive-card.json","version":"1.5"},"general-nudge":{"type":"AdaptiveCard","body":[{"type":"ColumnSet","columns":[{"type":"Column","items":[{"type":"Image","style":"Person","url":"https://firebasestorage.googleapis.com/v0/b/qharmony-dev.firebasestorage.app/o/ai_assistant.png?alt=media&token=e8aea1ee-bce5-4308-9880-0a7c6691f8ea","altText":"Brea - Your 24/7 Benefits Specialist","size":"Small"}],"width":"auto"},{"type":"Column","items":[{"type":"TextBlock","weight":"Bolder","text":"Brea - Your 24/7 Benefits Specialist","wrap":true}],"width":"stretch"}]},{"type":"TextBlock","text":"{{ADAPTIVECARD_TEXT}}","wrap":true},{"type":"ActionSet","actions":[{"type":"Action.Execute","title":"\uD83D\uDC4D I like it","data":{"messageId":"{{MESSAGE_ID}}","messageType":"I like it"}},{"type":"Action.Execute","title":"\uD83D\uDE10 Not Relevant","data":{"messageId":"{{MESSAGE_ID}}","messageType":"Not Relevant"}},{"type":"Action.Execute","title":"✅ Done","data":{"messageId":"{{MESSAGE_ID}}","messageType":"Done"}}]},{"type":"TextBlock","text":"--------------------","wrap":true,"weight":"Bolder"},{"type":"TextBlock","text":"AI-generated content—verify before use.","wrap":true,"weight":"Bolder"}],"$schema":"http://adaptivecards.io/schemas/adaptive-card.json","version":"1.5"}}'),g=a(99376),h=(0,l.Z)(()=>{let[e,t]=(0,o.useState)([]),[a,l]=(0,o.useState)([]),[i,h]=(0,o.useState)([]),[y,v]=(0,o.useState)(null),[f,x]=(0,o.useState)(""),[S,w]=(0,o.useState)(""),[A,E]=(0,o.useState)([]),[I,b]=(0,o.useState)([]),[C,T]=(0,o.useState)(!1),j=(0,g.useRouter)(),B=new u.Z;(0,o.useEffect)(()=>{console.log("Selected Companies >>",e)},[e]),(0,o.useEffect)(()=>{console.log("Selected Groups >>",i)},[i]),(0,o.useEffect)(()=>{console.log("Selected Adaptive Card >>",y)},[y]),(0,o.useEffect)(()=>{console.log("Message Content >>",f)},[f]),(0,o.useEffect)(()=>{(async()=>{try{let e=await (0,d.A_)("/groups-by-company-ids");if(e.success){let t=e.data.groups.map(e=>({value:e._id,label:"".concat(e.name," (").concat(e.userCount,")"),companyId:e.companyId,name:e.name}));E(t);let a=await (0,d.A_)("/company/get-all-companies");if(a.success){let e=a.data.map(e=>{let a=t.filter(t=>t.companyId===e._id).map(e=>e.value);return{value:e._id,label:"".concat(e.name," (").concat(e.userCount,")"),groupIds:a,name:e.name}});l([{value:"all",label:"All",groupIds:[]},...e])}}}catch(e){console.error("Error fetching data:",e),p.Am.error("Failed to fetch companies and groups")}})()},[]);let _={container:e=>({...e,width:"100%"}),control:e=>({...e,width:"100%"}),menu:e=>({...e,width:"100%"})},k=e=>0===e.length?[]:e.map(e=>({_id:e.value,name:e.name,groupIds:e.groupIds})),N=e=>0===e.length?[]:e.map(e=>({_id:e.value,name:e.name,companyId:e.companyId})),D=async()=>{let a;T(!0);try{let s=y?m[y.value]:null,l=f.replace(/\\/g,"\\\\").replace(/"/g,'\\"').replace(/\n/g,"\\n");s&&(s=JSON.parse(JSON.stringify(s).replace(/{{ADAPTIVECARD_TEXT}}/g,l))),a={selectedGroups:N(i),selectedCompanies:k(e),body_type:"adaptivecard",message:s,messageText:l},console.log("Notification payload:",a),await (0,d.j0)("/send-group-notifications",a),p.Am.success("Notifications sent successfully!"),t([]),h([]),v(null),x(""),w(""),b([])}catch(e){var s;console.error("Error sending notifications:",e),p.Am.error(null===(s=e.response)||void 0===s?void 0:s.data.message)}finally{T(!1)}};return(0,s.jsxs)(c.Z,{children:[(0,s.jsxs)("div",{className:"main-container",children:[(0,s.jsxs)("div",{className:"header-container",children:[(0,s.jsx)("div",{style:{width:"100px"}})," ",(0,s.jsx)("h1",{className:"header-title",children:"Broadcast Messages"}),(0,s.jsx)("button",{className:"view-history-btn",onClick:()=>j.push("/notification-history"),children:"View History"})]}),(0,s.jsxs)("div",{className:"select-company-dropdown",children:[(0,s.jsx)("label",{children:"Choose Employers"}),(0,s.jsx)(n.ZP,{isMulti:!0,options:a,value:e,onChange:e=>{let s;e.some(e=>"all"===e.value)?t(s=a.filter(e=>"all"!==e.value)):(s=e,t(e)),h([]);let l=s.flatMap(e=>e.groupIds);b([{value:"all",label:"All"},...A.filter(e=>l.includes(e.value))])},placeholder:"Choose Employers...",styles:_})]}),(0,s.jsxs)("div",{className:"select-group-dropdown",children:[(0,s.jsx)("label",{children:"Target Employee Groups"}),(0,s.jsx)(n.ZP,{isMulti:!0,options:I,value:i,onChange:e=>{if(!e){h([]);return}e.some(e=>"all"===e.value)?h(I.filter(e=>"all"!==e.value)):h(e)},placeholder:"Target Employee Groups...",styles:_})]}),(0,s.jsxs)("div",{className:"select-adaptive-card-dropdown",children:[(0,s.jsx)("label",{children:"Select Adaptive Card"}),(0,s.jsx)(n.ZP,{options:[{value:"scheduler-nudge",label:"Scheduler Nudge"},{value:"general-nudge",label:"General Nudge"}],value:y,onChange:e=>v(e),placeholder:"Select Adaptive Card...",styles:_})]}),(0,s.jsxs)("div",{className:"wysiwyg-editor",children:[(0,s.jsx)("label",{children:"Message Content"}),(0,s.jsx)(r(),{theme:"snow",value:S,onChange:e=>{w(e);let t=B.turndown(e);x(t),console.log("HTML content:",e),console.log("Converted to Markdown:",t)},placeholder:"Write your message here...",modules:{toolbar:[["bold","italic","underline"],[{list:"ordered"},{list:"bullet"}],["link"]]},style:{height:"250px"}})]}),(0,s.jsx)("div",{className:"send-notifications-container",children:(0,s.jsx)("button",{onClick:D,disabled:C||!(e.length>0&&null!==y&&""!==f.trim()),children:C?"Sending...":"Send Message"})})]}),(0,s.jsx)(p.Ix,{position:"top-right",autoClose:3e3,hideProgressBar:!1,newestOnTop:!1,closeOnClick:!0,rtl:!1,pauseOnFocusLoss:!0,draggable:!0,pauseOnHover:!0,theme:"light"})]})})},42603:function(){}}]);