"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1642],{53284:function(e,r,t){var n=t(94630),o=t(57437);r.Z=(0,n.Z)((0,o.jsx)("path",{d:"M6 17h3l2-4V7H5v6h3zm8 0h3l2-4V7h-6v6h3z"}),"FormatQuote")},35389:function(e,r,t){t.d(r,{Z:function(){return P}});var n=t(45008),o=t(2265),i=t(61994),l=t(20801),a=t(3146),s=t(16210),c=t(21086),u=t(37053),d=t(85657),v=t(3858),f=t(94143),p=t(50738);function m(e){return(0,p.ZP)("MuiCircularProgress",e)}(0,f.Z)("MuiCircularProgress",["root","determinate","indeterminate","colorPrimary","colorSecondary","svg","circle","circleDeterminate","circleIndeterminate","circleDisableShrink"]);var h=t(57437);function y(){let e=(0,n._)(["\n  0% {\n    transform: rotate(0deg);\n  }\n\n  100% {\n    transform: rotate(360deg);\n  }\n"]);return y=function(){return e},e}function S(){let e=(0,n._)(["\n  0% {\n    stroke-dasharray: 1px, 200px;\n    stroke-dashoffset: 0;\n  }\n\n  50% {\n    stroke-dasharray: 100px, 200px;\n    stroke-dashoffset: -15px;\n  }\n\n  100% {\n    stroke-dasharray: 1px, 200px;\n    stroke-dashoffset: -126px;\n  }\n"]);return S=function(){return e},e}function g(){let e=(0,n._)(["\n        animation: "," 1.4s linear infinite;\n      "]);return g=function(){return e},e}function x(){let e=(0,n._)(["\n        animation: "," 1.4s ease-in-out infinite;\n      "]);return x=function(){return e},e}let k=(0,a.F4)(y()),Z=(0,a.F4)(S()),z="string"!=typeof k?(0,a.iv)(g(),k):null,b="string"!=typeof Z?(0,a.iv)(x(),Z):null,w=e=>{let{classes:r,variant:t,color:n,disableShrink:o}=e,i={root:["root",t,"color".concat((0,d.Z)(n))],svg:["svg"],circle:["circle","circle".concat((0,d.Z)(t)),o&&"circleDisableShrink"]};return(0,l.Z)(i,m,r)},M=(0,s.default)("span",{name:"MuiCircularProgress",slot:"Root",overridesResolver:(e,r)=>{let{ownerState:t}=e;return[r.root,r[t.variant],r["color".concat((0,d.Z)(t.color))]]}})((0,c.Z)(e=>{let{theme:r}=e;return{display:"inline-block",variants:[{props:{variant:"determinate"},style:{transition:r.transitions.create("transform")}},{props:{variant:"indeterminate"},style:z||{animation:"".concat(k," 1.4s linear infinite")}},...Object.entries(r.palette).filter((0,v.Z)()).map(e=>{let[t]=e;return{props:{color:t},style:{color:(r.vars||r).palette[t].main}}})]}})),j=(0,s.default)("svg",{name:"MuiCircularProgress",slot:"Svg",overridesResolver:(e,r)=>r.svg})({display:"block"}),C=(0,s.default)("circle",{name:"MuiCircularProgress",slot:"Circle",overridesResolver:(e,r)=>{let{ownerState:t}=e;return[r.circle,r["circle".concat((0,d.Z)(t.variant))],t.disableShrink&&r.circleDisableShrink]}})((0,c.Z)(e=>{let{theme:r}=e;return{stroke:"currentColor",variants:[{props:{variant:"determinate"},style:{transition:r.transitions.create("stroke-dashoffset")}},{props:{variant:"indeterminate"},style:{strokeDasharray:"80px, 200px",strokeDashoffset:0}},{props:e=>{let{ownerState:r}=e;return"indeterminate"===r.variant&&!r.disableShrink},style:b||{animation:"".concat(Z," 1.4s ease-in-out infinite")}}]}}));var P=o.forwardRef(function(e,r){let t=(0,u.i)({props:e,name:"MuiCircularProgress"}),{className:n,color:o="primary",disableShrink:l=!1,size:a=40,style:s,thickness:c=3.6,value:d=0,variant:v="indeterminate",...f}=t,p={...t,color:o,disableShrink:l,size:a,thickness:c,value:d,variant:v},m=w(p),y={},S={},g={};if("determinate"===v){let e=2*Math.PI*((44-c)/2);y.strokeDasharray=e.toFixed(3),g["aria-valuenow"]=Math.round(d),y.strokeDashoffset="".concat(((100-d)/100*e).toFixed(3),"px"),S.transform="rotate(-90deg)"}return(0,h.jsx)(M,{className:(0,i.Z)(m.root,n),style:{width:a,height:a,...S,...s},ownerState:p,ref:r,role:"progressbar",...g,...f,children:(0,h.jsx)(j,{className:m.svg,ownerState:p,viewBox:"".concat(22," ").concat(22," ").concat(44," ").concat(44),children:(0,h.jsx)(C,{className:m.circle,style:y,ownerState:p,cx:44,cy:44,r:(44-c)/2,fill:"none",strokeWidth:c})})})})},31691:function(e,r,t){t.d(r,{default:function(){return l}}),t(2265);var n=t(20135),o=t(55201),i=t(22166);function l(){let e=(0,n.default)(o.Z);return e[i.Z]||e}},94630:function(e,r,t){t.d(r,{Z:function(){return y}});var n=t(2265),o=t(61994),i=t(20801),l=t(85657),a=t(16210),s=t(21086),c=t(37053),u=t(94143),d=t(50738);function v(e){return(0,d.ZP)("MuiSvgIcon",e)}(0,u.Z)("MuiSvgIcon",["root","colorPrimary","colorSecondary","colorAction","colorError","colorDisabled","fontSizeInherit","fontSizeSmall","fontSizeMedium","fontSizeLarge"]);var f=t(57437);let p=e=>{let{color:r,fontSize:t,classes:n}=e,o={root:["root","inherit"!==r&&"color".concat((0,l.Z)(r)),"fontSize".concat((0,l.Z)(t))]};return(0,i.Z)(o,v,n)},m=(0,a.default)("svg",{name:"MuiSvgIcon",slot:"Root",overridesResolver:(e,r)=>{let{ownerState:t}=e;return[r.root,"inherit"!==t.color&&r["color".concat((0,l.Z)(t.color))],r["fontSize".concat((0,l.Z)(t.fontSize))]]}})((0,s.Z)(e=>{var r,t,n,o,i,l,a,s,c,u,d,v,f,p,m,h,y,S;let{theme:g}=e;return{userSelect:"none",width:"1em",height:"1em",display:"inline-block",flexShrink:0,transition:null===(o=g.transitions)||void 0===o?void 0:null===(n=o.create)||void 0===n?void 0:n.call(o,"fill",{duration:null===(t=(null!==(m=g.vars)&&void 0!==m?m:g).transitions)||void 0===t?void 0:null===(r=t.duration)||void 0===r?void 0:r.shorter}),variants:[{props:e=>!e.hasSvgAsChild,style:{fill:"currentColor"}},{props:{fontSize:"inherit"},style:{fontSize:"inherit"}},{props:{fontSize:"small"},style:{fontSize:(null===(l=g.typography)||void 0===l?void 0:null===(i=l.pxToRem)||void 0===i?void 0:i.call(l,20))||"1.25rem"}},{props:{fontSize:"medium"},style:{fontSize:(null===(s=g.typography)||void 0===s?void 0:null===(a=s.pxToRem)||void 0===a?void 0:a.call(s,24))||"1.5rem"}},{props:{fontSize:"large"},style:{fontSize:(null===(u=g.typography)||void 0===u?void 0:null===(c=u.pxToRem)||void 0===c?void 0:c.call(u,35))||"2.1875rem"}},...Object.entries((null!==(h=g.vars)&&void 0!==h?h:g).palette).filter(e=>{let[,r]=e;return r&&r.main}).map(e=>{var r,t,n;let[o]=e;return{props:{color:o},style:{color:null===(t=(null!==(n=g.vars)&&void 0!==n?n:g).palette)||void 0===t?void 0:null===(r=t[o])||void 0===r?void 0:r.main}}}),{props:{color:"action"},style:{color:null===(v=(null!==(y=g.vars)&&void 0!==y?y:g).palette)||void 0===v?void 0:null===(d=v.action)||void 0===d?void 0:d.active}},{props:{color:"disabled"},style:{color:null===(p=(null!==(S=g.vars)&&void 0!==S?S:g).palette)||void 0===p?void 0:null===(f=p.action)||void 0===f?void 0:f.disabled}},{props:{color:"inherit"},style:{color:void 0}}]}})),h=n.forwardRef(function(e,r){let t=(0,c.i)({props:e,name:"MuiSvgIcon"}),{children:i,className:l,color:a="inherit",component:s="svg",fontSize:u="medium",htmlColor:d,inheritViewBox:v=!1,titleAccess:h,viewBox:y="0 0 24 24",...S}=t,g=n.isValidElement(i)&&"svg"===i.type,x={...t,color:a,component:s,fontSize:u,instanceFontSize:e.fontSize,inheritViewBox:v,viewBox:y,hasSvgAsChild:g},k={};v||(k.viewBox=y);let Z=p(x);return(0,f.jsxs)(m,{as:s,className:(0,o.Z)(Z.root,l),focusable:"false",color:d,"aria-hidden":!h||void 0,role:h?"img":void 0,ref:r,...k,...S,...g&&i.props,ownerState:x,children:[g?i.props.children:i,h?(0,f.jsx)("title",{children:h}):null]})});function y(e,r){function t(t,n){return(0,f.jsx)(h,{"data-testid":"".concat(r,"Icon"),ref:n,...t,children:e})}return t.muiName=h.muiName,n.memo(n.forwardRef(t))}h.muiName="SvgIcon"},45008:function(e,r,t){t.d(r,{_:function(){return n}});function n(e,r){return r||(r=e.slice(0)),Object.freeze(Object.defineProperties(e,{raw:{value:Object.freeze(r)}}))}}}]);