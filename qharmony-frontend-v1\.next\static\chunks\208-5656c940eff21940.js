"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[208],{15116:function(e,t,r){var o=r(94630),n=r(57437);t.Z=(0,o.Z)((0,n.jsx)("path",{d:"m17 7-1.41 1.41L18.17 11H8v2h10.17l-2.58 2.58L17 17l5-5zM4 5h8V3H4c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h8v-2H4z"}),"Logout")},67116:function(e,t,r){r.d(t,{Z:function(){return w}});var o=r(2265),n=r(61994),a=r(20801),i=r(16210),l=r(21086),c=r(37053),s=r(94630),u=r(57437),f=(0,s.Z)((0,u.jsx)("path",{d:"M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"}),"Person"),p=r(94143),d=r(50738);function y(e){return(0,d.ZP)("MuiAvatar",e)}(0,p.Z)("MuiAvatar",["root","colorDefault","circular","rounded","square","img","fallback"]);var v=r(79114);let m=e=>{let{classes:t,variant:r,colorDefault:o}=e;return(0,a.Z)({root:["root",r,o&&"colorDefault"],img:["img"],fallback:["fallback"]},y,t)},b=(0,i.default)("div",{name:"MuiAvatar",slot:"Root",overridesResolver:(e,t)=>{let{ownerState:r}=e;return[t.root,t[r.variant],r.colorDefault&&t.colorDefault]}})((0,l.Z)(e=>{let{theme:t}=e;return{position:"relative",display:"flex",alignItems:"center",justifyContent:"center",flexShrink:0,width:40,height:40,fontFamily:t.typography.fontFamily,fontSize:t.typography.pxToRem(20),lineHeight:1,borderRadius:"50%",overflow:"hidden",userSelect:"none",variants:[{props:{variant:"rounded"},style:{borderRadius:(t.vars||t).shape.borderRadius}},{props:{variant:"square"},style:{borderRadius:0}},{props:{colorDefault:!0},style:{color:(t.vars||t).palette.background.default,...t.vars?{backgroundColor:t.vars.palette.Avatar.defaultBg}:{backgroundColor:t.palette.grey[400],...t.applyStyles("dark",{backgroundColor:t.palette.grey[600]})}}}]}})),h=(0,i.default)("img",{name:"MuiAvatar",slot:"Img",overridesResolver:(e,t)=>t.img})({width:"100%",height:"100%",textAlign:"center",objectFit:"cover",color:"transparent",textIndent:1e4}),g=(0,i.default)(f,{name:"MuiAvatar",slot:"Fallback",overridesResolver:(e,t)=>t.fallback})({width:"75%",height:"75%"});var w=o.forwardRef(function(e,t){let r=(0,c.i)({props:e,name:"MuiAvatar"}),{alt:a,children:i,className:l,component:s="div",slots:f={},slotProps:p={},imgProps:d,sizes:y,src:w,srcSet:O,variant:j="circular",...S}=r,P=null,k={...r,component:s,variant:j},x=function(e){let{crossOrigin:t,referrerPolicy:r,src:n,srcSet:a}=e,[i,l]=o.useState(!1);return o.useEffect(()=>{if(!n&&!a)return;l(!1);let e=!0,o=new Image;return o.onload=()=>{e&&l("loaded")},o.onerror=()=>{e&&l("error")},o.crossOrigin=t,o.referrerPolicy=r,o.src=n,a&&(o.srcset=a),()=>{e=!1}},[t,r,n,a]),i}({...d,..."function"==typeof p.img?p.img(k):p.img,src:w,srcSet:O}),E=w||O,C=E&&"error"!==x;k.colorDefault=!C,delete k.ownerState;let R=m(k),[N,_]=(0,v.Z)("img",{className:R.img,elementType:h,externalForwardedProps:{slots:f,slotProps:{img:{...d,...p.img}}},additionalProps:{alt:a,src:w,srcSet:O,sizes:y},ownerState:k});return P=C?(0,u.jsx)(N,{..._}):i||0===i?i:E&&a?a[0]:(0,u.jsx)(g,{ownerState:k,className:R.fallback}),(0,u.jsx)(b,{as:s,className:(0,n.Z)(R.root,l),ref:t,...S,ownerState:k,children:P})})},14264:function(e,t){var r=Symbol.for("react.element"),o=(Symbol.for("react.portal"),Symbol.for("react.fragment"),Symbol.for("react.strict_mode"),Symbol.for("react.profiler"),Symbol.for("react.provider"),Symbol.for("react.context"),Symbol.for("react.forward_ref"),Symbol.for("react.suspense"),Symbol.for("react.memo"),Symbol.for("react.lazy"),{isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}}),n=Object.assign,a={};function i(e,t,r){this.props=e,this.context=t,this.refs=a,this.updater=r||o}function l(){}function c(e,t,r){this.props=e,this.context=t,this.refs=a,this.updater=r||o}i.prototype.isReactComponent={},i.prototype.setState=function(e,t){if("object"!=typeof e&&"function"!=typeof e&&null!=e)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")},i.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")},l.prototype=i.prototype;var s=c.prototype=new l;s.constructor=c,n(s,i.prototype),s.isPureReactComponent=!0;var u=Object.prototype.hasOwnProperty,f={key:!0,ref:!0,__self:!0,__source:!0};t.createElement=function(e,t,o){var n,a={},i=null,l=null;if(null!=t)for(n in void 0!==t.ref&&(l=t.ref),void 0!==t.key&&(i=""+t.key),t)u.call(t,n)&&!f.hasOwnProperty(n)&&(a[n]=t[n]);var c=arguments.length-2;if(1===c)a.children=o;else if(1<c){for(var s=Array(c),p=0;p<c;p++)s[p]=arguments[p+2];a.children=s}if(e&&e.defaultProps)for(n in c=e.defaultProps)void 0===a[n]&&(a[n]=c[n]);return{$$typeof:r,type:e,key:i,ref:l,props:a,_owner:null}}},94746:function(e,t,r){e.exports=r(14264)},46231:function(e,t,r){r.d(t,{w_:function(){return u}});var o=r(2265),n={color:void 0,size:void 0,className:void 0,style:void 0,attr:void 0},a=o.createContext&&o.createContext(n),i=["attr","size","title"];function l(){return(l=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var o in r)Object.prototype.hasOwnProperty.call(r,o)&&(e[o]=r[o])}return e}).apply(this,arguments)}function c(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,o)}return r}function s(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?c(Object(r),!0).forEach(function(t){var o,n;o=t,n=r[t],(o=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var o=r.call(e,t||"default");if("object"!=typeof o)return o;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(o))in e?Object.defineProperty(e,o,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[o]=n}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):c(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function u(e){return t=>o.createElement(f,l({attr:s({},e.attr)},t),function e(t){return t&&t.map((t,r)=>o.createElement(t.tag,s({key:r},t.attr),e(t.child)))}(e.child))}function f(e){var t=t=>{var r,{attr:n,size:a,title:c}=e,u=function(e,t){if(null==e)return{};var r,o,n=function(e,t){if(null==e)return{};var r={};for(var o in e)if(Object.prototype.hasOwnProperty.call(e,o)){if(t.indexOf(o)>=0)continue;r[o]=e[o]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(o=0;o<a.length;o++)r=a[o],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(n[r]=e[r])}return n}(e,i),f=a||t.size||"1em";return t.className&&(r=t.className),e.className&&(r=(r?r+" ":"")+e.className),o.createElement("svg",l({stroke:"currentColor",fill:"currentColor",strokeWidth:"0"},t.attr,n,u,{className:r,style:s(s({color:e.color||t.color},t.style),e.style),height:f,width:f,xmlns:"http://www.w3.org/2000/svg"}),c&&o.createElement("title",null,c),e.children)};return void 0!==a?o.createElement(a.Consumer,null,e=>t(e)):t(n)}}}]);