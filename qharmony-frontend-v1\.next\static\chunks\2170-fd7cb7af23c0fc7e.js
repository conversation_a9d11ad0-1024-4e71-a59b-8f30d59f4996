"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2170],{29886:function(e,t,r){var o=r(94630),l=r(57437);t.Z=(0,o.Z)((0,l.jsx)("path",{d:"M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6z"}),"Add")},29821:function(e,t,r){var o=r(94630),l=r(57437);t.Z=(0,o.Z)((0,l.jsx)("path",{d:"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2m-2 15-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8z"}),"CheckCircle")},8430:function(e,t,r){var o=r(94630),l=r(57437);t.Z=(0,o.Z)((0,l.jsx)("path",{d:"M19 6.41 17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"}),"Close")},76672:function(e,t,r){var o=r(94630),l=r(57437);t.Z=(0,o.Z)((0,l.jsx)("path",{d:"M6 19c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2V7H6zM19 4h-3.5l-1-1h-5l-1 1H5v2h14z"}),"Delete")},54862:function(e,t,r){var o=r(94630),l=r(57437);t.Z=(0,o.Z)((0,l.jsx)("path",{d:"M3 17.25V21h3.75L17.81 9.94l-3.75-3.75zM20.71 7.04c.39-.39.39-1.02 0-1.41l-2.34-2.34a.996.996 0 0 0-1.41 0l-1.83 1.83 3.75 3.75z"}),"Edit")},71586:function(e,t,r){r.d(t,{Z:function(){return v}});var o=r(2265),l=r(61994),n=r(20801),a=r(16210),i=r(37053),s=r(94143),c=r(50738);function d(e){return(0,c.ZP)("MuiCardActions",e)}(0,s.Z)("MuiCardActions",["root","spacing"]);var u=r(57437);let p=e=>{let{classes:t,disableSpacing:r}=e;return(0,n.Z)({root:["root",!r&&"spacing"]},d,t)},f=(0,a.default)("div",{name:"MuiCardActions",slot:"Root",overridesResolver:(e,t)=>{let{ownerState:r}=e;return[t.root,!r.disableSpacing&&t.spacing]}})({display:"flex",alignItems:"center",padding:8,variants:[{props:{disableSpacing:!1},style:{"& > :not(style) ~ :not(style)":{marginLeft:8}}}]});var v=o.forwardRef(function(e,t){let r=(0,i.i)({props:e,name:"MuiCardActions"}),{disableSpacing:o=!1,className:n,...a}=r,s={...r,disableSpacing:o},c=p(s);return(0,u.jsx)(f,{className:(0,l.Z)(c.root,n),ownerState:s,ref:t,...a})})},36137:function(e,t,r){r.d(t,{Z:function(){return v}});var o=r(2265),l=r(61994),n=r(20801),a=r(16210),i=r(37053),s=r(94143),c=r(50738);function d(e){return(0,c.ZP)("MuiCardContent",e)}(0,s.Z)("MuiCardContent",["root"]);var u=r(57437);let p=e=>{let{classes:t}=e;return(0,n.Z)({root:["root"]},d,t)},f=(0,a.default)("div",{name:"MuiCardContent",slot:"Root",overridesResolver:(e,t)=>t.root})({padding:16,"&:last-child":{paddingBottom:24}});var v=o.forwardRef(function(e,t){let r=(0,i.i)({props:e,name:"MuiCardContent"}),{className:o,component:n="div",...a}=r,s={...r,component:n},c=p(s);return(0,u.jsx)(f,{as:n,className:(0,l.Z)(c.root,o),ownerState:s,ref:t,...a})})},67208:function(e,t,r){r.d(t,{Z:function(){return b}});var o=r(2265),l=r(61994),n=r(20801),a=r(16210),i=r(37053),s=r(53410),c=r(94143),d=r(50738);function u(e){return(0,d.ZP)("MuiCard",e)}(0,c.Z)("MuiCard",["root"]);var p=r(57437);let f=e=>{let{classes:t}=e;return(0,n.Z)({root:["root"]},u,t)},v=(0,a.default)(s.Z,{name:"MuiCard",slot:"Root",overridesResolver:(e,t)=>t.root})({overflow:"hidden"});var b=o.forwardRef(function(e,t){let r=(0,i.i)({props:e,name:"MuiCard"}),{className:o,raised:n=!1,...a}=r,s={...r,raised:n},c=f(s);return(0,p.jsx)(v,{className:(0,l.Z)(c.root,o),elevation:n?8:void 0,ref:t,ownerState:s,...a})})},42187:function(e,t,r){r.d(t,{Z:function(){return M}});var o=r(2265),l=r(61994),n=r(20801),a=r(65208),i=r(34765),s=r(16210),c=r(21086),d=r(37053),u=r(15566),p=r(52559),f=r(84217),v=r(60118),b=r(42596),h=r(67752),m=r(3127),y=r(94143),g=r(50738);function x(e){return(0,g.ZP)("MuiMenuItem",e)}let Z=(0,y.Z)("MuiMenuItem",["root","focusVisible","dense","disabled","divider","gutters","selected"]);var S=r(57437);let w=e=>{let{disabled:t,dense:r,divider:o,disableGutters:l,selected:a,classes:i}=e,s=(0,n.Z)({root:["root",r&&"dense",t&&"disabled",!l&&"gutters",o&&"divider",a&&"selected"]},x,i);return{...i,...s}},C=(0,s.default)(p.Z,{shouldForwardProp:e=>(0,i.Z)(e)||"classes"===e,name:"MuiMenuItem",slot:"Root",overridesResolver:(e,t)=>{let{ownerState:r}=e;return[t.root,r.dense&&t.dense,r.divider&&t.divider,!r.disableGutters&&t.gutters]}})((0,c.Z)(e=>{let{theme:t}=e;return{...t.typography.body1,display:"flex",justifyContent:"flex-start",alignItems:"center",position:"relative",textDecoration:"none",minHeight:48,paddingTop:6,paddingBottom:6,boxSizing:"border-box",whiteSpace:"nowrap","&:hover":{textDecoration:"none",backgroundColor:(t.vars||t).palette.action.hover,"@media (hover: none)":{backgroundColor:"transparent"}},["&.".concat(Z.selected)]:{backgroundColor:t.vars?"rgba(".concat(t.vars.palette.primary.mainChannel," / ").concat(t.vars.palette.action.selectedOpacity,")"):(0,a.Fq)(t.palette.primary.main,t.palette.action.selectedOpacity),["&.".concat(Z.focusVisible)]:{backgroundColor:t.vars?"rgba(".concat(t.vars.palette.primary.mainChannel," / calc(").concat(t.vars.palette.action.selectedOpacity," + ").concat(t.vars.palette.action.focusOpacity,"))"):(0,a.Fq)(t.palette.primary.main,t.palette.action.selectedOpacity+t.palette.action.focusOpacity)}},["&.".concat(Z.selected,":hover")]:{backgroundColor:t.vars?"rgba(".concat(t.vars.palette.primary.mainChannel," / calc(").concat(t.vars.palette.action.selectedOpacity," + ").concat(t.vars.palette.action.hoverOpacity,"))"):(0,a.Fq)(t.palette.primary.main,t.palette.action.selectedOpacity+t.palette.action.hoverOpacity),"@media (hover: none)":{backgroundColor:t.vars?"rgba(".concat(t.vars.palette.primary.mainChannel," / ").concat(t.vars.palette.action.selectedOpacity,")"):(0,a.Fq)(t.palette.primary.main,t.palette.action.selectedOpacity)}},["&.".concat(Z.focusVisible)]:{backgroundColor:(t.vars||t).palette.action.focus},["&.".concat(Z.disabled)]:{opacity:(t.vars||t).palette.action.disabledOpacity},["& + .".concat(b.Z.root)]:{marginTop:t.spacing(1),marginBottom:t.spacing(1)},["& + .".concat(b.Z.inset)]:{marginLeft:52},["& .".concat(m.Z.root)]:{marginTop:0,marginBottom:0},["& .".concat(m.Z.inset)]:{paddingLeft:36},["& .".concat(h.Z.root)]:{minWidth:36},variants:[{props:e=>{let{ownerState:t}=e;return!t.disableGutters},style:{paddingLeft:16,paddingRight:16}},{props:e=>{let{ownerState:t}=e;return t.divider},style:{borderBottom:"1px solid ".concat((t.vars||t).palette.divider),backgroundClip:"padding-box"}},{props:e=>{let{ownerState:t}=e;return!t.dense},style:{[t.breakpoints.up("sm")]:{minHeight:"auto"}}},{props:e=>{let{ownerState:t}=e;return t.dense},style:{minHeight:32,paddingTop:4,paddingBottom:4,...t.typography.body2,["& .".concat(h.Z.root," svg")]:{fontSize:"1.25rem"}}}]}}));var M=o.forwardRef(function(e,t){let r;let n=(0,d.i)({props:e,name:"MuiMenuItem"}),{autoFocus:a=!1,component:i="li",dense:s=!1,divider:c=!1,disableGutters:p=!1,focusVisibleClassName:b,role:h="menuitem",tabIndex:m,className:y,...g}=n,x=o.useContext(u.Z),Z=o.useMemo(()=>({dense:s||x.dense||!1,disableGutters:p}),[x.dense,s,p]),M=o.useRef(null);(0,f.Z)(()=>{a&&M.current&&M.current.focus()},[a]);let B={...n,dense:Z.dense,divider:c,disableGutters:p},R=w(n),I=(0,v.Z)(M,t);return n.disabled||(r=void 0!==m?m:-1),(0,S.jsx)(u.Z.Provider,{value:Z,children:(0,S.jsx)(C,{ref:I,role:h,tabIndex:r,component:i,focusVisibleClassName:(0,l.Z)(R.focusVisible,b),className:(0,l.Z)(R.root,y),...g,ownerState:B,classes:R})})})},28450:function(e,t,r){r.d(t,{Z:function(){return y}});var o=r(2265),l=r(61994),n=r(20801),a=r(52559),i=r(85657),s=r(16210),c=r(21086),d=r(37053),u=r(94143),p=r(50738);function f(e){return(0,p.ZP)("MuiTab",e)}let v=(0,u.Z)("MuiTab",["root","labelIcon","textColorInherit","textColorPrimary","textColorSecondary","selected","disabled","fullWidth","wrapped","iconWrapper","icon"]);var b=r(57437);let h=e=>{let{classes:t,textColor:r,fullWidth:o,wrapped:l,icon:a,label:s,selected:c,disabled:d}=e,u={root:["root",a&&s&&"labelIcon","textColor".concat((0,i.Z)(r)),o&&"fullWidth",l&&"wrapped",c&&"selected",d&&"disabled"],icon:["iconWrapper","icon"]};return(0,n.Z)(u,f,t)},m=(0,s.default)(a.Z,{name:"MuiTab",slot:"Root",overridesResolver:(e,t)=>{let{ownerState:r}=e;return[t.root,r.label&&r.icon&&t.labelIcon,t["textColor".concat((0,i.Z)(r.textColor))],r.fullWidth&&t.fullWidth,r.wrapped&&t.wrapped,{["& .".concat(v.iconWrapper)]:t.iconWrapper},{["& .".concat(v.icon)]:t.icon}]}})((0,c.Z)(e=>{let{theme:t}=e;return{...t.typography.button,maxWidth:360,minWidth:90,position:"relative",minHeight:48,flexShrink:0,padding:"12px 16px",overflow:"hidden",whiteSpace:"normal",textAlign:"center",lineHeight:1.25,variants:[{props:e=>{let{ownerState:t}=e;return t.label&&("top"===t.iconPosition||"bottom"===t.iconPosition)},style:{flexDirection:"column"}},{props:e=>{let{ownerState:t}=e;return t.label&&"top"!==t.iconPosition&&"bottom"!==t.iconPosition},style:{flexDirection:"row"}},{props:e=>{let{ownerState:t}=e;return t.icon&&t.label},style:{minHeight:72,paddingTop:9,paddingBottom:9}},{props:e=>{let{ownerState:t,iconPosition:r}=e;return t.icon&&t.label&&"top"===r},style:{["& > .".concat(v.icon)]:{marginBottom:6}}},{props:e=>{let{ownerState:t,iconPosition:r}=e;return t.icon&&t.label&&"bottom"===r},style:{["& > .".concat(v.icon)]:{marginTop:6}}},{props:e=>{let{ownerState:t,iconPosition:r}=e;return t.icon&&t.label&&"start"===r},style:{["& > .".concat(v.icon)]:{marginRight:t.spacing(1)}}},{props:e=>{let{ownerState:t,iconPosition:r}=e;return t.icon&&t.label&&"end"===r},style:{["& > .".concat(v.icon)]:{marginLeft:t.spacing(1)}}},{props:{textColor:"inherit"},style:{color:"inherit",opacity:.6,["&.".concat(v.selected)]:{opacity:1},["&.".concat(v.disabled)]:{opacity:(t.vars||t).palette.action.disabledOpacity}}},{props:{textColor:"primary"},style:{color:(t.vars||t).palette.text.secondary,["&.".concat(v.selected)]:{color:(t.vars||t).palette.primary.main},["&.".concat(v.disabled)]:{color:(t.vars||t).palette.text.disabled}}},{props:{textColor:"secondary"},style:{color:(t.vars||t).palette.text.secondary,["&.".concat(v.selected)]:{color:(t.vars||t).palette.secondary.main},["&.".concat(v.disabled)]:{color:(t.vars||t).palette.text.disabled}}},{props:e=>{let{ownerState:t}=e;return t.fullWidth},style:{flexShrink:1,flexGrow:1,flexBasis:0,maxWidth:"none"}},{props:e=>{let{ownerState:t}=e;return t.wrapped},style:{fontSize:t.typography.pxToRem(12)}}]}}));var y=o.forwardRef(function(e,t){let r=(0,d.i)({props:e,name:"MuiTab"}),{className:n,disabled:a=!1,disableFocusRipple:i=!1,fullWidth:s,icon:c,iconPosition:u="top",indicator:p,label:f,onChange:v,onClick:y,onFocus:g,selected:x,selectionFollowsFocus:Z,textColor:S="inherit",value:w,wrapped:C=!1,...M}=r,B={...r,disabled:a,disableFocusRipple:i,selected:x,icon:!!c,iconPosition:u,label:!!f,fullWidth:s,textColor:S,wrapped:C},R=h(B),I=c&&f&&o.isValidElement(c)?o.cloneElement(c,{className:(0,l.Z)(R.icon,c.props.className)}):c;return(0,b.jsxs)(m,{focusRipple:!i,className:(0,l.Z)(R.root,n),ref:t,role:"tab","aria-selected":x,disabled:a,onClick:e=>{!x&&v&&v(e,w),y&&y(e)},onFocus:e=>{Z&&!x&&v&&v(e,w),g&&g(e)},ownerState:B,tabIndex:x?0:-1,...M,children:["top"===u||"start"===u?(0,b.jsxs)(o.Fragment,{children:[I,f]}):(0,b.jsxs)(o.Fragment,{children:[f,I]}),p]})})},64461:function(e,t,r){r.d(t,{Z:function(){return X}});var o=r(2265),l=r(61994),n=r(20801),a=r(77126),i=r(15988),s=r(16210),c=r(31691),d=r(21086),u=r(37053),p=r(24801);function f(e){return(1+Math.sin(Math.PI*e-Math.PI/2))/2}var v=r(84217),b=r(77636),h=r(57437);let m={width:99,height:99,position:"absolute",top:-9999,overflow:"scroll"};var y=r(94630),g=(0,y.Z)((0,h.jsx)("path",{d:"M15.41 16.09l-4.58-4.59 4.58-4.59L14 5.5l-6 6 6 6z"}),"KeyboardArrowLeft"),x=(0,y.Z)((0,h.jsx)("path",{d:"M8.59 16.34l4.58-4.59-4.58-4.59L10 5.75l6 6-6 6z"}),"KeyboardArrowRight"),Z=r(52559),S=r(94143),w=r(50738);function C(e){return(0,w.ZP)("MuiTabScrollButton",e)}let M=(0,S.Z)("MuiTabScrollButton",["root","vertical","horizontal","disabled"]),B=e=>{let{classes:t,orientation:r,disabled:o}=e;return(0,n.Z)({root:["root",r,o&&"disabled"]},C,t)},R=(0,s.default)(Z.Z,{name:"MuiTabScrollButton",slot:"Root",overridesResolver:(e,t)=>{let{ownerState:r}=e;return[t.root,r.orientation&&t[r.orientation]]}})({width:40,flexShrink:0,opacity:.8,["&.".concat(M.disabled)]:{opacity:0},variants:[{props:{orientation:"vertical"},style:{width:"100%",height:40,"& svg":{transform:"var(--TabScrollButton-svgRotate)"}}}]}),I=o.forwardRef(function(e,t){var r,o;let n=(0,u.i)({props:e,name:"MuiTabScrollButton"}),{className:s,slots:c={},slotProps:d={},direction:p,orientation:f,disabled:v,...b}=n,m=(0,a.useRtl)(),y={isRtl:m,...n},Z=B(y),S=null!==(r=c.StartScrollButtonIcon)&&void 0!==r?r:g,w=null!==(o=c.EndScrollButtonIcon)&&void 0!==o?o:x,C=(0,i.Z)({elementType:S,externalSlotProps:d.startScrollButtonIcon,additionalProps:{fontSize:"small"},ownerState:y}),M=(0,i.Z)({elementType:w,externalSlotProps:d.endScrollButtonIcon,additionalProps:{fontSize:"small"},ownerState:y});return(0,h.jsx)(R,{component:"div",className:(0,l.Z)(Z.root,s),ref:t,role:null,ownerState:y,tabIndex:null,...b,style:{...b.style,..."vertical"===f&&{"--TabScrollButton-svgRotate":"rotate(".concat(m?-90:90,"deg)")}},children:"left"===p?(0,h.jsx)(S,{...C}):(0,h.jsx)(w,{...M})})});var T=r(9665);function E(e){return(0,w.ZP)("MuiTabs",e)}let k=(0,S.Z)("MuiTabs",["root","vertical","list","flexContainer","flexContainerVertical","centered","scroller","fixed","scrollableX","scrollableY","hideScrollbar","scrollButtons","scrollButtonsHideMobile","indicator"]);var P=r(2262),j=r(79114);let z=(e,t)=>e===t?e.firstChild:t&&t.nextElementSibling?t.nextElementSibling:e.firstChild,L=(e,t)=>e===t?e.lastChild:t&&t.previousElementSibling?t.previousElementSibling:e.lastChild,W=(e,t,r)=>{let o=!1,l=r(e,t);for(;l;){if(l===e.firstChild){if(o)return;o=!0}let t=l.disabled||"true"===l.getAttribute("aria-disabled");if(!l.hasAttribute("tabindex")||t)l=r(e,l);else{l.focus();return}}},A=e=>{let{vertical:t,fixed:r,hideScrollbar:o,scrollableX:l,scrollableY:a,centered:i,scrollButtonsHideMobile:s,classes:c}=e;return(0,n.Z)({root:["root",t&&"vertical"],scroller:["scroller",r&&"fixed",o&&"hideScrollbar",l&&"scrollableX",a&&"scrollableY"],list:["list","flexContainer",t&&"flexContainerVertical",t&&"vertical",i&&"centered"],indicator:["indicator"],scrollButtons:["scrollButtons",s&&"scrollButtonsHideMobile"],scrollableX:[l&&"scrollableX"],hideScrollbar:[o&&"hideScrollbar"]},E,c)},O=(0,s.default)("div",{name:"MuiTabs",slot:"Root",overridesResolver:(e,t)=>{let{ownerState:r}=e;return[{["& .".concat(k.scrollButtons)]:t.scrollButtons},{["& .".concat(k.scrollButtons)]:r.scrollButtonsHideMobile&&t.scrollButtonsHideMobile},t.root,r.vertical&&t.vertical]}})((0,d.Z)(e=>{let{theme:t}=e;return{overflow:"hidden",minHeight:48,WebkitOverflowScrolling:"touch",display:"flex",variants:[{props:e=>{let{ownerState:t}=e;return t.vertical},style:{flexDirection:"column"}},{props:e=>{let{ownerState:t}=e;return t.scrollButtonsHideMobile},style:{["& .".concat(k.scrollButtons)]:{[t.breakpoints.down("sm")]:{display:"none"}}}}]}})),H=(0,s.default)("div",{name:"MuiTabs",slot:"Scroller",overridesResolver:(e,t)=>{let{ownerState:r}=e;return[t.scroller,r.fixed&&t.fixed,r.hideScrollbar&&t.hideScrollbar,r.scrollableX&&t.scrollableX,r.scrollableY&&t.scrollableY]}})({position:"relative",display:"inline-block",flex:"1 1 auto",whiteSpace:"nowrap",variants:[{props:e=>{let{ownerState:t}=e;return t.fixed},style:{overflowX:"hidden",width:"100%"}},{props:e=>{let{ownerState:t}=e;return t.hideScrollbar},style:{scrollbarWidth:"none","&::-webkit-scrollbar":{display:"none"}}},{props:e=>{let{ownerState:t}=e;return t.scrollableX},style:{overflowX:"auto",overflowY:"hidden"}},{props:e=>{let{ownerState:t}=e;return t.scrollableY},style:{overflowY:"auto",overflowX:"hidden"}}]}),N=(0,s.default)("div",{name:"MuiTabs",slot:"List",overridesResolver:(e,t)=>{let{ownerState:r}=e;return[t.list,t.flexContainer,r.vertical&&t.flexContainerVertical,r.centered&&t.centered]}})({display:"flex",variants:[{props:e=>{let{ownerState:t}=e;return t.vertical},style:{flexDirection:"column"}},{props:e=>{let{ownerState:t}=e;return t.centered},style:{justifyContent:"center"}}]}),D=(0,s.default)("span",{name:"MuiTabs",slot:"Indicator",overridesResolver:(e,t)=>t.indicator})((0,d.Z)(e=>{let{theme:t}=e;return{position:"absolute",height:2,bottom:0,width:"100%",transition:t.transitions.create(),variants:[{props:{indicatorColor:"primary"},style:{backgroundColor:(t.vars||t).palette.primary.main}},{props:{indicatorColor:"secondary"},style:{backgroundColor:(t.vars||t).palette.secondary.main}},{props:e=>{let{ownerState:t}=e;return t.vertical},style:{height:"100%",width:2,right:0}}]}})),F=(0,s.default)(function(e){let{onChange:t,...r}=e,l=o.useRef(),n=o.useRef(null),a=()=>{l.current=n.current.offsetHeight-n.current.clientHeight};return(0,v.Z)(()=>{let e=(0,p.Z)(()=>{let e=l.current;a(),e!==l.current&&t(l.current)}),r=(0,b.Z)(n.current);return r.addEventListener("resize",e),()=>{e.clear(),r.removeEventListener("resize",e)}},[t]),o.useEffect(()=>{a(),t(l.current)},[t]),(0,h.jsx)("div",{style:m,...r,ref:n})})({overflowX:"auto",overflowY:"hidden",scrollbarWidth:"none","&::-webkit-scrollbar":{display:"none"}}),V={};var X=o.forwardRef(function(e,t){let r=(0,u.i)({props:e,name:"MuiTabs"}),n=(0,c.default)(),s=(0,a.useRtl)(),{"aria-label":d,"aria-labelledby":v,action:m,centered:y=!1,children:g,className:x,component:Z="div",allowScrollButtonsMobile:S=!1,indicatorColor:w="primary",onChange:C,orientation:M="horizontal",ScrollButtonComponent:B,scrollButtons:R="auto",selectionFollowsFocus:E,slots:k={},slotProps:X={},TabIndicatorProps:Y={},TabScrollButtonProps:K={},textColor:q="primary",value:_,variant:G="standard",visibleScrollbar:U=!1,...J}=r,Q="scrollable"===G,$="vertical"===M,ee=$?"scrollTop":"scrollLeft",et=$?"top":"left",er=$?"bottom":"right",eo=$?"clientHeight":"clientWidth",el=$?"height":"width",en={...r,component:Z,allowScrollButtonsMobile:S,indicatorColor:w,orientation:M,vertical:$,scrollButtons:R,textColor:q,variant:G,visibleScrollbar:U,fixed:!Q,hideScrollbar:Q&&!U,scrollableX:Q&&!$,scrollableY:Q&&$,centered:y&&!Q,scrollButtonsHideMobile:!S},ea=A(en),ei=(0,i.Z)({elementType:k.StartScrollButtonIcon,externalSlotProps:X.startScrollButtonIcon,ownerState:en}),es=(0,i.Z)({elementType:k.EndScrollButtonIcon,externalSlotProps:X.endScrollButtonIcon,ownerState:en}),[ec,ed]=o.useState(!1),[eu,ep]=o.useState(V),[ef,ev]=o.useState(!1),[eb,eh]=o.useState(!1),[em,ey]=o.useState(!1),[eg,ex]=o.useState({overflow:"hidden",scrollbarWidth:0}),eZ=new Map,eS=o.useRef(null),ew=o.useRef(null),eC={slots:k,slotProps:{indicator:Y,scrollButton:K,...X}},eM=()=>{let e,t;let r=eS.current;if(r){let t=r.getBoundingClientRect();e={clientWidth:r.clientWidth,scrollLeft:r.scrollLeft,scrollTop:r.scrollTop,scrollWidth:r.scrollWidth,top:t.top,bottom:t.bottom,left:t.left,right:t.right}}if(r&&!1!==_){let e=ew.current.children;if(e.length>0){let r=e[eZ.get(_)];t=r?r.getBoundingClientRect():null}}return{tabsMeta:e,tabMeta:t}},eB=(0,T.Z)(()=>{let e;let{tabsMeta:t,tabMeta:r}=eM(),o=0;$?(e="top",r&&t&&(o=r.top-t.top+t.scrollTop)):(e=s?"right":"left",r&&t&&(o=(s?-1:1)*(r[e]-t[e]+t.scrollLeft)));let l={[e]:o,[el]:r?r[el]:0};if("number"!=typeof eu[e]||"number"!=typeof eu[el])ep(l);else{let t=Math.abs(eu[e]-l[e]),r=Math.abs(eu[el]-l[el]);(t>=1||r>=1)&&ep(l)}}),eR=function(e){let{animation:t=!0}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};t?function(e,t,r){let o=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},l=arguments.length>4&&void 0!==arguments[4]?arguments[4]:()=>{},{ease:n=f,duration:a=300}=o,i=null,s=t[e],c=!1,d=o=>{if(c){l(Error("Animation cancelled"));return}null===i&&(i=o);let u=Math.min(1,(o-i)/a);if(t[e]=n(u)*(r-s)+s,u>=1){requestAnimationFrame(()=>{l(null)});return}requestAnimationFrame(d)};return s===r?l(Error("Element already at target position")):requestAnimationFrame(d),()=>{c=!0}}(ee,eS.current,e,{duration:n.transitions.duration.standard}):eS.current[ee]=e},eI=e=>{let t=eS.current[ee];$?t+=e:t+=e*(s?-1:1),eR(t)},eT=()=>{let e=eS.current[eo],t=0,r=Array.from(ew.current.children);for(let o=0;o<r.length;o+=1){let l=r[o];if(t+l[eo]>e){0===o&&(t=e);break}t+=l[eo]}return t},eE=()=>{eI(-1*eT())},ek=()=>{eI(eT())},[eP,{onChange:ej,...ez}]=(0,j.Z)("scrollbar",{className:(0,l.Z)(ea.scrollableX,ea.hideScrollbar),elementType:F,shouldForwardComponentProp:!0,externalForwardedProps:eC,ownerState:en}),eL=o.useCallback(e=>{null==ej||ej(e),ex({overflow:null,scrollbarWidth:e})},[ej]),[eW,eA]=(0,j.Z)("scrollButtons",{className:(0,l.Z)(ea.scrollButtons,K.className),elementType:I,externalForwardedProps:eC,ownerState:en,additionalProps:{orientation:M,slots:{StartScrollButtonIcon:k.startScrollButtonIcon||k.StartScrollButtonIcon,EndScrollButtonIcon:k.endScrollButtonIcon||k.EndScrollButtonIcon},slotProps:{startScrollButtonIcon:ei,endScrollButtonIcon:es}}}),eO=(0,T.Z)(e=>{let{tabsMeta:t,tabMeta:r}=eM();r&&t&&(r[et]<t[et]?eR(t[ee]+(r[et]-t[et]),{animation:e}):r[er]>t[er]&&eR(t[ee]+(r[er]-t[er]),{animation:e}))}),eH=(0,T.Z)(()=>{Q&&!1!==R&&ey(!em)});o.useEffect(()=>{let e,t;let r=(0,p.Z)(()=>{eS.current&&eB()}),o=(0,b.Z)(eS.current);return o.addEventListener("resize",r),"undefined"!=typeof ResizeObserver&&(e=new ResizeObserver(r),Array.from(ew.current.children).forEach(t=>{e.observe(t)})),"undefined"!=typeof MutationObserver&&(t=new MutationObserver(t=>{t.forEach(t=>{t.removedNodes.forEach(t=>{null==e||e.unobserve(t)}),t.addedNodes.forEach(t=>{null==e||e.observe(t)})}),r(),eH()})).observe(ew.current,{childList:!0}),()=>{r.clear(),o.removeEventListener("resize",r),null==t||t.disconnect(),null==e||e.disconnect()}},[eB,eH]),o.useEffect(()=>{let e=Array.from(ew.current.children),t=e.length;if("undefined"!=typeof IntersectionObserver&&t>0&&Q&&!1!==R){let r=e[0],o=e[t-1],l={root:eS.current,threshold:.99},n=new IntersectionObserver(e=>{ev(!e[0].isIntersecting)},l);n.observe(r);let a=new IntersectionObserver(e=>{eh(!e[0].isIntersecting)},l);return a.observe(o),()=>{n.disconnect(),a.disconnect()}}},[Q,R,em,null==g?void 0:g.length]),o.useEffect(()=>{ed(!0)},[]),o.useEffect(()=>{eB()}),o.useEffect(()=>{eO(V!==eu)},[eO,eu]),o.useImperativeHandle(m,()=>({updateIndicator:eB,updateScrollButtons:eH}),[eB,eH]);let[eN,eD]=(0,j.Z)("indicator",{className:(0,l.Z)(ea.indicator,Y.className),elementType:D,externalForwardedProps:eC,ownerState:en,additionalProps:{style:eu}}),eF=(0,h.jsx)(eN,{...eD}),eV=0,eX=o.Children.map(g,e=>{if(!o.isValidElement(e))return null;let t=void 0===e.props.value?eV:e.props.value;eZ.set(t,eV);let r=t===_;return eV+=1,o.cloneElement(e,{fullWidth:"fullWidth"===G,indicator:r&&!ec&&eF,selected:r,selectionFollowsFocus:E,onChange:C,textColor:q,value:t,...1!==eV||!1!==_||e.props.tabIndex?{}:{tabIndex:0}})}),eY=e=>{if(e.altKey||e.shiftKey||e.ctrlKey||e.metaKey)return;let t=ew.current,r=(0,P.Z)(t).activeElement;if("tab"!==r.getAttribute("role"))return;let o="horizontal"===M?"ArrowLeft":"ArrowUp",l="horizontal"===M?"ArrowRight":"ArrowDown";switch("horizontal"===M&&s&&(o="ArrowRight",l="ArrowLeft"),e.key){case o:e.preventDefault(),W(t,r,L);break;case l:e.preventDefault(),W(t,r,z);break;case"Home":e.preventDefault(),W(t,null,z);break;case"End":e.preventDefault(),W(t,null,L)}},eK=(()=>{let e={};e.scrollbarSizeListener=Q?(0,h.jsx)(eP,{...ez,onChange:eL}):null;let t=Q&&("auto"===R&&(ef||eb)||!0===R);return e.scrollButtonStart=t?(0,h.jsx)(eW,{direction:s?"right":"left",onClick:eE,disabled:!ef,...eA}):null,e.scrollButtonEnd=t?(0,h.jsx)(eW,{direction:s?"left":"right",onClick:ek,disabled:!eb,...eA}):null,e})(),[eq,e_]=(0,j.Z)("root",{ref:t,className:(0,l.Z)(ea.root,x),elementType:O,externalForwardedProps:{...eC,...J,component:Z},ownerState:en}),[eG,eU]=(0,j.Z)("scroller",{ref:eS,className:ea.scroller,elementType:H,externalForwardedProps:eC,ownerState:en,additionalProps:{style:{overflow:eg.overflow,[$?"margin".concat(s?"Left":"Right"):"marginBottom"]:U?void 0:-eg.scrollbarWidth}}}),[eJ,eQ]=(0,j.Z)("list",{ref:ew,className:(0,l.Z)(ea.list,ea.flexContainer),elementType:N,externalForwardedProps:eC,ownerState:en,getSlotProps:e=>({...e,onKeyDown:t=>{var r;eY(t),null===(r=e.onKeyDown)||void 0===r||r.call(e,t)}})});return(0,h.jsxs)(eq,{...e_,children:[eK.scrollButtonStart,eK.scrollbarSizeListener,(0,h.jsxs)(eG,{...eU,children:[(0,h.jsx)(eJ,{"aria-label":d,"aria-labelledby":v,"aria-orientation":"vertical"===M?"vertical":null,role:"tablist",...eQ,children:eX}),ec&&eF]}),eK.scrollButtonEnd]})})}}]);