"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[32],{50032:function(t,e,n){n.d(e,{x7:function(){return tE},Me:function(){return tv},oo:function(){return tO},RR:function(){return tT},Cp:function(){return tA},dr:function(){return tC},cv:function(){return tb},uY:function(){return tR},dp:function(){return tL}});let i=["top","right","bottom","left"],r=Math.min,o=Math.max,l=Math.round,f=Math.floor,a=t=>({x:t,y:t}),c={left:"right",right:"left",bottom:"top",top:"bottom"},u={start:"end",end:"start"};function s(t,e){return"function"==typeof t?t(e):t}function d(t){return t.split("-")[0]}function h(t){return t.split("-")[1]}function p(t){return"x"===t?"y":"x"}function m(t){return"y"===t?"height":"width"}let g=new Set(["top","bottom"]);function w(t){return g.has(d(t))?"y":"x"}function y(t){return t.replace(/start|end/g,t=>u[t])}let x=["left","right"],v=["right","left"],b=["top","bottom"],R=["bottom","top"];function T(t){return t.replace(/left|right|bottom|top/g,t=>c[t])}function L(t){return"number"!=typeof t?{top:0,right:0,bottom:0,left:0,...t}:{top:t,right:t,bottom:t,left:t}}function A(t){let{x:e,y:n,width:i,height:r}=t;return{width:i,height:r,top:n,left:e,right:e+i,bottom:n+r,x:e,y:n}}function E(t,e,n){let i,{reference:r,floating:o}=t,l=w(e),f=p(w(e)),a=m(f),c=d(e),u="y"===l,s=r.x+r.width/2-o.width/2,g=r.y+r.height/2-o.height/2,y=r[a]/2-o[a]/2;switch(c){case"top":i={x:s,y:r.y-o.height};break;case"bottom":i={x:s,y:r.y+r.height};break;case"right":i={x:r.x+r.width,y:g};break;case"left":i={x:r.x-o.width,y:g};break;default:i={x:r.x,y:r.y}}switch(h(e)){case"start":i[f]-=y*(n&&u?-1:1);break;case"end":i[f]+=y*(n&&u?-1:1)}return i}let C=async(t,e,n)=>{let{placement:i="bottom",strategy:r="absolute",middleware:o=[],platform:l}=n,f=o.filter(Boolean),a=await (null==l.isRTL?void 0:l.isRTL(e)),c=await l.getElementRects({reference:t,floating:e,strategy:r}),{x:u,y:s}=E(c,i,a),d=i,h={},p=0;for(let n=0;n<f.length;n++){let{name:o,fn:m}=f[n],{x:g,y:w,data:y,reset:x}=await m({x:u,y:s,initialPlacement:i,placement:d,strategy:r,middlewareData:h,rects:c,platform:l,elements:{reference:t,floating:e}});u=null!=g?g:u,s=null!=w?w:s,h={...h,[o]:{...h[o],...y}},x&&p<=50&&(p++,"object"==typeof x&&(x.placement&&(d=x.placement),x.rects&&(c=!0===x.rects?await l.getElementRects({reference:t,floating:e,strategy:r}):x.rects),{x:u,y:s}=E(c,d,a)),n=-1)}return{x:u,y:s,placement:d,strategy:r,middlewareData:h}};async function O(t,e){var n;void 0===e&&(e={});let{x:i,y:r,platform:o,rects:l,elements:f,strategy:a}=t,{boundary:c="clippingAncestors",rootBoundary:u="viewport",elementContext:d="floating",altBoundary:h=!1,padding:p=0}=s(e,t),m=L(p),g=f[h?"floating"===d?"reference":"floating":d],w=A(await o.getClippingRect({element:null==(n=await (null==o.isElement?void 0:o.isElement(g)))||n?g:g.contextElement||await (null==o.getDocumentElement?void 0:o.getDocumentElement(f.floating)),boundary:c,rootBoundary:u,strategy:a})),y="floating"===d?{x:i,y:r,width:l.floating.width,height:l.floating.height}:l.reference,x=await (null==o.getOffsetParent?void 0:o.getOffsetParent(f.floating)),v=await (null==o.isElement?void 0:o.isElement(x))&&await (null==o.getScale?void 0:o.getScale(x))||{x:1,y:1},b=A(o.convertOffsetParentRelativeRectToViewportRelativeRect?await o.convertOffsetParentRelativeRectToViewportRelativeRect({elements:f,rect:y,offsetParent:x,strategy:a}):y);return{top:(w.top-b.top+m.top)/v.y,bottom:(b.bottom-w.bottom+m.bottom)/v.y,left:(w.left-b.left+m.left)/v.x,right:(b.right-w.right+m.right)/v.x}}function S(t,e){return{top:t.top-e.height,right:t.right-e.width,bottom:t.bottom-e.height,left:t.left-e.width}}function D(t){return i.some(e=>t[e]>=0)}let k=new Set(["left","top"]);async function H(t,e){let{placement:n,platform:i,elements:r}=t,o=await (null==i.isRTL?void 0:i.isRTL(r.floating)),l=d(n),f=h(n),a="y"===w(n),c=k.has(l)?-1:1,u=o&&a?-1:1,p=s(e,t),{mainAxis:m,crossAxis:g,alignmentAxis:y}="number"==typeof p?{mainAxis:p,crossAxis:0,alignmentAxis:null}:{mainAxis:p.mainAxis||0,crossAxis:p.crossAxis||0,alignmentAxis:p.alignmentAxis};return f&&"number"==typeof y&&(g="end"===f?-1*y:y),a?{x:g*u,y:m*c}:{x:m*c,y:g*u}}function F(){return"undefined"!=typeof window}function P(t){return V(t)?(t.nodeName||"").toLowerCase():"#document"}function W(t){var e;return(null==t||null==(e=t.ownerDocument)?void 0:e.defaultView)||window}function M(t){var e;return null==(e=(V(t)?t.ownerDocument:t.document)||window.document)?void 0:e.documentElement}function V(t){return!!F()&&(t instanceof Node||t instanceof W(t).Node)}function B(t){return!!F()&&(t instanceof Element||t instanceof W(t).Element)}function N(t){return!!F()&&(t instanceof HTMLElement||t instanceof W(t).HTMLElement)}function _(t){return!!F()&&"undefined"!=typeof ShadowRoot&&(t instanceof ShadowRoot||t instanceof W(t).ShadowRoot)}let z=new Set(["inline","contents"]);function I(t){let{overflow:e,overflowX:n,overflowY:i,display:r}=Z(t);return/auto|scroll|overlay|hidden|clip/.test(e+i+n)&&!z.has(r)}let j=new Set(["table","td","th"]),q=[":popover-open",":modal"];function Y(t){return q.some(e=>{try{return t.matches(e)}catch(t){return!1}})}let X=["transform","translate","scale","rotate","perspective"],$=["transform","translate","scale","rotate","perspective","filter"],G=["paint","layout","strict","content"];function J(t){let e=K(),n=B(t)?Z(t):t;return X.some(t=>!!n[t]&&"none"!==n[t])||!!n.containerType&&"normal"!==n.containerType||!e&&!!n.backdropFilter&&"none"!==n.backdropFilter||!e&&!!n.filter&&"none"!==n.filter||$.some(t=>(n.willChange||"").includes(t))||G.some(t=>(n.contain||"").includes(t))}function K(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}let Q=new Set(["html","body","#document"]);function U(t){return Q.has(P(t))}function Z(t){return W(t).getComputedStyle(t)}function tt(t){return B(t)?{scrollLeft:t.scrollLeft,scrollTop:t.scrollTop}:{scrollLeft:t.scrollX,scrollTop:t.scrollY}}function te(t){if("html"===P(t))return t;let e=t.assignedSlot||t.parentNode||_(t)&&t.host||M(t);return _(e)?e.host:e}function tn(t,e,n){var i;void 0===e&&(e=[]),void 0===n&&(n=!0);let r=function t(e){let n=te(e);return U(n)?e.ownerDocument?e.ownerDocument.body:e.body:N(n)&&I(n)?n:t(n)}(t),o=r===(null==(i=t.ownerDocument)?void 0:i.body),l=W(r);if(o){let t=ti(l);return e.concat(l,l.visualViewport||[],I(r)?r:[],t&&n?tn(t):[])}return e.concat(r,tn(r,[],n))}function ti(t){return t.parent&&Object.getPrototypeOf(t.parent)?t.frameElement:null}function tr(t){let e=Z(t),n=parseFloat(e.width)||0,i=parseFloat(e.height)||0,r=N(t),o=r?t.offsetWidth:n,f=r?t.offsetHeight:i,a=l(n)!==o||l(i)!==f;return a&&(n=o,i=f),{width:n,height:i,$:a}}function to(t){return B(t)?t:t.contextElement}function tl(t){let e=to(t);if(!N(e))return a(1);let n=e.getBoundingClientRect(),{width:i,height:r,$:o}=tr(e),f=(o?l(n.width):n.width)/i,c=(o?l(n.height):n.height)/r;return f&&Number.isFinite(f)||(f=1),c&&Number.isFinite(c)||(c=1),{x:f,y:c}}let tf=a(0);function ta(t){let e=W(t);return K()&&e.visualViewport?{x:e.visualViewport.offsetLeft,y:e.visualViewport.offsetTop}:tf}function tc(t,e,n,i){var r;void 0===e&&(e=!1),void 0===n&&(n=!1);let o=t.getBoundingClientRect(),l=to(t),f=a(1);e&&(i?B(i)&&(f=tl(i)):f=tl(t));let c=(void 0===(r=n)&&(r=!1),i&&(!r||i===W(l))&&r)?ta(l):a(0),u=(o.left+c.x)/f.x,s=(o.top+c.y)/f.y,d=o.width/f.x,h=o.height/f.y;if(l){let t=W(l),e=i&&B(i)?W(i):i,n=t,r=ti(n);for(;r&&i&&e!==n;){let t=tl(r),e=r.getBoundingClientRect(),i=Z(r),o=e.left+(r.clientLeft+parseFloat(i.paddingLeft))*t.x,l=e.top+(r.clientTop+parseFloat(i.paddingTop))*t.y;u*=t.x,s*=t.y,d*=t.x,h*=t.y,u+=o,s+=l,r=ti(n=W(r))}}return A({width:d,height:h,x:u,y:s})}function tu(t,e){let n=tt(t).scrollLeft;return e?e.left+n:tc(M(t)).left+n}function ts(t,e,n){void 0===n&&(n=!1);let i=t.getBoundingClientRect();return{x:i.left+e.scrollLeft-(n?0:tu(t,i)),y:i.top+e.scrollTop}}let td=new Set(["absolute","fixed"]);function th(t,e,n){let i;if("viewport"===e)i=function(t,e){let n=W(t),i=M(t),r=n.visualViewport,o=i.clientWidth,l=i.clientHeight,f=0,a=0;if(r){o=r.width,l=r.height;let t=K();(!t||t&&"fixed"===e)&&(f=r.offsetLeft,a=r.offsetTop)}return{width:o,height:l,x:f,y:a}}(t,n);else if("document"===e)i=function(t){let e=M(t),n=tt(t),i=t.ownerDocument.body,r=o(e.scrollWidth,e.clientWidth,i.scrollWidth,i.clientWidth),l=o(e.scrollHeight,e.clientHeight,i.scrollHeight,i.clientHeight),f=-n.scrollLeft+tu(t),a=-n.scrollTop;return"rtl"===Z(i).direction&&(f+=o(e.clientWidth,i.clientWidth)-r),{width:r,height:l,x:f,y:a}}(M(t));else if(B(e))i=function(t,e){let n=tc(t,!0,"fixed"===e),i=n.top+t.clientTop,r=n.left+t.clientLeft,o=N(t)?tl(t):a(1),l=t.clientWidth*o.x;return{width:l,height:t.clientHeight*o.y,x:r*o.x,y:i*o.y}}(e,n);else{let n=ta(t);i={x:e.x-n.x,y:e.y-n.y,width:e.width,height:e.height}}return A(i)}function tp(t){return"static"===Z(t).position}function tm(t,e){if(!N(t)||"fixed"===Z(t).position)return null;if(e)return e(t);let n=t.offsetParent;return M(t)===n&&(n=n.ownerDocument.body),n}function tg(t,e){var n;let i=W(t);if(Y(t))return i;if(!N(t)){let e=te(t);for(;e&&!U(e);){if(B(e)&&!tp(e))return e;e=te(e)}return i}let r=tm(t,e);for(;r&&(n=r,j.has(P(n)))&&tp(r);)r=tm(r,e);return r&&U(r)&&tp(r)&&!J(r)?i:r||function(t){let e=te(t);for(;N(e)&&!U(e);){if(J(e))return e;if(Y(e))break;e=te(e)}return null}(t)||i}let tw=async function(t){let e=this.getOffsetParent||tg,n=this.getDimensions,i=await n(t.floating);return{reference:function(t,e,n){let i=N(e),r=M(e),o="fixed"===n,l=tc(t,!0,o,e),f={scrollLeft:0,scrollTop:0},c=a(0);if(i||!i&&!o){if(("body"!==P(e)||I(r))&&(f=tt(e)),i){let t=tc(e,!0,o,e);c.x=t.x+e.clientLeft,c.y=t.y+e.clientTop}else r&&(c.x=tu(r))}o&&!i&&r&&(c.x=tu(r));let u=!r||i||o?a(0):ts(r,f);return{x:l.left+f.scrollLeft-c.x-u.x,y:l.top+f.scrollTop-c.y-u.y,width:l.width,height:l.height}}(t.reference,await e(t.floating),t.strategy),floating:{x:0,y:0,width:i.width,height:i.height}}},ty={convertOffsetParentRelativeRectToViewportRelativeRect:function(t){let{elements:e,rect:n,offsetParent:i,strategy:r}=t,o="fixed"===r,l=M(i),f=!!e&&Y(e.floating);if(i===l||f&&o)return n;let c={scrollLeft:0,scrollTop:0},u=a(1),s=a(0),d=N(i);if((d||!d&&!o)&&(("body"!==P(i)||I(l))&&(c=tt(i)),N(i))){let t=tc(i);u=tl(i),s.x=t.x+i.clientLeft,s.y=t.y+i.clientTop}let h=!l||d||o?a(0):ts(l,c,!0);return{width:n.width*u.x,height:n.height*u.y,x:n.x*u.x-c.scrollLeft*u.x+s.x+h.x,y:n.y*u.y-c.scrollTop*u.y+s.y+h.y}},getDocumentElement:M,getClippingRect:function(t){let{element:e,boundary:n,rootBoundary:i,strategy:l}=t,f=[..."clippingAncestors"===n?Y(e)?[]:function(t,e){let n=e.get(t);if(n)return n;let i=tn(t,[],!1).filter(t=>B(t)&&"body"!==P(t)),r=null,o="fixed"===Z(t).position,l=o?te(t):t;for(;B(l)&&!U(l);){let e=Z(l),n=J(l);n||"fixed"!==e.position||(r=null),(o?!n&&!r:!n&&"static"===e.position&&!!r&&td.has(r.position)||I(l)&&!n&&function t(e,n){let i=te(e);return!(i===n||!B(i)||U(i))&&("fixed"===Z(i).position||t(i,n))}(t,l))?i=i.filter(t=>t!==l):r=e,l=te(l)}return e.set(t,i),i}(e,this._c):[].concat(n),i],a=f[0],c=f.reduce((t,n)=>{let i=th(e,n,l);return t.top=o(i.top,t.top),t.right=r(i.right,t.right),t.bottom=r(i.bottom,t.bottom),t.left=o(i.left,t.left),t},th(e,a,l));return{width:c.right-c.left,height:c.bottom-c.top,x:c.left,y:c.top}},getOffsetParent:tg,getElementRects:tw,getClientRects:function(t){return Array.from(t.getClientRects())},getDimensions:function(t){let{width:e,height:n}=tr(t);return{width:e,height:n}},getScale:tl,isElement:B,isRTL:function(t){return"rtl"===Z(t).direction}};function tx(t,e){return t.x===e.x&&t.y===e.y&&t.width===e.width&&t.height===e.height}function tv(t,e,n,i){let l;void 0===i&&(i={});let{ancestorScroll:a=!0,ancestorResize:c=!0,elementResize:u="function"==typeof ResizeObserver,layoutShift:s="function"==typeof IntersectionObserver,animationFrame:d=!1}=i,h=to(t),p=a||c?[...h?tn(h):[],...tn(e)]:[];p.forEach(t=>{a&&t.addEventListener("scroll",n,{passive:!0}),c&&t.addEventListener("resize",n)});let m=h&&s?function(t,e){let n,i=null,l=M(t);function a(){var t;clearTimeout(n),null==(t=i)||t.disconnect(),i=null}return!function c(u,s){void 0===u&&(u=!1),void 0===s&&(s=1),a();let d=t.getBoundingClientRect(),{left:h,top:p,width:m,height:g}=d;if(u||e(),!m||!g)return;let w=f(p),y=f(l.clientWidth-(h+m)),x={rootMargin:-w+"px "+-y+"px "+-f(l.clientHeight-(p+g))+"px "+-f(h)+"px",threshold:o(0,r(1,s))||1},v=!0;function b(e){let i=e[0].intersectionRatio;if(i!==s){if(!v)return c();i?c(!1,i):n=setTimeout(()=>{c(!1,1e-7)},1e3)}1!==i||tx(d,t.getBoundingClientRect())||c(),v=!1}try{i=new IntersectionObserver(b,{...x,root:l.ownerDocument})}catch(t){i=new IntersectionObserver(b,x)}i.observe(t)}(!0),a}(h,n):null,g=-1,w=null;u&&(w=new ResizeObserver(t=>{let[i]=t;i&&i.target===h&&w&&(w.unobserve(e),cancelAnimationFrame(g),g=requestAnimationFrame(()=>{var t;null==(t=w)||t.observe(e)})),n()}),h&&!d&&w.observe(h),w.observe(e));let y=d?tc(t):null;return d&&function e(){let i=tc(t);y&&!tx(y,i)&&n(),y=i,l=requestAnimationFrame(e)}(),n(),()=>{var t;p.forEach(t=>{a&&t.removeEventListener("scroll",n),c&&t.removeEventListener("resize",n)}),null==m||m(),null==(t=w)||t.disconnect(),w=null,d&&cancelAnimationFrame(l)}}let tb=function(t){return void 0===t&&(t=0),{name:"offset",options:t,async fn(e){var n,i;let{x:r,y:o,placement:l,middlewareData:f}=e,a=await H(e,t);return l===(null==(n=f.offset)?void 0:n.placement)&&null!=(i=f.arrow)&&i.alignmentOffset?{}:{x:r+a.x,y:o+a.y,data:{...a,placement:l}}}}},tR=function(t){return void 0===t&&(t={}),{name:"shift",options:t,async fn(e){let{x:n,y:i,placement:l}=e,{mainAxis:f=!0,crossAxis:a=!1,limiter:c={fn:t=>{let{x:e,y:n}=t;return{x:e,y:n}}},...u}=s(t,e),h={x:n,y:i},m=await O(e,u),g=w(d(l)),y=p(g),x=h[y],v=h[g];if(f){let t="y"===y?"top":"left",e="y"===y?"bottom":"right",n=x+m[t],i=x-m[e];x=o(n,r(x,i))}if(a){let t="y"===g?"top":"left",e="y"===g?"bottom":"right",n=v+m[t],i=v-m[e];v=o(n,r(v,i))}let b=c.fn({...e,[y]:x,[g]:v});return{...b,data:{x:b.x-n,y:b.y-i,enabled:{[y]:f,[g]:a}}}}}},tT=function(t){return void 0===t&&(t={}),{name:"flip",options:t,async fn(e){var n,i,r,o,l;let{placement:f,middlewareData:a,rects:c,initialPlacement:u,platform:g,elements:L}=e,{mainAxis:A=!0,crossAxis:E=!0,fallbackPlacements:C,fallbackStrategy:S="bestFit",fallbackAxisSideDirection:D="none",flipAlignment:k=!0,...H}=s(t,e);if(null!=(n=a.arrow)&&n.alignmentOffset)return{};let F=d(f),P=w(u),W=d(u)===u,M=await (null==g.isRTL?void 0:g.isRTL(L.floating)),V=C||(W||!k?[T(u)]:function(t){let e=T(t);return[y(t),e,y(e)]}(u)),B="none"!==D;!C&&B&&V.push(...function(t,e,n,i){let r=h(t),o=function(t,e,n){switch(t){case"top":case"bottom":if(n)return e?v:x;return e?x:v;case"left":case"right":return e?b:R;default:return[]}}(d(t),"start"===n,i);return r&&(o=o.map(t=>t+"-"+r),e&&(o=o.concat(o.map(y)))),o}(u,k,D,M));let N=[u,...V],_=await O(e,H),z=[],I=(null==(i=a.flip)?void 0:i.overflows)||[];if(A&&z.push(_[F]),E){let t=function(t,e,n){void 0===n&&(n=!1);let i=h(t),r=p(w(t)),o=m(r),l="x"===r?i===(n?"end":"start")?"right":"left":"start"===i?"bottom":"top";return e.reference[o]>e.floating[o]&&(l=T(l)),[l,T(l)]}(f,c,M);z.push(_[t[0]],_[t[1]])}if(I=[...I,{placement:f,overflows:z}],!z.every(t=>t<=0)){let t=((null==(r=a.flip)?void 0:r.index)||0)+1,e=N[t];if(e&&(!("alignment"===E&&P!==w(e))||I.every(t=>t.overflows[0]>0&&w(t.placement)===P)))return{data:{index:t,overflows:I},reset:{placement:e}};let n=null==(o=I.filter(t=>t.overflows[0]<=0).sort((t,e)=>t.overflows[1]-e.overflows[1])[0])?void 0:o.placement;if(!n)switch(S){case"bestFit":{let t=null==(l=I.filter(t=>{if(B){let e=w(t.placement);return e===P||"y"===e}return!0}).map(t=>[t.placement,t.overflows.filter(t=>t>0).reduce((t,e)=>t+e,0)]).sort((t,e)=>t[1]-e[1])[0])?void 0:l[0];t&&(n=t);break}case"initialPlacement":n=u}if(f!==n)return{reset:{placement:n}}}return{}}}},tL=function(t){return void 0===t&&(t={}),{name:"size",options:t,async fn(e){var n,i;let l,f;let{placement:a,rects:c,platform:u,elements:p}=e,{apply:m=()=>{},...g}=s(t,e),y=await O(e,g),x=d(a),v=h(a),b="y"===w(a),{width:R,height:T}=c.floating;"top"===x||"bottom"===x?(l=x,f=v===(await (null==u.isRTL?void 0:u.isRTL(p.floating))?"start":"end")?"left":"right"):(f=x,l="end"===v?"top":"bottom");let L=T-y.top-y.bottom,A=R-y.left-y.right,E=r(T-y[l],L),C=r(R-y[f],A),S=!e.middlewareData.shift,D=E,k=C;if(null!=(n=e.middlewareData.shift)&&n.enabled.x&&(k=A),null!=(i=e.middlewareData.shift)&&i.enabled.y&&(D=L),S&&!v){let t=o(y.left,0),e=o(y.right,0),n=o(y.top,0),i=o(y.bottom,0);b?k=R-2*(0!==t||0!==e?t+e:o(y.left,y.right)):D=T-2*(0!==n||0!==i?n+i:o(y.top,y.bottom))}await m({...e,availableWidth:k,availableHeight:D});let H=await u.getDimensions(p.floating);return R!==H.width||T!==H.height?{reset:{rects:!0}}:{}}}},tA=function(t){return void 0===t&&(t={}),{name:"hide",options:t,async fn(e){let{rects:n}=e,{strategy:i="referenceHidden",...r}=s(t,e);switch(i){case"referenceHidden":{let t=S(await O(e,{...r,elementContext:"reference"}),n.reference);return{data:{referenceHiddenOffsets:t,referenceHidden:D(t)}}}case"escaped":{let t=S(await O(e,{...r,altBoundary:!0}),n.floating);return{data:{escapedOffsets:t,escaped:D(t)}}}default:return{}}}}},tE=t=>({name:"arrow",options:t,async fn(e){let{x:n,y:i,placement:l,rects:f,platform:a,elements:c,middlewareData:u}=e,{element:d,padding:g=0}=s(t,e)||{};if(null==d)return{};let y=L(g),x={x:n,y:i},v=p(w(l)),b=m(v),R=await a.getDimensions(d),T="y"===v,A=T?"clientHeight":"clientWidth",E=f.reference[b]+f.reference[v]-x[v]-f.floating[b],C=x[v]-f.reference[v],O=await (null==a.getOffsetParent?void 0:a.getOffsetParent(d)),S=O?O[A]:0;S&&await (null==a.isElement?void 0:a.isElement(O))||(S=c.floating[A]||f.floating[b]);let D=S/2-R[b]/2-1,k=r(y[T?"top":"left"],D),H=r(y[T?"bottom":"right"],D),F=S-R[b]-H,P=S/2-R[b]/2+(E/2-C/2),W=o(k,r(P,F)),M=!u.arrow&&null!=h(l)&&P!==W&&f.reference[b]/2-(P<k?k:H)-R[b]/2<0,V=M?P<k?P-k:P-F:0;return{[v]:x[v]+V,data:{[v]:W,centerOffset:P-W-V,...M&&{alignmentOffset:V}},reset:M}}}),tC=function(t){return void 0===t&&(t={}),{options:t,fn(e){let{x:n,y:i,placement:r,rects:o,middlewareData:l}=e,{offset:f=0,mainAxis:a=!0,crossAxis:c=!0}=s(t,e),u={x:n,y:i},h=w(r),m=p(h),g=u[m],y=u[h],x=s(f,e),v="number"==typeof x?{mainAxis:x,crossAxis:0}:{mainAxis:0,crossAxis:0,...x};if(a){let t="y"===m?"height":"width",e=o.reference[m]-o.floating[t]+v.mainAxis,n=o.reference[m]+o.reference[t]-v.mainAxis;g<e?g=e:g>n&&(g=n)}if(c){var b,R;let t="y"===m?"width":"height",e=k.has(d(r)),n=o.reference[h]-o.floating[t]+(e&&(null==(b=l.offset)?void 0:b[h])||0)+(e?0:v.crossAxis),i=o.reference[h]+o.reference[t]+(e?0:(null==(R=l.offset)?void 0:R[h])||0)-(e?v.crossAxis:0);y<n?y=n:y>i&&(y=i)}return{[m]:g,[h]:y}}}},tO=(t,e,n)=>{let i=new Map,r={platform:ty,...n},o={...r.platform,_c:i};return C(t,e,{...r,platform:o})}}}]);