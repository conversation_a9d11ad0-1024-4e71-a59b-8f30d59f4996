"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3344],{39547:function(e,n,t){t.d(n,{G9:function(){return I},JZ:function(){return g},M_:function(){return f},N:function(){return l},Nq:function(){return p},TQ:function(){return u},Ur:function(){return s},aE:function(){return c},aK:function(){return w},dA:function(){return d},gt:function(){return D},mb:function(){return b},qB:function(){return P},yu:function(){return m},zX:function(){return y}});var a=t(40256),o=t(61835),i=t(39124),r=t(70623);function s(e){switch(e){case"Our Culture & Workplace":return"Work Policies";case"Protecting Your Income":return"Income Security";default:return e}}function d(e){switch(e){case"On-site Amenities":return"Company Handbook";case"Protecting Your Income":return"Income Security";case"Got married/divorced":return"Marriage or Divorce";case"Had a baby or adopted a baby":return"New Baby or Adoption";case"Lost your insurance":return"Loss of Insurance";default:return e}}async function l(e,n){let t=await (0,a.A_)("/benefits/benefit-types",{companyId:n});return console.log("COMPANY BENEFIT TYPES RESPONSE: ",t.benefitTypes),e((0,o.x7)(t.benefitTypes)),t.benefitTypes}async function c(e,n){let t=await (0,a.A_)("/benefits/all-benefits",{companyId:n});console.log("COMPANY BENEFIT TYPES WITH BENEFITS RESPONSE: ",t),e((0,i.US)(t.benefitsPerType))}async function u(e){let n=await (0,a.A_)("/admin/all-employees");return console.log("COMPANY TEAM MEMBERS RESPONSE: ",n),e((0,o.Vv)(n.employees)),n.employees}async function m(e,n){return console.log("ADDING USERS: ",n),await (0,a.j0)("/admin/add/employees",{employeeList:n})}async function p(e,n,t){try{console.log("\uD83D\uDD0D Debug: User being updated:",n);let e={employeeId:n,updatedDetails:{name:t.name,email:t.email,details:{phoneNumber:t.phoneNumber||"",department:t.department||"",title:t.title||"",role:t.title||""}}};return t.dateOfBirth&&(e.updatedDetails.details.dateOfBirth=t.dateOfBirth),t.hireDate&&(e.updatedDetails.details.hireDate=t.hireDate),t.annualSalary&&(e.updatedDetails.details.annualSalary=t.annualSalary),t.employeeClassType&&(e.updatedDetails.details.employeeClassType=t.employeeClassType),t.workSchedule&&(e.updatedDetails.details.workSchedule=t.workSchedule),t.ssn&&(e.updatedDetails.details.ssn=t.ssn),t.employeeId&&(e.updatedDetails.details.employeeId=t.employeeId),t.workLocation&&(e.updatedDetails.details.workLocation=t.workLocation),t.address&&(e.updatedDetails.details.address=t.address),t.mailingAddress&&(e.updatedDetails.details.mailingAddress=t.mailingAddress),t.emergencyContact&&(e.updatedDetails.details.emergencyContact=t.emergencyContact),e.updatedDetails.details.dependents=t.dependents||[],console.log("Middleware - dependents being sent:",e.updatedDetails.details.dependents),console.log("Sending PUT request with cleaned data:",e),await (0,a.GH)("/admin/update/employee",e)}catch(e){throw console.error("Error in updateUser middleware:",e),e}}async function f(e,n){let t=await (0,a.A_)("/employee",{"user-id":n});return e((0,r.$l)({name:t.currentUser.name,email:t.currentUser.email,companyId:t.currentUser.companyId,role:t.currentUser.role,isAdmin:t.currentUser.isAdmin,isBroker:t.currentUser.isBroker,details:t.currentUser.details})),t}async function y(e,n,t){let o=await (0,a.j0)("/admin/onboard",{company:{name:n.name,adminEmail:n.adminEmail,adminRole:n.adminRole,companySize:n.companySize,industry:n.industry,location:n.location,website:n.website,howHeard:n.howHeard,brokerId:n.brokerId,brokerageId:n.brokerageId,isBrokerage:n.isBrokerage,isActivated:n.isActivated,referralSource:n.referralSource,details:{logo:""}},user:{email:t.email,name:t.name,role:t.role,isAdmin:t.isAdmin,isBroker:t.isBroker,isActivated:t.isActivated}}),i=o.data.userId,r=o.data.companyId;return localStorage.setItem("userid1",i),localStorage.setItem("companyId1",r),o}async function b(e,n){return console.log("SENDING LOGIN LINK TO EMPLOYEE: ",e,n),await (0,a.j0)("/admin/send-user-login-link",{userId:e,companyId:n})}async function I(e,n,t,o){let i=await (0,a.j0)("/admin/add/employer",{brokerId:e,companyName:n,companyAdminEmail:t,companyAdminName:o});return console.log("BROKER ADDS COMPANY RESPONSE: ",i),i}async function g(e,n){return 200===(await (0,a.j0)("/employee/offboard/",{userId:e,companyId:n})).status}async function D(e,n){return await (0,a.j0)("/employee/enable/",{userId:e,companyId:n})}async function P(e,n){try{let n=await (0,a.A_)("/admin/all-companies");console.log("CLIENT COMPANIES UNDER BROKER: ",n);let t=n.companies||[];try{let e=await (0,a.A_)("/employee/company-details");console.log("BROKER'S OWN COMPANY: ",e),e.company&&e.company.isBrokerage&&!t.some(n=>n._id===e.company._id)&&(t.unshift(e.company),console.log("Added broker's own company to the list"))}catch(e){console.log("Could not fetch broker's own company (this is normal if user is not a broker):",e)}return console.log("ALL COMPANIES (CLIENT + OWN): ",t),e((0,r.Ym)(t)),{...n,companies:t}}catch(n){return console.error("Error fetching companies:",n),e((0,r.Ym)([])),{companies:[]}}}async function w(e){let n=await (0,a.A_)("/employee/company-details");return e((0,o.sy)(n.company)),n.status}},83337:function(e,n,t){t.d(n,{C:function(){return i},T:function(){return o}});var a=t(68575);let o=()=>(0,a.I0)(),i=a.v9},39124:function(e,n,t){t.d(n,{D7:function(){return s},F5:function(){return r},H_:function(){return p},MJ:function(){return y},Sn:function(){return g},US:function(){return o},Yb:function(){return c},Yw:function(){return b},d8:function(){return D},iH:function(){return f},nM:function(){return I},oQ:function(){return i},oT:function(){return P}});let a=(0,t(39129).oM)({name:"benefits",initialState:{benefitsPerType:[],documentsPerBenefit:{benefitId:"",documents:[],links:[]},viewableDocuments:[],loadingDocuments:[],snackbarMessage:""},reducers:{setAllBenefitsPerType:(e,n)=>{e.benefitsPerType=n.payload},upsertBenefitsPerType:(e,n)=>{let{benefitType:t,benefits:a}=n.payload,o=e.benefitsPerType.findIndex(e=>e.benefitType===t);-1!==o?e.benefitsPerType[o]={benefitType:t,benefits:a}:e.benefitsPerType.push({benefitType:t,benefits:a})},setDocumentsPerBenefit:(e,n)=>{e.documentsPerBenefit=n.payload},setViewableDocuments:(e,n)=>{let t=n.payload.filter(n=>!e.viewableDocuments.some(e=>e.documentObjectKey===n.documentObjectKey));e.viewableDocuments=[...e.viewableDocuments,...t]},addViewableDocument:(e,n)=>{e.viewableDocuments.push(n.payload)},clearViewableDocuments:e=>{e.viewableDocuments=[]},clearBenefitsState:e=>{e.benefitsPerType=[],e.documentsPerBenefit={benefitId:"",documents:[],links:[]},e.viewableDocuments=[],e.loadingDocuments=[]},setLoadingDocument:(e,n)=>{e.loadingDocuments.push(n.payload)},clearLoadingDocument:(e,n)=>{e.loadingDocuments=e.loadingDocuments.filter(e=>e!==n.payload)},addDocument:(e,n)=>{let{benefitId:t,document:a}=n.payload;e.documentsPerBenefit.benefitId===t&&(e.documentsPerBenefit.documents=[...e.documentsPerBenefit.documents,a])},deleteDocument:(e,n)=>{let{benefitId:t,document:a}=n.payload;e.documentsPerBenefit.benefitId===t&&(e.documentsPerBenefit.documents=e.documentsPerBenefit.documents.filter(e=>e!==a),e.viewableDocuments=e.viewableDocuments.filter(e=>e.documentObjectKey!==a))},addLink:(e,n)=>{let{benefitId:t,link:a}=n.payload;e.documentsPerBenefit.benefitId===t&&(e.documentsPerBenefit.links=[...e.documentsPerBenefit.links,a])},deleteLink:(e,n)=>{let{benefitId:t,link:a}=n.payload;console.log("DELETE LINK REDUCER: ",e.documentsPerBenefit.benefitId),e.documentsPerBenefit.benefitId===t&&(e.documentsPerBenefit.links=e.documentsPerBenefit.links.filter(e=>e!==a))},setSnackbarMessage:(e,n)=>{e.snackbarMessage=n.payload},clearSnackbarMessage:e=>{e.snackbarMessage=""}}}),{setAllBenefitsPerType:o,upsertBenefitsPerType:i,setDocumentsPerBenefit:r,setViewableDocuments:s,addViewableDocument:d,clearViewableDocuments:l,clearBenefitsState:c,setLoadingDocument:u,clearLoadingDocument:m,addDocument:p,deleteDocument:f,addLink:y,deleteLink:b,setSnackbarMessage:I,clearSnackbarMessage:g}=a.actions,D=(e,n)=>{for(let{benefitType:t,benefits:a}of e.benefits.benefitsPerType){let e=a.find(e=>e._id===n);if(e)return{benefitType:t,benefit:e}}return null},P=(e,n)=>e.benefits.benefitsPerType.find(e=>e.benefitType===n)||null;n.ZP=a.reducer},61835:function(e,n,t){t.d(n,{Vv:function(){return i},sy:function(){return r},x7:function(){return o}});let a=(0,t(39129).oM)({name:"company",initialState:{companyBenefitTypes:[],companyTeamMembers:[],companyDetails:{_id:"",name:"",companySize:0,industry:"",location:"",website:"",adminEmail:"",adminRole:"",brokerId:"",brokerageId:"",isBrokerage:!1,isActivated:!1,howHeard:"",details:{logo:""},__v:0}},reducers:{setCompanyBenefitTypes:(e,n)=>{e.companyBenefitTypes=n.payload},setCompanyTeamMembers:(e,n)=>{e.companyTeamMembers=n.payload},setCompanyDetails:(e,n)=>{console.log("COMPANY DETAILS PAYLOAD: ",n.payload),e.companyDetails=n.payload}}}),{setCompanyBenefitTypes:o,setCompanyTeamMembers:i,setCompanyDetails:r}=a.actions;n.ZP=a.reducer},70623:function(e,n,t){t.d(n,{$l:function(){return i},CS:function(){return d},Iv:function(){return o},MP:function(){return u},Re:function(){return s},Ym:function(){return c},ki:function(){return l},v2:function(){return r}});let a=(0,t(39129).oM)({name:"user",initialState:{_id:"",userProfile:{name:"",email:"",companyId:"",role:"",isAdmin:!1,isBroker:!1,details:{phoneNumber:void 0,department:void 0,title:void 0,role:void 0,dateOfBirth:void 0,hireDate:void 0,annualSalary:void 0,employeeClassType:"",customPayrollFrequency:"",ssn:"",address:void 0,mailingAddress:void 0,dependents:[],emergencyContact:void 0,employeeId:"",managerId:"",workLocation:"",workSchedule:"",ein:""}},selectedBenefitType:"",selectedBenefitId:"",selectedFAQQuestion:"",managedCompanies:[]},reducers:{setUserId:(e,n)=>{e._id=n.payload},setUserProfile:(e,n)=>{let{name:t,email:a,companyId:o,role:i,isAdmin:r,isBroker:s,details:d}=n.payload;e.userProfile={name:t,email:a,companyId:o,role:i,isAdmin:r,isBroker:s,details:d||e.userProfile.details}},setSelectedBenefitType:(e,n)=>{console.log("action.payload",n.payload),e.selectedBenefitType=n.payload},setSelectedBenefitId:(e,n)=>{e.selectedBenefitId=n.payload},setSelectedFAQQuestion:(e,n)=>{e.selectedFAQQuestion=n.payload},clearSelectedFAQQuestion:e=>{e.selectedFAQQuestion=""},setManagedCompanies:(e,n)=>{e.managedCompanies=n.payload}}}),{setUserId:o,setUserProfile:i,setSelectedBenefitType:r,setSelectedBenefitId:s,setSelectedFAQQuestion:d,clearSelectedFAQQuestion:l,setManagedCompanies:c}=a.actions,u=e=>e.user.userProfile.companyId;n.ZP=a.reducer}}]);