"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4825],{40256:function(e,t,n){n.d(t,{$R:function(){return d},A_:function(){return c},BO:function(){return i},GH:function(){return f},_n:function(){return r},be:function(){return a},iG:function(){return s},j0:function(){return l}});var o=n(83464);let r="http://localhost:8080",i="<EMAIL>,<EMAIL>,<EMAIL>".split(",").map(e=>e.trim()),a=o.Z.create({baseURL:r});async function c(e,t,n){let o=new URL(n?"".concat(n).concat(e):"".concat(r).concat(e));return t&&Object.keys(t).forEach(e=>o.searchParams.append(e,t[e])),(await a.get(o.toString())).data}async function l(e,t,n){let o=n?"".concat(n).concat(e):"".concat(r).concat(e),i=await a.post(o,t,{headers:{"Content-Type":"application/json"}});return{status:i.status,data:i.data}}async function s(e,t,n){let o=n?"".concat(n).concat(e):"".concat(r).concat(e);console.log("Document upload to: ".concat(o));let i=await a.post(o,t,{headers:{"Content-Type":"multipart/form-data"}});return{status:i.status,data:i.data}}async function d(e,t,n){let o=new URL(n?"".concat(n).concat(e):"".concat(r).concat(e));return t&&Object.keys(t).forEach(e=>o.searchParams.append(e,t[e])),console.log("GET Blob request to: ".concat(o.toString())),(await a.get(o.toString(),{responseType:"blob"})).data}async function f(e,t,n){let o=n?"".concat(n).concat(e):"".concat(r).concat(e),i=await a.put(o,t,{headers:{"Content-Type":"application/json"}});return{status:i.status,data:i.data}}a.interceptors.request.use(e=>{let t=localStorage.getItem("userid1")||localStorage.getItem("userId");return t?e.headers["user-id"]=t:console.warn("No user ID found in localStorage for API request"),e})},14702:function(e,t,n){var o=n(57437),r=n(95656),i=n(53410),a=n(46387),c=n(59832),l=n(35389),s=n(89414),d=n(96729),f=n(83337),u=n(2265),p=n(54862),x=n(7022),g=n(33145);t.Z=()=>{var e;let t=(0,f.T)(),n=(0,f.C)(e=>e.company.companyDetails),h=(0,u.useRef)(null),[m,b]=(0,u.useState)(!1),y=()=>{h.current&&h.current.click()};return(0,o.jsx)(r.Z,{sx:{display:"flex",flexDirection:"column",alignItems:"flex-start",mt:3},children:(0,o.jsxs)(i.Z,{sx:{display:"flex",flexDirection:"column",alignItems:"flex-start",justifyContent:"space-between",width:"100%",boxShadow:"none",borderRadius:"30px",bgcolor:"#ffffff",paddingBottom:"12px",paddingX:"12px"},children:[(0,o.jsxs)(r.Z,{sx:{display:"flex",alignItems:"center",justifyContent:"space-between",width:"100%",mb:2,paddingTop:"25px",paddingX:"12px"},children:[(0,o.jsxs)(r.Z,{children:[(0,o.jsx)(a.Z,{variant:"h5",sx:{fontWeight:"bold",mb:.5,fontSize:"24px"},children:n.name||"N/A"}),(0,o.jsx)(a.Z,{variant:"body2",sx:{color:"#6c757d",fontSize:"16px"},children:n.industry||"N/A"})]}),(0,o.jsxs)(r.Z,{sx:{position:"relative",ml:2},children:[(0,o.jsx)(r.Z,{onClick:y,sx:{width:100,height:100,borderRadius:"12px",overflow:"hidden",position:"relative",display:"flex",alignItems:"center",justifyContent:"center",bgcolor:"#f4f4f4",cursor:"pointer"},children:(null===(e=n.details)||void 0===e?void 0:e.logo)?(0,o.jsx)(g.default,{src:n.details.logo,alt:"Company Logo",layout:"fill",objectFit:"contain"}):(0,o.jsxs)(r.Z,{sx:{display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",height:"100%",color:"#9e9e9e"},children:[(0,o.jsx)(d.Z,{fontSize:"large"}),(0,o.jsx)(a.Z,{variant:"caption",sx:{mb:.5,fontWeight:600},children:"Logo"})]})}),(0,o.jsx)(c.Z,{onClick:y,sx:{position:"absolute",top:0,right:0,bgcolor:"#000000",color:"#ffffff",borderRadius:"50%",width:"24px",height:"24px",p:"2px"},children:m?(0,o.jsx)(l.Z,{size:16}):(0,o.jsx)(p.Z,{sx:{fontSize:"16px"}})})]})]}),(0,o.jsx)(i.Z,{sx:{borderRadius:"30px",p:2.5,boxShadow:"none",bgcolor:"rgba(245, 245, 245, 0.7)",width:"100%",mt:2},children:(0,o.jsxs)(s.ZP,{container:!0,spacing:2,children:[(0,o.jsx)(s.ZP,{item:!0,xs:6,children:(0,o.jsx)(a.Z,{variant:"body2",sx:{color:"#6c757d",fontSize:"14px",textAlign:"left"},children:"Company Size"})}),(0,o.jsx)(s.ZP,{item:!0,xs:6,children:(0,o.jsx)(a.Z,{variant:"body2",sx:{fontSize:"14px",textAlign:"right",fontWeight:600,wordBreak:"break-word",overflowWrap:"break-word"},children:n.companySize||"2"})}),(0,o.jsx)(s.ZP,{item:!0,xs:6,children:(0,o.jsx)(a.Z,{variant:"body2",sx:{color:"#6c757d",fontSize:"14px",textAlign:"left",wordBreak:"break-word",overflowWrap:"break-word"},children:"Website"})}),(0,o.jsx)(s.ZP,{item:!0,xs:6,children:(0,o.jsx)(a.Z,{variant:"body2",sx:{fontSize:"14px",textAlign:"right",fontWeight:600,wordBreak:"break-word",overflowWrap:"break-word"},children:n.website?(0,o.jsx)("a",{href:"http://".concat(n.website),target:"_blank",rel:"noopener noreferrer",style:{textDecoration:"none",color:"#000"},children:n.website}):"BenOsphere.com"})}),(0,o.jsx)(s.ZP,{item:!0,xs:6,children:(0,o.jsx)(a.Z,{variant:"body2",sx:{color:"#6c757d",fontSize:"14px",textAlign:"left",wordBreak:"break-word",overflowWrap:"break-word"},children:"Admin Email"})}),(0,o.jsx)(s.ZP,{item:!0,xs:6,children:(0,o.jsx)(a.Z,{variant:"body2",sx:{fontSize:"14px",textAlign:"right",fontWeight:600,wordBreak:"break-word",overflowWrap:"break-word"},children:n.adminEmail||"<EMAIL>"})})]})}),(0,o.jsx)("input",{type:"file",accept:".png, .jpg",ref:h,style:{display:"none"},onChange:e=>{var n;let o=null===(n=e.target.files)||void 0===n?void 0:n[0];o&&(b(!0),(0,x.mH)(t,o).finally(()=>{b(!1)}))}})]})})}},42374:function(e,t,n){var o=n(57437),r=n(95656),i=n(67116),a=n(46387),c=n(59832),l=n(53410),s=n(89414),d=n(83337),f=n(2265),u=n(54862),p=n(98005);t.Z=()=>{var e,t;let n=(0,d.C)(e=>e.user.userProfile),[x,g]=(0,f.useState)(!1);return(0,o.jsxs)(r.Z,{sx:{display:"flex",flexDirection:"column",alignItems:"center",bgcolor:"#ffffff",paddingTop:7,paddingBottom:"12px",paddingX:"12px",borderRadius:"30px",width:"100%",position:"relative"},children:[(0,o.jsx)(r.Z,{sx:{position:"absolute",top:-60,display:"flex",justifyContent:"center",width:"100%"},children:(0,o.jsx)(i.Z,{sx:{backgroundImage:"linear-gradient(180deg, #4BD1E9 0%, #1274CF 100%)",color:"#ffffff",width:130,height:130,border:"10px solid #ffffff",fontSize:"48px",fontWeight:800},children:(e=>{console.log("Getting initials for name: ",JSON.stringify(n));let[t,o]=e.split(" ");return"".concat(t[0].toUpperCase()).concat(o?o[0].toUpperCase():"")})(n.name)})}),(0,o.jsxs)(r.Z,{sx:{display:"flex",alignItems:"center",mt:4,mb:4,position:"relative"},children:[(0,o.jsx)(a.Z,{variant:"h5",sx:{fontWeight:"bold",fontSize:"32px",flexGrow:1},children:n.name.replace(/\b\w/g,e=>e.toUpperCase())||"N/A"}),(0,o.jsx)(c.Z,{onClick:()=>{g(!0)},sx:{bgcolor:"#000000",color:"#ffffff",borderRadius:"50%",width:"24px",height:"24px",p:"2px",ml:2},children:(0,o.jsx)(u.Z,{sx:{fontSize:"16px"}})})]}),(0,o.jsx)(l.Z,{sx:{borderRadius:"30px",p:2.5,boxShadow:"none",bgcolor:"rgba(245, 245, 245, 0.7)",width:"100%"},children:(0,o.jsxs)(s.ZP,{container:!0,spacing:2,children:[(0,o.jsx)(s.ZP,{item:!0,xs:6,children:(0,o.jsx)(a.Z,{sx:{color:"#6c757d",fontSize:"14px",textAlign:"left"},children:"Title"})}),(0,o.jsx)(s.ZP,{item:!0,xs:6,children:(0,o.jsx)(a.Z,{sx:{fontSize:"14px",textAlign:"right",fontWeight:600},children:(null===(e=n.details)||void 0===e?void 0:e.title)||"N/A"})}),(0,o.jsx)(s.ZP,{item:!0,xs:6,children:(0,o.jsx)(a.Z,{variant:"body2",sx:{color:"#6c757d",fontSize:"14px",textAlign:"left"},children:"Department"})}),(0,o.jsx)(s.ZP,{item:!0,xs:6,children:(0,o.jsx)(a.Z,{variant:"body2",sx:{fontSize:"14px",textAlign:"right",fontWeight:600},children:(null===(t=n.details)||void 0===t?void 0:t.department)||"N/A"})}),(0,o.jsx)(s.ZP,{item:!0,xs:6,children:(0,o.jsx)(a.Z,{variant:"body2",sx:{color:"#6c757d",fontSize:"14px",textAlign:"left"},children:"Employment Type"})}),(0,o.jsx)(s.ZP,{item:!0,xs:6,children:(0,o.jsx)(a.Z,{variant:"body2",sx:{fontSize:"14px",textAlign:"right",fontWeight:600},children:"Full Time"})})]})}),(0,o.jsx)(p.Z,{open:x,onClose:()=>{g(!1)}})]})}},7022:function(e,t,n){n.d(t,{$t:function(){return d},SS:function(){return u},Y0:function(){return a},cd:function(){return f},fH:function(){return p},mH:function(){return x},ov:function(){return s},v0:function(){return c}});var o=n(40256),r=n(39124),i=n(39547);async function a(e,t,n){try{let i=await (0,o.A_)("/benefits/benefit-by-type",{companyId:t,type:n});i&&i.benefits?(console.log("GET BENEFITS FOR TYPE RESPONSE: ",i.benefits),e((0,r.oQ)({benefitType:n,benefits:i.benefits})),e((0,r.nM)("Benefits fetched successfully"))):(console.error("Invalid response format:",i),e((0,r.nM)("Failed to fetch benefits")))}catch(t){console.error("Error fetching benefits:",t),e((0,r.nM)("Error fetching benefits"))}}async function c(e,t,n,i){let a={benefitId:t,page:i};console.log("data",a);let c=await (0,o.A_)("/benefits/one-benefit",a),s={...c,benefitId:t};for(let t of(e((0,r.F5)(s)),c.documents)){let o=decodeURIComponent(t.split("_____")[1]);l(e,t,n,o)}}async function l(e,t,n,i){let a={objectKey:t,companyId:n};console.log("data",a);let c=await (0,o.$R)("/benefits/document",a);if(console.log("VIEW BENEFIT RESPONSE: ",c),c){let n=new Blob([c],{type:"application/pdf"}),o=URL.createObjectURL(n);e((0,r.D7)([{documentObjectKey:t,document:o,originalFileName:i}]))}}let s=async(e,t,n,r,c)=>200===(await (0,o.j0)("/benefits/toggle-benefits/",{benefitId:n,companyId:t,isActivated:r})).status&&(await a(e,t,c),await (0,i.N)(e,t),!0);async function d(e,t,n,i){let a=new FormData;i.forEach(e=>a.append("documents",e)),a.append("companyId",n),a.append("benefitId",t);try{console.log("uploadDocument",a);let c=await (0,o.iG)("/benefits/add/document",a),s=c.data.objectKeys;if(console.log("newObjectKeys",s),200===c.status)return s.forEach((o,a)=>{let c=i[a].name;e((0,r.H_)({benefitId:t,document:o})),l(e,o,n,c)}),e((0,r.nM)("Document added successfully")),!0;return console.error("Error adding document:",c.data.error),e((0,r.nM)("Failed to add document")),!1}catch(t){return console.error("Error adding document:",t),e((0,r.nM)("Error adding document")),!1}}async function f(e,t,n,i){try{let a=await (0,o.j0)("/benefits/delete/document",{benefitId:t,companyId:n,objectKey:i});if(200===a.status)return e((0,r.iH)({benefitId:t,document:i})),e((0,r.nM)("Document deleted successfully")),!0;return console.error("Error deleting document:",a.data.error),e((0,r.nM)("Failed to delete document")),!1}catch(t){return console.error("Error deleting document:",t),e((0,r.nM)("Error deleting document")),!1}}async function u(e,t,n,i){try{let a=await (0,o.j0)("/benefits/add/links",{benefitId:t,companyId:n,urls:[i]});if(200===a.status)return e((0,r.MJ)({benefitId:t,link:i})),e((0,r.nM)("Link added successfully")),!0;return console.error("Error adding link:",a.data.error),e((0,r.nM)("Failed to add link")),!1}catch(t){return console.error("Error adding link:",t),e((0,r.nM)("Error adding link")),!1}}async function p(e,t,n,i){try{let a=await (0,o.j0)("/benefits/delete/link",{benefitId:t,companyId:n,urls:i});if(200===a.status)return e((0,r.Yw)({benefitId:t,link:i})),e((0,r.nM)("Link deleted successfully")),!0;return console.error("Error deleting link:",a.data.error),e((0,r.nM)("Failed to delete link")),!1}catch(t){return console.error("Error deleting link:",t),e((0,r.nM)("Error deleting link")),!1}}async function x(e,t){let n=new FormData;n.append("logoImage",t);try{console.log("uploading company logo",n);let t=await (0,o.iG)("/admin/update-company-logo",n);if(await (0,i.aK)(e),200===t.status)return console.log("Company logo updated successfully"),e((0,r.nM)("Company logo updated successfully")),!0;return console.error("Error updating company logo:",t.data.error),e((0,r.nM)("Failed to update company logo")),!1}catch(t){return console.error("Error updating company logo:",t),e((0,r.nM)("Error updating company logo")),!1}}}}]);