"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4902],{24902:function(e,t,o){o.d(t,{Z:function(){return e1}});var r,n,i,a,p,s=o(2265),l=o(61994),c=o(56962),f=o(20801),u=o(65208),d=o(77126),m=o(62919),h=o(30628),v=o(16210),g=o(31691),y=o(21086),b=o(37053),w=o(85657),x=o(78826),O=o(23947),R=o(3450),E=o(72786);function T(e){if(null==e)return window;if("[object Window]"!==e.toString()){var t=e.ownerDocument;return t&&t.defaultView||window}return e}function j(e){var t=T(e).Element;return e instanceof t||e instanceof Element}function P(e){var t=T(e).HTMLElement;return e instanceof t||e instanceof HTMLElement}function M(e){if("undefined"==typeof ShadowRoot)return!1;var t=T(e).ShadowRoot;return e instanceof t||e instanceof ShadowRoot}var Z=Math.max,A=Math.min,k=Math.round;function L(){var e=navigator.userAgentData;return null!=e&&e.brands&&Array.isArray(e.brands)?e.brands.map(function(e){return e.brand+"/"+e.version}).join(" "):navigator.userAgent}function S(){return!/^((?!chrome|android).)*safari/i.test(L())}function W(e,t,o){void 0===t&&(t=!1),void 0===o&&(o=!1);var r=e.getBoundingClientRect(),n=1,i=1;t&&P(e)&&(n=e.offsetWidth>0&&k(r.width)/e.offsetWidth||1,i=e.offsetHeight>0&&k(r.height)/e.offsetHeight||1);var a=(j(e)?T(e):window).visualViewport,p=!S()&&o,s=(r.left+(p&&a?a.offsetLeft:0))/n,l=(r.top+(p&&a?a.offsetTop:0))/i,c=r.width/n,f=r.height/i;return{width:c,height:f,top:l,right:s+c,bottom:l+f,left:s,x:s,y:l}}function D(e){var t=T(e);return{scrollLeft:t.pageXOffset,scrollTop:t.pageYOffset}}function B(e){return e?(e.nodeName||"").toLowerCase():null}function C(e){return((j(e)?e.ownerDocument:e.document)||window.document).documentElement}function H(e){return W(C(e)).left+D(e).scrollLeft}function N(e){return T(e).getComputedStyle(e)}function I(e){var t=N(e),o=t.overflow,r=t.overflowX,n=t.overflowY;return/auto|scroll|overlay|hidden/.test(o+n+r)}function F(e){var t=W(e),o=e.offsetWidth,r=e.offsetHeight;return 1>=Math.abs(t.width-o)&&(o=t.width),1>=Math.abs(t.height-r)&&(r=t.height),{x:e.offsetLeft,y:e.offsetTop,width:o,height:r}}function V(e){return"html"===B(e)?e:e.assignedSlot||e.parentNode||(M(e)?e.host:null)||C(e)}function q(e,t){void 0===t&&(t=[]);var o,r=function e(t){return["html","body","#document"].indexOf(B(t))>=0?t.ownerDocument.body:P(t)&&I(t)?t:e(V(t))}(e),n=r===(null==(o=e.ownerDocument)?void 0:o.body),i=T(r),a=n?[i].concat(i.visualViewport||[],I(r)?r:[]):r,p=t.concat(a);return n?p:p.concat(q(V(a)))}function U(e){return P(e)&&"fixed"!==N(e).position?e.offsetParent:null}function z(e){for(var t=T(e),o=U(e);o&&["table","td","th"].indexOf(B(o))>=0&&"static"===N(o).position;)o=U(o);return o&&("html"===B(o)||"body"===B(o)&&"static"===N(o).position)?t:o||function(e){var t=/firefox/i.test(L());if(/Trident/i.test(L())&&P(e)&&"fixed"===N(e).position)return null;var o=V(e);for(M(o)&&(o=o.host);P(o)&&0>["html","body"].indexOf(B(o));){var r=N(o);if("none"!==r.transform||"none"!==r.perspective||"paint"===r.contain||-1!==["transform","perspective"].indexOf(r.willChange)||t&&"filter"===r.willChange||t&&r.filter&&"none"!==r.filter)return o;o=o.parentNode}return null}(e)||t}var _="bottom",X="right",Y="left",G="auto",J=["top",_,X,Y],K="start",Q="viewport",$="popper",ee=J.reduce(function(e,t){return e.concat([t+"-"+K,t+"-end"])},[]),et=[].concat(J,[G]).reduce(function(e,t){return e.concat([t,t+"-"+K,t+"-end"])},[]),eo=["beforeRead","read","afterRead","beforeMain","main","afterMain","beforeWrite","write","afterWrite"],er={placement:"bottom",modifiers:[],strategy:"absolute"};function en(){for(var e=arguments.length,t=Array(e),o=0;o<e;o++)t[o]=arguments[o];return!t.some(function(e){return!(e&&"function"==typeof e.getBoundingClientRect)})}var ei={passive:!0};function ea(e){return e.split("-")[0]}function ep(e){return e.split("-")[1]}function es(e){return["top","bottom"].indexOf(e)>=0?"x":"y"}function el(e){var t,o=e.reference,r=e.element,n=e.placement,i=n?ea(n):null,a=n?ep(n):null,p=o.x+o.width/2-r.width/2,s=o.y+o.height/2-r.height/2;switch(i){case"top":t={x:p,y:o.y-r.height};break;case _:t={x:p,y:o.y+o.height};break;case X:t={x:o.x+o.width,y:s};break;case Y:t={x:o.x-r.width,y:s};break;default:t={x:o.x,y:o.y}}var l=i?es(i):null;if(null!=l){var c="y"===l?"height":"width";switch(a){case K:t[l]=t[l]-(o[c]/2-r[c]/2);break;case"end":t[l]=t[l]+(o[c]/2-r[c]/2)}}return t}var ec={top:"auto",right:"auto",bottom:"auto",left:"auto"};function ef(e){var t,o,r,n,i,a,p,s=e.popper,l=e.popperRect,c=e.placement,f=e.variation,u=e.offsets,d=e.position,m=e.gpuAcceleration,h=e.adaptive,v=e.roundOffsets,g=e.isFixed,y=u.x,b=void 0===y?0:y,w=u.y,x=void 0===w?0:w,O="function"==typeof v?v({x:b,y:x}):{x:b,y:x};b=O.x,x=O.y;var R=u.hasOwnProperty("x"),E=u.hasOwnProperty("y"),j=Y,P="top",M=window;if(h){var Z=z(s),A="clientHeight",L="clientWidth";Z===T(s)&&"static"!==N(Z=C(s)).position&&"absolute"===d&&(A="scrollHeight",L="scrollWidth"),("top"===c||(c===Y||c===X)&&"end"===f)&&(P=_,x-=(g&&Z===M&&M.visualViewport?M.visualViewport.height:Z[A])-l.height,x*=m?1:-1),(c===Y||("top"===c||c===_)&&"end"===f)&&(j=X,b-=(g&&Z===M&&M.visualViewport?M.visualViewport.width:Z[L])-l.width,b*=m?1:-1)}var S=Object.assign({position:d},h&&ec),W=!0===v?(t={x:b,y:x},o=T(s),r=t.x,n=t.y,{x:k(r*(i=o.devicePixelRatio||1))/i||0,y:k(n*i)/i||0}):{x:b,y:x};return(b=W.x,x=W.y,m)?Object.assign({},S,((p={})[P]=E?"0":"",p[j]=R?"0":"",p.transform=1>=(M.devicePixelRatio||1)?"translate("+b+"px, "+x+"px)":"translate3d("+b+"px, "+x+"px, 0)",p)):Object.assign({},S,((a={})[P]=E?x+"px":"",a[j]=R?b+"px":"",a.transform="",a))}var eu={left:"right",right:"left",bottom:"top",top:"bottom"};function ed(e){return e.replace(/left|right|bottom|top/g,function(e){return eu[e]})}var em={start:"end",end:"start"};function eh(e){return e.replace(/start|end/g,function(e){return em[e]})}function ev(e,t){var o=t.getRootNode&&t.getRootNode();if(e.contains(t))return!0;if(o&&M(o)){var r=t;do{if(r&&e.isSameNode(r))return!0;r=r.parentNode||r.host}while(r)}return!1}function eg(e){return Object.assign({},e,{left:e.x,top:e.y,right:e.x+e.width,bottom:e.y+e.height})}function ey(e,t,o){var r,n,i,a,p,s,l,c,f,u;return t===Q?eg(function(e,t){var o=T(e),r=C(e),n=o.visualViewport,i=r.clientWidth,a=r.clientHeight,p=0,s=0;if(n){i=n.width,a=n.height;var l=S();(l||!l&&"fixed"===t)&&(p=n.offsetLeft,s=n.offsetTop)}return{width:i,height:a,x:p+H(e),y:s}}(e,o)):j(t)?((r=W(t,!1,"fixed"===o)).top=r.top+t.clientTop,r.left=r.left+t.clientLeft,r.bottom=r.top+t.clientHeight,r.right=r.left+t.clientWidth,r.width=t.clientWidth,r.height=t.clientHeight,r.x=r.left,r.y=r.top,r):eg((n=C(e),a=C(n),p=D(n),s=null==(i=n.ownerDocument)?void 0:i.body,l=Z(a.scrollWidth,a.clientWidth,s?s.scrollWidth:0,s?s.clientWidth:0),c=Z(a.scrollHeight,a.clientHeight,s?s.scrollHeight:0,s?s.clientHeight:0),f=-p.scrollLeft+H(n),u=-p.scrollTop,"rtl"===N(s||a).direction&&(f+=Z(a.clientWidth,s?s.clientWidth:0)-l),{width:l,height:c,x:f,y:u}))}function eb(){return{top:0,right:0,bottom:0,left:0}}function ew(e){return Object.assign({},eb(),e)}function ex(e,t){return t.reduce(function(t,o){return t[o]=e,t},{})}function eO(e,t){void 0===t&&(t={});var o,r,n,i,a,p,s,l,c=t,f=c.placement,u=void 0===f?e.placement:f,d=c.strategy,m=void 0===d?e.strategy:d,h=c.boundary,v=c.rootBoundary,g=c.elementContext,y=void 0===g?$:g,b=c.altBoundary,w=c.padding,x=void 0===w?0:w,O=ew("number"!=typeof x?x:ex(x,J)),R=e.rects.popper,E=e.elements[void 0!==b&&b?y===$?"reference":$:y],T=(o=j(E)?E:E.contextElement||C(e.elements.popper),r=void 0===h?"clippingParents":h,n=void 0===v?Q:v,s=(p=[].concat("clippingParents"===r?(i=q(V(o)),j(a=["absolute","fixed"].indexOf(N(o).position)>=0&&P(o)?z(o):o)?i.filter(function(e){return j(e)&&ev(e,a)&&"body"!==B(e)}):[]):[].concat(r),[n]))[0],(l=p.reduce(function(e,t){var r=ey(o,t,m);return e.top=Z(r.top,e.top),e.right=A(r.right,e.right),e.bottom=A(r.bottom,e.bottom),e.left=Z(r.left,e.left),e},ey(o,s,m))).width=l.right-l.left,l.height=l.bottom-l.top,l.x=l.left,l.y=l.top,l),M=W(e.elements.reference),k=el({reference:M,element:R,strategy:"absolute",placement:u}),L=eg(Object.assign({},R,k)),S=y===$?L:M,D={top:T.top-S.top+O.top,bottom:S.bottom-T.bottom+O.bottom,left:T.left-S.left+O.left,right:S.right-T.right+O.right},H=e.modifiersData.offset;if(y===$&&H){var I=H[u];Object.keys(D).forEach(function(e){var t=[X,_].indexOf(e)>=0?1:-1,o=["top",_].indexOf(e)>=0?"y":"x";D[e]+=I[o]*t})}return D}function eR(e,t,o){return Z(e,A(t,o))}function eE(e,t,o){return void 0===o&&(o={x:0,y:0}),{top:e.top-t.height-o.y,right:e.right-t.width+o.x,bottom:e.bottom-t.height+o.y,left:e.left-t.width-o.x}}function eT(e){return["top",X,_,Y].some(function(t){return e[t]>=0})}var ej=(i=void 0===(n=(r={defaultModifiers:[{name:"eventListeners",enabled:!0,phase:"write",fn:function(){},effect:function(e){var t=e.state,o=e.instance,r=e.options,n=r.scroll,i=void 0===n||n,a=r.resize,p=void 0===a||a,s=T(t.elements.popper),l=[].concat(t.scrollParents.reference,t.scrollParents.popper);return i&&l.forEach(function(e){e.addEventListener("scroll",o.update,ei)}),p&&s.addEventListener("resize",o.update,ei),function(){i&&l.forEach(function(e){e.removeEventListener("scroll",o.update,ei)}),p&&s.removeEventListener("resize",o.update,ei)}},data:{}},{name:"popperOffsets",enabled:!0,phase:"read",fn:function(e){var t=e.state,o=e.name;t.modifiersData[o]=el({reference:t.rects.reference,element:t.rects.popper,strategy:"absolute",placement:t.placement})},data:{}},{name:"computeStyles",enabled:!0,phase:"beforeWrite",fn:function(e){var t=e.state,o=e.options,r=o.gpuAcceleration,n=o.adaptive,i=o.roundOffsets,a=void 0===i||i,p={placement:ea(t.placement),variation:ep(t.placement),popper:t.elements.popper,popperRect:t.rects.popper,gpuAcceleration:void 0===r||r,isFixed:"fixed"===t.options.strategy};null!=t.modifiersData.popperOffsets&&(t.styles.popper=Object.assign({},t.styles.popper,ef(Object.assign({},p,{offsets:t.modifiersData.popperOffsets,position:t.options.strategy,adaptive:void 0===n||n,roundOffsets:a})))),null!=t.modifiersData.arrow&&(t.styles.arrow=Object.assign({},t.styles.arrow,ef(Object.assign({},p,{offsets:t.modifiersData.arrow,position:"absolute",adaptive:!1,roundOffsets:a})))),t.attributes.popper=Object.assign({},t.attributes.popper,{"data-popper-placement":t.placement})},data:{}},{name:"applyStyles",enabled:!0,phase:"write",fn:function(e){var t=e.state;Object.keys(t.elements).forEach(function(e){var o=t.styles[e]||{},r=t.attributes[e]||{},n=t.elements[e];P(n)&&B(n)&&(Object.assign(n.style,o),Object.keys(r).forEach(function(e){var t=r[e];!1===t?n.removeAttribute(e):n.setAttribute(e,!0===t?"":t)}))})},effect:function(e){var t=e.state,o={popper:{position:t.options.strategy,left:"0",top:"0",margin:"0"},arrow:{position:"absolute"},reference:{}};return Object.assign(t.elements.popper.style,o.popper),t.styles=o,t.elements.arrow&&Object.assign(t.elements.arrow.style,o.arrow),function(){Object.keys(t.elements).forEach(function(e){var r=t.elements[e],n=t.attributes[e]||{},i=Object.keys(t.styles.hasOwnProperty(e)?t.styles[e]:o[e]).reduce(function(e,t){return e[t]="",e},{});P(r)&&B(r)&&(Object.assign(r.style,i),Object.keys(n).forEach(function(e){r.removeAttribute(e)}))})}},requires:["computeStyles"]},{name:"offset",enabled:!0,phase:"main",requires:["popperOffsets"],fn:function(e){var t=e.state,o=e.options,r=e.name,n=o.offset,i=void 0===n?[0,0]:n,a=et.reduce(function(e,o){var r,n,a,p,s,l;return e[o]=(r=t.rects,a=[Y,"top"].indexOf(n=ea(o))>=0?-1:1,s=(p="function"==typeof i?i(Object.assign({},r,{placement:o})):i)[0],l=p[1],s=s||0,l=(l||0)*a,[Y,X].indexOf(n)>=0?{x:l,y:s}:{x:s,y:l}),e},{}),p=a[t.placement],s=p.x,l=p.y;null!=t.modifiersData.popperOffsets&&(t.modifiersData.popperOffsets.x+=s,t.modifiersData.popperOffsets.y+=l),t.modifiersData[r]=a}},{name:"flip",enabled:!0,phase:"main",fn:function(e){var t=e.state,o=e.options,r=e.name;if(!t.modifiersData[r]._skip){for(var n=o.mainAxis,i=void 0===n||n,a=o.altAxis,p=void 0===a||a,s=o.fallbackPlacements,l=o.padding,c=o.boundary,f=o.rootBoundary,u=o.altBoundary,d=o.flipVariations,m=void 0===d||d,h=o.allowedAutoPlacements,v=t.options.placement,g=ea(v)===v,y=s||(g||!m?[ed(v)]:function(e){if(ea(e)===G)return[];var t=ed(e);return[eh(e),t,eh(t)]}(v)),b=[v].concat(y).reduce(function(e,o){var r,n,i,a,p,s,u,d,v,g,y,b;return e.concat(ea(o)===G?(n=(r={placement:o,boundary:c,rootBoundary:f,padding:l,flipVariations:m,allowedAutoPlacements:h}).placement,i=r.boundary,a=r.rootBoundary,p=r.padding,s=r.flipVariations,d=void 0===(u=r.allowedAutoPlacements)?et:u,0===(y=(g=(v=ep(n))?s?ee:ee.filter(function(e){return ep(e)===v}):J).filter(function(e){return d.indexOf(e)>=0})).length&&(y=g),Object.keys(b=y.reduce(function(e,o){return e[o]=eO(t,{placement:o,boundary:i,rootBoundary:a,padding:p})[ea(o)],e},{})).sort(function(e,t){return b[e]-b[t]})):o)},[]),w=t.rects.reference,x=t.rects.popper,O=new Map,R=!0,E=b[0],T=0;T<b.length;T++){var j=b[T],P=ea(j),M=ep(j)===K,Z=["top",_].indexOf(P)>=0,A=Z?"width":"height",k=eO(t,{placement:j,boundary:c,rootBoundary:f,altBoundary:u,padding:l}),L=Z?M?X:Y:M?_:"top";w[A]>x[A]&&(L=ed(L));var S=ed(L),W=[];if(i&&W.push(k[P]<=0),p&&W.push(k[L]<=0,k[S]<=0),W.every(function(e){return e})){E=j,R=!1;break}O.set(j,W)}if(R)for(var D=m?3:1,B=function(e){var t=b.find(function(t){var o=O.get(t);if(o)return o.slice(0,e).every(function(e){return e})});if(t)return E=t,"break"},C=D;C>0&&"break"!==B(C);C--);t.placement!==E&&(t.modifiersData[r]._skip=!0,t.placement=E,t.reset=!0)}},requiresIfExists:["offset"],data:{_skip:!1}},{name:"preventOverflow",enabled:!0,phase:"main",fn:function(e){var t=e.state,o=e.options,r=e.name,n=o.mainAxis,i=o.altAxis,a=o.boundary,p=o.rootBoundary,s=o.altBoundary,l=o.padding,c=o.tether,f=void 0===c||c,u=o.tetherOffset,d=void 0===u?0:u,m=eO(t,{boundary:a,rootBoundary:p,padding:l,altBoundary:s}),h=ea(t.placement),v=ep(t.placement),g=!v,y=es(h),b="x"===y?"y":"x",w=t.modifiersData.popperOffsets,x=t.rects.reference,O=t.rects.popper,R="function"==typeof d?d(Object.assign({},t.rects,{placement:t.placement})):d,E="number"==typeof R?{mainAxis:R,altAxis:R}:Object.assign({mainAxis:0,altAxis:0},R),T=t.modifiersData.offset?t.modifiersData.offset[t.placement]:null,j={x:0,y:0};if(w){if(void 0===n||n){var P,M="y"===y?"top":Y,k="y"===y?_:X,L="y"===y?"height":"width",S=w[y],W=S+m[M],D=S-m[k],B=f?-O[L]/2:0,C=v===K?x[L]:O[L],H=v===K?-O[L]:-x[L],N=t.elements.arrow,I=f&&N?F(N):{width:0,height:0},V=t.modifiersData["arrow#persistent"]?t.modifiersData["arrow#persistent"].padding:eb(),q=V[M],U=V[k],G=eR(0,x[L],I[L]),J=g?x[L]/2-B-G-q-E.mainAxis:C-G-q-E.mainAxis,Q=g?-x[L]/2+B+G+U+E.mainAxis:H+G+U+E.mainAxis,$=t.elements.arrow&&z(t.elements.arrow),ee=$?"y"===y?$.clientTop||0:$.clientLeft||0:0,et=null!=(P=null==T?void 0:T[y])?P:0,eo=eR(f?A(W,S+J-et-ee):W,S,f?Z(D,S+Q-et):D);w[y]=eo,j[y]=eo-S}if(void 0!==i&&i){var er,en,ei="x"===y?"top":Y,el="x"===y?_:X,ec=w[b],ef="y"===b?"height":"width",eu=ec+m[ei],ed=ec-m[el],em=-1!==["top",Y].indexOf(h),eh=null!=(en=null==T?void 0:T[b])?en:0,ev=em?eu:ec-x[ef]-O[ef]-eh+E.altAxis,eg=em?ec+x[ef]+O[ef]-eh-E.altAxis:ed,ey=f&&em?(er=eR(ev,ec,eg))>eg?eg:er:eR(f?ev:eu,ec,f?eg:ed);w[b]=ey,j[b]=ey-ec}t.modifiersData[r]=j}},requiresIfExists:["offset"]},{name:"arrow",enabled:!0,phase:"main",fn:function(e){var t,o,r=e.state,n=e.name,i=e.options,a=r.elements.arrow,p=r.modifiersData.popperOffsets,s=ea(r.placement),l=es(s),c=[Y,X].indexOf(s)>=0?"height":"width";if(a&&p){var f=ew("number"!=typeof(t="function"==typeof(t=i.padding)?t(Object.assign({},r.rects,{placement:r.placement})):t)?t:ex(t,J)),u=F(a),d="y"===l?"top":Y,m="y"===l?_:X,h=r.rects.reference[c]+r.rects.reference[l]-p[l]-r.rects.popper[c],v=p[l]-r.rects.reference[l],g=z(a),y=g?"y"===l?g.clientHeight||0:g.clientWidth||0:0,b=f[d],w=y-u[c]-f[m],x=y/2-u[c]/2+(h/2-v/2),O=eR(b,x,w);r.modifiersData[n]=((o={})[l]=O,o.centerOffset=O-x,o)}},effect:function(e){var t=e.state,o=e.options.element,r=void 0===o?"[data-popper-arrow]":o;null!=r&&("string"!=typeof r||(r=t.elements.popper.querySelector(r)))&&ev(t.elements.popper,r)&&(t.elements.arrow=r)},requires:["popperOffsets"],requiresIfExists:["preventOverflow"]},{name:"hide",enabled:!0,phase:"main",requiresIfExists:["preventOverflow"],fn:function(e){var t=e.state,o=e.name,r=t.rects.reference,n=t.rects.popper,i=t.modifiersData.preventOverflow,a=eO(t,{elementContext:"reference"}),p=eO(t,{altBoundary:!0}),s=eE(a,r),l=eE(p,n,i),c=eT(s),f=eT(l);t.modifiersData[o]={referenceClippingOffsets:s,popperEscapeOffsets:l,isReferenceHidden:c,hasPopperEscaped:f},t.attributes.popper=Object.assign({},t.attributes.popper,{"data-popper-reference-hidden":c,"data-popper-escaped":f})}}]}).defaultModifiers)?[]:n,p=void 0===(a=r.defaultOptions)?er:a,function(e,t,o){void 0===o&&(o=p);var r,n,a={placement:"bottom",orderedModifiers:[],options:Object.assign({},er,p),modifiersData:{},elements:{reference:e,popper:t},attributes:{},styles:{}},s=[],l=!1,c={state:a,setOptions:function(o){var r,n,l,u,d,m="function"==typeof o?o(a.options):o;f(),a.options=Object.assign({},p,a.options,m),a.scrollParents={reference:j(e)?q(e):e.contextElement?q(e.contextElement):[],popper:q(t)};var h=(n=Object.keys(r=[].concat(i,a.options.modifiers).reduce(function(e,t){var o=e[t.name];return e[t.name]=o?Object.assign({},o,t,{options:Object.assign({},o.options,t.options),data:Object.assign({},o.data,t.data)}):t,e},{})).map(function(e){return r[e]}),l=new Map,u=new Set,d=[],n.forEach(function(e){l.set(e.name,e)}),n.forEach(function(e){u.has(e.name)||function e(t){u.add(t.name),[].concat(t.requires||[],t.requiresIfExists||[]).forEach(function(t){if(!u.has(t)){var o=l.get(t);o&&e(o)}}),d.push(t)}(e)}),eo.reduce(function(e,t){return e.concat(d.filter(function(e){return e.phase===t}))},[]));return a.orderedModifiers=h.filter(function(e){return e.enabled}),a.orderedModifiers.forEach(function(e){var t=e.name,o=e.options,r=e.effect;if("function"==typeof r){var n=r({state:a,name:t,instance:c,options:void 0===o?{}:o});s.push(n||function(){})}}),c.update()},forceUpdate:function(){if(!l){var e,t,o,r,n,i,p,s,f,u,d,m,h=a.elements,v=h.reference,g=h.popper;if(en(v,g)){a.rects={reference:(t=z(g),o="fixed"===a.options.strategy,r=P(t),s=P(t)&&(i=k((n=t.getBoundingClientRect()).width)/t.offsetWidth||1,p=k(n.height)/t.offsetHeight||1,1!==i||1!==p),f=C(t),u=W(v,s,o),d={scrollLeft:0,scrollTop:0},m={x:0,y:0},(r||!r&&!o)&&(("body"!==B(t)||I(f))&&(d=(e=t)!==T(e)&&P(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:D(e)),P(t)?(m=W(t,!0),m.x+=t.clientLeft,m.y+=t.clientTop):f&&(m.x=H(f))),{x:u.left+d.scrollLeft-m.x,y:u.top+d.scrollTop-m.y,width:u.width,height:u.height}),popper:F(g)},a.reset=!1,a.placement=a.options.placement,a.orderedModifiers.forEach(function(e){return a.modifiersData[e.name]=Object.assign({},e.data)});for(var y=0;y<a.orderedModifiers.length;y++){if(!0===a.reset){a.reset=!1,y=-1;continue}var b=a.orderedModifiers[y],w=b.fn,x=b.options,O=void 0===x?{}:x,R=b.name;"function"==typeof w&&(a=w({state:a,options:O,name:R,instance:c})||a)}}}},update:(r=function(){return new Promise(function(e){c.forceUpdate(),e(a)})},function(){return n||(n=new Promise(function(e){Promise.resolve().then(function(){n=void 0,e(r())})})),n}),destroy:function(){f(),l=!0}};if(!en(e,t))return c;function f(){s.forEach(function(e){return e()}),s=[]}return c.setOptions(o).then(function(e){!l&&o.onFirstUpdate&&o.onFirstUpdate(e)}),c}),eP=o(15988),eM=o(94589),eZ=o(94143),eA=o(50738);function ek(e){return(0,eA.ZP)("MuiPopper",e)}(0,eZ.Z)("MuiPopper",["root"]);var eL=o(57437);function eS(e){return"function"==typeof e?e():e}let eW=e=>{let{classes:t}=e;return(0,f.Z)({root:["root"]},ek,t)},eD={},eB=s.forwardRef(function(e,t){var o;let{anchorEl:r,children:n,direction:i,disablePortal:a,modifiers:p,open:l,placement:c,popperOptions:f,popperRef:u,slotProps:d={},slots:m={},TransitionProps:h,ownerState:v,...g}=e,y=s.useRef(null),b=(0,O.Z)(y,t),w=s.useRef(null),x=(0,O.Z)(w,u),E=s.useRef(x);(0,R.Z)(()=>{E.current=x},[x]),s.useImperativeHandle(u,()=>w.current,[]);let T=function(e,t){if("ltr"===t)return e;switch(e){case"bottom-end":return"bottom-start";case"bottom-start":return"bottom-end";case"top-end":return"top-start";case"top-start":return"top-end";default:return e}}(c,i),[j,P]=s.useState(T),[M,Z]=s.useState(eS(r));s.useEffect(()=>{w.current&&w.current.forceUpdate()}),s.useEffect(()=>{r&&Z(eS(r))},[r]),(0,R.Z)(()=>{if(!M||!l)return;let e=e=>{P(e.placement)},t=[{name:"preventOverflow",options:{altBoundary:a}},{name:"flip",options:{altBoundary:a}},{name:"onUpdate",enabled:!0,phase:"afterWrite",fn:t=>{let{state:o}=t;e(o)}}];null!=p&&(t=t.concat(p)),f&&null!=f.modifiers&&(t=t.concat(f.modifiers));let o=ej(M,y.current,{placement:T,...f,modifiers:t});return E.current(o),()=>{o.destroy(),E.current(null)}},[M,a,p,l,f,T]);let A={placement:j};null!==h&&(A.TransitionProps=h);let k=eW(e),L=null!==(o=m.root)&&void 0!==o?o:"div",S=(0,eP.Z)({elementType:L,externalSlotProps:d.root,externalForwardedProps:g,additionalProps:{role:"tooltip",ref:b},ownerState:e,className:k.root});return(0,eL.jsx)(L,{...S,children:"function"==typeof n?n(A):n})}),eC=s.forwardRef(function(e,t){let o;let{anchorEl:r,children:n,container:i,direction:a="ltr",disablePortal:p=!1,keepMounted:l=!1,modifiers:c,open:f,placement:u="bottom",popperOptions:d=eD,popperRef:m,style:h,transition:v=!1,slotProps:g={},slots:y={},...b}=e,[w,x]=s.useState(!0);if(!l&&!f&&(!v||w))return null;if(i)o=i;else if(r){let e=eS(r);o=e&&void 0!==e.nodeType?(0,E.Z)(e).body:(0,E.Z)(null).body}let O=!f&&l&&(!v||w)?"none":void 0,R=v?{in:f,onEnter:()=>{x(!1)},onExited:()=>{x(!0)}}:void 0;return(0,eL.jsx)(eM.Z,{disablePortal:p,container:o,children:(0,eL.jsx)(eB,{anchorEl:r,direction:a,disablePortal:p,modifiers:c,ref:t,open:v?!w:f,placement:u,popperOptions:d,popperRef:m,slotProps:g,slots:y,...b,style:{position:"fixed",top:0,left:0,display:O,...h},TransitionProps:R,children:n})})}),eH=(0,v.default)(eC,{name:"MuiPopper",slot:"Root",overridesResolver:(e,t)=>t.root})({}),eN=s.forwardRef(function(e,t){var o;let r=(0,d.useRtl)(),{anchorEl:n,component:i,components:a,componentsProps:p,container:s,disablePortal:l,keepMounted:c,modifiers:f,open:u,placement:m,popperOptions:h,popperRef:v,transition:g,slots:y,slotProps:w,...x}=(0,b.i)({props:e,name:"MuiPopper"}),O=null!==(o=null==y?void 0:y.root)&&void 0!==o?o:null==a?void 0:a.Root,R={anchorEl:n,container:s,disablePortal:l,keepMounted:c,modifiers:f,open:u,placement:m,popperOptions:h,popperRef:v,transition:g,...x};return(0,eL.jsx)(eH,{as:i,direction:r?"rtl":"ltr",slots:{root:O},slotProps:null!=w?w:p,...R,ref:t})});var eI=o(9665),eF=o(60118),eV=o(32709),eq=o(4778),eU=o(79114);function ez(e){return(0,eA.ZP)("MuiTooltip",e)}let e_=(0,eZ.Z)("MuiTooltip",["popper","popperInteractive","popperArrow","popperClose","tooltip","tooltipArrow","touch","tooltipPlacementLeft","tooltipPlacementRight","tooltipPlacementTop","tooltipPlacementBottom","arrow"]),eX=e=>{let{classes:t,disableInteractive:o,arrow:r,touch:n,placement:i}=e,a={popper:["popper",!o&&"popperInteractive",r&&"popperArrow"],tooltip:["tooltip",r&&"tooltipArrow",n&&"touch","tooltipPlacement".concat((0,w.Z)(i.split("-")[0]))],arrow:["arrow"]};return(0,f.Z)(a,ez,t)},eY=(0,v.default)(eN,{name:"MuiTooltip",slot:"Popper",overridesResolver:(e,t)=>{let{ownerState:o}=e;return[t.popper,!o.disableInteractive&&t.popperInteractive,o.arrow&&t.popperArrow,!o.open&&t.popperClose]}})((0,y.Z)(e=>{let{theme:t}=e;return{zIndex:(t.vars||t).zIndex.tooltip,pointerEvents:"none",variants:[{props:e=>{let{ownerState:t}=e;return!t.disableInteractive},style:{pointerEvents:"auto"}},{props:e=>{let{open:t}=e;return!t},style:{pointerEvents:"none"}},{props:e=>{let{ownerState:t}=e;return t.arrow},style:{['&[data-popper-placement*="bottom"] .'.concat(e_.arrow)]:{top:0,marginTop:"-0.71em","&::before":{transformOrigin:"0 100%"}},['&[data-popper-placement*="top"] .'.concat(e_.arrow)]:{bottom:0,marginBottom:"-0.71em","&::before":{transformOrigin:"100% 0"}},['&[data-popper-placement*="right"] .'.concat(e_.arrow)]:{height:"1em",width:"0.71em","&::before":{transformOrigin:"100% 100%"}},['&[data-popper-placement*="left"] .'.concat(e_.arrow)]:{height:"1em",width:"0.71em","&::before":{transformOrigin:"0 0"}}}},{props:e=>{let{ownerState:t}=e;return t.arrow&&!t.isRtl},style:{['&[data-popper-placement*="right"] .'.concat(e_.arrow)]:{left:0,marginLeft:"-0.71em"}}},{props:e=>{let{ownerState:t}=e;return t.arrow&&!!t.isRtl},style:{['&[data-popper-placement*="right"] .'.concat(e_.arrow)]:{right:0,marginRight:"-0.71em"}}},{props:e=>{let{ownerState:t}=e;return t.arrow&&!t.isRtl},style:{['&[data-popper-placement*="left"] .'.concat(e_.arrow)]:{right:0,marginRight:"-0.71em"}}},{props:e=>{let{ownerState:t}=e;return t.arrow&&!!t.isRtl},style:{['&[data-popper-placement*="left"] .'.concat(e_.arrow)]:{left:0,marginLeft:"-0.71em"}}}]}})),eG=(0,v.default)("div",{name:"MuiTooltip",slot:"Tooltip",overridesResolver:(e,t)=>{let{ownerState:o}=e;return[t.tooltip,o.touch&&t.touch,o.arrow&&t.tooltipArrow,t["tooltipPlacement".concat((0,w.Z)(o.placement.split("-")[0]))]]}})((0,y.Z)(e=>{let{theme:t}=e;return{backgroundColor:t.vars?t.vars.palette.Tooltip.bg:(0,u.Fq)(t.palette.grey[700],.92),borderRadius:(t.vars||t).shape.borderRadius,color:(t.vars||t).palette.common.white,fontFamily:t.typography.fontFamily,padding:"4px 8px",fontSize:t.typography.pxToRem(11),maxWidth:300,margin:2,wordWrap:"break-word",fontWeight:t.typography.fontWeightMedium,[".".concat(e_.popper,'[data-popper-placement*="left"] &')]:{transformOrigin:"right center"},[".".concat(e_.popper,'[data-popper-placement*="right"] &')]:{transformOrigin:"left center"},[".".concat(e_.popper,'[data-popper-placement*="top"] &')]:{transformOrigin:"center bottom",marginBottom:"14px"},[".".concat(e_.popper,'[data-popper-placement*="bottom"] &')]:{transformOrigin:"center top",marginTop:"14px"},variants:[{props:e=>{let{ownerState:t}=e;return t.arrow},style:{position:"relative",margin:0}},{props:e=>{let{ownerState:t}=e;return t.touch},style:{padding:"8px 16px",fontSize:t.typography.pxToRem(14),lineHeight:"".concat(Math.round(16/14*1e5)/1e5,"em"),fontWeight:t.typography.fontWeightRegular}},{props:e=>{let{ownerState:t}=e;return!t.isRtl},style:{[".".concat(e_.popper,'[data-popper-placement*="left"] &')]:{marginRight:"14px"},[".".concat(e_.popper,'[data-popper-placement*="right"] &')]:{marginLeft:"14px"}}},{props:e=>{let{ownerState:t}=e;return!t.isRtl&&t.touch},style:{[".".concat(e_.popper,'[data-popper-placement*="left"] &')]:{marginRight:"24px"},[".".concat(e_.popper,'[data-popper-placement*="right"] &')]:{marginLeft:"24px"}}},{props:e=>{let{ownerState:t}=e;return!!t.isRtl},style:{[".".concat(e_.popper,'[data-popper-placement*="left"] &')]:{marginLeft:"14px"},[".".concat(e_.popper,'[data-popper-placement*="right"] &')]:{marginRight:"14px"}}},{props:e=>{let{ownerState:t}=e;return!!t.isRtl&&t.touch},style:{[".".concat(e_.popper,'[data-popper-placement*="left"] &')]:{marginLeft:"24px"},[".".concat(e_.popper,'[data-popper-placement*="right"] &')]:{marginRight:"24px"}}},{props:e=>{let{ownerState:t}=e;return t.touch},style:{[".".concat(e_.popper,'[data-popper-placement*="top"] &')]:{marginBottom:"24px"}}},{props:e=>{let{ownerState:t}=e;return t.touch},style:{[".".concat(e_.popper,'[data-popper-placement*="bottom"] &')]:{marginTop:"24px"}}}]}})),eJ=(0,v.default)("span",{name:"MuiTooltip",slot:"Arrow",overridesResolver:(e,t)=>t.arrow})((0,y.Z)(e=>{let{theme:t}=e;return{overflow:"hidden",position:"absolute",width:"1em",height:"0.71em",boxSizing:"border-box",color:t.vars?t.vars.palette.Tooltip.bg:(0,u.Fq)(t.palette.grey[700],.9),"&::before":{content:'""',margin:"auto",display:"block",width:"100%",height:"100%",backgroundColor:"currentColor",transform:"rotate(45deg)"}}})),eK=!1,eQ=new c.V,e$={x:0,y:0};function e0(e,t){return function(o){for(var r=arguments.length,n=Array(r>1?r-1:0),i=1;i<r;i++)n[i-1]=arguments[i];t&&t(o,...n),e(o,...n)}}var e1=s.forwardRef(function(e,t){var o,r,n;let i=(0,b.i)({props:e,name:"MuiTooltip"}),{arrow:a=!1,children:p,classes:f,components:u={},componentsProps:v={},describeChild:y=!1,disableFocusListener:w=!1,disableHoverListener:O=!1,disableInteractive:R=!1,disableTouchListener:E=!1,enterDelay:T=100,enterNextDelay:j=0,enterTouchDelay:P=700,followCursor:M=!1,id:Z,leaveDelay:A=0,leaveTouchDelay:k=1500,onClose:L,onOpen:S,open:W,placement:D="bottom",PopperComponent:B,PopperProps:C={},slotProps:H={},slots:N={},title:I,TransitionComponent:F,TransitionProps:V,...q}=i,U=s.isValidElement(p)?p:(0,eL.jsx)("span",{children:p}),z=(0,g.default)(),_=(0,d.useRtl)(),[X,Y]=s.useState(),[G,J]=s.useState(null),K=s.useRef(!1),Q=R||M,$=(0,c.Z)(),ee=(0,c.Z)(),et=(0,c.Z)(),eo=(0,c.Z)(),[er,en]=(0,eq.Z)({controlled:W,default:!1,name:"Tooltip",state:"open"}),ei=er,ea=(0,eV.Z)(Z),ep=s.useRef(),es=(0,eI.Z)(()=>{void 0!==ep.current&&(document.body.style.WebkitUserSelect=ep.current,ep.current=void 0),eo.clear()});s.useEffect(()=>es,[es]);let el=e=>{eQ.clear(),eK=!0,en(!0),S&&!ei&&S(e)},ec=(0,eI.Z)(e=>{eQ.start(800+A,()=>{eK=!1}),en(!1),L&&ei&&L(e),$.start(z.transitions.duration.shortest,()=>{K.current=!1})}),ef=e=>{K.current&&"touchstart"!==e.type||(X&&X.removeAttribute("title"),ee.clear(),et.clear(),T||eK&&j?ee.start(eK?j:T,()=>{el(e)}):el(e))},eu=e=>{ee.clear(),et.start(A,()=>{ec(e)})},[,ed]=s.useState(!1),em=e=>{(0,m.Z)(e.target)||(ed(!1),eu(e))},eh=e=>{X||Y(e.currentTarget),(0,m.Z)(e.target)&&(ed(!0),ef(e))},ev=e=>{K.current=!0;let t=U.props;t.onTouchStart&&t.onTouchStart(e)};s.useEffect(()=>{if(ei)return document.addEventListener("keydown",e),()=>{document.removeEventListener("keydown",e)};function e(e){"Escape"===e.key&&ec(e)}},[ec,ei]);let eg=(0,eF.Z)((0,h.Z)(U),Y,t);I||0===I||(ei=!1);let ey=s.useRef(),eb={},ew="string"==typeof I;y?(eb.title=ei||!ew||O?null:I,eb["aria-describedby"]=ei?ea:null):(eb["aria-label"]=ew?I:null,eb["aria-labelledby"]=ei&&!ew?ea:null);let ex={...eb,...q,...U.props,className:(0,l.Z)(q.className,U.props.className),onTouchStart:ev,ref:eg,...M?{onMouseMove:e=>{let t=U.props;t.onMouseMove&&t.onMouseMove(e),e$={x:e.clientX,y:e.clientY},ey.current&&ey.current.update()}}:{}},eO={};E||(ex.onTouchStart=e=>{ev(e),et.clear(),$.clear(),es(),ep.current=document.body.style.WebkitUserSelect,document.body.style.WebkitUserSelect="none",eo.start(P,()=>{document.body.style.WebkitUserSelect=ep.current,ef(e)})},ex.onTouchEnd=e=>{U.props.onTouchEnd&&U.props.onTouchEnd(e),es(),et.start(k,()=>{ec(e)})}),O||(ex.onMouseOver=e0(ef,ex.onMouseOver),ex.onMouseLeave=e0(eu,ex.onMouseLeave),Q||(eO.onMouseOver=ef,eO.onMouseLeave=eu)),w||(ex.onFocus=e0(eh,ex.onFocus),ex.onBlur=e0(em,ex.onBlur),Q||(eO.onFocus=eh,eO.onBlur=em));let eR={...i,isRtl:_,arrow:a,disableInteractive:Q,placement:D,PopperComponentProp:B,touch:K.current},eE="function"==typeof H.popper?H.popper(eR):H.popper,eT=s.useMemo(()=>{var e,t;let o=[{name:"arrow",enabled:!!G,options:{element:G,padding:4}}];return(null===(e=C.popperOptions)||void 0===e?void 0:e.modifiers)&&(o=o.concat(C.popperOptions.modifiers)),(null==eE?void 0:null===(t=eE.popperOptions)||void 0===t?void 0:t.modifiers)&&(o=o.concat(eE.popperOptions.modifiers)),{...C.popperOptions,...null==eE?void 0:eE.popperOptions,modifiers:o}},[G,C.popperOptions,null==eE?void 0:eE.popperOptions]),ej=eX(eR),eP="function"==typeof H.transition?H.transition(eR):H.transition,eM={slots:{popper:u.Popper,transition:null!==(o=u.Transition)&&void 0!==o?o:F,tooltip:u.Tooltip,arrow:u.Arrow,...N},slotProps:{arrow:null!==(r=H.arrow)&&void 0!==r?r:v.arrow,popper:{...C,...null!=eE?eE:v.popper},tooltip:null!==(n=H.tooltip)&&void 0!==n?n:v.tooltip,transition:{...V,...null!=eP?eP:v.transition}}},[eZ,eA]=(0,eU.Z)("popper",{elementType:eY,externalForwardedProps:eM,ownerState:eR,className:(0,l.Z)(ej.popper,null==C?void 0:C.className)}),[ek,eS]=(0,eU.Z)("transition",{elementType:x.Z,externalForwardedProps:eM,ownerState:eR}),[eW,eD]=(0,eU.Z)("tooltip",{elementType:eG,className:ej.tooltip,externalForwardedProps:eM,ownerState:eR}),[eB,eC]=(0,eU.Z)("arrow",{elementType:eJ,className:ej.arrow,externalForwardedProps:eM,ownerState:eR,ref:J});return(0,eL.jsxs)(s.Fragment,{children:[s.cloneElement(U,ex),(0,eL.jsx)(eZ,{as:null!=B?B:eN,placement:D,anchorEl:M?{getBoundingClientRect:()=>({top:e$.y,left:e$.x,right:e$.x,bottom:e$.y,width:0,height:0})}:X,popperRef:ey,open:!!X&&ei,id:ea,transition:!0,...eO,...eA,popperOptions:eT,children:e=>{let{TransitionProps:t}=e;return(0,eL.jsx)(ek,{timeout:z.transitions.duration.shorter,...t,...eS,children:(0,eL.jsxs)(eW,{...eD,children:[I,a?(0,eL.jsx)(eB,{...eC}):null]})})}})]})})}}]);