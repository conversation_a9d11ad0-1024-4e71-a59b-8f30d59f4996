(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5034],{62145:function(e,t,n){"use strict";n.d(t,{default:function(){return a}}),n(2265);var r=n(63731),o=n(22166),i=n(57437);function l(e){let{theme:t,...n}=e,l=o.Z in t?t[o.Z]:void 0;return(0,i.jsx)(r.default,{...n,themeId:l?o.Z:void 0,theme:l||t})}var s=n(42899);function a(e){let{theme:t,...n}=e;if("function"==typeof t)return(0,i.jsx)(l,{theme:t,...n});let r=o.Z in t?t[o.Z]:t;return"colorSchemes"in r?(0,i.jsx)(s.CssVarsProvider,{theme:t,...n}):"vars"in r?(0,i.jsx)(l,{theme:t,...n}):(0,i.jsx)(l,{theme:{...t,vars:null},...n})}},42899:function(e,t,n){"use strict";n.r(t),n.d(t,{CssVarsProvider:function(){return p},Experimental_CssVarsProvider:function(){return f},getInitColorSchemeScript:function(){return h},useColorScheme:function(){return c}}),n(2265);var r=n(38720),o=n(1252),i=n(99163),l=n(84792),s=n(22166),a=n(57437);let{CssVarsProvider:u,useColorScheme:c,getInitColorSchemeScript:d}=(0,o.default)({themeId:s.Z,theme:()=>(0,i.Z)({cssVariables:!0}),colorSchemeStorageKey:"mui-color-scheme",modeStorageKey:"mui-mode",defaultColorScheme:{light:"light",dark:"dark"},resolveTheme:e=>{let t={...e,typography:(0,l.Z)(e.palette,e.typography)};return t.unstable_sx=function(e){return(0,r.Z)({sx:e,theme:this})},t}});function f(e){return(0,a.jsx)(u,{...e})}let m=!1,h=e=>(m||(console.warn("MUI: The getInitColorSchemeScript function has been deprecated.\n\nYou should use `import InitColorSchemeScript from '@mui/material/InitColorSchemeScript'`\nand replace the function call with `<InitColorSchemeScript />` instead."),m=!0),d(e)),p=u},31691:function(e,t,n){"use strict";n.d(t,{default:function(){return l}}),n(2265);var r=n(20135),o=n(55201),i=n(22166);function l(){let e=(0,r.default)(o.Z);return e[i.Z]||e}},64119:function(e,t,n){"use strict";n.d(t,{default:function(){return l}});var r=n(94873),o=n(55201),i=n(22166);function l(e){let{props:t,name:n}=e;return(0,r.default)({props:t,name:n,defaultTheme:o.Z,themeId:i.Z})}},46037:function(e,t,n){"use strict";let r=n(2265).createContext(null);t.Z=r},44462:function(e,t,n){"use strict";n.d(t,{Z:function(){return i}});var r=n(2265),o=n(46037);function i(){return r.useContext(o.Z)}},18598:function(e,t,n){"use strict";n.d(t,{default:function(){return i}}),n(2265);var r=n(3146),o=n(57437);function i(e){let{styles:t,defaultTheme:n={}}=e,i="function"==typeof t?e=>t(null==e||0===Object.keys(e).length?n:e):t;return(0,o.jsx)(r.xB,{styles:i})}},1696:function(e,t,n){"use strict";let r;n.d(t,{default:function(){return f}});var o=n(2265),i=n(25246),l=n(96749),s=n(11787),a=n(57437);let u=new Map,c={insert:void 0},d=(e,t)=>{let n=(0,l.Z)(e);return n.sheet=new t({key:n.key,nonce:n.sheet.nonce,container:n.sheet.container,speedy:n.sheet.isSpeedy,prepend:n.sheet.prepend,insertionPoint:n.sheet.insertionPoint}),n};if("object"==typeof document&&!(r=document.querySelector('[name="emotion-insertion-point"]'))){(r=document.createElement("meta")).setAttribute("name","emotion-insertion-point"),r.setAttribute("content","");let e=document.querySelector("head");e&&e.prepend(r)}function f(e){let{injectFirst:t,enableCssLayer:n,children:l}=e,f=o.useMemo(()=>{let e="".concat(t,"-").concat(n);if(u.has(e))return u.get(e);let o=function(e,t){if(e||t){class n extends s.m{insert(e,t){return c.insert?c.insert(e,t):(this.key&&this.key.endsWith("global")&&(this.before=r),super.insert(e,t))}}let o=d({key:"css",insertionPoint:e?r:void 0},n);if(t){let e=o.insert;o.insert=function(){for(var t=arguments.length,n=Array(t),r=0;r<t;r++)n[r]=arguments[r];return n[1].styles.startsWith("@layer")||(n[1].styles="@layer mui {".concat(n[1].styles,"}")),e(...n)}}return o}}(t,n);return u.set(e,o),o},[t,n]);return f?(0,a.jsx)(i.C,{value:f,children:l}):l}},40517:function(e,t,n){"use strict";n.d(t,{default:function(){return l}});var r=n(56063),o=n(98636);let i=(0,n(94143).Z)("MuiBox",["root"]);var l=(0,o.default)({defaultClassName:i.root,generateClassName:r.Z.generate})},63386:function(e,t,n){"use strict";let r=(0,n(20285).Z)();t.default=r},20285:function(e,t,n){"use strict";n.d(t,{Z:function(){return v}});var r=n(2265),o=n(61994),i=n(50738),l=n(20801),s=n(53004),a=n(94873),u=n(9084),c=n(88662),d=n(57437);let f=(0,c.Z)(),m=(0,u.Z)("div",{name:"MuiContainer",slot:"Root",overridesResolver:(e,t)=>{let{ownerState:n}=e;return[t.root,t[`maxWidth${(0,s.Z)(String(n.maxWidth))}`],n.fixed&&t.fixed,n.disableGutters&&t.disableGutters]}}),h=e=>(0,a.default)({props:e,name:"MuiContainer",defaultTheme:f}),p=(e,t)=>{let{classes:n,fixed:r,disableGutters:o,maxWidth:a}=e,u={root:["root",a&&`maxWidth${(0,s.Z)(String(a))}`,r&&"fixed",o&&"disableGutters"]};return(0,l.Z)(u,e=>(0,i.ZP)(t,e),n)};function v(e={}){let{createStyledComponent:t=m,useThemeProps:n=h,componentName:i="MuiContainer"}=e,l=t(({theme:e,ownerState:t})=>({width:"100%",marginLeft:"auto",boxSizing:"border-box",marginRight:"auto",...!t.disableGutters&&{paddingLeft:e.spacing(2),paddingRight:e.spacing(2),[e.breakpoints.up("sm")]:{paddingLeft:e.spacing(3),paddingRight:e.spacing(3)}}}),({theme:e,ownerState:t})=>t.fixed&&Object.keys(e.breakpoints.values).reduce((t,n)=>{let r=e.breakpoints.values[n];return 0!==r&&(t[e.breakpoints.up(n)]={maxWidth:`${r}${e.breakpoints.unit}`}),t},{}),({theme:e,ownerState:t})=>({..."xs"===t.maxWidth&&{[e.breakpoints.up("xs")]:{maxWidth:Math.max(e.breakpoints.values.xs,444)}},...t.maxWidth&&"xs"!==t.maxWidth&&{[e.breakpoints.up(t.maxWidth)]:{maxWidth:`${e.breakpoints.values[t.maxWidth]}${e.breakpoints.unit}`}}}));return r.forwardRef(function(e,t){let r=n(e),{className:s,component:a="div",disableGutters:u=!1,fixed:c=!1,maxWidth:f="lg",classes:m,...h}=r,v={...r,component:a,disableGutters:u,fixed:c,maxWidth:f},g=p(v,i);return(0,d.jsx)(l,{as:a,ownerState:v,className:(0,o.Z)(g.root,s),ref:t,...h})})}},21075:function(e,t,n){"use strict";n.r(t),n(2265);var r=n(18598),o=n(20135),i=n(57437);t.default=function(e){let{styles:t,themeId:n,defaultTheme:l={}}=e,s=(0,o.default)(l),a="function"==typeof t?t(n&&s[n]||s):t;return(0,i.jsx)(r.default,{styles:a})}},45165:function(e,t,n){"use strict";n.r(t),n.d(t,{default:function(){return A}});var r=n(2265),o=n(61994),i=n(94378),l=n(50738),s=n(20801),a=n(9084),u=n(94873),c=n(20135),d=n(95086),f=n(88662);let m=(e,t)=>e.filter(e=>t.includes(e)),h=(e,t,n)=>{let r=e.keys[0];Array.isArray(t)?t.forEach((t,r)=>{n((t,n)=>{r<=e.keys.length-1&&(0===r?Object.assign(t,n):t[e.up(e.keys[r])]=n)},t)}):t&&"object"==typeof t?(Object.keys(t).length>e.keys.length?e.keys:m(e.keys,Object.keys(t))).forEach(o=>{if(e.keys.includes(o)){let i=t[o];void 0!==i&&n((t,n)=>{r===o?Object.assign(t,n):t[e.up(o)]=n},i)}}):("number"==typeof t||"string"==typeof t)&&n((e,t)=>{Object.assign(e,t)},t)};function p(e){return`--Grid-${e}Spacing`}function v(e){return`--Grid-parent-${e}Spacing`}let g="--Grid-columns",y="--Grid-parent-columns",S=({theme:e,ownerState:t})=>{let n={};return h(e.breakpoints,t.size,(e,t)=>{let r={};"grow"===t&&(r={flexBasis:0,flexGrow:1,maxWidth:"100%"}),"auto"===t&&(r={flexBasis:"auto",flexGrow:0,flexShrink:0,maxWidth:"none",width:"auto"}),"number"==typeof t&&(r={flexGrow:0,flexBasis:"auto",width:`calc(100% * ${t} / var(${y}) - (var(${y}) - ${t}) * (var(${v("column")}) / var(${y})))`}),e(n,r)}),n},b=({theme:e,ownerState:t})=>{let n={};return h(e.breakpoints,t.offset,(e,t)=>{let r={};"auto"===t&&(r={marginLeft:"auto"}),"number"==typeof t&&(r={marginLeft:0===t?"0px":`calc(100% * ${t} / var(${y}) + var(${v("column")}) * ${t} / var(${y}))`}),e(n,r)}),n},k=({theme:e,ownerState:t})=>{if(!t.container)return{};let n={[g]:12};return h(e.breakpoints,t.columns,(e,t)=>{let r=t??12;e(n,{[g]:r,"> *":{[y]:r}})}),n},x=({theme:e,ownerState:t})=>{if(!t.container)return{};let n={};return h(e.breakpoints,t.rowSpacing,(t,r)=>{let o="string"==typeof r?r:e.spacing?.(r);t(n,{[p("row")]:o,"> *":{[v("row")]:o}})}),n},w=({theme:e,ownerState:t})=>{if(!t.container)return{};let n={};return h(e.breakpoints,t.columnSpacing,(t,r)=>{let o="string"==typeof r?r:e.spacing?.(r);t(n,{[p("column")]:o,"> *":{[v("column")]:o}})}),n},$=({theme:e,ownerState:t})=>{if(!t.container)return{};let n={};return h(e.breakpoints,t.direction,(e,t)=>{e(n,{flexDirection:t})}),n},C=({ownerState:e})=>({minWidth:0,boxSizing:"border-box",...e.container&&{display:"flex",flexWrap:"wrap",...e.wrap&&"wrap"!==e.wrap&&{flexWrap:e.wrap},gap:`var(${p("row")}) var(${p("column")})`}}),Z=e=>{let t=[];return Object.entries(e).forEach(([e,n])=>{!1!==n&&void 0!==n&&t.push(`grid-${e}-${String(n)}`)}),t},M=(e,t="xs")=>{function n(e){return void 0!==e&&("string"==typeof e&&!Number.isNaN(Number(e))||"number"==typeof e&&e>0)}if(n(e))return[`spacing-${t}-${String(e)}`];if("object"==typeof e&&!Array.isArray(e)){let t=[];return Object.entries(e).forEach(([e,r])=>{n(r)&&t.push(`spacing-${e}-${String(r)}`)}),t}return[]},j=e=>void 0===e?[]:"object"==typeof e?Object.entries(e).map(([e,t])=>`direction-${e}-${t}`):[`direction-xs-${String(e)}`];var E=n(57437);let _=(0,f.Z)(),W=(0,a.Z)("div",{name:"MuiGrid",slot:"Root",overridesResolver:(e,t)=>t.root});function I(e){return(0,u.default)({props:e,name:"MuiGrid",defaultTheme:_})}var A=function(e={}){let{createStyledComponent:t=W,useThemeProps:n=I,useTheme:a=c.default,componentName:u="MuiGrid"}=e,f=(e,t)=>{let{container:n,direction:r,spacing:o,wrap:i,size:a}=e,c={root:["root",n&&"container","wrap"!==i&&`wrap-xs-${String(i)}`,...j(r),...Z(a),...n?M(o,t.breakpoints.keys[0]):[]]};return(0,s.Z)(c,e=>(0,l.ZP)(u,e),{})};function m(e,t,n=()=>!0){let r={};return null===e||(Array.isArray(e)?e.forEach((e,o)=>{null!==e&&n(e)&&t.keys[o]&&(r[t.keys[o]]=e)}):"object"==typeof e?Object.keys(e).forEach(t=>{let o=e[t];null!=o&&n(o)&&(r[t]=o)}):r[t.keys[0]]=e),r}let h=t(k,w,x,S,$,C,b),p=r.forwardRef(function(e,t){let l=a(),s=n(e),u=(0,d.Z)(s);!function(e,t){let n=[];void 0!==e.item&&(delete e.item,n.push("item")),void 0!==e.zeroMinWidth&&(delete e.zeroMinWidth,n.push("zeroMinWidth")),t.keys.forEach(t=>{void 0!==e[t]&&(n.push(t),delete e[t])})}(u,l.breakpoints);let{className:c,children:p,columns:v=12,container:g=!1,component:y="div",direction:S="row",wrap:b="wrap",size:k={},offset:x={},spacing:w=0,rowSpacing:$=w,columnSpacing:C=w,unstable_level:Z=0,...M}=u,j=m(k,l.breakpoints,e=>!1!==e),_=m(x,l.breakpoints),W=e.columns??(Z?void 0:v),I=e.spacing??(Z?void 0:w),A=e.rowSpacing??e.spacing??(Z?void 0:$),N=e.columnSpacing??e.spacing??(Z?void 0:C),P={...u,level:Z,columns:W,container:g,direction:S,wrap:b,spacing:I,rowSpacing:A,columnSpacing:N,size:j,offset:_},L=f(P,l);return(0,E.jsx)(h,{ref:t,as:y,ownerState:P,className:(0,o.Z)(L.root,c),...M,children:r.Children.map(p,e=>r.isValidElement(e)&&(0,i.Z)(e,["Grid"])&&g&&e.props.container?r.cloneElement(e,{unstable_level:e.props?.unstable_level??Z+1}):e)})});return p.muiName="Grid",p}()},77126:function(e,t,n){"use strict";n.r(t),n.d(t,{useRtl:function(){return l}});var r=n(2265),o=n(57437);let i=r.createContext(),l=()=>{let e=r.useContext(i);return null!=e&&e};t.default=function(e){let{value:t,...n}=e;return(0,o.jsx)(i.Provider,{value:null==t||t,...n})}},53146:function(e,t,n){"use strict";n.r(t),n.d(t,{default:function(){return b}});var r=n(2265),o=n(61994),i=n(87354),l=n(50738),s=n(20801),a=n(9084),u=n(94873),c=n(95086),d=n(88662),f=n(27880),m=n(46380),h=n(57437);let p=(0,d.Z)(),v=(0,a.Z)("div",{name:"MuiStack",slot:"Root",overridesResolver:(e,t)=>t.root});function g(e){return(0,u.default)({props:e,name:"MuiStack",defaultTheme:p})}let y=e=>({row:"Left","row-reverse":"Right",column:"Top","column-reverse":"Bottom"})[e],S=({ownerState:e,theme:t})=>{let n={display:"flex",flexDirection:"column",...(0,f.k9)({theme:t},(0,f.P$)({values:e.direction,breakpoints:t.breakpoints.values}),e=>({flexDirection:e}))};if(e.spacing){let r=(0,m.hB)(t),o=Object.keys(t.breakpoints.values).reduce((t,n)=>(("object"==typeof e.spacing&&null!=e.spacing[n]||"object"==typeof e.direction&&null!=e.direction[n])&&(t[n]=!0),t),{}),l=(0,f.P$)({values:e.direction,base:o}),s=(0,f.P$)({values:e.spacing,base:o});"object"==typeof l&&Object.keys(l).forEach((e,t,n)=>{if(!l[e]){let r=t>0?l[n[t-1]]:"column";l[e]=r}}),n=(0,i.Z)(n,(0,f.k9)({theme:t},s,(t,n)=>e.useFlexGap?{gap:(0,m.NA)(r,t)}:{"& > :not(style):not(style)":{margin:0},"& > :not(style) ~ :not(style)":{[`margin${y(n?l[n]:e.direction)}`]:(0,m.NA)(r,t)}}))}return(0,f.dt)(t.breakpoints,n)};var b=function(e={}){let{createStyledComponent:t=v,useThemeProps:n=g,componentName:i="MuiStack"}=e,a=()=>(0,s.Z)({root:["root"]},e=>(0,l.ZP)(i,e),{}),u=t(S);return r.forwardRef(function(e,t){let i=n(e),{component:l="div",direction:s="column",spacing:d=0,divider:f,children:m,className:p,useFlexGap:v=!1,...g}=(0,c.Z)(i),y=a();return(0,h.jsx)(u,{as:l,ownerState:{direction:s,spacing:d,useFlexGap:v},ref:t,className:(0,o.Z)(y.root,p),...g,children:f?function(e,t){let n=r.Children.toArray(e).filter(Boolean);return n.reduce((e,o,i)=>(e.push(o),i<n.length-1&&e.push(r.cloneElement(t,{key:`separator-${i}`})),e),[])}(m,f):m})})}()},63731:function(e,t,n){"use strict";n.d(t,{default:function(){return p}});var r=n(2265),o=n(44462),i=n(46037),l="function"==typeof Symbol&&Symbol.for?Symbol.for("mui.nested"):"__THEME_NESTED__",s=n(57437),a=function(e){let{children:t,theme:n}=e,a=(0,o.Z)(),u=r.useMemo(()=>{let e=null===a?{...n}:"function"==typeof n?n(a):{...a,...n};return null!=e&&(e[l]=null!==a),e},[n,a]);return(0,s.jsx)(i.Z.Provider,{value:u,children:t})},u=n(25246),c=n(80184),d=n(77126),f=n(17804);let m={};function h(e,t,n){let o=arguments.length>3&&void 0!==arguments[3]&&arguments[3];return r.useMemo(()=>{let r=e&&t[e]||t;if("function"==typeof n){let i=n(r),l=e?{...t,[e]:i}:i;return o?()=>l:l}return e?{...t,[e]:n}:{...t,...n}},[e,t,n,o])}var p=function(e){let{children:t,theme:n,themeId:r}=e,i=(0,c.default)(m),l=(0,o.Z)()||m,p=h(r,i,n),v=h(r,l,n,!0),g="rtl"===(r?p[r]:p).direction;return(0,s.jsx)(a,{theme:v,children:(0,s.jsx)(u.T.Provider,{value:p,children:(0,s.jsx)(d.default,{value:g,children:(0,s.jsx)(f.Z,{value:r?p[r].components:p.components,children:t})})})})}},1252:function(e,t,n){"use strict";n.r(t),n.d(t,{DISABLE_CSS_TRANSITION:function(){return v},default:function(){return g}});var r=n(2265),o=n(18598),i=n(44462),l=n(3450),s=n(63731),a=n(57437);let u="mode",c="color-scheme";function d(){}var f=({key:e,storageWindow:t})=>(t||"undefined"==typeof window||(t=window),{get(n){let r;if("undefined"!=typeof window){if(!t)return n;try{r=t.localStorage.getItem(e)}catch{}return r||n}},set:n=>{if(t)try{t.localStorage.setItem(e,n)}catch{}},subscribe:n=>{if(!t)return d;let r=t=>{let r=t.newValue;t.key===e&&n(r)};return t.addEventListener("storage",r),()=>{t.removeEventListener("storage",r)}}});function m(){}function h(e){if("undefined"!=typeof window&&"function"==typeof window.matchMedia&&"system"===e)return window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light"}function p(e,t){return"light"===e.mode||"system"===e.mode&&"light"===e.systemMode?t("light"):"dark"===e.mode||"system"===e.mode&&"dark"===e.systemMode?t("dark"):void 0}let v="*{-webkit-transition:none!important;-moz-transition:none!important;-o-transition:none!important;-ms-transition:none!important;transition:none!important}";function g(e){let{themeId:t,theme:n={},modeStorageKey:d=u,colorSchemeStorageKey:g=c,disableTransitionOnChange:y=!1,defaultColorScheme:S,resolveTheme:b}=e,k={allColorSchemes:[],colorScheme:void 0,darkColorScheme:void 0,lightColorScheme:void 0,mode:void 0,setColorScheme:()=>{},setMode:()=>{},systemMode:void 0},x=r.createContext(void 0),w={},$={},C="string"==typeof S?S:S.light,Z="string"==typeof S?S:S.dark;return{CssVarsProvider:function(e){var k,C,Z,M;let{children:j,theme:E,modeStorageKey:_=d,colorSchemeStorageKey:W=g,disableTransitionOnChange:I=y,storageManager:A,storageWindow:N="undefined"==typeof window?void 0:window,documentNode:P="undefined"==typeof document?void 0:document,colorSchemeNode:L="undefined"==typeof document?void 0:document.documentElement,disableNestedContext:O=!1,disableStyleSheetGeneration:G=!1,defaultMode:R="system",noSsr:T}=e,V=r.useRef(!1),B=(0,i.Z)(),z=r.useContext(x),K=!!z&&!O,D=r.useMemo(()=>E||("function"==typeof n?n():n),[E]),q=D[t],F=q||D,{colorSchemes:H=w,components:U=$,cssVarPrefix:Q}=F,Y=Object.keys(H).filter(e=>!!H[e]).join(","),J=r.useMemo(()=>Y.split(","),[Y]),X="string"==typeof S?S:S.light,ee="string"==typeof S?S:S.dark,et=H[X]&&H[ee]?R:(null===(C=H[F.defaultColorScheme])||void 0===C?void 0:null===(k=C.palette)||void 0===k?void 0:k.mode)||(null===(Z=F.palette)||void 0===Z?void 0:Z.mode),{mode:en,setMode:er,systemMode:eo,lightColorScheme:ei,darkColorScheme:el,colorScheme:es,setColorScheme:ea}=function(e){let{defaultMode:t="light",defaultLightColorScheme:n,defaultDarkColorScheme:o,supportedColorSchemes:i=[],modeStorageKey:l=u,colorSchemeStorageKey:s=c,storageWindow:a="undefined"==typeof window?void 0:window,storageManager:d=f,noSsr:v=!1}=e,g=i.join(","),y=i.length>1,S=r.useMemo(()=>null==d?void 0:d({key:l,storageWindow:a}),[d,l,a]),b=r.useMemo(()=>null==d?void 0:d({key:"".concat(s,"-light"),storageWindow:a}),[d,s,a]),k=r.useMemo(()=>null==d?void 0:d({key:"".concat(s,"-dark"),storageWindow:a}),[d,s,a]),[x,w]=r.useState(()=>{let e=(null==S?void 0:S.get(t))||t,r=(null==b?void 0:b.get(n))||n,i=(null==k?void 0:k.get(o))||o;return{mode:e,systemMode:h(e),lightColorScheme:r,darkColorScheme:i}}),[$,C]=r.useState(v||!y);r.useEffect(()=>{C(!0)},[]);let Z=p(x,e=>"light"===e?x.lightColorScheme:"dark"===e?x.darkColorScheme:void 0),M=r.useCallback(e=>{w(n=>{if(e===n.mode)return n;let r=null!=e?e:t;return null==S||S.set(r),{...n,mode:r,systemMode:h(r)}})},[S,t]),j=r.useCallback(e=>{e?"string"==typeof e?e&&!g.includes(e)?console.error("`".concat(e,"` does not exist in `theme.colorSchemes`.")):w(t=>{let n={...t};return p(t,t=>{"light"===t&&(null==b||b.set(e),n.lightColorScheme=e),"dark"===t&&(null==k||k.set(e),n.darkColorScheme=e)}),n}):w(t=>{let r={...t},i=null===e.light?n:e.light,l=null===e.dark?o:e.dark;return i&&(g.includes(i)?(r.lightColorScheme=i,null==b||b.set(i)):console.error("`".concat(i,"` does not exist in `theme.colorSchemes`."))),l&&(g.includes(l)?(r.darkColorScheme=l,null==k||k.set(l)):console.error("`".concat(l,"` does not exist in `theme.colorSchemes`."))),r}):w(e=>(null==b||b.set(n),null==k||k.set(o),{...e,lightColorScheme:n,darkColorScheme:o}))},[g,b,k,n,o]),E=r.useCallback(e=>{"system"===x.mode&&w(t=>{let n=(null==e?void 0:e.matches)?"dark":"light";return t.systemMode===n?t:{...t,systemMode:n}})},[x.mode]),_=r.useRef(E);return _.current=E,r.useEffect(()=>{if("function"!=typeof window.matchMedia||!y)return;let e=function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return _.current(...t)},t=window.matchMedia("(prefers-color-scheme: dark)");return t.addListener(e),e(t),()=>{t.removeListener(e)}},[y]),r.useEffect(()=>{if(y){let e=(null==S?void 0:S.subscribe(e=>{(!e||["light","dark","system"].includes(e))&&M(e||t)}))||m,n=(null==b?void 0:b.subscribe(e=>{(!e||g.match(e))&&j({light:e})}))||m,r=(null==k?void 0:k.subscribe(e=>{(!e||g.match(e))&&j({dark:e})}))||m;return()=>{e(),n(),r()}}},[j,M,g,t,a,y,S,b,k]),{...x,mode:$?x.mode:void 0,systemMode:$?x.systemMode:void 0,colorScheme:$?Z:void 0,setMode:M,setColorScheme:j}}({supportedColorSchemes:J,defaultLightColorScheme:X,defaultDarkColorScheme:ee,modeStorageKey:_,colorSchemeStorageKey:W,defaultMode:et,storageManager:A,storageWindow:N,noSsr:T}),eu=en,ec=es;K&&(eu=z.mode,ec=z.colorScheme);let ed=r.useMemo(()=>{var e;let t=ec||F.defaultColorScheme,n=(null===(e=F.generateThemeVars)||void 0===e?void 0:e.call(F))||F.vars,r={...F,components:U,colorSchemes:H,cssVarPrefix:Q,vars:n};if("function"==typeof r.generateSpacing&&(r.spacing=r.generateSpacing()),t){let e=H[t];e&&"object"==typeof e&&Object.keys(e).forEach(t=>{e[t]&&"object"==typeof e[t]?r[t]={...r[t],...e[t]}:r[t]=e[t]})}return b?b(r):r},[F,ec,U,H,Q]),ef=F.colorSchemeSelector;(0,l.Z)(()=>{if(ec&&L&&ef&&"media"!==ef){let e=ef;if("class"===ef&&(e=".%s"),"data"===ef&&(e="[data-%s]"),(null==ef?void 0:ef.startsWith("data-"))&&!ef.includes("%s")&&(e="[".concat(ef,'="%s"]')),e.startsWith("."))L.classList.remove(...J.map(t=>e.substring(1).replace("%s",t))),L.classList.add(e.substring(1).replace("%s",ec));else{let t=e.replace("%s",ec).match(/\[([^\]]+)\]/);if(t){let[e,n]=t[1].split("=");n||J.forEach(t=>{L.removeAttribute(e.replace(ec,t))}),L.setAttribute(e,n?n.replace(/"|'/g,""):"")}else L.setAttribute(e,ec)}}},[ec,ef,L,J]),r.useEffect(()=>{let e;if(I&&V.current&&P){let t=P.createElement("style");t.appendChild(P.createTextNode(v)),P.head.appendChild(t),window.getComputedStyle(P.body),e=setTimeout(()=>{P.head.removeChild(t)},1)}return()=>{clearTimeout(e)}},[ec,I,P]),r.useEffect(()=>(V.current=!0,()=>{V.current=!1}),[]);let em=r.useMemo(()=>({allColorSchemes:J,colorScheme:ec,darkColorScheme:el,lightColorScheme:ei,mode:eu,setColorScheme:ea,setMode:er,systemMode:eo}),[J,ec,el,ei,eu,ea,er,eo,ed.colorSchemeSelector]),eh=!0;(G||!1===F.cssVariables||K&&(null==B?void 0:B.cssVarPrefix)===Q)&&(eh=!1);let ep=(0,a.jsxs)(r.Fragment,{children:[(0,a.jsx)(s.default,{themeId:q?t:void 0,theme:ed,children:j}),eh&&(0,a.jsx)(o.default,{styles:(null===(M=ed.generateStyleSheets)||void 0===M?void 0:M.call(ed))||[]})]});return K?ep:(0,a.jsx)(x.Provider,{value:em,children:ep})},useColorScheme:()=>r.useContext(x)||k,getInitColorSchemeScript:e=>(function(e){let{defaultMode:t="system",defaultLightColorScheme:n="light",defaultDarkColorScheme:r="dark",modeStorageKey:o=u,colorSchemeStorageKey:i=c,attribute:l="data-color-scheme",colorSchemeNode:s="document.documentElement",nonce:d}=e||{},f="",m=l;if("class"===l&&(m=".%s"),"data"===l&&(m="[data-%s]"),m.startsWith(".")){let e=m.substring(1);f+=`${s}.classList.remove('${e}'.replace('%s', light), '${e}'.replace('%s', dark));
      ${s}.classList.add('${e}'.replace('%s', colorScheme));`}let h=m.match(/\[([^\]]+)\]/);if(h){let[e,t]=h[1].split("=");t||(f+=`${s}.removeAttribute('${e}'.replace('%s', light));
      ${s}.removeAttribute('${e}'.replace('%s', dark));`),f+=`
      ${s}.setAttribute('${e}'.replace('%s', colorScheme), ${t?`${t}.replace('%s', colorScheme)`:'""'});`}else f+=`${s}.setAttribute('${m}', colorScheme);`;return(0,a.jsx)("script",{suppressHydrationWarning:!0,nonce:"undefined"==typeof window?d:"",dangerouslySetInnerHTML:{__html:`(function() {
try {
  let colorScheme = '';
  const mode = localStorage.getItem('${o}') || '${t}';
  const dark = localStorage.getItem('${i}-dark') || '${r}';
  const light = localStorage.getItem('${i}-light') || '${n}';
  if (mode === 'system') {
    // handle system mode
    const mql = window.matchMedia('(prefers-color-scheme: dark)');
    if (mql.matches) {
      colorScheme = dark
    } else {
      colorScheme = light
    }
  }
  if (mode === 'light') {
    colorScheme = light;
  }
  if (mode === 'dark') {
    colorScheme = dark;
  }
  if (colorScheme) {
    ${f}
  }
} catch(e){}})();`}},"mui-color-scheme-init")})({colorSchemeStorageKey:g,defaultLightColorScheme:C,defaultDarkColorScheme:Z,modeStorageKey:d,...e})}}},9084:function(e,t,n){"use strict";let r=(0,n(20825).ZP)();t.Z=r},82156:function(e,t,n){"use strict";n.r(t),n.d(t,{unstable_createUseMediaQuery:function(){return u}});var r,o=n(2265),i=n(3450),l=n(8675),s=n(80184);let a={...r||(r=n.t(o,2))}.useSyncExternalStore;function u(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},{themeId:t}=e;return function(e){let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=(0,s.default)();r&&t&&(r=r[t]||r);let u="undefined"!=typeof window&&void 0!==window.matchMedia,{defaultMatches:c=!1,matchMedia:d=u?window.matchMedia:null,ssrMatchMedia:f=null,noSsr:m=!1}=(0,l.Z)({name:"MuiUseMediaQuery",props:n,theme:r}),h="function"==typeof e?e(r):e;return(void 0!==a?function(e,t,n,r,i){let l=o.useCallback(()=>t,[t]),s=o.useMemo(()=>{if(i&&n)return()=>n(e).matches;if(null!==r){let{matches:t}=r(e);return()=>t}return l},[l,e,r,i,n]),[u,c]=o.useMemo(()=>{if(null===n)return[l,()=>()=>{}];let t=n(e);return[()=>t.matches,e=>(t.addEventListener("change",e),()=>{t.removeEventListener("change",e)})]},[l,n,e]);return a(c,u,s)}:function(e,t,n,r,l){let[s,a]=o.useState(()=>l&&n?n(e).matches:r?r(e).matches:t);return(0,i.Z)(()=>{if(!n)return;let t=n(e),r=()=>{a(t.matches)};return r(),t.addEventListener("change",r),()=>{t.removeEventListener("change",r)}},[e,n]),s})(h=h.replace(/^@media( ?)/m,""),c,d,f,m)}}let c=u();t.default=c},8675:function(e,t,n){"use strict";n.d(t,{Z:function(){return o}});var r=n(53232);function o(e){let{theme:t,name:n,props:o}=e;return t&&t.components&&t.components[n]&&t.components[n].defaultProps?(0,r.Z)(t.components[n].defaultProps,o):o}},94873:function(e,t,n){"use strict";n.d(t,{default:function(){return i}});var r=n(8675),o=n(20135);function i(e){let{props:t,name:n,defaultTheme:i,themeId:l}=e,s=(0,o.default)(i);return l&&(s=s[l]||s),(0,r.Z)({theme:s,name:n,props:t})}},94378:function(e,t,n){"use strict";n.d(t,{Z:function(){return o}});var r=n(2265);function o(e,t){return r.isValidElement(e)&&-1!==t.indexOf(e.type.muiName??e.type?._payload?.value?.muiName)}},3450:function(e,t,n){"use strict";var r=n(2265);let o="undefined"!=typeof window?r.useLayoutEffect:r.useEffect;t.Z=o},14811:function(e){e.exports={style:{fontFamily:"'__Inter_e8ce0c', '__Inter_Fallback_e8ce0c'",fontStyle:"normal"},className:"__className_e8ce0c"}}}]);