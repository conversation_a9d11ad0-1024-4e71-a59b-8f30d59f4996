"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5244],{45244:function(e,t,r){var o=r(57437),n=r(2265),s=r(33145),a=r(68575),i=r(76792),l=r(27345),d=r(70623);t.Z=e=>{let{isOpen:t,onClose:r}=e,p=(0,a.I0)(),c=(0,n.useRef)(null),[f,u]=(0,n.useState)(""),x=(0,a.v9)(e=>(0,d.MP)(e)),g=(0,a.v9)(e=>e.user._id),h=(0,a.v9)(e=>e.user.userProfile),b=(0,a.v9)(e=>e.qHarmonyBot.chatHistory),m=(0,a.v9)(e=>e.qHarmonyBot.isLoading),y=e=>{if(""===e.trim())return;let t={sender:"user",message:e.replace(/\n/g,"<br/>"),timestamp:new Date().toISOString()};p((0,i.Hz)(t)),p((0,i.wt)(!0)),(0,l.b)(p,e,g,x),u("")},w=e=>{if(!e)return"";let[t,r]=e.split(" ");return"".concat(t[0].toUpperCase()).concat(r?r[0].toUpperCase():"")},S=()=>{var e;null===(e=c.current)||void 0===e||e.scrollIntoView({behavior:"smooth"})};(0,n.useEffect)(()=>{t&&0===b.length&&h.name&&(p((0,i.wt)(!0)),setTimeout(()=>{let e={sender:"bot",message:"Hey ".concat(h.name,", how can I help you with your benefits enrollment today?"),timestamp:new Date().toISOString()};p((0,i.Hz)(e)),p((0,i.wt)(!1))},1e3))},[t,b.length,h.name,p]),(0,n.useEffect)(()=>{S()},[b]);let v=["Explain my plan options","Help me choose coverage","What are the costs?","Enrollment deadline"];return t?(0,o.jsx)("div",{style:{position:"fixed",top:0,left:0,right:0,bottom:0,backgroundColor:"rgba(0, 0, 0, 0.5)",zIndex:1e4,display:"flex",alignItems:"center",justifyContent:"center",padding:"20px"},children:(0,o.jsxs)("div",{style:{backgroundColor:"#f6f8fc",borderRadius:"12px",width:"100%",maxWidth:"800px",height:"600px",display:"flex",flexDirection:"column",boxShadow:"0 20px 60px rgba(0, 0, 0, 0.3)",overflow:"hidden"},children:[(0,o.jsxs)("div",{style:{backgroundColor:"#ffffff",padding:"16px 24px",borderBottom:"1px solid #e5e7eb",display:"flex",alignItems:"center",justifyContent:"space-between"},children:[(0,o.jsxs)("div",{style:{display:"flex",alignItems:"center",gap:"12px"},children:[(0,o.jsx)(s.default,{src:"/brea.png",alt:"Brea",width:40,height:40,style:{borderRadius:"50%"}}),(0,o.jsxs)("div",{children:[(0,o.jsx)("h3",{style:{margin:0,fontSize:"18px",fontWeight:"600",color:"#111827"},children:"Chat with Brea"}),(0,o.jsx)("p",{style:{margin:0,fontSize:"14px",color:"#6b7280"},children:"Your Benefits Specialist"})]})]}),(0,o.jsx)("button",{onClick:r,style:{background:"none",border:"none",fontSize:"24px",cursor:"pointer",color:"#6b7280",padding:"4px",borderRadius:"4px",display:"flex",alignItems:"center",justifyContent:"center"},children:"\xd7"})]}),(0,o.jsxs)("div",{style:{flex:1,overflow:"auto",padding:"16px",display:"flex",flexDirection:"column",gap:"12px"},children:[b.map((e,t)=>(0,o.jsxs)("div",{style:{display:"flex",flexDirection:"user"===e.sender?"row-reverse":"row",alignItems:"flex-start",gap:"8px"},children:[(0,o.jsx)("div",{style:{width:"32px",height:"32px",borderRadius:"50%",display:"flex",alignItems:"center",justifyContent:"center",backgroundColor:"user"===e.sender?"#000000":"transparent",color:"white",fontSize:"14px",fontWeight:"600",flexShrink:0},children:"user"===e.sender?w(h.name):(0,o.jsx)(s.default,{src:"/brea.png",alt:"Brea",width:32,height:32,style:{borderRadius:"50%"}})}),(0,o.jsxs)("div",{style:{maxWidth:"70%",backgroundColor:"user"===e.sender?"#000000":"#ffffff",color:"user"===e.sender?"#ffffff":"#000000",padding:"12px 16px",borderRadius:"user"===e.sender?"16px 16px 4px 16px":"16px 16px 16px 4px",fontSize:"14px",lineHeight:"1.5",boxShadow:"0 1px 3px rgba(0, 0, 0, 0.1)"},children:[(0,o.jsx)("div",{dangerouslySetInnerHTML:{__html:"bot"===e.sender?"".concat(e.message,'<br/><small style="color: #6b7280; font-size: 12px;">AI-generated content—verify before use.</small>'):e.message},style:{whiteSpace:"pre-wrap",wordBreak:"break-word"}}),"bot"===e.sender&&e.message.includes("how can I help you")&&t===b.length-1&&(0,o.jsx)("div",{style:{display:"flex",flexWrap:"wrap",gap:"8px",marginTop:"12px"},children:v.map(e=>(0,o.jsx)("button",{onClick:()=>y(e),style:{padding:"6px 12px",backgroundColor:"#f3f4f6",color:"#374151",border:"1px solid #d1d5db",borderRadius:"16px",fontSize:"12px",cursor:"pointer",transition:"all 0.2s"},onMouseOver:e=>{e.currentTarget.style.backgroundColor="#e5e7eb"},onMouseOut:e=>{e.currentTarget.style.backgroundColor="#f3f4f6"},children:e},e))})]})]},t)),m&&(0,o.jsxs)("div",{style:{display:"flex",alignItems:"flex-start",gap:"8px"},children:[(0,o.jsx)(s.default,{src:"/brea.png",alt:"Brea",width:32,height:32,style:{borderRadius:"50%"}}),(0,o.jsx)("div",{style:{backgroundColor:"#ffffff",padding:"12px 16px",borderRadius:"16px 16px 16px 4px",fontSize:"14px",color:"#6b7280",boxShadow:"0 1px 3px rgba(0, 0, 0, 0.1)"},children:"Brea is typing..."})]}),(0,o.jsx)("div",{ref:c})]}),(0,o.jsx)("div",{style:{backgroundColor:"#ffffff",padding:"16px",borderTop:"1px solid #e5e7eb"},children:(0,o.jsxs)("div",{style:{display:"flex",gap:"12px",alignItems:"flex-end"},children:[(0,o.jsx)("textarea",{value:f,onChange:e=>u(e.target.value),onKeyPress:e=>{"Enter"!==e.key||e.shiftKey||(e.preventDefault(),y(f))},placeholder:"Type your message...",style:{flex:1,padding:"12px",border:"1px solid #d1d5db",borderRadius:"8px",fontSize:"14px",resize:"none",minHeight:"44px",maxHeight:"120px",outline:"none",fontFamily:"inherit",color:"#000000",backgroundColor:"#ffffff"},rows:1}),(0,o.jsx)("button",{onClick:()=>y(f),disabled:!f.trim(),style:{padding:"12px 20px",backgroundColor:f.trim()?"#000000":"#e5e7eb",color:f.trim()?"#ffffff":"#9ca3af",border:"none",borderRadius:"8px",fontSize:"14px",fontWeight:"500",cursor:f.trim()?"pointer":"not-allowed",transition:"all 0.2s"},children:"Send"})]})})]})}):null}},27345:function(e,t,r){r.d(t,{b:function(){return s}});var o=r(76792);let n=(0,r(61103).GU)();async function s(e,t,r,s){let a={user_id:r,user_message:t,team_id:s};try{console.log("Sending chat message:",a);let t=await fetch("".concat(n,"/chat"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(a)});if(!t.body)throw Error("Readable stream not supported");let r=t.body.getReader(),s=new TextDecoder("utf-8"),i={sender:"bot",message:"",timestamp:new Date().toISOString()};for(e((0,o.wt)(!0));;){let{done:t,value:n}=await r.read();if(t)break;let a=s.decode(n,{stream:!0});e((0,o.wt)(!1)),console.log("Chunk:",a),i.message+=a,e((0,o.Hz)({sender:"bot",message:a,timestamp:new Date().toISOString()}))}}catch(r){console.error("Error sending chat message:",r);let t={sender:"bot",message:"Sorry, I encountered an error while processing your request.",timestamp:new Date().toISOString()};e((0,o.Hz)(t)),e((0,o.wt)(!1))}}},76792:function(e,t,r){r.d(t,{Hz:function(){return n},wt:function(){return a}});let o=(0,r(39129).oM)({name:"qHarmonyBot",initialState:{chatHistory:[],isLoading:!1},reducers:{addMessage:(e,t)=>{let{sender:r,message:o,timestamp:n}=t.payload;if(console.log("Adding message:",t.payload),"bot"===r&&e.chatHistory.length>0){let t=e.chatHistory[e.chatHistory.length-1];if("bot"===t.sender&&!t.timestamp.includes("Done")){t.message+=o,t.timestamp=n;return}}e.chatHistory.push(t.payload)},clearChatHistory:e=>{e.chatHistory=[]},setIsLoading:(e,t)=>{e.isLoading=t.payload}}}),{addMessage:n,clearChatHistory:s,setIsLoading:a}=o.actions;t.ZP=o.reducer},61103:function(e,t,r){r.d(t,{GU:function(){return s},bR:function(){return o},n5:function(){return n}});let o=()=>"http://localhost:8080",n=()=>{let e="userid1",t="userId",r=localStorage.getItem(e)||localStorage.getItem(t);return(console.log("\uD83D\uDD0D getUserId debug:",{primaryKey:e,altKey:t,primaryValue:localStorage.getItem(e),altValue:localStorage.getItem(t),finalUserId:r}),r)?r:(console.error("❌ User ID not found in localStorage"),"default-user")},s=()=>"https://bot.benosphere.com"}}]);