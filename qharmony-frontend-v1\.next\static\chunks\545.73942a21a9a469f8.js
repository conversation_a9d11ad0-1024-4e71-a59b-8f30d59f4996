(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[545],{5478:function(e,t,r){"use strict";r.d(t,{Ry:function(){return c}});var n=new WeakMap,i=new WeakMap,a={},o=0,u=function(e){return e&&(e.host||u(e.parentNode))},l=function(e,t,r,l){var c=(Array.isArray(e)?e:[e]).map(function(e){if(t.contains(e))return e;var r=u(e);return r&&t.contains(r)?r:(console.error("aria-hidden",e,"in not contained inside",t,". Doing nothing"),null)}).filter(function(e){return!!e});a[r]||(a[r]=new WeakMap);var s=a[r],f=[],d=new Set,p=new Set(c),h=function(e){!e||d.has(e)||(d.add(e),h(e.parentNode))};c.forEach(h);var v=function(e){!e||p.has(e)||Array.prototype.forEach.call(e.children,function(e){if(d.has(e))v(e);else try{var t=e.getAttribute(l),a=null!==t&&"false"!==t,o=(n.get(e)||0)+1,u=(s.get(e)||0)+1;n.set(e,o),s.set(e,u),f.push(e),1===o&&a&&i.set(e,!0),1===u&&e.setAttribute(r,"true"),a||e.setAttribute(l,"true")}catch(t){console.error("aria-hidden: cannot operate on ",e,t)}})};return v(t),d.clear(),o++,function(){f.forEach(function(e){var t=n.get(e)-1,a=s.get(e)-1;n.set(e,t),s.set(e,a),t||(i.has(e)||e.removeAttribute(l),i.delete(e)),a||e.removeAttribute(r)}),--o||(n=new WeakMap,n=new WeakMap,i=new WeakMap,a={})}},c=function(e,t,r){void 0===r&&(r="data-aria-hidden");var n=Array.from(Array.isArray(e)?e:[e]),i=t||("undefined"==typeof document?null:(Array.isArray(e)?e[0]:e).ownerDocument.body);return i?(n.push.apply(n,Array.from(i.querySelectorAll("[aria-live], script"))),l(n,i,r,"aria-hidden")):function(){return null}}},61134:function(e,t,r){var n;!function(i){"use strict";var a,o={precision:20,rounding:4,toExpNeg:-7,toExpPos:21,LN10:"2.302585092994045684017991454684364207601101488628772976033327900967572609677352480235997205089598298341967784042286"},u=!0,l="[DecimalError] ",c=l+"Invalid argument: ",s=l+"Exponent out of range: ",f=Math.floor,d=Math.pow,p=/^(\d+(\.\d*)?|\.\d+)(e[+-]?\d+)?$/i,h=f(1286742750677284.5),v={};function y(e,t){var r,n,i,a,o,l,c,s,f=e.constructor,d=f.precision;if(!e.s||!t.s)return t.s||(t=new f(e)),u?M(t,d):t;if(c=e.d,s=t.d,o=e.e,i=t.e,c=c.slice(),a=o-i){for(a<0?(n=c,a=-a,l=s.length):(n=s,i=o,l=c.length),a>(l=(o=Math.ceil(d/7))>l?o+1:l+1)&&(a=l,n.length=1),n.reverse();a--;)n.push(0);n.reverse()}for((l=c.length)-(a=s.length)<0&&(a=l,n=s,s=c,c=n),r=0;a;)r=(c[--a]=c[a]+s[a]+r)/1e7|0,c[a]%=1e7;for(r&&(c.unshift(r),++i),l=c.length;0==c[--l];)c.pop();return t.d=c,t.e=i,u?M(t,d):t}function g(e,t,r){if(e!==~~e||e<t||e>r)throw Error(c+e)}function m(e){var t,r,n,i=e.length-1,a="",o=e[0];if(i>0){for(a+=o,t=1;t<i;t++)(r=7-(n=e[t]+"").length)&&(a+=j(r)),a+=n;(r=7-(n=(o=e[t])+"").length)&&(a+=j(r))}else if(0===o)return"0";for(;o%10==0;)o/=10;return a+o}v.absoluteValue=v.abs=function(){var e=new this.constructor(this);return e.s&&(e.s=1),e},v.comparedTo=v.cmp=function(e){var t,r,n,i;if(e=new this.constructor(e),this.s!==e.s)return this.s||-e.s;if(this.e!==e.e)return this.e>e.e^this.s<0?1:-1;for(t=0,r=(n=this.d.length)<(i=e.d.length)?n:i;t<r;++t)if(this.d[t]!==e.d[t])return this.d[t]>e.d[t]^this.s<0?1:-1;return n===i?0:n>i^this.s<0?1:-1},v.decimalPlaces=v.dp=function(){var e=this.d.length-1,t=(e-this.e)*7;if(e=this.d[e])for(;e%10==0;e/=10)t--;return t<0?0:t},v.dividedBy=v.div=function(e){return b(this,new this.constructor(e))},v.dividedToIntegerBy=v.idiv=function(e){var t=this.constructor;return M(b(this,new t(e),0,1),t.precision)},v.equals=v.eq=function(e){return!this.cmp(e)},v.exponent=function(){return w(this)},v.greaterThan=v.gt=function(e){return this.cmp(e)>0},v.greaterThanOrEqualTo=v.gte=function(e){return this.cmp(e)>=0},v.isInteger=v.isint=function(){return this.e>this.d.length-2},v.isNegative=v.isneg=function(){return this.s<0},v.isPositive=v.ispos=function(){return this.s>0},v.isZero=function(){return 0===this.s},v.lessThan=v.lt=function(e){return 0>this.cmp(e)},v.lessThanOrEqualTo=v.lte=function(e){return 1>this.cmp(e)},v.logarithm=v.log=function(e){var t,r=this.constructor,n=r.precision,i=n+5;if(void 0===e)e=new r(10);else if((e=new r(e)).s<1||e.eq(a))throw Error(l+"NaN");if(this.s<1)throw Error(l+(this.s?"NaN":"-Infinity"));return this.eq(a)?new r(0):(u=!1,t=b(P(this,i),P(e,i),i),u=!0,M(t,n))},v.minus=v.sub=function(e){return e=new this.constructor(e),this.s==e.s?S(this,e):y(this,(e.s=-e.s,e))},v.modulo=v.mod=function(e){var t,r=this.constructor,n=r.precision;if(!(e=new r(e)).s)throw Error(l+"NaN");return this.s?(u=!1,t=b(this,e,0,1).times(e),u=!0,this.minus(t)):M(new r(this),n)},v.naturalExponential=v.exp=function(){return x(this)},v.naturalLogarithm=v.ln=function(){return P(this)},v.negated=v.neg=function(){var e=new this.constructor(this);return e.s=-e.s||0,e},v.plus=v.add=function(e){return e=new this.constructor(e),this.s==e.s?y(this,e):S(this,(e.s=-e.s,e))},v.precision=v.sd=function(e){var t,r,n;if(void 0!==e&&!!e!==e&&1!==e&&0!==e)throw Error(c+e);if(t=w(this)+1,r=7*(n=this.d.length-1)+1,n=this.d[n]){for(;n%10==0;n/=10)r--;for(n=this.d[0];n>=10;n/=10)r++}return e&&t>r?t:r},v.squareRoot=v.sqrt=function(){var e,t,r,n,i,a,o,c=this.constructor;if(this.s<1){if(!this.s)return new c(0);throw Error(l+"NaN")}for(e=w(this),u=!1,0==(i=Math.sqrt(+this))||i==1/0?(((t=m(this.d)).length+e)%2==0&&(t+="0"),i=Math.sqrt(t),e=f((e+1)/2)-(e<0||e%2),n=new c(t=i==1/0?"5e"+e:(t=i.toExponential()).slice(0,t.indexOf("e")+1)+e)):n=new c(i.toString()),i=o=(r=c.precision)+3;;)if(n=(a=n).plus(b(this,a,o+2)).times(.5),m(a.d).slice(0,o)===(t=m(n.d)).slice(0,o)){if(t=t.slice(o-3,o+1),i==o&&"4999"==t){if(M(a,r+1,0),a.times(a).eq(this)){n=a;break}}else if("9999"!=t)break;o+=4}return u=!0,M(n,r)},v.times=v.mul=function(e){var t,r,n,i,a,o,l,c,s,f=this.constructor,d=this.d,p=(e=new f(e)).d;if(!this.s||!e.s)return new f(0);for(e.s*=this.s,r=this.e+e.e,(c=d.length)<(s=p.length)&&(a=d,d=p,p=a,o=c,c=s,s=o),a=[],n=o=c+s;n--;)a.push(0);for(n=s;--n>=0;){for(t=0,i=c+n;i>n;)l=a[i]+p[n]*d[i-n-1]+t,a[i--]=l%1e7|0,t=l/1e7|0;a[i]=(a[i]+t)%1e7|0}for(;!a[--o];)a.pop();return t?++r:a.shift(),e.d=a,e.e=r,u?M(e,f.precision):e},v.toDecimalPlaces=v.todp=function(e,t){var r=this,n=r.constructor;return(r=new n(r),void 0===e)?r:(g(e,0,1e9),void 0===t?t=n.rounding:g(t,0,8),M(r,e+w(r)+1,t))},v.toExponential=function(e,t){var r,n=this,i=n.constructor;return void 0===e?r=A(n,!0):(g(e,0,1e9),void 0===t?t=i.rounding:g(t,0,8),r=A(n=M(new i(n),e+1,t),!0,e+1)),r},v.toFixed=function(e,t){var r,n,i=this.constructor;return void 0===e?A(this):(g(e,0,1e9),void 0===t?t=i.rounding:g(t,0,8),r=A((n=M(new i(this),e+w(this)+1,t)).abs(),!1,e+w(n)+1),this.isneg()&&!this.isZero()?"-"+r:r)},v.toInteger=v.toint=function(){var e=this.constructor;return M(new e(this),w(this)+1,e.rounding)},v.toNumber=function(){return+this},v.toPower=v.pow=function(e){var t,r,n,i,o,c,s=this,d=s.constructor,p=+(e=new d(e));if(!e.s)return new d(a);if(!(s=new d(s)).s){if(e.s<1)throw Error(l+"Infinity");return s}if(s.eq(a))return s;if(n=d.precision,e.eq(a))return M(s,n);if(c=(t=e.e)>=(r=e.d.length-1),o=s.s,c){if((r=p<0?-p:p)<=9007199254740991){for(i=new d(a),t=Math.ceil(n/7+4),u=!1;r%2&&E((i=i.times(s)).d,t),0!==(r=f(r/2));)E((s=s.times(s)).d,t);return u=!0,e.s<0?new d(a).div(i):M(i,n)}}else if(o<0)throw Error(l+"NaN");return o=o<0&&1&e.d[Math.max(t,r)]?-1:1,s.s=1,u=!1,i=e.times(P(s,n+12)),u=!0,(i=x(i)).s=o,i},v.toPrecision=function(e,t){var r,n,i=this,a=i.constructor;return void 0===e?(r=w(i),n=A(i,r<=a.toExpNeg||r>=a.toExpPos)):(g(e,1,1e9),void 0===t?t=a.rounding:g(t,0,8),r=w(i=M(new a(i),e,t)),n=A(i,e<=r||r<=a.toExpNeg,e)),n},v.toSignificantDigits=v.tosd=function(e,t){var r=this.constructor;return void 0===e?(e=r.precision,t=r.rounding):(g(e,1,1e9),void 0===t?t=r.rounding:g(t,0,8)),M(new r(this),e,t)},v.toString=v.valueOf=v.val=v.toJSON=function(){var e=w(this),t=this.constructor;return A(this,e<=t.toExpNeg||e>=t.toExpPos)};var b=function(){function e(e,t){var r,n=0,i=e.length;for(e=e.slice();i--;)r=e[i]*t+n,e[i]=r%1e7|0,n=r/1e7|0;return n&&e.unshift(n),e}function t(e,t,r,n){var i,a;if(r!=n)a=r>n?1:-1;else for(i=a=0;i<r;i++)if(e[i]!=t[i]){a=e[i]>t[i]?1:-1;break}return a}function r(e,t,r){for(var n=0;r--;)e[r]-=n,n=e[r]<t[r]?1:0,e[r]=1e7*n+e[r]-t[r];for(;!e[0]&&e.length>1;)e.shift()}return function(n,i,a,o){var u,c,s,f,d,p,h,v,y,g,m,b,x,O,j,P,k,S,A=n.constructor,E=n.s==i.s?1:-1,C=n.d,T=i.d;if(!n.s)return new A(n);if(!i.s)throw Error(l+"Division by zero");for(s=0,c=n.e-i.e,k=T.length,j=C.length,v=(h=new A(E)).d=[];T[s]==(C[s]||0);)++s;if(T[s]>(C[s]||0)&&--c,(b=null==a?a=A.precision:o?a+(w(n)-w(i))+1:a)<0)return new A(0);if(b=b/7+2|0,s=0,1==k)for(f=0,T=T[0],b++;(s<j||f)&&b--;s++)x=1e7*f+(C[s]||0),v[s]=x/T|0,f=x%T|0;else{for((f=1e7/(T[0]+1)|0)>1&&(T=e(T,f),C=e(C,f),k=T.length,j=C.length),O=k,g=(y=C.slice(0,k)).length;g<k;)y[g++]=0;(S=T.slice()).unshift(0),P=T[0],T[1]>=1e7/2&&++P;do f=0,(u=t(T,y,k,g))<0?(m=y[0],k!=g&&(m=1e7*m+(y[1]||0)),(f=m/P|0)>1?(f>=1e7&&(f=1e7-1),p=(d=e(T,f)).length,g=y.length,1==(u=t(d,y,p,g))&&(f--,r(d,k<p?S:T,p))):(0==f&&(u=f=1),d=T.slice()),(p=d.length)<g&&d.unshift(0),r(y,d,g),-1==u&&(g=y.length,(u=t(T,y,k,g))<1&&(f++,r(y,k<g?S:T,g))),g=y.length):0===u&&(f++,y=[0]),v[s++]=f,u&&y[0]?y[g++]=C[O]||0:(y=[C[O]],g=1);while((O++<j||void 0!==y[0])&&b--)}return v[0]||v.shift(),h.e=c,M(h,o?a+w(h)+1:a)}}();function x(e,t){var r,n,i,o,l,c=0,f=0,p=e.constructor,h=p.precision;if(w(e)>16)throw Error(s+w(e));if(!e.s)return new p(a);for(null==t?(u=!1,l=h):l=t,o=new p(.03125);e.abs().gte(.1);)e=e.times(o),f+=5;for(l+=Math.log(d(2,f))/Math.LN10*2+5|0,r=n=i=new p(a),p.precision=l;;){if(n=M(n.times(e),l),r=r.times(++c),m((o=i.plus(b(n,r,l))).d).slice(0,l)===m(i.d).slice(0,l)){for(;f--;)i=M(i.times(i),l);return p.precision=h,null==t?(u=!0,M(i,h)):i}i=o}}function w(e){for(var t=7*e.e,r=e.d[0];r>=10;r/=10)t++;return t}function O(e,t,r){if(t>e.LN10.sd())throw u=!0,r&&(e.precision=r),Error(l+"LN10 precision limit exceeded");return M(new e(e.LN10),t)}function j(e){for(var t="";e--;)t+="0";return t}function P(e,t){var r,n,i,o,c,s,f,d,p,h=1,v=e,y=v.d,g=v.constructor,x=g.precision;if(v.s<1)throw Error(l+(v.s?"NaN":"-Infinity"));if(v.eq(a))return new g(0);if(null==t?(u=!1,d=x):d=t,v.eq(10))return null==t&&(u=!0),O(g,d);if(d+=10,g.precision=d,n=(r=m(y)).charAt(0),!(15e14>Math.abs(o=w(v))))return f=O(g,d+2,x).times(o+""),v=P(new g(n+"."+r.slice(1)),d-10).plus(f),g.precision=x,null==t?(u=!0,M(v,x)):v;for(;n<7&&1!=n||1==n&&r.charAt(1)>3;)n=(r=m((v=v.times(e)).d)).charAt(0),h++;for(o=w(v),n>1?(v=new g("0."+r),o++):v=new g(n+"."+r.slice(1)),s=c=v=b(v.minus(a),v.plus(a),d),p=M(v.times(v),d),i=3;;){if(c=M(c.times(p),d),m((f=s.plus(b(c,new g(i),d))).d).slice(0,d)===m(s.d).slice(0,d))return s=s.times(2),0!==o&&(s=s.plus(O(g,d+2,x).times(o+""))),s=b(s,new g(h),d),g.precision=x,null==t?(u=!0,M(s,x)):s;s=f,i+=2}}function k(e,t){var r,n,i;for((r=t.indexOf("."))>-1&&(t=t.replace(".","")),(n=t.search(/e/i))>0?(r<0&&(r=n),r+=+t.slice(n+1),t=t.substring(0,n)):r<0&&(r=t.length),n=0;48===t.charCodeAt(n);)++n;for(i=t.length;48===t.charCodeAt(i-1);)--i;if(t=t.slice(n,i)){if(i-=n,r=r-n-1,e.e=f(r/7),e.d=[],n=(r+1)%7,r<0&&(n+=7),n<i){for(n&&e.d.push(+t.slice(0,n)),i-=7;n<i;)e.d.push(+t.slice(n,n+=7));n=7-(t=t.slice(n)).length}else n-=i;for(;n--;)t+="0";if(e.d.push(+t),u&&(e.e>h||e.e<-h))throw Error(s+r)}else e.s=0,e.e=0,e.d=[0];return e}function M(e,t,r){var n,i,a,o,l,c,p,v,y=e.d;for(o=1,a=y[0];a>=10;a/=10)o++;if((n=t-o)<0)n+=7,i=t,p=y[v=0];else{if((v=Math.ceil((n+1)/7))>=(a=y.length))return e;for(o=1,p=a=y[v];a>=10;a/=10)o++;n%=7,i=n-7+o}if(void 0!==r&&(l=p/(a=d(10,o-i-1))%10|0,c=t<0||void 0!==y[v+1]||p%a,c=r<4?(l||c)&&(0==r||r==(e.s<0?3:2)):l>5||5==l&&(4==r||c||6==r&&(n>0?i>0?p/d(10,o-i):0:y[v-1])%10&1||r==(e.s<0?8:7))),t<1||!y[0])return c?(a=w(e),y.length=1,t=t-a-1,y[0]=d(10,(7-t%7)%7),e.e=f(-t/7)||0):(y.length=1,y[0]=e.e=e.s=0),e;if(0==n?(y.length=v,a=1,v--):(y.length=v+1,a=d(10,7-n),y[v]=i>0?(p/d(10,o-i)%d(10,i)|0)*a:0),c)for(;;){if(0==v){1e7==(y[0]+=a)&&(y[0]=1,++e.e);break}if(y[v]+=a,1e7!=y[v])break;y[v--]=0,a=1}for(n=y.length;0===y[--n];)y.pop();if(u&&(e.e>h||e.e<-h))throw Error(s+w(e));return e}function S(e,t){var r,n,i,a,o,l,c,s,f,d,p=e.constructor,h=p.precision;if(!e.s||!t.s)return t.s?t.s=-t.s:t=new p(e),u?M(t,h):t;if(c=e.d,d=t.d,n=t.e,s=e.e,c=c.slice(),o=s-n){for((f=o<0)?(r=c,o=-o,l=d.length):(r=d,n=s,l=c.length),o>(i=Math.max(Math.ceil(h/7),l)+2)&&(o=i,r.length=1),r.reverse(),i=o;i--;)r.push(0);r.reverse()}else{for((f=(i=c.length)<(l=d.length))&&(l=i),i=0;i<l;i++)if(c[i]!=d[i]){f=c[i]<d[i];break}o=0}for(f&&(r=c,c=d,d=r,t.s=-t.s),l=c.length,i=d.length-l;i>0;--i)c[l++]=0;for(i=d.length;i>o;){if(c[--i]<d[i]){for(a=i;a&&0===c[--a];)c[a]=1e7-1;--c[a],c[i]+=1e7}c[i]-=d[i]}for(;0===c[--l];)c.pop();for(;0===c[0];c.shift())--n;return c[0]?(t.d=c,t.e=n,u?M(t,h):t):new p(0)}function A(e,t,r){var n,i=w(e),a=m(e.d),o=a.length;return t?(r&&(n=r-o)>0?a=a.charAt(0)+"."+a.slice(1)+j(n):o>1&&(a=a.charAt(0)+"."+a.slice(1)),a=a+(i<0?"e":"e+")+i):i<0?(a="0."+j(-i-1)+a,r&&(n=r-o)>0&&(a+=j(n))):i>=o?(a+=j(i+1-o),r&&(n=r-i-1)>0&&(a=a+"."+j(n))):((n=i+1)<o&&(a=a.slice(0,n)+"."+a.slice(n)),r&&(n=r-o)>0&&(i+1===o&&(a+="."),a+=j(n))),e.s<0?"-"+a:a}function E(e,t){if(e.length>t)return e.length=t,!0}function C(e){if(!e||"object"!=typeof e)throw Error(l+"Object expected");var t,r,n,i=["precision",1,1e9,"rounding",0,8,"toExpNeg",-1/0,0,"toExpPos",0,1/0];for(t=0;t<i.length;t+=3)if(void 0!==(n=e[r=i[t]])){if(f(n)===n&&n>=i[t+1]&&n<=i[t+2])this[r]=n;else throw Error(c+r+": "+n)}if(void 0!==(n=e[r="LN10"])){if(n==Math.LN10)this[r]=new this(n);else throw Error(c+r+": "+n)}return this}(o=function e(t){var r,n,i;function a(e){if(!(this instanceof a))return new a(e);if(this.constructor=a,e instanceof a){this.s=e.s,this.e=e.e,this.d=(e=e.d)?e.slice():e;return}if("number"==typeof e){if(0*e!=0)throw Error(c+e);if(e>0)this.s=1;else if(e<0)e=-e,this.s=-1;else{this.s=0,this.e=0,this.d=[0];return}if(e===~~e&&e<1e7){this.e=0,this.d=[e];return}return k(this,e.toString())}if("string"!=typeof e)throw Error(c+e);if(45===e.charCodeAt(0)?(e=e.slice(1),this.s=-1):this.s=1,p.test(e))k(this,e);else throw Error(c+e)}if(a.prototype=v,a.ROUND_UP=0,a.ROUND_DOWN=1,a.ROUND_CEIL=2,a.ROUND_FLOOR=3,a.ROUND_HALF_UP=4,a.ROUND_HALF_DOWN=5,a.ROUND_HALF_EVEN=6,a.ROUND_HALF_CEIL=7,a.ROUND_HALF_FLOOR=8,a.clone=e,a.config=a.set=C,void 0===t&&(t={}),t)for(r=0,i=["precision","rounding","toExpNeg","toExpPos","LN10"];r<i.length;)t.hasOwnProperty(n=i[r++])||(t[n]=this[n]);return a.config(t),a}(o)).default=o.Decimal=o,a=new o(1),void 0!==(n=(function(){return o}).call(t,r,t,e))&&(e.exports=n)}(0)},15870:function(e,t,r){e.exports=r(84073).get},38450:function(e,t,r){e.exports=r(7972).isEqual},46802:function(e,t,r){e.exports=r(49483).isPlainObject},37618:function(e,t,r){e.exports=r(17858).last},41664:function(e,t,r){e.exports=r(11480).range},31104:function(e,t,r){e.exports=r(26273).sortBy},34926:function(e,t,r){e.exports=r(9313).throttle},36841:function(e,t,r){e.exports=r(62992).uniqBy},95491:function(e,t){"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isUnsafeProperty=function(e){return"__proto__"===e}},949:function(e,t){"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.flatten=function(e,t=1){let r=[],n=Math.floor(t),i=(e,t)=>{for(let a=0;a<e.length;a++){let o=e[a];Array.isArray(o)&&t<n?i(o,t+1):r.push(o)}};return i(e,0),r}},68509:function(e,t){"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.last=function(e){return e[e.length-1]}},65745:function(e,t){"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.uniqBy=function(e,t){let r=new Map;for(let n=0;n<e.length;n++){let i=e[n],a=t(i);r.has(a)||r.set(a,i)}return Array.from(r.values())}},1260:function(e,t){"use strict";function r(e){return"symbol"==typeof e?1:null===e?2:void 0===e?3:e!=e?4:0}Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.compareValues=(e,t,n)=>{if(e!==t){let i=r(e),a=r(t);if(i===a&&0===i){if(e<t)return"desc"===n?1:-1;if(e>t)return"desc"===n?-1:1}return"desc"===n?a-i:i-a}return 0}},59924:function(e,t){"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.getSymbols=function(e){return Object.getOwnPropertySymbols(e).filter(t=>Object.prototype.propertyIsEnumerable.call(e,t))}},83984:function(e,t){"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.getTag=function(e){return null==e?void 0===e?"[object Undefined]":"[object Null]":Object.prototype.toString.call(e)}},35159:function(e,t){"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isDeepKey=function(e){switch(typeof e){case"number":case"symbol":return!1;case"string":return e.includes(".")||e.includes("[")||e.includes("]")}}},84623:function(e,t){"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let r=/^(?:0|[1-9]\d*)$/;t.isIndex=function(e,t=Number.MAX_SAFE_INTEGER){switch(typeof e){case"number":return Number.isInteger(e)&&e>=0&&e<t;case"symbol":return!1;case"string":return r.test(e)}}},34893:function(e,t,r){"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(84623),i=r(98934),a=r(13400),o=r(95037);t.isIterateeCall=function(e,t,r){return!!a.isObject(r)&&(!!("number"==typeof t&&i.isArrayLike(r)&&n.isIndex(t))&&t<r.length||"string"==typeof t&&t in r)&&o.eq(r[t],e)}},45799:function(e,t,r){"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(74675),i=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,a=/^\w*$/;t.isKey=function(e,t){return!Array.isArray(e)&&(!!("number"==typeof e||"boolean"==typeof e||null==e||n.isSymbol(e))||"string"==typeof e&&(a.test(e)||!i.test(e))||null!=t&&Object.hasOwn(t,e))}},91116:function(e,t){"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.argumentsTag="[object Arguments]",t.arrayBufferTag="[object ArrayBuffer]",t.arrayTag="[object Array]",t.bigInt64ArrayTag="[object BigInt64Array]",t.bigUint64ArrayTag="[object BigUint64Array]",t.booleanTag="[object Boolean]",t.dataViewTag="[object DataView]",t.dateTag="[object Date]",t.errorTag="[object Error]",t.float32ArrayTag="[object Float32Array]",t.float64ArrayTag="[object Float64Array]",t.functionTag="[object Function]",t.int16ArrayTag="[object Int16Array]",t.int32ArrayTag="[object Int32Array]",t.int8ArrayTag="[object Int8Array]",t.mapTag="[object Map]",t.numberTag="[object Number]",t.objectTag="[object Object]",t.regexpTag="[object RegExp]",t.setTag="[object Set]",t.stringTag="[object String]",t.symbolTag="[object Symbol]",t.uint16ArrayTag="[object Uint16Array]",t.uint32ArrayTag="[object Uint32Array]",t.uint8ArrayTag="[object Uint8Array]",t.uint8ClampedArrayTag="[object Uint8ClampedArray]"},77003:function(e,t){"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.toArray=function(e){return Array.isArray(e)?e:Array.from(e)}},49670:function(e,t){"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.toKey=function(e){return"string"==typeof e||"symbol"==typeof e?e:Object.is(e?.valueOf?.(),-0)?"-0":String(e)}},17858:function(e,t,r){"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(68509),i=r(77003),a=r(98934);t.last=function(e){if(a.isArrayLike(e))return n.last(i.toArray(e))}},46688:function(e,t,r){"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(1260),i=r(45799),a=r(64161);t.orderBy=function(e,t,r,o){if(null==e)return[];r=o?void 0:r,Array.isArray(e)||(e=Object.values(e)),Array.isArray(t)||(t=null==t?[null]:[t]),0===t.length&&(t=[null]),Array.isArray(r)||(r=null==r?[]:[r]),r=r.map(e=>String(e));let u=(e,t)=>{let r=e;for(let e=0;e<t.length&&null!=r;++e)r=r[t[e]];return r},l=(e,t)=>null==t||null==e?t:"object"==typeof e&&"key"in e?Object.hasOwn(t,e.key)?t[e.key]:u(t,e.path):"function"==typeof e?e(t):Array.isArray(e)?u(t,e):"object"==typeof t?t[e]:t,c=t.map(e=>(Array.isArray(e)&&1===e.length&&(e=e[0]),null==e||"function"==typeof e||Array.isArray(e)||i.isKey(e))?e:{key:e,path:a.toPath(e)});return e.map(e=>({original:e,criteria:c.map(t=>l(t,e))})).slice().sort((e,t)=>{for(let i=0;i<c.length;i++){let a=n.compareValues(e.criteria[i],t.criteria[i],r[i]);if(0!==a)return a}return 0}).map(e=>e.original)}},26273:function(e,t,r){"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(46688),i=r(949),a=r(34893);t.sortBy=function(e,...t){let r=t.length;return r>1&&a.isIterateeCall(e,t[0],t[1])?t=[]:r>2&&a.isIterateeCall(t[0],t[1],t[2])&&(t=[t[0]]),n.orderBy(e,i.flatten(t),["asc"])}},62992:function(e,t,r){"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(65745),i=r(50601),a=r(79311),o=r(55586);t.uniqBy=function(e,t=i.identity){return a.isArrayLikeObject(e)?n.uniqBy(Array.from(e),o.iteratee(t)):[]}},12801:function(e,t,r){"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(67384);t.debounce=function(e,t=0,r={}){let i;"object"!=typeof r&&(r={});let{leading:a=!1,trailing:o=!0,maxWait:u}=r,l=[,,];a&&(l[0]="leading"),o&&(l[1]="trailing");let c=null,s=n.debounce(function(...t){i=e.apply(this,t),c=null},t,{edges:l}),f=function(...t){return null!=u&&(null===c&&(c=Date.now()),Date.now()-c>=u)?(i=e.apply(this,t),c=Date.now(),s.cancel(),s.schedule()):s.apply(this,t),i};return f.cancel=s.cancel,f.flush=()=>(s.flush(),i),f}},9313:function(e,t,r){"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(12801);t.throttle=function(e,t=0,r={}){"object"!=typeof r&&(r={});let{leading:i=!0,trailing:a=!0}=r;return n.debounce(e,t,{leading:i,trailing:a,maxWait:t})}},11480:function(e,t,r){"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(34893),i=r(95810);t.range=function(e,t,r){r&&"number"!=typeof r&&n.isIterateeCall(e,t,r)&&(t=r=void 0),e=i.toFinite(e),void 0===t?(t=e,e=0):t=i.toFinite(t),r=void 0===r?e<t?1:-1:i.toFinite(r);let a=Math.max(Math.ceil((t-e)/(r||1)),0),o=Array(a);for(let t=0;t<a;t++)o[t]=e,e+=r;return o}},5076:function(e,t,r){"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(887);t.cloneDeep=function(e){return n.cloneDeepWith(e)}},887:function(e,t,r){"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(16737),i=r(91116);t.cloneDeepWith=function(e,t){return n.cloneDeepWith(e,(r,a,o,u)=>{let l=t?.(r,a,o,u);if(null!=l)return l;if("object"==typeof e)switch(Object.prototype.toString.call(e)){case i.numberTag:case i.stringTag:case i.booleanTag:{let t=new e.constructor(e?.valueOf());return n.copyProperties(t,e),t}case i.argumentsTag:{let t={};return n.copyProperties(t,e),t.length=e.length,t[Symbol.iterator]=e[Symbol.iterator],t}default:return}})}},84073:function(e,t,r){"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(95491),i=r(35159),a=r(49670),o=r(64161);t.get=function e(t,r,u){if(null==t)return u;switch(typeof r){case"string":{if(n.isUnsafeProperty(r))return u;let a=t[r];if(void 0===a){if(i.isDeepKey(r))return e(t,o.toPath(r),u);return u}return a}case"number":case"symbol":{"number"==typeof r&&(r=a.toKey(r));let e=t[r];if(void 0===e)return u;return e}default:{if(Array.isArray(r))return function(e,t,r){if(0===t.length)return r;let i=e;for(let e=0;e<t.length;e++){if(null==i||n.isUnsafeProperty(t[e]))return r;i=i[t[e]]}return void 0===i?r:i}(t,r,u);if(r=Object.is(r?.valueOf(),-0)?"-0":String(r),n.isUnsafeProperty(r))return u;let e=t[r];if(void 0===e)return u;return e}}}},18141:function(e,t,r){"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(35159),i=r(84623),a=r(62094),o=r(64161);t.has=function(e,t){let r;if(0===(r=Array.isArray(t)?t:"string"==typeof t&&n.isDeepKey(t)&&e?.[t]==null?o.toPath(t):[t]).length)return!1;let u=e;for(let e=0;e<r.length;e++){let t=r[e];if((null==u||!Object.hasOwn(u,t))&&!((Array.isArray(u)||a.isArguments(u))&&i.isIndex(t)&&t<u.length))return!1;u=u[t]}return!0}},83752:function(e,t,r){"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(84073);t.property=function(e){return function(t){return n.get(t,e)}}},62094:function(e,t,r){"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(83984);t.isArguments=function(e){return null!==e&&"object"==typeof e&&"[object Arguments]"===n.getTag(e)}},98934:function(e,t,r){"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(48973);t.isArrayLike=function(e){return null!=e&&"function"!=typeof e&&n.isLength(e.length)}},79311:function(e,t,r){"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(98934),i=r(77475);t.isArrayLikeObject=function(e){return i.isObjectLike(e)&&n.isArrayLike(e)}},16643:function(e,t,r){"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(80757);t.isMatch=function(e,t){return n.isMatchWith(e,t,()=>void 0)}},80757:function(e,t,r){"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(16643),i=r(13400),a=r(34526),o=r(95037);function u(e,t,r,n){if(t===e)return!0;switch(typeof t){case"object":return function(e,t,r,n){if(null==t)return!0;if(Array.isArray(t))return l(e,t,r,n);if(t instanceof Map)return function(e,t,r,n){if(0===t.size)return!0;if(!(e instanceof Map))return!1;for(let[i,a]of t.entries())if(!1===r(e.get(i),a,i,e,t,n))return!1;return!0}(e,t,r,n);if(t instanceof Set)return c(e,t,r,n);let i=Object.keys(t);if(null==e)return 0===i.length;if(0===i.length)return!0;if(n&&n.has(t))return n.get(t)===e;n&&n.set(t,e);try{for(let o=0;o<i.length;o++){let u=i[o];if(!a.isPrimitive(e)&&!(u in e)||void 0===t[u]&&void 0!==e[u]||null===t[u]&&null!==e[u]||!r(e[u],t[u],u,e,t,n))return!1}return!0}finally{n&&n.delete(t)}}(e,t,r,n);case"function":if(Object.keys(t).length>0)return u(e,{...t},r,n);return o.eq(e,t);default:if(!i.isObject(e))return o.eq(e,t);if("string"==typeof t)return""===t;return!0}}function l(e,t,r,n){if(0===t.length)return!0;if(!Array.isArray(e))return!1;let i=new Set;for(let a=0;a<t.length;a++){let o=t[a],u=!1;for(let l=0;l<e.length;l++){if(i.has(l))continue;let c=e[l],s=!1;if(r(c,o,a,e,t,n)&&(s=!0),s){i.add(l),u=!0;break}}if(!u)return!1}return!0}function c(e,t,r,n){return 0===t.size||e instanceof Set&&l([...e],[...t],r,n)}t.isMatchWith=function(e,t,r){return"function"!=typeof r?n.isMatch(e,t):u(e,t,function e(t,n,i,a,o,l){let c=r(t,n,i,a,o,l);return void 0!==c?!!c:u(t,n,e,l)},new Map)},t.isSetMatch=c},13400:function(e,t){"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isObject=function(e){return null!==e&&("object"==typeof e||"function"==typeof e)}},77475:function(e,t){"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isObjectLike=function(e){return"object"==typeof e&&null!==e}},49483:function(e,t){"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isPlainObject=function(e){if("object"!=typeof e||null==e)return!1;if(null===Object.getPrototypeOf(e))return!0;if("[object Object]"!==Object.prototype.toString.call(e)){let t=e[Symbol.toStringTag];return!!(null!=t&&Object.getOwnPropertyDescriptor(e,Symbol.toStringTag)?.writable)&&e.toString()===`[object ${t}]`}let t=e;for(;null!==Object.getPrototypeOf(t);)t=Object.getPrototypeOf(t);return Object.getPrototypeOf(e)===t}},74675:function(e,t){"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isSymbol=function(e){return"symbol"==typeof e||e instanceof Symbol}},76536:function(e,t,r){"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(16643),i=r(60631);t.matches=function(e){return e=i.cloneDeep(e),t=>n.isMatch(t,e)}},14308:function(e,t,r){"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(16643),i=r(49670),a=r(5076),o=r(84073),u=r(18141);t.matchesProperty=function(e,t){switch(typeof e){case"object":Object.is(e?.valueOf(),-0)&&(e="-0");break;case"number":e=i.toKey(e)}return t=a.cloneDeep(t),function(r){let i=o.get(r,e);return void 0===i?u.has(r,e):void 0===t?void 0===i:n.isMatch(i,t)}}},95037:function(e,t){"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.eq=function(e,t){return e===t||Number.isNaN(e)&&Number.isNaN(t)}},55586:function(e,t,r){"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(50601),i=r(83752),a=r(76536),o=r(14308);t.iteratee=function(e){if(null==e)return n.identity;switch(typeof e){case"function":return e;case"object":if(Array.isArray(e)&&2===e.length)return o.matchesProperty(e[0],e[1]);return a.matches(e);case"string":case"symbol":case"number":return i.property(e)}}},95810:function(e,t,r){"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(44201);t.toFinite=function(e){return e?(e=n.toNumber(e))===1/0||e===-1/0?(e<0?-1:1)*Number.MAX_VALUE:e==e?e:0:0===e?e:0}},44201:function(e,t,r){"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(74675);t.toNumber=function(e){return n.isSymbol(e)?NaN:Number(e)}},64161:function(e,t){"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.toPath=function(e){let t=[],r=e.length;if(0===r)return t;let n=0,i="",a="",o=!1;for(46===e.charCodeAt(0)&&(t.push(""),n++);n<r;){let u=e[n];a?"\\"===u&&n+1<r?i+=e[++n]:u===a?a="":i+=u:o?'"'===u||"'"===u?a=u:"]"===u?(o=!1,t.push(i),i=""):i+=u:"["===u?(o=!0,i&&(t.push(i),i="")):"."===u?i&&(t.push(i),i=""):i+=u,n++}return i&&t.push(i),t}},67384:function(e,t){"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.debounce=function(e,t,{signal:r,edges:n}={}){let i;let a=null,o=null!=n&&n.includes("leading"),u=null==n||n.includes("trailing"),l=()=>{null!==a&&(e.apply(i,a),i=void 0,a=null)},c=()=>{u&&l(),p()},s=null,f=()=>{null!=s&&clearTimeout(s),s=setTimeout(()=>{s=null,c()},t)},d=()=>{null!==s&&(clearTimeout(s),s=null)},p=()=>{d(),i=void 0,a=null},h=function(...e){if(r?.aborted)return;i=this,a=e;let t=null==s;f(),o&&t&&l()};return h.schedule=f,h.cancel=p,h.flush=()=>{d(),l()},r?.addEventListener("abort",p,{once:!0}),h}},50601:function(e,t){"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.identity=function(e){return e}},60196:function(e,t){"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.noop=function(){}},60631:function(e,t,r){"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(16737);t.cloneDeep=function(e){return n.cloneDeepWithImpl(e,void 0,e,new Map,void 0)}},16737:function(e,t,r){"use strict";var n=r(96434).Buffer;Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let i=r(59924),a=r(83984),o=r(91116),u=r(34526),l=r(23497);function c(e,t,r,i=new Map,f){let d=f?.(e,t,r,i);if(null!=d)return d;if(u.isPrimitive(e))return e;if(i.has(e))return i.get(e);if(Array.isArray(e)){let t=Array(e.length);i.set(e,t);for(let n=0;n<e.length;n++)t[n]=c(e[n],n,r,i,f);return Object.hasOwn(e,"index")&&(t.index=e.index),Object.hasOwn(e,"input")&&(t.input=e.input),t}if(e instanceof Date)return new Date(e.getTime());if(e instanceof RegExp){let t=new RegExp(e.source,e.flags);return t.lastIndex=e.lastIndex,t}if(e instanceof Map){let t=new Map;for(let[n,a]of(i.set(e,t),e))t.set(n,c(a,n,r,i,f));return t}if(e instanceof Set){let t=new Set;for(let n of(i.set(e,t),e))t.add(c(n,void 0,r,i,f));return t}if(void 0!==n&&n.isBuffer(e))return e.subarray();if(l.isTypedArray(e)){let t=new(Object.getPrototypeOf(e)).constructor(e.length);i.set(e,t);for(let n=0;n<e.length;n++)t[n]=c(e[n],n,r,i,f);return t}if(e instanceof ArrayBuffer||"undefined"!=typeof SharedArrayBuffer&&e instanceof SharedArrayBuffer)return e.slice(0);if(e instanceof DataView){let t=new DataView(e.buffer.slice(0),e.byteOffset,e.byteLength);return i.set(e,t),s(t,e,r,i,f),t}if("undefined"!=typeof File&&e instanceof File){let t=new File([e],e.name,{type:e.type});return i.set(e,t),s(t,e,r,i,f),t}if(e instanceof Blob){let t=new Blob([e],{type:e.type});return i.set(e,t),s(t,e,r,i,f),t}if(e instanceof Error){let t=new e.constructor;return i.set(e,t),t.message=e.message,t.name=e.name,t.stack=e.stack,t.cause=e.cause,s(t,e,r,i,f),t}if("object"==typeof e&&function(e){switch(a.getTag(e)){case o.argumentsTag:case o.arrayTag:case o.arrayBufferTag:case o.dataViewTag:case o.booleanTag:case o.dateTag:case o.float32ArrayTag:case o.float64ArrayTag:case o.int8ArrayTag:case o.int16ArrayTag:case o.int32ArrayTag:case o.mapTag:case o.numberTag:case o.objectTag:case o.regexpTag:case o.setTag:case o.stringTag:case o.symbolTag:case o.uint8ArrayTag:case o.uint8ClampedArrayTag:case o.uint16ArrayTag:case o.uint32ArrayTag:return!0;default:return!1}}(e)){let t=Object.create(Object.getPrototypeOf(e));return i.set(e,t),s(t,e,r,i,f),t}return e}function s(e,t,r=e,n,a){let o=[...Object.keys(t),...i.getSymbols(t)];for(let i=0;i<o.length;i++){let u=o[i],l=Object.getOwnPropertyDescriptor(e,u);(null==l||l.writable)&&(e[u]=c(t[u],u,r,n,a))}}t.cloneDeepWith=function(e,t){return c(e,void 0,e,new Map,t)},t.cloneDeepWithImpl=c,t.copyProperties=s},7972:function(e,t,r){"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(56993),i=r(60196);t.isEqual=function(e,t){return n.isEqualWith(e,t,i.noop)}},56993:function(e,t,r){"use strict";var n=r(96434).Buffer;Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let i=r(7252),a=r(59924),o=r(83984),u=r(91116),l=r(95037);t.isEqualWith=function(e,t,r){return function e(t,r,c,s,f,d,p){let h=p(t,r,c,s,f,d);if(void 0!==h)return h;if(typeof t==typeof r)switch(typeof t){case"bigint":case"string":case"boolean":case"symbol":case"undefined":case"function":return t===r;case"number":return t===r||Object.is(t,r)}return function t(r,c,s,f){if(Object.is(r,c))return!0;let d=o.getTag(r),p=o.getTag(c);if(d===u.argumentsTag&&(d=u.objectTag),p===u.argumentsTag&&(p=u.objectTag),d!==p)return!1;switch(d){case u.stringTag:return r.toString()===c.toString();case u.numberTag:{let e=r.valueOf(),t=c.valueOf();return l.eq(e,t)}case u.booleanTag:case u.dateTag:case u.symbolTag:return Object.is(r.valueOf(),c.valueOf());case u.regexpTag:return r.source===c.source&&r.flags===c.flags;case u.functionTag:return r===c}let h=(s=s??new Map).get(r),v=s.get(c);if(null!=h&&null!=v)return h===c;s.set(r,c),s.set(c,r);try{switch(d){case u.mapTag:if(r.size!==c.size)return!1;for(let[t,n]of r.entries())if(!c.has(t)||!e(n,c.get(t),t,r,c,s,f))return!1;return!0;case u.setTag:{if(r.size!==c.size)return!1;let t=Array.from(r.values()),n=Array.from(c.values());for(let i=0;i<t.length;i++){let a=t[i],o=n.findIndex(t=>e(a,t,void 0,r,c,s,f));if(-1===o)return!1;n.splice(o,1)}return!0}case u.arrayTag:case u.uint8ArrayTag:case u.uint8ClampedArrayTag:case u.uint16ArrayTag:case u.uint32ArrayTag:case u.bigUint64ArrayTag:case u.int8ArrayTag:case u.int16ArrayTag:case u.int32ArrayTag:case u.bigInt64ArrayTag:case u.float32ArrayTag:case u.float64ArrayTag:if(void 0!==n&&n.isBuffer(r)!==n.isBuffer(c)||r.length!==c.length)return!1;for(let t=0;t<r.length;t++)if(!e(r[t],c[t],t,r,c,s,f))return!1;return!0;case u.arrayBufferTag:if(r.byteLength!==c.byteLength)return!1;return t(new Uint8Array(r),new Uint8Array(c),s,f);case u.dataViewTag:if(r.byteLength!==c.byteLength||r.byteOffset!==c.byteOffset)return!1;return t(new Uint8Array(r),new Uint8Array(c),s,f);case u.errorTag:return r.name===c.name&&r.message===c.message;case u.objectTag:{if(!(t(r.constructor,c.constructor,s,f)||i.isPlainObject(r)&&i.isPlainObject(c)))return!1;let n=[...Object.keys(r),...a.getSymbols(r)],o=[...Object.keys(c),...a.getSymbols(c)];if(n.length!==o.length)return!1;for(let t=0;t<n.length;t++){let i=n[t],a=r[i];if(!Object.hasOwn(c,i))return!1;let o=c[i];if(!e(a,o,i,r,c,s,f))return!1}return!0}default:return!1}}finally{s.delete(r),s.delete(c)}}(t,r,d,p)}(e,t,void 0,void 0,void 0,void 0,r)}},48973:function(e,t){"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isLength=function(e){return Number.isSafeInteger(e)&&e>=0}},7252:function(e,t){"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isPlainObject=function(e){if(!e||"object"!=typeof e)return!1;let t=Object.getPrototypeOf(e);return(null===t||t===Object.prototype||null===Object.getPrototypeOf(t))&&"[object Object]"===Object.prototype.toString.call(e)}},34526:function(e,t){"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isPrimitive=function(e){return null==e||"object"!=typeof e&&"function"!=typeof e}},23497:function(e,t){"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isTypedArray=function(e){return ArrayBuffer.isView(e)&&!(e instanceof DataView)}},87922:function(e,t,r){"use strict";r.d(t,{Z:function(){return q}});var n,i,a,o,u,l,c,s=r(5853),f=r(2265),d="right-scroll-bar-position",p="width-before-scroll-bar";function h(e,t){return"function"==typeof e?e(t):e&&(e.current=t),e}var v="undefined"!=typeof window?f.useLayoutEffect:f.useEffect,y=new WeakMap,g=(void 0===n&&(n={}),(void 0===i&&(i=function(e){return e}),a=[],o=!1,u={read:function(){if(o)throw Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return a.length?a[a.length-1]:null},useMedium:function(e){var t=i(e,o);return a.push(t),function(){a=a.filter(function(e){return e!==t})}},assignSyncMedium:function(e){for(o=!0;a.length;){var t=a;a=[],t.forEach(e)}a={push:function(t){return e(t)},filter:function(){return a}}},assignMedium:function(e){o=!0;var t=[];if(a.length){var r=a;a=[],r.forEach(e),t=a}var n=function(){var r=t;t=[],r.forEach(e)},i=function(){return Promise.resolve().then(n)};i(),a={push:function(e){t.push(e),i()},filter:function(e){return t=t.filter(e),a}}}}).options=(0,s.pi)({async:!0,ssr:!1},n),u),m=function(){},b=f.forwardRef(function(e,t){var r,n,i,a,o=f.useRef(null),u=f.useState({onScrollCapture:m,onWheelCapture:m,onTouchMoveCapture:m}),l=u[0],c=u[1],d=e.forwardProps,p=e.children,b=e.className,x=e.removeScrollBar,w=e.enabled,O=e.shards,j=e.sideCar,P=e.noRelative,k=e.noIsolation,M=e.inert,S=e.allowPinchZoom,A=e.as,E=e.gapMode,C=(0,s._T)(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),T=(r=[o,t],n=function(e){return r.forEach(function(t){return h(t,e)})},(i=(0,f.useState)(function(){return{value:null,callback:n,facade:{get current(){return i.value},set current(value){var e=i.value;e!==value&&(i.value=value,i.callback(value,e))}}}})[0]).callback=n,a=i.facade,v(function(){var e=y.get(a);if(e){var t=new Set(e),n=new Set(r),i=a.current;t.forEach(function(e){n.has(e)||h(e,null)}),n.forEach(function(e){t.has(e)||h(e,i)})}y.set(a,r)},[r]),a),D=(0,s.pi)((0,s.pi)({},C),l);return f.createElement(f.Fragment,null,w&&f.createElement(j,{sideCar:g,removeScrollBar:x,shards:O,noRelative:P,noIsolation:k,inert:M,setCallbacks:c,allowPinchZoom:!!S,lockRef:o,gapMode:E}),d?f.cloneElement(f.Children.only(p),(0,s.pi)((0,s.pi)({},D),{ref:T})):f.createElement(void 0===A?"div":A,(0,s.pi)({},D,{className:b,ref:T}),p))});b.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},b.classNames={fullWidth:p,zeroRight:d};var x=function(e){var t=e.sideCar,r=(0,s._T)(e,["sideCar"]);if(!t)throw Error("Sidecar: please provide `sideCar` property to import the right car");var n=t.read();if(!n)throw Error("Sidecar medium not found");return f.createElement(n,(0,s.pi)({},r))};x.isSideCarExport=!0;var w=function(){var e=0,t=null;return{add:function(n){if(0==e&&(t=function(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=c||r.nc;return t&&e.setAttribute("nonce",t),e}())){var i,a;(i=t).styleSheet?i.styleSheet.cssText=n:i.appendChild(document.createTextNode(n)),a=t,(document.head||document.getElementsByTagName("head")[0]).appendChild(a)}e++},remove:function(){--e||!t||(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},O=function(){var e=w();return function(t,r){f.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&r])}},j=function(){var e=O();return function(t){return e(t.styles,t.dynamic),null}},P={left:0,top:0,right:0,gap:0},k=function(e){return parseInt(e||"",10)||0},M=function(e){var t=window.getComputedStyle(document.body),r=t["padding"===e?"paddingLeft":"marginLeft"],n=t["padding"===e?"paddingTop":"marginTop"],i=t["padding"===e?"paddingRight":"marginRight"];return[k(r),k(n),k(i)]},S=function(e){if(void 0===e&&(e="margin"),"undefined"==typeof window)return P;var t=M(e),r=document.documentElement.clientWidth,n=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,n-r+t[2]-t[0])}},A=j(),E="data-scroll-locked",C=function(e,t,r,n){var i=e.left,a=e.top,o=e.right,u=e.gap;return void 0===r&&(r="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(n,";\n   padding-right: ").concat(u,"px ").concat(n,";\n  }\n  body[").concat(E,"] {\n    overflow: hidden ").concat(n,";\n    overscroll-behavior: contain;\n    ").concat([t&&"position: relative ".concat(n,";"),"margin"===r&&"\n    padding-left: ".concat(i,"px;\n    padding-top: ").concat(a,"px;\n    padding-right: ").concat(o,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(u,"px ").concat(n,";\n    "),"padding"===r&&"padding-right: ".concat(u,"px ").concat(n,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(d," {\n    right: ").concat(u,"px ").concat(n,";\n  }\n  \n  .").concat(p," {\n    margin-right: ").concat(u,"px ").concat(n,";\n  }\n  \n  .").concat(d," .").concat(d," {\n    right: 0 ").concat(n,";\n  }\n  \n  .").concat(p," .").concat(p," {\n    margin-right: 0 ").concat(n,";\n  }\n  \n  body[").concat(E,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(u,"px;\n  }\n")},T=function(){var e=parseInt(document.body.getAttribute(E)||"0",10);return isFinite(e)?e:0},D=function(){f.useEffect(function(){return document.body.setAttribute(E,(T()+1).toString()),function(){var e=T()-1;e<=0?document.body.removeAttribute(E):document.body.setAttribute(E,e.toString())}},[])},N=function(e){var t=e.noRelative,r=e.noImportant,n=e.gapMode,i=void 0===n?"margin":n;D();var a=f.useMemo(function(){return S(i)},[i]);return f.createElement(A,{styles:C(a,!t,i,r?"":"!important")})},_=!1;if("undefined"!=typeof window)try{var I=Object.defineProperty({},"passive",{get:function(){return _=!0,!0}});window.addEventListener("test",I,I),window.removeEventListener("test",I,I)}catch(e){_=!1}var R=!!_&&{passive:!1},L=function(e,t){if(!(e instanceof Element))return!1;var r=window.getComputedStyle(e);return"hidden"!==r[t]&&!(r.overflowY===r.overflowX&&"TEXTAREA"!==e.tagName&&"visible"===r[t])},z=function(e,t){var r=t.ownerDocument,n=t;do{if("undefined"!=typeof ShadowRoot&&n instanceof ShadowRoot&&(n=n.host),F(e,n)){var i=W(e,n);if(i[1]>i[2])return!0}n=n.parentNode}while(n&&n!==r.body);return!1},F=function(e,t){return"v"===e?L(t,"overflowY"):L(t,"overflowX")},W=function(e,t){return"v"===e?[t.scrollTop,t.scrollHeight,t.clientHeight]:[t.scrollLeft,t.scrollWidth,t.clientWidth]},B=function(e,t,r,n,i){var a,o=(a=window.getComputedStyle(t).direction,"h"===e&&"rtl"===a?-1:1),u=o*n,l=r.target,c=t.contains(l),s=!1,f=u>0,d=0,p=0;do{if(!l)break;var h=W(e,l),v=h[0],y=h[1]-h[2]-o*v;(v||y)&&F(e,l)&&(d+=y,p+=v);var g=l.parentNode;l=g&&g.nodeType===Node.DOCUMENT_FRAGMENT_NODE?g.host:g}while(!c&&l!==document.body||c&&(t.contains(l)||t===l));return f&&(i&&1>Math.abs(d)||!i&&u>d)?s=!0:!f&&(i&&1>Math.abs(p)||!i&&-u>p)&&(s=!0),s},K=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},Z=function(e){return[e.deltaX,e.deltaY]},U=function(e){return e&&"current"in e?e.current:e},V=0,$=[],Y=(l=function(e){var t=f.useRef([]),r=f.useRef([0,0]),n=f.useRef(),i=f.useState(V++)[0],a=f.useState(j)[0],o=f.useRef(e);f.useEffect(function(){o.current=e},[e]),f.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(i));var t=(0,s.ev)([e.lockRef.current],(e.shards||[]).map(U),!0).filter(Boolean);return t.forEach(function(e){return e.classList.add("allow-interactivity-".concat(i))}),function(){document.body.classList.remove("block-interactivity-".concat(i)),t.forEach(function(e){return e.classList.remove("allow-interactivity-".concat(i))})}}},[e.inert,e.lockRef.current,e.shards]);var u=f.useCallback(function(e,t){if("touches"in e&&2===e.touches.length||"wheel"===e.type&&e.ctrlKey)return!o.current.allowPinchZoom;var i,a=K(e),u=r.current,l="deltaX"in e?e.deltaX:u[0]-a[0],c="deltaY"in e?e.deltaY:u[1]-a[1],s=e.target,f=Math.abs(l)>Math.abs(c)?"h":"v";if("touches"in e&&"h"===f&&"range"===s.type)return!1;var d=z(f,s);if(!d)return!0;if(d?i=f:(i="v"===f?"h":"v",d=z(f,s)),!d)return!1;if(!n.current&&"changedTouches"in e&&(l||c)&&(n.current=i),!i)return!0;var p=n.current||i;return B(p,t,e,"h"===p?l:c,!0)},[]),l=f.useCallback(function(e){if($.length&&$[$.length-1]===a){var r="deltaY"in e?Z(e):K(e),n=t.current.filter(function(t){var n;return t.name===e.type&&(t.target===e.target||e.target===t.shadowParent)&&(n=t.delta)[0]===r[0]&&n[1]===r[1]})[0];if(n&&n.should){e.cancelable&&e.preventDefault();return}if(!n){var i=(o.current.shards||[]).map(U).filter(Boolean).filter(function(t){return t.contains(e.target)});(i.length>0?u(e,i[0]):!o.current.noIsolation)&&e.cancelable&&e.preventDefault()}}},[]),c=f.useCallback(function(e,r,n,i){var a={name:e,delta:r,target:n,should:i,shadowParent:function(e){for(var t=null;null!==e;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}(n)};t.current.push(a),setTimeout(function(){t.current=t.current.filter(function(e){return e!==a})},1)},[]),d=f.useCallback(function(e){r.current=K(e),n.current=void 0},[]),p=f.useCallback(function(t){c(t.type,Z(t),t.target,u(t,e.lockRef.current))},[]),h=f.useCallback(function(t){c(t.type,K(t),t.target,u(t,e.lockRef.current))},[]);f.useEffect(function(){return $.push(a),e.setCallbacks({onScrollCapture:p,onWheelCapture:p,onTouchMoveCapture:h}),document.addEventListener("wheel",l,R),document.addEventListener("touchmove",l,R),document.addEventListener("touchstart",d,R),function(){$=$.filter(function(e){return e!==a}),document.removeEventListener("wheel",l,R),document.removeEventListener("touchmove",l,R),document.removeEventListener("touchstart",d,R)}},[]);var v=e.removeScrollBar,y=e.inert;return f.createElement(f.Fragment,null,y?f.createElement(a,{styles:"\n  .block-interactivity-".concat(i," {pointer-events: none;}\n  .allow-interactivity-").concat(i," {pointer-events: all;}\n")}):null,v?f.createElement(N,{noRelative:e.noRelative,gapMode:e.gapMode}):null)},g.useMedium(l),x),H=f.forwardRef(function(e,t){return f.createElement(b,(0,s.pi)({},e,{ref:t,sideCar:Y}))});H.classNames=b.classNames;var q=H},46595:function(e,t,r){"use strict";r.d(t,{r:function(){return N}});var n=r(2265),i=r(38450),a=r.n(i),o=(e,t)=>[0,3*e,3*t-6*e,3*e-3*t+1],u=(e,t)=>e.map((e,r)=>e*t**r).reduce((e,t)=>e+t),l=(e,t)=>r=>u(o(e,t),r),c=(e,t)=>r=>u([...o(e,t).map((e,t)=>e*t).slice(1),0],r),s=function(){for(var e,t,r,n,i=arguments.length,a=Array(i),o=0;o<i;o++)a[o]=arguments[o];if(1===a.length)switch(a[0]){case"linear":[e,r,t,n]=[0,0,1,1];break;case"ease":[e,r,t,n]=[.25,.1,.25,1];break;case"ease-in":[e,r,t,n]=[.42,0,1,1];break;case"ease-out":[e,r,t,n]=[.42,0,.58,1];break;case"ease-in-out":[e,r,t,n]=[0,0,.58,1];break;default:var u=a[0].split("(");"cubic-bezier"===u[0]&&4===u[1].split(")")[0].split(",").length&&([e,r,t,n]=u[1].split(")")[0].split(",").map(e=>parseFloat(e)))}else 4===a.length&&([e,r,t,n]=a);var s=l(e,t),f=l(r,n),d=c(e,t),p=e=>e>1?1:e<0?0:e,h=e=>{for(var t=e>1?1:e,r=t,n=0;n<8;++n){var i=s(r)-t,a=d(r);if(1e-4>Math.abs(i-t)||a<1e-4)break;r=p(r-i/a)}return f(r)};return h.isStepper=!1,h},f=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},{stiff:t=100,damping:r=8,dt:n=17}=e,i=(e,i,a)=>{var o=a+(-(e-i)*t-a*r)*n/1e3,u=a*n/1e3+e;return 1e-4>Math.abs(u-i)&&1e-4>Math.abs(o)?[i,0]:[u,o]};return i.isStepper=!0,i.dt=n,i},d=e=>{if("string"==typeof e)switch(e){case"ease":case"ease-in-out":case"ease-out":case"ease-in":case"linear":return s(e);case"spring":return f();default:if("cubic-bezier"===e.split("(")[0])return s(e)}return"function"==typeof e?e:null};function p(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function h(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?p(Object(r),!0).forEach(function(t){var n,i;n=t,i=r[t],(n=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(n))in e?Object.defineProperty(e,n,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[n]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):p(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var v=e=>e.replace(/([A-Z])/g,e=>"-".concat(e.toLowerCase())),y=(e,t,r)=>e.map(e=>"".concat(v(e)," ").concat(t,"ms ").concat(r)).join(","),g=(e,t)=>[Object.keys(e),Object.keys(t)].reduce((e,t)=>e.filter(e=>t.includes(e))),m=(e,t)=>Object.keys(t).reduce((r,n)=>h(h({},r),{},{[n]:e(n,t[n])}),{});function b(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function x(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?b(Object(r),!0).forEach(function(t){var n,i;n=t,i=r[t],(n=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(n))in e?Object.defineProperty(e,n,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[n]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):b(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var w=(e,t,r)=>e+(t-e)*r,O=e=>{var{from:t,to:r}=e;return t!==r},j=(e,t,r)=>{var n=m((t,r)=>{if(O(r)){var[n,i]=e(r.from,r.to,r.velocity);return x(x({},r),{},{from:n,velocity:i})}return r},t);return r<1?m((e,t)=>O(t)?x(x({},t),{},{velocity:w(t.velocity,n[e].velocity,r),from:w(t.from,n[e].from,r)}):t,t):j(e,n,r-1)},P=(e,t,r,n,i,a)=>{var o,u,l,c,s,f,d,p,h,v,y=g(e,t);return!0===r.isStepper?(u=y.reduce((r,n)=>x(x({},r),{},{[n]:{from:e[n],velocity:0,to:t[n]}}),{}),l=()=>m((e,t)=>t.from,u),c=()=>!Object.values(u).filter(O).length,s=null,f=n=>{o||(o=n);var d=(n-o)/r.dt;u=j(r,u,d),i(x(x(x({},e),t),l())),o=n,c()||(s=a.setTimeout(f))},()=>(s=a.setTimeout(f),()=>{s()})):(p=null,h=y.reduce((r,n)=>x(x({},r),{},{[n]:[e[n],t[n]]}),{}),v=o=>{d||(d=o);var u=(o-d)/n,l=m((e,t)=>w(...t,r(u)),h);if(i(x(x(x({},e),t),l)),u<1)p=a.setTimeout(v);else{var c=m((e,t)=>w(...t,r(1)),h);i(x(x(x({},e),t),c))}},()=>(p=a.setTimeout(v),()=>{p()}))};class k{setTimeout(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,r=performance.now(),n=null,i=a=>{a-r>=t?e(a):"function"==typeof requestAnimationFrame&&(n=requestAnimationFrame(i))};return n=requestAnimationFrame(i),()=>{cancelAnimationFrame(n)}}}var M=["children","begin","duration","attributeName","easing","isActive","from","to","canBegin","onAnimationEnd","shouldReAnimate","onAnimationReStart","animationManager"];function S(){return(S=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function A(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function E(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?A(Object(r),!0).forEach(function(t){C(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):A(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function C(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}class T extends n.PureComponent{componentDidMount(){var{isActive:e,canBegin:t}=this.props;this.mounted=!0,e&&t&&this.runAnimation(this.props)}componentDidUpdate(e){var{isActive:t,canBegin:r,attributeName:n,shouldReAnimate:i,to:o,from:u}=this.props,{style:l}=this.state;if(r){if(!t){this.state&&l&&(n&&l[n]!==o||!n&&l!==o)&&this.setState({style:n?{[n]:o}:o});return}if(!a()(e.to,o)||!e.canBegin||!e.isActive){var c=!e.canBegin||!e.isActive;this.manager.stop(),this.stopJSAnimation&&this.stopJSAnimation();var s=c||i?u:e.to;this.state&&l&&(n&&l[n]!==s||!n&&l!==s)&&this.setState({style:n?{[n]:s}:s}),this.runAnimation(E(E({},this.props),{},{from:s,begin:0}))}}}componentWillUnmount(){this.mounted=!1;var{onAnimationEnd:e}=this.props;this.unSubscribe&&this.unSubscribe(),this.manager.stop(),this.stopJSAnimation&&this.stopJSAnimation(),e&&e()}handleStyleChange(e){this.changeStyle(e)}changeStyle(e){this.mounted&&this.setState({style:e})}runJSAnimation(e){var{from:t,to:r,duration:n,easing:i,begin:a,onAnimationEnd:o,onAnimationStart:u}=e,l=P(t,r,d(i),n,this.changeStyle,this.manager.getTimeoutController());this.manager.start([u,a,()=>{this.stopJSAnimation=l()},n,o])}runAnimation(e){var{begin:t,duration:r,attributeName:n,to:i,easing:a,onAnimationStart:o,onAnimationEnd:u,children:l}=e;if(this.unSubscribe=this.manager.subscribe(this.handleStyleChange),"function"==typeof a||"function"==typeof l||"spring"===a){this.runJSAnimation(e);return}var c=n?{[n]:i}:i,s=y(Object.keys(c),r,a);this.manager.start([o,t,E(E({},c),{},{transition:s}),r,u])}render(){var e=this.props,{children:t,begin:r,duration:i,attributeName:a,easing:o,isActive:u,from:l,to:c,canBegin:s,onAnimationEnd:f,shouldReAnimate:d,onAnimationReStart:p,animationManager:h}=e,v=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,M),y=n.Children.count(t),g=this.state.style;if("function"==typeof t)return t(g);if(!u||0===y||i<=0)return t;var m=e=>{var{style:t={},className:r}=e.props;return(0,n.cloneElement)(e,E(E({},v),{},{style:E(E({},t),g),className:r}))};return 1===y?m(n.Children.only(t)):n.createElement("div",null,n.Children.map(t,e=>m(e)))}constructor(e,t){super(e,t),C(this,"mounted",!1),C(this,"manager",null),C(this,"stopJSAnimation",null),C(this,"unSubscribe",null);var{isActive:r,attributeName:n,from:i,to:a,children:o,duration:u,animationManager:l}=this.props;if(this.manager=l,this.handleStyleChange=this.handleStyleChange.bind(this),this.changeStyle=this.changeStyle.bind(this),!r||u<=0){this.state={style:{}},"function"==typeof o&&(this.state={style:a});return}if(i){if("function"==typeof o){this.state={style:i};return}this.state={style:n?{[n]:i}:i}}else this.state={style:{}}}}C(T,"displayName","Animate"),C(T,"defaultProps",{begin:0,duration:1e3,attributeName:"",easing:"ease",isActive:!0,canBegin:!0,onAnimationEnd:()=>{},onAnimationStart:()=>{}});var D=(0,n.createContext)(null);function N(e){var t,r,i,a,o,u,l,c=(0,n.useContext)(D);return n.createElement(T,S({},e,{animationManager:null!==(u=null!==(l=e.animationManager)&&void 0!==l?l:c)&&void 0!==u?u:(t=new k,r=()=>null,i=!1,a=null,o=e=>{if(!i){if(Array.isArray(e)){if(!e.length)return;var[n,...u]=e;if("number"==typeof n){a=t.setTimeout(o.bind(null,u),n);return}o(n),a=t.setTimeout(o.bind(null,u));return}"object"==typeof e&&r(e),"function"==typeof e&&e()}},{stop:()=>{i=!0},start:e=>{i=!1,a&&(a(),a=null),o(e)},subscribe:e=>(r=e,()=>{r=()=>null}),getTimeoutController:()=>t})}))}},32447:function(e,t,r){"use strict";r.d(t,{$:function(){return eq},u:function(){return eH}});var n=r(2265),i=r(61994),a=r(9841),o=r(82944),u=r(13790),l=r(58735),c=["children"],s=()=>{},f=(0,n.createContext)({addErrorBar:s,removeErrorBar:s}),d=(0,n.createContext)({data:[],xAxisId:"xAxis-0",yAxisId:"yAxis-0",dataPointFormatter:()=>({x:0,y:0,value:0}),errorBarOffset:0});function p(e){var{children:t}=e,r=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,c);return n.createElement(d.Provider,{value:r},t)}var h=()=>(0,n.useContext)(d),v=e=>{var{children:t,xAxisId:r,yAxisId:i,zAxisId:a,dataKey:o,data:c,stackId:s,hide:d,type:p,barSize:h}=e,[v,y]=n.useState([]),g=(0,n.useCallback)(e=>{y(t=>[...t,e])},[y]),m=(0,n.useCallback)(e=>{y(t=>t.filter(t=>t!==e))},[y]),b=(0,l.W)();return n.createElement(f.Provider,{value:{addErrorBar:g,removeErrorBar:m}},n.createElement(u.V,{type:p,data:c,xAxisId:r,yAxisId:i,zAxisId:a,dataKey:o,errorBars:v,stackId:s,hide:d,barSize:h,isPanorama:b}),t)};function y(e){var{addErrorBar:t,removeErrorBar:r}=(0,n.useContext)(f);return(0,n.useEffect)(()=>(t(e),()=>{r(e)}),[t,r,e]),null}var g=r(98628),m=r(39040),b=e=>{var t=(0,l.W)();return(0,m.C)(r=>(0,g.AS)(r,"xAxis",e,t))},x=e=>{var t=(0,l.W)();return(0,m.C)(r=>(0,g.AS)(r,"yAxis",e,t))},w=r(40130),O=r(46595),j=["direction","width","dataKey","isAnimationActive","animationBegin","animationDuration","animationEasing"];function P(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function k(){return(k=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function M(e){var{direction:t,width:r,dataKey:i,isAnimationActive:u,animationBegin:l,animationDuration:c,animationEasing:s}=e,f=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,j),d=(0,o.L6)(f,!1),{data:p,dataPointFormatter:v,xAxisId:y,yAxisId:g,errorBarOffset:m}=h(),w=b(y),P=x(g);if((null==w?void 0:w.scale)==null||(null==P?void 0:P.scale)==null||null==p||"x"===t&&"number"!==w.type)return null;var M=p.map(e=>{var o,f,{x:p,y:h,value:y,errorVal:g}=v(e,i,t);if(!g)return null;var b=[];if(Array.isArray(g)?[o,f]=g:o=f=g,"x"===t){var{scale:x}=w,j=h+m,M=j+r,S=j-r,A=x(y-o),E=x(y+f);b.push({x1:E,y1:M,x2:E,y2:S}),b.push({x1:A,y1:j,x2:E,y2:j}),b.push({x1:A,y1:M,x2:A,y2:S})}else if("y"===t){var{scale:C}=P,T=p+m,D=T-r,N=T+r,_=C(y-o),I=C(y+f);b.push({x1:D,y1:I,x2:N,y2:I}),b.push({x1:T,y1:_,x2:T,y2:I}),b.push({x1:D,y1:_,x2:N,y2:_})}var R="".concat(p+m,"px ").concat(h+m,"px");return n.createElement(a.m,k({className:"recharts-errorBar",key:"bar-".concat(b.map(e=>"".concat(e.x1,"-").concat(e.x2,"-").concat(e.y1,"-").concat(e.y2)))},d),b.map(e=>{var t=u?{transformOrigin:"".concat(e.x1-5,"px")}:void 0;return n.createElement(O.r,{from:{transform:"scaleY(0)",transformOrigin:R},to:{transform:"scaleY(1)",transformOrigin:R},begin:l,easing:s,isActive:u,duration:c,key:"line-".concat(e.x1,"-").concat(e.x2,"-").concat(e.y1,"-").concat(e.y2),style:{transformOrigin:R}},n.createElement("line",k({},e,{style:t})))}))});return n.createElement(a.m,{className:"recharts-errorBars"},M)}var S=(0,n.createContext)(void 0);function A(e){var{direction:t,children:r}=e;return n.createElement(S.Provider,{value:t},r)}var E={stroke:"black",strokeWidth:1.5,width:5,offset:0,isAnimationActive:!0,animationBegin:0,animationDuration:400,animationEasing:"ease-in-out"};function C(e){var t,r,i=(t=e.direction,r=(0,n.useContext)(S),null!=t?t:null!=r?r:"x"),{width:a,isAnimationActive:o,animationBegin:u,animationDuration:l,animationEasing:c}=(0,w.j)(e,E);return n.createElement(n.Fragment,null,n.createElement(y,{dataKey:e.dataKey,direction:i}),n.createElement(M,k({},e,{direction:i,width:a,isAnimationActive:o,animationBegin:u,animationDuration:l,animationEasing:c})))}class T extends n.Component{render(){return n.createElement(C,this.props)}}P(T,"defaultProps",E),P(T,"displayName","ErrorBar");var D=r(20407),N=r(37618),_=r.n(N),I=r(26680),R=r(49037),L=r(16630),z=["valueAccessor"],F=["data","dataKey","clockWise","id","textBreakAll"];function W(){return(W=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function B(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function K(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?B(Object(r),!0).forEach(function(t){var n,i;n=t,i=r[t],(n=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(n))in e?Object.defineProperty(e,n,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[n]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):B(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function Z(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}var U=e=>Array.isArray(e.value)?_()(e.value):e.value;function V(e){var{valueAccessor:t=U}=e,r=Z(e,z),{data:i,dataKey:u,clockWise:l,id:c,textBreakAll:s}=r,f=Z(r,F);return i&&i.length?n.createElement(a.m,{className:"recharts-label-list"},i.map((e,r)=>{var i=(0,L.Rw)(u)?t(e,r):(0,R.F$)(e&&e.payload,u),a=(0,L.Rw)(c)?{}:{id:"".concat(c,"-").concat(r)};return n.createElement(I._,W({},(0,o.L6)(e,!0),f,a,{parentViewBox:e.parentViewBox,value:i,textBreakAll:s,viewBox:I._.parseViewBox((0,L.Rw)(l)?e:K(K({},e),{},{clockWise:l})),key:"label-".concat(r),index:r}))})):null}V.displayName="LabelList",V.renderCallByParent=function(e,t){var r,i=!(arguments.length>2)||void 0===arguments[2]||arguments[2];if(!e||!e.children&&i&&!e.label)return null;var{children:a}=e,u=(0,o.NN)(a,V).map((e,r)=>(0,n.cloneElement)(e,{data:t,key:"labelList-".concat(r)}));return i?[(r=e.label)?!0===r?n.createElement(V,{key:"labelList-implicit",data:t}):n.isValidElement(r)||(0,I.d)(r)?n.createElement(V,{key:"labelList-implicit",data:t,content:r}):"object"==typeof r?n.createElement(V,W({data:t},r,{key:"labelList-implicit"})):null:null,...u]:u};var $=r(34067),Y=r(41637),H=r(80503),q=["x","y"];function X(){return(X=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function G(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function Q(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?G(Object(r),!0).forEach(function(t){var n,i;n=t,i=r[t],(n=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(n))in e?Object.defineProperty(e,n,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[n]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):G(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function J(e,t){var{x:r,y:n}=e,i=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,q),a=parseInt("".concat(r),10),o=parseInt("".concat(n),10),u=parseInt("".concat(t.height||i.height),10),l=parseInt("".concat(t.width||i.width),10);return Q(Q(Q(Q(Q({},t),i),a?{x:a}:{}),o?{y:o}:{}),{},{height:u,width:l,name:t.name,radius:t.radius})}function ee(e){return n.createElement(H.b,X({shapeType:"rectangle",propTransformer:J,activeClassName:"recharts-active-bar"},e))}var et=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return(r,n)=>{if((0,L.hj)(e))return e;var i=(0,L.hj)(r)||(0,L.Rw)(r);return i?e(r,n):(i||function(e,t){if(!e)throw Error("Invariant failed")}(!1),t)}},er=r(44296),en=r(35623),ei=r(19579),ea=()=>{var e=(0,m.T)();return(0,n.useEffect)(()=>(e((0,ei.a1)()),()=>{e((0,ei.nF)())})),null},eo=r(35953);function eu(e,t){var r,n,i=(0,m.C)(t=>(0,g.i9)(t,e)),a=(0,m.C)(e=>(0,g.t)(e,t)),o=null!==(r=null==i?void 0:i.allowDataOverflow)&&void 0!==r?r:g.dW.allowDataOverflow,u=null!==(n=null==a?void 0:a.allowDataOverflow)&&void 0!==n?n:g.RN.allowDataOverflow;return{needClip:o||u,needClipX:o,needClipY:u}}function el(e){var{xAxisId:t,yAxisId:r,clipPathId:i}=e,a=(0,eo.qD)(),{needClipX:o,needClipY:u,needClip:l}=eu(t,r);if(!l)return null;var{left:c,top:s,width:f,height:d}=a;return n.createElement("clipPath",{id:"clipPath-".concat(i)},n.createElement("rect",{x:o?c:c-f/2,y:u?s:s-d/2,width:o?f:2*f,height:u?d:2*d}))}var ec=r(92713),es=r(22932),ef=r(36289),ed=r(33968),ep=r(66395);function eh(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function ev(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?eh(Object(r),!0).forEach(function(t){var n,i;n=t,i=r[t],(n=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(n))in e?Object.defineProperty(e,n,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[n]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):eh(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var ey=(e,t,r,n,i)=>i,eg=(e,t,r)=>{var n=null!=r?r:e;if(!(0,L.Rw)(n))return(0,L.h1)(n,t,0)},em=(0,ec.P1)([eo.rE,g.bm,(e,t)=>t,(e,t,r)=>r,(e,t,r,n)=>n],(e,t,r,n,i)=>t.filter(t=>"horizontal"===e?t.xAxisId===r:t.yAxisId===n).filter(e=>e.isPanorama===i).filter(e=>!1===e.hide).filter(e=>"bar"===e.type));function eb(e){return null!=e.stackId&&null!=e.dataKey}var ex=(0,ec.P1)([em,ed.X8,(e,t,r)=>"horizontal"===(0,eo.rE)(e)?(0,g.Lu)(e,"xAxis",t):(0,g.Lu)(e,"yAxis",r)],(e,t,r)=>{var n=e.filter(eb),i=e.filter(e=>null==e.stackId);return[...Object.entries(n.reduce((e,t)=>(e[t.stackId]||(e[t.stackId]=[]),e[t.stackId].push(t),e),{})).map(e=>{var[n,i]=e;return{stackId:n,dataKeys:i.map(e=>e.dataKey),barSize:eg(t,r,i[0].barSize)}}),...i.map(e=>({stackId:void 0,dataKeys:[e.dataKey].filter(e=>null!=e),barSize:eg(t,r,e.barSize)}))]}),ew=(e,t,r,n)=>{var i,a;return"horizontal"===(0,eo.rE)(e)?(i=(0,g.AS)(e,"xAxis",t,n),a=(0,g.bY)(e,"xAxis",t,n)):(i=(0,g.AS)(e,"yAxis",r,n),a=(0,g.bY)(e,"yAxis",r,n)),(0,R.zT)(i,a)},eO=(0,ec.P1)([ex,ed.qy,ed.wK,ed.sd,(e,t,r,n,i)=>{var a,o,u,l,c=(0,eo.rE)(e),s=(0,ed.qy)(e),{maxBarSize:f}=i,d=(0,L.Rw)(f)?s:f;return"horizontal"===c?(u=(0,g.AS)(e,"xAxis",t,n),l=(0,g.bY)(e,"xAxis",t,n)):(u=(0,g.AS)(e,"yAxis",r,n),l=(0,g.bY)(e,"yAxis",r,n)),null!==(a=null!==(o=(0,R.zT)(u,l,!0))&&void 0!==o?o:d)&&void 0!==a?a:0},ew,(e,t,r,n,i)=>i.maxBarSize],(e,t,r,n,i,a,o)=>{var u=function(e,t,r,n,i){var a,o=n.length;if(!(o<1)){var u=(0,L.h1)(e,r,0,!0),l=[];if((0,ep.n)(n[0].barSize)){var c=!1,s=r/o,f=n.reduce((e,t)=>e+(t.barSize||0),0);(f+=(o-1)*u)>=r&&(f-=(o-1)*u,u=0),f>=r&&s>0&&(c=!0,s*=.9,f=o*s);var d={offset:((r-f)/2>>0)-u,size:0};a=n.reduce((e,t)=>{var r,n=[...e,{stackId:t.stackId,dataKeys:t.dataKeys,position:{offset:d.offset+d.size+u,size:c?s:null!==(r=t.barSize)&&void 0!==r?r:0}}];return d=n[n.length-1].position,n},l)}else{var p=(0,L.h1)(t,r,0,!0);r-2*p-(o-1)*u<=0&&(u=0);var h=(r-2*p-(o-1)*u)/o;h>1&&(h>>=0);var v=(0,ep.n)(i)?Math.min(h,i):h;a=n.reduce((e,t,r)=>[...e,{stackId:t.stackId,dataKeys:t.dataKeys,position:{offset:p+(h+u)*r+(h-v)/2,size:v}}],l)}return a}}(r,n,i!==a?i:a,e,(0,L.Rw)(o)?t:o);return i!==a&&null!=u&&(u=u.map(e=>ev(ev({},e),{},{position:ev(ev({},e.position),{},{offset:e.position.offset-i/2})}))),u}),ej=(0,ec.P1)([eO,ey],(e,t)=>{if(null!=e){var r=e.find(e=>e.stackId===t.stackId&&e.dataKeys.includes(t.dataKey));if(null!=r)return r.position}}),eP=(0,ec.P1)([g.bm,ey],(e,t)=>{if(e.some(e=>"bar"===e.type&&t.dataKey===e.dataKey&&t.stackId===e.stackId&&t.stackId===e.stackId))return t}),ek=(0,ec.P1)([(e,t,r,n)=>"horizontal"===(0,eo.rE)(e)?(0,g.g6)(e,"yAxis",r,n):(0,g.g6)(e,"xAxis",t,n),ey],(e,t)=>{if(e&&(null==t?void 0:t.dataKey)!=null){var{stackId:r}=t;if(null!=r){var n=e[r];if(n){var{stackedData:i}=n;if(i)return i.find(e=>e.key===t.dataKey)}}}}),eM=(0,ec.P1)([ef.bX,(e,t,r,n)=>(0,g.AS)(e,"xAxis",t,n),(e,t,r,n)=>(0,g.AS)(e,"yAxis",r,n),(e,t,r,n)=>(0,g.bY)(e,"xAxis",t,n),(e,t,r,n)=>(0,g.bY)(e,"yAxis",r,n),ej,eo.rE,es.hA,ew,ek,eP,(e,t,r,n,i,a)=>a],(e,t,r,n,i,a,o,u,l,c,s,f)=>{var d,{chartData:p,dataStartIndex:h,dataEndIndex:v}=u;if(null!=s&&null!=a&&("horizontal"===o||"vertical"===o)&&null!=t&&null!=r&&null!=n&&null!=i&&null!=l){var{data:y}=s;if(null!=(d=null!=y&&y.length>0?y:null==p?void 0:p.slice(h,v+1)))return eH({layout:o,barSettings:s,pos:a,bandSize:l,xAxis:t,yAxis:r,xAxisTicks:n,yAxisTicks:i,stackedData:c,displayedData:d,offset:e,cells:f})}}),eS=r(31944),eA=r(62658),eE=r(59087),eC=["onMouseEnter","onMouseLeave","onClick"],eT=["value","background","tooltipPosition"],eD=["onMouseEnter","onClick","onMouseLeave"];function eN(){return(eN=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function e_(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function eI(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?e_(Object(r),!0).forEach(function(t){eR(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):e_(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function eR(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function eL(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}var ez=e=>{var{dataKey:t,name:r,fill:n,legendType:i,hide:a}=e;return[{inactive:a,dataKey:t,type:i,color:n,value:(0,R.hn)(r,t),payload:e}]};function eF(e){var{dataKey:t,stroke:r,strokeWidth:n,fill:i,name:a,hide:o,unit:u}=e;return{dataDefinedOnItem:void 0,positions:void 0,settings:{stroke:r,strokeWidth:n,fill:i,dataKey:t,nameKey:void 0,name:(0,R.hn)(a,t),hide:o,type:e.tooltipType,color:e.fill,unit:u}}}function eW(e){var t=(0,m.C)(eS.Ve),{data:r,dataKey:i,background:a,allOtherBarProps:u}=e,{onMouseEnter:l,onMouseLeave:c,onClick:s}=u,f=eL(u,eC),d=(0,er.Df)(l,i),p=(0,er.oQ)(c),h=(0,er.nC)(s,i);if(!a||null==r)return null;var v=(0,o.L6)(a,!1);return n.createElement(n.Fragment,null,r.map((e,r)=>{var{value:o,background:u,tooltipPosition:l}=e,c=eL(e,eT);if(!u)return null;var s=d(e,r),y=p(e,r),g=h(e,r),m=eI(eI(eI(eI(eI({option:a,isActive:String(r)===t},c),{},{fill:"#eee"},u),v),(0,Y.bw)(f,e,r)),{},{onMouseEnter:s,onMouseLeave:y,onClick:g,dataKey:i,index:r,className:"recharts-bar-background-rectangle"});return n.createElement(ee,eN({key:"background-bar-".concat(r)},m))}))}function eB(e){var{data:t,props:r,showLabels:i}=e,u=(0,o.L6)(r,!1),{shape:l,dataKey:c,activeBar:s}=r,f=(0,m.C)(eS.Ve),d=(0,m.C)(eS.du),{onMouseEnter:p,onClick:h,onMouseLeave:v}=r,y=eL(r,eD),g=(0,er.Df)(p,c),b=(0,er.oQ)(v),x=(0,er.nC)(h,c);return t?n.createElement(n.Fragment,null,t.map((e,t)=>{var r=s&&String(t)===f&&(null==d||c===d),i=eI(eI(eI({},u),e),{},{isActive:r,option:r?s:l,index:t,dataKey:c});return n.createElement(a.m,eN({className:"recharts-bar-rectangle"},(0,Y.bw)(y,e,t),{onMouseEnter:g(e,t),onMouseLeave:b(e,t),onClick:x(e,t),key:"rectangle-".concat(null==e?void 0:e.x,"-").concat(null==e?void 0:e.y,"-").concat(null==e?void 0:e.value,"-").concat(t)}),n.createElement(ee,i))}),i&&V.renderCallByParent(r,t)):null}function eK(e){var{props:t,previousRectanglesRef:r}=e,{data:i,layout:o,isAnimationActive:u,animationBegin:l,animationDuration:c,animationEasing:s,onAnimationEnd:f,onAnimationStart:d}=t,p=r.current,h=(0,eE.i)(t,"recharts-bar-"),[v,y]=(0,n.useState)(!1),g=(0,n.useCallback)(()=>{"function"==typeof f&&f(),y(!1)},[f]),m=(0,n.useCallback)(()=>{"function"==typeof d&&d(),y(!0)},[d]);return n.createElement(O.r,{begin:l,duration:c,isActive:u,easing:s,from:{t:0},to:{t:1},onAnimationEnd:g,onAnimationStart:m,key:h},e=>{var{t:u}=e,l=1===u?i:i.map((e,t)=>{var r=p&&p[t];if(r){var n=(0,L.k4)(r.x,e.x),i=(0,L.k4)(r.y,e.y),a=(0,L.k4)(r.width,e.width),l=(0,L.k4)(r.height,e.height);return eI(eI({},e),{},{x:n(u),y:i(u),width:a(u),height:l(u)})}if("horizontal"===o){var c=(0,L.k4)(0,e.height)(u);return eI(eI({},e),{},{y:e.y+e.height-c,height:c})}var s=(0,L.k4)(0,e.width)(u);return eI(eI({},e),{},{width:s})});return u>0&&(r.current=l),n.createElement(a.m,null,n.createElement(eB,{props:t,data:l,showLabels:!v}))})}function eZ(e){var{data:t,isAnimationActive:r}=e,i=(0,n.useRef)(null);return r&&t&&t.length&&(null==i.current||i.current!==t)?n.createElement(eK,{previousRectanglesRef:i,props:e}):n.createElement(eB,{props:e,data:t,showLabels:!0})}var eU=(e,t)=>{var r=Array.isArray(e.value)?e.value[1]:e.value;return{x:e.x,y:e.y,value:r,errorVal:(0,R.F$)(e,t)}};class eV extends n.PureComponent{render(){var{hide:e,data:t,dataKey:r,className:o,xAxisId:u,yAxisId:l,needClip:c,background:s,id:f,layout:d}=this.props;if(e)return null;var p=(0,i.W)("recharts-bar",o),h=(0,L.Rw)(f)?this.id:f;return n.createElement(a.m,{className:p},c&&n.createElement("defs",null,n.createElement(el,{clipPathId:h,xAxisId:u,yAxisId:l})),n.createElement(a.m,{className:"recharts-bar-rectangles",clipPath:c?"url(#clipPath-".concat(h,")"):null},n.createElement(eW,{data:t,dataKey:r,background:s,allOtherBarProps:this.props}),n.createElement(eZ,this.props)),n.createElement(A,{direction:"horizontal"===d?"y":"x"},this.props.children))}constructor(){super(...arguments),eR(this,"id",(0,L.EL)("recharts-bar-"))}}var e$={activeBar:!1,animationBegin:0,animationDuration:400,animationEasing:"ease",hide:!1,isAnimationActive:!$.x.isSsr,legendType:"rect",minPointSize:0,xAxisId:0,yAxisId:0};function eY(e){var t,{xAxisId:r,yAxisId:i,hide:a,legendType:u,minPointSize:c,activeBar:s,animationBegin:f,animationDuration:d,animationEasing:h,isAnimationActive:v}=(0,w.j)(e,e$),{needClip:y}=eu(r,i),g=(0,eo.vn)(),b=(0,l.W)(),x=(0,n.useMemo)(()=>({barSize:e.barSize,data:void 0,dataKey:e.dataKey,maxBarSize:e.maxBarSize,minPointSize:c,stackId:(0,R.GA)(e.stackId)}),[e.barSize,e.dataKey,e.maxBarSize,c,e.stackId]),O=(0,o.NN)(e.children,D.b),j=(0,m.C)(e=>eM(e,r,i,b,x,O));if("vertical"!==g&&"horizontal"!==g)return null;var P=null==j?void 0:j[0];return t=null==P||null==P.height||null==P.width?0:"vertical"===g?P.height/2:P.width/2,n.createElement(p,{xAxisId:r,yAxisId:i,data:j,dataPointFormatter:eU,errorBarOffset:t},n.createElement(eV,eN({},e,{layout:g,needClip:y,data:j,xAxisId:r,yAxisId:i,hide:a,legendType:u,minPointSize:c,activeBar:s,animationBegin:f,animationDuration:d,animationEasing:h,isAnimationActive:v})))}function eH(e){var{layout:t,barSettings:{dataKey:r,minPointSize:n},pos:i,bandSize:a,xAxis:o,yAxis:u,xAxisTicks:l,yAxisTicks:c,stackedData:s,displayedData:f,offset:d,cells:p}=e,h="horizontal"===t?u:o,v=s?h.scale.domain():null,y=(0,R.Yj)({numericAxis:h});return f.map((e,f)=>{s?g=(0,R.Vv)(s[f],v):Array.isArray(g=(0,R.F$)(e,r))||(g=[y,g]);var h=et(n,0)(g[1],f);if("horizontal"===t){var g,m,b,x,w,O,j,[P,k]=[u.scale(g[0]),u.scale(g[1])];m=(0,R.Fy)({axis:o,ticks:l,bandSize:a,offset:i.offset,entry:e,index:f}),b=null!==(j=null!=k?k:P)&&void 0!==j?j:void 0,x=i.size;var M=P-k;if(w=(0,L.In)(M)?0:M,O={x:m,y:d.top,width:x,height:d.height},Math.abs(h)>0&&Math.abs(w)<Math.abs(h)){var S=(0,L.uY)(w||h)*(Math.abs(h)-Math.abs(w));b-=S,w+=S}}else{var[A,E]=[o.scale(g[0]),o.scale(g[1])];if(m=A,b=(0,R.Fy)({axis:u,ticks:c,bandSize:a,offset:i.offset,entry:e,index:f}),x=E-A,w=i.size,O={x:d.left,y:b,width:d.width,height:w},Math.abs(h)>0&&Math.abs(x)<Math.abs(h)){var C=(0,L.uY)(x||h)*(Math.abs(h)-Math.abs(x));x+=C}}return eI(eI({},e),{},{x:m,y:b,width:x,height:w,value:s?g:g[1],payload:e,background:O,tooltipPosition:{x:m+x/2,y:b+w/2}},p&&p[f]&&p[f].props)})}class eq extends n.PureComponent{render(){return n.createElement(v,{type:"bar",data:null,xAxisId:this.props.xAxisId,yAxisId:this.props.yAxisId,zAxisId:0,dataKey:this.props.dataKey,stackId:this.props.stackId,hide:this.props.hide,barSize:this.props.barSize},n.createElement(ea,null),n.createElement(eA.L,{legendPayload:ez(this.props)}),n.createElement(en.k,{fn:eF,args:this.props}),n.createElement(eY,this.props))}}eR(eq,"displayName","Bar"),eR(eq,"defaultProps",e$)},70949:function(e,t,r){"use strict";r.d(t,{O:function(){return O}});var n=r(2265),i=r(15870),a=r.n(i),o=r(61994);function u(e,t){for(var r in e)if(({}).hasOwnProperty.call(e,r)&&(!({}).hasOwnProperty.call(t,r)||e[r]!==t[r]))return!1;for(var n in t)if(({}).hasOwnProperty.call(t,n)&&!({}).hasOwnProperty.call(e,n))return!1;return!0}var l=r(9841),c=r(58811),s=r(26680),f=r(16630),d=r(41637),p=r(82944),h=r(12983),v=["viewBox"],y=["viewBox"];function g(){return(g=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function m(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function b(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?m(Object(r),!0).forEach(function(t){w(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):m(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function x(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}function w(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}class O extends n.Component{shouldComponentUpdate(e,t){var{viewBox:r}=e,n=x(e,v),i=this.props,{viewBox:a}=i,o=x(i,y);return!u(r,a)||!u(n,o)||!u(t,this.state)}getTickLineCoord(e){var t,r,n,i,a,o,{x:u,y:l,width:c,height:s,orientation:d,tickSize:p,mirror:h,tickMargin:v}=this.props,y=h?-1:1,g=e.tickSize||p,m=(0,f.hj)(e.tickCoord)?e.tickCoord:e.coordinate;switch(d){case"top":t=r=e.coordinate,o=(n=(i=l+ +!h*s)-y*g)-y*v,a=m;break;case"left":n=i=e.coordinate,a=(t=(r=u+ +!h*c)-y*g)-y*v,o=m;break;case"right":n=i=e.coordinate,a=(t=(r=u+ +h*c)+y*g)+y*v,o=m;break;default:t=r=e.coordinate,o=(n=(i=l+ +h*s)+y*g)+y*v,a=m}return{line:{x1:t,y1:n,x2:r,y2:i},tick:{x:a,y:o}}}getTickTextAnchor(){var e,{orientation:t,mirror:r}=this.props;switch(t){case"left":e=r?"start":"end";break;case"right":e=r?"end":"start";break;default:e="middle"}return e}getTickVerticalAnchor(){var{orientation:e,mirror:t}=this.props;switch(e){case"left":case"right":return"middle";case"top":return t?"start":"end";default:return t?"end":"start"}}renderAxisLine(){var{x:e,y:t,width:r,height:i,orientation:u,mirror:l,axisLine:c}=this.props,s=b(b(b({},(0,p.L6)(this.props,!1)),(0,p.L6)(c,!1)),{},{fill:"none"});if("top"===u||"bottom"===u){var f=+("top"===u&&!l||"bottom"===u&&l);s=b(b({},s),{},{x1:e,y1:t+f*i,x2:e+r,y2:t+f*i})}else{var d=+("left"===u&&!l||"right"===u&&l);s=b(b({},s),{},{x1:e+d*r,y1:t,x2:e+d*r,y2:t+i})}return n.createElement("line",g({},s,{className:(0,o.W)("recharts-cartesian-axis-line",a()(c,"className"))}))}static renderTickItem(e,t,r){var i,a=(0,o.W)(t.className,"recharts-cartesian-axis-tick-value");if(n.isValidElement(e))i=n.cloneElement(e,b(b({},t),{},{className:a}));else if("function"==typeof e)i=e(b(b({},t),{},{className:a}));else{var u="recharts-cartesian-axis-tick-value";"boolean"!=typeof e&&(u=(0,o.W)(u,e.className)),i=n.createElement(c.x,g({},t,{className:u}),r)}return i}renderTicks(e,t){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[],{tickLine:i,stroke:u,tick:c,tickFormatter:s,unit:f}=this.props,v=(0,h.f)(b(b({},this.props),{},{ticks:r}),e,t),y=this.getTickTextAnchor(),m=this.getTickVerticalAnchor(),x=(0,p.L6)(this.props,!1),w=(0,p.L6)(c,!1),j=b(b({},x),{},{fill:"none"},(0,p.L6)(i,!1)),P=v.map((e,t)=>{var{line:r,tick:p}=this.getTickLineCoord(e),h=b(b(b(b({textAnchor:y,verticalAnchor:m},x),{},{stroke:"none",fill:u},w),p),{},{index:t,payload:e,visibleTicksCount:v.length,tickFormatter:s});return n.createElement(l.m,g({className:"recharts-cartesian-axis-tick",key:"tick-".concat(e.value,"-").concat(e.coordinate,"-").concat(e.tickCoord)},(0,d.bw)(this.props,e,t)),i&&n.createElement("line",g({},j,r,{className:(0,o.W)("recharts-cartesian-axis-tick-line",a()(i,"className"))})),c&&O.renderTickItem(c,h,"".concat("function"==typeof s?s(e.value,t):e.value).concat(f||"")))});return P.length>0?n.createElement("g",{className:"recharts-cartesian-axis-ticks"},P):null}render(){var{axisLine:e,width:t,height:r,className:i,hide:a}=this.props;if(a)return null;var{ticks:u}=this.props;return null!=t&&t<=0||null!=r&&r<=0?null:n.createElement(l.m,{className:(0,o.W)("recharts-cartesian-axis",i),ref:e=>{if(e){var t=e.getElementsByClassName("recharts-cartesian-axis-tick-value");this.tickRefs.current=Array.from(t);var r=t[0];if(r){var n=window.getComputedStyle(r).fontSize,i=window.getComputedStyle(r).letterSpacing;(n!==this.state.fontSize||i!==this.state.letterSpacing)&&this.setState({fontSize:window.getComputedStyle(r).fontSize,letterSpacing:window.getComputedStyle(r).letterSpacing})}}}},e&&this.renderAxisLine(),this.renderTicks(this.state.fontSize,this.state.letterSpacing,u),s._.renderCallByParent(this.props))}constructor(e){super(e),this.tickRefs=n.createRef(),this.tickRefs.current=[],this.state={fontSize:"",letterSpacing:""}}}w(O,"displayName","CartesianAxis"),w(O,"defaultProps",{x:0,y:0,width:0,height:0,viewBox:{x:0,y:0,width:0,height:0},orientation:"bottom",ticks:[],stroke:"#666",tickLine:!0,axisLine:!0,tick:!0,mirror:!1,minTickGap:5,tickSize:6,tickMargin:2,interval:"preserveEnd"})},56940:function(e,t,r){"use strict";r.d(t,{q:function(){return D}});var n=r(2265),i=r(1175),a=r(16630),o=r(82944),u=r(49037),l=r(12983),c=r(70949),s=r(35953),f=r(98628),d=r(39040),p=r(58735),h=r(40130),v=["x1","y1","x2","y2","key"],y=["offset"],g=["xAxisId","yAxisId"],m=["xAxisId","yAxisId"];function b(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function x(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?b(Object(r),!0).forEach(function(t){var n,i;n=t,i=r[t],(n=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(n))in e?Object.defineProperty(e,n,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[n]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):b(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function w(){return(w=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function O(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}var j=e=>{var{fill:t}=e;if(!t||"none"===t)return null;var{fillOpacity:r,x:i,y:a,width:o,height:u,ry:l}=e;return n.createElement("rect",{x:i,y:a,ry:l,width:o,height:u,stroke:"none",fill:t,fillOpacity:r,className:"recharts-cartesian-grid-bg"})};function P(e,t){var r;if(n.isValidElement(e))r=n.cloneElement(e,t);else if("function"==typeof e)r=e(t);else{var{x1:i,y1:a,x2:u,y2:l,key:c}=t,s=O(t,v),f=(0,o.L6)(s,!1),{offset:d}=f,p=O(f,y);r=n.createElement("line",w({},p,{x1:i,y1:a,x2:u,y2:l,fill:"none",key:c}))}return r}function k(e){var{x:t,width:r,horizontal:i=!0,horizontalPoints:a}=e;if(!i||!a||!a.length)return null;var{xAxisId:o,yAxisId:u}=e,l=O(e,g),c=a.map((e,n)=>P(i,x(x({},l),{},{x1:t,y1:e,x2:t+r,y2:e,key:"line-".concat(n),index:n})));return n.createElement("g",{className:"recharts-cartesian-grid-horizontal"},c)}function M(e){var{y:t,height:r,vertical:i=!0,verticalPoints:a}=e;if(!i||!a||!a.length)return null;var{xAxisId:o,yAxisId:u}=e,l=O(e,m),c=a.map((e,n)=>P(i,x(x({},l),{},{x1:e,y1:t,x2:e,y2:t+r,key:"line-".concat(n),index:n})));return n.createElement("g",{className:"recharts-cartesian-grid-vertical"},c)}function S(e){var{horizontalFill:t,fillOpacity:r,x:i,y:a,width:o,height:u,horizontalPoints:l,horizontal:c=!0}=e;if(!c||!t||!t.length)return null;var s=l.map(e=>Math.round(e+a-a)).sort((e,t)=>e-t);a!==s[0]&&s.unshift(0);var f=s.map((e,l)=>{var c=s[l+1]?s[l+1]-e:a+u-e;if(c<=0)return null;var f=l%t.length;return n.createElement("rect",{key:"react-".concat(l),y:e,x:i,height:c,width:o,stroke:"none",fill:t[f],fillOpacity:r,className:"recharts-cartesian-grid-bg"})});return n.createElement("g",{className:"recharts-cartesian-gridstripes-horizontal"},f)}function A(e){var{vertical:t=!0,verticalFill:r,fillOpacity:i,x:a,y:o,width:u,height:l,verticalPoints:c}=e;if(!t||!r||!r.length)return null;var s=c.map(e=>Math.round(e+a-a)).sort((e,t)=>e-t);a!==s[0]&&s.unshift(0);var f=s.map((e,t)=>{var c=s[t+1]?s[t+1]-e:a+u-e;if(c<=0)return null;var f=t%r.length;return n.createElement("rect",{key:"react-".concat(t),x:e,y:o,width:c,height:l,stroke:"none",fill:r[f],fillOpacity:i,className:"recharts-cartesian-grid-bg"})});return n.createElement("g",{className:"recharts-cartesian-gridstripes-vertical"},f)}var E=(e,t)=>{var{xAxis:r,width:n,height:i,offset:a}=e;return(0,u.Rf)((0,l.f)(x(x(x({},c.O.defaultProps),r),{},{ticks:(0,u.uY)(r,!0),viewBox:{x:0,y:0,width:n,height:i}})),a.left,a.left+a.width,t)},C=(e,t)=>{var{yAxis:r,width:n,height:i,offset:a}=e;return(0,u.Rf)((0,l.f)(x(x(x({},c.O.defaultProps),r),{},{ticks:(0,u.uY)(r,!0),viewBox:{x:0,y:0,width:n,height:i}})),a.top,a.top+a.height,t)},T={horizontal:!0,vertical:!0,horizontalPoints:[],verticalPoints:[],stroke:"#ccc",fill:"none",verticalFill:[],horizontalFill:[],xAxisId:0,yAxisId:0};function D(e){var t=(0,s.zn)(),r=(0,s.Mw)(),o=(0,s.qD)(),u=x(x({},(0,h.j)(e,T)),{},{x:(0,a.hj)(e.x)?e.x:o.left,y:(0,a.hj)(e.y)?e.y:o.top,width:(0,a.hj)(e.width)?e.width:o.width,height:(0,a.hj)(e.height)?e.height:o.height}),{xAxisId:l,yAxisId:c,x:v,y,width:g,height:m,syncWithTicks:b,horizontalValues:O,verticalValues:P}=u,D=(0,p.W)(),N=(0,d.C)(e=>(0,f.Lg)(e,"xAxis",l,D)),_=(0,d.C)(e=>(0,f.Lg)(e,"yAxis",c,D));if(!(0,a.hj)(g)||g<=0||!(0,a.hj)(m)||m<=0||!(0,a.hj)(v)||v!==+v||!(0,a.hj)(y)||y!==+y)return null;var I=u.verticalCoordinatesGenerator||E,R=u.horizontalCoordinatesGenerator||C,{horizontalPoints:L,verticalPoints:z}=u;if((!L||!L.length)&&"function"==typeof R){var F=O&&O.length,W=R({yAxis:_?x(x({},_),{},{ticks:F?O:_.ticks}):void 0,width:t,height:r,offset:o},!!F||b);(0,i.Z)(Array.isArray(W),"horizontalCoordinatesGenerator should return Array but instead it returned [".concat(typeof W,"]")),Array.isArray(W)&&(L=W)}if((!z||!z.length)&&"function"==typeof I){var B=P&&P.length,K=I({xAxis:N?x(x({},N),{},{ticks:B?P:N.ticks}):void 0,width:t,height:r,offset:o},!!B||b);(0,i.Z)(Array.isArray(K),"verticalCoordinatesGenerator should return Array but instead it returned [".concat(typeof K,"]")),Array.isArray(K)&&(z=K)}return n.createElement("g",{className:"recharts-cartesian-grid"},n.createElement(j,{fill:u.fill,fillOpacity:u.fillOpacity,x:u.x,y:u.y,width:u.width,height:u.height,ry:u.ry}),n.createElement(S,w({},u,{horizontalPoints:L})),n.createElement(A,w({},u,{verticalPoints:z})),n.createElement(k,w({},u,{offset:o,horizontalPoints:L,xAxis:N,yAxis:_})),n.createElement(M,w({},u,{offset:o,verticalPoints:z,xAxis:N,yAxis:_})))}D.displayName="CartesianGrid"},97059:function(e,t,r){"use strict";r.d(t,{K:function(){return b}});var n=r(2265),i=r(61994),a=r(70949),o=r(39040),u=r(17644),l=r(98628),c=r(36289),s=r(58735),f=["children"],d=["dangerouslySetInnerHTML","ticks"];function p(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function h(){return(h=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function v(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}function y(e){var t=(0,o.T)(),r=(0,n.useMemo)(()=>{var{children:t}=e;return v(e,f)},[e]),i=(0,o.C)(e=>(0,l.i9)(e,r.id)),a=r===i;return((0,n.useEffect)(()=>(t((0,u.m2)(r)),()=>{t((0,u.Jj)(r))}),[r,t]),a)?e.children:null}var g=e=>{var{xAxisId:t,className:r}=e,u=(0,o.C)(c.zM),f=(0,s.W)(),p="xAxis",y=(0,o.C)(e=>(0,l.Vm)(e,p,t,f)),g=(0,o.C)(e=>(0,l.ox)(e,p,t,f)),m=(0,o.C)(e=>(0,l.Oy)(e,t)),b=(0,o.C)(e=>(0,l.rs)(e,t));if(null==m||null==b)return null;var{dangerouslySetInnerHTML:x,ticks:w}=e,O=v(e,d);return n.createElement(a.O,h({},O,{scale:y,x:b.x,y:b.y,width:m.width,height:m.height,className:(0,i.W)("recharts-".concat(p," ").concat(p),r),viewBox:u,ticks:g}))},m=e=>{var t,r,i,a,o;return n.createElement(y,{interval:null!==(t=e.interval)&&void 0!==t?t:"preserveEnd",id:e.xAxisId,scale:e.scale,type:e.type,padding:e.padding,allowDataOverflow:e.allowDataOverflow,domain:e.domain,dataKey:e.dataKey,allowDuplicatedCategory:e.allowDuplicatedCategory,allowDecimals:e.allowDecimals,tickCount:e.tickCount,includeHidden:null!==(r=e.includeHidden)&&void 0!==r&&r,reversed:e.reversed,ticks:e.ticks,height:e.height,orientation:e.orientation,mirror:e.mirror,hide:e.hide,unit:e.unit,name:e.name,angle:null!==(i=e.angle)&&void 0!==i?i:0,minTickGap:null!==(a=e.minTickGap)&&void 0!==a?a:5,tick:null===(o=e.tick)||void 0===o||o,tickFormatter:e.tickFormatter},n.createElement(g,e))};class b extends n.Component{render(){return n.createElement(m,this.props)}}p(b,"displayName","XAxis"),p(b,"defaultProps",{allowDataOverflow:l.dW.allowDataOverflow,allowDecimals:l.dW.allowDecimals,allowDuplicatedCategory:l.dW.allowDuplicatedCategory,height:l.dW.height,hide:!1,mirror:l.dW.mirror,orientation:l.dW.orientation,padding:l.dW.padding,reversed:l.dW.reversed,scale:l.dW.scale,tickCount:l.dW.tickCount,type:l.dW.type,xAxisId:0})},2027:function(e,t,r){"use strict";r.d(t,{Q:function(){return x}});var n=r(2265),i=r(61994),a=r(70949),o=r(17644),u=r(39040),l=r(98628),c=r(36289),s=r(58735),f=e=>{var{ticks:t,label:r,labelGapWithTick:n=5,tickSize:i=0,tickMargin:a=0}=e,o=0;if(t){t.forEach(e=>{if(e){var t=e.getBoundingClientRect();t.width>o&&(o=t.width)}});var u=r?r.getBoundingClientRect().width:0;return Math.round(o+(i+a)+u+(r?n:0))}return 0},d=r(26680),p=["dangerouslySetInnerHTML","ticks"];function h(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function v(){return(v=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function y(e){var t=(0,u.T)();return(0,n.useEffect)(()=>(t((0,o.TC)(e)),()=>{t((0,o.cB)(e))}),[e,t]),null}var g=e=>{var t,{yAxisId:r,className:h,width:y,label:g}=e,m=(0,n.useRef)(null),b=(0,n.useRef)(null),x=(0,u.C)(c.zM),w=(0,s.W)(),O=(0,u.T)(),j="yAxis",P=(0,u.C)(e=>(0,l.Vm)(e,j,r,w)),k=(0,u.C)(e=>(0,l.ON)(e,r)),M=(0,u.C)(e=>(0,l.lU)(e,r)),S=(0,u.C)(e=>(0,l.ox)(e,j,r,w));if((0,n.useLayoutEffect)(()=>{if(!("auto"!==y||!k||(0,d.d)(g)||(0,n.isValidElement)(g))){var e,t=m.current,i=null==t||null===(e=t.tickRefs)||void 0===e?void 0:e.current,{tickSize:a,tickMargin:u}=t.props,l=f({ticks:i,label:b.current,labelGapWithTick:5,tickSize:a,tickMargin:u});Math.round(k.width)!==Math.round(l)&&O((0,o.kB)({id:r,width:l}))}},[m,null==m||null===(t=m.current)||void 0===t||null===(t=t.tickRefs)||void 0===t?void 0:t.current,null==k?void 0:k.width,k,O,g,r,y]),null==k||null==M)return null;var{dangerouslySetInnerHTML:A,ticks:E}=e,C=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,p);return n.createElement(a.O,v({},C,{ref:m,labelRef:b,scale:P,x:M.x,y:M.y,width:k.width,height:k.height,className:(0,i.W)("recharts-".concat(j," ").concat(j),h),viewBox:x,ticks:S}))},m=e=>{var t,r,i,a,o;return n.createElement(n.Fragment,null,n.createElement(y,{interval:null!==(t=e.interval)&&void 0!==t?t:"preserveEnd",id:e.yAxisId,scale:e.scale,type:e.type,domain:e.domain,allowDataOverflow:e.allowDataOverflow,dataKey:e.dataKey,allowDuplicatedCategory:e.allowDuplicatedCategory,allowDecimals:e.allowDecimals,tickCount:e.tickCount,padding:e.padding,includeHidden:null!==(r=e.includeHidden)&&void 0!==r&&r,reversed:e.reversed,ticks:e.ticks,width:e.width,orientation:e.orientation,mirror:e.mirror,hide:e.hide,unit:e.unit,name:e.name,angle:null!==(i=e.angle)&&void 0!==i?i:0,minTickGap:null!==(a=e.minTickGap)&&void 0!==a?a:5,tick:null===(o=e.tick)||void 0===o||o,tickFormatter:e.tickFormatter}),n.createElement(g,e))},b={allowDataOverflow:l.RN.allowDataOverflow,allowDecimals:l.RN.allowDecimals,allowDuplicatedCategory:l.RN.allowDuplicatedCategory,hide:!1,mirror:l.RN.mirror,orientation:l.RN.orientation,padding:l.RN.padding,reversed:l.RN.reversed,scale:l.RN.scale,tickCount:l.RN.tickCount,type:l.RN.type,width:l.RN.width,yAxisId:0};class x extends n.Component{render(){return n.createElement(m,this.props)}}h(x,"displayName","YAxis"),h(x,"defaultProps",b)},12983:function(e,t,r){"use strict";r.d(t,{f:function(){return h}});var n,i,a=r(16630),o=r(4094),u=r(34067);class l{static create(e){return new l(e)}get domain(){return this.scale.domain}get range(){return this.scale.range}get rangeMin(){return this.range()[0]}get rangeMax(){return this.range()[1]}get bandwidth(){return this.scale.bandwidth}apply(e){var{bandAware:t,position:r}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(void 0!==e){if(r)switch(r){case"start":default:return this.scale(e);case"middle":var n=this.bandwidth?this.bandwidth()/2:0;return this.scale(e)+n;case"end":var i=this.bandwidth?this.bandwidth():0;return this.scale(e)+i}if(t){var a=this.bandwidth?this.bandwidth()/2:0;return this.scale(e)+a}return this.scale(e)}}isInRange(e){var t=this.range(),r=t[0],n=t[t.length-1];return r<=n?e>=r&&e<=n:e>=n&&e<=r}constructor(e){this.scale=e}}(i="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(i="EPS","string"))?n:n+"")in l?Object.defineProperty(l,i,{value:1e-4,enumerable:!0,configurable:!0,writable:!0}):l[i]=1e-4;var c=function(e){var{width:t,height:r}=e,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,i=(n%180+180)%180*Math.PI/180,a=Math.atan(r/t);return Math.abs(i>a&&i<Math.PI-a?r/Math.sin(i):t/Math.cos(i))};function s(e,t,r){if(t<1)return[];if(1===t&&void 0===r)return e;for(var n=[],i=0;i<e.length;i+=t){if(void 0!==r&&!0!==r(e[i]))return;n.push(e[i])}return n}function f(e,t,r,n,i){if(e*t<e*n||e*t>e*i)return!1;var a=r();return e*(t-e*a/2-n)>=0&&e*(t+e*a/2-i)<=0}function d(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function p(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?d(Object(r),!0).forEach(function(t){var n,i;n=t,i=r[t],(n=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(n))in e?Object.defineProperty(e,n,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[n]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):d(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function h(e,t,r){var n,{tick:i,ticks:l,viewBox:d,minTickGap:h,orientation:v,interval:y,tickFormatter:g,unit:m,angle:b}=e;if(!l||!l.length||!i)return[];if((0,a.hj)(y)||u.x.isSsr)return null!==(n=s(l,((0,a.hj)(y)?y:0)+1))&&void 0!==n?n:[];var x="top"===v||"bottom"===v?"width":"height",w=m&&"width"===x?(0,o.x)(m,{fontSize:t,letterSpacing:r}):{width:0,height:0},O=(e,n)=>{var i,a="function"==typeof g?g(e.value,n):e.value;return"width"===x?c({width:(i=(0,o.x)(a,{fontSize:t,letterSpacing:r})).width+w.width,height:i.height+w.height},b):(0,o.x)(a,{fontSize:t,letterSpacing:r})[x]},j=l.length>=2?(0,a.uY)(l[1].coordinate-l[0].coordinate):1,P=function(e,t,r){var n="width"===r,{x:i,y:a,width:o,height:u}=e;return 1===t?{start:n?i:a,end:n?i+o:a+u}:{start:n?i+o:a+u,end:n?i:a}}(d,j,x);return"equidistantPreserveStart"===y?function(e,t,r,n,i){for(var a,o=(n||[]).slice(),{start:u,end:l}=t,c=0,d=1,p=u;d<=o.length;)if(a=function(){var t,a=null==n?void 0:n[c];if(void 0===a)return{v:s(n,d)};var o=c,h=()=>(void 0===t&&(t=r(a,o)),t),v=a.coordinate,y=0===c||f(e,v,h,p,l);y||(c=0,p=u,d+=1),y&&(p=v+e*(h()/2+i),c+=d)}())return a.v;return[]}(j,P,O,l,h):("preserveStart"===y||"preserveStartEnd"===y?function(e,t,r,n,i,a){var o=(n||[]).slice(),u=o.length,{start:l,end:c}=t;if(a){var s=n[u-1],d=r(s,u-1),h=e*(s.coordinate+e*d/2-c);o[u-1]=s=p(p({},s),{},{tickCoord:h>0?s.coordinate-h*e:s.coordinate}),f(e,s.tickCoord,()=>d,l,c)&&(c=s.tickCoord-e*(d/2+i),o[u-1]=p(p({},s),{},{isShow:!0}))}for(var v=a?u-1:u,y=function(t){var n,a=o[t],u=()=>(void 0===n&&(n=r(a,t)),n);if(0===t){var s=e*(a.coordinate-e*u()/2-l);o[t]=a=p(p({},a),{},{tickCoord:s<0?a.coordinate-s*e:a.coordinate})}else o[t]=a=p(p({},a),{},{tickCoord:a.coordinate});f(e,a.tickCoord,u,l,c)&&(l=a.tickCoord+e*(u()/2+i),o[t]=p(p({},a),{},{isShow:!0}))},g=0;g<v;g++)y(g);return o}(j,P,O,l,h,"preserveStartEnd"===y):function(e,t,r,n,i){for(var a=(n||[]).slice(),o=a.length,{start:u}=t,{end:l}=t,c=function(t){var n,c=a[t],s=()=>(void 0===n&&(n=r(c,t)),n);if(t===o-1){var d=e*(c.coordinate+e*s()/2-l);a[t]=c=p(p({},c),{},{tickCoord:d>0?c.coordinate-d*e:c.coordinate})}else a[t]=c=p(p({},c),{},{tickCoord:c.coordinate});f(e,c.tickCoord,s,u,l)&&(l=c.tickCoord-e*(s()/2+i),a[t]=p(p({},c),{},{isShow:!0}))},s=o-1;s>=0;s--)c(s);return a}(j,P,O,l,h)).filter(e=>e.isShow)}},42736:function(e,t,r){"use strict";r.d(t,{v:function(){return g}});var n=r(2265),i=r(31057),a=r(82931),o=r(1196),u=r(87235),l=r(15317),c=r(39151),s=r(40130),f=r(66395),d=["width","height"];function p(){return(p=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}var h={accessibilityLayer:!0,layout:"horizontal",stackOffset:"none",barCategoryGap:"10%",barGap:4,margin:{top:5,right:5,bottom:5,left:5},reverseStackOrder:!1,syncMethod:"index"},v=(0,n.forwardRef)(function(e,t){var r,i=(0,s.j)(e.categoricalChartProps,h),{width:v,height:y}=i,g=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(i,d);if(!(0,f.r)(v)||!(0,f.r)(y))return null;var{chartName:m,defaultTooltipEventType:b,validateTooltipEventTypes:x,tooltipPayloadSearcher:w,categoricalChartProps:O}=e;return n.createElement(a.M,{preloadedState:{options:{chartName:m,defaultTooltipEventType:b,validateTooltipEventTypes:x,tooltipPayloadSearcher:w,eventEmitter:void 0}},reduxStoreName:null!==(r=O.id)&&void 0!==r?r:m},n.createElement(o.gt,{chartData:O.data}),n.createElement(u.v,{width:v,height:y,layout:i.layout,margin:i.margin}),n.createElement(l.b,{accessibilityLayer:i.accessibilityLayer,barCategoryGap:i.barCategoryGap,maxBarSize:i.maxBarSize,stackOffset:i.stackOffset,barGap:i.barGap,barSize:i.barSize,syncId:i.syncId,syncMethod:i.syncMethod,className:i.className}),n.createElement(c.r,p({},g,{width:v,height:y,ref:t})))}),y=["axis","item"],g=(0,n.forwardRef)((e,t)=>n.createElement(v,{chartName:"BarChart",defaultTooltipEventType:"axis",validateTooltipEventTypes:y,tooltipPayloadSearcher:i.NL,categoricalChartProps:e,ref:t}))},39151:function(e,t,r){"use strict";r.d(t,{r:function(){return L}});var n=r(2265),i=r(82944),a=r(35953),o=r(14847),u=r(58735),l=r(61994),c=["children","width","height","viewBox","className","style","title","desc"];function s(){return(s=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}var f=(0,n.forwardRef)((e,t)=>{var{children:r,width:a,height:o,viewBox:u,className:f,style:d,title:p,desc:h}=e,v=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,c),y=u||{width:a,height:o,x:0,y:0},g=(0,l.W)("recharts-surface",f);return n.createElement("svg",s({},(0,i.L6)(v,!0,"svg"),{className:g,width:a,height:o,style:d,viewBox:"".concat(y.x," ").concat(y.y," ").concat(y.width," ").concat(y.height),ref:t}),n.createElement("title",null,p),n.createElement("desc",null,h),r)}),d=r(39040),p=r(2431),h=r(66395),v=["children"];function y(){return(y=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}var g={width:"100%",height:"100%"},m=(0,n.forwardRef)((e,t)=>{var r,i,u=(0,a.zn)(),l=(0,a.Mw)(),c=(0,o.F)();if(!(0,h.r)(u)||!(0,h.r)(l))return null;var{children:s,otherAttributes:d,title:p,desc:v}=e;return r="number"==typeof d.tabIndex?d.tabIndex:c?0:void 0,i="string"==typeof d.role?d.role:c?"application":void 0,n.createElement(f,y({},d,{title:p,desc:v,role:i,tabIndex:r,width:u,height:l,style:g,ref:t}),s)}),b=e=>{var{children:t}=e,r=(0,d.C)(p.V);if(!r)return null;var{width:i,height:a,y:o,x:u}=r;return n.createElement(f,{width:i,height:a,x:u,y:o},t)},x=(0,n.forwardRef)((e,t)=>{var{children:r}=e,i=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,v);return(0,u.W)()?n.createElement(b,null,r):n.createElement(m,y({ref:t},i),r)}),w=r(64725),O=r(48323),j=r(318),P=r(7883),k=r(60152),M=r(65293),S=r(69366),A=r(83061),E=r(72321),C=(0,n.createContext)(null);function T(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}var D=(0,n.forwardRef)((e,t)=>{var{children:r,className:i,height:a,onClick:o,onContextMenu:u,onDoubleClick:c,onMouseDown:s,onMouseEnter:f,onMouseLeave:p,onMouseMove:v,onMouseUp:y,onTouchEnd:g,onTouchMove:m,onTouchStart:b,style:x,width:D}=e,N=(0,d.T)(),[_,I]=(0,n.useState)(null),[R,L]=(0,n.useState)(null);(0,j.W9)();var z=function(){var e=(0,d.T)(),[t,r]=(0,n.useState)(null),i=(0,d.C)(k.K$);return(0,n.useEffect)(()=>{if(null!=t){var r=t.getBoundingClientRect().width/t.offsetWidth;(0,h.n)(r)&&r!==i&&e((0,M.ZP)(r))}},[t,e,i]),r}(),F=(0,n.useCallback)(e=>{z(e),"function"==typeof t&&t(e),I(e),L(e)},[z,t,I,L]),W=(0,n.useCallback)(e=>{N((0,O.AE)(e)),N((0,S.r)({handler:o,reactEvent:e}))},[N,o]),B=(0,n.useCallback)(e=>{N((0,O.TK)(e)),N((0,S.r)({handler:f,reactEvent:e}))},[N,f]),K=(0,n.useCallback)(e=>{N((0,w.ne)()),N((0,S.r)({handler:p,reactEvent:e}))},[N,p]),Z=(0,n.useCallback)(e=>{N((0,O.TK)(e)),N((0,S.r)({handler:v,reactEvent:e}))},[N,v]),U=(0,n.useCallback)(()=>{N((0,P.eb)())},[N]),V=(0,n.useCallback)(e=>{N((0,P.sj)(e.key))},[N]),$=(0,n.useCallback)(e=>{N((0,S.r)({handler:u,reactEvent:e}))},[N,u]),Y=(0,n.useCallback)(e=>{N((0,S.r)({handler:c,reactEvent:e}))},[N,c]),H=(0,n.useCallback)(e=>{N((0,S.r)({handler:s,reactEvent:e}))},[N,s]),q=(0,n.useCallback)(e=>{N((0,S.r)({handler:y,reactEvent:e}))},[N,y]),X=(0,n.useCallback)(e=>{N((0,S.r)({handler:b,reactEvent:e}))},[N,b]),G=(0,n.useCallback)(e=>{N((0,A.$)(e)),N((0,S.r)({handler:m,reactEvent:e}))},[N,m]),Q=(0,n.useCallback)(e=>{N((0,S.r)({handler:g,reactEvent:e}))},[N,g]);return n.createElement(E.E.Provider,{value:_},n.createElement(C.Provider,{value:R},n.createElement("div",{className:(0,l.W)("recharts-wrapper",i),style:function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?T(Object(r),!0).forEach(function(t){var n,i;n=t,i=r[t],(n=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(n))in e?Object.defineProperty(e,n,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[n]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):T(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}({position:"relative",cursor:"default",width:D,height:a},x),role:"application",onClick:W,onContextMenu:$,onDoubleClick:Y,onFocus:U,onKeyDown:V,onMouseDown:H,onMouseEnter:B,onMouseLeave:K,onMouseMove:Z,onMouseUp:q,onTouchEnd:Q,onTouchMove:G,onTouchStart:X,ref:F},r)))}),N=r(16630),_=(0,n.createContext)(void 0),I=e=>{var{children:t}=e,[r]=(0,n.useState)("".concat((0,N.EL)("recharts"),"-clip")),i=(0,a.qD)();if(null==i)return null;var{left:o,top:u,height:l,width:c}=i;return n.createElement(_.Provider,{value:r},n.createElement("defs",null,n.createElement("clipPath",{id:r},n.createElement("rect",{x:o,y:u,height:l,width:c}))),t)},R=["children","className","width","height","style","compact","title","desc"],L=(0,n.forwardRef)((e,t)=>{var{children:r,className:a,width:o,height:u,style:l,compact:c,title:s,desc:f}=e,d=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,R),p=(0,i.L6)(d,!1);return c?n.createElement(x,{otherAttributes:p,title:s,desc:f},r):n.createElement(D,{className:a,style:l,width:o,height:u,onClick:e.onClick,onMouseLeave:e.onMouseLeave,onMouseEnter:e.onMouseEnter,onMouseMove:e.onMouseMove,onMouseDown:e.onMouseDown,onMouseUp:e.onMouseUp,onContextMenu:e.onContextMenu,onDoubleClick:e.onDoubleClick,onTouchStart:e.onTouchStart,onTouchMove:e.onTouchMove,onTouchEnd:e.onTouchEnd},n.createElement(x,{otherAttributes:p,title:s,desc:f,ref:t},n.createElement(I,null,r)))})},86810:function(e,t,r){"use strict";r.d(t,{u:function(){return w}});var n=r(2265),i=r(31057),a=r(82931),o=r(1196),u=r(87235),l=r(15317),c=r(39040),s=r(27410);function f(e){var t=(0,c.T)();return(0,n.useEffect)(()=>{t((0,s.a)(e))},[t,e]),null}var d=r(39151),p=r(40130),h=r(66395),v=["width","height","layout"];function y(){return(y=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}var g={accessibilityLayer:!0,stackOffset:"none",barCategoryGap:"10%",barGap:4,margin:{top:5,right:5,bottom:5,left:5},reverseStackOrder:!1,syncMethod:"index",layout:"radial"},m=(0,n.forwardRef)(function(e,t){var r,i=(0,p.j)(e.categoricalChartProps,g),{width:c,height:s,layout:m}=i,b=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(i,v);if(!(0,h.r)(c)||!(0,h.r)(s))return null;var{chartName:x,defaultTooltipEventType:w,validateTooltipEventTypes:O,tooltipPayloadSearcher:j}=e;return n.createElement(a.M,{preloadedState:{options:{chartName:x,defaultTooltipEventType:w,validateTooltipEventTypes:O,tooltipPayloadSearcher:j,eventEmitter:void 0}},reduxStoreName:null!==(r=i.id)&&void 0!==r?r:x},n.createElement(o.gt,{chartData:i.data}),n.createElement(u.v,{width:c,height:s,layout:m,margin:i.margin}),n.createElement(l.b,{accessibilityLayer:i.accessibilityLayer,barCategoryGap:i.barCategoryGap,maxBarSize:i.maxBarSize,stackOffset:i.stackOffset,barGap:i.barGap,barSize:i.barSize,syncId:i.syncId,syncMethod:i.syncMethod,className:i.className}),n.createElement(f,{cx:i.cx,cy:i.cy,startAngle:i.startAngle,endAngle:i.endAngle,innerRadius:i.innerRadius,outerRadius:i.outerRadius}),n.createElement(d.r,y({width:c,height:s},b,{ref:t})))}),b=["item"],x={layout:"centric",startAngle:0,endAngle:360,cx:"50%",cy:"50%",innerRadius:0,outerRadius:"80%"},w=(0,n.forwardRef)((e,t)=>{var r=(0,p.j)(e,x);return n.createElement(m,{chartName:"PieChart",defaultTooltipEventType:"item",validateTooltipEventTypes:b,tooltipPayloadSearcher:i.NL,categoricalChartProps:r,ref:t})})},20407:function(e,t,r){"use strict";r.d(t,{b:function(){return n}});var n=e=>null;n.displayName="Cell"},26680:function(e,t,r){"use strict";r.d(t,{_:function(){return j},d:function(){return g}});var n=r(2265),i=r(61994),a=r(58811),o=r(82944),u=r(16630),l=r(39206),c=r(35953),s=["offset"],f=["labelRef"];function d(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}function p(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function h(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?p(Object(r),!0).forEach(function(t){var n,i;n=t,i=r[t],(n=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(n))in e?Object.defineProperty(e,n,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[n]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):p(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function v(){return(v=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}var y=e=>{var{value:t,formatter:r}=e,n=(0,u.Rw)(e.children)?t:e.children;return"function"==typeof r?r(n):n},g=e=>null!=e&&"function"==typeof e,m=(e,t)=>(0,u.uY)(t-e)*Math.min(Math.abs(t-e),360),b=(e,t,r)=>{var a,o,{position:c,viewBox:s,offset:f,className:d}=e,{cx:p,cy:h,innerRadius:y,outerRadius:g,startAngle:b,endAngle:x,clockWise:w}=s,O=(y+g)/2,j=m(b,x),P=j>=0?1:-1;"insideStart"===c?(a=b+P*f,o=w):"insideEnd"===c?(a=x-P*f,o=!w):"end"===c&&(a=x+P*f,o=w),o=j<=0?o:!o;var k=(0,l.op)(p,h,O,a),M=(0,l.op)(p,h,O,a+(o?1:-1)*359),S="M".concat(k.x,",").concat(k.y,"\n    A").concat(O,",").concat(O,",0,1,").concat(o?0:1,",\n    ").concat(M.x,",").concat(M.y),A=(0,u.Rw)(e.id)?(0,u.EL)("recharts-radial-line-"):e.id;return n.createElement("text",v({},r,{dominantBaseline:"central",className:(0,i.W)("recharts-radial-bar-label",d)}),n.createElement("defs",null,n.createElement("path",{id:A,d:S})),n.createElement("textPath",{xlinkHref:"#".concat(A)},t))},x=e=>{var{viewBox:t,offset:r,position:n}=e,{cx:i,cy:a,innerRadius:o,outerRadius:u,startAngle:c,endAngle:s}=t,f=(c+s)/2;if("outside"===n){var{x:d,y:p}=(0,l.op)(i,a,u+r,f);return{x:d,y:p,textAnchor:d>=i?"start":"end",verticalAnchor:"middle"}}if("center"===n)return{x:i,y:a,textAnchor:"middle",verticalAnchor:"middle"};if("centerTop"===n)return{x:i,y:a,textAnchor:"middle",verticalAnchor:"start"};if("centerBottom"===n)return{x:i,y:a,textAnchor:"middle",verticalAnchor:"end"};var{x:h,y:v}=(0,l.op)(i,a,(o+u)/2,f);return{x:h,y:v,textAnchor:"middle",verticalAnchor:"middle"}},w=(e,t)=>{var{parentViewBox:r,offset:n,position:i}=e,{x:a,y:o,width:l,height:c}=t,s=c>=0?1:-1,f=s*n,d=s>0?"end":"start",p=s>0?"start":"end",v=l>=0?1:-1,y=v*n,g=v>0?"end":"start",m=v>0?"start":"end";if("top"===i)return h(h({},{x:a+l/2,y:o-s*n,textAnchor:"middle",verticalAnchor:d}),r?{height:Math.max(o-r.y,0),width:l}:{});if("bottom"===i)return h(h({},{x:a+l/2,y:o+c+f,textAnchor:"middle",verticalAnchor:p}),r?{height:Math.max(r.y+r.height-(o+c),0),width:l}:{});if("left"===i){var b={x:a-y,y:o+c/2,textAnchor:g,verticalAnchor:"middle"};return h(h({},b),r?{width:Math.max(b.x-r.x,0),height:c}:{})}if("right"===i){var x={x:a+l+y,y:o+c/2,textAnchor:m,verticalAnchor:"middle"};return h(h({},x),r?{width:Math.max(r.x+r.width-x.x,0),height:c}:{})}var w=r?{width:l,height:c}:{};return"insideLeft"===i?h({x:a+y,y:o+c/2,textAnchor:m,verticalAnchor:"middle"},w):"insideRight"===i?h({x:a+l-y,y:o+c/2,textAnchor:g,verticalAnchor:"middle"},w):"insideTop"===i?h({x:a+l/2,y:o+f,textAnchor:"middle",verticalAnchor:p},w):"insideBottom"===i?h({x:a+l/2,y:o+c-f,textAnchor:"middle",verticalAnchor:d},w):"insideTopLeft"===i?h({x:a+y,y:o+f,textAnchor:m,verticalAnchor:p},w):"insideTopRight"===i?h({x:a+l-y,y:o+f,textAnchor:g,verticalAnchor:p},w):"insideBottomLeft"===i?h({x:a+y,y:o+c-f,textAnchor:m,verticalAnchor:d},w):"insideBottomRight"===i?h({x:a+l-y,y:o+c-f,textAnchor:g,verticalAnchor:d},w):i&&"object"==typeof i&&((0,u.hj)(i.x)||(0,u.hU)(i.x))&&((0,u.hj)(i.y)||(0,u.hU)(i.y))?h({x:a+(0,u.h1)(i.x,l),y:o+(0,u.h1)(i.y,c),textAnchor:"end",verticalAnchor:"end"},w):h({x:a+l/2,y:o+c/2,textAnchor:"middle",verticalAnchor:"middle"},w)},O=e=>"cx"in e&&(0,u.hj)(e.cx);function j(e){var t,{offset:r=5}=e,l=h({offset:r},d(e,s)),{viewBox:p,position:g,value:m,children:j,content:P,className:k="",textBreakAll:M,labelRef:S}=l,A=(0,c.d2)(),E=p||A;if(!E||(0,u.Rw)(m)&&(0,u.Rw)(j)&&!(0,n.isValidElement)(P)&&"function"!=typeof P)return null;if((0,n.isValidElement)(P)){var{labelRef:C}=l,T=d(l,f);return(0,n.cloneElement)(P,T)}if("function"==typeof P){if(t=(0,n.createElement)(P,l),(0,n.isValidElement)(t))return t}else t=y(l);var D=O(E),N=(0,o.L6)(l,!0);if(D&&("insideStart"===g||"insideEnd"===g||"end"===g))return b(l,t,N);var _=D?x(l):w(l,E);return n.createElement(a.x,v({ref:S,className:(0,i.W)("recharts-label",k)},N,_,{breakAll:M}),t)}j.displayName="Label";var P=e=>{var{cx:t,cy:r,angle:n,startAngle:i,endAngle:a,r:o,radius:l,innerRadius:c,outerRadius:s,x:f,y:d,top:p,left:h,width:v,height:y,clockWise:g,labelViewBox:m}=e;if(m)return m;if((0,u.hj)(v)&&(0,u.hj)(y)){if((0,u.hj)(f)&&(0,u.hj)(d))return{x:f,y:d,width:v,height:y};if((0,u.hj)(p)&&(0,u.hj)(h))return{x:p,y:h,width:v,height:y}}return(0,u.hj)(f)&&(0,u.hj)(d)?{x:f,y:d,width:0,height:0}:(0,u.hj)(t)&&(0,u.hj)(r)?{cx:t,cy:r,startAngle:i||n||0,endAngle:a||n||0,innerRadius:c||0,outerRadius:s||l||o||0,clockWise:g}:e.viewBox?e.viewBox:void 0},k=(e,t,r)=>{if(!e)return null;var i={viewBox:t,labelRef:r};return!0===e?n.createElement(j,v({key:"label-implicit"},i)):(0,u.P2)(e)?n.createElement(j,v({key:"label-implicit",value:e},i)):(0,n.isValidElement)(e)?e.type===j?(0,n.cloneElement)(e,h({key:"label-implicit"},i)):n.createElement(j,v({key:"label-implicit",content:e},i)):g(e)?n.createElement(j,v({key:"label-implicit",content:e},i)):e&&"object"==typeof e?n.createElement(j,v({},e,{key:"label-implicit"},i)):null};j.parseViewBox=P,j.renderCallByParent=function(e,t){var r=!(arguments.length>2)||void 0===arguments[2]||arguments[2];if(!e||!e.children&&r&&!e.label)return null;var{children:i,labelRef:a}=e,u=P(e),l=(0,o.NN)(i,j).map((e,r)=>(0,n.cloneElement)(e,{viewBox:t||u,key:"label-".concat(r)}));return r?[k(e.label,t||u,a),...l]:l}},47625:function(e,t,r){"use strict";r.d(t,{h:function(){return f}});var n=r(61994),i=r(2265),a=r(34926),o=r.n(a),u=r(16630),l=r(1175);function c(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function s(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?c(Object(r),!0).forEach(function(t){var n,i;n=t,i=r[t],(n=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(n))in e?Object.defineProperty(e,n,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[n]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):c(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var f=(0,i.forwardRef)((e,t)=>{var{aspect:r,initialDimension:a={width:-1,height:-1},width:c="100%",height:f="100%",minWidth:d=0,minHeight:p,maxHeight:h,children:v,debounce:y=0,id:g,className:m,onResize:b,style:x={}}=e,w=(0,i.useRef)(null),O=(0,i.useRef)();O.current=b,(0,i.useImperativeHandle)(t,()=>w.current);var[j,P]=(0,i.useState)({containerWidth:a.width,containerHeight:a.height}),k=(0,i.useCallback)((e,t)=>{P(r=>{var n=Math.round(e),i=Math.round(t);return r.containerWidth===n&&r.containerHeight===i?r:{containerWidth:n,containerHeight:i}})},[]);(0,i.useEffect)(()=>{var e=e=>{var t,{width:r,height:n}=e[0].contentRect;k(r,n),null===(t=O.current)||void 0===t||t.call(O,r,n)};y>0&&(e=o()(e,y,{trailing:!0,leading:!1}));var t=new ResizeObserver(e),{width:r,height:n}=w.current.getBoundingClientRect();return k(r,n),t.observe(w.current),()=>{t.disconnect()}},[k,y]);var M=(0,i.useMemo)(()=>{var{containerWidth:e,containerHeight:t}=j;if(e<0||t<0)return null;(0,l.Z)((0,u.hU)(c)||(0,u.hU)(f),"The width(%s) and height(%s) are both fixed numbers,\n       maybe you don't need to use a ResponsiveContainer.",c,f),(0,l.Z)(!r||r>0,"The aspect(%s) must be greater than zero.",r);var n=(0,u.hU)(c)?e:c,a=(0,u.hU)(f)?t:f;return r&&r>0&&(n?a=n/r:a&&(n=a*r),h&&a>h&&(a=h)),(0,l.Z)(n>0||a>0,"The width(%s) and height(%s) of chart should be greater than 0,\n       please check the style of container, or the props width(%s) and height(%s),\n       or add a minWidth(%s) or minHeight(%s) or use aspect(%s) to control the\n       height and width.",n,a,c,f,d,p,r),i.Children.map(v,e=>(0,i.cloneElement)(e,{width:n,height:a,style:s({height:"100%",width:"100%",maxHeight:a,maxWidth:n},e.props.style)}))},[r,v,f,h,p,d,j,c]);return i.createElement("div",{id:g?"".concat(g):void 0,className:(0,n.W)("recharts-responsive-container",m),style:s(s({},x),{},{width:c,height:f,minWidth:d,minHeight:p,maxHeight:h}),ref:w},M)})},58811:function(e,t,r){"use strict";r.d(t,{x:function(){return E}});var n=r(2265),i=r(61994),a=r(16630),o=r(34067),u=r(82944),l=r(4094),c=/(-?\d+(?:\.\d+)?[a-zA-Z%]*)([*/])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,s=/(-?\d+(?:\.\d+)?[a-zA-Z%]*)([+-])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,f=/^px|cm|vh|vw|em|rem|%|mm|in|pt|pc|ex|ch|vmin|vmax|Q$/,d=/(-?\d+(?:\.\d+)?)([a-zA-Z%]+)?/,p={cm:96/2.54,mm:96/25.4,pt:96/72,pc:16,in:96,Q:96/101.6,px:1},h=Object.keys(p);class v{static parse(e){var t,[,r,n]=null!==(t=d.exec(e))&&void 0!==t?t:[];return new v(parseFloat(r),null!=n?n:"")}add(e){return this.unit!==e.unit?new v(NaN,""):new v(this.num+e.num,this.unit)}subtract(e){return this.unit!==e.unit?new v(NaN,""):new v(this.num-e.num,this.unit)}multiply(e){return""!==this.unit&&""!==e.unit&&this.unit!==e.unit?new v(NaN,""):new v(this.num*e.num,this.unit||e.unit)}divide(e){return""!==this.unit&&""!==e.unit&&this.unit!==e.unit?new v(NaN,""):new v(this.num/e.num,this.unit||e.unit)}toString(){return"".concat(this.num).concat(this.unit)}isNaN(){return(0,a.In)(this.num)}constructor(e,t){this.num=e,this.unit=t,this.num=e,this.unit=t,(0,a.In)(e)&&(this.unit=""),""===t||f.test(t)||(this.num=NaN,this.unit=""),h.includes(t)&&(this.num=e*p[t],this.unit="px")}}function y(e){if(e.includes("NaN"))return"NaN";for(var t=e;t.includes("*")||t.includes("/");){var r,[,n,i,a]=null!==(r=c.exec(t))&&void 0!==r?r:[],o=v.parse(null!=n?n:""),u=v.parse(null!=a?a:""),l="*"===i?o.multiply(u):o.divide(u);if(l.isNaN())return"NaN";t=t.replace(c,l.toString())}for(;t.includes("+")||/.-\d+(?:\.\d+)?/.test(t);){var f,[,d,p,h]=null!==(f=s.exec(t))&&void 0!==f?f:[],y=v.parse(null!=d?d:""),g=v.parse(null!=h?h:""),m="+"===p?y.add(g):y.subtract(g);if(m.isNaN())return"NaN";t=t.replace(s,m.toString())}return t}var g=/\(([^()]*)\)/;function m(e){var t=function(e){try{var t;return t=e.replace(/\s+/g,""),t=function(e){for(var t,r=e;null!=(t=g.exec(r));){var[,n]=t;r=r.replace(g,y(n))}return r}(t),t=y(t)}catch(e){return"NaN"}}(e.slice(5,-1));return"NaN"===t?"":t}var b=["x","y","lineHeight","capHeight","scaleToFit","textAnchor","verticalAnchor","fill"],x=["dx","dy","angle","className","breakAll"];function w(){return(w=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function O(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}var j=/[ \f\n\r\t\v\u2028\u2029]+/,P=e=>{var{children:t,breakAll:r,style:n}=e;try{var i=[];(0,a.Rw)(t)||(i=r?t.toString().split(""):t.toString().split(j));var o=i.map(e=>({word:e,width:(0,l.x)(e,n).width})),u=r?0:(0,l.x)("\xa0",n).width;return{wordsWithComputedWidth:o,spaceWidth:u}}catch(e){return null}},k=(e,t,r,n,i)=>{var o,{maxLines:u,children:l,style:c,breakAll:s}=e,f=(0,a.hj)(u),d=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return e.reduce((e,t)=>{var{word:a,width:o}=t,u=e[e.length-1];return u&&(null==n||i||u.width+o+r<Number(n))?(u.words.push(a),u.width+=o+r):e.push({words:[a],width:o}),e},[])},p=d(t),h=e=>e.reduce((e,t)=>e.width>t.width?e:t);if(!f||i||!(p.length>u||h(p).width>Number(n)))return p;for(var v=e=>{var t=d(P({breakAll:s,style:c,children:l.slice(0,e)+"…"}).wordsWithComputedWidth);return[t.length>u||h(t).width>Number(n),t]},y=0,g=l.length-1,m=0;y<=g&&m<=l.length-1;){var b=Math.floor((y+g)/2),[x,w]=v(b-1),[O]=v(b);if(x||O||(y=b+1),x&&O&&(g=b-1),!x&&O){o=w;break}m++}return o||p},M=e=>[{words:(0,a.Rw)(e)?[]:e.toString().split(j)}],S=e=>{var{width:t,scaleToFit:r,children:n,style:i,breakAll:a,maxLines:u}=e;if((t||r)&&!o.x.isSsr){var l=P({breakAll:a,children:n,style:i});if(!l)return M(n);var{wordsWithComputedWidth:c,spaceWidth:s}=l;return k({breakAll:a,children:n,maxLines:u,style:i},c,s,t,r)}return M(n)},A="#808080",E=(0,n.forwardRef)((e,t)=>{var r,{x:o=0,y:l=0,lineHeight:c="1em",capHeight:s="0.71em",scaleToFit:f=!1,textAnchor:d="start",verticalAnchor:p="end",fill:h=A}=e,v=O(e,b),y=(0,n.useMemo)(()=>S({breakAll:v.breakAll,children:v.children,maxLines:v.maxLines,scaleToFit:f,style:v.style,width:v.width}),[v.breakAll,v.children,v.maxLines,f,v.style,v.width]),{dx:g,dy:j,angle:P,className:k,breakAll:M}=v,E=O(v,x);if(!(0,a.P2)(o)||!(0,a.P2)(l))return null;var C=o+((0,a.hj)(g)?g:0),T=l+((0,a.hj)(j)?j:0);switch(p){case"start":r=m("calc(".concat(s,")"));break;case"middle":r=m("calc(".concat((y.length-1)/2," * -").concat(c," + (").concat(s," / 2))"));break;default:r=m("calc(".concat(y.length-1," * -").concat(c,")"))}var D=[];if(f){var N=y[0].width,{width:_}=v;D.push("scale(".concat((0,a.hj)(_)?_/N:1,")"))}return P&&D.push("rotate(".concat(P,", ").concat(C,", ").concat(T,")")),D.length&&(E.transform=D.join(" ")),n.createElement("text",w({},(0,u.L6)(E,!0),{ref:t,x:C,y:T,className:(0,i.W)("recharts-text",k),textAnchor:d,fill:h.includes("url")?A:h}),y.map((e,t)=>{var i=e.words.join(M?"":" ");return n.createElement("tspan",{x:C,dy:0===t?r:c,key:"".concat(i,"-").concat(t)},i)}))});E.displayName="Text"},77719:function(e,t,r){"use strict";r.d(t,{u:function(){return eo}});var n=r(2265),i=r(54887),a=r(31104),o=r.n(a),u=r(61994),l=r(16630);function c(){return(c=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function s(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function f(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?s(Object(r),!0).forEach(function(t){var n,i;n=t,i=r[t],(n=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(n))in e?Object.defineProperty(e,n,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[n]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):s(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function d(e){return Array.isArray(e)&&(0,l.P2)(e[0])&&(0,l.P2)(e[1])?e.join(" ~ "):e}var p=e=>{var{separator:t=" : ",contentStyle:r={},itemStyle:i={},labelStyle:a={},payload:s,formatter:p,itemSorter:h,wrapperClassName:v,labelClassName:y,label:g,labelFormatter:m,accessibilityLayer:b=!1}=e,x=f({margin:0,padding:10,backgroundColor:"#fff",border:"1px solid #ccc",whiteSpace:"nowrap"},r),w=f({margin:0},a),O=!(0,l.Rw)(g),j=O?g:"",P=(0,u.W)("recharts-default-tooltip",v),k=(0,u.W)("recharts-tooltip-label",y);return O&&m&&null!=s&&(j=m(g,s)),n.createElement("div",c({className:P,style:x},b?{role:"status","aria-live":"assertive"}:{}),n.createElement("p",{className:k,style:w},n.isValidElement(j)?j:"".concat(j)),(()=>{if(s&&s.length){var e=(h?o()(s,h):s).map((e,r)=>{if("none"===e.type)return null;var a=e.formatter||p||d,{value:o,name:u}=e,c=o,h=u;if(a){var v=a(o,u,e,r,s);if(Array.isArray(v))[c,h]=v;else{if(null==v)return null;c=v}}var y=f({display:"block",paddingTop:4,paddingBottom:4,color:e.color||"#000"},i);return n.createElement("li",{className:"recharts-tooltip-item",key:"tooltip-item-".concat(r),style:y},(0,l.P2)(h)?n.createElement("span",{className:"recharts-tooltip-item-name"},h):null,(0,l.P2)(h)?n.createElement("span",{className:"recharts-tooltip-item-separator"},t):null,n.createElement("span",{className:"recharts-tooltip-item-value"},c),n.createElement("span",{className:"recharts-tooltip-item-unit"},e.unit||""))});return n.createElement("ul",{className:"recharts-tooltip-item-list",style:{padding:0,margin:0}},e)}return null})())},h="recharts-tooltip-wrapper",v={visibility:"hidden"};function y(e){var{allowEscapeViewBox:t,coordinate:r,key:n,offsetTopLeft:i,position:a,reverseDirection:o,tooltipDimension:u,viewBox:c,viewBoxDimension:s}=e;if(a&&(0,l.hj)(a[n]))return a[n];var f=r[n]-u-(i>0?i:0),d=r[n]+i;if(t[n])return o[n]?f:d;var p=c[n];return null==p?0:o[n]?f<p?Math.max(d,p):Math.max(f,p):null==s?0:d+u>p+s?Math.max(f,p):Math.max(d,p)}function g(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function m(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?g(Object(r),!0).forEach(function(t){b(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):g(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function b(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}class x extends n.PureComponent{componentDidMount(){document.addEventListener("keydown",this.handleKeyDown)}componentWillUnmount(){document.removeEventListener("keydown",this.handleKeyDown)}componentDidUpdate(){var e,t;this.state.dismissed&&((null===(e=this.props.coordinate)||void 0===e?void 0:e.x)!==this.state.dismissedAtCoordinate.x||(null===(t=this.props.coordinate)||void 0===t?void 0:t.y)!==this.state.dismissedAtCoordinate.y)&&(this.state.dismissed=!1)}render(){var{active:e,allowEscapeViewBox:t,animationDuration:r,animationEasing:i,children:a,coordinate:o,hasPayload:c,isAnimationActive:s,offset:f,position:d,reverseDirection:p,useTranslate3d:g,viewBox:b,wrapperStyle:x,lastBoundingBox:w,innerRef:O,hasPortalFromProps:j}=this.props,{cssClasses:P,cssProperties:k}=function(e){var t,r,n,{allowEscapeViewBox:i,coordinate:a,offsetTopLeft:o,position:c,reverseDirection:s,tooltipBox:f,useTranslate3d:d,viewBox:p}=e;return{cssProperties:f.height>0&&f.width>0&&a?function(e){var{translateX:t,translateY:r,useTranslate3d:n}=e;return{transform:n?"translate3d(".concat(t,"px, ").concat(r,"px, 0)"):"translate(".concat(t,"px, ").concat(r,"px)")}}({translateX:r=y({allowEscapeViewBox:i,coordinate:a,key:"x",offsetTopLeft:o,position:c,reverseDirection:s,tooltipDimension:f.width,viewBox:p,viewBoxDimension:p.width}),translateY:n=y({allowEscapeViewBox:i,coordinate:a,key:"y",offsetTopLeft:o,position:c,reverseDirection:s,tooltipDimension:f.height,viewBox:p,viewBoxDimension:p.height}),useTranslate3d:d}):v,cssClasses:function(e){var{coordinate:t,translateX:r,translateY:n}=e;return(0,u.W)(h,{["".concat(h,"-right")]:(0,l.hj)(r)&&t&&(0,l.hj)(t.x)&&r>=t.x,["".concat(h,"-left")]:(0,l.hj)(r)&&t&&(0,l.hj)(t.x)&&r<t.x,["".concat(h,"-bottom")]:(0,l.hj)(n)&&t&&(0,l.hj)(t.y)&&n>=t.y,["".concat(h,"-top")]:(0,l.hj)(n)&&t&&(0,l.hj)(t.y)&&n<t.y})}({translateX:r,translateY:n,coordinate:a})}}({allowEscapeViewBox:t,coordinate:o,offsetTopLeft:f,position:d,reverseDirection:p,tooltipBox:{height:w.height,width:w.width},useTranslate3d:g,viewBox:b}),M=j?{}:m(m({transition:s&&e?"transform ".concat(r,"ms ").concat(i):void 0},k),{},{pointerEvents:"none",visibility:!this.state.dismissed&&e&&c?"visible":"hidden",position:"absolute",top:0,left:0}),S=m(m({},M),{},{visibility:!this.state.dismissed&&e&&c?"visible":"hidden"},x);return n.createElement("div",{xmlns:"http://www.w3.org/1999/xhtml",tabIndex:-1,className:P,style:S,ref:O},a)}constructor(){super(...arguments),b(this,"state",{dismissed:!1,dismissedAtCoordinate:{x:0,y:0}}),b(this,"handleKeyDown",e=>{if("Escape"===e.key){var t,r,n,i;this.setState({dismissed:!0,dismissedAtCoordinate:{x:null!==(t=null===(r=this.props.coordinate)||void 0===r?void 0:r.x)&&void 0!==t?t:0,y:null!==(n=null===(i=this.props.coordinate)||void 0===i?void 0:i.y)&&void 0!==n?n:0}})}})}}var w=r(34067),O=r(36841),j=r.n(O),P=r(35953),k=r(14847),M=r(57165),S=r(82944),A=["x","y","top","left","width","height","className"];function E(){return(E=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function C(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}var T=(e,t,r,n,i,a)=>"M".concat(e,",").concat(i,"v").concat(n,"M").concat(a,",").concat(t,"h").concat(r),D=e=>{var{x:t=0,y:r=0,top:i=0,left:a=0,width:o=0,height:c=0,className:s}=e,f=function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?C(Object(r),!0).forEach(function(t){var n,i;n=t,i=r[t],(n=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(n))in e?Object.defineProperty(e,n,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[n]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):C(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}({x:t,y:r,top:i,left:a,width:o,height:c},function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,A));return(0,l.hj)(t)&&(0,l.hj)(r)&&(0,l.hj)(o)&&(0,l.hj)(c)&&(0,l.hj)(i)&&(0,l.hj)(a)?n.createElement("path",E({},(0,S.L6)(f,!0),{className:(0,u.W)("recharts-cross",s),d:T(t,r,o,c,i,a)})):null},N=r(73649),_=r(39206);function I(e){var{cx:t,cy:r,radius:n,startAngle:i,endAngle:a}=e;return{points:[(0,_.op)(t,r,n,i),(0,_.op)(t,r,n,a)],cx:t,cy:r,radius:n,startAngle:i,endAngle:a}}var R=r(60474),L=r(39040),z=r(49037),F=r(31944);function W(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function B(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?W(Object(r),!0).forEach(function(t){var n,i;n=t,i=r[t],(n=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(n))in e?Object.defineProperty(e,n,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[n]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):W(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var K=()=>(0,L.C)(F.zv),Z=()=>{var e=K(),t=(0,L.C)(F.WQ),r=(0,L.C)(F.ri);return(0,z.zT)(B(B({},e),{},{scale:r}),t)},U=r(84461);function V(){return(V=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function $(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function Y(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?$(Object(r),!0).forEach(function(t){var n,i;n=t,i=r[t],(n=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(n))in e?Object.defineProperty(e,n,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[n]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):$(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function H(e){var t,r,i,{coordinate:a,payload:o,index:l,offset:c,tooltipAxisBandSize:s,layout:f,cursor:d,tooltipEventType:p,chartName:h}=e;if(!d||!a||"ScatterChart"!==h&&"axis"!==p)return null;if("ScatterChart"===h)r=a,i=D;else if("BarChart"===h)t=s/2,r={stroke:"none",fill:"#ccc",x:"horizontal"===f?a.x-t:c.left+.5,y:"horizontal"===f?c.top+.5:a.y-t,width:"horizontal"===f?s:c.width-1,height:"horizontal"===f?c.height-1:s},i=N.A;else if("radial"===f){var{cx:v,cy:y,radius:g,startAngle:m,endAngle:b}=I(a);r={cx:v,cy:y,startAngle:m,endAngle:b,innerRadius:g,outerRadius:g},i=R.L}else r={points:function(e,t,r){var n,i,a,o;if("horizontal"===e)a=n=t.x,i=r.top,o=r.top+r.height;else if("vertical"===e)o=i=t.y,n=r.left,a=r.left+r.width;else if(null!=t.cx&&null!=t.cy){if("centric"!==e)return I(t);var{cx:u,cy:l,innerRadius:c,outerRadius:s,angle:f}=t,d=(0,_.op)(u,l,c,f),p=(0,_.op)(u,l,s,f);n=d.x,i=d.y,a=p.x,o=p.y}return[{x:n,y:i},{x:a,y:o}]}(f,a,c)},i=M.H;var x="object"==typeof d&&"className"in d?d.className:void 0,w=Y(Y(Y(Y({stroke:"#ccc",pointerEvents:"none"},c),r),(0,S.L6)(d,!1)),{},{payload:o,payloadIndex:l,className:(0,u.W)("recharts-tooltip-cursor",x)});return(0,n.isValidElement)(d)?(0,n.cloneElement)(d,w):(0,n.createElement)(i,w)}function q(e){var t=Z(),r=(0,P.qD)(),i=(0,P.vn)(),a=(0,U.AC)();return n.createElement(H,V({},e,{coordinate:e.coordinate,index:e.index,payload:e.payload,offset:r,layout:i,tooltipAxisBandSize:t,chartName:a}))}var X=r(72321),G=r(64725),Q=r(318),J=r(46302),ee=r(40130);function et(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function er(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?et(Object(r),!0).forEach(function(t){var n,i;n=t,i=r[t],(n=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(n))in e?Object.defineProperty(e,n,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[n]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):et(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function en(e){return e.dataKey}var ei=[],ea={allowEscapeViewBox:{x:!1,y:!1},animationDuration:400,animationEasing:"ease",axisId:0,contentStyle:{},cursor:!0,filterNull:!0,isAnimationActive:!w.x.isSsr,itemSorter:"name",itemStyle:{},labelStyle:{},offset:10,reverseDirection:{x:!1,y:!1},separator:" : ",trigger:"hover",useTranslate3d:!1,wrapperStyle:{}};function eo(e){var t,r,a=(0,ee.j)(e,ea),{active:o,allowEscapeViewBox:u,animationDuration:l,animationEasing:c,content:s,filterNull:f,isAnimationActive:d,offset:h,payloadUniqBy:v,position:y,reverseDirection:g,useTranslate3d:m,wrapperStyle:b,cursor:w,shared:O,trigger:M,defaultIndex:S,portal:A,axisId:E}=a,C=(0,L.T)(),T="number"==typeof S?String(S):S;(0,n.useEffect)(()=>{C((0,G.PD)({shared:O,trigger:M,axisId:E,active:o,defaultIndex:T}))},[C,O,M,E,o,T]);var D=(0,P.d2)(),N=(0,k.F)(),_=(0,J.Y4)(O),{activeIndex:I,isActive:R}=(0,L.C)(e=>(0,U.oo)(e,_,M,T)),z=(0,L.C)(e=>(0,U.TT)(e,_,M,T)),F=(0,L.C)(e=>(0,U.i)(e,_,M,T)),W=(0,L.C)(e=>(0,U.ck)(e,_,M,T)),B=(0,X.C)(),K=null!=o?o:R,[Z,V]=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],[t,r]=(0,n.useState)({height:0,left:0,top:0,width:0}),i=(0,n.useCallback)(e=>{if(null!=e){var n=e.getBoundingClientRect(),i={height:n.height,left:n.left,top:n.top,width:n.width};(Math.abs(i.height-t.height)>1||Math.abs(i.left-t.left)>1||Math.abs(i.top-t.top)>1||Math.abs(i.width-t.width)>1)&&r({height:i.height,left:i.left,top:i.top,width:i.width})}},[t.width,t.height,t.top,t.left,...e]);return[t,i]}([z,K]),$="axis"===_?F:void 0;(0,Q.Fg)(_,M,W,$,I,K);var Y=null!=A?A:B;if(null==Y)return null;var H=null!=z?z:ei;K||(H=ei),f&&H.length&&(t=z.filter(e=>null!=e.value&&(!0!==e.hide||a.includeHidden)),H=!0===v?j()(t,en):"function"==typeof v?j()(t,v):t);var et=H.length>0,eo=n.createElement(x,{allowEscapeViewBox:u,animationDuration:l,animationEasing:c,isAnimationActive:d,active:K,coordinate:W,hasPayload:et,offset:h,position:y,reverseDirection:g,useTranslate3d:m,viewBox:D,wrapperStyle:b,lastBoundingBox:Z,innerRef:V,hasPortalFromProps:!!A},(r=er(er({},a),{},{payload:H,label:$,active:K,coordinate:W,accessibilityLayer:N}),n.isValidElement(s)?n.cloneElement(s,r):"function"==typeof s?n.createElement(s,r):n.createElement(p,r)));return n.createElement(n.Fragment,null,(0,i.createPortal)(eo,Y),K&&n.createElement(q,{cursor:w,tooltipEventType:_,coordinate:W,payload:z,index:I}))}},9841:function(e,t,r){"use strict";r.d(t,{m:function(){return l}});var n=r(2265),i=r(61994),a=r(82944),o=["children","className"];function u(){return(u=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}var l=n.forwardRef((e,t)=>{var{children:r,className:l}=e,c=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,o),s=(0,i.W)("recharts-layer",l);return n.createElement("g",u({className:s},(0,a.L6)(c,!0),{ref:t}),r)})},58735:function(e,t,r){"use strict";r.d(t,{W:function(){return a}});var n=r(2265),i=(0,n.createContext)(null),a=()=>null!=(0,n.useContext)(i)},14847:function(e,t,r){"use strict";r.d(t,{F:function(){return i}});var n=r(39040),i=()=>(0,n.C)(e=>e.rootProps.accessibilityLayer)},1196:function(e,t,r){"use strict";r.d(t,{gt:function(){return u}});var n=r(2265),i=r(39173),a=r(39040),o=r(58735),u=e=>{var{chartData:t}=e,r=(0,a.T)(),u=(0,o.W)();return(0,n.useEffect)(()=>u?()=>{}:(r((0,i.zR)(t)),()=>{r((0,i.zR)(void 0))}),[t,r,u]),null}},35953:function(e,t,r){"use strict";r.d(t,{Mw:function(){return d},d2:function(){return l},qD:function(){return s},rE:function(){return p},vn:function(){return h},zn:function(){return f}}),r(2265);var n=r(39040),i=r(36289),a=r(60152),o=r(58735),u=r(2431),l=()=>{var e,t=(0,o.W)(),r=(0,n.C)(i.nd),a=(0,n.C)(u.V),l=null===(e=(0,n.C)(u.F))||void 0===e?void 0:e.padding;return t&&a&&l?{width:a.width-l.left-l.right,height:a.height-l.top-l.bottom,x:l.left,y:l.top}:r},c={top:0,bottom:0,left:0,right:0,width:0,height:0,brushBottom:0},s=()=>{var e;return null!==(e=(0,n.C)(i.bX))&&void 0!==e?e:c},f=()=>(0,n.C)(a.RD),d=()=>(0,n.C)(a.d_),p=e=>e.layout.layoutType,h=()=>(0,n.C)(p)},44296:function(e,t,r){"use strict";r.d(t,{Df:function(){return a},nC:function(){return u},oQ:function(){return o}});var n=r(39040),i=r(64725),a=(e,t)=>{var r=(0,n.T)();return(n,a)=>o=>{null==e||e(n,a,o),r((0,i.M1)({activeIndex:String(a),activeDataKey:t,activeCoordinate:n.tooltipPosition}))}},o=e=>{var t=(0,n.T)();return(r,n)=>a=>{null==e||e(r,n,a),t((0,i.Vg)())}},u=(e,t)=>{var r=(0,n.T)();return(n,a)=>o=>{null==e||e(n,a,o),r((0,i.O_)({activeIndex:String(a),activeDataKey:t,activeCoordinate:n.tooltipPosition}))}}},72321:function(e,t,r){"use strict";r.d(t,{C:function(){return a},E:function(){return i}});var n=r(2265),i=(0,n.createContext)(null),a=()=>(0,n.useContext)(i)},10062:function(e,t,r){"use strict";r.d(t,{b:function(){return ej},w:function(){return eg}});var n=r(2265),i=r(15870),a=r.n(i),o=r(61994),u=r(92713),l=r(22932),c=r(36289),s=r(49037),f=r(98628),d=r(35953),p=r(40304),h=r(56462),v=r(33968),y=e=>e.graphicalItems.polarItems,g=(0,u.P1)([p.z,h.l],f.YZ),m=(0,u.P1)([y,f.fW,g],f.$B),b=(0,u.P1)([m],f.bU),x=(0,u.P1)([b,l.RV],f.tZ),w=(0,u.P1)([x,f.fW,m],f.UA),O=(0,u.P1)([x,f.fW,m],(e,t,r)=>r.length>0?e.flatMap(e=>r.flatMap(r=>{var n;return{value:(0,s.F$)(e,null!==(n=t.dataKey)&&void 0!==n?n:r.dataKey),errorDomain:[]}})).filter(Boolean):(null==t?void 0:t.dataKey)!=null?e.map(e=>({value:(0,s.F$)(e,t.dataKey),errorDomain:[]})):e.map(e=>({value:e,errorDomain:[]}))),j=()=>void 0,P=(0,u.P1)([f.fW,f.KB,j,O,j],f.E8),k=(0,u.P1)([f.fW,d.rE,x,w,v.Qw,p.z,P],f.l_),M=(0,u.P1)([k,f.fW,f.cV],f.vb);function S(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function A(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?S(Object(r),!0).forEach(function(t){var n,i;n=t,i=r[t],(n=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(n))in e?Object.defineProperty(e,n,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[n]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):S(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}(0,u.P1)([f.fW,k,M,p.z],f.kO);var E=(e,t)=>t,C=[],T=(e,t,r)=>(null==r?void 0:r.length)===0?C:r,D=(0,u.P1)([l.RV,E,T],(e,t,r)=>{var n,{chartData:i}=e;if((n=(null==t?void 0:t.data)!=null&&t.data.length>0?t.data:i)&&n.length||null==r||(n=r.map(e=>A(A({},t.presentationProps),e.props))),null!=n)return n}),N=(0,u.P1)([D,E,T],(e,t,r)=>{if(null!=e)return e.map((e,n)=>{var i,a,o=(0,s.F$)(e,t.nameKey,t.name);return a=null!=r&&null!==(i=r[n])&&void 0!==i&&null!==(i=i.props)&&void 0!==i&&i.fill?r[n].props.fill:"object"==typeof e&&null!=e&&"fill"in e?e.fill:t.fill,{value:(0,s.hn)(o,t.dataKey),color:a,payload:e,type:t.legendType}})}),_=(0,u.P1)([y,E],(e,t)=>{if(e.some(e=>"pie"===e.type&&t.dataKey===e.dataKey&&t.data===e.data))return t}),I=(0,u.P1)([D,_,T,c.bX],(e,t,r,n)=>{if(null!=t&&null!=e)return eg({offset:n,pieSettings:t,displayedData:e,cells:r})}),R=r(39040),L=r(13790),z=r(9841),F=r(57165),W=r(58811),B=r(20407),K=r(82944),Z=r(34067),U=r(39206),V=r(16630),$=r(41637),Y=r(80503),H=r(44296),q=r(35623),X=r(31944),G=r(62658),Q=r(78487),J=r(59087),ee=r(40130),et=r(46595),er=["onMouseEnter","onClick","onMouseLeave"];function en(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function ei(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?en(Object(r),!0).forEach(function(t){ea(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):en(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function ea(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function eo(){return(eo=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function eu(e){var t=(0,n.useMemo)(()=>(0,K.L6)(e,!1),[e]),r=(0,n.useMemo)(()=>(0,K.NN)(e.children,B.b),[e.children]),i=(0,n.useMemo)(()=>({name:e.name,nameKey:e.nameKey,tooltipType:e.tooltipType,data:e.data,dataKey:e.dataKey,cx:e.cx,cy:e.cy,startAngle:e.startAngle,endAngle:e.endAngle,minAngle:e.minAngle,paddingAngle:e.paddingAngle,innerRadius:e.innerRadius,outerRadius:e.outerRadius,cornerRadius:e.cornerRadius,legendType:e.legendType,fill:e.fill,presentationProps:t}),[e.cornerRadius,e.cx,e.cy,e.data,e.dataKey,e.endAngle,e.innerRadius,e.minAngle,e.name,e.nameKey,e.outerRadius,e.paddingAngle,e.startAngle,e.tooltipType,e.legendType,e.fill,t]),a=(0,R.C)(e=>N(e,i,r));return n.createElement(G.t,{legendPayload:a})}function el(e){var{dataKey:t,nameKey:r,sectors:n,stroke:i,strokeWidth:a,fill:o,name:u,hide:l,tooltipType:c}=e;return{dataDefinedOnItem:null==n?void 0:n.map(e=>e.tooltipPayload),positions:null==n?void 0:n.map(e=>e.tooltipPosition),settings:{stroke:i,strokeWidth:a,fill:o,dataKey:t,nameKey:r,name:(0,s.hn)(u,t),hide:l,type:c,color:o,unit:""}}}var ec=(e,t)=>e>t?"start":e<t?"end":"middle",es=(e,t,r)=>"function"==typeof t?t(e):(0,V.h1)(t,r,.8*r),ef=(e,t,r)=>{var{top:n,left:i,width:a,height:o}=t,u=(0,U.$4)(a,o),l=i+(0,V.h1)(e.cx,a,a/2),c=n+(0,V.h1)(e.cy,o,o/2);return{cx:l,cy:c,innerRadius:(0,V.h1)(e.innerRadius,u,0),outerRadius:es(r,e.outerRadius,u),maxRadius:e.maxRadius||Math.sqrt(a*a+o*o)/2}},ed=(e,t)=>(0,V.uY)(t-e)*Math.min(Math.abs(t-e),360),ep=(e,t)=>{if(n.isValidElement(e))return n.cloneElement(e,t);if("function"==typeof e)return e(t);var r=(0,o.W)("recharts-pie-label-line","boolean"!=typeof e?e.className:"");return n.createElement(F.H,eo({},t,{type:"linear",className:r}))},eh=(e,t,r)=>{if(n.isValidElement(e))return n.cloneElement(e,t);var i=r;if("function"==typeof e&&(i=e(t),n.isValidElement(i)))return i;var a=(0,o.W)("recharts-pie-label-text","boolean"!=typeof e&&"function"!=typeof e?e.className:"");return n.createElement(W.x,eo({},t,{alignmentBaseline:"middle",className:a}),i)};function ev(e){var{sectors:t,props:r,showLabels:i}=e,{label:a,labelLine:o,dataKey:u}=r;if(!i||!a||!t)return null;var l=(0,K.L6)(r,!1),c=(0,K.L6)(a,!1),f=(0,K.L6)(o,!1),d="object"==typeof a&&"offsetRadius"in a&&a.offsetRadius||20,p=t.map((e,t)=>{var r=(e.startAngle+e.endAngle)/2,i=(0,U.op)(e.cx,e.cy,e.outerRadius+d,r),p=ei(ei(ei(ei({},l),e),{},{stroke:"none"},c),{},{index:t,textAnchor:ec(i.x,e.cx)},i),h=ei(ei(ei(ei({},l),e),{},{fill:"none",stroke:e.fill},f),{},{index:t,points:[(0,U.op)(e.cx,e.cy,e.outerRadius,r),i],key:"line"});return n.createElement(z.m,{key:"label-".concat(e.startAngle,"-").concat(e.endAngle,"-").concat(e.midAngle,"-").concat(t)},o&&ep(o,h),eh(a,p,(0,s.F$)(e,u)))});return n.createElement(z.m,{className:"recharts-pie-labels"},p)}function ey(e){var{sectors:t,activeShape:r,inactiveShape:i,allOtherPieProps:a,showLabels:o}=e,u=(0,R.C)(X.Ve),{onMouseEnter:l,onClick:c,onMouseLeave:s}=a,f=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(a,er),d=(0,H.Df)(l,a.dataKey),p=(0,H.oQ)(s),h=(0,H.nC)(c,a.dataKey);return null==t?null:n.createElement(n.Fragment,null,t.map((e,o)=>{if((null==e?void 0:e.startAngle)===0&&(null==e?void 0:e.endAngle)===0&&1!==t.length)return null;var l=r&&String(o)===u,c=l?r:u?i:null,s=ei(ei({},e),{},{stroke:e.stroke,tabIndex:-1,[Q.Gh]:o,[Q.aN]:a.dataKey});return n.createElement(z.m,eo({tabIndex:-1,className:"recharts-pie-sector"},(0,$.bw)(f,e,o),{onMouseEnter:d(e,o),onMouseLeave:p(e,o),onClick:h(e,o),key:"sector-".concat(null==e?void 0:e.startAngle,"-").concat(null==e?void 0:e.endAngle,"-").concat(e.midAngle,"-").concat(o)}),n.createElement(Y.b,eo({option:c,isActive:l,shapeType:"sector"},s)))}),n.createElement(ev,{sectors:t,props:a,showLabels:o}))}function eg(e){var t,r,n,{pieSettings:i,displayedData:a,cells:o,offset:u}=e,{cornerRadius:l,startAngle:c,endAngle:f,dataKey:d,nameKey:p,tooltipType:h}=i,v=Math.abs(i.minAngle),y=ed(c,f),g=Math.abs(y),m=a.length<=1?0:null!==(t=i.paddingAngle)&&void 0!==t?t:0,b=a.filter(e=>0!==(0,s.F$)(e,d,0)).length,x=g-b*v-(g>=360?b:b-1)*m,w=a.reduce((e,t)=>{var r=(0,s.F$)(t,d,0);return e+((0,V.hj)(r)?r:0)},0);return w>0&&(r=a.map((e,t)=>{var r,a=(0,s.F$)(e,d,0),f=(0,s.F$)(e,p,t),g=ef(i,u,e),b=((0,V.hj)(a)?a:0)/w,O=ei(ei({},e),o&&o[t]&&o[t].props),j=(r=t?n.endAngle+(0,V.uY)(y)*m*(0!==a?1:0):c)+(0,V.uY)(y)*((0!==a?v:0)+b*x),P=(r+j)/2,k=(g.innerRadius+g.outerRadius)/2,M=[{name:f,value:a,payload:O,dataKey:d,type:h}],S=(0,U.op)(g.cx,g.cy,k,P);return n=ei(ei(ei(ei({},i.presentationProps),{},{percent:b,cornerRadius:l,name:f,tooltipPayload:M,midAngle:P,middleRadius:k,tooltipPosition:S},O),g),{},{value:(0,s.F$)(e,d),startAngle:r,endAngle:j,payload:O,paddingAngle:(0,V.uY)(y)*m})})),r}function em(e){var{props:t,previousSectorsRef:r}=e,{sectors:i,isAnimationActive:o,animationBegin:u,animationDuration:l,animationEasing:c,activeShape:s,inactiveShape:f,onAnimationStart:d,onAnimationEnd:p}=t,h=(0,J.i)(t,"recharts-pie-"),v=r.current,[y,g]=(0,n.useState)(!0),m=(0,n.useCallback)(()=>{"function"==typeof p&&p(),g(!1)},[p]),b=(0,n.useCallback)(()=>{"function"==typeof d&&d(),g(!0)},[d]);return n.createElement(et.r,{begin:u,duration:l,isActive:o,easing:c,from:{t:0},to:{t:1},onAnimationStart:b,onAnimationEnd:m,key:h},e=>{var{t:o}=e,u=[],l=(i&&i[0]).startAngle;return i.forEach((e,t)=>{var r=v&&v[t],n=t>0?a()(e,"paddingAngle",0):0;if(r){var i=(0,V.k4)(r.endAngle-r.startAngle,e.endAngle-e.startAngle),c=ei(ei({},e),{},{startAngle:l+n,endAngle:l+i(o)+n});u.push(c),l=c.endAngle}else{var{endAngle:s,startAngle:f}=e,d=(0,V.k4)(0,s-f)(o),p=ei(ei({},e),{},{startAngle:l+n,endAngle:l+d+n});u.push(p),l=p.endAngle}}),r.current=u,n.createElement(z.m,null,n.createElement(ey,{sectors:u,activeShape:s,inactiveShape:f,allOtherPieProps:t,showLabels:!y}))})}function eb(e){var{sectors:t,isAnimationActive:r,activeShape:i,inactiveShape:a}=e,o=(0,n.useRef)(null),u=o.current;return r&&t&&t.length&&(!u||u!==t)?n.createElement(em,{props:e,previousSectorsRef:o}):n.createElement(ey,{sectors:t,activeShape:i,inactiveShape:a,allOtherPieProps:e,showLabels:!0})}function ex(e){var{hide:t,className:r,rootTabIndex:i}=e,a=(0,o.W)("recharts-pie",r);return t?null:n.createElement(z.m,{tabIndex:i,className:a},n.createElement(eb,e))}var ew={animationBegin:400,animationDuration:1500,animationEasing:"ease",cx:"50%",cy:"50%",dataKey:"value",endAngle:360,fill:"#808080",hide:!1,innerRadius:0,isAnimationActive:!Z.x.isSsr,labelLine:!0,legendType:"rect",minAngle:0,nameKey:"name",outerRadius:"80%",paddingAngle:0,rootTabIndex:0,startAngle:0,stroke:"#fff"};function eO(e){var t=(0,ee.j)(e,ew),r=(0,n.useMemo)(()=>(0,K.NN)(e.children,B.b),[e.children]),i=(0,K.L6)(t,!1),a=(0,n.useMemo)(()=>({name:t.name,nameKey:t.nameKey,tooltipType:t.tooltipType,data:t.data,dataKey:t.dataKey,cx:t.cx,cy:t.cy,startAngle:t.startAngle,endAngle:t.endAngle,minAngle:t.minAngle,paddingAngle:t.paddingAngle,innerRadius:t.innerRadius,outerRadius:t.outerRadius,cornerRadius:t.cornerRadius,legendType:t.legendType,fill:t.fill,presentationProps:i}),[t.cornerRadius,t.cx,t.cy,t.data,t.dataKey,t.endAngle,t.innerRadius,t.minAngle,t.name,t.nameKey,t.outerRadius,t.paddingAngle,t.startAngle,t.tooltipType,t.legendType,t.fill,i]),o=(0,R.C)(e=>I(e,a,r));return n.createElement(n.Fragment,null,n.createElement(q.k,{fn:el,args:ei(ei({},t),{},{sectors:o})}),n.createElement(ex,eo({},t,{sectors:o})))}class ej extends n.PureComponent{render(){return n.createElement(n.Fragment,null,n.createElement(L.E,{data:this.props.data,dataKey:this.props.dataKey,hide:this.props.hide,angleAxisId:0,radiusAxisId:0,stackId:void 0,barSize:void 0,type:"pie"}),n.createElement(eu,this.props),n.createElement(eO,this.props),this.props.children)}constructor(){super(...arguments),ea(this,"id",(0,V.EL)("recharts-pie-"))}}ea(ej,"displayName","Pie"),ea(ej,"defaultProps",ew)},57165:function(e,t,r){"use strict";r.d(t,{H:function(){return Z}});var n=r(2265);function i(){}function a(e,t,r){e._context.bezierCurveTo((2*e._x0+e._x1)/3,(2*e._y0+e._y1)/3,(e._x0+2*e._x1)/3,(e._y0+2*e._y1)/3,(e._x0+4*e._x1+t)/6,(e._y0+4*e._y1+r)/6)}function o(e){this._context=e}function u(e){this._context=e}function l(e){this._context=e}o.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){switch(this._point){case 3:a(this,this._x1,this._y1);case 2:this._context.lineTo(this._x1,this._y1)}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){switch(e=+e,t=+t,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;break;case 2:this._point=3,this._context.lineTo((5*this._x0+this._x1)/6,(5*this._y0+this._y1)/6);default:a(this,e,t)}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t}},u.prototype={areaStart:i,areaEnd:i,lineStart:function(){this._x0=this._x1=this._x2=this._x3=this._x4=this._y0=this._y1=this._y2=this._y3=this._y4=NaN,this._point=0},lineEnd:function(){switch(this._point){case 1:this._context.moveTo(this._x2,this._y2),this._context.closePath();break;case 2:this._context.moveTo((this._x2+2*this._x3)/3,(this._y2+2*this._y3)/3),this._context.lineTo((this._x3+2*this._x2)/3,(this._y3+2*this._y2)/3),this._context.closePath();break;case 3:this.point(this._x2,this._y2),this.point(this._x3,this._y3),this.point(this._x4,this._y4)}},point:function(e,t){switch(e=+e,t=+t,this._point){case 0:this._point=1,this._x2=e,this._y2=t;break;case 1:this._point=2,this._x3=e,this._y3=t;break;case 2:this._point=3,this._x4=e,this._y4=t,this._context.moveTo((this._x0+4*this._x1+e)/6,(this._y0+4*this._y1+t)/6);break;default:a(this,e,t)}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t}},l.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){(this._line||0!==this._line&&3===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){switch(e=+e,t=+t,this._point){case 0:this._point=1;break;case 1:this._point=2;break;case 2:this._point=3;var r=(this._x0+4*this._x1+e)/6,n=(this._y0+4*this._y1+t)/6;this._line?this._context.lineTo(r,n):this._context.moveTo(r,n);break;case 3:this._point=4;default:a(this,e,t)}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t}};class c{constructor(e,t){this._context=e,this._x=t}areaStart(){this._line=0}areaEnd(){this._line=NaN}lineStart(){this._point=0}lineEnd(){(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line}point(e,t){switch(e=+e,t=+t,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;default:this._x?this._context.bezierCurveTo(this._x0=(this._x0+e)/2,this._y0,this._x0,t,e,t):this._context.bezierCurveTo(this._x0,this._y0=(this._y0+t)/2,e,this._y0,e,t)}this._x0=e,this._y0=t}}function s(e){this._context=e}function f(e){this._context=e}function d(e){return new f(e)}function p(e,t,r){var n=e._x1-e._x0,i=t-e._x1,a=(e._y1-e._y0)/(n||i<0&&-0),o=(r-e._y1)/(i||n<0&&-0);return((a<0?-1:1)+(o<0?-1:1))*Math.min(Math.abs(a),Math.abs(o),.5*Math.abs((a*i+o*n)/(n+i)))||0}function h(e,t){var r=e._x1-e._x0;return r?(3*(e._y1-e._y0)/r-t)/2:t}function v(e,t,r){var n=e._x0,i=e._y0,a=e._x1,o=e._y1,u=(a-n)/3;e._context.bezierCurveTo(n+u,i+u*t,a-u,o-u*r,a,o)}function y(e){this._context=e}function g(e){this._context=new m(e)}function m(e){this._context=e}function b(e){this._context=e}function x(e){var t,r,n=e.length-1,i=Array(n),a=Array(n),o=Array(n);for(i[0]=0,a[0]=2,o[0]=e[0]+2*e[1],t=1;t<n-1;++t)i[t]=1,a[t]=4,o[t]=4*e[t]+2*e[t+1];for(i[n-1]=2,a[n-1]=7,o[n-1]=8*e[n-1]+e[n],t=1;t<n;++t)r=i[t]/a[t-1],a[t]-=r,o[t]-=r*o[t-1];for(i[n-1]=o[n-1]/a[n-1],t=n-2;t>=0;--t)i[t]=(o[t]-i[t+1])/a[t];for(t=0,a[n-1]=(e[n]+i[n-1])/2;t<n-1;++t)a[t]=2*e[t+1]-i[t+1];return[i,a]}function w(e,t){this._context=e,this._t=t}s.prototype={areaStart:i,areaEnd:i,lineStart:function(){this._point=0},lineEnd:function(){this._point&&this._context.closePath()},point:function(e,t){e=+e,t=+t,this._point?this._context.lineTo(e,t):(this._point=1,this._context.moveTo(e,t))}},f.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._point=0},lineEnd:function(){(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){switch(e=+e,t=+t,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;default:this._context.lineTo(e,t)}}},y.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=this._t0=NaN,this._point=0},lineEnd:function(){switch(this._point){case 2:this._context.lineTo(this._x1,this._y1);break;case 3:v(this,this._t0,h(this,this._t0))}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){var r=NaN;if(t=+t,(e=+e)!==this._x1||t!==this._y1){switch(this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;break;case 2:this._point=3,v(this,h(this,r=p(this,e,t)),r);break;default:v(this,this._t0,r=p(this,e,t))}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t,this._t0=r}}},(g.prototype=Object.create(y.prototype)).point=function(e,t){y.prototype.point.call(this,t,e)},m.prototype={moveTo:function(e,t){this._context.moveTo(t,e)},closePath:function(){this._context.closePath()},lineTo:function(e,t){this._context.lineTo(t,e)},bezierCurveTo:function(e,t,r,n,i,a){this._context.bezierCurveTo(t,e,n,r,a,i)}},b.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=[],this._y=[]},lineEnd:function(){var e=this._x,t=this._y,r=e.length;if(r){if(this._line?this._context.lineTo(e[0],t[0]):this._context.moveTo(e[0],t[0]),2===r)this._context.lineTo(e[1],t[1]);else for(var n=x(e),i=x(t),a=0,o=1;o<r;++a,++o)this._context.bezierCurveTo(n[0][a],i[0][a],n[1][a],i[1][a],e[o],t[o])}(this._line||0!==this._line&&1===r)&&this._context.closePath(),this._line=1-this._line,this._x=this._y=null},point:function(e,t){this._x.push(+e),this._y.push(+t)}},w.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=this._y=NaN,this._point=0},lineEnd:function(){0<this._t&&this._t<1&&2===this._point&&this._context.lineTo(this._x,this._y),(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line>=0&&(this._t=1-this._t,this._line=1-this._line)},point:function(e,t){switch(e=+e,t=+t,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;default:if(this._t<=0)this._context.lineTo(this._x,t),this._context.lineTo(e,t);else{var r=this._x*(1-this._t)+e*this._t;this._context.lineTo(r,this._y),this._context.lineTo(r,t)}}this._x=e,this._y=t}};var O=r(22516),j=r(76115),P=r(67790);function k(e){return e[0]}function M(e){return e[1]}function S(e,t){var r=(0,j.Z)(!0),n=null,i=d,a=null,o=(0,P.d)(u);function u(u){var l,c,s,f=(u=(0,O.Z)(u)).length,d=!1;for(null==n&&(a=i(s=o())),l=0;l<=f;++l)!(l<f&&r(c=u[l],l,u))===d&&((d=!d)?a.lineStart():a.lineEnd()),d&&a.point(+e(c,l,u),+t(c,l,u));if(s)return a=null,s+""||null}return e="function"==typeof e?e:void 0===e?k:(0,j.Z)(e),t="function"==typeof t?t:void 0===t?M:(0,j.Z)(t),u.x=function(t){return arguments.length?(e="function"==typeof t?t:(0,j.Z)(+t),u):e},u.y=function(e){return arguments.length?(t="function"==typeof e?e:(0,j.Z)(+e),u):t},u.defined=function(e){return arguments.length?(r="function"==typeof e?e:(0,j.Z)(!!e),u):r},u.curve=function(e){return arguments.length?(i=e,null!=n&&(a=i(n)),u):i},u.context=function(e){return arguments.length?(null==e?n=a=null:a=i(n=e),u):n},u}function A(e,t,r){var n=null,i=(0,j.Z)(!0),a=null,o=d,u=null,l=(0,P.d)(c);function c(c){var s,f,d,p,h,v=(c=(0,O.Z)(c)).length,y=!1,g=Array(v),m=Array(v);for(null==a&&(u=o(h=l())),s=0;s<=v;++s){if(!(s<v&&i(p=c[s],s,c))===y){if(y=!y)f=s,u.areaStart(),u.lineStart();else{for(u.lineEnd(),u.lineStart(),d=s-1;d>=f;--d)u.point(g[d],m[d]);u.lineEnd(),u.areaEnd()}}y&&(g[s]=+e(p,s,c),m[s]=+t(p,s,c),u.point(n?+n(p,s,c):g[s],r?+r(p,s,c):m[s]))}if(h)return u=null,h+""||null}function s(){return S().defined(i).curve(o).context(a)}return e="function"==typeof e?e:void 0===e?k:(0,j.Z)(+e),t="function"==typeof t?t:void 0===t?(0,j.Z)(0):(0,j.Z)(+t),r="function"==typeof r?r:void 0===r?M:(0,j.Z)(+r),c.x=function(t){return arguments.length?(e="function"==typeof t?t:(0,j.Z)(+t),n=null,c):e},c.x0=function(t){return arguments.length?(e="function"==typeof t?t:(0,j.Z)(+t),c):e},c.x1=function(e){return arguments.length?(n=null==e?null:"function"==typeof e?e:(0,j.Z)(+e),c):n},c.y=function(e){return arguments.length?(t="function"==typeof e?e:(0,j.Z)(+e),r=null,c):t},c.y0=function(e){return arguments.length?(t="function"==typeof e?e:(0,j.Z)(+e),c):t},c.y1=function(e){return arguments.length?(r=null==e?null:"function"==typeof e?e:(0,j.Z)(+e),c):r},c.lineX0=c.lineY0=function(){return s().x(e).y(t)},c.lineY1=function(){return s().x(e).y(r)},c.lineX1=function(){return s().x(n).y(t)},c.defined=function(e){return arguments.length?(i="function"==typeof e?e:(0,j.Z)(!!e),c):i},c.curve=function(e){return arguments.length?(o=e,null!=a&&(u=o(a)),c):o},c.context=function(e){return arguments.length?(null==e?a=u=null:u=o(a=e),c):a},c}var E=r(61994),C=r(41637),T=r(82944),D=r(16630),N=r(66395);function _(){return(_=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function I(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function R(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?I(Object(r),!0).forEach(function(t){var n,i;n=t,i=r[t],(n=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(n))in e?Object.defineProperty(e,n,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[n]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):I(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var L={curveBasisClosed:function(e){return new u(e)},curveBasisOpen:function(e){return new l(e)},curveBasis:function(e){return new o(e)},curveBumpX:function(e){return new c(e,!0)},curveBumpY:function(e){return new c(e,!1)},curveLinearClosed:function(e){return new s(e)},curveLinear:d,curveMonotoneX:function(e){return new y(e)},curveMonotoneY:function(e){return new g(e)},curveNatural:function(e){return new b(e)},curveStep:function(e){return new w(e,.5)},curveStepAfter:function(e){return new w(e,1)},curveStepBefore:function(e){return new w(e,0)}},z=e=>(0,N.n)(e.x)&&(0,N.n)(e.y),F=e=>e.x,W=e=>e.y,B=(e,t)=>{if("function"==typeof e)return e;var r="curve".concat((0,D.jC)(e));return("curveMonotone"===r||"curveBump"===r)&&t?L["".concat(r).concat("vertical"===t?"Y":"X")]:L[r]||d},K=e=>{var t,{type:r="linear",points:n=[],baseLine:i,layout:a,connectNulls:o=!1}=e,u=B(r,a),l=o?n.filter(z):n;if(Array.isArray(i)){var c=o?i.filter(e=>z(e)):i,s=l.map((e,t)=>R(R({},e),{},{base:c[t]}));return(t="vertical"===a?A().y(W).x1(F).x0(e=>e.base.x):A().x(F).y1(W).y0(e=>e.base.y)).defined(z).curve(u),t(s)}return(t="vertical"===a&&(0,D.hj)(i)?A().y(W).x1(F).x0(i):(0,D.hj)(i)?A().x(F).y1(W).y0(i):S().x(F).y(W)).defined(z).curve(u),t(l)},Z=e=>{var{className:t,points:r,path:i,pathRef:a}=e;if((!r||!r.length)&&!i)return null;var o=r&&r.length?K(e):i;return n.createElement("path",_({},(0,T.L6)(e,!1),(0,C.Ym)(e),{className:(0,E.W)("recharts-curve",t),d:null===o?void 0:o,ref:a}))}},73649:function(e,t,r){"use strict";r.d(t,{A:function(){return f}});var n=r(2265),i=r(61994),a=r(82944),o=r(40130),u=r(46595);function l(){return(l=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}var c=(e,t,r,n,i)=>{var a,o=Math.min(Math.abs(r)/2,Math.abs(n)/2),u=n>=0?1:-1,l=r>=0?1:-1,c=n>=0&&r>=0||n<0&&r<0?1:0;if(o>0&&i instanceof Array){for(var s=[0,0,0,0],f=0;f<4;f++)s[f]=i[f]>o?o:i[f];a="M".concat(e,",").concat(t+u*s[0]),s[0]>0&&(a+="A ".concat(s[0],",").concat(s[0],",0,0,").concat(c,",").concat(e+l*s[0],",").concat(t)),a+="L ".concat(e+r-l*s[1],",").concat(t),s[1]>0&&(a+="A ".concat(s[1],",").concat(s[1],",0,0,").concat(c,",\n        ").concat(e+r,",").concat(t+u*s[1])),a+="L ".concat(e+r,",").concat(t+n-u*s[2]),s[2]>0&&(a+="A ".concat(s[2],",").concat(s[2],",0,0,").concat(c,",\n        ").concat(e+r-l*s[2],",").concat(t+n)),a+="L ".concat(e+l*s[3],",").concat(t+n),s[3]>0&&(a+="A ".concat(s[3],",").concat(s[3],",0,0,").concat(c,",\n        ").concat(e,",").concat(t+n-u*s[3])),a+="Z"}else if(o>0&&i===+i&&i>0){var d=Math.min(o,i);a="M ".concat(e,",").concat(t+u*d,"\n            A ").concat(d,",").concat(d,",0,0,").concat(c,",").concat(e+l*d,",").concat(t,"\n            L ").concat(e+r-l*d,",").concat(t,"\n            A ").concat(d,",").concat(d,",0,0,").concat(c,",").concat(e+r,",").concat(t+u*d,"\n            L ").concat(e+r,",").concat(t+n-u*d,"\n            A ").concat(d,",").concat(d,",0,0,").concat(c,",").concat(e+r-l*d,",").concat(t+n,"\n            L ").concat(e+l*d,",").concat(t+n,"\n            A ").concat(d,",").concat(d,",0,0,").concat(c,",").concat(e,",").concat(t+n-u*d," Z")}else a="M ".concat(e,",").concat(t," h ").concat(r," v ").concat(n," h ").concat(-r," Z");return a},s={x:0,y:0,width:0,height:0,radius:0,isAnimationActive:!1,isUpdateAnimationActive:!1,animationBegin:0,animationDuration:1500,animationEasing:"ease"},f=e=>{var t=(0,o.j)(e,s),r=(0,n.useRef)(null),[f,d]=(0,n.useState)(-1);(0,n.useEffect)(()=>{if(r.current&&r.current.getTotalLength)try{var e=r.current.getTotalLength();e&&d(e)}catch(e){}},[]);var{x:p,y:h,width:v,height:y,radius:g,className:m}=t,{animationEasing:b,animationDuration:x,animationBegin:w,isAnimationActive:O,isUpdateAnimationActive:j}=t;if(p!==+p||h!==+h||v!==+v||y!==+y||0===v||0===y)return null;var P=(0,i.W)("recharts-rectangle",m);return j?n.createElement(u.r,{canBegin:f>0,from:{width:v,height:y,x:p,y:h},to:{width:v,height:y,x:p,y:h},duration:x,animationEasing:b,isActive:j},e=>{var{width:i,height:o,x:s,y:d}=e;return n.createElement(u.r,{canBegin:f>0,from:"0px ".concat(-1===f?1:f,"px"),to:"".concat(f,"px 0px"),attributeName:"strokeDasharray",begin:w,duration:x,isActive:O,easing:b},n.createElement("path",l({},(0,a.L6)(t,!0),{className:P,d:c(s,d,i,o,g),ref:r})))}):n.createElement("path",l({},(0,a.L6)(t,!0),{className:P,d:c(p,h,v,y,g)}))}},60474:function(e,t,r){"use strict";r.d(t,{L:function(){return v}});var n=r(2265),i=r(61994),a=r(82944),o=r(39206),u=r(16630),l=r(40130);function c(){return(c=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}var s=(e,t)=>(0,u.uY)(t-e)*Math.min(Math.abs(t-e),359.999),f=e=>{var{cx:t,cy:r,radius:n,angle:i,sign:a,isExternal:u,cornerRadius:l,cornerIsExternal:c}=e,s=l*(u?1:-1)+n,f=Math.asin(l/s)/o.Wk,d=c?i:i+a*f;return{center:(0,o.op)(t,r,s,d),circleTangency:(0,o.op)(t,r,n,d),lineTangency:(0,o.op)(t,r,s*Math.cos(f*o.Wk),c?i-a*f:i),theta:f}},d=e=>{var{cx:t,cy:r,innerRadius:n,outerRadius:i,startAngle:a,endAngle:u}=e,l=s(a,u),c=a+l,f=(0,o.op)(t,r,i,a),d=(0,o.op)(t,r,i,c),p="M ".concat(f.x,",").concat(f.y,"\n    A ").concat(i,",").concat(i,",0,\n    ").concat(+(Math.abs(l)>180),",").concat(+(a>c),",\n    ").concat(d.x,",").concat(d.y,"\n  ");if(n>0){var h=(0,o.op)(t,r,n,a),v=(0,o.op)(t,r,n,c);p+="L ".concat(v.x,",").concat(v.y,"\n            A ").concat(n,",").concat(n,",0,\n            ").concat(+(Math.abs(l)>180),",").concat(+(a<=c),",\n            ").concat(h.x,",").concat(h.y," Z")}else p+="L ".concat(t,",").concat(r," Z");return p},p=e=>{var{cx:t,cy:r,innerRadius:n,outerRadius:i,cornerRadius:a,forceCornerRadius:o,cornerIsExternal:l,startAngle:c,endAngle:s}=e,p=(0,u.uY)(s-c),{circleTangency:h,lineTangency:v,theta:y}=f({cx:t,cy:r,radius:i,angle:c,sign:p,cornerRadius:a,cornerIsExternal:l}),{circleTangency:g,lineTangency:m,theta:b}=f({cx:t,cy:r,radius:i,angle:s,sign:-p,cornerRadius:a,cornerIsExternal:l}),x=l?Math.abs(c-s):Math.abs(c-s)-y-b;if(x<0)return o?"M ".concat(v.x,",").concat(v.y,"\n        a").concat(a,",").concat(a,",0,0,1,").concat(2*a,",0\n        a").concat(a,",").concat(a,",0,0,1,").concat(-(2*a),",0\n      "):d({cx:t,cy:r,innerRadius:n,outerRadius:i,startAngle:c,endAngle:s});var w="M ".concat(v.x,",").concat(v.y,"\n    A").concat(a,",").concat(a,",0,0,").concat(+(p<0),",").concat(h.x,",").concat(h.y,"\n    A").concat(i,",").concat(i,",0,").concat(+(x>180),",").concat(+(p<0),",").concat(g.x,",").concat(g.y,"\n    A").concat(a,",").concat(a,",0,0,").concat(+(p<0),",").concat(m.x,",").concat(m.y,"\n  ");if(n>0){var{circleTangency:O,lineTangency:j,theta:P}=f({cx:t,cy:r,radius:n,angle:c,sign:p,isExternal:!0,cornerRadius:a,cornerIsExternal:l}),{circleTangency:k,lineTangency:M,theta:S}=f({cx:t,cy:r,radius:n,angle:s,sign:-p,isExternal:!0,cornerRadius:a,cornerIsExternal:l}),A=l?Math.abs(c-s):Math.abs(c-s)-P-S;if(A<0&&0===a)return"".concat(w,"L").concat(t,",").concat(r,"Z");w+="L".concat(M.x,",").concat(M.y,"\n      A").concat(a,",").concat(a,",0,0,").concat(+(p<0),",").concat(k.x,",").concat(k.y,"\n      A").concat(n,",").concat(n,",0,").concat(+(A>180),",").concat(+(p>0),",").concat(O.x,",").concat(O.y,"\n      A").concat(a,",").concat(a,",0,0,").concat(+(p<0),",").concat(j.x,",").concat(j.y,"Z")}else w+="L".concat(t,",").concat(r,"Z");return w},h={cx:0,cy:0,innerRadius:0,outerRadius:0,startAngle:0,endAngle:0,cornerRadius:0,forceCornerRadius:!1,cornerIsExternal:!1},v=e=>{var t,r=(0,l.j)(e,h),{cx:o,cy:s,innerRadius:f,outerRadius:v,cornerRadius:y,forceCornerRadius:g,cornerIsExternal:m,startAngle:b,endAngle:x,className:w}=r;if(v<f||b===x)return null;var O=(0,i.W)("recharts-sector",w),j=v-f,P=(0,u.h1)(y,j,0,!0);return t=P>0&&360>Math.abs(b-x)?p({cx:o,cy:s,innerRadius:f,outerRadius:v,cornerRadius:Math.min(P,j/2),forceCornerRadius:g,cornerIsExternal:m,startAngle:b,endAngle:x}):d({cx:o,cy:s,innerRadius:f,outerRadius:v,startAngle:b,endAngle:x}),n.createElement("path",c({},(0,a.L6)(r,!0),{className:O,d:t}))}},50209:function(e,t,r){"use strict";r.d(t,{l:function(){return n}});var n=(0,r(2265).createContext)(null)},82931:function(e,t,r){"use strict";r.d(t,{M:function(){return U}});var n=r(2265),i=r(68575),a=r(59688),o=r(39129),u=r(31057),l=r(64725),c=r(39173),s=r(65293),f=r(48323);function d(e,t){return t instanceof HTMLElement?"HTMLElement <".concat(t.tagName,' class="').concat(t.className,'">'):t===window?"global.window":t}var p=r(17644),h=r(19579),v=r(10418),y=(0,o.oM)({name:"referenceElements",initialState:{dots:[],areas:[],lines:[]},reducers:{addDot:(e,t)=>{e.dots.push(t.payload)},removeDot:(e,t)=>{var r=(0,v.Vk)(e).dots.findIndex(e=>e===t.payload);-1!==r&&e.dots.splice(r,1)},addArea:(e,t)=>{e.areas.push(t.payload)},removeArea:(e,t)=>{var r=(0,v.Vk)(e).areas.findIndex(e=>e===t.payload);-1!==r&&e.areas.splice(r,1)},addLine:(e,t)=>{e.lines.push(t.payload)},removeLine:(e,t)=>{var r=(0,v.Vk)(e).lines.findIndex(e=>e===t.payload);-1!==r&&e.lines.splice(r,1)}}}),{addDot:g,removeDot:m,addArea:b,removeArea:x,addLine:w,removeLine:O}=y.actions,j=y.reducer,P={x:0,y:0,width:0,height:0,padding:{top:0,right:0,bottom:0,left:0}},k=(0,o.oM)({name:"brush",initialState:P,reducers:{setBrushSettings:(e,t)=>null==t.payload?P:t.payload}}),{setBrushSettings:M}=k.actions,S=k.reducer,A=r(32738),E=r(59156),C=(0,o.oM)({name:"polarAxis",initialState:{radiusAxis:{},angleAxis:{}},reducers:{addRadiusAxis(e,t){e.radiusAxis[t.payload.id]=(0,v.cA)(t.payload)},removeRadiusAxis(e,t){delete e.radiusAxis[t.payload.id]},addAngleAxis(e,t){e.angleAxis[t.payload.id]=(0,v.cA)(t.payload)},removeAngleAxis(e,t){delete e.angleAxis[t.payload.id]}}}),{addRadiusAxis:T,removeRadiusAxis:D,addAngleAxis:N,removeAngleAxis:_}=C.actions,I=C.reducer,R=r(27410),L=r(7883),z=r(69366),F=r(83061),W=(0,a.UY)({brush:S,cartesianAxis:p.vk,chartData:c.Q2,graphicalItems:h.iX,layout:s.EW,legend:A.Ny,options:u.wB,polarAxis:I,polarOptions:R.i,referenceElements:j,rootProps:E.$f,tooltip:l.Kw}),B=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"Chart";return(0,o.xC)({reducer:W,preloadedState:e,middleware:e=>e({serializableCheck:!1}).concat([f.RG.middleware,f.an.middleware,L._2.middleware,z.Y.middleware,F.x.middleware]),devTools:{serialize:{replacer:d},name:"recharts-".concat(t)}})},K=r(58735),Z=r(50209);function U(e){var{preloadedState:t,children:r,reduxStoreName:a}=e,o=(0,K.W)(),u=(0,n.useRef)(null);if(o)return r;null==u.current&&(u.current=B(t,a));var l=Z.l;return n.createElement(i.zt,{context:l,store:u.current},r)}},15317:function(e,t,r){"use strict";r.d(t,{b:function(){return o}});var n=r(2265),i=r(59156),a=r(39040);function o(e){var t=(0,a.T)();return(0,n.useEffect)(()=>{t((0,i.Rk)(e))},[t,e]),null}},87235:function(e,t,r){"use strict";r.d(t,{v:function(){return u}});var n=r(2265),i=r(58735),a=r(65293),o=r(39040);function u(e){var{layout:t,width:r,height:u,margin:l}=e,c=(0,o.T)(),s=(0,i.W)();return(0,n.useEffect)(()=>{s||(c((0,a.jx)(t)),c((0,a.Dx)({width:r,height:u})),c((0,a.Qb)(l)))},[c,s,t,r,u,l]),null}},13790:function(e,t,r){"use strict";r.d(t,{E:function(){return s},V:function(){return c}});var n=r(2265),i=r(39040),a=r(19579),o=r(49037);function u(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function l(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?u(Object(r),!0).forEach(function(t){var n,i;n=t,i=r[t],(n=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(n))in e?Object.defineProperty(e,n,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[n]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):u(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function c(e){var t=(0,i.T)();return(0,n.useEffect)(()=>{var r=l(l({},e),{},{stackId:(0,o.GA)(e.stackId)});return t((0,a.AC)(r)),()=>{t((0,a._Q)(r))}},[t,e]),null}function s(e){var t=(0,i.T)();return(0,n.useEffect)(()=>(t((0,a.Wz)(e)),()=>{t((0,a.ot)(e))}),[t,e]),null}},62658:function(e,t,r){"use strict";r.d(t,{L:function(){return c},t:function(){return s}});var n=r(2265),i=r(58735),a=r(35953),o=r(39040),u=r(32738),l=()=>{};function c(e){var{legendPayload:t}=e,r=(0,o.T)(),a=(0,i.W)();return(0,n.useEffect)(()=>a?l:(r((0,u.t8)(t)),()=>{r((0,u.ZR)(t))}),[r,a,t]),null}function s(e){var{legendPayload:t}=e,r=(0,o.T)(),i=(0,o.C)(a.rE);return(0,n.useEffect)(()=>"centric"!==i&&"radial"!==i?l:(r((0,u.t8)(t)),()=>{r((0,u.ZR)(t))}),[r,i,t]),null}},35623:function(e,t,r){"use strict";r.d(t,{k:function(){return u}});var n=r(2265),i=r(39040),a=r(64725),o=r(58735);function u(e){var{fn:t,args:r}=e,u=(0,i.T)(),l=(0,o.W)();return(0,n.useEffect)(()=>{if(!l){var e=t(r);return u((0,a.KO)(e)),()=>{u((0,a.cK)(e))}}},[t,r,u,l]),null}},17644:function(e,t,r){"use strict";r.d(t,{Jj:function(){return c},TC:function(){return s},cB:function(){return f},kB:function(){return h},m2:function(){return l},vk:function(){return v}});var n=r(39129),i=r(10418);function a(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function o(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?a(Object(r),!0).forEach(function(t){var n,i;n=t,i=r[t],(n=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(n))in e?Object.defineProperty(e,n,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[n]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):a(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var u=(0,n.oM)({name:"cartesianAxis",initialState:{xAxis:{},yAxis:{},zAxis:{}},reducers:{addXAxis(e,t){e.xAxis[t.payload.id]=(0,i.cA)(t.payload)},removeXAxis(e,t){delete e.xAxis[t.payload.id]},addYAxis(e,t){e.yAxis[t.payload.id]=(0,i.cA)(t.payload)},removeYAxis(e,t){delete e.yAxis[t.payload.id]},addZAxis(e,t){e.zAxis[t.payload.id]=(0,i.cA)(t.payload)},removeZAxis(e,t){delete e.zAxis[t.payload.id]},updateYAxisWidth(e,t){var{id:r,width:n}=t.payload;e.yAxis[r]&&(e.yAxis[r]=o(o({},e.yAxis[r]),{},{width:n}))}}}),{addXAxis:l,removeXAxis:c,addYAxis:s,removeYAxis:f,addZAxis:d,removeZAxis:p,updateYAxisWidth:h}=u.actions,v=u.reducer},39173:function(e,t,r){"use strict";r.d(t,{Q2:function(){return u},t0:function(){return a},zR:function(){return i}});var n=(0,r(39129).oM)({name:"chartData",initialState:{chartData:void 0,computedData:void 0,dataStartIndex:0,dataEndIndex:0},reducers:{setChartData(e,t){if(e.chartData=t.payload,null==t.payload){e.dataStartIndex=0,e.dataEndIndex=0;return}t.payload.length>0&&e.dataEndIndex!==t.payload.length-1&&(e.dataEndIndex=t.payload.length-1)},setComputedData(e,t){e.computedData=t.payload},setDataStartEndIndexes(e,t){var{startIndex:r,endIndex:n}=t.payload;null!=r&&(e.dataStartIndex=r),null!=n&&(e.dataEndIndex=n)}}}),{setChartData:i,setDataStartEndIndexes:a,setComputedData:o}=n.actions,u=n.reducer},69366:function(e,t,r){"use strict";r.d(t,{Y:function(){return o},r:function(){return a}});var n=r(39129),i=r(31944),a=(0,n.PH)("externalEvent"),o=(0,n.e)();o.startListening({actionCreator:a,effect:(e,t)=>{if(null!=e.payload.handler){var r=t.getState(),n={activeCoordinate:(0,i.pI)(r),activeDataKey:(0,i.du)(r),activeIndex:(0,i.Ve)(r),activeLabel:(0,i.i)(r),activeTooltipIndex:(0,i.Ve)(r),isTooltipActive:(0,i.oo)(r)};e.payload.handler(n,e.payload.reactEvent)}}})},19579:function(e,t,r){"use strict";r.d(t,{AC:function(){return l},Wz:function(){return s},_Q:function(){return c},a1:function(){return o},iX:function(){return d},nF:function(){return u},ot:function(){return f}});var n=r(39129),i=r(10418),a=(0,n.oM)({name:"graphicalItems",initialState:{countOfBars:0,cartesianItems:[],polarItems:[]},reducers:{addBar(e){e.countOfBars+=1},removeBar(e){e.countOfBars-=1},addCartesianGraphicalItem(e,t){e.cartesianItems.push((0,i.cA)(t.payload))},removeCartesianGraphicalItem(e,t){var r=(0,i.Vk)(e).cartesianItems.indexOf((0,i.cA)(t.payload));r>-1&&e.cartesianItems.splice(r,1)},addPolarGraphicalItem(e,t){e.polarItems.push((0,i.cA)(t.payload))},removePolarGraphicalItem(e,t){var r=(0,i.Vk)(e).polarItems.indexOf((0,i.cA)(t.payload));r>-1&&e.polarItems.splice(r,1)}}}),{addBar:o,removeBar:u,addCartesianGraphicalItem:l,removeCartesianGraphicalItem:c,addPolarGraphicalItem:s,removePolarGraphicalItem:f}=a.actions,d=a.reducer},39040:function(e,t,r){"use strict";r.d(t,{C:function(){return f},T:function(){return u}});var n=r(35195),i=r(2265),a=r(50209),o=e=>e,u=()=>{var e=(0,i.useContext)(a.l);return e?e.store.dispatch:o},l=()=>{},c=()=>l,s=(e,t)=>e===t;function f(e){var t=(0,i.useContext)(a.l);return(0,n.useSyncExternalStoreWithSelector)(t?t.subscription.addNestedSub:c,t?t.store.getState:l,t?t.store.getState:l,t?e:l,s)}},7883:function(e,t,r){"use strict";r.d(t,{_2:function(){return f},eb:function(){return s},sj:function(){return c}});var n=r(39129),i=r(64725),a=r(31944),o=r(84461),u=r(98628),l=r(6064),c=(0,n.PH)("keyDown"),s=(0,n.PH)("focus"),f=(0,n.e)();f.startListening({actionCreator:c,effect:(e,t)=>{var r=t.getState();if(!1!==r.rootProps.accessibilityLayer){var{keyboardInteraction:n}=r.tooltip,c=e.payload;if("ArrowRight"===c||"ArrowLeft"===c||"Enter"===c){var s=Number((0,l.p)(n,(0,a.wQ)(r))),f=(0,a.WQ)(r);if("Enter"===c){var d=(0,o.hA)(r,"axis","hover",String(n.index));t.dispatch((0,i.KN)({active:!n.active,activeIndex:n.index,activeDataKey:n.dataKey,activeCoordinate:d}));return}var p=s+("ArrowRight"===c?1:-1)*("left-to-right"===(0,u.Xv)(r)?1:-1);if(null!=f&&!(p>=f.length)&&!(p<0)){var h=(0,o.hA)(r,"axis","hover",String(p));t.dispatch((0,i.KN)({active:!0,activeIndex:p.toString(),activeDataKey:void 0,activeCoordinate:h}))}}}}}),f.startListening({actionCreator:s,effect:(e,t)=>{var r=t.getState();if(!1!==r.rootProps.accessibilityLayer){var{keyboardInteraction:n}=r.tooltip;if(!n.active&&null==n.index){var a=(0,o.hA)(r,"axis","hover",String("0"));t.dispatch((0,i.KN)({activeDataKey:void 0,active:!0,activeIndex:"0",activeCoordinate:a}))}}}})},65293:function(e,t,r){"use strict";r.d(t,{Dx:function(){return o},EW:function(){return l},Qb:function(){return i},ZP:function(){return u},jx:function(){return a}});var n=(0,r(39129).oM)({name:"chartLayout",initialState:{layoutType:"horizontal",width:0,height:0,margin:{top:5,right:5,bottom:5,left:5},scale:1},reducers:{setLayout(e,t){e.layoutType=t.payload},setChartSize(e,t){e.width=t.payload.width,e.height=t.payload.height},setMargin(e,t){e.margin.top=t.payload.top,e.margin.right=t.payload.right,e.margin.bottom=t.payload.bottom,e.margin.left=t.payload.left},setScale(e,t){e.scale=t.payload}}}),{setMargin:i,setLayout:a,setChartSize:o,setScale:u}=n.actions,l=n.reducer},32738:function(e,t,r){"use strict";r.d(t,{Ny:function(){return s},ZR:function(){return c},t8:function(){return l}});var n=r(39129),i=r(10418),a=(0,n.oM)({name:"legend",initialState:{settings:{layout:"horizontal",align:"center",verticalAlign:"middle",itemSorter:"value"},size:{width:0,height:0},payload:[]},reducers:{setLegendSize(e,t){e.size.width=t.payload.width,e.size.height=t.payload.height},setLegendSettings(e,t){e.settings.align=t.payload.align,e.settings.layout=t.payload.layout,e.settings.verticalAlign=t.payload.verticalAlign,e.settings.itemSorter=t.payload.itemSorter},addLegendPayload(e,t){e.payload.push((0,i.cA)(t.payload))},removeLegendPayload(e,t){var r=(0,i.Vk)(e).payload.indexOf((0,i.cA)(t.payload));r>-1&&e.payload.splice(r,1)}}}),{setLegendSize:o,setLegendSettings:u,addLegendPayload:l,removeLegendPayload:c}=a.actions,s=a.reducer},48323:function(e,t,r){"use strict";r.d(t,{AE:function(){return l},RG:function(){return c},TK:function(){return s},an:function(){return f}});var n=r(39129),i=r(64725),a=r(63330),o=r(46302),u=r(13169),l=(0,n.PH)("mouseClick"),c=(0,n.e)();c.startListening({actionCreator:l,effect:(e,t)=>{var r=e.payload,n=(0,a.h)(t.getState(),(0,u.a)(r));(null==n?void 0:n.activeIndex)!=null&&t.dispatch((0,i.eB)({activeIndex:n.activeIndex,activeDataKey:void 0,activeCoordinate:n.activeCoordinate}))}});var s=(0,n.PH)("mouseMove"),f=(0,n.e)();f.startListening({actionCreator:s,effect:(e,t)=>{var r=e.payload,n=t.getState(),l=(0,o.SB)(n,n.tooltip.settings.shared),c=(0,a.h)(n,(0,u.a)(r));"axis"===l&&((null==c?void 0:c.activeIndex)!=null?t.dispatch((0,i.Rr)({activeIndex:c.activeIndex,activeDataKey:void 0,activeCoordinate:c.activeCoordinate})):t.dispatch((0,i.ne)()))}})},31057:function(e,t,r){"use strict";r.d(t,{IC:function(){return l},NL:function(){return a},wB:function(){return u}});var n=r(39129),i=r(16630);function a(e,t){if(t){var r=Number.parseInt(t,10);if(!(0,i.In)(r))return null==e?void 0:e[r]}}var o=(0,n.oM)({name:"options",initialState:{chartName:"",tooltipPayloadSearcher:void 0,eventEmitter:void 0,defaultTooltipEventType:"axis"},reducers:{createEventEmitter:e=>{null==e.eventEmitter&&(e.eventEmitter=Symbol("rechartsEventEmitter"))}}}),u=o.reducer,{createEventEmitter:l}=o.actions},27410:function(e,t,r){"use strict";r.d(t,{a:function(){return i},i:function(){return a}});var n=(0,r(39129).oM)({name:"polarOptions",initialState:null,reducers:{updatePolarOptions:(e,t)=>t.payload}}),{updatePolarOptions:i}=n.actions,a=n.reducer},59156:function(e,t,r){"use strict";r.d(t,{$f:function(){return o},Rk:function(){return u}});var n=r(39129),i={accessibilityLayer:!0,barCategoryGap:"10%",barGap:4,barSize:void 0,className:void 0,maxBarSize:void 0,stackOffset:"none",syncId:void 0,syncMethod:"index"},a=(0,n.oM)({name:"rootProps",initialState:i,reducers:{updateOptions:(e,t)=>{var r;e.accessibilityLayer=t.payload.accessibilityLayer,e.barCategoryGap=t.payload.barCategoryGap,e.barGap=null!==(r=t.payload.barGap)&&void 0!==r?r:i.barGap,e.barSize=t.payload.barSize,e.maxBarSize=t.payload.maxBarSize,e.stackOffset=t.payload.stackOffset,e.syncId=t.payload.syncId,e.syncMethod=t.payload.syncMethod,e.className=t.payload.className}}}),o=a.reducer,{updateOptions:u}=a.actions},98628:function(e,t,r){"use strict";r.d(t,{VQ:function(){return ia},UA:function(){return n4},yT:function(){return iO},l_:function(){return iT},kO:function(){return iz},UC:function(){return an},tZ:function(){return n5},dz:function(){return ir},zX:function(){return ix},rC:function(){return at},bU:function(){return n2},$B:function(){return nQ},fY:function(){return iP},vb:function(){return iR},E8:function(){return iA},Yf:function(){return iN},Jw:function(){return iI},CK:function(){return ie},Tk:function(){return n0},zW:function(){return ih},c4:function(){return is},dW:function(){return nW},RN:function(){return nK},YZ:function(){return nq},Eu:function(){return id},Lg:function(){return aa},Fn:function(){return i$},Vm:function(){return iH},ub:function(){return nY},AS:function(){return al},fW:function(){return n$},Lu:function(){return ae},Xv:function(){return as},KB:function(){return iS},Q4:function(){return nH},cV:function(){return i_},ww:function(){return iy},RA:function(){return ip},xz:function(){return im},g6:function(){return it},ox:function(){return ao},bY:function(){return au},bm:function(){return nX},rs:function(){return i9},i9:function(){return nB},Oy:function(){return i2},lU:function(){return i7},t:function(){return nZ},ON:function(){return i8}});var n,i,a,o,u,l,c,s={};r.r(s),r.d(s,{scaleBand:function(){return w},scaleDiverging:function(){return function e(){var t=eV(r4()(eS));return t.copy=function(){return r6(t,e())},v.apply(t,arguments)}},scaleDivergingLog:function(){return function e(){var t=eJ(r4()).domain([.1,1,10]);return t.copy=function(){return r6(t,e()).base(t.base())},v.apply(t,arguments)}},scaleDivergingPow:function(){return r9},scaleDivergingSqrt:function(){return r7},scaleDivergingSymlog:function(){return function e(){var t=e2(r4());return t.copy=function(){return r6(t,e()).constant(t.constant())},v.apply(t,arguments)}},scaleIdentity:function(){return function e(t){var r;function n(e){return null==e||isNaN(e=+e)?r:e}return n.invert=n,n.domain=n.range=function(e){return arguments.length?(t=Array.from(e,ek),n):t.slice()},n.unknown=function(e){return arguments.length?(r=e,n):r},n.copy=function(){return e(t).unknown(r)},t=arguments.length?Array.from(t,ek):[0,1],eV(n)}},scaleImplicit:function(){return b},scaleLinear:function(){return function e(){var t=eN();return t.copy=function(){return eT(t,e())},h.apply(t,arguments),eV(t)}},scaleLog:function(){return function e(){let t=eJ(eD()).domain([1,10]);return t.copy=()=>eT(t,e()).base(t.base()),h.apply(t,arguments),t}},scaleOrdinal:function(){return x},scalePoint:function(){return O},scalePow:function(){return e9},scaleQuantile:function(){return function e(){var t,r=[],n=[],i=[];function a(){var e=0,t=Math.max(1,n.length);for(i=Array(t-1);++e<t;)i[e-1]=function(e,t,r=_){if(!(!(n=e.length)||isNaN(t=+t))){if(t<=0||n<2)return+r(e[0],0,e);if(t>=1)return+r(e[n-1],n-1,e);var n,i=(n-1)*t,a=Math.floor(i),o=+r(e[a],a,e);return o+(+r(e[a+1],a+1,e)-o)*(i-a)}}(r,e/t);return o}function o(e){return null==e||isNaN(e=+e)?t:n[R(i,e)]}return o.invertExtent=function(e){var t=n.indexOf(e);return t<0?[NaN,NaN]:[t>0?i[t-1]:r[0],t<i.length?i[t]:r[r.length-1]]},o.domain=function(e){if(!arguments.length)return r.slice();for(let t of(r=[],e))null==t||isNaN(t=+t)||r.push(t);return r.sort(C),a()},o.range=function(e){return arguments.length?(n=Array.from(e),a()):n.slice()},o.unknown=function(e){return arguments.length?(t=e,o):t},o.quantiles=function(){return i.slice()},o.copy=function(){return e().domain(r).range(n).unknown(t)},h.apply(o,arguments)}},scaleQuantize:function(){return function e(){var t,r=0,n=1,i=1,a=[.5],o=[0,1];function u(e){return null!=e&&e<=e?o[R(a,e,0,i)]:t}function l(){var e=-1;for(a=Array(i);++e<i;)a[e]=((e+1)*n-(e-i)*r)/(i+1);return u}return u.domain=function(e){return arguments.length?([r,n]=e,r=+r,n=+n,l()):[r,n]},u.range=function(e){return arguments.length?(i=(o=Array.from(e)).length-1,l()):o.slice()},u.invertExtent=function(e){var t=o.indexOf(e);return t<0?[NaN,NaN]:t<1?[r,a[0]]:t>=i?[a[i-1],n]:[a[t-1],a[t]]},u.unknown=function(e){return arguments.length&&(t=e),u},u.thresholds=function(){return a.slice()},u.copy=function(){return e().domain([r,n]).range(o).unknown(t)},h.apply(eV(u),arguments)}},scaleRadial:function(){return function e(){var t,r=eN(),n=[0,1],i=!1;function a(e){var n,a=Math.sign(n=r(e))*Math.sqrt(Math.abs(n));return isNaN(a)?t:i?Math.round(a):a}return a.invert=function(e){return r.invert(e8(e))},a.domain=function(e){return arguments.length?(r.domain(e),a):r.domain()},a.range=function(e){return arguments.length?(r.range((n=Array.from(e,ek)).map(e8)),a):n.slice()},a.rangeRound=function(e){return a.range(e).round(!0)},a.round=function(e){return arguments.length?(i=!!e,a):i},a.clamp=function(e){return arguments.length?(r.clamp(e),a):r.clamp()},a.unknown=function(e){return arguments.length?(t=e,a):t},a.copy=function(){return e(r.domain(),n).round(i).clamp(r.clamp()).unknown(t)},h.apply(a,arguments),eV(a)}},scaleSequential:function(){return function e(){var t=eV(r2()(eS));return t.copy=function(){return r6(t,e())},v.apply(t,arguments)}},scaleSequentialLog:function(){return function e(){var t=eJ(r2()).domain([1,10]);return t.copy=function(){return r6(t,e()).base(t.base())},v.apply(t,arguments)}},scaleSequentialPow:function(){return r5},scaleSequentialQuantile:function(){return function e(){var t=[],r=eS;function n(e){if(null!=e&&!isNaN(e=+e))return r((R(t,e,1)-1)/(t.length-1))}return n.domain=function(e){if(!arguments.length)return t.slice();for(let r of(t=[],e))null==r||isNaN(r=+r)||t.push(r);return t.sort(C),n},n.interpolator=function(e){return arguments.length?(r=e,n):r},n.range=function(){return t.map((e,n)=>r(n/(t.length-1)))},n.quantiles=function(e){return Array.from({length:e+1},(r,n)=>(function(e,t,r){if(!(!(n=(e=Float64Array.from(function*(e,t){if(void 0===t)for(let t of e)null!=t&&(t=+t)>=t&&(yield t);else{let r=-1;for(let n of e)null!=(n=t(n,++r,e))&&(n=+n)>=n&&(yield n)}}(e,void 0))).length)||isNaN(t=+t))){if(t<=0||n<2)return tt(e);if(t>=1)return te(e);var n,i=(n-1)*t,a=Math.floor(i),o=te((function e(t,r,n=0,i=1/0,a){if(r=Math.floor(r),n=Math.floor(Math.max(0,n)),i=Math.floor(Math.min(t.length-1,i)),!(n<=r&&r<=i))return t;for(a=void 0===a?tr:function(e=C){if(e===C)return tr;if("function"!=typeof e)throw TypeError("compare is not a function");return(t,r)=>{let n=e(t,r);return n||0===n?n:(0===e(r,r))-(0===e(t,t))}}(a);i>n;){if(i-n>600){let o=i-n+1,u=r-n+1,l=Math.log(o),c=.5*Math.exp(2*l/3),s=.5*Math.sqrt(l*c*(o-c)/o)*(u-o/2<0?-1:1),f=Math.max(n,Math.floor(r-u*c/o+s)),d=Math.min(i,Math.floor(r+(o-u)*c/o+s));e(t,r,f,d,a)}let o=t[r],u=n,l=i;for(tn(t,n,r),a(t[i],o)>0&&tn(t,n,i);u<l;){for(tn(t,u,l),++u,--l;0>a(t[u],o);)++u;for(;a(t[l],o)>0;)--l}0===a(t[n],o)?tn(t,n,l):tn(t,++l,i),l<=r&&(n=l+1),r<=l&&(i=l-1)}return t})(e,a).subarray(0,a+1));return o+(tt(e.subarray(a+1))-o)*(i-a)}})(t,n/e))},n.copy=function(){return e(r).domain(t)},v.apply(n,arguments)}},scaleSequentialSqrt:function(){return r3},scaleSequentialSymlog:function(){return function e(){var t=e2(r2());return t.copy=function(){return r6(t,e()).constant(t.constant())},v.apply(t,arguments)}},scaleSqrt:function(){return e7},scaleSymlog:function(){return function e(){var t=e2(eD());return t.copy=function(){return eT(t,e()).constant(t.constant())},h.apply(t,arguments)}},scaleThreshold:function(){return function e(){var t,r=[.5],n=[0,1],i=1;function a(e){return null!=e&&e<=e?n[R(r,e,0,i)]:t}return a.domain=function(e){return arguments.length?(i=Math.min((r=Array.from(e)).length,n.length-1),a):r.slice()},a.range=function(e){return arguments.length?(n=Array.from(e),i=Math.min(r.length,n.length-1),a):n.slice()},a.invertExtent=function(e){var t=n.indexOf(e);return[r[t-1],r[t]]},a.unknown=function(e){return arguments.length?(t=e,a):t},a.copy=function(){return e().domain(r).range(n).unknown(t)},h.apply(a,arguments)}},scaleTime:function(){return r0},scaleUtc:function(){return r1},tickFormat:function(){return eU}});var f=r(92713),d=r(41664),p=r.n(d);function h(e,t){switch(arguments.length){case 0:break;case 1:this.range(e);break;default:this.range(t).domain(e)}return this}function v(e,t){switch(arguments.length){case 0:break;case 1:"function"==typeof e?this.interpolator(e):this.range(e);break;default:this.domain(e),"function"==typeof t?this.interpolator(t):this.range(t)}return this}class y extends Map{constructor(e,t=m){if(super(),Object.defineProperties(this,{_intern:{value:new Map},_key:{value:t}}),null!=e)for(let[t,r]of e)this.set(t,r)}get(e){return super.get(g(this,e))}has(e){return super.has(g(this,e))}set(e,t){return super.set(function({_intern:e,_key:t},r){let n=t(r);return e.has(n)?e.get(n):(e.set(n,r),r)}(this,e),t)}delete(e){return super.delete(function({_intern:e,_key:t},r){let n=t(r);return e.has(n)&&(r=e.get(n),e.delete(n)),r}(this,e))}}function g({_intern:e,_key:t},r){let n=t(r);return e.has(n)?e.get(n):r}function m(e){return null!==e&&"object"==typeof e?e.valueOf():e}let b=Symbol("implicit");function x(){var e=new y,t=[],r=[],n=b;function i(i){let a=e.get(i);if(void 0===a){if(n!==b)return n;e.set(i,a=t.push(i)-1)}return r[a%r.length]}return i.domain=function(r){if(!arguments.length)return t.slice();for(let n of(t=[],e=new y,r))e.has(n)||e.set(n,t.push(n)-1);return i},i.range=function(e){return arguments.length?(r=Array.from(e),i):r.slice()},i.unknown=function(e){return arguments.length?(n=e,i):n},i.copy=function(){return x(t,r).unknown(n)},h.apply(i,arguments),i}function w(){var e,t,r=x().unknown(void 0),n=r.domain,i=r.range,a=0,o=1,u=!1,l=0,c=0,s=.5;function f(){var r=n().length,f=o<a,d=f?o:a,p=f?a:o;e=(p-d)/Math.max(1,r-l+2*c),u&&(e=Math.floor(e)),d+=(p-d-e*(r-l))*s,t=e*(1-l),u&&(d=Math.round(d),t=Math.round(t));var h=(function(e,t,r){e=+e,t=+t,r=(i=arguments.length)<2?(t=e,e=0,1):i<3?1:+r;for(var n=-1,i=0|Math.max(0,Math.ceil((t-e)/r)),a=Array(i);++n<i;)a[n]=e+n*r;return a})(r).map(function(t){return d+e*t});return i(f?h.reverse():h)}return delete r.unknown,r.domain=function(e){return arguments.length?(n(e),f()):n()},r.range=function(e){return arguments.length?([a,o]=e,a=+a,o=+o,f()):[a,o]},r.rangeRound=function(e){return[a,o]=e,a=+a,o=+o,u=!0,f()},r.bandwidth=function(){return t},r.step=function(){return e},r.round=function(e){return arguments.length?(u=!!e,f()):u},r.padding=function(e){return arguments.length?(l=Math.min(1,c=+e),f()):l},r.paddingInner=function(e){return arguments.length?(l=Math.min(1,e),f()):l},r.paddingOuter=function(e){return arguments.length?(c=+e,f()):c},r.align=function(e){return arguments.length?(s=Math.max(0,Math.min(1,e)),f()):s},r.copy=function(){return w(n(),[a,o]).round(u).paddingInner(l).paddingOuter(c).align(s)},h.apply(f(),arguments)}function O(){return function e(t){var r=t.copy;return t.padding=t.paddingOuter,delete t.paddingInner,delete t.paddingOuter,t.copy=function(){return e(r())},t}(w.apply(null,arguments).paddingInner(1))}let j=Math.sqrt(50),P=Math.sqrt(10),k=Math.sqrt(2);function M(e,t,r){let n,i,a;let o=(t-e)/Math.max(0,r),u=Math.floor(Math.log10(o)),l=o/Math.pow(10,u),c=l>=j?10:l>=P?5:l>=k?2:1;return(u<0?(n=Math.round(e*(a=Math.pow(10,-u)/c)),i=Math.round(t*a),n/a<e&&++n,i/a>t&&--i,a=-a):(n=Math.round(e/(a=Math.pow(10,u)*c)),i=Math.round(t/a),n*a<e&&++n,i*a>t&&--i),i<n&&.5<=r&&r<2)?M(e,t,2*r):[n,i,a]}function S(e,t,r){if(t=+t,e=+e,!((r=+r)>0))return[];if(e===t)return[e];let n=t<e,[i,a,o]=n?M(t,e,r):M(e,t,r);if(!(a>=i))return[];let u=a-i+1,l=Array(u);if(n){if(o<0)for(let e=0;e<u;++e)l[e]=-((a-e)/o);else for(let e=0;e<u;++e)l[e]=(a-e)*o}else if(o<0)for(let e=0;e<u;++e)l[e]=-((i+e)/o);else for(let e=0;e<u;++e)l[e]=(i+e)*o;return l}function A(e,t,r){return M(e=+e,t=+t,r=+r)[2]}function E(e,t,r){t=+t,e=+e,r=+r;let n=t<e,i=n?A(t,e,r):A(e,t,r);return(n?-1:1)*(i<0?-(1/i):i)}function C(e,t){return null==e||null==t?NaN:e<t?-1:e>t?1:e>=t?0:NaN}function T(e,t){return null==e||null==t?NaN:t<e?-1:t>e?1:t>=e?0:NaN}function D(e){let t,r,n;function i(e,n,i=0,a=e.length){if(i<a){if(0!==t(n,n))return a;do{let t=i+a>>>1;0>r(e[t],n)?i=t+1:a=t}while(i<a)}return i}return 2!==e.length?(t=C,r=(t,r)=>C(e(t),r),n=(t,r)=>e(t)-r):(t=e===C||e===T?e:N,r=e,n=e),{left:i,center:function(e,t,r=0,a=e.length){let o=i(e,t,r,a-1);return o>r&&n(e[o-1],t)>-n(e[o],t)?o-1:o},right:function(e,n,i=0,a=e.length){if(i<a){if(0!==t(n,n))return a;do{let t=i+a>>>1;0>=r(e[t],n)?i=t+1:a=t}while(i<a)}return i}}}function N(){return 0}function _(e){return null===e?NaN:+e}let I=D(C),R=I.right;function L(e,t,r){e.prototype=t.prototype=r,r.constructor=e}function z(e,t){var r=Object.create(e.prototype);for(var n in t)r[n]=t[n];return r}function F(){}I.left,D(_).center;var W="\\s*([+-]?\\d+)\\s*",B="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)\\s*",K="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)%\\s*",Z=/^#([0-9a-f]{3,8})$/,U=RegExp(`^rgb\\(${W},${W},${W}\\)$`),V=RegExp(`^rgb\\(${K},${K},${K}\\)$`),$=RegExp(`^rgba\\(${W},${W},${W},${B}\\)$`),Y=RegExp(`^rgba\\(${K},${K},${K},${B}\\)$`),H=RegExp(`^hsl\\(${B},${K},${K}\\)$`),q=RegExp(`^hsla\\(${B},${K},${K},${B}\\)$`),X={aliceblue:15792383,antiquewhite:16444375,aqua:65535,aquamarine:8388564,azure:15794175,beige:16119260,bisque:16770244,black:0,blanchedalmond:16772045,blue:255,blueviolet:9055202,brown:10824234,burlywood:14596231,cadetblue:6266528,chartreuse:8388352,chocolate:13789470,coral:16744272,cornflowerblue:6591981,cornsilk:16775388,crimson:14423100,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:12092939,darkgray:11119017,darkgreen:25600,darkgrey:11119017,darkkhaki:12433259,darkmagenta:9109643,darkolivegreen:5597999,darkorange:16747520,darkorchid:10040012,darkred:9109504,darksalmon:15308410,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:16716947,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:11674146,floralwhite:16775920,forestgreen:2263842,fuchsia:16711935,gainsboro:14474460,ghostwhite:16316671,gold:16766720,goldenrod:14329120,gray:8421504,green:32768,greenyellow:11403055,grey:8421504,honeydew:15794160,hotpink:16738740,indianred:13458524,indigo:4915330,ivory:16777200,khaki:15787660,lavender:15132410,lavenderblush:16773365,lawngreen:8190976,lemonchiffon:16775885,lightblue:11393254,lightcoral:15761536,lightcyan:14745599,lightgoldenrodyellow:16448210,lightgray:13882323,lightgreen:9498256,lightgrey:13882323,lightpink:16758465,lightsalmon:16752762,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:11584734,lightyellow:16777184,lime:65280,limegreen:3329330,linen:16445670,magenta:16711935,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:12211667,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:13047173,midnightblue:1644912,mintcream:16121850,mistyrose:16770273,moccasin:16770229,navajowhite:16768685,navy:128,oldlace:16643558,olive:8421376,olivedrab:7048739,orange:16753920,orangered:16729344,orchid:14315734,palegoldenrod:15657130,palegreen:10025880,paleturquoise:11529966,palevioletred:14381203,papayawhip:16773077,peachpuff:16767673,peru:13468991,pink:16761035,plum:14524637,powderblue:11591910,purple:8388736,rebeccapurple:6697881,red:16711680,rosybrown:12357519,royalblue:4286945,saddlebrown:9127187,salmon:16416882,sandybrown:16032864,seagreen:3050327,seashell:16774638,sienna:10506797,silver:12632256,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:16775930,springgreen:65407,steelblue:4620980,tan:13808780,teal:32896,thistle:14204888,tomato:16737095,turquoise:4251856,violet:15631086,wheat:16113331,white:16777215,whitesmoke:16119285,yellow:16776960,yellowgreen:10145074};function G(){return this.rgb().formatHex()}function Q(){return this.rgb().formatRgb()}function J(e){var t,r;return e=(e+"").trim().toLowerCase(),(t=Z.exec(e))?(r=t[1].length,t=parseInt(t[1],16),6===r?ee(t):3===r?new en(t>>8&15|t>>4&240,t>>4&15|240&t,(15&t)<<4|15&t,1):8===r?et(t>>24&255,t>>16&255,t>>8&255,(255&t)/255):4===r?et(t>>12&15|t>>8&240,t>>8&15|t>>4&240,t>>4&15|240&t,((15&t)<<4|15&t)/255):null):(t=U.exec(e))?new en(t[1],t[2],t[3],1):(t=V.exec(e))?new en(255*t[1]/100,255*t[2]/100,255*t[3]/100,1):(t=$.exec(e))?et(t[1],t[2],t[3],t[4]):(t=Y.exec(e))?et(255*t[1]/100,255*t[2]/100,255*t[3]/100,t[4]):(t=H.exec(e))?ec(t[1],t[2]/100,t[3]/100,1):(t=q.exec(e))?ec(t[1],t[2]/100,t[3]/100,t[4]):X.hasOwnProperty(e)?ee(X[e]):"transparent"===e?new en(NaN,NaN,NaN,0):null}function ee(e){return new en(e>>16&255,e>>8&255,255&e,1)}function et(e,t,r,n){return n<=0&&(e=t=r=NaN),new en(e,t,r,n)}function er(e,t,r,n){var i;return 1==arguments.length?((i=e)instanceof F||(i=J(i)),i)?new en((i=i.rgb()).r,i.g,i.b,i.opacity):new en:new en(e,t,r,null==n?1:n)}function en(e,t,r,n){this.r=+e,this.g=+t,this.b=+r,this.opacity=+n}function ei(){return`#${el(this.r)}${el(this.g)}${el(this.b)}`}function ea(){let e=eo(this.opacity);return`${1===e?"rgb(":"rgba("}${eu(this.r)}, ${eu(this.g)}, ${eu(this.b)}${1===e?")":`, ${e})`}`}function eo(e){return isNaN(e)?1:Math.max(0,Math.min(1,e))}function eu(e){return Math.max(0,Math.min(255,Math.round(e)||0))}function el(e){return((e=eu(e))<16?"0":"")+e.toString(16)}function ec(e,t,r,n){return n<=0?e=t=r=NaN:r<=0||r>=1?e=t=NaN:t<=0&&(e=NaN),new ef(e,t,r,n)}function es(e){if(e instanceof ef)return new ef(e.h,e.s,e.l,e.opacity);if(e instanceof F||(e=J(e)),!e)return new ef;if(e instanceof ef)return e;var t=(e=e.rgb()).r/255,r=e.g/255,n=e.b/255,i=Math.min(t,r,n),a=Math.max(t,r,n),o=NaN,u=a-i,l=(a+i)/2;return u?(o=t===a?(r-n)/u+(r<n)*6:r===a?(n-t)/u+2:(t-r)/u+4,u/=l<.5?a+i:2-a-i,o*=60):u=l>0&&l<1?0:o,new ef(o,u,l,e.opacity)}function ef(e,t,r,n){this.h=+e,this.s=+t,this.l=+r,this.opacity=+n}function ed(e){return(e=(e||0)%360)<0?e+360:e}function ep(e){return Math.max(0,Math.min(1,e||0))}function eh(e,t,r){return(e<60?t+(r-t)*e/60:e<180?r:e<240?t+(r-t)*(240-e)/60:t)*255}function ev(e,t,r,n,i){var a=e*e,o=a*e;return((1-3*e+3*a-o)*t+(4-6*a+3*o)*r+(1+3*e+3*a-3*o)*n+o*i)/6}L(F,J,{copy(e){return Object.assign(new this.constructor,this,e)},displayable(){return this.rgb().displayable()},hex:G,formatHex:G,formatHex8:function(){return this.rgb().formatHex8()},formatHsl:function(){return es(this).formatHsl()},formatRgb:Q,toString:Q}),L(en,er,z(F,{brighter(e){return e=null==e?1.4285714285714286:Math.pow(1.4285714285714286,e),new en(this.r*e,this.g*e,this.b*e,this.opacity)},darker(e){return e=null==e?.7:Math.pow(.7,e),new en(this.r*e,this.g*e,this.b*e,this.opacity)},rgb(){return this},clamp(){return new en(eu(this.r),eu(this.g),eu(this.b),eo(this.opacity))},displayable(){return -.5<=this.r&&this.r<255.5&&-.5<=this.g&&this.g<255.5&&-.5<=this.b&&this.b<255.5&&0<=this.opacity&&this.opacity<=1},hex:ei,formatHex:ei,formatHex8:function(){return`#${el(this.r)}${el(this.g)}${el(this.b)}${el((isNaN(this.opacity)?1:this.opacity)*255)}`},formatRgb:ea,toString:ea})),L(ef,function(e,t,r,n){return 1==arguments.length?es(e):new ef(e,t,r,null==n?1:n)},z(F,{brighter(e){return e=null==e?1.4285714285714286:Math.pow(1.4285714285714286,e),new ef(this.h,this.s,this.l*e,this.opacity)},darker(e){return e=null==e?.7:Math.pow(.7,e),new ef(this.h,this.s,this.l*e,this.opacity)},rgb(){var e=this.h%360+(this.h<0)*360,t=isNaN(e)||isNaN(this.s)?0:this.s,r=this.l,n=r+(r<.5?r:1-r)*t,i=2*r-n;return new en(eh(e>=240?e-240:e+120,i,n),eh(e,i,n),eh(e<120?e+240:e-120,i,n),this.opacity)},clamp(){return new ef(ed(this.h),ep(this.s),ep(this.l),eo(this.opacity))},displayable(){return(0<=this.s&&this.s<=1||isNaN(this.s))&&0<=this.l&&this.l<=1&&0<=this.opacity&&this.opacity<=1},formatHsl(){let e=eo(this.opacity);return`${1===e?"hsl(":"hsla("}${ed(this.h)}, ${100*ep(this.s)}%, ${100*ep(this.l)}%${1===e?")":`, ${e})`}`}}));var ey=e=>()=>e;function eg(e,t){var r=t-e;return r?function(t){return e+t*r}:ey(isNaN(e)?t:e)}var em=function e(t){var r,n=1==(r=+(r=t))?eg:function(e,t){var n,i,a;return t-e?(n=e,i=t,n=Math.pow(n,a=r),i=Math.pow(i,a)-n,a=1/a,function(e){return Math.pow(n+e*i,a)}):ey(isNaN(e)?t:e)};function i(e,t){var r=n((e=er(e)).r,(t=er(t)).r),i=n(e.g,t.g),a=n(e.b,t.b),o=eg(e.opacity,t.opacity);return function(t){return e.r=r(t),e.g=i(t),e.b=a(t),e.opacity=o(t),e+""}}return i.gamma=e,i}(1);function eb(e){return function(t){var r,n,i=t.length,a=Array(i),o=Array(i),u=Array(i);for(r=0;r<i;++r)n=er(t[r]),a[r]=n.r||0,o[r]=n.g||0,u[r]=n.b||0;return a=e(a),o=e(o),u=e(u),n.opacity=1,function(e){return n.r=a(e),n.g=o(e),n.b=u(e),n+""}}}function ex(e,t){return e=+e,t=+t,function(r){return e*(1-r)+t*r}}eb(function(e){var t=e.length-1;return function(r){var n=r<=0?r=0:r>=1?(r=1,t-1):Math.floor(r*t),i=e[n],a=e[n+1],o=n>0?e[n-1]:2*i-a,u=n<t-1?e[n+2]:2*a-i;return ev((r-n/t)*t,o,i,a,u)}}),eb(function(e){var t=e.length;return function(r){var n=Math.floor(((r%=1)<0?++r:r)*t),i=e[(n+t-1)%t],a=e[n%t],o=e[(n+1)%t],u=e[(n+2)%t];return ev((r-n/t)*t,i,a,o,u)}});var ew=/[-+]?(?:\d+\.?\d*|\.?\d+)(?:[eE][-+]?\d+)?/g,eO=RegExp(ew.source,"g");function ej(e,t){var r,n,i=typeof t;return null==t||"boolean"===i?ey(t):("number"===i?ex:"string"===i?(n=J(t))?(t=n,em):function(e,t){var r,n,i,a,o,u=ew.lastIndex=eO.lastIndex=0,l=-1,c=[],s=[];for(e+="",t+="";(i=ew.exec(e))&&(a=eO.exec(t));)(o=a.index)>u&&(o=t.slice(u,o),c[l]?c[l]+=o:c[++l]=o),(i=i[0])===(a=a[0])?c[l]?c[l]+=a:c[++l]=a:(c[++l]=null,s.push({i:l,x:ex(i,a)})),u=eO.lastIndex;return u<t.length&&(o=t.slice(u),c[l]?c[l]+=o:c[++l]=o),c.length<2?s[0]?(r=s[0].x,function(e){return r(e)+""}):(n=t,function(){return n}):(t=s.length,function(e){for(var r,n=0;n<t;++n)c[(r=s[n]).i]=r.x(e);return c.join("")})}:t instanceof J?em:t instanceof Date?function(e,t){var r=new Date;return e=+e,t=+t,function(n){return r.setTime(e*(1-n)+t*n),r}}:!ArrayBuffer.isView(r=t)||r instanceof DataView?Array.isArray(t)?function(e,t){var r,n=t?t.length:0,i=e?Math.min(n,e.length):0,a=Array(i),o=Array(n);for(r=0;r<i;++r)a[r]=ej(e[r],t[r]);for(;r<n;++r)o[r]=t[r];return function(e){for(r=0;r<i;++r)o[r]=a[r](e);return o}}:"function"!=typeof t.valueOf&&"function"!=typeof t.toString||isNaN(t)?function(e,t){var r,n={},i={};for(r in(null===e||"object"!=typeof e)&&(e={}),(null===t||"object"!=typeof t)&&(t={}),t)r in e?n[r]=ej(e[r],t[r]):i[r]=t[r];return function(e){for(r in n)i[r]=n[r](e);return i}}:ex:function(e,t){t||(t=[]);var r,n=e?Math.min(t.length,e.length):0,i=t.slice();return function(a){for(r=0;r<n;++r)i[r]=e[r]*(1-a)+t[r]*a;return i}})(e,t)}function eP(e,t){return e=+e,t=+t,function(r){return Math.round(e*(1-r)+t*r)}}function ek(e){return+e}var eM=[0,1];function eS(e){return e}function eA(e,t){var r;return(t-=e=+e)?function(r){return(r-e)/t}:(r=isNaN(t)?NaN:.5,function(){return r})}function eE(e,t,r){var n=e[0],i=e[1],a=t[0],o=t[1];return i<n?(n=eA(i,n),a=r(o,a)):(n=eA(n,i),a=r(a,o)),function(e){return a(n(e))}}function eC(e,t,r){var n=Math.min(e.length,t.length)-1,i=Array(n),a=Array(n),o=-1;for(e[n]<e[0]&&(e=e.slice().reverse(),t=t.slice().reverse());++o<n;)i[o]=eA(e[o],e[o+1]),a[o]=r(t[o],t[o+1]);return function(t){var r=R(e,t,1,n)-1;return a[r](i[r](t))}}function eT(e,t){return t.domain(e.domain()).range(e.range()).interpolate(e.interpolate()).clamp(e.clamp()).unknown(e.unknown())}function eD(){var e,t,r,n,i,a,o=eM,u=eM,l=ej,c=eS;function s(){var e,t,r,l=Math.min(o.length,u.length);return c!==eS&&(e=o[0],t=o[l-1],e>t&&(r=e,e=t,t=r),c=function(r){return Math.max(e,Math.min(t,r))}),n=l>2?eC:eE,i=a=null,f}function f(t){return null==t||isNaN(t=+t)?r:(i||(i=n(o.map(e),u,l)))(e(c(t)))}return f.invert=function(r){return c(t((a||(a=n(u,o.map(e),ex)))(r)))},f.domain=function(e){return arguments.length?(o=Array.from(e,ek),s()):o.slice()},f.range=function(e){return arguments.length?(u=Array.from(e),s()):u.slice()},f.rangeRound=function(e){return u=Array.from(e),l=eP,s()},f.clamp=function(e){return arguments.length?(c=!!e||eS,s()):c!==eS},f.interpolate=function(e){return arguments.length?(l=e,s()):l},f.unknown=function(e){return arguments.length?(r=e,f):r},function(r,n){return e=r,t=n,s()}}function eN(){return eD()(eS,eS)}var e_=/^(?:(.)?([<>=^]))?([+\-( ])?([$#])?(0)?(\d+)?(,)?(\.\d+)?(~)?([a-z%])?$/i;function eI(e){var t;if(!(t=e_.exec(e)))throw Error("invalid format: "+e);return new eR({fill:t[1],align:t[2],sign:t[3],symbol:t[4],zero:t[5],width:t[6],comma:t[7],precision:t[8]&&t[8].slice(1),trim:t[9],type:t[10]})}function eR(e){this.fill=void 0===e.fill?" ":e.fill+"",this.align=void 0===e.align?">":e.align+"",this.sign=void 0===e.sign?"-":e.sign+"",this.symbol=void 0===e.symbol?"":e.symbol+"",this.zero=!!e.zero,this.width=void 0===e.width?void 0:+e.width,this.comma=!!e.comma,this.precision=void 0===e.precision?void 0:+e.precision,this.trim=!!e.trim,this.type=void 0===e.type?"":e.type+""}function eL(e,t){if((r=(e=t?e.toExponential(t-1):e.toExponential()).indexOf("e"))<0)return null;var r,n=e.slice(0,r);return[n.length>1?n[0]+n.slice(2):n,+e.slice(r+1)]}function ez(e){return(e=eL(Math.abs(e)))?e[1]:NaN}function eF(e,t){var r=eL(e,t);if(!r)return e+"";var n=r[0],i=r[1];return i<0?"0."+Array(-i).join("0")+n:n.length>i+1?n.slice(0,i+1)+"."+n.slice(i+1):n+Array(i-n.length+2).join("0")}eI.prototype=eR.prototype,eR.prototype.toString=function(){return this.fill+this.align+this.sign+this.symbol+(this.zero?"0":"")+(void 0===this.width?"":Math.max(1,0|this.width))+(this.comma?",":"")+(void 0===this.precision?"":"."+Math.max(0,0|this.precision))+(this.trim?"~":"")+this.type};var eW={"%":(e,t)=>(100*e).toFixed(t),b:e=>Math.round(e).toString(2),c:e=>e+"",d:function(e){return Math.abs(e=Math.round(e))>=1e21?e.toLocaleString("en").replace(/,/g,""):e.toString(10)},e:(e,t)=>e.toExponential(t),f:(e,t)=>e.toFixed(t),g:(e,t)=>e.toPrecision(t),o:e=>Math.round(e).toString(8),p:(e,t)=>eF(100*e,t),r:eF,s:function(e,t){var r=eL(e,t);if(!r)return e+"";var i=r[0],a=r[1],o=a-(n=3*Math.max(-8,Math.min(8,Math.floor(a/3))))+1,u=i.length;return o===u?i:o>u?i+Array(o-u+1).join("0"):o>0?i.slice(0,o)+"."+i.slice(o):"0."+Array(1-o).join("0")+eL(e,Math.max(0,t+o-1))[0]},X:e=>Math.round(e).toString(16).toUpperCase(),x:e=>Math.round(e).toString(16)};function eB(e){return e}var eK=Array.prototype.map,eZ=["y","z","a","f","p","n","\xb5","m","","k","M","G","T","P","E","Z","Y"];function eU(e,t,r,n){var i,u,l=E(e,t,r);switch((n=eI(null==n?",f":n)).type){case"s":var c=Math.max(Math.abs(e),Math.abs(t));return null!=n.precision||isNaN(u=Math.max(0,3*Math.max(-8,Math.min(8,Math.floor(ez(c)/3)))-ez(Math.abs(l))))||(n.precision=u),o(n,c);case"":case"e":case"g":case"p":case"r":null!=n.precision||isNaN(u=Math.max(0,ez(Math.abs(Math.max(Math.abs(e),Math.abs(t)))-(i=Math.abs(i=l)))-ez(i))+1)||(n.precision=u-("e"===n.type));break;case"f":case"%":null!=n.precision||isNaN(u=Math.max(0,-ez(Math.abs(l))))||(n.precision=u-("%"===n.type)*2)}return a(n)}function eV(e){var t=e.domain;return e.ticks=function(e){var r=t();return S(r[0],r[r.length-1],null==e?10:e)},e.tickFormat=function(e,r){var n=t();return eU(n[0],n[n.length-1],null==e?10:e,r)},e.nice=function(r){null==r&&(r=10);var n,i,a=t(),o=0,u=a.length-1,l=a[o],c=a[u],s=10;for(c<l&&(i=l,l=c,c=i,i=o,o=u,u=i);s-- >0;){if((i=A(l,c,r))===n)return a[o]=l,a[u]=c,t(a);if(i>0)l=Math.floor(l/i)*i,c=Math.ceil(c/i)*i;else if(i<0)l=Math.ceil(l*i)/i,c=Math.floor(c*i)/i;else break;n=i}return e},e}function e$(e,t){e=e.slice();var r,n=0,i=e.length-1,a=e[n],o=e[i];return o<a&&(r=n,n=i,i=r,r=a,a=o,o=r),e[n]=t.floor(a),e[i]=t.ceil(o),e}function eY(e){return Math.log(e)}function eH(e){return Math.exp(e)}function eq(e){return-Math.log(-e)}function eX(e){return-Math.exp(-e)}function eG(e){return isFinite(e)?+("1e"+e):e<0?0:e}function eQ(e){return(t,r)=>-e(-t,r)}function eJ(e){let t,r;let n=e(eY,eH),i=n.domain,o=10;function u(){var a,u;return t=(a=o)===Math.E?Math.log:10===a&&Math.log10||2===a&&Math.log2||(a=Math.log(a),e=>Math.log(e)/a),r=10===(u=o)?eG:u===Math.E?Math.exp:e=>Math.pow(u,e),i()[0]<0?(t=eQ(t),r=eQ(r),e(eq,eX)):e(eY,eH),n}return n.base=function(e){return arguments.length?(o=+e,u()):o},n.domain=function(e){return arguments.length?(i(e),u()):i()},n.ticks=e=>{let n,a;let u=i(),l=u[0],c=u[u.length-1],s=c<l;s&&([l,c]=[c,l]);let f=t(l),d=t(c),p=null==e?10:+e,h=[];if(!(o%1)&&d-f<p){if(f=Math.floor(f),d=Math.ceil(d),l>0){for(;f<=d;++f)for(n=1;n<o;++n)if(!((a=f<0?n/r(-f):n*r(f))<l)){if(a>c)break;h.push(a)}}else for(;f<=d;++f)for(n=o-1;n>=1;--n)if(!((a=f>0?n/r(-f):n*r(f))<l)){if(a>c)break;h.push(a)}2*h.length<p&&(h=S(l,c,p))}else h=S(f,d,Math.min(d-f,p)).map(r);return s?h.reverse():h},n.tickFormat=(e,i)=>{if(null==e&&(e=10),null==i&&(i=10===o?"s":","),"function"!=typeof i&&(o%1||null!=(i=eI(i)).precision||(i.trim=!0),i=a(i)),e===1/0)return i;let u=Math.max(1,o*e/n.ticks().length);return e=>{let n=e/r(Math.round(t(e)));return n*o<o-.5&&(n*=o),n<=u?i(e):""}},n.nice=()=>i(e$(i(),{floor:e=>r(Math.floor(t(e))),ceil:e=>r(Math.ceil(t(e)))})),n}function e0(e){return function(t){return Math.sign(t)*Math.log1p(Math.abs(t/e))}}function e1(e){return function(t){return Math.sign(t)*Math.expm1(Math.abs(t))*e}}function e2(e){var t=1,r=e(e0(1),e1(t));return r.constant=function(r){return arguments.length?e(e0(t=+r),e1(t)):t},eV(r)}function e6(e){return function(t){return t<0?-Math.pow(-t,e):Math.pow(t,e)}}function e5(e){return e<0?-Math.sqrt(-e):Math.sqrt(e)}function e3(e){return e<0?-e*e:e*e}function e4(e){var t=e(eS,eS),r=1;return t.exponent=function(t){return arguments.length?1==(r=+t)?e(eS,eS):.5===r?e(e5,e3):e(e6(r),e6(1/r)):r},eV(t)}function e9(){var e=e4(eD());return e.copy=function(){return eT(e,e9()).exponent(e.exponent())},h.apply(e,arguments),e}function e7(){return e9.apply(null,arguments).exponent(.5)}function e8(e){return Math.sign(e)*e*e}function te(e,t){let r;if(void 0===t)for(let t of e)null!=t&&(r<t||void 0===r&&t>=t)&&(r=t);else{let n=-1;for(let i of e)null!=(i=t(i,++n,e))&&(r<i||void 0===r&&i>=i)&&(r=i)}return r}function tt(e,t){let r;if(void 0===t)for(let t of e)null!=t&&(r>t||void 0===r&&t>=t)&&(r=t);else{let n=-1;for(let i of e)null!=(i=t(i,++n,e))&&(r>i||void 0===r&&i>=i)&&(r=i)}return r}function tr(e,t){return(null==e||!(e>=e))-(null==t||!(t>=t))||(e<t?-1:e>t?1:0)}function tn(e,t,r){let n=e[t];e[t]=e[r],e[r]=n}a=(i=function(e){var t,r,i,a=void 0===e.grouping||void 0===e.thousands?eB:(t=eK.call(e.grouping,Number),r=e.thousands+"",function(e,n){for(var i=e.length,a=[],o=0,u=t[0],l=0;i>0&&u>0&&(l+u+1>n&&(u=Math.max(1,n-l)),a.push(e.substring(i-=u,i+u)),!((l+=u+1)>n));)u=t[o=(o+1)%t.length];return a.reverse().join(r)}),o=void 0===e.currency?"":e.currency[0]+"",u=void 0===e.currency?"":e.currency[1]+"",l=void 0===e.decimal?".":e.decimal+"",c=void 0===e.numerals?eB:(i=eK.call(e.numerals,String),function(e){return e.replace(/[0-9]/g,function(e){return i[+e]})}),s=void 0===e.percent?"%":e.percent+"",f=void 0===e.minus?"−":e.minus+"",d=void 0===e.nan?"NaN":e.nan+"";function p(e){var t=(e=eI(e)).fill,r=e.align,i=e.sign,p=e.symbol,h=e.zero,v=e.width,y=e.comma,g=e.precision,m=e.trim,b=e.type;"n"===b?(y=!0,b="g"):eW[b]||(void 0===g&&(g=12),m=!0,b="g"),(h||"0"===t&&"="===r)&&(h=!0,t="0",r="=");var x="$"===p?o:"#"===p&&/[boxX]/.test(b)?"0"+b.toLowerCase():"",w="$"===p?u:/[%p]/.test(b)?s:"",O=eW[b],j=/[defgprs%]/.test(b);function P(e){var o,u,s,p=x,P=w;if("c"===b)P=O(e)+P,e="";else{var k=(e=+e)<0||1/e<0;if(e=isNaN(e)?d:O(Math.abs(e),g),m&&(e=function(e){e:for(var t,r=e.length,n=1,i=-1;n<r;++n)switch(e[n]){case".":i=t=n;break;case"0":0===i&&(i=n),t=n;break;default:if(!+e[n])break e;i>0&&(i=0)}return i>0?e.slice(0,i)+e.slice(t+1):e}(e)),k&&0==+e&&"+"!==i&&(k=!1),p=(k?"("===i?i:f:"-"===i||"("===i?"":i)+p,P=("s"===b?eZ[8+n/3]:"")+P+(k&&"("===i?")":""),j){for(o=-1,u=e.length;++o<u;)if(48>(s=e.charCodeAt(o))||s>57){P=(46===s?l+e.slice(o+1):e.slice(o))+P,e=e.slice(0,o);break}}}y&&!h&&(e=a(e,1/0));var M=p.length+e.length+P.length,S=M<v?Array(v-M+1).join(t):"";switch(y&&h&&(e=a(S+e,S.length?v-P.length:1/0),S=""),r){case"<":e=p+e+P+S;break;case"=":e=p+S+e+P;break;case"^":e=S.slice(0,M=S.length>>1)+p+e+P+S.slice(M);break;default:e=S+p+e+P}return c(e)}return g=void 0===g?6:/[gprs]/.test(b)?Math.max(1,Math.min(21,g)):Math.max(0,Math.min(20,g)),P.toString=function(){return e+""},P}return{format:p,formatPrefix:function(e,t){var r=p(((e=eI(e)).type="f",e)),n=3*Math.max(-8,Math.min(8,Math.floor(ez(t)/3))),i=Math.pow(10,-n),a=eZ[8+n/3];return function(e){return r(i*e)+a}}}}({thousands:",",grouping:[3],currency:["$",""]})).format,o=i.formatPrefix;let ti=new Date,ta=new Date;function to(e,t,r,n){function i(t){return e(t=0==arguments.length?new Date:new Date(+t)),t}return i.floor=t=>(e(t=new Date(+t)),t),i.ceil=r=>(e(r=new Date(r-1)),t(r,1),e(r),r),i.round=e=>{let t=i(e),r=i.ceil(e);return e-t<r-e?t:r},i.offset=(e,r)=>(t(e=new Date(+e),null==r?1:Math.floor(r)),e),i.range=(r,n,a)=>{let o;let u=[];if(r=i.ceil(r),a=null==a?1:Math.floor(a),!(r<n)||!(a>0))return u;do u.push(o=new Date(+r)),t(r,a),e(r);while(o<r&&r<n);return u},i.filter=r=>to(t=>{if(t>=t)for(;e(t),!r(t);)t.setTime(t-1)},(e,n)=>{if(e>=e){if(n<0)for(;++n<=0;)for(;t(e,-1),!r(e););else for(;--n>=0;)for(;t(e,1),!r(e););}}),r&&(i.count=(t,n)=>(ti.setTime(+t),ta.setTime(+n),e(ti),e(ta),Math.floor(r(ti,ta))),i.every=e=>isFinite(e=Math.floor(e))&&e>0?e>1?i.filter(n?t=>n(t)%e==0:t=>i.count(0,t)%e==0):i:null),i}let tu=to(()=>{},(e,t)=>{e.setTime(+e+t)},(e,t)=>t-e);tu.every=e=>isFinite(e=Math.floor(e))&&e>0?e>1?to(t=>{t.setTime(Math.floor(t/e)*e)},(t,r)=>{t.setTime(+t+r*e)},(t,r)=>(r-t)/e):tu:null,tu.range;let tl=to(e=>{e.setTime(e-e.getMilliseconds())},(e,t)=>{e.setTime(+e+1e3*t)},(e,t)=>(t-e)/1e3,e=>e.getUTCSeconds());tl.range;let tc=to(e=>{e.setTime(e-e.getMilliseconds()-1e3*e.getSeconds())},(e,t)=>{e.setTime(+e+6e4*t)},(e,t)=>(t-e)/6e4,e=>e.getMinutes());tc.range;let ts=to(e=>{e.setUTCSeconds(0,0)},(e,t)=>{e.setTime(+e+6e4*t)},(e,t)=>(t-e)/6e4,e=>e.getUTCMinutes());ts.range;let tf=to(e=>{e.setTime(e-e.getMilliseconds()-1e3*e.getSeconds()-6e4*e.getMinutes())},(e,t)=>{e.setTime(+e+36e5*t)},(e,t)=>(t-e)/36e5,e=>e.getHours());tf.range;let td=to(e=>{e.setUTCMinutes(0,0,0)},(e,t)=>{e.setTime(+e+36e5*t)},(e,t)=>(t-e)/36e5,e=>e.getUTCHours());td.range;let tp=to(e=>e.setHours(0,0,0,0),(e,t)=>e.setDate(e.getDate()+t),(e,t)=>(t-e-(t.getTimezoneOffset()-e.getTimezoneOffset())*6e4)/864e5,e=>e.getDate()-1);tp.range;let th=to(e=>{e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCDate(e.getUTCDate()+t)},(e,t)=>(t-e)/864e5,e=>e.getUTCDate()-1);th.range;let tv=to(e=>{e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCDate(e.getUTCDate()+t)},(e,t)=>(t-e)/864e5,e=>Math.floor(e/864e5));function ty(e){return to(t=>{t.setDate(t.getDate()-(t.getDay()+7-e)%7),t.setHours(0,0,0,0)},(e,t)=>{e.setDate(e.getDate()+7*t)},(e,t)=>(t-e-(t.getTimezoneOffset()-e.getTimezoneOffset())*6e4)/6048e5)}tv.range;let tg=ty(0),tm=ty(1),tb=ty(2),tx=ty(3),tw=ty(4),tO=ty(5),tj=ty(6);function tP(e){return to(t=>{t.setUTCDate(t.getUTCDate()-(t.getUTCDay()+7-e)%7),t.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCDate(e.getUTCDate()+7*t)},(e,t)=>(t-e)/6048e5)}tg.range,tm.range,tb.range,tx.range,tw.range,tO.range,tj.range;let tk=tP(0),tM=tP(1),tS=tP(2),tA=tP(3),tE=tP(4),tC=tP(5),tT=tP(6);tk.range,tM.range,tS.range,tA.range,tE.range,tC.range,tT.range;let tD=to(e=>{e.setDate(1),e.setHours(0,0,0,0)},(e,t)=>{e.setMonth(e.getMonth()+t)},(e,t)=>t.getMonth()-e.getMonth()+(t.getFullYear()-e.getFullYear())*12,e=>e.getMonth());tD.range;let tN=to(e=>{e.setUTCDate(1),e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCMonth(e.getUTCMonth()+t)},(e,t)=>t.getUTCMonth()-e.getUTCMonth()+(t.getUTCFullYear()-e.getUTCFullYear())*12,e=>e.getUTCMonth());tN.range;let t_=to(e=>{e.setMonth(0,1),e.setHours(0,0,0,0)},(e,t)=>{e.setFullYear(e.getFullYear()+t)},(e,t)=>t.getFullYear()-e.getFullYear(),e=>e.getFullYear());t_.every=e=>isFinite(e=Math.floor(e))&&e>0?to(t=>{t.setFullYear(Math.floor(t.getFullYear()/e)*e),t.setMonth(0,1),t.setHours(0,0,0,0)},(t,r)=>{t.setFullYear(t.getFullYear()+r*e)}):null,t_.range;let tI=to(e=>{e.setUTCMonth(0,1),e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCFullYear(e.getUTCFullYear()+t)},(e,t)=>t.getUTCFullYear()-e.getUTCFullYear(),e=>e.getUTCFullYear());function tR(e,t,r,n,i,a){let o=[[tl,1,1e3],[tl,5,5e3],[tl,15,15e3],[tl,30,3e4],[a,1,6e4],[a,5,3e5],[a,15,9e5],[a,30,18e5],[i,1,36e5],[i,3,108e5],[i,6,216e5],[i,12,432e5],[n,1,864e5],[n,2,1728e5],[r,1,6048e5],[t,1,2592e6],[t,3,7776e6],[e,1,31536e6]];function u(t,r,n){let i=Math.abs(r-t)/n,a=D(([,,e])=>e).right(o,i);if(a===o.length)return e.every(E(t/31536e6,r/31536e6,n));if(0===a)return tu.every(Math.max(E(t,r,n),1));let[u,l]=o[i/o[a-1][2]<o[a][2]/i?a-1:a];return u.every(l)}return[function(e,t,r){let n=t<e;n&&([e,t]=[t,e]);let i=r&&"function"==typeof r.range?r:u(e,t,r),a=i?i.range(e,+t+1):[];return n?a.reverse():a},u]}tI.every=e=>isFinite(e=Math.floor(e))&&e>0?to(t=>{t.setUTCFullYear(Math.floor(t.getUTCFullYear()/e)*e),t.setUTCMonth(0,1),t.setUTCHours(0,0,0,0)},(t,r)=>{t.setUTCFullYear(t.getUTCFullYear()+r*e)}):null,tI.range;let[tL,tz]=tR(tI,tN,tk,tv,td,ts),[tF,tW]=tR(t_,tD,tg,tp,tf,tc);function tB(e){if(0<=e.y&&e.y<100){var t=new Date(-1,e.m,e.d,e.H,e.M,e.S,e.L);return t.setFullYear(e.y),t}return new Date(e.y,e.m,e.d,e.H,e.M,e.S,e.L)}function tK(e){if(0<=e.y&&e.y<100){var t=new Date(Date.UTC(-1,e.m,e.d,e.H,e.M,e.S,e.L));return t.setUTCFullYear(e.y),t}return new Date(Date.UTC(e.y,e.m,e.d,e.H,e.M,e.S,e.L))}function tZ(e,t,r){return{y:e,m:t,d:r,H:0,M:0,S:0,L:0}}var tU={"-":"",_:" ",0:"0"},tV=/^\s*\d+/,t$=/^%/,tY=/[\\^$*+?|[\]().{}]/g;function tH(e,t,r){var n=e<0?"-":"",i=(n?-e:e)+"",a=i.length;return n+(a<r?Array(r-a+1).join(t)+i:i)}function tq(e){return e.replace(tY,"\\$&")}function tX(e){return RegExp("^(?:"+e.map(tq).join("|")+")","i")}function tG(e){return new Map(e.map((e,t)=>[e.toLowerCase(),t]))}function tQ(e,t,r){var n=tV.exec(t.slice(r,r+1));return n?(e.w=+n[0],r+n[0].length):-1}function tJ(e,t,r){var n=tV.exec(t.slice(r,r+1));return n?(e.u=+n[0],r+n[0].length):-1}function t0(e,t,r){var n=tV.exec(t.slice(r,r+2));return n?(e.U=+n[0],r+n[0].length):-1}function t1(e,t,r){var n=tV.exec(t.slice(r,r+2));return n?(e.V=+n[0],r+n[0].length):-1}function t2(e,t,r){var n=tV.exec(t.slice(r,r+2));return n?(e.W=+n[0],r+n[0].length):-1}function t6(e,t,r){var n=tV.exec(t.slice(r,r+4));return n?(e.y=+n[0],r+n[0].length):-1}function t5(e,t,r){var n=tV.exec(t.slice(r,r+2));return n?(e.y=+n[0]+(+n[0]>68?1900:2e3),r+n[0].length):-1}function t3(e,t,r){var n=/^(Z)|([+-]\d\d)(?::?(\d\d))?/.exec(t.slice(r,r+6));return n?(e.Z=n[1]?0:-(n[2]+(n[3]||"00")),r+n[0].length):-1}function t4(e,t,r){var n=tV.exec(t.slice(r,r+1));return n?(e.q=3*n[0]-3,r+n[0].length):-1}function t9(e,t,r){var n=tV.exec(t.slice(r,r+2));return n?(e.m=n[0]-1,r+n[0].length):-1}function t7(e,t,r){var n=tV.exec(t.slice(r,r+2));return n?(e.d=+n[0],r+n[0].length):-1}function t8(e,t,r){var n=tV.exec(t.slice(r,r+3));return n?(e.m=0,e.d=+n[0],r+n[0].length):-1}function re(e,t,r){var n=tV.exec(t.slice(r,r+2));return n?(e.H=+n[0],r+n[0].length):-1}function rt(e,t,r){var n=tV.exec(t.slice(r,r+2));return n?(e.M=+n[0],r+n[0].length):-1}function rr(e,t,r){var n=tV.exec(t.slice(r,r+2));return n?(e.S=+n[0],r+n[0].length):-1}function rn(e,t,r){var n=tV.exec(t.slice(r,r+3));return n?(e.L=+n[0],r+n[0].length):-1}function ri(e,t,r){var n=tV.exec(t.slice(r,r+6));return n?(e.L=Math.floor(n[0]/1e3),r+n[0].length):-1}function ra(e,t,r){var n=t$.exec(t.slice(r,r+1));return n?r+n[0].length:-1}function ro(e,t,r){var n=tV.exec(t.slice(r));return n?(e.Q=+n[0],r+n[0].length):-1}function ru(e,t,r){var n=tV.exec(t.slice(r));return n?(e.s=+n[0],r+n[0].length):-1}function rl(e,t){return tH(e.getDate(),t,2)}function rc(e,t){return tH(e.getHours(),t,2)}function rs(e,t){return tH(e.getHours()%12||12,t,2)}function rf(e,t){return tH(1+tp.count(t_(e),e),t,3)}function rd(e,t){return tH(e.getMilliseconds(),t,3)}function rp(e,t){return rd(e,t)+"000"}function rh(e,t){return tH(e.getMonth()+1,t,2)}function rv(e,t){return tH(e.getMinutes(),t,2)}function ry(e,t){return tH(e.getSeconds(),t,2)}function rg(e){var t=e.getDay();return 0===t?7:t}function rm(e,t){return tH(tg.count(t_(e)-1,e),t,2)}function rb(e){var t=e.getDay();return t>=4||0===t?tw(e):tw.ceil(e)}function rx(e,t){return e=rb(e),tH(tw.count(t_(e),e)+(4===t_(e).getDay()),t,2)}function rw(e){return e.getDay()}function rO(e,t){return tH(tm.count(t_(e)-1,e),t,2)}function rj(e,t){return tH(e.getFullYear()%100,t,2)}function rP(e,t){return tH((e=rb(e)).getFullYear()%100,t,2)}function rk(e,t){return tH(e.getFullYear()%1e4,t,4)}function rM(e,t){var r=e.getDay();return tH((e=r>=4||0===r?tw(e):tw.ceil(e)).getFullYear()%1e4,t,4)}function rS(e){var t=e.getTimezoneOffset();return(t>0?"-":(t*=-1,"+"))+tH(t/60|0,"0",2)+tH(t%60,"0",2)}function rA(e,t){return tH(e.getUTCDate(),t,2)}function rE(e,t){return tH(e.getUTCHours(),t,2)}function rC(e,t){return tH(e.getUTCHours()%12||12,t,2)}function rT(e,t){return tH(1+th.count(tI(e),e),t,3)}function rD(e,t){return tH(e.getUTCMilliseconds(),t,3)}function rN(e,t){return rD(e,t)+"000"}function r_(e,t){return tH(e.getUTCMonth()+1,t,2)}function rI(e,t){return tH(e.getUTCMinutes(),t,2)}function rR(e,t){return tH(e.getUTCSeconds(),t,2)}function rL(e){var t=e.getUTCDay();return 0===t?7:t}function rz(e,t){return tH(tk.count(tI(e)-1,e),t,2)}function rF(e){var t=e.getUTCDay();return t>=4||0===t?tE(e):tE.ceil(e)}function rW(e,t){return e=rF(e),tH(tE.count(tI(e),e)+(4===tI(e).getUTCDay()),t,2)}function rB(e){return e.getUTCDay()}function rK(e,t){return tH(tM.count(tI(e)-1,e),t,2)}function rZ(e,t){return tH(e.getUTCFullYear()%100,t,2)}function rU(e,t){return tH((e=rF(e)).getUTCFullYear()%100,t,2)}function rV(e,t){return tH(e.getUTCFullYear()%1e4,t,4)}function r$(e,t){var r=e.getUTCDay();return tH((e=r>=4||0===r?tE(e):tE.ceil(e)).getUTCFullYear()%1e4,t,4)}function rY(){return"+0000"}function rH(){return"%"}function rq(e){return+e}function rX(e){return Math.floor(+e/1e3)}function rG(e){return new Date(e)}function rQ(e){return e instanceof Date?+e:+new Date(+e)}function rJ(e,t,r,n,i,a,o,u,l,c){var s=eN(),f=s.invert,d=s.domain,p=c(".%L"),h=c(":%S"),v=c("%I:%M"),y=c("%I %p"),g=c("%a %d"),m=c("%b %d"),b=c("%B"),x=c("%Y");function w(e){return(l(e)<e?p:u(e)<e?h:o(e)<e?v:a(e)<e?y:n(e)<e?i(e)<e?g:m:r(e)<e?b:x)(e)}return s.invert=function(e){return new Date(f(e))},s.domain=function(e){return arguments.length?d(Array.from(e,rQ)):d().map(rG)},s.ticks=function(t){var r=d();return e(r[0],r[r.length-1],null==t?10:t)},s.tickFormat=function(e,t){return null==t?w:c(t)},s.nice=function(e){var r=d();return e&&"function"==typeof e.range||(e=t(r[0],r[r.length-1],null==e?10:e)),e?d(e$(r,e)):s},s.copy=function(){return eT(s,rJ(e,t,r,n,i,a,o,u,l,c))},s}function r0(){return h.apply(rJ(tF,tW,t_,tD,tg,tp,tf,tc,tl,l).domain([new Date(2e3,0,1),new Date(2e3,0,2)]),arguments)}function r1(){return h.apply(rJ(tL,tz,tI,tN,tk,th,td,ts,tl,c).domain([Date.UTC(2e3,0,1),Date.UTC(2e3,0,2)]),arguments)}function r2(){var e,t,r,n,i,a=0,o=1,u=eS,l=!1;function c(t){return null==t||isNaN(t=+t)?i:u(0===r?.5:(t=(n(t)-e)*r,l?Math.max(0,Math.min(1,t)):t))}function s(e){return function(t){var r,n;return arguments.length?([r,n]=t,u=e(r,n),c):[u(0),u(1)]}}return c.domain=function(i){return arguments.length?([a,o]=i,e=n(a=+a),t=n(o=+o),r=e===t?0:1/(t-e),c):[a,o]},c.clamp=function(e){return arguments.length?(l=!!e,c):l},c.interpolator=function(e){return arguments.length?(u=e,c):u},c.range=s(ej),c.rangeRound=s(eP),c.unknown=function(e){return arguments.length?(i=e,c):i},function(i){return n=i,e=i(a),t=i(o),r=e===t?0:1/(t-e),c}}function r6(e,t){return t.domain(e.domain()).interpolator(e.interpolator()).clamp(e.clamp()).unknown(e.unknown())}function r5(){var e=e4(r2());return e.copy=function(){return r6(e,r5()).exponent(e.exponent())},v.apply(e,arguments)}function r3(){return r5.apply(null,arguments).exponent(.5)}function r4(){var e,t,r,n,i,a,o,u=0,l=.5,c=1,s=1,f=eS,d=!1;function p(e){return isNaN(e=+e)?o:(e=.5+((e=+a(e))-t)*(s*e<s*t?n:i),f(d?Math.max(0,Math.min(1,e)):e))}function h(e){return function(t){var r,n,i;return arguments.length?([r,n,i]=t,f=function(e,t){void 0===t&&(t=e,e=ej);for(var r=0,n=t.length-1,i=t[0],a=Array(n<0?0:n);r<n;)a[r]=e(i,i=t[++r]);return function(e){var t=Math.max(0,Math.min(n-1,Math.floor(e*=n)));return a[t](e-t)}}(e,[r,n,i]),p):[f(0),f(.5),f(1)]}}return p.domain=function(o){return arguments.length?([u,l,c]=o,e=a(u=+u),t=a(l=+l),r=a(c=+c),n=e===t?0:.5/(t-e),i=t===r?0:.5/(r-t),s=t<e?-1:1,p):[u,l,c]},p.clamp=function(e){return arguments.length?(d=!!e,p):d},p.interpolator=function(e){return arguments.length?(f=e,p):f},p.range=h(ej),p.rangeRound=h(eP),p.unknown=function(e){return arguments.length?(o=e,p):o},function(o){return a=o,e=o(u),t=o(l),r=o(c),n=e===t?0:.5/(t-e),i=t===r?0:.5/(r-t),s=t<e?-1:1,p}}function r9(){var e=e4(r4());return e.copy=function(){return r6(e,r9()).exponent(e.exponent())},v.apply(e,arguments)}function r7(){return r9.apply(null,arguments).exponent(.5)}l=(u=function(e){var t=e.dateTime,r=e.date,n=e.time,i=e.periods,a=e.days,o=e.shortDays,u=e.months,l=e.shortMonths,c=tX(i),s=tG(i),f=tX(a),d=tG(a),p=tX(o),h=tG(o),v=tX(u),y=tG(u),g=tX(l),m=tG(l),b={a:function(e){return o[e.getDay()]},A:function(e){return a[e.getDay()]},b:function(e){return l[e.getMonth()]},B:function(e){return u[e.getMonth()]},c:null,d:rl,e:rl,f:rp,g:rP,G:rM,H:rc,I:rs,j:rf,L:rd,m:rh,M:rv,p:function(e){return i[+(e.getHours()>=12)]},q:function(e){return 1+~~(e.getMonth()/3)},Q:rq,s:rX,S:ry,u:rg,U:rm,V:rx,w:rw,W:rO,x:null,X:null,y:rj,Y:rk,Z:rS,"%":rH},x={a:function(e){return o[e.getUTCDay()]},A:function(e){return a[e.getUTCDay()]},b:function(e){return l[e.getUTCMonth()]},B:function(e){return u[e.getUTCMonth()]},c:null,d:rA,e:rA,f:rN,g:rU,G:r$,H:rE,I:rC,j:rT,L:rD,m:r_,M:rI,p:function(e){return i[+(e.getUTCHours()>=12)]},q:function(e){return 1+~~(e.getUTCMonth()/3)},Q:rq,s:rX,S:rR,u:rL,U:rz,V:rW,w:rB,W:rK,x:null,X:null,y:rZ,Y:rV,Z:rY,"%":rH},w={a:function(e,t,r){var n=p.exec(t.slice(r));return n?(e.w=h.get(n[0].toLowerCase()),r+n[0].length):-1},A:function(e,t,r){var n=f.exec(t.slice(r));return n?(e.w=d.get(n[0].toLowerCase()),r+n[0].length):-1},b:function(e,t,r){var n=g.exec(t.slice(r));return n?(e.m=m.get(n[0].toLowerCase()),r+n[0].length):-1},B:function(e,t,r){var n=v.exec(t.slice(r));return n?(e.m=y.get(n[0].toLowerCase()),r+n[0].length):-1},c:function(e,r,n){return P(e,t,r,n)},d:t7,e:t7,f:ri,g:t5,G:t6,H:re,I:re,j:t8,L:rn,m:t9,M:rt,p:function(e,t,r){var n=c.exec(t.slice(r));return n?(e.p=s.get(n[0].toLowerCase()),r+n[0].length):-1},q:t4,Q:ro,s:ru,S:rr,u:tJ,U:t0,V:t1,w:tQ,W:t2,x:function(e,t,n){return P(e,r,t,n)},X:function(e,t,r){return P(e,n,t,r)},y:t5,Y:t6,Z:t3,"%":ra};function O(e,t){return function(r){var n,i,a,o=[],u=-1,l=0,c=e.length;for(r instanceof Date||(r=new Date(+r));++u<c;)37===e.charCodeAt(u)&&(o.push(e.slice(l,u)),null!=(i=tU[n=e.charAt(++u)])?n=e.charAt(++u):i="e"===n?" ":"0",(a=t[n])&&(n=a(r,i)),o.push(n),l=u+1);return o.push(e.slice(l,u)),o.join("")}}function j(e,t){return function(r){var n,i,a=tZ(1900,void 0,1);if(P(a,e,r+="",0)!=r.length)return null;if("Q"in a)return new Date(a.Q);if("s"in a)return new Date(1e3*a.s+("L"in a?a.L:0));if(!t||"Z"in a||(a.Z=0),"p"in a&&(a.H=a.H%12+12*a.p),void 0===a.m&&(a.m="q"in a?a.q:0),"V"in a){if(a.V<1||a.V>53)return null;"w"in a||(a.w=1),"Z"in a?(n=(i=(n=tK(tZ(a.y,0,1))).getUTCDay())>4||0===i?tM.ceil(n):tM(n),n=th.offset(n,(a.V-1)*7),a.y=n.getUTCFullYear(),a.m=n.getUTCMonth(),a.d=n.getUTCDate()+(a.w+6)%7):(n=(i=(n=tB(tZ(a.y,0,1))).getDay())>4||0===i?tm.ceil(n):tm(n),n=tp.offset(n,(a.V-1)*7),a.y=n.getFullYear(),a.m=n.getMonth(),a.d=n.getDate()+(a.w+6)%7)}else("W"in a||"U"in a)&&("w"in a||(a.w="u"in a?a.u%7:"W"in a?1:0),i="Z"in a?tK(tZ(a.y,0,1)).getUTCDay():tB(tZ(a.y,0,1)).getDay(),a.m=0,a.d="W"in a?(a.w+6)%7+7*a.W-(i+5)%7:a.w+7*a.U-(i+6)%7);return"Z"in a?(a.H+=a.Z/100|0,a.M+=a.Z%100,tK(a)):tB(a)}}function P(e,t,r,n){for(var i,a,o=0,u=t.length,l=r.length;o<u;){if(n>=l)return -1;if(37===(i=t.charCodeAt(o++))){if(!(a=w[(i=t.charAt(o++))in tU?t.charAt(o++):i])||(n=a(e,r,n))<0)return -1}else if(i!=r.charCodeAt(n++))return -1}return n}return b.x=O(r,b),b.X=O(n,b),b.c=O(t,b),x.x=O(r,x),x.X=O(n,x),x.c=O(t,x),{format:function(e){var t=O(e+="",b);return t.toString=function(){return e},t},parse:function(e){var t=j(e+="",!1);return t.toString=function(){return e},t},utcFormat:function(e){var t=O(e+="",x);return t.toString=function(){return e},t},utcParse:function(e){var t=j(e+="",!0);return t.toString=function(){return e},t}}}({dateTime:"%x, %X",date:"%-m/%-d/%Y",time:"%-I:%M:%S %p",periods:["AM","PM"],days:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],shortDays:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],months:["January","February","March","April","May","June","July","August","September","October","November","December"],shortMonths:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"]})).format,u.parse,c=u.utcFormat,u.utcParse;var r8=r(35953),ne=r(49037),nt=r(22932),nr=r(16630),nn=r(66395);function ni(e){if(Array.isArray(e)&&2===e.length){var[t,r]=e;if((0,nn.n)(t)&&(0,nn.n)(r))return!0}return!1}function na(e,t,r){return r?e:[Math.min(e[0],t[0]),Math.max(e[1],t[1])]}var no=r(61134),nu=r.n(no),nl=e=>e,nc={},ns=e=>e===nc,nf=e=>function t(){return 0==arguments.length||1==arguments.length&&ns(arguments.length<=0?void 0:arguments[0])?t:e(...arguments)},nd=(e,t)=>1===e?t:nf(function(){for(var r=arguments.length,n=Array(r),i=0;i<r;i++)n[i]=arguments[i];var a=n.filter(e=>e!==nc).length;return a>=e?t(...n):nd(e-a,nf(function(){for(var e=arguments.length,r=Array(e),i=0;i<e;i++)r[i]=arguments[i];return t(...n.map(e=>ns(e)?r.shift():e),...r)}))}),np=e=>nd(e.length,e),nh=(e,t)=>{for(var r=[],n=e;n<t;++n)r[n-e]=n;return r},nv=np((e,t)=>Array.isArray(t)?t.map(e):Object.keys(t).map(e=>t[e]).map(e)),ny=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];if(!t.length)return nl;var n=t.reverse(),i=n[0],a=n.slice(1);return function(){return a.reduce((e,t)=>t(e),i(...arguments))}},ng=e=>Array.isArray(e)?e.reverse():e.split("").reverse().join(""),nm=e=>{var t=null,r=null;return function(){for(var n=arguments.length,i=Array(n),a=0;a<n;a++)i[a]=arguments[a];return t&&i.every((e,r)=>{var n;return e===(null===(n=t)||void 0===n?void 0:n[r])})?r:(t=i,r=e(...i))}};function nb(e){return 0===e?1:Math.floor(new(nu())(e).abs().log(10).toNumber())+1}function nx(e,t,r){for(var n=new(nu())(e),i=0,a=[];n.lt(t)&&i<1e5;)a.push(n.toNumber()),n=n.add(r),i++;return a}np((e,t,r)=>{var n=+e;return n+r*(+t-n)}),np((e,t,r)=>{var n=t-+e;return(r-e)/(n=n||1/0)}),np((e,t,r)=>{var n=t-+e;return Math.max(0,Math.min(1,(r-e)/(n=n||1/0)))});var nw=e=>{var[t,r]=e,[n,i]=[t,r];return t>r&&([n,i]=[r,t]),[n,i]},nO=(e,t,r)=>{if(e.lte(0))return new(nu())(0);var n=nb(e.toNumber()),i=new(nu())(10).pow(n),a=e.div(i),o=1!==n?.05:.1,u=new(nu())(Math.ceil(a.div(o).toNumber())).add(r).mul(o).mul(i);return new(nu())(t?u.toNumber():Math.ceil(u.toNumber()))},nj=(e,t,r)=>{var n=new(nu())(1),i=new(nu())(e);if(!i.isint()&&r){var a=Math.abs(e);a<1?(n=new(nu())(10).pow(nb(e)-1),i=new(nu())(Math.floor(i.div(n).toNumber())).mul(n)):a>1&&(i=new(nu())(Math.floor(e)))}else 0===e?i=new(nu())(Math.floor((t-1)/2)):r||(i=new(nu())(Math.floor(e)));var o=Math.floor((t-1)/2);return ny(nv(e=>i.add(new(nu())(e-o).mul(n)).toNumber()),nh)(0,t)},nP=function(e,t,r,n){var i,a=arguments.length>4&&void 0!==arguments[4]?arguments[4]:0;if(!Number.isFinite((t-e)/(r-1)))return{step:new(nu())(0),tickMin:new(nu())(0),tickMax:new(nu())(0)};var o=nO(new(nu())(t).sub(e).div(r-1),n,a),u=Math.ceil((i=e<=0&&t>=0?new(nu())(0):(i=new(nu())(e).add(t).div(2)).sub(new(nu())(i).mod(o))).sub(e).div(o).toNumber()),l=Math.ceil(new(nu())(t).sub(i).div(o).toNumber()),c=u+l+1;return c>r?nP(e,t,r,n,a+1):(c<r&&(l=t>0?l+(r-c):l,u=t>0?u:u+(r-c)),{step:o,tickMin:i.sub(new(nu())(u).mul(o)),tickMax:i.add(new(nu())(l).mul(o))})},nk=nm(function(e){var[t,r]=e,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:6,i=!(arguments.length>2)||void 0===arguments[2]||arguments[2],a=Math.max(n,2),[o,u]=nw([t,r]);if(o===-1/0||u===1/0){var l=u===1/0?[o,...nh(0,n-1).map(()=>1/0)]:[...nh(0,n-1).map(()=>-1/0),u];return t>r?ng(l):l}if(o===u)return nj(o,n,i);var{step:c,tickMin:s,tickMax:f}=nP(o,u,a,i,0),d=nx(s,f.add(new(nu())(.1).mul(c)),c);return t>r?ng(d):d}),nM=nm(function(e,t){var[r,n]=e,i=!(arguments.length>2)||void 0===arguments[2]||arguments[2],[a,o]=nw([r,n]);if(a===-1/0||o===1/0)return[r,n];if(a===o)return[a];var u=nO(new(nu())(o).sub(a).div(Math.max(t,2)-1),i,0),l=[...nx(new(nu())(a),new(nu())(o).sub(new(nu())(.99).mul(u)),u),o];return r>n?ng(l):l}),nS=r(60152),nA=r(32498),nE=r(36289),nC=r(2431),nT=r(33968),nD=r(80796),nN=r(40304),n_=r(56462),nI=r(87367),nR=r(78487);function nL(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function nz(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?nL(Object(r),!0).forEach(function(t){var n,i;n=t,i=r[t],(n=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(n))in e?Object.defineProperty(e,n,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[n]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):nL(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var nF=[0,"auto"],nW={allowDataOverflow:!1,allowDecimals:!0,allowDuplicatedCategory:!0,angle:0,dataKey:void 0,domain:void 0,height:30,hide:!0,id:0,includeHidden:!1,interval:"preserveEnd",minTickGap:5,mirror:!1,name:void 0,orientation:"bottom",padding:{left:0,right:0},reversed:!1,scale:"auto",tick:!0,tickCount:5,tickFormatter:void 0,ticks:void 0,type:"category",unit:void 0},nB=(e,t)=>{var r=e.cartesianAxis.xAxis[t];return null==r?nW:r},nK={allowDataOverflow:!1,allowDecimals:!0,allowDuplicatedCategory:!0,angle:0,dataKey:void 0,domain:nF,hide:!0,id:0,includeHidden:!1,interval:"preserveEnd",minTickGap:5,mirror:!1,name:void 0,orientation:"left",padding:{top:0,bottom:0},reversed:!1,scale:"auto",tick:!0,tickCount:5,tickFormatter:void 0,ticks:void 0,type:"number",unit:void 0,width:nR.n9},nZ=(e,t)=>{var r=e.cartesianAxis.yAxis[t];return null==r?nK:r},nU={domain:[0,"auto"],includeHidden:!1,reversed:!1,allowDataOverflow:!1,allowDuplicatedCategory:!1,dataKey:void 0,id:0,name:"",range:[64,64],scale:"auto",type:"number",unit:""},nV=(e,t)=>{var r=e.cartesianAxis.zAxis[t];return null==r?nU:r},n$=(e,t,r)=>{switch(t){case"xAxis":return nB(e,r);case"yAxis":return nZ(e,r);case"zAxis":return nV(e,r);case"angleAxis":return(0,nD.dc)(e,r);case"radiusAxis":return(0,nD.Au)(e,r);default:throw Error("Unexpected axis type: ".concat(t))}},nY=(e,t,r)=>{switch(t){case"xAxis":return nB(e,r);case"yAxis":return nZ(e,r);case"angleAxis":return(0,nD.dc)(e,r);case"radiusAxis":return(0,nD.Au)(e,r);default:throw Error("Unexpected axis type: ".concat(t))}},nH=e=>e.graphicalItems.countOfBars>0;function nq(e,t){return r=>{switch(e){case"xAxis":return"xAxisId"in r&&r.xAxisId===t;case"yAxis":return"yAxisId"in r&&r.yAxisId===t;case"zAxis":return"zAxisId"in r&&r.zAxisId===t;case"angleAxis":return"angleAxisId"in r&&r.angleAxisId===t;case"radiusAxis":return"radiusAxisId"in r&&r.radiusAxisId===t;default:return!1}}}var nX=e=>e.graphicalItems.cartesianItems,nG=(0,f.P1)([nN.z,n_.l],nq),nQ=(e,t,r)=>e.filter(r).filter(e=>(null==t?void 0:t.includeHidden)===!0||!e.hide),nJ=(0,f.P1)([nX,n$,nG],nQ),n0=e=>e.filter(e=>void 0===e.stackId),n1=(0,f.P1)([nJ],n0),n2=e=>e.map(e=>e.data).filter(Boolean).flat(1),n6=(0,f.P1)([nJ],n2),n5=(e,t)=>{var{chartData:r=[],dataStartIndex:n,dataEndIndex:i}=t;return e.length>0?e:r.slice(n,i+1)},n3=(0,f.P1)([n6,nt.hA],n5),n4=(e,t,r)=>(null==t?void 0:t.dataKey)!=null?e.map(e=>({value:(0,ne.F$)(e,t.dataKey)})):r.length>0?r.map(e=>e.dataKey).flatMap(t=>e.map(e=>({value:(0,ne.F$)(e,t)}))):e.map(e=>({value:e})),n9=(0,f.P1)([n3,n$,nJ],n4);function n7(e,t){switch(e){case"xAxis":return"x"===t.direction;case"yAxis":return"y"===t.direction;default:return!1}}function n8(e){return e.filter(e=>(0,nr.P2)(e)||e instanceof Date).map(Number).filter(e=>!1===(0,nr.In)(e))}var ie=(e,t,r)=>Object.fromEntries(Object.entries(t.reduce((e,t)=>(null==t.stackId||(null==e[t.stackId]&&(e[t.stackId]=[]),e[t.stackId].push(t)),e),{})).map(t=>{var[n,i]=t,a=i.map(e=>e.dataKey);return[n,{stackedData:(0,ne.uX)(e,a,r),graphicalItems:i}]})),it=(0,f.P1)([n3,nJ,nT.Qw],ie),ir=(e,t,r)=>{var{dataStartIndex:n,dataEndIndex:i}=t;if("zAxis"!==r){var a=(0,ne.EB)(e,n,i);if(null==a||0!==a[0]||0!==a[1])return a}},ii=(0,f.P1)([it,nt.iP,nN.z],ir),ia=(e,t,r,n)=>r.length>0?e.flatMap(e=>r.flatMap(r=>{var i,a,o=null===(i=r.errorBars)||void 0===i?void 0:i.filter(e=>n7(n,e)),u=(0,ne.F$)(e,null!==(a=t.dataKey)&&void 0!==a?a:r.dataKey);return{value:u,errorDomain:!(!o||"number"!=typeof u||(0,nr.In)(u))&&o.length?n8(o.flatMap(t=>{var r,n,i=(0,ne.F$)(e,t.dataKey);if(Array.isArray(i)?[r,n]=i:r=n=i,(0,nn.n)(r)&&(0,nn.n)(n))return[u-r,u+n]})):[]}})).filter(Boolean):(null==t?void 0:t.dataKey)!=null?e.map(e=>({value:(0,ne.F$)(e,t.dataKey),errorDomain:[]})):e.map(e=>({value:e,errorDomain:[]})),io=(0,f.P1)(n3,n$,n1,nN.z,ia);function iu(e){var{value:t}=e;if((0,nr.P2)(t)||t instanceof Date)return t}var il=e=>{var t=n8(e.flatMap(e=>[e.value,e.errorDomain]).flat(1));if(0!==t.length)return[Math.min(...t),Math.max(...t)]},ic=(e,t,r)=>{var n=e.map(iu).filter(e=>null!=e);return r&&(null==t.dataKey||t.allowDuplicatedCategory&&(0,nr.bv)(n))?p()(0,e.length):t.allowDuplicatedCategory?n:Array.from(new Set(n))},is=e=>{var t;if(null==e||!("domain"in e))return nF;if(null!=e.domain)return e.domain;if(null!=e.ticks){if("number"===e.type){var r=n8(e.ticks);return[Math.min(...r),Math.max(...r)]}if("category"===e.type)return e.ticks.map(String)}return null!==(t=null==e?void 0:e.domain)&&void 0!==t?t:nF},id=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];var n=t.filter(Boolean);if(0!==n.length){var i=n.flat();return[Math.min(...i),Math.max(...i)]}},ip=e=>e.referenceElements.dots,ih=(e,t,r)=>e.filter(e=>"extendDomain"===e.ifOverflow).filter(e=>"xAxis"===t?e.xAxisId===r:e.yAxisId===r),iv=(0,f.P1)([ip,nN.z,n_.l],ih),iy=e=>e.referenceElements.areas,ig=(0,f.P1)([iy,nN.z,n_.l],ih),im=e=>e.referenceElements.lines,ib=(0,f.P1)([im,nN.z,n_.l],ih),ix=(e,t)=>{var r=n8(e.map(e=>"xAxis"===t?e.x:e.y));if(0!==r.length)return[Math.min(...r),Math.max(...r)]},iw=(0,f.P1)(iv,nN.z,ix),iO=(e,t)=>{var r=n8(e.flatMap(e=>["xAxis"===t?e.x1:e.y1,"xAxis"===t?e.x2:e.y2]));if(0!==r.length)return[Math.min(...r),Math.max(...r)]},ij=(0,f.P1)([ig,nN.z],iO),iP=(e,t)=>{var r=n8(e.map(e=>"xAxis"===t?e.x:e.y));if(0!==r.length)return[Math.min(...r),Math.max(...r)]},ik=(0,f.P1)(ib,nN.z,iP),iM=(0,f.P1)(iw,ik,ij,(e,t,r)=>id(e,r,t)),iS=(0,f.P1)([n$],is),iA=(e,t,r,n,i)=>{var a=function(e,t){if(t&&"function"!=typeof e&&Array.isArray(e)&&2===e.length){var r,n,[i,a]=e;if((0,nn.n)(i))r=i;else if("function"==typeof i)return;if((0,nn.n)(a))n=a;else if("function"==typeof a)return;var o=[r,n];if(ni(o))return o}}(t,e.allowDataOverflow);return null!=a?a:function(e,t,r){if(r||null!=t){if("function"==typeof e&&null!=t)try{var n=e(t,r);if(ni(n))return na(n,t,r)}catch(e){}if(Array.isArray(e)&&2===e.length){var i,a,[o,u]=e;if("auto"===o)null!=t&&(i=Math.min(...t));else if((0,nr.hj)(o))i=o;else if("function"==typeof o)try{null!=t&&(i=o(null==t?void 0:t[0]))}catch(e){}else if("string"==typeof o&&ne.rI.test(o)){var l=ne.rI.exec(o);if(null==l||null==t)i=void 0;else{var c=+l[1];i=t[0]-c}}else i=null==t?void 0:t[0];if("auto"===u)null!=t&&(a=Math.max(...t));else if((0,nr.hj)(u))a=u;else if("function"==typeof u)try{null!=t&&(a=u(null==t?void 0:t[1]))}catch(e){}else if("string"==typeof u&&ne.Ji.test(u)){var s=ne.Ji.exec(u);if(null==s||null==t)a=void 0;else{var f=+s[1];a=t[1]+f}}else a=null==t?void 0:t[1];var d=[i,a];if(ni(d))return null==t?d:na(d,t,r)}}}(t,id(r,i,il(n)),e.allowDataOverflow)},iE=(0,f.P1)([n$,iS,ii,io,iM],iA),iC=[0,1],iT=(e,t,r,n,i,a,o)=>{if(null!=e&&null!=r&&0!==r.length){var{dataKey:u,type:l}=e,c=(0,ne.NA)(t,a);return c&&null==u?p()(0,r.length):"category"===l?ic(n,e,c):"expand"===i?iC:o}},iD=(0,f.P1)([n$,r8.rE,n3,n9,nT.Qw,nN.z,iE],iT),iN=(e,t,r,n,i)=>{if(null!=e){var{scale:a,type:o}=e;if("auto"===a)return"radial"===t&&"radiusAxis"===i?"band":"radial"===t&&"angleAxis"===i?"linear":"category"===o&&n&&(n.indexOf("LineChart")>=0||n.indexOf("AreaChart")>=0||n.indexOf("ComposedChart")>=0&&!r)?"point":"category"===o?"band":"linear";if("string"==typeof a){var u="scale".concat((0,nr.jC)(a));return u in s?u:"point"}}},i_=(0,f.P1)([n$,r8.rE,nH,nT.E2,nN.z],iN);function iI(e,t,r,n){if(null!=r&&null!=n){if("function"==typeof e.scale)return e.scale.copy().domain(r).range(n);var i=function(e){if(null!=e){if(e in s)return s[e]();var t="scale".concat((0,nr.jC)(e));if(t in s)return s[t]()}}(t);if(null!=i){var a=i.domain(r).range(n);return(0,ne.zF)(a),a}}}var iR=(e,t,r)=>{var n=is(t);return"auto"!==r&&"linear"!==r?void 0:null!=t&&t.tickCount&&Array.isArray(n)&&("auto"===n[0]||"auto"===n[1])&&ni(e)?nk(e,t.tickCount,t.allowDecimals):null!=t&&t.tickCount&&"number"===t.type&&ni(e)?nM(e,t.tickCount,t.allowDecimals):void 0},iL=(0,f.P1)([iD,nY,i_],iR),iz=(e,t,r,n)=>"angleAxis"!==n&&(null==e?void 0:e.type)==="number"&&ni(t)&&Array.isArray(r)&&r.length>0?[Math.min(t[0],r[0]),Math.max(t[1],r[r.length-1])]:t,iF=(0,f.P1)([n$,iD,iL,nN.z],iz),iW=(0,f.P1)(n9,n$,(e,t)=>{if(t&&"number"===t.type){var r=1/0,n=Array.from(n8(e.map(e=>e.value))).sort((e,t)=>e-t);if(n.length<2)return 1/0;var i=n[n.length-1]-n[0];if(0===i)return 1/0;for(var a=0;a<n.length-1;a++)r=Math.min(r,n[a+1]-n[a]);return r/i}}),iB=(0,f.P1)(iW,r8.rE,nT.sd,nE.bX,(e,t,r,n)=>n,(e,t,r,n,i)=>{if(!(0,nn.n)(e))return 0;var a="vertical"===t?n.height:n.width;if("gap"===i)return e*a/2;if("no-gap"===i){var o=(0,nr.h1)(r,e*a),u=e*a/2;return u-o-(u-o)/a*o}return 0}),iK=(0,f.P1)(nB,(e,t)=>{var r=nB(e,t);return null==r||"string"!=typeof r.padding?0:iB(e,"xAxis",t,r.padding)},(e,t)=>{if(null==e)return{left:0,right:0};var r,n,{padding:i}=e;return"string"==typeof i?{left:t,right:t}:{left:(null!==(r=i.left)&&void 0!==r?r:0)+t,right:(null!==(n=i.right)&&void 0!==n?n:0)+t}}),iZ=(0,f.P1)(nZ,(e,t)=>{var r=nZ(e,t);return null==r||"string"!=typeof r.padding?0:iB(e,"yAxis",t,r.padding)},(e,t)=>{if(null==e)return{top:0,bottom:0};var r,n,{padding:i}=e;return"string"==typeof i?{top:t,bottom:t}:{top:(null!==(r=i.top)&&void 0!==r?r:0)+t,bottom:(null!==(n=i.bottom)&&void 0!==n?n:0)+t}}),iU=(0,f.P1)([nE.bX,iK,nC.V,nC.F,(e,t,r)=>r],(e,t,r,n,i)=>{var{padding:a}=n;return i?[a.left,r.width-a.right]:[e.left+t.left,e.left+e.width-t.right]}),iV=(0,f.P1)([nE.bX,r8.rE,iZ,nC.V,nC.F,(e,t,r)=>r],(e,t,r,n,i,a)=>{var{padding:o}=i;return a?[n.height-o.bottom,o.top]:"horizontal"===t?[e.top+e.height-r.bottom,e.top+r.top]:[e.top+r.top,e.top+e.height-r.bottom]}),i$=(e,t,r,n)=>{var i;switch(t){case"xAxis":return iU(e,r,n);case"yAxis":return iV(e,r,n);case"zAxis":return null===(i=nV(e,r))||void 0===i?void 0:i.range;case"angleAxis":return(0,nD.bH)(e);case"radiusAxis":return(0,nD.s9)(e,r);default:return}},iY=(0,f.P1)([n$,i$],nI.$),iH=(0,f.P1)([n$,i_,iF,iY],iI);function iq(e,t){return e.id<t.id?-1:e.id>t.id?1:0}(0,f.P1)(nJ,nN.z,(e,t)=>e.flatMap(e=>{var t;return null!==(t=e.errorBars)&&void 0!==t?t:[]}).filter(e=>n7(t,e)));var iX=(e,t)=>t,iG=(e,t,r)=>r,iQ=(0,f.P1)(nA.X,iX,iG,(e,t,r)=>e.filter(e=>e.orientation===t).filter(e=>e.mirror===r).sort(iq)),iJ=(0,f.P1)(nA.Z,iX,iG,(e,t,r)=>e.filter(e=>e.orientation===t).filter(e=>e.mirror===r).sort(iq)),i0=(e,t)=>({width:e.width,height:t.height}),i1=(e,t)=>({width:"number"==typeof t.width?t.width:nR.n9,height:e.height}),i2=(0,f.P1)(nE.bX,nB,i0),i6=(e,t,r)=>{switch(t){case"top":return e.top;case"bottom":return r-e.bottom;default:return 0}},i5=(e,t,r)=>{switch(t){case"left":return e.left;case"right":return r-e.right;default:return 0}},i3=(0,f.P1)(nS.d_,nE.bX,iQ,iX,iG,(e,t,r,n,i)=>{var a,o={};return r.forEach(r=>{var u=i0(t,r);null==a&&(a=i6(t,n,e));var l="top"===n&&!i||"bottom"===n&&i;o[r.id]=a-Number(l)*u.height,a+=(l?-1:1)*u.height}),o}),i4=(0,f.P1)(nS.RD,nE.bX,iJ,iX,iG,(e,t,r,n,i)=>{var a,o={};return r.forEach(r=>{var u=i1(t,r);null==a&&(a=i5(t,n,e));var l="left"===n&&!i||"right"===n&&i;o[r.id]=a-Number(l)*u.width,a+=(l?-1:1)*u.width}),o}),i9=(e,t)=>{var r=(0,nE.bX)(e),n=nB(e,t);if(null!=n){var i=i3(e,n.orientation,n.mirror)[t];return null==i?{x:r.left,y:0}:{x:r.left,y:i}}},i7=(e,t)=>{var r=(0,nE.bX)(e),n=nZ(e,t);if(null!=n){var i=i4(e,n.orientation,n.mirror)[t];return null==i?{x:0,y:r.top}:{x:i,y:r.top}}},i8=(0,f.P1)(nE.bX,nZ,(e,t)=>({width:"number"==typeof t.width?t.width:nR.n9,height:e.height})),ae=(e,t,r)=>{switch(t){case"xAxis":return i2(e,r).width;case"yAxis":return i8(e,r).height;default:return}},at=(e,t,r,n)=>{if(null!=r){var{allowDuplicatedCategory:i,type:a,dataKey:o}=r,u=(0,ne.NA)(e,n),l=t.map(e=>e.value);if(o&&u&&"category"===a&&i&&(0,nr.bv)(l))return l}},ar=(0,f.P1)([r8.rE,n9,n$,nN.z],at),an=(e,t,r,n)=>{if(null!=r&&null!=r.dataKey){var{type:i,scale:a}=r;if((0,ne.NA)(e,n)&&("number"===i||"auto"!==a))return t.map(e=>e.value)}},ai=(0,f.P1)([r8.rE,n9,nY,nN.z],an),aa=(0,f.P1)([r8.rE,(e,t,r)=>{switch(t){case"xAxis":return nB(e,r);case"yAxis":return nZ(e,r);default:throw Error("Unexpected axis type: ".concat(t))}},i_,iH,ar,ai,i$,iL,nN.z],(e,t,r,n,i,a,o,u,l)=>{if(null==t)return null;var c=(0,ne.NA)(e,l);return{angle:t.angle,interval:t.interval,minTickGap:t.minTickGap,orientation:t.orientation,tick:t.tick,tickCount:t.tickCount,tickFormatter:t.tickFormatter,ticks:t.ticks,type:t.type,unit:t.unit,axisType:l,categoricalDomain:a,duplicateDomain:i,isCategorical:c,niceTicks:u,range:o,realScaleType:r,scale:n}}),ao=(0,f.P1)([r8.rE,nY,i_,iH,iL,i$,ar,ai,nN.z],(e,t,r,n,i,a,o,u,l)=>{if(null!=t&&null!=n){var c=(0,ne.NA)(e,l),{type:s,ticks:f,tickCount:d}=t,p="scaleBand"===r&&"function"==typeof n.bandwidth?n.bandwidth()/2:2,h="category"===s&&n.bandwidth?n.bandwidth()/p:0;h="angleAxis"===l&&null!=a&&a.length>=2?2*(0,nr.uY)(a[0]-a[1])*h:h;var v=f||i;return v?v.map((e,t)=>({index:t,coordinate:n(o?o.indexOf(e):e)+h,value:e,offset:h})).filter(e=>!(0,nr.In)(e.coordinate)):c&&u?u.map((e,t)=>({coordinate:n(e)+h,value:e,index:t,offset:h})):n.ticks?n.ticks(d).map(e=>({coordinate:n(e)+h,value:e,offset:h})):n.domain().map((e,t)=>({coordinate:n(e)+h,value:o?o[e]:e,index:t,offset:h}))}}),au=(0,f.P1)([r8.rE,nY,iH,i$,ar,ai,nN.z],(e,t,r,n,i,a,o)=>{if(null!=t&&null!=r&&null!=n&&n[0]!==n[1]){var u=(0,ne.NA)(e,o),{tickCount:l}=t,c=0;return(c="angleAxis"===o&&(null==n?void 0:n.length)>=2?2*(0,nr.uY)(n[0]-n[1])*c:c,u&&a)?a.map((e,t)=>({coordinate:r(e)+c,value:e,index:t,offset:c})):r.ticks?r.ticks(l).map(e=>({coordinate:r(e)+c,value:e,offset:c})):r.domain().map((e,t)=>({coordinate:r(e)+c,value:i?i[e]:e,index:t,offset:c}))}}),al=(0,f.P1)(n$,iH,(e,t)=>{if(null!=e&&null!=t)return nz(nz({},e),{},{scale:t})}),ac=(0,f.P1)([n$,i_,iD,iY],iI);(0,f.P1)((e,t,r)=>nV(e,r),ac,(e,t)=>{if(null!=e&&null!=t)return nz(nz({},e),{},{scale:t})});var as=(0,f.P1)([r8.rE,nA.X,nA.Z],(e,t,r)=>{switch(e){case"horizontal":return t.some(e=>e.reversed)?"right-to-left":"left-to-right";case"vertical":return r.some(e=>e.reversed)?"bottom-to-top":"top-to-bottom";case"centric":case"radial":return"left-to-right";default:return}})},2431:function(e,t,r){"use strict";r.d(t,{F:function(){return u},V:function(){return l}});var n=r(92713),i=r(36289),a=r(60152),o=r(16630),u=e=>e.brush,l=(0,n.P1)([u,i.bX,a.lr],(e,t,r)=>({height:e.height,x:(0,o.hj)(e.x)?e.x:t.left,y:(0,o.hj)(e.y)?e.y:t.top+t.height+t.brushBottom-((null==r?void 0:r.bottom)||0),width:(0,o.hj)(e.width)?e.width:t.width}))},3838:function(e,t,r){"use strict";r.d(t,{b:function(){return i}});var n=r(16630),i=(e,t)=>{var r,i=Number(t);if(!(0,n.In)(i)&&null!=t)return i>=0?null==e||null===(r=e[i])||void 0===r?void 0:r.value:void 0}},6064:function(e,t,r){"use strict";r.d(t,{p:function(){return i}});var n=r(66395),i=(e,t)=>{var r=null==e?void 0:e.index;if(null==r)return null;var i=Number(r);if(!(0,n.n)(i))return r;var a=Infinity;return t.length>0&&(a=t.length-1),String(Math.max(0,Math.min(i,a)))}},87367:function(e,t,r){"use strict";r.d(t,{$:function(){return n}});var n=(e,t)=>e&&t?null!=e&&e.reversed?[t[1],t[0]]:t:void 0},8656:function(e,t,r){"use strict";r.d(t,{$:function(){return n}});var n=(e,t,r,n,i,a,o,u)=>{if(null!=a&&null!=u){var l=o[0],c=null==l?void 0:u(l.positions,a);if(null!=c)return c;var s=null==i?void 0:i[Number(a)];if(s)return"horizontal"===r?{x:s.coordinate,y:(n.top+t)/2}:{x:(n.left+e)/2,y:s.coordinate}}}},24597:function(e,t,r){"use strict";r.d(t,{k:function(){return o}});var n=r(64725);function i(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function a(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?i(Object(r),!0).forEach(function(t){var n,i;n=t,i=r[t],(n=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(n))in e?Object.defineProperty(e,n,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[n]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):i(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var o=(e,t,r,i)=>{if(null==t)return n.UW;var o="axis"===t?"click"===r?e.axisInteraction.click:e.axisInteraction.hover:"click"===r?e.itemInteraction.click:e.itemInteraction.hover;if(null==o)return n.UW;if(o.active)return o;if(e.keyboardInteraction.active)return e.keyboardInteraction;if(e.syncInteraction.active&&null!=e.syncInteraction.index)return e.syncInteraction;var u=!0===e.settings.active;if(null!=o.index){if(u)return a(a({},o),{},{active:!0})}else if(null!=i)return{active:!0,coordinate:void 0,dataKey:void 0,index:i};return a(a({},n.UW),{},{coordinate:o.coordinate})}},89667:function(e,t,r){"use strict";r.d(t,{k:function(){return n}});var n=(e,t,r,n)=>{var i;return"axis"===t?e.tooltipItemPayloads:0===e.tooltipItemPayloads.length?[]:null==(i="hover"===r?e.itemInteraction.hover.dataKey:e.itemInteraction.click.dataKey)&&null!=n?[e.tooltipItemPayloads[0]]:e.tooltipItemPayloads.filter(e=>{var t;return(null===(t=e.settings)||void 0===t?void 0:t.dataKey)===i})}},60152:function(e,t,r){"use strict";r.d(t,{K$:function(){return a},RD:function(){return n},d_:function(){return i},lr:function(){return o}});var n=e=>e.layout.width,i=e=>e.layout.height,a=e=>e.layout.scale,o=e=>e.layout.margin},22932:function(e,t,r){"use strict";r.d(t,{RV:function(){return a},hA:function(){return o},iP:function(){return i}});var n=r(92713),i=e=>e.chartData,a=(0,n.P1)([i],e=>{var t=null!=e.chartData?e.chartData.length-1:0;return{chartData:e.chartData,computedData:e.computedData,dataEndIndex:t,dataStartIndex:0}}),o=(e,t,r,n)=>n?a(e):i(e)},56462:function(e,t,r){"use strict";r.d(t,{l:function(){return n}});var n=(e,t,r)=>r},40304:function(e,t,r){"use strict";r.d(t,{z:function(){return n}});var n=(e,t)=>t},80796:function(e,t,r){"use strict";r.d(t,{dc:function(){return y},bH:function(){return O},HW:function(){return P},Au:function(){return g},s9:function(){return j}});var n=r(92713),i=r(60152),a=r(36289),o=r(39206),u=r(16630),l="auto",c="auto",s=r(87367),f=r(35953),d={allowDataOverflow:!1,allowDecimals:!1,allowDuplicatedCategory:!1,dataKey:void 0,domain:void 0,id:0,includeHidden:!1,name:void 0,reversed:!1,scale:l,tick:!0,tickCount:void 0,ticks:void 0,type:"category",unit:void 0},p={allowDataOverflow:!1,allowDecimals:!1,allowDuplicatedCategory:!0,dataKey:void 0,domain:void 0,id:0,includeHidden:!1,name:void 0,reversed:!1,scale:c,tick:!0,tickCount:5,ticks:void 0,type:"number",unit:void 0},h={allowDataOverflow:!1,allowDecimals:!1,allowDuplicatedCategory:!0,dataKey:void 0,domain:void 0,id:0,includeHidden:!1,name:void 0,reversed:!1,scale:l,tick:!0,tickCount:void 0,ticks:void 0,type:"number",unit:void 0},v={allowDataOverflow:!1,allowDecimals:!1,allowDuplicatedCategory:!0,dataKey:void 0,domain:void 0,id:0,includeHidden:!1,name:void 0,reversed:!1,scale:c,tick:!0,tickCount:5,ticks:void 0,type:"category",unit:void 0},y=(e,t)=>null!=e.polarAxis.angleAxis[t]?e.polarAxis.angleAxis[t]:"radial"===e.layout.layoutType?h:d,g=(e,t)=>null!=e.polarAxis.radiusAxis[t]?e.polarAxis.radiusAxis[t]:"radial"===e.layout.layoutType?v:p,m=e=>e.polarOptions,b=(0,n.P1)([i.RD,i.d_,a.bX],o.$4),x=(0,n.P1)([m,b],(e,t)=>{if(null!=e)return(0,u.h1)(e.innerRadius,t,0)}),w=(0,n.P1)([m,b],(e,t)=>{if(null!=e)return(0,u.h1)(e.outerRadius,t,.8*t)}),O=(0,n.P1)([m],e=>{if(null==e)return[0,0];var{startAngle:t,endAngle:r}=e;return[t,r]});(0,n.P1)([y,O],s.$);var j=(0,n.P1)([b,x,w],(e,t,r)=>{if(null!=e&&null!=t&&null!=r)return[t,r]});(0,n.P1)([g,j],s.$);var P=(0,n.P1)([f.rE,m,x,w,i.RD,i.d_],(e,t,r,n,i,a)=>{if(("centric"===e||"radial"===e)&&null!=t&&null!=r&&null!=n){var{cx:o,cy:l,startAngle:c,endAngle:s}=t;return{cx:(0,u.h1)(o,i,i/2),cy:(0,u.h1)(l,a,a/2),innerRadius:r,outerRadius:n,startAngle:c,endAngle:s,clockWise:!1}}})},33968:function(e,t,r){"use strict";r.d(t,{E2:function(){return l},Qw:function(){return u},Sg:function(){return f},X8:function(){return o},Yd:function(){return c},b2:function(){return s},qy:function(){return n},sd:function(){return a},wK:function(){return i}});var n=e=>e.rootProps.maxBarSize,i=e=>e.rootProps.barGap,a=e=>e.rootProps.barCategoryGap,o=e=>e.rootProps.barSize,u=e=>e.rootProps.stackOffset,l=e=>e.options.chartName,c=e=>e.rootProps.syncId,s=e=>e.rootProps.syncMethod,f=e=>e.options.eventEmitter},63330:function(e,t,r){"use strict";r.d(t,{h:function(){return c}});var n=r(92713),i=r(35953),a=r(31944),o=r(36289),u=r(84461),l=r(80796),c=(0,n.P1)([(e,t)=>t,i.rE,l.HW,a.cS,a.PG,a.WQ,u.EM,o.bX],u.Nb)},32498:function(e,t,r){"use strict";r.d(t,{X:function(){return i},Z:function(){return a}});var n=r(92713),i=(0,n.P1)(e=>e.cartesianAxis.xAxis,e=>Object.values(e)),a=(0,n.P1)(e=>e.cartesianAxis.yAxis,e=>Object.values(e))},36289:function(e,t,r){"use strict";r.d(t,{zM:function(){return g},bX:function(){return v},nd:function(){return y}});var n=r(92713),i=r(15870),a=r.n(i),o=r(31104),u=r.n(o),l=e=>e.legend.settings;(0,n.P1)([e=>e.legend.payload,l],(e,t)=>{var{itemSorter:r}=t,n=e.flat(1);return r?u()(n,r):n});var c=r(49037),s=r(60152),f=r(32498),d=r(78487);function p(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function h(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?p(Object(r),!0).forEach(function(t){var n,i;n=t,i=r[t],(n=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(n))in e?Object.defineProperty(e,n,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[n]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):p(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var v=(0,n.P1)([s.RD,s.d_,s.lr,e=>e.brush.height,f.X,f.Z,l,e=>e.legend.size],(e,t,r,n,i,o,u,l)=>{var s=o.reduce((e,t)=>{var{orientation:r}=t;if(!t.mirror&&!t.hide){var n="number"==typeof t.width?t.width:d.n9;return h(h({},e),{},{[r]:e[r]+n})}return e},{left:r.left||0,right:r.right||0}),f=i.reduce((e,t)=>{var{orientation:r}=t;return t.mirror||t.hide?e:h(h({},e),{},{[r]:a()(e,"".concat(r))+t.height})},{top:r.top||0,bottom:r.bottom||0}),p=h(h({},f),s),v=p.bottom;p.bottom+=n;var y=e-(p=(0,c.By)(p,u,l)).left-p.right,g=t-p.top-p.bottom;return h(h({brushBottom:v},p),{},{width:Math.max(y,0),height:Math.max(g,0)})}),y=(0,n.P1)(v,e=>({x:e.left,y:e.top,width:e.width,height:e.height})),g=(0,n.P1)(s.RD,s.d_,(e,t)=>({x:0,y:0,width:e,height:t}))},46302:function(e,t,r){"use strict";r.d(t,{Nb:function(){return a},SB:function(){return u},Y4:function(){return l},_j:function(){return o},iS:function(){return i}});var n=r(39040),i=e=>e.options.defaultTooltipEventType,a=e=>e.options.validateTooltipEventTypes;function o(e,t,r){if(null==e)return t;var n=e?"axis":"item";return null==r?t:r.includes(n)?n:t}function u(e,t){return o(t,i(e),a(e))}function l(e){return(0,n.C)(t=>u(t,e))}},73535:function(e,t,r){"use strict";r.d(t,{h:function(){return n}});var n=e=>e.options.tooltipPayloadSearcher},54803:function(e,t,r){"use strict";r.d(t,{M:function(){return n}});var n=e=>e.tooltip},84461:function(e,t,r){"use strict";r.d(t,{AC:function(){return P},EM:function(){return A},Nb:function(){return z},TT:function(){return R},ck:function(){return _},hA:function(){return N},i:function(){return I},oo:function(){return L},wi:function(){return T}});var n=r(92713),i=r(31104),a=r.n(i),o=r(39040),u=r(49037),l=r(16630),c=r(22932),s=r(31944),f=r(33968),d=r(35953),p=r(36289),h=r(60152),v=r(3838),y=r(24597),g=r(6064),m=r(8656),b=r(89667),x=r(73535),w=r(54803);function O(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function j(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?O(Object(r),!0).forEach(function(t){var n,i;n=t,i=r[t],(n=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(n))in e?Object.defineProperty(e,n,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[n]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):O(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var P=()=>(0,o.C)(f.E2),k=(e,t)=>t,M=(e,t,r)=>r,S=(e,t,r,n)=>n,A=(0,n.P1)(s.WQ,e=>a()(e,e=>e.coordinate)),E=(0,n.P1)([w.M,k,M,S],y.k),C=(0,n.P1)([E,s.wQ],g.p),T=(e,t,r)=>{if(null!=t){var n=(0,w.M)(e);return"axis"===t?"hover"===r?n.axisInteraction.hover.dataKey:n.axisInteraction.click.dataKey:"hover"===r?n.itemInteraction.hover.dataKey:n.itemInteraction.click.dataKey}},D=(0,n.P1)([w.M,k,M,S],b.k),N=(0,n.P1)([h.RD,h.d_,d.rE,p.bX,s.WQ,S,D,x.h],m.$),_=(0,n.P1)([E,N],(e,t)=>{var r;return null!==(r=e.coordinate)&&void 0!==r?r:t}),I=(0,n.P1)(s.WQ,C,v.b),R=(0,n.P1)([D,C,c.iP,s.zv,I,x.h,k],(e,t,r,n,i,a,o)=>{if(null!=t&&null!=a){var{chartData:c,computedData:s,dataStartIndex:f,dataEndIndex:d}=r;return e.reduce((e,r)=>{var p,h,v,y,{dataDefinedOnItem:g,settings:m}=r,b=Array.isArray(p=null!=g?g:c)&&p&&f+d!==0?p.slice(f,d+1):p,x=null!==(h=null==m?void 0:m.dataKey)&&void 0!==h?h:null==n?void 0:n.dataKey,w=null==m?void 0:m.nameKey;return Array.isArray(v=null!=n&&n.dataKey&&!(null!=n&&n.allowDuplicatedCategory)&&Array.isArray(b)&&"axis"===o?(0,l.Ap)(b,n.dataKey,i):a(b,t,s,w))?v.forEach(t=>{var r=j(j({},m),{},{name:t.name,unit:t.unit,color:void 0,fill:void 0});e.push((0,u.eV)({tooltipEntrySettings:r,dataKey:t.dataKey,payload:t.payload,value:(0,u.F$)(t.payload,t.dataKey),name:t.name}))}):e.push((0,u.eV)({tooltipEntrySettings:m,dataKey:x,payload:v,value:(0,u.F$)(v,x),name:null!==(y=(0,u.F$)(v,w))&&void 0!==y?y:null==m?void 0:m.name})),e},[])}}),L=(0,n.P1)([E],e=>({isActive:e.active,activeIndex:e.index})),z=(e,t,r,n,i,a,o,l)=>{if(e&&t&&n&&i&&a){var c=(0,u.Z2)(e.chartX,e.chartY,t,r,l);if(c){var s=(0,u.HZ)(c,t),f=(0,u.VO)(s,o,a,n,i),d=(0,u.ep)(t,a,f,c);return{activeIndex:String(f),activeCoordinate:d}}}}},31944:function(e,t,r){"use strict";r.d(t,{i:function(){return ei},pI:function(){return el},du:function(){return ea},Ve:function(){return en},oo:function(){return ec},zv:function(){return j},PG:function(){return H},ri:function(){return q},WQ:function(){return Q},cS:function(){return w},wQ:function(){return E}});var n=r(92713),i=r(98628),a=r(35953),o=r(49037),u=r(22932),l=r(33968),c=r(16630),s=r(87367),f=r(46302),d=r(3838),p=r(24597),h=r(6064),v=r(8656),y=r(60152),g=r(36289),m=r(89667),b=r(73535),x=r(54803),w=e=>{var t=(0,a.rE)(e);return"horizontal"===t?"xAxis":"vertical"===t?"yAxis":"centric"===t?"angleAxis":"radiusAxis"},O=e=>e.tooltip.settings.axisId,j=e=>{var t=w(e),r=O(e);return(0,i.ub)(e,t,r)},P=(0,n.P1)([j,a.rE,i.Q4,l.E2,w],i.Yf),k=(0,n.P1)([e=>e.graphicalItems.cartesianItems,e=>e.graphicalItems.polarItems],(e,t)=>[...e,...t]),M=(0,n.P1)([w,O],i.YZ),S=(0,n.P1)([k,j,M],i.$B),A=(0,n.P1)([S],i.bU),E=(0,n.P1)([A,u.iP],i.tZ),C=(0,n.P1)([E,j,S],i.UA),T=(0,n.P1)([j],i.c4),D=(0,n.P1)([E,S,l.Qw],i.CK),N=(0,n.P1)([D,u.iP,w],i.dz),_=(0,n.P1)([S],i.Tk),I=(0,n.P1)([E,j,_,w],i.VQ),R=(0,n.P1)([i.RA,w,O],i.zW),L=(0,n.P1)([R,w],i.zX),z=(0,n.P1)([i.ww,w,O],i.zW),F=(0,n.P1)([z,w],i.yT),W=(0,n.P1)([i.xz,w,O],i.zW),B=(0,n.P1)([W,w],i.fY),K=(0,n.P1)([L,B,F],i.Eu),Z=(0,n.P1)([j,T,N,I,K],i.E8),U=(0,n.P1)([j,a.rE,E,C,l.Qw,w,Z],i.l_),V=(0,n.P1)([U,j,P],i.vb),$=(0,n.P1)([j,U,V,w],i.kO),Y=e=>{var t=w(e),r=O(e);return(0,i.Fn)(e,t,r,!1)},H=(0,n.P1)([j,Y],s.$),q=(0,n.P1)([j,P,$,H],i.Jw),X=(0,n.P1)([a.rE,C,j,w],i.rC),G=(0,n.P1)([a.rE,C,j,w],i.UC),Q=(0,n.P1)([a.rE,j,P,q,Y,X,G,w],(e,t,r,n,i,a,u,l)=>{if(t){var{type:s}=t,f=(0,o.NA)(e,l);if(n){var d="scaleBand"===r&&n.bandwidth?n.bandwidth()/2:2,p="category"===s&&n.bandwidth?n.bandwidth()/d:0;return(p="angleAxis"===l&&null!=i&&(null==i?void 0:i.length)>=2?2*(0,c.uY)(i[0]-i[1])*p:p,f&&u)?u.map((e,t)=>({coordinate:n(e)+p,value:e,index:t,offset:p})):n.domain().map((e,t)=>({coordinate:n(e)+p,value:a?a[e]:e,index:t,offset:p}))}}}),J=(0,n.P1)([f.iS,f.Nb,e=>e.tooltip.settings],(e,t,r)=>(0,f._j)(r.shared,e,t)),ee=e=>e.tooltip.settings.trigger,et=e=>e.tooltip.settings.defaultIndex,er=(0,n.P1)([x.M,J,ee,et],p.k),en=(0,n.P1)([er,E],h.p),ei=(0,n.P1)([Q,en],d.b),ea=(0,n.P1)([er],e=>{if(e)return e.dataKey}),eo=(0,n.P1)([x.M,J,ee,et],m.k),eu=(0,n.P1)([y.RD,y.d_,a.rE,g.bX,Q,et,eo,b.h],v.$),el=(0,n.P1)([er,eu],(e,t)=>null!=e&&e.coordinate?e.coordinate:t),ec=(0,n.P1)([er],e=>e.active)},64725:function(e,t,r){"use strict";r.d(t,{$A:function(){return y},KN:function(){return g},KO:function(){return u},Kw:function(){return m},M1:function(){return s},O_:function(){return p},PD:function(){return c},Rr:function(){return h},UW:function(){return a},Vg:function(){return f},cK:function(){return l},eB:function(){return v},ne:function(){return d}});var n=r(39129),i=r(10418),a={active:!1,index:null,dataKey:void 0,coordinate:void 0},o=(0,n.oM)({name:"tooltip",initialState:{itemInteraction:{click:a,hover:a},axisInteraction:{click:a,hover:a},keyboardInteraction:a,syncInteraction:{active:!1,index:null,dataKey:void 0,label:void 0,coordinate:void 0},tooltipItemPayloads:[],settings:{shared:void 0,trigger:"hover",axisId:0,active:!1,defaultIndex:void 0}},reducers:{addTooltipEntrySettings(e,t){e.tooltipItemPayloads.push((0,i.cA)(t.payload))},removeTooltipEntrySettings(e,t){var r=(0,i.Vk)(e).tooltipItemPayloads.indexOf((0,i.cA)(t.payload));r>-1&&e.tooltipItemPayloads.splice(r,1)},setTooltipSettingsState(e,t){e.settings=t.payload},setActiveMouseOverItemIndex(e,t){e.syncInteraction.active=!1,e.keyboardInteraction.active=!1,e.itemInteraction.hover.active=!0,e.itemInteraction.hover.index=t.payload.activeIndex,e.itemInteraction.hover.dataKey=t.payload.activeDataKey,e.itemInteraction.hover.coordinate=t.payload.activeCoordinate},mouseLeaveChart(e){e.itemInteraction.hover.active=!1,e.axisInteraction.hover.active=!1},mouseLeaveItem(e){e.itemInteraction.hover.active=!1},setActiveClickItemIndex(e,t){e.syncInteraction.active=!1,e.itemInteraction.click.active=!0,e.keyboardInteraction.active=!1,e.itemInteraction.click.index=t.payload.activeIndex,e.itemInteraction.click.dataKey=t.payload.activeDataKey,e.itemInteraction.click.coordinate=t.payload.activeCoordinate},setMouseOverAxisIndex(e,t){e.syncInteraction.active=!1,e.axisInteraction.hover.active=!0,e.keyboardInteraction.active=!1,e.axisInteraction.hover.index=t.payload.activeIndex,e.axisInteraction.hover.dataKey=t.payload.activeDataKey,e.axisInteraction.hover.coordinate=t.payload.activeCoordinate},setMouseClickAxisIndex(e,t){e.syncInteraction.active=!1,e.keyboardInteraction.active=!1,e.axisInteraction.click.active=!0,e.axisInteraction.click.index=t.payload.activeIndex,e.axisInteraction.click.dataKey=t.payload.activeDataKey,e.axisInteraction.click.coordinate=t.payload.activeCoordinate},setSyncInteraction(e,t){e.syncInteraction=t.payload},setKeyboardInteraction(e,t){e.keyboardInteraction.active=t.payload.active,e.keyboardInteraction.index=t.payload.activeIndex,e.keyboardInteraction.coordinate=t.payload.activeCoordinate,e.keyboardInteraction.dataKey=t.payload.activeDataKey}}}),{addTooltipEntrySettings:u,removeTooltipEntrySettings:l,setTooltipSettingsState:c,setActiveMouseOverItemIndex:s,mouseLeaveItem:f,mouseLeaveChart:d,setActiveClickItemIndex:p,setMouseOverAxisIndex:h,setMouseClickAxisIndex:v,setSyncInteraction:y,setKeyboardInteraction:g}=o.actions,m=o.reducer},83061:function(e,t,r){"use strict";r.d(t,{$:function(){return h},x:function(){return v}});var n=r(39129),i=r(64725),a=r(63330),o=r(13169),u=r(46302),l=r(78487),c=r(92713),s=r(73535),f=r(54803),d=(0,c.P1)([f.M],e=>e.tooltipItemPayloads),p=(0,c.P1)([d,s.h,(e,t,r)=>t,(e,t,r)=>r],(e,t,r,n)=>{var i=e.find(e=>e.settings.dataKey===n);if(null!=i){var{positions:a}=i;if(null!=a)return t(a,r)}}),h=(0,n.PH)("touchMove"),v=(0,n.e)();v.startListening({actionCreator:h,effect:(e,t)=>{var r=e.payload,n=t.getState(),c=(0,u.SB)(n,n.tooltip.settings.shared);if("axis"===c){var s=(0,a.h)(n,(0,o.a)({clientX:r.touches[0].clientX,clientY:r.touches[0].clientY,currentTarget:r.currentTarget}));(null==s?void 0:s.activeIndex)!=null&&t.dispatch((0,i.Rr)({activeIndex:s.activeIndex,activeDataKey:void 0,activeCoordinate:s.activeCoordinate}))}else if("item"===c){var f,d=r.touches[0],h=document.elementFromPoint(d.clientX,d.clientY);if(!h||!h.getAttribute)return;var v=h.getAttribute(l.Gh),y=null!==(f=h.getAttribute(l.aN))&&void 0!==f?f:void 0,g=p(t.getState(),v,y);t.dispatch((0,i.M1)({activeDataKey:y,activeIndex:v,activeCoordinate:g}))}}})},318:function(e,t,r){"use strict";r.d(t,{W9:function(){return g},Fg:function(){return m}});var n=r(2265),i=r(39040),a=r(33968),o=new(r(14631)),u="recharts.syncEvent.tooltip",l="recharts.syncEvent.brush",c=r(31057),s=r(64725),f=r(84461),d=r(31944);function p(e){return e.tooltip.syncInteraction}var h=r(35953),v=r(39173),y=()=>{};function g(){var e,t,r,f,p,g,m,b,x,w,O,j=(0,i.T)();(0,n.useEffect)(()=>{j((0,c.IC)())},[j]),e=(0,i.C)(a.Yd),t=(0,i.C)(a.Sg),r=(0,i.T)(),f=(0,i.C)(a.b2),p=(0,i.C)(d.WQ),g=(0,h.vn)(),m=(0,h.d2)(),b=(0,i.C)(e=>e.rootProps.className),(0,n.useEffect)(()=>{if(null==e)return y;var n=(n,i,a)=>{if(t!==a&&e===n){if("index"===f){r(i);return}if(null!=p){if("function"==typeof f){var o,u=f(p,{activeTooltipIndex:null==i.payload.index?void 0:Number(i.payload.index),isTooltipActive:i.payload.active,activeIndex:null==i.payload.index?void 0:Number(i.payload.index),activeLabel:i.payload.label,activeDataKey:i.payload.dataKey,activeCoordinate:i.payload.coordinate});o=p[u]}else"value"===f&&(o=p.find(e=>String(e.value)===i.payload.label));var{coordinate:l}=i.payload;if(null==o||!1===i.payload.active||null==l||null==m){r((0,s.$A)({active:!1,coordinate:void 0,dataKey:void 0,index:null,label:void 0}));return}var{x:c,y:d}=l,h=Math.min(c,m.x+m.width),v=Math.min(d,m.y+m.height),y={x:"horizontal"===g?o.coordinate:h,y:"horizontal"===g?v:o.coordinate};r((0,s.$A)({active:i.payload.active,coordinate:y,dataKey:i.payload.dataKey,index:String(o.index),label:i.payload.label}))}}};return o.on(u,n),()=>{o.off(u,n)}},[b,r,t,e,f,p,g,m]),x=(0,i.C)(a.Yd),w=(0,i.C)(a.Sg),O=(0,i.T)(),(0,n.useEffect)(()=>{if(null==x)return y;var e=(e,t,r)=>{w!==r&&x===e&&O((0,v.t0)(t))};return o.on(l,e),()=>{o.off(l,e)}},[O,w,x])}function m(e,t,r,l,c,d){var h=(0,i.C)(r=>(0,f.wi)(r,e,t)),v=(0,i.C)(a.Sg),y=(0,i.C)(a.Yd),g=(0,i.C)(a.b2),m=(0,i.C)(p),b=null==m?void 0:m.active;(0,n.useEffect)(()=>{if(!b&&null!=y&&null!=v){var e=(0,s.$A)({active:d,coordinate:r,dataKey:h,index:c,label:"number"==typeof l?String(l):l});o.emit(u,y,e,v)}},[b,r,h,c,l,v,y,g,d])}},80503:function(e,t,r){"use strict";r.d(t,{b:function(){return q}});var n=r(2265),i=r(46802),a=r.n(i),o=r(73649),u=r(61994),l=r(82944),c=r(40130),s=r(46595);function f(){return(f=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}var d=(e,t,r,n,i)=>{var a=r-n;return"M ".concat(e,",").concat(t)+"L ".concat(e+r,",").concat(t)+"L ".concat(e+r-a/2,",").concat(t+i)+"L ".concat(e+r-a/2-n,",").concat(t+i)+"L ".concat(e,",").concat(t," Z")},p={x:0,y:0,upperWidth:0,lowerWidth:0,height:0,isUpdateAnimationActive:!1,animationBegin:0,animationDuration:1500,animationEasing:"ease"},h=e=>{var t=(0,c.j)(e,p),r=(0,n.useRef)(),[i,a]=(0,n.useState)(-1);(0,n.useEffect)(()=>{if(r.current&&r.current.getTotalLength)try{var e=r.current.getTotalLength();e&&a(e)}catch(e){}},[]);var{x:o,y:h,upperWidth:v,lowerWidth:y,height:g,className:m}=t,{animationEasing:b,animationDuration:x,animationBegin:w,isUpdateAnimationActive:O}=t;if(o!==+o||h!==+h||v!==+v||y!==+y||g!==+g||0===v&&0===y||0===g)return null;var j=(0,u.W)("recharts-trapezoid",m);return O?n.createElement(s.r,{canBegin:i>0,from:{upperWidth:0,lowerWidth:0,height:g,x:o,y:h},to:{upperWidth:v,lowerWidth:y,height:g,x:o,y:h},duration:x,animationEasing:b,isActive:O},e=>{var{upperWidth:a,lowerWidth:o,height:u,x:c,y:p}=e;return n.createElement(s.r,{canBegin:i>0,from:"0px ".concat(-1===i?1:i,"px"),to:"".concat(i,"px 0px"),attributeName:"strokeDasharray",begin:w,duration:x,easing:b},n.createElement("path",f({},(0,l.L6)(t,!0),{className:j,d:d(c,p,a,o,u),ref:r})))}):n.createElement("g",null,n.createElement("path",f({},(0,l.L6)(t,!0),{className:j,d:d(o,h,v,y,g)})))},v=r(60474),y=r(9841);let g=Math.cos,m=Math.sin,b=Math.sqrt,x=Math.PI,w=2*x;var O={draw(e,t){let r=b(t/x);e.moveTo(r,0),e.arc(0,0,r,0,w)}};let j=b(1/3),P=2*j,k=m(x/10)/m(7*x/10),M=m(w/10)*k,S=-g(w/10)*k,A=b(3),E=b(3)/2,C=1/b(12),T=(C/2+1)*3;var D=r(76115),N=r(67790);b(3),b(3);var _=r(16630),I=["type","size","sizeType"];function R(){return(R=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function L(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function z(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?L(Object(r),!0).forEach(function(t){var n,i;n=t,i=r[t],(n=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(n))in e?Object.defineProperty(e,n,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[n]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):L(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var F={symbolCircle:O,symbolCross:{draw(e,t){let r=b(t/5)/2;e.moveTo(-3*r,-r),e.lineTo(-r,-r),e.lineTo(-r,-3*r),e.lineTo(r,-3*r),e.lineTo(r,-r),e.lineTo(3*r,-r),e.lineTo(3*r,r),e.lineTo(r,r),e.lineTo(r,3*r),e.lineTo(-r,3*r),e.lineTo(-r,r),e.lineTo(-3*r,r),e.closePath()}},symbolDiamond:{draw(e,t){let r=b(t/P),n=r*j;e.moveTo(0,-r),e.lineTo(n,0),e.lineTo(0,r),e.lineTo(-n,0),e.closePath()}},symbolSquare:{draw(e,t){let r=b(t),n=-r/2;e.rect(n,n,r,r)}},symbolStar:{draw(e,t){let r=b(.8908130915292852*t),n=M*r,i=S*r;e.moveTo(0,-r),e.lineTo(n,i);for(let t=1;t<5;++t){let a=w*t/5,o=g(a),u=m(a);e.lineTo(u*r,-o*r),e.lineTo(o*n-u*i,u*n+o*i)}e.closePath()}},symbolTriangle:{draw(e,t){let r=-b(t/(3*A));e.moveTo(0,2*r),e.lineTo(-A*r,-r),e.lineTo(A*r,-r),e.closePath()}},symbolWye:{draw(e,t){let r=b(t/T),n=r/2,i=r*C,a=r*C+r,o=-n;e.moveTo(n,i),e.lineTo(n,a),e.lineTo(o,a),e.lineTo(-.5*n-E*i,E*n+-.5*i),e.lineTo(-.5*n-E*a,E*n+-.5*a),e.lineTo(-.5*o-E*a,E*o+-.5*a),e.lineTo(-.5*n+E*i,-.5*i-E*n),e.lineTo(-.5*n+E*a,-.5*a-E*n),e.lineTo(-.5*o+E*a,-.5*a-E*o),e.closePath()}}},W=Math.PI/180,B=e=>F["symbol".concat((0,_.jC)(e))]||O,K=(e,t,r)=>{if("area"===t)return e;switch(r){case"cross":return 5*e*e/9;case"diamond":return .5*e*e/Math.sqrt(3);case"square":return e*e;case"star":var n=18*W;return 1.25*e*e*(Math.tan(n)-Math.tan(2*n)*Math.tan(n)**2);case"triangle":return Math.sqrt(3)*e*e/4;case"wye":return(21-10*Math.sqrt(3))*e*e/8;default:return Math.PI*e*e/4}},Z=e=>{var t,{type:r="circle",size:i=64,sizeType:a="area"}=e,o=z(z({},function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,I)),{},{type:r,size:i,sizeType:a}),{className:c,cx:s,cy:f}=o,d=(0,l.L6)(o,!0);return s===+s&&f===+f&&i===+i?n.createElement("path",R({},d,{className:(0,u.W)("recharts-symbols",c),transform:"translate(".concat(s,", ").concat(f,")"),d:(t=B(r),(function(e,t){let r=null,n=(0,N.d)(i);function i(){let i;if(r||(r=i=n()),e.apply(this,arguments).draw(r,+t.apply(this,arguments)),i)return r=null,i+""||null}return e="function"==typeof e?e:(0,D.Z)(e||O),t="function"==typeof t?t:(0,D.Z)(void 0===t?64:+t),i.type=function(t){return arguments.length?(e="function"==typeof t?t:(0,D.Z)(t),i):e},i.size=function(e){return arguments.length?(t="function"==typeof e?e:(0,D.Z)(+e),i):t},i.context=function(e){return arguments.length?(r=null==e?null:e,i):r},i})().type(t).size(K(i,a,r))())})):null};Z.registerSymbol=(e,t)=>{F["symbol".concat((0,_.jC)(e))]=t};var U=["option","shapeType","propTransformer","activeClassName","isActive"];function V(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function $(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?V(Object(r),!0).forEach(function(t){var n,i;n=t,i=r[t],(n=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(n))in e?Object.defineProperty(e,n,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[n]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):V(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function Y(e,t){return $($({},t),e)}function H(e){var{shapeType:t,elementProps:r}=e;switch(t){case"rectangle":return n.createElement(o.A,r);case"trapezoid":return n.createElement(h,r);case"sector":return n.createElement(v.L,r);case"symbols":if("symbols"===t)return n.createElement(Z,r);break;default:return null}}function q(e){var t,{option:r,shapeType:i,propTransformer:o=Y,activeClassName:u="recharts-active-shape",isActive:l}=e,c=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,U);if((0,n.isValidElement)(r))t=(0,n.cloneElement)(r,$($({},c),(0,n.isValidElement)(r)?r.props:r));else if("function"==typeof r)t=r(c);else if(a()(r)&&"boolean"!=typeof r){var s=o(r,c);t=n.createElement(H,{shapeType:i,elementProps:s})}else t=n.createElement(H,{shapeType:i,elementProps:c});return l?n.createElement(y.m,{className:u},t):t}},49037:function(e,t,r){"use strict";r.d(t,{Ji:function(){return _},rI:function(){return N},By:function(){return b},VO:function(){return m},HZ:function(){return W},zF:function(){return j},ep:function(){return F},zT:function(){return I},Yj:function(){return E},Fy:function(){return A},Rf:function(){return w},EB:function(){return D},GA:function(){return S},uX:function(){return M},uY:function(){return O},eV:function(){return R},hn:function(){return L},F$:function(){return g},Z2:function(){return z},NA:function(){return x},Vv:function(){return P}});var n=r(31104),i=r.n(n),a=r(15870),o=r.n(a);function u(e,t){if((i=e.length)>1)for(var r,n,i,a=1,o=e[t[0]],u=o.length;a<i;++a)for(n=o,o=e[t[a]],r=0;r<u;++r)o[r][1]+=o[r][0]=isNaN(n[r][1])?n[r][0]:n[r][1]}var l=r(22516),c=r(76115);function s(e){for(var t=e.length,r=Array(t);--t>=0;)r[t]=t;return r}function f(e,t){return e[t]}function d(e){let t=[];return t.key=e,t}var p=r(16630),h=r(39206);function v(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function y(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?v(Object(r),!0).forEach(function(t){var n,i;n=t,i=r[t],(n=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(n))in e?Object.defineProperty(e,n,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[n]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):v(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function g(e,t,r){return(0,p.Rw)(e)||(0,p.Rw)(t)?r:(0,p.P2)(t)?o()(e,t,r):"function"==typeof t?t(e):r}var m=(e,t,r,n,i)=>{var a,o=-1,u=null!==(a=null==t?void 0:t.length)&&void 0!==a?a:0;if(u<=1||null==e)return 0;if("angleAxis"===n&&null!=i&&1e-6>=Math.abs(Math.abs(i[1]-i[0])-360))for(var l=0;l<u;l++){var c=l>0?r[l-1].coordinate:r[u-1].coordinate,s=r[l].coordinate,f=l>=u-1?r[0].coordinate:r[l+1].coordinate,d=void 0;if((0,p.uY)(s-c)!==(0,p.uY)(f-s)){var h=[];if((0,p.uY)(f-s)===(0,p.uY)(i[1]-i[0])){d=f;var v=s+i[1]-i[0];h[0]=Math.min(v,(v+c)/2),h[1]=Math.max(v,(v+c)/2)}else{d=c;var y=f+i[1]-i[0];h[0]=Math.min(s,(y+s)/2),h[1]=Math.max(s,(y+s)/2)}var g=[Math.min(s,(d+s)/2),Math.max(s,(d+s)/2)];if(e>g[0]&&e<=g[1]||e>=h[0]&&e<=h[1]){({index:o}=r[l]);break}}else{var m=Math.min(c,f),b=Math.max(c,f);if(e>(m+s)/2&&e<=(b+s)/2){({index:o}=r[l]);break}}}else if(t){for(var x=0;x<u;x++)if(0===x&&e<=(t[x].coordinate+t[x+1].coordinate)/2||x>0&&x<u-1&&e>(t[x].coordinate+t[x-1].coordinate)/2&&e<=(t[x].coordinate+t[x+1].coordinate)/2||x===u-1&&e>(t[x].coordinate+t[x-1].coordinate)/2){({index:o}=t[x]);break}}return o},b=(e,t,r)=>{if(t&&r){var{width:n,height:i}=r,{align:a,verticalAlign:o,layout:u}=t;if(("vertical"===u||"horizontal"===u&&"middle"===o)&&"center"!==a&&(0,p.hj)(e[a]))return y(y({},e),{},{[a]:e[a]+(n||0)});if(("horizontal"===u||"vertical"===u&&"center"===a)&&"middle"!==o&&(0,p.hj)(e[o]))return y(y({},e),{},{[o]:e[o]+(i||0)})}return e},x=(e,t)=>"horizontal"===e&&"xAxis"===t||"vertical"===e&&"yAxis"===t||"centric"===e&&"angleAxis"===t||"radial"===e&&"radiusAxis"===t,w=(e,t,r,n)=>{if(n)return e.map(e=>e.coordinate);var i,a,o=e.map(e=>(e.coordinate===t&&(i=!0),e.coordinate===r&&(a=!0),e.coordinate));return i||o.push(t),a||o.push(r),o},O=(e,t,r)=>{if(!e)return null;var{duplicateDomain:n,type:i,range:a,scale:o,realScaleType:u,isCategorical:l,categoricalDomain:c,tickCount:s,ticks:f,niceTicks:d,axisType:h}=e;if(!o)return null;var v="scaleBand"===u&&o.bandwidth?o.bandwidth()/2:2,y=(t||r)&&"category"===i&&o.bandwidth?o.bandwidth()/v:0;return(y="angleAxis"===h&&a&&a.length>=2?2*(0,p.uY)(a[0]-a[1])*y:y,t&&(f||d))?(f||d||[]).map((e,t)=>({coordinate:o(n?n.indexOf(e):e)+y,value:e,offset:y,index:t})).filter(e=>!(0,p.In)(e.coordinate)):l&&c?c.map((e,t)=>({coordinate:o(e)+y,value:e,index:t,offset:y})):o.ticks&&!r&&null!=s?o.ticks(s).map((e,t)=>({coordinate:o(e)+y,value:e,offset:y,index:t})):o.domain().map((e,t)=>({coordinate:o(e)+y,value:n?n[e]:e,index:t,offset:y}))},j=e=>{var t=e.domain();if(t&&!(t.length<=2)){var r=t.length,n=e.range(),i=Math.min(n[0],n[1])-1e-4,a=Math.max(n[0],n[1])+1e-4,o=e(t[0]),u=e(t[r-1]);(o<i||o>a||u<i||u>a)&&e.domain([t[0],t[r-1]])}},P=(e,t)=>{if(!t||2!==t.length||!(0,p.hj)(t[0])||!(0,p.hj)(t[1]))return e;var r=Math.min(t[0],t[1]),n=Math.max(t[0],t[1]),i=[e[0],e[1]];return(!(0,p.hj)(e[0])||e[0]<r)&&(i[0]=r),(!(0,p.hj)(e[1])||e[1]>n)&&(i[1]=n),i[0]>n&&(i[0]=n),i[1]<r&&(i[1]=r),i},k={sign:e=>{var t=e.length;if(!(t<=0))for(var r=0,n=e[0].length;r<n;++r)for(var i=0,a=0,o=0;o<t;++o){var u=(0,p.In)(e[o][r][1])?e[o][r][0]:e[o][r][1];u>=0?(e[o][r][0]=i,e[o][r][1]=i+u,i=e[o][r][1]):(e[o][r][0]=a,e[o][r][1]=a+u,a=e[o][r][1])}},expand:function(e,t){if((n=e.length)>0){for(var r,n,i,a=0,o=e[0].length;a<o;++a){for(i=r=0;r<n;++r)i+=e[r][a][1]||0;if(i)for(r=0;r<n;++r)e[r][a][1]/=i}u(e,t)}},none:u,silhouette:function(e,t){if((r=e.length)>0){for(var r,n=0,i=e[t[0]],a=i.length;n<a;++n){for(var o=0,l=0;o<r;++o)l+=e[o][n][1]||0;i[n][1]+=i[n][0]=-l/2}u(e,t)}},wiggle:function(e,t){if((i=e.length)>0&&(n=(r=e[t[0]]).length)>0){for(var r,n,i,a=0,o=1;o<n;++o){for(var l=0,c=0,s=0;l<i;++l){for(var f=e[t[l]],d=f[o][1]||0,p=(d-(f[o-1][1]||0))/2,h=0;h<l;++h){var v=e[t[h]];p+=(v[o][1]||0)-(v[o-1][1]||0)}c+=d,s+=p*d}r[o-1][1]+=r[o-1][0]=a,c&&(a-=s/c)}r[o-1][1]+=r[o-1][0]=a,u(e,t)}},positive:e=>{var t=e.length;if(!(t<=0))for(var r=0,n=e[0].length;r<n;++r)for(var i=0,a=0;a<t;++a){var o=(0,p.In)(e[a][r][1])?e[a][r][0]:e[a][r][1];o>=0?(e[a][r][0]=i,e[a][r][1]=i+o,i=e[a][r][1]):(e[a][r][0]=0,e[a][r][1]=0)}}},M=(e,t,r)=>{var n=k[r];return(function(){var e=(0,c.Z)([]),t=s,r=u,n=f;function i(i){var a,o,u=Array.from(e.apply(this,arguments),d),c=u.length,s=-1;for(let e of i)for(a=0,++s;a<c;++a)(u[a][s]=[0,+n(e,u[a].key,s,i)]).data=e;for(a=0,o=(0,l.Z)(t(u));a<c;++a)u[o[a]].index=a;return r(u,o),u}return i.keys=function(t){return arguments.length?(e="function"==typeof t?t:(0,c.Z)(Array.from(t)),i):e},i.value=function(e){return arguments.length?(n="function"==typeof e?e:(0,c.Z)(+e),i):n},i.order=function(e){return arguments.length?(t=null==e?s:"function"==typeof e?e:(0,c.Z)(Array.from(e)),i):t},i.offset=function(e){return arguments.length?(r=null==e?u:e,i):r},i})().keys(t).value((e,t)=>+g(e,t,0)).order(s).offset(n)(e)};function S(e){return null==e?void 0:String(e)}var A=e=>{var{axis:t,ticks:r,offset:n,bandSize:i,entry:a,index:o}=e;if("category"===t.type)return r[o]?r[o].coordinate+n:null;var u=g(a,t.dataKey,t.scale.domain()[o]);return(0,p.Rw)(u)?null:t.scale(u)-i/2+n},E=e=>{var{numericAxis:t}=e,r=t.scale.domain();if("number"===t.type){var n=Math.min(r[0],r[1]),i=Math.max(r[0],r[1]);return n<=0&&i>=0?0:i<0?i:n}return r[0]},C=e=>{var t=e.flat(2).filter(p.hj);return[Math.min(...t),Math.max(...t)]},T=e=>[e[0]===1/0?0:e[0],e[1]===-1/0?0:e[1]],D=(e,t,r)=>{if(null!=e)return T(Object.keys(e).reduce((n,i)=>{var{stackedData:a}=e[i],o=a.reduce((e,n)=>{var i=C(n.slice(t,r+1));return[Math.min(e[0],i[0]),Math.max(e[1],i[1])]},[1/0,-1/0]);return[Math.min(o[0],n[0]),Math.max(o[1],n[1])]},[1/0,-1/0]))},N=/^dataMin[\s]*-[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,_=/^dataMax[\s]*\+[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,I=(e,t,r)=>{if(e&&e.scale&&e.scale.bandwidth){var n=e.scale.bandwidth();if(!r||n>0)return n}if(e&&t&&t.length>=2){for(var a=i()(t,e=>e.coordinate),o=1/0,u=1,l=a.length;u<l;u++){var c=a[u],s=a[u-1];o=Math.min((c.coordinate||0)-(s.coordinate||0),o)}return o===1/0?0:o}return r?void 0:0};function R(e){var{tooltipEntrySettings:t,dataKey:r,payload:n,value:i,name:a}=e;return y(y({},t),{},{dataKey:r,payload:n,value:i,name:a})}function L(e,t){return e?String(e):"string"==typeof t?t:void 0}function z(e,t,r,n,i){return"horizontal"===r||"vertical"===r?e>=i.left&&e<=i.left+i.width&&t>=i.top&&t<=i.top+i.height?{x:e,y:t}:null:n?(0,h.z3)({x:e,y:t},n):null}var F=(e,t,r,n)=>{var i=t.find(e=>e&&e.index===r);if(i){if("horizontal"===e)return{x:i.coordinate,y:n.y};if("vertical"===e)return{x:n.x,y:i.coordinate};if("centric"===e){var a=i.coordinate,{radius:o}=n;return y(y(y({},n),(0,h.op)(n.cx,n.cy,o,a)),{},{angle:a,radius:o})}var u=i.coordinate,{angle:l}=n;return y(y(y({},n),(0,h.op)(n.cx,n.cy,u,l)),{},{angle:l,radius:u})}return{x:0,y:0}},W=(e,t)=>"horizontal"===t?e.x:"vertical"===t?e.y:"centric"===t?e.angle:e.radius},78487:function(e,t,r){"use strict";r.d(t,{Gh:function(){return n},aN:function(){return i},n9:function(){return a}});var n="data-recharts-item-index",i="data-recharts-item-data-key",a=60},4094:function(e,t,r){"use strict";r.d(t,{x:function(){return c}});var n=r(34067);function i(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function a(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?i(Object(r),!0).forEach(function(t){var n,i;n=t,i=r[t],(n=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(n))in e?Object.defineProperty(e,n,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[n]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):i(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var o={widthCache:{},cacheCount:0},u={position:"absolute",top:"-20000px",left:0,padding:0,margin:0,border:"none",whiteSpace:"pre"},l="recharts_measurement_span",c=function(e){var t,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(null==e||n.x.isSsr)return{width:0,height:0};var i=(Object.keys(t=a({},r)).forEach(e=>{t[e]||delete t[e]}),t),c=JSON.stringify({text:e,copyStyle:i});if(o.widthCache[c])return o.widthCache[c];try{var s=document.getElementById(l);s||((s=document.createElement("span")).setAttribute("id",l),s.setAttribute("aria-hidden","true"),document.body.appendChild(s));var f=a(a({},u),i);Object.assign(s.style,f),s.textContent="".concat(e);var d=s.getBoundingClientRect(),p={width:d.width,height:d.height};return o.widthCache[c]=p,++o.cacheCount>2e3&&(o.cacheCount=0,o.widthCache={}),p}catch(e){return{width:0,height:0}}}},16630:function(e,t,r){"use strict";r.d(t,{Ap:function(){return v},EL:function(){return f},In:function(){return o},P2:function(){return c},Rw:function(){return y},bv:function(){return p},h1:function(){return d},hU:function(){return u},hj:function(){return l},jC:function(){return g},k4:function(){return h},uY:function(){return a}});var n=r(15870),i=r.n(n),a=e=>0===e?0:e>0?1:-1,o=e=>"number"==typeof e&&e!=+e,u=e=>"string"==typeof e&&e.indexOf("%")===e.length-1,l=e=>("number"==typeof e||e instanceof Number)&&!o(e),c=e=>l(e)||"string"==typeof e,s=0,f=e=>{var t=++s;return"".concat(e||"").concat(t)},d=function(e,t){var r,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,i=arguments.length>3&&void 0!==arguments[3]&&arguments[3];if(!l(e)&&"string"!=typeof e)return n;if(u(e)){if(null==t)return n;var a=e.indexOf("%");r=t*parseFloat(e.slice(0,a))/100}else r=+e;return o(r)&&(r=n),i&&null!=t&&r>t&&(r=t),r},p=e=>{if(!Array.isArray(e))return!1;for(var t=e.length,r={},n=0;n<t;n++){if(r[e[n]])return!0;r[e[n]]=!0}return!1},h=(e,t)=>l(e)&&l(t)?r=>e+r*(t-e):()=>t;function v(e,t,r){if(e&&e.length)return e.find(e=>e&&("function"==typeof t?t(e):i()(e,t))===r)}var y=e=>null==e,g=e=>y(e)?e:"".concat(e.charAt(0).toUpperCase()).concat(e.slice(1))},34067:function(e,t,r){"use strict";r.d(t,{x:function(){return n}});var n={isSsr:!("undefined"!=typeof window&&window.document&&window.document.createElement&&window.setTimeout)}},1175:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});var n=function(e,t){for(var r=arguments.length,n=Array(r>2?r-2:0),i=2;i<r;i++)n[i-2]=arguments[i]}},39206:function(e,t,r){"use strict";function n(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function i(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?n(Object(r),!0).forEach(function(t){var n,i;n=t,i=r[t],(n=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(n))in e?Object.defineProperty(e,n,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[n]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):n(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}r.d(t,{$4:function(){return l},Wk:function(){return a},op:function(){return u},z3:function(){return p}}),r(2265);var a=Math.PI/180,o=e=>180*e/Math.PI,u=(e,t,r,n)=>({x:e+Math.cos(-a*n)*r,y:t+Math.sin(-a*n)*r}),l=function(e,t){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{top:0,right:0,bottom:0,left:0};return Math.min(Math.abs(e-(r.left||0)-(r.right||0)),Math.abs(t-(r.top||0)-(r.bottom||0)))/2},c=(e,t)=>{var{x:r,y:n}=e,{x:i,y:a}=t;return Math.sqrt((r-i)**2+(n-a)**2)},s=(e,t)=>{var{x:r,y:n}=e,{cx:i,cy:a}=t,u=c({x:r,y:n},{x:i,y:a});if(u<=0)return{radius:u,angle:0};var l=Math.acos((r-i)/u);return n>a&&(l=2*Math.PI-l),{radius:u,angle:o(l),angleInRadian:l}},f=e=>{var{startAngle:t,endAngle:r}=e,n=Math.min(Math.floor(t/360),Math.floor(r/360));return{startAngle:t-360*n,endAngle:r-360*n}},d=(e,t)=>{var{startAngle:r,endAngle:n}=t;return e+360*Math.min(Math.floor(r/360),Math.floor(n/360))},p=(e,t)=>{var r,{x:n,y:a}=e,{radius:o,angle:u}=s({x:n,y:a},t),{innerRadius:l,outerRadius:c}=t;if(o<l||o>c||0===o)return null;var{startAngle:p,endAngle:h}=f(t),v=u;if(p<=h){for(;v>h;)v-=360;for(;v<p;)v+=360;r=v>=p&&v<=h}else{for(;v>p;)v-=360;for(;v<h;)v+=360;r=v>=h&&v<=p}return r?i(i({},t),{},{radius:o,angle:d(v,t)}):null}},82944:function(e,t,r){"use strict";r.d(t,{L6:function(){return v},NN:function(){return p}});var n=r(15870),i=r.n(n),a=r(2265),o=r(59679),u=r(16630),l=r(41637),c=e=>"string"==typeof e?e:e?e.displayName||e.name||"Component":"",s=null,f=null,d=e=>{if(e===s&&Array.isArray(f))return f;var t=[];return a.Children.forEach(e,e=>{(0,u.Rw)(e)||((0,o.M2)(e)?t=t.concat(d(e.props.children)):t.push(e))}),f=t,s=e,t};function p(e,t){var r=[],n=[];return n=Array.isArray(t)?t.map(e=>c(e)):[c(t)],d(e).forEach(e=>{var t=i()(e,"type.displayName")||i()(e,"type.name");-1!==n.indexOf(t)&&r.push(e)}),r}var h=(e,t,r,n)=>{var i,a=null!==(i=n&&(null===l.ry||void 0===l.ry?void 0:l.ry[n]))&&void 0!==i?i:[];return t.startsWith("data-")||"function"!=typeof e&&(n&&a.includes(t)||l.Yh.includes(t))||r&&l.nv.includes(t)},v=(e,t,r)=>{if(!e||"function"==typeof e||"boolean"==typeof e)return null;var n=e;if((0,a.isValidElement)(e)&&(n=e.props),"object"!=typeof n&&"function"!=typeof n)return null;var i={};return Object.keys(n).forEach(e=>{var a;h(null===(a=n)||void 0===a?void 0:a[e],e,t,r)&&(i[e]=n[e])}),i}},13169:function(e,t,r){"use strict";r.d(t,{a:function(){return n}});var n=e=>{var t=e.currentTarget.getBoundingClientRect(),r=t.width/e.currentTarget.offsetWidth,n=t.height/e.currentTarget.offsetHeight;return{chartX:Math.round((e.clientX-t.left)/r),chartY:Math.round((e.clientY-t.top)/n)}}},66395:function(e,t,r){"use strict";function n(e){return Number.isFinite(e)}function i(e){return"number"==typeof e&&e>0&&Number.isFinite(e)}r.d(t,{n:function(){return n},r:function(){return i}})},40130:function(e,t,r){"use strict";function n(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function i(e,t){var r=function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?n(Object(r),!0).forEach(function(t){var n,i;n=t,i=r[t],(n=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(n))in e?Object.defineProperty(e,n,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[n]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):n(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}({},e);return Object.keys(t).reduce((e,r)=>(void 0===e[r]&&void 0!==t[r]&&(e[r]=t[r]),e),r)}r.d(t,{j:function(){return i}})},41637:function(e,t,r){"use strict";r.d(t,{Yh:function(){return i},Ym:function(){return l},bw:function(){return s},nv:function(){return u},ry:function(){return o}});var n=r(2265),i=["aria-activedescendant","aria-atomic","aria-autocomplete","aria-busy","aria-checked","aria-colcount","aria-colindex","aria-colspan","aria-controls","aria-current","aria-describedby","aria-details","aria-disabled","aria-errormessage","aria-expanded","aria-flowto","aria-haspopup","aria-hidden","aria-invalid","aria-keyshortcuts","aria-label","aria-labelledby","aria-level","aria-live","aria-modal","aria-multiline","aria-multiselectable","aria-orientation","aria-owns","aria-placeholder","aria-posinset","aria-pressed","aria-readonly","aria-relevant","aria-required","aria-roledescription","aria-rowcount","aria-rowindex","aria-rowspan","aria-selected","aria-setsize","aria-sort","aria-valuemax","aria-valuemin","aria-valuenow","aria-valuetext","className","color","height","id","lang","max","media","method","min","name","style","target","width","role","tabIndex","accentHeight","accumulate","additive","alignmentBaseline","allowReorder","alphabetic","amplitude","arabicForm","ascent","attributeName","attributeType","autoReverse","azimuth","baseFrequency","baselineShift","baseProfile","bbox","begin","bias","by","calcMode","capHeight","clip","clipPath","clipPathUnits","clipRule","colorInterpolation","colorInterpolationFilters","colorProfile","colorRendering","contentScriptType","contentStyleType","cursor","cx","cy","d","decelerate","descent","diffuseConstant","direction","display","divisor","dominantBaseline","dur","dx","dy","edgeMode","elevation","enableBackground","end","exponent","externalResourcesRequired","fill","fillOpacity","fillRule","filter","filterRes","filterUnits","floodColor","floodOpacity","focusable","fontFamily","fontSize","fontSizeAdjust","fontStretch","fontStyle","fontVariant","fontWeight","format","from","fx","fy","g1","g2","glyphName","glyphOrientationHorizontal","glyphOrientationVertical","glyphRef","gradientTransform","gradientUnits","hanging","horizAdvX","horizOriginX","href","ideographic","imageRendering","in2","in","intercept","k1","k2","k3","k4","k","kernelMatrix","kernelUnitLength","kerning","keyPoints","keySplines","keyTimes","lengthAdjust","letterSpacing","lightingColor","limitingConeAngle","local","markerEnd","markerHeight","markerMid","markerStart","markerUnits","markerWidth","mask","maskContentUnits","maskUnits","mathematical","mode","numOctaves","offset","opacity","operator","order","orient","orientation","origin","overflow","overlinePosition","overlineThickness","paintOrder","panose1","pathLength","patternContentUnits","patternTransform","patternUnits","pointerEvents","pointsAtX","pointsAtY","pointsAtZ","preserveAlpha","preserveAspectRatio","primitiveUnits","r","radius","refX","refY","renderingIntent","repeatCount","repeatDur","requiredExtensions","requiredFeatures","restart","result","rotate","rx","ry","seed","shapeRendering","slope","spacing","specularConstant","specularExponent","speed","spreadMethod","startOffset","stdDeviation","stemh","stemv","stitchTiles","stopColor","stopOpacity","strikethroughPosition","strikethroughThickness","string","stroke","strokeDasharray","strokeDashoffset","strokeLinecap","strokeLinejoin","strokeMiterlimit","strokeOpacity","strokeWidth","surfaceScale","systemLanguage","tableValues","targetX","targetY","textAnchor","textDecoration","textLength","textRendering","to","transform","u1","u2","underlinePosition","underlineThickness","unicode","unicodeBidi","unicodeRange","unitsPerEm","vAlphabetic","values","vectorEffect","version","vertAdvY","vertOriginX","vertOriginY","vHanging","vIdeographic","viewTarget","visibility","vMathematical","widths","wordSpacing","writingMode","x1","x2","x","xChannelSelector","xHeight","xlinkActuate","xlinkArcrole","xlinkHref","xlinkRole","xlinkShow","xlinkTitle","xlinkType","xmlBase","xmlLang","xmlns","xmlnsXlink","xmlSpace","y1","y2","y","yChannelSelector","z","zoomAndPan","ref","key","angle"],a=["points","pathLength"],o={svg:["viewBox","children"],polygon:a,polyline:a},u=["dangerouslySetInnerHTML","onCopy","onCopyCapture","onCut","onCutCapture","onPaste","onPasteCapture","onCompositionEnd","onCompositionEndCapture","onCompositionStart","onCompositionStartCapture","onCompositionUpdate","onCompositionUpdateCapture","onFocus","onFocusCapture","onBlur","onBlurCapture","onChange","onChangeCapture","onBeforeInput","onBeforeInputCapture","onInput","onInputCapture","onReset","onResetCapture","onSubmit","onSubmitCapture","onInvalid","onInvalidCapture","onLoad","onLoadCapture","onError","onErrorCapture","onKeyDown","onKeyDownCapture","onKeyPress","onKeyPressCapture","onKeyUp","onKeyUpCapture","onAbort","onAbortCapture","onCanPlay","onCanPlayCapture","onCanPlayThrough","onCanPlayThroughCapture","onDurationChange","onDurationChangeCapture","onEmptied","onEmptiedCapture","onEncrypted","onEncryptedCapture","onEnded","onEndedCapture","onLoadedData","onLoadedDataCapture","onLoadedMetadata","onLoadedMetadataCapture","onLoadStart","onLoadStartCapture","onPause","onPauseCapture","onPlay","onPlayCapture","onPlaying","onPlayingCapture","onProgress","onProgressCapture","onRateChange","onRateChangeCapture","onSeeked","onSeekedCapture","onSeeking","onSeekingCapture","onStalled","onStalledCapture","onSuspend","onSuspendCapture","onTimeUpdate","onTimeUpdateCapture","onVolumeChange","onVolumeChangeCapture","onWaiting","onWaitingCapture","onAuxClick","onAuxClickCapture","onClick","onClickCapture","onContextMenu","onContextMenuCapture","onDoubleClick","onDoubleClickCapture","onDrag","onDragCapture","onDragEnd","onDragEndCapture","onDragEnter","onDragEnterCapture","onDragExit","onDragExitCapture","onDragLeave","onDragLeaveCapture","onDragOver","onDragOverCapture","onDragStart","onDragStartCapture","onDrop","onDropCapture","onMouseDown","onMouseDownCapture","onMouseEnter","onMouseLeave","onMouseMove","onMouseMoveCapture","onMouseOut","onMouseOutCapture","onMouseOver","onMouseOverCapture","onMouseUp","onMouseUpCapture","onSelect","onSelectCapture","onTouchCancel","onTouchCancelCapture","onTouchEnd","onTouchEndCapture","onTouchMove","onTouchMoveCapture","onTouchStart","onTouchStartCapture","onPointerDown","onPointerDownCapture","onPointerMove","onPointerMoveCapture","onPointerUp","onPointerUpCapture","onPointerCancel","onPointerCancelCapture","onPointerEnter","onPointerEnterCapture","onPointerLeave","onPointerLeaveCapture","onPointerOver","onPointerOverCapture","onPointerOut","onPointerOutCapture","onGotPointerCapture","onGotPointerCaptureCapture","onLostPointerCapture","onLostPointerCaptureCapture","onScroll","onScrollCapture","onWheel","onWheelCapture","onAnimationStart","onAnimationStartCapture","onAnimationEnd","onAnimationEndCapture","onAnimationIteration","onAnimationIterationCapture","onTransitionEnd","onTransitionEndCapture"],l=(e,t)=>{if(!e||"function"==typeof e||"boolean"==typeof e)return null;var r=e;if((0,n.isValidElement)(e)&&(r=e.props),"object"!=typeof r&&"function"!=typeof r)return null;var i={};return Object.keys(r).forEach(e=>{u.includes(e)&&(i[e]=t||(t=>r[e](r,t)))}),i},c=(e,t,r)=>n=>(e(t,r,n),null),s=(e,t,r)=>{if(null===e||"object"!=typeof e&&"function"!=typeof e)return null;var n=null;return Object.keys(e).forEach(i=>{var a=e[i];u.includes(i)&&"function"==typeof a&&(n||(n={}),n[i]=c(a,t,r))}),n}},59087:function(e,t,r){"use strict";r.d(t,{i:function(){return a}});var n=r(2265),i=r(16630);function a(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"animation-",r=(0,n.useRef)((0,i.EL)(t)),a=(0,n.useRef)(e);return a.current!==e&&(r.current=(0,i.EL)(t),a.current=e),r.current}},14631:function(e){"use strict";var t=Object.prototype.hasOwnProperty,r="~";function n(){}function i(e,t,r){this.fn=e,this.context=t,this.once=r||!1}function a(e,t,n,a,o){if("function"!=typeof n)throw TypeError("The listener must be a function");var u=new i(n,a||e,o),l=r?r+t:t;return e._events[l]?e._events[l].fn?e._events[l]=[e._events[l],u]:e._events[l].push(u):(e._events[l]=u,e._eventsCount++),e}function o(e,t){0==--e._eventsCount?e._events=new n:delete e._events[t]}function u(){this._events=new n,this._eventsCount=0}Object.create&&(n.prototype=Object.create(null),new n().__proto__||(r=!1)),u.prototype.eventNames=function(){var e,n,i=[];if(0===this._eventsCount)return i;for(n in e=this._events)t.call(e,n)&&i.push(r?n.slice(1):n);return Object.getOwnPropertySymbols?i.concat(Object.getOwnPropertySymbols(e)):i},u.prototype.listeners=function(e){var t=r?r+e:e,n=this._events[t];if(!n)return[];if(n.fn)return[n.fn];for(var i=0,a=n.length,o=Array(a);i<a;i++)o[i]=n[i].fn;return o},u.prototype.listenerCount=function(e){var t=r?r+e:e,n=this._events[t];return n?n.fn?1:n.length:0},u.prototype.emit=function(e,t,n,i,a,o){var u=r?r+e:e;if(!this._events[u])return!1;var l,c,s=this._events[u],f=arguments.length;if(s.fn){switch(s.once&&this.removeListener(e,s.fn,void 0,!0),f){case 1:return s.fn.call(s.context),!0;case 2:return s.fn.call(s.context,t),!0;case 3:return s.fn.call(s.context,t,n),!0;case 4:return s.fn.call(s.context,t,n,i),!0;case 5:return s.fn.call(s.context,t,n,i,a),!0;case 6:return s.fn.call(s.context,t,n,i,a,o),!0}for(c=1,l=Array(f-1);c<f;c++)l[c-1]=arguments[c];s.fn.apply(s.context,l)}else{var d,p=s.length;for(c=0;c<p;c++)switch(s[c].once&&this.removeListener(e,s[c].fn,void 0,!0),f){case 1:s[c].fn.call(s[c].context);break;case 2:s[c].fn.call(s[c].context,t);break;case 3:s[c].fn.call(s[c].context,t,n);break;case 4:s[c].fn.call(s[c].context,t,n,i);break;default:if(!l)for(d=1,l=Array(f-1);d<f;d++)l[d-1]=arguments[d];s[c].fn.apply(s[c].context,l)}}return!0},u.prototype.on=function(e,t,r){return a(this,e,t,r,!1)},u.prototype.once=function(e,t,r){return a(this,e,t,r,!0)},u.prototype.removeListener=function(e,t,n,i){var a=r?r+e:e;if(!this._events[a])return this;if(!t)return o(this,a),this;var u=this._events[a];if(u.fn)u.fn!==t||i&&!u.once||n&&u.context!==n||o(this,a);else{for(var l=0,c=[],s=u.length;l<s;l++)(u[l].fn!==t||i&&!u[l].once||n&&u[l].context!==n)&&c.push(u[l]);c.length?this._events[a]=1===c.length?c[0]:c:o(this,a)}return this},u.prototype.removeAllListeners=function(e){var t;return e?(t=r?r+e:e,this._events[t]&&o(this,t)):(this._events=new n,this._eventsCount=0),this},u.prototype.off=u.prototype.removeListener,u.prototype.addListener=u.prototype.on,u.prefixed=r,u.EventEmitter=u,e.exports=u},24369:function(e,t,r){"use strict";var n=r(2265),i="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},a=n.useState,o=n.useEffect,u=n.useLayoutEffect,l=n.useDebugValue;function c(e){var t=e.getSnapshot;e=e.value;try{var r=t();return!i(e,r)}catch(e){return!0}}var s="undefined"==typeof window||void 0===window.document||void 0===window.document.createElement?function(e,t){return t()}:function(e,t){var r=t(),n=a({inst:{value:r,getSnapshot:t}}),i=n[0].inst,s=n[1];return u(function(){i.value=r,i.getSnapshot=t,c(i)&&s({inst:i})},[e,r,t]),o(function(){return c(i)&&s({inst:i}),e(function(){c(i)&&s({inst:i})})},[e]),l(r),r};t.useSyncExternalStore=void 0!==n.useSyncExternalStore?n.useSyncExternalStore:s},92860:function(e,t,r){"use strict";var n=r(2265),i=r(82558),a="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},o=i.useSyncExternalStore,u=n.useRef,l=n.useEffect,c=n.useMemo,s=n.useDebugValue;t.useSyncExternalStoreWithSelector=function(e,t,r,n,i){var f=u(null);if(null===f.current){var d={hasValue:!1,value:null};f.current=d}else d=f.current;var p=o(e,(f=c(function(){function e(e){if(!l){if(l=!0,o=e,e=n(e),void 0!==i&&d.hasValue){var t=d.value;if(i(t,e))return u=t}return u=e}if(t=u,a(o,e))return t;var r=n(e);return void 0!==i&&i(t,r)?(o=e,t):(o=e,u=r)}var o,u,l=!1,c=void 0===r?null:r;return[function(){return e(t())},null===c?void 0:function(){return e(c())}]},[t,r,n,i]))[0],f[1]);return l(function(){d.hasValue=!0,d.value=p},[p]),s(p),p}},82558:function(e,t,r){"use strict";e.exports=r(24369)},35195:function(e,t,r){"use strict";e.exports=r(92860)},13134:function(e,t,r){"use strict";r.d(t,{VY:function(){return eo},h4:function(){return ei},ck:function(){return en},fC:function(){return er},xz:function(){return ea}});var n=r(2265),i=r(73966),a=r(58068),o=r(98575),u=r(6741),l=r(80886),c=r(66840),s=r(61188),f=r(71599),d=r(99255),p=r(57437),h="Collapsible",[v,y]=(0,i.b)(h),[g,m]=v(h),b=n.forwardRef((e,t)=>{let{__scopeCollapsible:r,open:i,defaultOpen:a,disabled:o,onOpenChange:u,...s}=e,[f,v]=(0,l.T)({prop:i,defaultProp:null!=a&&a,onChange:u,caller:h});return(0,p.jsx)(g,{scope:r,disabled:o,contentId:(0,d.M)(),open:f,onOpenToggle:n.useCallback(()=>v(e=>!e),[v]),children:(0,p.jsx)(c.WV.div,{"data-state":k(f),"data-disabled":o?"":void 0,...s,ref:t})})});b.displayName=h;var x="CollapsibleTrigger",w=n.forwardRef((e,t)=>{let{__scopeCollapsible:r,...n}=e,i=m(x,r);return(0,p.jsx)(c.WV.button,{type:"button","aria-controls":i.contentId,"aria-expanded":i.open||!1,"data-state":k(i.open),"data-disabled":i.disabled?"":void 0,disabled:i.disabled,...n,ref:t,onClick:(0,u.M)(e.onClick,i.onOpenToggle)})});w.displayName=x;var O="CollapsibleContent",j=n.forwardRef((e,t)=>{let{forceMount:r,...n}=e,i=m(O,e.__scopeCollapsible);return(0,p.jsx)(f.z,{present:r||i.open,children:e=>{let{present:r}=e;return(0,p.jsx)(P,{...n,ref:t,present:r})}})});j.displayName=O;var P=n.forwardRef((e,t)=>{let{__scopeCollapsible:r,present:i,children:a,...u}=e,l=m(O,r),[f,d]=n.useState(i),h=n.useRef(null),v=(0,o.e)(t,h),y=n.useRef(0),g=y.current,b=n.useRef(0),x=b.current,w=l.open||f,j=n.useRef(w),P=n.useRef(void 0);return n.useEffect(()=>{let e=requestAnimationFrame(()=>j.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,s.b)(()=>{let e=h.current;if(e){P.current=P.current||{transitionDuration:e.style.transitionDuration,animationName:e.style.animationName},e.style.transitionDuration="0s",e.style.animationName="none";let t=e.getBoundingClientRect();y.current=t.height,b.current=t.width,j.current||(e.style.transitionDuration=P.current.transitionDuration,e.style.animationName=P.current.animationName),d(i)}},[l.open,i]),(0,p.jsx)(c.WV.div,{"data-state":k(l.open),"data-disabled":l.disabled?"":void 0,id:l.contentId,hidden:!w,...u,ref:v,style:{"--radix-collapsible-content-height":g?"".concat(g,"px"):void 0,"--radix-collapsible-content-width":x?"".concat(x,"px"):void 0,...e.style},children:w&&a})});function k(e){return e?"open":"closed"}var M=r(29114),S="Accordion",A=["Home","End","ArrowDown","ArrowUp","ArrowLeft","ArrowRight"],[E,C,T]=(0,a.B)(S),[D,N]=(0,i.b)(S,[T,y]),_=y(),I=n.forwardRef((e,t)=>{let{type:r,...n}=e;return(0,p.jsx)(E.Provider,{scope:e.__scopeAccordion,children:"multiple"===r?(0,p.jsx)(B,{...n,ref:t}):(0,p.jsx)(W,{...n,ref:t})})});I.displayName=S;var[R,L]=D(S),[z,F]=D(S,{collapsible:!1}),W=n.forwardRef((e,t)=>{let{value:r,defaultValue:i,onValueChange:a=()=>{},collapsible:o=!1,...u}=e,[c,s]=(0,l.T)({prop:r,defaultProp:null!=i?i:"",onChange:a,caller:S});return(0,p.jsx)(R,{scope:e.__scopeAccordion,value:n.useMemo(()=>c?[c]:[],[c]),onItemOpen:s,onItemClose:n.useCallback(()=>o&&s(""),[o,s]),children:(0,p.jsx)(z,{scope:e.__scopeAccordion,collapsible:o,children:(0,p.jsx)(U,{...u,ref:t})})})}),B=n.forwardRef((e,t)=>{let{value:r,defaultValue:i,onValueChange:a=()=>{},...o}=e,[u,c]=(0,l.T)({prop:r,defaultProp:null!=i?i:[],onChange:a,caller:S}),s=n.useCallback(e=>c(function(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return[...t,e]}),[c]),f=n.useCallback(e=>c(function(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return t.filter(t=>t!==e)}),[c]);return(0,p.jsx)(R,{scope:e.__scopeAccordion,value:u,onItemOpen:s,onItemClose:f,children:(0,p.jsx)(z,{scope:e.__scopeAccordion,collapsible:!0,children:(0,p.jsx)(U,{...o,ref:t})})})}),[K,Z]=D(S),U=n.forwardRef((e,t)=>{let{__scopeAccordion:r,disabled:i,dir:a,orientation:l="vertical",...s}=e,f=n.useRef(null),d=(0,o.e)(f,t),h=C(r),v="ltr"===(0,M.gm)(a),y=(0,u.M)(e.onKeyDown,e=>{var t;if(!A.includes(e.key))return;let r=e.target,n=h().filter(e=>{var t;return!(null===(t=e.ref.current)||void 0===t?void 0:t.disabled)}),i=n.findIndex(e=>e.ref.current===r),a=n.length;if(-1===i)return;e.preventDefault();let o=i,u=a-1,c=()=>{(o=i+1)>u&&(o=0)},s=()=>{(o=i-1)<0&&(o=u)};switch(e.key){case"Home":o=0;break;case"End":o=u;break;case"ArrowRight":"horizontal"===l&&(v?c():s());break;case"ArrowDown":"vertical"===l&&c();break;case"ArrowLeft":"horizontal"===l&&(v?s():c());break;case"ArrowUp":"vertical"===l&&s()}null===(t=n[o%a].ref.current)||void 0===t||t.focus()});return(0,p.jsx)(K,{scope:r,disabled:i,direction:a,orientation:l,children:(0,p.jsx)(E.Slot,{scope:r,children:(0,p.jsx)(c.WV.div,{...s,"data-orientation":l,ref:d,onKeyDown:i?void 0:y})})})}),V="AccordionItem",[$,Y]=D(V),H=n.forwardRef((e,t)=>{let{__scopeAccordion:r,value:n,...i}=e,a=Z(V,r),o=L(V,r),u=_(r),l=(0,d.M)(),c=n&&o.value.includes(n)||!1,s=a.disabled||e.disabled;return(0,p.jsx)($,{scope:r,open:c,disabled:s,triggerId:l,children:(0,p.jsx)(b,{"data-orientation":a.orientation,"data-state":et(c),...u,...i,ref:t,disabled:s,open:c,onOpenChange:e=>{e?o.onItemOpen(n):o.onItemClose(n)}})})});H.displayName=V;var q="AccordionHeader",X=n.forwardRef((e,t)=>{let{__scopeAccordion:r,...n}=e,i=Z(S,r),a=Y(q,r);return(0,p.jsx)(c.WV.h3,{"data-orientation":i.orientation,"data-state":et(a.open),"data-disabled":a.disabled?"":void 0,...n,ref:t})});X.displayName=q;var G="AccordionTrigger",Q=n.forwardRef((e,t)=>{let{__scopeAccordion:r,...n}=e,i=Z(S,r),a=Y(G,r),o=F(G,r),u=_(r);return(0,p.jsx)(E.ItemSlot,{scope:r,children:(0,p.jsx)(w,{"aria-disabled":a.open&&!o.collapsible||void 0,"data-orientation":i.orientation,id:a.triggerId,...u,...n,ref:t})})});Q.displayName=G;var J="AccordionContent",ee=n.forwardRef((e,t)=>{let{__scopeAccordion:r,...n}=e,i=Z(S,r),a=Y(J,r),o=_(r);return(0,p.jsx)(j,{role:"region","aria-labelledby":a.triggerId,"data-orientation":i.orientation,...o,...n,ref:t,style:{"--radix-accordion-content-height":"var(--radix-collapsible-content-height)","--radix-accordion-content-width":"var(--radix-collapsible-content-width)",...e.style}})});function et(e){return e?"open":"closed"}ee.displayName=J;var er=I,en=H,ei=X,ea=Q,eo=ee},90277:function(e,t,r){"use strict";r.d(t,{$j:function(){return L},Dx:function(){return z},VY:function(){return I},aU:function(){return R},aV:function(){return _},dk:function(){return F},fC:function(){return T},h_:function(){return N},xz:function(){return D}});var n=r(2265),i=r(73966),a=r(98575),o=r(49027),u=r(6741),l=r(7495),c=r(57437),s="AlertDialog",[f,d]=(0,i.b)(s,[o.p8]),p=(0,o.p8)(),h=e=>{let{__scopeAlertDialog:t,...r}=e,n=p(t);return(0,c.jsx)(o.fC,{...n,...r,modal:!0})};h.displayName=s;var v=n.forwardRef((e,t)=>{let{__scopeAlertDialog:r,...n}=e,i=p(r);return(0,c.jsx)(o.xz,{...i,...n,ref:t})});v.displayName="AlertDialogTrigger";var y=e=>{let{__scopeAlertDialog:t,...r}=e,n=p(t);return(0,c.jsx)(o.h_,{...n,...r})};y.displayName="AlertDialogPortal";var g=n.forwardRef((e,t)=>{let{__scopeAlertDialog:r,...n}=e,i=p(r);return(0,c.jsx)(o.aV,{...i,...n,ref:t})});g.displayName="AlertDialogOverlay";var m="AlertDialogContent",[b,x]=f(m),w=(0,l.sA)("AlertDialogContent"),O=n.forwardRef((e,t)=>{let{__scopeAlertDialog:r,children:i,...l}=e,s=p(r),f=n.useRef(null),d=(0,a.e)(t,f),h=n.useRef(null);return(0,c.jsx)(o.jm,{contentName:m,titleName:j,docsSlug:"alert-dialog",children:(0,c.jsx)(b,{scope:r,cancelRef:h,children:(0,c.jsxs)(o.VY,{role:"alertdialog",...s,...l,ref:d,onOpenAutoFocus:(0,u.M)(l.onOpenAutoFocus,e=>{var t;e.preventDefault(),null===(t=h.current)||void 0===t||t.focus({preventScroll:!0})}),onPointerDownOutside:e=>e.preventDefault(),onInteractOutside:e=>e.preventDefault(),children:[(0,c.jsx)(w,{children:i}),(0,c.jsx)(C,{contentRef:f})]})})})});O.displayName=m;var j="AlertDialogTitle",P=n.forwardRef((e,t)=>{let{__scopeAlertDialog:r,...n}=e,i=p(r);return(0,c.jsx)(o.Dx,{...i,...n,ref:t})});P.displayName=j;var k="AlertDialogDescription",M=n.forwardRef((e,t)=>{let{__scopeAlertDialog:r,...n}=e,i=p(r);return(0,c.jsx)(o.dk,{...i,...n,ref:t})});M.displayName=k;var S=n.forwardRef((e,t)=>{let{__scopeAlertDialog:r,...n}=e,i=p(r);return(0,c.jsx)(o.x8,{...i,...n,ref:t})});S.displayName="AlertDialogAction";var A="AlertDialogCancel",E=n.forwardRef((e,t)=>{let{__scopeAlertDialog:r,...n}=e,{cancelRef:i}=x(A,r),u=p(r),l=(0,a.e)(t,i);return(0,c.jsx)(o.x8,{...u,...n,ref:l})});E.displayName=A;var C=e=>{let{contentRef:t}=e,r="`".concat(m,"` requires a description for the component to be accessible for screen reader users.\n\nYou can add a description to the `").concat(m,"` by passing a `").concat(k,"` component as a child, which also benefits sighted users by adding visible context to the dialog.\n\nAlternatively, you can use your own component as a description by assigning it an `id` and passing the same value to the `aria-describedby` prop in `").concat(m,"`. If the description is confusing or duplicative for sighted users, you can use the `@radix-ui/react-visually-hidden` primitive as a wrapper around your description component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/alert-dialog");return n.useEffect(()=>{var e;document.getElementById(null===(e=t.current)||void 0===e?void 0:e.getAttribute("aria-describedby"))||console.warn(r)},[r,t]),null},T=h,D=v,N=y,_=g,I=O,R=S,L=E,z=P,F=M},61146:function(e,t,r){"use strict";r.d(t,{NY:function(){return P},Ee:function(){return j},fC:function(){return O}});var n=r(2265),i=r(73966),a=r(26606),o=r(61188),u=r(66840),l=r(82558);function c(){return()=>{}}var s=r(57437),f="Avatar",[d,p]=(0,i.b)(f),[h,v]=d(f),y=n.forwardRef((e,t)=>{let{__scopeAvatar:r,...i}=e,[a,o]=n.useState("idle");return(0,s.jsx)(h,{scope:r,imageLoadingStatus:a,onImageLoadingStatusChange:o,children:(0,s.jsx)(u.WV.span,{...i,ref:t})})});y.displayName=f;var g="AvatarImage",m=n.forwardRef((e,t)=>{let{__scopeAvatar:r,src:i,onLoadingStatusChange:f=()=>{},...d}=e,p=v(g,r),h=function(e,t){let{referrerPolicy:r,crossOrigin:i}=t,a=(0,l.useSyncExternalStore)(c,()=>!0,()=>!1),u=n.useRef(null),s=a?(u.current||(u.current=new window.Image),u.current):null,[f,d]=n.useState(()=>w(s,e));return(0,o.b)(()=>{d(w(s,e))},[s,e]),(0,o.b)(()=>{let e=e=>()=>{d(e)};if(!s)return;let t=e("loaded"),n=e("error");return s.addEventListener("load",t),s.addEventListener("error",n),r&&(s.referrerPolicy=r),"string"==typeof i&&(s.crossOrigin=i),()=>{s.removeEventListener("load",t),s.removeEventListener("error",n)}},[s,i,r]),f}(i,d),y=(0,a.W)(e=>{f(e),p.onImageLoadingStatusChange(e)});return(0,o.b)(()=>{"idle"!==h&&y(h)},[h,y]),"loaded"===h?(0,s.jsx)(u.WV.img,{...d,ref:t,src:i}):null});m.displayName=g;var b="AvatarFallback",x=n.forwardRef((e,t)=>{let{__scopeAvatar:r,delayMs:i,...a}=e,o=v(b,r),[l,c]=n.useState(void 0===i);return n.useEffect(()=>{if(void 0!==i){let e=window.setTimeout(()=>c(!0),i);return()=>window.clearTimeout(e)}},[i]),l&&"loaded"!==o.imageLoadingStatus?(0,s.jsx)(u.WV.span,{...a,ref:t}):null});function w(e,t){return e?t?(e.src!==t&&(e.src=t),e.complete&&e.naturalWidth>0?"loaded":"loading"):"error":"idle"}x.displayName=b;var O=y,j=m,P=x},49027:function(e,t,r){"use strict";r.d(t,{Dx:function(){return en},VY:function(){return er},aV:function(){return et},dk:function(){return ei},fC:function(){return Q},h_:function(){return ee},jm:function(){return H},p8:function(){return w},x8:function(){return ea},xz:function(){return J}});var n=r(2265),i=r(6741),a=r(98575),o=r(73966),u=r(99255),l=r(80886),c=r(15278),s=r(99103),f=r(83832),d=r(71599),p=r(66840),h=r(86097),v=r(87922),y=r(5478),g=r(7495),m=r(57437),b="Dialog",[x,w]=(0,o.b)(b),[O,j]=x(b),P=e=>{let{__scopeDialog:t,children:r,open:i,defaultOpen:a,onOpenChange:o,modal:c=!0}=e,s=n.useRef(null),f=n.useRef(null),[d,p]=(0,l.T)({prop:i,defaultProp:null!=a&&a,onChange:o,caller:b});return(0,m.jsx)(O,{scope:t,triggerRef:s,contentRef:f,contentId:(0,u.M)(),titleId:(0,u.M)(),descriptionId:(0,u.M)(),open:d,onOpenChange:p,onOpenToggle:n.useCallback(()=>p(e=>!e),[p]),modal:c,children:r})};P.displayName=b;var k="DialogTrigger",M=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,o=j(k,r),u=(0,a.e)(t,o.triggerRef);return(0,m.jsx)(p.WV.button,{type:"button","aria-haspopup":"dialog","aria-expanded":o.open,"aria-controls":o.contentId,"data-state":$(o.open),...n,ref:u,onClick:(0,i.M)(e.onClick,o.onOpenToggle)})});M.displayName=k;var S="DialogPortal",[A,E]=x(S,{forceMount:void 0}),C=e=>{let{__scopeDialog:t,forceMount:r,children:i,container:a}=e,o=j(S,t);return(0,m.jsx)(A,{scope:t,forceMount:r,children:n.Children.map(i,e=>(0,m.jsx)(d.z,{present:r||o.open,children:(0,m.jsx)(f.h,{asChild:!0,container:a,children:e})}))})};C.displayName=S;var T="DialogOverlay",D=n.forwardRef((e,t)=>{let r=E(T,e.__scopeDialog),{forceMount:n=r.forceMount,...i}=e,a=j(T,e.__scopeDialog);return a.modal?(0,m.jsx)(d.z,{present:n||a.open,children:(0,m.jsx)(_,{...i,ref:t})}):null});D.displayName=T;var N=(0,g.Z8)("DialogOverlay.RemoveScroll"),_=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,i=j(T,r);return(0,m.jsx)(v.Z,{as:N,allowPinchZoom:!0,shards:[i.contentRef],children:(0,m.jsx)(p.WV.div,{"data-state":$(i.open),...n,ref:t,style:{pointerEvents:"auto",...n.style}})})}),I="DialogContent",R=n.forwardRef((e,t)=>{let r=E(I,e.__scopeDialog),{forceMount:n=r.forceMount,...i}=e,a=j(I,e.__scopeDialog);return(0,m.jsx)(d.z,{present:n||a.open,children:a.modal?(0,m.jsx)(L,{...i,ref:t}):(0,m.jsx)(z,{...i,ref:t})})});R.displayName=I;var L=n.forwardRef((e,t)=>{let r=j(I,e.__scopeDialog),o=n.useRef(null),u=(0,a.e)(t,r.contentRef,o);return n.useEffect(()=>{let e=o.current;if(e)return(0,y.Ry)(e)},[]),(0,m.jsx)(F,{...e,ref:u,trapFocus:r.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,i.M)(e.onCloseAutoFocus,e=>{var t;e.preventDefault(),null===(t=r.triggerRef.current)||void 0===t||t.focus()}),onPointerDownOutside:(0,i.M)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,r=0===t.button&&!0===t.ctrlKey;(2===t.button||r)&&e.preventDefault()}),onFocusOutside:(0,i.M)(e.onFocusOutside,e=>e.preventDefault())})}),z=n.forwardRef((e,t)=>{let r=j(I,e.__scopeDialog),i=n.useRef(!1),a=n.useRef(!1);return(0,m.jsx)(F,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{var n,o;null===(n=e.onCloseAutoFocus)||void 0===n||n.call(e,t),t.defaultPrevented||(i.current||null===(o=r.triggerRef.current)||void 0===o||o.focus(),t.preventDefault()),i.current=!1,a.current=!1},onInteractOutside:t=>{var n,o;null===(n=e.onInteractOutside)||void 0===n||n.call(e,t),t.defaultPrevented||(i.current=!0,"pointerdown"!==t.detail.originalEvent.type||(a.current=!0));let u=t.target;(null===(o=r.triggerRef.current)||void 0===o?void 0:o.contains(u))&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&a.current&&t.preventDefault()}})}),F=n.forwardRef((e,t)=>{let{__scopeDialog:r,trapFocus:i,onOpenAutoFocus:o,onCloseAutoFocus:u,...l}=e,f=j(I,r),d=n.useRef(null),p=(0,a.e)(t,d);return(0,h.EW)(),(0,m.jsxs)(m.Fragment,{children:[(0,m.jsx)(s.M,{asChild:!0,loop:!0,trapped:i,onMountAutoFocus:o,onUnmountAutoFocus:u,children:(0,m.jsx)(c.XB,{role:"dialog",id:f.contentId,"aria-describedby":f.descriptionId,"aria-labelledby":f.titleId,"data-state":$(f.open),...l,ref:p,onDismiss:()=>f.onOpenChange(!1)})}),(0,m.jsxs)(m.Fragment,{children:[(0,m.jsx)(X,{titleId:f.titleId}),(0,m.jsx)(G,{contentRef:d,descriptionId:f.descriptionId})]})]})}),W="DialogTitle",B=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,i=j(W,r);return(0,m.jsx)(p.WV.h2,{id:i.titleId,...n,ref:t})});B.displayName=W;var K="DialogDescription",Z=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,i=j(K,r);return(0,m.jsx)(p.WV.p,{id:i.descriptionId,...n,ref:t})});Z.displayName=K;var U="DialogClose",V=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,a=j(U,r);return(0,m.jsx)(p.WV.button,{type:"button",...n,ref:t,onClick:(0,i.M)(e.onClick,()=>a.onOpenChange(!1))})});function $(e){return e?"open":"closed"}V.displayName=U;var Y="DialogTitleWarning",[H,q]=(0,o.k)(Y,{contentName:I,titleName:W,docsSlug:"dialog"}),X=e=>{let{titleId:t}=e,r=q(Y),i="`".concat(r.contentName,"` requires a `").concat(r.titleName,"` for the component to be accessible for screen reader users.\n\nIf you want to hide the `").concat(r.titleName,"`, you can wrap it with our VisuallyHidden component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/").concat(r.docsSlug);return n.useEffect(()=>{t&&!document.getElementById(t)&&console.error(i)},[i,t]),null},G=e=>{let{contentRef:t,descriptionId:r}=e,i=q("DialogDescriptionWarning"),a="Warning: Missing `Description` or `aria-describedby={undefined}` for {".concat(i.contentName,"}.");return n.useEffect(()=>{var e;let n=null===(e=t.current)||void 0===e?void 0:e.getAttribute("aria-describedby");r&&n&&!document.getElementById(r)&&console.warn(a)},[a,t,r]),null},Q=P,J=M,ee=C,et=D,er=R,en=B,ei=Z,ea=V},29114:function(e,t,r){"use strict";r.d(t,{gm:function(){return a}});var n=r(2265);r(57437);var i=n.createContext(void 0);function a(e){let t=n.useContext(i);return e||t||"ltr"}},70085:function(e,t,r){"use strict";r.d(t,{oC:function(){return e9},VY:function(){return e6},ZA:function(){return e5},ck:function(){return e4},wU:function(){return te},__:function(){return e3},Uv:function(){return e2},Ee:function(){return e7},Rk:function(){return e8},fC:function(){return e0},Z0:function(){return tt},Tr:function(){return tr},tu:function(){return ti},fF:function(){return tn},xz:function(){return e1}});var n=r(2265),i=r(6741),a=r(98575),o=r(73966),u=r(80886),l=r(66840),c=r(58068),s=r(29114),f=r(15278),d=r(86097),p=r(99103),h=r(99255),v=r(96806),y=r(83832),g=r(71599),m=r(1353),b=r(7495),x=r(26606),w=r(5478),O=r(87922),j=r(57437),P=["Enter"," "],k=["ArrowUp","PageDown","End"],M=["ArrowDown","PageUp","Home",...k],S={ltr:[...P,"ArrowRight"],rtl:[...P,"ArrowLeft"]},A={ltr:["ArrowLeft"],rtl:["ArrowRight"]},E="Menu",[C,T,D]=(0,c.B)(E),[N,_]=(0,o.b)(E,[D,v.D7,m.Pc]),I=(0,v.D7)(),R=(0,m.Pc)(),[L,z]=N(E),[F,W]=N(E),B=e=>{let{__scopeMenu:t,open:r=!1,children:i,dir:a,onOpenChange:o,modal:u=!0}=e,l=I(t),[c,f]=n.useState(null),d=n.useRef(!1),p=(0,x.W)(o),h=(0,s.gm)(a);return n.useEffect(()=>{let e=()=>{d.current=!0,document.addEventListener("pointerdown",t,{capture:!0,once:!0}),document.addEventListener("pointermove",t,{capture:!0,once:!0})},t=()=>d.current=!1;return document.addEventListener("keydown",e,{capture:!0}),()=>{document.removeEventListener("keydown",e,{capture:!0}),document.removeEventListener("pointerdown",t,{capture:!0}),document.removeEventListener("pointermove",t,{capture:!0})}},[]),(0,j.jsx)(v.fC,{...l,children:(0,j.jsx)(L,{scope:t,open:r,onOpenChange:p,content:c,onContentChange:f,children:(0,j.jsx)(F,{scope:t,onClose:n.useCallback(()=>p(!1),[p]),isUsingKeyboardRef:d,dir:h,modal:u,children:i})})})};B.displayName=E;var K=n.forwardRef((e,t)=>{let{__scopeMenu:r,...n}=e,i=I(r);return(0,j.jsx)(v.ee,{...i,...n,ref:t})});K.displayName="MenuAnchor";var Z="MenuPortal",[U,V]=N(Z,{forceMount:void 0}),$=e=>{let{__scopeMenu:t,forceMount:r,children:n,container:i}=e,a=z(Z,t);return(0,j.jsx)(U,{scope:t,forceMount:r,children:(0,j.jsx)(g.z,{present:r||a.open,children:(0,j.jsx)(y.h,{asChild:!0,container:i,children:n})})})};$.displayName=Z;var Y="MenuContent",[H,q]=N(Y),X=n.forwardRef((e,t)=>{let r=V(Y,e.__scopeMenu),{forceMount:n=r.forceMount,...i}=e,a=z(Y,e.__scopeMenu),o=W(Y,e.__scopeMenu);return(0,j.jsx)(C.Provider,{scope:e.__scopeMenu,children:(0,j.jsx)(g.z,{present:n||a.open,children:(0,j.jsx)(C.Slot,{scope:e.__scopeMenu,children:o.modal?(0,j.jsx)(G,{...i,ref:t}):(0,j.jsx)(Q,{...i,ref:t})})})})}),G=n.forwardRef((e,t)=>{let r=z(Y,e.__scopeMenu),o=n.useRef(null),u=(0,a.e)(t,o);return n.useEffect(()=>{let e=o.current;if(e)return(0,w.Ry)(e)},[]),(0,j.jsx)(ee,{...e,ref:u,trapFocus:r.open,disableOutsidePointerEvents:r.open,disableOutsideScroll:!0,onFocusOutside:(0,i.M)(e.onFocusOutside,e=>e.preventDefault(),{checkForDefaultPrevented:!1}),onDismiss:()=>r.onOpenChange(!1)})}),Q=n.forwardRef((e,t)=>{let r=z(Y,e.__scopeMenu);return(0,j.jsx)(ee,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,disableOutsideScroll:!1,onDismiss:()=>r.onOpenChange(!1)})}),J=(0,b.Z8)("MenuContent.ScrollLock"),ee=n.forwardRef((e,t)=>{let{__scopeMenu:r,loop:o=!1,trapFocus:u,onOpenAutoFocus:l,onCloseAutoFocus:c,disableOutsidePointerEvents:s,onEntryFocus:h,onEscapeKeyDown:y,onPointerDownOutside:g,onFocusOutside:b,onInteractOutside:x,onDismiss:w,disableOutsideScroll:P,...S}=e,A=z(Y,r),E=W(Y,r),C=I(r),D=R(r),N=T(r),[_,L]=n.useState(null),F=n.useRef(null),B=(0,a.e)(t,F,A.onContentChange),K=n.useRef(0),Z=n.useRef(""),U=n.useRef(0),V=n.useRef(null),$=n.useRef("right"),q=n.useRef(0),X=P?O.Z:n.Fragment,G=e=>{var t,r;let n=Z.current+e,i=N().filter(e=>!e.disabled),a=document.activeElement,o=null===(t=i.find(e=>e.ref.current===a))||void 0===t?void 0:t.textValue,u=function(e,t,r){var n;let i=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,a=(n=Math.max(r?e.indexOf(r):-1,0),e.map((t,r)=>e[(n+r)%e.length]));1===i.length&&(a=a.filter(e=>e!==r));let o=a.find(e=>e.toLowerCase().startsWith(i.toLowerCase()));return o!==r?o:void 0}(i.map(e=>e.textValue),n,o),l=null===(r=i.find(e=>e.textValue===u))||void 0===r?void 0:r.ref.current;!function e(t){Z.current=t,window.clearTimeout(K.current),""!==t&&(K.current=window.setTimeout(()=>e(""),1e3))}(n),l&&setTimeout(()=>l.focus())};n.useEffect(()=>()=>window.clearTimeout(K.current),[]),(0,d.EW)();let Q=n.useCallback(e=>{var t,r,n;return $.current===(null===(t=V.current)||void 0===t?void 0:t.side)&&!!(n=null===(r=V.current)||void 0===r?void 0:r.area)&&function(e,t){let{x:r,y:n}=e,i=!1;for(let e=0,a=t.length-1;e<t.length;a=e++){let o=t[e],u=t[a],l=o.x,c=o.y,s=u.x,f=u.y;c>n!=f>n&&r<(s-l)*(n-c)/(f-c)+l&&(i=!i)}return i}({x:e.clientX,y:e.clientY},n)},[]);return(0,j.jsx)(H,{scope:r,searchRef:Z,onItemEnter:n.useCallback(e=>{Q(e)&&e.preventDefault()},[Q]),onItemLeave:n.useCallback(e=>{var t;Q(e)||(null===(t=F.current)||void 0===t||t.focus(),L(null))},[Q]),onTriggerLeave:n.useCallback(e=>{Q(e)&&e.preventDefault()},[Q]),pointerGraceTimerRef:U,onPointerGraceIntentChange:n.useCallback(e=>{V.current=e},[]),children:(0,j.jsx)(X,{...P?{as:J,allowPinchZoom:!0}:void 0,children:(0,j.jsx)(p.M,{asChild:!0,trapped:u,onMountAutoFocus:(0,i.M)(l,e=>{var t;e.preventDefault(),null===(t=F.current)||void 0===t||t.focus({preventScroll:!0})}),onUnmountAutoFocus:c,children:(0,j.jsx)(f.XB,{asChild:!0,disableOutsidePointerEvents:s,onEscapeKeyDown:y,onPointerDownOutside:g,onFocusOutside:b,onInteractOutside:x,onDismiss:w,children:(0,j.jsx)(m.fC,{asChild:!0,...D,dir:E.dir,orientation:"vertical",loop:o,currentTabStopId:_,onCurrentTabStopIdChange:L,onEntryFocus:(0,i.M)(h,e=>{E.isUsingKeyboardRef.current||e.preventDefault()}),preventScrollOnEntryFocus:!0,children:(0,j.jsx)(v.VY,{role:"menu","aria-orientation":"vertical","data-state":eA(A.open),"data-radix-menu-content":"",dir:E.dir,...C,...S,ref:B,style:{outline:"none",...S.style},onKeyDown:(0,i.M)(S.onKeyDown,e=>{let t=e.target.closest("[data-radix-menu-content]")===e.currentTarget,r=e.ctrlKey||e.altKey||e.metaKey,n=1===e.key.length;t&&("Tab"===e.key&&e.preventDefault(),!r&&n&&G(e.key));let i=F.current;if(e.target!==i||!M.includes(e.key))return;e.preventDefault();let a=N().filter(e=>!e.disabled).map(e=>e.ref.current);k.includes(e.key)&&a.reverse(),function(e){let t=document.activeElement;for(let r of e)if(r===t||(r.focus(),document.activeElement!==t))return}(a)}),onBlur:(0,i.M)(e.onBlur,e=>{e.currentTarget.contains(e.target)||(window.clearTimeout(K.current),Z.current="")}),onPointerMove:(0,i.M)(e.onPointerMove,eT(e=>{let t=e.target,r=q.current!==e.clientX;if(e.currentTarget.contains(t)&&r){let t=e.clientX>q.current?"right":"left";$.current=t,q.current=e.clientX}}))})})})})})})});X.displayName=Y;var et=n.forwardRef((e,t)=>{let{__scopeMenu:r,...n}=e;return(0,j.jsx)(l.WV.div,{role:"group",...n,ref:t})});et.displayName="MenuGroup";var er=n.forwardRef((e,t)=>{let{__scopeMenu:r,...n}=e;return(0,j.jsx)(l.WV.div,{...n,ref:t})});er.displayName="MenuLabel";var en="MenuItem",ei="menu.itemSelect",ea=n.forwardRef((e,t)=>{let{disabled:r=!1,onSelect:o,...u}=e,c=n.useRef(null),s=W(en,e.__scopeMenu),f=q(en,e.__scopeMenu),d=(0,a.e)(t,c),p=n.useRef(!1);return(0,j.jsx)(eo,{...u,ref:d,disabled:r,onClick:(0,i.M)(e.onClick,()=>{let e=c.current;if(!r&&e){let t=new CustomEvent(ei,{bubbles:!0,cancelable:!0});e.addEventListener(ei,e=>null==o?void 0:o(e),{once:!0}),(0,l.jH)(e,t),t.defaultPrevented?p.current=!1:s.onClose()}}),onPointerDown:t=>{var r;null===(r=e.onPointerDown)||void 0===r||r.call(e,t),p.current=!0},onPointerUp:(0,i.M)(e.onPointerUp,e=>{var t;p.current||null===(t=e.currentTarget)||void 0===t||t.click()}),onKeyDown:(0,i.M)(e.onKeyDown,e=>{let t=""!==f.searchRef.current;!r&&(!t||" "!==e.key)&&P.includes(e.key)&&(e.currentTarget.click(),e.preventDefault())})})});ea.displayName=en;var eo=n.forwardRef((e,t)=>{let{__scopeMenu:r,disabled:o=!1,textValue:u,...c}=e,s=q(en,r),f=R(r),d=n.useRef(null),p=(0,a.e)(t,d),[h,v]=n.useState(!1),[y,g]=n.useState("");return n.useEffect(()=>{let e=d.current;if(e){var t;g((null!==(t=e.textContent)&&void 0!==t?t:"").trim())}},[c.children]),(0,j.jsx)(C.ItemSlot,{scope:r,disabled:o,textValue:null!=u?u:y,children:(0,j.jsx)(m.ck,{asChild:!0,...f,focusable:!o,children:(0,j.jsx)(l.WV.div,{role:"menuitem","data-highlighted":h?"":void 0,"aria-disabled":o||void 0,"data-disabled":o?"":void 0,...c,ref:p,onPointerMove:(0,i.M)(e.onPointerMove,eT(e=>{o?s.onItemLeave(e):(s.onItemEnter(e),e.defaultPrevented||e.currentTarget.focus({preventScroll:!0}))})),onPointerLeave:(0,i.M)(e.onPointerLeave,eT(e=>s.onItemLeave(e))),onFocus:(0,i.M)(e.onFocus,()=>v(!0)),onBlur:(0,i.M)(e.onBlur,()=>v(!1))})})})}),eu=n.forwardRef((e,t)=>{let{checked:r=!1,onCheckedChange:n,...a}=e;return(0,j.jsx)(ev,{scope:e.__scopeMenu,checked:r,children:(0,j.jsx)(ea,{role:"menuitemcheckbox","aria-checked":eE(r)?"mixed":r,...a,ref:t,"data-state":eC(r),onSelect:(0,i.M)(a.onSelect,()=>null==n?void 0:n(!!eE(r)||!r),{checkForDefaultPrevented:!1})})})});eu.displayName="MenuCheckboxItem";var el="MenuRadioGroup",[ec,es]=N(el,{value:void 0,onValueChange:()=>{}}),ef=n.forwardRef((e,t)=>{let{value:r,onValueChange:n,...i}=e,a=(0,x.W)(n);return(0,j.jsx)(ec,{scope:e.__scopeMenu,value:r,onValueChange:a,children:(0,j.jsx)(et,{...i,ref:t})})});ef.displayName=el;var ed="MenuRadioItem",ep=n.forwardRef((e,t)=>{let{value:r,...n}=e,a=es(ed,e.__scopeMenu),o=r===a.value;return(0,j.jsx)(ev,{scope:e.__scopeMenu,checked:o,children:(0,j.jsx)(ea,{role:"menuitemradio","aria-checked":o,...n,ref:t,"data-state":eC(o),onSelect:(0,i.M)(n.onSelect,()=>{var e;return null===(e=a.onValueChange)||void 0===e?void 0:e.call(a,r)},{checkForDefaultPrevented:!1})})})});ep.displayName=ed;var eh="MenuItemIndicator",[ev,ey]=N(eh,{checked:!1}),eg=n.forwardRef((e,t)=>{let{__scopeMenu:r,forceMount:n,...i}=e,a=ey(eh,r);return(0,j.jsx)(g.z,{present:n||eE(a.checked)||!0===a.checked,children:(0,j.jsx)(l.WV.span,{...i,ref:t,"data-state":eC(a.checked)})})});eg.displayName=eh;var em=n.forwardRef((e,t)=>{let{__scopeMenu:r,...n}=e;return(0,j.jsx)(l.WV.div,{role:"separator","aria-orientation":"horizontal",...n,ref:t})});em.displayName="MenuSeparator";var eb=n.forwardRef((e,t)=>{let{__scopeMenu:r,...n}=e,i=I(r);return(0,j.jsx)(v.Eh,{...i,...n,ref:t})});eb.displayName="MenuArrow";var ex="MenuSub",[ew,eO]=N(ex),ej=e=>{let{__scopeMenu:t,children:r,open:i=!1,onOpenChange:a}=e,o=z(ex,t),u=I(t),[l,c]=n.useState(null),[s,f]=n.useState(null),d=(0,x.W)(a);return n.useEffect(()=>(!1===o.open&&d(!1),()=>d(!1)),[o.open,d]),(0,j.jsx)(v.fC,{...u,children:(0,j.jsx)(L,{scope:t,open:i,onOpenChange:d,content:s,onContentChange:f,children:(0,j.jsx)(ew,{scope:t,contentId:(0,h.M)(),triggerId:(0,h.M)(),trigger:l,onTriggerChange:c,children:r})})})};ej.displayName=ex;var eP="MenuSubTrigger",ek=n.forwardRef((e,t)=>{let r=z(eP,e.__scopeMenu),o=W(eP,e.__scopeMenu),u=eO(eP,e.__scopeMenu),l=q(eP,e.__scopeMenu),c=n.useRef(null),{pointerGraceTimerRef:s,onPointerGraceIntentChange:f}=l,d={__scopeMenu:e.__scopeMenu},p=n.useCallback(()=>{c.current&&window.clearTimeout(c.current),c.current=null},[]);return n.useEffect(()=>p,[p]),n.useEffect(()=>{let e=s.current;return()=>{window.clearTimeout(e),f(null)}},[s,f]),(0,j.jsx)(K,{asChild:!0,...d,children:(0,j.jsx)(eo,{id:u.triggerId,"aria-haspopup":"menu","aria-expanded":r.open,"aria-controls":u.contentId,"data-state":eA(r.open),...e,ref:(0,a.F)(t,u.onTriggerChange),onClick:t=>{var n;null===(n=e.onClick)||void 0===n||n.call(e,t),e.disabled||t.defaultPrevented||(t.currentTarget.focus(),r.open||r.onOpenChange(!0))},onPointerMove:(0,i.M)(e.onPointerMove,eT(t=>{l.onItemEnter(t),t.defaultPrevented||e.disabled||r.open||c.current||(l.onPointerGraceIntentChange(null),c.current=window.setTimeout(()=>{r.onOpenChange(!0),p()},100))})),onPointerLeave:(0,i.M)(e.onPointerLeave,eT(e=>{var t,n;p();let i=null===(t=r.content)||void 0===t?void 0:t.getBoundingClientRect();if(i){let t=null===(n=r.content)||void 0===n?void 0:n.dataset.side,a="right"===t,o=i[a?"left":"right"],u=i[a?"right":"left"];l.onPointerGraceIntentChange({area:[{x:e.clientX+(a?-5:5),y:e.clientY},{x:o,y:i.top},{x:u,y:i.top},{x:u,y:i.bottom},{x:o,y:i.bottom}],side:t}),window.clearTimeout(s.current),s.current=window.setTimeout(()=>l.onPointerGraceIntentChange(null),300)}else{if(l.onTriggerLeave(e),e.defaultPrevented)return;l.onPointerGraceIntentChange(null)}})),onKeyDown:(0,i.M)(e.onKeyDown,t=>{let n=""!==l.searchRef.current;if(!e.disabled&&(!n||" "!==t.key)&&S[o.dir].includes(t.key)){var i;r.onOpenChange(!0),null===(i=r.content)||void 0===i||i.focus(),t.preventDefault()}})})})});ek.displayName=eP;var eM="MenuSubContent",eS=n.forwardRef((e,t)=>{let r=V(Y,e.__scopeMenu),{forceMount:o=r.forceMount,...u}=e,l=z(Y,e.__scopeMenu),c=W(Y,e.__scopeMenu),s=eO(eM,e.__scopeMenu),f=n.useRef(null),d=(0,a.e)(t,f);return(0,j.jsx)(C.Provider,{scope:e.__scopeMenu,children:(0,j.jsx)(g.z,{present:o||l.open,children:(0,j.jsx)(C.Slot,{scope:e.__scopeMenu,children:(0,j.jsx)(ee,{id:s.contentId,"aria-labelledby":s.triggerId,...u,ref:d,align:"start",side:"rtl"===c.dir?"left":"right",disableOutsidePointerEvents:!1,disableOutsideScroll:!1,trapFocus:!1,onOpenAutoFocus:e=>{var t;c.isUsingKeyboardRef.current&&(null===(t=f.current)||void 0===t||t.focus()),e.preventDefault()},onCloseAutoFocus:e=>e.preventDefault(),onFocusOutside:(0,i.M)(e.onFocusOutside,e=>{e.target!==s.trigger&&l.onOpenChange(!1)}),onEscapeKeyDown:(0,i.M)(e.onEscapeKeyDown,e=>{c.onClose(),e.preventDefault()}),onKeyDown:(0,i.M)(e.onKeyDown,e=>{let t=e.currentTarget.contains(e.target),r=A[c.dir].includes(e.key);if(t&&r){var n;l.onOpenChange(!1),null===(n=s.trigger)||void 0===n||n.focus(),e.preventDefault()}})})})})})});function eA(e){return e?"open":"closed"}function eE(e){return"indeterminate"===e}function eC(e){return eE(e)?"indeterminate":e?"checked":"unchecked"}function eT(e){return t=>"mouse"===t.pointerType?e(t):void 0}eS.displayName=eM;var eD="DropdownMenu",[eN,e_]=(0,o.b)(eD,[_]),eI=_(),[eR,eL]=eN(eD),ez=e=>{let{__scopeDropdownMenu:t,children:r,dir:i,open:a,defaultOpen:o,onOpenChange:l,modal:c=!0}=e,s=eI(t),f=n.useRef(null),[d,p]=(0,u.T)({prop:a,defaultProp:null!=o&&o,onChange:l,caller:eD});return(0,j.jsx)(eR,{scope:t,triggerId:(0,h.M)(),triggerRef:f,contentId:(0,h.M)(),open:d,onOpenChange:p,onOpenToggle:n.useCallback(()=>p(e=>!e),[p]),modal:c,children:(0,j.jsx)(B,{...s,open:d,onOpenChange:p,dir:i,modal:c,children:r})})};ez.displayName=eD;var eF="DropdownMenuTrigger",eW=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,disabled:n=!1,...o}=e,u=eL(eF,r),c=eI(r);return(0,j.jsx)(K,{asChild:!0,...c,children:(0,j.jsx)(l.WV.button,{type:"button",id:u.triggerId,"aria-haspopup":"menu","aria-expanded":u.open,"aria-controls":u.open?u.contentId:void 0,"data-state":u.open?"open":"closed","data-disabled":n?"":void 0,disabled:n,...o,ref:(0,a.F)(t,u.triggerRef),onPointerDown:(0,i.M)(e.onPointerDown,e=>{n||0!==e.button||!1!==e.ctrlKey||(u.onOpenToggle(),u.open||e.preventDefault())}),onKeyDown:(0,i.M)(e.onKeyDown,e=>{!n&&(["Enter"," "].includes(e.key)&&u.onOpenToggle(),"ArrowDown"===e.key&&u.onOpenChange(!0),["Enter"," ","ArrowDown"].includes(e.key)&&e.preventDefault())})})})});eW.displayName=eF;var eB=e=>{let{__scopeDropdownMenu:t,...r}=e,n=eI(t);return(0,j.jsx)($,{...n,...r})};eB.displayName="DropdownMenuPortal";var eK="DropdownMenuContent",eZ=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...a}=e,o=eL(eK,r),u=eI(r),l=n.useRef(!1);return(0,j.jsx)(X,{id:o.contentId,"aria-labelledby":o.triggerId,...u,...a,ref:t,onCloseAutoFocus:(0,i.M)(e.onCloseAutoFocus,e=>{var t;l.current||null===(t=o.triggerRef.current)||void 0===t||t.focus(),l.current=!1,e.preventDefault()}),onInteractOutside:(0,i.M)(e.onInteractOutside,e=>{let t=e.detail.originalEvent,r=0===t.button&&!0===t.ctrlKey,n=2===t.button||r;(!o.modal||n)&&(l.current=!0)}),style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});eZ.displayName=eK;var eU=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,i=eI(r);return(0,j.jsx)(et,{...i,...n,ref:t})});eU.displayName="DropdownMenuGroup";var eV=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,i=eI(r);return(0,j.jsx)(er,{...i,...n,ref:t})});eV.displayName="DropdownMenuLabel";var e$=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,i=eI(r);return(0,j.jsx)(ea,{...i,...n,ref:t})});e$.displayName="DropdownMenuItem";var eY=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,i=eI(r);return(0,j.jsx)(eu,{...i,...n,ref:t})});eY.displayName="DropdownMenuCheckboxItem";var eH=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,i=eI(r);return(0,j.jsx)(ef,{...i,...n,ref:t})});eH.displayName="DropdownMenuRadioGroup";var eq=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,i=eI(r);return(0,j.jsx)(ep,{...i,...n,ref:t})});eq.displayName="DropdownMenuRadioItem";var eX=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,i=eI(r);return(0,j.jsx)(eg,{...i,...n,ref:t})});eX.displayName="DropdownMenuItemIndicator";var eG=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,i=eI(r);return(0,j.jsx)(em,{...i,...n,ref:t})});eG.displayName="DropdownMenuSeparator",n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,i=eI(r);return(0,j.jsx)(eb,{...i,...n,ref:t})}).displayName="DropdownMenuArrow";var eQ=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,i=eI(r);return(0,j.jsx)(ek,{...i,...n,ref:t})});eQ.displayName="DropdownMenuSubTrigger";var eJ=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,i=eI(r);return(0,j.jsx)(eS,{...i,...n,ref:t,style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});eJ.displayName="DropdownMenuSubContent";var e0=ez,e1=eW,e2=eB,e6=eZ,e5=eU,e3=eV,e4=e$,e9=eY,e7=eH,e8=eq,te=eX,tt=eG,tr=e=>{let{__scopeDropdownMenu:t,children:r,open:n,onOpenChange:i,defaultOpen:a}=e,o=eI(t),[l,c]=(0,u.T)({prop:n,defaultProp:null!=a&&a,onChange:i,caller:"DropdownMenuSub"});return(0,j.jsx)(ej,{...o,open:l,onOpenChange:c,children:r})},tn=eQ,ti=eJ},86097:function(e,t,r){"use strict";r.d(t,{EW:function(){return a}});var n=r(2265),i=0;function a(){n.useEffect(()=>{var e,t;let r=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",null!==(e=r[0])&&void 0!==e?e:o()),document.body.insertAdjacentElement("beforeend",null!==(t=r[1])&&void 0!==t?t:o()),i++,()=>{1===i&&document.querySelectorAll("[data-radix-focus-guard]").forEach(e=>e.remove()),i--}},[])}function o(){let e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}},99103:function(e,t,r){"use strict";let n;r.d(t,{M:function(){return d}});var i=r(2265),a=r(98575),o=r(66840),u=r(26606),l=r(57437),c="focusScope.autoFocusOnMount",s="focusScope.autoFocusOnUnmount",f={bubbles:!1,cancelable:!0},d=i.forwardRef((e,t)=>{let{loop:r=!1,trapped:n=!1,onMountAutoFocus:d,onUnmountAutoFocus:g,...m}=e,[b,x]=i.useState(null),w=(0,u.W)(d),O=(0,u.W)(g),j=i.useRef(null),P=(0,a.e)(t,e=>x(e)),k=i.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;i.useEffect(()=>{if(n){let e=function(e){if(k.paused||!b)return;let t=e.target;b.contains(t)?j.current=t:v(j.current,{select:!0})},t=function(e){if(k.paused||!b)return;let t=e.relatedTarget;null===t||b.contains(t)||v(j.current,{select:!0})};document.addEventListener("focusin",e),document.addEventListener("focusout",t);let r=new MutationObserver(function(e){if(document.activeElement===document.body)for(let t of e)t.removedNodes.length>0&&v(b)});return b&&r.observe(b,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),r.disconnect()}}},[n,b,k.paused]),i.useEffect(()=>{if(b){y.add(k);let e=document.activeElement;if(!b.contains(e)){let t=new CustomEvent(c,f);b.addEventListener(c,w),b.dispatchEvent(t),t.defaultPrevented||(function(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=document.activeElement;for(let n of e)if(v(n,{select:t}),document.activeElement!==r)return}(p(b).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&v(b))}return()=>{b.removeEventListener(c,w),setTimeout(()=>{let t=new CustomEvent(s,f);b.addEventListener(s,O),b.dispatchEvent(t),t.defaultPrevented||v(null!=e?e:document.body,{select:!0}),b.removeEventListener(s,O),y.remove(k)},0)}}},[b,w,O,k]);let M=i.useCallback(e=>{if(!r&&!n||k.paused)return;let t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,i=document.activeElement;if(t&&i){let t=e.currentTarget,[n,a]=function(e){let t=p(e);return[h(t,e),h(t.reverse(),e)]}(t);n&&a?e.shiftKey||i!==a?e.shiftKey&&i===n&&(e.preventDefault(),r&&v(a,{select:!0})):(e.preventDefault(),r&&v(n,{select:!0})):i===t&&e.preventDefault()}},[r,n,k.paused]);return(0,l.jsx)(o.WV.div,{tabIndex:-1,...m,ref:P,onKeyDown:M})});function p(e){let t=[],r=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;r.nextNode();)t.push(r.currentNode);return t}function h(e,t){for(let r of e)if(!function(e,t){let{upTo:r}=t;if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===r||e!==r);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(r,{upTo:t}))return r}function v(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(e&&e.focus){var r;let n=document.activeElement;e.focus({preventScroll:!0}),e!==n&&(r=e)instanceof HTMLInputElement&&"select"in r&&t&&e.select()}}d.displayName="FocusScope";var y=(n=[],{add(e){let t=n[0];e!==t&&(null==t||t.pause()),(n=g(n,e)).unshift(e)},remove(e){var t;null===(t=(n=g(n,e))[0])||void 0===t||t.resume()}});function g(e,t){let r=[...e],n=r.indexOf(t);return -1!==n&&r.splice(n,1),r}},6394:function(e,t,r){"use strict";r.d(t,{f:function(){return u}});var n=r(2265),i=r(66840),a=r(57437),o=n.forwardRef((e,t)=>(0,a.jsx)(i.WV.label,{...e,ref:t,onMouseDown:t=>{var r;t.target.closest("button, input, select, textarea")||(null===(r=e.onMouseDown)||void 0===r||r.call(e,t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));o.displayName="Label";var u=o},1353:function(e,t,r){"use strict";r.d(t,{Pc:function(){return w},ck:function(){return T},fC:function(){return C}});var n=r(2265),i=r(6741),a=r(58068),o=r(98575),u=r(73966),l=r(99255),c=r(66840),s=r(26606),f=r(80886),d=r(29114),p=r(57437),h="rovingFocusGroup.onEntryFocus",v={bubbles:!1,cancelable:!0},y="RovingFocusGroup",[g,m,b]=(0,a.B)(y),[x,w]=(0,u.b)(y,[b]),[O,j]=x(y),P=n.forwardRef((e,t)=>(0,p.jsx)(g.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,p.jsx)(g.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,p.jsx)(k,{...e,ref:t})})}));P.displayName=y;var k=n.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:r,orientation:a,loop:u=!1,dir:l,currentTabStopId:g,defaultCurrentTabStopId:b,onCurrentTabStopIdChange:x,onEntryFocus:w,preventScrollOnEntryFocus:j=!1,...P}=e,k=n.useRef(null),M=(0,o.e)(t,k),S=(0,d.gm)(l),[A,C]=(0,f.T)({prop:g,defaultProp:null!=b?b:null,onChange:x,caller:y}),[T,D]=n.useState(!1),N=(0,s.W)(w),_=m(r),I=n.useRef(!1),[R,L]=n.useState(0);return n.useEffect(()=>{let e=k.current;if(e)return e.addEventListener(h,N),()=>e.removeEventListener(h,N)},[N]),(0,p.jsx)(O,{scope:r,orientation:a,dir:S,loop:u,currentTabStopId:A,onItemFocus:n.useCallback(e=>C(e),[C]),onItemShiftTab:n.useCallback(()=>D(!0),[]),onFocusableItemAdd:n.useCallback(()=>L(e=>e+1),[]),onFocusableItemRemove:n.useCallback(()=>L(e=>e-1),[]),children:(0,p.jsx)(c.WV.div,{tabIndex:T||0===R?-1:0,"data-orientation":a,...P,ref:M,style:{outline:"none",...e.style},onMouseDown:(0,i.M)(e.onMouseDown,()=>{I.current=!0}),onFocus:(0,i.M)(e.onFocus,e=>{let t=!I.current;if(e.target===e.currentTarget&&t&&!T){let t=new CustomEvent(h,v);if(e.currentTarget.dispatchEvent(t),!t.defaultPrevented){let e=_().filter(e=>e.focusable);E([e.find(e=>e.active),e.find(e=>e.id===A),...e].filter(Boolean).map(e=>e.ref.current),j)}}I.current=!1}),onBlur:(0,i.M)(e.onBlur,()=>D(!1))})})}),M="RovingFocusGroupItem",S=n.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:r,focusable:a=!0,active:o=!1,tabStopId:u,children:s,...f}=e,d=(0,l.M)(),h=u||d,v=j(M,r),y=v.currentTabStopId===h,b=m(r),{onFocusableItemAdd:x,onFocusableItemRemove:w,currentTabStopId:O}=v;return n.useEffect(()=>{if(a)return x(),()=>w()},[a,x,w]),(0,p.jsx)(g.ItemSlot,{scope:r,id:h,focusable:a,active:o,children:(0,p.jsx)(c.WV.span,{tabIndex:y?0:-1,"data-orientation":v.orientation,...f,ref:t,onMouseDown:(0,i.M)(e.onMouseDown,e=>{a?v.onItemFocus(h):e.preventDefault()}),onFocus:(0,i.M)(e.onFocus,()=>v.onItemFocus(h)),onKeyDown:(0,i.M)(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey){v.onItemShiftTab();return}if(e.target!==e.currentTarget)return;let t=function(e,t,r){var n;let i=(n=e.key,"rtl"!==r?n:"ArrowLeft"===n?"ArrowRight":"ArrowRight"===n?"ArrowLeft":n);if(!("vertical"===t&&["ArrowLeft","ArrowRight"].includes(i))&&!("horizontal"===t&&["ArrowUp","ArrowDown"].includes(i)))return A[i]}(e,v.orientation,v.dir);if(void 0!==t){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let i=b().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===t)i.reverse();else if("prev"===t||"next"===t){var r,n;"prev"===t&&i.reverse();let a=i.indexOf(e.currentTarget);i=v.loop?(r=i,n=a+1,r.map((e,t)=>r[(n+t)%r.length])):i.slice(a+1)}setTimeout(()=>E(i))}}),children:"function"==typeof s?s({isCurrentTabStop:y,hasTabStop:null!=O}):s})})});S.displayName=M;var A={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function E(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],r=document.activeElement;for(let n of e)if(n===r||(n.focus({preventScroll:t}),document.activeElement!==r))return}var C=P,T=S},93402:function(e,t,r){"use strict";r.d(t,{VY:function(){return eI},ZA:function(){return eL},JO:function(){return eN},ck:function(){return eF},wU:function(){return eB},eT:function(){return eW},__:function(){return ez},h_:function(){return e_},fC:function(){return eC},$G:function(){return eZ},u_:function(){return eK},Z0:function(){return eU},xz:function(){return eT},B4:function(){return eD},l_:function(){return eR}});var n=r(2265),i=r(54887);function a(e,[t,r]){return Math.min(r,Math.max(t,e))}var o=r(6741),u=r(58068),l=r(98575),c=r(73966),s=r(29114),f=r(15278),d=r(86097),p=r(99103),h=r(99255),v=r(96806),y=r(83832),g=r(66840),m=r(7495),b=r(26606),x=r(80886),w=r(61188),O=r(6718),j=r(95098),P=r(5478),k=r(87922),M=r(57437),S=[" ","Enter","ArrowUp","ArrowDown"],A=[" ","Enter"],E="Select",[C,T,D]=(0,u.B)(E),[N,_]=(0,c.b)(E,[D,v.D7]),I=(0,v.D7)(),[R,L]=N(E),[z,F]=N(E),W=e=>{let{__scopeSelect:t,children:r,open:i,defaultOpen:a,onOpenChange:o,value:u,defaultValue:l,onValueChange:c,dir:f,name:d,autoComplete:p,disabled:y,required:g,form:m}=e,b=I(t),[w,O]=n.useState(null),[j,P]=n.useState(null),[k,S]=n.useState(!1),A=(0,s.gm)(f),[T,D]=(0,x.T)({prop:i,defaultProp:null!=a&&a,onChange:o,caller:E}),[N,_]=(0,x.T)({prop:u,defaultProp:l,onChange:c,caller:E}),L=n.useRef(null),F=!w||m||!!w.closest("form"),[W,B]=n.useState(new Set),K=Array.from(W).map(e=>e.props.value).join(";");return(0,M.jsx)(v.fC,{...b,children:(0,M.jsxs)(R,{required:g,scope:t,trigger:w,onTriggerChange:O,valueNode:j,onValueNodeChange:P,valueNodeHasChildren:k,onValueNodeHasChildrenChange:S,contentId:(0,h.M)(),value:N,onValueChange:_,open:T,onOpenChange:D,dir:A,triggerPointerDownPosRef:L,disabled:y,children:[(0,M.jsx)(C.Provider,{scope:t,children:(0,M.jsx)(z,{scope:e.__scopeSelect,onNativeOptionAdd:n.useCallback(e=>{B(t=>new Set(t).add(e))},[]),onNativeOptionRemove:n.useCallback(e=>{B(t=>{let r=new Set(t);return r.delete(e),r})},[]),children:r})}),F?(0,M.jsxs)(eM,{"aria-hidden":!0,required:g,tabIndex:-1,name:d,autoComplete:p,value:N,onChange:e=>_(e.target.value),disabled:y,form:m,children:[void 0===N?(0,M.jsx)("option",{value:""}):null,Array.from(W)]},K):null]})})};W.displayName=E;var B="SelectTrigger",K=n.forwardRef((e,t)=>{let{__scopeSelect:r,disabled:i=!1,...a}=e,u=I(r),c=L(B,r),s=c.disabled||i,f=(0,l.e)(t,c.onTriggerChange),d=T(r),p=n.useRef("touch"),[h,y,m]=eA(e=>{let t=d().filter(e=>!e.disabled),r=t.find(e=>e.value===c.value),n=eE(t,e,r);void 0!==n&&c.onValueChange(n.value)}),b=e=>{s||(c.onOpenChange(!0),m()),e&&(c.triggerPointerDownPosRef.current={x:Math.round(e.pageX),y:Math.round(e.pageY)})};return(0,M.jsx)(v.ee,{asChild:!0,...u,children:(0,M.jsx)(g.WV.button,{type:"button",role:"combobox","aria-controls":c.contentId,"aria-expanded":c.open,"aria-required":c.required,"aria-autocomplete":"none",dir:c.dir,"data-state":c.open?"open":"closed",disabled:s,"data-disabled":s?"":void 0,"data-placeholder":eS(c.value)?"":void 0,...a,ref:f,onClick:(0,o.M)(a.onClick,e=>{e.currentTarget.focus(),"mouse"!==p.current&&b(e)}),onPointerDown:(0,o.M)(a.onPointerDown,e=>{p.current=e.pointerType;let t=e.target;t.hasPointerCapture(e.pointerId)&&t.releasePointerCapture(e.pointerId),0===e.button&&!1===e.ctrlKey&&"mouse"===e.pointerType&&(b(e),e.preventDefault())}),onKeyDown:(0,o.M)(a.onKeyDown,e=>{let t=""!==h.current;e.ctrlKey||e.altKey||e.metaKey||1!==e.key.length||y(e.key),(!t||" "!==e.key)&&S.includes(e.key)&&(b(),e.preventDefault())})})})});K.displayName=B;var Z="SelectValue",U=n.forwardRef((e,t)=>{let{__scopeSelect:r,className:n,style:i,children:a,placeholder:o="",...u}=e,c=L(Z,r),{onValueNodeHasChildrenChange:s}=c,f=void 0!==a,d=(0,l.e)(t,c.onValueNodeChange);return(0,w.b)(()=>{s(f)},[s,f]),(0,M.jsx)(g.WV.span,{...u,ref:d,style:{pointerEvents:"none"},children:eS(c.value)?(0,M.jsx)(M.Fragment,{children:o}):a})});U.displayName=Z;var V=n.forwardRef((e,t)=>{let{__scopeSelect:r,children:n,...i}=e;return(0,M.jsx)(g.WV.span,{"aria-hidden":!0,...i,ref:t,children:n||"▼"})});V.displayName="SelectIcon";var $=e=>(0,M.jsx)(y.h,{asChild:!0,...e});$.displayName="SelectPortal";var Y="SelectContent",H=n.forwardRef((e,t)=>{let r=L(Y,e.__scopeSelect),[a,o]=n.useState();return((0,w.b)(()=>{o(new DocumentFragment)},[]),r.open)?(0,M.jsx)(Q,{...e,ref:t}):a?i.createPortal((0,M.jsx)(q,{scope:e.__scopeSelect,children:(0,M.jsx)(C.Slot,{scope:e.__scopeSelect,children:(0,M.jsx)("div",{children:e.children})})}),a):null});H.displayName=Y;var[q,X]=N(Y),G=(0,m.Z8)("SelectContent.RemoveScroll"),Q=n.forwardRef((e,t)=>{let{__scopeSelect:r,position:i="item-aligned",onCloseAutoFocus:a,onEscapeKeyDown:u,onPointerDownOutside:c,side:s,sideOffset:h,align:v,alignOffset:y,arrowPadding:g,collisionBoundary:m,collisionPadding:b,sticky:x,hideWhenDetached:w,avoidCollisions:O,...j}=e,S=L(Y,r),[A,E]=n.useState(null),[C,D]=n.useState(null),N=(0,l.e)(t,e=>E(e)),[_,I]=n.useState(null),[R,z]=n.useState(null),F=T(r),[W,B]=n.useState(!1),K=n.useRef(!1);n.useEffect(()=>{if(A)return(0,P.Ry)(A)},[A]),(0,d.EW)();let Z=n.useCallback(e=>{let[t,...r]=F().map(e=>e.ref.current),[n]=r.slice(-1),i=document.activeElement;for(let r of e)if(r===i||(null==r||r.scrollIntoView({block:"nearest"}),r===t&&C&&(C.scrollTop=0),r===n&&C&&(C.scrollTop=C.scrollHeight),null==r||r.focus(),document.activeElement!==i))return},[F,C]),U=n.useCallback(()=>Z([_,A]),[Z,_,A]);n.useEffect(()=>{W&&U()},[W,U]);let{onOpenChange:V,triggerPointerDownPosRef:$}=S;n.useEffect(()=>{if(A){let e={x:0,y:0},t=t=>{var r,n,i,a;e={x:Math.abs(Math.round(t.pageX)-(null!==(i=null===(r=$.current)||void 0===r?void 0:r.x)&&void 0!==i?i:0)),y:Math.abs(Math.round(t.pageY)-(null!==(a=null===(n=$.current)||void 0===n?void 0:n.y)&&void 0!==a?a:0))}},r=r=>{e.x<=10&&e.y<=10?r.preventDefault():A.contains(r.target)||V(!1),document.removeEventListener("pointermove",t),$.current=null};return null!==$.current&&(document.addEventListener("pointermove",t),document.addEventListener("pointerup",r,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",t),document.removeEventListener("pointerup",r,{capture:!0})}}},[A,V,$]),n.useEffect(()=>{let e=()=>V(!1);return window.addEventListener("blur",e),window.addEventListener("resize",e),()=>{window.removeEventListener("blur",e),window.removeEventListener("resize",e)}},[V]);let[H,X]=eA(e=>{let t=F().filter(e=>!e.disabled),r=t.find(e=>e.ref.current===document.activeElement),n=eE(t,e,r);n&&setTimeout(()=>n.ref.current.focus())}),Q=n.useCallback((e,t,r)=>{let n=!K.current&&!r;(void 0!==S.value&&S.value===t||n)&&(I(e),n&&(K.current=!0))},[S.value]),et=n.useCallback(()=>null==A?void 0:A.focus(),[A]),er=n.useCallback((e,t,r)=>{let n=!K.current&&!r;(void 0!==S.value&&S.value===t||n)&&z(e)},[S.value]),en="popper"===i?ee:J,ei=en===ee?{side:s,sideOffset:h,align:v,alignOffset:y,arrowPadding:g,collisionBoundary:m,collisionPadding:b,sticky:x,hideWhenDetached:w,avoidCollisions:O}:{};return(0,M.jsx)(q,{scope:r,content:A,viewport:C,onViewportChange:D,itemRefCallback:Q,selectedItem:_,onItemLeave:et,itemTextRefCallback:er,focusSelectedItem:U,selectedItemText:R,position:i,isPositioned:W,searchRef:H,children:(0,M.jsx)(k.Z,{as:G,allowPinchZoom:!0,children:(0,M.jsx)(p.M,{asChild:!0,trapped:S.open,onMountAutoFocus:e=>{e.preventDefault()},onUnmountAutoFocus:(0,o.M)(a,e=>{var t;null===(t=S.trigger)||void 0===t||t.focus({preventScroll:!0}),e.preventDefault()}),children:(0,M.jsx)(f.XB,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:u,onPointerDownOutside:c,onFocusOutside:e=>e.preventDefault(),onDismiss:()=>S.onOpenChange(!1),children:(0,M.jsx)(en,{role:"listbox",id:S.contentId,"data-state":S.open?"open":"closed",dir:S.dir,onContextMenu:e=>e.preventDefault(),...j,...ei,onPlaced:()=>B(!0),ref:N,style:{display:"flex",flexDirection:"column",outline:"none",...j.style},onKeyDown:(0,o.M)(j.onKeyDown,e=>{let t=e.ctrlKey||e.altKey||e.metaKey;if("Tab"===e.key&&e.preventDefault(),t||1!==e.key.length||X(e.key),["ArrowUp","ArrowDown","Home","End"].includes(e.key)){let t=F().filter(e=>!e.disabled).map(e=>e.ref.current);if(["ArrowUp","End"].includes(e.key)&&(t=t.slice().reverse()),["ArrowUp","ArrowDown"].includes(e.key)){let r=e.target,n=t.indexOf(r);t=t.slice(n+1)}setTimeout(()=>Z(t)),e.preventDefault()}})})})})})})});Q.displayName="SelectContentImpl";var J=n.forwardRef((e,t)=>{let{__scopeSelect:r,onPlaced:i,...o}=e,u=L(Y,r),c=X(Y,r),[s,f]=n.useState(null),[d,p]=n.useState(null),h=(0,l.e)(t,e=>p(e)),v=T(r),y=n.useRef(!1),m=n.useRef(!0),{viewport:b,selectedItem:x,selectedItemText:O,focusSelectedItem:j}=c,P=n.useCallback(()=>{if(u.trigger&&u.valueNode&&s&&d&&b&&x&&O){let e=u.trigger.getBoundingClientRect(),t=d.getBoundingClientRect(),r=u.valueNode.getBoundingClientRect(),n=O.getBoundingClientRect();if("rtl"!==u.dir){let i=n.left-t.left,o=r.left-i,u=e.left-o,l=e.width+u,c=Math.max(l,t.width),f=a(o,[10,Math.max(10,window.innerWidth-10-c)]);s.style.minWidth=l+"px",s.style.left=f+"px"}else{let i=t.right-n.right,o=window.innerWidth-r.right-i,u=window.innerWidth-e.right-o,l=e.width+u,c=Math.max(l,t.width),f=a(o,[10,Math.max(10,window.innerWidth-10-c)]);s.style.minWidth=l+"px",s.style.right=f+"px"}let o=v(),l=window.innerHeight-20,c=b.scrollHeight,f=window.getComputedStyle(d),p=parseInt(f.borderTopWidth,10),h=parseInt(f.paddingTop,10),g=parseInt(f.borderBottomWidth,10),m=p+h+c+parseInt(f.paddingBottom,10)+g,w=Math.min(5*x.offsetHeight,m),j=window.getComputedStyle(b),P=parseInt(j.paddingTop,10),k=parseInt(j.paddingBottom,10),M=e.top+e.height/2-10,S=x.offsetHeight/2,A=p+h+(x.offsetTop+S);if(A<=M){let e=o.length>0&&x===o[o.length-1].ref.current;s.style.bottom="0px";let t=d.clientHeight-b.offsetTop-b.offsetHeight;s.style.height=A+Math.max(l-M,S+(e?k:0)+t+g)+"px"}else{let e=o.length>0&&x===o[0].ref.current;s.style.top="0px";let t=Math.max(M,p+b.offsetTop+(e?P:0)+S);s.style.height=t+(m-A)+"px",b.scrollTop=A-M+b.offsetTop}s.style.margin="".concat(10,"px 0"),s.style.minHeight=w+"px",s.style.maxHeight=l+"px",null==i||i(),requestAnimationFrame(()=>y.current=!0)}},[v,u.trigger,u.valueNode,s,d,b,x,O,u.dir,i]);(0,w.b)(()=>P(),[P]);let[k,S]=n.useState();(0,w.b)(()=>{d&&S(window.getComputedStyle(d).zIndex)},[d]);let A=n.useCallback(e=>{e&&!0===m.current&&(P(),null==j||j(),m.current=!1)},[P,j]);return(0,M.jsx)(et,{scope:r,contentWrapper:s,shouldExpandOnScrollRef:y,onScrollButtonChange:A,children:(0,M.jsx)("div",{ref:f,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:k},children:(0,M.jsx)(g.WV.div,{...o,ref:h,style:{boxSizing:"border-box",maxHeight:"100%",...o.style}})})})});J.displayName="SelectItemAlignedPosition";var ee=n.forwardRef((e,t)=>{let{__scopeSelect:r,align:n="start",collisionPadding:i=10,...a}=e,o=I(r);return(0,M.jsx)(v.VY,{...o,...a,ref:t,align:n,collisionPadding:i,style:{boxSizing:"border-box",...a.style,"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"}})});ee.displayName="SelectPopperPosition";var[et,er]=N(Y,{}),en="SelectViewport",ei=n.forwardRef((e,t)=>{let{__scopeSelect:r,nonce:i,...a}=e,u=X(en,r),c=er(en,r),s=(0,l.e)(t,u.onViewportChange),f=n.useRef(0);return(0,M.jsxs)(M.Fragment,{children:[(0,M.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce:i}),(0,M.jsx)(C.Slot,{scope:r,children:(0,M.jsx)(g.WV.div,{"data-radix-select-viewport":"",role:"presentation",...a,ref:s,style:{position:"relative",flex:1,overflow:"hidden auto",...a.style},onScroll:(0,o.M)(a.onScroll,e=>{let t=e.currentTarget,{contentWrapper:r,shouldExpandOnScrollRef:n}=c;if((null==n?void 0:n.current)&&r){let e=Math.abs(f.current-t.scrollTop);if(e>0){let n=window.innerHeight-20,i=Math.max(parseFloat(r.style.minHeight),parseFloat(r.style.height));if(i<n){let a=i+e,o=Math.min(n,a),u=a-o;r.style.height=o+"px","0px"===r.style.bottom&&(t.scrollTop=u>0?u:0,r.style.justifyContent="flex-end")}}}f.current=t.scrollTop})})})]})});ei.displayName=en;var ea="SelectGroup",[eo,eu]=N(ea),el=n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e,i=(0,h.M)();return(0,M.jsx)(eo,{scope:r,id:i,children:(0,M.jsx)(g.WV.div,{role:"group","aria-labelledby":i,...n,ref:t})})});el.displayName=ea;var ec="SelectLabel",es=n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e,i=eu(ec,r);return(0,M.jsx)(g.WV.div,{id:i.id,...n,ref:t})});es.displayName=ec;var ef="SelectItem",[ed,ep]=N(ef),eh=n.forwardRef((e,t)=>{let{__scopeSelect:r,value:i,disabled:a=!1,textValue:u,...c}=e,s=L(ef,r),f=X(ef,r),d=s.value===i,[p,v]=n.useState(null!=u?u:""),[y,m]=n.useState(!1),b=(0,l.e)(t,e=>{var t;return null===(t=f.itemRefCallback)||void 0===t?void 0:t.call(f,e,i,a)}),x=(0,h.M)(),w=n.useRef("touch"),O=()=>{a||(s.onValueChange(i),s.onOpenChange(!1))};if(""===i)throw Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return(0,M.jsx)(ed,{scope:r,value:i,disabled:a,textId:x,isSelected:d,onItemTextChange:n.useCallback(e=>{v(t=>{var r;return t||(null!==(r=null==e?void 0:e.textContent)&&void 0!==r?r:"").trim()})},[]),children:(0,M.jsx)(C.ItemSlot,{scope:r,value:i,disabled:a,textValue:p,children:(0,M.jsx)(g.WV.div,{role:"option","aria-labelledby":x,"data-highlighted":y?"":void 0,"aria-selected":d&&y,"data-state":d?"checked":"unchecked","aria-disabled":a||void 0,"data-disabled":a?"":void 0,tabIndex:a?void 0:-1,...c,ref:b,onFocus:(0,o.M)(c.onFocus,()=>m(!0)),onBlur:(0,o.M)(c.onBlur,()=>m(!1)),onClick:(0,o.M)(c.onClick,()=>{"mouse"!==w.current&&O()}),onPointerUp:(0,o.M)(c.onPointerUp,()=>{"mouse"===w.current&&O()}),onPointerDown:(0,o.M)(c.onPointerDown,e=>{w.current=e.pointerType}),onPointerMove:(0,o.M)(c.onPointerMove,e=>{if(w.current=e.pointerType,a){var t;null===(t=f.onItemLeave)||void 0===t||t.call(f)}else"mouse"===w.current&&e.currentTarget.focus({preventScroll:!0})}),onPointerLeave:(0,o.M)(c.onPointerLeave,e=>{if(e.currentTarget===document.activeElement){var t;null===(t=f.onItemLeave)||void 0===t||t.call(f)}}),onKeyDown:(0,o.M)(c.onKeyDown,e=>{var t;(null===(t=f.searchRef)||void 0===t?void 0:t.current)!==""&&" "===e.key||(A.includes(e.key)&&O()," "===e.key&&e.preventDefault())})})})})});eh.displayName=ef;var ev="SelectItemText",ey=n.forwardRef((e,t)=>{let{__scopeSelect:r,className:a,style:o,...u}=e,c=L(ev,r),s=X(ev,r),f=ep(ev,r),d=F(ev,r),[p,h]=n.useState(null),v=(0,l.e)(t,e=>h(e),f.onItemTextChange,e=>{var t;return null===(t=s.itemTextRefCallback)||void 0===t?void 0:t.call(s,e,f.value,f.disabled)}),y=null==p?void 0:p.textContent,m=n.useMemo(()=>(0,M.jsx)("option",{value:f.value,disabled:f.disabled,children:y},f.value),[f.disabled,f.value,y]),{onNativeOptionAdd:b,onNativeOptionRemove:x}=d;return(0,w.b)(()=>(b(m),()=>x(m)),[b,x,m]),(0,M.jsxs)(M.Fragment,{children:[(0,M.jsx)(g.WV.span,{id:f.textId,...u,ref:v}),f.isSelected&&c.valueNode&&!c.valueNodeHasChildren?i.createPortal(u.children,c.valueNode):null]})});ey.displayName=ev;var eg="SelectItemIndicator",em=n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e;return ep(eg,r).isSelected?(0,M.jsx)(g.WV.span,{"aria-hidden":!0,...n,ref:t}):null});em.displayName=eg;var eb="SelectScrollUpButton",ex=n.forwardRef((e,t)=>{let r=X(eb,e.__scopeSelect),i=er(eb,e.__scopeSelect),[a,o]=n.useState(!1),u=(0,l.e)(t,i.onScrollButtonChange);return(0,w.b)(()=>{if(r.viewport&&r.isPositioned){let e=function(){o(t.scrollTop>0)},t=r.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[r.viewport,r.isPositioned]),a?(0,M.jsx)(ej,{...e,ref:u,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=r;e&&t&&(e.scrollTop=e.scrollTop-t.offsetHeight)}}):null});ex.displayName=eb;var ew="SelectScrollDownButton",eO=n.forwardRef((e,t)=>{let r=X(ew,e.__scopeSelect),i=er(ew,e.__scopeSelect),[a,o]=n.useState(!1),u=(0,l.e)(t,i.onScrollButtonChange);return(0,w.b)(()=>{if(r.viewport&&r.isPositioned){let e=function(){let e=t.scrollHeight-t.clientHeight;o(Math.ceil(t.scrollTop)<e)},t=r.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[r.viewport,r.isPositioned]),a?(0,M.jsx)(ej,{...e,ref:u,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=r;e&&t&&(e.scrollTop=e.scrollTop+t.offsetHeight)}}):null});eO.displayName=ew;var ej=n.forwardRef((e,t)=>{let{__scopeSelect:r,onAutoScroll:i,...a}=e,u=X("SelectScrollButton",r),l=n.useRef(null),c=T(r),s=n.useCallback(()=>{null!==l.current&&(window.clearInterval(l.current),l.current=null)},[]);return n.useEffect(()=>()=>s(),[s]),(0,w.b)(()=>{var e;let t=c().find(e=>e.ref.current===document.activeElement);null==t||null===(e=t.ref.current)||void 0===e||e.scrollIntoView({block:"nearest"})},[c]),(0,M.jsx)(g.WV.div,{"aria-hidden":!0,...a,ref:t,style:{flexShrink:0,...a.style},onPointerDown:(0,o.M)(a.onPointerDown,()=>{null===l.current&&(l.current=window.setInterval(i,50))}),onPointerMove:(0,o.M)(a.onPointerMove,()=>{var e;null===(e=u.onItemLeave)||void 0===e||e.call(u),null===l.current&&(l.current=window.setInterval(i,50))}),onPointerLeave:(0,o.M)(a.onPointerLeave,()=>{s()})})}),eP=n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e;return(0,M.jsx)(g.WV.div,{"aria-hidden":!0,...n,ref:t})});eP.displayName="SelectSeparator";var ek="SelectArrow";n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e,i=I(r),a=L(ek,r),o=X(ek,r);return a.open&&"popper"===o.position?(0,M.jsx)(v.Eh,{...i,...n,ref:t}):null}).displayName=ek;var eM=n.forwardRef((e,t)=>{let{__scopeSelect:r,value:i,...a}=e,o=n.useRef(null),u=(0,l.e)(t,o),c=(0,O.D)(i);return n.useEffect(()=>{let e=o.current;if(!e)return;let t=Object.getOwnPropertyDescriptor(window.HTMLSelectElement.prototype,"value").set;if(c!==i&&t){let r=new Event("change",{bubbles:!0});t.call(e,i),e.dispatchEvent(r)}},[c,i]),(0,M.jsx)(g.WV.select,{...a,style:{...j.C2,...a.style},ref:u,defaultValue:i})});function eS(e){return""===e||void 0===e}function eA(e){let t=(0,b.W)(e),r=n.useRef(""),i=n.useRef(0),a=n.useCallback(e=>{let n=r.current+e;t(n),function e(t){r.current=t,window.clearTimeout(i.current),""!==t&&(i.current=window.setTimeout(()=>e(""),1e3))}(n)},[t]),o=n.useCallback(()=>{r.current="",window.clearTimeout(i.current)},[]);return n.useEffect(()=>()=>window.clearTimeout(i.current),[]),[r,a,o]}function eE(e,t,r){var n;let i=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,a=(n=Math.max(r?e.indexOf(r):-1,0),e.map((t,r)=>e[(n+r)%e.length]));1===i.length&&(a=a.filter(e=>e!==r));let o=a.find(e=>e.textValue.toLowerCase().startsWith(i.toLowerCase()));return o!==r?o:void 0}eM.displayName="SelectBubbleInput";var eC=W,eT=K,eD=U,eN=V,e_=$,eI=H,eR=ei,eL=el,ez=es,eF=eh,eW=ey,eB=em,eK=ex,eZ=eO,eU=eP},50721:function(e,t,r){"use strict";r.d(t,{bU:function(){return j},fC:function(){return O}});var n=r(2265),i=r(6741),a=r(98575),o=r(73966),u=r(80886),l=r(6718),c=r(90420),s=r(66840),f=r(57437),d="Switch",[p,h]=(0,o.b)(d),[v,y]=p(d),g=n.forwardRef((e,t)=>{let{__scopeSwitch:r,name:o,checked:l,defaultChecked:c,required:p,disabled:h,value:y="on",onCheckedChange:g,form:m,...b}=e,[O,j]=n.useState(null),P=(0,a.e)(t,e=>j(e)),k=n.useRef(!1),M=!O||m||!!O.closest("form"),[S,A]=(0,u.T)({prop:l,defaultProp:null!=c&&c,onChange:g,caller:d});return(0,f.jsxs)(v,{scope:r,checked:S,disabled:h,children:[(0,f.jsx)(s.WV.button,{type:"button",role:"switch","aria-checked":S,"aria-required":p,"data-state":w(S),"data-disabled":h?"":void 0,disabled:h,value:y,...b,ref:P,onClick:(0,i.M)(e.onClick,e=>{A(e=>!e),M&&(k.current=e.isPropagationStopped(),k.current||e.stopPropagation())})}),M&&(0,f.jsx)(x,{control:O,bubbles:!k.current,name:o,value:y,checked:S,required:p,disabled:h,form:m,style:{transform:"translateX(-100%)"}})]})});g.displayName=d;var m="SwitchThumb",b=n.forwardRef((e,t)=>{let{__scopeSwitch:r,...n}=e,i=y(m,r);return(0,f.jsx)(s.WV.span,{"data-state":w(i.checked),"data-disabled":i.disabled?"":void 0,...n,ref:t})});b.displayName=m;var x=n.forwardRef((e,t)=>{let{__scopeSwitch:r,control:i,checked:o,bubbles:u=!0,...s}=e,d=n.useRef(null),p=(0,a.e)(d,t),h=(0,l.D)(o),v=(0,c.t)(i);return n.useEffect(()=>{let e=d.current;if(!e)return;let t=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(h!==o&&t){let r=new Event("click",{bubbles:u});t.call(e,o),e.dispatchEvent(r)}},[h,o,u]),(0,f.jsx)("input",{type:"checkbox","aria-hidden":!0,defaultChecked:o,...s,tabIndex:-1,ref:p,style:{...s.style,...v,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})});function w(e){return e?"checked":"unchecked"}x.displayName="SwitchBubbleInput";var O=g,j=b},20271:function(e,t,r){"use strict";r.d(t,{VY:function(){return T},aV:function(){return E},fC:function(){return A},xz:function(){return C}});var n=r(2265),i=r(6741),a=r(73966),o=r(1353),u=r(71599),l=r(66840),c=r(29114),s=r(80886),f=r(99255),d=r(57437),p="Tabs",[h,v]=(0,a.b)(p,[o.Pc]),y=(0,o.Pc)(),[g,m]=h(p),b=n.forwardRef((e,t)=>{let{__scopeTabs:r,value:n,onValueChange:i,defaultValue:a,orientation:o="horizontal",dir:u,activationMode:h="automatic",...v}=e,y=(0,c.gm)(u),[m,b]=(0,s.T)({prop:n,onChange:i,defaultProp:null!=a?a:"",caller:p});return(0,d.jsx)(g,{scope:r,baseId:(0,f.M)(),value:m,onValueChange:b,orientation:o,dir:y,activationMode:h,children:(0,d.jsx)(l.WV.div,{dir:y,"data-orientation":o,...v,ref:t})})});b.displayName=p;var x="TabsList",w=n.forwardRef((e,t)=>{let{__scopeTabs:r,loop:n=!0,...i}=e,a=m(x,r),u=y(r);return(0,d.jsx)(o.fC,{asChild:!0,...u,orientation:a.orientation,dir:a.dir,loop:n,children:(0,d.jsx)(l.WV.div,{role:"tablist","aria-orientation":a.orientation,...i,ref:t})})});w.displayName=x;var O="TabsTrigger",j=n.forwardRef((e,t)=>{let{__scopeTabs:r,value:n,disabled:a=!1,...u}=e,c=m(O,r),s=y(r),f=M(c.baseId,n),p=S(c.baseId,n),h=n===c.value;return(0,d.jsx)(o.ck,{asChild:!0,...s,focusable:!a,active:h,children:(0,d.jsx)(l.WV.button,{type:"button",role:"tab","aria-selected":h,"aria-controls":p,"data-state":h?"active":"inactive","data-disabled":a?"":void 0,disabled:a,id:f,...u,ref:t,onMouseDown:(0,i.M)(e.onMouseDown,e=>{a||0!==e.button||!1!==e.ctrlKey?e.preventDefault():c.onValueChange(n)}),onKeyDown:(0,i.M)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&c.onValueChange(n)}),onFocus:(0,i.M)(e.onFocus,()=>{let e="manual"!==c.activationMode;h||a||!e||c.onValueChange(n)})})})});j.displayName=O;var P="TabsContent",k=n.forwardRef((e,t)=>{let{__scopeTabs:r,value:i,forceMount:a,children:o,...c}=e,s=m(P,r),f=M(s.baseId,i),p=S(s.baseId,i),h=i===s.value,v=n.useRef(h);return n.useEffect(()=>{let e=requestAnimationFrame(()=>v.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,d.jsx)(u.z,{present:a||h,children:r=>{let{present:n}=r;return(0,d.jsx)(l.WV.div,{"data-state":h?"active":"inactive","data-orientation":s.orientation,role:"tabpanel","aria-labelledby":f,hidden:!n,id:p,tabIndex:0,...c,ref:t,style:{...e.style,animationDuration:v.current?"0s":void 0},children:n&&o})}})});function M(e,t){return"".concat(e,"-trigger-").concat(t)}function S(e,t){return"".concat(e,"-content-").concat(t)}k.displayName=P;var A=b,E=w,C=j,T=k},6718:function(e,t,r){"use strict";r.d(t,{D:function(){return i}});var n=r(2265);function i(e){let t=n.useRef({value:e,previous:e});return n.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}},22516:function(e,t,r){"use strict";function n(e){return"object"==typeof e&&"length"in e?e:Array.from(e)}r.d(t,{Z:function(){return n}}),Array.prototype.slice},76115:function(e,t,r){"use strict";function n(e){return function(){return e}}r.d(t,{Z:function(){return n}})},67790:function(e,t,r){"use strict";r.d(t,{d:function(){return l}});let n=Math.PI,i=2*n,a=i-1e-6;function o(e){this._+=e[0];for(let t=1,r=e.length;t<r;++t)this._+=arguments[t]+e[t]}class u{constructor(e){this._x0=this._y0=this._x1=this._y1=null,this._="",this._append=null==e?o:function(e){let t=Math.floor(e);if(!(t>=0))throw Error(`invalid digits: ${e}`);if(t>15)return o;let r=10**t;return function(e){this._+=e[0];for(let t=1,n=e.length;t<n;++t)this._+=Math.round(arguments[t]*r)/r+e[t]}}(e)}moveTo(e,t){this._append`M${this._x0=this._x1=+e},${this._y0=this._y1=+t}`}closePath(){null!==this._x1&&(this._x1=this._x0,this._y1=this._y0,this._append`Z`)}lineTo(e,t){this._append`L${this._x1=+e},${this._y1=+t}`}quadraticCurveTo(e,t,r,n){this._append`Q${+e},${+t},${this._x1=+r},${this._y1=+n}`}bezierCurveTo(e,t,r,n,i,a){this._append`C${+e},${+t},${+r},${+n},${this._x1=+i},${this._y1=+a}`}arcTo(e,t,r,i,a){if(e=+e,t=+t,r=+r,i=+i,(a=+a)<0)throw Error(`negative radius: ${a}`);let o=this._x1,u=this._y1,l=r-e,c=i-t,s=o-e,f=u-t,d=s*s+f*f;if(null===this._x1)this._append`M${this._x1=e},${this._y1=t}`;else if(d>1e-6){if(Math.abs(f*l-c*s)>1e-6&&a){let p=r-o,h=i-u,v=l*l+c*c,y=Math.sqrt(v),g=Math.sqrt(d),m=a*Math.tan((n-Math.acos((v+d-(p*p+h*h))/(2*y*g)))/2),b=m/g,x=m/y;Math.abs(b-1)>1e-6&&this._append`L${e+b*s},${t+b*f}`,this._append`A${a},${a},0,0,${+(f*p>s*h)},${this._x1=e+x*l},${this._y1=t+x*c}`}else this._append`L${this._x1=e},${this._y1=t}`}}arc(e,t,r,o,u,l){if(e=+e,t=+t,l=!!l,(r=+r)<0)throw Error(`negative radius: ${r}`);let c=r*Math.cos(o),s=r*Math.sin(o),f=e+c,d=t+s,p=1^l,h=l?o-u:u-o;null===this._x1?this._append`M${f},${d}`:(Math.abs(this._x1-f)>1e-6||Math.abs(this._y1-d)>1e-6)&&this._append`L${f},${d}`,r&&(h<0&&(h=h%i+i),h>a?this._append`A${r},${r},0,1,${p},${e-c},${t-s}A${r},${r},0,1,${p},${this._x1=f},${this._y1=d}`:h>1e-6&&this._append`A${r},${r},0,${+(h>=n)},${p},${this._x1=e+r*Math.cos(u)},${this._y1=t+r*Math.sin(u)}`)}rect(e,t,r,n){this._append`M${this._x0=this._x1=+e},${this._y0=this._y1=+t}h${r=+r}v${+n}h${-r}Z`}toString(){return this._}}function l(e){let t=3;return e.digits=function(r){if(!arguments.length)return t;if(null==r)t=null;else{let e=Math.floor(r);if(!(e>=0))throw RangeError(`invalid digits: ${r}`);t=e}return e},()=>new u(t)}u.prototype},12100:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});let n=(0,r(96471).Z)("ArrowDown",[["path",{d:"M12 5v14",key:"s699le"}],["path",{d:"m19 12-7 7-7-7",key:"1idqje"}]])},5485:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});let n=(0,r(96471).Z)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},10155:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});let n=(0,r(96471).Z)("ArrowUp",[["path",{d:"m5 12 7-7 7 7",key:"hav0vg"}],["path",{d:"M12 19V5",key:"x0mq9r"}]])},66961:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});let n=(0,r(96471).Z)("Baby",[["path",{d:"M9 12h.01",key:"157uk2"}],["path",{d:"M15 12h.01",key:"1k8ypt"}],["path",{d:"M10 16c.5.3 1.2.5 2 .5s1.5-.2 2-.5",key:"1u7htd"}],["path",{d:"M19 6.3a9 9 0 0 1 1.8 3.9 2 2 0 0 1 0 3.6 9 9 0 0 1-17.6 0 2 2 0 0 1 0-3.6A9 9 0 0 1 12 3c2 0 3.5 1.1 3.5 2.5s-.9 2.5-2 2.5c-.8 0-1.5-.4-1.5-1",key:"5yv0yz"}]])},29935:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});let n=(0,r(96471).Z)("Building2",[["path",{d:"M6 22V4a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2v18Z",key:"1b4qmf"}],["path",{d:"M6 12H4a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2h2",key:"i71pzd"}],["path",{d:"M18 9h2a2 2 0 0 1 2 2v9a2 2 0 0 1-2 2h-2",key:"10jefs"}],["path",{d:"M10 6h4",key:"1itunk"}],["path",{d:"M10 10h4",key:"tcdvrf"}],["path",{d:"M10 14h4",key:"kelpxr"}],["path",{d:"M10 18h4",key:"1ulq68"}]])},91120:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});let n=(0,r(96471).Z)("Building",[["rect",{width:"16",height:"20",x:"4",y:"2",rx:"2",ry:"2",key:"76otgf"}],["path",{d:"M9 22v-4h6v4",key:"r93iot"}],["path",{d:"M8 6h.01",key:"1dz90k"}],["path",{d:"M16 6h.01",key:"1x0f13"}],["path",{d:"M12 6h.01",key:"1vi96p"}],["path",{d:"M12 10h.01",key:"1nrarc"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M16 10h.01",key:"1m94wz"}],["path",{d:"M16 14h.01",key:"1gbofw"}],["path",{d:"M8 10h.01",key:"19clt8"}],["path",{d:"M8 14h.01",key:"6423bh"}]])},90283:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});let n=(0,r(96471).Z)("Calculator",[["rect",{width:"16",height:"20",x:"4",y:"2",rx:"2",key:"1nb95v"}],["line",{x1:"8",x2:"16",y1:"6",y2:"6",key:"x4nwl0"}],["line",{x1:"16",x2:"16",y1:"14",y2:"18",key:"wjye3r"}],["path",{d:"M16 10h.01",key:"1m94wz"}],["path",{d:"M12 10h.01",key:"1nrarc"}],["path",{d:"M8 10h.01",key:"19clt8"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M8 14h.01",key:"6423bh"}],["path",{d:"M12 18h.01",key:"mhygvu"}],["path",{d:"M8 18h.01",key:"lrp35t"}]])},13147:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});let n=(0,r(96471).Z)("Calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},59701:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});let n=(0,r(96471).Z)("ChartColumn",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]])},59559:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});let n=(0,r(96471).Z)("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},3289:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});let n=(0,r(96471).Z)("ChevronDown",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},76848:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});let n=(0,r(96471).Z)("ChevronRight",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},47969:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});let n=(0,r(96471).Z)("ChevronUp",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]])},5255:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});let n=(0,r(96471).Z)("CircleCheckBig",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},54233:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});let n=(0,r(96471).Z)("CirclePlus",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M8 12h8",key:"1wcyev"}],["path",{d:"M12 8v8",key:"napkw2"}]])},78084:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});let n=(0,r(96471).Z)("Circle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]])},81475:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});let n=(0,r(96471).Z)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},63390:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});let n=(0,r(96471).Z)("CreditCard",[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]])},54585:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});let n=(0,r(96471).Z)("DollarSign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]])},45736:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});let n=(0,r(96471).Z)("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]])},56141:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});let n=(0,r(96471).Z)("Eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},87928:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});let n=(0,r(96471).Z)("FileText",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},46581:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});let n=(0,r(96471).Z)("FileX",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"m14.5 12.5-5 5",key:"b62r18"}],["path",{d:"m9.5 12.5 5 5",key:"1rk7el"}]])},86383:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});let n=(0,r(96471).Z)("Heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]])},99049:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});let n=(0,r(96471).Z)("House",[["path",{d:"M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8",key:"5wwlr5"}],["path",{d:"M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"1d0kgt"}]])},75980:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});let n=(0,r(96471).Z)("Lightbulb",[["path",{d:"M15 14c.2-1 .7-1.7 1.5-2.5 1-.9 1.5-2.2 1.5-3.5A6 6 0 0 0 6 8c0 1 .2 2.2 1.5 3.5.7.7 1.3 1.5 1.5 2.5",key:"1gvzjb"}],["path",{d:"M9 18h6",key:"x1upvd"}],["path",{d:"M10 22h4",key:"ceow96"}]])},53459:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});let n=(0,r(96471).Z)("LoaderCircle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},5252:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});let n=(0,r(96471).Z)("Lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]])},3683:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});let n=(0,r(96471).Z)("LogOut",[["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}],["polyline",{points:"16 17 21 12 16 7",key:"1gabdz"}],["line",{x1:"21",x2:"9",y1:"12",y2:"12",key:"1uyos4"}]])},46747:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});let n=(0,r(96471).Z)("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]])},96818:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});let n=(0,r(96471).Z)("Menu",[["line",{x1:"4",x2:"20",y1:"12",y2:"12",key:"1e0a9i"}],["line",{x1:"4",x2:"20",y1:"6",y2:"6",key:"1owob3"}],["line",{x1:"4",x2:"20",y1:"18",y2:"18",key:"yk5zj1"}]])},5341:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});let n=(0,r(96471).Z)("MessageCircle",[["path",{d:"M7.9 20A9 9 0 1 0 4 16.1L2 22Z",key:"vv11sd"}]])},19965:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});let n=(0,r(96471).Z)("MessageSquare",[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]])},69626:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});let n=(0,r(96471).Z)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},65887:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});let n=(0,r(96471).Z)("Redo",[["path",{d:"M21 7v6h-6",key:"3ptur4"}],["path",{d:"M3 17a9 9 0 0 1 9-9 9 9 0 0 1 6 2.3l3 2.7",key:"1kgawr"}]])},66314:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});let n=(0,r(96471).Z)("RefreshCw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},23818:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});let n=(0,r(96471).Z)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])},79679:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});let n=(0,r(96471).Z)("Send",[["path",{d:"M14.536 21.686a.5.5 0 0 0 .937-.024l6.5-19a.496.496 0 0 0-.635-.635l-19 6.5a.5.5 0 0 0-.024.937l7.93 3.18a2 2 0 0 1 1.112 1.11z",key:"1ffxy3"}],["path",{d:"m21.854 2.147-10.94 10.939",key:"12cjpa"}]])},40339:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});let n=(0,r(96471).Z)("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},73932:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});let n=(0,r(96471).Z)("Share2",[["circle",{cx:"18",cy:"5",r:"3",key:"gq8acd"}],["circle",{cx:"6",cy:"12",r:"3",key:"w7nqdw"}],["circle",{cx:"18",cy:"19",r:"3",key:"1xt0gg"}],["line",{x1:"8.59",x2:"15.42",y1:"13.51",y2:"17.49",key:"47mynk"}],["line",{x1:"15.41",x2:"8.59",y1:"6.51",y2:"10.49",key:"1n3mei"}]])},36086:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});let n=(0,r(96471).Z)("Shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]])},26563:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});let n=(0,r(96471).Z)("Star",[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]])},93835:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});let n=(0,r(96471).Z)("Target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]])},79282:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});let n=(0,r(96471).Z)("TrendingDown",[["polyline",{points:"22 17 13.5 8.5 8.5 13.5 2 7",key:"1r2t7k"}],["polyline",{points:"16 17 22 17 22 11",key:"11uiuu"}]])},16275:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});let n=(0,r(96471).Z)("TrendingUp",[["polyline",{points:"22 7 13.5 15.5 8.5 10.5 2 17",key:"126l90"}],["polyline",{points:"16 7 22 7 22 13",key:"kwv8wd"}]])},12032:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});let n=(0,r(96471).Z)("TriangleAlert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},53927:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});let n=(0,r(96471).Z)("Upload",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"17 8 12 3 7 8",key:"t8dd8p"}],["line",{x1:"12",x2:"12",y1:"3",y2:"15",key:"widbto"}]])},16049:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});let n=(0,r(96471).Z)("UserCheck",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["polyline",{points:"16 11 18 13 22 9",key:"1pwet4"}]])},67350:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});let n=(0,r(96471).Z)("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},16994:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});let n=(0,r(96471).Z)("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]])},92713:function(e,t,r){"use strict";r.d(t,{P1:function(){return x}});var n=e=>Array.isArray(e)?e:[e],i=0,a=class{revision=i;_value;_lastValue;_isEqual=o;constructor(e,t=o){this._value=this._lastValue=e,this._isEqual=t}get value(){return this._value}set value(e){this.value!==e&&(this._value=e,this.revision=++i)}};function o(e,t){return e===t}function u(e){return e instanceof a||console.warn("Not a valid cell! ",e),e.value}var l=(e,t)=>!1;function c(){return function(e,t=o){return new a(null,t)}(0,l)}var s=e=>{let t=e.collectionTag;null===t&&(t=e.collectionTag=c()),u(t)};Symbol();var f=0,d=Object.getPrototypeOf({}),p=class{constructor(e){this.value=e,this.value=e,this.tag.value=e}proxy=new Proxy(this,h);tag=c();tags={};children={};collectionTag=null;id=f++},h={get:(e,t)=>(function(){let{value:r}=e,n=Reflect.get(r,t);if("symbol"==typeof t||t in d)return n;if("object"==typeof n&&null!==n){let r=e.children[t];return void 0===r&&(r=e.children[t]=Array.isArray(n)?new v(n):new p(n)),r.tag&&u(r.tag),r.proxy}{let r=e.tags[t];return void 0===r&&((r=e.tags[t]=c()).value=n),u(r),n}})(),ownKeys:e=>(s(e),Reflect.ownKeys(e.value)),getOwnPropertyDescriptor:(e,t)=>Reflect.getOwnPropertyDescriptor(e.value,t),has:(e,t)=>Reflect.has(e.value,t)},v=class{constructor(e){this.value=e,this.value=e,this.tag.value=e}proxy=new Proxy([this],y);tag=c();tags={};children={};collectionTag=null;id=f++},y={get:([e],t)=>("length"===t&&s(e),h.get(e,t)),ownKeys:([e])=>h.ownKeys(e),getOwnPropertyDescriptor:([e],t)=>h.getOwnPropertyDescriptor(e,t),has:([e],t)=>h.has(e,t)},g="undefined"!=typeof WeakRef?WeakRef:class{constructor(e){this.value=e}deref(){return this.value}};function m(){return{s:0,v:void 0,o:null,p:null}}function b(e,t={}){let r,n=m(),{resultEqualityCheck:i}=t,a=0;function o(){let t,o=n,{length:u}=arguments;for(let e=0;e<u;e++){let t=arguments[e];if("function"==typeof t||"object"==typeof t&&null!==t){let e=o.o;null===e&&(o.o=e=new WeakMap);let r=e.get(t);void 0===r?(o=m(),e.set(t,o)):o=r}else{let e=o.p;null===e&&(o.p=e=new Map);let r=e.get(t);void 0===r?(o=m(),e.set(t,o)):o=r}}let l=o;if(1===o.s)t=o.v;else if(t=e.apply(null,arguments),a++,i){let e=r?.deref?.()??r;null!=e&&i(e,t)&&(t=e,0!==a&&a--),r="object"==typeof t&&null!==t||"function"==typeof t?new g(t):t}return l.s=1,l.v=t,t}return o.clearCache=()=>{n=m(),o.resetResultsCount()},o.resultsCount=()=>a,o.resetResultsCount=()=>{a=0},o}var x=function(e,...t){let r="function"==typeof e?{memoize:e,memoizeOptions:t}:e,i=(...e)=>{let t,i=0,a=0,o={},u=e.pop();"object"==typeof u&&(o=u,u=e.pop()),function(e,t=`expected a function, instead received ${typeof e}`){if("function"!=typeof e)throw TypeError(t)}(u,`createSelector expects an output function after the inputs, but received: [${typeof u}]`);let{memoize:l,memoizeOptions:c=[],argsMemoize:s=b,argsMemoizeOptions:f=[],devModeChecks:d={}}={...r,...o},p=n(c),h=n(f),v=function(e){let t=Array.isArray(e[0])?e[0]:e;return!function(e,t="expected all items to be functions, instead received the following types: "){if(!e.every(e=>"function"==typeof e)){let r=e.map(e=>"function"==typeof e?`function ${e.name||"unnamed"}()`:typeof e).join(", ");throw TypeError(`${t}[${r}]`)}}(t,"createSelector expects all input-selectors to be functions, but received the following types: "),t}(e),y=l(function(){return i++,u.apply(null,arguments)},...p);return Object.assign(s(function(){a++;let e=function(e,t){let r=[],{length:n}=e;for(let i=0;i<n;i++)r.push(e[i].apply(null,t));return r}(v,arguments);return t=y.apply(null,e)},...h),{resultFunc:u,memoizedResultFunc:y,dependencies:v,dependencyRecomputations:()=>a,resetDependencyRecomputations:()=>{a=0},lastResult:()=>t,recomputations:()=>i,resetRecomputations:()=>{i=0},memoize:l,argsMemoize:s})};return Object.assign(i,{withTypes:()=>i}),i}(b),w=Object.assign((e,t=x)=>{!function(e,t=`expected an object, instead received ${typeof e}`){if("object"!=typeof e)throw TypeError(t)}(e,`createStructuredSelector expects first argument to be an object where each property is a selector, instead received a ${typeof e}`);let r=Object.keys(e);return t(r.map(t=>e[t]),(...e)=>e.reduce((e,t,n)=>(e[r[n]]=t,e),{}))},{withTypes:()=>w})},14438:function(e,t,r){"use strict";r.d(t,{Am:function(){return l}});var n=r(2265);r(54887),Array(12).fill(0);let i=1;class a{constructor(){this.subscribe=e=>(this.subscribers.push(e),()=>{let t=this.subscribers.indexOf(e);this.subscribers.splice(t,1)}),this.publish=e=>{this.subscribers.forEach(t=>t(e))},this.addToast=e=>{this.publish(e),this.toasts=[...this.toasts,e]},this.create=e=>{var t;let{message:r,...n}=e,a="number"==typeof(null==e?void 0:e.id)||(null==(t=e.id)?void 0:t.length)>0?e.id:i++,o=this.toasts.find(e=>e.id===a),u=void 0===e.dismissible||e.dismissible;return this.dismissedToasts.has(a)&&this.dismissedToasts.delete(a),o?this.toasts=this.toasts.map(t=>t.id===a?(this.publish({...t,...e,id:a,title:r}),{...t,...e,id:a,dismissible:u,title:r}):t):this.addToast({title:r,...n,dismissible:u,id:a}),a},this.dismiss=e=>(e?(this.dismissedToasts.add(e),requestAnimationFrame(()=>this.subscribers.forEach(t=>t({id:e,dismiss:!0})))):this.toasts.forEach(e=>{this.subscribers.forEach(t=>t({id:e.id,dismiss:!0}))}),e),this.message=(e,t)=>this.create({...t,message:e}),this.error=(e,t)=>this.create({...t,message:e,type:"error"}),this.success=(e,t)=>this.create({...t,type:"success",message:e}),this.info=(e,t)=>this.create({...t,type:"info",message:e}),this.warning=(e,t)=>this.create({...t,type:"warning",message:e}),this.loading=(e,t)=>this.create({...t,type:"loading",message:e}),this.promise=(e,t)=>{let r,i;if(!t)return;void 0!==t.loading&&(i=this.create({...t,promise:e,type:"loading",message:t.loading,description:"function"!=typeof t.description?t.description:void 0}));let a=Promise.resolve(e instanceof Function?e():e),o=void 0!==i,l=a.then(async e=>{if(r=["resolve",e],n.isValidElement(e))o=!1,this.create({id:i,type:"default",message:e});else if(u(e)&&!e.ok){o=!1;let r="function"==typeof t.error?await t.error("HTTP error! status: ".concat(e.status)):t.error,a="function"==typeof t.description?await t.description("HTTP error! status: ".concat(e.status)):t.description,u="object"!=typeof r||n.isValidElement(r)?{message:r}:r;this.create({id:i,type:"error",description:a,...u})}else if(e instanceof Error){o=!1;let r="function"==typeof t.error?await t.error(e):t.error,a="function"==typeof t.description?await t.description(e):t.description,u="object"!=typeof r||n.isValidElement(r)?{message:r}:r;this.create({id:i,type:"error",description:a,...u})}else if(void 0!==t.success){o=!1;let r="function"==typeof t.success?await t.success(e):t.success,a="function"==typeof t.description?await t.description(e):t.description,u="object"!=typeof r||n.isValidElement(r)?{message:r}:r;this.create({id:i,type:"success",description:a,...u})}}).catch(async e=>{if(r=["reject",e],void 0!==t.error){o=!1;let r="function"==typeof t.error?await t.error(e):t.error,a="function"==typeof t.description?await t.description(e):t.description,u="object"!=typeof r||n.isValidElement(r)?{message:r}:r;this.create({id:i,type:"error",description:a,...u})}}).finally(()=>{o&&(this.dismiss(i),i=void 0),null==t.finally||t.finally.call(t)}),c=()=>new Promise((e,t)=>l.then(()=>"reject"===r[0]?t(r[1]):e(r[1])).catch(t));return"string"!=typeof i&&"number"!=typeof i?{unwrap:c}:Object.assign(i,{unwrap:c})},this.custom=(e,t)=>{let r=(null==t?void 0:t.id)||i++;return this.create({jsx:e(r),id:r,...t}),r},this.getActiveToasts=()=>this.toasts.filter(e=>!this.dismissedToasts.has(e.id)),this.subscribers=[],this.toasts=[],this.dismissedToasts=new Set}}let o=new a,u=e=>e&&"object"==typeof e&&"ok"in e&&"boolean"==typeof e.ok&&"status"in e&&"number"==typeof e.status,l=Object.assign((e,t)=>{let r=(null==t?void 0:t.id)||i++;return o.addToast({title:e,...t,id:r}),r},{success:o.success,info:o.info,warning:o.warning,error:o.error,custom:o.custom,message:o.message,promise:o.promise,dismiss:o.dismiss,loading:o.loading},{getHistory:()=>o.toasts,getToasts:()=>o.getActiveToasts()});!function(e){if(!e||"undefined"==typeof document)return;let t=document.head||document.getElementsByTagName("head")[0],r=document.createElement("style");r.type="text/css",t.appendChild(r),r.styleSheet?r.styleSheet.cssText=e:r.appendChild(document.createTextNode(e))}("[data-sonner-toaster][dir=ltr],html[dir=ltr]{--toast-icon-margin-start:-3px;--toast-icon-margin-end:4px;--toast-svg-margin-start:-1px;--toast-svg-margin-end:0px;--toast-button-margin-start:auto;--toast-button-margin-end:0;--toast-close-button-start:0;--toast-close-button-end:unset;--toast-close-button-transform:translate(-35%, -35%)}[data-sonner-toaster][dir=rtl],html[dir=rtl]{--toast-icon-margin-start:4px;--toast-icon-margin-end:-3px;--toast-svg-margin-start:0px;--toast-svg-margin-end:-1px;--toast-button-margin-start:0;--toast-button-margin-end:auto;--toast-close-button-start:unset;--toast-close-button-end:0;--toast-close-button-transform:translate(35%, -35%)}[data-sonner-toaster]{position:fixed;width:var(--width);font-family:ui-sans-serif,system-ui,-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji;--gray1:hsl(0, 0%, 99%);--gray2:hsl(0, 0%, 97.3%);--gray3:hsl(0, 0%, 95.1%);--gray4:hsl(0, 0%, 93%);--gray5:hsl(0, 0%, 90.9%);--gray6:hsl(0, 0%, 88.7%);--gray7:hsl(0, 0%, 85.8%);--gray8:hsl(0, 0%, 78%);--gray9:hsl(0, 0%, 56.1%);--gray10:hsl(0, 0%, 52.3%);--gray11:hsl(0, 0%, 43.5%);--gray12:hsl(0, 0%, 9%);--border-radius:8px;box-sizing:border-box;padding:0;margin:0;list-style:none;outline:0;z-index:999999999;transition:transform .4s ease}@media (hover:none) and (pointer:coarse){[data-sonner-toaster][data-lifted=true]{transform:none}}[data-sonner-toaster][data-x-position=right]{right:var(--offset-right)}[data-sonner-toaster][data-x-position=left]{left:var(--offset-left)}[data-sonner-toaster][data-x-position=center]{left:50%;transform:translateX(-50%)}[data-sonner-toaster][data-y-position=top]{top:var(--offset-top)}[data-sonner-toaster][data-y-position=bottom]{bottom:var(--offset-bottom)}[data-sonner-toast]{--y:translateY(100%);--lift-amount:calc(var(--lift) * var(--gap));z-index:var(--z-index);position:absolute;opacity:0;transform:var(--y);touch-action:none;transition:transform .4s,opacity .4s,height .4s,box-shadow .2s;box-sizing:border-box;outline:0;overflow-wrap:anywhere}[data-sonner-toast][data-styled=true]{padding:16px;background:var(--normal-bg);border:1px solid var(--normal-border);color:var(--normal-text);border-radius:var(--border-radius);box-shadow:0 4px 12px rgba(0,0,0,.1);width:var(--width);font-size:13px;display:flex;align-items:center;gap:6px}[data-sonner-toast]:focus-visible{box-shadow:0 4px 12px rgba(0,0,0,.1),0 0 0 2px rgba(0,0,0,.2)}[data-sonner-toast][data-y-position=top]{top:0;--y:translateY(-100%);--lift:1;--lift-amount:calc(1 * var(--gap))}[data-sonner-toast][data-y-position=bottom]{bottom:0;--y:translateY(100%);--lift:-1;--lift-amount:calc(var(--lift) * var(--gap))}[data-sonner-toast][data-styled=true] [data-description]{font-weight:400;line-height:1.4;color:#3f3f3f}[data-rich-colors=true][data-sonner-toast][data-styled=true] [data-description]{color:inherit}[data-sonner-toaster][data-sonner-theme=dark] [data-description]{color:#e8e8e8}[data-sonner-toast][data-styled=true] [data-title]{font-weight:500;line-height:1.5;color:inherit}[data-sonner-toast][data-styled=true] [data-icon]{display:flex;height:16px;width:16px;position:relative;justify-content:flex-start;align-items:center;flex-shrink:0;margin-left:var(--toast-icon-margin-start);margin-right:var(--toast-icon-margin-end)}[data-sonner-toast][data-promise=true] [data-icon]>svg{opacity:0;transform:scale(.8);transform-origin:center;animation:sonner-fade-in .3s ease forwards}[data-sonner-toast][data-styled=true] [data-icon]>*{flex-shrink:0}[data-sonner-toast][data-styled=true] [data-icon] svg{margin-left:var(--toast-svg-margin-start);margin-right:var(--toast-svg-margin-end)}[data-sonner-toast][data-styled=true] [data-content]{display:flex;flex-direction:column;gap:2px}[data-sonner-toast][data-styled=true] [data-button]{border-radius:4px;padding-left:8px;padding-right:8px;height:24px;font-size:12px;color:var(--normal-bg);background:var(--normal-text);margin-left:var(--toast-button-margin-start);margin-right:var(--toast-button-margin-end);border:none;font-weight:500;cursor:pointer;outline:0;display:flex;align-items:center;flex-shrink:0;transition:opacity .4s,box-shadow .2s}[data-sonner-toast][data-styled=true] [data-button]:focus-visible{box-shadow:0 0 0 2px rgba(0,0,0,.4)}[data-sonner-toast][data-styled=true] [data-button]:first-of-type{margin-left:var(--toast-button-margin-start);margin-right:var(--toast-button-margin-end)}[data-sonner-toast][data-styled=true] [data-cancel]{color:var(--normal-text);background:rgba(0,0,0,.08)}[data-sonner-toaster][data-sonner-theme=dark] [data-sonner-toast][data-styled=true] [data-cancel]{background:rgba(255,255,255,.3)}[data-sonner-toast][data-styled=true] [data-close-button]{position:absolute;left:var(--toast-close-button-start);right:var(--toast-close-button-end);top:0;height:20px;width:20px;display:flex;justify-content:center;align-items:center;padding:0;color:var(--gray12);background:var(--normal-bg);border:1px solid var(--gray4);transform:var(--toast-close-button-transform);border-radius:50%;cursor:pointer;z-index:1;transition:opacity .1s,background .2s,border-color .2s}[data-sonner-toast][data-styled=true] [data-close-button]:focus-visible{box-shadow:0 4px 12px rgba(0,0,0,.1),0 0 0 2px rgba(0,0,0,.2)}[data-sonner-toast][data-styled=true] [data-disabled=true]{cursor:not-allowed}[data-sonner-toast][data-styled=true]:hover [data-close-button]:hover{background:var(--gray2);border-color:var(--gray5)}[data-sonner-toast][data-swiping=true]::before{content:'';position:absolute;left:-100%;right:-100%;height:100%;z-index:-1}[data-sonner-toast][data-y-position=top][data-swiping=true]::before{bottom:50%;transform:scaleY(3) translateY(50%)}[data-sonner-toast][data-y-position=bottom][data-swiping=true]::before{top:50%;transform:scaleY(3) translateY(-50%)}[data-sonner-toast][data-swiping=false][data-removed=true]::before{content:'';position:absolute;inset:0;transform:scaleY(2)}[data-sonner-toast][data-expanded=true]::after{content:'';position:absolute;left:0;height:calc(var(--gap) + 1px);bottom:100%;width:100%}[data-sonner-toast][data-mounted=true]{--y:translateY(0);opacity:1}[data-sonner-toast][data-expanded=false][data-front=false]{--scale:var(--toasts-before) * 0.05 + 1;--y:translateY(calc(var(--lift-amount) * var(--toasts-before))) scale(calc(-1 * var(--scale)));height:var(--front-toast-height)}[data-sonner-toast]>*{transition:opacity .4s}[data-sonner-toast][data-x-position=right]{right:0}[data-sonner-toast][data-x-position=left]{left:0}[data-sonner-toast][data-expanded=false][data-front=false][data-styled=true]>*{opacity:0}[data-sonner-toast][data-visible=false]{opacity:0;pointer-events:none}[data-sonner-toast][data-mounted=true][data-expanded=true]{--y:translateY(calc(var(--lift) * var(--offset)));height:var(--initial-height)}[data-sonner-toast][data-removed=true][data-front=true][data-swipe-out=false]{--y:translateY(calc(var(--lift) * -100%));opacity:0}[data-sonner-toast][data-removed=true][data-front=false][data-swipe-out=false][data-expanded=true]{--y:translateY(calc(var(--lift) * var(--offset) + var(--lift) * -100%));opacity:0}[data-sonner-toast][data-removed=true][data-front=false][data-swipe-out=false][data-expanded=false]{--y:translateY(40%);opacity:0;transition:transform .5s,opacity .2s}[data-sonner-toast][data-removed=true][data-front=false]::before{height:calc(var(--initial-height) + 20%)}[data-sonner-toast][data-swiping=true]{transform:var(--y) translateY(var(--swipe-amount-y,0)) translateX(var(--swipe-amount-x,0));transition:none}[data-sonner-toast][data-swiped=true]{user-select:none}[data-sonner-toast][data-swipe-out=true][data-y-position=bottom],[data-sonner-toast][data-swipe-out=true][data-y-position=top]{animation-duration:.2s;animation-timing-function:ease-out;animation-fill-mode:forwards}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=left]{animation-name:swipe-out-left}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=right]{animation-name:swipe-out-right}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=up]{animation-name:swipe-out-up}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=down]{animation-name:swipe-out-down}@keyframes swipe-out-left{from{transform:var(--y) translateX(var(--swipe-amount-x));opacity:1}to{transform:var(--y) translateX(calc(var(--swipe-amount-x) - 100%));opacity:0}}@keyframes swipe-out-right{from{transform:var(--y) translateX(var(--swipe-amount-x));opacity:1}to{transform:var(--y) translateX(calc(var(--swipe-amount-x) + 100%));opacity:0}}@keyframes swipe-out-up{from{transform:var(--y) translateY(var(--swipe-amount-y));opacity:1}to{transform:var(--y) translateY(calc(var(--swipe-amount-y) - 100%));opacity:0}}@keyframes swipe-out-down{from{transform:var(--y) translateY(var(--swipe-amount-y));opacity:1}to{transform:var(--y) translateY(calc(var(--swipe-amount-y) + 100%));opacity:0}}@media (max-width:600px){[data-sonner-toaster]{position:fixed;right:var(--mobile-offset-right);left:var(--mobile-offset-left);width:100%}[data-sonner-toaster][dir=rtl]{left:calc(var(--mobile-offset-left) * -1)}[data-sonner-toaster] [data-sonner-toast]{left:0;right:0;width:calc(100% - var(--mobile-offset-left) * 2)}[data-sonner-toaster][data-x-position=left]{left:var(--mobile-offset-left)}[data-sonner-toaster][data-y-position=bottom]{bottom:var(--mobile-offset-bottom)}[data-sonner-toaster][data-y-position=top]{top:var(--mobile-offset-top)}[data-sonner-toaster][data-x-position=center]{left:var(--mobile-offset-left);right:var(--mobile-offset-right);transform:none}}[data-sonner-toaster][data-sonner-theme=light]{--normal-bg:#fff;--normal-border:var(--gray4);--normal-text:var(--gray12);--success-bg:hsl(143, 85%, 96%);--success-border:hsl(145, 92%, 87%);--success-text:hsl(140, 100%, 27%);--info-bg:hsl(208, 100%, 97%);--info-border:hsl(221, 91%, 93%);--info-text:hsl(210, 92%, 45%);--warning-bg:hsl(49, 100%, 97%);--warning-border:hsl(49, 91%, 84%);--warning-text:hsl(31, 92%, 45%);--error-bg:hsl(359, 100%, 97%);--error-border:hsl(359, 100%, 94%);--error-text:hsl(360, 100%, 45%)}[data-sonner-toaster][data-sonner-theme=light] [data-sonner-toast][data-invert=true]{--normal-bg:#000;--normal-border:hsl(0, 0%, 20%);--normal-text:var(--gray1)}[data-sonner-toaster][data-sonner-theme=dark] [data-sonner-toast][data-invert=true]{--normal-bg:#fff;--normal-border:var(--gray3);--normal-text:var(--gray12)}[data-sonner-toaster][data-sonner-theme=dark]{--normal-bg:#000;--normal-bg-hover:hsl(0, 0%, 12%);--normal-border:hsl(0, 0%, 20%);--normal-border-hover:hsl(0, 0%, 25%);--normal-text:var(--gray1);--success-bg:hsl(150, 100%, 6%);--success-border:hsl(147, 100%, 12%);--success-text:hsl(150, 86%, 65%);--info-bg:hsl(215, 100%, 6%);--info-border:hsl(223, 43%, 17%);--info-text:hsl(216, 87%, 65%);--warning-bg:hsl(64, 100%, 6%);--warning-border:hsl(60, 100%, 9%);--warning-text:hsl(46, 87%, 65%);--error-bg:hsl(358, 76%, 10%);--error-border:hsl(357, 89%, 16%);--error-text:hsl(358, 100%, 81%)}[data-sonner-toaster][data-sonner-theme=dark] [data-sonner-toast] [data-close-button]{background:var(--normal-bg);border-color:var(--normal-border);color:var(--normal-text)}[data-sonner-toaster][data-sonner-theme=dark] [data-sonner-toast] [data-close-button]:hover{background:var(--normal-bg-hover);border-color:var(--normal-border-hover)}[data-rich-colors=true][data-sonner-toast][data-type=success]{background:var(--success-bg);border-color:var(--success-border);color:var(--success-text)}[data-rich-colors=true][data-sonner-toast][data-type=success] [data-close-button]{background:var(--success-bg);border-color:var(--success-border);color:var(--success-text)}[data-rich-colors=true][data-sonner-toast][data-type=info]{background:var(--info-bg);border-color:var(--info-border);color:var(--info-text)}[data-rich-colors=true][data-sonner-toast][data-type=info] [data-close-button]{background:var(--info-bg);border-color:var(--info-border);color:var(--info-text)}[data-rich-colors=true][data-sonner-toast][data-type=warning]{background:var(--warning-bg);border-color:var(--warning-border);color:var(--warning-text)}[data-rich-colors=true][data-sonner-toast][data-type=warning] [data-close-button]{background:var(--warning-bg);border-color:var(--warning-border);color:var(--warning-text)}[data-rich-colors=true][data-sonner-toast][data-type=error]{background:var(--error-bg);border-color:var(--error-border);color:var(--error-text)}[data-rich-colors=true][data-sonner-toast][data-type=error] [data-close-button]{background:var(--error-bg);border-color:var(--error-border);color:var(--error-text)}.sonner-loading-wrapper{--size:16px;height:var(--size);width:var(--size);position:absolute;inset:0;z-index:10}.sonner-loading-wrapper[data-visible=false]{transform-origin:center;animation:sonner-fade-out .2s ease forwards}.sonner-spinner{position:relative;top:50%;left:50%;height:var(--size);width:var(--size)}.sonner-loading-bar{animation:sonner-spin 1.2s linear infinite;background:var(--gray11);border-radius:6px;height:8%;left:-10%;position:absolute;top:-3.9%;width:24%}.sonner-loading-bar:first-child{animation-delay:-1.2s;transform:rotate(.0001deg) translate(146%)}.sonner-loading-bar:nth-child(2){animation-delay:-1.1s;transform:rotate(30deg) translate(146%)}.sonner-loading-bar:nth-child(3){animation-delay:-1s;transform:rotate(60deg) translate(146%)}.sonner-loading-bar:nth-child(4){animation-delay:-.9s;transform:rotate(90deg) translate(146%)}.sonner-loading-bar:nth-child(5){animation-delay:-.8s;transform:rotate(120deg) translate(146%)}.sonner-loading-bar:nth-child(6){animation-delay:-.7s;transform:rotate(150deg) translate(146%)}.sonner-loading-bar:nth-child(7){animation-delay:-.6s;transform:rotate(180deg) translate(146%)}.sonner-loading-bar:nth-child(8){animation-delay:-.5s;transform:rotate(210deg) translate(146%)}.sonner-loading-bar:nth-child(9){animation-delay:-.4s;transform:rotate(240deg) translate(146%)}.sonner-loading-bar:nth-child(10){animation-delay:-.3s;transform:rotate(270deg) translate(146%)}.sonner-loading-bar:nth-child(11){animation-delay:-.2s;transform:rotate(300deg) translate(146%)}.sonner-loading-bar:nth-child(12){animation-delay:-.1s;transform:rotate(330deg) translate(146%)}@keyframes sonner-fade-in{0%{opacity:0;transform:scale(.8)}100%{opacity:1;transform:scale(1)}}@keyframes sonner-fade-out{0%{opacity:1;transform:scale(1)}100%{opacity:0;transform:scale(.8)}}@keyframes sonner-spin{0%{opacity:1}100%{opacity:.15}}@media (prefers-reduced-motion){.sonner-loading-bar,[data-sonner-toast],[data-sonner-toast]>*{transition:none!important;animation:none!important}}.sonner-loader{position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);transform-origin:center;transition:opacity .2s,transform .2s}.sonner-loader[data-visible=false]{opacity:0;transform:scale(.8) translate(-50%,-50%)}")}}]);