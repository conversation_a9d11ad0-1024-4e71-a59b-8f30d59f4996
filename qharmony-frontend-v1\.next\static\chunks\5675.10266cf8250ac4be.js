"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5675],{40256:function(e,s,a){a.d(s,{$R:function(){return o},A_:function(){return i},BO:function(){return r},GH:function(){return m},_n:function(){return l},be:function(){return n},iG:function(){return d},j0:function(){return c}});var t=a(83464);let l="http://localhost:8080",r="<EMAIL>,<EMAIL>,<EMAIL>".split(",").map(e=>e.trim()),n=t.Z.create({baseURL:l});async function i(e,s,a){let t=new URL(a?"".concat(a).concat(e):"".concat(l).concat(e));return s&&Object.keys(s).forEach(e=>t.searchParams.append(e,s[e])),(await n.get(t.toString())).data}async function c(e,s,a){let t=a?"".concat(a).concat(e):"".concat(l).concat(e),r=await n.post(t,s,{headers:{"Content-Type":"application/json"}});return{status:r.status,data:r.data}}async function d(e,s,a){let t=a?"".concat(a).concat(e):"".concat(l).concat(e);console.log("Document upload to: ".concat(t));let r=await n.post(t,s,{headers:{"Content-Type":"multipart/form-data"}});return{status:r.status,data:r.data}}async function o(e,s,a){let t=new URL(a?"".concat(a).concat(e):"".concat(l).concat(e));return s&&Object.keys(s).forEach(e=>t.searchParams.append(e,s[e])),console.log("GET Blob request to: ".concat(t.toString())),(await n.get(t.toString(),{responseType:"blob"})).data}async function m(e,s,a){let t=a?"".concat(a).concat(e):"".concat(l).concat(e),r=await n.put(t,s,{headers:{"Content-Type":"application/json"}});return{status:r.status,data:r.data}}n.interceptors.request.use(e=>{let s=localStorage.getItem("userid1")||localStorage.getItem("userId");return s?e.headers["user-id"]=s:console.warn("No user ID found in localStorage for API request"),e})},94705:function(e,s,a){a.r(s),a.d(s,{default:function(){return s8}});var t=a(57437),l=a(99376),r=a(2265);let n=(0,r.createContext)(null);var i=e=>{let{children:s}=e,a=(0,l.useRouter)();return(0,t.jsx)(n.Provider,{value:{navigate:e=>{if(e.startsWith("?page=")){let s="/census".concat(e);a.push(s);return}e.startsWith("/")&&(e=e.substring(1));let s="/census?page=".concat(e);a.push(s)}},children:s})},c=a(7495),d=a(90535),o=a(25829);let m=(0,d.j)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),x=r.forwardRef((e,s)=>{let{className:a,variant:l,size:r,asChild:n=!1,...i}=e,d=n?c.g7:"button";return(0,t.jsx)(d,{className:(0,o.cn)(m({variant:l,size:r,className:a})),ref:s,...i})});x.displayName="Button";let h=r.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,t.jsx)("div",{ref:s,className:(0,o.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",a),...l})});h.displayName="Card";let p=r.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,t.jsx)("div",{ref:s,className:(0,o.cn)("flex flex-col space-y-1.5 p-6",a),...l})});p.displayName="CardHeader";let u=r.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,t.jsx)("h3",{ref:s,className:(0,o.cn)("text-2xl font-semibold leading-none tracking-tight",a),...l})});u.displayName="CardTitle",r.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,t.jsx)("p",{ref:s,className:(0,o.cn)("text-sm text-muted-foreground",a),...l})}).displayName="CardDescription";let g=r.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,t.jsx)("div",{ref:s,className:(0,o.cn)("p-6 pt-0",a),...l})});g.displayName="CardContent",r.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,t.jsx)("div",{ref:s,className:(0,o.cn)("flex items-center p-6 pt-0",a),...l})}).displayName="CardFooter";var j=a(13134),b=a(3289);let f=j.fC,N=r.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,t.jsx)(j.ck,{ref:s,className:(0,o.cn)("border-b",a),...l})});N.displayName="AccordionItem";let y=r.forwardRef((e,s)=>{let{className:a,children:l,...r}=e;return(0,t.jsx)(j.h4,{className:"flex",children:(0,t.jsxs)(j.xz,{ref:s,className:(0,o.cn)("flex flex-1 items-center justify-between py-4 font-medium transition-all hover:underline [&[data-state=open]>svg]:rotate-180",a),...r,children:[l,(0,t.jsx)(b.Z,{className:"h-4 w-4 shrink-0 transition-transform duration-200"})]})})});y.displayName=j.xz.displayName;let v=r.forwardRef((e,s)=>{let{className:a,children:l,...r}=e;return(0,t.jsx)(j.VY,{ref:s,className:"overflow-hidden text-sm transition-all data-[state=closed]:animate-accordion-up data-[state=open]:animate-accordion-down",...r,children:(0,t.jsx)("div",{className:(0,o.cn)("pb-4 pt-0",a),children:l})})});v.displayName=j.VY.displayName;let w=()=>{let e=(0,r.useContext)(n);if(!e)throw Error("useNavigate must be used within NavigationProvider");return e.navigate},C=()=>({id:(0,l.useSearchParams)().get("id")}),k=()=>{let e=(0,l.useSearchParams)();return{get:s=>e.get(s),has:s=>e.has(s),toString:()=>e.toString()}};var D=a(69626),S=a(53927),P=a(45736),Z=a(91120),A=a(5255),R=a(46747),F=a(12032),E=a(81475),I=a(79282),O=a(46581),B=a(90283),z=a(68575),H=a(61146);let M=r.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,t.jsx)(H.fC,{ref:s,className:(0,o.cn)("relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full",a),...l})});M.displayName=H.fC.displayName;let T=r.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,t.jsx)(H.Ee,{ref:s,className:(0,o.cn)("aspect-square h-full w-full",a),...l})});T.displayName=H.Ee.displayName;let $=r.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,t.jsx)(H.NY,{ref:s,className:(0,o.cn)("flex h-full w-full items-center justify-center rounded-full bg-muted",a),...l})});$.displayName=H.NY.displayName;var L=a(70085),U=a(76848),V=a(59559),G=a(78084);let Y=L.fC,_=L.xz;L.ZA,L.Uv,L.Tr,L.Ee,r.forwardRef((e,s)=>{let{className:a,inset:l,children:r,...n}=e;return(0,t.jsxs)(L.fF,{ref:s,className:(0,o.cn)("flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent",l&&"pl-8",a),...n,children:[r,(0,t.jsx)(U.Z,{className:"ml-auto h-4 w-4"})]})}).displayName=L.fF.displayName,r.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,t.jsx)(L.tu,{ref:s,className:(0,o.cn)("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",a),...l})}).displayName=L.tu.displayName;let W=r.forwardRef((e,s)=>{let{className:a,sideOffset:l=4,...r}=e;return(0,t.jsx)(L.Uv,{children:(0,t.jsx)(L.VY,{ref:s,sideOffset:l,className:(0,o.cn)("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",a),...r})})});W.displayName=L.VY.displayName;let q=r.forwardRef((e,s)=>{let{className:a,inset:l,...r}=e;return(0,t.jsx)(L.ck,{ref:s,className:(0,o.cn)("relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",l&&"pl-8",a),...r})});q.displayName=L.ck.displayName,r.forwardRef((e,s)=>{let{className:a,children:l,checked:r,...n}=e;return(0,t.jsxs)(L.oC,{ref:s,className:(0,o.cn)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",a),checked:r,...n,children:[(0,t.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,t.jsx)(L.wU,{children:(0,t.jsx)(V.Z,{className:"h-4 w-4"})})}),l]})}).displayName=L.oC.displayName,r.forwardRef((e,s)=>{let{className:a,children:l,...r}=e;return(0,t.jsxs)(L.Rk,{ref:s,className:(0,o.cn)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",a),...r,children:[(0,t.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,t.jsx)(L.wU,{children:(0,t.jsx)(G.Z,{className:"h-2 w-2 fill-current"})})}),l]})}).displayName=L.Rk.displayName,r.forwardRef((e,s)=>{let{className:a,inset:l,...r}=e;return(0,t.jsx)(L.__,{ref:s,className:(0,o.cn)("px-2 py-1.5 text-sm font-semibold",l&&"pl-8",a),...r})}).displayName=L.__.displayName;let J=r.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,t.jsx)(L.Z0,{ref:s,className:(0,o.cn)("-mx-1 my-1 h-px bg-muted",a),...l})});J.displayName=L.Z0.displayName;var X=a(67350),K=a(40339),Q=a(3683),ee=a(30166);let es=(0,ee.default)(()=>Promise.all([a.e(3463),a.e(187),a.e(3919),a.e(2786),a.e(9826),a.e(9414),a.e(7404),a.e(3209),a.e(7571),a.e(2170),a.e(8005),a.e(7752)]).then(a.bind(a,98005)),{loadableGenerated:{webpack:()=>[98005]},ssr:!1});var ea=e=>{let{className:s=""}=e,a=w(),[l,n]=(0,r.useState)(!1),[i,c]=(0,r.useState)(!1),[d,o]=(0,r.useState)(""),[m,h]=(0,r.useState)(""),p=(0,z.v9)(e=>e.user.userProfile),u=(0,z.v9)(e=>e.user._id);return((0,r.useEffect)(()=>{let e=localStorage.getItem("userid1")||localStorage.getItem("userId"),s=p&&(p.name||p.email);e&&s?(c(!0),o(p.name||"User"),h(p.email||"")):e?(c(!0),o("User"),h("")):c(!1)},[p,u]),i)?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)(Y,{children:[(0,t.jsx)(_,{asChild:!0,children:(0,t.jsxs)(x,{variant:"ghost",className:"flex items-center gap-2 ".concat(s),children:[(0,t.jsxs)(M,{className:"h-8 w-8",children:[(0,t.jsx)(T,{src:"",alt:d}),(0,t.jsx)($,{className:"bg-gradient-to-r from-blue-600 to-purple-600 text-white text-sm",children:d.split(" ").map(e=>e.charAt(0)).join("").toUpperCase().slice(0,2)})]}),(0,t.jsxs)("div",{className:"flex flex-col items-start",children:[(0,t.jsx)("span",{className:"text-sm font-medium",children:d}),m&&(0,t.jsx)("span",{className:"text-xs text-gray-500",children:m})]}),(0,t.jsx)(b.Z,{className:"h-4 w-4"})]})}),(0,t.jsxs)(W,{className:"w-56 bg-white dark:bg-gray-800 border shadow-lg z-50",align:"end",sideOffset:5,children:[(0,t.jsxs)(q,{onClick:()=>{n(!0)},className:"flex items-center gap-2 hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer",children:[(0,t.jsx)(K.Z,{className:"h-4 w-4"}),"Edit Profile"]}),(0,t.jsx)(J,{}),(0,t.jsxs)(q,{onClick:()=>{localStorage.removeItem("userid1"),localStorage.removeItem("userId"),localStorage.removeItem("companyId1"),localStorage.removeItem("companyId"),a("?page=login-prompt"),window.location.reload()},className:"flex items-center gap-2 hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer text-red-600",children:[(0,t.jsx)(Q.Z,{className:"h-4 w-4"}),"Sign Out"]})]})]}),l&&(0,t.jsx)(es,{open:l,onClose:()=>n(!1)})]}):(0,t.jsxs)(x,{onClick:()=>{a("?page=login-prompt")},className:"bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white ".concat(s),children:[(0,t.jsx)(X.Z,{className:"mr-2 h-4 w-4"}),"Sign In"]})},et=()=>{let e=w(),[s,a]=(0,r.useState)(!1);return(0,r.useEffect)(()=>{let e=localStorage.getItem("userid1")||localStorage.getItem("userId"),s=localStorage.getItem("ssoDone1"),t=localStorage.getItem("userEmail1");console.log("Census Index Auth Check:",{userId:e,ssoDone:s,userEmail:t});let l=e&&("true"===s||t);console.log("Census Index - User authenticated:",l),a(!!l)},[]),(0,t.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-slate-50 to-blue-50",children:[(0,t.jsx)("header",{className:"border-b bg-white/90 backdrop-blur-sm sticky top-0 z-50",children:(0,t.jsxs)("div",{className:"max-w-6xl mx-auto px-3 sm:px-4 py-3 flex justify-between items-center",children:[(0,t.jsxs)("button",{onClick:()=>e("/"),className:"flex items-center space-x-2 hover:scale-105 transition-transform duration-200 cursor-pointer",children:[(0,t.jsx)("img",{src:"/logo_cesus.png",alt:"BenOsphere Logo",className:"w-8 h-8"}),(0,t.jsx)("span",{className:"text-xl sm:text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent",children:"BenOsphere"})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2 sm:space-x-4",children:[(0,t.jsx)(x,{variant:"ghost",size:"sm",onClick:()=>e("/pricing"),className:"hidden sm:inline-flex",children:"Pricing"}),(0,t.jsx)(x,{variant:"ghost",size:"sm",onClick:()=>e("/dashboard"),className:"hidden sm:inline-flex",children:"Dashboard"}),(0,t.jsx)(ea,{})]})]})}),(0,t.jsx)("section",{className:"border-b border-gray-200/60 bg-gradient-to-br from-slate-50 to-blue-50 relative",children:(0,t.jsxs)("main",{className:"max-w-6xl mx-auto px-3 sm:px-4 py-12 sm:py-20",children:[(0,t.jsxs)("div",{className:"absolute inset-0 overflow-hidden pointer-events-none",children:[(0,t.jsx)("div",{className:"absolute top-20 left-10 w-2 h-2 bg-blue-400 rounded-full opacity-40"}),(0,t.jsx)("div",{className:"absolute top-32 right-20 w-2 h-2 bg-purple-400 rounded-full opacity-40"}),(0,t.jsx)("div",{className:"absolute bottom-40 left-20 w-2 h-2 bg-blue-400 rounded-full opacity-40"}),(0,t.jsx)("div",{className:"absolute bottom-20 right-10 w-2 h-2 bg-purple-400 rounded-full opacity-40"}),(0,t.jsx)("div",{className:"absolute top-40 left-1/4 opacity-20",children:(0,t.jsx)(D.Z,{className:"w-4 h-4 text-gray-400"})}),(0,t.jsx)("div",{className:"absolute top-60 right-1/4 opacity-20",children:(0,t.jsx)(D.Z,{className:"w-4 h-4 text-gray-400"})}),(0,t.jsx)("div",{className:"absolute bottom-60 left-1/3 opacity-20",children:(0,t.jsx)(D.Z,{className:"w-4 h-4 text-gray-400"})}),(0,t.jsx)("div",{className:"absolute bottom-40 right-1/3 opacity-20",children:(0,t.jsx)(D.Z,{className:"w-4 h-4 text-gray-400"})})]}),(0,t.jsxs)("div",{className:"text-center relative z-10 mb-16",children:[(0,t.jsx)("div",{className:"mb-6",children:(0,t.jsx)("span",{className:"inline-block bg-blue-100 text-blue-800 text-sm font-medium px-4 py-2 rounded-full",children:"Revenue Engine for SMB Employee Benefit Brokers"})}),(0,t.jsxs)("h1",{className:"text-4xl sm:text-5xl lg:text-6xl font-bold text-gray-900 mb-6 leading-tight",children:["Upload a Census.",(0,t.jsx)("br",{}),(0,t.jsx)("span",{className:"bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent",children:"Unlock Smart Benefits"})]}),(0,t.jsx)("p",{className:"text-xl sm:text-2xl text-gray-600 mb-8 max-w-3xl mx-auto leading-relaxed",children:"BenOsphere transforms raw census files into intelligent, broker-ready reports in under 60 seconds."}),(0,t.jsxs)("div",{className:"flex flex-col items-center space-y-4 mb-8",children:[(0,t.jsxs)(x,{size:"lg",className:"bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-8 py-4 text-lg rounded-full shadow-lg hover:shadow-xl transition-all duration-200 hover:scale-105",onClick:()=>{s?e("/upload-census"):e("/login-prompt")},children:["Upload Census & See Instant Insight",(0,t.jsx)(S.Z,{className:"ml-2 h-5 w-5"})]}),(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("p",{className:"text-sm text-gray-600 mb-2",children:"Have the file? Give it a spin — it's free."}),(0,t.jsxs)(x,{variant:"ghost",size:"sm",className:"text-blue-600 hover:text-blue-700 underline text-sm",onClick:()=>{let e=new Blob(["Employee Name,Date of Birth,Gender,Coverage Tier,Annual Salary,Department\nJohn Doe,1985-06-15,Male,Employee + Spouse,65000,Marketing\nJane Smith,1990-03-22,Female,Employee Only,58000,Sales\nMike Johnson,1982-11-08,Male,Employee + Family,72000,Engineering"],{type:"text/csv"}),s=window.URL.createObjectURL(e),a=document.createElement("a");a.href=s,a.download="census_template.csv",document.body.appendChild(a),a.click(),document.body.removeChild(a),window.URL.revokeObjectURL(s)},children:[(0,t.jsx)(P.Z,{className:"mr-1 h-4 w-4"}),"Download Template"]})]})]})]}),(0,t.jsxs)("div",{className:"grid md:grid-cols-3 gap-8 relative z-10",children:[(0,t.jsx)("div",{className:"relative",children:(0,t.jsxs)("div",{className:"bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2",children:[(0,t.jsx)("div",{className:"w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center mb-4",children:(0,t.jsx)(Z.Z,{className:"w-6 h-6 text-blue-600"})}),(0,t.jsx)("h3",{className:"font-semibold text-gray-900 mb-2",children:"Save Hours of Manual Work"}),(0,t.jsx)("p",{className:"text-gray-600 text-sm",children:"No more reformatting spreadsheets or chasing employers for data"})]})}),(0,t.jsx)("div",{className:"relative",children:(0,t.jsx)("div",{className:"bg-gradient-to-br from-blue-50 to-purple-50 rounded-2xl p-8 shadow-xl border border-blue-100",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"w-16 h-16 bg-gradient-to-r from-blue-600 to-purple-600 rounded-2xl flex items-center justify-center mx-auto mb-4",children:(0,t.jsx)(A.Z,{className:"w-8 h-8 text-white"})}),(0,t.jsx)("h3",{className:"text-2xl font-bold text-gray-900 mb-2",children:"Instant Analysis"}),(0,t.jsx)("p",{className:"text-gray-600",children:"AI-powered insights with demographics, plan recommendations, and risk analysis"})]})})}),(0,t.jsx)("div",{className:"relative",children:(0,t.jsxs)("div",{className:"bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2",children:[(0,t.jsx)("div",{className:"w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center mb-4",children:(0,t.jsx)(A.Z,{className:"w-6 h-6 text-green-600"})}),(0,t.jsx)("h3",{className:"font-semibold text-gray-900 mb-2",children:"Win More Deals"}),(0,t.jsx)("p",{className:"text-gray-600 text-sm",children:"Faster turnaround with smart upsell opportunities and cost-saving insights"})]})})]})]})}),(0,t.jsx)("section",{className:"bg-white py-16",children:(0,t.jsx)("div",{className:"max-w-6xl mx-auto px-3 sm:px-4",children:(0,t.jsx)(h,{className:"bg-gradient-to-br from-purple-50 to-blue-50 border-purple-200 shadow-xl",children:(0,t.jsxs)(g,{className:"p-8 text-center",children:[(0,t.jsxs)("div",{className:"mb-6",children:[(0,t.jsx)("div",{className:"w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,t.jsx)(R.Z,{className:"w-8 h-8 text-purple-600"})}),(0,t.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-3",children:"Don't Have the Census File?"}),(0,t.jsx)("p",{className:"text-lg text-gray-600 mb-6 max-w-2xl mx-auto",children:"Send a secure link to your client — they'll upload their census directly. You'll be notified when their enriched report is ready."})]}),(0,t.jsxs)(x,{variant:"outline",size:"lg",className:"px-8 py-4 text-lg rounded-full border-2 border-purple-300 hover:bg-purple-50 hover:border-purple-400 transition-all duration-200",onClick:()=>e("/employer-invite"),children:[(0,t.jsx)(R.Z,{className:"mr-2 h-5 w-5"}),"Invite Employer to Upload"]}),(0,t.jsx)("p",{className:"text-sm text-gray-500 mt-4",children:"Secure, guided upload process • HIPAA compliant • Instant notifications"})]})})})}),(0,t.jsx)("section",{className:"bg-gray-50 py-12",children:(0,t.jsx)("div",{className:"max-w-6xl mx-auto px-3 sm:px-4",children:(0,t.jsx)(h,{className:"shadow-lg mb-8 sm:mb-12 border-0 bg-gradient-to-r from-red-50 to-orange-50 border-red-100",children:(0,t.jsxs)(g,{className:"p-6 sm:p-8",children:[(0,t.jsxs)("div",{className:"text-center mb-6",children:[(0,t.jsxs)("div",{className:"flex items-center justify-center mb-4",children:[(0,t.jsx)(F.Z,{className:"h-8 w-8 text-red-600 mr-3"}),(0,t.jsx)("h3",{className:"text-2xl sm:text-3xl font-bold text-gray-900",children:"The Problem"})]}),(0,t.jsx)("p",{className:"text-lg text-gray-700 mb-6",children:"Manual census workflows are broken."}),(0,t.jsx)("p",{className:"text-gray-600 text-base leading-relaxed mb-6",children:"Brokers waste hours reformatting spreadsheets, chasing employers, and entering data by hand — only to miss insights that could win deals."})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center bg-white/70 rounded-lg p-4",children:[(0,t.jsx)("div",{className:"bg-red-100 w-12 h-12 rounded-full flex items-center justify-center mr-4 flex-shrink-0",children:(0,t.jsx)(E.Z,{className:"h-6 w-6 text-red-600"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-semibold text-red-800 mb-1",children:"Time Drain"}),(0,t.jsx)("p",{className:"text-gray-600 text-sm",children:"Hours spent on manual data entry and reformatting instead of selling"})]})]}),(0,t.jsxs)("div",{className:"flex items-center bg-white/70 rounded-lg p-4",children:[(0,t.jsx)("div",{className:"bg-orange-100 w-12 h-12 rounded-full flex items-center justify-center mr-4 flex-shrink-0",children:(0,t.jsx)(I.Z,{className:"h-6 w-6 text-orange-600"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-semibold text-orange-800 mb-1",children:"Lost Opportunities"}),(0,t.jsx)("p",{className:"text-gray-600 text-sm",children:"Delays in turnaround mean competitors win the business"})]})]}),(0,t.jsxs)("div",{className:"flex items-center bg-white/70 rounded-lg p-4",children:[(0,t.jsx)("div",{className:"bg-yellow-100 w-12 h-12 rounded-full flex items-center justify-center mr-4 flex-shrink-0",children:(0,t.jsx)(O.Z,{className:"h-6 w-6 text-yellow-600"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-semibold text-yellow-800 mb-1",children:"Missed Insights"}),(0,t.jsx)("p",{className:"text-gray-600 text-sm",children:"Raw data without enrichment means no upsell opportunities"})]})]})]})]})})})}),(0,t.jsx)("section",{className:"bg-white py-16",children:(0,t.jsxs)("div",{className:"max-w-6xl mx-auto px-3 sm:px-4",children:[(0,t.jsxs)("div",{className:"text-center mb-12",children:[(0,t.jsx)("h2",{className:"text-3xl sm:text-4xl font-bold text-gray-900 mb-4",children:"CensusIQ Capabilities"}),(0,t.jsx)("p",{className:"text-xl text-gray-600",children:"Turn data dust into gold."})]}),(0,t.jsxs)("div",{className:"grid md:grid-cols-2 lg:grid-cols-3 gap-6",children:[(0,t.jsx)("div",{className:"bg-white rounded-xl p-6 shadow-lg border border-gray-200 hover:shadow-xl transition-all duration-300",children:(0,t.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,t.jsx)("div",{className:"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0 mt-1",children:(0,t.jsx)(I.Z,{className:"w-4 h-4 text-blue-600"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"font-semibold text-gray-900 mb-2",children:"Enrich census data with industry trends and health risk insights"}),(0,t.jsxs)("p",{className:"text-gray-600 text-sm flex items-center",children:[(0,t.jsx)("span",{className:"mr-2",children:"→"}),"See beyond basic demographics."]})]})]})}),(0,t.jsx)("div",{className:"bg-white rounded-xl p-6 shadow-lg border border-gray-200 hover:shadow-xl transition-all duration-300",children:(0,t.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,t.jsx)("div",{className:"w-8 h-8 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0 mt-1",children:(0,t.jsx)(D.Z,{className:"w-4 h-4 text-green-600"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"font-semibold text-gray-900 mb-2",children:"Identify hidden upsell opportunities"}),(0,t.jsxs)("p",{className:"text-gray-600 text-sm flex items-center",children:[(0,t.jsx)("span",{className:"mr-2",children:"→"}),"Stop missing revenue chances."]})]})]})}),(0,t.jsx)("div",{className:"bg-white rounded-xl p-6 shadow-lg border border-gray-200 hover:shadow-xl transition-all duration-300",children:(0,t.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,t.jsx)("div",{className:"w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center flex-shrink-0 mt-1",children:(0,t.jsx)(Z.Z,{className:"w-4 h-4 text-purple-600"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"font-semibold text-gray-900 mb-2",children:"Benchmark benefits against peers"}),(0,t.jsxs)("p",{className:"text-gray-600 text-sm flex items-center",children:[(0,t.jsx)("span",{className:"mr-2",children:"→"}),"Stay competitive in your market."]})]})]})}),(0,t.jsx)("div",{className:"bg-white rounded-xl p-6 shadow-lg border border-gray-200 hover:shadow-xl transition-all duration-300",children:(0,t.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,t.jsx)("div",{className:"w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center flex-shrink-0 mt-1",children:(0,t.jsx)(B.Z,{className:"w-4 h-4 text-orange-600"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"font-semibold text-gray-900 mb-2",children:"Tie insights to real-dollar savings"}),(0,t.jsxs)("p",{className:"text-gray-600 text-sm flex items-center",children:[(0,t.jsx)("span",{className:"mr-2",children:"→"}),"Prove the value of your strategies."]})]})]})}),(0,t.jsx)("div",{className:"bg-white rounded-xl p-6 shadow-lg border border-gray-200 hover:shadow-xl transition-all duration-300",children:(0,t.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,t.jsx)("div",{className:"w-8 h-8 bg-indigo-100 rounded-full flex items-center justify-center flex-shrink-0 mt-1",children:(0,t.jsx)(E.Z,{className:"w-4 h-4 text-indigo-600"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"font-semibold text-gray-900 mb-2",children:"Keep your strategies fresh year-round"}),(0,t.jsxs)("p",{className:"text-gray-600 text-sm flex items-center",children:[(0,t.jsx)("span",{className:"mr-2",children:"→"}),"Avoid insights going stale after renewal."]})]})]})}),(0,t.jsx)("div",{className:"bg-white rounded-xl p-6 shadow-lg border border-gray-200 hover:shadow-xl transition-all duration-300",children:(0,t.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,t.jsx)("div",{className:"w-8 h-8 bg-pink-100 rounded-full flex items-center justify-center flex-shrink-0 mt-1",children:(0,t.jsx)(A.Z,{className:"w-4 h-4 text-pink-600"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"font-semibold text-gray-900 mb-2",children:"Turn raw data into clear recommendations"}),(0,t.jsxs)("p",{className:"text-gray-600 text-sm flex items-center",children:[(0,t.jsx)("span",{className:"mr-2",children:"→"}),"Eliminate guesswork and manual analysis."]})]})]})})]})]})}),(0,t.jsx)("section",{className:"bg-white py-12",children:(0,t.jsx)("div",{className:"max-w-6xl mx-auto px-3 sm:px-4",children:(0,t.jsx)(h,{className:"shadow-lg mb-8 sm:mb-12 border-0 bg-gradient-to-r from-green-50 to-emerald-50 border-green-100",children:(0,t.jsxs)(g,{className:"p-6 sm:p-8",children:[(0,t.jsxs)("div",{className:"text-center mb-8",children:[(0,t.jsxs)("div",{className:"flex items-center justify-center mb-4",children:[(0,t.jsx)(A.Z,{className:"h-8 w-8 text-green-600 mr-3"}),(0,t.jsx)("h3",{className:"text-2xl sm:text-3xl font-bold text-gray-900",children:"The Solution: Smart Census, Instant Insights"})]}),(0,t.jsx)("p",{className:"text-lg text-gray-700 mb-4",children:"BenOsphere transforms messy census files into intelligent, broker-ready reports — in under 60 seconds."}),(0,t.jsx)("p",{className:"text-gray-600 text-base leading-relaxed mb-6",children:"No formatting. No delays. Just real-time enrichment that drives faster quotes, smarter upsells, and a better client experience."})]}),(0,t.jsxs)("div",{className:"grid lg:grid-cols-2 gap-8 items-center",children:[(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-start bg-white/70 rounded-lg p-4",children:[(0,t.jsx)("div",{className:"bg-blue-100 w-10 h-10 rounded-full flex items-center justify-center mr-4 flex-shrink-0 mt-1",children:(0,t.jsx)("span",{className:"text-lg",children:"\uD83D\uDCE4"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-semibold mb-2",children:"1. Upload Your Census File"}),(0,t.jsx)("p",{className:"text-sm text-gray-600",children:"Simply drag and drop your census file or send a secure invitation link directly to your employer."})]})]}),(0,t.jsxs)("div",{className:"flex items-start bg-white/70 rounded-lg p-4",children:[(0,t.jsx)("div",{className:"bg-purple-100 w-10 h-10 rounded-full flex items-center justify-center mr-4 flex-shrink-0 mt-1",children:(0,t.jsx)("span",{className:"text-lg",children:"\uD83E\uDD16"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-semibold mb-2",children:"2. Instant AI Analysis"}),(0,t.jsx)("p",{className:"text-sm text-gray-600",children:"Our AI instantly processes and enriches your data with dependent analysis, age demographics, plan recommendations, and risk insights."})]})]}),(0,t.jsxs)("div",{className:"flex items-start bg-white/70 rounded-lg p-4",children:[(0,t.jsx)("div",{className:"bg-green-100 w-10 h-10 rounded-full flex items-center justify-center mr-4 flex-shrink-0 mt-1",children:(0,t.jsx)("span",{className:"text-lg",children:"\uD83D\uDC40"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-semibold mb-2",children:"3. Get Your Smart Preview"}),(0,t.jsx)("p",{className:"text-sm text-gray-600",children:"View key insights instantly: group size, average age, family coverage patterns, and our recommended plan strategy."})]})]}),(0,t.jsxs)("div",{className:"flex items-start bg-white/70 rounded-lg p-4",children:[(0,t.jsx)("div",{className:"bg-orange-100 w-10 h-10 rounded-full flex items-center justify-center mr-4 flex-shrink-0 mt-1",children:(0,t.jsx)("span",{className:"text-lg",children:"\uD83D\uDD13"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-semibold mb-2",children:"4. Access Full Report & Insights"}),(0,t.jsx)("p",{className:"text-sm text-gray-600",children:"Sign in to unlock detailed cost-saving opportunities, contribution benchmarks, and tailored upsell recommendations — ready to present or quote."})]})]})]}),(0,t.jsx)("div",{children:(0,t.jsxs)("div",{className:"bg-white/80 rounded-xl p-4 shadow-inner",children:[(0,t.jsx)("img",{src:"/demoimage.png",alt:"BenOsphere dashboard showing enriched census data and insights",className:"w-full h-auto rounded-lg shadow-lg hover:scale-105 transition-transform duration-300"}),(0,t.jsx)("p",{className:"text-center text-sm text-gray-500 mt-2",children:"Live census analysis and insights dashboard"})]})})]})]})})})}),(0,t.jsx)("section",{className:"bg-white py-16",children:(0,t.jsxs)("div",{className:"max-w-6xl mx-auto px-3 sm:px-4",children:[(0,t.jsxs)("div",{className:"text-center mb-12",children:[(0,t.jsx)("h2",{className:"text-3xl sm:text-4xl font-bold text-gray-900 mb-4",children:"BenOsphere – FAQs for Brokers"}),(0,t.jsx)("p",{className:"text-xl text-gray-600",children:"Everything you need to know about transforming census data into revenue opportunities."})]}),(0,t.jsx)("div",{className:"bg-white rounded-2xl shadow-lg border border-gray-200 p-6 sm:p-8 max-w-4xl mx-auto",children:(0,t.jsxs)(f,{type:"single",collapsible:!0,className:"space-y-2",children:[(0,t.jsxs)(N,{value:"item-1",className:"border border-gray-200 rounded-lg",children:[(0,t.jsx)(y,{className:"text-left font-semibold text-gray-900 hover:text-blue-600 px-6 py-4",children:"How is BenOsphere different from tools like Employee Navigator or Nayya?"}),(0,t.jsx)(v,{className:"text-gray-600 leading-relaxed px-6 pb-4",children:"Employee Navigator stores and manages census data but doesn't analyze it in depth. Nayya helps employees choose benefits but focuses on the individual user experience. BenOsphere is built for brokers—it analyzes census data to uncover revenue opportunities, benchmark benefits, and show cost-saving strategies you can bring to clients."})]}),(0,t.jsxs)(N,{value:"item-2",className:"border border-gray-200 rounded-lg",children:[(0,t.jsx)(y,{className:"text-left font-semibold text-gray-900 hover:text-blue-600 px-6 py-4",children:"I already have census data. Why do I need BenOsphere?"}),(0,t.jsx)(v,{className:"text-gray-600 leading-relaxed px-6 pb-4",children:"Spreadsheets alone don't drive revenue or prove value. BenOsphere transforms raw census data into insights and specific recommendations—so you can walk into client meetings with actionable ideas, not just data files."})]}),(0,t.jsxs)(N,{value:"item-3",className:"border border-gray-200 rounded-lg",children:[(0,t.jsx)(y,{className:"text-left font-semibold text-gray-900 hover:text-blue-600 px-6 py-4",children:"How does BenOsphere help me save costs or make money?"}),(0,t.jsx)(v,{className:"text-gray-600 leading-relaxed px-6 pb-4",children:"BenOsphere shows where clients might be overspending and helps you recommend smarter plan designs that save them money. It also flags upsell opportunities, helping you grow revenue without adding more clients."})]}),(0,t.jsxs)(N,{value:"item-4",className:"border border-gray-200 rounded-lg",children:[(0,t.jsx)(y,{className:"text-left font-semibold text-gray-900 hover:text-blue-600 px-6 py-4",children:"We're a small broker agency—why do we need this?"}),(0,t.jsx)(v,{className:"text-gray-600 leading-relaxed px-6 pb-4",children:"We built BenOsphere exactly for smaller agencies like yours. Big firms have expensive analytics teams and tools that smaller brokers can't afford. BenOsphere levels the playing field, helping you act like a consultant, not just a broker—and deliver insights that set you apart."})]}),(0,t.jsxs)(N,{value:"item-5",className:"border border-gray-200 rounded-lg",children:[(0,t.jsx)(y,{className:"text-left font-semibold text-gray-900 hover:text-blue-600 px-6 py-4",children:"Is BenOsphere expensive or hard to implement?"}),(0,t.jsx)(v,{className:"text-gray-600 leading-relaxed px-6 pb-4",children:"No. BenOsphere is affordable for brokers of all sizes, with no complex integrations required. You simply upload census files and get insights back quickly."})]}),(0,t.jsxs)(N,{value:"item-6",className:"border border-gray-200 rounded-lg",children:[(0,t.jsx)(y,{className:"text-left font-semibold text-gray-900 hover:text-blue-600 px-6 py-4",children:"Is this only useful during renewal season?"}),(0,t.jsx)(v,{className:"text-gray-600 leading-relaxed px-6 pb-4",children:"Not at all. BenOsphere gives you insights you can revisit every quarter, helping you stay engaged with clients year-round—and proving your ongoing value beyond just renewals."})]})]})})]})}),(0,t.jsx)("footer",{className:"bg-gray-900 text-white py-8",children:(0,t.jsx)("div",{className:"max-w-6xl mx-auto px-3 sm:px-4",children:(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row justify-between items-center space-y-4 sm:space-y-0",children:[(0,t.jsxs)("div",{className:"text-center sm:text-left",children:[(0,t.jsx)("button",{onClick:()=>e("/"),className:"flex items-center space-x-2 hover:scale-105 transition-transform duration-200 cursor-pointer mb-2",children:(0,t.jsx)("span",{className:"text-lg font-bold bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent",children:"BenOsphere"})}),(0,t.jsx)("p",{className:"text-gray-400 text-sm",children:"Built in Seattle"})]}),(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row items-center space-y-2 sm:space-y-0 sm:space-x-6",children:[(0,t.jsx)("button",{className:"text-gray-300 hover:text-white text-sm transition-colors",children:"Privacy Policy"}),(0,t.jsx)("button",{className:"text-gray-300 hover:text-white text-sm transition-colors",children:"Terms & Conditions"})]})]})})})]})};let el=r.forwardRef((e,s)=>{let{className:a,type:l,...r}=e;return(0,t.jsx)("input",{type:l,className:(0,o.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",a),ref:s,...r})});el.displayName="Input";var er=a(93402),en=a(47969);let ei=er.fC;er.ZA;let ec=er.B4,ed=r.forwardRef((e,s)=>{let{className:a,children:l,...r}=e;return(0,t.jsxs)(er.xz,{ref:s,className:(0,o.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",a),...r,children:[l,(0,t.jsx)(er.JO,{asChild:!0,children:(0,t.jsx)(b.Z,{className:"h-4 w-4 opacity-50"})})]})});ed.displayName=er.xz.displayName;let eo=r.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,t.jsx)(er.u_,{ref:s,className:(0,o.cn)("flex cursor-default items-center justify-center py-1",a),...l,children:(0,t.jsx)(en.Z,{className:"h-4 w-4"})})});eo.displayName=er.u_.displayName;let em=r.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,t.jsx)(er.$G,{ref:s,className:(0,o.cn)("flex cursor-default items-center justify-center py-1",a),...l,children:(0,t.jsx)(b.Z,{className:"h-4 w-4"})})});em.displayName=er.$G.displayName;let ex=r.forwardRef((e,s)=>{let{className:a,children:l,position:r="popper",...n}=e;return(0,t.jsx)(er.h_,{children:(0,t.jsxs)(er.VY,{ref:s,className:(0,o.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===r&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",a),position:r,...n,children:[(0,t.jsx)(eo,{}),(0,t.jsx)(er.l_,{className:(0,o.cn)("p-1","popper"===r&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:l}),(0,t.jsx)(em,{})]})})});ex.displayName=er.VY.displayName,r.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,t.jsx)(er.__,{ref:s,className:(0,o.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",a),...l})}).displayName=er.__.displayName;let eh=r.forwardRef((e,s)=>{let{className:a,children:l,...r}=e;return(0,t.jsxs)(er.ck,{ref:s,className:(0,o.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",a),...r,children:[(0,t.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,t.jsx)(er.wU,{children:(0,t.jsx)(V.Z,{className:"h-4 w-4"})})}),(0,t.jsx)(er.eT,{children:l})]})});eh.displayName=er.ck.displayName,r.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,t.jsx)(er.Z0,{ref:s,className:(0,o.cn)("-mx-1 my-1 h-px bg-muted",a),...l})}).displayName=er.Z0.displayName;var ep=a(23818),eu=a(16994),eg=a(54585),ej=a(16275),eb=a(5252),ef=a(73932),eN=a(54887),ey=a(5341);let ev=(0,ee.default)(()=>Promise.all([a.e(3145),a.e(5244)]).then(a.bind(a,45244)),{loadableGenerated:{webpack:()=>[45244]},ssr:!1});var ew=e=>{let{context:s="",size:a="default",variant:l="default"}=e,[n,i]=(0,r.useState)(!1);return(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)(x,{size:a,variant:l,className:"flex items-center gap-2",onClick:()=>i(!0),children:[(0,t.jsx)(ey.Z,{className:"h-4 w-4"}),"Ask Brea"]}),n&&"undefined"!=typeof document&&(0,eN.createPortal)((0,t.jsx)("div",{style:{position:"fixed",top:0,left:0,right:0,bottom:0,zIndex:99999,pointerEvents:"auto"},children:(0,t.jsx)(ev,{isOpen:n,onClose:()=>i(!1)})}),document.body)]})},eC=a(96818),ek=a(99049),eD=a(59701),eS=a(29935),eP=a(87928),eZ=a(54233),eA=a(63390),eR=a(20271);let eF=eR.fC,eE=r.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,t.jsx)(eR.aV,{ref:s,className:(0,o.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",a),...l})});eE.displayName=eR.aV.displayName;let eI=r.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,t.jsx)(eR.xz,{ref:s,className:(0,o.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",a),...l})});eI.displayName=eR.xz.displayName;let eO=r.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,t.jsx)(eR.VY,{ref:s,className:(0,o.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",a),...l})});eO.displayName=eR.VY.displayName;var eB=a(49027),ez=a(44986);let eH=eB.fC,eM=eB.xz,eT=eB.h_;eB.x8;let e$=r.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,t.jsx)(eB.aV,{ref:s,className:(0,o.cn)("fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",a),...l})});e$.displayName=eB.aV.displayName;let eL=r.forwardRef((e,s)=>{let{className:a,children:l,...r}=e;return(0,t.jsxs)(eT,{children:[(0,t.jsx)(e$,{}),(0,t.jsxs)(eB.VY,{ref:s,className:(0,o.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",a),...r,children:[l,(0,t.jsxs)(eB.x8,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[(0,t.jsx)(ez.Z,{className:"h-4 w-4"}),(0,t.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})});eL.displayName=eB.VY.displayName;let eU=e=>{let{className:s,...a}=e;return(0,t.jsx)("div",{className:(0,o.cn)("flex flex-col space-y-1.5 text-center sm:text-left",s),...a})};eU.displayName="DialogHeader";let eV=e=>{let{className:s,...a}=e;return(0,t.jsx)("div",{className:(0,o.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",s),...a})};eV.displayName="DialogFooter";let eG=r.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,t.jsx)(eB.Dx,{ref:s,className:(0,o.cn)("text-lg font-semibold leading-none tracking-tight",a),...l})});eG.displayName=eB.Dx.displayName;let eY=r.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,t.jsx)(eB.dk,{ref:s,className:(0,o.cn)("text-sm text-muted-foreground",a),...l})});eY.displayName=eB.dk.displayName;let e_=r.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,t.jsx)("div",{className:"relative w-full overflow-auto",children:(0,t.jsx)("table",{ref:s,className:(0,o.cn)("w-full caption-bottom text-sm",a),...l})})});e_.displayName="Table";let eW=r.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,t.jsx)("thead",{ref:s,className:(0,o.cn)("[&_tr]:border-b",a),...l})});eW.displayName="TableHeader";let eq=r.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,t.jsx)("tbody",{ref:s,className:(0,o.cn)("[&_tr:last-child]:border-0",a),...l})});eq.displayName="TableBody",r.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,t.jsx)("tfoot",{ref:s,className:(0,o.cn)("border-t bg-muted/50 font-medium [&>tr]:last:border-b-0",a),...l})}).displayName="TableFooter";let eJ=r.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,t.jsx)("tr",{ref:s,className:(0,o.cn)("border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",a),...l})});eJ.displayName="TableRow";let eX=r.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,t.jsx)("th",{ref:s,className:(0,o.cn)("h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0",a),...l})});eX.displayName="TableHead";let eK=r.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,t.jsx)("td",{ref:s,className:(0,o.cn)("p-4 align-middle [&:has([role=checkbox])]:pr-0",a),...l})});eK.displayName="TableCell",r.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,t.jsx)("caption",{ref:s,className:(0,o.cn)("mt-4 text-sm text-muted-foreground",a),...l})}).displayName="TableCaption";var eQ=a(5485),e0=a(75980),e2=a(56141),e4=a(93835),e6=a(65887),e1=a(48968),e5=a(83337),e3=()=>{var e,s,a,n;let i=w(),c=(0,l.useSearchParams)().get("page")||"",d=c.includes("/")?c.split("/")[1]:"1",[o,m]=(0,r.useState)(""),[j,b]=(0,r.useState)("all"),[f,N]=(0,r.useState)("all"),[y,v]=(0,r.useState)("all"),[C,k]=(0,r.useState)(!1),[D,S]=(0,r.useState)(""),[P,Z]=(0,r.useState)(""),[E,I]=(0,r.useState)(null),[O,B]=(0,r.useState)(!1),{toast:z}=(0,e1.pm)();(0,e5.C)(e=>e.user.userProfile);let H={1:{companyName:"TechCorp Solutions",employees:43,averageAge:36,dependents:1.3,planType:"PPO + HSA Combo",potentialSavings:"$127,500",riskScore:"6.2/10",uploadDate:"2024-01-15",industry:"Technology",currentSpend:"$45,000/month",suggestedPlan:"Gold PPO with Disability Coverage",planFitSummary:{silverGoldPPO:67,hdhp:21,familyPPO:12,insight:"Higher average age + many in physical roles → more comprehensive plans needed. Disability & Rx coverage critical."},employeeProfiles:[{name:"Sarah Johnson",department:"Engineering",risk:"High",age:34,coverage:"Medical",hasDependents:!0,salary:"$85,000",currentPlan:{medical:"Silver PPO",dental:"Not Enrolled",vision:"Basic",life:"1x Salary",disability:"None"},coverageGaps:[{type:"Dental",severity:"High",reason:"No dental coverage for family of 4"},{type:"Disability",severity:"Critical",reason:"High-income earner with no disability protection"},{type:"Life Insurance",severity:"Medium",reason:"Basic coverage insufficient for family needs"}],recommendations:[{type:"Family Dental Plan",cost:"+$180/month",priority:"High",benefit:"Complete dental coverage for family"},{type:"Short-term Disability",cost:"+$45/month",priority:"Critical",benefit:"60% salary protection"},{type:"Additional Life Insurance",cost:"+$65/month",priority:"Medium",benefit:"3x salary coverage"}],planFitSummary:{goldPPO:{fit:85,reason:"Family with dependents needs comprehensive coverage"},silverPPO:{fit:65,reason:"Current plan but lacks dental and disability"},hdhp:{fit:25,reason:"Not ideal for family with regular medical needs"},basicPlan:{fit:15,reason:"Insufficient for high-income family with dependents"},recommendedPlan:"Gold PPO + Family Dental + Disability",insight:"High-earning family needs comprehensive coverage with strong dental and disability benefits"},insights:["Family of 4 with minimal dependent coverage","No disability insurance despite high-risk occupation"],upsells:[{type:"Family Dental Plan",value:"+$180/month",priority:"High"}]},{name:"Mike Chen",department:"Marketing",risk:"Medium",age:28,coverage:"Vision",hasDependents:!1,salary:"$62,000",currentPlan:{medical:"HDHP + HSA",dental:"Basic",vision:"Enhanced",life:"1x Salary",disability:"Short-term Only"},coverageGaps:[{type:"Long-term Disability",severity:"Medium",reason:"Only has short-term disability coverage"},{type:"HSA Contribution",severity:"Low",reason:"Not maximizing HSA employer match"}],recommendations:[{type:"Long-term Disability",cost:"+$35/month",priority:"Medium",benefit:"Long-term income protection"},{type:"Increase HSA Contribution",cost:"$0",priority:"High",benefit:"Tax savings + employer match"}],planFitSummary:{hdhp:{fit:90,reason:"Young, healthy, single - perfect for HSA strategy"},silverPPO:{fit:60,reason:"Good coverage but less tax advantaged"},goldPPO:{fit:40,reason:"Over-coverage for current needs"},basicPlan:{fit:70,reason:"Adequate but misses HSA benefits"},recommendedPlan:"HDHP + Enhanced HSA + Long-term Disability",insight:"Young professional ideal for HSA maximization with added disability protection"},insights:["Single coverage adequate for current needs","Young professional with growth potential"],upsells:[{type:"Vision Upgrade",value:"+$45/month",priority:"Medium"}]},{name:"Lisa Rodriguez",department:"Sales",risk:"Low",age:31,coverage:"Dental",hasDependents:!0,salary:"$78,000",currentPlan:{medical:"Gold PPO",dental:"Premium",vision:"Basic",life:"2x Salary",disability:"Both ST & LT"},coverageGaps:[{type:"Vision Upgrade",severity:"Low",reason:"Basic vision may not cover family needs"},{type:"Dependent Life",severity:"Low",reason:"No life insurance for spouse"}],recommendations:[{type:"Enhanced Vision",cost:"+$25/month",priority:"Low",benefit:"Better vision coverage for family"},{type:"Spouse Life Insurance",cost:"+$15/month",priority:"Low",benefit:"Financial protection for spouse"}],planFitSummary:{goldPPO:{fit:95,reason:"Excellent fit - comprehensive coverage for family"},silverPPO:{fit:75,reason:"Good but less comprehensive than current"},hdhp:{fit:30,reason:"Not ideal for family with established medical needs"},basicPlan:{fit:20,reason:"Insufficient for family coverage needs"},recommendedPlan:"Current Gold PPO + Enhanced Vision + Spouse Life",insight:"Optimal current coverage with minor enhancements needed for complete protection"},insights:["Optimal coverage for demographics","Recently married - may need updates"],upsells:[{type:"Spouse Coverage",value:"+$120/month",priority:"Low"}]},{name:"John Smith",department:"HR",risk:"Medium",age:45,coverage:"Waived",hasDependents:!1,salary:"$72,000",currentPlan:{medical:"Waived (Spouse Plan)",dental:"Waived",vision:"Waived",life:"Basic Company",disability:"None"},coverageGaps:[{type:"Supplemental Coverage",severity:"Medium",reason:"No backup if spouse plan changes"},{type:"Disability Insurance",severity:"High",reason:"No disability coverage at all"},{type:"Voluntary Benefits",severity:"Low",reason:"Missing supplemental options"}],recommendations:[{type:"Supplemental Medical",cost:"+$85/month",priority:"Medium",benefit:"Backup medical coverage"},{type:"Individual Disability",cost:"+$65/month",priority:"High",benefit:"Personal disability protection"},{type:"Critical Illness",cost:"+$35/month",priority:"Low",benefit:"Additional financial protection"}],planFitSummary:{supplementalPlan:{fit:85,reason:"Backup coverage essential for spouse plan dependency"},basicPPO:{fit:70,reason:"Simple coverage if spouse plan ends"},goldPPO:{fit:50,reason:"Comprehensive but may duplicate spouse coverage"},voluntaryOnly:{fit:75,reason:"Disability and critical illness without medical overlap"},recommendedPlan:"Supplemental Medical + Individual Disability + Voluntary Benefits",insight:"Focus on gap coverage and personal disability protection while maintaining spouse plan coordination"},insights:["Waived coverage due to spouse plan","May benefit from supplemental options"],upsells:[{type:"Supplemental Life",value:"+$65/month",priority:"Medium"}]}],upsellOpportunities:[{category:"Dental Coverage",description:"Only 60% have dental coverage",savings:"+$2,400/month",confidence:"94%",priority:"High"},{category:"Life Insurance Upgrade",description:"Basic coverage insufficient for 40% of workforce",savings:"+$1,800/month",confidence:"87%",priority:"High"},{category:"Wellness Program",description:"Reduce claims costs with preventive care",savings:"+$950/month",confidence:"78%",priority:"Medium"}]},2:{companyName:"Green Manufacturing",employees:87,averageAge:42,dependents:1.8,planType:"Traditional PPO",potentialSavings:"$245,000",riskScore:"7.1/10",uploadDate:"2024-01-10",industry:"Manufacturing",currentSpend:"$78,000/month",suggestedPlan:"Comprehensive PPO with Enhanced Coverage",planFitSummary:{silverGoldPPO:75,hdhp:15,familyPPO:10,insight:"Manufacturing environment requires comprehensive coverage. High-risk occupation demands enhanced disability and life insurance."},employeeProfiles:[{name:"Robert Smith",department:"Operations",risk:"High",age:48,coverage:"Medical",hasDependents:!0,insights:["Family coverage with aging dependents","High-risk occupation requiring additional coverage"],upsells:[{type:"Enhanced Life Insurance",value:"+$220/month",priority:"High"}]},{name:"Jennifer Davis",department:"Quality Control",risk:"Medium",age:55,coverage:"Dental",hasDependents:!1,insights:["Standard coverage meets current needs","Approaching retirement planning phase"],upsells:[{type:"Retirement Health Savings",value:"+$85/month",priority:"Medium"}]}],upsellOpportunities:[{category:"Disability Insurance",description:"High-risk manufacturing environment",savings:"+$3,200/month",confidence:"91%",priority:"High"},{category:"Family Coverage Expansion",description:"Large families underinsured",savings:"+$2,100/month",confidence:"85%",priority:"High"},{category:"Mental Health Support",description:"Stress management for manufacturing workers",savings:"+$1,400/month",confidence:"72%",priority:"Medium"}]},3:{companyName:"StartupXYZ",employees:18,averageAge:29,dependents:.8,planType:"HSA Only",potentialSavings:"$32,400",riskScore:"4.5/10",uploadDate:"2024-01-18",industry:"Startup",currentSpend:"$12,000/month",suggestedPlan:"Modern HSA Plus Plan",planFitSummary:{silverGoldPPO:45,hdhp:40,familyPPO:15,insight:"Young workforce suits HSA-focused plans. Emphasize digital health tools and preventive care for cost-conscious startup environment."},employeeProfiles:[{name:"Alex Thompson",department:"Development",risk:"Low",age:26,coverage:"Medical",hasDependents:!1,insights:["Young, healthy demographic","Minimal current healthcare needs"],upsells:[{type:"Preventive Care Package",value:"+$35/month",priority:"Low"}]},{name:"Emma Wilson",department:"Design",risk:"Low",age:24,coverage:"Vision",hasDependents:!1,insights:["Single coverage optimal","Tech-savvy, prefers digital health tools"],upsells:[{type:"Digital Health Platform",value:"+$25/month",priority:"Low"}]}],upsellOpportunities:[{category:"Growth Scaling Plan",description:"Prepare for rapid hiring expansion",savings:"+$800/month",confidence:"89%",priority:"Medium"},{category:"Mental Health & Wellness",description:"Startup stress management",savings:"+$450/month",confidence:"76%",priority:"Medium"},{category:"Tech Health Tools",description:"Digital-first health solutions",savings:"+$300/month",confidence:"82%",priority:"Low"}]}}[d];if(!H)return(0,t.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-slate-50 to-blue-50 flex items-center justify-center",children:(0,t.jsx)(h,{className:"max-w-md mx-auto",children:(0,t.jsxs)(g,{className:"p-6 text-center",children:[(0,t.jsx)("h2",{className:"text-xl font-bold mb-2",children:"Company Not Found"}),(0,t.jsx)("p",{className:"text-gray-600 mb-4",children:"The requested company insight could not be found."}),(0,t.jsxs)(x,{onClick:()=>i("/dashboard"),children:[(0,t.jsx)(eQ.Z,{className:"h-4 w-4 mr-2"}),"Back to Dashboard"]})]})})});let M=H.employeeProfiles.filter(e=>(!o||!!e.name.toLowerCase().includes(o.toLowerCase()))&&("all"===j||e.coverage.toLowerCase()===j)&&("all"===f||("under-30"!==f||!(e.age>=30))&&("30-45"!==f||!(e.age<30)&&!(e.age>45))&&("46-plus"!==f||!(e.age<=45)))&&("all"===y||("has-dependents"!==y||!!e.hasDependents)&&("no-dependents"!==y||!e.hasDependents))),T=e=>{I(e),B(!0)};return(0,t.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-slate-50 to-blue-50",children:[(0,t.jsx)("header",{className:"border-b bg-white/90 backdrop-blur-sm sticky top-0 z-50",children:(0,t.jsxs)("div",{className:"container mx-auto px-3 sm:px-4 py-3 flex justify-between items-center",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,t.jsxs)(x,{variant:"ghost",size:"sm",onClick:()=>i("/dashboard"),children:[(0,t.jsx)(eQ.Z,{className:"h-4 w-4 mr-2"}),"Back to Dashboard"]}),(0,t.jsx)("div",{className:"text-xl sm:text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent",children:"BenOsphere"})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(ew,{context:"".concat(H.companyName," employer insight with ").concat(H.employees," employees in ").concat(H.industry),size:"sm",variant:"outline"}),(0,t.jsxs)(eH,{open:C,onOpenChange:k,children:[(0,t.jsx)(eM,{asChild:!0,children:(0,t.jsxs)(x,{size:"sm",variant:"outline",children:[(0,t.jsx)(ef.Z,{className:"h-4 w-4 mr-2"}),"Share with Employer"]})}),(0,t.jsxs)(eL,{className:"sm:max-w-md",children:[(0,t.jsxs)(eU,{children:[(0,t.jsx)(eG,{children:"Share Report with Employer"}),(0,t.jsx)(eY,{children:"Send this insight report directly to the employer's email address."})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium text-gray-700 mb-2 block",children:"Email Address"}),(0,t.jsx)(el,{type:"email",placeholder:"<EMAIL>",value:D,onChange:e=>S(e.target.value)})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium text-gray-700 mb-2 block",children:"Message (Optional)"}),(0,t.jsx)("textarea",{className:"w-full p-3 border border-gray-300 rounded-md resize-none",rows:3,placeholder:"Add a personal message...",value:P,onChange:e=>Z(e.target.value)})]})]}),(0,t.jsxs)(eV,{children:[(0,t.jsx)(x,{variant:"outline",onClick:()=>k(!1),children:"Cancel"}),(0,t.jsxs)(x,{onClick:()=>{if(!D){z({title:"Email Required",description:"Please enter an email address to share the report.",variant:"destructive"});return}console.log("Sharing report with ".concat(D,":"),P),z({title:"Report Shared",description:"The report has been shared with ".concat(D)}),k(!1),S(""),Z("")},children:[(0,t.jsx)(R.Z,{className:"h-4 w-4 mr-2"}),"Send Report"]})]})]})]}),(0,t.jsx)(ea,{})]})]})}),(0,t.jsxs)("main",{className:"container mx-auto px-3 sm:px-4 py-6 max-w-7xl",children:[(0,t.jsx)(h,{className:"mb-6 border-2 border-gray-300 shadow-lg",children:(0,t.jsxs)(g,{className:"p-6",children:[(0,t.jsxs)("div",{className:"mb-4",children:[(0,t.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:H.companyName}),(0,t.jsxs)("div",{className:"flex flex-wrap items-center gap-4 text-sm text-gray-600",children:[(0,t.jsxs)("span",{className:"flex items-center",children:[(0,t.jsx)("span",{className:"mr-1",children:"\uD83D\uDCC8"}),H.industry]}),(0,t.jsxs)("span",{className:"flex items-center",children:[(0,t.jsx)(eu.Z,{className:"h-4 w-4 mr-1"}),H.employees," employees"]}),(0,t.jsxs)("span",{className:"flex items-center",children:[(0,t.jsx)("span",{className:"mr-1",children:"\uD83D\uDCCA"}),"Average Age: ",H.averageAge]}),(0,t.jsxs)("span",{className:"flex items-center",children:[(0,t.jsx)("span",{className:"mr-1",children:"\uD83D\uDCC5"}),"Analyzed: ",new Date(H.uploadDate).toLocaleDateString()]}),(0,t.jsxs)("span",{className:"flex items-center",children:[(0,t.jsx)(eg.Z,{className:"h-4 w-4 mr-1"}),"Current Spend: ",H.currentSpend]})]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-4",children:[(0,t.jsx)(h,{className:"bg-gradient-to-br from-orange-500 to-red-500 border-0 shadow-lg hover:shadow-xl transition-all duration-200 hover:scale-105",children:(0,t.jsxs)(g,{className:"p-4 text-center",children:[(0,t.jsx)(eg.Z,{className:"h-8 w-8 mx-auto mb-2 text-white"}),(0,t.jsx)("p",{className:"text-sm text-orange-100 font-medium",children:"Employer Savings Opportunity (Total)"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-white",children:H.potentialSavings})]})}),(0,t.jsx)(h,{className:"bg-gradient-to-br from-purple-500 to-pink-500 border-0 shadow-lg hover:shadow-xl transition-all duration-200 hover:scale-105",children:(0,t.jsxs)(g,{className:"p-4 text-center",children:[(0,t.jsx)(F.Z,{className:"h-8 w-8 mx-auto mb-2 text-white"}),(0,t.jsx)("p",{className:"text-sm text-purple-100 font-medium",children:"Risk Score"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-white",children:H.riskScore})]})}),(0,t.jsx)(h,{className:"bg-gradient-to-br from-emerald-500 to-teal-600 border-0 shadow-lg hover:shadow-xl transition-all duration-200 hover:scale-105",children:(0,t.jsxs)(g,{className:"p-4 text-center",children:[(0,t.jsx)(ej.Z,{className:"h-8 w-8 mx-auto mb-2 text-white"}),(0,t.jsx)("p",{className:"text-sm text-emerald-100 font-medium",children:"Benchmark Rate"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-white",children:"8.5% below"})]})})]})]})}),(0,t.jsxs)(eF,{defaultValue:"executive-overview",className:"w-full",children:[(0,t.jsxs)(eE,{className:"grid w-full grid-cols-4 mb-6",children:[(0,t.jsxs)(eI,{value:"executive-overview",className:"flex items-center gap-2",children:[(0,t.jsx)(ej.Z,{className:"h-4 w-4"}),"Executive Overview"]}),(0,t.jsxs)(eI,{value:"plan-fit",className:"flex items-center gap-2",children:[(0,t.jsx)(eD.Z,{className:"h-4 w-4"}),"Plan Fit Analysis"]}),(0,t.jsxs)(eI,{value:"upsells",className:"flex items-center gap-2",children:[(0,t.jsx)(eg.Z,{className:"h-4 w-4"}),"Upsell Opportunities"]}),(0,t.jsxs)(eI,{value:"employees",className:"flex items-center gap-2",children:[(0,t.jsx)(eu.Z,{className:"h-4 w-4"}),"Employee Insights"]})]}),(0,t.jsxs)(eO,{value:"executive-overview",children:[(0,t.jsxs)("div",{className:"grid lg:grid-cols-2 gap-8 mb-8",children:[(0,t.jsxs)(h,{className:"shadow-lg border-0",children:[(0,t.jsx)(p,{children:(0,t.jsxs)(u,{className:"text-xl bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent flex items-center",children:[(0,t.jsx)(eu.Z,{className:"h-5 w-5 mr-2"}),"Employee Demographics"]})}),(0,t.jsx)(g,{children:(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center p-3 bg-blue-50 rounded-lg",children:[(0,t.jsx)("span",{className:"font-medium",children:"Age 25-35"}),(0,t.jsx)("span",{className:"font-bold text-blue-600",children:"18 employees (42%)"})]}),(0,t.jsxs)("div",{className:"flex justify-between items-center p-3 bg-green-50 rounded-lg",children:[(0,t.jsx)("span",{className:"font-medium",children:"Age 36-50"}),(0,t.jsx)("span",{className:"font-bold text-green-600",children:"20 employees (46%)"})]}),(0,t.jsxs)("div",{className:"flex justify-between items-center p-3 bg-orange-50 rounded-lg",children:[(0,t.jsx)("span",{className:"font-medium",children:"Age 50+"}),(0,t.jsx)("span",{className:"font-bold text-orange-600",children:"5 employees (12%)"})]}),(0,t.jsxs)("div",{className:"flex justify-between items-center p-3 bg-purple-50 rounded-lg",children:[(0,t.jsx)("span",{className:"font-medium",children:"Employees with Families"}),(0,t.jsx)("span",{className:"font-bold text-purple-600",children:"28 employees (65%)"})]})]})})]}),(0,t.jsxs)(h,{className:"shadow-lg border-0",children:[(0,t.jsx)(p,{children:(0,t.jsxs)(u,{className:"text-xl bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent flex items-center",children:[(0,t.jsx)(eg.Z,{className:"h-5 w-5 mr-2"}),"Cost Analysis"]})}),(0,t.jsx)(g,{children:(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"p-4 bg-green-50 rounded-lg border border-green-200",children:[(0,t.jsx)("h4",{className:"font-semibold text-green-800 mb-2",children:"Potential Annual Savings"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-green-600",children:"$127,500"}),(0,t.jsx)("p",{className:"text-sm text-green-700",children:"By optimizing plan structure"})]}),(0,t.jsxs)("div",{className:"p-4 bg-blue-50 rounded-lg border border-blue-200",children:[(0,t.jsx)("h4",{className:"font-semibold text-blue-800 mb-2",children:"Current Annual Premium"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-blue-600",children:"$486,000"}),(0,t.jsx)("p",{className:"text-sm text-blue-700",children:"Average $11,300 per employee"})]}),(0,t.jsxs)("div",{className:"p-4 bg-orange-50 rounded-lg border border-orange-200",children:[(0,t.jsx)("h4",{className:"font-semibold text-orange-800 mb-2",children:"Benchmarked Rate"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-orange-600",children:"8.5% below"}),(0,t.jsx)("p",{className:"text-sm text-orange-700",children:"Industry average"})]})]})})]})]}),(0,t.jsxs)(h,{className:"shadow-xl mb-8 border-0",children:[(0,t.jsx)(p,{children:(0,t.jsxs)(u,{className:"text-2xl bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent flex items-center",children:[(0,t.jsx)(e0.Z,{className:"h-6 w-6 mr-2"}),"Personalized Recommendations"]})}),(0,t.jsx)(g,{children:(0,t.jsxs)("div",{className:"grid md:grid-cols-3 gap-6",children:[(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsx)("h3",{className:"font-semibold text-lg text-gray-900",children:"Cost Savings"}),(0,t.jsxs)("ul",{className:"space-y-2",children:[(0,t.jsxs)("li",{className:"flex items-start",children:[(0,t.jsx)("span",{className:"text-blue-500 mr-2 mt-1",children:"✓"}),(0,t.jsx)("span",{className:"text-sm text-gray-700",children:"Switch to HSA-eligible plan could save $127,500 annually"})]}),(0,t.jsxs)("li",{className:"flex items-start",children:[(0,t.jsx)("span",{className:"text-blue-500 mr-2 mt-1",children:"✓"}),(0,t.jsx)("span",{className:"text-sm text-gray-700",children:"Optimize contribution strategy for younger demographics"})]}),(0,t.jsxs)("li",{className:"flex items-start",children:[(0,t.jsx)("span",{className:"text-blue-500 mr-2 mt-1",children:"✓"}),(0,t.jsx)("span",{className:"text-sm text-gray-700",children:"Consider wellness programs to reduce risk factors"})]})]})]}),(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsx)("h3",{className:"font-semibold text-lg text-gray-900",children:"Plan Improvements"}),(0,t.jsxs)("ul",{className:"space-y-2",children:[(0,t.jsxs)("li",{className:"flex items-start",children:[(0,t.jsx)("span",{className:"text-blue-500 mr-2 mt-1",children:"✓"}),(0,t.jsx)("span",{className:"text-sm text-gray-700",children:"Add vision benefits - 68% of employees would benefit"})]}),(0,t.jsxs)("li",{className:"flex items-start",children:[(0,t.jsx)("span",{className:"text-blue-500 mr-2 mt-1",children:"✓"}),(0,t.jsx)("span",{className:"text-sm text-gray-700",children:"Increase mental health coverage options"})]}),(0,t.jsxs)("li",{className:"flex items-start",children:[(0,t.jsx)("span",{className:"text-blue-500 mr-2 mt-1",children:"✓"}),(0,t.jsx)("span",{className:"text-sm text-gray-700",children:"Consider flexible spending accounts"})]})]})]}),(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsx)("h3",{className:"font-semibold text-lg text-gray-900",children:"Employee Engagement"}),(0,t.jsxs)("ul",{className:"space-y-2",children:[(0,t.jsxs)("li",{className:"flex items-start",children:[(0,t.jsx)("span",{className:"text-blue-500 mr-2 mt-1",children:"✓"}),(0,t.jsx)("span",{className:"text-sm text-gray-700",children:"Implement benefits education program"})]}),(0,t.jsxs)("li",{className:"flex items-start",children:[(0,t.jsx)("span",{className:"text-blue-500 mr-2 mt-1",children:"✓"}),(0,t.jsx)("span",{className:"text-sm text-gray-700",children:"Add telemedicine options for remote workers"})]}),(0,t.jsxs)("li",{className:"flex items-start",children:[(0,t.jsx)("span",{className:"text-blue-500 mr-2 mt-1",children:"✓"}),(0,t.jsx)("span",{className:"text-sm text-gray-700",children:"Create benefits portal for easy access"})]})]})]})]})})]}),(0,t.jsxs)(h,{className:"shadow-lg border-0 mt-6",children:[(0,t.jsx)(p,{children:(0,t.jsxs)(u,{className:"text-xl bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent flex items-center",children:[(0,t.jsx)(eD.Z,{className:"h-5 w-5 mr-2"}),"Industry Benchmark Comparison"]})}),(0,t.jsx)(g,{children:(0,t.jsxs)("div",{className:"grid md:grid-cols-3 gap-6",children:[(0,t.jsx)(h,{className:"bg-gradient-to-br from-red-50 to-red-100 border-red-200",children:(0,t.jsxs)(g,{className:"p-4 text-center",children:[(0,t.jsx)("h4",{className:"text-sm font-medium text-gray-600 mb-2",children:"Current Position"}),(0,t.jsx)("p",{className:"text-3xl font-bold text-red-600",children:"45th"}),(0,t.jsx)("p",{className:"text-sm text-gray-600",children:"Percentile"})]})}),(0,t.jsx)(h,{className:"bg-gradient-to-br from-green-50 to-green-100 border-green-200",children:(0,t.jsxs)(g,{className:"p-4 text-center",children:[(0,t.jsx)("h4",{className:"text-sm font-medium text-gray-600 mb-2",children:"Projected Position"}),(0,t.jsx)("p",{className:"text-3xl font-bold text-green-600",children:"78th"}),(0,t.jsx)("p",{className:"text-sm text-gray-600",children:"Percentile"})]})}),(0,t.jsx)(h,{className:"bg-gradient-to-br from-blue-50 to-blue-100 border-blue-200",children:(0,t.jsxs)(g,{className:"p-4 text-center",children:[(0,t.jsx)("h4",{className:"text-sm font-medium text-gray-600 mb-2",children:"Industry Avg Savings"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-blue-600",children:"$95,000"}),(0,t.jsx)("p",{className:"text-sm text-gray-600",children:"Annual"})]})})]})})]})]}),(0,t.jsx)(eO,{value:"plan-fit",children:(0,t.jsxs)(h,{className:"shadow-lg border-0",children:[(0,t.jsx)(p,{className:"pb-4",children:(0,t.jsxs)(u,{className:"flex items-center",children:[(0,t.jsx)("div",{className:"bg-blue-100 p-2 rounded-lg mr-3",children:(0,t.jsx)(eD.Z,{className:"h-5 w-5 text-blue-600"})}),"Plan Fit Analysis"]})}),(0,t.jsxs)(g,{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"bg-gradient-to-r from-yellow-50 to-orange-50 p-4 rounded-lg border-l-4 border-yellow-500",children:[(0,t.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 mb-2 flex items-center",children:[(0,t.jsx)("span",{className:"mr-2",children:"\uD83C\uDFC6"}),"Suggested Plan:"]}),(0,t.jsx)("p",{className:"text-xl font-bold text-orange-800",children:H.suggestedPlan})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Plan Fit Summary:"}),(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between p-3 bg-blue-50 rounded-lg",children:[(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"w-4 h-4 bg-blue-500 rounded mr-3"}),(0,t.jsxs)("span",{className:"font-medium",children:[H.planFitSummary.silverGoldPPO,"% fit for Silver/Gold PPO"]})]}),(0,t.jsx)(eD.Z,{className:"h-4 w-4 text-blue-600"})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between p-3 bg-green-50 rounded-lg",children:[(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"w-4 h-4 bg-green-500 rounded mr-3"}),(0,t.jsxs)("span",{className:"font-medium",children:[H.planFitSummary.hdhp,"% fit for HDHP"]})]}),(0,t.jsx)(eD.Z,{className:"h-4 w-4 text-green-600"})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between p-3 bg-purple-50 rounded-lg",children:[(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"w-4 h-4 bg-purple-500 rounded mr-3"}),(0,t.jsxs)("span",{className:"font-medium",children:[H.planFitSummary.familyPPO,"% require Family PPO with Rx coverage"]})]}),(0,t.jsx)(eD.Z,{className:"h-4 w-4 text-purple-600"})]})]})]}),(0,t.jsxs)("div",{className:"mt-6",children:[(0,t.jsxs)("h4",{className:"text-lg font-semibold text-gray-900 mb-4 flex items-center",children:[(0,t.jsx)(A.Z,{className:"h-5 w-5 mr-2 text-green-600"}),"Detailed Recommendations"]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)(h,{className:"border-l-4 border-l-green-500",children:(0,t.jsxs)(g,{className:"p-4",children:[(0,t.jsx)("h4",{className:"font-semibold text-gray-900 mb-2",children:"Primary Plan Change"}),(0,t.jsx)("p",{className:"text-gray-700 mb-2",children:"Transition to Gold PPO with enhanced disability coverage"}),(0,t.jsx)("p",{className:"text-sm text-green-600 font-medium",children:"Reduce overall costs by 23% while improving coverage"})]})}),(0,t.jsx)(h,{className:"border-l-4 border-l-blue-500",children:(0,t.jsxs)(g,{className:"p-4",children:[(0,t.jsx)("h4",{className:"font-semibold text-gray-900 mb-2",children:"Voluntary Benefits"}),(0,t.jsx)("p",{className:"text-gray-700 mb-2",children:"Add dental and vision upgrade options"}),(0,t.jsx)("p",{className:"text-sm text-blue-600 font-medium",children:"Increase employee satisfaction and retention"})]})}),(0,t.jsx)(h,{className:"border-l-4 border-l-purple-500",children:(0,t.jsxs)(g,{className:"p-4",children:[(0,t.jsx)("h4",{className:"font-semibold text-gray-900 mb-2",children:"Add-on Services"}),(0,t.jsx)("p",{className:"text-gray-700 mb-2",children:"Include telehealth and mental health support"}),(0,t.jsx)("p",{className:"text-sm text-purple-600 font-medium",children:"Preventive care reducing long-term claims costs"})]})})]})]}),(0,t.jsxs)("div",{className:"mt-6",children:[(0,t.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 mb-4 flex items-center",children:[(0,t.jsx)(e0.Z,{className:"h-5 w-5 mr-2 text-blue-600"}),"Key Workforce Insights"]}),(0,t.jsxs)("div",{className:"grid md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{className:"flex items-start p-4 bg-blue-50 rounded-lg",children:[(0,t.jsx)(A.Z,{className:"h-5 w-5 text-blue-600 mr-3 mt-0.5 flex-shrink-0"}),(0,t.jsx)("p",{className:"text-gray-700",children:"Majority of employees fall in high-utilization age bracket (36 avg age)"})]}),(0,t.jsxs)("div",{className:"flex items-start p-4 bg-blue-50 rounded-lg",children:[(0,t.jsx)(A.Z,{className:"h-5 w-5 text-blue-600 mr-3 mt-0.5 flex-shrink-0"}),(0,t.jsx)("p",{className:"text-gray-700",children:"Disability coverage crucial due to technology industry risk profile"})]}),(0,t.jsxs)("div",{className:"flex items-start p-4 bg-blue-50 rounded-lg",children:[(0,t.jsx)(A.Z,{className:"h-5 w-5 text-blue-600 mr-3 mt-0.5 flex-shrink-0"}),(0,t.jsx)("p",{className:"text-gray-700",children:"67% of workforce suits comprehensive PPO coverage"})]}),(0,t.jsxs)("div",{className:"flex items-start p-4 bg-blue-50 rounded-lg",children:[(0,t.jsx)(A.Z,{className:"h-5 w-5 text-blue-600 mr-3 mt-0.5 flex-shrink-0"}),(0,t.jsx)("p",{className:"text-gray-700",children:"Current HSA utilization below industry average"})]})]})]})]})]})}),(0,t.jsx)(eO,{value:"upsells",children:(0,t.jsxs)(h,{className:"shadow-lg border-0",children:[(0,t.jsx)(p,{className:"pb-4",children:(0,t.jsxs)(u,{className:"flex items-center",children:[(0,t.jsx)("div",{className:"bg-orange-100 p-2 rounded-lg mr-3",children:(0,t.jsx)(eg.Z,{className:"h-5 w-5 text-orange-600"})}),"Projected Employer Savings"]})}),(0,t.jsxs)(g,{className:"space-y-4",children:[H.upsellOpportunities.map((e,s)=>(0,t.jsx)(h,{className:"border-l-4 border-l-orange-500 hover:shadow-md transition-all duration-200",children:(0,t.jsxs)(g,{className:"p-4",children:[(0,t.jsxs)("div",{className:"flex justify-between items-start mb-2",children:[(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(A.Z,{className:"h-4 w-4 text-blue-600 mr-2"}),(0,t.jsx)("span",{className:"font-semibold",children:e.category})]}),(0,t.jsx)("span",{className:"px-2 py-1 rounded-full text-xs font-medium ".concat("High"===e.priority?"bg-red-100 text-red-800":"Medium"===e.priority?"bg-yellow-100 text-yellow-800":"bg-green-100 text-green-800"),children:e.priority})]}),(0,t.jsx)("p",{className:"text-sm text-gray-600 mb-2",children:e.description}),(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsx)("span",{className:"text-lg font-bold text-green-600",children:e.savings}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsxs)("span",{className:"text-sm text-gray-500",children:[e.confidence," confidence"]}),(0,t.jsxs)(x,{size:"sm",variant:"outline",children:[(0,t.jsx)(e2.Z,{className:"h-3 w-3 mr-1"}),"View Details"]})]})]})]})},s)),(0,t.jsxs)("div",{className:"mt-6",children:[(0,t.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 mb-4 flex items-center",children:[(0,t.jsx)(e0.Z,{className:"h-5 w-5 mr-2 text-orange-600"}),"Add-on Service Opportunities"]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)(h,{className:"border-l-4 border-l-blue-500",children:(0,t.jsxs)(g,{className:"p-4",children:[(0,t.jsxs)("div",{className:"flex justify-between items-start mb-2",children:[(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(A.Z,{className:"h-4 w-4 text-blue-600 mr-2"}),(0,t.jsx)("span",{className:"font-semibold",children:"Telehealth Services"})]}),(0,t.jsx)("span",{className:"px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800",children:"High Impact"})]}),(0,t.jsx)("p",{className:"text-sm text-gray-600 mb-2",children:"Remote healthcare access for all employees"}),(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsx)("span",{className:"text-lg font-bold text-green-600",children:"+$35/employee/month"}),(0,t.jsx)("span",{className:"text-sm text-gray-500",children:"Reduces traditional care costs by 15%"})]})]})}),(0,t.jsx)(h,{className:"border-l-4 border-l-purple-500",children:(0,t.jsxs)(g,{className:"p-4",children:[(0,t.jsxs)("div",{className:"flex justify-between items-start mb-2",children:[(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(A.Z,{className:"h-4 w-4 text-purple-600 mr-2"}),(0,t.jsx)("span",{className:"font-semibold",children:"Mental Health Support"})]}),(0,t.jsx)("span",{className:"px-2 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800",children:"Medium Impact"})]}),(0,t.jsx)("p",{className:"text-sm text-gray-600 mb-2",children:"Enhanced EAP and mental wellness programs"}),(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsx)("span",{className:"text-lg font-bold text-green-600",children:"+$28/employee/month"}),(0,t.jsx)("span",{className:"text-sm text-gray-500",children:"Improves productivity and retention"})]})]})})]})]})]})]})}),(0,t.jsx)(eO,{value:"employees",children:(0,t.jsxs)(h,{className:"shadow-lg border-0",children:[(0,t.jsx)(p,{className:"pb-4",children:(0,t.jsxs)(u,{className:"flex items-center",children:[(0,t.jsx)("div",{className:"bg-blue-100 p-2 rounded-lg mr-3",children:(0,t.jsx)(eu.Z,{className:"h-5 w-5 text-blue-600"})}),"Employee Insights"]})}),(0,t.jsxs)(g,{children:[(0,t.jsxs)("div",{className:"mb-6 space-y-4",children:[(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(ep.Z,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4"}),(0,t.jsx)(el,{placeholder:"Search by name",value:o,onChange:e=>m(e.target.value),className:"pl-10"})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-3 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium text-gray-700 mb-2 block",children:"Coverage"}),(0,t.jsxs)(ei,{value:j,onValueChange:b,children:[(0,t.jsx)(ed,{children:(0,t.jsx)(ec,{placeholder:"All"})}),(0,t.jsxs)(ex,{className:"bg-white",children:[(0,t.jsx)(eh,{value:"all",children:"All"}),(0,t.jsx)(eh,{value:"medical",children:"Medical"}),(0,t.jsx)(eh,{value:"dental",children:"Dental"}),(0,t.jsx)(eh,{value:"vision",children:"Vision"}),(0,t.jsx)(eh,{value:"waived",children:"Waived"})]})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium text-gray-700 mb-2 block",children:"Age"}),(0,t.jsxs)(ei,{value:f,onValueChange:N,children:[(0,t.jsx)(ed,{children:(0,t.jsx)(ec,{placeholder:"All"})}),(0,t.jsxs)(ex,{className:"bg-white",children:[(0,t.jsx)(eh,{value:"all",children:"All"}),(0,t.jsx)(eh,{value:"under-30",children:"<30"}),(0,t.jsx)(eh,{value:"30-45",children:"30–45"}),(0,t.jsx)(eh,{value:"46-plus",children:"46+"})]})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium text-gray-700 mb-2 block",children:"Dependents"}),(0,t.jsxs)(ei,{value:y,onValueChange:v,children:[(0,t.jsx)(ed,{children:(0,t.jsx)(ec,{placeholder:"All"})}),(0,t.jsxs)(ex,{className:"bg-white",children:[(0,t.jsx)(eh,{value:"all",children:"All"}),(0,t.jsx)(eh,{value:"has-dependents",children:"Has Dependents"}),(0,t.jsx)(eh,{value:"no-dependents",children:"No Dependents"})]})]})]})]})]}),(0,t.jsx)("div",{className:"space-y-4",children:M.map((e,s)=>(0,t.jsx)(h,{className:"border-l-4 border-l-blue-500 hover:shadow-md transition-all duration-200",children:(0,t.jsxs)(g,{className:"p-4",children:[(0,t.jsxs)("div",{className:"flex justify-between items-start mb-2",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-semibold",children:e.name}),(0,t.jsxs)("p",{className:"text-sm text-gray-600",children:[e.department," • Age: ",e.age," • Coverage: ",e.coverage]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsxs)("span",{className:"px-2 py-1 rounded-full text-xs font-medium ".concat("High"===e.risk?"bg-red-100 text-red-800":"Medium"===e.risk?"bg-yellow-100 text-yellow-800":"bg-green-100 text-green-800"),children:[(0,t.jsx)(F.Z,{className:"h-3 w-3 inline mr-1"}),e.risk," Risk"]}),(0,t.jsxs)(x,{size:"sm",variant:"outline",onClick:()=>T(e),children:[(0,t.jsx)(e2.Z,{className:"h-3 w-3 mr-1"}),"View Details"]})]})]}),(0,t.jsxs)("div",{className:"mb-3",children:[(0,t.jsx)("p",{className:"text-sm font-medium text-gray-700 mb-1",children:"Key Insights:"}),(0,t.jsx)("ul",{className:"text-sm text-gray-600 space-y-1",children:e.insights.map((e,s)=>(0,t.jsxs)("li",{className:"flex items-start",children:[(0,t.jsx)("span",{className:"mr-2",children:"•"}),e]},s))})]}),(0,t.jsxs)("div",{className:"bg-orange-50 p-3 rounded-lg",children:[(0,t.jsx)("p",{className:"text-sm font-medium text-orange-800 mb-1",children:"Upsell Opportunities:"}),e.upsells.map((e,s)=>(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsx)("span",{className:"text-sm text-orange-700",children:e.type}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("span",{className:"text-sm font-bold text-green-600",children:e.value}),(0,t.jsx)("span",{className:"px-2 py-1 rounded-full text-xs font-medium ".concat("High"===e.priority?"bg-red-100 text-red-800":"Medium"===e.priority?"bg-yellow-100 text-yellow-800":"bg-green-100 text-green-800"),children:e.priority})]})]},s))]})]})},s))})]})]})})]}),(0,t.jsx)(h,{className:"mt-6 bg-gradient-to-r from-blue-100 to-purple-100 border-0 shadow-lg",children:(0,t.jsxs)(g,{className:"p-6 text-center",children:[(0,t.jsx)("h3",{className:"text-xl font-bold text-gray-900 mb-2",children:"Ready to Present?"}),(0,t.jsx)("p",{className:"text-gray-700 mb-4",children:"Generate a professional proposal based on these insights"}),(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row justify-center space-y-2 sm:space-y-0 sm:space-x-4",children:[(0,t.jsx)(x,{className:"bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700",onClick:()=>{i("/generate-proposal/".concat(d))},children:"Generate Proposal"}),(0,t.jsxs)(x,{variant:"outline",onClick:()=>k(!0),children:[(0,t.jsx)(ef.Z,{className:"h-4 w-4 mr-2"}),"Share with Employer"]})]})]})})]}),(0,t.jsx)(eH,{open:O,onOpenChange:B,children:(0,t.jsxs)(eL,{className:"max-w-4xl max-h-[90vh] overflow-y-auto",children:[(0,t.jsxs)(eU,{children:[(0,t.jsxs)(eG,{className:"flex items-center justify-between",children:[(0,t.jsxs)("span",{children:["Employee Detail: ",null==E?void 0:E.name]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(ew,{context:"employee ".concat(null==E?void 0:E.name," from ").concat(H.companyName," with detailed benefit analysis"),size:"sm",variant:"outline"}),(0,t.jsx)(x,{variant:"ghost",size:"sm",onClick:()=>B(!1),children:(0,t.jsx)(ez.Z,{className:"h-4 w-4"})})]})]}),(0,t.jsxs)(eY,{children:["Comprehensive plan analysis and recommendations for ",null==E?void 0:E.name]})]}),E&&(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)(h,{children:[(0,t.jsx)(p,{children:(0,t.jsx)(u,{className:"text-lg",children:"Employee Overview"})}),(0,t.jsx)(g,{children:(0,t.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm text-gray-600",children:"Department"}),(0,t.jsx)("p",{className:"font-semibold",children:E.department})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm text-gray-600",children:"Age"}),(0,t.jsx)("p",{className:"font-semibold",children:E.age})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm text-gray-600",children:"Salary"}),(0,t.jsx)("p",{className:"font-semibold",children:E.salary})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm text-gray-600",children:"Dependents"}),(0,t.jsx)("p",{className:"font-semibold",children:E.hasDependents?"Yes":"No"})]})]})})]}),(0,t.jsxs)(h,{children:[(0,t.jsx)(p,{children:(0,t.jsxs)(u,{className:"text-lg flex items-center",children:[(0,t.jsx)(e4.Z,{className:"h-5 w-5 mr-2 text-blue-600"}),"Individual Plan Fit Summary"]})}),(0,t.jsxs)(g,{children:[(0,t.jsxs)("div",{className:"bg-gradient-to-r from-green-50 to-emerald-50 p-4 rounded-lg border-l-4 border-green-500 mb-4",children:[(0,t.jsxs)("h4",{className:"text-lg font-semibold text-gray-900 mb-2 flex items-center",children:[(0,t.jsx)("span",{className:"mr-2",children:"\uD83C\uDFC6"}),"Recommended Plan:"]}),(0,t.jsx)("p",{className:"text-xl font-bold text-green-800",children:null===(e=E.planFitSummary)||void 0===e?void 0:e.recommendedPlan})]}),(0,t.jsxs)("div",{className:"space-y-3 mb-4",children:[(0,t.jsx)("h4",{className:"text-lg font-semibold text-gray-900",children:"Plan Fit Analysis:"}),E.planFitSummary&&Object.entries(E.planFitSummary).filter(e=>{let[s]=e;return!["recommendedPlan","insight"].includes(s)}).map(e=>{let[s,a]=e,l=a.fit;return(0,t.jsxs)("div",{className:"bg-gray-50 p-4 rounded-lg",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"w-4 h-4 ".concat(l>=80?"bg-green-500":l>=60?"bg-yellow-500":l>=40?"bg-orange-500":"bg-red-500"," rounded mr-3")}),(0,t.jsx)("span",{className:"font-medium capitalize",children:s.replace(/([A-Z])/g," $1").trim()})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsxs)("span",{className:"text-2xl font-bold text-gray-900",children:[l,"%"]}),(0,t.jsxs)("span",{className:"px-2 py-1 rounded-full text-xs font-medium ".concat(l>=80?"bg-green-100 text-green-800":l>=60?"bg-yellow-100 text-yellow-800":l>=40?"bg-orange-100 text-orange-800":"bg-red-100 text-red-800"),children:[l>=80?"Excellent":l>=60?"Good":l>=40?"Fair":"Poor"," Fit"]})]})]}),(0,t.jsx)("p",{className:"text-sm text-gray-600",children:a.reason})]},s)})]}),(0,t.jsxs)("div",{className:"bg-gradient-to-r from-blue-50 to-indigo-50 p-4 rounded-lg border-l-4 border-blue-500",children:[(0,t.jsxs)("h4",{className:"text-lg font-semibold text-gray-900 mb-2 flex items-center",children:[(0,t.jsx)("span",{className:"mr-2",children:"\uD83D\uDCA1"}),"Personal Insight:"]}),(0,t.jsx)("p",{className:"text-gray-700 italic",children:null===(s=E.planFitSummary)||void 0===s?void 0:s.insight})]})]})]}),(0,t.jsxs)(h,{children:[(0,t.jsx)(p,{children:(0,t.jsxs)(u,{className:"text-lg flex items-center",children:[(0,t.jsx)(A.Z,{className:"h-5 w-5 mr-2 text-green-600"}),"Current Plan Coverage"]})}),(0,t.jsx)(g,{children:(0,t.jsxs)(e_,{children:[(0,t.jsx)(eW,{children:(0,t.jsxs)(eJ,{children:[(0,t.jsx)(eX,{children:"Benefit Type"}),(0,t.jsx)(eX,{children:"Current Coverage"}),(0,t.jsx)(eX,{children:"Status"})]})}),(0,t.jsx)(eq,{children:Object.entries(E.currentPlan).map(e=>{let[s,a]=e;return(0,t.jsxs)(eJ,{children:[(0,t.jsx)(eK,{className:"font-medium capitalize",children:s}),(0,t.jsx)(eK,{children:a}),(0,t.jsx)(eK,{children:(0,t.jsx)("span",{className:"px-2 py-1 rounded-full text-xs font-medium ".concat(a.includes("None")||a.includes("Waived")||a.includes("Not Enrolled")?"bg-red-100 text-red-800":a.includes("Basic")?"bg-yellow-100 text-yellow-800":"bg-green-100 text-green-800"),children:a.includes("None")||a.includes("Waived")||a.includes("Not Enrolled")?"Not Covered":"Covered"})})]},s)})})]})})]}),(0,t.jsxs)(h,{children:[(0,t.jsx)(p,{children:(0,t.jsxs)(u,{className:"text-lg flex items-center",children:[(0,t.jsx)(F.Z,{className:"h-5 w-5 mr-2 text-red-600"}),"Identified Coverage Gaps"]})}),(0,t.jsx)(g,{children:(0,t.jsx)("div",{className:"space-y-3",children:null===(a=E.coverageGaps)||void 0===a?void 0:a.map((e,s)=>(0,t.jsxs)("div",{className:"border-l-4 border-l-red-500 bg-red-50 p-4 rounded-lg",children:[(0,t.jsxs)("div",{className:"flex justify-between items-start mb-2",children:[(0,t.jsx)("h4",{className:"font-semibold text-red-800",children:e.type}),(0,t.jsx)("span",{className:"px-2 py-1 rounded-full text-xs font-medium ".concat("Critical"===e.severity?"bg-red-200 text-red-800":"High"===e.severity?"bg-orange-200 text-orange-800":"Medium"===e.severity?"bg-yellow-200 text-yellow-800":"bg-blue-200 text-blue-800"),children:e.severity})]}),(0,t.jsx)("p",{className:"text-sm text-red-700",children:e.reason})]},s))})})]}),(0,t.jsxs)(h,{children:[(0,t.jsx)(p,{children:(0,t.jsxs)(u,{className:"text-lg flex items-center",children:[(0,t.jsx)(e6.Z,{className:"h-5 w-5 mr-2 text-blue-600"}),"Recommendations"]})}),(0,t.jsx)(g,{children:(0,t.jsx)("div",{className:"space-y-3",children:null===(n=E.recommendations)||void 0===n?void 0:n.map((e,s)=>(0,t.jsx)("div",{className:"border-l-4 border-l-blue-500 bg-blue-50 p-4 rounded-lg",children:(0,t.jsxs)("div",{className:"flex justify-between items-start mb-2",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-semibold text-blue-800",children:e.type}),(0,t.jsx)("p",{className:"text-sm text-blue-700 mt-1",children:e.benefit})]}),(0,t.jsx)("div",{className:"text-right",children:(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("span",{className:"text-sm font-bold text-green-600",children:e.cost}),(0,t.jsx)("span",{className:"px-2 py-1 rounded-full text-xs font-medium ".concat("Critical"===e.priority?"bg-red-100 text-red-800":"High"===e.priority?"bg-red-100 text-red-800":"Medium"===e.priority?"bg-yellow-100 text-yellow-800":"bg-green-100 text-green-800"),children:e.priority})]})})]})},s))})})]})]})]})})]})},e8=a(50721);let e7=r.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,t.jsx)(e8.fC,{className:(0,o.cn)("peer inline-flex h-6 w-11 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input",a),...l,ref:s,children:(0,t.jsx)(e8.bU,{className:(0,o.cn)("pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0")})})});e7.displayName=e8.fC.displayName;var e9=a(90277);let se=e9.fC,ss=e9.xz,sa=e9.h_,st=r.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,t.jsx)(e9.aV,{className:(0,o.cn)("fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",a),...l,ref:s})});st.displayName=e9.aV.displayName;let sl=r.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,t.jsxs)(sa,{children:[(0,t.jsx)(st,{}),(0,t.jsx)(e9.VY,{ref:s,className:(0,o.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",a),...l})]})});sl.displayName=e9.VY.displayName;let sr=e=>{let{className:s,...a}=e;return(0,t.jsx)("div",{className:(0,o.cn)("flex flex-col space-y-2 text-center sm:text-left",s),...a})};sr.displayName="AlertDialogHeader";let sn=e=>{let{className:s,...a}=e;return(0,t.jsx)("div",{className:(0,o.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",s),...a})};sn.displayName="AlertDialogFooter";let si=r.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,t.jsx)(e9.Dx,{ref:s,className:(0,o.cn)("text-lg font-semibold",a),...l})});si.displayName=e9.Dx.displayName;let sc=r.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,t.jsx)(e9.dk,{ref:s,className:(0,o.cn)("text-sm text-muted-foreground",a),...l})});sc.displayName=e9.dk.displayName;let sd=r.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,t.jsx)(e9.aU,{ref:s,className:(0,o.cn)(m(),a),...l})});sd.displayName=e9.aU.displayName;let so=r.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,t.jsx)(e9.$j,{ref:s,className:(0,o.cn)(m({variant:"outline"}),"mt-2 sm:mt-0",a),...l})});so.displayName=e9.$j.displayName;var sm=a(36086),sx=a(19965);let sh=(0,d.j)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function sp(e){let{className:s,variant:a,...l}=e;return(0,t.jsx)("div",{className:(0,o.cn)(sh({variant:a}),s),...l})}var su=a(86383),sg=a(66961),sj=a(16049),sb=e=>{let{employee:s,isOpen:a,onClose:l}=e;if(!s)return null;let r=s.dependents>0?85:72,n=s.dependents>0?"Gold PPO + Family Dental + Disability":"Standard PPO + Vision",i=s.dependents>0?[...s.dependents>=1?[{name:"Spouse",relationship:"Spouse",age:s.age-2,coverage:"Full Health + Dental"}]:[],...s.dependents>=2?[{name:"Child 1",relationship:"Child",age:12,coverage:"Full Health + Dental + Vision"}]:[],...s.dependents>=3?[{name:"Child 2",relationship:"Child",age:8,coverage:"Full Health + Dental + Vision"}]:[]]:[];return(0,t.jsx)(eH,{open:a,onOpenChange:l,children:(0,t.jsxs)(eL,{className:"max-w-4xl max-h-[90vh] overflow-y-auto",children:[(0,t.jsxs)(eU,{children:[(0,t.jsxs)(eG,{className:"text-2xl font-bold text-gray-900 flex items-center justify-between",children:["Employee Detail: ",s.name,(0,t.jsx)(x,{variant:"ghost",size:"sm",onClick:l,children:(0,t.jsx)(ez.Z,{className:"h-4 w-4"})})]}),(0,t.jsxs)(eY,{className:"text-gray-600",children:["Comprehensive plan analysis and recommendations for ",s.name]})]}),(0,t.jsxs)(eF,{defaultValue:"overview",className:"w-full",children:[(0,t.jsxs)(eE,{className:"grid w-full grid-cols-3",children:[(0,t.jsx)(eI,{value:"overview",children:"Overview & Analysis"}),(0,t.jsx)(eI,{value:"dependents",children:"Family Details"}),(0,t.jsx)(eI,{value:"recommendations",children:"Recommendations"})]}),(0,t.jsxs)(eO,{value:"overview",className:"space-y-6 mt-6",children:[(0,t.jsxs)(h,{children:[(0,t.jsx)(p,{children:(0,t.jsx)(u,{className:"text-xl text-gray-900",children:"Employee Overview"})}),(0,t.jsx)(g,{children:(0,t.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm text-gray-600 mb-1",children:"Department"}),(0,t.jsx)("p",{className:"font-semibold text-gray-900",children:s.department})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm text-gray-600 mb-1",children:"Age"}),(0,t.jsx)("p",{className:"font-semibold text-gray-900",children:s.age})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm text-gray-600 mb-1",children:"Salary"}),(0,t.jsx)("p",{className:"font-semibold text-gray-900",children:"$85,000"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm text-gray-600 mb-1",children:"Dependents"}),(0,t.jsx)("p",{className:"font-semibold text-gray-900",children:s.dependents})]})]})})]}),(0,t.jsxs)(h,{className:"border-blue-200",children:[(0,t.jsx)(p,{children:(0,t.jsxs)(u,{className:"text-xl text-blue-900 flex items-center",children:[(0,t.jsx)(sm.Z,{className:"h-5 w-5 mr-2"}),"Individual Plan Fit Summary"]})}),(0,t.jsxs)(g,{children:[(0,t.jsxs)("div",{className:"bg-green-50 border-l-4 border-green-500 p-4 mb-6",children:[(0,t.jsxs)("div",{className:"flex items-center mb-2",children:[(0,t.jsx)(X.Z,{className:"h-5 w-5 text-green-600 mr-2"}),(0,t.jsx)("span",{className:"font-semibold text-green-800",children:"Recommended Plan:"})]}),(0,t.jsx)("p",{className:"text-xl font-bold text-green-700",children:n})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)("h4",{className:"font-semibold text-gray-900",children:"Plan Fit Analysis:"}),(0,t.jsxs)("div",{className:"flex items-center justify-between p-4 bg-green-50 rounded-lg border border-green-200",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"w-4 h-4 bg-green-500 rounded-full inline-block mr-3"}),(0,t.jsx)("span",{className:"font-semibold text-green-800",children:"Gold PPO"})]}),(0,t.jsxs)("div",{className:"text-right",children:[(0,t.jsxs)("span",{className:"text-2xl font-bold text-green-600",children:[r,"%"]}),(0,t.jsx)(sp,{variant:"secondary",className:"ml-2 bg-green-100 text-green-800",children:r>=80?"Excellent Fit":r>=60?"Good Fit":"Needs Review"})]})]}),(0,t.jsx)("p",{className:"text-sm text-gray-600 ml-7",children:s.dependents>0?"Family with dependents needs comprehensive coverage":"Individual coverage with good network access"})]})]})]}),(0,t.jsxs)("div",{className:"grid md:grid-cols-2 gap-6",children:[(0,t.jsxs)(h,{children:[(0,t.jsx)(p,{children:(0,t.jsx)(u,{className:"text-lg text-gray-900",children:"Current Plan Details"})}),(0,t.jsxs)(g,{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsx)("span",{className:"text-gray-600",children:"Plan Type:"}),(0,t.jsx)(sp,{className:"bg-blue-100 text-blue-800",children:s.plan})]}),(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsx)("span",{className:"text-gray-600",children:"Monthly Premium:"}),(0,t.jsx)("span",{className:"font-semibold text-green-600",children:s.premium})]}),(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsx)("span",{className:"text-gray-600",children:"Coverage Level:"}),(0,t.jsx)(sp,{className:"bg-purple-100 text-purple-800",children:s.dependents>0?"Family":"Individual"})]}),(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsx)("span",{className:"text-gray-600",children:"Dependents:"}),(0,t.jsx)("span",{className:"font-semibold",children:s.dependents})]})]})]}),(0,t.jsxs)(h,{children:[(0,t.jsx)(p,{children:(0,t.jsxs)(u,{className:"text-lg text-gray-900 flex items-center",children:[(0,t.jsx)(eg.Z,{className:"h-5 w-5 mr-2"}),"Cost Analysis"]})}),(0,t.jsx)(g,{children:(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"p-3 bg-blue-50 rounded-lg border border-blue-200",children:[(0,t.jsx)("h4",{className:"font-semibold text-blue-800 mb-1",children:"Annual Premium Cost"}),(0,t.jsxs)("p",{className:"text-xl font-bold text-blue-600",children:["$",12*parseInt(s.premium.replace(/[^\d]/g,""))]}),(0,t.jsx)("p",{className:"text-xs text-blue-700",children:"Current plan cost"})]}),(0,t.jsxs)("div",{className:"p-3 bg-green-50 rounded-lg border border-green-200",children:[(0,t.jsx)("h4",{className:"font-semibold text-green-800 mb-1",children:"Potential Savings"}),(0,t.jsx)("p",{className:"text-xl font-bold text-green-600",children:"$1,200"}),(0,t.jsx)("p",{className:"text-xs text-green-700",children:"With optimized plan"})]}),(0,t.jsxs)("div",{className:"p-3 bg-orange-50 rounded-lg border border-orange-200",children:[(0,t.jsx)("h4",{className:"font-semibold text-orange-800 mb-1",children:"Employer Contribution"}),(0,t.jsx)("p",{className:"text-xl font-bold text-orange-600",children:"75%"}),(0,t.jsx)("p",{className:"text-xs text-orange-700",children:"Of total premium"})]})]})})]})]})]}),(0,t.jsx)(eO,{value:"dependents",className:"space-y-6 mt-6",children:s.dependents>0?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)(h,{children:[(0,t.jsx)(p,{children:(0,t.jsxs)(u,{className:"text-xl text-gray-900 flex items-center",children:[(0,t.jsx)(eu.Z,{className:"h-5 w-5 mr-2"}),"Family Coverage Details"]})}),(0,t.jsx)(g,{children:(0,t.jsx)("div",{className:"grid gap-4",children:i.map((e,s)=>(0,t.jsxs)("div",{className:"p-4 bg-gray-50 rounded-lg border border-gray-200",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)("div",{className:"w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center",children:"Spouse"===e.relationship?(0,t.jsx)(su.Z,{className:"h-5 w-5 text-blue-600"}):(0,t.jsx)(sg.Z,{className:"h-5 w-5 text-blue-600"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-semibold text-gray-900",children:e.name}),(0,t.jsx)("p",{className:"text-sm text-gray-600",children:e.relationship})]})]}),(0,t.jsxs)(sp,{className:"bg-green-100 text-green-800",children:[(0,t.jsx)(sj.Z,{className:"h-3 w-3 mr-1"}),"Covered"]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-4 text-sm",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"text-gray-600",children:"Age:"}),(0,t.jsx)("span",{className:"ml-2 font-medium",children:e.age})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"text-gray-600",children:"Coverage:"}),(0,t.jsx)("span",{className:"ml-2 font-medium",children:e.coverage})]})]})]},s))})})]}),(0,t.jsx)(h,{className:"bg-blue-50 border-blue-200",children:(0,t.jsxs)(g,{className:"p-6",children:[(0,t.jsx)("h3",{className:"font-semibold text-blue-800 mb-3",children:"Family Coverage Summary"}),(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-4 text-sm",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"text-blue-700",children:"Total Covered Members:"}),(0,t.jsx)("span",{className:"ml-2 font-bold text-blue-900",children:s.dependents+1})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"text-blue-700",children:"Family Premium:"}),(0,t.jsx)("span",{className:"ml-2 font-bold text-blue-900",children:s.premium})]})]})]})})]}):(0,t.jsx)(h,{children:(0,t.jsxs)(g,{className:"p-8 text-center",children:[(0,t.jsx)("div",{className:"w-16 h-16 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center",children:(0,t.jsx)(X.Z,{className:"h-8 w-8 text-gray-400"})}),(0,t.jsx)("h3",{className:"text-lg font-semibold text-gray-700 mb-2",children:"Individual Coverage"}),(0,t.jsx)("p",{className:"text-gray-600",children:"This employee does not have any dependents on their plan."})]})})}),(0,t.jsx)(eO,{value:"recommendations",className:"space-y-6 mt-6",children:(0,t.jsxs)(h,{children:[(0,t.jsx)(p,{children:(0,t.jsx)(u,{className:"text-lg text-gray-900",children:"Personalized Recommendations"})}),(0,t.jsx)(g,{children:(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("div",{className:"flex items-start",children:[(0,t.jsx)("span",{className:"text-green-500 mr-2 mt-1",children:"✓"}),(0,t.jsx)("span",{className:"text-sm text-gray-700",children:s.dependents>0?"Consider adding vision benefits for family coverage":"Current plan provides adequate coverage for individual needs"})]}),(0,t.jsxs)("div",{className:"flex items-start",children:[(0,t.jsx)("span",{className:"text-green-500 mr-2 mt-1",children:"✓"}),(0,t.jsx)("span",{className:"text-sm text-gray-700",children:"Optimize HSA contributions for tax savings"})]}),(0,t.jsxs)("div",{className:"flex items-start",children:[(0,t.jsx)("span",{className:"text-green-500 mr-2 mt-1",children:"✓"}),(0,t.jsx)("span",{className:"text-sm text-gray-700",children:"Consider wellness program participation for premium discounts"})]}),s.dependents>0&&(0,t.jsxs)("div",{className:"flex items-start",children:[(0,t.jsx)("span",{className:"text-green-500 mr-2 mt-1",children:"✓"}),(0,t.jsx)("span",{className:"text-sm text-gray-700",children:"Family dental benefits could provide additional savings"})]})]})})]})})]})]})})},sf=()=>{let e=w(),s=k().get("company")||"Your Company",[a,l]=(0,r.useState)(null),[n,i]=(0,r.useState)(!1),[c,d]=(0,r.useState)(!1),[o,m]=(0,r.useState)(!1),[j,b]=(0,r.useState)(null),f=[{icon:eg.Z,label:"Employer Saving Opportunity",value:"$127,500",color:"green",change:"Potential annual savings"},{icon:sm.Z,label:"Risk Score",value:"Low",color:"blue",change:"Better than average"},{icon:ej.Z,label:"Benchmark Rate",value:"8.5%",color:"purple",change:"Below industry average"}],N=()=>{let e="".concat(window.location.origin,"/shared-insight/").concat(s.toLowerCase().replace(/\s+/g,"-"));navigator.clipboard.writeText(e),console.log("Share link copied")},y=e=>{l(e),i(!0)};return(0,t.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50",children:[(0,t.jsx)("header",{className:"border-b bg-white/80 backdrop-blur-sm",children:(0,t.jsxs)("div",{className:"container mx-auto px-4 py-4 flex justify-between items-center",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,t.jsxs)(x,{variant:"ghost",onClick:()=>e("?page=dashboard"),children:[(0,t.jsx)(eQ.Z,{className:"h-4 w-4 mr-2"}),"Back"]}),(0,t.jsx)("div",{className:"text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent",children:"BenOsphere HR"})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsxs)(x,{variant:"outline",onClick:N,children:[(0,t.jsx)(ef.Z,{className:"h-4 w-4 mr-2"}),"Share Report"]}),(0,t.jsxs)(x,{className:"bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700",children:[(0,t.jsx)(P.Z,{className:"h-4 w-4 mr-2"}),"Download PDF"]})]})]})}),(0,t.jsxs)("main",{className:"container mx-auto px-4 py-12 max-w-6xl",children:[(0,t.jsxs)("div",{className:"text-center mb-12",children:[(0,t.jsx)("div",{className:"w-16 h-16 mx-auto mb-4 bg-gradient-to-r from-blue-100 to-purple-100 rounded-full flex items-center justify-center",children:(0,t.jsx)(Z.Z,{className:"h-8 w-8 bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent"})}),(0,t.jsxs)("h1",{className:"text-4xl font-bold text-gray-900 mb-4",children:["\uD83D\uDCCA Benefits Insights for ",s]}),(0,t.jsx)("p",{className:"text-xl text-gray-600",children:"Average Age: 36 • Comprehensive analysis of your employee benefits program"})]}),(0,t.jsxs)(h,{className:"shadow-lg mb-8 border-blue-200",children:[(0,t.jsx)(p,{children:(0,t.jsxs)(u,{className:"text-xl bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent flex items-center",children:[(0,t.jsx)(sx.Z,{className:"h-5 w-5 mr-2"}),"Employee Communications"]})}),(0,t.jsx)(g,{children:(0,t.jsxs)("div",{className:"flex items-center justify-between p-4 bg-blue-50 rounded-lg border border-blue-200",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)("div",{className:"w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center",children:(0,t.jsx)(sx.Z,{className:"h-5 w-5 text-blue-600"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"font-semibold text-blue-900",children:"SMS Notifications for All Employees"}),(0,t.jsx)("p",{className:"text-sm text-blue-700",children:c?"Employees will receive SMS updates about benefits changes and reminders":"Enable SMS notifications for benefits updates. Employees will receive a welcome message to opt in and give consent."})]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)("span",{className:"text-sm font-medium text-blue-800",children:c?"Enabled":"Disabled"}),(0,t.jsx)(e7,{checked:c,onCheckedChange:e=>{b(e),m(!0)}})]})]})})]}),(0,t.jsx)("div",{className:"grid md:grid-cols-3 gap-6 mb-12",children:f.map((e,s)=>{let a=e.icon;return(0,t.jsx)(h,{className:"border-2 ".concat({blue:"bg-blue-100 text-blue-600 border-blue-200",green:"bg-green-100 text-green-600 border-green-200",purple:"bg-purple-100 text-purple-600 border-purple-200",orange:"bg-orange-100 text-orange-600 border-orange-200"}[e.color]," shadow-lg hover:shadow-xl transition-shadow"),children:(0,t.jsxs)(g,{className:"p-6 text-center",children:[(0,t.jsx)("div",{className:"w-12 h-12 mx-auto mb-4 bg-white rounded-full flex items-center justify-center",children:(0,t.jsx)(a,{className:"h-6 w-6"})}),(0,t.jsx)("h3",{className:"font-semibold text-gray-700 mb-2",children:e.label}),(0,t.jsx)("p",{className:"text-2xl font-bold text-gray-900 mb-1",children:e.value}),(0,t.jsx)("p",{className:"text-xs text-gray-500",children:e.change})]})},s)})}),(0,t.jsxs)(h,{className:"shadow-xl mb-8",children:[(0,t.jsx)(p,{children:(0,t.jsx)(u,{className:"text-2xl bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent",children:"\uD83D\uDC65 Employee Insights"})}),(0,t.jsx)(g,{children:(0,t.jsxs)(eF,{defaultValue:"employees",className:"w-full",children:[(0,t.jsxs)(eE,{className:"grid w-full grid-cols-2",children:[(0,t.jsx)(eI,{value:"employees",children:"Employee Directory"}),(0,t.jsx)(eI,{value:"plans",children:"Plan Distribution"})]}),(0,t.jsx)(eO,{value:"employees",className:"mt-6",children:(0,t.jsx)("div",{className:"overflow-x-auto",children:(0,t.jsxs)(e_,{children:[(0,t.jsx)(eW,{children:(0,t.jsxs)(eJ,{children:[(0,t.jsx)(eX,{children:"Employee"}),(0,t.jsx)(eX,{children:"Age"}),(0,t.jsx)(eX,{children:"Department"}),(0,t.jsx)(eX,{children:"Current Plan"}),(0,t.jsx)(eX,{children:"Dependents"}),(0,t.jsx)(eX,{children:"Monthly Premium"})]})}),(0,t.jsx)(eq,{children:[{id:1,name:"John Smith",age:34,department:"Engineering",plan:"Premium Health + Dental",dependents:2,premium:"$850/month"},{id:2,name:"Sarah Johnson",age:28,department:"Marketing",plan:"Standard Health",dependents:0,premium:"$420/month"},{id:3,name:"Michael Chen",age:41,department:"Finance",plan:"Premium Health + Vision",dependents:3,premium:"$920/month"},{id:4,name:"Emily Davis",age:36,department:"HR",plan:"Premium Health + Dental",dependents:1,premium:"$650/month"},{id:5,name:"Robert Wilson",age:29,department:"Sales",plan:"Basic Health",dependents:0,premium:"$320/month"},{id:6,name:"Lisa Anderson",age:43,department:"Operations",plan:"Premium Health + Vision + Dental",dependents:2,premium:"$1080/month"}].map(e=>(0,t.jsxs)(eJ,{className:"cursor-pointer hover:bg-gray-50 transition-colors",onClick:()=>y(e),children:[(0,t.jsx)(eK,{children:(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)("div",{className:"w-8 h-8 bg-gradient-to-r from-blue-100 to-purple-100 rounded-full flex items-center justify-center",children:(0,t.jsx)(X.Z,{className:"h-4 w-4 text-blue-600"})}),(0,t.jsx)("span",{className:"font-medium",children:e.name})]})}),(0,t.jsx)(eK,{children:e.age}),(0,t.jsx)(eK,{children:(0,t.jsx)("span",{className:"px-2 py-1 bg-gray-100 text-gray-800 rounded-full text-xs",children:e.department})}),(0,t.jsx)(eK,{children:(0,t.jsx)("span",{className:"px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs",children:e.plan})}),(0,t.jsx)(eK,{children:e.dependents}),(0,t.jsx)(eK,{className:"font-semibold text-green-600",children:e.premium})]},e.id))})]})})}),(0,t.jsx)(eO,{value:"plans",className:"mt-6",children:(0,t.jsxs)("div",{className:"grid md:grid-cols-2 lg:grid-cols-3 gap-6",children:[(0,t.jsx)(h,{className:"border-blue-200 bg-blue-50",children:(0,t.jsxs)(g,{className:"p-6",children:[(0,t.jsx)("h3",{className:"font-semibold text-blue-800 mb-2",children:"Premium Plans"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-blue-600 mb-1",children:"67%"}),(0,t.jsx)("p",{className:"text-sm text-blue-700",children:"29 employees enrolled"})]})}),(0,t.jsx)(h,{className:"border-green-200 bg-green-50",children:(0,t.jsxs)(g,{className:"p-6",children:[(0,t.jsx)("h3",{className:"font-semibold text-green-800 mb-2",children:"Standard Plans"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-green-600 mb-1",children:"21%"}),(0,t.jsx)("p",{className:"text-sm text-green-700",children:"9 employees enrolled"})]})}),(0,t.jsx)(h,{className:"border-orange-200 bg-orange-50",children:(0,t.jsxs)(g,{className:"p-6",children:[(0,t.jsx)("h3",{className:"font-semibold text-orange-800 mb-2",children:"Basic Plans"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-orange-600 mb-1",children:"12%"}),(0,t.jsx)("p",{className:"text-sm text-orange-700",children:"5 employees enrolled"})]})})]})})]})})]}),(0,t.jsxs)("div",{className:"grid lg:grid-cols-2 gap-8 mb-12",children:[(0,t.jsxs)(h,{className:"shadow-lg",children:[(0,t.jsx)(p,{children:(0,t.jsx)(u,{className:"text-xl bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent",children:"\uD83D\uDC65 Employee Demographics"})}),(0,t.jsx)(g,{children:(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center p-3 bg-blue-50 rounded-lg",children:[(0,t.jsx)("span",{className:"font-medium",children:"Age 25-35"}),(0,t.jsx)("span",{className:"font-bold text-blue-600",children:"18 employees (42%)"})]}),(0,t.jsxs)("div",{className:"flex justify-between items-center p-3 bg-green-50 rounded-lg",children:[(0,t.jsx)("span",{className:"font-medium",children:"Age 36-50"}),(0,t.jsx)("span",{className:"font-bold text-green-600",children:"20 employees (46%)"})]}),(0,t.jsxs)("div",{className:"flex justify-between items-center p-3 bg-orange-50 rounded-lg",children:[(0,t.jsx)("span",{className:"font-medium",children:"Age 50+"}),(0,t.jsx)("span",{className:"font-bold text-orange-600",children:"5 employees (12%)"})]}),(0,t.jsxs)("div",{className:"flex justify-between items-center p-3 bg-purple-50 rounded-lg",children:[(0,t.jsx)("span",{className:"font-medium",children:"Employees with Families"}),(0,t.jsx)("span",{className:"font-bold text-purple-600",children:"28 employees (65%)"})]})]})})]}),(0,t.jsxs)(h,{className:"shadow-lg",children:[(0,t.jsx)(p,{children:(0,t.jsx)(u,{className:"text-xl bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent",children:"\uD83D\uDCB0 Cost Analysis"})}),(0,t.jsx)(g,{children:(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"p-4 bg-green-50 rounded-lg border border-green-200",children:[(0,t.jsx)("h4",{className:"font-semibold text-green-800 mb-2",children:"Potential Annual Savings"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-green-600",children:"$127,500"}),(0,t.jsx)("p",{className:"text-sm text-green-700",children:"By optimizing plan structure"})]}),(0,t.jsxs)("div",{className:"p-4 bg-blue-50 rounded-lg border border-blue-200",children:[(0,t.jsx)("h4",{className:"font-semibold text-blue-800 mb-2",children:"Current Annual Premium"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-blue-600",children:"$486,000"}),(0,t.jsx)("p",{className:"text-sm text-blue-700",children:"Average $11,300 per employee"})]}),(0,t.jsxs)("div",{className:"p-4 bg-orange-50 rounded-lg border border-orange-200",children:[(0,t.jsx)("h4",{className:"font-semibold text-orange-800 mb-2",children:"Benchmarked Rate"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-orange-600",children:"8.5% below"}),(0,t.jsx)("p",{className:"text-sm text-orange-700",children:"Industry average"})]})]})})]})]}),(0,t.jsxs)(h,{className:"shadow-xl mb-8",children:[(0,t.jsx)(p,{children:(0,t.jsx)(u,{className:"text-2xl bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent",children:"\uD83D\uDCA1 Personalized Recommendations"})}),(0,t.jsx)(g,{children:(0,t.jsx)("div",{className:"grid md:grid-cols-3 gap-6",children:[{category:"Cost Savings",items:["Switch to HSA-eligible plan could save $127,500 annually","Optimize contribution strategy for younger demographics","Consider wellness programs to reduce risk factors"]},{category:"Plan Improvements",items:["Add vision benefits - 68% of employees would benefit","Increase mental health coverage options","Consider flexible spending accounts"]},{category:"Employee Engagement",items:["Implement benefits education program","Add telemedicine options for remote workers","Create benefits portal for easy access"]}].map((e,s)=>(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsx)("h3",{className:"font-semibold text-lg text-gray-900",children:e.category}),(0,t.jsx)("ul",{className:"space-y-2",children:e.items.map((e,s)=>(0,t.jsxs)("li",{className:"flex items-start",children:[(0,t.jsx)("span",{className:"text-blue-500 mr-2 mt-1",children:"✓"}),(0,t.jsx)("span",{className:"text-sm text-gray-700",children:e})]},s))})]},s))})})]}),(0,t.jsx)(h,{className:"bg-gradient-to-r from-blue-600 to-purple-600 text-white border-0 shadow-2xl",children:(0,t.jsxs)(g,{className:"p-8 text-center",children:[(0,t.jsx)("h2",{className:"text-2xl font-bold mb-4",children:"\uD83D\uDE80 Share These Insights"}),(0,t.jsx)("p",{className:"text-blue-100 mb-6 text-lg",children:"Help other HR professionals discover BenOsphere's powerful census analysis"}),(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,t.jsxs)(x,{size:"lg",className:"bg-white text-blue-600 hover:bg-gray-100 px-8 py-4",onClick:N,children:[(0,t.jsx)(ef.Z,{className:"mr-2 h-5 w-5"}),"\uD83D\uDCE4 Share with Network"]}),(0,t.jsx)(x,{variant:"outline",size:"lg",className:"border-white text-white hover:bg-white/10 px-8 py-4",onClick:()=>e("/hr-upload"),children:"\uD83D\uDD04 Analyze Another Census"})]})]})}),(0,t.jsx)(h,{className:"mt-8 bg-amber-50 border-amber-200",children:(0,t.jsxs)(g,{className:"p-6",children:[(0,t.jsx)("h3",{className:"font-semibold text-amber-800 mb-3",children:"\uD83C\uDFAF Recommended Next Steps:"}),(0,t.jsxs)("ul",{className:"text-amber-700 space-y-1 text-sm",children:[(0,t.jsx)("li",{children:"• Schedule a consultation with our benefits experts"}),(0,t.jsx)("li",{children:"• Request detailed plan proposals based on these insights"}),(0,t.jsx)("li",{children:"• Share this report with your leadership team"}),(0,t.jsx)("li",{})]})]})})]}),(0,t.jsx)(sb,{employee:a,isOpen:n,onClose:()=>{i(!1),l(null)}}),(0,t.jsx)(se,{open:o,onOpenChange:m,children:(0,t.jsxs)(sl,{children:[(0,t.jsxs)(sr,{children:[(0,t.jsx)(si,{children:j?"Enable SMS Notifications?":"Disable SMS Notifications?"}),(0,t.jsx)(sc,{children:j?"Employees will get a welcome message to opt in and consent to SMS updates. Msg & data rates may apply.":"SMS notifications will be turned off, and employees will stop getting text updates."})]}),(0,t.jsxs)(sn,{children:[(0,t.jsx)(so,{onClick:()=>{m(!1),b(null)},children:"Cancel"}),(0,t.jsx)(sd,{onClick:()=>{null!==j&&(d(j),console.log("SMS notifications ".concat(j?"enabled":"disabled"," for all employees"))),m(!1),b(null)},children:j?"Enable SMS":"Disable SMS"})]})]})})]})},sN=()=>{let e=w(),[s,a]=(0,r.useState)(!1),[l,n]=(0,r.useState)(null),[i,c]=(0,r.useState)(null),[d,o]=(0,r.useState)(!1),m=e=>{if(e.size>10485760)return"File size must be less than 10MB";let s=e.name.toLowerCase().substring(e.name.lastIndexOf("."));return["text/csv","application/vnd.ms-excel","application/vnd.openxmlformats-officedocument.spreadsheetml.sheet","text/plain"].includes(e.type)||[".csv",".xls",".xlsx",".txt"].includes(s)?[".exe",".bat",".cmd",".scr",".pif",".com",".js",".vbs",".jar"].some(s=>e.name.toLowerCase().includes(s))?"File type not allowed for security reasons":e.name.length>255?"File name is too long":null:"Please upload a CSV, Excel (.xls, .xlsx), or text file"},j=e=>{e.preventDefault(),e.stopPropagation(),"dragenter"===e.type||"dragover"===e.type?a(!0):"dragleave"===e.type&&a(!1)};return(0,t.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50",children:[(0,t.jsx)("header",{className:"border-b bg-white/80 backdrop-blur-sm",children:(0,t.jsxs)("div",{className:"container mx-auto px-4 py-4 flex justify-between items-center",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,t.jsxs)(x,{variant:"ghost",onClick:()=>e("/"),children:[(0,t.jsx)(eQ.Z,{className:"h-4 w-4 mr-2"}),"Back"]}),(0,t.jsx)("div",{className:"text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent",children:"BenOsphere"})]}),(0,t.jsx)(ea,{})]})}),(0,t.jsxs)("main",{className:"container mx-auto px-4 py-16 max-w-4xl",children:[(0,t.jsxs)("div",{className:"text-center mb-12",children:[(0,t.jsx)("h1",{className:"text-4xl font-bold text-gray-900 mb-4",children:"\uD83D\uDCE4 Upload Group Census to Get Instant Insights"}),(0,t.jsx)("p",{className:"text-xl text-gray-600",children:"Drop in a census file — our AI will scan, enrich, and generate smart benefit recommendations. No formatting required."})]}),(0,t.jsxs)(h,{className:"shadow-xl border-0",children:[(0,t.jsx)(p,{children:(0,t.jsx)(u,{className:"text-center text-2xl",children:"Upload Census File"})}),(0,t.jsxs)(g,{className:"space-y-6",children:[(0,t.jsx)("div",{className:"border-2 border-dashed rounded-xl p-12 text-center transition-all ".concat(s?"border-blue-500 bg-blue-50":"border-gray-300 hover:border-blue-400 hover:bg-gray-50"),onDragEnter:j,onDragLeave:j,onDragOver:j,onDrop:e=>{if(e.preventDefault(),e.stopPropagation(),a(!1),c(null),e.dataTransfer.files&&e.dataTransfer.files[0]){let s=e.dataTransfer.files[0],a=m(s);a?(c(a),n(null)):n(s)}},children:(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)("div",{className:"mx-auto w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center",children:(0,t.jsx)(eP.Z,{className:"h-8 w-8 text-blue-600"})}),i&&(0,t.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4 mb-4",children:(0,t.jsxs)("p",{className:"text-red-600 font-medium",children:["❌ ",i]})}),l?(0,t.jsxs)("div",{children:[(0,t.jsxs)("p",{className:"text-lg font-semibold text-green-600",children:["✅ ",l.name]}),(0,t.jsxs)("p",{className:"text-sm text-gray-500",children:[(l.size/1024/1024).toFixed(2)," MB"]})]}):(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-xl font-semibold text-gray-700 mb-2",children:"\uD83D\uDCC4 Drag & drop CSV, Excel, or PDF"}),(0,t.jsx)("p",{className:"text-gray-500 mb-4",children:"or"}),(0,t.jsx)("input",{type:"file",id:"file-upload",className:"hidden",accept:".csv,.xlsx,.xls,.pdf",onChange:e=>{if(c(null),e.target.files&&e.target.files[0]){let s=e.target.files[0],a=m(s);a?(c(a),n(null),e.target.value=""):n(s)}}}),(0,t.jsx)("label",{htmlFor:"file-upload",children:(0,t.jsx)(x,{variant:"outline",className:"cursor-pointer bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white border-0",asChild:!0,children:(0,t.jsx)("span",{children:"\uD83D\uDD0D Browse Files"})})})]})]})}),(0,t.jsxs)("div",{className:"text-center text-sm text-gray-500",children:[(0,t.jsx)("p",{children:"Supported formats: CSV, Excel (.xlsx, .xls), PDF"}),(0,t.jsx)("p",{children:"Maximum file size: 50MB"})]}),(0,t.jsx)("div",{className:"text-center",children:(0,t.jsx)(x,{size:"lg",className:"bg-blue-600 hover:bg-blue-700 text-white px-12 py-4 text-lg",onClick:()=>{e("?page=file-preview")},disabled:!l||d,children:d?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("div",{className:"animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"}),"Processing..."]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(S.Z,{className:"mr-2 h-5 w-5"}),"➡️ Generate Report"]})})}),!l&&!i&&(0,t.jsx)("p",{className:"text-center text-sm text-gray-400",children:"Please select a file to generate your report"}),d&&(0,t.jsx)("p",{className:"text-center text-sm text-blue-600",children:"\uD83D\uDD04 Analyzing your census file..."})]})]}),(0,t.jsx)(h,{className:"mt-8 bg-blue-50 border-blue-200",children:(0,t.jsxs)(g,{className:"p-6",children:[(0,t.jsx)("h3",{className:"font-semibold text-blue-900 mb-2",children:"\uD83D\uDCA1 What happens next?"}),(0,t.jsxs)("ul",{className:"text-blue-800 space-y-1 text-sm",children:[(0,t.jsx)("li",{children:"• AI analyzes employee demographics and coverage patterns"}),(0,t.jsx)("li",{children:"• Identifies cost-saving opportunities and risk factors"}),(0,t.jsx)("li",{children:"• Generates benchmarking data vs. similar groups"}),(0,t.jsx)("li",{children:"• Provides personalized plan recommendations"})]})]})})]})]})},sy=()=>{let e=w(),[s,a]=(0,r.useState)(!1),[l,n]=(0,r.useState)(null),i=e=>{e.preventDefault(),e.stopPropagation(),"dragenter"===e.type||"dragover"===e.type?a(!0):"dragleave"===e.type&&a(!1)};return(0,t.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50",children:[(0,t.jsx)("header",{className:"border-b bg-white/80 backdrop-blur-sm",children:(0,t.jsxs)("div",{className:"container mx-auto px-4 py-4 flex justify-between items-center",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,t.jsxs)(x,{variant:"ghost",onClick:()=>e("/"),children:[(0,t.jsx)(eQ.Z,{className:"h-4 w-4 mr-2"}),"Back"]}),(0,t.jsx)("div",{className:"text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent",children:"BenOsphere HR"})]}),(0,t.jsx)(ea,{})]})}),(0,t.jsxs)("main",{className:"container mx-auto px-4 py-16 max-w-4xl",children:[(0,t.jsxs)("div",{className:"text-center mb-12",children:[(0,t.jsx)("div",{className:"w-20 h-20 mx-auto mb-6 bg-blue-100 rounded-full flex items-center justify-center",children:(0,t.jsx)(Z.Z,{className:"h-10 w-10 text-blue-600"})}),(0,t.jsx)("h1",{className:"text-4xl font-bold text-gray-900 mb-4",children:"\uD83C\uDFE2 HR Portal - Upload Employee Census"}),(0,t.jsx)("p",{className:"text-xl text-gray-600",children:"Get instant insights into your employee benefits and potential cost savings"})]}),(0,t.jsx)("form",{onSubmit:s=>{s.preventDefault(),l&&e("/hr-processing")},children:(0,t.jsxs)(h,{className:"shadow-xl border-0",children:[(0,t.jsx)(p,{children:(0,t.jsx)(u,{className:"text-center text-2xl text-blue-700",children:"Upload Employee Census"})}),(0,t.jsxs)(g,{className:"space-y-6",children:[(0,t.jsx)("div",{className:"border-2 border-dashed rounded-xl p-12 text-center transition-all ".concat(s?"border-blue-500 bg-blue-50":"border-gray-300 hover:border-blue-400 hover:bg-gray-50"),onDragEnter:i,onDragLeave:i,onDragOver:i,onDrop:e=>{e.preventDefault(),e.stopPropagation(),a(!1),e.dataTransfer.files&&e.dataTransfer.files[0]&&n(e.dataTransfer.files[0])},children:(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)("div",{className:"mx-auto w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center",children:(0,t.jsx)(eP.Z,{className:"h-8 w-8 text-blue-600"})}),l?(0,t.jsxs)("div",{children:[(0,t.jsxs)("p",{className:"text-lg font-semibold text-blue-600",children:["✅ ",l.name]}),(0,t.jsxs)("p",{className:"text-sm text-gray-500",children:[(l.size/1024/1024).toFixed(2)," MB"]})]}):(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-xl font-semibold text-gray-700 mb-2",children:"\uD83D\uDCC4 Drag & drop employee census file"}),(0,t.jsx)("p",{className:"text-gray-500 mb-4",children:"CSV, Excel, or PDF format"}),(0,t.jsx)("input",{type:"file",id:"file-upload",className:"hidden",accept:".csv,.xlsx,.xls,.pdf",onChange:e=>{e.target.files&&e.target.files[0]&&n(e.target.files[0])}}),(0,t.jsx)("label",{htmlFor:"file-upload",children:(0,t.jsx)(x,{type:"button",variant:"outline",className:"cursor-pointer bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white border-0",asChild:!0,children:(0,t.jsx)("span",{children:"\uD83D\uDD0D Browse Files"})})})]})]})}),(0,t.jsx)("div",{className:"text-center",children:(0,t.jsxs)(x,{type:"submit",size:"lg",className:"bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-12 py-4 text-lg",disabled:!l,children:[(0,t.jsx)(S.Z,{className:"mr-2 h-5 w-5"}),"➡️ Analyze Our Census"]})}),!l&&(0,t.jsx)("p",{className:"text-center text-sm text-gray-400",children:"Please select a file to upload"})]})]})}),(0,t.jsxs)("div",{className:"grid md:grid-cols-2 gap-6 mt-8",children:[(0,t.jsx)(h,{className:"bg-blue-50 border-blue-200",children:(0,t.jsxs)(g,{className:"p-6",children:[(0,t.jsx)("h3",{className:"font-semibold text-blue-900 mb-2",children:"\uD83D\uDCA1 What you'll get:"}),(0,t.jsxs)("ul",{className:"text-blue-800 space-y-1 text-sm",children:[(0,t.jsx)("li",{children:"• Detailed benefits analysis for your employees"}),(0,t.jsx)("li",{children:"• Cost optimization recommendations"}),(0,t.jsx)("li",{children:"• Benchmark against similar companies"}),(0,t.jsx)("li",{children:"• Risk assessment and planning insights"})]})]})}),(0,t.jsx)(h,{className:"bg-purple-50 border-purple-200",children:(0,t.jsxs)(g,{className:"p-6",children:[(0,t.jsx)("h3",{className:"font-semibold text-purple-900 mb-2",children:"\uD83D\uDD12 Your data is secure:"}),(0,t.jsxs)("ul",{className:"text-purple-800 space-y-1 text-sm",children:[(0,t.jsx)("li",{children:"• All data is encrypted and HIPAA compliant"}),(0,t.jsx)("li",{children:"• Files are automatically deleted after analysis"}),(0,t.jsx)("li",{children:"• Only aggregated insights are stored"}),(0,t.jsx)("li",{children:"• Full privacy and confidentiality guaranteed"})]})]})})]})]})]})},sv=a(6394);let sw=(0,d.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),sC=r.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,t.jsx)(sv.f,{ref:s,className:(0,o.cn)(sw(),a),...l})});sC.displayName=sv.f.displayName;var sk=a(79679),sD=a(39547),sS=()=>{let e=w(),[s,a]=(0,r.useState)(""),[l,n]=(0,r.useState)(""),[i,c]=(0,r.useState)(""),[d,o]=(0,r.useState)(!1),[m,j]=(0,r.useState)(!1),b=(0,e5.C)(e=>e.user._id),f=async e=>{if(e.preventDefault(),!s||!l||!i){alert("Please fill out all required fields.");return}j(!0);try{let e=await (0,sD.G9)(b,i,l,s);e&&200===e.status?o(!0):alert("Failed to send invite. Please try again.")}catch(e){console.error("Error sending invite:",e),alert("Failed to send invite. Please try again.")}finally{j(!1)}};return(0,t.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50",children:[(0,t.jsx)("header",{className:"border-b bg-white/80 backdrop-blur-sm",children:(0,t.jsxs)("div",{className:"container mx-auto px-4 py-4 flex justify-between items-center",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,t.jsxs)(x,{variant:"ghost",onClick:()=>e("/"),children:[(0,t.jsx)(eQ.Z,{className:"h-4 w-4 mr-2"}),"Back to Home"]}),(0,t.jsx)("div",{className:"text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent",children:"BenOsphere"})]}),(0,t.jsx)(ea,{})]})}),(0,t.jsxs)("main",{className:"container mx-auto px-4 py-16 max-w-2xl",children:[(0,t.jsxs)("div",{className:"text-center mb-12",children:[(0,t.jsx)("div",{className:"w-20 h-20 mx-auto mb-6 bg-purple-100 rounded-full flex items-center justify-center",children:(0,t.jsx)(eu.Z,{className:"h-10 w-10 text-purple-600"})}),(0,t.jsx)("h1",{className:"text-4xl font-bold text-gray-900 mb-4",children:"\uD83D\uDCE9 Don't have the census? Let the employer upload it."}),(0,t.jsx)("p",{className:"text-xl text-gray-600",children:"Send a secure link to your client — they'll be guided to upload their census. You'll be notified once their enriched group report is ready."})]}),d?(0,t.jsx)(h,{className:"shadow-2xl border-0",children:(0,t.jsxs)(g,{className:"p-12 text-center",children:[(0,t.jsx)("div",{className:"w-20 h-20 mx-auto mb-6 bg-green-100 rounded-full flex items-center justify-center",children:(0,t.jsx)(R.Z,{className:"h-10 w-10 text-green-600"})}),(0,t.jsxs)("h2",{className:"text-2xl font-bold text-gray-900 mb-4",children:["✅ Company invite sent to ",l,"!"]}),(0,t.jsxs)("p",{className:"text-lg text-gray-600 mb-6",children:[s," from ",i," will receive an invite to join the platform and upload their census."]}),(0,t.jsx)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-6 mb-6",children:(0,t.jsxs)("p",{className:"text-green-800 text-sm",children:[(0,t.jsx)("strong",{children:"Next steps:"}),(0,t.jsx)("br",{}),"• The employer will receive a secure upload link",(0,t.jsx)("br",{}),"• You'll get an email notification when analysis is complete",(0,t.jsx)("br",{}),"• Log in to BenOsphere to access the full enriched report"]})}),(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsx)(x,{onClick:()=>e("/"),className:"w-full bg-blue-600 hover:bg-blue-700",children:"Return to Dashboard"}),(0,t.jsx)(x,{variant:"outline",onClick:()=>{o(!1),a(""),n(""),c("")},className:"w-full",children:"Send Another Invite"})]})]})}):(0,t.jsxs)(h,{className:"shadow-2xl border-0",children:[(0,t.jsx)(p,{children:(0,t.jsx)(u,{className:"text-center text-2xl",children:"Send Employer Invite"})}),(0,t.jsxs)(g,{className:"space-y-6",children:[(0,t.jsxs)("form",{onSubmit:f,className:"space-y-6",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(sC,{htmlFor:"company-name",className:"text-base font-medium",children:"Company Name *"}),(0,t.jsx)(el,{id:"company-name",type:"text",placeholder:"e.g. ABC Corp, Smith & Associates",value:i,onChange:e=>c(e.target.value),required:!0,className:"h-12 text-base"})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(sC,{htmlFor:"employer-name",className:"text-base font-medium",children:"Employer Name *"}),(0,t.jsx)(el,{id:"employer-name",type:"text",placeholder:"John Smith",value:s,onChange:e=>a(e.target.value),required:!0,className:"h-12 text-base"})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(sC,{htmlFor:"employer-email",className:"text-base font-medium",children:"\uD83D\uDCE7 Employer Contact Email *"}),(0,t.jsx)(el,{id:"employer-email",type:"email",placeholder:"hr@company.<NAME_EMAIL>",value:l,onChange:e=>n(e.target.value),required:!0,className:"h-12 text-base"})]}),(0,t.jsxs)(x,{type:"submit",size:"lg",className:"w-full bg-purple-600 hover:bg-purple-700 text-white py-4 text-lg",disabled:!l||!s||!i||m,children:[(0,t.jsx)(sk.Z,{className:"mr-2 h-5 w-5"}),m?"Sending...":"➡️ Send Upload Link to Employer"]})]}),(0,t.jsx)(h,{className:"bg-purple-50 border-purple-200",children:(0,t.jsxs)(g,{className:"p-6",children:[(0,t.jsx)("h3",{className:"font-semibold text-purple-900 mb-3",children:"What happens next:"}),(0,t.jsxs)("ul",{className:"text-purple-800 space-y-2 text-sm",children:[(0,t.jsxs)("li",{className:"flex items-start",children:[(0,t.jsx)("span",{className:"mr-2 mt-0.5",children:"1."}),"Employer receives a secure, personalized upload link"]}),(0,t.jsxs)("li",{className:"flex items-start",children:[(0,t.jsx)("span",{className:"mr-2 mt-0.5",children:"2."}),"They upload their census file through our guided process"]}),(0,t.jsxs)("li",{className:"flex items-start",children:[(0,t.jsx)("span",{className:"mr-2 mt-0.5",children:"3."}),"We analyze the data and generate the enriched report"]}),(0,t.jsxs)("li",{className:"flex items-start",children:[(0,t.jsx)("span",{className:"mr-2 mt-0.5",children:"4."}),"You get notified when the analysis is complete and ready to review"]})]})]})}),(0,t.jsxs)("div",{className:"text-center pt-4 border-t",children:[(0,t.jsx)("p",{className:"text-sm text-gray-500 mb-3",children:"Prefer to upload yourself?"}),(0,t.jsx)(x,{variant:"outline",onClick:()=>e("/upload"),className:"w-full",children:"\uD83D\uDCE4 Upload Census File Directly"})]})]})]}),(0,t.jsx)("div",{className:"mt-8 text-center",children:(0,t.jsx)(h,{className:"bg-gray-50 border-gray-200",children:(0,t.jsx)(g,{className:"p-4",children:(0,t.jsx)("p",{className:"text-xs text-gray-600",children:"\uD83D\uDD12 All uploads are secure and encrypted. Census data is processed confidentially and never shared with third parties. HIPAA compliant."})})})})]})]})},sP=()=>{let e=w(),s=[{icon:eu.Z,label:"Total Employees",value:"43",color:"blue"},{icon:ej.Z,label:"Average Age",value:"36",color:"green"},{icon:eu.Z,label:"Dependents per Employee",value:"1.3",color:"purple"},{icon:sm.Z,label:"Suggested Plan Type",value:"PPO + HSA Combo",color:"orange"}];return(0,t.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50",children:[(0,t.jsx)("header",{className:"border-b bg-white/80 backdrop-blur-sm",children:(0,t.jsxs)("div",{className:"container mx-auto px-4 py-4 flex justify-between items-center",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,t.jsxs)(x,{variant:"ghost",onClick:()=>e("/upload"),children:[(0,t.jsx)(eQ.Z,{className:"h-4 w-4 mr-2"}),"Back"]}),(0,t.jsx)("div",{className:"text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent",children:"BenOsphere"})]}),(0,t.jsx)(ea,{})]})}),(0,t.jsxs)("main",{className:"container mx-auto px-4 py-12 max-w-6xl",children:[(0,t.jsxs)("div",{className:"text-center mb-12",children:[(0,t.jsx)("h1",{className:"text-4xl font-bold text-gray-900 mb-4",children:"✅ Smart Snapshot Ready — Here's a Preview"}),(0,t.jsx)("p",{className:"text-xl text-gray-600",children:"Your census analysis is complete. See key insights below."})]}),(0,t.jsx)("div",{className:"grid md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12",children:s.map((e,s)=>{let a=e.icon;return(0,t.jsx)(h,{className:"border-2 ".concat({blue:"bg-blue-100 text-blue-600 border-blue-200",green:"bg-green-100 text-green-600 border-green-200",purple:"bg-purple-100 text-purple-600 border-purple-200",orange:"bg-orange-100 text-orange-600 border-orange-200"}[e.color]," shadow-lg hover:shadow-xl transition-shadow"),children:(0,t.jsxs)(g,{className:"p-6 text-center",children:[(0,t.jsx)("div",{className:"w-12 h-12 mx-auto mb-4 bg-white rounded-full flex items-center justify-center",children:(0,t.jsx)(a,{className:"h-6 w-6"})}),(0,t.jsx)("h3",{className:"font-semibold text-gray-700 mb-2",children:e.label}),(0,t.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:e.value}),(0,t.jsx)("div",{className:"mt-2",children:(0,t.jsx)("span",{className:"text-green-600 text-sm font-medium",children:"✔️"})})]})},s)})}),(0,t.jsxs)(h,{className:"shadow-xl border-0 mb-8",children:[(0,t.jsx)(p,{className:"bg-gradient-to-r from-gray-50 to-gray-100 border-b",children:(0,t.jsxs)(u,{className:"flex items-center text-xl",children:[(0,t.jsx)(eb.Z,{className:"mr-2 h-5 w-5 text-gray-500"}),"Additional Insights (Login Required)"]})}),(0,t.jsx)(g,{className:"p-8",children:(0,t.jsx)("div",{className:"grid md:grid-cols-2 gap-4",children:["\uD83D\uDD12 Cost-saving opportunities","\uD83D\uDD12 Risk segmentation (e.g. 12 employees over 50)","\uD83D\uDD12 Contribution benchmark vs. similar groups","\uD83D\uDD12 Suggested add-ons: vision, disability, life"].map((e,s)=>(0,t.jsxs)("div",{className:"flex items-center p-4 bg-gray-50 rounded-lg border-2 border-dashed border-gray-300",children:[(0,t.jsx)("span",{className:"text-gray-500 mr-3",children:(0,t.jsx)(eb.Z,{className:"h-4 w-4"})}),(0,t.jsx)("span",{className:"text-gray-600 font-medium",children:e})]},s))})})]}),(0,t.jsx)(h,{className:"bg-amber-50 border-amber-200 shadow-lg mb-8",children:(0,t.jsx)(g,{className:"p-6",children:(0,t.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,t.jsx)("div",{className:"bg-amber-100 p-2 rounded-full",children:(0,t.jsx)(eb.Z,{className:"h-5 w-5 text-amber-600"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"font-semibold text-amber-800 mb-2",children:"Preview Mode"}),(0,t.jsx)("p",{className:"text-amber-700",children:"You're seeing a preview of the insights. Log in to unlock the full analysis, plan suggestions, and opportunity flags."})]})]})})}),(0,t.jsxs)("div",{className:"text-center space-y-6",children:[(0,t.jsx)(h,{className:"bg-gradient-to-r from-blue-600 to-purple-600 text-white border-0 shadow-2xl",children:(0,t.jsxs)(g,{className:"p-8",children:[(0,t.jsx)("h2",{className:"text-2xl font-bold mb-4",children:"Ready to see the full picture?"}),(0,t.jsx)("p",{className:"text-blue-100 mb-6 text-lg",children:"Unlock detailed cost analysis, risk insights, and personalized recommendations for this group."}),(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,t.jsx)(x,{size:"lg",className:"bg-white text-blue-600 hover:bg-gray-100 px-8 py-4",onClick:()=>e("/login"),children:"\uD83D\uDD13 Unlock Full Report"}),(0,t.jsx)(x,{variant:"outline",size:"lg",className:"border-white text-white hover:bg-white/10 px-8 py-4",onClick:()=>e("/upload"),children:"\uD83D\uDCE4 Upload Another Census"})]})]})}),(0,t.jsxs)("div",{className:"grid md:grid-cols-3 gap-6 mt-8",children:[(0,t.jsx)(h,{className:"border-dashed border-2 border-gray-300 opacity-75",children:(0,t.jsxs)(g,{className:"p-6 text-center",children:[(0,t.jsx)(eg.Z,{className:"h-8 w-8 mx-auto mb-2 text-gray-400"}),(0,t.jsx)("p",{className:"text-sm text-gray-500",children:"Potential Savings"}),(0,t.jsx)("p",{className:"text-xl font-bold text-gray-400",children:"$XXX,XXX"})]})}),(0,t.jsx)(h,{className:"border-dashed border-2 border-gray-300 opacity-75",children:(0,t.jsxs)(g,{className:"p-6 text-center",children:[(0,t.jsx)(ej.Z,{className:"h-8 w-8 mx-auto mb-2 text-gray-400"}),(0,t.jsx)("p",{className:"text-sm text-gray-500",children:"Risk Score"}),(0,t.jsx)("p",{className:"text-xl font-bold text-gray-400",children:"X.X/10"})]})}),(0,t.jsx)(h,{className:"border-dashed border-2 border-gray-300 opacity-75",children:(0,t.jsxs)(g,{className:"p-6 text-center",children:[(0,t.jsx)(sm.Z,{className:"h-8 w-8 mx-auto mb-2 text-gray-400"}),(0,t.jsx)("p",{className:"text-sm text-gray-500",children:"Recommended Plans"}),(0,t.jsx)("p",{className:"text-xl font-bold text-gray-400",children:"X Plans"})]})})]})]})]})]})},sZ=a(53459),sA=()=>{let e=w(),s=k().get("company")||"Your Company",[a,l]=(0,r.useState)(0),[n,i]=(0,r.useState)(0),c=["Identifying dependents...","Calculating cost drivers...","Matching plan tiers...","Analyzing risk factors...","Generating recommendations..."];return(0,r.useEffect)(()=>{let s=setInterval(()=>{i(a=>a>=100?(clearInterval(s),setTimeout(()=>e("/preview"),1e3),100):a+2)},100),a=setInterval(()=>{l(e=>e<c.length-1?e+1:(clearInterval(a),e))},1200);return()=>{clearInterval(s),clearInterval(a)}},[e,c.length]),(0,t.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 flex items-center justify-center",children:(0,t.jsxs)("div",{className:"container mx-auto px-4 max-w-2xl",children:[(0,t.jsx)(h,{className:"shadow-2xl border-0 overflow-hidden",children:(0,t.jsxs)(g,{className:"p-12 text-center",children:[(0,t.jsxs)("div",{className:"mb-8",children:[(0,t.jsx)("div",{className:"w-20 h-20 mx-auto mb-6 bg-blue-100 rounded-full flex items-center justify-center",children:(0,t.jsx)(sZ.Z,{className:"h-10 w-10 text-blue-600 animate-spin"})}),(0,t.jsxs)("h1",{className:"text-3xl font-bold text-gray-900 mb-4",children:["\uD83D\uDD0D Analyzing Census for ",s]}),(0,t.jsxs)("p",{className:"text-lg text-gray-600 mb-8",children:["We're enriching employee profiles and building a smart group analysis.",(0,t.jsx)("br",{}),(0,t.jsx)("span",{className:"text-sm text-gray-500",children:"(Usually done in under 30 seconds.)"})]})]}),(0,t.jsxs)("div",{className:"mb-8",children:[(0,t.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-3 mb-4",children:(0,t.jsx)("div",{className:"bg-gradient-to-r from-blue-600 to-purple-600 h-3 rounded-full transition-all duration-300 ease-out",style:{width:"".concat(n,"%")}})}),(0,t.jsxs)("p",{className:"text-sm text-gray-500",children:[n,"% Complete"]})]}),(0,t.jsx)("div",{className:"space-y-3",children:c.map((e,s)=>(0,t.jsxs)("div",{className:"flex items-center justify-center p-3 rounded-lg transition-all duration-500 ".concat(s<=a?s===a?"bg-blue-50 text-blue-700 font-medium":"bg-green-50 text-green-700":"text-gray-400"),children:[s<a&&(0,t.jsx)("span",{className:"mr-2",children:"✅"}),s===a&&(0,t.jsx)(sZ.Z,{className:"mr-2 h-4 w-4 animate-spin"}),s>a&&(0,t.jsx)("span",{className:"mr-2",children:"⏳"}),e]},s))}),100===n&&(0,t.jsxs)("div",{className:"mt-8 p-4 bg-green-50 rounded-lg border border-green-200 animate-fade-in",children:[(0,t.jsx)("p",{className:"text-green-700 font-medium",children:"✨ Analysis complete! Redirecting to your report..."}),(0,t.jsx)(x,{className:"mt-4 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white",onClick:()=>e("/preview"),children:"View Report Now"})]})]})}),(0,t.jsx)("div",{className:"mt-8 text-center",children:(0,t.jsx)(h,{className:"bg-gradient-to-r from-purple-100 to-blue-100 border-0",children:(0,t.jsx)(g,{className:"p-6",children:(0,t.jsxs)("p",{className:"text-sm text-gray-700",children:[(0,t.jsx)("strong",{children:"\uD83D\uDCA1 Did you know?"})," Our AI can identify potential cost savings of up to 25% on average for mid-market groups."]})})})})]})})},sR=a(26563),sF=()=>{let e=w(),s=(e,s)=>{let a=e["agency"===s.toLowerCase()?"enterprise":s.toLowerCase()];return"boolean"==typeof a?a?(0,t.jsx)(V.Z,{className:"h-5 w-5 text-green-600 mx-auto"}):(0,t.jsx)(ez.Z,{className:"h-5 w-5 text-red-500 mx-auto"}):(0,t.jsx)("span",{className:"text-sm text-center block",children:a})};return(0,t.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50",children:[(0,t.jsx)("header",{className:"border-b bg-white/80 backdrop-blur-sm",children:(0,t.jsxs)("div",{className:"container mx-auto px-4 py-4 flex justify-between items-center",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,t.jsxs)(x,{variant:"ghost",onClick:()=>e("?page=dashboard"),children:[(0,t.jsx)(eQ.Z,{className:"h-4 w-4 mr-2"}),"Back"]}),(0,t.jsx)("div",{className:"text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent",children:"BenOsphere"})]}),(0,t.jsx)(ea,{})]})}),(0,t.jsxs)("main",{className:"container mx-auto px-4 py-16 max-w-7xl",children:[(0,t.jsxs)("div",{className:"text-center mb-12",children:[(0,t.jsx)("h1",{className:"text-4xl font-bold text-gray-900 mb-4",children:"\uD83D\uDC8E BenOsphere Broker Plans"}),(0,t.jsx)("p",{className:"text-xl text-gray-600 mb-8",children:"Choose the plan that fits your business needs"})]}),(0,t.jsxs)("div",{className:"bg-blue-50 rounded-2xl p-8 mb-12 text-center",children:[(0,t.jsx)("h3",{className:"text-2xl font-bold text-gray-900 mb-4",children:"\uD83D\uDCB3 Credit-Based Pricing"}),(0,t.jsx)("p",{className:"text-lg text-gray-700 mb-4",children:"Simple, transparent pricing based on usage"}),(0,t.jsxs)("div",{className:"grid md:grid-cols-2 gap-6 max-w-2xl mx-auto",children:[(0,t.jsxs)("div",{className:"bg-white rounded-lg p-4 shadow-sm",children:[(0,t.jsx)("div",{className:"text-3xl font-bold text-blue-600",children:"1 Report"}),(0,t.jsx)("div",{className:"text-gray-600",children:"= 2 Credits"})]}),(0,t.jsxs)("div",{className:"bg-white rounded-lg p-4 shadow-sm",children:[(0,t.jsx)("div",{className:"text-3xl font-bold text-green-600",children:"No Waste"}),(0,t.jsx)("div",{className:"text-gray-600",children:"Pay only for what you use"})]})]})]}),(0,t.jsx)("div",{className:"grid md:grid-cols-3 gap-8 mb-16",children:[{name:"Free",price:"$0",period:"",description:"2 reports included",credits:"2 credits",popular:!1,buttonText:"Current Plan",buttonVariant:"outline",onClick:()=>{}},{name:"Pro",price:"Starting at $99",period:"/month",description:"Credit-based pricing",credits:"Choose your plan",popular:!0,buttonText:"Choose Pro Plan",buttonVariant:"default",onClick:()=>e("/billing"),subPlans:[{reports:"50 reports",price:"$99",credits:"25 credits"},{reports:"100 reports",price:"$199",credits:"50 credits"},{reports:"150 reports",price:"$299",credits:"75 credits"},{reports:"200 reports",price:"$479",credits:"100 credits"}]},{name:"Enterprise",price:"Contact Sales",period:"",description:"Custom solutions for large teams",credits:"Unlimited",popular:!1,buttonText:"Contact Sales",buttonVariant:"outline",onClick:()=>e("/billing")}].map((e,s)=>(0,t.jsxs)(h,{className:"relative ".concat(e.popular?"border-2 border-blue-500 shadow-xl":"shadow-lg"),children:[e.popular&&(0,t.jsx)("div",{className:"absolute -top-4 left-1/2 transform -translate-x-1/2",children:(0,t.jsxs)("div",{className:"bg-gradient-to-r from-blue-600 to-purple-600 text-white px-4 py-1 rounded-full text-sm font-medium flex items-center",children:[(0,t.jsx)(sR.Z,{className:"h-4 w-4 mr-1"}),"Most Popular"]})}),(0,t.jsxs)(p,{className:"text-center pb-4",children:[(0,t.jsx)(u,{className:"text-2xl",children:e.name}),(0,t.jsxs)("div",{className:"mt-4",children:[(0,t.jsx)("span",{className:"text-4xl font-bold text-gray-900",children:e.price}),(0,t.jsx)("span",{className:"text-gray-600",children:e.period})]}),(0,t.jsx)("p",{className:"text-gray-600 mt-2",children:e.description}),(0,t.jsx)("div",{className:"mt-2 text-sm text-blue-600 font-medium",children:e.credits}),"Pro"===e.name&&(0,t.jsx)("div",{className:"mt-4 text-xs text-gray-500",children:(0,t.jsx)("div",{className:"text-sm font-medium text-gray-700 mb-2",children:"1 report = 2 credits"})})]}),(0,t.jsxs)(g,{className:"text-center",children:[e.subPlans&&(0,t.jsx)("div",{className:"mb-6 space-y-2",children:e.subPlans.map((e,s)=>(0,t.jsxs)("div",{className:"border rounded-lg p-3 hover:border-blue-300 transition-colors",children:[(0,t.jsx)("div",{className:"font-medium text-gray-900",children:e.reports}),(0,t.jsxs)("div",{className:"text-2xl font-bold text-blue-600",children:[e.price,(0,t.jsx)("span",{className:"text-sm text-gray-500",children:"/month"})]}),(0,t.jsx)("div",{className:"text-xs text-gray-500",children:e.credits})]},s))}),(0,t.jsx)(x,{size:"lg",className:"w-full mb-6 ".concat(e.popular?"bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white":""),variant:e.buttonVariant,onClick:e.onClick,children:e.buttonText})]})]},s))}),(0,t.jsxs)(h,{className:"shadow-2xl",children:[(0,t.jsx)(p,{children:(0,t.jsx)(u,{className:"text-center text-2xl",children:"Feature Comparison"})}),(0,t.jsx)(g,{children:(0,t.jsx)("div",{className:"overflow-x-auto",children:(0,t.jsxs)("table",{className:"w-full",children:[(0,t.jsx)("thead",{children:(0,t.jsxs)("tr",{className:"border-b",children:[(0,t.jsx)("th",{className:"text-left py-4 px-4 font-semibold",children:"Feature"}),(0,t.jsx)("th",{className:"text-center py-4 px-4 font-semibold",children:"Free"}),(0,t.jsx)("th",{className:"text-center py-4 px-4 font-semibold",children:"Pro (Starting $99/mo)"}),(0,t.jsx)("th",{className:"text-center py-4 px-4 font-semibold",children:"Enterprise (Contact Sales)"})]})}),(0,t.jsx)("tbody",{children:[{icon:"\uD83D\uDCCA",name:"Reports Generated",free:"2 reports (1 credit each)",pro:"Based on plan credits",enterprise:"Unlimited"},{icon:"\uD83D\uDD0D",name:"Census Parsing & Enrichment",free:"Full Analysis",pro:"Full Analysis",enterprise:"Full Analysis"},{icon:"\uD83D\uDCC4",name:"Smart Group Summary (PDF)",free:"Basic Report",pro:"Professional Report",enterprise:"White-Label Reports"},{icon:"\uD83C\uDFF7️",name:"Risk Analysis & Tagging",free:"Basic Insights",pro:"Advanced Analytics",enterprise:"Custom Analytics"},{icon:"\uD83D\uDCA1",name:"Plan Recommendations",free:!0,pro:!0,enterprise:!0},{icon:"\uD83D\uDD16",name:"Smart Insights & Tags",free:"Standard Tags",pro:"Advanced Tags",enterprise:"Custom Tags"},{icon:"\uD83D\uDCC8",name:"Benchmarking vs. Industry",free:!1,pro:!0,enterprise:!0},{icon:"\uD83D\uDCB0",name:"Cost Savings Opportunities",free:!1,pro:!0,enterprise:!0},{icon:"\uD83C\uDFAF",name:"Upsell Recommendations",free:!1,pro:!0,enterprise:!0},{icon:"\uD83D\uDC65",name:"Multi-User Access",free:"1 User",pro:"5 Users",enterprise:"Unlimited"},{icon:"\uD83D\uDCDE",name:"Support",free:"Email",pro:"Priority Support",enterprise:"Dedicated Success Manager"},{icon:"\uD83D\uDD17",name:"CRM Integration",free:!1,pro:!0,enterprise:!0},{icon:"\uD83D\uDD04",name:"API Access",free:!1,pro:!1,enterprise:!0}].map((e,a)=>(0,t.jsxs)("tr",{className:"border-b hover:bg-gray-50",children:[(0,t.jsx)("td",{className:"py-4 px-4",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("span",{className:"mr-2",children:e.icon}),(0,t.jsx)("span",{className:"font-medium",children:e.name})]})}),(0,t.jsx)("td",{className:"py-4 px-4 text-center",children:s(e,"free")}),(0,t.jsx)("td",{className:"py-4 px-4 text-center",children:s(e,"pro")}),(0,t.jsx)("td",{className:"py-4 px-4 text-center",children:s(e,"agency")})]},a))})]})})})]}),(0,t.jsx)(h,{className:"mt-8 bg-amber-50 border-amber-200",children:(0,t.jsxs)(g,{className:"p-6 text-center",children:[(0,t.jsx)("h3",{className:"text-xl font-bold text-amber-800 mb-2",children:"⚠️ Free Plan Limit Reached"}),(0,t.jsx)("p",{className:"text-amber-700 mb-4",children:"You've used 5/5 census uploads this month. Upgrade to Pro for unlimited uploads and advanced features."}),(0,t.jsx)(x,{className:"bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white",onClick:()=>e("/billing"),children:"Upgrade Now"})]})}),(0,t.jsxs)("div",{className:"mt-16 text-center",children:[(0,t.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-8",children:"Frequently Asked Questions"}),(0,t.jsxs)("div",{className:"grid md:grid-cols-2 gap-8",children:[(0,t.jsx)(h,{children:(0,t.jsxs)(g,{className:"p-6",children:[(0,t.jsx)("h3",{className:"font-semibold mb-2",children:"Can I change plans anytime?"}),(0,t.jsx)("p",{className:"text-gray-600 text-sm",children:"Yes, you can upgrade or downgrade your plan at any time. Changes take effect immediately."})]})}),(0,t.jsx)(h,{children:(0,t.jsxs)(g,{className:"p-6",children:[(0,t.jsx)("h3",{className:"font-semibold mb-2",children:"Is there a setup fee?"}),(0,t.jsx)("p",{className:"text-gray-600 text-sm",children:"No setup fees. Pay only the monthly subscription fee."})]})})]})]})]})]})},sE=a(10155),sI=a(12100),sO=a(13147),sB=a(14438),sz=()=>{let e=w(),[s,a]=(0,r.useState)("monthly"),[l]=(0,r.useState)(!0),[n]=(0,r.useState)("Pro"),[i]=(0,r.useState)("active"),[c]=(0,r.useState)("2024-08-03"),[d]=(0,r.useState)(249),o={monthly:{pro:{price:249,savings:null},agency:{price:499,savings:null}},annual:{pro:{price:2490,monthly:207.5,savings:498},agency:{price:4990,monthly:415.83,savings:998}}},m=e=>{let a=o[s][e];console.log("Subscribing to ".concat(e," plan (").concat(s,"):"),a),sB.Am.success("Redirecting to secure checkout...",{description:"".concat(e.charAt(0).toUpperCase()+e.slice(1)," plan - ").concat(s," billing")}),setTimeout(()=>{sB.Am.info("Payment integration coming soon!",{description:"This will redirect to Stripe checkout when connected to a payment processor."})},1500)},j=e=>{console.log("Changing plan to ".concat(e,"...")),sB.Am.success("Plan change initiated",{description:"Switching to ".concat(e.charAt(0).toUpperCase()+e.slice(1)," plan. Changes will take effect at your next billing cycle.")})};return l?(0,t.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50",children:[(0,t.jsx)("header",{className:"border-b bg-white/80 backdrop-blur-sm",children:(0,t.jsx)("div",{className:"container mx-auto px-4 py-4 flex justify-between items-center",children:(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,t.jsxs)(x,{variant:"ghost",onClick:()=>e("?page=dashboard"),children:[(0,t.jsx)(eQ.Z,{className:"h-4 w-4 mr-2"}),"Back"]}),(0,t.jsx)("div",{className:"text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent",children:"Billing & Subscription"})]})})}),(0,t.jsxs)("main",{className:"container mx-auto px-4 py-8 max-w-4xl",children:[(0,t.jsxs)(h,{className:"mb-8",children:[(0,t.jsx)(p,{children:(0,t.jsxs)(u,{className:"flex items-center",children:[(0,t.jsx)(eA.Z,{className:"h-5 w-5 mr-2"}),"Current Subscription"]})}),(0,t.jsxs)(g,{children:[(0,t.jsxs)("div",{className:"grid md:grid-cols-3 gap-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"font-semibold text-gray-900",children:"Plan"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-blue-600",children:n}),(0,t.jsx)("p",{className:"text-sm text-gray-600",children:"Monthly billing"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"font-semibold text-gray-900",children:"Status"}),(0,t.jsxs)("div",{className:"flex items-center mt-1",children:[(0,t.jsx)("div",{className:"h-2 w-2 bg-green-500 rounded-full mr-2"}),(0,t.jsx)("span",{className:"text-green-600 capitalize font-medium",children:i})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"font-semibold text-gray-900",children:"Next Billing"}),(0,t.jsxs)("p",{className:"text-lg font-semibold",children:["$",d]}),(0,t.jsxs)("p",{className:"text-sm text-gray-600",children:["on ",c]})]})]}),(0,t.jsxs)("div",{className:"flex gap-4 mt-6",children:[(0,t.jsxs)(x,{onClick:()=>{console.log("Updating payment method..."),sB.Am.info("Redirecting to payment update...",{description:"This will open your payment method settings."})},variant:"outline",children:[(0,t.jsx)(eA.Z,{className:"h-4 w-4 mr-2"}),"Update Payment Method"]}),(0,t.jsxs)(se,{children:[(0,t.jsx)(ss,{asChild:!0,children:(0,t.jsx)(x,{variant:"outline",className:"text-red-600 border-red-200 hover:bg-red-50",children:"Cancel Subscription"})}),(0,t.jsxs)(sl,{children:[(0,t.jsxs)(sr,{children:[(0,t.jsx)(si,{children:"Cancel Subscription"}),(0,t.jsxs)(sc,{children:["Are you sure you want to cancel your subscription? You will lose access to Pro features at the end of your billing period (",c,"). This action cannot be undone."]})]}),(0,t.jsxs)(sn,{children:[(0,t.jsx)(so,{children:"Keep Subscription"}),(0,t.jsx)(sd,{onClick:()=>{console.log("Cancelling subscription..."),sB.Am.success("Subscription cancelled",{description:"You will retain access until your current billing period ends."})},className:"bg-red-600 hover:bg-red-700",children:"Cancel Subscription"})]})]})]})]})]})]}),(0,t.jsxs)(h,{className:"mb-8",children:[(0,t.jsx)(p,{children:(0,t.jsxs)(u,{className:"flex items-center",children:[(0,t.jsx)(sE.Z,{className:"h-5 w-5 mr-2"}),"Change Plan"]})}),(0,t.jsx)(g,{children:(0,t.jsxs)("div",{className:"grid md:grid-cols-2 gap-6",children:[(0,t.jsxs)("div",{className:"p-4 border rounded-lg ".concat("Pro"===n?"border-blue-500 bg-blue-50":"border-gray-200"),children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-lg font-semibold",children:"Pro Plan"}),(0,t.jsxs)("p",{className:"text-2xl font-bold",children:["$249",(0,t.jsx)("span",{className:"text-sm font-normal text-gray-600",children:"/month"})]})]}),"Pro"===n&&(0,t.jsx)("div",{className:"bg-blue-600 text-white px-3 py-1 rounded-full text-sm font-medium",children:"Current Plan"})]}),(0,t.jsxs)("div",{className:"space-y-2 text-sm mb-4",children:[(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(V.Z,{className:"h-4 w-4 text-green-600 mr-2"}),"50 Census uploads/month"]}),(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(V.Z,{className:"h-4 w-4 text-green-600 mr-2"}),"Full enrichment & analytics"]}),(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(V.Z,{className:"h-4 w-4 text-green-600 mr-2"}),"Employer dashboard access"]})]}),"Pro"!==n&&(0,t.jsxs)(x,{onClick:()=>j("pro"),className:"w-full",variant:"outline",children:[(0,t.jsx)(sI.Z,{className:"h-4 w-4 mr-2"}),"Downgrade to Pro"]})]}),(0,t.jsxs)("div",{className:"p-4 border rounded-lg ".concat("Agency"===n?"border-blue-500 bg-blue-50":"border-gray-200"),children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-lg font-semibold",children:"Agency Plan"}),(0,t.jsxs)("p",{className:"text-2xl font-bold",children:["$499",(0,t.jsx)("span",{className:"text-sm font-normal text-gray-600",children:"/month"})]})]}),"Agency"===n&&(0,t.jsx)("div",{className:"bg-blue-600 text-white px-3 py-1 rounded-full text-sm font-medium",children:"Current Plan"})]}),(0,t.jsxs)("div",{className:"space-y-2 text-sm mb-4",children:[(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(V.Z,{className:"h-4 w-4 text-green-600 mr-2"}),"Unlimited census uploads"]}),(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(V.Z,{className:"h-4 w-4 text-green-600 mr-2"}),"Multi-user CRM access"]}),(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(V.Z,{className:"h-4 w-4 text-green-600 mr-2"}),"Priority support"]})]}),"Agency"!==n&&(0,t.jsxs)(x,{onClick:()=>j("agency"),className:"w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white",children:[(0,t.jsx)(sE.Z,{className:"h-4 w-4 mr-2"}),"Upgrade to Agency"]})]})]})})]}),(0,t.jsxs)(h,{children:[(0,t.jsx)(p,{children:(0,t.jsxs)(u,{className:"flex items-center",children:[(0,t.jsx)(eP.Z,{className:"h-5 w-5 mr-2"}),"Billing History"]})}),(0,t.jsx)(g,{children:(0,t.jsx)("div",{className:"space-y-4",children:[{id:"inv_001",date:"2024-07-03",amount:249,status:"paid",pdf:"#"},{id:"inv_002",date:"2024-06-03",amount:249,status:"paid",pdf:"#"},{id:"inv_003",date:"2024-05-03",amount:249,status:"paid",pdf:"#"}].map(e=>(0,t.jsxs)("div",{className:"flex items-center justify-between p-4 border rounded-lg",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,t.jsx)(sO.Z,{className:"h-5 w-5 text-gray-400"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"font-medium",children:e.date}),(0,t.jsxs)("p",{className:"text-sm text-gray-600",children:["Invoice ",e.id]})]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,t.jsxs)("div",{className:"text-right",children:[(0,t.jsxs)("p",{className:"font-semibold",children:["$",e.amount]}),(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(V.Z,{className:"h-4 w-4 text-green-500 mr-1"}),(0,t.jsx)("span",{className:"text-sm text-green-600 capitalize",children:e.status})]})]}),(0,t.jsxs)(x,{variant:"outline",size:"sm",children:[(0,t.jsx)(eP.Z,{className:"h-4 w-4 mr-1"}),"Download"]})]})]},e.id))})})]})]})]}):(0,t.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50",children:[(0,t.jsx)("header",{className:"border-b bg-white/80 backdrop-blur-sm",children:(0,t.jsx)("div",{className:"container mx-auto px-4 py-4 flex justify-between items-center",children:(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,t.jsxs)(x,{variant:"ghost",onClick:()=>e("?page=dashboard"),children:[(0,t.jsx)(eQ.Z,{className:"h-4 w-4 mr-2"}),"Back"]}),(0,t.jsx)("div",{className:"text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent",children:"Choose Your Plan"})]})})}),(0,t.jsxs)("main",{className:"container mx-auto px-4 py-8 max-w-4xl",children:[(0,t.jsx)("div",{className:"text-center mb-8",children:(0,t.jsxs)("div",{className:"inline-flex items-center bg-gray-100 rounded-lg p-1",children:[(0,t.jsx)("button",{onClick:()=>a("monthly"),className:"px-4 py-2 rounded-md text-sm font-medium transition-all ".concat("monthly"===s?"bg-white text-gray-900 shadow-sm":"text-gray-600 hover:text-gray-900"),children:"Monthly"}),(0,t.jsxs)("button",{onClick:()=>a("annual"),className:"px-4 py-2 rounded-md text-sm font-medium transition-all ".concat("annual"===s?"bg-white text-gray-900 shadow-sm":"text-gray-600 hover:text-gray-900"),children:["Annual",(0,t.jsx)("span",{className:"ml-1 text-xs bg-green-100 text-green-700 px-1.5 py-0.5 rounded",children:"Save 20%"})]})]})}),(0,t.jsxs)("div",{className:"grid md:grid-cols-2 gap-8",children:[(0,t.jsxs)(h,{className:"border-2 border-blue-500 shadow-xl relative",children:[(0,t.jsx)("div",{className:"absolute -top-4 left-1/2 transform -translate-x-1/2",children:(0,t.jsx)("div",{className:"bg-gradient-to-r from-blue-600 to-purple-600 text-white px-4 py-1 rounded-full text-sm font-medium",children:"Most Popular"})}),(0,t.jsxs)(p,{className:"text-center pb-4",children:[(0,t.jsx)(u,{className:"text-2xl",children:"Pro"}),(0,t.jsx)("div",{className:"mt-4",children:"monthly"===s?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)("span",{className:"text-4xl font-bold text-gray-900",children:["$",o.monthly.pro.price]}),(0,t.jsx)("span",{className:"text-gray-600",children:"/month"})]}):(0,t.jsxs)("div",{children:[(0,t.jsxs)("span",{className:"text-4xl font-bold text-gray-900",children:["$",o.annual.pro.price]}),(0,t.jsx)("span",{className:"text-gray-600",children:"/year"}),(0,t.jsx)("div",{className:"mt-2",children:(0,t.jsxs)("span",{className:"text-sm text-green-600 font-medium",children:["$",o.annual.pro.monthly,"/month • Save $",o.annual.pro.savings,"/year"]})})]})}),(0,t.jsx)("p",{className:"text-gray-600 mt-2",children:"For growing brokers"})]}),(0,t.jsxs)(g,{className:"text-center",children:[(0,t.jsx)(x,{size:"lg",className:"w-full mb-6 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white",onClick:()=>m("pro"),children:"Subscribe to Pro"}),(0,t.jsxs)("div",{className:"text-left space-y-2 text-sm",children:[(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(V.Z,{className:"h-4 w-4 text-green-600 mr-2"}),"50 Census uploads/month"]}),(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(V.Z,{className:"h-4 w-4 text-green-600 mr-2"}),"Full enrichment & analytics"]}),(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(V.Z,{className:"h-4 w-4 text-green-600 mr-2"}),"Employer dashboard access"]}),(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(V.Z,{className:"h-4 w-4 text-green-600 mr-2"}),"AI-powered RFP builder"]})]})]})]}),(0,t.jsxs)(h,{className:"shadow-lg",children:[(0,t.jsxs)(p,{className:"text-center pb-4",children:[(0,t.jsx)(u,{className:"text-2xl",children:"Agency"}),(0,t.jsx)("div",{className:"mt-4",children:"monthly"===s?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)("span",{className:"text-4xl font-bold text-gray-900",children:["$",o.monthly.agency.price]}),(0,t.jsx)("span",{className:"text-gray-600",children:"/month"})]}):(0,t.jsxs)("div",{children:[(0,t.jsxs)("span",{className:"text-4xl font-bold text-gray-900",children:["$",o.annual.agency.price]}),(0,t.jsx)("span",{className:"text-gray-600",children:"/year"}),(0,t.jsx)("div",{className:"mt-2",children:(0,t.jsxs)("span",{className:"text-sm text-green-600 font-medium",children:["$",o.annual.agency.monthly,"/month • Save $",o.annual.agency.savings,"/year"]})})]})}),(0,t.jsx)("p",{className:"text-gray-600 mt-2",children:"For larger teams"})]}),(0,t.jsxs)(g,{className:"text-center",children:[(0,t.jsx)(x,{size:"lg",variant:"outline",className:"w-full mb-6",onClick:()=>m("agency"),children:"Subscribe to Agency"}),(0,t.jsxs)("div",{className:"text-left space-y-2 text-sm",children:[(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(V.Z,{className:"h-4 w-4 text-green-600 mr-2"}),"Unlimited census uploads"]}),(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(V.Z,{className:"h-4 w-4 text-green-600 mr-2"}),"Multi-user CRM access"]}),(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(V.Z,{className:"h-4 w-4 text-green-600 mr-2"}),"Bulk export capabilities"]}),(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(V.Z,{className:"h-4 w-4 text-green-600 mr-2"}),"Priority support"]})]})]})]})]}),(0,t.jsx)("div",{className:"text-center mt-8",children:(0,t.jsx)("p",{className:"text-sm text-gray-600",children:"\uD83D\uDCB0 30-day money-back guarantee • \uD83D\uDD12 Secure payment • \uD83D\uDCDE Cancel anytime"})})]})]})},sH=a(66314),sM=a(47625),sT=a(42736),s$=a(56940),sL=a(97059),sU=a(2027),sV=a(77719),sG=a(32447),sY=a(86810),s_=a(10062),sW=a(20407),sq=()=>{let{id:e}=C(),s=w();if(!e)return(0,t.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-slate-50 to-blue-50 flex items-center justify-center",children:(0,t.jsx)(h,{className:"max-w-md mx-auto",children:(0,t.jsxs)(g,{className:"p-6 text-center",children:[(0,t.jsx)("h2",{className:"text-xl font-bold mb-2",children:"No Company Selected"}),(0,t.jsx)("p",{className:"text-gray-600 mb-4",children:"Please select a company to generate a proposal for."}),(0,t.jsxs)(x,{onClick:()=>s("/dashboard"),children:[(0,t.jsx)(eQ.Z,{className:"h-4 w-4 mr-2"}),"Back to Dashboard"]})]})})});let a={1:{companyName:"TechCorp Solutions",employees:43,averageAge:36,riskScore:"6.2/10",currentPlanType:"PPO + HSA Combo",suggestedPlan:"Gold PPO with Disability Coverage",expectedSavings:"$127,500",analysisDate:"2024-01-15",planFitData:[{name:"PPO",value:67,color:"#3B82F6"},{name:"HDHP",value:21,color:"#10B981"},{name:"Family/Rx",value:12,color:"#8B5CF6"}],savingsData:[{category:"Current Cost",amount:54e4},{category:"Projected Cost",amount:412500},{category:"Savings",amount:127500}],keyInsights:["Majority of employees fall in high-utilization age bracket (36 avg age)","Disability coverage crucial due to technology industry risk profile","67% of workforce suits comprehensive PPO coverage","Current HSA utilization below industry average"],recommendations:[{category:"Primary Plan Change",description:"Transition to Gold PPO with enhanced disability coverage",impact:"Reduce overall costs by 23% while improving coverage"},{category:"Voluntary Benefits",description:"Add dental and vision upgrade options",impact:"Increase employee satisfaction and retention"},{category:"Add-on Services",description:"Include telehealth and mental health support",impact:"Preventive care reducing long-term claims costs"}],industryBenchmark:{currentPercentile:45,projectedPercentile:78,avgSavings:"$95,000"}}}[e];return a?(0,t.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-slate-50 to-blue-50",children:[(0,t.jsx)("header",{className:"border-b bg-white/90 backdrop-blur-sm sticky top-0 z-50",children:(0,t.jsxs)("div",{className:"container mx-auto px-3 sm:px-4 py-3 flex justify-between items-center",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,t.jsxs)(x,{variant:"ghost",size:"sm",onClick:()=>s("/dashboard"),children:[(0,t.jsx)(eQ.Z,{className:"h-4 w-4 mr-2"}),"Back to Dashboard"]}),(0,t.jsx)("div",{className:"text-xl sm:text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent",children:"BenOsphere"})]}),(0,t.jsx)("div",{className:"flex items-center space-x-2",children:(0,t.jsx)(ea,{})})]})}),(0,t.jsxs)("main",{className:"container mx-auto px-3 sm:px-4 py-6 max-w-6xl",children:[(0,t.jsxs)("div",{className:"text-center mb-8",children:[(0,t.jsx)("h1",{className:"text-4xl font-bold text-gray-900 mb-2",children:"Benefits Proposal"}),(0,t.jsx)("h2",{className:"text-2xl text-blue-600 font-semibold",children:a.companyName}),(0,t.jsxs)("p",{className:"text-gray-600 mt-2",children:["Generated on ",new Date(a.analysisDate).toLocaleDateString()]})]}),(0,t.jsxs)(h,{className:"mb-6 shadow-lg border-0",children:[(0,t.jsx)(p,{children:(0,t.jsxs)(u,{className:"flex items-center",children:[(0,t.jsx)("div",{className:"bg-blue-100 p-2 rounded-lg mr-3",children:(0,t.jsx)(eu.Z,{className:"h-5 w-5 text-blue-600"})}),"Client Overview"]})}),(0,t.jsx)(g,{children:(0,t.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-6",children:[(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("h4",{className:"text-sm font-medium text-gray-500 mb-1",children:"Total Employees"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:a.employees})]}),(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("h4",{className:"text-sm font-medium text-gray-500 mb-1",children:"Average Age"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:a.averageAge})]}),(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("h4",{className:"text-sm font-medium text-gray-500 mb-1",children:"Risk Score"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:a.riskScore})]}),(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("h4",{className:"text-sm font-medium text-gray-500 mb-1",children:"Current Plan"}),(0,t.jsx)("p",{className:"text-lg font-semibold text-gray-900",children:a.currentPlanType})]})]})})]}),(0,t.jsxs)(h,{className:"mb-6 shadow-lg border-0",children:[(0,t.jsx)(p,{children:(0,t.jsxs)(u,{className:"flex items-center",children:[(0,t.jsx)("div",{className:"bg-green-100 p-2 rounded-lg mr-3",children:(0,t.jsx)(e4.Z,{className:"h-5 w-5 text-green-600"})}),"Recommended Plan"]})}),(0,t.jsxs)(g,{children:[(0,t.jsxs)("div",{className:"bg-gradient-to-r from-yellow-50 to-orange-50 p-6 rounded-lg border-l-4 border-yellow-500 mb-4",children:[(0,t.jsx)("h3",{className:"text-2xl font-bold text-orange-800 mb-2",children:a.suggestedPlan}),(0,t.jsx)("p",{className:"text-gray-700 mb-4",children:"This plan is recommended based on your workforce demographics, current utilization patterns, and industry risk profile. The comprehensive PPO structure with enhanced disability coverage addresses the needs of your technology workforce."}),(0,t.jsxs)("div",{className:"flex items-center justify-between bg-white/50 p-4 rounded-lg",children:[(0,t.jsx)("span",{className:"text-lg font-semibold text-gray-700",children:"Expected Annual Savings"}),(0,t.jsx)("span",{className:"text-3xl font-bold text-green-600",children:a.expectedSavings})]})]}),(0,t.jsxs)("div",{className:"grid md:grid-cols-2 gap-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"text-lg font-semibold text-gray-900 mb-3",children:"Cost Comparison"}),(0,t.jsx)(sM.h,{width:"100%",height:250,children:(0,t.jsxs)(sT.v,{data:a.savingsData,children:[(0,t.jsx)(s$.q,{strokeDasharray:"3 3"}),(0,t.jsx)(sL.K,{dataKey:"category"}),(0,t.jsx)(sU.Q,{}),(0,t.jsx)(sV.u,{formatter:e=>["$".concat(e.toLocaleString()),"Amount"]}),(0,t.jsx)(sG.$,{dataKey:"amount",fill:"#3B82F6"})]})})]}),(0,t.jsx)("div",{className:"flex items-center justify-center",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"text-4xl font-bold text-green-600 mb-2",children:"23%"}),(0,t.jsx)("div",{className:"text-lg text-gray-600",children:"Cost Reduction"}),(0,t.jsx)("div",{className:"text-sm text-gray-500 mt-2",children:"vs. Current Plan"})]})})]})]})]}),(0,t.jsxs)(h,{className:"mb-6 shadow-lg border-0",children:[(0,t.jsx)(p,{children:(0,t.jsxs)(u,{className:"flex items-center",children:[(0,t.jsx)("div",{className:"bg-purple-100 p-2 rounded-lg mr-3",children:(0,t.jsx)(eD.Z,{className:"h-5 w-5 text-purple-600"})}),"Plan Fit Breakdown"]})}),(0,t.jsx)(g,{children:(0,t.jsxs)("div",{className:"grid md:grid-cols-2 gap-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"text-lg font-semibold text-gray-900 mb-3",children:"Employee Coverage Distribution"}),(0,t.jsx)(sM.h,{width:"100%",height:250,children:(0,t.jsxs)(sY.u,{children:[(0,t.jsx)(s_.b,{data:a.planFitData,cx:"50%",cy:"50%",outerRadius:80,dataKey:"value",label:e=>{let{name:s,value:a}=e;return"".concat(s,": ").concat(a,"%")},children:a.planFitData.map((e,s)=>(0,t.jsx)(sW.b,{fill:e.color},"cell-".concat(s)))}),(0,t.jsx)(sV.u,{})]})})]}),(0,t.jsx)("div",{className:"space-y-4",children:a.planFitData.map((e,s)=>(0,t.jsxs)("div",{className:"flex items-center justify-between p-3 rounded-lg",style:{backgroundColor:"".concat(e.color,"10")},children:[(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"w-4 h-4 rounded mr-3",style:{backgroundColor:e.color}}),(0,t.jsxs)("span",{className:"font-medium",children:[e.name," Coverage"]})]}),(0,t.jsxs)("span",{className:"text-lg font-bold",style:{color:e.color},children:[e.value,"%"]})]},s))})]})})]}),(0,t.jsxs)(h,{className:"mb-6 shadow-lg border-0",children:[(0,t.jsx)(p,{children:(0,t.jsxs)(u,{className:"flex items-center",children:[(0,t.jsx)("div",{className:"bg-orange-100 p-2 rounded-lg mr-3",children:(0,t.jsx)(F.Z,{className:"h-5 w-5 text-orange-600"})}),"Key Insights"]})}),(0,t.jsx)(g,{children:(0,t.jsx)("div",{className:"grid md:grid-cols-2 gap-4",children:a.keyInsights.map((e,s)=>(0,t.jsxs)("div",{className:"flex items-start p-4 bg-blue-50 rounded-lg",children:[(0,t.jsx)(A.Z,{className:"h-5 w-5 text-blue-600 mr-3 mt-0.5 flex-shrink-0"}),(0,t.jsx)("p",{className:"text-gray-700",children:e})]},s))})})]}),(0,t.jsxs)(h,{className:"mb-6 shadow-lg border-0",children:[(0,t.jsx)(p,{children:(0,t.jsxs)(u,{className:"flex items-center",children:[(0,t.jsx)("div",{className:"bg-green-100 p-2 rounded-lg mr-3",children:(0,t.jsx)(A.Z,{className:"h-5 w-5 text-green-600"})}),"Recommendation Summary"]})}),(0,t.jsx)(g,{children:(0,t.jsx)("div",{className:"space-y-4",children:a.recommendations.map((e,s)=>(0,t.jsx)(h,{className:"border-l-4 border-l-green-500",children:(0,t.jsxs)(g,{className:"p-4",children:[(0,t.jsx)("h4",{className:"font-semibold text-gray-900 mb-2",children:e.category}),(0,t.jsx)("p",{className:"text-gray-700 mb-2",children:e.description}),(0,t.jsx)("p",{className:"text-sm text-green-600 font-medium",children:e.impact})]})},s))})})]}),(0,t.jsxs)(h,{className:"mb-6 shadow-lg border-0",children:[(0,t.jsx)(p,{children:(0,t.jsxs)(u,{className:"flex items-center",children:[(0,t.jsx)("div",{className:"bg-indigo-100 p-2 rounded-lg mr-3",children:(0,t.jsx)(eD.Z,{className:"h-5 w-5 text-indigo-600"})}),"Industry Benchmark"]})}),(0,t.jsx)(g,{children:(0,t.jsxs)("div",{className:"grid md:grid-cols-3 gap-6 text-center",children:[(0,t.jsxs)("div",{className:"p-4 bg-red-50 rounded-lg",children:[(0,t.jsx)("h4",{className:"text-sm font-medium text-gray-500 mb-2",children:"Current Position"}),(0,t.jsxs)("p",{className:"text-2xl font-bold text-red-600",children:[a.industryBenchmark.currentPercentile,"th"]}),(0,t.jsx)("p",{className:"text-sm text-gray-600",children:"Percentile"})]}),(0,t.jsxs)("div",{className:"p-4 bg-green-50 rounded-lg",children:[(0,t.jsx)("h4",{className:"text-sm font-medium text-gray-500 mb-2",children:"Projected Position"}),(0,t.jsxs)("p",{className:"text-2xl font-bold text-green-600",children:[a.industryBenchmark.projectedPercentile,"th"]}),(0,t.jsx)("p",{className:"text-sm text-gray-600",children:"Percentile"})]}),(0,t.jsxs)("div",{className:"p-4 bg-blue-50 rounded-lg",children:[(0,t.jsx)("h4",{className:"text-sm font-medium text-gray-500 mb-2",children:"Industry Average Savings"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-blue-600",children:a.industryBenchmark.avgSavings}),(0,t.jsx)("p",{className:"text-sm text-gray-600",children:"Annual"})]})]})})]}),(0,t.jsx)(h,{className:"bg-gradient-to-r from-blue-100 to-purple-100 border-0 shadow-lg",children:(0,t.jsxs)(g,{className:"p-6",children:[(0,t.jsx)("h3",{className:"text-xl font-bold text-gray-900 mb-4 text-center",children:"Next Steps"}),(0,t.jsxs)("div",{className:"grid sm:grid-cols-2 lg:grid-cols-4 gap-4",children:[(0,t.jsxs)(x,{className:"bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700",children:[(0,t.jsx)(P.Z,{className:"h-4 w-4 mr-2"}),"Generate PDF"]}),(0,t.jsxs)(x,{variant:"outline",children:[(0,t.jsx)(sO.Z,{className:"h-4 w-4 mr-2"}),"Schedule Presentation"]}),(0,t.jsxs)(x,{variant:"outline",children:[(0,t.jsx)(ef.Z,{className:"h-4 w-4 mr-2"}),"Share with Client"]}),(0,t.jsxs)(x,{variant:"outline",children:[(0,t.jsx)(sH.Z,{className:"h-4 w-4 mr-2"}),"Re-run Analysis"]})]})]})})]})]}):(0,t.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-slate-50 to-blue-50 flex items-center justify-center",children:(0,t.jsx)(h,{className:"max-w-md mx-auto",children:(0,t.jsxs)(g,{className:"p-6 text-center",children:[(0,t.jsx)("h2",{className:"text-xl font-bold mb-2",children:"Company Not Found"}),(0,t.jsx)("p",{className:"text-gray-600 mb-4",children:"The requested company proposal could not be found."}),(0,t.jsxs)(x,{onClick:()=>s("/dashboard"),children:[(0,t.jsx)(eQ.Z,{className:"h-4 w-4 mr-2"}),"Back to Dashboard"]})]})})})},sJ=()=>{let e=w(),[s,l]=(0,r.useState)(""),[n,i]=(0,r.useState)(!1),[c,d]=(0,r.useState)(!1),[o,m]=(0,r.useState)(""),p=async e=>{if(e.preventDefault(),s){d(!0),m("");try{let{selfOnboard:e}=await a.e(7534).then(a.bind(a,77534)),t=await e(s);"magic_link_sent"===t?i(!0):"ask_admin_to_add"===t?m("No account found. Please contact your admin to get access."):m("Failed to send magic link. Please try again.")}catch(e){console.error("Login error:",e),m("Network error. Please try again.")}finally{d(!1)}}};return(0,t.jsxs)("div",{className:"min-h-screen bg-gray-50 flex flex-col px-4 py-6",children:[(0,t.jsx)("div",{className:"absolute top-4 left-4",children:(0,t.jsx)(x,{variant:"ghost",onClick:()=>e("/"),className:"text-gray-600 hover:bg-gray-100 p-2",children:(0,t.jsx)(eQ.Z,{className:"h-4 w-4"})})}),(0,t.jsx)("div",{className:"flex-1 flex items-center justify-center",children:(0,t.jsxs)("div",{className:"w-full max-w-5xl",children:[(0,t.jsxs)("div",{className:"text-center mb-6",children:[(0,t.jsx)("div",{className:"w-10 h-10 bg-gradient-to-r from-orange-400 to-orange-500 rounded-lg flex items-center justify-center mx-auto mb-3",children:(0,t.jsx)("span",{className:"text-white text-lg",children:"\uD83D\uDD13"})}),(0,t.jsx)("h1",{className:"text-2xl font-bold text-gray-900 mb-2",children:"Sign in to Upload Census"}),(0,t.jsx)("p",{className:"text-gray-600 text-sm",children:"Enter your email to get instant access to our census analysis platform."})]}),n?(0,t.jsx)(h,{className:"bg-white shadow-lg max-w-md mx-auto",children:(0,t.jsxs)(g,{className:"p-6 text-center",children:[(0,t.jsx)("div",{className:"w-16 h-16 mx-auto mb-4 bg-green-100 rounded-full flex items-center justify-center",children:(0,t.jsx)(R.Z,{className:"h-8 w-8 text-green-600"})}),(0,t.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-3",children:"✅ Magic Link Sent!"}),(0,t.jsxs)("p",{className:"text-lg text-gray-600 mb-4",children:["We've sent a secure login link to ",(0,t.jsx)("strong",{children:s})]}),(0,t.jsx)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4",children:(0,t.jsxs)("p",{className:"text-blue-800 text-sm",children:[(0,t.jsx)("strong",{children:"Next steps:"}),(0,t.jsx)("br",{}),"1. Check your email inbox (and spam folder)",(0,t.jsx)("br",{}),"2. Click the login link we sent",(0,t.jsx)("br",{}),"3. Access your census upload page"]})}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(x,{onClick:()=>{e("/upload-census")},className:"w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-base py-2",children:"Continue to Census Upload"}),(0,t.jsx)(x,{variant:"link",onClick:()=>e("/"),className:"w-full text-gray-500",children:"Back to Home"})]})]})}):(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,t.jsx)(h,{className:"bg-white shadow-lg",children:(0,t.jsxs)(g,{className:"p-6",children:[(0,t.jsx)("h2",{className:"text-xl font-semibold text-gray-900 mb-4",children:"Get Instant Access"}),(0,t.jsxs)("form",{onSubmit:p,className:"space-y-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsxs)("label",{htmlFor:"email",className:"flex items-center text-sm font-medium text-gray-700 mb-2",children:[(0,t.jsx)(R.Z,{className:"h-4 w-4 mr-2 text-blue-500"}),"Email Address"]}),(0,t.jsx)(el,{id:"email",type:"email",placeholder:"Enter your email address",value:s,onChange:e=>l(e.target.value),required:!0,className:"h-11 text-base border-gray-300 focus:border-blue-500 focus:ring-blue-500"})]}),o&&(0,t.jsx)("div",{className:"mb-4 p-3 bg-red-50 border border-red-200 rounded-md",children:(0,t.jsx)("p",{className:"text-sm text-red-600",children:o})}),(0,t.jsxs)(x,{type:"submit",className:"w-full h-11 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-medium",disabled:!s||c,children:[(0,t.jsx)(R.Z,{className:"h-4 w-4 mr-2"}),c?"Sending...":"Send Magic Link"]})]}),(0,t.jsxs)("div",{className:"mt-4 flex items-center justify-center space-x-2 text-sm text-gray-600",children:[(0,t.jsx)(sm.Z,{className:"h-4 w-4 text-green-600"}),(0,t.jsx)("span",{children:"We're HIPAA compliant and your data is in safe hands"})]})]})}),(0,t.jsx)(h,{className:"bg-blue-50 border-blue-200",children:(0,t.jsxs)(g,{className:"p-6",children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"What you'll unlock:"}),(0,t.jsx)("div",{className:"space-y-3 mb-6",children:["Complete cost analysis and potential savings breakdown","Detailed risk segmentation by age, family status, and more","Benchmarking against similar industry groups","Personalized plan recommendations and add-on suggestions","Export capabilities and white-label client presentations"].map((e,s)=>(0,t.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,t.jsx)("div",{className:"flex-shrink-0 w-5 h-5 rounded-full bg-green-500 flex items-center justify-center mt-0.5",children:(0,t.jsx)(V.Z,{className:"h-3 w-3 text-white"})}),(0,t.jsx)("p",{className:"text-gray-700 text-sm leading-relaxed",children:e})]},s))}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"text-base font-medium text-gray-900 mb-3",children:"Brokers like you are enjoying it:"}),(0,t.jsx)("div",{className:"space-y-3",children:[{name:"Sarah M.",image:"https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face",text:"This platform has revolutionized how I analyze census data for my clients."},{name:"Michael R.",image:"https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face",text:"The detailed insights help me provide better recommendations to employers."},{name:"Jennifer L.",image:"https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face",text:"I save hours of work with the automated analysis and reporting features."}].map((e,s)=>(0,t.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,t.jsxs)(M,{className:"h-8 w-8 flex-shrink-0",children:[(0,t.jsx)(T,{src:e.image,alt:e.name}),(0,t.jsx)($,{children:e.name.charAt(0)})]}),(0,t.jsxs)("div",{children:[(0,t.jsxs)("p",{className:"text-xs text-gray-700 italic",children:['"',e.text,'"']}),(0,t.jsxs)("p",{className:"text-xs text-gray-500 mt-1",children:["- ",e.name]})]})]},s))})]})]})})]})]})}),(0,t.jsx)("div",{className:"text-center mt-4",children:(0,t.jsx)("p",{className:"text-xs text-gray-500",children:"\xa92020 Felix All rights reserved."})})]})},sX=()=>{let e=w();return(0,r.useEffect)(()=>{console.error("404 Error: User attempted to access non-existent route")},[]),(0,t.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gradient-to-br from-slate-50 to-blue-50",children:(0,t.jsxs)("div",{className:"text-center p-8",children:[(0,t.jsx)("div",{className:"w-24 h-24 mx-auto mb-6 bg-gradient-to-r from-blue-100 to-purple-100 rounded-full flex items-center justify-center",children:(0,t.jsx)("span",{className:"text-4xl",children:"\uD83D\uDD0D"})}),(0,t.jsx)("h1",{className:"text-6xl font-bold mb-4 bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent",children:"404"}),(0,t.jsx)("p",{className:"text-xl text-gray-600 mb-6",children:"Oops! Page not found"}),(0,t.jsx)("p",{className:"text-gray-500 mb-8",children:"The page you're looking for doesn't exist or has been moved."}),(0,t.jsxs)(x,{onClick:()=>e("/"),className:"bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700",children:[(0,t.jsx)(ek.Z,{className:"mr-2 h-4 w-4"}),"Return to Home"]})]})})};let sK=(0,d.j)("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive"}},defaultVariants:{variant:"default"}}),sQ=r.forwardRef((e,s)=>{let{className:a,variant:l,...r}=e;return(0,t.jsx)("div",{ref:s,role:"alert",className:(0,o.cn)(sK({variant:l}),a),...r})});sQ.displayName="Alert",r.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,t.jsx)("h5",{ref:s,className:(0,o.cn)("mb-1 font-medium leading-none tracking-tight",a),...l})}).displayName="AlertTitle";let s0=r.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,t.jsx)("div",{ref:s,className:(0,o.cn)("text-sm [&_p]:leading-relaxed",a),...l})});s0.displayName="AlertDescription";let s2=["Employee ID","First Name","Last Name","Date of Birth","Gender","Department","Salary","Hire Date"];var s4=()=>{let e=w(),[s,a]=(0,r.useState)(!1),[l,n]=(0,r.useState)(null),[i,c]=(0,r.useState)(null),[d,o]=(0,r.useState)(!1),m=e=>{e.preventDefault(),e.stopPropagation(),"dragenter"===e.type||"dragover"===e.type?a(!0):"dragleave"===e.type&&a(!1)},j=e=>{n(e),b(e)},b=async e=>{o(!0),setTimeout(()=>{let e={headers:["Employee ID","First Name","Last Name","Date of Birth","Gender","Department","Salary","Hire Date"],rows:[["001","John","Doe","1985-03-15","Male","Engineering","$75,000","2020-01-15"],["002","Jane","Smith","1990-07-22","Female","Marketing","$65,000","2021-03-10"],["003","Mike","Johnson","1988-11-08","Male","Sales","$70,000","2019-08-20"],["004","Sarah","Wilson","1992-05-12","Female","HR","$60,000","2022-02-01"],["005","David","Brown","1987-09-30","Male","Finance","$80,000","2018-11-15"]],errors:[]},s=s2.filter(s=>!e.headers.includes(s));s.length>0&&e.errors.push({type:"missing_column",message:"Missing required columns: ".concat(s.join(", "))}),c(e),o(!1)},2e3)};return(0,t.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50",children:[(0,t.jsx)("header",{className:"border-b bg-white/80 backdrop-blur-sm",children:(0,t.jsx)("div",{className:"container mx-auto px-4 py-4 flex justify-between items-center",children:(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,t.jsxs)(x,{variant:"ghost",onClick:()=>{e("?page=upload-census")},children:[(0,t.jsx)(eQ.Z,{className:"h-4 w-4 mr-2"}),"Back to Upload"]}),(0,t.jsx)("div",{className:"text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent",children:"File Preview & Validation"})]})})}),(0,t.jsx)("main",{className:"container mx-auto px-4 py-8 max-w-7xl",children:l?(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)(h,{children:[(0,t.jsx)(p,{children:(0,t.jsxs)(u,{className:"flex items-center gap-2",children:[(0,t.jsx)(eP.Z,{className:"h-5 w-5"}),"File Information"]})}),(0,t.jsx)(g,{children:(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm text-gray-600",children:"File Name"}),(0,t.jsx)("p",{className:"font-medium",children:l.name})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm text-gray-600",children:"File Size"}),(0,t.jsxs)("p",{className:"font-medium",children:[(l.size/1024/1024).toFixed(2)," MB"]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm text-gray-600",children:"File Type"}),(0,t.jsx)("p",{className:"font-medium",children:l.type||"Unknown"})]})]})})]}),d?(0,t.jsx)(h,{children:(0,t.jsxs)(g,{className:"p-12 text-center",children:[(0,t.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"}),(0,t.jsx)("h3",{className:"text-xl font-semibold mb-2",children:"Processing File..."}),(0,t.jsx)("p",{className:"text-gray-600",children:"Validating data structure and content"})]})}):i&&(0,t.jsxs)(t.Fragment,{children:[i.errors.length>0?(0,t.jsxs)(sQ,{className:"border-red-200 bg-red-50",children:[(0,t.jsx)(F.Z,{className:"h-4 w-4 text-red-600"}),(0,t.jsxs)(s0,{className:"text-red-800",children:[(0,t.jsx)("strong",{children:"Validation Issues Found:"}),(0,t.jsx)("ul",{className:"mt-2 space-y-1",children:i.errors.map((e,s)=>(0,t.jsxs)("li",{children:["• ",e.message]},s))})]})]}):(0,t.jsxs)(sQ,{className:"border-green-200 bg-green-50",children:[(0,t.jsx)(A.Z,{className:"h-4 w-4 text-green-600"}),(0,t.jsxs)(s0,{className:"text-green-800",children:[(0,t.jsx)("strong",{children:"File validation passed!"})," Your census file is ready for processing."]})]}),(0,t.jsxs)(h,{children:[(0,t.jsx)(p,{children:(0,t.jsxs)(u,{className:"flex items-center justify-between",children:[(0,t.jsx)("span",{children:"Data Preview"}),(0,t.jsxs)(sp,{variant:"secondary",children:[i.rows.length," employees"]})]})}),(0,t.jsxs)(g,{children:[(0,t.jsx)("div",{className:"overflow-x-auto",children:(0,t.jsxs)(e_,{children:[(0,t.jsx)(eW,{children:(0,t.jsx)(eJ,{children:i.headers.map((e,s)=>(0,t.jsx)(eX,{className:"font-semibold",children:e},s))})}),(0,t.jsx)(eq,{children:i.rows.slice(0,10).map((e,s)=>(0,t.jsx)(eJ,{children:e.map((e,s)=>(0,t.jsx)(eK,{children:e},s))},s))})]})}),i.rows.length>10&&(0,t.jsxs)("p",{className:"text-sm text-gray-600 mt-4 text-center",children:["Showing first 10 rows of ",i.rows.length," total employees"]})]})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsxs)(x,{variant:"outline",onClick:()=>{n(null),c(null)},children:[(0,t.jsx)(ez.Z,{className:"mr-2 h-4 w-4"}),"Choose Different File"]}),(0,t.jsxs)(x,{onClick:()=>{e("?page=processing")},className:"bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700",disabled:i.errors.length>0,children:[(0,t.jsx)(A.Z,{className:"mr-2 h-4 w-4"}),"Proceed to Analysis"]})]})]})]}):(0,t.jsxs)(h,{className:"shadow-xl",children:[(0,t.jsx)(p,{children:(0,t.jsx)(u,{className:"text-2xl bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent",children:"\uD83D\uDCC4 Upload Census File for Preview"})}),(0,t.jsx)(g,{children:(0,t.jsxs)("div",{className:"border-2 border-dashed rounded-lg p-12 text-center transition-colors ".concat(s?"border-blue-500 bg-blue-50":"border-gray-300 hover:border-blue-400 hover:bg-gray-50"),onDragEnter:m,onDragLeave:m,onDragOver:m,onDrop:e=>{e.preventDefault(),e.stopPropagation(),a(!1);let s=e.dataTransfer.files;s&&s[0]&&j(s[0])},children:[(0,t.jsx)(S.Z,{className:"mx-auto h-16 w-16 text-gray-400 mb-4"}),(0,t.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-2",children:"Drop your census file here"}),(0,t.jsx)("p",{className:"text-gray-600 mb-6",children:"or click to browse and select a file"}),(0,t.jsx)("input",{type:"file",id:"file-upload",className:"hidden",accept:".csv,.xlsx,.xls",onChange:e=>{let s=e.target.files;s&&s[0]&&j(s[0])}}),(0,t.jsxs)(x,{onClick:()=>{var e;return null===(e=document.getElementById("file-upload"))||void 0===e?void 0:e.click()},className:"bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700",children:[(0,t.jsx)(eP.Z,{className:"mr-2 h-4 w-4"}),"Choose File"]}),(0,t.jsx)("p",{className:"text-sm text-gray-500 mt-4",children:"Supports CSV, Excel (.xlsx, .xls) files up to 10MB"})]})})]})})]})};let s6=[{title:"Home",to:"/",icon:(0,t.jsx)(ek.Z,{className:"h-4 w-4"}),page:(0,t.jsx)(et,{})},{title:"Dashboard",to:"/dashboard",icon:(0,t.jsx)(eD.Z,{className:"h-4 w-4"}),page:(0,t.jsx)(s3,{})},{title:"Employer Insight",to:"/employer-insight",icon:(0,t.jsx)(eS.Z,{className:"h-4 w-4"}),page:(0,t.jsx)(e3,{})},{title:"HR Insight",to:"/hr-insight",icon:(0,t.jsx)(eu.Z,{className:"h-4 w-4"}),page:(0,t.jsx)(sf,{})},{title:"Upload Census",to:"/upload-census",icon:(0,t.jsx)(S.Z,{className:"h-4 w-4"}),page:(0,t.jsx)(sN,{})},{title:"File Preview",to:"/file-preview",icon:(0,t.jsx)(eP.Z,{className:"h-4 w-4"}),page:(0,t.jsx)(s4,{})},{title:"HR Upload",to:"/hr-upload",icon:(0,t.jsx)(S.Z,{className:"h-4 w-4"}),page:(0,t.jsx)(sy,{})},{title:"Employer Invite",to:"/employer-invite",icon:(0,t.jsx)(eu.Z,{className:"h-4 w-4"}),page:(0,t.jsx)(sS,{})},{title:"Preview Report",to:"/preview-report",icon:(0,t.jsx)(eP.Z,{className:"h-4 w-4"}),page:(0,t.jsx)(sP,{})},{title:"Processing",to:"/processing",icon:(0,t.jsx)(K.Z,{className:"h-4 w-4"}),page:(0,t.jsx)(sA,{})},{title:"Generate Proposal",to:"/generate-proposal",icon:(0,t.jsx)(eZ.Z,{className:"h-4 w-4"}),page:(0,t.jsx)(sq,{})},{title:"Pricing",to:"/pricing",icon:(0,t.jsx)(eD.Z,{className:"h-4 w-4"}),page:(0,t.jsx)(sF,{})},{title:"Billing",to:"/billing",icon:(0,t.jsx)(eA.Z,{className:"h-4 w-4"}),page:(0,t.jsx)(sz,{})},{title:"Login",to:"/login-prompt",icon:(0,t.jsx)(eu.Z,{className:"h-4 w-4"}),page:(0,t.jsx)(sJ,{})},{title:"Not Found",to:"*",icon:(0,t.jsx)(eP.Z,{className:"h-4 w-4"}),page:(0,t.jsx)(sX,{})}];var s1=e=>{var s,a,l,n,i,c,d,o,m,h,p,u;let{variant:g="outline",size:j="sm"}=e,f=w(),[N,y]=(0,r.useState)(!1);s6.filter(e=>!["*","/"].includes(e.to));let v=e=>{let s=e.startsWith("/")?e.substring(1):e;f("?page=".concat(s)),y(!1)};return(0,t.jsxs)(Y,{open:N,onOpenChange:y,children:[(0,t.jsx)(_,{asChild:!0,children:(0,t.jsxs)(x,{variant:g,size:j,className:"flex items-center gap-2",children:[(0,t.jsx)(eC.Z,{className:"h-4 w-4"}),"Pages",(0,t.jsx)(b.Z,{className:"h-4 w-4"})]})}),(0,t.jsxs)(W,{className:"w-56 bg-white dark:bg-gray-800 border shadow-lg z-50",align:"end",sideOffset:5,children:[(0,t.jsxs)(q,{onClick:()=>v("/dashboard"),className:"flex items-center gap-2 hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer",children:[null===(s=s6.find(e=>"/dashboard"===e.to))||void 0===s?void 0:s.icon,"Dashboard"]}),(0,t.jsx)(J,{}),(0,t.jsxs)(q,{onClick:()=>v("/hr-insight"),className:"flex items-center gap-2 hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer",children:[null===(a=s6.find(e=>"/hr-insight"===e.to))||void 0===a?void 0:a.icon,"HR Insight"]}),(0,t.jsx)(J,{}),(0,t.jsxs)(q,{onClick:()=>v("/upload-census"),className:"flex items-center gap-2 hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer",children:[null===(l=s6.find(e=>"/upload-census"===e.to))||void 0===l?void 0:l.icon,"Upload Census"]}),(0,t.jsxs)(q,{onClick:()=>v("/file-preview"),className:"flex items-center gap-2 hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer",children:[null===(n=s6.find(e=>"/file-preview"===e.to))||void 0===n?void 0:n.icon,"File Preview"]}),(0,t.jsxs)(q,{onClick:()=>v("/hr-upload"),className:"flex items-center gap-2 hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer",children:[null===(i=s6.find(e=>"/hr-upload"===e.to))||void 0===i?void 0:i.icon,"HR Upload"]}),(0,t.jsxs)(q,{onClick:()=>v("/employer-invite"),className:"flex items-center gap-2 hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer",children:[null===(c=s6.find(e=>"/employer-invite"===e.to))||void 0===c?void 0:c.icon,"Invite Employer"]}),(0,t.jsx)(J,{}),(0,t.jsxs)(q,{onClick:()=>v("/preview-report"),className:"flex items-center gap-2 hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer",children:[null===(d=s6.find(e=>"/preview-report"===e.to))||void 0===d?void 0:d.icon,"Preview Report"]}),(0,t.jsxs)(q,{onClick:()=>v("/generate-proposal/1"),className:"flex items-center gap-2 hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer",children:[null===(o=s6.find(e=>"/generate-proposal"===e.to))||void 0===o?void 0:o.icon,"Generate Proposal"]}),(0,t.jsxs)(q,{onClick:()=>v("/processing"),className:"flex items-center gap-2 hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer",children:[null===(m=s6.find(e=>"/processing"===e.to))||void 0===m?void 0:m.icon,"Processing"]}),(0,t.jsx)(J,{}),(0,t.jsxs)(q,{onClick:()=>v("/login-prompt"),className:"flex items-center gap-2 hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer",children:[null===(h=s6.find(e=>"/login-prompt"===e.to))||void 0===h?void 0:h.icon,"Magic Link Sign In"]}),(0,t.jsxs)(q,{onClick:()=>v("/pricing"),className:"flex items-center gap-2 hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer",children:[null===(p=s6.find(e=>"/pricing"===e.to))||void 0===p?void 0:p.icon,"Pricing"]}),(0,t.jsxs)(q,{onClick:()=>v("/billing"),className:"flex items-center gap-2 hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer",children:[null===(u=s6.find(e=>"/billing"===e.to))||void 0===u?void 0:u.icon,"Billing & Subscriptions"]})]})]})};let s5=()=>{let[e,s]=(0,r.useState)([]);(0,r.useEffect)(()=>{let e=localStorage.getItem("viewedReports");if(e)try{s(JSON.parse(e))}catch(e){console.error("Error loading viewed reports:",e)}},[]);let a=e.length>=2,t=Math.max(0,2-e.length);return{maxReports:2,viewedReports:e.length,canViewReport:s=>!!e.includes(s)||e.length<2,trackReportView:a=>{if(!e.includes(a)){let t=[...e,a];s(t),localStorage.setItem("viewedReports",JSON.stringify(t))}},isAtLimit:a,reportsRemaining:t}};var s3=()=>{let e=w(),{toast:s}=(0,e1.pm)(),[a,l]=(0,r.useState)(""),[n,i]=(0,r.useState)("all"),[c,d]=(0,r.useState)("all"),[o,m]=(0,r.useState)("all"),j=(0,e5.C)(e=>e.user.userProfile),{canViewReport:b,reportsRemaining:f,isAtLimit:N,trackReportView:y}=s5(),v=[{id:"1",companyName:"TechCorp Solutions",employees:43,averageAge:36,dependents:1.3,planType:"PPO + HSA Combo",potentialSavings:"$127,500",riskScore:6.2,uploadDate:"2024-01-15",status:"analyzed"},{id:"2",companyName:"Green Manufacturing",employees:87,averageAge:42,dependents:1.8,planType:"Traditional PPO",potentialSavings:"$245,000",riskScore:7.1,uploadDate:"2024-01-10",status:"analyzed"},{id:"3",companyName:"StartupXYZ",employees:18,averageAge:29,dependents:.8,planType:"HSA Only",potentialSavings:"$32,400",riskScore:4.5,uploadDate:"2024-01-18",status:"processing"}].filter(e=>(!a||!!e.companyName.toLowerCase().includes(a.toLowerCase()))&&("all"===n||("1-50"!==n||!(e.employees>50))&&("51-100"!==n||!(e.employees<51)&&!(e.employees>100))&&("101+"!==n||!(e.employees<=100)))&&("all"===c||("low"!==c||!(e.riskScore>=5))&&("medium"!==c||!(e.riskScore<5)&&!(e.riskScore>7))&&("high"!==c||!(e.riskScore<=7)))&&("all"===o||("ppo-hsa"!==o||"PPO + HSA Combo"===e.planType)&&("traditional-ppo"!==o||"Traditional PPO"===e.planType)&&("other"!==o||"HSA Only"===e.planType||"Modern HSA Plus Plan"===e.planType))),C=e=>{let s="".concat(window.location.origin,"/shared-insight/").concat(e.toLowerCase().replace(/\s+/g,"-"));navigator.clipboard.writeText(s),console.log("Share link copied for ".concat(e))},k=(a,t)=>{if(console.log("Attempting to view report for ".concat(t," (ID: ").concat(a,")")),console.log("Can view report: ".concat(b(a))),console.log("Reports remaining: ".concat(f)),console.log("Is at limit: ".concat(N)),!b(a)){s({title:"Upgrade Required",description:"You've reached your free report limit (2 reports). Upgrade to Pro for unlimited access.",variant:"destructive"}),e("/pricing");return}y(a),e("?page=employer-insight/".concat(a))};return(0,t.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-slate-50 to-blue-50",children:[(0,t.jsx)("header",{className:"border-b bg-white/90 backdrop-blur-sm sticky top-0 z-50",children:(0,t.jsx)("div",{className:"container mx-auto px-3 sm:px-4 py-3",children:(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsx)("div",{className:"text-xl sm:text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent",children:"BenOsphere"}),(0,t.jsxs)("div",{className:"flex items-center space-x-2 sm:space-x-3",children:[(0,t.jsx)(s1,{}),(0,t.jsx)(ew,{context:"broker dashboard with multiple client insights",size:"sm",variant:"outline"}),(0,t.jsx)(ea,{className:"hidden sm:inline-flex"})]})]})})}),(0,t.jsxs)("main",{className:"container mx-auto px-3 sm:px-4 py-4 sm:py-6 max-w-7xl",children:[(0,t.jsxs)("div",{className:"mb-6",children:[(0,t.jsxs)("div",{className:"flex flex-col lg:flex-row lg:justify-between lg:items-start gap-4",children:[(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsx)("h1",{className:"text-2xl sm:text-3xl font-bold text-gray-900 mb-2",children:"\uD83D\uDCCA Broker Dashboard"}),(0,t.jsx)("p",{className:"text-gray-600 text-sm sm:text-base mb-4 lg:mb-0",children:"Manage and analyze your client census data"})]}),(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row gap-3",children:[(0,t.jsxs)(x,{size:"sm",onClick:()=>{j.isBroker?e("/upload-census"):j.isAdmin&&!j.isBroker?e("?page=hr-upload"):e("/upload-census")},className:"bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700",children:[(0,t.jsx)(D.Z,{className:"h-4 w-4 mr-2"}),"Upload New Census"]}),(0,t.jsxs)(x,{variant:"outline",size:"sm",onClick:()=>e("/employer-invite"),children:[(0,t.jsx)(R.Z,{className:"h-4 w-4 mr-2"}),"Invite Employer"]})]})]}),f<=1&&(0,t.jsx)(h,{className:"mt-4 bg-gradient-to-r from-amber-50 to-orange-50 border-amber-200",children:(0,t.jsx)(g,{className:"p-4",children:(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3",children:[(0,t.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,t.jsx)(F.Z,{className:"h-5 w-5 text-amber-600 mt-0.5 flex-shrink-0"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"font-medium text-amber-800",children:N?"Report limit reached!":"".concat(f," free report").concat(1===f?"":"s"," remaining")}),(0,t.jsx)("p",{className:"text-sm text-amber-700",children:N?"Upgrade to Pro for unlimited reports":"Upgrade to Pro for unlimited access"})]})]}),(0,t.jsx)(x,{onClick:()=>e("/pricing"),className:"bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white flex-shrink-0",size:"sm",children:"Upgrade Now"})]})})})]}),(0,t.jsx)(h,{className:"mb-6 shadow-lg border-0",children:(0,t.jsx)(g,{className:"p-4 sm:p-6",children:(0,t.jsxs)("div",{className:"flex flex-col gap-4",children:[(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(ep.Z,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4"}),(0,t.jsx)(el,{placeholder:"Search by Client Name",value:a,onChange:e=>l(e.target.value),className:"pl-10"})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-3 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium text-gray-700 mb-2 block",children:"Employee Count"}),(0,t.jsxs)(ei,{value:n,onValueChange:i,children:[(0,t.jsx)(ed,{children:(0,t.jsx)(ec,{placeholder:"All"})}),(0,t.jsxs)(ex,{className:"bg-white",children:[(0,t.jsx)(eh,{value:"all",children:"All"}),(0,t.jsx)(eh,{value:"1-50",children:"1–50"}),(0,t.jsx)(eh,{value:"51-100",children:"51–100"}),(0,t.jsx)(eh,{value:"101+",children:"101+"})]})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium text-gray-700 mb-2 block",children:"Risk Score"}),(0,t.jsxs)(ei,{value:c,onValueChange:d,children:[(0,t.jsx)(ed,{children:(0,t.jsx)(ec,{placeholder:"All"})}),(0,t.jsxs)(ex,{className:"bg-white",children:[(0,t.jsx)(eh,{value:"all",children:"All"}),(0,t.jsx)(eh,{value:"low",children:"Low (<5)"}),(0,t.jsx)(eh,{value:"medium",children:"Medium (5–7)"}),(0,t.jsx)(eh,{value:"high",children:"High (>7)"})]})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium text-gray-700 mb-2 block",children:"Suggested Plan"}),(0,t.jsxs)(ei,{value:o,onValueChange:m,children:[(0,t.jsx)(ed,{children:(0,t.jsx)(ec,{placeholder:"All"})}),(0,t.jsxs)(ex,{className:"bg-white",children:[(0,t.jsx)(eh,{value:"all",children:"All"}),(0,t.jsx)(eh,{value:"ppo-hsa",children:"PPO + HSA Combo"}),(0,t.jsx)(eh,{value:"traditional-ppo",children:"Traditional PPO"}),(0,t.jsx)(eh,{value:"other",children:"Other"})]})]})]})]})]})})}),(0,t.jsxs)("div",{className:"grid grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4 mb-6",children:[(0,t.jsx)(h,{className:"bg-gradient-to-br from-blue-100 to-blue-200 border-blue-300 hover:shadow-md transition-all duration-200",children:(0,t.jsxs)(g,{className:"p-3 sm:p-4 text-center",children:[(0,t.jsx)(eu.Z,{className:"h-6 w-6 sm:h-8 sm:w-8 mx-auto mb-1 sm:mb-2 text-blue-600"}),(0,t.jsx)("p",{className:"text-xs sm:text-sm text-blue-600 font-medium",children:"Total Clients"}),(0,t.jsx)("p",{className:"text-xl sm:text-2xl font-bold text-blue-900",children:v.length})]})}),(0,t.jsx)(h,{className:"bg-gradient-to-br from-emerald-100 to-teal-200 border-emerald-300 hover:shadow-md transition-all duration-200",children:(0,t.jsxs)(g,{className:"p-3 sm:p-4 text-center",children:[(0,t.jsx)(eu.Z,{className:"h-6 w-6 sm:h-8 sm:w-8 mx-auto mb-1 sm:mb-2 text-emerald-600"}),(0,t.jsx)("p",{className:"text-xs sm:text-sm text-emerald-600 font-medium",children:"Total Employees"}),(0,t.jsx)("p",{className:"text-xl sm:text-2xl font-bold text-emerald-900",children:v.reduce((e,s)=>e+s.employees,0)})]})}),(0,t.jsx)(h,{className:"bg-gradient-to-br from-orange-100 to-red-200 border-orange-300 hover:shadow-md transition-all duration-200",children:(0,t.jsxs)(g,{className:"p-3 sm:p-4 text-center",children:[(0,t.jsx)(eg.Z,{className:"h-6 w-6 sm:h-8 sm:w-8 mx-auto mb-1 sm:mb-2 text-orange-600"}),(0,t.jsx)("p",{className:"text-xs sm:text-sm text-orange-600 font-medium",children:"Total Potential Savings"}),(0,t.jsx)("p",{className:"text-lg sm:text-2xl font-bold text-orange-900",children:"$404,900"})]})}),(0,t.jsx)(h,{className:"bg-gradient-to-br from-purple-100 to-pink-200 border-purple-300 hover:shadow-md transition-all duration-200",children:(0,t.jsxs)(g,{className:"p-3 sm:p-4 text-center",children:[(0,t.jsx)(ej.Z,{className:"h-6 w-6 sm:h-8 sm:w-8 mx-auto mb-1 sm:mb-2 text-purple-600"}),(0,t.jsx)("p",{className:"text-xs sm:text-sm text-purple-600 font-medium",children:"Avg Risk Score"}),(0,t.jsx)("p",{className:"text-xl sm:text-2xl font-bold text-purple-900",children:"5.9/10"})]})})]}),(0,t.jsxs)(h,{className:"shadow-lg border-0",children:[(0,t.jsx)(p,{className:"pb-3 sm:pb-4",children:(0,t.jsx)(u,{className:"text-lg sm:text-xl",children:"Client Census Insights"})}),(0,t.jsx)(g,{className:"p-3 sm:p-6",children:(0,t.jsx)("div",{className:"space-y-3 sm:space-y-4",children:v.map(s=>{let a=b(s.id);return(0,t.jsxs)(h,{className:"border-l-4 border-l-blue-500 transition-all duration-200 cursor-pointer ".concat(a?"hover:shadow-md hover:-translate-y-0.5":"opacity-60 hover:opacity-80 cursor-not-allowed"," ").concat(a?"":"relative"),onClick:()=>k(s.id,s.companyName),children:[!a&&(0,t.jsx)("div",{className:"absolute inset-0 bg-gray-200/50 backdrop-blur-[1px] rounded-lg flex items-center justify-center z-10",children:(0,t.jsxs)("div",{className:"bg-white p-3 rounded-lg shadow-lg flex items-center space-x-2 border border-amber-200",children:[(0,t.jsx)(eb.Z,{className:"h-4 w-4 text-amber-600"}),(0,t.jsx)("span",{className:"text-sm font-medium text-amber-800",children:"Upgrade to view"})]})}),(0,t.jsxs)(g,{className:"p-4 sm:p-5",children:[(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row justify-between items-start mb-3 sm:mb-4 gap-3",children:[(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsx)("h3",{className:"text-lg sm:text-xl font-semibold text-gray-900 mb-1",children:s.companyName}),(0,t.jsxs)("p",{className:"text-xs sm:text-sm text-gray-500",children:["Uploaded: ",new Date(s.uploadDate).toLocaleDateString()]})]}),(0,t.jsx)("div",{className:"flex space-x-2 w-full sm:w-auto",children:(0,t.jsxs)(x,{size:"sm",variant:"outline",onClick:t=>{t.stopPropagation(),a?C(s.companyName):e("/pricing")},className:"flex-1 sm:flex-none",disabled:!a,children:[(0,t.jsx)(ef.Z,{className:"h-3 w-3 sm:h-4 sm:w-4 sm:mr-1"}),(0,t.jsx)("span",{className:"hidden sm:inline",children:"Share"})]})})]}),(0,t.jsxs)("div",{className:"grid grid-cols-2 lg:grid-cols-4 gap-2 sm:gap-3 mb-3 sm:mb-4",children:[(0,t.jsxs)("div",{className:"text-center p-2 sm:p-3 bg-gradient-to-br from-blue-50 to-blue-100 rounded-lg hover:scale-105 transition-transform duration-200",children:[(0,t.jsx)("p",{className:"text-xs sm:text-sm text-blue-600 font-medium",children:"Employees"}),(0,t.jsx)("p",{className:"text-sm sm:text-lg font-bold text-blue-900",children:s.employees})]}),(0,t.jsxs)("div",{className:"text-center p-2 sm:p-3 bg-gradient-to-br from-emerald-50 to-emerald-100 rounded-lg hover:scale-105 transition-transform duration-200",children:[(0,t.jsx)("p",{className:"text-xs sm:text-sm text-emerald-600 font-medium",children:"Avg Age"}),(0,t.jsx)("p",{className:"text-sm sm:text-lg font-bold text-emerald-900",children:s.averageAge})]}),(0,t.jsxs)("div",{className:"text-center p-2 sm:p-3 bg-gradient-to-br from-orange-50 to-orange-100 rounded-lg hover:scale-105 transition-transform duration-200",children:[(0,t.jsx)("p",{className:"text-xs sm:text-sm text-orange-600 font-medium",children:"Savings"}),(0,t.jsx)("p",{className:"text-sm sm:text-lg font-bold text-orange-900",children:s.potentialSavings})]}),(0,t.jsxs)("div",{className:"text-center p-2 sm:p-3 bg-gradient-to-br from-purple-50 to-purple-100 rounded-lg hover:scale-105 transition-transform duration-200",children:[(0,t.jsx)("p",{className:"text-xs sm:text-sm text-purple-600 font-medium",children:"Risk Score"}),(0,t.jsxs)("p",{className:"text-sm sm:text-lg font-bold text-purple-900",children:[s.riskScore,"/10"]})]})]}),(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row justify-between items-start sm:items-center gap-2 sm:gap-0",children:[(0,t.jsxs)("div",{className:"text-sm",children:[(0,t.jsx)("span",{className:"text-gray-600",children:"Suggested Plan: "}),(0,t.jsx)("span",{className:"font-medium text-gray-900",children:s.planType})]}),(0,t.jsx)("span",{className:"px-2 py-1 rounded-full text-xs font-medium ".concat("analyzed"===s.status?"bg-green-100 text-green-800":"bg-yellow-100 text-yellow-800"),children:"analyzed"===s.status?"✅ Analyzed":"⏳ Processing"})]})]})]},s.id)})})})]}),(0,t.jsx)(h,{className:"mt-6 bg-gradient-to-r from-purple-100 to-blue-100 border-0 shadow-lg",children:(0,t.jsxs)(g,{className:"p-4 sm:p-6 text-center",children:[(0,t.jsx)("h3",{className:"text-lg sm:text-xl font-bold text-gray-900 mb-2",children:"\uD83D\uDE80 Grow Your Network"}),(0,t.jsx)("p",{className:"text-gray-700 mb-4 text-sm sm:text-base",children:"Share BenOsphere with other brokers and get rewards for every signup"}),(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row justify-center space-y-2 sm:space-y-0 sm:space-x-4",children:[(0,t.jsxs)(x,{className:"bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white transition-all duration-200 hover:scale-105",children:[(0,t.jsx)(ef.Z,{className:"h-4 w-4 mr-2"}),"Refer a Broker"]}),(0,t.jsx)(x,{variant:"outline",className:"hover:bg-white/50",children:"View Referral Rewards"})]})]})})]})]})};function s8(){let e=(0,l.useSearchParams)(),s=e.get("page")||"home";e.get("id");let[a,r]=s.includes("/")?s.split("/"):[s,null];return(0,t.jsx)(i,{children:(0,t.jsx)("div",{children:(()=>{switch(a){case"home":case"":return(0,t.jsx)(et,{});case"dashboard":return(0,t.jsx)(s3,{});case"employer-insight":return(0,t.jsx)(e3,{});case"hr-insight":return(0,t.jsx)(sf,{});case"upload-census":return(0,t.jsx)(sN,{});case"file-preview":return(0,t.jsx)(s4,{});case"hr-upload":return(0,t.jsx)(sy,{});case"employer-invite":return(0,t.jsx)(sS,{});case"preview-report":return(0,t.jsx)(sP,{});case"processing":return(0,t.jsx)(sA,{});case"generate-proposal":return(0,t.jsx)(sq,{});case"pricing":return(0,t.jsx)(sF,{});case"billing":return(0,t.jsx)(sz,{});case"login-prompt":return(0,t.jsx)(sJ,{});default:return(0,t.jsx)(sX,{})}})()})})}}}]);