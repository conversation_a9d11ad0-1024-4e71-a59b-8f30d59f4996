"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6961],{96729:function(r,t,e){var o=e(94630),a=e(57437);t.Z=(0,o.Z)((0,a.jsx)("path",{d:"M19.35 10.04C18.67 6.59 15.64 4 12 4 9.11 4 6.6 5.64 5.35 8.04 2.34 8.36 0 10.91 0 14c0 3.31 2.69 6 6 6h13c2.76 0 5-2.24 5-5 0-2.64-2.05-4.78-4.65-4.96M14 13v4h-4v-4H7l5-5 5 5z"}),"CloudUpload")},46837:function(r,t,e){var o=e(94630),a=e(57437);t.Z=(0,o.Z)((0,a.jsx)("path",{d:"M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z"}),"Home")},61910:function(r,t,e){var o=e(94630),a=e(57437);t.Z=(0,o.Z)((0,a.jsx)("path",{d:"M3 18h18v-2H3zm0-5h18v-2H3zm0-7v2h18V6z"}),"Menu")},71495:function(r,t,e){e.d(t,{Z:function(){return b}});var o=e(2265),a=e(61994),l=e(20801),n=e(16210),i=e(21086),s=e(37053),p=e(85657),c=e(3858),u=e(53410),d=e(94143),v=e(50738);function f(r){return(0,v.ZP)("MuiAppBar",r)}(0,d.Z)("MuiAppBar",["root","positionFixed","positionAbsolute","positionSticky","positionStatic","positionRelative","colorDefault","colorPrimary","colorSecondary","colorInherit","colorTransparent","colorError","colorInfo","colorSuccess","colorWarning"]);var g=e(57437);let y=r=>{let{color:t,position:e,classes:o}=r,a={root:["root","color".concat((0,p.Z)(t)),"position".concat((0,p.Z)(e))]};return(0,l.Z)(a,f,o)},m=(r,t)=>r?"".concat(null==r?void 0:r.replace(")",""),", ").concat(t,")"):t,h=(0,n.default)(u.Z,{name:"MuiAppBar",slot:"Root",overridesResolver:(r,t)=>{let{ownerState:e}=r;return[t.root,t["position".concat((0,p.Z)(e.position))],t["color".concat((0,p.Z)(e.color))]]}})((0,i.Z)(r=>{let{theme:t}=r;return{display:"flex",flexDirection:"column",width:"100%",boxSizing:"border-box",flexShrink:0,variants:[{props:{position:"fixed"},style:{position:"fixed",zIndex:(t.vars||t).zIndex.appBar,top:0,left:"auto",right:0,"@media print":{position:"absolute"}}},{props:{position:"absolute"},style:{position:"absolute",zIndex:(t.vars||t).zIndex.appBar,top:0,left:"auto",right:0}},{props:{position:"sticky"},style:{position:"sticky",zIndex:(t.vars||t).zIndex.appBar,top:0,left:"auto",right:0}},{props:{position:"static"},style:{position:"static"}},{props:{position:"relative"},style:{position:"relative"}},{props:{color:"inherit"},style:{"--AppBar-color":"inherit"}},{props:{color:"default"},style:{"--AppBar-background":t.vars?t.vars.palette.AppBar.defaultBg:t.palette.grey[100],"--AppBar-color":t.vars?t.vars.palette.text.primary:t.palette.getContrastText(t.palette.grey[100]),...t.applyStyles("dark",{"--AppBar-background":t.vars?t.vars.palette.AppBar.defaultBg:t.palette.grey[900],"--AppBar-color":t.vars?t.vars.palette.text.primary:t.palette.getContrastText(t.palette.grey[900])})}},...Object.entries(t.palette).filter((0,c.Z)(["contrastText"])).map(r=>{var e,o;let[a]=r;return{props:{color:a},style:{"--AppBar-background":(null!==(e=t.vars)&&void 0!==e?e:t).palette[a].main,"--AppBar-color":(null!==(o=t.vars)&&void 0!==o?o:t).palette[a].contrastText}}}),{props:r=>!0===r.enableColorOnDark&&!["inherit","transparent"].includes(r.color),style:{backgroundColor:"var(--AppBar-background)",color:"var(--AppBar-color)"}},{props:r=>!1===r.enableColorOnDark&&!["inherit","transparent"].includes(r.color),style:{backgroundColor:"var(--AppBar-background)",color:"var(--AppBar-color)",...t.applyStyles("dark",{backgroundColor:t.vars?m(t.vars.palette.AppBar.darkBg,"var(--AppBar-background)"):null,color:t.vars?m(t.vars.palette.AppBar.darkColor,"var(--AppBar-color)"):null})}},{props:{color:"transparent"},style:{"--AppBar-background":"transparent","--AppBar-color":"inherit",backgroundColor:"var(--AppBar-background)",color:"var(--AppBar-color)",...t.applyStyles("dark",{backgroundImage:"none"})}}]}}));var b=o.forwardRef(function(r,t){let e=(0,s.i)({props:r,name:"MuiAppBar"}),{className:o,color:l="primary",enableColorOnDark:n=!1,position:i="fixed",...p}=e,c={...e,color:l,position:i,enableColorOnDark:n},u=y(c);return(0,g.jsx)(h,{square:!0,component:"header",ownerState:c,elevation:4,className:(0,a.Z)(u.root,o,"fixed"===i&&"mui-fixed"),ref:t,...p})})},67116:function(r,t,e){e.d(t,{Z:function(){return k}});var o=e(2265),a=e(61994),l=e(20801),n=e(16210),i=e(21086),s=e(37053),p=e(94630),c=e(57437),u=(0,p.Z)((0,c.jsx)("path",{d:"M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"}),"Person"),d=e(94143),v=e(50738);function f(r){return(0,v.ZP)("MuiAvatar",r)}(0,d.Z)("MuiAvatar",["root","colorDefault","circular","rounded","square","img","fallback"]);var g=e(79114);let y=r=>{let{classes:t,variant:e,colorDefault:o}=r;return(0,l.Z)({root:["root",e,o&&"colorDefault"],img:["img"],fallback:["fallback"]},f,t)},m=(0,n.default)("div",{name:"MuiAvatar",slot:"Root",overridesResolver:(r,t)=>{let{ownerState:e}=r;return[t.root,t[e.variant],e.colorDefault&&t.colorDefault]}})((0,i.Z)(r=>{let{theme:t}=r;return{position:"relative",display:"flex",alignItems:"center",justifyContent:"center",flexShrink:0,width:40,height:40,fontFamily:t.typography.fontFamily,fontSize:t.typography.pxToRem(20),lineHeight:1,borderRadius:"50%",overflow:"hidden",userSelect:"none",variants:[{props:{variant:"rounded"},style:{borderRadius:(t.vars||t).shape.borderRadius}},{props:{variant:"square"},style:{borderRadius:0}},{props:{colorDefault:!0},style:{color:(t.vars||t).palette.background.default,...t.vars?{backgroundColor:t.vars.palette.Avatar.defaultBg}:{backgroundColor:t.palette.grey[400],...t.applyStyles("dark",{backgroundColor:t.palette.grey[600]})}}}]}})),h=(0,n.default)("img",{name:"MuiAvatar",slot:"Img",overridesResolver:(r,t)=>t.img})({width:"100%",height:"100%",textAlign:"center",objectFit:"cover",color:"transparent",textIndent:1e4}),b=(0,n.default)(u,{name:"MuiAvatar",slot:"Fallback",overridesResolver:(r,t)=>t.fallback})({width:"75%",height:"75%"});var k=o.forwardRef(function(r,t){let e=(0,s.i)({props:r,name:"MuiAvatar"}),{alt:l,children:n,className:i,component:p="div",slots:u={},slotProps:d={},imgProps:v,sizes:f,src:k,srcSet:x,variant:A="circular",...B}=e,Z=null,M={...e,component:p,variant:A},R=function(r){let{crossOrigin:t,referrerPolicy:e,src:a,srcSet:l}=r,[n,i]=o.useState(!1);return o.useEffect(()=>{if(!a&&!l)return;i(!1);let r=!0,o=new Image;return o.onload=()=>{r&&i("loaded")},o.onerror=()=>{r&&i("error")},o.crossOrigin=t,o.referrerPolicy=e,o.src=a,l&&(o.srcset=l),()=>{r=!1}},[t,e,a,l]),n}({...v,..."function"==typeof d.img?d.img(M):d.img,src:k,srcSet:x}),S=k||x,w=S&&"error"!==R;M.colorDefault=!w,delete M.ownerState;let C=y(M),[z,I]=(0,g.Z)("img",{className:C.img,elementType:h,externalForwardedProps:{slots:u,slotProps:{img:{...v,...d.img}}},additionalProps:{alt:l,src:k,srcSet:x,sizes:f},ownerState:M});return Z=w?(0,c.jsx)(z,{...I}):n||0===n?n:S&&l?l[0]:(0,c.jsx)(b,{ownerState:M,className:C.fallback}),(0,c.jsx)(m,{as:p,className:(0,a.Z)(C.root,i),ref:t,...B,ownerState:M,children:Z})})},71004:function(r,t,e){e.d(t,{Z:function(){return g}});var o=e(2265),a=e(61994),l=e(20801),n=e(16210),i=e(21086),s=e(37053),p=e(94143),c=e(50738);function u(r){return(0,c.ZP)("MuiToolbar",r)}(0,p.Z)("MuiToolbar",["root","gutters","regular","dense"]);var d=e(57437);let v=r=>{let{classes:t,disableGutters:e,variant:o}=r;return(0,l.Z)({root:["root",!e&&"gutters",o]},u,t)},f=(0,n.default)("div",{name:"MuiToolbar",slot:"Root",overridesResolver:(r,t)=>{let{ownerState:e}=r;return[t.root,!e.disableGutters&&t.gutters,t[e.variant]]}})((0,i.Z)(r=>{let{theme:t}=r;return{position:"relative",display:"flex",alignItems:"center",variants:[{props:r=>{let{ownerState:t}=r;return!t.disableGutters},style:{paddingLeft:t.spacing(2),paddingRight:t.spacing(2),[t.breakpoints.up("sm")]:{paddingLeft:t.spacing(3),paddingRight:t.spacing(3)}}},{props:{variant:"dense"},style:{minHeight:48}},{props:{variant:"regular"},style:t.mixins.toolbar}]}}));var g=o.forwardRef(function(r,t){let e=(0,s.i)({props:r,name:"MuiToolbar"}),{className:o,component:l="div",disableGutters:n=!1,variant:i="regular",...p}=e,c={...e,component:l,disableGutters:n,variant:i},u=v(c);return(0,d.jsx)(f,{as:l,className:(0,a.Z)(u.root,o),ref:t,ownerState:c,...p})})}}]);