"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7534],{77534:function(n,a,e){e.d(a,{Ig:function(){return s},N8:function(){return o},lY:function(){return t},selfOnboard:function(){return u}});var r=e(40256);async function t(n){return(await (0,r.j0)("/auth/parse-params",{link:n})).data}async function u(n){return(await (0,r.j0)("/user/self-onboard",{userEmail:n})).data.data}async function o(n,a){return(await (0,r.j0)("/teams/user/self-onboard",{userEmail:n,tenantId:a})).data}async function s(n,a){let e=await (0,r.j0)("/employee/onboard",{companyId:n,userId:a});return console.log("Response from onboardEmployee:",e),e.data}}}]);