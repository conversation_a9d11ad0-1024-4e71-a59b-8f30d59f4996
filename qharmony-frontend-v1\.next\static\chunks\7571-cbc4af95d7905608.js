"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7571],{67571:function(e,a,t){t.d(a,{Z:function(){return x}});var o=t(2265),c=t(61994),l=t(20801),r=t(65208),n=t(94630),i=t(57437),s=(0,n.Z)((0,i.jsx)("path",{d:"M12 2C6.47 2 2 6.47 2 12s4.47 10 10 10 10-4.47 10-10S17.53 2 12 2zm5 13.59L15.59 17 12 13.41 8.41 17 7 15.59 10.59 12 7 8.41 8.41 7 12 10.59 15.59 7 17 8.41 13.41 12 17 15.59z"}),"Cancel"),p=t(60118),d=t(85657),v=t(52559),m=t(16210),u=t(21086),g=t(3858),b=t(37053),y=t(94143),h=t(50738);function C(e){return(0,h.ZP)("MuiChip",e)}let f=(0,y.Z)("MuiChip",["root","sizeSmall","sizeMedium","colorDefault","colorError","colorInfo","colorPrimary","colorSecondary","colorSuccess","colorWarning","disabled","clickable","clickableColorPrimary","clickableColorSecondary","deletable","deletableColorPrimary","deletableColorSecondary","outlined","filled","outlinedPrimary","outlinedSecondary","filledPrimary","filledSecondary","avatar","avatarSmall","avatarMedium","avatarColorPrimary","avatarColorSecondary","icon","iconSmall","iconMedium","iconColorPrimary","iconColorSecondary","label","labelSmall","labelMedium","deleteIcon","deleteIconSmall","deleteIconMedium","deleteIconColorPrimary","deleteIconColorSecondary","deleteIconOutlinedColorPrimary","deleteIconOutlinedColorSecondary","deleteIconFilledColorPrimary","deleteIconFilledColorSecondary","focusVisible"]),Z=e=>{let{classes:a,disabled:t,size:o,color:c,iconColor:r,onDelete:n,clickable:i,variant:s}=e,p={root:["root",s,t&&"disabled","size".concat((0,d.Z)(o)),"color".concat((0,d.Z)(c)),i&&"clickable",i&&"clickableColor".concat((0,d.Z)(c)),n&&"deletable",n&&"deletableColor".concat((0,d.Z)(c)),"".concat(s).concat((0,d.Z)(c))],label:["label","label".concat((0,d.Z)(o))],avatar:["avatar","avatar".concat((0,d.Z)(o)),"avatarColor".concat((0,d.Z)(c))],icon:["icon","icon".concat((0,d.Z)(o)),"iconColor".concat((0,d.Z)(r))],deleteIcon:["deleteIcon","deleteIcon".concat((0,d.Z)(o)),"deleteIconColor".concat((0,d.Z)(c)),"deleteIcon".concat((0,d.Z)(s),"Color").concat((0,d.Z)(c))]};return(0,l.Z)(p,C,a)},k=(0,m.default)("div",{name:"MuiChip",slot:"Root",overridesResolver:(e,a)=>{let{ownerState:t}=e,{color:o,iconColor:c,clickable:l,onDelete:r,size:n,variant:i}=t;return[{["& .".concat(f.avatar)]:a.avatar},{["& .".concat(f.avatar)]:a["avatar".concat((0,d.Z)(n))]},{["& .".concat(f.avatar)]:a["avatarColor".concat((0,d.Z)(o))]},{["& .".concat(f.icon)]:a.icon},{["& .".concat(f.icon)]:a["icon".concat((0,d.Z)(n))]},{["& .".concat(f.icon)]:a["iconColor".concat((0,d.Z)(c))]},{["& .".concat(f.deleteIcon)]:a.deleteIcon},{["& .".concat(f.deleteIcon)]:a["deleteIcon".concat((0,d.Z)(n))]},{["& .".concat(f.deleteIcon)]:a["deleteIconColor".concat((0,d.Z)(o))]},{["& .".concat(f.deleteIcon)]:a["deleteIcon".concat((0,d.Z)(i),"Color").concat((0,d.Z)(o))]},a.root,a["size".concat((0,d.Z)(n))],a["color".concat((0,d.Z)(o))],l&&a.clickable,l&&"default"!==o&&a["clickableColor".concat((0,d.Z)(o),")")],r&&a.deletable,r&&"default"!==o&&a["deletableColor".concat((0,d.Z)(o))],a[i],a["".concat(i).concat((0,d.Z)(o))]]}})((0,u.Z)(e=>{let{theme:a}=e,t="light"===a.palette.mode?a.palette.grey[700]:a.palette.grey[300];return{maxWidth:"100%",fontFamily:a.typography.fontFamily,fontSize:a.typography.pxToRem(13),display:"inline-flex",alignItems:"center",justifyContent:"center",height:32,color:(a.vars||a).palette.text.primary,backgroundColor:(a.vars||a).palette.action.selected,borderRadius:16,whiteSpace:"nowrap",transition:a.transitions.create(["background-color","box-shadow"]),cursor:"unset",outline:0,textDecoration:"none",border:0,padding:0,verticalAlign:"middle",boxSizing:"border-box",["&.".concat(f.disabled)]:{opacity:(a.vars||a).palette.action.disabledOpacity,pointerEvents:"none"},["& .".concat(f.avatar)]:{marginLeft:5,marginRight:-6,width:24,height:24,color:a.vars?a.vars.palette.Chip.defaultAvatarColor:t,fontSize:a.typography.pxToRem(12)},["& .".concat(f.avatarColorPrimary)]:{color:(a.vars||a).palette.primary.contrastText,backgroundColor:(a.vars||a).palette.primary.dark},["& .".concat(f.avatarColorSecondary)]:{color:(a.vars||a).palette.secondary.contrastText,backgroundColor:(a.vars||a).palette.secondary.dark},["& .".concat(f.avatarSmall)]:{marginLeft:4,marginRight:-4,width:18,height:18,fontSize:a.typography.pxToRem(10)},["& .".concat(f.icon)]:{marginLeft:5,marginRight:-6},["& .".concat(f.deleteIcon)]:{WebkitTapHighlightColor:"transparent",color:a.vars?"rgba(".concat(a.vars.palette.text.primaryChannel," / 0.26)"):(0,r.Fq)(a.palette.text.primary,.26),fontSize:22,cursor:"pointer",margin:"0 5px 0 -6px","&:hover":{color:a.vars?"rgba(".concat(a.vars.palette.text.primaryChannel," / 0.4)"):(0,r.Fq)(a.palette.text.primary,.4)}},variants:[{props:{size:"small"},style:{height:24,["& .".concat(f.icon)]:{fontSize:18,marginLeft:4,marginRight:-4},["& .".concat(f.deleteIcon)]:{fontSize:16,marginRight:4,marginLeft:-4}}},...Object.entries(a.palette).filter((0,g.Z)(["contrastText"])).map(e=>{let[t]=e;return{props:{color:t},style:{backgroundColor:(a.vars||a).palette[t].main,color:(a.vars||a).palette[t].contrastText,["& .".concat(f.deleteIcon)]:{color:a.vars?"rgba(".concat(a.vars.palette[t].contrastTextChannel," / 0.7)"):(0,r.Fq)(a.palette[t].contrastText,.7),"&:hover, &:active":{color:(a.vars||a).palette[t].contrastText}}}}}),{props:e=>e.iconColor===e.color,style:{["& .".concat(f.icon)]:{color:a.vars?a.vars.palette.Chip.defaultIconColor:t}}},{props:e=>e.iconColor===e.color&&"default"!==e.color,style:{["& .".concat(f.icon)]:{color:"inherit"}}},{props:{onDelete:!0},style:{["&.".concat(f.focusVisible)]:{backgroundColor:a.vars?"rgba(".concat(a.vars.palette.action.selectedChannel," / calc(").concat(a.vars.palette.action.selectedOpacity," + ").concat(a.vars.palette.action.focusOpacity,"))"):(0,r.Fq)(a.palette.action.selected,a.palette.action.selectedOpacity+a.palette.action.focusOpacity)}}},...Object.entries(a.palette).filter((0,g.Z)(["dark"])).map(e=>{let[t]=e;return{props:{color:t,onDelete:!0},style:{["&.".concat(f.focusVisible)]:{background:(a.vars||a).palette[t].dark}}}}),{props:{clickable:!0},style:{userSelect:"none",WebkitTapHighlightColor:"transparent",cursor:"pointer","&:hover":{backgroundColor:a.vars?"rgba(".concat(a.vars.palette.action.selectedChannel," / calc(").concat(a.vars.palette.action.selectedOpacity," + ").concat(a.vars.palette.action.hoverOpacity,"))"):(0,r.Fq)(a.palette.action.selected,a.palette.action.selectedOpacity+a.palette.action.hoverOpacity)},["&.".concat(f.focusVisible)]:{backgroundColor:a.vars?"rgba(".concat(a.vars.palette.action.selectedChannel," / calc(").concat(a.vars.palette.action.selectedOpacity," + ").concat(a.vars.palette.action.focusOpacity,"))"):(0,r.Fq)(a.palette.action.selected,a.palette.action.selectedOpacity+a.palette.action.focusOpacity)},"&:active":{boxShadow:(a.vars||a).shadows[1]}}},...Object.entries(a.palette).filter((0,g.Z)(["dark"])).map(e=>{let[t]=e;return{props:{color:t,clickable:!0},style:{["&:hover, &.".concat(f.focusVisible)]:{backgroundColor:(a.vars||a).palette[t].dark}}}}),{props:{variant:"outlined"},style:{backgroundColor:"transparent",border:a.vars?"1px solid ".concat(a.vars.palette.Chip.defaultBorder):"1px solid ".concat("light"===a.palette.mode?a.palette.grey[400]:a.palette.grey[700]),["&.".concat(f.clickable,":hover")]:{backgroundColor:(a.vars||a).palette.action.hover},["&.".concat(f.focusVisible)]:{backgroundColor:(a.vars||a).palette.action.focus},["& .".concat(f.avatar)]:{marginLeft:4},["& .".concat(f.avatarSmall)]:{marginLeft:2},["& .".concat(f.icon)]:{marginLeft:4},["& .".concat(f.iconSmall)]:{marginLeft:2},["& .".concat(f.deleteIcon)]:{marginRight:5},["& .".concat(f.deleteIconSmall)]:{marginRight:3}}},...Object.entries(a.palette).filter((0,g.Z)()).map(e=>{let[t]=e;return{props:{variant:"outlined",color:t},style:{color:(a.vars||a).palette[t].main,border:"1px solid ".concat(a.vars?"rgba(".concat(a.vars.palette[t].mainChannel," / 0.7)"):(0,r.Fq)(a.palette[t].main,.7)),["&.".concat(f.clickable,":hover")]:{backgroundColor:a.vars?"rgba(".concat(a.vars.palette[t].mainChannel," / ").concat(a.vars.palette.action.hoverOpacity,")"):(0,r.Fq)(a.palette[t].main,a.palette.action.hoverOpacity)},["&.".concat(f.focusVisible)]:{backgroundColor:a.vars?"rgba(".concat(a.vars.palette[t].mainChannel," / ").concat(a.vars.palette.action.focusOpacity,")"):(0,r.Fq)(a.palette[t].main,a.palette.action.focusOpacity)},["& .".concat(f.deleteIcon)]:{color:a.vars?"rgba(".concat(a.vars.palette[t].mainChannel," / 0.7)"):(0,r.Fq)(a.palette[t].main,.7),"&:hover, &:active":{color:(a.vars||a).palette[t].main}}}}})]}})),I=(0,m.default)("span",{name:"MuiChip",slot:"Label",overridesResolver:(e,a)=>{let{ownerState:t}=e,{size:o}=t;return[a.label,a["label".concat((0,d.Z)(o))]]}})({overflow:"hidden",textOverflow:"ellipsis",paddingLeft:12,paddingRight:12,whiteSpace:"nowrap",variants:[{props:{variant:"outlined"},style:{paddingLeft:11,paddingRight:11}},{props:{size:"small"},style:{paddingLeft:8,paddingRight:8}},{props:{size:"small",variant:"outlined"},style:{paddingLeft:7,paddingRight:7}}]});function S(e){return"Backspace"===e.key||"Delete"===e.key}var x=o.forwardRef(function(e,a){let t=(0,b.i)({props:e,name:"MuiChip"}),{avatar:l,className:r,clickable:n,color:d="default",component:m,deleteIcon:u,disabled:g=!1,icon:y,label:h,onClick:C,onDelete:f,onKeyDown:x,onKeyUp:O,size:R="medium",variant:w="filled",tabIndex:z,skipFocusWhenDisabled:L=!1,...F}=t,T=o.useRef(null),P=(0,p.Z)(T,a),V=e=>{e.stopPropagation(),f&&f(e)},N=!1!==n&&!!C||n,E=N||f?v.Z:m||"div",M={...t,component:E,disabled:g,size:R,color:d,iconColor:o.isValidElement(y)&&y.props.color||d,onDelete:!!f,clickable:N,variant:w},q=Z(M),j=E===v.Z?{component:m||"div",focusVisibleClassName:q.focusVisible,...f&&{disableRipple:!0}}:{},D=null;f&&(D=u&&o.isValidElement(u)?o.cloneElement(u,{className:(0,c.Z)(u.props.className,q.deleteIcon),onClick:V}):(0,i.jsx)(s,{className:(0,c.Z)(q.deleteIcon),onClick:V}));let W=null;l&&o.isValidElement(l)&&(W=o.cloneElement(l,{className:(0,c.Z)(q.avatar,l.props.className)}));let _=null;return y&&o.isValidElement(y)&&(_=o.cloneElement(y,{className:(0,c.Z)(q.icon,y.props.className)})),(0,i.jsxs)(k,{as:E,className:(0,c.Z)(q.root,r),disabled:!!N&&!!g||void 0,onClick:C,onKeyDown:e=>{e.currentTarget===e.target&&S(e)&&e.preventDefault(),x&&x(e)},onKeyUp:e=>{e.currentTarget===e.target&&f&&S(e)&&f(e),O&&O(e)},ref:P,tabIndex:L&&g?-1:z,ownerState:M,...j,...F,children:[W||_,(0,i.jsx)(I,{className:(0,c.Z)(q.label),ownerState:M,children:h}),D]})})}}]);