(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7804],{24601:function(){},18975:function(e,t,i){"use strict";var a=i(40257);i(24601);var s=i(2265),r=s&&"object"==typeof s&&"default"in s?s:{default:s},n=void 0!==a&&a.env&&!0,l=function(e){return"[object String]"===Object.prototype.toString.call(e)},o=function(){function e(e){var t=void 0===e?{}:e,i=t.name,a=void 0===i?"stylesheet":i,s=t.optimizeForSpeed,r=void 0===s?n:s;c(l(a),"`name` must be a string"),this._name=a,this._deletedRulePlaceholder="#"+a+"-deleted-rule____{}",c("boolean"==typeof r,"`optimizeForSpeed` must be a boolean"),this._optimizeForSpeed=r,this._serverSheet=void 0,this._tags=[],this._injected=!1,this._rulesCount=0;var o="undefined"!=typeof window&&document.querySelector('meta[property="csp-nonce"]');this._nonce=o?o.getAttribute("content"):null}var t=e.prototype;return t.setOptimizeForSpeed=function(e){c("boolean"==typeof e,"`setOptimizeForSpeed` accepts a boolean"),c(0===this._rulesCount,"optimizeForSpeed cannot be when rules have already been inserted"),this.flush(),this._optimizeForSpeed=e,this.inject()},t.isOptimizeForSpeed=function(){return this._optimizeForSpeed},t.inject=function(){var e=this;if(c(!this._injected,"sheet already injected"),this._injected=!0,"undefined"!=typeof window&&this._optimizeForSpeed){this._tags[0]=this.makeStyleTag(this._name),this._optimizeForSpeed="insertRule"in this.getSheet(),this._optimizeForSpeed||(n||console.warn("StyleSheet: optimizeForSpeed mode not supported falling back to standard mode."),this.flush(),this._injected=!0);return}this._serverSheet={cssRules:[],insertRule:function(t,i){return"number"==typeof i?e._serverSheet.cssRules[i]={cssText:t}:e._serverSheet.cssRules.push({cssText:t}),i},deleteRule:function(t){e._serverSheet.cssRules[t]=null}}},t.getSheetForTag=function(e){if(e.sheet)return e.sheet;for(var t=0;t<document.styleSheets.length;t++)if(document.styleSheets[t].ownerNode===e)return document.styleSheets[t]},t.getSheet=function(){return this.getSheetForTag(this._tags[this._tags.length-1])},t.insertRule=function(e,t){if(c(l(e),"`insertRule` accepts only strings"),"undefined"==typeof window)return"number"!=typeof t&&(t=this._serverSheet.cssRules.length),this._serverSheet.insertRule(e,t),this._rulesCount++;if(this._optimizeForSpeed){var i=this.getSheet();"number"!=typeof t&&(t=i.cssRules.length);try{i.insertRule(e,t)}catch(t){return n||console.warn("StyleSheet: illegal rule: \n\n"+e+"\n\nSee https://stackoverflow.com/q/20007992 for more info"),-1}}else{var a=this._tags[t];this._tags.push(this.makeStyleTag(this._name,e,a))}return this._rulesCount++},t.replaceRule=function(e,t){if(this._optimizeForSpeed||"undefined"==typeof window){var i="undefined"!=typeof window?this.getSheet():this._serverSheet;if(t.trim()||(t=this._deletedRulePlaceholder),!i.cssRules[e])return e;i.deleteRule(e);try{i.insertRule(t,e)}catch(a){n||console.warn("StyleSheet: illegal rule: \n\n"+t+"\n\nSee https://stackoverflow.com/q/20007992 for more info"),i.insertRule(this._deletedRulePlaceholder,e)}}else{var a=this._tags[e];c(a,"old rule at index `"+e+"` not found"),a.textContent=t}return e},t.deleteRule=function(e){if("undefined"==typeof window){this._serverSheet.deleteRule(e);return}if(this._optimizeForSpeed)this.replaceRule(e,"");else{var t=this._tags[e];c(t,"rule at index `"+e+"` not found"),t.parentNode.removeChild(t),this._tags[e]=null}},t.flush=function(){this._injected=!1,this._rulesCount=0,"undefined"!=typeof window?(this._tags.forEach(function(e){return e&&e.parentNode.removeChild(e)}),this._tags=[]):this._serverSheet.cssRules=[]},t.cssRules=function(){var e=this;return"undefined"==typeof window?this._serverSheet.cssRules:this._tags.reduce(function(t,i){return i?t=t.concat(Array.prototype.map.call(e.getSheetForTag(i).cssRules,function(t){return t.cssText===e._deletedRulePlaceholder?null:t})):t.push(null),t},[])},t.makeStyleTag=function(e,t,i){t&&c(l(t),"makeStyleTag accepts only strings as second parameter");var a=document.createElement("style");this._nonce&&a.setAttribute("nonce",this._nonce),a.type="text/css",a.setAttribute("data-"+e,""),t&&a.appendChild(document.createTextNode(t));var s=document.head||document.getElementsByTagName("head")[0];return i?s.insertBefore(a,i):s.appendChild(a),a},function(e,t){for(var i=0;i<t.length;i++){var a=t[i];a.enumerable=a.enumerable||!1,a.configurable=!0,"value"in a&&(a.writable=!0),Object.defineProperty(e,a.key,a)}}(e.prototype,[{key:"length",get:function(){return this._rulesCount}}]),e}();function c(e,t){if(!e)throw Error("StyleSheet: "+t+".")}var d=function(e){for(var t=5381,i=e.length;i;)t=33*t^e.charCodeAt(--i);return t>>>0},p={};function u(e,t){if(!t)return"jsx-"+e;var i=String(t),a=e+i;return p[a]||(p[a]="jsx-"+d(e+"-"+i)),p[a]}function m(e,t){"undefined"==typeof window&&(t=t.replace(/\/style/gi,"\\/style"));var i=e+t;return p[i]||(p[i]=t.replace(/__jsx-style-dynamic-selector/g,e)),p[i]}var f=function(){function e(e){var t=void 0===e?{}:e,i=t.styleSheet,a=void 0===i?null:i,s=t.optimizeForSpeed,r=void 0!==s&&s;this._sheet=a||new o({name:"styled-jsx",optimizeForSpeed:r}),this._sheet.inject(),a&&"boolean"==typeof r&&(this._sheet.setOptimizeForSpeed(r),this._optimizeForSpeed=this._sheet.isOptimizeForSpeed()),this._fromServer=void 0,this._indices={},this._instancesCounts={}}var t=e.prototype;return t.add=function(e){var t=this;void 0===this._optimizeForSpeed&&(this._optimizeForSpeed=Array.isArray(e.children),this._sheet.setOptimizeForSpeed(this._optimizeForSpeed),this._optimizeForSpeed=this._sheet.isOptimizeForSpeed()),"undefined"==typeof window||this._fromServer||(this._fromServer=this.selectFromServer(),this._instancesCounts=Object.keys(this._fromServer).reduce(function(e,t){return e[t]=0,e},{}));var i=this.getIdAndRules(e),a=i.styleId,s=i.rules;if(a in this._instancesCounts){this._instancesCounts[a]+=1;return}var r=s.map(function(e){return t._sheet.insertRule(e)}).filter(function(e){return -1!==e});this._indices[a]=r,this._instancesCounts[a]=1},t.remove=function(e){var t=this,i=this.getIdAndRules(e).styleId;if(function(e,t){if(!e)throw Error("StyleSheetRegistry: "+t+".")}(i in this._instancesCounts,"styleId: `"+i+"` not found"),this._instancesCounts[i]-=1,this._instancesCounts[i]<1){var a=this._fromServer&&this._fromServer[i];a?(a.parentNode.removeChild(a),delete this._fromServer[i]):(this._indices[i].forEach(function(e){return t._sheet.deleteRule(e)}),delete this._indices[i]),delete this._instancesCounts[i]}},t.update=function(e,t){this.add(t),this.remove(e)},t.flush=function(){this._sheet.flush(),this._sheet.inject(),this._fromServer=void 0,this._indices={},this._instancesCounts={}},t.cssRules=function(){var e=this,t=this._fromServer?Object.keys(this._fromServer).map(function(t){return[t,e._fromServer[t]]}):[],i=this._sheet.cssRules();return t.concat(Object.keys(this._indices).map(function(t){return[t,e._indices[t].map(function(e){return i[e].cssText}).join(e._optimizeForSpeed?"":"\n")]}).filter(function(e){return!!e[1]}))},t.styles=function(e){var t,i;return t=this.cssRules(),void 0===(i=e)&&(i={}),t.map(function(e){var t=e[0],a=e[1];return r.default.createElement("style",{id:"__"+t,key:"__"+t,nonce:i.nonce?i.nonce:void 0,dangerouslySetInnerHTML:{__html:a}})})},t.getIdAndRules=function(e){var t=e.children,i=e.dynamic,a=e.id;if(i){var s=u(a,i);return{styleId:s,rules:Array.isArray(t)?t.map(function(e){return m(s,e)}):[m(s,t)]}}return{styleId:u(a),rules:Array.isArray(t)?t:[t]}},t.selectFromServer=function(){return Array.prototype.slice.call(document.querySelectorAll('[id^="__jsx-"]')).reduce(function(e,t){return e[t.id.slice(2)]=t,e},{})},e}(),x=s.createContext(null);x.displayName="StyleSheetContext";var h=r.default.useInsertionEffect||r.default.useLayoutEffect,b="undefined"!=typeof window?new f:void 0;function g(e){var t=b||s.useContext(x);return t&&("undefined"==typeof window?t.add(e):h(function(){return t.add(e),function(){t.remove(e)}},[e.id,String(e.dynamic)])),null}g.dynamic=function(e){return e.map(function(e){return u(e[0],e[1])}).join(" ")},t.style=g},29:function(e,t,i){"use strict";e.exports=i(18975).style},63055:function(e,t,i){"use strict";i.d(t,{HP:function(){return l},Op:function(){return u},Vs:function(){return p},he:function(){return c},ie:function(){return o},nR:function(){return m},qY:function(){return d},yW:function(){return f}});var a=i(61103),s=i(40256);let r=(0,a.bR)(),n=()=>({"Content-Type":"application/json","user-id":(0,a.n5)()}),l=()=>{let e={"Health Insurance":["Medical"],"Ancillary Benefits":["Dental","Vision"],"Life & Disability Insurance":["Term Life","Supplemental Life Insurance","Short-Term Disability","Long-Term Disability","Whole Life","Group (Employer) Life","Accidental Death & Dismemberment (AD&D)"],"Voluntary Benefits":["Hospital Indemnity","Accident Insurance","Critical Illness Insurance","Cancer Insurance","Gap Insurance","Legal Insurance","Identity Theft Protection","Accident & Illness (Pets)","Nursing Care / Custodial Care"],"Wellness & Mental Health":["Wellness Programs","Employee Assistance Program","Gym Membership"]};return{success:!0,data:{planTypes:["PPO","HMO","HDHP","MEC","EPO","POS","Indemnity","Term Life","Whole Life","STD","LTD"],coverageCategories:Object.keys(e),coverageMap:e,metalTiers:["Bronze","Silver","Gold","Platinum","Catastrophic"]}}},o=async()=>{try{let e=await fetch("".concat(r,"/api/pre-enrollment/carriers/assignable"),{method:"GET",headers:n()});if(!e.ok){let t=await e.text();try{let e=await fetch("".concat(r,"/api/pre-enrollment/carriers?isSystemCarrier=true"),{method:"GET",headers:n()});if(e.ok){let t=await e.json();return{success:!0,data:t.carriers||[]}}}catch(e){}throw Error("HTTP error! status: ".concat(e.status,", message: ").concat(t))}let t=await e.json();return{success:!0,data:t.carriers||[]}}catch(e){return{success:!1,error:e instanceof Error?e.message:"Failed to fetch carriers"}}},c=async e=>{try{let t=await s.be.post("/api/pre-enrollment/plans",e);return{success:!0,data:t.data}}catch(e){var t,i,a,r;return{success:!1,error:(null===(i=e.response)||void 0===i?void 0:null===(t=i.data)||void 0===t?void 0:t.error)||(null===(r=e.response)||void 0===r?void 0:null===(a=r.data)||void 0===a?void 0:a.message)||e.message||"Failed to create plan"}}},d=async e=>{try{var t;let i=new URLSearchParams;e&&(console.log("\uD83D\uDD27 getPlans called with filters:",e),Object.entries(e).forEach(e=>{let[t,a]=e;void 0!==a&&i.append(t,a.toString())}));let a="/api/pre-enrollment/plans".concat(i.toString()?"?".concat(i.toString()):"");console.log("\uD83C\uDF10 Fetching plans from endpoint:",a);let r=(await s.be.get(a)).data;console.log("\uD83D\uDCE6 getPlans response:",r),console.log("\uD83D\uDCCA Plans returned:",(null===(t=r.plans)||void 0===t?void 0:t.length)||0);let n=(r.plans||[]).map((e,t)=>{var i;let a;if(console.log("\uD83D\uDD0D Plan ".concat(t," raw data structure:"),{hasDoc:!!e._doc,hasId:!!e._id,docId:null===(i=e._doc)||void 0===i?void 0:i._id,directId:e._id}),e._doc)a={...e._doc,_id:e._doc._id||e._id,...e.carrierData&&{carrierData:e.carrierData}},console.log("\uD83D\uDCC4 Plan ".concat(t," extracted from _doc:"),a);else try{a=JSON.parse(JSON.stringify(e))}catch(i){console.warn("⚠️ JSON transform failed for plan ".concat(t,":"),i),a={...e}}return a._id?"object"==typeof a._id&&a._id.toString?a._id=a._id.toString():"string"!=typeof a._id&&(a._id=String(a._id)):console.warn("⚠️ Plan ".concat(t," still missing _id after transformation")),console.log("✅ Plan ".concat(t," final _id:"),a._id),a});return console.log("\uD83D\uDD04 Transformed plans sample:",n[0]),{success:!0,data:{plans:n,count:r.count,pagination:r.pagination}}}catch(e){return{success:!1,error:"Failed to fetch plans"}}},p=async(e,t)=>{try{let i=await s.be.put("/api/pre-enrollment/plans/".concat(e),t);return{success:!0,data:i.data}}catch(e){var i,a,r,n;return{success:!1,error:(null===(a=e.response)||void 0===a?void 0:null===(i=a.data)||void 0===i?void 0:i.error)||(null===(n=e.response)||void 0===n?void 0:null===(r=n.data)||void 0===r?void 0:r.message)||e.message||"Failed to update plan"}}},u=async(e,t)=>{try{let i=new FormData;t.forEach(e=>{i.append("documents",e)});let s=await fetch("".concat(r,"/api/pre-enrollment/plans/").concat(e,"/documents"),{method:"POST",headers:{"user-id":(0,a.n5)()},body:i});if(!s.ok){let e=await s.json();throw Error(e.error||"HTTP error! status: ".concat(s.status))}let n=await s.json();return{success:!0,data:n}}catch(e){return{success:!1,error:e instanceof Error?e.message:"Failed to upload documents"}}},m=async(e,t)=>{try{var i,a,s;console.log("\uD83D\uDD0D Checking for duplicate plan name:",e,t?"(excluding ".concat(t,")"):"");let r=await d({planName:e,strict:!0});if(console.log("\uD83D\uDCCA Plan name duplicate check result:",r),console.log("\uD83D\uDCCA Number of plans returned:",(null===(a=r.data)||void 0===a?void 0:null===(i=a.plans)||void 0===i?void 0:i.length)||0),!r.success)return console.log("❌ Plan name check failed:",r.error),{success:!1,error:r.error||"Failed to fetch plans for duplicate check"};let n=(null===(s=r.data)||void 0===s?void 0:s.plans)||[],l=t?n.filter(e=>e._id!==t):n,o=l.length>0?l[0]:void 0;return console.log("\uD83C\uDFAF Duplicate plan found:",o?"YES - ".concat(o.planName):"NO",t?"(excluded ".concat(t,")"):""),{success:!0,data:{isDuplicate:!!o,existingPlan:o}}}catch(e){return console.log("\uD83D\uDCA5 Plan name check error:",e),{success:!1,error:"Failed to check for duplicate plan name"}}},f=async(e,t)=>{try{var i,a,s;console.log("\uD83D\uDD0D Checking for duplicate plan code:",e,t?"(excluding ".concat(t,")"):"");let r=await d({planCode:e,strict:!0});if(console.log("\uD83D\uDCCA Plan code duplicate check result:",r),console.log("\uD83D\uDCCA Number of plans returned:",(null===(a=r.data)||void 0===a?void 0:null===(i=a.plans)||void 0===i?void 0:i.length)||0),!r.success)return console.log("❌ Plan code check failed:",r.error),{success:!1,error:r.error||"Failed to fetch plans for duplicate check"};let n=(null===(s=r.data)||void 0===s?void 0:s.plans)||[],l=t?n.filter(e=>e._id!==t):n,o=l.length>0?l[0]:void 0;return console.log("\uD83C\uDFAF Duplicate plan found:",o?"YES - ".concat(o.planCode):"NO",t?"(excluded ".concat(t,")"):""),{success:!0,data:{isDuplicate:!!o,existingPlan:o}}}catch(e){return console.log("\uD83D\uDCA5 Plan code check error:",e),{success:!1,error:"Failed to check for duplicate plan code"}}}},57804:function(e,t,i){"use strict";var a=i(57437),s=i(29),r=i.n(s),n=i(2265),l=i(18913),o=i(84154),c=i(63055);i(43010),t.Z=(0,n.memo)(e=>{let{onCancel:t,onSubmit:i,initialData:s,isModal:d=!1,existingCarriers:p}=e,[u,m]=(0,n.useState)(1),[f,x]=(0,n.useState)(!1),[h,b]=(0,n.useState)(null),[g,v]=(0,n.useState)(!1),[y,j]=(0,n.useState)({isChecking:!1,isDuplicate:!1,error:null,existingPlan:null}),[w,k]=(0,n.useState)({isChecking:!1,isDuplicate:!1,error:null,existingPlan:null}),[N,S]=(0,n.useState)({documents:[],coverageCategory:"",coverageType:"",carrier:"",planName:(null==s?void 0:s.planName)||"",planCode:(null==s?void 0:s.planCode)||"",planType:(null==s?void 0:s.planType)||"",metalTier:(null==s?void 0:s.metalTier)||"",videoUrl:"",description:(null==s?void 0:s.description)||"",highlights:(null==s?void 0:s.highlights)||[""]}),C=[{number:1,title:"Documents",active:1===u,completed:u>1,tooltip:"Upload plan documents, brochures, and supporting materials (optional)"},{number:2,title:"Basic Details",active:2===u,completed:u>2,tooltip:"Enter plan name, carrier, coverage type, and metal tier information"},{number:3,title:"Description",active:3===u,completed:u>3,tooltip:"Add plan description, key highlights, and optional video content"},{number:4,title:"Preview",active:4===u,completed:u>4,tooltip:"Review all plan details and create your new plan"}];(0,n.useEffect)(()=>{(async()=>{try{var e,t,i,a,s,r,n,l;let o=(0,c.HP)(),d=p||[],u=!0;if(p)console.log("⚡ Using existing carriers data, skipping API call");else{console.log("\uD83D\uDD04 Fetching carriers from API...");let e=await (0,c.ie)();d=e.data||[],u=e.success}if(o.success&&u){let s={plans:[],templates:[],carriers:d,planTypes:(null===(e=o.data)||void 0===e?void 0:e.planTypes)||[],coverageCategories:(null===(t=o.data)||void 0===t?void 0:t.coverageCategories)||[],coverageMap:(null===(i=o.data)||void 0===i?void 0:i.coverageMap)||{},metalTiers:(null===(a=o.data)||void 0===a?void 0:a.metalTiers)||[]};b(s)}else{console.error("Failed to load data:",{constants:o.success,carriers:u,carriersError:p?"Using existing carriers":"API call failed"});let e={plans:[],templates:[],carriers:d,planTypes:(null===(s=o.data)||void 0===s?void 0:s.planTypes)||[],coverageCategories:(null===(r=o.data)||void 0===r?void 0:r.coverageCategories)||[],coverageMap:(null===(n=o.data)||void 0===n?void 0:n.coverageMap)||{},metalTiers:(null===(l=o.data)||void 0===l?void 0:l.metalTiers)||[]};b(e)}}catch(e){console.error("Error loading data:",e),b({plans:[],templates:[],carriers:[],planTypes:[],coverageCategories:[],coverageMap:{},metalTiers:[]})}})()},[]);let _=n.useMemo(()=>{let e;return t=>{if(clearTimeout(e),!t.trim()){j({isChecking:!1,isDuplicate:!1,error:null,existingPlan:null});return}j(e=>({...e,isChecking:!0,error:null})),e=setTimeout(async()=>{try{let e=await (0,c.nR)(t,null==s?void 0:s._id);e.success&&e.data?j({isChecking:!1,isDuplicate:e.data.isDuplicate,error:null,existingPlan:e.data.existingPlan||null}):j({isChecking:!1,isDuplicate:!1,error:e.error||"Failed to check for duplicates",existingPlan:null})}catch(e){j({isChecking:!1,isDuplicate:!1,error:"Network error while checking for duplicates",existingPlan:null})}},800)}},[null==s?void 0:s._id]),z=n.useMemo(()=>{let e;return t=>{if(clearTimeout(e),!t.trim()){k({isChecking:!1,isDuplicate:!1,error:null,existingPlan:null});return}k(e=>({...e,isChecking:!0,error:null})),e=setTimeout(async()=>{try{let e=await (0,c.yW)(t,null==s?void 0:s._id);e.success&&e.data?k({isChecking:!1,isDuplicate:e.data.isDuplicate,error:null,existingPlan:e.data.existingPlan||null}):k({isChecking:!1,isDuplicate:!1,error:e.error||"Failed to check for duplicates",existingPlan:null})}catch(e){k({isChecking:!1,isDuplicate:!1,error:"Network error while checking for duplicates",existingPlan:null})}},800)}},[null==s?void 0:s._id]);(0,n.useEffect)(()=>{if(N.planName&&N.planName.trim()){if(s&&s.planName===N.planName){console.log("⏭️ Skipping duplicate name check - editing same plan");return}_(N.planName)}},[N.planName,_,s]),(0,n.useEffect)(()=>{if(N.planCode&&N.planCode.trim()){if(s&&s.planCode===N.planCode){console.log("⏭️ Skipping duplicate code check - editing same plan");return}z(N.planCode)}},[N.planCode,z,s]),(0,n.useEffect)(()=>{let e=null,t=(t,a)=>{i();let s=t.getBoundingClientRect(),r=s.left+s.width/2-140;r<10&&(r=10),r+280>window.innerWidth-10&&(r=window.innerWidth-280-10);let n=s.top,l=window.innerHeight-s.bottom,o=n<120&&l>120,c=document.createElement("div");c.className="custom-tooltip",c.textContent=a,c.style.cssText="\n        position: fixed;\n        ".concat(o?"top: ".concat(s.bottom+4,"px;"):"bottom: ".concat(window.innerHeight-s.top+4,"px;"),"\n        left: ").concat(r,"px;\n        width: 280px;\n        background: #1f2937;\n        color: white;\n        padding: 0.75rem 1rem;\n        border-radius: 0.5rem;\n        font-size: 0.8rem;\n        font-weight: 400;\n        line-height: 1.4;\n        text-align: left;\n        white-space: normal;\n        word-wrap: break-word;\n        hyphens: auto;\n        box-shadow: 0 8px 16px rgba(0, 0, 0, 0.15);\n        z-index: 99999;\n        pointer-events: none;\n        opacity: 0;\n        transition: opacity 0.2s ease-in-out;\n      ");let d=document.createElement("div");d.className="custom-tooltip-arrow",d.style.cssText="\n        position: fixed;\n        ".concat(o?"top: ".concat(s.bottom-2,"px;"):"bottom: ".concat(window.innerHeight-s.top-2,"px;"),"\n        left: ").concat(s.left+s.width/2-6,"px;\n        width: 0;\n        height: 0;\n        border-left: 6px solid transparent;\n        border-right: 6px solid transparent;\n        ").concat(o?"border-bottom: 6px solid #1f2937;":"border-top: 6px solid #1f2937;","\n        z-index: 100000;\n        pointer-events: none;\n        opacity: 0;\n        transition: opacity 0.2s ease-in-out;\n      "),document.body.appendChild(c),document.body.appendChild(d),requestAnimationFrame(()=>{c.style.opacity="1",d.style.opacity="1"}),(e=c).arrow=d},i=()=>{e&&(e.remove(),e.arrow&&e.arrow.remove(),e=null)},a=e=>{let i=e.target,a=i.getAttribute("data-tooltip");a&&t(i,a)},s=()=>{i()},r=document.querySelectorAll(".tooltip-icon[data-tooltip]");return r.forEach(e=>{e.addEventListener("mouseenter",a),e.addEventListener("mouseleave",s)}),()=>{i(),r.forEach(e=>{e.removeEventListener("mouseenter",a),e.removeEventListener("mouseleave",s)})}},[u]),(0,n.useEffect)(()=>{if(s&&h){var e;console.log("\uD83D\uDD04 Prepopulating form with initialData:",s),console.log("\uD83D\uDD04 Available data:",h);let t="";if(null===(e=s.carrierData)||void 0===e?void 0:e._id)console.log("\uD83C\uDFAF Using embedded carrierData._id:",t=s.carrierData._id);else if(s.carrierId)console.log("\uD83C\uDFAF Using carrierId:",t=s.carrierId);else if(s.carrier){let e=h.carriers.find(e=>e._id===s.carrier),i=h.carriers.find(e=>e.carrierName===s.carrier||e.displayName===s.carrier);console.log("\uD83C\uDFAF Using carrier matching fallback:",t=(null==e?void 0:e._id)||(null==i?void 0:i._id)||"")}let i="",a="";if(h.coverageMap&&s.coverageType){for(let[e,t]of Object.entries(h.coverageMap))if(t.includes(s.coverageType)){i=e,a=s.coverageType;break}}if(!a&&s.coverageSubTypes&&s.coverageSubTypes.length>0){for(let[e,t]of(a=s.coverageSubTypes[0],Object.entries(h.coverageMap||{})))if(t.includes(a)){i=e;break}}console.log("\uD83D\uDD04 Prepopulation results:",{planName:s.planName,planCode:s.planCode,carrierId:t,coverageCategory:i,coverageSubType:a,planType:s.planType,metalTier:s.metalTier}),S(e=>{var r;return{...e,planName:s.planName||"",planCode:s.planCode||"",carrier:t,coverageCategory:i,coverageType:a,planType:s.planType||"",metalTier:s.metalTier||"",description:s.description||"",highlights:s.highlights||[""],videoUrl:(null===(r=s.informativeLinks)||void 0===r?void 0:r[0])||""}})}},[s,h]);let D=(e,t)=>{S(i=>({...i,[e]:t}))},T=e=>{if(e){let t=Array.from(e);S(e=>({...e,documents:[...e.documents,...t]}))}},P=e=>{S(t=>({...t,documents:t.documents.filter((t,i)=>i!==e)}))},F=()=>{u<4&&m(u+1)},R=()=>{u>1&&m(u-1)},A=async()=>{v(!0);try{let e;let t=N.coverageCategory,a={planName:N.planName,planCode:N.planCode,carrier:N.carrier,coverageType:t,coverageSubTypes:[N.coverageType],planType:N.planType,metalTier:N.metalTier,description:N.description,highlights:N.highlights.filter(e=>""!==e.trim()),informativeLinks:N.videoUrl?[N.videoUrl]:[],carrierId:N.carrier,isTemplate:!1,status:"Active"};if((e=s?await (0,c.Vs)(s._id,a):await (0,c.he)(a)).success&&e.data){if(console.log("Plan ".concat(s?"updated":"created"," successfully:"),e.data),!s&&N.documents.length>0){let t=await (0,c.Op)(e.data.plan._id,N.documents);t.success||console.warn("Failed to upload some documents:",t.error)}let t={_id:e.data.plan._id,planName:e.data.plan.planName,planCode:e.data.plan.planCode,coverageType:e.data.plan.coverageType,coverageSubTypes:e.data.plan.coverageSubTypes||[],planType:e.data.plan.planType,metalTier:e.data.plan.metalTier||N.metalTier||"Bronze",description:e.data.plan.description,highlights:e.data.plan.highlights,carrier:e.data.plan.carrier||N.carrier};i(t)}else throw Error(e.error||"Failed to ".concat(s?"update":"create"," plan"))}catch(e){console.error("Error ".concat(s?"updating":"creating"," plan:"),e),alert("Error ".concat(s?"updating":"creating"," plan: ").concat(e instanceof Error?e.message:"Please try again."))}finally{v(!1)}},I=()=>{S(e=>({...e,highlights:[...e.highlights,""]}))},E=(e,t)=>{S(i=>({...i,highlights:i.highlights.map((i,a)=>a===e?t:i)}))},H=e=>{N.highlights.length>1&&S(t=>({...t,highlights:t.highlights.filter((t,i)=>i!==e)}))},O=()=>N.planName&&N.planCode&&N.carrier&&N.planType&&N.coverageCategory&&N.coverageType&&!y.isDuplicate&&!y.isChecking&&!w.isDuplicate&&!w.isChecking,M=()=>N.description&&N.highlights.some(e=>""!==e.trim());if(!h)return(0,a.jsx)("div",{className:"p-6 text-center",children:"Loading..."});console.log("CreatePlanForm data:",h),console.log("Coverage categories:",h.coverageCategories),console.log("Coverage map:",h.coverageMap);let W=()=>{var e,i,s,n,c,d;return(0,a.jsxs)("div",{className:"jsx-756eb827991f8e8b space-y-6",children:[(0,a.jsx)(r(),{id:"756eb827991f8e8b",children:"input.jsx-756eb827991f8e8b,select.jsx-756eb827991f8e8b,textarea.jsx-756eb827991f8e8b{background-color:white!important;color:#374151!important}input.jsx-756eb827991f8e8b::-webkit-input-placeholder{color:#9ca3af!important}input.jsx-756eb827991f8e8b:-moz-placeholder{color:#9ca3af!important}input.jsx-756eb827991f8e8b::-moz-placeholder{color:#9ca3af!important}input.jsx-756eb827991f8e8b:-ms-input-placeholder{color:#9ca3af!important}input.jsx-756eb827991f8e8b::-ms-input-placeholder{color:#9ca3af!important}input.jsx-756eb827991f8e8b::placeholder{color:#9ca3af!important}"}),(0,a.jsx)("div",{className:"jsx-756eb827991f8e8b flex items-center justify-between mb-6",children:(0,a.jsxs)("div",{className:"jsx-756eb827991f8e8b flex items-center gap-3",children:[(0,a.jsx)(o.kAf,{size:20,className:"text-purple-600"}),(0,a.jsx)("h3",{style:{fontSize:"14px",fontWeight:"500",lineHeight:"21px",color:"#374151"},className:"jsx-756eb827991f8e8b",children:"Basic Details"})]})}),(0,a.jsxs)("div",{className:"jsx-756eb827991f8e8b space-y-2",children:[(0,a.jsxs)("div",{className:"jsx-756eb827991f8e8b",children:[(0,a.jsxs)("label",{htmlFor:"coverageCategory",style:{fontSize:"14px",fontWeight:"500",lineHeight:"21px"},className:"jsx-756eb827991f8e8b block text-gray-900 mb-1",children:["Coverage Category *",(0,a.jsx)("span",{"data-tooltip":"Select the main category of coverage this plan provides (e.g., Medical, Dental, Vision)",className:"jsx-756eb827991f8e8b tooltip-icon"})]}),(0,a.jsxs)("select",{id:"coverageCategory",value:N.coverageCategory,onChange:e=>{D("coverageCategory",e.target.value),D("coverageType","")},style:{backgroundColor:"white",color:"#374151"},className:"jsx-756eb827991f8e8b w-full px-4 py-2.5 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-gray-900 bg-white",children:[(0,a.jsx)("option",{value:"",className:"jsx-756eb827991f8e8b",children:"Select coverage category"}),(null==h?void 0:null===(e=h.coverageCategories)||void 0===e?void 0:e.map(e=>(0,a.jsx)("option",{value:e,className:"jsx-756eb827991f8e8b",children:e},e)))||[]]})]}),(0,a.jsxs)("div",{className:"jsx-756eb827991f8e8b",children:[(0,a.jsxs)("label",{htmlFor:"coverageType",style:{fontSize:"14px",fontWeight:"500",lineHeight:"21px"},className:"jsx-756eb827991f8e8b block text-gray-900 mb-1",children:["Coverage Type *",(0,a.jsx)("span",{"data-tooltip":"Select the specific type of coverage within the category (e.g., PPO, HMO, EPO for Medical)",className:"jsx-756eb827991f8e8b tooltip-icon"})]}),(0,a.jsxs)("select",{id:"coverageType",value:N.coverageType,onChange:e=>D("coverageType",e.target.value),disabled:!N.coverageCategory,style:{backgroundColor:N.coverageCategory?"white":"#f9fafb",color:"#374151"},className:"jsx-756eb827991f8e8b w-full px-4 py-2.5 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-gray-900 bg-white disabled:bg-gray-100 disabled:cursor-not-allowed",children:[(0,a.jsx)("option",{value:"",className:"jsx-756eb827991f8e8b",children:"Select coverage type"}),N.coverageCategory&&(null==h?void 0:null===(s=h.coverageMap)||void 0===s?void 0:null===(i=s[N.coverageCategory])||void 0===i?void 0:i.map(e=>(0,a.jsx)("option",{value:e,className:"jsx-756eb827991f8e8b",children:e},e)))||[]]})]}),(0,a.jsxs)("div",{className:"jsx-756eb827991f8e8b",children:[(0,a.jsxs)("label",{htmlFor:"carrier",style:{fontSize:"14px",fontWeight:"500",lineHeight:"21px"},className:"jsx-756eb827991f8e8b block text-gray-900 mb-1",children:["Carrier *",(0,a.jsx)("span",{"data-tooltip":"Select the insurance carrier that provides this plan (e.g., Blue Shield, Kaiser)",className:"jsx-756eb827991f8e8b tooltip-icon"})]}),(0,a.jsxs)("select",{id:"carrier",value:N.carrier,onChange:e=>D("carrier",e.target.value),className:"jsx-756eb827991f8e8b w-full px-4 py-2.5 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-gray-900 bg-white",children:[(0,a.jsx)("option",{value:"",className:"jsx-756eb827991f8e8b",children:"Select carrier"}),(null==h?void 0:null===(n=h.carriers)||void 0===n?void 0:n.map(e=>(0,a.jsx)("option",{value:e._id,className:"jsx-756eb827991f8e8b",children:e.displayName||e.carrierName},e._id)))||[]]})]}),(0,a.jsxs)("div",{className:"jsx-756eb827991f8e8b",children:[(0,a.jsxs)("label",{htmlFor:"planName",style:{fontSize:"14px",fontWeight:"500",lineHeight:"21px"},className:"jsx-756eb827991f8e8b block text-gray-900 mb-1",children:["Plan Name *",(0,a.jsx)("span",{"data-tooltip":"Enter a clear, descriptive name that helps identify this plan (e.g., Blue Shield PPO 500). We'll check for duplicates automatically.",className:"jsx-756eb827991f8e8b tooltip-icon"})]}),(0,a.jsxs)("div",{className:"jsx-756eb827991f8e8b relative",children:[(0,a.jsx)("input",{type:"text",id:"planName",placeholder:"e.g. Blue Shield PPO 500",value:N.planName,onChange:e=>D("planName",e.target.value),style:{backgroundColor:"white",color:"#374151"},className:"jsx-756eb827991f8e8b "+"w-full px-4 py-2.5 pr-12 border rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-gray-900 bg-white ".concat(y.isDuplicate?"border-red-500":y.isChecking?"border-yellow-500":N.planName&&!y.isDuplicate?"border-green-500":"border-gray-300")}),(0,a.jsxs)("div",{className:"jsx-756eb827991f8e8b absolute right-3 top-1/2 transform -translate-y-1/2",children:[y.isChecking&&(0,a.jsx)("div",{className:"jsx-756eb827991f8e8b w-4 h-4 border-2 border-yellow-500 border-t-transparent rounded-full animate-spin"}),!y.isChecking&&N.planName&&!y.isDuplicate&&(0,a.jsx)("span",{className:"jsx-756eb827991f8e8b text-green-600 font-bold",children:"✓"}),y.isDuplicate&&(0,a.jsx)("span",{className:"jsx-756eb827991f8e8b text-red-600 font-bold",children:"✗"})]})]}),y.isDuplicate&&y.existingPlan&&(0,a.jsxs)("div",{className:"jsx-756eb827991f8e8b mt-2 p-2 bg-red-50 border border-red-200 rounded text-sm text-red-700",children:[(0,a.jsx)("strong",{className:"jsx-756eb827991f8e8b",children:"Plan name already exists:"}),' "',y.existingPlan.planName,'"',y.existingPlan.planCode&&" (".concat(y.existingPlan.planCode,")")]}),y.error&&(0,a.jsxs)("div",{className:"jsx-756eb827991f8e8b mt-2 p-2 bg-yellow-50 border border-yellow-200 rounded text-sm text-yellow-700",children:[(0,a.jsx)("strong",{className:"jsx-756eb827991f8e8b",children:"Warning:"})," ",y.error]}),N.planName&&!y.isChecking&&!y.isDuplicate&&!y.error&&(0,a.jsx)("div",{className:"jsx-756eb827991f8e8b mt-2 p-2 bg-green-50 border border-green-200 rounded text-sm text-green-700",children:"✓ Plan name is available"})]}),(0,a.jsxs)("div",{className:"jsx-756eb827991f8e8b",children:[(0,a.jsxs)("label",{htmlFor:"planCode",style:{fontSize:"14px",fontWeight:"500",lineHeight:"21px"},className:"jsx-756eb827991f8e8b block text-gray-900 mb-1",children:["Plan Code *",(0,a.jsx)("span",{"data-tooltip":"Unique identifier for this plan used in systems and reports (e.g., BS-PPO-500). We'll check for duplicates automatically.",className:"jsx-756eb827991f8e8b tooltip-icon"})]}),(0,a.jsxs)("div",{className:"jsx-756eb827991f8e8b relative",children:[(0,a.jsx)("input",{type:"text",id:"planCode",placeholder:"e.g. BS-PPO-500",value:N.planCode,onChange:e=>D("planCode",e.target.value),style:{backgroundColor:"white",color:"#374151"},className:"jsx-756eb827991f8e8b "+"w-full px-4 py-2.5 pr-12 border rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-gray-900 bg-white ".concat(w.isDuplicate?"border-red-500":w.isChecking?"border-yellow-500":N.planCode&&!w.isDuplicate?"border-green-500":"border-gray-300")}),(0,a.jsxs)("div",{className:"jsx-756eb827991f8e8b absolute right-3 top-1/2 transform -translate-y-1/2",children:[w.isChecking&&(0,a.jsx)("div",{className:"jsx-756eb827991f8e8b w-4 h-4 border-2 border-yellow-500 border-t-transparent rounded-full animate-spin"}),!w.isChecking&&N.planCode&&!w.isDuplicate&&(0,a.jsx)("span",{className:"jsx-756eb827991f8e8b text-green-600 font-bold",children:"✓"}),w.isDuplicate&&(0,a.jsx)("span",{className:"jsx-756eb827991f8e8b text-red-600 font-bold",children:"✗"})]})]}),w.isDuplicate&&w.existingPlan&&(0,a.jsxs)("div",{className:"jsx-756eb827991f8e8b mt-2 p-2 bg-red-50 border border-red-200 rounded text-sm text-red-700",children:[(0,a.jsx)("strong",{className:"jsx-756eb827991f8e8b",children:"Plan code already exists:"}),' "',w.existingPlan.planCode,'"',w.existingPlan.planName&&" (".concat(w.existingPlan.planName,")")]}),w.error&&(0,a.jsxs)("div",{className:"jsx-756eb827991f8e8b mt-2 p-2 bg-yellow-50 border border-yellow-200 rounded text-sm text-yellow-700",children:[(0,a.jsx)("strong",{className:"jsx-756eb827991f8e8b",children:"Warning:"})," ",w.error]}),N.planCode&&!w.isChecking&&!w.isDuplicate&&!w.error&&(0,a.jsx)("div",{className:"jsx-756eb827991f8e8b mt-2 p-2 bg-green-50 border border-green-200 rounded text-sm text-green-700",children:"✓ Plan code is available"})]}),(0,a.jsxs)("div",{className:"jsx-756eb827991f8e8b",children:[(0,a.jsxs)("label",{htmlFor:"planType",style:{fontSize:"14px",fontWeight:"500",lineHeight:"21px"},className:"jsx-756eb827991f8e8b block text-gray-900 mb-1",children:["Plan Type *",(0,a.jsx)("span",{"data-tooltip":"Choose the type of health plan structure (PPO, HMO, EPO, POS, etc.)",className:"jsx-756eb827991f8e8b tooltip-icon"})]}),(0,a.jsxs)("select",{id:"planType",value:N.planType,onChange:e=>D("planType",e.target.value),className:"jsx-756eb827991f8e8b w-full px-4 py-2.5 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-gray-900 bg-white",children:[(0,a.jsx)("option",{value:"",className:"jsx-756eb827991f8e8b",children:"Select type"}),(null==h?void 0:null===(c=h.planTypes)||void 0===c?void 0:c.map(e=>(0,a.jsx)("option",{value:e,className:"jsx-756eb827991f8e8b",children:e},e)))||[]]})]}),(0,a.jsxs)("div",{className:"jsx-756eb827991f8e8b",children:[(0,a.jsxs)("label",{htmlFor:"metalTier",style:{fontSize:"14px",fontWeight:"500",lineHeight:"21px"},className:"jsx-756eb827991f8e8b block text-gray-900 mb-1",children:["Metal Tier (Optional)",(0,a.jsx)("span",{"data-tooltip":"Choose the coverage level (Bronze, Silver, Gold, Platinum). This field is optional and typically applies to medical plans.",className:"jsx-756eb827991f8e8b tooltip-icon"})]}),(0,a.jsxs)("select",{id:"metalTier",value:N.metalTier,onChange:e=>D("metalTier",e.target.value),className:"jsx-756eb827991f8e8b w-full px-4 py-2.5 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-gray-900 bg-white",children:[(0,a.jsx)("option",{value:"",className:"jsx-756eb827991f8e8b",children:"Select tier"}),(null==h?void 0:null===(d=h.metalTiers)||void 0===d?void 0:d.map(e=>(0,a.jsx)("option",{value:e,className:"jsx-756eb827991f8e8b",children:e},e)))||[]]})]})]}),(0,a.jsxs)("div",{className:"jsx-756eb827991f8e8b flex justify-between pt-4",children:[(0,a.jsxs)("button",{type:"button",onClick:t,className:"jsx-756eb827991f8e8b flex items-center gap-2 px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50",children:[(0,a.jsx)(l.Tsu,{size:16}),"Back to Selection"]}),(0,a.jsx)("button",{type:"button",onClick:F,disabled:!O(),style:{fontSize:"14px",lineHeight:"21px",fontFamily:"'SF Pro', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif"},className:"jsx-756eb827991f8e8b "+"px-4 py-2 rounded-md ".concat(O()?"bg-gradient-to-r from-blue-600 to-purple-600 text-white hover:opacity-90":"bg-gray-300 text-gray-500 cursor-not-allowed"),children:"Continue to Basic Info"})]})]})},U=()=>(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3 mb-6",children:[(0,a.jsx)(o.jNQ,{size:20,className:"text-purple-600"}),(0,a.jsx)("h3",{style:{fontSize:"14px",fontWeight:"500",lineHeight:"21px",color:"#374151"},children:"Description & Video"})]}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("label",{htmlFor:"videoUrl",style:{fontSize:"14px",fontWeight:"500",lineHeight:"21px"},className:"block text-gray-900 mb-1",children:["Video URL (Optional)",(0,a.jsx)("span",{className:"tooltip-icon","data-tooltip":"Add a YouTube or Vimeo URL to help explain plan benefits and features"})]}),(0,a.jsxs)("div",{className:"input-with-prefix",children:[(0,a.jsx)("span",{className:"input-prefix",children:"\uD83C\uDFA5"}),(0,a.jsx)("input",{type:"url",id:"videoUrl",placeholder:"https://youtube.com/watch?v=...",value:N.videoUrl,onChange:e=>D("videoUrl",e.target.value),style:{fontSize:"14px",fontWeight:"400",lineHeight:"21px"},className:"w-full px-4 py-2.5 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-gray-900 bg-white"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("label",{htmlFor:"description",style:{fontSize:"14px",fontWeight:"500",lineHeight:"21px"},className:"block text-gray-900 mb-1",children:["Plan Description *",(0,a.jsx)("span",{className:"tooltip-icon","data-tooltip":"Provide a detailed description of the plan benefits, coverage, and key features"})]}),(0,a.jsx)("textarea",{id:"description",placeholder:"Describe the plan benefits and features...",value:N.description,onChange:e=>D("description",e.target.value),rows:4,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 text-gray-900 bg-white"}),(0,a.jsx)("small",{className:"text-gray-500 text-sm",children:"Describe the key benefits, coverage details, and what makes this plan unique"})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("label",{style:{fontSize:"14px",fontWeight:"500",lineHeight:"21px"},className:"block text-gray-900 mb-1",children:["Plan Highlights *",(0,a.jsx)("span",{className:"tooltip-icon","data-tooltip":"Add key selling points and highlights that make this plan attractive (e.g., Low deductible, Nationwide network, No referrals needed)"})]}),(0,a.jsx)("small",{className:"text-gray-500 text-sm mb-3 block",children:"Add the most important features that make this plan attractive"}),(0,a.jsx)("div",{className:"space-y-3",children:N.highlights.map((e,t)=>(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("input",{type:"text",placeholder:"e.g. Low deductible, Nationwide network",value:e,onChange:e=>E(t,e.target.value),className:"flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 text-gray-900 bg-white"}),N.highlights.length>1&&(0,a.jsx)("button",{type:"button",onClick:()=>H(t),className:"text-red-500 hover:text-red-700 p-1",children:(0,a.jsx)(l.fMW,{size:16})})]},t))}),(0,a.jsxs)("button",{type:"button",onClick:I,className:"mt-3 flex items-center gap-2 text-purple-600 hover:text-purple-800 text-sm font-medium",children:[(0,a.jsx)(l.r7I,{size:16}),"Add Highlight"]})]})]}),(0,a.jsxs)("div",{className:"flex justify-between pt-4",children:[(0,a.jsxs)("button",{type:"button",onClick:R,className:"flex items-center gap-2 px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50",children:[(0,a.jsx)(l.Tsu,{size:16}),"Back"]}),(0,a.jsx)("button",{type:"button",onClick:F,disabled:!M(),className:"px-4 py-2 rounded-md ".concat(M()?"bg-gradient-to-r from-blue-600 to-purple-600 text-white hover:opacity-90":"bg-gray-300 text-gray-500 cursor-not-allowed"),style:{fontSize:"14px",lineHeight:"21px",fontFamily:"'SF Pro', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif"},children:"Preview Plan"})]})]}),B=()=>{var e,t,i,r;return(0,a.jsxs)("div",{className:"form-section",children:[(0,a.jsxs)("div",{className:"form-header",children:[(0,a.jsxs)("div",{className:"form-header-content",children:[(0,a.jsx)(o.GH$,{size:20}),(0,a.jsx)("h3",{style:{fontSize:"14px",fontWeight:"500",lineHeight:"21px",color:"#374151"},children:"AI-Powered Plan Preview"})]}),(0,a.jsxs)("div",{style:{display:"flex",alignItems:"center",gap:"0.5rem",padding:"0.375rem 0.75rem",borderRadius:"0.5rem",fontSize:"0.875rem",fontWeight:"500",background:"#dcfce7",color:"#166534"},children:[(0,a.jsx)(l.PjL,{size:16}),"Ready to Create"]})]}),(0,a.jsxs)("div",{className:"review-content",children:[(0,a.jsxs)("div",{className:"review-section",children:[(0,a.jsxs)("div",{className:"review-section-header",children:[(0,a.jsx)(o.kAf,{size:18}),(0,a.jsx)("h4",{children:"Plan Information"})]}),(0,a.jsxs)("div",{className:"review-items",children:[(0,a.jsxs)("div",{className:"review-item",children:[(0,a.jsx)("span",{className:"review-label",children:"Plan Name:"}),(0,a.jsx)("span",{className:"review-value",children:N.planName})]}),(0,a.jsxs)("div",{className:"review-item",children:[(0,a.jsx)("span",{className:"review-label",children:"Plan Code:"}),(0,a.jsx)("span",{className:"review-value font-mono",children:N.planCode})]}),(0,a.jsxs)("div",{className:"review-item",children:[(0,a.jsx)("span",{className:"review-label",children:"Carrier:"}),(0,a.jsx)("span",{className:"review-value",children:(null==h?void 0:null===(t=h.carriers)||void 0===t?void 0:null===(e=t.find(e=>e._id===N.carrier))||void 0===e?void 0:e.displayName)||(null==h?void 0:null===(r=h.carriers)||void 0===r?void 0:null===(i=r.find(e=>e._id===N.carrier))||void 0===i?void 0:i.carrierName)||"Unknown"})]}),(0,a.jsxs)("div",{className:"review-item",children:[(0,a.jsx)("span",{className:"review-label",children:"Plan Type:"}),(0,a.jsx)("span",{className:"review-value",children:N.planType})]}),(0,a.jsxs)("div",{className:"review-item",children:[(0,a.jsx)("span",{className:"review-label",children:"Coverage Type:"}),(0,a.jsx)("span",{className:"review-value",children:N.coverageType})]}),N.metalTier&&(0,a.jsxs)("div",{className:"review-item",children:[(0,a.jsx)("span",{className:"review-label",children:"Metal Tier:"}),(0,a.jsx)("span",{className:"review-value",children:N.metalTier})]})]})]}),(N.videoUrl||N.documents.length>0)&&(0,a.jsxs)("div",{className:"review-section",children:[(0,a.jsxs)("div",{className:"review-section-header",children:[(0,a.jsx)(l.vrJ,{size:18}),(0,a.jsx)("h4",{children:"Media & Documents"})]}),(0,a.jsxs)("div",{className:"review-items",children:[N.videoUrl&&(0,a.jsxs)("div",{className:"review-item",children:[(0,a.jsx)("span",{className:"review-label",children:"Video URL:"}),(0,a.jsx)("a",{href:N.videoUrl,target:"_blank",rel:"noopener noreferrer",className:"review-link",children:N.videoUrl})]}),N.documents.length>0&&(0,a.jsxs)("div",{className:"review-item full-width",children:[(0,a.jsx)("span",{className:"review-label",children:"Documents:"}),(0,a.jsx)("div",{className:"review-documents",children:N.documents.map((e,t)=>(0,a.jsxs)("div",{className:"review-document",children:[(0,a.jsx)(l.vrJ,{size:14}),(0,a.jsx)("span",{children:e.name}),(0,a.jsxs)("small",{children:[(e.size/1024/1024).toFixed(2)," MB"]})]},t))})]})]})]}),(0,a.jsxs)("div",{className:"review-section",children:[(0,a.jsxs)("div",{className:"review-section-header",children:[(0,a.jsx)(o.jNQ,{size:18}),(0,a.jsx)("h4",{children:"Description & Highlights"})]}),(0,a.jsxs)("div",{className:"review-items",children:[(0,a.jsxs)("div",{className:"review-item full-width",children:[(0,a.jsx)("span",{className:"review-label",children:"Description:"}),(0,a.jsx)("p",{className:"review-value",children:N.description})]}),(0,a.jsxs)("div",{className:"review-item full-width",children:[(0,a.jsx)("span",{className:"review-label",children:"Highlights:"}),(0,a.jsx)("ul",{className:"review-highlights",children:N.highlights.filter(e=>e.trim()).map((e,t)=>(0,a.jsxs)("li",{className:"review-highlight",children:[(0,a.jsx)("span",{className:"highlight-bullet"}),e]},t))})]})]})]})]}),(0,a.jsxs)("div",{className:"flex justify-between pt-4",children:[(0,a.jsxs)("button",{type:"button",onClick:R,className:"flex items-center gap-2 px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50",children:[(0,a.jsx)(l.Tsu,{size:16}),"Back"]}),(0,a.jsx)("button",{type:"button",onClick:A,disabled:g,className:"px-6 py-2 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-md hover:opacity-90 ".concat(g?"opacity-70 cursor-not-allowed":""),style:{fontSize:"14px",lineHeight:"21px",fontFamily:"'SF Pro', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif"},children:g?s?"Updating Plan...":"Creating Plan...":s?"Update Plan":"Create Plan"})]})]})},L=()=>(0,a.jsxs)("div",{className:"jsx-756eb827991f8e8b space-y-6",children:[(0,a.jsx)(r(),{id:"756eb827991f8e8b",children:"input.jsx-756eb827991f8e8b,select.jsx-756eb827991f8e8b,textarea.jsx-756eb827991f8e8b{background-color:white!important;color:#374151!important}input.jsx-756eb827991f8e8b::-webkit-input-placeholder{color:#9ca3af!important}input.jsx-756eb827991f8e8b:-moz-placeholder{color:#9ca3af!important}input.jsx-756eb827991f8e8b::-moz-placeholder{color:#9ca3af!important}input.jsx-756eb827991f8e8b:-ms-input-placeholder{color:#9ca3af!important}input.jsx-756eb827991f8e8b::-ms-input-placeholder{color:#9ca3af!important}input.jsx-756eb827991f8e8b::placeholder{color:#9ca3af!important}"}),(0,a.jsxs)("div",{className:"jsx-756eb827991f8e8b flex items-center gap-3 mb-6",children:[(0,a.jsx)(l.vrJ,{size:20,className:"text-purple-600"}),(0,a.jsx)("h3",{style:{fontSize:"14px",fontWeight:"500",lineHeight:"21px",color:"#374151"},className:"jsx-756eb827991f8e8b",children:"Documents Upload"})]}),(0,a.jsxs)("div",{className:"jsx-756eb827991f8e8b space-y-6",children:[(0,a.jsxs)("div",{className:"jsx-756eb827991f8e8b",children:[(0,a.jsxs)("label",{style:{fontSize:"14px",fontWeight:"500",lineHeight:"21px"},className:"jsx-756eb827991f8e8b block text-gray-900 mb-2",children:["Plan Documents (Optional)",(0,a.jsx)("span",{"data-tooltip":"Upload documents related to this plan such as brochures, benefit summaries, or plan details (PDF, DOC, TXT, or Image files)",className:"jsx-756eb827991f8e8b tooltip-icon"})]}),(0,a.jsxs)("div",{className:"jsx-756eb827991f8e8b file-upload-area",children:[(0,a.jsx)("input",{type:"file",multiple:!0,accept:".pdf,.doc,.docx,.txt,.jpg,.jpeg,.png,.gif",onChange:e=>T(e.target.files),className:"jsx-756eb827991f8e8b file-input"}),(0,a.jsxs)("div",{className:"jsx-756eb827991f8e8b file-upload-label",children:[(0,a.jsx)(l.qX3,{size:48,className:"text-gray-400"}),(0,a.jsx)("span",{style:{fontSize:"14px",fontWeight:"500",lineHeight:"21px"},className:"jsx-756eb827991f8e8b",children:"Click to upload or drag and drop"}),(0,a.jsx)("small",{style:{fontSize:"12px",fontWeight:"400",lineHeight:"18px"},className:"jsx-756eb827991f8e8b text-gray-500",children:"PDF, DOC, TXT, or Image files (Max 10MB each)"})]})]})]}),N.documents.length>0&&(0,a.jsxs)("div",{className:"jsx-756eb827991f8e8b uploaded-files",children:[(0,a.jsx)("h4",{style:{fontSize:"14px",fontWeight:"500",lineHeight:"21px"},className:"jsx-756eb827991f8e8b",children:"Uploaded Files"}),N.documents.map((e,t)=>(0,a.jsxs)("div",{className:"jsx-756eb827991f8e8b uploaded-file",children:[(0,a.jsx)(l.vrJ,{size:16,className:"text-gray-500"}),(0,a.jsx)("span",{style:{fontSize:"14px",fontWeight:"400",lineHeight:"21px"},className:"jsx-756eb827991f8e8b file-name",children:e.name}),(0,a.jsxs)("span",{style:{fontSize:"12px",fontWeight:"400",lineHeight:"18px"},className:"jsx-756eb827991f8e8b file-size",children:[(e.size/1024/1024).toFixed(2)," MB"]}),(0,a.jsx)("button",{onClick:()=>P(t),title:"Remove file",className:"jsx-756eb827991f8e8b remove-file",children:(0,a.jsx)(l.fMW,{size:12})})]},t))]})]}),(0,a.jsx)("div",{className:"jsx-756eb827991f8e8b flex justify-end",children:(0,a.jsx)("button",{type:"button",onClick:F,style:{fontSize:"14px",lineHeight:"21px",fontFamily:"'SF Pro', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif"},className:"jsx-756eb827991f8e8b px-4 py-2 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-md hover:opacity-90",children:"Continue to Basic Info"})})]}),q=e=>{switch(e){case 1:default:return(0,a.jsx)(l.vrJ,{});case 2:return(0,a.jsx)(o.kAf,{});case 3:return(0,a.jsx)(o.jNQ,{});case 4:return(0,a.jsx)(o.GH$,{})}};return(0,a.jsxs)("div",{style:{fontSize:"14px",lineHeight:"21px",color:"#374151",fontFamily:"'SF Pro', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif"},className:"jsx-71927067afdf6a49 bg-white space-y-6",children:[(0,a.jsx)(r(),{id:"71927067afdf6a49",children:'.text-gray-900.jsx-71927067afdf6a49{color:#374151!important}input.jsx-71927067afdf6a49,select.jsx-71927067afdf6a49,textarea.jsx-71927067afdf6a49{color:#374151!important}.file-upload-area.jsx-71927067afdf6a49{position:relative;border:2px dashed#d1d5db;-webkit-border-radius:.75rem;-moz-border-radius:.75rem;border-radius:.75rem;padding:2rem;text-align:center;-webkit-transition:all.2s ease;-moz-transition:all.2s ease;-o-transition:all.2s ease;transition:all.2s ease;background:#fafafa}.file-upload-area.jsx-71927067afdf6a49:hover{border-color:#8b5cf6;background:#f8fafc}.file-input.jsx-71927067afdf6a49{position:absolute;inset:0;width:100%;height:100%;opacity:0;cursor:pointer}.file-upload-label.jsx-71927067afdf6a49{display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-orient:vertical;-webkit-box-direction:normal;-webkit-flex-direction:column;-moz-box-orient:vertical;-moz-box-direction:normal;-ms-flex-direction:column;flex-direction:column;-webkit-box-align:center;-webkit-align-items:center;-moz-box-align:center;-ms-flex-align:center;align-items:center;gap:.5rem;pointer-events:none}.uploaded-files.jsx-71927067afdf6a49{margin-top:1rem}.uploaded-file.jsx-71927067afdf6a49{display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-align:center;-webkit-align-items:center;-moz-box-align:center;-ms-flex-align:center;align-items:center;gap:.5rem;padding:.5rem;background:#f9fafb;border:1px solid#e5e7eb;-webkit-border-radius:.5rem;-moz-border-radius:.5rem;border-radius:.5rem;margin-bottom:.5rem}.file-name.jsx-71927067afdf6a49{-webkit-box-flex:1;-webkit-flex:1;-moz-box-flex:1;-ms-flex:1;flex:1}.file-size.jsx-71927067afdf6a49{color:#6b7280}.remove-file.jsx-71927067afdf6a49{padding:.25rem;background:#ef4444;color:white;border:none;-webkit-border-radius:.25rem;-moz-border-radius:.25rem;border-radius:.25rem;cursor:pointer}.input-with-prefix.jsx-71927067afdf6a49{position:relative}.input-prefix.jsx-71927067afdf6a49{position:absolute;left:.75rem;top:50%;-webkit-transform:translatey(-50%);-moz-transform:translatey(-50%);-ms-transform:translatey(-50%);-o-transform:translatey(-50%);transform:translatey(-50%);z-index:1}.input-with-prefix.jsx-71927067afdf6a49 input.jsx-71927067afdf6a49{padding-left:2.5rem}.form-section.jsx-71927067afdf6a49{background:white}.form-header.jsx-71927067afdf6a49{display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-align:center;-webkit-align-items:center;-moz-box-align:center;-ms-flex-align:center;align-items:center;-webkit-box-pack:justify;-webkit-justify-content:space-between;-moz-box-pack:justify;-ms-flex-pack:justify;justify-content:space-between;margin-bottom:1.5rem}.form-header-content.jsx-71927067afdf6a49{display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-align:center;-webkit-align-items:center;-moz-box-align:center;-ms-flex-align:center;align-items:center;gap:.75rem;color:#8b5cf6}.status-badge.jsx-71927067afdf6a49{display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-align:center;-webkit-align-items:center;-moz-box-align:center;-ms-flex-align:center;align-items:center;gap:.5rem;padding:.375rem .75rem;-webkit-border-radius:.5rem;-moz-border-radius:.5rem;border-radius:.5rem;font-size:.875rem;font-weight:500}.status-badge.ready.jsx-71927067afdf6a49{background:#dcfce7;color:#166534}.review-content.jsx-71927067afdf6a49{display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-orient:vertical;-webkit-box-direction:normal;-webkit-flex-direction:column;-moz-box-orient:vertical;-moz-box-direction:normal;-ms-flex-direction:column;flex-direction:column;gap:1.5rem}.review-section.jsx-71927067afdf6a49{background:#f8fafc;-webkit-border-radius:1rem;-moz-border-radius:1rem;border-radius:1rem;padding:1.5rem;border:1px solid#e2e8f0;-webkit-transition:all.3s ease;-moz-transition:all.3s ease;-o-transition:all.3s ease;transition:all.3s ease}.review-section.jsx-71927067afdf6a49:hover{-webkit-box-shadow:0 2px 8px rgba(0,0,0,.05);-moz-box-shadow:0 2px 8px rgba(0,0,0,.05);box-shadow:0 2px 8px rgba(0,0,0,.05)}.review-section-header.jsx-71927067afdf6a49{display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-align:center;-webkit-align-items:center;-moz-box-align:center;-ms-flex-align:center;align-items:center;gap:.75rem;margin-bottom:1.25rem;color:#3b82f6}.review-section-header.jsx-71927067afdf6a49 h4.jsx-71927067afdf6a49{font-size:1.125rem;font-weight:600;color:#1e293b;margin:0;font-family:"SF Pro",-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,sans-serif}.review-items.jsx-71927067afdf6a49{display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-orient:vertical;-webkit-box-direction:normal;-webkit-flex-direction:column;-moz-box-orient:vertical;-moz-box-direction:normal;-ms-flex-direction:column;flex-direction:column;gap:.75rem}.review-item.jsx-71927067afdf6a49{display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-pack:justify;-webkit-justify-content:space-between;-moz-box-pack:justify;-ms-flex-pack:justify;justify-content:space-between;-webkit-box-align:center;-webkit-align-items:center;-moz-box-align:center;-ms-flex-align:center;align-items:center}.review-item.full-width.jsx-71927067afdf6a49{-webkit-box-orient:vertical;-webkit-box-direction:normal;-webkit-flex-direction:column;-moz-box-orient:vertical;-moz-box-direction:normal;-ms-flex-direction:column;flex-direction:column;-webkit-box-align:start;-webkit-align-items:flex-start;-moz-box-align:start;-ms-flex-align:start;align-items:flex-start;gap:.5rem}.review-label.jsx-71927067afdf6a49{font-size:.875rem;color:#64748b;font-weight:500}.review-value.jsx-71927067afdf6a49{font-size:.875rem;color:#1e293b;font-weight:400}.review-link.jsx-71927067afdf6a49{color:#3b82f6;text-decoration:none;font-weight:500;word-break:break-all}.review-link.jsx-71927067afdf6a49:hover{text-decoration:underline}.review-documents.jsx-71927067afdf6a49{display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-orient:vertical;-webkit-box-direction:normal;-webkit-flex-direction:column;-moz-box-orient:vertical;-moz-box-direction:normal;-ms-flex-direction:column;flex-direction:column;gap:.5rem;margin-top:.5rem}.review-document.jsx-71927067afdf6a49{display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-align:center;-webkit-align-items:center;-moz-box-align:center;-ms-flex-align:center;align-items:center;gap:.5rem;padding:.5rem;background:white;border:1px solid#e5e7eb;-webkit-border-radius:.5rem;-moz-border-radius:.5rem;border-radius:.5rem}.review-document.jsx-71927067afdf6a49 span.jsx-71927067afdf6a49{font-size:.875rem;color:#374151;font-weight:500}.review-document.jsx-71927067afdf6a49 small.jsx-71927067afdf6a49{font-size:.75rem;color:#6b7280;margin-left:auto}.review-highlights.jsx-71927067afdf6a49{list-style:none;padding:0;margin:.5rem 0 0 0;display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-orient:vertical;-webkit-box-direction:normal;-webkit-flex-direction:column;-moz-box-orient:vertical;-moz-box-direction:normal;-ms-flex-direction:column;flex-direction:column;gap:.5rem}.review-highlight.jsx-71927067afdf6a49{display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-align:center;-webkit-align-items:center;-moz-box-align:center;-ms-flex-align:center;align-items:center;gap:.5rem;font-size:.875rem;color:#374151}.highlight-bullet.jsx-71927067afdf6a49{width:.375rem;height:.375rem;background:#8b5cf6;-webkit-border-radius:50%;-moz-border-radius:50%;border-radius:50%;-webkit-flex-shrink:0;-ms-flex-negative:0;flex-shrink:0}.page-nav-item.completed.jsx-71927067afdf6a49::after{content:"✓";display:-webkit-inline-box;display:-webkit-inline-flex;display:-moz-inline-box;display:-ms-inline-flexbox;display:inline-flex;-webkit-box-align:center;-webkit-align-items:center;-moz-box-align:center;-ms-flex-align:center;align-items:center;-webkit-box-pack:center;-webkit-justify-content:center;-moz-box-pack:center;-ms-flex-pack:center;justify-content:center;width:16px;height:16px;background:#7c3aed;-webkit-border-radius:50%;-moz-border-radius:50%;border-radius:50%;margin-left:6px;font-size:10px;font-weight:700;color:white;line-height:1;-webkit-flex-shrink:0;-ms-flex-negative:0;flex-shrink:0}'}),(0,a.jsx)("div",{className:"jsx-71927067afdf6a49 flex gap-3 mb-6",children:C.map(e=>(0,a.jsxs)("button",{style:{fontSize:"13px",lineHeight:"1.2",fontWeight:"450",fontFamily:"'SF Pro', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif",display:"inline-flex",alignItems:"center",gap:"6px",padding:"8px 16px",background:e.completed?"#ddd6fe":e.active?"#ede9fe":"#f3f4f6",border:"none",borderRadius:"24px",cursor:e.number<=u?"pointer":"not-allowed",color:e.completed?"#6d28d9":e.active?"#7c3aed":"#6b7280",transition:"all 0.2s ease",whiteSpace:"nowrap",minWidth:"fit-content",position:"relative"},onClick:()=>e.number<=u&&m(e.number),disabled:e.number>u,className:"jsx-71927067afdf6a49 "+"page-nav-item ".concat(e.completed?"completed":e.active?"active":""),children:[(0,a.jsx)("span",{className:"jsx-71927067afdf6a49 text-base",children:q(e.number)}),e.title]},e.number))}),(()=>{switch(u){case 1:default:return L();case 2:return W();case 3:return U();case 4:return B()}})(),f&&(0,a.jsxs)("div",{className:"jsx-71927067afdf6a49 fixed bottom-4 right-4 bg-gray-900 text-white p-4 rounded-lg shadow-lg flex items-center gap-3",children:[(0,a.jsx)(l.Moc,{size:20,className:"animate-spin"}),(0,a.jsxs)("div",{className:"jsx-71927067afdf6a49",children:[(0,a.jsx)("h4",{className:"jsx-71927067afdf6a49 font-medium",children:"AI Assist Applied"}),(0,a.jsx)("p",{className:"jsx-71927067afdf6a49 text-sm opacity-90",children:"Plan details have been pre-filled for you."})]})]})]})})},43010:function(){}}]);