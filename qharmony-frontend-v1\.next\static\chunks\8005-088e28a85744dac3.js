"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8005],{98005:function(e,t,l){l.r(t);var i=l(57437),a=l(2265),n=l(68575),s=l(95656),d=l(53392),r=l(79507),o=l(59832),u=l(77468),x=l(64461),h=l(28450),c=l(89414),v=l(97404),m=l(41327),g=l(1037),p=l(16557),f=l(42187),j=l(46387),Z=l(8350),b=l(94013),S=l(67208),C=l(36137),y=l(67571),P=l(71586),W=l(35389),N=l(9026),D=l(8430),k=l(29821),A=l(29886),w=l(54862),O=l(76672),I=l(39547);function T(e){let{children:t,value:l,index:a,...n}=e;return(0,i.jsx)("div",{role:"tabpanel",hidden:l!==a,id:"profile-tabpanel-".concat(a),"aria-labelledby":"profile-tab-".concat(a),...n,children:l===a&&(0,i.jsx)(s.Z,{sx:{p:3},children:t})})}t.default=e=>{let{open:t,onClose:l}=e,z=(0,n.I0)(),_=(0,n.v9)(e=>e.user._id),B=(0,n.v9)(e=>e.user.userProfile),[E,X]=(0,a.useState)(0),[F,R]=(0,a.useState)(""),[L,q]=(0,a.useState)(""),[U,H]=(0,a.useState)(""),[M,G]=(0,a.useState)(""),[J,Y]=(0,a.useState)(""),[K,Q]=(0,a.useState)(""),[V,$]=(0,a.useState)(""),[ee,et]=(0,a.useState)(""),[el,ei]=(0,a.useState)(""),[ea,en]=(0,a.useState)(""),[es,ed]=(0,a.useState)(""),[er,eo]=(0,a.useState)(""),[eu,ex]=(0,a.useState)(""),[eh,ec]=(0,a.useState)(""),[ev,em]=(0,a.useState)(""),[eg,ep]=(0,a.useState)(""),[ef,ej]=(0,a.useState)(""),[eZ,eb]=(0,a.useState)(""),[eS,eC]=(0,a.useState)("US"),[ey,eP]=(0,a.useState)(""),[eW,eN]=(0,a.useState)(""),[eD,ek]=(0,a.useState)(""),[eA,ew]=(0,a.useState)(""),[eO,eI]=(0,a.useState)(""),[eT,ez]=(0,a.useState)("US"),[e_,eB]=(0,a.useState)(""),[eE,eX]=(0,a.useState)(""),[eF,eR]=(0,a.useState)(""),[eL,eq]=(0,a.useState)(""),[eU,eH]=(0,a.useState)(""),[eM,eG]=(0,a.useState)([]),[eJ,eY]=(0,a.useState)(!1),[eK,eQ]=(0,a.useState)(null),[eV,e$]=(0,a.useState)({firstName:"",middleName:"",lastName:"",gender:"",dateOfBirth:"",relationship:"",ssn:"",isStudent:!1,isDisabled:!1,isActive:!0}),[e0,e2]=(0,a.useState)(""),[e1,e3]=(0,a.useState)(!1),e6=e=>{if(eQ(e),!e.firstName&&e.name){let t=e.name.split(" "),l=t[0]||"",i=t.length>1?t[t.length-1]:"",a=t.length>2?t.slice(1,-1).join(" "):"";e$({...e,firstName:l,middleName:a,lastName:i})}else e$({...e});eY(!0)},e5=e=>{window.confirm("Are you sure you want to remove this dependent?")&&eG(t=>t.filter(t=>t._id!==e))};(0,a.useEffect)(()=>{if(B){var e,t,l,i,a,n,s,d,r,o,u,x,h,c,v,m,g,p,f,j,Z,b,S,C,y,P,W,N,D,k,A,w,O,I,T,z,_,E,X,F,L,U,M,J,K;let V=B.name.split(" ");if(R(V[0]||""),q(V.slice(1).join(" ")||""),H(B.email||""),G((null===(e=B.details)||void 0===e?void 0:e.phoneNumber)||""),Y((null===(t=B.details)||void 0===t?void 0:t.department)||""),Q((null===(l=B.details)||void 0===l?void 0:l.title)||""),$((null===(i=B.details)||void 0===i?void 0:i.dateOfBirth)?new Date(B.details.dateOfBirth).toISOString().split("T")[0]:""),et((null===(a=B.details)||void 0===a?void 0:a.hireDate)?new Date(B.details.hireDate).toISOString().split("T")[0]:""),ei((null===(s=B.details)||void 0===s?void 0:null===(n=s.annualSalary)||void 0===n?void 0:n.toString())||""),en((null===(d=B.details)||void 0===d?void 0:d.employeeClassType)||""),ed((null===(r=B.details)||void 0===r?void 0:r.workSchedule)||""),eo((null===(o=B.details)||void 0===o?void 0:o.ssn)||""),ex((null===(u=B.details)||void 0===u?void 0:u.employeeId)||""),ec((null===(h=B.details)||void 0===h?void 0:null===(x=h.address)||void 0===x?void 0:x.street1)||""),em((null===(v=B.details)||void 0===v?void 0:null===(c=v.address)||void 0===c?void 0:c.street2)||""),ep((null===(g=B.details)||void 0===g?void 0:null===(m=g.address)||void 0===m?void 0:m.city)||""),ej((null===(f=B.details)||void 0===f?void 0:null===(p=f.address)||void 0===p?void 0:p.state)||""),eb((null===(Z=B.details)||void 0===Z?void 0:null===(j=Z.address)||void 0===j?void 0:j.zipCode)||""),eC((null===(S=B.details)||void 0===S?void 0:null===(b=S.address)||void 0===b?void 0:b.country)||"US"),eP((null===(y=B.details)||void 0===y?void 0:null===(C=y.mailingAddress)||void 0===C?void 0:C.street1)||""),eN((null===(W=B.details)||void 0===W?void 0:null===(P=W.mailingAddress)||void 0===P?void 0:P.street2)||""),ek((null===(D=B.details)||void 0===D?void 0:null===(N=D.mailingAddress)||void 0===N?void 0:N.city)||""),ew((null===(A=B.details)||void 0===A?void 0:null===(k=A.mailingAddress)||void 0===k?void 0:k.state)||""),eI((null===(O=B.details)||void 0===O?void 0:null===(w=O.mailingAddress)||void 0===w?void 0:w.zipCode)||""),ez((null===(T=B.details)||void 0===T?void 0:null===(I=T.mailingAddress)||void 0===I?void 0:I.country)||"US"),eB((null===(_=B.details)||void 0===_?void 0:null===(z=_.emergencyContact)||void 0===z?void 0:z.name)||""),eX((null===(X=B.details)||void 0===X?void 0:null===(E=X.emergencyContact)||void 0===E?void 0:E.relationship)||""),eR((null===(L=B.details)||void 0===L?void 0:null===(F=L.emergencyContact)||void 0===F?void 0:F.phoneNumber)||""),eq((null===(M=B.details)||void 0===M?void 0:null===(U=M.emergencyContact)||void 0===U?void 0:U.email)||""),eH((null===(J=B.details)||void 0===J?void 0:J.workLocation)||""),(null===(K=B.details)||void 0===K?void 0:K.dependents)&&Array.isArray(B.details.dependents)){let e=B.details.dependents.map((e,t)=>{let l=(e.name||"").split(" "),i=l[0]||"",a=l.length>1?l[l.length-1]:"",n=l.length>2?l.slice(1,-1).join(" "):"";return{_id:e._id||"existing_".concat(t,"_").concat(Date.now()),firstName:i,middleName:n,lastName:a,gender:e.gender||"",dateOfBirth:e.dateOfBirth?new Date(e.dateOfBirth).toISOString().split("T")[0]:"",relationship:e.relationship||"",ssn:e.ssn||"",isStudent:e.isStudent||!1,isDisabled:e.isDisabled||!1,isActive:!1!==e.isActive}});console.log("Loading existing dependents:",e),eG(e)}else console.log("No existing dependents found or dependents is not an array"),eG([])}},[B]);let e7=async()=>{var e,t,i,a,n,s,d;e3(!0);try{let t={name:"".concat(F," ").concat(L).trim(),email:U,phoneNumber:M||"",department:J||"",title:K||""};V&&(t.dateOfBirth=new Date(V).toISOString()),ee&&(t.hireDate=new Date(ee).toISOString()),el&&!isNaN(parseFloat(el))&&(t.annualSalary=parseFloat(el)),ea&&(t.employeeClassType=ea),es&&(t.workSchedule=es),er&&(t.ssn=er),eu&&(t.employeeId=eu),eU&&(t.workLocation=eU),eh&&eg&&ef&&eZ&&(t.address={street1:eh,city:eg,state:ef,zipCode:eZ,country:eS||"US"},ev&&(t.address.street2=ev)),ey&&eD&&eA&&eO&&(t.mailingAddress={street1:ey,city:eD,state:eA,zipCode:eO,country:eT||"US"},eW&&(t.mailingAddress.street2=eW)),e_&&(t.emergencyContact={name:e_},eE&&(t.emergencyContact.relationship=eE),eF&&(t.emergencyContact.phoneNumber=eF),eL&&(t.emergencyContact.email=eL)),t.dependents=eM.map(e=>({_id:e._id&&!e._id.toString().startsWith("temp_")?e._id:void 0,name:"".concat(e.firstName," ").concat(e.middleName?e.middleName+" ":"").concat(e.lastName).trim(),gender:e.gender,dateOfBirth:new Date(e.dateOfBirth).toISOString(),relationship:e.relationship,ssn:e.ssn||void 0,isStudent:e.isStudent||!1,isDisabled:e.isDisabled||!1,isActive:!1!==e.isActive})),console.log("Dependents being sent:",t.dependents),console.log("Current dependents state:",eM),console.log("Sending update request with data:",t),console.log("Dependents in state before sending:",eM),console.log("Formatted dependents for backend:",t.dependents);let i=await (0,I.Nq)(z,_,t);if(i&&200===i.status)await (0,I.M_)(z,_),e2("Profile updated successfully!"),setTimeout(()=>{e2(""),l()},1500);else throw Error((null==i?void 0:null===(e=i.data)||void 0===e?void 0:e.error)||"Update failed")}catch(l){console.error("Profile update error:",l),console.error("Error response:",null===(t=l.response)||void 0===t?void 0:t.data),console.error("Error status:",null===(i=l.response)||void 0===i?void 0:i.status);let e="Error updating profile. Please try again.";(null===(n=l.response)||void 0===n?void 0:null===(a=n.data)||void 0===a?void 0:a.message)?e=l.response.data.message:(null===(s=l.response)||void 0===s?void 0:s.status)===400?e="Invalid data format. Please check all required fields.":(null===(d=l.response)||void 0===d?void 0:d.status)===403&&(e="You don't have permission to edit this profile."),e2(e),setTimeout(()=>{e2("")},3e3)}finally{e3(!1)}};return(0,i.jsxs)(d.Z,{open:t,onClose:l,maxWidth:"md",fullWidth:!0,PaperProps:{style:{borderRadius:"16px",boxShadow:"0px 10px 30px rgba(0, 0, 0, 0.1)",padding:"5px",maxHeight:"90vh"}},children:[(0,i.jsxs)(r.Z,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",fontWeight:"bold",fontSize:"1.5rem",pb:1},children:["Edit Profile",(0,i.jsx)(o.Z,{onClick:l,children:(0,i.jsx)(D.Z,{})})]}),(0,i.jsxs)(u.Z,{sx:{p:0},children:[(0,i.jsx)(s.Z,{sx:{borderBottom:1,borderColor:"divider"},children:(0,i.jsxs)(x.Z,{value:E,onChange:(e,t)=>{X(t)},"aria-label":"profile tabs",children:[(0,i.jsx)(h.Z,{label:"Basic Info"}),(0,i.jsx)(h.Z,{label:"Employment"}),(0,i.jsx)(h.Z,{label:"Address"}),(0,i.jsx)(h.Z,{label:"Emergency Contact"}),(0,i.jsx)(h.Z,{label:"Dependents"})]})}),(0,i.jsx)(T,{value:E,index:0,children:(0,i.jsxs)(c.ZP,{container:!0,spacing:3,children:[(0,i.jsx)(c.ZP,{item:!0,xs:12,sm:6,children:(0,i.jsx)(v.Z,{fullWidth:!0,label:"First Name",value:F,onChange:e=>R(e.target.value),variant:"outlined",sx:{mb:2}})}),(0,i.jsx)(c.ZP,{item:!0,xs:12,sm:6,children:(0,i.jsx)(v.Z,{fullWidth:!0,label:"Last Name",value:L,onChange:e=>q(e.target.value),variant:"outlined",sx:{mb:2}})}),(0,i.jsx)(c.ZP,{item:!0,xs:12,children:(0,i.jsx)(v.Z,{fullWidth:!0,label:"Email",value:U,onChange:e=>H(e.target.value),variant:"outlined",sx:{mb:2}})}),(0,i.jsx)(c.ZP,{item:!0,xs:12,sm:6,children:(0,i.jsx)(v.Z,{fullWidth:!0,label:"Phone Number",value:M,onChange:e=>G(e.target.value),variant:"outlined",sx:{mb:2}})}),(0,i.jsx)(c.ZP,{item:!0,xs:12,sm:6,children:(0,i.jsx)(v.Z,{fullWidth:!0,label:"Department",value:J,onChange:e=>Y(e.target.value),variant:"outlined",sx:{mb:2}})}),(0,i.jsx)(c.ZP,{item:!0,xs:12,children:(0,i.jsx)(v.Z,{fullWidth:!0,label:"Job Title",value:K,onChange:e=>Q(e.target.value),variant:"outlined",sx:{mb:2}})}),(0,i.jsx)(c.ZP,{item:!0,xs:12,sm:6,children:(0,i.jsx)(v.Z,{fullWidth:!0,label:"Date of Birth *",type:"date",value:V,onChange:e=>$(e.target.value),variant:"outlined",InputLabelProps:{shrink:!0},sx:{mb:2},required:!0})}),(0,i.jsx)(c.ZP,{item:!0,xs:12,sm:6,children:(0,i.jsx)(v.Z,{fullWidth:!0,label:"SSN",value:er,onChange:e=>eo(e.target.value),variant:"outlined",placeholder:"XXX-XX-XXXX",sx:{mb:2}})})]})}),(0,i.jsx)(T,{value:E,index:1,children:(0,i.jsxs)(c.ZP,{container:!0,spacing:3,children:[(0,i.jsx)(c.ZP,{item:!0,xs:12,sm:6,children:(0,i.jsx)(v.Z,{fullWidth:!0,label:"Employee ID",value:eu,onChange:e=>ex(e.target.value),variant:"outlined",sx:{mb:2}})}),(0,i.jsx)(c.ZP,{item:!0,xs:12,sm:6,children:(0,i.jsx)(v.Z,{fullWidth:!0,label:"Hire Date *",type:"date",value:ee,onChange:e=>et(e.target.value),variant:"outlined",InputLabelProps:{shrink:!0},sx:{mb:2},required:!0})}),(0,i.jsx)(c.ZP,{item:!0,xs:12,sm:6,children:(0,i.jsx)(v.Z,{fullWidth:!0,label:"Annual Salary",type:"number",value:el,onChange:e=>ei(e.target.value),variant:"outlined",sx:{mb:2}})}),(0,i.jsx)(c.ZP,{item:!0,xs:12,sm:6,children:(0,i.jsxs)(m.Z,{fullWidth:!0,sx:{mb:2},required:!0,children:[(0,i.jsx)(g.Z,{children:"Employee Class Type *"}),(0,i.jsxs)(p.Z,{value:ea,onChange:e=>en(e.target.value),label:"Employee Class Type *",required:!0,children:[(0,i.jsx)(f.Z,{value:"Full-Time",children:"Full-Time"}),(0,i.jsx)(f.Z,{value:"Part-Time",children:"Part-Time"}),(0,i.jsx)(f.Z,{value:"Contractor",children:"Contractor"}),(0,i.jsx)(f.Z,{value:"Temporary",children:"Temporary"})]})]})}),(0,i.jsx)(c.ZP,{item:!0,xs:12,sm:6,children:(0,i.jsxs)(m.Z,{fullWidth:!0,sx:{mb:2},children:[(0,i.jsx)(g.Z,{children:"Work Schedule"}),(0,i.jsxs)(p.Z,{value:es,onChange:e=>ed(e.target.value),label:"Work Schedule",children:[(0,i.jsx)(f.Z,{value:"Full-Time",children:"Full-Time"}),(0,i.jsx)(f.Z,{value:"Part-Time",children:"Part-Time"}),(0,i.jsx)(f.Z,{value:"Remote",children:"Remote"}),(0,i.jsx)(f.Z,{value:"Hybrid",children:"Hybrid"})]})]})}),(0,i.jsx)(c.ZP,{item:!0,xs:12,sm:6,children:(0,i.jsx)(v.Z,{fullWidth:!0,label:"Work Location",value:eU,onChange:e=>eH(e.target.value),variant:"outlined",sx:{mb:2}})})]})}),(0,i.jsxs)(T,{value:E,index:2,children:[(0,i.jsx)(j.Z,{variant:"h6",sx:{mb:2,fontWeight:"bold"},children:"Primary Address"}),(0,i.jsxs)(c.ZP,{container:!0,spacing:3,children:[(0,i.jsx)(c.ZP,{item:!0,xs:12,children:(0,i.jsx)(v.Z,{fullWidth:!0,label:"Street Address",value:eh,onChange:e=>ec(e.target.value),variant:"outlined",sx:{mb:2}})}),(0,i.jsx)(c.ZP,{item:!0,xs:12,children:(0,i.jsx)(v.Z,{fullWidth:!0,label:"Apartment, Suite, Unit (Optional)",value:ev,onChange:e=>em(e.target.value),variant:"outlined",sx:{mb:2}})}),(0,i.jsx)(c.ZP,{item:!0,xs:12,sm:6,children:(0,i.jsx)(v.Z,{fullWidth:!0,label:"City",value:eg,onChange:e=>ep(e.target.value),variant:"outlined",sx:{mb:2}})}),(0,i.jsx)(c.ZP,{item:!0,xs:12,sm:3,children:(0,i.jsx)(v.Z,{fullWidth:!0,label:"State",value:ef,onChange:e=>ej(e.target.value),variant:"outlined",sx:{mb:2}})}),(0,i.jsx)(c.ZP,{item:!0,xs:12,sm:3,children:(0,i.jsx)(v.Z,{fullWidth:!0,label:"ZIP Code",value:eZ,onChange:e=>eb(e.target.value),variant:"outlined",sx:{mb:2}})})]}),(0,i.jsx)(Z.Z,{sx:{my:3}}),(0,i.jsx)(j.Z,{variant:"h6",sx:{mb:2,fontWeight:"bold"},children:"Mailing Address (if different)"}),(0,i.jsxs)(c.ZP,{container:!0,spacing:3,children:[(0,i.jsx)(c.ZP,{item:!0,xs:12,children:(0,i.jsx)(v.Z,{fullWidth:!0,label:"Mailing Street Address",value:ey,onChange:e=>eP(e.target.value),variant:"outlined",sx:{mb:2}})}),(0,i.jsx)(c.ZP,{item:!0,xs:12,children:(0,i.jsx)(v.Z,{fullWidth:!0,label:"Apartment, Suite, Unit (Optional)",value:eW,onChange:e=>eN(e.target.value),variant:"outlined",sx:{mb:2}})}),(0,i.jsx)(c.ZP,{item:!0,xs:12,sm:6,children:(0,i.jsx)(v.Z,{fullWidth:!0,label:"City",value:eD,onChange:e=>ek(e.target.value),variant:"outlined",sx:{mb:2}})}),(0,i.jsx)(c.ZP,{item:!0,xs:12,sm:3,children:(0,i.jsx)(v.Z,{fullWidth:!0,label:"State",value:eA,onChange:e=>ew(e.target.value),variant:"outlined",sx:{mb:2}})}),(0,i.jsx)(c.ZP,{item:!0,xs:12,sm:3,children:(0,i.jsx)(v.Z,{fullWidth:!0,label:"ZIP Code",value:eO,onChange:e=>eI(e.target.value),variant:"outlined",sx:{mb:2}})})]})]}),(0,i.jsx)(T,{value:E,index:3,children:(0,i.jsxs)(c.ZP,{container:!0,spacing:3,children:[(0,i.jsx)(c.ZP,{item:!0,xs:12,sm:6,children:(0,i.jsx)(v.Z,{fullWidth:!0,label:"Emergency Contact Name",value:e_,onChange:e=>eB(e.target.value),variant:"outlined",sx:{mb:2}})}),(0,i.jsx)(c.ZP,{item:!0,xs:12,sm:6,children:(0,i.jsxs)(m.Z,{fullWidth:!0,sx:{mb:2},children:[(0,i.jsx)(g.Z,{children:"Relationship"}),(0,i.jsxs)(p.Z,{value:eE,onChange:e=>eX(e.target.value),label:"Relationship",children:[(0,i.jsx)(f.Z,{value:"Spouse",children:"Spouse"}),(0,i.jsx)(f.Z,{value:"Parent",children:"Parent"}),(0,i.jsx)(f.Z,{value:"Child",children:"Child"}),(0,i.jsx)(f.Z,{value:"Sibling",children:"Sibling"}),(0,i.jsx)(f.Z,{value:"Friend",children:"Friend"}),(0,i.jsx)(f.Z,{value:"Other",children:"Other"})]})]})}),(0,i.jsx)(c.ZP,{item:!0,xs:12,sm:6,children:(0,i.jsx)(v.Z,{fullWidth:!0,label:"Emergency Contact Phone",value:eF,onChange:e=>eR(e.target.value),variant:"outlined",sx:{mb:2}})}),(0,i.jsx)(c.ZP,{item:!0,xs:12,sm:6,children:(0,i.jsx)(v.Z,{fullWidth:!0,label:"Emergency Contact Email",value:eL,onChange:e=>eq(e.target.value),variant:"outlined",sx:{mb:2}})})]})}),(0,i.jsxs)(T,{value:E,index:4,children:[(0,i.jsxs)(s.Z,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",mb:3},children:[(0,i.jsx)(j.Z,{variant:"h6",sx:{fontWeight:"bold"},children:"Dependents"}),(0,i.jsx)(b.Z,{variant:"contained",startIcon:(0,i.jsx)(A.Z,{}),onClick:()=>{eQ(null),e$({firstName:"",middleName:"",lastName:"",gender:"",dateOfBirth:"",relationship:"",ssn:"",isStudent:!1,isDisabled:!1,isActive:!0}),eY(!0)},sx:{backgroundColor:"#000000",color:"#ffffff",borderRadius:"8px",textTransform:"none","&:hover":{backgroundColor:"#333333"}},children:"Add Dependent"})]}),(0,i.jsxs)(s.Z,{sx:{mb:3},children:[eM.map(e=>(0,i.jsx)(S.Z,{sx:{border:"1px solid #e0e0e0",borderRadius:"8px",mb:1.5,"&:hover":{boxShadow:"0 2px 8px rgba(0,0,0,0.1)",transition:"box-shadow 0.2s ease-in-out"}},children:(0,i.jsx)(C.Z,{sx:{p:2,"&:last-child":{pb:2}},children:(0,i.jsxs)(s.Z,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center"},children:[(0,i.jsxs)(s.Z,{sx:{display:"flex",alignItems:"center",flex:1},children:[(0,i.jsxs)(s.Z,{sx:{mr:4},children:[(0,i.jsx)(j.Z,{variant:"h6",sx:{fontWeight:"bold",mb:.25,fontSize:"1.1rem"},children:"".concat(e.firstName," ").concat(e.middleName?e.middleName+" ":"").concat(e.lastName).trim()}),(0,i.jsx)(j.Z,{variant:"body2",color:"text.secondary",sx:{fontSize:"0.875rem"},children:e.relationship})]}),(0,i.jsxs)(s.Z,{sx:{display:"flex",gap:3,alignItems:"center",flex:1},children:[(0,i.jsxs)(s.Z,{children:[(0,i.jsx)(j.Z,{variant:"caption",color:"text.secondary",sx:{display:"block",fontSize:"0.75rem",lineHeight:1.2},children:"Date of Birth"}),(0,i.jsx)(j.Z,{variant:"body2",sx:{fontWeight:500,fontSize:"0.875rem",mt:.25},children:new Date(e.dateOfBirth).toLocaleDateString()})]}),(0,i.jsxs)(s.Z,{children:[(0,i.jsx)(j.Z,{variant:"caption",color:"text.secondary",sx:{display:"block",fontSize:"0.75rem",lineHeight:1.2},children:"Gender"}),(0,i.jsx)(j.Z,{variant:"body2",sx:{fontWeight:500,fontSize:"0.875rem",mt:.25},children:e.gender})]}),(0,i.jsxs)(s.Z,{children:[(0,i.jsx)(j.Z,{variant:"caption",color:"text.secondary",sx:{display:"block",fontSize:"0.75rem",lineHeight:1.2},children:"Age"}),(0,i.jsxs)(j.Z,{variant:"body2",sx:{fontWeight:500,fontSize:"0.875rem",mt:.25},children:[Math.floor((new Date().getTime()-new Date(e.dateOfBirth).getTime())/31536e6)," years"]})]})]})]}),(0,i.jsxs)(s.Z,{sx:{display:"flex",alignItems:"center",gap:1.5},children:[(0,i.jsxs)(s.Z,{sx:{display:"flex",gap:.5},children:[e.isStudent&&(0,i.jsx)(y.Z,{label:"Student",size:"small",color:"primary",variant:"outlined",sx:{height:"24px",fontSize:"0.75rem"}}),e.isDisabled&&(0,i.jsx)(y.Z,{label:"Disabled",size:"small",color:"secondary",variant:"outlined",sx:{height:"24px",fontSize:"0.75rem"}}),(0,i.jsx)(y.Z,{label:e.isActive?"Active":"Inactive",size:"small",color:e.isActive?"success":"default",variant:e.isActive?"filled":"outlined",sx:{height:"24px",fontSize:"0.75rem"}})]}),(0,i.jsxs)(s.Z,{sx:{display:"flex",gap:.5},children:[(0,i.jsx)(o.Z,{size:"small",onClick:()=>e6(e),sx:{backgroundColor:"#f5f5f5",width:"32px",height:"32px","&:hover":{backgroundColor:"#e0e0e0"}},children:(0,i.jsx)(w.Z,{sx:{fontSize:"16px"}})}),(0,i.jsx)(o.Z,{size:"small",onClick:()=>e5(e._id),sx:{backgroundColor:"#ffebee",color:"#d32f2f",width:"32px",height:"32px","&:hover":{backgroundColor:"#ffcdd2"}},children:(0,i.jsx)(O.Z,{sx:{fontSize:"16px"}})})]})]})]})})},e._id)),0===eM.length&&(0,i.jsxs)(s.Z,{sx:{textAlign:"center",py:6,border:"2px dashed #e0e0e0",borderRadius:"12px",backgroundColor:"#fafafa"},children:[(0,i.jsx)(j.Z,{variant:"h6",color:"text.secondary",sx:{mb:1},children:"No dependents added yet"}),(0,i.jsx)(j.Z,{variant:"body2",color:"text.secondary",children:'Click "Add Dependent" to get started adding family members to your profile.'})]})]}),eJ&&(0,i.jsx)(s.Z,{sx:{position:"fixed",top:0,left:0,right:0,bottom:0,backgroundColor:"rgba(0, 0, 0, 0.5)",display:"flex",alignItems:"center",justifyContent:"center",zIndex:9999},children:(0,i.jsxs)(S.Z,{sx:{width:"90%",maxWidth:"600px",maxHeight:"90vh",overflow:"auto"},children:[(0,i.jsxs)(C.Z,{children:[(0,i.jsx)(j.Z,{variant:"h6",sx:{mb:3,fontWeight:"bold"},children:eK?"Edit Dependent":"Add New Dependent"}),(0,i.jsxs)(c.ZP,{container:!0,spacing:2,children:[(0,i.jsx)(c.ZP,{item:!0,xs:12,sm:4,children:(0,i.jsx)(v.Z,{fullWidth:!0,label:"First Name *",value:eV.firstName,onChange:e=>e$(t=>({...t,firstName:e.target.value})),variant:"outlined",required:!0})}),(0,i.jsx)(c.ZP,{item:!0,xs:12,sm:4,children:(0,i.jsx)(v.Z,{fullWidth:!0,label:"Middle Name (Optional)",value:eV.middleName,onChange:e=>e$(t=>({...t,middleName:e.target.value})),variant:"outlined"})}),(0,i.jsx)(c.ZP,{item:!0,xs:12,sm:4,children:(0,i.jsx)(v.Z,{fullWidth:!0,label:"Last Name *",value:eV.lastName,onChange:e=>e$(t=>({...t,lastName:e.target.value})),variant:"outlined",required:!0})}),(0,i.jsx)(c.ZP,{item:!0,xs:12,sm:6,children:(0,i.jsxs)(m.Z,{fullWidth:!0,children:[(0,i.jsx)(g.Z,{children:"Gender *"}),(0,i.jsxs)(p.Z,{value:eV.gender,onChange:e=>e$(t=>({...t,gender:e.target.value})),label:"Gender *",children:[(0,i.jsx)(f.Z,{value:"Male",children:"Male"}),(0,i.jsx)(f.Z,{value:"Female",children:"Female"}),(0,i.jsx)(f.Z,{value:"Other",children:"Other"}),(0,i.jsx)(f.Z,{value:"Prefer not to say",children:"Prefer not to say"})]})]})}),(0,i.jsx)(c.ZP,{item:!0,xs:12,sm:6,children:(0,i.jsx)(v.Z,{fullWidth:!0,label:"Date of Birth *",type:"date",value:eV.dateOfBirth,onChange:e=>e$(t=>({...t,dateOfBirth:e.target.value})),variant:"outlined",InputLabelProps:{shrink:!0}})}),(0,i.jsx)(c.ZP,{item:!0,xs:12,children:(0,i.jsxs)(m.Z,{fullWidth:!0,children:[(0,i.jsx)(g.Z,{children:"Relationship *"}),(0,i.jsxs)(p.Z,{value:eV.relationship,onChange:e=>e$(t=>({...t,relationship:e.target.value})),label:"Relationship *",children:[(0,i.jsx)(f.Z,{value:"Spouse",children:"Spouse"}),(0,i.jsx)(f.Z,{value:"Child",children:"Child"}),(0,i.jsx)(f.Z,{value:"Domestic Partner",children:"Domestic Partner"}),(0,i.jsx)(f.Z,{value:"Stepchild",children:"Stepchild"}),(0,i.jsx)(f.Z,{value:"Adopted Child",children:"Adopted Child"}),(0,i.jsx)(f.Z,{value:"Other",children:"Other"})]})]})}),(0,i.jsx)(c.ZP,{item:!0,xs:12,children:(0,i.jsx)(v.Z,{fullWidth:!0,label:"SSN (Optional)",value:eV.ssn,onChange:e=>e$(t=>({...t,ssn:e.target.value})),variant:"outlined",placeholder:"XXX-XX-XXXX"})})]})]}),(0,i.jsxs)(P.Z,{sx:{justifyContent:"flex-end",p:2},children:[(0,i.jsx)(b.Z,{onClick:()=>{eY(!1),eQ(null)},sx:{color:"#666",textTransform:"none"},children:"Cancel"}),(0,i.jsxs)(b.Z,{onClick:()=>{if(!eV.firstName||!eV.lastName||!eV.dateOfBirth||!eV.relationship){alert("Please fill in all required fields (First Name, Last Name, Date of Birth, Relationship)");return}if(eK)eG(e=>e.map(e=>e._id===eK._id?{...eV}:e));else{let e={...eV,_id:"temp_".concat(Date.now(),"_").concat(Math.random().toString(36).substr(2,9))};eG(t=>[...t,e])}eY(!1),eQ(null)},variant:"contained",sx:{backgroundColor:"#000000",color:"#ffffff",textTransform:"none","&:hover":{backgroundColor:"#333333"}},children:[eK?"Update":"Add"," Dependent"]})]})]})})]}),e1&&(0,i.jsx)(s.Z,{sx:{display:"flex",justifyContent:"center",p:2},children:(0,i.jsx)(W.Z,{})}),e0&&(0,i.jsxs)(s.Z,{sx:{color:"green",p:2,display:"flex",alignItems:"center",justifyContent:"center"},children:[(0,i.jsx)(k.Z,{sx:{mr:1}}),e0]})]}),(0,i.jsxs)(N.Z,{sx:{padding:"16px"},children:[(0,i.jsx)(b.Z,{onClick:l,sx:{color:"#666",borderRadius:"12px",padding:"8px 24px",textTransform:"none",fontWeight:"bold",mr:1},children:"Cancel"}),(0,i.jsx)(b.Z,{onClick:e7,sx:{color:"#ffffff",backgroundColor:"#000000",borderRadius:"12px",padding:"8px 24px",textTransform:"none",fontWeight:"bold","&:hover":{backgroundColor:"#333333"}},disabled:e1,children:e1?"Saving...":"Save Changes"})]})]})}}}]);