(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8023],{67752:function(e,t,r){"use strict";r.d(t,{f:function(){return i}});var n=r(94143),o=r(50738);function i(e){return(0,o.ZP)("MuiListItemIcon",e)}let a=(0,n.Z)("MuiListItemIcon",["root","alignItemsFlexStart"]);t.Z=a},5478:function(e,t,r){"use strict";r.d(t,{Ry:function(){return c}});var n=new WeakMap,o=new WeakMap,i={},a=0,l=function(e){return e&&(e.host||l(e.parentNode))},u=function(e,t,r,u){var c=(Array.isArray(e)?e:[e]).map(function(e){if(t.contains(e))return e;var r=l(e);return r&&t.contains(r)?r:(console.error("aria-hidden",e,"in not contained inside",t,". Doing nothing"),null)}).filter(function(e){return!!e});i[r]||(i[r]=new WeakMap);var s=i[r],d=[],f=new Set,p=new Set(c),h=function(e){!e||f.has(e)||(f.add(e),h(e.parentNode))};c.forEach(h);var v=function(e){!e||p.has(e)||Array.prototype.forEach.call(e.children,function(e){if(f.has(e))v(e);else try{var t=e.getAttribute(u),i=null!==t&&"false"!==t,a=(n.get(e)||0)+1,l=(s.get(e)||0)+1;n.set(e,a),s.set(e,l),d.push(e),1===a&&i&&o.set(e,!0),1===l&&e.setAttribute(r,"true"),i||e.setAttribute(u,"true")}catch(t){console.error("aria-hidden: cannot operate on ",e,t)}})};return v(t),f.clear(),a++,function(){d.forEach(function(e){var t=n.get(e)-1,i=s.get(e)-1;n.set(e,t),s.set(e,i),t||(o.has(e)||e.removeAttribute(u),o.delete(e)),i||e.removeAttribute(r)}),--a||(n=new WeakMap,n=new WeakMap,o=new WeakMap,i={})}},c=function(e,t,r){void 0===r&&(r="data-aria-hidden");var n=Array.from(Array.isArray(e)?e:[e]),o=t||("undefined"==typeof document?null:(Array.isArray(e)?e[0]:e).ownerDocument.body);return o?(n.push.apply(n,Array.from(o.querySelectorAll("[aria-live], script"))),u(n,o,r,"aria-hidden")):function(){return null}}},61134:function(e,t,r){var n;!function(o){"use strict";var i,a={precision:20,rounding:4,toExpNeg:-7,toExpPos:21,LN10:"2.302585092994045684017991454684364207601101488628772976033327900967572609677352480235997205089598298341967784042286"},l=!0,u="[DecimalError] ",c=u+"Invalid argument: ",s=u+"Exponent out of range: ",d=Math.floor,f=Math.pow,p=/^(\d+(\.\d*)?|\.\d+)(e[+-]?\d+)?$/i,h=d(1286742750677284.5),v={};function y(e,t){var r,n,o,i,a,u,c,s,d=e.constructor,f=d.precision;if(!e.s||!t.s)return t.s||(t=new d(e)),l?E(t,f):t;if(c=e.d,s=t.d,a=e.e,o=t.e,c=c.slice(),i=a-o){for(i<0?(n=c,i=-i,u=s.length):(n=s,o=a,u=c.length),i>(u=(a=Math.ceil(f/7))>u?a+1:u+1)&&(i=u,n.length=1),n.reverse();i--;)n.push(0);n.reverse()}for((u=c.length)-(i=s.length)<0&&(i=u,n=s,s=c,c=n),r=0;i;)r=(c[--i]=c[i]+s[i]+r)/1e7|0,c[i]%=1e7;for(r&&(c.unshift(r),++o),u=c.length;0==c[--u];)c.pop();return t.d=c,t.e=o,l?E(t,f):t}function m(e,t,r){if(e!==~~e||e<t||e>r)throw Error(c+e)}function g(e){var t,r,n,o=e.length-1,i="",a=e[0];if(o>0){for(i+=a,t=1;t<o;t++)(r=7-(n=e[t]+"").length)&&(i+=j(r)),i+=n;(r=7-(n=(a=e[t])+"").length)&&(i+=j(r))}else if(0===a)return"0";for(;a%10==0;)a/=10;return i+a}v.absoluteValue=v.abs=function(){var e=new this.constructor(this);return e.s&&(e.s=1),e},v.comparedTo=v.cmp=function(e){var t,r,n,o;if(e=new this.constructor(e),this.s!==e.s)return this.s||-e.s;if(this.e!==e.e)return this.e>e.e^this.s<0?1:-1;for(t=0,r=(n=this.d.length)<(o=e.d.length)?n:o;t<r;++t)if(this.d[t]!==e.d[t])return this.d[t]>e.d[t]^this.s<0?1:-1;return n===o?0:n>o^this.s<0?1:-1},v.decimalPlaces=v.dp=function(){var e=this.d.length-1,t=(e-this.e)*7;if(e=this.d[e])for(;e%10==0;e/=10)t--;return t<0?0:t},v.dividedBy=v.div=function(e){return b(this,new this.constructor(e))},v.dividedToIntegerBy=v.idiv=function(e){var t=this.constructor;return E(b(this,new t(e),0,1),t.precision)},v.equals=v.eq=function(e){return!this.cmp(e)},v.exponent=function(){return x(this)},v.greaterThan=v.gt=function(e){return this.cmp(e)>0},v.greaterThanOrEqualTo=v.gte=function(e){return this.cmp(e)>=0},v.isInteger=v.isint=function(){return this.e>this.d.length-2},v.isNegative=v.isneg=function(){return this.s<0},v.isPositive=v.ispos=function(){return this.s>0},v.isZero=function(){return 0===this.s},v.lessThan=v.lt=function(e){return 0>this.cmp(e)},v.lessThanOrEqualTo=v.lte=function(e){return 1>this.cmp(e)},v.logarithm=v.log=function(e){var t,r=this.constructor,n=r.precision,o=n+5;if(void 0===e)e=new r(10);else if((e=new r(e)).s<1||e.eq(i))throw Error(u+"NaN");if(this.s<1)throw Error(u+(this.s?"NaN":"-Infinity"));return this.eq(i)?new r(0):(l=!1,t=b(k(this,o),k(e,o),o),l=!0,E(t,n))},v.minus=v.sub=function(e){return e=new this.constructor(e),this.s==e.s?M(this,e):y(this,(e.s=-e.s,e))},v.modulo=v.mod=function(e){var t,r=this.constructor,n=r.precision;if(!(e=new r(e)).s)throw Error(u+"NaN");return this.s?(l=!1,t=b(this,e,0,1).times(e),l=!0,this.minus(t)):E(new r(this),n)},v.naturalExponential=v.exp=function(){return w(this)},v.naturalLogarithm=v.ln=function(){return k(this)},v.negated=v.neg=function(){var e=new this.constructor(this);return e.s=-e.s||0,e},v.plus=v.add=function(e){return e=new this.constructor(e),this.s==e.s?y(this,e):M(this,(e.s=-e.s,e))},v.precision=v.sd=function(e){var t,r,n;if(void 0!==e&&!!e!==e&&1!==e&&0!==e)throw Error(c+e);if(t=x(this)+1,r=7*(n=this.d.length-1)+1,n=this.d[n]){for(;n%10==0;n/=10)r--;for(n=this.d[0];n>=10;n/=10)r++}return e&&t>r?t:r},v.squareRoot=v.sqrt=function(){var e,t,r,n,o,i,a,c=this.constructor;if(this.s<1){if(!this.s)return new c(0);throw Error(u+"NaN")}for(e=x(this),l=!1,0==(o=Math.sqrt(+this))||o==1/0?(((t=g(this.d)).length+e)%2==0&&(t+="0"),o=Math.sqrt(t),e=d((e+1)/2)-(e<0||e%2),n=new c(t=o==1/0?"5e"+e:(t=o.toExponential()).slice(0,t.indexOf("e")+1)+e)):n=new c(o.toString()),o=a=(r=c.precision)+3;;)if(n=(i=n).plus(b(this,i,a+2)).times(.5),g(i.d).slice(0,a)===(t=g(n.d)).slice(0,a)){if(t=t.slice(a-3,a+1),o==a&&"4999"==t){if(E(i,r+1,0),i.times(i).eq(this)){n=i;break}}else if("9999"!=t)break;a+=4}return l=!0,E(n,r)},v.times=v.mul=function(e){var t,r,n,o,i,a,u,c,s,d=this.constructor,f=this.d,p=(e=new d(e)).d;if(!this.s||!e.s)return new d(0);for(e.s*=this.s,r=this.e+e.e,(c=f.length)<(s=p.length)&&(i=f,f=p,p=i,a=c,c=s,s=a),i=[],n=a=c+s;n--;)i.push(0);for(n=s;--n>=0;){for(t=0,o=c+n;o>n;)u=i[o]+p[n]*f[o-n-1]+t,i[o--]=u%1e7|0,t=u/1e7|0;i[o]=(i[o]+t)%1e7|0}for(;!i[--a];)i.pop();return t?++r:i.shift(),e.d=i,e.e=r,l?E(e,d.precision):e},v.toDecimalPlaces=v.todp=function(e,t){var r=this,n=r.constructor;return(r=new n(r),void 0===e)?r:(m(e,0,1e9),void 0===t?t=n.rounding:m(t,0,8),E(r,e+x(r)+1,t))},v.toExponential=function(e,t){var r,n=this,o=n.constructor;return void 0===e?r=S(n,!0):(m(e,0,1e9),void 0===t?t=o.rounding:m(t,0,8),r=S(n=E(new o(n),e+1,t),!0,e+1)),r},v.toFixed=function(e,t){var r,n,o=this.constructor;return void 0===e?S(this):(m(e,0,1e9),void 0===t?t=o.rounding:m(t,0,8),r=S((n=E(new o(this),e+x(this)+1,t)).abs(),!1,e+x(n)+1),this.isneg()&&!this.isZero()?"-"+r:r)},v.toInteger=v.toint=function(){var e=this.constructor;return E(new e(this),x(this)+1,e.rounding)},v.toNumber=function(){return+this},v.toPower=v.pow=function(e){var t,r,n,o,a,c,s=this,f=s.constructor,p=+(e=new f(e));if(!e.s)return new f(i);if(!(s=new f(s)).s){if(e.s<1)throw Error(u+"Infinity");return s}if(s.eq(i))return s;if(n=f.precision,e.eq(i))return E(s,n);if(c=(t=e.e)>=(r=e.d.length-1),a=s.s,c){if((r=p<0?-p:p)<=9007199254740991){for(o=new f(i),t=Math.ceil(n/7+4),l=!1;r%2&&C((o=o.times(s)).d,t),0!==(r=d(r/2));)C((s=s.times(s)).d,t);return l=!0,e.s<0?new f(i).div(o):E(o,n)}}else if(a<0)throw Error(u+"NaN");return a=a<0&&1&e.d[Math.max(t,r)]?-1:1,s.s=1,l=!1,o=e.times(k(s,n+12)),l=!0,(o=w(o)).s=a,o},v.toPrecision=function(e,t){var r,n,o=this,i=o.constructor;return void 0===e?(r=x(o),n=S(o,r<=i.toExpNeg||r>=i.toExpPos)):(m(e,1,1e9),void 0===t?t=i.rounding:m(t,0,8),r=x(o=E(new i(o),e,t)),n=S(o,e<=r||r<=i.toExpNeg,e)),n},v.toSignificantDigits=v.tosd=function(e,t){var r=this.constructor;return void 0===e?(e=r.precision,t=r.rounding):(m(e,1,1e9),void 0===t?t=r.rounding:m(t,0,8)),E(new r(this),e,t)},v.toString=v.valueOf=v.val=v.toJSON=function(){var e=x(this),t=this.constructor;return S(this,e<=t.toExpNeg||e>=t.toExpPos)};var b=function(){function e(e,t){var r,n=0,o=e.length;for(e=e.slice();o--;)r=e[o]*t+n,e[o]=r%1e7|0,n=r/1e7|0;return n&&e.unshift(n),e}function t(e,t,r,n){var o,i;if(r!=n)i=r>n?1:-1;else for(o=i=0;o<r;o++)if(e[o]!=t[o]){i=e[o]>t[o]?1:-1;break}return i}function r(e,t,r){for(var n=0;r--;)e[r]-=n,n=e[r]<t[r]?1:0,e[r]=1e7*n+e[r]-t[r];for(;!e[0]&&e.length>1;)e.shift()}return function(n,o,i,a){var l,c,s,d,f,p,h,v,y,m,g,b,w,O,j,k,P,M,S=n.constructor,C=n.s==o.s?1:-1,A=n.d,T=o.d;if(!n.s)return new S(n);if(!o.s)throw Error(u+"Division by zero");for(s=0,c=n.e-o.e,P=T.length,j=A.length,v=(h=new S(C)).d=[];T[s]==(A[s]||0);)++s;if(T[s]>(A[s]||0)&&--c,(b=null==i?i=S.precision:a?i+(x(n)-x(o))+1:i)<0)return new S(0);if(b=b/7+2|0,s=0,1==P)for(d=0,T=T[0],b++;(s<j||d)&&b--;s++)w=1e7*d+(A[s]||0),v[s]=w/T|0,d=w%T|0;else{for((d=1e7/(T[0]+1)|0)>1&&(T=e(T,d),A=e(A,d),P=T.length,j=A.length),O=P,m=(y=A.slice(0,P)).length;m<P;)y[m++]=0;(M=T.slice()).unshift(0),k=T[0],T[1]>=1e7/2&&++k;do d=0,(l=t(T,y,P,m))<0?(g=y[0],P!=m&&(g=1e7*g+(y[1]||0)),(d=g/k|0)>1?(d>=1e7&&(d=1e7-1),p=(f=e(T,d)).length,m=y.length,1==(l=t(f,y,p,m))&&(d--,r(f,P<p?M:T,p))):(0==d&&(l=d=1),f=T.slice()),(p=f.length)<m&&f.unshift(0),r(y,f,m),-1==l&&(m=y.length,(l=t(T,y,P,m))<1&&(d++,r(y,P<m?M:T,m))),m=y.length):0===l&&(d++,y=[0]),v[s++]=d,l&&y[0]?y[m++]=A[O]||0:(y=[A[O]],m=1);while((O++<j||void 0!==y[0])&&b--)}return v[0]||v.shift(),h.e=c,E(h,a?i+x(h)+1:i)}}();function w(e,t){var r,n,o,a,u,c=0,d=0,p=e.constructor,h=p.precision;if(x(e)>16)throw Error(s+x(e));if(!e.s)return new p(i);for(null==t?(l=!1,u=h):u=t,a=new p(.03125);e.abs().gte(.1);)e=e.times(a),d+=5;for(u+=Math.log(f(2,d))/Math.LN10*2+5|0,r=n=o=new p(i),p.precision=u;;){if(n=E(n.times(e),u),r=r.times(++c),g((a=o.plus(b(n,r,u))).d).slice(0,u)===g(o.d).slice(0,u)){for(;d--;)o=E(o.times(o),u);return p.precision=h,null==t?(l=!0,E(o,h)):o}o=a}}function x(e){for(var t=7*e.e,r=e.d[0];r>=10;r/=10)t++;return t}function O(e,t,r){if(t>e.LN10.sd())throw l=!0,r&&(e.precision=r),Error(u+"LN10 precision limit exceeded");return E(new e(e.LN10),t)}function j(e){for(var t="";e--;)t+="0";return t}function k(e,t){var r,n,o,a,c,s,d,f,p,h=1,v=e,y=v.d,m=v.constructor,w=m.precision;if(v.s<1)throw Error(u+(v.s?"NaN":"-Infinity"));if(v.eq(i))return new m(0);if(null==t?(l=!1,f=w):f=t,v.eq(10))return null==t&&(l=!0),O(m,f);if(f+=10,m.precision=f,n=(r=g(y)).charAt(0),!(15e14>Math.abs(a=x(v))))return d=O(m,f+2,w).times(a+""),v=k(new m(n+"."+r.slice(1)),f-10).plus(d),m.precision=w,null==t?(l=!0,E(v,w)):v;for(;n<7&&1!=n||1==n&&r.charAt(1)>3;)n=(r=g((v=v.times(e)).d)).charAt(0),h++;for(a=x(v),n>1?(v=new m("0."+r),a++):v=new m(n+"."+r.slice(1)),s=c=v=b(v.minus(i),v.plus(i),f),p=E(v.times(v),f),o=3;;){if(c=E(c.times(p),f),g((d=s.plus(b(c,new m(o),f))).d).slice(0,f)===g(s.d).slice(0,f))return s=s.times(2),0!==a&&(s=s.plus(O(m,f+2,w).times(a+""))),s=b(s,new m(h),f),m.precision=w,null==t?(l=!0,E(s,w)):s;s=d,o+=2}}function P(e,t){var r,n,o;for((r=t.indexOf("."))>-1&&(t=t.replace(".","")),(n=t.search(/e/i))>0?(r<0&&(r=n),r+=+t.slice(n+1),t=t.substring(0,n)):r<0&&(r=t.length),n=0;48===t.charCodeAt(n);)++n;for(o=t.length;48===t.charCodeAt(o-1);)--o;if(t=t.slice(n,o)){if(o-=n,r=r-n-1,e.e=d(r/7),e.d=[],n=(r+1)%7,r<0&&(n+=7),n<o){for(n&&e.d.push(+t.slice(0,n)),o-=7;n<o;)e.d.push(+t.slice(n,n+=7));n=7-(t=t.slice(n)).length}else n-=o;for(;n--;)t+="0";if(e.d.push(+t),l&&(e.e>h||e.e<-h))throw Error(s+r)}else e.s=0,e.e=0,e.d=[0];return e}function E(e,t,r){var n,o,i,a,u,c,p,v,y=e.d;for(a=1,i=y[0];i>=10;i/=10)a++;if((n=t-a)<0)n+=7,o=t,p=y[v=0];else{if((v=Math.ceil((n+1)/7))>=(i=y.length))return e;for(a=1,p=i=y[v];i>=10;i/=10)a++;n%=7,o=n-7+a}if(void 0!==r&&(u=p/(i=f(10,a-o-1))%10|0,c=t<0||void 0!==y[v+1]||p%i,c=r<4?(u||c)&&(0==r||r==(e.s<0?3:2)):u>5||5==u&&(4==r||c||6==r&&(n>0?o>0?p/f(10,a-o):0:y[v-1])%10&1||r==(e.s<0?8:7))),t<1||!y[0])return c?(i=x(e),y.length=1,t=t-i-1,y[0]=f(10,(7-t%7)%7),e.e=d(-t/7)||0):(y.length=1,y[0]=e.e=e.s=0),e;if(0==n?(y.length=v,i=1,v--):(y.length=v+1,i=f(10,7-n),y[v]=o>0?(p/f(10,a-o)%f(10,o)|0)*i:0),c)for(;;){if(0==v){1e7==(y[0]+=i)&&(y[0]=1,++e.e);break}if(y[v]+=i,1e7!=y[v])break;y[v--]=0,i=1}for(n=y.length;0===y[--n];)y.pop();if(l&&(e.e>h||e.e<-h))throw Error(s+x(e));return e}function M(e,t){var r,n,o,i,a,u,c,s,d,f,p=e.constructor,h=p.precision;if(!e.s||!t.s)return t.s?t.s=-t.s:t=new p(e),l?E(t,h):t;if(c=e.d,f=t.d,n=t.e,s=e.e,c=c.slice(),a=s-n){for((d=a<0)?(r=c,a=-a,u=f.length):(r=f,n=s,u=c.length),a>(o=Math.max(Math.ceil(h/7),u)+2)&&(a=o,r.length=1),r.reverse(),o=a;o--;)r.push(0);r.reverse()}else{for((d=(o=c.length)<(u=f.length))&&(u=o),o=0;o<u;o++)if(c[o]!=f[o]){d=c[o]<f[o];break}a=0}for(d&&(r=c,c=f,f=r,t.s=-t.s),u=c.length,o=f.length-u;o>0;--o)c[u++]=0;for(o=f.length;o>a;){if(c[--o]<f[o]){for(i=o;i&&0===c[--i];)c[i]=1e7-1;--c[i],c[o]+=1e7}c[o]-=f[o]}for(;0===c[--u];)c.pop();for(;0===c[0];c.shift())--n;return c[0]?(t.d=c,t.e=n,l?E(t,h):t):new p(0)}function S(e,t,r){var n,o=x(e),i=g(e.d),a=i.length;return t?(r&&(n=r-a)>0?i=i.charAt(0)+"."+i.slice(1)+j(n):a>1&&(i=i.charAt(0)+"."+i.slice(1)),i=i+(o<0?"e":"e+")+o):o<0?(i="0."+j(-o-1)+i,r&&(n=r-a)>0&&(i+=j(n))):o>=a?(i+=j(o+1-a),r&&(n=r-o-1)>0&&(i=i+"."+j(n))):((n=o+1)<a&&(i=i.slice(0,n)+"."+i.slice(n)),r&&(n=r-a)>0&&(o+1===a&&(i+="."),i+=j(n))),e.s<0?"-"+i:i}function C(e,t){if(e.length>t)return e.length=t,!0}function A(e){if(!e||"object"!=typeof e)throw Error(u+"Object expected");var t,r,n,o=["precision",1,1e9,"rounding",0,8,"toExpNeg",-1/0,0,"toExpPos",0,1/0];for(t=0;t<o.length;t+=3)if(void 0!==(n=e[r=o[t]])){if(d(n)===n&&n>=o[t+1]&&n<=o[t+2])this[r]=n;else throw Error(c+r+": "+n)}if(void 0!==(n=e[r="LN10"])){if(n==Math.LN10)this[r]=new this(n);else throw Error(c+r+": "+n)}return this}(a=function e(t){var r,n,o;function i(e){if(!(this instanceof i))return new i(e);if(this.constructor=i,e instanceof i){this.s=e.s,this.e=e.e,this.d=(e=e.d)?e.slice():e;return}if("number"==typeof e){if(0*e!=0)throw Error(c+e);if(e>0)this.s=1;else if(e<0)e=-e,this.s=-1;else{this.s=0,this.e=0,this.d=[0];return}if(e===~~e&&e<1e7){this.e=0,this.d=[e];return}return P(this,e.toString())}if("string"!=typeof e)throw Error(c+e);if(45===e.charCodeAt(0)?(e=e.slice(1),this.s=-1):this.s=1,p.test(e))P(this,e);else throw Error(c+e)}if(i.prototype=v,i.ROUND_UP=0,i.ROUND_DOWN=1,i.ROUND_CEIL=2,i.ROUND_FLOOR=3,i.ROUND_HALF_UP=4,i.ROUND_HALF_DOWN=5,i.ROUND_HALF_EVEN=6,i.ROUND_HALF_CEIL=7,i.ROUND_HALF_FLOOR=8,i.clone=e,i.config=i.set=A,void 0===t&&(t={}),t)for(r=0,o=["precision","rounding","toExpNeg","toExpPos","LN10"];r<o.length;)t.hasOwnProperty(n=o[r++])||(t[n]=this[n]);return i.config(t),i}(a)).default=a.Decimal=a,i=new a(1),void 0!==(n=(function(){return a}).call(t,r,t,e))&&(e.exports=n)}(0)},15870:function(e,t,r){e.exports=r(84073).get},38450:function(e,t,r){e.exports=r(7972).isEqual},46802:function(e,t,r){e.exports=r(49483).isPlainObject},37618:function(e,t,r){e.exports=r(17858).last},41664:function(e,t,r){e.exports=r(11480).range},31104:function(e,t,r){e.exports=r(26273).sortBy},34926:function(e,t,r){e.exports=r(9313).throttle},36841:function(e,t,r){e.exports=r(62992).uniqBy},95491:function(e,t){"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isUnsafeProperty=function(e){return"__proto__"===e}},949:function(e,t){"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.flatten=function(e,t=1){let r=[],n=Math.floor(t),o=(e,t)=>{for(let i=0;i<e.length;i++){let a=e[i];Array.isArray(a)&&t<n?o(a,t+1):r.push(a)}};return o(e,0),r}},68509:function(e,t){"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.last=function(e){return e[e.length-1]}},65745:function(e,t){"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.uniqBy=function(e,t){let r=new Map;for(let n=0;n<e.length;n++){let o=e[n],i=t(o);r.has(i)||r.set(i,o)}return Array.from(r.values())}},1260:function(e,t){"use strict";function r(e){return"symbol"==typeof e?1:null===e?2:void 0===e?3:e!=e?4:0}Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.compareValues=(e,t,n)=>{if(e!==t){let o=r(e),i=r(t);if(o===i&&0===o){if(e<t)return"desc"===n?1:-1;if(e>t)return"desc"===n?-1:1}return"desc"===n?i-o:o-i}return 0}},59924:function(e,t){"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.getSymbols=function(e){return Object.getOwnPropertySymbols(e).filter(t=>Object.prototype.propertyIsEnumerable.call(e,t))}},83984:function(e,t){"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.getTag=function(e){return null==e?void 0===e?"[object Undefined]":"[object Null]":Object.prototype.toString.call(e)}},35159:function(e,t){"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isDeepKey=function(e){switch(typeof e){case"number":case"symbol":return!1;case"string":return e.includes(".")||e.includes("[")||e.includes("]")}}},84623:function(e,t){"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let r=/^(?:0|[1-9]\d*)$/;t.isIndex=function(e,t=Number.MAX_SAFE_INTEGER){switch(typeof e){case"number":return Number.isInteger(e)&&e>=0&&e<t;case"symbol":return!1;case"string":return r.test(e)}}},34893:function(e,t,r){"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(84623),o=r(98934),i=r(13400),a=r(95037);t.isIterateeCall=function(e,t,r){return!!i.isObject(r)&&(!!("number"==typeof t&&o.isArrayLike(r)&&n.isIndex(t))&&t<r.length||"string"==typeof t&&t in r)&&a.eq(r[t],e)}},45799:function(e,t,r){"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(74675),o=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,i=/^\w*$/;t.isKey=function(e,t){return!Array.isArray(e)&&(!!("number"==typeof e||"boolean"==typeof e||null==e||n.isSymbol(e))||"string"==typeof e&&(i.test(e)||!o.test(e))||null!=t&&Object.hasOwn(t,e))}},91116:function(e,t){"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.argumentsTag="[object Arguments]",t.arrayBufferTag="[object ArrayBuffer]",t.arrayTag="[object Array]",t.bigInt64ArrayTag="[object BigInt64Array]",t.bigUint64ArrayTag="[object BigUint64Array]",t.booleanTag="[object Boolean]",t.dataViewTag="[object DataView]",t.dateTag="[object Date]",t.errorTag="[object Error]",t.float32ArrayTag="[object Float32Array]",t.float64ArrayTag="[object Float64Array]",t.functionTag="[object Function]",t.int16ArrayTag="[object Int16Array]",t.int32ArrayTag="[object Int32Array]",t.int8ArrayTag="[object Int8Array]",t.mapTag="[object Map]",t.numberTag="[object Number]",t.objectTag="[object Object]",t.regexpTag="[object RegExp]",t.setTag="[object Set]",t.stringTag="[object String]",t.symbolTag="[object Symbol]",t.uint16ArrayTag="[object Uint16Array]",t.uint32ArrayTag="[object Uint32Array]",t.uint8ArrayTag="[object Uint8Array]",t.uint8ClampedArrayTag="[object Uint8ClampedArray]"},77003:function(e,t){"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.toArray=function(e){return Array.isArray(e)?e:Array.from(e)}},49670:function(e,t){"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.toKey=function(e){return"string"==typeof e||"symbol"==typeof e?e:Object.is(e?.valueOf?.(),-0)?"-0":String(e)}},17858:function(e,t,r){"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(68509),o=r(77003),i=r(98934);t.last=function(e){if(i.isArrayLike(e))return n.last(o.toArray(e))}},46688:function(e,t,r){"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(1260),o=r(45799),i=r(64161);t.orderBy=function(e,t,r,a){if(null==e)return[];r=a?void 0:r,Array.isArray(e)||(e=Object.values(e)),Array.isArray(t)||(t=null==t?[null]:[t]),0===t.length&&(t=[null]),Array.isArray(r)||(r=null==r?[]:[r]),r=r.map(e=>String(e));let l=(e,t)=>{let r=e;for(let e=0;e<t.length&&null!=r;++e)r=r[t[e]];return r},u=(e,t)=>null==t||null==e?t:"object"==typeof e&&"key"in e?Object.hasOwn(t,e.key)?t[e.key]:l(t,e.path):"function"==typeof e?e(t):Array.isArray(e)?l(t,e):"object"==typeof t?t[e]:t,c=t.map(e=>(Array.isArray(e)&&1===e.length&&(e=e[0]),null==e||"function"==typeof e||Array.isArray(e)||o.isKey(e))?e:{key:e,path:i.toPath(e)});return e.map(e=>({original:e,criteria:c.map(t=>u(t,e))})).slice().sort((e,t)=>{for(let o=0;o<c.length;o++){let i=n.compareValues(e.criteria[o],t.criteria[o],r[o]);if(0!==i)return i}return 0}).map(e=>e.original)}},26273:function(e,t,r){"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(46688),o=r(949),i=r(34893);t.sortBy=function(e,...t){let r=t.length;return r>1&&i.isIterateeCall(e,t[0],t[1])?t=[]:r>2&&i.isIterateeCall(t[0],t[1],t[2])&&(t=[t[0]]),n.orderBy(e,o.flatten(t),["asc"])}},62992:function(e,t,r){"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(65745),o=r(50601),i=r(79311),a=r(55586);t.uniqBy=function(e,t=o.identity){return i.isArrayLikeObject(e)?n.uniqBy(Array.from(e),a.iteratee(t)):[]}},12801:function(e,t,r){"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(67384);t.debounce=function(e,t=0,r={}){let o;"object"!=typeof r&&(r={});let{leading:i=!1,trailing:a=!0,maxWait:l}=r,u=[,,];i&&(u[0]="leading"),a&&(u[1]="trailing");let c=null,s=n.debounce(function(...t){o=e.apply(this,t),c=null},t,{edges:u}),d=function(...t){return null!=l&&(null===c&&(c=Date.now()),Date.now()-c>=l)?(o=e.apply(this,t),c=Date.now(),s.cancel(),s.schedule()):s.apply(this,t),o};return d.cancel=s.cancel,d.flush=()=>(s.flush(),o),d}},9313:function(e,t,r){"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(12801);t.throttle=function(e,t=0,r={}){"object"!=typeof r&&(r={});let{leading:o=!0,trailing:i=!0}=r;return n.debounce(e,t,{leading:o,trailing:i,maxWait:t})}},11480:function(e,t,r){"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(34893),o=r(95810);t.range=function(e,t,r){r&&"number"!=typeof r&&n.isIterateeCall(e,t,r)&&(t=r=void 0),e=o.toFinite(e),void 0===t?(t=e,e=0):t=o.toFinite(t),r=void 0===r?e<t?1:-1:o.toFinite(r);let i=Math.max(Math.ceil((t-e)/(r||1)),0),a=Array(i);for(let t=0;t<i;t++)a[t]=e,e+=r;return a}},5076:function(e,t,r){"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(887);t.cloneDeep=function(e){return n.cloneDeepWith(e)}},887:function(e,t,r){"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(16737),o=r(91116);t.cloneDeepWith=function(e,t){return n.cloneDeepWith(e,(r,i,a,l)=>{let u=t?.(r,i,a,l);if(null!=u)return u;if("object"==typeof e)switch(Object.prototype.toString.call(e)){case o.numberTag:case o.stringTag:case o.booleanTag:{let t=new e.constructor(e?.valueOf());return n.copyProperties(t,e),t}case o.argumentsTag:{let t={};return n.copyProperties(t,e),t.length=e.length,t[Symbol.iterator]=e[Symbol.iterator],t}default:return}})}},84073:function(e,t,r){"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(95491),o=r(35159),i=r(49670),a=r(64161);t.get=function e(t,r,l){if(null==t)return l;switch(typeof r){case"string":{if(n.isUnsafeProperty(r))return l;let i=t[r];if(void 0===i){if(o.isDeepKey(r))return e(t,a.toPath(r),l);return l}return i}case"number":case"symbol":{"number"==typeof r&&(r=i.toKey(r));let e=t[r];if(void 0===e)return l;return e}default:{if(Array.isArray(r))return function(e,t,r){if(0===t.length)return r;let o=e;for(let e=0;e<t.length;e++){if(null==o||n.isUnsafeProperty(t[e]))return r;o=o[t[e]]}return void 0===o?r:o}(t,r,l);if(r=Object.is(r?.valueOf(),-0)?"-0":String(r),n.isUnsafeProperty(r))return l;let e=t[r];if(void 0===e)return l;return e}}}},18141:function(e,t,r){"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(35159),o=r(84623),i=r(62094),a=r(64161);t.has=function(e,t){let r;if(0===(r=Array.isArray(t)?t:"string"==typeof t&&n.isDeepKey(t)&&e?.[t]==null?a.toPath(t):[t]).length)return!1;let l=e;for(let e=0;e<r.length;e++){let t=r[e];if((null==l||!Object.hasOwn(l,t))&&!((Array.isArray(l)||i.isArguments(l))&&o.isIndex(t)&&t<l.length))return!1;l=l[t]}return!0}},83752:function(e,t,r){"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(84073);t.property=function(e){return function(t){return n.get(t,e)}}},62094:function(e,t,r){"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(83984);t.isArguments=function(e){return null!==e&&"object"==typeof e&&"[object Arguments]"===n.getTag(e)}},98934:function(e,t,r){"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(48973);t.isArrayLike=function(e){return null!=e&&"function"!=typeof e&&n.isLength(e.length)}},79311:function(e,t,r){"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(98934),o=r(77475);t.isArrayLikeObject=function(e){return o.isObjectLike(e)&&n.isArrayLike(e)}},16643:function(e,t,r){"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(80757);t.isMatch=function(e,t){return n.isMatchWith(e,t,()=>void 0)}},80757:function(e,t,r){"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(16643),o=r(13400),i=r(34526),a=r(95037);function l(e,t,r,n){if(t===e)return!0;switch(typeof t){case"object":return function(e,t,r,n){if(null==t)return!0;if(Array.isArray(t))return u(e,t,r,n);if(t instanceof Map)return function(e,t,r,n){if(0===t.size)return!0;if(!(e instanceof Map))return!1;for(let[o,i]of t.entries())if(!1===r(e.get(o),i,o,e,t,n))return!1;return!0}(e,t,r,n);if(t instanceof Set)return c(e,t,r,n);let o=Object.keys(t);if(null==e)return 0===o.length;if(0===o.length)return!0;if(n&&n.has(t))return n.get(t)===e;n&&n.set(t,e);try{for(let a=0;a<o.length;a++){let l=o[a];if(!i.isPrimitive(e)&&!(l in e)||void 0===t[l]&&void 0!==e[l]||null===t[l]&&null!==e[l]||!r(e[l],t[l],l,e,t,n))return!1}return!0}finally{n&&n.delete(t)}}(e,t,r,n);case"function":if(Object.keys(t).length>0)return l(e,{...t},r,n);return a.eq(e,t);default:if(!o.isObject(e))return a.eq(e,t);if("string"==typeof t)return""===t;return!0}}function u(e,t,r,n){if(0===t.length)return!0;if(!Array.isArray(e))return!1;let o=new Set;for(let i=0;i<t.length;i++){let a=t[i],l=!1;for(let u=0;u<e.length;u++){if(o.has(u))continue;let c=e[u],s=!1;if(r(c,a,i,e,t,n)&&(s=!0),s){o.add(u),l=!0;break}}if(!l)return!1}return!0}function c(e,t,r,n){return 0===t.size||e instanceof Set&&u([...e],[...t],r,n)}t.isMatchWith=function(e,t,r){return"function"!=typeof r?n.isMatch(e,t):l(e,t,function e(t,n,o,i,a,u){let c=r(t,n,o,i,a,u);return void 0!==c?!!c:l(t,n,e,u)},new Map)},t.isSetMatch=c},13400:function(e,t){"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isObject=function(e){return null!==e&&("object"==typeof e||"function"==typeof e)}},77475:function(e,t){"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isObjectLike=function(e){return"object"==typeof e&&null!==e}},49483:function(e,t){"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isPlainObject=function(e){if("object"!=typeof e||null==e)return!1;if(null===Object.getPrototypeOf(e))return!0;if("[object Object]"!==Object.prototype.toString.call(e)){let t=e[Symbol.toStringTag];return!!(null!=t&&Object.getOwnPropertyDescriptor(e,Symbol.toStringTag)?.writable)&&e.toString()===`[object ${t}]`}let t=e;for(;null!==Object.getPrototypeOf(t);)t=Object.getPrototypeOf(t);return Object.getPrototypeOf(e)===t}},74675:function(e,t){"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isSymbol=function(e){return"symbol"==typeof e||e instanceof Symbol}},76536:function(e,t,r){"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(16643),o=r(60631);t.matches=function(e){return e=o.cloneDeep(e),t=>n.isMatch(t,e)}},14308:function(e,t,r){"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(16643),o=r(49670),i=r(5076),a=r(84073),l=r(18141);t.matchesProperty=function(e,t){switch(typeof e){case"object":Object.is(e?.valueOf(),-0)&&(e="-0");break;case"number":e=o.toKey(e)}return t=i.cloneDeep(t),function(r){let o=a.get(r,e);return void 0===o?l.has(r,e):void 0===t?void 0===o:n.isMatch(o,t)}}},95037:function(e,t){"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.eq=function(e,t){return e===t||Number.isNaN(e)&&Number.isNaN(t)}},55586:function(e,t,r){"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(50601),o=r(83752),i=r(76536),a=r(14308);t.iteratee=function(e){if(null==e)return n.identity;switch(typeof e){case"function":return e;case"object":if(Array.isArray(e)&&2===e.length)return a.matchesProperty(e[0],e[1]);return i.matches(e);case"string":case"symbol":case"number":return o.property(e)}}},95810:function(e,t,r){"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(44201);t.toFinite=function(e){return e?(e=n.toNumber(e))===1/0||e===-1/0?(e<0?-1:1)*Number.MAX_VALUE:e==e?e:0:0===e?e:0}},44201:function(e,t,r){"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(74675);t.toNumber=function(e){return n.isSymbol(e)?NaN:Number(e)}},64161:function(e,t){"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.toPath=function(e){let t=[],r=e.length;if(0===r)return t;let n=0,o="",i="",a=!1;for(46===e.charCodeAt(0)&&(t.push(""),n++);n<r;){let l=e[n];i?"\\"===l&&n+1<r?o+=e[++n]:l===i?i="":o+=l:a?'"'===l||"'"===l?i=l:"]"===l?(a=!1,t.push(o),o=""):o+=l:"["===l?(a=!0,o&&(t.push(o),o="")):"."===l?o&&(t.push(o),o=""):o+=l,n++}return o&&t.push(o),t}},67384:function(e,t){"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.debounce=function(e,t,{signal:r,edges:n}={}){let o;let i=null,a=null!=n&&n.includes("leading"),l=null==n||n.includes("trailing"),u=()=>{null!==i&&(e.apply(o,i),o=void 0,i=null)},c=()=>{l&&u(),p()},s=null,d=()=>{null!=s&&clearTimeout(s),s=setTimeout(()=>{s=null,c()},t)},f=()=>{null!==s&&(clearTimeout(s),s=null)},p=()=>{f(),o=void 0,i=null},h=function(...e){if(r?.aborted)return;o=this,i=e;let t=null==s;d(),a&&t&&u()};return h.schedule=d,h.cancel=p,h.flush=()=>{f(),u()},r?.addEventListener("abort",p,{once:!0}),h}},50601:function(e,t){"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.identity=function(e){return e}},60196:function(e,t){"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.noop=function(){}},60631:function(e,t,r){"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(16737);t.cloneDeep=function(e){return n.cloneDeepWithImpl(e,void 0,e,new Map,void 0)}},16737:function(e,t,r){"use strict";var n=r(96434).Buffer;Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let o=r(59924),i=r(83984),a=r(91116),l=r(34526),u=r(23497);function c(e,t,r,o=new Map,d){let f=d?.(e,t,r,o);if(null!=f)return f;if(l.isPrimitive(e))return e;if(o.has(e))return o.get(e);if(Array.isArray(e)){let t=Array(e.length);o.set(e,t);for(let n=0;n<e.length;n++)t[n]=c(e[n],n,r,o,d);return Object.hasOwn(e,"index")&&(t.index=e.index),Object.hasOwn(e,"input")&&(t.input=e.input),t}if(e instanceof Date)return new Date(e.getTime());if(e instanceof RegExp){let t=new RegExp(e.source,e.flags);return t.lastIndex=e.lastIndex,t}if(e instanceof Map){let t=new Map;for(let[n,i]of(o.set(e,t),e))t.set(n,c(i,n,r,o,d));return t}if(e instanceof Set){let t=new Set;for(let n of(o.set(e,t),e))t.add(c(n,void 0,r,o,d));return t}if(void 0!==n&&n.isBuffer(e))return e.subarray();if(u.isTypedArray(e)){let t=new(Object.getPrototypeOf(e)).constructor(e.length);o.set(e,t);for(let n=0;n<e.length;n++)t[n]=c(e[n],n,r,o,d);return t}if(e instanceof ArrayBuffer||"undefined"!=typeof SharedArrayBuffer&&e instanceof SharedArrayBuffer)return e.slice(0);if(e instanceof DataView){let t=new DataView(e.buffer.slice(0),e.byteOffset,e.byteLength);return o.set(e,t),s(t,e,r,o,d),t}if("undefined"!=typeof File&&e instanceof File){let t=new File([e],e.name,{type:e.type});return o.set(e,t),s(t,e,r,o,d),t}if(e instanceof Blob){let t=new Blob([e],{type:e.type});return o.set(e,t),s(t,e,r,o,d),t}if(e instanceof Error){let t=new e.constructor;return o.set(e,t),t.message=e.message,t.name=e.name,t.stack=e.stack,t.cause=e.cause,s(t,e,r,o,d),t}if("object"==typeof e&&function(e){switch(i.getTag(e)){case a.argumentsTag:case a.arrayTag:case a.arrayBufferTag:case a.dataViewTag:case a.booleanTag:case a.dateTag:case a.float32ArrayTag:case a.float64ArrayTag:case a.int8ArrayTag:case a.int16ArrayTag:case a.int32ArrayTag:case a.mapTag:case a.numberTag:case a.objectTag:case a.regexpTag:case a.setTag:case a.stringTag:case a.symbolTag:case a.uint8ArrayTag:case a.uint8ClampedArrayTag:case a.uint16ArrayTag:case a.uint32ArrayTag:return!0;default:return!1}}(e)){let t=Object.create(Object.getPrototypeOf(e));return o.set(e,t),s(t,e,r,o,d),t}return e}function s(e,t,r=e,n,i){let a=[...Object.keys(t),...o.getSymbols(t)];for(let o=0;o<a.length;o++){let l=a[o],u=Object.getOwnPropertyDescriptor(e,l);(null==u||u.writable)&&(e[l]=c(t[l],l,r,n,i))}}t.cloneDeepWith=function(e,t){return c(e,void 0,e,new Map,t)},t.cloneDeepWithImpl=c,t.copyProperties=s},7972:function(e,t,r){"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(56993),o=r(60196);t.isEqual=function(e,t){return n.isEqualWith(e,t,o.noop)}},56993:function(e,t,r){"use strict";var n=r(96434).Buffer;Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let o=r(7252),i=r(59924),a=r(83984),l=r(91116),u=r(95037);t.isEqualWith=function(e,t,r){return function e(t,r,c,s,d,f,p){let h=p(t,r,c,s,d,f);if(void 0!==h)return h;if(typeof t==typeof r)switch(typeof t){case"bigint":case"string":case"boolean":case"symbol":case"undefined":case"function":return t===r;case"number":return t===r||Object.is(t,r)}return function t(r,c,s,d){if(Object.is(r,c))return!0;let f=a.getTag(r),p=a.getTag(c);if(f===l.argumentsTag&&(f=l.objectTag),p===l.argumentsTag&&(p=l.objectTag),f!==p)return!1;switch(f){case l.stringTag:return r.toString()===c.toString();case l.numberTag:{let e=r.valueOf(),t=c.valueOf();return u.eq(e,t)}case l.booleanTag:case l.dateTag:case l.symbolTag:return Object.is(r.valueOf(),c.valueOf());case l.regexpTag:return r.source===c.source&&r.flags===c.flags;case l.functionTag:return r===c}let h=(s=s??new Map).get(r),v=s.get(c);if(null!=h&&null!=v)return h===c;s.set(r,c),s.set(c,r);try{switch(f){case l.mapTag:if(r.size!==c.size)return!1;for(let[t,n]of r.entries())if(!c.has(t)||!e(n,c.get(t),t,r,c,s,d))return!1;return!0;case l.setTag:{if(r.size!==c.size)return!1;let t=Array.from(r.values()),n=Array.from(c.values());for(let o=0;o<t.length;o++){let i=t[o],a=n.findIndex(t=>e(i,t,void 0,r,c,s,d));if(-1===a)return!1;n.splice(a,1)}return!0}case l.arrayTag:case l.uint8ArrayTag:case l.uint8ClampedArrayTag:case l.uint16ArrayTag:case l.uint32ArrayTag:case l.bigUint64ArrayTag:case l.int8ArrayTag:case l.int16ArrayTag:case l.int32ArrayTag:case l.bigInt64ArrayTag:case l.float32ArrayTag:case l.float64ArrayTag:if(void 0!==n&&n.isBuffer(r)!==n.isBuffer(c)||r.length!==c.length)return!1;for(let t=0;t<r.length;t++)if(!e(r[t],c[t],t,r,c,s,d))return!1;return!0;case l.arrayBufferTag:if(r.byteLength!==c.byteLength)return!1;return t(new Uint8Array(r),new Uint8Array(c),s,d);case l.dataViewTag:if(r.byteLength!==c.byteLength||r.byteOffset!==c.byteOffset)return!1;return t(new Uint8Array(r),new Uint8Array(c),s,d);case l.errorTag:return r.name===c.name&&r.message===c.message;case l.objectTag:{if(!(t(r.constructor,c.constructor,s,d)||o.isPlainObject(r)&&o.isPlainObject(c)))return!1;let n=[...Object.keys(r),...i.getSymbols(r)],a=[...Object.keys(c),...i.getSymbols(c)];if(n.length!==a.length)return!1;for(let t=0;t<n.length;t++){let o=n[t],i=r[o];if(!Object.hasOwn(c,o))return!1;let a=c[o];if(!e(i,a,o,r,c,s,d))return!1}return!0}default:return!1}}finally{s.delete(r),s.delete(c)}}(t,r,f,p)}(e,t,void 0,void 0,void 0,void 0,r)}},48973:function(e,t){"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isLength=function(e){return Number.isSafeInteger(e)&&e>=0}},7252:function(e,t){"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isPlainObject=function(e){if(!e||"object"!=typeof e)return!1;let t=Object.getPrototypeOf(e);return(null===t||t===Object.prototype||null===Object.getPrototypeOf(t))&&"[object Object]"===Object.prototype.toString.call(e)}},34526:function(e,t){"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isPrimitive=function(e){return null==e||"object"!=typeof e&&"function"!=typeof e}},23497:function(e,t){"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isTypedArray=function(e){return ArrayBuffer.isView(e)&&!(e instanceof DataView)}},87922:function(e,t,r){"use strict";r.d(t,{Z:function(){return q}});var n,o,i,a,l,u,c,s=r(5853),d=r(2265),f="right-scroll-bar-position",p="width-before-scroll-bar";function h(e,t){return"function"==typeof e?e(t):e&&(e.current=t),e}var v="undefined"!=typeof window?d.useLayoutEffect:d.useEffect,y=new WeakMap,m=(void 0===n&&(n={}),(void 0===o&&(o=function(e){return e}),i=[],a=!1,l={read:function(){if(a)throw Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return i.length?i[i.length-1]:null},useMedium:function(e){var t=o(e,a);return i.push(t),function(){i=i.filter(function(e){return e!==t})}},assignSyncMedium:function(e){for(a=!0;i.length;){var t=i;i=[],t.forEach(e)}i={push:function(t){return e(t)},filter:function(){return i}}},assignMedium:function(e){a=!0;var t=[];if(i.length){var r=i;i=[],r.forEach(e),t=i}var n=function(){var r=t;t=[],r.forEach(e)},o=function(){return Promise.resolve().then(n)};o(),i={push:function(e){t.push(e),o()},filter:function(e){return t=t.filter(e),i}}}}).options=(0,s.pi)({async:!0,ssr:!1},n),l),g=function(){},b=d.forwardRef(function(e,t){var r,n,o,i,a=d.useRef(null),l=d.useState({onScrollCapture:g,onWheelCapture:g,onTouchMoveCapture:g}),u=l[0],c=l[1],f=e.forwardProps,p=e.children,b=e.className,w=e.removeScrollBar,x=e.enabled,O=e.shards,j=e.sideCar,k=e.noRelative,P=e.noIsolation,E=e.inert,M=e.allowPinchZoom,S=e.as,C=e.gapMode,A=(0,s._T)(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),T=(r=[a,t],n=function(e){return r.forEach(function(t){return h(t,e)})},(o=(0,d.useState)(function(){return{value:null,callback:n,facade:{get current(){return o.value},set current(value){var e=o.value;e!==value&&(o.value=value,o.callback(value,e))}}}})[0]).callback=n,i=o.facade,v(function(){var e=y.get(i);if(e){var t=new Set(e),n=new Set(r),o=i.current;t.forEach(function(e){n.has(e)||h(e,null)}),n.forEach(function(e){t.has(e)||h(e,o)})}y.set(i,r)},[r]),i),D=(0,s.pi)((0,s.pi)({},A),u);return d.createElement(d.Fragment,null,x&&d.createElement(j,{sideCar:m,removeScrollBar:w,shards:O,noRelative:k,noIsolation:P,inert:E,setCallbacks:c,allowPinchZoom:!!M,lockRef:a,gapMode:C}),f?d.cloneElement(d.Children.only(p),(0,s.pi)((0,s.pi)({},D),{ref:T})):d.createElement(void 0===S?"div":S,(0,s.pi)({},D,{className:b,ref:T}),p))});b.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},b.classNames={fullWidth:p,zeroRight:f};var w=function(e){var t=e.sideCar,r=(0,s._T)(e,["sideCar"]);if(!t)throw Error("Sidecar: please provide `sideCar` property to import the right car");var n=t.read();if(!n)throw Error("Sidecar medium not found");return d.createElement(n,(0,s.pi)({},r))};w.isSideCarExport=!0;var x=function(){var e=0,t=null;return{add:function(n){if(0==e&&(t=function(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=c||r.nc;return t&&e.setAttribute("nonce",t),e}())){var o,i;(o=t).styleSheet?o.styleSheet.cssText=n:o.appendChild(document.createTextNode(n)),i=t,(document.head||document.getElementsByTagName("head")[0]).appendChild(i)}e++},remove:function(){--e||!t||(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},O=function(){var e=x();return function(t,r){d.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&r])}},j=function(){var e=O();return function(t){return e(t.styles,t.dynamic),null}},k={left:0,top:0,right:0,gap:0},P=function(e){return parseInt(e||"",10)||0},E=function(e){var t=window.getComputedStyle(document.body),r=t["padding"===e?"paddingLeft":"marginLeft"],n=t["padding"===e?"paddingTop":"marginTop"],o=t["padding"===e?"paddingRight":"marginRight"];return[P(r),P(n),P(o)]},M=function(e){if(void 0===e&&(e="margin"),"undefined"==typeof window)return k;var t=E(e),r=document.documentElement.clientWidth,n=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,n-r+t[2]-t[0])}},S=j(),C="data-scroll-locked",A=function(e,t,r,n){var o=e.left,i=e.top,a=e.right,l=e.gap;return void 0===r&&(r="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(n,";\n   padding-right: ").concat(l,"px ").concat(n,";\n  }\n  body[").concat(C,"] {\n    overflow: hidden ").concat(n,";\n    overscroll-behavior: contain;\n    ").concat([t&&"position: relative ".concat(n,";"),"margin"===r&&"\n    padding-left: ".concat(o,"px;\n    padding-top: ").concat(i,"px;\n    padding-right: ").concat(a,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(l,"px ").concat(n,";\n    "),"padding"===r&&"padding-right: ".concat(l,"px ").concat(n,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(f," {\n    right: ").concat(l,"px ").concat(n,";\n  }\n  \n  .").concat(p," {\n    margin-right: ").concat(l,"px ").concat(n,";\n  }\n  \n  .").concat(f," .").concat(f," {\n    right: 0 ").concat(n,";\n  }\n  \n  .").concat(p," .").concat(p," {\n    margin-right: 0 ").concat(n,";\n  }\n  \n  body[").concat(C,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(l,"px;\n  }\n")},T=function(){var e=parseInt(document.body.getAttribute(C)||"0",10);return isFinite(e)?e:0},D=function(){d.useEffect(function(){return document.body.setAttribute(C,(T()+1).toString()),function(){var e=T()-1;e<=0?document.body.removeAttribute(C):document.body.setAttribute(C,e.toString())}},[])},N=function(e){var t=e.noRelative,r=e.noImportant,n=e.gapMode,o=void 0===n?"margin":n;D();var i=d.useMemo(function(){return M(o)},[o]);return d.createElement(S,{styles:A(i,!t,o,r?"":"!important")})},_=!1;if("undefined"!=typeof window)try{var I=Object.defineProperty({},"passive",{get:function(){return _=!0,!0}});window.addEventListener("test",I,I),window.removeEventListener("test",I,I)}catch(e){_=!1}var R=!!_&&{passive:!1},L=function(e,t){if(!(e instanceof Element))return!1;var r=window.getComputedStyle(e);return"hidden"!==r[t]&&!(r.overflowY===r.overflowX&&"TEXTAREA"!==e.tagName&&"visible"===r[t])},z=function(e,t){var r=t.ownerDocument,n=t;do{if("undefined"!=typeof ShadowRoot&&n instanceof ShadowRoot&&(n=n.host),W(e,n)){var o=F(e,n);if(o[1]>o[2])return!0}n=n.parentNode}while(n&&n!==r.body);return!1},W=function(e,t){return"v"===e?L(t,"overflowY"):L(t,"overflowX")},F=function(e,t){return"v"===e?[t.scrollTop,t.scrollHeight,t.clientHeight]:[t.scrollLeft,t.scrollWidth,t.clientWidth]},B=function(e,t,r,n,o){var i,a=(i=window.getComputedStyle(t).direction,"h"===e&&"rtl"===i?-1:1),l=a*n,u=r.target,c=t.contains(u),s=!1,d=l>0,f=0,p=0;do{if(!u)break;var h=F(e,u),v=h[0],y=h[1]-h[2]-a*v;(v||y)&&W(e,u)&&(f+=y,p+=v);var m=u.parentNode;u=m&&m.nodeType===Node.DOCUMENT_FRAGMENT_NODE?m.host:m}while(!c&&u!==document.body||c&&(t.contains(u)||t===u));return d&&(o&&1>Math.abs(f)||!o&&l>f)?s=!0:!d&&(o&&1>Math.abs(p)||!o&&-l>p)&&(s=!0),s},K=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},V=function(e){return[e.deltaX,e.deltaY]},Z=function(e){return e&&"current"in e?e.current:e},U=0,$=[],H=(u=function(e){var t=d.useRef([]),r=d.useRef([0,0]),n=d.useRef(),o=d.useState(U++)[0],i=d.useState(j)[0],a=d.useRef(e);d.useEffect(function(){a.current=e},[e]),d.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(o));var t=(0,s.ev)([e.lockRef.current],(e.shards||[]).map(Z),!0).filter(Boolean);return t.forEach(function(e){return e.classList.add("allow-interactivity-".concat(o))}),function(){document.body.classList.remove("block-interactivity-".concat(o)),t.forEach(function(e){return e.classList.remove("allow-interactivity-".concat(o))})}}},[e.inert,e.lockRef.current,e.shards]);var l=d.useCallback(function(e,t){if("touches"in e&&2===e.touches.length||"wheel"===e.type&&e.ctrlKey)return!a.current.allowPinchZoom;var o,i=K(e),l=r.current,u="deltaX"in e?e.deltaX:l[0]-i[0],c="deltaY"in e?e.deltaY:l[1]-i[1],s=e.target,d=Math.abs(u)>Math.abs(c)?"h":"v";if("touches"in e&&"h"===d&&"range"===s.type)return!1;var f=z(d,s);if(!f)return!0;if(f?o=d:(o="v"===d?"h":"v",f=z(d,s)),!f)return!1;if(!n.current&&"changedTouches"in e&&(u||c)&&(n.current=o),!o)return!0;var p=n.current||o;return B(p,t,e,"h"===p?u:c,!0)},[]),u=d.useCallback(function(e){if($.length&&$[$.length-1]===i){var r="deltaY"in e?V(e):K(e),n=t.current.filter(function(t){var n;return t.name===e.type&&(t.target===e.target||e.target===t.shadowParent)&&(n=t.delta)[0]===r[0]&&n[1]===r[1]})[0];if(n&&n.should){e.cancelable&&e.preventDefault();return}if(!n){var o=(a.current.shards||[]).map(Z).filter(Boolean).filter(function(t){return t.contains(e.target)});(o.length>0?l(e,o[0]):!a.current.noIsolation)&&e.cancelable&&e.preventDefault()}}},[]),c=d.useCallback(function(e,r,n,o){var i={name:e,delta:r,target:n,should:o,shadowParent:function(e){for(var t=null;null!==e;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}(n)};t.current.push(i),setTimeout(function(){t.current=t.current.filter(function(e){return e!==i})},1)},[]),f=d.useCallback(function(e){r.current=K(e),n.current=void 0},[]),p=d.useCallback(function(t){c(t.type,V(t),t.target,l(t,e.lockRef.current))},[]),h=d.useCallback(function(t){c(t.type,K(t),t.target,l(t,e.lockRef.current))},[]);d.useEffect(function(){return $.push(i),e.setCallbacks({onScrollCapture:p,onWheelCapture:p,onTouchMoveCapture:h}),document.addEventListener("wheel",u,R),document.addEventListener("touchmove",u,R),document.addEventListener("touchstart",f,R),function(){$=$.filter(function(e){return e!==i}),document.removeEventListener("wheel",u,R),document.removeEventListener("touchmove",u,R),document.removeEventListener("touchstart",f,R)}},[]);var v=e.removeScrollBar,y=e.inert;return d.createElement(d.Fragment,null,y?d.createElement(i,{styles:"\n  .block-interactivity-".concat(o," {pointer-events: none;}\n  .allow-interactivity-").concat(o," {pointer-events: all;}\n")}):null,v?d.createElement(N,{noRelative:e.noRelative,gapMode:e.gapMode}):null)},m.useMedium(u),w),Y=d.forwardRef(function(e,t){return d.createElement(b,(0,s.pi)({},e,{ref:t,sideCar:H}))});Y.classNames=b.classNames;var q=Y},46595:function(e,t,r){"use strict";r.d(t,{r:function(){return N}});var n=r(2265),o=r(38450),i=r.n(o),a=(e,t)=>[0,3*e,3*t-6*e,3*e-3*t+1],l=(e,t)=>e.map((e,r)=>e*t**r).reduce((e,t)=>e+t),u=(e,t)=>r=>l(a(e,t),r),c=(e,t)=>r=>l([...a(e,t).map((e,t)=>e*t).slice(1),0],r),s=function(){for(var e,t,r,n,o=arguments.length,i=Array(o),a=0;a<o;a++)i[a]=arguments[a];if(1===i.length)switch(i[0]){case"linear":[e,r,t,n]=[0,0,1,1];break;case"ease":[e,r,t,n]=[.25,.1,.25,1];break;case"ease-in":[e,r,t,n]=[.42,0,1,1];break;case"ease-out":[e,r,t,n]=[.42,0,.58,1];break;case"ease-in-out":[e,r,t,n]=[0,0,.58,1];break;default:var l=i[0].split("(");"cubic-bezier"===l[0]&&4===l[1].split(")")[0].split(",").length&&([e,r,t,n]=l[1].split(")")[0].split(",").map(e=>parseFloat(e)))}else 4===i.length&&([e,r,t,n]=i);var s=u(e,t),d=u(r,n),f=c(e,t),p=e=>e>1?1:e<0?0:e,h=e=>{for(var t=e>1?1:e,r=t,n=0;n<8;++n){var o=s(r)-t,i=f(r);if(1e-4>Math.abs(o-t)||i<1e-4)break;r=p(r-o/i)}return d(r)};return h.isStepper=!1,h},d=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},{stiff:t=100,damping:r=8,dt:n=17}=e,o=(e,o,i)=>{var a=i+(-(e-o)*t-i*r)*n/1e3,l=i*n/1e3+e;return 1e-4>Math.abs(l-o)&&1e-4>Math.abs(a)?[o,0]:[l,a]};return o.isStepper=!0,o.dt=n,o},f=e=>{if("string"==typeof e)switch(e){case"ease":case"ease-in-out":case"ease-out":case"ease-in":case"linear":return s(e);case"spring":return d();default:if("cubic-bezier"===e.split("(")[0])return s(e)}return"function"==typeof e?e:null};function p(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function h(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?p(Object(r),!0).forEach(function(t){var n,o;n=t,o=r[t],(n=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(n))in e?Object.defineProperty(e,n,{value:o,enumerable:!0,configurable:!0,writable:!0}):e[n]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):p(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var v=e=>e.replace(/([A-Z])/g,e=>"-".concat(e.toLowerCase())),y=(e,t,r)=>e.map(e=>"".concat(v(e)," ").concat(t,"ms ").concat(r)).join(","),m=(e,t)=>[Object.keys(e),Object.keys(t)].reduce((e,t)=>e.filter(e=>t.includes(e))),g=(e,t)=>Object.keys(t).reduce((r,n)=>h(h({},r),{},{[n]:e(n,t[n])}),{});function b(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function w(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?b(Object(r),!0).forEach(function(t){var n,o;n=t,o=r[t],(n=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(n))in e?Object.defineProperty(e,n,{value:o,enumerable:!0,configurable:!0,writable:!0}):e[n]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):b(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var x=(e,t,r)=>e+(t-e)*r,O=e=>{var{from:t,to:r}=e;return t!==r},j=(e,t,r)=>{var n=g((t,r)=>{if(O(r)){var[n,o]=e(r.from,r.to,r.velocity);return w(w({},r),{},{from:n,velocity:o})}return r},t);return r<1?g((e,t)=>O(t)?w(w({},t),{},{velocity:x(t.velocity,n[e].velocity,r),from:x(t.from,n[e].from,r)}):t,t):j(e,n,r-1)},k=(e,t,r,n,o,i)=>{var a,l,u,c,s,d,f,p,h,v,y=m(e,t);return!0===r.isStepper?(l=y.reduce((r,n)=>w(w({},r),{},{[n]:{from:e[n],velocity:0,to:t[n]}}),{}),u=()=>g((e,t)=>t.from,l),c=()=>!Object.values(l).filter(O).length,s=null,d=n=>{a||(a=n);var f=(n-a)/r.dt;l=j(r,l,f),o(w(w(w({},e),t),u())),a=n,c()||(s=i.setTimeout(d))},()=>(s=i.setTimeout(d),()=>{s()})):(p=null,h=y.reduce((r,n)=>w(w({},r),{},{[n]:[e[n],t[n]]}),{}),v=a=>{f||(f=a);var l=(a-f)/n,u=g((e,t)=>x(...t,r(l)),h);if(o(w(w(w({},e),t),u)),l<1)p=i.setTimeout(v);else{var c=g((e,t)=>x(...t,r(1)),h);o(w(w(w({},e),t),c))}},()=>(p=i.setTimeout(v),()=>{p()}))};class P{setTimeout(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,r=performance.now(),n=null,o=i=>{i-r>=t?e(i):"function"==typeof requestAnimationFrame&&(n=requestAnimationFrame(o))};return n=requestAnimationFrame(o),()=>{cancelAnimationFrame(n)}}}var E=["children","begin","duration","attributeName","easing","isActive","from","to","canBegin","onAnimationEnd","shouldReAnimate","onAnimationReStart","animationManager"];function M(){return(M=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function S(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function C(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?S(Object(r),!0).forEach(function(t){A(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):S(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function A(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}class T extends n.PureComponent{componentDidMount(){var{isActive:e,canBegin:t}=this.props;this.mounted=!0,e&&t&&this.runAnimation(this.props)}componentDidUpdate(e){var{isActive:t,canBegin:r,attributeName:n,shouldReAnimate:o,to:a,from:l}=this.props,{style:u}=this.state;if(r){if(!t){this.state&&u&&(n&&u[n]!==a||!n&&u!==a)&&this.setState({style:n?{[n]:a}:a});return}if(!i()(e.to,a)||!e.canBegin||!e.isActive){var c=!e.canBegin||!e.isActive;this.manager.stop(),this.stopJSAnimation&&this.stopJSAnimation();var s=c||o?l:e.to;this.state&&u&&(n&&u[n]!==s||!n&&u!==s)&&this.setState({style:n?{[n]:s}:s}),this.runAnimation(C(C({},this.props),{},{from:s,begin:0}))}}}componentWillUnmount(){this.mounted=!1;var{onAnimationEnd:e}=this.props;this.unSubscribe&&this.unSubscribe(),this.manager.stop(),this.stopJSAnimation&&this.stopJSAnimation(),e&&e()}handleStyleChange(e){this.changeStyle(e)}changeStyle(e){this.mounted&&this.setState({style:e})}runJSAnimation(e){var{from:t,to:r,duration:n,easing:o,begin:i,onAnimationEnd:a,onAnimationStart:l}=e,u=k(t,r,f(o),n,this.changeStyle,this.manager.getTimeoutController());this.manager.start([l,i,()=>{this.stopJSAnimation=u()},n,a])}runAnimation(e){var{begin:t,duration:r,attributeName:n,to:o,easing:i,onAnimationStart:a,onAnimationEnd:l,children:u}=e;if(this.unSubscribe=this.manager.subscribe(this.handleStyleChange),"function"==typeof i||"function"==typeof u||"spring"===i){this.runJSAnimation(e);return}var c=n?{[n]:o}:o,s=y(Object.keys(c),r,i);this.manager.start([a,t,C(C({},c),{},{transition:s}),r,l])}render(){var e=this.props,{children:t,begin:r,duration:o,attributeName:i,easing:a,isActive:l,from:u,to:c,canBegin:s,onAnimationEnd:d,shouldReAnimate:f,onAnimationReStart:p,animationManager:h}=e,v=function(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}(e,E),y=n.Children.count(t),m=this.state.style;if("function"==typeof t)return t(m);if(!l||0===y||o<=0)return t;var g=e=>{var{style:t={},className:r}=e.props;return(0,n.cloneElement)(e,C(C({},v),{},{style:C(C({},t),m),className:r}))};return 1===y?g(n.Children.only(t)):n.createElement("div",null,n.Children.map(t,e=>g(e)))}constructor(e,t){super(e,t),A(this,"mounted",!1),A(this,"manager",null),A(this,"stopJSAnimation",null),A(this,"unSubscribe",null);var{isActive:r,attributeName:n,from:o,to:i,children:a,duration:l,animationManager:u}=this.props;if(this.manager=u,this.handleStyleChange=this.handleStyleChange.bind(this),this.changeStyle=this.changeStyle.bind(this),!r||l<=0){this.state={style:{}},"function"==typeof a&&(this.state={style:i});return}if(o){if("function"==typeof a){this.state={style:o};return}this.state={style:n?{[n]:o}:o}}else this.state={style:{}}}}A(T,"displayName","Animate"),A(T,"defaultProps",{begin:0,duration:1e3,attributeName:"",easing:"ease",isActive:!0,canBegin:!0,onAnimationEnd:()=>{},onAnimationStart:()=>{}});var D=(0,n.createContext)(null);function N(e){var t,r,o,i,a,l,u,c=(0,n.useContext)(D);return n.createElement(T,M({},e,{animationManager:null!==(l=null!==(u=e.animationManager)&&void 0!==u?u:c)&&void 0!==l?l:(t=new P,r=()=>null,o=!1,i=null,a=e=>{if(!o){if(Array.isArray(e)){if(!e.length)return;var[n,...l]=e;if("number"==typeof n){i=t.setTimeout(a.bind(null,l),n);return}a(n),i=t.setTimeout(a.bind(null,l));return}"object"==typeof e&&r(e),"function"==typeof e&&e()}},{stop:()=>{o=!0},start:e=>{o=!1,i&&(i(),i=null),a(e)},subscribe:e=>(r=e,()=>{r=()=>null}),getTimeoutController:()=>t})}))}},32447:function(e,t,r){"use strict";r.d(t,{$:function(){return eq},u:function(){return eY}});var n=r(2265),o=r(61994),i=r(9841),a=r(82944),l=r(13790),u=r(58735),c=["children"],s=()=>{},d=(0,n.createContext)({addErrorBar:s,removeErrorBar:s}),f=(0,n.createContext)({data:[],xAxisId:"xAxis-0",yAxisId:"yAxis-0",dataPointFormatter:()=>({x:0,y:0,value:0}),errorBarOffset:0});function p(e){var{children:t}=e,r=function(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}(e,c);return n.createElement(f.Provider,{value:r},t)}var h=()=>(0,n.useContext)(f),v=e=>{var{children:t,xAxisId:r,yAxisId:o,zAxisId:i,dataKey:a,data:c,stackId:s,hide:f,type:p,barSize:h}=e,[v,y]=n.useState([]),m=(0,n.useCallback)(e=>{y(t=>[...t,e])},[y]),g=(0,n.useCallback)(e=>{y(t=>t.filter(t=>t!==e))},[y]),b=(0,u.W)();return n.createElement(d.Provider,{value:{addErrorBar:m,removeErrorBar:g}},n.createElement(l.V,{type:p,data:c,xAxisId:r,yAxisId:o,zAxisId:i,dataKey:a,errorBars:v,stackId:s,hide:f,barSize:h,isPanorama:b}),t)};function y(e){var{addErrorBar:t,removeErrorBar:r}=(0,n.useContext)(d);return(0,n.useEffect)(()=>(t(e),()=>{r(e)}),[t,r,e]),null}var m=r(98628),g=r(39040),b=e=>{var t=(0,u.W)();return(0,g.C)(r=>(0,m.AS)(r,"xAxis",e,t))},w=e=>{var t=(0,u.W)();return(0,g.C)(r=>(0,m.AS)(r,"yAxis",e,t))},x=r(40130),O=r(46595),j=["direction","width","dataKey","isAnimationActive","animationBegin","animationDuration","animationEasing"];function k(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function P(){return(P=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function E(e){var{direction:t,width:r,dataKey:o,isAnimationActive:l,animationBegin:u,animationDuration:c,animationEasing:s}=e,d=function(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}(e,j),f=(0,a.L6)(d,!1),{data:p,dataPointFormatter:v,xAxisId:y,yAxisId:m,errorBarOffset:g}=h(),x=b(y),k=w(m);if((null==x?void 0:x.scale)==null||(null==k?void 0:k.scale)==null||null==p||"x"===t&&"number"!==x.type)return null;var E=p.map(e=>{var a,d,{x:p,y:h,value:y,errorVal:m}=v(e,o,t);if(!m)return null;var b=[];if(Array.isArray(m)?[a,d]=m:a=d=m,"x"===t){var{scale:w}=x,j=h+g,E=j+r,M=j-r,S=w(y-a),C=w(y+d);b.push({x1:C,y1:E,x2:C,y2:M}),b.push({x1:S,y1:j,x2:C,y2:j}),b.push({x1:S,y1:E,x2:S,y2:M})}else if("y"===t){var{scale:A}=k,T=p+g,D=T-r,N=T+r,_=A(y-a),I=A(y+d);b.push({x1:D,y1:I,x2:N,y2:I}),b.push({x1:T,y1:_,x2:T,y2:I}),b.push({x1:D,y1:_,x2:N,y2:_})}var R="".concat(p+g,"px ").concat(h+g,"px");return n.createElement(i.m,P({className:"recharts-errorBar",key:"bar-".concat(b.map(e=>"".concat(e.x1,"-").concat(e.x2,"-").concat(e.y1,"-").concat(e.y2)))},f),b.map(e=>{var t=l?{transformOrigin:"".concat(e.x1-5,"px")}:void 0;return n.createElement(O.r,{from:{transform:"scaleY(0)",transformOrigin:R},to:{transform:"scaleY(1)",transformOrigin:R},begin:u,easing:s,isActive:l,duration:c,key:"line-".concat(e.x1,"-").concat(e.x2,"-").concat(e.y1,"-").concat(e.y2),style:{transformOrigin:R}},n.createElement("line",P({},e,{style:t})))}))});return n.createElement(i.m,{className:"recharts-errorBars"},E)}var M=(0,n.createContext)(void 0);function S(e){var{direction:t,children:r}=e;return n.createElement(M.Provider,{value:t},r)}var C={stroke:"black",strokeWidth:1.5,width:5,offset:0,isAnimationActive:!0,animationBegin:0,animationDuration:400,animationEasing:"ease-in-out"};function A(e){var t,r,o=(t=e.direction,r=(0,n.useContext)(M),null!=t?t:null!=r?r:"x"),{width:i,isAnimationActive:a,animationBegin:l,animationDuration:u,animationEasing:c}=(0,x.j)(e,C);return n.createElement(n.Fragment,null,n.createElement(y,{dataKey:e.dataKey,direction:o}),n.createElement(E,P({},e,{direction:o,width:i,isAnimationActive:a,animationBegin:l,animationDuration:u,animationEasing:c})))}class T extends n.Component{render(){return n.createElement(A,this.props)}}k(T,"defaultProps",C),k(T,"displayName","ErrorBar");var D=r(20407),N=r(37618),_=r.n(N),I=r(26680),R=r(49037),L=r(16630),z=["valueAccessor"],W=["data","dataKey","clockWise","id","textBreakAll"];function F(){return(F=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function B(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function K(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?B(Object(r),!0).forEach(function(t){var n,o;n=t,o=r[t],(n=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(n))in e?Object.defineProperty(e,n,{value:o,enumerable:!0,configurable:!0,writable:!0}):e[n]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):B(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function V(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}var Z=e=>Array.isArray(e.value)?_()(e.value):e.value;function U(e){var{valueAccessor:t=Z}=e,r=V(e,z),{data:o,dataKey:l,clockWise:u,id:c,textBreakAll:s}=r,d=V(r,W);return o&&o.length?n.createElement(i.m,{className:"recharts-label-list"},o.map((e,r)=>{var o=(0,L.Rw)(l)?t(e,r):(0,R.F$)(e&&e.payload,l),i=(0,L.Rw)(c)?{}:{id:"".concat(c,"-").concat(r)};return n.createElement(I._,F({},(0,a.L6)(e,!0),d,i,{parentViewBox:e.parentViewBox,value:o,textBreakAll:s,viewBox:I._.parseViewBox((0,L.Rw)(u)?e:K(K({},e),{},{clockWise:u})),key:"label-".concat(r),index:r}))})):null}U.displayName="LabelList",U.renderCallByParent=function(e,t){var r,o=!(arguments.length>2)||void 0===arguments[2]||arguments[2];if(!e||!e.children&&o&&!e.label)return null;var{children:i}=e,l=(0,a.NN)(i,U).map((e,r)=>(0,n.cloneElement)(e,{data:t,key:"labelList-".concat(r)}));return o?[(r=e.label)?!0===r?n.createElement(U,{key:"labelList-implicit",data:t}):n.isValidElement(r)||(0,I.d)(r)?n.createElement(U,{key:"labelList-implicit",data:t,content:r}):"object"==typeof r?n.createElement(U,F({data:t},r,{key:"labelList-implicit"})):null:null,...l]:l};var $=r(34067),H=r(41637),Y=r(80503),q=["x","y"];function X(){return(X=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function G(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function Q(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?G(Object(r),!0).forEach(function(t){var n,o;n=t,o=r[t],(n=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(n))in e?Object.defineProperty(e,n,{value:o,enumerable:!0,configurable:!0,writable:!0}):e[n]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):G(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function J(e,t){var{x:r,y:n}=e,o=function(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}(e,q),i=parseInt("".concat(r),10),a=parseInt("".concat(n),10),l=parseInt("".concat(t.height||o.height),10),u=parseInt("".concat(t.width||o.width),10);return Q(Q(Q(Q(Q({},t),o),i?{x:i}:{}),a?{y:a}:{}),{},{height:l,width:u,name:t.name,radius:t.radius})}function ee(e){return n.createElement(Y.b,X({shapeType:"rectangle",propTransformer:J,activeClassName:"recharts-active-bar"},e))}var et=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return(r,n)=>{if((0,L.hj)(e))return e;var o=(0,L.hj)(r)||(0,L.Rw)(r);return o?e(r,n):(o||function(e,t){if(!e)throw Error("Invariant failed")}(!1),t)}},er=r(44296),en=r(35623),eo=r(19579),ei=()=>{var e=(0,g.T)();return(0,n.useEffect)(()=>(e((0,eo.a1)()),()=>{e((0,eo.nF)())})),null},ea=r(35953);function el(e,t){var r,n,o=(0,g.C)(t=>(0,m.i9)(t,e)),i=(0,g.C)(e=>(0,m.t)(e,t)),a=null!==(r=null==o?void 0:o.allowDataOverflow)&&void 0!==r?r:m.dW.allowDataOverflow,l=null!==(n=null==i?void 0:i.allowDataOverflow)&&void 0!==n?n:m.RN.allowDataOverflow;return{needClip:a||l,needClipX:a,needClipY:l}}function eu(e){var{xAxisId:t,yAxisId:r,clipPathId:o}=e,i=(0,ea.qD)(),{needClipX:a,needClipY:l,needClip:u}=el(t,r);if(!u)return null;var{left:c,top:s,width:d,height:f}=i;return n.createElement("clipPath",{id:"clipPath-".concat(o)},n.createElement("rect",{x:a?c:c-d/2,y:l?s:s-f/2,width:a?d:2*d,height:l?f:2*f}))}var ec=r(92713),es=r(22932),ed=r(36289),ef=r(33968),ep=r(66395);function eh(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function ev(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?eh(Object(r),!0).forEach(function(t){var n,o;n=t,o=r[t],(n=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(n))in e?Object.defineProperty(e,n,{value:o,enumerable:!0,configurable:!0,writable:!0}):e[n]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):eh(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var ey=(e,t,r,n,o)=>o,em=(e,t,r)=>{var n=null!=r?r:e;if(!(0,L.Rw)(n))return(0,L.h1)(n,t,0)},eg=(0,ec.P1)([ea.rE,m.bm,(e,t)=>t,(e,t,r)=>r,(e,t,r,n)=>n],(e,t,r,n,o)=>t.filter(t=>"horizontal"===e?t.xAxisId===r:t.yAxisId===n).filter(e=>e.isPanorama===o).filter(e=>!1===e.hide).filter(e=>"bar"===e.type));function eb(e){return null!=e.stackId&&null!=e.dataKey}var ew=(0,ec.P1)([eg,ef.X8,(e,t,r)=>"horizontal"===(0,ea.rE)(e)?(0,m.Lu)(e,"xAxis",t):(0,m.Lu)(e,"yAxis",r)],(e,t,r)=>{var n=e.filter(eb),o=e.filter(e=>null==e.stackId);return[...Object.entries(n.reduce((e,t)=>(e[t.stackId]||(e[t.stackId]=[]),e[t.stackId].push(t),e),{})).map(e=>{var[n,o]=e;return{stackId:n,dataKeys:o.map(e=>e.dataKey),barSize:em(t,r,o[0].barSize)}}),...o.map(e=>({stackId:void 0,dataKeys:[e.dataKey].filter(e=>null!=e),barSize:em(t,r,e.barSize)}))]}),ex=(e,t,r,n)=>{var o,i;return"horizontal"===(0,ea.rE)(e)?(o=(0,m.AS)(e,"xAxis",t,n),i=(0,m.bY)(e,"xAxis",t,n)):(o=(0,m.AS)(e,"yAxis",r,n),i=(0,m.bY)(e,"yAxis",r,n)),(0,R.zT)(o,i)},eO=(0,ec.P1)([ew,ef.qy,ef.wK,ef.sd,(e,t,r,n,o)=>{var i,a,l,u,c=(0,ea.rE)(e),s=(0,ef.qy)(e),{maxBarSize:d}=o,f=(0,L.Rw)(d)?s:d;return"horizontal"===c?(l=(0,m.AS)(e,"xAxis",t,n),u=(0,m.bY)(e,"xAxis",t,n)):(l=(0,m.AS)(e,"yAxis",r,n),u=(0,m.bY)(e,"yAxis",r,n)),null!==(i=null!==(a=(0,R.zT)(l,u,!0))&&void 0!==a?a:f)&&void 0!==i?i:0},ex,(e,t,r,n,o)=>o.maxBarSize],(e,t,r,n,o,i,a)=>{var l=function(e,t,r,n,o){var i,a=n.length;if(!(a<1)){var l=(0,L.h1)(e,r,0,!0),u=[];if((0,ep.n)(n[0].barSize)){var c=!1,s=r/a,d=n.reduce((e,t)=>e+(t.barSize||0),0);(d+=(a-1)*l)>=r&&(d-=(a-1)*l,l=0),d>=r&&s>0&&(c=!0,s*=.9,d=a*s);var f={offset:((r-d)/2>>0)-l,size:0};i=n.reduce((e,t)=>{var r,n=[...e,{stackId:t.stackId,dataKeys:t.dataKeys,position:{offset:f.offset+f.size+l,size:c?s:null!==(r=t.barSize)&&void 0!==r?r:0}}];return f=n[n.length-1].position,n},u)}else{var p=(0,L.h1)(t,r,0,!0);r-2*p-(a-1)*l<=0&&(l=0);var h=(r-2*p-(a-1)*l)/a;h>1&&(h>>=0);var v=(0,ep.n)(o)?Math.min(h,o):h;i=n.reduce((e,t,r)=>[...e,{stackId:t.stackId,dataKeys:t.dataKeys,position:{offset:p+(h+l)*r+(h-v)/2,size:v}}],u)}return i}}(r,n,o!==i?o:i,e,(0,L.Rw)(a)?t:a);return o!==i&&null!=l&&(l=l.map(e=>ev(ev({},e),{},{position:ev(ev({},e.position),{},{offset:e.position.offset-o/2})}))),l}),ej=(0,ec.P1)([eO,ey],(e,t)=>{if(null!=e){var r=e.find(e=>e.stackId===t.stackId&&e.dataKeys.includes(t.dataKey));if(null!=r)return r.position}}),ek=(0,ec.P1)([m.bm,ey],(e,t)=>{if(e.some(e=>"bar"===e.type&&t.dataKey===e.dataKey&&t.stackId===e.stackId&&t.stackId===e.stackId))return t}),eP=(0,ec.P1)([(e,t,r,n)=>"horizontal"===(0,ea.rE)(e)?(0,m.g6)(e,"yAxis",r,n):(0,m.g6)(e,"xAxis",t,n),ey],(e,t)=>{if(e&&(null==t?void 0:t.dataKey)!=null){var{stackId:r}=t;if(null!=r){var n=e[r];if(n){var{stackedData:o}=n;if(o)return o.find(e=>e.key===t.dataKey)}}}}),eE=(0,ec.P1)([ed.bX,(e,t,r,n)=>(0,m.AS)(e,"xAxis",t,n),(e,t,r,n)=>(0,m.AS)(e,"yAxis",r,n),(e,t,r,n)=>(0,m.bY)(e,"xAxis",t,n),(e,t,r,n)=>(0,m.bY)(e,"yAxis",r,n),ej,ea.rE,es.hA,ex,eP,ek,(e,t,r,n,o,i)=>i],(e,t,r,n,o,i,a,l,u,c,s,d)=>{var f,{chartData:p,dataStartIndex:h,dataEndIndex:v}=l;if(null!=s&&null!=i&&("horizontal"===a||"vertical"===a)&&null!=t&&null!=r&&null!=n&&null!=o&&null!=u){var{data:y}=s;if(null!=(f=null!=y&&y.length>0?y:null==p?void 0:p.slice(h,v+1)))return eY({layout:a,barSettings:s,pos:i,bandSize:u,xAxis:t,yAxis:r,xAxisTicks:n,yAxisTicks:o,stackedData:c,displayedData:f,offset:e,cells:d})}}),eM=r(31944),eS=r(62658),eC=r(59087),eA=["onMouseEnter","onMouseLeave","onClick"],eT=["value","background","tooltipPosition"],eD=["onMouseEnter","onClick","onMouseLeave"];function eN(){return(eN=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function e_(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function eI(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?e_(Object(r),!0).forEach(function(t){eR(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):e_(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function eR(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function eL(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}var ez=e=>{var{dataKey:t,name:r,fill:n,legendType:o,hide:i}=e;return[{inactive:i,dataKey:t,type:o,color:n,value:(0,R.hn)(r,t),payload:e}]};function eW(e){var{dataKey:t,stroke:r,strokeWidth:n,fill:o,name:i,hide:a,unit:l}=e;return{dataDefinedOnItem:void 0,positions:void 0,settings:{stroke:r,strokeWidth:n,fill:o,dataKey:t,nameKey:void 0,name:(0,R.hn)(i,t),hide:a,type:e.tooltipType,color:e.fill,unit:l}}}function eF(e){var t=(0,g.C)(eM.Ve),{data:r,dataKey:o,background:i,allOtherBarProps:l}=e,{onMouseEnter:u,onMouseLeave:c,onClick:s}=l,d=eL(l,eA),f=(0,er.Df)(u,o),p=(0,er.oQ)(c),h=(0,er.nC)(s,o);if(!i||null==r)return null;var v=(0,a.L6)(i,!1);return n.createElement(n.Fragment,null,r.map((e,r)=>{var{value:a,background:l,tooltipPosition:u}=e,c=eL(e,eT);if(!l)return null;var s=f(e,r),y=p(e,r),m=h(e,r),g=eI(eI(eI(eI(eI({option:i,isActive:String(r)===t},c),{},{fill:"#eee"},l),v),(0,H.bw)(d,e,r)),{},{onMouseEnter:s,onMouseLeave:y,onClick:m,dataKey:o,index:r,className:"recharts-bar-background-rectangle"});return n.createElement(ee,eN({key:"background-bar-".concat(r)},g))}))}function eB(e){var{data:t,props:r,showLabels:o}=e,l=(0,a.L6)(r,!1),{shape:u,dataKey:c,activeBar:s}=r,d=(0,g.C)(eM.Ve),f=(0,g.C)(eM.du),{onMouseEnter:p,onClick:h,onMouseLeave:v}=r,y=eL(r,eD),m=(0,er.Df)(p,c),b=(0,er.oQ)(v),w=(0,er.nC)(h,c);return t?n.createElement(n.Fragment,null,t.map((e,t)=>{var r=s&&String(t)===d&&(null==f||c===f),o=eI(eI(eI({},l),e),{},{isActive:r,option:r?s:u,index:t,dataKey:c});return n.createElement(i.m,eN({className:"recharts-bar-rectangle"},(0,H.bw)(y,e,t),{onMouseEnter:m(e,t),onMouseLeave:b(e,t),onClick:w(e,t),key:"rectangle-".concat(null==e?void 0:e.x,"-").concat(null==e?void 0:e.y,"-").concat(null==e?void 0:e.value,"-").concat(t)}),n.createElement(ee,o))}),o&&U.renderCallByParent(r,t)):null}function eK(e){var{props:t,previousRectanglesRef:r}=e,{data:o,layout:a,isAnimationActive:l,animationBegin:u,animationDuration:c,animationEasing:s,onAnimationEnd:d,onAnimationStart:f}=t,p=r.current,h=(0,eC.i)(t,"recharts-bar-"),[v,y]=(0,n.useState)(!1),m=(0,n.useCallback)(()=>{"function"==typeof d&&d(),y(!1)},[d]),g=(0,n.useCallback)(()=>{"function"==typeof f&&f(),y(!0)},[f]);return n.createElement(O.r,{begin:u,duration:c,isActive:l,easing:s,from:{t:0},to:{t:1},onAnimationEnd:m,onAnimationStart:g,key:h},e=>{var{t:l}=e,u=1===l?o:o.map((e,t)=>{var r=p&&p[t];if(r){var n=(0,L.k4)(r.x,e.x),o=(0,L.k4)(r.y,e.y),i=(0,L.k4)(r.width,e.width),u=(0,L.k4)(r.height,e.height);return eI(eI({},e),{},{x:n(l),y:o(l),width:i(l),height:u(l)})}if("horizontal"===a){var c=(0,L.k4)(0,e.height)(l);return eI(eI({},e),{},{y:e.y+e.height-c,height:c})}var s=(0,L.k4)(0,e.width)(l);return eI(eI({},e),{},{width:s})});return l>0&&(r.current=u),n.createElement(i.m,null,n.createElement(eB,{props:t,data:u,showLabels:!v}))})}function eV(e){var{data:t,isAnimationActive:r}=e,o=(0,n.useRef)(null);return r&&t&&t.length&&(null==o.current||o.current!==t)?n.createElement(eK,{previousRectanglesRef:o,props:e}):n.createElement(eB,{props:e,data:t,showLabels:!0})}var eZ=(e,t)=>{var r=Array.isArray(e.value)?e.value[1]:e.value;return{x:e.x,y:e.y,value:r,errorVal:(0,R.F$)(e,t)}};class eU extends n.PureComponent{render(){var{hide:e,data:t,dataKey:r,className:a,xAxisId:l,yAxisId:u,needClip:c,background:s,id:d,layout:f}=this.props;if(e)return null;var p=(0,o.W)("recharts-bar",a),h=(0,L.Rw)(d)?this.id:d;return n.createElement(i.m,{className:p},c&&n.createElement("defs",null,n.createElement(eu,{clipPathId:h,xAxisId:l,yAxisId:u})),n.createElement(i.m,{className:"recharts-bar-rectangles",clipPath:c?"url(#clipPath-".concat(h,")"):null},n.createElement(eF,{data:t,dataKey:r,background:s,allOtherBarProps:this.props}),n.createElement(eV,this.props)),n.createElement(S,{direction:"horizontal"===f?"y":"x"},this.props.children))}constructor(){super(...arguments),eR(this,"id",(0,L.EL)("recharts-bar-"))}}var e$={activeBar:!1,animationBegin:0,animationDuration:400,animationEasing:"ease",hide:!1,isAnimationActive:!$.x.isSsr,legendType:"rect",minPointSize:0,xAxisId:0,yAxisId:0};function eH(e){var t,{xAxisId:r,yAxisId:o,hide:i,legendType:l,minPointSize:c,activeBar:s,animationBegin:d,animationDuration:f,animationEasing:h,isAnimationActive:v}=(0,x.j)(e,e$),{needClip:y}=el(r,o),m=(0,ea.vn)(),b=(0,u.W)(),w=(0,n.useMemo)(()=>({barSize:e.barSize,data:void 0,dataKey:e.dataKey,maxBarSize:e.maxBarSize,minPointSize:c,stackId:(0,R.GA)(e.stackId)}),[e.barSize,e.dataKey,e.maxBarSize,c,e.stackId]),O=(0,a.NN)(e.children,D.b),j=(0,g.C)(e=>eE(e,r,o,b,w,O));if("vertical"!==m&&"horizontal"!==m)return null;var k=null==j?void 0:j[0];return t=null==k||null==k.height||null==k.width?0:"vertical"===m?k.height/2:k.width/2,n.createElement(p,{xAxisId:r,yAxisId:o,data:j,dataPointFormatter:eZ,errorBarOffset:t},n.createElement(eU,eN({},e,{layout:m,needClip:y,data:j,xAxisId:r,yAxisId:o,hide:i,legendType:l,minPointSize:c,activeBar:s,animationBegin:d,animationDuration:f,animationEasing:h,isAnimationActive:v})))}function eY(e){var{layout:t,barSettings:{dataKey:r,minPointSize:n},pos:o,bandSize:i,xAxis:a,yAxis:l,xAxisTicks:u,yAxisTicks:c,stackedData:s,displayedData:d,offset:f,cells:p}=e,h="horizontal"===t?l:a,v=s?h.scale.domain():null,y=(0,R.Yj)({numericAxis:h});return d.map((e,d)=>{s?m=(0,R.Vv)(s[d],v):Array.isArray(m=(0,R.F$)(e,r))||(m=[y,m]);var h=et(n,0)(m[1],d);if("horizontal"===t){var m,g,b,w,x,O,j,[k,P]=[l.scale(m[0]),l.scale(m[1])];g=(0,R.Fy)({axis:a,ticks:u,bandSize:i,offset:o.offset,entry:e,index:d}),b=null!==(j=null!=P?P:k)&&void 0!==j?j:void 0,w=o.size;var E=k-P;if(x=(0,L.In)(E)?0:E,O={x:g,y:f.top,width:w,height:f.height},Math.abs(h)>0&&Math.abs(x)<Math.abs(h)){var M=(0,L.uY)(x||h)*(Math.abs(h)-Math.abs(x));b-=M,x+=M}}else{var[S,C]=[a.scale(m[0]),a.scale(m[1])];if(g=S,b=(0,R.Fy)({axis:l,ticks:c,bandSize:i,offset:o.offset,entry:e,index:d}),w=C-S,x=o.size,O={x:f.left,y:b,width:f.width,height:x},Math.abs(h)>0&&Math.abs(w)<Math.abs(h)){var A=(0,L.uY)(w||h)*(Math.abs(h)-Math.abs(w));w+=A}}return eI(eI({},e),{},{x:g,y:b,width:w,height:x,value:s?m:m[1],payload:e,background:O,tooltipPosition:{x:g+w/2,y:b+x/2}},p&&p[d]&&p[d].props)})}class eq extends n.PureComponent{render(){return n.createElement(v,{type:"bar",data:null,xAxisId:this.props.xAxisId,yAxisId:this.props.yAxisId,zAxisId:0,dataKey:this.props.dataKey,stackId:this.props.stackId,hide:this.props.hide,barSize:this.props.barSize},n.createElement(ei,null),n.createElement(eS.L,{legendPayload:ez(this.props)}),n.createElement(en.k,{fn:eW,args:this.props}),n.createElement(eH,this.props))}}eR(eq,"displayName","Bar"),eR(eq,"defaultProps",e$)},70949:function(e,t,r){"use strict";r.d(t,{O:function(){return O}});var n=r(2265),o=r(15870),i=r.n(o),a=r(61994);function l(e,t){for(var r in e)if(({}).hasOwnProperty.call(e,r)&&(!({}).hasOwnProperty.call(t,r)||e[r]!==t[r]))return!1;for(var n in t)if(({}).hasOwnProperty.call(t,n)&&!({}).hasOwnProperty.call(e,n))return!1;return!0}var u=r(9841),c=r(58811),s=r(26680),d=r(16630),f=r(41637),p=r(82944),h=r(12983),v=["viewBox"],y=["viewBox"];function m(){return(m=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function g(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function b(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?g(Object(r),!0).forEach(function(t){x(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):g(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function w(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}function x(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}class O extends n.Component{shouldComponentUpdate(e,t){var{viewBox:r}=e,n=w(e,v),o=this.props,{viewBox:i}=o,a=w(o,y);return!l(r,i)||!l(n,a)||!l(t,this.state)}getTickLineCoord(e){var t,r,n,o,i,a,{x:l,y:u,width:c,height:s,orientation:f,tickSize:p,mirror:h,tickMargin:v}=this.props,y=h?-1:1,m=e.tickSize||p,g=(0,d.hj)(e.tickCoord)?e.tickCoord:e.coordinate;switch(f){case"top":t=r=e.coordinate,a=(n=(o=u+ +!h*s)-y*m)-y*v,i=g;break;case"left":n=o=e.coordinate,i=(t=(r=l+ +!h*c)-y*m)-y*v,a=g;break;case"right":n=o=e.coordinate,i=(t=(r=l+ +h*c)+y*m)+y*v,a=g;break;default:t=r=e.coordinate,a=(n=(o=u+ +h*s)+y*m)+y*v,i=g}return{line:{x1:t,y1:n,x2:r,y2:o},tick:{x:i,y:a}}}getTickTextAnchor(){var e,{orientation:t,mirror:r}=this.props;switch(t){case"left":e=r?"start":"end";break;case"right":e=r?"end":"start";break;default:e="middle"}return e}getTickVerticalAnchor(){var{orientation:e,mirror:t}=this.props;switch(e){case"left":case"right":return"middle";case"top":return t?"start":"end";default:return t?"end":"start"}}renderAxisLine(){var{x:e,y:t,width:r,height:o,orientation:l,mirror:u,axisLine:c}=this.props,s=b(b(b({},(0,p.L6)(this.props,!1)),(0,p.L6)(c,!1)),{},{fill:"none"});if("top"===l||"bottom"===l){var d=+("top"===l&&!u||"bottom"===l&&u);s=b(b({},s),{},{x1:e,y1:t+d*o,x2:e+r,y2:t+d*o})}else{var f=+("left"===l&&!u||"right"===l&&u);s=b(b({},s),{},{x1:e+f*r,y1:t,x2:e+f*r,y2:t+o})}return n.createElement("line",m({},s,{className:(0,a.W)("recharts-cartesian-axis-line",i()(c,"className"))}))}static renderTickItem(e,t,r){var o,i=(0,a.W)(t.className,"recharts-cartesian-axis-tick-value");if(n.isValidElement(e))o=n.cloneElement(e,b(b({},t),{},{className:i}));else if("function"==typeof e)o=e(b(b({},t),{},{className:i}));else{var l="recharts-cartesian-axis-tick-value";"boolean"!=typeof e&&(l=(0,a.W)(l,e.className)),o=n.createElement(c.x,m({},t,{className:l}),r)}return o}renderTicks(e,t){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[],{tickLine:o,stroke:l,tick:c,tickFormatter:s,unit:d}=this.props,v=(0,h.f)(b(b({},this.props),{},{ticks:r}),e,t),y=this.getTickTextAnchor(),g=this.getTickVerticalAnchor(),w=(0,p.L6)(this.props,!1),x=(0,p.L6)(c,!1),j=b(b({},w),{},{fill:"none"},(0,p.L6)(o,!1)),k=v.map((e,t)=>{var{line:r,tick:p}=this.getTickLineCoord(e),h=b(b(b(b({textAnchor:y,verticalAnchor:g},w),{},{stroke:"none",fill:l},x),p),{},{index:t,payload:e,visibleTicksCount:v.length,tickFormatter:s});return n.createElement(u.m,m({className:"recharts-cartesian-axis-tick",key:"tick-".concat(e.value,"-").concat(e.coordinate,"-").concat(e.tickCoord)},(0,f.bw)(this.props,e,t)),o&&n.createElement("line",m({},j,r,{className:(0,a.W)("recharts-cartesian-axis-tick-line",i()(o,"className"))})),c&&O.renderTickItem(c,h,"".concat("function"==typeof s?s(e.value,t):e.value).concat(d||"")))});return k.length>0?n.createElement("g",{className:"recharts-cartesian-axis-ticks"},k):null}render(){var{axisLine:e,width:t,height:r,className:o,hide:i}=this.props;if(i)return null;var{ticks:l}=this.props;return null!=t&&t<=0||null!=r&&r<=0?null:n.createElement(u.m,{className:(0,a.W)("recharts-cartesian-axis",o),ref:e=>{if(e){var t=e.getElementsByClassName("recharts-cartesian-axis-tick-value");this.tickRefs.current=Array.from(t);var r=t[0];if(r){var n=window.getComputedStyle(r).fontSize,o=window.getComputedStyle(r).letterSpacing;(n!==this.state.fontSize||o!==this.state.letterSpacing)&&this.setState({fontSize:window.getComputedStyle(r).fontSize,letterSpacing:window.getComputedStyle(r).letterSpacing})}}}},e&&this.renderAxisLine(),this.renderTicks(this.state.fontSize,this.state.letterSpacing,l),s._.renderCallByParent(this.props))}constructor(e){super(e),this.tickRefs=n.createRef(),this.tickRefs.current=[],this.state={fontSize:"",letterSpacing:""}}}x(O,"displayName","CartesianAxis"),x(O,"defaultProps",{x:0,y:0,width:0,height:0,viewBox:{x:0,y:0,width:0,height:0},orientation:"bottom",ticks:[],stroke:"#666",tickLine:!0,axisLine:!0,tick:!0,mirror:!1,minTickGap:5,tickSize:6,tickMargin:2,interval:"preserveEnd"})},56940:function(e,t,r){"use strict";r.d(t,{q:function(){return D}});var n=r(2265),o=r(1175),i=r(16630),a=r(82944),l=r(49037),u=r(12983),c=r(70949),s=r(35953),d=r(98628),f=r(39040),p=r(58735),h=r(40130),v=["x1","y1","x2","y2","key"],y=["offset"],m=["xAxisId","yAxisId"],g=["xAxisId","yAxisId"];function b(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function w(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?b(Object(r),!0).forEach(function(t){var n,o;n=t,o=r[t],(n=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(n))in e?Object.defineProperty(e,n,{value:o,enumerable:!0,configurable:!0,writable:!0}):e[n]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):b(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function x(){return(x=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function O(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}var j=e=>{var{fill:t}=e;if(!t||"none"===t)return null;var{fillOpacity:r,x:o,y:i,width:a,height:l,ry:u}=e;return n.createElement("rect",{x:o,y:i,ry:u,width:a,height:l,stroke:"none",fill:t,fillOpacity:r,className:"recharts-cartesian-grid-bg"})};function k(e,t){var r;if(n.isValidElement(e))r=n.cloneElement(e,t);else if("function"==typeof e)r=e(t);else{var{x1:o,y1:i,x2:l,y2:u,key:c}=t,s=O(t,v),d=(0,a.L6)(s,!1),{offset:f}=d,p=O(d,y);r=n.createElement("line",x({},p,{x1:o,y1:i,x2:l,y2:u,fill:"none",key:c}))}return r}function P(e){var{x:t,width:r,horizontal:o=!0,horizontalPoints:i}=e;if(!o||!i||!i.length)return null;var{xAxisId:a,yAxisId:l}=e,u=O(e,m),c=i.map((e,n)=>k(o,w(w({},u),{},{x1:t,y1:e,x2:t+r,y2:e,key:"line-".concat(n),index:n})));return n.createElement("g",{className:"recharts-cartesian-grid-horizontal"},c)}function E(e){var{y:t,height:r,vertical:o=!0,verticalPoints:i}=e;if(!o||!i||!i.length)return null;var{xAxisId:a,yAxisId:l}=e,u=O(e,g),c=i.map((e,n)=>k(o,w(w({},u),{},{x1:e,y1:t,x2:e,y2:t+r,key:"line-".concat(n),index:n})));return n.createElement("g",{className:"recharts-cartesian-grid-vertical"},c)}function M(e){var{horizontalFill:t,fillOpacity:r,x:o,y:i,width:a,height:l,horizontalPoints:u,horizontal:c=!0}=e;if(!c||!t||!t.length)return null;var s=u.map(e=>Math.round(e+i-i)).sort((e,t)=>e-t);i!==s[0]&&s.unshift(0);var d=s.map((e,u)=>{var c=s[u+1]?s[u+1]-e:i+l-e;if(c<=0)return null;var d=u%t.length;return n.createElement("rect",{key:"react-".concat(u),y:e,x:o,height:c,width:a,stroke:"none",fill:t[d],fillOpacity:r,className:"recharts-cartesian-grid-bg"})});return n.createElement("g",{className:"recharts-cartesian-gridstripes-horizontal"},d)}function S(e){var{vertical:t=!0,verticalFill:r,fillOpacity:o,x:i,y:a,width:l,height:u,verticalPoints:c}=e;if(!t||!r||!r.length)return null;var s=c.map(e=>Math.round(e+i-i)).sort((e,t)=>e-t);i!==s[0]&&s.unshift(0);var d=s.map((e,t)=>{var c=s[t+1]?s[t+1]-e:i+l-e;if(c<=0)return null;var d=t%r.length;return n.createElement("rect",{key:"react-".concat(t),x:e,y:a,width:c,height:u,stroke:"none",fill:r[d],fillOpacity:o,className:"recharts-cartesian-grid-bg"})});return n.createElement("g",{className:"recharts-cartesian-gridstripes-vertical"},d)}var C=(e,t)=>{var{xAxis:r,width:n,height:o,offset:i}=e;return(0,l.Rf)((0,u.f)(w(w(w({},c.O.defaultProps),r),{},{ticks:(0,l.uY)(r,!0),viewBox:{x:0,y:0,width:n,height:o}})),i.left,i.left+i.width,t)},A=(e,t)=>{var{yAxis:r,width:n,height:o,offset:i}=e;return(0,l.Rf)((0,u.f)(w(w(w({},c.O.defaultProps),r),{},{ticks:(0,l.uY)(r,!0),viewBox:{x:0,y:0,width:n,height:o}})),i.top,i.top+i.height,t)},T={horizontal:!0,vertical:!0,horizontalPoints:[],verticalPoints:[],stroke:"#ccc",fill:"none",verticalFill:[],horizontalFill:[],xAxisId:0,yAxisId:0};function D(e){var t=(0,s.zn)(),r=(0,s.Mw)(),a=(0,s.qD)(),l=w(w({},(0,h.j)(e,T)),{},{x:(0,i.hj)(e.x)?e.x:a.left,y:(0,i.hj)(e.y)?e.y:a.top,width:(0,i.hj)(e.width)?e.width:a.width,height:(0,i.hj)(e.height)?e.height:a.height}),{xAxisId:u,yAxisId:c,x:v,y,width:m,height:g,syncWithTicks:b,horizontalValues:O,verticalValues:k}=l,D=(0,p.W)(),N=(0,f.C)(e=>(0,d.Lg)(e,"xAxis",u,D)),_=(0,f.C)(e=>(0,d.Lg)(e,"yAxis",c,D));if(!(0,i.hj)(m)||m<=0||!(0,i.hj)(g)||g<=0||!(0,i.hj)(v)||v!==+v||!(0,i.hj)(y)||y!==+y)return null;var I=l.verticalCoordinatesGenerator||C,R=l.horizontalCoordinatesGenerator||A,{horizontalPoints:L,verticalPoints:z}=l;if((!L||!L.length)&&"function"==typeof R){var W=O&&O.length,F=R({yAxis:_?w(w({},_),{},{ticks:W?O:_.ticks}):void 0,width:t,height:r,offset:a},!!W||b);(0,o.Z)(Array.isArray(F),"horizontalCoordinatesGenerator should return Array but instead it returned [".concat(typeof F,"]")),Array.isArray(F)&&(L=F)}if((!z||!z.length)&&"function"==typeof I){var B=k&&k.length,K=I({xAxis:N?w(w({},N),{},{ticks:B?k:N.ticks}):void 0,width:t,height:r,offset:a},!!B||b);(0,o.Z)(Array.isArray(K),"verticalCoordinatesGenerator should return Array but instead it returned [".concat(typeof K,"]")),Array.isArray(K)&&(z=K)}return n.createElement("g",{className:"recharts-cartesian-grid"},n.createElement(j,{fill:l.fill,fillOpacity:l.fillOpacity,x:l.x,y:l.y,width:l.width,height:l.height,ry:l.ry}),n.createElement(M,x({},l,{horizontalPoints:L})),n.createElement(S,x({},l,{verticalPoints:z})),n.createElement(P,x({},l,{offset:a,horizontalPoints:L,xAxis:N,yAxis:_})),n.createElement(E,x({},l,{offset:a,verticalPoints:z,xAxis:N,yAxis:_})))}D.displayName="CartesianGrid"},97059:function(e,t,r){"use strict";r.d(t,{K:function(){return b}});var n=r(2265),o=r(61994),i=r(70949),a=r(39040),l=r(17644),u=r(98628),c=r(36289),s=r(58735),d=["children"],f=["dangerouslySetInnerHTML","ticks"];function p(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function h(){return(h=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function v(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}function y(e){var t=(0,a.T)(),r=(0,n.useMemo)(()=>{var{children:t}=e;return v(e,d)},[e]),o=(0,a.C)(e=>(0,u.i9)(e,r.id)),i=r===o;return((0,n.useEffect)(()=>(t((0,l.m2)(r)),()=>{t((0,l.Jj)(r))}),[r,t]),i)?e.children:null}var m=e=>{var{xAxisId:t,className:r}=e,l=(0,a.C)(c.zM),d=(0,s.W)(),p="xAxis",y=(0,a.C)(e=>(0,u.Vm)(e,p,t,d)),m=(0,a.C)(e=>(0,u.ox)(e,p,t,d)),g=(0,a.C)(e=>(0,u.Oy)(e,t)),b=(0,a.C)(e=>(0,u.rs)(e,t));if(null==g||null==b)return null;var{dangerouslySetInnerHTML:w,ticks:x}=e,O=v(e,f);return n.createElement(i.O,h({},O,{scale:y,x:b.x,y:b.y,width:g.width,height:g.height,className:(0,o.W)("recharts-".concat(p," ").concat(p),r),viewBox:l,ticks:m}))},g=e=>{var t,r,o,i,a;return n.createElement(y,{interval:null!==(t=e.interval)&&void 0!==t?t:"preserveEnd",id:e.xAxisId,scale:e.scale,type:e.type,padding:e.padding,allowDataOverflow:e.allowDataOverflow,domain:e.domain,dataKey:e.dataKey,allowDuplicatedCategory:e.allowDuplicatedCategory,allowDecimals:e.allowDecimals,tickCount:e.tickCount,includeHidden:null!==(r=e.includeHidden)&&void 0!==r&&r,reversed:e.reversed,ticks:e.ticks,height:e.height,orientation:e.orientation,mirror:e.mirror,hide:e.hide,unit:e.unit,name:e.name,angle:null!==(o=e.angle)&&void 0!==o?o:0,minTickGap:null!==(i=e.minTickGap)&&void 0!==i?i:5,tick:null===(a=e.tick)||void 0===a||a,tickFormatter:e.tickFormatter},n.createElement(m,e))};class b extends n.Component{render(){return n.createElement(g,this.props)}}p(b,"displayName","XAxis"),p(b,"defaultProps",{allowDataOverflow:u.dW.allowDataOverflow,allowDecimals:u.dW.allowDecimals,allowDuplicatedCategory:u.dW.allowDuplicatedCategory,height:u.dW.height,hide:!1,mirror:u.dW.mirror,orientation:u.dW.orientation,padding:u.dW.padding,reversed:u.dW.reversed,scale:u.dW.scale,tickCount:u.dW.tickCount,type:u.dW.type,xAxisId:0})},2027:function(e,t,r){"use strict";r.d(t,{Q:function(){return w}});var n=r(2265),o=r(61994),i=r(70949),a=r(17644),l=r(39040),u=r(98628),c=r(36289),s=r(58735),d=e=>{var{ticks:t,label:r,labelGapWithTick:n=5,tickSize:o=0,tickMargin:i=0}=e,a=0;if(t){t.forEach(e=>{if(e){var t=e.getBoundingClientRect();t.width>a&&(a=t.width)}});var l=r?r.getBoundingClientRect().width:0;return Math.round(a+(o+i)+l+(r?n:0))}return 0},f=r(26680),p=["dangerouslySetInnerHTML","ticks"];function h(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function v(){return(v=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function y(e){var t=(0,l.T)();return(0,n.useEffect)(()=>(t((0,a.TC)(e)),()=>{t((0,a.cB)(e))}),[e,t]),null}var m=e=>{var t,{yAxisId:r,className:h,width:y,label:m}=e,g=(0,n.useRef)(null),b=(0,n.useRef)(null),w=(0,l.C)(c.zM),x=(0,s.W)(),O=(0,l.T)(),j="yAxis",k=(0,l.C)(e=>(0,u.Vm)(e,j,r,x)),P=(0,l.C)(e=>(0,u.ON)(e,r)),E=(0,l.C)(e=>(0,u.lU)(e,r)),M=(0,l.C)(e=>(0,u.ox)(e,j,r,x));if((0,n.useLayoutEffect)(()=>{if(!("auto"!==y||!P||(0,f.d)(m)||(0,n.isValidElement)(m))){var e,t=g.current,o=null==t||null===(e=t.tickRefs)||void 0===e?void 0:e.current,{tickSize:i,tickMargin:l}=t.props,u=d({ticks:o,label:b.current,labelGapWithTick:5,tickSize:i,tickMargin:l});Math.round(P.width)!==Math.round(u)&&O((0,a.kB)({id:r,width:u}))}},[g,null==g||null===(t=g.current)||void 0===t||null===(t=t.tickRefs)||void 0===t?void 0:t.current,null==P?void 0:P.width,P,O,m,r,y]),null==P||null==E)return null;var{dangerouslySetInnerHTML:S,ticks:C}=e,A=function(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}(e,p);return n.createElement(i.O,v({},A,{ref:g,labelRef:b,scale:k,x:E.x,y:E.y,width:P.width,height:P.height,className:(0,o.W)("recharts-".concat(j," ").concat(j),h),viewBox:w,ticks:M}))},g=e=>{var t,r,o,i,a;return n.createElement(n.Fragment,null,n.createElement(y,{interval:null!==(t=e.interval)&&void 0!==t?t:"preserveEnd",id:e.yAxisId,scale:e.scale,type:e.type,domain:e.domain,allowDataOverflow:e.allowDataOverflow,dataKey:e.dataKey,allowDuplicatedCategory:e.allowDuplicatedCategory,allowDecimals:e.allowDecimals,tickCount:e.tickCount,padding:e.padding,includeHidden:null!==(r=e.includeHidden)&&void 0!==r&&r,reversed:e.reversed,ticks:e.ticks,width:e.width,orientation:e.orientation,mirror:e.mirror,hide:e.hide,unit:e.unit,name:e.name,angle:null!==(o=e.angle)&&void 0!==o?o:0,minTickGap:null!==(i=e.minTickGap)&&void 0!==i?i:5,tick:null===(a=e.tick)||void 0===a||a,tickFormatter:e.tickFormatter}),n.createElement(m,e))},b={allowDataOverflow:u.RN.allowDataOverflow,allowDecimals:u.RN.allowDecimals,allowDuplicatedCategory:u.RN.allowDuplicatedCategory,hide:!1,mirror:u.RN.mirror,orientation:u.RN.orientation,padding:u.RN.padding,reversed:u.RN.reversed,scale:u.RN.scale,tickCount:u.RN.tickCount,type:u.RN.type,width:u.RN.width,yAxisId:0};class w extends n.Component{render(){return n.createElement(g,this.props)}}h(w,"displayName","YAxis"),h(w,"defaultProps",b)},12983:function(e,t,r){"use strict";r.d(t,{f:function(){return h}});var n,o,i=r(16630),a=r(4094),l=r(34067);class u{static create(e){return new u(e)}get domain(){return this.scale.domain}get range(){return this.scale.range}get rangeMin(){return this.range()[0]}get rangeMax(){return this.range()[1]}get bandwidth(){return this.scale.bandwidth}apply(e){var{bandAware:t,position:r}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(void 0!==e){if(r)switch(r){case"start":default:return this.scale(e);case"middle":var n=this.bandwidth?this.bandwidth()/2:0;return this.scale(e)+n;case"end":var o=this.bandwidth?this.bandwidth():0;return this.scale(e)+o}if(t){var i=this.bandwidth?this.bandwidth()/2:0;return this.scale(e)+i}return this.scale(e)}}isInRange(e){var t=this.range(),r=t[0],n=t[t.length-1];return r<=n?e>=r&&e<=n:e>=n&&e<=r}constructor(e){this.scale=e}}(o="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(o="EPS","string"))?n:n+"")in u?Object.defineProperty(u,o,{value:1e-4,enumerable:!0,configurable:!0,writable:!0}):u[o]=1e-4;var c=function(e){var{width:t,height:r}=e,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,o=(n%180+180)%180*Math.PI/180,i=Math.atan(r/t);return Math.abs(o>i&&o<Math.PI-i?r/Math.sin(o):t/Math.cos(o))};function s(e,t,r){if(t<1)return[];if(1===t&&void 0===r)return e;for(var n=[],o=0;o<e.length;o+=t){if(void 0!==r&&!0!==r(e[o]))return;n.push(e[o])}return n}function d(e,t,r,n,o){if(e*t<e*n||e*t>e*o)return!1;var i=r();return e*(t-e*i/2-n)>=0&&e*(t+e*i/2-o)<=0}function f(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function p(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?f(Object(r),!0).forEach(function(t){var n,o;n=t,o=r[t],(n=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(n))in e?Object.defineProperty(e,n,{value:o,enumerable:!0,configurable:!0,writable:!0}):e[n]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):f(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function h(e,t,r){var n,{tick:o,ticks:u,viewBox:f,minTickGap:h,orientation:v,interval:y,tickFormatter:m,unit:g,angle:b}=e;if(!u||!u.length||!o)return[];if((0,i.hj)(y)||l.x.isSsr)return null!==(n=s(u,((0,i.hj)(y)?y:0)+1))&&void 0!==n?n:[];var w="top"===v||"bottom"===v?"width":"height",x=g&&"width"===w?(0,a.x)(g,{fontSize:t,letterSpacing:r}):{width:0,height:0},O=(e,n)=>{var o,i="function"==typeof m?m(e.value,n):e.value;return"width"===w?c({width:(o=(0,a.x)(i,{fontSize:t,letterSpacing:r})).width+x.width,height:o.height+x.height},b):(0,a.x)(i,{fontSize:t,letterSpacing:r})[w]},j=u.length>=2?(0,i.uY)(u[1].coordinate-u[0].coordinate):1,k=function(e,t,r){var n="width"===r,{x:o,y:i,width:a,height:l}=e;return 1===t?{start:n?o:i,end:n?o+a:i+l}:{start:n?o+a:i+l,end:n?o:i}}(f,j,w);return"equidistantPreserveStart"===y?function(e,t,r,n,o){for(var i,a=(n||[]).slice(),{start:l,end:u}=t,c=0,f=1,p=l;f<=a.length;)if(i=function(){var t,i=null==n?void 0:n[c];if(void 0===i)return{v:s(n,f)};var a=c,h=()=>(void 0===t&&(t=r(i,a)),t),v=i.coordinate,y=0===c||d(e,v,h,p,u);y||(c=0,p=l,f+=1),y&&(p=v+e*(h()/2+o),c+=f)}())return i.v;return[]}(j,k,O,u,h):("preserveStart"===y||"preserveStartEnd"===y?function(e,t,r,n,o,i){var a=(n||[]).slice(),l=a.length,{start:u,end:c}=t;if(i){var s=n[l-1],f=r(s,l-1),h=e*(s.coordinate+e*f/2-c);a[l-1]=s=p(p({},s),{},{tickCoord:h>0?s.coordinate-h*e:s.coordinate}),d(e,s.tickCoord,()=>f,u,c)&&(c=s.tickCoord-e*(f/2+o),a[l-1]=p(p({},s),{},{isShow:!0}))}for(var v=i?l-1:l,y=function(t){var n,i=a[t],l=()=>(void 0===n&&(n=r(i,t)),n);if(0===t){var s=e*(i.coordinate-e*l()/2-u);a[t]=i=p(p({},i),{},{tickCoord:s<0?i.coordinate-s*e:i.coordinate})}else a[t]=i=p(p({},i),{},{tickCoord:i.coordinate});d(e,i.tickCoord,l,u,c)&&(u=i.tickCoord+e*(l()/2+o),a[t]=p(p({},i),{},{isShow:!0}))},m=0;m<v;m++)y(m);return a}(j,k,O,u,h,"preserveStartEnd"===y):function(e,t,r,n,o){for(var i=(n||[]).slice(),a=i.length,{start:l}=t,{end:u}=t,c=function(t){var n,c=i[t],s=()=>(void 0===n&&(n=r(c,t)),n);if(t===a-1){var f=e*(c.coordinate+e*s()/2-u);i[t]=c=p(p({},c),{},{tickCoord:f>0?c.coordinate-f*e:c.coordinate})}else i[t]=c=p(p({},c),{},{tickCoord:c.coordinate});d(e,c.tickCoord,s,l,u)&&(u=c.tickCoord-e*(s()/2+o),i[t]=p(p({},c),{},{isShow:!0}))},s=a-1;s>=0;s--)c(s);return i}(j,k,O,u,h)).filter(e=>e.isShow)}},42736:function(e,t,r){"use strict";r.d(t,{v:function(){return m}});var n=r(2265),o=r(31057),i=r(82931),a=r(1196),l=r(87235),u=r(15317),c=r(39151),s=r(40130),d=r(66395),f=["width","height"];function p(){return(p=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}var h={accessibilityLayer:!0,layout:"horizontal",stackOffset:"none",barCategoryGap:"10%",barGap:4,margin:{top:5,right:5,bottom:5,left:5},reverseStackOrder:!1,syncMethod:"index"},v=(0,n.forwardRef)(function(e,t){var r,o=(0,s.j)(e.categoricalChartProps,h),{width:v,height:y}=o,m=function(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}(o,f);if(!(0,d.r)(v)||!(0,d.r)(y))return null;var{chartName:g,defaultTooltipEventType:b,validateTooltipEventTypes:w,tooltipPayloadSearcher:x,categoricalChartProps:O}=e;return n.createElement(i.M,{preloadedState:{options:{chartName:g,defaultTooltipEventType:b,validateTooltipEventTypes:w,tooltipPayloadSearcher:x,eventEmitter:void 0}},reduxStoreName:null!==(r=O.id)&&void 0!==r?r:g},n.createElement(a.gt,{chartData:O.data}),n.createElement(l.v,{width:v,height:y,layout:o.layout,margin:o.margin}),n.createElement(u.b,{accessibilityLayer:o.accessibilityLayer,barCategoryGap:o.barCategoryGap,maxBarSize:o.maxBarSize,stackOffset:o.stackOffset,barGap:o.barGap,barSize:o.barSize,syncId:o.syncId,syncMethod:o.syncMethod,className:o.className}),n.createElement(c.r,p({},m,{width:v,height:y,ref:t})))}),y=["axis","item"],m=(0,n.forwardRef)((e,t)=>n.createElement(v,{chartName:"BarChart",defaultTooltipEventType:"axis",validateTooltipEventTypes:y,tooltipPayloadSearcher:o.NL,categoricalChartProps:e,ref:t}))},39151:function(e,t,r){"use strict";r.d(t,{r:function(){return L}});var n=r(2265),o=r(82944),i=r(35953),a=r(14847),l=r(58735),u=r(61994),c=["children","width","height","viewBox","className","style","title","desc"];function s(){return(s=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}var d=(0,n.forwardRef)((e,t)=>{var{children:r,width:i,height:a,viewBox:l,className:d,style:f,title:p,desc:h}=e,v=function(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}(e,c),y=l||{width:i,height:a,x:0,y:0},m=(0,u.W)("recharts-surface",d);return n.createElement("svg",s({},(0,o.L6)(v,!0,"svg"),{className:m,width:i,height:a,style:f,viewBox:"".concat(y.x," ").concat(y.y," ").concat(y.width," ").concat(y.height),ref:t}),n.createElement("title",null,p),n.createElement("desc",null,h),r)}),f=r(39040),p=r(2431),h=r(66395),v=["children"];function y(){return(y=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}var m={width:"100%",height:"100%"},g=(0,n.forwardRef)((e,t)=>{var r,o,l=(0,i.zn)(),u=(0,i.Mw)(),c=(0,a.F)();if(!(0,h.r)(l)||!(0,h.r)(u))return null;var{children:s,otherAttributes:f,title:p,desc:v}=e;return r="number"==typeof f.tabIndex?f.tabIndex:c?0:void 0,o="string"==typeof f.role?f.role:c?"application":void 0,n.createElement(d,y({},f,{title:p,desc:v,role:o,tabIndex:r,width:l,height:u,style:m,ref:t}),s)}),b=e=>{var{children:t}=e,r=(0,f.C)(p.V);if(!r)return null;var{width:o,height:i,y:a,x:l}=r;return n.createElement(d,{width:o,height:i,x:l,y:a},t)},w=(0,n.forwardRef)((e,t)=>{var{children:r}=e,o=function(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}(e,v);return(0,l.W)()?n.createElement(b,null,r):n.createElement(g,y({ref:t},o),r)}),x=r(64725),O=r(48323),j=r(318),k=r(7883),P=r(60152),E=r(65293),M=r(69366),S=r(83061),C=r(72321),A=(0,n.createContext)(null);function T(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}var D=(0,n.forwardRef)((e,t)=>{var{children:r,className:o,height:i,onClick:a,onContextMenu:l,onDoubleClick:c,onMouseDown:s,onMouseEnter:d,onMouseLeave:p,onMouseMove:v,onMouseUp:y,onTouchEnd:m,onTouchMove:g,onTouchStart:b,style:w,width:D}=e,N=(0,f.T)(),[_,I]=(0,n.useState)(null),[R,L]=(0,n.useState)(null);(0,j.W9)();var z=function(){var e=(0,f.T)(),[t,r]=(0,n.useState)(null),o=(0,f.C)(P.K$);return(0,n.useEffect)(()=>{if(null!=t){var r=t.getBoundingClientRect().width/t.offsetWidth;(0,h.n)(r)&&r!==o&&e((0,E.ZP)(r))}},[t,e,o]),r}(),W=(0,n.useCallback)(e=>{z(e),"function"==typeof t&&t(e),I(e),L(e)},[z,t,I,L]),F=(0,n.useCallback)(e=>{N((0,O.AE)(e)),N((0,M.r)({handler:a,reactEvent:e}))},[N,a]),B=(0,n.useCallback)(e=>{N((0,O.TK)(e)),N((0,M.r)({handler:d,reactEvent:e}))},[N,d]),K=(0,n.useCallback)(e=>{N((0,x.ne)()),N((0,M.r)({handler:p,reactEvent:e}))},[N,p]),V=(0,n.useCallback)(e=>{N((0,O.TK)(e)),N((0,M.r)({handler:v,reactEvent:e}))},[N,v]),Z=(0,n.useCallback)(()=>{N((0,k.eb)())},[N]),U=(0,n.useCallback)(e=>{N((0,k.sj)(e.key))},[N]),$=(0,n.useCallback)(e=>{N((0,M.r)({handler:l,reactEvent:e}))},[N,l]),H=(0,n.useCallback)(e=>{N((0,M.r)({handler:c,reactEvent:e}))},[N,c]),Y=(0,n.useCallback)(e=>{N((0,M.r)({handler:s,reactEvent:e}))},[N,s]),q=(0,n.useCallback)(e=>{N((0,M.r)({handler:y,reactEvent:e}))},[N,y]),X=(0,n.useCallback)(e=>{N((0,M.r)({handler:b,reactEvent:e}))},[N,b]),G=(0,n.useCallback)(e=>{N((0,S.$)(e)),N((0,M.r)({handler:g,reactEvent:e}))},[N,g]),Q=(0,n.useCallback)(e=>{N((0,M.r)({handler:m,reactEvent:e}))},[N,m]);return n.createElement(C.E.Provider,{value:_},n.createElement(A.Provider,{value:R},n.createElement("div",{className:(0,u.W)("recharts-wrapper",o),style:function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?T(Object(r),!0).forEach(function(t){var n,o;n=t,o=r[t],(n=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(n))in e?Object.defineProperty(e,n,{value:o,enumerable:!0,configurable:!0,writable:!0}):e[n]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):T(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}({position:"relative",cursor:"default",width:D,height:i},w),role:"application",onClick:F,onContextMenu:$,onDoubleClick:H,onFocus:Z,onKeyDown:U,onMouseDown:Y,onMouseEnter:B,onMouseLeave:K,onMouseMove:V,onMouseUp:q,onTouchEnd:Q,onTouchMove:G,onTouchStart:X,ref:W},r)))}),N=r(16630),_=(0,n.createContext)(void 0),I=e=>{var{children:t}=e,[r]=(0,n.useState)("".concat((0,N.EL)("recharts"),"-clip")),o=(0,i.qD)();if(null==o)return null;var{left:a,top:l,height:u,width:c}=o;return n.createElement(_.Provider,{value:r},n.createElement("defs",null,n.createElement("clipPath",{id:r},n.createElement("rect",{x:a,y:l,height:u,width:c}))),t)},R=["children","className","width","height","style","compact","title","desc"],L=(0,n.forwardRef)((e,t)=>{var{children:r,className:i,width:a,height:l,style:u,compact:c,title:s,desc:d}=e,f=function(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}(e,R),p=(0,o.L6)(f,!1);return c?n.createElement(w,{otherAttributes:p,title:s,desc:d},r):n.createElement(D,{className:i,style:u,width:a,height:l,onClick:e.onClick,onMouseLeave:e.onMouseLeave,onMouseEnter:e.onMouseEnter,onMouseMove:e.onMouseMove,onMouseDown:e.onMouseDown,onMouseUp:e.onMouseUp,onContextMenu:e.onContextMenu,onDoubleClick:e.onDoubleClick,onTouchStart:e.onTouchStart,onTouchMove:e.onTouchMove,onTouchEnd:e.onTouchEnd},n.createElement(w,{otherAttributes:p,title:s,desc:d,ref:t},n.createElement(I,null,r)))})},86810:function(e,t,r){"use strict";r.d(t,{u:function(){return x}});var n=r(2265),o=r(31057),i=r(82931),a=r(1196),l=r(87235),u=r(15317),c=r(39040),s=r(27410);function d(e){var t=(0,c.T)();return(0,n.useEffect)(()=>{t((0,s.a)(e))},[t,e]),null}var f=r(39151),p=r(40130),h=r(66395),v=["width","height","layout"];function y(){return(y=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}var m={accessibilityLayer:!0,stackOffset:"none",barCategoryGap:"10%",barGap:4,margin:{top:5,right:5,bottom:5,left:5},reverseStackOrder:!1,syncMethod:"index",layout:"radial"},g=(0,n.forwardRef)(function(e,t){var r,o=(0,p.j)(e.categoricalChartProps,m),{width:c,height:s,layout:g}=o,b=function(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}(o,v);if(!(0,h.r)(c)||!(0,h.r)(s))return null;var{chartName:w,defaultTooltipEventType:x,validateTooltipEventTypes:O,tooltipPayloadSearcher:j}=e;return n.createElement(i.M,{preloadedState:{options:{chartName:w,defaultTooltipEventType:x,validateTooltipEventTypes:O,tooltipPayloadSearcher:j,eventEmitter:void 0}},reduxStoreName:null!==(r=o.id)&&void 0!==r?r:w},n.createElement(a.gt,{chartData:o.data}),n.createElement(l.v,{width:c,height:s,layout:g,margin:o.margin}),n.createElement(u.b,{accessibilityLayer:o.accessibilityLayer,barCategoryGap:o.barCategoryGap,maxBarSize:o.maxBarSize,stackOffset:o.stackOffset,barGap:o.barGap,barSize:o.barSize,syncId:o.syncId,syncMethod:o.syncMethod,className:o.className}),n.createElement(d,{cx:o.cx,cy:o.cy,startAngle:o.startAngle,endAngle:o.endAngle,innerRadius:o.innerRadius,outerRadius:o.outerRadius}),n.createElement(f.r,y({width:c,height:s},b,{ref:t})))}),b=["item"],w={layout:"centric",startAngle:0,endAngle:360,cx:"50%",cy:"50%",innerRadius:0,outerRadius:"80%"},x=(0,n.forwardRef)((e,t)=>{var r=(0,p.j)(e,w);return n.createElement(g,{chartName:"PieChart",defaultTooltipEventType:"item",validateTooltipEventTypes:b,tooltipPayloadSearcher:o.NL,categoricalChartProps:r,ref:t})})},20407:function(e,t,r){"use strict";r.d(t,{b:function(){return n}});var n=e=>null;n.displayName="Cell"},26680:function(e,t,r){"use strict";r.d(t,{_:function(){return j},d:function(){return m}});var n=r(2265),o=r(61994),i=r(58811),a=r(82944),l=r(16630),u=r(39206),c=r(35953),s=["offset"],d=["labelRef"];function f(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}function p(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function h(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?p(Object(r),!0).forEach(function(t){var n,o;n=t,o=r[t],(n=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(n))in e?Object.defineProperty(e,n,{value:o,enumerable:!0,configurable:!0,writable:!0}):e[n]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):p(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function v(){return(v=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}var y=e=>{var{value:t,formatter:r}=e,n=(0,l.Rw)(e.children)?t:e.children;return"function"==typeof r?r(n):n},m=e=>null!=e&&"function"==typeof e,g=(e,t)=>(0,l.uY)(t-e)*Math.min(Math.abs(t-e),360),b=(e,t,r)=>{var i,a,{position:c,viewBox:s,offset:d,className:f}=e,{cx:p,cy:h,innerRadius:y,outerRadius:m,startAngle:b,endAngle:w,clockWise:x}=s,O=(y+m)/2,j=g(b,w),k=j>=0?1:-1;"insideStart"===c?(i=b+k*d,a=x):"insideEnd"===c?(i=w-k*d,a=!x):"end"===c&&(i=w+k*d,a=x),a=j<=0?a:!a;var P=(0,u.op)(p,h,O,i),E=(0,u.op)(p,h,O,i+(a?1:-1)*359),M="M".concat(P.x,",").concat(P.y,"\n    A").concat(O,",").concat(O,",0,1,").concat(a?0:1,",\n    ").concat(E.x,",").concat(E.y),S=(0,l.Rw)(e.id)?(0,l.EL)("recharts-radial-line-"):e.id;return n.createElement("text",v({},r,{dominantBaseline:"central",className:(0,o.W)("recharts-radial-bar-label",f)}),n.createElement("defs",null,n.createElement("path",{id:S,d:M})),n.createElement("textPath",{xlinkHref:"#".concat(S)},t))},w=e=>{var{viewBox:t,offset:r,position:n}=e,{cx:o,cy:i,innerRadius:a,outerRadius:l,startAngle:c,endAngle:s}=t,d=(c+s)/2;if("outside"===n){var{x:f,y:p}=(0,u.op)(o,i,l+r,d);return{x:f,y:p,textAnchor:f>=o?"start":"end",verticalAnchor:"middle"}}if("center"===n)return{x:o,y:i,textAnchor:"middle",verticalAnchor:"middle"};if("centerTop"===n)return{x:o,y:i,textAnchor:"middle",verticalAnchor:"start"};if("centerBottom"===n)return{x:o,y:i,textAnchor:"middle",verticalAnchor:"end"};var{x:h,y:v}=(0,u.op)(o,i,(a+l)/2,d);return{x:h,y:v,textAnchor:"middle",verticalAnchor:"middle"}},x=(e,t)=>{var{parentViewBox:r,offset:n,position:o}=e,{x:i,y:a,width:u,height:c}=t,s=c>=0?1:-1,d=s*n,f=s>0?"end":"start",p=s>0?"start":"end",v=u>=0?1:-1,y=v*n,m=v>0?"end":"start",g=v>0?"start":"end";if("top"===o)return h(h({},{x:i+u/2,y:a-s*n,textAnchor:"middle",verticalAnchor:f}),r?{height:Math.max(a-r.y,0),width:u}:{});if("bottom"===o)return h(h({},{x:i+u/2,y:a+c+d,textAnchor:"middle",verticalAnchor:p}),r?{height:Math.max(r.y+r.height-(a+c),0),width:u}:{});if("left"===o){var b={x:i-y,y:a+c/2,textAnchor:m,verticalAnchor:"middle"};return h(h({},b),r?{width:Math.max(b.x-r.x,0),height:c}:{})}if("right"===o){var w={x:i+u+y,y:a+c/2,textAnchor:g,verticalAnchor:"middle"};return h(h({},w),r?{width:Math.max(r.x+r.width-w.x,0),height:c}:{})}var x=r?{width:u,height:c}:{};return"insideLeft"===o?h({x:i+y,y:a+c/2,textAnchor:g,verticalAnchor:"middle"},x):"insideRight"===o?h({x:i+u-y,y:a+c/2,textAnchor:m,verticalAnchor:"middle"},x):"insideTop"===o?h({x:i+u/2,y:a+d,textAnchor:"middle",verticalAnchor:p},x):"insideBottom"===o?h({x:i+u/2,y:a+c-d,textAnchor:"middle",verticalAnchor:f},x):"insideTopLeft"===o?h({x:i+y,y:a+d,textAnchor:g,verticalAnchor:p},x):"insideTopRight"===o?h({x:i+u-y,y:a+d,textAnchor:m,verticalAnchor:p},x):"insideBottomLeft"===o?h({x:i+y,y:a+c-d,textAnchor:g,verticalAnchor:f},x):"insideBottomRight"===o?h({x:i+u-y,y:a+c-d,textAnchor:m,verticalAnchor:f},x):o&&"object"==typeof o&&((0,l.hj)(o.x)||(0,l.hU)(o.x))&&((0,l.hj)(o.y)||(0,l.hU)(o.y))?h({x:i+(0,l.h1)(o.x,u),y:a+(0,l.h1)(o.y,c),textAnchor:"end",verticalAnchor:"end"},x):h({x:i+u/2,y:a+c/2,textAnchor:"middle",verticalAnchor:"middle"},x)},O=e=>"cx"in e&&(0,l.hj)(e.cx);function j(e){var t,{offset:r=5}=e,u=h({offset:r},f(e,s)),{viewBox:p,position:m,value:g,children:j,content:k,className:P="",textBreakAll:E,labelRef:M}=u,S=(0,c.d2)(),C=p||S;if(!C||(0,l.Rw)(g)&&(0,l.Rw)(j)&&!(0,n.isValidElement)(k)&&"function"!=typeof k)return null;if((0,n.isValidElement)(k)){var{labelRef:A}=u,T=f(u,d);return(0,n.cloneElement)(k,T)}if("function"==typeof k){if(t=(0,n.createElement)(k,u),(0,n.isValidElement)(t))return t}else t=y(u);var D=O(C),N=(0,a.L6)(u,!0);if(D&&("insideStart"===m||"insideEnd"===m||"end"===m))return b(u,t,N);var _=D?w(u):x(u,C);return n.createElement(i.x,v({ref:M,className:(0,o.W)("recharts-label",P)},N,_,{breakAll:E}),t)}j.displayName="Label";var k=e=>{var{cx:t,cy:r,angle:n,startAngle:o,endAngle:i,r:a,radius:u,innerRadius:c,outerRadius:s,x:d,y:f,top:p,left:h,width:v,height:y,clockWise:m,labelViewBox:g}=e;if(g)return g;if((0,l.hj)(v)&&(0,l.hj)(y)){if((0,l.hj)(d)&&(0,l.hj)(f))return{x:d,y:f,width:v,height:y};if((0,l.hj)(p)&&(0,l.hj)(h))return{x:p,y:h,width:v,height:y}}return(0,l.hj)(d)&&(0,l.hj)(f)?{x:d,y:f,width:0,height:0}:(0,l.hj)(t)&&(0,l.hj)(r)?{cx:t,cy:r,startAngle:o||n||0,endAngle:i||n||0,innerRadius:c||0,outerRadius:s||u||a||0,clockWise:m}:e.viewBox?e.viewBox:void 0},P=(e,t,r)=>{if(!e)return null;var o={viewBox:t,labelRef:r};return!0===e?n.createElement(j,v({key:"label-implicit"},o)):(0,l.P2)(e)?n.createElement(j,v({key:"label-implicit",value:e},o)):(0,n.isValidElement)(e)?e.type===j?(0,n.cloneElement)(e,h({key:"label-implicit"},o)):n.createElement(j,v({key:"label-implicit",content:e},o)):m(e)?n.createElement(j,v({key:"label-implicit",content:e},o)):e&&"object"==typeof e?n.createElement(j,v({},e,{key:"label-implicit"},o)):null};j.parseViewBox=k,j.renderCallByParent=function(e,t){var r=!(arguments.length>2)||void 0===arguments[2]||arguments[2];if(!e||!e.children&&r&&!e.label)return null;var{children:o,labelRef:i}=e,l=k(e),u=(0,a.NN)(o,j).map((e,r)=>(0,n.cloneElement)(e,{viewBox:t||l,key:"label-".concat(r)}));return r?[P(e.label,t||l,i),...u]:u}},47625:function(e,t,r){"use strict";r.d(t,{h:function(){return d}});var n=r(61994),o=r(2265),i=r(34926),a=r.n(i),l=r(16630),u=r(1175);function c(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function s(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?c(Object(r),!0).forEach(function(t){var n,o;n=t,o=r[t],(n=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(n))in e?Object.defineProperty(e,n,{value:o,enumerable:!0,configurable:!0,writable:!0}):e[n]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):c(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var d=(0,o.forwardRef)((e,t)=>{var{aspect:r,initialDimension:i={width:-1,height:-1},width:c="100%",height:d="100%",minWidth:f=0,minHeight:p,maxHeight:h,children:v,debounce:y=0,id:m,className:g,onResize:b,style:w={}}=e,x=(0,o.useRef)(null),O=(0,o.useRef)();O.current=b,(0,o.useImperativeHandle)(t,()=>x.current);var[j,k]=(0,o.useState)({containerWidth:i.width,containerHeight:i.height}),P=(0,o.useCallback)((e,t)=>{k(r=>{var n=Math.round(e),o=Math.round(t);return r.containerWidth===n&&r.containerHeight===o?r:{containerWidth:n,containerHeight:o}})},[]);(0,o.useEffect)(()=>{var e=e=>{var t,{width:r,height:n}=e[0].contentRect;P(r,n),null===(t=O.current)||void 0===t||t.call(O,r,n)};y>0&&(e=a()(e,y,{trailing:!0,leading:!1}));var t=new ResizeObserver(e),{width:r,height:n}=x.current.getBoundingClientRect();return P(r,n),t.observe(x.current),()=>{t.disconnect()}},[P,y]);var E=(0,o.useMemo)(()=>{var{containerWidth:e,containerHeight:t}=j;if(e<0||t<0)return null;(0,u.Z)((0,l.hU)(c)||(0,l.hU)(d),"The width(%s) and height(%s) are both fixed numbers,\n       maybe you don't need to use a ResponsiveContainer.",c,d),(0,u.Z)(!r||r>0,"The aspect(%s) must be greater than zero.",r);var n=(0,l.hU)(c)?e:c,i=(0,l.hU)(d)?t:d;return r&&r>0&&(n?i=n/r:i&&(n=i*r),h&&i>h&&(i=h)),(0,u.Z)(n>0||i>0,"The width(%s) and height(%s) of chart should be greater than 0,\n       please check the style of container, or the props width(%s) and height(%s),\n       or add a minWidth(%s) or minHeight(%s) or use aspect(%s) to control the\n       height and width.",n,i,c,d,f,p,r),o.Children.map(v,e=>(0,o.cloneElement)(e,{width:n,height:i,style:s({height:"100%",width:"100%",maxHeight:i,maxWidth:n},e.props.style)}))},[r,v,d,h,p,f,j,c]);return o.createElement("div",{id:m?"".concat(m):void 0,className:(0,n.W)("recharts-responsive-container",g),style:s(s({},w),{},{width:c,height:d,minWidth:f,minHeight:p,maxHeight:h}),ref:x},E)})},58811:function(e,t,r){"use strict";r.d(t,{x:function(){return C}});var n=r(2265),o=r(61994),i=r(16630),a=r(34067),l=r(82944),u=r(4094),c=/(-?\d+(?:\.\d+)?[a-zA-Z%]*)([*/])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,s=/(-?\d+(?:\.\d+)?[a-zA-Z%]*)([+-])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,d=/^px|cm|vh|vw|em|rem|%|mm|in|pt|pc|ex|ch|vmin|vmax|Q$/,f=/(-?\d+(?:\.\d+)?)([a-zA-Z%]+)?/,p={cm:96/2.54,mm:96/25.4,pt:96/72,pc:16,in:96,Q:96/101.6,px:1},h=Object.keys(p);class v{static parse(e){var t,[,r,n]=null!==(t=f.exec(e))&&void 0!==t?t:[];return new v(parseFloat(r),null!=n?n:"")}add(e){return this.unit!==e.unit?new v(NaN,""):new v(this.num+e.num,this.unit)}subtract(e){return this.unit!==e.unit?new v(NaN,""):new v(this.num-e.num,this.unit)}multiply(e){return""!==this.unit&&""!==e.unit&&this.unit!==e.unit?new v(NaN,""):new v(this.num*e.num,this.unit||e.unit)}divide(e){return""!==this.unit&&""!==e.unit&&this.unit!==e.unit?new v(NaN,""):new v(this.num/e.num,this.unit||e.unit)}toString(){return"".concat(this.num).concat(this.unit)}isNaN(){return(0,i.In)(this.num)}constructor(e,t){this.num=e,this.unit=t,this.num=e,this.unit=t,(0,i.In)(e)&&(this.unit=""),""===t||d.test(t)||(this.num=NaN,this.unit=""),h.includes(t)&&(this.num=e*p[t],this.unit="px")}}function y(e){if(e.includes("NaN"))return"NaN";for(var t=e;t.includes("*")||t.includes("/");){var r,[,n,o,i]=null!==(r=c.exec(t))&&void 0!==r?r:[],a=v.parse(null!=n?n:""),l=v.parse(null!=i?i:""),u="*"===o?a.multiply(l):a.divide(l);if(u.isNaN())return"NaN";t=t.replace(c,u.toString())}for(;t.includes("+")||/.-\d+(?:\.\d+)?/.test(t);){var d,[,f,p,h]=null!==(d=s.exec(t))&&void 0!==d?d:[],y=v.parse(null!=f?f:""),m=v.parse(null!=h?h:""),g="+"===p?y.add(m):y.subtract(m);if(g.isNaN())return"NaN";t=t.replace(s,g.toString())}return t}var m=/\(([^()]*)\)/;function g(e){var t=function(e){try{var t;return t=e.replace(/\s+/g,""),t=function(e){for(var t,r=e;null!=(t=m.exec(r));){var[,n]=t;r=r.replace(m,y(n))}return r}(t),t=y(t)}catch(e){return"NaN"}}(e.slice(5,-1));return"NaN"===t?"":t}var b=["x","y","lineHeight","capHeight","scaleToFit","textAnchor","verticalAnchor","fill"],w=["dx","dy","angle","className","breakAll"];function x(){return(x=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function O(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}var j=/[ \f\n\r\t\v\u2028\u2029]+/,k=e=>{var{children:t,breakAll:r,style:n}=e;try{var o=[];(0,i.Rw)(t)||(o=r?t.toString().split(""):t.toString().split(j));var a=o.map(e=>({word:e,width:(0,u.x)(e,n).width})),l=r?0:(0,u.x)("\xa0",n).width;return{wordsWithComputedWidth:a,spaceWidth:l}}catch(e){return null}},P=(e,t,r,n,o)=>{var a,{maxLines:l,children:u,style:c,breakAll:s}=e,d=(0,i.hj)(l),f=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return e.reduce((e,t)=>{var{word:i,width:a}=t,l=e[e.length-1];return l&&(null==n||o||l.width+a+r<Number(n))?(l.words.push(i),l.width+=a+r):e.push({words:[i],width:a}),e},[])},p=f(t),h=e=>e.reduce((e,t)=>e.width>t.width?e:t);if(!d||o||!(p.length>l||h(p).width>Number(n)))return p;for(var v=e=>{var t=f(k({breakAll:s,style:c,children:u.slice(0,e)+"…"}).wordsWithComputedWidth);return[t.length>l||h(t).width>Number(n),t]},y=0,m=u.length-1,g=0;y<=m&&g<=u.length-1;){var b=Math.floor((y+m)/2),[w,x]=v(b-1),[O]=v(b);if(w||O||(y=b+1),w&&O&&(m=b-1),!w&&O){a=x;break}g++}return a||p},E=e=>[{words:(0,i.Rw)(e)?[]:e.toString().split(j)}],M=e=>{var{width:t,scaleToFit:r,children:n,style:o,breakAll:i,maxLines:l}=e;if((t||r)&&!a.x.isSsr){var u=k({breakAll:i,children:n,style:o});if(!u)return E(n);var{wordsWithComputedWidth:c,spaceWidth:s}=u;return P({breakAll:i,children:n,maxLines:l,style:o},c,s,t,r)}return E(n)},S="#808080",C=(0,n.forwardRef)((e,t)=>{var r,{x:a=0,y:u=0,lineHeight:c="1em",capHeight:s="0.71em",scaleToFit:d=!1,textAnchor:f="start",verticalAnchor:p="end",fill:h=S}=e,v=O(e,b),y=(0,n.useMemo)(()=>M({breakAll:v.breakAll,children:v.children,maxLines:v.maxLines,scaleToFit:d,style:v.style,width:v.width}),[v.breakAll,v.children,v.maxLines,d,v.style,v.width]),{dx:m,dy:j,angle:k,className:P,breakAll:E}=v,C=O(v,w);if(!(0,i.P2)(a)||!(0,i.P2)(u))return null;var A=a+((0,i.hj)(m)?m:0),T=u+((0,i.hj)(j)?j:0);switch(p){case"start":r=g("calc(".concat(s,")"));break;case"middle":r=g("calc(".concat((y.length-1)/2," * -").concat(c," + (").concat(s," / 2))"));break;default:r=g("calc(".concat(y.length-1," * -").concat(c,")"))}var D=[];if(d){var N=y[0].width,{width:_}=v;D.push("scale(".concat((0,i.hj)(_)?_/N:1,")"))}return k&&D.push("rotate(".concat(k,", ").concat(A,", ").concat(T,")")),D.length&&(C.transform=D.join(" ")),n.createElement("text",x({},(0,l.L6)(C,!0),{ref:t,x:A,y:T,className:(0,o.W)("recharts-text",P),textAnchor:f,fill:h.includes("url")?S:h}),y.map((e,t)=>{var o=e.words.join(E?"":" ");return n.createElement("tspan",{x:A,dy:0===t?r:c,key:"".concat(o,"-").concat(t)},o)}))});C.displayName="Text"},77719:function(e,t,r){"use strict";r.d(t,{u:function(){return ea}});var n=r(2265),o=r(54887),i=r(31104),a=r.n(i),l=r(61994),u=r(16630);function c(){return(c=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function s(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function d(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?s(Object(r),!0).forEach(function(t){var n,o;n=t,o=r[t],(n=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(n))in e?Object.defineProperty(e,n,{value:o,enumerable:!0,configurable:!0,writable:!0}):e[n]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):s(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function f(e){return Array.isArray(e)&&(0,u.P2)(e[0])&&(0,u.P2)(e[1])?e.join(" ~ "):e}var p=e=>{var{separator:t=" : ",contentStyle:r={},itemStyle:o={},labelStyle:i={},payload:s,formatter:p,itemSorter:h,wrapperClassName:v,labelClassName:y,label:m,labelFormatter:g,accessibilityLayer:b=!1}=e,w=d({margin:0,padding:10,backgroundColor:"#fff",border:"1px solid #ccc",whiteSpace:"nowrap"},r),x=d({margin:0},i),O=!(0,u.Rw)(m),j=O?m:"",k=(0,l.W)("recharts-default-tooltip",v),P=(0,l.W)("recharts-tooltip-label",y);return O&&g&&null!=s&&(j=g(m,s)),n.createElement("div",c({className:k,style:w},b?{role:"status","aria-live":"assertive"}:{}),n.createElement("p",{className:P,style:x},n.isValidElement(j)?j:"".concat(j)),(()=>{if(s&&s.length){var e=(h?a()(s,h):s).map((e,r)=>{if("none"===e.type)return null;var i=e.formatter||p||f,{value:a,name:l}=e,c=a,h=l;if(i){var v=i(a,l,e,r,s);if(Array.isArray(v))[c,h]=v;else{if(null==v)return null;c=v}}var y=d({display:"block",paddingTop:4,paddingBottom:4,color:e.color||"#000"},o);return n.createElement("li",{className:"recharts-tooltip-item",key:"tooltip-item-".concat(r),style:y},(0,u.P2)(h)?n.createElement("span",{className:"recharts-tooltip-item-name"},h):null,(0,u.P2)(h)?n.createElement("span",{className:"recharts-tooltip-item-separator"},t):null,n.createElement("span",{className:"recharts-tooltip-item-value"},c),n.createElement("span",{className:"recharts-tooltip-item-unit"},e.unit||""))});return n.createElement("ul",{className:"recharts-tooltip-item-list",style:{padding:0,margin:0}},e)}return null})())},h="recharts-tooltip-wrapper",v={visibility:"hidden"};function y(e){var{allowEscapeViewBox:t,coordinate:r,key:n,offsetTopLeft:o,position:i,reverseDirection:a,tooltipDimension:l,viewBox:c,viewBoxDimension:s}=e;if(i&&(0,u.hj)(i[n]))return i[n];var d=r[n]-l-(o>0?o:0),f=r[n]+o;if(t[n])return a[n]?d:f;var p=c[n];return null==p?0:a[n]?d<p?Math.max(f,p):Math.max(d,p):null==s?0:f+l>p+s?Math.max(d,p):Math.max(f,p)}function m(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function g(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?m(Object(r),!0).forEach(function(t){b(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):m(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function b(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}class w extends n.PureComponent{componentDidMount(){document.addEventListener("keydown",this.handleKeyDown)}componentWillUnmount(){document.removeEventListener("keydown",this.handleKeyDown)}componentDidUpdate(){var e,t;this.state.dismissed&&((null===(e=this.props.coordinate)||void 0===e?void 0:e.x)!==this.state.dismissedAtCoordinate.x||(null===(t=this.props.coordinate)||void 0===t?void 0:t.y)!==this.state.dismissedAtCoordinate.y)&&(this.state.dismissed=!1)}render(){var{active:e,allowEscapeViewBox:t,animationDuration:r,animationEasing:o,children:i,coordinate:a,hasPayload:c,isAnimationActive:s,offset:d,position:f,reverseDirection:p,useTranslate3d:m,viewBox:b,wrapperStyle:w,lastBoundingBox:x,innerRef:O,hasPortalFromProps:j}=this.props,{cssClasses:k,cssProperties:P}=function(e){var t,r,n,{allowEscapeViewBox:o,coordinate:i,offsetTopLeft:a,position:c,reverseDirection:s,tooltipBox:d,useTranslate3d:f,viewBox:p}=e;return{cssProperties:d.height>0&&d.width>0&&i?function(e){var{translateX:t,translateY:r,useTranslate3d:n}=e;return{transform:n?"translate3d(".concat(t,"px, ").concat(r,"px, 0)"):"translate(".concat(t,"px, ").concat(r,"px)")}}({translateX:r=y({allowEscapeViewBox:o,coordinate:i,key:"x",offsetTopLeft:a,position:c,reverseDirection:s,tooltipDimension:d.width,viewBox:p,viewBoxDimension:p.width}),translateY:n=y({allowEscapeViewBox:o,coordinate:i,key:"y",offsetTopLeft:a,position:c,reverseDirection:s,tooltipDimension:d.height,viewBox:p,viewBoxDimension:p.height}),useTranslate3d:f}):v,cssClasses:function(e){var{coordinate:t,translateX:r,translateY:n}=e;return(0,l.W)(h,{["".concat(h,"-right")]:(0,u.hj)(r)&&t&&(0,u.hj)(t.x)&&r>=t.x,["".concat(h,"-left")]:(0,u.hj)(r)&&t&&(0,u.hj)(t.x)&&r<t.x,["".concat(h,"-bottom")]:(0,u.hj)(n)&&t&&(0,u.hj)(t.y)&&n>=t.y,["".concat(h,"-top")]:(0,u.hj)(n)&&t&&(0,u.hj)(t.y)&&n<t.y})}({translateX:r,translateY:n,coordinate:i})}}({allowEscapeViewBox:t,coordinate:a,offsetTopLeft:d,position:f,reverseDirection:p,tooltipBox:{height:x.height,width:x.width},useTranslate3d:m,viewBox:b}),E=j?{}:g(g({transition:s&&e?"transform ".concat(r,"ms ").concat(o):void 0},P),{},{pointerEvents:"none",visibility:!this.state.dismissed&&e&&c?"visible":"hidden",position:"absolute",top:0,left:0}),M=g(g({},E),{},{visibility:!this.state.dismissed&&e&&c?"visible":"hidden"},w);return n.createElement("div",{xmlns:"http://www.w3.org/1999/xhtml",tabIndex:-1,className:k,style:M,ref:O},i)}constructor(){super(...arguments),b(this,"state",{dismissed:!1,dismissedAtCoordinate:{x:0,y:0}}),b(this,"handleKeyDown",e=>{if("Escape"===e.key){var t,r,n,o;this.setState({dismissed:!0,dismissedAtCoordinate:{x:null!==(t=null===(r=this.props.coordinate)||void 0===r?void 0:r.x)&&void 0!==t?t:0,y:null!==(n=null===(o=this.props.coordinate)||void 0===o?void 0:o.y)&&void 0!==n?n:0}})}})}}var x=r(34067),O=r(36841),j=r.n(O),k=r(35953),P=r(14847),E=r(57165),M=r(82944),S=["x","y","top","left","width","height","className"];function C(){return(C=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function A(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}var T=(e,t,r,n,o,i)=>"M".concat(e,",").concat(o,"v").concat(n,"M").concat(i,",").concat(t,"h").concat(r),D=e=>{var{x:t=0,y:r=0,top:o=0,left:i=0,width:a=0,height:c=0,className:s}=e,d=function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?A(Object(r),!0).forEach(function(t){var n,o;n=t,o=r[t],(n=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(n))in e?Object.defineProperty(e,n,{value:o,enumerable:!0,configurable:!0,writable:!0}):e[n]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):A(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}({x:t,y:r,top:o,left:i,width:a,height:c},function(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}(e,S));return(0,u.hj)(t)&&(0,u.hj)(r)&&(0,u.hj)(a)&&(0,u.hj)(c)&&(0,u.hj)(o)&&(0,u.hj)(i)?n.createElement("path",C({},(0,M.L6)(d,!0),{className:(0,l.W)("recharts-cross",s),d:T(t,r,a,c,o,i)})):null},N=r(73649),_=r(39206);function I(e){var{cx:t,cy:r,radius:n,startAngle:o,endAngle:i}=e;return{points:[(0,_.op)(t,r,n,o),(0,_.op)(t,r,n,i)],cx:t,cy:r,radius:n,startAngle:o,endAngle:i}}var R=r(60474),L=r(39040),z=r(49037),W=r(31944);function F(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function B(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?F(Object(r),!0).forEach(function(t){var n,o;n=t,o=r[t],(n=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(n))in e?Object.defineProperty(e,n,{value:o,enumerable:!0,configurable:!0,writable:!0}):e[n]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):F(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var K=()=>(0,L.C)(W.zv),V=()=>{var e=K(),t=(0,L.C)(W.WQ),r=(0,L.C)(W.ri);return(0,z.zT)(B(B({},e),{},{scale:r}),t)},Z=r(84461);function U(){return(U=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function $(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function H(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?$(Object(r),!0).forEach(function(t){var n,o;n=t,o=r[t],(n=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(n))in e?Object.defineProperty(e,n,{value:o,enumerable:!0,configurable:!0,writable:!0}):e[n]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):$(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function Y(e){var t,r,o,{coordinate:i,payload:a,index:u,offset:c,tooltipAxisBandSize:s,layout:d,cursor:f,tooltipEventType:p,chartName:h}=e;if(!f||!i||"ScatterChart"!==h&&"axis"!==p)return null;if("ScatterChart"===h)r=i,o=D;else if("BarChart"===h)t=s/2,r={stroke:"none",fill:"#ccc",x:"horizontal"===d?i.x-t:c.left+.5,y:"horizontal"===d?c.top+.5:i.y-t,width:"horizontal"===d?s:c.width-1,height:"horizontal"===d?c.height-1:s},o=N.A;else if("radial"===d){var{cx:v,cy:y,radius:m,startAngle:g,endAngle:b}=I(i);r={cx:v,cy:y,startAngle:g,endAngle:b,innerRadius:m,outerRadius:m},o=R.L}else r={points:function(e,t,r){var n,o,i,a;if("horizontal"===e)i=n=t.x,o=r.top,a=r.top+r.height;else if("vertical"===e)a=o=t.y,n=r.left,i=r.left+r.width;else if(null!=t.cx&&null!=t.cy){if("centric"!==e)return I(t);var{cx:l,cy:u,innerRadius:c,outerRadius:s,angle:d}=t,f=(0,_.op)(l,u,c,d),p=(0,_.op)(l,u,s,d);n=f.x,o=f.y,i=p.x,a=p.y}return[{x:n,y:o},{x:i,y:a}]}(d,i,c)},o=E.H;var w="object"==typeof f&&"className"in f?f.className:void 0,x=H(H(H(H({stroke:"#ccc",pointerEvents:"none"},c),r),(0,M.L6)(f,!1)),{},{payload:a,payloadIndex:u,className:(0,l.W)("recharts-tooltip-cursor",w)});return(0,n.isValidElement)(f)?(0,n.cloneElement)(f,x):(0,n.createElement)(o,x)}function q(e){var t=V(),r=(0,k.qD)(),o=(0,k.vn)(),i=(0,Z.AC)();return n.createElement(Y,U({},e,{coordinate:e.coordinate,index:e.index,payload:e.payload,offset:r,layout:o,tooltipAxisBandSize:t,chartName:i}))}var X=r(72321),G=r(64725),Q=r(318),J=r(46302),ee=r(40130);function et(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function er(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?et(Object(r),!0).forEach(function(t){var n,o;n=t,o=r[t],(n=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(n))in e?Object.defineProperty(e,n,{value:o,enumerable:!0,configurable:!0,writable:!0}):e[n]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):et(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function en(e){return e.dataKey}var eo=[],ei={allowEscapeViewBox:{x:!1,y:!1},animationDuration:400,animationEasing:"ease",axisId:0,contentStyle:{},cursor:!0,filterNull:!0,isAnimationActive:!x.x.isSsr,itemSorter:"name",itemStyle:{},labelStyle:{},offset:10,reverseDirection:{x:!1,y:!1},separator:" : ",trigger:"hover",useTranslate3d:!1,wrapperStyle:{}};function ea(e){var t,r,i=(0,ee.j)(e,ei),{active:a,allowEscapeViewBox:l,animationDuration:u,animationEasing:c,content:s,filterNull:d,isAnimationActive:f,offset:h,payloadUniqBy:v,position:y,reverseDirection:m,useTranslate3d:g,wrapperStyle:b,cursor:x,shared:O,trigger:E,defaultIndex:M,portal:S,axisId:C}=i,A=(0,L.T)(),T="number"==typeof M?String(M):M;(0,n.useEffect)(()=>{A((0,G.PD)({shared:O,trigger:E,axisId:C,active:a,defaultIndex:T}))},[A,O,E,C,a,T]);var D=(0,k.d2)(),N=(0,P.F)(),_=(0,J.Y4)(O),{activeIndex:I,isActive:R}=(0,L.C)(e=>(0,Z.oo)(e,_,E,T)),z=(0,L.C)(e=>(0,Z.TT)(e,_,E,T)),W=(0,L.C)(e=>(0,Z.i)(e,_,E,T)),F=(0,L.C)(e=>(0,Z.ck)(e,_,E,T)),B=(0,X.C)(),K=null!=a?a:R,[V,U]=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],[t,r]=(0,n.useState)({height:0,left:0,top:0,width:0}),o=(0,n.useCallback)(e=>{if(null!=e){var n=e.getBoundingClientRect(),o={height:n.height,left:n.left,top:n.top,width:n.width};(Math.abs(o.height-t.height)>1||Math.abs(o.left-t.left)>1||Math.abs(o.top-t.top)>1||Math.abs(o.width-t.width)>1)&&r({height:o.height,left:o.left,top:o.top,width:o.width})}},[t.width,t.height,t.top,t.left,...e]);return[t,o]}([z,K]),$="axis"===_?W:void 0;(0,Q.Fg)(_,E,F,$,I,K);var H=null!=S?S:B;if(null==H)return null;var Y=null!=z?z:eo;K||(Y=eo),d&&Y.length&&(t=z.filter(e=>null!=e.value&&(!0!==e.hide||i.includeHidden)),Y=!0===v?j()(t,en):"function"==typeof v?j()(t,v):t);var et=Y.length>0,ea=n.createElement(w,{allowEscapeViewBox:l,animationDuration:u,animationEasing:c,isAnimationActive:f,active:K,coordinate:F,hasPayload:et,offset:h,position:y,reverseDirection:m,useTranslate3d:g,viewBox:D,wrapperStyle:b,lastBoundingBox:V,innerRef:U,hasPortalFromProps:!!S},(r=er(er({},i),{},{payload:Y,label:$,active:K,coordinate:F,accessibilityLayer:N}),n.isValidElement(s)?n.cloneElement(s,r):"function"==typeof s?n.createElement(s,r):n.createElement(p,r)));return n.createElement(n.Fragment,null,(0,o.createPortal)(ea,H),K&&n.createElement(q,{cursor:x,tooltipEventType:_,coordinate:F,payload:z,index:I}))}},9841:function(e,t,r){"use strict";r.d(t,{m:function(){return u}});var n=r(2265),o=r(61994),i=r(82944),a=["children","className"];function l(){return(l=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}var u=n.forwardRef((e,t)=>{var{children:r,className:u}=e,c=function(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}(e,a),s=(0,o.W)("recharts-layer",u);return n.createElement("g",l({className:s},(0,i.L6)(c,!0),{ref:t}),r)})},58735:function(e,t,r){"use strict";r.d(t,{W:function(){return i}});var n=r(2265),o=(0,n.createContext)(null),i=()=>null!=(0,n.useContext)(o)},14847:function(e,t,r){"use strict";r.d(t,{F:function(){return o}});var n=r(39040),o=()=>(0,n.C)(e=>e.rootProps.accessibilityLayer)},1196:function(e,t,r){"use strict";r.d(t,{gt:function(){return l}});var n=r(2265),o=r(39173),i=r(39040),a=r(58735),l=e=>{var{chartData:t}=e,r=(0,i.T)(),l=(0,a.W)();return(0,n.useEffect)(()=>l?()=>{}:(r((0,o.zR)(t)),()=>{r((0,o.zR)(void 0))}),[t,r,l]),null}},35953:function(e,t,r){"use strict";r.d(t,{Mw:function(){return f},d2:function(){return u},qD:function(){return s},rE:function(){return p},vn:function(){return h},zn:function(){return d}}),r(2265);var n=r(39040),o=r(36289),i=r(60152),a=r(58735),l=r(2431),u=()=>{var e,t=(0,a.W)(),r=(0,n.C)(o.nd),i=(0,n.C)(l.V),u=null===(e=(0,n.C)(l.F))||void 0===e?void 0:e.padding;return t&&i&&u?{width:i.width-u.left-u.right,height:i.height-u.top-u.bottom,x:u.left,y:u.top}:r},c={top:0,bottom:0,left:0,right:0,width:0,height:0,brushBottom:0},s=()=>{var e;return null!==(e=(0,n.C)(o.bX))&&void 0!==e?e:c},d=()=>(0,n.C)(i.RD),f=()=>(0,n.C)(i.d_),p=e=>e.layout.layoutType,h=()=>(0,n.C)(p)},44296:function(e,t,r){"use strict";r.d(t,{Df:function(){return i},nC:function(){return l},oQ:function(){return a}});var n=r(39040),o=r(64725),i=(e,t)=>{var r=(0,n.T)();return(n,i)=>a=>{null==e||e(n,i,a),r((0,o.M1)({activeIndex:String(i),activeDataKey:t,activeCoordinate:n.tooltipPosition}))}},a=e=>{var t=(0,n.T)();return(r,n)=>i=>{null==e||e(r,n,i),t((0,o.Vg)())}},l=(e,t)=>{var r=(0,n.T)();return(n,i)=>a=>{null==e||e(n,i,a),r((0,o.O_)({activeIndex:String(i),activeDataKey:t,activeCoordinate:n.tooltipPosition}))}}},72321:function(e,t,r){"use strict";r.d(t,{C:function(){return i},E:function(){return o}});var n=r(2265),o=(0,n.createContext)(null),i=()=>(0,n.useContext)(o)},10062:function(e,t,r){"use strict";r.d(t,{b:function(){return ej},w:function(){return em}});var n=r(2265),o=r(15870),i=r.n(o),a=r(61994),l=r(92713),u=r(22932),c=r(36289),s=r(49037),d=r(98628),f=r(35953),p=r(40304),h=r(56462),v=r(33968),y=e=>e.graphicalItems.polarItems,m=(0,l.P1)([p.z,h.l],d.YZ),g=(0,l.P1)([y,d.fW,m],d.$B),b=(0,l.P1)([g],d.bU),w=(0,l.P1)([b,u.RV],d.tZ),x=(0,l.P1)([w,d.fW,g],d.UA),O=(0,l.P1)([w,d.fW,g],(e,t,r)=>r.length>0?e.flatMap(e=>r.flatMap(r=>{var n;return{value:(0,s.F$)(e,null!==(n=t.dataKey)&&void 0!==n?n:r.dataKey),errorDomain:[]}})).filter(Boolean):(null==t?void 0:t.dataKey)!=null?e.map(e=>({value:(0,s.F$)(e,t.dataKey),errorDomain:[]})):e.map(e=>({value:e,errorDomain:[]}))),j=()=>void 0,k=(0,l.P1)([d.fW,d.KB,j,O,j],d.E8),P=(0,l.P1)([d.fW,f.rE,w,x,v.Qw,p.z,k],d.l_),E=(0,l.P1)([P,d.fW,d.cV],d.vb);function M(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function S(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?M(Object(r),!0).forEach(function(t){var n,o;n=t,o=r[t],(n=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(n))in e?Object.defineProperty(e,n,{value:o,enumerable:!0,configurable:!0,writable:!0}):e[n]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):M(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}(0,l.P1)([d.fW,P,E,p.z],d.kO);var C=(e,t)=>t,A=[],T=(e,t,r)=>(null==r?void 0:r.length)===0?A:r,D=(0,l.P1)([u.RV,C,T],(e,t,r)=>{var n,{chartData:o}=e;if((n=(null==t?void 0:t.data)!=null&&t.data.length>0?t.data:o)&&n.length||null==r||(n=r.map(e=>S(S({},t.presentationProps),e.props))),null!=n)return n}),N=(0,l.P1)([D,C,T],(e,t,r)=>{if(null!=e)return e.map((e,n)=>{var o,i,a=(0,s.F$)(e,t.nameKey,t.name);return i=null!=r&&null!==(o=r[n])&&void 0!==o&&null!==(o=o.props)&&void 0!==o&&o.fill?r[n].props.fill:"object"==typeof e&&null!=e&&"fill"in e?e.fill:t.fill,{value:(0,s.hn)(a,t.dataKey),color:i,payload:e,type:t.legendType}})}),_=(0,l.P1)([y,C],(e,t)=>{if(e.some(e=>"pie"===e.type&&t.dataKey===e.dataKey&&t.data===e.data))return t}),I=(0,l.P1)([D,_,T,c.bX],(e,t,r,n)=>{if(null!=t&&null!=e)return em({offset:n,pieSettings:t,displayedData:e,cells:r})}),R=r(39040),L=r(13790),z=r(9841),W=r(57165),F=r(58811),B=r(20407),K=r(82944),V=r(34067),Z=r(39206),U=r(16630),$=r(41637),H=r(80503),Y=r(44296),q=r(35623),X=r(31944),G=r(62658),Q=r(78487),J=r(59087),ee=r(40130),et=r(46595),er=["onMouseEnter","onClick","onMouseLeave"];function en(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function eo(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?en(Object(r),!0).forEach(function(t){ei(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):en(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function ei(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function ea(){return(ea=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function el(e){var t=(0,n.useMemo)(()=>(0,K.L6)(e,!1),[e]),r=(0,n.useMemo)(()=>(0,K.NN)(e.children,B.b),[e.children]),o=(0,n.useMemo)(()=>({name:e.name,nameKey:e.nameKey,tooltipType:e.tooltipType,data:e.data,dataKey:e.dataKey,cx:e.cx,cy:e.cy,startAngle:e.startAngle,endAngle:e.endAngle,minAngle:e.minAngle,paddingAngle:e.paddingAngle,innerRadius:e.innerRadius,outerRadius:e.outerRadius,cornerRadius:e.cornerRadius,legendType:e.legendType,fill:e.fill,presentationProps:t}),[e.cornerRadius,e.cx,e.cy,e.data,e.dataKey,e.endAngle,e.innerRadius,e.minAngle,e.name,e.nameKey,e.outerRadius,e.paddingAngle,e.startAngle,e.tooltipType,e.legendType,e.fill,t]),i=(0,R.C)(e=>N(e,o,r));return n.createElement(G.t,{legendPayload:i})}function eu(e){var{dataKey:t,nameKey:r,sectors:n,stroke:o,strokeWidth:i,fill:a,name:l,hide:u,tooltipType:c}=e;return{dataDefinedOnItem:null==n?void 0:n.map(e=>e.tooltipPayload),positions:null==n?void 0:n.map(e=>e.tooltipPosition),settings:{stroke:o,strokeWidth:i,fill:a,dataKey:t,nameKey:r,name:(0,s.hn)(l,t),hide:u,type:c,color:a,unit:""}}}var ec=(e,t)=>e>t?"start":e<t?"end":"middle",es=(e,t,r)=>"function"==typeof t?t(e):(0,U.h1)(t,r,.8*r),ed=(e,t,r)=>{var{top:n,left:o,width:i,height:a}=t,l=(0,Z.$4)(i,a),u=o+(0,U.h1)(e.cx,i,i/2),c=n+(0,U.h1)(e.cy,a,a/2);return{cx:u,cy:c,innerRadius:(0,U.h1)(e.innerRadius,l,0),outerRadius:es(r,e.outerRadius,l),maxRadius:e.maxRadius||Math.sqrt(i*i+a*a)/2}},ef=(e,t)=>(0,U.uY)(t-e)*Math.min(Math.abs(t-e),360),ep=(e,t)=>{if(n.isValidElement(e))return n.cloneElement(e,t);if("function"==typeof e)return e(t);var r=(0,a.W)("recharts-pie-label-line","boolean"!=typeof e?e.className:"");return n.createElement(W.H,ea({},t,{type:"linear",className:r}))},eh=(e,t,r)=>{if(n.isValidElement(e))return n.cloneElement(e,t);var o=r;if("function"==typeof e&&(o=e(t),n.isValidElement(o)))return o;var i=(0,a.W)("recharts-pie-label-text","boolean"!=typeof e&&"function"!=typeof e?e.className:"");return n.createElement(F.x,ea({},t,{alignmentBaseline:"middle",className:i}),o)};function ev(e){var{sectors:t,props:r,showLabels:o}=e,{label:i,labelLine:a,dataKey:l}=r;if(!o||!i||!t)return null;var u=(0,K.L6)(r,!1),c=(0,K.L6)(i,!1),d=(0,K.L6)(a,!1),f="object"==typeof i&&"offsetRadius"in i&&i.offsetRadius||20,p=t.map((e,t)=>{var r=(e.startAngle+e.endAngle)/2,o=(0,Z.op)(e.cx,e.cy,e.outerRadius+f,r),p=eo(eo(eo(eo({},u),e),{},{stroke:"none"},c),{},{index:t,textAnchor:ec(o.x,e.cx)},o),h=eo(eo(eo(eo({},u),e),{},{fill:"none",stroke:e.fill},d),{},{index:t,points:[(0,Z.op)(e.cx,e.cy,e.outerRadius,r),o],key:"line"});return n.createElement(z.m,{key:"label-".concat(e.startAngle,"-").concat(e.endAngle,"-").concat(e.midAngle,"-").concat(t)},a&&ep(a,h),eh(i,p,(0,s.F$)(e,l)))});return n.createElement(z.m,{className:"recharts-pie-labels"},p)}function ey(e){var{sectors:t,activeShape:r,inactiveShape:o,allOtherPieProps:i,showLabels:a}=e,l=(0,R.C)(X.Ve),{onMouseEnter:u,onClick:c,onMouseLeave:s}=i,d=function(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}(i,er),f=(0,Y.Df)(u,i.dataKey),p=(0,Y.oQ)(s),h=(0,Y.nC)(c,i.dataKey);return null==t?null:n.createElement(n.Fragment,null,t.map((e,a)=>{if((null==e?void 0:e.startAngle)===0&&(null==e?void 0:e.endAngle)===0&&1!==t.length)return null;var u=r&&String(a)===l,c=u?r:l?o:null,s=eo(eo({},e),{},{stroke:e.stroke,tabIndex:-1,[Q.Gh]:a,[Q.aN]:i.dataKey});return n.createElement(z.m,ea({tabIndex:-1,className:"recharts-pie-sector"},(0,$.bw)(d,e,a),{onMouseEnter:f(e,a),onMouseLeave:p(e,a),onClick:h(e,a),key:"sector-".concat(null==e?void 0:e.startAngle,"-").concat(null==e?void 0:e.endAngle,"-").concat(e.midAngle,"-").concat(a)}),n.createElement(H.b,ea({option:c,isActive:u,shapeType:"sector"},s)))}),n.createElement(ev,{sectors:t,props:i,showLabels:a}))}function em(e){var t,r,n,{pieSettings:o,displayedData:i,cells:a,offset:l}=e,{cornerRadius:u,startAngle:c,endAngle:d,dataKey:f,nameKey:p,tooltipType:h}=o,v=Math.abs(o.minAngle),y=ef(c,d),m=Math.abs(y),g=i.length<=1?0:null!==(t=o.paddingAngle)&&void 0!==t?t:0,b=i.filter(e=>0!==(0,s.F$)(e,f,0)).length,w=m-b*v-(m>=360?b:b-1)*g,x=i.reduce((e,t)=>{var r=(0,s.F$)(t,f,0);return e+((0,U.hj)(r)?r:0)},0);return x>0&&(r=i.map((e,t)=>{var r,i=(0,s.F$)(e,f,0),d=(0,s.F$)(e,p,t),m=ed(o,l,e),b=((0,U.hj)(i)?i:0)/x,O=eo(eo({},e),a&&a[t]&&a[t].props),j=(r=t?n.endAngle+(0,U.uY)(y)*g*(0!==i?1:0):c)+(0,U.uY)(y)*((0!==i?v:0)+b*w),k=(r+j)/2,P=(m.innerRadius+m.outerRadius)/2,E=[{name:d,value:i,payload:O,dataKey:f,type:h}],M=(0,Z.op)(m.cx,m.cy,P,k);return n=eo(eo(eo(eo({},o.presentationProps),{},{percent:b,cornerRadius:u,name:d,tooltipPayload:E,midAngle:k,middleRadius:P,tooltipPosition:M},O),m),{},{value:(0,s.F$)(e,f),startAngle:r,endAngle:j,payload:O,paddingAngle:(0,U.uY)(y)*g})})),r}function eg(e){var{props:t,previousSectorsRef:r}=e,{sectors:o,isAnimationActive:a,animationBegin:l,animationDuration:u,animationEasing:c,activeShape:s,inactiveShape:d,onAnimationStart:f,onAnimationEnd:p}=t,h=(0,J.i)(t,"recharts-pie-"),v=r.current,[y,m]=(0,n.useState)(!0),g=(0,n.useCallback)(()=>{"function"==typeof p&&p(),m(!1)},[p]),b=(0,n.useCallback)(()=>{"function"==typeof f&&f(),m(!0)},[f]);return n.createElement(et.r,{begin:l,duration:u,isActive:a,easing:c,from:{t:0},to:{t:1},onAnimationStart:b,onAnimationEnd:g,key:h},e=>{var{t:a}=e,l=[],u=(o&&o[0]).startAngle;return o.forEach((e,t)=>{var r=v&&v[t],n=t>0?i()(e,"paddingAngle",0):0;if(r){var o=(0,U.k4)(r.endAngle-r.startAngle,e.endAngle-e.startAngle),c=eo(eo({},e),{},{startAngle:u+n,endAngle:u+o(a)+n});l.push(c),u=c.endAngle}else{var{endAngle:s,startAngle:d}=e,f=(0,U.k4)(0,s-d)(a),p=eo(eo({},e),{},{startAngle:u+n,endAngle:u+f+n});l.push(p),u=p.endAngle}}),r.current=l,n.createElement(z.m,null,n.createElement(ey,{sectors:l,activeShape:s,inactiveShape:d,allOtherPieProps:t,showLabels:!y}))})}function eb(e){var{sectors:t,isAnimationActive:r,activeShape:o,inactiveShape:i}=e,a=(0,n.useRef)(null),l=a.current;return r&&t&&t.length&&(!l||l!==t)?n.createElement(eg,{props:e,previousSectorsRef:a}):n.createElement(ey,{sectors:t,activeShape:o,inactiveShape:i,allOtherPieProps:e,showLabels:!0})}function ew(e){var{hide:t,className:r,rootTabIndex:o}=e,i=(0,a.W)("recharts-pie",r);return t?null:n.createElement(z.m,{tabIndex:o,className:i},n.createElement(eb,e))}var ex={animationBegin:400,animationDuration:1500,animationEasing:"ease",cx:"50%",cy:"50%",dataKey:"value",endAngle:360,fill:"#808080",hide:!1,innerRadius:0,isAnimationActive:!V.x.isSsr,labelLine:!0,legendType:"rect",minAngle:0,nameKey:"name",outerRadius:"80%",paddingAngle:0,rootTabIndex:0,startAngle:0,stroke:"#fff"};function eO(e){var t=(0,ee.j)(e,ex),r=(0,n.useMemo)(()=>(0,K.NN)(e.children,B.b),[e.children]),o=(0,K.L6)(t,!1),i=(0,n.useMemo)(()=>({name:t.name,nameKey:t.nameKey,tooltipType:t.tooltipType,data:t.data,dataKey:t.dataKey,cx:t.cx,cy:t.cy,startAngle:t.startAngle,endAngle:t.endAngle,minAngle:t.minAngle,paddingAngle:t.paddingAngle,innerRadius:t.innerRadius,outerRadius:t.outerRadius,cornerRadius:t.cornerRadius,legendType:t.legendType,fill:t.fill,presentationProps:o}),[t.cornerRadius,t.cx,t.cy,t.data,t.dataKey,t.endAngle,t.innerRadius,t.minAngle,t.name,t.nameKey,t.outerRadius,t.paddingAngle,t.startAngle,t.tooltipType,t.legendType,t.fill,o]),a=(0,R.C)(e=>I(e,i,r));return n.createElement(n.Fragment,null,n.createElement(q.k,{fn:eu,args:eo(eo({},t),{},{sectors:a})}),n.createElement(ew,ea({},t,{sectors:a})))}class ej extends n.PureComponent{render(){return n.createElement(n.Fragment,null,n.createElement(L.E,{data:this.props.data,dataKey:this.props.dataKey,hide:this.props.hide,angleAxisId:0,radiusAxisId:0,stackId:void 0,barSize:void 0,type:"pie"}),n.createElement(el,this.props),n.createElement(eO,this.props),this.props.children)}constructor(){super(...arguments),ei(this,"id",(0,U.EL)("recharts-pie-"))}}ei(ej,"displayName","Pie"),ei(ej,"defaultProps",ex)},57165:function(e,t,r){"use strict";r.d(t,{H:function(){return V}});var n=r(2265);function o(){}function i(e,t,r){e._context.bezierCurveTo((2*e._x0+e._x1)/3,(2*e._y0+e._y1)/3,(e._x0+2*e._x1)/3,(e._y0+2*e._y1)/3,(e._x0+4*e._x1+t)/6,(e._y0+4*e._y1+r)/6)}function a(e){this._context=e}function l(e){this._context=e}function u(e){this._context=e}a.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){switch(this._point){case 3:i(this,this._x1,this._y1);case 2:this._context.lineTo(this._x1,this._y1)}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){switch(e=+e,t=+t,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;break;case 2:this._point=3,this._context.lineTo((5*this._x0+this._x1)/6,(5*this._y0+this._y1)/6);default:i(this,e,t)}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t}},l.prototype={areaStart:o,areaEnd:o,lineStart:function(){this._x0=this._x1=this._x2=this._x3=this._x4=this._y0=this._y1=this._y2=this._y3=this._y4=NaN,this._point=0},lineEnd:function(){switch(this._point){case 1:this._context.moveTo(this._x2,this._y2),this._context.closePath();break;case 2:this._context.moveTo((this._x2+2*this._x3)/3,(this._y2+2*this._y3)/3),this._context.lineTo((this._x3+2*this._x2)/3,(this._y3+2*this._y2)/3),this._context.closePath();break;case 3:this.point(this._x2,this._y2),this.point(this._x3,this._y3),this.point(this._x4,this._y4)}},point:function(e,t){switch(e=+e,t=+t,this._point){case 0:this._point=1,this._x2=e,this._y2=t;break;case 1:this._point=2,this._x3=e,this._y3=t;break;case 2:this._point=3,this._x4=e,this._y4=t,this._context.moveTo((this._x0+4*this._x1+e)/6,(this._y0+4*this._y1+t)/6);break;default:i(this,e,t)}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t}},u.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){(this._line||0!==this._line&&3===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){switch(e=+e,t=+t,this._point){case 0:this._point=1;break;case 1:this._point=2;break;case 2:this._point=3;var r=(this._x0+4*this._x1+e)/6,n=(this._y0+4*this._y1+t)/6;this._line?this._context.lineTo(r,n):this._context.moveTo(r,n);break;case 3:this._point=4;default:i(this,e,t)}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t}};class c{constructor(e,t){this._context=e,this._x=t}areaStart(){this._line=0}areaEnd(){this._line=NaN}lineStart(){this._point=0}lineEnd(){(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line}point(e,t){switch(e=+e,t=+t,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;default:this._x?this._context.bezierCurveTo(this._x0=(this._x0+e)/2,this._y0,this._x0,t,e,t):this._context.bezierCurveTo(this._x0,this._y0=(this._y0+t)/2,e,this._y0,e,t)}this._x0=e,this._y0=t}}function s(e){this._context=e}function d(e){this._context=e}function f(e){return new d(e)}function p(e,t,r){var n=e._x1-e._x0,o=t-e._x1,i=(e._y1-e._y0)/(n||o<0&&-0),a=(r-e._y1)/(o||n<0&&-0);return((i<0?-1:1)+(a<0?-1:1))*Math.min(Math.abs(i),Math.abs(a),.5*Math.abs((i*o+a*n)/(n+o)))||0}function h(e,t){var r=e._x1-e._x0;return r?(3*(e._y1-e._y0)/r-t)/2:t}function v(e,t,r){var n=e._x0,o=e._y0,i=e._x1,a=e._y1,l=(i-n)/3;e._context.bezierCurveTo(n+l,o+l*t,i-l,a-l*r,i,a)}function y(e){this._context=e}function m(e){this._context=new g(e)}function g(e){this._context=e}function b(e){this._context=e}function w(e){var t,r,n=e.length-1,o=Array(n),i=Array(n),a=Array(n);for(o[0]=0,i[0]=2,a[0]=e[0]+2*e[1],t=1;t<n-1;++t)o[t]=1,i[t]=4,a[t]=4*e[t]+2*e[t+1];for(o[n-1]=2,i[n-1]=7,a[n-1]=8*e[n-1]+e[n],t=1;t<n;++t)r=o[t]/i[t-1],i[t]-=r,a[t]-=r*a[t-1];for(o[n-1]=a[n-1]/i[n-1],t=n-2;t>=0;--t)o[t]=(a[t]-o[t+1])/i[t];for(t=0,i[n-1]=(e[n]+o[n-1])/2;t<n-1;++t)i[t]=2*e[t+1]-o[t+1];return[o,i]}function x(e,t){this._context=e,this._t=t}s.prototype={areaStart:o,areaEnd:o,lineStart:function(){this._point=0},lineEnd:function(){this._point&&this._context.closePath()},point:function(e,t){e=+e,t=+t,this._point?this._context.lineTo(e,t):(this._point=1,this._context.moveTo(e,t))}},d.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._point=0},lineEnd:function(){(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){switch(e=+e,t=+t,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;default:this._context.lineTo(e,t)}}},y.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=this._t0=NaN,this._point=0},lineEnd:function(){switch(this._point){case 2:this._context.lineTo(this._x1,this._y1);break;case 3:v(this,this._t0,h(this,this._t0))}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){var r=NaN;if(t=+t,(e=+e)!==this._x1||t!==this._y1){switch(this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;break;case 2:this._point=3,v(this,h(this,r=p(this,e,t)),r);break;default:v(this,this._t0,r=p(this,e,t))}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t,this._t0=r}}},(m.prototype=Object.create(y.prototype)).point=function(e,t){y.prototype.point.call(this,t,e)},g.prototype={moveTo:function(e,t){this._context.moveTo(t,e)},closePath:function(){this._context.closePath()},lineTo:function(e,t){this._context.lineTo(t,e)},bezierCurveTo:function(e,t,r,n,o,i){this._context.bezierCurveTo(t,e,n,r,i,o)}},b.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=[],this._y=[]},lineEnd:function(){var e=this._x,t=this._y,r=e.length;if(r){if(this._line?this._context.lineTo(e[0],t[0]):this._context.moveTo(e[0],t[0]),2===r)this._context.lineTo(e[1],t[1]);else for(var n=w(e),o=w(t),i=0,a=1;a<r;++i,++a)this._context.bezierCurveTo(n[0][i],o[0][i],n[1][i],o[1][i],e[a],t[a])}(this._line||0!==this._line&&1===r)&&this._context.closePath(),this._line=1-this._line,this._x=this._y=null},point:function(e,t){this._x.push(+e),this._y.push(+t)}},x.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=this._y=NaN,this._point=0},lineEnd:function(){0<this._t&&this._t<1&&2===this._point&&this._context.lineTo(this._x,this._y),(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line>=0&&(this._t=1-this._t,this._line=1-this._line)},point:function(e,t){switch(e=+e,t=+t,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;default:if(this._t<=0)this._context.lineTo(this._x,t),this._context.lineTo(e,t);else{var r=this._x*(1-this._t)+e*this._t;this._context.lineTo(r,this._y),this._context.lineTo(r,t)}}this._x=e,this._y=t}};var O=r(22516),j=r(76115),k=r(67790);function P(e){return e[0]}function E(e){return e[1]}function M(e,t){var r=(0,j.Z)(!0),n=null,o=f,i=null,a=(0,k.d)(l);function l(l){var u,c,s,d=(l=(0,O.Z)(l)).length,f=!1;for(null==n&&(i=o(s=a())),u=0;u<=d;++u)!(u<d&&r(c=l[u],u,l))===f&&((f=!f)?i.lineStart():i.lineEnd()),f&&i.point(+e(c,u,l),+t(c,u,l));if(s)return i=null,s+""||null}return e="function"==typeof e?e:void 0===e?P:(0,j.Z)(e),t="function"==typeof t?t:void 0===t?E:(0,j.Z)(t),l.x=function(t){return arguments.length?(e="function"==typeof t?t:(0,j.Z)(+t),l):e},l.y=function(e){return arguments.length?(t="function"==typeof e?e:(0,j.Z)(+e),l):t},l.defined=function(e){return arguments.length?(r="function"==typeof e?e:(0,j.Z)(!!e),l):r},l.curve=function(e){return arguments.length?(o=e,null!=n&&(i=o(n)),l):o},l.context=function(e){return arguments.length?(null==e?n=i=null:i=o(n=e),l):n},l}function S(e,t,r){var n=null,o=(0,j.Z)(!0),i=null,a=f,l=null,u=(0,k.d)(c);function c(c){var s,d,f,p,h,v=(c=(0,O.Z)(c)).length,y=!1,m=Array(v),g=Array(v);for(null==i&&(l=a(h=u())),s=0;s<=v;++s){if(!(s<v&&o(p=c[s],s,c))===y){if(y=!y)d=s,l.areaStart(),l.lineStart();else{for(l.lineEnd(),l.lineStart(),f=s-1;f>=d;--f)l.point(m[f],g[f]);l.lineEnd(),l.areaEnd()}}y&&(m[s]=+e(p,s,c),g[s]=+t(p,s,c),l.point(n?+n(p,s,c):m[s],r?+r(p,s,c):g[s]))}if(h)return l=null,h+""||null}function s(){return M().defined(o).curve(a).context(i)}return e="function"==typeof e?e:void 0===e?P:(0,j.Z)(+e),t="function"==typeof t?t:void 0===t?(0,j.Z)(0):(0,j.Z)(+t),r="function"==typeof r?r:void 0===r?E:(0,j.Z)(+r),c.x=function(t){return arguments.length?(e="function"==typeof t?t:(0,j.Z)(+t),n=null,c):e},c.x0=function(t){return arguments.length?(e="function"==typeof t?t:(0,j.Z)(+t),c):e},c.x1=function(e){return arguments.length?(n=null==e?null:"function"==typeof e?e:(0,j.Z)(+e),c):n},c.y=function(e){return arguments.length?(t="function"==typeof e?e:(0,j.Z)(+e),r=null,c):t},c.y0=function(e){return arguments.length?(t="function"==typeof e?e:(0,j.Z)(+e),c):t},c.y1=function(e){return arguments.length?(r=null==e?null:"function"==typeof e?e:(0,j.Z)(+e),c):r},c.lineX0=c.lineY0=function(){return s().x(e).y(t)},c.lineY1=function(){return s().x(e).y(r)},c.lineX1=function(){return s().x(n).y(t)},c.defined=function(e){return arguments.length?(o="function"==typeof e?e:(0,j.Z)(!!e),c):o},c.curve=function(e){return arguments.length?(a=e,null!=i&&(l=a(i)),c):a},c.context=function(e){return arguments.length?(null==e?i=l=null:l=a(i=e),c):i},c}var C=r(61994),A=r(41637),T=r(82944),D=r(16630),N=r(66395);function _(){return(_=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function I(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function R(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?I(Object(r),!0).forEach(function(t){var n,o;n=t,o=r[t],(n=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(n))in e?Object.defineProperty(e,n,{value:o,enumerable:!0,configurable:!0,writable:!0}):e[n]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):I(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var L={curveBasisClosed:function(e){return new l(e)},curveBasisOpen:function(e){return new u(e)},curveBasis:function(e){return new a(e)},curveBumpX:function(e){return new c(e,!0)},curveBumpY:function(e){return new c(e,!1)},curveLinearClosed:function(e){return new s(e)},curveLinear:f,curveMonotoneX:function(e){return new y(e)},curveMonotoneY:function(e){return new m(e)},curveNatural:function(e){return new b(e)},curveStep:function(e){return new x(e,.5)},curveStepAfter:function(e){return new x(e,1)},curveStepBefore:function(e){return new x(e,0)}},z=e=>(0,N.n)(e.x)&&(0,N.n)(e.y),W=e=>e.x,F=e=>e.y,B=(e,t)=>{if("function"==typeof e)return e;var r="curve".concat((0,D.jC)(e));return("curveMonotone"===r||"curveBump"===r)&&t?L["".concat(r).concat("vertical"===t?"Y":"X")]:L[r]||f},K=e=>{var t,{type:r="linear",points:n=[],baseLine:o,layout:i,connectNulls:a=!1}=e,l=B(r,i),u=a?n.filter(z):n;if(Array.isArray(o)){var c=a?o.filter(e=>z(e)):o,s=u.map((e,t)=>R(R({},e),{},{base:c[t]}));return(t="vertical"===i?S().y(F).x1(W).x0(e=>e.base.x):S().x(W).y1(F).y0(e=>e.base.y)).defined(z).curve(l),t(s)}return(t="vertical"===i&&(0,D.hj)(o)?S().y(F).x1(W).x0(o):(0,D.hj)(o)?S().x(W).y1(F).y0(o):M().x(W).y(F)).defined(z).curve(l),t(u)},V=e=>{var{className:t,points:r,path:o,pathRef:i}=e;if((!r||!r.length)&&!o)return null;var a=r&&r.length?K(e):o;return n.createElement("path",_({},(0,T.L6)(e,!1),(0,A.Ym)(e),{className:(0,C.W)("recharts-curve",t),d:null===a?void 0:a,ref:i}))}},73649:function(e,t,r){"use strict";r.d(t,{A:function(){return d}});var n=r(2265),o=r(61994),i=r(82944),a=r(40130),l=r(46595);function u(){return(u=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}var c=(e,t,r,n,o)=>{var i,a=Math.min(Math.abs(r)/2,Math.abs(n)/2),l=n>=0?1:-1,u=r>=0?1:-1,c=n>=0&&r>=0||n<0&&r<0?1:0;if(a>0&&o instanceof Array){for(var s=[0,0,0,0],d=0;d<4;d++)s[d]=o[d]>a?a:o[d];i="M".concat(e,",").concat(t+l*s[0]),s[0]>0&&(i+="A ".concat(s[0],",").concat(s[0],",0,0,").concat(c,",").concat(e+u*s[0],",").concat(t)),i+="L ".concat(e+r-u*s[1],",").concat(t),s[1]>0&&(i+="A ".concat(s[1],",").concat(s[1],",0,0,").concat(c,",\n        ").concat(e+r,",").concat(t+l*s[1])),i+="L ".concat(e+r,",").concat(t+n-l*s[2]),s[2]>0&&(i+="A ".concat(s[2],",").concat(s[2],",0,0,").concat(c,",\n        ").concat(e+r-u*s[2],",").concat(t+n)),i+="L ".concat(e+u*s[3],",").concat(t+n),s[3]>0&&(i+="A ".concat(s[3],",").concat(s[3],",0,0,").concat(c,",\n        ").concat(e,",").concat(t+n-l*s[3])),i+="Z"}else if(a>0&&o===+o&&o>0){var f=Math.min(a,o);i="M ".concat(e,",").concat(t+l*f,"\n            A ").concat(f,",").concat(f,",0,0,").concat(c,",").concat(e+u*f,",").concat(t,"\n            L ").concat(e+r-u*f,",").concat(t,"\n            A ").concat(f,",").concat(f,",0,0,").concat(c,",").concat(e+r,",").concat(t+l*f,"\n            L ").concat(e+r,",").concat(t+n-l*f,"\n            A ").concat(f,",").concat(f,",0,0,").concat(c,",").concat(e+r-u*f,",").concat(t+n,"\n            L ").concat(e+u*f,",").concat(t+n,"\n            A ").concat(f,",").concat(f,",0,0,").concat(c,",").concat(e,",").concat(t+n-l*f," Z")}else i="M ".concat(e,",").concat(t," h ").concat(r," v ").concat(n," h ").concat(-r," Z");return i},s={x:0,y:0,width:0,height:0,radius:0,isAnimationActive:!1,isUpdateAnimationActive:!1,animationBegin:0,animationDuration:1500,animationEasing:"ease"},d=e=>{var t=(0,a.j)(e,s),r=(0,n.useRef)(null),[d,f]=(0,n.useState)(-1);(0,n.useEffect)(()=>{if(r.current&&r.current.getTotalLength)try{var e=r.current.getTotalLength();e&&f(e)}catch(e){}},[]);var{x:p,y:h,width:v,height:y,radius:m,className:g}=t,{animationEasing:b,animationDuration:w,animationBegin:x,isAnimationActive:O,isUpdateAnimationActive:j}=t;if(p!==+p||h!==+h||v!==+v||y!==+y||0===v||0===y)return null;var k=(0,o.W)("recharts-rectangle",g);return j?n.createElement(l.r,{canBegin:d>0,from:{width:v,height:y,x:p,y:h},to:{width:v,height:y,x:p,y:h},duration:w,animationEasing:b,isActive:j},e=>{var{width:o,height:a,x:s,y:f}=e;return n.createElement(l.r,{canBegin:d>0,from:"0px ".concat(-1===d?1:d,"px"),to:"".concat(d,"px 0px"),attributeName:"strokeDasharray",begin:x,duration:w,isActive:O,easing:b},n.createElement("path",u({},(0,i.L6)(t,!0),{className:k,d:c(s,f,o,a,m),ref:r})))}):n.createElement("path",u({},(0,i.L6)(t,!0),{className:k,d:c(p,h,v,y,m)}))}},60474:function(e,t,r){"use strict";r.d(t,{L:function(){return v}});var n=r(2265),o=r(61994),i=r(82944),a=r(39206),l=r(16630),u=r(40130);function c(){return(c=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}var s=(e,t)=>(0,l.uY)(t-e)*Math.min(Math.abs(t-e),359.999),d=e=>{var{cx:t,cy:r,radius:n,angle:o,sign:i,isExternal:l,cornerRadius:u,cornerIsExternal:c}=e,s=u*(l?1:-1)+n,d=Math.asin(u/s)/a.Wk,f=c?o:o+i*d;return{center:(0,a.op)(t,r,s,f),circleTangency:(0,a.op)(t,r,n,f),lineTangency:(0,a.op)(t,r,s*Math.cos(d*a.Wk),c?o-i*d:o),theta:d}},f=e=>{var{cx:t,cy:r,innerRadius:n,outerRadius:o,startAngle:i,endAngle:l}=e,u=s(i,l),c=i+u,d=(0,a.op)(t,r,o,i),f=(0,a.op)(t,r,o,c),p="M ".concat(d.x,",").concat(d.y,"\n    A ").concat(o,",").concat(o,",0,\n    ").concat(+(Math.abs(u)>180),",").concat(+(i>c),",\n    ").concat(f.x,",").concat(f.y,"\n  ");if(n>0){var h=(0,a.op)(t,r,n,i),v=(0,a.op)(t,r,n,c);p+="L ".concat(v.x,",").concat(v.y,"\n            A ").concat(n,",").concat(n,",0,\n            ").concat(+(Math.abs(u)>180),",").concat(+(i<=c),",\n            ").concat(h.x,",").concat(h.y," Z")}else p+="L ".concat(t,",").concat(r," Z");return p},p=e=>{var{cx:t,cy:r,innerRadius:n,outerRadius:o,cornerRadius:i,forceCornerRadius:a,cornerIsExternal:u,startAngle:c,endAngle:s}=e,p=(0,l.uY)(s-c),{circleTangency:h,lineTangency:v,theta:y}=d({cx:t,cy:r,radius:o,angle:c,sign:p,cornerRadius:i,cornerIsExternal:u}),{circleTangency:m,lineTangency:g,theta:b}=d({cx:t,cy:r,radius:o,angle:s,sign:-p,cornerRadius:i,cornerIsExternal:u}),w=u?Math.abs(c-s):Math.abs(c-s)-y-b;if(w<0)return a?"M ".concat(v.x,",").concat(v.y,"\n        a").concat(i,",").concat(i,",0,0,1,").concat(2*i,",0\n        a").concat(i,",").concat(i,",0,0,1,").concat(-(2*i),",0\n      "):f({cx:t,cy:r,innerRadius:n,outerRadius:o,startAngle:c,endAngle:s});var x="M ".concat(v.x,",").concat(v.y,"\n    A").concat(i,",").concat(i,",0,0,").concat(+(p<0),",").concat(h.x,",").concat(h.y,"\n    A").concat(o,",").concat(o,",0,").concat(+(w>180),",").concat(+(p<0),",").concat(m.x,",").concat(m.y,"\n    A").concat(i,",").concat(i,",0,0,").concat(+(p<0),",").concat(g.x,",").concat(g.y,"\n  ");if(n>0){var{circleTangency:O,lineTangency:j,theta:k}=d({cx:t,cy:r,radius:n,angle:c,sign:p,isExternal:!0,cornerRadius:i,cornerIsExternal:u}),{circleTangency:P,lineTangency:E,theta:M}=d({cx:t,cy:r,radius:n,angle:s,sign:-p,isExternal:!0,cornerRadius:i,cornerIsExternal:u}),S=u?Math.abs(c-s):Math.abs(c-s)-k-M;if(S<0&&0===i)return"".concat(x,"L").concat(t,",").concat(r,"Z");x+="L".concat(E.x,",").concat(E.y,"\n      A").concat(i,",").concat(i,",0,0,").concat(+(p<0),",").concat(P.x,",").concat(P.y,"\n      A").concat(n,",").concat(n,",0,").concat(+(S>180),",").concat(+(p>0),",").concat(O.x,",").concat(O.y,"\n      A").concat(i,",").concat(i,",0,0,").concat(+(p<0),",").concat(j.x,",").concat(j.y,"Z")}else x+="L".concat(t,",").concat(r,"Z");return x},h={cx:0,cy:0,innerRadius:0,outerRadius:0,startAngle:0,endAngle:0,cornerRadius:0,forceCornerRadius:!1,cornerIsExternal:!1},v=e=>{var t,r=(0,u.j)(e,h),{cx:a,cy:s,innerRadius:d,outerRadius:v,cornerRadius:y,forceCornerRadius:m,cornerIsExternal:g,startAngle:b,endAngle:w,className:x}=r;if(v<d||b===w)return null;var O=(0,o.W)("recharts-sector",x),j=v-d,k=(0,l.h1)(y,j,0,!0);return t=k>0&&360>Math.abs(b-w)?p({cx:a,cy:s,innerRadius:d,outerRadius:v,cornerRadius:Math.min(k,j/2),forceCornerRadius:m,cornerIsExternal:g,startAngle:b,endAngle:w}):f({cx:a,cy:s,innerRadius:d,outerRadius:v,startAngle:b,endAngle:w}),n.createElement("path",c({},(0,i.L6)(r,!0),{className:O,d:t}))}},50209:function(e,t,r){"use strict";r.d(t,{l:function(){return n}});var n=(0,r(2265).createContext)(null)},82931:function(e,t,r){"use strict";r.d(t,{M:function(){return Z}});var n=r(2265),o=r(68575),i=r(59688),a=r(39129),l=r(31057),u=r(64725),c=r(39173),s=r(65293),d=r(48323);function f(e,t){return t instanceof HTMLElement?"HTMLElement <".concat(t.tagName,' class="').concat(t.className,'">'):t===window?"global.window":t}var p=r(17644),h=r(19579),v=r(10418),y=(0,a.oM)({name:"referenceElements",initialState:{dots:[],areas:[],lines:[]},reducers:{addDot:(e,t)=>{e.dots.push(t.payload)},removeDot:(e,t)=>{var r=(0,v.Vk)(e).dots.findIndex(e=>e===t.payload);-1!==r&&e.dots.splice(r,1)},addArea:(e,t)=>{e.areas.push(t.payload)},removeArea:(e,t)=>{var r=(0,v.Vk)(e).areas.findIndex(e=>e===t.payload);-1!==r&&e.areas.splice(r,1)},addLine:(e,t)=>{e.lines.push(t.payload)},removeLine:(e,t)=>{var r=(0,v.Vk)(e).lines.findIndex(e=>e===t.payload);-1!==r&&e.lines.splice(r,1)}}}),{addDot:m,removeDot:g,addArea:b,removeArea:w,addLine:x,removeLine:O}=y.actions,j=y.reducer,k={x:0,y:0,width:0,height:0,padding:{top:0,right:0,bottom:0,left:0}},P=(0,a.oM)({name:"brush",initialState:k,reducers:{setBrushSettings:(e,t)=>null==t.payload?k:t.payload}}),{setBrushSettings:E}=P.actions,M=P.reducer,S=r(32738),C=r(59156),A=(0,a.oM)({name:"polarAxis",initialState:{radiusAxis:{},angleAxis:{}},reducers:{addRadiusAxis(e,t){e.radiusAxis[t.payload.id]=(0,v.cA)(t.payload)},removeRadiusAxis(e,t){delete e.radiusAxis[t.payload.id]},addAngleAxis(e,t){e.angleAxis[t.payload.id]=(0,v.cA)(t.payload)},removeAngleAxis(e,t){delete e.angleAxis[t.payload.id]}}}),{addRadiusAxis:T,removeRadiusAxis:D,addAngleAxis:N,removeAngleAxis:_}=A.actions,I=A.reducer,R=r(27410),L=r(7883),z=r(69366),W=r(83061),F=(0,i.UY)({brush:M,cartesianAxis:p.vk,chartData:c.Q2,graphicalItems:h.iX,layout:s.EW,legend:S.Ny,options:l.wB,polarAxis:I,polarOptions:R.i,referenceElements:j,rootProps:C.$f,tooltip:u.Kw}),B=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"Chart";return(0,a.xC)({reducer:F,preloadedState:e,middleware:e=>e({serializableCheck:!1}).concat([d.RG.middleware,d.an.middleware,L._2.middleware,z.Y.middleware,W.x.middleware]),devTools:{serialize:{replacer:f},name:"recharts-".concat(t)}})},K=r(58735),V=r(50209);function Z(e){var{preloadedState:t,children:r,reduxStoreName:i}=e,a=(0,K.W)(),l=(0,n.useRef)(null);if(a)return r;null==l.current&&(l.current=B(t,i));var u=V.l;return n.createElement(o.zt,{context:u,store:l.current},r)}},15317:function(e,t,r){"use strict";r.d(t,{b:function(){return a}});var n=r(2265),o=r(59156),i=r(39040);function a(e){var t=(0,i.T)();return(0,n.useEffect)(()=>{t((0,o.Rk)(e))},[t,e]),null}},87235:function(e,t,r){"use strict";r.d(t,{v:function(){return l}});var n=r(2265),o=r(58735),i=r(65293),a=r(39040);function l(e){var{layout:t,width:r,height:l,margin:u}=e,c=(0,a.T)(),s=(0,o.W)();return(0,n.useEffect)(()=>{s||(c((0,i.jx)(t)),c((0,i.Dx)({width:r,height:l})),c((0,i.Qb)(u)))},[c,s,t,r,l,u]),null}},13790:function(e,t,r){"use strict";r.d(t,{E:function(){return s},V:function(){return c}});var n=r(2265),o=r(39040),i=r(19579),a=r(49037);function l(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function u(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?l(Object(r),!0).forEach(function(t){var n,o;n=t,o=r[t],(n=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(n))in e?Object.defineProperty(e,n,{value:o,enumerable:!0,configurable:!0,writable:!0}):e[n]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):l(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function c(e){var t=(0,o.T)();return(0,n.useEffect)(()=>{var r=u(u({},e),{},{stackId:(0,a.GA)(e.stackId)});return t((0,i.AC)(r)),()=>{t((0,i._Q)(r))}},[t,e]),null}function s(e){var t=(0,o.T)();return(0,n.useEffect)(()=>(t((0,i.Wz)(e)),()=>{t((0,i.ot)(e))}),[t,e]),null}},62658:function(e,t,r){"use strict";r.d(t,{L:function(){return c},t:function(){return s}});var n=r(2265),o=r(58735),i=r(35953),a=r(39040),l=r(32738),u=()=>{};function c(e){var{legendPayload:t}=e,r=(0,a.T)(),i=(0,o.W)();return(0,n.useEffect)(()=>i?u:(r((0,l.t8)(t)),()=>{r((0,l.ZR)(t))}),[r,i,t]),null}function s(e){var{legendPayload:t}=e,r=(0,a.T)(),o=(0,a.C)(i.rE);return(0,n.useEffect)(()=>"centric"!==o&&"radial"!==o?u:(r((0,l.t8)(t)),()=>{r((0,l.ZR)(t))}),[r,o,t]),null}},35623:function(e,t,r){"use strict";r.d(t,{k:function(){return l}});var n=r(2265),o=r(39040),i=r(64725),a=r(58735);function l(e){var{fn:t,args:r}=e,l=(0,o.T)(),u=(0,a.W)();return(0,n.useEffect)(()=>{if(!u){var e=t(r);return l((0,i.KO)(e)),()=>{l((0,i.cK)(e))}}},[t,r,l,u]),null}},17644:function(e,t,r){"use strict";r.d(t,{Jj:function(){return c},TC:function(){return s},cB:function(){return d},kB:function(){return h},m2:function(){return u},vk:function(){return v}});var n=r(39129),o=r(10418);function i(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function a(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?i(Object(r),!0).forEach(function(t){var n,o;n=t,o=r[t],(n=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(n))in e?Object.defineProperty(e,n,{value:o,enumerable:!0,configurable:!0,writable:!0}):e[n]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):i(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var l=(0,n.oM)({name:"cartesianAxis",initialState:{xAxis:{},yAxis:{},zAxis:{}},reducers:{addXAxis(e,t){e.xAxis[t.payload.id]=(0,o.cA)(t.payload)},removeXAxis(e,t){delete e.xAxis[t.payload.id]},addYAxis(e,t){e.yAxis[t.payload.id]=(0,o.cA)(t.payload)},removeYAxis(e,t){delete e.yAxis[t.payload.id]},addZAxis(e,t){e.zAxis[t.payload.id]=(0,o.cA)(t.payload)},removeZAxis(e,t){delete e.zAxis[t.payload.id]},updateYAxisWidth(e,t){var{id:r,width:n}=t.payload;e.yAxis[r]&&(e.yAxis[r]=a(a({},e.yAxis[r]),{},{width:n}))}}}),{addXAxis:u,removeXAxis:c,addYAxis:s,removeYAxis:d,addZAxis:f,removeZAxis:p,updateYAxisWidth:h}=l.actions,v=l.reducer},39173:function(e,t,r){"use strict";r.d(t,{Q2:function(){return l},t0:function(){return i},zR:function(){return o}});var n=(0,r(39129).oM)({name:"chartData",initialState:{chartData:void 0,computedData:void 0,dataStartIndex:0,dataEndIndex:0},reducers:{setChartData(e,t){if(e.chartData=t.payload,null==t.payload){e.dataStartIndex=0,e.dataEndIndex=0;return}t.payload.length>0&&e.dataEndIndex!==t.payload.length-1&&(e.dataEndIndex=t.payload.length-1)},setComputedData(e,t){e.computedData=t.payload},setDataStartEndIndexes(e,t){var{startIndex:r,endIndex:n}=t.payload;null!=r&&(e.dataStartIndex=r),null!=n&&(e.dataEndIndex=n)}}}),{setChartData:o,setDataStartEndIndexes:i,setComputedData:a}=n.actions,l=n.reducer},69366:function(e,t,r){"use strict";r.d(t,{Y:function(){return a},r:function(){return i}});var n=r(39129),o=r(31944),i=(0,n.PH)("externalEvent"),a=(0,n.e)();a.startListening({actionCreator:i,effect:(e,t)=>{if(null!=e.payload.handler){var r=t.getState(),n={activeCoordinate:(0,o.pI)(r),activeDataKey:(0,o.du)(r),activeIndex:(0,o.Ve)(r),activeLabel:(0,o.i)(r),activeTooltipIndex:(0,o.Ve)(r),isTooltipActive:(0,o.oo)(r)};e.payload.handler(n,e.payload.reactEvent)}}})},19579:function(e,t,r){"use strict";r.d(t,{AC:function(){return u},Wz:function(){return s},_Q:function(){return c},a1:function(){return a},iX:function(){return f},nF:function(){return l},ot:function(){return d}});var n=r(39129),o=r(10418),i=(0,n.oM)({name:"graphicalItems",initialState:{countOfBars:0,cartesianItems:[],polarItems:[]},reducers:{addBar(e){e.countOfBars+=1},removeBar(e){e.countOfBars-=1},addCartesianGraphicalItem(e,t){e.cartesianItems.push((0,o.cA)(t.payload))},removeCartesianGraphicalItem(e,t){var r=(0,o.Vk)(e).cartesianItems.indexOf((0,o.cA)(t.payload));r>-1&&e.cartesianItems.splice(r,1)},addPolarGraphicalItem(e,t){e.polarItems.push((0,o.cA)(t.payload))},removePolarGraphicalItem(e,t){var r=(0,o.Vk)(e).polarItems.indexOf((0,o.cA)(t.payload));r>-1&&e.polarItems.splice(r,1)}}}),{addBar:a,removeBar:l,addCartesianGraphicalItem:u,removeCartesianGraphicalItem:c,addPolarGraphicalItem:s,removePolarGraphicalItem:d}=i.actions,f=i.reducer},39040:function(e,t,r){"use strict";r.d(t,{C:function(){return d},T:function(){return l}});var n=r(35195),o=r(2265),i=r(50209),a=e=>e,l=()=>{var e=(0,o.useContext)(i.l);return e?e.store.dispatch:a},u=()=>{},c=()=>u,s=(e,t)=>e===t;function d(e){var t=(0,o.useContext)(i.l);return(0,n.useSyncExternalStoreWithSelector)(t?t.subscription.addNestedSub:c,t?t.store.getState:u,t?t.store.getState:u,t?e:u,s)}},7883:function(e,t,r){"use strict";r.d(t,{_2:function(){return d},eb:function(){return s},sj:function(){return c}});var n=r(39129),o=r(64725),i=r(31944),a=r(84461),l=r(98628),u=r(6064),c=(0,n.PH)("keyDown"),s=(0,n.PH)("focus"),d=(0,n.e)();d.startListening({actionCreator:c,effect:(e,t)=>{var r=t.getState();if(!1!==r.rootProps.accessibilityLayer){var{keyboardInteraction:n}=r.tooltip,c=e.payload;if("ArrowRight"===c||"ArrowLeft"===c||"Enter"===c){var s=Number((0,u.p)(n,(0,i.wQ)(r))),d=(0,i.WQ)(r);if("Enter"===c){var f=(0,a.hA)(r,"axis","hover",String(n.index));t.dispatch((0,o.KN)({active:!n.active,activeIndex:n.index,activeDataKey:n.dataKey,activeCoordinate:f}));return}var p=s+("ArrowRight"===c?1:-1)*("left-to-right"===(0,l.Xv)(r)?1:-1);if(null!=d&&!(p>=d.length)&&!(p<0)){var h=(0,a.hA)(r,"axis","hover",String(p));t.dispatch((0,o.KN)({active:!0,activeIndex:p.toString(),activeDataKey:void 0,activeCoordinate:h}))}}}}}),d.startListening({actionCreator:s,effect:(e,t)=>{var r=t.getState();if(!1!==r.rootProps.accessibilityLayer){var{keyboardInteraction:n}=r.tooltip;if(!n.active&&null==n.index){var i=(0,a.hA)(r,"axis","hover",String("0"));t.dispatch((0,o.KN)({activeDataKey:void 0,active:!0,activeIndex:"0",activeCoordinate:i}))}}}})},65293:function(e,t,r){"use strict";r.d(t,{Dx:function(){return a},EW:function(){return u},Qb:function(){return o},ZP:function(){return l},jx:function(){return i}});var n=(0,r(39129).oM)({name:"chartLayout",initialState:{layoutType:"horizontal",width:0,height:0,margin:{top:5,right:5,bottom:5,left:5},scale:1},reducers:{setLayout(e,t){e.layoutType=t.payload},setChartSize(e,t){e.width=t.payload.width,e.height=t.payload.height},setMargin(e,t){e.margin.top=t.payload.top,e.margin.right=t.payload.right,e.margin.bottom=t.payload.bottom,e.margin.left=t.payload.left},setScale(e,t){e.scale=t.payload}}}),{setMargin:o,setLayout:i,setChartSize:a,setScale:l}=n.actions,u=n.reducer},32738:function(e,t,r){"use strict";r.d(t,{Ny:function(){return s},ZR:function(){return c},t8:function(){return u}});var n=r(39129),o=r(10418),i=(0,n.oM)({name:"legend",initialState:{settings:{layout:"horizontal",align:"center",verticalAlign:"middle",itemSorter:"value"},size:{width:0,height:0},payload:[]},reducers:{setLegendSize(e,t){e.size.width=t.payload.width,e.size.height=t.payload.height},setLegendSettings(e,t){e.settings.align=t.payload.align,e.settings.layout=t.payload.layout,e.settings.verticalAlign=t.payload.verticalAlign,e.settings.itemSorter=t.payload.itemSorter},addLegendPayload(e,t){e.payload.push((0,o.cA)(t.payload))},removeLegendPayload(e,t){var r=(0,o.Vk)(e).payload.indexOf((0,o.cA)(t.payload));r>-1&&e.payload.splice(r,1)}}}),{setLegendSize:a,setLegendSettings:l,addLegendPayload:u,removeLegendPayload:c}=i.actions,s=i.reducer},48323:function(e,t,r){"use strict";r.d(t,{AE:function(){return u},RG:function(){return c},TK:function(){return s},an:function(){return d}});var n=r(39129),o=r(64725),i=r(63330),a=r(46302),l=r(13169),u=(0,n.PH)("mouseClick"),c=(0,n.e)();c.startListening({actionCreator:u,effect:(e,t)=>{var r=e.payload,n=(0,i.h)(t.getState(),(0,l.a)(r));(null==n?void 0:n.activeIndex)!=null&&t.dispatch((0,o.eB)({activeIndex:n.activeIndex,activeDataKey:void 0,activeCoordinate:n.activeCoordinate}))}});var s=(0,n.PH)("mouseMove"),d=(0,n.e)();d.startListening({actionCreator:s,effect:(e,t)=>{var r=e.payload,n=t.getState(),u=(0,a.SB)(n,n.tooltip.settings.shared),c=(0,i.h)(n,(0,l.a)(r));"axis"===u&&((null==c?void 0:c.activeIndex)!=null?t.dispatch((0,o.Rr)({activeIndex:c.activeIndex,activeDataKey:void 0,activeCoordinate:c.activeCoordinate})):t.dispatch((0,o.ne)()))}})},31057:function(e,t,r){"use strict";r.d(t,{IC:function(){return u},NL:function(){return i},wB:function(){return l}});var n=r(39129),o=r(16630);function i(e,t){if(t){var r=Number.parseInt(t,10);if(!(0,o.In)(r))return null==e?void 0:e[r]}}var a=(0,n.oM)({name:"options",initialState:{chartName:"",tooltipPayloadSearcher:void 0,eventEmitter:void 0,defaultTooltipEventType:"axis"},reducers:{createEventEmitter:e=>{null==e.eventEmitter&&(e.eventEmitter=Symbol("rechartsEventEmitter"))}}}),l=a.reducer,{createEventEmitter:u}=a.actions},27410:function(e,t,r){"use strict";r.d(t,{a:function(){return o},i:function(){return i}});var n=(0,r(39129).oM)({name:"polarOptions",initialState:null,reducers:{updatePolarOptions:(e,t)=>t.payload}}),{updatePolarOptions:o}=n.actions,i=n.reducer},59156:function(e,t,r){"use strict";r.d(t,{$f:function(){return a},Rk:function(){return l}});var n=r(39129),o={accessibilityLayer:!0,barCategoryGap:"10%",barGap:4,barSize:void 0,className:void 0,maxBarSize:void 0,stackOffset:"none",syncId:void 0,syncMethod:"index"},i=(0,n.oM)({name:"rootProps",initialState:o,reducers:{updateOptions:(e,t)=>{var r;e.accessibilityLayer=t.payload.accessibilityLayer,e.barCategoryGap=t.payload.barCategoryGap,e.barGap=null!==(r=t.payload.barGap)&&void 0!==r?r:o.barGap,e.barSize=t.payload.barSize,e.maxBarSize=t.payload.maxBarSize,e.stackOffset=t.payload.stackOffset,e.syncId=t.payload.syncId,e.syncMethod=t.payload.syncMethod,e.className=t.payload.className}}}),a=i.reducer,{updateOptions:l}=i.actions},98628:function(e,t,r){"use strict";r.d(t,{VQ:function(){return oo},UA:function(){return n4},yT:function(){return ow},l_:function(){return oC},kO:function(){return oR},UC:function(){return it},tZ:function(){return n5},dz:function(){return or},zX:function(){return og},rC:function(){return o8},bU:function(){return n2},$B:function(){return nQ},fY:function(){return oO},vb:function(){return o_},E8:function(){return oE},Yf:function(){return oT},Jw:function(){return oN},CK:function(){return oe},Tk:function(){return n0},zW:function(){return of},c4:function(){return oc},dW:function(){return nF},RN:function(){return nK},YZ:function(){return nq},Eu:function(){return os},Lg:function(){return io},Fn:function(){return oZ},Vm:function(){return o$},ub:function(){return nH},AS:function(){return il},fW:function(){return n$},Lu:function(){return o7},Xv:function(){return ic},KB:function(){return oP},Q4:function(){return nY},cV:function(){return oD},ww:function(){return oh},RA:function(){return od},xz:function(){return oy},g6:function(){return ot},ox:function(){return ii},bY:function(){return ia},bm:function(){return nX},rs:function(){return o3},i9:function(){return nB},Oy:function(){return o0},lU:function(){return o4},t:function(){return nV},ON:function(){return o9}});var n,o,i,a,l,u,c,s={};r.r(s),r.d(s,{scaleBand:function(){return x},scaleDiverging:function(){return function e(){var t=eU(r4()(eM));return t.copy=function(){return r6(t,e())},v.apply(t,arguments)}},scaleDivergingLog:function(){return function e(){var t=eJ(r4()).domain([.1,1,10]);return t.copy=function(){return r6(t,e()).base(t.base())},v.apply(t,arguments)}},scaleDivergingPow:function(){return r9},scaleDivergingSqrt:function(){return r7},scaleDivergingSymlog:function(){return function e(){var t=e2(r4());return t.copy=function(){return r6(t,e()).constant(t.constant())},v.apply(t,arguments)}},scaleIdentity:function(){return function e(t){var r;function n(e){return null==e||isNaN(e=+e)?r:e}return n.invert=n,n.domain=n.range=function(e){return arguments.length?(t=Array.from(e,eP),n):t.slice()},n.unknown=function(e){return arguments.length?(r=e,n):r},n.copy=function(){return e(t).unknown(r)},t=arguments.length?Array.from(t,eP):[0,1],eU(n)}},scaleImplicit:function(){return b},scaleLinear:function(){return function e(){var t=eN();return t.copy=function(){return eT(t,e())},h.apply(t,arguments),eU(t)}},scaleLog:function(){return function e(){let t=eJ(eD()).domain([1,10]);return t.copy=()=>eT(t,e()).base(t.base()),h.apply(t,arguments),t}},scaleOrdinal:function(){return w},scalePoint:function(){return O},scalePow:function(){return e9},scaleQuantile:function(){return function e(){var t,r=[],n=[],o=[];function i(){var e=0,t=Math.max(1,n.length);for(o=Array(t-1);++e<t;)o[e-1]=function(e,t,r=_){if(!(!(n=e.length)||isNaN(t=+t))){if(t<=0||n<2)return+r(e[0],0,e);if(t>=1)return+r(e[n-1],n-1,e);var n,o=(n-1)*t,i=Math.floor(o),a=+r(e[i],i,e);return a+(+r(e[i+1],i+1,e)-a)*(o-i)}}(r,e/t);return a}function a(e){return null==e||isNaN(e=+e)?t:n[R(o,e)]}return a.invertExtent=function(e){var t=n.indexOf(e);return t<0?[NaN,NaN]:[t>0?o[t-1]:r[0],t<o.length?o[t]:r[r.length-1]]},a.domain=function(e){if(!arguments.length)return r.slice();for(let t of(r=[],e))null==t||isNaN(t=+t)||r.push(t);return r.sort(A),i()},a.range=function(e){return arguments.length?(n=Array.from(e),i()):n.slice()},a.unknown=function(e){return arguments.length?(t=e,a):t},a.quantiles=function(){return o.slice()},a.copy=function(){return e().domain(r).range(n).unknown(t)},h.apply(a,arguments)}},scaleQuantize:function(){return function e(){var t,r=0,n=1,o=1,i=[.5],a=[0,1];function l(e){return null!=e&&e<=e?a[R(i,e,0,o)]:t}function u(){var e=-1;for(i=Array(o);++e<o;)i[e]=((e+1)*n-(e-o)*r)/(o+1);return l}return l.domain=function(e){return arguments.length?([r,n]=e,r=+r,n=+n,u()):[r,n]},l.range=function(e){return arguments.length?(o=(a=Array.from(e)).length-1,u()):a.slice()},l.invertExtent=function(e){var t=a.indexOf(e);return t<0?[NaN,NaN]:t<1?[r,i[0]]:t>=o?[i[o-1],n]:[i[t-1],i[t]]},l.unknown=function(e){return arguments.length&&(t=e),l},l.thresholds=function(){return i.slice()},l.copy=function(){return e().domain([r,n]).range(a).unknown(t)},h.apply(eU(l),arguments)}},scaleRadial:function(){return function e(){var t,r=eN(),n=[0,1],o=!1;function i(e){var n,i=Math.sign(n=r(e))*Math.sqrt(Math.abs(n));return isNaN(i)?t:o?Math.round(i):i}return i.invert=function(e){return r.invert(e8(e))},i.domain=function(e){return arguments.length?(r.domain(e),i):r.domain()},i.range=function(e){return arguments.length?(r.range((n=Array.from(e,eP)).map(e8)),i):n.slice()},i.rangeRound=function(e){return i.range(e).round(!0)},i.round=function(e){return arguments.length?(o=!!e,i):o},i.clamp=function(e){return arguments.length?(r.clamp(e),i):r.clamp()},i.unknown=function(e){return arguments.length?(t=e,i):t},i.copy=function(){return e(r.domain(),n).round(o).clamp(r.clamp()).unknown(t)},h.apply(i,arguments),eU(i)}},scaleSequential:function(){return function e(){var t=eU(r2()(eM));return t.copy=function(){return r6(t,e())},v.apply(t,arguments)}},scaleSequentialLog:function(){return function e(){var t=eJ(r2()).domain([1,10]);return t.copy=function(){return r6(t,e()).base(t.base())},v.apply(t,arguments)}},scaleSequentialPow:function(){return r5},scaleSequentialQuantile:function(){return function e(){var t=[],r=eM;function n(e){if(null!=e&&!isNaN(e=+e))return r((R(t,e,1)-1)/(t.length-1))}return n.domain=function(e){if(!arguments.length)return t.slice();for(let r of(t=[],e))null==r||isNaN(r=+r)||t.push(r);return t.sort(A),n},n.interpolator=function(e){return arguments.length?(r=e,n):r},n.range=function(){return t.map((e,n)=>r(n/(t.length-1)))},n.quantiles=function(e){return Array.from({length:e+1},(r,n)=>(function(e,t,r){if(!(!(n=(e=Float64Array.from(function*(e,t){if(void 0===t)for(let t of e)null!=t&&(t=+t)>=t&&(yield t);else{let r=-1;for(let n of e)null!=(n=t(n,++r,e))&&(n=+n)>=n&&(yield n)}}(e,void 0))).length)||isNaN(t=+t))){if(t<=0||n<2)return tt(e);if(t>=1)return te(e);var n,o=(n-1)*t,i=Math.floor(o),a=te((function e(t,r,n=0,o=1/0,i){if(r=Math.floor(r),n=Math.floor(Math.max(0,n)),o=Math.floor(Math.min(t.length-1,o)),!(n<=r&&r<=o))return t;for(i=void 0===i?tr:function(e=A){if(e===A)return tr;if("function"!=typeof e)throw TypeError("compare is not a function");return(t,r)=>{let n=e(t,r);return n||0===n?n:(0===e(r,r))-(0===e(t,t))}}(i);o>n;){if(o-n>600){let a=o-n+1,l=r-n+1,u=Math.log(a),c=.5*Math.exp(2*u/3),s=.5*Math.sqrt(u*c*(a-c)/a)*(l-a/2<0?-1:1),d=Math.max(n,Math.floor(r-l*c/a+s)),f=Math.min(o,Math.floor(r+(a-l)*c/a+s));e(t,r,d,f,i)}let a=t[r],l=n,u=o;for(tn(t,n,r),i(t[o],a)>0&&tn(t,n,o);l<u;){for(tn(t,l,u),++l,--u;0>i(t[l],a);)++l;for(;i(t[u],a)>0;)--u}0===i(t[n],a)?tn(t,n,u):tn(t,++u,o),u<=r&&(n=u+1),r<=u&&(o=u-1)}return t})(e,i).subarray(0,i+1));return a+(tt(e.subarray(i+1))-a)*(o-i)}})(t,n/e))},n.copy=function(){return e(r).domain(t)},v.apply(n,arguments)}},scaleSequentialSqrt:function(){return r3},scaleSequentialSymlog:function(){return function e(){var t=e2(r2());return t.copy=function(){return r6(t,e()).constant(t.constant())},v.apply(t,arguments)}},scaleSqrt:function(){return e7},scaleSymlog:function(){return function e(){var t=e2(eD());return t.copy=function(){return eT(t,e()).constant(t.constant())},h.apply(t,arguments)}},scaleThreshold:function(){return function e(){var t,r=[.5],n=[0,1],o=1;function i(e){return null!=e&&e<=e?n[R(r,e,0,o)]:t}return i.domain=function(e){return arguments.length?(o=Math.min((r=Array.from(e)).length,n.length-1),i):r.slice()},i.range=function(e){return arguments.length?(n=Array.from(e),o=Math.min(r.length,n.length-1),i):n.slice()},i.invertExtent=function(e){var t=n.indexOf(e);return[r[t-1],r[t]]},i.unknown=function(e){return arguments.length?(t=e,i):t},i.copy=function(){return e().domain(r).range(n).unknown(t)},h.apply(i,arguments)}},scaleTime:function(){return r0},scaleUtc:function(){return r1},tickFormat:function(){return eZ}});var d=r(92713),f=r(41664),p=r.n(f);function h(e,t){switch(arguments.length){case 0:break;case 1:this.range(e);break;default:this.range(t).domain(e)}return this}function v(e,t){switch(arguments.length){case 0:break;case 1:"function"==typeof e?this.interpolator(e):this.range(e);break;default:this.domain(e),"function"==typeof t?this.interpolator(t):this.range(t)}return this}class y extends Map{constructor(e,t=g){if(super(),Object.defineProperties(this,{_intern:{value:new Map},_key:{value:t}}),null!=e)for(let[t,r]of e)this.set(t,r)}get(e){return super.get(m(this,e))}has(e){return super.has(m(this,e))}set(e,t){return super.set(function({_intern:e,_key:t},r){let n=t(r);return e.has(n)?e.get(n):(e.set(n,r),r)}(this,e),t)}delete(e){return super.delete(function({_intern:e,_key:t},r){let n=t(r);return e.has(n)&&(r=e.get(n),e.delete(n)),r}(this,e))}}function m({_intern:e,_key:t},r){let n=t(r);return e.has(n)?e.get(n):r}function g(e){return null!==e&&"object"==typeof e?e.valueOf():e}let b=Symbol("implicit");function w(){var e=new y,t=[],r=[],n=b;function o(o){let i=e.get(o);if(void 0===i){if(n!==b)return n;e.set(o,i=t.push(o)-1)}return r[i%r.length]}return o.domain=function(r){if(!arguments.length)return t.slice();for(let n of(t=[],e=new y,r))e.has(n)||e.set(n,t.push(n)-1);return o},o.range=function(e){return arguments.length?(r=Array.from(e),o):r.slice()},o.unknown=function(e){return arguments.length?(n=e,o):n},o.copy=function(){return w(t,r).unknown(n)},h.apply(o,arguments),o}function x(){var e,t,r=w().unknown(void 0),n=r.domain,o=r.range,i=0,a=1,l=!1,u=0,c=0,s=.5;function d(){var r=n().length,d=a<i,f=d?a:i,p=d?i:a;e=(p-f)/Math.max(1,r-u+2*c),l&&(e=Math.floor(e)),f+=(p-f-e*(r-u))*s,t=e*(1-u),l&&(f=Math.round(f),t=Math.round(t));var h=(function(e,t,r){e=+e,t=+t,r=(o=arguments.length)<2?(t=e,e=0,1):o<3?1:+r;for(var n=-1,o=0|Math.max(0,Math.ceil((t-e)/r)),i=Array(o);++n<o;)i[n]=e+n*r;return i})(r).map(function(t){return f+e*t});return o(d?h.reverse():h)}return delete r.unknown,r.domain=function(e){return arguments.length?(n(e),d()):n()},r.range=function(e){return arguments.length?([i,a]=e,i=+i,a=+a,d()):[i,a]},r.rangeRound=function(e){return[i,a]=e,i=+i,a=+a,l=!0,d()},r.bandwidth=function(){return t},r.step=function(){return e},r.round=function(e){return arguments.length?(l=!!e,d()):l},r.padding=function(e){return arguments.length?(u=Math.min(1,c=+e),d()):u},r.paddingInner=function(e){return arguments.length?(u=Math.min(1,e),d()):u},r.paddingOuter=function(e){return arguments.length?(c=+e,d()):c},r.align=function(e){return arguments.length?(s=Math.max(0,Math.min(1,e)),d()):s},r.copy=function(){return x(n(),[i,a]).round(l).paddingInner(u).paddingOuter(c).align(s)},h.apply(d(),arguments)}function O(){return function e(t){var r=t.copy;return t.padding=t.paddingOuter,delete t.paddingInner,delete t.paddingOuter,t.copy=function(){return e(r())},t}(x.apply(null,arguments).paddingInner(1))}let j=Math.sqrt(50),k=Math.sqrt(10),P=Math.sqrt(2);function E(e,t,r){let n,o,i;let a=(t-e)/Math.max(0,r),l=Math.floor(Math.log10(a)),u=a/Math.pow(10,l),c=u>=j?10:u>=k?5:u>=P?2:1;return(l<0?(n=Math.round(e*(i=Math.pow(10,-l)/c)),o=Math.round(t*i),n/i<e&&++n,o/i>t&&--o,i=-i):(n=Math.round(e/(i=Math.pow(10,l)*c)),o=Math.round(t/i),n*i<e&&++n,o*i>t&&--o),o<n&&.5<=r&&r<2)?E(e,t,2*r):[n,o,i]}function M(e,t,r){if(t=+t,e=+e,!((r=+r)>0))return[];if(e===t)return[e];let n=t<e,[o,i,a]=n?E(t,e,r):E(e,t,r);if(!(i>=o))return[];let l=i-o+1,u=Array(l);if(n){if(a<0)for(let e=0;e<l;++e)u[e]=-((i-e)/a);else for(let e=0;e<l;++e)u[e]=(i-e)*a}else if(a<0)for(let e=0;e<l;++e)u[e]=-((o+e)/a);else for(let e=0;e<l;++e)u[e]=(o+e)*a;return u}function S(e,t,r){return E(e=+e,t=+t,r=+r)[2]}function C(e,t,r){t=+t,e=+e,r=+r;let n=t<e,o=n?S(t,e,r):S(e,t,r);return(n?-1:1)*(o<0?-(1/o):o)}function A(e,t){return null==e||null==t?NaN:e<t?-1:e>t?1:e>=t?0:NaN}function T(e,t){return null==e||null==t?NaN:t<e?-1:t>e?1:t>=e?0:NaN}function D(e){let t,r,n;function o(e,n,o=0,i=e.length){if(o<i){if(0!==t(n,n))return i;do{let t=o+i>>>1;0>r(e[t],n)?o=t+1:i=t}while(o<i)}return o}return 2!==e.length?(t=A,r=(t,r)=>A(e(t),r),n=(t,r)=>e(t)-r):(t=e===A||e===T?e:N,r=e,n=e),{left:o,center:function(e,t,r=0,i=e.length){let a=o(e,t,r,i-1);return a>r&&n(e[a-1],t)>-n(e[a],t)?a-1:a},right:function(e,n,o=0,i=e.length){if(o<i){if(0!==t(n,n))return i;do{let t=o+i>>>1;0>=r(e[t],n)?o=t+1:i=t}while(o<i)}return o}}}function N(){return 0}function _(e){return null===e?NaN:+e}let I=D(A),R=I.right;function L(e,t,r){e.prototype=t.prototype=r,r.constructor=e}function z(e,t){var r=Object.create(e.prototype);for(var n in t)r[n]=t[n];return r}function W(){}I.left,D(_).center;var F="\\s*([+-]?\\d+)\\s*",B="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)\\s*",K="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)%\\s*",V=/^#([0-9a-f]{3,8})$/,Z=RegExp(`^rgb\\(${F},${F},${F}\\)$`),U=RegExp(`^rgb\\(${K},${K},${K}\\)$`),$=RegExp(`^rgba\\(${F},${F},${F},${B}\\)$`),H=RegExp(`^rgba\\(${K},${K},${K},${B}\\)$`),Y=RegExp(`^hsl\\(${B},${K},${K}\\)$`),q=RegExp(`^hsla\\(${B},${K},${K},${B}\\)$`),X={aliceblue:15792383,antiquewhite:16444375,aqua:65535,aquamarine:8388564,azure:15794175,beige:16119260,bisque:16770244,black:0,blanchedalmond:16772045,blue:255,blueviolet:9055202,brown:10824234,burlywood:14596231,cadetblue:6266528,chartreuse:8388352,chocolate:13789470,coral:16744272,cornflowerblue:6591981,cornsilk:16775388,crimson:14423100,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:12092939,darkgray:11119017,darkgreen:25600,darkgrey:11119017,darkkhaki:12433259,darkmagenta:9109643,darkolivegreen:5597999,darkorange:16747520,darkorchid:10040012,darkred:9109504,darksalmon:15308410,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:16716947,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:11674146,floralwhite:16775920,forestgreen:2263842,fuchsia:16711935,gainsboro:14474460,ghostwhite:16316671,gold:16766720,goldenrod:14329120,gray:8421504,green:32768,greenyellow:11403055,grey:8421504,honeydew:15794160,hotpink:16738740,indianred:13458524,indigo:4915330,ivory:16777200,khaki:15787660,lavender:15132410,lavenderblush:16773365,lawngreen:8190976,lemonchiffon:16775885,lightblue:11393254,lightcoral:15761536,lightcyan:14745599,lightgoldenrodyellow:16448210,lightgray:13882323,lightgreen:9498256,lightgrey:13882323,lightpink:16758465,lightsalmon:16752762,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:11584734,lightyellow:16777184,lime:65280,limegreen:3329330,linen:16445670,magenta:16711935,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:12211667,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:13047173,midnightblue:1644912,mintcream:16121850,mistyrose:16770273,moccasin:16770229,navajowhite:16768685,navy:128,oldlace:16643558,olive:8421376,olivedrab:7048739,orange:16753920,orangered:16729344,orchid:14315734,palegoldenrod:15657130,palegreen:10025880,paleturquoise:11529966,palevioletred:14381203,papayawhip:16773077,peachpuff:16767673,peru:13468991,pink:16761035,plum:14524637,powderblue:11591910,purple:8388736,rebeccapurple:6697881,red:16711680,rosybrown:12357519,royalblue:4286945,saddlebrown:9127187,salmon:16416882,sandybrown:16032864,seagreen:3050327,seashell:16774638,sienna:10506797,silver:12632256,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:16775930,springgreen:65407,steelblue:4620980,tan:13808780,teal:32896,thistle:14204888,tomato:16737095,turquoise:4251856,violet:15631086,wheat:16113331,white:16777215,whitesmoke:16119285,yellow:16776960,yellowgreen:10145074};function G(){return this.rgb().formatHex()}function Q(){return this.rgb().formatRgb()}function J(e){var t,r;return e=(e+"").trim().toLowerCase(),(t=V.exec(e))?(r=t[1].length,t=parseInt(t[1],16),6===r?ee(t):3===r?new en(t>>8&15|t>>4&240,t>>4&15|240&t,(15&t)<<4|15&t,1):8===r?et(t>>24&255,t>>16&255,t>>8&255,(255&t)/255):4===r?et(t>>12&15|t>>8&240,t>>8&15|t>>4&240,t>>4&15|240&t,((15&t)<<4|15&t)/255):null):(t=Z.exec(e))?new en(t[1],t[2],t[3],1):(t=U.exec(e))?new en(255*t[1]/100,255*t[2]/100,255*t[3]/100,1):(t=$.exec(e))?et(t[1],t[2],t[3],t[4]):(t=H.exec(e))?et(255*t[1]/100,255*t[2]/100,255*t[3]/100,t[4]):(t=Y.exec(e))?ec(t[1],t[2]/100,t[3]/100,1):(t=q.exec(e))?ec(t[1],t[2]/100,t[3]/100,t[4]):X.hasOwnProperty(e)?ee(X[e]):"transparent"===e?new en(NaN,NaN,NaN,0):null}function ee(e){return new en(e>>16&255,e>>8&255,255&e,1)}function et(e,t,r,n){return n<=0&&(e=t=r=NaN),new en(e,t,r,n)}function er(e,t,r,n){var o;return 1==arguments.length?((o=e)instanceof W||(o=J(o)),o)?new en((o=o.rgb()).r,o.g,o.b,o.opacity):new en:new en(e,t,r,null==n?1:n)}function en(e,t,r,n){this.r=+e,this.g=+t,this.b=+r,this.opacity=+n}function eo(){return`#${eu(this.r)}${eu(this.g)}${eu(this.b)}`}function ei(){let e=ea(this.opacity);return`${1===e?"rgb(":"rgba("}${el(this.r)}, ${el(this.g)}, ${el(this.b)}${1===e?")":`, ${e})`}`}function ea(e){return isNaN(e)?1:Math.max(0,Math.min(1,e))}function el(e){return Math.max(0,Math.min(255,Math.round(e)||0))}function eu(e){return((e=el(e))<16?"0":"")+e.toString(16)}function ec(e,t,r,n){return n<=0?e=t=r=NaN:r<=0||r>=1?e=t=NaN:t<=0&&(e=NaN),new ed(e,t,r,n)}function es(e){if(e instanceof ed)return new ed(e.h,e.s,e.l,e.opacity);if(e instanceof W||(e=J(e)),!e)return new ed;if(e instanceof ed)return e;var t=(e=e.rgb()).r/255,r=e.g/255,n=e.b/255,o=Math.min(t,r,n),i=Math.max(t,r,n),a=NaN,l=i-o,u=(i+o)/2;return l?(a=t===i?(r-n)/l+(r<n)*6:r===i?(n-t)/l+2:(t-r)/l+4,l/=u<.5?i+o:2-i-o,a*=60):l=u>0&&u<1?0:a,new ed(a,l,u,e.opacity)}function ed(e,t,r,n){this.h=+e,this.s=+t,this.l=+r,this.opacity=+n}function ef(e){return(e=(e||0)%360)<0?e+360:e}function ep(e){return Math.max(0,Math.min(1,e||0))}function eh(e,t,r){return(e<60?t+(r-t)*e/60:e<180?r:e<240?t+(r-t)*(240-e)/60:t)*255}function ev(e,t,r,n,o){var i=e*e,a=i*e;return((1-3*e+3*i-a)*t+(4-6*i+3*a)*r+(1+3*e+3*i-3*a)*n+a*o)/6}L(W,J,{copy(e){return Object.assign(new this.constructor,this,e)},displayable(){return this.rgb().displayable()},hex:G,formatHex:G,formatHex8:function(){return this.rgb().formatHex8()},formatHsl:function(){return es(this).formatHsl()},formatRgb:Q,toString:Q}),L(en,er,z(W,{brighter(e){return e=null==e?1.4285714285714286:Math.pow(1.4285714285714286,e),new en(this.r*e,this.g*e,this.b*e,this.opacity)},darker(e){return e=null==e?.7:Math.pow(.7,e),new en(this.r*e,this.g*e,this.b*e,this.opacity)},rgb(){return this},clamp(){return new en(el(this.r),el(this.g),el(this.b),ea(this.opacity))},displayable(){return -.5<=this.r&&this.r<255.5&&-.5<=this.g&&this.g<255.5&&-.5<=this.b&&this.b<255.5&&0<=this.opacity&&this.opacity<=1},hex:eo,formatHex:eo,formatHex8:function(){return`#${eu(this.r)}${eu(this.g)}${eu(this.b)}${eu((isNaN(this.opacity)?1:this.opacity)*255)}`},formatRgb:ei,toString:ei})),L(ed,function(e,t,r,n){return 1==arguments.length?es(e):new ed(e,t,r,null==n?1:n)},z(W,{brighter(e){return e=null==e?1.4285714285714286:Math.pow(1.4285714285714286,e),new ed(this.h,this.s,this.l*e,this.opacity)},darker(e){return e=null==e?.7:Math.pow(.7,e),new ed(this.h,this.s,this.l*e,this.opacity)},rgb(){var e=this.h%360+(this.h<0)*360,t=isNaN(e)||isNaN(this.s)?0:this.s,r=this.l,n=r+(r<.5?r:1-r)*t,o=2*r-n;return new en(eh(e>=240?e-240:e+120,o,n),eh(e,o,n),eh(e<120?e+240:e-120,o,n),this.opacity)},clamp(){return new ed(ef(this.h),ep(this.s),ep(this.l),ea(this.opacity))},displayable(){return(0<=this.s&&this.s<=1||isNaN(this.s))&&0<=this.l&&this.l<=1&&0<=this.opacity&&this.opacity<=1},formatHsl(){let e=ea(this.opacity);return`${1===e?"hsl(":"hsla("}${ef(this.h)}, ${100*ep(this.s)}%, ${100*ep(this.l)}%${1===e?")":`, ${e})`}`}}));var ey=e=>()=>e;function em(e,t){var r=t-e;return r?function(t){return e+t*r}:ey(isNaN(e)?t:e)}var eg=function e(t){var r,n=1==(r=+(r=t))?em:function(e,t){var n,o,i;return t-e?(n=e,o=t,n=Math.pow(n,i=r),o=Math.pow(o,i)-n,i=1/i,function(e){return Math.pow(n+e*o,i)}):ey(isNaN(e)?t:e)};function o(e,t){var r=n((e=er(e)).r,(t=er(t)).r),o=n(e.g,t.g),i=n(e.b,t.b),a=em(e.opacity,t.opacity);return function(t){return e.r=r(t),e.g=o(t),e.b=i(t),e.opacity=a(t),e+""}}return o.gamma=e,o}(1);function eb(e){return function(t){var r,n,o=t.length,i=Array(o),a=Array(o),l=Array(o);for(r=0;r<o;++r)n=er(t[r]),i[r]=n.r||0,a[r]=n.g||0,l[r]=n.b||0;return i=e(i),a=e(a),l=e(l),n.opacity=1,function(e){return n.r=i(e),n.g=a(e),n.b=l(e),n+""}}}function ew(e,t){return e=+e,t=+t,function(r){return e*(1-r)+t*r}}eb(function(e){var t=e.length-1;return function(r){var n=r<=0?r=0:r>=1?(r=1,t-1):Math.floor(r*t),o=e[n],i=e[n+1],a=n>0?e[n-1]:2*o-i,l=n<t-1?e[n+2]:2*i-o;return ev((r-n/t)*t,a,o,i,l)}}),eb(function(e){var t=e.length;return function(r){var n=Math.floor(((r%=1)<0?++r:r)*t),o=e[(n+t-1)%t],i=e[n%t],a=e[(n+1)%t],l=e[(n+2)%t];return ev((r-n/t)*t,o,i,a,l)}});var ex=/[-+]?(?:\d+\.?\d*|\.?\d+)(?:[eE][-+]?\d+)?/g,eO=RegExp(ex.source,"g");function ej(e,t){var r,n,o=typeof t;return null==t||"boolean"===o?ey(t):("number"===o?ew:"string"===o?(n=J(t))?(t=n,eg):function(e,t){var r,n,o,i,a,l=ex.lastIndex=eO.lastIndex=0,u=-1,c=[],s=[];for(e+="",t+="";(o=ex.exec(e))&&(i=eO.exec(t));)(a=i.index)>l&&(a=t.slice(l,a),c[u]?c[u]+=a:c[++u]=a),(o=o[0])===(i=i[0])?c[u]?c[u]+=i:c[++u]=i:(c[++u]=null,s.push({i:u,x:ew(o,i)})),l=eO.lastIndex;return l<t.length&&(a=t.slice(l),c[u]?c[u]+=a:c[++u]=a),c.length<2?s[0]?(r=s[0].x,function(e){return r(e)+""}):(n=t,function(){return n}):(t=s.length,function(e){for(var r,n=0;n<t;++n)c[(r=s[n]).i]=r.x(e);return c.join("")})}:t instanceof J?eg:t instanceof Date?function(e,t){var r=new Date;return e=+e,t=+t,function(n){return r.setTime(e*(1-n)+t*n),r}}:!ArrayBuffer.isView(r=t)||r instanceof DataView?Array.isArray(t)?function(e,t){var r,n=t?t.length:0,o=e?Math.min(n,e.length):0,i=Array(o),a=Array(n);for(r=0;r<o;++r)i[r]=ej(e[r],t[r]);for(;r<n;++r)a[r]=t[r];return function(e){for(r=0;r<o;++r)a[r]=i[r](e);return a}}:"function"!=typeof t.valueOf&&"function"!=typeof t.toString||isNaN(t)?function(e,t){var r,n={},o={};for(r in(null===e||"object"!=typeof e)&&(e={}),(null===t||"object"!=typeof t)&&(t={}),t)r in e?n[r]=ej(e[r],t[r]):o[r]=t[r];return function(e){for(r in n)o[r]=n[r](e);return o}}:ew:function(e,t){t||(t=[]);var r,n=e?Math.min(t.length,e.length):0,o=t.slice();return function(i){for(r=0;r<n;++r)o[r]=e[r]*(1-i)+t[r]*i;return o}})(e,t)}function ek(e,t){return e=+e,t=+t,function(r){return Math.round(e*(1-r)+t*r)}}function eP(e){return+e}var eE=[0,1];function eM(e){return e}function eS(e,t){var r;return(t-=e=+e)?function(r){return(r-e)/t}:(r=isNaN(t)?NaN:.5,function(){return r})}function eC(e,t,r){var n=e[0],o=e[1],i=t[0],a=t[1];return o<n?(n=eS(o,n),i=r(a,i)):(n=eS(n,o),i=r(i,a)),function(e){return i(n(e))}}function eA(e,t,r){var n=Math.min(e.length,t.length)-1,o=Array(n),i=Array(n),a=-1;for(e[n]<e[0]&&(e=e.slice().reverse(),t=t.slice().reverse());++a<n;)o[a]=eS(e[a],e[a+1]),i[a]=r(t[a],t[a+1]);return function(t){var r=R(e,t,1,n)-1;return i[r](o[r](t))}}function eT(e,t){return t.domain(e.domain()).range(e.range()).interpolate(e.interpolate()).clamp(e.clamp()).unknown(e.unknown())}function eD(){var e,t,r,n,o,i,a=eE,l=eE,u=ej,c=eM;function s(){var e,t,r,u=Math.min(a.length,l.length);return c!==eM&&(e=a[0],t=a[u-1],e>t&&(r=e,e=t,t=r),c=function(r){return Math.max(e,Math.min(t,r))}),n=u>2?eA:eC,o=i=null,d}function d(t){return null==t||isNaN(t=+t)?r:(o||(o=n(a.map(e),l,u)))(e(c(t)))}return d.invert=function(r){return c(t((i||(i=n(l,a.map(e),ew)))(r)))},d.domain=function(e){return arguments.length?(a=Array.from(e,eP),s()):a.slice()},d.range=function(e){return arguments.length?(l=Array.from(e),s()):l.slice()},d.rangeRound=function(e){return l=Array.from(e),u=ek,s()},d.clamp=function(e){return arguments.length?(c=!!e||eM,s()):c!==eM},d.interpolate=function(e){return arguments.length?(u=e,s()):u},d.unknown=function(e){return arguments.length?(r=e,d):r},function(r,n){return e=r,t=n,s()}}function eN(){return eD()(eM,eM)}var e_=/^(?:(.)?([<>=^]))?([+\-( ])?([$#])?(0)?(\d+)?(,)?(\.\d+)?(~)?([a-z%])?$/i;function eI(e){var t;if(!(t=e_.exec(e)))throw Error("invalid format: "+e);return new eR({fill:t[1],align:t[2],sign:t[3],symbol:t[4],zero:t[5],width:t[6],comma:t[7],precision:t[8]&&t[8].slice(1),trim:t[9],type:t[10]})}function eR(e){this.fill=void 0===e.fill?" ":e.fill+"",this.align=void 0===e.align?">":e.align+"",this.sign=void 0===e.sign?"-":e.sign+"",this.symbol=void 0===e.symbol?"":e.symbol+"",this.zero=!!e.zero,this.width=void 0===e.width?void 0:+e.width,this.comma=!!e.comma,this.precision=void 0===e.precision?void 0:+e.precision,this.trim=!!e.trim,this.type=void 0===e.type?"":e.type+""}function eL(e,t){if((r=(e=t?e.toExponential(t-1):e.toExponential()).indexOf("e"))<0)return null;var r,n=e.slice(0,r);return[n.length>1?n[0]+n.slice(2):n,+e.slice(r+1)]}function ez(e){return(e=eL(Math.abs(e)))?e[1]:NaN}function eW(e,t){var r=eL(e,t);if(!r)return e+"";var n=r[0],o=r[1];return o<0?"0."+Array(-o).join("0")+n:n.length>o+1?n.slice(0,o+1)+"."+n.slice(o+1):n+Array(o-n.length+2).join("0")}eI.prototype=eR.prototype,eR.prototype.toString=function(){return this.fill+this.align+this.sign+this.symbol+(this.zero?"0":"")+(void 0===this.width?"":Math.max(1,0|this.width))+(this.comma?",":"")+(void 0===this.precision?"":"."+Math.max(0,0|this.precision))+(this.trim?"~":"")+this.type};var eF={"%":(e,t)=>(100*e).toFixed(t),b:e=>Math.round(e).toString(2),c:e=>e+"",d:function(e){return Math.abs(e=Math.round(e))>=1e21?e.toLocaleString("en").replace(/,/g,""):e.toString(10)},e:(e,t)=>e.toExponential(t),f:(e,t)=>e.toFixed(t),g:(e,t)=>e.toPrecision(t),o:e=>Math.round(e).toString(8),p:(e,t)=>eW(100*e,t),r:eW,s:function(e,t){var r=eL(e,t);if(!r)return e+"";var o=r[0],i=r[1],a=i-(n=3*Math.max(-8,Math.min(8,Math.floor(i/3))))+1,l=o.length;return a===l?o:a>l?o+Array(a-l+1).join("0"):a>0?o.slice(0,a)+"."+o.slice(a):"0."+Array(1-a).join("0")+eL(e,Math.max(0,t+a-1))[0]},X:e=>Math.round(e).toString(16).toUpperCase(),x:e=>Math.round(e).toString(16)};function eB(e){return e}var eK=Array.prototype.map,eV=["y","z","a","f","p","n","\xb5","m","","k","M","G","T","P","E","Z","Y"];function eZ(e,t,r,n){var o,l,u=C(e,t,r);switch((n=eI(null==n?",f":n)).type){case"s":var c=Math.max(Math.abs(e),Math.abs(t));return null!=n.precision||isNaN(l=Math.max(0,3*Math.max(-8,Math.min(8,Math.floor(ez(c)/3)))-ez(Math.abs(u))))||(n.precision=l),a(n,c);case"":case"e":case"g":case"p":case"r":null!=n.precision||isNaN(l=Math.max(0,ez(Math.abs(Math.max(Math.abs(e),Math.abs(t)))-(o=Math.abs(o=u)))-ez(o))+1)||(n.precision=l-("e"===n.type));break;case"f":case"%":null!=n.precision||isNaN(l=Math.max(0,-ez(Math.abs(u))))||(n.precision=l-("%"===n.type)*2)}return i(n)}function eU(e){var t=e.domain;return e.ticks=function(e){var r=t();return M(r[0],r[r.length-1],null==e?10:e)},e.tickFormat=function(e,r){var n=t();return eZ(n[0],n[n.length-1],null==e?10:e,r)},e.nice=function(r){null==r&&(r=10);var n,o,i=t(),a=0,l=i.length-1,u=i[a],c=i[l],s=10;for(c<u&&(o=u,u=c,c=o,o=a,a=l,l=o);s-- >0;){if((o=S(u,c,r))===n)return i[a]=u,i[l]=c,t(i);if(o>0)u=Math.floor(u/o)*o,c=Math.ceil(c/o)*o;else if(o<0)u=Math.ceil(u*o)/o,c=Math.floor(c*o)/o;else break;n=o}return e},e}function e$(e,t){e=e.slice();var r,n=0,o=e.length-1,i=e[n],a=e[o];return a<i&&(r=n,n=o,o=r,r=i,i=a,a=r),e[n]=t.floor(i),e[o]=t.ceil(a),e}function eH(e){return Math.log(e)}function eY(e){return Math.exp(e)}function eq(e){return-Math.log(-e)}function eX(e){return-Math.exp(-e)}function eG(e){return isFinite(e)?+("1e"+e):e<0?0:e}function eQ(e){return(t,r)=>-e(-t,r)}function eJ(e){let t,r;let n=e(eH,eY),o=n.domain,a=10;function l(){var i,l;return t=(i=a)===Math.E?Math.log:10===i&&Math.log10||2===i&&Math.log2||(i=Math.log(i),e=>Math.log(e)/i),r=10===(l=a)?eG:l===Math.E?Math.exp:e=>Math.pow(l,e),o()[0]<0?(t=eQ(t),r=eQ(r),e(eq,eX)):e(eH,eY),n}return n.base=function(e){return arguments.length?(a=+e,l()):a},n.domain=function(e){return arguments.length?(o(e),l()):o()},n.ticks=e=>{let n,i;let l=o(),u=l[0],c=l[l.length-1],s=c<u;s&&([u,c]=[c,u]);let d=t(u),f=t(c),p=null==e?10:+e,h=[];if(!(a%1)&&f-d<p){if(d=Math.floor(d),f=Math.ceil(f),u>0){for(;d<=f;++d)for(n=1;n<a;++n)if(!((i=d<0?n/r(-d):n*r(d))<u)){if(i>c)break;h.push(i)}}else for(;d<=f;++d)for(n=a-1;n>=1;--n)if(!((i=d>0?n/r(-d):n*r(d))<u)){if(i>c)break;h.push(i)}2*h.length<p&&(h=M(u,c,p))}else h=M(d,f,Math.min(f-d,p)).map(r);return s?h.reverse():h},n.tickFormat=(e,o)=>{if(null==e&&(e=10),null==o&&(o=10===a?"s":","),"function"!=typeof o&&(a%1||null!=(o=eI(o)).precision||(o.trim=!0),o=i(o)),e===1/0)return o;let l=Math.max(1,a*e/n.ticks().length);return e=>{let n=e/r(Math.round(t(e)));return n*a<a-.5&&(n*=a),n<=l?o(e):""}},n.nice=()=>o(e$(o(),{floor:e=>r(Math.floor(t(e))),ceil:e=>r(Math.ceil(t(e)))})),n}function e0(e){return function(t){return Math.sign(t)*Math.log1p(Math.abs(t/e))}}function e1(e){return function(t){return Math.sign(t)*Math.expm1(Math.abs(t))*e}}function e2(e){var t=1,r=e(e0(1),e1(t));return r.constant=function(r){return arguments.length?e(e0(t=+r),e1(t)):t},eU(r)}function e6(e){return function(t){return t<0?-Math.pow(-t,e):Math.pow(t,e)}}function e5(e){return e<0?-Math.sqrt(-e):Math.sqrt(e)}function e3(e){return e<0?-e*e:e*e}function e4(e){var t=e(eM,eM),r=1;return t.exponent=function(t){return arguments.length?1==(r=+t)?e(eM,eM):.5===r?e(e5,e3):e(e6(r),e6(1/r)):r},eU(t)}function e9(){var e=e4(eD());return e.copy=function(){return eT(e,e9()).exponent(e.exponent())},h.apply(e,arguments),e}function e7(){return e9.apply(null,arguments).exponent(.5)}function e8(e){return Math.sign(e)*e*e}function te(e,t){let r;if(void 0===t)for(let t of e)null!=t&&(r<t||void 0===r&&t>=t)&&(r=t);else{let n=-1;for(let o of e)null!=(o=t(o,++n,e))&&(r<o||void 0===r&&o>=o)&&(r=o)}return r}function tt(e,t){let r;if(void 0===t)for(let t of e)null!=t&&(r>t||void 0===r&&t>=t)&&(r=t);else{let n=-1;for(let o of e)null!=(o=t(o,++n,e))&&(r>o||void 0===r&&o>=o)&&(r=o)}return r}function tr(e,t){return(null==e||!(e>=e))-(null==t||!(t>=t))||(e<t?-1:e>t?1:0)}function tn(e,t,r){let n=e[t];e[t]=e[r],e[r]=n}i=(o=function(e){var t,r,o,i=void 0===e.grouping||void 0===e.thousands?eB:(t=eK.call(e.grouping,Number),r=e.thousands+"",function(e,n){for(var o=e.length,i=[],a=0,l=t[0],u=0;o>0&&l>0&&(u+l+1>n&&(l=Math.max(1,n-u)),i.push(e.substring(o-=l,o+l)),!((u+=l+1)>n));)l=t[a=(a+1)%t.length];return i.reverse().join(r)}),a=void 0===e.currency?"":e.currency[0]+"",l=void 0===e.currency?"":e.currency[1]+"",u=void 0===e.decimal?".":e.decimal+"",c=void 0===e.numerals?eB:(o=eK.call(e.numerals,String),function(e){return e.replace(/[0-9]/g,function(e){return o[+e]})}),s=void 0===e.percent?"%":e.percent+"",d=void 0===e.minus?"−":e.minus+"",f=void 0===e.nan?"NaN":e.nan+"";function p(e){var t=(e=eI(e)).fill,r=e.align,o=e.sign,p=e.symbol,h=e.zero,v=e.width,y=e.comma,m=e.precision,g=e.trim,b=e.type;"n"===b?(y=!0,b="g"):eF[b]||(void 0===m&&(m=12),g=!0,b="g"),(h||"0"===t&&"="===r)&&(h=!0,t="0",r="=");var w="$"===p?a:"#"===p&&/[boxX]/.test(b)?"0"+b.toLowerCase():"",x="$"===p?l:/[%p]/.test(b)?s:"",O=eF[b],j=/[defgprs%]/.test(b);function k(e){var a,l,s,p=w,k=x;if("c"===b)k=O(e)+k,e="";else{var P=(e=+e)<0||1/e<0;if(e=isNaN(e)?f:O(Math.abs(e),m),g&&(e=function(e){e:for(var t,r=e.length,n=1,o=-1;n<r;++n)switch(e[n]){case".":o=t=n;break;case"0":0===o&&(o=n),t=n;break;default:if(!+e[n])break e;o>0&&(o=0)}return o>0?e.slice(0,o)+e.slice(t+1):e}(e)),P&&0==+e&&"+"!==o&&(P=!1),p=(P?"("===o?o:d:"-"===o||"("===o?"":o)+p,k=("s"===b?eV[8+n/3]:"")+k+(P&&"("===o?")":""),j){for(a=-1,l=e.length;++a<l;)if(48>(s=e.charCodeAt(a))||s>57){k=(46===s?u+e.slice(a+1):e.slice(a))+k,e=e.slice(0,a);break}}}y&&!h&&(e=i(e,1/0));var E=p.length+e.length+k.length,M=E<v?Array(v-E+1).join(t):"";switch(y&&h&&(e=i(M+e,M.length?v-k.length:1/0),M=""),r){case"<":e=p+e+k+M;break;case"=":e=p+M+e+k;break;case"^":e=M.slice(0,E=M.length>>1)+p+e+k+M.slice(E);break;default:e=M+p+e+k}return c(e)}return m=void 0===m?6:/[gprs]/.test(b)?Math.max(1,Math.min(21,m)):Math.max(0,Math.min(20,m)),k.toString=function(){return e+""},k}return{format:p,formatPrefix:function(e,t){var r=p(((e=eI(e)).type="f",e)),n=3*Math.max(-8,Math.min(8,Math.floor(ez(t)/3))),o=Math.pow(10,-n),i=eV[8+n/3];return function(e){return r(o*e)+i}}}}({thousands:",",grouping:[3],currency:["$",""]})).format,a=o.formatPrefix;let to=new Date,ti=new Date;function ta(e,t,r,n){function o(t){return e(t=0==arguments.length?new Date:new Date(+t)),t}return o.floor=t=>(e(t=new Date(+t)),t),o.ceil=r=>(e(r=new Date(r-1)),t(r,1),e(r),r),o.round=e=>{let t=o(e),r=o.ceil(e);return e-t<r-e?t:r},o.offset=(e,r)=>(t(e=new Date(+e),null==r?1:Math.floor(r)),e),o.range=(r,n,i)=>{let a;let l=[];if(r=o.ceil(r),i=null==i?1:Math.floor(i),!(r<n)||!(i>0))return l;do l.push(a=new Date(+r)),t(r,i),e(r);while(a<r&&r<n);return l},o.filter=r=>ta(t=>{if(t>=t)for(;e(t),!r(t);)t.setTime(t-1)},(e,n)=>{if(e>=e){if(n<0)for(;++n<=0;)for(;t(e,-1),!r(e););else for(;--n>=0;)for(;t(e,1),!r(e););}}),r&&(o.count=(t,n)=>(to.setTime(+t),ti.setTime(+n),e(to),e(ti),Math.floor(r(to,ti))),o.every=e=>isFinite(e=Math.floor(e))&&e>0?e>1?o.filter(n?t=>n(t)%e==0:t=>o.count(0,t)%e==0):o:null),o}let tl=ta(()=>{},(e,t)=>{e.setTime(+e+t)},(e,t)=>t-e);tl.every=e=>isFinite(e=Math.floor(e))&&e>0?e>1?ta(t=>{t.setTime(Math.floor(t/e)*e)},(t,r)=>{t.setTime(+t+r*e)},(t,r)=>(r-t)/e):tl:null,tl.range;let tu=ta(e=>{e.setTime(e-e.getMilliseconds())},(e,t)=>{e.setTime(+e+1e3*t)},(e,t)=>(t-e)/1e3,e=>e.getUTCSeconds());tu.range;let tc=ta(e=>{e.setTime(e-e.getMilliseconds()-1e3*e.getSeconds())},(e,t)=>{e.setTime(+e+6e4*t)},(e,t)=>(t-e)/6e4,e=>e.getMinutes());tc.range;let ts=ta(e=>{e.setUTCSeconds(0,0)},(e,t)=>{e.setTime(+e+6e4*t)},(e,t)=>(t-e)/6e4,e=>e.getUTCMinutes());ts.range;let td=ta(e=>{e.setTime(e-e.getMilliseconds()-1e3*e.getSeconds()-6e4*e.getMinutes())},(e,t)=>{e.setTime(+e+36e5*t)},(e,t)=>(t-e)/36e5,e=>e.getHours());td.range;let tf=ta(e=>{e.setUTCMinutes(0,0,0)},(e,t)=>{e.setTime(+e+36e5*t)},(e,t)=>(t-e)/36e5,e=>e.getUTCHours());tf.range;let tp=ta(e=>e.setHours(0,0,0,0),(e,t)=>e.setDate(e.getDate()+t),(e,t)=>(t-e-(t.getTimezoneOffset()-e.getTimezoneOffset())*6e4)/864e5,e=>e.getDate()-1);tp.range;let th=ta(e=>{e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCDate(e.getUTCDate()+t)},(e,t)=>(t-e)/864e5,e=>e.getUTCDate()-1);th.range;let tv=ta(e=>{e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCDate(e.getUTCDate()+t)},(e,t)=>(t-e)/864e5,e=>Math.floor(e/864e5));function ty(e){return ta(t=>{t.setDate(t.getDate()-(t.getDay()+7-e)%7),t.setHours(0,0,0,0)},(e,t)=>{e.setDate(e.getDate()+7*t)},(e,t)=>(t-e-(t.getTimezoneOffset()-e.getTimezoneOffset())*6e4)/6048e5)}tv.range;let tm=ty(0),tg=ty(1),tb=ty(2),tw=ty(3),tx=ty(4),tO=ty(5),tj=ty(6);function tk(e){return ta(t=>{t.setUTCDate(t.getUTCDate()-(t.getUTCDay()+7-e)%7),t.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCDate(e.getUTCDate()+7*t)},(e,t)=>(t-e)/6048e5)}tm.range,tg.range,tb.range,tw.range,tx.range,tO.range,tj.range;let tP=tk(0),tE=tk(1),tM=tk(2),tS=tk(3),tC=tk(4),tA=tk(5),tT=tk(6);tP.range,tE.range,tM.range,tS.range,tC.range,tA.range,tT.range;let tD=ta(e=>{e.setDate(1),e.setHours(0,0,0,0)},(e,t)=>{e.setMonth(e.getMonth()+t)},(e,t)=>t.getMonth()-e.getMonth()+(t.getFullYear()-e.getFullYear())*12,e=>e.getMonth());tD.range;let tN=ta(e=>{e.setUTCDate(1),e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCMonth(e.getUTCMonth()+t)},(e,t)=>t.getUTCMonth()-e.getUTCMonth()+(t.getUTCFullYear()-e.getUTCFullYear())*12,e=>e.getUTCMonth());tN.range;let t_=ta(e=>{e.setMonth(0,1),e.setHours(0,0,0,0)},(e,t)=>{e.setFullYear(e.getFullYear()+t)},(e,t)=>t.getFullYear()-e.getFullYear(),e=>e.getFullYear());t_.every=e=>isFinite(e=Math.floor(e))&&e>0?ta(t=>{t.setFullYear(Math.floor(t.getFullYear()/e)*e),t.setMonth(0,1),t.setHours(0,0,0,0)},(t,r)=>{t.setFullYear(t.getFullYear()+r*e)}):null,t_.range;let tI=ta(e=>{e.setUTCMonth(0,1),e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCFullYear(e.getUTCFullYear()+t)},(e,t)=>t.getUTCFullYear()-e.getUTCFullYear(),e=>e.getUTCFullYear());function tR(e,t,r,n,o,i){let a=[[tu,1,1e3],[tu,5,5e3],[tu,15,15e3],[tu,30,3e4],[i,1,6e4],[i,5,3e5],[i,15,9e5],[i,30,18e5],[o,1,36e5],[o,3,108e5],[o,6,216e5],[o,12,432e5],[n,1,864e5],[n,2,1728e5],[r,1,6048e5],[t,1,2592e6],[t,3,7776e6],[e,1,31536e6]];function l(t,r,n){let o=Math.abs(r-t)/n,i=D(([,,e])=>e).right(a,o);if(i===a.length)return e.every(C(t/31536e6,r/31536e6,n));if(0===i)return tl.every(Math.max(C(t,r,n),1));let[l,u]=a[o/a[i-1][2]<a[i][2]/o?i-1:i];return l.every(u)}return[function(e,t,r){let n=t<e;n&&([e,t]=[t,e]);let o=r&&"function"==typeof r.range?r:l(e,t,r),i=o?o.range(e,+t+1):[];return n?i.reverse():i},l]}tI.every=e=>isFinite(e=Math.floor(e))&&e>0?ta(t=>{t.setUTCFullYear(Math.floor(t.getUTCFullYear()/e)*e),t.setUTCMonth(0,1),t.setUTCHours(0,0,0,0)},(t,r)=>{t.setUTCFullYear(t.getUTCFullYear()+r*e)}):null,tI.range;let[tL,tz]=tR(tI,tN,tP,tv,tf,ts),[tW,tF]=tR(t_,tD,tm,tp,td,tc);function tB(e){if(0<=e.y&&e.y<100){var t=new Date(-1,e.m,e.d,e.H,e.M,e.S,e.L);return t.setFullYear(e.y),t}return new Date(e.y,e.m,e.d,e.H,e.M,e.S,e.L)}function tK(e){if(0<=e.y&&e.y<100){var t=new Date(Date.UTC(-1,e.m,e.d,e.H,e.M,e.S,e.L));return t.setUTCFullYear(e.y),t}return new Date(Date.UTC(e.y,e.m,e.d,e.H,e.M,e.S,e.L))}function tV(e,t,r){return{y:e,m:t,d:r,H:0,M:0,S:0,L:0}}var tZ={"-":"",_:" ",0:"0"},tU=/^\s*\d+/,t$=/^%/,tH=/[\\^$*+?|[\]().{}]/g;function tY(e,t,r){var n=e<0?"-":"",o=(n?-e:e)+"",i=o.length;return n+(i<r?Array(r-i+1).join(t)+o:o)}function tq(e){return e.replace(tH,"\\$&")}function tX(e){return RegExp("^(?:"+e.map(tq).join("|")+")","i")}function tG(e){return new Map(e.map((e,t)=>[e.toLowerCase(),t]))}function tQ(e,t,r){var n=tU.exec(t.slice(r,r+1));return n?(e.w=+n[0],r+n[0].length):-1}function tJ(e,t,r){var n=tU.exec(t.slice(r,r+1));return n?(e.u=+n[0],r+n[0].length):-1}function t0(e,t,r){var n=tU.exec(t.slice(r,r+2));return n?(e.U=+n[0],r+n[0].length):-1}function t1(e,t,r){var n=tU.exec(t.slice(r,r+2));return n?(e.V=+n[0],r+n[0].length):-1}function t2(e,t,r){var n=tU.exec(t.slice(r,r+2));return n?(e.W=+n[0],r+n[0].length):-1}function t6(e,t,r){var n=tU.exec(t.slice(r,r+4));return n?(e.y=+n[0],r+n[0].length):-1}function t5(e,t,r){var n=tU.exec(t.slice(r,r+2));return n?(e.y=+n[0]+(+n[0]>68?1900:2e3),r+n[0].length):-1}function t3(e,t,r){var n=/^(Z)|([+-]\d\d)(?::?(\d\d))?/.exec(t.slice(r,r+6));return n?(e.Z=n[1]?0:-(n[2]+(n[3]||"00")),r+n[0].length):-1}function t4(e,t,r){var n=tU.exec(t.slice(r,r+1));return n?(e.q=3*n[0]-3,r+n[0].length):-1}function t9(e,t,r){var n=tU.exec(t.slice(r,r+2));return n?(e.m=n[0]-1,r+n[0].length):-1}function t7(e,t,r){var n=tU.exec(t.slice(r,r+2));return n?(e.d=+n[0],r+n[0].length):-1}function t8(e,t,r){var n=tU.exec(t.slice(r,r+3));return n?(e.m=0,e.d=+n[0],r+n[0].length):-1}function re(e,t,r){var n=tU.exec(t.slice(r,r+2));return n?(e.H=+n[0],r+n[0].length):-1}function rt(e,t,r){var n=tU.exec(t.slice(r,r+2));return n?(e.M=+n[0],r+n[0].length):-1}function rr(e,t,r){var n=tU.exec(t.slice(r,r+2));return n?(e.S=+n[0],r+n[0].length):-1}function rn(e,t,r){var n=tU.exec(t.slice(r,r+3));return n?(e.L=+n[0],r+n[0].length):-1}function ro(e,t,r){var n=tU.exec(t.slice(r,r+6));return n?(e.L=Math.floor(n[0]/1e3),r+n[0].length):-1}function ri(e,t,r){var n=t$.exec(t.slice(r,r+1));return n?r+n[0].length:-1}function ra(e,t,r){var n=tU.exec(t.slice(r));return n?(e.Q=+n[0],r+n[0].length):-1}function rl(e,t,r){var n=tU.exec(t.slice(r));return n?(e.s=+n[0],r+n[0].length):-1}function ru(e,t){return tY(e.getDate(),t,2)}function rc(e,t){return tY(e.getHours(),t,2)}function rs(e,t){return tY(e.getHours()%12||12,t,2)}function rd(e,t){return tY(1+tp.count(t_(e),e),t,3)}function rf(e,t){return tY(e.getMilliseconds(),t,3)}function rp(e,t){return rf(e,t)+"000"}function rh(e,t){return tY(e.getMonth()+1,t,2)}function rv(e,t){return tY(e.getMinutes(),t,2)}function ry(e,t){return tY(e.getSeconds(),t,2)}function rm(e){var t=e.getDay();return 0===t?7:t}function rg(e,t){return tY(tm.count(t_(e)-1,e),t,2)}function rb(e){var t=e.getDay();return t>=4||0===t?tx(e):tx.ceil(e)}function rw(e,t){return e=rb(e),tY(tx.count(t_(e),e)+(4===t_(e).getDay()),t,2)}function rx(e){return e.getDay()}function rO(e,t){return tY(tg.count(t_(e)-1,e),t,2)}function rj(e,t){return tY(e.getFullYear()%100,t,2)}function rk(e,t){return tY((e=rb(e)).getFullYear()%100,t,2)}function rP(e,t){return tY(e.getFullYear()%1e4,t,4)}function rE(e,t){var r=e.getDay();return tY((e=r>=4||0===r?tx(e):tx.ceil(e)).getFullYear()%1e4,t,4)}function rM(e){var t=e.getTimezoneOffset();return(t>0?"-":(t*=-1,"+"))+tY(t/60|0,"0",2)+tY(t%60,"0",2)}function rS(e,t){return tY(e.getUTCDate(),t,2)}function rC(e,t){return tY(e.getUTCHours(),t,2)}function rA(e,t){return tY(e.getUTCHours()%12||12,t,2)}function rT(e,t){return tY(1+th.count(tI(e),e),t,3)}function rD(e,t){return tY(e.getUTCMilliseconds(),t,3)}function rN(e,t){return rD(e,t)+"000"}function r_(e,t){return tY(e.getUTCMonth()+1,t,2)}function rI(e,t){return tY(e.getUTCMinutes(),t,2)}function rR(e,t){return tY(e.getUTCSeconds(),t,2)}function rL(e){var t=e.getUTCDay();return 0===t?7:t}function rz(e,t){return tY(tP.count(tI(e)-1,e),t,2)}function rW(e){var t=e.getUTCDay();return t>=4||0===t?tC(e):tC.ceil(e)}function rF(e,t){return e=rW(e),tY(tC.count(tI(e),e)+(4===tI(e).getUTCDay()),t,2)}function rB(e){return e.getUTCDay()}function rK(e,t){return tY(tE.count(tI(e)-1,e),t,2)}function rV(e,t){return tY(e.getUTCFullYear()%100,t,2)}function rZ(e,t){return tY((e=rW(e)).getUTCFullYear()%100,t,2)}function rU(e,t){return tY(e.getUTCFullYear()%1e4,t,4)}function r$(e,t){var r=e.getUTCDay();return tY((e=r>=4||0===r?tC(e):tC.ceil(e)).getUTCFullYear()%1e4,t,4)}function rH(){return"+0000"}function rY(){return"%"}function rq(e){return+e}function rX(e){return Math.floor(+e/1e3)}function rG(e){return new Date(e)}function rQ(e){return e instanceof Date?+e:+new Date(+e)}function rJ(e,t,r,n,o,i,a,l,u,c){var s=eN(),d=s.invert,f=s.domain,p=c(".%L"),h=c(":%S"),v=c("%I:%M"),y=c("%I %p"),m=c("%a %d"),g=c("%b %d"),b=c("%B"),w=c("%Y");function x(e){return(u(e)<e?p:l(e)<e?h:a(e)<e?v:i(e)<e?y:n(e)<e?o(e)<e?m:g:r(e)<e?b:w)(e)}return s.invert=function(e){return new Date(d(e))},s.domain=function(e){return arguments.length?f(Array.from(e,rQ)):f().map(rG)},s.ticks=function(t){var r=f();return e(r[0],r[r.length-1],null==t?10:t)},s.tickFormat=function(e,t){return null==t?x:c(t)},s.nice=function(e){var r=f();return e&&"function"==typeof e.range||(e=t(r[0],r[r.length-1],null==e?10:e)),e?f(e$(r,e)):s},s.copy=function(){return eT(s,rJ(e,t,r,n,o,i,a,l,u,c))},s}function r0(){return h.apply(rJ(tW,tF,t_,tD,tm,tp,td,tc,tu,u).domain([new Date(2e3,0,1),new Date(2e3,0,2)]),arguments)}function r1(){return h.apply(rJ(tL,tz,tI,tN,tP,th,tf,ts,tu,c).domain([Date.UTC(2e3,0,1),Date.UTC(2e3,0,2)]),arguments)}function r2(){var e,t,r,n,o,i=0,a=1,l=eM,u=!1;function c(t){return null==t||isNaN(t=+t)?o:l(0===r?.5:(t=(n(t)-e)*r,u?Math.max(0,Math.min(1,t)):t))}function s(e){return function(t){var r,n;return arguments.length?([r,n]=t,l=e(r,n),c):[l(0),l(1)]}}return c.domain=function(o){return arguments.length?([i,a]=o,e=n(i=+i),t=n(a=+a),r=e===t?0:1/(t-e),c):[i,a]},c.clamp=function(e){return arguments.length?(u=!!e,c):u},c.interpolator=function(e){return arguments.length?(l=e,c):l},c.range=s(ej),c.rangeRound=s(ek),c.unknown=function(e){return arguments.length?(o=e,c):o},function(o){return n=o,e=o(i),t=o(a),r=e===t?0:1/(t-e),c}}function r6(e,t){return t.domain(e.domain()).interpolator(e.interpolator()).clamp(e.clamp()).unknown(e.unknown())}function r5(){var e=e4(r2());return e.copy=function(){return r6(e,r5()).exponent(e.exponent())},v.apply(e,arguments)}function r3(){return r5.apply(null,arguments).exponent(.5)}function r4(){var e,t,r,n,o,i,a,l=0,u=.5,c=1,s=1,d=eM,f=!1;function p(e){return isNaN(e=+e)?a:(e=.5+((e=+i(e))-t)*(s*e<s*t?n:o),d(f?Math.max(0,Math.min(1,e)):e))}function h(e){return function(t){var r,n,o;return arguments.length?([r,n,o]=t,d=function(e,t){void 0===t&&(t=e,e=ej);for(var r=0,n=t.length-1,o=t[0],i=Array(n<0?0:n);r<n;)i[r]=e(o,o=t[++r]);return function(e){var t=Math.max(0,Math.min(n-1,Math.floor(e*=n)));return i[t](e-t)}}(e,[r,n,o]),p):[d(0),d(.5),d(1)]}}return p.domain=function(a){return arguments.length?([l,u,c]=a,e=i(l=+l),t=i(u=+u),r=i(c=+c),n=e===t?0:.5/(t-e),o=t===r?0:.5/(r-t),s=t<e?-1:1,p):[l,u,c]},p.clamp=function(e){return arguments.length?(f=!!e,p):f},p.interpolator=function(e){return arguments.length?(d=e,p):d},p.range=h(ej),p.rangeRound=h(ek),p.unknown=function(e){return arguments.length?(a=e,p):a},function(a){return i=a,e=a(l),t=a(u),r=a(c),n=e===t?0:.5/(t-e),o=t===r?0:.5/(r-t),s=t<e?-1:1,p}}function r9(){var e=e4(r4());return e.copy=function(){return r6(e,r9()).exponent(e.exponent())},v.apply(e,arguments)}function r7(){return r9.apply(null,arguments).exponent(.5)}u=(l=function(e){var t=e.dateTime,r=e.date,n=e.time,o=e.periods,i=e.days,a=e.shortDays,l=e.months,u=e.shortMonths,c=tX(o),s=tG(o),d=tX(i),f=tG(i),p=tX(a),h=tG(a),v=tX(l),y=tG(l),m=tX(u),g=tG(u),b={a:function(e){return a[e.getDay()]},A:function(e){return i[e.getDay()]},b:function(e){return u[e.getMonth()]},B:function(e){return l[e.getMonth()]},c:null,d:ru,e:ru,f:rp,g:rk,G:rE,H:rc,I:rs,j:rd,L:rf,m:rh,M:rv,p:function(e){return o[+(e.getHours()>=12)]},q:function(e){return 1+~~(e.getMonth()/3)},Q:rq,s:rX,S:ry,u:rm,U:rg,V:rw,w:rx,W:rO,x:null,X:null,y:rj,Y:rP,Z:rM,"%":rY},w={a:function(e){return a[e.getUTCDay()]},A:function(e){return i[e.getUTCDay()]},b:function(e){return u[e.getUTCMonth()]},B:function(e){return l[e.getUTCMonth()]},c:null,d:rS,e:rS,f:rN,g:rZ,G:r$,H:rC,I:rA,j:rT,L:rD,m:r_,M:rI,p:function(e){return o[+(e.getUTCHours()>=12)]},q:function(e){return 1+~~(e.getUTCMonth()/3)},Q:rq,s:rX,S:rR,u:rL,U:rz,V:rF,w:rB,W:rK,x:null,X:null,y:rV,Y:rU,Z:rH,"%":rY},x={a:function(e,t,r){var n=p.exec(t.slice(r));return n?(e.w=h.get(n[0].toLowerCase()),r+n[0].length):-1},A:function(e,t,r){var n=d.exec(t.slice(r));return n?(e.w=f.get(n[0].toLowerCase()),r+n[0].length):-1},b:function(e,t,r){var n=m.exec(t.slice(r));return n?(e.m=g.get(n[0].toLowerCase()),r+n[0].length):-1},B:function(e,t,r){var n=v.exec(t.slice(r));return n?(e.m=y.get(n[0].toLowerCase()),r+n[0].length):-1},c:function(e,r,n){return k(e,t,r,n)},d:t7,e:t7,f:ro,g:t5,G:t6,H:re,I:re,j:t8,L:rn,m:t9,M:rt,p:function(e,t,r){var n=c.exec(t.slice(r));return n?(e.p=s.get(n[0].toLowerCase()),r+n[0].length):-1},q:t4,Q:ra,s:rl,S:rr,u:tJ,U:t0,V:t1,w:tQ,W:t2,x:function(e,t,n){return k(e,r,t,n)},X:function(e,t,r){return k(e,n,t,r)},y:t5,Y:t6,Z:t3,"%":ri};function O(e,t){return function(r){var n,o,i,a=[],l=-1,u=0,c=e.length;for(r instanceof Date||(r=new Date(+r));++l<c;)37===e.charCodeAt(l)&&(a.push(e.slice(u,l)),null!=(o=tZ[n=e.charAt(++l)])?n=e.charAt(++l):o="e"===n?" ":"0",(i=t[n])&&(n=i(r,o)),a.push(n),u=l+1);return a.push(e.slice(u,l)),a.join("")}}function j(e,t){return function(r){var n,o,i=tV(1900,void 0,1);if(k(i,e,r+="",0)!=r.length)return null;if("Q"in i)return new Date(i.Q);if("s"in i)return new Date(1e3*i.s+("L"in i?i.L:0));if(!t||"Z"in i||(i.Z=0),"p"in i&&(i.H=i.H%12+12*i.p),void 0===i.m&&(i.m="q"in i?i.q:0),"V"in i){if(i.V<1||i.V>53)return null;"w"in i||(i.w=1),"Z"in i?(n=(o=(n=tK(tV(i.y,0,1))).getUTCDay())>4||0===o?tE.ceil(n):tE(n),n=th.offset(n,(i.V-1)*7),i.y=n.getUTCFullYear(),i.m=n.getUTCMonth(),i.d=n.getUTCDate()+(i.w+6)%7):(n=(o=(n=tB(tV(i.y,0,1))).getDay())>4||0===o?tg.ceil(n):tg(n),n=tp.offset(n,(i.V-1)*7),i.y=n.getFullYear(),i.m=n.getMonth(),i.d=n.getDate()+(i.w+6)%7)}else("W"in i||"U"in i)&&("w"in i||(i.w="u"in i?i.u%7:"W"in i?1:0),o="Z"in i?tK(tV(i.y,0,1)).getUTCDay():tB(tV(i.y,0,1)).getDay(),i.m=0,i.d="W"in i?(i.w+6)%7+7*i.W-(o+5)%7:i.w+7*i.U-(o+6)%7);return"Z"in i?(i.H+=i.Z/100|0,i.M+=i.Z%100,tK(i)):tB(i)}}function k(e,t,r,n){for(var o,i,a=0,l=t.length,u=r.length;a<l;){if(n>=u)return -1;if(37===(o=t.charCodeAt(a++))){if(!(i=x[(o=t.charAt(a++))in tZ?t.charAt(a++):o])||(n=i(e,r,n))<0)return -1}else if(o!=r.charCodeAt(n++))return -1}return n}return b.x=O(r,b),b.X=O(n,b),b.c=O(t,b),w.x=O(r,w),w.X=O(n,w),w.c=O(t,w),{format:function(e){var t=O(e+="",b);return t.toString=function(){return e},t},parse:function(e){var t=j(e+="",!1);return t.toString=function(){return e},t},utcFormat:function(e){var t=O(e+="",w);return t.toString=function(){return e},t},utcParse:function(e){var t=j(e+="",!0);return t.toString=function(){return e},t}}}({dateTime:"%x, %X",date:"%-m/%-d/%Y",time:"%-I:%M:%S %p",periods:["AM","PM"],days:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],shortDays:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],months:["January","February","March","April","May","June","July","August","September","October","November","December"],shortMonths:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"]})).format,l.parse,c=l.utcFormat,l.utcParse;var r8=r(35953),ne=r(49037),nt=r(22932),nr=r(16630),nn=r(66395);function no(e){if(Array.isArray(e)&&2===e.length){var[t,r]=e;if((0,nn.n)(t)&&(0,nn.n)(r))return!0}return!1}function ni(e,t,r){return r?e:[Math.min(e[0],t[0]),Math.max(e[1],t[1])]}var na=r(61134),nl=r.n(na),nu=e=>e,nc={},ns=e=>e===nc,nd=e=>function t(){return 0==arguments.length||1==arguments.length&&ns(arguments.length<=0?void 0:arguments[0])?t:e(...arguments)},nf=(e,t)=>1===e?t:nd(function(){for(var r=arguments.length,n=Array(r),o=0;o<r;o++)n[o]=arguments[o];var i=n.filter(e=>e!==nc).length;return i>=e?t(...n):nf(e-i,nd(function(){for(var e=arguments.length,r=Array(e),o=0;o<e;o++)r[o]=arguments[o];return t(...n.map(e=>ns(e)?r.shift():e),...r)}))}),np=e=>nf(e.length,e),nh=(e,t)=>{for(var r=[],n=e;n<t;++n)r[n-e]=n;return r},nv=np((e,t)=>Array.isArray(t)?t.map(e):Object.keys(t).map(e=>t[e]).map(e)),ny=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];if(!t.length)return nu;var n=t.reverse(),o=n[0],i=n.slice(1);return function(){return i.reduce((e,t)=>t(e),o(...arguments))}},nm=e=>Array.isArray(e)?e.reverse():e.split("").reverse().join(""),ng=e=>{var t=null,r=null;return function(){for(var n=arguments.length,o=Array(n),i=0;i<n;i++)o[i]=arguments[i];return t&&o.every((e,r)=>{var n;return e===(null===(n=t)||void 0===n?void 0:n[r])})?r:(t=o,r=e(...o))}};function nb(e){return 0===e?1:Math.floor(new(nl())(e).abs().log(10).toNumber())+1}function nw(e,t,r){for(var n=new(nl())(e),o=0,i=[];n.lt(t)&&o<1e5;)i.push(n.toNumber()),n=n.add(r),o++;return i}np((e,t,r)=>{var n=+e;return n+r*(+t-n)}),np((e,t,r)=>{var n=t-+e;return(r-e)/(n=n||1/0)}),np((e,t,r)=>{var n=t-+e;return Math.max(0,Math.min(1,(r-e)/(n=n||1/0)))});var nx=e=>{var[t,r]=e,[n,o]=[t,r];return t>r&&([n,o]=[r,t]),[n,o]},nO=(e,t,r)=>{if(e.lte(0))return new(nl())(0);var n=nb(e.toNumber()),o=new(nl())(10).pow(n),i=e.div(o),a=1!==n?.05:.1,l=new(nl())(Math.ceil(i.div(a).toNumber())).add(r).mul(a).mul(o);return new(nl())(t?l.toNumber():Math.ceil(l.toNumber()))},nj=(e,t,r)=>{var n=new(nl())(1),o=new(nl())(e);if(!o.isint()&&r){var i=Math.abs(e);i<1?(n=new(nl())(10).pow(nb(e)-1),o=new(nl())(Math.floor(o.div(n).toNumber())).mul(n)):i>1&&(o=new(nl())(Math.floor(e)))}else 0===e?o=new(nl())(Math.floor((t-1)/2)):r||(o=new(nl())(Math.floor(e)));var a=Math.floor((t-1)/2);return ny(nv(e=>o.add(new(nl())(e-a).mul(n)).toNumber()),nh)(0,t)},nk=function(e,t,r,n){var o,i=arguments.length>4&&void 0!==arguments[4]?arguments[4]:0;if(!Number.isFinite((t-e)/(r-1)))return{step:new(nl())(0),tickMin:new(nl())(0),tickMax:new(nl())(0)};var a=nO(new(nl())(t).sub(e).div(r-1),n,i),l=Math.ceil((o=e<=0&&t>=0?new(nl())(0):(o=new(nl())(e).add(t).div(2)).sub(new(nl())(o).mod(a))).sub(e).div(a).toNumber()),u=Math.ceil(new(nl())(t).sub(o).div(a).toNumber()),c=l+u+1;return c>r?nk(e,t,r,n,i+1):(c<r&&(u=t>0?u+(r-c):u,l=t>0?l:l+(r-c)),{step:a,tickMin:o.sub(new(nl())(l).mul(a)),tickMax:o.add(new(nl())(u).mul(a))})},nP=ng(function(e){var[t,r]=e,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:6,o=!(arguments.length>2)||void 0===arguments[2]||arguments[2],i=Math.max(n,2),[a,l]=nx([t,r]);if(a===-1/0||l===1/0){var u=l===1/0?[a,...nh(0,n-1).map(()=>1/0)]:[...nh(0,n-1).map(()=>-1/0),l];return t>r?nm(u):u}if(a===l)return nj(a,n,o);var{step:c,tickMin:s,tickMax:d}=nk(a,l,i,o,0),f=nw(s,d.add(new(nl())(.1).mul(c)),c);return t>r?nm(f):f}),nE=ng(function(e,t){var[r,n]=e,o=!(arguments.length>2)||void 0===arguments[2]||arguments[2],[i,a]=nx([r,n]);if(i===-1/0||a===1/0)return[r,n];if(i===a)return[i];var l=nO(new(nl())(a).sub(i).div(Math.max(t,2)-1),o,0),u=[...nw(new(nl())(i),new(nl())(a).sub(new(nl())(.99).mul(l)),l),a];return r>n?nm(u):u}),nM=r(60152),nS=r(32498),nC=r(36289),nA=r(2431),nT=r(33968),nD=r(80796),nN=r(40304),n_=r(56462),nI=r(87367),nR=r(78487);function nL(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function nz(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?nL(Object(r),!0).forEach(function(t){var n,o;n=t,o=r[t],(n=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(n))in e?Object.defineProperty(e,n,{value:o,enumerable:!0,configurable:!0,writable:!0}):e[n]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):nL(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var nW=[0,"auto"],nF={allowDataOverflow:!1,allowDecimals:!0,allowDuplicatedCategory:!0,angle:0,dataKey:void 0,domain:void 0,height:30,hide:!0,id:0,includeHidden:!1,interval:"preserveEnd",minTickGap:5,mirror:!1,name:void 0,orientation:"bottom",padding:{left:0,right:0},reversed:!1,scale:"auto",tick:!0,tickCount:5,tickFormatter:void 0,ticks:void 0,type:"category",unit:void 0},nB=(e,t)=>{var r=e.cartesianAxis.xAxis[t];return null==r?nF:r},nK={allowDataOverflow:!1,allowDecimals:!0,allowDuplicatedCategory:!0,angle:0,dataKey:void 0,domain:nW,hide:!0,id:0,includeHidden:!1,interval:"preserveEnd",minTickGap:5,mirror:!1,name:void 0,orientation:"left",padding:{top:0,bottom:0},reversed:!1,scale:"auto",tick:!0,tickCount:5,tickFormatter:void 0,ticks:void 0,type:"number",unit:void 0,width:nR.n9},nV=(e,t)=>{var r=e.cartesianAxis.yAxis[t];return null==r?nK:r},nZ={domain:[0,"auto"],includeHidden:!1,reversed:!1,allowDataOverflow:!1,allowDuplicatedCategory:!1,dataKey:void 0,id:0,name:"",range:[64,64],scale:"auto",type:"number",unit:""},nU=(e,t)=>{var r=e.cartesianAxis.zAxis[t];return null==r?nZ:r},n$=(e,t,r)=>{switch(t){case"xAxis":return nB(e,r);case"yAxis":return nV(e,r);case"zAxis":return nU(e,r);case"angleAxis":return(0,nD.dc)(e,r);case"radiusAxis":return(0,nD.Au)(e,r);default:throw Error("Unexpected axis type: ".concat(t))}},nH=(e,t,r)=>{switch(t){case"xAxis":return nB(e,r);case"yAxis":return nV(e,r);case"angleAxis":return(0,nD.dc)(e,r);case"radiusAxis":return(0,nD.Au)(e,r);default:throw Error("Unexpected axis type: ".concat(t))}},nY=e=>e.graphicalItems.countOfBars>0;function nq(e,t){return r=>{switch(e){case"xAxis":return"xAxisId"in r&&r.xAxisId===t;case"yAxis":return"yAxisId"in r&&r.yAxisId===t;case"zAxis":return"zAxisId"in r&&r.zAxisId===t;case"angleAxis":return"angleAxisId"in r&&r.angleAxisId===t;case"radiusAxis":return"radiusAxisId"in r&&r.radiusAxisId===t;default:return!1}}}var nX=e=>e.graphicalItems.cartesianItems,nG=(0,d.P1)([nN.z,n_.l],nq),nQ=(e,t,r)=>e.filter(r).filter(e=>(null==t?void 0:t.includeHidden)===!0||!e.hide),nJ=(0,d.P1)([nX,n$,nG],nQ),n0=e=>e.filter(e=>void 0===e.stackId),n1=(0,d.P1)([nJ],n0),n2=e=>e.map(e=>e.data).filter(Boolean).flat(1),n6=(0,d.P1)([nJ],n2),n5=(e,t)=>{var{chartData:r=[],dataStartIndex:n,dataEndIndex:o}=t;return e.length>0?e:r.slice(n,o+1)},n3=(0,d.P1)([n6,nt.hA],n5),n4=(e,t,r)=>(null==t?void 0:t.dataKey)!=null?e.map(e=>({value:(0,ne.F$)(e,t.dataKey)})):r.length>0?r.map(e=>e.dataKey).flatMap(t=>e.map(e=>({value:(0,ne.F$)(e,t)}))):e.map(e=>({value:e})),n9=(0,d.P1)([n3,n$,nJ],n4);function n7(e,t){switch(e){case"xAxis":return"x"===t.direction;case"yAxis":return"y"===t.direction;default:return!1}}function n8(e){return e.filter(e=>(0,nr.P2)(e)||e instanceof Date).map(Number).filter(e=>!1===(0,nr.In)(e))}var oe=(e,t,r)=>Object.fromEntries(Object.entries(t.reduce((e,t)=>(null==t.stackId||(null==e[t.stackId]&&(e[t.stackId]=[]),e[t.stackId].push(t)),e),{})).map(t=>{var[n,o]=t,i=o.map(e=>e.dataKey);return[n,{stackedData:(0,ne.uX)(e,i,r),graphicalItems:o}]})),ot=(0,d.P1)([n3,nJ,nT.Qw],oe),or=(e,t,r)=>{var{dataStartIndex:n,dataEndIndex:o}=t;if("zAxis"!==r){var i=(0,ne.EB)(e,n,o);if(null==i||0!==i[0]||0!==i[1])return i}},on=(0,d.P1)([ot,nt.iP,nN.z],or),oo=(e,t,r,n)=>r.length>0?e.flatMap(e=>r.flatMap(r=>{var o,i,a=null===(o=r.errorBars)||void 0===o?void 0:o.filter(e=>n7(n,e)),l=(0,ne.F$)(e,null!==(i=t.dataKey)&&void 0!==i?i:r.dataKey);return{value:l,errorDomain:!(!a||"number"!=typeof l||(0,nr.In)(l))&&a.length?n8(a.flatMap(t=>{var r,n,o=(0,ne.F$)(e,t.dataKey);if(Array.isArray(o)?[r,n]=o:r=n=o,(0,nn.n)(r)&&(0,nn.n)(n))return[l-r,l+n]})):[]}})).filter(Boolean):(null==t?void 0:t.dataKey)!=null?e.map(e=>({value:(0,ne.F$)(e,t.dataKey),errorDomain:[]})):e.map(e=>({value:e,errorDomain:[]})),oi=(0,d.P1)(n3,n$,n1,nN.z,oo);function oa(e){var{value:t}=e;if((0,nr.P2)(t)||t instanceof Date)return t}var ol=e=>{var t=n8(e.flatMap(e=>[e.value,e.errorDomain]).flat(1));if(0!==t.length)return[Math.min(...t),Math.max(...t)]},ou=(e,t,r)=>{var n=e.map(oa).filter(e=>null!=e);return r&&(null==t.dataKey||t.allowDuplicatedCategory&&(0,nr.bv)(n))?p()(0,e.length):t.allowDuplicatedCategory?n:Array.from(new Set(n))},oc=e=>{var t;if(null==e||!("domain"in e))return nW;if(null!=e.domain)return e.domain;if(null!=e.ticks){if("number"===e.type){var r=n8(e.ticks);return[Math.min(...r),Math.max(...r)]}if("category"===e.type)return e.ticks.map(String)}return null!==(t=null==e?void 0:e.domain)&&void 0!==t?t:nW},os=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];var n=t.filter(Boolean);if(0!==n.length){var o=n.flat();return[Math.min(...o),Math.max(...o)]}},od=e=>e.referenceElements.dots,of=(e,t,r)=>e.filter(e=>"extendDomain"===e.ifOverflow).filter(e=>"xAxis"===t?e.xAxisId===r:e.yAxisId===r),op=(0,d.P1)([od,nN.z,n_.l],of),oh=e=>e.referenceElements.areas,ov=(0,d.P1)([oh,nN.z,n_.l],of),oy=e=>e.referenceElements.lines,om=(0,d.P1)([oy,nN.z,n_.l],of),og=(e,t)=>{var r=n8(e.map(e=>"xAxis"===t?e.x:e.y));if(0!==r.length)return[Math.min(...r),Math.max(...r)]},ob=(0,d.P1)(op,nN.z,og),ow=(e,t)=>{var r=n8(e.flatMap(e=>["xAxis"===t?e.x1:e.y1,"xAxis"===t?e.x2:e.y2]));if(0!==r.length)return[Math.min(...r),Math.max(...r)]},ox=(0,d.P1)([ov,nN.z],ow),oO=(e,t)=>{var r=n8(e.map(e=>"xAxis"===t?e.x:e.y));if(0!==r.length)return[Math.min(...r),Math.max(...r)]},oj=(0,d.P1)(om,nN.z,oO),ok=(0,d.P1)(ob,oj,ox,(e,t,r)=>os(e,r,t)),oP=(0,d.P1)([n$],oc),oE=(e,t,r,n,o)=>{var i=function(e,t){if(t&&"function"!=typeof e&&Array.isArray(e)&&2===e.length){var r,n,[o,i]=e;if((0,nn.n)(o))r=o;else if("function"==typeof o)return;if((0,nn.n)(i))n=i;else if("function"==typeof i)return;var a=[r,n];if(no(a))return a}}(t,e.allowDataOverflow);return null!=i?i:function(e,t,r){if(r||null!=t){if("function"==typeof e&&null!=t)try{var n=e(t,r);if(no(n))return ni(n,t,r)}catch(e){}if(Array.isArray(e)&&2===e.length){var o,i,[a,l]=e;if("auto"===a)null!=t&&(o=Math.min(...t));else if((0,nr.hj)(a))o=a;else if("function"==typeof a)try{null!=t&&(o=a(null==t?void 0:t[0]))}catch(e){}else if("string"==typeof a&&ne.rI.test(a)){var u=ne.rI.exec(a);if(null==u||null==t)o=void 0;else{var c=+u[1];o=t[0]-c}}else o=null==t?void 0:t[0];if("auto"===l)null!=t&&(i=Math.max(...t));else if((0,nr.hj)(l))i=l;else if("function"==typeof l)try{null!=t&&(i=l(null==t?void 0:t[1]))}catch(e){}else if("string"==typeof l&&ne.Ji.test(l)){var s=ne.Ji.exec(l);if(null==s||null==t)i=void 0;else{var d=+s[1];i=t[1]+d}}else i=null==t?void 0:t[1];var f=[o,i];if(no(f))return null==t?f:ni(f,t,r)}}}(t,os(r,o,ol(n)),e.allowDataOverflow)},oM=(0,d.P1)([n$,oP,on,oi,ok],oE),oS=[0,1],oC=(e,t,r,n,o,i,a)=>{if(null!=e&&null!=r&&0!==r.length){var{dataKey:l,type:u}=e,c=(0,ne.NA)(t,i);return c&&null==l?p()(0,r.length):"category"===u?ou(n,e,c):"expand"===o?oS:a}},oA=(0,d.P1)([n$,r8.rE,n3,n9,nT.Qw,nN.z,oM],oC),oT=(e,t,r,n,o)=>{if(null!=e){var{scale:i,type:a}=e;if("auto"===i)return"radial"===t&&"radiusAxis"===o?"band":"radial"===t&&"angleAxis"===o?"linear":"category"===a&&n&&(n.indexOf("LineChart")>=0||n.indexOf("AreaChart")>=0||n.indexOf("ComposedChart")>=0&&!r)?"point":"category"===a?"band":"linear";if("string"==typeof i){var l="scale".concat((0,nr.jC)(i));return l in s?l:"point"}}},oD=(0,d.P1)([n$,r8.rE,nY,nT.E2,nN.z],oT);function oN(e,t,r,n){if(null!=r&&null!=n){if("function"==typeof e.scale)return e.scale.copy().domain(r).range(n);var o=function(e){if(null!=e){if(e in s)return s[e]();var t="scale".concat((0,nr.jC)(e));if(t in s)return s[t]()}}(t);if(null!=o){var i=o.domain(r).range(n);return(0,ne.zF)(i),i}}}var o_=(e,t,r)=>{var n=oc(t);return"auto"!==r&&"linear"!==r?void 0:null!=t&&t.tickCount&&Array.isArray(n)&&("auto"===n[0]||"auto"===n[1])&&no(e)?nP(e,t.tickCount,t.allowDecimals):null!=t&&t.tickCount&&"number"===t.type&&no(e)?nE(e,t.tickCount,t.allowDecimals):void 0},oI=(0,d.P1)([oA,nH,oD],o_),oR=(e,t,r,n)=>"angleAxis"!==n&&(null==e?void 0:e.type)==="number"&&no(t)&&Array.isArray(r)&&r.length>0?[Math.min(t[0],r[0]),Math.max(t[1],r[r.length-1])]:t,oL=(0,d.P1)([n$,oA,oI,nN.z],oR),oz=(0,d.P1)(n9,n$,(e,t)=>{if(t&&"number"===t.type){var r=1/0,n=Array.from(n8(e.map(e=>e.value))).sort((e,t)=>e-t);if(n.length<2)return 1/0;var o=n[n.length-1]-n[0];if(0===o)return 1/0;for(var i=0;i<n.length-1;i++)r=Math.min(r,n[i+1]-n[i]);return r/o}}),oW=(0,d.P1)(oz,r8.rE,nT.sd,nC.bX,(e,t,r,n)=>n,(e,t,r,n,o)=>{if(!(0,nn.n)(e))return 0;var i="vertical"===t?n.height:n.width;if("gap"===o)return e*i/2;if("no-gap"===o){var a=(0,nr.h1)(r,e*i),l=e*i/2;return l-a-(l-a)/i*a}return 0}),oF=(0,d.P1)(nB,(e,t)=>{var r=nB(e,t);return null==r||"string"!=typeof r.padding?0:oW(e,"xAxis",t,r.padding)},(e,t)=>{if(null==e)return{left:0,right:0};var r,n,{padding:o}=e;return"string"==typeof o?{left:t,right:t}:{left:(null!==(r=o.left)&&void 0!==r?r:0)+t,right:(null!==(n=o.right)&&void 0!==n?n:0)+t}}),oB=(0,d.P1)(nV,(e,t)=>{var r=nV(e,t);return null==r||"string"!=typeof r.padding?0:oW(e,"yAxis",t,r.padding)},(e,t)=>{if(null==e)return{top:0,bottom:0};var r,n,{padding:o}=e;return"string"==typeof o?{top:t,bottom:t}:{top:(null!==(r=o.top)&&void 0!==r?r:0)+t,bottom:(null!==(n=o.bottom)&&void 0!==n?n:0)+t}}),oK=(0,d.P1)([nC.bX,oF,nA.V,nA.F,(e,t,r)=>r],(e,t,r,n,o)=>{var{padding:i}=n;return o?[i.left,r.width-i.right]:[e.left+t.left,e.left+e.width-t.right]}),oV=(0,d.P1)([nC.bX,r8.rE,oB,nA.V,nA.F,(e,t,r)=>r],(e,t,r,n,o,i)=>{var{padding:a}=o;return i?[n.height-a.bottom,a.top]:"horizontal"===t?[e.top+e.height-r.bottom,e.top+r.top]:[e.top+r.top,e.top+e.height-r.bottom]}),oZ=(e,t,r,n)=>{var o;switch(t){case"xAxis":return oK(e,r,n);case"yAxis":return oV(e,r,n);case"zAxis":return null===(o=nU(e,r))||void 0===o?void 0:o.range;case"angleAxis":return(0,nD.bH)(e);case"radiusAxis":return(0,nD.s9)(e,r);default:return}},oU=(0,d.P1)([n$,oZ],nI.$),o$=(0,d.P1)([n$,oD,oL,oU],oN);function oH(e,t){return e.id<t.id?-1:e.id>t.id?1:0}(0,d.P1)(nJ,nN.z,(e,t)=>e.flatMap(e=>{var t;return null!==(t=e.errorBars)&&void 0!==t?t:[]}).filter(e=>n7(t,e)));var oY=(e,t)=>t,oq=(e,t,r)=>r,oX=(0,d.P1)(nS.X,oY,oq,(e,t,r)=>e.filter(e=>e.orientation===t).filter(e=>e.mirror===r).sort(oH)),oG=(0,d.P1)(nS.Z,oY,oq,(e,t,r)=>e.filter(e=>e.orientation===t).filter(e=>e.mirror===r).sort(oH)),oQ=(e,t)=>({width:e.width,height:t.height}),oJ=(e,t)=>({width:"number"==typeof t.width?t.width:nR.n9,height:e.height}),o0=(0,d.P1)(nC.bX,nB,oQ),o1=(e,t,r)=>{switch(t){case"top":return e.top;case"bottom":return r-e.bottom;default:return 0}},o2=(e,t,r)=>{switch(t){case"left":return e.left;case"right":return r-e.right;default:return 0}},o6=(0,d.P1)(nM.d_,nC.bX,oX,oY,oq,(e,t,r,n,o)=>{var i,a={};return r.forEach(r=>{var l=oQ(t,r);null==i&&(i=o1(t,n,e));var u="top"===n&&!o||"bottom"===n&&o;a[r.id]=i-Number(u)*l.height,i+=(u?-1:1)*l.height}),a}),o5=(0,d.P1)(nM.RD,nC.bX,oG,oY,oq,(e,t,r,n,o)=>{var i,a={};return r.forEach(r=>{var l=oJ(t,r);null==i&&(i=o2(t,n,e));var u="left"===n&&!o||"right"===n&&o;a[r.id]=i-Number(u)*l.width,i+=(u?-1:1)*l.width}),a}),o3=(e,t)=>{var r=(0,nC.bX)(e),n=nB(e,t);if(null!=n){var o=o6(e,n.orientation,n.mirror)[t];return null==o?{x:r.left,y:0}:{x:r.left,y:o}}},o4=(e,t)=>{var r=(0,nC.bX)(e),n=nV(e,t);if(null!=n){var o=o5(e,n.orientation,n.mirror)[t];return null==o?{x:0,y:r.top}:{x:o,y:r.top}}},o9=(0,d.P1)(nC.bX,nV,(e,t)=>({width:"number"==typeof t.width?t.width:nR.n9,height:e.height})),o7=(e,t,r)=>{switch(t){case"xAxis":return o0(e,r).width;case"yAxis":return o9(e,r).height;default:return}},o8=(e,t,r,n)=>{if(null!=r){var{allowDuplicatedCategory:o,type:i,dataKey:a}=r,l=(0,ne.NA)(e,n),u=t.map(e=>e.value);if(a&&l&&"category"===i&&o&&(0,nr.bv)(u))return u}},ie=(0,d.P1)([r8.rE,n9,n$,nN.z],o8),it=(e,t,r,n)=>{if(null!=r&&null!=r.dataKey){var{type:o,scale:i}=r;if((0,ne.NA)(e,n)&&("number"===o||"auto"!==i))return t.map(e=>e.value)}},ir=(0,d.P1)([r8.rE,n9,nH,nN.z],it),io=(0,d.P1)([r8.rE,(e,t,r)=>{switch(t){case"xAxis":return nB(e,r);case"yAxis":return nV(e,r);default:throw Error("Unexpected axis type: ".concat(t))}},oD,o$,ie,ir,oZ,oI,nN.z],(e,t,r,n,o,i,a,l,u)=>{if(null==t)return null;var c=(0,ne.NA)(e,u);return{angle:t.angle,interval:t.interval,minTickGap:t.minTickGap,orientation:t.orientation,tick:t.tick,tickCount:t.tickCount,tickFormatter:t.tickFormatter,ticks:t.ticks,type:t.type,unit:t.unit,axisType:u,categoricalDomain:i,duplicateDomain:o,isCategorical:c,niceTicks:l,range:a,realScaleType:r,scale:n}}),ii=(0,d.P1)([r8.rE,nH,oD,o$,oI,oZ,ie,ir,nN.z],(e,t,r,n,o,i,a,l,u)=>{if(null!=t&&null!=n){var c=(0,ne.NA)(e,u),{type:s,ticks:d,tickCount:f}=t,p="scaleBand"===r&&"function"==typeof n.bandwidth?n.bandwidth()/2:2,h="category"===s&&n.bandwidth?n.bandwidth()/p:0;h="angleAxis"===u&&null!=i&&i.length>=2?2*(0,nr.uY)(i[0]-i[1])*h:h;var v=d||o;return v?v.map((e,t)=>({index:t,coordinate:n(a?a.indexOf(e):e)+h,value:e,offset:h})).filter(e=>!(0,nr.In)(e.coordinate)):c&&l?l.map((e,t)=>({coordinate:n(e)+h,value:e,index:t,offset:h})):n.ticks?n.ticks(f).map(e=>({coordinate:n(e)+h,value:e,offset:h})):n.domain().map((e,t)=>({coordinate:n(e)+h,value:a?a[e]:e,index:t,offset:h}))}}),ia=(0,d.P1)([r8.rE,nH,o$,oZ,ie,ir,nN.z],(e,t,r,n,o,i,a)=>{if(null!=t&&null!=r&&null!=n&&n[0]!==n[1]){var l=(0,ne.NA)(e,a),{tickCount:u}=t,c=0;return(c="angleAxis"===a&&(null==n?void 0:n.length)>=2?2*(0,nr.uY)(n[0]-n[1])*c:c,l&&i)?i.map((e,t)=>({coordinate:r(e)+c,value:e,index:t,offset:c})):r.ticks?r.ticks(u).map(e=>({coordinate:r(e)+c,value:e,offset:c})):r.domain().map((e,t)=>({coordinate:r(e)+c,value:o?o[e]:e,index:t,offset:c}))}}),il=(0,d.P1)(n$,o$,(e,t)=>{if(null!=e&&null!=t)return nz(nz({},e),{},{scale:t})}),iu=(0,d.P1)([n$,oD,oA,oU],oN);(0,d.P1)((e,t,r)=>nU(e,r),iu,(e,t)=>{if(null!=e&&null!=t)return nz(nz({},e),{},{scale:t})});var ic=(0,d.P1)([r8.rE,nS.X,nS.Z],(e,t,r)=>{switch(e){case"horizontal":return t.some(e=>e.reversed)?"right-to-left":"left-to-right";case"vertical":return r.some(e=>e.reversed)?"bottom-to-top":"top-to-bottom";case"centric":case"radial":return"left-to-right";default:return}})},2431:function(e,t,r){"use strict";r.d(t,{F:function(){return l},V:function(){return u}});var n=r(92713),o=r(36289),i=r(60152),a=r(16630),l=e=>e.brush,u=(0,n.P1)([l,o.bX,i.lr],(e,t,r)=>({height:e.height,x:(0,a.hj)(e.x)?e.x:t.left,y:(0,a.hj)(e.y)?e.y:t.top+t.height+t.brushBottom-((null==r?void 0:r.bottom)||0),width:(0,a.hj)(e.width)?e.width:t.width}))},3838:function(e,t,r){"use strict";r.d(t,{b:function(){return o}});var n=r(16630),o=(e,t)=>{var r,o=Number(t);if(!(0,n.In)(o)&&null!=t)return o>=0?null==e||null===(r=e[o])||void 0===r?void 0:r.value:void 0}},6064:function(e,t,r){"use strict";r.d(t,{p:function(){return o}});var n=r(66395),o=(e,t)=>{var r=null==e?void 0:e.index;if(null==r)return null;var o=Number(r);if(!(0,n.n)(o))return r;var i=Infinity;return t.length>0&&(i=t.length-1),String(Math.max(0,Math.min(o,i)))}},87367:function(e,t,r){"use strict";r.d(t,{$:function(){return n}});var n=(e,t)=>e&&t?null!=e&&e.reversed?[t[1],t[0]]:t:void 0},8656:function(e,t,r){"use strict";r.d(t,{$:function(){return n}});var n=(e,t,r,n,o,i,a,l)=>{if(null!=i&&null!=l){var u=a[0],c=null==u?void 0:l(u.positions,i);if(null!=c)return c;var s=null==o?void 0:o[Number(i)];if(s)return"horizontal"===r?{x:s.coordinate,y:(n.top+t)/2}:{x:(n.left+e)/2,y:s.coordinate}}}},24597:function(e,t,r){"use strict";r.d(t,{k:function(){return a}});var n=r(64725);function o(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function i(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?o(Object(r),!0).forEach(function(t){var n,o;n=t,o=r[t],(n=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(n))in e?Object.defineProperty(e,n,{value:o,enumerable:!0,configurable:!0,writable:!0}):e[n]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):o(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var a=(e,t,r,o)=>{if(null==t)return n.UW;var a="axis"===t?"click"===r?e.axisInteraction.click:e.axisInteraction.hover:"click"===r?e.itemInteraction.click:e.itemInteraction.hover;if(null==a)return n.UW;if(a.active)return a;if(e.keyboardInteraction.active)return e.keyboardInteraction;if(e.syncInteraction.active&&null!=e.syncInteraction.index)return e.syncInteraction;var l=!0===e.settings.active;if(null!=a.index){if(l)return i(i({},a),{},{active:!0})}else if(null!=o)return{active:!0,coordinate:void 0,dataKey:void 0,index:o};return i(i({},n.UW),{},{coordinate:a.coordinate})}},89667:function(e,t,r){"use strict";r.d(t,{k:function(){return n}});var n=(e,t,r,n)=>{var o;return"axis"===t?e.tooltipItemPayloads:0===e.tooltipItemPayloads.length?[]:null==(o="hover"===r?e.itemInteraction.hover.dataKey:e.itemInteraction.click.dataKey)&&null!=n?[e.tooltipItemPayloads[0]]:e.tooltipItemPayloads.filter(e=>{var t;return(null===(t=e.settings)||void 0===t?void 0:t.dataKey)===o})}},60152:function(e,t,r){"use strict";r.d(t,{K$:function(){return i},RD:function(){return n},d_:function(){return o},lr:function(){return a}});var n=e=>e.layout.width,o=e=>e.layout.height,i=e=>e.layout.scale,a=e=>e.layout.margin},22932:function(e,t,r){"use strict";r.d(t,{RV:function(){return i},hA:function(){return a},iP:function(){return o}});var n=r(92713),o=e=>e.chartData,i=(0,n.P1)([o],e=>{var t=null!=e.chartData?e.chartData.length-1:0;return{chartData:e.chartData,computedData:e.computedData,dataEndIndex:t,dataStartIndex:0}}),a=(e,t,r,n)=>n?i(e):o(e)},56462:function(e,t,r){"use strict";r.d(t,{l:function(){return n}});var n=(e,t,r)=>r},40304:function(e,t,r){"use strict";r.d(t,{z:function(){return n}});var n=(e,t)=>t},80796:function(e,t,r){"use strict";r.d(t,{dc:function(){return y},bH:function(){return O},HW:function(){return k},Au:function(){return m},s9:function(){return j}});var n=r(92713),o=r(60152),i=r(36289),a=r(39206),l=r(16630),u="auto",c="auto",s=r(87367),d=r(35953),f={allowDataOverflow:!1,allowDecimals:!1,allowDuplicatedCategory:!1,dataKey:void 0,domain:void 0,id:0,includeHidden:!1,name:void 0,reversed:!1,scale:u,tick:!0,tickCount:void 0,ticks:void 0,type:"category",unit:void 0},p={allowDataOverflow:!1,allowDecimals:!1,allowDuplicatedCategory:!0,dataKey:void 0,domain:void 0,id:0,includeHidden:!1,name:void 0,reversed:!1,scale:c,tick:!0,tickCount:5,ticks:void 0,type:"number",unit:void 0},h={allowDataOverflow:!1,allowDecimals:!1,allowDuplicatedCategory:!0,dataKey:void 0,domain:void 0,id:0,includeHidden:!1,name:void 0,reversed:!1,scale:u,tick:!0,tickCount:void 0,ticks:void 0,type:"number",unit:void 0},v={allowDataOverflow:!1,allowDecimals:!1,allowDuplicatedCategory:!0,dataKey:void 0,domain:void 0,id:0,includeHidden:!1,name:void 0,reversed:!1,scale:c,tick:!0,tickCount:5,ticks:void 0,type:"category",unit:void 0},y=(e,t)=>null!=e.polarAxis.angleAxis[t]?e.polarAxis.angleAxis[t]:"radial"===e.layout.layoutType?h:f,m=(e,t)=>null!=e.polarAxis.radiusAxis[t]?e.polarAxis.radiusAxis[t]:"radial"===e.layout.layoutType?v:p,g=e=>e.polarOptions,b=(0,n.P1)([o.RD,o.d_,i.bX],a.$4),w=(0,n.P1)([g,b],(e,t)=>{if(null!=e)return(0,l.h1)(e.innerRadius,t,0)}),x=(0,n.P1)([g,b],(e,t)=>{if(null!=e)return(0,l.h1)(e.outerRadius,t,.8*t)}),O=(0,n.P1)([g],e=>{if(null==e)return[0,0];var{startAngle:t,endAngle:r}=e;return[t,r]});(0,n.P1)([y,O],s.$);var j=(0,n.P1)([b,w,x],(e,t,r)=>{if(null!=e&&null!=t&&null!=r)return[t,r]});(0,n.P1)([m,j],s.$);var k=(0,n.P1)([d.rE,g,w,x,o.RD,o.d_],(e,t,r,n,o,i)=>{if(("centric"===e||"radial"===e)&&null!=t&&null!=r&&null!=n){var{cx:a,cy:u,startAngle:c,endAngle:s}=t;return{cx:(0,l.h1)(a,o,o/2),cy:(0,l.h1)(u,i,i/2),innerRadius:r,outerRadius:n,startAngle:c,endAngle:s,clockWise:!1}}})},33968:function(e,t,r){"use strict";r.d(t,{E2:function(){return u},Qw:function(){return l},Sg:function(){return d},X8:function(){return a},Yd:function(){return c},b2:function(){return s},qy:function(){return n},sd:function(){return i},wK:function(){return o}});var n=e=>e.rootProps.maxBarSize,o=e=>e.rootProps.barGap,i=e=>e.rootProps.barCategoryGap,a=e=>e.rootProps.barSize,l=e=>e.rootProps.stackOffset,u=e=>e.options.chartName,c=e=>e.rootProps.syncId,s=e=>e.rootProps.syncMethod,d=e=>e.options.eventEmitter},63330:function(e,t,r){"use strict";r.d(t,{h:function(){return c}});var n=r(92713),o=r(35953),i=r(31944),a=r(36289),l=r(84461),u=r(80796),c=(0,n.P1)([(e,t)=>t,o.rE,u.HW,i.cS,i.PG,i.WQ,l.EM,a.bX],l.Nb)},32498:function(e,t,r){"use strict";r.d(t,{X:function(){return o},Z:function(){return i}});var n=r(92713),o=(0,n.P1)(e=>e.cartesianAxis.xAxis,e=>Object.values(e)),i=(0,n.P1)(e=>e.cartesianAxis.yAxis,e=>Object.values(e))},36289:function(e,t,r){"use strict";r.d(t,{zM:function(){return m},bX:function(){return v},nd:function(){return y}});var n=r(92713),o=r(15870),i=r.n(o),a=r(31104),l=r.n(a),u=e=>e.legend.settings;(0,n.P1)([e=>e.legend.payload,u],(e,t)=>{var{itemSorter:r}=t,n=e.flat(1);return r?l()(n,r):n});var c=r(49037),s=r(60152),d=r(32498),f=r(78487);function p(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function h(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?p(Object(r),!0).forEach(function(t){var n,o;n=t,o=r[t],(n=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(n))in e?Object.defineProperty(e,n,{value:o,enumerable:!0,configurable:!0,writable:!0}):e[n]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):p(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var v=(0,n.P1)([s.RD,s.d_,s.lr,e=>e.brush.height,d.X,d.Z,u,e=>e.legend.size],(e,t,r,n,o,a,l,u)=>{var s=a.reduce((e,t)=>{var{orientation:r}=t;if(!t.mirror&&!t.hide){var n="number"==typeof t.width?t.width:f.n9;return h(h({},e),{},{[r]:e[r]+n})}return e},{left:r.left||0,right:r.right||0}),d=o.reduce((e,t)=>{var{orientation:r}=t;return t.mirror||t.hide?e:h(h({},e),{},{[r]:i()(e,"".concat(r))+t.height})},{top:r.top||0,bottom:r.bottom||0}),p=h(h({},d),s),v=p.bottom;p.bottom+=n;var y=e-(p=(0,c.By)(p,l,u)).left-p.right,m=t-p.top-p.bottom;return h(h({brushBottom:v},p),{},{width:Math.max(y,0),height:Math.max(m,0)})}),y=(0,n.P1)(v,e=>({x:e.left,y:e.top,width:e.width,height:e.height})),m=(0,n.P1)(s.RD,s.d_,(e,t)=>({x:0,y:0,width:e,height:t}))},46302:function(e,t,r){"use strict";r.d(t,{Nb:function(){return i},SB:function(){return l},Y4:function(){return u},_j:function(){return a},iS:function(){return o}});var n=r(39040),o=e=>e.options.defaultTooltipEventType,i=e=>e.options.validateTooltipEventTypes;function a(e,t,r){if(null==e)return t;var n=e?"axis":"item";return null==r?t:r.includes(n)?n:t}function l(e,t){return a(t,o(e),i(e))}function u(e){return(0,n.C)(t=>l(t,e))}},73535:function(e,t,r){"use strict";r.d(t,{h:function(){return n}});var n=e=>e.options.tooltipPayloadSearcher},54803:function(e,t,r){"use strict";r.d(t,{M:function(){return n}});var n=e=>e.tooltip},84461:function(e,t,r){"use strict";r.d(t,{AC:function(){return k},EM:function(){return S},Nb:function(){return z},TT:function(){return R},ck:function(){return _},hA:function(){return N},i:function(){return I},oo:function(){return L},wi:function(){return T}});var n=r(92713),o=r(31104),i=r.n(o),a=r(39040),l=r(49037),u=r(16630),c=r(22932),s=r(31944),d=r(33968),f=r(35953),p=r(36289),h=r(60152),v=r(3838),y=r(24597),m=r(6064),g=r(8656),b=r(89667),w=r(73535),x=r(54803);function O(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function j(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?O(Object(r),!0).forEach(function(t){var n,o;n=t,o=r[t],(n=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(n))in e?Object.defineProperty(e,n,{value:o,enumerable:!0,configurable:!0,writable:!0}):e[n]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):O(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var k=()=>(0,a.C)(d.E2),P=(e,t)=>t,E=(e,t,r)=>r,M=(e,t,r,n)=>n,S=(0,n.P1)(s.WQ,e=>i()(e,e=>e.coordinate)),C=(0,n.P1)([x.M,P,E,M],y.k),A=(0,n.P1)([C,s.wQ],m.p),T=(e,t,r)=>{if(null!=t){var n=(0,x.M)(e);return"axis"===t?"hover"===r?n.axisInteraction.hover.dataKey:n.axisInteraction.click.dataKey:"hover"===r?n.itemInteraction.hover.dataKey:n.itemInteraction.click.dataKey}},D=(0,n.P1)([x.M,P,E,M],b.k),N=(0,n.P1)([h.RD,h.d_,f.rE,p.bX,s.WQ,M,D,w.h],g.$),_=(0,n.P1)([C,N],(e,t)=>{var r;return null!==(r=e.coordinate)&&void 0!==r?r:t}),I=(0,n.P1)(s.WQ,A,v.b),R=(0,n.P1)([D,A,c.iP,s.zv,I,w.h,P],(e,t,r,n,o,i,a)=>{if(null!=t&&null!=i){var{chartData:c,computedData:s,dataStartIndex:d,dataEndIndex:f}=r;return e.reduce((e,r)=>{var p,h,v,y,{dataDefinedOnItem:m,settings:g}=r,b=Array.isArray(p=null!=m?m:c)&&p&&d+f!==0?p.slice(d,f+1):p,w=null!==(h=null==g?void 0:g.dataKey)&&void 0!==h?h:null==n?void 0:n.dataKey,x=null==g?void 0:g.nameKey;return Array.isArray(v=null!=n&&n.dataKey&&!(null!=n&&n.allowDuplicatedCategory)&&Array.isArray(b)&&"axis"===a?(0,u.Ap)(b,n.dataKey,o):i(b,t,s,x))?v.forEach(t=>{var r=j(j({},g),{},{name:t.name,unit:t.unit,color:void 0,fill:void 0});e.push((0,l.eV)({tooltipEntrySettings:r,dataKey:t.dataKey,payload:t.payload,value:(0,l.F$)(t.payload,t.dataKey),name:t.name}))}):e.push((0,l.eV)({tooltipEntrySettings:g,dataKey:w,payload:v,value:(0,l.F$)(v,w),name:null!==(y=(0,l.F$)(v,x))&&void 0!==y?y:null==g?void 0:g.name})),e},[])}}),L=(0,n.P1)([C],e=>({isActive:e.active,activeIndex:e.index})),z=(e,t,r,n,o,i,a,u)=>{if(e&&t&&n&&o&&i){var c=(0,l.Z2)(e.chartX,e.chartY,t,r,u);if(c){var s=(0,l.HZ)(c,t),d=(0,l.VO)(s,a,i,n,o),f=(0,l.ep)(t,i,d,c);return{activeIndex:String(d),activeCoordinate:f}}}}},31944:function(e,t,r){"use strict";r.d(t,{i:function(){return eo},pI:function(){return eu},du:function(){return ei},Ve:function(){return en},oo:function(){return ec},zv:function(){return j},PG:function(){return Y},ri:function(){return q},WQ:function(){return Q},cS:function(){return x},wQ:function(){return C}});var n=r(92713),o=r(98628),i=r(35953),a=r(49037),l=r(22932),u=r(33968),c=r(16630),s=r(87367),d=r(46302),f=r(3838),p=r(24597),h=r(6064),v=r(8656),y=r(60152),m=r(36289),g=r(89667),b=r(73535),w=r(54803),x=e=>{var t=(0,i.rE)(e);return"horizontal"===t?"xAxis":"vertical"===t?"yAxis":"centric"===t?"angleAxis":"radiusAxis"},O=e=>e.tooltip.settings.axisId,j=e=>{var t=x(e),r=O(e);return(0,o.ub)(e,t,r)},k=(0,n.P1)([j,i.rE,o.Q4,u.E2,x],o.Yf),P=(0,n.P1)([e=>e.graphicalItems.cartesianItems,e=>e.graphicalItems.polarItems],(e,t)=>[...e,...t]),E=(0,n.P1)([x,O],o.YZ),M=(0,n.P1)([P,j,E],o.$B),S=(0,n.P1)([M],o.bU),C=(0,n.P1)([S,l.iP],o.tZ),A=(0,n.P1)([C,j,M],o.UA),T=(0,n.P1)([j],o.c4),D=(0,n.P1)([C,M,u.Qw],o.CK),N=(0,n.P1)([D,l.iP,x],o.dz),_=(0,n.P1)([M],o.Tk),I=(0,n.P1)([C,j,_,x],o.VQ),R=(0,n.P1)([o.RA,x,O],o.zW),L=(0,n.P1)([R,x],o.zX),z=(0,n.P1)([o.ww,x,O],o.zW),W=(0,n.P1)([z,x],o.yT),F=(0,n.P1)([o.xz,x,O],o.zW),B=(0,n.P1)([F,x],o.fY),K=(0,n.P1)([L,B,W],o.Eu),V=(0,n.P1)([j,T,N,I,K],o.E8),Z=(0,n.P1)([j,i.rE,C,A,u.Qw,x,V],o.l_),U=(0,n.P1)([Z,j,k],o.vb),$=(0,n.P1)([j,Z,U,x],o.kO),H=e=>{var t=x(e),r=O(e);return(0,o.Fn)(e,t,r,!1)},Y=(0,n.P1)([j,H],s.$),q=(0,n.P1)([j,k,$,Y],o.Jw),X=(0,n.P1)([i.rE,A,j,x],o.rC),G=(0,n.P1)([i.rE,A,j,x],o.UC),Q=(0,n.P1)([i.rE,j,k,q,H,X,G,x],(e,t,r,n,o,i,l,u)=>{if(t){var{type:s}=t,d=(0,a.NA)(e,u);if(n){var f="scaleBand"===r&&n.bandwidth?n.bandwidth()/2:2,p="category"===s&&n.bandwidth?n.bandwidth()/f:0;return(p="angleAxis"===u&&null!=o&&(null==o?void 0:o.length)>=2?2*(0,c.uY)(o[0]-o[1])*p:p,d&&l)?l.map((e,t)=>({coordinate:n(e)+p,value:e,index:t,offset:p})):n.domain().map((e,t)=>({coordinate:n(e)+p,value:i?i[e]:e,index:t,offset:p}))}}}),J=(0,n.P1)([d.iS,d.Nb,e=>e.tooltip.settings],(e,t,r)=>(0,d._j)(r.shared,e,t)),ee=e=>e.tooltip.settings.trigger,et=e=>e.tooltip.settings.defaultIndex,er=(0,n.P1)([w.M,J,ee,et],p.k),en=(0,n.P1)([er,C],h.p),eo=(0,n.P1)([Q,en],f.b),ei=(0,n.P1)([er],e=>{if(e)return e.dataKey}),ea=(0,n.P1)([w.M,J,ee,et],g.k),el=(0,n.P1)([y.RD,y.d_,i.rE,m.bX,Q,et,ea,b.h],v.$),eu=(0,n.P1)([er,el],(e,t)=>null!=e&&e.coordinate?e.coordinate:t),ec=(0,n.P1)([er],e=>e.active)},64725:function(e,t,r){"use strict";r.d(t,{$A:function(){return y},KN:function(){return m},KO:function(){return l},Kw:function(){return g},M1:function(){return s},O_:function(){return p},PD:function(){return c},Rr:function(){return h},UW:function(){return i},Vg:function(){return d},cK:function(){return u},eB:function(){return v},ne:function(){return f}});var n=r(39129),o=r(10418),i={active:!1,index:null,dataKey:void 0,coordinate:void 0},a=(0,n.oM)({name:"tooltip",initialState:{itemInteraction:{click:i,hover:i},axisInteraction:{click:i,hover:i},keyboardInteraction:i,syncInteraction:{active:!1,index:null,dataKey:void 0,label:void 0,coordinate:void 0},tooltipItemPayloads:[],settings:{shared:void 0,trigger:"hover",axisId:0,active:!1,defaultIndex:void 0}},reducers:{addTooltipEntrySettings(e,t){e.tooltipItemPayloads.push((0,o.cA)(t.payload))},removeTooltipEntrySettings(e,t){var r=(0,o.Vk)(e).tooltipItemPayloads.indexOf((0,o.cA)(t.payload));r>-1&&e.tooltipItemPayloads.splice(r,1)},setTooltipSettingsState(e,t){e.settings=t.payload},setActiveMouseOverItemIndex(e,t){e.syncInteraction.active=!1,e.keyboardInteraction.active=!1,e.itemInteraction.hover.active=!0,e.itemInteraction.hover.index=t.payload.activeIndex,e.itemInteraction.hover.dataKey=t.payload.activeDataKey,e.itemInteraction.hover.coordinate=t.payload.activeCoordinate},mouseLeaveChart(e){e.itemInteraction.hover.active=!1,e.axisInteraction.hover.active=!1},mouseLeaveItem(e){e.itemInteraction.hover.active=!1},setActiveClickItemIndex(e,t){e.syncInteraction.active=!1,e.itemInteraction.click.active=!0,e.keyboardInteraction.active=!1,e.itemInteraction.click.index=t.payload.activeIndex,e.itemInteraction.click.dataKey=t.payload.activeDataKey,e.itemInteraction.click.coordinate=t.payload.activeCoordinate},setMouseOverAxisIndex(e,t){e.syncInteraction.active=!1,e.axisInteraction.hover.active=!0,e.keyboardInteraction.active=!1,e.axisInteraction.hover.index=t.payload.activeIndex,e.axisInteraction.hover.dataKey=t.payload.activeDataKey,e.axisInteraction.hover.coordinate=t.payload.activeCoordinate},setMouseClickAxisIndex(e,t){e.syncInteraction.active=!1,e.keyboardInteraction.active=!1,e.axisInteraction.click.active=!0,e.axisInteraction.click.index=t.payload.activeIndex,e.axisInteraction.click.dataKey=t.payload.activeDataKey,e.axisInteraction.click.coordinate=t.payload.activeCoordinate},setSyncInteraction(e,t){e.syncInteraction=t.payload},setKeyboardInteraction(e,t){e.keyboardInteraction.active=t.payload.active,e.keyboardInteraction.index=t.payload.activeIndex,e.keyboardInteraction.coordinate=t.payload.activeCoordinate,e.keyboardInteraction.dataKey=t.payload.activeDataKey}}}),{addTooltipEntrySettings:l,removeTooltipEntrySettings:u,setTooltipSettingsState:c,setActiveMouseOverItemIndex:s,mouseLeaveItem:d,mouseLeaveChart:f,setActiveClickItemIndex:p,setMouseOverAxisIndex:h,setMouseClickAxisIndex:v,setSyncInteraction:y,setKeyboardInteraction:m}=a.actions,g=a.reducer},83061:function(e,t,r){"use strict";r.d(t,{$:function(){return h},x:function(){return v}});var n=r(39129),o=r(64725),i=r(63330),a=r(13169),l=r(46302),u=r(78487),c=r(92713),s=r(73535),d=r(54803),f=(0,c.P1)([d.M],e=>e.tooltipItemPayloads),p=(0,c.P1)([f,s.h,(e,t,r)=>t,(e,t,r)=>r],(e,t,r,n)=>{var o=e.find(e=>e.settings.dataKey===n);if(null!=o){var{positions:i}=o;if(null!=i)return t(i,r)}}),h=(0,n.PH)("touchMove"),v=(0,n.e)();v.startListening({actionCreator:h,effect:(e,t)=>{var r=e.payload,n=t.getState(),c=(0,l.SB)(n,n.tooltip.settings.shared);if("axis"===c){var s=(0,i.h)(n,(0,a.a)({clientX:r.touches[0].clientX,clientY:r.touches[0].clientY,currentTarget:r.currentTarget}));(null==s?void 0:s.activeIndex)!=null&&t.dispatch((0,o.Rr)({activeIndex:s.activeIndex,activeDataKey:void 0,activeCoordinate:s.activeCoordinate}))}else if("item"===c){var d,f=r.touches[0],h=document.elementFromPoint(f.clientX,f.clientY);if(!h||!h.getAttribute)return;var v=h.getAttribute(u.Gh),y=null!==(d=h.getAttribute(u.aN))&&void 0!==d?d:void 0,m=p(t.getState(),v,y);t.dispatch((0,o.M1)({activeDataKey:y,activeIndex:v,activeCoordinate:m}))}}})},318:function(e,t,r){"use strict";r.d(t,{W9:function(){return m},Fg:function(){return g}});var n=r(2265),o=r(39040),i=r(33968),a=new(r(14631)),l="recharts.syncEvent.tooltip",u="recharts.syncEvent.brush",c=r(31057),s=r(64725),d=r(84461),f=r(31944);function p(e){return e.tooltip.syncInteraction}var h=r(35953),v=r(39173),y=()=>{};function m(){var e,t,r,d,p,m,g,b,w,x,O,j=(0,o.T)();(0,n.useEffect)(()=>{j((0,c.IC)())},[j]),e=(0,o.C)(i.Yd),t=(0,o.C)(i.Sg),r=(0,o.T)(),d=(0,o.C)(i.b2),p=(0,o.C)(f.WQ),m=(0,h.vn)(),g=(0,h.d2)(),b=(0,o.C)(e=>e.rootProps.className),(0,n.useEffect)(()=>{if(null==e)return y;var n=(n,o,i)=>{if(t!==i&&e===n){if("index"===d){r(o);return}if(null!=p){if("function"==typeof d){var a,l=d(p,{activeTooltipIndex:null==o.payload.index?void 0:Number(o.payload.index),isTooltipActive:o.payload.active,activeIndex:null==o.payload.index?void 0:Number(o.payload.index),activeLabel:o.payload.label,activeDataKey:o.payload.dataKey,activeCoordinate:o.payload.coordinate});a=p[l]}else"value"===d&&(a=p.find(e=>String(e.value)===o.payload.label));var{coordinate:u}=o.payload;if(null==a||!1===o.payload.active||null==u||null==g){r((0,s.$A)({active:!1,coordinate:void 0,dataKey:void 0,index:null,label:void 0}));return}var{x:c,y:f}=u,h=Math.min(c,g.x+g.width),v=Math.min(f,g.y+g.height),y={x:"horizontal"===m?a.coordinate:h,y:"horizontal"===m?v:a.coordinate};r((0,s.$A)({active:o.payload.active,coordinate:y,dataKey:o.payload.dataKey,index:String(a.index),label:o.payload.label}))}}};return a.on(l,n),()=>{a.off(l,n)}},[b,r,t,e,d,p,m,g]),w=(0,o.C)(i.Yd),x=(0,o.C)(i.Sg),O=(0,o.T)(),(0,n.useEffect)(()=>{if(null==w)return y;var e=(e,t,r)=>{x!==r&&w===e&&O((0,v.t0)(t))};return a.on(u,e),()=>{a.off(u,e)}},[O,x,w])}function g(e,t,r,u,c,f){var h=(0,o.C)(r=>(0,d.wi)(r,e,t)),v=(0,o.C)(i.Sg),y=(0,o.C)(i.Yd),m=(0,o.C)(i.b2),g=(0,o.C)(p),b=null==g?void 0:g.active;(0,n.useEffect)(()=>{if(!b&&null!=y&&null!=v){var e=(0,s.$A)({active:f,coordinate:r,dataKey:h,index:c,label:"number"==typeof u?String(u):u});a.emit(l,y,e,v)}},[b,r,h,c,u,v,y,m,f])}},80503:function(e,t,r){"use strict";r.d(t,{b:function(){return q}});var n=r(2265),o=r(46802),i=r.n(o),a=r(73649),l=r(61994),u=r(82944),c=r(40130),s=r(46595);function d(){return(d=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}var f=(e,t,r,n,o)=>{var i=r-n;return"M ".concat(e,",").concat(t)+"L ".concat(e+r,",").concat(t)+"L ".concat(e+r-i/2,",").concat(t+o)+"L ".concat(e+r-i/2-n,",").concat(t+o)+"L ".concat(e,",").concat(t," Z")},p={x:0,y:0,upperWidth:0,lowerWidth:0,height:0,isUpdateAnimationActive:!1,animationBegin:0,animationDuration:1500,animationEasing:"ease"},h=e=>{var t=(0,c.j)(e,p),r=(0,n.useRef)(),[o,i]=(0,n.useState)(-1);(0,n.useEffect)(()=>{if(r.current&&r.current.getTotalLength)try{var e=r.current.getTotalLength();e&&i(e)}catch(e){}},[]);var{x:a,y:h,upperWidth:v,lowerWidth:y,height:m,className:g}=t,{animationEasing:b,animationDuration:w,animationBegin:x,isUpdateAnimationActive:O}=t;if(a!==+a||h!==+h||v!==+v||y!==+y||m!==+m||0===v&&0===y||0===m)return null;var j=(0,l.W)("recharts-trapezoid",g);return O?n.createElement(s.r,{canBegin:o>0,from:{upperWidth:0,lowerWidth:0,height:m,x:a,y:h},to:{upperWidth:v,lowerWidth:y,height:m,x:a,y:h},duration:w,animationEasing:b,isActive:O},e=>{var{upperWidth:i,lowerWidth:a,height:l,x:c,y:p}=e;return n.createElement(s.r,{canBegin:o>0,from:"0px ".concat(-1===o?1:o,"px"),to:"".concat(o,"px 0px"),attributeName:"strokeDasharray",begin:x,duration:w,easing:b},n.createElement("path",d({},(0,u.L6)(t,!0),{className:j,d:f(c,p,i,a,l),ref:r})))}):n.createElement("g",null,n.createElement("path",d({},(0,u.L6)(t,!0),{className:j,d:f(a,h,v,y,m)})))},v=r(60474),y=r(9841);let m=Math.cos,g=Math.sin,b=Math.sqrt,w=Math.PI,x=2*w;var O={draw(e,t){let r=b(t/w);e.moveTo(r,0),e.arc(0,0,r,0,x)}};let j=b(1/3),k=2*j,P=g(w/10)/g(7*w/10),E=g(x/10)*P,M=-m(x/10)*P,S=b(3),C=b(3)/2,A=1/b(12),T=(A/2+1)*3;var D=r(76115),N=r(67790);b(3),b(3);var _=r(16630),I=["type","size","sizeType"];function R(){return(R=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function L(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function z(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?L(Object(r),!0).forEach(function(t){var n,o;n=t,o=r[t],(n=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(n))in e?Object.defineProperty(e,n,{value:o,enumerable:!0,configurable:!0,writable:!0}):e[n]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):L(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var W={symbolCircle:O,symbolCross:{draw(e,t){let r=b(t/5)/2;e.moveTo(-3*r,-r),e.lineTo(-r,-r),e.lineTo(-r,-3*r),e.lineTo(r,-3*r),e.lineTo(r,-r),e.lineTo(3*r,-r),e.lineTo(3*r,r),e.lineTo(r,r),e.lineTo(r,3*r),e.lineTo(-r,3*r),e.lineTo(-r,r),e.lineTo(-3*r,r),e.closePath()}},symbolDiamond:{draw(e,t){let r=b(t/k),n=r*j;e.moveTo(0,-r),e.lineTo(n,0),e.lineTo(0,r),e.lineTo(-n,0),e.closePath()}},symbolSquare:{draw(e,t){let r=b(t),n=-r/2;e.rect(n,n,r,r)}},symbolStar:{draw(e,t){let r=b(.8908130915292852*t),n=E*r,o=M*r;e.moveTo(0,-r),e.lineTo(n,o);for(let t=1;t<5;++t){let i=x*t/5,a=m(i),l=g(i);e.lineTo(l*r,-a*r),e.lineTo(a*n-l*o,l*n+a*o)}e.closePath()}},symbolTriangle:{draw(e,t){let r=-b(t/(3*S));e.moveTo(0,2*r),e.lineTo(-S*r,-r),e.lineTo(S*r,-r),e.closePath()}},symbolWye:{draw(e,t){let r=b(t/T),n=r/2,o=r*A,i=r*A+r,a=-n;e.moveTo(n,o),e.lineTo(n,i),e.lineTo(a,i),e.lineTo(-.5*n-C*o,C*n+-.5*o),e.lineTo(-.5*n-C*i,C*n+-.5*i),e.lineTo(-.5*a-C*i,C*a+-.5*i),e.lineTo(-.5*n+C*o,-.5*o-C*n),e.lineTo(-.5*n+C*i,-.5*i-C*n),e.lineTo(-.5*a+C*i,-.5*i-C*a),e.closePath()}}},F=Math.PI/180,B=e=>W["symbol".concat((0,_.jC)(e))]||O,K=(e,t,r)=>{if("area"===t)return e;switch(r){case"cross":return 5*e*e/9;case"diamond":return .5*e*e/Math.sqrt(3);case"square":return e*e;case"star":var n=18*F;return 1.25*e*e*(Math.tan(n)-Math.tan(2*n)*Math.tan(n)**2);case"triangle":return Math.sqrt(3)*e*e/4;case"wye":return(21-10*Math.sqrt(3))*e*e/8;default:return Math.PI*e*e/4}},V=e=>{var t,{type:r="circle",size:o=64,sizeType:i="area"}=e,a=z(z({},function(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}(e,I)),{},{type:r,size:o,sizeType:i}),{className:c,cx:s,cy:d}=a,f=(0,u.L6)(a,!0);return s===+s&&d===+d&&o===+o?n.createElement("path",R({},f,{className:(0,l.W)("recharts-symbols",c),transform:"translate(".concat(s,", ").concat(d,")"),d:(t=B(r),(function(e,t){let r=null,n=(0,N.d)(o);function o(){let o;if(r||(r=o=n()),e.apply(this,arguments).draw(r,+t.apply(this,arguments)),o)return r=null,o+""||null}return e="function"==typeof e?e:(0,D.Z)(e||O),t="function"==typeof t?t:(0,D.Z)(void 0===t?64:+t),o.type=function(t){return arguments.length?(e="function"==typeof t?t:(0,D.Z)(t),o):e},o.size=function(e){return arguments.length?(t="function"==typeof e?e:(0,D.Z)(+e),o):t},o.context=function(e){return arguments.length?(r=null==e?null:e,o):r},o})().type(t).size(K(o,i,r))())})):null};V.registerSymbol=(e,t)=>{W["symbol".concat((0,_.jC)(e))]=t};var Z=["option","shapeType","propTransformer","activeClassName","isActive"];function U(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function $(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?U(Object(r),!0).forEach(function(t){var n,o;n=t,o=r[t],(n=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(n))in e?Object.defineProperty(e,n,{value:o,enumerable:!0,configurable:!0,writable:!0}):e[n]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):U(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function H(e,t){return $($({},t),e)}function Y(e){var{shapeType:t,elementProps:r}=e;switch(t){case"rectangle":return n.createElement(a.A,r);case"trapezoid":return n.createElement(h,r);case"sector":return n.createElement(v.L,r);case"symbols":if("symbols"===t)return n.createElement(V,r);break;default:return null}}function q(e){var t,{option:r,shapeType:o,propTransformer:a=H,activeClassName:l="recharts-active-shape",isActive:u}=e,c=function(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}(e,Z);if((0,n.isValidElement)(r))t=(0,n.cloneElement)(r,$($({},c),(0,n.isValidElement)(r)?r.props:r));else if("function"==typeof r)t=r(c);else if(i()(r)&&"boolean"!=typeof r){var s=a(r,c);t=n.createElement(Y,{shapeType:o,elementProps:s})}else t=n.createElement(Y,{shapeType:o,elementProps:c});return u?n.createElement(y.m,{className:l},t):t}},49037:function(e,t,r){"use strict";r.d(t,{Ji:function(){return _},rI:function(){return N},By:function(){return b},VO:function(){return g},HZ:function(){return F},zF:function(){return j},ep:function(){return W},zT:function(){return I},Yj:function(){return C},Fy:function(){return S},Rf:function(){return x},EB:function(){return D},GA:function(){return M},uX:function(){return E},uY:function(){return O},eV:function(){return R},hn:function(){return L},F$:function(){return m},Z2:function(){return z},NA:function(){return w},Vv:function(){return k}});var n=r(31104),o=r.n(n),i=r(15870),a=r.n(i);function l(e,t){if((o=e.length)>1)for(var r,n,o,i=1,a=e[t[0]],l=a.length;i<o;++i)for(n=a,a=e[t[i]],r=0;r<l;++r)a[r][1]+=a[r][0]=isNaN(n[r][1])?n[r][0]:n[r][1]}var u=r(22516),c=r(76115);function s(e){for(var t=e.length,r=Array(t);--t>=0;)r[t]=t;return r}function d(e,t){return e[t]}function f(e){let t=[];return t.key=e,t}var p=r(16630),h=r(39206);function v(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function y(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?v(Object(r),!0).forEach(function(t){var n,o;n=t,o=r[t],(n=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(n))in e?Object.defineProperty(e,n,{value:o,enumerable:!0,configurable:!0,writable:!0}):e[n]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):v(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function m(e,t,r){return(0,p.Rw)(e)||(0,p.Rw)(t)?r:(0,p.P2)(t)?a()(e,t,r):"function"==typeof t?t(e):r}var g=(e,t,r,n,o)=>{var i,a=-1,l=null!==(i=null==t?void 0:t.length)&&void 0!==i?i:0;if(l<=1||null==e)return 0;if("angleAxis"===n&&null!=o&&1e-6>=Math.abs(Math.abs(o[1]-o[0])-360))for(var u=0;u<l;u++){var c=u>0?r[u-1].coordinate:r[l-1].coordinate,s=r[u].coordinate,d=u>=l-1?r[0].coordinate:r[u+1].coordinate,f=void 0;if((0,p.uY)(s-c)!==(0,p.uY)(d-s)){var h=[];if((0,p.uY)(d-s)===(0,p.uY)(o[1]-o[0])){f=d;var v=s+o[1]-o[0];h[0]=Math.min(v,(v+c)/2),h[1]=Math.max(v,(v+c)/2)}else{f=c;var y=d+o[1]-o[0];h[0]=Math.min(s,(y+s)/2),h[1]=Math.max(s,(y+s)/2)}var m=[Math.min(s,(f+s)/2),Math.max(s,(f+s)/2)];if(e>m[0]&&e<=m[1]||e>=h[0]&&e<=h[1]){({index:a}=r[u]);break}}else{var g=Math.min(c,d),b=Math.max(c,d);if(e>(g+s)/2&&e<=(b+s)/2){({index:a}=r[u]);break}}}else if(t){for(var w=0;w<l;w++)if(0===w&&e<=(t[w].coordinate+t[w+1].coordinate)/2||w>0&&w<l-1&&e>(t[w].coordinate+t[w-1].coordinate)/2&&e<=(t[w].coordinate+t[w+1].coordinate)/2||w===l-1&&e>(t[w].coordinate+t[w-1].coordinate)/2){({index:a}=t[w]);break}}return a},b=(e,t,r)=>{if(t&&r){var{width:n,height:o}=r,{align:i,verticalAlign:a,layout:l}=t;if(("vertical"===l||"horizontal"===l&&"middle"===a)&&"center"!==i&&(0,p.hj)(e[i]))return y(y({},e),{},{[i]:e[i]+(n||0)});if(("horizontal"===l||"vertical"===l&&"center"===i)&&"middle"!==a&&(0,p.hj)(e[a]))return y(y({},e),{},{[a]:e[a]+(o||0)})}return e},w=(e,t)=>"horizontal"===e&&"xAxis"===t||"vertical"===e&&"yAxis"===t||"centric"===e&&"angleAxis"===t||"radial"===e&&"radiusAxis"===t,x=(e,t,r,n)=>{if(n)return e.map(e=>e.coordinate);var o,i,a=e.map(e=>(e.coordinate===t&&(o=!0),e.coordinate===r&&(i=!0),e.coordinate));return o||a.push(t),i||a.push(r),a},O=(e,t,r)=>{if(!e)return null;var{duplicateDomain:n,type:o,range:i,scale:a,realScaleType:l,isCategorical:u,categoricalDomain:c,tickCount:s,ticks:d,niceTicks:f,axisType:h}=e;if(!a)return null;var v="scaleBand"===l&&a.bandwidth?a.bandwidth()/2:2,y=(t||r)&&"category"===o&&a.bandwidth?a.bandwidth()/v:0;return(y="angleAxis"===h&&i&&i.length>=2?2*(0,p.uY)(i[0]-i[1])*y:y,t&&(d||f))?(d||f||[]).map((e,t)=>({coordinate:a(n?n.indexOf(e):e)+y,value:e,offset:y,index:t})).filter(e=>!(0,p.In)(e.coordinate)):u&&c?c.map((e,t)=>({coordinate:a(e)+y,value:e,index:t,offset:y})):a.ticks&&!r&&null!=s?a.ticks(s).map((e,t)=>({coordinate:a(e)+y,value:e,offset:y,index:t})):a.domain().map((e,t)=>({coordinate:a(e)+y,value:n?n[e]:e,index:t,offset:y}))},j=e=>{var t=e.domain();if(t&&!(t.length<=2)){var r=t.length,n=e.range(),o=Math.min(n[0],n[1])-1e-4,i=Math.max(n[0],n[1])+1e-4,a=e(t[0]),l=e(t[r-1]);(a<o||a>i||l<o||l>i)&&e.domain([t[0],t[r-1]])}},k=(e,t)=>{if(!t||2!==t.length||!(0,p.hj)(t[0])||!(0,p.hj)(t[1]))return e;var r=Math.min(t[0],t[1]),n=Math.max(t[0],t[1]),o=[e[0],e[1]];return(!(0,p.hj)(e[0])||e[0]<r)&&(o[0]=r),(!(0,p.hj)(e[1])||e[1]>n)&&(o[1]=n),o[0]>n&&(o[0]=n),o[1]<r&&(o[1]=r),o},P={sign:e=>{var t=e.length;if(!(t<=0))for(var r=0,n=e[0].length;r<n;++r)for(var o=0,i=0,a=0;a<t;++a){var l=(0,p.In)(e[a][r][1])?e[a][r][0]:e[a][r][1];l>=0?(e[a][r][0]=o,e[a][r][1]=o+l,o=e[a][r][1]):(e[a][r][0]=i,e[a][r][1]=i+l,i=e[a][r][1])}},expand:function(e,t){if((n=e.length)>0){for(var r,n,o,i=0,a=e[0].length;i<a;++i){for(o=r=0;r<n;++r)o+=e[r][i][1]||0;if(o)for(r=0;r<n;++r)e[r][i][1]/=o}l(e,t)}},none:l,silhouette:function(e,t){if((r=e.length)>0){for(var r,n=0,o=e[t[0]],i=o.length;n<i;++n){for(var a=0,u=0;a<r;++a)u+=e[a][n][1]||0;o[n][1]+=o[n][0]=-u/2}l(e,t)}},wiggle:function(e,t){if((o=e.length)>0&&(n=(r=e[t[0]]).length)>0){for(var r,n,o,i=0,a=1;a<n;++a){for(var u=0,c=0,s=0;u<o;++u){for(var d=e[t[u]],f=d[a][1]||0,p=(f-(d[a-1][1]||0))/2,h=0;h<u;++h){var v=e[t[h]];p+=(v[a][1]||0)-(v[a-1][1]||0)}c+=f,s+=p*f}r[a-1][1]+=r[a-1][0]=i,c&&(i-=s/c)}r[a-1][1]+=r[a-1][0]=i,l(e,t)}},positive:e=>{var t=e.length;if(!(t<=0))for(var r=0,n=e[0].length;r<n;++r)for(var o=0,i=0;i<t;++i){var a=(0,p.In)(e[i][r][1])?e[i][r][0]:e[i][r][1];a>=0?(e[i][r][0]=o,e[i][r][1]=o+a,o=e[i][r][1]):(e[i][r][0]=0,e[i][r][1]=0)}}},E=(e,t,r)=>{var n=P[r];return(function(){var e=(0,c.Z)([]),t=s,r=l,n=d;function o(o){var i,a,l=Array.from(e.apply(this,arguments),f),c=l.length,s=-1;for(let e of o)for(i=0,++s;i<c;++i)(l[i][s]=[0,+n(e,l[i].key,s,o)]).data=e;for(i=0,a=(0,u.Z)(t(l));i<c;++i)l[a[i]].index=i;return r(l,a),l}return o.keys=function(t){return arguments.length?(e="function"==typeof t?t:(0,c.Z)(Array.from(t)),o):e},o.value=function(e){return arguments.length?(n="function"==typeof e?e:(0,c.Z)(+e),o):n},o.order=function(e){return arguments.length?(t=null==e?s:"function"==typeof e?e:(0,c.Z)(Array.from(e)),o):t},o.offset=function(e){return arguments.length?(r=null==e?l:e,o):r},o})().keys(t).value((e,t)=>+m(e,t,0)).order(s).offset(n)(e)};function M(e){return null==e?void 0:String(e)}var S=e=>{var{axis:t,ticks:r,offset:n,bandSize:o,entry:i,index:a}=e;if("category"===t.type)return r[a]?r[a].coordinate+n:null;var l=m(i,t.dataKey,t.scale.domain()[a]);return(0,p.Rw)(l)?null:t.scale(l)-o/2+n},C=e=>{var{numericAxis:t}=e,r=t.scale.domain();if("number"===t.type){var n=Math.min(r[0],r[1]),o=Math.max(r[0],r[1]);return n<=0&&o>=0?0:o<0?o:n}return r[0]},A=e=>{var t=e.flat(2).filter(p.hj);return[Math.min(...t),Math.max(...t)]},T=e=>[e[0]===1/0?0:e[0],e[1]===-1/0?0:e[1]],D=(e,t,r)=>{if(null!=e)return T(Object.keys(e).reduce((n,o)=>{var{stackedData:i}=e[o],a=i.reduce((e,n)=>{var o=A(n.slice(t,r+1));return[Math.min(e[0],o[0]),Math.max(e[1],o[1])]},[1/0,-1/0]);return[Math.min(a[0],n[0]),Math.max(a[1],n[1])]},[1/0,-1/0]))},N=/^dataMin[\s]*-[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,_=/^dataMax[\s]*\+[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,I=(e,t,r)=>{if(e&&e.scale&&e.scale.bandwidth){var n=e.scale.bandwidth();if(!r||n>0)return n}if(e&&t&&t.length>=2){for(var i=o()(t,e=>e.coordinate),a=1/0,l=1,u=i.length;l<u;l++){var c=i[l],s=i[l-1];a=Math.min((c.coordinate||0)-(s.coordinate||0),a)}return a===1/0?0:a}return r?void 0:0};function R(e){var{tooltipEntrySettings:t,dataKey:r,payload:n,value:o,name:i}=e;return y(y({},t),{},{dataKey:r,payload:n,value:o,name:i})}function L(e,t){return e?String(e):"string"==typeof t?t:void 0}function z(e,t,r,n,o){return"horizontal"===r||"vertical"===r?e>=o.left&&e<=o.left+o.width&&t>=o.top&&t<=o.top+o.height?{x:e,y:t}:null:n?(0,h.z3)({x:e,y:t},n):null}var W=(e,t,r,n)=>{var o=t.find(e=>e&&e.index===r);if(o){if("horizontal"===e)return{x:o.coordinate,y:n.y};if("vertical"===e)return{x:n.x,y:o.coordinate};if("centric"===e){var i=o.coordinate,{radius:a}=n;return y(y(y({},n),(0,h.op)(n.cx,n.cy,a,i)),{},{angle:i,radius:a})}var l=o.coordinate,{angle:u}=n;return y(y(y({},n),(0,h.op)(n.cx,n.cy,l,u)),{},{angle:u,radius:l})}return{x:0,y:0}},F=(e,t)=>"horizontal"===t?e.x:"vertical"===t?e.y:"centric"===t?e.angle:e.radius},78487:function(e,t,r){"use strict";r.d(t,{Gh:function(){return n},aN:function(){return o},n9:function(){return i}});var n="data-recharts-item-index",o="data-recharts-item-data-key",i=60},4094:function(e,t,r){"use strict";r.d(t,{x:function(){return c}});var n=r(34067);function o(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function i(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?o(Object(r),!0).forEach(function(t){var n,o;n=t,o=r[t],(n=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(n))in e?Object.defineProperty(e,n,{value:o,enumerable:!0,configurable:!0,writable:!0}):e[n]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):o(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var a={widthCache:{},cacheCount:0},l={position:"absolute",top:"-20000px",left:0,padding:0,margin:0,border:"none",whiteSpace:"pre"},u="recharts_measurement_span",c=function(e){var t,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(null==e||n.x.isSsr)return{width:0,height:0};var o=(Object.keys(t=i({},r)).forEach(e=>{t[e]||delete t[e]}),t),c=JSON.stringify({text:e,copyStyle:o});if(a.widthCache[c])return a.widthCache[c];try{var s=document.getElementById(u);s||((s=document.createElement("span")).setAttribute("id",u),s.setAttribute("aria-hidden","true"),document.body.appendChild(s));var d=i(i({},l),o);Object.assign(s.style,d),s.textContent="".concat(e);var f=s.getBoundingClientRect(),p={width:f.width,height:f.height};return a.widthCache[c]=p,++a.cacheCount>2e3&&(a.cacheCount=0,a.widthCache={}),p}catch(e){return{width:0,height:0}}}},16630:function(e,t,r){"use strict";r.d(t,{Ap:function(){return v},EL:function(){return d},In:function(){return a},P2:function(){return c},Rw:function(){return y},bv:function(){return p},h1:function(){return f},hU:function(){return l},hj:function(){return u},jC:function(){return m},k4:function(){return h},uY:function(){return i}});var n=r(15870),o=r.n(n),i=e=>0===e?0:e>0?1:-1,a=e=>"number"==typeof e&&e!=+e,l=e=>"string"==typeof e&&e.indexOf("%")===e.length-1,u=e=>("number"==typeof e||e instanceof Number)&&!a(e),c=e=>u(e)||"string"==typeof e,s=0,d=e=>{var t=++s;return"".concat(e||"").concat(t)},f=function(e,t){var r,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,o=arguments.length>3&&void 0!==arguments[3]&&arguments[3];if(!u(e)&&"string"!=typeof e)return n;if(l(e)){if(null==t)return n;var i=e.indexOf("%");r=t*parseFloat(e.slice(0,i))/100}else r=+e;return a(r)&&(r=n),o&&null!=t&&r>t&&(r=t),r},p=e=>{if(!Array.isArray(e))return!1;for(var t=e.length,r={},n=0;n<t;n++){if(r[e[n]])return!0;r[e[n]]=!0}return!1},h=(e,t)=>u(e)&&u(t)?r=>e+r*(t-e):()=>t;function v(e,t,r){if(e&&e.length)return e.find(e=>e&&("function"==typeof t?t(e):o()(e,t))===r)}var y=e=>null==e,m=e=>y(e)?e:"".concat(e.charAt(0).toUpperCase()).concat(e.slice(1))},34067:function(e,t,r){"use strict";r.d(t,{x:function(){return n}});var n={isSsr:!("undefined"!=typeof window&&window.document&&window.document.createElement&&window.setTimeout)}},1175:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});var n=function(e,t){for(var r=arguments.length,n=Array(r>2?r-2:0),o=2;o<r;o++)n[o-2]=arguments[o]}},39206:function(e,t,r){"use strict";function n(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function o(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?n(Object(r),!0).forEach(function(t){var n,o;n=t,o=r[t],(n=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(n))in e?Object.defineProperty(e,n,{value:o,enumerable:!0,configurable:!0,writable:!0}):e[n]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):n(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}r.d(t,{$4:function(){return u},Wk:function(){return i},op:function(){return l},z3:function(){return p}}),r(2265);var i=Math.PI/180,a=e=>180*e/Math.PI,l=(e,t,r,n)=>({x:e+Math.cos(-i*n)*r,y:t+Math.sin(-i*n)*r}),u=function(e,t){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{top:0,right:0,bottom:0,left:0};return Math.min(Math.abs(e-(r.left||0)-(r.right||0)),Math.abs(t-(r.top||0)-(r.bottom||0)))/2},c=(e,t)=>{var{x:r,y:n}=e,{x:o,y:i}=t;return Math.sqrt((r-o)**2+(n-i)**2)},s=(e,t)=>{var{x:r,y:n}=e,{cx:o,cy:i}=t,l=c({x:r,y:n},{x:o,y:i});if(l<=0)return{radius:l,angle:0};var u=Math.acos((r-o)/l);return n>i&&(u=2*Math.PI-u),{radius:l,angle:a(u),angleInRadian:u}},d=e=>{var{startAngle:t,endAngle:r}=e,n=Math.min(Math.floor(t/360),Math.floor(r/360));return{startAngle:t-360*n,endAngle:r-360*n}},f=(e,t)=>{var{startAngle:r,endAngle:n}=t;return e+360*Math.min(Math.floor(r/360),Math.floor(n/360))},p=(e,t)=>{var r,{x:n,y:i}=e,{radius:a,angle:l}=s({x:n,y:i},t),{innerRadius:u,outerRadius:c}=t;if(a<u||a>c||0===a)return null;var{startAngle:p,endAngle:h}=d(t),v=l;if(p<=h){for(;v>h;)v-=360;for(;v<p;)v+=360;r=v>=p&&v<=h}else{for(;v>p;)v-=360;for(;v<h;)v+=360;r=v>=h&&v<=p}return r?o(o({},t),{},{radius:a,angle:f(v,t)}):null}},82944:function(e,t,r){"use strict";r.d(t,{L6:function(){return v},NN:function(){return p}});var n=r(15870),o=r.n(n),i=r(2265),a=r(59679),l=r(16630),u=r(41637),c=e=>"string"==typeof e?e:e?e.displayName||e.name||"Component":"",s=null,d=null,f=e=>{if(e===s&&Array.isArray(d))return d;var t=[];return i.Children.forEach(e,e=>{(0,l.Rw)(e)||((0,a.M2)(e)?t=t.concat(f(e.props.children)):t.push(e))}),d=t,s=e,t};function p(e,t){var r=[],n=[];return n=Array.isArray(t)?t.map(e=>c(e)):[c(t)],f(e).forEach(e=>{var t=o()(e,"type.displayName")||o()(e,"type.name");-1!==n.indexOf(t)&&r.push(e)}),r}var h=(e,t,r,n)=>{var o,i=null!==(o=n&&(null===u.ry||void 0===u.ry?void 0:u.ry[n]))&&void 0!==o?o:[];return t.startsWith("data-")||"function"!=typeof e&&(n&&i.includes(t)||u.Yh.includes(t))||r&&u.nv.includes(t)},v=(e,t,r)=>{if(!e||"function"==typeof e||"boolean"==typeof e)return null;var n=e;if((0,i.isValidElement)(e)&&(n=e.props),"object"!=typeof n&&"function"!=typeof n)return null;var o={};return Object.keys(n).forEach(e=>{var i;h(null===(i=n)||void 0===i?void 0:i[e],e,t,r)&&(o[e]=n[e])}),o}},13169:function(e,t,r){"use strict";r.d(t,{a:function(){return n}});var n=e=>{var t=e.currentTarget.getBoundingClientRect(),r=t.width/e.currentTarget.offsetWidth,n=t.height/e.currentTarget.offsetHeight;return{chartX:Math.round((e.clientX-t.left)/r),chartY:Math.round((e.clientY-t.top)/n)}}},66395:function(e,t,r){"use strict";function n(e){return Number.isFinite(e)}function o(e){return"number"==typeof e&&e>0&&Number.isFinite(e)}r.d(t,{n:function(){return n},r:function(){return o}})},40130:function(e,t,r){"use strict";function n(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function o(e,t){var r=function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?n(Object(r),!0).forEach(function(t){var n,o;n=t,o=r[t],(n=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(n))in e?Object.defineProperty(e,n,{value:o,enumerable:!0,configurable:!0,writable:!0}):e[n]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):n(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}({},e);return Object.keys(t).reduce((e,r)=>(void 0===e[r]&&void 0!==t[r]&&(e[r]=t[r]),e),r)}r.d(t,{j:function(){return o}})},41637:function(e,t,r){"use strict";r.d(t,{Yh:function(){return o},Ym:function(){return u},bw:function(){return s},nv:function(){return l},ry:function(){return a}});var n=r(2265),o=["aria-activedescendant","aria-atomic","aria-autocomplete","aria-busy","aria-checked","aria-colcount","aria-colindex","aria-colspan","aria-controls","aria-current","aria-describedby","aria-details","aria-disabled","aria-errormessage","aria-expanded","aria-flowto","aria-haspopup","aria-hidden","aria-invalid","aria-keyshortcuts","aria-label","aria-labelledby","aria-level","aria-live","aria-modal","aria-multiline","aria-multiselectable","aria-orientation","aria-owns","aria-placeholder","aria-posinset","aria-pressed","aria-readonly","aria-relevant","aria-required","aria-roledescription","aria-rowcount","aria-rowindex","aria-rowspan","aria-selected","aria-setsize","aria-sort","aria-valuemax","aria-valuemin","aria-valuenow","aria-valuetext","className","color","height","id","lang","max","media","method","min","name","style","target","width","role","tabIndex","accentHeight","accumulate","additive","alignmentBaseline","allowReorder","alphabetic","amplitude","arabicForm","ascent","attributeName","attributeType","autoReverse","azimuth","baseFrequency","baselineShift","baseProfile","bbox","begin","bias","by","calcMode","capHeight","clip","clipPath","clipPathUnits","clipRule","colorInterpolation","colorInterpolationFilters","colorProfile","colorRendering","contentScriptType","contentStyleType","cursor","cx","cy","d","decelerate","descent","diffuseConstant","direction","display","divisor","dominantBaseline","dur","dx","dy","edgeMode","elevation","enableBackground","end","exponent","externalResourcesRequired","fill","fillOpacity","fillRule","filter","filterRes","filterUnits","floodColor","floodOpacity","focusable","fontFamily","fontSize","fontSizeAdjust","fontStretch","fontStyle","fontVariant","fontWeight","format","from","fx","fy","g1","g2","glyphName","glyphOrientationHorizontal","glyphOrientationVertical","glyphRef","gradientTransform","gradientUnits","hanging","horizAdvX","horizOriginX","href","ideographic","imageRendering","in2","in","intercept","k1","k2","k3","k4","k","kernelMatrix","kernelUnitLength","kerning","keyPoints","keySplines","keyTimes","lengthAdjust","letterSpacing","lightingColor","limitingConeAngle","local","markerEnd","markerHeight","markerMid","markerStart","markerUnits","markerWidth","mask","maskContentUnits","maskUnits","mathematical","mode","numOctaves","offset","opacity","operator","order","orient","orientation","origin","overflow","overlinePosition","overlineThickness","paintOrder","panose1","pathLength","patternContentUnits","patternTransform","patternUnits","pointerEvents","pointsAtX","pointsAtY","pointsAtZ","preserveAlpha","preserveAspectRatio","primitiveUnits","r","radius","refX","refY","renderingIntent","repeatCount","repeatDur","requiredExtensions","requiredFeatures","restart","result","rotate","rx","ry","seed","shapeRendering","slope","spacing","specularConstant","specularExponent","speed","spreadMethod","startOffset","stdDeviation","stemh","stemv","stitchTiles","stopColor","stopOpacity","strikethroughPosition","strikethroughThickness","string","stroke","strokeDasharray","strokeDashoffset","strokeLinecap","strokeLinejoin","strokeMiterlimit","strokeOpacity","strokeWidth","surfaceScale","systemLanguage","tableValues","targetX","targetY","textAnchor","textDecoration","textLength","textRendering","to","transform","u1","u2","underlinePosition","underlineThickness","unicode","unicodeBidi","unicodeRange","unitsPerEm","vAlphabetic","values","vectorEffect","version","vertAdvY","vertOriginX","vertOriginY","vHanging","vIdeographic","viewTarget","visibility","vMathematical","widths","wordSpacing","writingMode","x1","x2","x","xChannelSelector","xHeight","xlinkActuate","xlinkArcrole","xlinkHref","xlinkRole","xlinkShow","xlinkTitle","xlinkType","xmlBase","xmlLang","xmlns","xmlnsXlink","xmlSpace","y1","y2","y","yChannelSelector","z","zoomAndPan","ref","key","angle"],i=["points","pathLength"],a={svg:["viewBox","children"],polygon:i,polyline:i},l=["dangerouslySetInnerHTML","onCopy","onCopyCapture","onCut","onCutCapture","onPaste","onPasteCapture","onCompositionEnd","onCompositionEndCapture","onCompositionStart","onCompositionStartCapture","onCompositionUpdate","onCompositionUpdateCapture","onFocus","onFocusCapture","onBlur","onBlurCapture","onChange","onChangeCapture","onBeforeInput","onBeforeInputCapture","onInput","onInputCapture","onReset","onResetCapture","onSubmit","onSubmitCapture","onInvalid","onInvalidCapture","onLoad","onLoadCapture","onError","onErrorCapture","onKeyDown","onKeyDownCapture","onKeyPress","onKeyPressCapture","onKeyUp","onKeyUpCapture","onAbort","onAbortCapture","onCanPlay","onCanPlayCapture","onCanPlayThrough","onCanPlayThroughCapture","onDurationChange","onDurationChangeCapture","onEmptied","onEmptiedCapture","onEncrypted","onEncryptedCapture","onEnded","onEndedCapture","onLoadedData","onLoadedDataCapture","onLoadedMetadata","onLoadedMetadataCapture","onLoadStart","onLoadStartCapture","onPause","onPauseCapture","onPlay","onPlayCapture","onPlaying","onPlayingCapture","onProgress","onProgressCapture","onRateChange","onRateChangeCapture","onSeeked","onSeekedCapture","onSeeking","onSeekingCapture","onStalled","onStalledCapture","onSuspend","onSuspendCapture","onTimeUpdate","onTimeUpdateCapture","onVolumeChange","onVolumeChangeCapture","onWaiting","onWaitingCapture","onAuxClick","onAuxClickCapture","onClick","onClickCapture","onContextMenu","onContextMenuCapture","onDoubleClick","onDoubleClickCapture","onDrag","onDragCapture","onDragEnd","onDragEndCapture","onDragEnter","onDragEnterCapture","onDragExit","onDragExitCapture","onDragLeave","onDragLeaveCapture","onDragOver","onDragOverCapture","onDragStart","onDragStartCapture","onDrop","onDropCapture","onMouseDown","onMouseDownCapture","onMouseEnter","onMouseLeave","onMouseMove","onMouseMoveCapture","onMouseOut","onMouseOutCapture","onMouseOver","onMouseOverCapture","onMouseUp","onMouseUpCapture","onSelect","onSelectCapture","onTouchCancel","onTouchCancelCapture","onTouchEnd","onTouchEndCapture","onTouchMove","onTouchMoveCapture","onTouchStart","onTouchStartCapture","onPointerDown","onPointerDownCapture","onPointerMove","onPointerMoveCapture","onPointerUp","onPointerUpCapture","onPointerCancel","onPointerCancelCapture","onPointerEnter","onPointerEnterCapture","onPointerLeave","onPointerLeaveCapture","onPointerOver","onPointerOverCapture","onPointerOut","onPointerOutCapture","onGotPointerCapture","onGotPointerCaptureCapture","onLostPointerCapture","onLostPointerCaptureCapture","onScroll","onScrollCapture","onWheel","onWheelCapture","onAnimationStart","onAnimationStartCapture","onAnimationEnd","onAnimationEndCapture","onAnimationIteration","onAnimationIterationCapture","onTransitionEnd","onTransitionEndCapture"],u=(e,t)=>{if(!e||"function"==typeof e||"boolean"==typeof e)return null;var r=e;if((0,n.isValidElement)(e)&&(r=e.props),"object"!=typeof r&&"function"!=typeof r)return null;var o={};return Object.keys(r).forEach(e=>{l.includes(e)&&(o[e]=t||(t=>r[e](r,t)))}),o},c=(e,t,r)=>n=>(e(t,r,n),null),s=(e,t,r)=>{if(null===e||"object"!=typeof e&&"function"!=typeof e)return null;var n=null;return Object.keys(e).forEach(o=>{var i=e[o];l.includes(o)&&"function"==typeof i&&(n||(n={}),n[o]=c(i,t,r))}),n}},59087:function(e,t,r){"use strict";r.d(t,{i:function(){return i}});var n=r(2265),o=r(16630);function i(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"animation-",r=(0,n.useRef)((0,o.EL)(t)),i=(0,n.useRef)(e);return i.current!==e&&(r.current=(0,o.EL)(t),i.current=e),r.current}},14631:function(e){"use strict";var t=Object.prototype.hasOwnProperty,r="~";function n(){}function o(e,t,r){this.fn=e,this.context=t,this.once=r||!1}function i(e,t,n,i,a){if("function"!=typeof n)throw TypeError("The listener must be a function");var l=new o(n,i||e,a),u=r?r+t:t;return e._events[u]?e._events[u].fn?e._events[u]=[e._events[u],l]:e._events[u].push(l):(e._events[u]=l,e._eventsCount++),e}function a(e,t){0==--e._eventsCount?e._events=new n:delete e._events[t]}function l(){this._events=new n,this._eventsCount=0}Object.create&&(n.prototype=Object.create(null),new n().__proto__||(r=!1)),l.prototype.eventNames=function(){var e,n,o=[];if(0===this._eventsCount)return o;for(n in e=this._events)t.call(e,n)&&o.push(r?n.slice(1):n);return Object.getOwnPropertySymbols?o.concat(Object.getOwnPropertySymbols(e)):o},l.prototype.listeners=function(e){var t=r?r+e:e,n=this._events[t];if(!n)return[];if(n.fn)return[n.fn];for(var o=0,i=n.length,a=Array(i);o<i;o++)a[o]=n[o].fn;return a},l.prototype.listenerCount=function(e){var t=r?r+e:e,n=this._events[t];return n?n.fn?1:n.length:0},l.prototype.emit=function(e,t,n,o,i,a){var l=r?r+e:e;if(!this._events[l])return!1;var u,c,s=this._events[l],d=arguments.length;if(s.fn){switch(s.once&&this.removeListener(e,s.fn,void 0,!0),d){case 1:return s.fn.call(s.context),!0;case 2:return s.fn.call(s.context,t),!0;case 3:return s.fn.call(s.context,t,n),!0;case 4:return s.fn.call(s.context,t,n,o),!0;case 5:return s.fn.call(s.context,t,n,o,i),!0;case 6:return s.fn.call(s.context,t,n,o,i,a),!0}for(c=1,u=Array(d-1);c<d;c++)u[c-1]=arguments[c];s.fn.apply(s.context,u)}else{var f,p=s.length;for(c=0;c<p;c++)switch(s[c].once&&this.removeListener(e,s[c].fn,void 0,!0),d){case 1:s[c].fn.call(s[c].context);break;case 2:s[c].fn.call(s[c].context,t);break;case 3:s[c].fn.call(s[c].context,t,n);break;case 4:s[c].fn.call(s[c].context,t,n,o);break;default:if(!u)for(f=1,u=Array(d-1);f<d;f++)u[f-1]=arguments[f];s[c].fn.apply(s[c].context,u)}}return!0},l.prototype.on=function(e,t,r){return i(this,e,t,r,!1)},l.prototype.once=function(e,t,r){return i(this,e,t,r,!0)},l.prototype.removeListener=function(e,t,n,o){var i=r?r+e:e;if(!this._events[i])return this;if(!t)return a(this,i),this;var l=this._events[i];if(l.fn)l.fn!==t||o&&!l.once||n&&l.context!==n||a(this,i);else{for(var u=0,c=[],s=l.length;u<s;u++)(l[u].fn!==t||o&&!l[u].once||n&&l[u].context!==n)&&c.push(l[u]);c.length?this._events[i]=1===c.length?c[0]:c:a(this,i)}return this},l.prototype.removeAllListeners=function(e){var t;return e?(t=r?r+e:e,this._events[t]&&a(this,t)):(this._events=new n,this._eventsCount=0),this},l.prototype.off=l.prototype.removeListener,l.prototype.addListener=l.prototype.on,l.prefixed=r,l.EventEmitter=l,e.exports=l},24369:function(e,t,r){"use strict";var n=r(2265),o="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},i=n.useState,a=n.useEffect,l=n.useLayoutEffect,u=n.useDebugValue;function c(e){var t=e.getSnapshot;e=e.value;try{var r=t();return!o(e,r)}catch(e){return!0}}var s="undefined"==typeof window||void 0===window.document||void 0===window.document.createElement?function(e,t){return t()}:function(e,t){var r=t(),n=i({inst:{value:r,getSnapshot:t}}),o=n[0].inst,s=n[1];return l(function(){o.value=r,o.getSnapshot=t,c(o)&&s({inst:o})},[e,r,t]),a(function(){return c(o)&&s({inst:o}),e(function(){c(o)&&s({inst:o})})},[e]),u(r),r};t.useSyncExternalStore=void 0!==n.useSyncExternalStore?n.useSyncExternalStore:s},92860:function(e,t,r){"use strict";var n=r(2265),o=r(82558),i="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},a=o.useSyncExternalStore,l=n.useRef,u=n.useEffect,c=n.useMemo,s=n.useDebugValue;t.useSyncExternalStoreWithSelector=function(e,t,r,n,o){var d=l(null);if(null===d.current){var f={hasValue:!1,value:null};d.current=f}else f=d.current;var p=a(e,(d=c(function(){function e(e){if(!u){if(u=!0,a=e,e=n(e),void 0!==o&&f.hasValue){var t=f.value;if(o(t,e))return l=t}return l=e}if(t=l,i(a,e))return t;var r=n(e);return void 0!==o&&o(t,r)?(a=e,t):(a=e,l=r)}var a,l,u=!1,c=void 0===r?null:r;return[function(){return e(t())},null===c?void 0:function(){return e(c())}]},[t,r,n,o]))[0],d[1]);return u(function(){f.hasValue=!0,f.value=p},[p]),s(p),p}},82558:function(e,t,r){"use strict";e.exports=r(24369)},35195:function(e,t,r){"use strict";e.exports=r(92860)},6741:function(e,t,r){"use strict";function n(e,t,{checkForDefaultPrevented:r=!0}={}){return function(n){if(e?.(n),!1===r||!n.defaultPrevented)return t?.(n)}}r.d(t,{M:function(){return n}})},13134:function(e,t,r){"use strict";r.d(t,{VY:function(){return ea},h4:function(){return eo},ck:function(){return en},fC:function(){return er},xz:function(){return ei}});var n=r(2265),o=r(73966),i=r(58068),a=r(98575),l=r(6741),u=r(80886),c=r(66840),s=r(61188),d=r(71599),f=r(99255),p=r(57437),h="Collapsible",[v,y]=(0,o.b)(h),[m,g]=v(h),b=n.forwardRef((e,t)=>{let{__scopeCollapsible:r,open:o,defaultOpen:i,disabled:a,onOpenChange:l,...s}=e,[d,v]=(0,u.T)({prop:o,defaultProp:null!=i&&i,onChange:l,caller:h});return(0,p.jsx)(m,{scope:r,disabled:a,contentId:(0,f.M)(),open:d,onOpenToggle:n.useCallback(()=>v(e=>!e),[v]),children:(0,p.jsx)(c.WV.div,{"data-state":P(d),"data-disabled":a?"":void 0,...s,ref:t})})});b.displayName=h;var w="CollapsibleTrigger",x=n.forwardRef((e,t)=>{let{__scopeCollapsible:r,...n}=e,o=g(w,r);return(0,p.jsx)(c.WV.button,{type:"button","aria-controls":o.contentId,"aria-expanded":o.open||!1,"data-state":P(o.open),"data-disabled":o.disabled?"":void 0,disabled:o.disabled,...n,ref:t,onClick:(0,l.M)(e.onClick,o.onOpenToggle)})});x.displayName=w;var O="CollapsibleContent",j=n.forwardRef((e,t)=>{let{forceMount:r,...n}=e,o=g(O,e.__scopeCollapsible);return(0,p.jsx)(d.z,{present:r||o.open,children:e=>{let{present:r}=e;return(0,p.jsx)(k,{...n,ref:t,present:r})}})});j.displayName=O;var k=n.forwardRef((e,t)=>{let{__scopeCollapsible:r,present:o,children:i,...l}=e,u=g(O,r),[d,f]=n.useState(o),h=n.useRef(null),v=(0,a.e)(t,h),y=n.useRef(0),m=y.current,b=n.useRef(0),w=b.current,x=u.open||d,j=n.useRef(x),k=n.useRef(void 0);return n.useEffect(()=>{let e=requestAnimationFrame(()=>j.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,s.b)(()=>{let e=h.current;if(e){k.current=k.current||{transitionDuration:e.style.transitionDuration,animationName:e.style.animationName},e.style.transitionDuration="0s",e.style.animationName="none";let t=e.getBoundingClientRect();y.current=t.height,b.current=t.width,j.current||(e.style.transitionDuration=k.current.transitionDuration,e.style.animationName=k.current.animationName),f(o)}},[u.open,o]),(0,p.jsx)(c.WV.div,{"data-state":P(u.open),"data-disabled":u.disabled?"":void 0,id:u.contentId,hidden:!x,...l,ref:v,style:{"--radix-collapsible-content-height":m?"".concat(m,"px"):void 0,"--radix-collapsible-content-width":w?"".concat(w,"px"):void 0,...e.style},children:x&&i})});function P(e){return e?"open":"closed"}var E=r(29114),M="Accordion",S=["Home","End","ArrowDown","ArrowUp","ArrowLeft","ArrowRight"],[C,A,T]=(0,i.B)(M),[D,N]=(0,o.b)(M,[T,y]),_=y(),I=n.forwardRef((e,t)=>{let{type:r,...n}=e;return(0,p.jsx)(C.Provider,{scope:e.__scopeAccordion,children:"multiple"===r?(0,p.jsx)(B,{...n,ref:t}):(0,p.jsx)(F,{...n,ref:t})})});I.displayName=M;var[R,L]=D(M),[z,W]=D(M,{collapsible:!1}),F=n.forwardRef((e,t)=>{let{value:r,defaultValue:o,onValueChange:i=()=>{},collapsible:a=!1,...l}=e,[c,s]=(0,u.T)({prop:r,defaultProp:null!=o?o:"",onChange:i,caller:M});return(0,p.jsx)(R,{scope:e.__scopeAccordion,value:n.useMemo(()=>c?[c]:[],[c]),onItemOpen:s,onItemClose:n.useCallback(()=>a&&s(""),[a,s]),children:(0,p.jsx)(z,{scope:e.__scopeAccordion,collapsible:a,children:(0,p.jsx)(Z,{...l,ref:t})})})}),B=n.forwardRef((e,t)=>{let{value:r,defaultValue:o,onValueChange:i=()=>{},...a}=e,[l,c]=(0,u.T)({prop:r,defaultProp:null!=o?o:[],onChange:i,caller:M}),s=n.useCallback(e=>c(function(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return[...t,e]}),[c]),d=n.useCallback(e=>c(function(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return t.filter(t=>t!==e)}),[c]);return(0,p.jsx)(R,{scope:e.__scopeAccordion,value:l,onItemOpen:s,onItemClose:d,children:(0,p.jsx)(z,{scope:e.__scopeAccordion,collapsible:!0,children:(0,p.jsx)(Z,{...a,ref:t})})})}),[K,V]=D(M),Z=n.forwardRef((e,t)=>{let{__scopeAccordion:r,disabled:o,dir:i,orientation:u="vertical",...s}=e,d=n.useRef(null),f=(0,a.e)(d,t),h=A(r),v="ltr"===(0,E.gm)(i),y=(0,l.M)(e.onKeyDown,e=>{var t;if(!S.includes(e.key))return;let r=e.target,n=h().filter(e=>{var t;return!(null===(t=e.ref.current)||void 0===t?void 0:t.disabled)}),o=n.findIndex(e=>e.ref.current===r),i=n.length;if(-1===o)return;e.preventDefault();let a=o,l=i-1,c=()=>{(a=o+1)>l&&(a=0)},s=()=>{(a=o-1)<0&&(a=l)};switch(e.key){case"Home":a=0;break;case"End":a=l;break;case"ArrowRight":"horizontal"===u&&(v?c():s());break;case"ArrowDown":"vertical"===u&&c();break;case"ArrowLeft":"horizontal"===u&&(v?s():c());break;case"ArrowUp":"vertical"===u&&s()}null===(t=n[a%i].ref.current)||void 0===t||t.focus()});return(0,p.jsx)(K,{scope:r,disabled:o,direction:i,orientation:u,children:(0,p.jsx)(C.Slot,{scope:r,children:(0,p.jsx)(c.WV.div,{...s,"data-orientation":u,ref:f,onKeyDown:o?void 0:y})})})}),U="AccordionItem",[$,H]=D(U),Y=n.forwardRef((e,t)=>{let{__scopeAccordion:r,value:n,...o}=e,i=V(U,r),a=L(U,r),l=_(r),u=(0,f.M)(),c=n&&a.value.includes(n)||!1,s=i.disabled||e.disabled;return(0,p.jsx)($,{scope:r,open:c,disabled:s,triggerId:u,children:(0,p.jsx)(b,{"data-orientation":i.orientation,"data-state":et(c),...l,...o,ref:t,disabled:s,open:c,onOpenChange:e=>{e?a.onItemOpen(n):a.onItemClose(n)}})})});Y.displayName=U;var q="AccordionHeader",X=n.forwardRef((e,t)=>{let{__scopeAccordion:r,...n}=e,o=V(M,r),i=H(q,r);return(0,p.jsx)(c.WV.h3,{"data-orientation":o.orientation,"data-state":et(i.open),"data-disabled":i.disabled?"":void 0,...n,ref:t})});X.displayName=q;var G="AccordionTrigger",Q=n.forwardRef((e,t)=>{let{__scopeAccordion:r,...n}=e,o=V(M,r),i=H(G,r),a=W(G,r),l=_(r);return(0,p.jsx)(C.ItemSlot,{scope:r,children:(0,p.jsx)(x,{"aria-disabled":i.open&&!a.collapsible||void 0,"data-orientation":o.orientation,id:i.triggerId,...l,...n,ref:t})})});Q.displayName=G;var J="AccordionContent",ee=n.forwardRef((e,t)=>{let{__scopeAccordion:r,...n}=e,o=V(M,r),i=H(J,r),a=_(r);return(0,p.jsx)(j,{role:"region","aria-labelledby":i.triggerId,"data-orientation":o.orientation,...a,...n,ref:t,style:{"--radix-accordion-content-height":"var(--radix-collapsible-content-height)","--radix-accordion-content-width":"var(--radix-collapsible-content-width)",...e.style}})});function et(e){return e?"open":"closed"}ee.displayName=J;var er=I,en=Y,eo=X,ei=Q,ea=ee},90277:function(e,t,r){"use strict";r.d(t,{$j:function(){return L},Dx:function(){return z},VY:function(){return I},aU:function(){return R},aV:function(){return _},dk:function(){return W},fC:function(){return T},h_:function(){return N},xz:function(){return D}});var n=r(2265),o=r(73966),i=r(98575),a=r(49027),l=r(6741),u=r(7495),c=r(57437),s="AlertDialog",[d,f]=(0,o.b)(s,[a.p8]),p=(0,a.p8)(),h=e=>{let{__scopeAlertDialog:t,...r}=e,n=p(t);return(0,c.jsx)(a.fC,{...n,...r,modal:!0})};h.displayName=s;var v=n.forwardRef((e,t)=>{let{__scopeAlertDialog:r,...n}=e,o=p(r);return(0,c.jsx)(a.xz,{...o,...n,ref:t})});v.displayName="AlertDialogTrigger";var y=e=>{let{__scopeAlertDialog:t,...r}=e,n=p(t);return(0,c.jsx)(a.h_,{...n,...r})};y.displayName="AlertDialogPortal";var m=n.forwardRef((e,t)=>{let{__scopeAlertDialog:r,...n}=e,o=p(r);return(0,c.jsx)(a.aV,{...o,...n,ref:t})});m.displayName="AlertDialogOverlay";var g="AlertDialogContent",[b,w]=d(g),x=(0,u.sA)("AlertDialogContent"),O=n.forwardRef((e,t)=>{let{__scopeAlertDialog:r,children:o,...u}=e,s=p(r),d=n.useRef(null),f=(0,i.e)(t,d),h=n.useRef(null);return(0,c.jsx)(a.jm,{contentName:g,titleName:j,docsSlug:"alert-dialog",children:(0,c.jsx)(b,{scope:r,cancelRef:h,children:(0,c.jsxs)(a.VY,{role:"alertdialog",...s,...u,ref:f,onOpenAutoFocus:(0,l.M)(u.onOpenAutoFocus,e=>{var t;e.preventDefault(),null===(t=h.current)||void 0===t||t.focus({preventScroll:!0})}),onPointerDownOutside:e=>e.preventDefault(),onInteractOutside:e=>e.preventDefault(),children:[(0,c.jsx)(x,{children:o}),(0,c.jsx)(A,{contentRef:d})]})})})});O.displayName=g;var j="AlertDialogTitle",k=n.forwardRef((e,t)=>{let{__scopeAlertDialog:r,...n}=e,o=p(r);return(0,c.jsx)(a.Dx,{...o,...n,ref:t})});k.displayName=j;var P="AlertDialogDescription",E=n.forwardRef((e,t)=>{let{__scopeAlertDialog:r,...n}=e,o=p(r);return(0,c.jsx)(a.dk,{...o,...n,ref:t})});E.displayName=P;var M=n.forwardRef((e,t)=>{let{__scopeAlertDialog:r,...n}=e,o=p(r);return(0,c.jsx)(a.x8,{...o,...n,ref:t})});M.displayName="AlertDialogAction";var S="AlertDialogCancel",C=n.forwardRef((e,t)=>{let{__scopeAlertDialog:r,...n}=e,{cancelRef:o}=w(S,r),l=p(r),u=(0,i.e)(t,o);return(0,c.jsx)(a.x8,{...l,...n,ref:u})});C.displayName=S;var A=e=>{let{contentRef:t}=e,r="`".concat(g,"` requires a description for the component to be accessible for screen reader users.\n\nYou can add a description to the `").concat(g,"` by passing a `").concat(P,"` component as a child, which also benefits sighted users by adding visible context to the dialog.\n\nAlternatively, you can use your own component as a description by assigning it an `id` and passing the same value to the `aria-describedby` prop in `").concat(g,"`. If the description is confusing or duplicative for sighted users, you can use the `@radix-ui/react-visually-hidden` primitive as a wrapper around your description component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/alert-dialog");return n.useEffect(()=>{var e;document.getElementById(null===(e=t.current)||void 0===e?void 0:e.getAttribute("aria-describedby"))||console.warn(r)},[r,t]),null},T=h,D=v,N=y,_=m,I=O,R=M,L=C,z=k,W=E},61146:function(e,t,r){"use strict";r.d(t,{NY:function(){return k},Ee:function(){return j},fC:function(){return O}});var n=r(2265),o=r(73966),i=r(26606),a=r(61188),l=r(66840),u=r(82558);function c(){return()=>{}}var s=r(57437),d="Avatar",[f,p]=(0,o.b)(d),[h,v]=f(d),y=n.forwardRef((e,t)=>{let{__scopeAvatar:r,...o}=e,[i,a]=n.useState("idle");return(0,s.jsx)(h,{scope:r,imageLoadingStatus:i,onImageLoadingStatusChange:a,children:(0,s.jsx)(l.WV.span,{...o,ref:t})})});y.displayName=d;var m="AvatarImage",g=n.forwardRef((e,t)=>{let{__scopeAvatar:r,src:o,onLoadingStatusChange:d=()=>{},...f}=e,p=v(m,r),h=function(e,t){let{referrerPolicy:r,crossOrigin:o}=t,i=(0,u.useSyncExternalStore)(c,()=>!0,()=>!1),l=n.useRef(null),s=i?(l.current||(l.current=new window.Image),l.current):null,[d,f]=n.useState(()=>x(s,e));return(0,a.b)(()=>{f(x(s,e))},[s,e]),(0,a.b)(()=>{let e=e=>()=>{f(e)};if(!s)return;let t=e("loaded"),n=e("error");return s.addEventListener("load",t),s.addEventListener("error",n),r&&(s.referrerPolicy=r),"string"==typeof o&&(s.crossOrigin=o),()=>{s.removeEventListener("load",t),s.removeEventListener("error",n)}},[s,o,r]),d}(o,f),y=(0,i.W)(e=>{d(e),p.onImageLoadingStatusChange(e)});return(0,a.b)(()=>{"idle"!==h&&y(h)},[h,y]),"loaded"===h?(0,s.jsx)(l.WV.img,{...f,ref:t,src:o}):null});g.displayName=m;var b="AvatarFallback",w=n.forwardRef((e,t)=>{let{__scopeAvatar:r,delayMs:o,...i}=e,a=v(b,r),[u,c]=n.useState(void 0===o);return n.useEffect(()=>{if(void 0!==o){let e=window.setTimeout(()=>c(!0),o);return()=>window.clearTimeout(e)}},[o]),u&&"loaded"!==a.imageLoadingStatus?(0,s.jsx)(l.WV.span,{...i,ref:t}):null});function x(e,t){return e?t?(e.src!==t&&(e.src=t),e.complete&&e.naturalWidth>0?"loaded":"loading"):"error":"idle"}w.displayName=b;var O=y,j=g,k=w},58068:function(e,t,r){"use strict";r.d(t,{B:function(){return u}});var n=r(2265),o=r(73966),i=r(98575),a=r(7495),l=r(57437);function u(e){let t=e+"CollectionProvider",[r,u]=(0,o.b)(t),[c,s]=r(t,{collectionRef:{current:null},itemMap:new Map}),d=e=>{let{scope:t,children:r}=e,o=n.useRef(null),i=n.useRef(new Map).current;return(0,l.jsx)(c,{scope:t,itemMap:i,collectionRef:o,children:r})};d.displayName=t;let f=e+"CollectionSlot",p=(0,a.Z8)(f),h=n.forwardRef((e,t)=>{let{scope:r,children:n}=e,o=s(f,r),a=(0,i.e)(t,o.collectionRef);return(0,l.jsx)(p,{ref:a,children:n})});h.displayName=f;let v=e+"CollectionItemSlot",y="data-radix-collection-item",m=(0,a.Z8)(v),g=n.forwardRef((e,t)=>{let{scope:r,children:o,...a}=e,u=n.useRef(null),c=(0,i.e)(t,u),d=s(v,r);return n.useEffect(()=>(d.itemMap.set(u,{ref:u,...a}),()=>void d.itemMap.delete(u))),(0,l.jsx)(m,{[y]:"",ref:c,children:o})});return g.displayName=v,[{Provider:d,Slot:h,ItemSlot:g},function(t){let r=s(e+"CollectionConsumer",t);return n.useCallback(()=>{let e=r.collectionRef.current;if(!e)return[];let t=Array.from(e.querySelectorAll("[".concat(y,"]")));return Array.from(r.itemMap.values()).sort((e,r)=>t.indexOf(e.ref.current)-t.indexOf(r.ref.current))},[r.collectionRef,r.itemMap])},u]}},98575:function(e,t,r){"use strict";r.d(t,{F:function(){return i},e:function(){return a}});var n=r(2265);function o(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function i(...e){return t=>{let r=!1,n=e.map(e=>{let n=o(e,t);return r||"function"!=typeof n||(r=!0),n});if(r)return()=>{for(let t=0;t<n.length;t++){let r=n[t];"function"==typeof r?r():o(e[t],null)}}}}function a(...e){return n.useCallback(i(...e),e)}},73966:function(e,t,r){"use strict";r.d(t,{b:function(){return a},k:function(){return i}});var n=r(2265),o=r(57437);function i(e,t){let r=n.createContext(t),i=e=>{let{children:t,...i}=e,a=n.useMemo(()=>i,Object.values(i));return(0,o.jsx)(r.Provider,{value:a,children:t})};return i.displayName=e+"Provider",[i,function(o){let i=n.useContext(r);if(i)return i;if(void 0!==t)return t;throw Error(`\`${o}\` must be used within \`${e}\``)}]}function a(e,t=[]){let r=[],i=()=>{let t=r.map(e=>n.createContext(e));return function(r){let o=r?.[e]||t;return n.useMemo(()=>({[`__scope${e}`]:{...r,[e]:o}}),[r,o])}};return i.scopeName=e,[function(t,i){let a=n.createContext(i),l=r.length;r=[...r,i];let u=t=>{let{scope:r,children:i,...u}=t,c=r?.[e]?.[l]||a,s=n.useMemo(()=>u,Object.values(u));return(0,o.jsx)(c.Provider,{value:s,children:i})};return u.displayName=t+"Provider",[u,function(r,o){let u=o?.[e]?.[l]||a,c=n.useContext(u);if(c)return c;if(void 0!==i)return i;throw Error(`\`${r}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let r=()=>{let r=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let o=r.reduce((t,{useScope:r,scopeName:n})=>{let o=r(e)[`__scope${n}`];return{...t,...o}},{});return n.useMemo(()=>({[`__scope${t.scopeName}`]:o}),[o])}};return r.scopeName=t.scopeName,r}(i,...t)]}},49027:function(e,t,r){"use strict";r.d(t,{Dx:function(){return en},VY:function(){return er},aV:function(){return et},dk:function(){return eo},fC:function(){return Q},h_:function(){return ee},jm:function(){return Y},p8:function(){return x},x8:function(){return ei},xz:function(){return J}});var n=r(2265),o=r(6741),i=r(98575),a=r(73966),l=r(99255),u=r(80886),c=r(15278),s=r(99103),d=r(83832),f=r(71599),p=r(66840),h=r(86097),v=r(87922),y=r(5478),m=r(7495),g=r(57437),b="Dialog",[w,x]=(0,a.b)(b),[O,j]=w(b),k=e=>{let{__scopeDialog:t,children:r,open:o,defaultOpen:i,onOpenChange:a,modal:c=!0}=e,s=n.useRef(null),d=n.useRef(null),[f,p]=(0,u.T)({prop:o,defaultProp:null!=i&&i,onChange:a,caller:b});return(0,g.jsx)(O,{scope:t,triggerRef:s,contentRef:d,contentId:(0,l.M)(),titleId:(0,l.M)(),descriptionId:(0,l.M)(),open:f,onOpenChange:p,onOpenToggle:n.useCallback(()=>p(e=>!e),[p]),modal:c,children:r})};k.displayName=b;var P="DialogTrigger",E=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,a=j(P,r),l=(0,i.e)(t,a.triggerRef);return(0,g.jsx)(p.WV.button,{type:"button","aria-haspopup":"dialog","aria-expanded":a.open,"aria-controls":a.contentId,"data-state":$(a.open),...n,ref:l,onClick:(0,o.M)(e.onClick,a.onOpenToggle)})});E.displayName=P;var M="DialogPortal",[S,C]=w(M,{forceMount:void 0}),A=e=>{let{__scopeDialog:t,forceMount:r,children:o,container:i}=e,a=j(M,t);return(0,g.jsx)(S,{scope:t,forceMount:r,children:n.Children.map(o,e=>(0,g.jsx)(f.z,{present:r||a.open,children:(0,g.jsx)(d.h,{asChild:!0,container:i,children:e})}))})};A.displayName=M;var T="DialogOverlay",D=n.forwardRef((e,t)=>{let r=C(T,e.__scopeDialog),{forceMount:n=r.forceMount,...o}=e,i=j(T,e.__scopeDialog);return i.modal?(0,g.jsx)(f.z,{present:n||i.open,children:(0,g.jsx)(_,{...o,ref:t})}):null});D.displayName=T;var N=(0,m.Z8)("DialogOverlay.RemoveScroll"),_=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,o=j(T,r);return(0,g.jsx)(v.Z,{as:N,allowPinchZoom:!0,shards:[o.contentRef],children:(0,g.jsx)(p.WV.div,{"data-state":$(o.open),...n,ref:t,style:{pointerEvents:"auto",...n.style}})})}),I="DialogContent",R=n.forwardRef((e,t)=>{let r=C(I,e.__scopeDialog),{forceMount:n=r.forceMount,...o}=e,i=j(I,e.__scopeDialog);return(0,g.jsx)(f.z,{present:n||i.open,children:i.modal?(0,g.jsx)(L,{...o,ref:t}):(0,g.jsx)(z,{...o,ref:t})})});R.displayName=I;var L=n.forwardRef((e,t)=>{let r=j(I,e.__scopeDialog),a=n.useRef(null),l=(0,i.e)(t,r.contentRef,a);return n.useEffect(()=>{let e=a.current;if(e)return(0,y.Ry)(e)},[]),(0,g.jsx)(W,{...e,ref:l,trapFocus:r.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,o.M)(e.onCloseAutoFocus,e=>{var t;e.preventDefault(),null===(t=r.triggerRef.current)||void 0===t||t.focus()}),onPointerDownOutside:(0,o.M)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,r=0===t.button&&!0===t.ctrlKey;(2===t.button||r)&&e.preventDefault()}),onFocusOutside:(0,o.M)(e.onFocusOutside,e=>e.preventDefault())})}),z=n.forwardRef((e,t)=>{let r=j(I,e.__scopeDialog),o=n.useRef(!1),i=n.useRef(!1);return(0,g.jsx)(W,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{var n,a;null===(n=e.onCloseAutoFocus)||void 0===n||n.call(e,t),t.defaultPrevented||(o.current||null===(a=r.triggerRef.current)||void 0===a||a.focus(),t.preventDefault()),o.current=!1,i.current=!1},onInteractOutside:t=>{var n,a;null===(n=e.onInteractOutside)||void 0===n||n.call(e,t),t.defaultPrevented||(o.current=!0,"pointerdown"!==t.detail.originalEvent.type||(i.current=!0));let l=t.target;(null===(a=r.triggerRef.current)||void 0===a?void 0:a.contains(l))&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&i.current&&t.preventDefault()}})}),W=n.forwardRef((e,t)=>{let{__scopeDialog:r,trapFocus:o,onOpenAutoFocus:a,onCloseAutoFocus:l,...u}=e,d=j(I,r),f=n.useRef(null),p=(0,i.e)(t,f);return(0,h.EW)(),(0,g.jsxs)(g.Fragment,{children:[(0,g.jsx)(s.M,{asChild:!0,loop:!0,trapped:o,onMountAutoFocus:a,onUnmountAutoFocus:l,children:(0,g.jsx)(c.XB,{role:"dialog",id:d.contentId,"aria-describedby":d.descriptionId,"aria-labelledby":d.titleId,"data-state":$(d.open),...u,ref:p,onDismiss:()=>d.onOpenChange(!1)})}),(0,g.jsxs)(g.Fragment,{children:[(0,g.jsx)(X,{titleId:d.titleId}),(0,g.jsx)(G,{contentRef:f,descriptionId:d.descriptionId})]})]})}),F="DialogTitle",B=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,o=j(F,r);return(0,g.jsx)(p.WV.h2,{id:o.titleId,...n,ref:t})});B.displayName=F;var K="DialogDescription",V=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,o=j(K,r);return(0,g.jsx)(p.WV.p,{id:o.descriptionId,...n,ref:t})});V.displayName=K;var Z="DialogClose",U=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,i=j(Z,r);return(0,g.jsx)(p.WV.button,{type:"button",...n,ref:t,onClick:(0,o.M)(e.onClick,()=>i.onOpenChange(!1))})});function $(e){return e?"open":"closed"}U.displayName=Z;var H="DialogTitleWarning",[Y,q]=(0,a.k)(H,{contentName:I,titleName:F,docsSlug:"dialog"}),X=e=>{let{titleId:t}=e,r=q(H),o="`".concat(r.contentName,"` requires a `").concat(r.titleName,"` for the component to be accessible for screen reader users.\n\nIf you want to hide the `").concat(r.titleName,"`, you can wrap it with our VisuallyHidden component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/").concat(r.docsSlug);return n.useEffect(()=>{t&&!document.getElementById(t)&&console.error(o)},[o,t]),null},G=e=>{let{contentRef:t,descriptionId:r}=e,o=q("DialogDescriptionWarning"),i="Warning: Missing `Description` or `aria-describedby={undefined}` for {".concat(o.contentName,"}.");return n.useEffect(()=>{var e;let n=null===(e=t.current)||void 0===e?void 0:e.getAttribute("aria-describedby");r&&n&&!document.getElementById(r)&&console.warn(i)},[i,t,r]),null},Q=k,J=E,ee=A,et=D,er=R,en=B,eo=V,ei=U},29114:function(e,t,r){"use strict";r.d(t,{gm:function(){return i}});var n=r(2265);r(57437);var o=n.createContext(void 0);function i(e){let t=n.useContext(o);return e||t||"ltr"}},15278:function(e,t,r){"use strict";r.d(t,{I0:function(){return m},XB:function(){return f},fC:function(){return y}});var n,o=r(2265),i=r(6741),a=r(66840),l=r(98575),u=r(26606),c=r(57437),s="dismissableLayer.update",d=o.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),f=o.forwardRef((e,t)=>{var r,f;let{disableOutsidePointerEvents:p=!1,onEscapeKeyDown:y,onPointerDownOutside:m,onFocusOutside:g,onInteractOutside:b,onDismiss:w,...x}=e,O=o.useContext(d),[j,k]=o.useState(null),P=null!==(f=null==j?void 0:j.ownerDocument)&&void 0!==f?f:null===(r=globalThis)||void 0===r?void 0:r.document,[,E]=o.useState({}),M=(0,l.e)(t,e=>k(e)),S=Array.from(O.layers),[C]=[...O.layersWithOutsidePointerEventsDisabled].slice(-1),A=S.indexOf(C),T=j?S.indexOf(j):-1,D=O.layersWithOutsidePointerEventsDisabled.size>0,N=T>=A,_=function(e){var t;let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null===(t=globalThis)||void 0===t?void 0:t.document,n=(0,u.W)(e),i=o.useRef(!1),a=o.useRef(()=>{});return o.useEffect(()=>{let e=e=>{if(e.target&&!i.current){let t=function(){v("dismissableLayer.pointerDownOutside",n,o,{discrete:!0})},o={originalEvent:e};"touch"===e.pointerType?(r.removeEventListener("click",a.current),a.current=t,r.addEventListener("click",a.current,{once:!0})):t()}else r.removeEventListener("click",a.current);i.current=!1},t=window.setTimeout(()=>{r.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(t),r.removeEventListener("pointerdown",e),r.removeEventListener("click",a.current)}},[r,n]),{onPointerDownCapture:()=>i.current=!0}}(e=>{let t=e.target,r=[...O.branches].some(e=>e.contains(t));!N||r||(null==m||m(e),null==b||b(e),e.defaultPrevented||null==w||w())},P),I=function(e){var t;let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null===(t=globalThis)||void 0===t?void 0:t.document,n=(0,u.W)(e),i=o.useRef(!1);return o.useEffect(()=>{let e=e=>{e.target&&!i.current&&v("dismissableLayer.focusOutside",n,{originalEvent:e},{discrete:!1})};return r.addEventListener("focusin",e),()=>r.removeEventListener("focusin",e)},[r,n]),{onFocusCapture:()=>i.current=!0,onBlurCapture:()=>i.current=!1}}(e=>{let t=e.target;[...O.branches].some(e=>e.contains(t))||(null==g||g(e),null==b||b(e),e.defaultPrevented||null==w||w())},P);return!function(e,t=globalThis?.document){let r=(0,u.W)(e);o.useEffect(()=>{let e=e=>{"Escape"===e.key&&r(e)};return t.addEventListener("keydown",e,{capture:!0}),()=>t.removeEventListener("keydown",e,{capture:!0})},[r,t])}(e=>{T!==O.layers.size-1||(null==y||y(e),!e.defaultPrevented&&w&&(e.preventDefault(),w()))},P),o.useEffect(()=>{if(j)return p&&(0===O.layersWithOutsidePointerEventsDisabled.size&&(n=P.body.style.pointerEvents,P.body.style.pointerEvents="none"),O.layersWithOutsidePointerEventsDisabled.add(j)),O.layers.add(j),h(),()=>{p&&1===O.layersWithOutsidePointerEventsDisabled.size&&(P.body.style.pointerEvents=n)}},[j,P,p,O]),o.useEffect(()=>()=>{j&&(O.layers.delete(j),O.layersWithOutsidePointerEventsDisabled.delete(j),h())},[j,O]),o.useEffect(()=>{let e=()=>E({});return document.addEventListener(s,e),()=>document.removeEventListener(s,e)},[]),(0,c.jsx)(a.WV.div,{...x,ref:M,style:{pointerEvents:D?N?"auto":"none":void 0,...e.style},onFocusCapture:(0,i.M)(e.onFocusCapture,I.onFocusCapture),onBlurCapture:(0,i.M)(e.onBlurCapture,I.onBlurCapture),onPointerDownCapture:(0,i.M)(e.onPointerDownCapture,_.onPointerDownCapture)})});f.displayName="DismissableLayer";var p=o.forwardRef((e,t)=>{let r=o.useContext(d),n=o.useRef(null),i=(0,l.e)(t,n);return o.useEffect(()=>{let e=n.current;if(e)return r.branches.add(e),()=>{r.branches.delete(e)}},[r.branches]),(0,c.jsx)(a.WV.div,{...e,ref:i})});function h(){let e=new CustomEvent(s);document.dispatchEvent(e)}function v(e,t,r,n){let{discrete:o}=n,i=r.originalEvent.target,l=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:r});t&&i.addEventListener(e,t,{once:!0}),o?(0,a.jH)(i,l):i.dispatchEvent(l)}p.displayName="DismissableLayerBranch";var y=f,m=p},70085:function(e,t,r){"use strict";r.d(t,{oC:function(){return e9},VY:function(){return e6},ZA:function(){return e5},ck:function(){return e4},wU:function(){return te},__:function(){return e3},Uv:function(){return e2},Ee:function(){return e7},Rk:function(){return e8},fC:function(){return e0},Z0:function(){return tt},Tr:function(){return tr},tu:function(){return to},fF:function(){return tn},xz:function(){return e1}});var n=r(2265),o=r(6741),i=r(98575),a=r(73966),l=r(80886),u=r(66840),c=r(58068),s=r(29114),d=r(15278),f=r(86097),p=r(99103),h=r(99255),v=r(96806),y=r(83832),m=r(71599),g=r(1353),b=r(7495),w=r(26606),x=r(5478),O=r(87922),j=r(57437),k=["Enter"," "],P=["ArrowUp","PageDown","End"],E=["ArrowDown","PageUp","Home",...P],M={ltr:[...k,"ArrowRight"],rtl:[...k,"ArrowLeft"]},S={ltr:["ArrowLeft"],rtl:["ArrowRight"]},C="Menu",[A,T,D]=(0,c.B)(C),[N,_]=(0,a.b)(C,[D,v.D7,g.Pc]),I=(0,v.D7)(),R=(0,g.Pc)(),[L,z]=N(C),[W,F]=N(C),B=e=>{let{__scopeMenu:t,open:r=!1,children:o,dir:i,onOpenChange:a,modal:l=!0}=e,u=I(t),[c,d]=n.useState(null),f=n.useRef(!1),p=(0,w.W)(a),h=(0,s.gm)(i);return n.useEffect(()=>{let e=()=>{f.current=!0,document.addEventListener("pointerdown",t,{capture:!0,once:!0}),document.addEventListener("pointermove",t,{capture:!0,once:!0})},t=()=>f.current=!1;return document.addEventListener("keydown",e,{capture:!0}),()=>{document.removeEventListener("keydown",e,{capture:!0}),document.removeEventListener("pointerdown",t,{capture:!0}),document.removeEventListener("pointermove",t,{capture:!0})}},[]),(0,j.jsx)(v.fC,{...u,children:(0,j.jsx)(L,{scope:t,open:r,onOpenChange:p,content:c,onContentChange:d,children:(0,j.jsx)(W,{scope:t,onClose:n.useCallback(()=>p(!1),[p]),isUsingKeyboardRef:f,dir:h,modal:l,children:o})})})};B.displayName=C;var K=n.forwardRef((e,t)=>{let{__scopeMenu:r,...n}=e,o=I(r);return(0,j.jsx)(v.ee,{...o,...n,ref:t})});K.displayName="MenuAnchor";var V="MenuPortal",[Z,U]=N(V,{forceMount:void 0}),$=e=>{let{__scopeMenu:t,forceMount:r,children:n,container:o}=e,i=z(V,t);return(0,j.jsx)(Z,{scope:t,forceMount:r,children:(0,j.jsx)(m.z,{present:r||i.open,children:(0,j.jsx)(y.h,{asChild:!0,container:o,children:n})})})};$.displayName=V;var H="MenuContent",[Y,q]=N(H),X=n.forwardRef((e,t)=>{let r=U(H,e.__scopeMenu),{forceMount:n=r.forceMount,...o}=e,i=z(H,e.__scopeMenu),a=F(H,e.__scopeMenu);return(0,j.jsx)(A.Provider,{scope:e.__scopeMenu,children:(0,j.jsx)(m.z,{present:n||i.open,children:(0,j.jsx)(A.Slot,{scope:e.__scopeMenu,children:a.modal?(0,j.jsx)(G,{...o,ref:t}):(0,j.jsx)(Q,{...o,ref:t})})})})}),G=n.forwardRef((e,t)=>{let r=z(H,e.__scopeMenu),a=n.useRef(null),l=(0,i.e)(t,a);return n.useEffect(()=>{let e=a.current;if(e)return(0,x.Ry)(e)},[]),(0,j.jsx)(ee,{...e,ref:l,trapFocus:r.open,disableOutsidePointerEvents:r.open,disableOutsideScroll:!0,onFocusOutside:(0,o.M)(e.onFocusOutside,e=>e.preventDefault(),{checkForDefaultPrevented:!1}),onDismiss:()=>r.onOpenChange(!1)})}),Q=n.forwardRef((e,t)=>{let r=z(H,e.__scopeMenu);return(0,j.jsx)(ee,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,disableOutsideScroll:!1,onDismiss:()=>r.onOpenChange(!1)})}),J=(0,b.Z8)("MenuContent.ScrollLock"),ee=n.forwardRef((e,t)=>{let{__scopeMenu:r,loop:a=!1,trapFocus:l,onOpenAutoFocus:u,onCloseAutoFocus:c,disableOutsidePointerEvents:s,onEntryFocus:h,onEscapeKeyDown:y,onPointerDownOutside:m,onFocusOutside:b,onInteractOutside:w,onDismiss:x,disableOutsideScroll:k,...M}=e,S=z(H,r),C=F(H,r),A=I(r),D=R(r),N=T(r),[_,L]=n.useState(null),W=n.useRef(null),B=(0,i.e)(t,W,S.onContentChange),K=n.useRef(0),V=n.useRef(""),Z=n.useRef(0),U=n.useRef(null),$=n.useRef("right"),q=n.useRef(0),X=k?O.Z:n.Fragment,G=e=>{var t,r;let n=V.current+e,o=N().filter(e=>!e.disabled),i=document.activeElement,a=null===(t=o.find(e=>e.ref.current===i))||void 0===t?void 0:t.textValue,l=function(e,t,r){var n;let o=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,i=(n=Math.max(r?e.indexOf(r):-1,0),e.map((t,r)=>e[(n+r)%e.length]));1===o.length&&(i=i.filter(e=>e!==r));let a=i.find(e=>e.toLowerCase().startsWith(o.toLowerCase()));return a!==r?a:void 0}(o.map(e=>e.textValue),n,a),u=null===(r=o.find(e=>e.textValue===l))||void 0===r?void 0:r.ref.current;!function e(t){V.current=t,window.clearTimeout(K.current),""!==t&&(K.current=window.setTimeout(()=>e(""),1e3))}(n),u&&setTimeout(()=>u.focus())};n.useEffect(()=>()=>window.clearTimeout(K.current),[]),(0,f.EW)();let Q=n.useCallback(e=>{var t,r,n;return $.current===(null===(t=U.current)||void 0===t?void 0:t.side)&&!!(n=null===(r=U.current)||void 0===r?void 0:r.area)&&function(e,t){let{x:r,y:n}=e,o=!1;for(let e=0,i=t.length-1;e<t.length;i=e++){let a=t[e],l=t[i],u=a.x,c=a.y,s=l.x,d=l.y;c>n!=d>n&&r<(s-u)*(n-c)/(d-c)+u&&(o=!o)}return o}({x:e.clientX,y:e.clientY},n)},[]);return(0,j.jsx)(Y,{scope:r,searchRef:V,onItemEnter:n.useCallback(e=>{Q(e)&&e.preventDefault()},[Q]),onItemLeave:n.useCallback(e=>{var t;Q(e)||(null===(t=W.current)||void 0===t||t.focus(),L(null))},[Q]),onTriggerLeave:n.useCallback(e=>{Q(e)&&e.preventDefault()},[Q]),pointerGraceTimerRef:Z,onPointerGraceIntentChange:n.useCallback(e=>{U.current=e},[]),children:(0,j.jsx)(X,{...k?{as:J,allowPinchZoom:!0}:void 0,children:(0,j.jsx)(p.M,{asChild:!0,trapped:l,onMountAutoFocus:(0,o.M)(u,e=>{var t;e.preventDefault(),null===(t=W.current)||void 0===t||t.focus({preventScroll:!0})}),onUnmountAutoFocus:c,children:(0,j.jsx)(d.XB,{asChild:!0,disableOutsidePointerEvents:s,onEscapeKeyDown:y,onPointerDownOutside:m,onFocusOutside:b,onInteractOutside:w,onDismiss:x,children:(0,j.jsx)(g.fC,{asChild:!0,...D,dir:C.dir,orientation:"vertical",loop:a,currentTabStopId:_,onCurrentTabStopIdChange:L,onEntryFocus:(0,o.M)(h,e=>{C.isUsingKeyboardRef.current||e.preventDefault()}),preventScrollOnEntryFocus:!0,children:(0,j.jsx)(v.VY,{role:"menu","aria-orientation":"vertical","data-state":eS(S.open),"data-radix-menu-content":"",dir:C.dir,...A,...M,ref:B,style:{outline:"none",...M.style},onKeyDown:(0,o.M)(M.onKeyDown,e=>{let t=e.target.closest("[data-radix-menu-content]")===e.currentTarget,r=e.ctrlKey||e.altKey||e.metaKey,n=1===e.key.length;t&&("Tab"===e.key&&e.preventDefault(),!r&&n&&G(e.key));let o=W.current;if(e.target!==o||!E.includes(e.key))return;e.preventDefault();let i=N().filter(e=>!e.disabled).map(e=>e.ref.current);P.includes(e.key)&&i.reverse(),function(e){let t=document.activeElement;for(let r of e)if(r===t||(r.focus(),document.activeElement!==t))return}(i)}),onBlur:(0,o.M)(e.onBlur,e=>{e.currentTarget.contains(e.target)||(window.clearTimeout(K.current),V.current="")}),onPointerMove:(0,o.M)(e.onPointerMove,eT(e=>{let t=e.target,r=q.current!==e.clientX;if(e.currentTarget.contains(t)&&r){let t=e.clientX>q.current?"right":"left";$.current=t,q.current=e.clientX}}))})})})})})})});X.displayName=H;var et=n.forwardRef((e,t)=>{let{__scopeMenu:r,...n}=e;return(0,j.jsx)(u.WV.div,{role:"group",...n,ref:t})});et.displayName="MenuGroup";var er=n.forwardRef((e,t)=>{let{__scopeMenu:r,...n}=e;return(0,j.jsx)(u.WV.div,{...n,ref:t})});er.displayName="MenuLabel";var en="MenuItem",eo="menu.itemSelect",ei=n.forwardRef((e,t)=>{let{disabled:r=!1,onSelect:a,...l}=e,c=n.useRef(null),s=F(en,e.__scopeMenu),d=q(en,e.__scopeMenu),f=(0,i.e)(t,c),p=n.useRef(!1);return(0,j.jsx)(ea,{...l,ref:f,disabled:r,onClick:(0,o.M)(e.onClick,()=>{let e=c.current;if(!r&&e){let t=new CustomEvent(eo,{bubbles:!0,cancelable:!0});e.addEventListener(eo,e=>null==a?void 0:a(e),{once:!0}),(0,u.jH)(e,t),t.defaultPrevented?p.current=!1:s.onClose()}}),onPointerDown:t=>{var r;null===(r=e.onPointerDown)||void 0===r||r.call(e,t),p.current=!0},onPointerUp:(0,o.M)(e.onPointerUp,e=>{var t;p.current||null===(t=e.currentTarget)||void 0===t||t.click()}),onKeyDown:(0,o.M)(e.onKeyDown,e=>{let t=""!==d.searchRef.current;!r&&(!t||" "!==e.key)&&k.includes(e.key)&&(e.currentTarget.click(),e.preventDefault())})})});ei.displayName=en;var ea=n.forwardRef((e,t)=>{let{__scopeMenu:r,disabled:a=!1,textValue:l,...c}=e,s=q(en,r),d=R(r),f=n.useRef(null),p=(0,i.e)(t,f),[h,v]=n.useState(!1),[y,m]=n.useState("");return n.useEffect(()=>{let e=f.current;if(e){var t;m((null!==(t=e.textContent)&&void 0!==t?t:"").trim())}},[c.children]),(0,j.jsx)(A.ItemSlot,{scope:r,disabled:a,textValue:null!=l?l:y,children:(0,j.jsx)(g.ck,{asChild:!0,...d,focusable:!a,children:(0,j.jsx)(u.WV.div,{role:"menuitem","data-highlighted":h?"":void 0,"aria-disabled":a||void 0,"data-disabled":a?"":void 0,...c,ref:p,onPointerMove:(0,o.M)(e.onPointerMove,eT(e=>{a?s.onItemLeave(e):(s.onItemEnter(e),e.defaultPrevented||e.currentTarget.focus({preventScroll:!0}))})),onPointerLeave:(0,o.M)(e.onPointerLeave,eT(e=>s.onItemLeave(e))),onFocus:(0,o.M)(e.onFocus,()=>v(!0)),onBlur:(0,o.M)(e.onBlur,()=>v(!1))})})})}),el=n.forwardRef((e,t)=>{let{checked:r=!1,onCheckedChange:n,...i}=e;return(0,j.jsx)(ev,{scope:e.__scopeMenu,checked:r,children:(0,j.jsx)(ei,{role:"menuitemcheckbox","aria-checked":eC(r)?"mixed":r,...i,ref:t,"data-state":eA(r),onSelect:(0,o.M)(i.onSelect,()=>null==n?void 0:n(!!eC(r)||!r),{checkForDefaultPrevented:!1})})})});el.displayName="MenuCheckboxItem";var eu="MenuRadioGroup",[ec,es]=N(eu,{value:void 0,onValueChange:()=>{}}),ed=n.forwardRef((e,t)=>{let{value:r,onValueChange:n,...o}=e,i=(0,w.W)(n);return(0,j.jsx)(ec,{scope:e.__scopeMenu,value:r,onValueChange:i,children:(0,j.jsx)(et,{...o,ref:t})})});ed.displayName=eu;var ef="MenuRadioItem",ep=n.forwardRef((e,t)=>{let{value:r,...n}=e,i=es(ef,e.__scopeMenu),a=r===i.value;return(0,j.jsx)(ev,{scope:e.__scopeMenu,checked:a,children:(0,j.jsx)(ei,{role:"menuitemradio","aria-checked":a,...n,ref:t,"data-state":eA(a),onSelect:(0,o.M)(n.onSelect,()=>{var e;return null===(e=i.onValueChange)||void 0===e?void 0:e.call(i,r)},{checkForDefaultPrevented:!1})})})});ep.displayName=ef;var eh="MenuItemIndicator",[ev,ey]=N(eh,{checked:!1}),em=n.forwardRef((e,t)=>{let{__scopeMenu:r,forceMount:n,...o}=e,i=ey(eh,r);return(0,j.jsx)(m.z,{present:n||eC(i.checked)||!0===i.checked,children:(0,j.jsx)(u.WV.span,{...o,ref:t,"data-state":eA(i.checked)})})});em.displayName=eh;var eg=n.forwardRef((e,t)=>{let{__scopeMenu:r,...n}=e;return(0,j.jsx)(u.WV.div,{role:"separator","aria-orientation":"horizontal",...n,ref:t})});eg.displayName="MenuSeparator";var eb=n.forwardRef((e,t)=>{let{__scopeMenu:r,...n}=e,o=I(r);return(0,j.jsx)(v.Eh,{...o,...n,ref:t})});eb.displayName="MenuArrow";var ew="MenuSub",[ex,eO]=N(ew),ej=e=>{let{__scopeMenu:t,children:r,open:o=!1,onOpenChange:i}=e,a=z(ew,t),l=I(t),[u,c]=n.useState(null),[s,d]=n.useState(null),f=(0,w.W)(i);return n.useEffect(()=>(!1===a.open&&f(!1),()=>f(!1)),[a.open,f]),(0,j.jsx)(v.fC,{...l,children:(0,j.jsx)(L,{scope:t,open:o,onOpenChange:f,content:s,onContentChange:d,children:(0,j.jsx)(ex,{scope:t,contentId:(0,h.M)(),triggerId:(0,h.M)(),trigger:u,onTriggerChange:c,children:r})})})};ej.displayName=ew;var ek="MenuSubTrigger",eP=n.forwardRef((e,t)=>{let r=z(ek,e.__scopeMenu),a=F(ek,e.__scopeMenu),l=eO(ek,e.__scopeMenu),u=q(ek,e.__scopeMenu),c=n.useRef(null),{pointerGraceTimerRef:s,onPointerGraceIntentChange:d}=u,f={__scopeMenu:e.__scopeMenu},p=n.useCallback(()=>{c.current&&window.clearTimeout(c.current),c.current=null},[]);return n.useEffect(()=>p,[p]),n.useEffect(()=>{let e=s.current;return()=>{window.clearTimeout(e),d(null)}},[s,d]),(0,j.jsx)(K,{asChild:!0,...f,children:(0,j.jsx)(ea,{id:l.triggerId,"aria-haspopup":"menu","aria-expanded":r.open,"aria-controls":l.contentId,"data-state":eS(r.open),...e,ref:(0,i.F)(t,l.onTriggerChange),onClick:t=>{var n;null===(n=e.onClick)||void 0===n||n.call(e,t),e.disabled||t.defaultPrevented||(t.currentTarget.focus(),r.open||r.onOpenChange(!0))},onPointerMove:(0,o.M)(e.onPointerMove,eT(t=>{u.onItemEnter(t),t.defaultPrevented||e.disabled||r.open||c.current||(u.onPointerGraceIntentChange(null),c.current=window.setTimeout(()=>{r.onOpenChange(!0),p()},100))})),onPointerLeave:(0,o.M)(e.onPointerLeave,eT(e=>{var t,n;p();let o=null===(t=r.content)||void 0===t?void 0:t.getBoundingClientRect();if(o){let t=null===(n=r.content)||void 0===n?void 0:n.dataset.side,i="right"===t,a=o[i?"left":"right"],l=o[i?"right":"left"];u.onPointerGraceIntentChange({area:[{x:e.clientX+(i?-5:5),y:e.clientY},{x:a,y:o.top},{x:l,y:o.top},{x:l,y:o.bottom},{x:a,y:o.bottom}],side:t}),window.clearTimeout(s.current),s.current=window.setTimeout(()=>u.onPointerGraceIntentChange(null),300)}else{if(u.onTriggerLeave(e),e.defaultPrevented)return;u.onPointerGraceIntentChange(null)}})),onKeyDown:(0,o.M)(e.onKeyDown,t=>{let n=""!==u.searchRef.current;if(!e.disabled&&(!n||" "!==t.key)&&M[a.dir].includes(t.key)){var o;r.onOpenChange(!0),null===(o=r.content)||void 0===o||o.focus(),t.preventDefault()}})})})});eP.displayName=ek;var eE="MenuSubContent",eM=n.forwardRef((e,t)=>{let r=U(H,e.__scopeMenu),{forceMount:a=r.forceMount,...l}=e,u=z(H,e.__scopeMenu),c=F(H,e.__scopeMenu),s=eO(eE,e.__scopeMenu),d=n.useRef(null),f=(0,i.e)(t,d);return(0,j.jsx)(A.Provider,{scope:e.__scopeMenu,children:(0,j.jsx)(m.z,{present:a||u.open,children:(0,j.jsx)(A.Slot,{scope:e.__scopeMenu,children:(0,j.jsx)(ee,{id:s.contentId,"aria-labelledby":s.triggerId,...l,ref:f,align:"start",side:"rtl"===c.dir?"left":"right",disableOutsidePointerEvents:!1,disableOutsideScroll:!1,trapFocus:!1,onOpenAutoFocus:e=>{var t;c.isUsingKeyboardRef.current&&(null===(t=d.current)||void 0===t||t.focus()),e.preventDefault()},onCloseAutoFocus:e=>e.preventDefault(),onFocusOutside:(0,o.M)(e.onFocusOutside,e=>{e.target!==s.trigger&&u.onOpenChange(!1)}),onEscapeKeyDown:(0,o.M)(e.onEscapeKeyDown,e=>{c.onClose(),e.preventDefault()}),onKeyDown:(0,o.M)(e.onKeyDown,e=>{let t=e.currentTarget.contains(e.target),r=S[c.dir].includes(e.key);if(t&&r){var n;u.onOpenChange(!1),null===(n=s.trigger)||void 0===n||n.focus(),e.preventDefault()}})})})})})});function eS(e){return e?"open":"closed"}function eC(e){return"indeterminate"===e}function eA(e){return eC(e)?"indeterminate":e?"checked":"unchecked"}function eT(e){return t=>"mouse"===t.pointerType?e(t):void 0}eM.displayName=eE;var eD="DropdownMenu",[eN,e_]=(0,a.b)(eD,[_]),eI=_(),[eR,eL]=eN(eD),ez=e=>{let{__scopeDropdownMenu:t,children:r,dir:o,open:i,defaultOpen:a,onOpenChange:u,modal:c=!0}=e,s=eI(t),d=n.useRef(null),[f,p]=(0,l.T)({prop:i,defaultProp:null!=a&&a,onChange:u,caller:eD});return(0,j.jsx)(eR,{scope:t,triggerId:(0,h.M)(),triggerRef:d,contentId:(0,h.M)(),open:f,onOpenChange:p,onOpenToggle:n.useCallback(()=>p(e=>!e),[p]),modal:c,children:(0,j.jsx)(B,{...s,open:f,onOpenChange:p,dir:o,modal:c,children:r})})};ez.displayName=eD;var eW="DropdownMenuTrigger",eF=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,disabled:n=!1,...a}=e,l=eL(eW,r),c=eI(r);return(0,j.jsx)(K,{asChild:!0,...c,children:(0,j.jsx)(u.WV.button,{type:"button",id:l.triggerId,"aria-haspopup":"menu","aria-expanded":l.open,"aria-controls":l.open?l.contentId:void 0,"data-state":l.open?"open":"closed","data-disabled":n?"":void 0,disabled:n,...a,ref:(0,i.F)(t,l.triggerRef),onPointerDown:(0,o.M)(e.onPointerDown,e=>{n||0!==e.button||!1!==e.ctrlKey||(l.onOpenToggle(),l.open||e.preventDefault())}),onKeyDown:(0,o.M)(e.onKeyDown,e=>{!n&&(["Enter"," "].includes(e.key)&&l.onOpenToggle(),"ArrowDown"===e.key&&l.onOpenChange(!0),["Enter"," ","ArrowDown"].includes(e.key)&&e.preventDefault())})})})});eF.displayName=eW;var eB=e=>{let{__scopeDropdownMenu:t,...r}=e,n=eI(t);return(0,j.jsx)($,{...n,...r})};eB.displayName="DropdownMenuPortal";var eK="DropdownMenuContent",eV=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...i}=e,a=eL(eK,r),l=eI(r),u=n.useRef(!1);return(0,j.jsx)(X,{id:a.contentId,"aria-labelledby":a.triggerId,...l,...i,ref:t,onCloseAutoFocus:(0,o.M)(e.onCloseAutoFocus,e=>{var t;u.current||null===(t=a.triggerRef.current)||void 0===t||t.focus(),u.current=!1,e.preventDefault()}),onInteractOutside:(0,o.M)(e.onInteractOutside,e=>{let t=e.detail.originalEvent,r=0===t.button&&!0===t.ctrlKey,n=2===t.button||r;(!a.modal||n)&&(u.current=!0)}),style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});eV.displayName=eK;var eZ=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=eI(r);return(0,j.jsx)(et,{...o,...n,ref:t})});eZ.displayName="DropdownMenuGroup";var eU=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=eI(r);return(0,j.jsx)(er,{...o,...n,ref:t})});eU.displayName="DropdownMenuLabel";var e$=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=eI(r);return(0,j.jsx)(ei,{...o,...n,ref:t})});e$.displayName="DropdownMenuItem";var eH=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=eI(r);return(0,j.jsx)(el,{...o,...n,ref:t})});eH.displayName="DropdownMenuCheckboxItem";var eY=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=eI(r);return(0,j.jsx)(ed,{...o,...n,ref:t})});eY.displayName="DropdownMenuRadioGroup";var eq=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=eI(r);return(0,j.jsx)(ep,{...o,...n,ref:t})});eq.displayName="DropdownMenuRadioItem";var eX=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=eI(r);return(0,j.jsx)(em,{...o,...n,ref:t})});eX.displayName="DropdownMenuItemIndicator";var eG=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=eI(r);return(0,j.jsx)(eg,{...o,...n,ref:t})});eG.displayName="DropdownMenuSeparator",n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=eI(r);return(0,j.jsx)(eb,{...o,...n,ref:t})}).displayName="DropdownMenuArrow";var eQ=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=eI(r);return(0,j.jsx)(eP,{...o,...n,ref:t})});eQ.displayName="DropdownMenuSubTrigger";var eJ=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=eI(r);return(0,j.jsx)(eM,{...o,...n,ref:t,style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});eJ.displayName="DropdownMenuSubContent";var e0=ez,e1=eF,e2=eB,e6=eV,e5=eZ,e3=eU,e4=e$,e9=eH,e7=eY,e8=eq,te=eX,tt=eG,tr=e=>{let{__scopeDropdownMenu:t,children:r,open:n,onOpenChange:o,defaultOpen:i}=e,a=eI(t),[u,c]=(0,l.T)({prop:n,defaultProp:null!=i&&i,onChange:o,caller:"DropdownMenuSub"});return(0,j.jsx)(ej,{...a,open:u,onOpenChange:c,children:r})},tn=eQ,to=eJ},86097:function(e,t,r){"use strict";r.d(t,{EW:function(){return i}});var n=r(2265),o=0;function i(){n.useEffect(()=>{var e,t;let r=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",null!==(e=r[0])&&void 0!==e?e:a()),document.body.insertAdjacentElement("beforeend",null!==(t=r[1])&&void 0!==t?t:a()),o++,()=>{1===o&&document.querySelectorAll("[data-radix-focus-guard]").forEach(e=>e.remove()),o--}},[])}function a(){let e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}},99103:function(e,t,r){"use strict";let n;r.d(t,{M:function(){return f}});var o=r(2265),i=r(98575),a=r(66840),l=r(26606),u=r(57437),c="focusScope.autoFocusOnMount",s="focusScope.autoFocusOnUnmount",d={bubbles:!1,cancelable:!0},f=o.forwardRef((e,t)=>{let{loop:r=!1,trapped:n=!1,onMountAutoFocus:f,onUnmountAutoFocus:m,...g}=e,[b,w]=o.useState(null),x=(0,l.W)(f),O=(0,l.W)(m),j=o.useRef(null),k=(0,i.e)(t,e=>w(e)),P=o.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;o.useEffect(()=>{if(n){let e=function(e){if(P.paused||!b)return;let t=e.target;b.contains(t)?j.current=t:v(j.current,{select:!0})},t=function(e){if(P.paused||!b)return;let t=e.relatedTarget;null===t||b.contains(t)||v(j.current,{select:!0})};document.addEventListener("focusin",e),document.addEventListener("focusout",t);let r=new MutationObserver(function(e){if(document.activeElement===document.body)for(let t of e)t.removedNodes.length>0&&v(b)});return b&&r.observe(b,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),r.disconnect()}}},[n,b,P.paused]),o.useEffect(()=>{if(b){y.add(P);let e=document.activeElement;if(!b.contains(e)){let t=new CustomEvent(c,d);b.addEventListener(c,x),b.dispatchEvent(t),t.defaultPrevented||(function(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=document.activeElement;for(let n of e)if(v(n,{select:t}),document.activeElement!==r)return}(p(b).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&v(b))}return()=>{b.removeEventListener(c,x),setTimeout(()=>{let t=new CustomEvent(s,d);b.addEventListener(s,O),b.dispatchEvent(t),t.defaultPrevented||v(null!=e?e:document.body,{select:!0}),b.removeEventListener(s,O),y.remove(P)},0)}}},[b,x,O,P]);let E=o.useCallback(e=>{if(!r&&!n||P.paused)return;let t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,o=document.activeElement;if(t&&o){let t=e.currentTarget,[n,i]=function(e){let t=p(e);return[h(t,e),h(t.reverse(),e)]}(t);n&&i?e.shiftKey||o!==i?e.shiftKey&&o===n&&(e.preventDefault(),r&&v(i,{select:!0})):(e.preventDefault(),r&&v(n,{select:!0})):o===t&&e.preventDefault()}},[r,n,P.paused]);return(0,u.jsx)(a.WV.div,{tabIndex:-1,...g,ref:k,onKeyDown:E})});function p(e){let t=[],r=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;r.nextNode();)t.push(r.currentNode);return t}function h(e,t){for(let r of e)if(!function(e,t){let{upTo:r}=t;if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===r||e!==r);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(r,{upTo:t}))return r}function v(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(e&&e.focus){var r;let n=document.activeElement;e.focus({preventScroll:!0}),e!==n&&(r=e)instanceof HTMLInputElement&&"select"in r&&t&&e.select()}}f.displayName="FocusScope";var y=(n=[],{add(e){let t=n[0];e!==t&&(null==t||t.pause()),(n=m(n,e)).unshift(e)},remove(e){var t;null===(t=(n=m(n,e))[0])||void 0===t||t.resume()}});function m(e,t){let r=[...e],n=r.indexOf(t);return -1!==n&&r.splice(n,1),r}},99255:function(e,t,r){"use strict";r.d(t,{M:function(){return u}});var n,o=r(2265),i=r(61188),a=(n||(n=r.t(o,2)))[" useId ".trim().toString()]||(()=>void 0),l=0;function u(e){let[t,r]=o.useState(a());return(0,i.b)(()=>{e||r(e=>e??String(l++))},[e]),e||(t?`radix-${t}`:"")}},6394:function(e,t,r){"use strict";r.d(t,{f:function(){return l}});var n=r(2265),o=r(66840),i=r(57437),a=n.forwardRef((e,t)=>(0,i.jsx)(o.WV.label,{...e,ref:t,onMouseDown:t=>{var r;t.target.closest("button, input, select, textarea")||(null===(r=e.onMouseDown)||void 0===r||r.call(e,t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));a.displayName="Label";var l=a},96806:function(e,t,r){"use strict";r.d(t,{ee:function(){return $},Eh:function(){return Y},VY:function(){return H},fC:function(){return U},D7:function(){return C}});var n=r(2265),o=r(50032),i=r(54887),a="undefined"!=typeof document?n.useLayoutEffect:function(){};function l(e,t){let r,n,o;if(e===t)return!0;if(typeof e!=typeof t)return!1;if("function"==typeof e&&e.toString()===t.toString())return!0;if(e&&t&&"object"==typeof e){if(Array.isArray(e)){if((r=e.length)!==t.length)return!1;for(n=r;0!=n--;)if(!l(e[n],t[n]))return!1;return!0}if((r=(o=Object.keys(e)).length)!==Object.keys(t).length)return!1;for(n=r;0!=n--;)if(!({}).hasOwnProperty.call(t,o[n]))return!1;for(n=r;0!=n--;){let r=o[n];if(("_owner"!==r||!e.$$typeof)&&!l(e[r],t[r]))return!1}return!0}return e!=e&&t!=t}function u(e){return"undefined"==typeof window?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function c(e,t){let r=u(e);return Math.round(t*r)/r}function s(e){let t=n.useRef(e);return a(()=>{t.current=e}),t}let d=e=>({name:"arrow",options:e,fn(t){let{element:r,padding:n}="function"==typeof e?e(t):e;return r&&({}).hasOwnProperty.call(r,"current")?null!=r.current?(0,o.x7)({element:r.current,padding:n}).fn(t):{}:r?(0,o.x7)({element:r,padding:n}).fn(t):{}}}),f=(e,t)=>({...(0,o.cv)(e),options:[e,t]}),p=(e,t)=>({...(0,o.uY)(e),options:[e,t]}),h=(e,t)=>({...(0,o.dr)(e),options:[e,t]}),v=(e,t)=>({...(0,o.RR)(e),options:[e,t]}),y=(e,t)=>({...(0,o.dp)(e),options:[e,t]}),m=(e,t)=>({...(0,o.Cp)(e),options:[e,t]}),g=(e,t)=>({...d(e),options:[e,t]});var b=r(66840),w=r(57437),x=n.forwardRef((e,t)=>{let{children:r,width:n=10,height:o=5,...i}=e;return(0,w.jsx)(b.WV.svg,{...i,ref:t,width:n,height:o,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?r:(0,w.jsx)("polygon",{points:"0,0 30,0 15,10"})})});x.displayName="Arrow";var O=r(98575),j=r(73966),k=r(26606),P=r(61188),E=r(90420),M="Popper",[S,C]=(0,j.b)(M),[A,T]=S(M),D=e=>{let{__scopePopper:t,children:r}=e,[o,i]=n.useState(null);return(0,w.jsx)(A,{scope:t,anchor:o,onAnchorChange:i,children:r})};D.displayName=M;var N="PopperAnchor",_=n.forwardRef((e,t)=>{let{__scopePopper:r,virtualRef:o,...i}=e,a=T(N,r),l=n.useRef(null),u=(0,O.e)(t,l);return n.useEffect(()=>{a.onAnchorChange((null==o?void 0:o.current)||l.current)}),o?null:(0,w.jsx)(b.WV.div,{...i,ref:u})});_.displayName=N;var I="PopperContent",[R,L]=S(I),z=n.forwardRef((e,t)=>{var r,d,x,j,M,S,C,A;let{__scopePopper:D,side:N="bottom",sideOffset:_=0,align:L="center",alignOffset:z=0,arrowPadding:W=0,avoidCollisions:F=!0,collisionBoundary:B=[],collisionPadding:U=0,sticky:$="partial",hideWhenDetached:H=!1,updatePositionStrategy:Y="optimized",onPlaced:q,...X}=e,G=T(I,D),[Q,J]=n.useState(null),ee=(0,O.e)(t,e=>J(e)),[et,er]=n.useState(null),en=(0,E.t)(et),eo=null!==(C=null==en?void 0:en.width)&&void 0!==C?C:0,ei=null!==(A=null==en?void 0:en.height)&&void 0!==A?A:0,ea="number"==typeof U?U:{top:0,right:0,bottom:0,left:0,...U},el=Array.isArray(B)?B:[B],eu=el.length>0,ec={padding:ea,boundary:el.filter(K),altBoundary:eu},{refs:es,floatingStyles:ed,placement:ef,isPositioned:ep,middlewareData:eh}=function(e){void 0===e&&(e={});let{placement:t="bottom",strategy:r="absolute",middleware:d=[],platform:f,elements:{reference:p,floating:h}={},transform:v=!0,whileElementsMounted:y,open:m}=e,[g,b]=n.useState({x:0,y:0,strategy:r,placement:t,middlewareData:{},isPositioned:!1}),[w,x]=n.useState(d);l(w,d)||x(d);let[O,j]=n.useState(null),[k,P]=n.useState(null),E=n.useCallback(e=>{e!==A.current&&(A.current=e,j(e))},[]),M=n.useCallback(e=>{e!==T.current&&(T.current=e,P(e))},[]),S=p||O,C=h||k,A=n.useRef(null),T=n.useRef(null),D=n.useRef(g),N=null!=y,_=s(y),I=s(f),R=s(m),L=n.useCallback(()=>{if(!A.current||!T.current)return;let e={placement:t,strategy:r,middleware:w};I.current&&(e.platform=I.current),(0,o.oo)(A.current,T.current,e).then(e=>{let t={...e,isPositioned:!1!==R.current};z.current&&!l(D.current,t)&&(D.current=t,i.flushSync(()=>{b(t)}))})},[w,t,r,I,R]);a(()=>{!1===m&&D.current.isPositioned&&(D.current.isPositioned=!1,b(e=>({...e,isPositioned:!1})))},[m]);let z=n.useRef(!1);a(()=>(z.current=!0,()=>{z.current=!1}),[]),a(()=>{if(S&&(A.current=S),C&&(T.current=C),S&&C){if(_.current)return _.current(S,C,L);L()}},[S,C,L,_,N]);let W=n.useMemo(()=>({reference:A,floating:T,setReference:E,setFloating:M}),[E,M]),F=n.useMemo(()=>({reference:S,floating:C}),[S,C]),B=n.useMemo(()=>{let e={position:r,left:0,top:0};if(!F.floating)return e;let t=c(F.floating,g.x),n=c(F.floating,g.y);return v?{...e,transform:"translate("+t+"px, "+n+"px)",...u(F.floating)>=1.5&&{willChange:"transform"}}:{position:r,left:t,top:n}},[r,v,F.floating,g.x,g.y]);return n.useMemo(()=>({...g,update:L,refs:W,elements:F,floatingStyles:B}),[g,L,W,F,B])}({strategy:"fixed",placement:N+("center"!==L?"-"+L:""),whileElementsMounted:function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,o.Me)(...t,{animationFrame:"always"===Y})},elements:{reference:G.anchor},middleware:[f({mainAxis:_+ei,alignmentAxis:z}),F&&p({mainAxis:!0,crossAxis:!1,limiter:"partial"===$?h():void 0,...ec}),F&&v({...ec}),y({...ec,apply:e=>{let{elements:t,rects:r,availableWidth:n,availableHeight:o}=e,{width:i,height:a}=r.reference,l=t.floating.style;l.setProperty("--radix-popper-available-width","".concat(n,"px")),l.setProperty("--radix-popper-available-height","".concat(o,"px")),l.setProperty("--radix-popper-anchor-width","".concat(i,"px")),l.setProperty("--radix-popper-anchor-height","".concat(a,"px"))}}),et&&g({element:et,padding:W}),V({arrowWidth:eo,arrowHeight:ei}),H&&m({strategy:"referenceHidden",...ec})]}),[ev,ey]=Z(ef),em=(0,k.W)(q);(0,P.b)(()=>{ep&&(null==em||em())},[ep,em]);let eg=null===(r=eh.arrow)||void 0===r?void 0:r.x,eb=null===(d=eh.arrow)||void 0===d?void 0:d.y,ew=(null===(x=eh.arrow)||void 0===x?void 0:x.centerOffset)!==0,[ex,eO]=n.useState();return(0,P.b)(()=>{Q&&eO(window.getComputedStyle(Q).zIndex)},[Q]),(0,w.jsx)("div",{ref:es.setFloating,"data-radix-popper-content-wrapper":"",style:{...ed,transform:ep?ed.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:ex,"--radix-popper-transform-origin":[null===(j=eh.transformOrigin)||void 0===j?void 0:j.x,null===(M=eh.transformOrigin)||void 0===M?void 0:M.y].join(" "),...(null===(S=eh.hide)||void 0===S?void 0:S.referenceHidden)&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:(0,w.jsx)(R,{scope:D,placedSide:ev,onArrowChange:er,arrowX:eg,arrowY:eb,shouldHideArrow:ew,children:(0,w.jsx)(b.WV.div,{"data-side":ev,"data-align":ey,...X,ref:ee,style:{...X.style,animation:ep?void 0:"none"}})})})});z.displayName=I;var W="PopperArrow",F={top:"bottom",right:"left",bottom:"top",left:"right"},B=n.forwardRef(function(e,t){let{__scopePopper:r,...n}=e,o=L(W,r),i=F[o.placedSide];return(0,w.jsx)("span",{ref:o.onArrowChange,style:{position:"absolute",left:o.arrowX,top:o.arrowY,[i]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[o.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[o.placedSide],visibility:o.shouldHideArrow?"hidden":void 0},children:(0,w.jsx)(x,{...n,ref:t,style:{...n.style,display:"block"}})})});function K(e){return null!==e}B.displayName=W;var V=e=>({name:"transformOrigin",options:e,fn(t){var r,n,o,i,a;let{placement:l,rects:u,middlewareData:c}=t,s=(null===(r=c.arrow)||void 0===r?void 0:r.centerOffset)!==0,d=s?0:e.arrowWidth,f=s?0:e.arrowHeight,[p,h]=Z(l),v={start:"0%",center:"50%",end:"100%"}[h],y=(null!==(i=null===(n=c.arrow)||void 0===n?void 0:n.x)&&void 0!==i?i:0)+d/2,m=(null!==(a=null===(o=c.arrow)||void 0===o?void 0:o.y)&&void 0!==a?a:0)+f/2,g="",b="";return"bottom"===p?(g=s?v:"".concat(y,"px"),b="".concat(-f,"px")):"top"===p?(g=s?v:"".concat(y,"px"),b="".concat(u.floating.height+f,"px")):"right"===p?(g="".concat(-f,"px"),b=s?v:"".concat(m,"px")):"left"===p&&(g="".concat(u.floating.width+f,"px"),b=s?v:"".concat(m,"px")),{data:{x:g,y:b}}}});function Z(e){let[t,r="center"]=e.split("-");return[t,r]}var U=D,$=_,H=z,Y=B},83832:function(e,t,r){"use strict";r.d(t,{h:function(){return u}});var n=r(2265),o=r(54887),i=r(66840),a=r(61188),l=r(57437),u=n.forwardRef((e,t)=>{var r,u;let{container:c,...s}=e,[d,f]=n.useState(!1);(0,a.b)(()=>f(!0),[]);let p=c||d&&(null===(u=globalThis)||void 0===u?void 0:null===(r=u.document)||void 0===r?void 0:r.body);return p?o.createPortal((0,l.jsx)(i.WV.div,{...s,ref:t}),p):null});u.displayName="Portal"},71599:function(e,t,r){"use strict";r.d(t,{z:function(){return a}});var n=r(2265),o=r(98575),i=r(61188),a=e=>{var t,r;let a,u;let{present:c,children:s}=e,d=function(e){var t,r;let[o,a]=n.useState(),u=n.useRef(null),c=n.useRef(e),s=n.useRef("none"),[d,f]=(t=e?"mounted":"unmounted",r={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},n.useReducer((e,t)=>{let n=r[e][t];return null!=n?n:e},t));return n.useEffect(()=>{let e=l(u.current);s.current="mounted"===d?e:"none"},[d]),(0,i.b)(()=>{let t=u.current,r=c.current;if(r!==e){let n=s.current,o=l(t);e?f("MOUNT"):"none"===o||(null==t?void 0:t.display)==="none"?f("UNMOUNT"):r&&n!==o?f("ANIMATION_OUT"):f("UNMOUNT"),c.current=e}},[e,f]),(0,i.b)(()=>{if(o){var e;let t;let r=null!==(e=o.ownerDocument.defaultView)&&void 0!==e?e:window,n=e=>{let n=l(u.current).includes(e.animationName);if(e.target===o&&n&&(f("ANIMATION_END"),!c.current)){let e=o.style.animationFillMode;o.style.animationFillMode="forwards",t=r.setTimeout(()=>{"forwards"===o.style.animationFillMode&&(o.style.animationFillMode=e)})}},i=e=>{e.target===o&&(s.current=l(u.current))};return o.addEventListener("animationstart",i),o.addEventListener("animationcancel",n),o.addEventListener("animationend",n),()=>{r.clearTimeout(t),o.removeEventListener("animationstart",i),o.removeEventListener("animationcancel",n),o.removeEventListener("animationend",n)}}f("ANIMATION_END")},[o,f]),{isPresent:["mounted","unmountSuspended"].includes(d),ref:n.useCallback(e=>{u.current=e?getComputedStyle(e):null,a(e)},[])}}(c),f="function"==typeof s?s({present:d.isPresent}):n.Children.only(s),p=(0,o.e)(d.ref,(a=null===(t=Object.getOwnPropertyDescriptor(f.props,"ref"))||void 0===t?void 0:t.get)&&"isReactWarning"in a&&a.isReactWarning?f.ref:(a=null===(r=Object.getOwnPropertyDescriptor(f,"ref"))||void 0===r?void 0:r.get)&&"isReactWarning"in a&&a.isReactWarning?f.props.ref:f.props.ref||f.ref);return"function"==typeof s||d.isPresent?n.cloneElement(f,{ref:p}):null};function l(e){return(null==e?void 0:e.animationName)||"none"}a.displayName="Presence"},66840:function(e,t,r){"use strict";r.d(t,{WV:function(){return l},jH:function(){return u}});var n=r(2265),o=r(54887),i=r(7495),a=r(57437),l=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let r=(0,i.Z8)(`Primitive.${t}`),o=n.forwardRef((e,n)=>{let{asChild:o,...i}=e,l=o?r:t;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,a.jsx)(l,{...i,ref:n})});return o.displayName=`Primitive.${t}`,{...e,[t]:o}},{});function u(e,t){e&&o.flushSync(()=>e.dispatchEvent(t))}},1353:function(e,t,r){"use strict";r.d(t,{Pc:function(){return x},ck:function(){return T},fC:function(){return A}});var n=r(2265),o=r(6741),i=r(58068),a=r(98575),l=r(73966),u=r(99255),c=r(66840),s=r(26606),d=r(80886),f=r(29114),p=r(57437),h="rovingFocusGroup.onEntryFocus",v={bubbles:!1,cancelable:!0},y="RovingFocusGroup",[m,g,b]=(0,i.B)(y),[w,x]=(0,l.b)(y,[b]),[O,j]=w(y),k=n.forwardRef((e,t)=>(0,p.jsx)(m.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,p.jsx)(m.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,p.jsx)(P,{...e,ref:t})})}));k.displayName=y;var P=n.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:r,orientation:i,loop:l=!1,dir:u,currentTabStopId:m,defaultCurrentTabStopId:b,onCurrentTabStopIdChange:w,onEntryFocus:x,preventScrollOnEntryFocus:j=!1,...k}=e,P=n.useRef(null),E=(0,a.e)(t,P),M=(0,f.gm)(u),[S,A]=(0,d.T)({prop:m,defaultProp:null!=b?b:null,onChange:w,caller:y}),[T,D]=n.useState(!1),N=(0,s.W)(x),_=g(r),I=n.useRef(!1),[R,L]=n.useState(0);return n.useEffect(()=>{let e=P.current;if(e)return e.addEventListener(h,N),()=>e.removeEventListener(h,N)},[N]),(0,p.jsx)(O,{scope:r,orientation:i,dir:M,loop:l,currentTabStopId:S,onItemFocus:n.useCallback(e=>A(e),[A]),onItemShiftTab:n.useCallback(()=>D(!0),[]),onFocusableItemAdd:n.useCallback(()=>L(e=>e+1),[]),onFocusableItemRemove:n.useCallback(()=>L(e=>e-1),[]),children:(0,p.jsx)(c.WV.div,{tabIndex:T||0===R?-1:0,"data-orientation":i,...k,ref:E,style:{outline:"none",...e.style},onMouseDown:(0,o.M)(e.onMouseDown,()=>{I.current=!0}),onFocus:(0,o.M)(e.onFocus,e=>{let t=!I.current;if(e.target===e.currentTarget&&t&&!T){let t=new CustomEvent(h,v);if(e.currentTarget.dispatchEvent(t),!t.defaultPrevented){let e=_().filter(e=>e.focusable);C([e.find(e=>e.active),e.find(e=>e.id===S),...e].filter(Boolean).map(e=>e.ref.current),j)}}I.current=!1}),onBlur:(0,o.M)(e.onBlur,()=>D(!1))})})}),E="RovingFocusGroupItem",M=n.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:r,focusable:i=!0,active:a=!1,tabStopId:l,children:s,...d}=e,f=(0,u.M)(),h=l||f,v=j(E,r),y=v.currentTabStopId===h,b=g(r),{onFocusableItemAdd:w,onFocusableItemRemove:x,currentTabStopId:O}=v;return n.useEffect(()=>{if(i)return w(),()=>x()},[i,w,x]),(0,p.jsx)(m.ItemSlot,{scope:r,id:h,focusable:i,active:a,children:(0,p.jsx)(c.WV.span,{tabIndex:y?0:-1,"data-orientation":v.orientation,...d,ref:t,onMouseDown:(0,o.M)(e.onMouseDown,e=>{i?v.onItemFocus(h):e.preventDefault()}),onFocus:(0,o.M)(e.onFocus,()=>v.onItemFocus(h)),onKeyDown:(0,o.M)(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey){v.onItemShiftTab();return}if(e.target!==e.currentTarget)return;let t=function(e,t,r){var n;let o=(n=e.key,"rtl"!==r?n:"ArrowLeft"===n?"ArrowRight":"ArrowRight"===n?"ArrowLeft":n);if(!("vertical"===t&&["ArrowLeft","ArrowRight"].includes(o))&&!("horizontal"===t&&["ArrowUp","ArrowDown"].includes(o)))return S[o]}(e,v.orientation,v.dir);if(void 0!==t){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let o=b().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===t)o.reverse();else if("prev"===t||"next"===t){var r,n;"prev"===t&&o.reverse();let i=o.indexOf(e.currentTarget);o=v.loop?(r=o,n=i+1,r.map((e,t)=>r[(n+t)%r.length])):o.slice(i+1)}setTimeout(()=>C(o))}}),children:"function"==typeof s?s({isCurrentTabStop:y,hasTabStop:null!=O}):s})})});M.displayName=E;var S={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function C(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],r=document.activeElement;for(let n of e)if(n===r||(n.focus({preventScroll:t}),document.activeElement!==r))return}var A=k,T=M},93402:function(e,t,r){"use strict";r.d(t,{VY:function(){return eI},ZA:function(){return eL},JO:function(){return eN},ck:function(){return eW},wU:function(){return eB},eT:function(){return eF},__:function(){return ez},h_:function(){return e_},fC:function(){return eA},$G:function(){return eV},u_:function(){return eK},Z0:function(){return eZ},xz:function(){return eT},B4:function(){return eD},l_:function(){return eR}});var n=r(2265),o=r(54887);function i(e,[t,r]){return Math.min(r,Math.max(t,e))}var a=r(6741),l=r(58068),u=r(98575),c=r(73966),s=r(29114),d=r(15278),f=r(86097),p=r(99103),h=r(99255),v=r(96806),y=r(83832),m=r(66840),g=r(7495),b=r(26606),w=r(80886),x=r(61188),O=r(6718),j=r(95098),k=r(5478),P=r(87922),E=r(57437),M=[" ","Enter","ArrowUp","ArrowDown"],S=[" ","Enter"],C="Select",[A,T,D]=(0,l.B)(C),[N,_]=(0,c.b)(C,[D,v.D7]),I=(0,v.D7)(),[R,L]=N(C),[z,W]=N(C),F=e=>{let{__scopeSelect:t,children:r,open:o,defaultOpen:i,onOpenChange:a,value:l,defaultValue:u,onValueChange:c,dir:d,name:f,autoComplete:p,disabled:y,required:m,form:g}=e,b=I(t),[x,O]=n.useState(null),[j,k]=n.useState(null),[P,M]=n.useState(!1),S=(0,s.gm)(d),[T,D]=(0,w.T)({prop:o,defaultProp:null!=i&&i,onChange:a,caller:C}),[N,_]=(0,w.T)({prop:l,defaultProp:u,onChange:c,caller:C}),L=n.useRef(null),W=!x||g||!!x.closest("form"),[F,B]=n.useState(new Set),K=Array.from(F).map(e=>e.props.value).join(";");return(0,E.jsx)(v.fC,{...b,children:(0,E.jsxs)(R,{required:m,scope:t,trigger:x,onTriggerChange:O,valueNode:j,onValueNodeChange:k,valueNodeHasChildren:P,onValueNodeHasChildrenChange:M,contentId:(0,h.M)(),value:N,onValueChange:_,open:T,onOpenChange:D,dir:S,triggerPointerDownPosRef:L,disabled:y,children:[(0,E.jsx)(A.Provider,{scope:t,children:(0,E.jsx)(z,{scope:e.__scopeSelect,onNativeOptionAdd:n.useCallback(e=>{B(t=>new Set(t).add(e))},[]),onNativeOptionRemove:n.useCallback(e=>{B(t=>{let r=new Set(t);return r.delete(e),r})},[]),children:r})}),W?(0,E.jsxs)(eE,{"aria-hidden":!0,required:m,tabIndex:-1,name:f,autoComplete:p,value:N,onChange:e=>_(e.target.value),disabled:y,form:g,children:[void 0===N?(0,E.jsx)("option",{value:""}):null,Array.from(F)]},K):null]})})};F.displayName=C;var B="SelectTrigger",K=n.forwardRef((e,t)=>{let{__scopeSelect:r,disabled:o=!1,...i}=e,l=I(r),c=L(B,r),s=c.disabled||o,d=(0,u.e)(t,c.onTriggerChange),f=T(r),p=n.useRef("touch"),[h,y,g]=eS(e=>{let t=f().filter(e=>!e.disabled),r=t.find(e=>e.value===c.value),n=eC(t,e,r);void 0!==n&&c.onValueChange(n.value)}),b=e=>{s||(c.onOpenChange(!0),g()),e&&(c.triggerPointerDownPosRef.current={x:Math.round(e.pageX),y:Math.round(e.pageY)})};return(0,E.jsx)(v.ee,{asChild:!0,...l,children:(0,E.jsx)(m.WV.button,{type:"button",role:"combobox","aria-controls":c.contentId,"aria-expanded":c.open,"aria-required":c.required,"aria-autocomplete":"none",dir:c.dir,"data-state":c.open?"open":"closed",disabled:s,"data-disabled":s?"":void 0,"data-placeholder":eM(c.value)?"":void 0,...i,ref:d,onClick:(0,a.M)(i.onClick,e=>{e.currentTarget.focus(),"mouse"!==p.current&&b(e)}),onPointerDown:(0,a.M)(i.onPointerDown,e=>{p.current=e.pointerType;let t=e.target;t.hasPointerCapture(e.pointerId)&&t.releasePointerCapture(e.pointerId),0===e.button&&!1===e.ctrlKey&&"mouse"===e.pointerType&&(b(e),e.preventDefault())}),onKeyDown:(0,a.M)(i.onKeyDown,e=>{let t=""!==h.current;e.ctrlKey||e.altKey||e.metaKey||1!==e.key.length||y(e.key),(!t||" "!==e.key)&&M.includes(e.key)&&(b(),e.preventDefault())})})})});K.displayName=B;var V="SelectValue",Z=n.forwardRef((e,t)=>{let{__scopeSelect:r,className:n,style:o,children:i,placeholder:a="",...l}=e,c=L(V,r),{onValueNodeHasChildrenChange:s}=c,d=void 0!==i,f=(0,u.e)(t,c.onValueNodeChange);return(0,x.b)(()=>{s(d)},[s,d]),(0,E.jsx)(m.WV.span,{...l,ref:f,style:{pointerEvents:"none"},children:eM(c.value)?(0,E.jsx)(E.Fragment,{children:a}):i})});Z.displayName=V;var U=n.forwardRef((e,t)=>{let{__scopeSelect:r,children:n,...o}=e;return(0,E.jsx)(m.WV.span,{"aria-hidden":!0,...o,ref:t,children:n||"▼"})});U.displayName="SelectIcon";var $=e=>(0,E.jsx)(y.h,{asChild:!0,...e});$.displayName="SelectPortal";var H="SelectContent",Y=n.forwardRef((e,t)=>{let r=L(H,e.__scopeSelect),[i,a]=n.useState();return((0,x.b)(()=>{a(new DocumentFragment)},[]),r.open)?(0,E.jsx)(Q,{...e,ref:t}):i?o.createPortal((0,E.jsx)(q,{scope:e.__scopeSelect,children:(0,E.jsx)(A.Slot,{scope:e.__scopeSelect,children:(0,E.jsx)("div",{children:e.children})})}),i):null});Y.displayName=H;var[q,X]=N(H),G=(0,g.Z8)("SelectContent.RemoveScroll"),Q=n.forwardRef((e,t)=>{let{__scopeSelect:r,position:o="item-aligned",onCloseAutoFocus:i,onEscapeKeyDown:l,onPointerDownOutside:c,side:s,sideOffset:h,align:v,alignOffset:y,arrowPadding:m,collisionBoundary:g,collisionPadding:b,sticky:w,hideWhenDetached:x,avoidCollisions:O,...j}=e,M=L(H,r),[S,C]=n.useState(null),[A,D]=n.useState(null),N=(0,u.e)(t,e=>C(e)),[_,I]=n.useState(null),[R,z]=n.useState(null),W=T(r),[F,B]=n.useState(!1),K=n.useRef(!1);n.useEffect(()=>{if(S)return(0,k.Ry)(S)},[S]),(0,f.EW)();let V=n.useCallback(e=>{let[t,...r]=W().map(e=>e.ref.current),[n]=r.slice(-1),o=document.activeElement;for(let r of e)if(r===o||(null==r||r.scrollIntoView({block:"nearest"}),r===t&&A&&(A.scrollTop=0),r===n&&A&&(A.scrollTop=A.scrollHeight),null==r||r.focus(),document.activeElement!==o))return},[W,A]),Z=n.useCallback(()=>V([_,S]),[V,_,S]);n.useEffect(()=>{F&&Z()},[F,Z]);let{onOpenChange:U,triggerPointerDownPosRef:$}=M;n.useEffect(()=>{if(S){let e={x:0,y:0},t=t=>{var r,n,o,i;e={x:Math.abs(Math.round(t.pageX)-(null!==(o=null===(r=$.current)||void 0===r?void 0:r.x)&&void 0!==o?o:0)),y:Math.abs(Math.round(t.pageY)-(null!==(i=null===(n=$.current)||void 0===n?void 0:n.y)&&void 0!==i?i:0))}},r=r=>{e.x<=10&&e.y<=10?r.preventDefault():S.contains(r.target)||U(!1),document.removeEventListener("pointermove",t),$.current=null};return null!==$.current&&(document.addEventListener("pointermove",t),document.addEventListener("pointerup",r,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",t),document.removeEventListener("pointerup",r,{capture:!0})}}},[S,U,$]),n.useEffect(()=>{let e=()=>U(!1);return window.addEventListener("blur",e),window.addEventListener("resize",e),()=>{window.removeEventListener("blur",e),window.removeEventListener("resize",e)}},[U]);let[Y,X]=eS(e=>{let t=W().filter(e=>!e.disabled),r=t.find(e=>e.ref.current===document.activeElement),n=eC(t,e,r);n&&setTimeout(()=>n.ref.current.focus())}),Q=n.useCallback((e,t,r)=>{let n=!K.current&&!r;(void 0!==M.value&&M.value===t||n)&&(I(e),n&&(K.current=!0))},[M.value]),et=n.useCallback(()=>null==S?void 0:S.focus(),[S]),er=n.useCallback((e,t,r)=>{let n=!K.current&&!r;(void 0!==M.value&&M.value===t||n)&&z(e)},[M.value]),en="popper"===o?ee:J,eo=en===ee?{side:s,sideOffset:h,align:v,alignOffset:y,arrowPadding:m,collisionBoundary:g,collisionPadding:b,sticky:w,hideWhenDetached:x,avoidCollisions:O}:{};return(0,E.jsx)(q,{scope:r,content:S,viewport:A,onViewportChange:D,itemRefCallback:Q,selectedItem:_,onItemLeave:et,itemTextRefCallback:er,focusSelectedItem:Z,selectedItemText:R,position:o,isPositioned:F,searchRef:Y,children:(0,E.jsx)(P.Z,{as:G,allowPinchZoom:!0,children:(0,E.jsx)(p.M,{asChild:!0,trapped:M.open,onMountAutoFocus:e=>{e.preventDefault()},onUnmountAutoFocus:(0,a.M)(i,e=>{var t;null===(t=M.trigger)||void 0===t||t.focus({preventScroll:!0}),e.preventDefault()}),children:(0,E.jsx)(d.XB,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:l,onPointerDownOutside:c,onFocusOutside:e=>e.preventDefault(),onDismiss:()=>M.onOpenChange(!1),children:(0,E.jsx)(en,{role:"listbox",id:M.contentId,"data-state":M.open?"open":"closed",dir:M.dir,onContextMenu:e=>e.preventDefault(),...j,...eo,onPlaced:()=>B(!0),ref:N,style:{display:"flex",flexDirection:"column",outline:"none",...j.style},onKeyDown:(0,a.M)(j.onKeyDown,e=>{let t=e.ctrlKey||e.altKey||e.metaKey;if("Tab"===e.key&&e.preventDefault(),t||1!==e.key.length||X(e.key),["ArrowUp","ArrowDown","Home","End"].includes(e.key)){let t=W().filter(e=>!e.disabled).map(e=>e.ref.current);if(["ArrowUp","End"].includes(e.key)&&(t=t.slice().reverse()),["ArrowUp","ArrowDown"].includes(e.key)){let r=e.target,n=t.indexOf(r);t=t.slice(n+1)}setTimeout(()=>V(t)),e.preventDefault()}})})})})})})});Q.displayName="SelectContentImpl";var J=n.forwardRef((e,t)=>{let{__scopeSelect:r,onPlaced:o,...a}=e,l=L(H,r),c=X(H,r),[s,d]=n.useState(null),[f,p]=n.useState(null),h=(0,u.e)(t,e=>p(e)),v=T(r),y=n.useRef(!1),g=n.useRef(!0),{viewport:b,selectedItem:w,selectedItemText:O,focusSelectedItem:j}=c,k=n.useCallback(()=>{if(l.trigger&&l.valueNode&&s&&f&&b&&w&&O){let e=l.trigger.getBoundingClientRect(),t=f.getBoundingClientRect(),r=l.valueNode.getBoundingClientRect(),n=O.getBoundingClientRect();if("rtl"!==l.dir){let o=n.left-t.left,a=r.left-o,l=e.left-a,u=e.width+l,c=Math.max(u,t.width),d=i(a,[10,Math.max(10,window.innerWidth-10-c)]);s.style.minWidth=u+"px",s.style.left=d+"px"}else{let o=t.right-n.right,a=window.innerWidth-r.right-o,l=window.innerWidth-e.right-a,u=e.width+l,c=Math.max(u,t.width),d=i(a,[10,Math.max(10,window.innerWidth-10-c)]);s.style.minWidth=u+"px",s.style.right=d+"px"}let a=v(),u=window.innerHeight-20,c=b.scrollHeight,d=window.getComputedStyle(f),p=parseInt(d.borderTopWidth,10),h=parseInt(d.paddingTop,10),m=parseInt(d.borderBottomWidth,10),g=p+h+c+parseInt(d.paddingBottom,10)+m,x=Math.min(5*w.offsetHeight,g),j=window.getComputedStyle(b),k=parseInt(j.paddingTop,10),P=parseInt(j.paddingBottom,10),E=e.top+e.height/2-10,M=w.offsetHeight/2,S=p+h+(w.offsetTop+M);if(S<=E){let e=a.length>0&&w===a[a.length-1].ref.current;s.style.bottom="0px";let t=f.clientHeight-b.offsetTop-b.offsetHeight;s.style.height=S+Math.max(u-E,M+(e?P:0)+t+m)+"px"}else{let e=a.length>0&&w===a[0].ref.current;s.style.top="0px";let t=Math.max(E,p+b.offsetTop+(e?k:0)+M);s.style.height=t+(g-S)+"px",b.scrollTop=S-E+b.offsetTop}s.style.margin="".concat(10,"px 0"),s.style.minHeight=x+"px",s.style.maxHeight=u+"px",null==o||o(),requestAnimationFrame(()=>y.current=!0)}},[v,l.trigger,l.valueNode,s,f,b,w,O,l.dir,o]);(0,x.b)(()=>k(),[k]);let[P,M]=n.useState();(0,x.b)(()=>{f&&M(window.getComputedStyle(f).zIndex)},[f]);let S=n.useCallback(e=>{e&&!0===g.current&&(k(),null==j||j(),g.current=!1)},[k,j]);return(0,E.jsx)(et,{scope:r,contentWrapper:s,shouldExpandOnScrollRef:y,onScrollButtonChange:S,children:(0,E.jsx)("div",{ref:d,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:P},children:(0,E.jsx)(m.WV.div,{...a,ref:h,style:{boxSizing:"border-box",maxHeight:"100%",...a.style}})})})});J.displayName="SelectItemAlignedPosition";var ee=n.forwardRef((e,t)=>{let{__scopeSelect:r,align:n="start",collisionPadding:o=10,...i}=e,a=I(r);return(0,E.jsx)(v.VY,{...a,...i,ref:t,align:n,collisionPadding:o,style:{boxSizing:"border-box",...i.style,"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"}})});ee.displayName="SelectPopperPosition";var[et,er]=N(H,{}),en="SelectViewport",eo=n.forwardRef((e,t)=>{let{__scopeSelect:r,nonce:o,...i}=e,l=X(en,r),c=er(en,r),s=(0,u.e)(t,l.onViewportChange),d=n.useRef(0);return(0,E.jsxs)(E.Fragment,{children:[(0,E.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce:o}),(0,E.jsx)(A.Slot,{scope:r,children:(0,E.jsx)(m.WV.div,{"data-radix-select-viewport":"",role:"presentation",...i,ref:s,style:{position:"relative",flex:1,overflow:"hidden auto",...i.style},onScroll:(0,a.M)(i.onScroll,e=>{let t=e.currentTarget,{contentWrapper:r,shouldExpandOnScrollRef:n}=c;if((null==n?void 0:n.current)&&r){let e=Math.abs(d.current-t.scrollTop);if(e>0){let n=window.innerHeight-20,o=Math.max(parseFloat(r.style.minHeight),parseFloat(r.style.height));if(o<n){let i=o+e,a=Math.min(n,i),l=i-a;r.style.height=a+"px","0px"===r.style.bottom&&(t.scrollTop=l>0?l:0,r.style.justifyContent="flex-end")}}}d.current=t.scrollTop})})})]})});eo.displayName=en;var ei="SelectGroup",[ea,el]=N(ei),eu=n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e,o=(0,h.M)();return(0,E.jsx)(ea,{scope:r,id:o,children:(0,E.jsx)(m.WV.div,{role:"group","aria-labelledby":o,...n,ref:t})})});eu.displayName=ei;var ec="SelectLabel",es=n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e,o=el(ec,r);return(0,E.jsx)(m.WV.div,{id:o.id,...n,ref:t})});es.displayName=ec;var ed="SelectItem",[ef,ep]=N(ed),eh=n.forwardRef((e,t)=>{let{__scopeSelect:r,value:o,disabled:i=!1,textValue:l,...c}=e,s=L(ed,r),d=X(ed,r),f=s.value===o,[p,v]=n.useState(null!=l?l:""),[y,g]=n.useState(!1),b=(0,u.e)(t,e=>{var t;return null===(t=d.itemRefCallback)||void 0===t?void 0:t.call(d,e,o,i)}),w=(0,h.M)(),x=n.useRef("touch"),O=()=>{i||(s.onValueChange(o),s.onOpenChange(!1))};if(""===o)throw Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return(0,E.jsx)(ef,{scope:r,value:o,disabled:i,textId:w,isSelected:f,onItemTextChange:n.useCallback(e=>{v(t=>{var r;return t||(null!==(r=null==e?void 0:e.textContent)&&void 0!==r?r:"").trim()})},[]),children:(0,E.jsx)(A.ItemSlot,{scope:r,value:o,disabled:i,textValue:p,children:(0,E.jsx)(m.WV.div,{role:"option","aria-labelledby":w,"data-highlighted":y?"":void 0,"aria-selected":f&&y,"data-state":f?"checked":"unchecked","aria-disabled":i||void 0,"data-disabled":i?"":void 0,tabIndex:i?void 0:-1,...c,ref:b,onFocus:(0,a.M)(c.onFocus,()=>g(!0)),onBlur:(0,a.M)(c.onBlur,()=>g(!1)),onClick:(0,a.M)(c.onClick,()=>{"mouse"!==x.current&&O()}),onPointerUp:(0,a.M)(c.onPointerUp,()=>{"mouse"===x.current&&O()}),onPointerDown:(0,a.M)(c.onPointerDown,e=>{x.current=e.pointerType}),onPointerMove:(0,a.M)(c.onPointerMove,e=>{if(x.current=e.pointerType,i){var t;null===(t=d.onItemLeave)||void 0===t||t.call(d)}else"mouse"===x.current&&e.currentTarget.focus({preventScroll:!0})}),onPointerLeave:(0,a.M)(c.onPointerLeave,e=>{if(e.currentTarget===document.activeElement){var t;null===(t=d.onItemLeave)||void 0===t||t.call(d)}}),onKeyDown:(0,a.M)(c.onKeyDown,e=>{var t;(null===(t=d.searchRef)||void 0===t?void 0:t.current)!==""&&" "===e.key||(S.includes(e.key)&&O()," "===e.key&&e.preventDefault())})})})})});eh.displayName=ed;var ev="SelectItemText",ey=n.forwardRef((e,t)=>{let{__scopeSelect:r,className:i,style:a,...l}=e,c=L(ev,r),s=X(ev,r),d=ep(ev,r),f=W(ev,r),[p,h]=n.useState(null),v=(0,u.e)(t,e=>h(e),d.onItemTextChange,e=>{var t;return null===(t=s.itemTextRefCallback)||void 0===t?void 0:t.call(s,e,d.value,d.disabled)}),y=null==p?void 0:p.textContent,g=n.useMemo(()=>(0,E.jsx)("option",{value:d.value,disabled:d.disabled,children:y},d.value),[d.disabled,d.value,y]),{onNativeOptionAdd:b,onNativeOptionRemove:w}=f;return(0,x.b)(()=>(b(g),()=>w(g)),[b,w,g]),(0,E.jsxs)(E.Fragment,{children:[(0,E.jsx)(m.WV.span,{id:d.textId,...l,ref:v}),d.isSelected&&c.valueNode&&!c.valueNodeHasChildren?o.createPortal(l.children,c.valueNode):null]})});ey.displayName=ev;var em="SelectItemIndicator",eg=n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e;return ep(em,r).isSelected?(0,E.jsx)(m.WV.span,{"aria-hidden":!0,...n,ref:t}):null});eg.displayName=em;var eb="SelectScrollUpButton",ew=n.forwardRef((e,t)=>{let r=X(eb,e.__scopeSelect),o=er(eb,e.__scopeSelect),[i,a]=n.useState(!1),l=(0,u.e)(t,o.onScrollButtonChange);return(0,x.b)(()=>{if(r.viewport&&r.isPositioned){let e=function(){a(t.scrollTop>0)},t=r.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[r.viewport,r.isPositioned]),i?(0,E.jsx)(ej,{...e,ref:l,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=r;e&&t&&(e.scrollTop=e.scrollTop-t.offsetHeight)}}):null});ew.displayName=eb;var ex="SelectScrollDownButton",eO=n.forwardRef((e,t)=>{let r=X(ex,e.__scopeSelect),o=er(ex,e.__scopeSelect),[i,a]=n.useState(!1),l=(0,u.e)(t,o.onScrollButtonChange);return(0,x.b)(()=>{if(r.viewport&&r.isPositioned){let e=function(){let e=t.scrollHeight-t.clientHeight;a(Math.ceil(t.scrollTop)<e)},t=r.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[r.viewport,r.isPositioned]),i?(0,E.jsx)(ej,{...e,ref:l,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=r;e&&t&&(e.scrollTop=e.scrollTop+t.offsetHeight)}}):null});eO.displayName=ex;var ej=n.forwardRef((e,t)=>{let{__scopeSelect:r,onAutoScroll:o,...i}=e,l=X("SelectScrollButton",r),u=n.useRef(null),c=T(r),s=n.useCallback(()=>{null!==u.current&&(window.clearInterval(u.current),u.current=null)},[]);return n.useEffect(()=>()=>s(),[s]),(0,x.b)(()=>{var e;let t=c().find(e=>e.ref.current===document.activeElement);null==t||null===(e=t.ref.current)||void 0===e||e.scrollIntoView({block:"nearest"})},[c]),(0,E.jsx)(m.WV.div,{"aria-hidden":!0,...i,ref:t,style:{flexShrink:0,...i.style},onPointerDown:(0,a.M)(i.onPointerDown,()=>{null===u.current&&(u.current=window.setInterval(o,50))}),onPointerMove:(0,a.M)(i.onPointerMove,()=>{var e;null===(e=l.onItemLeave)||void 0===e||e.call(l),null===u.current&&(u.current=window.setInterval(o,50))}),onPointerLeave:(0,a.M)(i.onPointerLeave,()=>{s()})})}),ek=n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e;return(0,E.jsx)(m.WV.div,{"aria-hidden":!0,...n,ref:t})});ek.displayName="SelectSeparator";var eP="SelectArrow";n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e,o=I(r),i=L(eP,r),a=X(eP,r);return i.open&&"popper"===a.position?(0,E.jsx)(v.Eh,{...o,...n,ref:t}):null}).displayName=eP;var eE=n.forwardRef((e,t)=>{let{__scopeSelect:r,value:o,...i}=e,a=n.useRef(null),l=(0,u.e)(t,a),c=(0,O.D)(o);return n.useEffect(()=>{let e=a.current;if(!e)return;let t=Object.getOwnPropertyDescriptor(window.HTMLSelectElement.prototype,"value").set;if(c!==o&&t){let r=new Event("change",{bubbles:!0});t.call(e,o),e.dispatchEvent(r)}},[c,o]),(0,E.jsx)(m.WV.select,{...i,style:{...j.C2,...i.style},ref:l,defaultValue:o})});function eM(e){return""===e||void 0===e}function eS(e){let t=(0,b.W)(e),r=n.useRef(""),o=n.useRef(0),i=n.useCallback(e=>{let n=r.current+e;t(n),function e(t){r.current=t,window.clearTimeout(o.current),""!==t&&(o.current=window.setTimeout(()=>e(""),1e3))}(n)},[t]),a=n.useCallback(()=>{r.current="",window.clearTimeout(o.current)},[]);return n.useEffect(()=>()=>window.clearTimeout(o.current),[]),[r,i,a]}function eC(e,t,r){var n;let o=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,i=(n=Math.max(r?e.indexOf(r):-1,0),e.map((t,r)=>e[(n+r)%e.length]));1===o.length&&(i=i.filter(e=>e!==r));let a=i.find(e=>e.textValue.toLowerCase().startsWith(o.toLowerCase()));return a!==r?a:void 0}eE.displayName="SelectBubbleInput";var eA=F,eT=K,eD=Z,eN=U,e_=$,eI=Y,eR=eo,eL=eu,ez=es,eW=eh,eF=ey,eB=eg,eK=ew,eV=eO,eZ=ek},7495:function(e,t,r){"use strict";r.d(t,{Z8:function(){return a},g7:function(){return l},sA:function(){return c}});var n=r(2265),o=r(98575),i=r(57437);function a(e){let t=function(e){let t=n.forwardRef((e,t)=>{let{children:r,...i}=e;if(n.isValidElement(r)){let e,a;let l=(e=Object.getOwnPropertyDescriptor(r.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?r.ref:(e=Object.getOwnPropertyDescriptor(r,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?r.props.ref:r.props.ref||r.ref,u=function(e,t){let r={...t};for(let n in t){let o=e[n],i=t[n];/^on[A-Z]/.test(n)?o&&i?r[n]=(...e)=>{let t=i(...e);return o(...e),t}:o&&(r[n]=o):"style"===n?r[n]={...o,...i}:"className"===n&&(r[n]=[o,i].filter(Boolean).join(" "))}return{...e,...r}}(i,r.props);return r.type!==n.Fragment&&(u.ref=t?(0,o.F)(t,l):l),n.cloneElement(r,u)}return n.Children.count(r)>1?n.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),r=n.forwardRef((e,r)=>{let{children:o,...a}=e,l=n.Children.toArray(o),u=l.find(s);if(u){let e=u.props.children,o=l.map(t=>t!==u?t:n.Children.count(e)>1?n.Children.only(null):n.isValidElement(e)?e.props.children:null);return(0,i.jsx)(t,{...a,ref:r,children:n.isValidElement(e)?n.cloneElement(e,void 0,o):null})}return(0,i.jsx)(t,{...a,ref:r,children:o})});return r.displayName=`${e}.Slot`,r}var l=a("Slot"),u=Symbol("radix.slottable");function c(e){let t=({children:e})=>(0,i.jsx)(i.Fragment,{children:e});return t.displayName=`${e}.Slottable`,t.__radixId=u,t}function s(e){return n.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===u}},50721:function(e,t,r){"use strict";r.d(t,{bU:function(){return j},fC:function(){return O}});var n=r(2265),o=r(6741),i=r(98575),a=r(73966),l=r(80886),u=r(6718),c=r(90420),s=r(66840),d=r(57437),f="Switch",[p,h]=(0,a.b)(f),[v,y]=p(f),m=n.forwardRef((e,t)=>{let{__scopeSwitch:r,name:a,checked:u,defaultChecked:c,required:p,disabled:h,value:y="on",onCheckedChange:m,form:g,...b}=e,[O,j]=n.useState(null),k=(0,i.e)(t,e=>j(e)),P=n.useRef(!1),E=!O||g||!!O.closest("form"),[M,S]=(0,l.T)({prop:u,defaultProp:null!=c&&c,onChange:m,caller:f});return(0,d.jsxs)(v,{scope:r,checked:M,disabled:h,children:[(0,d.jsx)(s.WV.button,{type:"button",role:"switch","aria-checked":M,"aria-required":p,"data-state":x(M),"data-disabled":h?"":void 0,disabled:h,value:y,...b,ref:k,onClick:(0,o.M)(e.onClick,e=>{S(e=>!e),E&&(P.current=e.isPropagationStopped(),P.current||e.stopPropagation())})}),E&&(0,d.jsx)(w,{control:O,bubbles:!P.current,name:a,value:y,checked:M,required:p,disabled:h,form:g,style:{transform:"translateX(-100%)"}})]})});m.displayName=f;var g="SwitchThumb",b=n.forwardRef((e,t)=>{let{__scopeSwitch:r,...n}=e,o=y(g,r);return(0,d.jsx)(s.WV.span,{"data-state":x(o.checked),"data-disabled":o.disabled?"":void 0,...n,ref:t})});b.displayName=g;var w=n.forwardRef((e,t)=>{let{__scopeSwitch:r,control:o,checked:a,bubbles:l=!0,...s}=e,f=n.useRef(null),p=(0,i.e)(f,t),h=(0,u.D)(a),v=(0,c.t)(o);return n.useEffect(()=>{let e=f.current;if(!e)return;let t=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(h!==a&&t){let r=new Event("click",{bubbles:l});t.call(e,a),e.dispatchEvent(r)}},[h,a,l]),(0,d.jsx)("input",{type:"checkbox","aria-hidden":!0,defaultChecked:a,...s,tabIndex:-1,ref:p,style:{...s.style,...v,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})});function x(e){return e?"checked":"unchecked"}w.displayName="SwitchBubbleInput";var O=m,j=b},20271:function(e,t,r){"use strict";r.d(t,{VY:function(){return T},aV:function(){return C},fC:function(){return S},xz:function(){return A}});var n=r(2265),o=r(6741),i=r(73966),a=r(1353),l=r(71599),u=r(66840),c=r(29114),s=r(80886),d=r(99255),f=r(57437),p="Tabs",[h,v]=(0,i.b)(p,[a.Pc]),y=(0,a.Pc)(),[m,g]=h(p),b=n.forwardRef((e,t)=>{let{__scopeTabs:r,value:n,onValueChange:o,defaultValue:i,orientation:a="horizontal",dir:l,activationMode:h="automatic",...v}=e,y=(0,c.gm)(l),[g,b]=(0,s.T)({prop:n,onChange:o,defaultProp:null!=i?i:"",caller:p});return(0,f.jsx)(m,{scope:r,baseId:(0,d.M)(),value:g,onValueChange:b,orientation:a,dir:y,activationMode:h,children:(0,f.jsx)(u.WV.div,{dir:y,"data-orientation":a,...v,ref:t})})});b.displayName=p;var w="TabsList",x=n.forwardRef((e,t)=>{let{__scopeTabs:r,loop:n=!0,...o}=e,i=g(w,r),l=y(r);return(0,f.jsx)(a.fC,{asChild:!0,...l,orientation:i.orientation,dir:i.dir,loop:n,children:(0,f.jsx)(u.WV.div,{role:"tablist","aria-orientation":i.orientation,...o,ref:t})})});x.displayName=w;var O="TabsTrigger",j=n.forwardRef((e,t)=>{let{__scopeTabs:r,value:n,disabled:i=!1,...l}=e,c=g(O,r),s=y(r),d=E(c.baseId,n),p=M(c.baseId,n),h=n===c.value;return(0,f.jsx)(a.ck,{asChild:!0,...s,focusable:!i,active:h,children:(0,f.jsx)(u.WV.button,{type:"button",role:"tab","aria-selected":h,"aria-controls":p,"data-state":h?"active":"inactive","data-disabled":i?"":void 0,disabled:i,id:d,...l,ref:t,onMouseDown:(0,o.M)(e.onMouseDown,e=>{i||0!==e.button||!1!==e.ctrlKey?e.preventDefault():c.onValueChange(n)}),onKeyDown:(0,o.M)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&c.onValueChange(n)}),onFocus:(0,o.M)(e.onFocus,()=>{let e="manual"!==c.activationMode;h||i||!e||c.onValueChange(n)})})})});j.displayName=O;var k="TabsContent",P=n.forwardRef((e,t)=>{let{__scopeTabs:r,value:o,forceMount:i,children:a,...c}=e,s=g(k,r),d=E(s.baseId,o),p=M(s.baseId,o),h=o===s.value,v=n.useRef(h);return n.useEffect(()=>{let e=requestAnimationFrame(()=>v.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,f.jsx)(l.z,{present:i||h,children:r=>{let{present:n}=r;return(0,f.jsx)(u.WV.div,{"data-state":h?"active":"inactive","data-orientation":s.orientation,role:"tabpanel","aria-labelledby":d,hidden:!n,id:p,tabIndex:0,...c,ref:t,style:{...e.style,animationDuration:v.current?"0s":void 0},children:n&&a})}})});function E(e,t){return"".concat(e,"-trigger-").concat(t)}function M(e,t){return"".concat(e,"-content-").concat(t)}P.displayName=k;var S=b,C=x,A=j,T=P},41915:function(e,t,r){"use strict";r.d(t,{Dx:function(){return J},aU:function(){return et},dk:function(){return ee},fC:function(){return Q},l_:function(){return G},x8:function(){return er},zt:function(){return X}});var n=r(2265),o=r(54887),i=r(6741),a=r(98575),l=r(58068),u=r(73966),c=r(15278),s=r(83832),d=r(71599),f=r(66840),p=r(26606),h=r(80886),v=r(61188),y=r(95098),m=r(57437),g="ToastProvider",[b,w,x]=(0,l.B)("Toast"),[O,j]=(0,u.b)("Toast",[x]),[k,P]=O(g),E=e=>{let{__scopeToast:t,label:r="Notification",duration:o=5e3,swipeDirection:i="right",swipeThreshold:a=50,children:l}=e,[u,c]=n.useState(null),[s,d]=n.useState(0),f=n.useRef(!1),p=n.useRef(!1);return r.trim()||console.error("Invalid prop `label` supplied to `".concat(g,"`. Expected non-empty `string`.")),(0,m.jsx)(b.Provider,{scope:t,children:(0,m.jsx)(k,{scope:t,label:r,duration:o,swipeDirection:i,swipeThreshold:a,toastCount:s,viewport:u,onViewportChange:c,onToastAdd:n.useCallback(()=>d(e=>e+1),[]),onToastRemove:n.useCallback(()=>d(e=>e-1),[]),isFocusedToastEscapeKeyDownRef:f,isClosePausedRef:p,children:l})})};E.displayName=g;var M="ToastViewport",S=["F8"],C="toast.viewportPause",A="toast.viewportResume",T=n.forwardRef((e,t)=>{let{__scopeToast:r,hotkey:o=S,label:i="Notifications ({hotkey})",...l}=e,u=P(M,r),s=w(r),d=n.useRef(null),p=n.useRef(null),h=n.useRef(null),v=n.useRef(null),y=(0,a.e)(t,v,u.onViewportChange),g=o.join("+").replace(/Key/g,"").replace(/Digit/g,""),x=u.toastCount>0;n.useEffect(()=>{let e=e=>{var t;0!==o.length&&o.every(t=>e[t]||e.code===t)&&(null===(t=v.current)||void 0===t||t.focus())};return document.addEventListener("keydown",e),()=>document.removeEventListener("keydown",e)},[o]),n.useEffect(()=>{let e=d.current,t=v.current;if(x&&e&&t){let r=()=>{if(!u.isClosePausedRef.current){let e=new CustomEvent(C);t.dispatchEvent(e),u.isClosePausedRef.current=!0}},n=()=>{if(u.isClosePausedRef.current){let e=new CustomEvent(A);t.dispatchEvent(e),u.isClosePausedRef.current=!1}},o=t=>{e.contains(t.relatedTarget)||n()},i=()=>{e.contains(document.activeElement)||n()};return e.addEventListener("focusin",r),e.addEventListener("focusout",o),e.addEventListener("pointermove",r),e.addEventListener("pointerleave",i),window.addEventListener("blur",r),window.addEventListener("focus",n),()=>{e.removeEventListener("focusin",r),e.removeEventListener("focusout",o),e.removeEventListener("pointermove",r),e.removeEventListener("pointerleave",i),window.removeEventListener("blur",r),window.removeEventListener("focus",n)}}},[x,u.isClosePausedRef]);let O=n.useCallback(e=>{let{tabbingDirection:t}=e,r=s().map(e=>{let r=e.ref.current,n=[r,...function(e){let t=[],r=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;r.nextNode();)t.push(r.currentNode);return t}(r)];return"forwards"===t?n:n.reverse()});return("forwards"===t?r.reverse():r).flat()},[s]);return n.useEffect(()=>{let e=v.current;if(e){let t=t=>{let r=t.altKey||t.ctrlKey||t.metaKey;if("Tab"===t.key&&!r){var n,o,i;let r=document.activeElement,a=t.shiftKey;if(t.target===e&&a){null===(n=p.current)||void 0===n||n.focus();return}let l=O({tabbingDirection:a?"backwards":"forwards"}),u=l.findIndex(e=>e===r);q(l.slice(u+1))?t.preventDefault():a?null===(o=p.current)||void 0===o||o.focus():null===(i=h.current)||void 0===i||i.focus()}};return e.addEventListener("keydown",t),()=>e.removeEventListener("keydown",t)}},[s,O]),(0,m.jsxs)(c.I0,{ref:d,role:"region","aria-label":i.replace("{hotkey}",g),tabIndex:-1,style:{pointerEvents:x?void 0:"none"},children:[x&&(0,m.jsx)(N,{ref:p,onFocusFromOutsideViewport:()=>{q(O({tabbingDirection:"forwards"}))}}),(0,m.jsx)(b.Slot,{scope:r,children:(0,m.jsx)(f.WV.ol,{tabIndex:-1,...l,ref:y})}),x&&(0,m.jsx)(N,{ref:h,onFocusFromOutsideViewport:()=>{q(O({tabbingDirection:"backwards"}))}})]})});T.displayName=M;var D="ToastFocusProxy",N=n.forwardRef((e,t)=>{let{__scopeToast:r,onFocusFromOutsideViewport:n,...o}=e,i=P(D,r);return(0,m.jsx)(y.TX,{"aria-hidden":!0,tabIndex:0,...o,ref:t,style:{position:"fixed"},onFocus:e=>{var t;let r=e.relatedTarget;(null===(t=i.viewport)||void 0===t?void 0:t.contains(r))||n()}})});N.displayName=D;var _="Toast",I=n.forwardRef((e,t)=>{let{forceMount:r,open:n,defaultOpen:o,onOpenChange:a,...l}=e,[u,c]=(0,h.T)({prop:n,defaultProp:null==o||o,onChange:a,caller:_});return(0,m.jsx)(d.z,{present:r||u,children:(0,m.jsx)(z,{open:u,...l,ref:t,onClose:()=>c(!1),onPause:(0,p.W)(e.onPause),onResume:(0,p.W)(e.onResume),onSwipeStart:(0,i.M)(e.onSwipeStart,e=>{e.currentTarget.setAttribute("data-swipe","start")}),onSwipeMove:(0,i.M)(e.onSwipeMove,e=>{let{x:t,y:r}=e.detail.delta;e.currentTarget.setAttribute("data-swipe","move"),e.currentTarget.style.setProperty("--radix-toast-swipe-move-x","".concat(t,"px")),e.currentTarget.style.setProperty("--radix-toast-swipe-move-y","".concat(r,"px"))}),onSwipeCancel:(0,i.M)(e.onSwipeCancel,e=>{e.currentTarget.setAttribute("data-swipe","cancel"),e.currentTarget.style.removeProperty("--radix-toast-swipe-move-x"),e.currentTarget.style.removeProperty("--radix-toast-swipe-move-y"),e.currentTarget.style.removeProperty("--radix-toast-swipe-end-x"),e.currentTarget.style.removeProperty("--radix-toast-swipe-end-y")}),onSwipeEnd:(0,i.M)(e.onSwipeEnd,e=>{let{x:t,y:r}=e.detail.delta;e.currentTarget.setAttribute("data-swipe","end"),e.currentTarget.style.removeProperty("--radix-toast-swipe-move-x"),e.currentTarget.style.removeProperty("--radix-toast-swipe-move-y"),e.currentTarget.style.setProperty("--radix-toast-swipe-end-x","".concat(t,"px")),e.currentTarget.style.setProperty("--radix-toast-swipe-end-y","".concat(r,"px")),c(!1)})})})});I.displayName=_;var[R,L]=O(_,{onClose(){}}),z=n.forwardRef((e,t)=>{let{__scopeToast:r,type:l="foreground",duration:u,open:s,onClose:d,onEscapeKeyDown:h,onPause:v,onResume:y,onSwipeStart:g,onSwipeMove:w,onSwipeCancel:x,onSwipeEnd:O,...j}=e,k=P(_,r),[E,M]=n.useState(null),S=(0,a.e)(t,e=>M(e)),T=n.useRef(null),D=n.useRef(null),N=u||k.duration,I=n.useRef(0),L=n.useRef(N),z=n.useRef(0),{onToastAdd:F,onToastRemove:B}=k,K=(0,p.W)(()=>{var e;(null==E?void 0:E.contains(document.activeElement))&&(null===(e=k.viewport)||void 0===e||e.focus()),d()}),V=n.useCallback(e=>{e&&e!==1/0&&(window.clearTimeout(z.current),I.current=new Date().getTime(),z.current=window.setTimeout(K,e))},[K]);n.useEffect(()=>{let e=k.viewport;if(e){let t=()=>{V(L.current),null==y||y()},r=()=>{let e=new Date().getTime()-I.current;L.current=L.current-e,window.clearTimeout(z.current),null==v||v()};return e.addEventListener(C,r),e.addEventListener(A,t),()=>{e.removeEventListener(C,r),e.removeEventListener(A,t)}}},[k.viewport,N,v,y,V]),n.useEffect(()=>{s&&!k.isClosePausedRef.current&&V(N)},[s,N,k.isClosePausedRef,V]),n.useEffect(()=>(F(),()=>B()),[F,B]);let Z=n.useMemo(()=>E?function e(t){let r=[];return Array.from(t.childNodes).forEach(t=>{if(t.nodeType===t.TEXT_NODE&&t.textContent&&r.push(t.textContent),t.nodeType===t.ELEMENT_NODE){let n=t.ariaHidden||t.hidden||"none"===t.style.display,o=""===t.dataset.radixToastAnnounceExclude;if(!n){if(o){let e=t.dataset.radixToastAnnounceAlt;e&&r.push(e)}else r.push(...e(t))}}}),r}(E):null,[E]);return k.viewport?(0,m.jsxs)(m.Fragment,{children:[Z&&(0,m.jsx)(W,{__scopeToast:r,role:"status","aria-live":"foreground"===l?"assertive":"polite","aria-atomic":!0,children:Z}),(0,m.jsx)(R,{scope:r,onClose:K,children:o.createPortal((0,m.jsx)(b.ItemSlot,{scope:r,children:(0,m.jsx)(c.fC,{asChild:!0,onEscapeKeyDown:(0,i.M)(h,()=>{k.isFocusedToastEscapeKeyDownRef.current||K(),k.isFocusedToastEscapeKeyDownRef.current=!1}),children:(0,m.jsx)(f.WV.li,{role:"status","aria-live":"off","aria-atomic":!0,tabIndex:0,"data-state":s?"open":"closed","data-swipe-direction":k.swipeDirection,...j,ref:S,style:{userSelect:"none",touchAction:"none",...e.style},onKeyDown:(0,i.M)(e.onKeyDown,e=>{"Escape"!==e.key||(null==h||h(e.nativeEvent),e.nativeEvent.defaultPrevented||(k.isFocusedToastEscapeKeyDownRef.current=!0,K()))}),onPointerDown:(0,i.M)(e.onPointerDown,e=>{0===e.button&&(T.current={x:e.clientX,y:e.clientY})}),onPointerMove:(0,i.M)(e.onPointerMove,e=>{if(!T.current)return;let t=e.clientX-T.current.x,r=e.clientY-T.current.y,n=!!D.current,o=["left","right"].includes(k.swipeDirection),i=["left","up"].includes(k.swipeDirection)?Math.min:Math.max,a=o?i(0,t):0,l=o?0:i(0,r),u="touch"===e.pointerType?10:2,c={x:a,y:l},s={originalEvent:e,delta:c};n?(D.current=c,H("toast.swipeMove",w,s,{discrete:!1})):Y(c,k.swipeDirection,u)?(D.current=c,H("toast.swipeStart",g,s,{discrete:!1}),e.target.setPointerCapture(e.pointerId)):(Math.abs(t)>u||Math.abs(r)>u)&&(T.current=null)}),onPointerUp:(0,i.M)(e.onPointerUp,e=>{let t=D.current,r=e.target;if(r.hasPointerCapture(e.pointerId)&&r.releasePointerCapture(e.pointerId),D.current=null,T.current=null,t){let r=e.currentTarget,n={originalEvent:e,delta:t};Y(t,k.swipeDirection,k.swipeThreshold)?H("toast.swipeEnd",O,n,{discrete:!0}):H("toast.swipeCancel",x,n,{discrete:!0}),r.addEventListener("click",e=>e.preventDefault(),{once:!0})}})})})}),k.viewport)})]}):null}),W=e=>{let{__scopeToast:t,children:r,...o}=e,i=P(_,t),[a,l]=n.useState(!1),[u,c]=n.useState(!1);return function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:()=>{},t=(0,p.W)(e);(0,v.b)(()=>{let e=0,r=0;return e=window.requestAnimationFrame(()=>r=window.requestAnimationFrame(t)),()=>{window.cancelAnimationFrame(e),window.cancelAnimationFrame(r)}},[t])}(()=>l(!0)),n.useEffect(()=>{let e=window.setTimeout(()=>c(!0),1e3);return()=>window.clearTimeout(e)},[]),u?null:(0,m.jsx)(s.h,{asChild:!0,children:(0,m.jsx)(y.TX,{...o,children:a&&(0,m.jsxs)(m.Fragment,{children:[i.label," ",r]})})})},F=n.forwardRef((e,t)=>{let{__scopeToast:r,...n}=e;return(0,m.jsx)(f.WV.div,{...n,ref:t})});F.displayName="ToastTitle";var B=n.forwardRef((e,t)=>{let{__scopeToast:r,...n}=e;return(0,m.jsx)(f.WV.div,{...n,ref:t})});B.displayName="ToastDescription";var K="ToastAction",V=n.forwardRef((e,t)=>{let{altText:r,...n}=e;return r.trim()?(0,m.jsx)($,{altText:r,asChild:!0,children:(0,m.jsx)(U,{...n,ref:t})}):(console.error("Invalid prop `altText` supplied to `".concat(K,"`. Expected non-empty `string`.")),null)});V.displayName=K;var Z="ToastClose",U=n.forwardRef((e,t)=>{let{__scopeToast:r,...n}=e,o=L(Z,r);return(0,m.jsx)($,{asChild:!0,children:(0,m.jsx)(f.WV.button,{type:"button",...n,ref:t,onClick:(0,i.M)(e.onClick,o.onClose)})})});U.displayName=Z;var $=n.forwardRef((e,t)=>{let{__scopeToast:r,altText:n,...o}=e;return(0,m.jsx)(f.WV.div,{"data-radix-toast-announce-exclude":"","data-radix-toast-announce-alt":n||void 0,...o,ref:t})});function H(e,t,r,n){let{discrete:o}=n,i=r.originalEvent.currentTarget,a=new CustomEvent(e,{bubbles:!0,cancelable:!0,detail:r});t&&i.addEventListener(e,t,{once:!0}),o?(0,f.jH)(i,a):i.dispatchEvent(a)}var Y=function(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,n=Math.abs(e.x),o=Math.abs(e.y),i=n>o;return"left"===t||"right"===t?i&&n>r:!i&&o>r};function q(e){let t=document.activeElement;return e.some(e=>e===t||(e.focus(),document.activeElement!==t))}var X=E,G=T,Q=I,J=F,ee=B,et=V,er=U},61312:function(e,t,r){"use strict";r.d(t,{VY:function(){return V},fC:function(){return B},xz:function(){return K},zt:function(){return F}});var n=r(2265),o=r(6741),i=r(98575),a=r(73966),l=r(15278),u=r(99255),c=r(96806),s=(r(83832),r(71599)),d=r(66840),f=r(7495),p=r(80886),h=r(95098),v=r(57437),[y,m]=(0,a.b)("Tooltip",[c.D7]),g=(0,c.D7)(),b="TooltipProvider",w="tooltip.open",[x,O]=y(b),j=e=>{let{__scopeTooltip:t,delayDuration:r=700,skipDelayDuration:o=300,disableHoverableContent:i=!1,children:a}=e,l=n.useRef(!0),u=n.useRef(!1),c=n.useRef(0);return n.useEffect(()=>{let e=c.current;return()=>window.clearTimeout(e)},[]),(0,v.jsx)(x,{scope:t,isOpenDelayedRef:l,delayDuration:r,onOpen:n.useCallback(()=>{window.clearTimeout(c.current),l.current=!1},[]),onClose:n.useCallback(()=>{window.clearTimeout(c.current),c.current=window.setTimeout(()=>l.current=!0,o)},[o]),isPointerInTransitRef:u,onPointerInTransitChange:n.useCallback(e=>{u.current=e},[]),disableHoverableContent:i,children:a})};j.displayName=b;var k="Tooltip",[P,E]=y(k),M=e=>{let{__scopeTooltip:t,children:r,open:o,defaultOpen:i,onOpenChange:a,disableHoverableContent:l,delayDuration:s}=e,d=O(k,e.__scopeTooltip),f=g(t),[h,y]=n.useState(null),m=(0,u.M)(),b=n.useRef(0),x=null!=l?l:d.disableHoverableContent,j=null!=s?s:d.delayDuration,E=n.useRef(!1),[M,S]=(0,p.T)({prop:o,defaultProp:null!=i&&i,onChange:e=>{e?(d.onOpen(),document.dispatchEvent(new CustomEvent(w))):d.onClose(),null==a||a(e)},caller:k}),C=n.useMemo(()=>M?E.current?"delayed-open":"instant-open":"closed",[M]),A=n.useCallback(()=>{window.clearTimeout(b.current),b.current=0,E.current=!1,S(!0)},[S]),T=n.useCallback(()=>{window.clearTimeout(b.current),b.current=0,S(!1)},[S]),D=n.useCallback(()=>{window.clearTimeout(b.current),b.current=window.setTimeout(()=>{E.current=!0,S(!0),b.current=0},j)},[j,S]);return n.useEffect(()=>()=>{b.current&&(window.clearTimeout(b.current),b.current=0)},[]),(0,v.jsx)(c.fC,{...f,children:(0,v.jsx)(P,{scope:t,contentId:m,open:M,stateAttribute:C,trigger:h,onTriggerChange:y,onTriggerEnter:n.useCallback(()=>{d.isOpenDelayedRef.current?D():A()},[d.isOpenDelayedRef,D,A]),onTriggerLeave:n.useCallback(()=>{x?T():(window.clearTimeout(b.current),b.current=0)},[T,x]),onOpen:A,onClose:T,disableHoverableContent:x,children:r})})};M.displayName=k;var S="TooltipTrigger",C=n.forwardRef((e,t)=>{let{__scopeTooltip:r,...a}=e,l=E(S,r),u=O(S,r),s=g(r),f=n.useRef(null),p=(0,i.e)(t,f,l.onTriggerChange),h=n.useRef(!1),y=n.useRef(!1),m=n.useCallback(()=>h.current=!1,[]);return n.useEffect(()=>()=>document.removeEventListener("pointerup",m),[m]),(0,v.jsx)(c.ee,{asChild:!0,...s,children:(0,v.jsx)(d.WV.button,{"aria-describedby":l.open?l.contentId:void 0,"data-state":l.stateAttribute,...a,ref:p,onPointerMove:(0,o.M)(e.onPointerMove,e=>{"touch"===e.pointerType||y.current||u.isPointerInTransitRef.current||(l.onTriggerEnter(),y.current=!0)}),onPointerLeave:(0,o.M)(e.onPointerLeave,()=>{l.onTriggerLeave(),y.current=!1}),onPointerDown:(0,o.M)(e.onPointerDown,()=>{l.open&&l.onClose(),h.current=!0,document.addEventListener("pointerup",m,{once:!0})}),onFocus:(0,o.M)(e.onFocus,()=>{h.current||l.onOpen()}),onBlur:(0,o.M)(e.onBlur,l.onClose),onClick:(0,o.M)(e.onClick,l.onClose)})})});C.displayName=S;var[A,T]=y("TooltipPortal",{forceMount:void 0}),D="TooltipContent",N=n.forwardRef((e,t)=>{let r=T(D,e.__scopeTooltip),{forceMount:n=r.forceMount,side:o="top",...i}=e,a=E(D,e.__scopeTooltip);return(0,v.jsx)(s.z,{present:n||a.open,children:a.disableHoverableContent?(0,v.jsx)(z,{side:o,...i,ref:t}):(0,v.jsx)(_,{side:o,...i,ref:t})})}),_=n.forwardRef((e,t)=>{let r=E(D,e.__scopeTooltip),o=O(D,e.__scopeTooltip),a=n.useRef(null),l=(0,i.e)(t,a),[u,c]=n.useState(null),{trigger:s,onClose:d}=r,f=a.current,{onPointerInTransitChange:p}=o,h=n.useCallback(()=>{c(null),p(!1)},[p]),y=n.useCallback((e,t)=>{let r=e.currentTarget,n={x:e.clientX,y:e.clientY},o=function(e,t){let r=Math.abs(t.top-e.y),n=Math.abs(t.bottom-e.y),o=Math.abs(t.right-e.x),i=Math.abs(t.left-e.x);switch(Math.min(r,n,o,i)){case i:return"left";case o:return"right";case r:return"top";case n:return"bottom";default:throw Error("unreachable")}}(n,r.getBoundingClientRect());c(function(e){let t=e.slice();return t.sort((e,t)=>e.x<t.x?-1:e.x>t.x?1:e.y<t.y?-1:e.y>t.y?1:0),function(e){if(e.length<=1)return e.slice();let t=[];for(let r=0;r<e.length;r++){let n=e[r];for(;t.length>=2;){let e=t[t.length-1],r=t[t.length-2];if((e.x-r.x)*(n.y-r.y)>=(e.y-r.y)*(n.x-r.x))t.pop();else break}t.push(n)}t.pop();let r=[];for(let t=e.length-1;t>=0;t--){let n=e[t];for(;r.length>=2;){let e=r[r.length-1],t=r[r.length-2];if((e.x-t.x)*(n.y-t.y)>=(e.y-t.y)*(n.x-t.x))r.pop();else break}r.push(n)}return(r.pop(),1===t.length&&1===r.length&&t[0].x===r[0].x&&t[0].y===r[0].y)?t:t.concat(r)}(t)}([...function(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:5,n=[];switch(t){case"top":n.push({x:e.x-r,y:e.y+r},{x:e.x+r,y:e.y+r});break;case"bottom":n.push({x:e.x-r,y:e.y-r},{x:e.x+r,y:e.y-r});break;case"left":n.push({x:e.x+r,y:e.y-r},{x:e.x+r,y:e.y+r});break;case"right":n.push({x:e.x-r,y:e.y-r},{x:e.x-r,y:e.y+r})}return n}(n,o),...function(e){let{top:t,right:r,bottom:n,left:o}=e;return[{x:o,y:t},{x:r,y:t},{x:r,y:n},{x:o,y:n}]}(t.getBoundingClientRect())])),p(!0)},[p]);return n.useEffect(()=>()=>h(),[h]),n.useEffect(()=>{if(s&&f){let e=e=>y(e,f),t=e=>y(e,s);return s.addEventListener("pointerleave",e),f.addEventListener("pointerleave",t),()=>{s.removeEventListener("pointerleave",e),f.removeEventListener("pointerleave",t)}}},[s,f,y,h]),n.useEffect(()=>{if(u){let e=e=>{let t=e.target,r={x:e.clientX,y:e.clientY},n=(null==s?void 0:s.contains(t))||(null==f?void 0:f.contains(t)),o=!function(e,t){let{x:r,y:n}=e,o=!1;for(let e=0,i=t.length-1;e<t.length;i=e++){let a=t[e],l=t[i],u=a.x,c=a.y,s=l.x,d=l.y;c>n!=d>n&&r<(s-u)*(n-c)/(d-c)+u&&(o=!o)}return o}(r,u);n?h():o&&(h(),d())};return document.addEventListener("pointermove",e),()=>document.removeEventListener("pointermove",e)}},[s,f,u,d,h]),(0,v.jsx)(z,{...e,ref:l})}),[I,R]=y(k,{isInside:!1}),L=(0,f.sA)("TooltipContent"),z=n.forwardRef((e,t)=>{let{__scopeTooltip:r,children:o,"aria-label":i,onEscapeKeyDown:a,onPointerDownOutside:u,...s}=e,d=E(D,r),f=g(r),{onClose:p}=d;return n.useEffect(()=>(document.addEventListener(w,p),()=>document.removeEventListener(w,p)),[p]),n.useEffect(()=>{if(d.trigger){let e=e=>{let t=e.target;(null==t?void 0:t.contains(d.trigger))&&p()};return window.addEventListener("scroll",e,{capture:!0}),()=>window.removeEventListener("scroll",e,{capture:!0})}},[d.trigger,p]),(0,v.jsx)(l.XB,{asChild:!0,disableOutsidePointerEvents:!1,onEscapeKeyDown:a,onPointerDownOutside:u,onFocusOutside:e=>e.preventDefault(),onDismiss:p,children:(0,v.jsxs)(c.VY,{"data-state":d.stateAttribute,...f,...s,ref:t,style:{...s.style,"--radix-tooltip-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-tooltip-content-available-width":"var(--radix-popper-available-width)","--radix-tooltip-content-available-height":"var(--radix-popper-available-height)","--radix-tooltip-trigger-width":"var(--radix-popper-anchor-width)","--radix-tooltip-trigger-height":"var(--radix-popper-anchor-height)"},children:[(0,v.jsx)(L,{children:o}),(0,v.jsx)(I,{scope:r,isInside:!0,children:(0,v.jsx)(h.fC,{id:d.contentId,role:"tooltip",children:i||o})})]})})});N.displayName=D;var W="TooltipArrow";n.forwardRef((e,t)=>{let{__scopeTooltip:r,...n}=e,o=g(r);return R(W,r).isInside?null:(0,v.jsx)(c.Eh,{...o,...n,ref:t})}).displayName=W;var F=j,B=M,K=C,V=N},26606:function(e,t,r){"use strict";r.d(t,{W:function(){return o}});var n=r(2265);function o(e){let t=n.useRef(e);return n.useEffect(()=>{t.current=e}),n.useMemo(()=>(...e)=>t.current?.(...e),[])}},80886:function(e,t,r){"use strict";r.d(t,{T:function(){return l}});var n,o=r(2265),i=r(61188),a=(n||(n=r.t(o,2)))[" useInsertionEffect ".trim().toString()]||i.b;function l({prop:e,defaultProp:t,onChange:r=()=>{},caller:n}){let[i,l,u]=function({defaultProp:e,onChange:t}){let[r,n]=o.useState(e),i=o.useRef(r),l=o.useRef(t);return a(()=>{l.current=t},[t]),o.useEffect(()=>{i.current!==r&&(l.current?.(r),i.current=r)},[r,i]),[r,n,l]}({defaultProp:t,onChange:r}),c=void 0!==e,s=c?e:i;{let t=o.useRef(void 0!==e);o.useEffect(()=>{let e=t.current;if(e!==c){let t=c?"controlled":"uncontrolled";console.warn(`${n} is changing from ${e?"controlled":"uncontrolled"} to ${t}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}t.current=c},[c,n])}return[s,o.useCallback(t=>{if(c){let r="function"==typeof t?t(e):t;r!==e&&u.current?.(r)}else l(t)},[c,e,l,u])]}Symbol("RADIX:SYNC_STATE")},61188:function(e,t,r){"use strict";r.d(t,{b:function(){return o}});var n=r(2265),o=globalThis?.document?n.useLayoutEffect:()=>{}},6718:function(e,t,r){"use strict";r.d(t,{D:function(){return o}});var n=r(2265);function o(e){let t=n.useRef({value:e,previous:e});return n.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}},90420:function(e,t,r){"use strict";r.d(t,{t:function(){return i}});var n=r(2265),o=r(61188);function i(e){let[t,r]=n.useState(void 0);return(0,o.b)(()=>{if(e){r({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let n,o;if(!Array.isArray(t)||!t.length)return;let i=t[0];if("borderBoxSize"in i){let e=i.borderBoxSize,t=Array.isArray(e)?e[0]:e;n=t.inlineSize,o=t.blockSize}else n=e.offsetWidth,o=e.offsetHeight;r({width:n,height:o})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}r(void 0)},[e]),t}},95098:function(e,t,r){"use strict";r.d(t,{C2:function(){return a},TX:function(){return l},fC:function(){return u}});var n=r(2265),o=r(66840),i=r(57437),a=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"}),l=n.forwardRef((e,t)=>(0,i.jsx)(o.WV.span,{...e,ref:t,style:{...a,...e.style}}));l.displayName="VisuallyHidden";var u=l},90535:function(e,t,r){"use strict";r.d(t,{j:function(){return a}});var n=r(61994);let o=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,i=n.W,a=(e,t)=>r=>{var n;if((null==t?void 0:t.variants)==null)return i(e,null==r?void 0:r.class,null==r?void 0:r.className);let{variants:a,defaultVariants:l}=t,u=Object.keys(a).map(e=>{let t=null==r?void 0:r[e],n=null==l?void 0:l[e];if(null===t)return null;let i=o(t)||o(n);return a[e][i]}),c=r&&Object.entries(r).reduce((e,t)=>{let[r,n]=t;return void 0===n||(e[r]=n),e},{});return i(e,u,null==t?void 0:null===(n=t.compoundVariants)||void 0===n?void 0:n.reduce((e,t)=>{let{class:r,className:n,...o}=t;return Object.entries(o).every(e=>{let[t,r]=e;return Array.isArray(r)?r.includes({...l,...c}[t]):({...l,...c})[t]===r})?[...e,r,n]:e},[]),null==r?void 0:r.class,null==r?void 0:r.className)}},22516:function(e,t,r){"use strict";function n(e){return"object"==typeof e&&"length"in e?e:Array.from(e)}r.d(t,{Z:function(){return n}}),Array.prototype.slice},76115:function(e,t,r){"use strict";function n(e){return function(){return e}}r.d(t,{Z:function(){return n}})},67790:function(e,t,r){"use strict";r.d(t,{d:function(){return u}});let n=Math.PI,o=2*n,i=o-1e-6;function a(e){this._+=e[0];for(let t=1,r=e.length;t<r;++t)this._+=arguments[t]+e[t]}class l{constructor(e){this._x0=this._y0=this._x1=this._y1=null,this._="",this._append=null==e?a:function(e){let t=Math.floor(e);if(!(t>=0))throw Error(`invalid digits: ${e}`);if(t>15)return a;let r=10**t;return function(e){this._+=e[0];for(let t=1,n=e.length;t<n;++t)this._+=Math.round(arguments[t]*r)/r+e[t]}}(e)}moveTo(e,t){this._append`M${this._x0=this._x1=+e},${this._y0=this._y1=+t}`}closePath(){null!==this._x1&&(this._x1=this._x0,this._y1=this._y0,this._append`Z`)}lineTo(e,t){this._append`L${this._x1=+e},${this._y1=+t}`}quadraticCurveTo(e,t,r,n){this._append`Q${+e},${+t},${this._x1=+r},${this._y1=+n}`}bezierCurveTo(e,t,r,n,o,i){this._append`C${+e},${+t},${+r},${+n},${this._x1=+o},${this._y1=+i}`}arcTo(e,t,r,o,i){if(e=+e,t=+t,r=+r,o=+o,(i=+i)<0)throw Error(`negative radius: ${i}`);let a=this._x1,l=this._y1,u=r-e,c=o-t,s=a-e,d=l-t,f=s*s+d*d;if(null===this._x1)this._append`M${this._x1=e},${this._y1=t}`;else if(f>1e-6){if(Math.abs(d*u-c*s)>1e-6&&i){let p=r-a,h=o-l,v=u*u+c*c,y=Math.sqrt(v),m=Math.sqrt(f),g=i*Math.tan((n-Math.acos((v+f-(p*p+h*h))/(2*y*m)))/2),b=g/m,w=g/y;Math.abs(b-1)>1e-6&&this._append`L${e+b*s},${t+b*d}`,this._append`A${i},${i},0,0,${+(d*p>s*h)},${this._x1=e+w*u},${this._y1=t+w*c}`}else this._append`L${this._x1=e},${this._y1=t}`}}arc(e,t,r,a,l,u){if(e=+e,t=+t,u=!!u,(r=+r)<0)throw Error(`negative radius: ${r}`);let c=r*Math.cos(a),s=r*Math.sin(a),d=e+c,f=t+s,p=1^u,h=u?a-l:l-a;null===this._x1?this._append`M${d},${f}`:(Math.abs(this._x1-d)>1e-6||Math.abs(this._y1-f)>1e-6)&&this._append`L${d},${f}`,r&&(h<0&&(h=h%o+o),h>i?this._append`A${r},${r},0,1,${p},${e-c},${t-s}A${r},${r},0,1,${p},${this._x1=d},${this._y1=f}`:h>1e-6&&this._append`A${r},${r},0,${+(h>=n)},${p},${this._x1=e+r*Math.cos(l)},${this._y1=t+r*Math.sin(l)}`)}rect(e,t,r,n){this._append`M${this._x0=this._x1=+e},${this._y0=this._y1=+t}h${r=+r}v${+n}h${-r}Z`}toString(){return this._}}function u(e){let t=3;return e.digits=function(r){if(!arguments.length)return t;if(null==r)t=null;else{let e=Math.floor(r);if(!(e>=0))throw RangeError(`invalid digits: ${r}`);t=e}return e},()=>new l(t)}l.prototype},96471:function(e,t,r){"use strict";r.d(t,{Z:function(){return u}});var n=r(2265);let o=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),i=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return t.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim()};var a={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let l=(0,n.forwardRef)((e,t)=>{let{color:r="currentColor",size:o=24,strokeWidth:l=2,absoluteStrokeWidth:u,className:c="",children:s,iconNode:d,...f}=e;return(0,n.createElement)("svg",{ref:t,...a,width:o,height:o,stroke:r,strokeWidth:u?24*Number(l)/Number(o):l,className:i("lucide",c),...f},[...d.map(e=>{let[t,r]=e;return(0,n.createElement)(t,r)}),...Array.isArray(s)?s:[s]])}),u=(e,t)=>{let r=(0,n.forwardRef)((r,a)=>{let{className:u,...c}=r;return(0,n.createElement)(l,{ref:a,iconNode:t,className:i("lucide-".concat(o(e)),u),...c})});return r.displayName="".concat(e),r}},12100:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});let n=(0,r(96471).Z)("ArrowDown",[["path",{d:"M12 5v14",key:"s699le"}],["path",{d:"m19 12-7 7-7-7",key:"1idqje"}]])},5485:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});let n=(0,r(96471).Z)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},10155:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});let n=(0,r(96471).Z)("ArrowUp",[["path",{d:"m5 12 7-7 7 7",key:"hav0vg"}],["path",{d:"M12 19V5",key:"x0mq9r"}]])},66961:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});let n=(0,r(96471).Z)("Baby",[["path",{d:"M9 12h.01",key:"157uk2"}],["path",{d:"M15 12h.01",key:"1k8ypt"}],["path",{d:"M10 16c.5.3 1.2.5 2 .5s1.5-.2 2-.5",key:"1u7htd"}],["path",{d:"M19 6.3a9 9 0 0 1 1.8 3.9 2 2 0 0 1 0 3.6 9 9 0 0 1-17.6 0 2 2 0 0 1 0-3.6A9 9 0 0 1 12 3c2 0 3.5 1.1 3.5 2.5s-.9 2.5-2 2.5c-.8 0-1.5-.4-1.5-1",key:"5yv0yz"}]])},29935:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});let n=(0,r(96471).Z)("Building2",[["path",{d:"M6 22V4a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2v18Z",key:"1b4qmf"}],["path",{d:"M6 12H4a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2h2",key:"i71pzd"}],["path",{d:"M18 9h2a2 2 0 0 1 2 2v9a2 2 0 0 1-2 2h-2",key:"10jefs"}],["path",{d:"M10 6h4",key:"1itunk"}],["path",{d:"M10 10h4",key:"tcdvrf"}],["path",{d:"M10 14h4",key:"kelpxr"}],["path",{d:"M10 18h4",key:"1ulq68"}]])},91120:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});let n=(0,r(96471).Z)("Building",[["rect",{width:"16",height:"20",x:"4",y:"2",rx:"2",ry:"2",key:"76otgf"}],["path",{d:"M9 22v-4h6v4",key:"r93iot"}],["path",{d:"M8 6h.01",key:"1dz90k"}],["path",{d:"M16 6h.01",key:"1x0f13"}],["path",{d:"M12 6h.01",key:"1vi96p"}],["path",{d:"M12 10h.01",key:"1nrarc"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M16 10h.01",key:"1m94wz"}],["path",{d:"M16 14h.01",key:"1gbofw"}],["path",{d:"M8 10h.01",key:"19clt8"}],["path",{d:"M8 14h.01",key:"6423bh"}]])},90283:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});let n=(0,r(96471).Z)("Calculator",[["rect",{width:"16",height:"20",x:"4",y:"2",rx:"2",key:"1nb95v"}],["line",{x1:"8",x2:"16",y1:"6",y2:"6",key:"x4nwl0"}],["line",{x1:"16",x2:"16",y1:"14",y2:"18",key:"wjye3r"}],["path",{d:"M16 10h.01",key:"1m94wz"}],["path",{d:"M12 10h.01",key:"1nrarc"}],["path",{d:"M8 10h.01",key:"19clt8"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M8 14h.01",key:"6423bh"}],["path",{d:"M12 18h.01",key:"mhygvu"}],["path",{d:"M8 18h.01",key:"lrp35t"}]])},13147:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});let n=(0,r(96471).Z)("Calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},59701:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});let n=(0,r(96471).Z)("ChartColumn",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]])},59559:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});let n=(0,r(96471).Z)("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},3289:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});let n=(0,r(96471).Z)("ChevronDown",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},76848:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});let n=(0,r(96471).Z)("ChevronRight",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},47969:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});let n=(0,r(96471).Z)("ChevronUp",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]])},5255:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});let n=(0,r(96471).Z)("CircleCheckBig",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},54233:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});let n=(0,r(96471).Z)("CirclePlus",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M8 12h8",key:"1wcyev"}],["path",{d:"M12 8v8",key:"napkw2"}]])},78084:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});let n=(0,r(96471).Z)("Circle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]])},81475:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});let n=(0,r(96471).Z)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},63390:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});let n=(0,r(96471).Z)("CreditCard",[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]])},54585:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});let n=(0,r(96471).Z)("DollarSign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]])},45736:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});let n=(0,r(96471).Z)("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]])},56141:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});let n=(0,r(96471).Z)("Eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},87928:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});let n=(0,r(96471).Z)("FileText",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},46581:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});let n=(0,r(96471).Z)("FileX",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"m14.5 12.5-5 5",key:"b62r18"}],["path",{d:"m9.5 12.5 5 5",key:"1rk7el"}]])},86383:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});let n=(0,r(96471).Z)("Heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]])},99049:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});let n=(0,r(96471).Z)("House",[["path",{d:"M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8",key:"5wwlr5"}],["path",{d:"M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"1d0kgt"}]])},75980:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});let n=(0,r(96471).Z)("Lightbulb",[["path",{d:"M15 14c.2-1 .7-1.7 1.5-2.5 1-.9 1.5-2.2 1.5-3.5A6 6 0 0 0 6 8c0 1 .2 2.2 1.5 3.5.7.7 1.3 1.5 1.5 2.5",key:"1gvzjb"}],["path",{d:"M9 18h6",key:"x1upvd"}],["path",{d:"M10 22h4",key:"ceow96"}]])},53459:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});let n=(0,r(96471).Z)("LoaderCircle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},5252:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});let n=(0,r(96471).Z)("Lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]])},3683:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});let n=(0,r(96471).Z)("LogOut",[["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}],["polyline",{points:"16 17 21 12 16 7",key:"1gabdz"}],["line",{x1:"21",x2:"9",y1:"12",y2:"12",key:"1uyos4"}]])},46747:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});let n=(0,r(96471).Z)("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]])},96818:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});let n=(0,r(96471).Z)("Menu",[["line",{x1:"4",x2:"20",y1:"12",y2:"12",key:"1e0a9i"}],["line",{x1:"4",x2:"20",y1:"6",y2:"6",key:"1owob3"}],["line",{x1:"4",x2:"20",y1:"18",y2:"18",key:"yk5zj1"}]])},5341:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});let n=(0,r(96471).Z)("MessageCircle",[["path",{d:"M7.9 20A9 9 0 1 0 4 16.1L2 22Z",key:"vv11sd"}]])},19965:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});let n=(0,r(96471).Z)("MessageSquare",[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]])},69626:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});let n=(0,r(96471).Z)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},65887:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});let n=(0,r(96471).Z)("Redo",[["path",{d:"M21 7v6h-6",key:"3ptur4"}],["path",{d:"M3 17a9 9 0 0 1 9-9 9 9 0 0 1 6 2.3l3 2.7",key:"1kgawr"}]])},66314:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});let n=(0,r(96471).Z)("RefreshCw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},23818:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});let n=(0,r(96471).Z)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])},79679:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});let n=(0,r(96471).Z)("Send",[["path",{d:"M14.536 21.686a.5.5 0 0 0 .937-.024l6.5-19a.496.496 0 0 0-.635-.635l-19 6.5a.5.5 0 0 0-.024.937l7.93 3.18a2 2 0 0 1 1.112 1.11z",key:"1ffxy3"}],["path",{d:"m21.854 2.147-10.94 10.939",key:"12cjpa"}]])},40339:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});let n=(0,r(96471).Z)("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},73932:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});let n=(0,r(96471).Z)("Share2",[["circle",{cx:"18",cy:"5",r:"3",key:"gq8acd"}],["circle",{cx:"6",cy:"12",r:"3",key:"w7nqdw"}],["circle",{cx:"18",cy:"19",r:"3",key:"1xt0gg"}],["line",{x1:"8.59",x2:"15.42",y1:"13.51",y2:"17.49",key:"47mynk"}],["line",{x1:"15.41",x2:"8.59",y1:"6.51",y2:"10.49",key:"1n3mei"}]])},36086:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});let n=(0,r(96471).Z)("Shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]])},26563:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});let n=(0,r(96471).Z)("Star",[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]])},93835:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});let n=(0,r(96471).Z)("Target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]])},79282:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});let n=(0,r(96471).Z)("TrendingDown",[["polyline",{points:"22 17 13.5 8.5 8.5 13.5 2 7",key:"1r2t7k"}],["polyline",{points:"16 17 22 17 22 11",key:"11uiuu"}]])},16275:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});let n=(0,r(96471).Z)("TrendingUp",[["polyline",{points:"22 7 13.5 15.5 8.5 10.5 2 17",key:"126l90"}],["polyline",{points:"16 7 22 7 22 13",key:"kwv8wd"}]])},12032:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});let n=(0,r(96471).Z)("TriangleAlert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},53927:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});let n=(0,r(96471).Z)("Upload",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"17 8 12 3 7 8",key:"t8dd8p"}],["line",{x1:"12",x2:"12",y1:"3",y2:"15",key:"widbto"}]])},16049:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});let n=(0,r(96471).Z)("UserCheck",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["polyline",{points:"16 11 18 13 22 9",key:"1pwet4"}]])},67350:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});let n=(0,r(96471).Z)("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},16994:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});let n=(0,r(96471).Z)("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]])},44986:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});let n=(0,r(96471).Z)("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},92713:function(e,t,r){"use strict";r.d(t,{P1:function(){return w}});var n=e=>Array.isArray(e)?e:[e],o=0,i=class{revision=o;_value;_lastValue;_isEqual=a;constructor(e,t=a){this._value=this._lastValue=e,this._isEqual=t}get value(){return this._value}set value(e){this.value!==e&&(this._value=e,this.revision=++o)}};function a(e,t){return e===t}function l(e){return e instanceof i||console.warn("Not a valid cell! ",e),e.value}var u=(e,t)=>!1;function c(){return function(e,t=a){return new i(null,t)}(0,u)}var s=e=>{let t=e.collectionTag;null===t&&(t=e.collectionTag=c()),l(t)};Symbol();var d=0,f=Object.getPrototypeOf({}),p=class{constructor(e){this.value=e,this.value=e,this.tag.value=e}proxy=new Proxy(this,h);tag=c();tags={};children={};collectionTag=null;id=d++},h={get:(e,t)=>(function(){let{value:r}=e,n=Reflect.get(r,t);if("symbol"==typeof t||t in f)return n;if("object"==typeof n&&null!==n){let r=e.children[t];return void 0===r&&(r=e.children[t]=Array.isArray(n)?new v(n):new p(n)),r.tag&&l(r.tag),r.proxy}{let r=e.tags[t];return void 0===r&&((r=e.tags[t]=c()).value=n),l(r),n}})(),ownKeys:e=>(s(e),Reflect.ownKeys(e.value)),getOwnPropertyDescriptor:(e,t)=>Reflect.getOwnPropertyDescriptor(e.value,t),has:(e,t)=>Reflect.has(e.value,t)},v=class{constructor(e){this.value=e,this.value=e,this.tag.value=e}proxy=new Proxy([this],y);tag=c();tags={};children={};collectionTag=null;id=d++},y={get:([e],t)=>("length"===t&&s(e),h.get(e,t)),ownKeys:([e])=>h.ownKeys(e),getOwnPropertyDescriptor:([e],t)=>h.getOwnPropertyDescriptor(e,t),has:([e],t)=>h.has(e,t)},m="undefined"!=typeof WeakRef?WeakRef:class{constructor(e){this.value=e}deref(){return this.value}};function g(){return{s:0,v:void 0,o:null,p:null}}function b(e,t={}){let r,n=g(),{resultEqualityCheck:o}=t,i=0;function a(){let t,a=n,{length:l}=arguments;for(let e=0;e<l;e++){let t=arguments[e];if("function"==typeof t||"object"==typeof t&&null!==t){let e=a.o;null===e&&(a.o=e=new WeakMap);let r=e.get(t);void 0===r?(a=g(),e.set(t,a)):a=r}else{let e=a.p;null===e&&(a.p=e=new Map);let r=e.get(t);void 0===r?(a=g(),e.set(t,a)):a=r}}let u=a;if(1===a.s)t=a.v;else if(t=e.apply(null,arguments),i++,o){let e=r?.deref?.()??r;null!=e&&o(e,t)&&(t=e,0!==i&&i--),r="object"==typeof t&&null!==t||"function"==typeof t?new m(t):t}return u.s=1,u.v=t,t}return a.clearCache=()=>{n=g(),a.resetResultsCount()},a.resultsCount=()=>i,a.resetResultsCount=()=>{i=0},a}var w=function(e,...t){let r="function"==typeof e?{memoize:e,memoizeOptions:t}:e,o=(...e)=>{let t,o=0,i=0,a={},l=e.pop();"object"==typeof l&&(a=l,l=e.pop()),function(e,t=`expected a function, instead received ${typeof e}`){if("function"!=typeof e)throw TypeError(t)}(l,`createSelector expects an output function after the inputs, but received: [${typeof l}]`);let{memoize:u,memoizeOptions:c=[],argsMemoize:s=b,argsMemoizeOptions:d=[],devModeChecks:f={}}={...r,...a},p=n(c),h=n(d),v=function(e){let t=Array.isArray(e[0])?e[0]:e;return!function(e,t="expected all items to be functions, instead received the following types: "){if(!e.every(e=>"function"==typeof e)){let r=e.map(e=>"function"==typeof e?`function ${e.name||"unnamed"}()`:typeof e).join(", ");throw TypeError(`${t}[${r}]`)}}(t,"createSelector expects all input-selectors to be functions, but received the following types: "),t}(e),y=u(function(){return o++,l.apply(null,arguments)},...p);return Object.assign(s(function(){i++;let e=function(e,t){let r=[],{length:n}=e;for(let o=0;o<n;o++)r.push(e[o].apply(null,t));return r}(v,arguments);return t=y.apply(null,e)},...h),{resultFunc:l,memoizedResultFunc:y,dependencies:v,dependencyRecomputations:()=>i,resetDependencyRecomputations:()=>{i=0},lastResult:()=>t,recomputations:()=>o,resetRecomputations:()=>{o=0},memoize:u,argsMemoize:s})};return Object.assign(o,{withTypes:()=>o}),o}(b),x=Object.assign((e,t=w)=>{!function(e,t=`expected an object, instead received ${typeof e}`){if("object"!=typeof e)throw TypeError(t)}(e,`createStructuredSelector expects first argument to be an object where each property is a selector, instead received a ${typeof e}`);let r=Object.keys(e);return t(r.map(t=>e[t]),(...e)=>e.reduce((e,t,n)=>(e[r[n]]=t,e),{}))},{withTypes:()=>x})},14438:function(e,t,r){"use strict";r.d(t,{Am:function(){return u}});var n=r(2265);r(54887),Array(12).fill(0);let o=1;class i{constructor(){this.subscribe=e=>(this.subscribers.push(e),()=>{let t=this.subscribers.indexOf(e);this.subscribers.splice(t,1)}),this.publish=e=>{this.subscribers.forEach(t=>t(e))},this.addToast=e=>{this.publish(e),this.toasts=[...this.toasts,e]},this.create=e=>{var t;let{message:r,...n}=e,i="number"==typeof(null==e?void 0:e.id)||(null==(t=e.id)?void 0:t.length)>0?e.id:o++,a=this.toasts.find(e=>e.id===i),l=void 0===e.dismissible||e.dismissible;return this.dismissedToasts.has(i)&&this.dismissedToasts.delete(i),a?this.toasts=this.toasts.map(t=>t.id===i?(this.publish({...t,...e,id:i,title:r}),{...t,...e,id:i,dismissible:l,title:r}):t):this.addToast({title:r,...n,dismissible:l,id:i}),i},this.dismiss=e=>(e?(this.dismissedToasts.add(e),requestAnimationFrame(()=>this.subscribers.forEach(t=>t({id:e,dismiss:!0})))):this.toasts.forEach(e=>{this.subscribers.forEach(t=>t({id:e.id,dismiss:!0}))}),e),this.message=(e,t)=>this.create({...t,message:e}),this.error=(e,t)=>this.create({...t,message:e,type:"error"}),this.success=(e,t)=>this.create({...t,type:"success",message:e}),this.info=(e,t)=>this.create({...t,type:"info",message:e}),this.warning=(e,t)=>this.create({...t,type:"warning",message:e}),this.loading=(e,t)=>this.create({...t,type:"loading",message:e}),this.promise=(e,t)=>{let r,o;if(!t)return;void 0!==t.loading&&(o=this.create({...t,promise:e,type:"loading",message:t.loading,description:"function"!=typeof t.description?t.description:void 0}));let i=Promise.resolve(e instanceof Function?e():e),a=void 0!==o,u=i.then(async e=>{if(r=["resolve",e],n.isValidElement(e))a=!1,this.create({id:o,type:"default",message:e});else if(l(e)&&!e.ok){a=!1;let r="function"==typeof t.error?await t.error("HTTP error! status: ".concat(e.status)):t.error,i="function"==typeof t.description?await t.description("HTTP error! status: ".concat(e.status)):t.description,l="object"!=typeof r||n.isValidElement(r)?{message:r}:r;this.create({id:o,type:"error",description:i,...l})}else if(e instanceof Error){a=!1;let r="function"==typeof t.error?await t.error(e):t.error,i="function"==typeof t.description?await t.description(e):t.description,l="object"!=typeof r||n.isValidElement(r)?{message:r}:r;this.create({id:o,type:"error",description:i,...l})}else if(void 0!==t.success){a=!1;let r="function"==typeof t.success?await t.success(e):t.success,i="function"==typeof t.description?await t.description(e):t.description,l="object"!=typeof r||n.isValidElement(r)?{message:r}:r;this.create({id:o,type:"success",description:i,...l})}}).catch(async e=>{if(r=["reject",e],void 0!==t.error){a=!1;let r="function"==typeof t.error?await t.error(e):t.error,i="function"==typeof t.description?await t.description(e):t.description,l="object"!=typeof r||n.isValidElement(r)?{message:r}:r;this.create({id:o,type:"error",description:i,...l})}}).finally(()=>{a&&(this.dismiss(o),o=void 0),null==t.finally||t.finally.call(t)}),c=()=>new Promise((e,t)=>u.then(()=>"reject"===r[0]?t(r[1]):e(r[1])).catch(t));return"string"!=typeof o&&"number"!=typeof o?{unwrap:c}:Object.assign(o,{unwrap:c})},this.custom=(e,t)=>{let r=(null==t?void 0:t.id)||o++;return this.create({jsx:e(r),id:r,...t}),r},this.getActiveToasts=()=>this.toasts.filter(e=>!this.dismissedToasts.has(e.id)),this.subscribers=[],this.toasts=[],this.dismissedToasts=new Set}}let a=new i,l=e=>e&&"object"==typeof e&&"ok"in e&&"boolean"==typeof e.ok&&"status"in e&&"number"==typeof e.status,u=Object.assign((e,t)=>{let r=(null==t?void 0:t.id)||o++;return a.addToast({title:e,...t,id:r}),r},{success:a.success,info:a.info,warning:a.warning,error:a.error,custom:a.custom,message:a.message,promise:a.promise,dismiss:a.dismiss,loading:a.loading},{getHistory:()=>a.toasts,getToasts:()=>a.getActiveToasts()});!function(e){if(!e||"undefined"==typeof document)return;let t=document.head||document.getElementsByTagName("head")[0],r=document.createElement("style");r.type="text/css",t.appendChild(r),r.styleSheet?r.styleSheet.cssText=e:r.appendChild(document.createTextNode(e))}("[data-sonner-toaster][dir=ltr],html[dir=ltr]{--toast-icon-margin-start:-3px;--toast-icon-margin-end:4px;--toast-svg-margin-start:-1px;--toast-svg-margin-end:0px;--toast-button-margin-start:auto;--toast-button-margin-end:0;--toast-close-button-start:0;--toast-close-button-end:unset;--toast-close-button-transform:translate(-35%, -35%)}[data-sonner-toaster][dir=rtl],html[dir=rtl]{--toast-icon-margin-start:4px;--toast-icon-margin-end:-3px;--toast-svg-margin-start:0px;--toast-svg-margin-end:-1px;--toast-button-margin-start:0;--toast-button-margin-end:auto;--toast-close-button-start:unset;--toast-close-button-end:0;--toast-close-button-transform:translate(35%, -35%)}[data-sonner-toaster]{position:fixed;width:var(--width);font-family:ui-sans-serif,system-ui,-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji;--gray1:hsl(0, 0%, 99%);--gray2:hsl(0, 0%, 97.3%);--gray3:hsl(0, 0%, 95.1%);--gray4:hsl(0, 0%, 93%);--gray5:hsl(0, 0%, 90.9%);--gray6:hsl(0, 0%, 88.7%);--gray7:hsl(0, 0%, 85.8%);--gray8:hsl(0, 0%, 78%);--gray9:hsl(0, 0%, 56.1%);--gray10:hsl(0, 0%, 52.3%);--gray11:hsl(0, 0%, 43.5%);--gray12:hsl(0, 0%, 9%);--border-radius:8px;box-sizing:border-box;padding:0;margin:0;list-style:none;outline:0;z-index:999999999;transition:transform .4s ease}@media (hover:none) and (pointer:coarse){[data-sonner-toaster][data-lifted=true]{transform:none}}[data-sonner-toaster][data-x-position=right]{right:var(--offset-right)}[data-sonner-toaster][data-x-position=left]{left:var(--offset-left)}[data-sonner-toaster][data-x-position=center]{left:50%;transform:translateX(-50%)}[data-sonner-toaster][data-y-position=top]{top:var(--offset-top)}[data-sonner-toaster][data-y-position=bottom]{bottom:var(--offset-bottom)}[data-sonner-toast]{--y:translateY(100%);--lift-amount:calc(var(--lift) * var(--gap));z-index:var(--z-index);position:absolute;opacity:0;transform:var(--y);touch-action:none;transition:transform .4s,opacity .4s,height .4s,box-shadow .2s;box-sizing:border-box;outline:0;overflow-wrap:anywhere}[data-sonner-toast][data-styled=true]{padding:16px;background:var(--normal-bg);border:1px solid var(--normal-border);color:var(--normal-text);border-radius:var(--border-radius);box-shadow:0 4px 12px rgba(0,0,0,.1);width:var(--width);font-size:13px;display:flex;align-items:center;gap:6px}[data-sonner-toast]:focus-visible{box-shadow:0 4px 12px rgba(0,0,0,.1),0 0 0 2px rgba(0,0,0,.2)}[data-sonner-toast][data-y-position=top]{top:0;--y:translateY(-100%);--lift:1;--lift-amount:calc(1 * var(--gap))}[data-sonner-toast][data-y-position=bottom]{bottom:0;--y:translateY(100%);--lift:-1;--lift-amount:calc(var(--lift) * var(--gap))}[data-sonner-toast][data-styled=true] [data-description]{font-weight:400;line-height:1.4;color:#3f3f3f}[data-rich-colors=true][data-sonner-toast][data-styled=true] [data-description]{color:inherit}[data-sonner-toaster][data-sonner-theme=dark] [data-description]{color:#e8e8e8}[data-sonner-toast][data-styled=true] [data-title]{font-weight:500;line-height:1.5;color:inherit}[data-sonner-toast][data-styled=true] [data-icon]{display:flex;height:16px;width:16px;position:relative;justify-content:flex-start;align-items:center;flex-shrink:0;margin-left:var(--toast-icon-margin-start);margin-right:var(--toast-icon-margin-end)}[data-sonner-toast][data-promise=true] [data-icon]>svg{opacity:0;transform:scale(.8);transform-origin:center;animation:sonner-fade-in .3s ease forwards}[data-sonner-toast][data-styled=true] [data-icon]>*{flex-shrink:0}[data-sonner-toast][data-styled=true] [data-icon] svg{margin-left:var(--toast-svg-margin-start);margin-right:var(--toast-svg-margin-end)}[data-sonner-toast][data-styled=true] [data-content]{display:flex;flex-direction:column;gap:2px}[data-sonner-toast][data-styled=true] [data-button]{border-radius:4px;padding-left:8px;padding-right:8px;height:24px;font-size:12px;color:var(--normal-bg);background:var(--normal-text);margin-left:var(--toast-button-margin-start);margin-right:var(--toast-button-margin-end);border:none;font-weight:500;cursor:pointer;outline:0;display:flex;align-items:center;flex-shrink:0;transition:opacity .4s,box-shadow .2s}[data-sonner-toast][data-styled=true] [data-button]:focus-visible{box-shadow:0 0 0 2px rgba(0,0,0,.4)}[data-sonner-toast][data-styled=true] [data-button]:first-of-type{margin-left:var(--toast-button-margin-start);margin-right:var(--toast-button-margin-end)}[data-sonner-toast][data-styled=true] [data-cancel]{color:var(--normal-text);background:rgba(0,0,0,.08)}[data-sonner-toaster][data-sonner-theme=dark] [data-sonner-toast][data-styled=true] [data-cancel]{background:rgba(255,255,255,.3)}[data-sonner-toast][data-styled=true] [data-close-button]{position:absolute;left:var(--toast-close-button-start);right:var(--toast-close-button-end);top:0;height:20px;width:20px;display:flex;justify-content:center;align-items:center;padding:0;color:var(--gray12);background:var(--normal-bg);border:1px solid var(--gray4);transform:var(--toast-close-button-transform);border-radius:50%;cursor:pointer;z-index:1;transition:opacity .1s,background .2s,border-color .2s}[data-sonner-toast][data-styled=true] [data-close-button]:focus-visible{box-shadow:0 4px 12px rgba(0,0,0,.1),0 0 0 2px rgba(0,0,0,.2)}[data-sonner-toast][data-styled=true] [data-disabled=true]{cursor:not-allowed}[data-sonner-toast][data-styled=true]:hover [data-close-button]:hover{background:var(--gray2);border-color:var(--gray5)}[data-sonner-toast][data-swiping=true]::before{content:'';position:absolute;left:-100%;right:-100%;height:100%;z-index:-1}[data-sonner-toast][data-y-position=top][data-swiping=true]::before{bottom:50%;transform:scaleY(3) translateY(50%)}[data-sonner-toast][data-y-position=bottom][data-swiping=true]::before{top:50%;transform:scaleY(3) translateY(-50%)}[data-sonner-toast][data-swiping=false][data-removed=true]::before{content:'';position:absolute;inset:0;transform:scaleY(2)}[data-sonner-toast][data-expanded=true]::after{content:'';position:absolute;left:0;height:calc(var(--gap) + 1px);bottom:100%;width:100%}[data-sonner-toast][data-mounted=true]{--y:translateY(0);opacity:1}[data-sonner-toast][data-expanded=false][data-front=false]{--scale:var(--toasts-before) * 0.05 + 1;--y:translateY(calc(var(--lift-amount) * var(--toasts-before))) scale(calc(-1 * var(--scale)));height:var(--front-toast-height)}[data-sonner-toast]>*{transition:opacity .4s}[data-sonner-toast][data-x-position=right]{right:0}[data-sonner-toast][data-x-position=left]{left:0}[data-sonner-toast][data-expanded=false][data-front=false][data-styled=true]>*{opacity:0}[data-sonner-toast][data-visible=false]{opacity:0;pointer-events:none}[data-sonner-toast][data-mounted=true][data-expanded=true]{--y:translateY(calc(var(--lift) * var(--offset)));height:var(--initial-height)}[data-sonner-toast][data-removed=true][data-front=true][data-swipe-out=false]{--y:translateY(calc(var(--lift) * -100%));opacity:0}[data-sonner-toast][data-removed=true][data-front=false][data-swipe-out=false][data-expanded=true]{--y:translateY(calc(var(--lift) * var(--offset) + var(--lift) * -100%));opacity:0}[data-sonner-toast][data-removed=true][data-front=false][data-swipe-out=false][data-expanded=false]{--y:translateY(40%);opacity:0;transition:transform .5s,opacity .2s}[data-sonner-toast][data-removed=true][data-front=false]::before{height:calc(var(--initial-height) + 20%)}[data-sonner-toast][data-swiping=true]{transform:var(--y) translateY(var(--swipe-amount-y,0)) translateX(var(--swipe-amount-x,0));transition:none}[data-sonner-toast][data-swiped=true]{user-select:none}[data-sonner-toast][data-swipe-out=true][data-y-position=bottom],[data-sonner-toast][data-swipe-out=true][data-y-position=top]{animation-duration:.2s;animation-timing-function:ease-out;animation-fill-mode:forwards}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=left]{animation-name:swipe-out-left}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=right]{animation-name:swipe-out-right}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=up]{animation-name:swipe-out-up}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=down]{animation-name:swipe-out-down}@keyframes swipe-out-left{from{transform:var(--y) translateX(var(--swipe-amount-x));opacity:1}to{transform:var(--y) translateX(calc(var(--swipe-amount-x) - 100%));opacity:0}}@keyframes swipe-out-right{from{transform:var(--y) translateX(var(--swipe-amount-x));opacity:1}to{transform:var(--y) translateX(calc(var(--swipe-amount-x) + 100%));opacity:0}}@keyframes swipe-out-up{from{transform:var(--y) translateY(var(--swipe-amount-y));opacity:1}to{transform:var(--y) translateY(calc(var(--swipe-amount-y) - 100%));opacity:0}}@keyframes swipe-out-down{from{transform:var(--y) translateY(var(--swipe-amount-y));opacity:1}to{transform:var(--y) translateY(calc(var(--swipe-amount-y) + 100%));opacity:0}}@media (max-width:600px){[data-sonner-toaster]{position:fixed;right:var(--mobile-offset-right);left:var(--mobile-offset-left);width:100%}[data-sonner-toaster][dir=rtl]{left:calc(var(--mobile-offset-left) * -1)}[data-sonner-toaster] [data-sonner-toast]{left:0;right:0;width:calc(100% - var(--mobile-offset-left) * 2)}[data-sonner-toaster][data-x-position=left]{left:var(--mobile-offset-left)}[data-sonner-toaster][data-y-position=bottom]{bottom:var(--mobile-offset-bottom)}[data-sonner-toaster][data-y-position=top]{top:var(--mobile-offset-top)}[data-sonner-toaster][data-x-position=center]{left:var(--mobile-offset-left);right:var(--mobile-offset-right);transform:none}}[data-sonner-toaster][data-sonner-theme=light]{--normal-bg:#fff;--normal-border:var(--gray4);--normal-text:var(--gray12);--success-bg:hsl(143, 85%, 96%);--success-border:hsl(145, 92%, 87%);--success-text:hsl(140, 100%, 27%);--info-bg:hsl(208, 100%, 97%);--info-border:hsl(221, 91%, 93%);--info-text:hsl(210, 92%, 45%);--warning-bg:hsl(49, 100%, 97%);--warning-border:hsl(49, 91%, 84%);--warning-text:hsl(31, 92%, 45%);--error-bg:hsl(359, 100%, 97%);--error-border:hsl(359, 100%, 94%);--error-text:hsl(360, 100%, 45%)}[data-sonner-toaster][data-sonner-theme=light] [data-sonner-toast][data-invert=true]{--normal-bg:#000;--normal-border:hsl(0, 0%, 20%);--normal-text:var(--gray1)}[data-sonner-toaster][data-sonner-theme=dark] [data-sonner-toast][data-invert=true]{--normal-bg:#fff;--normal-border:var(--gray3);--normal-text:var(--gray12)}[data-sonner-toaster][data-sonner-theme=dark]{--normal-bg:#000;--normal-bg-hover:hsl(0, 0%, 12%);--normal-border:hsl(0, 0%, 20%);--normal-border-hover:hsl(0, 0%, 25%);--normal-text:var(--gray1);--success-bg:hsl(150, 100%, 6%);--success-border:hsl(147, 100%, 12%);--success-text:hsl(150, 86%, 65%);--info-bg:hsl(215, 100%, 6%);--info-border:hsl(223, 43%, 17%);--info-text:hsl(216, 87%, 65%);--warning-bg:hsl(64, 100%, 6%);--warning-border:hsl(60, 100%, 9%);--warning-text:hsl(46, 87%, 65%);--error-bg:hsl(358, 76%, 10%);--error-border:hsl(357, 89%, 16%);--error-text:hsl(358, 100%, 81%)}[data-sonner-toaster][data-sonner-theme=dark] [data-sonner-toast] [data-close-button]{background:var(--normal-bg);border-color:var(--normal-border);color:var(--normal-text)}[data-sonner-toaster][data-sonner-theme=dark] [data-sonner-toast] [data-close-button]:hover{background:var(--normal-bg-hover);border-color:var(--normal-border-hover)}[data-rich-colors=true][data-sonner-toast][data-type=success]{background:var(--success-bg);border-color:var(--success-border);color:var(--success-text)}[data-rich-colors=true][data-sonner-toast][data-type=success] [data-close-button]{background:var(--success-bg);border-color:var(--success-border);color:var(--success-text)}[data-rich-colors=true][data-sonner-toast][data-type=info]{background:var(--info-bg);border-color:var(--info-border);color:var(--info-text)}[data-rich-colors=true][data-sonner-toast][data-type=info] [data-close-button]{background:var(--info-bg);border-color:var(--info-border);color:var(--info-text)}[data-rich-colors=true][data-sonner-toast][data-type=warning]{background:var(--warning-bg);border-color:var(--warning-border);color:var(--warning-text)}[data-rich-colors=true][data-sonner-toast][data-type=warning] [data-close-button]{background:var(--warning-bg);border-color:var(--warning-border);color:var(--warning-text)}[data-rich-colors=true][data-sonner-toast][data-type=error]{background:var(--error-bg);border-color:var(--error-border);color:var(--error-text)}[data-rich-colors=true][data-sonner-toast][data-type=error] [data-close-button]{background:var(--error-bg);border-color:var(--error-border);color:var(--error-text)}.sonner-loading-wrapper{--size:16px;height:var(--size);width:var(--size);position:absolute;inset:0;z-index:10}.sonner-loading-wrapper[data-visible=false]{transform-origin:center;animation:sonner-fade-out .2s ease forwards}.sonner-spinner{position:relative;top:50%;left:50%;height:var(--size);width:var(--size)}.sonner-loading-bar{animation:sonner-spin 1.2s linear infinite;background:var(--gray11);border-radius:6px;height:8%;left:-10%;position:absolute;top:-3.9%;width:24%}.sonner-loading-bar:first-child{animation-delay:-1.2s;transform:rotate(.0001deg) translate(146%)}.sonner-loading-bar:nth-child(2){animation-delay:-1.1s;transform:rotate(30deg) translate(146%)}.sonner-loading-bar:nth-child(3){animation-delay:-1s;transform:rotate(60deg) translate(146%)}.sonner-loading-bar:nth-child(4){animation-delay:-.9s;transform:rotate(90deg) translate(146%)}.sonner-loading-bar:nth-child(5){animation-delay:-.8s;transform:rotate(120deg) translate(146%)}.sonner-loading-bar:nth-child(6){animation-delay:-.7s;transform:rotate(150deg) translate(146%)}.sonner-loading-bar:nth-child(7){animation-delay:-.6s;transform:rotate(180deg) translate(146%)}.sonner-loading-bar:nth-child(8){animation-delay:-.5s;transform:rotate(210deg) translate(146%)}.sonner-loading-bar:nth-child(9){animation-delay:-.4s;transform:rotate(240deg) translate(146%)}.sonner-loading-bar:nth-child(10){animation-delay:-.3s;transform:rotate(270deg) translate(146%)}.sonner-loading-bar:nth-child(11){animation-delay:-.2s;transform:rotate(300deg) translate(146%)}.sonner-loading-bar:nth-child(12){animation-delay:-.1s;transform:rotate(330deg) translate(146%)}@keyframes sonner-fade-in{0%{opacity:0;transform:scale(.8)}100%{opacity:1;transform:scale(1)}}@keyframes sonner-fade-out{0%{opacity:1;transform:scale(1)}100%{opacity:0;transform:scale(.8)}}@keyframes sonner-spin{0%{opacity:1}100%{opacity:.15}}@media (prefers-reduced-motion){.sonner-loading-bar,[data-sonner-toast],[data-sonner-toast]>*{transition:none!important;animation:none!important}}.sonner-loader{position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);transform-origin:center;transition:opacity .2s,transform .2s}.sonner-loader[data-visible=false]{opacity:0;transform:scale(.8) translate(-50%,-50%)}")},53335:function(e,t,r){"use strict";r.d(t,{m6:function(){return ec}});let n=e=>{let t=l(e),{conflictingClassGroups:r,conflictingClassGroupModifiers:n}=e;return{getClassGroupId:e=>{let r=e.split("-");return""===r[0]&&1!==r.length&&r.shift(),o(r,t)||a(e)},getConflictingClassGroupIds:(e,t)=>{let o=r[e]||[];return t&&n[e]?[...o,...n[e]]:o}}},o=(e,t)=>{if(0===e.length)return t.classGroupId;let r=e[0],n=t.nextPart.get(r),i=n?o(e.slice(1),n):void 0;if(i)return i;if(0===t.validators.length)return;let a=e.join("-");return t.validators.find(({validator:e})=>e(a))?.classGroupId},i=/^\[(.+)\]$/,a=e=>{if(i.test(e)){let t=i.exec(e)[1],r=t?.substring(0,t.indexOf(":"));if(r)return"arbitrary.."+r}},l=e=>{let{theme:t,classGroups:r}=e,n={nextPart:new Map,validators:[]};for(let e in r)u(r[e],n,e,t);return n},u=(e,t,r,n)=>{e.forEach(e=>{if("string"==typeof e){(""===e?t:c(t,e)).classGroupId=r;return}if("function"==typeof e){if(s(e)){u(e(n),t,r,n);return}t.validators.push({validator:e,classGroupId:r});return}Object.entries(e).forEach(([e,o])=>{u(o,c(t,e),r,n)})})},c=(e,t)=>{let r=e;return t.split("-").forEach(e=>{r.nextPart.has(e)||r.nextPart.set(e,{nextPart:new Map,validators:[]}),r=r.nextPart.get(e)}),r},s=e=>e.isThemeGetter,d=e=>{if(e<1)return{get:()=>void 0,set:()=>{}};let t=0,r=new Map,n=new Map,o=(o,i)=>{r.set(o,i),++t>e&&(t=0,n=r,r=new Map)};return{get(e){let t=r.get(e);return void 0!==t?t:void 0!==(t=n.get(e))?(o(e,t),t):void 0},set(e,t){r.has(e)?r.set(e,t):o(e,t)}}},f=e=>{let{prefix:t,experimentalParseClassName:r}=e,n=e=>{let t;let r=[],n=0,o=0,i=0;for(let a=0;a<e.length;a++){let l=e[a];if(0===n&&0===o){if(":"===l){r.push(e.slice(i,a)),i=a+1;continue}if("/"===l){t=a;continue}}"["===l?n++:"]"===l?n--:"("===l?o++:")"===l&&o--}let a=0===r.length?e:e.substring(i),l=p(a);return{modifiers:r,hasImportantModifier:l!==a,baseClassName:l,maybePostfixModifierPosition:t&&t>i?t-i:void 0}};if(t){let e=t+":",r=n;n=t=>t.startsWith(e)?r(t.substring(e.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:t,maybePostfixModifierPosition:void 0}}if(r){let e=n;n=t=>r({className:t,parseClassName:e})}return n},p=e=>e.endsWith("!")?e.substring(0,e.length-1):e.startsWith("!")?e.substring(1):e,h=e=>{let t=Object.fromEntries(e.orderSensitiveModifiers.map(e=>[e,!0]));return e=>{if(e.length<=1)return e;let r=[],n=[];return e.forEach(e=>{"["===e[0]||t[e]?(r.push(...n.sort(),e),n=[]):n.push(e)}),r.push(...n.sort()),r}},v=e=>({cache:d(e.cacheSize),parseClassName:f(e),sortModifiers:h(e),...n(e)}),y=/\s+/,m=(e,t)=>{let{parseClassName:r,getClassGroupId:n,getConflictingClassGroupIds:o,sortModifiers:i}=t,a=[],l=e.trim().split(y),u="";for(let e=l.length-1;e>=0;e-=1){let t=l[e],{isExternal:c,modifiers:s,hasImportantModifier:d,baseClassName:f,maybePostfixModifierPosition:p}=r(t);if(c){u=t+(u.length>0?" "+u:u);continue}let h=!!p,v=n(h?f.substring(0,p):f);if(!v){if(!h||!(v=n(f))){u=t+(u.length>0?" "+u:u);continue}h=!1}let y=i(s).join(":"),m=d?y+"!":y,g=m+v;if(a.includes(g))continue;a.push(g);let b=o(v,h);for(let e=0;e<b.length;++e){let t=b[e];a.push(m+t)}u=t+(u.length>0?" "+u:u)}return u};function g(){let e,t,r=0,n="";for(;r<arguments.length;)(e=arguments[r++])&&(t=b(e))&&(n&&(n+=" "),n+=t);return n}let b=e=>{let t;if("string"==typeof e)return e;let r="";for(let n=0;n<e.length;n++)e[n]&&(t=b(e[n]))&&(r&&(r+=" "),r+=t);return r},w=e=>{let t=t=>t[e]||[];return t.isThemeGetter=!0,t},x=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,O=/^\((?:(\w[\w-]*):)?(.+)\)$/i,j=/^\d+\/\d+$/,k=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,P=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,E=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,M=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,S=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,C=e=>j.test(e),A=e=>!!e&&!Number.isNaN(Number(e)),T=e=>!!e&&Number.isInteger(Number(e)),D=e=>e.endsWith("%")&&A(e.slice(0,-1)),N=e=>k.test(e),_=()=>!0,I=e=>P.test(e)&&!E.test(e),R=()=>!1,L=e=>M.test(e),z=e=>S.test(e),W=e=>!B(e)&&!H(e),F=e=>ee(e,eo,R),B=e=>x.test(e),K=e=>ee(e,ei,I),V=e=>ee(e,ea,A),Z=e=>ee(e,er,R),U=e=>ee(e,en,z),$=e=>ee(e,eu,L),H=e=>O.test(e),Y=e=>et(e,ei),q=e=>et(e,el),X=e=>et(e,er),G=e=>et(e,eo),Q=e=>et(e,en),J=e=>et(e,eu,!0),ee=(e,t,r)=>{let n=x.exec(e);return!!n&&(n[1]?t(n[1]):r(n[2]))},et=(e,t,r=!1)=>{let n=O.exec(e);return!!n&&(n[1]?t(n[1]):r)},er=e=>"position"===e||"percentage"===e,en=e=>"image"===e||"url"===e,eo=e=>"length"===e||"size"===e||"bg-size"===e,ei=e=>"length"===e,ea=e=>"number"===e,el=e=>"family-name"===e,eu=e=>"shadow"===e,ec=function(e,...t){let r,n,o;let i=function(l){return n=(r=v(t.reduce((e,t)=>t(e),e()))).cache.get,o=r.cache.set,i=a,a(l)};function a(e){let t=n(e);if(t)return t;let i=m(e,r);return o(e,i),i}return function(){return i(g.apply(null,arguments))}}(()=>{let e=w("color"),t=w("font"),r=w("text"),n=w("font-weight"),o=w("tracking"),i=w("leading"),a=w("breakpoint"),l=w("container"),u=w("spacing"),c=w("radius"),s=w("shadow"),d=w("inset-shadow"),f=w("text-shadow"),p=w("drop-shadow"),h=w("blur"),v=w("perspective"),y=w("aspect"),m=w("ease"),g=w("animate"),b=()=>["auto","avoid","all","avoid-page","page","left","right","column"],x=()=>["center","top","bottom","left","right","top-left","left-top","top-right","right-top","bottom-right","right-bottom","bottom-left","left-bottom"],O=()=>[...x(),H,B],j=()=>["auto","hidden","clip","visible","scroll"],k=()=>["auto","contain","none"],P=()=>[H,B,u],E=()=>[C,"full","auto",...P()],M=()=>[T,"none","subgrid",H,B],S=()=>["auto",{span:["full",T,H,B]},T,H,B],I=()=>[T,"auto",H,B],R=()=>["auto","min","max","fr",H,B],L=()=>["start","end","center","between","around","evenly","stretch","baseline","center-safe","end-safe"],z=()=>["start","end","center","stretch","center-safe","end-safe"],ee=()=>["auto",...P()],et=()=>[C,"auto","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",...P()],er=()=>[e,H,B],en=()=>[...x(),X,Z,{position:[H,B]}],eo=()=>["no-repeat",{repeat:["","x","y","space","round"]}],ei=()=>["auto","cover","contain",G,F,{size:[H,B]}],ea=()=>[D,Y,K],el=()=>["","none","full",c,H,B],eu=()=>["",A,Y,K],ec=()=>["solid","dashed","dotted","double"],es=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],ed=()=>[A,D,X,Z],ef=()=>["","none",h,H,B],ep=()=>["none",A,H,B],eh=()=>["none",A,H,B],ev=()=>[A,H,B],ey=()=>[C,"full",...P()];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[N],breakpoint:[N],color:[_],container:[N],"drop-shadow":[N],ease:["in","out","in-out"],font:[W],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[N],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[N],shadow:[N],spacing:["px",A],text:[N],"text-shadow":[N],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",C,B,H,y]}],container:["container"],columns:[{columns:[A,B,H,l]}],"break-after":[{"break-after":b()}],"break-before":[{"break-before":b()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:O()}],overflow:[{overflow:j()}],"overflow-x":[{"overflow-x":j()}],"overflow-y":[{"overflow-y":j()}],overscroll:[{overscroll:k()}],"overscroll-x":[{"overscroll-x":k()}],"overscroll-y":[{"overscroll-y":k()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:E()}],"inset-x":[{"inset-x":E()}],"inset-y":[{"inset-y":E()}],start:[{start:E()}],end:[{end:E()}],top:[{top:E()}],right:[{right:E()}],bottom:[{bottom:E()}],left:[{left:E()}],visibility:["visible","invisible","collapse"],z:[{z:[T,"auto",H,B]}],basis:[{basis:[C,"full","auto",l,...P()]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[A,C,"auto","initial","none",B]}],grow:[{grow:["",A,H,B]}],shrink:[{shrink:["",A,H,B]}],order:[{order:[T,"first","last","none",H,B]}],"grid-cols":[{"grid-cols":M()}],"col-start-end":[{col:S()}],"col-start":[{"col-start":I()}],"col-end":[{"col-end":I()}],"grid-rows":[{"grid-rows":M()}],"row-start-end":[{row:S()}],"row-start":[{"row-start":I()}],"row-end":[{"row-end":I()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":R()}],"auto-rows":[{"auto-rows":R()}],gap:[{gap:P()}],"gap-x":[{"gap-x":P()}],"gap-y":[{"gap-y":P()}],"justify-content":[{justify:[...L(),"normal"]}],"justify-items":[{"justify-items":[...z(),"normal"]}],"justify-self":[{"justify-self":["auto",...z()]}],"align-content":[{content:["normal",...L()]}],"align-items":[{items:[...z(),{baseline:["","last"]}]}],"align-self":[{self:["auto",...z(),{baseline:["","last"]}]}],"place-content":[{"place-content":L()}],"place-items":[{"place-items":[...z(),"baseline"]}],"place-self":[{"place-self":["auto",...z()]}],p:[{p:P()}],px:[{px:P()}],py:[{py:P()}],ps:[{ps:P()}],pe:[{pe:P()}],pt:[{pt:P()}],pr:[{pr:P()}],pb:[{pb:P()}],pl:[{pl:P()}],m:[{m:ee()}],mx:[{mx:ee()}],my:[{my:ee()}],ms:[{ms:ee()}],me:[{me:ee()}],mt:[{mt:ee()}],mr:[{mr:ee()}],mb:[{mb:ee()}],ml:[{ml:ee()}],"space-x":[{"space-x":P()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":P()}],"space-y-reverse":["space-y-reverse"],size:[{size:et()}],w:[{w:[l,"screen",...et()]}],"min-w":[{"min-w":[l,"screen","none",...et()]}],"max-w":[{"max-w":[l,"screen","none","prose",{screen:[a]},...et()]}],h:[{h:["screen","lh",...et()]}],"min-h":[{"min-h":["screen","lh","none",...et()]}],"max-h":[{"max-h":["screen","lh",...et()]}],"font-size":[{text:["base",r,Y,K]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[n,H,V]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",D,B]}],"font-family":[{font:[q,B,t]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[o,H,B]}],"line-clamp":[{"line-clamp":[A,"none",H,V]}],leading:[{leading:[i,...P()]}],"list-image":[{"list-image":["none",H,B]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",H,B]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:er()}],"text-color":[{text:er()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...ec(),"wavy"]}],"text-decoration-thickness":[{decoration:[A,"from-font","auto",H,K]}],"text-decoration-color":[{decoration:er()}],"underline-offset":[{"underline-offset":[A,"auto",H,B]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:P()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",H,B]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],wrap:[{wrap:["break-word","anywhere","normal"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",H,B]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:en()}],"bg-repeat":[{bg:eo()}],"bg-size":[{bg:ei()}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},T,H,B],radial:["",H,B],conic:[T,H,B]},Q,U]}],"bg-color":[{bg:er()}],"gradient-from-pos":[{from:ea()}],"gradient-via-pos":[{via:ea()}],"gradient-to-pos":[{to:ea()}],"gradient-from":[{from:er()}],"gradient-via":[{via:er()}],"gradient-to":[{to:er()}],rounded:[{rounded:el()}],"rounded-s":[{"rounded-s":el()}],"rounded-e":[{"rounded-e":el()}],"rounded-t":[{"rounded-t":el()}],"rounded-r":[{"rounded-r":el()}],"rounded-b":[{"rounded-b":el()}],"rounded-l":[{"rounded-l":el()}],"rounded-ss":[{"rounded-ss":el()}],"rounded-se":[{"rounded-se":el()}],"rounded-ee":[{"rounded-ee":el()}],"rounded-es":[{"rounded-es":el()}],"rounded-tl":[{"rounded-tl":el()}],"rounded-tr":[{"rounded-tr":el()}],"rounded-br":[{"rounded-br":el()}],"rounded-bl":[{"rounded-bl":el()}],"border-w":[{border:eu()}],"border-w-x":[{"border-x":eu()}],"border-w-y":[{"border-y":eu()}],"border-w-s":[{"border-s":eu()}],"border-w-e":[{"border-e":eu()}],"border-w-t":[{"border-t":eu()}],"border-w-r":[{"border-r":eu()}],"border-w-b":[{"border-b":eu()}],"border-w-l":[{"border-l":eu()}],"divide-x":[{"divide-x":eu()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":eu()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:[...ec(),"hidden","none"]}],"divide-style":[{divide:[...ec(),"hidden","none"]}],"border-color":[{border:er()}],"border-color-x":[{"border-x":er()}],"border-color-y":[{"border-y":er()}],"border-color-s":[{"border-s":er()}],"border-color-e":[{"border-e":er()}],"border-color-t":[{"border-t":er()}],"border-color-r":[{"border-r":er()}],"border-color-b":[{"border-b":er()}],"border-color-l":[{"border-l":er()}],"divide-color":[{divide:er()}],"outline-style":[{outline:[...ec(),"none","hidden"]}],"outline-offset":[{"outline-offset":[A,H,B]}],"outline-w":[{outline:["",A,Y,K]}],"outline-color":[{outline:er()}],shadow:[{shadow:["","none",s,J,$]}],"shadow-color":[{shadow:er()}],"inset-shadow":[{"inset-shadow":["none",d,J,$]}],"inset-shadow-color":[{"inset-shadow":er()}],"ring-w":[{ring:eu()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:er()}],"ring-offset-w":[{"ring-offset":[A,K]}],"ring-offset-color":[{"ring-offset":er()}],"inset-ring-w":[{"inset-ring":eu()}],"inset-ring-color":[{"inset-ring":er()}],"text-shadow":[{"text-shadow":["none",f,J,$]}],"text-shadow-color":[{"text-shadow":er()}],opacity:[{opacity:[A,H,B]}],"mix-blend":[{"mix-blend":[...es(),"plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":es()}],"mask-clip":[{"mask-clip":["border","padding","content","fill","stroke","view"]},"mask-no-clip"],"mask-composite":[{mask:["add","subtract","intersect","exclude"]}],"mask-image-linear-pos":[{"mask-linear":[A]}],"mask-image-linear-from-pos":[{"mask-linear-from":ed()}],"mask-image-linear-to-pos":[{"mask-linear-to":ed()}],"mask-image-linear-from-color":[{"mask-linear-from":er()}],"mask-image-linear-to-color":[{"mask-linear-to":er()}],"mask-image-t-from-pos":[{"mask-t-from":ed()}],"mask-image-t-to-pos":[{"mask-t-to":ed()}],"mask-image-t-from-color":[{"mask-t-from":er()}],"mask-image-t-to-color":[{"mask-t-to":er()}],"mask-image-r-from-pos":[{"mask-r-from":ed()}],"mask-image-r-to-pos":[{"mask-r-to":ed()}],"mask-image-r-from-color":[{"mask-r-from":er()}],"mask-image-r-to-color":[{"mask-r-to":er()}],"mask-image-b-from-pos":[{"mask-b-from":ed()}],"mask-image-b-to-pos":[{"mask-b-to":ed()}],"mask-image-b-from-color":[{"mask-b-from":er()}],"mask-image-b-to-color":[{"mask-b-to":er()}],"mask-image-l-from-pos":[{"mask-l-from":ed()}],"mask-image-l-to-pos":[{"mask-l-to":ed()}],"mask-image-l-from-color":[{"mask-l-from":er()}],"mask-image-l-to-color":[{"mask-l-to":er()}],"mask-image-x-from-pos":[{"mask-x-from":ed()}],"mask-image-x-to-pos":[{"mask-x-to":ed()}],"mask-image-x-from-color":[{"mask-x-from":er()}],"mask-image-x-to-color":[{"mask-x-to":er()}],"mask-image-y-from-pos":[{"mask-y-from":ed()}],"mask-image-y-to-pos":[{"mask-y-to":ed()}],"mask-image-y-from-color":[{"mask-y-from":er()}],"mask-image-y-to-color":[{"mask-y-to":er()}],"mask-image-radial":[{"mask-radial":[H,B]}],"mask-image-radial-from-pos":[{"mask-radial-from":ed()}],"mask-image-radial-to-pos":[{"mask-radial-to":ed()}],"mask-image-radial-from-color":[{"mask-radial-from":er()}],"mask-image-radial-to-color":[{"mask-radial-to":er()}],"mask-image-radial-shape":[{"mask-radial":["circle","ellipse"]}],"mask-image-radial-size":[{"mask-radial":[{closest:["side","corner"],farthest:["side","corner"]}]}],"mask-image-radial-pos":[{"mask-radial-at":x()}],"mask-image-conic-pos":[{"mask-conic":[A]}],"mask-image-conic-from-pos":[{"mask-conic-from":ed()}],"mask-image-conic-to-pos":[{"mask-conic-to":ed()}],"mask-image-conic-from-color":[{"mask-conic-from":er()}],"mask-image-conic-to-color":[{"mask-conic-to":er()}],"mask-mode":[{mask:["alpha","luminance","match"]}],"mask-origin":[{"mask-origin":["border","padding","content","fill","stroke","view"]}],"mask-position":[{mask:en()}],"mask-repeat":[{mask:eo()}],"mask-size":[{mask:ei()}],"mask-type":[{"mask-type":["alpha","luminance"]}],"mask-image":[{mask:["none",H,B]}],filter:[{filter:["","none",H,B]}],blur:[{blur:ef()}],brightness:[{brightness:[A,H,B]}],contrast:[{contrast:[A,H,B]}],"drop-shadow":[{"drop-shadow":["","none",p,J,$]}],"drop-shadow-color":[{"drop-shadow":er()}],grayscale:[{grayscale:["",A,H,B]}],"hue-rotate":[{"hue-rotate":[A,H,B]}],invert:[{invert:["",A,H,B]}],saturate:[{saturate:[A,H,B]}],sepia:[{sepia:["",A,H,B]}],"backdrop-filter":[{"backdrop-filter":["","none",H,B]}],"backdrop-blur":[{"backdrop-blur":ef()}],"backdrop-brightness":[{"backdrop-brightness":[A,H,B]}],"backdrop-contrast":[{"backdrop-contrast":[A,H,B]}],"backdrop-grayscale":[{"backdrop-grayscale":["",A,H,B]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[A,H,B]}],"backdrop-invert":[{"backdrop-invert":["",A,H,B]}],"backdrop-opacity":[{"backdrop-opacity":[A,H,B]}],"backdrop-saturate":[{"backdrop-saturate":[A,H,B]}],"backdrop-sepia":[{"backdrop-sepia":["",A,H,B]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":P()}],"border-spacing-x":[{"border-spacing-x":P()}],"border-spacing-y":[{"border-spacing-y":P()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",H,B]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[A,"initial",H,B]}],ease:[{ease:["linear","initial",m,H,B]}],delay:[{delay:[A,H,B]}],animate:[{animate:["none",g,H,B]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[v,H,B]}],"perspective-origin":[{"perspective-origin":O()}],rotate:[{rotate:ep()}],"rotate-x":[{"rotate-x":ep()}],"rotate-y":[{"rotate-y":ep()}],"rotate-z":[{"rotate-z":ep()}],scale:[{scale:eh()}],"scale-x":[{"scale-x":eh()}],"scale-y":[{"scale-y":eh()}],"scale-z":[{"scale-z":eh()}],"scale-3d":["scale-3d"],skew:[{skew:ev()}],"skew-x":[{"skew-x":ev()}],"skew-y":[{"skew-y":ev()}],transform:[{transform:[H,B,"","none","gpu","cpu"]}],"transform-origin":[{origin:O()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:ey()}],"translate-x":[{"translate-x":ey()}],"translate-y":[{"translate-y":ey()}],"translate-z":[{"translate-z":ey()}],"translate-none":["translate-none"],accent:[{accent:er()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:er()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",H,B]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":P()}],"scroll-mx":[{"scroll-mx":P()}],"scroll-my":[{"scroll-my":P()}],"scroll-ms":[{"scroll-ms":P()}],"scroll-me":[{"scroll-me":P()}],"scroll-mt":[{"scroll-mt":P()}],"scroll-mr":[{"scroll-mr":P()}],"scroll-mb":[{"scroll-mb":P()}],"scroll-ml":[{"scroll-ml":P()}],"scroll-p":[{"scroll-p":P()}],"scroll-px":[{"scroll-px":P()}],"scroll-py":[{"scroll-py":P()}],"scroll-ps":[{"scroll-ps":P()}],"scroll-pe":[{"scroll-pe":P()}],"scroll-pt":[{"scroll-pt":P()}],"scroll-pr":[{"scroll-pr":P()}],"scroll-pb":[{"scroll-pb":P()}],"scroll-pl":[{"scroll-pl":P()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",H,B]}],fill:[{fill:["none",...er()]}],"stroke-w":[{stroke:[A,Y,K,V]}],stroke:[{stroke:["none",...er()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-x","border-w-y","border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-x","border-color-y","border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["*","**","after","backdrop","before","details-content","file","first-letter","first-line","marker","placeholder","selection"]}})},5853:function(e,t,r){"use strict";r.d(t,{_T:function(){return o},ev:function(){return i},pi:function(){return n}});var n=function(){return(n=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var o in t=arguments[r])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)};function o(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)0>t.indexOf(n[o])&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]]);return r}function i(e,t,r){if(r||2==arguments.length)for(var n,o=0,i=t.length;o<i;o++)!n&&o in t||(n||(n=Array.prototype.slice.call(t,0,o)),n[o]=t[o]);return e.concat(n||Array.prototype.slice.call(t))}"function"==typeof SuppressedError&&SuppressedError}}]);