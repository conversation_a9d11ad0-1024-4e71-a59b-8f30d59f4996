"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8510],{30166:function(e,t,r){r.d(t,{default:function(){return o.a}});var n=r(55775),o=r.n(n)},55775:function(e,t,r){Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return i}});let n=r(47043);r(57437),r(2265);let o=n._(r(15602));function i(e,t){var r;let n={loading:e=>{let{error:t,isLoading:r,pastDelay:n}=e;return null}};"function"==typeof e&&(n.loader=e);let i={...n,...t};return(0,o.default)({...i,modules:null==(r=i.loadableGenerated)?void 0:r.modules})}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},81523:function(e,t,r){Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"BailoutToCSR",{enumerable:!0,get:function(){return o}});let n=r(18993);function o(e){let{reason:t,children:r}=e;if("undefined"==typeof window)throw new n.BailoutToCSRError(t);return r}},15602:function(e,t,r){Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return u}});let n=r(57437),o=r(2265),i=r(81523),l=r(70049);function a(e){return{default:e&&"default"in e?e.default:e}}let s={loader:()=>Promise.resolve(a(()=>null)),loading:null,ssr:!0},u=function(e){let t={...s,...e},r=(0,o.lazy)(()=>t.loader().then(a)),u=t.loading;function c(e){let a=u?(0,n.jsx)(u,{isLoading:!0,pastDelay:!0,error:null}):null,s=t.ssr?(0,n.jsxs)(n.Fragment,{children:["undefined"==typeof window?(0,n.jsx)(l.PreloadCss,{moduleIds:t.modules}):null,(0,n.jsx)(r,{...e})]}):(0,n.jsx)(i.BailoutToCSR,{reason:"next/dynamic",children:(0,n.jsx)(r,{...e})});return(0,n.jsx)(o.Suspense,{fallback:a,children:s})}return c.displayName="LoadableComponent",c}},70049:function(e,t,r){Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"PreloadCss",{enumerable:!0,get:function(){return i}});let n=r(57437),o=r(20544);function i(e){let{moduleIds:t}=e;if("undefined"!=typeof window)return null;let r=(0,o.getExpectedRequestStore)("next/dynamic css"),i=[];if(r.reactLoadableManifest&&t){let e=r.reactLoadableManifest;for(let r of t){if(!e[r])continue;let t=e[r].files.filter(e=>e.endsWith(".css"));i.push(...t)}}return 0===i.length?null:(0,n.jsx)(n.Fragment,{children:i.map(e=>(0,n.jsx)("link",{precedence:"dynamic",rel:"stylesheet",href:r.assetPrefix+"/_next/"+encodeURI(e),as:"style"},e))})}},6741:function(e,t,r){r.d(t,{M:function(){return n}});function n(e,t,{checkForDefaultPrevented:r=!0}={}){return function(n){if(e?.(n),!1===r||!n.defaultPrevented)return t?.(n)}}},58068:function(e,t,r){r.d(t,{B:function(){return s}});var n=r(2265),o=r(73966),i=r(98575),l=r(7495),a=r(57437);function s(e){let t=e+"CollectionProvider",[r,s]=(0,o.b)(t),[u,c]=r(t,{collectionRef:{current:null},itemMap:new Map}),d=e=>{let{scope:t,children:r}=e,o=n.useRef(null),i=n.useRef(new Map).current;return(0,a.jsx)(u,{scope:t,itemMap:i,collectionRef:o,children:r})};d.displayName=t;let f=e+"CollectionSlot",p=(0,l.Z8)(f),m=n.forwardRef((e,t)=>{let{scope:r,children:n}=e,o=c(f,r),l=(0,i.e)(t,o.collectionRef);return(0,a.jsx)(p,{ref:l,children:n})});m.displayName=f;let v=e+"CollectionItemSlot",b="data-radix-collection-item",g=(0,l.Z8)(v),h=n.forwardRef((e,t)=>{let{scope:r,children:o,...l}=e,s=n.useRef(null),u=(0,i.e)(t,s),d=c(v,r);return n.useEffect(()=>(d.itemMap.set(s,{ref:s,...l}),()=>void d.itemMap.delete(s))),(0,a.jsx)(g,{[b]:"",ref:u,children:o})});return h.displayName=v,[{Provider:d,Slot:m,ItemSlot:h},function(t){let r=c(e+"CollectionConsumer",t);return n.useCallback(()=>{let e=r.collectionRef.current;if(!e)return[];let t=Array.from(e.querySelectorAll("[".concat(b,"]")));return Array.from(r.itemMap.values()).sort((e,r)=>t.indexOf(e.ref.current)-t.indexOf(r.ref.current))},[r.collectionRef,r.itemMap])},s]}},98575:function(e,t,r){r.d(t,{F:function(){return i},e:function(){return l}});var n=r(2265);function o(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function i(...e){return t=>{let r=!1,n=e.map(e=>{let n=o(e,t);return r||"function"!=typeof n||(r=!0),n});if(r)return()=>{for(let t=0;t<n.length;t++){let r=n[t];"function"==typeof r?r():o(e[t],null)}}}}function l(...e){return n.useCallback(i(...e),e)}},73966:function(e,t,r){r.d(t,{b:function(){return l},k:function(){return i}});var n=r(2265),o=r(57437);function i(e,t){let r=n.createContext(t),i=e=>{let{children:t,...i}=e,l=n.useMemo(()=>i,Object.values(i));return(0,o.jsx)(r.Provider,{value:l,children:t})};return i.displayName=e+"Provider",[i,function(o){let i=n.useContext(r);if(i)return i;if(void 0!==t)return t;throw Error(`\`${o}\` must be used within \`${e}\``)}]}function l(e,t=[]){let r=[],i=()=>{let t=r.map(e=>n.createContext(e));return function(r){let o=r?.[e]||t;return n.useMemo(()=>({[`__scope${e}`]:{...r,[e]:o}}),[r,o])}};return i.scopeName=e,[function(t,i){let l=n.createContext(i),a=r.length;r=[...r,i];let s=t=>{let{scope:r,children:i,...s}=t,u=r?.[e]?.[a]||l,c=n.useMemo(()=>s,Object.values(s));return(0,o.jsx)(u.Provider,{value:c,children:i})};return s.displayName=t+"Provider",[s,function(r,o){let s=o?.[e]?.[a]||l,u=n.useContext(s);if(u)return u;if(void 0!==i)return i;throw Error(`\`${r}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let r=()=>{let r=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let o=r.reduce((t,{useScope:r,scopeName:n})=>{let o=r(e)[`__scope${n}`];return{...t,...o}},{});return n.useMemo(()=>({[`__scope${t.scopeName}`]:o}),[o])}};return r.scopeName=t.scopeName,r}(i,...t)]}},15278:function(e,t,r){r.d(t,{I0:function(){return g},XB:function(){return f},fC:function(){return b}});var n,o=r(2265),i=r(6741),l=r(66840),a=r(98575),s=r(26606),u=r(57437),c="dismissableLayer.update",d=o.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),f=o.forwardRef((e,t)=>{var r,f;let{disableOutsidePointerEvents:p=!1,onEscapeKeyDown:b,onPointerDownOutside:g,onFocusOutside:h,onInteractOutside:y,onDismiss:w,...x}=e,k=o.useContext(d),[E,C]=o.useState(null),T=null!==(f=null==E?void 0:E.ownerDocument)&&void 0!==f?f:null===(r=globalThis)||void 0===r?void 0:r.document,[,P]=o.useState({}),j=(0,a.e)(t,e=>C(e)),R=Array.from(k.layers),[M]=[...k.layersWithOutsidePointerEventsDisabled].slice(-1),N=R.indexOf(M),S=E?R.indexOf(E):-1,O=k.layersWithOutsidePointerEventsDisabled.size>0,L=S>=N,z=function(e){var t;let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null===(t=globalThis)||void 0===t?void 0:t.document,n=(0,s.W)(e),i=o.useRef(!1),l=o.useRef(()=>{});return o.useEffect(()=>{let e=e=>{if(e.target&&!i.current){let t=function(){v("dismissableLayer.pointerDownOutside",n,o,{discrete:!0})},o={originalEvent:e};"touch"===e.pointerType?(r.removeEventListener("click",l.current),l.current=t,r.addEventListener("click",l.current,{once:!0})):t()}else r.removeEventListener("click",l.current);i.current=!1},t=window.setTimeout(()=>{r.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(t),r.removeEventListener("pointerdown",e),r.removeEventListener("click",l.current)}},[r,n]),{onPointerDownCapture:()=>i.current=!0}}(e=>{let t=e.target,r=[...k.branches].some(e=>e.contains(t));!L||r||(null==g||g(e),null==y||y(e),e.defaultPrevented||null==w||w())},T),A=function(e){var t;let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null===(t=globalThis)||void 0===t?void 0:t.document,n=(0,s.W)(e),i=o.useRef(!1);return o.useEffect(()=>{let e=e=>{e.target&&!i.current&&v("dismissableLayer.focusOutside",n,{originalEvent:e},{discrete:!1})};return r.addEventListener("focusin",e),()=>r.removeEventListener("focusin",e)},[r,n]),{onFocusCapture:()=>i.current=!0,onBlurCapture:()=>i.current=!1}}(e=>{let t=e.target;[...k.branches].some(e=>e.contains(t))||(null==h||h(e),null==y||y(e),e.defaultPrevented||null==w||w())},T);return!function(e,t=globalThis?.document){let r=(0,s.W)(e);o.useEffect(()=>{let e=e=>{"Escape"===e.key&&r(e)};return t.addEventListener("keydown",e,{capture:!0}),()=>t.removeEventListener("keydown",e,{capture:!0})},[r,t])}(e=>{S!==k.layers.size-1||(null==b||b(e),!e.defaultPrevented&&w&&(e.preventDefault(),w()))},T),o.useEffect(()=>{if(E)return p&&(0===k.layersWithOutsidePointerEventsDisabled.size&&(n=T.body.style.pointerEvents,T.body.style.pointerEvents="none"),k.layersWithOutsidePointerEventsDisabled.add(E)),k.layers.add(E),m(),()=>{p&&1===k.layersWithOutsidePointerEventsDisabled.size&&(T.body.style.pointerEvents=n)}},[E,T,p,k]),o.useEffect(()=>()=>{E&&(k.layers.delete(E),k.layersWithOutsidePointerEventsDisabled.delete(E),m())},[E,k]),o.useEffect(()=>{let e=()=>P({});return document.addEventListener(c,e),()=>document.removeEventListener(c,e)},[]),(0,u.jsx)(l.WV.div,{...x,ref:j,style:{pointerEvents:O?L?"auto":"none":void 0,...e.style},onFocusCapture:(0,i.M)(e.onFocusCapture,A.onFocusCapture),onBlurCapture:(0,i.M)(e.onBlurCapture,A.onBlurCapture),onPointerDownCapture:(0,i.M)(e.onPointerDownCapture,z.onPointerDownCapture)})});f.displayName="DismissableLayer";var p=o.forwardRef((e,t)=>{let r=o.useContext(d),n=o.useRef(null),i=(0,a.e)(t,n);return o.useEffect(()=>{let e=n.current;if(e)return r.branches.add(e),()=>{r.branches.delete(e)}},[r.branches]),(0,u.jsx)(l.WV.div,{...e,ref:i})});function m(){let e=new CustomEvent(c);document.dispatchEvent(e)}function v(e,t,r,n){let{discrete:o}=n,i=r.originalEvent.target,a=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:r});t&&i.addEventListener(e,t,{once:!0}),o?(0,l.jH)(i,a):i.dispatchEvent(a)}p.displayName="DismissableLayerBranch";var b=f,g=p},99255:function(e,t,r){r.d(t,{M:function(){return s}});var n,o=r(2265),i=r(61188),l=(n||(n=r.t(o,2)))[" useId ".trim().toString()]||(()=>void 0),a=0;function s(e){let[t,r]=o.useState(l());return(0,i.b)(()=>{e||r(e=>e??String(a++))},[e]),e||(t?`radix-${t}`:"")}},96806:function(e,t,r){r.d(t,{ee:function(){return X},Eh:function(){return Y},VY:function(){return K},fC:function(){return U},D7:function(){return M}});var n=r(2265),o=r(50032),i=r(54887),l="undefined"!=typeof document?n.useLayoutEffect:function(){};function a(e,t){let r,n,o;if(e===t)return!0;if(typeof e!=typeof t)return!1;if("function"==typeof e&&e.toString()===t.toString())return!0;if(e&&t&&"object"==typeof e){if(Array.isArray(e)){if((r=e.length)!==t.length)return!1;for(n=r;0!=n--;)if(!a(e[n],t[n]))return!1;return!0}if((r=(o=Object.keys(e)).length)!==Object.keys(t).length)return!1;for(n=r;0!=n--;)if(!({}).hasOwnProperty.call(t,o[n]))return!1;for(n=r;0!=n--;){let r=o[n];if(("_owner"!==r||!e.$$typeof)&&!a(e[r],t[r]))return!1}return!0}return e!=e&&t!=t}function s(e){return"undefined"==typeof window?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function u(e,t){let r=s(e);return Math.round(t*r)/r}function c(e){let t=n.useRef(e);return l(()=>{t.current=e}),t}let d=e=>({name:"arrow",options:e,fn(t){let{element:r,padding:n}="function"==typeof e?e(t):e;return r&&({}).hasOwnProperty.call(r,"current")?null!=r.current?(0,o.x7)({element:r.current,padding:n}).fn(t):{}:r?(0,o.x7)({element:r,padding:n}).fn(t):{}}}),f=(e,t)=>({...(0,o.cv)(e),options:[e,t]}),p=(e,t)=>({...(0,o.uY)(e),options:[e,t]}),m=(e,t)=>({...(0,o.dr)(e),options:[e,t]}),v=(e,t)=>({...(0,o.RR)(e),options:[e,t]}),b=(e,t)=>({...(0,o.dp)(e),options:[e,t]}),g=(e,t)=>({...(0,o.Cp)(e),options:[e,t]}),h=(e,t)=>({...d(e),options:[e,t]});var y=r(66840),w=r(57437),x=n.forwardRef((e,t)=>{let{children:r,width:n=10,height:o=5,...i}=e;return(0,w.jsx)(y.WV.svg,{...i,ref:t,width:n,height:o,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?r:(0,w.jsx)("polygon",{points:"0,0 30,0 15,10"})})});x.displayName="Arrow";var k=r(98575),E=r(73966),C=r(26606),T=r(61188),P=r(90420),j="Popper",[R,M]=(0,E.b)(j),[N,S]=R(j),O=e=>{let{__scopePopper:t,children:r}=e,[o,i]=n.useState(null);return(0,w.jsx)(N,{scope:t,anchor:o,onAnchorChange:i,children:r})};O.displayName=j;var L="PopperAnchor",z=n.forwardRef((e,t)=>{let{__scopePopper:r,virtualRef:o,...i}=e,l=S(L,r),a=n.useRef(null),s=(0,k.e)(t,a);return n.useEffect(()=>{l.onAnchorChange((null==o?void 0:o.current)||a.current)}),o?null:(0,w.jsx)(y.WV.div,{...i,ref:s})});z.displayName=L;var A="PopperContent",[D,I]=R(A),_=n.forwardRef((e,t)=>{var r,d,x,E,j,R,M,N;let{__scopePopper:O,side:L="bottom",sideOffset:z=0,align:I="center",alignOffset:_=0,arrowPadding:W=0,avoidCollisions:F=!0,collisionBoundary:V=[],collisionPadding:U=0,sticky:X="partial",hideWhenDetached:K=!1,updatePositionStrategy:Y="optimized",onPlaced:G,...Z}=e,q=S(A,O),[J,Q]=n.useState(null),ee=(0,k.e)(t,e=>Q(e)),[et,er]=n.useState(null),en=(0,P.t)(et),eo=null!==(M=null==en?void 0:en.width)&&void 0!==M?M:0,ei=null!==(N=null==en?void 0:en.height)&&void 0!==N?N:0,el="number"==typeof U?U:{top:0,right:0,bottom:0,left:0,...U},ea=Array.isArray(V)?V:[V],es=ea.length>0,eu={padding:el,boundary:ea.filter($),altBoundary:es},{refs:ec,floatingStyles:ed,placement:ef,isPositioned:ep,middlewareData:em}=function(e){void 0===e&&(e={});let{placement:t="bottom",strategy:r="absolute",middleware:d=[],platform:f,elements:{reference:p,floating:m}={},transform:v=!0,whileElementsMounted:b,open:g}=e,[h,y]=n.useState({x:0,y:0,strategy:r,placement:t,middlewareData:{},isPositioned:!1}),[w,x]=n.useState(d);a(w,d)||x(d);let[k,E]=n.useState(null),[C,T]=n.useState(null),P=n.useCallback(e=>{e!==N.current&&(N.current=e,E(e))},[]),j=n.useCallback(e=>{e!==S.current&&(S.current=e,T(e))},[]),R=p||k,M=m||C,N=n.useRef(null),S=n.useRef(null),O=n.useRef(h),L=null!=b,z=c(b),A=c(f),D=c(g),I=n.useCallback(()=>{if(!N.current||!S.current)return;let e={placement:t,strategy:r,middleware:w};A.current&&(e.platform=A.current),(0,o.oo)(N.current,S.current,e).then(e=>{let t={...e,isPositioned:!1!==D.current};_.current&&!a(O.current,t)&&(O.current=t,i.flushSync(()=>{y(t)}))})},[w,t,r,A,D]);l(()=>{!1===g&&O.current.isPositioned&&(O.current.isPositioned=!1,y(e=>({...e,isPositioned:!1})))},[g]);let _=n.useRef(!1);l(()=>(_.current=!0,()=>{_.current=!1}),[]),l(()=>{if(R&&(N.current=R),M&&(S.current=M),R&&M){if(z.current)return z.current(R,M,I);I()}},[R,M,I,z,L]);let W=n.useMemo(()=>({reference:N,floating:S,setReference:P,setFloating:j}),[P,j]),F=n.useMemo(()=>({reference:R,floating:M}),[R,M]),V=n.useMemo(()=>{let e={position:r,left:0,top:0};if(!F.floating)return e;let t=u(F.floating,h.x),n=u(F.floating,h.y);return v?{...e,transform:"translate("+t+"px, "+n+"px)",...s(F.floating)>=1.5&&{willChange:"transform"}}:{position:r,left:t,top:n}},[r,v,F.floating,h.x,h.y]);return n.useMemo(()=>({...h,update:I,refs:W,elements:F,floatingStyles:V}),[h,I,W,F,V])}({strategy:"fixed",placement:L+("center"!==I?"-"+I:""),whileElementsMounted:function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,o.Me)(...t,{animationFrame:"always"===Y})},elements:{reference:q.anchor},middleware:[f({mainAxis:z+ei,alignmentAxis:_}),F&&p({mainAxis:!0,crossAxis:!1,limiter:"partial"===X?m():void 0,...eu}),F&&v({...eu}),b({...eu,apply:e=>{let{elements:t,rects:r,availableWidth:n,availableHeight:o}=e,{width:i,height:l}=r.reference,a=t.floating.style;a.setProperty("--radix-popper-available-width","".concat(n,"px")),a.setProperty("--radix-popper-available-height","".concat(o,"px")),a.setProperty("--radix-popper-anchor-width","".concat(i,"px")),a.setProperty("--radix-popper-anchor-height","".concat(l,"px"))}}),et&&h({element:et,padding:W}),B({arrowWidth:eo,arrowHeight:ei}),K&&g({strategy:"referenceHidden",...eu})]}),[ev,eb]=H(ef),eg=(0,C.W)(G);(0,T.b)(()=>{ep&&(null==eg||eg())},[ep,eg]);let eh=null===(r=em.arrow)||void 0===r?void 0:r.x,ey=null===(d=em.arrow)||void 0===d?void 0:d.y,ew=(null===(x=em.arrow)||void 0===x?void 0:x.centerOffset)!==0,[ex,ek]=n.useState();return(0,T.b)(()=>{J&&ek(window.getComputedStyle(J).zIndex)},[J]),(0,w.jsx)("div",{ref:ec.setFloating,"data-radix-popper-content-wrapper":"",style:{...ed,transform:ep?ed.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:ex,"--radix-popper-transform-origin":[null===(E=em.transformOrigin)||void 0===E?void 0:E.x,null===(j=em.transformOrigin)||void 0===j?void 0:j.y].join(" "),...(null===(R=em.hide)||void 0===R?void 0:R.referenceHidden)&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:(0,w.jsx)(D,{scope:O,placedSide:ev,onArrowChange:er,arrowX:eh,arrowY:ey,shouldHideArrow:ew,children:(0,w.jsx)(y.WV.div,{"data-side":ev,"data-align":eb,...Z,ref:ee,style:{...Z.style,animation:ep?void 0:"none"}})})})});_.displayName=A;var W="PopperArrow",F={top:"bottom",right:"left",bottom:"top",left:"right"},V=n.forwardRef(function(e,t){let{__scopePopper:r,...n}=e,o=I(W,r),i=F[o.placedSide];return(0,w.jsx)("span",{ref:o.onArrowChange,style:{position:"absolute",left:o.arrowX,top:o.arrowY,[i]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[o.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[o.placedSide],visibility:o.shouldHideArrow?"hidden":void 0},children:(0,w.jsx)(x,{...n,ref:t,style:{...n.style,display:"block"}})})});function $(e){return null!==e}V.displayName=W;var B=e=>({name:"transformOrigin",options:e,fn(t){var r,n,o,i,l;let{placement:a,rects:s,middlewareData:u}=t,c=(null===(r=u.arrow)||void 0===r?void 0:r.centerOffset)!==0,d=c?0:e.arrowWidth,f=c?0:e.arrowHeight,[p,m]=H(a),v={start:"0%",center:"50%",end:"100%"}[m],b=(null!==(i=null===(n=u.arrow)||void 0===n?void 0:n.x)&&void 0!==i?i:0)+d/2,g=(null!==(l=null===(o=u.arrow)||void 0===o?void 0:o.y)&&void 0!==l?l:0)+f/2,h="",y="";return"bottom"===p?(h=c?v:"".concat(b,"px"),y="".concat(-f,"px")):"top"===p?(h=c?v:"".concat(b,"px"),y="".concat(s.floating.height+f,"px")):"right"===p?(h="".concat(-f,"px"),y=c?v:"".concat(g,"px")):"left"===p&&(h="".concat(s.floating.width+f,"px"),y=c?v:"".concat(g,"px")),{data:{x:h,y}}}});function H(e){let[t,r="center"]=e.split("-");return[t,r]}var U=O,X=z,K=_,Y=V},83832:function(e,t,r){r.d(t,{h:function(){return s}});var n=r(2265),o=r(54887),i=r(66840),l=r(61188),a=r(57437),s=n.forwardRef((e,t)=>{var r,s;let{container:u,...c}=e,[d,f]=n.useState(!1);(0,l.b)(()=>f(!0),[]);let p=u||d&&(null===(s=globalThis)||void 0===s?void 0:null===(r=s.document)||void 0===r?void 0:r.body);return p?o.createPortal((0,a.jsx)(i.WV.div,{...c,ref:t}),p):null});s.displayName="Portal"},71599:function(e,t,r){r.d(t,{z:function(){return l}});var n=r(2265),o=r(98575),i=r(61188),l=e=>{var t,r;let l,s;let{present:u,children:c}=e,d=function(e){var t,r;let[o,l]=n.useState(),s=n.useRef(null),u=n.useRef(e),c=n.useRef("none"),[d,f]=(t=e?"mounted":"unmounted",r={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},n.useReducer((e,t)=>{let n=r[e][t];return null!=n?n:e},t));return n.useEffect(()=>{let e=a(s.current);c.current="mounted"===d?e:"none"},[d]),(0,i.b)(()=>{let t=s.current,r=u.current;if(r!==e){let n=c.current,o=a(t);e?f("MOUNT"):"none"===o||(null==t?void 0:t.display)==="none"?f("UNMOUNT"):r&&n!==o?f("ANIMATION_OUT"):f("UNMOUNT"),u.current=e}},[e,f]),(0,i.b)(()=>{if(o){var e;let t;let r=null!==(e=o.ownerDocument.defaultView)&&void 0!==e?e:window,n=e=>{let n=a(s.current).includes(e.animationName);if(e.target===o&&n&&(f("ANIMATION_END"),!u.current)){let e=o.style.animationFillMode;o.style.animationFillMode="forwards",t=r.setTimeout(()=>{"forwards"===o.style.animationFillMode&&(o.style.animationFillMode=e)})}},i=e=>{e.target===o&&(c.current=a(s.current))};return o.addEventListener("animationstart",i),o.addEventListener("animationcancel",n),o.addEventListener("animationend",n),()=>{r.clearTimeout(t),o.removeEventListener("animationstart",i),o.removeEventListener("animationcancel",n),o.removeEventListener("animationend",n)}}f("ANIMATION_END")},[o,f]),{isPresent:["mounted","unmountSuspended"].includes(d),ref:n.useCallback(e=>{s.current=e?getComputedStyle(e):null,l(e)},[])}}(u),f="function"==typeof c?c({present:d.isPresent}):n.Children.only(c),p=(0,o.e)(d.ref,(l=null===(t=Object.getOwnPropertyDescriptor(f.props,"ref"))||void 0===t?void 0:t.get)&&"isReactWarning"in l&&l.isReactWarning?f.ref:(l=null===(r=Object.getOwnPropertyDescriptor(f,"ref"))||void 0===r?void 0:r.get)&&"isReactWarning"in l&&l.isReactWarning?f.props.ref:f.props.ref||f.ref);return"function"==typeof c||d.isPresent?n.cloneElement(f,{ref:p}):null};function a(e){return(null==e?void 0:e.animationName)||"none"}l.displayName="Presence"},66840:function(e,t,r){r.d(t,{WV:function(){return a},jH:function(){return s}});var n=r(2265),o=r(54887),i=r(7495),l=r(57437),a=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let r=(0,i.Z8)(`Primitive.${t}`),o=n.forwardRef((e,n)=>{let{asChild:o,...i}=e,a=o?r:t;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,l.jsx)(a,{...i,ref:n})});return o.displayName=`Primitive.${t}`,{...e,[t]:o}},{});function s(e,t){e&&o.flushSync(()=>e.dispatchEvent(t))}},7495:function(e,t,r){r.d(t,{Z8:function(){return l},g7:function(){return a},sA:function(){return u}});var n=r(2265),o=r(98575),i=r(57437);function l(e){let t=function(e){let t=n.forwardRef((e,t)=>{let{children:r,...i}=e;if(n.isValidElement(r)){let e,l;let a=(e=Object.getOwnPropertyDescriptor(r.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?r.ref:(e=Object.getOwnPropertyDescriptor(r,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?r.props.ref:r.props.ref||r.ref,s=function(e,t){let r={...t};for(let n in t){let o=e[n],i=t[n];/^on[A-Z]/.test(n)?o&&i?r[n]=(...e)=>{let t=i(...e);return o(...e),t}:o&&(r[n]=o):"style"===n?r[n]={...o,...i}:"className"===n&&(r[n]=[o,i].filter(Boolean).join(" "))}return{...e,...r}}(i,r.props);return r.type!==n.Fragment&&(s.ref=t?(0,o.F)(t,a):a),n.cloneElement(r,s)}return n.Children.count(r)>1?n.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),r=n.forwardRef((e,r)=>{let{children:o,...l}=e,a=n.Children.toArray(o),s=a.find(c);if(s){let e=s.props.children,o=a.map(t=>t!==s?t:n.Children.count(e)>1?n.Children.only(null):n.isValidElement(e)?e.props.children:null);return(0,i.jsx)(t,{...l,ref:r,children:n.isValidElement(e)?n.cloneElement(e,void 0,o):null})}return(0,i.jsx)(t,{...l,ref:r,children:o})});return r.displayName=`${e}.Slot`,r}var a=l("Slot"),s=Symbol("radix.slottable");function u(e){let t=({children:e})=>(0,i.jsx)(i.Fragment,{children:e});return t.displayName=`${e}.Slottable`,t.__radixId=s,t}function c(e){return n.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===s}},41915:function(e,t,r){r.d(t,{Dx:function(){return Q},aU:function(){return et},dk:function(){return ee},fC:function(){return J},l_:function(){return q},x8:function(){return er},zt:function(){return Z}});var n=r(2265),o=r(54887),i=r(6741),l=r(98575),a=r(58068),s=r(73966),u=r(15278),c=r(83832),d=r(71599),f=r(66840),p=r(26606),m=r(80886),v=r(61188),b=r(95098),g=r(57437),h="ToastProvider",[y,w,x]=(0,a.B)("Toast"),[k,E]=(0,s.b)("Toast",[x]),[C,T]=k(h),P=e=>{let{__scopeToast:t,label:r="Notification",duration:o=5e3,swipeDirection:i="right",swipeThreshold:l=50,children:a}=e,[s,u]=n.useState(null),[c,d]=n.useState(0),f=n.useRef(!1),p=n.useRef(!1);return r.trim()||console.error("Invalid prop `label` supplied to `".concat(h,"`. Expected non-empty `string`.")),(0,g.jsx)(y.Provider,{scope:t,children:(0,g.jsx)(C,{scope:t,label:r,duration:o,swipeDirection:i,swipeThreshold:l,toastCount:c,viewport:s,onViewportChange:u,onToastAdd:n.useCallback(()=>d(e=>e+1),[]),onToastRemove:n.useCallback(()=>d(e=>e-1),[]),isFocusedToastEscapeKeyDownRef:f,isClosePausedRef:p,children:a})})};P.displayName=h;var j="ToastViewport",R=["F8"],M="toast.viewportPause",N="toast.viewportResume",S=n.forwardRef((e,t)=>{let{__scopeToast:r,hotkey:o=R,label:i="Notifications ({hotkey})",...a}=e,s=T(j,r),c=w(r),d=n.useRef(null),p=n.useRef(null),m=n.useRef(null),v=n.useRef(null),b=(0,l.e)(t,v,s.onViewportChange),h=o.join("+").replace(/Key/g,"").replace(/Digit/g,""),x=s.toastCount>0;n.useEffect(()=>{let e=e=>{var t;0!==o.length&&o.every(t=>e[t]||e.code===t)&&(null===(t=v.current)||void 0===t||t.focus())};return document.addEventListener("keydown",e),()=>document.removeEventListener("keydown",e)},[o]),n.useEffect(()=>{let e=d.current,t=v.current;if(x&&e&&t){let r=()=>{if(!s.isClosePausedRef.current){let e=new CustomEvent(M);t.dispatchEvent(e),s.isClosePausedRef.current=!0}},n=()=>{if(s.isClosePausedRef.current){let e=new CustomEvent(N);t.dispatchEvent(e),s.isClosePausedRef.current=!1}},o=t=>{e.contains(t.relatedTarget)||n()},i=()=>{e.contains(document.activeElement)||n()};return e.addEventListener("focusin",r),e.addEventListener("focusout",o),e.addEventListener("pointermove",r),e.addEventListener("pointerleave",i),window.addEventListener("blur",r),window.addEventListener("focus",n),()=>{e.removeEventListener("focusin",r),e.removeEventListener("focusout",o),e.removeEventListener("pointermove",r),e.removeEventListener("pointerleave",i),window.removeEventListener("blur",r),window.removeEventListener("focus",n)}}},[x,s.isClosePausedRef]);let k=n.useCallback(e=>{let{tabbingDirection:t}=e,r=c().map(e=>{let r=e.ref.current,n=[r,...function(e){let t=[],r=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;r.nextNode();)t.push(r.currentNode);return t}(r)];return"forwards"===t?n:n.reverse()});return("forwards"===t?r.reverse():r).flat()},[c]);return n.useEffect(()=>{let e=v.current;if(e){let t=t=>{let r=t.altKey||t.ctrlKey||t.metaKey;if("Tab"===t.key&&!r){var n,o,i;let r=document.activeElement,l=t.shiftKey;if(t.target===e&&l){null===(n=p.current)||void 0===n||n.focus();return}let a=k({tabbingDirection:l?"backwards":"forwards"}),s=a.findIndex(e=>e===r);G(a.slice(s+1))?t.preventDefault():l?null===(o=p.current)||void 0===o||o.focus():null===(i=m.current)||void 0===i||i.focus()}};return e.addEventListener("keydown",t),()=>e.removeEventListener("keydown",t)}},[c,k]),(0,g.jsxs)(u.I0,{ref:d,role:"region","aria-label":i.replace("{hotkey}",h),tabIndex:-1,style:{pointerEvents:x?void 0:"none"},children:[x&&(0,g.jsx)(L,{ref:p,onFocusFromOutsideViewport:()=>{G(k({tabbingDirection:"forwards"}))}}),(0,g.jsx)(y.Slot,{scope:r,children:(0,g.jsx)(f.WV.ol,{tabIndex:-1,...a,ref:b})}),x&&(0,g.jsx)(L,{ref:m,onFocusFromOutsideViewport:()=>{G(k({tabbingDirection:"backwards"}))}})]})});S.displayName=j;var O="ToastFocusProxy",L=n.forwardRef((e,t)=>{let{__scopeToast:r,onFocusFromOutsideViewport:n,...o}=e,i=T(O,r);return(0,g.jsx)(b.TX,{"aria-hidden":!0,tabIndex:0,...o,ref:t,style:{position:"fixed"},onFocus:e=>{var t;let r=e.relatedTarget;(null===(t=i.viewport)||void 0===t?void 0:t.contains(r))||n()}})});L.displayName=O;var z="Toast",A=n.forwardRef((e,t)=>{let{forceMount:r,open:n,defaultOpen:o,onOpenChange:l,...a}=e,[s,u]=(0,m.T)({prop:n,defaultProp:null==o||o,onChange:l,caller:z});return(0,g.jsx)(d.z,{present:r||s,children:(0,g.jsx)(_,{open:s,...a,ref:t,onClose:()=>u(!1),onPause:(0,p.W)(e.onPause),onResume:(0,p.W)(e.onResume),onSwipeStart:(0,i.M)(e.onSwipeStart,e=>{e.currentTarget.setAttribute("data-swipe","start")}),onSwipeMove:(0,i.M)(e.onSwipeMove,e=>{let{x:t,y:r}=e.detail.delta;e.currentTarget.setAttribute("data-swipe","move"),e.currentTarget.style.setProperty("--radix-toast-swipe-move-x","".concat(t,"px")),e.currentTarget.style.setProperty("--radix-toast-swipe-move-y","".concat(r,"px"))}),onSwipeCancel:(0,i.M)(e.onSwipeCancel,e=>{e.currentTarget.setAttribute("data-swipe","cancel"),e.currentTarget.style.removeProperty("--radix-toast-swipe-move-x"),e.currentTarget.style.removeProperty("--radix-toast-swipe-move-y"),e.currentTarget.style.removeProperty("--radix-toast-swipe-end-x"),e.currentTarget.style.removeProperty("--radix-toast-swipe-end-y")}),onSwipeEnd:(0,i.M)(e.onSwipeEnd,e=>{let{x:t,y:r}=e.detail.delta;e.currentTarget.setAttribute("data-swipe","end"),e.currentTarget.style.removeProperty("--radix-toast-swipe-move-x"),e.currentTarget.style.removeProperty("--radix-toast-swipe-move-y"),e.currentTarget.style.setProperty("--radix-toast-swipe-end-x","".concat(t,"px")),e.currentTarget.style.setProperty("--radix-toast-swipe-end-y","".concat(r,"px")),u(!1)})})})});A.displayName=z;var[D,I]=k(z,{onClose(){}}),_=n.forwardRef((e,t)=>{let{__scopeToast:r,type:a="foreground",duration:s,open:c,onClose:d,onEscapeKeyDown:m,onPause:v,onResume:b,onSwipeStart:h,onSwipeMove:w,onSwipeCancel:x,onSwipeEnd:k,...E}=e,C=T(z,r),[P,j]=n.useState(null),R=(0,l.e)(t,e=>j(e)),S=n.useRef(null),O=n.useRef(null),L=s||C.duration,A=n.useRef(0),I=n.useRef(L),_=n.useRef(0),{onToastAdd:F,onToastRemove:V}=C,$=(0,p.W)(()=>{var e;(null==P?void 0:P.contains(document.activeElement))&&(null===(e=C.viewport)||void 0===e||e.focus()),d()}),B=n.useCallback(e=>{e&&e!==1/0&&(window.clearTimeout(_.current),A.current=new Date().getTime(),_.current=window.setTimeout($,e))},[$]);n.useEffect(()=>{let e=C.viewport;if(e){let t=()=>{B(I.current),null==b||b()},r=()=>{let e=new Date().getTime()-A.current;I.current=I.current-e,window.clearTimeout(_.current),null==v||v()};return e.addEventListener(M,r),e.addEventListener(N,t),()=>{e.removeEventListener(M,r),e.removeEventListener(N,t)}}},[C.viewport,L,v,b,B]),n.useEffect(()=>{c&&!C.isClosePausedRef.current&&B(L)},[c,L,C.isClosePausedRef,B]),n.useEffect(()=>(F(),()=>V()),[F,V]);let H=n.useMemo(()=>P?function e(t){let r=[];return Array.from(t.childNodes).forEach(t=>{if(t.nodeType===t.TEXT_NODE&&t.textContent&&r.push(t.textContent),t.nodeType===t.ELEMENT_NODE){let n=t.ariaHidden||t.hidden||"none"===t.style.display,o=""===t.dataset.radixToastAnnounceExclude;if(!n){if(o){let e=t.dataset.radixToastAnnounceAlt;e&&r.push(e)}else r.push(...e(t))}}}),r}(P):null,[P]);return C.viewport?(0,g.jsxs)(g.Fragment,{children:[H&&(0,g.jsx)(W,{__scopeToast:r,role:"status","aria-live":"foreground"===a?"assertive":"polite","aria-atomic":!0,children:H}),(0,g.jsx)(D,{scope:r,onClose:$,children:o.createPortal((0,g.jsx)(y.ItemSlot,{scope:r,children:(0,g.jsx)(u.fC,{asChild:!0,onEscapeKeyDown:(0,i.M)(m,()=>{C.isFocusedToastEscapeKeyDownRef.current||$(),C.isFocusedToastEscapeKeyDownRef.current=!1}),children:(0,g.jsx)(f.WV.li,{role:"status","aria-live":"off","aria-atomic":!0,tabIndex:0,"data-state":c?"open":"closed","data-swipe-direction":C.swipeDirection,...E,ref:R,style:{userSelect:"none",touchAction:"none",...e.style},onKeyDown:(0,i.M)(e.onKeyDown,e=>{"Escape"!==e.key||(null==m||m(e.nativeEvent),e.nativeEvent.defaultPrevented||(C.isFocusedToastEscapeKeyDownRef.current=!0,$()))}),onPointerDown:(0,i.M)(e.onPointerDown,e=>{0===e.button&&(S.current={x:e.clientX,y:e.clientY})}),onPointerMove:(0,i.M)(e.onPointerMove,e=>{if(!S.current)return;let t=e.clientX-S.current.x,r=e.clientY-S.current.y,n=!!O.current,o=["left","right"].includes(C.swipeDirection),i=["left","up"].includes(C.swipeDirection)?Math.min:Math.max,l=o?i(0,t):0,a=o?0:i(0,r),s="touch"===e.pointerType?10:2,u={x:l,y:a},c={originalEvent:e,delta:u};n?(O.current=u,K("toast.swipeMove",w,c,{discrete:!1})):Y(u,C.swipeDirection,s)?(O.current=u,K("toast.swipeStart",h,c,{discrete:!1}),e.target.setPointerCapture(e.pointerId)):(Math.abs(t)>s||Math.abs(r)>s)&&(S.current=null)}),onPointerUp:(0,i.M)(e.onPointerUp,e=>{let t=O.current,r=e.target;if(r.hasPointerCapture(e.pointerId)&&r.releasePointerCapture(e.pointerId),O.current=null,S.current=null,t){let r=e.currentTarget,n={originalEvent:e,delta:t};Y(t,C.swipeDirection,C.swipeThreshold)?K("toast.swipeEnd",k,n,{discrete:!0}):K("toast.swipeCancel",x,n,{discrete:!0}),r.addEventListener("click",e=>e.preventDefault(),{once:!0})}})})})}),C.viewport)})]}):null}),W=e=>{let{__scopeToast:t,children:r,...o}=e,i=T(z,t),[l,a]=n.useState(!1),[s,u]=n.useState(!1);return function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:()=>{},t=(0,p.W)(e);(0,v.b)(()=>{let e=0,r=0;return e=window.requestAnimationFrame(()=>r=window.requestAnimationFrame(t)),()=>{window.cancelAnimationFrame(e),window.cancelAnimationFrame(r)}},[t])}(()=>a(!0)),n.useEffect(()=>{let e=window.setTimeout(()=>u(!0),1e3);return()=>window.clearTimeout(e)},[]),s?null:(0,g.jsx)(c.h,{asChild:!0,children:(0,g.jsx)(b.TX,{...o,children:l&&(0,g.jsxs)(g.Fragment,{children:[i.label," ",r]})})})},F=n.forwardRef((e,t)=>{let{__scopeToast:r,...n}=e;return(0,g.jsx)(f.WV.div,{...n,ref:t})});F.displayName="ToastTitle";var V=n.forwardRef((e,t)=>{let{__scopeToast:r,...n}=e;return(0,g.jsx)(f.WV.div,{...n,ref:t})});V.displayName="ToastDescription";var $="ToastAction",B=n.forwardRef((e,t)=>{let{altText:r,...n}=e;return r.trim()?(0,g.jsx)(X,{altText:r,asChild:!0,children:(0,g.jsx)(U,{...n,ref:t})}):(console.error("Invalid prop `altText` supplied to `".concat($,"`. Expected non-empty `string`.")),null)});B.displayName=$;var H="ToastClose",U=n.forwardRef((e,t)=>{let{__scopeToast:r,...n}=e,o=I(H,r);return(0,g.jsx)(X,{asChild:!0,children:(0,g.jsx)(f.WV.button,{type:"button",...n,ref:t,onClick:(0,i.M)(e.onClick,o.onClose)})})});U.displayName=H;var X=n.forwardRef((e,t)=>{let{__scopeToast:r,altText:n,...o}=e;return(0,g.jsx)(f.WV.div,{"data-radix-toast-announce-exclude":"","data-radix-toast-announce-alt":n||void 0,...o,ref:t})});function K(e,t,r,n){let{discrete:o}=n,i=r.originalEvent.currentTarget,l=new CustomEvent(e,{bubbles:!0,cancelable:!0,detail:r});t&&i.addEventListener(e,t,{once:!0}),o?(0,f.jH)(i,l):i.dispatchEvent(l)}var Y=function(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,n=Math.abs(e.x),o=Math.abs(e.y),i=n>o;return"left"===t||"right"===t?i&&n>r:!i&&o>r};function G(e){let t=document.activeElement;return e.some(e=>e===t||(e.focus(),document.activeElement!==t))}var Z=P,q=S,J=A,Q=F,ee=V,et=B,er=U},61312:function(e,t,r){r.d(t,{VY:function(){return B},fC:function(){return V},xz:function(){return $},zt:function(){return F}});var n=r(2265),o=r(6741),i=r(98575),l=r(73966),a=r(15278),s=r(99255),u=r(96806),c=(r(83832),r(71599)),d=r(66840),f=r(7495),p=r(80886),m=r(95098),v=r(57437),[b,g]=(0,l.b)("Tooltip",[u.D7]),h=(0,u.D7)(),y="TooltipProvider",w="tooltip.open",[x,k]=b(y),E=e=>{let{__scopeTooltip:t,delayDuration:r=700,skipDelayDuration:o=300,disableHoverableContent:i=!1,children:l}=e,a=n.useRef(!0),s=n.useRef(!1),u=n.useRef(0);return n.useEffect(()=>{let e=u.current;return()=>window.clearTimeout(e)},[]),(0,v.jsx)(x,{scope:t,isOpenDelayedRef:a,delayDuration:r,onOpen:n.useCallback(()=>{window.clearTimeout(u.current),a.current=!1},[]),onClose:n.useCallback(()=>{window.clearTimeout(u.current),u.current=window.setTimeout(()=>a.current=!0,o)},[o]),isPointerInTransitRef:s,onPointerInTransitChange:n.useCallback(e=>{s.current=e},[]),disableHoverableContent:i,children:l})};E.displayName=y;var C="Tooltip",[T,P]=b(C),j=e=>{let{__scopeTooltip:t,children:r,open:o,defaultOpen:i,onOpenChange:l,disableHoverableContent:a,delayDuration:c}=e,d=k(C,e.__scopeTooltip),f=h(t),[m,b]=n.useState(null),g=(0,s.M)(),y=n.useRef(0),x=null!=a?a:d.disableHoverableContent,E=null!=c?c:d.delayDuration,P=n.useRef(!1),[j,R]=(0,p.T)({prop:o,defaultProp:null!=i&&i,onChange:e=>{e?(d.onOpen(),document.dispatchEvent(new CustomEvent(w))):d.onClose(),null==l||l(e)},caller:C}),M=n.useMemo(()=>j?P.current?"delayed-open":"instant-open":"closed",[j]),N=n.useCallback(()=>{window.clearTimeout(y.current),y.current=0,P.current=!1,R(!0)},[R]),S=n.useCallback(()=>{window.clearTimeout(y.current),y.current=0,R(!1)},[R]),O=n.useCallback(()=>{window.clearTimeout(y.current),y.current=window.setTimeout(()=>{P.current=!0,R(!0),y.current=0},E)},[E,R]);return n.useEffect(()=>()=>{y.current&&(window.clearTimeout(y.current),y.current=0)},[]),(0,v.jsx)(u.fC,{...f,children:(0,v.jsx)(T,{scope:t,contentId:g,open:j,stateAttribute:M,trigger:m,onTriggerChange:b,onTriggerEnter:n.useCallback(()=>{d.isOpenDelayedRef.current?O():N()},[d.isOpenDelayedRef,O,N]),onTriggerLeave:n.useCallback(()=>{x?S():(window.clearTimeout(y.current),y.current=0)},[S,x]),onOpen:N,onClose:S,disableHoverableContent:x,children:r})})};j.displayName=C;var R="TooltipTrigger",M=n.forwardRef((e,t)=>{let{__scopeTooltip:r,...l}=e,a=P(R,r),s=k(R,r),c=h(r),f=n.useRef(null),p=(0,i.e)(t,f,a.onTriggerChange),m=n.useRef(!1),b=n.useRef(!1),g=n.useCallback(()=>m.current=!1,[]);return n.useEffect(()=>()=>document.removeEventListener("pointerup",g),[g]),(0,v.jsx)(u.ee,{asChild:!0,...c,children:(0,v.jsx)(d.WV.button,{"aria-describedby":a.open?a.contentId:void 0,"data-state":a.stateAttribute,...l,ref:p,onPointerMove:(0,o.M)(e.onPointerMove,e=>{"touch"===e.pointerType||b.current||s.isPointerInTransitRef.current||(a.onTriggerEnter(),b.current=!0)}),onPointerLeave:(0,o.M)(e.onPointerLeave,()=>{a.onTriggerLeave(),b.current=!1}),onPointerDown:(0,o.M)(e.onPointerDown,()=>{a.open&&a.onClose(),m.current=!0,document.addEventListener("pointerup",g,{once:!0})}),onFocus:(0,o.M)(e.onFocus,()=>{m.current||a.onOpen()}),onBlur:(0,o.M)(e.onBlur,a.onClose),onClick:(0,o.M)(e.onClick,a.onClose)})})});M.displayName=R;var[N,S]=b("TooltipPortal",{forceMount:void 0}),O="TooltipContent",L=n.forwardRef((e,t)=>{let r=S(O,e.__scopeTooltip),{forceMount:n=r.forceMount,side:o="top",...i}=e,l=P(O,e.__scopeTooltip);return(0,v.jsx)(c.z,{present:n||l.open,children:l.disableHoverableContent?(0,v.jsx)(_,{side:o,...i,ref:t}):(0,v.jsx)(z,{side:o,...i,ref:t})})}),z=n.forwardRef((e,t)=>{let r=P(O,e.__scopeTooltip),o=k(O,e.__scopeTooltip),l=n.useRef(null),a=(0,i.e)(t,l),[s,u]=n.useState(null),{trigger:c,onClose:d}=r,f=l.current,{onPointerInTransitChange:p}=o,m=n.useCallback(()=>{u(null),p(!1)},[p]),b=n.useCallback((e,t)=>{let r=e.currentTarget,n={x:e.clientX,y:e.clientY},o=function(e,t){let r=Math.abs(t.top-e.y),n=Math.abs(t.bottom-e.y),o=Math.abs(t.right-e.x),i=Math.abs(t.left-e.x);switch(Math.min(r,n,o,i)){case i:return"left";case o:return"right";case r:return"top";case n:return"bottom";default:throw Error("unreachable")}}(n,r.getBoundingClientRect());u(function(e){let t=e.slice();return t.sort((e,t)=>e.x<t.x?-1:e.x>t.x?1:e.y<t.y?-1:e.y>t.y?1:0),function(e){if(e.length<=1)return e.slice();let t=[];for(let r=0;r<e.length;r++){let n=e[r];for(;t.length>=2;){let e=t[t.length-1],r=t[t.length-2];if((e.x-r.x)*(n.y-r.y)>=(e.y-r.y)*(n.x-r.x))t.pop();else break}t.push(n)}t.pop();let r=[];for(let t=e.length-1;t>=0;t--){let n=e[t];for(;r.length>=2;){let e=r[r.length-1],t=r[r.length-2];if((e.x-t.x)*(n.y-t.y)>=(e.y-t.y)*(n.x-t.x))r.pop();else break}r.push(n)}return(r.pop(),1===t.length&&1===r.length&&t[0].x===r[0].x&&t[0].y===r[0].y)?t:t.concat(r)}(t)}([...function(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:5,n=[];switch(t){case"top":n.push({x:e.x-r,y:e.y+r},{x:e.x+r,y:e.y+r});break;case"bottom":n.push({x:e.x-r,y:e.y-r},{x:e.x+r,y:e.y-r});break;case"left":n.push({x:e.x+r,y:e.y-r},{x:e.x+r,y:e.y+r});break;case"right":n.push({x:e.x-r,y:e.y-r},{x:e.x-r,y:e.y+r})}return n}(n,o),...function(e){let{top:t,right:r,bottom:n,left:o}=e;return[{x:o,y:t},{x:r,y:t},{x:r,y:n},{x:o,y:n}]}(t.getBoundingClientRect())])),p(!0)},[p]);return n.useEffect(()=>()=>m(),[m]),n.useEffect(()=>{if(c&&f){let e=e=>b(e,f),t=e=>b(e,c);return c.addEventListener("pointerleave",e),f.addEventListener("pointerleave",t),()=>{c.removeEventListener("pointerleave",e),f.removeEventListener("pointerleave",t)}}},[c,f,b,m]),n.useEffect(()=>{if(s){let e=e=>{let t=e.target,r={x:e.clientX,y:e.clientY},n=(null==c?void 0:c.contains(t))||(null==f?void 0:f.contains(t)),o=!function(e,t){let{x:r,y:n}=e,o=!1;for(let e=0,i=t.length-1;e<t.length;i=e++){let l=t[e],a=t[i],s=l.x,u=l.y,c=a.x,d=a.y;u>n!=d>n&&r<(c-s)*(n-u)/(d-u)+s&&(o=!o)}return o}(r,s);n?m():o&&(m(),d())};return document.addEventListener("pointermove",e),()=>document.removeEventListener("pointermove",e)}},[c,f,s,d,m]),(0,v.jsx)(_,{...e,ref:a})}),[A,D]=b(C,{isInside:!1}),I=(0,f.sA)("TooltipContent"),_=n.forwardRef((e,t)=>{let{__scopeTooltip:r,children:o,"aria-label":i,onEscapeKeyDown:l,onPointerDownOutside:s,...c}=e,d=P(O,r),f=h(r),{onClose:p}=d;return n.useEffect(()=>(document.addEventListener(w,p),()=>document.removeEventListener(w,p)),[p]),n.useEffect(()=>{if(d.trigger){let e=e=>{let t=e.target;(null==t?void 0:t.contains(d.trigger))&&p()};return window.addEventListener("scroll",e,{capture:!0}),()=>window.removeEventListener("scroll",e,{capture:!0})}},[d.trigger,p]),(0,v.jsx)(a.XB,{asChild:!0,disableOutsidePointerEvents:!1,onEscapeKeyDown:l,onPointerDownOutside:s,onFocusOutside:e=>e.preventDefault(),onDismiss:p,children:(0,v.jsxs)(u.VY,{"data-state":d.stateAttribute,...f,...c,ref:t,style:{...c.style,"--radix-tooltip-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-tooltip-content-available-width":"var(--radix-popper-available-width)","--radix-tooltip-content-available-height":"var(--radix-popper-available-height)","--radix-tooltip-trigger-width":"var(--radix-popper-anchor-width)","--radix-tooltip-trigger-height":"var(--radix-popper-anchor-height)"},children:[(0,v.jsx)(I,{children:o}),(0,v.jsx)(A,{scope:r,isInside:!0,children:(0,v.jsx)(m.fC,{id:d.contentId,role:"tooltip",children:i||o})})]})})});L.displayName=O;var W="TooltipArrow";n.forwardRef((e,t)=>{let{__scopeTooltip:r,...n}=e,o=h(r);return D(W,r).isInside?null:(0,v.jsx)(u.Eh,{...o,...n,ref:t})}).displayName=W;var F=E,V=j,$=M,B=L},26606:function(e,t,r){r.d(t,{W:function(){return o}});var n=r(2265);function o(e){let t=n.useRef(e);return n.useEffect(()=>{t.current=e}),n.useMemo(()=>(...e)=>t.current?.(...e),[])}},80886:function(e,t,r){r.d(t,{T:function(){return a}});var n,o=r(2265),i=r(61188),l=(n||(n=r.t(o,2)))[" useInsertionEffect ".trim().toString()]||i.b;function a({prop:e,defaultProp:t,onChange:r=()=>{},caller:n}){let[i,a,s]=function({defaultProp:e,onChange:t}){let[r,n]=o.useState(e),i=o.useRef(r),a=o.useRef(t);return l(()=>{a.current=t},[t]),o.useEffect(()=>{i.current!==r&&(a.current?.(r),i.current=r)},[r,i]),[r,n,a]}({defaultProp:t,onChange:r}),u=void 0!==e,c=u?e:i;{let t=o.useRef(void 0!==e);o.useEffect(()=>{let e=t.current;if(e!==u){let t=u?"controlled":"uncontrolled";console.warn(`${n} is changing from ${e?"controlled":"uncontrolled"} to ${t}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}t.current=u},[u,n])}return[c,o.useCallback(t=>{if(u){let r="function"==typeof t?t(e):t;r!==e&&s.current?.(r)}else a(t)},[u,e,a,s])]}Symbol("RADIX:SYNC_STATE")},61188:function(e,t,r){r.d(t,{b:function(){return o}});var n=r(2265),o=globalThis?.document?n.useLayoutEffect:()=>{}},90420:function(e,t,r){r.d(t,{t:function(){return i}});var n=r(2265),o=r(61188);function i(e){let[t,r]=n.useState(void 0);return(0,o.b)(()=>{if(e){r({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let n,o;if(!Array.isArray(t)||!t.length)return;let i=t[0];if("borderBoxSize"in i){let e=i.borderBoxSize,t=Array.isArray(e)?e[0]:e;n=t.inlineSize,o=t.blockSize}else n=e.offsetWidth,o=e.offsetHeight;r({width:n,height:o})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}r(void 0)},[e]),t}},95098:function(e,t,r){r.d(t,{C2:function(){return l},TX:function(){return a},fC:function(){return s}});var n=r(2265),o=r(66840),i=r(57437),l=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"}),a=n.forwardRef((e,t)=>(0,i.jsx)(o.WV.span,{...e,ref:t,style:{...l,...e.style}}));a.displayName="VisuallyHidden";var s=a},90535:function(e,t,r){r.d(t,{j:function(){return l}});var n=r(61994);let o=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,i=n.W,l=(e,t)=>r=>{var n;if((null==t?void 0:t.variants)==null)return i(e,null==r?void 0:r.class,null==r?void 0:r.className);let{variants:l,defaultVariants:a}=t,s=Object.keys(l).map(e=>{let t=null==r?void 0:r[e],n=null==a?void 0:a[e];if(null===t)return null;let i=o(t)||o(n);return l[e][i]}),u=r&&Object.entries(r).reduce((e,t)=>{let[r,n]=t;return void 0===n||(e[r]=n),e},{});return i(e,s,null==t?void 0:null===(n=t.compoundVariants)||void 0===n?void 0:n.reduce((e,t)=>{let{class:r,className:n,...o}=t;return Object.entries(o).every(e=>{let[t,r]=e;return Array.isArray(r)?r.includes({...a,...u}[t]):({...a,...u})[t]===r})?[...e,r,n]:e},[]),null==r?void 0:r.class,null==r?void 0:r.className)}},61994:function(e,t,r){function n(){for(var e,t,r=0,n="",o=arguments.length;r<o;r++)(e=arguments[r])&&(t=function e(t){var r,n,o="";if("string"==typeof t||"number"==typeof t)o+=t;else if("object"==typeof t){if(Array.isArray(t)){var i=t.length;for(r=0;r<i;r++)t[r]&&(n=e(t[r]))&&(o&&(o+=" "),o+=n)}else for(n in t)t[n]&&(o&&(o+=" "),o+=n)}return o}(e))&&(n&&(n+=" "),n+=t);return n}r.d(t,{W:function(){return n}}),t.Z=n},96471:function(e,t,r){r.d(t,{Z:function(){return s}});var n=r(2265);let o=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),i=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return t.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim()};var l={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let a=(0,n.forwardRef)((e,t)=>{let{color:r="currentColor",size:o=24,strokeWidth:a=2,absoluteStrokeWidth:s,className:u="",children:c,iconNode:d,...f}=e;return(0,n.createElement)("svg",{ref:t,...l,width:o,height:o,stroke:r,strokeWidth:s?24*Number(a)/Number(o):a,className:i("lucide",u),...f},[...d.map(e=>{let[t,r]=e;return(0,n.createElement)(t,r)}),...Array.isArray(c)?c:[c]])}),s=(e,t)=>{let r=(0,n.forwardRef)((r,l)=>{let{className:s,...u}=r;return(0,n.createElement)(a,{ref:l,iconNode:t,className:i("lucide-".concat(o(e)),s),...u})});return r.displayName="".concat(e),r}},44986:function(e,t,r){r.d(t,{Z:function(){return n}});let n=(0,r(96471).Z)("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},53335:function(e,t,r){r.d(t,{m6:function(){return eu}});let n=e=>{let t=a(e),{conflictingClassGroups:r,conflictingClassGroupModifiers:n}=e;return{getClassGroupId:e=>{let r=e.split("-");return""===r[0]&&1!==r.length&&r.shift(),o(r,t)||l(e)},getConflictingClassGroupIds:(e,t)=>{let o=r[e]||[];return t&&n[e]?[...o,...n[e]]:o}}},o=(e,t)=>{if(0===e.length)return t.classGroupId;let r=e[0],n=t.nextPart.get(r),i=n?o(e.slice(1),n):void 0;if(i)return i;if(0===t.validators.length)return;let l=e.join("-");return t.validators.find(({validator:e})=>e(l))?.classGroupId},i=/^\[(.+)\]$/,l=e=>{if(i.test(e)){let t=i.exec(e)[1],r=t?.substring(0,t.indexOf(":"));if(r)return"arbitrary.."+r}},a=e=>{let{theme:t,classGroups:r}=e,n={nextPart:new Map,validators:[]};for(let e in r)s(r[e],n,e,t);return n},s=(e,t,r,n)=>{e.forEach(e=>{if("string"==typeof e){(""===e?t:u(t,e)).classGroupId=r;return}if("function"==typeof e){if(c(e)){s(e(n),t,r,n);return}t.validators.push({validator:e,classGroupId:r});return}Object.entries(e).forEach(([e,o])=>{s(o,u(t,e),r,n)})})},u=(e,t)=>{let r=e;return t.split("-").forEach(e=>{r.nextPart.has(e)||r.nextPart.set(e,{nextPart:new Map,validators:[]}),r=r.nextPart.get(e)}),r},c=e=>e.isThemeGetter,d=e=>{if(e<1)return{get:()=>void 0,set:()=>{}};let t=0,r=new Map,n=new Map,o=(o,i)=>{r.set(o,i),++t>e&&(t=0,n=r,r=new Map)};return{get(e){let t=r.get(e);return void 0!==t?t:void 0!==(t=n.get(e))?(o(e,t),t):void 0},set(e,t){r.has(e)?r.set(e,t):o(e,t)}}},f=e=>{let{prefix:t,experimentalParseClassName:r}=e,n=e=>{let t;let r=[],n=0,o=0,i=0;for(let l=0;l<e.length;l++){let a=e[l];if(0===n&&0===o){if(":"===a){r.push(e.slice(i,l)),i=l+1;continue}if("/"===a){t=l;continue}}"["===a?n++:"]"===a?n--:"("===a?o++:")"===a&&o--}let l=0===r.length?e:e.substring(i),a=p(l);return{modifiers:r,hasImportantModifier:a!==l,baseClassName:a,maybePostfixModifierPosition:t&&t>i?t-i:void 0}};if(t){let e=t+":",r=n;n=t=>t.startsWith(e)?r(t.substring(e.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:t,maybePostfixModifierPosition:void 0}}if(r){let e=n;n=t=>r({className:t,parseClassName:e})}return n},p=e=>e.endsWith("!")?e.substring(0,e.length-1):e.startsWith("!")?e.substring(1):e,m=e=>{let t=Object.fromEntries(e.orderSensitiveModifiers.map(e=>[e,!0]));return e=>{if(e.length<=1)return e;let r=[],n=[];return e.forEach(e=>{"["===e[0]||t[e]?(r.push(...n.sort(),e),n=[]):n.push(e)}),r.push(...n.sort()),r}},v=e=>({cache:d(e.cacheSize),parseClassName:f(e),sortModifiers:m(e),...n(e)}),b=/\s+/,g=(e,t)=>{let{parseClassName:r,getClassGroupId:n,getConflictingClassGroupIds:o,sortModifiers:i}=t,l=[],a=e.trim().split(b),s="";for(let e=a.length-1;e>=0;e-=1){let t=a[e],{isExternal:u,modifiers:c,hasImportantModifier:d,baseClassName:f,maybePostfixModifierPosition:p}=r(t);if(u){s=t+(s.length>0?" "+s:s);continue}let m=!!p,v=n(m?f.substring(0,p):f);if(!v){if(!m||!(v=n(f))){s=t+(s.length>0?" "+s:s);continue}m=!1}let b=i(c).join(":"),g=d?b+"!":b,h=g+v;if(l.includes(h))continue;l.push(h);let y=o(v,m);for(let e=0;e<y.length;++e){let t=y[e];l.push(g+t)}s=t+(s.length>0?" "+s:s)}return s};function h(){let e,t,r=0,n="";for(;r<arguments.length;)(e=arguments[r++])&&(t=y(e))&&(n&&(n+=" "),n+=t);return n}let y=e=>{let t;if("string"==typeof e)return e;let r="";for(let n=0;n<e.length;n++)e[n]&&(t=y(e[n]))&&(r&&(r+=" "),r+=t);return r},w=e=>{let t=t=>t[e]||[];return t.isThemeGetter=!0,t},x=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,k=/^\((?:(\w[\w-]*):)?(.+)\)$/i,E=/^\d+\/\d+$/,C=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,T=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,P=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,j=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,R=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,M=e=>E.test(e),N=e=>!!e&&!Number.isNaN(Number(e)),S=e=>!!e&&Number.isInteger(Number(e)),O=e=>e.endsWith("%")&&N(e.slice(0,-1)),L=e=>C.test(e),z=()=>!0,A=e=>T.test(e)&&!P.test(e),D=()=>!1,I=e=>j.test(e),_=e=>R.test(e),W=e=>!V(e)&&!K(e),F=e=>ee(e,eo,D),V=e=>x.test(e),$=e=>ee(e,ei,A),B=e=>ee(e,el,N),H=e=>ee(e,er,D),U=e=>ee(e,en,_),X=e=>ee(e,es,I),K=e=>k.test(e),Y=e=>et(e,ei),G=e=>et(e,ea),Z=e=>et(e,er),q=e=>et(e,eo),J=e=>et(e,en),Q=e=>et(e,es,!0),ee=(e,t,r)=>{let n=x.exec(e);return!!n&&(n[1]?t(n[1]):r(n[2]))},et=(e,t,r=!1)=>{let n=k.exec(e);return!!n&&(n[1]?t(n[1]):r)},er=e=>"position"===e||"percentage"===e,en=e=>"image"===e||"url"===e,eo=e=>"length"===e||"size"===e||"bg-size"===e,ei=e=>"length"===e,el=e=>"number"===e,ea=e=>"family-name"===e,es=e=>"shadow"===e,eu=function(e,...t){let r,n,o;let i=function(a){return n=(r=v(t.reduce((e,t)=>t(e),e()))).cache.get,o=r.cache.set,i=l,l(a)};function l(e){let t=n(e);if(t)return t;let i=g(e,r);return o(e,i),i}return function(){return i(h.apply(null,arguments))}}(()=>{let e=w("color"),t=w("font"),r=w("text"),n=w("font-weight"),o=w("tracking"),i=w("leading"),l=w("breakpoint"),a=w("container"),s=w("spacing"),u=w("radius"),c=w("shadow"),d=w("inset-shadow"),f=w("text-shadow"),p=w("drop-shadow"),m=w("blur"),v=w("perspective"),b=w("aspect"),g=w("ease"),h=w("animate"),y=()=>["auto","avoid","all","avoid-page","page","left","right","column"],x=()=>["center","top","bottom","left","right","top-left","left-top","top-right","right-top","bottom-right","right-bottom","bottom-left","left-bottom"],k=()=>[...x(),K,V],E=()=>["auto","hidden","clip","visible","scroll"],C=()=>["auto","contain","none"],T=()=>[K,V,s],P=()=>[M,"full","auto",...T()],j=()=>[S,"none","subgrid",K,V],R=()=>["auto",{span:["full",S,K,V]},S,K,V],A=()=>[S,"auto",K,V],D=()=>["auto","min","max","fr",K,V],I=()=>["start","end","center","between","around","evenly","stretch","baseline","center-safe","end-safe"],_=()=>["start","end","center","stretch","center-safe","end-safe"],ee=()=>["auto",...T()],et=()=>[M,"auto","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",...T()],er=()=>[e,K,V],en=()=>[...x(),Z,H,{position:[K,V]}],eo=()=>["no-repeat",{repeat:["","x","y","space","round"]}],ei=()=>["auto","cover","contain",q,F,{size:[K,V]}],el=()=>[O,Y,$],ea=()=>["","none","full",u,K,V],es=()=>["",N,Y,$],eu=()=>["solid","dashed","dotted","double"],ec=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],ed=()=>[N,O,Z,H],ef=()=>["","none",m,K,V],ep=()=>["none",N,K,V],em=()=>["none",N,K,V],ev=()=>[N,K,V],eb=()=>[M,"full",...T()];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[L],breakpoint:[L],color:[z],container:[L],"drop-shadow":[L],ease:["in","out","in-out"],font:[W],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[L],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[L],shadow:[L],spacing:["px",N],text:[L],"text-shadow":[L],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",M,V,K,b]}],container:["container"],columns:[{columns:[N,V,K,a]}],"break-after":[{"break-after":y()}],"break-before":[{"break-before":y()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:k()}],overflow:[{overflow:E()}],"overflow-x":[{"overflow-x":E()}],"overflow-y":[{"overflow-y":E()}],overscroll:[{overscroll:C()}],"overscroll-x":[{"overscroll-x":C()}],"overscroll-y":[{"overscroll-y":C()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:P()}],"inset-x":[{"inset-x":P()}],"inset-y":[{"inset-y":P()}],start:[{start:P()}],end:[{end:P()}],top:[{top:P()}],right:[{right:P()}],bottom:[{bottom:P()}],left:[{left:P()}],visibility:["visible","invisible","collapse"],z:[{z:[S,"auto",K,V]}],basis:[{basis:[M,"full","auto",a,...T()]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[N,M,"auto","initial","none",V]}],grow:[{grow:["",N,K,V]}],shrink:[{shrink:["",N,K,V]}],order:[{order:[S,"first","last","none",K,V]}],"grid-cols":[{"grid-cols":j()}],"col-start-end":[{col:R()}],"col-start":[{"col-start":A()}],"col-end":[{"col-end":A()}],"grid-rows":[{"grid-rows":j()}],"row-start-end":[{row:R()}],"row-start":[{"row-start":A()}],"row-end":[{"row-end":A()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":D()}],"auto-rows":[{"auto-rows":D()}],gap:[{gap:T()}],"gap-x":[{"gap-x":T()}],"gap-y":[{"gap-y":T()}],"justify-content":[{justify:[...I(),"normal"]}],"justify-items":[{"justify-items":[..._(),"normal"]}],"justify-self":[{"justify-self":["auto",..._()]}],"align-content":[{content:["normal",...I()]}],"align-items":[{items:[..._(),{baseline:["","last"]}]}],"align-self":[{self:["auto",..._(),{baseline:["","last"]}]}],"place-content":[{"place-content":I()}],"place-items":[{"place-items":[..._(),"baseline"]}],"place-self":[{"place-self":["auto",..._()]}],p:[{p:T()}],px:[{px:T()}],py:[{py:T()}],ps:[{ps:T()}],pe:[{pe:T()}],pt:[{pt:T()}],pr:[{pr:T()}],pb:[{pb:T()}],pl:[{pl:T()}],m:[{m:ee()}],mx:[{mx:ee()}],my:[{my:ee()}],ms:[{ms:ee()}],me:[{me:ee()}],mt:[{mt:ee()}],mr:[{mr:ee()}],mb:[{mb:ee()}],ml:[{ml:ee()}],"space-x":[{"space-x":T()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":T()}],"space-y-reverse":["space-y-reverse"],size:[{size:et()}],w:[{w:[a,"screen",...et()]}],"min-w":[{"min-w":[a,"screen","none",...et()]}],"max-w":[{"max-w":[a,"screen","none","prose",{screen:[l]},...et()]}],h:[{h:["screen","lh",...et()]}],"min-h":[{"min-h":["screen","lh","none",...et()]}],"max-h":[{"max-h":["screen","lh",...et()]}],"font-size":[{text:["base",r,Y,$]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[n,K,B]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",O,V]}],"font-family":[{font:[G,V,t]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[o,K,V]}],"line-clamp":[{"line-clamp":[N,"none",K,B]}],leading:[{leading:[i,...T()]}],"list-image":[{"list-image":["none",K,V]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",K,V]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:er()}],"text-color":[{text:er()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...eu(),"wavy"]}],"text-decoration-thickness":[{decoration:[N,"from-font","auto",K,$]}],"text-decoration-color":[{decoration:er()}],"underline-offset":[{"underline-offset":[N,"auto",K,V]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:T()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",K,V]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],wrap:[{wrap:["break-word","anywhere","normal"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",K,V]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:en()}],"bg-repeat":[{bg:eo()}],"bg-size":[{bg:ei()}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},S,K,V],radial:["",K,V],conic:[S,K,V]},J,U]}],"bg-color":[{bg:er()}],"gradient-from-pos":[{from:el()}],"gradient-via-pos":[{via:el()}],"gradient-to-pos":[{to:el()}],"gradient-from":[{from:er()}],"gradient-via":[{via:er()}],"gradient-to":[{to:er()}],rounded:[{rounded:ea()}],"rounded-s":[{"rounded-s":ea()}],"rounded-e":[{"rounded-e":ea()}],"rounded-t":[{"rounded-t":ea()}],"rounded-r":[{"rounded-r":ea()}],"rounded-b":[{"rounded-b":ea()}],"rounded-l":[{"rounded-l":ea()}],"rounded-ss":[{"rounded-ss":ea()}],"rounded-se":[{"rounded-se":ea()}],"rounded-ee":[{"rounded-ee":ea()}],"rounded-es":[{"rounded-es":ea()}],"rounded-tl":[{"rounded-tl":ea()}],"rounded-tr":[{"rounded-tr":ea()}],"rounded-br":[{"rounded-br":ea()}],"rounded-bl":[{"rounded-bl":ea()}],"border-w":[{border:es()}],"border-w-x":[{"border-x":es()}],"border-w-y":[{"border-y":es()}],"border-w-s":[{"border-s":es()}],"border-w-e":[{"border-e":es()}],"border-w-t":[{"border-t":es()}],"border-w-r":[{"border-r":es()}],"border-w-b":[{"border-b":es()}],"border-w-l":[{"border-l":es()}],"divide-x":[{"divide-x":es()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":es()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:[...eu(),"hidden","none"]}],"divide-style":[{divide:[...eu(),"hidden","none"]}],"border-color":[{border:er()}],"border-color-x":[{"border-x":er()}],"border-color-y":[{"border-y":er()}],"border-color-s":[{"border-s":er()}],"border-color-e":[{"border-e":er()}],"border-color-t":[{"border-t":er()}],"border-color-r":[{"border-r":er()}],"border-color-b":[{"border-b":er()}],"border-color-l":[{"border-l":er()}],"divide-color":[{divide:er()}],"outline-style":[{outline:[...eu(),"none","hidden"]}],"outline-offset":[{"outline-offset":[N,K,V]}],"outline-w":[{outline:["",N,Y,$]}],"outline-color":[{outline:er()}],shadow:[{shadow:["","none",c,Q,X]}],"shadow-color":[{shadow:er()}],"inset-shadow":[{"inset-shadow":["none",d,Q,X]}],"inset-shadow-color":[{"inset-shadow":er()}],"ring-w":[{ring:es()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:er()}],"ring-offset-w":[{"ring-offset":[N,$]}],"ring-offset-color":[{"ring-offset":er()}],"inset-ring-w":[{"inset-ring":es()}],"inset-ring-color":[{"inset-ring":er()}],"text-shadow":[{"text-shadow":["none",f,Q,X]}],"text-shadow-color":[{"text-shadow":er()}],opacity:[{opacity:[N,K,V]}],"mix-blend":[{"mix-blend":[...ec(),"plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":ec()}],"mask-clip":[{"mask-clip":["border","padding","content","fill","stroke","view"]},"mask-no-clip"],"mask-composite":[{mask:["add","subtract","intersect","exclude"]}],"mask-image-linear-pos":[{"mask-linear":[N]}],"mask-image-linear-from-pos":[{"mask-linear-from":ed()}],"mask-image-linear-to-pos":[{"mask-linear-to":ed()}],"mask-image-linear-from-color":[{"mask-linear-from":er()}],"mask-image-linear-to-color":[{"mask-linear-to":er()}],"mask-image-t-from-pos":[{"mask-t-from":ed()}],"mask-image-t-to-pos":[{"mask-t-to":ed()}],"mask-image-t-from-color":[{"mask-t-from":er()}],"mask-image-t-to-color":[{"mask-t-to":er()}],"mask-image-r-from-pos":[{"mask-r-from":ed()}],"mask-image-r-to-pos":[{"mask-r-to":ed()}],"mask-image-r-from-color":[{"mask-r-from":er()}],"mask-image-r-to-color":[{"mask-r-to":er()}],"mask-image-b-from-pos":[{"mask-b-from":ed()}],"mask-image-b-to-pos":[{"mask-b-to":ed()}],"mask-image-b-from-color":[{"mask-b-from":er()}],"mask-image-b-to-color":[{"mask-b-to":er()}],"mask-image-l-from-pos":[{"mask-l-from":ed()}],"mask-image-l-to-pos":[{"mask-l-to":ed()}],"mask-image-l-from-color":[{"mask-l-from":er()}],"mask-image-l-to-color":[{"mask-l-to":er()}],"mask-image-x-from-pos":[{"mask-x-from":ed()}],"mask-image-x-to-pos":[{"mask-x-to":ed()}],"mask-image-x-from-color":[{"mask-x-from":er()}],"mask-image-x-to-color":[{"mask-x-to":er()}],"mask-image-y-from-pos":[{"mask-y-from":ed()}],"mask-image-y-to-pos":[{"mask-y-to":ed()}],"mask-image-y-from-color":[{"mask-y-from":er()}],"mask-image-y-to-color":[{"mask-y-to":er()}],"mask-image-radial":[{"mask-radial":[K,V]}],"mask-image-radial-from-pos":[{"mask-radial-from":ed()}],"mask-image-radial-to-pos":[{"mask-radial-to":ed()}],"mask-image-radial-from-color":[{"mask-radial-from":er()}],"mask-image-radial-to-color":[{"mask-radial-to":er()}],"mask-image-radial-shape":[{"mask-radial":["circle","ellipse"]}],"mask-image-radial-size":[{"mask-radial":[{closest:["side","corner"],farthest:["side","corner"]}]}],"mask-image-radial-pos":[{"mask-radial-at":x()}],"mask-image-conic-pos":[{"mask-conic":[N]}],"mask-image-conic-from-pos":[{"mask-conic-from":ed()}],"mask-image-conic-to-pos":[{"mask-conic-to":ed()}],"mask-image-conic-from-color":[{"mask-conic-from":er()}],"mask-image-conic-to-color":[{"mask-conic-to":er()}],"mask-mode":[{mask:["alpha","luminance","match"]}],"mask-origin":[{"mask-origin":["border","padding","content","fill","stroke","view"]}],"mask-position":[{mask:en()}],"mask-repeat":[{mask:eo()}],"mask-size":[{mask:ei()}],"mask-type":[{"mask-type":["alpha","luminance"]}],"mask-image":[{mask:["none",K,V]}],filter:[{filter:["","none",K,V]}],blur:[{blur:ef()}],brightness:[{brightness:[N,K,V]}],contrast:[{contrast:[N,K,V]}],"drop-shadow":[{"drop-shadow":["","none",p,Q,X]}],"drop-shadow-color":[{"drop-shadow":er()}],grayscale:[{grayscale:["",N,K,V]}],"hue-rotate":[{"hue-rotate":[N,K,V]}],invert:[{invert:["",N,K,V]}],saturate:[{saturate:[N,K,V]}],sepia:[{sepia:["",N,K,V]}],"backdrop-filter":[{"backdrop-filter":["","none",K,V]}],"backdrop-blur":[{"backdrop-blur":ef()}],"backdrop-brightness":[{"backdrop-brightness":[N,K,V]}],"backdrop-contrast":[{"backdrop-contrast":[N,K,V]}],"backdrop-grayscale":[{"backdrop-grayscale":["",N,K,V]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[N,K,V]}],"backdrop-invert":[{"backdrop-invert":["",N,K,V]}],"backdrop-opacity":[{"backdrop-opacity":[N,K,V]}],"backdrop-saturate":[{"backdrop-saturate":[N,K,V]}],"backdrop-sepia":[{"backdrop-sepia":["",N,K,V]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":T()}],"border-spacing-x":[{"border-spacing-x":T()}],"border-spacing-y":[{"border-spacing-y":T()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",K,V]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[N,"initial",K,V]}],ease:[{ease:["linear","initial",g,K,V]}],delay:[{delay:[N,K,V]}],animate:[{animate:["none",h,K,V]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[v,K,V]}],"perspective-origin":[{"perspective-origin":k()}],rotate:[{rotate:ep()}],"rotate-x":[{"rotate-x":ep()}],"rotate-y":[{"rotate-y":ep()}],"rotate-z":[{"rotate-z":ep()}],scale:[{scale:em()}],"scale-x":[{"scale-x":em()}],"scale-y":[{"scale-y":em()}],"scale-z":[{"scale-z":em()}],"scale-3d":["scale-3d"],skew:[{skew:ev()}],"skew-x":[{"skew-x":ev()}],"skew-y":[{"skew-y":ev()}],transform:[{transform:[K,V,"","none","gpu","cpu"]}],"transform-origin":[{origin:k()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:eb()}],"translate-x":[{"translate-x":eb()}],"translate-y":[{"translate-y":eb()}],"translate-z":[{"translate-z":eb()}],"translate-none":["translate-none"],accent:[{accent:er()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:er()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",K,V]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":T()}],"scroll-mx":[{"scroll-mx":T()}],"scroll-my":[{"scroll-my":T()}],"scroll-ms":[{"scroll-ms":T()}],"scroll-me":[{"scroll-me":T()}],"scroll-mt":[{"scroll-mt":T()}],"scroll-mr":[{"scroll-mr":T()}],"scroll-mb":[{"scroll-mb":T()}],"scroll-ml":[{"scroll-ml":T()}],"scroll-p":[{"scroll-p":T()}],"scroll-px":[{"scroll-px":T()}],"scroll-py":[{"scroll-py":T()}],"scroll-ps":[{"scroll-ps":T()}],"scroll-pe":[{"scroll-pe":T()}],"scroll-pt":[{"scroll-pt":T()}],"scroll-pr":[{"scroll-pr":T()}],"scroll-pb":[{"scroll-pb":T()}],"scroll-pl":[{"scroll-pl":T()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",K,V]}],fill:[{fill:["none",...er()]}],"stroke-w":[{stroke:[N,Y,$,B]}],stroke:[{stroke:["none",...er()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-x","border-w-y","border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-x","border-color-y","border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["*","**","after","backdrop","before","details-content","file","first-letter","first-line","marker","placeholder","selection"]}})}}]);