"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8575],{95274:function(e,t,n){var u=n(2265),r="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},i=u.useSyncExternalStore,o=u.useRef,c=u.useEffect,l=u.useMemo,a=u.useDebugValue;t.useSyncExternalStoreWithSelector=function(e,t,n,u,f){var s=o(null);if(null===s.current){var v={hasValue:!1,value:null};s.current=v}else v=s.current;var b=i(e,(s=l(function(){function e(e){if(!c){if(c=!0,i=e,e=u(e),void 0!==f&&v.hasValue){var t=v.value;if(f(t,e))return o=t}return o=e}if(t=o,r(i,e))return t;var n=u(e);return void 0!==f&&f(t,n)?(i=e,t):(i=e,o=n)}var i,o,c=!1,l=void 0===n?null:n;return[function(){return e(t())},null===l?void 0:function(){return e(l())}]},[t,n,u,f]))[0],s[1]);return c(function(){v.hasValue=!0,v.value=b},[b]),a(b),b}},76548:function(e,t,n){e.exports=n(95274)},68575:function(e,t,n){n.d(t,{I0:function(){return y},v9:function(){return x},zt:function(){return v}});var u=n(2265),r=n(76548),i={notify(){},get:()=>[]},o=!!("undefined"!=typeof window&&void 0!==window.document&&void 0!==window.document.createElement),c="undefined"!=typeof navigator&&"ReactNative"===navigator.product,l=o||c?u.useLayoutEffect:u.useEffect,a=Symbol.for("react-redux-context"),f="undefined"!=typeof globalThis?globalThis:{},s=function(){if(!u.createContext)return{};let e=f[a]??=new Map,t=e.get(u.createContext);return t||(t=u.createContext(null),e.set(u.createContext,t)),t}(),v=function(e){let{children:t,context:n,serverState:r,store:o}=e,c=u.useMemo(()=>{let e=function(e,t){let n;let u=i,r=0,o=!1;function c(){f.onStateChange&&f.onStateChange()}function l(){if(r++,!n){let t,r;n=e.subscribe(c),t=null,r=null,u={clear(){t=null,r=null},notify(){(()=>{let e=t;for(;e;)e.callback(),e=e.next})()},get(){let e=[],n=t;for(;n;)e.push(n),n=n.next;return e},subscribe(e){let n=!0,u=r={callback:e,next:null,prev:r};return u.prev?u.prev.next=u:t=u,function(){n&&null!==t&&(n=!1,u.next?u.next.prev=u.prev:r=u.prev,u.prev?u.prev.next=u.next:t=u.next)}}}}}function a(){r--,n&&0===r&&(n(),n=void 0,u.clear(),u=i)}let f={addNestedSub:function(e){l();let t=u.subscribe(e),n=!1;return()=>{n||(n=!0,t(),a())}},notifyNestedSubs:function(){u.notify()},handleChangeWrapper:c,isSubscribed:function(){return o},trySubscribe:function(){o||(o=!0,l())},tryUnsubscribe:function(){o&&(o=!1,a())},getListeners:()=>u};return f}(o);return{store:o,subscription:e,getServerState:r?()=>r:void 0}},[o,r]),a=u.useMemo(()=>o.getState(),[o]);return l(()=>{let{subscription:e}=c;return e.onStateChange=e.notifyNestedSubs,e.trySubscribe(),a!==o.getState()&&e.notifyNestedSubs(),()=>{e.tryUnsubscribe(),e.onStateChange=void 0}},[c,a]),u.createElement((n||s).Provider,{value:c},t)};function b(e=s){return function(){return u.useContext(e)}}var d=b();function S(e=s){let t=e===s?d:b(e),n=()=>{let{store:e}=t();return e};return Object.assign(n,{withTypes:()=>n}),n}var p=S(),y=function(e=s){let t=e===s?p:S(e),n=()=>t().dispatch;return Object.assign(n,{withTypes:()=>n}),n}(),g=(e,t)=>e===t,x=function(e=s){let t=e===s?d:b(e),n=(e,n={})=>{let{equalityFn:i=g}="function"==typeof n?{equalityFn:n}:n,{store:o,subscription:c,getServerState:l}=t();u.useRef(!0);let a=u.useCallback({[e.name]:t=>e(t)}[e.name],[e]),f=(0,r.useSyncExternalStoreWithSelector)(c.addNestedSub,o.getState,l||o.getState,a,i);return u.useDebugValue(f),f};return Object.assign(n,{withTypes:()=>n}),n}()}}]);