"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8884],{88884:function(e,t,n){n.d(t,{KE:function(){return i},Pb:function(){return d},T1:function(){return g},Uc:function(){return u},createPlanAssignment:function(){return c},fH:function(){return o},updatePlanAssignment:function(){return l}});var s=n(61103);let a=(0,s.bR)(),r=()=>({"Content-Type":"application/json","user-id":(0,s.n5)()}),o=async function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=arguments.length>2?arguments[2]:void 0;try{let o=(0,s.n5)();console.log("\uD83D\uDD0D Plan Assignment API Debug:",{companyId:e,userId:o,filters:t,pagination:n,apiBaseUrl:a});let c=new URLSearchParams;t.status&&c.append("status",t.status),t.planId&&c.append("planId",t.planId),t.assignmentYear&&c.append("assignmentYear",t.assignmentYear.toString()),t.referenceDate&&c.append("referenceDate",t.referenceDate),void 0!==t.includePlanData&&c.append("includePlanData",t.includePlanData.toString()),t.enrollmentPeriodOnly&&c.append("enrollmentPeriodOnly","true"),t.effectiveOnly&&c.append("effectiveOnly","true"),t.futureOnly&&c.append("futureOnly","true"),t.includeInactive&&c.append("includeInactive","true"),t.includeExpired&&c.append("includeExpired","true"),t.brokerId&&c.append("brokerId",t.brokerId),n&&(c.append("page",n.page.toString()),c.append("limit",n.limit.toString()));let l="".concat(a,"/api/pre-enrollment/plan-assignments/company/").concat(e).concat(c.toString()?"?".concat(c.toString()):"");console.log("\uD83D\uDCE1 Fetching plan assignments from optimized API:",l);let i=await fetch(l,{method:"GET",headers:r()});if(console.log("Plan assignments API response status:",i.status),!i.ok){let t="HTTP error! status: ".concat(i.status);if(403===i.status){let t=(0,s.n5)();return console.error("\uD83D\uDEAB 403 Forbidden Error Details:",{url:l,userId:t,companyId:e,userIdSource:localStorage.getItem("userid1")?"userid1":localStorage.getItem("userId")?"userId":"none"}),console.log("\uD83D\uDD27 Broker has no existing plan assignments for this company - returning empty result to allow plan creation"),{success:!0,data:{assignments:[],count:0,message:"No existing plan assignments. You can create new plan assignments for this company.",canCreateAssignments:!0,accessDeniedToExisting:!0}}}try{let e=await i.json();console.error("API Error Response:",e),t+=" - ".concat(e.error||e.message||"Unknown error")}catch(e){console.log("No additional error details available")}throw Error(t)}let u=await i.json();return console.log("Plan assignments result:",u),console.log("First assignment details:",u.assignments[0]),{success:!0,data:u}}catch(e){return console.error("Error fetching plan assignments:",e),{success:!1,error:"Failed to fetch plan assignments"}}},c=async e=>{try{console.log("\uD83D\uDE80 Creating plan assignment with data:",e),console.log("\uD83D\uDD0D Required fields check:",{planId:e.planId,companyId:e.companyId,rateStructure:e.rateStructure,coverageTiers:e.coverageTiers,planEffectiveDate:e.planEffectiveDate,planEndDate:e.planEndDate,enrollmentStartDate:e.enrollmentStartDate,enrollmentEndDate:e.enrollmentEndDate}),console.log("\uD83D\uDCE1 Request URL:","".concat(a,"/api/pre-enrollment/plan-assignments")),console.log("\uD83D\uDD11 Headers:",r());let t=await fetch("".concat(a,"/api/pre-enrollment/plan-assignments"),{method:"POST",headers:r(),body:JSON.stringify(e)});if(!t.ok){let n=await t.json().catch(()=>({})),s=n.error||n.message||"HTTP error! status: ".concat(t.status),a=n.details||n.required||[];return console.error("❌ Plan assignment creation failed:",{status:t.status,error:s,details:a,fullErrorData:n,sentData:e}),{success:!1,error:"".concat(s).concat(a.length>0?" - ".concat(a.join(", ")):"")}}let n=await t.json();return{success:!0,data:n.assignment}}catch(e){return console.error("Error creating plan assignment:",e),{success:!1,error:e instanceof Error?e.message:"Failed to create plan assignment"}}},l=async(e,t)=>{try{console.log("Updating plan assignment:",e,"with data:",t);let n=await fetch("".concat(a,"/api/pre-enrollment/plan-assignments/").concat(e),{method:"PUT",headers:r(),body:JSON.stringify(t)});if(console.log("Update response status:",n.status),!n.ok){let e=await n.json().catch(()=>({})),s=e.error||e.message||"HTTP error! status: ".concat(n.status),a=e.details||[];return console.error("Plan assignment update failed:",{status:n.status,error:s,details:a,updateData:t}),{success:!1,error:"".concat(s).concat(a.length>0?" - ".concat(a.join(", ")):"")}}let s=await n.json();return console.log("Update successful:",s),{success:!0,data:s.assignment}}catch(e){return console.error("Error updating plan assignment:",e),{success:!1,error:e instanceof Error?e.message:"Failed to update plan assignment"}}},i=async e=>{try{let t=await fetch("".concat(a,"/api/pre-enrollment/plan-assignments/").concat(e),{method:"DELETE",headers:r()});if(!t.ok)throw Error("HTTP error! status: ".concat(t.status));let n=await t.json();return{success:!0,data:{message:n.message||"Plan assignment deleted successfully"}}}catch(e){return console.error("Error deleting plan assignment:",e),{success:!1,error:"Failed to delete plan assignment"}}},u=async(e,t)=>{try{console.log("Cloning plan assignment:",e,"with overrides:",t);let n=await fetch("".concat(a,"/api/pre-enrollment/plan-assignments/").concat(e,"/clone"),{method:"POST",headers:r(),body:JSON.stringify(t||{})});if(console.log("Clone response status:",n.status),!n.ok){let e=await n.json().catch(()=>({})),s=e.error||e.message||"HTTP error! status: ".concat(n.status),a=e.details||[];return console.error("Plan assignment clone failed:",{status:n.status,error:s,details:a,cloneData:t}),{success:!1,error:"".concat(s).concat(a.length>0?" - ".concat(a.join(", ")):"")}}let s=await n.json();return console.log("Clone successful:",s),{success:!0,data:s.assignment}}catch(e){return console.error("Error cloning plan assignment:",e),{success:!1,error:e instanceof Error?e.message:"Failed to clone plan assignment"}}},d=async()=>{try{let e=await fetch("".concat(a,"/api/pre-enrollment/plans/assignable"),{method:"GET",headers:r()});if(!e.ok)throw Error("HTTP error! status: ".concat(e.status));let t=((await e.json()).plans||[]).map((e,t)=>{console.log("\uD83D\uDD0D Plan ".concat(t," raw structure:"),e),console.log("\uD83D\uDD0D Plan ".concat(t," _id:"),e._id);let n=null;if(e._id&&("string"==typeof e._id?n=e._id:e._id.$oid?n=e._id.$oid:e._id.toString&&(n=e._id.toString())),e._doc){var s,a;let r=(null===(s=e._doc._id)||void 0===s?void 0:s.$oid)||(null===(a=e._doc._id)||void 0===a?void 0:a.toString())||e._doc._id,o={...e._doc,_id:n||r};return console.log("\uD83D\uDD0D Plan ".concat(t," transformed:"),o),o}let r={...e,_id:n};return console.log("\uD83D\uDD0D Plan ".concat(t," transformed:"),r),r});return{success:!0,data:t}}catch(e){return console.error("Error fetching assignable plans:",e),{success:!1,error:"Failed to fetch assignable plans"}}},g=async()=>{try{let e=(0,s.n5)();console.log("\uD83D\uDD0D Fetching broker plan assignments count for userId:",e);let t="".concat(a,"/api/pre-enrollment/plan-assignments?includePlanData=false");console.log("\uD83D\uDCE1 Fetching broker assignments count from:",t);let n=await fetch(t,{method:"GET",headers:r()});if(console.log("Broker assignments count API response status:",n.status),n.ok){let e=await n.json();return console.log("✅ Broker assignments count response:",e),{success:!0,data:{count:e.count||0}}}{let e=await n.text();return console.error("❌ Failed to fetch broker assignments count:",n.status,e),{success:!1,error:"Failed to fetch assignments count: ".concat(n.status)}}}catch(e){return console.error("❌ Error fetching broker assignments count:",e),{success:!1,error:"Network error while fetching assignments count"}}}},61103:function(e,t,n){n.d(t,{GU:function(){return r},bR:function(){return s},n5:function(){return a}});let s=()=>"http://localhost:8080",a=()=>{let e="userid1",t="userId",n=localStorage.getItem(e)||localStorage.getItem(t);return(console.log("\uD83D\uDD0D getUserId debug:",{primaryKey:e,altKey:t,primaryValue:localStorage.getItem(e),altValue:localStorage.getItem(t),finalUserId:n}),n)?n:(console.error("❌ User ID not found in localStorage"),"default-user")},r=()=>"https://bot.benosphere.com"}}]);