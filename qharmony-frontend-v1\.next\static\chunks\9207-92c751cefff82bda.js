"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9207],{53284:function(e,t,r){var a=r(94630),o=r(57437);t.Z=(0,a.Z)((0,o.jsx)("path",{d:"M6 17h3l2-4V7H5v6h3zm8 0h3l2-4V7h-6v6h3z"}),"FormatQuote")},42596:function(e,t,r){r.d(t,{V:function(){return n}});var a=r(94143),o=r(50738);function n(e){return(0,o.ZP)("MuiDivider",e)}let l=(0,a.Z)("MuiDivider",["root","absolute","fullWidth","inset","middle","flexItem","light","vertical","withChildren","withChildrenVertical","textAlignRight","textAlignLeft","wrapper","wrapperVertical"]);t.Z=l},85860:function(e,t,r){r.d(t,{Z:function(){return C}});var a=r(2265),o=r(61994),n=r(20801),l=r(66515),i=r(16210),s=r(21086),c=r(37053),d=r(46387),p=r(85657),u=r(94143),m=r(50738);function b(e){return(0,m.ZP)("MuiFormControlLabel",e)}let v=(0,u.Z)("MuiFormControlLabel",["root","labelPlacementStart","labelPlacementTop","labelPlacementBottom","disabled","label","error","required","asterisk"]);var f=r(48904),g=r(79114),h=r(57437);let y=e=>{let{classes:t,disabled:r,labelPlacement:a,error:o,required:l}=e,i={root:["root",r&&"disabled","labelPlacement".concat((0,p.Z)(a)),o&&"error",l&&"required"],label:["label",r&&"disabled"],asterisk:["asterisk",o&&"error"]};return(0,n.Z)(i,b,t)},Z=(0,i.default)("label",{name:"MuiFormControlLabel",slot:"Root",overridesResolver:(e,t)=>{let{ownerState:r}=e;return[{["& .".concat(v.label)]:t.label},t.root,t["labelPlacement".concat((0,p.Z)(r.labelPlacement))]]}})((0,s.Z)(e=>{let{theme:t}=e;return{display:"inline-flex",alignItems:"center",cursor:"pointer",verticalAlign:"middle",WebkitTapHighlightColor:"transparent",marginLeft:-11,marginRight:16,["&.".concat(v.disabled)]:{cursor:"default"},["& .".concat(v.label)]:{["&.".concat(v.disabled)]:{color:(t.vars||t).palette.text.disabled}},variants:[{props:{labelPlacement:"start"},style:{flexDirection:"row-reverse",marginRight:-11}},{props:{labelPlacement:"top"},style:{flexDirection:"column-reverse"}},{props:{labelPlacement:"bottom"},style:{flexDirection:"column"}},{props:e=>{let{labelPlacement:t}=e;return"start"===t||"top"===t||"bottom"===t},style:{marginLeft:16}}]}})),x=(0,i.default)("span",{name:"MuiFormControlLabel",slot:"Asterisk",overridesResolver:(e,t)=>t.asterisk})((0,s.Z)(e=>{let{theme:t}=e;return{["&.".concat(v.error)]:{color:(t.vars||t).palette.error.main}}}));var C=a.forwardRef(function(e,t){var r;let n=(0,c.i)({props:e,name:"MuiFormControlLabel"}),{checked:i,className:s,componentsProps:p={},control:u,disabled:m,disableTypography:b,inputRef:v,label:C,labelPlacement:k="end",name:M,onChange:P,required:w,slots:I={},slotProps:L={},value:O,...R}=n,F=(0,l.Z)(),V=null!==(r=null!=m?m:u.props.disabled)&&void 0!==r?r:null==F?void 0:F.disabled,j=null!=w?w:u.props.required,q={disabled:V,required:j};["checked","name","onChange","value","inputRef"].forEach(e=>{void 0===u.props[e]&&void 0!==n[e]&&(q[e]=n[e])});let N=(0,f.Z)({props:n,muiFormControl:F,states:["error"]}),S={...n,disabled:V,labelPlacement:k,required:j,error:N.error},D=y(S),T={slots:I,slotProps:{...p,...L}},[B,H]=(0,g.Z)("typography",{elementType:d.Z,externalForwardedProps:T,ownerState:S}),z=C;return null==z||z.type===d.Z||b||(z=(0,h.jsx)(B,{component:"span",...H,className:(0,o.Z)(D.label,null==H?void 0:H.className),children:z})),(0,h.jsxs)(Z,{className:(0,o.Z)(D.root,s),ownerState:S,ref:t,...R,children:[a.cloneElement(u,q),j?(0,h.jsxs)("div",{children:[z,(0,h.jsxs)(x,{ownerState:S,"aria-hidden":!0,className:D.asterisk,children:[" ","*"]})]}):z]})})},67752:function(e,t,r){r.d(t,{f:function(){return n}});var a=r(94143),o=r(50738);function n(e){return(0,o.ZP)("MuiListItemIcon",e)}let l=(0,a.Z)("MuiListItemIcon",["root","alignItemsFlexStart"]);t.Z=l},42187:function(e,t,r){r.d(t,{Z:function(){return P}});var a=r(2265),o=r(61994),n=r(20801),l=r(65208),i=r(34765),s=r(16210),c=r(21086),d=r(37053),p=r(15566),u=r(52559),m=r(84217),b=r(60118),v=r(42596),f=r(67752),g=r(3127),h=r(94143),y=r(50738);function Z(e){return(0,y.ZP)("MuiMenuItem",e)}let x=(0,h.Z)("MuiMenuItem",["root","focusVisible","dense","disabled","divider","gutters","selected"]);var C=r(57437);let k=e=>{let{disabled:t,dense:r,divider:a,disableGutters:o,selected:l,classes:i}=e,s=(0,n.Z)({root:["root",r&&"dense",t&&"disabled",!o&&"gutters",a&&"divider",l&&"selected"]},Z,i);return{...i,...s}},M=(0,s.default)(u.Z,{shouldForwardProp:e=>(0,i.Z)(e)||"classes"===e,name:"MuiMenuItem",slot:"Root",overridesResolver:(e,t)=>{let{ownerState:r}=e;return[t.root,r.dense&&t.dense,r.divider&&t.divider,!r.disableGutters&&t.gutters]}})((0,c.Z)(e=>{let{theme:t}=e;return{...t.typography.body1,display:"flex",justifyContent:"flex-start",alignItems:"center",position:"relative",textDecoration:"none",minHeight:48,paddingTop:6,paddingBottom:6,boxSizing:"border-box",whiteSpace:"nowrap","&:hover":{textDecoration:"none",backgroundColor:(t.vars||t).palette.action.hover,"@media (hover: none)":{backgroundColor:"transparent"}},["&.".concat(x.selected)]:{backgroundColor:t.vars?"rgba(".concat(t.vars.palette.primary.mainChannel," / ").concat(t.vars.palette.action.selectedOpacity,")"):(0,l.Fq)(t.palette.primary.main,t.palette.action.selectedOpacity),["&.".concat(x.focusVisible)]:{backgroundColor:t.vars?"rgba(".concat(t.vars.palette.primary.mainChannel," / calc(").concat(t.vars.palette.action.selectedOpacity," + ").concat(t.vars.palette.action.focusOpacity,"))"):(0,l.Fq)(t.palette.primary.main,t.palette.action.selectedOpacity+t.palette.action.focusOpacity)}},["&.".concat(x.selected,":hover")]:{backgroundColor:t.vars?"rgba(".concat(t.vars.palette.primary.mainChannel," / calc(").concat(t.vars.palette.action.selectedOpacity," + ").concat(t.vars.palette.action.hoverOpacity,"))"):(0,l.Fq)(t.palette.primary.main,t.palette.action.selectedOpacity+t.palette.action.hoverOpacity),"@media (hover: none)":{backgroundColor:t.vars?"rgba(".concat(t.vars.palette.primary.mainChannel," / ").concat(t.vars.palette.action.selectedOpacity,")"):(0,l.Fq)(t.palette.primary.main,t.palette.action.selectedOpacity)}},["&.".concat(x.focusVisible)]:{backgroundColor:(t.vars||t).palette.action.focus},["&.".concat(x.disabled)]:{opacity:(t.vars||t).palette.action.disabledOpacity},["& + .".concat(v.Z.root)]:{marginTop:t.spacing(1),marginBottom:t.spacing(1)},["& + .".concat(v.Z.inset)]:{marginLeft:52},["& .".concat(g.Z.root)]:{marginTop:0,marginBottom:0},["& .".concat(g.Z.inset)]:{paddingLeft:36},["& .".concat(f.Z.root)]:{minWidth:36},variants:[{props:e=>{let{ownerState:t}=e;return!t.disableGutters},style:{paddingLeft:16,paddingRight:16}},{props:e=>{let{ownerState:t}=e;return t.divider},style:{borderBottom:"1px solid ".concat((t.vars||t).palette.divider),backgroundClip:"padding-box"}},{props:e=>{let{ownerState:t}=e;return!t.dense},style:{[t.breakpoints.up("sm")]:{minHeight:"auto"}}},{props:e=>{let{ownerState:t}=e;return t.dense},style:{minHeight:32,paddingTop:4,paddingBottom:4,...t.typography.body2,["& .".concat(f.Z.root," svg")]:{fontSize:"1.25rem"}}}]}}));var P=a.forwardRef(function(e,t){let r;let n=(0,d.i)({props:e,name:"MuiMenuItem"}),{autoFocus:l=!1,component:i="li",dense:s=!1,divider:c=!1,disableGutters:u=!1,focusVisibleClassName:v,role:f="menuitem",tabIndex:g,className:h,...y}=n,Z=a.useContext(p.Z),x=a.useMemo(()=>({dense:s||Z.dense||!1,disableGutters:u}),[Z.dense,s,u]),P=a.useRef(null);(0,m.Z)(()=>{l&&P.current&&P.current.focus()},[l]);let w={...n,dense:x.dense,divider:c,disableGutters:u},I=k(n),L=(0,b.Z)(P,t);return n.disabled||(r=void 0!==g?g:-1),(0,C.jsx)(p.Z.Provider,{value:x,children:(0,C.jsx)(M,{ref:L,role:f,tabIndex:r,component:i,focusVisibleClassName:(0,o.Z)(I.focusVisible,v),className:(0,o.Z)(I.root,h),...y,ownerState:w,classes:I})})})}}]);