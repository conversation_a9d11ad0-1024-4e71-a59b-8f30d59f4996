"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9932],{73143:function(e,t,r){let n,i,o,a,s,c,l;function d(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var i=0;for(n=Object.getOwnPropertySymbols(e);i<n.length;i++)0>t.indexOf(n[i])&&Object.prototype.propertyIsEnumerable.call(e,n[i])&&(r[n[i]]=e[n[i]])}return r}function h(e,t,r,n){return new(r||(r=Promise))(function(i,o){function a(e){try{c(n.next(e))}catch(e){o(e)}}function s(e){try{c(n.throw(e))}catch(e){o(e)}}function c(e){var t;e.done?i(e.value):((t=e.value)instanceof r?t:new r(function(e){e(t)})).then(a,s)}c((n=n.apply(e,t||[])).next())})}r.d(t,{fw:function(){return td},j2:function(){return tl}}),"function"==typeof SuppressedError&&SuppressedError;let u="2.0.1",g=["teams.microsoft.com","teams.microsoft.us","gov.teams.microsoft.us","dod.teams.microsoft.us","int.teams.microsoft.com","outlook.office.com","outlook-sdf.office.com","outlook.office365.com","outlook-sdf.office365.com","outlook.live.com","outlook-sdf.live.com","teams.live.com","local.teams.live.com","local.teams.live.com:8080","local.teams.office.com","local.teams.office.com:8080","devspaces.skype.com","*.www.office.com","www.office.com","word.office.com","excel.office.com","powerpoint.office.com","www.officeppe.com","*.www.microsoft365.com","www.microsoft365.com","bing.com","edgeservices.bing.com","work.bing.com","www.bing.com","www.staging-bing-int.com","*.cloud.microsoft","*.m365.cloud.microsoft","chatuxmanager.svc.cloud.microsoft","copilot.microsoft.com","windows.msn.com","fa000000125.resources.office.net","fa000000129.resources.office.net","fa000000124.resources.office.net","fa000000128.resources.office.net","fa000000136.resources.office.net"],p=new URL("https://res.cdn.office.net/teams-js/validDomains/json/validDomains.json"),m=/^https:\/\//,f="The library has not yet been initialized";var C,y,T,I,v,A,w,S,E,k,R={randomUUID:"undefined"!=typeof crypto&&crypto.randomUUID&&crypto.randomUUID.bind(crypto)};let _=new Uint8Array(16),b=[];for(let e=0;e<256;++e)b.push((e+256).toString(16).slice(1));var O=/^(?:[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}|00000000-0000-0000-0000-000000000000)$/i;function N(e,t){if("string"!=typeof e||"string"!=typeof t)return NaN;let r=e.split("."),n=t.split(".");function i(e){return/^\d+$/.test(e)}if(!r.every(i)||!n.every(i))return NaN;for(;r.length<n.length;)r.push("0");for(;n.length<r.length;)n.push("0");for(let e=0;e<r.length;++e)if(Number(r[e])!=Number(n[e]))return Number(r[e])>Number(n[e])?1:-1;return 0}function P(){return"undefined"==typeof window}let M=!!performance&&"now"in performance;function U(){return M?performance.now()+performance.timeOrigin:void 0}class q{constructor(e=function(e,t,r){if(R.randomUUID&&!e)return R.randomUUID();let i=(e=e||{}).random||(e.rng||function(){if(!n&&!(n="undefined"!=typeof crypto&&crypto.getRandomValues&&crypto.getRandomValues.bind(crypto)))throw Error("crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported");return n(_)})();return i[6]=15&i[6]|64,i[8]=63&i[8]|128,function(e,t=0){return b[e[t+0]]+b[e[t+1]]+b[e[t+2]]+b[e[t+3]]+"-"+b[e[t+4]]+b[e[t+5]]+"-"+b[e[t+6]]+b[e[t+7]]+"-"+b[e[t+8]]+b[e[t+9]]+"-"+b[e[t+10]]+b[e[t+11]]+b[e[t+12]]+b[e[t+13]]+b[e[t+14]]+b[e[t+15]]}(i)}()){this.uuid=e,function(e){if(!e)throw Error("id must not be empty");if(!1===("string"==typeof e&&O.test(e)))throw Error("id must be a valid UUID")}(e)}toString(){return this.uuid}serialize(){return this.toString()}}class H{}H.initializeCalled=!1,H.initializeCompleted=!1,H.additionalValidOrigins=[],H.initializePromise=void 0,H.isFramelessWindow=!1,H.frameContext=void 0,H.hostClientType=void 0,H.printCapabilityEnabled=!1,H.teamsJsInstanceId=(new q).toString();var L={exports:{}},D=function(e){function t(e){let n,i,o,a=null;function s(...e){if(!s.enabled)return;let r=Number(new Date),i=r-(n||r);s.diff=i,s.prev=n,s.curr=r,n=r,e[0]=t.coerce(e[0]),"string"!=typeof e[0]&&e.unshift("%O");let o=0;e[0]=e[0].replace(/%([a-zA-Z%])/g,(r,n)=>{if("%%"===r)return"%";o++;let i=t.formatters[n];if("function"==typeof i){let t=e[o];r=i.call(s,t),e.splice(o,1),o--}return r}),t.formatArgs.call(s,e),(s.log||t.log).apply(s,e)}return s.namespace=e,s.useColors=t.useColors(),s.color=t.selectColor(e),s.extend=r,s.destroy=t.destroy,Object.defineProperty(s,"enabled",{enumerable:!0,configurable:!1,get:()=>null!==a?a:(i!==t.namespaces&&(i=t.namespaces,o=t.enabled(e)),o),set:e=>{a=e}}),"function"==typeof t.init&&t.init(s),s}function r(e,r){let n=t(this.namespace+(void 0===r?":":r)+e);return n.log=this.log,n}function n(e){return e.toString().substring(2,e.toString().length-2).replace(/\.\*\?$/,"*")}return t.debug=t,t.default=t,t.coerce=function(e){return e instanceof Error?e.stack||e.message:e},t.disable=function(){let e=[...t.names.map(n),...t.skips.map(n).map(e=>"-"+e)].join(",");return t.enable(""),e},t.enable=function(e){let r;t.save(e),t.namespaces=e,t.names=[],t.skips=[];let n=("string"==typeof e?e:"").split(/[\s,]+/),i=n.length;for(r=0;r<i;r++)n[r]&&("-"===(e=n[r].replace(/\*/g,".*?"))[0]?t.skips.push(RegExp("^"+e.slice(1)+"$")):t.names.push(RegExp("^"+e+"$")))},t.enabled=function(e){let r,n;if("*"===e[e.length-1])return!0;for(r=0,n=t.skips.length;r<n;r++)if(t.skips[r].test(e))return!1;for(r=0,n=t.names.length;r<n;r++)if(t.names[r].test(e))return!0;return!1},t.humanize=function(){if(y)return C;function e(e,t,r,n){return Math.round(e/r)+" "+n+(t>=1.5*r?"s":"")}return y=1,C=function(t,r){r=r||{};var n,i,o=typeof t;if("string"===o&&t.length>0)return function(e){if(!((e=String(e)).length>100)){var t=/^(-?(?:\d+)?\.?\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)?$/i.exec(e);if(t){var r=parseFloat(t[1]);switch((t[2]||"ms").toLowerCase()){case"years":case"year":case"yrs":case"yr":case"y":return 315576e5*r;case"weeks":case"week":case"w":return 6048e5*r;case"days":case"day":case"d":return 864e5*r;case"hours":case"hour":case"hrs":case"hr":case"h":return 36e5*r;case"minutes":case"minute":case"mins":case"min":case"m":return 6e4*r;case"seconds":case"second":case"secs":case"sec":case"s":return 1e3*r;case"milliseconds":case"millisecond":case"msecs":case"msec":case"ms":return r;default:return}}}}(t);if("number"===o&&isFinite(t))return r.long?(n=Math.abs(t))>=864e5?e(t,n,864e5,"day"):n>=36e5?e(t,n,36e5,"hour"):n>=6e4?e(t,n,6e4,"minute"):n>=1e3?e(t,n,1e3,"second"):t+" ms":(i=Math.abs(t))>=864e5?Math.round(t/864e5)+"d":i>=36e5?Math.round(t/36e5)+"h":i>=6e4?Math.round(t/6e4)+"m":i>=1e3?Math.round(t/1e3)+"s":t+"ms";throw Error("val is not a non-empty string or a valid number. val="+JSON.stringify(t))}}(),t.destroy=function(){console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.")},Object.keys(e).forEach(r=>{t[r]=e[r]}),t.names=[],t.skips=[],t.formatters={},t.selectColor=function(e){let r=0;for(let t=0;t<e.length;t++)r=(r<<5)-r+e.charCodeAt(t)|0;return t.colors[Math.abs(r)%t.colors.length]},t.enable(t.load()),t},F=r(40257);!function(e,t){let r;t.formatArgs=function(t){if(t[0]=(this.useColors?"%c":"")+this.namespace+(this.useColors?" %c":" ")+t[0]+(this.useColors?"%c ":" ")+"+"+e.exports.humanize(this.diff),!this.useColors)return;let r="color: "+this.color;t.splice(1,0,r,"color: inherit");let n=0,i=0;t[0].replace(/%[a-zA-Z%]/g,e=>{"%%"!==e&&(n++,"%c"===e&&(i=n))}),t.splice(i,0,r)},t.save=function(e){try{e?t.storage.setItem("debug",e):t.storage.removeItem("debug")}catch(e){}},t.load=function(){let e;try{e=t.storage.getItem("debug")}catch(e){}return!e&&void 0!==F&&"env"in F&&(e=F.env.DEBUG),e},t.useColors=function(){return"undefined"!=typeof window&&!!window.process&&("renderer"===window.process.type||!!window.process.__nwjs)||!("undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/(edge|trident)\/(\d+)/))&&("undefined"!=typeof document&&document.documentElement&&document.documentElement.style&&document.documentElement.style.WebkitAppearance||"undefined"!=typeof window&&window.console&&(window.console.firebug||window.console.exception&&window.console.table)||"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/firefox\/(\d+)/)&&parseInt(RegExp.$1,10)>=31||"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/applewebkit\/(\d+)/))},t.storage=function(){try{return localStorage}catch(e){}}(),t.destroy=(r=!1,()=>{r||(r=!0,console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`."))}),t.colors=["#0000CC","#0000FF","#0033CC","#0033FF","#0066CC","#0066FF","#0099CC","#0099FF","#00CC00","#00CC33","#00CC66","#00CC99","#00CCCC","#00CCFF","#3300CC","#3300FF","#3333CC","#3333FF","#3366CC","#3366FF","#3399CC","#3399FF","#33CC00","#33CC33","#33CC66","#33CC99","#33CCCC","#33CCFF","#6600CC","#6600FF","#6633CC","#6633FF","#66CC00","#66CC33","#9900CC","#9900FF","#9933CC","#9933FF","#99CC00","#99CC33","#CC0000","#CC0033","#CC0066","#CC0099","#CC00CC","#CC00FF","#CC3300","#CC3333","#CC3366","#CC3399","#CC33CC","#CC33FF","#CC6600","#CC6633","#CC9900","#CC9933","#CCCC00","#CCCC33","#FF0000","#FF0033","#FF0066","#FF0099","#FF00CC","#FF00FF","#FF3300","#FF3333","#FF3366","#FF3399","#FF33CC","#FF33FF","#FF6600","#FF6633","#FF9900","#FF9933","#FFCC00","#FFCC33"],t.log=console.debug||console.log||(()=>{}),e.exports=D(t);let{formatters:n}=e.exports;n.j=function(e){try{return JSON.stringify(e)}catch(e){return"[UnexpectedJSONParseError]: "+e.message}}}(L,L.exports);var x=L.exports;let B=new q,K=x.debug.formatArgs;x.debug.formatArgs=function(e){e[0]=`(${(new Date).toISOString()}): ${e[0]} [${B.toString()}]`,K.call(this,e)};let G=x.debug("teamsJs");function z(e){return G.extend(e)}function $(e,t){return`${e}_${t}`}function W(e){return/^v\d+_[\w.]+$/.test(e)}var V=r(60866);let Q=z("runtime");function j(e){return 4===e.apiVersion}let Y={apiVersion:-1,supports:{}},J={apiVersion:4,isNAAChannelRecommended:!1,isDeeplyNestedAuthSupported:!1,hostVersionsInfo:V._m,isLegacyTeams:!0,supports:{appInstallDialog:{},appEntity:{},call:{},chat:{},conversations:{},dialog:{card:{bot:{}},url:{bot:{},parentCommunication:{}},update:{}},interactive:{},logs:{},meetingRoom:{},menus:{},monetization:{},notifications:{},pages:{config:{},backStack:{},fullTrust:{}},remoteCamera:{},teams:{fullTrust:{}},teamsCore:{},video:{sharedFrame:{}}}},X=[V.WH.desktop,V.WH.web,V.WH.rigel,V.WH.surfaceHub,V.WH.teamsRoomsWindows,V.WH.teamsRoomsAndroid,V.WH.teamsPhones,V.WH.teamsDisplays],Z=[...X,V.WH.android,V.WH.ios,V.WH.ipados,V.WH.visionOS],ee=[{versionToUpgradeFrom:1,upgradeToNextVersion:e=>{var t;return{apiVersion:2,hostVersionsInfo:void 0,isLegacyTeams:e.isLegacyTeams,supports:Object.assign(Object.assign({},e.supports),{dialog:e.supports.dialog?{card:void 0,url:e.supports.dialog,update:null===(t=e.supports.dialog)||void 0===t?void 0:t.update}:void 0})}}},{versionToUpgradeFrom:2,upgradeToNextVersion:e=>{let t=d(e.supports,["appNotification"]);return Object.assign(Object.assign({},e),{apiVersion:3,supports:t})}},{versionToUpgradeFrom:3,upgradeToNextVersion:e=>{var t,r,n,i,o;return{apiVersion:4,hostVersionsInfo:e.hostVersionsInfo,isNAAChannelRecommended:e.isNAAChannelRecommended,isLegacyTeams:e.isLegacyTeams,supports:Object.assign(Object.assign({},e.supports),{dialog:e.supports.dialog?{card:null===(t=e.supports.dialog)||void 0===t?void 0:t.card,url:{bot:null===(n=null===(r=e.supports.dialog)||void 0===r?void 0:r.url)||void 0===n?void 0:n.bot,parentCommunication:(null===(i=e.supports.dialog)||void 0===i?void 0:i.url)?{}:void 0},update:null===(o=e.supports.dialog)||void 0===o?void 0:o.update}:void 0})}}}],et={"1.0.0":[{capability:{pages:{appButton:{},tabs:{}},stageView:{}},hostClientTypes:X}],"1.9.0":[{capability:{location:{}},hostClientTypes:Z}],"2.0.0":[{capability:{people:{}},hostClientTypes:Z},{capability:{sharing:{}},hostClientTypes:[V.WH.desktop,V.WH.web]}],"2.0.1":[{capability:{teams:{fullTrust:{joinedTeams:{}}}},hostClientTypes:[V.WH.android,V.WH.desktop,V.WH.ios,V.WH.teamsRoomsAndroid,V.WH.teamsPhones,V.WH.teamsDisplays,V.WH.web]},{capability:{webStorage:{}},hostClientTypes:[V.WH.desktop]}],"2.0.5":[{capability:{webStorage:{}},hostClientTypes:[V.WH.android,V.WH.ios]}],"2.0.8":[{capability:{sharing:{}},hostClientTypes:[V.WH.android,V.WH.ios]}],"2.1.1":[{capability:{nestedAppAuth:{}},hostClientTypes:[V.WH.android,V.WH.ios,V.WH.ipados,V.WH.visionOS]}]},er=Q.extend("generateBackCompatRuntimeConfig"),en=Q.extend("applyRuntimeConfig");function ei(e){"string"==typeof e.apiVersion&&(en("Trying to apply runtime with string apiVersion, processing as v1: %o",e),e=Object.assign(Object.assign({},e),{apiVersion:1})),en("Fast-forwarding runtime %o",e);let t=function(e){let t=e;if(t.apiVersion<4&&ee.forEach(e=>{t.apiVersion===e.versionToUpgradeFrom&&(t=e.upgradeToNextVersion(t))}),j(t))return t;throw Error("Received a runtime that could not be upgraded to the latest version")}(e);en("Applying runtime %o",t),Y=function e(t){return Object.keys(t).forEach(r=>{null!==t[r]&&void 0!==t[r]&&"object"==typeof t[r]&&e(t[r])}),Object.freeze(t)}(t)}let eo="2.36.0",ea=e=>{let{uuid:t}=e,r=d(e,["uuid"]),n=null==t?void 0:t.toString();return Object.assign(Object.assign({},r),{uuidAsString:n})},es=e=>{let{uuidAsString:t}=e;return Object.assign(Object.assign({},d(e,["uuidAsString"])),{uuid:t?new q(t):void 0})},ec=e=>{let{uuid:t}=e,r=d(e,["uuid"]),n=null==t?void 0:t.toString();return Object.assign(Object.assign({},r),{uuidAsString:n})};function el(e){return void 0!==e.uuidAsString?`${e.uuidAsString} (legacy id: ${e.id})`:void 0!==e.uuid?`${e.uuid.toString()} (legacy id: ${e.id})`:`legacy id: ${e.id} (no uuid)`}let ed=z("flushMessageQueue");function eh(e,t,r,n){if(e&&t&&0!==r.length)for(;r.length>0;){let i=r.shift();if(i){let r=ea(i);ed("Flushing message %s from %s message queue via postMessage.",el(r),n),e.postMessage(r,t)}}}let eu=z("internal"),eg=eu.extend("ensureInitializeCalled"),ep=eu.extend("ensureInitialized");function em(e,...t){if(!H.initializeCompleted)throw ep("%s. initializeCalled: %s",f,H.initializeCalled.toString()),Error(f);if(t&&t.length>0){let e=!1;for(let r=0;r<t.length;r++)if(t[r]===H.frameContext){e=!0;break}if(!e)throw Error(`This call is only allowed in following contexts: ${JSON.stringify(t)}. Current context: "${H.frameContext}".`)}return function(e){if(j(e))return!0;throw -1===e.apiVersion?Error("The runtime has not yet been initialized"):Error("The runtime version is not supported")}(e)}function ef(){var e;i&&i()||(function(){return!1}()?eb("backButtonPress",[]):(e=$("v2","pages.backStack.navigateBack"),new Promise(t=>{if(em(Y),!(!(!em(Y)||!Y.supports.pages)&&Y.supports.pages.backStack))throw V._A;t(function(e,t,r,...n){return eB(e,t,n).then(([e,t])=>{if(!e)throw Error(t||r)})}(e,"navigateBack","Back navigation is not supported in the current client or context."))})))}let eC=z("handlers");class ey{static initializeHandlers(){ey.handlers.themeChange=ev,ey.handlers.load=ew,ey.handlers.beforeUnload=eS,eI($("v2","pages.backStack.registerBackButtonPressHandler"),"backButtonPress",ef,!1)}static uninitializeHandlers(){ey.handlers={},ey.themeChangeHandler=null,ey.loadHandler=null,ey.beforeUnloadHandler=null,ey.beforeSuspendOrTerminateHandler=null,ey.resumeHandler=null}}ey.handlers={},ey.themeChangeHandler=null,ey.loadHandler=null,ey.beforeUnloadHandler=null,ey.beforeSuspendOrTerminateHandler=null,ey.resumeHandler=null,ey.hostToAppPerformanceMetricsHandler=null;let eT=eC.extend("callHandler");function eI(e,t,r,n=!0,i=[]){r?(ey.handlers[t]=r,n&&eK(e,"registerHandler",[t,...i])):delete ey.handlers[t]}function ev(e){ey.themeChangeHandler&&ey.themeChangeHandler(e),function(){return!1}()&&eb("themeChange",[e])}function eA(e){ey.hostToAppPerformanceMetricsHandler&&ey.hostToAppPerformanceMetricsHandler(e)}function ew(e){let t={entityId:e.entityId,contentUrl:new URL(e.contentUrl)};ey.resumeHandler?(ey.resumeHandler(t),function(){return!1}()&&eb("load",[t])):ey.loadHandler&&(ey.loadHandler(e),function(){return!1}()&&eb("load",[e]))}function eS(){return h(this,void 0,void 0,function*(){let e=()=>{eK($("v2","handleBeforeUnload"),"readyToUnload",[])};ey.beforeSuspendOrTerminateHandler?(yield ey.beforeSuspendOrTerminateHandler(),function(){return!1}()?eb("beforeUnload"):e()):ey.beforeUnloadHandler&&ey.beforeUnloadHandler(e)||(function(){return!1}()?eb("beforeUnload"):e())})}let eE=z("childProxyingCommunication");class ek{}function eR(){return!1}ek.messageQueue=[];let e_=eE.extend("handleIncomingMessageFromChild");function eb(e,t){let r=ek.window,n={func:e,args:t||[]},i=ek.origin;r&&i?r.postMessage(n,i):ek.messageQueue.push(n)}class eO{static storeCallbackInformation(e,t){eO.callbackInformation.set(e,t)}static clearMessages(){eO.callbackInformation.clear()}static deleteMessageInformation(e){eO.callbackInformation.delete(e)}static handleOneWayPerformanceMetrics(e,t,r){let n=e.monotonicTimestamp;n&&r?eA({actionName:e.func,messageDelay:r-n,requestStartedAt:n}):t("Unable to send performance metrics for event %s",e.func)}static handlePerformanceMetrics(e,t,r,n){let i=eO.callbackInformation.get(e);i&&t.monotonicTimestamp&&n?(eA({actionName:i.name,messageDelay:n-t.monotonicTimestamp,requestStartedAt:i.calledAt}),eO.deleteMessageInformation(e)):r("Unable to send performance metrics for callback %s with arguments %o",e.toString(),t.args)}}eO.callbackInformation=new Map;let eN=z("nestedAppAuthUtils"),eP=eN.extend("tryPolyfillWithNestedAppAuthBridge"),eM=eN.extend("createNestedAppAuthBridge"),eU=[],eq=z("validateOrigin");function eH(e=!1){return h(this,void 0,void 0,function*(){if(0!==eU.length&&!e)return eU;if(P())return eU=g,g;{eq("Initiating fetch call to acquire valid origins list from CDN");let e=new AbortController,t=setTimeout(()=>e.abort(),1500);return fetch(p,{signal:e.signal}).then(e=>{if(clearTimeout(t),!e.ok)throw Error("Invalid Response from Fetch Call");return eq("Fetch call completed and retrieved valid origins list from CDN"),e.json().then(e=>{if(function(e){let t=JSON.parse(e);try{t=JSON.parse(e)}catch(e){return!1}if(!t.validOrigins)return!1;for(let e of t.validOrigins)try{new URL("https://"+e)}catch(t){return eq("isValidOriginsFromCDN call failed to validate origin: %s",e),!1}return!0}(JSON.stringify(e)))return eU=e.validOrigins;throw Error("Valid origins list retrieved from CDN is invalid")})}).catch(e=>("AbortError"===e.name?eq("validOrigins fetch call to CDN failed due to Timeout of 1500 ms. Defaulting to fallback list"):eq("validOrigins fetch call to CDN failed with error: %s. Defaulting to fallback list",e),eU=g))}})}function eL(e,t){if("*."===e.substring(0,2)){let r=e.substring(1);if(t.length>r.length&&t.split(".").length===r.split(".").length&&t.substring(t.length-r.length)===r)return!0}else if(e===t)return!0;return!1}!function(){h(this,void 0,void 0,function*(){yield eH()})}();let eD=z("communication");class eF{}class ex{}function eB(e,t,r){if(!W(e))throw Error(`apiVersionTag: ${e} passed in doesn't follow the pattern starting with 'v' followed by digits, then underscore with words, please check.`);return new Promise(n=>{var i;n((i=eQ(e,t,r).uuid,new Promise(e=>{ex.promiseCallbacks.set(i,e)})))})}function eK(e,t,r,n){let i;if(r instanceof Function?n=r:r instanceof Array&&(i=r),!W(e))throw Error(`apiVersionTag: ${e} passed in doesn't follow the pattern starting with 'v' followed by digits, then underscore with words, please check.`);let o=eQ(e,t,i);n&&ex.callbacks.set(o.uuid,n)}ex.parentMessageQueue=[],ex.topMessageQueue=[],ex.nextMessageId=0,ex.callbacks=new Map,ex.promiseCallbacks=new Map,ex.portCallbacks=new Map,ex.legacyMessageIdsToUuidMap={};let eG=eD.extend("sendNestedAuthRequestToTopWindow");function ez(e,t){let r=eF.topWindow,n=function(e,t){let r=ex.nextMessageId++,n=new q;return ex.legacyMessageIdsToUuidMap[r]=n,{id:r,uuid:n,func:"nestedAppAuth.execute",timestamp:Date.now(),monotonicTimestamp:U(),apiVersionTag:t,args:[],data:e}}(e,t);return eG("Message %s information: %o",el(n),{actionName:n.func}),eW(r,n)}let e$=eD.extend("sendRequestToTargetWindowHelper");function eW(e,t){let r=e===eF.topWindow&&e6()?"top":e===eF.parentWindow?"parent":null,n=ea(t);if(H.isFramelessWindow)eF.currentWindow&&eF.currentWindow.nativeInterface&&(e$("Sending message %s to %s via framelessPostMessage interface",el(n),r),eF.currentWindow.nativeInterface.framelessPostMessage(JSON.stringify(n)));else{let i=e===eF.topWindow&&e6()?eF.topOrigin:e===eF.parentWindow?eF.parentOrigin:null;e&&i?(e$("Sending message %s to %s via postMessage",el(n),r),e.postMessage(n,i)):(e$("Adding message %s to %s message queue",el(n),r),(e===eF.topWindow&&e6()?ex.topMessageQueue:e===eF.parentWindow?ex.parentMessageQueue:[]).push(t))}return t}let eV=eD.extend("sendMessageToParentHelper");function eQ(e,t,r,n,i){let o=eF.parentWindow,a=function(e,t,r,n,i){let o=ex.nextMessageId++,a=new q;ex.legacyMessageIdsToUuidMap[o]=a;let s=!0===n?i:H.teamsJsInstanceId;return{id:o,uuid:a,func:t,timestamp:Date.now(),monotonicTimestamp:U(),args:r||[],apiVersionTag:e,isProxiedFromChild:null!=n&&n,teamsJsInstanceId:s}}(e,t,r,n,i);return eO.storeCallbackInformation(a.uuid,{name:t,calledAt:a.timestamp}),eV("Message %s information: %o",el(a),{actionName:t,args:r}),eW(o,a)}let ej=eD.extend("processIncomingMessage"),eY=eD.extend("processAuthBridgeMessage");function eJ(e,t){var r,n;if(!e||!e.data||"object"!=typeof e.data)return void eY("Unrecognized message format received by app, message being ignored. Message: %o",e);let{args:i}=e.data,[,o]=null!=i?i:[],a=(()=>{try{return JSON.parse(o)}catch(e){return null}})();if(!a||"object"!=typeof a||"NestedAppAuthResponse"!==a.messageType)return void eY("Unrecognized data format received by app, message being ignored. Message: %o",e);let s=e.source||(null===(r=null==e?void 0:e.originalEvent)||void 0===r?void 0:r.source),c=e.origin||(null===(n=null==e?void 0:e.originalEvent)||void 0===n?void 0:n.origin);s?eZ(s,c)?(eF.topWindow&&!eF.topWindow.closed&&s!==eF.topWindow||(eF.topWindow=s,eF.topOrigin=c),eF.topWindow&&eF.topWindow.closed&&(eF.topWindow=null,eF.topOrigin=null),eh(eF.topWindow,eF.topOrigin,ex.topMessageQueue,"top"),t(o)):eY("Message being ignored by app because it is either coming from the current window or a different window with an invalid origin"):eY("Message being ignored by app because it is coming for a target that is null")}let eX=eD.extend("shouldProcessIncomingMessage");function eZ(e,t){return h(this,void 0,void 0,function*(){if(eF.currentWindow&&e===eF.currentWindow)return eX("Should not process message because it is coming from the current window"),!1;if(eF.currentWindow&&eF.currentWindow.location&&t&&t===eF.currentWindow.location.origin)return!0;{var r;let e;try{e=new URL(t)}catch(e){return eX("Message has an invalid origin of %s",t),!1}let n=yield(r=e,eH(void 0).then(e=>{if("https:"!==r.protocol)return eq("Origin %s is invalid because it is not using https protocol. Protocol being used: %s",r,r.protocol),!1;let t=r.host;if(e.some(e=>eL(e,t)))return!0;for(let e of H.additionalValidOrigins)if(eL("https://"===e.substring(0,8)?e.substring(8):e,t))return!0;return eq("Origin %s is invalid because it is not an origin approved by this library or included in the call to app.initialize.\nOrigins approved by this library: %o\nOrigins included in app.initialize: %o",r,e,H.additionalValidOrigins),!1}));return n||eX("Message has an invalid origin of %s",t),n}})}let e0=eD.extend("handleIncomingMessageFromParent");function e1(e,t){if(t){let r=[...e].find(([e,r])=>e.toString()===t.toString());if(r)return r[0]}}function e3(e,t){let r=e1(t,e.uuid);r&&t.delete(r),e.uuid?ex.legacyMessageIdsToUuidMap={}:delete ex.legacyMessageIdsToUuidMap[e.id]}function e2(e){let t=U();if("id"in e.data&&"number"==typeof e.data.id){let r=es(e.data),n=function(e){if(!e.uuid)return ex.legacyMessageIdsToUuidMap[e.id];{let t=e.uuid,r=e1(ex.callbacks,t);if(r)return r;let n=e1(ex.promiseCallbacks,t);if(n)return n;let i=e1(ex.portCallbacks,t);if(i)return i}e0("Received message %s that failed to produce a callbackId",el(e))}(r);if(n){let i=ex.callbacks.get(n);e0("Received a response from parent for message %s",n.toString()),eO.handlePerformanceMetrics(n,r,e0,t),i&&(e0("Invoking the registered callback for message %s with arguments %o",n.toString(),r.args),i.apply(null,[...r.args,r.isPartialResponse]),!0===e.data.isPartialResponse||(e0("Removing registered callback for message %s",n.toString()),e3(r,ex.callbacks)));let o=ex.promiseCallbacks.get(n);o&&(e0("Invoking the registered promise callback for message %s with arguments %o",n.toString(),r.args),o(r.args),e0("Removing registered promise callback for message %s",n.toString()),e3(r,ex.promiseCallbacks));let a=ex.portCallbacks.get(n);if(a){let t;e0("Invoking the registered port callback for message %s with arguments %o",n.toString(),r.args),e.ports&&e.ports[0]instanceof MessagePort&&(t=e.ports[0]),a(t,r.args),e0("Removing registered port callback for message %s",n.toString()),e3(r,ex.portCallbacks)}r.uuid&&(ex.legacyMessageIdsToUuidMap={})}}else if("func"in e.data&&"string"==typeof e.data.func){let r=e.data;eO.handleOneWayPerformanceMetrics(r,e0,t),e0('Received a message from parent %s, action: "%s"',el(r),r.func),function(e,t){let r=ey.handlers[e];r?(eT("Invoking the registered handler for message %s with arguments %o",e,t),[!0,r.apply(this,t)]):(function(){return!1}()?eb(e,t):eT("Handler for action message %s not found.",e),[!1,void 0])}(r.func,r.args)}else e0("Received an unknown message: %O",e)}function e6(){return eF.topWindow!==eF.parentWindow}let e4=[];function e5(e){H.frameContext&&(H.frameContext===V.$c.task?e4.push(e):delete ey.handlers.messageForChild)}function e9(e){s&&s(e)||(em(Y),eK($("v2","menus.handleViewConfigItemPress"),"viewConfigItemPress",[e]))}function e8(e){o&&o(e)||(em(Y),eK($("v2","menus.handleNavBarMenuItemPress"),"handleNavBarMenuItemPress",[e]))}function e7(e){a&&a(e)||(em(Y),eK($("v2","menus.handleActionMenuItemPress"),"handleActionMenuItemPress",[e]))}function te(e){let t=new tt(e);c?c(t):t.notifySuccess()}(w=T||(T={}))[w.ifRoom=0]="ifRoom",w[w.overflowOnly=1]="overflowOnly",(S=I||(I={})).dropDown="dropDown",S.popOver="popOver";class tt{constructor(e){this.notified=!1,this.result=e||{}}notifySuccess(){this.ensureNotNotified(),eK($("v2","pages.saveEvent.notifySuccess"),"settings.save.success"),this.notified=!0}notifyFailure(e){this.ensureNotNotified(),eK($("v2","pages.saveEvent.notifyFailure"),"settings.save.failure",[e]),this.notified=!0}ensureNotNotified(){if(this.notified)throw Error("The SaveEvent may only notify success or failure once.")}}function tr(){let e=new tn;l?l(e):e.notifySuccess()}class tn{constructor(){this.notified=!1}notifySuccess(){this.ensureNotNotified(),eK($("v2","pages.removeEvent.notifySuccess"),"settings.remove.success"),this.notified=!0}notifyFailure(e){this.ensureNotNotified(),eK($("v2","pages.removeEvent.notifyFailure"),"settings.remove.failure",[e]),this.notified=!0}ensureNotNotified(){if(this.notified)throw Error("The removeEventType may only notify success or failure once.")}}let ti=z("app"),to=ti.extend("initializeHelper");class ta{constructor(e){this.idAsString=e,function(e){if(RegExp(`${/<script[^>]*>|&lt;script[^&]*&gt;|%3Cscript[^%]*%3E/gi.source}|${/<\/script[^>]*>|&lt;\/script[^&]*&gt;|%3C\/script[^%]*%3E/gi.source}`,"gi").test(e))throw Error(`Potential app id (${e}) is invalid; it contains script tags.`);if([...e].some(e=>{let t=e.charCodeAt(0);return t<32||t>126}))throw Error(`Potential app id (${e}) is invalid; it contains non-printable characters.`)}(e)}serialize(){return this.toString()}toString(){return this.idAsString}}class ts extends ta{constructor(e){super(e),function(e){if(!(e.length<256&&e.length>4))throw Error(`Potential app id (${e}) is invalid; its length ${e.length} is not within the length limits (4-256).`)}(e)}toJSON(){return{appIdAsString:this.toString()}}}let tc=z("app");function tl(e){var t,r,n;return t=$("v2","app.initialize"),P()?(ti.extend("initialize")("window object undefined at initialization"),Promise.resolve()):(r=()=>new Promise(r=>{H.initializeCalled||(H.initializeCalled=!0,ey.initializeHandlers(),H.initializePromise=(function(e,t){if(ex.messageListener=e=>(function(e){return h(this,void 0,void 0,function*(){if(!e||!e.data||"object"!=typeof e.data)return void ej("Unrecognized message format received by app, message being ignored. Message: %o",e);let t=e.source||e.originalEvent&&e.originalEvent.source,r=e.origin||e.originalEvent&&e.originalEvent.origin;return eZ(t,r).then(n=>{n?(H.isFramelessWindow||eF.parentWindow&&!eF.parentWindow.closed&&t!==eF.parentWindow||(eF.parentWindow=t,eF.parentOrigin=r),eF.parentWindow&&eF.parentWindow.closed&&(eF.parentWindow=null,eF.parentOrigin=null),eh(eF.parentWindow,eF.parentOrigin,ex.parentMessageQueue,"parent"),t!==eF.parentWindow||e2(e)):ej("Message being ignored by app because it is either coming from the current window or a different window with an invalid origin, message: %o, source: %o, origin: %o",e,t,r)})})})(e),eF.currentWindow=eF.currentWindow||function(){if(P())throw Error("window object undefined at SSR check");return window}(),eF.parentWindow=eF.currentWindow.parent!==eF.currentWindow.self?eF.currentWindow.parent:eF.currentWindow.opener,eF.topWindow=eF.currentWindow.top,(eF.parentWindow||e)&&eF.currentWindow.addEventListener("message",ex.messageListener,!1),!eF.parentWindow){let e=eF.currentWindow;if(!e.nativeInterface)return Promise.reject(Error("Initialization Failed. No Parent window found."));H.isFramelessWindow=!0,e.onNativeMessage=e2}try{return eF.parentOrigin="*",eB(t,"initialize",[eo,4,e]).then(([e,t,r,n])=>((function(e,t,r){var n;if(H.isFramelessWindow)return void eP("Cannot polyfill nestedAppAuthBridge as current window is frameless");if(!t)return void eP("Cannot polyfill nestedAppAuthBridge as current window does not exist");if(t.parent!==t.top)return void eP("Default NAA bridge injection not supported in nested iframe. Use standalone NAA bridge instead.");let i=(()=>{try{return JSON.parse(e)}catch(e){return null}})();if(!i||!(null===(n=i.supports)||void 0===n?void 0:n.nestedAppAuth))return void eP("Cannot polyfill nestedAppAuthBridge as current hub does not support nested app auth");if(t.nestedAppAuthBridge)return void eP("nestedAppAuthBridge already exists on current window, skipping polyfill");let o=function(e,t){if(!e)return eM("nestedAppAuthBridge cannot be created as current window does not exist"),null;let{onMessage:r,sendPostMessage:n}=t,i=e=>t=>r(t,e);return{addEventListener:(t,r)=>{"message"===t?e.addEventListener(t,i(r)):eM(`Event ${t} is not supported by nestedAppAuthBridge`)},postMessage:e=>{let t=(()=>{try{return JSON.parse(e)}catch(e){return null}})();if(!t||"object"!=typeof t||"NestedAppAuthRequest"!==t.messageType)return void eM("Unrecognized data format received by app, message being ignored. Message: %o",e);n(e,$("v2","nestedAppAuth.execute"))},removeEventListener:(t,r)=>{e.removeEventListener(t,i(r))}}}(t,r);o&&(t.nestedAppAuthBridge=o)})(n,eF.currentWindow,{onMessage:eJ,sendPostMessage:ez}),{context:e,clientType:t,runtimeConfig:r,clientSupportedSDKVersion:n}))}finally{eF.parentOrigin=null}})(e,t).then(({context:e,clientType:t,runtimeConfig:r,clientSupportedSDKVersion:n=u})=>{H.frameContext=e,H.hostClientType=t,H.clientSupportedSDKVersion=n;try{to("Parsing %s",r);let e=JSON.parse(r);if(to("Checking if %o is a valid runtime object",null!=e?e:"null"),!e||!e.apiVersion)throw Error("Received runtime config is invalid");r&&ei(e)}catch(e){if(!(e instanceof SyntaxError))throw e;try{to("Attempting to parse %s as an SDK version",r),isNaN(N(r,u))||(H.clientSupportedSDKVersion=r);let e=JSON.parse(n);if(to("givenRuntimeConfig parsed to %o",null!=e?e:"null"),!e)throw Error("givenRuntimeConfig string was successfully parsed. However, it parsed to value of null");ei(e)}catch(e){if(!(e instanceof SyntaxError))throw e;ei(function(e,t,r){er("generating back compat runtime config for %s",e);let n=Object.assign({},t.supports);er("Supported capabilities in config before updating based on highestSupportedVersion: %o",n),Object.keys(r).forEach(t=>{N(e,t)>=0&&r[t].forEach(e=>{void 0!==H.hostClientType&&e.hostClientTypes.includes(H.hostClientType)&&(n=function e(t,r){let n=Object.assign({},t);for(let i in r)Object.prototype.hasOwnProperty.call(r,i)&&("object"!=typeof r[i]||Array.isArray(r[i])?i in t||(n[i]=r[i]):n[i]=e(t[i]||{},r[i]));return n}(n,e.capability))})});let i={apiVersion:4,hostVersionsInfo:V._m,isLegacyTeams:!0,supports:n};return er("Runtime config after updating based on highestSupportedVersion: %o",i),i}(H.clientSupportedSDKVersion,J,et))}}H.initializeCompleted=!0}),eI($("v2","menus.registerNavBarMenuItemPressHandler"),"navBarMenuItemPress",e8,!1),eI($("v2","menus.registerActionMenuItemPressHandler"),"actionMenuItemPress",e7,!1),eI($("v2","menus.registerSetModuleViewHandler"),"setModuleView",e9,!1),eI($("v2","pages.config.registerSettingsSaveHandler"),"settings.save",te,!1),eI($("v2","pages.config.registerSettingsRemoveHandler"),"settings.remove",tr,!1),eI($("v2","dialog.registerMessageForChildHandler"),"messageForChild",e5,!1)),Array.isArray(e)&&function(e){let t=H.additionalValidOrigins.concat(e.filter(e=>"string"==typeof e&&m.test(e))),r={};t=t.filter(e=>!r[e]&&(r[e]=!0,!0)),H.additionalValidOrigins=t}(e),void 0!==H.initializePromise?r(H.initializePromise):to("GlobalVars.initializePromise is unexpectedly undefined")}),n=Error("SDK initialization timed out."),new Promise((e,t)=>{let i=setTimeout(t,6e4,n);r().then(t=>{clearTimeout(i),e(t)}).catch(e=>{clearTimeout(i),t(e)})}))}function td(){return new Promise(e=>{(function(){if(!H.initializeCalled)throw eg(f),Error(f)})(),e(function(e,t,...r){return eB(e,t,r).then(([e])=>e)}($("v2","app.getContext"),"getContext"))}).then(e=>{var t;return{actionInfo:e.actionInfo,app:{locale:e.locale,sessionId:e.appSessionId?e.appSessionId:"",theme:e.theme?e.theme:"default",iconPositionVertical:e.appIconPosition,osLocaleInfo:e.osLocaleInfo,parentMessageId:e.parentMessageId,userClickTime:e.userClickTime,userClickTimeV2:e.userClickTimeV2,userFileOpenPreference:e.userFileOpenPreference,host:{name:e.hostName?e.hostName:V.UB.teams,clientType:e.hostClientType?e.hostClientType:V.WH.web,sessionId:e.sessionId?e.sessionId:"",ringId:e.ringId},appLaunchId:e.appLaunchId,appId:e.appId?new ts(e.appId):void 0,manifestVersion:e.manifestVersion},page:{id:e.entityId,frameContext:e.frameContext?e.frameContext:H.frameContext,subPageId:e.subEntityId,isFullScreen:e.isFullScreen,isMultiWindow:e.isMultiWindow,isBackgroundLoad:e.isBackgroundLoad,sourceOrigin:e.sourceOrigin},user:{id:null!==(t=e.userObjectId)&&void 0!==t?t:"",displayName:e.userDisplayName,isCallingAllowed:e.isCallingAllowed,isPSTNCallingAllowed:e.isPSTNCallingAllowed,licenseType:e.userLicenseType,loginHint:e.loginHint,userPrincipalName:e.userPrincipalName,tenant:e.tid?{id:e.tid,teamsSku:e.tenantSKU}:void 0},channel:e.channelId?{id:e.channelId,displayName:e.channelName,relativeUrl:e.channelRelativeUrl,membershipType:e.channelType,defaultOneNoteSectionId:e.defaultOneNoteSectionId,ownerGroupId:e.hostTeamGroupId,ownerTenantId:e.hostTeamTenantId}:void 0,chat:e.chatId?{id:e.chatId}:void 0,meeting:e.meetingId?{id:e.meetingId}:void 0,sharepoint:e.sharepoint,team:e.teamId?{internalId:e.teamId,displayName:e.teamName,type:e.teamType,groupId:e.groupId,templateId:e.teamTemplateId,isArchived:e.isTeamArchived,userRole:e.userTeamRole}:void 0,sharePointSite:e.teamSiteUrl||e.teamSiteDomain||e.teamSitePath||e.mySitePath||e.mySiteDomain?{teamSiteUrl:e.teamSiteUrl,teamSiteDomain:e.teamSiteDomain,teamSitePath:e.teamSitePath,teamSiteId:e.teamSiteId,mySitePath:e.mySitePath,mySiteDomain:e.mySiteDomain}:void 0,dialogParameters:e.dialogParameters||{}}})}(E=v||(v={})).AuthFailed="AuthFailed",E.Timeout="Timeout",E.Other="Other",(k=A||(A={})).PermissionError="PermissionError",k.NotFound="NotFound",k.Throttling="Throttling",k.Offline="Offline",k.Other="Other",tc("teamsjs instance is version %s, starting at %s UTC (%s local)",eo,(new Date).toISOString(),(new Date).toLocaleString()),function(){if(P())return;let e=document.getElementsByTagName("script"),t=e&&e[e.length-1]&&e[e.length-1].src,r="Today, teamsjs can only be used from a single script or you may see undefined behavior. This log line is used to help detect cases where teamsjs is loaded multiple times -- it is always written. The presence of the log itself does not indicate a multi-load situation, but multiples of these log lines will. If you would like to use teamjs from more than one script at the same time, please open an issue at https://github.com/OfficeDev/microsoft-teams-library-js/issues";t&&0!==t.length?tc("teamsjs is being used from %s. %s",t,r):tc("teamsjs is being used from a script tag embedded directly in your html. %s",r)}()},60866:function(e,t,r){var n,i,o,a,s,c,l,d,h,u,g,p,m,f,C,y,T,I,v,A,w,S,E,k,R,_,b,O,N,P,M,U,q;r.d(t,{$c:function(){return m},WH:function(){return g},UB:function(){return p},_A:function(){return H},_m:function(){return L}}),(I=n||(n={})).Inline="inline",I.Desktop="desktop",I.Web="web",(i||(i={})).M365Content="m365content",(v=o||(o={})).DriveId="driveId",v.GroupId="groupId",v.SiteId="siteId",v.UserId="userId",(A=a||(a={}))[A.NOT_SUPPORTED_ON_PLATFORM=100]="NOT_SUPPORTED_ON_PLATFORM",A[A.INTERNAL_ERROR=500]="INTERNAL_ERROR",A[A.NOT_SUPPORTED_IN_CURRENT_CONTEXT=501]="NOT_SUPPORTED_IN_CURRENT_CONTEXT",A[A.PERMISSION_DENIED=1e3]="PERMISSION_DENIED",A[A.NETWORK_ERROR=2e3]="NETWORK_ERROR",A[A.NO_HW_SUPPORT=3e3]="NO_HW_SUPPORT",A[A.INVALID_ARGUMENTS=4e3]="INVALID_ARGUMENTS",A[A.UNAUTHORIZED_USER_OPERATION=5e3]="UNAUTHORIZED_USER_OPERATION",A[A.INSUFFICIENT_RESOURCES=6e3]="INSUFFICIENT_RESOURCES",A[A.THROTTLE=7e3]="THROTTLE",A[A.USER_ABORT=8e3]="USER_ABORT",A[A.OPERATION_TIMED_OUT=8001]="OPERATION_TIMED_OUT",A[A.OLD_PLATFORM=9e3]="OLD_PLATFORM",A[A.FILE_NOT_FOUND=404]="FILE_NOT_FOUND",A[A.SIZE_EXCEEDED=1e4]="SIZE_EXCEEDED",(w=s||(s={})).GeoLocation="geolocation",w.Media="media",(S=c||(c={})).BCAIS="bcais",S.BCWAF="bcwaf",S.BCWBF="bcwbf",(E=l||(l={})).Faculty="faculty",E.Student="student",E.Other="other",(k=d||(d={})).Adult="adult",k.MinorNoParentalConsentRequired="minorNoParentalConsentRequired",k.MinorWithoutParentalConsent="minorWithoutParentalConsent",k.MinorWithParentalConsent="minorWithParentalConsent",k.NonAdult="nonAdult",(R=h||(h={})).HigherEducation="higherEducation",R.K12="k12",R.Other="other",(_=u||(u={})).TextPlain="text/plain",_.TextHtml="text/html",_.ImagePNG="image/png",_.ImageJPEG="image/jpeg",(b=g||(g={})).desktop="desktop",b.web="web",b.android="android",b.ios="ios",b.ipados="ipados",b.macos="macos",b.visionOS="visionOS",b.rigel="rigel",b.surfaceHub="surfaceHub",b.teamsRoomsWindows="teamsRoomsWindows",b.teamsRoomsAndroid="teamsRoomsAndroid",b.teamsPhones="teamsPhones",b.teamsDisplays="teamsDisplays",(O=p||(p={})).office="Office",O.outlook="Outlook",O.outlookWin32="OutlookWin32",O.orange="Orange",O.places="Places",O.teams="Teams",O.teamsModern="TeamsModern",(N=m||(m={})).settings="settings",N.content="content",N.authentication="authentication",N.remove="remove",N.task="task",N.sidePanel="sidePanel",N.stage="stage",N.meetingStage="meetingStage",(P=f||(f={}))[P.Standard=0]="Standard",P[P.Edu=1]="Edu",P[P.Class=2]="Class",P[P.Plc=3]="Plc",P[P.Staff=4]="Staff",(M=C||(C={}))[M.Admin=0]="Admin",M[M.User=1]="User",M[M.Guest=2]="Guest",(U=y||(y={})).Large="large",U.Medium="medium",U.Small="small",(q=T||(T={})).Regular="Regular",q.Private="Private",q.Shared="Shared";let H={errorCode:a.NOT_SUPPORTED_ON_PLATFORM},L={adaptiveCardSchemaVersion:{majorVersion:1,minorVersion:5}};Error("Invalid input count: Must supply a valid image count (limit of 10)."),Error("Invalid response: Received more images than the specified max limit in the response.")},84392:function(e,t,r){r.d(t,{Lx:function(){return iq}});var n,i,o=r(71986),a=r(7289);let s={AAD:"AAD",OIDC:"OIDC"},c={None:"none"},l="unexpected_error",d="post_request_failed",h={[l]:"Unexpected error in authentication.",[d]:"Post request failed from the network, could be a 4xx/5xx or a network unavailability. Please check the exact error code for details."};h[l],h[d];class u extends Error{constructor(e,t,r){super(t?`${e}: ${t}`:e),Object.setPrototypeOf(this,u.prototype),this.errorCode=e||a.gT.EMPTY_STRING,this.errorMessage=t||a.gT.EMPTY_STRING,this.subError=r||a.gT.EMPTY_STRING,this.name="AuthError"}setCorrelationId(e){this.correlationId=e}}function g(e,t){return new u(e,t?`${h[e]} ${t}`:h[e])}let p="client_info_decoding_error",m="client_info_empty_error",f="token_parsing_error",C="null_or_empty_token",y="endpoints_resolution_error",T="network_error",I="openid_config_error",v="hash_not_deserialized",A="invalid_state",w="state_mismatch",S="state_not_found",E="nonce_mismatch",k="auth_time_not_found",R="max_age_transpired",_="multiple_matching_tokens",b="multiple_matching_accounts",O="multiple_matching_appMetadata",N="request_cannot_be_made",P="cannot_remove_empty_scope",M="cannot_append_scopeset",U="empty_input_scopeset",q="device_code_polling_cancelled",H="device_code_expired",L="device_code_unknown_error",D="no_account_in_silent_request",F="invalid_cache_record",x="invalid_cache_environment",B="no_account_found",K="no_crypto_object",G="unexpected_credential_type",z="invalid_assertion",$="invalid_client_credential",W="token_refresh_required",V="user_timeout_reached",Q="token_claims_cnf_required_for_signedjwt",j="authorization_code_missing_from_server_response",Y="binding_key_not_removed",J="end_session_endpoint_not_supported",X="key_id_missing",Z="no_network_connectivity",ee="user_canceled",et="missing_tenant_id_error",er="method_not_implemented",en="nested_app_auth_bridge_disabled",ei={[p]:"The client info could not be parsed/decoded correctly",[m]:"The client info was empty",[f]:"Token cannot be parsed",[C]:"The token is null or empty",[y]:"Endpoints cannot be resolved",[T]:"Network request failed",[I]:"Could not retrieve endpoints. Check your authority and verify the .well-known/openid-configuration endpoint returns the required endpoints.",[v]:"The hash parameters could not be deserialized",[A]:"State was not the expected format",[w]:"State mismatch error",[S]:"State not found",[E]:"Nonce mismatch error",[k]:"Max Age was requested and the ID token is missing the auth_time variable. auth_time is an optional claim and is not enabled by default - it must be enabled. See https://aka.ms/msaljs/optional-claims for more information.",[R]:"Max Age is set to 0, or too much time has elapsed since the last end-user authentication.",[_]:"The cache contains multiple tokens satisfying the requirements. Call AcquireToken again providing more requirements such as authority or account.",[b]:"The cache contains multiple accounts satisfying the given parameters. Please pass more info to obtain the correct account",[O]:"The cache contains multiple appMetadata satisfying the given parameters. Please pass more info to obtain the correct appMetadata",[N]:"Token request cannot be made without authorization code or refresh token.",[P]:"Cannot remove null or empty scope from ScopeSet",[M]:"Cannot append ScopeSet",[U]:"Empty input ScopeSet cannot be processed",[q]:"Caller has cancelled token endpoint polling during device code flow by setting DeviceCodeRequest.cancel = true.",[H]:"Device code is expired.",[L]:"Device code stopped polling for unknown reasons.",[D]:"Please pass an account object, silent flow is not supported without account information",[F]:"Cache record object was null or undefined.",[x]:"Invalid environment when attempting to create cache entry",[B]:"No account found in cache for given key.",[K]:"No crypto object detected.",[G]:"Unexpected credential type.",[z]:"Client assertion must meet requirements described in https://tools.ietf.org/html/rfc7515",[$]:"Client credential (secret, certificate, or assertion) must not be empty when creating a confidential client. An application should at most have one credential",[W]:"Cannot return token from cache because it must be refreshed. This may be due to one of the following reasons: forceRefresh parameter is set to true, claims have been requested, there is no cached access token or it is expired.",[V]:"User defined timeout for device code polling reached",[Q]:"Cannot generate a POP jwt if the token_claims are not populated",[j]:"Server response does not contain an authorization code to proceed",[Y]:"Could not remove the credential's binding key from storage.",[J]:"The provided authority does not support logout",[X]:"A keyId value is missing from the requested bound token's cache record and is required to match the token to it's stored binding key.",[Z]:"No network connectivity. Check your internet connection.",[ee]:"User cancelled the flow.",[et]:"A tenant id - not common, organizations, or consumers - must be specified when using the client_credentials flow.",[er]:"This method has not been implemented",[en]:"The nested app auth bridge is disabled"};ei[p],ei[m],ei[f],ei[C],ei[y],ei[T],ei[I],ei[v],ei[A],ei[w],ei[S],ei[E],ei[k],ei[R],ei[_],ei[b],ei[O],ei[N],ei[P],ei[M],ei[U],ei[q],ei[H],ei[L],ei[D],ei[F],ei[x],ei[B],ei[K],ei[G],ei[z],ei[$],ei[W],ei[V],ei[Q],ei[j],ei[Y],ei[J],ei[X],ei[Z],ei[ee],ei[et],ei[en];class eo extends u{constructor(e,t){super(e,t?`${ei[e]}: ${t}`:ei[e]),this.name="ClientAuthError",Object.setPrototypeOf(this,eo.prototype)}}function ea(e,t){return new eo(e,t)}let es={createNewGuid:()=>{throw ea(er)},base64Decode:()=>{throw ea(er)},base64Encode:()=>{throw ea(er)},base64UrlEncode:()=>{throw ea(er)},encodeKid:()=>{throw ea(er)},async getPublicKeyThumbprint(){throw ea(er)},async removeTokenBindingKey(){throw ea(er)},async clearKeystore(){throw ea(er)},async signJwt(){throw ea(er)},async hashString(){throw ea(er)}},ec="@azure/msal-common",el="14.16.0";function ed(e,t){let r=function(e){if(!e)throw ea(C);let t=/^([^\.\s]*)\.([^\.\s]+)\.([^\.\s]*)$/.exec(e);if(!t||t.length<4)throw ea(f);return t[2]}(e);try{let e=t(r);return JSON.parse(e)}catch(e){throw ea(f)}}function eh(e,t){if(0===t||Date.now()-3e5>e+t)throw ea(R)}function eu(){return Math.round(new Date().getTime()/1e3)}function eg(e,t){let r=Number(e)||0;return eu()+t>r}function ep(e){return[[e.homeAccountId,e.environment].join(a.Bv.CACHE_KEY_SEPARATOR).toLowerCase(),function(e){let t=e.credentialType===a.d3.REFRESH_TOKEN&&e.familyId||e.clientId;return[e.credentialType,t,e.realm||""].join(a.Bv.CACHE_KEY_SEPARATOR).toLowerCase()}(e),(e.target||"").toLowerCase(),(e.requestedClaimsHash||"").toLowerCase(),e.tokenType&&e.tokenType.toLowerCase()!==a.hO.BEARER.toLowerCase()?e.tokenType.toLowerCase():""].join(a.Bv.CACHE_KEY_SEPARATOR).toLowerCase()}function em(e,t,r,n,i){return{credentialType:a.d3.ID_TOKEN,homeAccountId:e,environment:t,clientId:n,secret:r,realm:i}}function ef(e,t,r,n,i,o,s,c,l,d,h,u,g,p,m){let f={homeAccountId:e,credentialType:a.d3.ACCESS_TOKEN,secret:r,cachedAt:eu().toString(),expiresOn:s.toString(),extendedExpiresOn:c.toString(),environment:t,clientId:n,realm:i,target:o,tokenType:h||a.hO.BEARER};if(u&&(f.userAssertionHash=u),d&&(f.refreshOn=d.toString()),p&&(f.requestedClaims=p,f.requestedClaimsHash=m),f.tokenType?.toLowerCase()!==a.hO.BEARER.toLowerCase())switch(f.credentialType=a.d3.ACCESS_TOKEN_WITH_AUTH_SCHEME,f.tokenType){case a.hO.POP:let C=ed(r,l);if(!C?.cnf?.kid)throw ea(Q);f.keyId=C.cnf.kid;break;case a.hO.SSH:f.keyId=g}return f}function eC(e,t,r,n,i,o,s){let c={credentialType:a.d3.REFRESH_TOKEN,homeAccountId:e,environment:t,clientId:n,secret:r};return o&&(c.userAssertionHash=o),i&&(c.familyId=i),s&&(c.expiresOn=s.toString()),c}function ey(e){return e.hasOwnProperty("homeAccountId")&&e.hasOwnProperty("environment")&&e.hasOwnProperty("credentialType")&&e.hasOwnProperty("clientId")&&e.hasOwnProperty("secret")}function eT(e){return!!e&&ey(e)&&e.hasOwnProperty("realm")&&e.hasOwnProperty("target")&&(e.credentialType===a.d3.ACCESS_TOKEN||e.credentialType===a.d3.ACCESS_TOKEN_WITH_AUTH_SCHEME)}function eI(e){return!!e&&ey(e)&&e.hasOwnProperty("realm")&&e.credentialType===a.d3.ID_TOKEN}function ev(e){return!!e&&ey(e)&&e.credentialType===a.d3.REFRESH_TOKEN}function eA(){return eu()+a.QU.REFRESH_TIME_SECONDS}function ew(e,t,r){e.authorization_endpoint=t.authorization_endpoint,e.token_endpoint=t.token_endpoint,e.end_session_endpoint=t.end_session_endpoint,e.issuer=t.issuer,e.endpointsFromNetwork=r,e.jwks_uri=t.jwks_uri}function eS(e,t,r){e.aliases=t.aliases,e.preferred_cache=t.preferred_cache,e.preferred_network=t.preferred_network,e.aliasesFromNetwork=r}function eE(e){return e.expiresAt<=eu()}let ek="redirect_uri_empty",eR="claims_request_parsing_error",e_="authority_uri_insecure",eb="url_parse_error",eO="empty_url_error",eN="empty_input_scopes_error",eP="invalid_prompt_value",eM="invalid_claims",eU="token_request_empty",eq="logout_request_empty",eH="invalid_code_challenge_method",eL="pkce_params_missing",eD="invalid_cloud_discovery_metadata",eF="invalid_authority_metadata",ex="untrusted_authority",eB="missing_ssh_jwk",eK="missing_ssh_kid",eG="missing_nonce_authentication_header",ez="invalid_authentication_header",e$="cannot_set_OIDCOptions",eW="cannot_allow_native_broker",eV="authority_mismatch",eQ={[ek]:"A redirect URI is required for all calls, and none has been set.",[eR]:"Could not parse the given claims request object.",[e_]:"Authority URIs must use https.  Please see here for valid authority configuration options: https://docs.microsoft.com/en-us/azure/active-directory/develop/msal-js-initializing-client-applications#configuration-options",[eb]:"URL could not be parsed into appropriate segments.",[eO]:"URL was empty or null.",[eN]:"Scopes cannot be passed as null, undefined or empty array because they are required to obtain an access token.",[eP]:"Please see here for valid configuration options: https://azuread.github.io/microsoft-authentication-library-for-js/ref/modules/_azure_msal_common.html#commonauthorizationurlrequest",[eM]:"Given claims parameter must be a stringified JSON object.",[eU]:"Token request was empty and not found in cache.",[eq]:"The logout request was null or undefined.",[eH]:'code_challenge_method passed is invalid. Valid values are "plain" and "S256".',[eL]:"Both params: code_challenge and code_challenge_method are to be passed if to be sent in the request",[eD]:"Invalid cloudDiscoveryMetadata provided. Must be a stringified JSON object containing tenant_discovery_endpoint and metadata fields",[eF]:"Invalid authorityMetadata provided. Must by a stringified JSON object containing authorization_endpoint, token_endpoint, issuer fields.",[ex]:"The provided authority is not a trusted authority. Please include this authority in the knownAuthorities config parameter.",[eB]:"Missing sshJwk in SSH certificate request. A stringified JSON Web Key is required when using the SSH authentication scheme.",[eK]:"Missing sshKid in SSH certificate request. A string that uniquely identifies the public SSH key is required when using the SSH authentication scheme.",[eG]:"Unable to find an authentication header containing server nonce. Either the Authentication-Info or WWW-Authenticate headers must be present in order to obtain a server nonce.",[ez]:"Invalid authentication header provided",[e$]:"Cannot set OIDCOptions parameter. Please change the protocol mode to OIDC or use a non-Microsoft authority.",[eW]:"Cannot set allowNativeBroker parameter to true when not in AAD protocol mode.",[eV]:"Authority mismatch error. Authority provided in login request or PublicClientApplication config does not match the environment of the provided account. Please use a matching account or make an interactive request to login to this authority."};eQ[ek],eQ[eR],eQ[e_],eQ[eb],eQ[eO],eQ[eN],eQ[eP],eQ[eM],eQ[eU],eQ[eq],eQ[eH],eQ[eL],eQ[eD],eQ[eF],eQ[ex],eQ[eB],eQ[eK],eQ[eG],eQ[ez],eQ[e$],eQ[eW],eQ[eV];class ej extends u{constructor(e){super(e,eQ[e]),this.name="ClientConfigurationError",Object.setPrototypeOf(this,ej.prototype)}}function eY(e){return new ej(e)}class eJ{static isEmptyObj(e){if(e)try{let t=JSON.parse(e);return 0===Object.keys(t).length}catch(e){}return!0}static startsWith(e,t){return 0===e.indexOf(t)}static endsWith(e,t){return e.length>=t.length&&e.lastIndexOf(t)===e.length-t.length}static queryStringToObject(e){let t={},r=e.split("&"),n=e=>decodeURIComponent(e.replace(/\+/g," "));return r.forEach(e=>{if(e.trim()){let[r,i]=e.split(/=(.+)/g,2);r&&i&&(t[n(r)]=n(i))}}),t}static trimArrayEntries(e){return e.map(e=>e.trim())}static removeEmptyStringsFromArray(e){return e.filter(e=>!!e)}static jsonParseHelper(e){try{return JSON.parse(e)}catch(e){return null}}static matchPattern(e,t){return new RegExp(e.replace(/\\/g,"\\\\").replace(/\*/g,"[^ ]*").replace(/\?/g,"\\?")).test(t)}}class eX{constructor(e){let t=e?eJ.trimArrayEntries([...e]):[],r=t?eJ.removeEmptyStringsFromArray(t):[];this.validateInputScopes(r),this.scopes=new Set,r.forEach(e=>this.scopes.add(e))}static fromString(e){return new eX((e||a.gT.EMPTY_STRING).split(" "))}static createSearchScopes(e){let t=new eX(e);return t.containsOnlyOIDCScopes()?t.removeScope(a.gT.OFFLINE_ACCESS_SCOPE):t.removeOIDCScopes(),t}validateInputScopes(e){if(!e||e.length<1)throw eY(eN)}containsScope(e){let t=new eX(this.printScopesLowerCase().split(" "));return!!e&&t.scopes.has(e.toLowerCase())}containsScopeSet(e){return!!e&&!(e.scopes.size<=0)&&this.scopes.size>=e.scopes.size&&e.asArray().every(e=>this.containsScope(e))}containsOnlyOIDCScopes(){let e=0;return a.$f.forEach(t=>{this.containsScope(t)&&(e+=1)}),this.scopes.size===e}appendScope(e){e&&this.scopes.add(e.trim())}appendScopes(e){try{e.forEach(e=>this.appendScope(e))}catch(e){throw ea(M)}}removeScope(e){if(!e)throw ea(P);this.scopes.delete(e.trim())}removeOIDCScopes(){a.$f.forEach(e=>{this.scopes.delete(e)})}unionScopeSets(e){if(!e)throw ea(U);let t=new Set;return e.scopes.forEach(e=>t.add(e.toLowerCase())),this.scopes.forEach(e=>t.add(e.toLowerCase())),t}intersectingScopeSets(e){if(!e)throw ea(U);e.containsOnlyOIDCScopes()||e.removeOIDCScopes();let t=this.unionScopeSets(e),r=e.getScopeCount(),n=this.getScopeCount();return t.size<n+r}getScopeCount(){return this.scopes.size}asArray(){let e=[];return this.scopes.forEach(t=>e.push(t)),e}printScopes(){return this.scopes?this.asArray().join(" "):a.gT.EMPTY_STRING}printScopesLowerCase(){return this.printScopes().toLowerCase()}}function eZ(e,t){if(!e)throw ea(m);try{let r=t(e);return JSON.parse(r)}catch(e){throw ea(p)}}function e0(e){if(!e)throw ea(p);let t=e.split(a.Bv.CLIENT_INFO_SEPARATOR,2);return{uid:t[0],utid:t.length<2?a.gT.EMPTY_STRING:t[1]}}function e1(e,t){return!!e&&!!t&&e===t.split(".")[1]}function e3(e,t,r,n){if(!n)return{tenantId:r,localAccountId:t,isHomeTenant:e1(r,e)};{let{oid:t,sub:r,tid:i,name:o,tfp:a,acr:s}=n,c=i||a||s||"";return{tenantId:c,localAccountId:t||r||"",name:o,isHomeTenant:e1(c,e)}}}function e2(e,t,r,n){let i=e;if(t){let{isHomeTenant:r,...n}=t;i={...e,...n}}if(r){let{isHomeTenant:t,...o}=e3(e.homeAccountId,e.localAccountId,e.tenantId,r);i={...i,...o,idTokenClaims:r,idToken:n}}return i}let e6={Default:0,Adfs:1,Dsts:2,Ciam:3};function e4(e){return e&&(e.tid||e.tfp||e.acr)||null}class e5{generateAccountId(){return[this.homeAccountId,this.environment].join(a.Bv.CACHE_KEY_SEPARATOR).toLowerCase()}generateAccountKey(){return e5.generateAccountCacheKey({homeAccountId:this.homeAccountId,environment:this.environment,tenantId:this.realm,username:this.username,localAccountId:this.localAccountId})}getAccountInfo(){return{homeAccountId:this.homeAccountId,environment:this.environment,tenantId:this.realm,username:this.username,localAccountId:this.localAccountId,name:this.name,nativeAccountId:this.nativeAccountId,authorityType:this.authorityType,tenantProfiles:new Map((this.tenantProfiles||[]).map(e=>[e.tenantId,e]))}}isSingleTenant(){return!this.tenantProfiles}static generateAccountCacheKey(e){let t=e.homeAccountId.split(".")[1];return[e.homeAccountId,e.environment||"",t||e.tenantId||""].join(a.Bv.CACHE_KEY_SEPARATOR).toLowerCase()}static createAccount(e,t,r){let n;let i=new e5;t.authorityType===e6.Adfs?i.authorityType=a.iR.ADFS_ACCOUNT_TYPE:t.protocolMode===s.AAD?i.authorityType=a.iR.MSSTS_ACCOUNT_TYPE:i.authorityType=a.iR.GENERIC_ACCOUNT_TYPE,e.clientInfo&&r&&(n=eZ(e.clientInfo,r)),i.clientInfo=e.clientInfo,i.homeAccountId=e.homeAccountId,i.nativeAccountId=e.nativeAccountId;let o=e.environment||t&&t.getPreferredCache();if(!o)throw ea(x);i.environment=o,i.realm=n?.utid||e4(e.idTokenClaims)||"",i.localAccountId=n?.uid||e.idTokenClaims?.oid||e.idTokenClaims?.sub||"";let c=e.idTokenClaims?.preferred_username||e.idTokenClaims?.upn,l=e.idTokenClaims?.emails?e.idTokenClaims.emails[0]:null;if(i.username=c||l||"",i.name=e.idTokenClaims?.name||"",i.cloudGraphHostName=e.cloudGraphHostName,i.msGraphHost=e.msGraphHost,e.tenantProfiles)i.tenantProfiles=e.tenantProfiles;else{let t=e3(e.homeAccountId,i.localAccountId,i.realm,e.idTokenClaims);i.tenantProfiles=[t]}return i}static createFromAccountInfo(e,t,r){let n=new e5;return n.authorityType=e.authorityType||a.iR.GENERIC_ACCOUNT_TYPE,n.homeAccountId=e.homeAccountId,n.localAccountId=e.localAccountId,n.nativeAccountId=e.nativeAccountId,n.realm=e.tenantId,n.environment=e.environment,n.username=e.username,n.name=e.name,n.cloudGraphHostName=t,n.msGraphHost=r,n.tenantProfiles=Array.from(e.tenantProfiles?.values()||[]),n}static generateHomeAccountId(e,t,r,n,i){if(!(t===e6.Adfs||t===e6.Dsts)){if(e)try{let t=eZ(e,n.base64Decode);if(t.uid&&t.utid)return`${t.uid}.${t.utid}`}catch(e){}r.warning("No client info in response")}return i?.sub||""}static isAccountEntity(e){return!!e&&e.hasOwnProperty("homeAccountId")&&e.hasOwnProperty("environment")&&e.hasOwnProperty("realm")&&e.hasOwnProperty("localAccountId")&&e.hasOwnProperty("username")&&e.hasOwnProperty("authorityType")}static accountInfoIsEqual(e,t,r){if(!e||!t)return!1;let n=!0;if(r){let r=e.idTokenClaims||{},i=t.idTokenClaims||{};n=r.iat===i.iat&&r.nonce===i.nonce}return e.homeAccountId===t.homeAccountId&&e.localAccountId===t.localAccountId&&e.username===t.username&&e.tenantId===t.tenantId&&e.environment===t.environment&&e.nativeAccountId===t.nativeAccountId&&n}}function e9(e){return e.startsWith("#/")?e.substring(2):e.startsWith("#")||e.startsWith("?")?e.substring(1):e}function e8(e){if(!e||0>e.indexOf("="))return null;try{let t=e9(e),r=Object.fromEntries(new URLSearchParams(t));if(r.code||r.error||r.error_description||r.state)return r}catch(e){throw ea(v)}return null}class e7{get urlString(){return this._urlString}constructor(e){if(this._urlString=e,!this._urlString)throw eY(eO);e.includes("#")||(this._urlString=e7.canonicalizeUri(e))}static canonicalizeUri(e){if(e){let t=e.toLowerCase();return eJ.endsWith(t,"?")?t=t.slice(0,-1):eJ.endsWith(t,"?/")&&(t=t.slice(0,-2)),eJ.endsWith(t,"/")||(t+="/"),t}return e}validateAsUri(){let e;try{e=this.getUrlComponents()}catch(e){throw eY(eb)}if(!e.HostNameAndPort||!e.PathSegments)throw eY(eb);if(!e.Protocol||"https:"!==e.Protocol.toLowerCase())throw eY(e_)}static appendQueryString(e,t){return t?0>e.indexOf("?")?`${e}?${t}`:`${e}&${t}`:e}static removeHashFromUrl(e){return e7.canonicalizeUri(e.split("#")[0])}replaceTenantPath(e){let t=this.getUrlComponents(),r=t.PathSegments;return e&&0!==r.length&&(r[0]===a.Nb.COMMON||r[0]===a.Nb.ORGANIZATIONS)&&(r[0]=e),e7.constructAuthorityUriFromObject(t)}getUrlComponents(){let e=RegExp("^(([^:/?#]+):)?(//([^/?#]*))?([^?#]*)(\\?([^#]*))?(#(.*))?"),t=this.urlString.match(e);if(!t)throw eY(eb);let r={Protocol:t[1],HostNameAndPort:t[4],AbsolutePath:t[5],QueryString:t[7]},n=r.AbsolutePath.split("/");return n=n.filter(e=>e&&e.length>0),r.PathSegments=n,r.QueryString&&r.QueryString.endsWith("/")&&(r.QueryString=r.QueryString.substring(0,r.QueryString.length-1)),r}static getDomainFromUrl(e){let t=RegExp("^([^:/?#]+://)?([^/?#]*)"),r=e.match(t);if(!r)throw eY(eb);return r[2]}static getAbsoluteUrl(e,t){if(e[0]===a.gT.FORWARD_SLASH){let r=new e7(t).getUrlComponents();return r.Protocol+"//"+r.HostNameAndPort+e}return e}static constructAuthorityUriFromObject(e){return new e7(e.Protocol+"//"+e.HostNameAndPort+"/"+e.PathSegments.join("/"))}static hashContainsKnownProperties(e){return!!e8(e)}}let te={"login.microsoftonline.com":{token_endpoint:"https://login.microsoftonline.com/{tenantid}/oauth2/v2.0/token",jwks_uri:"https://login.microsoftonline.com/{tenantid}/discovery/v2.0/keys",issuer:"https://login.microsoftonline.com/{tenantid}/v2.0",authorization_endpoint:"https://login.microsoftonline.com/{tenantid}/oauth2/v2.0/authorize",end_session_endpoint:"https://login.microsoftonline.com/{tenantid}/oauth2/v2.0/logout"},"login.chinacloudapi.cn":{token_endpoint:"https://login.chinacloudapi.cn/{tenantid}/oauth2/v2.0/token",jwks_uri:"https://login.chinacloudapi.cn/{tenantid}/discovery/v2.0/keys",issuer:"https://login.partner.microsoftonline.cn/{tenantid}/v2.0",authorization_endpoint:"https://login.chinacloudapi.cn/{tenantid}/oauth2/v2.0/authorize",end_session_endpoint:"https://login.chinacloudapi.cn/{tenantid}/oauth2/v2.0/logout"},"login.microsoftonline.us":{token_endpoint:"https://login.microsoftonline.us/{tenantid}/oauth2/v2.0/token",jwks_uri:"https://login.microsoftonline.us/{tenantid}/discovery/v2.0/keys",issuer:"https://login.microsoftonline.us/{tenantid}/v2.0",authorization_endpoint:"https://login.microsoftonline.us/{tenantid}/oauth2/v2.0/authorize",end_session_endpoint:"https://login.microsoftonline.us/{tenantid}/oauth2/v2.0/logout"}},tt={metadata:[{preferred_network:"login.microsoftonline.com",preferred_cache:"login.windows.net",aliases:["login.microsoftonline.com","login.windows.net","login.microsoft.com","sts.windows.net"]},{preferred_network:"login.partner.microsoftonline.cn",preferred_cache:"login.partner.microsoftonline.cn",aliases:["login.partner.microsoftonline.cn","login.chinacloudapi.cn"]},{preferred_network:"login.microsoftonline.de",preferred_cache:"login.microsoftonline.de",aliases:["login.microsoftonline.de"]},{preferred_network:"login.microsoftonline.us",preferred_cache:"login.microsoftonline.us",aliases:["login.microsoftonline.us","login.usgovcloudapi.net"]},{preferred_network:"login-us.microsoftonline.com",preferred_cache:"login-us.microsoftonline.com",aliases:["login-us.microsoftonline.com"]}]},tr=new Set;function tn(e,t,r,n){if(n?.trace(`getAliasesFromMetadata called with source: ${r}`),e&&t){let i=ti(t,e);if(i)return n?.trace(`getAliasesFromMetadata: found cloud discovery metadata in ${r}, returning aliases`),i.aliases;n?.trace(`getAliasesFromMetadata: did not find cloud discovery metadata in ${r}`)}return null}function ti(e,t){for(let r=0;r<e.length;r++){let n=e[r];if(n.aliases.includes(t))return n}return null}tt.metadata.forEach(e=>{e.aliases.forEach(e=>{tr.add(e)})});let to="cache_quota_exceeded",ta="cache_error_unknown",ts={[to]:"Exceeded cache storage capacity.",[ta]:"Unexpected error occurred when using cache storage."};class tc extends Error{constructor(e,t){let r=t||(ts[e]?ts[e]:ts[ta]);super(`${e}: ${r}`),Object.setPrototypeOf(this,tc.prototype),this.name="CacheError",this.errorCode=e,this.errorMessage=r}}class tl{constructor(e,t,r,n){this.clientId=e,this.cryptoImpl=t,this.commonLogger=r.clone(ec,el),this.staticAuthorityOptions=n}getAllAccounts(e){return this.buildTenantProfiles(this.getAccountsFilteredBy(e||{}),e)}getAccountInfoFilteredBy(e){let t=this.getAllAccounts(e);return t.length>1?t.sort(e=>e.idTokenClaims?-1:1)[0]:1===t.length?t[0]:null}getBaseAccountInfo(e){let t=this.getAccountsFilteredBy(e);return t.length>0?t[0].getAccountInfo():null}buildTenantProfiles(e,t){return e.flatMap(e=>this.getTenantProfilesFromAccountEntity(e,t?.tenantId,t))}getTenantedAccountInfoByFilter(e,t,r,n){let i;if(n&&!this.tenantProfileMatchesFilter(r,n))return null;let o=this.getIdToken(e,t,r.tenantId);return o&&(i=ed(o.secret,this.cryptoImpl.base64Decode),!this.idTokenClaimsMatchTenantProfileFilter(i,n))?null:e2(e,r,i,o?.secret)}getTenantProfilesFromAccountEntity(e,t,r){let n=e.getAccountInfo(),i=n.tenantProfiles||new Map,o=this.getTokenKeys();if(t){let e=i.get(t);if(!e)return[];i=new Map([[t,e]])}let a=[];return i.forEach(e=>{let t=this.getTenantedAccountInfoByFilter(n,o,e,r);t&&a.push(t)}),a}tenantProfileMatchesFilter(e,t){return(!t.localAccountId||!!this.matchLocalAccountIdFromTenantProfile(e,t.localAccountId))&&(!t.name||e.name===t.name)&&(void 0===t.isHomeTenant||e.isHomeTenant===t.isHomeTenant)}idTokenClaimsMatchTenantProfileFilter(e,t){return!t||(!t.localAccountId||!!this.matchLocalAccountIdFromTokenClaims(e,t.localAccountId))&&(!t.loginHint||!!this.matchLoginHintFromTokenClaims(e,t.loginHint))&&(!t.username||!!this.matchUsername(e.preferred_username,t.username))&&(!t.name||!!this.matchName(e,t.name))&&(!t.sid||!!this.matchSid(e,t.sid))}async saveCacheRecord(e,t,r){if(!e)throw ea(F);try{e.account&&this.setAccount(e.account),e.idToken&&t?.idToken!==!1&&this.setIdTokenCredential(e.idToken),e.accessToken&&t?.accessToken!==!1&&await this.saveAccessToken(e.accessToken),e.refreshToken&&t?.refreshToken!==!1&&this.setRefreshTokenCredential(e.refreshToken),e.appMetadata&&this.setAppMetadata(e.appMetadata)}catch(e){if(this.commonLogger?.error("CacheManager.saveCacheRecord: failed"),e instanceof Error){if(this.commonLogger?.errorPii(`CacheManager.saveCacheRecord: ${e.message}`,r),"QuotaExceededError"===e.name||"NS_ERROR_DOM_QUOTA_REACHED"===e.name||e.message.includes("exceeded the quota"))throw this.commonLogger?.error("CacheManager.saveCacheRecord: exceeded storage quota",r),new tc(to);throw new tc(e.name,e.message)}throw this.commonLogger?.errorPii(`CacheManager.saveCacheRecord: ${e}`,r),new tc(ta)}}async saveAccessToken(e){let t={clientId:e.clientId,credentialType:e.credentialType,environment:e.environment,homeAccountId:e.homeAccountId,realm:e.realm,tokenType:e.tokenType,requestedClaimsHash:e.requestedClaimsHash},r=this.getTokenKeys(),n=eX.fromString(e.target),i=[];r.accessToken.forEach(e=>{if(!this.accessTokenKeyMatchesFilter(e,t,!1))return;let r=this.getAccessTokenCredential(e);r&&this.credentialMatchesFilter(r,t)&&eX.fromString(r.target).intersectingScopeSets(n)&&i.push(this.removeAccessToken(e))}),await Promise.all(i),this.setAccessTokenCredential(e)}getAccountsFilteredBy(e){let t=this.getAccountKeys(),r=[];return t.forEach(t=>{if(!this.isAccountKey(t,e.homeAccountId))return;let n=this.getAccount(t,this.commonLogger);if(!n||e.homeAccountId&&!this.matchHomeAccountId(n,e.homeAccountId)||e.username&&!this.matchUsername(n.username,e.username)||e.environment&&!this.matchEnvironment(n,e.environment)||e.realm&&!this.matchRealm(n,e.realm)||e.nativeAccountId&&!this.matchNativeAccountId(n,e.nativeAccountId)||e.authorityType&&!this.matchAuthorityType(n,e.authorityType))return;let i={localAccountId:e?.localAccountId,name:e?.name},o=n.tenantProfiles?.filter(e=>this.tenantProfileMatchesFilter(e,i));o&&0===o.length||r.push(n)}),r}isAccountKey(e,t,r){return!(e.split(a.Bv.CACHE_KEY_SEPARATOR).length<3)&&(!t||!!e.toLowerCase().includes(t.toLowerCase()))&&(!r||!!e.toLowerCase().includes(r.toLowerCase()))}isCredentialKey(e){if(e.split(a.Bv.CACHE_KEY_SEPARATOR).length<6)return!1;let t=e.toLowerCase();if(-1===t.indexOf(a.d3.ID_TOKEN.toLowerCase())&&-1===t.indexOf(a.d3.ACCESS_TOKEN.toLowerCase())&&-1===t.indexOf(a.d3.ACCESS_TOKEN_WITH_AUTH_SCHEME.toLowerCase())&&-1===t.indexOf(a.d3.REFRESH_TOKEN.toLowerCase()))return!1;if(t.indexOf(a.d3.REFRESH_TOKEN.toLowerCase())>-1){let e=`${a.d3.REFRESH_TOKEN}${a.Bv.CACHE_KEY_SEPARATOR}${this.clientId}${a.Bv.CACHE_KEY_SEPARATOR}`,r=`${a.d3.REFRESH_TOKEN}${a.Bv.CACHE_KEY_SEPARATOR}${a.ch}${a.Bv.CACHE_KEY_SEPARATOR}`;if(-1===t.indexOf(e.toLowerCase())&&-1===t.indexOf(r.toLowerCase()))return!1}else if(-1===t.indexOf(this.clientId.toLowerCase()))return!1;return!0}credentialMatchesFilter(e,t){return(!t.clientId||!!this.matchClientId(e,t.clientId))&&(!t.userAssertionHash||!!this.matchUserAssertionHash(e,t.userAssertionHash))&&("string"!=typeof t.homeAccountId||!!this.matchHomeAccountId(e,t.homeAccountId))&&(!t.environment||!!this.matchEnvironment(e,t.environment))&&(!t.realm||!!this.matchRealm(e,t.realm))&&(!t.credentialType||!!this.matchCredentialType(e,t.credentialType))&&(!t.familyId||!!this.matchFamilyId(e,t.familyId))&&(!t.target||!!this.matchTarget(e,t.target))&&(!t.requestedClaimsHash&&!e.requestedClaimsHash||e.requestedClaimsHash===t.requestedClaimsHash)&&(e.credentialType!==a.d3.ACCESS_TOKEN_WITH_AUTH_SCHEME||(!t.tokenType||!!this.matchTokenType(e,t.tokenType))&&(t.tokenType!==a.hO.SSH||!t.keyId||!!this.matchKeyId(e,t.keyId)))}getAppMetadataFilteredBy(e){let t=this.getKeys(),r={};return t.forEach(t=>{if(!this.isAppMetadata(t))return;let n=this.getAppMetadata(t);n&&(!e.environment||this.matchEnvironment(n,e.environment))&&(!e.clientId||this.matchClientId(n,e.clientId))&&(r[t]=n)}),r}getAuthorityMetadataByAlias(e){let t=this.getAuthorityMetadataKeys(),r=null;return t.forEach(t=>{if(!this.isAuthorityMetadata(t)||-1===t.indexOf(this.clientId))return;let n=this.getAuthorityMetadata(t);n&&-1!==n.aliases.indexOf(e)&&(r=n)}),r}async removeAllAccounts(){let e=this.getAccountKeys(),t=[];e.forEach(e=>{t.push(this.removeAccount(e))}),await Promise.all(t)}async removeAccount(e){let t=this.getAccount(e,this.commonLogger);t&&(await this.removeAccountContext(t),this.removeItem(e))}async removeAccountContext(e){let t=this.getTokenKeys(),r=e.generateAccountId(),n=[];t.idToken.forEach(e=>{0===e.indexOf(r)&&this.removeIdToken(e)}),t.accessToken.forEach(e=>{0===e.indexOf(r)&&n.push(this.removeAccessToken(e))}),t.refreshToken.forEach(e=>{0===e.indexOf(r)&&this.removeRefreshToken(e)}),await Promise.all(n)}updateOutdatedCachedAccount(e,t,r){if(t&&t.isSingleTenant()){this.commonLogger?.verbose("updateOutdatedCachedAccount: Found a single-tenant (outdated) account entity in the cache, migrating to multi-tenant account entity");let n=this.getAccountKeys().filter(e=>e.startsWith(t.homeAccountId)),i=[];n.forEach(e=>{let t=this.getCachedAccountEntity(e);t&&i.push(t)});let o=i.find(e=>e1(e.realm,e.homeAccountId))||i[0];o.tenantProfiles=i.map(e=>({tenantId:e.realm,localAccountId:e.localAccountId,name:e.name,isHomeTenant:e1(e.realm,e.homeAccountId)}));let a=tl.toObject(new e5,{...o}),s=a.generateAccountKey();return n.forEach(t=>{t!==s&&this.removeOutdatedAccount(e)}),this.setAccount(a),r?.verbose("Updated an outdated account entity in the cache"),a}return t}async removeAccessToken(e){let t=this.getAccessTokenCredential(e);if(t){if(t.credentialType.toLowerCase()===a.d3.ACCESS_TOKEN_WITH_AUTH_SCHEME.toLowerCase()&&t.tokenType===a.hO.POP){let e=t.keyId;if(e)try{await this.cryptoImpl.removeTokenBindingKey(e)}catch(e){throw ea(Y)}}return this.removeItem(e)}}removeAppMetadata(){return this.getKeys().forEach(e=>{this.isAppMetadata(e)&&this.removeItem(e)}),!0}readAccountFromCache(e){let t=e5.generateAccountCacheKey(e);return this.getAccount(t,this.commonLogger)}getIdToken(e,t,r,n,i){this.commonLogger.trace("CacheManager - getIdToken called");let o={homeAccountId:e.homeAccountId,environment:e.environment,credentialType:a.d3.ID_TOKEN,clientId:this.clientId,realm:r},s=this.getIdTokensByFilter(o,t),c=s.size;if(c<1)return this.commonLogger.info("CacheManager:getIdToken - No token found"),null;if(c>1){let t=s;if(!r){let r=new Map;s.forEach((t,n)=>{t.realm===e.tenantId&&r.set(n,t)});let n=r.size;if(n<1)return this.commonLogger.info("CacheManager:getIdToken - Multiple ID tokens found for account but none match account entity tenant id, returning first result"),s.values().next().value;if(1===n)return this.commonLogger.info("CacheManager:getIdToken - Multiple ID tokens found for account, defaulting to home tenant profile"),r.values().next().value;t=r}return this.commonLogger.info("CacheManager:getIdToken - Multiple matching ID tokens found, clearing them"),t.forEach((e,t)=>{this.removeIdToken(t)}),n&&i&&n.addFields({multiMatchedID:s.size},i),null}return this.commonLogger.info("CacheManager:getIdToken - Returning ID token"),s.values().next().value}getIdTokensByFilter(e,t){let r=t&&t.idToken||this.getTokenKeys().idToken,n=new Map;return r.forEach(t=>{if(!this.idTokenKeyMatchesFilter(t,{clientId:this.clientId,...e}))return;let r=this.getIdTokenCredential(t);r&&this.credentialMatchesFilter(r,e)&&n.set(t,r)}),n}idTokenKeyMatchesFilter(e,t){let r=e.toLowerCase();return(!t.clientId||-1!==r.indexOf(t.clientId.toLowerCase()))&&(!t.homeAccountId||-1!==r.indexOf(t.homeAccountId.toLowerCase()))}removeIdToken(e){this.removeItem(e)}removeRefreshToken(e){this.removeItem(e)}getAccessToken(e,t,r,n,i,o){this.commonLogger.trace("CacheManager - getAccessToken called");let s=eX.createSearchScopes(t.scopes),c=t.authenticationScheme||a.hO.BEARER,l=c&&c.toLowerCase()!==a.hO.BEARER.toLowerCase()?a.d3.ACCESS_TOKEN_WITH_AUTH_SCHEME:a.d3.ACCESS_TOKEN,d={homeAccountId:e.homeAccountId,environment:e.environment,credentialType:l,clientId:this.clientId,realm:n||e.tenantId,target:s,tokenType:c,keyId:t.sshKid,requestedClaimsHash:t.requestedClaimsHash},h=r&&r.accessToken||this.getTokenKeys().accessToken,u=[];h.forEach(e=>{if(this.accessTokenKeyMatchesFilter(e,d,!0)){let t=this.getAccessTokenCredential(e);t&&this.credentialMatchesFilter(t,d)&&u.push(t)}});let g=u.length;return g<1?(this.commonLogger.info("CacheManager:getAccessToken - No token found"),null):g>1?(this.commonLogger.info("CacheManager:getAccessToken - Multiple access tokens found, clearing them"),u.forEach(e=>{this.removeAccessToken(ep(e))}),i&&o&&i.addFields({multiMatchedAT:u.length},o),null):(this.commonLogger.info("CacheManager:getAccessToken - Returning access token"),u[0])}accessTokenKeyMatchesFilter(e,t,r){let n=e.toLowerCase();if(t.clientId&&-1===n.indexOf(t.clientId.toLowerCase())||t.homeAccountId&&-1===n.indexOf(t.homeAccountId.toLowerCase())||t.realm&&-1===n.indexOf(t.realm.toLowerCase())||t.requestedClaimsHash&&-1===n.indexOf(t.requestedClaimsHash.toLowerCase()))return!1;if(t.target){let e=t.target.asArray();for(let t=0;t<e.length;t++){if(r&&!n.includes(e[t].toLowerCase()))return!1;if(!r&&n.includes(e[t].toLowerCase()))break}}return!0}getAccessTokensByFilter(e){let t=this.getTokenKeys(),r=[];return t.accessToken.forEach(t=>{if(!this.accessTokenKeyMatchesFilter(t,e,!0))return;let n=this.getAccessTokenCredential(t);n&&this.credentialMatchesFilter(n,e)&&r.push(n)}),r}getRefreshToken(e,t,r,n,i){this.commonLogger.trace("CacheManager - getRefreshToken called");let o=t?a.ch:void 0,s={homeAccountId:e.homeAccountId,environment:e.environment,credentialType:a.d3.REFRESH_TOKEN,clientId:this.clientId,familyId:o},c=r&&r.refreshToken||this.getTokenKeys().refreshToken,l=[];c.forEach(e=>{if(this.refreshTokenKeyMatchesFilter(e,s)){let t=this.getRefreshTokenCredential(e);t&&this.credentialMatchesFilter(t,s)&&l.push(t)}});let d=l.length;return d<1?(this.commonLogger.info("CacheManager:getRefreshToken - No refresh token found."),null):(d>1&&n&&i&&n.addFields({multiMatchedRT:d},i),this.commonLogger.info("CacheManager:getRefreshToken - returning refresh token"),l[0])}refreshTokenKeyMatchesFilter(e,t){let r=e.toLowerCase();return(!t.familyId||-1!==r.indexOf(t.familyId.toLowerCase()))&&(!!t.familyId||!t.clientId||-1!==r.indexOf(t.clientId.toLowerCase()))&&(!t.homeAccountId||-1!==r.indexOf(t.homeAccountId.toLowerCase()))}readAppMetadataFromCache(e){let t={environment:e,clientId:this.clientId},r=this.getAppMetadataFilteredBy(t),n=Object.keys(r).map(e=>r[e]),i=n.length;if(i<1)return null;if(i>1)throw ea(O);return n[0]}isAppMetadataFOCI(e){let t=this.readAppMetadataFromCache(e);return!!(t&&t.familyId===a.ch)}matchHomeAccountId(e,t){return!!("string"==typeof e.homeAccountId&&t===e.homeAccountId)}matchLocalAccountIdFromTokenClaims(e,t){return t===(e.oid||e.sub)}matchLocalAccountIdFromTenantProfile(e,t){return e.localAccountId===t}matchName(e,t){return!(t.toLowerCase()!==e.name?.toLowerCase())}matchUsername(e,t){return!!(e&&"string"==typeof e&&t?.toLowerCase()===e.toLowerCase())}matchUserAssertionHash(e,t){return!!(e.userAssertionHash&&t===e.userAssertionHash)}matchEnvironment(e,t){if(this.staticAuthorityOptions){let r=function(e,t){let r;let n=e.canonicalAuthority;if(n){let i=new e7(n).getUrlComponents().HostNameAndPort;r=tn(i,e.cloudDiscoveryMetadata?.metadata,a.pQ.CONFIG,t)||tn(i,tt.metadata,a.pQ.HARDCODED_VALUES,t)||e.knownAuthorities}return r||[]}(this.staticAuthorityOptions,this.commonLogger);if(r.includes(t)&&r.includes(e.environment))return!0}let r=this.getAuthorityMetadataByAlias(t);return!!(r&&r.aliases.indexOf(e.environment)>-1)}matchCredentialType(e,t){return e.credentialType&&t.toLowerCase()===e.credentialType.toLowerCase()}matchClientId(e,t){return!!(e.clientId&&t===e.clientId)}matchFamilyId(e,t){return!!(e.familyId&&t===e.familyId)}matchRealm(e,t){return!(e.realm?.toLowerCase()!==t.toLowerCase())}matchNativeAccountId(e,t){return!!(e.nativeAccountId&&t===e.nativeAccountId)}matchLoginHintFromTokenClaims(e,t){return e.login_hint===t||e.preferred_username===t||e.upn===t}matchSid(e,t){return e.sid===t}matchAuthorityType(e,t){return!!(e.authorityType&&t.toLowerCase()===e.authorityType.toLowerCase())}matchTarget(e,t){return(e.credentialType===a.d3.ACCESS_TOKEN||e.credentialType===a.d3.ACCESS_TOKEN_WITH_AUTH_SCHEME)&&!!e.target&&eX.fromString(e.target).containsScopeSet(t)}matchTokenType(e,t){return!!(e.tokenType&&e.tokenType===t)}matchKeyId(e,t){return!!(e.keyId&&e.keyId===t)}isAppMetadata(e){return -1!==e.indexOf(a.dg)}isAuthorityMetadata(e){return -1!==e.indexOf(a.QU.CACHE_KEY)}generateAuthorityMetadataCacheKey(e){return`${a.QU.CACHE_KEY}-${this.clientId}-${e}`}static toObject(e,t){for(let r in t)e[r]=t[r];return e}}class td extends tl{setAccount(){throw ea(er)}getAccount(){throw ea(er)}getCachedAccountEntity(){throw ea(er)}setIdTokenCredential(){throw ea(er)}getIdTokenCredential(){throw ea(er)}setAccessTokenCredential(){throw ea(er)}getAccessTokenCredential(){throw ea(er)}setRefreshTokenCredential(){throw ea(er)}getRefreshTokenCredential(){throw ea(er)}setAppMetadata(){throw ea(er)}getAppMetadata(){throw ea(er)}setServerTelemetry(){throw ea(er)}getServerTelemetry(){throw ea(er)}setAuthorityMetadata(){throw ea(er)}getAuthorityMetadata(){throw ea(er)}getAuthorityMetadataKeys(){throw ea(er)}setThrottlingCache(){throw ea(er)}getThrottlingCache(){throw ea(er)}removeItem(){throw ea(er)}getKeys(){throw ea(er)}getAccountKeys(){throw ea(er)}getTokenKeys(){throw ea(er)}updateCredentialCacheKey(){throw ea(er)}removeOutdatedAccount(){throw ea(er)}}let th={tokenRenewalOffsetSeconds:a.$A,preventCorsPreflight:!1},tu={loggerCallback:()=>{},piiLoggingEnabled:!1,logLevel:o.i.Info,correlationId:a.gT.EMPTY_STRING},tg={claimsBasedCachingEnabled:!1},tp={async sendGetRequestAsync(){throw ea(er)},async sendPostRequestAsync(){throw ea(er)}},tm={sku:a.gT.SKU,version:el,cpu:a.gT.EMPTY_STRING,os:a.gT.EMPTY_STRING},tf={clientSecret:a.gT.EMPTY_STRING,clientAssertion:void 0},tC={azureCloudInstance:c.None,tenant:`${a.gT.DEFAULT_COMMON_TENANT}`},ty={application:{appName:"",appVersion:""}};function tT(e){return e.authOptions.authority.options.protocolMode===s.OIDC}let tI={sendGetRequestAsync:()=>Promise.reject(ea(er)),sendPostRequestAsync:()=>Promise.reject(ea(er))},tv={AcquireTokenByCode:"acquireTokenByCode",AcquireTokenByRefreshToken:"acquireTokenByRefreshToken",AcquireTokenSilent:"acquireTokenSilent",AcquireTokenSilentAsync:"acquireTokenSilentAsync",AcquireTokenPopup:"acquireTokenPopup",AcquireTokenPreRedirect:"acquireTokenPreRedirect",AcquireTokenRedirect:"acquireTokenRedirect",CryptoOptsGetPublicKeyThumbprint:"cryptoOptsGetPublicKeyThumbprint",CryptoOptsSignJwt:"cryptoOptsSignJwt",SilentCacheClientAcquireToken:"silentCacheClientAcquireToken",SilentIframeClientAcquireToken:"silentIframeClientAcquireToken",AwaitConcurrentIframe:"awaitConcurrentIframe",SilentRefreshClientAcquireToken:"silentRefreshClientAcquireToken",SsoSilent:"ssoSilent",StandardInteractionClientGetDiscoveredAuthority:"standardInteractionClientGetDiscoveredAuthority",FetchAccountIdWithNativeBroker:"fetchAccountIdWithNativeBroker",NativeInteractionClientAcquireToken:"nativeInteractionClientAcquireToken",BaseClientCreateTokenRequestHeaders:"baseClientCreateTokenRequestHeaders",NetworkClientSendPostRequestAsync:"networkClientSendPostRequestAsync",RefreshTokenClientExecutePostToTokenEndpoint:"refreshTokenClientExecutePostToTokenEndpoint",AuthorizationCodeClientExecutePostToTokenEndpoint:"authorizationCodeClientExecutePostToTokenEndpoint",BrokerHandhshake:"brokerHandshake",AcquireTokenByRefreshTokenInBroker:"acquireTokenByRefreshTokenInBroker",AcquireTokenByBroker:"acquireTokenByBroker",RefreshTokenClientExecuteTokenRequest:"refreshTokenClientExecuteTokenRequest",RefreshTokenClientAcquireToken:"refreshTokenClientAcquireToken",RefreshTokenClientAcquireTokenWithCachedRefreshToken:"refreshTokenClientAcquireTokenWithCachedRefreshToken",RefreshTokenClientAcquireTokenByRefreshToken:"refreshTokenClientAcquireTokenByRefreshToken",RefreshTokenClientCreateTokenRequestBody:"refreshTokenClientCreateTokenRequestBody",AcquireTokenFromCache:"acquireTokenFromCache",SilentFlowClientAcquireCachedToken:"silentFlowClientAcquireCachedToken",SilentFlowClientGenerateResultFromCacheRecord:"silentFlowClientGenerateResultFromCacheRecord",AcquireTokenBySilentIframe:"acquireTokenBySilentIframe",InitializeBaseRequest:"initializeBaseRequest",InitializeSilentRequest:"initializeSilentRequest",InitializeClientApplication:"initializeClientApplication",SilentIframeClientTokenHelper:"silentIframeClientTokenHelper",SilentHandlerInitiateAuthRequest:"silentHandlerInitiateAuthRequest",SilentHandlerMonitorIframeForHash:"silentHandlerMonitorIframeForHash",SilentHandlerLoadFrame:"silentHandlerLoadFrame",SilentHandlerLoadFrameSync:"silentHandlerLoadFrameSync",StandardInteractionClientCreateAuthCodeClient:"standardInteractionClientCreateAuthCodeClient",StandardInteractionClientGetClientConfiguration:"standardInteractionClientGetClientConfiguration",StandardInteractionClientInitializeAuthorizationRequest:"standardInteractionClientInitializeAuthorizationRequest",StandardInteractionClientInitializeAuthorizationCodeRequest:"standardInteractionClientInitializeAuthorizationCodeRequest",GetAuthCodeUrl:"getAuthCodeUrl",HandleCodeResponseFromServer:"handleCodeResponseFromServer",HandleCodeResponse:"handleCodeResponse",UpdateTokenEndpointAuthority:"updateTokenEndpointAuthority",AuthClientAcquireToken:"authClientAcquireToken",AuthClientExecuteTokenRequest:"authClientExecuteTokenRequest",AuthClientCreateTokenRequestBody:"authClientCreateTokenRequestBody",AuthClientCreateQueryString:"authClientCreateQueryString",PopTokenGenerateCnf:"popTokenGenerateCnf",PopTokenGenerateKid:"popTokenGenerateKid",HandleServerTokenResponse:"handleServerTokenResponse",DeserializeResponse:"deserializeResponse",AuthorityFactoryCreateDiscoveredInstance:"authorityFactoryCreateDiscoveredInstance",AuthorityResolveEndpointsAsync:"authorityResolveEndpointsAsync",AuthorityResolveEndpointsFromLocalSources:"authorityResolveEndpointsFromLocalSources",AuthorityGetCloudDiscoveryMetadataFromNetwork:"authorityGetCloudDiscoveryMetadataFromNetwork",AuthorityUpdateCloudDiscoveryMetadata:"authorityUpdateCloudDiscoveryMetadata",AuthorityGetEndpointMetadataFromNetwork:"authorityGetEndpointMetadataFromNetwork",AuthorityUpdateEndpointMetadata:"authorityUpdateEndpointMetadata",AuthorityUpdateMetadataWithRegionalInformation:"authorityUpdateMetadataWithRegionalInformation",RegionDiscoveryDetectRegion:"regionDiscoveryDetectRegion",RegionDiscoveryGetRegionFromIMDS:"regionDiscoveryGetRegionFromIMDS",RegionDiscoveryGetCurrentVersion:"regionDiscoveryGetCurrentVersion",AcquireTokenByCodeAsync:"acquireTokenByCodeAsync",GetEndpointMetadataFromNetwork:"getEndpointMetadataFromNetwork",GetCloudDiscoveryMetadataFromNetworkMeasurement:"getCloudDiscoveryMetadataFromNetworkMeasurement",HandleRedirectPromiseMeasurement:"handleRedirectPromise",HandleNativeRedirectPromiseMeasurement:"handleNativeRedirectPromise",UpdateCloudDiscoveryMetadataMeasurement:"updateCloudDiscoveryMetadataMeasurement",UsernamePasswordClientAcquireToken:"usernamePasswordClientAcquireToken",NativeMessageHandlerHandshake:"nativeMessageHandlerHandshake",NativeGenerateAuthResult:"nativeGenerateAuthResult",RemoveHiddenIframe:"removeHiddenIframe",ClearTokensAndKeysWithClaims:"clearTokensAndKeysWithClaims",CacheManagerGetRefreshToken:"cacheManagerGetRefreshToken",GeneratePkceCodes:"generatePkceCodes",GenerateCodeVerifier:"generateCodeVerifier",GenerateCodeChallengeFromVerifier:"generateCodeChallengeFromVerifier",Sha256Digest:"sha256Digest",GetRandomValues:"getRandomValues"};tv.AcquireTokenByCode,tv.AcquireTokenByRefreshToken,tv.AcquireTokenSilent,tv.AcquireTokenSilentAsync,tv.AcquireTokenPopup,tv.AcquireTokenRedirect,tv.CryptoOptsGetPublicKeyThumbprint,tv.CryptoOptsSignJwt,tv.SilentCacheClientAcquireToken,tv.SilentIframeClientAcquireToken,tv.SilentRefreshClientAcquireToken,tv.SsoSilent,tv.StandardInteractionClientGetDiscoveredAuthority,tv.FetchAccountIdWithNativeBroker,tv.NativeInteractionClientAcquireToken,tv.BaseClientCreateTokenRequestHeaders,tv.NetworkClientSendPostRequestAsync,tv.RefreshTokenClientExecutePostToTokenEndpoint,tv.AuthorizationCodeClientExecutePostToTokenEndpoint,tv.BrokerHandhshake,tv.AcquireTokenByRefreshTokenInBroker,tv.AcquireTokenByBroker,tv.RefreshTokenClientExecuteTokenRequest,tv.RefreshTokenClientAcquireToken,tv.RefreshTokenClientAcquireTokenWithCachedRefreshToken,tv.RefreshTokenClientAcquireTokenByRefreshToken,tv.RefreshTokenClientCreateTokenRequestBody,tv.AcquireTokenFromCache,tv.SilentFlowClientAcquireCachedToken,tv.SilentFlowClientGenerateResultFromCacheRecord,tv.AcquireTokenBySilentIframe,tv.InitializeBaseRequest,tv.InitializeSilentRequest,tv.InitializeClientApplication,tv.SilentIframeClientTokenHelper,tv.SilentHandlerInitiateAuthRequest,tv.SilentHandlerMonitorIframeForHash,tv.SilentHandlerLoadFrame,tv.SilentHandlerLoadFrameSync,tv.StandardInteractionClientCreateAuthCodeClient,tv.StandardInteractionClientGetClientConfiguration,tv.StandardInteractionClientInitializeAuthorizationRequest,tv.StandardInteractionClientInitializeAuthorizationCodeRequest,tv.GetAuthCodeUrl,tv.HandleCodeResponseFromServer,tv.HandleCodeResponse,tv.UpdateTokenEndpointAuthority,tv.AuthClientAcquireToken,tv.AuthClientExecuteTokenRequest,tv.AuthClientCreateTokenRequestBody,tv.AuthClientCreateQueryString,tv.PopTokenGenerateCnf,tv.PopTokenGenerateKid,tv.HandleServerTokenResponse,tv.DeserializeResponse,tv.AuthorityFactoryCreateDiscoveredInstance,tv.AuthorityResolveEndpointsAsync,tv.AuthorityResolveEndpointsFromLocalSources,tv.AuthorityGetCloudDiscoveryMetadataFromNetwork,tv.AuthorityUpdateCloudDiscoveryMetadata,tv.AuthorityGetEndpointMetadataFromNetwork,tv.AuthorityUpdateEndpointMetadata,tv.AuthorityUpdateMetadataWithRegionalInformation,tv.RegionDiscoveryDetectRegion,tv.RegionDiscoveryGetRegionFromIMDS,tv.RegionDiscoveryGetCurrentVersion,tv.AcquireTokenByCodeAsync,tv.GetEndpointMetadataFromNetwork,tv.GetCloudDiscoveryMetadataFromNetworkMeasurement,tv.HandleRedirectPromiseMeasurement,tv.HandleNativeRedirectPromiseMeasurement,tv.UpdateCloudDiscoveryMetadataMeasurement,tv.UsernamePasswordClientAcquireToken,tv.NativeMessageHandlerHandshake,tv.NativeGenerateAuthResult,tv.RemoveHiddenIframe,tv.ClearTokensAndKeysWithClaims,tv.CacheManagerGetRefreshToken,tv.GeneratePkceCodes,tv.GenerateCodeVerifier,tv.GenerateCodeChallengeFromVerifier,tv.Sha256Digest,tv.GetRandomValues;class tA{startMeasurement(){}endMeasurement(){}flushMeasurement(){return null}}class tw{generateId(){return"callback-id"}startMeasurement(e,t){return{end:()=>null,discard:()=>{},add:()=>{},increment:()=>{},event:{eventId:this.generateId(),status:1,authority:"",libraryName:"",libraryVersion:"",clientId:"",name:e,startTimeMs:Date.now(),correlationId:t||""},measurement:new tA}}startPerformanceMeasurement(){return new tA}calculateQueuedTime(){return 0}addQueueMeasurement(){}setPreQueueTime(){}endMeasurement(){return null}discardMeasurements(){}removePerformanceCallback(){return!0}addPerformanceCallback(){return""}emitEvents(){}addFields(){}incrementFields(){}cacheEventByCorrelationId(){}}let tS={INVALID_GRANT_ERROR:"invalid_grant",POPUP_WIDTH:483,POPUP_HEIGHT:600,POPUP_NAME_PREFIX:"msal",DEFAULT_POLL_INTERVAL_MS:30,MSAL_SKU:"msal.js.browser"},tE={CHANNEL_ID:"53ee284d-920a-4b59-9d30-a60315b26836",PREFERRED_EXTENSION_ID:"ppnbnpeolgkicgegkbkbjmhlideopiji",MATS_TELEMETRY:"MATS"},tk={HandshakeRequest:"Handshake",HandshakeResponse:"HandshakeResponse",GetToken:"GetToken",Response:"Response"},tR={LocalStorage:"localStorage",SessionStorage:"sessionStorage",MemoryStorage:"memoryStorage"},t_={GET:"GET",POST:"POST"},tb={AUTHORITY:"authority",REQUEST_STATE:"request.state",NONCE_IDTOKEN:"nonce.id_token",ORIGIN_URI:"request.origin",URL_HASH:"urlHash",REQUEST_PARAMS:"request.params",INTERACTION_STATUS_KEY:"interaction.status",CCS_CREDENTIAL:"ccs.credential",CORRELATION_ID:"request.correlationId",NATIVE_REQUEST:"request.native"},tO={ACCOUNT_KEYS:"msal.account.keys",TOKEN_KEYS:"msal.token.keys"},tN="wrapper.sku",tP="wrapper.version",tM={acquireTokenRedirect:861,acquireTokenPopup:862,ssoSilent:863,handleRedirectPromise:865,acquireTokenByCode:866,acquireTokenSilent_silentFlow:61,logout:961,logoutPopup:962};(n=i||(i={})).Redirect="redirect",n.Popup="popup",n.Silent="silent",n.None="none";let tU={scopes:a.f_},tq="msal.db",tH=`${tq}.keys`,tL={Default:0,AccessToken:1,AccessTokenAndRefreshToken:2,RefreshToken:3,RefreshTokenAndNetwork:4,Skip:5},tD=[tL.Default,tL.Skip,tL.RefreshTokenAndNetwork];class tF{navigateInternal(e,t){return tF.defaultNavigateWindow(e,t)}navigateExternal(e,t){return tF.defaultNavigateWindow(e,t)}static defaultNavigateWindow(e,t){return t.noHistory?window.location.replace(e):window.location.assign(e),new Promise(e=>{setTimeout(()=>{e(!0)},t.timeout)})}}class tx extends u{constructor(e,t,r){super(e.errorCode,e.errorMessage,e.subError),Object.setPrototypeOf(this,tx.prototype),this.name="NetworkError",this.error=e,this.httpStatus=t,this.responseHeaders=r}}let tB="pkce_not_created",tK="crypto_nonexistent",tG="empty_navigate_uri",tz="hash_empty_error",t$="no_state_in_hash",tW="hash_does_not_contain_known_properties",tV="unable_to_parse_state",tQ="state_interaction_type_mismatch",tj="interaction_in_progress",tY="popup_window_error",tJ="empty_window_error",tX="user_cancelled",tZ="monitor_popup_timeout",t0="monitor_window_timeout",t1="redirect_in_iframe",t3="block_iframe_reload",t2="block_nested_popups",t6="iframe_closed_prematurely",t4="silent_logout_unsupported",t5="no_account_error",t9="silent_prompt_value_error",t8="no_token_request_cache_error",t7="unable_to_parse_token_request_cache_error",re="no_cached_authority_error",rt="auth_request_not_set_error",rr="invalid_cache_type",rn="non_browser_environment",ri="database_not_open",ro="no_network_connectivity",ra="post_request_failed",rs="get_request_failed",rc="failed_to_parse_response",rl="unable_to_load_token",rd="crypto_key_not_found",rh="auth_code_required",ru="auth_code_or_nativeAccountId_required",rg="spa_code_and_nativeAccountId_present",rp="database_unavailable",rm="unable_to_acquire_token_from_native_platform",rf="native_handshake_timeout",rC="native_extension_not_installed",ry="native_connection_not_established",rT="uninitialized_public_client_application",rI="native_prompt_not_supported",rv="invalid_base64_string",rA="invalid_pop_token_request",rw="failed_to_build_headers",rS="failed_to_parse_headers",rE="For more visit: aka.ms/msaljs/browser-errors",rk={[tB]:"The PKCE code challenge and verifier could not be generated.",[tK]:"The crypto object or function is not available.",[tG]:"Navigation URI is empty. Please check stack trace for more info.",[tz]:`Hash value cannot be processed because it is empty. Please verify that your redirectUri is not clearing the hash. ${rE}`,[t$]:"Hash does not contain state. Please verify that the request originated from msal.",[tW]:`Hash does not contain known properites. Please verify that your redirectUri is not changing the hash.  ${rE}`,[tV]:"Unable to parse state. Please verify that the request originated from msal.",[tQ]:"Hash contains state but the interaction type does not match the caller.",[tj]:`Interaction is currently in progress. Please ensure that this interaction has been completed before calling an interactive API.   ${rE}`,[tY]:"Error opening popup window. This can happen if you are using IE or if popups are blocked in the browser.",[tJ]:"window.open returned null or undefined window object.",[tX]:"User cancelled the flow.",[tZ]:`Token acquisition in popup failed due to timeout.  ${rE}`,[t0]:`Token acquisition in iframe failed due to timeout.  ${rE}`,[t1]:"Redirects are not supported for iframed or brokered applications. Please ensure you are using MSAL.js in a top frame of the window if using the redirect APIs, or use the popup APIs.",[t3]:`Request was blocked inside an iframe because MSAL detected an authentication response.  ${rE}`,[t2]:"Request was blocked inside a popup because MSAL detected it was running in a popup.",[t6]:"The iframe being monitored was closed prematurely.",[t4]:"Silent logout not supported. Please call logoutRedirect or logoutPopup instead.",[t5]:"No account object provided to acquireTokenSilent and no active account has been set. Please call setActiveAccount or provide an account on the request.",[t9]:"The value given for the prompt value is not valid for silent requests - must be set to 'none' or 'no_session'.",[t8]:"No token request found in cache.",[t7]:"The cached token request could not be parsed.",[re]:"No cached authority found.",[rt]:"Auth Request not set. Please ensure initiateAuthRequest was called from the InteractionHandler",[rr]:"Invalid cache type",[rn]:"Login and token requests are not supported in non-browser environments.",[ri]:"Database is not open!",[ro]:"No network connectivity. Check your internet connection.",[ra]:"Network request failed: If the browser threw a CORS error, check that the redirectUri is registered in the Azure App Portal as type 'SPA'",[rs]:"Network request failed. Please check the network trace to determine root cause.",[rc]:"Failed to parse network response. Check network trace.",[rl]:"Error loading token to cache.",[rd]:"Cryptographic Key or Keypair not found in browser storage.",[rh]:"An authorization code must be provided (as the `code` property on the request) to this flow.",[ru]:"An authorization code or nativeAccountId must be provided to this flow.",[rg]:"Request cannot contain both spa code and native account id.",[rp]:"IndexedDB, which is required for persistent cryptographic key storage, is unavailable. This may be caused by browser privacy features which block persistent storage in third-party contexts.",[rm]:`Unable to acquire token from native platform.  ${rE}`,[rf]:"Timed out while attempting to establish connection to browser extension",[rC]:"Native extension is not installed. If you think this is a mistake call the initialize function.",[ry]:`Connection to native platform has not been established. Please install a compatible browser extension and run initialize().  ${rE}`,[rT]:`You must call and await the initialize function before attempting to call any other MSAL API.  ${rE}`,[rI]:"The provided prompt is not supported by the native platform. This request should be routed to the web based flow.",[rv]:"Invalid base64 encoded string.",[rA]:"Invalid PoP token request. The request should not have both a popKid value and signPopToken set to true.",[rw]:"Failed to build request headers object.",[rS]:"Failed to parse response headers"};rk[tB],rk[tK],rk[tG],rk[tz],rk[t$],rk[tW],rk[tV],rk[tQ],rk[tj],rk[tY],rk[tJ],rk[tX],rk[tZ],rk[t0],rk[t1],rk[t3],rk[t2],rk[t6],rk[t4],rk[t5],rk[t9],rk[t8],rk[t7],rk[re],rk[rt],rk[rr],rk[rn],rk[ri],rk[ro],rk[ra],rk[rs],rk[rc],rk[rl],rk[rd],rk[rh],rk[ru],rk[rg],rk[rp],rk[rm],rk[rf],rk[rC],rk[ry],rk[rT],rk[rI],rk[rv],rk[rA];class rR extends u{constructor(e,t){super(e,rk[e],t),Object.setPrototypeOf(this,rR.prototype),this.name="BrowserAuthError"}}function r_(e,t){return new rR(e,t)}class rb{async sendGetRequestAsync(e,t){let r;let n={},i=0,o=rO(t);try{r=await fetch(e,{method:t_.GET,headers:o})}catch(e){throw r_(window.navigator.onLine?rs:ro)}n=rN(r.headers);try{return i=r.status,{headers:n,body:await r.json(),status:i}}catch(e){throw new tx(r_(rc),i,n)}}async sendPostRequestAsync(e,t){let r;let n=t&&t.body||"",i=rO(t),o=0,a={};try{r=await fetch(e,{method:t_.POST,headers:i,body:n})}catch(e){throw r_(window.navigator.onLine?ra:ro)}a=rN(r.headers);try{return o=r.status,{headers:a,body:await r.json(),status:o}}catch(e){throw new tx(r_(rc),o,a)}}}function rO(e){try{let t=new Headers;if(!(e&&e.headers))return t;let r=e.headers;return Object.entries(r).forEach(([e,r])=>{t.append(e,r)}),t}catch(e){throw r_(rw)}}function rN(e){try{let t={};return e.forEach((e,r)=>{t[r]=e}),t}catch(e){throw r_(rS)}}function rP(e){return encodeURIComponent(rU(e).replace(/=/g,"").replace(/\+/g,"-").replace(/\//g,"_"))}function rM(e){return rq(e).replace(/=/g,"").replace(/\+/g,"-").replace(/\//g,"_")}function rU(e){return rq(new TextEncoder().encode(e))}function rq(e){return btoa(Array.from(e,e=>String.fromCodePoint(e)).join(""))}let rH="SHA-256",rL=new Uint8Array([1,0,1]),rD="0123456789abcdef",rF=new Uint32Array(1),rx={name:"RSASSA-PKCS1-v1_5",hash:rH,modulusLength:2048,publicExponent:rL};async function rB(e,t,r){t?.addQueueMeasurement(tv.Sha256Digest,r);let n=new TextEncoder().encode(e);return window.crypto.subtle.digest(rH,n)}function rK(e){return window.crypto.getRandomValues(e)}function rG(){return window.crypto.getRandomValues(rF),rF[0]}function rz(){let e=Date.now(),t=1024*rG()+(1023&rG()),r=new Uint8Array(16),n=Math.trunc(t/1073741824),i=t&1073741824-1,o=rG();r[0]=e/1099511627776,r[1]=e/4294967296,r[2]=e/16777216,r[3]=e/65536,r[4]=e/256,r[5]=e,r[6]=112|n>>>8,r[7]=n,r[8]=128|i>>>24,r[9]=i>>>16,r[10]=i>>>8,r[11]=i,r[12]=o>>>24,r[13]=o>>>16,r[14]=o>>>8,r[15]=o;let a="";for(let e=0;e<r.length;e++)a+=rD.charAt(r[e]>>>4)+rD.charAt(15&r[e]),(3===e||5===e||7===e||9===e)&&(a+="-");return a}async function r$(e,t){return window.crypto.subtle.generateKey(rx,e,t)}async function rW(e){return window.crypto.subtle.exportKey("jwk",e)}async function rV(e,t,r){return window.crypto.subtle.importKey("jwk",e,rx,t,r)}async function rQ(e,t){return window.crypto.subtle.sign(rx,e,t)}async function rj(e){return rM(new Uint8Array(await rB(e)))}let rY="storage_not_supported",rJ="stubbed_public_client_application_called",rX="in_mem_redirect_unavailable",rZ={[rY]:"Given storage configuration option was not supported.",[rJ]:"Stub instance of Public Client Application was called. If using msal-react, please ensure context is not used without a provider. For more visit: aka.ms/msaljs/browser-errors",[rX]:"Redirect cannot be supported. In-memory storage was selected and storeAuthStateInCookie=false, which would cause the library to be unable to handle the incoming hash. If you would like to use the redirect API, please use session/localStorage or set storeAuthStateInCookie=true."};rZ[rY],rZ[rJ],rZ[rX];class r0 extends u{constructor(e,t){super(e,t),this.name="BrowserConfigurationAuthError",Object.setPrototypeOf(this,r0.prototype)}}function r1(e){return new r0(e,rZ[e])}function r3(){return window.parent!==window}function r2(){return"undefined"!=typeof window&&window.location?window.location.href.split("?")[0].split("#")[0]:""}function r6(){if("undefined"==typeof window)throw r_(rn)}function r4(e){if(!e)throw r_(rT)}function r5(e){r6(),function(){if(e7.hashContainsKnownProperties(window.location.hash)&&r3())throw r_(t3)}(),function(){if("undefined"!=typeof window&&window.opener&&window.opener!==window&&"string"==typeof window.name&&0===window.name.indexOf(`${tS.POPUP_NAME_PREFIX}.`))throw r_(t2)}(),r4(e)}function r9(e,t){if(r5(e),!function(e){if(r3()&&!e)throw r_(t1)}(t.system.allowRedirectInIframe),t.cache.cacheLocation===tR.MemoryStorage&&!t.cache.storeAuthStateInCookie)throw r1(rX)}function r8(e){let t=document.createElement("link");t.rel="preconnect",t.href=new URL(e).origin,t.crossOrigin="anonymous",document.head.appendChild(t),window.setTimeout(()=>{try{document.head.removeChild(t)}catch{}},1e4)}let r7="3.28.1";class ne{static loggerCallback(e,t){switch(e){case o.i.Error:console.error(t);return;case o.i.Info:console.info(t);return;case o.i.Verbose:console.debug(t);return;case o.i.Warning:console.warn(t);return;default:console.log(t);return}}constructor(e){let t;this.browserEnvironment="undefined"!=typeof window,this.config=function({auth:e,cache:t,system:r,telemetry:n},i){let l={clientId:a.gT.EMPTY_STRING,authority:`${a.gT.DEFAULT_AUTHORITY}`,knownAuthorities:[],cloudDiscoveryMetadata:a.gT.EMPTY_STRING,authorityMetadata:a.gT.EMPTY_STRING,redirectUri:"undefined"!=typeof window?r2():"",postLogoutRedirectUri:a.gT.EMPTY_STRING,navigateToLoginRequestUrl:!0,clientCapabilities:[],protocolMode:s.AAD,OIDCOptions:{serverResponseType:a.rg.FRAGMENT,defaultScopes:[a.gT.OPENID_SCOPE,a.gT.PROFILE_SCOPE,a.gT.OFFLINE_ACCESS_SCOPE]},azureCloudOptions:{azureCloudInstance:c.None,tenant:a.gT.EMPTY_STRING},skipAuthorityMetadataCache:!1,supportsNestedAppAuth:!1,instanceAware:!1},d={cacheLocation:tR.SessionStorage,temporaryCacheLocation:tR.SessionStorage,storeAuthStateInCookie:!1,secureCookies:!1,cacheMigrationEnabled:!!t&&t.cacheLocation===tR.LocalStorage,claimsBasedCachingEnabled:!1},h={loggerCallback:()=>{},logLevel:o.i.Info,piiLoggingEnabled:!1},u={...{...th,loggerOptions:h,networkClient:i?new rb:tI,navigationClient:new tF,loadFrameTimeout:0,windowHashTimeout:r?.loadFrameTimeout||6e4,iframeHashTimeout:r?.loadFrameTimeout||1e4,navigateFrameWait:0,redirectNavigationTimeout:3e4,asyncPopups:!1,allowRedirectInIframe:!1,allowNativeBroker:!1,nativeBrokerHandshakeTimeout:r?.nativeBrokerHandshakeTimeout||2e3,pollIntervalMilliseconds:tS.DEFAULT_POLL_INTERVAL_MS},...r,loggerOptions:r?.loggerOptions||h},g={application:{appName:a.gT.EMPTY_STRING,appVersion:a.gT.EMPTY_STRING},client:new tw};if(e?.protocolMode!==s.OIDC&&e?.OIDCOptions&&new o.Y(u.loggerOptions).warning(JSON.stringify(eY(e$))),e?.protocolMode&&e.protocolMode!==s.AAD&&u?.allowNativeBroker)throw eY(eW);return{auth:{...l,...e,OIDCOptions:{...l.OIDCOptions,...e?.OIDCOptions}},cache:{...d,...t},system:u,telemetry:{...g,...n}}}(e,this.browserEnvironment);try{t=window[tR.SessionStorage]}catch(e){}let r=t?.getItem("msal.browser.log.level"),n=t?.getItem("msal.browser.log.pii")?.toLowerCase(),i="true"===n||"false"!==n&&void 0,l={...this.config.system.loggerOptions},d=r&&Object.keys(o.i).includes(r)?o.i[r]:void 0;d&&(l.loggerCallback=ne.loggerCallback,l.logLevel=d),void 0!==i&&(l.piiLoggingEnabled=i),this.logger=new o.Y(l,"@azure/msal-browser",r7),this.available=!1}getConfig(){return this.config}getLogger(){return this.logger}isAvailable(){return this.available}isBrowserEnvironment(){return this.browserEnvironment}}class nt extends ne{getModuleName(){return nt.MODULE_NAME}getId(){return nt.ID}async initialize(){return this.available="undefined"!=typeof window,this.available}}nt.MODULE_NAME="",nt.ID="StandardOperatingContext";let nr="missing_kid_error",nn="missing_alg_error",ni={[nr]:"The JOSE Header for the requested JWT, JWS or JWK object requires a keyId to be configured as the 'kid' header claim. No 'kid' value was provided.",[nn]:"The JOSE Header for the requested JWT, JWS or JWK object requires an algorithm to be specified as the 'alg' header claim. No 'alg' value was provided."};class no extends u{constructor(e,t){super(e,t),this.name="JoseHeaderError",Object.setPrototypeOf(this,no.prototype)}}function na(e){return new no(e,ni[e])}class ns{constructor(e){this.typ=e.typ,this.alg=e.alg,this.kid=e.kid}static getShrHeaderString(e){if(!e.kid)throw na(nr);if(!e.alg)throw na(nn);return JSON.stringify(new ns({typ:e.typ||a.xd.Pop,kid:e.kid,alg:e.alg}))}}function nc(e){return new TextDecoder().decode(function(e){let t=e.replace(/-/g,"+").replace(/_/g,"/");switch(t.length%4){case 0:break;case 2:t+="==";break;case 3:t+="=";break;default:throw r_(rv)}let r=atob(t);return Uint8Array.from(r,e=>e.codePointAt(0)||0)}(e))}class nl{constructor(){this.dbName=tq,this.version=1,this.tableName=tH,this.dbOpen=!1}async open(){return new Promise((e,t)=>{let r=window.indexedDB.open(this.dbName,this.version);r.addEventListener("upgradeneeded",e=>{e.target.result.createObjectStore(this.tableName)}),r.addEventListener("success",t=>{this.db=t.target.result,this.dbOpen=!0,e()}),r.addEventListener("error",()=>t(r_(rp)))})}closeConnection(){let e=this.db;e&&this.dbOpen&&(e.close(),this.dbOpen=!1)}async validateDbIsOpen(){if(!this.dbOpen)return this.open()}async getItem(e){return await this.validateDbIsOpen(),new Promise((t,r)=>{if(!this.db)return r(r_(ri));let n=this.db.transaction([this.tableName],"readonly").objectStore(this.tableName).get(e);n.addEventListener("success",e=>{this.closeConnection(),t(e.target.result)}),n.addEventListener("error",e=>{this.closeConnection(),r(e)})})}async setItem(e,t){return await this.validateDbIsOpen(),new Promise((r,n)=>{if(!this.db)return n(r_(ri));let i=this.db.transaction([this.tableName],"readwrite").objectStore(this.tableName).put(t,e);i.addEventListener("success",()=>{this.closeConnection(),r()}),i.addEventListener("error",e=>{this.closeConnection(),n(e)})})}async removeItem(e){return await this.validateDbIsOpen(),new Promise((t,r)=>{if(!this.db)return r(r_(ri));let n=this.db.transaction([this.tableName],"readwrite").objectStore(this.tableName).delete(e);n.addEventListener("success",()=>{this.closeConnection(),t()}),n.addEventListener("error",e=>{this.closeConnection(),r(e)})})}async getKeys(){return await this.validateDbIsOpen(),new Promise((e,t)=>{if(!this.db)return t(r_(ri));let r=this.db.transaction([this.tableName],"readonly").objectStore(this.tableName).getAllKeys();r.addEventListener("success",t=>{this.closeConnection(),e(t.target.result)}),r.addEventListener("error",e=>{this.closeConnection(),t(e)})})}async containsKey(e){return await this.validateDbIsOpen(),new Promise((t,r)=>{if(!this.db)return r(r_(ri));let n=this.db.transaction([this.tableName],"readonly").objectStore(this.tableName).count(e);n.addEventListener("success",e=>{this.closeConnection(),t(1===e.target.result)}),n.addEventListener("error",e=>{this.closeConnection(),r(e)})})}async deleteDatabase(){return this.db&&this.dbOpen&&this.closeConnection(),new Promise((e,t)=>{let r=window.indexedDB.deleteDatabase(tq),n=setTimeout(()=>t(!1),200);r.addEventListener("success",()=>(clearTimeout(n),e(!0))),r.addEventListener("blocked",()=>(clearTimeout(n),e(!0))),r.addEventListener("error",()=>(clearTimeout(n),t(!1)))})}}class nd{constructor(){this.cache=new Map}getItem(e){return this.cache.get(e)||null}setItem(e,t){this.cache.set(e,t)}removeItem(e){this.cache.delete(e)}getKeys(){let e=[];return this.cache.forEach((t,r)=>{e.push(r)}),e}containsKey(e){return this.cache.has(e)}clear(){this.cache.clear()}}class nh{constructor(e){this.inMemoryCache=new nd,this.indexedDBCache=new nl,this.logger=e}handleDatabaseAccessError(e){if(e instanceof rR&&e.errorCode===rp)this.logger.error("Could not access persistent storage. This may be caused by browser privacy features which block persistent storage in third-party contexts.");else throw e}async getItem(e){let t=this.inMemoryCache.getItem(e);if(!t)try{return this.logger.verbose("Queried item not found in in-memory cache, now querying persistent storage."),await this.indexedDBCache.getItem(e)}catch(e){this.handleDatabaseAccessError(e)}return t}async setItem(e,t){this.inMemoryCache.setItem(e,t);try{await this.indexedDBCache.setItem(e,t)}catch(e){this.handleDatabaseAccessError(e)}}async removeItem(e){this.inMemoryCache.removeItem(e);try{await this.indexedDBCache.removeItem(e)}catch(e){this.handleDatabaseAccessError(e)}}async getKeys(){let e=this.inMemoryCache.getKeys();if(0===e.length)try{return this.logger.verbose("In-memory cache is empty, now querying persistent storage."),await this.indexedDBCache.getKeys()}catch(e){this.handleDatabaseAccessError(e)}return e}async containsKey(e){let t=this.inMemoryCache.containsKey(e);if(!t)try{return this.logger.verbose("Key not found in in-memory cache, now querying persistent storage."),await this.indexedDBCache.containsKey(e)}catch(e){this.handleDatabaseAccessError(e)}return t}clearInMemory(){this.logger.verbose("Deleting in-memory keystore"),this.inMemoryCache.clear(),this.logger.verbose("In-memory keystore deleted")}async clearPersistent(){try{this.logger.verbose("Deleting persistent keystore");let e=await this.indexedDBCache.deleteDatabase();return e&&this.logger.verbose("Persistent keystore deleted"),e}catch(e){return this.handleDatabaseAccessError(e),!1}}}class nu{constructor(e,t,r){this.logger=e,function(e){if(!window)throw r_(rn);if(!window.crypto)throw r_(tK);if(!e&&!window.crypto.subtle)throw r_(tK,"crypto_subtle_undefined")}(r??!1),this.cache=new nh(this.logger),this.performanceClient=t}createNewGuid(){return rz()}base64Encode(e){return rU(e)}base64Decode(e){return nc(e)}base64UrlEncode(e){return rP(e)}encodeKid(e){return this.base64UrlEncode(JSON.stringify({kid:e}))}async getPublicKeyThumbprint(e){let t=this.performanceClient?.startMeasurement(tv.CryptoOptsGetPublicKeyThumbprint,e.correlationId),r=await r$(nu.EXTRACTABLE,nu.POP_KEY_USAGES),n=await rW(r.publicKey),i=ng({e:n.e,kty:n.kty,n:n.n}),o=await this.hashString(i),a=await rW(r.privateKey),s=await rV(a,!1,["sign"]);return await this.cache.setItem(o,{privateKey:s,publicKey:r.publicKey,requestMethod:e.resourceRequestMethod,requestUri:e.resourceRequestUri}),t&&t.end({success:!0}),o}async removeTokenBindingKey(e){return await this.cache.removeItem(e),!await this.cache.containsKey(e)}async clearKeystore(){this.cache.clearInMemory();try{return await this.cache.clearPersistent(),!0}catch(e){return e instanceof Error?this.logger.error(`Clearing keystore failed with error: ${e.message}`):this.logger.error("Clearing keystore failed with unknown error"),!1}}async signJwt(e,t,r,n){let i=this.performanceClient?.startMeasurement(tv.CryptoOptsSignJwt,n),o=await this.cache.getItem(t);if(!o)throw r_(rd);let a=await rW(o.publicKey),s=ng(a),c=rP(JSON.stringify({kid:t})),l=rP(ns.getShrHeaderString({...r?.header,alg:a.alg,kid:c}));e.cnf={jwk:JSON.parse(s)};let d=rP(JSON.stringify(e)),h=`${l}.${d}`,u=new TextEncoder().encode(h),g=rM(new Uint8Array(await rQ(o.privateKey,u))),p=`${h}.${g}`;return i&&i.end({success:!0}),p}async hashString(e){return rj(e)}}function ng(e){return JSON.stringify(e,Object.keys(e).sort())}nu.POP_KEY_USAGES=["sign","verify"],nu.EXTRACTABLE=!0;let np=(e,t,r,n,i)=>(...o)=>{r.trace(`Executing function ${t}`);let a=n?.startMeasurement(t,i);i&&n?.incrementFields({[t+"CallCount"]:1},i);try{let n=e(...o);return a?.end({success:!0}),r.trace(`Returning result from ${t}`),n}catch(e){r.trace(`Error occurred in ${t}`);try{r.trace(JSON.stringify(e))}catch(e){r.trace("Unable to print error message.")}throw a?.end({success:!1},e),e}},nm=(e,t,r,n,i)=>(...o)=>{r.trace(`Executing function ${t}`);let a=n?.startMeasurement(t,i);return i&&n?.incrementFields({[t+"CallCount"]:1},i),n?.setPreQueueTime(t,i),e(...o).then(e=>(r.trace(`Returning result from ${t}`),a?.end({success:!0}),e)).catch(e=>{r.trace(`Error occurred in ${t}`);try{r.trace(JSON.stringify(e))}catch(e){r.trace("Unable to print error message.")}throw a?.end({success:!1},e),e})};class nf{constructor(e,t,r,n){this.networkInterface=e,this.logger=t,this.performanceClient=r,this.correlationId=n}async detectRegion(e,t){this.performanceClient?.addQueueMeasurement(tv.RegionDiscoveryDetectRegion,this.correlationId);let r=e;if(r)t.region_source=a.W.ENVIRONMENT_VARIABLE;else{let e=nf.IMDS_OPTIONS;try{let n=await nm(this.getRegionFromIMDS.bind(this),tv.RegionDiscoveryGetRegionFromIMDS,this.logger,this.performanceClient,this.correlationId)(a.gT.IMDS_VERSION,e);if(n.status===a.YY.httpSuccess&&(r=n.body,t.region_source=a.W.IMDS),n.status===a.YY.httpBadRequest){let n=await nm(this.getCurrentVersion.bind(this),tv.RegionDiscoveryGetCurrentVersion,this.logger,this.performanceClient,this.correlationId)(e);if(!n)return t.region_source=a.W.FAILED_AUTO_DETECTION,null;let i=await nm(this.getRegionFromIMDS.bind(this),tv.RegionDiscoveryGetRegionFromIMDS,this.logger,this.performanceClient,this.correlationId)(n,e);i.status===a.YY.httpSuccess&&(r=i.body,t.region_source=a.W.IMDS)}}catch(e){return t.region_source=a.W.FAILED_AUTO_DETECTION,null}}return r||(t.region_source=a.W.FAILED_AUTO_DETECTION),r||null}async getRegionFromIMDS(e,t){return this.performanceClient?.addQueueMeasurement(tv.RegionDiscoveryGetRegionFromIMDS,this.correlationId),this.networkInterface.sendGetRequestAsync(`${a.gT.IMDS_ENDPOINT}?api-version=${e}&format=text`,t,a.gT.IMDS_TIMEOUT)}async getCurrentVersion(e){this.performanceClient?.addQueueMeasurement(tv.RegionDiscoveryGetCurrentVersion,this.correlationId);try{let t=await this.networkInterface.sendGetRequestAsync(`${a.gT.IMDS_ENDPOINT}?format=json`,e);if(t.status===a.YY.httpBadRequest&&t.body&&t.body["newest-versions"]&&t.body["newest-versions"].length>0)return t.body["newest-versions"][0];return null}catch(e){return null}}}nf.IMDS_OPTIONS={headers:{Metadata:"true"}};class nC{constructor(e,t,r,n,i,o,a,s){this.canonicalAuthority=e,this._canonicalAuthority.validateAsUri(),this.networkInterface=t,this.cacheManager=r,this.authorityOptions=n,this.regionDiscoveryMetadata={region_used:void 0,region_source:void 0,region_outcome:void 0},this.logger=i,this.performanceClient=a,this.correlationId=o,this.managedIdentity=s||!1,this.regionDiscovery=new nf(t,this.logger,this.performanceClient,this.correlationId)}getAuthorityType(e){if(e.HostNameAndPort.endsWith(a.gT.CIAM_AUTH_URL))return e6.Ciam;let t=e.PathSegments;if(t.length)switch(t[0].toLowerCase()){case a.gT.ADFS:return e6.Adfs;case a.gT.DSTS:return e6.Dsts}return e6.Default}get authorityType(){return this.getAuthorityType(this.canonicalAuthorityUrlComponents)}get protocolMode(){return this.authorityOptions.protocolMode}get options(){return this.authorityOptions}get canonicalAuthority(){return this._canonicalAuthority.urlString}set canonicalAuthority(e){this._canonicalAuthority=new e7(e),this._canonicalAuthority.validateAsUri(),this._canonicalAuthorityUrlComponents=null}get canonicalAuthorityUrlComponents(){return this._canonicalAuthorityUrlComponents||(this._canonicalAuthorityUrlComponents=this._canonicalAuthority.getUrlComponents()),this._canonicalAuthorityUrlComponents}get hostnameAndPort(){return this.canonicalAuthorityUrlComponents.HostNameAndPort.toLowerCase()}get tenant(){return this.canonicalAuthorityUrlComponents.PathSegments[0]}get authorizationEndpoint(){if(this.discoveryComplete())return this.replacePath(this.metadata.authorization_endpoint);throw ea(y)}get tokenEndpoint(){if(this.discoveryComplete())return this.replacePath(this.metadata.token_endpoint);throw ea(y)}get deviceCodeEndpoint(){if(this.discoveryComplete())return this.replacePath(this.metadata.token_endpoint.replace("/token","/devicecode"));throw ea(y)}get endSessionEndpoint(){if(this.discoveryComplete()){if(!this.metadata.end_session_endpoint)throw ea(J);return this.replacePath(this.metadata.end_session_endpoint)}throw ea(y)}get selfSignedJwtAudience(){if(this.discoveryComplete())return this.replacePath(this.metadata.issuer);throw ea(y)}get jwksUri(){if(this.discoveryComplete())return this.replacePath(this.metadata.jwks_uri);throw ea(y)}canReplaceTenant(e){return 1===e.PathSegments.length&&!nC.reservedTenantDomains.has(e.PathSegments[0])&&this.getAuthorityType(e)===e6.Default&&this.protocolMode===s.AAD}replaceTenant(e){return e.replace(/{tenant}|{tenantid}/g,this.tenant)}replacePath(e){let t=e,r=new e7(this.metadata.canonical_authority).getUrlComponents(),n=r.PathSegments;return this.canonicalAuthorityUrlComponents.PathSegments.forEach((e,i)=>{let o=n[i];if(0===i&&this.canReplaceTenant(r)){let e=new e7(this.metadata.authorization_endpoint).getUrlComponents().PathSegments[0];o!==e&&(this.logger.verbose(`Replacing tenant domain name ${o} with id ${e}`),o=e)}e!==o&&(t=t.replace(`/${o}/`,`/${e}/`))}),this.replaceTenant(t)}get defaultOpenIdConfigurationEndpoint(){let e=this.hostnameAndPort;return this.canonicalAuthority.endsWith("v2.0/")||this.authorityType===e6.Adfs||this.protocolMode!==s.AAD&&!this.isAliasOfKnownMicrosoftAuthority(e)?`${this.canonicalAuthority}.well-known/openid-configuration`:`${this.canonicalAuthority}v2.0/.well-known/openid-configuration`}discoveryComplete(){return!!this.metadata}async resolveEndpointsAsync(){this.performanceClient?.addQueueMeasurement(tv.AuthorityResolveEndpointsAsync,this.correlationId);let e=this.getCurrentMetadataEntity(),t=await nm(this.updateCloudDiscoveryMetadata.bind(this),tv.AuthorityUpdateCloudDiscoveryMetadata,this.logger,this.performanceClient,this.correlationId)(e);this.canonicalAuthority=this.canonicalAuthority.replace(this.hostnameAndPort,e.preferred_network);let r=await nm(this.updateEndpointMetadata.bind(this),tv.AuthorityUpdateEndpointMetadata,this.logger,this.performanceClient,this.correlationId)(e);this.updateCachedMetadata(e,t,{source:r}),this.performanceClient?.addFields({cloudDiscoverySource:t,authorityEndpointSource:r},this.correlationId)}getCurrentMetadataEntity(){let e=this.cacheManager.getAuthorityMetadataByAlias(this.hostnameAndPort);return e||(e={aliases:[],preferred_cache:this.hostnameAndPort,preferred_network:this.hostnameAndPort,canonical_authority:this.canonicalAuthority,authorization_endpoint:"",token_endpoint:"",end_session_endpoint:"",issuer:"",aliasesFromNetwork:!1,endpointsFromNetwork:!1,expiresAt:eA(),jwks_uri:""}),e}updateCachedMetadata(e,t,r){t!==a.pQ.CACHE&&r?.source!==a.pQ.CACHE&&(e.expiresAt=eA(),e.canonical_authority=this.canonicalAuthority);let n=this.cacheManager.generateAuthorityMetadataCacheKey(e.preferred_cache);this.cacheManager.setAuthorityMetadata(n,e),this.metadata=e}async updateEndpointMetadata(e){this.performanceClient?.addQueueMeasurement(tv.AuthorityUpdateEndpointMetadata,this.correlationId);let t=this.updateEndpointMetadataFromLocalSources(e);if(t)return t.source===a.pQ.HARDCODED_VALUES&&this.authorityOptions.azureRegionConfiguration?.azureRegion&&t.metadata&&(ew(e,await nm(this.updateMetadataWithRegionalInformation.bind(this),tv.AuthorityUpdateMetadataWithRegionalInformation,this.logger,this.performanceClient,this.correlationId)(t.metadata),!1),e.canonical_authority=this.canonicalAuthority),t.source;let r=await nm(this.getEndpointMetadataFromNetwork.bind(this),tv.AuthorityGetEndpointMetadataFromNetwork,this.logger,this.performanceClient,this.correlationId)();if(r)return this.authorityOptions.azureRegionConfiguration?.azureRegion&&(r=await nm(this.updateMetadataWithRegionalInformation.bind(this),tv.AuthorityUpdateMetadataWithRegionalInformation,this.logger,this.performanceClient,this.correlationId)(r)),ew(e,r,!0),a.pQ.NETWORK;throw ea(I,this.defaultOpenIdConfigurationEndpoint)}updateEndpointMetadataFromLocalSources(e){this.logger.verbose("Attempting to get endpoint metadata from authority configuration");let t=this.getEndpointMetadataFromConfig();if(t)return this.logger.verbose("Found endpoint metadata in authority configuration"),ew(e,t,!1),{source:a.pQ.CONFIG};if(this.logger.verbose("Did not find endpoint metadata in the config... Attempting to get endpoint metadata from the hardcoded values."),this.authorityOptions.skipAuthorityMetadataCache)this.logger.verbose("Skipping hardcoded metadata cache since skipAuthorityMetadataCache is set to true. Attempting to get endpoint metadata from the network metadata cache.");else{let t=this.getEndpointMetadataFromHardcodedValues();if(t)return ew(e,t,!1),{source:a.pQ.HARDCODED_VALUES,metadata:t};this.logger.verbose("Did not find endpoint metadata in hardcoded values... Attempting to get endpoint metadata from the network metadata cache.")}let r=eE(e);return this.isAuthoritySameType(e)&&e.endpointsFromNetwork&&!r?(this.logger.verbose("Found endpoint metadata in the cache."),{source:a.pQ.CACHE}):(r&&this.logger.verbose("The metadata entity is expired."),null)}isAuthoritySameType(e){return new e7(e.canonical_authority).getUrlComponents().PathSegments.length===this.canonicalAuthorityUrlComponents.PathSegments.length}getEndpointMetadataFromConfig(){if(this.authorityOptions.authorityMetadata)try{return JSON.parse(this.authorityOptions.authorityMetadata)}catch(e){throw eY(eF)}return null}async getEndpointMetadataFromNetwork(){this.performanceClient?.addQueueMeasurement(tv.AuthorityGetEndpointMetadataFromNetwork,this.correlationId);let e=this.defaultOpenIdConfigurationEndpoint;this.logger.verbose(`Authority.getEndpointMetadataFromNetwork: attempting to retrieve OAuth endpoints from ${e}`);try{var t;let r=await this.networkInterface.sendGetRequestAsync(e,{});if((t=r.body).hasOwnProperty("authorization_endpoint")&&t.hasOwnProperty("token_endpoint")&&t.hasOwnProperty("issuer")&&t.hasOwnProperty("jwks_uri"))return r.body;return this.logger.verbose("Authority.getEndpointMetadataFromNetwork: could not parse response as OpenID configuration"),null}catch(e){return this.logger.verbose(`Authority.getEndpointMetadataFromNetwork: ${e}`),null}}getEndpointMetadataFromHardcodedValues(){return this.hostnameAndPort in te?te[this.hostnameAndPort]:null}async updateMetadataWithRegionalInformation(e){this.performanceClient?.addQueueMeasurement(tv.AuthorityUpdateMetadataWithRegionalInformation,this.correlationId);let t=this.authorityOptions.azureRegionConfiguration?.azureRegion;if(t){if(t!==a.gT.AZURE_REGION_AUTO_DISCOVER_FLAG)return this.regionDiscoveryMetadata.region_outcome=a.ki.CONFIGURED_NO_AUTO_DETECTION,this.regionDiscoveryMetadata.region_used=t,nC.replaceWithRegionalInformation(e,t);let r=await nm(this.regionDiscovery.detectRegion.bind(this.regionDiscovery),tv.RegionDiscoveryDetectRegion,this.logger,this.performanceClient,this.correlationId)(this.authorityOptions.azureRegionConfiguration?.environmentRegion,this.regionDiscoveryMetadata);if(r)return this.regionDiscoveryMetadata.region_outcome=a.ki.AUTO_DETECTION_REQUESTED_SUCCESSFUL,this.regionDiscoveryMetadata.region_used=r,nC.replaceWithRegionalInformation(e,r);this.regionDiscoveryMetadata.region_outcome=a.ki.AUTO_DETECTION_REQUESTED_FAILED}return e}async updateCloudDiscoveryMetadata(e){this.performanceClient?.addQueueMeasurement(tv.AuthorityUpdateCloudDiscoveryMetadata,this.correlationId);let t=this.updateCloudDiscoveryMetadataFromLocalSources(e);if(t)return t;let r=await nm(this.getCloudDiscoveryMetadataFromNetwork.bind(this),tv.AuthorityGetCloudDiscoveryMetadataFromNetwork,this.logger,this.performanceClient,this.correlationId)();if(r)return eS(e,r,!0),a.pQ.NETWORK;throw eY(ex)}updateCloudDiscoveryMetadataFromLocalSources(e){this.logger.verbose("Attempting to get cloud discovery metadata  from authority configuration"),this.logger.verbosePii(`Known Authorities: ${this.authorityOptions.knownAuthorities||a.gT.NOT_APPLICABLE}`),this.logger.verbosePii(`Authority Metadata: ${this.authorityOptions.authorityMetadata||a.gT.NOT_APPLICABLE}`),this.logger.verbosePii(`Canonical Authority: ${e.canonical_authority||a.gT.NOT_APPLICABLE}`);let t=this.getCloudDiscoveryMetadataFromConfig();if(t)return this.logger.verbose("Found cloud discovery metadata in authority configuration"),eS(e,t,!1),a.pQ.CONFIG;if(this.logger.verbose("Did not find cloud discovery metadata in the config... Attempting to get cloud discovery metadata from the hardcoded values."),this.options.skipAuthorityMetadataCache)this.logger.verbose("Skipping hardcoded cloud discovery metadata cache since skipAuthorityMetadataCache is set to true. Attempting to get cloud discovery metadata from the network metadata cache.");else{var r;let t=(r=this.hostnameAndPort,ti(tt.metadata,r));if(t)return this.logger.verbose("Found cloud discovery metadata from hardcoded values."),eS(e,t,!1),a.pQ.HARDCODED_VALUES;this.logger.verbose("Did not find cloud discovery metadata in hardcoded values... Attempting to get cloud discovery metadata from the network metadata cache.")}let n=eE(e);return this.isAuthoritySameType(e)&&e.aliasesFromNetwork&&!n?(this.logger.verbose("Found cloud discovery metadata in the cache."),a.pQ.CACHE):(n&&this.logger.verbose("The metadata entity is expired."),null)}getCloudDiscoveryMetadataFromConfig(){if(this.authorityType===e6.Ciam)return this.logger.verbose("CIAM authorities do not support cloud discovery metadata, generate the aliases from authority host."),nC.createCloudDiscoveryMetadataFromHost(this.hostnameAndPort);if(this.authorityOptions.cloudDiscoveryMetadata){this.logger.verbose("The cloud discovery metadata has been provided as a network response, in the config.");try{this.logger.verbose("Attempting to parse the cloud discovery metadata.");let e=JSON.parse(this.authorityOptions.cloudDiscoveryMetadata),t=ti(e.metadata,this.hostnameAndPort);if(this.logger.verbose("Parsed the cloud discovery metadata."),t)return this.logger.verbose("There is returnable metadata attached to the parsed cloud discovery metadata."),t;this.logger.verbose("There is no metadata attached to the parsed cloud discovery metadata.")}catch(e){throw this.logger.verbose("Unable to parse the cloud discovery metadata. Throwing Invalid Cloud Discovery Metadata Error."),eY(eD)}}return this.isInKnownAuthorities()?(this.logger.verbose("The host is included in knownAuthorities. Creating new cloud discovery metadata from the host."),nC.createCloudDiscoveryMetadataFromHost(this.hostnameAndPort)):null}async getCloudDiscoveryMetadataFromNetwork(){this.performanceClient?.addQueueMeasurement(tv.AuthorityGetCloudDiscoveryMetadataFromNetwork,this.correlationId);let e=`${a.gT.AAD_INSTANCE_DISCOVERY_ENDPT}${this.canonicalAuthority}oauth2/v2.0/authorize`,t=null;try{var r,n;let i,o;let s=await this.networkInterface.sendGetRequestAsync(e,{});if((r=s.body).hasOwnProperty("tenant_discovery_endpoint")&&r.hasOwnProperty("metadata"))o=(i=s.body).metadata,this.logger.verbosePii(`tenant_discovery_endpoint is: ${i.tenant_discovery_endpoint}`);else{if(!((n=s.body).hasOwnProperty("error")&&n.hasOwnProperty("error_description")))return this.logger.error("AAD did not return a CloudInstanceDiscoveryResponse or CloudInstanceDiscoveryErrorResponse"),null;if(this.logger.warning(`A CloudInstanceDiscoveryErrorResponse was returned. The cloud instance discovery network request's status code is: ${s.status}`),(i=s.body).error===a.gT.INVALID_INSTANCE)return this.logger.error("The CloudInstanceDiscoveryErrorResponse error is invalid_instance."),null;this.logger.warning(`The CloudInstanceDiscoveryErrorResponse error is ${i.error}`),this.logger.warning(`The CloudInstanceDiscoveryErrorResponse error description is ${i.error_description}`),this.logger.warning("Setting the value of the CloudInstanceDiscoveryMetadata (returned from the network) to []"),o=[]}this.logger.verbose("Attempting to find a match between the developer's authority and the CloudInstanceDiscoveryMetadata returned from the network request."),t=ti(o,this.hostnameAndPort)}catch(e){return e instanceof u?this.logger.error(`There was a network error while attempting to get the cloud discovery instance metadata.
Error: ${e.errorCode}
Error Description: ${e.errorMessage}`):this.logger.error(`A non-MSALJS error was thrown while attempting to get the cloud instance discovery metadata.
Error: ${e.name}
Error Description: ${e.message}`),null}return t||(this.logger.warning("The developer's authority was not found within the CloudInstanceDiscoveryMetadata returned from the network request."),this.logger.verbose("Creating custom Authority for custom domain scenario."),t=nC.createCloudDiscoveryMetadataFromHost(this.hostnameAndPort)),t}isInKnownAuthorities(){return this.authorityOptions.knownAuthorities.filter(e=>e&&e7.getDomainFromUrl(e).toLowerCase()===this.hostnameAndPort).length>0}static generateAuthority(e,t){let r;if(t&&t.azureCloudInstance!==c.None){let e=t.tenant?t.tenant:a.gT.DEFAULT_COMMON_TENANT;r=`${t.azureCloudInstance}/${e}/`}return r||e}static createCloudDiscoveryMetadataFromHost(e){return{preferred_network:e,preferred_cache:e,aliases:[e]}}getPreferredCache(){if(this.managedIdentity)return a.gT.DEFAULT_AUTHORITY_HOST;if(this.discoveryComplete())return this.metadata.preferred_cache;throw ea(y)}isAlias(e){return this.metadata.aliases.indexOf(e)>-1}isAliasOfKnownMicrosoftAuthority(e){return tr.has(e)}static isPublicCloudAuthority(e){return a.gT.KNOWN_PUBLIC_CLOUDS.indexOf(e)>=0}static buildRegionalAuthorityString(e,t,r){let n=new e7(e);n.validateAsUri();let i=n.getUrlComponents(),o=`${t}.${i.HostNameAndPort}`;this.isPublicCloudAuthority(i.HostNameAndPort)&&(o=`${t}.${a.gT.REGIONAL_AUTH_PUBLIC_CLOUD_SUFFIX}`);let s=e7.constructAuthorityUriFromObject({...n.getUrlComponents(),HostNameAndPort:o}).urlString;return r?`${s}?${r}`:s}static replaceWithRegionalInformation(e,t){let r={...e};return r.authorization_endpoint=nC.buildRegionalAuthorityString(r.authorization_endpoint,t),r.token_endpoint=nC.buildRegionalAuthorityString(r.token_endpoint,t),r.end_session_endpoint&&(r.end_session_endpoint=nC.buildRegionalAuthorityString(r.end_session_endpoint,t)),r}static transformCIAMAuthority(e){let t=e,r=new e7(e).getUrlComponents();if(0===r.PathSegments.length&&r.HostNameAndPort.endsWith(a.gT.CIAM_AUTH_URL)){let e=r.HostNameAndPort.split(".")[0];t=`${t}${e}${a.gT.AAD_TENANT_DOMAIN_SUFFIX}`}return t}}function ny(e){return e.endsWith(a.gT.FORWARD_SLASH)?e:`${e}${a.gT.FORWARD_SLASH}`}nC.reservedTenantDomains=new Set(["{tenant}","{tenantid}",a.Nb.COMMON,a.Nb.CONSUMERS,a.Nb.ORGANIZATIONS]);let nT="no_tokens_found",nI="native_account_unavailable",nv="refresh_token_expired",nA="bad_token",nw=["interaction_required","consent_required","login_required",nA],nS=["message_only","additional_action","basic_action","user_password_expired","consent_required","bad_token"],nE={[nT]:"No refresh token found in the cache. Please sign-in.",[nI]:"The requested account is not available in the native broker. It may have been deleted or logged out. Please sign-in again using an interactive API.",[nv]:"Refresh token has expired.",[nA]:"Identity provider returned bad_token due to an expired or invalid refresh token. Please invoke an interactive API to resolve."};nE[nT],nE[nI],nE[nA];class nk extends u{constructor(e,t,r,n,i,o,s,c){super(e,t,r),Object.setPrototypeOf(this,nk.prototype),this.timestamp=n||a.gT.EMPTY_STRING,this.traceId=i||a.gT.EMPTY_STRING,this.correlationId=o||a.gT.EMPTY_STRING,this.claims=s||a.gT.EMPTY_STRING,this.name="InteractionRequiredAuthError",this.errorNo=c}}function nR(e,t,r){let n=!!e&&nw.indexOf(e)>-1,i=!!r&&nS.indexOf(r)>-1,o=!!t&&nw.some(e=>t.indexOf(e)>-1);return n||o||i}function n_(e){return new nk(e,nE[e])}class nb{static setRequestState(e,t,r){let n=nb.generateLibraryState(e,r);return t?`${n}${a.gT.RESOURCE_DELIM}${t}`:n}static generateLibraryState(e,t){if(!e)throw ea(K);let r={id:e.createNewGuid()};t&&(r.meta=t);let n=JSON.stringify(r);return e.base64Encode(n)}static parseRequestState(e,t){if(!e)throw ea(K);if(!t)throw ea(A);try{let r=t.split(a.gT.RESOURCE_DELIM),n=r[0],i=r.length>1?r.slice(1).join(a.gT.RESOURCE_DELIM):a.gT.EMPTY_STRING,o=e.base64Decode(n),s=JSON.parse(o);return{userRequestState:i||a.gT.EMPTY_STRING,libraryState:s}}catch(e){throw ea(A)}}}let nO={HOME_ACCOUNT_ID:"home_account_id",UPN:"UPN"};class nN{constructor(){if(!window.localStorage)throw r1(rY)}getItem(e){return window.localStorage.getItem(e)}setItem(e,t){window.localStorage.setItem(e,t)}removeItem(e){window.localStorage.removeItem(e)}getKeys(){return Object.keys(window.localStorage)}containsKey(e){return window.localStorage.hasOwnProperty(e)}}class nP{constructor(){if(!window.sessionStorage)throw r1(rY)}getItem(e){return window.sessionStorage.getItem(e)}setItem(e,t){window.sessionStorage.setItem(e,t)}removeItem(e){window.sessionStorage.removeItem(e)}getKeys(){return Object.keys(window.sessionStorage)}containsKey(e){return window.sessionStorage.hasOwnProperty(e)}}function nM(e,t){if(!t)return null;try{return nb.parseRequestState(e,t).libraryState.meta}catch(e){throw ea(A)}}class nU{getItem(e){let t=`${encodeURIComponent(e)}`,r=document.cookie.split(";");for(let e=0;e<r.length;e++){let[n,...i]=decodeURIComponent(r[e]).trim().split("="),o=i.join("=");if(n===t)return o}return""}setItem(e,t,r,n=!0){let i=`${encodeURIComponent(e)}=${encodeURIComponent(t)};path=/;SameSite=Lax;`;if(r){let e=new Date(new Date().getTime()+864e5*r).toUTCString();i+=`expires=${e};`}n&&(i+="Secure;"),document.cookie=i}removeItem(e){this.setItem(e,"",-1)}getKeys(){let e=document.cookie.split(";"),t=[];return e.forEach(e=>{let r=decodeURIComponent(e).trim().split("=");t.push(r[0])}),t}containsKey(e){return this.getKeys().includes(e)}}class nq extends tl{constructor(e,t,r,n,i,o){super(e,r,n,i),this.cacheConfig=t,this.logger=n,this.internalStorage=new nd,this.browserStorage=this.setupBrowserStorage(this.cacheConfig.cacheLocation),this.temporaryCacheStorage=this.setupBrowserStorage(this.cacheConfig.temporaryCacheLocation),this.cookieStorage=new nU,t.cacheMigrationEnabled&&(this.migrateCacheEntries(),this.createKeyMaps()),this.performanceClient=o}setupBrowserStorage(e){try{switch(e){case tR.LocalStorage:return new nN;case tR.SessionStorage:return new nP;case tR.MemoryStorage:}}catch(e){this.logger.error(e)}return this.cacheConfig.cacheLocation=tR.MemoryStorage,new nd}migrateCacheEntries(){let e=`${a.gT.CACHE_PREFIX}.${a.Vi.ID_TOKEN}`,t=`${a.gT.CACHE_PREFIX}.${a.Vi.CLIENT_INFO}`,r=`${a.gT.CACHE_PREFIX}.${a.Vi.ERROR}`,n=`${a.gT.CACHE_PREFIX}.${a.Vi.ERROR_DESC}`,i=this.browserStorage.getItem(e),o=[i,this.browserStorage.getItem(t),this.browserStorage.getItem(r),this.browserStorage.getItem(n)];[a.Vi.ID_TOKEN,a.Vi.CLIENT_INFO,a.Vi.ERROR,a.Vi.ERROR_DESC].forEach((e,t)=>{let r=o[t];r&&this.setTemporaryCache(e,r,!0)})}createKeyMaps(){this.logger.trace("BrowserCacheManager - createKeyMaps called.");let e=this.getItem(tO.ACCOUNT_KEYS),t=this.getItem(`${tO.TOKEN_KEYS}.${this.clientId}`);if(e&&t){this.logger.verbose("BrowserCacheManager:createKeyMaps - account and token key maps already exist, skipping migration.");return}this.browserStorage.getKeys().forEach(e=>{if(this.isCredentialKey(e)){let t=this.getItem(e);if(t){let r=this.validateAndParseJson(t);if(r&&r.hasOwnProperty("credentialType"))switch(r.credentialType){case a.d3.ID_TOKEN:if(eI(r)){this.logger.trace("BrowserCacheManager:createKeyMaps - idToken found, saving key to token key map"),this.logger.tracePii(`BrowserCacheManager:createKeyMaps - idToken with key: ${e} found, saving key to token key map`);let t=this.updateCredentialCacheKey(e,r);this.addTokenKey(t,a.d3.ID_TOKEN);return}this.logger.trace("BrowserCacheManager:createKeyMaps - key found matching idToken schema with value containing idToken credentialType field but value failed IdTokenEntity validation, skipping."),this.logger.tracePii(`BrowserCacheManager:createKeyMaps - failed idToken validation on key: ${e}`);break;case a.d3.ACCESS_TOKEN:case a.d3.ACCESS_TOKEN_WITH_AUTH_SCHEME:if(eT(r)){this.logger.trace("BrowserCacheManager:createKeyMaps - accessToken found, saving key to token key map"),this.logger.tracePii(`BrowserCacheManager:createKeyMaps - accessToken with key: ${e} found, saving key to token key map`);let t=this.updateCredentialCacheKey(e,r);this.addTokenKey(t,a.d3.ACCESS_TOKEN);return}this.logger.trace("BrowserCacheManager:createKeyMaps - key found matching accessToken schema with value containing accessToken credentialType field but value failed AccessTokenEntity validation, skipping."),this.logger.tracePii(`BrowserCacheManager:createKeyMaps - failed accessToken validation on key: ${e}`);break;case a.d3.REFRESH_TOKEN:if(ev(r)){this.logger.trace("BrowserCacheManager:createKeyMaps - refreshToken found, saving key to token key map"),this.logger.tracePii(`BrowserCacheManager:createKeyMaps - refreshToken with key: ${e} found, saving key to token key map`);let t=this.updateCredentialCacheKey(e,r);this.addTokenKey(t,a.d3.REFRESH_TOKEN);return}this.logger.trace("BrowserCacheManager:createKeyMaps - key found matching refreshToken schema with value containing refreshToken credentialType field but value failed RefreshTokenEntity validation, skipping."),this.logger.tracePii(`BrowserCacheManager:createKeyMaps - failed refreshToken validation on key: ${e}`)}}}if(this.isAccountKey(e)){let t=this.getItem(e);if(t){let r=this.validateAndParseJson(t);r&&e5.isAccountEntity(r)&&(this.logger.trace("BrowserCacheManager:createKeyMaps - account found, saving key to account key map"),this.logger.tracePii(`BrowserCacheManager:createKeyMaps - account with key: ${e} found, saving key to account key map`),this.addAccountKeyToMap(e))}}})}validateAndParseJson(e){try{let t=JSON.parse(e);return t&&"object"==typeof t?t:null}catch(e){return null}}getItem(e){return this.browserStorage.getItem(e)}setItem(e,t){this.browserStorage.setItem(e,t)}getAccount(e,t){this.logger.trace("BrowserCacheManager.getAccount called");let r=this.getCachedAccountEntity(e);return this.updateOutdatedCachedAccount(e,r,t)}getCachedAccountEntity(e){let t=this.getItem(e);if(!t)return this.removeAccountKeyFromMap(e),null;let r=this.validateAndParseJson(t);return r&&e5.isAccountEntity(r)?tl.toObject(new e5,r):(this.removeAccountKeyFromMap(e),null)}setAccount(e){this.logger.trace("BrowserCacheManager.setAccount called");let t=e.generateAccountKey();this.setItem(t,JSON.stringify(e)),this.addAccountKeyToMap(t)}getAccountKeys(){this.logger.trace("BrowserCacheManager.getAccountKeys called");let e=this.getItem(tO.ACCOUNT_KEYS);return e?JSON.parse(e):(this.logger.verbose("BrowserCacheManager.getAccountKeys - No account keys found"),[])}addAccountKeyToMap(e){this.logger.trace("BrowserCacheManager.addAccountKeyToMap called"),this.logger.tracePii(`BrowserCacheManager.addAccountKeyToMap called with key: ${e}`);let t=this.getAccountKeys();-1===t.indexOf(e)?(t.push(e),this.setItem(tO.ACCOUNT_KEYS,JSON.stringify(t)),this.logger.verbose("BrowserCacheManager.addAccountKeyToMap account key added")):this.logger.verbose("BrowserCacheManager.addAccountKeyToMap account key already exists in map")}removeAccountKeyFromMap(e){this.logger.trace("BrowserCacheManager.removeAccountKeyFromMap called"),this.logger.tracePii(`BrowserCacheManager.removeAccountKeyFromMap called with key: ${e}`);let t=this.getAccountKeys(),r=t.indexOf(e);r>-1?(t.splice(r,1),this.setItem(tO.ACCOUNT_KEYS,JSON.stringify(t)),this.logger.trace("BrowserCacheManager.removeAccountKeyFromMap account key removed")):this.logger.trace("BrowserCacheManager.removeAccountKeyFromMap key not found in existing map")}async removeAccount(e){super.removeAccount(e),this.removeAccountKeyFromMap(e)}removeOutdatedAccount(e){this.removeItem(e),this.removeAccountKeyFromMap(e)}removeIdToken(e){super.removeIdToken(e),this.removeTokenKey(e,a.d3.ID_TOKEN)}async removeAccessToken(e){super.removeAccessToken(e),this.removeTokenKey(e,a.d3.ACCESS_TOKEN)}removeRefreshToken(e){super.removeRefreshToken(e),this.removeTokenKey(e,a.d3.REFRESH_TOKEN)}getTokenKeys(){this.logger.trace("BrowserCacheManager.getTokenKeys called");let e=this.getItem(`${tO.TOKEN_KEYS}.${this.clientId}`);if(e){let t=this.validateAndParseJson(e);if(t&&t.hasOwnProperty("idToken")&&t.hasOwnProperty("accessToken")&&t.hasOwnProperty("refreshToken"))return t;this.logger.error("BrowserCacheManager.getTokenKeys - Token keys found but in an unknown format. Returning empty key map.")}else this.logger.verbose("BrowserCacheManager.getTokenKeys - No token keys found");return{idToken:[],accessToken:[],refreshToken:[]}}addTokenKey(e,t){this.logger.trace("BrowserCacheManager addTokenKey called");let r=this.getTokenKeys();switch(t){case a.d3.ID_TOKEN:-1===r.idToken.indexOf(e)&&(this.logger.info("BrowserCacheManager: addTokenKey - idToken added to map"),r.idToken.push(e));break;case a.d3.ACCESS_TOKEN:-1===r.accessToken.indexOf(e)&&(this.logger.info("BrowserCacheManager: addTokenKey - accessToken added to map"),r.accessToken.push(e));break;case a.d3.REFRESH_TOKEN:-1===r.refreshToken.indexOf(e)&&(this.logger.info("BrowserCacheManager: addTokenKey - refreshToken added to map"),r.refreshToken.push(e));break;default:throw this.logger.error(`BrowserCacheManager:addTokenKey - CredentialType provided invalid. CredentialType: ${t}`),ea(G)}this.setItem(`${tO.TOKEN_KEYS}.${this.clientId}`,JSON.stringify(r))}removeTokenKey(e,t){this.logger.trace("BrowserCacheManager removeTokenKey called");let r=this.getTokenKeys();switch(t){case a.d3.ID_TOKEN:this.logger.infoPii(`BrowserCacheManager: removeTokenKey - attempting to remove idToken with key: ${e} from map`);let n=r.idToken.indexOf(e);n>-1?(this.logger.info("BrowserCacheManager: removeTokenKey - idToken removed from map"),r.idToken.splice(n,1)):this.logger.info("BrowserCacheManager: removeTokenKey - idToken does not exist in map. Either it was previously removed or it was never added.");break;case a.d3.ACCESS_TOKEN:this.logger.infoPii(`BrowserCacheManager: removeTokenKey - attempting to remove accessToken with key: ${e} from map`);let i=r.accessToken.indexOf(e);i>-1?(this.logger.info("BrowserCacheManager: removeTokenKey - accessToken removed from map"),r.accessToken.splice(i,1)):this.logger.info("BrowserCacheManager: removeTokenKey - accessToken does not exist in map. Either it was previously removed or it was never added.");break;case a.d3.REFRESH_TOKEN:this.logger.infoPii(`BrowserCacheManager: removeTokenKey - attempting to remove refreshToken with key: ${e} from map`);let o=r.refreshToken.indexOf(e);o>-1?(this.logger.info("BrowserCacheManager: removeTokenKey - refreshToken removed from map"),r.refreshToken.splice(o,1)):this.logger.info("BrowserCacheManager: removeTokenKey - refreshToken does not exist in map. Either it was previously removed or it was never added.");break;default:throw this.logger.error(`BrowserCacheManager:removeTokenKey - CredentialType provided invalid. CredentialType: ${t}`),ea(G)}this.setItem(`${tO.TOKEN_KEYS}.${this.clientId}`,JSON.stringify(r))}getIdTokenCredential(e){let t=this.getItem(e);if(!t)return this.logger.trace("BrowserCacheManager.getIdTokenCredential: called, no cache hit"),this.removeTokenKey(e,a.d3.ID_TOKEN),null;let r=this.validateAndParseJson(t);return r&&eI(r)?(this.logger.trace("BrowserCacheManager.getIdTokenCredential: cache hit"),r):(this.logger.trace("BrowserCacheManager.getIdTokenCredential: called, no cache hit"),this.removeTokenKey(e,a.d3.ID_TOKEN),null)}setIdTokenCredential(e){this.logger.trace("BrowserCacheManager.setIdTokenCredential called");let t=ep(e);this.setItem(t,JSON.stringify(e)),this.addTokenKey(t,a.d3.ID_TOKEN)}getAccessTokenCredential(e){let t=this.getItem(e);if(!t)return this.logger.trace("BrowserCacheManager.getAccessTokenCredential: called, no cache hit"),this.removeTokenKey(e,a.d3.ACCESS_TOKEN),null;let r=this.validateAndParseJson(t);return r&&eT(r)?(this.logger.trace("BrowserCacheManager.getAccessTokenCredential: cache hit"),r):(this.logger.trace("BrowserCacheManager.getAccessTokenCredential: called, no cache hit"),this.removeTokenKey(e,a.d3.ACCESS_TOKEN),null)}setAccessTokenCredential(e){this.logger.trace("BrowserCacheManager.setAccessTokenCredential called");let t=ep(e);this.setItem(t,JSON.stringify(e)),this.addTokenKey(t,a.d3.ACCESS_TOKEN)}getRefreshTokenCredential(e){let t=this.getItem(e);if(!t)return this.logger.trace("BrowserCacheManager.getRefreshTokenCredential: called, no cache hit"),this.removeTokenKey(e,a.d3.REFRESH_TOKEN),null;let r=this.validateAndParseJson(t);return r&&ev(r)?(this.logger.trace("BrowserCacheManager.getRefreshTokenCredential: cache hit"),r):(this.logger.trace("BrowserCacheManager.getRefreshTokenCredential: called, no cache hit"),this.removeTokenKey(e,a.d3.REFRESH_TOKEN),null)}setRefreshTokenCredential(e){this.logger.trace("BrowserCacheManager.setRefreshTokenCredential called");let t=ep(e);this.setItem(t,JSON.stringify(e)),this.addTokenKey(t,a.d3.REFRESH_TOKEN)}getAppMetadata(e){let t=this.getItem(e);if(!t)return this.logger.trace("BrowserCacheManager.getAppMetadata: called, no cache hit"),null;let r=this.validateAndParseJson(t);return r&&r&&0===e.indexOf(a.dg)&&r.hasOwnProperty("clientId")&&r.hasOwnProperty("environment")?(this.logger.trace("BrowserCacheManager.getAppMetadata: cache hit"),r):(this.logger.trace("BrowserCacheManager.getAppMetadata: called, no cache hit"),null)}setAppMetadata(e){this.logger.trace("BrowserCacheManager.setAppMetadata called");let t=function({environment:e,clientId:t}){return[a.dg,e,t].join(a.Bv.CACHE_KEY_SEPARATOR).toLowerCase()}(e);this.setItem(t,JSON.stringify(e))}getServerTelemetry(e){let t=this.getItem(e);if(!t)return this.logger.trace("BrowserCacheManager.getServerTelemetry: called, no cache hit"),null;let r=this.validateAndParseJson(t);return r&&function(e,t){let r=0===e.indexOf(a.HN.CACHE_KEY),n=!0;return t&&(n=t.hasOwnProperty("failedRequests")&&t.hasOwnProperty("errors")&&t.hasOwnProperty("cacheHits")),r&&n}(e,r)?(this.logger.trace("BrowserCacheManager.getServerTelemetry: cache hit"),r):(this.logger.trace("BrowserCacheManager.getServerTelemetry: called, no cache hit"),null)}setServerTelemetry(e,t){this.logger.trace("BrowserCacheManager.setServerTelemetry called"),this.setItem(e,JSON.stringify(t))}getAuthorityMetadata(e){let t=this.internalStorage.getItem(e);if(!t)return this.logger.trace("BrowserCacheManager.getAuthorityMetadata: called, no cache hit"),null;let r=this.validateAndParseJson(t);return r&&r&&0===e.indexOf(a.QU.CACHE_KEY)&&r.hasOwnProperty("aliases")&&r.hasOwnProperty("preferred_cache")&&r.hasOwnProperty("preferred_network")&&r.hasOwnProperty("canonical_authority")&&r.hasOwnProperty("authorization_endpoint")&&r.hasOwnProperty("token_endpoint")&&r.hasOwnProperty("issuer")&&r.hasOwnProperty("aliasesFromNetwork")&&r.hasOwnProperty("endpointsFromNetwork")&&r.hasOwnProperty("expiresAt")&&r.hasOwnProperty("jwks_uri")?(this.logger.trace("BrowserCacheManager.getAuthorityMetadata: cache hit"),r):null}getAuthorityMetadataKeys(){return this.internalStorage.getKeys().filter(e=>this.isAuthorityMetadata(e))}setWrapperMetadata(e,t){this.internalStorage.setItem(tN,e),this.internalStorage.setItem(tP,t)}getWrapperMetadata(){return[this.internalStorage.getItem(tN)||a.gT.EMPTY_STRING,this.internalStorage.getItem(tP)||a.gT.EMPTY_STRING]}setAuthorityMetadata(e,t){this.logger.trace("BrowserCacheManager.setAuthorityMetadata called"),this.internalStorage.setItem(e,JSON.stringify(t))}getActiveAccount(){let e=this.generateCacheKey(a.Vi.ACTIVE_ACCOUNT_FILTERS),t=this.getItem(e);if(!t){this.logger.trace("BrowserCacheManager.getActiveAccount: No active account filters cache schema found, looking for legacy schema");let e=this.generateCacheKey(a.Vi.ACTIVE_ACCOUNT),t=this.getItem(e);if(!t)return this.logger.trace("BrowserCacheManager.getActiveAccount: No active account found"),null;let r=this.getAccountInfoFilteredBy({localAccountId:t});return r?(this.logger.trace("BrowserCacheManager.getActiveAccount: Legacy active account cache schema found"),this.logger.trace("BrowserCacheManager.getActiveAccount: Adding active account filters cache schema"),this.setActiveAccount(r),r):null}let r=this.validateAndParseJson(t);return r?(this.logger.trace("BrowserCacheManager.getActiveAccount: Active account filters schema found"),this.getAccountInfoFilteredBy({homeAccountId:r.homeAccountId,localAccountId:r.localAccountId,tenantId:r.tenantId})):(this.logger.trace("BrowserCacheManager.getActiveAccount: No active account found"),null)}setActiveAccount(e){let t=this.generateCacheKey(a.Vi.ACTIVE_ACCOUNT_FILTERS),r=this.generateCacheKey(a.Vi.ACTIVE_ACCOUNT);if(e){this.logger.verbose("setActiveAccount: Active account set");let n={homeAccountId:e.homeAccountId,localAccountId:e.localAccountId,tenantId:e.tenantId};this.browserStorage.setItem(t,JSON.stringify(n)),this.browserStorage.setItem(r,e.localAccountId)}else this.logger.verbose("setActiveAccount: No account passed, active account not set"),this.browserStorage.removeItem(t),this.browserStorage.removeItem(r)}getThrottlingCache(e){let t,r;let n=this.getItem(e);if(!n)return this.logger.trace("BrowserCacheManager.getThrottlingCache: called, no cache hit"),null;let i=this.validateAndParseJson(n);return i&&(t=!1,e&&(t=0===e.indexOf(a.bb.THROTTLING_PREFIX)),r=!0,i&&(r=i.hasOwnProperty("throttleTime")),t&&r)?(this.logger.trace("BrowserCacheManager.getThrottlingCache: cache hit"),i):(this.logger.trace("BrowserCacheManager.getThrottlingCache: called, no cache hit"),null)}setThrottlingCache(e,t){this.logger.trace("BrowserCacheManager.setThrottlingCache called"),this.setItem(e,JSON.stringify(t))}getTemporaryCache(e,t){let r=t?this.generateCacheKey(e):e;if(this.cacheConfig.storeAuthStateInCookie){let e=this.cookieStorage.getItem(r);if(e)return this.logger.trace("BrowserCacheManager.getTemporaryCache: storeAuthStateInCookies set to true, retrieving from cookies"),e}let n=this.temporaryCacheStorage.getItem(r);if(!n){if(this.cacheConfig.cacheLocation===tR.LocalStorage){let e=this.browserStorage.getItem(r);if(e)return this.logger.trace("BrowserCacheManager.getTemporaryCache: Temporary cache item found in local storage"),e}return this.logger.trace("BrowserCacheManager.getTemporaryCache: No cache item found in local storage"),null}return this.logger.trace("BrowserCacheManager.getTemporaryCache: Temporary cache item returned"),n}setTemporaryCache(e,t,r){let n=r?this.generateCacheKey(e):e;this.temporaryCacheStorage.setItem(n,t),this.cacheConfig.storeAuthStateInCookie&&(this.logger.trace("BrowserCacheManager.setTemporaryCache: storeAuthStateInCookie set to true, setting item cookie"),this.cookieStorage.setItem(n,t,void 0,this.cacheConfig.secureCookies))}removeItem(e){this.browserStorage.removeItem(e)}removeTemporaryItem(e){this.temporaryCacheStorage.removeItem(e),this.cacheConfig.storeAuthStateInCookie&&(this.logger.trace("BrowserCacheManager.removeItem: storeAuthStateInCookie is true, clearing item cookie"),this.cookieStorage.removeItem(e))}getKeys(){return this.browserStorage.getKeys()}async clear(){await this.removeAllAccounts(),this.removeAppMetadata(),this.temporaryCacheStorage.getKeys().forEach(e=>{(-1!==e.indexOf(a.gT.CACHE_PREFIX)||-1!==e.indexOf(this.clientId))&&this.removeTemporaryItem(e)}),this.browserStorage.getKeys().forEach(e=>{(-1!==e.indexOf(a.gT.CACHE_PREFIX)||-1!==e.indexOf(this.clientId))&&this.browserStorage.removeItem(e)}),this.internalStorage.clear()}async clearTokensAndKeysWithClaims(e,t){e.addQueueMeasurement(tv.ClearTokensAndKeysWithClaims,t);let r=this.getTokenKeys(),n=[];r.accessToken.forEach(e=>{let t=this.getAccessTokenCredential(e);t?.requestedClaimsHash&&e.includes(t.requestedClaimsHash.toLowerCase())&&n.push(this.removeAccessToken(e))}),await Promise.all(n),n.length>0&&this.logger.warning(`${n.length} access tokens with claims in the cache keys have been removed from the cache.`)}generateCacheKey(e){return this.validateAndParseJson(e)?JSON.stringify(e):eJ.startsWith(e,a.gT.CACHE_PREFIX)||eJ.startsWith(e,a.Vi.ADAL_ID_TOKEN)?e:`${a.gT.CACHE_PREFIX}.${this.clientId}.${e}`}generateAuthorityKey(e){let{libraryState:{id:t}}=nb.parseRequestState(this.cryptoImpl,e);return this.generateCacheKey(`${tb.AUTHORITY}.${t}`)}generateNonceKey(e){let{libraryState:{id:t}}=nb.parseRequestState(this.cryptoImpl,e);return this.generateCacheKey(`${tb.NONCE_IDTOKEN}.${t}`)}generateStateKey(e){let{libraryState:{id:t}}=nb.parseRequestState(this.cryptoImpl,e);return this.generateCacheKey(`${tb.REQUEST_STATE}.${t}`)}getCachedAuthority(e){let t=this.generateStateKey(e),r=this.getTemporaryCache(t);if(!r)return null;let n=this.generateAuthorityKey(r);return this.getTemporaryCache(n)}updateCacheEntries(e,t,r,n,i){this.logger.trace("BrowserCacheManager.updateCacheEntries called");let o=this.generateStateKey(e);this.setTemporaryCache(o,e,!1);let a=this.generateNonceKey(e);this.setTemporaryCache(a,t,!1);let s=this.generateAuthorityKey(e);if(this.setTemporaryCache(s,r,!1),i){let e={credential:i.homeAccountId,type:nO.HOME_ACCOUNT_ID};this.setTemporaryCache(tb.CCS_CREDENTIAL,JSON.stringify(e),!0)}else if(n){let e={credential:n,type:nO.UPN};this.setTemporaryCache(tb.CCS_CREDENTIAL,JSON.stringify(e),!0)}}resetRequestCache(e){this.logger.trace("BrowserCacheManager.resetRequestCache called"),e&&(this.temporaryCacheStorage.getKeys().forEach(t=>{-1!==t.indexOf(e)&&this.removeTemporaryItem(t)}),this.removeTemporaryItem(this.generateStateKey(e)),this.removeTemporaryItem(this.generateNonceKey(e)),this.removeTemporaryItem(this.generateAuthorityKey(e))),this.removeTemporaryItem(this.generateCacheKey(tb.REQUEST_PARAMS)),this.removeTemporaryItem(this.generateCacheKey(tb.ORIGIN_URI)),this.removeTemporaryItem(this.generateCacheKey(tb.URL_HASH)),this.removeTemporaryItem(this.generateCacheKey(tb.CORRELATION_ID)),this.removeTemporaryItem(this.generateCacheKey(tb.CCS_CREDENTIAL)),this.removeTemporaryItem(this.generateCacheKey(tb.NATIVE_REQUEST)),this.setInteractionInProgress(!1)}cleanRequestByState(e){if(this.logger.trace("BrowserCacheManager.cleanRequestByState called"),e){let t=this.generateStateKey(e),r=this.temporaryCacheStorage.getItem(t);this.logger.infoPii(`BrowserCacheManager.cleanRequestByState: Removing temporary cache items for state: ${r}`),this.resetRequestCache(r||a.gT.EMPTY_STRING)}}cleanRequestByInteractionType(e){this.logger.trace("BrowserCacheManager.cleanRequestByInteractionType called"),this.temporaryCacheStorage.getKeys().forEach(t=>{if(-1===t.indexOf(tb.REQUEST_STATE))return;let r=this.temporaryCacheStorage.getItem(t);if(!r)return;let n=nM(this.cryptoImpl,r);n&&n.interactionType===e&&(this.logger.infoPii(`BrowserCacheManager.cleanRequestByInteractionType: Removing temporary cache items for state: ${r}`),this.resetRequestCache(r))}),this.setInteractionInProgress(!1)}cacheCodeRequest(e){this.logger.trace("BrowserCacheManager.cacheCodeRequest called");let t=rU(JSON.stringify(e));this.setTemporaryCache(tb.REQUEST_PARAMS,t,!0)}getCachedRequest(e){let t;this.logger.trace("BrowserCacheManager.getCachedRequest called");let r=this.getTemporaryCache(tb.REQUEST_PARAMS,!0);if(!r)throw r_(t8);try{t=JSON.parse(nc(r))}catch(e){throw this.logger.errorPii(`Attempted to parse: ${r}`),this.logger.error(`Parsing cached token request threw with error: ${e}`),r_(t7)}if(this.removeTemporaryItem(this.generateCacheKey(tb.REQUEST_PARAMS)),!t.authority){let r=this.generateAuthorityKey(e),n=this.getTemporaryCache(r);if(!n)throw r_(re);t.authority=n}return t}getCachedNativeRequest(){this.logger.trace("BrowserCacheManager.getCachedNativeRequest called");let e=this.getTemporaryCache(tb.NATIVE_REQUEST,!0);return e?this.validateAndParseJson(e)||(this.logger.error("BrowserCacheManager.getCachedNativeRequest: Unable to parse native request"),null):(this.logger.trace("BrowserCacheManager.getCachedNativeRequest: No cached native request found"),null)}isInteractionInProgress(e){let t=this.getInteractionInProgress();return e?t===this.clientId:!!t}getInteractionInProgress(){let e=`${a.gT.CACHE_PREFIX}.${tb.INTERACTION_STATUS_KEY}`;return this.getTemporaryCache(e,!1)}setInteractionInProgress(e){let t=`${a.gT.CACHE_PREFIX}.${tb.INTERACTION_STATUS_KEY}`;if(e){if(this.getInteractionInProgress())throw r_(tj);this.setTemporaryCache(t,this.clientId,!1)}else e||this.getInteractionInProgress()!==this.clientId||this.removeTemporaryItem(t)}getLegacyLoginHint(){let e=this.getTemporaryCache(a.Vi.ADAL_ID_TOKEN);e&&(this.browserStorage.removeItem(a.Vi.ADAL_ID_TOKEN),this.logger.verbose("Cached ADAL id token retrieved."));let t=this.getTemporaryCache(a.Vi.ID_TOKEN,!0);t&&(this.browserStorage.removeItem(this.generateCacheKey(a.Vi.ID_TOKEN)),this.logger.verbose("Cached MSAL.js v1 id token retrieved"));let r=t||e;if(r){let e=ed(r,nc);if(e.preferred_username)return this.logger.verbose("No SSO params used and ADAL/MSAL v1 token retrieved, setting ADAL/MSAL v1 preferred_username as loginHint"),e.preferred_username;if(e.upn)return this.logger.verbose("No SSO params used and ADAL/MSAL v1 token retrieved, setting ADAL/MSAL v1 upn as loginHint"),e.upn;this.logger.verbose("No SSO params used and ADAL/MSAL v1 token retrieved, however, no account hint claim found. Enable preferred_username or upn id token claim to get SSO.")}return null}updateCredentialCacheKey(e,t){let r=ep(t);if(e!==r){let n=this.getItem(e);if(n)return this.browserStorage.removeItem(e),this.setItem(r,n),this.logger.verbose(`Updated an outdated ${t.credentialType} cache key`),r;this.logger.error(`Attempted to update an outdated ${t.credentialType} cache key but no item matching the outdated key was found in storage`)}return e}async hydrateCache(e,t){let r;let n=em(e.account?.homeAccountId,e.account?.environment,e.idToken,this.clientId,e.tenantId);t.claims&&(r=await this.cryptoImpl.hashString(t.claims));let i=ef(e.account?.homeAccountId,e.account.environment,e.accessToken,this.clientId,e.tenantId,e.scopes.join(" "),e.expiresOn?e.expiresOn.getTime()/1e3:0,e.extExpiresOn?e.extExpiresOn.getTime()/1e3:0,nc,void 0,e.tokenType,void 0,t.sshKid,t.claims,r);return this.saveCacheRecord({idToken:n,accessToken:i})}async saveCacheRecord(e,t,r){try{await super.saveCacheRecord(e,t,r)}catch(e){if(e instanceof tc&&this.performanceClient&&r)try{let e=this.getTokenKeys();this.performanceClient.addFields({cacheRtCount:e.refreshToken.length,cacheIdCount:e.idToken.length,cacheAtCount:e.accessToken.length},r)}catch(e){}throw e}}}let nH=(e,t)=>new nq(e,{cacheLocation:tR.MemoryStorage,temporaryCacheLocation:tR.MemoryStorage,storeAuthStateInCookie:!1,secureCookies:!1,cacheMigrationEnabled:!1,claimsBasedCachingEnabled:!1},es,t),nL={INITIALIZE_START:"msal:initializeStart",INITIALIZE_END:"msal:initializeEnd",ACCOUNT_ADDED:"msal:accountAdded",ACCOUNT_REMOVED:"msal:accountRemoved",ACTIVE_ACCOUNT_CHANGED:"msal:activeAccountChanged",LOGIN_START:"msal:loginStart",LOGIN_SUCCESS:"msal:loginSuccess",LOGIN_FAILURE:"msal:loginFailure",ACQUIRE_TOKEN_START:"msal:acquireTokenStart",ACQUIRE_TOKEN_SUCCESS:"msal:acquireTokenSuccess",ACQUIRE_TOKEN_FAILURE:"msal:acquireTokenFailure",ACQUIRE_TOKEN_NETWORK_START:"msal:acquireTokenFromNetworkStart",SSO_SILENT_START:"msal:ssoSilentStart",SSO_SILENT_SUCCESS:"msal:ssoSilentSuccess",SSO_SILENT_FAILURE:"msal:ssoSilentFailure",ACQUIRE_TOKEN_BY_CODE_START:"msal:acquireTokenByCodeStart",ACQUIRE_TOKEN_BY_CODE_SUCCESS:"msal:acquireTokenByCodeSuccess",ACQUIRE_TOKEN_BY_CODE_FAILURE:"msal:acquireTokenByCodeFailure",HANDLE_REDIRECT_START:"msal:handleRedirectStart",HANDLE_REDIRECT_END:"msal:handleRedirectEnd",POPUP_OPENED:"msal:popupOpened",LOGOUT_START:"msal:logoutStart",LOGOUT_SUCCESS:"msal:logoutSuccess",LOGOUT_FAILURE:"msal:logoutFailure",LOGOUT_END:"msal:logoutEnd",RESTORE_FROM_BFCACHE:"msal:restoreFromBFCache"};class nD{constructor(e){this.eventCallbacks=new Map,this.logger=e||new o.Y({})}addEventCallback(e,t,r){if("undefined"!=typeof window){let n=r||rz();return this.eventCallbacks.has(n)?(this.logger.error(`Event callback with id: ${n} is already registered. Please provide a unique id or remove the existing callback and try again.`),null):(this.eventCallbacks.set(n,[e,t||[]]),this.logger.verbose(`Event callback registered with id: ${n}`),n)}return null}removeEventCallback(e){this.eventCallbacks.delete(e),this.logger.verbose(`Event callback ${e} removed.`)}emitEvent(e,t,r,n){if("undefined"!=typeof window){let i={eventType:e,interactionType:t||null,payload:r||null,error:n||null,timestamp:Date.now()};this.eventCallbacks.forEach(([t,r],n)=>{(0===r.length||r.includes(e))&&(this.logger.verbose(`Emitting event to callback ${n}: ${e}`),t.apply(null,[i]))})}}}class nF extends u{constructor(e,t,r,n,i){super(e,t,r),this.name="ServerError",this.errorNo=n,this.status=i,Object.setPrototypeOf(this,nF.prototype)}}class nx{static generateThrottlingStorageKey(e){return`${a.bb.THROTTLING_PREFIX}.${JSON.stringify(e)}`}static preProcess(e,t){let r=nx.generateThrottlingStorageKey(t),n=e.getThrottlingCache(r);if(n){if(n.throttleTime<Date.now()){e.removeItem(r);return}throw new nF(n.errorCodes?.join(" ")||a.gT.EMPTY_STRING,n.errorMessage,n.subError)}}static postProcess(e,t,r){if(nx.checkResponseStatus(r)||nx.checkResponseForRetryAfter(r)){let n={throttleTime:nx.calculateThrottleTime(parseInt(r.headers[a.SZ.RETRY_AFTER])),error:r.body.error,errorCodes:r.body.error_codes,errorMessage:r.body.error_description,subError:r.body.suberror};e.setThrottlingCache(nx.generateThrottlingStorageKey(t),n)}}static checkResponseStatus(e){return 429===e.status||e.status>=500&&e.status<600}static checkResponseForRetryAfter(e){return!!e.headers&&e.headers.hasOwnProperty(a.SZ.RETRY_AFTER)&&(e.status<200||e.status>=300)}static calculateThrottleTime(e){let t=Date.now()/1e3;return Math.floor(1e3*Math.min(t+((e<=0?0:e)||a.bb.DEFAULT_THROTTLE_TIME_SECONDS),t+a.bb.DEFAULT_MAX_THROTTLE_TIME_SECONDS))}static removeThrottle(e,t,r,n){let i={clientId:t,authority:r.authority,scopes:r.scopes,homeAccountIdentifier:n,claims:r.claims,authenticationScheme:r.authenticationScheme,resourceRequestMethod:r.resourceRequestMethod,resourceRequestUri:r.resourceRequestUri,shrClaims:r.shrClaims,sshKid:r.sshKid},o=this.generateThrottlingStorageKey(i);e.removeItem(o)}}let nB="client_id",nK="redirect_uri",nG="response_type",nz="token_type",n$="req_cnf",nW="return_spa_code",nV="brk_client_id",nQ="brk_redirect_uri";class nj{static validateRedirectUri(e){if(!e)throw eY(ek)}static validatePrompt(e){let t=[];for(let e in a.NJ)t.push(a.NJ[e]);if(0>t.indexOf(e))throw eY(eP)}static validateClaims(e){try{JSON.parse(e)}catch(e){throw eY(eM)}}static validateCodeChallengeParams(e,t){if(e&&t)this.validateCodeChallengeMethod(t);else throw eY(eL)}static validateCodeChallengeMethod(e){if(0>[a.VX.PLAIN,a.VX.S256].indexOf(e))throw eY(eH)}}class nY{constructor(e,t){this.parameters=new Map,this.performanceClient=t,this.correlationId=e}addResponseTypeCode(){this.parameters.set(nG,encodeURIComponent(a.gT.CODE_RESPONSE_TYPE))}addResponseTypeForTokenAndIdToken(){this.parameters.set(nG,encodeURIComponent(`${a.gT.TOKEN_RESPONSE_TYPE} ${a.gT.ID_TOKEN_RESPONSE_TYPE}`))}addResponseMode(e){this.parameters.set("response_mode",encodeURIComponent(e||a.vw.QUERY))}addNativeBroker(){this.parameters.set("nativebroker",encodeURIComponent("1"))}addScopes(e,t=!0,r=a.f_){!t||r.includes("openid")||e.includes("openid")||r.push("openid");let n=new eX(t?[...e||[],...r]:e||[]);this.parameters.set("scope",encodeURIComponent(n.printScopes()))}addClientId(e){this.parameters.set(nB,encodeURIComponent(e))}addRedirectUri(e){nj.validateRedirectUri(e),this.parameters.set(nK,encodeURIComponent(e))}addPostLogoutRedirectUri(e){nj.validateRedirectUri(e),this.parameters.set("post_logout_redirect_uri",encodeURIComponent(e))}addIdTokenHint(e){this.parameters.set("id_token_hint",encodeURIComponent(e))}addDomainHint(e){this.parameters.set("domain_hint",encodeURIComponent(e))}addLoginHint(e){this.parameters.set("login_hint",encodeURIComponent(e))}addCcsUpn(e){this.parameters.set(a.SZ.CCS_HEADER,encodeURIComponent(`UPN:${e}`))}addCcsOid(e){this.parameters.set(a.SZ.CCS_HEADER,encodeURIComponent(`Oid:${e.uid}@${e.utid}`))}addSid(e){this.parameters.set("sid",encodeURIComponent(e))}addClaims(e,t){let r=this.addClientCapabilitiesToClaims(e,t);nj.validateClaims(r),this.parameters.set("claims",encodeURIComponent(r))}addCorrelationId(e){this.parameters.set("client-request-id",encodeURIComponent(e))}addLibraryInfo(e){this.parameters.set("x-client-SKU",e.sku),this.parameters.set("x-client-VER",e.version),e.os&&this.parameters.set("x-client-OS",e.os),e.cpu&&this.parameters.set("x-client-CPU",e.cpu)}addApplicationTelemetry(e){e?.appName&&this.parameters.set("x-app-name",e.appName),e?.appVersion&&this.parameters.set("x-app-ver",e.appVersion)}addPrompt(e){nj.validatePrompt(e),this.parameters.set("prompt",encodeURIComponent(e))}addState(e){e&&this.parameters.set("state",encodeURIComponent(e))}addNonce(e){this.parameters.set("nonce",encodeURIComponent(e))}addCodeChallengeParams(e,t){if(nj.validateCodeChallengeParams(e,t),e&&t)this.parameters.set("code_challenge",encodeURIComponent(e)),this.parameters.set("code_challenge_method",encodeURIComponent(t));else throw eY(eL)}addAuthorizationCode(e){this.parameters.set("code",encodeURIComponent(e))}addDeviceCode(e){this.parameters.set("device_code",encodeURIComponent(e))}addRefreshToken(e){this.parameters.set("refresh_token",encodeURIComponent(e))}addCodeVerifier(e){this.parameters.set("code_verifier",encodeURIComponent(e))}addClientSecret(e){this.parameters.set("client_secret",encodeURIComponent(e))}addClientAssertion(e){e&&this.parameters.set("client_assertion",encodeURIComponent(e))}addClientAssertionType(e){e&&this.parameters.set("client_assertion_type",encodeURIComponent(e))}addOboAssertion(e){this.parameters.set("assertion",encodeURIComponent(e))}addRequestTokenUse(e){this.parameters.set("requested_token_use",encodeURIComponent(e))}addGrantType(e){this.parameters.set("grant_type",encodeURIComponent(e))}addClientInfo(){this.parameters.set(a.sR,"1")}addExtraQueryParameters(e){Object.entries(e).forEach(([e,t])=>{!this.parameters.has(e)&&t&&this.parameters.set(e,t)})}addClientCapabilitiesToClaims(e,t){let r;if(e)try{r=JSON.parse(e)}catch(e){throw eY(eM)}else r={};return t&&t.length>0&&(r.hasOwnProperty(a.As.ACCESS_TOKEN)||(r[a.As.ACCESS_TOKEN]={}),r[a.As.ACCESS_TOKEN][a.As.XMS_CC]={values:t}),JSON.stringify(r)}addUsername(e){this.parameters.set(a.Ge.username,encodeURIComponent(e))}addPassword(e){this.parameters.set(a.Ge.password,encodeURIComponent(e))}addPopToken(e){e&&(this.parameters.set(nz,a.hO.POP),this.parameters.set(n$,encodeURIComponent(e)))}addSshJwk(e){e&&(this.parameters.set(nz,a.hO.SSH),this.parameters.set(n$,encodeURIComponent(e)))}addServerTelemetry(e){this.parameters.set("x-client-current-telemetry",e.generateCurrentRequestHeaderValue()),this.parameters.set("x-client-last-telemetry",e.generateLastRequestHeaderValue())}addThrottling(){this.parameters.set("x-ms-lib-capability",a.bb.X_MS_LIB_CAPABILITY_VALUE)}addLogoutHint(e){this.parameters.set("logout_hint",encodeURIComponent(e))}addBrokerParameters(e){let t={};t[nV]=e.brokerClientId,t[nQ]=e.brokerRedirectUri,this.addExtraQueryParameters(t)}createQueryString(){let e=[];return this.parameters.forEach((t,r)=>{e.push(`${r}=${t}`)}),!function(e,t,r){if(!t)return;let n=e.get(nB);n&&e.has(nV)&&r?.addFields({embeddedClientId:n,embeddedRedirectUri:e.get(nK)},t)}(this.parameters,this.correlationId,this.performanceClient),e.join("&")}}async function nJ(e,t,r,n,i,o,a){a?.addQueueMeasurement(tv.AuthorityFactoryCreateDiscoveredInstance,o);let s=nC.transformCIAMAuthority(ny(e)),c=new nC(s,t,r,n,i,o,a);try{return await nm(c.resolveEndpointsAsync.bind(c),tv.AuthorityResolveEndpointsAsync,i,a,o)(),c}catch(e){throw ea(y)}}class nX{constructor(e,t){this.config=function({authOptions:e,systemOptions:t,loggerOptions:r,cacheOptions:n,storageInterface:i,networkInterface:a,cryptoInterface:s,clientCredentials:c,libraryInfo:l,telemetry:d,serverTelemetryManager:h,persistencePlugin:u,serializableCache:g}){let p={...tu,...r};return{authOptions:{clientCapabilities:[],azureCloudOptions:tC,skipAuthorityMetadataCache:!1,instanceAware:!1,...e},systemOptions:{...th,...t},loggerOptions:p,cacheOptions:{...tg,...n},storageInterface:i||new td(e.clientId,es,new o.Y(p)),networkInterface:a||tp,cryptoInterface:s||es,clientCredentials:c||tf,libraryInfo:{...tm,...l},telemetry:{...ty,...d},serverTelemetryManager:h||null,persistencePlugin:u||null,serializableCache:g||null}}(e),this.logger=new o.Y(this.config.loggerOptions,ec,el),this.cryptoUtils=this.config.cryptoInterface,this.cacheManager=this.config.storageInterface,this.networkClient=this.config.networkInterface,this.serverTelemetryManager=this.config.serverTelemetryManager,this.authority=this.config.authOptions.authority,this.performanceClient=t}createTokenRequestHeaders(e){let t={};if(t[a.SZ.CONTENT_TYPE]=a.gT.URL_FORM_CONTENT_TYPE,!this.config.systemOptions.preventCorsPreflight&&e)switch(e.type){case nO.HOME_ACCOUNT_ID:try{let r=e0(e.credential);t[a.SZ.CCS_HEADER]=`Oid:${r.uid}@${r.utid}`}catch(e){this.logger.verbose("Could not parse home account ID for CCS Header: "+e)}break;case nO.UPN:t[a.SZ.CCS_HEADER]=`UPN: ${e.credential}`}return t}async executePostToTokenEndpoint(e,t,r,n,i,o){o&&this.performanceClient?.addQueueMeasurement(o,i);let a=await this.sendPostRequest(n,e,{body:t,headers:r},i);return this.config.serverTelemetryManager&&a.status<500&&429!==a.status&&this.config.serverTelemetryManager.clearTelemetryCache(),a}async sendPostRequest(e,t,r,n){let i;nx.preProcess(this.cacheManager,e);try{let e=(i=await nm(this.networkClient.sendPostRequestAsync.bind(this.networkClient),tv.NetworkClientSendPostRequestAsync,this.logger,this.performanceClient,n)(t,r)).headers||{};this.performanceClient?.addFields({refreshTokenSize:i.body.refresh_token?.length||0,httpVerToken:e[a.SZ.X_MS_HTTP_VERSION]||"",requestId:e[a.SZ.X_MS_REQUEST_ID]||""},n)}catch(e){if(e instanceof tx){let t=e.responseHeaders;throw t&&this.performanceClient?.addFields({httpVerToken:t[a.SZ.X_MS_HTTP_VERSION]||"",requestId:t[a.SZ.X_MS_REQUEST_ID]||"",contentTypeHeader:t[a.SZ.CONTENT_TYPE]||void 0,contentLengthHeader:t[a.SZ.CONTENT_LENGTH]||void 0,httpStatus:e.httpStatus},n),e.error}if(e instanceof u)throw e;throw ea(T)}return nx.postProcess(this.cacheManager,e,i),i}async updateAuthority(e,t){this.performanceClient?.addQueueMeasurement(tv.UpdateTokenEndpointAuthority,t);let r=`https://${e}/${this.authority.tenant}/`,n=await nJ(r,this.networkClient,this.cacheManager,this.authority.options,this.logger,t,this.performanceClient);this.authority=n}createTokenQueryParameters(e){let t=new nY(e.correlationId,this.performanceClient);return e.embeddedClientId&&t.addBrokerParameters({brokerClientId:this.config.authOptions.clientId,brokerRedirectUri:this.config.authOptions.redirectUri}),e.tokenQueryParameters&&t.addExtraQueryParameters(e.tokenQueryParameters),t.addCorrelationId(e.correlationId),t.createQueryString()}}class nZ{constructor(e,t){this.cryptoUtils=e,this.performanceClient=t}async generateCnf(e,t){this.performanceClient?.addQueueMeasurement(tv.PopTokenGenerateCnf,e.correlationId);let r=await nm(this.generateKid.bind(this),tv.PopTokenGenerateCnf,t,this.performanceClient,e.correlationId)(e),n=this.cryptoUtils.base64UrlEncode(JSON.stringify(r));return{kid:r.kid,reqCnfString:n}}async generateKid(e){return this.performanceClient?.addQueueMeasurement(tv.PopTokenGenerateKid,e.correlationId),{kid:await this.cryptoUtils.getPublicKeyThumbprint(e),xms_ksl:"sw"}}async signPopToken(e,t,r){return this.signPayload(e,t,r)}async signPayload(e,t,r,n){let{resourceRequestMethod:i,resourceRequestUri:o,shrClaims:a,shrNonce:s,shrOptions:c}=r,l=o?new e7(o):void 0,d=l?.getUrlComponents();return this.cryptoUtils.signJwt({at:e,ts:eu(),m:i?.toUpperCase(),u:d?.HostNameAndPort,nonce:s||this.cryptoUtils.createNewGuid(),p:d?.AbsolutePath,q:d?.QueryString?[[],d.QueryString]:void 0,client_claims:a||void 0,...n},t,c,r.correlationId)}}class n0{constructor(e,t){this.cache=e,this.hasChanged=t}get cacheHasChanged(){return this.hasChanged}get tokenCache(){return this.cache}}class n1{constructor(e,t,r,n,i,o,a){this.clientId=e,this.cacheStorage=t,this.cryptoObj=r,this.logger=n,this.serializableCache=i,this.persistencePlugin=o,this.performanceClient=a}validateServerAuthorizationCodeResponse(e,t){let r,n;if(!e.state||!t)throw e.state?ea(S,"Cached State"):ea(S,"Server State");try{r=decodeURIComponent(e.state)}catch(t){throw ea(A,e.state)}try{n=decodeURIComponent(t)}catch(t){throw ea(A,e.state)}if(r!==n)throw ea(w);if(e.error||e.error_description||e.suberror){let t=function(e){let t="code=",r=e.error_uri?.lastIndexOf(t);return r&&r>=0?e.error_uri?.substring(r+t.length):void 0}(e);if(nR(e.error,e.error_description,e.suberror))throw new nk(e.error||"",e.error_description,e.suberror,e.timestamp||"",e.trace_id||"",e.correlation_id||"",e.claims||"",t);throw new nF(e.error||"",e.error_description,e.suberror,t)}}validateTokenResponse(e,t){if(e.error||e.error_description||e.suberror){let r=`Error(s): ${e.error_codes||a.gT.NOT_AVAILABLE} - Timestamp: ${e.timestamp||a.gT.NOT_AVAILABLE} - Description: ${e.error_description||a.gT.NOT_AVAILABLE} - Correlation ID: ${e.correlation_id||a.gT.NOT_AVAILABLE} - Trace ID: ${e.trace_id||a.gT.NOT_AVAILABLE}`,n=e.error_codes?.length?e.error_codes[0]:void 0,i=new nF(e.error,r,e.suberror,n,e.status);if(t&&e.status&&e.status>=a.oj.SERVER_ERROR_RANGE_START&&e.status<=a.oj.SERVER_ERROR_RANGE_END){this.logger.warning(`executeTokenRequest:validateTokenResponse - AAD is currently unavailable and the access token is unable to be refreshed.
${i}`);return}if(t&&e.status&&e.status>=a.oj.CLIENT_ERROR_RANGE_START&&e.status<=a.oj.CLIENT_ERROR_RANGE_END){this.logger.warning(`executeTokenRequest:validateTokenResponse - AAD is currently available but is unable to refresh the access token.
${i}`);return}if(nR(e.error,e.error_description,e.suberror))throw new nk(e.error,e.error_description,e.suberror,e.timestamp||a.gT.EMPTY_STRING,e.trace_id||a.gT.EMPTY_STRING,e.correlation_id||a.gT.EMPTY_STRING,e.claims||a.gT.EMPTY_STRING,n);throw i}}async handleServerTokenResponse(e,t,r,n,i,o,s,c,l){let d,h,u;if(this.performanceClient?.addQueueMeasurement(tv.HandleServerTokenResponse,e.correlation_id),e.id_token){if(d=ed(e.id_token||a.gT.EMPTY_STRING,this.cryptoObj.base64Decode),i&&i.nonce&&d.nonce!==i.nonce)throw ea(E);if(n.maxAge||0===n.maxAge){let e=d.auth_time;if(!e)throw ea(k);eh(e,n.maxAge)}}this.homeAccountIdentifier=e5.generateHomeAccountId(e.client_info||a.gT.EMPTY_STRING,t.authorityType,this.logger,this.cryptoObj,d),i&&i.state&&(h=nb.parseRequestState(this.cryptoObj,i.state)),e.key_id=e.key_id||n.sshKid||void 0;let g=this.generateCacheRecord(e,t,r,n,d,o,i);try{if(this.persistencePlugin&&this.serializableCache&&(this.logger.verbose("Persistence enabled, calling beforeCacheAccess"),u=new n0(this.serializableCache,!0),await this.persistencePlugin.beforeCacheAccess(u)),s&&!c&&g.account){let e=g.account.generateAccountKey();if(!this.cacheStorage.getAccount(e,this.logger))return this.logger.warning("Account used to refresh tokens not in persistence, refreshed tokens will not be stored in the cache"),await n1.generateAuthenticationResult(this.cryptoObj,t,g,!1,n,d,h,void 0,l)}await this.cacheStorage.saveCacheRecord(g,n.storeInCache,n.correlationId)}finally{this.persistencePlugin&&this.serializableCache&&u&&(this.logger.verbose("Persistence enabled, calling afterCacheAccess"),await this.persistencePlugin.afterCacheAccess(u))}return n1.generateAuthenticationResult(this.cryptoObj,t,g,!1,n,d,h,e,l)}generateCacheRecord(e,t,r,n,i,o,a){let s,c;let l=t.getPreferredCache();if(!l)throw ea(x);let d=e4(i);e.id_token&&i&&(s=em(this.homeAccountIdentifier,l,e.id_token,this.clientId,d||""),c=n3(this.cacheStorage,t,this.homeAccountIdentifier,this.cryptoObj.base64Decode,i,e.client_info,l,d,a,void 0,this.logger));let h=null;if(e.access_token){let i=e.scope?eX.fromString(e.scope):new eX(n.scopes||[]),a=("string"==typeof e.expires_in?parseInt(e.expires_in,10):e.expires_in)||0,s=("string"==typeof e.ext_expires_in?parseInt(e.ext_expires_in,10):e.ext_expires_in)||0,c=("string"==typeof e.refresh_in?parseInt(e.refresh_in,10):e.refresh_in)||void 0,u=r+a;h=ef(this.homeAccountIdentifier,l,e.access_token,this.clientId,d||t.tenant||"",i.printScopes(),u,u+s,this.cryptoObj.base64Decode,c&&c>0?r+c:void 0,e.token_type,o,e.key_id,n.claims,n.requestedClaimsHash)}let u=null;if(e.refresh_token){let t;e.refresh_token_expires_in&&(t=r+("string"==typeof e.refresh_token_expires_in?parseInt(e.refresh_token_expires_in,10):e.refresh_token_expires_in)),u=eC(this.homeAccountIdentifier,l,e.refresh_token,this.clientId,e.foci,o,t)}let g=null;return e.foci&&(g={clientId:this.clientId,environment:l,familyId:e.foci}),{account:c,idToken:s,accessToken:h,refreshToken:u,appMetadata:g}}static async generateAuthenticationResult(e,t,r,n,i,o,s,c,l){let d,h,u=a.gT.EMPTY_STRING,g=[],p=null,m=a.gT.EMPTY_STRING;if(r.accessToken){if(r.accessToken.tokenType!==a.hO.POP||i.popKid)u=r.accessToken.secret;else{let t=new nZ(e),{secret:n,keyId:o}=r.accessToken;if(!o)throw ea(X);u=await t.signPopToken(n,o,i)}g=eX.fromString(r.accessToken.target).asArray(),p=new Date(1e3*Number(r.accessToken.expiresOn)),d=new Date(1e3*Number(r.accessToken.extendedExpiresOn)),r.accessToken.refreshOn&&(h=new Date(1e3*Number(r.accessToken.refreshOn)))}r.appMetadata&&(m=r.appMetadata.familyId===a.ch?a.ch:"");let f=o?.oid||o?.sub||"",C=o?.tid||"";c?.spa_accountid&&r.account&&(r.account.nativeAccountId=c?.spa_accountid);let y=r.account?e2(r.account.getAccountInfo(),void 0,o,r.idToken?.secret):null;return{authority:t.canonicalAuthority,uniqueId:f,tenantId:C,scopes:g,account:y,idToken:r?.idToken?.secret||"",idTokenClaims:o||{},accessToken:u,fromCache:n,expiresOn:p,extExpiresOn:d,refreshOn:h,correlationId:i.correlationId,requestId:l||a.gT.EMPTY_STRING,familyId:m,tokenType:r.accessToken?.tokenType||a.gT.EMPTY_STRING,state:s?s.userRequestState:a.gT.EMPTY_STRING,cloudGraphHostName:r.account?.cloudGraphHostName||a.gT.EMPTY_STRING,msGraphHost:r.account?.msGraphHost||a.gT.EMPTY_STRING,code:c?.spa_code,fromNativeBroker:!1}}}function n3(e,t,r,n,i,o,a,s,c,l,d){d?.verbose("setCachedAccount called");let h=e.getAccountKeys().find(e=>e.startsWith(r)),u=null;h&&(u=e.getAccount(h,d));let g=u||e5.createAccount({homeAccountId:r,idTokenClaims:i,clientInfo:o,environment:a,cloudGraphHostName:c?.cloud_graph_host_name,msGraphHost:c?.msgraph_host,nativeAccountId:l},t,n),p=g.tenantProfiles||[],m=s||g.realm;if(m&&!p.find(e=>e.tenantId===m)){let e=e3(r,g.localAccountId,m,i);p.push(e)}return g.tenantProfiles=p,g}async function n2(e,t,r){return"string"==typeof e?e:e({clientId:t,tokenEndpoint:r})}class n6 extends nX{constructor(e,t){super(e,t),this.includeRedirectUri=!0,this.oidcDefaultScopes=this.config.authOptions.authority.options.OIDCOptions?.defaultScopes}async getAuthCodeUrl(e){this.performanceClient?.addQueueMeasurement(tv.GetAuthCodeUrl,e.correlationId);let t=await nm(this.createAuthCodeUrlQueryString.bind(this),tv.AuthClientCreateQueryString,this.logger,this.performanceClient,e.correlationId)(e);return e7.appendQueryString(this.authority.authorizationEndpoint,t)}async acquireToken(e,t){if(this.performanceClient?.addQueueMeasurement(tv.AuthClientAcquireToken,e.correlationId),!e.code)throw ea(N);let r=eu(),n=await nm(this.executeTokenRequest.bind(this),tv.AuthClientExecuteTokenRequest,this.logger,this.performanceClient,e.correlationId)(this.authority,e),i=n.headers?.[a.SZ.X_MS_REQUEST_ID],o=new n1(this.config.authOptions.clientId,this.cacheManager,this.cryptoUtils,this.logger,this.config.serializableCache,this.config.persistencePlugin,this.performanceClient);return o.validateTokenResponse(n.body),nm(o.handleServerTokenResponse.bind(o),tv.HandleServerTokenResponse,this.logger,this.performanceClient,e.correlationId)(n.body,this.authority,r,e,t,void 0,void 0,void 0,i)}handleFragmentResponse(e,t){if(new n1(this.config.authOptions.clientId,this.cacheManager,this.cryptoUtils,this.logger,null,null).validateServerAuthorizationCodeResponse(e,t),!e.code)throw ea(j);return e}getLogoutUri(e){if(!e)throw eY(eq);let t=this.createLogoutUrlQueryString(e);return e7.appendQueryString(this.authority.endSessionEndpoint,t)}async executeTokenRequest(e,t){let r;this.performanceClient?.addQueueMeasurement(tv.AuthClientExecuteTokenRequest,t.correlationId);let n=this.createTokenQueryParameters(t),i=e7.appendQueryString(e.tokenEndpoint,n),o=await nm(this.createTokenRequestBody.bind(this),tv.AuthClientCreateTokenRequestBody,this.logger,this.performanceClient,t.correlationId)(t);if(t.clientInfo)try{let e=eZ(t.clientInfo,this.cryptoUtils.base64Decode);r={credential:`${e.uid}${a.Bv.CLIENT_INFO_SEPARATOR}${e.utid}`,type:nO.HOME_ACCOUNT_ID}}catch(e){this.logger.verbose("Could not parse client info for CCS Header: "+e)}let s=this.createTokenRequestHeaders(r||t.ccsCredential),c={clientId:t.tokenBodyParameters?.clientId||this.config.authOptions.clientId,authority:e.canonicalAuthority,scopes:t.scopes,claims:t.claims,authenticationScheme:t.authenticationScheme,resourceRequestMethod:t.resourceRequestMethod,resourceRequestUri:t.resourceRequestUri,shrClaims:t.shrClaims,sshKid:t.sshKid};return nm(this.executePostToTokenEndpoint.bind(this),tv.AuthorizationCodeClientExecutePostToTokenEndpoint,this.logger,this.performanceClient,t.correlationId)(i,o,s,c,t.correlationId,tv.AuthorizationCodeClientExecutePostToTokenEndpoint)}async createTokenRequestBody(e){let t;this.performanceClient?.addQueueMeasurement(tv.AuthClientCreateTokenRequestBody,e.correlationId);let r=new nY(e.correlationId,this.performanceClient);if(r.addClientId(e.embeddedClientId||e.tokenBodyParameters?.[nB]||this.config.authOptions.clientId),this.includeRedirectUri?r.addRedirectUri(e.redirectUri):nj.validateRedirectUri(e.redirectUri),r.addScopes(e.scopes,!0,this.oidcDefaultScopes),r.addAuthorizationCode(e.code),r.addLibraryInfo(this.config.libraryInfo),r.addApplicationTelemetry(this.config.telemetry.application),r.addThrottling(),this.serverTelemetryManager&&!tT(this.config)&&r.addServerTelemetry(this.serverTelemetryManager),e.codeVerifier&&r.addCodeVerifier(e.codeVerifier),this.config.clientCredentials.clientSecret&&r.addClientSecret(this.config.clientCredentials.clientSecret),this.config.clientCredentials.clientAssertion){let t=this.config.clientCredentials.clientAssertion;r.addClientAssertion(await n2(t.assertion,this.config.authOptions.clientId,e.resourceRequestUri)),r.addClientAssertionType(t.assertionType)}if(r.addGrantType(a.qJ.AUTHORIZATION_CODE_GRANT),r.addClientInfo(),e.authenticationScheme===a.hO.POP){let t;let n=new nZ(this.cryptoUtils,this.performanceClient);t=e.popKid?this.cryptoUtils.encodeKid(e.popKid):(await nm(n.generateCnf.bind(n),tv.PopTokenGenerateCnf,this.logger,this.performanceClient,e.correlationId)(e,this.logger)).reqCnfString,r.addPopToken(t)}else if(e.authenticationScheme===a.hO.SSH){if(e.sshJwk)r.addSshJwk(e.sshJwk);else throw eY(eB)}if((!eJ.isEmptyObj(e.claims)||this.config.authOptions.clientCapabilities&&this.config.authOptions.clientCapabilities.length>0)&&r.addClaims(e.claims,this.config.authOptions.clientCapabilities),e.clientInfo)try{let r=eZ(e.clientInfo,this.cryptoUtils.base64Decode);t={credential:`${r.uid}${a.Bv.CLIENT_INFO_SEPARATOR}${r.utid}`,type:nO.HOME_ACCOUNT_ID}}catch(e){this.logger.verbose("Could not parse client info for CCS Header: "+e)}else t=e.ccsCredential;if(this.config.systemOptions.preventCorsPreflight&&t)switch(t.type){case nO.HOME_ACCOUNT_ID:try{let e=e0(t.credential);r.addCcsOid(e)}catch(e){this.logger.verbose("Could not parse home account ID for CCS Header: "+e)}break;case nO.UPN:r.addCcsUpn(t.credential)}return e.embeddedClientId&&r.addBrokerParameters({brokerClientId:this.config.authOptions.clientId,brokerRedirectUri:this.config.authOptions.redirectUri}),e.tokenBodyParameters&&r.addExtraQueryParameters(e.tokenBodyParameters),!e.enableSpaAuthorizationCode||e.tokenBodyParameters&&e.tokenBodyParameters[nW]||r.addExtraQueryParameters({[nW]:"1"}),r.createQueryString()}async createAuthCodeUrlQueryString(e){let t=e.correlationId||this.config.cryptoInterface.createNewGuid();this.performanceClient?.addQueueMeasurement(tv.AuthClientCreateQueryString,t);let r=new nY(t,this.performanceClient);r.addClientId(e.embeddedClientId||e.extraQueryParameters?.[nB]||this.config.authOptions.clientId);let n=[...e.scopes||[],...e.extraScopesToConsent||[]];if(r.addScopes(n,!0,this.oidcDefaultScopes),r.addRedirectUri(e.redirectUri),r.addCorrelationId(t),r.addResponseMode(e.responseMode),r.addResponseTypeCode(),r.addLibraryInfo(this.config.libraryInfo),tT(this.config)||r.addApplicationTelemetry(this.config.telemetry.application),r.addClientInfo(),e.codeChallenge&&e.codeChallengeMethod&&r.addCodeChallengeParams(e.codeChallenge,e.codeChallengeMethod),e.prompt&&r.addPrompt(e.prompt),e.domainHint&&r.addDomainHint(e.domainHint),e.prompt!==a.NJ.SELECT_ACCOUNT){if(e.sid&&e.prompt===a.NJ.NONE)this.logger.verbose("createAuthCodeUrlQueryString: Prompt is none, adding sid from request"),r.addSid(e.sid);else if(e.account){let t=this.extractAccountSid(e.account),n=this.extractLoginHint(e.account);if(n&&e.domainHint&&(this.logger.warning('AuthorizationCodeClient.createAuthCodeUrlQueryString: "domainHint" param is set, skipping opaque "login_hint" claim. Please consider not passing domainHint'),n=null),n){this.logger.verbose("createAuthCodeUrlQueryString: login_hint claim present on account"),r.addLoginHint(n);try{let t=e0(e.account.homeAccountId);r.addCcsOid(t)}catch(e){this.logger.verbose("createAuthCodeUrlQueryString: Could not parse home account ID for CCS Header")}}else if(t&&e.prompt===a.NJ.NONE){this.logger.verbose("createAuthCodeUrlQueryString: Prompt is none, adding sid from account"),r.addSid(t);try{let t=e0(e.account.homeAccountId);r.addCcsOid(t)}catch(e){this.logger.verbose("createAuthCodeUrlQueryString: Could not parse home account ID for CCS Header")}}else if(e.loginHint)this.logger.verbose("createAuthCodeUrlQueryString: Adding login_hint from request"),r.addLoginHint(e.loginHint),r.addCcsUpn(e.loginHint);else if(e.account.username){this.logger.verbose("createAuthCodeUrlQueryString: Adding login_hint from account"),r.addLoginHint(e.account.username);try{let t=e0(e.account.homeAccountId);r.addCcsOid(t)}catch(e){this.logger.verbose("createAuthCodeUrlQueryString: Could not parse home account ID for CCS Header")}}}else e.loginHint&&(this.logger.verbose("createAuthCodeUrlQueryString: No account, adding login_hint from request"),r.addLoginHint(e.loginHint),r.addCcsUpn(e.loginHint))}else this.logger.verbose("createAuthCodeUrlQueryString: Prompt is select_account, ignoring account hints");if(e.nonce&&r.addNonce(e.nonce),e.state&&r.addState(e.state),(e.claims||this.config.authOptions.clientCapabilities&&this.config.authOptions.clientCapabilities.length>0)&&r.addClaims(e.claims,this.config.authOptions.clientCapabilities),e.embeddedClientId&&r.addBrokerParameters({brokerClientId:this.config.authOptions.clientId,brokerRedirectUri:this.config.authOptions.redirectUri}),this.addExtraQueryParams(e,r),e.nativeBroker&&(r.addNativeBroker(),e.authenticationScheme===a.hO.POP)){let t;let n=new nZ(this.cryptoUtils);t=e.popKid?this.cryptoUtils.encodeKid(e.popKid):(await nm(n.generateCnf.bind(n),tv.PopTokenGenerateCnf,this.logger,this.performanceClient,e.correlationId)(e,this.logger)).reqCnfString,r.addPopToken(t)}return r.createQueryString()}createLogoutUrlQueryString(e){let t=new nY(e.correlationId,this.performanceClient);return e.postLogoutRedirectUri&&t.addPostLogoutRedirectUri(e.postLogoutRedirectUri),e.correlationId&&t.addCorrelationId(e.correlationId),e.idTokenHint&&t.addIdTokenHint(e.idTokenHint),e.state&&t.addState(e.state),e.logoutHint&&t.addLogoutHint(e.logoutHint),this.addExtraQueryParams(e,t),t.createQueryString()}addExtraQueryParams(e,t){!(e.extraQueryParameters&&e.extraQueryParameters.hasOwnProperty("instance_aware"))&&this.config.authOptions.instanceAware&&(e.extraQueryParameters=e.extraQueryParameters||{},e.extraQueryParameters.instance_aware="true"),e.extraQueryParameters&&t.addExtraQueryParameters(e.extraQueryParameters)}extractAccountSid(e){return e.idTokenClaims?.sid||null}extractLoginHint(e){return e.idTokenClaims?.login_hint||null}}class n4{constructor(e,t){this.cacheOutcome=a.To.NOT_APPLICABLE,this.cacheManager=t,this.apiId=e.apiId,this.correlationId=e.correlationId,this.wrapperSKU=e.wrapperSKU||a.gT.EMPTY_STRING,this.wrapperVer=e.wrapperVer||a.gT.EMPTY_STRING,this.telemetryCacheKey=a.HN.CACHE_KEY+a.Bv.CACHE_KEY_SEPARATOR+e.clientId}generateCurrentRequestHeaderValue(){let e=`${this.apiId}${a.HN.VALUE_SEPARATOR}${this.cacheOutcome}`,t=[this.wrapperSKU,this.wrapperVer],r=this.getNativeBrokerErrorCode();r?.length&&t.push(`broker_error=${r}`);let n=t.join(a.HN.VALUE_SEPARATOR),i=[e,this.getRegionDiscoveryFields()].join(a.HN.VALUE_SEPARATOR);return[a.HN.SCHEMA_VERSION,i,n].join(a.HN.CATEGORY_SEPARATOR)}generateLastRequestHeaderValue(){let e=this.getLastRequests(),t=n4.maxErrorsToSend(e),r=e.failedRequests.slice(0,2*t).join(a.HN.VALUE_SEPARATOR),n=e.errors.slice(0,t).join(a.HN.VALUE_SEPARATOR),i=e.errors.length,o=t<i?a.HN.OVERFLOW_TRUE:a.HN.OVERFLOW_FALSE,s=[i,o].join(a.HN.VALUE_SEPARATOR);return[a.HN.SCHEMA_VERSION,e.cacheHits,r,n,s].join(a.HN.CATEGORY_SEPARATOR)}cacheFailedRequest(e){let t=this.getLastRequests();t.errors.length>=a.HN.MAX_CACHED_ERRORS&&(t.failedRequests.shift(),t.failedRequests.shift(),t.errors.shift()),t.failedRequests.push(this.apiId,this.correlationId),e instanceof Error&&e&&e.toString()?e instanceof u?e.subError?t.errors.push(e.subError):e.errorCode?t.errors.push(e.errorCode):t.errors.push(e.toString()):t.errors.push(e.toString()):t.errors.push(a.HN.UNKNOWN_ERROR),this.cacheManager.setServerTelemetry(this.telemetryCacheKey,t)}incrementCacheHits(){let e=this.getLastRequests();return e.cacheHits+=1,this.cacheManager.setServerTelemetry(this.telemetryCacheKey,e),e.cacheHits}getLastRequests(){return this.cacheManager.getServerTelemetry(this.telemetryCacheKey)||{failedRequests:[],errors:[],cacheHits:0}}clearTelemetryCache(){let e=this.getLastRequests(),t=n4.maxErrorsToSend(e);if(t===e.errors.length)this.cacheManager.removeItem(this.telemetryCacheKey);else{let r={failedRequests:e.failedRequests.slice(2*t),errors:e.errors.slice(t),cacheHits:0};this.cacheManager.setServerTelemetry(this.telemetryCacheKey,r)}}static maxErrorsToSend(e){let t;let r=0,n=0,i=e.errors.length;for(t=0;t<i;t++){let i=e.failedRequests[2*t]||a.gT.EMPTY_STRING,o=e.failedRequests[2*t+1]||a.gT.EMPTY_STRING,s=e.errors[t]||a.gT.EMPTY_STRING;if((n+=i.toString().length+o.toString().length+s.length+3)<a.HN.MAX_LAST_HEADER_BYTES)r+=1;else break}return r}getRegionDiscoveryFields(){let e=[];return e.push(this.regionUsed||a.gT.EMPTY_STRING),e.push(this.regionSource||a.gT.EMPTY_STRING),e.push(this.regionOutcome||a.gT.EMPTY_STRING),e.join(",")}updateRegionDiscoveryMetadata(e){this.regionUsed=e.region_used,this.regionSource=e.region_source,this.regionOutcome=e.region_outcome}setCacheOutcome(e){this.cacheOutcome=e}setNativeBrokerErrorCode(e){let t=this.getLastRequests();t.nativeBrokerErrorCode=e,this.cacheManager.setServerTelemetry(this.telemetryCacheKey,t)}getNativeBrokerErrorCode(){return this.getLastRequests().nativeBrokerErrorCode}clearNativeBrokerErrorCode(){let e=this.getLastRequests();delete e.nativeBrokerErrorCode,this.cacheManager.setServerTelemetry(this.telemetryCacheKey,e)}static makeExtraSkuString(e){return function(e){let{skus:t,libraryName:r,libraryVersion:n,extensionName:i,extensionVersion:o}=e,a=new Map([[0,[r,n]],[2,[i,o]]]),s=[];if(t?.length){if((s=t.split(",")).length<4)return t}else s=Array.from({length:4},()=>"|");return a.forEach((e,t)=>{2===e.length&&e[0]?.length&&e[1]?.length&&function(e){let{skuArr:t,index:r,skuName:n,skuVersion:i}=e;r>=t.length||(t[r]=[n,i].join("|"))}({skuArr:s,index:t,skuName:e[0],skuVersion:e[1]})}),s.join(",")}(e)}}class n5{constructor(e,t,r,n,i,o,a,s,c){this.config=e,this.browserStorage=t,this.browserCrypto=r,this.networkClient=this.config.system.networkClient,this.eventHandler=i,this.navigationClient=o,this.nativeMessageHandler=s,this.correlationId=c||rz(),this.logger=n.clone(tS.MSAL_SKU,r7,this.correlationId),this.performanceClient=a}async clearCacheOnLogout(e){if(e){e5.accountInfoIsEqual(e,this.browserStorage.getActiveAccount(),!1)&&(this.logger.verbose("Setting active account to null"),this.browserStorage.setActiveAccount(null));try{await this.browserStorage.removeAccount(e5.generateAccountCacheKey(e)),this.logger.verbose("Cleared cache items belonging to the account provided in the logout request.")}catch(e){this.logger.error("Account provided in logout request was not found. Local cache unchanged.")}}else try{this.logger.verbose("No account provided in logout request, clearing all cache items.",this.correlationId),await this.browserStorage.clear(),await this.browserCrypto.clearKeystore()}catch(e){this.logger.error("Attempted to clear all MSAL cache items and failed. Local cache unchanged.")}}getRedirectUri(e){this.logger.verbose("getRedirectUri called");let t=e||this.config.auth.redirectUri;return e7.getAbsoluteUrl(t,r2())}initializeServerTelemetryManager(e,t){return this.logger.verbose("initializeServerTelemetryManager called"),new n4({clientId:this.config.auth.clientId,correlationId:this.correlationId,apiId:e,forceRefresh:t||!1,wrapperSKU:this.browserStorage.getWrapperMetadata()[0],wrapperVer:this.browserStorage.getWrapperMetadata()[1]},this.browserStorage)}async getDiscoveredAuthority(e){let{account:t}=e,r=e.requestExtraQueryParameters&&e.requestExtraQueryParameters.hasOwnProperty("instance_aware")?e.requestExtraQueryParameters.instance_aware:void 0;this.performanceClient.addQueueMeasurement(tv.StandardInteractionClientGetDiscoveredAuthority,this.correlationId);let n={protocolMode:this.config.auth.protocolMode,OIDCOptions:this.config.auth.OIDCOptions,knownAuthorities:this.config.auth.knownAuthorities,cloudDiscoveryMetadata:this.config.auth.cloudDiscoveryMetadata,authorityMetadata:this.config.auth.authorityMetadata,skipAuthorityMetadataCache:this.config.auth.skipAuthorityMetadataCache},i=e.requestAuthority||this.config.auth.authority,o=r?.length?"true"===r:this.config.auth.instanceAware,a=t&&o?this.config.auth.authority.replace(e7.getDomainFromUrl(i),t.environment):i,s=nC.generateAuthority(a,e.requestAzureCloudOptions||this.config.auth.azureCloudOptions),c=await nm(nJ,tv.AuthorityFactoryCreateDiscoveredInstance,this.logger,this.performanceClient,this.correlationId)(s,this.config.system.networkClient,this.browserStorage,n,this.logger,this.correlationId,this.performanceClient);if(t&&!c.isAlias(t.environment))throw eY(eV);return c}}async function n9(e,t,r){e.addQueueMeasurement(tv.GeneratePkceCodes,r);let n=np(n8,tv.GenerateCodeVerifier,t,e,r)(e,t,r),i=await nm(n7,tv.GenerateCodeChallengeFromVerifier,t,e,r)(n,e,t,r);return{verifier:n,challenge:i}}function n8(e,t,r){try{let n=new Uint8Array(32);return np(rK,tv.GetRandomValues,t,e,r)(n),rM(n)}catch(e){throw r_(tB)}}async function n7(e,t,r,n){t.addQueueMeasurement(tv.GenerateCodeChallengeFromVerifier,n);try{let i=await nm(rB,tv.Sha256Digest,r,t,n)(e,t,n);return rM(new Uint8Array(i))}catch(e){throw r_(tB)}}async function ie(e,t,r,n){r.addQueueMeasurement(tv.InitializeBaseRequest,e.correlationId);let i=e.authority||t.auth.authority,o=[...e&&e.scopes||[]],s={...e,correlationId:e.correlationId,authority:i,scopes:o};if(s.authenticationScheme){if(s.authenticationScheme===a.hO.SSH){if(!e.sshJwk)throw eY(eB);if(!e.sshKid)throw eY(eK)}n.verbose(`Authentication Scheme set to "${s.authenticationScheme}" as configured in Auth request`)}else s.authenticationScheme=a.hO.BEARER,n.verbose('Authentication Scheme wasn\'t explicitly set in request, defaulting to "Bearer" request');return t.cache.claimsBasedCachingEnabled&&e.claims&&!eJ.isEmptyObj(e.claims)&&(s.requestedClaimsHash=await rj(e.claims)),s}async function it(e,t,r,n,i){n.addQueueMeasurement(tv.InitializeSilentRequest,e.correlationId);let o=await nm(ie,tv.InitializeBaseRequest,i,n,e.correlationId)(e,r,n,i);return{...e,...o,account:t,forceRefresh:e.forceRefresh||!1}}class ir extends n5{async initializeAuthorizationCodeRequest(e){this.performanceClient.addQueueMeasurement(tv.StandardInteractionClientInitializeAuthorizationCodeRequest,this.correlationId);let t=await nm(n9,tv.GeneratePkceCodes,this.logger,this.performanceClient,this.correlationId)(this.performanceClient,this.logger,this.correlationId),r={...e,redirectUri:e.redirectUri,code:a.gT.EMPTY_STRING,codeVerifier:t.verifier};return e.codeChallenge=t.challenge,e.codeChallengeMethod=a.gT.S256_CODE_CHALLENGE_METHOD,r}initializeLogoutRequest(e){this.logger.verbose("initializeLogoutRequest called",e?.correlationId);let t={correlationId:this.correlationId||rz(),...e};if(e){if(e.logoutHint)this.logger.verbose("logoutHint has already been set in logoutRequest");else if(e.account){let r=this.getLogoutHintFromIdTokenClaims(e.account);r&&(this.logger.verbose("Setting logoutHint to login_hint ID Token Claim value for the account provided"),t.logoutHint=r)}else this.logger.verbose("logoutHint was not set and account was not passed into logout request, logoutHint will not be set")}else this.logger.verbose("logoutHint will not be set since no logout request was configured");return e&&null===e.postLogoutRedirectUri?this.logger.verbose("postLogoutRedirectUri passed as null, not setting post logout redirect uri",t.correlationId):e&&e.postLogoutRedirectUri?(this.logger.verbose("Setting postLogoutRedirectUri to uri set on logout request",t.correlationId),t.postLogoutRedirectUri=e7.getAbsoluteUrl(e.postLogoutRedirectUri,r2())):null===this.config.auth.postLogoutRedirectUri?this.logger.verbose("postLogoutRedirectUri configured as null and no uri set on request, not passing post logout redirect",t.correlationId):this.config.auth.postLogoutRedirectUri?(this.logger.verbose("Setting postLogoutRedirectUri to configured uri",t.correlationId),t.postLogoutRedirectUri=e7.getAbsoluteUrl(this.config.auth.postLogoutRedirectUri,r2())):(this.logger.verbose("Setting postLogoutRedirectUri to current page",t.correlationId),t.postLogoutRedirectUri=e7.getAbsoluteUrl(r2(),r2())),t}getLogoutHintFromIdTokenClaims(e){let t=e.idTokenClaims;if(t){if(t.login_hint)return t.login_hint;this.logger.verbose("The ID Token Claims tied to the provided account do not contain a login_hint claim, logoutHint will not be added to logout request")}else this.logger.verbose("The provided account does not contain ID Token Claims, logoutHint will not be added to logout request");return null}async createAuthCodeClient(e){return this.performanceClient.addQueueMeasurement(tv.StandardInteractionClientCreateAuthCodeClient,this.correlationId),new n6(await nm(this.getClientConfiguration.bind(this),tv.StandardInteractionClientGetClientConfiguration,this.logger,this.performanceClient,this.correlationId)(e),this.performanceClient)}async getClientConfiguration(e){let{serverTelemetryManager:t,requestAuthority:r,requestAzureCloudOptions:n,requestExtraQueryParameters:i,account:o}=e;this.performanceClient.addQueueMeasurement(tv.StandardInteractionClientGetClientConfiguration,this.correlationId);let s=await nm(this.getDiscoveredAuthority.bind(this),tv.StandardInteractionClientGetDiscoveredAuthority,this.logger,this.performanceClient,this.correlationId)({requestAuthority:r,requestAzureCloudOptions:n,requestExtraQueryParameters:i,account:o}),c=this.config.system.loggerOptions;return{authOptions:{clientId:this.config.auth.clientId,authority:s,clientCapabilities:this.config.auth.clientCapabilities,redirectUri:this.config.auth.redirectUri},systemOptions:{tokenRenewalOffsetSeconds:this.config.system.tokenRenewalOffsetSeconds,preventCorsPreflight:!0},loggerOptions:{loggerCallback:c.loggerCallback,piiLoggingEnabled:c.piiLoggingEnabled,logLevel:c.logLevel,correlationId:this.correlationId},cacheOptions:{claimsBasedCachingEnabled:this.config.cache.claimsBasedCachingEnabled},cryptoInterface:this.browserCrypto,networkInterface:this.networkClient,storageInterface:this.browserStorage,serverTelemetryManager:t,libraryInfo:{sku:tS.MSAL_SKU,version:r7,cpu:a.gT.EMPTY_STRING,os:a.gT.EMPTY_STRING},telemetry:this.config.telemetry}}async initializeAuthorizationRequest(e,t){this.performanceClient.addQueueMeasurement(tv.StandardInteractionClientInitializeAuthorizationRequest,this.correlationId);let r=this.getRedirectUri(e.redirectUri),n=nb.setRequestState(this.browserCrypto,e&&e.state||a.gT.EMPTY_STRING,{interactionType:t}),i={...await nm(ie,tv.InitializeBaseRequest,this.logger,this.performanceClient,this.correlationId)({...e,correlationId:this.correlationId},this.config,this.performanceClient,this.logger),redirectUri:r,state:n,nonce:e.nonce||rz(),responseMode:this.config.auth.OIDCOptions.serverResponseType};if(e.loginHint||e.sid)return i;let o=e.account||this.browserStorage.getActiveAccount();if(o&&(this.logger.verbose("Setting validated request account",this.correlationId),this.logger.verbosePii(`Setting validated request account: ${o.homeAccountId}`,this.correlationId),i.account=o),!i.loginHint&&!o){let e=this.browserStorage.getLegacyLoginHint();e&&(i.loginHint=e)}return i}}let ii="user_switch",io={[ii]:"User attempted to switch accounts in the native broker, which is not allowed. All new accounts must sign-in through the standard web flow first, please try again."};class ia extends u{constructor(e,t,r){super(e,t),Object.setPrototypeOf(this,ia.prototype),this.name="NativeAuthError",this.ext=r}}function is(e){return!!e.ext&&!!e.ext.status&&("PERSISTENT_ERROR"===e.ext.status||"DISABLED"===e.ext.status)||!!e.ext&&!!e.ext.error&&-**********===e.ext.error||"ContentError"===e.errorCode}function ic(e,t,r){if(r&&r.status)switch(r.status){case"ACCOUNT_UNAVAILABLE":return n_(nI);case"USER_INTERACTION_REQUIRED":return new nk(e,t);case"USER_CANCEL":return r_(tX);case"NO_NETWORK":return r_(ro)}return new ia(e,io[e]||t,r)}class il extends nX{constructor(e,t){super(e,t)}async acquireToken(e){this.performanceClient?.addQueueMeasurement(tv.RefreshTokenClientAcquireToken,e.correlationId);let t=eu(),r=await nm(this.executeTokenRequest.bind(this),tv.RefreshTokenClientExecuteTokenRequest,this.logger,this.performanceClient,e.correlationId)(e,this.authority),n=r.headers?.[a.SZ.X_MS_REQUEST_ID],i=new n1(this.config.authOptions.clientId,this.cacheManager,this.cryptoUtils,this.logger,this.config.serializableCache,this.config.persistencePlugin);return i.validateTokenResponse(r.body),nm(i.handleServerTokenResponse.bind(i),tv.HandleServerTokenResponse,this.logger,this.performanceClient,e.correlationId)(r.body,this.authority,t,e,void 0,void 0,!0,e.forceCache,n)}async acquireTokenByRefreshToken(e){if(!e)throw eY(eU);if(this.performanceClient?.addQueueMeasurement(tv.RefreshTokenClientAcquireTokenByRefreshToken,e.correlationId),!e.account)throw ea(D);if(this.cacheManager.isAppMetadataFOCI(e.account.environment))try{return await nm(this.acquireTokenWithCachedRefreshToken.bind(this),tv.RefreshTokenClientAcquireTokenWithCachedRefreshToken,this.logger,this.performanceClient,e.correlationId)(e,!0)}catch(r){let e=r instanceof nk&&r.errorCode===nT,t=r instanceof nF&&r.errorCode===a.D1.INVALID_GRANT_ERROR&&r.subError===a.D1.CLIENT_MISMATCH_ERROR;if(e||t);else throw r}return nm(this.acquireTokenWithCachedRefreshToken.bind(this),tv.RefreshTokenClientAcquireTokenWithCachedRefreshToken,this.logger,this.performanceClient,e.correlationId)(e,!1)}async acquireTokenWithCachedRefreshToken(e,t){this.performanceClient?.addQueueMeasurement(tv.RefreshTokenClientAcquireTokenWithCachedRefreshToken,e.correlationId);let r=np(this.cacheManager.getRefreshToken.bind(this.cacheManager),tv.CacheManagerGetRefreshToken,this.logger,this.performanceClient,e.correlationId)(e.account,t,void 0,this.performanceClient,e.correlationId);if(!r)throw n_(nT);if(r.expiresOn&&eg(r.expiresOn,e.refreshTokenExpirationOffsetSeconds||300))throw n_(nv);let n={...e,refreshToken:r.secret,authenticationScheme:e.authenticationScheme||a.hO.BEARER,ccsCredential:{credential:e.account.homeAccountId,type:nO.HOME_ACCOUNT_ID}};try{return await nm(this.acquireToken.bind(this),tv.RefreshTokenClientAcquireToken,this.logger,this.performanceClient,e.correlationId)(n)}catch(e){if(e instanceof nk&&e.subError===nA){this.logger.verbose("acquireTokenWithRefreshToken: bad refresh token, removing from cache");let e=ep(r);this.cacheManager.removeRefreshToken(e)}throw e}}async executeTokenRequest(e,t){this.performanceClient?.addQueueMeasurement(tv.RefreshTokenClientExecuteTokenRequest,e.correlationId);let r=this.createTokenQueryParameters(e),n=e7.appendQueryString(t.tokenEndpoint,r),i=await nm(this.createTokenRequestBody.bind(this),tv.RefreshTokenClientCreateTokenRequestBody,this.logger,this.performanceClient,e.correlationId)(e),o=this.createTokenRequestHeaders(e.ccsCredential),a={clientId:e.tokenBodyParameters?.clientId||this.config.authOptions.clientId,authority:t.canonicalAuthority,scopes:e.scopes,claims:e.claims,authenticationScheme:e.authenticationScheme,resourceRequestMethod:e.resourceRequestMethod,resourceRequestUri:e.resourceRequestUri,shrClaims:e.shrClaims,sshKid:e.sshKid};return nm(this.executePostToTokenEndpoint.bind(this),tv.RefreshTokenClientExecutePostToTokenEndpoint,this.logger,this.performanceClient,e.correlationId)(n,i,o,a,e.correlationId,tv.RefreshTokenClientExecutePostToTokenEndpoint)}async createTokenRequestBody(e){this.performanceClient?.addQueueMeasurement(tv.RefreshTokenClientCreateTokenRequestBody,e.correlationId);let t=new nY(e.correlationId,this.performanceClient);if(t.addClientId(e.embeddedClientId||e.tokenBodyParameters?.[nB]||this.config.authOptions.clientId),e.redirectUri&&t.addRedirectUri(e.redirectUri),t.addScopes(e.scopes,!0,this.config.authOptions.authority.options.OIDCOptions?.defaultScopes),t.addGrantType(a.qJ.REFRESH_TOKEN_GRANT),t.addClientInfo(),t.addLibraryInfo(this.config.libraryInfo),t.addApplicationTelemetry(this.config.telemetry.application),t.addThrottling(),this.serverTelemetryManager&&!tT(this.config)&&t.addServerTelemetry(this.serverTelemetryManager),t.addRefreshToken(e.refreshToken),this.config.clientCredentials.clientSecret&&t.addClientSecret(this.config.clientCredentials.clientSecret),this.config.clientCredentials.clientAssertion){let r=this.config.clientCredentials.clientAssertion;t.addClientAssertion(await n2(r.assertion,this.config.authOptions.clientId,e.resourceRequestUri)),t.addClientAssertionType(r.assertionType)}if(e.authenticationScheme===a.hO.POP){let r;let n=new nZ(this.cryptoUtils,this.performanceClient);r=e.popKid?this.cryptoUtils.encodeKid(e.popKid):(await nm(n.generateCnf.bind(n),tv.PopTokenGenerateCnf,this.logger,this.performanceClient,e.correlationId)(e,this.logger)).reqCnfString,t.addPopToken(r)}else if(e.authenticationScheme===a.hO.SSH){if(e.sshJwk)t.addSshJwk(e.sshJwk);else throw eY(eB)}if((!eJ.isEmptyObj(e.claims)||this.config.authOptions.clientCapabilities&&this.config.authOptions.clientCapabilities.length>0)&&t.addClaims(e.claims,this.config.authOptions.clientCapabilities),this.config.systemOptions.preventCorsPreflight&&e.ccsCredential)switch(e.ccsCredential.type){case nO.HOME_ACCOUNT_ID:try{let r=e0(e.ccsCredential.credential);t.addCcsOid(r)}catch(e){this.logger.verbose("Could not parse home account ID for CCS Header: "+e)}break;case nO.UPN:t.addCcsUpn(e.ccsCredential.credential)}return e.embeddedClientId&&t.addBrokerParameters({brokerClientId:this.config.authOptions.clientId,brokerRedirectUri:this.config.authOptions.redirectUri}),e.tokenBodyParameters&&t.addExtraQueryParameters(e.tokenBodyParameters),t.createQueryString()}}class id extends nX{constructor(e,t){super(e,t)}async acquireToken(e){try{let[t,r]=await this.acquireCachedToken({...e,scopes:e.scopes?.length?e.scopes:[...a.f_]});return r===a.To.PROACTIVELY_REFRESHED&&(this.logger.info("SilentFlowClient:acquireCachedToken - Cached access token's refreshOn property has been exceeded'. It's not expired, but must be refreshed."),new il(this.config,this.performanceClient).acquireTokenByRefreshToken(e).catch(()=>{})),t}catch(t){if(t instanceof eo&&t.errorCode===W)return new il(this.config,this.performanceClient).acquireTokenByRefreshToken(e);throw t}}async acquireCachedToken(e){this.performanceClient?.addQueueMeasurement(tv.SilentFlowClientAcquireCachedToken,e.correlationId);let t=a.To.NOT_APPLICABLE;if(e.forceRefresh||!this.config.cacheOptions.claimsBasedCachingEnabled&&!eJ.isEmptyObj(e.claims))throw this.setCacheOutcome(a.To.FORCE_REFRESH_OR_CLAIMS,e.correlationId),ea(W);if(!e.account)throw ea(D);let r=e.account.tenantId||function(e){let t=new e7(e).getUrlComponents(),r=t.PathSegments.slice(-1)[0]?.toLowerCase();switch(r){case a.Nb.COMMON:case a.Nb.ORGANIZATIONS:case a.Nb.CONSUMERS:return;default:return r}}(e.authority),n=this.cacheManager.getTokenKeys(),i=this.cacheManager.getAccessToken(e.account,e,n,r,this.performanceClient,e.correlationId);if(i){if(Number(i.cachedAt)>eu()||eg(i.expiresOn,this.config.systemOptions.tokenRenewalOffsetSeconds))throw this.setCacheOutcome(a.To.CACHED_ACCESS_TOKEN_EXPIRED,e.correlationId),ea(W);i.refreshOn&&eg(i.refreshOn,0)&&(t=a.To.PROACTIVELY_REFRESHED)}else throw this.setCacheOutcome(a.To.NO_CACHED_ACCESS_TOKEN,e.correlationId),ea(W);let o=e.authority||this.authority.getPreferredCache(),s={account:this.cacheManager.readAccountFromCache(e.account),accessToken:i,idToken:this.cacheManager.getIdToken(e.account,n,r,this.performanceClient,e.correlationId),refreshToken:null,appMetadata:this.cacheManager.readAppMetadataFromCache(o)};return this.setCacheOutcome(t,e.correlationId),this.config.serverTelemetryManager&&this.config.serverTelemetryManager.incrementCacheHits(),[await nm(this.generateResultFromCacheRecord.bind(this),tv.SilentFlowClientGenerateResultFromCacheRecord,this.logger,this.performanceClient,e.correlationId)(s,e),t]}setCacheOutcome(e,t){this.serverTelemetryManager?.setCacheOutcome(e),this.performanceClient?.addFields({cacheOutcome:e},t),e!==a.To.NOT_APPLICABLE&&this.logger.info(`Token refresh is required due to cache outcome: ${e}`)}async generateResultFromCacheRecord(e,t){let r;if(this.performanceClient?.addQueueMeasurement(tv.SilentFlowClientGenerateResultFromCacheRecord,t.correlationId),e.idToken&&(r=ed(e.idToken.secret,this.config.cryptoInterface.base64Decode)),t.maxAge||0===t.maxAge){let e=r?.auth_time;if(!e)throw ea(k);eh(e,t.maxAge)}return n1.generateAuthenticationResult(this.cryptoUtils,this.authority,e,!0,t,r)}}class ih extends ir{async acquireToken(e){this.performanceClient.addQueueMeasurement(tv.SilentCacheClientAcquireToken,e.correlationId);let t=this.initializeServerTelemetryManager(tM.acquireTokenSilent_silentFlow),r=new id(await nm(this.getClientConfiguration.bind(this),tv.StandardInteractionClientGetClientConfiguration,this.logger,this.performanceClient,this.correlationId)({serverTelemetryManager:t,requestAuthority:e.authority,requestAzureCloudOptions:e.azureCloudOptions,account:e.account}),this.performanceClient);this.logger.verbose("Silent auth client created");try{let t=(await nm(r.acquireCachedToken.bind(r),tv.SilentFlowClientAcquireCachedToken,this.logger,this.performanceClient,e.correlationId)(e))[0];return this.performanceClient.addFields({fromCache:!0},e.correlationId),t}catch(e){throw e instanceof rR&&e.errorCode===rd&&this.logger.verbose("Signing keypair for bound access token not found. Refreshing bound access token and generating a new crypto keypair."),e}}logout(e){this.logger.verbose("logoutRedirect called");let t=this.initializeLogoutRequest(e);return this.clearCacheOnLogout(t?.account)}}class iu extends n5{constructor(e,t,r,n,i,o,a,s,c,l,d,h){super(e,t,r,n,i,o,s,c,h),this.apiId=a,this.accountId=l,this.nativeMessageHandler=c,this.nativeStorageManager=d,this.silentCacheClient=new ih(e,this.nativeStorageManager,r,n,i,o,s,c,h),this.serverTelemetryManager=this.initializeServerTelemetryManager(this.apiId);let u=this.nativeMessageHandler.getExtensionId()===tE.PREFERRED_EXTENSION_ID?"chrome":this.nativeMessageHandler.getExtensionId()?.length?"unknown":void 0;this.skus=n4.makeExtraSkuString({libraryName:tS.MSAL_SKU,libraryVersion:r7,extensionName:u,extensionVersion:this.nativeMessageHandler.getExtensionVersion()})}addRequestSKUs(e){e.extraParameters={...e.extraParameters,"x-client-xtra-sku":this.skus}}async acquireToken(e){this.performanceClient.addQueueMeasurement(tv.NativeInteractionClientAcquireToken,e.correlationId),this.logger.trace("NativeInteractionClient - acquireToken called.");let t=this.performanceClient.startMeasurement(tv.NativeInteractionClientAcquireToken,e.correlationId),r=eu();try{let n=await this.initializeNativeRequest(e);try{let e=await this.acquireTokensFromCache(this.accountId,n);return t.end({success:!0,isNativeBroker:!1,fromCache:!0}),e}catch(e){this.logger.info("MSAL internal Cache does not contain tokens, proceed to make a native call")}let{...i}=n,o={method:tk.GetToken,request:i},a=await this.nativeMessageHandler.sendMessage(o),s=this.validateNativeResponse(a);return await this.handleNativeResponse(s,n,r).then(e=>(t.end({success:!0,isNativeBroker:!0,requestId:e.requestId}),this.serverTelemetryManager.clearNativeBrokerErrorCode(),e)).catch(e=>{throw t.end({success:!1,errorCode:e.errorCode,subErrorCode:e.subError,isNativeBroker:!0}),e})}catch(e){throw e instanceof ia&&this.serverTelemetryManager.setNativeBrokerErrorCode(e.errorCode),e}}createSilentCacheRequest(e,t){return{authority:e.authority,correlationId:this.correlationId,scopes:eX.fromString(e.scope).asArray(),account:t,forceRefresh:!1}}async acquireTokensFromCache(e,t){if(!e)throw this.logger.warning("NativeInteractionClient:acquireTokensFromCache - No nativeAccountId provided"),ea(B);let r=this.browserStorage.getBaseAccountInfo({nativeAccountId:e});if(!r)throw ea(B);try{let e=this.createSilentCacheRequest(t,r),n=await this.silentCacheClient.acquireToken(e),i={...r,idTokenClaims:n?.idTokenClaims,idToken:n?.idToken};return{...n,account:i}}catch(e){throw e}}async acquireTokenRedirect(e,t){this.logger.trace("NativeInteractionClient - acquireTokenRedirect called.");let{...r}=e;delete r.onRedirectNavigate;let n=await this.initializeNativeRequest(r),i={method:tk.GetToken,request:n};try{let e=await this.nativeMessageHandler.sendMessage(i);this.validateNativeResponse(e)}catch(e){if(e instanceof ia&&(this.serverTelemetryManager.setNativeBrokerErrorCode(e.errorCode),is(e)))throw e}this.browserStorage.setTemporaryCache(tb.NATIVE_REQUEST,JSON.stringify(n),!0);let o={apiId:tM.acquireTokenRedirect,timeout:this.config.system.redirectNavigationTimeout,noHistory:!1},a=this.config.auth.navigateToLoginRequestUrl?window.location.href:this.getRedirectUri(e.redirectUri);t.end({success:!0}),await this.navigationClient.navigateExternal(a,o)}async handleRedirectPromise(e,t){if(this.logger.trace("NativeInteractionClient - handleRedirectPromise called."),!this.browserStorage.isInteractionInProgress(!0))return this.logger.info("handleRedirectPromise called but there is no interaction in progress, returning null."),null;let r=this.browserStorage.getCachedNativeRequest();if(!r)return this.logger.verbose("NativeInteractionClient - handleRedirectPromise called but there is no cached request, returning null."),e&&t&&e?.addFields({errorCode:"no_cached_request"},t),null;let{prompt:n,...i}=r;n&&this.logger.verbose("NativeInteractionClient - handleRedirectPromise called and prompt was included in the original request, removing prompt from cached request to prevent second interaction with native broker window."),this.browserStorage.removeItem(this.browserStorage.generateCacheKey(tb.NATIVE_REQUEST));let o={method:tk.GetToken,request:i},a=eu();try{this.logger.verbose("NativeInteractionClient - handleRedirectPromise sending message to native broker.");let e=await this.nativeMessageHandler.sendMessage(o);this.validateNativeResponse(e);let t=this.handleNativeResponse(e,i,a);this.browserStorage.setInteractionInProgress(!1);let r=await t;return this.serverTelemetryManager.clearNativeBrokerErrorCode(),r}catch(e){throw this.browserStorage.setInteractionInProgress(!1),e}}logout(){return this.logger.trace("NativeInteractionClient - logout called."),Promise.reject("Logout not implemented yet")}async handleNativeResponse(e,t,r){this.logger.trace("NativeInteractionClient - handleNativeResponse called.");let n=ed(e.id_token,nc),i=this.createHomeAccountIdentifier(e,n);if(i!==this.browserStorage.getAccountInfoFilteredBy({nativeAccountId:t.accountId})?.homeAccountId&&e.account.id!==t.accountId)throw ic(ii);let o=await this.getDiscoveredAuthority({requestAuthority:t.authority}),a=n3(this.browserStorage,o,i,nc,n,e.client_info,void 0,n.tid,void 0,e.account.id,this.logger),s=await this.generateAuthenticationResult(e,t,n,a,o.canonicalAuthority,r);return this.cacheAccount(a),this.cacheNativeTokens(e,t,i,n,e.access_token,s.tenantId,r),s}createHomeAccountIdentifier(e,t){return e5.generateHomeAccountId(e.client_info||a.gT.EMPTY_STRING,e6.Default,this.logger,this.browserCrypto,t)}generateScopes(e,t){return e.scope?eX.fromString(e.scope):eX.fromString(t.scope)}async generatePopAccessToken(e,t){if(t.tokenType!==a.hO.POP||!t.signPopToken)return e.access_token;{if(e.shr)return this.logger.trace("handleNativeServerResponse: SHR is enabled in native layer"),e.shr;let r=new nZ(this.browserCrypto),n={resourceRequestMethod:t.resourceRequestMethod,resourceRequestUri:t.resourceRequestUri,shrClaims:t.shrClaims,shrNonce:t.shrNonce};if(!t.keyId)throw ea(X);return r.signPopToken(e.access_token,t.keyId,n)}}async generateAuthenticationResult(e,t,r,n,i,o){let s=this.addTelemetryFromNativeResponse(e),c=e.scope?eX.fromString(e.scope):eX.fromString(t.scope),l=e.account.properties||{},d=l.UID||r.oid||r.sub||a.gT.EMPTY_STRING,h=l.TenantId||r.tid||a.gT.EMPTY_STRING,u=e2(n.getAccountInfo(),void 0,r,e.id_token);u.nativeAccountId!==e.account.id&&(u.nativeAccountId=e.account.id);let g=await this.generatePopAccessToken(e,t),p=t.tokenType===a.hO.POP?a.hO.POP:a.hO.BEARER;return{authority:i,uniqueId:d,tenantId:h,scopes:c.asArray(),account:u,idToken:e.id_token,idTokenClaims:r,accessToken:g,fromCache:!!s&&this.isResponseFromCache(s),expiresOn:new Date(1e3*Number(o+e.expires_in)),tokenType:p,correlationId:this.correlationId,state:e.state,fromNativeBroker:!0}}cacheAccount(e){this.browserStorage.setAccount(e),this.browserStorage.removeAccountContext(e).catch(e=>{this.logger.error(`Error occurred while removing account context from browser storage. ${e}`)})}cacheNativeTokens(e,t,r,n,i,o,s){let c=em(r,t.authority,e.id_token||"",t.clientId,n.tid||""),l=t.tokenType===a.hO.POP?a.gT.SHR_NONCE_VALIDITY:("string"==typeof e.expires_in?parseInt(e.expires_in,10):e.expires_in)||0,d=this.generateScopes(e,t),h=ef(r,t.authority,i,t.clientId,n.tid||o,d.printScopes(),s+l,0,nc,void 0,t.tokenType,void 0,t.keyId);this.nativeStorageManager.saveCacheRecord({idToken:c,accessToken:h},t.storeInCache)}addTelemetryFromNativeResponse(e){let t=this.getMATSFromResponse(e);return t?(this.performanceClient.addFields({extensionId:this.nativeMessageHandler.getExtensionId(),extensionVersion:this.nativeMessageHandler.getExtensionVersion(),matsBrokerVersion:t.broker_version,matsAccountJoinOnStart:t.account_join_on_start,matsAccountJoinOnEnd:t.account_join_on_end,matsDeviceJoin:t.device_join,matsPromptBehavior:t.prompt_behavior,matsApiErrorCode:t.api_error_code,matsUiVisible:t.ui_visible,matsSilentCode:t.silent_code,matsSilentBiSubCode:t.silent_bi_sub_code,matsSilentMessage:t.silent_message,matsSilentStatus:t.silent_status,matsHttpStatus:t.http_status,matsHttpEventCount:t.http_event_count},this.correlationId),t):null}validateNativeResponse(e){if(e.hasOwnProperty("access_token")&&e.hasOwnProperty("id_token")&&e.hasOwnProperty("client_info")&&e.hasOwnProperty("account")&&e.hasOwnProperty("scope")&&e.hasOwnProperty("expires_in"))return e;throw g(l,"Response missing expected properties.")}getMATSFromResponse(e){if(e.properties.MATS)try{return JSON.parse(e.properties.MATS)}catch(e){this.logger.error("NativeInteractionClient - Error parsing MATS telemetry, returning null instead")}return null}isResponseFromCache(e){return void 0===e.is_cached?(this.logger.verbose("NativeInteractionClient - MATS telemetry does not contain field indicating if response was served from cache. Returning false."),!1):!!e.is_cached}async initializeNativeRequest(e){this.logger.trace("NativeInteractionClient - initializeNativeRequest called");let t=e.authority||this.config.auth.authority;e.account&&await this.getDiscoveredAuthority({requestAuthority:t,requestAzureCloudOptions:e.azureCloudOptions,account:e.account});let r=new e7(t);r.validateAsUri();let{scopes:n,...i}=e,o=new eX(n||[]);o.appendScopes(a.f_);let s={...i,accountId:this.accountId,clientId:this.config.auth.clientId,authority:r.urlString,scope:o.printScopes(),redirectUri:this.getRedirectUri(e.redirectUri),prompt:(()=>{switch(this.apiId){case tM.ssoSilent:case tM.acquireTokenSilent_silentFlow:return this.logger.trace("initializeNativeRequest: silent request sets prompt to none"),a.NJ.NONE}if(!e.prompt){this.logger.trace("initializeNativeRequest: prompt was not provided");return}switch(e.prompt){case a.NJ.NONE:case a.NJ.CONSENT:case a.NJ.LOGIN:return this.logger.trace("initializeNativeRequest: prompt is compatible with native flow"),e.prompt;default:throw this.logger.trace(`initializeNativeRequest: prompt = ${e.prompt} is not compatible with native flow`),r_(rI)}})(),correlationId:this.correlationId,tokenType:e.authenticationScheme,windowTitleSubstring:document.title,extraParameters:{...e.extraQueryParameters,...e.tokenQueryParameters},extendedExpiryToken:!1,keyId:e.popKid};if(s.signPopToken&&e.popKid)throw r_(rA);if(this.handleExtraBrokerParams(s),s.extraParameters=s.extraParameters||{},s.extraParameters.telemetry=tE.MATS_TELEMETRY,e.authenticationScheme===a.hO.POP){let t;let r={resourceRequestUri:e.resourceRequestUri,resourceRequestMethod:e.resourceRequestMethod,shrClaims:e.shrClaims,shrNonce:e.shrNonce},n=new nZ(this.browserCrypto);if(s.keyId)t=this.browserCrypto.base64UrlEncode(JSON.stringify({kid:s.keyId})),s.signPopToken=!1;else{let i=await nm(n.generateCnf.bind(n),tv.PopTokenGenerateCnf,this.logger,this.performanceClient,e.correlationId)(r,this.logger);t=i.reqCnfString,s.keyId=i.kid,s.signPopToken=!0}s.reqCnf=t}return this.addRequestSKUs(s),s}handleExtraBrokerParams(e){let t=e.extraParameters&&e.extraParameters.hasOwnProperty(nV)&&e.extraParameters.hasOwnProperty(nQ)&&e.extraParameters.hasOwnProperty(nB);if(!e.embeddedClientId&&!t)return;let r="",n=e.redirectUri;e.embeddedClientId?(e.redirectUri=this.config.auth.redirectUri,r=e.embeddedClientId):e.extraParameters&&(e.redirectUri=e.extraParameters[nQ],r=e.extraParameters[nB]),e.extraParameters={child_client_id:r,child_redirect_uri:n},this.performanceClient?.addFields({embeddedClientId:r,embeddedRedirectUri:n},e.correlationId)}}class ig{constructor(e,t,r,n){this.logger=e,this.handshakeTimeoutMs=t,this.extensionId=n,this.resolvers=new Map,this.handshakeResolvers=new Map,this.messageChannel=new MessageChannel,this.windowListener=this.onWindowMessage.bind(this),this.performanceClient=r,this.handshakeEvent=r.startMeasurement(tv.NativeMessageHandlerHandshake)}async sendMessage(e){this.logger.trace("NativeMessageHandler - sendMessage called.");let t={channel:tE.CHANNEL_ID,extensionId:this.extensionId,responseId:rz(),body:e};return this.logger.trace("NativeMessageHandler - Sending request to browser extension"),this.logger.tracePii(`NativeMessageHandler - Sending request to browser extension: ${JSON.stringify(t)}`),this.messageChannel.port1.postMessage(t),new Promise((e,r)=>{this.resolvers.set(t.responseId,{resolve:e,reject:r})})}static async createProvider(e,t,r){e.trace("NativeMessageHandler - createProvider called.");try{let n=new ig(e,t,r,tE.PREFERRED_EXTENSION_ID);return await n.sendHandshakeRequest(),n}catch(i){let n=new ig(e,t,r);return await n.sendHandshakeRequest(),n}}async sendHandshakeRequest(){this.logger.trace("NativeMessageHandler - sendHandshakeRequest called."),window.addEventListener("message",this.windowListener,!1);let e={channel:tE.CHANNEL_ID,extensionId:this.extensionId,responseId:rz(),body:{method:tk.HandshakeRequest}};return this.handshakeEvent.add({extensionId:this.extensionId,extensionHandshakeTimeoutMs:this.handshakeTimeoutMs}),this.messageChannel.port1.onmessage=e=>{this.onChannelMessage(e)},window.postMessage(e,window.origin,[this.messageChannel.port2]),new Promise((t,r)=>{this.handshakeResolvers.set(e.responseId,{resolve:t,reject:r}),this.timeoutId=window.setTimeout(()=>{window.removeEventListener("message",this.windowListener,!1),this.messageChannel.port1.close(),this.messageChannel.port2.close(),this.handshakeEvent.end({extensionHandshakeTimedOut:!0,success:!1}),r(r_(rf)),this.handshakeResolvers.delete(e.responseId)},this.handshakeTimeoutMs)})}onWindowMessage(e){if(this.logger.trace("NativeMessageHandler - onWindowMessage called"),e.source!==window)return;let t=e.data;if(t.channel&&t.channel===tE.CHANNEL_ID&&(!t.extensionId||t.extensionId===this.extensionId)&&t.body.method===tk.HandshakeRequest){let e=this.handshakeResolvers.get(t.responseId);if(!e){this.logger.trace(`NativeMessageHandler.onWindowMessage - resolver can't be found for request ${t.responseId}`);return}this.logger.verbose(t.extensionId?`Extension with id: ${t.extensionId} not installed`:"No extension installed"),clearTimeout(this.timeoutId),this.messageChannel.port1.close(),this.messageChannel.port2.close(),window.removeEventListener("message",this.windowListener,!1),this.handshakeEvent.end({success:!1,extensionInstalled:!1}),e.reject(r_(rC))}}onChannelMessage(e){this.logger.trace("NativeMessageHandler - onChannelMessage called.");let t=e.data,r=this.resolvers.get(t.responseId),n=this.handshakeResolvers.get(t.responseId);try{let e=t.body.method;if(e===tk.Response){if(!r)return;let e=t.body.response;if(this.logger.trace("NativeMessageHandler - Received response from browser extension"),this.logger.tracePii(`NativeMessageHandler - Received response from browser extension: ${JSON.stringify(e)}`),"Success"!==e.status)r.reject(ic(e.code,e.description,e.ext));else if(e.result)e.result.code&&e.result.description?r.reject(ic(e.result.code,e.result.description,e.result.ext)):r.resolve(e.result);else throw g(l,"Event does not contain result.");this.resolvers.delete(t.responseId)}else if(e===tk.HandshakeResponse){if(!n){this.logger.trace(`NativeMessageHandler.onChannelMessage - resolver can't be found for request ${t.responseId}`);return}clearTimeout(this.timeoutId),window.removeEventListener("message",this.windowListener,!1),this.extensionId=t.extensionId,this.extensionVersion=t.body.version,this.logger.verbose(`NativeMessageHandler - Received HandshakeResponse from extension: ${this.extensionId}`),this.handshakeEvent.end({extensionInstalled:!0,success:!0}),n.resolve(),this.handshakeResolvers.delete(t.responseId)}}catch(t){this.logger.error("Error parsing response from WAM Extension"),this.logger.errorPii(`Error parsing response from WAM Extension: ${t}`),this.logger.errorPii(`Unable to parse ${e}`),r?r.reject(t):n&&n.reject(t)}}getExtensionId(){return this.extensionId}getExtensionVersion(){return this.extensionVersion}static isNativeAvailable(e,t,r,n){if(t.trace("isNativeAvailable called"),!e.system.allowNativeBroker)return t.trace("isNativeAvailable: allowNativeBroker is not enabled, returning false"),!1;if(!r)return t.trace("isNativeAvailable: WAM extension provider is not initialized, returning false"),!1;if(n)switch(n){case a.hO.BEARER:case a.hO.POP:t.trace("isNativeAvailable: authenticationScheme is supported, returning true");break;default:return t.trace("isNativeAvailable: authenticationScheme is not supported, returning false"),!1}return!0}}class ip{constructor(e,t,r,n,i){this.authModule=e,this.browserStorage=t,this.authCodeRequest=r,this.logger=n,this.performanceClient=i}async handleCodeResponse(e,t){let r;this.performanceClient.addQueueMeasurement(tv.HandleCodeResponse,t.correlationId);try{r=this.authModule.handleFragmentResponse(e,t.state)}catch(e){if(e instanceof nF&&e.subError===tX)throw r_(tX);throw e}return nm(this.handleCodeResponseFromServer.bind(this),tv.HandleCodeResponseFromServer,this.logger,this.performanceClient,t.correlationId)(r,t)}async handleCodeResponseFromServer(e,t,r=!0){if(this.performanceClient.addQueueMeasurement(tv.HandleCodeResponseFromServer,t.correlationId),this.logger.trace("InteractionHandler.handleCodeResponseFromServer called"),this.authCodeRequest.code=e.code,e.cloud_instance_host_name&&await nm(this.authModule.updateAuthority.bind(this.authModule),tv.UpdateTokenEndpointAuthority,this.logger,this.performanceClient,t.correlationId)(e.cloud_instance_host_name,t.correlationId),r&&(e.nonce=t.nonce||void 0),e.state=t.state,e.client_info)this.authCodeRequest.clientInfo=e.client_info;else{let e=this.createCcsCredentials(t);e&&(this.authCodeRequest.ccsCredential=e)}return await nm(this.authModule.acquireToken.bind(this.authModule),tv.AuthClientAcquireToken,this.logger,this.performanceClient,t.correlationId)(this.authCodeRequest,e)}createCcsCredentials(e){return e.account?{credential:e.account.homeAccountId,type:nO.HOME_ACCOUNT_ID}:e.loginHint?{credential:e.loginHint,type:nO.UPN}:null}}function im(e,t,r){let n=e8(e);if(!n){if(e9(e))throw r.error(`A ${t} is present in the iframe but it does not contain known properties. It's likely that the ${t} has been replaced by code running on the redirectUri page.`),r.errorPii(`The ${t} detected is: ${e}`),r_(tW);throw r.error(`The request has returned to the redirectUri but a ${t} is not present. It's likely that the ${t} has been removed or the page has been redirected by code running on the redirectUri page.`),r_(tz)}return n}class iC extends ir{constructor(e,t,r,n,i,o,a,s,c,l){super(e,t,r,n,i,o,a,c,l),this.unloadWindow=this.unloadWindow.bind(this),this.nativeStorage=s}acquireToken(e){try{let t={popupName:this.generatePopupName(e.scopes||a.f_,e.authority||this.config.auth.authority),popupWindowAttributes:e.popupWindowAttributes||{},popupWindowParent:e.popupWindowParent??window};if(this.config.system.asyncPopups)return this.logger.verbose("asyncPopups set to true, acquiring token"),this.acquireTokenPopupAsync(e,t);return this.logger.verbose("asyncPopup set to false, opening popup before acquiring token"),t.popup=this.openSizedPopup("about:blank",t),this.acquireTokenPopupAsync(e,t)}catch(e){return Promise.reject(e)}}logout(e){try{this.logger.verbose("logoutPopup called");let t=this.initializeLogoutRequest(e),r={popupName:this.generateLogoutPopupName(t),popupWindowAttributes:e?.popupWindowAttributes||{},popupWindowParent:e?.popupWindowParent??window},n=e&&e.authority,i=e&&e.mainWindowRedirectUri;if(this.config.system.asyncPopups)return this.logger.verbose("asyncPopups set to true"),this.logoutPopupAsync(t,r,n,i);return this.logger.verbose("asyncPopup set to false, opening popup"),r.popup=this.openSizedPopup("about:blank",r),this.logoutPopupAsync(t,r,n,i)}catch(e){return Promise.reject(e)}}async acquireTokenPopupAsync(e,t){this.logger.verbose("acquireTokenPopupAsync called");let r=this.initializeServerTelemetryManager(tM.acquireTokenPopup),n=await nm(this.initializeAuthorizationRequest.bind(this),tv.StandardInteractionClientInitializeAuthorizationRequest,this.logger,this.performanceClient,this.correlationId)(e,i.Popup);r8(n.authority);try{let o;let a=await nm(this.initializeAuthorizationCodeRequest.bind(this),tv.StandardInteractionClientInitializeAuthorizationCodeRequest,this.logger,this.performanceClient,this.correlationId)(n),s=await nm(this.createAuthCodeClient.bind(this),tv.StandardInteractionClientCreateAuthCodeClient,this.logger,this.performanceClient,this.correlationId)({serverTelemetryManager:r,requestAuthority:n.authority,requestAzureCloudOptions:n.azureCloudOptions,requestExtraQueryParameters:n.extraQueryParameters,account:n.account}),c=ig.isNativeAvailable(this.config,this.logger,this.nativeMessageHandler,e.authenticationScheme);c&&(o=this.performanceClient.startMeasurement(tv.FetchAccountIdWithNativeBroker,e.correlationId));let l=await s.getAuthCodeUrl({...n,nativeBroker:c}),d=new ip(s,this.browserStorage,a,this.logger,this.performanceClient),h=this.initiateAuthRequest(l,t);this.eventHandler.emitEvent(nL.POPUP_OPENED,i.Popup,{popupWindow:h},null);let u=await this.monitorPopupForHash(h,t.popupWindowParent),g=np(im,tv.DeserializeResponse,this.logger,this.performanceClient,this.correlationId)(u,this.config.auth.OIDCOptions.serverResponseType,this.logger);if(nx.removeThrottle(this.browserStorage,this.config.auth.clientId,a),g.accountId){if(this.logger.verbose("Account id found in hash, calling WAM for token"),o&&o.end({success:!0,isNativeBroker:!0}),!this.nativeMessageHandler)throw r_(ry);let e=new iu(this.config,this.browserStorage,this.browserCrypto,this.logger,this.eventHandler,this.navigationClient,tM.acquireTokenPopup,this.performanceClient,this.nativeMessageHandler,g.accountId,this.nativeStorage,n.correlationId),{userRequestState:t}=nb.parseRequestState(this.browserCrypto,n.state);return await e.acquireToken({...n,state:t,prompt:void 0})}return await d.handleCodeResponse(g,n)}catch(e){throw t.popup?.close(),e instanceof u&&(e.setCorrelationId(this.correlationId),r.cacheFailedRequest(e)),e}}async logoutPopupAsync(e,t,r,n){this.logger.verbose("logoutPopupAsync called"),this.eventHandler.emitEvent(nL.LOGOUT_START,i.Popup,e);let o=this.initializeServerTelemetryManager(tM.logoutPopup);try{await this.clearCacheOnLogout(e.account);let a=await nm(this.createAuthCodeClient.bind(this),tv.StandardInteractionClientCreateAuthCodeClient,this.logger,this.performanceClient,this.correlationId)({serverTelemetryManager:o,requestAuthority:r,account:e.account||void 0});try{a.authority.endSessionEndpoint}catch{if(e.account?.homeAccountId&&e.postLogoutRedirectUri&&a.authority.protocolMode===s.OIDC){if(this.browserStorage.removeAccount(e.account?.homeAccountId),this.eventHandler.emitEvent(nL.LOGOUT_SUCCESS,i.Popup,e),n){let e={apiId:tM.logoutPopup,timeout:this.config.system.redirectNavigationTimeout,noHistory:!1},t=e7.getAbsoluteUrl(n,r2());await this.navigationClient.navigateInternal(t,e)}t.popup?.close();return}}let c=a.getLogoutUri(e);this.eventHandler.emitEvent(nL.LOGOUT_SUCCESS,i.Popup,e);let l=this.openPopup(c,t);if(this.eventHandler.emitEvent(nL.POPUP_OPENED,i.Popup,{popupWindow:l},null),await this.monitorPopupForHash(l,t.popupWindowParent).catch(()=>{}),n){let e={apiId:tM.logoutPopup,timeout:this.config.system.redirectNavigationTimeout,noHistory:!1},t=e7.getAbsoluteUrl(n,r2());this.logger.verbose("Redirecting main window to url specified in the request"),this.logger.verbosePii(`Redirecting main window to: ${t}`),await this.navigationClient.navigateInternal(t,e)}else this.logger.verbose("No main window navigation requested")}catch(e){throw t.popup?.close(),e instanceof u&&(e.setCorrelationId(this.correlationId),o.cacheFailedRequest(e)),this.browserStorage.setInteractionInProgress(!1),this.eventHandler.emitEvent(nL.LOGOUT_FAILURE,i.Popup,null,e),this.eventHandler.emitEvent(nL.LOGOUT_END,i.Popup),e}this.eventHandler.emitEvent(nL.LOGOUT_END,i.Popup)}initiateAuthRequest(e,t){if(e)return this.logger.infoPii(`Navigate to: ${e}`),this.openPopup(e,t);throw this.logger.error("Navigate url is empty"),r_(tG)}monitorPopupForHash(e,t){return new Promise((t,r)=>{this.logger.verbose("PopupHandler.monitorPopupForHash - polling started");let n=setInterval(()=>{if(e.closed){this.logger.error("PopupHandler.monitorPopupForHash - window closed"),clearInterval(n),r(r_(tX));return}let i="";try{i=e.location.href}catch(e){}if(!i||"about:blank"===i)return;clearInterval(n);let o="",s=this.config.auth.OIDCOptions.serverResponseType;e&&(o=s===a.rg.QUERY?e.location.search:e.location.hash),this.logger.verbose("PopupHandler.monitorPopupForHash - popup window is on same origin as caller"),t(o)},this.config.system.pollIntervalMilliseconds)}).finally(()=>{this.cleanPopup(e,t)})}openPopup(e,t){try{let r;if(t.popup?(r=t.popup,this.logger.verbosePii(`Navigating popup window to: ${e}`),r.location.assign(e)):void 0===t.popup&&(this.logger.verbosePii(`Opening popup window to: ${e}`),r=this.openSizedPopup(e,t)),!r)throw r_(tJ);return r.focus&&r.focus(),this.currentWindow=r,t.popupWindowParent.addEventListener("beforeunload",this.unloadWindow),r}catch(e){throw this.logger.error("error opening popup "+e.message),this.browserStorage.setInteractionInProgress(!1),r_(tY)}}openSizedPopup(e,{popupName:t,popupWindowAttributes:r,popupWindowParent:n}){let i=n.screenLeft?n.screenLeft:n.screenX,o=n.screenTop?n.screenTop:n.screenY,a=n.innerWidth||document.documentElement.clientWidth||document.body.clientWidth,s=n.innerHeight||document.documentElement.clientHeight||document.body.clientHeight,c=r.popupSize?.width,l=r.popupSize?.height,d=r.popupPosition?.top,h=r.popupPosition?.left;return(!c||c<0||c>a)&&(this.logger.verbose("Default popup window width used. Window width not configured or invalid."),c=tS.POPUP_WIDTH),(!l||l<0||l>s)&&(this.logger.verbose("Default popup window height used. Window height not configured or invalid."),l=tS.POPUP_HEIGHT),(!d||d<0||d>s)&&(this.logger.verbose("Default popup window top position used. Window top not configured or invalid."),d=Math.max(0,s/2-tS.POPUP_HEIGHT/2+o)),(!h||h<0||h>a)&&(this.logger.verbose("Default popup window left position used. Window left not configured or invalid."),h=Math.max(0,a/2-tS.POPUP_WIDTH/2+i)),n.open(e,t,`width=${c}, height=${l}, top=${d}, left=${h}, scrollbars=yes`)}unloadWindow(e){this.browserStorage.cleanRequestByInteractionType(i.Popup),this.currentWindow&&this.currentWindow.close(),e.preventDefault()}cleanPopup(e,t){e.close(),t.removeEventListener("beforeunload",this.unloadWindow),this.browserStorage.setInteractionInProgress(!1)}generatePopupName(e,t){return`${tS.POPUP_NAME_PREFIX}.${this.config.auth.clientId}.${e.join("-")}.${t}.${this.correlationId}`}generateLogoutPopupName(e){let t=e.account&&e.account.homeAccountId;return`${tS.POPUP_NAME_PREFIX}.${this.config.auth.clientId}.${t}.${this.correlationId}`}}class iy{constructor(e,t,r,n,i){this.authModule=e,this.browserStorage=t,this.authCodeRequest=r,this.logger=n,this.performanceClient=i}async initiateAuthRequest(e,t){if(this.logger.verbose("RedirectHandler.initiateAuthRequest called"),e){t.redirectStartPage&&(this.logger.verbose("RedirectHandler.initiateAuthRequest: redirectStartPage set, caching start page"),this.browserStorage.setTemporaryCache(tb.ORIGIN_URI,t.redirectStartPage,!0)),this.browserStorage.setTemporaryCache(tb.CORRELATION_ID,this.authCodeRequest.correlationId,!0),this.browserStorage.cacheCodeRequest(this.authCodeRequest),this.logger.infoPii(`RedirectHandler.initiateAuthRequest: Navigate to: ${e}`);let r={apiId:tM.acquireTokenRedirect,timeout:t.redirectTimeout,noHistory:!1};if("function"==typeof t.onRedirectNavigate){if(this.logger.verbose("RedirectHandler.initiateAuthRequest: Invoking onRedirectNavigate callback"),!1!==t.onRedirectNavigate(e)){this.logger.verbose("RedirectHandler.initiateAuthRequest: onRedirectNavigate did not return false, navigating"),await t.navigationClient.navigateExternal(e,r);return}this.logger.verbose("RedirectHandler.initiateAuthRequest: onRedirectNavigate returned false, stopping navigation");return}this.logger.verbose("RedirectHandler.initiateAuthRequest: Navigating window to navigate url"),await t.navigationClient.navigateExternal(e,r);return}throw this.logger.info("RedirectHandler.initiateAuthRequest: Navigate url is empty"),r_(tG)}async handleCodeResponse(e,t){let r;this.logger.verbose("RedirectHandler.handleCodeResponse called"),this.browserStorage.setInteractionInProgress(!1);let n=this.browserStorage.generateStateKey(t),i=this.browserStorage.getTemporaryCache(n);if(!i)throw ea(S,"Cached State");try{r=this.authModule.handleFragmentResponse(e,i)}catch(e){if(e instanceof nF&&e.subError===tX)throw r_(tX);throw e}let o=this.browserStorage.generateNonceKey(i),a=this.browserStorage.getTemporaryCache(o);if(this.authCodeRequest.code=r.code,r.cloud_instance_host_name&&await nm(this.authModule.updateAuthority.bind(this.authModule),tv.UpdateTokenEndpointAuthority,this.logger,this.performanceClient,this.authCodeRequest.correlationId)(r.cloud_instance_host_name,this.authCodeRequest.correlationId),r.nonce=a||void 0,r.state=i,r.client_info)this.authCodeRequest.clientInfo=r.client_info;else{let e=this.checkCcsCredentials();e&&(this.authCodeRequest.ccsCredential=e)}let s=await this.authModule.acquireToken(this.authCodeRequest,r);return this.browserStorage.cleanRequestByState(t),s}checkCcsCredentials(){let e=this.browserStorage.getTemporaryCache(tb.CCS_CREDENTIAL,!0);if(e)try{return JSON.parse(e)}catch(t){this.authModule.logger.error("Cache credential could not be parsed"),this.authModule.logger.errorPii(`Cache credential could not be parsed: ${e}`)}return null}}class iT extends ir{constructor(e,t,r,n,i,o,a,s,c,l){super(e,t,r,n,i,o,a,c,l),this.nativeStorage=s}async acquireToken(e){let t=await nm(this.initializeAuthorizationRequest.bind(this),tv.StandardInteractionClientInitializeAuthorizationRequest,this.logger,this.performanceClient,this.correlationId)(e,i.Redirect);this.browserStorage.updateCacheEntries(t.state,t.nonce,t.authority,t.loginHint||"",t.account||null);let r=this.initializeServerTelemetryManager(tM.acquireTokenRedirect),n=e=>{e.persisted&&(this.logger.verbose("Page was restored from back/forward cache. Clearing temporary cache."),this.browserStorage.cleanRequestByState(t.state),this.eventHandler.emitEvent(nL.RESTORE_FROM_BFCACHE,i.Redirect))};try{let i=await nm(this.initializeAuthorizationCodeRequest.bind(this),tv.StandardInteractionClientInitializeAuthorizationCodeRequest,this.logger,this.performanceClient,this.correlationId)(t),o=await nm(this.createAuthCodeClient.bind(this),tv.StandardInteractionClientCreateAuthCodeClient,this.logger,this.performanceClient,this.correlationId)({serverTelemetryManager:r,requestAuthority:t.authority,requestAzureCloudOptions:t.azureCloudOptions,requestExtraQueryParameters:t.extraQueryParameters,account:t.account}),a=new iy(o,this.browserStorage,i,this.logger,this.performanceClient),s=await o.getAuthCodeUrl({...t,nativeBroker:ig.isNativeAvailable(this.config,this.logger,this.nativeMessageHandler,e.authenticationScheme)}),c=this.getRedirectStartPage(e.redirectStartPage);return this.logger.verbosePii(`Redirect start page: ${c}`),window.addEventListener("pageshow",n),await a.initiateAuthRequest(s,{navigationClient:this.navigationClient,redirectTimeout:this.config.system.redirectNavigationTimeout,redirectStartPage:c,onRedirectNavigate:e.onRedirectNavigate||this.config.auth.onRedirectNavigate})}catch(e){throw e instanceof u&&(e.setCorrelationId(this.correlationId),r.cacheFailedRequest(e)),window.removeEventListener("pageshow",n),this.browserStorage.cleanRequestByState(t.state),e}}async handleRedirectPromise(e="",t){let r=this.initializeServerTelemetryManager(tM.handleRedirectPromise);try{if(!this.browserStorage.isInteractionInProgress(!0))return this.logger.info("handleRedirectPromise called but there is no interaction in progress, returning null."),null;let[n,o]=this.getRedirectResponse(e||"");if(!n)return this.logger.info("handleRedirectPromise did not detect a response as a result of a redirect. Cleaning temporary cache."),this.browserStorage.cleanRequestByInteractionType(i.Redirect),"back_forward"!==function(){if("undefined"==typeof window||void 0===window.performance||"function"!=typeof window.performance.getEntriesByType)return;let e=window.performance.getEntriesByType("navigation"),t=e.length?e[0]:void 0;return t?.type}()?t.event.errorCode="no_server_response":this.logger.verbose("Back navigation event detected. Muting no_server_response error"),null;let s=this.browserStorage.getTemporaryCache(tb.ORIGIN_URI,!0)||a.gT.EMPTY_STRING,c=e7.removeHashFromUrl(s),l=e7.removeHashFromUrl(window.location.href);if(c===l&&this.config.auth.navigateToLoginRequestUrl)return this.logger.verbose("Current page is loginRequestUrl, handling response"),s.indexOf("#")>-1&&function(e){let t=e.split("#");t.shift(),window.location.hash=t.length>0?t.join("#"):""}(s),await this.handleResponse(n,r);if(!this.config.auth.navigateToLoginRequestUrl)return this.logger.verbose("NavigateToLoginRequestUrl set to false, handling response"),await this.handleResponse(n,r);if(!r3()||this.config.system.allowRedirectInIframe){this.browserStorage.setTemporaryCache(tb.URL_HASH,o,!0);let e={apiId:tM.handleRedirectPromise,timeout:this.config.system.redirectNavigationTimeout,noHistory:!0},t=!0;if(s&&"null"!==s)this.logger.verbose(`Navigating to loginRequestUrl: ${s}`),t=await this.navigationClient.navigateInternal(s,e);else{let r=function(){let e=new e7(window.location.href).getUrlComponents();return`${e.Protocol}//${e.HostNameAndPort}/`}();this.browserStorage.setTemporaryCache(tb.ORIGIN_URI,r,!0),this.logger.warning("Unable to get valid login request url from cache, redirecting to home page"),t=await this.navigationClient.navigateInternal(r,e)}if(!t)return await this.handleResponse(n,r)}return null}catch(e){throw e instanceof u&&(e.setCorrelationId(this.correlationId),r.cacheFailedRequest(e)),this.browserStorage.cleanRequestByInteractionType(i.Redirect),e}}getRedirectResponse(e){this.logger.verbose("getRedirectResponseHash called");let t=e;t||(t=this.config.auth.OIDCOptions.serverResponseType===a.rg.QUERY?window.location.search:window.location.hash);let r=e8(t);if(r){var n;try{!function(e,t,r){if(!e.state)throw r_(t$);let n=nM(t,e.state);if(!n)throw r_(tV);if(n.interactionType!==r)throw r_(tQ)}(r,this.browserCrypto,i.Redirect)}catch(e){return e instanceof u&&this.logger.error(`Interaction type validation failed due to ${e.errorCode}: ${e.errorMessage}`),[null,""]}return(n=window).location.hash="","function"==typeof n.history.replaceState&&n.history.replaceState(null,"",`${n.location.origin}${n.location.pathname}${n.location.search}`),this.logger.verbose("Hash contains known properties, returning response hash"),[r,t]}let o=this.browserStorage.getTemporaryCache(tb.URL_HASH,!0);return(this.browserStorage.removeItem(this.browserStorage.generateCacheKey(tb.URL_HASH)),o&&(r=e8(o)))?(this.logger.verbose("Hash does not contain known properties, returning cached hash"),[r,o]):[null,""]}async handleResponse(e,t){let r=e.state;if(!r)throw r_(t$);let n=this.browserStorage.getCachedRequest(r);if(this.logger.verbose("handleResponse called, retrieved cached request"),e.accountId){if(this.logger.verbose("Account id found in hash, calling WAM for token"),!this.nativeMessageHandler)throw r_(ry);let t=new iu(this.config,this.browserStorage,this.browserCrypto,this.logger,this.eventHandler,this.navigationClient,tM.acquireTokenPopup,this.performanceClient,this.nativeMessageHandler,e.accountId,this.nativeStorage,n.correlationId),{userRequestState:i}=nb.parseRequestState(this.browserCrypto,r);return t.acquireToken({...n,state:i,prompt:void 0}).finally(()=>{this.browserStorage.cleanRequestByState(r)})}let i=this.browserStorage.getCachedAuthority(r);if(!i)throw r_(re);let o=await nm(this.createAuthCodeClient.bind(this),tv.StandardInteractionClientCreateAuthCodeClient,this.logger,this.performanceClient,this.correlationId)({serverTelemetryManager:t,requestAuthority:i});return nx.removeThrottle(this.browserStorage,this.config.auth.clientId,n),new iy(o,this.browserStorage,n,this.logger,this.performanceClient).handleCodeResponse(e,r)}async logout(e){this.logger.verbose("logoutRedirect called");let t=this.initializeLogoutRequest(e),r=this.initializeServerTelemetryManager(tM.logout);try{this.eventHandler.emitEvent(nL.LOGOUT_START,i.Redirect,e),await this.clearCacheOnLogout(t.account);let n={apiId:tM.logout,timeout:this.config.system.redirectNavigationTimeout,noHistory:!1},o=await nm(this.createAuthCodeClient.bind(this),tv.StandardInteractionClientCreateAuthCodeClient,this.logger,this.performanceClient,this.correlationId)({serverTelemetryManager:r,requestAuthority:e&&e.authority,requestExtraQueryParameters:e?.extraQueryParameters,account:e&&e.account||void 0});if(o.authority.protocolMode===s.OIDC)try{o.authority.endSessionEndpoint}catch{if(t.account?.homeAccountId){this.browserStorage.removeAccount(t.account?.homeAccountId),this.eventHandler.emitEvent(nL.LOGOUT_SUCCESS,i.Redirect,t);return}}let a=o.getLogoutUri(t);if(this.eventHandler.emitEvent(nL.LOGOUT_SUCCESS,i.Redirect,t),e&&"function"==typeof e.onRedirectNavigate){let t=e.onRedirectNavigate(a);if(!1!==t){this.logger.verbose("Logout onRedirectNavigate did not return false, navigating"),this.browserStorage.getInteractionInProgress()||this.browserStorage.setInteractionInProgress(!0),await this.navigationClient.navigateExternal(a,n);return}this.browserStorage.setInteractionInProgress(!1),this.logger.verbose("Logout onRedirectNavigate returned false, stopping navigation")}else{this.browserStorage.getInteractionInProgress()||this.browserStorage.setInteractionInProgress(!0),await this.navigationClient.navigateExternal(a,n);return}}catch(e){throw e instanceof u&&(e.setCorrelationId(this.correlationId),r.cacheFailedRequest(e)),this.eventHandler.emitEvent(nL.LOGOUT_FAILURE,i.Redirect,null,e),this.eventHandler.emitEvent(nL.LOGOUT_END,i.Redirect),e}this.eventHandler.emitEvent(nL.LOGOUT_END,i.Redirect)}getRedirectStartPage(e){let t=e||window.location.href;return e7.getAbsoluteUrl(t,r2())}}async function iI(e,t,r,n,i){if(t.addQueueMeasurement(tv.SilentHandlerInitiateAuthRequest,n),!e)throw r.info("Navigate url is empty"),r_(tG);return i?nm(iA,tv.SilentHandlerLoadFrame,r,t,n)(e,i,t,n):np(iw,tv.SilentHandlerLoadFrameSync,r,t,n)(e)}async function iv(e,t,r,n,i,o,s){return n.addQueueMeasurement(tv.SilentHandlerMonitorIframeForHash,o),new Promise((n,o)=>{t<1e4&&i.warning(`system.loadFrameTimeout or system.iframeHashTimeout set to lower (${t}ms) than the default (10000ms). This may result in timeouts.`);let c=window.setTimeout(()=>{window.clearInterval(l),o(r_(t0))},t),l=window.setInterval(()=>{let t="",r=e.contentWindow;try{t=r?r.location.href:""}catch(e){}if(!t||"about:blank"===t)return;let i="";r&&(i=s===a.rg.QUERY?r.location.search:r.location.hash),window.clearTimeout(c),window.clearInterval(l),n(i)},r)}).finally(()=>{np(iE,tv.RemoveHiddenIframe,i,n,o)(e)})}function iA(e,t,r,n){return r.addQueueMeasurement(tv.SilentHandlerLoadFrame,n),new Promise((r,n)=>{let i=iS();window.setTimeout(()=>{if(!i){n("Unable to load iframe");return}i.src=e,r(i)},t)})}function iw(e){let t=iS();return t.src=e,t}function iS(){let e=document.createElement("iframe");return e.className="msalSilentIframe",e.style.visibility="hidden",e.style.position="absolute",e.style.width=e.style.height="0",e.style.border="0",e.setAttribute("sandbox","allow-scripts allow-same-origin allow-forms"),document.body.appendChild(e),e}function iE(e){document.body===e.parentNode&&document.body.removeChild(e)}class ik extends ir{constructor(e,t,r,n,i,o,a,s,c,l,d){super(e,t,r,n,i,o,s,l,d),this.apiId=a,this.nativeStorage=c}async acquireToken(e){let t;this.performanceClient.addQueueMeasurement(tv.SilentIframeClientAcquireToken,e.correlationId),e.loginHint||e.sid||e.account&&e.account.username||this.logger.warning("No user hint provided. The authorization server may need more information to complete this request.");let r={...e};r.prompt?r.prompt!==a.NJ.NONE&&r.prompt!==a.NJ.NO_SESSION&&(this.logger.warning(`SilentIframeClient. Replacing invalid prompt ${r.prompt} with ${a.NJ.NONE}`),r.prompt=a.NJ.NONE):r.prompt=a.NJ.NONE;let n=await nm(this.initializeAuthorizationRequest.bind(this),tv.StandardInteractionClientInitializeAuthorizationRequest,this.logger,this.performanceClient,e.correlationId)(r,i.Silent);r8(n.authority);let o=this.initializeServerTelemetryManager(this.apiId);try{return t=await nm(this.createAuthCodeClient.bind(this),tv.StandardInteractionClientCreateAuthCodeClient,this.logger,this.performanceClient,e.correlationId)({serverTelemetryManager:o,requestAuthority:n.authority,requestAzureCloudOptions:n.azureCloudOptions,requestExtraQueryParameters:n.extraQueryParameters,account:n.account}),await nm(this.silentTokenHelper.bind(this),tv.SilentIframeClientTokenHelper,this.logger,this.performanceClient,e.correlationId)(t,n)}catch(a){if(a instanceof u&&(a.setCorrelationId(this.correlationId),o.cacheFailedRequest(a)),!t||!(a instanceof u)||a.errorCode!==tS.INVALID_GRANT_ERROR)throw a;this.performanceClient.addFields({retryError:a.errorCode},this.correlationId);let n=await nm(this.initializeAuthorizationRequest.bind(this),tv.StandardInteractionClientInitializeAuthorizationRequest,this.logger,this.performanceClient,e.correlationId)(r,i.Silent);return await nm(this.silentTokenHelper.bind(this),tv.SilentIframeClientTokenHelper,this.logger,this.performanceClient,this.correlationId)(t,n)}}logout(){return Promise.reject(r_(t4))}async silentTokenHelper(e,t){let r=t.correlationId;this.performanceClient.addQueueMeasurement(tv.SilentIframeClientTokenHelper,r);let n=await nm(this.initializeAuthorizationCodeRequest.bind(this),tv.StandardInteractionClientInitializeAuthorizationCodeRequest,this.logger,this.performanceClient,r)(t),i=await nm(e.getAuthCodeUrl.bind(e),tv.GetAuthCodeUrl,this.logger,this.performanceClient,r)({...t,nativeBroker:ig.isNativeAvailable(this.config,this.logger,this.nativeMessageHandler,t.authenticationScheme)}),o=new ip(e,this.browserStorage,n,this.logger,this.performanceClient),s=await nm(iI,tv.SilentHandlerInitiateAuthRequest,this.logger,this.performanceClient,r)(i,this.performanceClient,this.logger,r,this.config.system.navigateFrameWait),c=this.config.auth.OIDCOptions.serverResponseType,l=await nm(iv,tv.SilentHandlerMonitorIframeForHash,this.logger,this.performanceClient,r)(s,this.config.system.iframeHashTimeout,this.config.system.pollIntervalMilliseconds,this.performanceClient,this.logger,r,c),d=np(im,tv.DeserializeResponse,this.logger,this.performanceClient,this.correlationId)(l,c,this.logger);if(d.accountId){if(this.logger.verbose("Account id found in hash, calling WAM for token"),!this.nativeMessageHandler)throw r_(ry);let e=new iu(this.config,this.browserStorage,this.browserCrypto,this.logger,this.eventHandler,this.navigationClient,this.apiId,this.performanceClient,this.nativeMessageHandler,d.accountId,this.browserStorage,r),{userRequestState:n}=nb.parseRequestState(this.browserCrypto,t.state);return nm(e.acquireToken.bind(e),tv.NativeInteractionClientAcquireToken,this.logger,this.performanceClient,r)({...t,state:n,prompt:t.prompt||a.NJ.NONE})}return nm(o.handleCodeResponse.bind(o),tv.HandleCodeResponse,this.logger,this.performanceClient,r)(d,t)}}class iR extends ir{async acquireToken(e){this.performanceClient.addQueueMeasurement(tv.SilentRefreshClientAcquireToken,e.correlationId);let t=await nm(ie,tv.InitializeBaseRequest,this.logger,this.performanceClient,e.correlationId)(e,this.config,this.performanceClient,this.logger),r={...e,...t};e.redirectUri&&(r.redirectUri=this.getRedirectUri(e.redirectUri));let n=this.initializeServerTelemetryManager(tM.acquireTokenSilent_silentFlow),i=await this.createRefreshTokenClient({serverTelemetryManager:n,authorityUrl:r.authority,azureCloudOptions:r.azureCloudOptions,account:r.account});return nm(i.acquireTokenByRefreshToken.bind(i),tv.RefreshTokenClientAcquireTokenByRefreshToken,this.logger,this.performanceClient,e.correlationId)(r).catch(e=>{throw e.setCorrelationId(this.correlationId),n.cacheFailedRequest(e),e})}logout(){return Promise.reject(r_(t4))}async createRefreshTokenClient(e){return new il(await nm(this.getClientConfiguration.bind(this),tv.StandardInteractionClientGetClientConfiguration,this.logger,this.performanceClient,this.correlationId)({serverTelemetryManager:e.serverTelemetryManager,requestAuthority:e.authorityUrl,requestAzureCloudOptions:e.azureCloudOptions,requestExtraQueryParameters:e.extraQueryParameters,account:e.account}),this.performanceClient)}}class i_{constructor(e,t,r,n){this.isBrowserEnvironment="undefined"!=typeof window,this.config=e,this.storage=t,this.logger=r,this.cryptoObj=n}loadExternalTokens(e,t,r){if(!this.isBrowserEnvironment)throw r_(rn);let n=t.id_token?ed(t.id_token,nc):void 0,i={protocolMode:this.config.auth.protocolMode,knownAuthorities:this.config.auth.knownAuthorities,cloudDiscoveryMetadata:this.config.auth.cloudDiscoveryMetadata,authorityMetadata:this.config.auth.authorityMetadata,skipAuthorityMetadataCache:this.config.auth.skipAuthorityMetadataCache},o=e.authority?new nC(nC.generateAuthority(e.authority,e.azureCloudOptions),this.config.system.networkClient,this.storage,i,this.logger,e.correlationId||rz()):void 0,a=this.loadAccount(e,r.clientInfo||t.client_info||"",n,o),s=this.loadIdToken(t,a.homeAccountId,a.environment,a.realm),c=this.loadAccessToken(e,t,a.homeAccountId,a.environment,a.realm,r),l=this.loadRefreshToken(t,a.homeAccountId,a.environment);return this.generateAuthenticationResult(e,{account:a,idToken:s,accessToken:c,refreshToken:l},n,o)}loadAccount(e,t,r,n){if(this.logger.verbose("TokenCache - loading account"),e.account){let t=e5.createFromAccountInfo(e.account);return this.storage.setAccount(t),t}if(!n||!t&&!r)throw this.logger.error("TokenCache - if an account is not provided on the request, authority and either clientInfo or idToken must be provided instead."),r_(rl);let i=e5.generateHomeAccountId(t,n.authorityType,this.logger,this.cryptoObj,r),o=r?.tid,a=n3(this.storage,n,i,nc,r,t,n.hostnameAndPort,o,void 0,void 0,this.logger);return this.storage.setAccount(a),a}loadIdToken(e,t,r,n){if(!e.id_token)return this.logger.verbose("TokenCache - no id token found in response"),null;this.logger.verbose("TokenCache - loading id token");let i=em(t,r,e.id_token,this.config.auth.clientId,n);return this.storage.setIdTokenCredential(i),i}loadAccessToken(e,t,r,n,i,o){if(!t.access_token)return this.logger.verbose("TokenCache - no access token found in response"),null;if(!t.expires_in)return this.logger.error("TokenCache - no expiration set on the access token. Cannot add it to the cache."),null;if(!t.scope&&(!e.scopes||!e.scopes.length))return this.logger.error("TokenCache - scopes not specified in the request or response. Cannot add token to the cache."),null;this.logger.verbose("TokenCache - loading access token");let a=t.scope?eX.fromString(t.scope):new eX(e.scopes),s=o.expiresOn||t.expires_in+new Date().getTime()/1e3,c=o.extendedExpiresOn||(t.ext_expires_in||t.expires_in)+new Date().getTime()/1e3,l=ef(r,n,t.access_token,this.config.auth.clientId,i,a.printScopes(),s,c,nc);return this.storage.setAccessTokenCredential(l),l}loadRefreshToken(e,t,r){if(!e.refresh_token)return this.logger.verbose("TokenCache - no refresh token found in response"),null;this.logger.verbose("TokenCache - loading refresh token");let n=eC(t,r,e.refresh_token,this.config.auth.clientId,e.foci,void 0,e.refresh_token_expires_in);return this.storage.setRefreshTokenCredential(n),n}generateAuthenticationResult(e,t,r,n){let i,o="",a=[],s=null;t?.accessToken&&(o=t.accessToken.secret,a=eX.fromString(t.accessToken.target).asArray(),s=new Date(1e3*Number(t.accessToken.expiresOn)),i=new Date(1e3*Number(t.accessToken.extendedExpiresOn)));let c=t.account;return{authority:n?n.canonicalAuthority:"",uniqueId:t.account.localAccountId,tenantId:t.account.realm,scopes:a,account:c.getAccountInfo(),idToken:t.idToken?.secret||"",idTokenClaims:r||{},accessToken:o,fromCache:!0,expiresOn:s,correlationId:e.correlationId||"",requestId:"",extExpiresOn:i,familyId:t.refreshToken?.familyId||"",tokenType:t?.accessToken?.tokenType||"",state:e.state||"",cloudGraphHostName:c.cloudGraphHostName||"",msGraphHost:c.msGraphHost||"",fromNativeBroker:!1}}}class ib extends n6{constructor(e){super(e),this.includeRedirectUri=!1}}class iO extends ir{constructor(e,t,r,n,i,o,a,s,c,l){super(e,t,r,n,i,o,s,c,l),this.apiId=a}async acquireToken(e){if(!e.code)throw r_(rh);let t=await nm(this.initializeAuthorizationRequest.bind(this),tv.StandardInteractionClientInitializeAuthorizationRequest,this.logger,this.performanceClient,e.correlationId)(e,i.Silent),r=this.initializeServerTelemetryManager(this.apiId);try{let n={...t,code:e.code},i=await nm(this.getClientConfiguration.bind(this),tv.StandardInteractionClientGetClientConfiguration,this.logger,this.performanceClient,e.correlationId)({serverTelemetryManager:r,requestAuthority:t.authority,requestAzureCloudOptions:t.azureCloudOptions,requestExtraQueryParameters:t.extraQueryParameters,account:t.account}),o=new ib(i);this.logger.verbose("Auth code client created");let a=new ip(o,this.browserStorage,n,this.logger,this.performanceClient);return await nm(a.handleCodeResponseFromServer.bind(a),tv.HandleCodeResponseFromServer,this.logger,this.performanceClient,e.correlationId)({code:e.code,msgraph_host:e.msGraphHost,cloud_graph_host_name:e.cloudGraphHostName,cloud_instance_host_name:e.cloudInstanceHostName},t,!1)}catch(e){throw e instanceof u&&(e.setCorrelationId(this.correlationId),r.cacheFailedRequest(e)),e}}logout(){return Promise.reject(r_(t4))}}function iN(e){let t=e?.idTokenClaims;return t?.tfp||t?.acr?"B2C":t?.tid?t?.tid==="9188040d-6c67-4c5b-b112-36a304b66dad"?"MSA":"AAD":void 0}function iP(e,t){try{r5(e)}catch(e){throw t.end({success:!1},e),e}}class iM{constructor(e){this.operatingContext=e,this.isBrowserEnvironment=this.operatingContext.isBrowserEnvironment(),this.config=e.getConfig(),this.initialized=!1,this.logger=this.operatingContext.getLogger(),this.networkClient=this.config.system.networkClient,this.navigationClient=this.config.system.navigationClient,this.redirectResponse=new Map,this.hybridAuthCodeResponses=new Map,this.performanceClient=this.config.telemetry.client,this.browserCrypto=this.isBrowserEnvironment?new nu(this.logger,this.performanceClient):es,this.eventHandler=new nD(this.logger),this.browserStorage=this.isBrowserEnvironment?new nq(this.config.auth.clientId,this.config.cache,this.browserCrypto,this.logger,function(e){let t;let r=e.cloudDiscoveryMetadata;if(r)try{t=JSON.parse(r)}catch(e){throw eY(eD)}return{canonicalAuthority:e.authority?ny(e.authority):void 0,knownAuthorities:e.knownAuthorities,cloudDiscoveryMetadata:t}}(this.config.auth),this.performanceClient):nH(this.config.auth.clientId,this.logger);let t={cacheLocation:tR.MemoryStorage,temporaryCacheLocation:tR.MemoryStorage,storeAuthStateInCookie:!1,secureCookies:!1,cacheMigrationEnabled:!1,claimsBasedCachingEnabled:!1};this.nativeInternalStorage=new nq(this.config.auth.clientId,t,this.browserCrypto,this.logger,void 0,this.performanceClient),this.tokenCache=new i_(this.config,this.browserStorage,this.logger,this.browserCrypto),this.activeSilentTokenRequests=new Map,this.trackPageVisibility=this.trackPageVisibility.bind(this),this.trackPageVisibilityWithMeasurement=this.trackPageVisibilityWithMeasurement.bind(this),this.listeningToStorageEvents=!1,this.handleAccountCacheChange=this.handleAccountCacheChange.bind(this)}static async createController(e,t){let r=new iM(e);return await r.initialize(t),r}trackPageVisibility(e){e&&(this.logger.info("Perf: Visibility change detected"),this.performanceClient.incrementFields({visibilityChangeCount:1},e))}async initialize(e){if(this.logger.trace("initialize called"),this.initialized){this.logger.info("initialize has already been called, exiting early.");return}if(!this.isBrowserEnvironment){this.logger.info("in non-browser environment, exiting early."),this.initialized=!0,this.eventHandler.emitEvent(nL.INITIALIZE_END);return}let t=e?.correlationId||this.getRequestCorrelationId(),r=this.config.system.allowNativeBroker,n=this.performanceClient.startMeasurement(tv.InitializeClientApplication,t);if(this.eventHandler.emitEvent(nL.INITIALIZE_START),r)try{this.nativeExtensionProvider=await ig.createProvider(this.logger,this.config.system.nativeBrokerHandshakeTimeout,this.performanceClient)}catch(e){this.logger.verbose(e)}this.config.cache.claimsBasedCachingEnabled||(this.logger.verbose("Claims-based caching is disabled. Clearing the previous cache with claims"),await nm(this.browserStorage.clearTokensAndKeysWithClaims.bind(this.browserStorage),tv.ClearTokensAndKeysWithClaims,this.logger,this.performanceClient,t)(this.performanceClient,t)),this.initialized=!0,this.eventHandler.emitEvent(nL.INITIALIZE_END),n.end({allowNativeBroker:r,success:!0})}async handleRedirectPromise(e){if(this.logger.verbose("handleRedirectPromise called"),r4(this.initialized),this.isBrowserEnvironment){let t=e||"",r=this.redirectResponse.get(t);return void 0===r?(r=this.handleRedirectPromiseInternal(e),this.redirectResponse.set(t,r),this.logger.verbose("handleRedirectPromise has been called for the first time, storing the promise")):this.logger.verbose("handleRedirectPromise has been called previously, returning the result from the first call"),r}return this.logger.verbose("handleRedirectPromise returns null, not browser environment"),null}async handleRedirectPromiseInternal(e){let t;let r=this.getAllAccounts(),n=this.browserStorage.getCachedNativeRequest(),o=n&&ig.isNativeAvailable(this.config,this.logger,this.nativeExtensionProvider)&&this.nativeExtensionProvider&&!e,a=o?n?.correlationId:this.browserStorage.getTemporaryCache(tb.CORRELATION_ID,!0)||"",s=this.performanceClient.startMeasurement(tv.AcquireTokenRedirect,a);if(this.eventHandler.emitEvent(nL.HANDLE_REDIRECT_START,i.Redirect),o&&this.nativeExtensionProvider){this.logger.trace("handleRedirectPromise - acquiring token from native platform");let e=new iu(this.config,this.browserStorage,this.browserCrypto,this.logger,this.eventHandler,this.navigationClient,tM.handleRedirectPromise,this.performanceClient,this.nativeExtensionProvider,n.accountId,this.nativeInternalStorage,n.correlationId);t=nm(e.handleRedirectPromise.bind(e),tv.HandleNativeRedirectPromiseMeasurement,this.logger,this.performanceClient,s.event.correlationId)(this.performanceClient,s.event.correlationId)}else{this.logger.trace("handleRedirectPromise - acquiring token from web flow");let r=this.createRedirectClient(a);t=nm(r.handleRedirectPromise.bind(r),tv.HandleRedirectPromiseMeasurement,this.logger,this.performanceClient,s.event.correlationId)(e,s)}return t.then(e=>(e?(r.length<this.getAllAccounts().length?(this.eventHandler.emitEvent(nL.LOGIN_SUCCESS,i.Redirect,e),this.logger.verbose("handleRedirectResponse returned result, login success")):(this.eventHandler.emitEvent(nL.ACQUIRE_TOKEN_SUCCESS,i.Redirect,e),this.logger.verbose("handleRedirectResponse returned result, acquire token success")),s.end({success:!0,accountType:iN(e.account)})):s.event.errorCode?s.end({success:!1}):s.discard(),this.eventHandler.emitEvent(nL.HANDLE_REDIRECT_END,i.Redirect),e)).catch(e=>{throw r.length>0?this.eventHandler.emitEvent(nL.ACQUIRE_TOKEN_FAILURE,i.Redirect,null,e):this.eventHandler.emitEvent(nL.LOGIN_FAILURE,i.Redirect,null,e),this.eventHandler.emitEvent(nL.HANDLE_REDIRECT_END,i.Redirect),s.end({success:!1},e),e})}async acquireTokenRedirect(e){let t=this.getRequestCorrelationId(e);this.logger.verbose("acquireTokenRedirect called",t);let r=this.performanceClient.startMeasurement(tv.AcquireTokenPreRedirect,t);r.add({accountType:iN(e.account),scenarioId:e.scenarioId});let n=e.onRedirectNavigate;if(n)e.onRedirectNavigate=e=>{let t="function"==typeof n?n(e):void 0;return!1!==t?r.end({success:!0}):r.discard(),t};else{let e=this.config.auth.onRedirectNavigate;this.config.auth.onRedirectNavigate=t=>{let n="function"==typeof e?e(t):void 0;return!1!==n?r.end({success:!0}):r.discard(),n}}let o=this.getAllAccounts().length>0;try{let n;return r9(this.initialized,this.config),this.browserStorage.setInteractionInProgress(!0),o?this.eventHandler.emitEvent(nL.ACQUIRE_TOKEN_START,i.Redirect,e):this.eventHandler.emitEvent(nL.LOGIN_START,i.Redirect,e),n=this.nativeExtensionProvider&&this.canUseNative(e)?new iu(this.config,this.browserStorage,this.browserCrypto,this.logger,this.eventHandler,this.navigationClient,tM.acquireTokenRedirect,this.performanceClient,this.nativeExtensionProvider,this.getNativeAccountId(e),this.nativeInternalStorage,t).acquireTokenRedirect(e,r).catch(r=>{if(r instanceof ia&&is(r))return this.nativeExtensionProvider=void 0,this.createRedirectClient(t).acquireToken(e);if(r instanceof nk)return this.logger.verbose("acquireTokenRedirect - Resolving interaction required error thrown by native broker by falling back to web flow"),this.createRedirectClient(t).acquireToken(e);throw this.browserStorage.setInteractionInProgress(!1),r}):this.createRedirectClient(t).acquireToken(e),await n}catch(e){throw r.end({success:!1},e),o?this.eventHandler.emitEvent(nL.ACQUIRE_TOKEN_FAILURE,i.Redirect,null,e):this.eventHandler.emitEvent(nL.LOGIN_FAILURE,i.Redirect,null,e),e}}acquireTokenPopup(e){let t=this.getRequestCorrelationId(e),r=this.performanceClient.startMeasurement(tv.AcquireTokenPopup,t);r.add({scenarioId:e.scenarioId,accountType:iN(e.account)});try{this.logger.verbose("acquireTokenPopup called",t),iP(this.initialized,r),this.browserStorage.setInteractionInProgress(!0)}catch(e){return Promise.reject(e)}let n=this.getAllAccounts();return n.length>0?this.eventHandler.emitEvent(nL.ACQUIRE_TOKEN_START,i.Popup,e):this.eventHandler.emitEvent(nL.LOGIN_START,i.Popup,e),(this.canUseNative(e)?this.acquireTokenNative({...e,correlationId:t},tM.acquireTokenPopup).then(e=>(this.browserStorage.setInteractionInProgress(!1),r.end({success:!0,isNativeBroker:!0,accountType:iN(e.account)}),e)).catch(r=>{if(r instanceof ia&&is(r))return this.nativeExtensionProvider=void 0,this.createPopupClient(t).acquireToken(e);if(r instanceof nk)return this.logger.verbose("acquireTokenPopup - Resolving interaction required error thrown by native broker by falling back to web flow"),this.createPopupClient(t).acquireToken(e);throw this.browserStorage.setInteractionInProgress(!1),r}):this.createPopupClient(t).acquireToken(e)).then(e=>(n.length<this.getAllAccounts().length?this.eventHandler.emitEvent(nL.LOGIN_SUCCESS,i.Popup,e):this.eventHandler.emitEvent(nL.ACQUIRE_TOKEN_SUCCESS,i.Popup,e),r.end({success:!0,accessTokenSize:e.accessToken.length,idTokenSize:e.idToken.length,accountType:iN(e.account)}),e)).catch(e=>(n.length>0?this.eventHandler.emitEvent(nL.ACQUIRE_TOKEN_FAILURE,i.Popup,null,e):this.eventHandler.emitEvent(nL.LOGIN_FAILURE,i.Popup,null,e),r.end({success:!1},e),Promise.reject(e)))}trackPageVisibilityWithMeasurement(){let e=this.ssoSilentMeasurement||this.acquireTokenByCodeAsyncMeasurement;e&&(this.logger.info("Perf: Visibility change detected in ",e.event.name),e.increment({visibilityChangeCount:1}))}async ssoSilent(e){let t=this.getRequestCorrelationId(e),r={...e,prompt:e.prompt,correlationId:t};return this.ssoSilentMeasurement=this.performanceClient.startMeasurement(tv.SsoSilent,t),this.ssoSilentMeasurement?.add({scenarioId:e.scenarioId,accountType:iN(e.account)}),iP(this.initialized,this.ssoSilentMeasurement),this.ssoSilentMeasurement?.increment({visibilityChangeCount:0}),document.addEventListener("visibilitychange",this.trackPageVisibilityWithMeasurement),this.logger.verbose("ssoSilent called",t),this.eventHandler.emitEvent(nL.SSO_SILENT_START,i.Silent,r),(this.canUseNative(r)?this.acquireTokenNative(r,tM.ssoSilent).catch(e=>{if(e instanceof ia&&is(e))return this.nativeExtensionProvider=void 0,this.createSilentIframeClient(r.correlationId).acquireToken(r);throw e}):this.createSilentIframeClient(r.correlationId).acquireToken(r)).then(e=>(this.eventHandler.emitEvent(nL.SSO_SILENT_SUCCESS,i.Silent,e),this.ssoSilentMeasurement?.end({success:!0,isNativeBroker:e.fromNativeBroker,accessTokenSize:e.accessToken.length,idTokenSize:e.idToken.length,accountType:iN(e.account)}),e)).catch(e=>{throw this.eventHandler.emitEvent(nL.SSO_SILENT_FAILURE,i.Silent,null,e),this.ssoSilentMeasurement?.end({success:!1},e),e}).finally(()=>{document.removeEventListener("visibilitychange",this.trackPageVisibilityWithMeasurement)})}async acquireTokenByCode(e){let t=this.getRequestCorrelationId(e);this.logger.trace("acquireTokenByCode called",t);let r=this.performanceClient.startMeasurement(tv.AcquireTokenByCode,t);iP(this.initialized,r),this.eventHandler.emitEvent(nL.ACQUIRE_TOKEN_BY_CODE_START,i.Silent,e),r.add({scenarioId:e.scenarioId});try{if(e.code&&e.nativeAccountId)throw r_(rg);if(e.code){let n=e.code,o=this.hybridAuthCodeResponses.get(n);return o?(this.logger.verbose("Existing acquireTokenByCode request found",t),r.discard()):(this.logger.verbose("Initiating new acquireTokenByCode request",t),o=this.acquireTokenByCodeAsync({...e,correlationId:t}).then(e=>(this.eventHandler.emitEvent(nL.ACQUIRE_TOKEN_BY_CODE_SUCCESS,i.Silent,e),this.hybridAuthCodeResponses.delete(n),r.end({success:!0,isNativeBroker:e.fromNativeBroker,accessTokenSize:e.accessToken.length,idTokenSize:e.idToken.length,accountType:iN(e.account)}),e)).catch(e=>{throw this.hybridAuthCodeResponses.delete(n),this.eventHandler.emitEvent(nL.ACQUIRE_TOKEN_BY_CODE_FAILURE,i.Silent,null,e),r.end({success:!1},e),e}),this.hybridAuthCodeResponses.set(n,o)),await o}if(e.nativeAccountId){if(this.canUseNative(e,e.nativeAccountId)){let n=await this.acquireTokenNative({...e,correlationId:t},tM.acquireTokenByCode,e.nativeAccountId).catch(e=>{throw e instanceof ia&&is(e)&&(this.nativeExtensionProvider=void 0),e});return r.end({accountType:iN(n.account),success:!0}),n}throw r_(rm)}throw r_(ru)}catch(e){throw this.eventHandler.emitEvent(nL.ACQUIRE_TOKEN_BY_CODE_FAILURE,i.Silent,null,e),r.end({success:!1},e),e}}async acquireTokenByCodeAsync(e){this.logger.trace("acquireTokenByCodeAsync called",e.correlationId),this.acquireTokenByCodeAsyncMeasurement=this.performanceClient.startMeasurement(tv.AcquireTokenByCodeAsync,e.correlationId),this.acquireTokenByCodeAsyncMeasurement?.increment({visibilityChangeCount:0}),document.addEventListener("visibilitychange",this.trackPageVisibilityWithMeasurement);let t=this.createSilentAuthCodeClient(e.correlationId);return await t.acquireToken(e).then(e=>(this.acquireTokenByCodeAsyncMeasurement?.end({success:!0,fromCache:e.fromCache,isNativeBroker:e.fromNativeBroker}),e)).catch(e=>{throw this.acquireTokenByCodeAsyncMeasurement?.end({success:!1},e),e}).finally(()=>{document.removeEventListener("visibilitychange",this.trackPageVisibilityWithMeasurement)})}async acquireTokenFromCache(e,t){switch(this.performanceClient.addQueueMeasurement(tv.AcquireTokenFromCache,e.correlationId),t){case tL.Default:case tL.AccessToken:case tL.AccessTokenAndRefreshToken:let r=this.createSilentCacheClient(e.correlationId);return nm(r.acquireToken.bind(r),tv.SilentCacheClientAcquireToken,this.logger,this.performanceClient,e.correlationId)(e);default:throw ea(W)}}async acquireTokenByRefreshToken(e,t){switch(this.performanceClient.addQueueMeasurement(tv.AcquireTokenByRefreshToken,e.correlationId),t){case tL.Default:case tL.AccessTokenAndRefreshToken:case tL.RefreshToken:case tL.RefreshTokenAndNetwork:let r=this.createSilentRefreshClient(e.correlationId);return nm(r.acquireToken.bind(r),tv.SilentRefreshClientAcquireToken,this.logger,this.performanceClient,e.correlationId)(e);default:throw ea(W)}}async acquireTokenBySilentIframe(e){this.performanceClient.addQueueMeasurement(tv.AcquireTokenBySilentIframe,e.correlationId);let t=this.createSilentIframeClient(e.correlationId);return nm(t.acquireToken.bind(t),tv.SilentIframeClientAcquireToken,this.logger,this.performanceClient,e.correlationId)(e)}async logout(e){let t=this.getRequestCorrelationId(e);return this.logger.warning("logout API is deprecated and will be removed in msal-browser v3.0.0. Use logoutRedirect instead.",t),this.logoutRedirect({correlationId:t,...e})}async logoutRedirect(e){let t=this.getRequestCorrelationId(e);return r9(this.initialized,this.config),this.browserStorage.setInteractionInProgress(!0),this.createRedirectClient(t).logout(e)}logoutPopup(e){try{let t=this.getRequestCorrelationId(e);return r5(this.initialized),this.browserStorage.setInteractionInProgress(!0),this.createPopupClient(t).logout(e)}catch(e){return Promise.reject(e)}}async clearCache(e){if(!this.isBrowserEnvironment){this.logger.info("in non-browser environment, returning early.");return}let t=this.getRequestCorrelationId(e);return this.createSilentCacheClient(t).logout(e)}getAllAccounts(e){var t,r,n;return t=this.logger,r=this.browserStorage,n=this.isBrowserEnvironment,t.verbose("getAllAccounts called"),n?r.getAllAccounts(e):[]}getAccount(e){return function(e,t,r){if(t.trace("getAccount called"),0===Object.keys(e).length)return t.warning("getAccount: No accountFilter provided"),null;let n=r.getAccountInfoFilteredBy(e);return n?(t.verbose("getAccount: Account matching provided filter found, returning"),n):(t.verbose("getAccount: No matching account found, returning null"),null)}(e,this.logger,this.browserStorage)}getAccountByUsername(e){return function(e,t,r){if(t.trace("getAccountByUsername called"),!e)return t.warning("getAccountByUsername: No username provided"),null;let n=r.getAccountInfoFilteredBy({username:e});return n?(t.verbose("getAccountByUsername: Account matching username found, returning"),t.verbosePii(`getAccountByUsername: Returning signed-in accounts matching username: ${e}`),n):(t.verbose("getAccountByUsername: No matching account found, returning null"),null)}(e,this.logger,this.browserStorage)}getAccountByHomeId(e){return function(e,t,r){if(t.trace("getAccountByHomeId called"),!e)return t.warning("getAccountByHomeId: No homeAccountId provided"),null;let n=r.getAccountInfoFilteredBy({homeAccountId:e});return n?(t.verbose("getAccountByHomeId: Account matching homeAccountId found, returning"),t.verbosePii(`getAccountByHomeId: Returning signed-in accounts matching homeAccountId: ${e}`),n):(t.verbose("getAccountByHomeId: No matching account found, returning null"),null)}(e,this.logger,this.browserStorage)}getAccountByLocalId(e){return function(e,t,r){if(t.trace("getAccountByLocalId called"),!e)return t.warning("getAccountByLocalId: No localAccountId provided"),null;let n=r.getAccountInfoFilteredBy({localAccountId:e});return n?(t.verbose("getAccountByLocalId: Account matching localAccountId found, returning"),t.verbosePii(`getAccountByLocalId: Returning signed-in accounts matching localAccountId: ${e}`),n):(t.verbose("getAccountByLocalId: No matching account found, returning null"),null)}(e,this.logger,this.browserStorage)}setActiveAccount(e){!function(e,t){t.setActiveAccount(e)}(e,this.browserStorage)}getActiveAccount(){return this.browserStorage.getActiveAccount()}async hydrateCache(e,t){this.logger.verbose("hydrateCache called");let r=e5.createFromAccountInfo(e.account,e.cloudGraphHostName,e.msGraphHost);return(this.browserStorage.setAccount(r),e.fromNativeBroker)?(this.logger.verbose("Response was from native broker, storing in-memory"),this.nativeInternalStorage.hydrateCache(e,t)):this.browserStorage.hydrateCache(e,t)}async acquireTokenNative(e,t,r){if(this.logger.trace("acquireTokenNative called"),!this.nativeExtensionProvider)throw r_(ry);return new iu(this.config,this.browserStorage,this.browserCrypto,this.logger,this.eventHandler,this.navigationClient,t,this.performanceClient,this.nativeExtensionProvider,r||this.getNativeAccountId(e),this.nativeInternalStorage,e.correlationId).acquireToken(e)}canUseNative(e,t){if(this.logger.trace("canUseNative called"),!ig.isNativeAvailable(this.config,this.logger,this.nativeExtensionProvider,e.authenticationScheme))return this.logger.trace("canUseNative: isNativeAvailable returned false, returning false"),!1;if(e.prompt)switch(e.prompt){case a.NJ.NONE:case a.NJ.CONSENT:case a.NJ.LOGIN:this.logger.trace("canUseNative: prompt is compatible with native flow");break;default:return this.logger.trace(`canUseNative: prompt = ${e.prompt} is not compatible with native flow, returning false`),!1}return!!(t||this.getNativeAccountId(e))||(this.logger.trace("canUseNative: nativeAccountId is not available, returning false"),!1)}getNativeAccountId(e){let t=e.account||this.getAccount({loginHint:e.loginHint,sid:e.sid})||this.getActiveAccount();return t&&t.nativeAccountId||""}createPopupClient(e){return new iC(this.config,this.browserStorage,this.browserCrypto,this.logger,this.eventHandler,this.navigationClient,this.performanceClient,this.nativeInternalStorage,this.nativeExtensionProvider,e)}createRedirectClient(e){return new iT(this.config,this.browserStorage,this.browserCrypto,this.logger,this.eventHandler,this.navigationClient,this.performanceClient,this.nativeInternalStorage,this.nativeExtensionProvider,e)}createSilentIframeClient(e){return new ik(this.config,this.browserStorage,this.browserCrypto,this.logger,this.eventHandler,this.navigationClient,tM.ssoSilent,this.performanceClient,this.nativeInternalStorage,this.nativeExtensionProvider,e)}createSilentCacheClient(e){return new ih(this.config,this.browserStorage,this.browserCrypto,this.logger,this.eventHandler,this.navigationClient,this.performanceClient,this.nativeExtensionProvider,e)}createSilentRefreshClient(e){return new iR(this.config,this.browserStorage,this.browserCrypto,this.logger,this.eventHandler,this.navigationClient,this.performanceClient,this.nativeExtensionProvider,e)}createSilentAuthCodeClient(e){return new iO(this.config,this.browserStorage,this.browserCrypto,this.logger,this.eventHandler,this.navigationClient,tM.acquireTokenByCode,this.performanceClient,this.nativeExtensionProvider,e)}addEventCallback(e,t){return this.eventHandler.addEventCallback(e,t)}removeEventCallback(e){this.eventHandler.removeEventCallback(e)}addPerformanceCallback(e){return r6(),this.performanceClient.addPerformanceCallback(e)}removePerformanceCallback(e){return this.performanceClient.removePerformanceCallback(e)}enableAccountStorageEvents(){"undefined"!=typeof window&&(this.listeningToStorageEvents?this.logger.verbose("Account storage listener already registered."):(this.logger.verbose("Adding account storage listener."),this.listeningToStorageEvents=!0,window.addEventListener("storage",this.handleAccountCacheChange)))}disableAccountStorageEvents(){"undefined"!=typeof window&&(this.listeningToStorageEvents?(this.logger.verbose("Removing account storage listener."),window.removeEventListener("storage",this.handleAccountCacheChange),this.listeningToStorageEvents=!1):this.logger.verbose("No account storage listener registered."))}handleAccountCacheChange(e){try{e.key?.includes(a.Vi.ACTIVE_ACCOUNT_FILTERS)&&this.eventHandler.emitEvent(nL.ACTIVE_ACCOUNT_CHANGED);let t=e.newValue||e.oldValue;if(!t)return;let r=JSON.parse(t);if("object"!=typeof r||!e5.isAccountEntity(r))return;let n=tl.toObject(new e5,r).getAccountInfo();!e.oldValue&&e.newValue?(this.logger.info("Account was added to cache in a different window"),this.eventHandler.emitEvent(nL.ACCOUNT_ADDED,void 0,n)):!e.newValue&&e.oldValue&&(this.logger.info("Account was removed from cache in a different window"),this.eventHandler.emitEvent(nL.ACCOUNT_REMOVED,void 0,n))}catch(e){return}}getTokenCache(){return this.tokenCache}getLogger(){return this.logger}setLogger(e){this.logger=e}initializeWrapperLibrary(e,t){this.browserStorage.setWrapperMetadata(e,t)}setNavigationClient(e){this.navigationClient=e}getConfiguration(){return this.config}getPerformanceClient(){return this.performanceClient}isBrowserEnv(){return this.isBrowserEnvironment}getRequestCorrelationId(e){return e?.correlationId?e.correlationId:this.isBrowserEnvironment?rz():a.gT.EMPTY_STRING}async loginRedirect(e){let t=this.getRequestCorrelationId(e);return this.logger.verbose("loginRedirect called",t),this.acquireTokenRedirect({correlationId:t,...e||tU})}loginPopup(e){let t=this.getRequestCorrelationId(e);return this.logger.verbose("loginPopup called",t),this.acquireTokenPopup({correlationId:t,...e||tU})}async acquireTokenSilent(e){let t=this.getRequestCorrelationId(e),r=this.performanceClient.startMeasurement(tv.AcquireTokenSilent,t);r.add({cacheLookupPolicy:e.cacheLookupPolicy,scenarioId:e.scenarioId}),iP(this.initialized,r),this.logger.verbose("acquireTokenSilent called",t);let n=e.account||this.getActiveAccount();if(!n)throw r_(t5);r.add({accountType:iN(n)});let i=JSON.stringify({clientId:this.config.auth.clientId,authority:e.authority||a.gT.EMPTY_STRING,scopes:e.scopes,homeAccountIdentifier:n.homeAccountId,claims:e.claims,authenticationScheme:e.authenticationScheme,resourceRequestMethod:e.resourceRequestMethod,resourceRequestUri:e.resourceRequestUri,shrClaims:e.shrClaims,sshKid:e.sshKid,shrOptions:e.shrOptions}),o=this.activeSilentTokenRequests.get(i);if(void 0!==o)return this.logger.verbose("acquireTokenSilent has been called previously, returning the result from the first call",t),r.discard(),{...await o,state:e.state};{this.logger.verbose("acquireTokenSilent called for the first time, storing active request",t);let o=nm(this.acquireTokenSilentAsync.bind(this),tv.AcquireTokenSilentAsync,this.logger,this.performanceClient,t)({...e,correlationId:t},n).then(t=>(this.activeSilentTokenRequests.delete(i),r.end({success:!0,fromCache:t.fromCache,isNativeBroker:t.fromNativeBroker,cacheLookupPolicy:e.cacheLookupPolicy,accessTokenSize:t.accessToken.length,idTokenSize:t.idToken.length}),t)).catch(e=>{throw this.activeSilentTokenRequests.delete(i),r.end({success:!1},e),e});return this.activeSilentTokenRequests.set(i,o),{...await o,state:e.state}}}async acquireTokenSilentAsync(e,t){let r=()=>this.trackPageVisibility(e.correlationId);this.performanceClient.addQueueMeasurement(tv.AcquireTokenSilentAsync,e.correlationId),this.eventHandler.emitEvent(nL.ACQUIRE_TOKEN_START,i.Silent,e),e.correlationId&&this.performanceClient.incrementFields({visibilityChangeCount:0},e.correlationId),document.addEventListener("visibilitychange",r);let n=await nm(it,tv.InitializeSilentRequest,this.logger,this.performanceClient,e.correlationId)(e,t,this.config,this.performanceClient,this.logger),o=e.cacheLookupPolicy||tL.Default;return this.acquireTokenSilentNoIframe(n,o).catch(async e=>{if(function(e,t){let r=!(e instanceof nk&&e.subError!==nA),n=e.errorCode===tS.INVALID_GRANT_ERROR||e.errorCode===W,i=r&&n||e.errorCode===nT||e.errorCode===nv,o=tD.includes(t);return i&&o}(e,o)){if(this.activeIframeRequest){if(o===tL.Skip)return this.logger.warning("Another iframe request is currently in progress and CacheLookupPolicy is set to Skip. This may result in degraded performance and/or reliability for both calls. Please consider changing the CacheLookupPolicy to take advantage of request queuing and token cache.",n.correlationId),nm(this.acquireTokenBySilentIframe.bind(this),tv.AcquireTokenBySilentIframe,this.logger,this.performanceClient,n.correlationId)(n);{let[t,r]=this.activeIframeRequest;this.logger.verbose(`Iframe request is already in progress, awaiting resolution for request with correlationId: ${r}`,n.correlationId);let i=this.performanceClient.startMeasurement(tv.AwaitConcurrentIframe,n.correlationId);i.add({awaitIframeCorrelationId:r});let a=await t;if(i.end({success:a}),a)return this.logger.verbose(`Parallel iframe request with correlationId: ${r} succeeded. Retrying cache and/or RT redemption`,n.correlationId),this.acquireTokenSilentNoIframe(n,o);throw this.logger.info(`Iframe request with correlationId: ${r} failed. Interaction is required.`),e}}{let e;return this.activeIframeRequest=[new Promise(t=>{e=t}),n.correlationId],this.logger.verbose("Refresh token expired/invalid or CacheLookupPolicy is set to Skip, attempting acquire token by iframe.",n.correlationId),nm(this.acquireTokenBySilentIframe.bind(this),tv.AcquireTokenBySilentIframe,this.logger,this.performanceClient,n.correlationId)(n).then(t=>(e(!0),t)).catch(t=>{throw e(!1),t}).finally(()=>{this.activeIframeRequest=void 0})}}throw e}).then(t=>(this.eventHandler.emitEvent(nL.ACQUIRE_TOKEN_SUCCESS,i.Silent,t),e.correlationId&&this.performanceClient.addFields({fromCache:t.fromCache,isNativeBroker:t.fromNativeBroker},e.correlationId),t)).catch(e=>{throw this.eventHandler.emitEvent(nL.ACQUIRE_TOKEN_FAILURE,i.Silent,null,e),e}).finally(()=>{document.removeEventListener("visibilitychange",r)})}async acquireTokenSilentNoIframe(e,t){return ig.isNativeAvailable(this.config,this.logger,this.nativeExtensionProvider,e.authenticationScheme)&&e.account.nativeAccountId?(this.logger.verbose("acquireTokenSilent - attempting to acquire token from native platform"),this.acquireTokenNative(e,tM.acquireTokenSilent_silentFlow).catch(async e=>{if(e instanceof ia&&is(e))throw this.logger.verbose("acquireTokenSilent - native platform unavailable, falling back to web flow"),this.nativeExtensionProvider=void 0,ea(W);throw e})):(this.logger.verbose("acquireTokenSilent - attempting to acquire token from web flow"),nm(this.acquireTokenFromCache.bind(this),tv.AcquireTokenFromCache,this.logger,this.performanceClient,e.correlationId)(e,t).catch(r=>{if(t===tL.AccessToken)throw r;return this.eventHandler.emitEvent(nL.ACQUIRE_TOKEN_NETWORK_START,i.Silent,e),nm(this.acquireTokenByRefreshToken.bind(this),tv.AcquireTokenByRefreshToken,this.logger,this.performanceClient,e.correlationId)(e,t)}))}}async function iU(e,t){let r=new nt(e);return await r.initialize(),iM.createController(r,t)}class iq{static async createPublicClientApplication(e){let t=await iU(e);return new iq(e,t)}constructor(e,t){this.controller=t||new iM(new nt(e))}async initialize(e){return this.controller.initialize(e)}async acquireTokenPopup(e){return this.controller.acquireTokenPopup(e)}acquireTokenRedirect(e){return this.controller.acquireTokenRedirect(e)}acquireTokenSilent(e){return this.controller.acquireTokenSilent(e)}acquireTokenByCode(e){return this.controller.acquireTokenByCode(e)}addEventCallback(e,t){return this.controller.addEventCallback(e,t)}removeEventCallback(e){return this.controller.removeEventCallback(e)}addPerformanceCallback(e){return this.controller.addPerformanceCallback(e)}removePerformanceCallback(e){return this.controller.removePerformanceCallback(e)}enableAccountStorageEvents(){this.controller.enableAccountStorageEvents()}disableAccountStorageEvents(){this.controller.disableAccountStorageEvents()}getAccount(e){return this.controller.getAccount(e)}getAccountByHomeId(e){return this.controller.getAccountByHomeId(e)}getAccountByLocalId(e){return this.controller.getAccountByLocalId(e)}getAccountByUsername(e){return this.controller.getAccountByUsername(e)}getAllAccounts(e){return this.controller.getAllAccounts(e)}handleRedirectPromise(e){return this.controller.handleRedirectPromise(e)}loginPopup(e){return this.controller.loginPopup(e)}loginRedirect(e){return this.controller.loginRedirect(e)}logout(e){return this.controller.logout(e)}logoutRedirect(e){return this.controller.logoutRedirect(e)}logoutPopup(e){return this.controller.logoutPopup(e)}ssoSilent(e){return this.controller.ssoSilent(e)}getTokenCache(){return this.controller.getTokenCache()}getLogger(){return this.controller.getLogger()}setLogger(e){this.controller.setLogger(e)}setActiveAccount(e){this.controller.setActiveAccount(e)}getActiveAccount(){return this.controller.getActiveAccount()}initializeWrapperLibrary(e,t){return this.controller.initializeWrapperLibrary(e,t)}setNavigationClient(e){this.controller.setNavigationClient(e)}getConfiguration(){return this.controller.getConfiguration()}async hydrateCache(e,t){return this.controller.hydrateCache(e,t)}clearCache(e){return this.controller.clearCache(e)}}},71986:function(e,t,r){r.d(t,{Y:function(){return a},i:function(){return i}});var n,i,o=r(7289);(n=i||(i={}))[n.Error=0]="Error",n[n.Warning=1]="Warning",n[n.Info=2]="Info",n[n.Verbose=3]="Verbose",n[n.Trace=4]="Trace";class a{constructor(e,t,r){this.level=i.Info;let n=e||a.createDefaultLoggerOptions();this.localCallback=n.loggerCallback||(()=>{}),this.piiLoggingEnabled=n.piiLoggingEnabled||!1,this.level="number"==typeof n.logLevel?n.logLevel:i.Info,this.correlationId=n.correlationId||o.gT.EMPTY_STRING,this.packageName=t||o.gT.EMPTY_STRING,this.packageVersion=r||o.gT.EMPTY_STRING}static createDefaultLoggerOptions(){return{loggerCallback:()=>{},piiLoggingEnabled:!1,logLevel:i.Info}}clone(e,t,r){return new a({loggerCallback:this.localCallback,piiLoggingEnabled:this.piiLoggingEnabled,logLevel:this.level,correlationId:r||this.correlationId},e,t)}logMessage(e,t){if(t.logLevel>this.level||!this.piiLoggingEnabled&&t.containsPii)return;let r=new Date().toUTCString(),n=`[${r}] : [${t.correlationId||this.correlationId||""}]`,o=`${n} : ${this.packageName}@${this.packageVersion} : ${i[t.logLevel]} - ${e}`;this.executeCallback(t.logLevel,o,t.containsPii||!1)}executeCallback(e,t,r){this.localCallback&&this.localCallback(e,t,r)}error(e,t){this.logMessage(e,{logLevel:i.Error,containsPii:!1,correlationId:t||o.gT.EMPTY_STRING})}errorPii(e,t){this.logMessage(e,{logLevel:i.Error,containsPii:!0,correlationId:t||o.gT.EMPTY_STRING})}warning(e,t){this.logMessage(e,{logLevel:i.Warning,containsPii:!1,correlationId:t||o.gT.EMPTY_STRING})}warningPii(e,t){this.logMessage(e,{logLevel:i.Warning,containsPii:!0,correlationId:t||o.gT.EMPTY_STRING})}info(e,t){this.logMessage(e,{logLevel:i.Info,containsPii:!1,correlationId:t||o.gT.EMPTY_STRING})}infoPii(e,t){this.logMessage(e,{logLevel:i.Info,containsPii:!0,correlationId:t||o.gT.EMPTY_STRING})}verbose(e,t){this.logMessage(e,{logLevel:i.Verbose,containsPii:!1,correlationId:t||o.gT.EMPTY_STRING})}verbosePii(e,t){this.logMessage(e,{logLevel:i.Verbose,containsPii:!0,correlationId:t||o.gT.EMPTY_STRING})}trace(e,t){this.logMessage(e,{logLevel:i.Trace,containsPii:!1,correlationId:t||o.gT.EMPTY_STRING})}tracePii(e,t){this.logMessage(e,{logLevel:i.Trace,containsPii:!0,correlationId:t||o.gT.EMPTY_STRING})}isPiiLoggingEnabled(){return this.piiLoggingEnabled||!1}}},7289:function(e,t,r){r.d(t,{$A:function(){return U},$f:function(){return a},As:function(){return d},Bv:function(){return C},D1:function(){return R},Ge:function(){return _},HN:function(){return S},NJ:function(){return h},Nb:function(){return l},QU:function(){return A},SZ:function(){return s},To:function(){return P},VX:function(){return u},Vi:function(){return c},W:function(){return O},YY:function(){return b},bb:function(){return k},ch:function(){return v},d3:function(){return y},dg:function(){return T},f_:function(){return o},gT:function(){return n},hO:function(){return E},iR:function(){return f},ki:function(){return N},oj:function(){return i},pQ:function(){return w},qJ:function(){return m},rg:function(){return g},sR:function(){return I},vw:function(){return p},xd:function(){return M}});let n={LIBRARY_NAME:"MSAL.JS",SKU:"msal.js.common",CACHE_PREFIX:"msal",DEFAULT_AUTHORITY:"https://login.microsoftonline.com/common/",DEFAULT_AUTHORITY_HOST:"login.microsoftonline.com",DEFAULT_COMMON_TENANT:"common",ADFS:"adfs",DSTS:"dstsv2",AAD_INSTANCE_DISCOVERY_ENDPT:"https://login.microsoftonline.com/common/discovery/instance?api-version=1.1&authorization_endpoint=",CIAM_AUTH_URL:".ciamlogin.com",AAD_TENANT_DOMAIN_SUFFIX:".onmicrosoft.com",RESOURCE_DELIM:"|",NO_ACCOUNT:"NO_ACCOUNT",CLAIMS:"claims",CONSUMER_UTID:"9188040d-6c67-4c5b-b112-36a304b66dad",OPENID_SCOPE:"openid",PROFILE_SCOPE:"profile",OFFLINE_ACCESS_SCOPE:"offline_access",EMAIL_SCOPE:"email",CODE_RESPONSE_TYPE:"code",CODE_GRANT_TYPE:"authorization_code",RT_GRANT_TYPE:"refresh_token",FRAGMENT_RESPONSE_MODE:"fragment",S256_CODE_CHALLENGE_METHOD:"S256",URL_FORM_CONTENT_TYPE:"application/x-www-form-urlencoded;charset=utf-8",AUTHORIZATION_PENDING:"authorization_pending",NOT_DEFINED:"not_defined",EMPTY_STRING:"",NOT_APPLICABLE:"N/A",NOT_AVAILABLE:"Not Available",FORWARD_SLASH:"/",IMDS_ENDPOINT:"http://***************/metadata/instance/compute/location",IMDS_VERSION:"2020-06-01",IMDS_TIMEOUT:2e3,AZURE_REGION_AUTO_DISCOVER_FLAG:"TryAutoDetect",REGIONAL_AUTH_PUBLIC_CLOUD_SUFFIX:"login.microsoft.com",KNOWN_PUBLIC_CLOUDS:["login.microsoftonline.com","login.windows.net","login.microsoft.com","sts.windows.net"],TOKEN_RESPONSE_TYPE:"token",ID_TOKEN_RESPONSE_TYPE:"id_token",SHR_NONCE_VALIDITY:240,INVALID_INSTANCE:"invalid_instance"},i={SUCCESS:200,SUCCESS_RANGE_START:200,SUCCESS_RANGE_END:299,REDIRECT:302,CLIENT_ERROR:400,CLIENT_ERROR_RANGE_START:400,BAD_REQUEST:400,UNAUTHORIZED:401,NOT_FOUND:404,REQUEST_TIMEOUT:408,TOO_MANY_REQUESTS:429,CLIENT_ERROR_RANGE_END:499,SERVER_ERROR:500,SERVER_ERROR_RANGE_START:500,SERVICE_UNAVAILABLE:503,GATEWAY_TIMEOUT:504,SERVER_ERROR_RANGE_END:599,MULTI_SIDED_ERROR:600},o=[n.OPENID_SCOPE,n.PROFILE_SCOPE,n.OFFLINE_ACCESS_SCOPE],a=[...o,n.EMAIL_SCOPE],s={CONTENT_TYPE:"Content-Type",CONTENT_LENGTH:"Content-Length",RETRY_AFTER:"Retry-After",CCS_HEADER:"X-AnchorMailbox",WWWAuthenticate:"WWW-Authenticate",AuthenticationInfo:"Authentication-Info",X_MS_REQUEST_ID:"x-ms-request-id",X_MS_HTTP_VERSION:"x-ms-httpver"},c={ID_TOKEN:"idtoken",CLIENT_INFO:"client.info",ADAL_ID_TOKEN:"adal.idtoken",ERROR:"error",ERROR_DESC:"error.description",ACTIVE_ACCOUNT:"active-account",ACTIVE_ACCOUNT_FILTERS:"active-account-filters"},l={COMMON:"common",ORGANIZATIONS:"organizations",CONSUMERS:"consumers"},d={ACCESS_TOKEN:"access_token",XMS_CC:"xms_cc"},h={LOGIN:"login",SELECT_ACCOUNT:"select_account",CONSENT:"consent",NONE:"none",CREATE:"create",NO_SESSION:"no_session"},u={PLAIN:"plain",S256:"S256"},g={QUERY:"query",FRAGMENT:"fragment"},p={...g,FORM_POST:"form_post"},m={IMPLICIT_GRANT:"implicit",AUTHORIZATION_CODE_GRANT:"authorization_code",CLIENT_CREDENTIALS_GRANT:"client_credentials",RESOURCE_OWNER_PASSWORD_GRANT:"password",REFRESH_TOKEN_GRANT:"refresh_token",DEVICE_CODE_GRANT:"device_code",JWT_BEARER:"urn:ietf:params:oauth:grant-type:jwt-bearer"},f={MSSTS_ACCOUNT_TYPE:"MSSTS",ADFS_ACCOUNT_TYPE:"ADFS",MSAV1_ACCOUNT_TYPE:"MSA",GENERIC_ACCOUNT_TYPE:"Generic"},C={CACHE_KEY_SEPARATOR:"-",CLIENT_INFO_SEPARATOR:"."},y={ID_TOKEN:"IdToken",ACCESS_TOKEN:"AccessToken",ACCESS_TOKEN_WITH_AUTH_SCHEME:"AccessToken_With_AuthScheme",REFRESH_TOKEN:"RefreshToken"},T="appmetadata",I="client_info",v="1",A={CACHE_KEY:"authority-metadata",REFRESH_TIME_SECONDS:86400},w={CONFIG:"config",CACHE:"cache",NETWORK:"network",HARDCODED_VALUES:"hardcoded_values"},S={SCHEMA_VERSION:5,MAX_CUR_HEADER_BYTES:80,MAX_LAST_HEADER_BYTES:330,MAX_CACHED_ERRORS:50,CACHE_KEY:"server-telemetry",CATEGORY_SEPARATOR:"|",VALUE_SEPARATOR:",",OVERFLOW_TRUE:"1",OVERFLOW_FALSE:"0",UNKNOWN_ERROR:"unknown_error"},E={BEARER:"Bearer",POP:"pop",SSH:"ssh-cert"},k={DEFAULT_THROTTLE_TIME_SECONDS:60,DEFAULT_MAX_THROTTLE_TIME_SECONDS:3600,THROTTLING_PREFIX:"throttling",X_MS_LIB_CAPABILITY_VALUE:"retry-after, h429"},R={INVALID_GRANT_ERROR:"invalid_grant",CLIENT_MISMATCH_ERROR:"client_mismatch"},_={username:"username",password:"password"},b={httpSuccess:200,httpBadRequest:400},O={FAILED_AUTO_DETECTION:"1",INTERNAL_CACHE:"2",ENVIRONMENT_VARIABLE:"3",IMDS:"4"},N={CONFIGURED_MATCHES_DETECTED:"1",CONFIGURED_NO_AUTO_DETECTION:"2",CONFIGURED_NOT_DETECTED:"3",AUTO_DETECTION_REQUESTED_SUCCESSFUL:"4",AUTO_DETECTION_REQUESTED_FAILED:"5"},P={NOT_APPLICABLE:"0",FORCE_REFRESH_OR_CLAIMS:"1",NO_CACHED_ACCESS_TOKEN:"2",CACHED_ACCESS_TOKEN_EXPIRED:"3",PROACTIVELY_REFRESHED:"4"},M={Jwt:"JWT",Jwk:"JWK",Pop:"pop"},U=300}}]);