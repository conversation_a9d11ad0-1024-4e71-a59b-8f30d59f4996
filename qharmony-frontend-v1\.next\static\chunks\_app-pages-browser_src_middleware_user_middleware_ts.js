"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_src_middleware_user_middleware_ts"],{

/***/ "(app-pages-browser)/./src/middleware/user_middleware.ts":
/*!*******************************************!*\
  !*** ./src/middleware/user_middleware.ts ***!
  \*******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   onboardAdmin: function() { return /* binding */ onboardAdmin; },\n/* harmony export */   onboardEmployee: function() { return /* binding */ onboardEmployee; },\n/* harmony export */   parseParamsFromUrl: function() { return /* binding */ parseParamsFromUrl; },\n/* harmony export */   selfOnboard: function() { return /* binding */ selfOnboard; },\n/* harmony export */   teamsSelfOnboard: function() { return /* binding */ teamsSelfOnboard; }\n/* harmony export */ });\n/* harmony import */ var _APILayer_axios_helper__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/APILayer/axios_helper */ \"(app-pages-browser)/./src/APILayer/axios_helper.ts\");\n\nasync function onboardAdmin(admin, company) {\n    console.log(\"onboardAdmin called\");\n    const data = {\n        company,\n        user: admin\n    };\n    try {\n        const response = await (0,_APILayer_axios_helper__WEBPACK_IMPORTED_MODULE_0__.postRequest)(\"/admin/onboard\", data);\n        console.log(\"Response from onboardAdmin:\", response);\n        return response.data;\n    } catch (error) {\n        console.error(\"Error in onboardAdmin:\", error);\n        throw error; // Re-throw to let the caller handle it\n    }\n}\nasync function parseParamsFromUrl(url) {\n    const response = await (0,_APILayer_axios_helper__WEBPACK_IMPORTED_MODULE_0__.postRequest)(\"/auth/parse-params\", {\n        link: url\n    });\n    return response.data;\n}\nasync function selfOnboard(email) {\n    const response = await (0,_APILayer_axios_helper__WEBPACK_IMPORTED_MODULE_0__.postRequest)(\"/user/self-onboard\", {\n        userEmail: email\n    });\n    return response.data.data;\n}\nasync function teamsSelfOnboard(email, tenantId) {\n    const response = await (0,_APILayer_axios_helper__WEBPACK_IMPORTED_MODULE_0__.postRequest)(\"/teams/user/self-onboard\", {\n        userEmail: email,\n        tenantId\n    });\n    return response.data;\n}\nasync function onboardEmployee(companyId, userId) {\n    const response = await (0,_APILayer_axios_helper__WEBPACK_IMPORTED_MODULE_0__.postRequest)(\"/employee/onboard\", {\n        companyId: companyId,\n        userId: userId\n    });\n    console.log(\"Response from onboardEmployee:\", response);\n    return response.data;\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/middleware/user_middleware.ts\n"));

/***/ })

}]);