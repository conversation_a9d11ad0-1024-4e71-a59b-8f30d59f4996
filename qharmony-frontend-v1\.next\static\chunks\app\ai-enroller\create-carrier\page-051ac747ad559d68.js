(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9417],{71818:function(e,a,s){Promise.resolve().then(s.bind(s,17990))},17990:function(e,a,s){"use strict";s.r(a);var c=s(57437),r=s(29),l=s.n(r),t=s(2265),n=s(99376),o=s(18913);s(2778);let i=["PPO","HMO","HDHP","MEC","EPO","POS","Indemnity","Term Life","Whole Life","STD","LTD"],d=["Health Insurance","Ancillary Benefits","Life & Disability Insurance","Voluntary Benefits","Wellness & Mental Health","Spending & Savings Accounts","Financial Benefits","Retirement Benefits","Time Off & Leave","Family & Caregiver Support","Career & Development","Workplace Environment","Life Events"],x=["Medical","Dental","Vision","Term Life","Supplemental Life Insurance","Short-Term Disability","Long-Term Disability","Whole Life","Group (Employer) Life","Hospital Indemnity","Accident Insurance","Critical Illness Insurance","Cancer Insurance","Gap Insurance","Legal Insurance","Identity Theft Protection","Accident & Illness (Pets)","Nursing Care / Custodial Care","Wellness Programs","Employee Assistance Program","Gym Membership","Health Savings Account","Flexible Savings Accounts","Commuter Benefits","Technology Stipend","Pay & Bonus","Stock Options","Student Loan Assistance","401(k)","403(b)","Pension Plan","Paid Time Off (PTO)","Parental Leave","Family and Medical Leave","Paid Volunteer Time","On-site Child Care","Employee Training & Development","Tuition Reimbursement","Employee Recognition","Performance Goals & Process","Pet-friendly Workplace","Ergonomic Workplace","Company Handbook","Marriage or Divorce","New Baby or Adoption","Loss of Insurance"],m=["AL","AK","AZ","AR","CA","CO","CT","DE","FL","GA","HI","ID","IL","IN","IA","KS","KY","LA","ME","MD","MA","MI","MN","MS","MO","MT","NE","NV","NH","NJ","NM","NY","NC","ND","OH","OK","OR","PA","RI","SC","SD","TN","TX","UT","VT","VA","WA","WV","WI","WY"],u=["API_KEY","OAUTH","BASIC_AUTH","CERTIFICATE"],p=["EDI","JSON","XML"];a.default=()=>{let e=(0,n.useRouter)(),[a,s]=(0,t.useState)(!1),[r,f]=(0,t.useState)(null),[g,b]=(0,t.useState)(!1),[h,j]=(0,t.useState)({carrierName:"",carrierCode:"",displayName:"",isSystemCarrier:!0,contactInfo:{phone:"",email:"",website:"",supportEmail:"",claimsPhone:"",memberServicesPhone:""},supportedPlanTypes:[],supportedCoverageTypes:[],supportedCoverageSubTypes:[],integration:{ediCapable:!1,apiEndpoint:"",apiVersion:"",authMethod:"API_KEY",dataFormat:"JSON"},licenseStates:[],amRating:"",networkName:""});(0,t.useEffect)(()=>{console.log("Create carrier page loaded - superadmin check disabled for testing")},[e]);let y=(e,a)=>{if(e.includes(".")){let[s,c]=e.split(".");j(e=>{let r=e[s];return"object"!=typeof r||null===r||Array.isArray(r)?e:{...e,[s]:{...r,[c]:a}}})}else j(s=>({...s,[e]:a}))},N=(e,a)=>{j(s=>{let c=s[e],r=c.includes(a)?c.filter(e=>e!==a):[...c,a];return{...s,[e]:r}})},v=(e,a)=>{j(s=>{let c=s[e],r=a.every(e=>c.includes(e));return{...s,[e]:r?[]:a}})},C=async a=>{a.preventDefault(),s(!0),f(null);try{let a="http://localhost:8080",s=localStorage.getItem("userId")||localStorage.getItem("userid1");if(!s)throw Error("User ID not found. Please authenticate first.");if(console.log("Creating carrier with data:",h),console.log("API URL:","".concat(a,"/api/pre-enrollment/carriers/create")),console.log("User ID:",s),!h.carrierName||!h.carrierCode)throw Error("Carrier name and code are required");console.log("Form validation passed, sending request...");let c=await fetch("".concat(a,"/api/pre-enrollment/carriers/create"),{method:"POST",headers:{"Content-Type":"application/json","user-id":s},body:JSON.stringify(h)});if(console.log("Response status:",c.status),console.log("Response headers:",c.headers),!c.ok){let e=await c.text();console.log("Error response text:",e);try{let a=JSON.parse(e);throw Error(a.error||"Failed to create carrier")}catch(a){throw Error("HTTP ".concat(c.status,": ").concat(e||"Failed to create carrier"))}}let r=await c.json();console.log("Success response:",r),b(!0),setTimeout(()=>{e.push("/ai-enroller")},2e3)}catch(e){f(e instanceof Error?e.message:"Failed to create carrier")}finally{s(!1)}};return g?(0,c.jsx)("div",{className:"min-h-screen bg-white flex items-center justify-center",children:(0,c.jsxs)("div",{className:"text-center",children:[(0,c.jsx)("div",{className:"w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,c.jsx)(o.Sul,{className:"w-8 h-8 text-green-600"})}),(0,c.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-2",children:"Carrier Created Successfully!"}),(0,c.jsx)("p",{className:"text-gray-600 mb-4",children:"The carrier has been created and is now available in the system."}),(0,c.jsx)("p",{className:"text-sm text-gray-500",children:"Redirecting to dashboard..."})]})}):(0,c.jsxs)("div",{className:"jsx-760cc8fa2520ad01 min-h-screen bg-white",children:[(0,c.jsx)(l(),{id:"760cc8fa2520ad01",children:"input.jsx-760cc8fa2520ad01,select.jsx-760cc8fa2520ad01,textarea.jsx-760cc8fa2520ad01{background-color:white!important;color:black!important}input.jsx-760cc8fa2520ad01::-webkit-input-placeholder{color:#9ca3af!important}input.jsx-760cc8fa2520ad01:-moz-placeholder{color:#9ca3af!important}input.jsx-760cc8fa2520ad01::-moz-placeholder{color:#9ca3af!important}input.jsx-760cc8fa2520ad01:-ms-input-placeholder{color:#9ca3af!important}input.jsx-760cc8fa2520ad01::-ms-input-placeholder{color:#9ca3af!important}input.jsx-760cc8fa2520ad01::placeholder{color:#9ca3af!important}"}),(0,c.jsxs)("div",{className:"jsx-760cc8fa2520ad01 max-w-4xl mx-auto py-8 px-4 sm:px-6 lg:px-8",children:[(0,c.jsxs)("div",{className:"jsx-760cc8fa2520ad01 mb-8",children:[(0,c.jsxs)("div",{className:"jsx-760cc8fa2520ad01 flex items-center gap-3 mb-4",children:[(0,c.jsx)("div",{className:"jsx-760cc8fa2520ad01 w-10 h-10 bg-gray-900 rounded-lg flex items-center justify-center",children:(0,c.jsx)(o.$xp,{className:"w-6 h-6 text-white"})}),(0,c.jsxs)("div",{className:"jsx-760cc8fa2520ad01",children:[(0,c.jsx)("h1",{style:{fontSize:"clamp(20px, 5vw, 24px)",fontWeight:"600",color:"#111827",fontFamily:"sans-serif"},className:"jsx-760cc8fa2520ad01",children:"Create New Carrier"}),(0,c.jsx)("p",{style:{fontSize:"clamp(12px, 3vw, 14px)",lineHeight:"1.4",color:"#6b7280",fontFamily:"sans-serif"},className:"jsx-760cc8fa2520ad01",children:"Add a new insurance carrier to the system"})]})]}),(0,c.jsxs)("nav",{className:"jsx-760cc8fa2520ad01 text-sm text-gray-500",children:[(0,c.jsx)("span",{onClick:()=>e.push("/ai-enroller"),className:"jsx-760cc8fa2520ad01 hover:text-gray-700 cursor-pointer",children:"Dashboard"}),(0,c.jsx)("span",{className:"jsx-760cc8fa2520ad01 mx-2",children:"/"}),(0,c.jsx)("span",{className:"jsx-760cc8fa2520ad01 text-gray-900",children:"Create Carrier"})]})]}),r&&(0,c.jsx)("div",{className:"jsx-760cc8fa2520ad01 mb-6 bg-red-50 border border-red-200 rounded-lg p-4",children:(0,c.jsxs)("div",{className:"jsx-760cc8fa2520ad01 flex items-center gap-2",children:[(0,c.jsx)(o.fMW,{className:"w-5 h-5 text-red-600"}),(0,c.jsx)("p",{className:"jsx-760cc8fa2520ad01 text-red-600 text-sm",children:r})]})}),(0,c.jsxs)("form",{onSubmit:C,className:"jsx-760cc8fa2520ad01 space-y-8",children:[(0,c.jsxs)("div",{className:"jsx-760cc8fa2520ad01 bg-gray-50 rounded-lg p-6",children:[(0,c.jsx)("h3",{style:{fontSize:"clamp(18px, 4vw, 20px)",fontWeight:"600",color:"#111827",marginBottom:"1rem",fontFamily:"sans-serif"},className:"jsx-760cc8fa2520ad01",children:"Basic Information"}),(0,c.jsxs)("div",{className:"jsx-760cc8fa2520ad01 grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,c.jsxs)("div",{className:"jsx-760cc8fa2520ad01",children:[(0,c.jsx)("label",{className:"jsx-760cc8fa2520ad01 block text-sm font-medium text-gray-700 mb-2",children:"Carrier Name *"}),(0,c.jsx)("input",{type:"text",required:!0,value:h.carrierName,onChange:e=>y("carrierName",e.target.value),style:{backgroundColor:"white",color:"black"},placeholder:"e.g., Blue Cross Blue Shield",className:"jsx-760cc8fa2520ad01 w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"})]}),(0,c.jsxs)("div",{className:"jsx-760cc8fa2520ad01",children:[(0,c.jsx)("label",{className:"jsx-760cc8fa2520ad01 block text-sm font-medium text-gray-700 mb-2",children:"Carrier Code *"}),(0,c.jsx)("input",{type:"text",required:!0,value:h.carrierCode,onChange:e=>y("carrierCode",e.target.value.toUpperCase()),style:{backgroundColor:"white",color:"black"},placeholder:"e.g., BCBS",className:"jsx-760cc8fa2520ad01 w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"})]}),(0,c.jsxs)("div",{className:"jsx-760cc8fa2520ad01",children:[(0,c.jsx)("label",{className:"jsx-760cc8fa2520ad01 block text-sm font-medium text-gray-700 mb-2",children:"Display Name"}),(0,c.jsx)("input",{type:"text",value:h.displayName,onChange:e=>y("displayName",e.target.value),style:{backgroundColor:"white",color:"black"},placeholder:"User-friendly display name",className:"jsx-760cc8fa2520ad01 w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"})]}),(0,c.jsxs)("div",{className:"jsx-760cc8fa2520ad01 flex items-center",children:[(0,c.jsx)("input",{type:"checkbox",id:"isSystemCarrier",checked:h.isSystemCarrier,onChange:e=>y("isSystemCarrier",e.target.checked),className:"jsx-760cc8fa2520ad01 w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"}),(0,c.jsx)("label",{htmlFor:"isSystemCarrier",className:"jsx-760cc8fa2520ad01 ml-2 text-sm text-gray-700",children:"System Carrier (Available to all brokers)"})]})]})]}),(0,c.jsxs)("div",{className:"jsx-760cc8fa2520ad01 bg-gray-50 rounded-lg p-6",children:[(0,c.jsx)("h3",{style:{fontSize:"clamp(18px, 4vw, 20px)",fontWeight:"600",color:"#111827",marginBottom:"1rem",fontFamily:"sans-serif"},className:"jsx-760cc8fa2520ad01",children:"Contact Information"}),(0,c.jsxs)("div",{className:"jsx-760cc8fa2520ad01 grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,c.jsxs)("div",{className:"jsx-760cc8fa2520ad01",children:[(0,c.jsxs)("label",{className:"jsx-760cc8fa2520ad01 block text-sm font-medium text-gray-700 mb-2",children:[(0,c.jsx)(o.PES,{className:"w-4 h-4 inline mr-1"}),"Phone"]}),(0,c.jsx)("input",{type:"tel",value:h.contactInfo.phone,onChange:e=>y("contactInfo.phone",e.target.value),placeholder:"(*************",className:"jsx-760cc8fa2520ad01 w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"})]}),(0,c.jsxs)("div",{className:"jsx-760cc8fa2520ad01",children:[(0,c.jsxs)("label",{className:"jsx-760cc8fa2520ad01 block text-sm font-medium text-gray-700 mb-2",children:[(0,c.jsx)(o.Zuw,{className:"w-4 h-4 inline mr-1"}),"Email"]}),(0,c.jsx)("input",{type:"email",value:h.contactInfo.email,onChange:e=>y("contactInfo.email",e.target.value),placeholder:"<EMAIL>",className:"jsx-760cc8fa2520ad01 w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"})]}),(0,c.jsxs)("div",{className:"jsx-760cc8fa2520ad01",children:[(0,c.jsxs)("label",{className:"jsx-760cc8fa2520ad01 block text-sm font-medium text-gray-700 mb-2",children:[(0,c.jsx)(o.goM,{className:"w-4 h-4 inline mr-1"}),"Website"]}),(0,c.jsx)("input",{type:"url",value:h.contactInfo.website,onChange:e=>y("contactInfo.website",e.target.value),placeholder:"https://www.carrier.com",className:"jsx-760cc8fa2520ad01 w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"})]}),(0,c.jsxs)("div",{className:"jsx-760cc8fa2520ad01",children:[(0,c.jsx)("label",{className:"jsx-760cc8fa2520ad01 block text-sm font-medium text-gray-700 mb-2",children:"Support Email"}),(0,c.jsx)("input",{type:"email",value:h.contactInfo.supportEmail,onChange:e=>y("contactInfo.supportEmail",e.target.value),placeholder:"<EMAIL>",className:"jsx-760cc8fa2520ad01 w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"})]}),(0,c.jsxs)("div",{className:"jsx-760cc8fa2520ad01",children:[(0,c.jsx)("label",{className:"jsx-760cc8fa2520ad01 block text-sm font-medium text-gray-700 mb-2",children:"Claims Phone"}),(0,c.jsx)("input",{type:"tel",value:h.contactInfo.claimsPhone,onChange:e=>y("contactInfo.claimsPhone",e.target.value),placeholder:"(*************",className:"jsx-760cc8fa2520ad01 w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"})]}),(0,c.jsxs)("div",{className:"jsx-760cc8fa2520ad01",children:[(0,c.jsx)("label",{className:"jsx-760cc8fa2520ad01 block text-sm font-medium text-gray-700 mb-2",children:"Member Services Phone"}),(0,c.jsx)("input",{type:"tel",value:h.contactInfo.memberServicesPhone,onChange:e=>y("contactInfo.memberServicesPhone",e.target.value),placeholder:"(*************",className:"jsx-760cc8fa2520ad01 w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"})]})]})]}),(0,c.jsxs)("div",{className:"jsx-760cc8fa2520ad01 bg-gray-50 rounded-lg p-6",children:[(0,c.jsxs)("div",{className:"jsx-760cc8fa2520ad01 flex items-center justify-between mb-4",children:[(0,c.jsx)("h3",{className:"jsx-760cc8fa2520ad01 text-lg font-semibold text-gray-900",children:"Supported Plan Types"}),(0,c.jsx)("button",{type:"button",onClick:()=>v("supportedPlanTypes",i),className:"jsx-760cc8fa2520ad01 px-3 py-1 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors",children:i.every(e=>h.supportedPlanTypes.includes(e))?"Deselect All":"Select All"})]}),(0,c.jsx)("div",{className:"jsx-760cc8fa2520ad01 grid grid-cols-2 md:grid-cols-4 gap-3",children:i.map(e=>(0,c.jsxs)("label",{className:"jsx-760cc8fa2520ad01 flex items-center space-x-2 cursor-pointer",children:[(0,c.jsx)("input",{type:"checkbox",checked:h.supportedPlanTypes.includes(e),onChange:()=>N("supportedPlanTypes",e),className:"jsx-760cc8fa2520ad01 w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"}),(0,c.jsx)("span",{className:"jsx-760cc8fa2520ad01 text-sm text-gray-700",children:e})]},e))})]}),(0,c.jsxs)("div",{className:"jsx-760cc8fa2520ad01 bg-gray-50 rounded-lg p-6",children:[(0,c.jsxs)("div",{className:"jsx-760cc8fa2520ad01 flex items-center justify-between mb-4",children:[(0,c.jsx)("h3",{className:"jsx-760cc8fa2520ad01 text-lg font-semibold text-gray-900",children:"Supported Coverage Types"}),(0,c.jsx)("button",{type:"button",onClick:()=>v("supportedCoverageTypes",d),className:"jsx-760cc8fa2520ad01 px-3 py-1 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors",children:d.every(e=>h.supportedCoverageTypes.includes(e))?"Deselect All":"Select All"})]}),(0,c.jsx)("div",{className:"jsx-760cc8fa2520ad01 grid grid-cols-1 md:grid-cols-2 gap-3",children:d.map(e=>(0,c.jsxs)("label",{className:"jsx-760cc8fa2520ad01 flex items-center space-x-2 cursor-pointer",children:[(0,c.jsx)("input",{type:"checkbox",checked:h.supportedCoverageTypes.includes(e),onChange:()=>N("supportedCoverageTypes",e),className:"jsx-760cc8fa2520ad01 w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"}),(0,c.jsx)("span",{className:"jsx-760cc8fa2520ad01 text-sm text-gray-700",children:e})]},e))})]}),(0,c.jsxs)("div",{className:"jsx-760cc8fa2520ad01 bg-gray-50 rounded-lg p-6",children:[(0,c.jsxs)("div",{className:"jsx-760cc8fa2520ad01 flex items-center justify-between mb-4",children:[(0,c.jsx)("h3",{className:"jsx-760cc8fa2520ad01 text-lg font-semibold text-gray-900",children:"Supported Coverage Subtypes"}),(0,c.jsx)("button",{type:"button",onClick:()=>v("supportedCoverageSubTypes",x),className:"jsx-760cc8fa2520ad01 px-3 py-1 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors",children:x.every(e=>h.supportedCoverageSubTypes.includes(e))?"Deselect All":"Select All"})]}),(0,c.jsx)("div",{className:"jsx-760cc8fa2520ad01 grid grid-cols-2 md:grid-cols-3 gap-3 max-h-60 overflow-y-auto",children:x.map(e=>(0,c.jsxs)("label",{className:"jsx-760cc8fa2520ad01 flex items-center space-x-2 cursor-pointer",children:[(0,c.jsx)("input",{type:"checkbox",checked:h.supportedCoverageSubTypes.includes(e),onChange:()=>N("supportedCoverageSubTypes",e),className:"jsx-760cc8fa2520ad01 w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"}),(0,c.jsx)("span",{className:"jsx-760cc8fa2520ad01 text-sm text-gray-700",children:e})]},e))})]}),(0,c.jsxs)("div",{className:"jsx-760cc8fa2520ad01 bg-gray-50 rounded-lg p-6",children:[(0,c.jsx)("h3",{className:"jsx-760cc8fa2520ad01 text-lg font-semibold text-gray-900 mb-4",children:"Integration Settings"}),(0,c.jsxs)("div",{className:"jsx-760cc8fa2520ad01 space-y-6",children:[(0,c.jsxs)("div",{className:"jsx-760cc8fa2520ad01 flex items-center",children:[(0,c.jsx)("input",{type:"checkbox",id:"ediCapable",checked:h.integration.ediCapable,onChange:e=>y("integration.ediCapable",e.target.checked),className:"jsx-760cc8fa2520ad01 w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"}),(0,c.jsx)("label",{htmlFor:"ediCapable",className:"jsx-760cc8fa2520ad01 ml-2 text-sm text-gray-700",children:"EDI Capable (Electronic Data Interchange)"})]}),(0,c.jsxs)("div",{className:"jsx-760cc8fa2520ad01 grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,c.jsxs)("div",{className:"jsx-760cc8fa2520ad01",children:[(0,c.jsx)("label",{className:"jsx-760cc8fa2520ad01 block text-sm font-medium text-gray-700 mb-2",children:"API Endpoint"}),(0,c.jsx)("input",{type:"url",value:h.integration.apiEndpoint,onChange:e=>y("integration.apiEndpoint",e.target.value),placeholder:"https://api.carrier.com/v1",className:"jsx-760cc8fa2520ad01 w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"})]}),(0,c.jsxs)("div",{className:"jsx-760cc8fa2520ad01",children:[(0,c.jsx)("label",{className:"jsx-760cc8fa2520ad01 block text-sm font-medium text-gray-700 mb-2",children:"API Version"}),(0,c.jsx)("input",{type:"text",value:h.integration.apiVersion,onChange:e=>y("integration.apiVersion",e.target.value),placeholder:"v1.0",className:"jsx-760cc8fa2520ad01 w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"})]}),(0,c.jsxs)("div",{className:"jsx-760cc8fa2520ad01",children:[(0,c.jsx)("label",{className:"jsx-760cc8fa2520ad01 block text-sm font-medium text-gray-700 mb-2",children:"Authentication Method"}),(0,c.jsx)("select",{value:h.integration.authMethod,onChange:e=>y("integration.authMethod",e.target.value),className:"jsx-760cc8fa2520ad01 w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500",children:u.map(e=>(0,c.jsx)("option",{value:e,className:"jsx-760cc8fa2520ad01",children:e},e))})]}),(0,c.jsxs)("div",{className:"jsx-760cc8fa2520ad01",children:[(0,c.jsx)("label",{className:"jsx-760cc8fa2520ad01 block text-sm font-medium text-gray-700 mb-2",children:"Data Format"}),(0,c.jsx)("select",{value:h.integration.dataFormat,onChange:e=>y("integration.dataFormat",e.target.value),className:"jsx-760cc8fa2520ad01 w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500",children:p.map(e=>(0,c.jsx)("option",{value:e,className:"jsx-760cc8fa2520ad01",children:e},e))})]})]})]})]}),(0,c.jsxs)("div",{className:"jsx-760cc8fa2520ad01 bg-gray-50 rounded-lg p-6",children:[(0,c.jsx)("h3",{className:"jsx-760cc8fa2520ad01 text-lg font-semibold text-gray-900 mb-4",children:"Business Information"}),(0,c.jsxs)("div",{className:"jsx-760cc8fa2520ad01 grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,c.jsxs)("div",{className:"jsx-760cc8fa2520ad01",children:[(0,c.jsx)("label",{className:"jsx-760cc8fa2520ad01 block text-sm font-medium text-gray-700 mb-2",children:"A.M. Best Rating"}),(0,c.jsx)("input",{type:"text",value:h.amRating,onChange:e=>y("amRating",e.target.value),placeholder:"e.g., A++",className:"jsx-760cc8fa2520ad01 w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"})]}),(0,c.jsxs)("div",{className:"jsx-760cc8fa2520ad01",children:[(0,c.jsx)("label",{className:"jsx-760cc8fa2520ad01 block text-sm font-medium text-gray-700 mb-2",children:"Network Name"}),(0,c.jsx)("input",{type:"text",value:h.networkName,onChange:e=>y("networkName",e.target.value),placeholder:"e.g., Choice Plus Network",className:"jsx-760cc8fa2520ad01 w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"})]})]}),(0,c.jsxs)("div",{className:"jsx-760cc8fa2520ad01 mt-6",children:[(0,c.jsxs)("div",{className:"jsx-760cc8fa2520ad01 flex items-center justify-between mb-3",children:[(0,c.jsx)("label",{className:"jsx-760cc8fa2520ad01 block text-sm font-medium text-gray-700",children:"Licensed States"}),(0,c.jsx)("button",{type:"button",onClick:()=>v("licenseStates",m),className:"jsx-760cc8fa2520ad01 px-2 py-1 text-xs bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors",children:m.every(e=>h.licenseStates.includes(e))?"Deselect All":"Select All"})]}),(0,c.jsx)("div",{className:"jsx-760cc8fa2520ad01 grid grid-cols-5 md:grid-cols-10 gap-2 max-h-40 overflow-y-auto",children:m.map(e=>(0,c.jsxs)("label",{className:"jsx-760cc8fa2520ad01 flex items-center space-x-1 cursor-pointer",children:[(0,c.jsx)("input",{type:"checkbox",checked:h.licenseStates.includes(e),onChange:()=>N("licenseStates",e),className:"jsx-760cc8fa2520ad01 w-3 h-3 text-blue-600 border-gray-300 rounded focus:ring-blue-500"}),(0,c.jsx)("span",{className:"jsx-760cc8fa2520ad01 text-xs text-gray-700",children:e})]},e))})]})]}),(0,c.jsxs)("div",{className:"jsx-760cc8fa2520ad01 flex justify-end space-x-4 pt-6 border-t border-gray-200",children:[(0,c.jsx)("button",{type:"button",onClick:()=>e.push("/ai-enroller"),className:"jsx-760cc8fa2520ad01 px-6 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors",children:"Cancel"}),(0,c.jsx)("button",{type:"submit",disabled:a,className:"jsx-760cc8fa2520ad01 px-6 py-2 bg-gray-900 text-white rounded-lg hover:bg-gray-800 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center gap-2",children:a?(0,c.jsxs)(c.Fragment,{children:[(0,c.jsx)("div",{className:"jsx-760cc8fa2520ad01 w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"}),"Creating..."]}):(0,c.jsxs)(c.Fragment,{children:[(0,c.jsx)(o.r7I,{className:"w-4 h-4"}),"Create Carrier"]})})]})]})]})]})}},2778:function(){}},function(e){e.O(0,[2461,8422,1507,2971,2117,1744],function(){return e(e.s=71818)}),_N_E=e.O()}]);