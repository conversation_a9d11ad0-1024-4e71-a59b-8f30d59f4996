(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1791],{80053:function(e,a,t){Promise.resolve().then(t.bind(t,40363))},40363:function(e,a,t){"use strict";t.r(a),t.d(a,{default:function(){return j}});var i=t(57437),l=t(29),r=t.n(l),s=t(2265),n=t(99376),o=t(27648),c=t(33145),d=t(48223),p=t(24079),h=()=>{let e=(0,n.useRouter)();return(0,s.useEffect)(()=>{let a=["/ai-enroller","/ai-enroller/plans","/ai-enroller/manage-groups"],t=setTimeout(()=>{a.forEach(a=>{e.prefetch(a)})},200);return["/brea.png"].forEach(e=>{new Image().src=e}),window.formPatterns={planCode:/^[A-Z0-9-_]{3,20}$/i,email:/^[^\s@]+@[^\s@]+\.[^\s@]+$/,url:/^https?:\/\/.+/},(()=>{try{let e="ai-enroller-test";localStorage.setItem(e,"test"),localStorage.removeItem(e),window.storageAvailable=!0}catch(e){window.storageAvailable=!1,console.warn("localStorage not available")}})(),()=>{clearTimeout(t)}},[e]),null},m=t(24902),u=t(32768),g=t(99859),v=t(18913),f=t(84154);t(43010);var x=t(63055),j=()=>{let e=(0,n.useRouter)(),[a,t]=(0,s.useState)(1),[l,j]=(0,s.useState)(null),[y,b]=(0,s.useState)(!0),[N,w]=(0,s.useState)(null),[C,D]=(0,s.useState)({isChecking:!1,isDuplicate:!1}),[P,T]=(0,s.useState)({isChecking:!1,isDuplicate:!1});(0,p.$)("Create Plan Page");let[k,S]=(0,s.useState)({planName:"",planCode:"",carrier:"",planType:"",coverageCategory:"",coverageType:"",metalTier:"",videoUrl:"",documents:[],description:"",highlights:[""],effectiveDate:"",endDate:"",copay:"",deductible:""}),I=[{number:1,title:"Documents",subtitle:"Upload files",active:1===a,completed:a>1,tooltip:"Upload plan documents, brochures, and supporting materials (optional)"},{number:2,title:"Basic Info",subtitle:"Plan details",active:2===a,completed:a>2,tooltip:"Enter plan name, carrier, coverage type, and metal tier information"},{number:3,title:"Description",subtitle:"Details & video",active:3===a,completed:a>3,tooltip:"Add plan description, key highlights, and optional video content"},{number:4,title:"Preview",subtitle:"Review & create",active:4===a,completed:a>4,tooltip:"Review all plan details and create your new plan"},{number:5,title:"Success",subtitle:"Plan created",active:5===a,completed:!1,tooltip:"Plan successfully created and added to your catalog"}],z=(0,s.useMemo)(()=>(0,x.HP)(),[]);(0,s.useEffect)(()=>{localStorage.removeItem("ai-enroller-draft-plan"),e.prefetch("/ai-enroller"),e.prefetch("/ai-enroller/plans"),(async()=>{var e,a,t,i,l,r,s,n;b(!0);try{let l=sessionStorage.getItem("ai-enroller-create-plan-data"),r=sessionStorage.getItem("ai-enroller-create-plan-cache-time"),s=r?Date.now()-parseInt(r):1/0;if(l&&s<3e5){let e=JSON.parse(l);w(e),b(!1);let a=localStorage.getItem("ai-enroller-draft-plan");if(a&&!k.planName&&!k.coverageCategory){let e=JSON.parse(a);console.log("Loading draft data from cache:",e),S(e)}return}let n=Promise.race([(0,x.ie)(),new Promise((e,a)=>setTimeout(()=>a(Error("Carriers API timeout")),3e3))]),o=await n.catch(e=>(console.warn("Carriers API failed or timed out:",e),{success:!1,error:e.message})),c={plans:[],templates:[],carriers:o.success&&o.data||[],planTypes:(null===(e=z.data)||void 0===e?void 0:e.planTypes)||[],coverageCategories:(null===(a=z.data)||void 0===a?void 0:a.coverageCategories)||[],coverageMap:(null===(t=z.data)||void 0===t?void 0:t.coverageMap)||{},metalTiers:(null===(i=z.data)||void 0===i?void 0:i.metalTiers)||[]};w(c),console.log("Data loaded successfully:",c),console.log("Coverage categories:",c.coverageCategories),console.log("Loaded carriers:",c.carriers),sessionStorage.setItem("ai-enroller-create-plan-data",JSON.stringify(c)),sessionStorage.setItem("ai-enroller-create-plan-cache-time",Date.now().toString());let d=localStorage.getItem("ai-enroller-draft-plan");if(d&&!k.planName&&!k.coverageCategory){let e=JSON.parse(d);console.log("Loading draft data from fresh load:",e),S(e)}}catch(e){console.error("Error loading data:",e),w({plans:[],templates:[],carriers:[],planTypes:(null===(l=z.data)||void 0===l?void 0:l.planTypes)||[],coverageCategories:(null===(r=z.data)||void 0===r?void 0:r.coverageCategories)||[],coverageMap:(null===(s=z.data)||void 0===s?void 0:s.coverageMap)||{},metalTiers:(null===(n=z.data)||void 0===n?void 0:n.metalTiers)||[]})}finally{b(!1)}})()},[e,z]);let O=(0,s.useMemo)(()=>{let e;return a=>{clearTimeout(e),e=setTimeout(()=>{localStorage.setItem("ai-enroller-draft-plan",JSON.stringify(a))},500)}},[]),F=(0,s.useMemo)(()=>{let e;return a=>{if(clearTimeout(e),!a.trim()){D({isChecking:!1,isDuplicate:!1});return}D(e=>({...e,isChecking:!0,error:void 0})),e=setTimeout(async()=>{try{let e=await (0,x.nR)(a);e.success&&e.data?D({isChecking:!1,isDuplicate:e.data.isDuplicate,existingPlan:e.data.existingPlan}):D({isChecking:!1,isDuplicate:!1,error:e.error||"Failed to check for duplicates"})}catch(e){D({isChecking:!1,isDuplicate:!1,error:"Error checking for duplicates"})}},800)}},[]),E=(0,s.useMemo)(()=>{let e;return a=>{if(clearTimeout(e),!a.trim()){T({isChecking:!1,isDuplicate:!1});return}T(e=>({...e,isChecking:!0,error:void 0})),e=setTimeout(async()=>{try{let e=await (0,x.yW)(a);e.success&&e.data?T({isChecking:!1,isDuplicate:e.data.isDuplicate,existingPlan:e.data.existingPlan}):T({isChecking:!1,isDuplicate:!1,error:e.error||"Failed to check for duplicates"})}catch(e){T({isChecking:!1,isDuplicate:!1,error:"Error checking for duplicates"})}},800)}},[]);(0,s.useEffect)(()=>{k.planName&&k.planName.trim()&&(console.log("\uD83D\uDD04 Form data changed: Checking plan name for duplicates"),F(k.planName))},[k.planName,F]),(0,s.useEffect)(()=>{k.planCode&&k.planCode.trim()&&(console.log("\uD83D\uDD04 Form data changed: Checking plan code for duplicates"),E(k.planCode))},[k.planCode,E]),(0,s.useEffect)(()=>{let e=null,a=(a,i)=>{t();let l=a.getBoundingClientRect(),r=l.left+l.width/2-140;r<10&&(r=10),r+280>window.innerWidth-10&&(r=window.innerWidth-280-10);let s=l.top,n=window.innerHeight-l.bottom,o=s<120&&n>120,c=document.createElement("div");c.className="custom-tooltip",c.textContent=i,c.style.cssText="\n        position: fixed;\n        ".concat(o?"top: ".concat(l.bottom+4,"px;"):"bottom: ".concat(window.innerHeight-l.top+4,"px;"),"\n        left: ").concat(r,"px;\n        width: 280px;\n        background: #1f2937;\n        color: white;\n        padding: 0.75rem 1rem;\n        border-radius: 0.5rem;\n        font-size: 0.8rem;\n        font-weight: 400;\n        line-height: 1.4;\n        text-align: left;\n        white-space: normal;\n        word-wrap: break-word;\n        hyphens: auto;\n        box-shadow: 0 8px 16px rgba(0, 0, 0, 0.15);\n        z-index: 99999;\n        pointer-events: none;\n        opacity: 0;\n        transition: opacity 0.2s ease-in-out;\n      ");let d=document.createElement("div");d.className="custom-tooltip-arrow",d.style.cssText="\n        position: fixed;\n        ".concat(o?"top: ".concat(l.bottom-2,"px;"):"bottom: ".concat(window.innerHeight-l.top-2,"px;"),"\n        left: ").concat(l.left+l.width/2-6,"px;\n        width: 0;\n        height: 0;\n        border-left: 6px solid transparent;\n        border-right: 6px solid transparent;\n        ").concat(o?"border-bottom: 6px solid #1f2937;":"border-top: 6px solid #1f2937;","\n        z-index: 100000;\n        pointer-events: none;\n        opacity: 0;\n        transition: opacity 0.2s ease-in-out;\n      "),document.body.appendChild(c),document.body.appendChild(d),requestAnimationFrame(()=>{c.style.opacity="1",d.style.opacity="1"}),(e=c).arrow=d},t=()=>{e&&(e.remove(),e.arrow&&e.arrow.remove(),e=null)},i=e=>{let t=e.target,i=t.getAttribute("data-tooltip");i&&a(t,i)},l=()=>{t()},r=document.querySelectorAll(".tooltip-icon[data-tooltip]");return r.forEach(e=>{e.addEventListener("mouseenter",i),e.addEventListener("mouseleave",l)}),()=>{t(),r.forEach(e=>{e.removeEventListener("mouseenter",i),e.removeEventListener("mouseleave",l)})}},[a]);let A=(e,a)=>{console.log("handleInputChange called:",{field:e,value:a,currentFormData:k});let t={...k,[e]:a};console.log("Updated form data:",t),S(t),setTimeout(()=>{console.log("Form data after setState (delayed):",k)},100),O(t)},M=e=>{if(e){let a=Array.from(e);S(e=>({...e,documents:[...e.documents,...a]}))}},U=e=>{S(a=>({...a,documents:a.documents.filter((a,t)=>t!==e)}))},_=()=>{a<5&&t(a+1)},R=()=>{a>1&&t(a-1)},L=async()=>{try{let e=k.coverageCategory,a={planName:k.planName,planCode:k.planCode,carrier:k.carrier,coverageType:e,coverageSubTypes:[k.coverageType],planType:k.planType,metalTier:k.metalTier,description:k.description,highlights:k.highlights.filter(e=>""!==e.trim()),informativeLinks:k.videoUrl?[k.videoUrl]:[],carrierId:k.carrier,isTemplate:!1,status:"Active"};console.log("Creating plan with data:",a),console.log("Form data mapping:"),console.log("- Coverage Category (formData.coverageCategory):",k.coverageCategory,"→ coverageType"),console.log("- Coverage Type (formData.coverageType):",k.coverageType,"→ coverageSubTypes"),console.log("- Carrier ID:",k.carrier),console.log("- Plan Type:",k.planType),console.log("- Available carriers:",null==N?void 0:N.carriers);let i=await (0,x.he)(a);if(i.success&&i.data){let e=i.data.plan;if(k.documents.length>0){let a=await (0,x.Op)(e._id,k.documents);a.success||console.warn("Failed to upload some documents:",a.error)}j(e),localStorage.removeItem("ai-enroller-draft-plan"),t(5)}else throw Error(i.error||"Failed to create plan")}catch(e){alert("Error creating plan: ".concat(e instanceof Error?e.message:"Please try again."))}},H=()=>{S(e=>({...e,highlights:[...e.highlights,""]}))},W=(e,a)=>{S(t=>({...t,highlights:t.highlights.map((t,i)=>i===e?a:t)}))},B=e=>{k.highlights.length>1&&S(a=>({...a,highlights:a.highlights.filter((a,t)=>t!==e)}))};(0,s.useMemo)(()=>!0,[]);let G=(0,s.useMemo)(()=>k.planName&&k.planCode&&k.carrier&&k.planType&&k.coverageCategory&&k.coverageType&&!C.isDuplicate&&!C.isChecking&&!P.isDuplicate&&!P.isChecking,[k.planName,k.planCode,k.carrier,k.planType,k.coverageCategory,k.coverageType,C.isDuplicate,C.isChecking,P.isDuplicate,P.isChecking]),Z=(0,s.useMemo)(()=>k.description&&k.highlights.some(e=>""!==e.trim()),[k.description,k.highlights]),J=()=>(0,i.jsxs)("div",{className:"form-section",children:[(0,i.jsx)("div",{className:"form-header",children:(0,i.jsxs)("div",{className:"form-header-content",children:[(0,i.jsx)("div",{className:"gradient-icon",children:(0,i.jsx)(f.GJ3,{size:20})}),(0,i.jsx)("h3",{children:"Plan Documents"})]})}),(0,i.jsx)("div",{className:"form-content",children:(0,i.jsxs)("div",{className:"form-group",children:[(0,i.jsxs)("label",{children:["Plan Documents (Optional)",(0,i.jsx)(m.Z,{title:"Upload documents related to this plan such as brochures, benefit summaries, or plan details (PDF, DOC, TXT, or Image files)",placement:"top",arrow:!0,enterDelay:300,leaveDelay:200,slotProps:{tooltip:{sx:{bgcolor:"#374151",color:"white",fontSize:"0.75rem",maxWidth:"250px",zIndex:9999,"& .MuiTooltip-arrow":{color:"#374151"}}}},children:(0,i.jsx)("span",{children:(0,i.jsx)(u.gOU,{className:"form-tooltip-icon",size:16,title:"Upload documents related to this plan such as brochures, benefit summaries, or plan details (PDF, DOC, TXT, or Image files)"})})})]}),(0,i.jsxs)("div",{className:"file-upload-area",children:[(0,i.jsx)("input",{type:"file",id:"documents",multiple:!0,accept:".pdf,.doc,.docx,.txt,.jpg,.jpeg,.png",onChange:e=>M(e.target.files),className:"file-input"}),(0,i.jsxs)("label",{htmlFor:"documents",className:"file-upload-label",children:[(0,i.jsx)(v.qX3,{size:20}),(0,i.jsx)("span",{children:"Click to upload documents"}),(0,i.jsx)("small",{children:"PDF, DOC, TXT, or Image files"})]})]}),k.documents.length>0&&(0,i.jsx)("div",{className:"uploaded-files",children:k.documents.map((e,a)=>(0,i.jsxs)("div",{className:"uploaded-file",children:[(0,i.jsx)("div",{className:"gradient-icon-small",children:(0,i.jsx)(v.vrJ,{size:16})}),(0,i.jsx)("span",{className:"file-name",children:e.name}),(0,i.jsxs)("span",{className:"file-size",children:["(",(e.size/1024).toFixed(1)," KB)"]}),(0,i.jsx)("button",{type:"button",className:"remove-file",onClick:()=>U(a),title:"Remove this document",children:(0,i.jsx)(v.fMW,{size:14})})]},a))})]})}),(0,i.jsx)("div",{className:"form-navigation",style:{justifyContent:"flex-end"},children:(0,i.jsx)("button",{className:"nav-btn primary enabled",onClick:_,children:"Continue to Basic Info"})})]}),V=()=>{var e,a,t,l,r;return(0,i.jsxs)("div",{className:"form-section",children:[(0,i.jsx)("div",{className:"form-header",children:(0,i.jsxs)("div",{className:"form-header-content",children:[(0,i.jsx)("div",{className:"gradient-icon",children:(0,i.jsx)(f.kAf,{size:20})}),(0,i.jsx)("h3",{children:"Basic Details"})]})}),(0,i.jsxs)("div",{className:"form-content",children:[(0,i.jsxs)("div",{className:"form-group",children:[(0,i.jsxs)("label",{htmlFor:"coverageCategory",children:["Coverage Category",(0,i.jsx)(m.Z,{title:"Select the main category of coverage this plan provides (e.g., Medical, Dental, Vision)",placement:"top",arrow:!0,enterDelay:300,leaveDelay:200,slotProps:{tooltip:{sx:{bgcolor:"#374151",color:"white",fontSize:"0.75rem",maxWidth:"250px",zIndex:9999,"& .MuiTooltip-arrow":{color:"#374151"}}}},children:(0,i.jsx)("span",{children:(0,i.jsx)(u.gOU,{className:"form-tooltip-icon",size:16,title:"Select the main category of coverage this plan provides (e.g., Medical, Dental, Vision)"})})})]}),(0,i.jsxs)("select",{id:"coverageCategory",value:k.coverageCategory,onChange:e=>{console.log("Coverage category dropdown changed:",e.target.value),console.log("Current formData.coverageCategory before change:",k.coverageCategory);let a={...k,coverageCategory:e.target.value,coverageType:""};console.log("Updated form data (combined):",a),S(a),O(a)},title:"Choose the main category of benefits",children:[(0,i.jsx)("option",{value:"",children:"Select coverage category"}),((null==N?void 0:N.coverageCategories)||[]).map(e=>(console.log("Rendering category option:",e),(0,i.jsx)("option",{value:e,children:e},e)))]})]}),(0,i.jsxs)("div",{className:"form-group",children:[(0,i.jsxs)("label",{htmlFor:"coverageType",children:["Coverage Type",(0,i.jsx)(m.Z,{title:"Select the specific type of coverage within the category (e.g., PPO, HMO, EPO for Medical)",placement:"top",arrow:!0,enterDelay:300,leaveDelay:200,slotProps:{tooltip:{sx:{bgcolor:"#374151",color:"white",fontSize:"0.75rem",maxWidth:"250px",zIndex:9999,"& .MuiTooltip-arrow":{color:"#374151"}}}},children:(0,i.jsx)("span",{children:(0,i.jsx)(u.gOU,{className:"form-tooltip-icon",size:16,title:"Select the specific type of coverage within the category (e.g., PPO, HMO, EPO for Medical)"})})})]}),(0,i.jsxs)("select",{id:"coverageType",value:k.coverageType,onChange:e=>A("coverageType",e.target.value),disabled:!k.coverageCategory,title:"Choose the specific type of benefits covered",style:{backgroundColor:k.coverageCategory?"white":"#f9fafb",cursor:k.coverageCategory?"pointer":"not-allowed"},children:[(0,i.jsx)("option",{value:"",children:"Select coverage type"}),k.coverageCategory&&(null==N?void 0:null===(a=N.coverageMap)||void 0===a?void 0:null===(e=a[k.coverageCategory])||void 0===e?void 0:e.map(e=>(0,i.jsx)("option",{value:e,children:e},e)))||[]]})]}),(0,i.jsxs)("div",{className:"form-group",children:[(0,i.jsxs)("label",{htmlFor:"carrier",children:["Carrier",(0,i.jsx)(m.Z,{title:"Select the insurance carrier that provides this plan (e.g., Blue Shield, Kaiser)",placement:"top",arrow:!0,enterDelay:300,leaveDelay:200,slotProps:{tooltip:{sx:{bgcolor:"#374151",color:"white",fontSize:"0.75rem",maxWidth:"250px",zIndex:9999,"& .MuiTooltip-arrow":{color:"#374151"}}}},children:(0,i.jsx)("span",{children:(0,i.jsx)(u.gOU,{className:"form-tooltip-icon",size:16,title:"Select the insurance carrier that provides this plan (e.g., Blue Shield, Kaiser)"})})})]}),(0,i.jsxs)("select",{id:"carrier",value:k.carrier,onChange:e=>A("carrier",e.target.value),children:[(0,i.jsx)("option",{value:"",children:"Select carrier"}),(null==N?void 0:null===(t=N.carriers)||void 0===t?void 0:t.map(e=>(0,i.jsx)("option",{value:e._id,children:e.displayName||e.carrierName},e._id)))||[]]})]}),(0,i.jsxs)("div",{className:"form-group",children:[(0,i.jsxs)("label",{htmlFor:"planName",children:["Plan Name",(0,i.jsx)(m.Z,{title:"Enter a clear, descriptive name that helps identify this plan (e.g., Blue Shield PPO 500). We'll check for duplicates automatically.",placement:"top",arrow:!0,enterDelay:300,leaveDelay:200,slotProps:{tooltip:{sx:{bgcolor:"#374151",color:"white",fontSize:"0.75rem",maxWidth:"250px",zIndex:9999,"& .MuiTooltip-arrow":{color:"#374151"}}}},children:(0,i.jsx)("span",{children:(0,i.jsx)(u.gOU,{className:"form-tooltip-icon",size:16,title:"Enter a clear, descriptive name that helps identify this plan (e.g., Blue Shield PPO 500). We'll check for duplicates automatically."})})})]}),(0,i.jsxs)("div",{style:{position:"relative",width:"100%"},children:[(0,i.jsx)("input",{type:"text",id:"planName",placeholder:"e.g. Blue Shield PPO 500",value:k.planName,onChange:e=>A("planName",e.target.value),style:{width:"100%",borderColor:C.isDuplicate?"#ef4444":C.isChecking?"#f59e0b":k.planName&&!C.isDuplicate?"#10b981":"#d1d5db",paddingRight:"40px"}}),(0,i.jsxs)("div",{style:{position:"absolute",right:"12px",top:"50%",transform:"translateY(-50%)",display:"flex",alignItems:"center",fontSize:"14px",fontFamily:"sans-serif"},children:[C.isChecking&&(0,i.jsx)("div",{style:{width:"16px",height:"16px",border:"2px solid #f59e0b",borderTop:"2px solid transparent",borderRadius:"50%",animation:"spin 1s linear infinite"}}),!C.isChecking&&k.planName&&!C.isDuplicate&&(0,i.jsx)("span",{style:{color:"#10b981",fontWeight:"600",fontFamily:"sans-serif"},children:"✓"}),C.isDuplicate&&(0,i.jsx)("span",{style:{color:"#ef4444",fontWeight:"600",fontFamily:"sans-serif"},children:"✗"})]})]}),C.isDuplicate&&C.existingPlan&&(0,i.jsxs)("div",{style:{marginTop:"8px",padding:"8px 12px",backgroundColor:"#fef2f2",border:"1px solid #fecaca",borderRadius:"6px",fontSize:"13px",color:"#dc2626",lineHeight:"1.4",fontFamily:"sans-serif"},children:[(0,i.jsx)("strong",{children:"Plan name already exists:"}),' "',C.existingPlan.planName,'"',C.existingPlan.planCode&&" (".concat(C.existingPlan.planCode,")"),(0,i.jsx)("br",{}),(0,i.jsx)("span",{style:{fontSize:"12px",color:"#7f1d1d",fontFamily:"sans-serif"},children:"Please choose a different name."})]}),C.error&&(0,i.jsxs)("div",{style:{marginTop:"8px",padding:"8px 12px",backgroundColor:"#fef3cd",border:"1px solid #fde68a",borderRadius:"6px",fontSize:"13px",color:"#92400e",lineHeight:"1.4",fontFamily:"sans-serif"},children:[(0,i.jsx)("strong",{children:"Warning:"})," ",C.error]}),k.planName&&!C.isChecking&&!C.isDuplicate&&!C.error&&(0,i.jsx)("div",{style:{marginTop:"8px",padding:"8px 12px",backgroundColor:"#f0fdf4",border:"1px solid #bbf7d0",borderRadius:"6px",fontSize:"13px",color:"#166534",lineHeight:"1.4",fontFamily:"sans-serif"},children:"✓ Plan name is available"})]}),(0,i.jsxs)("div",{className:"form-group",children:[(0,i.jsxs)("label",{htmlFor:"planCode",children:["Plan Code",(0,i.jsx)(m.Z,{title:"Unique identifier for this plan used in systems and reports (e.g., BS-PPO-500). We'll check for duplicates automatically.",placement:"top",arrow:!0,enterDelay:300,leaveDelay:200,slotProps:{tooltip:{sx:{bgcolor:"#374151",color:"white",fontSize:"0.75rem",maxWidth:"250px",zIndex:9999,"& .MuiTooltip-arrow":{color:"#374151"}}}},children:(0,i.jsx)("span",{children:(0,i.jsx)(u.gOU,{className:"form-tooltip-icon",size:16,title:"Unique identifier for this plan used in systems and reports (e.g., BS-PPO-500). We'll check for duplicates automatically."})})})]}),(0,i.jsxs)("div",{style:{position:"relative",width:"100%"},children:[(0,i.jsx)("input",{type:"text",id:"planCode",placeholder:"e.g. BS-PPO-500",value:k.planCode,onChange:e=>A("planCode",e.target.value),style:{width:"100%",borderColor:P.isDuplicate?"#ef4444":P.isChecking?"#f59e0b":k.planCode&&!P.isDuplicate?"#10b981":"#d1d5db",paddingRight:"40px"}}),(0,i.jsxs)("div",{style:{position:"absolute",right:"12px",top:"50%",transform:"translateY(-50%)",display:"flex",alignItems:"center",fontSize:"14px",fontFamily:"sans-serif"},children:[P.isChecking&&(0,i.jsx)("div",{style:{width:"16px",height:"16px",border:"2px solid #f59e0b",borderTop:"2px solid transparent",borderRadius:"50%",animation:"spin 1s linear infinite"}}),!P.isChecking&&k.planCode&&!P.isDuplicate&&(0,i.jsx)("span",{style:{color:"#10b981",fontWeight:"600",fontFamily:"sans-serif"},children:"✓"}),P.isDuplicate&&(0,i.jsx)("span",{style:{color:"#ef4444",fontWeight:"600",fontFamily:"sans-serif"},children:"✗"})]})]}),P.isDuplicate&&P.existingPlan&&(0,i.jsxs)("div",{style:{marginTop:"8px",padding:"8px 12px",backgroundColor:"#fef2f2",border:"1px solid #fecaca",borderRadius:"6px",fontSize:"13px",color:"#dc2626",lineHeight:"1.4",fontFamily:"sans-serif"},children:[(0,i.jsx)("strong",{children:"Plan code already exists:"}),' "',P.existingPlan.planCode,'"',P.existingPlan.planName&&" (".concat(P.existingPlan.planName,")"),(0,i.jsx)("br",{}),(0,i.jsx)("span",{style:{fontSize:"12px",color:"#7f1d1d",fontFamily:"sans-serif"},children:"Please choose a different code."})]}),P.error&&(0,i.jsxs)("div",{style:{marginTop:"8px",padding:"8px 12px",backgroundColor:"#fef3cd",border:"1px solid #fde68a",borderRadius:"6px",fontSize:"13px",color:"#92400e",lineHeight:"1.4",fontFamily:"sans-serif"},children:[(0,i.jsx)("strong",{children:"Warning:"})," ",P.error]}),k.planCode&&!P.isChecking&&!P.isDuplicate&&!P.error&&(0,i.jsx)("div",{style:{marginTop:"8px",padding:"8px 12px",backgroundColor:"#f0fdf4",border:"1px solid #bbf7d0",borderRadius:"6px",fontSize:"13px",color:"#166534",lineHeight:"1.4",fontFamily:"sans-serif"},children:"✓ Plan code is available"})]}),(0,i.jsxs)("div",{className:"form-group",children:[(0,i.jsxs)("label",{htmlFor:"planType",children:["Plan Type",(0,i.jsx)(m.Z,{title:"Choose the type of health plan structure (PPO, HMO, EPO, POS, etc.)",placement:"top",arrow:!0,enterDelay:300,leaveDelay:200,slotProps:{tooltip:{sx:{bgcolor:"#374151",color:"white",fontSize:"0.75rem",maxWidth:"250px",zIndex:9999,"& .MuiTooltip-arrow":{color:"#374151"}}}},children:(0,i.jsx)("span",{children:(0,i.jsx)(u.gOU,{className:"form-tooltip-icon",size:16,title:"Choose the type of health plan structure (PPO, HMO, EPO, POS, etc.)"})})})]}),(0,i.jsxs)("select",{id:"planType",value:k.planType,onChange:e=>A("planType",e.target.value),title:"Select the plan structure (PPO, HMO, etc.)",children:[(0,i.jsx)("option",{value:"",children:"Select type"}),(null==N?void 0:null===(l=N.planTypes)||void 0===l?void 0:l.map(e=>(0,i.jsx)("option",{value:e,children:e},e)))||[]]})]}),(0,i.jsxs)("div",{className:"form-group",children:[(0,i.jsxs)("label",{htmlFor:"metalTier",children:["Metal Tier (Optional)",(0,i.jsx)(m.Z,{title:"Choose the coverage level (Bronze, Silver, Gold, Platinum). This field is optional and typically applies to medical plans.",placement:"top",arrow:!0,enterDelay:300,leaveDelay:200,slotProps:{tooltip:{sx:{bgcolor:"#374151",color:"white",fontSize:"0.75rem",maxWidth:"250px",zIndex:9999,"& .MuiTooltip-arrow":{color:"#374151"}}}},children:(0,i.jsx)("span",{children:(0,i.jsx)(u.gOU,{className:"form-tooltip-icon",size:16,title:"Choose the coverage level (Bronze, Silver, Gold, Platinum). This field is optional and typically applies to medical plans."})})})]}),(0,i.jsxs)("select",{id:"metalTier",value:k.metalTier,onChange:e=>A("metalTier",e.target.value),title:"Choose the coverage level (Bronze, Silver, Gold, Platinum) - Optional",children:[(0,i.jsx)("option",{value:"",children:"Select tier (optional)"}),(null==N?void 0:null===(r=N.metalTiers)||void 0===r?void 0:r.map(e=>(0,i.jsx)("option",{value:e,children:e},e)))||[]]})]})]}),(0,i.jsxs)("div",{className:"form-navigation",children:[(0,i.jsx)(o.default,{href:"/ai-enroller",prefetch:!0,children:(0,i.jsxs)("button",{className:"nav-btn secondary",children:[(0,i.jsx)(v.Tsu,{size:16}),"Back to Main"]})}),(0,i.jsx)("button",{className:"nav-btn primary ".concat(G?"enabled":"disabled"),onClick:_,disabled:!G,title:"Continue to description and video",children:"Continue to Description"})]})]})},Y=()=>(0,i.jsxs)("div",{className:"form-section",children:[(0,i.jsx)("div",{className:"form-header",children:(0,i.jsxs)("div",{className:"form-header-content",children:[(0,i.jsx)("div",{className:"gradient-icon",children:(0,i.jsx)(f.yMK,{size:20})}),(0,i.jsx)("h3",{children:"Description & Video"})]})}),(0,i.jsxs)("div",{className:"form-content",children:[(0,i.jsxs)("div",{className:"form-group",children:[(0,i.jsxs)("label",{htmlFor:"videoUrl",children:["Video URL (Optional)",(0,i.jsx)(m.Z,{title:"Add a YouTube or Vimeo URL to help explain plan benefits and features",placement:"top",arrow:!0,enterDelay:300,leaveDelay:200,slotProps:{tooltip:{sx:{bgcolor:"#374151",color:"white",fontSize:"0.75rem",maxWidth:"250px",zIndex:9999,"& .MuiTooltip-arrow":{color:"#374151"}}}},children:(0,i.jsx)("span",{children:(0,i.jsx)(u.gOU,{className:"form-tooltip-icon",size:16,title:"Add a YouTube or Vimeo URL to help explain plan benefits and features"})})})]}),(0,i.jsx)("input",{type:"url",id:"videoUrl",placeholder:"e.g. https://youtube.com/watch?v=...",value:k.videoUrl,onChange:e=>A("videoUrl",e.target.value)}),(0,i.jsx)("small",{className:"field-hint",children:"Add a video to help explain plan benefits and features"})]}),(0,i.jsxs)("div",{className:"form-group",children:[(0,i.jsxs)("label",{htmlFor:"description",children:["Plan Description",(0,i.jsx)(m.Z,{title:"Provide a detailed description of the plan benefits, coverage, and key features",placement:"top",arrow:!0,enterDelay:300,leaveDelay:200,slotProps:{tooltip:{sx:{bgcolor:"#374151",color:"white",fontSize:"0.75rem",maxWidth:"250px",zIndex:9999,"& .MuiTooltip-arrow":{color:"#374151"}}}},children:(0,i.jsx)("span",{children:(0,i.jsx)(u.gOU,{className:"form-tooltip-icon",size:16,title:"Provide a detailed description of the plan benefits, coverage, and key features"})})})]}),(0,i.jsx)("textarea",{id:"description",placeholder:"Describe the plan benefits and features...",value:k.description,onChange:e=>A("description",e.target.value),rows:4}),(0,i.jsx)("small",{className:"field-hint",children:"Describe the key benefits, coverage details, and what makes this plan unique"})]}),(0,i.jsxs)("div",{className:"form-group",children:[(0,i.jsxs)("label",{children:["Plan Highlights",(0,i.jsx)(m.Z,{title:"Add key selling points and highlights that make this plan attractive (e.g., Low deductible, Nationwide network, No referrals needed)",placement:"top",arrow:!0,enterDelay:300,leaveDelay:200,slotProps:{tooltip:{sx:{bgcolor:"#374151",color:"white",fontSize:"0.75rem",maxWidth:"250px",zIndex:9999,"& .MuiTooltip-arrow":{color:"#374151"}}}},children:(0,i.jsx)("span",{children:(0,i.jsx)(u.gOU,{className:"form-tooltip-icon",size:16,title:"Add key selling points and highlights that make this plan attractive (e.g., Low deductible, Nationwide network, No referrals needed)"})})})]}),(0,i.jsx)("small",{className:"field-hint",children:"Add the most important features that make this plan attractive"}),k.highlights.map((e,a)=>(0,i.jsxs)("div",{className:"highlight-input",children:[(0,i.jsx)("input",{type:"text",placeholder:"e.g. Low deductible, Nationwide network",value:e,onChange:e=>W(a,e.target.value),title:"Enter a key benefit or feature"}),k.highlights.length>1&&(0,i.jsx)("button",{type:"button",className:"remove-highlight",onClick:()=>B(a),title:"Remove this highlight",children:(0,i.jsx)(v.fMW,{size:14})})]},a)),(0,i.jsxs)("button",{type:"button",className:"add-highlight",onClick:H,title:"Add another highlight",children:[(0,i.jsx)(v.r7I,{size:16}),"Add Highlight"]})]})]}),(0,i.jsxs)("div",{className:"form-navigation",children:[(0,i.jsxs)("button",{className:"nav-btn secondary",onClick:R,title:"Go back to basic information",children:[(0,i.jsx)(v.Tsu,{size:16}),"Back"]}),(0,i.jsx)("button",{className:"nav-btn primary ".concat(Z?"enabled":"disabled"),onClick:_,disabled:!Z,title:"Continue to preview your plan",children:"Preview Plan"})]})]}),q=()=>{var e,a,t,l;return(0,i.jsxs)("div",{className:"form-section",children:[(0,i.jsxs)("div",{className:"form-header",children:[(0,i.jsxs)("div",{className:"form-header-content",children:[(0,i.jsx)("div",{className:"gradient-icon",children:(0,i.jsx)(f.GH$,{size:20})}),(0,i.jsx)("h3",{children:"AI-Powered Plan Preview"})]}),(0,i.jsxs)("div",{className:"ready-badge",title:"All required information has been provided",children:[(0,i.jsx)("div",{children:(0,i.jsx)(v.PjL,{size:16})}),"Ready to Create"]})]}),(0,i.jsxs)("div",{className:"review-content",children:[(0,i.jsxs)("div",{className:"review-section",children:[(0,i.jsxs)("div",{className:"review-section-header",children:[(0,i.jsx)("div",{className:"gradient-icon-small",children:(0,i.jsx)(f.kAf,{size:18})}),(0,i.jsx)("h4",{children:"Plan Information"})]}),(0,i.jsxs)("div",{className:"review-items",children:[(0,i.jsxs)("div",{className:"review-item",children:[(0,i.jsx)("span",{className:"review-label",title:"The name of this plan",children:"Plan Name:"}),(0,i.jsx)("span",{className:"review-value",children:k.planName})]}),(0,i.jsxs)("div",{className:"review-item",children:[(0,i.jsx)("span",{className:"review-label",title:"Unique identifier for this plan",children:"Plan Code:"}),(0,i.jsx)("span",{className:"review-value plan-code",children:k.planCode})]}),(0,i.jsxs)("div",{className:"review-item",children:[(0,i.jsx)("span",{className:"review-label",title:"Insurance carrier providing this plan",children:"Carrier:"}),(0,i.jsx)("span",{className:"review-value",children:(null==N?void 0:null===(a=N.carriers)||void 0===a?void 0:null===(e=a.find(e=>e._id===k.carrier))||void 0===e?void 0:e.displayName)||(null==N?void 0:null===(l=N.carriers)||void 0===l?void 0:null===(t=l.find(e=>e._id===k.carrier))||void 0===t?void 0:t.carrierName)||"Unknown"})]}),(0,i.jsxs)("div",{className:"review-item",children:[(0,i.jsx)("span",{className:"review-label",title:"Type of health plan structure",children:"Plan Type:"}),(0,i.jsx)("span",{className:"review-value",children:k.planType})]}),(0,i.jsxs)("div",{className:"review-item",children:[(0,i.jsx)("span",{className:"review-label",title:"Type of coverage provided",children:"Coverage Type:"}),(0,i.jsx)("span",{className:"review-value",children:k.coverageType})]}),(0,i.jsxs)("div",{className:"review-item",children:[(0,i.jsx)("span",{className:"review-label",title:"Metal tier level indicating coverage level",children:"Metal Tier:"}),(0,i.jsx)("span",{className:"review-value metal-tier",children:k.metalTier})]})]})]}),(k.videoUrl||k.documents.length>0)&&(0,i.jsxs)("div",{className:"review-section",children:[(0,i.jsxs)("div",{className:"review-section-header",children:[(0,i.jsx)("div",{className:"gradient-icon-small",children:(0,i.jsx)(v.vrJ,{size:18})}),(0,i.jsx)("h4",{children:"Media & Documents"})]}),(0,i.jsxs)("div",{className:"review-items",children:[k.videoUrl&&(0,i.jsxs)("div",{className:"review-item full-width",children:[(0,i.jsx)("span",{className:"review-label",title:"Video URL for plan explanation",children:"Video URL:"}),(0,i.jsx)("a",{href:k.videoUrl,target:"_blank",rel:"noopener noreferrer",className:"review-link",children:k.videoUrl})]}),k.documents.length>0&&(0,i.jsxs)("div",{className:"review-item full-width",children:[(0,i.jsx)("span",{className:"review-label",title:"Documents uploaded for this plan",children:"Documents:"}),(0,i.jsx)("div",{className:"review-documents",children:k.documents.map((e,a)=>(0,i.jsxs)("div",{className:"review-document",children:[(0,i.jsx)("div",{className:"gradient-icon-small",children:(0,i.jsx)(v.vrJ,{size:16})}),(0,i.jsx)("span",{children:e.name}),(0,i.jsxs)("small",{children:["(",(e.size/1024).toFixed(1)," KB)"]})]},a))})]})]})]}),(0,i.jsxs)("div",{className:"review-section",children:[(0,i.jsxs)("div",{className:"review-section-header",children:[(0,i.jsx)("div",{className:"gradient-icon-small",children:(0,i.jsx)(f.jNQ,{size:18})}),(0,i.jsx)("h4",{children:"Description & Highlights"})]}),(0,i.jsxs)("div",{className:"review-items",children:[(0,i.jsxs)("div",{className:"review-item full-width",children:[(0,i.jsx)("span",{className:"review-label",title:"Detailed plan description",children:"Description:"}),(0,i.jsx)("p",{className:"review-description",children:k.description})]}),(0,i.jsxs)("div",{className:"review-item full-width",children:[(0,i.jsx)("span",{className:"review-label",title:"Key plan features and benefits",children:"Highlights:"}),(0,i.jsx)("ul",{className:"review-highlights",children:k.highlights.filter(e=>e.trim()).map((e,a)=>(0,i.jsx)("li",{children:e},a))})]})]})]}),(0,i.jsxs)("div",{className:"create-confirmation",children:[(0,i.jsx)("div",{className:"confirmation-icon",children:(0,i.jsx)("div",{className:"gradient-icon",children:(0,i.jsx)(f.GH$,{size:24})})}),(0,i.jsxs)("div",{className:"confirmation-text",children:[(0,i.jsx)("h4",{children:"Ready to Create Plan"}),(0,i.jsxs)("p",{children:['Your new plan "',k.planName,'" will be added to your catalog and available for assignment to employer groups.']})]})]})]}),(0,i.jsxs)("div",{className:"form-navigation",children:[(0,i.jsxs)("button",{className:"nav-btn secondary",onClick:R,title:"Go back to description and video",children:[(0,i.jsx)(v.Tsu,{size:16}),"Back"]}),(0,i.jsx)("button",{className:"nav-btn primary enabled",onClick:L,title:"Create this plan and add it to your catalog",children:"Create Plan"})]})]})},K=()=>{var e,a;return(0,i.jsx)("div",{className:"form-section success-section",children:(0,i.jsxs)("div",{className:"success-content",children:[(0,i.jsx)("div",{className:"success-icon",children:(0,i.jsx)("div",{className:"gradient-icon-large",children:(0,i.jsx)(f.euf,{size:32})})}),(0,i.jsx)("h3",{children:"Plan Created Successfully!"}),(0,i.jsxs)("p",{children:["Your plan '",k.planName,"' is now available in your catalog."]}),l&&(0,i.jsxs)("div",{className:"plan-details-card",children:[(0,i.jsxs)("div",{className:"plan-details-header",children:[(0,i.jsx)("div",{className:"gradient-icon",children:(0,i.jsx)(f.kAf,{size:15})}),(0,i.jsx)("h4",{children:"Plan Details"})]}),(0,i.jsxs)("div",{className:"plan-details-content",children:[(0,i.jsxs)("div",{className:"plan-detail-item",children:[(0,i.jsx)("span",{className:"detail-label",children:"Plan ID:"}),(0,i.jsx)("span",{className:"detail-value plan-id",children:l._id})]}),(0,i.jsxs)("div",{className:"plan-detail-item",children:[(0,i.jsx)("span",{className:"detail-label",children:"Plan Code:"}),(0,i.jsx)("span",{className:"detail-value plan-code",children:l.planCode})]}),(0,i.jsxs)("div",{className:"plan-detail-item",children:[(0,i.jsx)("span",{className:"detail-label",children:"Status:"}),(0,i.jsx)("span",{className:"detail-value status-active",children:l.status||"Active"})]}),(0,i.jsxs)("div",{className:"plan-detail-item",children:[(0,i.jsx)("span",{className:"detail-label",children:"Created:"}),(0,i.jsx)("span",{className:"detail-value",children:l.createdAt?new Date(l.createdAt).toLocaleString():"Just now"})]}),(0,i.jsxs)("div",{className:"plan-detail-item",children:[(0,i.jsx)("span",{className:"detail-label",children:"Carrier:"}),(0,i.jsx)("span",{className:"detail-value",children:(null==N?void 0:null===(e=N.carriers.find(e=>e._id===(l.carrierId||l.carrier)))||void 0===e?void 0:e.displayName)||(null==N?void 0:null===(a=N.carriers.find(e=>e._id===(l.carrierId||l.carrier)))||void 0===a?void 0:a.carrierName)||"Unknown"})]})]})]}),(0,i.jsxs)("div",{className:"success-actions",children:[(0,i.jsx)(o.default,{href:"/ai-enroller/manage-groups",prefetch:!0,children:(0,i.jsx)("button",{className:"nav-btn primary",children:"Assign to Group Now"})}),(0,i.jsx)(o.default,{href:"/ai-enroller",prefetch:!0,children:(0,i.jsx)("button",{className:"nav-btn secondary",children:"Back to Main Menu"})})]})]})})};if(y||!N)return(0,i.jsx)("div",{className:"create-plan-wrapper",children:(0,i.jsx)("div",{className:"create-plan-page",children:(0,i.jsx)(()=>(0,i.jsxs)("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",minHeight:"400px",flexDirection:"column",gap:"16px"},className:"jsx-ff161281ed666c63",children:[(0,i.jsx)("div",{style:{width:"40px",height:"40px",border:"3px solid #f3f4f6",borderTop:"3px solid #3b82f6",borderRadius:"50%",animation:"spin 1s linear infinite"},className:"jsx-ff161281ed666c63"}),(0,i.jsx)("p",{style:{color:"#6b7280",fontSize:"14px",lineHeight:"1.6",fontFamily:"sans-serif"},className:"jsx-ff161281ed666c63",children:"Loading plan data..."}),(0,i.jsx)(r(),{id:"ff161281ed666c63",children:"@-webkit-keyframes spin{0%{-webkit-transform:rotate(0deg);transform:rotate(0deg)}100%{-webkit-transform:rotate(360deg);transform:rotate(360deg)}}@-moz-keyframes spin{0%{-moz-transform:rotate(0deg);transform:rotate(0deg)}100%{-moz-transform:rotate(360deg);transform:rotate(360deg)}}@-o-keyframes spin{0%{-o-transform:rotate(0deg);transform:rotate(0deg)}100%{-o-transform:rotate(360deg);transform:rotate(360deg)}}@keyframes spin{0%{-webkit-transform:rotate(0deg);-moz-transform:rotate(0deg);-o-transform:rotate(0deg);transform:rotate(0deg)}100%{-webkit-transform:rotate(360deg);-moz-transform:rotate(360deg);-o-transform:rotate(360deg);transform:rotate(360deg)}}"})]}),{})})});let $=()=>{switch(a){case 1:return{title:"Hi! I'm Brea, your AI Benefits Assistant. Let's create an amazing plan together! \uD83D\uDE0A",subtitle:"I'll help you set up the basic plan information including name, carrier, and coverage details. This should only take a few minutes!"};case 2:return{title:"Great progress! Now let's add some media to make your plan shine ✨",subtitle:"You can upload videos, brochures, or any documents that help explain your plan. Don't worry, this step is completely optional!"};case 3:return{title:"Perfect! Now tell me what makes this plan special \uD83C\uDF1F",subtitle:"Help me understand the key benefits and highlights. I'll use this to create compelling descriptions that really sell your plan!"};case 4:return{title:"Almost there! Let's review everything before we launch your plan \uD83D\uDE80",subtitle:"Take a moment to review all the details. Once you're happy with everything, I'll create your plan and make it available immediately!"};case 5:return{title:"Congratulations! Your plan is now live and ready to go! \uD83C\uDF89",subtitle:"I've successfully created your plan and it's now available for assignment to employer groups. Great work!"};default:return{title:"Hi there! Ready to create something amazing? \uD83D\uDCAB",subtitle:"I'm here to help you build the perfect benefits plan. Let's get started!"}}},X=e=>{switch(e){case 1:return(0,i.jsx)("div",{className:"gradient-icon-nav",children:(0,i.jsx)(v.vrJ,{})});case 2:default:return(0,i.jsx)("div",{className:"gradient-icon-nav",children:(0,i.jsx)(f.kAf,{})});case 3:return(0,i.jsx)("div",{className:"gradient-icon-nav",children:(0,i.jsx)(f.jNQ,{})});case 4:return(0,i.jsx)("div",{className:"gradient-icon-nav",children:(0,i.jsx)(f.GH$,{})});case 5:return(0,i.jsx)("div",{className:"gradient-icon-nav",children:(0,i.jsx)(f.euf,{})})}};return(0,i.jsx)(d.Z,{children:(0,i.jsxs)("div",{className:"create-plan-wrapper",children:[(0,i.jsx)(g.Z,{}),(0,i.jsx)(h,{}),(0,i.jsxs)("div",{className:"create-plan-page",children:[(0,i.jsx)("div",{className:"progress-header-component",children:(0,i.jsxs)("div",{className:"progress-header",children:[(0,i.jsxs)("div",{className:"progress-title",children:[(0,i.jsx)("h1",{className:"page-title",children:"Plan Creation Progress"}),(0,i.jsxs)("span",{className:"subtitle-text",children:[a," of 5"]})]}),(0,i.jsx)("div",{className:"progress-bar-container",children:(0,i.jsx)("div",{className:"progress-bar-fill",style:{width:"".concat(a/5*100,"%")}})}),(0,i.jsx)("div",{className:"page-navigation",children:I.map(e=>(0,i.jsxs)("button",{className:"page-nav-item ".concat(e.active?"active":""," ").concat(e.completed?"completed":""),onClick:()=>a<5?t(e.number):null,disabled:e.number>a||5===a,children:[(0,i.jsx)("span",{className:"nav-icon",children:X(e.number)}),e.title]},e.number))})]})}),(0,i.jsx)("div",{className:"ai-assistant-message",children:(0,i.jsxs)("div",{className:"ai-message-content",children:[(0,i.jsx)("div",{className:"ai-avatar",children:(0,i.jsx)("div",{className:"avatar-circle",children:(0,i.jsx)(c.default,{src:"/brea.png",alt:"Brea - AI Assistant",className:"brea-avatar",width:48,height:48,priority:!0})})}),(0,i.jsxs)("div",{className:"chat-bubble",children:[(0,i.jsx)("div",{className:"chat-message",children:$().title}),(0,i.jsx)("div",{className:"chat-subtitle",children:$().subtitle})]})]})}),(0,i.jsx)("div",{className:"main-content",children:(0,i.jsx)("div",{className:"form-container",children:(()=>{switch(a){case 1:default:return J();case 2:return V();case 3:return Y();case 4:return q();case 5:return K()}})()})})]})]})})}},63055:function(e,a,t){"use strict";t.d(a,{HP:function(){return n},Op:function(){return h},Vs:function(){return p},he:function(){return c},ie:function(){return o},nR:function(){return m},qY:function(){return d},yW:function(){return u}});var i=t(61103),l=t(40256);let r=(0,i.bR)(),s=()=>({"Content-Type":"application/json","user-id":(0,i.n5)()}),n=()=>{let e={"Health Insurance":["Medical"],"Ancillary Benefits":["Dental","Vision"],"Life & Disability Insurance":["Term Life","Supplemental Life Insurance","Short-Term Disability","Long-Term Disability","Whole Life","Group (Employer) Life","Accidental Death & Dismemberment (AD&D)"],"Voluntary Benefits":["Hospital Indemnity","Accident Insurance","Critical Illness Insurance","Cancer Insurance","Gap Insurance","Legal Insurance","Identity Theft Protection","Accident & Illness (Pets)","Nursing Care / Custodial Care"],"Wellness & Mental Health":["Wellness Programs","Employee Assistance Program","Gym Membership"]};return{success:!0,data:{planTypes:["PPO","HMO","HDHP","MEC","EPO","POS","Indemnity","Term Life","Whole Life","STD","LTD"],coverageCategories:Object.keys(e),coverageMap:e,metalTiers:["Bronze","Silver","Gold","Platinum","Catastrophic"]}}},o=async()=>{try{let e=await fetch("".concat(r,"/api/pre-enrollment/carriers/assignable"),{method:"GET",headers:s()});if(!e.ok){let a=await e.text();try{let e=await fetch("".concat(r,"/api/pre-enrollment/carriers?isSystemCarrier=true"),{method:"GET",headers:s()});if(e.ok){let a=await e.json();return{success:!0,data:a.carriers||[]}}}catch(e){}throw Error("HTTP error! status: ".concat(e.status,", message: ").concat(a))}let a=await e.json();return{success:!0,data:a.carriers||[]}}catch(e){return{success:!1,error:e instanceof Error?e.message:"Failed to fetch carriers"}}},c=async e=>{try{let a=await l.be.post("/api/pre-enrollment/plans",e);return{success:!0,data:a.data}}catch(e){var a,t,i,r;return{success:!1,error:(null===(t=e.response)||void 0===t?void 0:null===(a=t.data)||void 0===a?void 0:a.error)||(null===(r=e.response)||void 0===r?void 0:null===(i=r.data)||void 0===i?void 0:i.message)||e.message||"Failed to create plan"}}},d=async e=>{try{var a;let t=new URLSearchParams;e&&(console.log("\uD83D\uDD27 getPlans called with filters:",e),Object.entries(e).forEach(e=>{let[a,i]=e;void 0!==i&&t.append(a,i.toString())}));let i="/api/pre-enrollment/plans".concat(t.toString()?"?".concat(t.toString()):"");console.log("\uD83C\uDF10 Fetching plans from endpoint:",i);let r=(await l.be.get(i)).data;console.log("\uD83D\uDCE6 getPlans response:",r),console.log("\uD83D\uDCCA Plans returned:",(null===(a=r.plans)||void 0===a?void 0:a.length)||0);let s=(r.plans||[]).map((e,a)=>{var t;let i;if(console.log("\uD83D\uDD0D Plan ".concat(a," raw data structure:"),{hasDoc:!!e._doc,hasId:!!e._id,docId:null===(t=e._doc)||void 0===t?void 0:t._id,directId:e._id}),e._doc)i={...e._doc,_id:e._doc._id||e._id,...e.carrierData&&{carrierData:e.carrierData}},console.log("\uD83D\uDCC4 Plan ".concat(a," extracted from _doc:"),i);else try{i=JSON.parse(JSON.stringify(e))}catch(t){console.warn("⚠️ JSON transform failed for plan ".concat(a,":"),t),i={...e}}return i._id?"object"==typeof i._id&&i._id.toString?i._id=i._id.toString():"string"!=typeof i._id&&(i._id=String(i._id)):console.warn("⚠️ Plan ".concat(a," still missing _id after transformation")),console.log("✅ Plan ".concat(a," final _id:"),i._id),i});return console.log("\uD83D\uDD04 Transformed plans sample:",s[0]),{success:!0,data:{plans:s,count:r.count,pagination:r.pagination}}}catch(e){return{success:!1,error:"Failed to fetch plans"}}},p=async(e,a)=>{try{let t=await l.be.put("/api/pre-enrollment/plans/".concat(e),a);return{success:!0,data:t.data}}catch(e){var t,i,r,s;return{success:!1,error:(null===(i=e.response)||void 0===i?void 0:null===(t=i.data)||void 0===t?void 0:t.error)||(null===(s=e.response)||void 0===s?void 0:null===(r=s.data)||void 0===r?void 0:r.message)||e.message||"Failed to update plan"}}},h=async(e,a)=>{try{let t=new FormData;a.forEach(e=>{t.append("documents",e)});let l=await fetch("".concat(r,"/api/pre-enrollment/plans/").concat(e,"/documents"),{method:"POST",headers:{"user-id":(0,i.n5)()},body:t});if(!l.ok){let e=await l.json();throw Error(e.error||"HTTP error! status: ".concat(l.status))}let s=await l.json();return{success:!0,data:s}}catch(e){return{success:!1,error:e instanceof Error?e.message:"Failed to upload documents"}}},m=async(e,a)=>{try{var t,i,l;console.log("\uD83D\uDD0D Checking for duplicate plan name:",e,a?"(excluding ".concat(a,")"):"");let r=await d({planName:e,strict:!0});if(console.log("\uD83D\uDCCA Plan name duplicate check result:",r),console.log("\uD83D\uDCCA Number of plans returned:",(null===(i=r.data)||void 0===i?void 0:null===(t=i.plans)||void 0===t?void 0:t.length)||0),!r.success)return console.log("❌ Plan name check failed:",r.error),{success:!1,error:r.error||"Failed to fetch plans for duplicate check"};let s=(null===(l=r.data)||void 0===l?void 0:l.plans)||[],n=a?s.filter(e=>e._id!==a):s,o=n.length>0?n[0]:void 0;return console.log("\uD83C\uDFAF Duplicate plan found:",o?"YES - ".concat(o.planName):"NO",a?"(excluded ".concat(a,")"):""),{success:!0,data:{isDuplicate:!!o,existingPlan:o}}}catch(e){return console.log("\uD83D\uDCA5 Plan name check error:",e),{success:!1,error:"Failed to check for duplicate plan name"}}},u=async(e,a)=>{try{var t,i,l;console.log("\uD83D\uDD0D Checking for duplicate plan code:",e,a?"(excluding ".concat(a,")"):"");let r=await d({planCode:e,strict:!0});if(console.log("\uD83D\uDCCA Plan code duplicate check result:",r),console.log("\uD83D\uDCCA Number of plans returned:",(null===(i=r.data)||void 0===i?void 0:null===(t=i.plans)||void 0===t?void 0:t.length)||0),!r.success)return console.log("❌ Plan code check failed:",r.error),{success:!1,error:r.error||"Failed to fetch plans for duplicate check"};let s=(null===(l=r.data)||void 0===l?void 0:l.plans)||[],n=a?s.filter(e=>e._id!==a):s,o=n.length>0?n[0]:void 0;return console.log("\uD83C\uDFAF Duplicate plan found:",o?"YES - ".concat(o.planCode):"NO",a?"(excluded ".concat(a,")"):""),{success:!0,data:{isDuplicate:!!o,existingPlan:o}}}catch(e){return console.log("\uD83D\uDCA5 Plan code check error:",e),{success:!1,error:"Failed to check for duplicate plan code"}}}},24079:function(e,a,t){"use strict";t.d(a,{$:function(){return l}});var i=t(2265);let l=e=>{(0,i.useEffect)(()=>{let a=performance.now(),t=()=>{let t=performance.now()-a;{let a=JSON.parse(localStorage.getItem("ai-enroller-perf")||"{}");a[e]={loadTime:t.toFixed(2),timestamp:new Date().toISOString()},localStorage.setItem("ai-enroller-perf",JSON.stringify(a))}};return"complete"===document.readyState?t():window.addEventListener("load",t),()=>{window.removeEventListener("load",t)}},[e])}},48223:function(e,a,t){"use strict";var i=t(57437),l=t(2265),r=t(47369),s=t(99376),n=t(83337),o=t(70623),c=t(39547),d=t(35389),p=t(95656);let h=()=>/Mobi|Android/i.test(navigator.userAgent);a.Z=e=>{let{children:a}=e,{user:t,loading:m}=(0,r.a)(),u=(0,s.useRouter)(),g=(0,s.usePathname)(),v=(0,n.T)(),[f,x]=(0,l.useState)(!1),j=(0,n.C)(e=>e.user.userProfile);return((0,l.useEffect)(()=>{{let e=localStorage.getItem("userid1")||localStorage.getItem("userId");console.log("USER ID FROM LOCAL STORAGE: ",e),e&&!j.name&&(v((0,o.Iv)(e)),(async()=>{try{await (0,c.M_)(v,e),await (0,c.aK)(v)}catch(e){console.error("Error fetching user data in ProtectedRoute:",e)}})())}},[v,j.name]),(0,l.useEffect)(()=>{console.log("ProtectedRoute useEffect triggered"),console.log("Current user: ",t),console.log("Loading state: ",m),console.log("Current user details: ",j),m||t||(console.log("User not authenticated, redirecting to home"),x(!1),u.push("/")),!m&&j.companyId&&""===j.companyId&&(console.log("Waiting to retrieve company details"),x(!1)),!m&&j.companyId&&""!==j.companyId&&(console.log("User found, rendering children"),x(!0)),h()&&!g.startsWith("/mobile")&&(console.log("Redirecting to mobile version of ".concat(g)),u.push("/mobile".concat(g)))},[t,m,j,u,g]),f)?t?(0,i.jsx)(i.Fragment,{children:a}):null:(0,i.jsx)(p.Z,{sx:{display:"flex",justifyContent:"center",alignItems:"center",height:"100vh",bgcolor:"#f6f8fc"},children:(0,i.jsx)(d.Z,{})})}},61103:function(e,a,t){"use strict";t.d(a,{GU:function(){return r},bR:function(){return i},n5:function(){return l}});let i=()=>"http://localhost:8080",l=()=>{let e="userid1",a="userId",t=localStorage.getItem(e)||localStorage.getItem(a);return(console.log("\uD83D\uDD0D getUserId debug:",{primaryKey:e,altKey:a,primaryValue:localStorage.getItem(e),altValue:localStorage.getItem(a),finalUserId:t}),t)?t:(console.error("❌ User ID not found in localStorage"),"default-user")},r=()=>"https://bot.benosphere.com"},43010:function(){}},function(e){e.O(0,[1424,139,8422,522,5706,3463,3301,8575,8685,187,1423,9932,3919,9129,2786,208,7648,4902,9218,3344,9859,2971,2117,1744],function(){return e(e.s=80053)}),_N_E=e.O()}]);