(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6029,8884,5244],{22839:function(e,n,t){Promise.resolve().then(t.bind(t,26167))},45244:function(e,n,t){"use strict";var o=t(57437),l=t(2265),r=t(33145),i=t(68575),a=t(76792),s=t(27345),d=t(70623);n.Z=e=>{let{isOpen:n,onClose:t}=e,c=(0,i.I0)(),p=(0,l.useRef)(null),[u,g]=(0,l.useState)(""),x=(0,i.v9)(e=>(0,d.MP)(e)),f=(0,i.v9)(e=>e.user._id),h=(0,i.v9)(e=>e.user.userProfile),m=(0,i.v9)(e=>e.qHarmonyBot.chatHistory),y=(0,i.v9)(e=>e.qHarmonyBot.isLoading),b=e=>{if(""===e.trim())return;let n={sender:"user",message:e.replace(/\n/g,"<br/>"),timestamp:new Date().toISOString()};c((0,a.Hz)(n)),c((0,a.wt)(!0)),(0,s.b)(c,e,f,x),g("")},v=e=>{if(!e)return"";let[n,t]=e.split(" ");return"".concat(n[0].toUpperCase()).concat(t?t[0].toUpperCase():"")},D=()=>{var e;null===(e=p.current)||void 0===e||e.scrollIntoView({behavior:"smooth"})};(0,l.useEffect)(()=>{n&&0===m.length&&h.name&&(c((0,a.wt)(!0)),setTimeout(()=>{let e={sender:"bot",message:"Hey ".concat(h.name,", how can I help you with your benefits enrollment today?"),timestamp:new Date().toISOString()};c((0,a.Hz)(e)),c((0,a.wt)(!1))},1e3))},[n,m.length,h.name,c]),(0,l.useEffect)(()=>{D()},[m]);let j=["Explain my plan options","Help me choose coverage","What are the costs?","Enrollment deadline"];return n?(0,o.jsx)("div",{style:{position:"fixed",top:0,left:0,right:0,bottom:0,backgroundColor:"rgba(0, 0, 0, 0.5)",zIndex:1e4,display:"flex",alignItems:"center",justifyContent:"center",padding:"20px"},children:(0,o.jsxs)("div",{style:{backgroundColor:"#f6f8fc",borderRadius:"12px",width:"100%",maxWidth:"800px",height:"600px",display:"flex",flexDirection:"column",boxShadow:"0 20px 60px rgba(0, 0, 0, 0.3)",overflow:"hidden"},children:[(0,o.jsxs)("div",{style:{backgroundColor:"#ffffff",padding:"16px 24px",borderBottom:"1px solid #e5e7eb",display:"flex",alignItems:"center",justifyContent:"space-between"},children:[(0,o.jsxs)("div",{style:{display:"flex",alignItems:"center",gap:"12px"},children:[(0,o.jsx)(r.default,{src:"/brea.png",alt:"Brea",width:40,height:40,style:{borderRadius:"50%"}}),(0,o.jsxs)("div",{children:[(0,o.jsx)("h3",{style:{margin:0,fontSize:"18px",fontWeight:"600",color:"#111827"},children:"Chat with Brea"}),(0,o.jsx)("p",{style:{margin:0,fontSize:"14px",color:"#6b7280"},children:"Your Benefits Specialist"})]})]}),(0,o.jsx)("button",{onClick:t,style:{background:"none",border:"none",fontSize:"24px",cursor:"pointer",color:"#6b7280",padding:"4px",borderRadius:"4px",display:"flex",alignItems:"center",justifyContent:"center"},children:"\xd7"})]}),(0,o.jsxs)("div",{style:{flex:1,overflow:"auto",padding:"16px",display:"flex",flexDirection:"column",gap:"12px"},children:[m.map((e,n)=>(0,o.jsxs)("div",{style:{display:"flex",flexDirection:"user"===e.sender?"row-reverse":"row",alignItems:"flex-start",gap:"8px"},children:[(0,o.jsx)("div",{style:{width:"32px",height:"32px",borderRadius:"50%",display:"flex",alignItems:"center",justifyContent:"center",backgroundColor:"user"===e.sender?"#000000":"transparent",color:"white",fontSize:"14px",fontWeight:"600",flexShrink:0},children:"user"===e.sender?v(h.name):(0,o.jsx)(r.default,{src:"/brea.png",alt:"Brea",width:32,height:32,style:{borderRadius:"50%"}})}),(0,o.jsxs)("div",{style:{maxWidth:"70%",backgroundColor:"user"===e.sender?"#000000":"#ffffff",color:"user"===e.sender?"#ffffff":"#000000",padding:"12px 16px",borderRadius:"user"===e.sender?"16px 16px 4px 16px":"16px 16px 16px 4px",fontSize:"14px",lineHeight:"1.5",boxShadow:"0 1px 3px rgba(0, 0, 0, 0.1)"},children:[(0,o.jsx)("div",{dangerouslySetInnerHTML:{__html:"bot"===e.sender?"".concat(e.message,'<br/><small style="color: #6b7280; font-size: 12px;">AI-generated content—verify before use.</small>'):e.message},style:{whiteSpace:"pre-wrap",wordBreak:"break-word"}}),"bot"===e.sender&&e.message.includes("how can I help you")&&n===m.length-1&&(0,o.jsx)("div",{style:{display:"flex",flexWrap:"wrap",gap:"8px",marginTop:"12px"},children:j.map(e=>(0,o.jsx)("button",{onClick:()=>b(e),style:{padding:"6px 12px",backgroundColor:"#f3f4f6",color:"#374151",border:"1px solid #d1d5db",borderRadius:"16px",fontSize:"12px",cursor:"pointer",transition:"all 0.2s"},onMouseOver:e=>{e.currentTarget.style.backgroundColor="#e5e7eb"},onMouseOut:e=>{e.currentTarget.style.backgroundColor="#f3f4f6"},children:e},e))})]})]},n)),y&&(0,o.jsxs)("div",{style:{display:"flex",alignItems:"flex-start",gap:"8px"},children:[(0,o.jsx)(r.default,{src:"/brea.png",alt:"Brea",width:32,height:32,style:{borderRadius:"50%"}}),(0,o.jsx)("div",{style:{backgroundColor:"#ffffff",padding:"12px 16px",borderRadius:"16px 16px 16px 4px",fontSize:"14px",color:"#6b7280",boxShadow:"0 1px 3px rgba(0, 0, 0, 0.1)"},children:"Brea is typing..."})]}),(0,o.jsx)("div",{ref:p})]}),(0,o.jsx)("div",{style:{backgroundColor:"#ffffff",padding:"16px",borderTop:"1px solid #e5e7eb"},children:(0,o.jsxs)("div",{style:{display:"flex",gap:"12px",alignItems:"flex-end"},children:[(0,o.jsx)("textarea",{value:u,onChange:e=>g(e.target.value),onKeyPress:e=>{"Enter"!==e.key||e.shiftKey||(e.preventDefault(),b(u))},placeholder:"Type your message...",style:{flex:1,padding:"12px",border:"1px solid #d1d5db",borderRadius:"8px",fontSize:"14px",resize:"none",minHeight:"44px",maxHeight:"120px",outline:"none",fontFamily:"inherit",color:"#000000",backgroundColor:"#ffffff"},rows:1}),(0,o.jsx)("button",{onClick:()=>b(u),disabled:!u.trim(),style:{padding:"12px 20px",backgroundColor:u.trim()?"#000000":"#e5e7eb",color:u.trim()?"#ffffff":"#9ca3af",border:"none",borderRadius:"8px",fontSize:"14px",fontWeight:"500",cursor:u.trim()?"pointer":"not-allowed",transition:"all 0.2s"},children:"Send"})]})})]})}):null}},86586:function(e,n,t){"use strict";var o=t(57437),l=t(2265),r=t(18913),i=t(64677),a=t(8790);n.Z=e=>{let{isOpen:n,onClose:t,onSignatureComplete:s,employeeName:d="Employee"}=e,c=(0,l.useRef)(null),p=(0,l.useRef)(null),[u,g]=(0,l.useState)(!1),[x,f]=(0,l.useState)(!1);(0,l.useEffect)(()=>{if(n&&c.current&&!p.current){let e=c.current;e.width=600,e.height=200;let n=new i.Z(e,{backgroundColor:"#ffffff",penColor:"#000000",minWidth:1,maxWidth:3,throttle:16,minDistance:5,dotSize:0,velocityFilterWeight:.7});n.addEventListener("beginStroke",()=>{g(!0)}),n.addEventListener("endStroke",()=>{g(!n.isEmpty())}),p.current=n;let t=e.getContext("2d");t&&(t.strokeStyle="#d1d5db",t.lineWidth=1,t.setLineDash([5,5]),t.beginPath(),t.moveTo(50,e.height-30),t.lineTo(e.width-50,e.height-30),t.stroke(),t.setLineDash([]))}return()=>{p.current&&!n&&(p.current.off(),p.current=null)}},[n]);let h=async()=>{if(u&&x&&p.current)try{let e=p.current.toDataURL("image/png",1),n={signature:e,timestamp:new Date().toISOString(),employeeName:d,userAgent:navigator.userAgent,ipAddress:"client-side",signatureHash:btoa(e).substring(0,32),signaturePadData:p.current.toData(),quality:1,format:"PNG"},t=btoa(Array.from(new TextEncoder().encode(JSON.stringify(n))).map(e=>String.fromCharCode(e)).join(""));localStorage.setItem("enrollmentSignature",t);let o=await (0,a.we)(t);o.success||console.warn("⚠️ Failed to save signature to database:",o.error),console.log("\uD83D\uDCDD Signature captured, stored locally and sent to database:",{timestamp:n.timestamp,employeeName:n.employeeName,signatureHash:n.signatureHash,dataSize:e.length,vectorPoints:n.signaturePadData.length}),s(t)}catch(e){console.error("❌ Error saving signature:",e),alert("Signature saved locally but failed to save to database. Please contact support if this persists.")}};return n?(0,o.jsx)("div",{style:{position:"fixed",top:0,left:0,right:0,bottom:0,backgroundColor:"rgba(0, 0, 0, 0.5)",zIndex:1e4,display:"flex",alignItems:"center",justifyContent:"center",padding:"20px"},children:(0,o.jsxs)("div",{style:{backgroundColor:"#ffffff",borderRadius:"16px",padding:"32px",maxWidth:"700px",width:"100%",maxHeight:"90vh",overflowY:"auto",boxShadow:"0 25px 50px rgba(0, 0, 0, 0.25)",position:"relative"},children:[(0,o.jsx)("button",{onClick:t,style:{position:"absolute",top:"16px",right:"16px",background:"none",border:"none",fontSize:"24px",cursor:"pointer",color:"#6b7280",padding:"4px"},children:(0,o.jsx)(r.fMW,{})}),(0,o.jsxs)("div",{style:{marginBottom:"24px"},children:[(0,o.jsx)("h2",{style:{fontSize:"24px",fontWeight:"600",color:"#111827",margin:"0 0 8px 0"},children:"Create your signature"}),(0,o.jsx)("p",{style:{color:"#6b7280",fontSize:"14px",margin:0,lineHeight:"21px"},children:"Some carriers require a hand-drawn signature. Please draw your signature in the box below."})]}),(0,o.jsxs)("div",{style:{border:"2px solid #e5e7eb",borderRadius:"8px",marginBottom:"16px",position:"relative",backgroundColor:"#ffffff"},children:[(0,o.jsx)("canvas",{ref:c,style:{width:"100%",height:"200px",cursor:"crosshair",display:"block",touchAction:"none"}}),(0,o.jsx)("button",{onClick:()=>{if(p.current){p.current.clear(),g(!1);let e=c.current;if(e){let n=e.getContext("2d");n&&(n.strokeStyle="#d1d5db",n.lineWidth=1,n.setLineDash([5,5]),n.beginPath(),n.moveTo(50,e.height-30),n.lineTo(e.width-50,e.height-30),n.stroke(),n.setLineDash([]))}}},style:{position:"absolute",top:"8px",right:"8px",background:"none",border:"none",color:"#6b7280",fontSize:"14px",cursor:"pointer",padding:"4px 8px",borderRadius:"4px",backgroundColor:"rgba(255, 255, 255, 0.9)"},children:"Clear"}),(0,o.jsxs)("div",{style:{position:"absolute",bottom:"8px",left:"50px",fontSize:"12px",color:"#9ca3af"},children:[(0,o.jsx)(r._vs,{style:{display:"inline",marginRight:"4px"}}),"Sign above this line"]})]}),(0,o.jsx)("div",{style:{backgroundColor:"#f0fdf4",border:"1px solid #bbf7d0",borderRadius:"8px",padding:"16px",marginBottom:"24px"},children:(0,o.jsxs)("label",{style:{display:"flex",alignItems:"flex-start",gap:"12px",cursor:"pointer"},children:[(0,o.jsx)("input",{type:"checkbox",checked:x,onChange:e=>f(e.target.checked),style:{marginTop:"2px",width:"16px",height:"16px"}}),(0,o.jsxs)("div",{children:[(0,o.jsx)("div",{style:{fontSize:"14px",fontWeight:"600",color:"#065f46",marginBottom:"4px"},children:"\uD83D\uDD12 SHA-256 with RSA Encryption"}),(0,o.jsx)("div",{style:{fontSize:"14px",color:"#047857",lineHeight:"20px"},children:"I understand this is a legal representation of my signature and confirms my enrollment selections."})]})]})}),(0,o.jsxs)("div",{style:{display:"flex",gap:"12px",justifyContent:"flex-end"},children:[(0,o.jsx)("button",{onClick:t,style:{padding:"12px 24px",backgroundColor:"white",border:"1px solid #d1d5db",borderRadius:"8px",color:"#374151",cursor:"pointer",fontWeight:"500",fontSize:"14px"},children:"Cancel"}),(0,o.jsxs)("button",{onClick:h,disabled:!u||!x,style:{padding:"12px 24px",backgroundColor:u&&x?"#2563eb":"#9ca3af",border:"none",borderRadius:"8px",color:"white",cursor:u&&x?"pointer":"not-allowed",fontWeight:"500",fontSize:"14px",display:"flex",alignItems:"center",gap:"8px"},children:["Next",(0,o.jsx)("span",{style:{fontSize:"14px"},children:"→"})]})]})]})}):null}},26167:function(e,n,t){"use strict";t.r(n),t.d(n,{default:function(){return eM}});var o=t(57437),l=t(2265);t(36521);var r=t(90887),i=t(62184),a=t(16994),s=t(9260),d=t(56141),c=t(60425),p=t(36086),u=t(87928),g=t(5255),x=t(5485),f=t(91230),h=t(33145),m=t(77101),y=t(64225),b=t(44986),v=t(79679);let D=e=>{let{onClose:n}=e,[t,r]=(0,l.useState)(!!n),[i,a]=(0,l.useState)(""),[s,d]=(0,l.useState)([]),c=()=>{r(!1),n&&n()},p=e=>{let n={question:e.fullQuestion,answer:e.answer,isCustom:!1};d(e=>[...e,n])},u=()=>{if(i.trim()){let e={question:i,answer:"I'd be happy to help! For specific questions about plan details, costs, or coverage, please contact HR or the insurance carrier directly. You can also check the plan documents for detailed information.",isCustom:!0};d(n=>[...n,e]),a("")}};return t?(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)("div",{style:{position:"fixed",inset:0,backgroundColor:"rgba(0, 0, 0, 0.5)",zIndex:50},onClick:c}),(0,o.jsxs)("div",{style:{position:"fixed",top:"50%",left:"50%",transform:"translate(-50%, -50%)",width:"95vw",maxWidth:"800px",maxHeight:"85vh",backgroundColor:"white",borderRadius:"16px",boxShadow:"0 25px 50px rgba(0, 0, 0, 0.25)",zIndex:51,overflow:"hidden",display:"flex",flexDirection:"column"},children:[(0,o.jsxs)("div",{style:{display:"flex",alignItems:"center",justifyContent:"space-between",padding:"16px 20px",borderBottom:"1px solid #e5e7eb",backgroundColor:"#f8fafc"},children:[(0,o.jsxs)("div",{style:{display:"flex",alignItems:"center",gap:"8px"},children:[(0,o.jsx)("div",{style:{width:"24px",height:"24px",backgroundColor:"#3b82f6",borderRadius:"6px",display:"flex",alignItems:"center",justifyContent:"center"},children:(0,o.jsx)(y.Z,{size:14,style:{color:"white"}})}),(0,o.jsx)("h2",{style:{fontSize:"16px",fontWeight:"600",color:"#111827",margin:0,fontFamily:"sans-serif"},children:"Benefits Q&A Assistant"})]}),(0,o.jsx)("button",{onClick:c,style:{padding:"4px",backgroundColor:"transparent",border:"none",borderRadius:"4px",cursor:"pointer",color:"#6b7280"},children:(0,o.jsx)(b.Z,{size:16})})]}),(0,o.jsxs)("div",{style:{flex:1,display:"flex",flexDirection:"column",overflow:"hidden"},children:[(0,o.jsxs)("div",{style:{padding:"16px 20px",borderBottom:"1px solid #e5e7eb"},children:[(0,o.jsx)("h3",{style:{fontSize:"14px",fontWeight:"600",color:"#111827",margin:"0 0 12px 0",fontFamily:"sans-serif"},children:"Common Questions:"}),(0,o.jsx)("div",{style:{display:"grid",gridTemplateColumns:"repeat(3, 1fr)",gridTemplateRows:"repeat(2, 1fr)",gap:"8px"},children:[{question:"What happens if I don't enroll...",fullQuestion:"What happens if I don't enroll in benefits?",answer:"If you don't enroll during open enrollment, you'll have no medical, dental, or vision coverage for 2025. You can only enroll outside of open enrollment if you have a qualifying life event."},{question:"Can I change my plan after enr...",fullQuestion:"Can I change my plan after enrollment?",answer:"You can only change your plan during the next open enrollment period or if you have a qualifying life event (marriage, birth of child, job loss, etc.)."},{question:"What's the difference between ...",fullQuestion:"What's the difference between HMO and PPO?",answer:"PPO plans offer more flexibility to see any doctor without referrals but cost more. HMO plans require you to choose a primary care physician and get referrals for specialists, but have lower costs."},{question:"How much will I pay per payche...",fullQuestion:"How much will I pay per paycheck?",answer:"Your paycheck deduction depends on the plan you choose and your family size. The costs shown during enrollment are per month - they'll be divided across your paychecks."},{question:"Are my current doctors covered...",fullQuestion:"Are my current doctors covered?",answer:"This depends on the plan's network. Check the provider directory for each plan to see if your doctors are covered. PPO plans typically have larger networks than HMO plans."}].map((e,n)=>(0,o.jsx)("button",{onClick:()=>p(e),style:{padding:"8px 12px",backgroundColor:"white",border:"1px solid #e2e8f0",borderRadius:"6px",fontSize:"13px",color:"#475569",textAlign:"left",cursor:"pointer",transition:"all 0.2s ease",fontFamily:"sans-serif",whiteSpace:"nowrap",overflow:"hidden",textOverflow:"ellipsis"},onMouseOver:e=>{e.currentTarget.style.backgroundColor="#f8fafc",e.currentTarget.style.borderColor="#cbd5e1"},onMouseOut:e=>{e.currentTarget.style.backgroundColor="white",e.currentTarget.style.borderColor="#e2e8f0"},children:e.question},n))})]}),(0,o.jsx)("div",{style:{flex:1,minHeight:"300px",padding:"20px",overflowY:"auto",display:"flex",flexDirection:"column",gap:"12px",backgroundColor:"#fafbfc"},children:0===s.length?(0,o.jsx)("div",{style:{display:"flex",alignItems:"center",justifyContent:"center",height:"100%",color:"#94a3b8",fontSize:"14px",fontFamily:"sans-serif"},children:"Click a question above or type your own question below"}):s.map((e,n)=>(0,o.jsxs)("div",{style:{display:"flex",flexDirection:"column",gap:"8px"},children:[(0,o.jsx)("div",{style:{padding:"12px 16px",backgroundColor:"#3b82f6",borderRadius:"16px",alignSelf:"flex-end",maxWidth:"80%"},children:(0,o.jsx)("p",{style:{color:"white",fontSize:"14px",margin:0,fontFamily:"sans-serif"},children:e.question})}),(0,o.jsx)("div",{style:{padding:"16px",backgroundColor:"#f1f5f9",borderRadius:"16px",alignSelf:"flex-start",maxWidth:"90%"},children:(0,o.jsx)("p",{style:{color:"#334155",fontSize:"14px",margin:0,lineHeight:"1.5",fontFamily:"sans-serif"},children:e.answer})})]},n))}),(0,o.jsx)("div",{style:{padding:"16px 20px",backgroundColor:"#f8fafc",borderTop:"1px solid #e5e7eb"},children:(0,o.jsxs)("div",{style:{display:"flex",gap:"8px",alignItems:"flex-end"},children:[(0,o.jsx)("input",{type:"text",value:i,onChange:e=>a(e.target.value),onKeyDown:e=>{"Enter"!==e.key||e.shiftKey||(e.preventDefault(),u())},placeholder:"Ask a question about your benefits...",style:{flex:1,padding:"12px 16px",border:"1px solid #d1d5db",borderRadius:"24px",fontSize:"14px",outline:"none",fontFamily:"sans-serif",backgroundColor:"white",color:"#111827"}}),(0,o.jsx)("button",{onClick:u,disabled:!i.trim(),style:{width:"40px",height:"40px",backgroundColor:i.trim()?"#1e293b":"#94a3b8",border:"none",borderRadius:"50%",display:"flex",alignItems:"center",justifyContent:"center",cursor:i.trim()?"pointer":"not-allowed",transition:"all 0.2s ease"},children:(0,o.jsx)(v.Z,{size:16,style:{color:"white"}})})]})})]})]})]}):(0,o.jsx)("div",{style:{position:"fixed",bottom:"24px",right:"24px",zIndex:50},children:(0,o.jsxs)("button",{onClick:()=>r(!0),style:{display:"flex",alignItems:"center",gap:"8px",padding:"12px 20px",backgroundColor:"#2563eb",color:"white",border:"none",borderRadius:"50px",fontSize:"14px",fontWeight:"500",cursor:"pointer",boxShadow:"0 10px 25px rgba(0, 0, 0, 0.15)",transition:"all 0.2s ease",fontFamily:"sans-serif"},onMouseOver:e=>{e.currentTarget.style.backgroundColor="#1d4ed8",e.currentTarget.style.boxShadow="0 15px 35px rgba(0, 0, 0, 0.2)"},onMouseOut:e=>{e.currentTarget.style.backgroundColor="#2563eb",e.currentTarget.style.boxShadow="0 10px 25px rgba(0, 0, 0, 0.15)"},children:[(0,o.jsx)(y.Z,{size:20}),"Need Help?"]})})};var j=t(59860);let S=e=>{let{title:n,description:t,thumbnailUrl:r,videoUrl:i,planType:a,onClose:s}=e,[d,c]=(0,l.useState)(!0),p=()=>{c(!1),s&&s()},u=(()=>{switch(a){case"medical":return{title:"Understanding Medical Plans: PPO vs HMO",description:"Learn the key differences between PPO and HMO plans, including costs, flexibility, and how to choose the right one for your needs.",duration:"3:24"};case"dental":return{title:"Dental Benefits Explained",description:"Discover how dental insurance works, what's covered, and how to maximize your benefits for routine and major dental work.",duration:"2:45"};case"vision":return{title:"Vision Benefits Overview",description:"Learn about vision coverage for eye exams, glasses, contacts, and how to use your benefits effectively.",duration:"2:15"};case"summary":return{title:"Benefits Summary Overview",description:"Review your benefits enrollment summary and understand your next steps in the enrollment process.",duration:"2:30"};default:return{title:n,description:t,duration:"3:00"}}})();return d?(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)("div",{style:{position:"fixed",inset:0,backgroundColor:"rgba(0, 0, 0, 0.5)",zIndex:50},onClick:p}),(0,o.jsxs)("div",{style:{position:"fixed",top:"50%",left:"50%",transform:"translate(-50%, -50%)",width:"90vw",maxWidth:"900px",backgroundColor:"white",borderRadius:"16px",boxShadow:"0 25px 50px rgba(0, 0, 0, 0.25)",zIndex:51,overflow:"hidden"},children:[(0,o.jsxs)("div",{style:{display:"flex",alignItems:"center",justifyContent:"space-between",padding:"20px 24px",borderBottom:"1px solid #e5e7eb"},children:[(0,o.jsxs)("div",{style:{display:"flex",alignItems:"center",gap:"8px"},children:[(0,o.jsx)(j.Z,{size:20,style:{color:"#2563eb"}}),(0,o.jsx)("h2",{style:{fontSize:"18px",fontWeight:"600",color:"#111827",margin:0,fontFamily:"sans-serif"},children:u.title})]}),(0,o.jsx)("button",{onClick:p,style:{padding:"8px",backgroundColor:"transparent",border:"none",borderRadius:"8px",cursor:"pointer",color:"#6b7280"},children:(0,o.jsx)(b.Z,{size:20})})]}),(0,o.jsxs)("div",{style:{padding:"24px"},children:[(0,o.jsx)("div",{style:{aspectRatio:"16/9",borderRadius:"8px",overflow:"hidden",marginBottom:"16px"},children:(0,o.jsx)("iframe",{width:"100%",height:"100%",src:"https://www.youtube.com/embed/MPN66L_skBw",title:"Benefits Enrollment Video",frameBorder:"0",allow:"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share",allowFullScreen:!0,style:{border:"none",borderRadius:"8px"}})}),(0,o.jsxs)("div",{style:{backgroundColor:"white",border:"1px solid #e5e7eb",borderRadius:"8px",padding:"16px"},children:[(0,o.jsx)("h4",{style:{fontSize:"14px",fontWeight:"600",color:"#111827",margin:"0 0 8px 0",fontFamily:"sans-serif"},children:"What you'll learn:"}),(0,o.jsx)("p",{style:{fontSize:"14px",color:"#6b7280",margin:0,lineHeight:"1.5",fontFamily:"sans-serif"},children:u.description})]})]})]})]}):(0,o.jsxs)("button",{onClick:()=>c(!0),style:{display:"flex",alignItems:"center",gap:"8px",padding:"8px 16px",color:"#374151",border:"1px solid #d1d5db",borderRadius:"8px",backgroundColor:"white",cursor:"pointer",transition:"background-color 0.2s ease",fontSize:"14px",fontWeight:"500",fontFamily:"sans-serif"},onMouseOver:e=>e.currentTarget.style.backgroundColor="#f9fafb",onMouseOut:e=>e.currentTarget.style.backgroundColor="white",children:[(0,o.jsx)(m.Z,{size:16,style:{color:"#6b7280"}}),"Watch Video"]})};var C=t(45244);let w=()=>"June 30, 2025",k=e=>{let{onNext:n}=e,[t,r]=(0,l.useState)(!1),[i,a]=(0,l.useState)(!1),[s,d]=(0,l.useState)(!1);return(0,o.jsxs)("div",{style:{display:"flex",flexDirection:"column",gap:"24px",fontFamily:'"SF Pro", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif'},children:[(0,o.jsxs)("div",{style:{display:"flex",gap:"16px"},children:[(0,o.jsx)("div",{style:{width:"40px",height:"40px",borderRadius:"50%",display:"flex",alignItems:"center",justifyContent:"center",flexShrink:0,overflow:"hidden"},children:(0,o.jsx)(h.default,{src:"/brea.png",alt:"Brea - AI Benefits Assistant",width:40,height:40,style:{borderRadius:"50%"}})}),(0,o.jsxs)("div",{style:{backgroundColor:"#f9fafb",borderRadius:"8px",padding:"16px",maxWidth:"1000px"},children:[(0,o.jsx)("p",{style:{color:"#374151",lineHeight:"1.6",margin:0},children:(0,o.jsx)("span",{style:{fontWeight:"600"},children:"\uD83D\uDC4B Hi there! Let's simplify your benefits enrollment."})}),(0,o.jsx)("p",{style:{color:"#374151",lineHeight:"1.6",margin:"8px 0 0 0"},children:"I'll ask a few quick questions and guide you to the best-fit plans — no stress, no guesswork."})]})]}),(0,o.jsx)("div",{style:{backgroundColor:"white",borderRadius:"8px",border:"1px solid #e5e7eb",padding:"24px",boxShadow:"0 1px 3px 0 rgba(0, 0, 0, 0.1)"},children:(0,o.jsxs)("div",{style:{display:"flex",flexDirection:"column",gap:"16px"},children:[(0,o.jsx)("p",{style:{color:"#374151",display:"flex",alignItems:"center",gap:"8px",margin:0,fontWeight:"600",fontSize:"16px",lineHeight:"1.6",fontFamily:'"SF Pro", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif'},children:"\uD83D\uDD14 Smarter Benefits Start Now"}),(0,o.jsx)("div",{style:{backgroundColor:"#fdf2f8",border:"1px solid #fce7f3",borderRadius:"8px",padding:"16px"},children:(0,o.jsxs)("p",{style:{color:"#be185d",fontWeight:"500",display:"flex",alignItems:"center",gap:"8px",margin:0,fontSize:"14px",lineHeight:"1.6",fontFamily:'"SF Pro", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif'},children:["\uD83D\uDCC5 ",(0,o.jsx)("span",{style:{fontWeight:"600"},children:"Enrollment Deadline:"})," ",w()]})}),(0,o.jsxs)("div",{style:{display:"flex",flexDirection:"column",gap:"12px"},children:[(0,o.jsx)("p",{style:{color:"#ec4899",fontWeight:"500",display:"flex",alignItems:"center",gap:"8px",margin:0,fontSize:"14px",lineHeight:"1.6",fontFamily:'"SF Pro", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif'},children:"\uD83E\uDDE0 I'm Brea, your AI benefits assistant. Here's how I'll help:"}),(0,o.jsxs)("ul",{style:{display:"flex",flexDirection:"column",gap:"8px",marginLeft:"24px",listStyle:"none",padding:0,margin:0},children:[(0,o.jsxs)("li",{style:{color:"#374151",display:"flex",alignItems:"flex-start",gap:"8px",fontSize:"14px",lineHeight:"1.6",fontFamily:'"SF Pro", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif'},children:[(0,o.jsx)("span",{style:{color:"#2563eb",marginTop:"4px"},children:"•"}),"Understand your needs in minutes"]}),(0,o.jsxs)("li",{style:{color:"#374151",display:"flex",alignItems:"flex-start",gap:"8px",fontSize:"14px",lineHeight:"1.6",fontFamily:'"SF Pro", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif'},children:[(0,o.jsx)("span",{style:{color:"#2563eb",marginTop:"4px"},children:"•"}),"Recommend plans tailored to you"]}),(0,o.jsxs)("li",{style:{color:"#374151",display:"flex",alignItems:"flex-start",gap:"8px",fontSize:"14px",lineHeight:"1.6",fontFamily:'"SF Pro", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif'},children:[(0,o.jsx)("span",{style:{color:"#2563eb",marginTop:"4px"},children:"•"}),"Show short, clear explainer videos"]}),(0,o.jsxs)("li",{style:{color:"#374151",display:"flex",alignItems:"flex-start",gap:"8px",fontSize:"14px",lineHeight:"1.6",fontFamily:'"SF Pro", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif'},children:[(0,o.jsx)("span",{style:{color:"#2563eb",marginTop:"4px"},children:"•"}),"Answer your questions on the spot"]}),(0,o.jsxs)("li",{style:{color:"#374151",display:"flex",alignItems:"flex-start",gap:"8px",fontSize:"14px",lineHeight:"1.6",fontFamily:'"SF Pro", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif'},children:[(0,o.jsx)("span",{style:{color:"#2563eb",marginTop:"4px"},children:"•"}),"Compare top plan choices side by side"]})]})]}),(0,o.jsxs)("div",{style:{display:"flex",gap:"12px",paddingTop:"16px"},children:[(0,o.jsxs)("button",{onClick:()=>d(!0),style:{display:"flex",alignItems:"center",gap:"8px",padding:"8px 16px",color:"#374151",border:"1px solid #d1d5db",borderRadius:"8px",backgroundColor:"white",cursor:"pointer",transition:"background-color 0.2s ease",fontSize:"14px",fontWeight:"500",fontFamily:'"SF Pro", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif'},onMouseOver:e=>e.currentTarget.style.backgroundColor="#f9fafb",onMouseOut:e=>e.currentTarget.style.backgroundColor="white",children:[(0,o.jsx)("span",{style:{color:"#2563eb"},children:"❓"}),"Ask Questions"]}),(0,o.jsxs)("button",{onClick:()=>a(!0),style:{display:"flex",alignItems:"center",gap:"8px",padding:"8px 16px",color:"#374151",border:"1px solid #d1d5db",borderRadius:"8px",backgroundColor:"white",cursor:"pointer",transition:"background-color 0.2s ease",fontSize:"14px",fontWeight:"500",fontFamily:'"SF Pro", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif'},onMouseOver:e=>e.currentTarget.style.backgroundColor="#f9fafb",onMouseOut:e=>e.currentTarget.style.backgroundColor="white",children:[(0,o.jsx)(m.Z,{size:16,style:{color:"#6b7280"}}),"Watch Video"]})]}),(0,o.jsx)("button",{onClick:n,style:{width:"100%",backgroundColor:"#111827",color:"white",padding:"12px 24px",borderRadius:"8px",fontWeight:"600",border:"none",cursor:"pointer",transition:"background-color 0.2s ease",display:"flex",alignItems:"center",justifyContent:"center",gap:"8px",marginTop:"24px",fontSize:"14px",fontFamily:'"SF Pro", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif'},onMouseOver:e=>e.currentTarget.style.backgroundColor="#374151",onMouseOut:e=>e.currentTarget.style.backgroundColor="#111827",children:"\uD83D\uDE80 Start My Smart Enrollment"})]})}),t&&(0,o.jsx)(D,{onClose:()=>r(!1)}),i&&(0,o.jsx)(S,{title:"Benefits Enrollment Overview",description:"Learn how to navigate your benefits enrollment process",planType:"medical",onClose:()=>a(!1)}),s&&(0,o.jsx)(C.Z,{isOpen:s,onClose:()=>d(!1)})]})};var I=t(36549),P=t(86383),W=t(54585);let E=e=>{let{onComplete:n,initialData:t}=e,[r,i]=(0,l.useState)((null==t?void 0:t.familyMembers)||""),[s,d]=(0,l.useState)((null==t?void 0:t.expectedMedicalUsage)||""),[c,p]=(0,l.useState)((null==t?void 0:t.budgetPreference)||""),[u,g]=(0,l.useState)((null==t?void 0:t.budgetAmount)||100),[x,f]=(0,l.useState)((null==t?void 0:t.healthConsiderations)||[]),[y,b]=(0,l.useState)(!1),[v,j]=(0,l.useState)(!1),[w,k]=(0,l.useState)(!1),E=(e,n)=>{n?f(n=>[...n,e]):f(n=>n.filter(n=>n!==e))},T=r&&s&&c;return(0,o.jsxs)("div",{style:{display:"flex",flexDirection:"column",gap:"24px"},children:[(0,o.jsxs)("div",{style:{display:"flex",gap:"16px"},children:[(0,o.jsx)("div",{style:{width:"40px",height:"40px",borderRadius:"50%",display:"flex",alignItems:"center",justifyContent:"center",flexShrink:0,overflow:"hidden"},children:(0,o.jsx)(h.default,{src:"/brea.png",alt:"Brea - AI Benefits Assistant",width:40,height:40,style:{borderRadius:"50%"}})}),(0,o.jsxs)("div",{style:{backgroundColor:"#f9fafb",borderRadius:"8px",padding:"16px",maxWidth:"1000px"},children:[(0,o.jsx)("p",{style:{color:"#374151",lineHeight:"1.6",margin:0},children:"\uD83E\uDD14 Let me learn about you to give the best recommendations!"}),(0,o.jsx)("p",{style:{color:"#374151",lineHeight:"1.6",margin:"8px 0 0 0"},children:"I'll ask a few quick questions about your healthcare needs, budget preferences, and family situation."})]})]}),(0,o.jsxs)("div",{style:{backgroundColor:"white",borderRadius:"8px",border:"1px solid #e5e7eb",padding:"24px",boxShadow:"0 1px 3px 0 rgba(0, 0, 0, 0.1)"},children:[(0,o.jsxs)("div",{style:{display:"flex",alignItems:"center",gap:"8px",marginBottom:"24px"},children:[(0,o.jsx)("div",{style:{width:"24px",height:"24px",backgroundColor:"#f3e8ff",borderRadius:"4px",display:"flex",alignItems:"center",justifyContent:"center"},children:(0,o.jsx)(I.Z,{style:{width:"16px",height:"16px",color:"#9333ea"}})}),(0,o.jsx)("h2",{style:{fontSize:"20px",fontWeight:"600",color:"#111827",margin:0},children:"AI-Powered Personalization ✨"})]}),(0,o.jsx)("p",{style:{color:"#6b7280",marginBottom:"24px",margin:0},children:"Let me learn about you to provide the most accurate recommendations:"}),(0,o.jsxs)("div",{style:{display:"flex",flexDirection:"column",gap:"24px"},children:[(0,o.jsxs)("div",{style:{display:"flex",flexDirection:"column",gap:"16px"},children:[(0,o.jsxs)("div",{style:{display:"flex",alignItems:"center",gap:"8px"},children:[(0,o.jsx)(P.Z,{style:{width:"20px",height:"20px",color:"#ef4444"}}),(0,o.jsx)("h3",{style:{fontSize:"18px",fontWeight:"500",color:"#111827",margin:0},children:"Enhanced Health Profile"})]}),(0,o.jsxs)("div",{style:{display:"flex",flexDirection:"column",gap:"12px"},children:[(0,o.jsxs)("div",{style:{display:"flex",alignItems:"center",gap:"8px"},children:[(0,o.jsx)(a.Z,{style:{width:"16px",height:"16px",color:"#6b7280"}}),(0,o.jsx)("label",{style:{fontWeight:"500",color:"#374151",margin:0},children:"Family Coverage Needed"})]}),(0,o.jsx)("div",{style:{display:"flex",flexDirection:"column",gap:"8px"},children:[{value:"employee-only",label:"Just me (Employee only)"},{value:"employee-spouse",label:"Me + Spouse/Partner"},{value:"employee-children",label:"Me + Child(ren)"},{value:"employee-family",label:"Me + Family (includes children)"}].map(e=>(0,o.jsxs)("label",{style:{display:"flex",alignItems:"center",gap:"12px",cursor:"pointer"},children:[(0,o.jsx)("input",{type:"radio",name:"familyMembers",value:e.value,checked:r===e.value,onChange:e=>i(e.target.value),style:{width:"16px",height:"16px",accentColor:"#000000"}}),(0,o.jsx)("span",{style:{color:"#374151"},children:e.label})]},e.value))})]}),(0,o.jsxs)("div",{style:{display:"flex",flexDirection:"column",gap:"12px"},children:[(0,o.jsxs)("div",{style:{display:"flex",alignItems:"center",gap:"8px"},children:[(0,o.jsx)(I.Z,{style:{width:"16px",height:"16px",color:"#6b7280"}}),(0,o.jsx)("label",{style:{fontWeight:"500",color:"#374151",margin:0},children:"Expected Healthcare Usage"})]}),(0,o.jsx)("div",{style:{display:"flex",flexDirection:"column",gap:"8px"},children:[{value:"low",label:"Low - Just preventive care & checkups"},{value:"moderate",label:"Moderate - Occasional visits & some prescriptions"},{value:"high",label:"High - Regular specialists, procedures, or chronic conditions"}].map(e=>(0,o.jsxs)("label",{style:{display:"flex",alignItems:"center",gap:"12px",cursor:"pointer"},children:[(0,o.jsx)("input",{type:"radio",name:"expectedUsage",value:e.value,checked:s===e.value,onChange:e=>d(e.target.value),style:{width:"16px",height:"16px",accentColor:"#000000"}}),(0,o.jsx)("span",{style:{color:"#374151"},children:e.label})]},e.value))})]}),(0,o.jsxs)("div",{style:{display:"flex",flexDirection:"column",gap:"12px"},children:[(0,o.jsxs)("div",{style:{display:"flex",alignItems:"center",gap:"8px"},children:[(0,o.jsx)(W.Z,{style:{width:"16px",height:"16px",color:"#6b7280"}}),(0,o.jsx)("label",{style:{fontWeight:"500",color:"#374151",margin:0},children:"Budget Preference"})]}),(0,o.jsx)("div",{style:{display:"flex",flexDirection:"column",gap:"8px"},children:[{value:"low-premium",label:"Lower monthly cost, higher deductible"},{value:"balanced",label:"Balanced monthly cost and deductible"},{value:"low-deductible",label:"Higher monthly cost, lower deductible"}].map(e=>(0,o.jsxs)("label",{style:{display:"flex",alignItems:"center",gap:"12px",cursor:"pointer"},children:[(0,o.jsx)("input",{type:"radio",name:"budgetPreference",value:e.value,checked:c===e.value,onChange:e=>p(e.target.value),style:{width:"16px",height:"16px",accentColor:"#000000"}}),(0,o.jsx)("span",{style:{color:"#374151"},children:e.label})]},e.value))})]}),(0,o.jsxs)("div",{style:{display:"flex",flexDirection:"column",gap:"12px"},children:[(0,o.jsxs)("label",{style:{fontWeight:"500",color:"#374151",margin:0},children:["Maximum monthly budget: $",u,"/paycheck"]}),(0,o.jsx)("input",{type:"range",min:"50",max:"200",value:u,onChange:e=>g(Number(e.target.value)),style:{width:"100%",height:"8px",backgroundColor:"#000000",borderRadius:"8px",appearance:"none",cursor:"pointer"}})]}),(0,o.jsxs)("div",{style:{display:"flex",flexDirection:"column",gap:"12px"},children:[(0,o.jsx)("label",{style:{fontWeight:"500",color:"#374151",margin:0},children:"Health Considerations"}),(0,o.jsx)("div",{style:{display:"flex",flexDirection:"column",gap:"8px"},children:[{value:"glasses-contacts",label:"I wear glasses or contacts"},{value:"dental-care",label:"I need regular dental care or have dental work planned"},{value:"ongoing-conditions",label:"I have ongoing health conditions requiring regular care"},{value:"prescription-medications",label:"I take regular prescription medications"},{value:"preferred-doctors",label:"I have preferred doctors I want to keep seeing"}].map(e=>(0,o.jsxs)("label",{style:{display:"flex",alignItems:"center",gap:"12px",cursor:"pointer"},children:[(0,o.jsx)("input",{type:"checkbox",value:e.value,checked:x.includes(e.value),onChange:n=>E(e.value,n.target.checked),style:{width:"16px",height:"16px",accentColor:"#000000"}}),(0,o.jsx)("span",{style:{color:"#374151"},children:e.label})]},e.value))})]})]}),(0,o.jsx)("button",{onClick:()=>{n({familyMembers:r,expectedMedicalUsage:s,budgetPreference:c,budgetAmount:u,healthConsiderations:x,chronicConditions:x.includes("ongoing-conditions"),prescriptionNeeds:x.includes("prescription-medications"),hasPreferredDoctors:x.includes("preferred-doctors")})},disabled:!T,style:{width:"100%",padding:"12px 24px",borderRadius:"8px",fontWeight:"500",border:"none",cursor:T?"pointer":"not-allowed",transition:"background-color 0.2s ease",backgroundColor:T?"#000000":"#d1d5db",color:T?"white":"#9ca3af"},onMouseOver:e=>{T&&(e.currentTarget.style.backgroundColor="#374151")},onMouseOut:e=>{T&&(e.currentTarget.style.backgroundColor="#000000")},children:"Get My Personalized Recommendations"})]}),(0,o.jsxs)("div",{style:{display:"flex",gap:"12px",paddingTop:"24px",borderTop:"1px solid #e5e7eb",marginTop:"24px"},children:[(0,o.jsxs)("button",{onClick:()=>k(!0),style:{display:"flex",alignItems:"center",gap:"8px",padding:"8px 16px",color:"#374151",border:"1px solid #d1d5db",borderRadius:"8px",backgroundColor:"white",cursor:"pointer",transition:"background-color 0.2s ease"},onMouseOver:e=>e.currentTarget.style.backgroundColor="#f9fafb",onMouseOut:e=>e.currentTarget.style.backgroundColor="white",children:[(0,o.jsx)("span",{style:{color:"#2563eb"},children:"❓"}),"Ask Questions"]}),(0,o.jsxs)("button",{onClick:()=>j(!0),style:{display:"flex",alignItems:"center",gap:"8px",padding:"8px 16px",color:"#374151",border:"1px solid #d1d5db",borderRadius:"8px",backgroundColor:"white",cursor:"pointer",transition:"background-color 0.2s ease"},onMouseOver:e=>e.currentTarget.style.backgroundColor="#f9fafb",onMouseOut:e=>e.currentTarget.style.backgroundColor="white",children:[(0,o.jsx)(m.Z,{size:16,style:{color:"#6b7280"}}),"Watch Video"]})]})]}),y&&(0,o.jsx)(D,{onClose:()=>b(!1)}),v&&(0,o.jsx)(S,{title:"Personalization Guide",description:"Learn how to answer personalization questions for better recommendations",planType:"medical",onClose:()=>j(!1)}),w&&(0,o.jsx)(C.Z,{isOpen:w,onClose:()=>k(!1)})]})};var T=t(25361),z=t(16049),R=t(47677),A=t(69626),F=t(67350),B=t(43728),N=t(40256),O=t(98005),L=t(12032),M=e=>{let{isOpen:n,onClose:t,onConfirm:l,title:r,message:i,type:a="alert",confirmText:s="OK",cancelText:d="Cancel"}=e;if(!n)return null;let c=()=>{switch(a){case"success":return"#10b981";case"error":return"#ef4444";case"confirm":return"#f59e0b";default:return"#3b82f6"}};return(0,o.jsx)("div",{style:{position:"fixed",top:0,left:0,right:0,bottom:0,backgroundColor:"rgba(0, 0, 0, 0.5)",display:"flex",alignItems:"center",justifyContent:"center",zIndex:1e3},children:(0,o.jsxs)("div",{style:{backgroundColor:"white",borderRadius:"12px",padding:"32px",maxWidth:"400px",width:"90%",boxShadow:"0 20px 25px -5px rgba(0, 0, 0, 0.1)",textAlign:"center",position:"relative"},children:[(0,o.jsx)("button",{onClick:t,style:{position:"absolute",top:"16px",right:"16px",background:"none",border:"none",cursor:"pointer",padding:"4px",borderRadius:"4px"},children:(0,o.jsx)(b.Z,{style:{width:"20px",height:"20px",color:"#6b7280"}})}),(0,o.jsx)("div",{style:{width:"60px",height:"60px",backgroundColor:c(),borderRadius:"50%",display:"flex",alignItems:"center",justifyContent:"center",margin:"0 auto 20px"},children:(()=>{switch(a){case"success":return(0,o.jsx)(g.Z,{style:{width:"48px",height:"48px",color:"#10b981"}});case"error":return(0,o.jsx)(L.Z,{style:{width:"48px",height:"48px",color:"#ef4444"}});case"confirm":return(0,o.jsx)(L.Z,{style:{width:"48px",height:"48px",color:"#f59e0b"}});default:return(0,o.jsx)(L.Z,{style:{width:"48px",height:"48px",color:"#3b82f6"}})}})()}),(0,o.jsx)("h3",{style:{fontSize:"20px",fontWeight:"600",color:"#111827",margin:"0 0 12px 0"},children:r}),(0,o.jsx)("p",{style:{color:"#6b7280",margin:"0 0 24px 0",lineHeight:"1.5",whiteSpace:"pre-line"},children:i}),(0,o.jsxs)("div",{style:{display:"flex",gap:"12px",justifyContent:"center"},children:["confirm"===a&&(0,o.jsx)("button",{onClick:t,style:{padding:"12px 24px",backgroundColor:"white",border:"1px solid #d1d5db",borderRadius:"8px",color:"#374151",cursor:"pointer",fontWeight:"500",fontSize:"16px"},children:d}),(0,o.jsx)("button",{onClick:()=>{l&&l(),t()},style:{padding:"12px 24px",backgroundColor:c(),border:"none",borderRadius:"8px",color:"white",cursor:"pointer",fontWeight:"500",fontSize:"16px"},children:s})]})]})})};let H=async()=>{try{let e=await (0,N.A_)("/employee/company-details");if(e&&e.company){let n=e.company;try{let e=await (0,N.A_)("/admin/all-employees");if(e&&e.employees){let t=e.employees.find(e=>e.email===n.adminEmail&&e.isAdmin);if(null==t?void 0:t._id)return t._id}}catch(e){console.log("Admin endpoint not available, using fallback")}}return localStorage.getItem("userid1")||localStorage.getItem("userId")}catch(e){return console.log("Using fallback admin ID due to error:",e),localStorage.getItem("userid1")||localStorage.getItem("userId")}};var V=e=>{let{selectedDependents:n,onConfirm:t,onBack:r,onDependentsChange:i,onProfileUpdate:s}=e,[d,c]=(0,l.useState)(null),[p,u]=(0,l.useState)([]),[x,f]=(0,l.useState)(null),[m,y]=(0,l.useState)(!1),[v,D]=(0,l.useState)(!1),[j,S]=(0,l.useState)(null),[C,w]=(0,l.useState)(!1),[k,I]=(0,l.useState)(!1),[W,E]=(0,l.useState)({isValid:!0,missingFields:[],errors:[]}),[L,V]=(0,l.useState)({firstName:"",middleName:"",lastName:"",relationship:"Spouse",dateOfBirth:"",gender:"Male",ssn:""}),[U,_]=(0,l.useState)({name:"",relationship:"Spouse",dateOfBirth:"",gender:"Male",ssn:""}),[Z,q]=(0,l.useState)(!1),[$,Y]=(0,l.useState)({title:"",message:"",type:"alert",onConfirm:()=>{},confirmText:"OK",cancelText:"Cancel"}),J=function(e,n){let t=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"alert";Y({title:e,message:n,type:t,onConfirm:()=>{},confirmText:"OK",cancelText:"Cancel"}),q(!0)},G=function(e,n,t){let o=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"OK",l=arguments.length>4&&void 0!==arguments[4]?arguments[4]:"Cancel";Y({title:e,message:n,type:"confirm",onConfirm:t,confirmText:o,cancelText:l}),q(!0)},X=()=>localStorage.getItem("userid1")||localStorage.getItem("userId"),K=async()=>{try{let e=X();if(!e){console.error("User ID not found");return}let n=await (0,N.A_)("/employee",{"user-id":e});(null==n?void 0:n.currentUser)&&c(n.currentUser)}catch(e){console.error("Error fetching user details:",e)}};(0,l.useEffect)(()=>{K()},[]),(0,l.useEffect)(()=>{i&&i(p)},[p,i]),(0,l.useEffect)(()=>{var e;if((null==d?void 0:null===(e=d.details)||void 0===e?void 0:e.dependents)&&Array.isArray(d.details.dependents)){let e=d.details.dependents.map((e,n)=>({_id:e._id||"existing_".concat(n,"_").concat(Date.now()),name:e.name||"",relationship:e.relationship||"",dateOfBirth:e.dateOfBirth?new Date(e.dateOfBirth).toISOString().split("T")[0]:"",gender:e.gender||"",ssn:e.ssn||""}));console.log("\uD83D\uDD0D DependentsConfirmationPage - selectedDependents:",n),console.log("\uD83D\uDD0D DependentsConfirmationPage - formattedDependents:",e);let t=e.filter(e=>{let t=e.relationship.toLowerCase();return(console.log("\uD83D\uDD0D Checking dependent: ".concat(e.name," with relationship: ").concat(t)),n.includes("spouse")&&("spouse"===t||"domestic partner"===t))?(console.log("✅ Including spouse: ".concat(e.name)),!0):n.includes("children")&&("child"===t||"stepchild"===t||"adopted child"===t)?(console.log("✅ Including child: ".concat(e.name)),!0):(console.log("❌ Excluding dependent: ".concat(e.name," (relationship: ").concat(t,")")),!1)});console.log("\uD83D\uDD0D DependentsConfirmationPage - filteredDependents:",t),u(t)}else u([])},[n,d]);let Q=e=>{var n,t,o,l;let r=[],i=[];return(null==e?void 0:null===(n=e.details)||void 0===n?void 0:n.dateOfBirth)||(r.push("Date of Birth"),i.push("Date of birth is required for age-based cost calculations")),(null==e?void 0:null===(t=e.details)||void 0===t?void 0:t.hireDate)||(r.push("Hire Date"),i.push("Hire date is required for waiting period eligibility")),(null==e?void 0:null===(o=e.details)||void 0===o?void 0:o.employeeClassType)||(r.push("Employee Class Type"),i.push("Employee class type is required for plan eligibility")),(null==e?void 0:null===(l=e.details)||void 0===l?void 0:l.phoneNumber)||(r.push("Phone Number"),i.push("Phone number is required for enrollment communications")),{isValid:0===r.length,missingFields:r,errors:i}};(0,l.useEffect)(()=>{d&&E(Q(d))},[d]);let ee=e=>{S(e),_({name:e.name,relationship:e.relationship,dateOfBirth:e.dateOfBirth||"",gender:e.gender||"Male",ssn:e.ssn||""}),D(!0)},en=async()=>{if(!j||!U.name.trim()){J("Validation Error","Please fill in all required fields","error");return}w(!0);try{var e;let n=((null==d?void 0:null===(e=d.details)||void 0===e?void 0:e.dependents)||[]).map(e=>e._id===j._id?{...e,name:U.name.trim(),relationship:U.relationship,dateOfBirth:U.dateOfBirth?new Date(U.dateOfBirth):e.dateOfBirth,gender:U.gender,ssn:U.ssn.trim()}:e);if(console.log("Updating dependent in API:",U),console.log("Updated dependents array:",n),!X())throw Error("User ID not found");let t=await H();if(!t)throw Error("Company admin ID not found");let o=await (0,N.GH)("/admin/update/employee",{adminId:t,updatedDetails:{name:d.name,email:d.email,details:{...d.details,dependents:n}}});if(o&&200===o.status)await K(),console.log("Dependent updated successfully"),D(!1),S(null);else throw Error("Failed to update dependent")}catch(e){console.error("Error updating dependent:",e),J("Update Failed","Failed to update dependent. Please try again.","error")}finally{w(!1)}},et=async()=>{if(!L.firstName.trim()||!L.lastName.trim()||!L.dateOfBirth){J("Validation Error","Please fill in all required fields (First Name, Last Name, Date of Birth)","error");return}w(!0);try{var e;let n="".concat(L.firstName.trim()," ").concat(L.middleName.trim()?L.middleName.trim()+" ":"").concat(L.lastName.trim()).trim(),t={_id:"temp_".concat(Date.now(),"_").concat(Math.random().toString(36).substr(2,9)),name:n,relationship:L.relationship,dateOfBirth:L.dateOfBirth,gender:L.gender,ssn:L.ssn.trim()};u(e=>[...e,t]);let o=(null==d?void 0:null===(e=d.details)||void 0===e?void 0:e.dependents)||[],l={name:n,relationship:L.relationship,dateOfBirth:new Date(L.dateOfBirth).toISOString(),gender:L.gender,ssn:L.ssn.trim(),isStudent:!1,isDisabled:!1,isActive:!0},r=[...o,l];if(console.log("Adding dependent to API:",l),console.log("Updated dependents array:",r),!X())throw Error("User ID not found");let i=await H();if(!i)throw Error("Company admin ID not found");let a=await (0,N.GH)("/admin/update/employee",{adminId:i,updatedDetails:{name:d.name,email:d.email,details:{...d.details,dependents:r}}});if(a&&200===a.status)await K(),console.log("Dependent added successfully");else throw Error("Failed to update user");V({firstName:"",middleName:"",lastName:"",relationship:"Spouse",dateOfBirth:"",gender:"Male",ssn:""}),y(!1)}catch(e){console.error("Error adding dependent:",e),J("Add Failed","Failed to add dependent. Please try again.","error"),u(e=>e.filter(e=>{var n;return!(null===(n=e._id)||void 0===n?void 0:n.toString().startsWith("temp_"))}))}finally{w(!1)}},eo=async e=>{G("Remove Dependent","Are you sure you want to remove this dependent?",()=>el(e),"Remove","Cancel")},el=async e=>{var n,t,o;w(!0);try{if(u(n=>n.filter(n=>n._id!==e)),e.toString().startsWith("temp_")){w(!1);return}let t=((null==d?void 0:null===(n=d.details)||void 0===n?void 0:n.dependents)||[]).filter(n=>n._id!==e);if(console.log("Removing dependent from API, updated array:",t),!X())throw Error("User ID not found");let o=await H();if(!o)throw Error("Company admin ID not found");let l=await (0,N.GH)("/admin/update/employee",{adminId:o,updatedDetails:{name:d.name,email:d.email,details:{...d.details,dependents:t}}});if(l&&200===l.status)await K(),console.log("Dependent removed successfully");else throw Error("Failed to update user")}catch(l){console.error("Error removing dependent:",l),J("Remove Failed","Failed to remove dependent. Please try again.","error");let n=null==d?void 0:null===(o=d.details)||void 0===o?void 0:null===(t=o.dependents)||void 0===t?void 0:t.find(n=>n._id===e);if(n){let e={_id:n._id,name:n.name,relationship:n.relationship,dateOfBirth:n.dateOfBirth?new Date(n.dateOfBirth).toISOString().split("T")[0]:"",gender:n.gender};u(n=>[...n,e])}}finally{w(!1)}},er=()=>{V({firstName:"",middleName:"",lastName:"",relationship:"Spouse",dateOfBirth:"",gender:"Male",ssn:""}),y(!1)};return(0,o.jsxs)("div",{style:{display:"flex",flexDirection:"column",gap:"24px"},children:[(0,o.jsxs)("div",{style:{display:"flex",gap:"16px"},children:[(0,o.jsx)("div",{style:{width:"40px",height:"40px",borderRadius:"50%",display:"flex",alignItems:"center",justifyContent:"center",flexShrink:0,overflow:"hidden"},children:(0,o.jsx)(h.default,{src:"/brea.png",alt:"Brea - AI Benefits Assistant",width:40,height:40,style:{borderRadius:"50%"}})}),(0,o.jsxs)("div",{style:{backgroundColor:"#f9fafb",borderRadius:"8px",padding:"16px",maxWidth:"1000px"},children:[(0,o.jsx)("p",{style:{color:"#374151",lineHeight:"1.6",margin:0},children:"\uD83D\uDC68‍\uD83D\uDC69‍\uD83D\uDC67‍\uD83D\uDC66 Let's confirm your family members for coverage."}),(0,o.jsxs)("p",{style:{color:"#374151",lineHeight:"1.6",margin:"8px 0 0 0"},children:[n.includes("spouse")&&n.includes("children")?"I see you selected family coverage. Please review your spouse and children information below.":n.includes("spouse")?"I see you selected spouse coverage. Please review your spouse information below.":n.includes("children")?"I see you selected children coverage. Please review your children information below.":"I see you selected employee-only coverage. Let's make sure your profile is complete before proceeding."," ",n.length>0?"You can edit any details or add new family members if needed.":"You can add family members if your situation changes."]})]})]}),!W.isValid&&(0,o.jsxs)("div",{style:{backgroundColor:"#fef3c7",border:"1px solid #f59e0b",borderRadius:"8px",padding:"16px",marginBottom:"24px",display:"flex",alignItems:"flex-start",gap:"12px"},children:[(0,o.jsx)(T.Z,{size:20,style:{color:"#f59e0b",marginTop:"2px",flexShrink:0}}),(0,o.jsxs)("div",{style:{flex:1},children:[(0,o.jsx)("h4",{style:{fontSize:"14px",fontWeight:"600",color:"#92400e",margin:"0 0 8px 0"},children:"Complete Your Profile to Continue"}),(0,o.jsx)("p",{style:{fontSize:"14px",color:"#92400e",margin:"0 0 12px 0",lineHeight:"1.5"},children:"The following required information is missing from your profile:"}),(0,o.jsx)("ul",{style:{margin:"0 0 16px 0",paddingLeft:"20px",color:"#92400e",fontSize:"14px"},children:W.missingFields.map((e,n)=>(0,o.jsx)("li",{style:{marginBottom:"4px"},children:e},n))}),(0,o.jsxs)("button",{onClick:()=>I(!0),style:{display:"flex",alignItems:"center",gap:"8px",padding:"8px 16px",backgroundColor:"#f59e0b",color:"white",border:"none",borderRadius:"6px",fontSize:"14px",fontWeight:"500",cursor:"pointer",transition:"background-color 0.2s"},onMouseOver:e=>e.currentTarget.style.backgroundColor="#d97706",onMouseOut:e=>e.currentTarget.style.backgroundColor="#f59e0b",children:[(0,o.jsx)(z.Z,{size:16}),"Complete Profile"]})]})]}),W.isValid&&(0,o.jsxs)("div",{style:{backgroundColor:"#f0fdf4",border:"1px solid #16a34a",borderRadius:"8px",padding:"16px",marginBottom:"24px",display:"flex",alignItems:"center",gap:"12px"},children:[(0,o.jsx)(g.Z,{size:20,style:{color:"#16a34a"}}),(0,o.jsx)("div",{children:(0,o.jsx)("p",{style:{fontSize:"14px",color:"#16a34a",margin:0,fontWeight:"500"},children:"Your profile is complete and ready for enrollment!"})})]}),(0,o.jsxs)("div",{style:{backgroundColor:"white",borderRadius:"8px",border:"1px solid #e5e7eb",padding:"24px",boxShadow:"0 1px 3px 0 rgba(0, 0, 0, 0.1)"},children:[(0,o.jsxs)("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",marginBottom:"24px"},children:[(0,o.jsxs)("div",{style:{display:"flex",alignItems:"center",gap:"8px"},children:[(0,o.jsx)(a.Z,{style:{width:"24px",height:"24px",color:"#3b82f6"}}),(0,o.jsx)("h2",{style:{fontSize:"20px",fontWeight:"600",color:"#111827",margin:0},children:n.length>0?"Confirm Family Members":"Profile & Coverage Confirmation"})]}),(0,o.jsxs)("div",{style:{display:"flex",gap:"12px"},children:[(0,o.jsxs)("button",{onClick:()=>I(!0),style:{display:"flex",alignItems:"center",gap:"8px",padding:"8px 16px",backgroundColor:"white",color:"#374151",border:"1px solid #d1d5db",borderRadius:"8px",cursor:"pointer",fontSize:"14px",fontWeight:"500"},children:[(0,o.jsx)(R.Z,{size:16}),"Edit Profile"]}),(0,o.jsxs)("button",{onClick:()=>y(!0),style:{display:"flex",alignItems:"center",gap:"8px",padding:"8px 16px",backgroundColor:"#000000",color:"white",border:"none",borderRadius:"8px",cursor:"pointer",fontSize:"14px",fontWeight:"500"},children:[(0,o.jsx)(A.Z,{size:16}),"Add Dependent"]})]})]}),(0,o.jsx)("div",{style:{border:"1px solid #e5e7eb",borderRadius:"8px",padding:"16px",marginBottom:"16px",backgroundColor:"#f8fafc"},children:(0,o.jsxs)("div",{style:{display:"flex",alignItems:"center",gap:"12px"},children:[(0,o.jsx)(F.Z,{style:{width:"20px",height:"20px",color:"#6b7280"}}),(0,o.jsxs)("div",{children:[(0,o.jsxs)("h3",{style:{fontSize:"16px",fontWeight:"600",color:"#111827",margin:0},children:[(null==d?void 0:d.name)||"Employee"," (Myself)"]}),(0,o.jsx)("p",{style:{fontSize:"14px",color:"#6b7280",margin:"4px 0 0 0"},children:"Employee"})]})]})}),p.map(e=>(0,o.jsxs)("div",{style:{border:"1px solid #e5e7eb",borderRadius:"8px",padding:"16px",marginBottom:"16px",position:"relative"},children:[(0,o.jsx)("button",{onClick:()=>eo(e._id),disabled:C,style:{position:"absolute",top:"12px",right:"12px",background:"none",border:"none",cursor:C?"not-allowed":"pointer",padding:"4px",borderRadius:"4px",display:"flex",alignItems:"center",justifyContent:"center",opacity:C?.5:1},onMouseOver:e=>!C&&(e.currentTarget.style.backgroundColor="#fee2e2"),onMouseOut:e=>e.currentTarget.style.backgroundColor="transparent",children:(0,o.jsx)(B.Z,{style:{width:"16px",height:"16px",color:"#dc2626"}})}),(0,o.jsx)("div",{style:{display:"flex",alignItems:"center",paddingRight:"32px"},children:(0,o.jsxs)("div",{style:{display:"flex",alignItems:"center",gap:"12px"},children:[(0,o.jsx)(P.Z,{style:{width:"20px",height:"20px",color:"#ec4899"}}),(0,o.jsxs)("div",{children:[(0,o.jsxs)("h3",{style:{fontSize:"16px",fontWeight:"600",color:"#111827",margin:0,display:"flex",alignItems:"center",gap:"8px"},children:[e.name," (",e.relationship,")",(0,o.jsx)("button",{onClick:()=>ee(e),style:{background:"none",border:"none",cursor:"pointer",padding:"2px"},title:"Edit dependent details",children:(0,o.jsx)(R.Z,{style:{width:"14px",height:"14px",color:"#6b7280"}})})]}),(0,o.jsxs)("p",{style:{fontSize:"14px",color:"#6b7280",margin:"4px 0 0 0"},children:[e.relationship,e.dateOfBirth&&" • Born ".concat(new Date(e.dateOfBirth).toLocaleDateString()),e.gender&&" • ".concat(e.gender)]})]})]})})]},e._id))]}),m&&(0,o.jsx)("div",{style:{position:"fixed",top:0,left:0,right:0,bottom:0,backgroundColor:"rgba(0, 0, 0, 0.5)",display:"flex",alignItems:"center",justifyContent:"center",zIndex:1e3},children:(0,o.jsxs)("div",{style:{backgroundColor:"white",borderRadius:"12px",padding:"24px",width:"90%",maxWidth:"400px",boxShadow:"0 20px 25px -5px rgba(0, 0, 0, 0.1)",position:"relative"},children:[(0,o.jsx)("button",{onClick:er,style:{position:"absolute",top:"16px",right:"16px",background:"none",border:"none",cursor:"pointer",padding:"4px",borderRadius:"4px"},children:(0,o.jsx)(b.Z,{size:20,style:{color:"#6b7280"}})}),(0,o.jsx)("h3",{style:{fontSize:"18px",fontWeight:"600",color:"#111827",margin:"0 0 20px 0"},children:"Add Family Member"}),(0,o.jsxs)("div",{style:{display:"grid",gridTemplateColumns:"1fr 1fr 1fr",gap:"12px",marginBottom:"16px"},children:[(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{style:{display:"block",fontSize:"14px",fontWeight:"500",color:"#374151",marginBottom:"6px"},children:"First Name *"}),(0,o.jsx)("input",{type:"text",value:L.firstName,onChange:e=>V(n=>({...n,firstName:e.target.value})),placeholder:"First name",style:{width:"100%",padding:"10px 12px",border:"1px solid #d1d5db",borderRadius:"6px",fontSize:"14px",outline:"none",boxSizing:"border-box",backgroundColor:"white",color:"#111827"},onFocus:e=>e.target.style.borderColor="#3b82f6",onBlur:e=>e.target.style.borderColor="#d1d5db"})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{style:{display:"block",fontSize:"14px",fontWeight:"500",color:"#374151",marginBottom:"6px"},children:"Middle Name"}),(0,o.jsx)("input",{type:"text",value:L.middleName,onChange:e=>V(n=>({...n,middleName:e.target.value})),placeholder:"Middle name",style:{width:"100%",padding:"10px 12px",border:"1px solid #d1d5db",borderRadius:"6px",fontSize:"14px",outline:"none",boxSizing:"border-box",backgroundColor:"white",color:"#111827"},onFocus:e=>e.target.style.borderColor="#3b82f6",onBlur:e=>e.target.style.borderColor="#d1d5db"})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{style:{display:"block",fontSize:"14px",fontWeight:"500",color:"#374151",marginBottom:"6px"},children:"Last Name *"}),(0,o.jsx)("input",{type:"text",value:L.lastName,onChange:e=>V(n=>({...n,lastName:e.target.value})),placeholder:"Last name",style:{width:"100%",padding:"10px 12px",border:"1px solid #d1d5db",borderRadius:"6px",fontSize:"14px",outline:"none",boxSizing:"border-box",backgroundColor:"white",color:"#111827"},onFocus:e=>e.target.style.borderColor="#3b82f6",onBlur:e=>e.target.style.borderColor="#d1d5db"})]})]}),(0,o.jsxs)("div",{style:{marginBottom:"16px"},children:[(0,o.jsx)("label",{style:{display:"block",fontSize:"14px",fontWeight:"500",color:"#374151",marginBottom:"6px"},children:"Relationship *"}),(0,o.jsxs)("select",{value:L.relationship,onChange:e=>V(n=>({...n,relationship:e.target.value})),style:{width:"100%",padding:"10px 12px",border:"1px solid #d1d5db",borderRadius:"6px",fontSize:"14px",outline:"none",backgroundColor:"white",color:"#111827",boxSizing:"border-box"},children:[(0,o.jsx)("option",{value:"Spouse",children:"Spouse"}),(0,o.jsx)("option",{value:"Domestic Partner",children:"Domestic Partner"}),(0,o.jsx)("option",{value:"Child",children:"Child"}),(0,o.jsx)("option",{value:"Stepchild",children:"Stepchild"}),(0,o.jsx)("option",{value:"Adopted Child",children:"Adopted Child"})]})]}),(0,o.jsxs)("div",{style:{marginBottom:"16px"},children:[(0,o.jsx)("label",{style:{display:"block",fontSize:"14px",fontWeight:"500",color:"#374151",marginBottom:"6px"},children:"Gender *"}),(0,o.jsxs)("select",{value:L.gender,onChange:e=>V(n=>({...n,gender:e.target.value})),style:{width:"100%",padding:"10px 12px",border:"1px solid #d1d5db",borderRadius:"6px",fontSize:"14px",outline:"none",backgroundColor:"white",color:"#111827",boxSizing:"border-box"},children:[(0,o.jsx)("option",{value:"Male",children:"Male"}),(0,o.jsx)("option",{value:"Female",children:"Female"}),(0,o.jsx)("option",{value:"Other",children:"Other"}),(0,o.jsx)("option",{value:"Prefer not to say",children:"Prefer not to say"})]})]}),(0,o.jsxs)("div",{style:{marginBottom:"24px"},children:[(0,o.jsx)("label",{style:{display:"block",fontSize:"14px",fontWeight:"500",color:"#374151",marginBottom:"6px"},children:"Date of Birth *"}),(0,o.jsx)("input",{type:"date",value:L.dateOfBirth,onChange:e=>V(n=>({...n,dateOfBirth:e.target.value})),style:{width:"100%",padding:"10px 12px",border:"1px solid #d1d5db",borderRadius:"6px",fontSize:"14px",outline:"none",boxSizing:"border-box",backgroundColor:"white",color:"#111827",colorScheme:"light",WebkitAppearance:"none",MozAppearance:"textfield"},onFocus:e=>e.target.style.borderColor="#3b82f6",onBlur:e=>e.target.style.borderColor="#d1d5db"})]}),(0,o.jsxs)("div",{style:{marginBottom:"24px"},children:[(0,o.jsx)("label",{style:{display:"block",fontSize:"14px",fontWeight:"500",color:"#374151",marginBottom:"6px"},children:"Social Security Number (Optional)"}),(0,o.jsx)("input",{type:"text",value:L.ssn,onChange:e=>V(n=>({...n,ssn:e.target.value})),placeholder:"XXX-XX-XXXX",maxLength:11,style:{width:"100%",padding:"10px 12px",border:"1px solid #d1d5db",borderRadius:"6px",fontSize:"14px",outline:"none",boxSizing:"border-box",backgroundColor:"white",color:"#111827"},onFocus:e=>e.target.style.borderColor="#3b82f6",onBlur:e=>e.target.style.borderColor="#d1d5db"})]}),(0,o.jsxs)("div",{style:{display:"flex",gap:"12px",justifyContent:"flex-end"},children:[(0,o.jsx)("button",{onClick:er,style:{padding:"10px 20px",backgroundColor:"white",border:"1px solid #d1d5db",borderRadius:"6px",color:"#374151",cursor:"pointer",fontSize:"14px",fontWeight:"500"},children:"Cancel"}),(0,o.jsx)("button",{onClick:et,disabled:!L.firstName.trim()||!L.lastName.trim()||!L.dateOfBirth||C,style:{padding:"10px 20px",backgroundColor:L.firstName.trim()&&L.lastName.trim()&&L.dateOfBirth&&!C?"#000000":"#d1d5db",border:"none",borderRadius:"6px",color:"white",cursor:L.firstName.trim()&&L.lastName.trim()&&L.dateOfBirth&&!C?"pointer":"not-allowed",fontSize:"14px",fontWeight:"500"},children:C?"Adding...":"Add Member"})]})]})}),v&&(0,o.jsx)("div",{style:{position:"fixed",top:0,left:0,right:0,bottom:0,backgroundColor:"rgba(0, 0, 0, 0.5)",display:"flex",alignItems:"center",justifyContent:"center",zIndex:1e3},children:(0,o.jsxs)("div",{style:{backgroundColor:"white",borderRadius:"12px",padding:"24px",width:"90%",maxWidth:"500px",maxHeight:"90vh",overflow:"auto",boxShadow:"0 20px 25px -5px rgba(0, 0, 0, 0.1)"},children:[(0,o.jsxs)("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",marginBottom:"20px"},children:[(0,o.jsx)("h3",{style:{fontSize:"18px",fontWeight:"600",color:"#111827",margin:0},children:"Edit Dependent Details"}),(0,o.jsx)("button",{onClick:()=>{D(!1),S(null)},style:{background:"none",border:"none",cursor:"pointer",padding:"4px"},children:(0,o.jsx)(b.Z,{style:{width:"20px",height:"20px",color:"#6b7280"}})})]}),(0,o.jsxs)("div",{style:{display:"flex",flexDirection:"column",gap:"16px"},children:[(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{style:{display:"block",fontSize:"14px",fontWeight:"500",color:"#374151",marginBottom:"6px"},children:"Full Name *"}),(0,o.jsx)("input",{type:"text",value:U.name,onChange:e=>_(n=>({...n,name:e.target.value})),placeholder:"Enter full name",style:{width:"100%",padding:"10px 12px",border:"1px solid #d1d5db",borderRadius:"6px",fontSize:"14px",outline:"none",boxSizing:"border-box",backgroundColor:"white",color:"#111827"}})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{style:{display:"block",fontSize:"14px",fontWeight:"500",color:"#374151",marginBottom:"6px"},children:"Relationship *"}),(0,o.jsxs)("select",{value:U.relationship,onChange:e=>_(n=>({...n,relationship:e.target.value})),style:{width:"100%",padding:"10px 12px",border:"1px solid #d1d5db",borderRadius:"6px",fontSize:"14px",outline:"none",boxSizing:"border-box",backgroundColor:"white",color:"#111827"},children:[(0,o.jsx)("option",{value:"Spouse",children:"Spouse"}),(0,o.jsx)("option",{value:"Domestic Partner",children:"Domestic Partner"}),(0,o.jsx)("option",{value:"Child",children:"Child"}),(0,o.jsx)("option",{value:"Stepchild",children:"Stepchild"}),(0,o.jsx)("option",{value:"Adopted Child",children:"Adopted Child"})]})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{style:{display:"block",fontSize:"14px",fontWeight:"500",color:"#374151",marginBottom:"6px"},children:"Date of Birth"}),(0,o.jsx)("input",{type:"date",value:U.dateOfBirth,onChange:e=>_(n=>({...n,dateOfBirth:e.target.value})),style:{width:"100%",padding:"10px 12px",border:"1px solid #d1d5db",borderRadius:"6px",fontSize:"14px",outline:"none",boxSizing:"border-box",backgroundColor:"white",color:"#111827"}})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{style:{display:"block",fontSize:"14px",fontWeight:"500",color:"#374151",marginBottom:"6px"},children:"Gender"}),(0,o.jsxs)("select",{value:U.gender,onChange:e=>_(n=>({...n,gender:e.target.value})),style:{width:"100%",padding:"10px 12px",border:"1px solid #d1d5db",borderRadius:"6px",fontSize:"14px",outline:"none",boxSizing:"border-box",backgroundColor:"white",color:"#111827"},children:[(0,o.jsx)("option",{value:"Male",children:"Male"}),(0,o.jsx)("option",{value:"Female",children:"Female"}),(0,o.jsx)("option",{value:"Other",children:"Other"}),(0,o.jsx)("option",{value:"Prefer not to say",children:"Prefer not to say"})]})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{style:{display:"block",fontSize:"14px",fontWeight:"500",color:"#374151",marginBottom:"6px"},children:"Social Security Number (Optional)"}),(0,o.jsx)("input",{type:"text",value:U.ssn,onChange:e=>_(n=>({...n,ssn:e.target.value})),placeholder:"XXX-XX-XXXX",maxLength:11,style:{width:"100%",padding:"10px 12px",border:"1px solid #d1d5db",borderRadius:"6px",fontSize:"14px",outline:"none",boxSizing:"border-box",backgroundColor:"white",color:"#111827"}})]})]}),(0,o.jsxs)("div",{style:{display:"flex",gap:"12px",marginTop:"24px",justifyContent:"flex-end"},children:[(0,o.jsx)("button",{onClick:()=>{D(!1),S(null)},style:{padding:"10px 20px",backgroundColor:"white",border:"1px solid #d1d5db",borderRadius:"6px",fontSize:"14px",fontWeight:"500",color:"#374151",cursor:"pointer"},disabled:C,children:"Cancel"}),(0,o.jsx)("button",{onClick:en,style:{padding:"10px 20px",backgroundColor:"#111827",border:"none",borderRadius:"6px",fontSize:"14px",fontWeight:"500",color:"white",cursor:C?"not-allowed":"pointer",opacity:C?.6:1},disabled:C,children:C?"Updating...":"Update Dependent"})]})]})}),k&&(0,o.jsx)(O.Z,{open:k,onClose:async()=>{I(!1),await K(),s&&s()}}),(0,o.jsx)(M,{isOpen:Z,onClose:()=>q(!1),onConfirm:$.onConfirm,title:$.title,message:$.message,type:$.type,confirmText:$.confirmText,cancelText:$.cancelText})]})},U=t(59701);let _=e=>{let{plans:n,selectedPlans:t=[],onClose:r}=e,[i,a]=(0,l.useState)(!0),[s,d]=(0,l.useState)(t.slice(0,3)),c=()=>{a(!1),r&&r()},p=n.length>0?n:[{id:"1",name:"HealthFirst PPO Gold",type:"medical",tier:"Gold",monthlyPremium:450,deductible:1e3,outOfPocketMax:5e3,features:["Nationwide network","No referrals needed","Prescription coverage","Mental health coverage"],network:"PPO Network"},{id:"2",name:"HealthFirst HMO Silver",type:"medical",tier:"Silver",monthlyPremium:320,deductible:2e3,outOfPocketMax:7e3,features:["Local network","Primary care referrals","Prescription coverage","Preventive care"],network:"HMO Network"},{id:"3",name:"DentalCare Plus",type:"dental",tier:"Gold",monthlyPremium:45,deductible:50,outOfPocketMax:1500,features:["Cleanings covered 100%","Major work 50% coverage","Orthodontics included","No waiting period"],network:"Dental Network"}],u=p.filter(e=>s.includes(e.id)),g=e=>{s.includes(e)?d(n=>n.filter(n=>n!==e)):s.length<3&&d(n=>[...n,e])};return i?(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)("div",{style:{position:"fixed",inset:0,backgroundColor:"rgba(0, 0, 0, 0.5)",zIndex:50},onClick:c}),(0,o.jsxs)("div",{style:{position:"fixed",top:"50%",left:"50%",transform:"translate(-50%, -50%)",width:"95vw",maxWidth:"1200px",maxHeight:"90vh",backgroundColor:"white",borderRadius:"16px",boxShadow:"0 25px 50px rgba(0, 0, 0, 0.25)",zIndex:51,overflow:"hidden"},children:[(0,o.jsxs)("div",{style:{display:"flex",alignItems:"center",justifyContent:"space-between",padding:"20px 24px",borderBottom:"1px solid #e5e7eb"},children:[(0,o.jsxs)("div",{style:{display:"flex",alignItems:"center",gap:"8px"},children:[(0,o.jsx)(U.Z,{size:20,style:{color:"#2563eb"}}),(0,o.jsx)("h2",{style:{fontSize:"18px",fontWeight:"600",color:"#111827",margin:0,fontFamily:"sans-serif"},children:"Compare Plans"})]}),(0,o.jsx)("button",{onClick:c,style:{padding:"8px",backgroundColor:"transparent",border:"none",borderRadius:"8px",cursor:"pointer",color:"#6b7280"},children:(0,o.jsx)(b.Z,{size:20})})]}),(0,o.jsxs)("div",{style:{padding:"16px 24px",backgroundColor:"#f9fafb",borderBottom:"1px solid #e5e7eb"},children:[(0,o.jsxs)("p",{style:{fontSize:"14px",color:"#6b7280",margin:"0 0 12px 0",fontFamily:"sans-serif"},children:["Select up to 3 plans to compare (",s.length,"/3 selected):"]}),(0,o.jsx)("div",{style:{display:"flex",gap:"8px",flexWrap:"wrap"},children:p.map(e=>(0,o.jsx)("button",{onClick:()=>g(e.id),disabled:!s.includes(e.id)&&s.length>=3,style:{padding:"6px 12px",backgroundColor:s.includes(e.id)?"#2563eb":"white",color:s.includes(e.id)?"white":"#374151",border:"1px solid #d1d5db",borderRadius:"6px",fontSize:"12px",fontWeight:"500",cursor:s.includes(e.id)||s.length<3?"pointer":"not-allowed",opacity:!s.includes(e.id)&&s.length>=3?.5:1,fontFamily:"sans-serif"},children:e.name},e.id))})]}),(0,o.jsx)("div",{style:{padding:"24px",overflowX:"auto",maxHeight:"500px",overflowY:"auto"},children:0===u.length?(0,o.jsxs)("div",{style:{textAlign:"center",padding:"40px",color:"#6b7280"},children:[(0,o.jsx)(U.Z,{size:48,style:{margin:"0 auto 16px",opacity:.5}}),(0,o.jsx)("p",{style:{fontSize:"16px",fontWeight:"500",margin:"0 0 8px 0",fontFamily:"sans-serif"},children:"Select plans to compare"}),(0,o.jsx)("p",{style:{fontSize:"14px",margin:0,fontFamily:"sans-serif"},children:"Choose up to 3 plans from the list above to see a detailed comparison."})]}):(0,o.jsx)("div",{style:{minWidth:"600px"},children:(0,o.jsxs)("table",{style:{width:"100%",borderCollapse:"collapse"},children:[(0,o.jsx)("thead",{children:(0,o.jsxs)("tr",{children:[(0,o.jsx)("th",{style:{padding:"12px",textAlign:"left",borderBottom:"2px solid #e5e7eb",fontSize:"14px",fontWeight:"600",color:"#111827",fontFamily:"sans-serif"},children:"Feature"}),u.map(e=>(0,o.jsx)("th",{style:{padding:"12px",textAlign:"center",borderBottom:"2px solid #e5e7eb",fontSize:"14px",fontWeight:"600",color:"#111827",fontFamily:"sans-serif"},children:e.name},e.id))]})}),(0,o.jsxs)("tbody",{children:[(0,o.jsxs)("tr",{children:[(0,o.jsx)("td",{style:{padding:"12px",borderBottom:"1px solid #e5e7eb",fontSize:"14px",fontWeight:"500",color:"#374151",fontFamily:"sans-serif"},children:"Monthly Premium"}),u.map(e=>(0,o.jsxs)("td",{style:{padding:"12px",textAlign:"center",borderBottom:"1px solid #e5e7eb",fontSize:"14px",color:"#111827",fontFamily:"sans-serif"},children:["$",e.monthlyPremium]},e.id))]}),(0,o.jsxs)("tr",{children:[(0,o.jsx)("td",{style:{padding:"12px",borderBottom:"1px solid #e5e7eb",fontSize:"14px",fontWeight:"500",color:"#374151",fontFamily:"sans-serif"},children:"Deductible"}),u.map(e=>(0,o.jsxs)("td",{style:{padding:"12px",textAlign:"center",borderBottom:"1px solid #e5e7eb",fontSize:"14px",color:"#111827",fontFamily:"sans-serif"},children:["$",e.deductible.toLocaleString()]},e.id))]}),(0,o.jsxs)("tr",{children:[(0,o.jsx)("td",{style:{padding:"12px",borderBottom:"1px solid #e5e7eb",fontSize:"14px",fontWeight:"500",color:"#374151",fontFamily:"sans-serif"},children:"Out-of-Pocket Max"}),u.map(e=>(0,o.jsxs)("td",{style:{padding:"12px",textAlign:"center",borderBottom:"1px solid #e5e7eb",fontSize:"14px",color:"#111827",fontFamily:"sans-serif"},children:["$",e.outOfPocketMax.toLocaleString()]},e.id))]}),(0,o.jsxs)("tr",{children:[(0,o.jsx)("td",{style:{padding:"12px",borderBottom:"1px solid #e5e7eb",fontSize:"14px",fontWeight:"500",color:"#374151",fontFamily:"sans-serif"},children:"Network"}),u.map(e=>(0,o.jsx)("td",{style:{padding:"12px",textAlign:"center",borderBottom:"1px solid #e5e7eb",fontSize:"14px",color:"#111827",fontFamily:"sans-serif"},children:e.network},e.id))]})]})]})})})]})]}):(0,o.jsxs)("button",{onClick:()=>a(!0),style:{display:"flex",alignItems:"center",gap:"8px",padding:"8px 16px",color:"#374151",border:"1px solid #d1d5db",borderRadius:"8px",backgroundColor:"white",cursor:"pointer",transition:"background-color 0.2s ease",fontSize:"14px",fontWeight:"500",fontFamily:"sans-serif"},onMouseOver:e=>e.currentTarget.style.backgroundColor="#f9fafb",onMouseOut:e=>e.currentTarget.style.backgroundColor="white",children:[(0,o.jsx)(U.Z,{size:16,style:{color:"#6b7280"}}),"Compare Plans"]})};var Z=e=>{var n,t,r;let{planName:i,availableTiers:a,preferredTier:s,onTierSelect:d,selectedTier:c,category:p}=e,[u,g]=(0,l.useState)(""),[x,f]=(0,l.useState)(!1),h=a.some(e=>e.tierName===s),m=h?s:(null===(n=a.find(e=>"Employee Only"===e.tierName))||void 0===n?void 0:n.tierName)||(null===(t=a[0])||void 0===t?void 0:t.tierName)||"";(0,l.useEffect)(()=>{if(c)g(c);else if(m){g(m);let e=a.find(e=>e.tierName===m);e&&d(e)}!h&&s&&a.length>0&&f(!0)},[c,m,h,s,a,d]);let y=e=>{g(e),d(a.find(n=>n.tierName===e)||null),f(!1)};return 0===a.length?(0,o.jsx)("div",{style:{backgroundColor:"#fef2f2",border:"1px solid #fecaca",borderRadius:"8px",padding:"16px",marginTop:"12px"},children:(0,o.jsx)("p",{style:{color:"#dc2626",margin:0,fontSize:"14px"},children:"No coverage tiers available for this plan."})}):(0,o.jsxs)("div",{style:{marginTop:"16px"},children:[x&&(0,o.jsx)("div",{style:{backgroundColor:"#fef3c7",border:"1px solid #fbbf24",borderRadius:"8px",padding:"12px",marginBottom:"16px"},children:(0,o.jsxs)("p",{style:{color:"#92400e",margin:0,fontSize:"14px",fontWeight:"500"},children:["⚠️ Your preferred coverage tier “",s,"” is not available for this ",p.toLowerCase()," plan. Please select from the available options below:"]})}),(0,o.jsxs)("div",{style:{backgroundColor:"white",border:"1px solid #e5e7eb",borderRadius:"12px",padding:"20px",boxShadow:"0 1px 3px rgba(0, 0, 0, 0.1)"},children:[(0,o.jsxs)("h4",{style:{fontSize:"16px",fontWeight:"600",color:"#111827",margin:"0 0 16px 0",display:"flex",alignItems:"center",gap:"8px"},children:[(0,o.jsx)("span",{style:{width:"24px",height:"24px",backgroundColor:"#3b82f6",borderRadius:"6px",display:"flex",alignItems:"center",justifyContent:"center",fontSize:"12px",color:"white",fontWeight:"700"},children:"\uD83D\uDCB0"}),"Choose Coverage for ",i]}),(0,o.jsx)("div",{style:{display:"flex",flexDirection:"column",gap:"12px"},children:a.map(e=>{let n=e.tierName===s,t=u===e.tierName;return(0,o.jsxs)("div",{onClick:()=>y(e.tierName),style:{position:"relative",display:"flex",alignItems:"center",justifyContent:"space-between",padding:"16px 20px",backgroundColor:t?"#f0f9ff":"white",border:t?"2px solid #0ea5e9":n?"2px solid #10b981":"1px solid #e5e7eb",borderRadius:"12px",cursor:"pointer",transition:"all 0.3s ease",boxShadow:t?"0 4px 12px rgba(14, 165, 233, 0.15)":n?"0 4px 12px rgba(16, 185, 129, 0.15)":"0 1px 3px rgba(0, 0, 0, 0.05)",transform:t?"translateY(-2px)":"none"},onMouseEnter:e=>{t||(e.currentTarget.style.backgroundColor="#f8fafc",e.currentTarget.style.transform="translateY(-1px)",e.currentTarget.style.boxShadow="0 4px 12px rgba(0, 0, 0, 0.1)")},onMouseLeave:e=>{t||(e.currentTarget.style.backgroundColor="white",e.currentTarget.style.transform="none",e.currentTarget.style.boxShadow="0 1px 3px rgba(0, 0, 0, 0.05)")},children:[n&&(0,o.jsx)("div",{style:{position:"absolute",top:"-8px",right:"16px",backgroundColor:"#10b981",color:"white",fontSize:"11px",fontWeight:"600",padding:"4px 8px",borderRadius:"6px",textTransform:"uppercase",letterSpacing:"0.5px"},children:"⭐ Recommended"}),t&&(0,o.jsx)("div",{style:{position:"absolute",top:"12px",right:"12px",width:"24px",height:"24px",backgroundColor:"#0ea5e9",borderRadius:"50%",display:"flex",alignItems:"center",justifyContent:"center",color:"white",fontSize:"14px",fontWeight:"700"},children:"✓"}),(0,o.jsxs)("div",{style:{display:"flex",alignItems:"center",gap:"16px",flex:1},children:[(0,o.jsx)("div",{style:{width:"20px",height:"20px",borderRadius:"50%",border:t?"6px solid #0ea5e9":"2px solid #d1d5db",backgroundColor:t?"white":"transparent",transition:"all 0.2s ease"}}),(0,o.jsxs)("div",{style:{flex:1},children:[(0,o.jsx)("div",{style:{fontSize:"16px",fontWeight:"600",color:t?"#0ea5e9":"#111827",marginBottom:"4px"},children:e.tierName}),(0,o.jsxs)("div",{style:{fontSize:"13px",color:"#6b7280",display:"flex",gap:"16px"},children:[(0,o.jsxs)("span",{children:["Total: $",e.totalCost.toFixed(2)]}),(0,o.jsxs)("span",{children:["Employer: $",e.employerCost.toFixed(2)]})]})]})]}),(0,o.jsxs)("div",{style:{textAlign:"right",marginRight:t?"32px":"0"},children:[(0,o.jsxs)("div",{style:{fontSize:"20px",fontWeight:"700",color:t?"#0ea5e9":"#111827",marginBottom:"2px"},children:["$",e.employeeCost.toFixed(2)]}),(0,o.jsx)("div",{style:{fontSize:"12px",color:"#6b7280",fontWeight:"500"},children:"per paycheck"})]})]},e.tierName)})}),u&&(0,o.jsx)("div",{style:{marginTop:"16px",padding:"16px",background:"linear-gradient(135deg, #ecfdf5 0%, #f0fdf4 100%)",border:"1px solid #10b981",borderRadius:"12px",boxShadow:"0 2px 8px rgba(16, 185, 129, 0.1)"},children:(0,o.jsxs)("div",{style:{display:"flex",alignItems:"center",gap:"12px"},children:[(0,o.jsx)("div",{style:{width:"32px",height:"32px",backgroundColor:"#10b981",borderRadius:"50%",display:"flex",alignItems:"center",justifyContent:"center",color:"white",fontSize:"16px",fontWeight:"700"},children:"✓"}),(0,o.jsxs)("div",{style:{flex:1},children:[(0,o.jsxs)("div",{style:{fontSize:"16px",fontWeight:"600",color:"#065f46",marginBottom:"4px"},children:[u," Selected"]}),(0,o.jsxs)("div",{style:{fontSize:"14px",color:"#047857",fontWeight:"500"},children:["You pay $",null===(r=a.find(e=>e.tierName===u))||void 0===r?void 0:r.employeeCost.toFixed(2)," per paycheck"]})]})]})})]})]})};let q={SELECTED_PLANS:"selectedPlansData",WAIVED_COVERAGES:"waivedCoveragesData",PREFERRED_TIER:"selectedCoverageTier",ENROLLMENT_SUMMARY:"enrollmentSummarySnapshot"},$=e=>{try{let n=[...Y().filter(n=>n.category!==e.category),e];localStorage.setItem(q.SELECTED_PLANS,JSON.stringify(n));let t="selected".concat(e.category.replace(/\s+/g,""),"Plan");localStorage.setItem(t,JSON.stringify({id:e.planId,name:e.planName,cost:e.selectedTier.employeeCost,selectedTier:e.selectedTier,originalPlan:e.originalPlan})),console.log("\uD83D\uDCE6 Stored plan for ".concat(e.category,":"),e)}catch(e){console.error("Error storing selected plan:",e)}},Y=()=>{try{let e=localStorage.getItem(q.SELECTED_PLANS);return e?JSON.parse(e):[]}catch(e){return console.error("Error getting stored plans:",e),[]}},J=e=>Y().find(n=>n.category===e)||null,G=e=>{try{let n=[...X().filter(n=>n.category!==e.category),e];localStorage.setItem(q.WAIVED_COVERAGES,JSON.stringify(n));let t="".concat(e.category.toLowerCase(),"Waived"),o="".concat(e.category.toLowerCase(),"WaiveReason");localStorage.setItem(t,"true"),localStorage.setItem(o,e.waiveReason),Q(e.category),console.log("\uD83D\uDEAB Stored waive for ".concat(e.category,":"),e)}catch(e){console.error("Error storing waived coverage:",e)}},X=()=>{try{let e=localStorage.getItem(q.WAIVED_COVERAGES);return e?JSON.parse(e):[]}catch(e){return console.error("Error getting stored waives:",e),[]}},K=e=>X().some(n=>n.category===e),Q=e=>{try{let n=Y().filter(n=>n.category!==e);localStorage.setItem(q.SELECTED_PLANS,JSON.stringify(n));let t="selected".concat(e.replace(/\s+/g,""),"Plan");localStorage.removeItem(t),console.log("\uD83D\uDDD1️ Removed plan for ".concat(e))}catch(e){console.error("Error removing selected plan:",e)}},ee=e=>{try{let n=X().filter(n=>n.category!==e);localStorage.setItem(q.WAIVED_COVERAGES,JSON.stringify(n));let t="".concat(e.toLowerCase(),"Waived"),o="".concat(e.toLowerCase(),"WaiveReason");localStorage.removeItem(t),localStorage.removeItem(o),console.log("\uD83D\uDDD1️ Removed waive for ".concat(e))}catch(e){console.error("Error removing waived coverage:",e)}},en=()=>localStorage.getItem(q.PREFERRED_TIER)||"Employee Only",et=()=>{let e=Y(),n=X(),t={selectedCoverageTier:en(),enrollmentDate:new Date().toISOString(),dependents:[],selectedPlans:e,waivedCoverages:n};return e.forEach(e=>{let n=e.category.toLowerCase().replace(/\s+/g,"");t["".concat(n,"Plan")]={id:e.planId,name:e.planName,planName:e.planName,cost:e.selectedTier.employeeCost,selectedTier:e.selectedTier,originalPlan:e.originalPlan}}),n.forEach(e=>{let n=e.category.toLowerCase().replace(/\s+/g,"");t["".concat(n,"Waived")]=!0,t["".concat(n,"WaiveReason")]=e.waiveReason}),t},eo=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n={...et(),...e,snapshotDate:new Date().toISOString()};return localStorage.setItem(q.ENROLLMENT_SUMMARY,JSON.stringify(n)),console.log("\uD83D\uDCF8 Created enrollment snapshot:",n),n},el=[{primary:"#1E90FF",accent:"#E6F0FF",name:"Blue"},{primary:"#32CD32",accent:"#E6FFE6",name:"Green"},{primary:"#FF8C00",accent:"#FFF3E6",name:"Orange"},{primary:"#800080",accent:"#F5E6FF",name:"Purple"}];var er=e=>{var n,t;let{category:r,plans:i,selectedCoverageTier:a,themeIndex:s,onPlanSelect:d}=e,[c,p]=(0,l.useState)(null),[u,x]=(0,l.useState)(null),[f,y]=(0,l.useState)(!1),[b,v]=(0,l.useState)(!1),[D,j]=(0,l.useState)(!1),[C,w]=(0,l.useState)(""),[k,I]=(0,l.useState)(!1),P=en(),[W,E]=(0,l.useState)(!1),[T,z]=(0,l.useState)({title:"",message:"",type:"alert",onConfirm:()=>{},confirmText:"OK",cancelText:"Cancel"}),R=el[s%el.length],A=e=>({Dental:"\uD83E\uDDB7",Vision:"\uD83D\uDC41️","Life Insurance":"\uD83D\uDEE1️","AD&D Insurance":"⚡",Medical:"\uD83C\uDFE5","Disability Insurance":"\uD83E\uDD1D","Voluntary Benefits":"\uD83D\uDCCB"})[e]||"\uD83D\uDCC4",F=e=>{if(c===e){p(null),x(null),Q(r),ee(r),d(null);return}p(e),x(null),ee(r)},B=(i||[]).map(e=>({id:e.id||e._id,name:e.name||e.planName,cost:e.cost||0,features:e.features||[],recommended:e.recommended||!1,originalPlan:e,coverageTiers:e.coverageTiers||[]})),N=B.filter(e=>e.coverageTiers&&0!==e.coverageTiers.length?(e.coverageTiers.some(e=>e.tierName===P)?console.log("✅ Plan ".concat(e.name," has preferred tier ").concat(P)):console.log("⚠️ Plan ".concat(e.name," does not have preferred tier ").concat(P,", but has other tiers:"),e.coverageTiers.map(e=>e.tierName)),!0):(console.log("⚠️ Plan ".concat(e.name," has no coverage tiers, excluding from display")),!1));return(0,l.useEffect)(()=>{let e=J(r);if(K(r))p("WAIVE");else if(e)p(e.planId),x(e.selectedTier);else{let e="selected".concat(r.replace(/\s+/g,""),"Plan"),n="".concat(r.toLowerCase(),"Waived");if("true"===localStorage.getItem(n))p("WAIVE");else{let n=localStorage.getItem(e);if(n)try{let e=JSON.parse(n);p(e.id)}catch(e){console.error("Error parsing saved ".concat(r," plan:"),e)}}}},[r]),(0,o.jsxs)("div",{style:{display:"flex",flexDirection:"column",gap:"24px",fontFamily:'"SF Pro", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif'},children:[(0,o.jsxs)("div",{style:{display:"flex",gap:"16px"},children:[(0,o.jsx)("div",{style:{width:"40px",height:"40px",borderRadius:"50%",display:"flex",alignItems:"center",justifyContent:"center",flexShrink:0,overflow:"hidden"},children:(0,o.jsx)(h.default,{src:"/brea.png",alt:"Brea - AI Benefits Assistant",width:40,height:40,style:{borderRadius:"50%"}})}),(0,o.jsxs)("div",{style:{backgroundColor:"#f9fafb",borderRadius:"8px",padding:"16px",maxWidth:"1000px"},children:[(0,o.jsx)("p",{style:{color:"#374151",lineHeight:"1.6",margin:0},children:{Dental:"\uD83D\uDE01 Now let's take care of your smile! Which dental plan works best for you?",Vision:"\uD83D\uDC41️ Let's help you see clearly! Which vision plan fits your needs?","Life Insurance":"\uD83D\uDEE1️ Let's protect your family's future! Which life insurance plan is right for you?","AD&D Insurance":"⚡ Additional protection for unexpected events. Which AD&D plan would you like?",Medical:"\uD83C\uDFE5 Let's find the perfect medical plan for your healthcare needs!","Disability Insurance":"\uD83E\uDD1D Protect your income with disability coverage. Which plan works for you?","Voluntary Benefits":"\uD83D\uDCCB Explore additional voluntary benefits to enhance your coverage."}[r]||"Let's choose your ".concat(r.toLowerCase()," plan!")}),(0,o.jsx)("p",{style:{color:"#374151",lineHeight:"1.6",margin:"8px 0 0 0"},children:{Dental:"Even if you don't need dental care now, having coverage can save you money later.",Vision:"Regular eye exams and vision care are important for your overall health.","Life Insurance":"Life insurance provides financial security for your loved ones.","AD&D Insurance":"Accidental Death & Dismemberment insurance provides additional protection.",Medical:"Choose a plan that fits your healthcare needs and budget.","Disability Insurance":"Disability insurance helps replace income if you can't work due to illness or injury.","Voluntary Benefits":"These optional benefits can provide additional peace of mind."}[r]||"Select the ".concat(r.toLowerCase()," coverage that best fits your needs.")})]})]}),(0,o.jsxs)("div",{style:{backgroundColor:"white",borderRadius:"8px",border:"1px solid #e5e7eb",padding:"24px",boxShadow:"0 1px 3px 0 rgba(0, 0, 0, 0.1)"},children:[(0,o.jsxs)("div",{style:{display:"flex",alignItems:"center",gap:"8px",marginBottom:"16px"},children:[(0,o.jsx)("span",{style:{fontSize:"18px"},children:A(r)}),(0,o.jsxs)("h2",{style:{fontSize:"20px",fontWeight:"600",color:"#111827",margin:0},children:["Smart ",r," Plan Selection"]})]}),(0,o.jsxs)("p",{style:{color:"#6b7280",marginBottom:"24px",margin:0},children:["Now let's find your perfect ",r.toLowerCase()," plan:"]}),(0,o.jsx)("div",{style:{display:"flex",flexDirection:"column",gap:"16px"},children:0===N.length?(0,o.jsxs)("div",{style:{textAlign:"center",padding:"40px 20px",backgroundColor:"#f9fafb",borderRadius:"8px",border:"2px dashed #e5e7eb"},children:[(0,o.jsx)("div",{style:{fontSize:"48px",marginBottom:"16px"},children:A(r)}),(0,o.jsxs)("h3",{style:{fontSize:"18px",fontWeight:"600",color:"#374151",margin:"0 0 8px 0"},children:["No ",r," Plans Available"]}),(0,o.jsxs)("p",{style:{color:"#6b7280",fontSize:"14px",margin:0,lineHeight:"1.5"},children:[0===B.length?"Your company hasn't set up any ".concat(r.toLowerCase()," plan assignments yet."):"No ".concat(r.toLowerCase()," plans are available with proper coverage tier configurations."),(0,o.jsx)("br",{}),"Please contact your HR administrator for more information."]})]}):(0,o.jsxs)(o.Fragment,{children:[N.map(e=>{let n=c===e.id;return(0,o.jsxs)("div",{style:{border:n?"2px solid ".concat(R.primary):e.recommended?"2px solid #f59e0b":"2px solid #e5e7eb",borderRadius:"8px",padding:"20px",backgroundColor:n?R.accent:e.recommended?"#fef3e2":"#f9fafb",cursor:"pointer",transition:"all 0.2s ease"},onClick:()=>F(e.id),children:[(0,o.jsxs)("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"flex-start",marginBottom:"12px"},children:[(0,o.jsxs)("div",{children:[(0,o.jsx)("h3",{style:{fontSize:"18px",fontWeight:"600",color:"#111827",margin:0},children:e.name}),(0,o.jsx)("div",{style:{display:"flex",flexDirection:"column",alignItems:"flex-start",marginTop:"4px"},children:(()=>{var n,t;let l=null===(n=e.coverageTiers)||void 0===n?void 0:n.find(e=>e.tierName===P),r=l?l.employeeCost:e.cost,i=!!l;return(0,o.jsxs)("div",{children:[(0,o.jsxs)("div",{style:{display:"flex",alignItems:"baseline",gap:"4px"},children:[(0,o.jsxs)("span",{style:{fontSize:"24px",fontWeight:"700",color:i?"#111827":"#6b7280"},children:["$",r.toFixed(2)]}),(0,o.jsx)("span",{style:{color:"#6b7280",fontSize:"14px"},children:"/paycheck"})]}),i&&(0,o.jsxs)("div",{style:{fontSize:"12px",color:"#10b981",fontWeight:"500",marginTop:"2px"},children:["✓ ",P," available"]}),!i&&(null===(t=e.coverageTiers)||void 0===t?void 0:t.length)>0&&(0,o.jsx)("div",{style:{fontSize:"11px",color:"#f59e0b",fontWeight:"500",marginTop:"2px"},children:"Starting from (tier selection required)"})]})})()})]}),e.recommended&&(0,o.jsx)("div",{style:{backgroundColor:"#f59e0b",color:"white",padding:"4px 8px",borderRadius:"12px",fontSize:"12px",fontWeight:"500"},children:"Recommended"})]}),(0,o.jsx)("div",{style:{display:"flex",flexDirection:"column",gap:"8px",marginBottom:"16px"},children:e.features.map((e,n)=>(0,o.jsxs)("div",{style:{display:"flex",alignItems:"center",gap:"8px"},children:[(0,o.jsx)(g.Z,{style:{width:"16px",height:"16px",color:"#10b981",flexShrink:0}}),(0,o.jsx)("span",{style:{color:"#374151",fontSize:"14px"},children:e})]},n))}),(0,o.jsx)("button",{style:{width:"100%",backgroundColor:n?"#000000":"#f3f4f6",color:n?"white":"#6b7280",padding:"8px 16px",borderRadius:"6px",fontWeight:"500",border:"none",cursor:"pointer",fontSize:"14px"},children:n?"✓ Selected":"Select This Plan"})]},e.id)}),c&&"WAIVE"!==c&&(0,o.jsx)(Z,{planName:(null===(n=N.find(e=>e.id===c))||void 0===n?void 0:n.name)||"",availableTiers:(null===(t=N.find(e=>e.id===c))||void 0===t?void 0:t.coverageTiers)||[],preferredTier:P,onTierSelect:e=>{if(c&&"WAIVE"!==c&&(x(e),e)){let n=N.find(e=>e.id===c);n&&($({planId:c,planName:n.name,category:r,selectedTier:e,originalPlan:n.originalPlan,selectionDate:new Date().toISOString()}),d({...n,cost:e.employeeCost,selectedTier:e}))}},selectedTier:null==u?void 0:u.tierName,category:r}),(0,o.jsxs)("div",{style:{border:"WAIVE"===c?"2px solid #ef4444":"2px solid #e5e7eb",borderRadius:"8px",padding:"20px",backgroundColor:"WAIVE"===c?"#fef2f2":"#f9fafb",cursor:"pointer",transition:"all 0.2s ease"},onClick:()=>{if("WAIVE"===c){p(null),x(null),ee(r),d(null);return}j(!0)},children:[(0,o.jsxs)("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"flex-start",marginBottom:"12px"},children:[(0,o.jsxs)("div",{children:[(0,o.jsxs)("h3",{style:{fontSize:"18px",fontWeight:"600",color:"#111827",margin:0},children:["Waive ",r," Coverage"]}),(0,o.jsxs)("div",{style:{display:"flex",alignItems:"baseline",gap:"4px",marginTop:"4px"},children:[(0,o.jsx)("span",{style:{fontSize:"24px",fontWeight:"700",color:"#111827"},children:"$0.00"}),(0,o.jsx)("span",{style:{color:"#6b7280",fontSize:"14px"},children:"/paycheck"})]})]}),(0,o.jsx)("div",{style:{backgroundColor:"#ef4444",color:"white",padding:"4px 8px",borderRadius:"12px",fontSize:"12px",fontWeight:"500"},children:"No Coverage"})]}),(0,o.jsxs)("div",{style:{display:"flex",flexDirection:"column",gap:"8px",marginBottom:"16px"},children:[(0,o.jsxs)("div",{style:{display:"flex",alignItems:"center",gap:"8px"},children:[(0,o.jsx)("span",{style:{color:"#ef4444",fontSize:"16px"},children:"⚠️"}),(0,o.jsxs)("span",{style:{color:"#374151",fontSize:"14px"},children:["No ",r.toLowerCase()," coverage or benefits"]})]}),(0,o.jsxs)("div",{style:{display:"flex",alignItems:"center",gap:"8px"},children:[(0,o.jsx)("span",{style:{color:"#ef4444",fontSize:"16px"},children:"⚠️"}),(0,o.jsxs)("span",{style:{color:"#374151",fontSize:"14px"},children:["You'll pay full cost for ",r.toLowerCase()," care"]})]}),(0,o.jsxs)("div",{style:{display:"flex",alignItems:"center",gap:"8px"},children:[(0,o.jsx)("span",{style:{color:"#ef4444",fontSize:"16px"},children:"⚠️"}),(0,o.jsx)("span",{style:{color:"#374151",fontSize:"14px"},children:"Can only enroll during next open enrollment"})]})]}),(0,o.jsx)("button",{style:{width:"100%",backgroundColor:"WAIVE"===c?"#ef4444":"#f3f4f6",color:"WAIVE"===c?"white":"#6b7280",padding:"8px 16px",borderRadius:"6px",fontWeight:"500",border:"none",cursor:"pointer",fontSize:"14px"},children:"WAIVE"===c?"✓ Coverage Waived":"Waive Coverage"})]})]})}),c&&(0,o.jsxs)("div",{style:{backgroundColor:"WAIVE"===c?"#fef2f2":R.accent,border:"WAIVE"===c?"1px solid #fecaca":"1px solid ".concat(R.primary,"40"),borderRadius:"8px",padding:"16px",marginTop:"16px"},children:[(0,o.jsx)("h3",{style:{fontSize:"16px",fontWeight:"600",color:"WAIVE"===c?"#dc2626":R.primary,margin:"0 0 12px 0"},children:"WAIVE"===c?"".concat(r," Coverage Waived"):"Selected ".concat(r," Plan")}),(0,o.jsx)("div",{style:{display:"flex",flexDirection:"column",gap:"8px"},children:"WAIVE"===c?(0,o.jsxs)("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",backgroundColor:"white",padding:"12px",borderRadius:"6px"},children:[(0,o.jsxs)("span",{style:{fontWeight:"500",color:"#111827"},children:["No ",r," Coverage"]}),(0,o.jsx)("span",{style:{color:"#dc2626",fontWeight:"600"},children:"$0.00/paycheck"})]}):(()=>{let e=B.find(e=>e.id===c);return e?(0,o.jsxs)("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",backgroundColor:"white",padding:"12px",borderRadius:"6px"},children:[(0,o.jsx)("span",{style:{fontWeight:"500",color:"#111827"},children:e.name}),(0,o.jsxs)("span",{style:{color:R.primary,fontWeight:"600"},children:["$",(()=>{var n;let t=null===(n=e.coverageTiers)||void 0===n?void 0:n.find(e=>e.tierName===P);return t?t.employeeCost.toFixed(2):e.cost.toFixed(2)})(),"/paycheck"]})]}):null})()})]}),(0,o.jsxs)("div",{style:{display:"flex",gap:"12px",paddingTop:"24px",borderTop:"1px solid #e5e7eb",marginTop:"24px"},children:[N.length>0&&(0,o.jsxs)("button",{onClick:()=>v(!0),style:{display:"flex",alignItems:"center",gap:"8px",padding:"8px 16px",color:"#374151",border:"1px solid #d1d5db",borderRadius:"8px",backgroundColor:"white",cursor:"pointer",transition:"background-color 0.2s ease"},children:[(0,o.jsx)(m.Z,{size:16,style:{color:"#6b7280"}}),"Watch Video"]}),B.length>1&&(0,o.jsxs)("button",{onClick:()=>I(!0),style:{display:"flex",alignItems:"center",gap:"8px",padding:"8px 16px",color:"#374151",border:"1px solid #d1d5db",borderRadius:"8px",backgroundColor:"white",cursor:"pointer",transition:"background-color 0.2s ease"},children:[(0,o.jsx)(U.Z,{size:16,style:{color:"#6b7280"}}),"Compare Plans"]})]})]}),D&&(0,o.jsx)("div",{style:{position:"fixed",top:0,left:0,right:0,bottom:0,backgroundColor:"rgba(0, 0, 0, 0.5)",display:"flex",alignItems:"center",justifyContent:"center",zIndex:1e3},children:(0,o.jsxs)("div",{style:{backgroundColor:"white",borderRadius:"12px",padding:"24px",maxWidth:"500px",width:"90%",maxHeight:"80vh",overflow:"auto"},children:[(0,o.jsxs)("h3",{style:{fontSize:"18px",fontWeight:"600",color:"#111827",margin:"0 0 16px 0"},children:["Waive ",r," Coverage"]}),(0,o.jsxs)("p",{style:{color:"#6b7280",marginBottom:"16px",lineHeight:"1.5"},children:["Please select a reason for waiving ",r.toLowerCase()," coverage:"]}),(0,o.jsx)("div",{style:{marginBottom:"24px"},children:["I have coverage through my spouse/partner","I have coverage through another employer","I don't need this coverage","Cost is too high","Other"].map(e=>(0,o.jsxs)("label",{style:{display:"flex",alignItems:"center",gap:"8px",padding:"8px 0",cursor:"pointer"},children:[(0,o.jsx)("input",{type:"radio",name:"waiveReason",value:e,checked:C===e,onChange:e=>w(e.target.value),style:{margin:0}}),(0,o.jsx)("span",{style:{fontSize:"14px",color:"#374151"},children:e})]},e))}),(0,o.jsxs)("div",{style:{display:"flex",gap:"12px",justifyContent:"flex-end"},children:[(0,o.jsx)("button",{onClick:()=>{j(!1),w("")},style:{padding:"8px 16px",backgroundColor:"#f3f4f6",color:"#374151",border:"none",borderRadius:"6px",cursor:"pointer",fontSize:"14px",fontWeight:"500"},children:"Cancel"}),(0,o.jsx)("button",{onClick:()=>{if(!C){alert("Please select a reason for waiving coverage.");return}p("WAIVE"),x(null),G({category:r,waiveReason:C,waiveDate:new Date().toISOString()}),j(!1),w(""),d(null)},disabled:!C,style:{padding:"8px 16px",backgroundColor:C?"#ef4444":"#d1d5db",color:"white",border:"none",borderRadius:"6px",cursor:C?"pointer":"not-allowed",fontSize:"14px",fontWeight:"500"},children:"Waive Coverage"})]})]})}),b&&(0,o.jsx)(S,{title:"".concat(r," Plan Overview"),description:"Learn more about ".concat(r.toLowerCase()," coverage options"),planType:r.toLowerCase(),onClose:()=>v(!1),videoUrl:"https://example.com/video"}),k&&N.length>1&&(0,o.jsx)(_,{onClose:()=>I(!1),plans:N.map(e=>({id:e.id,name:e.name,type:r.toLowerCase(),tier:"Silver",monthlyPremium:e.cost,deductible:0,outOfPocketMax:0,features:e.features,network:"Standard Network"}))})]})},ei=t(99376),ea=t(15847),es=t(83337),ed=t(61103);let ec=(0,ed.bR)(),ep=()=>{let e=(0,ed.n5)();return console.log("\uD83D\uDD0D BULK WAIVE API - User ID for headers:",e),{"Content-Type":"application/json","user-id":e}},eu=async e=>{try{console.log("\uD83D\uDEAB BULK WAIVE API - Starting request..."),console.log("\uD83D\uDD0D Request details:",{employeeId:e.employeeId,planAssignmentIds:e.planAssignmentIds,waiveReason:e.waiveReason,enrollmentType:e.enrollmentType||"Open Enrollment",planAssignmentCount:e.planAssignmentIds.length});let n=eg(e);if(!n.isValid)return console.error("❌ Request validation failed:",n.errors),{success:!1,error:"Validation failed: ".concat(n.errors.join(", "))};let t={employeeId:e.employeeId,planAssignmentIds:e.planAssignmentIds,waiveReason:e.waiveReason,waiveDate:e.waiveDate||new Date().toISOString(),enrollmentType:e.enrollmentType||"Open Enrollment"};console.log("\uD83D\uDD0D Request body being sent:",t),console.log("\uD83D\uDD0D API URL:","".concat(ec,"/api/pre-enrollment/employee-enrollments/bulk-waive")),console.log("\uD83D\uDD0D Headers:",ep());let o=await fetch("".concat(ec,"/api/pre-enrollment/employee-enrollments/bulk-waive"),{method:"POST",headers:ep(),body:JSON.stringify(t)});if(console.log("\uD83D\uDD0D Bulk waive API response status:",o.status),console.log("\uD83D\uDD0D Response headers:",Object.fromEntries(o.headers.entries())),!o.ok){let e="HTTP error! status: ".concat(o.status);try{let n=await o.json();console.error("❌ Bulk waive API error response:",n),e=n.error||n.message||e,n.errors&&console.error("❌ Individual plan assignment errors:",n.errors)}catch(t){console.log("⚠️ Could not parse error response as JSON");let n=await o.text();console.error("❌ Raw error response:",n),e=n||e}return{success:!1,error:e}}let l=await o.json();return console.log("✅ Bulk waive API successful response:",l),l.createdEnrollments&&console.log("✅ Created waived enrollments:",l.createdEnrollments),l.errors&&l.errors.length>0&&console.warn("⚠️ Some plan assignments failed:",l.errors),{success:!0,data:l}}catch(e){return console.error("❌ Network/fetch error in bulk waive enrollments:",e),{success:!1,error:e instanceof Error?e.message:"Failed to bulk waive enrollments"}}},eg=e=>{var n,t;let o=[];return(null===(n=e.employeeId)||void 0===n?void 0:n.trim())||o.push("Employee ID is required"),e.planAssignmentIds&&0!==e.planAssignmentIds.length||o.push("At least one plan assignment ID is required"),(null===(t=e.waiveReason)||void 0===t?void 0:t.trim())||o.push("Waive reason is required"),{isValid:0===o.length,errors:o}},ex=()=>"http://localhost:8080",ef=()=>localStorage.getItem("userid1")||localStorage.getItem("userId")||"",eh=()=>({"Content-Type":"application/json","user-id":ef()}),em=async e=>{try{let n=ex(),t=ef();if(!t)throw Error("User ID not found. Please log in again.");console.log("\uD83D\uDE80 Bulk Enrollment API Request:",{url:"".concat(n,"/api/pre-enrollment/employee-enrollments/bulk"),userId:t,payload:e});let o=await fetch("".concat(n,"/api/pre-enrollment/employee-enrollments/bulk"),{method:"POST",headers:eh(),body:JSON.stringify(e)}),l=await o.json();if(o.ok)return console.log("✅ Bulk enrollment successful:",l),{success:!0,...l};return console.error("❌ Bulk enrollment failed:",{status:o.status,statusText:o.statusText,error:l}),{success:!1,error:l.error||"Bulk enrollment failed",failedPlan:l.failedPlan,details:l.details,rollbackPerformed:l.rollbackPerformed||!1}}catch(e){return console.error("❌ Bulk enrollment API error:",e),{success:!1,error:e instanceof Error?e.message:"Network error occurred",rollbackPerformed:!1}}},ey=function(e,n){let t=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[],o=[],l=(e,l)=>{(null==e?void 0:e.id)&&(o.push({planAssignmentId:e.id,coverageTier:n,dependentIds:t,enrollmentType:"Open Enrollment"}),console.log("✅ Added ".concat(l," plan to bulk enrollment:"),e.id))};return l(e.dentalPlan,"Dental"),l(e.visionPlan,"Vision"),l(e.lifePlan,"Life Insurance"),l(e.addPlan,"AD&D"),console.log("\uD83D\uDCCB Prepared plan selections for bulk enrollment:",o),o},eb=async(e,n)=>{if("Employee Only"===n)return[];let t=e.dependents||[];if(0===t.length){let e=localStorage.getItem("enrollmentDependents");if(e)try{let n=JSON.parse(e);t.push(...n)}catch(e){console.error("❌ Error parsing stored dependents:",e)}}let o=t.map(e=>e.id||e._id||e.dependentId).filter(Boolean);return console.log("\uD83D\uDC65 Dependents for bulk enrollment:",{coverageTier:n,dependentCount:t.length,dependentIds:o}),o},ev=e=>{var n;let t=[];return e.employeeId||t.push("Employee ID is required"),e.companyId||t.push("Company ID is required"),e.planSelections&&0!==e.planSelections.length||t.push("At least one plan selection is required"),null===(n=e.planSelections)||void 0===n||n.forEach((e,n)=>{e.planAssignmentId||t.push("Plan assignment ID is required for selection ".concat(n+1)),e.coverageTier||t.push("Coverage tier is required for selection ".concat(n+1))}),{isValid:0===t.length,errors:t}},eD=e=>{if(!e.success)return"Enrollment failed: ".concat(e.error);let n=e.summary;return n?"✅ Enrollment completed successfully!\n\uD83D\uDCCA ".concat(n.totalEnrollments," plan(s) enrolled\n\uD83D\uDCB0 Total monthly cost: $").concat(n.totalMonthlyCost.toFixed(2),"\n").concat(n.hasWarnings?"⚠️ Some validation warnings occurred":""):"Enrollment completed successfully"};var ej=t(86586),eS=t(35303),eC=e=>{let{showCosts:n=!0,showWaived:t=!0,style:l="summary"}=e,r=et(),i=r.selectedPlans||[],a=r.waivedCoverages||[],s=e=>{let n=e.toLowerCase();return n.includes("dental")?{bg:"#f0fdf4",text:"#166534",price:"#059669",icon:"\uD83E\uDDB7"}:n.includes("vision")?{bg:"#eff6ff",text:"#1e40af",price:"#2563eb",icon:"\uD83D\uDC53"}:n.includes("life")?{bg:"#faf5ff",text:"#6b21a8",price:"#7c3aed",icon:"\uD83D\uDEE1️"}:n.includes("ad&d")||n.includes("add")?{bg:"#fef3c7",text:"#92400e",price:"#d97706",icon:"\uD83D\uDEE1️"}:n.includes("medical")?{bg:"#fef2f2",text:"#991b1b",price:"#dc2626",icon:"\uD83C\uDFE5"}:{bg:"#f9fafb",text:"#374151",price:"#111827",icon:"\uD83D\uDCCB"}},d=i.map(e=>{let n=e.selectedTier;return{planName:e.planName,planType:e.category,employeeCost:n.employeeCost,employerCost:n.employerCost,totalCost:n.totalCost,coverageTier:n.tierName}}),c=d.reduce((e,n)=>e+n.employeeCost,0);return 0===i.length&&0===a.length?(0,o.jsx)("div",{style:{backgroundColor:"#f9fafb",borderRadius:"8px",padding:"24px",textAlign:"center",border:"1px solid #e5e7eb"},children:(0,o.jsx)("p",{style:{color:"#6b7280",margin:0,fontSize:"14px"},children:"No plans selected or waived yet."})}):(0,o.jsxs)("div",{children:[i.length>0&&(0,o.jsxs)("div",{style:{marginBottom:t&&a.length>0?"24px":"0"},children:[(0,o.jsx)("h3",{style:{fontSize:"16px",fontWeight:"600",color:"#111827",marginBottom:"16px",margin:"0 0 16px 0"},children:"Selected Plans"}),n?(0,o.jsxs)("div",{style:{display:"flex",flexDirection:"column",gap:"12px"},children:[d.map((e,n)=>{let t=s(e.planType);return(0,o.jsx)("div",{style:{backgroundColor:t.bg,borderRadius:"8px",padding:"16px",border:"1px solid #e5e7eb"},children:(0,o.jsxs)("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"flex-start"},children:[(0,o.jsxs)("div",{children:[(0,o.jsxs)("h3",{style:{fontSize:"16px",fontWeight:"600",color:"#111827",margin:"0 0 4px 0"},children:[t.icon," ",e.planType,": ",e.planName]}),(0,o.jsx)("p",{style:{fontSize:"14px",color:t.text,margin:"0 0 8px 0",fontWeight:"500"},children:e.coverageTier}),(0,o.jsxs)("div",{style:{fontSize:"12px",color:"#6b7280"},children:[(0,o.jsxs)("div",{children:["Total Cost: $",e.totalCost.toFixed(2),"/month"]}),(0,o.jsxs)("div",{children:["Employer Pays: $",e.employerCost.toFixed(2),"/month"]})]})]}),(0,o.jsxs)("div",{style:{textAlign:"right"},children:[(0,o.jsxs)("p",{style:{fontSize:"18px",fontWeight:"600",color:t.price,margin:0},children:["$",e.employeeCost.toFixed(2)]}),(0,o.jsx)("p",{style:{fontSize:"12px",color:"#6b7280",margin:"4px 0 0 0"},children:"per paycheck"})]})]})},n)}),d.length>0&&(0,o.jsx)("div",{style:{backgroundColor:"#f3f4f6",borderRadius:"8px",padding:"16px",border:"2px solid #d1d5db",marginTop:"8px"},children:(0,o.jsxs)("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center"},children:[(0,o.jsx)("h3",{style:{fontSize:"16px",fontWeight:"600",color:"#111827",margin:0},children:"Total Monthly Cost"}),(0,o.jsxs)("p",{style:{fontSize:"20px",fontWeight:"700",color:"#111827",margin:0},children:["$",c.toFixed(2)]})]})}),0===d.length&&a.length>0&&(0,o.jsxs)("div",{style:{backgroundColor:"#f3f4f6",borderRadius:"8px",padding:"16px",border:"2px solid #d1d5db",marginTop:"8px"},children:[(0,o.jsxs)("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center"},children:[(0,o.jsx)("h3",{style:{fontSize:"16px",fontWeight:"600",color:"#111827",margin:0},children:"Total Monthly Cost"}),(0,o.jsx)("p",{style:{fontSize:"20px",fontWeight:"700",color:"#111827",margin:0},children:"$0.00"})]}),(0,o.jsx)("p",{style:{fontSize:"12px",color:"#6b7280",margin:"4px 0 0 0",textAlign:"center"},children:"All coverages waived"})]})]}):(0,o.jsx)("div",{style:{display:"flex",flexDirection:"column",gap:"8px"},children:i.map((e,n)=>{let t=s(e.category);return(0,o.jsxs)("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center"},children:[(0,o.jsxs)("span",{style:{color:"#374151",fontSize:"14px",fontWeight:"500"},children:[t.icon," ",e.category,":"]}),(0,o.jsx)("span",{style:{color:"#111827",fontSize:"14px",fontWeight:"600"},children:e.planName})]},n)})})]}),t&&a.length>0&&(0,o.jsxs)("div",{children:[(0,o.jsx)("h3",{style:{fontSize:"16px",fontWeight:"600",color:"#111827",marginBottom:"16px",margin:"0 0 16px 0"},children:"Waived Coverages"}),(0,o.jsx)("div",{style:{display:"flex",flexDirection:"column",gap:"8px"},children:a.map((e,n)=>{let t=s(e.category);return(0,o.jsxs)("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center"},children:[(0,o.jsxs)("span",{style:{color:"#dc2626",fontSize:"14px",fontWeight:"500"},children:[t.icon," ",e.category,":"]}),(0,o.jsx)("span",{style:{color:"#dc2626",fontSize:"14px",fontWeight:"600"},children:"Waived"})]},n)})}),"confirmation"===l&&(0,o.jsx)("div",{style:{marginTop:"12px",fontSize:"12px",color:"#6b7280"},children:a.map((e,n)=>(0,o.jsxs)("div",{children:[e.category," waive reason: ",e.waiveReason]},n))})]})]})},ew=e=>{let{enrollmentData:n,selectedCoverageTier:t,onMakeChanges:r,onConfirmEnrollment:i,planAssignments:a={}}=e,s=(0,ei.useRouter)(),d=(0,es.C)(e=>e.user.userProfile),[c,p]=(0,l.useState)({}),[u,x]=(0,l.useState)(!1),[f,m]=(0,l.useState)(!1),[y,b]=(0,l.useState)(!1),[v,D]=(0,l.useState)(""),[j,S]=(0,l.useState)(!1),[C,w]=(0,l.useState)(!1),k=()=>"http://localhost:8080",I=()=>localStorage.getItem("userid1")||localStorage.getItem("userId")||"6838677aef6db0212bcfdacd",P=async e=>{let n=k(),t=I();try{console.log("\uD83D\uDD0D Fetching plan assignment details for ID:",e);let o=await fetch("".concat(n,"/api/pre-enrollment/plan-assignments/").concat(e),{headers:{"Content-Type":"application/json","user-id":t}});if(!o.ok)return console.error("❌ Failed to fetch plan assignment details. Status:",o.status),null;{let e=await o.json();return console.log("✅ Plan assignment fetch response:",e),e.assignment._doc||e.assignment}}catch(e){return console.error("❌ Error fetching plan assignment:",e),null}},W=()=>{console.log("\uD83E\uDDF9 Clearing enrollment data from localStorage..."),["selectedCoverageTier","enrollmentDependents","userProfile","enrollmentRecommendation"].forEach(e=>{localStorage.removeItem(e),console.log("  ✅ Removed: ".concat(e))}),console.log("✅ Enrollment data cleared from localStorage"),console.log("\uD83D\uDD12 Preserved for confirmation page:",["selectedDentalPlan","selectedVisionPlan","selectedLifePlan","selectedADDPlan","dentalWaived","visionWaived","lifeWaived","addWaived","dentalWaiveReason","visionWaiveReason","lifeWaiveReason","addWaiveReason","enrollmentSummarySnapshot"])},E=async()=>{f||((0,eS.yd)()?(console.log("\uD83D\uDCDD Signature already exists in localStorage, proceeding with enrollment..."),T()):(console.log("\uD83D\uDCDD No signature found, showing signature modal..."),S(!0)))},T=async()=>{m(!0),console.log("\uD83C\uDFAF Starting BULK enrollment confirmation process...");try{let e=I(),t=d.companyId||localStorage.getItem("companyId")||"";if(!t)throw Error("Company ID not found. Please contact support.");let o=await eb(n,z),l=ey({dentalPlan:M,visionPlan:H,lifePlan:V,addPlan:U},z,o),r=null;if(l.length>0){let n={employeeId:e,companyId:t,employeeClassType:"Full-Time",planSelections:l},o=ev(n);if(!o.isValid)throw Error("Bulk enrollment validation failed: ".concat(o.errors.join(", ")));if(console.log("\uD83D\uDE80 Calling bulk enrollment API with validated request:",n),(r=await em(n)).success)console.log("✅ Bulk enrollment successful:",r),console.log("\uD83D\uDCCA Enrollment summary:",eD(r));else throw console.error("❌ Bulk enrollment failed:",r),Error(r.error||"Bulk enrollment failed")}else console.log("ℹ️ No plans selected for enrollment, proceeding with waive operations only");let c=[],p=[];if(console.log("\uD83D\uDD0D BULK WAIVE DEBUG - Starting bulk waive process..."),console.log("\uD83D\uDD0D User ID:",e),console.log("\uD83D\uDD0D Plan assignments available:",Object.keys(a)),console.log("\uD83D\uDD0D Waive statuses:",{dentalWaived:A,visionWaived:F,lifeWaived:B}),A)try{console.log("\uD83D\uDEAB Processing dental waive...");let n=localStorage.getItem("dentalWaiveReason")||"Coverage not needed";console.log("\uD83D\uDD0D Dental waive reason:",n);let t=O("dental");if(0===t.length)console.log("⚠️ No dental plan assignments found for waiving"),console.log("\uD83D\uDD0D Available plan assignment keys:",Object.keys(a)),console.log("\uD83D\uDD0D Plan assignments structure:",a);else{let o=t.map(e=>{var n;return(null===(n=e.assignment)||void 0===n?void 0:n._id)||e._id||e.id}).filter(Boolean);if(console.log("\uD83D\uDD0D Dental plan assignment IDs to waive:",o),o.length>0){console.log("\uD83D\uDEAB Calling bulk waive API for dental...");let t=await eu({employeeId:e,planAssignmentIds:o,waiveReason:n,enrollmentType:"Open Enrollment"});console.log("\uD83D\uDD0D Dental bulk waive API response:",t),t.success&&t.data?(console.log("✅ Dental bulk waive successful:",t.data),c.push({type:"Dental",result:t.data})):(console.error("❌ Dental bulk waive failed:",t.error),p.push({type:"Dental Waive",error:t.error||"Failed to waive dental coverage"}))}else console.log("⚠️ No valid dental plan assignment IDs found")}}catch(e){console.error("❌ Dental bulk waive error:",e),p.push({type:"Dental Waive",error:e instanceof Error?e.message:"Failed to waive dental coverage"})}if(F)try{console.log("\uD83D\uDEAB Processing vision waive...");let n=localStorage.getItem("visionWaiveReason")||"Coverage not needed";console.log("\uD83D\uDD0D Vision waive reason:",n);let t=O("vision");if(0===t.length)console.log("⚠️ No vision plan assignments found for waiving");else{let o=t.map(e=>{var n;return(null===(n=e.assignment)||void 0===n?void 0:n._id)||e._id||e.id}).filter(Boolean);if(console.log("\uD83D\uDD0D Vision plan assignment IDs to waive:",o),o.length>0){console.log("\uD83D\uDEAB Calling bulk waive API for vision...");let t=await eu({employeeId:e,planAssignmentIds:o,waiveReason:n,enrollmentType:"Open Enrollment"});console.log("\uD83D\uDD0D Vision bulk waive API response:",t),t.success&&t.data?(console.log("✅ Vision bulk waive successful:",t.data),c.push({type:"Vision",result:t.data})):(console.error("❌ Vision bulk waive failed:",t.error),p.push({type:"Vision Waive",error:t.error||"Failed to waive vision coverage"}))}else console.log("⚠️ No valid vision plan assignment IDs found")}}catch(e){console.error("❌ Vision bulk waive error:",e),p.push({type:"Vision Waive",error:e instanceof Error?e.message:"Failed to waive vision coverage"})}if(B)try{console.log("\uD83D\uDEAB Processing life insurance waive...");let n=localStorage.getItem("lifeWaiveReason")||"Coverage not needed";console.log("\uD83D\uDD0D Life insurance waive reason:",n);let t=[];for(let e of["term life","life","life insurance","supplemental life insurance","group (employer) life"]){let n=O(e);n.length>0&&(t.push(...n),console.log("\uD83D\uDD0D Found life insurance plans in category '".concat(e,"':"),n))}if(0===t.length)console.log("⚠️ No life insurance plan assignments found for waiving");else{let o=t.map(e=>{var n;return(null===(n=e.assignment)||void 0===n?void 0:n._id)||e._id||e.id}).filter(Boolean);if(console.log("\uD83D\uDD0D Life insurance plan assignment IDs to waive:",o),o.length>0){console.log("\uD83D\uDEAB Calling bulk waive API for life insurance...");let t=await eu({employeeId:e,planAssignmentIds:o,waiveReason:n,enrollmentType:"Open Enrollment"});console.log("\uD83D\uDD0D Life insurance bulk waive API response:",t),t.success&&t.data?(console.log("✅ Life insurance bulk waive successful:",t.data),c.push({type:"Life Insurance",result:t.data})):(console.error("❌ Life insurance bulk waive failed:",t.error),p.push({type:"Life Insurance Waive",error:t.error||"Failed to waive life insurance coverage"}))}else console.log("⚠️ No valid life insurance plan assignment IDs found")}}catch(e){console.error("❌ Life insurance bulk waive error:",e),p.push({type:"Life Insurance Waive",error:e instanceof Error?e.message:"Failed to waive life insurance coverage"})}if(N)try{console.log("\uD83D\uDEAB Processing AD&D waive...");let n=localStorage.getItem("addWaiveReason")||"Coverage not needed";console.log("\uD83D\uDD0D AD&D waive reason:",n);let t=[];for(let e of["accidental death & dismemberment (ad&d)","ad&d","add","accidental death and dismemberment"]){let n=O(e);n.length>0&&(t.push(...n),console.log("\uD83D\uDD0D Found AD&D plans in category '".concat(e,"':"),n))}if(0===t.length)console.log("⚠️ No AD&D plan assignments found for waiving");else{let o=t.map(e=>{var n;return(null===(n=e.assignment)||void 0===n?void 0:n._id)||e._id||e.id}).filter(Boolean);if(console.log("\uD83D\uDD0D AD&D plan assignment IDs to waive:",o),o.length>0){console.log("\uD83D\uDEAB Calling bulk waive API for AD&D...");let t=await eu({employeeId:e,planAssignmentIds:o,waiveReason:n,enrollmentType:"Open Enrollment"});console.log("\uD83D\uDD0D AD&D bulk waive API response:",t),t.success&&t.data?(console.log("✅ AD&D bulk waive successful:",t.data),c.push({type:"AD&D",result:t.data})):(console.error("❌ AD&D bulk waive failed:",t.error),p.push({type:"AD&D Waive",error:t.error||"Failed to waive AD&D coverage"}))}else console.log("⚠️ No valid AD&D plan assignment IDs found")}}catch(e){console.error("❌ AD&D bulk waive error:",e),p.push({type:"AD&D Waive",error:e instanceof Error?e.message:"Failed to waive AD&D coverage"})}console.log("\uD83D\uDEAB Bulk waive results:",c);let u=r&&!r.success,g=p.length>0;if(u||g){console.error("❌ Some operations failed:",{bulkEnrollmentError:u&&r?r.error:null,waiveErrors:p});let e="❌ Enrollment Error\n\n";u&&r&&(e+="Enrollment failed: ".concat(r.error,"\n\n")),g&&(e+="Waive errors:\n".concat(p.map(e=>e.error).join("\n"))),D(e),b(!0)}else{console.log("✅ All operations completed successfully:",{bulkEnrollment:r,waiveResults:c}),console.log("\uD83D\uDD0D Pre-snapshot debug - Plan variables:",{dentalPlan:M,visionPlan:H,lifePlan:V,addPlan:U,addWaived:N});let e=eo({dentalPlan:M,visionPlan:H,lifePlan:V,addPlan:U,dependents:n.dependents,selectedCoverageTier:z,dentalWaived:A,visionWaived:F,lifeWaived:B,addWaived:N,dentalWaiveReason:localStorage.getItem("dentalWaiveReason"),visionWaiveReason:localStorage.getItem("visionWaiveReason"),lifeWaiveReason:localStorage.getItem("lifeWaiveReason"),addWaiveReason:localStorage.getItem("addWaiveReason")});console.log("\uD83D\uDCF8 Saved enrollment summary snapshot:",e),console.log("\uD83D\uDEE1️ AD&D in snapshot specifically:",{addPlan:e.addPlan,addWaived:e.addWaived,addWaiveReason:e.addWaiveReason}),W(),console.log("\uD83C\uDF89 Enrollment completed successfully, navigating to confirmation page..."),i?i():s.push("/ai-enroller/employee-enrol/confirmation")}}catch(e){console.error("❌ Enrollment confirmation failed:",e),D("❌ Enrollment Error\n\nThere was an error in enrolling. Please contact HR or try again."),b(!0)}finally{m(!1)}},z=(()=>{if(console.log("\uD83D\uDD0D Checking coverage tier sources:"),console.log("  - selectedCoverageTier prop:",t),t)return console.log("  ✅ Using prop value:",t),t;let e=localStorage.getItem("selectedCoverageTier");return(console.log("  - localStorage value:",e),e)?(console.log("  ✅ Using localStorage value:",e),e):(console.log("  ⚠️ Using fallback: Employee Only"),"Employee Only")})();console.log("\uD83C\uDFAF Final coverage tier being used:",z);let R=(e,n)=>{if(console.log("\uD83D\uDD0D Getting cost breakdown for ".concat(n,":"),e),console.log("\uD83C\uDFAF Selected coverage tier: ".concat(z)),!e)return console.log("❌ No plan data for ".concat(n)),null;if(!e.coverageTiers||!Array.isArray(e.coverageTiers))return console.log("⚠️ No coverage tiers found for ".concat(n,", using fallback cost")),{planName:e.name||e.planName||"Unknown Plan",planType:n,employeeCost:e.cost||0,employerCost:0,totalCost:e.cost||0};console.log("\uD83D\uDCCA Coverage tiers for ".concat(n,":"),e.coverageTiers),e.coverageTiers.forEach((e,n)=>{console.log("  Tier ".concat(n,": ").concat(e.tierName," - Employee: $").concat(e.employeeCost,", Employer: $").concat(e.employerCost,", Total: $").concat(e.totalCost))});let t=e.coverageTiers.find(e=>e.tierName===z);if(!t){console.log("⚠️ No matching tier for ".concat(z,", trying Employee Only"));let t=e.coverageTiers.find(e=>"Employee Only"===e.tierName);if(!t){console.log("⚠️ No Employee Only tier found, using first tier");let t=e.coverageTiers[0];return t?{planName:e.name||e.planName||"Unknown Plan",planType:n,employeeCost:t.employeeCost||0,employerCost:t.employerCost||0,totalCost:t.totalCost||0}:null}return{planName:e.name||e.planName||"Unknown Plan",planType:n,employeeCost:t.employeeCost||0,employerCost:t.employerCost||0,totalCost:t.totalCost||0}}return console.log("✅ Found matching tier for ".concat(n,":"),t),{planName:e.name||e.planName||"Unknown Plan",planType:n,employeeCost:t.employeeCost||0,employerCost:t.employerCost||0,totalCost:t.totalCost||0}};console.log("\uD83D\uDCCB Enrollment data received:",n),console.log("\uD83D\uDC65 Dependents:",n.dependents),console.log("\uD83E\uDDB7 Dental plan:",n.dentalPlan),console.log("\uD83D\uDC53 Vision plan:",n.visionPlan),console.log("\uD83D\uDEE1️ Life plan:",n.lifePlan);let A="true"===localStorage.getItem("dentalWaived"),F="true"===localStorage.getItem("visionWaived"),B="true"===localStorage.getItem("lifeWaived"),N="true"===localStorage.getItem("addWaived");console.log("\uD83D\uDEAB Waived coverages:",{dentalWaived:A,visionWaived:F,lifeWaived:B,addWaived:N}),console.log("\uD83D\uDCCB Plan assignments for bulk waive:",a);let O=e=>{for(let n of[e.toLowerCase(),e,"".concat(e," plan"),"".concat(e.toLowerCase()," plan"),e.charAt(0).toUpperCase()+e.slice(1).toLowerCase(),"".concat(e.charAt(0).toUpperCase()+e.slice(1).toLowerCase()," Plan")])if(a[n]&&a[n].length>0)return console.log("\uD83D\uDD0D Found ".concat(e," plans under key '").concat(n,"':"),a[n]),a[n];return console.log("⚠️ No ".concat(e," plan assignments found. Available keys:"),Object.keys(a)),[]};(0,l.useEffect)(()=>{(async()=>{x(!0),console.log("\uD83D\uDD04 Fetching real plan assignment data for summary...");try{let e={},n=localStorage.getItem("selectedDentalPlan"),t=localStorage.getItem("selectedVisionPlan"),o=localStorage.getItem("selectedLifePlan"),l=localStorage.getItem("selectedADDPlan");if(n)try{let t=JSON.parse(n);if(t.id){console.log("\uD83E\uDDB7 Fetching real dental plan assignment:",t.id);let n=await P(t.id);n&&(e.dentalPlan={...t,...n,name:n.planName||t.name,coverageTiers:n.coverageTiers})}}catch(e){console.error("❌ Error parsing dental plan data:",e)}if(t)try{let n=JSON.parse(t);if(n.id){console.log("\uD83D\uDC53 Fetching real vision plan assignment:",n.id);let t=await P(n.id);t&&(e.visionPlan={...n,...t,name:t.planName||n.name,coverageTiers:t.coverageTiers})}}catch(e){console.error("❌ Error parsing vision plan data:",e)}if(o)try{let n=JSON.parse(o);if(n.id){console.log("\uD83D\uDEE1️ Fetching real life plan assignment:",n.id);let t=await P(n.id);t&&(e.lifePlan={...n,...t,name:t.planName||n.name,coverageTiers:t.coverageTiers})}}catch(e){console.error("❌ Error parsing life plan data:",e)}if(l)try{let n=JSON.parse(l);if(n.id){console.log("\uD83D\uDEE1️ Fetching real AD&D plan assignment:",n.id);let t=await P(n.id);t&&(e.addPlan={...n,...t,name:t.planName||n.name,coverageTiers:t.coverageTiers})}}catch(e){console.error("❌ Error parsing AD&D plan data:",e)}console.log("✅ Real plan data fetched:",e),p(e)}catch(e){console.error("❌ Error fetching real plan data:",e)}finally{x(!1)}})()},[]);let L=(e,n,t)=>{if(t)return console.log("✅ Using real plan data for ".concat(e,":"),t),t;if(n)return n;let o=localStorage.getItem(e);if(o)try{return JSON.parse(o)}catch(n){console.log("❌ Error parsing ".concat(e,":"),n)}return null},M=L("selectedDentalPlan",n.dentalPlan,c.dentalPlan),H=L("selectedVisionPlan",n.visionPlan,c.visionPlan),V=L("selectedLifePlan",n.lifePlan,c.lifePlan),U=L("selectedADDPlan",n.addPlan,c.addPlan);console.log("\uD83D\uDD0D Final plan data:",{dentalPlan:M,visionPlan:H,lifePlan:V,addPlan:U}),console.log("\uD83D\uDEE1️ AD&D Plan Debug:",{localStorage_selectedADDPlan:localStorage.getItem("selectedADDPlan"),enrollmentData_addPlan:n.addPlan,realPlanData_addPlan:c.addPlan,final_addPlan:U,addWaived:localStorage.getItem("addWaived")}),(null==M?void 0:M.coverageTiers)&&console.log("\uD83E\uDDB7 Dental plan coverage tiers:",M.coverageTiers),(null==H?void 0:H.coverageTiers)&&console.log("\uD83D\uDC53 Vision plan coverage tiers:",H.coverageTiers),(null==V?void 0:V.coverageTiers)&&console.log("\uD83D\uDEE1️ Life plan coverage tiers:",V.coverageTiers),(null==U?void 0:U.coverageTiers)&&console.log("\uD83D\uDEE1️ AD&D plan coverage tiers:",U.coverageTiers);let _=[],Z=[];if(console.log("\uD83E\uDDB7 Dental insurance check:",{dentalPlan:M,dentalWaived:A}),M){console.log("\uD83E\uDDB7 Processing dental plan:",M);let e=R(M,"Dental");console.log("\uD83E\uDDB7 Dental cost breakdown result:",e),e?(_.push(e),console.log("\uD83E\uDDB7 Dental cost added to planCosts")):console.log("\uD83E\uDDB7 Dental cost breakdown returned null - plan will not be displayed")}else A?(console.log("\uD83E\uDDB7 Dental waived"),Z.push("Dental")):console.log("\uD83E\uDDB7 No dental plan or waive status found");if(console.log("\uD83D\uDC53 Vision insurance check:",{visionPlan:H,visionWaived:F}),H){console.log("\uD83D\uDC53 Processing vision plan:",H);let e=R(H,"Vision");console.log("\uD83D\uDC53 Vision cost breakdown result:",e),e?(_.push(e),console.log("\uD83D\uDC53 Vision cost added to planCosts")):console.log("\uD83D\uDC53 Vision cost breakdown returned null - plan will not be displayed")}else F?(console.log("\uD83D\uDC53 Vision waived"),Z.push("Vision")):console.log("\uD83D\uDC53 No vision plan or waive status found");if(console.log("\uD83D\uDEE1️ Life insurance check:",{lifePlan:V,lifeWaived:B}),V){console.log("\uD83D\uDEE1️ Processing life insurance plan:",V);let e=R(V,"Life Insurance");console.log("\uD83D\uDEE1️ Life insurance cost breakdown:",e),e&&_.push(e)}else B?(console.log("\uD83D\uDEE1️ Life insurance waived"),Z.push("Life Insurance")):console.log("\uD83D\uDEE1️ No life insurance plan or waive status found");if(console.log("\uD83D\uDEE1️ AD&D insurance check:",{addPlan:U,addWaived:N}),U){console.log("\uD83D\uDEE1️ Processing AD&D plan:",U);let e=R(U,"AD&D");console.log("\uD83D\uDEE1️ AD&D cost breakdown:",e),e?(_.push(e),console.log("\uD83D\uDEE1️ AD&D cost added to planCosts")):console.log("\uD83D\uDEE1️ AD&D cost breakdown returned null - plan will not be displayed")}else N?(console.log("\uD83D\uDEE1️ AD&D waived"),Z.push("AD&D")):console.log("\uD83D\uDEE1️ No AD&D plan or waive status found");let q=_.reduce((e,n)=>e+n.employeeCost,0),$=_.reduce((e,n)=>e+n.employerCost,0),Y=_.reduce((e,n)=>e+n.totalCost,0);return console.log("\uD83D\uDCB0 Plan costs calculated:",_),console.log("\uD83D\uDCB0 Totals:",{totalEmployeeCost:q,totalEmployerCost:$,grandTotal:Y}),(0,o.jsxs)("div",{style:{display:"flex",flexDirection:"column",gap:"24px",fontFamily:'"SF Pro", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif'},children:[(0,o.jsxs)("div",{style:{display:"flex",gap:"16px"},children:[(0,o.jsx)("div",{style:{width:"40px",height:"40px",borderRadius:"50%",display:"flex",alignItems:"center",justifyContent:"center",flexShrink:0,overflow:"hidden"},children:(0,o.jsx)(h.default,{src:"/brea.png",alt:"Brea - AI Benefits Assistant",width:40,height:40,style:{borderRadius:"50%"}})}),(0,o.jsxs)("div",{style:{backgroundColor:"#f9fafb",borderRadius:"8px",padding:"16px",maxWidth:"1000px"},children:[(0,o.jsx)("p",{style:{color:"#374151",lineHeight:"1.6",margin:0},children:"\uD83D\uDCCB Perfect! Here's your personalized benefits package:"}),(0,o.jsx)("p",{style:{color:"#374151",lineHeight:"1.6",margin:"8px 0 0 0"},children:"Take a moment to review everything. You can always go back and make changes if needed."})]})]}),(0,o.jsxs)("div",{style:{backgroundColor:"white",borderRadius:"12px",border:"1px solid #e5e7eb",padding:"32px",boxShadow:"0 1px 3px 0 rgba(0, 0, 0, 0.1)"},children:[(0,o.jsx)("div",{style:{marginBottom:"24px"},children:(0,o.jsxs)("div",{style:{display:"flex",alignItems:"center",gap:"8px",marginBottom:"16px"},children:[(0,o.jsx)("span",{style:{fontSize:"24px"},children:"\uD83D\uDCC4"}),(0,o.jsx)("h1",{style:{fontSize:"24px",fontWeight:"600",color:"#111827",margin:0},children:"Enrollment Summary"})]})}),(()=>{let e=et();return e.selectedPlans&&e.selectedPlans.length>0||e.waivedCoverages&&e.waivedCoverages.length>0?(0,o.jsx)("div",{style:{backgroundColor:"#f8fffe",borderRadius:"12px",padding:"24px",marginBottom:"24px",border:"1px solid #d1fae5"},children:(0,o.jsx)(eC,{showCosts:!0,showWaived:!0,style:"summary"})}):null})(),(()=>{let e=et();return e.selectedPlans&&e.selectedPlans.length>0||e.waivedCoverages&&e.waivedCoverages.length>0?null:(0,o.jsxs)("div",{style:{backgroundColor:"#f8fffe",borderRadius:"12px",padding:"24px",marginBottom:"24px",border:"1px solid #d1fae5"},children:[(_.length>0||Z.length>0)&&(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)("div",{style:{display:"flex",flexDirection:"column",gap:"16px",marginBottom:"24px"},children:_.map((e,n)=>{let t=(e=>{switch(e.toLowerCase()){case"medical":return{bg:"#fef2f2",text:"#991b1b",price:"#dc2626"};case"dental":return{bg:"#f0fdf4",text:"#166534",price:"#059669"};case"vision":return{bg:"#eff6ff",text:"#1e40af",price:"#2563eb"};case"life insurance":return{bg:"#faf5ff",text:"#6b21a8",price:"#7c3aed"};case"ad&d":return{bg:"#fef3c7",text:"#92400e",price:"#d97706"};default:return{bg:"#f9fafb",text:"#374151",price:"#111827"}}})(e.planType);return(0,o.jsxs)("div",{style:{backgroundColor:t.bg,borderRadius:"8px",padding:"16px",border:"1px solid #e5e7eb"},children:[(0,o.jsxs)("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"flex-start",marginBottom:"5px"},children:[(0,o.jsxs)("div",{children:[(0,o.jsxs)("h3",{style:{fontSize:"16px",fontWeight:"600",color:"#111827",margin:"0 0 4px 0"},children:[e.planType,": ",e.planName]}),(0,o.jsx)("p",{style:{fontSize:"14px",color:t.text,margin:0,fontWeight:"500"},children:"Medical"===e.planType?"HMO Plan":"Dental"===e.planType?"PPO Plan":"Vision"===e.planType?"Choice Plan":"Life Insurance"===e.planType?"Term Life":"AD&D"===e.planType?"Accident Coverage":"Coverage Plan"})]}),(0,o.jsx)("div",{style:{textAlign:"right"},children:(0,o.jsxs)("p",{style:{fontSize:"16px",fontWeight:"600",color:t.price,margin:0},children:["You pay: $",e.employeeCost.toFixed(2)]})})]}),(0,o.jsx)("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center"},children:(0,o.jsxs)("div",{style:{display:"flex",alignItems:"center",gap:"2px"},children:[(0,o.jsx)("span",{style:{fontSize:"14px",color:"#6b7280"}}),(0,o.jsxs)("span",{style:{fontSize:"14px",color:"#374151"},children:["Employer pays: $",e.employerCost.toFixed(2)]})]})})]},n)})}),(0,o.jsxs)("div",{style:{display:"flex",flexDirection:"column",gap:"12px",paddingTop:"16px",borderTop:"1px solid #e5e7eb"},children:[(0,o.jsxs)("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center"},children:[(0,o.jsx)("span",{style:{fontSize:"16px",fontWeight:"600",color:"#111827"},children:"Total Plan Cost:"}),(0,o.jsxs)("span",{style:{fontSize:"16px",fontWeight:"600",color:"#111827"},children:["$",Y.toFixed(2)]})]}),(0,o.jsxs)("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center"},children:[(0,o.jsx)("span",{style:{fontSize:"16px",fontWeight:"600",color:"#111827"},children:"Employer Contribution:"}),(0,o.jsxs)("span",{style:{fontSize:"16px",fontWeight:"600",color:"#10b981"},children:["-$",$.toFixed(2)]})]}),(0,o.jsxs)("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",paddingTop:"8px",borderTop:"1px solid #e5e7eb"},children:[(0,o.jsx)("span",{style:{fontSize:"18px",fontWeight:"700",color:"#111827"},children:"Your Cost per Paycheck:"}),(0,o.jsxs)("span",{style:{fontSize:"24px",fontWeight:"700",color:"#2563eb"},children:["$",q.toFixed(2)]})]})]}),Z.map((e,n)=>(0,o.jsxs)("div",{style:{backgroundColor:"#fef2f2",borderRadius:"8px",padding:"16px",border:"1px solid #fecaca",marginTop:"16px"},children:[(0,o.jsxs)("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"flex-start",marginBottom:"8px"},children:[(0,o.jsxs)("div",{children:[(0,o.jsxs)("h3",{style:{fontSize:"16px",fontWeight:"600",color:"#111827",margin:"0 0 4px 0"},children:[e,": Waived"]}),(0,o.jsx)("p",{style:{fontSize:"14px",color:"#dc2626",margin:0,fontWeight:"500"},children:"No Coverage"})]}),(0,o.jsx)("div",{style:{textAlign:"right"},children:(0,o.jsx)("p",{style:{fontSize:"16px",fontWeight:"600",color:"#dc2626",margin:0},children:"You pay: $0.00"})})]}),(0,o.jsxs)("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center"},children:[(0,o.jsxs)("div",{style:{display:"flex",alignItems:"center",gap:"4px"},children:[(0,o.jsx)("span",{style:{fontSize:"14px",color:"#dc2626"},children:"⚠️"}),(0,o.jsxs)("span",{style:{fontSize:"14px",color:"#374151"},children:["No ",e.toLowerCase()," benefits"]})]}),(0,o.jsx)("div",{style:{fontSize:"14px",fontWeight:"600",color:"#dc2626"},children:"You pay: $0.00"})]})]},n))]}),0===_.length&&(M||H||V||U)&&(0,o.jsxs)("div",{style:{marginBottom:"24px"},children:[(0,o.jsx)("h3",{style:{fontSize:"16px",fontWeight:"600",color:"#111827",marginBottom:"16px"},children:"Selected Plans (Basic Info)"}),(0,o.jsxs)("div",{style:{display:"flex",flexDirection:"column",gap:"12px"},children:[M&&(0,o.jsx)("div",{style:{backgroundColor:"#f0fdf4",borderRadius:"8px",padding:"16px",border:"1px solid #e5e7eb"},children:(0,o.jsxs)("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"flex-start"},children:[(0,o.jsxs)("div",{children:[(0,o.jsxs)("h3",{style:{fontSize:"16px",fontWeight:"600",color:"#111827",margin:"0 0 4px 0"},children:["Dental: ",M.name||M.planName||"Selected Plan"]}),(0,o.jsx)("p",{style:{fontSize:"14px",color:"#166534",margin:0,fontWeight:"500"},children:"PPO Plan"})]}),(0,o.jsx)("div",{style:{textAlign:"right"},children:(0,o.jsx)("p",{style:{fontSize:"16px",fontWeight:"600",color:"#059669",margin:0},children:"Cost info unavailable"})})]})}),H&&(0,o.jsx)("div",{style:{backgroundColor:"#eff6ff",borderRadius:"8px",padding:"16px",border:"1px solid #e5e7eb"},children:(0,o.jsxs)("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"flex-start"},children:[(0,o.jsxs)("div",{children:[(0,o.jsxs)("h3",{style:{fontSize:"16px",fontWeight:"600",color:"#111827",margin:"0 0 4px 0"},children:["Vision: ",H.name||H.planName||"Selected Plan"]}),(0,o.jsx)("p",{style:{fontSize:"14px",color:"#1e40af",margin:0,fontWeight:"500"},children:"Choice Plan"})]}),(0,o.jsx)("div",{style:{textAlign:"right"},children:(0,o.jsx)("p",{style:{fontSize:"16px",fontWeight:"600",color:"#2563eb",margin:0},children:"Cost info unavailable"})})]})}),V&&(0,o.jsx)("div",{style:{backgroundColor:"#faf5ff",borderRadius:"8px",padding:"16px",border:"1px solid #e5e7eb"},children:(0,o.jsxs)("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"flex-start"},children:[(0,o.jsxs)("div",{children:[(0,o.jsxs)("h3",{style:{fontSize:"16px",fontWeight:"600",color:"#111827",margin:"0 0 4px 0"},children:["Life Insurance: ",V.name||V.planName||"Selected Plan"]}),(0,o.jsx)("p",{style:{fontSize:"14px",color:"#6b21a8",margin:0,fontWeight:"500"},children:"Term Life"})]}),(0,o.jsx)("div",{style:{textAlign:"right"},children:(0,o.jsx)("p",{style:{fontSize:"16px",fontWeight:"600",color:"#7c3aed",margin:0},children:"Cost info unavailable"})})]})}),U&&(0,o.jsx)("div",{style:{backgroundColor:"#fef3c7",borderRadius:"8px",padding:"16px",border:"1px solid #e5e7eb"},children:(0,o.jsxs)("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"flex-start"},children:[(0,o.jsxs)("div",{children:[(0,o.jsxs)("h3",{style:{fontSize:"16px",fontWeight:"600",color:"#111827",margin:"0 0 4px 0"},children:["AD&D: ",U.name||U.planName||"Selected Plan"]}),(0,o.jsx)("p",{style:{fontSize:"14px",color:"#92400e",margin:0,fontWeight:"500"},children:"Accident Coverage"})]}),(0,o.jsx)("div",{style:{textAlign:"right"},children:(0,o.jsx)("p",{style:{fontSize:"16px",fontWeight:"600",color:"#d97706",margin:0},children:"Cost info unavailable"})})]})})]}),(0,o.jsx)("div",{style:{backgroundColor:"#fef3c7",borderRadius:"6px",padding:"12px",marginTop:"16px",border:"1px solid #fbbf24"},children:(0,o.jsx)("p",{style:{fontSize:"14px",color:"#92400e",margin:0},children:"⚠️ Cost information is not available. Please contact your HR administrator for pricing details."})})]})]})})(),_.length>0&&(0,o.jsxs)("div",{style:{marginTop:"16px",paddingTop:"16px",borderTop:"1px solid #e5e7eb",display:"none"},children:[(0,o.jsx)("h3",{style:{fontSize:"16px",fontWeight:"600",color:"#111827",marginBottom:"12px"},children:"Family Contribution Breakdown:"}),(0,o.jsxs)("div",{style:{display:"flex",flexDirection:"column",gap:"8px"},children:[_.map((e,n)=>(0,o.jsxs)("div",{style:{backgroundColor:"#f9fafb",padding:"12px",borderRadius:"6px",border:"1px solid #e5e7eb"},children:[(0,o.jsxs)("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",marginBottom:"8px"},children:[(0,o.jsxs)("span",{style:{fontSize:"14px",fontWeight:"600",color:"#111827"},children:[e.planType,": ",e.planName]}),(0,o.jsxs)("span",{style:{fontSize:"14px",fontWeight:"600",color:"#2563eb"},children:["You pay: $",e.employeeCost.toFixed(2)]})]}),(0,o.jsxs)("div",{style:{display:"grid",gridTemplateColumns:"1fr 1fr",gap:"8px",fontSize:"12px"},children:[(0,o.jsxs)("div",{style:{display:"flex",alignItems:"center"},children:[(0,o.jsx)("span",{style:{color:"#6b7280"},children:"Employer pays: "}),(0,o.jsxs)("span",{style:{color:"#10b981",fontWeight:"500"},children:["$",e.employerCost.toFixed(2)]})]}),(0,o.jsxs)("div",{style:{display:"flex",alignItems:"center"},children:[(0,o.jsx)("span",{style:{color:"#6b7280"},children:"You pay: "}),(0,o.jsxs)("span",{style:{color:"#dc2626",fontWeight:"500"},children:["$",e.employeeCost.toFixed(2)]})]})]})]},n)),(0,o.jsxs)("div",{style:{backgroundColor:"#eff6ff",padding:"12px",borderRadius:"6px",border:"1px solid #bfdbfe",marginTop:"8px"},children:[(0,o.jsxs)("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",marginBottom:"8px"},children:[(0,o.jsx)("span",{style:{fontSize:"14px",fontWeight:"600",color:"#111827"},children:"Total Family Coverage"}),(0,o.jsxs)("span",{style:{fontSize:"14px",fontWeight:"600",color:"#2563eb"},children:["You pay: $",q.toFixed(2)]})]}),(0,o.jsxs)("div",{style:{display:"grid",gridTemplateColumns:"1fr 1fr",gap:"8px",fontSize:"12px"},children:[(0,o.jsxs)("div",{style:{display:"flex",alignItems:"center"},children:[(0,o.jsx)("span",{style:{color:"#6b7280"},children:"Total employer contribution: "}),(0,o.jsxs)("span",{style:{color:"#10b981",fontWeight:"600"},children:["$",$.toFixed(2)]})]}),(0,o.jsxs)("div",{style:{display:"flex",alignItems:"center"},children:[(0,o.jsx)("span",{style:{color:"#6b7280"},children:"Total you pay: "}),(0,o.jsxs)("span",{style:{color:"#dc2626",fontWeight:"600"},children:["$",q.toFixed(2)]})]})]})]})]})]}),(0,o.jsxs)("div",{style:{marginTop:"16px",paddingTop:"16px",borderTop:"1px solid #e5e7eb",display:"none"},children:[(0,o.jsx)("h3",{style:{fontSize:"16px",fontWeight:"600",color:"#111827",marginBottom:"12px"},children:n.dependents&&n.dependents.length>0?"Family Members Covered:":"Coverage For:"}),(0,o.jsxs)("div",{style:{display:"flex",flexDirection:"column",gap:"8px"},children:[(0,o.jsxs)("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",backgroundColor:"#f0f9ff",padding:"12px",borderRadius:"6px",border:"1px solid #bfdbfe"},children:[(0,o.jsxs)("div",{children:[(0,o.jsx)("span",{style:{fontSize:"14px",fontWeight:"600",color:"#111827"},children:d.name||"Myself"}),(0,o.jsx)("span",{style:{fontSize:"14px",color:"#6b7280",marginLeft:"8px"},children:"(Employee)"})]}),(0,o.jsx)("span",{style:{fontSize:"12px",color:"#10b981",fontWeight:"500"},children:"✓ Covered"})]}),n.dependents&&n.dependents.map((e,n)=>(0,o.jsxs)("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",backgroundColor:"#f9fafb",padding:"12px",borderRadius:"6px",border:"1px solid #e5e7eb"},children:[(0,o.jsxs)("div",{children:[(0,o.jsx)("span",{style:{fontSize:"14px",fontWeight:"500",color:"#111827"},children:e.name}),(0,o.jsxs)("span",{style:{fontSize:"14px",color:"#6b7280",marginLeft:"8px"},children:["(","Spouse"===e.relationship?"Spouse/Partner":"Child",")"]})]}),(0,o.jsx)("span",{style:{fontSize:"12px",color:"#10b981",fontWeight:"500"},children:"✓ Covered"})]},n))]})]}),(_.length>0||Z.length>0)&&(0,o.jsxs)("div",{style:{display:"flex",gap:"12px",marginTop:"24px"},children:[(0,o.jsxs)("button",{onClick:r,style:{flex:1,display:"flex",alignItems:"center",justifyContent:"center",gap:"8px",padding:"12px 20px",backgroundColor:"white",border:"1px solid #d1d5db",borderRadius:"8px",color:"#374151",cursor:"pointer",fontWeight:"500",fontSize:"16px"},children:[(0,o.jsx)(ea.Z,{size:16}),"Make Changes"]}),(0,o.jsxs)("button",{onClick:E,disabled:f,style:{flex:2,display:"flex",alignItems:"center",justifyContent:"center",gap:"8px",padding:"12px 20px",backgroundColor:f?"#666666":"#1f2937",border:"none",borderRadius:"8px",color:"white",cursor:f?"not-allowed":"pointer",fontWeight:"500",fontSize:"16px",opacity:f?.7:1,transition:"all 0.2s ease"},children:[(0,o.jsx)(g.Z,{size:16}),f?"Creating Enrollments...":"Confirm Enrollment"]})]}),0===_.length&&0===Z.length&&(0,o.jsxs)("div",{style:{backgroundColor:"#fef3c7",borderRadius:"8px",padding:"20px",marginTop:"24px",border:"1px solid #fbbf24",textAlign:"center"},children:[(0,o.jsx)("h3",{style:{fontSize:"18px",fontWeight:"600",color:"#92400e",margin:"0 0 8px 0"},children:"No Plans Selected"}),(0,o.jsx)("p",{style:{color:"#92400e",margin:0},children:"You haven't selected any benefit plans or waived any coverages yet. Go back to make your selections."})]})]}),y&&(0,o.jsx)("div",{style:{position:"fixed",top:0,left:0,right:0,bottom:0,backgroundColor:"rgba(0, 0, 0, 0.5)",display:"flex",alignItems:"center",justifyContent:"center",zIndex:1e3},children:(0,o.jsxs)("div",{style:{backgroundColor:"white",borderRadius:"12px",padding:"32px",maxWidth:"600px",width:"90%",boxShadow:"0 20px 25px -5px rgba(0, 0, 0, 0.1)",textAlign:"center"},children:[(0,o.jsx)("div",{style:{width:"60px",height:"60px",backgroundColor:"#ef4444",borderRadius:"50%",display:"flex",alignItems:"center",justifyContent:"center",margin:"0 auto 20px"},children:(0,o.jsx)("span",{style:{fontSize:"32px",color:"white"},children:"⚠️"})}),(0,o.jsx)("h3",{style:{fontSize:"20px",fontWeight:"600",color:"#111827",margin:"0 0 12px 0"},children:"Enrollment Error"}),(0,o.jsx)("p",{style:{color:"#6b7280",margin:"0 0 24px 0",lineHeight:"1.5",whiteSpace:"pre-line"},children:v}),(0,o.jsx)("button",{onClick:()=>{b(!1)},style:{padding:"12px 24px",backgroundColor:"#dc2626",border:"none",borderRadius:"8px",color:"white",cursor:"pointer",fontWeight:"500",fontSize:"16px"},children:"Try Again"})]})}),(0,o.jsx)(ej.Z,{isOpen:j,onClose:()=>S(!1),onSignatureComplete:e=>{console.log("✅ Signature completed, proceeding with enrollment..."),w(!0),S(!1),localStorage.setItem("enrollmentSignatureRef",e),T()},employeeName:d.name||"Employee"})]})},ek=t(45736),eI=t(99049),eP=t(46747),eW=t(54007),eE=e=>{let{enrollmentData:n,planAssignments:t={}}=e,r=(0,ei.useRouter)(),[i,a]=(0,l.useState)(!1),[s,p]=(0,l.useState)(!1),[u,g]=(0,l.useState)(null),x=(0,es.C)(e=>e.user.userProfile),f=()=>{try{let e=Object.values(t).flat();if(0===e.length)return console.log("\uD83D\uDD0D No plan assignments found, using default date"),"January 1, 2025";let n=[];for(let t of e){let e=t.assignment||t,o=e.startDate||e.effectiveDate||e.coverageStartDate;if(o){let e=new Date(o);isNaN(e.getTime())||n.push(e)}}if(0===n.length)return console.log("\uD83D\uDD0D No valid start dates found in plan assignments, using default"),"January 1, 2025";let o=new Date(Math.min(...n.map(e=>e.getTime()))).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"});return console.log("\uD83D\uDD0D Dynamic benefits start date calculated:",o),console.log("\uD83D\uDD0D Plan assignments used for date calculation:",e.length,"assignments"),o}catch(e){return console.error("❌ Error getting benefits start date:",e),"January 1, 2025"}};return(0,l.useEffect)(()=>{console.log("\uD83D\uDD0D ConfirmationPage - Plan assignments received:",t),console.log("\uD83D\uDD0D ConfirmationPage - Plan assignment keys:",Object.keys(t));let e=localStorage.getItem("enrollmentSummarySnapshot");if(console.log("\uD83D\uDD0D Checking for stored enrollment summary..."),e)try{let n=JSON.parse(e);if(g(n),console.log("\uD83D\uDCF8 Loaded enrollment summary snapshot:",n),console.log("\uD83D\uDEE1️ Life insurance data in summary:",{lifePlan:n.lifePlan,lifeWaived:n.lifeWaived,lifeWaiveReason:n.lifeWaiveReason,hasLifePlan:!!n.lifePlan,lifePlanKeys:n.lifePlan?Object.keys(n.lifePlan):"null"}),console.log("\uD83D\uDC53 Vision data in summary:",{visionPlan:n.visionPlan,visionWaived:n.visionWaived,visionWaiveReason:n.visionWaiveReason,hasVisionPlan:!!n.visionPlan,visionPlanKeys:n.visionPlan?Object.keys(n.visionPlan):"null"}),console.log("\uD83E\uDDB7 Dental data in summary:",{dentalPlan:n.dentalPlan,dentalWaived:n.dentalWaived,dentalWaiveReason:n.dentalWaiveReason,hasDentalPlan:!!n.dentalPlan}),console.log("\uD83D\uDEE1️ AD&D data in summary:",{addPlan:n.addPlan,addWaived:n.addWaived,addWaiveReason:n.addWaiveReason,hasADDPlan:!!n.addPlan,addPlanKeys:n.addPlan?Object.keys(n.addPlan):"null"}),console.log("\uD83C\uDFAF Plan display logic check:",{shouldShowDental:!!(n.dentalPlan&&!n.dentalWaived),shouldShowVision:!!(n.visionPlan&&!n.visionWaived),shouldShowLife:!!(n.lifePlan&&!n.lifeWaived),shouldShowADD:!!(n.addPlan&&!n.addWaived),shouldShowDentalWaived:!!n.dentalWaived,shouldShowVisionWaived:!!n.visionWaived,shouldShowLifeWaived:!!n.lifeWaived,shouldShowADDWaived:!!n.addWaived}),console.log("\uD83D\uDD0D Detailed plan analysis:"),console.log("  Dental Plan:",n.dentalPlan?"EXISTS":"NULL","| Waived:",n.dentalWaived?"YES":"NO"),console.log("  Vision Plan:",n.visionPlan?"EXISTS":"NULL","| Waived:",n.visionWaived?"YES":"NO"),console.log("  Life Plan:",n.lifePlan?"EXISTS":"NULL","| Waived:",n.lifeWaived?"YES":"NO"),console.log("  AD&D Plan:",n.addPlan?"EXISTS":"NULL","| Waived:",n.addWaived?"YES":"NO"),!n.addPlan&&!n.addWaived){console.log("\uD83D\uDD27 AD&D data missing from snapshot, checking localStorage directly...");let e=localStorage.getItem("selectedADDPlan"),t=localStorage.getItem("addWaived");if(e)try{let t=JSON.parse(e);console.log("\uD83D\uDD27 Found AD&D plan in localStorage:",t),n.addPlan=t}catch(e){console.error("❌ Error parsing AD&D plan from localStorage:",e)}"true"===t&&(console.log("\uD83D\uDD27 Found AD&D waived in localStorage"),n.addWaived=!0,n.addWaiveReason=localStorage.getItem("addWaiveReason")),g({...n}),console.log("\uD83D\uDD27 Updated enrollment summary with AD&D data:",n)}}catch(e){console.error("❌ Error parsing enrollment summary:",e)}else if(console.warn("⚠️ No enrollment summary snapshot found in localStorage"),n){console.log("\uD83D\uDD04 Attempting to use enrollmentData as fallback:",n);let e={dentalPlan:n.selectedDental,visionPlan:n.selectedVision,lifePlan:n.selectedLife,addPlan:n.selectedADD,dependents:n.dependents,selectedCoverageTier:n.familyMembers||"Employee Only",enrollmentDate:new Date().toISOString(),dentalWaived:"true"===localStorage.getItem("dentalWaived"),visionWaived:"true"===localStorage.getItem("visionWaived"),lifeWaived:"true"===localStorage.getItem("lifeWaived"),addWaived:"true"===localStorage.getItem("addWaived"),dentalWaiveReason:localStorage.getItem("dentalWaiveReason")||void 0,visionWaiveReason:localStorage.getItem("visionWaiveReason")||void 0,lifeWaiveReason:localStorage.getItem("lifeWaiveReason")||void 0,addWaiveReason:localStorage.getItem("addWaiveReason")||void 0};g(e),console.log("\uD83D\uDD04 Using fallback enrollment summary:",e)}},[n,t]),(0,o.jsxs)("div",{style:{display:"flex",flexDirection:"column",gap:"24px"},children:[(0,o.jsxs)("div",{style:{display:"flex",gap:"16px"},children:[(0,o.jsx)("div",{style:{width:"40px",height:"40px",borderRadius:"50%",display:"flex",alignItems:"center",justifyContent:"center",flexShrink:0,overflow:"hidden"},children:(0,o.jsx)(h.default,{src:"/brea.png",alt:"Brea - AI Benefits Assistant",width:40,height:40,style:{borderRadius:"50%"}})}),(0,o.jsxs)("div",{style:{backgroundColor:"#f9fafb",borderRadius:"8px",padding:"16px",maxWidth:"1000px"},children:[(0,o.jsx)("p",{style:{color:"#374151",lineHeight:"1.6",margin:0,fontWeight:"bold"},children:"\uD83C\uDF89 Congratulations—you're enrolled!"}),(0,o.jsx)("p",{style:{color:"#374151",lineHeight:"1.6",margin:"8px 0 0 0"},children:"Take a look at the tips and follow-up actions to make the most of your benefits."})]})]}),(0,o.jsxs)("div",{style:{backgroundColor:"white",borderRadius:"8px",border:"1px solid #e5e7eb",padding:"24px",boxShadow:"0 1px 3px 0 rgba(0, 0, 0, 0.1)"},children:[u&&(0,o.jsxs)("div",{style:{backgroundColor:"#f0f9ff",borderRadius:"8px",padding:"20px",marginBottom:"24px",border:"1px solid #bfdbfe"},children:[(0,o.jsxs)("div",{style:{display:"flex",alignItems:"center",gap:"8px",marginBottom:"16px"},children:[(0,o.jsx)("span",{style:{fontSize:"16px"},children:"\uD83D\uDCCB"}),(0,o.jsx)("h3",{style:{fontSize:"18px",fontWeight:"600",color:"#111827",margin:0},children:"Your Enrollment Summary:"})]}),(()=>{let e=et();return e.selectedPlans&&e.selectedPlans.length>0||e.waivedCoverages&&e.waivedCoverages.length>0?(0,o.jsx)(eC,{showCosts:!1,showWaived:!0,style:"confirmation"}):(0,o.jsxs)("div",{style:{display:"flex",flexDirection:"column",gap:"12px"},children:[u.dentalPlan&&!u.dentalWaived&&(0,o.jsxs)("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center"},children:[(0,o.jsx)("span",{style:{color:"#374151",fontSize:"14px",fontWeight:"500"},children:"\uD83E\uDDB7 Dental Plan:"}),(0,o.jsx)("span",{style:{color:"#111827",fontSize:"14px",fontWeight:"600"},children:u.dentalPlan.name})]}),u.visionPlan&&!u.visionWaived&&(0,o.jsxs)("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center"},children:[(0,o.jsx)("span",{style:{color:"#374151",fontSize:"14px",fontWeight:"500"},children:"\uD83D\uDC53 Vision Plan:"}),(0,o.jsx)("span",{style:{color:"#111827",fontSize:"14px",fontWeight:"600"},children:u.visionPlan.name})]}),u.lifePlan&&!u.lifeWaived&&(0,o.jsxs)("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center"},children:[(0,o.jsx)("span",{style:{color:"#374151",fontSize:"14px",fontWeight:"500"},children:"\uD83D\uDEE1️ Life Insurance:"}),(0,o.jsx)("span",{style:{color:"#111827",fontSize:"14px",fontWeight:"600"},children:u.lifePlan.name||u.lifePlan.planName||"Life Insurance Plan"})]}),u.addPlan&&!u.addWaived&&(0,o.jsxs)("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center"},children:[(0,o.jsx)("span",{style:{color:"#374151",fontSize:"14px",fontWeight:"500"},children:"\uD83D\uDEE1️ Accidental Death & Dismemberment (AD&D):"}),(0,o.jsx)("span",{style:{color:"#111827",fontSize:"14px",fontWeight:"600"},children:u.addPlan.name||u.addPlan.planName||"AD&D Plan"})]}),u.dentalWaived&&(0,o.jsxs)("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center"},children:[(0,o.jsx)("span",{style:{color:"#dc2626",fontSize:"14px",fontWeight:"500"},children:"\uD83E\uDDB7 Dental:"}),(0,o.jsx)("span",{style:{color:"#dc2626",fontSize:"14px",fontWeight:"600"},children:"Waived"})]}),u.visionWaived&&(0,o.jsxs)("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center"},children:[(0,o.jsx)("span",{style:{color:"#dc2626",fontSize:"14px",fontWeight:"500"},children:"\uD83D\uDC53 Vision:"}),(0,o.jsx)("span",{style:{color:"#dc2626",fontSize:"14px",fontWeight:"600"},children:"Waived"})]}),u.lifeWaived&&(0,o.jsxs)("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center"},children:[(0,o.jsx)("span",{style:{color:"#dc2626",fontSize:"14px",fontWeight:"500"},children:"\uD83D\uDEE1️ Life Insurance:"}),(0,o.jsx)("span",{style:{color:"#dc2626",fontSize:"14px",fontWeight:"600"},children:"Waived"})]}),u.addWaived&&(0,o.jsxs)("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center"},children:[(0,o.jsx)("span",{style:{color:"#dc2626",fontSize:"14px",fontWeight:"500"},children:"\uD83D\uDEE1️ Accidental Death & Dismemberment (AD&D):"}),(0,o.jsx)("span",{style:{color:"#dc2626",fontSize:"14px",fontWeight:"600"},children:"Waived"})]}),(0,o.jsxs)("div",{style:{marginTop:"8px",paddingTop:"12px",borderTop:"1px solid #e5e7eb"},children:[(0,o.jsxs)("span",{style:{color:"#374151",fontSize:"14px",fontWeight:"500",marginBottom:"8px",display:"block"},children:["\uD83D\uDC68‍\uD83D\uDC69‍\uD83D\uDC67‍\uD83D\uDC66 ",u.dependents&&u.dependents.length>0?"Family Members Covered:":"Coverage For:"]}),(0,o.jsx)("div",{style:{marginLeft:"16px",marginBottom:"4px"},children:(0,o.jsxs)("span",{style:{color:"#111827",fontSize:"14px",fontWeight:"600"},children:[x.name||"Myself"," (Employee)"]})}),u.dependents&&u.dependents.map((e,n)=>(0,o.jsx)("div",{style:{marginLeft:"16px",marginBottom:"4px"},children:(0,o.jsxs)("span",{style:{color:"#111827",fontSize:"14px"},children:[e.name," (",e.relationship,")"]})},n))]})]})})()]}),u&&!u.dentalPlan&&!u.dentalWaived&&!u.visionPlan&&!u.visionWaived&&!u.lifePlan&&!u.lifeWaived&&!u.addPlan&&!u.addWaived&&(0,o.jsxs)("div",{style:{backgroundColor:"#fef2f2",borderRadius:"8px",padding:"20px",marginBottom:"24px",border:"1px solid #fca5a5"},children:[(0,o.jsxs)("div",{style:{display:"flex",alignItems:"center",gap:"8px",marginBottom:"16px"},children:[(0,o.jsx)("span",{style:{fontSize:"16px"},children:"⚠️"}),(0,o.jsx)("h3",{style:{fontSize:"18px",fontWeight:"600",color:"#dc2626",margin:0},children:"No Enrollment Data Found"})]}),(0,o.jsx)("p",{style:{color:"#dc2626",fontSize:"14px",margin:0},children:"It appears no plan selections or waive decisions were recorded. Please contact HR or try enrolling again."})]}),(0,o.jsxs)("div",{style:{backgroundColor:"#f8fafc",borderRadius:"8px",padding:"20px",marginBottom:"24px"},children:[(0,o.jsxs)("div",{style:{display:"flex",alignItems:"center",gap:"8px",marginBottom:"16px"},children:[(0,o.jsx)("span",{style:{fontSize:"16px"},children:"\uD83D\uDCCB"}),(0,o.jsx)("h3",{style:{fontSize:"18px",fontWeight:"600",color:"#111827",margin:0},children:"Next Steps & Pro Tips:"})]}),(0,o.jsxs)("div",{style:{display:"flex",flexDirection:"column",gap:"12px"},children:[(0,o.jsxs)("div",{style:{display:"flex",alignItems:"flex-start",gap:"12px"},children:[(0,o.jsx)(c.Z,{style:{width:"16px",height:"16px",color:"#6b7280",marginTop:"2px",flexShrink:0}}),(0,o.jsx)("span",{style:{color:"#374151",fontSize:"14px"},children:"Need a dentist? Find one near you in your member portal"})]}),(0,o.jsxs)("div",{style:{display:"flex",alignItems:"flex-start",gap:"12px"},children:[(0,o.jsx)(d.Z,{style:{width:"16px",height:"16px",color:"#6b7280",marginTop:"2px",flexShrink:0}}),(0,o.jsx)("span",{style:{color:"#374151",fontSize:"14px"},children:"Use your vision benefit for a free exam & $150 frames"})]}),(0,o.jsxs)("div",{style:{display:"flex",alignItems:"flex-start",gap:"12px"},children:[(0,o.jsx)("span",{style:{fontSize:"14px",marginTop:"2px"},children:"\uD83D\uDCB0"}),(0,o.jsx)("span",{style:{color:"#374151",fontSize:"14px"},children:"Set up your HSA/FSA to save on taxes"})]}),(0,o.jsxs)("div",{style:{display:"flex",alignItems:"flex-start",gap:"12px"},children:[(0,o.jsx)(ek.Z,{style:{width:"16px",height:"16px",color:"#6b7280",marginTop:"2px",flexShrink:0}}),(0,o.jsx)("span",{style:{color:"#374151",fontSize:"14px"},children:"Download your insurance apps for easy access"})]}),(0,o.jsxs)("div",{style:{display:"flex",alignItems:"flex-start",gap:"12px"},children:[(0,o.jsx)(c.Z,{style:{width:"16px",height:"16px",color:"#6b7280",marginTop:"2px",flexShrink:0}}),(0,o.jsx)("span",{style:{color:"#374151",fontSize:"14px"},children:"Schedule your preventive care visits early in the year"})]})]})]}),(0,o.jsxs)("div",{style:{display:"flex",gap:"12px",justifyContent:"center",flexWrap:"wrap"},children:[(0,o.jsxs)("button",{onClick:()=>{var e;console.log("\uD83E\uDDF9 Final cleanup - removing remaining enrollment data..."),["selectedDentalPlan","selectedVisionPlan","selectedLifePlan","selectedADDPlan","dentalWaived","visionWaived","lifeWaived","addWaived","dentalWaiveReason","visionWaiveReason","lifeWaiveReason","addWaiveReason","enrollmentSummarySnapshot"].forEach(e=>{localStorage.removeItem(e)}),console.log("✅ Final cleanup completed");let n=null==x?void 0:null===(e=x.role)||void 0===e?void 0:e.toLowerCase();"broker"===n||"admin"===n?r.push("/ai-enroller"):r.push("/dashboard")},style:{display:"flex",alignItems:"center",gap:"8px",padding:"12px 20px",backgroundColor:"#000000",border:"none",borderRadius:"8px",color:"white",cursor:"pointer",fontWeight:"600",fontSize:"14px",transition:"all 0.2s ease",boxShadow:"0 2px 4px rgba(0, 0, 0, 0.1)"},onMouseOver:e=>{e.currentTarget.style.backgroundColor="#374151",e.currentTarget.style.transform="translateY(-1px)",e.currentTarget.style.boxShadow="0 4px 8px rgba(0, 0, 0, 0.15)"},onMouseOut:e=>{e.currentTarget.style.backgroundColor="#000000",e.currentTarget.style.transform="translateY(0)",e.currentTarget.style.boxShadow="0 2px 4px rgba(0, 0, 0, 0.1)"},children:[(0,o.jsx)(eI.Z,{size:16}),"Return to Home"]}),(0,o.jsxs)("button",{onClick:()=>{p(!0)},style:{display:"flex",alignItems:"center",gap:"8px",padding:"12px 20px",backgroundColor:"white",border:"1px solid #d1d5db",borderRadius:"8px",color:"#374151",cursor:"pointer",fontSize:"14px",fontWeight:"500"},onMouseOver:e=>e.currentTarget.style.backgroundColor="#f9fafb",onMouseOut:e=>e.currentTarget.style.backgroundColor="white",children:[(0,o.jsx)("span",{style:{color:"#2563eb"},children:"❓"}),"Ask Questions"]}),(0,o.jsxs)("button",{onClick:()=>{let e=encodeURIComponent("Benefits Enrollment Summary"),n=encodeURIComponent("Please find my benefits enrollment summary attached.");window.location.href="mailto:?subject=".concat(e,"&body=").concat(n)},style:{display:"flex",alignItems:"center",gap:"8px",padding:"12px 20px",backgroundColor:"white",border:"1px solid #d1d5db",borderRadius:"8px",color:"#374151",cursor:"pointer",fontSize:"14px",fontWeight:"500"},onMouseOver:e=>e.currentTarget.style.backgroundColor="#f9fafb",onMouseOut:e=>e.currentTarget.style.backgroundColor="white",children:[(0,o.jsx)(eP.Z,{size:16,style:{color:"#6b7280"}}),"Email Summary"]}),(0,o.jsxs)("button",{onClick:()=>{if(!u){console.warn("No enrollment summary data available for printing");return}let e=[],n=[],t=0,o=0,l=(e,n)=>{if(!e||!e.coverageTiers)return{employeeCost:0,employerCost:0,totalCost:0};let t=e.coverageTiers.find(e=>e.tierName===n);return t?{employeeCost:t.employeeCost||0,employerCost:t.employerCost||0,totalCost:t.totalCost||t.employeeCost+t.employerCost||0}:{employeeCost:e.cost||0,employerCost:0,totalCost:e.cost||0}};if(u.dentalWaived)n.push("Dental");else if(u.dentalPlan){let n=l(u.dentalPlan,u.selectedCoverageTier||"Employee Only");e.push({planName:u.dentalPlan.name||"Dental Plan",planType:"Dental",...n}),t+=n.employeeCost,o+=n.employerCost}if(u.visionWaived)n.push("Vision");else if(u.visionPlan){let n=l(u.visionPlan,u.selectedCoverageTier||"Employee Only");e.push({planName:u.visionPlan.name||"Vision Plan",planType:"Vision",...n}),t+=n.employeeCost,o+=n.employerCost}if(u.lifeWaived)n.push("Life Insurance");else if(u.lifePlan){let n=l(u.lifePlan,u.selectedCoverageTier||"Employee Only");e.push({planName:u.lifePlan.name||"Life Insurance Plan",planType:"Life Insurance",...n}),t+=n.employeeCost,o+=n.employerCost}if(u.addWaived)n.push("AD&D");else if(u.addPlan){let n=l(u.addPlan,u.selectedCoverageTier||"Employee Only");e.push({planName:u.addPlan.name||"AD&D Plan",planType:"AD&D",...n}),t+=n.employeeCost,o+=n.employerCost}let r=t+o,i="\n      <html>\n        <head>\n          <title>Benefits Enrollment Summary</title>\n          <style>\n            @import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap');\n\n            * {\n              margin: 0;\n              padding: 0;\n              box-sizing: border-box;\n            }\n\n            body {\n              font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;\n              margin: 0;\n              padding: 40px;\n              color: #1f2937;\n              line-height: 1.6;\n              background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n              min-height: 100vh;\n            }\n\n            .container {\n              max-width: 800px;\n              margin: 0 auto;\n              background: white;\n              border-radius: 16px;\n              box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);\n              overflow: hidden;\n            }\n\n            .header {\n              background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n              color: white;\n              text-align: center;\n              padding: 40px 30px;\n              position: relative;\n            }\n\n            .header h1 {\n              font-size: 32px;\n              font-weight: 700;\n              margin-bottom: 8px;\n            }\n\n            .header p {\n              font-size: 16px;\n              opacity: 0.9;\n            }\n\n            .content {\n              padding: 40px 30px;\n            }\n\n            .coverage-info {\n              background: #f8fafc;\n              padding: 20px;\n              border-radius: 12px;\n              margin-bottom: 30px;\n              border-left: 4px solid #3b82f6;\n            }\n\n            .coverage-info h3 {\n              color: #1e40af;\n              font-size: 18px;\n              font-weight: 600;\n              margin-bottom: 8px;\n            }\n\n            .plan-section {\n              margin: 20px 0;\n              padding: 24px;\n              border: 1px solid #e5e7eb;\n              border-radius: 12px;\n              background: white;\n              box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\n            }\n\n            .plan-title {\n              font-weight: 600;\n              font-size: 18px;\n              margin-bottom: 16px;\n              color: #1f2937;\n              display: flex;\n              align-items: center;\n              gap: 8px;\n            }\n\n            .cost-breakdown {\n              background: #f9fafb;\n              padding: 16px;\n              border-radius: 8px;\n              border: 1px solid #f3f4f6;\n            }\n\n            .cost-breakdown p {\n              margin: 8px 0;\n              display: flex;\n              justify-content: space-between;\n              align-items: center;\n            }\n\n            .cost-breakdown p:last-child {\n              text-align: right;\n            }\n\n            .total-section {\n              margin-top: 30px;\n              padding: 24px;\n              background: linear-gradient(135deg, #10b981 0%, #059669 100%);\n              color: white;\n              border-radius: 12px;\n              box-shadow: 0 4px 6px rgba(16, 185, 129, 0.25);\n            }\n\n            .total-section h3 {\n              font-size: 20px;\n              font-weight: 700;\n              margin-bottom: 16px;\n              text-align: center;\n            }\n\n            .total-section p {\n              margin: 12px 0;\n              display: flex;\n              justify-content: space-between;\n              align-items: center;\n              font-size: 16px;\n            }\n\n            .waived {\n              background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);\n              border-color: #fca5a5;\n              color: #991b1b;\n            }\n\n            .dependents-section {\n              margin-top: 30px;\n              padding: 24px;\n              background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);\n              border-radius: 12px;\n              border: 1px solid #bfdbfe;\n            }\n\n            .dependents-section h3 {\n              color: #1e40af;\n              font-size: 18px;\n              font-weight: 600;\n              margin-bottom: 16px;\n            }\n\n            .next-steps {\n              margin-top: 30px;\n              padding: 24px;\n              background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);\n              border-radius: 12px;\n              border: 1px solid #f59e0b;\n            }\n\n            .next-steps .plan-title {\n              color: #92400e;\n            }\n\n            .next-steps ul {\n              list-style: none;\n              padding: 0;\n            }\n\n            .next-steps li {\n              margin: 12px 0;\n              padding: 8px 0;\n              border-bottom: 1px solid rgba(146, 64, 14, 0.1);\n              color: #92400e;\n              font-weight: 500;\n            }\n\n            .next-steps li:last-child {\n              border-bottom: none;\n            }\n\n            @media print {\n              body {\n                background: white !important;\n                padding: 20px !important;\n              }\n\n              .container {\n                box-shadow: none !important;\n                border: 1px solid #e5e7eb;\n              }\n\n              .header {\n                background: #4f46e5 !important;\n                -webkit-print-color-adjust: exact;\n                color-adjust: exact;\n              }\n\n              .total-section {\n                background: #10b981 !important;\n                -webkit-print-color-adjust: exact;\n                color-adjust: exact;\n              }\n            }\n          </style>\n        </head>\n        <body>\n          <div class=\"container\">\n            <div class=\"header\">\n              <h1>\uD83C\uDF89 Benefits Enrollment Summary</h1>\n              <p><strong>Enrollment Date:</strong> ".concat(new Date(u.enrollmentDate||Date.now()).toLocaleDateString(),"</p>\n              <p><strong>Coverage Effective:</strong> ").concat(f(),'</p>\n            </div>\n\n            <div class="content">\n              <div class="coverage-info">\n                <h3>\uD83D\uDCCB Coverage Information</h3>\n                <p><strong>Coverage Tier:</strong> ').concat(u.selectedCoverageTier||"Employee Only","</p>\n              </div>\n\n          ").concat(e.map(e=>'\n            <div class="plan-section">\n              <div class="plan-title">'.concat(e.planType,": ").concat(e.planName,'</div>\n              <div class="cost-breakdown">\n                <p><strong>Total Cost:</strong> $').concat(e.totalCost.toFixed(2),"/paycheck</p>\n                <p><strong>Employer Contribution:</strong> $").concat(e.employerCost.toFixed(2),"</p>\n                <p><strong>Your Cost:</strong> $").concat(e.employeeCost.toFixed(2),"</p>\n              </div>\n            </div>\n          ")).join(""),"\n\n          ").concat(n.map(e=>'\n            <div class="plan-section waived">\n              <div class="plan-title">'.concat(e,": Waived</div>\n              <p>No coverage selected - $0.00/paycheck</p>\n            </div>\n          ")).join(""),'\n\n          <div class="total-section">\n            <h3>Total Summary</h3>\n            <p><strong>Total Plan Cost: $').concat(r.toFixed(2),"/paycheck</strong></p>\n            <p><strong>Employer Contribution: -$").concat(o.toFixed(2),"</strong></p>\n            <p><strong>Your Cost per Paycheck: $").concat(t.toFixed(2),'</strong></p>\n          </div>\n\n          <div class="dependents-section">\n            <h3>').concat(u.dependents&&u.dependents.length>0?"Family Members Covered":"Coverage For","</h3>\n            <p><strong>").concat(x.name||"Myself","</strong> (Employee)</p>\n            ").concat(u.dependents?u.dependents.map(e=>"\n              <p><strong>".concat(e.name,"</strong> (").concat(e.relationship,")</p>\n            ")).join(""):"",'\n          </div>\n\n              <div class="next-steps">\n                <div class="plan-title">\uD83D\uDCA1 Next Steps & Pro Tips</div>\n                <ul>\n                  <li>\uD83E\uDDB7 Find dentists in your member portal</li>\n                  <li>\uD83D\uDC53 Use vision benefit for free exam & $150 frames</li>\n                  <li>\uD83D\uDCB0 Set up HSA/FSA to save on taxes</li>\n                  <li>\uD83D\uDCF1 Download insurance apps for easy access</li>\n                  <li>\uD83D\uDDD3️ Schedule preventive care visits early</li>\n                </ul>\n              </div>\n            </div>\n          </div>\n        </body>\n      </html>\n    '),a=window.open("","_blank");a&&(a.document.write(i),a.document.close(),a.print())},style:{display:"flex",alignItems:"center",gap:"8px",padding:"12px 20px",backgroundColor:"white",border:"1px solid #d1d5db",borderRadius:"8px",color:"#374151",cursor:"pointer",fontSize:"14px",fontWeight:"500"},onMouseOver:e=>e.currentTarget.style.backgroundColor="#f9fafb",onMouseOut:e=>e.currentTarget.style.backgroundColor="white",children:[(0,o.jsx)(eW.Z,{size:16,style:{color:"#6b7280"}}),"Print Summary"]})]})]}),i&&(0,o.jsx)(D,{onClose:()=>a(!1)}),s&&(0,o.jsx)(C.Z,{isOpen:s,onClose:()=>p(!1)})]})},eT=()=>{let[e,n]=(0,l.useState)(!1);return(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)("div",{onClick:()=>{n(!0)},style:{position:"fixed",bottom:"24px",right:"24px",width:"60px",height:"60px",backgroundColor:"#000000",borderRadius:"50%",display:"flex",alignItems:"center",justifyContent:"center",cursor:"pointer",boxShadow:"0 4px 12px rgba(0, 0, 0, 0.15)",zIndex:1e3,transition:"all 0.3s ease",border:"2px solid #ffffff"},onMouseOver:e=>{e.currentTarget.style.transform="scale(1.1)",e.currentTarget.style.boxShadow="0 6px 20px rgba(0, 0, 0, 0.25)"},onMouseOut:e=>{e.currentTarget.style.transform="scale(1)",e.currentTarget.style.boxShadow="0 4px 12px rgba(0, 0, 0, 0.15)"},children:(0,o.jsx)(h.default,{src:"/brea.png",alt:"Chat with Brea",width:40,height:40,style:{borderRadius:"50%",objectFit:"cover"}})}),(0,o.jsxs)("div",{style:{position:"fixed",bottom:"32px",right:"92px",backgroundColor:"#000000",color:"white",padding:"8px 12px",borderRadius:"8px",fontSize:"14px",fontWeight:"500",zIndex:999,opacity:e?0:1,visibility:e?"hidden":"visible",transition:"all 0.3s ease",whiteSpace:"nowrap",boxShadow:"0 2px 8px rgba(0, 0, 0, 0.15)"},children:["Chat with Brea",(0,o.jsx)("div",{style:{position:"absolute",top:"50%",right:"-6px",transform:"translateY(-50%)",width:0,height:0,borderLeft:"6px solid #000000",borderTop:"6px solid transparent",borderBottom:"6px solid transparent"}})]}),e&&(0,o.jsx)(C.Z,{isOpen:e,onClose:()=>{n(!1)}})]})},ez=t(99859);class eR{getHeaders(){let e={"Content-Type":"application/json"},n=localStorage.getItem("userid1")||localStorage.getItem("userId");return n&&(e["user-id"]=n),e}async getPlanById(e){try{let n=await fetch("".concat(this.apiBaseUrl,"/api/pre-enrollment/plans/").concat(e),{method:"GET",headers:this.getHeaders()});if(!n.ok)throw Error("HTTP error! status: ".concat(n.status));let t=await n.json();return{success:!0,data:t.plan}}catch(e){return console.error("Error fetching plan details:",e),{success:!1,error:"Failed to fetch plan details"}}}async getPlanAssignmentsByCompany(e){try{console.log("\uD83D\uDD0D Fetching plan assignments for company:",e);let n=new URLSearchParams;n.append("includeExpired","false");let t=await fetch("".concat(this.apiBaseUrl,"/api/pre-enrollment/plan-assignments/company/").concat(e,"?").concat(n.toString()),{method:"GET",headers:this.getHeaders()});if(!t.ok)throw Error("HTTP error! status: ".concat(t.status));let o=await t.json();console.log("\uD83D\uDCCB Plan assignments response:",o);let l=o.assignments||[];console.log("\uD83D\uDCCA Total assignments found:",l.length);let r=new Date,i=l.filter(e=>{let n=new Date(e.enrollmentStartDate),t=new Date(e.enrollmentEndDate),o=e.isActive&&n<=r&&t>=r;return o||console.log("⏰ Skipping assignment ".concat(e._id," - not in enrollment period:"),{enrollmentStart:n.toISOString(),enrollmentEnd:t.toISOString(),now:r.toISOString(),isActive:e.isActive}),o});console.log("\uD83D\uDCCA Assignments in enrollment period: ".concat(i.length," of ").concat(l.length));let a={};for(let e of i){console.log("\uD83D\uDD0D Processing assignment:",e._id,"planId:",e.planId);let n=await this.getPlanById(e.planId);if(n.success&&n.data){let t=n.data;console.log("✅ Plan details:",{planName:t.planName,coverageSubTypes:t.coverageSubTypes});let o={...t,assignment:e,id:e._id,name:t.planName,planCode:t.planCode,carrierName:t.carrierName,coverageType:t.coverageType,coverageSubTypes:t.coverageSubTypes,cost:this.calculatePlanCost(e),features:this.extractPlanFeatures(t,e)};t.coverageSubTypes&&Array.isArray(t.coverageSubTypes)&&t.coverageSubTypes.forEach(e=>{let n=e.toLowerCase();a[n]||(a[n]=[]),a[n].push(o)})}else console.warn("⚠️ Failed to fetch plan details for:",e.planId)}return console.log("\uD83C\uDFAF Grouped plans by subtype:",Object.keys(a)),{success:!0,data:a}}catch(e){return console.error("❌ Error fetching plan assignments:",e),{success:!1,error:e instanceof Error?e.message:"Failed to fetch plan assignments"}}}calculatePlanCost(e,n){try{var t;if(n&&e.coverageTiers){let t=e.coverageTiers.find(e=>e.tierName===n);if(t)return t.employeeCost||0}let o=null===(t=e.coverageTiers)||void 0===t?void 0:t.find(e=>{var n,t;return(null===(n=e.tierName)||void 0===n?void 0:n.toLowerCase().includes("employee only"))||(null===(t=e.tierName)||void 0===t?void 0:t.toLowerCase())==="employee"});if(o)return o.employeeCost||0;if(e.coverageTiers&&e.coverageTiers.length>0)return e.coverageTiers[0].employeeCost||0;return 0}catch(e){return console.warn("Error calculating plan cost:",e),0}}extractPlanFeatures(e,n){let t=[];return e.highlights&&Array.isArray(e.highlights)&&e.highlights.length>0?e.highlights:n.highlights&&Array.isArray(n.highlights)&&n.highlights.length>0?n.highlights:(e.metalTier&&t.push("".concat(e.metalTier," tier coverage")),e.carrierName&&t.push("".concat(e.carrierName," network")),n.coverageTiers&&n.coverageTiers.length>0&&t.push("".concat(n.coverageTiers.length," coverage tier options")),e.coverageType&&t.push("".concat(e.coverageType," benefits")),t.length>0?t:["Comprehensive coverage","Network benefits","Quality care"])}constructor(){this.apiBaseUrl="http://localhost:8080"}}let eA=new eR;var eF=t(88884),eB=t(47369);let eN=[{primary:"#1E90FF",accent:"#E6F0FF",name:"Blue"},{primary:"#32CD32",accent:"#E6FFE6",name:"Green"},{primary:"#FF8C00",accent:"#FFF3E6",name:"Orange"},{primary:"#800080",accent:"#F5E6FF",name:"Purple"}],eO={medical:"Medical",health:"Medical","health insurance":"Medical",dental:"Dental","dental plan":"Dental",vision:"Vision","vision plan":"Vision","term life":"Life Insurance",life:"Life Insurance","life insurance":"Life Insurance","supplemental life insurance":"Life Insurance","whole life":"Life Insurance","group (employer) life":"Life Insurance","accidental death & dismemberment (ad&d)":"AD&D Insurance","ad&d":"AD&D Insurance",add:"AD&D Insurance","short-term disability":"Disability Insurance","long-term disability":"Disability Insurance",std:"Disability Insurance",ltd:"Disability Insurance","hospital indemnity":"Voluntary Benefits","accident insurance":"Voluntary Benefits","critical illness insurance":"Voluntary Benefits","cancer insurance":"Voluntary Benefits"},eL=["Medical","Dental","Vision","Life Insurance","AD&D Insurance","Disability Insurance","Voluntary Benefits"];function eM(){let{logout:e}=(0,eB.a)();(0,es.C)(e=>e.user.userProfile);let[n,t]=(0,l.useState)(0),[h,m]=(0,l.useState)({familyMembers:"",expectedMedicalUsage:"",budgetPreference:"",chronicConditions:!1,prescriptionNeeds:!1,hasPreferredDoctors:!1}),[y,b]=(0,l.useState)(null),[v,D]=(0,l.useState)(!1),[j,S]=(0,l.useState)([]),[C,w]=(0,l.useState)([]),[I,P]=(0,l.useState)([]),W=(0,l.useRef)(null),[T,z]=(0,l.useState)({}),[R,A]=(0,l.useState)(!1),[F,B]=(0,l.useState)(null),[O,L]=(0,l.useState)([]),[M,H]=(0,l.useState)([]),U=e=>{switch(e){case"employee-only":default:return"Employee Only";case"employee-spouse":return"Employee + Spouse";case"employee-children":return"Employee + Child(ren)";case"employee-family":return"Family"}},_=[{id:"kickoff",title:"Welcome & Kickoff",icon:r.Z,completed:!1},{id:"personalization",title:"Smart Personalization",icon:i.Z,completed:!1},{id:"dependents",title:"Family Members",icon:a.Z,completed:!1},...O.map((e,n)=>({id:"plan-".concat(n),title:"".concat(e," Plan"),icon:"Dental"===e?s.Z:"Vision"===e?d.Z:"Life Insurance"===e?c.Z:"AD&D Insurance"===e?p.Z:u.Z,completed:!1})),{id:"summary",title:"Summary Review",icon:u.Z,completed:!1},{id:"confirmation",title:"Confirmation",icon:g.Z,completed:!1}];(0,l.useEffect)(()=>{(async()=>{try{let n=localStorage.getItem("userid1")||localStorage.getItem("userId");if(!n){console.warn("No user ID found in localStorage");return}console.log("\uD83D\uDD0D Fetching user details for:",n);let t=await fetch("".concat("http://localhost:8080","/employee"),{headers:{"user-id":n}});if(t.ok){let n=await t.json();console.log("\uD83D\uDC64 User details:",n);let o=n.currentUser;if(B(o),console.log("\uD83D\uDD0D Current user extracted:",o),console.log("\uD83C\uDFE2 Company ID from user:",null==o?void 0:o.companyId),o&&o.companyId){console.log("\uD83C\uDFE2 Fetching plan assignments for company:",o.companyId),A(!0);let n=await (0,eF.fH)(o.companyId,{includePlanData:!0});if(n.success&&n.data){console.log("✅ Plan assignments loaded:",n.data);let t=n.data.assignments||[],o={};for(let n of t)try{var e;console.log("\uD83D\uDD0D Processing assignment:",n._id,"planId:",n.planId);let t="string"==typeof n.planId?n.planId:(null===(e=n.planId)||void 0===e?void 0:e._id)||String(n.planId),l=await eA.getPlanById(t);if(l.success&&l.data){let e=l.data;console.log("✅ Plan details:",{planName:e.planName,coverageSubTypes:e.coverageSubTypes});let t={...e,assignment:n,id:n._id,name:e.planName,planCode:e.planCode,carrierName:e.carrierName,coverageType:e.coverageType,coverageSubTypes:e.coverageSubTypes,coverageTiers:n.coverageTiers,cost:eA.calculatePlanCost(n),features:eA.extractPlanFeatures(e,n)};e.coverageSubTypes&&Array.isArray(e.coverageSubTypes)&&e.coverageSubTypes.forEach(e=>{let n=e.toLowerCase();o[n]||(o[n]=[]),o[n].push(t)})}else console.warn("⚠️ Failed to fetch plan details for:",n.planId)}catch(e){console.error("❌ Error processing assignment:",n._id,e)}console.log("\uD83C\uDFAF Grouped plans by subtype:",Object.keys(o)),z(o);let l=function(e){let n=new Set;return Object.keys(e).forEach(t=>{let o=eO[t.toLowerCase().trim()];o&&e[t].length>0&&n.add(o)}),eL.filter(e=>n.has(e))}(o);console.log("\uD83D\uDCCB Extracted plan categories:",l),L(l)}else console.warn("⚠️ Failed to load plan assignments:",n.error),z({})}else console.warn("⚠️ User has no company ID")}else console.error("❌ Failed to fetch user details")}catch(e){console.error("❌ Error fetching user and plans:",e)}finally{A(!1)}})()},[]);let Z=e=>{if(e<3)return!0;let n=e-3,t=O.length;if(n>=0&&n<t){let e=O[n],t={Medical:"medical",Dental:"dental",Vision:"vision","Life Insurance":"life","AD&D Insurance":"add","Disability Insurance":"disability","Voluntary Benefits":"voluntary"}[e]||e.toLowerCase().replace(/\s+/g,""),o=localStorage.getItem("selected".concat(e.replace(/\s+/g,""),"Plan")),l=localStorage.getItem("".concat(t,"Waived")),r=null!==J(e),i=K(e);return null!==o||"true"===l||r||i}return!0},q=e=>{var n,t,o,l;let r=[],i=[];return(null==e?void 0:null===(n=e.details)||void 0===n?void 0:n.dateOfBirth)||(r.push("Date of Birth"),i.push("Date of birth is required for age-based cost calculations")),(null==e?void 0:null===(t=e.details)||void 0===t?void 0:t.hireDate)||(r.push("Hire Date"),i.push("Hire date is required for waiting period eligibility")),(null==e?void 0:null===(o=e.details)||void 0===o?void 0:o.employeeClassType)||(r.push("Employee Class Type"),i.push("Employee class type is required for plan eligibility")),(null==e?void 0:null===(l=e.details)||void 0===l?void 0:l.phoneNumber)||(r.push("Phone Number"),i.push("Phone number is required for enrollment communications")),{isValid:0===r.length,missingFields:r,errors:i}},$=()=>{if(2===n){let e=q(F);if(!e.isValid){let n=e.missingFields.join(", ");alert("Please complete your profile before continuing. Missing required fields: ".concat(n,'\n\nClick the "Complete Profile" button on the dependents page to update your information.'));return}console.log("Step 2 Next clicked, currentDependents:",I),X(I)}else n<_.length-1&&(Z(n)?t(n+1):alert("Please select a plan or choose to waive coverage before proceeding."))},Y=()=>{n>0&&t(n-1)},G=e=>{m(n=>({...n,...e}));let n=U(e.familyMembers||"employee-only");localStorage.setItem("selectedCoverageTier",n),console.log("\uD83C\uDFAF Stored coverage tier in localStorage:",n);let t=[];"employee-spouse"===e.familyMembers?t.push("spouse"):"employee-children"===e.familyMembers?t.push("children"):"employee-family"===e.familyMembers&&t.push("spouse","children"),S(t),b(et(e)),$()},X=e=>{console.log("handleDependentsConfirm called with:",e),w(e),t(n+1)},Q=()=>localStorage.getItem("userid1")||localStorage.getItem("userId"),ee=async()=>{try{let e=Q();if(!e){console.error("User ID not found");return}let n=await (0,N.A_)("/employee",{"user-id":e});(null==n?void 0:n.currentUser)&&(B(n.currentUser),console.log("✅ User details refreshed after profile update"))}catch(e){console.error("❌ Error refreshing user details:",e)}},en=()=>{Y()},et=e=>{let{expectedMedicalUsage:n,budgetPreference:t}=e;return{plan:{name:"Anthem PPO 035",cost:82.9,deductible:2e3,features:["Balanced cost and coverage","Deductible: $2,000","Covers your PCP visits at $25","Good for moderate usage & predictable costs"]},reason:"This plan offers the best balance of monthly cost and coverage for someone with moderate healthcare needs."}},eo=(e,n)=>{let t="selected".concat(e.charAt(0).toUpperCase()+e.slice(1));console.log("\uD83D\uDD04 Setting ".concat(t," with data:"),n),"life"===e&&console.log("\uD83D\uDEE1️ Life insurance plan selection details:",{planData:n,hasId:!!(null==n?void 0:n.id),hasName:!!(null==n?void 0:n.name),hasCoverageTiers:!!(null==n?void 0:n.coverageTiers),keys:n?Object.keys(n):"null"}),m(o=>{let l={...o,[t]:n};return console.log("\uD83D\uDCDD Updated user profile:",l),"life"===e&&console.log("\uD83D\uDEE1️ Life insurance in user profile:",l.selectedLife),l})},el=()=>{t(1)},ei=()=>{console.log("\uD83C\uDFAF Enrollment confirmed! Moving to confirmation page..."),console.log("\uD83D\uDD0D Current user profile before creating summary:",h);let e=(e,n)=>{if(n)return console.log("✅ Using userProfile data for ".concat(e,":"),n),n;let t=localStorage.getItem(e);if(t)try{let n=JSON.parse(t);return console.log("✅ Using localStorage data for ".concat(e,":"),n),n}catch(n){console.error("❌ Error parsing ".concat(e," from localStorage:"),n)}return console.log("⚠️ No data found for ".concat(e)),null},n={dentalPlan:e("selectedDentalPlan",h.selectedDental),visionPlan:e("selectedVisionPlan",h.selectedVision),lifePlan:e("selectedLifePlan",h.selectedLife),addPlan:e("selectedADDPlan",h.selectedADD),dependents:C,selectedCoverageTier:U(h.familyMembers||"employee-only"),enrollmentDate:new Date().toISOString(),dentalWaived:"true"===localStorage.getItem("dentalWaived"),visionWaived:"true"===localStorage.getItem("visionWaived"),lifeWaived:"true"===localStorage.getItem("lifeWaived"),addWaived:"true"===localStorage.getItem("addWaived"),dentalWaiveReason:localStorage.getItem("dentalWaiveReason"),visionWaiveReason:localStorage.getItem("visionWaiveReason"),lifeWaiveReason:localStorage.getItem("lifeWaiveReason"),addWaiveReason:localStorage.getItem("addWaiveReason")};console.log("\uD83D\uDD0D All plan data in enrollment summary:",{dentalPlan:n.dentalPlan,dentalWaived:n.dentalWaived,visionPlan:n.visionPlan,visionWaived:n.visionWaived,lifePlan:n.lifePlan,lifeWaived:n.lifeWaived,addPlan:n.addPlan,addWaived:n.addWaived,userProfileData:{selectedDental:h.selectedDental,selectedVision:h.selectedVision,selectedLife:h.selectedLife}}),localStorage.setItem("enrollmentSummarySnapshot",JSON.stringify(n)),console.log("\uD83D\uDCF8 Enrollment summary snapshot saved:",n),D(!0),$()};return(0,o.jsxs)("div",{className:"min-h-screen",style:{backgroundColor:"#f3f4f6",fontFamily:'"SF Pro", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif'},children:[(0,o.jsx)(ez.Z,{}),(0,o.jsxs)("div",{style:{maxWidth:"1024px",margin:"0 auto",padding:"clamp(16px, 4vw, 32px) clamp(12px, 3vw, 24px)",minHeight:"calc(100vh - 65px)"},children:[(0,o.jsxs)("div",{style:{display:"flex",alignItems:"center",justifyContent:"space-between",marginBottom:"24px",flexWrap:"wrap",gap:"16px"},children:[(0,o.jsx)("div",{style:{display:"flex",alignItems:"center",minWidth:"0",flex:"1"},children:(0,o.jsxs)("div",{style:{minWidth:"0"},children:[(0,o.jsx)("h1",{style:{fontSize:"clamp(20px, 5vw, 24px)",fontWeight:"600",color:"#111827",margin:0,fontFamily:"'SF Pro', 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif"},children:"Your Smart Benefits Assistant"}),(0,o.jsx)("p",{style:{fontSize:"clamp(12px, 3vw, 14px)",color:"#6b7280",margin:0,lineHeight:"1.4",fontFamily:"'SF Pro', 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif"},children:"Compare Plans. Ask Questions. Choose Smarter"})]})}),(0,o.jsx)("div",{style:{display:"flex",alignItems:"center",gap:"12px"}})]}),(0,o.jsxs)("div",{style:{backgroundColor:"white",borderRadius:"16px",border:"1px solid #e5e7eb",padding:"24px",marginBottom:"24px",boxShadow:"0 1px 3px rgba(0, 0, 0, 0.1)",marginLeft:"-20px",marginRight:"-20px",maxWidth:"calc(100% + 40px)"},children:[(0,o.jsxs)("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",marginBottom:"20px",flexWrap:"wrap",gap:"12px"},children:[(0,o.jsx)("h2",{style:{fontSize:"clamp(18px, 4vw, 20px)",fontWeight:"600",color:"#111827",margin:0,fontFamily:"sans-serif"},children:"Enrollment Progress"}),(0,o.jsxs)("span",{style:{fontSize:"clamp(12px, 3vw, 14px)",color:"#6b7280",backgroundColor:"#f3f4f6",padding:"6px 12px",borderRadius:"20px",fontWeight:"500",fontFamily:"sans-serif"},children:["Step ",n+1," of ",_.length]})]}),(0,o.jsx)("div",{style:{width:"100%",backgroundColor:"#e5e7eb",borderRadius:"12px",height:"8px",marginBottom:"24px"},children:(0,o.jsx)("div",{style:{backgroundColor:"#000000",height:"8px",borderRadius:"12px",transition:"all 0.4s ease",width:"".concat(n/(_.length-1)*100,"%")}})}),(0,o.jsx)("div",{className:"step-grid",style:{marginBottom:"8px"},children:_.map((e,t)=>{let l=e.icon,r=t===n,i=t<n,a=i?{backgroundColor:"#ddd6fe",color:"#6d28d9",border:"none"}:r?{backgroundColor:"#ede9fe",color:"#7c3aed",border:"none"}:{backgroundColor:"#f3f4f6",color:"#6b7280",border:"none"};return(0,o.jsxs)("div",{style:{display:"inline-flex",alignItems:"center",gap:"6px",padding:"8px 16px",borderRadius:"24px",fontSize:"13px",fontWeight:"450",transition:"all 0.2s ease",backgroundColor:a.backgroundColor,color:a.color,border:a.border,fontFamily:"sans-serif",width:"fit-content",justifyContent:"flex-start",whiteSpace:"nowrap",minWidth:"fit-content"},children:[(0,o.jsx)(l,{size:14}),(0,o.jsx)("span",{style:{lineHeight:"1.2"},children:e.title}),i&&(0,o.jsx)("div",{style:{display:"inline-flex",alignItems:"center",justifyContent:"center",width:"16px",height:"16px",backgroundColor:"#7c3aed",borderRadius:"50%",marginLeft:"8px",fontSize:"10px",fontWeight:"600",color:"white",lineHeight:"1"},children:"✓"})]},e.id)})})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("div",{ref:W,style:{width:"100%"},children:(()=>{switch(n){case 0:return(0,o.jsx)(k,{onNext:$});case 1:return(0,o.jsx)(E,{onComplete:G,initialData:h});case 2:let e=(()=>{let e=(null==h?void 0:h.familyMembers)||"employee-only";switch(console.log("\uD83D\uDD0D Coverage tier selected:",e),e){case"employee-only":return[];case"employee-spouse":return["spouse"];case"employee-children":return["children"];case"employee-family":return["spouse","children"];default:return console.log("⚠️ Unknown family members selection:",e),[]}})();return console.log("\uD83D\uDC65 Filtered dependents for coverage tier:",e),(0,o.jsx)(V,{selectedDependents:e,onConfirm:X,onBack:en,onDependentsChange:P,onProfileUpdate:ee});default:let t=n-3,l=O.length;if(t>=0&&t<l){let e=O[t],n=function(e,n){let t=[];return Object.entries(eO).forEach(o=>{let[l,r]=o;r===e&&n[l]&&t.push(...n[l])}),t}(e,T),l=function(e,n){let t=n.indexOf(e);return t>=0?t%eN.length:0}(e,O);return(0,o.jsx)(er,{category:e,plans:n,selectedCoverageTier:U(h.familyMembers||"employee-only"),themeIndex:l,onPlanSelect:n=>{eo({Medical:"medical",Dental:"dental",Vision:"vision","Life Insurance":"life","AD&D Insurance":"add"}[e]||"medical",n)}})}let r=3+l;if(n===r)return(0,o.jsx)(ew,{enrollmentData:{dentalPlan:h.selectedDental,visionPlan:h.selectedVision,lifePlan:h.selectedLife,addPlan:h.selectedADD,dependents:C},selectedCoverageTier:U(h.familyMembers||"employee-only"),onMakeChanges:el,onConfirmEnrollment:ei,planAssignments:T});if(n===r+1)return(0,o.jsx)(eE,{enrollmentData:{...h,dependents:C},planAssignments:T});return(0,o.jsx)(k,{onNext:$})}})()}),n>0&&n<_.length-1&&(0,o.jsxs)("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",marginTop:"32px",paddingTop:"24px",borderTop:"1px solid #e5e7eb",gap:"16px"},children:[(0,o.jsxs)("button",{onClick:Y,style:{display:"flex",alignItems:"center",gap:"8px",padding:"12px 20px",backgroundColor:"white",border:"1px solid #e5e7eb",borderRadius:"8px",cursor:"pointer",fontSize:"14px",fontWeight:"500",color:"#374151",transition:"all 0.2s ease",fontFamily:"sans-serif",boxShadow:"0 1px 3px rgba(0, 0, 0, 0.1)"},onMouseOver:e=>{e.currentTarget.style.backgroundColor="#f9fafb",e.currentTarget.style.borderColor="#d1d5db"},onMouseOut:e=>{e.currentTarget.style.backgroundColor="white",e.currentTarget.style.borderColor="#e5e7eb"},children:[(0,o.jsx)(x.Z,{size:16}),"Back"]}),n<3+O.length&&(0,o.jsxs)("button",{onClick:$,style:{display:"flex",alignItems:"center",gap:"8px",padding:"12px 24px",backgroundColor:"#000000",color:"white",borderRadius:"8px",border:"none",cursor:"pointer",fontSize:"14px",fontWeight:"600",transition:"all 0.2s ease",fontFamily:"sans-serif",boxShadow:"0 2px 4px rgba(0, 0, 0, 0.1)"},onMouseOver:e=>{e.currentTarget.style.backgroundColor="#374151",e.currentTarget.style.transform="translateY(-1px)"},onMouseOut:e=>{e.currentTarget.style.backgroundColor="#000000",e.currentTarget.style.transform="translateY(0)"},children:["Next",(0,o.jsx)(f.Z,{size:16})]})]})]})]}),(0,o.jsx)(eT,{})]})}},8790:function(e,n,t){"use strict";t.d(n,{C4:function(){return a},YN:function(){return s},we:function(){return c}});let o=()=>"http://localhost:8080",l=()=>localStorage.getItem("userid1")||localStorage.getItem("userId")||"",r=()=>({"Content-Type":"application/json","user-id":l()}),i=async e=>{try{let n=o(),t=l();if(!t)throw Error("User ID not found. Please log in again.");console.log("\uD83D\uDE80 Saving signature to database via API:",{endpoint:"".concat(n,"/admin/update/signature"),userId:t,signatureLength:e.length});let i=await fetch("".concat(n,"/admin/update/signature"),{method:"POST",headers:r(),body:JSON.stringify({signatureData:e})});if(i.ok){let e=await i.json();return console.log("✅ Signature saved to database successfully:",e),{success:!0,message:e.message||"Signature saved successfully",signatureId:e.signatureId}}{let e=await i.text();return console.error("❌ Failed to save signature to database:",{status:i.status,statusText:i.statusText,error:e}),{success:!1,error:"Failed to save signature: ".concat(e)}}}catch(e){return console.error("❌ Signature API error:",e),{success:!1,error:e instanceof Error?e.message:"Network error occurred"}}},a=async()=>{console.log("ℹ️ No GET endpoint available - checking localStorage only");try{return!!localStorage.getItem("enrollmentSignature")}catch(e){return console.error("❌ Error checking signature existence:",e),!1}},s=e=>{if(!e||""===e.trim())return{isValid:!1,error:"Signature data is required"};try{atob(e)}catch(e){return{isValid:!1,error:"Invalid signature data format"}}return e.length>5242880?{isValid:!1,error:"Signature data too large"}:{isValid:!0}},d=async e=>{let n=s(e);return n.isValid?await i(e):{success:!1,error:n.error}},c=async function(e){let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:3,t="";for(let o=1;o<=n;o++){console.log("\uD83D\uDD04 Signature save attempt ".concat(o,"/").concat(n));let l=await d(e);if(l.success)return console.log("✅ Signature saved successfully on attempt ".concat(o)),l;if(t=l.error||"Unknown error",console.warn("⚠️ Attempt ".concat(o," failed:"),t),o<n){let e=1e3*Math.pow(2,o);await new Promise(n=>setTimeout(n,e))}}return{success:!1,error:"Failed after ".concat(n," attempts. Last error: ").concat(t)}}},35303:function(e,n,t){"use strict";t.d(n,{Fj:function(){return s},Pe:function(){return d},ni:function(){return l},td:function(){return i},uM:function(){return a},yd:function(){return r}});let o=e=>{try{let n=atob(e),t=new Uint8Array(Array.from(n).map(e=>e.charCodeAt(0))),o=new TextDecoder().decode(t);return JSON.parse(o)}catch(e){throw console.error("Error decrypting signature:",e),Error("Failed to decrypt signature")}},l=()=>{try{let e=localStorage.getItem("enrollmentSignature");if(!e)return null;return o(e)}catch(e){return console.error("Error retrieving signature:",e),null}},r=()=>{try{let e=localStorage.getItem("enrollmentSignatureRef");if(!e)return null;return JSON.parse(e)}catch(e){return console.error("Error retrieving signature reference:",e),null}},i=()=>{try{localStorage.removeItem("enrollmentSignature"),localStorage.removeItem("enrollmentSignatureRef"),console.log("\uD83D\uDDD1️ Signature data cleared")}catch(e){console.error("Error clearing signature:",e)}},a=e=>{try{return btoa(e.signature).substring(0,32)===e.signatureHash}catch(e){return console.error("Error verifying signature:",e),!1}},s=e=>{try{return new Date(e).toLocaleString("en-US",{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit",second:"2-digit",timeZoneName:"short"})}catch(n){return e}},d=()=>{let e=l(),n=r();if(console.log("\uD83D\uDD0D Signature Debug Info:"),console.log("Reference:",n),e){console.log("Full Signature Data:",{timestamp:e.timestamp,employeeName:e.employeeName,signatureHash:e.signatureHash,userAgent:e.userAgent,signatureSize:e.signature.length,isValid:a(e)});let n=new Image;n.onload=()=>{console.log("\uD83D\uDCDD Signature Image:",n),console.log("Dimensions:",n.width,"x",n.height)},n.src=e.signature}else console.log("No signature found in storage")}},88884:function(e,n,t){"use strict";t.d(n,{KE:function(){return d},Pb:function(){return p},T1:function(){return u},Uc:function(){return c},createPlanAssignment:function(){return a},fH:function(){return i},updatePlanAssignment:function(){return s}});var o=t(61103);let l=(0,o.bR)(),r=()=>({"Content-Type":"application/json","user-id":(0,o.n5)()}),i=async function(e){let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},t=arguments.length>2?arguments[2]:void 0;try{let i=(0,o.n5)();console.log("\uD83D\uDD0D Plan Assignment API Debug:",{companyId:e,userId:i,filters:n,pagination:t,apiBaseUrl:l});let a=new URLSearchParams;n.status&&a.append("status",n.status),n.planId&&a.append("planId",n.planId),n.assignmentYear&&a.append("assignmentYear",n.assignmentYear.toString()),n.referenceDate&&a.append("referenceDate",n.referenceDate),void 0!==n.includePlanData&&a.append("includePlanData",n.includePlanData.toString()),n.enrollmentPeriodOnly&&a.append("enrollmentPeriodOnly","true"),n.effectiveOnly&&a.append("effectiveOnly","true"),n.futureOnly&&a.append("futureOnly","true"),n.includeInactive&&a.append("includeInactive","true"),n.includeExpired&&a.append("includeExpired","true"),n.brokerId&&a.append("brokerId",n.brokerId),t&&(a.append("page",t.page.toString()),a.append("limit",t.limit.toString()));let s="".concat(l,"/api/pre-enrollment/plan-assignments/company/").concat(e).concat(a.toString()?"?".concat(a.toString()):"");console.log("\uD83D\uDCE1 Fetching plan assignments from optimized API:",s);let d=await fetch(s,{method:"GET",headers:r()});if(console.log("Plan assignments API response status:",d.status),!d.ok){let n="HTTP error! status: ".concat(d.status);if(403===d.status){let n=(0,o.n5)();return console.error("\uD83D\uDEAB 403 Forbidden Error Details:",{url:s,userId:n,companyId:e,userIdSource:localStorage.getItem("userid1")?"userid1":localStorage.getItem("userId")?"userId":"none"}),console.log("\uD83D\uDD27 Broker has no existing plan assignments for this company - returning empty result to allow plan creation"),{success:!0,data:{assignments:[],count:0,message:"No existing plan assignments. You can create new plan assignments for this company.",canCreateAssignments:!0,accessDeniedToExisting:!0}}}try{let e=await d.json();console.error("API Error Response:",e),n+=" - ".concat(e.error||e.message||"Unknown error")}catch(e){console.log("No additional error details available")}throw Error(n)}let c=await d.json();return console.log("Plan assignments result:",c),console.log("First assignment details:",c.assignments[0]),{success:!0,data:c}}catch(e){return console.error("Error fetching plan assignments:",e),{success:!1,error:"Failed to fetch plan assignments"}}},a=async e=>{try{console.log("\uD83D\uDE80 Creating plan assignment with data:",e),console.log("\uD83D\uDD0D Required fields check:",{planId:e.planId,companyId:e.companyId,rateStructure:e.rateStructure,coverageTiers:e.coverageTiers,planEffectiveDate:e.planEffectiveDate,planEndDate:e.planEndDate,enrollmentStartDate:e.enrollmentStartDate,enrollmentEndDate:e.enrollmentEndDate}),console.log("\uD83D\uDCE1 Request URL:","".concat(l,"/api/pre-enrollment/plan-assignments")),console.log("\uD83D\uDD11 Headers:",r());let n=await fetch("".concat(l,"/api/pre-enrollment/plan-assignments"),{method:"POST",headers:r(),body:JSON.stringify(e)});if(!n.ok){let t=await n.json().catch(()=>({})),o=t.error||t.message||"HTTP error! status: ".concat(n.status),l=t.details||t.required||[];return console.error("❌ Plan assignment creation failed:",{status:n.status,error:o,details:l,fullErrorData:t,sentData:e}),{success:!1,error:"".concat(o).concat(l.length>0?" - ".concat(l.join(", ")):"")}}let t=await n.json();return{success:!0,data:t.assignment}}catch(e){return console.error("Error creating plan assignment:",e),{success:!1,error:e instanceof Error?e.message:"Failed to create plan assignment"}}},s=async(e,n)=>{try{console.log("Updating plan assignment:",e,"with data:",n);let t=await fetch("".concat(l,"/api/pre-enrollment/plan-assignments/").concat(e),{method:"PUT",headers:r(),body:JSON.stringify(n)});if(console.log("Update response status:",t.status),!t.ok){let e=await t.json().catch(()=>({})),o=e.error||e.message||"HTTP error! status: ".concat(t.status),l=e.details||[];return console.error("Plan assignment update failed:",{status:t.status,error:o,details:l,updateData:n}),{success:!1,error:"".concat(o).concat(l.length>0?" - ".concat(l.join(", ")):"")}}let o=await t.json();return console.log("Update successful:",o),{success:!0,data:o.assignment}}catch(e){return console.error("Error updating plan assignment:",e),{success:!1,error:e instanceof Error?e.message:"Failed to update plan assignment"}}},d=async e=>{try{let n=await fetch("".concat(l,"/api/pre-enrollment/plan-assignments/").concat(e),{method:"DELETE",headers:r()});if(!n.ok)throw Error("HTTP error! status: ".concat(n.status));let t=await n.json();return{success:!0,data:{message:t.message||"Plan assignment deleted successfully"}}}catch(e){return console.error("Error deleting plan assignment:",e),{success:!1,error:"Failed to delete plan assignment"}}},c=async(e,n)=>{try{console.log("Cloning plan assignment:",e,"with overrides:",n);let t=await fetch("".concat(l,"/api/pre-enrollment/plan-assignments/").concat(e,"/clone"),{method:"POST",headers:r(),body:JSON.stringify(n||{})});if(console.log("Clone response status:",t.status),!t.ok){let e=await t.json().catch(()=>({})),o=e.error||e.message||"HTTP error! status: ".concat(t.status),l=e.details||[];return console.error("Plan assignment clone failed:",{status:t.status,error:o,details:l,cloneData:n}),{success:!1,error:"".concat(o).concat(l.length>0?" - ".concat(l.join(", ")):"")}}let o=await t.json();return console.log("Clone successful:",o),{success:!0,data:o.assignment}}catch(e){return console.error("Error cloning plan assignment:",e),{success:!1,error:e instanceof Error?e.message:"Failed to clone plan assignment"}}},p=async()=>{try{let e=await fetch("".concat(l,"/api/pre-enrollment/plans/assignable"),{method:"GET",headers:r()});if(!e.ok)throw Error("HTTP error! status: ".concat(e.status));let n=((await e.json()).plans||[]).map((e,n)=>{console.log("\uD83D\uDD0D Plan ".concat(n," raw structure:"),e),console.log("\uD83D\uDD0D Plan ".concat(n," _id:"),e._id);let t=null;if(e._id&&("string"==typeof e._id?t=e._id:e._id.$oid?t=e._id.$oid:e._id.toString&&(t=e._id.toString())),e._doc){var o,l;let r=(null===(o=e._doc._id)||void 0===o?void 0:o.$oid)||(null===(l=e._doc._id)||void 0===l?void 0:l.toString())||e._doc._id,i={...e._doc,_id:t||r};return console.log("\uD83D\uDD0D Plan ".concat(n," transformed:"),i),i}let r={...e,_id:t};return console.log("\uD83D\uDD0D Plan ".concat(n," transformed:"),r),r});return{success:!0,data:n}}catch(e){return console.error("Error fetching assignable plans:",e),{success:!1,error:"Failed to fetch assignable plans"}}},u=async()=>{try{let e=(0,o.n5)();console.log("\uD83D\uDD0D Fetching broker plan assignments count for userId:",e);let n="".concat(l,"/api/pre-enrollment/plan-assignments?includePlanData=false");console.log("\uD83D\uDCE1 Fetching broker assignments count from:",n);let t=await fetch(n,{method:"GET",headers:r()});if(console.log("Broker assignments count API response status:",t.status),t.ok){let e=await t.json();return console.log("✅ Broker assignments count response:",e),{success:!0,data:{count:e.count||0}}}{let e=await t.text();return console.error("❌ Failed to fetch broker assignments count:",t.status,e),{success:!1,error:"Failed to fetch assignments count: ".concat(t.status)}}}catch(e){return console.error("❌ Error fetching broker assignments count:",e),{success:!1,error:"Network error while fetching assignments count"}}}},27345:function(e,n,t){"use strict";t.d(n,{b:function(){return r}});var o=t(76792);let l=(0,t(61103).GU)();async function r(e,n,t,r){let i={user_id:t,user_message:n,team_id:r};try{console.log("Sending chat message:",i);let n=await fetch("".concat(l,"/chat"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(i)});if(!n.body)throw Error("Readable stream not supported");let t=n.body.getReader(),r=new TextDecoder("utf-8"),a={sender:"bot",message:"",timestamp:new Date().toISOString()};for(e((0,o.wt)(!0));;){let{done:n,value:l}=await t.read();if(n)break;let i=r.decode(l,{stream:!0});e((0,o.wt)(!1)),console.log("Chunk:",i),a.message+=i,e((0,o.Hz)({sender:"bot",message:i,timestamp:new Date().toISOString()}))}}catch(t){console.error("Error sending chat message:",t);let n={sender:"bot",message:"Sorry, I encountered an error while processing your request.",timestamp:new Date().toISOString()};e((0,o.Hz)(n)),e((0,o.wt)(!1))}}},76792:function(e,n,t){"use strict";t.d(n,{Hz:function(){return l},wt:function(){return i}});let o=(0,t(39129).oM)({name:"qHarmonyBot",initialState:{chatHistory:[],isLoading:!1},reducers:{addMessage:(e,n)=>{let{sender:t,message:o,timestamp:l}=n.payload;if(console.log("Adding message:",n.payload),"bot"===t&&e.chatHistory.length>0){let n=e.chatHistory[e.chatHistory.length-1];if("bot"===n.sender&&!n.timestamp.includes("Done")){n.message+=o,n.timestamp=l;return}}e.chatHistory.push(n.payload)},clearChatHistory:e=>{e.chatHistory=[]},setIsLoading:(e,n)=>{e.isLoading=n.payload}}}),{addMessage:l,clearChatHistory:r,setIsLoading:i}=o.actions;n.ZP=o.reducer},61103:function(e,n,t){"use strict";t.d(n,{GU:function(){return r},bR:function(){return o},n5:function(){return l}});let o=()=>"http://localhost:8080",l=()=>{let e="userid1",n="userId",t=localStorage.getItem(e)||localStorage.getItem(n);return(console.log("\uD83D\uDD0D getUserId debug:",{primaryKey:e,altKey:n,primaryValue:localStorage.getItem(e),altValue:localStorage.getItem(n),finalUserId:t}),t)?t:(console.error("❌ User ID not found in localStorage"),"default-user")},r=()=>"https://bot.benosphere.com"},36521:function(){}},function(e){e.O(0,[2551,139,8422,3463,3301,8575,8685,187,1423,9932,3919,9129,2786,9826,9414,7404,208,3209,7571,2170,4677,5611,3344,9859,8005,2971,2117,1744],function(){return e(e.s=22839)}),_N_E=e.O()}]);