(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2887],{14363:function(e,t,r){Promise.resolve().then(r.bind(r,70503))},8790:function(e,t,r){"use strict";r.d(t,{C4:function(){return s},YN:function(){return c},we:function(){return d}});let o=()=>"http://localhost:8080",n=()=>localStorage.getItem("userid1")||localStorage.getItem("userId")||"",a=()=>({"Content-Type":"application/json","user-id":n()}),i=async e=>{try{let t=o(),r=n();if(!r)throw Error("User ID not found. Please log in again.");console.log("\uD83D\uDE80 Saving signature to database via API:",{endpoint:"".concat(t,"/admin/update/signature"),userId:r,signatureLength:e.length});let i=await fetch("".concat(t,"/admin/update/signature"),{method:"POST",headers:a(),body:JSON.stringify({signatureData:e})});if(i.ok){let e=await i.json();return console.log("✅ Signature saved to database successfully:",e),{success:!0,message:e.message||"Signature saved successfully",signatureId:e.signatureId}}{let e=await i.text();return console.error("❌ Failed to save signature to database:",{status:i.status,statusText:i.statusText,error:e}),{success:!1,error:"Failed to save signature: ".concat(e)}}}catch(e){return console.error("❌ Signature API error:",e),{success:!1,error:e instanceof Error?e.message:"Network error occurred"}}},s=async()=>{console.log("ℹ️ No GET endpoint available - checking localStorage only");try{return!!localStorage.getItem("enrollmentSignature")}catch(e){return console.error("❌ Error checking signature existence:",e),!1}},c=e=>{if(!e||""===e.trim())return{isValid:!1,error:"Signature data is required"};try{atob(e)}catch(e){return{isValid:!1,error:"Invalid signature data format"}}return e.length>5242880?{isValid:!1,error:"Signature data too large"}:{isValid:!0}},l=async e=>{let t=c(e);return t.isValid?await i(e):{success:!1,error:t.error}},d=async function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:3,r="";for(let o=1;o<=t;o++){console.log("\uD83D\uDD04 Signature save attempt ".concat(o,"/").concat(t));let n=await l(e);if(n.success)return console.log("✅ Signature saved successfully on attempt ".concat(o)),n;if(r=n.error||"Unknown error",console.warn("⚠️ Attempt ".concat(o," failed:"),r),o<t){let e=1e3*Math.pow(2,o);await new Promise(t=>setTimeout(t,e))}}return{success:!1,error:"Failed after ".concat(t," attempts. Last error: ").concat(r)}}},70503:function(e,t,r){"use strict";r.r(t);var o=r(57437),n=r(2265),a=r(8790);t.default=()=>{let[e,t]=(0,n.useState)([]),[r,i]=(0,n.useState)(!1),s=e=>{t(t=>[...t,"".concat(new Date().toLocaleTimeString(),": ").concat(e)])},c=()=>{t([])},l=async()=>{i(!0),s("\uD83E\uDDEA Testing signature save to database...");try{let e=btoa(JSON.stringify({signature:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==",timestamp:new Date().toISOString(),employeeName:"Test Employee",userAgent:navigator.userAgent,signatureHash:"test-hash-123"})),t=await (0,a.we)(e);t.success?(s("✅ Signature saved successfully to database"),s("\uD83D\uDCCB Response: ".concat(t.message)),t.signatureId&&s("\uD83C\uDD94 Signature ID: ".concat(t.signatureId))):s("❌ Failed to save signature: ".concat(t.error))}catch(e){s("❌ Error during save test: ".concat(e))}i(!1)},d=()=>{s("\uD83E\uDDEA Testing local signature storage...");try{let e=localStorage.getItem("enrollmentSignature");if(e){s("✅ Signature found in localStorage"),s("\uD83D\uDCCB Signature data length: ".concat(e.length," characters"));try{let t=JSON.parse(atob(e));s("\uD83D\uDC64 Employee: ".concat(t.employeeName||"Unknown")),s("\uD83D\uDCC5 Timestamp: ".concat(t.timestamp||"Unknown"))}catch(e){s("⚠️ Could not decode signature metadata")}}else s("ℹ️ No signature found in localStorage")}catch(e){s("❌ Error during localStorage test: ".concat(e))}},u=async()=>{i(!0),s("\uD83E\uDDEA Testing signature existence check...");try{await (0,a.C4)()?s("✅ Signature exists in database"):s("ℹ️ No signature found in database")}catch(e){s("❌ Error during existence check: ".concat(e))}i(!1)},g=()=>{s("\uD83E\uDDEA Testing signature validation...");let e=btoa('{"test": "data"}'),t=(0,a.YN)(e);s("✅ Valid signature test: ".concat(t.isValid?"PASSED":"FAILED"));let r=(0,a.YN)("");s("✅ Empty signature test: ".concat(r.isValid?"FAILED":"PASSED"," - ").concat(r.error));let o=(0,a.YN)("invalid-base64!@#");s("✅ Invalid base64 test: ".concat(o.isValid?"FAILED":"PASSED"," - ").concat(o.error))},p=async()=>{c(),s("\uD83D\uDE80 Starting comprehensive API tests..."),g(),await u(),await l(),d(),s("\uD83C\uDFC1 All tests completed!")};return(0,o.jsx)("div",{style:{minHeight:"100vh",backgroundColor:"#f9fafb",padding:"40px 20px"},children:(0,o.jsxs)("div",{style:{maxWidth:"1000px",margin:"0 auto",backgroundColor:"white",borderRadius:"16px",padding:"40px",boxShadow:"0 10px 25px rgba(0, 0, 0, 0.1)"},children:[(0,o.jsxs)("div",{style:{textAlign:"center",marginBottom:"40px"},children:[(0,o.jsx)("h1",{style:{fontSize:"32px",fontWeight:"600",color:"#111827",margin:"0 0 16px 0"},children:"\uD83E\uDDEA Signature API Test Suite"}),(0,o.jsxs)("p",{style:{color:"#6b7280",fontSize:"16px",margin:0,lineHeight:"24px"},children:["Test the signature API integration with your backend endpoint:",(0,o.jsx)("br",{}),(0,o.jsx)("code",{style:{backgroundColor:"#f3f4f6",padding:"2px 6px",borderRadius:"4px"},children:"POST /admin/update/signature"})]})]}),(0,o.jsxs)("div",{style:{display:"grid",gridTemplateColumns:"repeat(auto-fit, minmax(200px, 1fr))",gap:"16px",marginBottom:"32px"},children:[(0,o.jsx)("button",{onClick:l,disabled:r,style:{padding:"16px 24px",backgroundColor:"#2563eb",border:"none",borderRadius:"8px",color:"white",cursor:r?"not-allowed":"pointer",fontWeight:"500",fontSize:"16px",opacity:r?.6:1},children:"\uD83D\uDCBE Test Save Signature"}),(0,o.jsx)("button",{onClick:d,style:{padding:"16px 24px",backgroundColor:"#059669",border:"none",borderRadius:"8px",color:"white",cursor:"pointer",fontWeight:"500",fontSize:"16px"},children:"\uD83D\uDCBE Test localStorage"}),(0,o.jsx)("button",{onClick:u,disabled:r,style:{padding:"16px 24px",backgroundColor:"#7c3aed",border:"none",borderRadius:"8px",color:"white",cursor:r?"not-allowed":"pointer",fontWeight:"500",fontSize:"16px",opacity:r?.6:1},children:"\uD83D\uDD0D Test Exists Check"}),(0,o.jsx)("button",{onClick:g,disabled:r,style:{padding:"16px 24px",backgroundColor:"#dc2626",border:"none",borderRadius:"8px",color:"white",cursor:r?"not-allowed":"pointer",fontWeight:"500",fontSize:"16px",opacity:r?.6:1},children:"✅ Test Validation"}),(0,o.jsx)("button",{onClick:p,disabled:r,style:{padding:"16px 24px",backgroundColor:"#059669",border:"none",borderRadius:"8px",color:"white",cursor:r?"not-allowed":"pointer",fontWeight:"500",fontSize:"16px",opacity:r?.6:1,gridColumn:"span 2"},children:"\uD83D\uDE80 Run All Tests"}),(0,o.jsx)("button",{onClick:c,style:{padding:"16px 24px",backgroundColor:"#6b7280",border:"none",borderRadius:"8px",color:"white",cursor:"pointer",fontWeight:"500",fontSize:"16px",gridColumn:"span 2"},children:"\uD83D\uDDD1️ Clear Results"})]}),(0,o.jsxs)("div",{style:{backgroundColor:"#f8fafc",border:"1px solid #e2e8f0",borderRadius:"8px",padding:"24px",minHeight:"300px"},children:[(0,o.jsx)("h3",{style:{fontSize:"18px",fontWeight:"600",color:"#111827",margin:"0 0 16px 0"},children:"Test Results:"}),0===e.length?(0,o.jsx)("p",{style:{color:"#6b7280",fontStyle:"italic"},children:"No tests run yet. Click a test button to start."}):(0,o.jsx)("div",{style:{backgroundColor:"#1f2937",color:"#f9fafb",padding:"16px",borderRadius:"6px",fontFamily:"monospace",fontSize:"14px",lineHeight:"20px",maxHeight:"400px",overflowY:"auto"},children:e.map((e,t)=>(0,o.jsx)("div",{style:{marginBottom:"4px"},children:e},t))})]}),(0,o.jsxs)("div",{style:{marginTop:"32px",padding:"16px",backgroundColor:"#fef3c7",border:"1px solid #f59e0b",borderRadius:"8px",fontSize:"14px",color:"#92400e"},children:[(0,o.jsx)("strong",{children:"\uD83D\uDCCB API Endpoint Details:"}),(0,o.jsx)("br",{}),(0,o.jsx)("code",{children:"POST /admin/update/signature"}),(0,o.jsx)("br",{}),(0,o.jsx)("strong",{children:"Body:"})," ",(0,o.jsx)("code",{children:'{ "signatureData": "base64string..." }'}),(0,o.jsx)("br",{}),(0,o.jsx)("strong",{children:"Headers:"})," ",(0,o.jsx)("code",{children:"Content-Type: application/json, user-id: [userId]"})]}),(0,o.jsx)("div",{style:{textAlign:"center",marginTop:"32px"},children:(0,o.jsx)("a",{href:"/ai-enroller/employee-enrol",style:{display:"inline-block",padding:"12px 24px",backgroundColor:"white",border:"2px solid #e5e7eb",borderRadius:"8px",color:"#374151",textDecoration:"none",fontWeight:"500",fontSize:"14px"},children:"← Back to Enrollment"})})]})})}}},function(e){e.O(0,[2971,2117,1744],function(){return e(e.s=14363)}),_N_E=e.O()}]);