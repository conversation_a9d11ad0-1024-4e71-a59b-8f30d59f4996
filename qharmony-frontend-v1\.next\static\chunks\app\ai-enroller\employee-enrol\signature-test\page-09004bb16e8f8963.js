(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8792],{19232:function(e,t,r){Promise.resolve().then(r.bind(r,88234))},86586:function(e,t,r){"use strict";var n=r(57437),o=r(2265),i=r(18913),a=r(64677),s=r(8790);t.Z=e=>{let{isOpen:t,onClose:r,onSignatureComplete:l,employeeName:d="Employee"}=e,c=(0,o.useRef)(null),u=(0,o.useRef)(null),[g,p]=(0,o.useState)(!1),[x,h]=(0,o.useState)(!1);(0,o.useEffect)(()=>{if(t&&c.current&&!u.current){let e=c.current;e.width=600,e.height=200;let t=new a.Z(e,{backgroundColor:"#ffffff",penColor:"#000000",minWidth:1,maxWidth:3,throttle:16,minDistance:5,dotSize:0,velocityFilterWeight:.7});t.addEventListener("beginStroke",()=>{p(!0)}),t.addEventListener("endStroke",()=>{p(!t.isEmpty())}),u.current=t;let r=e.getContext("2d");r&&(r.strokeStyle="#d1d5db",r.lineWidth=1,r.setLineDash([5,5]),r.beginPath(),r.moveTo(50,e.height-30),r.lineTo(e.width-50,e.height-30),r.stroke(),r.setLineDash([]))}return()=>{u.current&&!t&&(u.current.off(),u.current=null)}},[t]);let f=async()=>{if(g&&x&&u.current)try{let e=u.current.toDataURL("image/png",1),t={signature:e,timestamp:new Date().toISOString(),employeeName:d,userAgent:navigator.userAgent,ipAddress:"client-side",signatureHash:btoa(e).substring(0,32),signaturePadData:u.current.toData(),quality:1,format:"PNG"},r=btoa(Array.from(new TextEncoder().encode(JSON.stringify(t))).map(e=>String.fromCharCode(e)).join(""));localStorage.setItem("enrollmentSignature",r);let n=await (0,s.we)(r);n.success||console.warn("⚠️ Failed to save signature to database:",n.error),console.log("\uD83D\uDCDD Signature captured, stored locally and sent to database:",{timestamp:t.timestamp,employeeName:t.employeeName,signatureHash:t.signatureHash,dataSize:e.length,vectorPoints:t.signaturePadData.length}),l(r)}catch(e){console.error("❌ Error saving signature:",e),alert("Signature saved locally but failed to save to database. Please contact support if this persists.")}};return t?(0,n.jsx)("div",{style:{position:"fixed",top:0,left:0,right:0,bottom:0,backgroundColor:"rgba(0, 0, 0, 0.5)",zIndex:1e4,display:"flex",alignItems:"center",justifyContent:"center",padding:"20px"},children:(0,n.jsxs)("div",{style:{backgroundColor:"#ffffff",borderRadius:"16px",padding:"32px",maxWidth:"700px",width:"100%",maxHeight:"90vh",overflowY:"auto",boxShadow:"0 25px 50px rgba(0, 0, 0, 0.25)",position:"relative"},children:[(0,n.jsx)("button",{onClick:r,style:{position:"absolute",top:"16px",right:"16px",background:"none",border:"none",fontSize:"24px",cursor:"pointer",color:"#6b7280",padding:"4px"},children:(0,n.jsx)(i.fMW,{})}),(0,n.jsxs)("div",{style:{marginBottom:"24px"},children:[(0,n.jsx)("h2",{style:{fontSize:"24px",fontWeight:"600",color:"#111827",margin:"0 0 8px 0"},children:"Create your signature"}),(0,n.jsx)("p",{style:{color:"#6b7280",fontSize:"14px",margin:0,lineHeight:"21px"},children:"Some carriers require a hand-drawn signature. Please draw your signature in the box below."})]}),(0,n.jsxs)("div",{style:{border:"2px solid #e5e7eb",borderRadius:"8px",marginBottom:"16px",position:"relative",backgroundColor:"#ffffff"},children:[(0,n.jsx)("canvas",{ref:c,style:{width:"100%",height:"200px",cursor:"crosshair",display:"block",touchAction:"none"}}),(0,n.jsx)("button",{onClick:()=>{if(u.current){u.current.clear(),p(!1);let e=c.current;if(e){let t=e.getContext("2d");t&&(t.strokeStyle="#d1d5db",t.lineWidth=1,t.setLineDash([5,5]),t.beginPath(),t.moveTo(50,e.height-30),t.lineTo(e.width-50,e.height-30),t.stroke(),t.setLineDash([]))}}},style:{position:"absolute",top:"8px",right:"8px",background:"none",border:"none",color:"#6b7280",fontSize:"14px",cursor:"pointer",padding:"4px 8px",borderRadius:"4px",backgroundColor:"rgba(255, 255, 255, 0.9)"},children:"Clear"}),(0,n.jsxs)("div",{style:{position:"absolute",bottom:"8px",left:"50px",fontSize:"12px",color:"#9ca3af"},children:[(0,n.jsx)(i._vs,{style:{display:"inline",marginRight:"4px"}}),"Sign above this line"]})]}),(0,n.jsx)("div",{style:{backgroundColor:"#f0fdf4",border:"1px solid #bbf7d0",borderRadius:"8px",padding:"16px",marginBottom:"24px"},children:(0,n.jsxs)("label",{style:{display:"flex",alignItems:"flex-start",gap:"12px",cursor:"pointer"},children:[(0,n.jsx)("input",{type:"checkbox",checked:x,onChange:e=>h(e.target.checked),style:{marginTop:"2px",width:"16px",height:"16px"}}),(0,n.jsxs)("div",{children:[(0,n.jsx)("div",{style:{fontSize:"14px",fontWeight:"600",color:"#065f46",marginBottom:"4px"},children:"\uD83D\uDD12 SHA-256 with RSA Encryption"}),(0,n.jsx)("div",{style:{fontSize:"14px",color:"#047857",lineHeight:"20px"},children:"I understand this is a legal representation of my signature and confirms my enrollment selections."})]})]})}),(0,n.jsxs)("div",{style:{display:"flex",gap:"12px",justifyContent:"flex-end"},children:[(0,n.jsx)("button",{onClick:r,style:{padding:"12px 24px",backgroundColor:"white",border:"1px solid #d1d5db",borderRadius:"8px",color:"#374151",cursor:"pointer",fontWeight:"500",fontSize:"14px"},children:"Cancel"}),(0,n.jsxs)("button",{onClick:f,disabled:!g||!x,style:{padding:"12px 24px",backgroundColor:g&&x?"#2563eb":"#9ca3af",border:"none",borderRadius:"8px",color:"white",cursor:g&&x?"pointer":"not-allowed",fontWeight:"500",fontSize:"14px",display:"flex",alignItems:"center",gap:"8px"},children:["Next",(0,n.jsx)("span",{style:{fontSize:"14px"},children:"→"})]})]})]})}):null}},8790:function(e,t,r){"use strict";r.d(t,{C4:function(){return s},YN:function(){return l},we:function(){return c}});let n=()=>"http://localhost:8080",o=()=>localStorage.getItem("userid1")||localStorage.getItem("userId")||"",i=()=>({"Content-Type":"application/json","user-id":o()}),a=async e=>{try{let t=n(),r=o();if(!r)throw Error("User ID not found. Please log in again.");console.log("\uD83D\uDE80 Saving signature to database via API:",{endpoint:"".concat(t,"/admin/update/signature"),userId:r,signatureLength:e.length});let a=await fetch("".concat(t,"/admin/update/signature"),{method:"POST",headers:i(),body:JSON.stringify({signatureData:e})});if(a.ok){let e=await a.json();return console.log("✅ Signature saved to database successfully:",e),{success:!0,message:e.message||"Signature saved successfully",signatureId:e.signatureId}}{let e=await a.text();return console.error("❌ Failed to save signature to database:",{status:a.status,statusText:a.statusText,error:e}),{success:!1,error:"Failed to save signature: ".concat(e)}}}catch(e){return console.error("❌ Signature API error:",e),{success:!1,error:e instanceof Error?e.message:"Network error occurred"}}},s=async()=>{console.log("ℹ️ No GET endpoint available - checking localStorage only");try{return!!localStorage.getItem("enrollmentSignature")}catch(e){return console.error("❌ Error checking signature existence:",e),!1}},l=e=>{if(!e||""===e.trim())return{isValid:!1,error:"Signature data is required"};try{atob(e)}catch(e){return{isValid:!1,error:"Invalid signature data format"}}return e.length>5242880?{isValid:!1,error:"Signature data too large"}:{isValid:!0}},d=async e=>{let t=l(e);return t.isValid?await a(e):{success:!1,error:t.error}},c=async function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:3,r="";for(let n=1;n<=t;n++){console.log("\uD83D\uDD04 Signature save attempt ".concat(n,"/").concat(t));let o=await d(e);if(o.success)return console.log("✅ Signature saved successfully on attempt ".concat(n)),o;if(r=o.error||"Unknown error",console.warn("⚠️ Attempt ".concat(n," failed:"),r),n<t){let e=1e3*Math.pow(2,n);await new Promise(t=>setTimeout(t,e))}}return{success:!1,error:"Failed after ".concat(t," attempts. Last error: ").concat(r)}}},88234:function(e,t,r){"use strict";r.r(t),r.d(t,{default:function(){return d}});var n=r(57437),o=r(2265),i=r(86586),a=r(18913),s=r(35303),l=e=>{let{isOpen:t,onClose:r}=e,[i,l]=(0,o.useState)(null),[d,c]=(0,o.useState)(null),[u,g]=(0,o.useState)(!1);(0,o.useEffect)(()=>{t&&p()},[t]);let p=()=>{g(!0);try{let e=(0,s.ni)(),t=(0,s.yd)();l(e),c(t),console.log("\uD83D\uDCDD Loaded signature data:",{signature:!!e,reference:t})}catch(e){console.error("Error loading signature data:",e)}finally{g(!1)}};return t?(0,n.jsx)("div",{style:{position:"fixed",top:0,left:0,right:0,bottom:0,backgroundColor:"rgba(0, 0, 0, 0.5)",zIndex:1e4,display:"flex",alignItems:"center",justifyContent:"center",padding:"20px"},children:(0,n.jsxs)("div",{style:{backgroundColor:"#ffffff",borderRadius:"16px",padding:"32px",maxWidth:"800px",width:"100%",maxHeight:"90vh",overflowY:"auto",boxShadow:"0 25px 50px rgba(0, 0, 0, 0.25)",position:"relative"},children:[(0,n.jsx)("button",{onClick:r,style:{position:"absolute",top:"16px",right:"16px",background:"none",border:"none",fontSize:"24px",cursor:"pointer",color:"#6b7280",padding:"4px"},children:(0,n.jsx)(a.fMW,{})}),(0,n.jsxs)("div",{style:{marginBottom:"24px"},children:[(0,n.jsx)("h2",{style:{fontSize:"24px",fontWeight:"600",color:"#111827",margin:"0 0 8px 0"},children:"\uD83D\uDCDD Enrollment Signature"}),(0,n.jsx)("p",{style:{color:"#6b7280",fontSize:"14px",margin:0,lineHeight:"21px"},children:"View and manage your stored enrollment signature"})]}),u?(0,n.jsx)("div",{style:{textAlign:"center",padding:"40px",color:"#6b7280"},children:"Loading signature data..."}):i?(0,n.jsxs)("div",{children:[(0,n.jsxs)("div",{style:{backgroundColor:"#f9fafb",border:"1px solid #e5e7eb",borderRadius:"8px",padding:"16px",marginBottom:"24px"},children:[(0,n.jsx)("h3",{style:{fontSize:"16px",fontWeight:"600",color:"#111827",margin:"0 0 12px 0"},children:"Signature Details"}),(0,n.jsxs)("div",{style:{display:"grid",gridTemplateColumns:"1fr 1fr",gap:"12px",fontSize:"14px"},children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("strong",{children:"Employee:"})," ",i.employeeName]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("strong",{children:"Signed:"})," ",(0,s.Fj)(i.timestamp)]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("strong",{children:"Signature Hash:"})," ",i.signatureHash]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("strong",{children:"Valid:"}),(0,n.jsx)("span",{style:{color:(0,s.uM)(i)?"#10b981":"#dc2626",fontWeight:"600",marginLeft:"4px"},children:(0,s.uM)(i)?"✅ Valid":"❌ Invalid"})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("strong",{children:"Format:"})," ",i.format||"PNG"]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("strong",{children:"Quality:"})," ",i.quality||"Standard"]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("strong",{children:"Vector Data:"})," ",i.signaturePadData?"".concat(i.signaturePadData.length," strokes"):"Not available"]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("strong",{children:"Data Size:"})," ",(i.signature.length/1024).toFixed(1)," KB"]})]})]}),(0,n.jsxs)("div",{style:{border:"2px solid #e5e7eb",borderRadius:"8px",padding:"16px",marginBottom:"24px",backgroundColor:"#ffffff",textAlign:"center"},children:[(0,n.jsx)("h3",{style:{fontSize:"16px",fontWeight:"600",color:"#111827",margin:"0 0 16px 0"},children:"Digital Signature"}),(0,n.jsx)("img",{src:i.signature,alt:"Digital Signature",style:{maxWidth:"100%",height:"auto",border:"1px solid #d1d5db",borderRadius:"4px"}})]}),(0,n.jsxs)("div",{style:{display:"flex",gap:"12px",justifyContent:"center",flexWrap:"wrap"},children:[(0,n.jsxs)("button",{onClick:()=>{if(null==i?void 0:i.signature)try{let e=document.createElement("a");e.href=i.signature,e.download="enrollment-signature-".concat(i.employeeName,"-").concat(new Date(i.timestamp).toISOString().split("T")[0],".png"),document.body.appendChild(e),e.click(),document.body.removeChild(e),console.log("\uD83D\uDCE5 Signature downloaded")}catch(e){console.error("Error downloading signature:",e)}},style:{padding:"12px 20px",backgroundColor:"#2563eb",border:"none",borderRadius:"8px",color:"white",cursor:"pointer",fontWeight:"500",fontSize:"14px",display:"flex",alignItems:"center",gap:"8px"},children:[(0,n.jsx)(a.yFZ,{}),"Download"]}),(0,n.jsxs)("button",{onClick:()=>{(0,s.Pe)()},style:{padding:"12px 20px",backgroundColor:"#6b7280",border:"none",borderRadius:"8px",color:"white",cursor:"pointer",fontWeight:"500",fontSize:"14px",display:"flex",alignItems:"center",gap:"8px"},children:[(0,n.jsx)(a.Vvo,{}),"Debug Info"]}),(0,n.jsxs)("button",{onClick:()=>{window.confirm("Are you sure you want to clear the stored signature? This action cannot be undone.")&&((0,s.td)(),l(null),c(null),console.log("\uD83D\uDDD1️ Signature cleared"))},style:{padding:"12px 20px",backgroundColor:"#dc2626",border:"none",borderRadius:"8px",color:"white",cursor:"pointer",fontWeight:"500",fontSize:"14px",display:"flex",alignItems:"center",gap:"8px"},children:[(0,n.jsx)(a.Bhs,{}),"Clear"]})]})]}):(0,n.jsxs)("div",{style:{textAlign:"center",padding:"40px",color:"#6b7280"},children:[(0,n.jsx)("div",{style:{fontSize:"48px",marginBottom:"16px"},children:"\uD83D\uDCDD"}),(0,n.jsx)("h3",{style:{fontSize:"18px",fontWeight:"600",color:"#111827",marginBottom:"8px"},children:"No Signature Found"}),(0,n.jsx)("p",{style:{margin:0,fontSize:"14px"},children:"Complete your enrollment to create a digital signature."})]}),(0,n.jsx)("div",{style:{marginTop:"24px",textAlign:"center"},children:(0,n.jsx)("button",{onClick:r,style:{padding:"12px 24px",backgroundColor:"white",border:"1px solid #d1d5db",borderRadius:"8px",color:"#374151",cursor:"pointer",fontWeight:"500",fontSize:"14px"},children:"Close"})})]})}):null},d=()=>{let[e,t]=(0,o.useState)(!1),[r,a]=(0,o.useState)(!1),[d,c]=(0,o.useState)("");return(0,n.jsxs)("div",{style:{minHeight:"100vh",backgroundColor:"#f9fafb",padding:"40px 20px"},children:[(0,n.jsxs)("div",{style:{maxWidth:"800px",margin:"0 auto",backgroundColor:"white",borderRadius:"16px",padding:"40px",boxShadow:"0 10px 25px rgba(0, 0, 0, 0.1)"},children:[(0,n.jsxs)("div",{style:{textAlign:"center",marginBottom:"40px"},children:[(0,n.jsx)("h1",{style:{fontSize:"32px",fontWeight:"600",color:"#111827",margin:"0 0 16px 0"},children:"\uD83D\uDCDD Signature Test Page"}),(0,n.jsx)("p",{style:{color:"#6b7280",fontSize:"16px",margin:0,lineHeight:"24px"},children:"Test the digital signature functionality for enrollment process"})]}),d&&(0,n.jsxs)("div",{style:{backgroundColor:"#f0fdf4",border:"1px solid #bbf7d0",borderRadius:"8px",padding:"16px",marginBottom:"32px",color:"#047857"},children:[(0,n.jsx)("strong",{children:"Status:"})," ",d]}),(0,n.jsxs)("div",{style:{display:"grid",gridTemplateColumns:"repeat(auto-fit, minmax(200px, 1fr))",gap:"16px",marginBottom:"32px"},children:[(0,n.jsx)("button",{onClick:()=>t(!0),style:{padding:"16px 24px",backgroundColor:"#2563eb",border:"none",borderRadius:"8px",color:"white",cursor:"pointer",fontWeight:"500",fontSize:"16px",textAlign:"center"},children:"\uD83D\uDD8A️ Create Signature"}),(0,n.jsx)("button",{onClick:()=>a(!0),style:{padding:"16px 24px",backgroundColor:"#059669",border:"none",borderRadius:"8px",color:"white",cursor:"pointer",fontWeight:"500",fontSize:"16px",textAlign:"center"},children:"\uD83D\uDC41️ View Signature"}),(0,n.jsx)("button",{onClick:()=>{let e=(0,s.yd)();e?c("Signature found: ".concat(e.employeeName," signed on ").concat(new Date(e.timestamp).toLocaleString())):c("No signature found in storage")},style:{padding:"16px 24px",backgroundColor:"#7c3aed",border:"none",borderRadius:"8px",color:"white",cursor:"pointer",fontWeight:"500",fontSize:"16px",textAlign:"center"},children:"\uD83D\uDD0D Check Status"}),(0,n.jsx)("button",{onClick:()=>{(0,s.Pe)(),c("Debug info logged to console")},style:{padding:"16px 24px",backgroundColor:"#6b7280",border:"none",borderRadius:"8px",color:"white",cursor:"pointer",fontWeight:"500",fontSize:"16px",textAlign:"center"},children:"\uD83D\uDC1B Debug Info"})]}),(0,n.jsxs)("div",{style:{backgroundColor:"#f8fafc",border:"1px solid #e2e8f0",borderRadius:"8px",padding:"24px"},children:[(0,n.jsx)("h3",{style:{fontSize:"18px",fontWeight:"600",color:"#111827",margin:"0 0 16px 0"},children:"How it works:"}),(0,n.jsxs)("ul",{style:{color:"#4b5563",fontSize:"14px",lineHeight:"20px",margin:0,paddingLeft:"20px"},children:[(0,n.jsxs)("li",{children:[(0,n.jsx)("strong",{children:"Create Signature:"})," Opens the signature modal where you can draw your signature"]}),(0,n.jsxs)("li",{children:[(0,n.jsx)("strong",{children:"View Signature:"})," Shows the stored signature with metadata and verification"]}),(0,n.jsxs)("li",{children:[(0,n.jsx)("strong",{children:"Check Status:"})," Displays current signature status and basic info"]}),(0,n.jsxs)("li",{children:[(0,n.jsx)("strong",{children:"Debug Info:"})," Logs detailed signature information to browser console"]})]}),(0,n.jsxs)("div",{style:{marginTop:"16px",padding:"12px",backgroundColor:"#fef3c7",border:"1px solid #f59e0b",borderRadius:"6px",fontSize:"12px",color:"#92400e"},children:[(0,n.jsx)("strong",{children:"\uD83D\uDD12 Security Note:"})," Signatures are encrypted and stored locally in browser storage. In production, these would be sent to a secure backend server."]})]}),(0,n.jsx)("div",{style:{textAlign:"center",marginTop:"32px"},children:(0,n.jsx)("a",{href:"/ai-enroller/employee-enrol",style:{display:"inline-block",padding:"12px 24px",backgroundColor:"white",border:"2px solid #e5e7eb",borderRadius:"8px",color:"#374151",textDecoration:"none",fontWeight:"500",fontSize:"14px",transition:"all 0.2s"},onMouseOver:e=>{e.currentTarget.style.borderColor="#2563eb",e.currentTarget.style.color="#2563eb"},onMouseOut:e=>{e.currentTarget.style.borderColor="#e5e7eb",e.currentTarget.style.color="#374151"},children:"← Back to Enrollment"})})]}),(0,n.jsx)(i.Z,{isOpen:e,onClose:()=>t(!1),onSignatureComplete:e=>{console.log("✅ Signature completed!",e),c("Signature completed and stored successfully!"),t(!1)},employeeName:"Test Employee"}),(0,n.jsx)(l,{isOpen:r,onClose:()=>a(!1)})]})}},35303:function(e,t,r){"use strict";r.d(t,{Fj:function(){return l},Pe:function(){return d},ni:function(){return o},td:function(){return a},uM:function(){return s},yd:function(){return i}});let n=e=>{try{let t=atob(e),r=new Uint8Array(Array.from(t).map(e=>e.charCodeAt(0))),n=new TextDecoder().decode(r);return JSON.parse(n)}catch(e){throw console.error("Error decrypting signature:",e),Error("Failed to decrypt signature")}},o=()=>{try{let e=localStorage.getItem("enrollmentSignature");if(!e)return null;return n(e)}catch(e){return console.error("Error retrieving signature:",e),null}},i=()=>{try{let e=localStorage.getItem("enrollmentSignatureRef");if(!e)return null;return JSON.parse(e)}catch(e){return console.error("Error retrieving signature reference:",e),null}},a=()=>{try{localStorage.removeItem("enrollmentSignature"),localStorage.removeItem("enrollmentSignatureRef"),console.log("\uD83D\uDDD1️ Signature data cleared")}catch(e){console.error("Error clearing signature:",e)}},s=e=>{try{return btoa(e.signature).substring(0,32)===e.signatureHash}catch(e){return console.error("Error verifying signature:",e),!1}},l=e=>{try{return new Date(e).toLocaleString("en-US",{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit",second:"2-digit",timeZoneName:"short"})}catch(t){return e}},d=()=>{let e=o(),t=i();if(console.log("\uD83D\uDD0D Signature Debug Info:"),console.log("Reference:",t),e){console.log("Full Signature Data:",{timestamp:e.timestamp,employeeName:e.employeeName,signatureHash:e.signatureHash,userAgent:e.userAgent,signatureSize:e.signature.length,isValid:s(e)});let t=new Image;t.onload=()=>{console.log("\uD83D\uDCDD Signature Image:",t),console.log("Dimensions:",t.width,"x",t.height)},t.src=e.signature}else console.log("No signature found in storage")}},46231:function(e,t,r){"use strict";r.d(t,{w_:function(){return c}});var n=r(2265),o={color:void 0,size:void 0,className:void 0,style:void 0,attr:void 0},i=n.createContext&&n.createContext(o),a=["attr","size","title"];function s(){return(s=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function l(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function d(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?l(Object(r),!0).forEach(function(t){var n,o;n=t,o=r[t],(n=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(n))in e?Object.defineProperty(e,n,{value:o,enumerable:!0,configurable:!0,writable:!0}):e[n]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):l(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function c(e){return t=>n.createElement(u,s({attr:d({},e.attr)},t),function e(t){return t&&t.map((t,r)=>n.createElement(t.tag,d({key:r},t.attr),e(t.child)))}(e.child))}function u(e){var t=t=>{var r,{attr:o,size:i,title:l}=e,c=function(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}(e,a),u=i||t.size||"1em";return t.className&&(r=t.className),e.className&&(r=(r?r+" ":"")+e.className),n.createElement("svg",s({stroke:"currentColor",fill:"currentColor",strokeWidth:"0"},t.attr,o,c,{className:r,style:d(d({color:e.color||t.color},t.style),e.style),height:u,width:u,xmlns:"http://www.w3.org/2000/svg"}),l&&n.createElement("title",null,l),e.children)};return void 0!==i?n.createElement(i.Consumer,null,e=>t(e)):t(o)}}},function(e){e.O(0,[8422,4677,2971,2117,1744],function(){return e(e.s=19232)}),_N_E=e.O()}]);