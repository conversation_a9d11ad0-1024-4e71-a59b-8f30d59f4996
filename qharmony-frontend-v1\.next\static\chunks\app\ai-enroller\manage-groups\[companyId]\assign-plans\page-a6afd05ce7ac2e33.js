(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3711],{95822:function(e,a,r){Promise.resolve().then(r.bind(r,78227))},99376:function(e,a,r){"use strict";var s=r(35475);r.o(s,"useParams")&&r.d(a,{useParams:function(){return s.useParams}}),r.o(s,"usePathname")&&r.d(a,{usePathname:function(){return s.usePathname}}),r.o(s,"useRouter")&&r.d(a,{useRouter:function(){return s.useRouter}}),r.o(s,"useSearchParams")&&r.d(a,{useSearchParams:function(){return s.useSearchParams}})},78227:function(e,a,r){"use strict";r.r(a);var s=r(57437),n=r(2265),i=r(99376),t=r(18913),l=r(68575);r(70082),a.default=()=>{let e=(0,i.useRouter)(),a=(0,i.useParams)();(0,l.I0)();let r=a.companyId,[c,o]=(0,n.useState)([]),[d,p]=(0,n.useState)([]),[u,h]=(0,n.useState)(!0),[m,v]=(0,n.useState)(null),g=(0,l.v9)(e=>e.user.managedCompanies),f=null==g?void 0:g.find(e=>e._id===r);(0,n.useEffect)(()=>{r&&j()},[r]);let j=async()=>{try{h(!0),v(null),await new Promise(e=>setTimeout(e,1e3)),p([{_id:"plan1",planName:"Blue Cross Blue Shield PPO",planCode:"BCBS-PPO-2024",coverageType:"Your Health",coverageSubTypes:["Medical"],planType:"PPO",metalTier:"Gold",carrierId:"carrier1",carrier:{carrierName:"Blue Cross Blue Shield"},description:"Comprehensive PPO plan with nationwide coverage",highlights:["Nationwide network","No referrals needed","Preventive care covered"]},{_id:"plan2",planName:"Aetna Better Health HMO",planCode:"AETNA-HMO-2024",coverageType:"Your Health",coverageSubTypes:["Medical"],planType:"HMO",metalTier:"Silver",carrierId:"carrier2",carrier:{carrierName:"Aetna"},description:"Cost-effective HMO plan with coordinated care",highlights:["Lower premiums","Coordinated care","Primary care focus"]},{_id:"plan3",planName:"Delta Dental PPO",planCode:"DELTA-PPO-2024",coverageType:"Dental",coverageSubTypes:["Dental"],planType:"PPO",carrierId:"carrier3",carrier:{carrierName:"Delta Dental"},description:"Comprehensive dental coverage with large network",highlights:["Large provider network","Preventive care covered","Orthodontics included"]},{_id:"plan4",planName:"Guardian Dental HMO",planCode:"GUARD-HMO-2024",coverageType:"Dental",coverageSubTypes:["Dental"],planType:"HMO",carrierId:"carrier4",carrier:{carrierName:"Guardian"},description:"Affordable dental HMO with quality care",highlights:["Lower cost option","Quality providers","Basic and major services"]},{_id:"plan5",planName:"VSP Vision Care",planCode:"VSP-2024",coverageType:"Vision",coverageSubTypes:["Vision"],planType:"Vision",carrierId:"carrier5",carrier:{carrierName:"VSP"},description:"Complete vision care with frame allowances",highlights:["Annual eye exams","Frame allowance","Contact lens coverage"]},{_id:"plan6",planName:"MetLife Life Insurance",planCode:"MET-LIFE-2024",coverageType:"Ancillary",coverageSubTypes:["Term Life"],planType:"Term Life",carrierId:"carrier6",carrier:{carrierName:"MetLife"},description:"Term life insurance for financial protection",highlights:["Competitive rates","Easy enrollment","Portable coverage"]}])}catch(e){console.error("Error fetching available plans:",e),v("Failed to load available plans")}finally{h(!1)}},y=()=>{e.push("/ai-enroller/manage-groups/select-company")},N=e=>{o(a=>a.includes(e)?a.filter(a=>a!==e):[...a,e])},b=e=>{switch(e.toLowerCase()){case"your health":case"medical":return(0,s.jsx)(t.wkn,{className:"plan-icon medical"});case"dental":return(0,s.jsx)(t.Q5u,{className:"plan-icon dental"});case"vision":return(0,s.jsx)(t.Vvo,{className:"plan-icon vision"});default:return(0,s.jsx)(t.Moc,{className:"plan-icon ancillary"})}},x={medical:[],dental:[],vision:[],ancillary:[]};return(d.forEach(e=>{var a;let r=(null===(a=e.coverageType)||void 0===a?void 0:a.toLowerCase())||"";r.includes("health")||r.includes("medical")?x.medical.push(e):r.includes("dental")?x.dental.push(e):r.includes("vision")?x.vision.push(e):x.ancillary.push(e)}),u)?(0,s.jsx)("div",{className:"assign-plans-page",children:(0,s.jsxs)("div",{className:"loading-container",children:[(0,s.jsx)("div",{className:"loading-spinner"}),(0,s.jsx)("p",{children:"Loading available plans..."})]})}):m?(0,s.jsx)("div",{className:"assign-plans-page",children:(0,s.jsxs)("div",{className:"error-container",children:[(0,s.jsx)("p",{children:m}),(0,s.jsxs)("button",{onClick:y,className:"back-button",children:[(0,s.jsx)(t.Tsu,{size:20}),"Back to Company Selection"]})]})}):(0,s.jsxs)("div",{className:"assign-plans-page",children:[(0,s.jsxs)("div",{className:"progress-steps",children:[(0,s.jsxs)("div",{className:"step active",children:[(0,s.jsx)("div",{className:"step-number",children:"1"}),(0,s.jsx)("div",{className:"step-label",children:"Select Plans"})]}),(0,s.jsx)("div",{className:"step-connector"}),(0,s.jsxs)("div",{className:"step",children:[(0,s.jsx)("div",{className:"step-number",children:"2"}),(0,s.jsx)("div",{className:"step-label",children:"Configure Tiers"})]}),(0,s.jsx)("div",{className:"step-connector"}),(0,s.jsxs)("div",{className:"step",children:[(0,s.jsx)("div",{className:"step-number",children:"3"}),(0,s.jsx)("div",{className:"step-label",children:"Set Dates"})]}),(0,s.jsx)("div",{className:"step-connector"}),(0,s.jsxs)("div",{className:"step",children:[(0,s.jsx)("div",{className:"step-number",children:"4"}),(0,s.jsx)("div",{className:"step-label",children:"Review & Assign"})]})]}),(0,s.jsxs)("div",{className:"page-header",children:[(0,s.jsxs)("button",{onClick:y,className:"back-button",children:[(0,s.jsx)(t.Tsu,{size:20}),"Back"]}),(0,s.jsxs)("div",{className:"header-content",children:[(0,s.jsx)("h1",{children:"Select Plans to Assign"}),(0,s.jsxs)("p",{children:["Choose the benefit plans you want to assign to ",(null==f?void 0:f.companyName)||"this company"]})]})]}),c.length>0&&(0,s.jsx)("div",{className:"selected-summary",children:(0,s.jsxs)("div",{className:"summary-content",children:[(0,s.jsx)(t.Sul,{size:20}),(0,s.jsxs)("span",{children:[c.length," plan",1!==c.length?"s":""," selected"]})]})}),Object.entries(x).map(e=>{let[a,r]=e;return r.length>0&&(0,s.jsxs)("div",{className:"plan-category",children:[(0,s.jsxs)("div",{className:"category-header",children:[(0,s.jsxs)("h2",{children:[a.charAt(0).toUpperCase()+a.slice(1)," Plans"]}),(0,s.jsxs)("span",{className:"plan-count",children:[r.length," available"]})]}),(0,s.jsx)("div",{className:"plans-grid",children:r.map(e=>{var a;return(0,s.jsxs)("div",{className:"plan-card ".concat(c.includes(e._id)?"selected":""),onClick:()=>N(e._id),children:[(0,s.jsxs)("div",{className:"plan-header",children:[(0,s.jsx)("div",{className:"plan-icon-wrapper",children:b(e.coverageType)}),(0,s.jsx)("div",{className:"selection-indicator",children:c.includes(e._id)&&(0,s.jsx)(t.Sul,{size:16})})]}),(0,s.jsxs)("div",{className:"plan-content",children:[(0,s.jsx)("h3",{children:e.planName}),(0,s.jsx)("div",{className:"plan-carrier",children:null===(a=e.carrier)||void 0===a?void 0:a.carrierName}),(0,s.jsxs)("div",{className:"plan-type",children:[e.planType," ",e.metalTier&&"• ".concat(e.metalTier)]}),e.description&&(0,s.jsx)("p",{className:"plan-description",children:e.description}),e.highlights&&e.highlights.length>0&&(0,s.jsx)("div",{className:"plan-highlights",children:e.highlights.slice(0,3).map((e,a)=>(0,s.jsxs)("div",{className:"highlight-item",children:[(0,s.jsx)(t.Sul,{size:12}),(0,s.jsx)("span",{children:e})]},a))})]})]},e._id)})})]},a)}),(0,s.jsx)("div",{className:"continue-section",children:(0,s.jsxs)("button",{className:"continue-btn ".concat(0===c.length?"disabled":""),onClick:()=>{if(0===c.length){alert("Please select at least one plan to continue.");return}localStorage.setItem("selectedPlansForAssignment",JSON.stringify(c)),e.push("/ai-enroller/manage-groups/".concat(r,"/assign-plans/configure"))},disabled:0===c.length,children:["Continue to Configure Tiers",(0,s.jsx)(t.mR2,{size:20})]})})]})}},70082:function(){},46231:function(e,a,r){"use strict";r.d(a,{w_:function(){return d}});var s=r(2265),n={color:void 0,size:void 0,className:void 0,style:void 0,attr:void 0},i=s.createContext&&s.createContext(n),t=["attr","size","title"];function l(){return(l=Object.assign?Object.assign.bind():function(e){for(var a=1;a<arguments.length;a++){var r=arguments[a];for(var s in r)Object.prototype.hasOwnProperty.call(r,s)&&(e[s]=r[s])}return e}).apply(this,arguments)}function c(e,a){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(e);a&&(s=s.filter(function(a){return Object.getOwnPropertyDescriptor(e,a).enumerable})),r.push.apply(r,s)}return r}function o(e){for(var a=1;a<arguments.length;a++){var r=null!=arguments[a]?arguments[a]:{};a%2?c(Object(r),!0).forEach(function(a){var s,n;s=a,n=r[a],(s=function(e){var a=function(e,a){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var s=r.call(e,a||"default");if("object"!=typeof s)return s;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===a?String:Number)(e)}(e,"string");return"symbol"==typeof a?a:a+""}(s))in e?Object.defineProperty(e,s,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[s]=n}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):c(Object(r)).forEach(function(a){Object.defineProperty(e,a,Object.getOwnPropertyDescriptor(r,a))})}return e}function d(e){return a=>s.createElement(p,l({attr:o({},e.attr)},a),function e(a){return a&&a.map((a,r)=>s.createElement(a.tag,o({key:r},a.attr),e(a.child)))}(e.child))}function p(e){var a=a=>{var r,{attr:n,size:i,title:c}=e,d=function(e,a){if(null==e)return{};var r,s,n=function(e,a){if(null==e)return{};var r={};for(var s in e)if(Object.prototype.hasOwnProperty.call(e,s)){if(a.indexOf(s)>=0)continue;r[s]=e[s]}return r}(e,a);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(s=0;s<i.length;s++)r=i[s],!(a.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(n[r]=e[r])}return n}(e,t),p=i||a.size||"1em";return a.className&&(r=a.className),e.className&&(r=(r?r+" ":"")+e.className),s.createElement("svg",l({stroke:"currentColor",fill:"currentColor",strokeWidth:"0"},a.attr,n,d,{className:r,style:o(o({color:e.color||a.color},a.style),e.style),height:p,width:p,xmlns:"http://www.w3.org/2000/svg"}),c&&s.createElement("title",null,c),e.children)};return void 0!==i?s.createElement(i.Consumer,null,e=>a(e)):a(n)}}},function(e){e.O(0,[8288,8422,8575,2971,2117,1744],function(){return e(e.s=95822)}),_N_E=e.O()}]);