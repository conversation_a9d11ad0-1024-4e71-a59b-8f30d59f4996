(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4905],{1430:function(e,a,n){Promise.resolve().then(n.bind(n,85861))},99376:function(e,a,n){"use strict";var r=n(35475);n.o(r,"useParams")&&n.d(a,{useParams:function(){return r.useParams}}),n.o(r,"usePathname")&&n.d(a,{usePathname:function(){return r.usePathname}}),n.o(r,"useRouter")&&n.d(a,{useRouter:function(){return r.useRouter}}),n.o(r,"useSearchParams")&&n.d(a,{useSearchParams:function(){return r.useSearchParams}})},85861:function(e,a,n){"use strict";n.r(a);var r=n(57437),s=n(2265),t=n(99376),l=n(18913),i=n(68575);n(5507),a.default=()=>{let e=(0,t.useRouter)(),a=(0,t.useParams)(),n=(0,t.useSearchParams)(),c=a.companyId,o=n.get("category")||"all",[d,u]=(0,s.useState)([]),[p,h]=(0,s.useState)(!0),[m,v]=(0,s.useState)(null),f=(0,i.v9)(e=>e.user.managedCompanies),j=null==f?void 0:f.find(e=>e._id===c),g=async()=>{try{h(!0),v(null);let e=await fetch("".concat("http://localhost:8080","/api/plans/broker/").concat("6838677aef6db0212bcfdacd"),{method:"GET",headers:{"Content-Type":"application/json","user-id":localStorage.getItem("userid1")||localStorage.getItem("userId")||"6838677aef6db0212bcfdacd"}});if(e.ok){let a=await e.json();console.log("Available plans result:",a);let n=a.plans||[];"all"!==o&&(n=n.filter(e=>{var a;let n=(null===(a=e.coverageType)||void 0===a?void 0:a.toLowerCase())||"";switch(o.toLowerCase()){case"medical":return n.includes("health")||n.includes("medical");case"dental":return n.includes("dental");case"vision":return n.includes("vision");case"ancillary":return!n.includes("health")&&!n.includes("medical")&&!n.includes("dental")&&!n.includes("vision");default:return!0}})),u(n)}else throw Error("Failed to fetch plans")}catch(e){console.error("Error fetching available plans:",e),v("Failed to load available plans")}finally{h(!1)}};(0,s.useEffect)(()=>{g()},[o]);let b=()=>{e.push("/ai-enroller/manage-groups/".concat(c,"/manage-plans"))},x=a=>{e.push("/ai-enroller/manage-groups/".concat(c,"/manage-plans/configure-plan?planId=").concat(a._id))},y=e=>{switch(e.toLowerCase()){case"your health":case"medical":return(0,r.jsx)(l.wkn,{className:"plan-icon medical"});case"dental":return(0,r.jsx)(l.Q5u,{className:"plan-icon dental"});case"vision":return(0,r.jsx)(l.Vvo,{className:"plan-icon vision"});default:return(0,r.jsx)(l.Moc,{className:"plan-icon ancillary"})}};return p?(0,r.jsx)("div",{className:"manage-plans-page",children:(0,r.jsxs)("div",{className:"loading-container",children:[(0,r.jsx)("div",{className:"loading-spinner"}),(0,r.jsx)("p",{children:"Loading available plans..."})]})}):m?(0,r.jsx)("div",{className:"manage-plans-page",children:(0,r.jsxs)("div",{className:"error-container",children:[(0,r.jsx)("p",{children:m}),(0,r.jsxs)("button",{onClick:b,className:"back-button",children:[(0,r.jsx)(l.Tsu,{size:20}),"Back to Plans"]})]})}):(0,r.jsxs)("div",{className:"manage-plans-page",children:[(0,r.jsxs)("div",{className:"page-header",children:[(0,r.jsxs)("button",{onClick:b,className:"back-button",children:[(0,r.jsx)(l.Tsu,{size:20}),"Back to Plans"]}),(0,r.jsxs)("div",{className:"header-content",children:[(0,r.jsxs)("h1",{children:["Add ",o.charAt(0).toUpperCase()+o.slice(1)," Plan"]}),(0,r.jsxs)("p",{children:["Select a plan to add to ",(null==j?void 0:j.companyName)||"this company"]})]})]}),(0,r.jsxs)("div",{className:"plan-category",children:[(0,r.jsxs)("div",{className:"category-header",children:[(0,r.jsx)("h2",{children:"Available Plans"}),(0,r.jsxs)("span",{className:"plan-count",children:[d.length," plan",1!==d.length?"s":""," available"]})]}),0===d.length?(0,r.jsxs)("div",{className:"no-plans",children:[(0,r.jsx)(l.Moc,{size:48}),(0,r.jsx)("h3",{children:"No plans available"}),(0,r.jsxs)("p",{children:["There are no ",o," plans available for assignment at this time."]})]}):(0,r.jsx)("div",{className:"plans-grid",children:d.map(e=>{var a;return(0,r.jsxs)("div",{className:"plan-card selectable",onClick:()=>x(e),children:[(0,r.jsxs)("div",{className:"plan-header",children:[(0,r.jsx)("div",{className:"plan-icon-wrapper",children:y(e.coverageType)}),(0,r.jsx)("div",{className:"add-indicator",children:(0,r.jsx)(l.r7I,{size:16})})]}),(0,r.jsxs)("div",{className:"plan-content",children:[(0,r.jsx)("h3",{children:e.planName}),(0,r.jsx)("div",{className:"plan-carrier",children:(null===(a=e.carrier)||void 0===a?void 0:a.carrierName)||"Unknown Carrier"}),(0,r.jsxs)("div",{className:"plan-type",children:[e.planType," ",e.metalTier&&"• ".concat(e.metalTier)]}),e.description&&(0,r.jsx)("p",{className:"plan-description",children:e.description}),e.planCode&&(0,r.jsxs)("div",{className:"plan-code",children:[(0,r.jsx)("span",{className:"code-label",children:"Plan Code:"}),(0,r.jsx)("span",{className:"code-value",children:e.planCode})]}),e.highlights&&e.highlights.length>0&&(0,r.jsx)("div",{className:"plan-highlights",children:e.highlights.slice(0,3).map((e,a)=>(0,r.jsx)("div",{className:"highlight-item",children:(0,r.jsxs)("span",{children:["• ",e]})},a))})]}),(0,r.jsx)("div",{className:"plan-actions",children:(0,r.jsxs)("button",{className:"select-plan-btn",children:[(0,r.jsx)(l.r7I,{size:16}),"Add Plan"]})})]},e._id)})})]})]})}},5507:function(){},46231:function(e,a,n){"use strict";n.d(a,{w_:function(){return d}});var r=n(2265),s={color:void 0,size:void 0,className:void 0,style:void 0,attr:void 0},t=r.createContext&&r.createContext(s),l=["attr","size","title"];function i(){return(i=Object.assign?Object.assign.bind():function(e){for(var a=1;a<arguments.length;a++){var n=arguments[a];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}function c(e,a){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);a&&(r=r.filter(function(a){return Object.getOwnPropertyDescriptor(e,a).enumerable})),n.push.apply(n,r)}return n}function o(e){for(var a=1;a<arguments.length;a++){var n=null!=arguments[a]?arguments[a]:{};a%2?c(Object(n),!0).forEach(function(a){var r,s;r=a,s=n[a],(r=function(e){var a=function(e,a){if("object"!=typeof e||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,a||"default");if("object"!=typeof r)return r;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===a?String:Number)(e)}(e,"string");return"symbol"==typeof a?a:a+""}(r))in e?Object.defineProperty(e,r,{value:s,enumerable:!0,configurable:!0,writable:!0}):e[r]=s}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):c(Object(n)).forEach(function(a){Object.defineProperty(e,a,Object.getOwnPropertyDescriptor(n,a))})}return e}function d(e){return a=>r.createElement(u,i({attr:o({},e.attr)},a),function e(a){return a&&a.map((a,n)=>r.createElement(a.tag,o({key:n},a.attr),e(a.child)))}(e.child))}function u(e){var a=a=>{var n,{attr:s,size:t,title:c}=e,d=function(e,a){if(null==e)return{};var n,r,s=function(e,a){if(null==e)return{};var n={};for(var r in e)if(Object.prototype.hasOwnProperty.call(e,r)){if(a.indexOf(r)>=0)continue;n[r]=e[r]}return n}(e,a);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(e);for(r=0;r<t.length;r++)n=t[r],!(a.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(s[n]=e[n])}return s}(e,l),u=t||a.size||"1em";return a.className&&(n=a.className),e.className&&(n=(n?n+" ":"")+e.className),r.createElement("svg",i({stroke:"currentColor",fill:"currentColor",strokeWidth:"0"},a.attr,s,d,{className:n,style:o(o({color:e.color||a.color},a.style),e.style),height:u,width:u,xmlns:"http://www.w3.org/2000/svg"}),c&&r.createElement("title",null,c),e.children)};return void 0!==t?r.createElement(t.Consumer,null,e=>a(e)):a(s)}}},function(e){e.O(0,[7397,8422,8575,2971,2117,1744],function(){return e(e.s=1430)}),_N_E=e.O()}]);