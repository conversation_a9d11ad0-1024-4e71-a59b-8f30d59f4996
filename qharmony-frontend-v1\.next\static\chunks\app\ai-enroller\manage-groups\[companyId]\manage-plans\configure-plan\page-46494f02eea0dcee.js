(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[715],{60589:function(e,t,a){Promise.resolve().then(a.bind(a,10060))},99376:function(e,t,a){"use strict";var s=a(35475);a.o(s,"useParams")&&a.d(t,{useParams:function(){return s.useParams}}),a.o(s,"usePathname")&&a.d(t,{usePathname:function(){return s.usePathname}}),a.o(s,"useRouter")&&a.d(t,{useRouter:function(){return s.useRouter}}),a.o(s,"useSearchParams")&&a.d(t,{useSearchParams:function(){return s.useSearchParams}})},10060:function(e,t,a){"use strict";a.r(t);var s=a(57437),l=a(2265),n=a(99376),r=a(18913),o=a(68575);a(5507),t.default=()=>{var e;let t=(0,n.useRouter)(),a=(0,n.useParams)(),i=(0,n.useSearchParams)(),c=a.companyId,d=i.get("planId"),[m,u]=(0,l.useState)(null),[p,h]=(0,l.useState)(!0),[v,y]=(0,l.useState)(null),[g,j]=(0,l.useState)(!1),[f,N]=(0,l.useState)("Percentage"),[b,x]=(0,l.useState)([{tierName:"Employee Only",totalCost:450,employeeCost:90,employerCost:360},{tierName:"Employee + Spouse",totalCost:890,employeeCost:178,employerCost:712},{tierName:"Employee + Children",totalCost:720,employeeCost:144,employerCost:576},{tierName:"Employee + Family",totalCost:1250,employeeCost:250,employerCost:1e3}]),C=(0,o.v9)(e=>e.user.managedCompanies);null==C||C.find(e=>e._id===c);let P=async()=>{try{h(!0),y(null);let e=await fetch("".concat("http://localhost:8080","/api/plans/").concat(d),{method:"GET",headers:{"Content-Type":"application/json","user-id":localStorage.getItem("userid1")||localStorage.getItem("userId")||"6838677aef6db0212bcfdacd"}});if(e.ok){let t=await e.json();console.log("Plan details result:",t),u(t.plan||t)}else throw Error("Failed to fetch plan details")}catch(e){console.error("Error fetching plan details:",e),y("Failed to load plan details")}finally{h(!1)}};(0,l.useEffect)(()=>{d&&P()},[d,P]);let O=()=>{t.push("/ai-enroller/manage-groups/".concat(c,"/manage-plans"))},S=(e,t,a)=>{let s=[...b];if(s[e]={...s[e],[t]:"tierName"===t?a:parseFloat(a.toString())||0},"totalCost"===t||"employeeCost"===t){let t=s[e].totalCost,a=s[e].employeeCost;s[e].employerCost=t-a}x(s)},w=(e,t)=>{let a=[...b],s=a[e].totalCost,l=s*t/100;a[e]={...a[e],employerCost:l,employeeCost:s-l},x(a)},E=e=>{b.length>1&&x(b.filter((t,a)=>a!==e))},k=async()=>{try{j(!0);let e={planId:d,companyId:c,groupNumber:"POL-".concat(Math.random().toString(36).substr(2,9).toUpperCase()),waitingPeriod:{enabled:!1,days:0,rule:"Immediate"},enrollmentType:"Active",rateStructure:"Flat",coverageTiers:b,planEffectiveDate:new Date().toISOString(),planEndDate:new Date(new Date().getFullYear()+1,11,31).toISOString(),enrollmentStartDate:new Date().toISOString(),enrollmentEndDate:new Date(new Date().getFullYear(),11,31).toISOString(),employerContribution:{contributionType:f,contributionAmount:80},employeeContribution:{contributionType:f,contributionAmount:20},ageBandedRates:[],salaryBasedRates:[],planCustomizations:{},isActive:!1,status:"Draft"},a=await fetch("".concat("http://localhost:8080","/api/pre-enrollment/plan-assignments"),{method:"POST",headers:{"Content-Type":"application/json","user-id":localStorage.getItem("userid1")||localStorage.getItem("userId")||"6838677aef6db0212bcfdacd"},body:JSON.stringify(e)});if(a.ok){let e=await a.json();console.log("Plan assignment created:",e),t.push("/ai-enroller/manage-groups/".concat(c,"/manage-plans"))}else throw Error("Failed to create plan assignment")}catch(e){console.error("Error saving plan assignment:",e),alert("Failed to save plan configuration. Please try again.")}finally{j(!1)}};return p?(0,s.jsx)("div",{className:"manage-plans-page",children:(0,s.jsxs)("div",{className:"loading-container",children:[(0,s.jsx)("div",{className:"loading-spinner"}),(0,s.jsx)("p",{children:"Loading plan details..."})]})}):v||!m?(0,s.jsx)("div",{className:"manage-plans-page",children:(0,s.jsxs)("div",{className:"error-container",children:[(0,s.jsx)("p",{children:v||"Plan not found"}),(0,s.jsxs)("button",{onClick:()=>{t.push("/ai-enroller/manage-groups/".concat(c,"/manage-plans/add-plan"))},className:"back-button",children:[(0,s.jsx)(r.Tsu,{size:20}),"Back to Plan Selection"]})]})}):(0,s.jsxs)("div",{className:"manage-plans-page",children:[(0,s.jsx)("div",{className:"modal-header",children:(0,s.jsxs)("div",{className:"modal-title",children:[(0,s.jsx)("h1",{children:"Edit Plan"}),(0,s.jsx)("button",{onClick:O,className:"close-button",children:(0,s.jsx)(r.fMW,{size:24})})]})}),(0,s.jsxs)("div",{className:"plan-info-section",children:[(0,s.jsxs)("div",{className:"plan-title",children:[(0,s.jsx)("h2",{children:m.planName}),(0,s.jsx)("span",{className:"status-badge active",children:"active"})]}),(0,s.jsx)("div",{className:"plan-carrier",children:null===(e=m.carrier)||void 0===e?void 0:e.carrierName}),(0,s.jsxs)("div",{className:"plan-details-grid",children:[(0,s.jsxs)("div",{className:"detail-item",children:[(0,s.jsx)("span",{className:"label",children:"Type:"}),(0,s.jsx)("span",{className:"value",children:m.planType})]}),(0,s.jsxs)("div",{className:"detail-item",children:[(0,s.jsx)("span",{className:"label",children:"Plan Code:"}),(0,s.jsx)("span",{className:"value",children:m.planCode||"N/A"})]}),(0,s.jsxs)("div",{className:"detail-item",children:[(0,s.jsx)("span",{className:"label",children:"Category:"}),(0,s.jsx)("span",{className:"value",children:m.coverageType})]}),(0,s.jsxs)("div",{className:"detail-item",children:[(0,s.jsx)("span",{className:"label",children:"Policy #:"}),(0,s.jsxs)("span",{className:"value",children:["POL-",Math.random().toString(36).substr(2,9).toUpperCase()]})]})]})]}),(0,s.jsxs)("div",{className:"coverage-section",children:[(0,s.jsxs)("div",{className:"section-header",children:[(0,s.jsx)("h3",{children:"Coverage Tiers & Contributions"}),(0,s.jsxs)("div",{className:"contribution-type-selector",children:[(0,s.jsxs)("select",{value:f,onChange:e=>N(e.target.value),children:[(0,s.jsx)("option",{value:"Percentage",children:"Percentage"}),(0,s.jsx)("option",{value:"Fixed",children:"Fixed Amount"})]}),(0,s.jsxs)("button",{onClick:()=>{x([...b,{tierName:"New Tier",totalCost:0,employeeCost:0,employerCost:0}])},className:"add-tier-btn",children:[(0,s.jsx)(r.r7I,{size:16}),"Add"]})]})]}),(0,s.jsxs)("div",{className:"tiers-table",children:[(0,s.jsxs)("div",{className:"table-header",children:[(0,s.jsx)("div",{className:"col-tier",children:"Coverage Tier"}),(0,s.jsx)("div",{className:"col-premium",children:"Premium"}),(0,s.jsx)("div",{className:"col-employer",children:"Employer (%)"}),(0,s.jsx)("div",{className:"col-employer-pays",children:"Employer Pays"}),(0,s.jsx)("div",{className:"col-employee-pays",children:"Employee Pays"}),(0,s.jsx)("div",{className:"col-actions"})]}),b.map((e,t)=>(0,s.jsxs)("div",{className:"table-row",children:[(0,s.jsx)("div",{className:"col-tier",children:(0,s.jsx)("input",{type:"text",value:e.tierName,onChange:e=>S(t,"tierName",e.target.value),className:"tier-name-input"})}),(0,s.jsx)("div",{className:"col-premium",children:(0,s.jsx)("input",{type:"number",value:e.totalCost,onChange:e=>S(t,"totalCost",e.target.value),className:"cost-input",step:"0.01"})}),(0,s.jsx)("div",{className:"col-employer",children:(0,s.jsx)("input",{type:"number",value:e.totalCost>0?Math.round(e.employerCost/e.totalCost*100):0,onChange:e=>w(t,parseFloat(e.target.value)||0),className:"percentage-input",min:"0",max:"100"})}),(0,s.jsx)("div",{className:"col-employer-pays",children:(0,s.jsxs)("span",{className:"cost-display",children:["$",e.employerCost.toFixed(2)]})}),(0,s.jsx)("div",{className:"col-employee-pays",children:(0,s.jsxs)("span",{className:"cost-display",children:["$",e.employeeCost.toFixed(2)]})}),(0,s.jsx)("div",{className:"col-actions",children:b.length>1&&(0,s.jsx)("button",{onClick:()=>E(t),className:"remove-tier-btn",children:(0,s.jsx)(r.Bhs,{size:16})})})]},t))]})]}),(0,s.jsxs)("div",{className:"modal-actions",children:[(0,s.jsx)("button",{onClick:O,className:"cancel-btn",children:"Cancel"}),(0,s.jsx)("button",{onClick:k,className:"save-btn",disabled:g,children:g?"Saving...":"Save Changes"})]})]})}},5507:function(){},46231:function(e,t,a){"use strict";a.d(t,{w_:function(){return d}});var s=a(2265),l={color:void 0,size:void 0,className:void 0,style:void 0,attr:void 0},n=s.createContext&&s.createContext(l),r=["attr","size","title"];function o(){return(o=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var s in a)Object.prototype.hasOwnProperty.call(a,s)&&(e[s]=a[s])}return e}).apply(this,arguments)}function i(e,t){var a=Object.keys(e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(e);t&&(s=s.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),a.push.apply(a,s)}return a}function c(e){for(var t=1;t<arguments.length;t++){var a=null!=arguments[t]?arguments[t]:{};t%2?i(Object(a),!0).forEach(function(t){var s,l;s=t,l=a[t],(s=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var a=e[Symbol.toPrimitive];if(void 0!==a){var s=a.call(e,t||"default");if("object"!=typeof s)return s;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(s))in e?Object.defineProperty(e,s,{value:l,enumerable:!0,configurable:!0,writable:!0}):e[s]=l}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(a)):i(Object(a)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(a,t))})}return e}function d(e){return t=>s.createElement(m,o({attr:c({},e.attr)},t),function e(t){return t&&t.map((t,a)=>s.createElement(t.tag,c({key:a},t.attr),e(t.child)))}(e.child))}function m(e){var t=t=>{var a,{attr:l,size:n,title:i}=e,d=function(e,t){if(null==e)return{};var a,s,l=function(e,t){if(null==e)return{};var a={};for(var s in e)if(Object.prototype.hasOwnProperty.call(e,s)){if(t.indexOf(s)>=0)continue;a[s]=e[s]}return a}(e,t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);for(s=0;s<n.length;s++)a=n[s],!(t.indexOf(a)>=0)&&Object.prototype.propertyIsEnumerable.call(e,a)&&(l[a]=e[a])}return l}(e,r),m=n||t.size||"1em";return t.className&&(a=t.className),e.className&&(a=(a?a+" ":"")+e.className),s.createElement("svg",o({stroke:"currentColor",fill:"currentColor",strokeWidth:"0"},t.attr,l,d,{className:a,style:c(c({color:e.color||t.color},t.style),e.style),height:m,width:m,xmlns:"http://www.w3.org/2000/svg"}),i&&s.createElement("title",null,i),e.children)};return void 0!==n?s.createElement(n.Consumer,null,e=>t(e)):t(l)}}},function(e){e.O(0,[7397,8422,8575,2971,2117,1744],function(){return e(e.s=60589)}),_N_E=e.O()}]);