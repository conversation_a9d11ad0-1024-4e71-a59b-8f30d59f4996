(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7073],{2458:function(e,a,s){Promise.resolve().then(s.bind(s,9690))},99376:function(e,a,s){"use strict";var n=s(35475);s.o(n,"useParams")&&s.d(a,{useParams:function(){return n.useParams}}),s.o(n,"usePathname")&&s.d(a,{usePathname:function(){return n.usePathname}}),s.o(n,"useRouter")&&s.d(a,{useRouter:function(){return n.useRouter}}),s.o(n,"useSearchParams")&&s.d(a,{useSearchParams:function(){return n.useSearchParams}})},9690:function(e,a,s){"use strict";s.r(a);var n=s(57437),l=s(2265),r=s(99376),t=s(18913);s(5507);var i=s(88884);a.default=()=>{let e=(0,r.useRouter)(),a=(0,r.useParams)().companyId,[s,c]=(0,l.useState)(!1),[o,d]=(0,l.useState)([]),[u,m]=(0,l.useState)(!0),[p,h]=(0,l.useState)(null),v=async e=>{try{let i=await fetch("".concat("http://localhost:8080","/api/pre-enrollment/plans/").concat(e),{headers:{"Content-Type":"application/json","user-id":(()=>{let e=localStorage.getItem("userid1")||localStorage.getItem("userId");if(!e)throw Error("User ID not found. Please authenticate first.");return e})()}});if(i.ok){var a,s,n,l,r,t;let e=await i.json();return{planName:(null===(a=e.plan)||void 0===a?void 0:a.planName)||"Unknown Plan",planCode:(null===(s=e.plan)||void 0===s?void 0:s.planCode)||"N/A",planType:(null===(n=e.plan)||void 0===n?void 0:n.planType)||"N/A",coverageType:(null===(l=e.plan)||void 0===l?void 0:l.coverageType)||"Unknown",coverageSubTypes:(null===(r=e.plan)||void 0===r?void 0:r.coverageSubTypes)||[],carrierName:(null===(t=e.carrier)||void 0===t?void 0:t.carrierName)||"Unknown Carrier"}}}catch(a){console.error("Error fetching plan details for planId:",e,a)}return{planName:"Unknown Plan",planCode:"N/A",planType:"N/A",coverageType:"Unknown",coverageSubTypes:[],carrierName:"Unknown Carrier"}},g=async()=>{try{m(!0),h(null);let e=await (0,i.fH)(a,{includePlanData:!0});if(e.success&&e.data){let a=e.data.assignments;console.log("Fetched plan assignments:",a);let s=await Promise.all(a.map(async e=>{let a="string"==typeof e.planId?e.planId:e.planId._id,s=await v(a);return{...e,planDetails:s}}));console.log("Enriched assignments with plan details:",s),d(s)}else h(e.error||"Failed to fetch plan assignments")}catch(e){console.error("Error fetching plan assignments:",e),h("Failed to load plan assignments")}finally{m(!1)}};(0,l.useEffect)(()=>{g()},[a]),(0,l.useEffect)(()=>{let e=setInterval(()=>{console.log("Auto-refreshing plan assignments..."),g()},3e4);return()=>clearInterval(e)},[a]);let y=s=>{e.push("/ai-enroller/manage-groups/".concat(a,"/manage-plans/add-plan?category=").concat(s))},j=e=>{console.log("Edit plan:",e)},b=e=>e.planDetails?{name:e.planDetails.planName,code:e.planDetails.planCode,carrier:e.planDetails.carrierName,type:e.planDetails.planType,coverageType:e.planDetails.coverageType,coverageSubTypes:e.planDetails.coverageSubTypes||[]}:(console.warn("Plan assignment missing planDetails:",e._id),{name:"Unknown Plan",code:"N/A",carrier:"Unknown Carrier",type:"N/A",coverageType:"Unknown",coverageSubTypes:[]}),f=(e,a)=>{let s=new Date(e).toLocaleDateString(),n=new Date(a).toLocaleDateString();return"".concat(s," - ").concat(n)},x=e=>e.coverageSubTypes&&e.coverageSubTypes.length>0?e.coverageSubTypes[0]:e.type||"N/A",N=(()=>{let e={};return o.forEach(a=>{let s=b(a);if(console.log("Categorizing plan:",s.name,"Coverage SubTypes:",s.coverageSubTypes),s.coverageSubTypes&&s.coverageSubTypes.length>0){let n=s.coverageSubTypes[0],l=n.toLowerCase();e[l]||(e[l]=[]),e[l].push(a),console.log('Plan "'.concat(s.name,'" assigned to category: ').concat(n))}else{let n="other";e[n]||(e[n]=[]),e[n].push(a),console.log('Plan "'.concat(s.name,'" assigned to fallback category: ').concat(n))}}),console.log("Final categorization:",Object.keys(e).reduce((a,s)=>(a[s]=e[s].length,a),{})),e})(),P=e=>({medical:"Medical Plans",dental:"Dental Plans",vision:"Vision Plans","term life":"Term Life Plans","supplemental life insurance":"Supplemental Life Plans","short-term disability":"Short-Term Disability Plans","long-term disability":"Long-Term Disability Plans","whole life":"Whole Life Plans","group (employer) life":"Group Life Plans",other:"Other Plans"})[e]||"".concat(e.charAt(0).toUpperCase()+e.slice(1)," Plans");return u?(0,n.jsx)("div",{className:"manage-plans-page",children:(0,n.jsxs)("div",{className:"loading-container",children:[(0,n.jsx)("div",{className:"loading-spinner"}),(0,n.jsx)("p",{children:"Loading plan assignments..."})]})}):p?(0,n.jsx)("div",{className:"manage-plans-page",children:(0,n.jsxs)("div",{className:"error-container",children:[(0,n.jsx)("p",{children:p}),(0,n.jsx)("button",{onClick:g,className:"retry-button",children:"Try Again"})]})}):(0,n.jsxs)("div",{className:"manage-plans-page",children:[(0,n.jsxs)("div",{className:"breadcrumb-nav",children:[(0,n.jsxs)("div",{className:"breadcrumb-item completed",children:[(0,n.jsx)(t.Sul,{size:16}),(0,n.jsx)("span",{children:"Home"})]}),(0,n.jsxs)("div",{className:"breadcrumb-item completed",children:[(0,n.jsx)(t.Sul,{size:16}),(0,n.jsx)("span",{children:"Select Company"})]}),(0,n.jsxs)("div",{className:"breadcrumb-item active",children:[(0,n.jsx)(t.Sul,{size:16}),(0,n.jsx)("span",{children:"View Plans"})]}),(0,n.jsx)("div",{className:"breadcrumb-item",children:(0,n.jsx)("span",{children:"Contributions"})}),(0,n.jsx)("div",{className:"breadcrumb-item",children:(0,n.jsx)("span",{children:"Review"})})]}),(0,n.jsxs)("div",{className:"page-header",children:[(0,n.jsxs)("div",{className:"header-content",children:[(0,n.jsx)("h1",{children:"Current Plan Overview"}),(0,n.jsx)("p",{className:"company-subtitle",children:"for TechCorp Inc."})]}),(0,n.jsxs)("div",{className:"header-actions",children:[(0,n.jsxs)("button",{className:"refresh-btn",onClick:()=>{console.log("Manual refresh triggered"),g()},title:"Refresh plan assignments",children:[(0,n.jsx)(t.zHJ,{size:20}),"Refresh"]}),(0,n.jsxs)("button",{className:"add-new-plan-btn",onClick:()=>{e.push("/ai-enroller/manage-groups/".concat(a,"/manage-plans/add-plan"))},children:[(0,n.jsx)(t.r7I,{size:20}),"Add New Plan"]})]})]}),(0,n.jsxs)("div",{className:"summary-cards",children:[(0,n.jsxs)("div",{className:"summary-card purple",children:[(0,n.jsx)("div",{className:"summary-icon",children:(0,n.jsx)(t.Q5u,{size:24})}),(0,n.jsxs)("div",{className:"summary-content",children:[(0,n.jsx)("div",{className:"summary-label",children:"Total Plans"}),(0,n.jsx)("div",{className:"summary-value",children:o.length}),(0,n.jsx)("div",{className:"summary-subtitle",children:"Active benefit plans"})]})]}),(0,n.jsxs)("div",{className:"summary-card green",children:[(0,n.jsx)("div",{className:"summary-icon",children:(0,n.jsx)(t.Sul,{size:24})}),(0,n.jsxs)("div",{className:"summary-content",children:[(0,n.jsx)("div",{className:"summary-label",children:"Plan Year"}),(0,n.jsx)("div",{className:"summary-value",children:"2024"}),(0,n.jsx)("div",{className:"summary-subtitle",children:"January 1 - December 31"})]})]}),(0,n.jsxs)("div",{className:"summary-card orange",children:[(0,n.jsx)("div",{className:"summary-icon",children:(0,n.jsx)("span",{children:"$"})}),(0,n.jsxs)("div",{className:"summary-content",children:[(0,n.jsx)("div",{className:"summary-label",children:"Est. Monthly Cost"}),(0,n.jsx)("div",{className:"summary-value",children:"$3,032"}),(0,n.jsx)("div",{className:"summary-subtitle",children:"Employer contribution"})]})]})]}),(0,n.jsx)("div",{className:"select-all-section",children:(0,n.jsxs)("label",{className:"select-all-checkbox",children:[(0,n.jsx)("input",{type:"checkbox",checked:s,onChange:()=>{c(!s)}}),(0,n.jsxs)("span",{children:["Select All Plans (0 of ",o.length," selected)"]})]})}),(()=>{let e=Object.keys(N),a=[];for(let s of["medical","dental","vision","term life","supplemental life insurance","whole life","group (employer) life","short-term disability","long-term disability","other"])e.includes(s)&&a.push(s);for(let s of e)a.includes(s)||a.push(s);return a})().map(e=>{let a=N[e];return(0,n.jsxs)("div",{className:"plan-category",children:[(0,n.jsxs)("div",{className:"category-header",children:[(0,n.jsx)("h2",{children:P(e)}),(0,n.jsxs)("span",{className:"plan-count",children:[a.length," plans configured"]}),(0,n.jsxs)("button",{className:"add-plan-btn",onClick:()=>y(e),children:[(0,n.jsx)(t.r7I,{size:16}),"Add Plan"]})]}),(0,n.jsx)("div",{className:"plans-list",children:a.map(e=>{var a;let s=b(e);return(0,n.jsxs)("div",{className:"plan-card",children:[(0,n.jsxs)("div",{className:"plan-card-header",children:[(0,n.jsx)("input",{type:"checkbox",className:"plan-checkbox"}),(0,n.jsxs)("div",{className:"plan-name-section",children:[(0,n.jsx)("h3",{children:s.name}),(0,n.jsx)("div",{className:"plan-carrier",children:s.carrier})]}),(0,n.jsx)("div",{className:"plan-status-badge ".concat((null===(a=e.status)||void 0===a?void 0:a.toLowerCase())||"active"),children:e.status||"Active"})]}),(0,n.jsxs)("div",{className:"plan-details-grid",children:[(0,n.jsxs)("div",{className:"plan-detail",children:[(0,n.jsx)("span",{className:"detail-label",children:"Type:"}),(0,n.jsx)("span",{className:"detail-value",children:x(s)})]}),(0,n.jsxs)("div",{className:"plan-detail",children:[(0,n.jsx)("span",{className:"detail-label",children:"Plan Code:"}),(0,n.jsx)("span",{className:"detail-value",children:s.code})]}),(0,n.jsxs)("div",{className:"plan-detail",children:[(0,n.jsx)("span",{className:"detail-label",children:"Period:"}),(0,n.jsx)("span",{className:"detail-value",children:f(e.planEffectiveDate,e.planEndDate)})]}),(0,n.jsxs)("div",{className:"plan-detail",children:[(0,n.jsx)("span",{className:"detail-label",children:"Group #:"}),(0,n.jsx)("span",{className:"detail-value",children:e.groupNumber||"N/A"})]})]}),(0,n.jsx)("div",{className:"plan-actions",children:(0,n.jsxs)("button",{className:"edit-btn",onClick:()=>j(e._id),children:[(0,n.jsx)(t._vs,{size:16}),"Edit"]})})]},e._id)})})]},e)})]})}},5507:function(){},46231:function(e,a,s){"use strict";s.d(a,{w_:function(){return d}});var n=s(2265),l={color:void 0,size:void 0,className:void 0,style:void 0,attr:void 0},r=n.createContext&&n.createContext(l),t=["attr","size","title"];function i(){return(i=Object.assign?Object.assign.bind():function(e){for(var a=1;a<arguments.length;a++){var s=arguments[a];for(var n in s)Object.prototype.hasOwnProperty.call(s,n)&&(e[n]=s[n])}return e}).apply(this,arguments)}function c(e,a){var s=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);a&&(n=n.filter(function(a){return Object.getOwnPropertyDescriptor(e,a).enumerable})),s.push.apply(s,n)}return s}function o(e){for(var a=1;a<arguments.length;a++){var s=null!=arguments[a]?arguments[a]:{};a%2?c(Object(s),!0).forEach(function(a){var n,l;n=a,l=s[a],(n=function(e){var a=function(e,a){if("object"!=typeof e||!e)return e;var s=e[Symbol.toPrimitive];if(void 0!==s){var n=s.call(e,a||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===a?String:Number)(e)}(e,"string");return"symbol"==typeof a?a:a+""}(n))in e?Object.defineProperty(e,n,{value:l,enumerable:!0,configurable:!0,writable:!0}):e[n]=l}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(s)):c(Object(s)).forEach(function(a){Object.defineProperty(e,a,Object.getOwnPropertyDescriptor(s,a))})}return e}function d(e){return a=>n.createElement(u,i({attr:o({},e.attr)},a),function e(a){return a&&a.map((a,s)=>n.createElement(a.tag,o({key:s},a.attr),e(a.child)))}(e.child))}function u(e){var a=a=>{var s,{attr:l,size:r,title:c}=e,d=function(e,a){if(null==e)return{};var s,n,l=function(e,a){if(null==e)return{};var s={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(a.indexOf(n)>=0)continue;s[n]=e[n]}return s}(e,a);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);for(n=0;n<r.length;n++)s=r[n],!(a.indexOf(s)>=0)&&Object.prototype.propertyIsEnumerable.call(e,s)&&(l[s]=e[s])}return l}(e,t),u=r||a.size||"1em";return a.className&&(s=a.className),e.className&&(s=(s?s+" ":"")+e.className),n.createElement("svg",i({stroke:"currentColor",fill:"currentColor",strokeWidth:"0"},a.attr,l,d,{className:s,style:o(o({color:e.color||a.color},a.style),e.style),height:u,width:u,xmlns:"http://www.w3.org/2000/svg"}),c&&n.createElement("title",null,c),e.children)};return void 0!==r?n.createElement(r.Consumer,null,e=>a(e)):a(l)}}},function(e){e.O(0,[7397,8422,8884,2971,2117,1744],function(){return e(e.s=2458)}),_N_E=e.O()}]);