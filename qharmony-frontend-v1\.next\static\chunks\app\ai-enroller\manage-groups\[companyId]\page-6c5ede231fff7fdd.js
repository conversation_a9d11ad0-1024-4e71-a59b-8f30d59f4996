(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5773],{8706:function(e,s,a){Promise.resolve().then(a.bind(a,69451))},69451:function(e,s,a){"use strict";a.r(s);var n=a(57437),i=a(2265),l=a(99376),c=a(18913),d=a(68575),r=a(99859);a(57549),s.default=()=>{let e=(0,l.useRouter)(),s=(0,l.useParams)();(0,d.I0)();let a=s.companyId,[t,o]=(0,i.useState)([]),[m,h]=(0,i.useState)(!0),[j,v]=(0,i.useState)("overview"),x=(0,d.v9)(e=>e.user.managedCompanies),p=null==x?void 0:x.find(e=>e._id===a),u={totalEmployees:(null==p?void 0:p.companySize)||127,avgAge:35,highRisk:"40%",withDependents:"60%"};(0,i.useEffect)(()=>{p&&N()},[p]),(0,i.useEffect)(()=>{o([{_id:"1",name:"John Doe",email:"<EMAIL>",role:"Manager",isActivated:!0},{_id:"2",name:"Jane Smith",email:"<EMAIL>",role:"Developer",isActivated:!0},{_id:"3",name:"Bob Johnson",email:"<EMAIL>",role:"Designer",isActivated:!1}]),h(!1)},[]);let N=async()=>{try{h(!1)}catch(e){console.error("Error fetching employees:",e),h(!1)}},g=()=>{e.push("/ai-enroller/manage-groups")},y=e=>{switch(e){case"medical":return(0,n.jsx)(c.wkn,{className:"plan-icon medical"});case"dental":return(0,n.jsx)(c.Q5u,{className:"plan-icon dental"});case"vision":return(0,n.jsx)(c.Vvo,{className:"plan-icon vision"});default:return(0,n.jsx)(c.Q5u,{className:"plan-icon"})}};return p?(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(r.Z,{}),(0,n.jsxs)("div",{className:"company-detail-page",children:[(0,n.jsxs)("div",{className:"page-header",children:[(0,n.jsxs)("button",{onClick:g,className:"back-button",children:[(0,n.jsx)(c.Tsu,{size:20}),"Back"]}),(0,n.jsx)("button",{className:"edit-group-btn",children:"Edit Group"})]}),(0,n.jsxs)("div",{className:"company-overview",children:[(0,n.jsx)("h1",{children:"Company Overview"}),(0,n.jsxs)("div",{className:"overview-grid",children:[(0,n.jsxs)("div",{className:"overview-item",children:[(0,n.jsx)("span",{className:"label",children:"Industry"}),(0,n.jsx)("span",{className:"value",children:p.industry})]}),(0,n.jsxs)("div",{className:"overview-item",children:[(0,n.jsx)("span",{className:"label",children:"Employees"}),(0,n.jsx)("span",{className:"value",children:u.totalEmployees})]}),(0,n.jsxs)("div",{className:"overview-item",children:[(0,n.jsx)("span",{className:"label",children:"Location"}),(0,n.jsx)("span",{className:"value",children:p.location||"San Francisco, CA"})]}),(0,n.jsxs)("div",{className:"overview-item",children:[(0,n.jsx)("span",{className:"label",children:"Revenue"}),(0,n.jsx)("span",{className:"value",children:"$50M - $100M"})]})]})]}),(0,n.jsxs)("div",{className:"current-plans",children:[(0,n.jsx)("h2",{children:"Current Plans"}),(0,n.jsx)("div",{className:"plans-grid",children:[{id:"1",name:"Blue Cross Blue Shield",type:"medical",cost:"$450/mo"},{id:"2",name:"Delta Dental",type:"dental",cost:"$85/mo"},{id:"3",name:"VSP",type:"vision",cost:"$25/mo"}].map(e=>(0,n.jsxs)("div",{className:"plan-card",children:[y(e.type),(0,n.jsxs)("div",{className:"plan-info",children:[(0,n.jsx)("h3",{children:e.name}),(0,n.jsx)("span",{className:"plan-cost",children:e.cost})]})]},e.id))})]}),(0,n.jsxs)("div",{className:"statistics-grid",children:[(0,n.jsxs)("div",{className:"stat-card",children:[(0,n.jsx)("div",{className:"stat-number",children:u.totalEmployees}),(0,n.jsx)("div",{className:"stat-label",children:"Total Employees"})]}),(0,n.jsxs)("div",{className:"stat-card",children:[(0,n.jsx)("div",{className:"stat-number",children:u.avgAge}),(0,n.jsx)("div",{className:"stat-label",children:"Avg Age"})]}),(0,n.jsxs)("div",{className:"stat-card",children:[(0,n.jsx)("div",{className:"stat-number",children:u.highRisk}),(0,n.jsx)("div",{className:"stat-label",children:"High Risk"})]}),(0,n.jsxs)("div",{className:"stat-card",children:[(0,n.jsx)("div",{className:"stat-number",children:u.withDependents}),(0,n.jsx)("div",{className:"stat-label",children:"With Dependents"})]})]}),(0,n.jsxs)("div",{className:"feature-cards",children:[(0,n.jsxs)("div",{className:"feature-card",children:[(0,n.jsx)("div",{className:"feature-icon",children:(0,n.jsx)(c.r7I,{size:24})}),(0,n.jsxs)("div",{className:"feature-content",children:[(0,n.jsx)("h3",{children:"Enrichment"}),(0,n.jsx)("p",{children:"Coming Soon"})]})]}),(0,n.jsxs)("div",{className:"feature-card",children:[(0,n.jsx)("div",{className:"feature-icon",children:(0,n.jsx)(c.r7I,{size:24})}),(0,n.jsxs)("div",{className:"feature-content",children:[(0,n.jsx)("h3",{children:"Optimization"}),(0,n.jsx)("p",{children:"Coming Soon"})]})]}),(0,n.jsxs)("div",{className:"feature-card",children:[(0,n.jsx)("div",{className:"feature-icon",children:(0,n.jsx)(c.r7I,{size:24})}),(0,n.jsxs)("div",{className:"feature-content",children:[(0,n.jsx)("h3",{children:"Insights"}),(0,n.jsx)("p",{children:"Coming Soon"})]})]}),(0,n.jsxs)("div",{className:"feature-card clickable",onClick:()=>v("employees"),children:[(0,n.jsx)("div",{className:"feature-icon",children:(0,n.jsx)(c.Otr,{size:24})}),(0,n.jsxs)("div",{className:"feature-content",children:[(0,n.jsx)("h3",{children:"Employees"}),(0,n.jsx)("p",{children:"View employee list"})]})]})]}),(0,n.jsxs)("div",{className:"action-buttons-section",children:[(0,n.jsxs)("button",{className:"manage-plans-btn",onClick:()=>{e.push("/ai-enroller/manage-groups/".concat(a,"/manage-plans"))},children:[(0,n.jsx)(c.Vvo,{size:20}),"Manage Plans"]}),(0,n.jsxs)("button",{className:"add-plans-btn",onClick:()=>{e.push("/ai-enroller/manage-groups/".concat(a,"/add-plans"))},children:[(0,n.jsx)(c.r7I,{size:20}),"Add Plans"]})]}),"employees"===j&&(0,n.jsxs)("div",{className:"employees-section",children:[(0,n.jsxs)("div",{className:"employees-header",children:[(0,n.jsxs)("h2",{children:["Employees (",t.length,")"]}),(0,n.jsx)("button",{className:"close-employees",onClick:()=>v("overview"),children:"\xd7"})]}),(0,n.jsx)("div",{className:"employees-list",children:m?(0,n.jsx)("div",{className:"loading-spinner"}):0===t.length?(0,n.jsx)("p",{children:"No employees found"}):t.map(e=>(0,n.jsxs)("div",{className:"employee-card",children:[(0,n.jsxs)("div",{className:"employee-info",children:[(0,n.jsx)("h4",{children:e.name}),(0,n.jsx)("p",{children:e.email}),(0,n.jsx)("span",{className:"employee-role",children:e.role})]}),(0,n.jsx)("div",{className:"employee-status ".concat(e.isActivated?"active":"inactive"),children:e.isActivated?"Active":"Inactive"})]},e._id))})]})]})]}):(0,n.jsx)("div",{className:"company-detail-page",children:(0,n.jsxs)("div",{className:"loading-container",children:[(0,n.jsx)("p",{children:"Company not found"}),(0,n.jsxs)("button",{onClick:g,className:"back-button",children:[(0,n.jsx)(c.Tsu,{size:20}),"Back to Companies"]})]})})}},83337:function(e,s,a){"use strict";a.d(s,{C:function(){return l},T:function(){return i}});var n=a(68575);let i=()=>(0,n.I0)(),l=n.v9},57549:function(){}},function(e){e.O(0,[6560,139,8422,3463,3301,8575,293,9810,187,3145,9932,3919,208,9859,2971,2117,1744],function(){return e(e.s=8706)}),_N_E=e.O()}]);