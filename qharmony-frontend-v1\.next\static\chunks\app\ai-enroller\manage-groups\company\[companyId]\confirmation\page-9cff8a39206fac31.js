(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8560],{76849:function(e,t,s){Promise.resolve().then(s.bind(s,83546))},99376:function(e,t,s){"use strict";var a=s(35475);s.o(a,"useParams")&&s.d(t,{useParams:function(){return a.useParams}}),s.o(a,"usePathname")&&s.d(t,{usePathname:function(){return a.usePathname}}),s.o(a,"useRouter")&&s.d(t,{useRouter:function(){return a.useRouter}}),s.o(a,"useSearchParams")&&s.d(t,{useSearchParams:function(){return a.useSearchParams}})},83546:function(e,t,s){"use strict";s.r(t),s.d(t,{default:function(){return o}});var a=s(57437),n=s(2265),r=s(99376),l=s(18913),c=s(88884),i=s(61103);function o(){var e;let t=(0,r.useRouter)(),s=(0,r.useParams)(),o=(0,r.useSearchParams)(),m=s.companyId,d=(null===(e=o.get("assignments"))||void 0===e?void 0:e.split(","))||[],[x,u]=(0,n.useState)(null),[p,h]=(0,n.useState)([]),[f,g]=(0,n.useState)(!0);return((0,n.useEffect)(()=>{let e=async()=>{try{g(!0);let e=(0,i.bR)(),t=(0,i.n5)(),s=await fetch("".concat(e,"/api/pre-enrollment/company-benefits-settings/company/").concat(m),{headers:{"Content-Type":"application/json","user-id":t}});if(s.ok){let e=await s.json();u({_id:m,companyName:e.companyName||"Company Name",employeeCount:e.employeeCount||250})}else console.error("Failed to fetch company details:",s.status),u({_id:m,companyName:"Company Name",employeeCount:250});console.log("Fetching plan assignments for company:",m),console.log("Assignment IDs from URL:",d);let a=await (0,c.fH)(m);if(console.log("All plan assignments response:",a),!a.success||!a.data||!a.data.assignments||0===a.data.assignments.length){console.log("No plan assignments found, using fallback data"),h([]);return}let n=a.data.assignments.filter(e=>d.includes(e._id));console.log("Confirmed assignments:",n);let r=await Promise.all(n.map(async s=>{try{let a=await fetch("".concat(e,"/api/pre-enrollment/plans/").concat(s.planId),{headers:{"Content-Type":"application/json","user-id":t}});if(!a.ok)return console.error("Failed to fetch plan details for planId:",s.planId),s;{let e=await a.json();return{...s,planName:e.planName,carrier:e.carrier,planCode:e.planCode,coverageType:e.coverageType,coverageSubtype:e.coverageSubtype}}}catch(e){return console.error("Error fetching plan details:",e),s}}));h(r)}catch(e){console.error("Error fetching data:",e),u({_id:m,companyName:"Company Name",employeeCount:250}),h([])}finally{g(!1)}};m&&d.length>0?(console.log("Starting fetch with companyId:",m,"assignmentIds:",d),e()):(console.log("Skipping fetch - companyId:",m,"assignmentIds:",d),g(!1),u({_id:m,companyName:"Company Name",employeeCount:250}),h([]))},[m,d.join(",")]),f)?(0,a.jsx)("div",{className:"min-h-screen bg-white flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-black mx-auto"}),(0,a.jsx)("p",{className:"mt-4 text-gray-600",children:"Loading confirmation..."})]})}):(0,a.jsxs)("div",{className:"min-h-screen bg-white",children:[(0,a.jsx)("div",{className:"bg-white border-b border-gray-200 px-6 py-4",children:(0,a.jsx)("div",{className:"max-w-7xl mx-auto",children:(0,a.jsxs)("nav",{className:"flex items-center space-x-3",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("div",{className:"w-5 h-5 rounded-full bg-green-500 flex items-center justify-center",children:(0,a.jsx)("span",{className:"text-white text-xs",children:"✓"})}),(0,a.jsx)("span",{className:"text-green-600 text-sm",children:"Home"})]}),(0,a.jsx)("span",{className:"text-gray-400",children:"›"}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("div",{className:"w-5 h-5 rounded-full bg-green-500 flex items-center justify-center",children:(0,a.jsx)("span",{className:"text-white text-xs",children:"✓"})}),(0,a.jsx)("span",{className:"text-green-600 text-sm",children:"Select Company"})]}),(0,a.jsx)("span",{className:"text-gray-400",children:"›"}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("div",{className:"w-5 h-5 rounded-full bg-green-500 flex items-center justify-center",children:(0,a.jsx)("span",{className:"text-white text-xs",children:"✓"})}),(0,a.jsx)("span",{className:"text-green-600 text-sm",children:"View Plans"})]}),(0,a.jsx)("span",{className:"text-gray-400",children:"›"}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("div",{className:"w-5 h-5 rounded-full bg-green-500 flex items-center justify-center",children:(0,a.jsx)("span",{className:"text-white text-xs",children:"✓"})}),(0,a.jsx)("span",{className:"text-green-600 text-sm",children:"Contributions"})]}),(0,a.jsx)("span",{className:"text-gray-400",children:"›"}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("div",{className:"w-5 h-5 rounded-full bg-green-500 flex items-center justify-center",children:(0,a.jsx)("span",{className:"text-white text-xs",children:"✓"})}),(0,a.jsx)("span",{className:"text-green-600 text-sm",children:"Review"})]}),(0,a.jsx)("span",{className:"text-gray-400",children:"›"}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("div",{className:"w-5 h-5 rounded-full bg-green-500 flex items-center justify-center",children:(0,a.jsx)("span",{className:"text-white text-xs",children:"✓"})}),(0,a.jsx)("span",{className:"text-green-600 font-medium text-sm",children:"Confirmation"})]})]})})}),(0,a.jsxs)("div",{className:"max-w-4xl mx-auto px-6 py-12 bg-white text-center",children:[(0,a.jsxs)("div",{className:"mb-8",children:[(0,a.jsx)("div",{className:"w-20 h-20 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-6",children:(0,a.jsx)(l.PjL,{className:"w-12 h-12 text-white"})}),(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-4",children:"Configuration Complete!"}),(0,a.jsxs)("p",{className:"text-lg text-gray-600",children:["All benefit plans have been successfully configured for ",null==x?void 0:x.companyName,"."]})]}),(0,a.jsx)("div",{className:"bg-green-50 border border-green-200 rounded-xl p-4 mb-8 text-left",children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(l.PjL,{className:"text-green-600 w-5 h-5"}),(0,a.jsx)("span",{className:"text-green-800 font-medium",children:"Success!"}),(0,a.jsx)("span",{className:"text-green-700",children:"Your benefit plan configurations have been saved and are now active."})]})}),(0,a.jsxs)("div",{className:"bg-white border border-gray-200 rounded-xl p-6 mb-8 text-left",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3 mb-6",children:[(0,a.jsx)("div",{className:"w-8 h-8 bg-gray-100 rounded-xl flex items-center justify-center",children:(0,a.jsx)("span",{className:"text-gray-600 text-lg",children:"\uD83D\uDCCB"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"text-xl font-semibold text-gray-900",children:"Configuration Summary"}),(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:[p.length," plan assignment",1!==p.length?"s":""," configured for ",null==x?void 0:x.companyName]})]})]}),(0,a.jsx)("div",{className:"space-y-4",children:p.length>0?p.map(e=>(0,a.jsxs)("div",{className:"flex items-center justify-between p-4 bg-gray-50 rounded-xl",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900",children:e.planName||"Plan ID: ".concat(e.planId)}),(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:[e.coverageType||e.type||"Coverage Type",e.coverageSubtype&&" - ".concat(e.coverageSubtype)]}),(0,a.jsxs)("p",{className:"text-xs text-gray-500",children:[e.carrier&&"Carrier: ".concat(e.carrier),e.planCode&&" | Code: ".concat(e.planCode)]}),e.effectiveDate&&e.endDate&&(0,a.jsxs)("p",{className:"text-xs text-gray-500",children:["Effective: ",new Date(e.effectiveDate).toLocaleDateString()," - ",new Date(e.endDate).toLocaleDateString()]}),(0,a.jsxs)("p",{className:"text-xs text-blue-600 font-medium",children:["Status: ",e.status||"Active"]})]}),(0,a.jsx)("div",{className:"w-6 h-6 bg-green-500 rounded-full flex items-center justify-center",children:(0,a.jsx)("span",{className:"text-white text-xs",children:"✓"})})]},e._id)):(0,a.jsxs)("div",{className:"text-center py-8 text-gray-500",children:[(0,a.jsx)("p",{children:"No plan assignments found."}),(0,a.jsxs)("p",{className:"text-sm",children:["Assignment IDs: ",d.join(", ")]})]})})]}),(0,a.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-xl p-6 mb-8 text-left",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"What's Next?"}),(0,a.jsxs)("ul",{className:"space-y-2 text-sm text-gray-700",children:[(0,a.jsxs)("li",{className:"flex items-start gap-2",children:[(0,a.jsx)("span",{className:"text-blue-600 mt-1",children:"•"}),(0,a.jsx)("span",{children:"Employee communications will be generated based on these configurations"})]}),(0,a.jsxs)("li",{className:"flex items-start gap-2",children:[(0,a.jsx)("span",{className:"text-blue-600 mt-1",children:"•"}),(0,a.jsx)("span",{children:"Plan documents will be updated with the new contribution rates"})]}),(0,a.jsxs)("li",{className:"flex items-start gap-2",children:[(0,a.jsx)("span",{className:"text-blue-600 mt-1",children:"•"}),(0,a.jsx)("span",{children:"Enrollment systems will be updated for the next plan year"})]})]})]}),(0,a.jsxs)("div",{className:"flex gap-4 justify-center",children:[(0,a.jsxs)("button",{onClick:()=>{alert("Configuration summary downloaded successfully!")},className:"px-6 py-3 border border-gray-300 text-gray-700 rounded-xl hover:bg-gray-50 transition-colors flex items-center gap-2",children:[(0,a.jsx)(l.yFZ,{className:"w-5 h-5"}),"Download Summary"]}),(0,a.jsxs)("button",{onClick:()=>{t.push("/ai-enroller")},className:"px-6 py-3 bg-black text-white rounded-xl hover:bg-gray-800 transition-colors flex items-center gap-2",children:[(0,a.jsx)(l.VRM,{className:"w-5 h-5"}),"Return to Dashboard"]})]})]})]})}},46231:function(e,t,s){"use strict";s.d(t,{w_:function(){return m}});var a=s(2265),n={color:void 0,size:void 0,className:void 0,style:void 0,attr:void 0},r=a.createContext&&a.createContext(n),l=["attr","size","title"];function c(){return(c=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var s=arguments[t];for(var a in s)Object.prototype.hasOwnProperty.call(s,a)&&(e[a]=s[a])}return e}).apply(this,arguments)}function i(e,t){var s=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),s.push.apply(s,a)}return s}function o(e){for(var t=1;t<arguments.length;t++){var s=null!=arguments[t]?arguments[t]:{};t%2?i(Object(s),!0).forEach(function(t){var a,n;a=t,n=s[t],(a=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var s=e[Symbol.toPrimitive];if(void 0!==s){var a=s.call(e,t||"default");if("object"!=typeof a)return a;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(a))in e?Object.defineProperty(e,a,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[a]=n}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(s)):i(Object(s)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(s,t))})}return e}function m(e){return t=>a.createElement(d,c({attr:o({},e.attr)},t),function e(t){return t&&t.map((t,s)=>a.createElement(t.tag,o({key:s},t.attr),e(t.child)))}(e.child))}function d(e){var t=t=>{var s,{attr:n,size:r,title:i}=e,m=function(e,t){if(null==e)return{};var s,a,n=function(e,t){if(null==e)return{};var s={};for(var a in e)if(Object.prototype.hasOwnProperty.call(e,a)){if(t.indexOf(a)>=0)continue;s[a]=e[a]}return s}(e,t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);for(a=0;a<r.length;a++)s=r[a],!(t.indexOf(s)>=0)&&Object.prototype.propertyIsEnumerable.call(e,s)&&(n[s]=e[s])}return n}(e,l),d=r||t.size||"1em";return t.className&&(s=t.className),e.className&&(s=(s?s+" ":"")+e.className),a.createElement("svg",c({stroke:"currentColor",fill:"currentColor",strokeWidth:"0"},t.attr,n,m,{className:s,style:o(o({color:e.color||t.color},t.style),e.style),height:d,width:d,xmlns:"http://www.w3.org/2000/svg"}),i&&a.createElement("title",null,i),e.children)};return void 0!==r?a.createElement(r.Consumer,null,e=>t(e)):t(n)}}},function(e){e.O(0,[8422,8884,2971,2117,1744],function(){return e(e.s=76849)}),_N_E=e.O()}]);