(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7552],{40054:function(e,s,a){Promise.resolve().then(a.bind(a,44798))},44798:function(e,s,a){"use strict";a.r(s),a.d(s,{default:function(){return c}});var r=a(57437),l=a(2265),t=a(99376),n=a(18913),i=a(99859);function c(){var e;let s=(0,t.useRouter)(),a=(0,t.useParams)(),c=(0,t.useSearchParams)(),o=a.companyId,d=(null===(e=c.get("plans"))||void 0===e?void 0:e.split(","))||[],[m,x]=(0,l.useState)(null),[u,g]=(0,l.useState)([]),[p,h]=(0,l.useState)(!0),[b,y]=(0,l.useState)(""),[j,N]=(0,l.useState)(""),[f,v]=(0,l.useState)(""),[w,P]=(0,l.useState)(""),C=async()=>{try{let e=[{_id:"1",planName:"Blue Cross Blue Shield PPO",planCode:"BCBS-PPO-2024",carrier:"Blue Cross Blue Shield",type:"Medical",metalTier:"PPO",period:"2024-01-01 - 2024-12-31",policyNumber:"POL-*********",status:"active"},{_id:"2",planName:"Aetna Better Health HMO",planCode:"AETNA-HMO-2024",carrier:"Aetna",type:"Medical",metalTier:"HMO",period:"2024-01-01 - 2024-12-31",policyNumber:"POL-*********",status:"active"},{_id:"3",planName:"Delta Dental PPO",planCode:"DD-PPO-2024",carrier:"Delta Dental",type:"Dental",period:"2024-01-01 - 2024-12-31",policyNumber:"POL-*********",status:"active"},{_id:"4",planName:"Guardian Dental HMO",planCode:"GUARD-HMO-2024",carrier:"Guardian",type:"Dental",period:"2024-01-01 - 2024-12-31",policyNumber:"POL-*********",status:"active"},{_id:"5",planName:"VSP Vision Care",planCode:"VSP-2024",carrier:"VSP",type:"Vision",period:"2024-01-01 - 2024-12-31",policyNumber:"POL-*********",status:"active"}].filter(e=>d.includes(e._id));x({_id:o,companyName:"TechCorp Inc."}),g(e)}catch(e){console.error("Error fetching data:",e)}finally{h(!1)}};(0,l.useEffect)(()=>{C()},[o,C]);let S=()=>{s.push("/ai-enroller/manage-groups/company/".concat(o,"/plans"))};return p?(0,r.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"}),(0,r.jsx)("p",{className:"mt-4 text-gray-600",children:"Loading enrollment setup..."})]})}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(i.Z,{}),(0,r.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,r.jsx)("div",{className:"bg-white border-b border-gray-200 px-4 py-3",children:(0,r.jsx)("div",{className:"max-w-7xl mx-auto",children:(0,r.jsxs)("nav",{className:"flex items-center space-x-2 text-sm text-gray-500",children:[(0,r.jsx)("button",{onClick:()=>s.push("/ai-enroller"),className:"hover:text-gray-700",children:"Home"}),(0,r.jsx)("span",{children:"›"}),(0,r.jsx)("button",{onClick:()=>s.push("/ai-enroller/manage-groups/select-company"),className:"hover:text-gray-700",children:"Select Company"}),(0,r.jsx)("span",{children:"›"}),(0,r.jsx)("button",{onClick:()=>s.push("/ai-enroller/manage-groups/company/".concat(o,"/plans")),className:"hover:text-gray-700",children:"View Plans"}),(0,r.jsx)("span",{children:"›"}),(0,r.jsx)("span",{className:"text-gray-900",children:"Set Enrollment Dates"})]})})}),(0,r.jsx)("div",{className:"bg-white border-b border-gray-200 px-4 py-6",children:(0,r.jsxs)("div",{className:"max-w-7xl mx-auto",children:[(0,r.jsx)("div",{className:"flex items-center gap-4 mb-4",children:(0,r.jsxs)("button",{onClick:S,className:"flex items-center gap-2 text-gray-600 hover:text-gray-800 transition-colors",children:[(0,r.jsx)(n.Tsu,{className:"w-5 h-5"}),"Back to Plans"]})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Set Enrollment Dates"}),(0,r.jsxs)("p",{className:"text-gray-600",children:["Configure enrollment and plan effective dates for ",null==m?void 0:m.companyName]})]})]})}),(0,r.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,r.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-6 mb-8",children:[(0,r.jsxs)("h2",{className:"text-lg font-semibold text-blue-900 mb-4",children:["Selected Plans (",u.length,")"]}),(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:u.map(e=>(0,r.jsxs)("div",{className:"bg-white border border-blue-200 rounded-lg p-4",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,r.jsx)(n.PjL,{className:"w-5 h-5 text-green-600"}),(0,r.jsx)("h3",{className:"font-semibold text-gray-900",children:e.planName})]}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:e.carrier}),(0,r.jsxs)("p",{className:"text-xs text-gray-500",children:[e.type," • ",e.planCode]})]},e._id))})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8",children:[(0,r.jsxs)("div",{className:"bg-white rounded-lg border border-gray-200 p-6",children:[(0,r.jsxs)("div",{className:"flex items-center gap-3 mb-6",children:[(0,r.jsx)("div",{className:"w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center",children:(0,r.jsx)(n.Bge,{className:"w-5 h-5 text-purple-600"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h2",{className:"text-xl font-semibold text-gray-900",children:"Enrollment Period"}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"When employees can enroll in benefits"})]})]}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Enrollment Start Date"}),(0,r.jsx)("input",{type:"date",value:b,onChange:e=>y(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 bg-white",style:{color:"#111827",backgroundColor:"white"}})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Enrollment End Date"}),(0,r.jsx)("input",{type:"date",value:j,onChange:e=>N(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 bg-white",style:{color:"#111827",backgroundColor:"white"}})]})]})]}),(0,r.jsxs)("div",{className:"bg-white rounded-lg border border-gray-200 p-6",children:[(0,r.jsxs)("div",{className:"flex items-center gap-3 mb-6",children:[(0,r.jsx)("div",{className:"w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center",children:(0,r.jsx)(n.Bge,{className:"w-5 h-5 text-green-600"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h2",{className:"text-xl font-semibold text-gray-900",children:"Plan Effective Period"}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"When the benefits coverage is active"})]})]}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Plan Start Date"}),(0,r.jsx)("input",{type:"date",value:f,onChange:e=>v(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 bg-white",style:{color:"#111827",backgroundColor:"white"}})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Plan End Date"}),(0,r.jsx)("input",{type:"date",value:w,onChange:e=>P(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 bg-white",style:{color:"#111827",backgroundColor:"white"}})]})]})]})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between mt-8",children:[(0,r.jsxs)("button",{onClick:S,className:"flex items-center gap-2 px-6 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors",children:[(0,r.jsx)(n.Tsu,{className:"w-4 h-4"}),"Back to Plans"]}),(0,r.jsxs)("button",{onClick:()=>{if(!b||!j||!f||!w){alert("Please fill in all date fields to continue.");return}s.push("/ai-enroller/manage-groups/company/".concat(o,"/set-dates?plans=").concat(d.join(",")))},disabled:!b||!j||!f||!w,className:"px-6 py-2 rounded-lg font-medium transition-colors flex items-center gap-2 ".concat(b&&j&&f&&w?"bg-purple-600 text-white hover:bg-purple-700":"bg-gray-300 text-gray-500 cursor-not-allowed"),children:["Save & Continue",(0,r.jsx)("span",{children:"→"})]})]})]})]})]})}},83337:function(e,s,a){"use strict";a.d(s,{C:function(){return t},T:function(){return l}});var r=a(68575);let l=()=>(0,r.I0)(),t=r.v9}},function(e){e.O(0,[139,8422,3463,3301,8575,8685,187,1423,9932,3919,208,9859,2971,2117,1744],function(){return e(e.s=40054)}),_N_E=e.O()}]);