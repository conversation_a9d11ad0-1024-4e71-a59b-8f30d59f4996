(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8659],{98406:function(e,r,t){Promise.resolve().then(t.bind(t,22856))},99376:function(e,r,t){"use strict";var s=t(35475);t.o(s,"useParams")&&t.d(r,{useParams:function(){return s.useParams}}),t.o(s,"usePathname")&&t.d(r,{usePathname:function(){return s.usePathname}}),t.o(s,"useRouter")&&t.d(r,{useRouter:function(){return s.useRouter}}),t.o(s,"useSearchParams")&&t.d(r,{useSearchParams:function(){return s.useSearchParams}})},22856:function(e,r,t){"use strict";t.r(r),t.d(r,{default:function(){return l}});var s=t(57437);t(2265);var n=t(99376),a=t(18913);function l(){let e=(0,n.useRouter)(),r=(0,n.useParams)().companyId;return(0,s.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,s.jsx)("div",{className:"bg-white border-b border-gray-200 px-4 py-3",children:(0,s.jsx)("div",{className:"max-w-7xl mx-auto",children:(0,s.jsxs)("nav",{className:"flex items-center space-x-2 text-sm text-gray-500",children:[(0,s.jsx)("button",{onClick:()=>e.push("/ai-enroller"),className:"hover:text-gray-700",children:"Home"}),(0,s.jsx)("span",{children:"›"}),(0,s.jsx)("button",{onClick:()=>e.push("/ai-enroller/manage-groups/select-company"),className:"hover:text-gray-700",children:"Select Company"}),(0,s.jsx)("span",{children:"›"}),(0,s.jsx)("button",{onClick:()=>e.push("/ai-enroller/manage-groups/company/".concat(r,"/plans")),className:"hover:text-gray-700",children:"View Plans"}),(0,s.jsx)("span",{children:"›"}),(0,s.jsx)("span",{className:"text-gray-400",children:"Contributions"}),(0,s.jsx)("span",{children:"›"}),(0,s.jsx)("span",{className:"text-gray-400",children:"Set Dates"}),(0,s.jsx)("span",{children:"›"}),(0,s.jsx)("span",{className:"text-gray-400",children:"Review"}),(0,s.jsx)("span",{children:"›"}),(0,s.jsx)("span",{className:"text-green-600 font-medium",children:"Confirmation"})]})})}),(0,s.jsxs)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12",children:[(0,s.jsxs)("div",{className:"text-center mb-8",children:[(0,s.jsx)("div",{className:"w-20 h-20 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,s.jsx)(a.PjL,{className:"w-12 h-12 text-green-600"})}),(0,s.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"Configuration Complete!"}),(0,s.jsx)("p",{className:"text-gray-600",children:"All benefit plans have been successfully configured for TechCorp Inc."})]}),(0,s.jsx)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-6 mb-8",children:(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(a.PjL,{className:"w-5 h-5 text-green-600"}),(0,s.jsx)("span",{className:"font-medium text-green-900",children:"Success! Your benefit plan configurations have been saved and are now active."})]})}),(0,s.jsxs)("div",{className:"bg-white rounded-lg border border-gray-200 p-6 mb-8",children:[(0,s.jsx)("div",{className:"flex items-center gap-2 mb-4",children:(0,s.jsx)("span",{className:"text-lg font-semibold text-gray-900",children:"\uD83D\uDCCB Configuration Summary"})}),(0,s.jsx)("p",{className:"text-gray-600 mb-6",children:"Plans configured for TechCorp Inc."}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between p-4 bg-blue-50 border border-blue-200 rounded-lg",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"font-semibold text-blue-900",children:"Blue Cross Blue Shield PPO"}),(0,s.jsxs)("div",{className:"text-sm text-blue-700",children:[(0,s.jsx)("span",{className:"font-medium",children:"Medical"}),(0,s.jsx)("span",{className:"mx-2",children:"•"}),(0,s.jsx)("span",{children:"Code: BCBS-PPO-2024 | Policy: POL-123456789"})]})]}),(0,s.jsx)(a.PjL,{className:"w-6 h-6 text-green-600"})]}),(0,s.jsxs)("div",{className:"flex items-center justify-between p-4 bg-green-50 border border-green-200 rounded-lg",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"font-semibold text-green-900",children:"Delta Dental PPO"}),(0,s.jsxs)("div",{className:"text-sm text-green-700",children:[(0,s.jsx)("span",{className:"font-medium",children:"Dental"}),(0,s.jsx)("span",{className:"mx-2",children:"•"}),(0,s.jsx)("span",{children:"Code: DD-PPO-2024 | Policy: POL-987654321"})]})]}),(0,s.jsx)(a.PjL,{className:"w-6 h-6 text-green-600"})]})]})]}),(0,s.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-6 mb-8",children:[(0,s.jsx)("h2",{className:"font-semibold text-blue-900 mb-3",children:"What's Next?"}),(0,s.jsxs)("ul",{className:"text-sm text-blue-800 space-y-2",children:[(0,s.jsx)("li",{children:"• Employee communications will be generated based on these configurations"}),(0,s.jsx)("li",{children:"• Plan documents will be updated with the new contribution rates"}),(0,s.jsx)("li",{children:"• Enrollment systems will be updated for the next plan year"})]})]}),(0,s.jsxs)("div",{className:"flex justify-center gap-4",children:[(0,s.jsxs)("button",{onClick:()=>{console.log("Download summary")},className:"px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors flex items-center gap-2",children:[(0,s.jsx)(a.yFZ,{className:"w-4 h-4"}),"Download Summary"]}),(0,s.jsxs)("button",{onClick:()=>{e.push("/ai-enroller")},className:"px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center gap-2",children:[(0,s.jsx)(a.VRM,{className:"w-4 h-4"}),"Return to Dashboard"]})]})]})]})}},46231:function(e,r,t){"use strict";t.d(r,{w_:function(){return u}});var s=t(2265),n={color:void 0,size:void 0,className:void 0,style:void 0,attr:void 0},a=s.createContext&&s.createContext(n),l=["attr","size","title"];function c(){return(c=Object.assign?Object.assign.bind():function(e){for(var r=1;r<arguments.length;r++){var t=arguments[r];for(var s in t)Object.prototype.hasOwnProperty.call(t,s)&&(e[s]=t[s])}return e}).apply(this,arguments)}function i(e,r){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(e);r&&(s=s.filter(function(r){return Object.getOwnPropertyDescriptor(e,r).enumerable})),t.push.apply(t,s)}return t}function o(e){for(var r=1;r<arguments.length;r++){var t=null!=arguments[r]?arguments[r]:{};r%2?i(Object(t),!0).forEach(function(r){var s,n;s=r,n=t[r],(s=function(e){var r=function(e,r){if("object"!=typeof e||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var s=t.call(e,r||"default");if("object"!=typeof s)return s;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===r?String:Number)(e)}(e,"string");return"symbol"==typeof r?r:r+""}(s))in e?Object.defineProperty(e,s,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[s]=n}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):i(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}function u(e){return r=>s.createElement(d,c({attr:o({},e.attr)},r),function e(r){return r&&r.map((r,t)=>s.createElement(r.tag,o({key:t},r.attr),e(r.child)))}(e.child))}function d(e){var r=r=>{var t,{attr:n,size:a,title:i}=e,u=function(e,r){if(null==e)return{};var t,s,n=function(e,r){if(null==e)return{};var t={};for(var s in e)if(Object.prototype.hasOwnProperty.call(e,s)){if(r.indexOf(s)>=0)continue;t[s]=e[s]}return t}(e,r);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(s=0;s<a.length;s++)t=a[s],!(r.indexOf(t)>=0)&&Object.prototype.propertyIsEnumerable.call(e,t)&&(n[t]=e[t])}return n}(e,l),d=a||r.size||"1em";return r.className&&(t=r.className),e.className&&(t=(t?t+" ":"")+e.className),s.createElement("svg",c({stroke:"currentColor",fill:"currentColor",strokeWidth:"0"},r.attr,n,u,{className:t,style:o(o({color:e.color||r.color},r.style),e.style),height:d,width:d,xmlns:"http://www.w3.org/2000/svg"}),i&&s.createElement("title",null,i),e.children)};return void 0!==a?s.createElement(a.Consumer,null,e=>r(e)):r(n)}}},function(e){e.O(0,[8422,2971,2117,1744],function(){return e(e.s=98406)}),_N_E=e.O()}]);