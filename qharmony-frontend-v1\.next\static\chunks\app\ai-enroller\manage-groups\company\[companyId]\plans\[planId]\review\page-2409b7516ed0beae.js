(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6181],{91632:function(e,s,t){Promise.resolve().then(t.bind(t,37935))},99376:function(e,s,t){"use strict";var r=t(35475);t.o(r,"useParams")&&t.d(s,{useParams:function(){return r.useParams}}),t.o(r,"usePathname")&&t.d(s,{usePathname:function(){return r.usePathname}}),t.o(r,"useRouter")&&t.d(s,{useRouter:function(){return r.useRouter}}),t.o(r,"useSearchParams")&&t.d(s,{useSearchParams:function(){return r.useSearchParams}})},37935:function(e,s,t){"use strict";t.r(s),t.d(s,{default:function(){return l}});var r=t(57437);t(2265);var a=t(99376),n=t(18913);function l(){let e=(0,a.useRouter)(),s=(0,a.useParams)(),t=s.companyId,l=s.planId;return(0,r.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,r.jsx)("div",{className:"bg-white border-b border-gray-200 px-4 py-3",children:(0,r.jsx)("div",{className:"max-w-7xl mx-auto",children:(0,r.jsxs)("nav",{className:"flex items-center space-x-2 text-sm text-gray-500",children:[(0,r.jsx)("button",{onClick:()=>e.push("/ai-enroller"),className:"hover:text-gray-700",children:"Home"}),(0,r.jsx)("span",{children:"›"}),(0,r.jsx)("button",{onClick:()=>e.push("/ai-enroller/manage-groups/select-company"),className:"hover:text-gray-700",children:"Select Company"}),(0,r.jsx)("span",{children:"›"}),(0,r.jsx)("button",{onClick:()=>e.push("/ai-enroller/manage-groups/company/".concat(t,"/plans")),className:"hover:text-gray-700",children:"View Plans"}),(0,r.jsx)("span",{children:"›"}),(0,r.jsx)("span",{className:"text-gray-400",children:"Contributions"}),(0,r.jsx)("span",{children:"›"}),(0,r.jsx)("span",{className:"text-gray-400",children:"Set Dates"}),(0,r.jsx)("span",{children:"›"}),(0,r.jsx)("span",{className:"text-blue-600 font-medium",children:"Review"})]})})}),(0,r.jsx)("div",{className:"bg-white border-b border-gray-200 px-4 py-6",children:(0,r.jsxs)("div",{className:"max-w-4xl mx-auto flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Final Review & Confirmation"}),(0,r.jsx)("p",{className:"text-gray-600",children:"Review all changes before saving for TechCorp Inc."})]}),(0,r.jsxs)("div",{className:"text-right",children:[(0,r.jsx)("div",{className:"text-sm text-gray-500",children:"Step 5 of 5"}),(0,r.jsx)("div",{className:"text-xs text-blue-600",children:"Final review"})]})]})}),(0,r.jsxs)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,r.jsx)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-4 mb-8",children:(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(n.PjL,{className:"w-5 h-5 text-green-600"}),(0,r.jsx)("span",{className:"font-medium text-green-900",children:"Ready to save! Review the changes below and confirm when ready."})]})}),(0,r.jsxs)("div",{className:"bg-white rounded-lg border border-gray-200 p-6 mb-8",children:[(0,r.jsx)("div",{className:"flex items-center gap-2 mb-4",children:(0,r.jsx)("span",{className:"text-lg font-semibold text-gray-900",children:"\uD83D\uDCCB Plan Summary"})}),(0,r.jsx)("p",{className:"text-gray-600 mb-6",children:"Side-by-side comparison of new vs existing plans"}),(0,r.jsxs)("div",{className:"border border-blue-200 rounded-lg p-6 mb-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Blue Cross Blue Shield PPO"}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"Blue Cross Blue Shield • PPO"})]}),(0,r.jsxs)("div",{className:"flex gap-2",children:[(0,r.jsx)("span",{className:"bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-md",children:"Updated"}),(0,r.jsx)("button",{className:"text-blue-600 hover:text-blue-700",children:(0,r.jsx)(n.Vvo,{className:"w-4 h-4"})})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-3 gap-4 mb-4",children:[(0,r.jsxs)("div",{className:"bg-gray-50 rounded-lg p-4 text-center",children:[(0,r.jsx)("div",{className:"text-sm text-gray-600 mb-1",children:"Total Monthly Premium"}),(0,r.jsx)("div",{className:"text-xl font-bold text-gray-900",children:"$3,310.00"})]}),(0,r.jsxs)("div",{className:"bg-green-50 rounded-lg p-4 text-center",children:[(0,r.jsx)("div",{className:"text-sm text-gray-600 mb-1",children:"Employer Contribution"}),(0,r.jsx)("div",{className:"text-xl font-bold text-green-600",children:"$2,648.00"})]}),(0,r.jsxs)("div",{className:"bg-blue-50 rounded-lg p-4 text-center",children:[(0,r.jsx)("div",{className:"text-sm text-gray-600 mb-1",children:"Employee Contribution"}),(0,r.jsx)("div",{className:"text-xl font-bold text-blue-600",children:"$662.00"})]})]}),(0,r.jsxs)("div",{className:"bg-blue-50 rounded-lg p-4",children:[(0,r.jsx)("h4",{className:"font-medium text-blue-900 mb-2",children:"Changes Made:"}),(0,r.jsxs)("ul",{className:"text-sm text-blue-800 space-y-1",children:[(0,r.jsx)("li",{children:"• Contribution rates updated"}),(0,r.jsx)("li",{children:"• New tier added"})]})]})]}),(0,r.jsxs)("div",{className:"border border-green-200 rounded-lg p-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Delta Dental PPO"}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"Delta Dental • PPO"})]}),(0,r.jsxs)("div",{className:"flex gap-2",children:[(0,r.jsx)("span",{className:"bg-green-100 text-green-800 text-xs px-2 py-1 rounded-md",children:"New"}),(0,r.jsx)("button",{className:"text-blue-600 hover:text-blue-700",children:(0,r.jsx)(n.Vvo,{className:"w-4 h-4"})})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-3 gap-4 mb-4",children:[(0,r.jsxs)("div",{className:"bg-gray-50 rounded-lg p-4 text-center",children:[(0,r.jsx)("div",{className:"text-sm text-gray-600 mb-1",children:"Total Monthly Premium"}),(0,r.jsx)("div",{className:"text-xl font-bold text-gray-900",children:"$480.00"})]}),(0,r.jsxs)("div",{className:"bg-green-50 rounded-lg p-4 text-center",children:[(0,r.jsx)("div",{className:"text-sm text-gray-600 mb-1",children:"Employer Contribution"}),(0,r.jsx)("div",{className:"text-xl font-bold text-green-600",children:"$384.00"})]}),(0,r.jsxs)("div",{className:"bg-blue-50 rounded-lg p-4 text-center",children:[(0,r.jsx)("div",{className:"text-sm text-gray-600 mb-1",children:"Employee Contribution"}),(0,r.jsx)("div",{className:"text-xl font-bold text-blue-600",children:"$96.00"})]})]}),(0,r.jsxs)("div",{className:"bg-green-50 rounded-lg p-4",children:[(0,r.jsx)("h4",{className:"font-medium text-green-900 mb-2",children:"Changes Made:"}),(0,r.jsx)("ul",{className:"text-sm text-green-800 space-y-1",children:(0,r.jsx)("li",{children:"• New plan added"})})]})]})]}),(0,r.jsxs)("div",{className:"bg-white rounded-lg border border-gray-200 p-6 mb-8",children:[(0,r.jsx)("div",{className:"flex items-center gap-2 mb-4",children:(0,r.jsx)("span",{className:"text-lg font-semibold text-gray-900",children:"\uD83D\uDCCA Company Impact Summary"})}),(0,r.jsx)("p",{className:"text-gray-600 mb-6",children:"Total cost impact for TechCorp Inc."}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,r.jsxs)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-6 text-center",children:[(0,r.jsx)("div",{className:"text-sm text-gray-600 mb-2",children:"Total Monthly Employer Cost"}),(0,r.jsx)("div",{className:"text-2xl font-bold text-green-600",children:"$3,032.00"}),(0,r.jsx)("div",{className:"text-xs text-green-600",children:"+$184.00 from previous"})]}),(0,r.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-6 text-center",children:[(0,r.jsx)("div",{className:"text-sm text-gray-600 mb-2",children:"Total Annual Employer Cost"}),(0,r.jsx)("div",{className:"text-2xl font-bold text-blue-600",children:"$36,384.00"}),(0,r.jsx)("div",{className:"text-xs text-blue-600",children:"Projected for full year"})]}),(0,r.jsxs)("div",{className:"bg-orange-50 border border-orange-200 rounded-lg p-6 text-center",children:[(0,r.jsxs)("div",{className:"flex items-center justify-center gap-2 mb-2",children:[(0,r.jsx)("span",{className:"text-orange-600",children:"\uD83D\uDC65"}),(0,r.jsx)("span",{className:"text-sm text-gray-600",children:"Average Cost per Employee"})]}),(0,r.jsx)("div",{className:"text-2xl font-bold text-orange-600",children:"$145.53"}),(0,r.jsx)("div",{className:"text-xs text-orange-600",children:"Based on 250 employees"})]})]})]}),(0,r.jsx)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-4 mb-8",children:(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(n.PjL,{className:"w-5 h-5 text-green-600"}),(0,r.jsx)("span",{className:"font-medium text-green-900",children:"Validation Check: All contribution amounts are within acceptable ranges. No issues detected."})]})}),(0,r.jsx)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4 mb-8",children:(0,r.jsxs)("div",{className:"flex items-start gap-2",children:[(0,r.jsx)("span",{className:"text-blue-600 mt-0.5",children:"⚠️"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"font-medium text-blue-900",children:"Important:"}),(0,r.jsx)("div",{className:"text-sm text-blue-800",children:"Once confirmed, these changes will be applied to TechCorp Inc.'s benefit plans. Plan documents and employee communications should be updated accordingly."})]})]})}),(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsxs)("button",{onClick:()=>{e.push("/ai-enroller/manage-groups/company/".concat(t,"/plans"))},className:"px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors flex items-center gap-2",children:[(0,r.jsx)(n.Vvo,{className:"w-4 h-4"}),"View Plans Page"]}),(0,r.jsxs)("div",{className:"flex gap-3",children:[(0,r.jsxs)("button",{onClick:()=>{console.log("Download summary")},className:"px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors flex items-center gap-2",children:[(0,r.jsx)(n.yFZ,{className:"w-4 h-4"}),"Download Summary"]}),(0,r.jsx)("button",{onClick:()=>{e.push("/ai-enroller/manage-groups/company/".concat(t,"/plans/").concat(l,"/confirmation"))},className:"px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors",children:"Confirm and Save"})]})]})]})]})}},46231:function(e,s,t){"use strict";t.d(s,{w_:function(){return o}});var r=t(2265),a={color:void 0,size:void 0,className:void 0,style:void 0,attr:void 0},n=r.createContext&&r.createContext(a),l=["attr","size","title"];function i(){return(i=Object.assign?Object.assign.bind():function(e){for(var s=1;s<arguments.length;s++){var t=arguments[s];for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])}return e}).apply(this,arguments)}function c(e,s){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);s&&(r=r.filter(function(s){return Object.getOwnPropertyDescriptor(e,s).enumerable})),t.push.apply(t,r)}return t}function d(e){for(var s=1;s<arguments.length;s++){var t=null!=arguments[s]?arguments[s]:{};s%2?c(Object(t),!0).forEach(function(s){var r,a;r=s,a=t[s],(r=function(e){var s=function(e,s){if("object"!=typeof e||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var r=t.call(e,s||"default");if("object"!=typeof r)return r;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===s?String:Number)(e)}(e,"string");return"symbol"==typeof s?s:s+""}(r))in e?Object.defineProperty(e,r,{value:a,enumerable:!0,configurable:!0,writable:!0}):e[r]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):c(Object(t)).forEach(function(s){Object.defineProperty(e,s,Object.getOwnPropertyDescriptor(t,s))})}return e}function o(e){return s=>r.createElement(x,i({attr:d({},e.attr)},s),function e(s){return s&&s.map((s,t)=>r.createElement(s.tag,d({key:t},s.attr),e(s.child)))}(e.child))}function x(e){var s=s=>{var t,{attr:a,size:n,title:c}=e,o=function(e,s){if(null==e)return{};var t,r,a=function(e,s){if(null==e)return{};var t={};for(var r in e)if(Object.prototype.hasOwnProperty.call(e,r)){if(s.indexOf(r)>=0)continue;t[r]=e[r]}return t}(e,s);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);for(r=0;r<n.length;r++)t=n[r],!(s.indexOf(t)>=0)&&Object.prototype.propertyIsEnumerable.call(e,t)&&(a[t]=e[t])}return a}(e,l),x=n||s.size||"1em";return s.className&&(t=s.className),e.className&&(t=(t?t+" ":"")+e.className),r.createElement("svg",i({stroke:"currentColor",fill:"currentColor",strokeWidth:"0"},s.attr,a,o,{className:t,style:d(d({color:e.color||s.color},s.style),e.style),height:x,width:x,xmlns:"http://www.w3.org/2000/svg"}),c&&r.createElement("title",null,c),e.children)};return void 0!==n?r.createElement(n.Consumer,null,e=>s(e)):s(a)}}},function(e){e.O(0,[8422,2971,2117,1744],function(){return e(e.s=91632)}),_N_E=e.O()}]);