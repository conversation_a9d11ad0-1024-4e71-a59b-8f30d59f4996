(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2948],{19204:function(e,r,t){Promise.resolve().then(t.bind(t,44275))},99376:function(e,r,t){"use strict";var s=t(35475);t.o(s,"useParams")&&t.d(r,{useParams:function(){return s.useParams}}),t.o(s,"usePathname")&&t.d(r,{usePathname:function(){return s.usePathname}}),t.o(s,"useRouter")&&t.d(r,{useRouter:function(){return s.useRouter}}),t.o(s,"useSearchParams")&&t.d(r,{useSearchParams:function(){return s.useSearchParams}})},44275:function(e,r,t){"use strict";t.r(r),t.d(r,{default:function(){return i}});var s=t(57437),n=t(2265),a=t(99376),l=t(18913);function i(){let e=(0,a.useRouter)(),r=(0,a.useParams)(),t=r.companyId,i=r.planId,[c,o]=(0,n.useState)(!0),[d,u]=(0,n.useState)("0"),[m,x]=(0,n.useState)("30");return(0,s.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,s.jsx)("div",{className:"bg-white border-b border-gray-200 px-4 py-3",children:(0,s.jsx)("div",{className:"max-w-7xl mx-auto",children:(0,s.jsxs)("nav",{className:"flex items-center space-x-2 text-sm text-gray-500",children:[(0,s.jsx)("button",{onClick:()=>e.push("/ai-enroller"),className:"hover:text-gray-700",children:"Home"}),(0,s.jsx)("span",{children:"›"}),(0,s.jsx)("button",{onClick:()=>e.push("/ai-enroller/manage-groups/select-company"),className:"hover:text-gray-700",children:"Select Company"}),(0,s.jsx)("span",{children:"›"}),(0,s.jsx)("button",{onClick:()=>e.push("/ai-enroller/manage-groups/company/".concat(t,"/plans")),className:"hover:text-gray-700",children:"View Plans"}),(0,s.jsx)("span",{children:"›"}),(0,s.jsx)("span",{className:"text-gray-900",children:"Define Contributions"}),(0,s.jsx)("span",{children:"›"}),(0,s.jsx)("span",{className:"text-blue-600 font-medium",children:"Set Dates"}),(0,s.jsx)("span",{children:"›"}),(0,s.jsx)("span",{className:"text-gray-400",children:"Review"})]})})}),(0,s.jsx)("div",{className:"bg-white border-b border-gray-200 px-4 py-6",children:(0,s.jsxs)("div",{className:"max-w-4xl mx-auto flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Set Enrollment & Active Dates"}),(0,s.jsx)("p",{className:"text-gray-600",children:"Configure dates for Blue Cross Blue Shield PPO for TechCorp Inc."})]}),(0,s.jsxs)("div",{className:"text-right",children:[(0,s.jsx)("div",{className:"text-sm text-gray-500",children:"Step 4 of 5"}),(0,s.jsx)("div",{className:"text-xs text-blue-600",children:"Set plan dates"})]})]})}),(0,s.jsxs)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,s.jsx)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4 mb-8",children:(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(l.if7,{className:"w-5 h-5 text-blue-600"}),(0,s.jsx)("span",{className:"font-medium text-blue-900",children:"Plan: Blue Cross Blue Shield PPO for TechCorp Inc."})]})}),(0,s.jsxs)("div",{className:"bg-white rounded-lg border border-gray-200 p-6 mb-8",children:[(0,s.jsx)("h2",{className:"text-lg font-semibold text-gray-900 mb-4",children:"\uD83D\uDCC5 Date Configuration"}),(0,s.jsx)("p",{className:"text-gray-600 mb-6",children:"Set enrollment and plan active dates for this specific plan"}),(0,s.jsx)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6",children:(0,s.jsxs)("label",{className:"flex items-start gap-3 cursor-pointer",children:[(0,s.jsx)("input",{type:"checkbox",checked:c,onChange:e=>o(e.target.checked),className:"w-5 h-5 text-blue-600 rounded mt-0.5"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"font-medium text-blue-900",children:"Use Global Company Dates"}),(0,s.jsx)("div",{className:"text-sm text-blue-700",children:"This plan will use the company-wide enrollment and plan dates"}),(0,s.jsx)("div",{className:"flex items-center gap-1 mt-1",children:(0,s.jsx)(l.if7,{className:"w-4 h-4 text-blue-600"})})]})]})}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 mb-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"font-medium text-gray-900 mb-2",children:"Global Enrollment Period"}),(0,s.jsx)("div",{className:"text-sm text-gray-600",children:"November 1 - November 30, 2024"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"font-medium text-gray-900 mb-2",children:"Global Plan Period"}),(0,s.jsx)("div",{className:"text-sm text-gray-600",children:"January 1 - December 31, 2025"})]})]})]}),(0,s.jsxs)("div",{className:"bg-white rounded-lg border border-gray-200 p-6 mb-8",children:[(0,s.jsx)("h2",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Additional Settings"}),(0,s.jsx)("p",{className:"text-gray-600 mb-6",children:"Optional configuration for this plan"}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Waiting Period (days)"}),(0,s.jsx)("input",{type:"number",value:d,onChange:e=>u(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",min:"0",max:"365"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Grace Period (days)"}),(0,s.jsx)("input",{type:"number",value:m,onChange:e=>x(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",min:"0",max:"90"})]})]})]}),(0,s.jsx)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-4 mb-8",children:(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("div",{className:"w-5 h-5 bg-green-500 rounded-full flex items-center justify-center",children:(0,s.jsx)("span",{className:"text-white text-xs",children:"✓"})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"font-medium text-green-900",children:"Plan Configuration Complete"}),(0,s.jsx)("div",{className:"text-sm text-green-700",children:"You can now return to configure other plans or continue the workflow"})]})]})}),(0,s.jsxs)("div",{className:"flex justify-between",children:[(0,s.jsx)("button",{onClick:()=>{e.push("/ai-enroller/manage-groups/company/".concat(t,"/plans"))},className:"px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors",children:"Save & Return to Plans"}),(0,s.jsxs)("button",{onClick:()=>{e.push("/ai-enroller/manage-groups/company/".concat(t,"/plans/").concat(i,"/review"))},className:"px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center gap-2",children:["Continue to Review",(0,s.jsx)("span",{children:"→"})]})]})]})]})}},46231:function(e,r,t){"use strict";t.d(r,{w_:function(){return d}});var s=t(2265),n={color:void 0,size:void 0,className:void 0,style:void 0,attr:void 0},a=s.createContext&&s.createContext(n),l=["attr","size","title"];function i(){return(i=Object.assign?Object.assign.bind():function(e){for(var r=1;r<arguments.length;r++){var t=arguments[r];for(var s in t)Object.prototype.hasOwnProperty.call(t,s)&&(e[s]=t[s])}return e}).apply(this,arguments)}function c(e,r){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(e);r&&(s=s.filter(function(r){return Object.getOwnPropertyDescriptor(e,r).enumerable})),t.push.apply(t,s)}return t}function o(e){for(var r=1;r<arguments.length;r++){var t=null!=arguments[r]?arguments[r]:{};r%2?c(Object(t),!0).forEach(function(r){var s,n;s=r,n=t[r],(s=function(e){var r=function(e,r){if("object"!=typeof e||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var s=t.call(e,r||"default");if("object"!=typeof s)return s;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===r?String:Number)(e)}(e,"string");return"symbol"==typeof r?r:r+""}(s))in e?Object.defineProperty(e,s,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[s]=n}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):c(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}function d(e){return r=>s.createElement(u,i({attr:o({},e.attr)},r),function e(r){return r&&r.map((r,t)=>s.createElement(r.tag,o({key:t},r.attr),e(r.child)))}(e.child))}function u(e){var r=r=>{var t,{attr:n,size:a,title:c}=e,d=function(e,r){if(null==e)return{};var t,s,n=function(e,r){if(null==e)return{};var t={};for(var s in e)if(Object.prototype.hasOwnProperty.call(e,s)){if(r.indexOf(s)>=0)continue;t[s]=e[s]}return t}(e,r);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(s=0;s<a.length;s++)t=a[s],!(r.indexOf(t)>=0)&&Object.prototype.propertyIsEnumerable.call(e,t)&&(n[t]=e[t])}return n}(e,l),u=a||r.size||"1em";return r.className&&(t=r.className),e.className&&(t=(t?t+" ":"")+e.className),s.createElement("svg",i({stroke:"currentColor",fill:"currentColor",strokeWidth:"0"},r.attr,n,d,{className:t,style:o(o({color:e.color||r.color},r.style),e.style),height:u,width:u,xmlns:"http://www.w3.org/2000/svg"}),c&&s.createElement("title",null,c),e.children)};return void 0!==a?s.createElement(a.Consumer,null,e=>r(e)):r(n)}}},function(e){e.O(0,[8422,2971,2117,1744],function(){return e(e.s=19204)}),_N_E=e.O()}]);