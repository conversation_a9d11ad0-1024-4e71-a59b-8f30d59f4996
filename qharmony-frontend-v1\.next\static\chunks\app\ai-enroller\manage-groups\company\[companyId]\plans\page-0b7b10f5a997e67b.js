(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2717],{22248:function(e,t,a){Promise.resolve().then(a.bind(a,37993))},37993:function(e,t,a){"use strict";a.r(t),a.d(t,{default:function(){return p}});var s=a(57437),n=a(2265),l=a(99376),r=a(18913);a(2778);var o=a(57804),i=e=>{let{isOpen:t,onClose:a,type:n,title:l,message:o,confirmText:i="OK",onConfirm:c}=e;if(!t)return null;let d=(()=>{switch(n){case"success":return{bg:"bg-green-50",border:"border-green-200",button:"bg-green-600 hover:bg-green-700"};case"error":return{bg:"bg-red-50",border:"border-red-200",button:"bg-red-600 hover:bg-red-700"};case"warning":return{bg:"bg-yellow-50",border:"border-yellow-200",button:"bg-yellow-600 hover:bg-yellow-700"};default:return{bg:"bg-blue-50",border:"border-blue-200",button:"bg-blue-600 hover:bg-blue-700"}}})();return(0,s.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,s.jsxs)("div",{className:"bg-white rounded-lg shadow-xl max-w-md w-full mx-4",children:[(0,s.jsx)("div",{className:"".concat(d.bg," ").concat(d.border," border-b px-6 py-4 rounded-t-lg"),children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[(()=>{switch(n){case"success":return(0,s.jsx)(r.PjL,{className:"w-12 h-12 text-green-500"});case"error":return(0,s.jsx)(r.C9x,{className:"w-12 h-12 text-red-500"});case"warning":return(0,s.jsx)(r.baL,{className:"w-12 h-12 text-yellow-500"});default:return(0,s.jsx)(r.if7,{className:"w-12 h-12 text-blue-500"})}})(),(0,s.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:l})]}),(0,s.jsx)("button",{onClick:a,className:"text-gray-400 hover:text-gray-600 transition-colors",children:(0,s.jsx)(r.apv,{className:"w-6 h-6"})})]})}),(0,s.jsx)("div",{className:"px-6 py-4",children:(0,s.jsx)("p",{className:"text-gray-700 leading-relaxed",children:o})}),(0,s.jsxs)("div",{className:"px-6 py-4 bg-gray-50 rounded-b-lg flex justify-end space-x-3",children:[(0,s.jsx)("button",{onClick:a,className:"px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors",children:"Cancel"}),(0,s.jsx)("button",{onClick:()=>{c&&c(),a()},className:"px-6 py-2 ".concat(d.button," text-white rounded-lg transition-colors font-medium"),children:i})]})]})})};let c=()=>{let[e,t]=(0,n.useState)({isOpen:!1,type:"info",title:"",message:""}),a=(e,a,s,n,l)=>{t({isOpen:!0,type:e,title:a,message:s,confirmText:n,onConfirm:l})};return{notification:e,showNotification:a,hideNotification:()=>{t(e=>({...e,isOpen:!1}))},showSuccess:(e,t,s,n)=>{a("success",e,t,s,n)},showError:(e,t,s,n)=>{a("error",e,t,s,n)},showWarning:(e,t,s,n)=>{a("warning",e,t,s,n)},showInfo:(e,t,s,n)=>{a("info",e,t,s,n)}}};var d=a(88884),m=e=>{let{isOpen:t,onClose:l,onSelectPlan:m,onCreatePlan:u,availablePlans:x=[],companyId:p,editMode:g=!1,editingPlan:h=null,editingCoverageTiers:y=[]}=e,[b,f]=(0,n.useState)(""),[v,j]=(0,n.useState)(!1),[N,w]=(0,n.useState)("plan"),[D,C]=(0,n.useState)([]),[P,S]=(0,n.useState)(!1),[E,T]=(0,n.useState)(!1),[k,A]=(0,n.useState)(null),{notification:F,showSuccess:L,showError:I,showWarning:_,hideNotification:M}=c(),[O,z]=(0,n.useState)(""),[R,B]=(0,n.useState)(null),[U,W]=(0,n.useState)("All Statuses"),[Y,$]=(0,n.useState)("All Carriers"),[V,G]=(0,n.useState)(g?"configure":"select"),[H,K]=(0,n.useState)("percentage"),[Z,q]=(0,n.useState)([{id:"1",tier:"Employee Only",premium:450,employerPercent:80,employerPays:360,employeePays:90},{id:"2",tier:"Employee + Spouse",premium:880,employerPercent:80,employerPays:704,employeePays:176},{id:"3",tier:"Employee + Children",premium:720,employerPercent:80,employerPays:576,employeePays:144},{id:"4",tier:"Employee + Family",premium:1250,employerPercent:80,employerPays:1e3,employeePays:250}]),[J,Q]=(0,n.useState)("2024-11-01"),[X,ee]=(0,n.useState)("2024-11-30"),[et,ea]=(0,n.useState)("2025-01-01"),[es,en]=(0,n.useState)("2025-12-31"),el=async()=>{try{S(!0),A(null);let e=await (0,d.Pb)();e.success&&e.data?(C(e.data),e.data.length>0&&w(e.data[0].coverageType||"plan")):(A(e.error||"Failed to fetch assignable plans"),C(x))}catch(e){console.error("Error fetching assignable plans:",e),A("Failed to fetch assignable plans"),C(x)}finally{S(!1)}};if((0,n.useEffect)(()=>{t&&el()},[t]),(0,n.useEffect)(()=>{if(g&&h&&t){console.log("\uD83D\uDD27 Initializing edit mode with plan:",h),console.log("\uD83D\uDD27 Editing coverage tiers received:",y);let e=h.planId||h._id;if(B({_id:e,planName:h.planName,planCode:h.planCode,coverageType:h.coverageType||h.type,planType:h.type,metalTier:h.metalTier}),z(e),y&&y.length>0?(console.log("\uD83D\uDD27 Setting coverage tiers in modal:",y),q(y)):console.log("\uD83D\uDD27 No coverage tiers provided, using default"),h.enrollmentStartDate){let e=h.enrollmentStartDate.split("T")[0];console.log("\uD83D\uDD27 Setting enrollment start date:",e),Q(e)}if(h.enrollmentEndDate){let e=h.enrollmentEndDate.split("T")[0];console.log("\uD83D\uDD27 Setting enrollment end date:",e),ee(e)}if(h.planEffectiveDate){let e=h.planEffectiveDate.split("T")[0];console.log("\uD83D\uDD27 Setting plan start date:",e),ea(e)}if(h.planEndDate){let e=h.planEndDate.split("T")[0];console.log("\uD83D\uDD27 Setting plan end date:",e),en(e)}G("configure")}},[g,h,y,t]),(0,n.useEffect)(()=>{!g&&t&&(console.log("\uD83D\uDD04 Resetting modal to default state (not edit mode)"),G("select"),B(null),z(""),q([{id:"1",tier:"Employee Only",premium:450,employerPercent:80,employerPays:360,employeePays:90},{id:"2",tier:"Employee + Spouse",premium:880,employerPercent:80,employerPays:704,employeePays:176},{id:"3",tier:"Employee + Children",premium:720,employerPercent:80,employerPays:576,employeePays:144},{id:"4",tier:"Employee + Family",premium:1250,employerPercent:80,employerPays:1e3,employeePays:250}]))},[g,t]),!t)return null;let er=D.filter(e=>{let t=""===b||(e.planName||"").toLowerCase().includes(b.toLowerCase())||(e.planCode||"").toLowerCase().includes(b.toLowerCase())||(e.planType||"").toLowerCase().includes(b.toLowerCase())||(e.coverageType||"").toLowerCase().includes(b.toLowerCase())||e.coverageSubTypes&&e.coverageSubTypes.some(e=>e.toLowerCase().includes(b.toLowerCase())),a="All Statuses"===U||(e.status||"Active")===U,s="All Carriers"===Y||e.coverageType===Y||e.coverageSubTypes&&e.coverageSubTypes.includes(Y);return t&&a&&s}),eo=e=>{console.log("\uD83D\uDD0D Plan Selection Debug:",e),console.log("\uD83D\uDD0D Plan _id:",e._id),console.log("\uD83D\uDD0D Plan keys:",Object.keys(e)),z(e._id),B(e),G("configure")},ei=(e,t,a)=>{q(s=>s.map(s=>{if(s.id===e){let e={...s,[t]:a};if("percentage"===H){if("premium"===t||"employerPercent"===t){let s="premium"===t?a:e.premium,n="employerPercent"===t?a:e.employerPercent;e.employerPays=s*n/100,e.employeePays=s-e.employerPays}}else if("premium"===t||"employerPays"===t){let s="premium"===t?a:e.premium,n="employerPays"===t?a:e.employerPays;e.employerPays=n,e.employeePays=s-n,e.employerPercent=s>0?Math.round(n/s*100):0}return e}return s}))},ec=async()=>{if(!R||!p){I("Missing Information","Please ensure a plan is selected and company information is available.");return}if(E){console.log("\uD83D\uDEAB Already submitting, ignoring duplicate request");return}try{let e;T(!0),console.log("\uD83D\uDD0D Modal Debug - Selected Plan:",R),console.log("\uD83D\uDD0D Modal Debug - Company ID:",p),console.log("\uD83D\uDD0D Modal Debug - Coverage Tiers:",Z),console.log("\uD83D\uDD0D Modal Debug - Dates:",{enrollmentStartDate:J,enrollmentEndDate:X,planStartDate:et,planEndDate:es});let t={planId:R._id,companyId:p,rateStructure:"Composite",coverageTiers:Z.map(e=>({tierName:e.tier,totalCost:e.premium,employerCost:e.employerPays,employeeCost:e.employeePays})),enrollmentStartDate:new Date(J).toISOString(),enrollmentEndDate:new Date(X).toISOString(),planEffectiveDate:new Date(et).toISOString(),planEndDate:new Date(es).toISOString(),assignmentYear:new Date(es).getFullYear(),assignmentExpiry:new Date(es).toISOString(),employerContribution:{contributionType:"Percentage",contributionAmount:80},employeeContribution:{contributionType:"Percentage",contributionAmount:20},ageBandedRates:[],salaryBasedRates:[],planCustomizations:{},waitingPeriod:{enabled:!1,days:0,rule:"Immediate"},eligibleEmployeeClasses:["Full-Time"],enrollmentType:"Active",status:"Active",isActive:!0},{createPlanAssignment:s,updatePlanAssignment:n}=await Promise.resolve().then(a.bind(a,88884));g&&h?(console.log("\uD83D\uDD27 Updating plan assignment:",h._id),e=await n(h._id,t)):(console.log("\uD83C\uDD95 Creating new plan assignment"),e=await s(t)),e.success?L(g?"Plan Assignment Updated!":"Plan Assignment Created!",g?"The plan assignment has been successfully updated with your changes.":"The plan has been successfully assigned to the company with your configured settings.","Continue",()=>{window.location.reload(),l()}):I(g?"Update Failed":"Assignment Failed","Failed to ".concat(g?"update":"create"," plan assignment: ").concat(e.error||"Unknown error occurred"))}catch(e){console.error("Error creating plan assignment:",e),I("Assignment Error","An unexpected error occurred while creating the plan assignment. Please try again.")}finally{T(!1)}};return(0,s.jsxs)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:[(0,s.jsx)("div",{className:"bg-white rounded-lg shadow-xl w-full max-w-7xl max-h-[95vh] overflow-y-auto",children:(0,s.jsxs)("div",{className:"p-6",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,s.jsxs)("div",{className:"flex items-center gap-4",children:["select"!==V&&(0,s.jsx)("button",{onClick:"dates"===V?()=>{G("configure")}:()=>{G("select"),z(""),B(null)},className:"text-gray-600 hover:text-gray-800 transition-colors",children:"← Back"}),(0,s.jsx)("h2",{className:"text-2xl font-bold text-gray-900",children:(()=>{let e=g?"Edit":"";switch(V){case"select":return v?"Create a new plan":"Select a plan";case"configure":return"".concat(e," Configure Coverage Tiers - ").concat(null==R?void 0:R.planName);case"dates":return"".concat(e," Set Enrollment & Coverage Dates - ").concat(null==R?void 0:R.planName);default:return"Select a plan"}})()})]}),(0,s.jsxs)("div",{className:"flex items-center gap-3",children:["select"===V&&!v&&(0,s.jsxs)("button",{onClick:()=>j(!0),className:"flex items-center gap-2 bg-gray-900 text-white px-4 py-2 rounded-lg hover:bg-gray-800 transition-colors",children:[(0,s.jsx)(r.r7I,{className:"w-4 h-4"}),"Create new plan"]}),(0,s.jsx)("button",{onClick:l,className:"text-gray-400 hover:text-gray-600 transition-colors",children:(0,s.jsx)(r.fMW,{className:"w-6 h-6"})})]})]}),(0,s.jsx)("div",{className:"flex items-center justify-center mb-8",children:(0,s.jsxs)("div",{className:"flex items-center gap-4",children:[(0,s.jsx)("div",{className:"flex items-center justify-center w-8 h-8 rounded-full ".concat("select"===V?"bg-blue-600 text-white":"bg-gray-200 text-gray-600"),children:"1"}),(0,s.jsx)("div",{className:"w-16 h-1 bg-gray-200"}),(0,s.jsx)("div",{className:"flex items-center justify-center w-8 h-8 rounded-full ".concat("configure"===V?"bg-blue-600 text-white":"bg-gray-200 text-gray-600"),children:"2"}),(0,s.jsx)("div",{className:"w-16 h-1 bg-gray-200"}),(0,s.jsx)("div",{className:"flex items-center justify-center w-8 h-8 rounded-full ".concat("dates"===V?"bg-blue-600 text-white":"bg-gray-200 text-gray-600"),children:"3"})]})}),"select"===V&&(0,s.jsx)(s.Fragment,{children:v?(0,s.jsx)(o.Z,{onCancel:()=>j(!1),onSubmit:e=>{console.log("\uD83C\uDFAF Plan created successfully, auto-selecting for configuration:",e),B(e),z(e._id),j(!1),G("configure"),C(t=>[e,...t])}}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)("div",{className:"mb-6",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2 mb-4",children:[(0,s.jsx)(r.O6C,{className:"w-5 h-5 text-gray-400"}),(0,s.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Search & Filter"})]}),(0,s.jsxs)("div",{className:"flex gap-4 items-center",children:[(0,s.jsxs)("div",{className:"relative flex-1",children:[(0,s.jsx)("input",{type:"text",placeholder:"Search by plan name, code, or coverage subtype...",className:"w-full px-4 py-2.5 pl-10 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-gray-900 bg-white",value:b,onChange:e=>f(e.target.value)}),(0,s.jsx)(r.O6C,{className:"absolute left-3 top-3 text-gray-400 w-5 h-5"})]}),(0,s.jsxs)("select",{className:"px-3 py-2.5 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white text-gray-700 min-w-[140px]",value:U,onChange:e=>W(e.target.value),children:[(0,s.jsx)("option",{children:"All Statuses"}),(0,s.jsx)("option",{children:"Active"}),(0,s.jsx)("option",{children:"Draft"}),(0,s.jsx)("option",{children:"Archived"})]}),(0,s.jsxs)("select",{className:"px-3 py-2.5 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white text-gray-700 min-w-[140px]",value:Y,onChange:e=>$(e.target.value),children:[(0,s.jsx)("option",{children:"All Carriers"}),(0,s.jsx)("option",{children:"Your Health"}),(0,s.jsx)("option",{children:"Income Security"})]})]}),(0,s.jsxs)("div",{className:"mt-3 text-sm text-gray-600",children:["Showing ",er.length," of ",D.length," plans"]})]}),(0,s.jsx)("div",{className:"mb-6",children:(0,s.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Plans List"})}),P&&(0,s.jsxs)("div",{className:"flex justify-center items-center py-8",children:[(0,s.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600"}),(0,s.jsx)("span",{className:"ml-2 text-gray-600",children:"Loading plans..."})]}),k&&(0,s.jsxs)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4 mb-4",children:[(0,s.jsx)("p",{className:"text-red-600 text-sm",children:k}),(0,s.jsx)("button",{onClick:el,className:"mt-2 text-red-600 hover:text-red-800 text-sm font-medium",children:"Try again"})]}),!P&&!k&&(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"grid grid-cols-12 gap-6 px-6 py-3 bg-gray-50 rounded-lg text-sm font-medium text-gray-700",children:[(0,s.jsx)("div",{className:"col-span-3",children:"Plan Name"}),(0,s.jsx)("div",{className:"col-span-3",children:"Plan Code"}),(0,s.jsx)("div",{className:"col-span-2",children:"Coverage Subtype"}),(0,s.jsx)("div",{className:"col-span-1",children:"Status"}),(0,s.jsx)("div",{className:"col-span-1",children:"Metal Tier"}),(0,s.jsx)("div",{className:"col-span-2",children:"Action"})]}),0===er.length?(0,s.jsx)("div",{className:"text-center py-8 text-gray-500",children:b?"No plans found matching your search.":"No plans available."}):er.map(e=>(0,s.jsxs)("div",{className:"grid grid-cols-12 gap-6 px-6 py-4 bg-white border rounded-lg hover:shadow-md transition-all ".concat(O===e._id?"border-blue-300 bg-blue-50 shadow-md":"border-gray-200 hover:border-gray-300"),children:[(0,s.jsxs)("div",{className:"col-span-3",children:[(0,s.jsx)("div",{className:"font-semibold text-gray-900",children:e.planName||"Unnamed Plan"}),e.description&&(0,s.jsx)("div",{className:"text-sm text-gray-500 truncate mt-1",children:e.description})]}),(0,s.jsx)("div",{className:"col-span-3",children:(0,s.jsx)("span",{className:"inline-flex items-center px-3 py-1 rounded-md text-sm font-medium bg-blue-100 text-blue-800",children:e.planCode||"No Code"})}),(0,s.jsx)("div",{className:"col-span-2",children:(0,s.jsx)("div",{className:"flex flex-wrap gap-1",children:e.coverageSubTypes&&e.coverageSubTypes.length>0?e.coverageSubTypes.map((e,t)=>(0,s.jsx)("span",{className:"inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-blue-50 text-blue-700",children:e},t)):(0,s.jsx)("span",{className:"inline-flex items-center px-3 py-1 rounded-md text-sm font-medium bg-gray-50 text-gray-700",children:e.coverageType})})}),(0,s.jsx)("div",{className:"col-span-1",children:(0,s.jsx)("span",{className:"inline-flex items-center px-2 py-1 rounded-md text-xs font-medium ".concat("Active"===e.status?"bg-green-100 text-green-800":"Draft"===e.status?"bg-yellow-100 text-yellow-800":"bg-gray-100 text-gray-800"),children:e.status||"Active"})}),(0,s.jsx)("div",{className:"col-span-1",children:(0,s.jsx)("span",{className:"text-sm text-gray-600 font-medium",children:e.metalTier||"-"})}),(0,s.jsx)("div",{className:"col-span-2",children:(0,s.jsx)("button",{onClick:()=>eo(e),className:"px-4 py-2 rounded-lg text-sm font-medium transition-colors bg-blue-600 text-white hover:bg-blue-700",children:"Select Plan"})})]},e._id))]})]})}),"configure"===V&&R&&(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:[(0,s.jsx)("h3",{className:"font-semibold text-blue-900 mb-2",children:"Selected Plan"}),(0,s.jsxs)("div",{className:"text-sm text-blue-800",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{children:"Plan Name:"})," ",R.planName]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{children:"Coverage Type:"})," ",R.coverageType]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{children:"Plan Type:"})," ",R.planType]})]})]}),(0,s.jsxs)("div",{className:"bg-white rounded-xl border border-gray-200 p-6",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,s.jsx)("h4",{className:"text-lg font-semibold text-gray-900",children:"Coverage Tiers & Contributions"}),(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[(0,s.jsx)("span",{className:"text-sm font-medium text-gray-700",children:"Contribution Type:"}),(0,s.jsxs)("select",{value:H,onChange:e=>K(e.target.value),className:"px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white text-gray-900",children:[(0,s.jsx)("option",{value:"percentage",children:"Percentage"}),(0,s.jsx)("option",{value:"fixed",children:"Fixed Amount"})]})]})]}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"grid grid-cols-5 gap-4 text-sm font-medium text-gray-700 pb-2 border-b",children:[(0,s.jsx)("div",{children:"Coverage Tier"}),(0,s.jsx)("div",{children:"Premium"}),(0,s.jsx)("div",{children:"percentage"===H?"Employer %":"Employer Pays"}),(0,s.jsx)("div",{children:"percentage"===H?"Employer Pays":"Employee Pays"}),(0,s.jsx)("div",{children:"percentage"===H?"Employee Pays":"Employer %"})]}),Z.map(e=>(0,s.jsxs)("div",{className:"grid grid-cols-5 gap-4 items-center text-sm",children:[(0,s.jsx)("input",{type:"text",value:e.tier,onChange:t=>q(a=>a.map(a=>a.id===e.id?{...a,tier:t.target.value}:a)),className:"px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white text-gray-900"}),(0,s.jsx)("input",{type:"number",value:e.premium,onChange:t=>ei(e.id,"premium",parseFloat(t.target.value)||0),className:"px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white text-gray-900"}),"percentage"===H?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("input",{type:"number",value:e.employerPercent,onChange:t=>ei(e.id,"employerPercent",parseFloat(t.target.value)||0),className:"px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white text-gray-900",min:"0",max:"100"}),(0,s.jsxs)("div",{className:"px-3 py-2 bg-gray-50 rounded-lg text-gray-700 font-medium",children:["$",e.employerPays.toFixed(2)]}),(0,s.jsxs)("div",{className:"px-3 py-2 bg-gray-50 rounded-lg text-gray-700 font-medium",children:["$",e.employeePays.toFixed(2)]})]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("input",{type:"number",value:e.employerPays,onChange:t=>ei(e.id,"employerPays",parseFloat(t.target.value)||0),className:"px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white text-gray-900",min:"0"}),(0,s.jsxs)("div",{className:"px-3 py-2 bg-gray-50 rounded-lg text-gray-700 font-medium",children:["$",e.employeePays.toFixed(2)]}),(0,s.jsxs)("div",{className:"px-3 py-2 bg-gray-50 rounded-lg text-gray-700 font-medium",children:[e.employerPercent.toFixed(1),"%"]})]})]},e.id))]})]}),(0,s.jsx)("div",{className:"flex justify-end",children:(0,s.jsx)("button",{onClick:()=>{G("dates")},className:"bg-gradient-to-r from-blue-600 to-purple-600 text-white px-6 py-2 rounded-lg hover:opacity-90 transition-all",children:"Continue to Dates"})})]}),"dates"===V&&R&&(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:[(0,s.jsx)("h3",{className:"font-semibold text-blue-900 mb-2",children:"Plan Assignment Summary"}),(0,s.jsxs)("div",{className:"text-sm text-blue-800",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{children:"Plan:"})," ",R.planName]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{children:"Coverage Tiers:"})," ",Z.length," configured"]})]})]}),(0,s.jsxs)("div",{className:"grid grid-cols-2 gap-6",children:[(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)("h4",{className:"text-lg font-semibold text-gray-900",children:"Enrollment Period"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Enrollment Start Date"}),(0,s.jsx)("input",{type:"date",value:J,onChange:e=>Q(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white text-gray-900"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Enrollment End Date"}),(0,s.jsx)("input",{type:"date",value:X,onChange:e=>ee(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white text-gray-900"})]})]}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)("h4",{className:"text-lg font-semibold text-gray-900",children:"Coverage Period"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Plan Effective Date"}),(0,s.jsx)("input",{type:"date",value:et,onChange:e=>ea(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white text-gray-900"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Plan End Date"}),(0,s.jsx)("input",{type:"date",value:es,onChange:e=>en(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white text-gray-900"})]})]})]}),(0,s.jsx)("div",{className:"flex justify-end",children:(0,s.jsx)("button",{onClick:ec,disabled:E,className:"px-6 py-2 rounded-lg transition-all ".concat(E?"bg-gray-400 cursor-not-allowed":"bg-gradient-to-r from-blue-600 to-purple-600 hover:opacity-90"," text-white"),children:E?"Creating Assignment...":"Complete Plan Assignment"})})]})]})}),(0,s.jsx)(i,{isOpen:F.isOpen,onClose:M,type:F.type,title:F.title,message:F.message,confirmText:F.confirmText,onConfirm:F.onConfirm})]})},u=a(61103),x=a(99859),p=function(){let e=(0,l.useRouter)(),t=(0,l.useParams)().companyId,[a,o]=(0,n.useState)(null),[p,g]=(0,n.useState)([]),[h,y]=(0,n.useState)(!0),[b,f]=(0,n.useState)(null),[v,j]=(0,n.useState)([]),[N,w]=(0,n.useState)(null),[D,C]=(0,n.useState)(!1),[P,S]=(0,n.useState)("all"),[E,T]=(0,n.useState)("all"),[k,A]=(0,n.useState)(1),[F]=(0,n.useState)(10),[L,I]=(0,n.useState)("active"),[_,M]=(0,n.useState)(!1),[O,z]=(0,n.useState)("percentage"),[R,B]=(0,n.useState)([]),[U,W]=(0,n.useState)(null),[Y,$]=(0,n.useState)(!1),{notification:V,showNotification:G,showSuccess:H,showError:K,showWarning:Z,hideNotification:q}=c(),[J,Q]=(0,n.useState)({}),[X,ee]=(0,n.useState)(0),[et,ea]=(0,n.useState)(0),es=()=>{if(!X||0===Object.keys(J).length)return 0;console.log("Calculating estimated monthly cost..."),console.log("Company employee count:",X),console.log("Plan assignment details:",J);let e=0;Object.values(J).forEach(t=>{if(t&&t.coverageTiers){let a=t.coverageTiers.find(e=>{var t,a;return(null===(t=e.tierName)||void 0===t?void 0:t.toLowerCase().includes("employee only"))||(null===(a=e.tierName)||void 0===a?void 0:a.toLowerCase())==="employee"});a&&(console.log("Found employee-only tier:",a),e+=a.employerCost||0)}}),console.log("Total employee-only employer cost per employee:",e);let t=X*e;return console.log("Total monthly employer cost:",t),Math.round(t)},en=async()=>{let e={};for(let t of p)try{let a=await ed(t.assignmentId);a&&(e[t.assignmentId]=a)}catch(e){console.warn("Failed to fetch details for assignment ".concat(t.assignmentId,":"),e)}Q(e)},el=async e=>{let t=(0,u.bR)(),a=(0,u.n5)();try{var s,n,l,r,o;let i=await fetch("".concat(t,"/employee/company-details"),{headers:{"Content-Type":"application/json","user-id":a}});if(i.ok){let t=await i.json();if(t.company&&t.company.isBrokerage&&t.company._id===e)return console.log("Found broker's own company with employee count:",t.company.companySize),{_id:t.company._id,companyName:t.company.name||"Unknown Company",employeeCount:t.company.companySize||250}}let c=await fetch("".concat(t,"/admin/all-companies"),{headers:{"Content-Type":"application/json","user-id":a}});if(c.ok){let t=await c.json(),a=null===(s=t.companies)||void 0===s?void 0:s.find(t=>t._id===e);if(a)return console.log("Found client company with employee count:",a.companySize),{_id:a._id,companyName:a.name||"Unknown Company",employeeCount:a.companySize||250}}let d=await fetch("".concat(t,"/api/pre-enrollment/companies/").concat(e),{headers:{"Content-Type":"application/json","user-id":a}});if(d.ok){let t=await d.json();return{_id:(null===(n=t.company)||void 0===n?void 0:n._id)||e,companyName:(null===(l=t.company)||void 0===l?void 0:l.companyName)||"Unknown Company",employeeCount:(null===(r=t.company)||void 0===r?void 0:r.companySize)||(null===(o=t.company)||void 0===o?void 0:o.employeeCount)||250}}}catch(e){console.error("Error fetching company details:",e)}return{_id:e,companyName:"Unknown Company",employeeCount:250}},er=(0,n.useCallback)(async()=>{try{y(!0),f(null);let e=await el(t);ee(e.employeeCount||250),console.log("Company employee count set to:",e.employeeCount||250);let a=await (0,d.fH)(t,{includePlanData:!0});if(a.success&&a.data){let t=a.data.assignments;if(console.log("Fetched plan assignments:",t),a.data.accessDeniedToExisting&&a.data.canCreateAssignments){console.log("\uD83D\uDD27 Broker can create new plan assignments for this company"),o(e),g([]);return}let s=await Promise.all(t.map(async e=>{var t;console.log("Processing assignment with enriched data:",e._id,e);let a=e.planData||e.plan,s=e.carrierData,n="string"==typeof e.planId?e.planId:(null===(t=e.planId)||void 0===t?void 0:t._id)||"",l=(null==a?void 0:a.planName)||"Unknown Plan",r=(null==a?void 0:a.planCode)||"N/A",o=(null==s?void 0:s.carrierName)||"Unknown Carrier",i=(null==a?void 0:a.coverageType)||"",c=(null==a?void 0:a.coverageSubTypes)||[],d=(null==a?void 0:a.metalTier)||"";console.log("Using enriched plan data:",{planName:l,planCode:r,carrierName:o,coverageType:i,coverageSubTypes:c,metalTier:d});let m="Medical";if(c&&c.length>0){let e=c[0].toLowerCase();m=e.includes("dental")?"Dental":e.includes("vision")?"Vision":e.includes("medical")||e.includes("health")?"Medical":"Ancillary"}else if(i){let e=i.toLowerCase();m=e.includes("dental")?"Dental":e.includes("vision")?"Vision":e.includes("medical")||e.includes("health")?"Medical":"Ancillary"}let u="Active"===e.status||"Draft"===e.status,x="Active"===e.status||"Draft"===e.status;return{_id:e._id,planName:l,planCode:r,carrier:o,type:m,metalTier:d,period:"".concat(new Date(e.planEffectiveDate).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric"})," - ").concat(new Date(e.planEndDate).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric"})),status:e.status,assignmentId:e._id,planId:n,canEdit:u,canDelete:x,coverageType:i,coverageTiers:e.coverageTiers||[],enrollmentStartDate:e.enrollmentStartDate,enrollmentEndDate:e.enrollmentEndDate,planEffectiveDate:e.planEffectiveDate,planEndDate:e.planEndDate}}));console.log("Final display plans:",s),o(e),g(s)}else f(a.error||"Failed to fetch plan assignments"),g([])}catch(e){console.error("Error fetching data:",e),f("Failed to fetch plan assignments"),g([])}finally{y(!1)}},[t]);(0,n.useEffect)(()=>{er()},[er]),(0,n.useEffect)(()=>{p.length>0&&en()},[p]),(0,n.useEffect)(()=>{Object.keys(J).length>0&&X>0&&ea(es())},[J,X]);let eo=()=>{M(!0)},ei=async e=>{console.log("\uD83D\uDD04 Workflow 2: Creating fresh plan assignment from template:",e);try{let a=new Date().getFullYear(),s=a+1,n={planId:e._id,companyId:t,rateStructure:"Composite",coverageTiers:[{tierName:"Employee Only",totalCost:500,employerCost:400,employeeCost:100},{tierName:"Employee + Spouse",totalCost:1e3,employerCost:800,employeeCost:200},{tierName:"Employee + Child(ren)",totalCost:800,employerCost:640,employeeCost:160},{tierName:"Family",totalCost:1500,employerCost:1200,employeeCost:300}],planEffectiveDate:"".concat(s,"-01-01"),planEndDate:"".concat(s,"-12-31"),enrollmentStartDate:"".concat(a,"-11-01"),enrollmentEndDate:"".concat(a,"-11-30"),groupNumber:"GRP-".concat(t,"-").concat(e._id.slice(-6)),waitingPeriod:{enabled:!1,days:0,rule:"Immediate"},enrollmentType:"Active",employerContribution:{contributionType:"Percentage",contributionAmount:80},employeeContribution:{contributionType:"Percentage",contributionAmount:20},ageBandedRates:[],salaryBasedRates:[],planCustomizations:{},status:"Draft"},l=await (0,d.createPlanAssignment)(n);l.success&&l.data?(console.log("✅ Fresh plan assignment created successfully:",l.data),await er()):(console.error("❌ Failed to create plan assignment:",l.error),K("Assignment Failed","Failed to assign plan to company: "+(l.error||"Unknown error")))}catch(e){console.error("Error creating plan assignment:",e),K("Assignment Error","Failed to assign plan to company. Please try again.")}},ec=async e=>{console.log("\uD83D\uDD04 Workflow 2: Creating fresh plan assignment for new plan:",e);try{let a=new Date().getFullYear(),s=a+1,n={planId:e._id,companyId:t,rateStructure:"Composite",coverageTiers:[{tierName:"Employee Only",totalCost:500,employerCost:400,employeeCost:100},{tierName:"Employee + Spouse",totalCost:1e3,employerCost:800,employeeCost:200},{tierName:"Employee + Child(ren)",totalCost:800,employerCost:640,employeeCost:160},{tierName:"Family",totalCost:1500,employerCost:1200,employeeCost:300}],planEffectiveDate:"".concat(s,"-01-01"),planEndDate:"".concat(s,"-12-31"),enrollmentStartDate:"".concat(a,"-11-01"),enrollmentEndDate:"".concat(a,"-11-30"),groupNumber:"GRP-".concat(t,"-").concat(e._id.slice(-6)),waitingPeriod:{enabled:!1,days:0,rule:"Immediate"},enrollmentType:"Active",employerContribution:{contributionType:"Percentage",contributionAmount:80},employeeContribution:{contributionType:"Percentage",contributionAmount:20},ageBandedRates:[],salaryBasedRates:[],planCustomizations:{},status:"Draft"},l=await (0,d.createPlanAssignment)(n);l.success&&l.data?(await er(),H("Plan Created!","Plan created and assigned to company successfully!")):(console.error("❌ Failed to create plan assignment:",l.error),K("Assignment Failed","Plan created but failed to assign to company: "+(l.error||"Unknown error")))}catch(e){console.error("Error creating plan assignment:",e),K("Assignment Error","Plan created but failed to assign to company. Please try again.")}M(!1)},ed=async e=>{let t=(0,u.bR)(),a=(0,u.n5)();try{console.log("Fetching plan assignment details for ID:",e);let s=await fetch("".concat(t,"/api/pre-enrollment/plan-assignments/").concat(e),{headers:{"Content-Type":"application/json","user-id":a}});if(console.log("Plan assignment fetch response status:",s.status),s.ok){let e=await s.json();console.log("Plan assignment fetch response data:",e),console.log("Assignment object:",e.assignment);let t=e.assignment._doc||e.assignment;return console.log("Processed assignment:",t),console.log("Coverage tiers in assignment:",null==t?void 0:t.coverageTiers),t}{console.error("Failed to fetch plan assignment details. Status:",s.status);let e=await s.text();console.error("Error response:",e)}}catch(e){console.error("Error fetching plan assignment details:",e)}return null},em=async e=>{try{let t=new Date().getFullYear(),a=t+1,s="".concat(a,"-12-31"),n=await (0,d.Uc)(e.assignmentId,{planEffectiveDate:"".concat(a,"-01-01"),planEndDate:s,enrollmentStartDate:"".concat(t,"-11-01"),enrollmentEndDate:"".concat(t,"-11-30"),status:"Draft",assignmentYear:a,assignmentExpiry:s,employerContribution:{contributionType:"Percentage",contributionAmount:80},employeeContribution:{contributionType:"Percentage",contributionAmount:20},eligibleEmployeeClasses:["Full-Time"],enrollmentType:"Active"});n.success?(H("Plan Renewed Successfully","Plan has been renewed for ".concat(a,". The new assignment is in Draft status.")),await er()):K("Renewal Failed",n.error||"Failed to renew plan assignment")}catch(e){console.error("Error renewing plan:",e),K("Renewal Error","An unexpected error occurred while renewing the plan")}},eu=async e=>{if(!e.canEdit){Z("Cannot Edit Plan","This plan cannot be edited due to its current status or active enrollments.");return}console.log("\uD83D\uDD27 Starting edit mode for plan:",e);try{let t=await ed(e.assignmentId);if(!t){K("Error","Failed to load plan assignment details for editing.");return}console.log("\uD83D\uDD27 Full assignment details for editing:",t);let a=[];t.coverageTiers&&t.coverageTiers.length>0&&(a=t.coverageTiers.map((e,t)=>{let a=e.employerContributionPercent||80;return{id:(t+1).toString(),tier:e.tierName||"Tier ".concat(t+1),premium:e.totalCost||0,employerPercent:a,employerPays:e.employerCost||0,employeePays:e.employeeCost||0}}));let s={...e,...t,assignmentId:e.assignmentId,_id:e.assignmentId};console.log("\uD83D\uDD27 Enhanced plan for editing:",s),console.log("\uD83D\uDD27 Formatted tiers for editing:",a),w(s),B(a),M(!0)}catch(e){console.error("Error preparing edit mode:",e),K("Error","Failed to prepare plan assignment for editing.")}},ex=async e=>{try{if(!e.canDelete){Z("Cannot Delete Plan","This plan cannot be deleted due to its current status or active enrollments.");return}G("warning","Confirm Deletion",'Are you sure you want to delete the plan assignment for "'.concat(e.planName,'"? This action cannot be undone.'),"Delete",async()=>{let t=await (0,d.KE)(e.assignmentId);t.success?(H("Plan Deleted","Plan assignment has been successfully deleted."),await er()):K("Deletion Failed",t.error||"Failed to delete plan assignment")})}catch(e){console.error("Error deleting plan:",e),K("Deletion Error","An unexpected error occurred while deleting the plan")}},ep=e=>{console.log("\uD83D\uDD0D Viewing plan assignment:",e),W(e),$(!0)},eg=(e=>{let t=new Date;return e.reduce((e,a)=>{let s=new Date(a.planEffectiveDate||a.enrollmentStartDate||new Date),n=new Date(a.planEndDate||a.enrollmentEndDate||new Date);return s<=t&&t<=n?e.active.push(a):s>t?e.upcoming.push(a):n<t&&e.past.push(a),e},{active:[],upcoming:[],past:[]})})(p),eh=("active"===L?eg.active:"upcoming"===L?eg.upcoming:eg.past).reduce((e,t)=>{var a,s;return e+((null===(s=t.coverageTiers)||void 0===s?void 0:null===(a=s[0])||void 0===a?void 0:a.employeeCost)||0)},0),ey=p.filter(e=>{let t="all"===P||e.status===P,a="all"===E||e.carrier===E,s=new Date,n=new Date(e.planEffectiveDate||e.enrollmentStartDate||new Date),l=new Date(e.planEndDate||e.enrollmentEndDate||new Date),r=!0;return"active"===L?r=n<=s&&s<=l:"upcoming"===L?r=n>s:"past"===L&&(r=l<s),t&&a&&r});ey.length;let eb=(k-1)*F,ef=ey.slice(eb,eb+F);return(n.useEffect(()=>{A(1)},[P,E,L]),ef.reduce((e,t)=>(e[t.type]||(e[t.type]=[]),e[t.type].push(t),e),{}),h)?(0,s.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-black mx-auto"}),(0,s.jsx)("p",{className:"mt-4 text-gray-600",children:"Loading plan assignments..."})]})}):b?(0,s.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:(0,s.jsx)("div",{className:"text-center",children:(0,s.jsxs)("div",{className:"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded-xl",children:[(0,s.jsx)("h3",{className:"font-bold",children:"Error Loading Plans"}),(0,s.jsx)("p",{children:b}),(0,s.jsx)("button",{onClick:er,className:"mt-2 bg-red-600 text-white px-4 py-2 rounded-xl hover:bg-red-700",children:"Try Again"})]})})}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(x.Z,{}),(0,s.jsxs)("div",{className:"min-h-screen bg-white",children:[(0,s.jsx)("div",{className:"bg-white",children:(0,s.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("button",{onClick:()=>e.push("/ai-enroller/manage-groups"),className:"inline-flex items-center gap-2 text-sm font-medium text-gray-600 hover:text-gray-900 transition-colors group",children:[(0,s.jsx)("svg",{className:"w-4 h-4 group-hover:-translate-x-1 transition-transform",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 19l-7-7 7-7"})}),"Back to Groups"]}),(0,s.jsxs)("button",{onClick:eo,className:"inline-flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-blue-600 to-purple-600 text-white text-sm font-medium rounded-lg hover:opacity-90 transition-all shadow-sm hover:shadow-md",children:[(0,s.jsx)(r.r7I,{className:"w-4 h-4"}),"Add Plan"]})]})})}),(0,s.jsx)("div",{className:"bg-white ",children:(0,s.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6",children:[(0,s.jsxs)("div",{className:"flex items-center gap-6 mb-8",children:[(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("div",{className:"w-16 h-16 bg-gradient-to-br from-blue-600 to-purple-600 rounded-xl flex items-center justify-center shadow-lg",children:(0,s.jsx)(r.$xp,{className:"w-8 h-8 text-white"})}),(0,s.jsx)("div",{className:"absolute -bottom-1 -right-1 w-5 h-5 bg-green-500 rounded-full border-2 border-white flex items-center justify-center",children:(0,s.jsx)(r.PjL,{className:"w-3 h-3 text-white"})})]}),(0,s.jsxs)("div",{className:"flex-1",children:[(0,s.jsxs)("div",{className:"flex items-center gap-3 mb-2",children:[(0,s.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:(null==a?void 0:a.companyName)||"Loading..."}),(0,s.jsx)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800",children:"Active"})]}),(0,s.jsxs)("div",{className:"flex items-center gap-6 text-sm text-gray-600",children:[(0,s.jsxs)("div",{className:"flex items-center gap-1",children:[(0,s.jsx)(r.$xp,{className:"w-4 h-4"}),(0,s.jsxs)("span",{children:[(null==a?void 0:a.employeeCount)||0," employees"]})]}),(0,s.jsxs)("div",{className:"flex items-center gap-1",children:[(0,s.jsx)(r.Bge,{className:"w-4 h-4"}),(0,s.jsxs)("span",{children:["Plan Year ",(()=>{let e=new Date().getFullYear();return"".concat(e,"-").concat(e+1)})()]})]}),(0,s.jsxs)("div",{className:"flex items-center gap-1",children:[(0,s.jsxs)("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:[(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"}),(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 11a3 3 0 11-6 0 3 3 0 016 0z"})]}),(0,s.jsx)("span",{children:"San Francisco, CA"})]})]})]})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[(0,s.jsx)("div",{className:"bg-white rounded-xl p-6 border border-green-400 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm font-medium text-green-600",children:"active"===L?"Active Plans":"upcoming"===L?"Upcoming Plans":"Past Plans"}),(0,s.jsx)("p",{className:"text-3xl font-bold mt-2 text-green-900",children:"active"===L?eg.active.length:"upcoming"===L?eg.upcoming.length:eg.past.length}),(0,s.jsx)("p",{className:"text-xs mt-1 text-green-600",children:"active"===L?"Currently active":"upcoming"===L?"Starting soon":"Previously active"})]}),(0,s.jsx)("div",{className:"w-12 h-12 bg-gradient-to-br from-green-50 to-green-100 rounded-xl flex items-center justify-center shadow-lg",children:(0,s.jsx)(r.GwR,{className:"w-6 h-6 text-green-600"})})]})}),(0,s.jsx)("div",{className:"bg-white rounded-xl p-6 border border-blue-400 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm text-blue-600 font-medium",children:"Total Premium"}),(0,s.jsxs)("p",{className:"text-3xl font-bold text-blue-900 mt-2",children:["$",eh.toLocaleString()]}),(0,s.jsx)("p",{className:"text-xs text-blue-600 mt-1",children:"active"===L?"Current monthly cost":"upcoming"===L?"Future monthly cost":"Previous monthly cost"})]}),(0,s.jsx)("div",{className:"w-12 h-12 bg-gradient-to-br from-blue-50 to-blue-100 rounded-xl flex items-center justify-center shadow-lg",children:(0,s.jsx)(r.Tue,{className:"w-6 h-6 text-blue-600"})})]})}),(0,s.jsx)("div",{className:"bg-white rounded-xl p-6 border border-purple-400 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm text-purple-600 font-medium",children:"Employees"}),(0,s.jsx)("p",{className:"text-3xl font-bold text-purple-900 mt-2",children:(null==a?void 0:a.employeeCount)||0}),(0,s.jsx)("p",{className:"text-xs text-purple-600 mt-1",children:"Total covered"})]}),(0,s.jsx)("div",{className:"w-12 h-12 bg-gradient-to-br from-purple-50 to-purple-100 rounded-xl flex items-center justify-center shadow-lg",children:(0,s.jsx)(r.$xp,{className:"w-6 h-6 text-purple-600"})})]})}),(0,s.jsx)("div",{className:"bg-white rounded-xl p-6 border border-orange-400 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm text-orange-600 font-medium",children:"Per Employee"}),(0,s.jsxs)("p",{className:"text-3xl font-bold text-orange-900 mt-2",children:["$",(null==a?void 0:a.employeeCount)?Math.round(eh/a.employeeCount):0]}),(0,s.jsx)("p",{className:"text-xs text-orange-600 mt-1",children:"active"===L?"Current avg cost":"upcoming"===L?"Future avg cost":"Previous avg cost"})]}),(0,s.jsx)("div",{className:"w-12 h-12 bg-gradient-to-br from-orange-50 to-orange-100 rounded-xl flex items-center justify-center shadow-lg",children:(0,s.jsx)(r.Tue,{className:"w-6 h-6 text-orange-600"})})]})})]})]})}),(0,s.jsx)("div",{className:"bg-white ",children:(0,s.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6",children:(0,s.jsxs)("div",{className:"flex flex-col lg:flex-row lg:items-center gap-4",children:[(0,s.jsxs)("div",{className:"relative flex-1",children:[(0,s.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,s.jsx)("svg",{className:"h-5 w-5 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})})}),(0,s.jsx)("input",{type:"text",placeholder:"Search plans by name, carrier, or type...",className:"block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-lg text-sm text-gray-900 placeholder-gray-500 bg-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"})]}),(0,s.jsxs)("div",{className:"flex items-center gap-3 flex-shrink-0",children:[(0,s.jsxs)("select",{className:"px-3 py-3 border border-gray-300 rounded-lg text-sm text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white",children:[(0,s.jsx)("option",{value:"all",children:"All Status"}),(0,s.jsx)("option",{value:"active",children:"Active"}),(0,s.jsx)("option",{value:"draft",children:"Draft"}),(0,s.jsx)("option",{value:"inactive",children:"Inactive"})]}),(0,s.jsxs)("select",{className:"px-3 py-3 border border-gray-300 rounded-lg text-sm text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white",children:[(0,s.jsx)("option",{value:"all",children:"All Types"}),(0,s.jsx)("option",{value:"medical",children:"Medical"}),(0,s.jsx)("option",{value:"dental",children:"Dental"}),(0,s.jsx)("option",{value:"vision",children:"Vision"})]}),(0,s.jsxs)("button",{className:"inline-flex items-center gap-2 px-3 py-3 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors",children:[(0,s.jsx)("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.707A1 1 0 013 7V4z"})}),"Filter"]})]})]})})}),(0,s.jsx)("div",{className:"bg-white",children:(0,s.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-0",children:(0,s.jsx)("div",{className:"flex items-center justify-center",children:(0,s.jsxs)("div",{className:"relative bg-gray-100 rounded-full p-1 flex items-center",style:{width:"280px"},children:[(0,s.jsx)("div",{className:"absolute top-1 bottom-1 rounded-full transition-all duration-500 ease-out shadow-lg",style:{width:"90px",left:"active"===L?"4px":"upcoming"===L?"95px":"186px",backgroundColor:"active"===L?"#10b981":"upcoming"===L?"#3b82f6":"#6b7280"}}),(0,s.jsx)("button",{onClick:()=>I("active"),className:"relative z-10 flex-1 py-3 text-sm font-medium rounded-full transition-all duration-300 ease-in-out ".concat("active"===L?"text-white":"text-gray-600 hover:text-green-600"),children:"Active"}),(0,s.jsx)("button",{onClick:()=>I("upcoming"),className:"relative z-10 flex-1 py-3 text-sm font-medium rounded-full transition-all duration-300 ease-in-out ".concat("upcoming"===L?"text-white":"text-gray-600 hover:text-blue-600"),children:"Upcoming"}),(0,s.jsx)("button",{onClick:()=>I("past"),className:"relative z-10 flex-1 py-3 text-sm font-medium rounded-full transition-all duration-300 ease-in-out ".concat("past"===L?"text-white":"text-gray-600 hover:text-gray-700"),children:"Past"})]})})})}),(0,s.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:(0,s.jsxs)("div",{className:"bg-white rounded-2xl shadow-xl border border-gray-200 overflow-hidden",children:[(0,s.jsx)("div",{className:"px-6 py-4 bg-gradient-to-r from-gray-50 to-gray-100 border-b border-gray-200",children:(0,s.jsxs)("div",{className:"grid grid-cols-12 gap-4 text-sm font-semibold text-gray-700 uppercase tracking-wide",children:[(0,s.jsx)("div",{className:"col-span-3 text-left",children:"Plan Details"}),(0,s.jsx)("div",{className:"col-span-2 text-center",children:"Coverage Type"}),(0,s.jsx)("div",{className:"col-span-1 text-center",children:"Status"}),(0,s.jsx)("div",{className:"col-span-2 text-center",children:"Enrollment Period"}),(0,s.jsx)("div",{className:"col-span-2 text-center",children:"Employee Cost"}),(0,s.jsx)("div",{className:"col-span-2 text-center",children:"Actions"})]})}),(0,s.jsx)("div",{className:"divide-y divide-gray-200",children:0===ey.length?(0,s.jsxs)("div",{className:"px-6 py-16 text-center",children:[(0,s.jsx)("div",{className:"w-20 h-20 mx-auto bg-gray-100 rounded-full flex items-center justify-center mb-6",children:(0,s.jsx)(r.GwR,{className:"h-10 w-10 text-gray-400"})}),(0,s.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:["active"===L&&"No active plans","upcoming"===L&&"No upcoming plans","past"===L&&"No past plans"]}),(0,s.jsxs)("p",{className:"text-gray-500 mb-8 max-w-sm mx-auto",children:["active"===L&&"There are no plans currently active for this company. Add a new plan to get started.","upcoming"===L&&"There are no plans scheduled to start in the future for this company.","past"===L&&"There are no expired or past plans for this company."]}),(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsxs)("button",{onClick:eo,className:"inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-lg text-white bg-gradient-to-r from-blue-600 to-purple-600 hover:opacity-90 transition-all shadow-lg hover:shadow-xl",children:[(0,s.jsx)(r.r7I,{className:"-ml-1 mr-2 h-5 w-5"}),"Add First Plan"]}),(0,s.jsx)("div",{className:"text-sm text-gray-400",children:"or browse available plans to assign"})]})]}):ey.map(e=>{var t,a,n,l;let o=(null===(a=e.coverageTiers)||void 0===a?void 0:null===(t=a[0])||void 0===t?void 0:t.employeeCost)||0,i=e.enrollmentStartDate?new Date(e.enrollmentStartDate).toLocaleDateString():"N/A",c=e.enrollmentEndDate?new Date(e.enrollmentEndDate).toLocaleDateString():"N/A";return(0,s.jsx)("div",{className:"px-6 py-5 hover:bg-gray-50 transition-all duration-200",children:(0,s.jsxs)("div",{className:"grid grid-cols-12 gap-4 items-center",children:[(0,s.jsx)("div",{className:"col-span-3",children:(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,s.jsx)("div",{className:"flex-shrink-0",children:(e=>{switch(null==e?void 0:e.toLowerCase()){case"medical":case"health":return(0,s.jsx)(r.GwR,{className:"w-5 h-5 text-blue-600"});case"dental":return(0,s.jsx)("svg",{className:"w-5 h-5 text-green-600",fill:"currentColor",viewBox:"0 0 20 20",children:(0,s.jsx)("path",{d:"M10 2C6.686 2 4 4.686 4 8c0 1.5.5 3 1.5 4.5L10 18l4.5-5.5C15.5 11 16 9.5 16 8c0-3.314-2.686-6-6-6z"})});case"vision":return(0,s.jsxs)("svg",{className:"w-5 h-5 text-purple-600",fill:"currentColor",viewBox:"0 0 20 20",children:[(0,s.jsx)("path",{d:"M10 12a2 2 0 100-4 2 2 0 000 4z"}),(0,s.jsx)("path",{fillRule:"evenodd",d:"M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z",clipRule:"evenodd"})]});default:return(0,s.jsx)(r.$xp,{className:"w-5 h-5 text-gray-600"})}})(e.coverageType)}),(0,s.jsxs)("div",{className:"min-w-0 flex-1",children:[(0,s.jsx)("div",{className:"font-semibold text-gray-700 truncate",children:e.planName}),(0,s.jsxs)("div",{className:"text-sm text-gray-500 truncate",children:[e.carrier," • ",e.planCode]})]})]})}),(0,s.jsx)("div",{className:"col-span-2 text-center",children:(0,s.jsxs)("div",{className:"flex flex-col gap-1 items-center",children:[(0,s.jsx)("span",{className:"text-sm font-medium text-gray-700",children:e.coverageType}),e.metalTier&&(0,s.jsx)("span",{className:"text-xs font-medium text-gray-600",children:e.metalTier})]})}),(0,s.jsx)("div",{className:"col-span-1 text-center",children:(0,s.jsx)("div",{className:"flex justify-center",children:(0,s.jsxs)("span",{className:"inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold ".concat((e=>{switch(null==e?void 0:e.toLowerCase()){case"active":return"bg-green-100 text-green-800";case"draft":return"bg-yellow-100 text-yellow-800";case"inactive":return"bg-red-100 text-red-800";default:return"bg-gray-100 text-gray-800"}})(e.status)),children:[(0,s.jsx)("div",{className:"w-2 h-2 rounded-full mr-2 ".concat((null===(n=e.status)||void 0===n?void 0:n.toLowerCase())==="active"?"bg-green-500":(null===(l=e.status)||void 0===l?void 0:l.toLowerCase())==="draft"?"bg-yellow-500":"bg-red-500")}),e.status]})})}),(0,s.jsxs)("div",{className:"col-span-2 text-center",children:[(0,s.jsx)("div",{className:"text-sm font-medium text-gray-700",children:i}),(0,s.jsxs)("div",{className:"text-xs text-gray-500",children:["to ",c]})]}),(0,s.jsxs)("div",{className:"col-span-2 text-center",children:[(0,s.jsxs)("div",{className:"text-lg font-bold text-gray-700",children:["$",o]}),(0,s.jsx)("div",{className:"text-xs text-gray-500",children:"per month"})]}),(0,s.jsx)("div",{className:"col-span-2",children:(0,s.jsxs)("div",{className:"flex items-center justify-center space-x-2",children:[(0,s.jsx)("button",{onClick:()=>em(e),className:"px-3 py-1 text-xs font-medium text-white bg-red-500 hover:bg-red-600 rounded transition-all duration-200",title:"Renew Plan for Next Year",children:"Renew"}),(0,s.jsx)("button",{onClick:()=>ep(e),className:"p-2 text-blue-600 hover:text-blue-800 hover:bg-blue-100 rounded-lg transition-all duration-200",title:"View Plan Assignment Details",children:(0,s.jsx)(r.Vvo,{className:"w-4 h-4"})}),e.canEdit&&(0,s.jsx)("button",{onClick:()=>eu(e),className:"p-2 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-lg transition-all duration-200",title:"Edit Plan Assignment",children:(0,s.jsx)(r._vs,{className:"w-4 h-4"})}),(0,s.jsx)("button",{onClick:()=>ex(e),className:"p-2 text-red-600 hover:text-red-800 hover:bg-red-100 rounded-lg transition-all duration-200",title:"Delete Plan Assignment",children:(0,s.jsx)(r.Bhs,{className:"w-4 h-4"})})]})})]})},e._id)})})]})}),_&&(0,s.jsx)(m,{isOpen:_,onClose:()=>{M(!1),w(null),B([])},onSelectPlan:ei,onCreatePlan:ec,companyId:t,editMode:!!N,editingPlan:N,editingCoverageTiers:R}),Y&&U&&(0,s.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,s.jsxs)("div",{className:"bg-white rounded-lg p-6 max-w-2xl w-full mx-4 max-h-[80vh] overflow-y-auto",children:[(0,s.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,s.jsx)("h2",{className:"text-xl font-bold text-gray-900",children:"Plan Assignment Details"}),(0,s.jsx)("button",{onClick:()=>$(!1),className:"text-gray-400 hover:text-gray-600",children:(0,s.jsx)("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]}),(0,s.jsx)("div",{className:"space-y-4",children:(0,s.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Plan Name"}),(0,s.jsx)("p",{className:"text-sm text-gray-900",children:U.planName})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Plan Code"}),(0,s.jsx)("p",{className:"text-sm text-gray-900",children:U.planCode})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Carrier"}),(0,s.jsx)("p",{className:"text-sm text-gray-900",children:U.carrier})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Status"}),(0,s.jsx)("p",{className:"text-sm text-gray-900",children:U.status})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Effective Date"}),(0,s.jsx)("p",{className:"text-sm text-gray-900",children:U.planEffectiveDate?new Date(U.planEffectiveDate).toLocaleDateString():"N/A"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"End Date"}),(0,s.jsx)("p",{className:"text-sm text-gray-900",children:U.planEndDate?new Date(U.planEndDate).toLocaleDateString():"N/A"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Coverage Type"}),(0,s.jsx)("p",{className:"text-sm text-gray-900",children:U.coverageType||U.type})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Assignment ID"}),(0,s.jsx)("p",{className:"text-sm text-gray-900 font-mono",children:U._id})]})]})}),(0,s.jsx)("div",{className:"mt-6 flex justify-end",children:(0,s.jsx)("button",{onClick:()=>$(!1),className:"px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600 transition-colors",children:"Close"})})]})}),(0,s.jsx)(i,{isOpen:V.isOpen,onClose:q,type:V.type,title:V.title,message:V.message,confirmText:V.confirmText,onConfirm:V.onConfirm})]})]})}},83337:function(e,t,a){"use strict";a.d(t,{C:function(){return l},T:function(){return n}});var s=a(68575);let n=()=>(0,s.I0)(),l=s.v9},2778:function(){}},function(e){e.O(0,[2461,1424,139,8422,522,3463,3301,8575,8685,187,1423,9932,3919,208,9859,8884,7804,2971,2117,1744],function(){return e(e.s=22248)}),_N_E=e.O()}]);