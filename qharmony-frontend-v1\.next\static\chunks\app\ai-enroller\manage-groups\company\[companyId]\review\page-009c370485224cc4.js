(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6574],{65e3:function(e,t,s){Promise.resolve().then(s.bind(s,56399))},99376:function(e,t,s){"use strict";var a=s(35475);s.o(a,"useParams")&&s.d(t,{useParams:function(){return a.useParams}}),s.o(a,"usePathname")&&s.d(t,{usePathname:function(){return a.usePathname}}),s.o(a,"useRouter")&&s.d(t,{useRouter:function(){return a.useRouter}}),s.o(a,"useSearchParams")&&s.d(t,{useSearchParams:function(){return a.useSearchParams}})},56399:function(e,t,s){"use strict";s.r(t),s.d(t,{default:function(){return c}});var a=s(57437),n=s(2265),l=s(99376),r=s(18913),o=s(88884),i=s(61103);function c(){var e;let t=(0,l.useRouter)(),s=(0,l.useParams)(),c=(0,l.useSearchParams)(),d=s.companyId,m=(null===(e=c.get("assignments"))||void 0===e?void 0:e.split(","))||[],[p,u]=(0,n.useState)(null),[x,h]=(0,n.useState)([]),[g,y]=(0,n.useState)(!0),f=async e=>{try{let i=await fetch("".concat("http://localhost:8080","/api/pre-enrollment/plans/").concat(e),{headers:{"Content-Type":"application/json","user-id":(()=>{let e=localStorage.getItem("userid1")||localStorage.getItem("userId");if(!e)throw Error("User ID not found. Please authenticate first.");return e})()}});if(i.ok){var t,s,a,n,l,r,o;let e=await i.json();return{planName:(null===(t=e.plan)||void 0===t?void 0:t.planName)||"Unknown Plan",planCode:(null===(s=e.plan)||void 0===s?void 0:s.planCode)||"N/A",planType:(null===(a=e.plan)||void 0===a?void 0:a.planType)||"N/A",coverageType:(null===(n=e.plan)||void 0===n?void 0:n.coverageType)||"Unknown",coverageSubTypes:(null===(l=e.plan)||void 0===l?void 0:l.coverageSubTypes)||[],metalTier:(null===(r=e.plan)||void 0===r?void 0:r.metalTier)||"",carrierName:(null===(o=e.carrier)||void 0===o?void 0:o.carrierName)||"Unknown Carrier"}}}catch(t){console.error("Error fetching plan details for planId:",e,t)}return{planName:"Unknown Plan",planCode:"N/A",planType:"N/A",coverageType:"Unknown",coverageSubTypes:[],metalTier:"",carrierName:"Unknown Carrier"}},b=async e=>{try{console.log("Fetching plan assignment details for ID:",e);let t=await fetch("".concat("http://localhost:8080","/api/pre-enrollment/plan-assignments/").concat(e),{headers:{"Content-Type":"application/json","user-id":localStorage.getItem("userid1")||localStorage.getItem("userId")||"6838677aef6db0212bcfdacd"}});if(t.ok){let e=await t.json();return e.assignment._doc||e.assignment}console.error("Failed to fetch plan assignment details. Status:",t.status)}catch(e){console.error("Error fetching plan assignment details:",e)}return null},v=async e=>{let t=(0,i.bR)(),s=(0,i.n5)();try{let n=await fetch("".concat(t,"/employee/company-details"),{headers:{"Content-Type":"application/json","user-id":s}});if(n.ok){let t=await n.json();if(t.company&&t.company.isBrokerage&&t.company._id===e)return console.log("Found broker's own company with employee count:",t.company.companySize),{_id:t.company._id,companyName:t.company.name||"Unknown Company",employeeCount:t.company.companySize||250}}let l=await fetch("".concat(t,"/admin/all-companies"),{headers:{"Content-Type":"application/json","user-id":s}});if(l.ok){var a;let t=await l.json(),s=null===(a=t.companies)||void 0===a?void 0:a.find(t=>t._id===e);if(s)return console.log("Found client company with employee count:",s.companySize),{_id:s._id,companyName:s.name||"Unknown Company",employeeCount:s.companySize||250}}}catch(e){console.error("Error fetching company details:",e)}return{_id:e,companyName:"Unknown Company",employeeCount:250}},j=(0,n.useCallback)(async()=>{try{y(!0);let e=await v(d);u(e);let t=await (0,o.fH)(d,{includePlanData:!0});if(t.success&&t.data){let e=t.data.assignments;console.log("All assignments:",e.length),console.log("Selected assignment IDs from URL:",m);let s=e;m.length>0&&(s=e.filter(e=>m.includes(e._id))),console.log("Filtered assignments for review:",s.length);let a=await Promise.all(s.map(async e=>{var t;let s="string"==typeof e.planId?e.planId:(null===(t=e.planId)||void 0===t?void 0:t._id)||"";console.log("Processing assignment:",e._id,"with planId:",s);let a=null;s&&(a=await f(s),console.log("Fetched plan details:",a));let n=await b(e._id);console.log("Fetched assignment details:",n);let l=(null==n?void 0:n.coverageTiers)||e.coverageTiers||[],r=l.find(e=>{var t,s;return(null===(t=e.tierName)||void 0===t?void 0:t.toLowerCase().includes("employee only"))||(null===(s=e.tierName)||void 0===s?void 0:s.toLowerCase())==="employee"})||l[0]||{},o=r.totalCost||0,i=r.employerCost||0,c=r.employeeCost||o-i,d="Medical";if((null==a?void 0:a.coverageSubTypes)&&a.coverageSubTypes.length>0){let e=a.coverageSubTypes[0].toLowerCase();e.includes("dental")?d="Dental":e.includes("vision")&&(d="Vision")}else if(null==a?void 0:a.coverageType){let e=a.coverageType.toLowerCase();e.includes("dental")?d="Dental":e.includes("vision")&&(d="Vision")}return{_id:e._id,planName:(null==a?void 0:a.planName)||"Unknown Plan",carrier:(null==a?void 0:a.carrierName)||"Unknown Carrier",type:d,planCode:(null==a?void 0:a.planCode)||"N/A",totalMonthlyPremium:o,employerContribution:i,employeeContribution:c,isNew:"Draft"===e.status,isUpdated:"Active"===e.status,changes:"Draft"===e.status?["New plan added"]:["Plan configuration updated"]}}));console.log("Enhanced plans for review:",a),h(a)}else console.error("Failed to fetch plan assignments:",t.error),h([])}catch(e){console.error("Error fetching data:",e),u({_id:d,companyName:"TechCorp Inc.",employeeCount:250}),h([])}finally{y(!1)}},[d]);(0,n.useEffect)(()=>{j()},[j]);let N=(null==p?void 0:p.employeeCount)||250,w=x.reduce((e,t)=>e+(t.employerContribution||0),0);x.reduce((e,t)=>e+(t.employeeContribution||0),0),x.reduce((e,t)=>e+(t.totalMonthlyPremium||0),0);let S=w*N;return(console.log("Cost Calculation Debug:"),console.log("Employee Count:",N),console.log("Sum of all plans employer cost (per employee):",w),console.log("Total company monthly employer cost:",S),console.log("Average cost per employee:",w),g)?(0,a.jsx)("div",{className:"min-h-screen bg-white flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-black mx-auto"}),(0,a.jsx)("p",{className:"mt-4 text-gray-600",children:"Loading review..."})]})}):(0,a.jsxs)("div",{className:"min-h-screen bg-white",children:[(0,a.jsx)("div",{className:"bg-white border-b border-gray-200 px-6 py-4",children:(0,a.jsxs)("div",{className:"max-w-7xl mx-auto",children:[(0,a.jsxs)("div",{className:"flex gap-3 overflow-x-auto",children:[(0,a.jsxs)("button",{onClick:()=>t.push("/ai-enroller"),className:"flex items-center gap-2 px-4 py-2 rounded-full text-sm font-medium transition-all bg-purple-100 text-purple-700 hover:bg-purple-200 whitespace-nowrap",style:{fontFamily:"'SF Pro', 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif",fontSize:"13px",fontWeight:"450",lineHeight:"1.2"},children:[(0,a.jsx)(r.VRM,{className:"w-4 h-4"}),"Home",(0,a.jsx)("span",{className:"flex items-center justify-center w-4 h-4 bg-purple-600 text-white rounded-full ml-2",children:(0,a.jsx)(r.PjL,{className:"w-5 h-5"})})]}),(0,a.jsxs)("button",{onClick:()=>t.push("/ai-enroller/manage-groups/select-company"),className:"flex items-center gap-2 px-4 py-2 rounded-full text-sm font-medium transition-all bg-purple-100 text-purple-700 hover:bg-purple-200 whitespace-nowrap",style:{fontFamily:"'SF Pro', 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif",fontSize:"13px",fontWeight:"450",lineHeight:"1.2"},children:[(0,a.jsx)(r.$xp,{className:"w-4 h-4"}),"Select Company",(0,a.jsx)("span",{className:"flex items-center justify-center w-4 h-4 bg-purple-600 text-white rounded-full ml-2",children:(0,a.jsx)(r.PjL,{className:"w-5 h-5"})})]}),(0,a.jsxs)("button",{onClick:()=>t.push("/ai-enroller/manage-groups/company/".concat(d,"/plans")),className:"flex items-center gap-2 px-4 py-2 rounded-full text-sm font-medium transition-all bg-purple-100 text-purple-700 hover:bg-purple-200 whitespace-nowrap",style:{fontFamily:"'SF Pro', 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif",fontSize:"13px",fontWeight:"450",lineHeight:"1.2"},children:[(0,a.jsx)(r.GwR,{className:"w-4 h-4"}),"View Plans",(0,a.jsx)("span",{className:"flex items-center justify-center w-4 h-4 bg-purple-600 text-white rounded-full ml-2",children:(0,a.jsx)(r.PjL,{className:"w-5 h-5"})})]}),(0,a.jsxs)("button",{onClick:()=>t.push("/ai-enroller/manage-groups/company/".concat(d,"/set-dates")),className:"flex items-center gap-2 px-4 py-2 rounded-full text-sm font-medium transition-all bg-purple-100 text-purple-700 hover:bg-purple-200 whitespace-nowrap",style:{fontFamily:"'SF Pro', 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif",fontSize:"13px",fontWeight:"450",lineHeight:"1.2"},children:[(0,a.jsx)(r.Bge,{className:"w-4 h-4"}),"Set Dates",(0,a.jsx)("span",{className:"flex items-center justify-center w-4 h-4 bg-purple-600 text-white rounded-full ml-2",children:(0,a.jsx)(r.PjL,{className:"w-5 h-5"})})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2 px-4 py-2 rounded-full text-sm font-medium bg-purple-50 text-purple-600 whitespace-nowrap",style:{fontFamily:"'SF Pro', 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif",fontSize:"13px",fontWeight:"450",lineHeight:"1.2"},children:[(0,a.jsx)(r.PjL,{className:"w-4 h-4"}),"Review"]}),(0,a.jsxs)("div",{className:"flex items-center gap-2 px-4 py-2 rounded-full text-sm font-medium bg-gray-100 text-gray-600 whitespace-nowrap",style:{fontFamily:"'SF Pro', 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif",fontSize:"13px",fontWeight:"450",lineHeight:"1.2"},children:[(0,a.jsx)(r.PjL,{className:"w-4 h-4"}),"Confirmation"]})]}),(0,a.jsx)("div",{className:"mt-4",children:(0,a.jsxs)("div",{className:"text-right text-sm text-gray-500",children:["Step 5 of 6",(0,a.jsx)("br",{}),(0,a.jsx)("span",{className:"text-xs text-purple-600 font-medium",children:"Final review"})]})})]})}),(0,a.jsxs)("div",{className:"max-w-6xl mx-auto px-6 py-8 bg-white",children:[(0,a.jsxs)("div",{className:"mb-8",children:[(0,a.jsx)("h1",{className:"text-2xl font-bold text-gray-900 mb-2",children:"Final Review & Confirmation"}),(0,a.jsxs)("p",{className:"text-gray-600",children:["Review all changes before saving for ",null==p?void 0:p.companyName]})]}),(0,a.jsx)("div",{className:"bg-green-50 border border-green-200 rounded-xl p-4 mb-8",children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(r.PjL,{className:"text-green-600 w-5 h-5"}),(0,a.jsx)("span",{className:"text-green-800 font-medium",children:"Ready to save!"}),(0,a.jsx)("span",{className:"text-green-700",children:"Review the changes below and confirm when ready."})]})}),(0,a.jsxs)("div",{className:"bg-white border border-gray-200 rounded-xl p-6 mb-8",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3 mb-6",children:[(0,a.jsx)(r.PjL,{className:"text-gray-600 w-6 h-6"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"text-xl font-semibold text-gray-900",children:"Plan Summary"}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Side-by-side comparison of new vs. existing plans"})]})]}),(0,a.jsx)("div",{className:"space-y-6",children:x.map(e=>{var t,s,n;return(0,a.jsxs)("div",{className:"border-l-4 border-blue-500 pl-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:e.planName}),(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:[e.carrier," • ",e.type]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[e.isUpdated&&(0,a.jsx)("span",{className:"bg-blue-100 text-blue-800 text-xs px-2.5 py-1 rounded-xl border border-blue-200 font-medium",children:"Updated"}),e.isNew&&(0,a.jsx)("span",{className:"bg-green-100 text-green-800 text-xs px-2.5 py-1 rounded-md border border-green-200 font-medium",children:"New"}),(0,a.jsx)("button",{className:"text-blue-600 hover:text-blue-700 p-1",children:(0,a.jsx)(r._vs,{className:"w-4 h-4"})})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-3 gap-4 mb-4",children:[(0,a.jsxs)("div",{className:"text-center p-3 bg-gray-50 rounded-xl",children:[(0,a.jsx)("p",{className:"text-xs text-gray-500 mb-1",children:"Total Monthly Premium"}),(0,a.jsxs)("p",{className:"text-lg font-semibold text-gray-900",children:["$",null===(t=e.totalMonthlyPremium)||void 0===t?void 0:t.toFixed(2)]})]}),(0,a.jsxs)("div",{className:"text-center p-3 bg-green-50 rounded-xl",children:[(0,a.jsx)("p",{className:"text-xs text-gray-500 mb-1",children:"Employer Contribution"}),(0,a.jsxs)("p",{className:"text-lg font-semibold text-green-600",children:["$",null===(s=e.employerContribution)||void 0===s?void 0:s.toFixed(2)]})]}),(0,a.jsxs)("div",{className:"text-center p-3 bg-blue-50 rounded-xl",children:[(0,a.jsx)("p",{className:"text-xs text-gray-500 mb-1",children:"Employee Contribution"}),(0,a.jsxs)("p",{className:"text-lg font-semibold text-blue-600",children:["$",null===(n=e.employeeContribution)||void 0===n?void 0:n.toFixed(2)]})]})]}),e.changes&&e.changes.length>0&&(0,a.jsxs)("div",{className:"bg-blue-50 rounded-xl p-3",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-blue-900 mb-2",children:"Changes Made:"}),(0,a.jsx)("ul",{className:"space-y-1",children:e.changes.map((e,t)=>(0,a.jsxs)("li",{className:"text-sm text-blue-800 flex items-center gap-2",children:[(0,a.jsx)("span",{className:"w-1 h-1 bg-blue-600 rounded-full"}),e]},t))})]})]},e._id)})})]}),(0,a.jsxs)("div",{className:"bg-white border border-gray-200 rounded-xl p-6 mb-8",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3 mb-6",children:[(0,a.jsx)(r.Otr,{className:"text-gray-600 w-6 h-6"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"text-xl font-semibold text-gray-900",children:"Company Impact Summary"}),(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:["Total cost impact for ",null==p?void 0:p.companyName]})]})]}),(0,a.jsxs)("div",{className:"bg-gray-50 rounded-xl p-4 mb-6 text-xs",children:[(0,a.jsx)("h4",{className:"font-medium text-gray-700 mb-2",children:"Calculation Breakdown:"}),(0,a.jsxs)("div",{className:"space-y-1 text-gray-600",children:[(0,a.jsxs)("p",{children:["• Company has ",N," employees"]}),(0,a.jsxs)("p",{children:["• Plans selected: ",x.length]}),x.map((e,t)=>{var s;return(0,a.jsxs)("p",{children:["• ",e.planName,": $",(null===(s=e.employerContribution)||void 0===s?void 0:s.toFixed(2))||"0.00"," employer cost per employee"]},t)}),(0,a.jsxs)("p",{className:"font-medium pt-2",children:["• Total per employee: $",w.toFixed(2)]}),(0,a.jsxs)("p",{className:"font-medium",children:["• Total for company: $",w.toFixed(2)," \xd7 ",N," = $",S.toFixed(2)]})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-3 gap-6",children:[(0,a.jsxs)("div",{className:"border-l-4 border-green-500 pl-4",children:[(0,a.jsx)("p",{className:"text-sm text-gray-600 mb-1",children:"Total Monthly Employer Cost"}),(0,a.jsxs)("p",{className:"text-2xl font-bold text-green-600",children:["$",S.toFixed(2)]}),(0,a.jsxs)("p",{className:"text-xs text-green-600",children:["$",w.toFixed(2)," per employee \xd7 ",N," employees"]})]}),(0,a.jsxs)("div",{className:"border-l-4 border-blue-500 pl-4",children:[(0,a.jsx)("p",{className:"text-sm text-gray-600 mb-1",children:"Total Annual Employer Cost"}),(0,a.jsxs)("p",{className:"text-2xl font-bold text-blue-600",children:["$",(12*S).toFixed(2)]}),(0,a.jsx)("p",{className:"text-xs text-blue-600",children:"Projected for full year"})]}),(0,a.jsxs)("div",{className:"border-l-4 border-orange-500 pl-4",children:[(0,a.jsx)("p",{className:"text-sm text-gray-600 mb-1",children:"Average Cost per Employee"}),(0,a.jsxs)("p",{className:"text-2xl font-bold text-orange-600",children:["$",w.toFixed(2)]}),(0,a.jsx)("p",{className:"text-xs text-orange-600",children:"Sum of all plans for Employee Only tier"})]})]})]}),(0,a.jsx)("div",{className:"bg-green-50 border border-green-200 rounded-xl p-4 mb-8",children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(r.PjL,{className:"text-green-600 w-5 h-5"}),(0,a.jsx)("span",{className:"text-green-800 font-medium",children:"Validation Check:"}),(0,a.jsx)("span",{className:"text-green-700",children:"All contribution amounts are within acceptable ranges. No issues detected."})]})}),(0,a.jsxs)("div",{className:"flex gap-4 mb-8",children:[(0,a.jsxs)("button",{onClick:()=>{t.push("/ai-enroller/manage-groups/company/".concat(d,"/plans"))},className:"px-6 py-2 border border-gray-300 text-gray-700 rounded-xl hover:bg-gray-50 transition-colors flex items-center gap-2",children:[(0,a.jsx)(r.Vvo,{className:"w-4 h-4"}),"View Plans Page"]}),(0,a.jsxs)("button",{onClick:()=>{alert("Summary downloaded successfully!")},className:"px-6 py-2 border border-gray-300 text-gray-700 rounded-xl hover:bg-gray-50 transition-colors flex items-center gap-2",children:[(0,a.jsx)(r.yFZ,{className:"w-4 h-4"}),"Download Summary"]}),(0,a.jsxs)("button",{onClick:()=>{let e=x.map(e=>e._id);t.push("/ai-enroller/manage-groups/company/".concat(d,"/confirmation?assignments=").concat(e.join(",")))},className:"flex-1 px-6 py-2 bg-black text-white rounded-xl hover:bg-gray-800 transition-colors flex items-center justify-center gap-2",children:[(0,a.jsx)(r.PjL,{className:"w-4 h-4"}),"Confirm and Save"]})]}),(0,a.jsx)("div",{className:"bg-blue-50 border border-blue-200 rounded-xl p-4",children:(0,a.jsxs)("div",{className:"flex items-start gap-2",children:[(0,a.jsx)("div",{className:"w-5 h-5 bg-blue-500 rounded-full flex items-center justify-center mt-0.5",children:(0,a.jsx)("span",{className:"text-white text-xs",children:"!"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium text-blue-800",children:"Important:"}),(0,a.jsxs)("span",{className:"text-blue-700",children:[" Once confirmed, these changes will be applied to ",null==p?void 0:p.companyName,"'s benefit plans. Plan documents and employee communications should be updated accordingly."]})]})]})})]})]})}},46231:function(e,t,s){"use strict";s.d(t,{w_:function(){return d}});var a=s(2265),n={color:void 0,size:void 0,className:void 0,style:void 0,attr:void 0},l=a.createContext&&a.createContext(n),r=["attr","size","title"];function o(){return(o=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var s=arguments[t];for(var a in s)Object.prototype.hasOwnProperty.call(s,a)&&(e[a]=s[a])}return e}).apply(this,arguments)}function i(e,t){var s=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),s.push.apply(s,a)}return s}function c(e){for(var t=1;t<arguments.length;t++){var s=null!=arguments[t]?arguments[t]:{};t%2?i(Object(s),!0).forEach(function(t){var a,n;a=t,n=s[t],(a=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var s=e[Symbol.toPrimitive];if(void 0!==s){var a=s.call(e,t||"default");if("object"!=typeof a)return a;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(a))in e?Object.defineProperty(e,a,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[a]=n}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(s)):i(Object(s)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(s,t))})}return e}function d(e){return t=>a.createElement(m,o({attr:c({},e.attr)},t),function e(t){return t&&t.map((t,s)=>a.createElement(t.tag,c({key:s},t.attr),e(t.child)))}(e.child))}function m(e){var t=t=>{var s,{attr:n,size:l,title:i}=e,d=function(e,t){if(null==e)return{};var s,a,n=function(e,t){if(null==e)return{};var s={};for(var a in e)if(Object.prototype.hasOwnProperty.call(e,a)){if(t.indexOf(a)>=0)continue;s[a]=e[a]}return s}(e,t);if(Object.getOwnPropertySymbols){var l=Object.getOwnPropertySymbols(e);for(a=0;a<l.length;a++)s=l[a],!(t.indexOf(s)>=0)&&Object.prototype.propertyIsEnumerable.call(e,s)&&(n[s]=e[s])}return n}(e,r),m=l||t.size||"1em";return t.className&&(s=t.className),e.className&&(s=(s?s+" ":"")+e.className),a.createElement("svg",o({stroke:"currentColor",fill:"currentColor",strokeWidth:"0"},t.attr,n,d,{className:s,style:c(c({color:e.color||t.color},t.style),e.style),height:m,width:m,xmlns:"http://www.w3.org/2000/svg"}),i&&a.createElement("title",null,i),e.children)};return void 0!==l?a.createElement(l.Consumer,null,e=>t(e)):t(n)}}},function(e){e.O(0,[8422,8884,2971,2117,1744],function(){return e(e.s=65e3)}),_N_E=e.O()}]);