(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5405],{66104:function(e,t,a){Promise.resolve().then(a.bind(a,49297))},49297:function(e,t,a){"use strict";a.r(t),a.d(t,{default:function(){return c}});var l=a(57437),s=a(2265),n=a(99376),r=a(18913),o=a(88884),i=a(99859);function c(){let e=(0,n.useRouter)(),t=(0,n.useParams)(),a=(0,n.useSearchParams)(),c=t.companyId,d=(0,s.useMemo)(()=>{var e;return(null===(e=a.get("plans"))||void 0===e?void 0:e.split(","))||[]},[a]),[m,p]=(0,s.useState)(null),[u,g]=(0,s.useState)([]),[x,h]=(0,s.useState)(!0),[y,f]=(0,s.useState)(!1),[b,v]=(0,s.useState)(!1),[j,N]=(0,s.useState)("2024-11-01"),[w,S]=(0,s.useState)("2024-11-30"),[k,C]=(0,s.useState)("2025-01-01"),[P,D]=(0,s.useState)("2025-12-31"),[F,E]=(0,s.useState)("0"),[I,T]=(0,s.useState)("30"),A=async e=>{try{let i=await fetch("".concat("http://localhost:8080","/api/pre-enrollment/plans/").concat(e),{headers:{"Content-Type":"application/json","user-id":(()=>{let e=localStorage.getItem("userid1")||localStorage.getItem("userId");if(!e)throw Error("User ID not found. Please authenticate first.");return e})()}});if(i.ok){var t,a,l,s,n,r,o;let e=await i.json();return{planName:(null===(t=e.plan)||void 0===t?void 0:t.planName)||"Unknown Plan",planCode:(null===(a=e.plan)||void 0===a?void 0:a.planCode)||"N/A",planType:(null===(l=e.plan)||void 0===l?void 0:l.planType)||"N/A",coverageType:(null===(s=e.plan)||void 0===s?void 0:s.coverageType)||"Unknown",coverageSubTypes:(null===(n=e.plan)||void 0===n?void 0:n.coverageSubTypes)||[],metalTier:(null===(r=e.plan)||void 0===r?void 0:r.metalTier)||"",carrierName:(null===(o=e.carrier)||void 0===o?void 0:o.carrierName)||"Unknown Carrier"}}}catch(t){console.error("Error fetching plan details for planId:",e,t)}return{planName:"Unknown Plan",planCode:"N/A",planType:"N/A",coverageType:"Unknown",coverageSubTypes:[],metalTier:"",carrierName:"Unknown Carrier"}},U=async e=>{try{console.log("Fetching plan assignment details for ID:",e);let t=await fetch("".concat("http://localhost:8080","/api/pre-enrollment/plan-assignments/").concat(e),{headers:{"Content-Type":"application/json","user-id":localStorage.getItem("userid1")||localStorage.getItem("userId")||"6838677aef6db0212bcfdacd"}});if(console.log("Plan assignment fetch response status:",t.status),t.ok){let e=await t.json();console.log("Plan assignment fetch response data:",e),console.log("Assignment object:",e.assignment);let a=e.assignment._doc||e.assignment;return console.log("Processed assignment:",a),console.log("Coverage tiers in assignment:",null==a?void 0:a.coverageTiers),a}{console.error("Failed to fetch plan assignment details. Status:",t.status);let e=await t.text();console.error("Error response:",e)}}catch(e){console.error("Error fetching plan assignment details:",e)}return null},_=(0,s.useCallback)(async()=>{try{h(!0);let s=await fetch("".concat("http://localhost:8080","/api/pre-enrollment/company-benefits-settings/company/").concat(c),{headers:{"Content-Type":"application/json","user-id":(()=>{let e=localStorage.getItem("userid1")||localStorage.getItem("userId");if(!e)throw Error("User ID not found. Please authenticate first.");return e})()}});if(s.ok){let e=await s.json();p({_id:c,companyName:e.companyName||"Company Name",employeeCount:e.employeeCount||250})}else console.error("Failed to fetch company details:",s.status),p({_id:c,companyName:"Company Name",employeeCount:250});let n=await (0,o.fH)(c);if(n.success&&n.data){let s=n.data.assignments;console.log("All assignments:",s.length),console.log("Selected assignment IDs from URL:",d),console.log("Assignment IDs in data:",s.map(e=>e._id)),console.log("Sample assignment structure:",s[0]);let r=s;d.length>0&&(r=s.filter(e=>d.includes(e._id))),console.log("Filtered assignments:",r.length);let o=await Promise.all(r.map(async e=>{var t;let a="string"==typeof e.planId?e.planId:(null===(t=e.planId)||void 0===t?void 0:t._id)||"";console.log("Processing assignment:",e._id,"with planId:",a);let l=null;a&&(l=await A(a),console.log("Fetched plan details:",l));let s=await U(e._id);return console.log("Fetched assignment details:",s),{...e,planDetails:l,assignmentDetails:s}}));if(g(o),o.length>0){var e,t,a,l;let s=o[0];N((null===(e=s.enrollmentStartDate)||void 0===e?void 0:e.split("T")[0])||"2024-11-01"),S((null===(t=s.enrollmentEndDate)||void 0===t?void 0:t.split("T")[0])||"2024-11-30"),C((null===(a=s.planEffectiveDate)||void 0===a?void 0:a.split("T")[0])||"2025-01-01"),D((null===(l=s.planEndDate)||void 0===l?void 0:l.split("T")[0])||"2025-12-31")}}else console.error("Failed to fetch plan assignments:",n.error),g([])}catch(e){console.error("Error fetching data:",e),p({_id:c,companyName:"Company Name",employeeCount:250}),g([])}finally{h(!1)}},[c,d]);(0,s.useEffect)(()=>{_()},[_]);let H=async()=>{if(0===u.length)return!0;f(!0);try{let e=u.map(async e=>{let t=await (0,o.updatePlanAssignment)(e._id,{enrollmentStartDate:j,enrollmentEndDate:w,planEffectiveDate:k,planEndDate:P});if(!t.success){var a;throw console.error("Failed to update assignment ".concat(e._id,":"),t.error),Error("Failed to update ".concat((null===(a=e.plan)||void 0===a?void 0:a.planName)||"plan",": ").concat(t.error))}return t.data});return await Promise.all(e),console.log("All plan assignments updated successfully"),!0}catch(e){return console.error("Error saving date changes:",e),alert("Error saving changes: ".concat(e instanceof Error?e.message:"Unknown error")),!1}finally{f(!1)}},R=async()=>{if(await H()){let t=u.map(e=>e._id);e.push("/ai-enroller/manage-groups/company/".concat(c,"/review?assignments=").concat(t.join(",")))}},B=async()=>{await H()&&e.push("/ai-enroller/manage-groups/company/".concat(c,"/plans"))};return x?(0,l.jsx)("div",{className:"min-h-screen bg-white flex items-center justify-center",children:(0,l.jsxs)("div",{className:"text-center",children:[(0,l.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-black mx-auto"}),(0,l.jsx)("p",{className:"mt-4 text-gray-600",children:"Loading..."})]})}):(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)(i.Z,{}),(0,l.jsxs)("div",{className:"min-h-screen bg-white",children:[(0,l.jsx)("div",{className:"bg-white border-b border-gray-200 px-6 py-4",children:(0,l.jsxs)("div",{className:"max-w-7xl mx-auto",children:[(0,l.jsxs)("div",{className:"flex gap-3 overflow-x-auto",children:[(0,l.jsxs)("button",{onClick:()=>e.push("/ai-enroller"),className:"flex items-center gap-2 px-4 py-2 rounded-full text-sm font-medium transition-all bg-purple-100 text-purple-700 hover:bg-purple-200 whitespace-nowrap",style:{fontFamily:"'SF Pro', 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif",fontSize:"13px",fontWeight:"450",lineHeight:"1.2"},children:[(0,l.jsx)(r.VRM,{className:"w-4 h-4"}),"Home",(0,l.jsx)("span",{className:"flex items-center justify-center w-4 h-4 bg-purple-600 rounded-full ml-2",children:(0,l.jsx)(r.PjL,{className:"w-5 h-5 text-white"})})]}),(0,l.jsxs)("button",{onClick:()=>e.push("/ai-enroller/manage-groups/select-company"),className:"flex items-center gap-2 px-4 py-2 rounded-full text-sm font-medium transition-all bg-purple-100 text-purple-700 hover:bg-purple-200 whitespace-nowrap",style:{fontFamily:"'SF Pro', 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif",fontSize:"13px",fontWeight:"450",lineHeight:"1.2"},children:[(0,l.jsx)(r.$xp,{className:"w-4 h-4"}),"Select Company",(0,l.jsx)("span",{className:"flex items-center justify-center w-4 h-4 bg-purple-600 text-white rounded-full ml-2",children:(0,l.jsx)(r.PjL,{className:"w-5 h-5"})})]}),(0,l.jsxs)("button",{onClick:()=>e.push("/ai-enroller/manage-groups/company/".concat(c,"/plans")),className:"flex items-center gap-2 px-4 py-2 rounded-full text-sm font-medium transition-all bg-purple-100 text-purple-700 hover:bg-purple-200 whitespace-nowrap",style:{fontFamily:"'SF Pro', 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif",fontSize:"13px",fontWeight:"450",lineHeight:"1.2"},children:[(0,l.jsx)(r.GwR,{className:"w-4 h-4"}),"View Plans",(0,l.jsx)("span",{className:"flex items-center justify-center w-4 h-4 bg-purple-600 text-white rounded-full ml-2",children:(0,l.jsx)(r.PjL,{className:"w-5 h-5"})})]}),(0,l.jsxs)("div",{className:"flex items-center gap-2 px-4 py-2 rounded-full text-sm font-medium bg-purple-50 text-purple-600 whitespace-nowrap",style:{fontFamily:"'SF Pro', 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif",fontSize:"13px",fontWeight:"450",lineHeight:"1.2"},children:[(0,l.jsx)(r.Bge,{className:"w-4 h-4"}),"Set Dates"]}),(0,l.jsxs)("div",{className:"flex items-center gap-2 px-4 py-2 rounded-full text-sm font-medium bg-gray-100 text-gray-600 whitespace-nowrap",style:{fontFamily:"'SF Pro', 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif",fontSize:"13px",fontWeight:"450",lineHeight:"1.2"},children:[(0,l.jsx)(r.PjL,{className:"w-4 h-4"}),"Review"]}),(0,l.jsxs)("div",{className:"flex items-center gap-2 px-4 py-2 rounded-full text-sm font-medium bg-gray-100 text-gray-600 whitespace-nowrap",style:{fontFamily:"'SF Pro', 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif",fontSize:"13px",fontWeight:"450",lineHeight:"1.2"},children:[(0,l.jsx)(r.PjL,{className:"w-4 h-4"}),"Confirmation"]})]}),(0,l.jsx)("div",{className:"mt-4",children:(0,l.jsxs)("div",{className:"text-right text-sm text-gray-500",children:["Step 4 of 6",(0,l.jsx)("br",{}),(0,l.jsx)("span",{className:"text-xs text-purple-600 font-medium",children:"Set plan dates"})]})})]})}),(0,l.jsxs)("div",{className:"max-w-4xl mx-auto px-6 py-8 bg-white",children:[(0,l.jsxs)("div",{className:"mb-8",children:[(0,l.jsx)("h1",{className:"text-2xl font-bold text-gray-900 mb-2",children:"Set Enrollment & Active Dates"}),(0,l.jsxs)("p",{className:"text-gray-600",children:["Configure dates for ",u.length," plan",1!==u.length?"s":"",u.length>0&&(0,l.jsx)("span",{className:"block text-sm text-gray-500 mt-1",children:u.map(e=>{var t;return(null===(t=e.planDetails)||void 0===t?void 0:t.planName)||"Unknown Plan"}).join(", ")})]})]}),0===u.length&&!x&&(0,l.jsx)("div",{className:"bg-yellow-50 border border-yellow-200 rounded-xl p-6 mb-6",children:(0,l.jsxs)("div",{className:"flex items-center gap-3",children:[(0,l.jsx)("div",{className:"w-8 h-8 bg-yellow-100 rounded-xl flex items-center justify-center",children:(0,l.jsx)("span",{className:"text-yellow-600 text-lg",children:"⚠️"})}),(0,l.jsxs)("div",{children:[(0,l.jsx)("h3",{className:"font-semibold text-yellow-800",children:"No Plan Assignments Found"}),(0,l.jsx)("p",{className:"text-sm text-yellow-700",children:"No plan assignments were found for this company. Please create plan assignments first."})]})]})}),u.length>0&&(0,l.jsxs)("div",{className:"bg-white border border-gray-200 rounded-xl p-6 mb-6",children:[(0,l.jsxs)("div",{className:"flex items-center gap-3 mb-6",children:[(0,l.jsx)("div",{className:"w-8 h-8 bg-blue-100 rounded-xl flex items-center justify-center",children:(0,l.jsx)("span",{className:"text-blue-600 text-lg",children:"\uD83D\uDCCB"})}),(0,l.jsxs)("div",{children:[(0,l.jsx)("h2",{className:"text-xl font-semibold text-gray-900",children:"Selected Plans"}),(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"Plans that will be updated with new dates"})]})]}),(0,l.jsx)("div",{className:"space-y-4",children:u.map(e=>{var t,a,s,n,r,o,i,c;let d=(null===(t=e.planDetails)||void 0===t?void 0:t.planName)||"Unknown Plan",m=(null===(a=e.planDetails)||void 0===a?void 0:a.planCode)||"N/A",p=(null===(s=e.planDetails)||void 0===s?void 0:s.coverageType)||"Unknown Coverage",u=(null===(n=e.planDetails)||void 0===n?void 0:n.carrierName)||"Unknown Carrier",g=(null===(r=e.planDetails)||void 0===r?void 0:r.metalTier)||"",x=(null===(o=e.assignmentDetails)||void 0===o?void 0:o.coverageTiers)||e.coverageTiers||[],h=x[0]||{},y=h.totalCost||0,f=h.employerCost||0,b=h.employeeCost||y-f;return(0,l.jsx)("div",{className:"border-l-4 border-blue-500 pl-4 py-3 bg-gray-50 rounded-r-lg",children:(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"flex-1",children:[(0,l.jsx)("h3",{className:"font-semibold text-gray-900",children:d}),(0,l.jsxs)("p",{className:"text-sm text-gray-600",children:[u," • ",p," ",g&&"• ".concat(g)," • ",m]}),(0,l.jsxs)("p",{className:"text-xs text-gray-500",children:["Current: ",(null===(i=e.enrollmentStartDate)||void 0===i?void 0:i.split("T")[0])||"N/A"," to ",(null===(c=e.enrollmentEndDate)||void 0===c?void 0:c.split("T")[0])||"N/A"]}),x.length>1&&(0,l.jsxs)("p",{className:"text-xs text-blue-600 mt-1",children:[x.length," coverage tiers available"]})]}),(0,l.jsxs)("div",{className:"text-right ml-4",children:[(0,l.jsxs)("p",{className:"text-sm font-medium text-gray-900",children:["$",y.toFixed(2),"/month"]}),(0,l.jsxs)("p",{className:"text-xs text-gray-500",children:["Employer: $",f.toFixed(2)]}),b>0&&(0,l.jsxs)("p",{className:"text-xs text-gray-500",children:["Employee: $",b.toFixed(2)]}),x.length>0&&(0,l.jsx)("p",{className:"text-xs text-gray-400 mt-1",children:h.tierName||"Employee Only"})]})]})},e._id)})})]}),(0,l.jsxs)("div",{className:"bg-white border border-gray-200 rounded-xl p-6 mb-6",children:[(0,l.jsxs)("div",{className:"flex items-center gap-3 mb-6",children:[(0,l.jsx)(r.Bge,{className:"text-gray-600 w-6 h-6"}),(0,l.jsxs)("div",{children:[(0,l.jsx)("h2",{className:"text-xl font-semibold text-gray-900",children:"Date Configuration"}),(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"Set enrollment and plan active dates for these plans"})]})]}),(0,l.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8",children:[(0,l.jsxs)("div",{className:"border-l-4 border-blue-500 pl-6",children:[(0,l.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"Open Enrollment Dates"}),(0,l.jsx)("p",{className:"text-sm text-gray-600 mb-4",children:"When employees can enroll in this plan"}),(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Enrollment Start Date"}),(0,l.jsx)("input",{type:"date",value:j,onChange:e=>N(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-black focus:border-black bg-white",style:{color:"#111827",backgroundColor:"white"}})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Enrollment End Date"}),(0,l.jsx)("input",{type:"date",value:w,onChange:e=>S(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-black focus:border-black bg-white",style:{color:"#111827",backgroundColor:"white"}})]})]})]}),(0,l.jsxs)("div",{className:"border-l-4 border-green-500 pl-6",children:[(0,l.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"Plan Active Dates"}),(0,l.jsx)("p",{className:"text-sm text-gray-600 mb-4",children:"When this plan coverage is active"}),(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Plan Start Date"}),(0,l.jsx)("input",{type:"date",value:k,onChange:e=>C(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-black focus:border-black bg-white",style:{color:"#111827",backgroundColor:"white"}})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Plan End Date"}),(0,l.jsx)("input",{type:"date",value:P,onChange:e=>D(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-black focus:border-black bg-white",style:{color:"#111827",backgroundColor:"white"}})]})]})]})]})]}),(0,l.jsxs)("div",{className:"bg-white border border-gray-200 rounded-xl p-6 mb-6",children:[(0,l.jsx)("h2",{className:"text-lg font-semibold text-gray-900 mb-2",children:"Additional Settings"}),(0,l.jsx)("p",{className:"text-sm text-blue-600 mb-6",children:"Optional configuration for this plan"}),(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Waiting Period (days)"}),(0,l.jsx)("input",{type:"number",value:F,onChange:e=>E(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-black focus:border-black bg-white",style:{color:"#111827",backgroundColor:"white"},min:"0",max:"365"})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Grace Period (days)"}),(0,l.jsx)("input",{type:"number",value:I,onChange:e=>T(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-black focus:border-black bg-white",style:{color:"#111827",backgroundColor:"white"},min:"0",max:"90"})]})]})]}),(0,l.jsx)("div",{className:"bg-green-50 border border-green-200 rounded-xl p-4 mb-8",children:(0,l.jsxs)("div",{className:"flex items-center gap-2",children:[(0,l.jsx)("div",{className:"w-5 h-5 bg-green-500 rounded-full flex items-center justify-center",children:(0,l.jsx)("span",{className:"text-white text-xs",children:"✓"})}),(0,l.jsxs)("div",{children:[(0,l.jsx)("span",{className:"font-medium text-green-800",children:"Plan Configuration Complete"}),(0,l.jsx)("p",{className:"text-green-700 text-sm",children:"You can now return to configure other plans or continue the workflow"})]})]})}),(0,l.jsxs)("div",{className:"flex gap-4",children:[(0,l.jsx)("button",{onClick:B,disabled:y,className:"px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors disabled:opacity-50 disabled:cursor-not-allowed",children:y?"Saving...":"Save & Return to Plans"}),(0,l.jsxs)("button",{onClick:R,disabled:y,className:"flex-1 px-6 py-2 bg-black text-white rounded-lg hover:bg-gray-800 transition-colors flex items-center justify-center gap-2 cursor-pointer disabled:opacity-50 disabled:cursor-not-allowed",type:"button",children:[y?"Saving...":"Continue to Review",!y&&(0,l.jsx)("span",{children:"→"})]})]})]})]})]})}},83337:function(e,t,a){"use strict";a.d(t,{C:function(){return n},T:function(){return s}});var l=a(68575);let s=()=>(0,l.I0)(),n=l.v9}},function(e){e.O(0,[139,8422,3463,3301,8575,8685,187,1423,9932,3919,208,9859,8884,2971,2117,1744],function(){return e(e.s=66104)}),_N_E=e.O()}]);