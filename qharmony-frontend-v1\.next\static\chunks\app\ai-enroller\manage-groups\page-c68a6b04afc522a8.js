(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2250],{41107:function(e,a,s){Promise.resolve().then(s.bind(s,81322))},65110:function(e,a,s){"use strict";var n=s(57437),i=s(2265),t=s(18913),l=s(39547);s(18093),a.Z=e=>{let{isOpen:a,onClose:s,onSuccess:o}=e,[c,r]=(0,i.useState)({companyName:"",adminName:"",adminEmail:"",industry:"",location:"",companySize:""}),[d,m]=(0,i.useState)(!1),[h,u]=(0,i.useState)(""),p=e=>{let{name:a,value:s}=e.target;r(e=>({...e,[a]:s})),u("")},x=async e=>{e.preventDefault(),m(!0),u("");try{let e=localStorage.getItem("userid1");if(!e)throw Error("User not authenticated");let s=await (0,l.G9)(e,c.companyName,c.adminEmail,c.adminName);if(200===s.status)o(),r({companyName:"",adminName:"",adminEmail:"",industry:"",location:"",companySize:""});else{var a;throw Error((null===(a=s.data)||void 0===a?void 0:a.message)||"Failed to add company")}}catch(e){u(e instanceof Error?e.message:"An error occurred")}finally{m(!1)}};return a?(0,n.jsx)("div",{className:"modal-overlay",children:(0,n.jsxs)("div",{className:"modal-container",children:[(0,n.jsxs)("div",{className:"modal-header",children:[(0,n.jsxs)("div",{className:"modal-title",children:[(0,n.jsx)(t.$xp,{size:24}),(0,n.jsx)("h2",{children:"Add New Employer Group"})]}),(0,n.jsx)("button",{className:"close-button",onClick:s,children:(0,n.jsx)(t.fMW,{size:24})})]}),(0,n.jsxs)("form",{onSubmit:x,className:"modal-form",children:[(0,n.jsxs)("div",{className:"form-grid",children:[(0,n.jsxs)("div",{className:"form-group",children:[(0,n.jsx)("label",{htmlFor:"companyName",children:"Company Name *"}),(0,n.jsx)("input",{type:"text",id:"companyName",name:"companyName",value:c.companyName,onChange:p,placeholder:"Enter company name",required:!0})]}),(0,n.jsxs)("div",{className:"form-group",children:[(0,n.jsx)("label",{htmlFor:"adminName",children:"Admin Name *"}),(0,n.jsx)("input",{type:"text",id:"adminName",name:"adminName",value:c.adminName,onChange:p,placeholder:"Enter admin full name",required:!0})]}),(0,n.jsxs)("div",{className:"form-group",children:[(0,n.jsx)("label",{htmlFor:"adminEmail",children:"Admin Email *"}),(0,n.jsx)("input",{type:"email",id:"adminEmail",name:"adminEmail",value:c.adminEmail,onChange:p,placeholder:"<EMAIL>",required:!0})]}),(0,n.jsxs)("div",{className:"form-group",children:[(0,n.jsx)("label",{htmlFor:"industry",children:"Industry"}),(0,n.jsxs)("select",{id:"industry",name:"industry",value:c.industry,onChange:p,children:[(0,n.jsx)("option",{value:"",children:"Select industry"}),["Technology","Healthcare","Finance","Manufacturing","Retail","Education","Construction","Real Estate","Transportation","Hospitality","Other"].map(e=>(0,n.jsx)("option",{value:e,children:e},e))]})]}),(0,n.jsxs)("div",{className:"form-group",children:[(0,n.jsx)("label",{htmlFor:"location",children:"Location"}),(0,n.jsx)("input",{type:"text",id:"location",name:"location",value:c.location,onChange:p,placeholder:"City, State"})]}),(0,n.jsxs)("div",{className:"form-group",children:[(0,n.jsx)("label",{htmlFor:"companySize",children:"Company Size"}),(0,n.jsxs)("select",{id:"companySize",name:"companySize",value:c.companySize,onChange:p,children:[(0,n.jsx)("option",{value:"",children:"Select company size"}),["1-10 employees","11-50 employees","51-200 employees","201-500 employees","501-1000 employees","1000+ employees"].map(e=>(0,n.jsx)("option",{value:e,children:e},e))]})]})]}),h&&(0,n.jsx)("div",{className:"error-message",children:h}),(0,n.jsxs)("div",{className:"form-actions",children:[(0,n.jsx)("button",{type:"button",className:"cancel-button",onClick:s,disabled:d,children:"Cancel"}),(0,n.jsx)("button",{type:"submit",className:"submit-button",disabled:!(c.companyName.trim()&&c.adminName.trim()&&c.adminEmail.trim()&&c.adminEmail.includes("@"))||d,children:d?"Adding...":"Add Company"})]})]}),(0,n.jsx)("div",{className:"modal-note",children:(0,n.jsxs)("p",{children:[(0,n.jsx)("strong",{children:"Note:"})," The admin will receive an email invitation to set up their account and complete the company onboarding process."]})})]})}):null}},81322:function(e,a,s){"use strict";s.r(a);var n=s(57437),i=s(2265),t=s(99376),l=s(18913),o=s(48223),c=s(39547),r=s(68575),d=s(65110),m=s(88884),h=s(99859);s(17673),a.default=()=>{let e=(0,t.useRouter)(),a=(0,r.I0)(),[s,u]=(0,i.useState)(!0),[p,x]=(0,i.useState)(!1),j=(0,r.v9)(e=>e.user.managedCompanies),g=async()=>{try{console.log("\uD83D\uDD0D Fetching broker plan assignments count using optimized API");let e=await (0,m.T1)();if(e.success&&e.data){let a=e.data.count;console.log("✅ Total plan assignments managed by broker (optimized):",a),y(a)}else console.warn("❌ Failed to fetch broker plan assignments count:",e.error),y(0)}catch(e){console.error("❌ Error fetching plan assignments count (optimized):",e),y(0)}},N=(0,i.useCallback)(async()=>{try{{let e=localStorage.getItem("userid1")||"6838677aef6db0212bcfdacd";await (0,c.qB)(a,e),await g()}u(!1)}catch(e){console.error("Error fetching dashboard data:",e),u(!1)}},[a]);(0,i.useEffect)(()=>{N()},[N]);let[v,y]=(0,i.useState)(0),f=(null==j?void 0:j.length)||0,b=(null==j?void 0:j.reduce((e,a)=>e+a.companySize,0))||0;return s?(0,n.jsx)("div",{className:"broker-dashboard",children:(0,n.jsxs)("div",{className:"loading-container",children:[(0,n.jsx)("div",{className:"loading-spinner"}),(0,n.jsx)("p",{children:"Loading dashboard..."})]})}):(0,n.jsxs)(o.Z,{children:[(0,n.jsx)(h.Z,{}),(0,n.jsxs)("div",{className:"broker-dashboard",children:[(0,n.jsx)("div",{className:"dashboard-header",children:(0,n.jsxs)("div",{className:"header-content",children:[(0,n.jsx)("h1",{className:"page-title",children:"Broker Dashboard"}),(0,n.jsx)("p",{className:"subtitle-text",children:"Manage employer groups and benefit plans with ease. Your one-stop solution for comprehensive benefits administration."})]})}),(0,n.jsxs)("div",{className:"stats-grid",children:[(0,n.jsxs)("div",{className:"stat-card",children:[(0,n.jsxs)("div",{className:"stat-content",children:[(0,n.jsx)("div",{className:"stat-number",children:f}),(0,n.jsx)("div",{className:"stat-label",children:"Active Companies"})]}),(0,n.jsx)("div",{className:"stat-icon",children:(0,n.jsx)(l.$xp,{size:24})})]}),(0,n.jsxs)("div",{className:"stat-card",children:[(0,n.jsxs)("div",{className:"stat-content",children:[(0,n.jsx)("div",{className:"stat-number",children:b.toLocaleString()}),(0,n.jsx)("div",{className:"stat-label",children:"Total Employees"})]}),(0,n.jsx)("div",{className:"stat-icon",children:(0,n.jsx)(l.Otr,{size:24})})]}),(0,n.jsxs)("div",{className:"stat-card",children:[(0,n.jsxs)("div",{className:"stat-content",children:[(0,n.jsx)("div",{className:"stat-number",children:v}),(0,n.jsx)("div",{className:"stat-label",children:"Plan Assignments"})]}),(0,n.jsx)("div",{className:"stat-icon",children:(0,n.jsx)(l.GwR,{size:24})})]})]}),(0,n.jsxs)("div",{className:"quick-actions",children:[(0,n.jsx)("h2",{className:"section-header",children:"Quick Actions"}),(0,n.jsx)("p",{className:"body-text",children:"Choose how you'd like to get started"}),(0,n.jsxs)("div",{className:"action-cards",children:[(0,n.jsxs)("div",{className:"action-card",onClick:()=>{x(!0)},children:[(0,n.jsx)("div",{className:"action-icon",children:(0,n.jsx)(l.r7I,{size:32})}),(0,n.jsxs)("div",{className:"action-content",children:[(0,n.jsx)("h3",{className:"section-header",style:{fontSize:"18px"},children:"New Group"}),(0,n.jsx)("p",{className:"body-text",children:"Setting up benefits for a new organization or group that hasn't had coverage before"}),(0,n.jsxs)("div",{className:"action-tags",children:[(0,n.jsx)("span",{className:"tag",children:"Fresh start"}),(0,n.jsx)("span",{className:"tag",children:"New enrollment"})]})]})]}),(0,n.jsxs)("div",{className:"action-card",onClick:()=>{e.push("/ai-enroller/manage-groups/select-company")},children:[(0,n.jsx)("div",{className:"action-icon",children:(0,n.jsx)(l.$xp,{size:32})}),(0,n.jsxs)("div",{className:"action-content",children:[(0,n.jsx)("h3",{className:"section-header",style:{fontSize:"18px"},children:"Existing Group"}),(0,n.jsx)("p",{className:"body-text",children:"Adding or modifying benefits for an organization that already has some coverage in place"}),(0,n.jsxs)("div",{className:"action-tags",children:[(0,n.jsx)("span",{className:"tag",children:"Active enrollment"}),(0,n.jsx)("span",{className:"tag",children:"Plan changes"})]})]})]})]})]}),(0,n.jsx)("div",{className:"back-button-container",children:(0,n.jsxs)("button",{className:"back-button",onClick:()=>{e.push("/ai-enroller")},children:[(0,n.jsx)(l.Tsu,{size:20}),"Back to Main"]})}),p&&(0,n.jsx)(d.Z,{isOpen:p,onClose:()=>x(!1),onSuccess:()=>{x(!1),N()}})]})]})}},48223:function(e,a,s){"use strict";var n=s(57437),i=s(2265),t=s(47369),l=s(99376),o=s(83337),c=s(70623),r=s(39547),d=s(35389),m=s(95656);let h=()=>/Mobi|Android/i.test(navigator.userAgent);a.Z=e=>{let{children:a}=e,{user:s,loading:u}=(0,t.a)(),p=(0,l.useRouter)(),x=(0,l.usePathname)(),j=(0,o.T)(),[g,N]=(0,i.useState)(!1),v=(0,o.C)(e=>e.user.userProfile);return((0,i.useEffect)(()=>{{let e=localStorage.getItem("userid1")||localStorage.getItem("userId");console.log("USER ID FROM LOCAL STORAGE: ",e),e&&!v.name&&(j((0,c.Iv)(e)),(async()=>{try{await (0,r.M_)(j,e),await (0,r.aK)(j)}catch(e){console.error("Error fetching user data in ProtectedRoute:",e)}})())}},[j,v.name]),(0,i.useEffect)(()=>{console.log("ProtectedRoute useEffect triggered"),console.log("Current user: ",s),console.log("Loading state: ",u),console.log("Current user details: ",v),u||s||(console.log("User not authenticated, redirecting to home"),N(!1),p.push("/")),!u&&v.companyId&&""===v.companyId&&(console.log("Waiting to retrieve company details"),N(!1)),!u&&v.companyId&&""!==v.companyId&&(console.log("User found, rendering children"),N(!0)),h()&&!x.startsWith("/mobile")&&(console.log("Redirecting to mobile version of ".concat(x)),p.push("/mobile".concat(x)))},[s,u,v,p,x]),g)?s?(0,n.jsx)(n.Fragment,{children:a}):null:(0,n.jsx)(m.Z,{sx:{display:"flex",justifyContent:"center",alignItems:"center",height:"100vh",bgcolor:"#f6f8fc"},children:(0,n.jsx)(d.Z,{})})}},18093:function(){},17673:function(){}},function(e){e.O(0,[4793,139,8422,3463,3301,8575,293,9810,187,3145,9932,3919,9129,208,3344,9859,8884,2971,2117,1744],function(){return e(e.s=41107)}),_N_E=e.O()}]);