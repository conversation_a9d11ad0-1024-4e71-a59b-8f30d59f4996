(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7790],{4776:function(e,a,s){Promise.resolve().then(s.bind(s,85631))},65110:function(e,a,s){"use strict";var t=s(57437),n=s(2265),l=s(18913),r=s(39547);s(18093),a.Z=e=>{let{isOpen:a,onClose:s,onSuccess:i}=e,[o,c]=(0,n.useState)({companyName:"",adminName:"",adminEmail:"",industry:"",location:"",companySize:""}),[d,m]=(0,n.useState)(!1),[x,u]=(0,n.useState)(""),h=e=>{let{name:a,value:s}=e.target;c(e=>({...e,[a]:s})),u("")},p=async e=>{e.preventDefault(),m(!0),u("");try{let e=localStorage.getItem("userid1");if(!e)throw Error("User not authenticated");let s=await (0,r.G9)(e,o.companyName,o.adminEmail,o.adminName);if(200===s.status)i(),c({companyName:"",adminName:"",adminEmail:"",industry:"",location:"",companySize:""});else{var a;throw Error((null===(a=s.data)||void 0===a?void 0:a.message)||"Failed to add company")}}catch(e){u(e instanceof Error?e.message:"An error occurred")}finally{m(!1)}};return a?(0,t.jsx)("div",{className:"modal-overlay",children:(0,t.jsxs)("div",{className:"modal-container",children:[(0,t.jsxs)("div",{className:"modal-header",children:[(0,t.jsxs)("div",{className:"modal-title",children:[(0,t.jsx)(l.$xp,{size:24}),(0,t.jsx)("h2",{children:"Add New Employer Group"})]}),(0,t.jsx)("button",{className:"close-button",onClick:s,children:(0,t.jsx)(l.fMW,{size:24})})]}),(0,t.jsxs)("form",{onSubmit:p,className:"modal-form",children:[(0,t.jsxs)("div",{className:"form-grid",children:[(0,t.jsxs)("div",{className:"form-group",children:[(0,t.jsx)("label",{htmlFor:"companyName",children:"Company Name *"}),(0,t.jsx)("input",{type:"text",id:"companyName",name:"companyName",value:o.companyName,onChange:h,placeholder:"Enter company name",required:!0})]}),(0,t.jsxs)("div",{className:"form-group",children:[(0,t.jsx)("label",{htmlFor:"adminName",children:"Admin Name *"}),(0,t.jsx)("input",{type:"text",id:"adminName",name:"adminName",value:o.adminName,onChange:h,placeholder:"Enter admin full name",required:!0})]}),(0,t.jsxs)("div",{className:"form-group",children:[(0,t.jsx)("label",{htmlFor:"adminEmail",children:"Admin Email *"}),(0,t.jsx)("input",{type:"email",id:"adminEmail",name:"adminEmail",value:o.adminEmail,onChange:h,placeholder:"<EMAIL>",required:!0})]}),(0,t.jsxs)("div",{className:"form-group",children:[(0,t.jsx)("label",{htmlFor:"industry",children:"Industry"}),(0,t.jsxs)("select",{id:"industry",name:"industry",value:o.industry,onChange:h,children:[(0,t.jsx)("option",{value:"",children:"Select industry"}),["Technology","Healthcare","Finance","Manufacturing","Retail","Education","Construction","Real Estate","Transportation","Hospitality","Other"].map(e=>(0,t.jsx)("option",{value:e,children:e},e))]})]}),(0,t.jsxs)("div",{className:"form-group",children:[(0,t.jsx)("label",{htmlFor:"location",children:"Location"}),(0,t.jsx)("input",{type:"text",id:"location",name:"location",value:o.location,onChange:h,placeholder:"City, State"})]}),(0,t.jsxs)("div",{className:"form-group",children:[(0,t.jsx)("label",{htmlFor:"companySize",children:"Company Size"}),(0,t.jsxs)("select",{id:"companySize",name:"companySize",value:o.companySize,onChange:h,children:[(0,t.jsx)("option",{value:"",children:"Select company size"}),["1-10 employees","11-50 employees","51-200 employees","201-500 employees","501-1000 employees","1000+ employees"].map(e=>(0,t.jsx)("option",{value:e,children:e},e))]})]})]}),x&&(0,t.jsx)("div",{className:"error-message",children:x}),(0,t.jsxs)("div",{className:"form-actions",children:[(0,t.jsx)("button",{type:"button",className:"cancel-button",onClick:s,disabled:d,children:"Cancel"}),(0,t.jsx)("button",{type:"submit",className:"submit-button",disabled:!(o.companyName.trim()&&o.adminName.trim()&&o.adminEmail.trim()&&o.adminEmail.includes("@"))||d,children:d?"Adding...":"Add Company"})]})]}),(0,t.jsx)("div",{className:"modal-note",children:(0,t.jsxs)("p",{children:[(0,t.jsx)("strong",{children:"Note:"})," The admin will receive an email invitation to set up their account and complete the company onboarding process."]})})]})}):null}},85631:function(e,a,s){"use strict";s.r(a),s.d(a,{default:function(){return u}});var t=s(57437),n=s(2265),l=s(99376),r=s(18913),i=s(48223),o=s(68575),c=s(39547),d=s(88884),m=s(65110),x=s(99859);function u(){let e=(0,l.useRouter)(),a=(0,o.I0)(),[s,u]=(0,n.useState)(!0),[h,p]=(0,n.useState)(""),[g,y]=(0,n.useState)(0),[j,f]=(0,n.useState)(!1),N=(0,o.v9)(e=>e.user.managedCompanies),b=(0,n.useCallback)(async()=>{try{console.log("\uD83D\uDD0D Fetching plan assignments count for broker...");let e=await (0,d.T1)();if(e.success&&e.data){let a=e.data.count;console.log("✅ Total plan assignments managed by broker:",a),y(a)}else console.warn("❌ Failed to fetch broker plan assignments count:",e.error),y(0)}catch(e){console.error("❌ Error fetching plan assignments count:",e),y(0)}},[]),v=(0,n.useCallback)(async()=>{try{{let e=localStorage.getItem("userid1")||localStorage.getItem("userId");e?await (0,c.qB)(a,e):console.error("User ID not found. Please authenticate first.")}}catch(e){console.error("Error fetching companies:",e)}finally{u(!1)}},[a]);(0,n.useEffect)(()=>{(async()=>{await v(),await b()})()},[v,b]);let S=((null==N?void 0:N.map((e,a)=>({_id:e._id,companyName:e.name,ein:e.ein||"12-".concat(String(a+1).padStart(7,"0")),location:e.location||"San Francisco, CA",companySize:e.companySize||Math.floor(500*Math.random())+50,status:e.isActivated?"active":"pending",industry:e.industry||"Technology",plansAssigned:Math.floor(10*Math.random())+2,joinDate:e.createdAt?new Date(e.createdAt).toLocaleDateString():new Date().toLocaleDateString()})))||[]).filter(e=>e.companyName.toLowerCase().includes(h.toLowerCase())||e.ein.includes(h)||e.location.toLowerCase().includes(h.toLowerCase())),w=a=>{e.push("/ai-enroller/manage-groups/company/".concat(a,"/plans"))},C=async()=>{f(!1),await v(),await b()},E=(null==N?void 0:N.length)||0,k=(null==N?void 0:N.reduce((e,a)=>e+a.companySize,0))||0;return s?(0,t.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-black mx-auto"}),(0,t.jsx)("p",{className:"mt-4 text-gray-600",children:"Loading companies..."})]})}):(0,t.jsxs)(i.Z,{children:[(0,t.jsx)(x.Z,{}),(0,t.jsxs)("div",{className:"min-h-screen bg-white",children:[(0,t.jsx)("div",{className:"bg-white border-b border-gray-200 px-6 py-4",children:(0,t.jsx)("div",{className:"max-w-6xl mx-auto",children:(0,t.jsxs)("nav",{className:"flex items-center space-x-2 text-sm",children:[(0,t.jsx)("button",{onClick:()=>e.push("/ai-enroller"),className:"text-gray-600 hover:text-gray-900 font-medium transition-colors",children:"Home"}),(0,t.jsx)("span",{className:"text-gray-400",children:"›"}),(0,t.jsx)("span",{className:"text-gray-900 font-medium",children:"Select Company"})]})})}),(0,t.jsxs)("div",{className:"max-w-6xl mx-auto px-6 py-8 bg-white",children:[(0,t.jsxs)("div",{className:"page-header",children:[(0,t.jsxs)("div",{className:"header-left",children:[(0,t.jsx)("div",{className:"page-icon",children:(0,t.jsx)(r.$xp,{size:24})}),(0,t.jsx)("h1",{children:"Manage Groups"})]}),(0,t.jsxs)("button",{className:"add-new-group-btn",onClick:()=>{f(!0)},style:{background:"linear-gradient(135deg, #2563eb 0%, #8b5cf6 100%)",border:"none"},children:[(0,t.jsx)("span",{children:"+"}),"Add New Group"]})]}),(0,t.jsx)("div",{className:"mb-6",children:(0,t.jsx)("p",{className:"text-gray-600",style:{fontSize:"16px",lineHeight:"1.6",fontFamily:"'SF Pro', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif"},children:"\xa0Oversee all employer groups and their benefit plan assignments"})}),(0,t.jsxs)("div",{className:"stats-grid",children:[(0,t.jsxs)("div",{className:"stat-card",children:[(0,t.jsxs)("div",{className:"stat-content",children:[(0,t.jsx)("div",{className:"stat-number",children:E}),(0,t.jsx)("div",{className:"stat-label",children:"Active Companies"})]}),(0,t.jsx)("div",{className:"stat-icon",children:(0,t.jsx)(r.$xp,{size:24})})]}),(0,t.jsxs)("div",{className:"stat-card",children:[(0,t.jsxs)("div",{className:"stat-content",children:[(0,t.jsx)("div",{className:"stat-number",children:k.toLocaleString()}),(0,t.jsx)("div",{className:"stat-label",children:"Total Employees"})]}),(0,t.jsx)("div",{className:"stat-icon",children:(0,t.jsx)(r.Otr,{size:24})})]}),(0,t.jsxs)("div",{className:"stat-card",children:[(0,t.jsxs)("div",{className:"stat-content",children:[(0,t.jsx)("div",{className:"stat-number",children:g}),(0,t.jsx)("div",{className:"stat-label",children:"Plan Assignments"})]}),(0,t.jsx)("div",{className:"stat-icon",children:(0,t.jsx)(r.GwR,{size:24})})]})]}),(0,t.jsx)("div",{className:"mb-6",children:(0,t.jsxs)("div",{className:"relative w-full",children:[(0,t.jsx)(r.O6C,{className:"absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5"}),(0,t.jsx)("input",{type:"text",placeholder:"Search by company name, EIN, or location...",value:h,onChange:e=>p(e.target.value),className:"w-full pl-12 pr-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm bg-white transition-all duration-200 hover:border-gray-400 text-gray-900",style:{fontFamily:"'SF Pro', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif",boxShadow:"0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)",color:"#111827"}})]})}),0===S.length?(0,t.jsxs)("div",{className:"text-center py-12",children:[(0,t.jsx)(r.$xp,{className:"mx-auto h-12 w-12 text-gray-400"}),(0,t.jsx)("h3",{className:"mt-2 text-sm font-medium text-gray-900",children:"No companies found"}),(0,t.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:h?"Try adjusting your search terms.":"No companies available."})]}):(0,t.jsx)("div",{className:"space-y-3",children:S.map(e=>(0,t.jsx)("div",{className:"bg-white border border-gray-200 rounded-xl transition-all duration-300 hover:border-gray-300",style:{boxShadow:"0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)"},onMouseEnter:e=>{e.currentTarget.style.transform="scale(1.03)",e.currentTarget.style.boxShadow="0 0 0 rgba(0, 0, 0, 0.15)"},onMouseLeave:e=>{e.currentTarget.style.transform="scale(1)",e.currentTarget.style.boxShadow="0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)"},children:(0,t.jsx)("div",{className:"p-4",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center gap-4 flex-1",children:[(0,t.jsx)("div",{className:"w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center flex-shrink-0",children:(0,t.jsx)(r.$xp,{className:"w-5 h-5 text-white"})}),(0,t.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,t.jsxs)("div",{className:"flex items-center gap-3 mb-1",children:[(0,t.jsx)("h3",{className:"text-base font-semibold text-gray-900 truncate",children:e.companyName}),(0,t.jsx)("span",{className:"inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ".concat("active"===e.status?"bg-green-100 text-green-800":"pending"===e.status?"bg-yellow-100 text-yellow-800":"bg-red-100 text-red-800"),children:e.status})]}),(0,t.jsxs)("div",{className:"flex items-center gap-6 text-sm text-gray-500",children:[(0,t.jsx)("span",{className:"inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-gray-100 text-gray-700",children:e.industry}),(0,t.jsxs)("div",{className:"flex items-center gap-1",children:[(0,t.jsx)(r.Otr,{className:"w-4 h-4"}),(0,t.jsxs)("span",{children:[e.companySize," employees"]})]}),(0,t.jsxs)("div",{className:"flex items-center gap-1",children:[(0,t.jsx)(r.k9l,{className:"w-4 h-4"}),(0,t.jsx)("span",{children:e.location})]})]})]})]}),(0,t.jsx)("div",{className:"flex-shrink-0 ml-6",children:(0,t.jsx)("button",{onClick:()=>w(e._id),className:"px-4 py-2 bg-gradient-to-r from-blue-600 to-purple-600 text-white font-medium rounded-lg hover:shadow-lg transition-all duration-200 hover:-translate-y-0.5 text-sm",style:{background:"linear-gradient(135deg, #2563eb 0%, #8b5cf6 100%)",boxShadow:"0 2px 4px -1px rgba(0, 0, 0, 0.1)",fontFamily:"'SF Pro', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif"},children:"Manage Plans"})})]})})},e._id))}),j&&(0,t.jsx)(m.Z,{isOpen:j,onClose:()=>f(!1),onSuccess:C})]})]})]})}s(17673)},48223:function(e,a,s){"use strict";var t=s(57437),n=s(2265),l=s(47369),r=s(99376),i=s(83337),o=s(70623),c=s(39547),d=s(35389),m=s(95656);let x=()=>/Mobi|Android/i.test(navigator.userAgent);a.Z=e=>{let{children:a}=e,{user:s,loading:u}=(0,l.a)(),h=(0,r.useRouter)(),p=(0,r.usePathname)(),g=(0,i.T)(),[y,j]=(0,n.useState)(!1),f=(0,i.C)(e=>e.user.userProfile);return((0,n.useEffect)(()=>{{let e=localStorage.getItem("userid1")||localStorage.getItem("userId");console.log("USER ID FROM LOCAL STORAGE: ",e),e&&!f.name&&(g((0,o.Iv)(e)),(async()=>{try{await (0,c.M_)(g,e),await (0,c.aK)(g)}catch(e){console.error("Error fetching user data in ProtectedRoute:",e)}})())}},[g,f.name]),(0,n.useEffect)(()=>{console.log("ProtectedRoute useEffect triggered"),console.log("Current user: ",s),console.log("Loading state: ",u),console.log("Current user details: ",f),u||s||(console.log("User not authenticated, redirecting to home"),j(!1),h.push("/")),!u&&f.companyId&&""===f.companyId&&(console.log("Waiting to retrieve company details"),j(!1)),!u&&f.companyId&&""!==f.companyId&&(console.log("User found, rendering children"),j(!0)),x()&&!p.startsWith("/mobile")&&(console.log("Redirecting to mobile version of ".concat(p)),h.push("/mobile".concat(p)))},[s,u,f,h,p]),y)?s?(0,t.jsx)(t.Fragment,{children:a}):null:(0,t.jsx)(m.Z,{sx:{display:"flex",justifyContent:"center",alignItems:"center",height:"100vh",bgcolor:"#f6f8fc"},children:(0,t.jsx)(d.Z,{})})}},18093:function(){},17673:function(){}},function(e){e.O(0,[4793,139,8422,3463,3301,8575,293,9810,187,3145,9932,3919,9129,208,3344,9859,8884,2971,2117,1744],function(){return e(e.s=4776)}),_N_E=e.O()}]);