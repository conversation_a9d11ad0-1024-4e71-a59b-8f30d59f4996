(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5823,7534],{48707:function(e,t,r){Promise.resolve().then(r.bind(r,31922))},40256:function(e,t,r){"use strict";r.d(t,{$R:function(){return d},A_:function(){return l},BO:function(){return a},GH:function(){return u},_n:function(){return n},be:function(){return s},iG:function(){return c},j0:function(){return i}});var o=r(83464);let n="http://localhost:8080",a="<EMAIL>,<EMAIL>,<EMAIL>".split(",").map(e=>e.trim()),s=o.Z.create({baseURL:n});async function l(e,t,r){let o=new URL(r?"".concat(r).concat(e):"".concat(n).concat(e));return t&&Object.keys(t).forEach(e=>o.searchParams.append(e,t[e])),(await s.get(o.toString())).data}async function i(e,t,r){let o=r?"".concat(r).concat(e):"".concat(n).concat(e),a=await s.post(o,t,{headers:{"Content-Type":"application/json"}});return{status:a.status,data:a.data}}async function c(e,t,r){let o=r?"".concat(r).concat(e):"".concat(n).concat(e);console.log("Document upload to: ".concat(o));let a=await s.post(o,t,{headers:{"Content-Type":"multipart/form-data"}});return{status:a.status,data:a.data}}async function d(e,t,r){let o=new URL(r?"".concat(r).concat(e):"".concat(n).concat(e));return t&&Object.keys(t).forEach(e=>o.searchParams.append(e,t[e])),console.log("GET Blob request to: ".concat(o.toString())),(await s.get(o.toString(),{responseType:"blob"})).data}async function u(e,t,r){let o=r?"".concat(r).concat(e):"".concat(n).concat(e),a=await s.put(o,t,{headers:{"Content-Type":"application/json"}});return{status:a.status,data:a.data}}s.interceptors.request.use(e=>{let t=localStorage.getItem("userid1")||localStorage.getItem("userId");return t?e.headers["user-id"]=t:console.warn("No user ID found in localStorage for API request"),e})},24079:function(e,t,r){"use strict";r.d(t,{$:function(){return n}});var o=r(2265);let n=e=>{(0,o.useEffect)(()=>{let t=performance.now(),r=()=>{let r=performance.now()-t;{let t=JSON.parse(localStorage.getItem("ai-enroller-perf")||"{}");t[e]={loadTime:r.toFixed(2),timestamp:new Date().toISOString()},localStorage.setItem("ai-enroller-perf",JSON.stringify(t))}};return"complete"===document.readyState?r():window.addEventListener("load",r),()=>{window.removeEventListener("load",r)}},[e])}},31922:function(e,t,r){"use strict";r.r(t),r.d(t,{default:function(){return g}});var o=r(57437),n=r(2265),a=r(99376),s=r(27648),l=r(18913),i=r(33145),c=r(48223),d=()=>{let e=(0,a.useRouter)();return(0,n.useEffect)(()=>{let t=["/ai-enroller/create-plan","/ai-enroller/plans","/ai-enroller/manage-groups","/ai-enroller/employee-enrol"],r=setTimeout(()=>{t.forEach(t=>{e.prefetch(t)})},100);return["/brea.png"].forEach(e=>{new Image().src=e}),document.fonts&&(document.fonts.load("400 16px SF Pro").catch(()=>{}),document.fonts.load("500 16px SF Pro").catch(()=>{}),document.fonts.load("600 16px SF Pro").catch(()=>{})),()=>{clearTimeout(r)}},[e]),null},u=r(24079),p=r(83337);r(22586);var g=()=>{let e=(0,a.useRouter)(),[t,r]=(0,n.useState)(""),[g,x]=(0,n.useState)(!1),[h,m]=(0,n.useState)(!0),f=(0,p.C)(e=>e.user.userProfile);return((0,u.$)("AI Enroller Dashboard"),(0,n.useEffect)(()=>{{let t=localStorage.getItem("userRole")||"";if(r(t),f&&!f.isAdmin&&!f.isBroker){x(!0),e.replace("/ai-enroller/employee-enrol");return}e.prefetch("/ai-enroller/create-plan"),e.prefetch("/ai-enroller/plans"),e.prefetch("/ai-enroller/manage-groups"),e.prefetch("/ai-enroller/employee-enrol"),"superadmin"===t&&e.prefetch("/ai-enroller/create-carrier")}},[e,f]),f&&!f.isAdmin&&!f.isBroker||g)?(0,o.jsx)(c.Z,{children:(0,o.jsx)("div",{style:{minHeight:"100vh",backgroundColor:"#f9fafb",display:"flex",alignItems:"center",justifyContent:"center",fontFamily:'SF Pro, -apple-system, BlinkMacSystemFont, "SF Pro Display", "Segoe UI", "Roboto", "Helvetica Neue", Arial, sans-serif'},children:(0,o.jsxs)("div",{style:{backgroundColor:"white",borderRadius:"16px",boxShadow:"0 4px 6px rgba(0, 0, 0, 0.1)",padding:"32px",textAlign:"center",maxWidth:"400px",width:"90%"},children:[(0,o.jsx)("div",{style:{width:"48px",height:"48px",backgroundColor:"#7c3aed",borderRadius:"50%",display:"flex",alignItems:"center",justifyContent:"center",margin:"0 auto 16px"},children:(0,o.jsx)(i.default,{src:"/brea.png",alt:"Brea AI Assistant",width:48,height:48,style:{borderRadius:"50%"},priority:!0})}),(0,o.jsx)("h2",{style:{fontSize:"20px",fontWeight:"600",color:"#111827",margin:"0 0 8px 0"},children:"Welcome to AI Enroller"}),(0,o.jsx)("p",{style:{color:"#6b7280",fontSize:"14px",margin:"0 0 16px 0",lineHeight:"1.5"},children:"Taking you to your personalized enrollment experience..."}),(0,o.jsx)("div",{style:{width:"32px",height:"32px",border:"3px solid #e5e7eb",borderTop:"3px solid #7c3aed",borderRadius:"50%",animation:"spin 1s linear infinite",margin:"0 auto"}})]})})}):(0,o.jsx)(c.Z,{children:(0,o.jsxs)("div",{style:{minHeight:"100vh",backgroundColor:"#f9fafb",fontFamily:'SF Pro, -apple-system, BlinkMacSystemFont, "SF Pro Display", "Segoe UI", "Roboto", "Helvetica Neue", Arial, sans-serif'},children:[(0,o.jsx)(d,{}),(0,o.jsxs)("div",{style:{maxWidth:"896px",margin:"0 auto",padding:"32px 24px"},children:[(0,o.jsx)("div",{style:{backgroundColor:"white",borderRadius:"16px",boxShadow:"2px 4px 6px -1px rgba(0, 0, 0, 0.2)",border:"1px solid #e5e7eb",padding:"24px",marginBottom:"32px"},children:(0,o.jsxs)("div",{style:{display:"flex",alignItems:"center",gap:"16px"},children:[(0,o.jsx)("div",{style:{width:"48px",height:"48px",background:"linear-gradient(135deg, #2563eb 0%, #8b5cf6 100%)",borderRadius:"50%",display:"flex",alignItems:"center",justifyContent:"center",flexShrink:0},children:(0,o.jsx)(i.default,{src:"/brea.png",alt:"Brea AI Assistant",width:48,height:48,style:{borderRadius:"50%"},priority:!0,loading:"eager"})}),(0,o.jsxs)("div",{style:{flex:1,minWidth:0},children:[(0,o.jsx)("div",{style:{display:"flex",alignItems:"center",gap:"8px",marginBottom:"8px",flexWrap:"wrap"},children:(0,o.jsxs)("span",{className:"page-title",style:{fontSize:"clamp(16px, 4vw, 18px)",fontFamily:"'SF Pro', 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif",lineHeight:"1.4",wordBreak:"break-word"},children:["Hi ",(()=>{if(null==f?void 0:f.name)return f.name.split(" ")[0];{let e=localStorage.getItem("brokerName");if(e)return e.split(" ")[0]}return"there"})(),"! I'm here to help you manage your insurance plans."]})}),(0,o.jsx)("p",{className:"subtitle-text",style:{fontFamily:"'SF Pro', 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif",margin:0},children:"What would you like to do today?"})]})]})}),(0,o.jsxs)("div",{style:{display:"flex",flexDirection:"column",gap:"16px"},children:[(0,o.jsx)(s.default,{href:"/ai-enroller/create-plan",prefetch:!0,children:(0,o.jsx)("div",{style:{backgroundColor:"white",borderRadius:"12px",border:"1px solid #e5e7eb",padding:"24px",cursor:"pointer",transition:"all 0.3s ease",textDecoration:"none"},onMouseOver:e=>{e.currentTarget.style.boxShadow="0 4px 6px -1px rgba(0, 0, 0, 0.2)",e.currentTarget.style.transform="scale(1.03)"},onMouseOut:e=>{e.currentTarget.style.boxShadow="2px 4px 6px 0 rgba(0, 0, 0, 0.2)",e.currentTarget.style.transform="scale(1)"},children:(0,o.jsxs)("div",{style:{display:"flex",alignItems:"center",gap:"16px"},children:[(0,o.jsx)("div",{style:{width:"48px",height:"48px",backgroundColor:"#eff6ff",borderRadius:"12px",display:"flex",alignItems:"center",justifyContent:"center"},children:(0,o.jsx)(l.r7I,{style:{width:"24px",height:"24px",color:"#3b82f6"}})}),(0,o.jsxs)("div",{children:[(0,o.jsx)("h3",{className:"section-header",style:{margin:"0 0 4px 0"},children:"Create New Plan"}),(0,o.jsx)("p",{className:"body-text",children:"Set up a new insurance plan with AI assistance"})]})]})})}),(0,o.jsx)(s.default,{href:"/ai-enroller/plans",prefetch:!0,children:(0,o.jsx)("div",{style:{backgroundColor:"white",borderRadius:"12px",border:"1px solid #e5e7eb",padding:"24px",cursor:"pointer",transition:"all 0.3s ease",textDecoration:"none"},onMouseOver:e=>{e.currentTarget.style.boxShadow="0 4px 6px -1px rgba(0, 0, 0, 0.2)",e.currentTarget.style.transform="scale(1.03)"},onMouseOut:e=>{e.currentTarget.style.boxShadow="2px 4px 6px 0 rgba(0, 0, 0, 0.2)",e.currentTarget.style.transform="scale(1)"},children:(0,o.jsxs)("div",{style:{display:"flex",alignItems:"center",gap:"16px"},children:[(0,o.jsx)("div",{style:{width:"48px",height:"48px",backgroundColor:"#f0fdf4",borderRadius:"12px",display:"flex",alignItems:"center",justifyContent:"center"},children:(0,o.jsx)(l.vrJ,{style:{width:"24px",height:"24px",color:"#22c55e"}})}),(0,o.jsxs)("div",{children:[(0,o.jsx)("h3",{className:"section-header",style:{margin:"0 0 4px 0"},children:"View All Plans"}),(0,o.jsx)("p",{className:"body-text",children:"Browse, search, and manage your existing plans"})]})]})})}),(0,o.jsx)(s.default,{href:"/ai-enroller/manage-groups",prefetch:!0,children:(0,o.jsx)("div",{style:{backgroundColor:"white",borderRadius:"12px",border:"1px solid #e5e7eb",padding:"24px",cursor:"pointer",transition:"all 0.3s ease",textDecoration:"none"},onMouseOver:e=>{e.currentTarget.style.boxShadow="0 4px 6px -1px rgba(0, 0, 0, 0.2)",e.currentTarget.style.transform="scale(1.03)"},onMouseOut:e=>{e.currentTarget.style.boxShadow="2px 4px 6px 0 rgba(0, 0, 0, 0.2)",e.currentTarget.style.transform="scale(1)"},children:(0,o.jsxs)("div",{style:{display:"flex",alignItems:"center",gap:"16px"},children:[(0,o.jsx)("div",{style:{width:"48px",height:"48px",backgroundColor:"#fff7ed",borderRadius:"12px",display:"flex",alignItems:"center",justifyContent:"center"},children:(0,o.jsx)(l.Otr,{style:{width:"24px",height:"24px",color:"#f97316"}})}),(0,o.jsxs)("div",{children:[(0,o.jsx)("h3",{className:"section-header",style:{margin:"0 0 4px 0"},children:"Manage Group Plans"}),(0,o.jsx)("p",{className:"body-text",children:"Assign plans to employer groups and manage renewals"})]})]})})}),(0,o.jsx)(s.default,{href:"/ai-enroller/employee-enrol",prefetch:!0,children:(0,o.jsx)("div",{style:{backgroundColor:"white",borderRadius:"12px",border:"1px solid #e5e7eb",padding:"24px",cursor:"pointer",transition:"all 0.3s ease",textDecoration:"none"},onMouseOver:e=>{e.currentTarget.style.boxShadow="0 4px 6px -1px rgba(0, 0, 0, 0.2)",e.currentTarget.style.transform="scale(1.03)"},onMouseOut:e=>{e.currentTarget.style.boxShadow="2px 4px 6px 0 rgba(0, 0, 0, 0.2)",e.currentTarget.style.transform="scale(1)"},children:(0,o.jsxs)("div",{style:{display:"flex",alignItems:"center",gap:"16px"},children:[(0,o.jsx)("div",{style:{width:"48px",height:"48px",backgroundColor:"#ede9fe",borderRadius:"12px",display:"flex",alignItems:"center",justifyContent:"center"},children:(0,o.jsx)(l.TSD,{style:{width:"24px",height:"24px",color:"#7c3aed"}})}),(0,o.jsxs)("div",{children:[(0,o.jsx)("h3",{className:"section-header",style:{margin:"0 0 4px 0"},children:"Employee Enrollment"}),(0,o.jsx)("p",{className:"body-text",children:"AI-powered benefits enrollment for employees"})]})]})})}),"superadmin"===t&&(0,o.jsx)(s.default,{href:"/ai-enroller/create-carrier",prefetch:!0,children:(0,o.jsx)("div",{style:{backgroundColor:"white",borderRadius:"12px",border:"1px solid #e5e7eb",padding:"24px",cursor:"pointer",transition:"box-shadow 0.2s ease",textDecoration:"none"},onMouseOver:e=>e.currentTarget.style.boxShadow="0 4px 6px -1px rgba(0, 0, 0, 0.1)",onMouseOut:e=>e.currentTarget.style.boxShadow="0 1px 3px 0 rgba(0, 0, 0, 0.1)",children:(0,o.jsxs)("div",{style:{display:"flex",alignItems:"center",gap:"16px"},children:[(0,o.jsx)("div",{style:{width:"48px",height:"48px",backgroundColor:"#fed7aa",borderRadius:"12px",display:"flex",alignItems:"center",justifyContent:"center"},children:(0,o.jsx)(l.$xp,{style:{width:"24px",height:"24px",color:"#ea580c"}})}),(0,o.jsxs)("div",{children:[(0,o.jsx)("h3",{className:"section-header",style:{margin:"0 0 4px 0"},children:"Create Carrier"}),(0,o.jsx)("p",{className:"body-text",style:{margin:"0 0 4px 0"},children:"Add new insurance carriers to the system"}),(0,o.jsx)("span",{style:{display:"inline-flex",alignItems:"center",padding:"2px 8px",borderRadius:"9999px",fontSize:"12px",fontWeight:"500",backgroundColor:"#fed7aa",color:"#9a3412"},children:"SuperAdmin Only"})]})]})})})]})]})]})})}},47369:function(e,t,r){"use strict";r.d(t,{AuthProvider:function(){return h},a:function(){return m}});var o=r(57437),n=r(73143),a=r(2265),s=r(28411),l=r(43301),i=r(71986),c=r(84392);let d={auth:{clientId:"08e8620a-5979-4a37-b279-b2a92a75f515",authority:"https://login.microsoftonline.com/ca41443d-acdd-4223-9c81-dcaeb58b3406",redirectUri:window.location.hostname.includes("test.benosphere.com")?"https://test.benosphere.com/teams-landing":"https://app.benosphere.com/teams-landing",navigateToLoginRequestUrl:!1},cache:{cacheLocation:"sessionStorage",storeAuthStateInCookie:!1},system:{allowRedirectInIframe:!0,loggerOptions:{loggerCallback:(e,t)=>{console.log(t)},logLevel:i.i.Verbose}}},u=new c.Lx(d);var p=r(77534),g=r(99376);let x=(0,a.createContext)(void 0),h=e=>{let{children:t}=e,[r,i]=(0,a.useState)(null),[c,d]=(0,a.useState)(!0),h=(0,g.useRouter)(),m=()=>{Object.keys(localStorage).forEach(e=>{localStorage.removeItem(e)}),localStorage.removeItem("userid1"),localStorage.removeItem("userEmail1"),localStorage.removeItem("isTeamsApp1"),localStorage.removeItem("companyId1"),localStorage.removeItem("firstTimeLogin1"),localStorage.removeItem("wellness_results"),localStorage.removeItem("wellness_user_answers"),console.log("All localStorage items cleared")},f=()=>{document.cookie.split(";").forEach(e=>{let t=e.split("=")[0].trim();document.cookie="".concat(t,"=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;")}),console.log("All cookies cleared")};return(0,a.useEffect)(()=>{let e=(0,l.Aj)(s.I,e=>{if(e)console.log("Firebase user exists:",e),i(e);else{let e=localStorage.getItem("ssoDone1"),t=localStorage.getItem("userid1");console.log("isSSODone",e),console.log("userid1",t),n.j2().then(async()=>{var e,t,r,o,a;let s=await n.fw();console.log("Current user teams context:",null===(e=s.user)||void 0===e?void 0:e.loginHint);let l=null===(t=s.user)||void 0===t?void 0:t.loginHint,c=null===(o=s.user)||void 0===o?void 0:null===(r=o.tenant)||void 0===r?void 0:r.id;localStorage.setItem("userEmail1",l),localStorage.setItem("isTeamsApp1","true");let d=await (0,p.N8)(l,c);if("login_user"===d.data){console.log("Onboarding successful:",d);let e=d.userId,t=d.companyId;localStorage.setItem("userid1",e),localStorage.setItem("companyId1",t),localStorage.setItem("ssoDone1","true"),i(null===(a=s.user)||void 0===a?void 0:a.loginHint),h.push("/dashboard")}else h.push("/teams-landing")})}d(!1)});return()=>e()},[]),(0,o.jsx)(x.Provider,{value:{user:r,loading:c,logout:()=>{(0,l.w7)(s.I).then(()=>{console.log("Firebase user signed out"),i(null),m(),f(),h.push("/login")}).catch(e=>{console.error("Error signing out: ",e)}),u.logoutRedirect().catch(e=>{console.error("Error signing out from Microsoft: ",e)})},setUser:i},children:t})},m=()=>{let e=(0,a.useContext)(x);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}},48223:function(e,t,r){"use strict";var o=r(57437),n=r(2265),a=r(47369),s=r(99376),l=r(83337),i=r(70623),c=r(39547),d=r(35389),u=r(95656);let p=()=>/Mobi|Android/i.test(navigator.userAgent);t.Z=e=>{let{children:t}=e,{user:r,loading:g}=(0,a.a)(),x=(0,s.useRouter)(),h=(0,s.usePathname)(),m=(0,l.T)(),[f,y]=(0,n.useState)(!1),b=(0,l.C)(e=>e.user.userProfile);return((0,n.useEffect)(()=>{{let e=localStorage.getItem("userid1")||localStorage.getItem("userId");console.log("USER ID FROM LOCAL STORAGE: ",e),e&&!b.name&&(m((0,i.Iv)(e)),(async()=>{try{await (0,c.M_)(m,e),await (0,c.aK)(m)}catch(e){console.error("Error fetching user data in ProtectedRoute:",e)}})())}},[m,b.name]),(0,n.useEffect)(()=>{console.log("ProtectedRoute useEffect triggered"),console.log("Current user: ",r),console.log("Loading state: ",g),console.log("Current user details: ",b),g||r||(console.log("User not authenticated, redirecting to home"),y(!1),x.push("/")),!g&&b.companyId&&""===b.companyId&&(console.log("Waiting to retrieve company details"),y(!1)),!g&&b.companyId&&""!==b.companyId&&(console.log("User found, rendering children"),y(!0)),p()&&!h.startsWith("/mobile")&&(console.log("Redirecting to mobile version of ".concat(h)),x.push("/mobile".concat(h)))},[r,g,b,x,h]),f)?r?(0,o.jsx)(o.Fragment,{children:t}):null:(0,o.jsx)(u.Z,{sx:{display:"flex",justifyContent:"center",alignItems:"center",height:"100vh",bgcolor:"#f6f8fc"},children:(0,o.jsx)(d.Z,{})})}},77534:function(e,t,r){"use strict";r.d(t,{Ig:function(){return l},N8:function(){return s},lY:function(){return n},selfOnboard:function(){return a}});var o=r(40256);async function n(e){return(await (0,o.j0)("/auth/parse-params",{link:e})).data}async function a(e){return(await (0,o.j0)("/user/self-onboard",{userEmail:e})).data.data}async function s(e,t){return(await (0,o.j0)("/teams/user/self-onboard",{userEmail:e,tenantId:t})).data}async function l(e,t){let r=await (0,o.j0)("/employee/onboard",{companyId:e,userId:t});return console.log("Response from onboardEmployee:",r),r.data}},28411:function(e,t,r){"use strict";r.d(t,{I:function(){return l}});var o=r(738),n=r(43301);let a=function(){{let e=window.location.hostname;if(e.includes("test.benosphere.com")||"localhost"===e)return{apiKey:"AIzaSyCPGurROOIHelcNrg3KK3UZpT5NY_v33cw",authDomain:"qharmony-test.firebaseapp.com",projectId:"qharmony-test",storageBucket:"qharmony-test.firebasestorage.app",messagingSenderId:"1017404738235",appId:"1:1017404738235:web:d0566182eb575065e3379e"}}return{apiKey:"AIzaSyAOalm98qF_u74s3qQlIqQ5A6IpWyd0wKA",authDomain:"qharmony-dev.firebaseapp.com",projectId:"qharmony-dev",storageBucket:"qharmony-dev.appspot.com",messagingSenderId:"756187162353",appId:"1:756187162353:web:3fc7d63dee1c57bc9d6b50"}}(),s=(0,o.C6)().length?(0,o.C6)()[0]:(0,o.ZF)(a),l=(0,n.v0)(s)},22586:function(){}},function(e){e.O(0,[1784,7511,139,8422,3463,3301,8575,293,9810,3145,9932,9129,7648,2296,3344,2971,2117,1744],function(){return e(e.s=48707)}),_N_E=e.O()}]);