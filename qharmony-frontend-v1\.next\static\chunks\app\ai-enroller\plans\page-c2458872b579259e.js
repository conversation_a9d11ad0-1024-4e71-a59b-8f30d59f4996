(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2373],{26966:function(e,a,l){Promise.resolve().then(l.bind(l,5300))},5300:function(e,a,l){"use strict";l.r(a);var s=l(57437),n=l(2265),t=l(99376),o=l(18913),r=l(84154),i=l(48223),c=l(63055),d=l(57804);l(97785);var u=l(61103),h=l(40256),m=l(45244),p=l(99859);(0,u.bR)(),a.default=()=>{var e,a;let l=(0,t.useRouter)(),[u,v]=(0,n.useState)([]),[x,j]=(0,n.useState)(""),[g,N]=(0,n.useState)("all"),[b,f]=(0,n.useState)(null),[y,C]=(0,n.useState)(!0),[S,P]=(0,n.useState)(null),[w,E]=(0,n.useState)([]),[k,D]=(0,n.useState)(!1),[A,I]=(0,n.useState)(!1),[z,_]=(0,n.useState)(null),[L,F]=(0,n.useState)(!1),[R,M]=(0,n.useState)(1),[T]=(0,n.useState)(10),[O,q]=(0,n.useState)(!1),[U,W]=(0,n.useState)(null),[Z,B]=(0,n.useState)(!1),[V,Y]=(0,n.useState)(null),[K,G]=(0,n.useState)(!1),[H,J]=(0,n.useState)(null);(0,n.useEffect)(()=>{console.log("\uD83D\uDD0D localStorage debug:",{userid1:localStorage.getItem("userid1"),userId:localStorage.getItem("userId"),allKeys:Object.keys(localStorage)}),Q()},[]);let Q=async()=>{try{C(!0),P(null);let[e,a]=await Promise.all([(0,c.qY)(),(0,c.ie)()]);if(e.success&&e.data&&e.data.plans){console.log("\uD83D\uDCE6 Plans loaded:",e.data.plans),console.log("\uD83D\uDCCA First plan structure:",e.data.plans[0]),v(e.data.plans);let a=e.data.plans.length;e.data.plans.filter(e=>"Active"===e.status).length;let l=e.data.plans.filter(e=>{if(!e.createdAt)return!1;let a=new Date(e.createdAt),l=new Date;return l.setDate(l.getDate()-7),a>l}),s=e.data.plans.reduce((e,a)=>{let l=a.status||"Unknown";return e[l]=(e[l]||0)+1,e},{});f({totalPlans:a,plansByStatus:s,recentPlans:l})}else P(e.error||"Failed to load plans");a.success&&a.data&&E(a.data)}catch(e){P("Failed to load plans"),console.error("Error loading plans:",e)}finally{C(!1)}},X=(u||[]).filter(e=>{let a=(e.planName||"").toLowerCase().includes(x.toLowerCase())||(e.planCode||"").toLowerCase().includes(x.toLowerCase())||(e.coverageSubTypes||[]).some(e=>e.toLowerCase().includes(x.toLowerCase())),l="all"===g||(e.planType||"").toLowerCase()===g.toLowerCase()||(e.status||"").toLowerCase()===g.toLowerCase();return a&&l}),$=Math.ceil(X.length/T),ee=(R-1)*T,ea=ee+T,el=X.slice(ee,ea),es=e=>{M(e)},en=(e,a,l)=>{Y({title:e,message:a,onClose:l}),B(!0)},et=(e,a,l,s)=>{W({title:e,message:a,onConfirm:l,onCancel:s}),q(!0)},eo=()=>{B(!1),(null==V?void 0:V.onClose)&&V.onClose(),Y(null)},er=()=>{q(!1),(null==U?void 0:U.onCancel)&&U.onCancel(),W(null)},ei=(e,a,l,s)=>{J({title:e,fields:a,onSubmit:l,onCancel:s}),G(!0)},ec=()=>{G(!1),(null==H?void 0:H.onCancel)&&H.onCancel(),J(null)},ed=async e=>{console.log("\uD83D\uDD27 handleEditPlan called with planId:",e);try{let a=(await h.be.get("/api/pre-enrollment/plans/".concat(e,"/can-edit"))).data;if(a.canEdit){let a=u.find(a=>a._id===e);a?(_(a),I(!0)):en("Error","Plan not found")}else en("Cannot Edit Plan",a.message)}catch(e){console.error("Error checking plan editability:",e),en("Error","Error checking plan editability")}},eu=async e=>{console.log("\uD83D\uDD27 handleCopyPlan called with planId:",e);try{let a=u.find(a=>a._id===e);if(!a){en("Error","Plan not found");return}ei("Copy Plan",[{name:"planName",label:"Plan Name",placeholder:"Enter name for the copied plan",defaultValue:"".concat(a.planName," (Copy)"),required:!0},{name:"planCode",label:"Plan Code (Optional)",placeholder:"Enter plan code for the copied plan",defaultValue:"".concat(a.planCode||"","-COPY"),required:!1}],async a=>{let l=a.planName,s=a.planCode;try{await h.be.post("/api/pre-enrollment/plans/".concat(e,"/duplicate"),{planName:l,planCode:s||void 0}),en("Success","Plan copied successfully!"),Q()}catch(a){var n,t,o,r;console.error("Error copying plan:",a);let e=(null===(t=a.response)||void 0===t?void 0:null===(n=t.data)||void 0===n?void 0:n.error)||(null===(r=a.response)||void 0===r?void 0:null===(o=r.data)||void 0===o?void 0:o.message)||a.message||"Error copying plan";en("Error","Error copying plan: ".concat(e))}})}catch(e){console.error("Error copying plan:",e),en("Error","Error copying plan")}},eh=async e=>{var a,l,s,n,t;try{let l=(await h.be.get("/api/pre-enrollment/plans/".concat(e,"/can-delete"))).data;if(l.canDelete)et("Delete Plan","Are you sure you want to delete this plan? This action cannot be undone.",async()=>{try{await h.be.delete("/api/pre-enrollment/plans/".concat(e)),en("Success","Plan deleted successfully!"),Q()}catch(t){var a,l,s,n;console.error("Error deleting plan:",t);let e=(null===(l=t.response)||void 0===l?void 0:null===(a=l.data)||void 0===a?void 0:a.error)||(null===(n=t.response)||void 0===n?void 0:null===(s=n.data)||void 0===s?void 0:s.message)||t.message||"Error deleting plan. Please try again.";en("Error","Error deleting plan: ".concat(e))}});else{let s=(await h.be.get("/api/pre-enrollment/plans/".concat(e,"/dependent-assignments"))).data,n=(null===(a=s.dependentAssignments)||void 0===a?void 0:a.map(e=>"Assignment ".concat(e._id)).join(", "))||"Unknown assignments";en("Cannot Delete Plan","".concat(l.message,"\n\nThis plan is referenced by ").concat(s.count," assignment(s):\n").concat(n))}}catch(a){console.error("Error deleting plan:",a);let e=(null===(s=a.response)||void 0===s?void 0:null===(l=s.data)||void 0===l?void 0:l.error)||(null===(t=a.response)||void 0===t?void 0:null===(n=t.data)||void 0===n?void 0:n.message)||a.message||"Error deleting plan";en("Error","Error deleting plan: ".concat(e))}},em=async e=>{console.log("\uD83D\uDD27 handleActivatePlan called with planId:",e);try{await h.be.post("/api/pre-enrollment/plans/".concat(e,"/activate")),en("Success","Plan activated successfully!"),Q()}catch(t){var a,l,s,n;console.error("Error activating plan:",t);let e=(null===(l=t.response)||void 0===l?void 0:null===(a=l.data)||void 0===a?void 0:a.error)||(null===(n=t.response)||void 0===n?void 0:null===(s=n.data)||void 0===s?void 0:s.message)||t.message||"Error activating plan. Please try again.";en("Error","Error activating plan: ".concat(e))}},ep=async e=>{try{et("Convert to Draft","Are you sure you want to convert this plan to draft? It will no longer be available for new assignments.",async()=>{await h.be.post("/api/pre-enrollment/plans/".concat(e,"/convert-to-draft")),en("Success","Plan converted to draft successfully!"),Q()})}catch(t){var a,l,s,n;console.error("Error converting plan to draft:",t);let e=(null===(l=t.response)||void 0===l?void 0:null===(a=l.data)||void 0===a?void 0:a.error)||(null===(n=t.response)||void 0===n?void 0:null===(s=n.data)||void 0===s?void 0:s.message)||t.message||"Error converting plan to draft. Please try again.";en("Error","Error converting plan to draft: ".concat(e))}},ev=()=>{I(!1),_(null)};return(0,s.jsxs)(i.Z,{children:[(0,s.jsx)(p.Z,{}),(0,s.jsx)("div",{className:"plans-wrapper",children:(0,s.jsxs)("div",{className:"plans-page",children:[(0,s.jsxs)("div",{className:"plans-header",children:[(0,s.jsx)("div",{className:"header-left",children:(0,s.jsxs)("div",{className:"left-align-wrapper",children:[(0,s.jsx)("div",{className:"title-icon",children:(0,s.jsx)(o.vrJ,{size:24,style:{color:"#ffffff"}})}),(0,s.jsxs)("div",{className:"title-content",children:[(0,s.jsx)("h1",{children:"Manage Plans"}),(0,s.jsx)("p",{children:"Manage and view all insurance plans"})]})]})}),(0,s.jsxs)("div",{className:"header-actions",children:[(0,s.jsxs)("button",{className:"create-btn",onClick:()=>{_(null),I(!0)},children:[(0,s.jsx)(o.r7I,{size:16}),"Create New Plan"]}),(0,s.jsxs)("button",{className:"ask-questions-btn",onClick:()=>F(!0),children:[(0,s.jsx)(r.$Rt,{size:16}),"Ask Questions"]}),(0,s.jsxs)("button",{className:"dashboard-btn",onClick:()=>l.push("/ai-enroller"),children:[(0,s.jsx)(r.s0d,{size:16}),"Dashboard"]})]})]}),b&&(0,s.jsxs)("div",{className:"stats-grid",children:[(0,s.jsxs)("div",{className:"stat-card blue",children:[(0,s.jsxs)("div",{className:"stat-content",children:[(0,s.jsx)("div",{className:"stat-label",children:"Total Plans"}),(0,s.jsx)("div",{className:"stat-number",children:b.totalPlans})]}),(0,s.jsx)("div",{className:"stat-icon blue",children:(0,s.jsx)(r.s0d,{size:24})})]}),(0,s.jsxs)("div",{className:"stat-card green",children:[(0,s.jsxs)("div",{className:"stat-content",children:[(0,s.jsx)("div",{className:"stat-label",children:"Active Plans"}),(0,s.jsx)("div",{className:"stat-number",children:(null===(e=b.plansByStatus)||void 0===e?void 0:e.Active)||0})]}),(0,s.jsx)("div",{className:"stat-icon green",children:(0,s.jsx)(r.euf,{size:24})})]}),(0,s.jsxs)("div",{className:"stat-card orange",children:[(0,s.jsxs)("div",{className:"stat-content",children:[(0,s.jsx)("div",{className:"stat-label",children:"Recent Plans"}),(0,s.jsx)("div",{className:"stat-number",children:(null===(a=b.recentPlans)||void 0===a?void 0:a.length)||0})]}),(0,s.jsx)("div",{className:"stat-icon orange",children:(0,s.jsx)(r.qXL,{size:24})})]})]}),(0,s.jsxs)("div",{className:"search-filter-section",children:[(0,s.jsxs)("div",{className:"filter-icon",children:[(0,s.jsx)(o.O6C,{size:16}),(0,s.jsx)("span",{children:"Search & Filter"})]}),(0,s.jsxs)("div",{className:"search-controls",children:[(0,s.jsx)("input",{type:"text",placeholder:"Search by plan name, code, or carrier type...",value:x,onChange:e=>j(e.target.value),className:"search-input"}),(0,s.jsxs)("select",{className:"status-filter",value:g,onChange:e=>N(e.target.value),children:[(0,s.jsx)("option",{value:"all",children:"All Statuses"}),(0,s.jsx)("option",{value:"active",children:"Active"}),(0,s.jsx)("option",{value:"inactive",children:"Inactive"}),(0,s.jsx)("option",{value:"draft",children:"Draft"}),(0,s.jsx)("option",{value:"template",children:"Template"}),(0,s.jsx)("option",{value:"archived",children:"Archived"})]}),(0,s.jsxs)("select",{className:"carrier-filter",children:[(0,s.jsx)("option",{value:"all",children:"All Carriers"}),w.map(e=>(0,s.jsx)("option",{value:e._id,children:e.carrierName},e._id))]}),(0,s.jsx)("button",{className:"clear-filters-btn",onClick:()=>{j(""),N("all"),M(1)},children:"Clear Filters"})]}),(0,s.jsxs)("div",{className:"results-count",children:["Showing ",X.length," of ",u.length," plans"]})]}),y&&(0,s.jsxs)("div",{className:"loading-state",children:[(0,s.jsx)("div",{className:"loading-spinner"}),(0,s.jsx)("p",{children:"Loading plans..."})]}),S&&(0,s.jsxs)("div",{className:"error-state",children:[(0,s.jsxs)("p",{children:["Error: ",S]}),(0,s.jsx)("button",{onClick:Q,className:"retry-btn",children:"Retry"})]}),!y&&!S&&(0,s.jsx)("div",{className:"plans-table-container",children:0===X.length?(0,s.jsxs)("div",{className:"empty-state",children:[(0,s.jsx)(r.euf,{size:48}),(0,s.jsx)("h3",{children:"No Plans Found"}),(0,s.jsx)("p",{children:0===u.length?"You haven't created any plans yet. Create your first plan to get started.":"No plans match your search criteria. Try adjusting your filters."}),(0,s.jsxs)("button",{className:"create-first-plan-btn",onClick:()=>l.push("/ai-enroller/create-plan"),children:[(0,s.jsx)(o.r7I,{size:16}),"Create Your First Plan"]})]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{className:"table-header",children:(0,s.jsx)("h3",{children:"Manage Plans"})}),(0,s.jsx)("div",{className:"table-wrapper",children:(0,s.jsxs)("table",{className:"plans-table",children:[(0,s.jsx)("thead",{children:(0,s.jsxs)("tr",{children:[(0,s.jsx)("th",{children:"Plan Name"}),(0,s.jsx)("th",{children:"Plan Code"}),(0,s.jsx)("th",{children:"Coverage Type"}),(0,s.jsx)("th",{children:"Status"}),(0,s.jsx)("th",{children:"Actions"})]})}),(0,s.jsx)("tbody",{children:el.map(e=>{var a,l,n;return console.log("\uD83D\uDD0D Rendering plan:",e._id,e.planName),(0,s.jsxs)("tr",{children:[(0,s.jsx)("td",{className:"plan-name-cell",children:(0,s.jsx)("div",{className:"plan-name",children:e.planName})}),(0,s.jsx)("td",{className:"plan-code-cell",children:(0,s.jsx)("span",{className:"plan-code-badge",children:e.planCode})}),(0,s.jsx)("td",{className:"carrier-type-cell",children:(0,s.jsx)("span",{className:"carrier-type-badge ".concat(null===(a=(null===(l=e.coverageSubTypes)||void 0===l?void 0:l[0])||e.coverageType)||void 0===a?void 0:a.toLowerCase().replace(" ","-")),children:(null===(n=e.coverageSubTypes)||void 0===n?void 0:n[0])||e.coverageType})}),(0,s.jsx)("td",{className:"status-cell",children:(0,s.jsx)("span",{className:"status-badge ".concat((e.status||"unknown").toLowerCase()),children:e.status||"Unknown"})}),(0,s.jsx)("td",{className:"actions-cell",children:(0,s.jsxs)("div",{className:"action-buttons",children:[(0,s.jsx)("button",{className:"action-btn edit",onClick:()=>ed(e._id),title:"Edit Plan",children:(0,s.jsx)(o._vs,{size:16})}),(0,s.jsx)("button",{className:"action-btn copy",onClick:()=>eu(e._id),title:"Copy Plan",children:(0,s.jsx)(o.GS5,{size:16})}),"Active"===e.status?(0,s.jsx)("button",{className:"action-btn deactivate",onClick:()=>ep(e._id),title:"Convert to Draft",children:(0,s.jsx)(o.hvF,{size:16})}):(0,s.jsx)("button",{className:"action-btn activate",onClick:()=>em(e._id),title:"Activate Plan",children:(0,s.jsx)(o.aL1,{size:16})}),(0,s.jsx)("button",{className:"action-btn delete",onClick:()=>eh(e._id),title:"Delete Plan",children:(0,s.jsx)(o.Bhs,{size:16})})]})})]},e._id)})})]})}),$>1&&(0,s.jsxs)("div",{className:"pagination-container",children:[(0,s.jsxs)("div",{className:"pagination-info",children:["Showing ",ee+1,"-",Math.min(ea,X.length)," of ",X.length," plans"]}),(0,s.jsxs)("div",{className:"pagination-controls",children:[(0,s.jsx)("button",{className:"pagination-btn",onClick:()=>es(R-1),disabled:1===R,children:"Previous"}),Array.from({length:$},(e,a)=>a+1).map(e=>(0,s.jsx)("button",{className:"pagination-btn ".concat(e===R?"active":""),onClick:()=>es(e),children:e},e)),(0,s.jsx)("button",{className:"pagination-btn",onClick:()=>es(R+1),disabled:R===$,children:"Next"})]})]})]})}),A&&(0,s.jsx)("div",{className:"modal-overlay",onClick:ev,children:(0,s.jsxs)("div",{className:"modal-content plan-modal-content",onClick:e=>e.stopPropagation(),children:[(0,s.jsxs)("div",{className:"modal-header",children:[(0,s.jsx)("h2",{children:z?"Edit Plan":"Create New Plan"}),(0,s.jsx)("button",{className:"modal-close",onClick:ev,children:(0,s.jsx)(o.fMW,{size:20})})]}),(0,s.jsx)("div",{className:"modal-body",children:(0,s.jsx)(d.Z,{initialData:z,onSubmit:e=>{I(!1),_(null),Q()},onCancel:ev,isModal:!0,existingCarriers:w})})]})}),(0,s.jsx)(m.Z,{isOpen:L,onClose:()=>F(!1)}),Z&&V&&(0,s.jsx)("div",{className:"modal-overlay",onClick:eo,children:(0,s.jsxs)("div",{className:"modal-content",onClick:e=>e.stopPropagation(),children:[(0,s.jsxs)("div",{className:"modal-header",children:[(0,s.jsx)("h2",{children:V.title}),(0,s.jsx)("button",{className:"modal-close",onClick:eo,children:(0,s.jsx)(o.fMW,{size:20})})]}),(0,s.jsx)("div",{className:"modal-body",children:(0,s.jsx)("p",{style:{whiteSpace:"pre-line"},children:V.message})}),(0,s.jsx)("div",{className:"modal-footer",children:(0,s.jsx)("button",{className:"modal-btn primary",onClick:eo,children:"OK"})})]})}),O&&U&&(0,s.jsx)("div",{className:"modal-overlay",onClick:er,children:(0,s.jsxs)("div",{className:"modal-content",onClick:e=>e.stopPropagation(),children:[(0,s.jsxs)("div",{className:"modal-header",children:[(0,s.jsx)("h2",{children:U.title}),(0,s.jsx)("button",{className:"modal-close",onClick:er,children:(0,s.jsx)(o.fMW,{size:20})})]}),(0,s.jsx)("div",{className:"modal-body",children:(0,s.jsx)("p",{style:{whiteSpace:"pre-line"},children:U.message})}),(0,s.jsxs)("div",{className:"modal-footer",children:[(0,s.jsx)("button",{className:"modal-btn secondary",onClick:er,children:"Cancel"}),(0,s.jsx)("button",{className:"modal-btn primary",onClick:()=>{(null==U?void 0:U.onConfirm)&&U.onConfirm(),er()},children:"Confirm"})]})]})}),K&&H&&(0,s.jsx)("div",{className:"modal-overlay",onClick:ec,children:(0,s.jsxs)("div",{className:"modal-content",onClick:e=>e.stopPropagation(),children:[(0,s.jsxs)("div",{className:"modal-header",children:[(0,s.jsx)("h2",{children:H.title}),(0,s.jsx)("button",{className:"modal-close",onClick:ec,children:(0,s.jsx)(o.fMW,{size:20})})]}),(0,s.jsxs)("form",{onSubmit:e=>{e.preventDefault();let a=new FormData(e.target),l={};H.fields.forEach(e=>{l[e.name]=a.get(e.name)||""}),H.onSubmit(l),ec()},children:[(0,s.jsx)("div",{className:"modal-body",children:H.fields.map(e=>(0,s.jsxs)("div",{className:"form-group",style:{marginBottom:"1rem"},children:[(0,s.jsxs)("label",{htmlFor:e.name,style:{display:"block",marginBottom:"0.5rem",fontSize:"14px",lineHeight:"21px",fontWeight:"500",color:"#374151"},children:[e.label,e.required&&(0,s.jsx)("span",{style:{color:"#dc2626"},children:"*"})]}),(0,s.jsx)("input",{type:"text",id:e.name,name:e.name,placeholder:e.placeholder,defaultValue:e.defaultValue,required:e.required,style:{width:"100%",padding:"0.75rem",border:"1px solid #d1d5db",borderRadius:"0.5rem",fontSize:"14px",lineHeight:"21px",fontFamily:"'SF Pro', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif"}})]},e.name))}),(0,s.jsxs)("div",{className:"modal-footer",children:[(0,s.jsx)("button",{type:"button",className:"modal-btn secondary",onClick:ec,children:"Cancel"}),(0,s.jsx)("button",{type:"submit",className:"modal-btn primary",children:"Submit"})]})]})]})})]})})]})}},48223:function(e,a,l){"use strict";var s=l(57437),n=l(2265),t=l(47369),o=l(99376),r=l(83337),i=l(70623),c=l(39547),d=l(35389),u=l(95656);let h=()=>/Mobi|Android/i.test(navigator.userAgent);a.Z=e=>{let{children:a}=e,{user:l,loading:m}=(0,t.a)(),p=(0,o.useRouter)(),v=(0,o.usePathname)(),x=(0,r.T)(),[j,g]=(0,n.useState)(!1),N=(0,r.C)(e=>e.user.userProfile);return((0,n.useEffect)(()=>{{let e=localStorage.getItem("userid1")||localStorage.getItem("userId");console.log("USER ID FROM LOCAL STORAGE: ",e),e&&!N.name&&(x((0,i.Iv)(e)),(async()=>{try{await (0,c.M_)(x,e),await (0,c.aK)(x)}catch(e){console.error("Error fetching user data in ProtectedRoute:",e)}})())}},[x,N.name]),(0,n.useEffect)(()=>{console.log("ProtectedRoute useEffect triggered"),console.log("Current user: ",l),console.log("Loading state: ",m),console.log("Current user details: ",N),m||l||(console.log("User not authenticated, redirecting to home"),g(!1),p.push("/")),!m&&N.companyId&&""===N.companyId&&(console.log("Waiting to retrieve company details"),g(!1)),!m&&N.companyId&&""!==N.companyId&&(console.log("User found, rendering children"),g(!0)),h()&&!v.startsWith("/mobile")&&(console.log("Redirecting to mobile version of ".concat(v)),p.push("/mobile".concat(v)))},[l,m,N,p,v]),j)?l?(0,s.jsx)(s.Fragment,{children:a}):null:(0,s.jsx)(u.Z,{sx:{display:"flex",justifyContent:"center",alignItems:"center",height:"100vh",bgcolor:"#f6f8fc"},children:(0,s.jsx)(d.Z,{})})}},97785:function(){}},function(e){e.O(0,[1424,1750,139,8422,522,3463,3301,8575,8685,187,1423,9932,3919,9129,208,3344,9859,7804,5244,2971,2117,1744],function(){return e(e.s=26966)}),_N_E=e.O()}]);