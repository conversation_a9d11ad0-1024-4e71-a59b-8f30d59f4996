(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6349],{36208:function(e,t,s){Promise.resolve().then(s.bind(s,66944))},99376:function(e,t,s){"use strict";var a=s(35475);s.o(a,"useParams")&&s.d(t,{useParams:function(){return a.useParams}}),s.o(a,"usePathname")&&s.d(t,{usePathname:function(){return a.usePathname}}),s.o(a,"useRouter")&&s.d(t,{useRouter:function(){return a.useRouter}}),s.o(a,"useSearchParams")&&s.d(t,{useSearchParams:function(){return a.useSearchParams}})},66944:function(e,t,s){"use strict";s.r(t);var a=s(57437),i=s(2265),r=s(99376),n=s(18913);s(32242),s(51980),s(2536),t.default=()=>{let e=(0,r.useParams)(),t=(0,r.useRouter)(),[s,l]=(0,i.useState)(4),c="TechCorp Solutions",[o,d]=(0,i.useState)([{id:"sbc",title:"Summary of Benefits and Coverage (SBC)",description:"Upload updated plan documents for TechCorp Solutions. These documents will be tagged with the new plan year and made available to HR and employees.",required:!0,files:[],placeholder:"No summary of benefits and coverage (sbc) uploaded yet"},{id:"plan-docs",title:"Plan Documents",description:"",required:!1,files:[],placeholder:"No plan documents uploaded yet"},{id:"rate-sheets",title:"Rate Sheets",description:"",required:!1,files:[],placeholder:"No rate sheets uploaded yet"},{id:"carrier-contracts",title:"Carrier Contracts",description:"",required:!1,files:[],placeholder:"No carrier contracts uploaded yet"},{id:"compliance-docs",title:"Compliance Documents",description:"",required:!1,files:[],placeholder:"No compliance documents uploaded yet"}]),u=[{number:1,title:"Review Current Plans",subtitle:"View existing benefit plans",active:!1,completed:!0},{number:2,title:"Renewal Options",subtitle:"Choose renewal type",active:!1,completed:!0},{number:3,title:"Plan Configuration",subtitle:"Set dates and modifications",active:!1,completed:!0},{number:4,title:"Document Upload",subtitle:"Upload plan documents",active:4===s},{number:5,title:"Validation",subtitle:"Review and validate setup",active:!1},{number:6,title:"Finalize",subtitle:"Complete renewal process",active:!1},{number:7,title:"Export",subtitle:"Download and share data",active:!1}],p=(e,t)=>{t&&d(s=>s.map(s=>s.id===e?{...s,files:[...s.files,...Array.from(t)]}:s))};return(0,a.jsxs)("div",{className:"plan-renewal-detail",children:[(0,a.jsxs)("div",{className:"detail-header",children:[(0,a.jsxs)("button",{className:"back-btn",onClick:()=>t.push("/ai-enroller/renewal"),children:[(0,a.jsx)(n.Tsu,{size:20}),"Back to Dashboard"]}),(0,a.jsxs)("div",{className:"header-info",children:[(0,a.jsx)("h1",{children:"Plan Renewal"}),(0,a.jsx)("h2",{children:c}),(0,a.jsxs)("div",{className:"step-indicator",children:["Step ",s," of 7"]})]}),(0,a.jsx)("div",{className:"completion-status",children:"57% Complete"})]}),(0,a.jsx)("div",{className:"renewal-steps",children:u.map((e,t)=>(0,a.jsxs)("div",{className:"renewal-step ".concat(e.active?"active":""," ").concat(e.completed?"completed":""),children:[(0,a.jsx)("div",{className:"step-number",children:e.completed?"✓":e.number}),(0,a.jsxs)("div",{className:"step-content",children:[(0,a.jsx)("div",{className:"step-title",children:e.title}),(0,a.jsx)("div",{className:"step-subtitle",children:e.subtitle})]}),t<u.length-1&&(0,a.jsx)("div",{className:"step-connector"})]},e.number))}),(0,a.jsxs)("div",{className:"document-upload-section",children:[(0,a.jsxs)("div",{className:"upload-header",children:[(0,a.jsxs)("div",{className:"upload-title",children:[(0,a.jsx)(n.tw,{size:20}),(0,a.jsx)("h3",{children:"Plan Document Upload"})]}),(0,a.jsxs)("p",{children:["Upload updated plan documents for ",c,". These documents will be tagged with the new plan year and made available to HR and employees."]})]}),(0,a.jsxs)("div",{className:"upload-content",children:[o.map(e=>(0,a.jsxs)("div",{className:"upload-card",children:[(0,a.jsxs)("div",{className:"card-header",children:[(0,a.jsxs)("div",{className:"header-content",children:[(0,a.jsxs)("h4",{children:[e.title,e.required&&(0,a.jsx)("span",{className:"required-badge",children:"Required"})]}),e.description&&(0,a.jsx)("p",{children:e.description})]}),(0,a.jsxs)("label",{className:"upload-btn",children:[(0,a.jsx)(n.tw,{size:16}),"Upload Files",(0,a.jsx)("input",{type:"file",multiple:!0,accept:".pdf,.doc,.docx,.xls,.xlsx",onChange:t=>p(e.id,t.target.files),style:{display:"none"}})]})]}),(0,a.jsx)("div",{className:"upload-area",children:e.files.length>0?(0,a.jsx)("div",{className:"files-list",children:e.files.map((e,t)=>(0,a.jsxs)("div",{className:"file-item",children:[(0,a.jsx)(n.vrJ,{size:16}),(0,a.jsx)("span",{children:e.name}),(0,a.jsxs)("span",{className:"file-size",children:[(e.size/1024/1024).toFixed(2)," MB"]})]},t))}):(0,a.jsxs)("div",{className:"empty-state",children:[(0,a.jsx)(n.vrJ,{size:24}),(0,a.jsx)("span",{children:e.placeholder})]})})]},e.id)),(0,a.jsxs)("div",{className:"guidelines-card",children:[(0,a.jsxs)("div",{className:"card-header",children:[(0,a.jsx)(n.if7,{size:20}),(0,a.jsx)("h4",{children:"Document Guidelines"})]}),(0,a.jsx)("div",{className:"guidelines-content",children:(0,a.jsxs)("ul",{children:[(0,a.jsx)("li",{children:"Accepted formats: PDF, DOC, DOCX, XLS, XLSX"}),(0,a.jsx)("li",{children:"Maximum file size: 10MB per file"}),(0,a.jsx)("li",{children:"Documents will be automatically tagged with the plan year"}),(0,a.jsx)("li",{children:"SBC documents are required for compliance"}),(0,a.jsx)("li",{children:"All uploads will be available to HR admins immediately"})]})})]})]}),(0,a.jsxs)("div",{className:"navigation-section",children:[(0,a.jsxs)("button",{className:"nav-btn secondary",onClick:()=>{t.back()},children:[(0,a.jsx)(n.Tsu,{size:16}),"Previous"]}),(0,a.jsxs)("div",{className:"nav-actions",children:[(0,a.jsx)("button",{className:"nav-btn secondary",onClick:()=>{t.push("/ai-enroller/renewal/".concat(e.groupId,"/validation"))},children:"Skip Upload"}),(0,a.jsx)("button",{className:"nav-btn primary enabled",onClick:()=>{t.push("/ai-enroller/renewal/".concat(e.groupId,"/validation"))},children:"Continue"})]})]})]})]})}},2536:function(){},51980:function(){},32242:function(){},46231:function(e,t,s){"use strict";s.d(t,{w_:function(){return d}});var a=s(2265),i={color:void 0,size:void 0,className:void 0,style:void 0,attr:void 0},r=a.createContext&&a.createContext(i),n=["attr","size","title"];function l(){return(l=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var s=arguments[t];for(var a in s)Object.prototype.hasOwnProperty.call(s,a)&&(e[a]=s[a])}return e}).apply(this,arguments)}function c(e,t){var s=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),s.push.apply(s,a)}return s}function o(e){for(var t=1;t<arguments.length;t++){var s=null!=arguments[t]?arguments[t]:{};t%2?c(Object(s),!0).forEach(function(t){var a,i;a=t,i=s[t],(a=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var s=e[Symbol.toPrimitive];if(void 0!==s){var a=s.call(e,t||"default");if("object"!=typeof a)return a;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(a))in e?Object.defineProperty(e,a,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[a]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(s)):c(Object(s)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(s,t))})}return e}function d(e){return t=>a.createElement(u,l({attr:o({},e.attr)},t),function e(t){return t&&t.map((t,s)=>a.createElement(t.tag,o({key:s},t.attr),e(t.child)))}(e.child))}function u(e){var t=t=>{var s,{attr:i,size:r,title:c}=e,d=function(e,t){if(null==e)return{};var s,a,i=function(e,t){if(null==e)return{};var s={};for(var a in e)if(Object.prototype.hasOwnProperty.call(e,a)){if(t.indexOf(a)>=0)continue;s[a]=e[a]}return s}(e,t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);for(a=0;a<r.length;a++)s=r[a],!(t.indexOf(s)>=0)&&Object.prototype.propertyIsEnumerable.call(e,s)&&(i[s]=e[s])}return i}(e,n),u=r||t.size||"1em";return t.className&&(s=t.className),e.className&&(s=(s?s+" ":"")+e.className),a.createElement("svg",l({stroke:"currentColor",fill:"currentColor",strokeWidth:"0"},t.attr,i,d,{className:s,style:o(o({color:e.color||t.color},t.style),e.style),height:u,width:u,xmlns:"http://www.w3.org/2000/svg"}),c&&a.createElement("title",null,c),e.children)};return void 0!==r?a.createElement(r.Consumer,null,e=>t(e)):t(i)}}},function(e){e.O(0,[3417,3240,8422,2971,2117,1744],function(){return e(e.s=36208)}),_N_E=e.O()}]);