(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2721],{33908:function(e,t,s){Promise.resolve().then(s.bind(s,17744))},99376:function(e,t,s){"use strict";var r=s(35475);s.o(r,"useParams")&&s.d(t,{useParams:function(){return r.useParams}}),s.o(r,"usePathname")&&s.d(t,{usePathname:function(){return r.usePathname}}),s.o(r,"useRouter")&&s.d(t,{useRouter:function(){return r.useRouter}}),s.o(r,"useSearchParams")&&s.d(t,{useSearchParams:function(){return r.useSearchParams}})},17744:function(e,t,s){"use strict";s.r(t);var r=s(57437),n=s(2265),a=s(99376),i=s(18913);s(32242),s(51980),s(65791),t.default=()=>{(0,a.useParams)();let e=(0,a.useRouter)(),[t,s]=(0,n.useState)(7),l="Green Valley Manufacturing",o=[{number:1,title:"Review Current Plans",subtitle:"View existing benefit plans",active:!1,completed:!0},{number:2,title:"Renewal Options",subtitle:"Choose renewal type",active:!1,completed:!0},{number:3,title:"Plan Configuration",subtitle:"Set dates and modifications",active:!1,completed:!0},{number:4,title:"Document Upload",subtitle:"Upload plan documents",active:!1,completed:!0},{number:5,title:"Validation",subtitle:"Review and validate setup",active:!1,completed:!0},{number:6,title:"Finalize",subtitle:"Complete renewal process",active:!1,completed:!0},{number:7,title:"Export",subtitle:"Download and share data",active:7===t}],c=[{id:"csv",title:"CSV Export",description:"Spreadsheet format with plan details, rates, and employee data",filename:"Green_Valley_Manufacturing_renewal_2025.csv",bestFor:"Import into payroll systems, Excel analysis",icon:(0,r.jsx)(i.vrJ,{size:24})},{id:"json",title:"JSON Export",description:"Structured data format for API integration and system sync",filename:"Green_Valley_Manufacturing_renewal_2025.json",bestFor:"API integration, automated system sync",icon:(0,r.jsx)(i.oT$,{size:24})},{id:"summary",title:"Summary Report",description:"PDF report with renewal overview and plan comparisons",filename:"Green_Valley_Manufacturing_renewal_summary.pdf",bestFor:"Client presentations, internal reporting",icon:(0,r.jsx)(i.vrJ,{size:24})}],d="Green Valley Manufacturing",m="1/1/2025",u="26/5/2025",p=e=>{console.log("Downloading ".concat(e," export..."))};return(0,r.jsxs)("div",{className:"plan-renewal-detail",children:[(0,r.jsxs)("div",{className:"detail-header",children:[(0,r.jsxs)("button",{className:"back-btn",onClick:()=>e.push("/ai-enroller/renewal"),children:[(0,r.jsx)(i.Tsu,{size:20}),"Back to Dashboard"]}),(0,r.jsxs)("div",{className:"header-info",children:[(0,r.jsx)("h1",{children:"Plan Renewal"}),(0,r.jsx)("h2",{children:l}),(0,r.jsxs)("div",{className:"step-indicator",children:["Step ",t," of 7"]})]}),(0,r.jsx)("div",{className:"completion-status",children:"100% Complete"})]}),(0,r.jsx)("div",{className:"renewal-steps",children:o.map((e,t)=>(0,r.jsxs)("div",{className:"renewal-step ".concat(e.active?"active":""," ").concat(e.completed?"completed":""),children:[(0,r.jsx)("div",{className:"step-number",children:e.completed?"✓":e.number}),(0,r.jsxs)("div",{className:"step-content",children:[(0,r.jsx)("div",{className:"step-title",children:e.title}),(0,r.jsx)("div",{className:"step-subtitle",children:e.subtitle})]}),t<o.length-1&&(0,r.jsx)("div",{className:"step-connector"})]},e.number))}),(0,r.jsxs)("div",{className:"export-section",children:[(0,r.jsxs)("div",{className:"export-header",children:[(0,r.jsxs)("div",{className:"export-title",children:[(0,r.jsx)(i.yFZ,{size:20}),(0,r.jsx)("h3",{children:"Export & Share Renewal Data"})]}),(0,r.jsx)("p",{children:"Download renewal data in various formats or copy summary information for sharing with your team."})]}),(0,r.jsxs)("div",{className:"export-content",children:[(0,r.jsxs)("div",{className:"success-message",children:[(0,r.jsx)(i.PjL,{size:24}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{children:"Renewal Successfully Completed!"}),(0,r.jsxs)("p",{children:["All plans for ",l," have been renewed and are now active. Use the options below to export data or share the renewal summary."]})]})]}),(0,r.jsx)("div",{className:"export-options",children:c.map(e=>(0,r.jsxs)("div",{className:"export-card",children:[(0,r.jsxs)("div",{className:"export-card-header",children:[(0,r.jsx)("div",{className:"export-icon",children:e.icon}),(0,r.jsxs)("div",{className:"export-info",children:[(0,r.jsx)("h4",{children:e.title}),(0,r.jsx)("p",{children:e.description})]})]}),(0,r.jsxs)("div",{className:"export-details",children:[(0,r.jsxs)("div",{className:"filename",children:[(0,r.jsx)("strong",{children:"Filename:"})," ",e.filename]}),(0,r.jsxs)("div",{className:"best-for",children:[(0,r.jsx)("strong",{children:"Best for:"})," ",e.bestFor]})]}),(0,r.jsxs)("button",{className:"download-btn",onClick:()=>p(e.id),children:[(0,r.jsx)(i.yFZ,{size:16}),"Download ",e.title]})]},e.id))}),(0,r.jsxs)("div",{className:"quick-share",children:[(0,r.jsx)("h4",{children:"Quick Share Options"}),(0,r.jsx)("p",{children:"Quickly share renewal information with your team via common communication tools."}),(0,r.jsxs)("div",{className:"share-buttons",children:[(0,r.jsxs)("button",{className:"share-btn",onClick:()=>{let e="Renewal Process Complete\n\nGroup: ".concat(d,"\nEffective Date: ").concat(m,"\nPlans Renewed: ").concat(2,"\nEmployees Affected: ").concat(89,"\nCompleted: ").concat(u);navigator.clipboard.writeText(e),alert("Summary copied to clipboard!")},children:[(0,r.jsx)(i.zFu,{size:16}),"Copy to Clipboard",(0,r.jsx)("span",{className:"share-desc",children:"Summary text for Slack, Teams, or email"})]}),(0,r.jsxs)("button",{className:"share-btn",onClick:()=>{console.log("Sending email notification...")},children:[(0,r.jsx)(i.Zuw,{size:16}),"Send Email Notification",(0,r.jsx)("span",{className:"share-desc",children:"Quick email to stakeholders"})]})]})]}),(0,r.jsxs)("div",{className:"process-summary",children:[(0,r.jsx)("h4",{children:"Renewal Process Complete"}),(0,r.jsxs)("div",{className:"summary-grid",children:[(0,r.jsxs)("div",{className:"summary-item",children:[(0,r.jsx)("strong",{children:"Group:"})," ",d]}),(0,r.jsxs)("div",{className:"summary-item",children:[(0,r.jsx)("strong",{children:"Effective Date:"})," ",m]}),(0,r.jsxs)("div",{className:"summary-item",children:[(0,r.jsx)("strong",{children:"Plans Renewed:"})," ",2]}),(0,r.jsxs)("div",{className:"summary-item",children:[(0,r.jsx)("strong",{children:"Employees Affected:"})," ",89]}),(0,r.jsxs)("div",{className:"summary-item",children:[(0,r.jsx)("strong",{children:"Documents Uploaded:"})," ",0]}),(0,r.jsxs)("div",{className:"summary-item",children:[(0,r.jsx)("strong",{children:"Completed:"})," ",u]})]})]})]}),(0,r.jsxs)("div",{className:"navigation-section",children:[(0,r.jsxs)("button",{className:"nav-btn secondary",onClick:()=>e.back(),children:[(0,r.jsx)(i.Tsu,{size:16}),"Previous"]}),(0,r.jsx)("button",{className:"nav-btn primary enabled",onClick:()=>{e.push("/ai-enroller/renewal")},children:"Return to Dashboard"})]})]})]})}},65791:function(){},51980:function(){},32242:function(){},46231:function(e,t,s){"use strict";s.d(t,{w_:function(){return d}});var r=s(2265),n={color:void 0,size:void 0,className:void 0,style:void 0,attr:void 0},a=r.createContext&&r.createContext(n),i=["attr","size","title"];function l(){return(l=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var s=arguments[t];for(var r in s)Object.prototype.hasOwnProperty.call(s,r)&&(e[r]=s[r])}return e}).apply(this,arguments)}function o(e,t){var s=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),s.push.apply(s,r)}return s}function c(e){for(var t=1;t<arguments.length;t++){var s=null!=arguments[t]?arguments[t]:{};t%2?o(Object(s),!0).forEach(function(t){var r,n;r=t,n=s[t],(r=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var s=e[Symbol.toPrimitive];if(void 0!==s){var r=s.call(e,t||"default");if("object"!=typeof r)return r;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(r))in e?Object.defineProperty(e,r,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[r]=n}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(s)):o(Object(s)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(s,t))})}return e}function d(e){return t=>r.createElement(m,l({attr:c({},e.attr)},t),function e(t){return t&&t.map((t,s)=>r.createElement(t.tag,c({key:s},t.attr),e(t.child)))}(e.child))}function m(e){var t=t=>{var s,{attr:n,size:a,title:o}=e,d=function(e,t){if(null==e)return{};var s,r,n=function(e,t){if(null==e)return{};var s={};for(var r in e)if(Object.prototype.hasOwnProperty.call(e,r)){if(t.indexOf(r)>=0)continue;s[r]=e[r]}return s}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(r=0;r<a.length;r++)s=a[r],!(t.indexOf(s)>=0)&&Object.prototype.propertyIsEnumerable.call(e,s)&&(n[s]=e[s])}return n}(e,i),m=a||t.size||"1em";return t.className&&(s=t.className),e.className&&(s=(s?s+" ":"")+e.className),r.createElement("svg",l({stroke:"currentColor",fill:"currentColor",strokeWidth:"0"},t.attr,n,d,{className:s,style:c(c({color:e.color||t.color},t.style),e.style),height:m,width:m,xmlns:"http://www.w3.org/2000/svg"}),o&&r.createElement("title",null,o),e.children)};return void 0!==a?r.createElement(a.Consumer,null,e=>t(e)):t(n)}}},function(e){e.O(0,[3417,5984,8422,2971,2117,1744],function(){return e(e.s=33908)}),_N_E=e.O()}]);