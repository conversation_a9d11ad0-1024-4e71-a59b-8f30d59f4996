(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[123],{71570:function(e,s,t){Promise.resolve().then(t.bind(t,24371))},99376:function(e,s,t){"use strict";var n=t(35475);t.o(n,"useParams")&&t.d(s,{useParams:function(){return n.useParams}}),t.o(n,"usePathname")&&t.d(s,{usePathname:function(){return n.usePathname}}),t.o(n,"useRouter")&&t.d(s,{useRouter:function(){return n.useRouter}}),t.o(n,"useSearchParams")&&t.d(s,{useSearchParams:function(){return n.useSearchParams}})},24371:function(e,s,t){"use strict";t.r(s);var n=t(57437),a=t(2265),i=t(99376),r=t(18913);t(32242),t(51980),t(96991),s.default=()=>{let e=(0,i.useParams)(),s=(0,i.useRouter)(),[t,c]=(0,a.useState)(6),[l,d]=(0,a.useState)(!1),o="Green Valley Manufacturing",u=[{number:1,title:"Review Current Plans",subtitle:"View existing benefit plans",active:!1,completed:!0},{number:2,title:"Renewal Options",subtitle:"Choose renewal type",active:!1,completed:!0},{number:3,title:"Plan Configuration",subtitle:"Set dates and modifications",active:!1,completed:!0},{number:4,title:"Document Upload",subtitle:"Upload plan documents",active:!1,completed:!0},{number:5,title:"Validation",subtitle:"Review and validate setup",active:!1,completed:!0},{number:6,title:"Finalize",subtitle:"Complete renewal process",active:6===t},{number:7,title:"Export",subtitle:"Download and share data",active:!1}],m=async()=>{d(!0),await new Promise(e=>setTimeout(e,3e3)),s.push("/ai-enroller/renewal/".concat(e.groupId,"/export"))};return(0,n.jsxs)("div",{className:"plan-renewal-detail",children:[(0,n.jsxs)("div",{className:"detail-header",children:[(0,n.jsxs)("button",{className:"back-btn",onClick:()=>s.push("/ai-enroller/renewal"),children:[(0,n.jsx)(r.Tsu,{size:20}),"Back to Dashboard"]}),(0,n.jsxs)("div",{className:"header-info",children:[(0,n.jsx)("h1",{children:"Plan Renewal"}),(0,n.jsx)("h2",{children:o}),(0,n.jsxs)("div",{className:"step-indicator",children:["Step ",t," of 7"]})]}),(0,n.jsx)("div",{className:"completion-status",children:"86% Complete"})]}),(0,n.jsx)("div",{className:"renewal-steps",children:u.map((e,s)=>(0,n.jsxs)("div",{className:"renewal-step ".concat(e.active?"active":""," ").concat(e.completed?"completed":""),children:[(0,n.jsx)("div",{className:"step-number",children:e.completed?"✓":e.number}),(0,n.jsxs)("div",{className:"step-content",children:[(0,n.jsx)("div",{className:"step-title",children:e.title}),(0,n.jsx)("div",{className:"step-subtitle",children:e.subtitle})]}),s<u.length-1&&(0,n.jsx)("div",{className:"step-connector"})]},e.number))}),(0,n.jsxs)("div",{className:"finalize-section",children:[(0,n.jsxs)("div",{className:"finalize-header",children:[(0,n.jsxs)("div",{className:"finalize-title",children:[(0,n.jsx)(r.Moc,{size:20}),(0,n.jsx)("h3",{children:"Finalize Plan Renewal"})]}),(0,n.jsxs)("p",{children:["Complete the renewal process for ",o,". This will activate the new plans and archive the old ones."]})]}),(0,n.jsxs)("div",{className:"finalize-content",children:[(0,n.jsxs)("div",{className:"summary-cards",children:[(0,n.jsxs)("div",{className:"summary-card",children:[(0,n.jsx)("div",{className:"card-icon",children:(0,n.jsx)(r.vrJ,{size:24})}),(0,n.jsxs)("div",{className:"card-content",children:[(0,n.jsx)("div",{className:"card-number",children:2}),(0,n.jsx)("div",{className:"card-label",children:"Plans Renewed"})]})]}),(0,n.jsxs)("div",{className:"summary-card",children:[(0,n.jsx)("div",{className:"card-icon",children:(0,n.jsx)(r.Otr,{size:24})}),(0,n.jsxs)("div",{className:"card-content",children:[(0,n.jsx)("div",{className:"card-number",children:89}),(0,n.jsx)("div",{className:"card-label",children:"Employees Affected"})]})]}),(0,n.jsxs)("div",{className:"summary-card",children:[(0,n.jsx)("div",{className:"card-icon",children:(0,n.jsx)(r.Bge,{size:24})}),(0,n.jsxs)("div",{className:"card-content",children:[(0,n.jsx)("div",{className:"card-number",children:"1/1/2025"}),(0,n.jsx)("div",{className:"card-label",children:"Effective Date"})]})]}),(0,n.jsxs)("div",{className:"summary-card",children:[(0,n.jsx)("div",{className:"card-icon",children:(0,n.jsx)(r.tw,{size:24})}),(0,n.jsxs)("div",{className:"card-content",children:[(0,n.jsx)("div",{className:"card-number",children:0}),(0,n.jsx)("div",{className:"card-label",children:"Documents Uploaded"})]})]})]}),(0,n.jsxs)("div",{className:"finalize-steps",children:[(0,n.jsx)("h4",{children:"What happens when you finalize:"}),(0,n.jsx)("div",{className:"steps-list",children:[{number:1,title:"Archive Current Plans",description:'Existing plans will be marked as "Expired" and archived',completed:!1},{number:2,title:"Activate Renewed Plans",description:"New plans become active on the effective date",completed:!1},{number:3,title:"Notify HR Team",description:"HR administrators will receive renewal confirmation",completed:!1},{number:4,title:"Update Employee Portal",description:"Employees will see updated plan information",completed:!1}].map(e=>(0,n.jsxs)("div",{className:"finalize-step",children:[(0,n.jsx)("div",{className:"step-indicator",children:(0,n.jsx)("div",{className:"step-circle",children:e.number})}),(0,n.jsxs)("div",{className:"step-content",children:[(0,n.jsx)("h5",{children:e.title}),(0,n.jsx)("p",{children:e.description})]})]},e.number))})]}),(0,n.jsxs)("div",{className:"ready-notice",children:[(0,n.jsx)(r.PjL,{size:20}),(0,n.jsxs)("div",{children:[(0,n.jsx)("h4",{children:"Ready to Finalize"}),(0,n.jsx)("p",{children:'All validation checks have passed. Once you click "Finalize Renewal", the changes will be applied and cannot be undone without creating a new renewal.'})]})]})]}),(0,n.jsxs)("div",{className:"navigation-section",children:[(0,n.jsxs)("button",{className:"nav-btn secondary",onClick:()=>{s.back()},disabled:l,children:[(0,n.jsx)(r.Tsu,{size:16}),"Previous"]}),(0,n.jsx)("button",{className:"finalize-btn ".concat(l?"processing":"enabled"),onClick:m,disabled:l,children:l?(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)("div",{className:"spinner"}),"Processing..."]}):(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(r.Moc,{size:16}),"Finalize Renewal"]})})]})]})]})}},96991:function(){},51980:function(){},32242:function(){},46231:function(e,s,t){"use strict";t.d(s,{w_:function(){return o}});var n=t(2265),a={color:void 0,size:void 0,className:void 0,style:void 0,attr:void 0},i=n.createContext&&n.createContext(a),r=["attr","size","title"];function c(){return(c=Object.assign?Object.assign.bind():function(e){for(var s=1;s<arguments.length;s++){var t=arguments[s];for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])}return e}).apply(this,arguments)}function l(e,s){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);s&&(n=n.filter(function(s){return Object.getOwnPropertyDescriptor(e,s).enumerable})),t.push.apply(t,n)}return t}function d(e){for(var s=1;s<arguments.length;s++){var t=null!=arguments[s]?arguments[s]:{};s%2?l(Object(t),!0).forEach(function(s){var n,a;n=s,a=t[s],(n=function(e){var s=function(e,s){if("object"!=typeof e||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var n=t.call(e,s||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===s?String:Number)(e)}(e,"string");return"symbol"==typeof s?s:s+""}(n))in e?Object.defineProperty(e,n,{value:a,enumerable:!0,configurable:!0,writable:!0}):e[n]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):l(Object(t)).forEach(function(s){Object.defineProperty(e,s,Object.getOwnPropertyDescriptor(t,s))})}return e}function o(e){return s=>n.createElement(u,c({attr:d({},e.attr)},s),function e(s){return s&&s.map((s,t)=>n.createElement(s.tag,d({key:t},s.attr),e(s.child)))}(e.child))}function u(e){var s=s=>{var t,{attr:a,size:i,title:l}=e,o=function(e,s){if(null==e)return{};var t,n,a=function(e,s){if(null==e)return{};var t={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(s.indexOf(n)>=0)continue;t[n]=e[n]}return t}(e,s);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)t=i[n],!(s.indexOf(t)>=0)&&Object.prototype.propertyIsEnumerable.call(e,t)&&(a[t]=e[t])}return a}(e,r),u=i||s.size||"1em";return s.className&&(t=s.className),e.className&&(t=(t?t+" ":"")+e.className),n.createElement("svg",c({stroke:"currentColor",fill:"currentColor",strokeWidth:"0"},s.attr,a,o,{className:t,style:d(d({color:e.color||s.color},s.style),e.style),height:u,width:u,xmlns:"http://www.w3.org/2000/svg"}),l&&n.createElement("title",null,l),e.children)};return void 0!==i?n.createElement(i.Consumer,null,e=>s(e)):s(a)}}},function(e){e.O(0,[3417,7941,8422,2971,2117,1744],function(){return e(e.s=71570)}),_N_E=e.O()}]);