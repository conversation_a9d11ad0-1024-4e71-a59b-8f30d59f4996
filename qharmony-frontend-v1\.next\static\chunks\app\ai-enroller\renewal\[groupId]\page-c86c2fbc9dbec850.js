(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[859],{99026:function(e,s,t){Promise.resolve().then(t.bind(t,22737))},99376:function(e,s,t){"use strict";var l=t(35475);t.o(l,"useParams")&&t.d(s,{useParams:function(){return l.useParams}}),t.o(l,"usePathname")&&t.d(s,{usePathname:function(){return l.usePathname}}),t.o(l,"useRouter")&&t.d(s,{useRouter:function(){return l.useRouter}}),t.o(l,"useSearchParams")&&t.d(s,{useSearchParams:function(){return l.useSearchParams}})},22737:function(e,s,t){"use strict";t.r(s);var l=t(57437),i=t(2265),n=t(99376),a=t(18913),r=t(49089);t(32242),t(51980);let c=[{id:"1",name:"Blue Cross Blue Shield PPO",carrier:"BCBS",type:"Medical",deductible:"$2,500",copay:"$30",monthlyPremiums:{employeeOnly:"$458.50",employeeSpouse:"$917.00",employeeChild:"$687.75",family:"$1,374.00"},enrolled:"142/145",expiration:"31/12/2024",selected:!1},{id:"2",name:"Delta Dental PPO",carrier:"Delta Dental",type:"Dental",deductible:"$75",copay:"$0",monthlyPremiums:{employeeOnly:"$42.50",employeeSpouse:"$85.00",employeeChild:"$63.75",family:"$127.50"},enrolled:"138/145",expiration:"31/12/2024",selected:!1},{id:"3",name:"VSP Vision Care",carrier:"VSP",type:"Vision",deductible:"$15",copay:"$15",monthlyPremiums:{employeeOnly:"$18.75",employeeSpouse:"$37.50",employeeChild:"$28.13",family:"$56.25"},enrolled:"124/145",expiration:"31/12/2024",selected:!1},{id:"4",name:"Basic Life Insurance",carrier:"MetLife",type:"Life",deductible:"N/A",copay:"N/A",monthlyPremiums:{employeeOnly:"$15.60",employeeSpouse:"$0.00",employeeChild:"$0.00",family:"$0.00"},enrolled:"145/145",expiration:"31/12/2024",selected:!1}];s.default=()=>{let e=(0,n.useParams)(),s=(0,n.useRouter)(),[t,o]=(0,i.useState)(c),[d,m]=(0,i.useState)(1),u="TechCorp Solutions",p=e=>{o(t.map(s=>s.id===e?{...s,selected:!s.selected}:s))},h=t.filter(e=>e.selected).length,j=e=>{switch(e){case"Medical":default:return(0,l.jsx)(a.wkn,{size:24});case"Dental":return(0,l.jsx)(r.sr,{size:24});case"Vision":return(0,l.jsx)(a.Vvo,{size:24});case"Life":return(0,l.jsx)(a.c3K,{size:24})}},x=[{number:1,title:"Review Current Plans",subtitle:"View existing benefit plans",active:1===d},{number:2,title:"Renewal Options",subtitle:"Choose renewal type",active:2===d},{number:3,title:"Plan Configuration",subtitle:"Set dates and modifications",active:3===d},{number:4,title:"Document Upload",subtitle:"Upload plan documents",active:4===d},{number:5,title:"Validation",subtitle:"Review and validate setup",active:5===d},{number:6,title:"Finalize",subtitle:"Complete renewal process",active:6===d},{number:7,title:"Export",subtitle:"Download and share data",active:7===d}];return(0,l.jsxs)("div",{className:"plan-renewal-detail",children:[(0,l.jsxs)("div",{className:"detail-header",children:[(0,l.jsxs)("button",{className:"back-btn",onClick:()=>s.back(),children:[(0,l.jsx)(a.Tsu,{size:20}),"Back to Dashboard"]}),(0,l.jsxs)("div",{className:"header-info",children:[(0,l.jsx)("h1",{children:"Plan Renewal"}),(0,l.jsx)("h2",{children:u}),(0,l.jsxs)("div",{className:"step-indicator",children:["Step ",d," of 7"]})]}),(0,l.jsx)("div",{className:"completion-status",children:"14% Complete"})]}),(0,l.jsx)("div",{className:"renewal-steps",children:x.map((e,s)=>(0,l.jsxs)("div",{className:"renewal-step ".concat(e.active?"active":""),children:[(0,l.jsx)("div",{className:"step-number",children:e.number}),(0,l.jsxs)("div",{className:"step-content",children:[(0,l.jsx)("div",{className:"step-title",children:e.title}),(0,l.jsx)("div",{className:"step-subtitle",children:e.subtitle})]}),s<x.length-1&&(0,l.jsx)("div",{className:"step-connector"})]},e.number))}),(0,l.jsxs)("div",{className:"plan-portfolio",children:[(0,l.jsxs)("div",{className:"portfolio-header",children:[(0,l.jsxs)("div",{className:"portfolio-title",children:[(0,l.jsx)(a.wkn,{size:20}),(0,l.jsx)("h3",{children:"Current Plan Portfolio"})]}),(0,l.jsxs)("p",{children:["Review the existing benefit plans for ",u,". Select the plans you want to renew for the next plan year."]})]}),(0,l.jsx)("div",{className:"plans-grid",children:t.map(e=>(0,l.jsxs)("div",{className:"plan-card ".concat(e.selected?"selected":""),children:[(0,l.jsxs)("div",{className:"plan-header",children:[(0,l.jsx)("div",{className:"plan-select",children:(0,l.jsx)("button",{className:"select-btn ".concat(e.selected?"selected":""),onClick:()=>p(e.id),children:e.selected?(0,l.jsx)(a.PjL,{size:20}):(0,l.jsx)(a.C9x,{size:20})})}),(0,l.jsxs)("div",{className:"plan-info",children:[(0,l.jsx)("div",{className:"plan-icon",children:j(e.type)}),(0,l.jsxs)("div",{children:[(0,l.jsx)("h4",{children:e.name}),(0,l.jsx)("span",{className:"plan-carrier",children:e.carrier})]})]}),(0,l.jsx)("div",{className:"plan-type-badge",children:e.type})]}),(0,l.jsxs)("div",{className:"plan-details",children:[(0,l.jsxs)("div",{className:"detail-row",children:[(0,l.jsx)("span",{className:"label",children:"Deductible"}),(0,l.jsx)("span",{className:"value",children:e.deductible})]}),(0,l.jsxs)("div",{className:"detail-row",children:[(0,l.jsx)("span",{className:"label",children:"Copay"}),(0,l.jsx)("span",{className:"value",children:e.copay})]})]}),(0,l.jsxs)("div",{className:"premium-section",children:[(0,l.jsx)("h5",{children:"Monthly Premiums"}),(0,l.jsxs)("div",{className:"premium-grid",children:[(0,l.jsxs)("div",{className:"premium-item",children:[(0,l.jsx)("span",{className:"premium-label",children:"Employee Only:"}),(0,l.jsx)("span",{className:"premium-value",children:e.monthlyPremiums.employeeOnly})]}),(0,l.jsxs)("div",{className:"premium-item",children:[(0,l.jsx)("span",{className:"premium-label",children:"EE + Spouse:"}),(0,l.jsx)("span",{className:"premium-value",children:e.monthlyPremiums.employeeSpouse})]}),(0,l.jsxs)("div",{className:"premium-item",children:[(0,l.jsx)("span",{className:"premium-label",children:"EE + Child:"}),(0,l.jsx)("span",{className:"premium-value",children:e.monthlyPremiums.employeeChild})]}),(0,l.jsxs)("div",{className:"premium-item",children:[(0,l.jsx)("span",{className:"premium-label",children:"Family:"}),(0,l.jsx)("span",{className:"premium-value",children:e.monthlyPremiums.family})]})]})]}),(0,l.jsxs)("div",{className:"plan-footer",children:[(0,l.jsxs)("div",{className:"enrollment-info",children:[(0,l.jsx)(a.Otr,{size:16}),(0,l.jsxs)("span",{children:[e.enrolled," enrolled"]})]}),(0,l.jsxs)("div",{className:"expiration-info",children:[(0,l.jsx)(a.Bge,{size:16}),(0,l.jsxs)("span",{children:["Exp: ",e.expiration]})]})]})]},e.id))}),(0,l.jsxs)("div",{className:"quick-actions",children:[(0,l.jsxs)("div",{className:"actions-info",children:[(0,l.jsx)(a.PjL,{size:20}),(0,l.jsxs)("div",{children:[(0,l.jsx)("h4",{children:"Quick Actions"}),(0,l.jsx)("p",{children:"You can select all plans to renew with existing terms, or choose specific plans to modify. Individual plan modifications can be made in the next steps."})]})]}),(0,l.jsxs)("div",{className:"action-buttons",children:[(0,l.jsx)("button",{className:"action-btn secondary",onClick:()=>{let e=t.every(e=>e.selected);o(t.map(s=>({...s,selected:!e})))},children:"Select All Plans"}),(0,l.jsx)("button",{className:"action-btn secondary",onClick:()=>{o(t.map(e=>({...e,selected:!1})))},children:"Clear Selection"})]})]}),(0,l.jsxs)("div",{className:"continue-section",children:[(0,l.jsxs)("button",{className:"continue-btn ".concat(h>0?"enabled":"disabled"),disabled:0===h,onClick:()=>{h>0&&s.push("/ai-enroller/renewal/".concat(e.groupId,"/renewal-options"))},type:"button",children:["Continue with ",h," Plans"]}),0===h&&(0,l.jsx)("p",{style:{marginTop:"0.5rem",fontSize:"0.875rem",color:"#6b7280"},children:"Please select at least one plan to continue"})]})]})]})}},51980:function(){},32242:function(){},46231:function(e,s,t){"use strict";t.d(s,{w_:function(){return d}});var l=t(2265),i={color:void 0,size:void 0,className:void 0,style:void 0,attr:void 0},n=l.createContext&&l.createContext(i),a=["attr","size","title"];function r(){return(r=Object.assign?Object.assign.bind():function(e){for(var s=1;s<arguments.length;s++){var t=arguments[s];for(var l in t)Object.prototype.hasOwnProperty.call(t,l)&&(e[l]=t[l])}return e}).apply(this,arguments)}function c(e,s){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var l=Object.getOwnPropertySymbols(e);s&&(l=l.filter(function(s){return Object.getOwnPropertyDescriptor(e,s).enumerable})),t.push.apply(t,l)}return t}function o(e){for(var s=1;s<arguments.length;s++){var t=null!=arguments[s]?arguments[s]:{};s%2?c(Object(t),!0).forEach(function(s){var l,i;l=s,i=t[s],(l=function(e){var s=function(e,s){if("object"!=typeof e||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var l=t.call(e,s||"default");if("object"!=typeof l)return l;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===s?String:Number)(e)}(e,"string");return"symbol"==typeof s?s:s+""}(l))in e?Object.defineProperty(e,l,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[l]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):c(Object(t)).forEach(function(s){Object.defineProperty(e,s,Object.getOwnPropertyDescriptor(t,s))})}return e}function d(e){return s=>l.createElement(m,r({attr:o({},e.attr)},s),function e(s){return s&&s.map((s,t)=>l.createElement(s.tag,o({key:t},s.attr),e(s.child)))}(e.child))}function m(e){var s=s=>{var t,{attr:i,size:n,title:c}=e,d=function(e,s){if(null==e)return{};var t,l,i=function(e,s){if(null==e)return{};var t={};for(var l in e)if(Object.prototype.hasOwnProperty.call(e,l)){if(s.indexOf(l)>=0)continue;t[l]=e[l]}return t}(e,s);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);for(l=0;l<n.length;l++)t=n[l],!(s.indexOf(t)>=0)&&Object.prototype.propertyIsEnumerable.call(e,t)&&(i[t]=e[t])}return i}(e,a),m=n||s.size||"1em";return s.className&&(t=s.className),e.className&&(t=(t?t+" ":"")+e.className),l.createElement("svg",r({stroke:"currentColor",fill:"currentColor",strokeWidth:"0"},s.attr,i,d,{className:t,style:o(o({color:e.color||s.color},s.style),e.style),height:m,width:m,xmlns:"http://www.w3.org/2000/svg"}),c&&l.createElement("title",null,c),e.children)};return void 0!==n?l.createElement(n.Consumer,null,e=>s(e)):s(i)}}},function(e){e.O(0,[3417,8422,7699,2971,2117,1744],function(){return e(e.s=99026)}),_N_E=e.O()}]);