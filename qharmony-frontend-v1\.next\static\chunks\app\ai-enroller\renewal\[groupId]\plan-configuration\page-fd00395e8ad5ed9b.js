(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[925],{35700:function(e,t,n){Promise.resolve().then(n.bind(n,27183))},99376:function(e,t,n){"use strict";var a=n(35475);n.o(a,"useParams")&&n.d(t,{useParams:function(){return a.useParams}}),n.o(a,"usePathname")&&n.d(t,{usePathname:function(){return a.usePathname}}),n.o(a,"useRouter")&&n.d(t,{useRouter:function(){return a.useRouter}}),n.o(a,"useSearchParams")&&n.d(t,{useSearchParams:function(){return a.useSearchParams}})},27183:function(e,t,n){"use strict";n.r(t);var a=n(57437),s=n(2265),r=n(99376),i=n(18913);n(32242),n(51980),n(10785),t.default=()=>{let e=(0,r.useParams)(),t=(0,r.useRouter)(),n=(0,r.useSearchParams)(),[l,c]=(0,s.useState)(3),[o,d]=(0,s.useState)("01/01/2025"),[u,m]=(0,s.useState)("31/12/2025"),h="TechCorp Solutions",p=n.get("option")||"renew-as-is",v=[{number:1,title:"Review Current Plans",subtitle:"View existing benefit plans",active:!1,completed:!0},{number:2,title:"Renewal Options",subtitle:"Choose renewal type",active:!1,completed:!0},{number:3,title:"Plan Configuration",subtitle:"Set dates and modifications",active:3===l},{number:4,title:"Document Upload",subtitle:"Upload plan documents",active:!1},{number:5,title:"Validation",subtitle:"Review and validate setup",active:!1},{number:6,title:"Finalize",subtitle:"Complete renewal process",active:!1},{number:7,title:"Export",subtitle:"Download and share data",active:!1}],f=e=>new Date(e).toLocaleDateString("en-US",{day:"2-digit",month:"2-digit",year:"numeric"});return(0,a.jsxs)("div",{className:"plan-renewal-detail",children:[(0,a.jsxs)("div",{className:"detail-header",children:[(0,a.jsxs)("button",{className:"back-btn",onClick:()=>t.push("/ai-enroller/renewal"),children:[(0,a.jsx)(i.Tsu,{size:20}),"Back to Dashboard"]}),(0,a.jsxs)("div",{className:"header-info",children:[(0,a.jsx)("h1",{children:"Plan Renewal"}),(0,a.jsx)("h2",{children:h}),(0,a.jsxs)("div",{className:"step-indicator",children:["Step ",l," of 7"]})]}),(0,a.jsx)("div",{className:"completion-status",children:"43% Complete"})]}),(0,a.jsx)("div",{className:"renewal-steps",children:v.map((e,t)=>(0,a.jsxs)("div",{className:"renewal-step ".concat(e.active?"active":""," ").concat(e.completed?"completed":""),children:[(0,a.jsx)("div",{className:"step-number",children:e.completed?"✓":e.number}),(0,a.jsxs)("div",{className:"step-content",children:[(0,a.jsx)("div",{className:"step-title",children:e.title}),(0,a.jsx)("div",{className:"step-subtitle",children:e.subtitle})]}),t<v.length-1&&(0,a.jsx)("div",{className:"step-connector"})]},e.number))}),(0,a.jsxs)("div",{className:"plan-configuration-section",children:[(0,a.jsxs)("div",{className:"config-header",children:[(0,a.jsxs)("div",{className:"config-title",children:[(0,a.jsx)(i.XlX,{size:20}),(0,a.jsx)("h3",{children:"Plan Configuration"})]}),(0,a.jsxs)("p",{children:["Set the new plan year dates and configure plan details for ",h,"."]})]}),(0,a.jsxs)("div",{className:"config-content",children:[(0,a.jsxs)("div",{className:"config-card",children:[(0,a.jsxs)("div",{className:"card-header",children:[(0,a.jsx)(i.Bge,{size:20}),(0,a.jsx)("h4",{children:"Plan Year Dates"})]}),(0,a.jsxs)("div",{className:"date-inputs",children:[(0,a.jsxs)("div",{className:"date-field",children:[(0,a.jsx)("label",{htmlFor:"start-date",children:"Plan Year Start Date"}),(0,a.jsx)("input",{id:"start-date",type:"date",value:o.split("/").reverse().join("-"),onChange:e=>d(e.target.value.split("-").reverse().join("/")),className:"date-input"}),(0,a.jsx)("span",{className:"date-help",children:"When the new plan year begins"})]}),(0,a.jsxs)("div",{className:"date-field",children:[(0,a.jsx)("label",{htmlFor:"end-date",children:"Plan Year End Date"}),(0,a.jsx)("input",{id:"end-date",type:"date",value:u.split("/").reverse().join("-"),onChange:e=>m(e.target.value.split("-").reverse().join("/")),className:"date-input"}),(0,a.jsx)("span",{className:"date-help",children:"When the plan year expires"})]})]})]}),(0,a.jsxs)("div",{className:"config-card summary-card",children:[(0,a.jsxs)("div",{className:"card-header",children:[(0,a.jsx)(i.if7,{size:20}),(0,a.jsx)("h4",{children:"Plan Year Summary"})]}),(0,a.jsx)("div",{className:"summary-content",children:(0,a.jsxs)("p",{children:["The new plan year will run from ",(0,a.jsx)("strong",{children:f(o)})," to ",(0,a.jsx)("strong",{children:f(u)}),", covering a total of ",(0,a.jsxs)("strong",{children:[(()=>{let e=new Date(o);return Math.ceil(Math.abs(new Date(u).getTime()-e.getTime())/864e5)})()," days"]}),"."]})})]}),"renew-as-is"!==p&&(0,a.jsxs)("div",{className:"config-card",children:[(0,a.jsxs)("div",{className:"card-header",children:[(0,a.jsx)(i.XlX,{size:20}),(0,a.jsx)("h4",{children:"Plan Modifications"})]}),(0,a.jsx)("div",{className:"modifications-content",children:(0,a.jsxs)("div",{className:"modification-notice",children:[(0,a.jsx)(i.if7,{size:20}),(0,a.jsxs)("p",{children:["Plan modifications will be configured in the next steps based on your selected renewal strategy:",(0,a.jsx)("strong",{children:"copy-modify"===p?"Copy & Modify Plans":"Major Plan Changes"})]})]})})]})]}),(0,a.jsxs)("div",{className:"navigation-section",children:[(0,a.jsxs)("button",{className:"nav-btn secondary",onClick:()=>{t.back()},children:[(0,a.jsx)(i.Tsu,{size:16}),"Previous"]}),(0,a.jsx)("button",{className:"nav-btn primary enabled",onClick:()=>{t.push("/ai-enroller/renewal/".concat(e.groupId,"/document-upload"))},children:"Continue"})]})]})]})}},10785:function(){},51980:function(){},32242:function(){},46231:function(e,t,n){"use strict";n.d(t,{w_:function(){return d}});var a=n(2265),s={color:void 0,size:void 0,className:void 0,style:void 0,attr:void 0},r=a.createContext&&a.createContext(s),i=["attr","size","title"];function l(){return(l=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e}).apply(this,arguments)}function c(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,a)}return n}function o(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?c(Object(n),!0).forEach(function(t){var a,s;a=t,s=n[t],(a=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var a=n.call(e,t||"default");if("object"!=typeof a)return a;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(a))in e?Object.defineProperty(e,a,{value:s,enumerable:!0,configurable:!0,writable:!0}):e[a]=s}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):c(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}function d(e){return t=>a.createElement(u,l({attr:o({},e.attr)},t),function e(t){return t&&t.map((t,n)=>a.createElement(t.tag,o({key:n},t.attr),e(t.child)))}(e.child))}function u(e){var t=t=>{var n,{attr:s,size:r,title:c}=e,d=function(e,t){if(null==e)return{};var n,a,s=function(e,t){if(null==e)return{};var n={};for(var a in e)if(Object.prototype.hasOwnProperty.call(e,a)){if(t.indexOf(a)>=0)continue;n[a]=e[a]}return n}(e,t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);for(a=0;a<r.length;a++)n=r[a],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(s[n]=e[n])}return s}(e,i),u=r||t.size||"1em";return t.className&&(n=t.className),e.className&&(n=(n?n+" ":"")+e.className),a.createElement("svg",l({stroke:"currentColor",fill:"currentColor",strokeWidth:"0"},t.attr,s,d,{className:n,style:o(o({color:e.color||t.color},t.style),e.style),height:u,width:u,xmlns:"http://www.w3.org/2000/svg"}),c&&a.createElement("title",null,c),e.children)};return void 0!==r?a.createElement(r.Consumer,null,e=>t(e)):t(s)}}},function(e){e.O(0,[3417,6998,8422,2971,2117,1744],function(){return e(e.s=35700)}),_N_E=e.O()}]);