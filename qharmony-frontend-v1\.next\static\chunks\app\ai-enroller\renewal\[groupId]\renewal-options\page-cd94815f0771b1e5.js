(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7636],{88855:function(e,t,n){Promise.resolve().then(n.bind(n,34097))},99376:function(e,t,n){"use strict";var s=n(35475);n.o(s,"useParams")&&n.d(t,{useParams:function(){return s.useParams}}),n.o(s,"usePathname")&&n.d(t,{usePathname:function(){return s.usePathname}}),n.o(s,"useRouter")&&n.d(t,{useRouter:function(){return s.useRouter}}),n.o(s,"useSearchParams")&&n.d(t,{useSearchParams:function(){return s.useSearchParams}})},34097:function(e,t,n){"use strict";n.r(t);var s=n(57437),i=n(2265),r=n(99376),a=n(18913);n(32242),n(51980),n(13763),t.default=()=>{let e=(0,r.useParams)(),t=(0,r.useRouter)(),[n,c]=(0,i.useState)(""),[o,l]=(0,i.useState)(2),u="TechCorp Solutions",d=[{id:"renew-as-is",title:"Renew As-Is",description:"Keep all existing plan terms, benefits, and premium structures unchanged",icon:(0,s.jsx)(a.zHJ,{size:24}),recommended:!0,features:["Fastest processing time","Automatic rate rollover"],benefits:["No disruption to employees","Same plan codes and IDs"]},{id:"copy-modify",title:"Copy & Modify Plans",description:"Start with current plans but make adjustments to rates, benefits, or terms",icon:(0,s.jsx)(a.bnh,{size:24}),features:["Flexible rate adjustments","Modify plan names"],benefits:["Update deductibles/copays","Change carrier terms"]},{id:"major-changes",title:"Major Plan Changes",description:"Significant modifications including new carriers, plan designs, or benefit structures",icon:(0,s.jsx)(a.Moc,{size:24}),features:["New carrier integration","Benefit restructuring"],benefits:["Plan design overhaul","Custom plan creation"]}],m=[{number:1,title:"Review Current Plans",subtitle:"View existing benefit plans",active:!1},{number:2,title:"Renewal Options",subtitle:"Choose renewal type",active:2===o},{number:3,title:"Plan Configuration",subtitle:"Set dates and modifications",active:!1},{number:4,title:"Document Upload",subtitle:"Upload plan documents",active:!1},{number:5,title:"Validation",subtitle:"Review and validate setup",active:!1},{number:6,title:"Finalize",subtitle:"Complete renewal process",active:!1},{number:7,title:"Export",subtitle:"Download and share data",active:!1}],p=e=>{c(e)};return(0,s.jsxs)("div",{className:"plan-renewal-detail",children:[(0,s.jsxs)("div",{className:"detail-header",children:[(0,s.jsxs)("button",{className:"back-btn",onClick:()=>t.push("/ai-enroller/renewal"),children:[(0,s.jsx)(a.Tsu,{size:20}),"Back to Dashboard"]}),(0,s.jsxs)("div",{className:"header-info",children:[(0,s.jsx)("h1",{children:"Plan Renewal"}),(0,s.jsx)("h2",{children:u}),(0,s.jsxs)("div",{className:"step-indicator",children:["Step ",o," of 7"]})]}),(0,s.jsx)("div",{className:"completion-status",children:"29% Complete"})]}),(0,s.jsx)("div",{className:"renewal-steps",children:m.map((e,t)=>(0,s.jsxs)("div",{className:"renewal-step ".concat(e.active?"active":""),children:[(0,s.jsx)("div",{className:"step-number",children:e.number}),(0,s.jsxs)("div",{className:"step-content",children:[(0,s.jsx)("div",{className:"step-title",children:e.title}),(0,s.jsx)("div",{className:"step-subtitle",children:e.subtitle})]}),t<m.length-1&&(0,s.jsx)("div",{className:"step-connector"})]},e.number))}),(0,s.jsxs)("div",{className:"renewal-options-section",children:[(0,s.jsxs)("div",{className:"options-header",children:[(0,s.jsxs)("div",{className:"options-title",children:[(0,s.jsx)(a.zHJ,{size:20}),(0,s.jsx)("h3",{children:"Plan Renewal Strategy"})]}),(0,s.jsxs)("p",{children:["Choose how you want to approach the renewal process for ",u,". This will determine the workflow and options available in subsequent steps."]})]}),(0,s.jsx)("div",{className:"options-grid",children:d.map(e=>(0,s.jsxs)("div",{className:"option-card ".concat(n===e.id?"selected":""),onClick:()=>p(e.id),children:[e.recommended&&(0,s.jsx)("div",{className:"recommended-badge",children:"Recommended"}),(0,s.jsxs)("div",{className:"option-header",children:[(0,s.jsx)("div",{className:"option-select",children:(0,s.jsx)("div",{className:"radio-btn ".concat(n===e.id?"selected":""),children:n===e.id&&(0,s.jsx)(a.PjL,{size:16})})}),(0,s.jsx)("div",{className:"option-icon",children:e.icon}),(0,s.jsxs)("div",{className:"option-info",children:[(0,s.jsx)("h4",{children:e.title}),(0,s.jsx)("p",{children:e.description})]})]}),(0,s.jsxs)("div",{className:"option-features",children:[(0,s.jsxs)("div",{className:"features-section",children:[(0,s.jsx)("h5",{children:"Key Features:"}),(0,s.jsx)("ul",{children:e.features.map((e,t)=>(0,s.jsx)("li",{children:e},t))})]}),(0,s.jsx)("div",{className:"benefits-section",children:(0,s.jsx)("ul",{children:e.benefits.map((e,t)=>(0,s.jsx)("li",{children:e},t))})})]})]},e.id))}),(0,s.jsxs)("div",{className:"navigation-section",children:[(0,s.jsxs)("button",{className:"nav-btn secondary",onClick:()=>{t.back()},children:[(0,s.jsx)(a.Tsu,{size:16}),"Previous"]}),(0,s.jsx)("button",{className:"nav-btn primary ".concat(n?"enabled":"disabled"),onClick:()=>{n&&t.push("/ai-enroller/renewal/".concat(e.groupId,"/plan-configuration?option=").concat(n))},disabled:!n,children:"Continue"})]})]})]})}},51980:function(){},13763:function(){},32242:function(){},46231:function(e,t,n){"use strict";n.d(t,{w_:function(){return u}});var s=n(2265),i={color:void 0,size:void 0,className:void 0,style:void 0,attr:void 0},r=s.createContext&&s.createContext(i),a=["attr","size","title"];function c(){return(c=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var s in n)Object.prototype.hasOwnProperty.call(n,s)&&(e[s]=n[s])}return e}).apply(this,arguments)}function o(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(e);t&&(s=s.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,s)}return n}function l(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?o(Object(n),!0).forEach(function(t){var s,i;s=t,i=n[t],(s=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var s=n.call(e,t||"default");if("object"!=typeof s)return s;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(s))in e?Object.defineProperty(e,s,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[s]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):o(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}function u(e){return t=>s.createElement(d,c({attr:l({},e.attr)},t),function e(t){return t&&t.map((t,n)=>s.createElement(t.tag,l({key:n},t.attr),e(t.child)))}(e.child))}function d(e){var t=t=>{var n,{attr:i,size:r,title:o}=e,u=function(e,t){if(null==e)return{};var n,s,i=function(e,t){if(null==e)return{};var n={};for(var s in e)if(Object.prototype.hasOwnProperty.call(e,s)){if(t.indexOf(s)>=0)continue;n[s]=e[s]}return n}(e,t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);for(s=0;s<r.length;s++)n=r[s],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(i[n]=e[n])}return i}(e,a),d=r||t.size||"1em";return t.className&&(n=t.className),e.className&&(n=(n?n+" ":"")+e.className),s.createElement("svg",c({stroke:"currentColor",fill:"currentColor",strokeWidth:"0"},t.attr,i,u,{className:n,style:l(l({color:e.color||t.color},t.style),e.style),height:d,width:d,xmlns:"http://www.w3.org/2000/svg"}),o&&s.createElement("title",null,o),e.children)};return void 0!==r?s.createElement(r.Consumer,null,e=>t(e)):t(i)}}},function(e){e.O(0,[3417,7784,8422,2971,2117,1744],function(){return e(e.s=88855)}),_N_E=e.O()}]);