(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9988],{81855:function(e,t,s){Promise.resolve().then(s.bind(s,76970))},99376:function(e,t,s){"use strict";var i=s(35475);s.o(i,"useParams")&&s.d(t,{useParams:function(){return i.useParams}}),s.o(i,"usePathname")&&s.d(t,{usePathname:function(){return i.usePathname}}),s.o(i,"useRouter")&&s.d(t,{useRouter:function(){return i.useRouter}}),s.o(i,"useSearchParams")&&s.d(t,{useSearchParams:function(){return i.useSearchParams}})},76970:function(e,t,s){"use strict";s.r(t);var i=s(57437),a=s(2265),n=s(99376),r=s(18913);s(32242),s(51980),s(71637),t.default=()=>{let e=(0,n.useParams)(),t=(0,n.useRouter)(),[s,l]=(0,a.useState)(5),c="Green Valley Manufacturing",o=[{id:"eligibility",title:"Eligibility",description:"All employee tiers and age bands configured correctly",details:"142 employees eligible for medical, 138 for dental",status:"passed"},{id:"premiums",title:"Premiums",description:"Medical premium increase detected",details:"Employee-only rate increased by 8.2% from previous year",status:"warning"},{id:"compliance",title:"Compliance",description:"All compliance requirements met",details:"SBC documents uploaded, ACA requirements satisfied",status:"passed"},{id:"data-integrity",title:"Data Integrity",description:"No missing data detected",details:"All required fields populated for each plan tier",status:"passed"},{id:"carrier-sync",title:"Carrier Sync",description:"Carrier rate verification pending",details:"Recommend confirming rates with BCBS before finalizing",status:"warning"}],d=[{number:1,title:"Review Current Plans",subtitle:"View existing benefit plans",active:!1,completed:!0},{number:2,title:"Renewal Options",subtitle:"Choose renewal type",active:!1,completed:!0},{number:3,title:"Plan Configuration",subtitle:"Set dates and modifications",active:!1,completed:!0},{number:4,title:"Document Upload",subtitle:"Upload plan documents",active:!1,completed:!0},{number:5,title:"Validation",subtitle:"Review and validate setup",active:5===s},{number:6,title:"Finalize",subtitle:"Complete renewal process",active:!1},{number:7,title:"Export",subtitle:"Download and share data",active:!1}],u=o.filter(e=>"passed"===e.status).length,m=o.filter(e=>"warning"===e.status).length,h=o.filter(e=>"failed"===e.status).length,p=e=>{switch(e){case"passed":return(0,i.jsx)(r.PjL,{size:20,className:"status-icon passed"});case"warning":return(0,i.jsx)(r.baL,{size:20,className:"status-icon warning"});case"failed":return(0,i.jsx)(r.baL,{size:20,className:"status-icon failed"});default:return null}};return(0,i.jsxs)("div",{className:"plan-renewal-detail",children:[(0,i.jsxs)("div",{className:"detail-header",children:[(0,i.jsxs)("button",{className:"back-btn",onClick:()=>t.push("/ai-enroller/renewal"),children:[(0,i.jsx)(r.Tsu,{size:20}),"Back to Dashboard"]}),(0,i.jsxs)("div",{className:"header-info",children:[(0,i.jsx)("h1",{children:"Plan Renewal"}),(0,i.jsx)("h2",{children:c}),(0,i.jsxs)("div",{className:"step-indicator",children:["Step ",s," of 7"]})]}),(0,i.jsx)("div",{className:"completion-status",children:"71% Complete"})]}),(0,i.jsx)("div",{className:"renewal-steps",children:d.map((e,t)=>(0,i.jsxs)("div",{className:"renewal-step ".concat(e.active?"active":""," ").concat(e.completed?"completed":""),children:[(0,i.jsx)("div",{className:"step-number",children:e.completed?"✓":e.number}),(0,i.jsxs)("div",{className:"step-content",children:[(0,i.jsx)("div",{className:"step-title",children:e.title}),(0,i.jsx)("div",{className:"step-subtitle",children:e.subtitle})]}),t<d.length-1&&(0,i.jsx)("div",{className:"step-connector"})]},e.number))}),(0,i.jsxs)("div",{className:"validation-section",children:[(0,i.jsxs)("div",{className:"validation-header",children:[(0,i.jsxs)("div",{className:"validation-title",children:[(0,i.jsx)(r.c3K,{size:20}),(0,i.jsx)("h3",{children:"Plan Setup Validation"})]}),(0,i.jsxs)("p",{children:["Reviewing plan configuration for ",c," to ensure everything is set up correctly before finalizing."]})]}),(0,i.jsxs)("div",{className:"validation-content",children:[(0,i.jsx)("div",{className:"validation-checks",children:o.map(e=>(0,i.jsxs)("div",{className:"validation-check ".concat(e.status),children:[(0,i.jsxs)("div",{className:"check-header",children:[p(e.status),(0,i.jsxs)("div",{className:"check-info",children:[(0,i.jsx)("h4",{children:e.title}),(0,i.jsx)("p",{children:e.description})]}),(0,i.jsx)("div",{className:"check-status",children:e.status})]}),(0,i.jsx)("div",{className:"check-details",children:e.details})]},e.id))}),(0,i.jsxs)("div",{className:"validation-summary",children:[(0,i.jsx)("h4",{children:"Validation Summary"}),(0,i.jsxs)("div",{className:"summary-stats",children:[(0,i.jsxs)("div",{className:"stat-item passed",children:[(0,i.jsx)("div",{className:"stat-number",children:u}),(0,i.jsx)("div",{className:"stat-label",children:"Checks Passed"})]}),(0,i.jsxs)("div",{className:"stat-item warning",children:[(0,i.jsx)("div",{className:"stat-number",children:m}),(0,i.jsx)("div",{className:"stat-label",children:"Warnings"})]}),(0,i.jsxs)("div",{className:"stat-item failed",children:[(0,i.jsx)("div",{className:"stat-number",children:h}),(0,i.jsx)("div",{className:"stat-label",children:"Failed Checks"})]})]})]}),m>0&&(0,i.jsxs)("div",{className:"warnings-notice",children:[(0,i.jsx)(r.baL,{size:20}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h4",{children:"Warnings Detected"}),(0,i.jsx)("p",{children:"Your plan setup has some warnings but no critical issues. You can proceed with the renewal, but we recommend reviewing the flagged items."})]})]})]}),(0,i.jsxs)("div",{className:"navigation-section",children:[(0,i.jsxs)("button",{className:"nav-btn secondary",onClick:()=>{t.back()},children:[(0,i.jsx)(r.Tsu,{size:16}),"Previous"]}),(0,i.jsxs)("div",{className:"nav-actions",children:[(0,i.jsx)("button",{className:"nav-btn secondary",onClick:()=>{console.log("Re-running validation...")},children:"Re-run Validation"}),(0,i.jsx)("button",{className:"nav-btn primary enabled",onClick:()=>{t.push("/ai-enroller/renewal/".concat(e.groupId,"/finalize"))},children:"Continue"})]})]})]})]})}},51980:function(){},71637:function(){},32242:function(){},46231:function(e,t,s){"use strict";s.d(t,{w_:function(){return d}});var i=s(2265),a={color:void 0,size:void 0,className:void 0,style:void 0,attr:void 0},n=i.createContext&&i.createContext(a),r=["attr","size","title"];function l(){return(l=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var s=arguments[t];for(var i in s)Object.prototype.hasOwnProperty.call(s,i)&&(e[i]=s[i])}return e}).apply(this,arguments)}function c(e,t){var s=Object.keys(e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);t&&(i=i.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),s.push.apply(s,i)}return s}function o(e){for(var t=1;t<arguments.length;t++){var s=null!=arguments[t]?arguments[t]:{};t%2?c(Object(s),!0).forEach(function(t){var i,a;i=t,a=s[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var s=e[Symbol.toPrimitive];if(void 0!==s){var i=s.call(e,t||"default");if("object"!=typeof i)return i;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in e?Object.defineProperty(e,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):e[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(s)):c(Object(s)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(s,t))})}return e}function d(e){return t=>i.createElement(u,l({attr:o({},e.attr)},t),function e(t){return t&&t.map((t,s)=>i.createElement(t.tag,o({key:s},t.attr),e(t.child)))}(e.child))}function u(e){var t=t=>{var s,{attr:a,size:n,title:c}=e,d=function(e,t){if(null==e)return{};var s,i,a=function(e,t){if(null==e)return{};var s={};for(var i in e)if(Object.prototype.hasOwnProperty.call(e,i)){if(t.indexOf(i)>=0)continue;s[i]=e[i]}return s}(e,t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);for(i=0;i<n.length;i++)s=n[i],!(t.indexOf(s)>=0)&&Object.prototype.propertyIsEnumerable.call(e,s)&&(a[s]=e[s])}return a}(e,r),u=n||t.size||"1em";return t.className&&(s=t.className),e.className&&(s=(s?s+" ":"")+e.className),i.createElement("svg",l({stroke:"currentColor",fill:"currentColor",strokeWidth:"0"},t.attr,a,d,{className:s,style:o(o({color:e.color||t.color},t.style),e.style),height:u,width:u,xmlns:"http://www.w3.org/2000/svg"}),c&&i.createElement("title",null,c),e.children)};return void 0!==n?i.createElement(n.Consumer,null,e=>t(e)):t(a)}}},function(e){e.O(0,[3417,7189,8422,2971,2117,1744],function(){return e(e.s=81855)}),_N_E=e.O()}]);