(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3134],{44050:function(e,r,t){Promise.resolve().then(t.bind(t,37988))},99376:function(e,r,t){"use strict";var n=t(35475);t.o(n,"useParams")&&t.d(r,{useParams:function(){return n.useParams}}),t.o(n,"usePathname")&&t.d(r,{usePathname:function(){return n.usePathname}}),t.o(n,"useRouter")&&t.d(r,{useRouter:function(){return n.useRouter}}),t.o(n,"useSearchParams")&&t.d(r,{useSearchParams:function(){return n.useSearchParams}})},37988:function(e,r,t){"use strict";t.r(r);var n=t(57437);t(2265);var o=t(99376),i=t(18913);r.default=()=>{let e=(0,o.useRouter)(),r=()=>{e.push("/ai-enroller")};return(0,n.jsx)("div",{className:"renewal-wrapper",children:(0,n.jsxs)("div",{style:{maxWidth:"800px",margin:"0 auto",padding:"2rem",fontFamily:"SF Pro Text, -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, sans-serif"},children:[(0,n.jsx)("div",{style:{marginBottom:"2rem"},children:(0,n.jsxs)("button",{onClick:r,style:{display:"flex",alignItems:"center",gap:"0.5rem",background:"white",border:"1px solid #e5e7eb",padding:"0.5rem 1rem",borderRadius:"0.5rem",color:"#374151",cursor:"pointer",fontSize:"0.875rem",fontWeight:"500"},children:[(0,n.jsx)(i.Tsu,{size:16}),"Back to Main"]})}),(0,n.jsxs)("div",{style:{display:"flex",alignItems:"flex-start",gap:"1rem",background:"white",padding:"1.5rem",borderRadius:"1rem",boxShadow:"0 2px 8px rgba(0, 0, 0, 0.04)",border:"1px solid #e2e8f0",marginBottom:"2rem"},children:[(0,n.jsx)("div",{style:{width:"2.5rem",height:"2.5rem",background:"linear-gradient(135deg, #8b5cf6 0%, #a855f7 100%)",borderRadius:"50%",display:"flex",alignItems:"center",justifyContent:"center",fontSize:"1.25rem",flexShrink:0},children:"\uD83E\uDD16"}),(0,n.jsx)("div",{style:{flex:1,fontSize:"0.875rem",color:"#374151",lineHeight:1.6},children:"Welcome to the Group Plan Management section! This feature is coming soon. You'll be able to assign plans to employer groups and manage renewals here."})]}),(0,n.jsxs)("div",{style:{background:"white",border:"1px solid #e2e8f0",borderRadius:"1rem",padding:"3rem 2rem",textAlign:"center",boxShadow:"0 2px 8px rgba(0, 0, 0, 0.04)"},children:[(0,n.jsx)("div",{style:{fontSize:"3rem",marginBottom:"1rem"},children:"\uD83D\uDEA7"}),(0,n.jsx)("h2",{style:{fontSize:"1.5rem",fontWeight:"600",color:"#1e293b",marginBottom:"1rem"},children:"Group Plan Management"}),(0,n.jsx)("p",{style:{fontSize:"1rem",color:"#64748b",marginBottom:"2rem",maxWidth:"400px",margin:"0 auto 2rem auto"},children:"This section will allow you to assign insurance plans to employer groups and manage plan renewals."}),(0,n.jsxs)("div",{style:{background:"#f8fafc",border:"1px solid #e2e8f0",borderRadius:"0.75rem",padding:"1.5rem",marginBottom:"2rem"},children:[(0,n.jsx)("h3",{style:{fontSize:"1rem",fontWeight:"600",color:"#1e293b",marginBottom:"1rem"},children:"Coming Features:"}),(0,n.jsxs)("ul",{style:{textAlign:"left",color:"#374151",fontSize:"0.875rem",lineHeight:1.6,margin:0,paddingLeft:"1.5rem"},children:[(0,n.jsx)("li",{children:"Assign plans to employer groups"}),(0,n.jsx)("li",{children:"Manage plan renewals and updates"}),(0,n.jsx)("li",{children:"Track group enrollment status"}),(0,n.jsx)("li",{children:"Generate renewal reports"}),(0,n.jsx)("li",{children:"Automated renewal notifications"})]})]}),(0,n.jsx)("button",{onClick:r,style:{background:"#8b5cf6",color:"white",border:"none",padding:"0.75rem 1.5rem",borderRadius:"0.5rem",fontSize:"0.875rem",fontWeight:"600",cursor:"pointer"},children:"Back to Main Menu"})]})]})})}},46231:function(e,r,t){"use strict";t.d(r,{w_:function(){return u}});var n=t(2265),o={color:void 0,size:void 0,className:void 0,style:void 0,attr:void 0},i=n.createContext&&n.createContext(o),a=["attr","size","title"];function s(){return(s=Object.assign?Object.assign.bind():function(e){for(var r=1;r<arguments.length;r++){var t=arguments[r];for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])}return e}).apply(this,arguments)}function l(e,r){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);r&&(n=n.filter(function(r){return Object.getOwnPropertyDescriptor(e,r).enumerable})),t.push.apply(t,n)}return t}function c(e){for(var r=1;r<arguments.length;r++){var t=null!=arguments[r]?arguments[r]:{};r%2?l(Object(t),!0).forEach(function(r){var n,o;n=r,o=t[r],(n=function(e){var r=function(e,r){if("object"!=typeof e||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var n=t.call(e,r||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===r?String:Number)(e)}(e,"string");return"symbol"==typeof r?r:r+""}(n))in e?Object.defineProperty(e,n,{value:o,enumerable:!0,configurable:!0,writable:!0}):e[n]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):l(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}function u(e){return r=>n.createElement(m,s({attr:c({},e.attr)},r),function e(r){return r&&r.map((r,t)=>n.createElement(r.tag,c({key:t},r.attr),e(r.child)))}(e.child))}function m(e){var r=r=>{var t,{attr:o,size:i,title:l}=e,u=function(e,r){if(null==e)return{};var t,n,o=function(e,r){if(null==e)return{};var t={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(r.indexOf(n)>=0)continue;t[n]=e[n]}return t}(e,r);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)t=i[n],!(r.indexOf(t)>=0)&&Object.prototype.propertyIsEnumerable.call(e,t)&&(o[t]=e[t])}return o}(e,a),m=i||r.size||"1em";return r.className&&(t=r.className),e.className&&(t=(t?t+" ":"")+e.className),n.createElement("svg",s({stroke:"currentColor",fill:"currentColor",strokeWidth:"0"},r.attr,o,u,{className:t,style:c(c({color:e.color||r.color},r.style),e.style),height:m,width:m,xmlns:"http://www.w3.org/2000/svg"}),l&&n.createElement("title",null,l),e.children)};return void 0!==i?n.createElement(i.Consumer,null,e=>r(e)):r(o)}}},function(e){e.O(0,[8422,2971,2117,1744],function(){return e(e.s=44050)}),_N_E=e.O()}]);