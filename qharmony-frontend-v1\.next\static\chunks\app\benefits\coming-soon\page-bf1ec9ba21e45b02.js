(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6604],{84477:function(e,t,r){Promise.resolve().then(r.bind(r,73332))},40256:function(e,t,r){"use strict";r.d(t,{$R:function(){return d},A_:function(){return a},BO:function(){return o},GH:function(){return x},_n:function(){return s},be:function(){return i},iG:function(){return c},j0:function(){return l}});var n=r(83464);let s="http://localhost:8080",o="<EMAIL>,<EMAIL>,<EMAIL>".split(",").map(e=>e.trim()),i=n.Z.create({baseURL:s});async function a(e,t,r){let n=new URL(r?"".concat(r).concat(e):"".concat(s).concat(e));return t&&Object.keys(t).forEach(e=>n.searchParams.append(e,t[e])),(await i.get(n.toString())).data}async function l(e,t,r){let n=r?"".concat(r).concat(e):"".concat(s).concat(e),o=await i.post(n,t,{headers:{"Content-Type":"application/json"}});return{status:o.status,data:o.data}}async function c(e,t,r){let n=r?"".concat(r).concat(e):"".concat(s).concat(e);console.log("Document upload to: ".concat(n));let o=await i.post(n,t,{headers:{"Content-Type":"multipart/form-data"}});return{status:o.status,data:o.data}}async function d(e,t,r){let n=new URL(r?"".concat(r).concat(e):"".concat(s).concat(e));return t&&Object.keys(t).forEach(e=>n.searchParams.append(e,t[e])),console.log("GET Blob request to: ".concat(n.toString())),(await i.get(n.toString(),{responseType:"blob"})).data}async function x(e,t,r){let n=r?"".concat(r).concat(e):"".concat(s).concat(e),o=await i.put(n,t,{headers:{"Content-Type":"application/json"}});return{status:o.status,data:o.data}}i.interceptors.request.use(e=>{let t=localStorage.getItem("userid1")||localStorage.getItem("userId");return t?e.headers["user-id"]=t:console.warn("No user ID found in localStorage for API request"),e})},73332:function(e,t,r){"use strict";r.r(t),r.d(t,{default:function(){return O}});var n=r(57437),s=r(30166),o=r(2265),i=r(31691),a=r(60062),l=r(95656),c=r(85765),d=r(46387),x=r(97312),p=r(97404),u=r(94013),h=r(40256),m=r(71495),f=r(71004),y=r(59832),g=r(92253),b=r(15273),j=r(73261),v=r(11741),S=r(67051),w=r(27648),k=r(33145),Z=r(61910),F=r(8430);let C=[{text:"Why BenOsphere\xae",href:"https://benosphere.com/#why"},{text:"Solutions",href:"https://benosphere.com/#benifits"},{text:"How It Works",href:"https://benosphere.com/#workflow"},{text:"Census",href:"/census"},{text:"Pricing",href:"https://benosphere.com/#section-pricing"},{text:"FAQ",href:"https://benosphere.com/#faq"},{text:"Blog",href:"https://benosphere.com/blog"}];var D=e=>{let{hideGetStarted:t=!1}=e,[r,s]=(0,o.useState)(!1),[x,p]=(0,o.useState)(!1);(0,i.default)();let h=(0,a.Z)("(max-width:767px)",{noSsr:!0}),D=()=>{s(window.scrollY>10)};(0,o.useEffect)(()=>(window.addEventListener("scroll",D),()=>window.removeEventListener("scroll",D)),[]);let I=()=>{p(!x)};return(0,n.jsxs)(m.Z,{position:"fixed",elevation:r?4:0,sx:{backgroundColor:r?"rgba(0, 0, 0, 0.85)":"transparent",color:"#fff",transition:"background-color 0.3s ease",boxShadow:r?"0 0 8px rgba(0,0,0,0.3)":"none",backdropFilter:"blur(8px)",zIndex:1300},children:[(0,n.jsx)(c.Z,{maxWidth:"lg",children:(0,n.jsxs)(f.Z,{disableGutters:!0,sx:{display:"flex",justifyContent:"space-between",height:64},children:[(0,n.jsx)(l.Z,{sx:{display:"flex",alignItems:"center"},children:(0,n.jsx)(w.default,{href:"https://benosphere.com/",passHref:!0,style:{textDecoration:"none"},children:(0,n.jsx)(l.Z,{sx:{display:"flex",alignItems:"center",cursor:"pointer"},children:(0,n.jsx)(k.default,{src:"/logo1.png",alt:"Benosphere Logo",width:160,height:160})})})}),!h&&(0,n.jsxs)(l.Z,{sx:{display:"flex",alignItems:"center",gap:4},children:[C.map(e=>(0,n.jsx)(w.default,{href:e.href,passHref:!0,style:{textDecoration:"none"},children:(0,n.jsx)(d.Z,{sx:{color:"#fff",textDecoration:"none",fontSize:"1rem",cursor:"pointer","&:hover":{color:"#B88BFF"},transition:"color 0.2s ease"},children:e.text})},e.href)),!t&&(0,n.jsx)(w.default,{href:"/onboard",passHref:!0,style:{textDecoration:"none"},children:(0,n.jsx)(u.Z,{variant:"contained",sx:{background:"linear-gradient(to bottom, #6C42FF, #B88BFF)",color:"#fff",textTransform:"none",fontWeight:600,px:2.5,py:1,borderRadius:"8px","&:hover":{opacity:.9}},children:"Get Started for Free"})})]}),h&&(0,n.jsx)(y.Z,{edge:"end",color:"inherit","aria-label":"menu",onClick:I,sx:{ml:2},children:(0,n.jsx)(Z.Z,{})})]})}),(0,n.jsx)(g.ZP,{anchor:"right",open:x,onClose:I,PaperProps:{sx:{width:"250px",maxWidth:"80%",backgroundColor:"rgba(0, 0, 0, 0.95)",color:"#fff"}},children:(0,n.jsxs)(l.Z,{sx:{p:1.5},children:[" ",(0,n.jsx)(l.Z,{sx:{display:"flex",justifyContent:"flex-end"},children:(0,n.jsx)(y.Z,{color:"inherit",onClick:I,sx:{padding:"6px","& .MuiSvgIcon-root":{fontSize:"1.25rem"}},children:(0,n.jsx)(F.Z,{})})}),(0,n.jsxs)(b.Z,{children:[C.map(e=>(0,n.jsx)(w.default,{href:e.href,passHref:!0,style:{textDecoration:"none"},children:(0,n.jsx)(j.ZP,{disablePadding:!0,children:(0,n.jsx)(v.Z,{onClick:I,sx:{px:1.5,py:1,"&:hover":{backgroundColor:"rgba(255, 255, 255, 0.08)"}},children:(0,n.jsx)(S.Z,{primary:e.text,sx:{"& .MuiListItemText-primary":{color:"#fff",fontSize:"1rem",fontWeight:500}}})})})},e.href)),!t&&(0,n.jsx)(j.ZP,{sx:{mt:4},children:(0,n.jsx)(w.default,{href:"https://app.benosphere.com/onboard",passHref:!0,style:{width:"100%",textDecoration:"none"},children:(0,n.jsx)(u.Z,{fullWidth:!0,variant:"contained",onClick:I,sx:{background:"linear-gradient(to bottom, #6C42FF, #B88BFF)",color:"#fff",textTransform:"none",fontWeight:600,py:1.5,borderRadius:"8px","&:hover":{opacity:.9}},children:"Get Started for Free"})})})]})]})})]})},I=()=>{let e=(0,i.default)();return(0,a.Z)(e.breakpoints.down("sm")),(0,n.jsx)(l.Z,{component:"footer",sx:{bgcolor:"#000",color:"#f0f0f0",py:{xs:2,sm:3},px:{xs:1,sm:2},mt:{xs:6,sm:10}},children:(0,n.jsx)(c.Z,{maxWidth:"md",children:(0,n.jsxs)(l.Z,{sx:{display:"flex",flexDirection:{xs:"column",sm:"row"},justifyContent:"center",alignItems:"center",flexWrap:"wrap",textAlign:"center",gap:{xs:1,sm:2}},children:[(0,n.jsx)(d.Z,{variant:"body2",sx:{fontSize:{xs:"0.75rem",sm:"0.875rem"},order:{xs:1,sm:1},color:"#888"},children:"\xa9 BenOsphere 2025"}),(0,n.jsxs)(l.Z,{sx:{display:"flex",flexDirection:{xs:"column",sm:"row"},gap:{xs:1,sm:2},order:{xs:3,sm:2}},children:[(0,n.jsx)(x.Z,{href:"https://benosphere.com/terms-conditions",target:"_blank",rel:"noopener noreferrer",sx:{color:"#888",textDecoration:"none",fontSize:{xs:"0.75rem",sm:"0.875rem"},"&:hover":{textDecoration:"underline"}},children:"Terms of Service"}),(0,n.jsx)(x.Z,{href:"https://benosphere.com/privacy-policy",target:"_blank",rel:"noopener noreferrer",sx:{color:"#888",textDecoration:"none",fontSize:{xs:"0.75rem",sm:"0.875rem"},"&:hover":{textDecoration:"underline"}},children:"Privacy Policy"}),(0,n.jsx)(x.Z,{href:"mailto:<EMAIL>",sx:{color:"#888",textDecoration:"none",fontSize:{xs:"0.75rem",sm:"0.875rem"},"&:hover":{textDecoration:"underline"}},children:"<EMAIL>"})]}),(0,n.jsx)(d.Z,{variant:"body2",sx:{fontSize:{xs:"0.75rem",sm:"0.875rem"},order:{xs:2,sm:3},color:"#888"},children:"\uD83E\uDD0D With love from Seattle"})]})})})},T=r(82279),$=r(99376),z=r(49089),M=r(61273),P=e=>{let{title:t}=e,r=(0,$.usePathname)(),s=encodeURIComponent("https://app.benosphere.com".concat(r)),i=encodeURIComponent(t),[a,l]=(0,o.useState)(!1),[c,d]=(0,o.useState)(!1),[x,p]=(0,o.useState)(!1);return(0,o.useEffect)(()=>{(()=>{let e=navigator.userAgent||navigator.vendor||window.opera||"";l(/android|iphone|ipad|ipod|blackberry|windows phone/i.test(e)),d(/iphone|ipad|ipod/i.test(e)),p(/android/i.test(e))})()},[]),(0,n.jsxs)("div",{style:{display:"flex",gap:"12px",alignItems:"center"},children:[(0,n.jsx)("a",{href:"https://twitter.com/intent/tweet?url=".concat(s,"&text=").concat(i),target:"_blank",rel:"noopener noreferrer",title:"Share on X",style:{display:"inline-block",transition:"transform 0.3s ease"},onMouseOver:e=>{e.currentTarget.style.transform="scale(1.2)",e.currentTarget.style.opacity="0.8"},onMouseOut:e=>{e.currentTarget.style.transform="scale(1)",e.currentTarget.style.opacity="1"},children:(0,n.jsx)(M.lcJ,{size:24,color:"#FFFFFF"})}),(0,n.jsx)("a",{href:"https://www.linkedin.com/sharing/share-offsite/?url=".concat(s,"&text=").concat(i),target:"_blank",rel:"noopener noreferrer",title:"Share on LinkedIn",style:{display:"inline-block",transition:"transform 0.3s ease"},onMouseOver:e=>{e.currentTarget.style.transform="scale(1.2)",e.currentTarget.style.opacity="0.8"},onMouseOut:e=>{e.currentTarget.style.transform="scale(1)",e.currentTarget.style.opacity="1"},children:(0,n.jsx)(z.ltd,{size:24,color:"#0077B5"})}),(0,n.jsx)("a",{href:"https://api.whatsapp.com/send?text=".concat(i,"%20").concat(s),target:"_blank",rel:"noopener noreferrer",title:"Share on WhatsApp",style:{display:"inline-block",transition:"transform 0.3s ease"},onMouseOver:e=>{e.currentTarget.style.transform="scale(1.2)",e.currentTarget.style.opacity="0.8"},onMouseOut:e=>{e.currentTarget.style.transform="scale(1)",e.currentTarget.style.opacity="1"},children:(0,n.jsx)(z.xpo,{size:24,color:"#25D366"})}),a&&(0,n.jsx)("a",{href:c?"sms:&body=".concat(i,"%20").concat(s):"sms:?body=".concat(i,"%20").concat(s),title:"Share via SMS",style:{display:"inline-block",transition:"transform 0.3s ease"},onMouseOver:e=>{e.currentTarget.style.transform="scale(1.2)",e.currentTarget.style.opacity="0.8"},onMouseOut:e=>{e.currentTarget.style.transform="scale(1)",e.currentTarget.style.opacity="1"},children:(0,n.jsx)(z.bJx,{size:24,color:"#FFC107"})}),(!a||!c&&!x)&&(0,n.jsx)("a",{href:"https://www.facebook.com/sharer/sharer.php?u=".concat(s),target:"_blank",rel:"noopener noreferrer",title:"Share on Facebook",style:{display:"inline-block",transition:"transform 0.3s ease"},onMouseOver:e=>{e.currentTarget.style.transform="scale(1.2)",e.currentTarget.style.opacity="0.8"},onMouseOut:e=>{e.currentTarget.style.transform="scale(1)",e.currentTarget.style.opacity="1"},children:(0,n.jsx)(z.Am9,{size:24,color:"#1877F2"})})]})};let W=["❌ 1 in 4 skip checkups yearly","❌ 55% of PTO goes unused yearly","❌ Up to $500 wellness perks unused yearly","❌ 70% don't know their benefits"],A=[["Preventive care","$300","$500","Skipped annual exams, delayed diagnosis (CDC)"],["Unused PTO","$600","$1,200","Time off expired or forfeited (U.S. Travel)"],["FSA/HSA funds","$300","$600","Forgotten or unused funds (WageWorks)"],["Wellness perks","$150","$400","Gym/wellness stipends unused (SHRM)"],["Mental health support","$100","$250","Low EAP utilization (NAMI)"],["Financial/legal assistance","$50","$150","Underused (MetLife)"],["Tuition/learning stipends","$100","$300","Missed career development (SHRM)"],["Caregiver/childcare support","$100","$300","Not utilized (Care.com)"],["Subtotal: Unused Benefits","$1,700","$3,700",""],["Chronic condition development","$1,000","$3,000","Diabetes, hypertension, etc. (CDC, ADA)"],["Emergency/hospital visits","$500","$2,000","ER visits, preventable hospitalization (KFF)"],["Productivity loss","$500","$1,000","Presenteeism (IBI)"],["Absenteeism/disability","$500","$1,000","Short-term disability or leave (SHRM)"],["Subtotal: Health & Productivity Loss","$2,500","$7,000",""],["Total Estimated Loss per Employee","$4,200","$11,000","Rounded for clarity"]],B=()=>{(0,o.useEffect)(()=>{document.title="BenOsphere: Stop Losing Your Benefits Money"},[]);let e=(0,i.default)();(0,a.Z)(e.breakpoints.down("sm"));let[t,r]=(0,o.useState)(""),[s,m]=(0,o.useState)(""),[f,y]=(0,o.useState)(!1),[g,b]=(0,o.useState)(""),[j,v]=(0,o.useState)(!1),[S,w]=(0,o.useState)(!1),[k,Z]=(0,o.useState)(0),[F,C]=(0,o.useState)(279),$=(0,o.useRef)(null);(0,o.useEffect)(()=>{(async()=>{try{let e=await (0,h.A_)("/test/waitlist/entries");if(console.log("Waitlist response:",e),e&&e.entries&&Array.isArray(e.entries)){let t=279+e.entries.length;C(t),console.log("Setting target counter to:",t)}}catch(e){console.error("Error fetching waitlist entries:",e)}})()},[]),(0,o.useEffect)(()=>{$.current&&clearInterval($.current),Z(0);let e=Math.ceil(F/50);return $.current=setInterval(()=>{Z(t=>{let r=t+e;return r>=F?(clearInterval($.current),F):r})},40),()=>{$.current&&clearInterval($.current)}},[F]);let z=async e=>{if(e.preventDefault(),b(""),!t)return b("Email address is required.");if(!s)return b("Phone number is required.");if(10!==s.length||!/^\d{10}$/.test(s))return b("Phone number must be exactly 10 digits.");try{await (0,h.j0)("/test/waitlist/join",{email:t.toLowerCase(),phone:s,joinedAt:new Date().toISOString()}),C(e=>e+1),y(!0),w(!1)}catch(e){var r,n;console.error("Waitlist join error:",e),b((null==e?void 0:null===(n=e.response)||void 0===n?void 0:null===(r=n.data)||void 0===r?void 0:r.message)||"Failed to join waitlist. Please check your connection and try again.")}};return(0,n.jsxs)(l.Z,{sx:{bgcolor:"#000",color:"#fff",minHeight:"99vh",display:"flex",flexDirection:"column",fontFamily:'"SF Pro", -apple-system, BlinkMacSystemFont, sans-serif'},children:[(0,n.jsx)(D,{hideGetStarted:!0}),(0,n.jsx)(l.Z,{component:"main",sx:{flex:1,display:"flex",alignItems:"center",justifyContent:"center",py:{xs:1,md:3},pt:{xs:4,md:9}},children:(0,n.jsx)(c.Z,{maxWidth:"md",sx:{px:{xs:1,sm:2,md:3},display:"flex",alignItems:"center",justifyContent:"center",height:"100%",pt:{xs:2,md:6}},children:(0,n.jsxs)(l.Z,{textAlign:"center",maxWidth:900,mx:"auto",sx:{width:"100%",display:"flex",flexDirection:"column",alignItems:"center",gap:{xs:1,sm:1.5},py:{xs:1,sm:2}},children:[(0,n.jsx)(d.Z,{variant:"h1",sx:{fontSize:{xs:"1.5rem",sm:"2rem",md:"2.5rem"},fontWeight:600,mb:{xs:1,sm:2},mt:{xs:4,sm:2},lineHeight:1.2},children:"You could be losing a third of your paycheck."}),(0,n.jsx)(d.Z,{sx:{color:"#ccc",mb:{xs:.5,sm:1},fontSize:{xs:"0.875rem",sm:"1rem",md:"1.25rem"}},children:"Feeling sick while debt piles up? Don't skip the care your family already has."}),(0,n.jsxs)(x.Z,{href:"#",onClick:e=>{e.preventDefault(),v(!0)},sx:{color:"#B983FF",fontSize:{xs:"0.875rem",sm:"1rem"},textDecoration:"none",mb:{xs:1,sm:2},display:"inline-block","&:hover":{color:"#D4A5FF"},fontFamily:'"SF Pro", -apple-system, BlinkMacSystemFont, sans-serif'},children:[(0,n.jsx)("span",{style:{textDecoration:"none"},children:"\uD83D\uDC49"})," ",(0,n.jsx)("span",{style:{textDecoration:"underline"},children:"Find your lost employee benefits"})]}),(0,n.jsx)(l.Z,{sx:{display:"flex",flexWrap:"wrap",justifyContent:"center",gap:{xs:"4px",sm:"8px"},mb:{xs:1,sm:2}},children:W.map((e,t)=>(0,n.jsx)(l.Z,{sx:{bgcolor:"#111",border:"1px solid #333",borderRadius:"8px",px:{xs:1.5,sm:2},py:{xs:.5,sm:.75},fontSize:{xs:"0.8rem",sm:"0.95rem"},fontFamily:'"SF Pro", -apple-system, BlinkMacSystemFont, sans-serif'},children:e},t))}),(0,n.jsxs)(d.Z,{className:"counter-text",sx:{fontSize:{xs:"0.875rem",sm:"1rem"},color:"#ccc",mt:{xs:.5,sm:1}},children:["\uD83D\uDD25 ",k," people have already joined the waitlist"]}),f?(0,n.jsx)(T.Z,{severity:"success",sx:{mt:{xs:2.5,sm:3.5},maxWidth:{xs:"300px",sm:"400px"},mx:"auto","& .MuiAlert-message":{fontSize:{xs:"0.875rem",sm:"1rem"}}},children:"Thanks for joining our waitlist! We'll let you know when we launch."}):S?(0,n.jsxs)(l.Z,{component:"form",onSubmit:z,sx:{width:"100%",maxWidth:{xs:"280px",sm:"400px"},mx:"auto",mt:{xs:2.5,sm:3.5},display:"flex",flexDirection:"column",gap:{xs:1.5,sm:2}},children:[(0,n.jsx)(p.Z,{type:"email",placeholder:"Email address",value:t,onChange:e=>r(e.target.value),fullWidth:!0,required:!0,sx:{"& .MuiInputBase-root":{height:{xs:"35px",sm:"45px"},color:"#fff",fontSize:{xs:"0.75rem",sm:"1rem"}},"& .MuiOutlinedInput-notchedOutline":{borderColor:"#444"},"&:hover .MuiOutlinedInput-notchedOutline":{borderColor:"#666"}}}),(0,n.jsx)(p.Z,{type:"tel",placeholder:"Phone number",value:s,onChange:e=>m(e.target.value),fullWidth:!0,required:!0,sx:{"& .MuiInputBase-root":{height:{xs:"35px",sm:"45px"},color:"#fff",fontSize:{xs:"0.75rem",sm:"1rem"}},"& .MuiOutlinedInput-notchedOutline":{borderColor:"#444"},"&:hover .MuiOutlinedInput-notchedOutline":{borderColor:"#666"}}}),g&&(0,n.jsx)(T.Z,{severity:"error",sx:{fontSize:{xs:"0.75rem",sm:"0.875rem"}},children:g}),(0,n.jsx)(u.Z,{type:"submit",fullWidth:!0,variant:"contained",sx:{height:{xs:"32px",sm:"45px"},borderRadius:"8px",fontWeight:600,fontSize:{xs:"0.75rem",sm:"1rem"},background:"#B983FF",color:"#000","&:hover":{backgroundColor:"#D4A5FF"},textTransform:"none",mt:{xs:.5,sm:1}},children:"Save Your Paycheck"})]}):(0,n.jsx)(u.Z,{onClick:()=>w(!0),variant:"contained",sx:{mt:{xs:2.5,sm:3.5},px:{xs:3,sm:4},py:{xs:1,sm:1.5},borderRadius:"8px",fontWeight:600,fontSize:{xs:"0.875rem",sm:"1rem"},background:"#B983FF",color:"#000","&:hover":{backgroundColor:"#D4A5FF"},textTransform:"none"},children:"Save Your Paycheck"}),(0,n.jsxs)(l.Z,{sx:{mt:{xs:2,sm:3},display:"flex",justifyContent:"center",alignItems:"center",flexDirection:"column",gap:1},children:[(0,n.jsx)(d.Z,{sx:{fontSize:{xs:"0.875rem",sm:"1rem"},color:"#ccc",mb:1},children:"Help a teammate stop losing money — share this."}),(0,n.jsx)(P,{title:"Saw this and thought it might be helpful — we might be losing thousands in employee benefits."})]})]})})}),j&&(0,n.jsx)(l.Z,{sx:{position:"fixed",inset:0,bgcolor:"rgba(0, 0, 0, 0.7)",zIndex:999,display:"flex",alignItems:"center",justifyContent:"center"},onClick:()=>v(!1),children:(0,n.jsxs)(l.Z,{sx:{bgcolor:"#111",color:"#fff",p:3,borderRadius:2,border:"1px solid #333",width:"90%",maxWidth:600,maxHeight:"80vh",overflowY:"auto",boxShadow:"0 10px 30px rgba(0,0,0,0.5)"},onClick:e=>e.stopPropagation(),children:[(0,n.jsxs)(l.Z,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",mb:2},children:[(0,n.jsx)(d.Z,{variant:"h6",children:"You're losing up to $11,000 annually on benefits."}),(0,n.jsx)(u.Z,{onClick:()=>v(!1),sx:{color:"#aaa",minWidth:"auto",p:.5},children:"X"})]}),(0,n.jsx)(l.Z,{sx:{width:"100%",overflowX:"auto"},children:(0,n.jsxs)(l.Z,{component:"table",sx:{width:"100%",borderCollapse:"collapse",fontSize:"0.9rem"},children:[(0,n.jsx)("thead",{children:(0,n.jsxs)("tr",{children:[(0,n.jsx)("th",{style:{textAlign:"left",padding:"8px 12px",borderBottom:"1px solid #444"},children:"Category"}),(0,n.jsx)("th",{style:{textAlign:"right",padding:"8px 12px",borderBottom:"1px solid #444"},children:"Low"}),(0,n.jsx)("th",{style:{textAlign:"right",padding:"8px 12px",borderBottom:"1px solid #444"},children:"High"}),(0,n.jsx)("th",{style:{textAlign:"left",padding:"8px 12px",borderBottom:"1px solid #444"},children:"Notes"})]})}),(0,n.jsx)("tbody",{children:A.map((e,t)=>{let[r,s,o,i]=e;return(0,n.jsxs)("tr",{style:{background:[8,13].includes(t)?"#222":14===t?"#333":"transparent",fontWeight:[8,13,14].includes(t)?"bold":"normal"},children:[(0,n.jsx)("td",{style:{padding:"8px 12px"},children:r}),(0,n.jsx)("td",{style:{padding:"8px 12px",textAlign:"right"},children:s}),(0,n.jsx)("td",{style:{padding:"8px 12px",textAlign:"right"},children:o}),(0,n.jsx)("td",{style:{padding:"8px 12px",color:"#ccc"},children:i})]},t)})})]})}),(0,n.jsx)(d.Z,{sx:{fontSize:"0.8rem",mt:3,color:"#aaa",textAlign:"center"},children:"Disclaimer: National averages based on industry research. Actual losses vary per individual."})]})}),(0,n.jsx)(I,{})]})};var O=(0,s.default)(()=>Promise.resolve(B),{ssr:!1})}},function(e){e.O(0,[7699,7240,3463,3301,187,3145,3919,2786,9826,8166,7404,7648,5515,42,2971,2117,1744],function(){return e(e.s=84477)}),_N_E=e.O()}]);