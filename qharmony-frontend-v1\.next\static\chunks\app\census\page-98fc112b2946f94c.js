(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9681],{85543:function(e,t,s){Promise.resolve().then(s.bind(s,65165))},48968:function(e,t,s){"use strict";s.d(t,{pm:function(){return f}});var r=s(2265);let a=0,o=new Map,n=e=>{if(o.has(e))return;let t=setTimeout(()=>{o.delete(e),l({type:"REMOVE_TOAST",toastId:e})},1e6);o.set(e,t)},i=(e,t)=>{switch(t.type){case"ADD_TOAST":return{...e,toasts:[t.toast,...e.toasts].slice(0,1)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(e=>e.id===t.toast.id?{...e,...t.toast}:e)};case"DISMISS_TOAST":{let{toastId:s}=t;return s?n(s):e.toasts.forEach(e=>{n(e.id)}),{...e,toasts:e.toasts.map(e=>e.id===s||void 0===s?{...e,open:!1}:e)}}case"REMOVE_TOAST":if(void 0===t.toastId)return{...e,toasts:[]};return{...e,toasts:e.toasts.filter(e=>e.id!==t.toastId)}}},d=[],u={toasts:[]};function l(e){u=i(u,e),d.forEach(e=>{e(u)})}function c(e){let{...t}=e,s=(a=(a+1)%Number.MAX_SAFE_INTEGER).toString(),r=()=>l({type:"DISMISS_TOAST",toastId:s});return l({type:"ADD_TOAST",toast:{...t,id:s,open:!0,onOpenChange:e=>{e||r()}}}),{id:s,dismiss:r,update:e=>l({type:"UPDATE_TOAST",toast:{...e,id:s}})}}function f(){let[e,t]=r.useState(u);return r.useEffect(()=>(d.push(t),()=>{let e=d.indexOf(t);e>-1&&d.splice(e,1)}),[e]),{...e,toast:c,dismiss:e=>l({type:"DISMISS_TOAST",toastId:e})}}},25829:function(e,t,s){"use strict";s.d(t,{cn:function(){return o}});var r=s(61994),a=s(53335);function o(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return(0,a.m6)((0,r.W)(t))}},65165:function(e,t,s){"use strict";s.r(t),s.d(t,{default:function(){return j}});var r=s(57437),a=s(30166),o=s(2265),n=s(61312),i=s(25829);let d=n.zt;n.fC,n.xz,o.forwardRef((e,t)=>{let{className:s,sideOffset:a=4,...o}=e;return(0,r.jsx)(n.VY,{ref:t,sideOffset:a,className:(0,i.cn)("z-50 overflow-hidden rounded-md border bg-popover px-3 py-1.5 text-sm text-popover-foreground shadow-md animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",s),...o})}).displayName=n.VY.displayName;var u=s(48968),l=s(41915),c=s(90535),f=s(44986);let p=l.zt,m=o.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)(l.l_,{ref:t,className:(0,i.cn)("fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]",s),...a})});m.displayName=l.l_.displayName;let x=(0,c.j)("group pointer-events-auto relative flex w-full items-center justify-between space-x-4 overflow-hidden rounded-md border p-6 pr-8 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full",{variants:{variant:{default:"border bg-background text-foreground",destructive:"destructive group border-destructive bg-destructive text-destructive-foreground"}},defaultVariants:{variant:"default"}}),v=o.forwardRef((e,t)=>{let{className:s,variant:a,...o}=e;return(0,r.jsx)(l.fC,{ref:t,className:(0,i.cn)(x({variant:a}),s),...o})});v.displayName=l.fC.displayName,o.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)(l.aU,{ref:t,className:(0,i.cn)("inline-flex h-8 shrink-0 items-center justify-center rounded-md border bg-transparent px-3 text-sm font-medium ring-offset-background transition-colors hover:bg-secondary focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 group-[.destructive]:border-muted/40 group-[.destructive]:hover:border-destructive/30 group-[.destructive]:hover:bg-destructive group-[.destructive]:hover:text-destructive-foreground group-[.destructive]:focus:ring-destructive",s),...a})}).displayName=l.aU.displayName;let g=o.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)(l.x8,{ref:t,className:(0,i.cn)("absolute right-2 top-2 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-2 group-hover:opacity-100 group-[.destructive]:text-red-300 group-[.destructive]:hover:text-red-50 group-[.destructive]:focus:ring-red-400 group-[.destructive]:focus:ring-offset-red-600",s),"toast-close":"",...a,children:(0,r.jsx)(f.Z,{className:"h-4 w-4"})})});g.displayName=l.x8.displayName;let h=o.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)(l.Dx,{ref:t,className:(0,i.cn)("text-sm font-semibold",s),...a})});h.displayName=l.Dx.displayName;let b=o.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)(l.dk,{ref:t,className:(0,i.cn)("text-sm opacity-90",s),...a})});function y(){let{toasts:e}=(0,u.pm)();return(0,r.jsxs)(p,{children:[e.map(function(e){let{id:t,title:s,description:a,action:o,...n}=e;return(0,r.jsxs)(v,{...n,children:[(0,r.jsxs)("div",{className:"grid gap-1",children:[s&&(0,r.jsx)(h,{children:s}),a&&(0,r.jsx)(b,{children:a})]}),o,(0,r.jsx)(g,{})]},t)}),(0,r.jsx)(m,{})]})}b.displayName=l.dk.displayName;let N=(0,a.default)(()=>Promise.all([s.e(3301),s.e(8575),s.e(293),s.e(9129),s.e(545),s.e(3344),s.e(5675)]).then(s.bind(s,94705)),{loadableGenerated:{webpack:()=>[94705]},ssr:!1,loading:()=>(0,r.jsx)(w,{})});function w(){return(0,r.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"}),(0,r.jsx)("p",{className:"text-gray-600",children:"Loading census application..."})]})})}function j(){return(0,r.jsx)("div",{"data-page":"census",children:(0,r.jsxs)(d,{children:[(0,r.jsx)(y,{}),(0,r.jsx)(N,{})]})})}}},function(e){e.O(0,[32,8510,2971,2117,1744],function(){return e(e.s=85543)}),_N_E=e.O()}]);