(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7702],{21729:function(e,t,r){Promise.resolve().then(r.bind(r,24963))},96729:function(e,t,r){"use strict";var o=r(94630),a=r(57437);t.Z=(0,o.Z)((0,a.jsx)("path",{d:"M19.35 10.04C18.67 6.59 15.64 4 12 4 9.11 4 6.6 5.64 5.35 8.04 2.34 8.36 0 10.91 0 14c0 3.31 2.69 6 6 6h13c2.76 0 5-2.24 5-5 0-2.64-2.05-4.78-4.65-4.96M14 13v4h-4v-4H7l5-5 5 5z"}),"CloudUpload")},67116:function(e,t,r){"use strict";r.d(t,{Z:function(){return D}});var o=r(2265),a=r(61994),n=r(20801),i=r(16210),l=r(21086),s=r(37053),c=r(94630),d=r(57437),u=(0,c.Z)((0,d.jsx)("path",{d:"M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"}),"Person"),p=r(94143),f=r(50738);function h(e){return(0,f.ZP)("MuiAvatar",e)}(0,p.Z)("MuiAvatar",["root","colorDefault","circular","rounded","square","img","fallback"]);var x=r(79114);let m=e=>{let{classes:t,variant:r,colorDefault:o}=e;return(0,n.Z)({root:["root",r,o&&"colorDefault"],img:["img"],fallback:["fallback"]},h,t)},g=(0,i.default)("div",{name:"MuiAvatar",slot:"Root",overridesResolver:(e,t)=>{let{ownerState:r}=e;return[t.root,t[r.variant],r.colorDefault&&t.colorDefault]}})((0,l.Z)(e=>{let{theme:t}=e;return{position:"relative",display:"flex",alignItems:"center",justifyContent:"center",flexShrink:0,width:40,height:40,fontFamily:t.typography.fontFamily,fontSize:t.typography.pxToRem(20),lineHeight:1,borderRadius:"50%",overflow:"hidden",userSelect:"none",variants:[{props:{variant:"rounded"},style:{borderRadius:(t.vars||t).shape.borderRadius}},{props:{variant:"square"},style:{borderRadius:0}},{props:{colorDefault:!0},style:{color:(t.vars||t).palette.background.default,...t.vars?{backgroundColor:t.vars.palette.Avatar.defaultBg}:{backgroundColor:t.palette.grey[400],...t.applyStyles("dark",{backgroundColor:t.palette.grey[600]})}}}]}})),b=(0,i.default)("img",{name:"MuiAvatar",slot:"Img",overridesResolver:(e,t)=>t.img})({width:"100%",height:"100%",textAlign:"center",objectFit:"cover",color:"transparent",textIndent:1e4}),v=(0,i.default)(u,{name:"MuiAvatar",slot:"Fallback",overridesResolver:(e,t)=>t.fallback})({width:"75%",height:"75%"});var D=o.forwardRef(function(e,t){let r=(0,s.i)({props:e,name:"MuiAvatar"}),{alt:n,children:i,className:l,component:c="div",slots:u={},slotProps:p={},imgProps:f,sizes:h,src:D,srcSet:w,variant:y="circular",...k}=r,j=null,Z={...r,component:c,variant:y},C=function(e){let{crossOrigin:t,referrerPolicy:r,src:a,srcSet:n}=e,[i,l]=o.useState(!1);return o.useEffect(()=>{if(!a&&!n)return;l(!1);let e=!0,o=new Image;return o.onload=()=>{e&&l("loaded")},o.onerror=()=>{e&&l("error")},o.crossOrigin=t,o.referrerPolicy=r,o.src=a,n&&(o.srcset=n),()=>{e=!1}},[t,r,a,n]),i}({...f,..."function"==typeof p.img?p.img(Z):p.img,src:D,srcSet:w}),S=D||w,I=S&&"error"!==C;Z.colorDefault=!I,delete Z.ownerState;let R=m(Z),[F,W]=(0,x.Z)("img",{className:R.img,elementType:b,externalForwardedProps:{slots:u,slotProps:{img:{...f,...p.img}}},additionalProps:{alt:n,src:D,srcSet:w,sizes:h},ownerState:Z});return j=I?(0,d.jsx)(F,{...W}):i||0===i?i:S&&n?n[0]:(0,d.jsx)(v,{ownerState:Z,className:R.fallback}),(0,d.jsx)(g,{as:c,className:(0,a.Z)(R.root,l),ref:t,...k,ownerState:Z,children:j})})},24963:function(e,t,r){"use strict";r.r(t),r.d(t,{default:function(){return S}});var o=r(57437),a=r(95656),n=r(46387),i=r(89414),l=r(13571),s=r(48223),c=r(2265),d=r(67116),u=r(94013),p=r(56336),f=r(33145),h=r(70623),x=r(68575),m=r(99376);let g=[{label:"What benefits are available to me?",icon:"\uD83D\uDD25",description:"Top question asked"},{label:"How do I enroll in benefits?",icon:"\uD83E\uDDD9‍♂️"},{label:"What benefits are available to me?",icon:"\uD83D\uDD25",description:"Top question asked"},{label:"How do I enroll in benefits?",icon:"\uD83E\uDDD9‍♂️"},{label:"What benefits are available to me?",icon:"\uD83D\uDD25",description:"Top question asked"},{label:"How do I enroll in benefits?",icon:"\uD83E\uDDD9‍♂️"},{label:"What benefits are available to me?",icon:"\uD83D\uDD25",description:"Top question asked"},{label:"How do I enroll in benefits?",icon:"\uD83E\uDDD9‍♂️"},{label:"What benefits are available to me?",icon:"\uD83D\uDD25",description:"Top question asked"},{label:"How do I enroll in benefits?",icon:"\uD83E\uDDD9‍♂️"}],b=[{label:"What is covered under my healthcare insurance plan?",icon:"\uD83C\uDFDD️",description:"Top question asked"},{label:"How can I apply for the family medical leave act (FMLA)?",icon:"\uD83D\uDCC4"},{label:"What is covered under my healthcare insurance plan?",icon:"\uD83C\uDFDD️",description:"Top question asked"},{label:"How can I apply for the family medical leave act (FMLA)?",icon:"\uD83D\uDCC4"},{label:"What is covered under my healthcare insurance plan?",icon:"\uD83C\uDFDD️",description:"Top question asked"},{label:"How can I apply for the family medical leave act (FMLA)?",icon:"\uD83D\uDCC4"},{label:"What is covered under my healthcare insurance plan?",icon:"\uD83C\uDFDD️",description:"Top question asked"},{label:"How can I apply for the family medical leave act (FMLA)?",icon:"\uD83D\uDCC4"},{label:"What is covered under my healthcare insurance plan?",icon:"\uD83C\uDFDD️",description:"Top question asked"},{label:"How can I apply for the family medical leave act (FMLA)?",icon:"\uD83D\uDCC4"}],v={"@keyframes scrollRight":{"0%":{transform:"translateX(0)"},"100%":{transform:"translateX(-100%)"}},"@keyframes scrollLeft":{"0%":{transform:"translateX(-100%)"},"100%":{transform:"translateX(0)"}}};function D(){let e=(0,x.I0)(),t=(0,m.useRouter)(),[r,i]=(0,c.useState)(""),[l,s]=(0,c.useState)(!1);(0,c.useEffect)(()=>{s("true"===localStorage.getItem("isTeamsApp1"))},[]);let D=r=>{e((0,h.CS)(r)),t.push("/qHarmonyBot")};return(0,o.jsxs)(a.Z,{sx:{backgroundColor:"#ffffff",paddingY:4,mt:0,borderRadius:"30px"},children:[!l&&(0,o.jsxs)(a.Z,{sx:{display:"flex",alignItems:"center",mb:3,paddingX:3},children:[(0,o.jsx)(d.Z,{sx:{width:60,height:60,mr:2,overflow:"hidden"},children:(0,o.jsx)(f.default,{src:p.Z,alt:"Chat Avatar",layout:"fill",objectFit:"cover"})}),(0,o.jsxs)(a.Z,{children:[(0,o.jsx)(n.Z,{sx:{fontWeight:800,fontSize:"28px"},children:"Chat with Brea – Your benefits specialist"}),(0,o.jsx)(n.Z,{sx:{fontWeight:500,fontSize:"14px",color:"rgba(0, 0, 0, 0.6)"},children:"24/7 available"})]})]}),(0,o.jsxs)(a.Z,{sx:{overflow:"hidden",position:"relative"},children:[(0,o.jsx)(a.Z,{sx:{display:"flex",whiteSpace:"nowrap",animation:"scrollRight 30s linear infinite",...v,marginBottom:2},children:g.concat(g).map((e,t)=>(0,o.jsxs)(a.Z,{onClick:()=>D(e.label),sx:{display:"flex",alignItems:"center",px:2,py:1,mx:1,borderRadius:"12px",bgcolor:"#F6F6F6",minWidth:"300px",cursor:"pointer",flexShrink:0,overflow:"hidden",textOverflow:"ellipsis","&:hover":{backgroundColor:"#e9ecef"}},children:[(0,o.jsx)(n.Z,{variant:"body1",sx:{fontSize:"40px",marginRight:"10px"},children:e.icon}),(0,o.jsxs)(a.Z,{sx:{display:"flex",flexDirection:"column"},children:[(0,o.jsx)(n.Z,{variant:"body1",sx:{fontWeight:800,fontSize:"16px",whiteSpace:"nowrap",overflow:"hidden",textOverflow:"ellipsis"},children:e.label}),e.description&&(0,o.jsx)(n.Z,{variant:"caption",sx:{fontWeight:500,color:"#6c757d",fontSize:"13px",whiteSpace:"nowrap",overflow:"hidden",textOverflow:"ellipsis"},children:e.description})]})]},t))}),(0,o.jsx)(a.Z,{sx:{display:"flex",whiteSpace:"nowrap",animation:"scrollLeft 30s linear infinite",...v},children:b.concat(b).map((e,t)=>(0,o.jsxs)(a.Z,{onClick:()=>D(e.label),sx:{display:"flex",alignItems:"center",px:2,py:1,mx:1,borderRadius:"12px",bgcolor:"#F6F6F6",minWidth:"300px",cursor:"pointer",flexShrink:0,overflow:"hidden",textOverflow:"ellipsis","&:hover":{backgroundColor:"#e9ecef"}},children:[(0,o.jsx)(n.Z,{variant:"body1",sx:{fontSize:"40px",marginRight:"10px"},children:e.icon}),(0,o.jsxs)(a.Z,{sx:{display:"flex",flexDirection:"column"},children:[(0,o.jsx)(n.Z,{variant:"body1",sx:{fontWeight:800,fontSize:"16px",whiteSpace:"nowrap",overflow:"hidden",textOverflow:"ellipsis"},children:e.label}),e.description&&(0,o.jsx)(n.Z,{variant:"caption",sx:{fontWeight:500,color:"#6c757d",fontSize:"13px",whiteSpace:"nowrap",overflow:"hidden",textOverflow:"ellipsis"},children:e.description})]})]},t))})]}),(0,o.jsxs)(a.Z,{sx:{display:"flex",alignItems:"center",justifyContent:"space-between",mt:4,bgcolor:"#f1f3f5",mx:3,px:2,py:1,borderRadius:"100px"},children:[(0,o.jsx)("input",{type:"text",value:r,onChange:e=>{i(e.target.value)},placeholder:"How can I assist you?",style:{flex:1,border:"none",outline:"none",backgroundColor:"transparent",color:"black",fontSize:"16px",marginRight:"10px"}}),(0,o.jsx)(u.Z,{variant:"contained",onClick:()=>{r.trim()&&(D(r),i(""))},sx:{bgcolor:"#000000",color:"#ffffff",borderRadius:"100px",textTransform:"none",px:3},children:"Send"})]})]})}var w=r(83337),y=r(42374),k=r(14702),j=r(59701);function Z(){let e=(0,m.useRouter)(),t=(0,w.C)(e=>e.user.userProfile);return(0,o.jsx)(a.Z,{onClick:()=>{let r=localStorage.getItem("userid1")||localStorage.getItem("userId"),o=localStorage.getItem("ssoDone1"),a=localStorage.getItem("userEmail1");console.log("Census Auth Check:",{userId:r,ssoDone:o,userEmail:a,userDetailsName:null==t?void 0:t.name,userDetailsEmail:null==t?void 0:t.email}),r&&("true"===o||(null==t?void 0:t.name)||(null==t?void 0:t.email)||a)?(console.log("User authenticated, going to upload census"),e.push("/census?page=upload-census")):(console.log("User not authenticated, going to login prompt"),e.push("/census?page=login-prompt"))},sx:{backgroundColor:"white",padding:2,display:"flex",alignItems:"center",justifyContent:"space-between",borderRadius:"30px",boxShadow:"none",maxWidth:"100%",mt:3,cursor:"pointer",transition:"all 0.2s ease","&:hover":{boxShadow:"0 4px 12px rgba(0, 0, 0, 0.1)",transform:"translateY(-2px)"}},children:(0,o.jsxs)(a.Z,{sx:{display:"flex",alignItems:"center",flexDirection:"row"},children:[(0,o.jsx)(d.Z,{sx:{width:50,height:50,mr:2,backgroundColor:"#2563eb",background:"linear-gradient(135deg, #2563eb 0%, #8b5cf6 100%)"},children:(0,o.jsx)(j.Z,{size:28,color:"white",style:{filter:"drop-shadow(0 0 2px rgba(255,255,255,0.3))"}})}),(0,o.jsxs)(a.Z,{children:[(0,o.jsx)(a.Z,{sx:{display:"flex",alignItems:"center",flexDirection:"row"},children:(0,o.jsx)(n.Z,{sx:{fontWeight:700,fontSize:"24px",display:"flex",alignItems:"center"},children:"Census"})}),(0,o.jsx)(n.Z,{sx:{fontWeight:500,fontSize:"14px",color:"#6c757d"},children:"Upload and analyze employee census data"})]})]})})}var C=r(73143),S=(0,l.Z)(()=>{let e=(0,m.useRouter)(),t=(0,w.C)(e=>e.user.userProfile),[r,l]=(0,c.useState)(!1);return(0,c.useEffect)(()=>{try{window.parent!==window&&C.j2()}catch(e){console.log("Teams app initialization skipped - not running in Teams context")}},[]),(0,c.useEffect)(()=>{l("true"===localStorage.getItem("isTeamsApp1"))},[]),(0,c.useEffect)(()=>{localStorage.getItem("firstTimeLogin1")||(localStorage.setItem("firstTimeLogin1","true"),e.refresh())},[t]),(0,o.jsx)(s.Z,{children:(0,o.jsxs)(a.Z,{component:"main",sx:{flexGrow:1,p:4,bgcolor:"#F5F6FA",minHeight:"95vh"},children:[(0,o.jsxs)(n.Z,{variant:"h4",sx:{fontWeight:"bold",mb:1},children:["Hey, ",t.name.replace(/\b\w/g,e=>e.toUpperCase()),"!"]}),(0,o.jsx)(n.Z,{variant:"body1",sx:{color:"#6c757d",mb:4},children:"Let's make the most of your benefits today!"}),(0,o.jsxs)(i.ZP,{container:!0,spacing:2,children:[(0,o.jsxs)(i.ZP,{item:!0,xs:12,md:8,children:[(0,o.jsx)(D,{}),!r&&(0,o.jsx)(Z,{})]}),(0,o.jsxs)(i.ZP,{item:!0,xs:12,md:4,children:[(0,o.jsx)(y.Z,{}),(0,o.jsx)(k.Z,{})]})]})]})})})},96471:function(e,t,r){"use strict";r.d(t,{Z:function(){return s}});var o=r(2265);let a=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),n=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return t.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim()};var i={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let l=(0,o.forwardRef)((e,t)=>{let{color:r="currentColor",size:a=24,strokeWidth:l=2,absoluteStrokeWidth:s,className:c="",children:d,iconNode:u,...p}=e;return(0,o.createElement)("svg",{ref:t,...i,width:a,height:a,stroke:r,strokeWidth:s?24*Number(l)/Number(a):l,className:n("lucide",c),...p},[...u.map(e=>{let[t,r]=e;return(0,o.createElement)(t,r)}),...Array.isArray(d)?d:[d]])}),s=(e,t)=>{let r=(0,o.forwardRef)((r,i)=>{let{className:s,...c}=r;return(0,o.createElement)(l,{ref:i,iconNode:t,className:n("lucide-".concat(a(e)),s),...c})});return r.displayName="".concat(e),r}},59701:function(e,t,r){"use strict";r.d(t,{Z:function(){return o}});let o=(0,r(96471).Z)("ChartColumn",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]])}},function(e){e.O(0,[139,3463,3301,8575,8685,187,1423,9932,3919,9129,2786,9826,8166,8760,9414,7404,3209,7571,2170,3344,9662,1356,8005,4825,2971,2117,1744],function(){return e(e.s=21729)}),_N_E=e.O()}]);