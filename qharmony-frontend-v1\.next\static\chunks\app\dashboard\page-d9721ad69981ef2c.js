(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7702],{21729:function(e,t,n){Promise.resolve().then(n.bind(n,24963))},96729:function(e,t,n){"use strict";var o=n(94630),r=n(57437);t.Z=(0,o.Z)((0,r.jsx)("path",{d:"M19.35 10.04C18.67 6.59 15.64 4 12 4 9.11 4 6.6 5.64 5.35 8.04 2.34 8.36 0 10.91 0 14c0 3.31 2.69 6 6 6h13c2.76 0 5-2.24 5-5 0-2.64-2.05-4.78-4.65-4.96M14 13v4h-4v-4H7l5-5 5 5z"}),"CloudUpload")},67116:function(e,t,n){"use strict";n.d(t,{Z:function(){return w}});var o=n(2265),r=n(61994),i=n(20801),a=n(16210),l=n(21086),s=n(37053),c=n(94630),d=n(57437),u=(0,c.Z)((0,d.jsx)("path",{d:"M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"}),"Person"),f=n(94143),p=n(50738);function x(e){return(0,p.ZP)("MuiAvatar",e)}(0,f.Z)("MuiAvatar",["root","colorDefault","circular","rounded","square","img","fallback"]);var h=n(79114);let m=e=>{let{classes:t,variant:n,colorDefault:o}=e;return(0,i.Z)({root:["root",n,o&&"colorDefault"],img:["img"],fallback:["fallback"]},x,t)},g=(0,a.default)("div",{name:"MuiAvatar",slot:"Root",overridesResolver:(e,t)=>{let{ownerState:n}=e;return[t.root,t[n.variant],n.colorDefault&&t.colorDefault]}})((0,l.Z)(e=>{let{theme:t}=e;return{position:"relative",display:"flex",alignItems:"center",justifyContent:"center",flexShrink:0,width:40,height:40,fontFamily:t.typography.fontFamily,fontSize:t.typography.pxToRem(20),lineHeight:1,borderRadius:"50%",overflow:"hidden",userSelect:"none",variants:[{props:{variant:"rounded"},style:{borderRadius:(t.vars||t).shape.borderRadius}},{props:{variant:"square"},style:{borderRadius:0}},{props:{colorDefault:!0},style:{color:(t.vars||t).palette.background.default,...t.vars?{backgroundColor:t.vars.palette.Avatar.defaultBg}:{backgroundColor:t.palette.grey[400],...t.applyStyles("dark",{backgroundColor:t.palette.grey[600]})}}}]}})),b=(0,a.default)("img",{name:"MuiAvatar",slot:"Img",overridesResolver:(e,t)=>t.img})({width:"100%",height:"100%",textAlign:"center",objectFit:"cover",color:"transparent",textIndent:1e4}),y=(0,a.default)(u,{name:"MuiAvatar",slot:"Fallback",overridesResolver:(e,t)=>t.fallback})({width:"75%",height:"75%"});var w=o.forwardRef(function(e,t){let n=(0,s.i)({props:e,name:"MuiAvatar"}),{alt:i,children:a,className:l,component:c="div",slots:u={},slotProps:f={},imgProps:p,sizes:x,src:w,srcSet:v,variant:j="circular",...Z}=n,D=null,k={...n,component:c,variant:j},S=function(e){let{crossOrigin:t,referrerPolicy:n,src:r,srcSet:i}=e,[a,l]=o.useState(!1);return o.useEffect(()=>{if(!r&&!i)return;l(!1);let e=!0,o=new Image;return o.onload=()=>{e&&l("loaded")},o.onerror=()=>{e&&l("error")},o.crossOrigin=t,o.referrerPolicy=n,o.src=r,i&&(o.srcset=i),()=>{e=!1}},[t,n,r,i]),a}({...p,..."function"==typeof f.img?f.img(k):f.img,src:w,srcSet:v}),C=w||v,I=C&&"error"!==S;k.colorDefault=!I,delete k.ownerState;let E=m(k),[A,R]=(0,h.Z)("img",{className:E.img,elementType:b,externalForwardedProps:{slots:u,slotProps:{img:{...p,...f.img}}},additionalProps:{alt:i,src:w,srcSet:v,sizes:x},ownerState:k});return D=I?(0,d.jsx)(A,{...R}):a||0===a?a:C&&i?i[0]:(0,d.jsx)(y,{ownerState:k,className:E.fallback}),(0,d.jsx)(g,{as:c,className:(0,r.Z)(E.root,l),ref:t,...Z,ownerState:k,children:D})})},40256:function(e,t,n){"use strict";n.d(t,{$R:function(){return d},A_:function(){return l},BO:function(){return i},GH:function(){return u},_n:function(){return r},be:function(){return a},iG:function(){return c},j0:function(){return s}});var o=n(83464);let r="http://localhost:8080",i="<EMAIL>,<EMAIL>,<EMAIL>".split(",").map(e=>e.trim()),a=o.Z.create({baseURL:r});async function l(e,t,n){let o=new URL(n?"".concat(n).concat(e):"".concat(r).concat(e));return t&&Object.keys(t).forEach(e=>o.searchParams.append(e,t[e])),(await a.get(o.toString())).data}async function s(e,t,n){let o=n?"".concat(n).concat(e):"".concat(r).concat(e),i=await a.post(o,t,{headers:{"Content-Type":"application/json"}});return{status:i.status,data:i.data}}async function c(e,t,n){let o=n?"".concat(n).concat(e):"".concat(r).concat(e);console.log("Document upload to: ".concat(o));let i=await a.post(o,t,{headers:{"Content-Type":"multipart/form-data"}});return{status:i.status,data:i.data}}async function d(e,t,n){let o=new URL(n?"".concat(n).concat(e):"".concat(r).concat(e));return t&&Object.keys(t).forEach(e=>o.searchParams.append(e,t[e])),console.log("GET Blob request to: ".concat(o.toString())),(await a.get(o.toString(),{responseType:"blob"})).data}async function u(e,t,n){let o=n?"".concat(n).concat(e):"".concat(r).concat(e),i=await a.put(o,t,{headers:{"Content-Type":"application/json"}});return{status:i.status,data:i.data}}a.interceptors.request.use(e=>{let t=localStorage.getItem("userid1")||localStorage.getItem("userId");return t?e.headers["user-id"]=t:console.warn("No user ID found in localStorage for API request"),e})},14702:function(e,t,n){"use strict";var o=n(57437),r=n(95656),i=n(53410),a=n(46387),l=n(59832),s=n(35389),c=n(89414),d=n(96729),u=n(83337),f=n(2265),p=n(54862),x=n(7022),h=n(33145);t.Z=()=>{var e;let t=(0,u.T)(),n=(0,u.C)(e=>e.company.companyDetails),m=(0,f.useRef)(null),[g,b]=(0,f.useState)(!1),y=()=>{m.current&&m.current.click()};return(0,o.jsx)(r.Z,{sx:{display:"flex",flexDirection:"column",alignItems:"flex-start",mt:3},children:(0,o.jsxs)(i.Z,{sx:{display:"flex",flexDirection:"column",alignItems:"flex-start",justifyContent:"space-between",width:"100%",boxShadow:"none",borderRadius:"30px",bgcolor:"#ffffff",paddingBottom:"12px",paddingX:"12px"},children:[(0,o.jsxs)(r.Z,{sx:{display:"flex",alignItems:"center",justifyContent:"space-between",width:"100%",mb:2,paddingTop:"25px",paddingX:"12px"},children:[(0,o.jsxs)(r.Z,{children:[(0,o.jsx)(a.Z,{variant:"h5",sx:{fontWeight:"bold",mb:.5,fontSize:"24px"},children:n.name||"N/A"}),(0,o.jsx)(a.Z,{variant:"body2",sx:{color:"#6c757d",fontSize:"16px"},children:n.industry||"N/A"})]}),(0,o.jsxs)(r.Z,{sx:{position:"relative",ml:2},children:[(0,o.jsx)(r.Z,{onClick:y,sx:{width:100,height:100,borderRadius:"12px",overflow:"hidden",position:"relative",display:"flex",alignItems:"center",justifyContent:"center",bgcolor:"#f4f4f4",cursor:"pointer"},children:(null===(e=n.details)||void 0===e?void 0:e.logo)?(0,o.jsx)(h.default,{src:n.details.logo,alt:"Company Logo",layout:"fill",objectFit:"contain"}):(0,o.jsxs)(r.Z,{sx:{display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",height:"100%",color:"#9e9e9e"},children:[(0,o.jsx)(d.Z,{fontSize:"large"}),(0,o.jsx)(a.Z,{variant:"caption",sx:{mb:.5,fontWeight:600},children:"Logo"})]})}),(0,o.jsx)(l.Z,{onClick:y,sx:{position:"absolute",top:0,right:0,bgcolor:"#000000",color:"#ffffff",borderRadius:"50%",width:"24px",height:"24px",p:"2px"},children:g?(0,o.jsx)(s.Z,{size:16}):(0,o.jsx)(p.Z,{sx:{fontSize:"16px"}})})]})]}),(0,o.jsx)(i.Z,{sx:{borderRadius:"30px",p:2.5,boxShadow:"none",bgcolor:"rgba(245, 245, 245, 0.7)",width:"100%",mt:2},children:(0,o.jsxs)(c.ZP,{container:!0,spacing:2,children:[(0,o.jsx)(c.ZP,{item:!0,xs:6,children:(0,o.jsx)(a.Z,{variant:"body2",sx:{color:"#6c757d",fontSize:"14px",textAlign:"left"},children:"Company Size"})}),(0,o.jsx)(c.ZP,{item:!0,xs:6,children:(0,o.jsx)(a.Z,{variant:"body2",sx:{fontSize:"14px",textAlign:"right",fontWeight:600,wordBreak:"break-word",overflowWrap:"break-word"},children:n.companySize||"2"})}),(0,o.jsx)(c.ZP,{item:!0,xs:6,children:(0,o.jsx)(a.Z,{variant:"body2",sx:{color:"#6c757d",fontSize:"14px",textAlign:"left",wordBreak:"break-word",overflowWrap:"break-word"},children:"Website"})}),(0,o.jsx)(c.ZP,{item:!0,xs:6,children:(0,o.jsx)(a.Z,{variant:"body2",sx:{fontSize:"14px",textAlign:"right",fontWeight:600,wordBreak:"break-word",overflowWrap:"break-word"},children:n.website?(0,o.jsx)("a",{href:"http://".concat(n.website),target:"_blank",rel:"noopener noreferrer",style:{textDecoration:"none",color:"#000"},children:n.website}):"BenOsphere.com"})}),(0,o.jsx)(c.ZP,{item:!0,xs:6,children:(0,o.jsx)(a.Z,{variant:"body2",sx:{color:"#6c757d",fontSize:"14px",textAlign:"left",wordBreak:"break-word",overflowWrap:"break-word"},children:"Admin Email"})}),(0,o.jsx)(c.ZP,{item:!0,xs:6,children:(0,o.jsx)(a.Z,{variant:"body2",sx:{fontSize:"14px",textAlign:"right",fontWeight:600,wordBreak:"break-word",overflowWrap:"break-word"},children:n.adminEmail||"<EMAIL>"})})]})}),(0,o.jsx)("input",{type:"file",accept:".png, .jpg",ref:m,style:{display:"none"},onChange:e=>{var n;let o=null===(n=e.target.files)||void 0===n?void 0:n[0];o&&(b(!0),(0,x.mH)(t,o).finally(()=>{b(!1)}))}})]})})}},42374:function(e,t,n){"use strict";var o=n(57437),r=n(95656),i=n(67116),a=n(46387),l=n(59832),s=n(53410),c=n(89414),d=n(83337),u=n(2265),f=n(54862),p=n(98005);t.Z=()=>{var e,t;let n=(0,d.C)(e=>e.user.userProfile),[x,h]=(0,u.useState)(!1);return(0,o.jsxs)(r.Z,{sx:{display:"flex",flexDirection:"column",alignItems:"center",bgcolor:"#ffffff",paddingTop:7,paddingBottom:"12px",paddingX:"12px",borderRadius:"30px",width:"100%",position:"relative"},children:[(0,o.jsx)(r.Z,{sx:{position:"absolute",top:-60,display:"flex",justifyContent:"center",width:"100%"},children:(0,o.jsx)(i.Z,{sx:{backgroundImage:"linear-gradient(180deg, #4BD1E9 0%, #1274CF 100%)",color:"#ffffff",width:130,height:130,border:"10px solid #ffffff",fontSize:"48px",fontWeight:800},children:(e=>{console.log("Getting initials for name: ",JSON.stringify(n));let[t,o]=e.split(" ");return"".concat(t[0].toUpperCase()).concat(o?o[0].toUpperCase():"")})(n.name)})}),(0,o.jsxs)(r.Z,{sx:{display:"flex",alignItems:"center",mt:4,mb:4,position:"relative"},children:[(0,o.jsx)(a.Z,{variant:"h5",sx:{fontWeight:"bold",fontSize:"32px",flexGrow:1},children:n.name.replace(/\b\w/g,e=>e.toUpperCase())||"N/A"}),(0,o.jsx)(l.Z,{onClick:()=>{h(!0)},sx:{bgcolor:"#000000",color:"#ffffff",borderRadius:"50%",width:"24px",height:"24px",p:"2px",ml:2},children:(0,o.jsx)(f.Z,{sx:{fontSize:"16px"}})})]}),(0,o.jsx)(s.Z,{sx:{borderRadius:"30px",p:2.5,boxShadow:"none",bgcolor:"rgba(245, 245, 245, 0.7)",width:"100%"},children:(0,o.jsxs)(c.ZP,{container:!0,spacing:2,children:[(0,o.jsx)(c.ZP,{item:!0,xs:6,children:(0,o.jsx)(a.Z,{sx:{color:"#6c757d",fontSize:"14px",textAlign:"left"},children:"Title"})}),(0,o.jsx)(c.ZP,{item:!0,xs:6,children:(0,o.jsx)(a.Z,{sx:{fontSize:"14px",textAlign:"right",fontWeight:600},children:(null===(e=n.details)||void 0===e?void 0:e.title)||"N/A"})}),(0,o.jsx)(c.ZP,{item:!0,xs:6,children:(0,o.jsx)(a.Z,{variant:"body2",sx:{color:"#6c757d",fontSize:"14px",textAlign:"left"},children:"Department"})}),(0,o.jsx)(c.ZP,{item:!0,xs:6,children:(0,o.jsx)(a.Z,{variant:"body2",sx:{fontSize:"14px",textAlign:"right",fontWeight:600},children:(null===(t=n.details)||void 0===t?void 0:t.department)||"N/A"})}),(0,o.jsx)(c.ZP,{item:!0,xs:6,children:(0,o.jsx)(a.Z,{variant:"body2",sx:{color:"#6c757d",fontSize:"14px",textAlign:"left"},children:"Employment Type"})}),(0,o.jsx)(c.ZP,{item:!0,xs:6,children:(0,o.jsx)(a.Z,{variant:"body2",sx:{fontSize:"14px",textAlign:"right",fontWeight:600},children:"Full Time"})})]})}),(0,o.jsx)(p.default,{open:x,onClose:()=>{h(!1)}})]})}},24963:function(e,t,n){"use strict";n.r(t),n.d(t,{default:function(){return C}});var o=n(57437),r=n(95656),i=n(46387),a=n(89414),l=n(13571),s=n(48223),c=n(2265),d=n(67116),u=n(94013),f=n(56336),p=n(33145),x=n(70623),h=n(68575),m=n(99376);let g=[{label:"What benefits are available to me?",icon:"\uD83D\uDD25",description:"Top question asked"},{label:"How do I enroll in benefits?",icon:"\uD83E\uDDD9‍♂️"},{label:"What benefits are available to me?",icon:"\uD83D\uDD25",description:"Top question asked"},{label:"How do I enroll in benefits?",icon:"\uD83E\uDDD9‍♂️"},{label:"What benefits are available to me?",icon:"\uD83D\uDD25",description:"Top question asked"},{label:"How do I enroll in benefits?",icon:"\uD83E\uDDD9‍♂️"},{label:"What benefits are available to me?",icon:"\uD83D\uDD25",description:"Top question asked"},{label:"How do I enroll in benefits?",icon:"\uD83E\uDDD9‍♂️"},{label:"What benefits are available to me?",icon:"\uD83D\uDD25",description:"Top question asked"},{label:"How do I enroll in benefits?",icon:"\uD83E\uDDD9‍♂️"}],b=[{label:"What is covered under my healthcare insurance plan?",icon:"\uD83C\uDFDD️",description:"Top question asked"},{label:"How can I apply for the family medical leave act (FMLA)?",icon:"\uD83D\uDCC4"},{label:"What is covered under my healthcare insurance plan?",icon:"\uD83C\uDFDD️",description:"Top question asked"},{label:"How can I apply for the family medical leave act (FMLA)?",icon:"\uD83D\uDCC4"},{label:"What is covered under my healthcare insurance plan?",icon:"\uD83C\uDFDD️",description:"Top question asked"},{label:"How can I apply for the family medical leave act (FMLA)?",icon:"\uD83D\uDCC4"},{label:"What is covered under my healthcare insurance plan?",icon:"\uD83C\uDFDD️",description:"Top question asked"},{label:"How can I apply for the family medical leave act (FMLA)?",icon:"\uD83D\uDCC4"},{label:"What is covered under my healthcare insurance plan?",icon:"\uD83C\uDFDD️",description:"Top question asked"},{label:"How can I apply for the family medical leave act (FMLA)?",icon:"\uD83D\uDCC4"}],y={"@keyframes scrollRight":{"0%":{transform:"translateX(0)"},"100%":{transform:"translateX(-100%)"}},"@keyframes scrollLeft":{"0%":{transform:"translateX(-100%)"},"100%":{transform:"translateX(0)"}}};function w(){let e=(0,h.I0)(),t=(0,m.useRouter)(),[n,a]=(0,c.useState)(""),[l,s]=(0,c.useState)(!1);(0,c.useEffect)(()=>{s("true"===localStorage.getItem("isTeamsApp1"))},[]);let w=n=>{e((0,x.CS)(n)),t.push("/qHarmonyBot")};return(0,o.jsxs)(r.Z,{sx:{backgroundColor:"#ffffff",paddingY:4,mt:0,borderRadius:"30px"},children:[!l&&(0,o.jsxs)(r.Z,{sx:{display:"flex",alignItems:"center",mb:3,paddingX:3},children:[(0,o.jsx)(d.Z,{sx:{width:60,height:60,mr:2,overflow:"hidden"},children:(0,o.jsx)(p.default,{src:f.Z,alt:"Chat Avatar",layout:"fill",objectFit:"cover"})}),(0,o.jsxs)(r.Z,{children:[(0,o.jsx)(i.Z,{sx:{fontWeight:800,fontSize:"28px"},children:"Chat with Brea – Your benefits specialist"}),(0,o.jsx)(i.Z,{sx:{fontWeight:500,fontSize:"14px",color:"rgba(0, 0, 0, 0.6)"},children:"24/7 available"})]})]}),(0,o.jsxs)(r.Z,{sx:{overflow:"hidden",position:"relative"},children:[(0,o.jsx)(r.Z,{sx:{display:"flex",whiteSpace:"nowrap",animation:"scrollRight 30s linear infinite",...y,marginBottom:2},children:g.concat(g).map((e,t)=>(0,o.jsxs)(r.Z,{onClick:()=>w(e.label),sx:{display:"flex",alignItems:"center",px:2,py:1,mx:1,borderRadius:"12px",bgcolor:"#F6F6F6",minWidth:"300px",cursor:"pointer",flexShrink:0,overflow:"hidden",textOverflow:"ellipsis","&:hover":{backgroundColor:"#e9ecef"}},children:[(0,o.jsx)(i.Z,{variant:"body1",sx:{fontSize:"40px",marginRight:"10px"},children:e.icon}),(0,o.jsxs)(r.Z,{sx:{display:"flex",flexDirection:"column"},children:[(0,o.jsx)(i.Z,{variant:"body1",sx:{fontWeight:800,fontSize:"16px",whiteSpace:"nowrap",overflow:"hidden",textOverflow:"ellipsis"},children:e.label}),e.description&&(0,o.jsx)(i.Z,{variant:"caption",sx:{fontWeight:500,color:"#6c757d",fontSize:"13px",whiteSpace:"nowrap",overflow:"hidden",textOverflow:"ellipsis"},children:e.description})]})]},t))}),(0,o.jsx)(r.Z,{sx:{display:"flex",whiteSpace:"nowrap",animation:"scrollLeft 30s linear infinite",...y},children:b.concat(b).map((e,t)=>(0,o.jsxs)(r.Z,{onClick:()=>w(e.label),sx:{display:"flex",alignItems:"center",px:2,py:1,mx:1,borderRadius:"12px",bgcolor:"#F6F6F6",minWidth:"300px",cursor:"pointer",flexShrink:0,overflow:"hidden",textOverflow:"ellipsis","&:hover":{backgroundColor:"#e9ecef"}},children:[(0,o.jsx)(i.Z,{variant:"body1",sx:{fontSize:"40px",marginRight:"10px"},children:e.icon}),(0,o.jsxs)(r.Z,{sx:{display:"flex",flexDirection:"column"},children:[(0,o.jsx)(i.Z,{variant:"body1",sx:{fontWeight:800,fontSize:"16px",whiteSpace:"nowrap",overflow:"hidden",textOverflow:"ellipsis"},children:e.label}),e.description&&(0,o.jsx)(i.Z,{variant:"caption",sx:{fontWeight:500,color:"#6c757d",fontSize:"13px",whiteSpace:"nowrap",overflow:"hidden",textOverflow:"ellipsis"},children:e.description})]})]},t))})]}),(0,o.jsxs)(r.Z,{sx:{display:"flex",alignItems:"center",justifyContent:"space-between",mt:4,bgcolor:"#f1f3f5",mx:3,px:2,py:1,borderRadius:"100px"},children:[(0,o.jsx)("input",{type:"text",value:n,onChange:e=>{a(e.target.value)},placeholder:"How can I assist you?",style:{flex:1,border:"none",outline:"none",backgroundColor:"transparent",color:"black",fontSize:"16px",marginRight:"10px"}}),(0,o.jsx)(u.Z,{variant:"contained",onClick:()=>{n.trim()&&(w(n),a(""))},sx:{bgcolor:"#000000",color:"#ffffff",borderRadius:"100px",textTransform:"none",px:3},children:"Send"})]})]})}var v=n(83337),j=n(42374),Z=n(14702),D=n(59701);function k(){let e=(0,m.useRouter)(),t=(0,v.C)(e=>e.user.userProfile);return(0,o.jsx)(r.Z,{onClick:()=>{let n=localStorage.getItem("userid1")||localStorage.getItem("userId"),o=localStorage.getItem("ssoDone1"),r=localStorage.getItem("userEmail1");console.log("Census Auth Check:",{userId:n,ssoDone:o,userEmail:r,userDetailsName:null==t?void 0:t.name,userDetailsEmail:null==t?void 0:t.email}),n&&("true"===o||(null==t?void 0:t.name)||(null==t?void 0:t.email)||r)?(console.log("User authenticated, going to upload census"),e.push("/census?page=upload-census")):(console.log("User not authenticated, going to login prompt"),e.push("/census?page=login-prompt"))},sx:{backgroundColor:"white",padding:2,display:"flex",alignItems:"center",justifyContent:"space-between",borderRadius:"30px",boxShadow:"none",maxWidth:"100%",mt:3,cursor:"pointer",transition:"all 0.2s ease","&:hover":{boxShadow:"0 4px 12px rgba(0, 0, 0, 0.1)",transform:"translateY(-2px)"}},children:(0,o.jsxs)(r.Z,{sx:{display:"flex",alignItems:"center",flexDirection:"row"},children:[(0,o.jsx)(d.Z,{sx:{width:50,height:50,mr:2,backgroundColor:"#2563eb",background:"linear-gradient(135deg, #2563eb 0%, #8b5cf6 100%)"},children:(0,o.jsx)(D.Z,{size:28,color:"white",style:{filter:"drop-shadow(0 0 2px rgba(255,255,255,0.3))"}})}),(0,o.jsxs)(r.Z,{children:[(0,o.jsx)(r.Z,{sx:{display:"flex",alignItems:"center",flexDirection:"row"},children:(0,o.jsx)(i.Z,{sx:{fontWeight:700,fontSize:"24px",display:"flex",alignItems:"center"},children:"Census"})}),(0,o.jsx)(i.Z,{sx:{fontWeight:500,fontSize:"14px",color:"#6c757d"},children:"Upload and analyze employee census data"})]})]})})}var S=n(73143),C=(0,l.Z)(()=>{let e=(0,m.useRouter)(),t=(0,v.C)(e=>e.user.userProfile),[n,l]=(0,c.useState)(!1);return(0,c.useEffect)(()=>{try{window.parent!==window&&S.j2()}catch(e){console.log("Teams app initialization skipped - not running in Teams context")}},[]),(0,c.useEffect)(()=>{l("true"===localStorage.getItem("isTeamsApp1"))},[]),(0,c.useEffect)(()=>{localStorage.getItem("firstTimeLogin1")||(localStorage.setItem("firstTimeLogin1","true"),e.refresh())},[t]),(0,o.jsx)(s.Z,{children:(0,o.jsxs)(r.Z,{component:"main",sx:{flexGrow:1,p:4,bgcolor:"#F5F6FA",minHeight:"95vh"},children:[(0,o.jsxs)(i.Z,{variant:"h4",sx:{fontWeight:"bold",mb:1},children:["Hey, ",t.name.replace(/\b\w/g,e=>e.toUpperCase()),"!"]}),(0,o.jsx)(i.Z,{variant:"body1",sx:{color:"#6c757d",mb:4},children:"Let's make the most of your benefits today!"}),(0,o.jsxs)(a.ZP,{container:!0,spacing:2,children:[(0,o.jsxs)(a.ZP,{item:!0,xs:12,md:8,children:[(0,o.jsx)(w,{}),!n&&(0,o.jsx)(k,{})]}),(0,o.jsxs)(a.ZP,{item:!0,xs:12,md:4,children:[(0,o.jsx)(j.Z,{}),(0,o.jsx)(Z.Z,{})]})]})]})})})},7022:function(e,t,n){"use strict";n.d(t,{$t:function(){return d},SS:function(){return f},Y0:function(){return a},cd:function(){return u},fH:function(){return p},mH:function(){return x},ov:function(){return c},v0:function(){return l}});var o=n(40256),r=n(39124),i=n(39547);async function a(e,t,n){try{let i=await (0,o.A_)("/benefits/benefit-by-type",{companyId:t,type:n});i&&i.benefits?(console.log("GET BENEFITS FOR TYPE RESPONSE: ",i.benefits),e((0,r.oQ)({benefitType:n,benefits:i.benefits})),e((0,r.nM)("Benefits fetched successfully"))):(console.error("Invalid response format:",i),e((0,r.nM)("Failed to fetch benefits")))}catch(t){console.error("Error fetching benefits:",t),e((0,r.nM)("Error fetching benefits"))}}async function l(e,t,n,i){let a={benefitId:t,page:i};console.log("data",a);let l=await (0,o.A_)("/benefits/one-benefit",a),c={...l,benefitId:t};for(let t of(e((0,r.F5)(c)),l.documents)){let o=decodeURIComponent(t.split("_____")[1]);s(e,t,n,o)}}async function s(e,t,n,i){let a={objectKey:t,companyId:n};console.log("data",a);let l=await (0,o.$R)("/benefits/document",a);if(console.log("VIEW BENEFIT RESPONSE: ",l),l){let n=new Blob([l],{type:"application/pdf"}),o=URL.createObjectURL(n);e((0,r.D7)([{documentObjectKey:t,document:o,originalFileName:i}]))}}let c=async(e,t,n,r,l)=>200===(await (0,o.j0)("/benefits/toggle-benefits/",{benefitId:n,companyId:t,isActivated:r})).status&&(await a(e,t,l),await (0,i.N)(e,t),!0);async function d(e,t,n,i){let a=new FormData;i.forEach(e=>a.append("documents",e)),a.append("companyId",n),a.append("benefitId",t);try{console.log("uploadDocument",a);let l=await (0,o.iG)("/benefits/add/document",a),c=l.data.objectKeys;if(console.log("newObjectKeys",c),200===l.status)return c.forEach((o,a)=>{let l=i[a].name;e((0,r.H_)({benefitId:t,document:o})),s(e,o,n,l)}),e((0,r.nM)("Document added successfully")),!0;return console.error("Error adding document:",l.data.error),e((0,r.nM)("Failed to add document")),!1}catch(t){return console.error("Error adding document:",t),e((0,r.nM)("Error adding document")),!1}}async function u(e,t,n,i){try{let a=await (0,o.j0)("/benefits/delete/document",{benefitId:t,companyId:n,objectKey:i});if(200===a.status)return e((0,r.iH)({benefitId:t,document:i})),e((0,r.nM)("Document deleted successfully")),!0;return console.error("Error deleting document:",a.data.error),e((0,r.nM)("Failed to delete document")),!1}catch(t){return console.error("Error deleting document:",t),e((0,r.nM)("Error deleting document")),!1}}async function f(e,t,n,i){try{let a=await (0,o.j0)("/benefits/add/links",{benefitId:t,companyId:n,urls:[i]});if(200===a.status)return e((0,r.MJ)({benefitId:t,link:i})),e((0,r.nM)("Link added successfully")),!0;return console.error("Error adding link:",a.data.error),e((0,r.nM)("Failed to add link")),!1}catch(t){return console.error("Error adding link:",t),e((0,r.nM)("Error adding link")),!1}}async function p(e,t,n,i){try{let a=await (0,o.j0)("/benefits/delete/link",{benefitId:t,companyId:n,urls:i});if(200===a.status)return e((0,r.Yw)({benefitId:t,link:i})),e((0,r.nM)("Link deleted successfully")),!0;return console.error("Error deleting link:",a.data.error),e((0,r.nM)("Failed to delete link")),!1}catch(t){return console.error("Error deleting link:",t),e((0,r.nM)("Error deleting link")),!1}}async function x(e,t){let n=new FormData;n.append("logoImage",t);try{console.log("uploading company logo",n);let t=await (0,o.iG)("/admin/update-company-logo",n);if(await (0,i.aK)(e),200===t.status)return console.log("Company logo updated successfully"),e((0,r.nM)("Company logo updated successfully")),!0;return console.error("Error updating company logo:",t.data.error),e((0,r.nM)("Failed to update company logo")),!1}catch(t){return console.error("Error updating company logo:",t),e((0,r.nM)("Error updating company logo")),!1}}},96471:function(e,t,n){"use strict";n.d(t,{Z:function(){return s}});var o=n(2265);let r=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),i=function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return t.filter((e,t,n)=>!!e&&""!==e.trim()&&n.indexOf(e)===t).join(" ").trim()};var a={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let l=(0,o.forwardRef)((e,t)=>{let{color:n="currentColor",size:r=24,strokeWidth:l=2,absoluteStrokeWidth:s,className:c="",children:d,iconNode:u,...f}=e;return(0,o.createElement)("svg",{ref:t,...a,width:r,height:r,stroke:n,strokeWidth:s?24*Number(l)/Number(r):l,className:i("lucide",c),...f},[...u.map(e=>{let[t,n]=e;return(0,o.createElement)(t,n)}),...Array.isArray(d)?d:[d]])}),s=(e,t)=>{let n=(0,o.forwardRef)((n,a)=>{let{className:s,...c}=n;return(0,o.createElement)(l,{ref:a,iconNode:t,className:i("lucide-".concat(r(e)),s),...c})});return n.displayName="".concat(e),n}},59701:function(e,t,n){"use strict";n.d(t,{Z:function(){return o}});let o=(0,n(96471).Z)("ChartColumn",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]])}},function(e){e.O(0,[139,3463,3301,8575,293,9810,187,3145,9932,3919,9129,2786,9826,8166,8760,9414,7404,3209,7571,2170,3344,9662,1356,8005,2971,2117,1744],function(){return e(e.s=21729)}),_N_E=e.O()}]);