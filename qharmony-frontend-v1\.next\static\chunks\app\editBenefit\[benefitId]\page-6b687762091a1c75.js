(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5190],{845:function(e,n,t){Promise.resolve().then(t.bind(t,59297))},40256:function(e,n,t){"use strict";t.d(n,{$R:function(){return d},A_:function(){return l},BO:function(){return a},GH:function(){return u},_n:function(){return r},be:function(){return i},iG:function(){return s},j0:function(){return c}});var o=t(83464);let r="http://localhost:8080",a="<EMAIL>,<EMAIL>,<EMAIL>".split(",").map(e=>e.trim()),i=o.Z.create({baseURL:r});async function l(e,n,t){let o=new URL(t?"".concat(t).concat(e):"".concat(r).concat(e));return n&&Object.keys(n).forEach(e=>o.searchParams.append(e,n[e])),(await i.get(o.toString())).data}async function c(e,n,t){let o=t?"".concat(t).concat(e):"".concat(r).concat(e),a=await i.post(o,n,{headers:{"Content-Type":"application/json"}});return{status:a.status,data:a.data}}async function s(e,n,t){let o=t?"".concat(t).concat(e):"".concat(r).concat(e);console.log("Document upload to: ".concat(o));let a=await i.post(o,n,{headers:{"Content-Type":"multipart/form-data"}});return{status:a.status,data:a.data}}async function d(e,n,t){let o=new URL(t?"".concat(t).concat(e):"".concat(r).concat(e));return n&&Object.keys(n).forEach(e=>o.searchParams.append(e,n[e])),console.log("GET Blob request to: ".concat(o.toString())),(await i.get(o.toString(),{responseType:"blob"})).data}async function u(e,n,t){let o=t?"".concat(t).concat(e):"".concat(r).concat(e),a=await i.put(o,n,{headers:{"Content-Type":"application/json"}});return{status:a.status,data:a.data}}i.interceptors.request.use(e=>{let n=localStorage.getItem("userid1")||localStorage.getItem("userId");return n?e.headers["user-id"]=n:console.warn("No user ID found in localStorage for API request"),e})},59297:function(e,n,t){"use strict";t.r(n);var o=t(57437),r=t(2265),a=t(83337),i=t(7022),l=t(99376),c=t(95656),s=t(94013),d=t(46387),u=t(35389),f=t(59832),p=t(89414),x=t(24902),m=t(8350),g=t(8444),h=t(82279),b=t(53392),y=t(79507),j=t(77468),w=t(12258),k=t(97404),v=t(9026),E=t(26484),Z=t(74853),C=t(97470),S=t(68575),I=t(39124),R=t(70623),M=t(13571),F=t(48223),T=t(73143),_=t(60866),D=t(39547);n.default=(0,M.Z)(()=>{var e,n;let{benefitId:t}=(0,l.useParams)(),M=(0,l.useRouter)(),P=(0,a.T)(),L=(0,r.useRef)(null),[z,O]=(0,r.useState)(""),[W,A]=(0,r.useState)(!1),[B,N]=(0,r.useState)(null),[H,K]=(0,r.useState)(!1),[U,G]=(0,r.useState)(""),[Y,q]=(0,r.useState)(null),[$,J]=(0,r.useState)(null),[Q,V]=(0,r.useState)(!1),[X,ee]=(0,r.useState)(null),[en,et]=(0,r.useState)(!1),eo=(0,S.v9)(e=>(0,R.MP)(e)),er=(0,a.C)(e=>e.benefits.documentsPerBenefit),ea=(0,a.C)(e=>e.benefits.viewableDocuments),ei=(0,a.C)(e=>e.benefits.loadingDocuments),el=(0,S.v9)(e=>(0,I.d8)(e,t)),ec=(0,a.C)(e=>e.benefits.snackbarMessage);(0,r.useEffect)(()=>{T.j2().then(()=>{T.fw().then(e=>{e.app.host.name===_.UB.teams&&et(!0)})})},[]),(0,r.useEffect)(()=>{console.log("documents:",er)},[er]),(0,r.useEffect)(()=>{eo&&t&&(0,i.v0)(P,t,eo,"edit_benefit")},[eo,t,P]);let es=(e,n)=>{window.open("https://api.benosphere.com/benefits/document?objectKey=".concat(e,"&companyId=").concat(n),"_blank")},ed=e=>{q(e),K(!0)},eu=e=>{J(e),K(!0)},ef=()=>{K(!1),G(""),q(null),J(null)},ep=async()=>{"delete link"===U&&null!==$&&eo&&t&&(ee($),ef(),await (0,i.fH)(P,t,eo,er.links[$]),ee(null),J(null))},ex=async()=>{z&&(V(!0),await (0,i.SS)(P,t,eo,z),O(""),V(!1))},em=()=>{P((0,I.Sn)())};return(0,o.jsx)(F.Z,{children:(0,o.jsxs)(c.Z,{sx:{bgcolor:"#F5F6F8",minHeight:"98vh",padding:"32px"},children:[(0,o.jsx)(c.Z,{sx:{display:"flex",alignItems:"center",mb:3},children:(0,o.jsxs)(s.Z,{startIcon:(0,o.jsx)(E.Z,{sx:{fontSize:16}}),onClick:()=>M.back(),sx:{color:"#6c757d",fontWeight:"normal",textTransform:"none",fontSize:"1.2rem","&:hover":{bgcolor:"transparent"}},children:[(0,D.Ur)((null==el?void 0:el.benefitType)||"")," /",(0,o.jsx)("span",{style:{fontWeight:"bold",color:"#000000",marginLeft:5},children:(0,D.dA)((null==el?void 0:null===(e=el.benefit)||void 0===e?void 0:e.subType)||"")})]})}),(0,o.jsxs)(c.Z,{sx:{bgcolor:"#ffffff",borderRadius:"12px",padding:"32px",boxShadow:"0px 4px 12px rgba(0, 0, 0, 0.1)"},children:[(0,o.jsx)(d.Z,{variant:"h4",sx:{fontWeight:600,fontSize:"28px",mb:2},children:(0,D.dA)((null==el?void 0:null===(n=el.benefit)||void 0===n?void 0:n.subType)||"")}),(0,o.jsx)(d.Z,{variant:"body1",sx:{color:"#6c757d",mb:3},children:"You can find all your health insurance details here, including coverage options, policy documents, and claim information."}),(0,o.jsxs)(c.Z,{sx:{display:"flex",flexWrap:"wrap",gap:"35px",overflowY:"auto",maxHeight:"300px"},children:[er.documents.map((e,n)=>{let t=ea.find(n=>n.documentObjectKey===e);console.log("viewable document >>>",t);let r=["linear-gradient(135deg, #6a11cb 0%, #2575fc 100%)","linear-gradient(135deg, #ff7e5f 0%, #feb47b 100%)","linear-gradient(135deg, #43cea2 0%, #185a9d 100%)","linear-gradient(135deg, #ffafbd 0%, #ffc3a0 100%)","linear-gradient(135deg, #00c6ff 0%, #0072ff 100%)"],a=r[n%r.length];return(0,o.jsxs)(c.Z,{sx:{position:"relative",width:"160px",height:"245px",display:"flex",flexDirection:"column",alignItems:"flex-start",justifyContent:"flex-start"},children:[(0,o.jsx)(c.Z,{sx:{width:"160px",minHeight:"215px",borderRadius:"12px",overflow:"hidden",boxShadow:"0px 4px 12px rgba(0, 0, 0, 0.1)",position:"relative",display:"flex",alignItems:"center",justifyContent:"center",background:a,color:"#ffffff",cursor:"pointer",textAlign:"center",padding:"8px"},onClick:()=>es(t.documentObjectKey,eo),children:ei.includes(e)||!t?(0,o.jsx)(u.Z,{}):(0,o.jsx)(d.Z,{sx:{fontSize:"16px",fontWeight:"bold",wordWrap:"break-word",overflow:"hidden",textOverflow:"ellipsis",maxHeight:"100%",display:"-webkit-box",WebkitLineClamp:3,WebkitBoxOrient:"vertical"},children:(null==t?void 0:t.originalFileName)||"Document Preview"})}),(0,o.jsx)(f.Z,{className:"delete-icon",onClick:()=>ed(e),sx:{position:"absolute",top:-2,right:-2,padding:"5px",color:"black","&:hover":{color:"red"}},children:B===e?(0,o.jsx)(u.Z,{size:20}):(0,o.jsx)(Z.Z,{sx:{color:"white"}})})]},e)}),(0,o.jsxs)(c.Z,{sx:{display:"flex",flexDirection:"column",alignItems:"center",marginLeft:"0px"},children:[(0,o.jsx)(c.Z,{onClick:()=>{var e;null===(e=L.current)||void 0===e||e.click()},sx:{border:"2px dashed #d3d3d3",bgcolor:"#f9f9f9",borderRadius:"12px",width:"160px",height:"215px",display:"flex",justifyContent:"center",alignItems:"center",color:"#d3d3d3",fontSize:"2.5rem","&:hover":{backgroundColor:"#f0f0f0"}},children:W?(0,o.jsx)(u.Z,{}):"+"}),(0,o.jsx)("input",{type:"file",ref:L,style:{display:"none"},onChange:e=>{let n=e.target.files;n&&n.length>0&&(Array.from(n).every(e=>"application/pdf"===e.type)?eo&&t&&(A(!0),(0,i.$t)(P,t,eo,Array.from(n)).finally(()=>{A(!1),L.current&&(L.current.value="")})):alert("Please select PDF files only."))},accept:"application/pdf"}),(0,o.jsx)(d.Z,{sx:{color:"#a9a9a9",fontSize:"0.875rem",mt:1},children:"Add up to 5 pdf files"})]})]}),(0,o.jsxs)(c.Z,{sx:{mt:8},children:[(0,o.jsx)(c.Z,{sx:{alignItems:"center",mb:3},children:(0,o.jsxs)(c.Z,{sx:{display:"flex",alignItems:"center",width:"40%"},children:[(0,o.jsx)("input",{type:"text",value:z,onChange:e=>{O(e.target.value)},onKeyDown:e=>{"Enter"===e.key&&ex()},placeholder:"Enter Link To Add (eg. www.BenOsphere.com)",style:{padding:"15px 14px",borderRadius:"8px",border:"none",width:"70%",backgroundColor:"#f3f3f3",color:"#000000",fontSize:"0.9rem"}}),(0,o.jsx)(s.Z,{onClick:ex,disabled:!z||Q,sx:{display:"flex",alignItems:"center",justifyContent:"center",color:"#45a049",textTransform:"none",fontSize:"1rem",fontWeight:z?"bold":"normal",padding:0,backgroundColor:"transparent",border:"none",cursor:"pointer",marginLeft:"20px","&:disabled":{color:"#9E9E9E"}},children:Q?(0,o.jsx)(u.Z,{size:20}):(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)("span",{style:{fontSize:"1.8rem",marginRight:"8px",fontWeight:"normal"},children:"+"}),"Add Link"]})})]})}),(0,o.jsx)(p.ZP,{container:!0,spacing:2,children:er.links.map((e,n)=>(0,o.jsx)(p.ZP,{item:!0,xs:12,children:(0,o.jsxs)(c.Z,{sx:{paddingLeft:1},children:[(0,o.jsxs)(c.Z,{sx:{display:"flex",alignItems:"center"},children:[(0,o.jsx)(d.Z,{component:"a",href:e.startsWith("http")?e:"https://".concat(e),target:"_blank",rel:"noopener noreferrer",sx:{textDecoration:"none",color:"primary.main",display:"block",paddingBottom:"10px"},children:e||"Link ".concat(n+1)}),(0,o.jsx)(x.Z,{title:"Remove Link",children:(0,o.jsx)(f.Z,{onClick:()=>eu(n),sx:{color:"gray",marginLeft:"8px",padding:"4px",marginBottom:"7px","&:hover":{color:"#ff4d4d"}},children:X===n?(0,o.jsx)(u.Z,{size:20}):(0,o.jsx)(C.Z,{sx:{fontSize:"20px"}})})})]}),(0,o.jsx)(m.Z,{sx:{width:"10%"}})]})},n))})]})]}),(0,o.jsx)(g.Z,{open:!!ec,autoHideDuration:3e3,onClose:em,children:(0,o.jsx)(h.Z,{onClose:em,severity:"success",sx:{width:"100%"},children:ec})}),(0,o.jsxs)(b.Z,{open:H,onClose:ef,PaperProps:{style:{borderRadius:"16px",boxShadow:"0px 10px 30px rgba(0, 0, 0, 0.1)"}},children:[(0,o.jsx)(y.Z,{sx:{fontWeight:"bold",fontSize:"1.5rem",color:"#000000"},children:"Confirm Deletion"}),(0,o.jsxs)(j.Z,{children:[(0,o.jsxs)(w.Z,{sx:{color:"#6c757d",fontSize:"1rem",mb:2},children:['To confirm deletion, please type "',(0,o.jsx)("strong",{style:{color:"black"},children:null!==$?"delete link":"delete document"}),'" in the box below.']}),(0,o.jsx)(k.Z,{autoFocus:!0,fullWidth:!0,variant:"outlined",value:U,onChange:e=>G(e.target.value),InputProps:{style:{borderRadius:"12px",backgroundColor:"#f9f9f9"}}})]}),(0,o.jsxs)(v.Z,{sx:{padding:"16px"},children:[(0,o.jsx)(s.Z,{onClick:ef,sx:{color:"#6c757d",backgroundColor:"#ffffff",borderRadius:"8px",padding:"8px 16px",textTransform:"none",boxShadow:"none","&:hover":{backgroundColor:"#f0f0f0"}},children:"Cancel"}),(0,o.jsx)(s.Z,{onClick:null!==$?ep:()=>{"delete document"===U&&Y&&eo&&t&&(N(Y),(0,i.cd)(P,t,eo,Y).finally(()=>{N(null),ef()}))},disabled:U!==(null!==$?"delete link":"delete document"),sx:{color:"#ffffff",backgroundColor:U===(null!==$?"delete link":"delete document")?"#000000":"#9E9E9E",borderRadius:"8px",padding:"8px 16px",textTransform:"none","&:hover":{backgroundColor:U===(null!==$?"delete link":"delete document")?"#333333":"#9E9E9E"}},children:B||null!==X?(0,o.jsx)(u.Z,{size:20,sx:{color:"white"}}):"Confirm"})]})]})]})})})},7022:function(e,n,t){"use strict";t.d(n,{$t:function(){return d},SS:function(){return f},Y0:function(){return i},cd:function(){return u},fH:function(){return p},mH:function(){return x},ov:function(){return s},v0:function(){return l}});var o=t(40256),r=t(39124),a=t(39547);async function i(e,n,t){try{let a=await (0,o.A_)("/benefits/benefit-by-type",{companyId:n,type:t});a&&a.benefits?(console.log("GET BENEFITS FOR TYPE RESPONSE: ",a.benefits),e((0,r.oQ)({benefitType:t,benefits:a.benefits})),e((0,r.nM)("Benefits fetched successfully"))):(console.error("Invalid response format:",a),e((0,r.nM)("Failed to fetch benefits")))}catch(n){console.error("Error fetching benefits:",n),e((0,r.nM)("Error fetching benefits"))}}async function l(e,n,t,a){let i={benefitId:n,page:a};console.log("data",i);let l=await (0,o.A_)("/benefits/one-benefit",i),s={...l,benefitId:n};for(let n of(e((0,r.F5)(s)),l.documents)){let o=decodeURIComponent(n.split("_____")[1]);c(e,n,t,o)}}async function c(e,n,t,a){let i={objectKey:n,companyId:t};console.log("data",i);let l=await (0,o.$R)("/benefits/document",i);if(console.log("VIEW BENEFIT RESPONSE: ",l),l){let t=new Blob([l],{type:"application/pdf"}),o=URL.createObjectURL(t);e((0,r.D7)([{documentObjectKey:n,document:o,originalFileName:a}]))}}let s=async(e,n,t,r,l)=>200===(await (0,o.j0)("/benefits/toggle-benefits/",{benefitId:t,companyId:n,isActivated:r})).status&&(await i(e,n,l),await (0,a.N)(e,n),!0);async function d(e,n,t,a){let i=new FormData;a.forEach(e=>i.append("documents",e)),i.append("companyId",t),i.append("benefitId",n);try{console.log("uploadDocument",i);let l=await (0,o.iG)("/benefits/add/document",i),s=l.data.objectKeys;if(console.log("newObjectKeys",s),200===l.status)return s.forEach((o,i)=>{let l=a[i].name;e((0,r.H_)({benefitId:n,document:o})),c(e,o,t,l)}),e((0,r.nM)("Document added successfully")),!0;return console.error("Error adding document:",l.data.error),e((0,r.nM)("Failed to add document")),!1}catch(n){return console.error("Error adding document:",n),e((0,r.nM)("Error adding document")),!1}}async function u(e,n,t,a){try{let i=await (0,o.j0)("/benefits/delete/document",{benefitId:n,companyId:t,objectKey:a});if(200===i.status)return e((0,r.iH)({benefitId:n,document:a})),e((0,r.nM)("Document deleted successfully")),!0;return console.error("Error deleting document:",i.data.error),e((0,r.nM)("Failed to delete document")),!1}catch(n){return console.error("Error deleting document:",n),e((0,r.nM)("Error deleting document")),!1}}async function f(e,n,t,a){try{let i=await (0,o.j0)("/benefits/add/links",{benefitId:n,companyId:t,urls:[a]});if(200===i.status)return e((0,r.MJ)({benefitId:n,link:a})),e((0,r.nM)("Link added successfully")),!0;return console.error("Error adding link:",i.data.error),e((0,r.nM)("Failed to add link")),!1}catch(n){return console.error("Error adding link:",n),e((0,r.nM)("Error adding link")),!1}}async function p(e,n,t,a){try{let i=await (0,o.j0)("/benefits/delete/link",{benefitId:n,companyId:t,urls:a});if(200===i.status)return e((0,r.Yw)({benefitId:n,link:a})),e((0,r.nM)("Link deleted successfully")),!0;return console.error("Error deleting link:",i.data.error),e((0,r.nM)("Failed to delete link")),!1}catch(n){return console.error("Error deleting link:",n),e((0,r.nM)("Error deleting link")),!1}}async function x(e,n){let t=new FormData;t.append("logoImage",n);try{console.log("uploading company logo",t);let n=await (0,o.iG)("/admin/update-company-logo",t);if(await (0,a.aK)(e),200===n.status)return console.log("Company logo updated successfully"),e((0,r.nM)("Company logo updated successfully")),!0;return console.error("Error updating company logo:",n.data.error),e((0,r.nM)("Failed to update company logo")),!1}catch(n){return console.error("Error updating company logo:",n),e((0,r.nM)("Error updating company logo")),!1}}}},function(e){e.O(0,[139,3463,3301,8575,293,9810,187,3145,9932,3919,9129,2786,9826,8166,8760,9414,7404,3209,4902,8401,3344,9662,1356,2971,2117,1744],function(){return e(e.s=845)}),_N_E=e.O()}]);