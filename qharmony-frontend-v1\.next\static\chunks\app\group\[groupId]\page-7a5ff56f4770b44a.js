(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3558],{96065:function(e,t,r){Promise.resolve().then(r.bind(r,70490))},67116:function(e,t,r){"use strict";r.d(t,{Z:function(){return j}});var n=r(2265),o=r(61994),a=r(20801),i=r(16210),s=r(21086),l=r(37053),c=r(94630),d=r(57437),u=(0,c.Z)((0,d.jsx)("path",{d:"M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"}),"Person"),x=r(94143),f=r(50738);function h(e){return(0,f.ZP)("MuiAvatar",e)}(0,x.Z)("MuiAvatar",["root","colorDefault","circular","rounded","square","img","fallback"]);var p=r(79114);let m=e=>{let{classes:t,variant:r,colorDefault:n}=e;return(0,a.Z)({root:["root",r,n&&"colorDefault"],img:["img"],fallback:["fallback"]},h,t)},g=(0,i.default)("div",{name:"MuiAvatar",slot:"Root",overridesResolver:(e,t)=>{let{ownerState:r}=e;return[t.root,t[r.variant],r.colorDefault&&t.colorDefault]}})((0,s.Z)(e=>{let{theme:t}=e;return{position:"relative",display:"flex",alignItems:"center",justifyContent:"center",flexShrink:0,width:40,height:40,fontFamily:t.typography.fontFamily,fontSize:t.typography.pxToRem(20),lineHeight:1,borderRadius:"50%",overflow:"hidden",userSelect:"none",variants:[{props:{variant:"rounded"},style:{borderRadius:(t.vars||t).shape.borderRadius}},{props:{variant:"square"},style:{borderRadius:0}},{props:{colorDefault:!0},style:{color:(t.vars||t).palette.background.default,...t.vars?{backgroundColor:t.vars.palette.Avatar.defaultBg}:{backgroundColor:t.palette.grey[400],...t.applyStyles("dark",{backgroundColor:t.palette.grey[600]})}}}]}})),y=(0,i.default)("img",{name:"MuiAvatar",slot:"Img",overridesResolver:(e,t)=>t.img})({width:"100%",height:"100%",textAlign:"center",objectFit:"cover",color:"transparent",textIndent:1e4}),b=(0,i.default)(u,{name:"MuiAvatar",slot:"Fallback",overridesResolver:(e,t)=>t.fallback})({width:"75%",height:"75%"});var j=n.forwardRef(function(e,t){let r=(0,l.i)({props:e,name:"MuiAvatar"}),{alt:a,children:i,className:s,component:c="div",slots:u={},slotProps:x={},imgProps:f,sizes:h,src:j,srcSet:Z,variant:v="circular",...k}=r,w=null,S={...r,component:c,variant:v},_=function(e){let{crossOrigin:t,referrerPolicy:r,src:o,srcSet:a}=e,[i,s]=n.useState(!1);return n.useEffect(()=>{if(!o&&!a)return;s(!1);let e=!0,n=new Image;return n.onload=()=>{e&&s("loaded")},n.onerror=()=>{e&&s("error")},n.crossOrigin=t,n.referrerPolicy=r,n.src=o,a&&(n.srcset=a),()=>{e=!1}},[t,r,o,a]),i}({...f,..."function"==typeof x.img?x.img(S):x.img,src:j,srcSet:Z}),B=j||Z,C=B&&"error"!==_;S.colorDefault=!C,delete S.ownerState;let E=m(S),[A,P]=(0,p.Z)("img",{className:E.img,elementType:y,externalForwardedProps:{slots:u,slotProps:{img:{...f,...x.img}}},additionalProps:{alt:a,src:j,srcSet:Z,sizes:h},ownerState:S});return w=C?(0,d.jsx)(A,{...P}):i||0===i?i:B&&a?a[0]:(0,d.jsx)(b,{ownerState:S,className:E.fallback}),(0,d.jsx)(g,{as:c,className:(0,o.Z)(E.root,s),ref:t,...k,ownerState:S,children:w})})},47159:function(e,t,r){"use strict";let n=r(2265).createContext(void 0);t.Z=n},66515:function(e,t,r){"use strict";r.d(t,{Z:function(){return a}});var n=r(2265),o=r(47159);function a(){return n.useContext(o.Z)}},4778:function(e,t,r){"use strict";r.d(t,{Z:function(){return o}});var n=r(2265),o=function(e){let{controlled:t,default:r,name:o,state:a="value"}=e,{current:i}=n.useRef(void 0!==t),[s,l]=n.useState(r),c=n.useCallback(e=>{i||l(e)},[]);return[i?t:s,c]}},40256:function(e,t,r){"use strict";r.d(t,{$R:function(){return d},A_:function(){return s},BO:function(){return a},GH:function(){return u},_n:function(){return o},be:function(){return i},iG:function(){return c},j0:function(){return l}});var n=r(83464);let o="http://localhost:8080",a="<EMAIL>,<EMAIL>,<EMAIL>".split(",").map(e=>e.trim()),i=n.Z.create({baseURL:o});async function s(e,t,r){let n=new URL(r?"".concat(r).concat(e):"".concat(o).concat(e));return t&&Object.keys(t).forEach(e=>n.searchParams.append(e,t[e])),(await i.get(n.toString())).data}async function l(e,t,r){let n=r?"".concat(r).concat(e):"".concat(o).concat(e),a=await i.post(n,t,{headers:{"Content-Type":"application/json"}});return{status:a.status,data:a.data}}async function c(e,t,r){let n=r?"".concat(r).concat(e):"".concat(o).concat(e);console.log("Document upload to: ".concat(n));let a=await i.post(n,t,{headers:{"Content-Type":"multipart/form-data"}});return{status:a.status,data:a.data}}async function d(e,t,r){let n=new URL(r?"".concat(r).concat(e):"".concat(o).concat(e));return t&&Object.keys(t).forEach(e=>n.searchParams.append(e,t[e])),console.log("GET Blob request to: ".concat(n.toString())),(await i.get(n.toString(),{responseType:"blob"})).data}async function u(e,t,r){let n=r?"".concat(r).concat(e):"".concat(o).concat(e),a=await i.put(n,t,{headers:{"Content-Type":"application/json"}});return{status:a.status,data:a.data}}i.interceptors.request.use(e=>{let t=localStorage.getItem("userid1")||localStorage.getItem("userId");return t?e.headers["user-id"]=t:console.warn("No user ID found in localStorage for API request"),e})},70490:function(e,t,r){"use strict";r.r(t);var n=r(57437),o=r(2265),a=r(95656),i=r(46387),s=r(94013),l=r(53410),c=r(89414),d=r(11953),u=r(13571),x=r(48223),f=r(99376),h=r(68575),p=r(70623),m=r(40256),g=r(5485),y=r(91230),b=r(39547);let j=e=>{let t=e.split("_____");return t.length>1?t[1]:e};t.default=(0,u.Z)(()=>{let e=(0,f.useParams)(),t=(0,h.v9)(e=>(0,p.MP)(e)),[r,u]=(0,o.useState)(0),[Z,v]=(0,o.useState)(null),[k,w]=(0,o.useState)([]),[S,_]=(0,o.useState)([]),[B,C]=(0,o.useState)(!0),[E,A]=(0,o.useState)(!1),P=(0,f.useRouter)();(0,o.useEffect)(()=>{(async()=>{await R(),await M()})()},[]),(0,o.useEffect)(()=>{0===r?M():1===r&&I()},[r]),(0,o.useEffect)(()=>{Z&&S.length>0&&A(Z.employee_ids.length===S.length)},[Z,S]);let R=async()=>{try{let t=await (0,m.A_)("/group-detail/".concat(e.groupId));v(t.group)}catch(e){console.error("Error fetching group details:",e)}},I=async()=>{try{let e=(await (0,m.A_)("/benefits/all-benefits",{companyId:t})).benefitsPerType.reduce((e,t)=>{let r=t.benefits.filter(e=>e.isActivated);return r.length>0&&e.push({...t,benefits:r}),e},[]);w(e)}catch(e){console.error("Error fetching benefits data:",e)}},M=async()=>{try{let e=await (0,m.A_)("/admin/all-employees");if(_(e.employees),Z&&e.employees.length>0){let t=e.employees.length===Z.employee_ids.length;A(t)}}catch(e){console.error("Error fetching team members:",e)}},T=(e,t)=>{Z&&(C(!1),v(r=>{if(!r)return r;let n=r["employee"===t?"employee_ids":"document_ids"].includes(e)?r["employee"===t?"employee_ids":"document_ids"].filter(t=>t!==e):[...r["employee"===t?"employee_ids":"document_ids"],e];return{...r,["employee"===t?"employee_ids":"document_ids"]:n}}))},F=async()=>{let t={groupId:e.groupId,documentIds:null==Z?void 0:Z.document_ids,employeeIds:null==Z?void 0:Z.employee_ids};200===(await (0,m.j0)("/group/update-group",t)).status&&C(!0)},z=e=>{Z&&(C(!1),v(t=>{if(!t)return t;let r=e.imageS3Urls.every(e=>t.document_ids.includes(e))?t.document_ids.filter(t=>!e.imageS3Urls.includes(t)):[...new Set([...t.document_ids,...e.imageS3Urls])];return{...t,document_ids:r}}))};return(0,n.jsx)(x.Z,{children:(0,n.jsxs)(a.Z,{sx:{bgcolor:"#F5F6FA",py:2,width:"100%",height:"95vh",overflow:"auto"},children:[(0,n.jsxs)(a.Z,{sx:{px:4,display:"flex",justifyContent:"space-between",alignItems:"center"},children:[(0,n.jsx)(i.Z,{sx:{fontWeight:"500",fontSize:"28px",color:"black",textAlign:"left"},children:null==Z?void 0:Z.name}),0===r?(0,n.jsx)(s.Z,{variant:"contained",sx:{textTransform:"none",borderRadius:"8px",bgcolor:"rgba(0, 0, 0, 0.06)",color:"black",boxShadow:"none",width:"15%",paddingY:"10px","&:hover":{backgroundColor:"rgba(0, 0, 0, 0.1)",boxShadow:"none"}},disabled:B,onClick:F,children:"Confirm Changes"}):""]}),(0,n.jsxs)(a.Z,{children:[0===r&&(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(i.Z,{sx:{px:4,fontWeight:"500",fontSize:"17px",color:"black",textAlign:"left",mb:"40px"},children:"Select Users"}),(0,n.jsxs)(l.Z,{sx:{bgcolor:"#ffffff",borderRadius:"12px",marginBottom:9,boxShadow:"none",mx:4},children:[(0,n.jsxs)(c.ZP,{container:!0,sx:{borderBottom:"1px solid #E0E0E0",background:"#F0F0F0",px:3},children:[(0,n.jsx)(c.ZP,{item:!0,xs:1,children:(0,n.jsx)(d.Z,{checked:E,onChange:()=>{A(e=>!e),C(!1),v(e=>{if(!e)return e;let t=E?[]:S.map(e=>e._id);return{...e,employee_ids:t}})},sx:{color:"#B0B0B0","&.Mui-checked":{color:"#B0B0B0"}}})}),(0,n.jsx)(c.ZP,{item:!0,xs:5,children:(0,n.jsx)(i.Z,{variant:"body2",sx:{fontWeight:500,color:"#B0B0B0",py:1},children:"NAME"})}),(0,n.jsx)(c.ZP,{item:!0,xs:6,borderLeft:"1px solid #E0E0E0",paddingLeft:"5px",children:(0,n.jsx)(i.Z,{variant:"body2",sx:{fontWeight:500,color:"#B0B0B0",py:1},children:"EMAIL"})})]}),S.map(e=>(0,n.jsx)(a.Z,{sx:{transition:"background-color 0.3s ease","&:hover":{bgcolor:"#f0f0f0",cursor:"pointer"},borderBottom:"1px solid #E0E0E0",px:3},children:(0,n.jsxs)(c.ZP,{container:!0,alignItems:"center",children:[(0,n.jsx)(c.ZP,{item:!0,xs:1,children:(0,n.jsx)(d.Z,{checked:E||(null==Z?void 0:Z.employee_ids.includes(e._id)),onChange:()=>T(e._id,"employee"),sx:{color:"#B0B0B0","&.Mui-checked":{color:"#B0B0B0"}}})}),(0,n.jsx)(c.ZP,{item:!0,xs:5,children:(0,n.jsx)(i.Z,{sx:{fontWeight:"500",color:"black",py:2},children:e.name})}),(0,n.jsx)(c.ZP,{item:!0,xs:6,borderLeft:"1px solid #E0E0E0",paddingLeft:"5px",children:(0,n.jsx)(i.Z,{sx:{fontWeight:"500",color:"black",py:2},children:e.email})})]})},e._id))]}),(0,n.jsxs)(a.Z,{sx:{display:"flex",width:"100%",justifyContent:"center",gap:"10px"},children:[(0,n.jsxs)(s.Z,{variant:"contained",onClick:()=>P.push("/manage-groups"),sx:{alignSelf:"center",display:"flex",gap:"10px",textTransform:"none",borderRadius:"8px",bgcolor:"black",color:"white",boxShadow:"none"},children:[(0,n.jsx)(a.Z,{sx:{display:"flex",alignItems:"center"},children:(0,n.jsx)(g.Z,{color:"white",size:16})}),"Back"," "]}),(0,n.jsxs)(s.Z,{variant:"contained",onClick:()=>u(1),sx:{alignSelf:"center",display:"flex",gap:"10px",textTransform:"none",borderRadius:"8px",bgcolor:"black",color:"white",boxShadow:"none"},children:["Continue"," ",(0,n.jsx)(a.Z,{sx:{display:"flex",alignItems:"center"},children:(0,n.jsx)(y.Z,{color:"white",size:16})})]})]})]}),1===r&&(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(i.Z,{sx:{px:4,fontWeight:"500",fontSize:"17px",color:"black",textAlign:"left",mb:"40px"},children:"Select Documents"}),(0,n.jsx)(a.Z,{sx:{display:"flex",flexDirection:"column"},children:(0,n.jsxs)(a.Z,{children:[k.map((e,t)=>(0,n.jsxs)(a.Z,{sx:{mb:3},children:[(0,n.jsx)(i.Z,{sx:{fontWeight:"500",fontSize:"22px",mb:1,px:4},children:(0,b.Ur)(e.benefitType)}),e.benefits.map((e,t)=>(0,n.jsxs)(l.Z,{sx:{bgcolor:"#ffffff",mb:2,mx:4},children:[(0,n.jsx)(i.Z,{sx:{fontWeight:"500",fontSize:"20px",mb:1,px:4,pt:2},children:(0,b.dA)(e.subType)}),e.imageS3Urls.length>0?(0,n.jsxs)(a.Z,{children:[(0,n.jsxs)(c.ZP,{container:!0,sx:{borderBottom:"1px solid #E0E0E0",background:"#F0F0F0",px:3},children:[(0,n.jsx)(c.ZP,{item:!0,xs:1,children:(0,n.jsx)(d.Z,{checked:e.imageS3Urls.every(e=>null==Z?void 0:Z.document_ids.includes(e)),onChange:()=>z(e),sx:{color:"#B0B0B0","&.Mui-checked":{color:"#B0B0B0"}}})}),(0,n.jsx)(c.ZP,{item:!0,xs:5,children:(0,n.jsx)(i.Z,{variant:"body2",sx:{fontWeight:500,color:"#B0B0B0",py:1},children:"DOCUMENT NAME"})})]}),e.imageS3Urls.map((e,t)=>(0,n.jsxs)(c.ZP,{container:!0,sx:{px:3,py:1},alignItems:"center",children:[(0,n.jsx)(c.ZP,{item:!0,xs:1,children:(0,n.jsx)(d.Z,{checked:(null==Z?void 0:Z.document_ids.includes(e))||!1,onChange:()=>T(e,"document"),sx:{color:"green","&.Mui-checked":{color:"green"}}})}),(0,n.jsx)(c.ZP,{item:!0,xs:11,children:(0,n.jsx)(i.Z,{children:j(e)})})]},t))]}):(0,n.jsx)(a.Z,{children:(0,n.jsx)(i.Z,{sx:{fontSize:"16px",fontWeight:"400",color:"gray",ml:2,pb:3,textAlign:"center"},children:"There are no documents in this category."})})]},t))]},t)),(0,n.jsxs)(a.Z,{sx:{alignSelf:"center",display:"flex",gap:"10px",justifyContent:"center"},children:[(0,n.jsxs)(s.Z,{variant:"contained",onClick:()=>u(0),sx:{alignSelf:"center",display:"flex",gap:"10px",textTransform:"none",borderRadius:"8px",bgcolor:"black",color:"white",boxShadow:"none"},children:[(0,n.jsx)(a.Z,{sx:{display:"flex",alignItems:"center"},children:(0,n.jsx)(g.Z,{color:"white",size:16})}),"Back"," "]}),(0,n.jsx)(s.Z,{variant:"contained",sx:{textTransform:"none",borderRadius:"8px",bgcolor:"black",color:"white",boxShadow:"none",height:"36px",paddingY:"10px","&:hover":{backgroundColor:"rgba(0, 0, 0, 0.1)",boxShadow:"none"}},disabled:B,onClick:F,children:"Confirm Changes"})]})]})})]})]})]})})})},96471:function(e,t,r){"use strict";r.d(t,{Z:function(){return l}});var n=r(2265);let o=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),a=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return t.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim()};var i={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let s=(0,n.forwardRef)((e,t)=>{let{color:r="currentColor",size:o=24,strokeWidth:s=2,absoluteStrokeWidth:l,className:c="",children:d,iconNode:u,...x}=e;return(0,n.createElement)("svg",{ref:t,...i,width:o,height:o,stroke:r,strokeWidth:l?24*Number(s)/Number(o):s,className:a("lucide",c),...x},[...u.map(e=>{let[t,r]=e;return(0,n.createElement)(t,r)}),...Array.isArray(d)?d:[d]])}),l=(e,t)=>{let r=(0,n.forwardRef)((r,i)=>{let{className:l,...c}=r;return(0,n.createElement)(s,{ref:i,iconNode:t,className:a("lucide-".concat(o(e)),l),...c})});return r.displayName="".concat(e),r}},5485:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});let n=(0,r(96471).Z)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},91230:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});let n=(0,r(96471).Z)("ArrowRight",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])}},function(e){e.O(0,[139,3463,3301,8575,293,9810,187,3145,9932,3919,9129,2786,9826,8166,8760,9414,1953,3344,9662,1356,2971,2117,1744],function(){return e(e.s=96065)}),_N_E=e.O()}]);