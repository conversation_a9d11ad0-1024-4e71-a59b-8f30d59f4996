(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4401],{75190:function(a,e,t){Promise.resolve().then(t.bind(t,52173))},52173:function(a,e,t){"use strict";t.r(e),t.d(e,{default:function(){return d}});var c=t(57437),s=t(2265),n=t(20449),r=t.n(n);function d(){let[a,e]=(0,s.useState)("All");return(0,s.useEffect)(()=>{document.querySelectorAll("tr[data-category]").forEach(e=>{"All"===a||e.getAttribute("data-category")===a?e.style.display="":e.style.display="none"})},[a]),(0,c.jsx)(c.Fragment,{children:(0,c.jsxs)("div",{className:r().container,children:[(0,c.jsxs)("header",{className:r().header,children:[(0,c.jsx)("h1",{className:r().title,children:"\uD83D\uDD10 HIPAA Compliance at BenOsphere"}),(0,c.jsxs)("div",{className:r().infoBox,children:[(0,c.jsxs)("p",{className:r().infoText,children:["At BenOsphere, protecting employee health information is a top priority. As a platform trusted to handle sensitive benefits and wellness data, we are fully committed to maintaining HIPAA-compliant systems, policies, and processes. Our HIPAA compliance program has been reviewed and attested by the team at ",(0,c.jsx)("a",{href:"https://www.compliancy.com/",className:r().link,target:"_blank",rel:"noopener noreferrer",children:"Compliancy"}),", an industry-recognized third-party compliance partner."]}),(0,c.jsx)("p",{className:r().infoText,children:"We understand the responsibility that comes with managing protected health information (PHI), and we take that responsibility seriously. From secure infrastructure to strict access controls, every layer of BenOsphere is designed with privacy and compliance in mind."}),(0,c.jsxs)("p",{className:r().infoText,children:["Have questions about our HIPAA practices? \uD83D\uDCE9 ",(0,c.jsx)("a",{href:"mailto:<EMAIL>",className:r().link,children:"<EMAIL>"})]})]})]}),(0,c.jsx)("div",{className:r().filterContainer,children:["All","Product","Infrastructure","Organizational"].map(t=>(0,c.jsx)("button",{className:"".concat(r().filterButton," ").concat(a===t?r().filterButtonActive:""),onClick:()=>e(t),children:t},t))}),(0,c.jsx)("main",{children:(0,c.jsx)("div",{className:r().tableContainer,children:(0,c.jsxs)("table",{className:r().table,children:[(0,c.jsx)("thead",{children:(0,c.jsxs)("tr",{className:r().tr,children:[(0,c.jsx)("th",{className:r().th}),(0,c.jsx)("th",{className:r().th,children:"Control"}),(0,c.jsx)("th",{className:r().th,children:"Category"}),(0,c.jsx)("th",{className:r().th,children:"Description"})]})}),(0,c.jsxs)("tbody",{children:[(0,c.jsxs)("tr",{"data-category":"Product",children:[(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().checkmark),children:"✓"}),(0,c.jsx)("td",{className:r().td,children:"Customer Data Deletion Upon Termination"}),(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().category),children:"Product"}),(0,c.jsx)("td",{className:r().td,children:"Customer data is securely deleted when no longer needed or upon contract termination in accordance with data retention policies."})]}),(0,c.jsxs)("tr",{"data-category":"Product",children:[(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().checkmark),children:"✓"}),(0,c.jsx)("td",{className:r().td,children:"Data Classification"}),(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().category),children:"Product"}),(0,c.jsx)("td",{className:r().td,children:"Data is classified by sensitivity and handled accordingly to ensure appropriate levels of protection."})]}),(0,c.jsxs)("tr",{"data-category":"Organizational",children:[(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().checkmark),children:"✓"}),(0,c.jsx)("td",{className:r().td,children:"Designated Security Officials"}),(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().category),children:"Organizational"}),(0,c.jsx)("td",{className:r().td,children:"BenOsphere has implemented and verified control for: Designated Security Officials."})]}),(0,c.jsxs)("tr",{"data-category":"Organizational",children:[(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().checkmark),children:"✓"}),(0,c.jsx)("td",{className:r().td,children:"Contractor Requirements"}),(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().category),children:"Organizational"}),(0,c.jsx)("td",{className:r().td,children:"BenOsphere has implemented and verified control for: Contractor Requirements."})]}),(0,c.jsxs)("tr",{"data-category":"Infrastructure",children:[(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().checkmark),children:"✓"}),(0,c.jsx)("td",{className:r().td,children:"Credential Keys Managed"}),(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().category),children:"Infrastructure"}),(0,c.jsx)("td",{className:r().td,children:"BenOsphere has implemented and verified control for: Credential Keys Managed."})]}),(0,c.jsxs)("tr",{"data-category":"Infrastructure",children:[(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().checkmark),children:"✓"}),(0,c.jsx)("td",{className:r().td,children:"Cryptography Policies"}),(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().category),children:"Infrastructure"}),(0,c.jsx)("td",{className:r().td,children:"BenOsphere has implemented and verified control for: Cryptography Policies."})]}),(0,c.jsxs)("tr",{"data-category":"Infrastructure",children:[(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().checkmark),children:"✓"}),(0,c.jsx)("td",{className:r().td,children:"System Access Granted"}),(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().category),children:"Infrastructure"}),(0,c.jsx)("td",{className:r().td,children:"System access is managed using a role-based model and is revoked immediately upon employee or contractor termination."})]}),(0,c.jsxs)("tr",{"data-category":"Infrastructure",children:[(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().checkmark),children:"✓"}),(0,c.jsx)("td",{className:r().td,children:"Terminated Employee Access Revoked Within One Business Day"}),(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().category),children:"Infrastructure"}),(0,c.jsx)("td",{className:r().td,children:"System access is managed using a role-based model and is revoked immediately upon employee or contractor termination."})]}),(0,c.jsxs)("tr",{"data-category":"Infrastructure",children:[(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().checkmark),children:"✓"}),(0,c.jsx)("td",{className:r().td,children:"Unique Accounts Used"}),(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().category),children:"Infrastructure"}),(0,c.jsx)("td",{className:r().td,children:"BenOsphere has implemented and verified control for: Unique Accounts Used."})]}),(0,c.jsxs)("tr",{"data-category":"Infrastructure",children:[(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().checkmark),children:"✓"}),(0,c.jsx)("td",{className:r().td,children:"Unique SSH"}),(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().category),children:"Infrastructure"}),(0,c.jsx)("td",{className:r().td,children:"Public SSH access is disabled. Only authenticated and authorized users with unique credentials can access systems via secure protocols."})]}),(0,c.jsxs)("tr",{"data-category":"Product",children:[(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().checkmark),children:"✓"}),(0,c.jsx)("td",{className:r().td,children:"Users Can Access All Their Information"}),(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().category),children:"Product"}),(0,c.jsx)("td",{className:r().td,children:"Each user is provisioned with a unique account. Access is monitored and governed by least privilege principles."})]}),(0,c.jsxs)("tr",{"data-category":"Product",children:[(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().checkmark),children:"✓"}),(0,c.jsx)("td",{className:r().td,children:"Users Can Update their Information"}),(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().category),children:"Product"}),(0,c.jsx)("td",{className:r().td,children:"Users can view and update their information through a secure, authenticated interface in compliance with data accuracy standards."})]}),(0,c.jsxs)("tr",{"data-category":"Infrastructure",children:[(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().checkmark),children:"✓"}),(0,c.jsx)("td",{className:r().td,children:"VPN Required for Production Access"}),(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().category),children:"Infrastructure"}),(0,c.jsx)("td",{className:r().td,children:"Production systems are only accessible through a secure VPN to protect internal assets from unauthorized external access."})]}),(0,c.jsxs)("tr",{"data-category":"Infrastructure",children:[(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().checkmark),children:"✓"}),(0,c.jsx)("td",{className:r().td,children:"Vulnerability Management"}),(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().category),children:"Infrastructure"}),(0,c.jsx)("td",{className:r().td,children:"BenOsphere has implemented and verified control for: Vulnerability Management."})]}),(0,c.jsxs)("tr",{"data-category":"Product",children:[(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().checkmark),children:"✓"}),(0,c.jsx)("td",{className:r().td,children:"Inactivity and Browser Exit Logout"}),(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().category),children:"Product"}),(0,c.jsx)("td",{className:r().td,children:"System activity and security events are logged centrally and monitored for suspicious behavior with real-time alerts."})]}),(0,c.jsxs)("tr",{"data-category":"Product",children:[(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().checkmark),children:"✓"}),(0,c.jsx)("td",{className:r().td,children:"Least-Privileged Policy for Customer Data Access"}),(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().category),children:"Product"}),(0,c.jsx)("td",{className:r().td,children:"A formal Least-Privileged Policy for Customer Data Access is in place to govern consistent and secure operations across the organization."})]}),(0,c.jsxs)("tr",{"data-category":"Infrastructure",children:[(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().checkmark),children:"✓"}),(0,c.jsx)("td",{className:r().td,children:"Log Management System"}),(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().category),children:"Infrastructure"}),(0,c.jsx)("td",{className:r().td,children:"System activity and security events are logged centrally and monitored for suspicious behavior with real-time alerts."})]}),(0,c.jsxs)("tr",{"data-category":"Infrastructure",children:[(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().checkmark),children:"✓"}),(0,c.jsx)("td",{className:r().td,children:"Logging/Monitoring"}),(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().category),children:"Infrastructure"}),(0,c.jsx)("td",{className:r().td,children:"System activity and security events are logged centrally and monitored for suspicious behavior with real-time alerts."})]}),(0,c.jsxs)("tr",{"data-category":"Infrastructure",children:[(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().checkmark),children:"✓"}),(0,c.jsx)("td",{className:r().td,children:"Logs Centrally Stored"}),(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().category),children:"Infrastructure"}),(0,c.jsx)("td",{className:r().td,children:"System activity and security events are logged centrally and monitored for suspicious behavior with real-time alerts."})]}),(0,c.jsxs)("tr",{"data-category":"Infrastructure",children:[(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().checkmark),children:"✓"}),(0,c.jsx)("td",{className:r().td,children:"Malware Detection Software Installed"}),(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().category),children:"Infrastructure"}),(0,c.jsx)("td",{className:r().td,children:"BenOsphere has implemented and verified control for: Malware Detection Software Installed."})]}),(0,c.jsxs)("tr",{"data-category":"Infrastructure",children:[(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().checkmark),children:"✓"}),(0,c.jsx)("td",{className:r().td,children:"Multiple Availability Zones"}),(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().category),children:"Infrastructure"}),(0,c.jsx)("td",{className:r().td,children:"BenOsphere has implemented and verified control for: Multiple Availability Zones."})]}),(0,c.jsxs)("tr",{"data-category":"Infrastructure",children:[(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().checkmark),children:"✓"}),(0,c.jsx)("td",{className:r().td,children:"Network segmentation in place"}),(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().category),children:"Infrastructure"}),(0,c.jsx)("td",{className:r().td,children:"BenOsphere has implemented and verified control for: Network segmentation in place."})]}),(0,c.jsxs)("tr",{"data-category":"Organizational",children:[(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().checkmark),children:"✓"}),(0,c.jsx)("td",{className:r().td,children:"Operational Audit"}),(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().category),children:"Organizational"}),(0,c.jsx)("td",{className:r().td,children:"Security controls and data access are reviewed regularly to maintain regulatory compliance and identify improvement opportunities."})]}),(0,c.jsxs)("tr",{"data-category":"Organizational",children:[(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().checkmark),children:"✓"}),(0,c.jsx)("td",{className:r().td,children:"Oversight of Security Controls"}),(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().category),children:"Organizational"}),(0,c.jsx)("td",{className:r().td,children:"BenOsphere has implemented and verified control for: Oversight of Security Controls."})]}),(0,c.jsxs)("tr",{"data-category":"Infrastructure",children:[(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().checkmark),children:"✓"}),(0,c.jsx)("td",{className:r().td,children:"Password Manager"}),(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().category),children:"Infrastructure"}),(0,c.jsx)("td",{className:r().td,children:"BenOsphere has implemented and verified control for: Password Manager."})]}),(0,c.jsxs)("tr",{"data-category":"Organizational",children:[(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().checkmark),children:"✓"}),(0,c.jsx)("td",{className:r().td,children:"Password Policy"}),(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().category),children:"Organizational"}),(0,c.jsx)("td",{className:r().td,children:"A formal Password Policy is in place to govern consistent and secure operations across the organization."})]}),(0,c.jsxs)("tr",{"data-category":"Infrastructure",children:[(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().checkmark),children:"✓"}),(0,c.jsx)("td",{className:r().td,children:"Password Storage"}),(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().category),children:"Infrastructure"}),(0,c.jsx)("td",{className:r().td,children:"BenOsphere has implemented and verified control for: Password Storage."})]}),(0,c.jsxs)("tr",{"data-category":"Infrastructure",children:[(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().checkmark),children:"✓"}),(0,c.jsx)("td",{className:r().td,children:"Removable Media Device Encryption"}),(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().category),children:"Infrastructure"}),(0,c.jsx)("td",{className:r().td,children:"Data is encrypted both at rest and in transit using industry-standard protocols. Encryption keys are tightly controlled and accessible only to authorized personnel."})]}),(0,c.jsxs)("tr",{"data-category":"Product",children:[(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().checkmark),children:"✓"}),(0,c.jsx)("td",{className:r().td,children:"Require Authentication for Access"}),(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().category),children:"Product"}),(0,c.jsx)("td",{className:r().td,children:"System access is managed using a role-based model and is revoked immediately upon employee or contractor termination."})]}),(0,c.jsxs)("tr",{"data-category":"Infrastructure",children:[(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().checkmark),children:"✓"}),(0,c.jsx)("td",{className:r().td,children:"Require Encryption of Web-Based Admin Access"}),(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().category),children:"Infrastructure"}),(0,c.jsx)("td",{className:r().td,children:"Data is encrypted both at rest and in transit using industry-standard protocols. Encryption keys are tightly controlled and accessible only to authorized personnel."})]}),(0,c.jsxs)("tr",{"data-category":"Product",children:[(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().checkmark),children:"✓"}),(0,c.jsx)("td",{className:r().td,children:"Role-Based Security Implementation"}),(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().category),children:"Product"}),(0,c.jsx)("td",{className:r().td,children:"BenOsphere has implemented and verified control for: Role-Based Security Implementation."})]}),(0,c.jsxs)("tr",{"data-category":"Infrastructure",children:[(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().checkmark),children:"✓"}),(0,c.jsx)("td",{className:r().td,children:"Servers Monitored and Alarmed"}),(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().category),children:"Infrastructure"}),(0,c.jsx)("td",{className:r().td,children:"Critical infrastructure components are continuously monitored with alerts configured for anomalous behavior or system failures."})]}),(0,c.jsxs)("tr",{"data-category":"Infrastructure",children:[(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().checkmark),children:"✓"}),(0,c.jsx)("td",{className:r().td,children:"Session Lock"}),(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().category),children:"Infrastructure"}),(0,c.jsx)("td",{className:r().td,children:"BenOsphere has implemented and verified control for: Session Lock."})]}),(0,c.jsxs)("tr",{"data-category":"Infrastructure",children:[(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().checkmark),children:"✓"}),(0,c.jsx)("td",{className:r().td,children:"SSL/TLS Enforced"}),(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().category),children:"Infrastructure"}),(0,c.jsx)("td",{className:r().td,children:"BenOsphere has implemented and verified control for: SSL/TLS Enforced."})]}),(0,c.jsxs)("tr",{"data-category":"Organizational",children:[(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().checkmark),children:"✓"}),(0,c.jsx)("td",{className:r().td,children:"Activity Review"}),(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().category),children:"Organizational"}),(0,c.jsx)("td",{className:r().td,children:"Security controls and data access are reviewed regularly to maintain regulatory compliance and identify improvement opportunities."})]}),(0,c.jsxs)("tr",{"data-category":"Organizational",children:[(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().checkmark),children:"✓"}),(0,c.jsx)("td",{className:r().td,children:"Annual Access Control Review"}),(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().category),children:"Organizational"}),(0,c.jsx)("td",{className:r().td,children:"Security controls and data access are reviewed regularly to maintain regulatory compliance and identify improvement opportunities."})]}),(0,c.jsxs)("tr",{"data-category":"Organizational",children:[(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().checkmark),children:"✓"}),(0,c.jsx)("td",{className:r().td,children:"Annual Incident Response Test"}),(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().category),children:"Organizational"}),(0,c.jsx)("td",{className:r().td,children:"A comprehensive incident response plan exists and is tested annually to ensure swift action during security events."})]}),(0,c.jsxs)("tr",{"data-category":"Organizational",children:[(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().checkmark),children:"✓"}),(0,c.jsx)("td",{className:r().td,children:"Annual Penetration Tests"}),(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().category),children:"Organizational"}),(0,c.jsx)("td",{className:r().td,children:"BenOsphere has implemented and verified control for: Annual Penetration Tests."})]}),(0,c.jsxs)("tr",{"data-category":"Infrastructure",children:[(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().checkmark),children:"✓"}),(0,c.jsx)("td",{className:r().td,children:"Architectural Diagram"}),(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().category),children:"Infrastructure"}),(0,c.jsx)("td",{className:r().td,children:"BenOsphere has implemented and verified control for: Architectural Diagram."})]}),(0,c.jsxs)("tr",{"data-category":"Infrastructure",children:[(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().checkmark),children:"✓"}),(0,c.jsx)("td",{className:r().td,children:"Authentication Protocol"}),(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().category),children:"Infrastructure"}),(0,c.jsx)("td",{className:r().td,children:"BenOsphere has implemented and verified control for: Authentication Protocol."})]}),(0,c.jsxs)("tr",{"data-category":"Infrastructure",children:[(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().checkmark),children:"✓"}),(0,c.jsx)("td",{className:r().td,children:"Backup Integrity and Completeness"}),(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().category),children:"Infrastructure"}),(0,c.jsx)("td",{className:r().td,children:"Regular, automated backups are performed with integrity checks and monitored for successful completion to ensure recoverability in case of data loss."})]}),(0,c.jsxs)("tr",{"data-category":"Organizational",children:[(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().checkmark),children:"✓"}),(0,c.jsx)("td",{className:r().td,children:"Backup Policy"}),(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().category),children:"Organizational"}),(0,c.jsx)("td",{className:r().td,children:"Regular, automated backups are performed with integrity checks and monitored for successful completion to ensure recoverability in case of data loss."})]}),(0,c.jsxs)("tr",{"data-category":"Product",children:[(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().checkmark),children:"✓"}),(0,c.jsx)("td",{className:r().td,children:"Customer Data is Encrypted at Rest"}),(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().category),children:"Product"}),(0,c.jsx)("td",{className:r().td,children:"BenOsphere has implemented and verified control for: Customer Data is Encrypted at Rest."})]}),(0,c.jsxs)("tr",{"data-category":"Organizational",children:[(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().checkmark),children:"✓"}),(0,c.jsx)("td",{className:r().td,children:"Customer Data Policies"}),(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().category),children:"Organizational"}),(0,c.jsx)("td",{className:r().td,children:"BenOsphere has implemented and verified control for: Customer Data Policies."})]}),(0,c.jsxs)("tr",{"data-category":"Infrastructure",children:[(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().checkmark),children:"✓"}),(0,c.jsx)("td",{className:r().td,children:"Daily Backup Statuses Monitored"}),(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().category),children:"Infrastructure"}),(0,c.jsx)("td",{className:r().td,children:"Regular, automated backups are performed with integrity checks and monitored for successful completion to ensure recoverability in case of data loss."})]}),(0,c.jsxs)("tr",{"data-category":"Organizational",children:[(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().checkmark),children:"✓"}),(0,c.jsx)("td",{className:r().td,children:"Data Destruction Policy"}),(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().category),children:"Organizational"}),(0,c.jsx)("td",{className:r().td,children:"A formal Data Destruction Policy is in place to govern consistent and secure operations across the organization."})]}),(0,c.jsxs)("tr",{"data-category":"Organizational",children:[(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().checkmark),children:"✓"}),(0,c.jsx)("td",{className:r().td,children:"Data Retention Policy"}),(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().category),children:"Organizational"}),(0,c.jsx)("td",{className:r().td,children:"A formal Data Retention Policy is in place to govern consistent and secure operations across the organization."})]}),(0,c.jsxs)("tr",{"data-category":"Infrastructure",children:[(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().checkmark),children:"✓"}),(0,c.jsx)("td",{className:r().td,children:"Database Monitored and Alarmed"}),(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().category),children:"Infrastructure"}),(0,c.jsx)("td",{className:r().td,children:"Critical infrastructure components are continuously monitored with alerts configured for anomalous behavior or system failures."})]}),(0,c.jsxs)("tr",{"data-category":"Infrastructure",children:[(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().checkmark),children:"✓"}),(0,c.jsx)("td",{className:r().td,children:"Denial of Public SSH"}),(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().category),children:"Infrastructure"}),(0,c.jsx)("td",{className:r().td,children:"Public SSH access is disabled. Only authenticated and authorized users with unique credentials can access systems via secure protocols."})]}),(0,c.jsxs)("tr",{"data-category":"Organizational",children:[(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().checkmark),children:"✓"}),(0,c.jsx)("td",{className:r().td,children:"Disaster Recovery Plan"}),(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().category),children:"Organizational"}),(0,c.jsx)("td",{className:r().td,children:"Business continuity and disaster recovery plans are in place to minimize disruption during unforeseen incidents."})]}),(0,c.jsxs)("tr",{"data-category":"Infrastructure",children:[(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().checkmark),children:"✓"}),(0,c.jsx)("td",{className:r().td,children:"Disposal of Sensitive Data on Hardware"}),(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().category),children:"Infrastructure"}),(0,c.jsx)("td",{className:r().td,children:"BenOsphere has implemented and verified control for: Disposal of Sensitive Data on Hardware."})]}),(0,c.jsxs)("tr",{"data-category":"Infrastructure",children:[(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().checkmark),children:"✓"}),(0,c.jsx)("td",{className:r().td,children:"Disposal of Sensitive Data on Paper"}),(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().category),children:"Infrastructure"}),(0,c.jsx)("td",{className:r().td,children:"BenOsphere has implemented and verified control for: Disposal of Sensitive Data on Paper."})]}),(0,c.jsxs)("tr",{"data-category":"Organizational",children:[(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().checkmark),children:"✓"}),(0,c.jsx)("td",{className:r().td,children:"Encryption Policy"}),(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().category),children:"Organizational"}),(0,c.jsx)("td",{className:r().td,children:"Data is encrypted both at rest and in transit using industry-standard protocols. Encryption keys are tightly controlled and accessible only to authorized personnel."})]}),(0,c.jsxs)("tr",{"data-category":"Infrastructure",children:[(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().checkmark),children:"✓"}),(0,c.jsx)("td",{className:r().td,children:"Event Logging"}),(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().category),children:"Infrastructure"}),(0,c.jsx)("td",{className:r().td,children:"System activity and security events are logged centrally and monitored for suspicious behavior with real-time alerts."})]}),(0,c.jsxs)("tr",{"data-category":"Infrastructure",children:[(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().checkmark),children:"✓"}),(0,c.jsx)("td",{className:r().td,children:"Failed Backup Alert and Action"}),(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().category),children:"Infrastructure"}),(0,c.jsx)("td",{className:r().td,children:"Regular, automated backups are performed with integrity checks and monitored for successful completion to ensure recoverability in case of data loss."})]}),(0,c.jsxs)("tr",{"data-category":"Infrastructure",children:[(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().checkmark),children:"✓"}),(0,c.jsx)("td",{className:r().td,children:"FIM (File Integrity Monitoring) Software in Place"}),(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().category),children:"Infrastructure"}),(0,c.jsx)("td",{className:r().td,children:"Critical infrastructure components are continuously monitored with alerts configured for anomalous behavior or system failures."})]}),(0,c.jsxs)("tr",{"data-category":"Infrastructure",children:[(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().checkmark),children:"✓"}),(0,c.jsx)("td",{className:r().td,children:"Hard-Disk Encryption"}),(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().category),children:"Infrastructure"}),(0,c.jsx)("td",{className:r().td,children:"Data is encrypted both at rest and in transit using industry-standard protocols. Encryption keys are tightly controlled and accessible only to authorized personnel."})]}),(0,c.jsxs)("tr",{"data-category":"Infrastructure",children:[(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().checkmark),children:"✓"}),(0,c.jsx)("td",{className:r().td,children:"Hardening Standards in Place"}),(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().category),children:"Infrastructure"}),(0,c.jsx)("td",{className:r().td,children:"BenOsphere has implemented and verified control for: Hardening Standards in Place."})]}),(0,c.jsxs)("tr",{"data-category":"Organizational",children:[(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().checkmark),children:"✓"}),(0,c.jsx)("td",{className:r().td,children:"Document Retention Period"}),(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().category),children:"Organizational"}),(0,c.jsx)("td",{className:r().td,children:"Data retention is governed by clearly defined policies ensuring legal and operational requirements are met."})]}),(0,c.jsxs)("tr",{"data-category":"Organizational",children:[(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().checkmark),children:"✓"}),(0,c.jsx)("td",{className:r().td,children:"Employee Disclosure Process"}),(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().category),children:"Organizational"}),(0,c.jsx)("td",{className:r().td,children:"BenOsphere has implemented and verified control for: Employee Disclosure Process."})]}),(0,c.jsxs)("tr",{"data-category":"Organizational",children:[(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().checkmark),children:"✓"}),(0,c.jsx)("td",{className:r().td,children:"Follow-Ups Tracked"}),(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().category),children:"Organizational"}),(0,c.jsx)("td",{className:r().td,children:"BenOsphere has implemented and verified control for: Follow-Ups Tracked."})]}),(0,c.jsxs)("tr",{"data-category":"Organizational",children:[(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().checkmark),children:"✓"}),(0,c.jsx)("td",{className:r().td,children:"HIPAA Awareness Training"}),(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().category),children:"Organizational"}),(0,c.jsx)("td",{className:r().td,children:"All employees and contractors complete regular HIPAA and cybersecurity training to ensure awareness of responsibilities and threats."})]}),(0,c.jsxs)("tr",{"data-category":"Organizational",children:[(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().checkmark),children:"✓"}),(0,c.jsx)("td",{className:r().td,children:"Incident Response Team"}),(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().category),children:"Organizational"}),(0,c.jsx)("td",{className:r().td,children:"A comprehensive incident response plan exists and is tested annually to ensure swift action during security events."})]}),(0,c.jsxs)("tr",{"data-category":"Organizational",children:[(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().checkmark),children:"✓"}),(0,c.jsx)("td",{className:r().td,children:"Incident Response Plan"}),(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().category),children:"Organizational"}),(0,c.jsx)("td",{className:r().td,children:"A comprehensive incident response plan exists and is tested annually to ensure swift action during security events."})]}),(0,c.jsxs)("tr",{"data-category":"Organizational",children:[(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().checkmark),children:"✓"}),(0,c.jsx)("td",{className:r().td,children:"Termination/Offboarding Checklist"}),(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().category),children:"Organizational"}),(0,c.jsx)("td",{className:r().td,children:"BenOsphere has implemented and verified control for: Termination/Offboarding Checklist."})]}),(0,c.jsxs)("tr",{"data-category":"Organizational",children:[(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().checkmark),children:"✓"}),(0,c.jsx)("td",{className:r().td,children:"3rd Parties and Vendors Given Instructions on Breach Reporting"}),(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().category),children:"Organizational"}),(0,c.jsx)("td",{className:r().td,children:"Vendors are assessed for compliance, and agreements are maintained to ensure they meet BenOsphere’s security and privacy standards."})]}),(0,c.jsxs)("tr",{"data-category":"Organizational",children:[(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().checkmark),children:"✓"}),(0,c.jsx)("td",{className:r().td,children:"Acceptable Use Policy Employees Acknowledge"}),(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().category),children:"Organizational"}),(0,c.jsx)("td",{className:r().td,children:"A formal Acceptable Use Policy Employees Acknowledge is in place to govern consistent and secure operations across the organization."})]}),(0,c.jsxs)("tr",{"data-category":"Organizational",children:[(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().checkmark),children:"✓"}),(0,c.jsx)("td",{className:r().td,children:"Allowable Use and Disclosure"}),(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().category),children:"Organizational"}),(0,c.jsx)("td",{className:r().td,children:"BenOsphere has implemented and verified control for: Allowable Use and Disclosure."})]}),(0,c.jsxs)("tr",{"data-category":"Organizational",children:[(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().checkmark),children:"✓"}),(0,c.jsx)("td",{className:r().td,children:"Annual Review of Purposes"}),(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().category),children:"Organizational"}),(0,c.jsx)("td",{className:r().td,children:"Security controls and data access are reviewed regularly to maintain regulatory compliance and identify improvement opportunities."})]}),(0,c.jsxs)("tr",{"data-category":"Organizational",children:[(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().checkmark),children:"✓"}),(0,c.jsx)("td",{className:r().td,children:"Asset Management Policy"}),(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().category),children:"Organizational"}),(0,c.jsx)("td",{className:r().td,children:"A formal Asset Management Policy is in place to govern consistent and secure operations across the organization."})]}),(0,c.jsxs)("tr",{"data-category":"Organizational",children:[(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().checkmark),children:"✓"}),(0,c.jsx)("td",{className:r().td,children:"Background Checks"}),(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().category),children:"Organizational"}),(0,c.jsx)("td",{className:r().td,children:"BenOsphere has implemented and verified control for: Background Checks."})]}),(0,c.jsxs)("tr",{"data-category":"Organizational",children:[(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().checkmark),children:"✓"}),(0,c.jsx)("td",{className:r().td,children:"Business Associate Agreements"}),(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().category),children:"Organizational"}),(0,c.jsx)("td",{className:r().td,children:"BenOsphere has implemented and verified control for: Business Associate Agreements."})]}),(0,c.jsxs)("tr",{"data-category":"Organizational",children:[(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().checkmark),children:"✓"}),(0,c.jsx)("td",{className:r().td,children:"Business Continuity Plan"}),(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().category),children:"Organizational"}),(0,c.jsx)("td",{className:r().td,children:"Business continuity and disaster recovery plans are in place to minimize disruption during unforeseen incidents."})]}),(0,c.jsxs)("tr",{"data-category":"Organizational",children:[(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().checkmark),children:"✓"}),(0,c.jsx)("td",{className:r().td,children:"Business Impact Analysis"}),(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().category),children:"Organizational"}),(0,c.jsx)("td",{className:r().td,children:"BenOsphere has implemented and verified control for: Business Impact Analysis."})]}),(0,c.jsxs)("tr",{"data-category":"Organizational",children:[(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().checkmark),children:"✓"}),(0,c.jsx)("td",{className:r().td,children:"Breach Notification"}),(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().category),children:"Organizational"}),(0,c.jsx)("td",{className:r().td,children:"BenOsphere has implemented and verified control for: Breach Notification."})]}),(0,c.jsxs)("tr",{"data-category":"Organizational",children:[(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().checkmark),children:"✓"}),(0,c.jsx)("td",{className:r().td,children:"Board Oversight Briefings Conducted"}),(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().category),children:"Organizational"}),(0,c.jsx)("td",{className:r().td,children:"BenOsphere has implemented and verified control for: Board Oversight Briefings Conducted."})]}),(0,c.jsxs)("tr",{"data-category":"Organizational",children:[(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().checkmark),children:"✓"}),(0,c.jsx)("td",{className:r().td,children:"Code of Conduct"}),(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().category),children:"Organizational"}),(0,c.jsx)("td",{className:r().td,children:"BenOsphere has implemented and verified control for: Code of Conduct."})]}),(0,c.jsxs)("tr",{"data-category":"Product",children:[(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().checkmark),children:"✓"}),(0,c.jsx)("td",{className:r().td,children:"Commitments Explained to Customers"}),(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().category),children:"Product"}),(0,c.jsx)("td",{className:r().td,children:"BenOsphere has implemented and verified control for: Commitments Explained to Customers."})]}),(0,c.jsxs)("tr",{"data-category":"Organizational",children:[(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().checkmark),children:"✓"}),(0,c.jsx)("td",{className:r().td,children:"Communication to 3rd Parties"}),(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().category),children:"Organizational"}),(0,c.jsx)("td",{className:r().td,children:"BenOsphere has implemented and verified control for: Communication to 3rd Parties."})]}),(0,c.jsxs)("tr",{"data-category":"Organizational",children:[(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().checkmark),children:"✓"}),(0,c.jsx)("td",{className:r().td,children:"Conduct Control Self-Assessments"}),(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().category),children:"Organizational"}),(0,c.jsx)("td",{className:r().td,children:"BenOsphere has implemented and verified control for: Conduct Control Self-Assessments."})]}),(0,c.jsxs)("tr",{"data-category":"Organizational",children:[(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().checkmark),children:"✓"}),(0,c.jsx)("td",{className:r().td,children:"Continuous Control Monitoring"}),(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().category),children:"Organizational"}),(0,c.jsx)("td",{className:r().td,children:"Critical infrastructure components are continuously monitored with alerts configured for anomalous behavior or system failures."})]}),(0,c.jsxs)("tr",{"data-category":"Organizational",children:[(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().checkmark),children:"✓"}),(0,c.jsx)("td",{className:r().td,children:"Defined Management Roles & Responsibilities"}),(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().category),children:"Organizational"}),(0,c.jsx)("td",{className:r().td,children:"BenOsphere has implemented and verified control for: Defined Management Roles & Responsibilities."})]}),(0,c.jsxs)("tr",{"data-category":"Organizational",children:[(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().checkmark),children:"✓"}),(0,c.jsx)("td",{className:r().td,children:"Data Protection Policy"}),(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().category),children:"Organizational"}),(0,c.jsx)("td",{className:r().td,children:"A formal Data Protection Policy is in place to govern consistent and secure operations across the organization."})]}),(0,c.jsxs)("tr",{"data-category":"Infrastructure",children:[(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().checkmark),children:"✓"}),(0,c.jsx)("td",{className:r().td,children:"DLP (Data Loss Prevention) Software is Used"}),(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().category),children:"Infrastructure"}),(0,c.jsx)("td",{className:r().td,children:"BenOsphere has implemented and verified control for: DLP (Data Loss Prevention) Software is Used."})]}),(0,c.jsxs)("tr",{"data-category":"Organizational",children:[(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().checkmark),children:"✓"}),(0,c.jsx)("td",{className:r().td,children:"Disclosure with 3rd Parties"}),(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().category),children:"Organizational"}),(0,c.jsx)("td",{className:r().td,children:"BenOsphere has implemented and verified control for: Disclosure with 3rd Parties."})]}),(0,c.jsxs)("tr",{"data-category":"Organizational",children:[(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().checkmark),children:"✓"}),(0,c.jsx)("td",{className:r().td,children:"Disclosure Process for Customers"}),(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().category),children:"Organizational"}),(0,c.jsx)("td",{className:r().td,children:"BenOsphere has implemented and verified control for: Disclosure Process for Customers."})]}),(0,c.jsxs)("tr",{"data-category":"Organizational",children:[(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().checkmark),children:"✓"}),(0,c.jsx)("td",{className:r().td,children:"Information Security Policy"}),(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().category),children:"Organizational"}),(0,c.jsx)("td",{className:r().td,children:"A formal Information Security Policy is in place to govern consistent and secure operations across the organization."})]}),(0,c.jsxs)("tr",{"data-category":"Organizational",children:[(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().checkmark),children:"✓"}),(0,c.jsx)("td",{className:r().td,children:"Information Security Skills Matrix"}),(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().category),children:"Organizational"}),(0,c.jsx)("td",{className:r().td,children:"BenOsphere has implemented and verified control for: Information Security Skills Matrix."})]}),(0,c.jsxs)("tr",{"data-category":"Organizational",children:[(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().checkmark),children:"✓"}),(0,c.jsx)("td",{className:r().td,children:"Maintains a Privacy Policy"}),(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().category),children:"Organizational"}),(0,c.jsx)("td",{className:r().td,children:"A formal Maintains a Privacy Policy is in place to govern consistent and secure operations across the organization."})]}),(0,c.jsxs)("tr",{"data-category":"Organizational",children:[(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().checkmark),children:"✓"}),(0,c.jsx)("td",{className:r().td,children:"Maintains Asset Inventory"}),(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().category),children:"Organizational"}),(0,c.jsx)("td",{className:r().td,children:"BenOsphere has implemented and verified control for: Maintains Asset Inventory."})]}),(0,c.jsxs)("tr",{"data-category":"Infrastructure",children:[(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().checkmark),children:"✓"}),(0,c.jsx)("td",{className:r().td,children:"Messaging Queues Monitored and Alarmed"}),(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().category),children:"Infrastructure"}),(0,c.jsx)("td",{className:r().td,children:"Critical infrastructure components are continuously monitored with alerts configured for anomalous behavior or system failures."})]}),(0,c.jsxs)("tr",{"data-category":"Organizational",children:[(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().checkmark),children:"✓"}),(0,c.jsx)("td",{className:r().td,children:"Notice of Breach to Affected Users"}),(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().category),children:"Organizational"}),(0,c.jsx)("td",{className:r().td,children:"Each user is provisioned with a unique account. Access is monitored and governed by least privilege principles."})]}),(0,c.jsxs)("tr",{"data-category":"Organizational",children:[(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().checkmark),children:"✓"}),(0,c.jsx)("td",{className:r().td,children:"PII with 3rd Parties and Vendors"}),(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().category),children:"Organizational"}),(0,c.jsx)("td",{className:r().td,children:"Vendors are assessed for compliance, and agreements are maintained to ensure they meet BenOsphere’s security and privacy standards."})]}),(0,c.jsxs)("tr",{"data-category":"Organizational",children:[(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().checkmark),children:"✓"}),(0,c.jsx)("td",{className:r().td,children:"Privacy Policy Includes 3rd Party Vendors"}),(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().category),children:"Organizational"}),(0,c.jsx)("td",{className:r().td,children:"A formal Privacy Policy Includes 3rd Party Vendors is in place to govern consistent and secure operations across the organization."})]}),(0,c.jsxs)("tr",{"data-category":"Organizational",children:[(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().checkmark),children:"✓"}),(0,c.jsx)("td",{className:r().td,children:"Privacy Policy Publicly Available"}),(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().category),children:"Organizational"}),(0,c.jsx)("td",{className:r().td,children:"A formal Privacy Policy Publicly Available is in place to govern consistent and secure operations across the organization."})]}),(0,c.jsxs)("tr",{"data-category":"Organizational",children:[(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().checkmark),children:"✓"}),(0,c.jsx)("td",{className:r().td,children:"Privacy, Use, and Disclosure"}),(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().category),children:"Organizational"}),(0,c.jsx)("td",{className:r().td,children:"BenOsphere has implemented and verified control for: Privacy, Use, and Disclosure."})]}),(0,c.jsxs)("tr",{"data-category":"Organizational",children:[(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().checkmark),children:"✓"}),(0,c.jsx)("td",{className:r().td,children:"Provide Notice of Privacy Practices"}),(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().category),children:"Organizational"}),(0,c.jsx)("td",{className:r().td,children:"BenOsphere has implemented and verified control for: Provide Notice of Privacy Practices."})]}),(0,c.jsxs)("tr",{"data-category":"Organizational",children:[(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().checkmark),children:"✓"}),(0,c.jsx)("td",{className:r().td,children:"Quarterly Review of Privacy Compliance"}),(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().category),children:"Organizational"}),(0,c.jsx)("td",{className:r().td,children:"Security controls and data access are reviewed regularly to maintain regulatory compliance and identify improvement opportunities."})]}),(0,c.jsxs)("tr",{"data-category":"Organizational",children:[(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().checkmark),children:"✓"}),(0,c.jsx)("td",{className:r().td,children:"Remediation Plan"}),(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().category),children:"Organizational"}),(0,c.jsx)("td",{className:r().td,children:"BenOsphere has implemented and verified control for: Remediation Plan."})]}),(0,c.jsxs)("tr",{"data-category":"Organizational",children:[(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().checkmark),children:"✓"}),(0,c.jsx)("td",{className:r().td,children:"Review Privacy Notice Annually"}),(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().category),children:"Organizational"}),(0,c.jsx)("td",{className:r().td,children:"Security controls and data access are reviewed regularly to maintain regulatory compliance and identify improvement opportunities."})]}),(0,c.jsxs)("tr",{"data-category":"Organizational",children:[(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().checkmark),children:"✓"}),(0,c.jsx)("td",{className:r().td,children:"Risk Assessment Policy"}),(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().category),children:"Organizational"}),(0,c.jsx)("td",{className:r().td,children:"A formal Risk Assessment Policy is in place to govern consistent and secure operations across the organization."})]}),(0,c.jsxs)("tr",{"data-category":"Organizational",children:[(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().checkmark),children:"✓"}),(0,c.jsx)("td",{className:r().td,children:"Security Team Communicates in a Timely Manner"}),(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().category),children:"Organizational"}),(0,c.jsx)("td",{className:r().td,children:"A dedicated security team oversees compliance efforts and ensures accountability at the organizational level."})]}),(0,c.jsxs)("tr",{"data-category":"Organizational",children:[(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().checkmark),children:"✓"}),(0,c.jsx)("td",{className:r().td,children:"Security Team/Steering Committee"}),(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().category),children:"Organizational"}),(0,c.jsx)("td",{className:r().td,children:"A dedicated security team oversees compliance efforts and ensures accountability at the organizational level."})]}),(0,c.jsxs)("tr",{"data-category":"Organizational",children:[(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().checkmark),children:"✓"}),(0,c.jsx)("td",{className:r().td,children:"Security Training"}),(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().category),children:"Organizational"}),(0,c.jsx)("td",{className:r().td,children:"All employees and contractors complete regular HIPAA and cybersecurity training to ensure awareness of responsibilities and threats."})]}),(0,c.jsxs)("tr",{"data-category":"Infrastructure",children:[(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().checkmark),children:"✓"}),(0,c.jsx)("td",{className:r().td,children:"Security Updates"}),(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().category),children:"Infrastructure"}),(0,c.jsx)("td",{className:r().td,children:"BenOsphere has implemented and verified control for: Security Updates."})]}),(0,c.jsxs)("tr",{"data-category":"Organizational",children:[(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().checkmark),children:"✓"}),(0,c.jsx)("td",{className:r().td,children:"Software Development Life Cycle Policy"}),(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().category),children:"Organizational"}),(0,c.jsx)("td",{className:r().td,children:"A formal Software Development Life Cycle Policy is in place to govern consistent and secure operations across the organization."})]}),(0,c.jsxs)("tr",{"data-category":"Organizational",children:[(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().checkmark),children:"✓"}),(0,c.jsx)("td",{className:r().td,children:"Storage of Sensitive Data on Paper"}),(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().category),children:"Organizational"}),(0,c.jsx)("td",{className:r().td,children:"BenOsphere has implemented and verified control for: Storage of Sensitive Data on Paper."})]}),(0,c.jsxs)("tr",{"data-category":"Organizational",children:[(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().checkmark),children:"✓"}),(0,c.jsx)("td",{className:r().td,children:"System Access Control Policy"}),(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().category),children:"Organizational"}),(0,c.jsx)("td",{className:r().td,children:"A formal System Access Control Policy is in place to govern consistent and secure operations across the organization."})]}),(0,c.jsxs)("tr",{"data-category":"Organizational",children:[(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().checkmark),children:"✓"}),(0,c.jsx)("td",{className:r().td,children:"Unauthorized Disclosures by 3rd Parties"}),(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().category),children:"Organizational"}),(0,c.jsx)("td",{className:r().td,children:"BenOsphere has implemented and verified control for: Unauthorized Disclosures by 3rd Parties."})]}),(0,c.jsxs)("tr",{"data-category":"Organizational",children:[(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().checkmark),children:"✓"}),(0,c.jsx)("td",{className:r().td,children:"Vendor Agreements Maintained"}),(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().category),children:"Organizational"}),(0,c.jsx)("td",{className:r().td,children:"Vendors are assessed for compliance, and agreements are maintained to ensure they meet BenOsphere’s security and privacy standards."})]}),(0,c.jsxs)("tr",{"data-category":"Organizational",children:[(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().checkmark),children:"✓"}),(0,c.jsx)("td",{className:r().td,children:"Vendor Compliance Reports"}),(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().category),children:"Organizational"}),(0,c.jsx)("td",{className:r().td,children:"Vendors are assessed for compliance, and agreements are maintained to ensure they meet BenOsphere’s security and privacy standards."})]}),(0,c.jsxs)("tr",{"data-category":"Organizational",children:[(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().checkmark),children:"✓"}),(0,c.jsx)("td",{className:r().td,children:"Vendor Management Policy"}),(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().category),children:"Organizational"}),(0,c.jsx)("td",{className:r().td,children:"A formal Vendor Management Policy is in place to govern consistent and secure operations across the organization."})]}),(0,c.jsxs)("tr",{"data-category":"Organizational",children:[(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().checkmark),children:"✓"}),(0,c.jsx)("td",{className:r().td,children:"Vendors and PHI"}),(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().category),children:"Organizational"}),(0,c.jsx)("td",{className:r().td,children:"Vendors are assessed for compliance, and agreements are maintained to ensure they meet BenOsphere’s security and privacy standards."})]}),(0,c.jsxs)("tr",{"data-category":"Organizational",children:[(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().checkmark),children:"✓"}),(0,c.jsx)("td",{className:r().td,children:"Annual Risk Assessment"}),(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().category),children:"Organizational"}),(0,c.jsx)("td",{className:r().td,children:"BenOsphere has implemented and verified control for: Annual Risk Assessment."})]}),(0,c.jsxs)("tr",{"data-category":"Infrastructure",children:[(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().checkmark),children:"✓"}),(0,c.jsx)("td",{className:r().td,children:"Intrusion Detection System in Place"}),(0,c.jsx)("td",{className:"".concat(r().td," ").concat(r().category),children:"Infrastructure"}),(0,c.jsx)("td",{className:r().td,children:"BenOsphere has implemented and verified control for: Intrusion Detection System in Place."})]})]})]})})})]})})}},20449:function(a){a.exports={container:"benosphere_container__ikQpW",header:"benosphere_header__X0nC6",title:"benosphere_title__IT2vR",subtitle:"benosphere_subtitle__QQnHD",link:"benosphere_link__dOmks",tableContainer:"benosphere_tableContainer__aUKYv",filterContainer:"benosphere_filterContainer__6MXmY",filterButton:"benosphere_filterButton__xftuv",filterButtonActive:"benosphere_filterButtonActive__iEhmg",table:"benosphere_table__w3Gjt",th:"benosphere_th__0yscr",td:"benosphere_td__9_xQO",tr:"benosphere_tr__pthAH",checkmark:"benosphere_checkmark__R4DMJ",category:"benosphere_category__T8kJo",infoBox:"benosphere_infoBox__VCt9D",infoBoxHeader:"benosphere_infoBoxHeader__y2Q5k",infoText:"benosphere_infoText__5wMhH",infoBoxLogo:"benosphere_infoBoxLogo__xtwDn"}}},function(a){a.O(0,[927,2971,2117,1744],function(){return a(a.s=75190)}),_N_E=a.O()}]);