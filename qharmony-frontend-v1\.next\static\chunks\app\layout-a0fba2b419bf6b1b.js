(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3185,7534],{19849:function(e,t,n){Promise.resolve().then(n.bind(n,16210)),Promise.resolve().then(n.bind(n,62145)),Promise.resolve().then(n.bind(n,42899)),Promise.resolve().then(n.bind(n,31691)),Promise.resolve().then(n.bind(n,64119)),Promise.resolve().then(n.bind(n,18598)),Promise.resolve().then(n.bind(n,1696)),Promise.resolve().then(n.bind(n,40517)),Promise.resolve().then(n.bind(n,63386)),Promise.resolve().then(n.bind(n,98636)),Promise.resolve().then(n.bind(n,1252)),Promise.resolve().then(n.bind(n,21075)),Promise.resolve().then(n.bind(n,45165)),Promise.resolve().then(n.bind(n,77126)),Promise.resolve().then(n.bind(n,53146)),Promise.resolve().then(n.bind(n,63731)),Promise.resolve().then(n.bind(n,82156)),Promise.resolve().then(n.bind(n,20135)),Promise.resolve().then(n.bind(n,94873)),Promise.resolve().then(n.bind(n,80184)),Promise.resolve().then(n.t.bind(n,14811,23)),Promise.resolve().then(n.t.bind(n,2778,23)),Promise.resolve().then(n.bind(n,47369)),Promise.resolve().then(n.bind(n,41356)),Promise.resolve().then(n.bind(n,41454))},40256:function(e,t,n){"use strict";n.d(t,{$R:function(){return d},A_:function(){return s},BO:function(){return r},GH:function(){return u},_n:function(){return a},be:function(){return i},iG:function(){return c},j0:function(){return l}});var o=n(83464);let a="http://localhost:8080",r="<EMAIL>,<EMAIL>,<EMAIL>".split(",").map(e=>e.trim()),i=o.Z.create({baseURL:a});async function s(e,t,n){let o=new URL(n?"".concat(n).concat(e):"".concat(a).concat(e));return t&&Object.keys(t).forEach(e=>o.searchParams.append(e,t[e])),(await i.get(o.toString())).data}async function l(e,t,n){let o=n?"".concat(n).concat(e):"".concat(a).concat(e),r=await i.post(o,t,{headers:{"Content-Type":"application/json"}});return{status:r.status,data:r.data}}async function c(e,t,n){let o=n?"".concat(n).concat(e):"".concat(a).concat(e);console.log("Document upload to: ".concat(o));let r=await i.post(o,t,{headers:{"Content-Type":"multipart/form-data"}});return{status:r.status,data:r.data}}async function d(e,t,n){let o=new URL(n?"".concat(n).concat(e):"".concat(a).concat(e));return t&&Object.keys(t).forEach(e=>o.searchParams.append(e,t[e])),console.log("GET Blob request to: ".concat(o.toString())),(await i.get(o.toString(),{responseType:"blob"})).data}async function u(e,t,n){let o=n?"".concat(n).concat(e):"".concat(a).concat(e),r=await i.put(o,t,{headers:{"Content-Type":"application/json"}});return{status:r.status,data:r.data}}i.interceptors.request.use(e=>{let t=localStorage.getItem("userid1")||localStorage.getItem("userId");return t?e.headers["user-id"]=t:console.warn("No user ID found in localStorage for API request"),e})},47369:function(e,t,n){"use strict";n.d(t,{AuthProvider:function(){return g},a:function(){return b}});var o=n(57437),a=n(73143),r=n(2265),i=n(28411),s=n(43301),l=n(71986),c=n(84392);let d={auth:{clientId:"08e8620a-5979-4a37-b279-b2a92a75f515",authority:"https://login.microsoftonline.com/ca41443d-acdd-4223-9c81-dcaeb58b3406",redirectUri:window.location.hostname.includes("test.benosphere.com")?"https://test.benosphere.com/teams-landing":"https://app.benosphere.com/teams-landing",navigateToLoginRequestUrl:!1},cache:{cacheLocation:"sessionStorage",storeAuthStateInCookie:!1},system:{allowRedirectInIframe:!0,loggerOptions:{loggerCallback:(e,t)=>{console.log(t)},logLevel:l.i.Verbose}}},u=new c.Lx(d);var m=n(77534),f=n(99376);let p=(0,r.createContext)(void 0),g=e=>{let{children:t}=e,[n,l]=(0,r.useState)(null),[c,d]=(0,r.useState)(!0),g=(0,f.useRouter)(),b=()=>{Object.keys(localStorage).forEach(e=>{localStorage.removeItem(e)}),localStorage.removeItem("userid1"),localStorage.removeItem("userEmail1"),localStorage.removeItem("isTeamsApp1"),localStorage.removeItem("companyId1"),localStorage.removeItem("firstTimeLogin1"),localStorage.removeItem("wellness_results"),localStorage.removeItem("wellness_user_answers"),console.log("All localStorage items cleared")},y=()=>{document.cookie.split(";").forEach(e=>{let t=e.split("=")[0].trim();document.cookie="".concat(t,"=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;")}),console.log("All cookies cleared")};return(0,r.useEffect)(()=>{let e=(0,s.Aj)(i.I,e=>{if(e)console.log("Firebase user exists:",e),l(e);else{let e=localStorage.getItem("ssoDone1"),t=localStorage.getItem("userid1");console.log("isSSODone",e),console.log("userid1",t),a.j2().then(async()=>{var e,t,n,o,r;let i=await a.fw();console.log("Current user teams context:",null===(e=i.user)||void 0===e?void 0:e.loginHint);let s=null===(t=i.user)||void 0===t?void 0:t.loginHint,c=null===(o=i.user)||void 0===o?void 0:null===(n=o.tenant)||void 0===n?void 0:n.id;localStorage.setItem("userEmail1",s),localStorage.setItem("isTeamsApp1","true");let d=await (0,m.N8)(s,c);if("login_user"===d.data){console.log("Onboarding successful:",d);let e=d.userId,t=d.companyId;localStorage.setItem("userid1",e),localStorage.setItem("companyId1",t),localStorage.setItem("ssoDone1","true"),l(null===(r=i.user)||void 0===r?void 0:r.loginHint),g.push("/dashboard")}else g.push("/teams-landing")})}d(!1)});return()=>e()},[]),(0,o.jsx)(p.Provider,{value:{user:n,loading:c,logout:()=>{(0,s.w7)(i.I).then(()=>{console.log("Firebase user signed out"),l(null),b(),y(),g.push("/login")}).catch(e=>{console.error("Error signing out: ",e)}),u.logoutRedirect().catch(e=>{console.error("Error signing out from Microsoft: ",e)})},setUser:l},children:t})},b=()=>{let e=(0,r.useContext)(p);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}},77534:function(e,t,n){"use strict";n.d(t,{Ig:function(){return s},N8:function(){return i},lY:function(){return a},selfOnboard:function(){return r}});var o=n(40256);async function a(e){return(await (0,o.j0)("/auth/parse-params",{link:e})).data}async function r(e){return(await (0,o.j0)("/user/self-onboard",{userEmail:e})).data.data}async function i(e,t){return(await (0,o.j0)("/teams/user/self-onboard",{userEmail:e,tenantId:t})).data}async function s(e,t){let n=await (0,o.j0)("/employee/onboard",{companyId:e,userId:t});return console.log("Response from onboardEmployee:",n),n.data}},41356:function(e,t,n){"use strict";n.d(t,{StoreProvider:function(){return y}});var o=n(57437),a=n(68575),r=n(39129),i=n(70623),s=n(61835),l=n(39124),c=n(76792);let d=(0,r.oM)({name:"onboarding",initialState:{additionalParams:{isAdmin:!1},userDetails:{email:"",name:"",role:"",isAdmin:!1,isBroker:!1,isActivated:!1},companyDetails:{name:"",adminEmail:"",adminRole:"",companySize:0,industry:"",location:"",website:"",howHeard:"",brokerId:"",brokerageId:"",isBrokerage:!1,isActivated:!1}},reducers:{setUserDetails:(e,t)=>{e.userDetails=t.payload},setCompanyDetails:(e,t)=>{e.companyDetails=t.payload},setAdditionalParams:(e,t)=>{e.additionalParams=t.payload}}}),{setUserDetails:u,setCompanyDetails:m,setAdditionalParams:f}=d.actions;var p=d.reducer,g=n(47723);let b=(0,r.xC)({reducer:{user:i.ZP,company:s.ZP,benefits:l.ZP,qHarmonyBot:c.ZP,onboarding:p,mobileSidebarToggle:g.ZP}});function y(e){let{children:t}=e;return(0,o.jsx)(a.zt,{store:b,children:t})}},39124:function(e,t,n){"use strict";n.d(t,{D7:function(){return s},F5:function(){return i},H_:function(){return f},MJ:function(){return g},Sn:function(){return h},US:function(){return a},Yb:function(){return d},Yw:function(){return b},d8:function(){return v},iH:function(){return p},nM:function(){return y},oQ:function(){return r},oT:function(){return P}});let o=(0,n(39129).oM)({name:"benefits",initialState:{benefitsPerType:[],documentsPerBenefit:{benefitId:"",documents:[],links:[]},viewableDocuments:[],loadingDocuments:[],snackbarMessage:""},reducers:{setAllBenefitsPerType:(e,t)=>{e.benefitsPerType=t.payload},upsertBenefitsPerType:(e,t)=>{let{benefitType:n,benefits:o}=t.payload,a=e.benefitsPerType.findIndex(e=>e.benefitType===n);-1!==a?e.benefitsPerType[a]={benefitType:n,benefits:o}:e.benefitsPerType.push({benefitType:n,benefits:o})},setDocumentsPerBenefit:(e,t)=>{e.documentsPerBenefit=t.payload},setViewableDocuments:(e,t)=>{let n=t.payload.filter(t=>!e.viewableDocuments.some(e=>e.documentObjectKey===t.documentObjectKey));e.viewableDocuments=[...e.viewableDocuments,...n]},addViewableDocument:(e,t)=>{e.viewableDocuments.push(t.payload)},clearViewableDocuments:e=>{e.viewableDocuments=[]},clearBenefitsState:e=>{e.benefitsPerType=[],e.documentsPerBenefit={benefitId:"",documents:[],links:[]},e.viewableDocuments=[],e.loadingDocuments=[]},setLoadingDocument:(e,t)=>{e.loadingDocuments.push(t.payload)},clearLoadingDocument:(e,t)=>{e.loadingDocuments=e.loadingDocuments.filter(e=>e!==t.payload)},addDocument:(e,t)=>{let{benefitId:n,document:o}=t.payload;e.documentsPerBenefit.benefitId===n&&(e.documentsPerBenefit.documents=[...e.documentsPerBenefit.documents,o])},deleteDocument:(e,t)=>{let{benefitId:n,document:o}=t.payload;e.documentsPerBenefit.benefitId===n&&(e.documentsPerBenefit.documents=e.documentsPerBenefit.documents.filter(e=>e!==o),e.viewableDocuments=e.viewableDocuments.filter(e=>e.documentObjectKey!==o))},addLink:(e,t)=>{let{benefitId:n,link:o}=t.payload;e.documentsPerBenefit.benefitId===n&&(e.documentsPerBenefit.links=[...e.documentsPerBenefit.links,o])},deleteLink:(e,t)=>{let{benefitId:n,link:o}=t.payload;console.log("DELETE LINK REDUCER: ",e.documentsPerBenefit.benefitId),e.documentsPerBenefit.benefitId===n&&(e.documentsPerBenefit.links=e.documentsPerBenefit.links.filter(e=>e!==o))},setSnackbarMessage:(e,t)=>{e.snackbarMessage=t.payload},clearSnackbarMessage:e=>{e.snackbarMessage=""}}}),{setAllBenefitsPerType:a,upsertBenefitsPerType:r,setDocumentsPerBenefit:i,setViewableDocuments:s,addViewableDocument:l,clearViewableDocuments:c,clearBenefitsState:d,setLoadingDocument:u,clearLoadingDocument:m,addDocument:f,deleteDocument:p,addLink:g,deleteLink:b,setSnackbarMessage:y,clearSnackbarMessage:h}=o.actions,v=(e,t)=>{for(let{benefitType:n,benefits:o}of e.benefits.benefitsPerType){let e=o.find(e=>e._id===t);if(e)return{benefitType:n,benefit:e}}return null},P=(e,t)=>e.benefits.benefitsPerType.find(e=>e.benefitType===t)||null;t.ZP=o.reducer},61835:function(e,t,n){"use strict";n.d(t,{Vv:function(){return r},sy:function(){return i},x7:function(){return a}});let o=(0,n(39129).oM)({name:"company",initialState:{companyBenefitTypes:[],companyTeamMembers:[],companyDetails:{_id:"",name:"",companySize:0,industry:"",location:"",website:"",adminEmail:"",adminRole:"",brokerId:"",brokerageId:"",isBrokerage:!1,isActivated:!1,howHeard:"",details:{logo:""},__v:0}},reducers:{setCompanyBenefitTypes:(e,t)=>{e.companyBenefitTypes=t.payload},setCompanyTeamMembers:(e,t)=>{e.companyTeamMembers=t.payload},setCompanyDetails:(e,t)=>{console.log("COMPANY DETAILS PAYLOAD: ",t.payload),e.companyDetails=t.payload}}}),{setCompanyBenefitTypes:a,setCompanyTeamMembers:r,setCompanyDetails:i}=o.actions;t.ZP=o.reducer},47723:function(e,t,n){"use strict";n.d(t,{FJ:function(){return a},dL:function(){return r}});let o=(0,n(39129).oM)({name:"drawer",initialState:{isOpen:!1},reducers:{openDrawer:e=>{e.isOpen=!0},closeDrawer:e=>{e.isOpen=!1},toggleDrawer:e=>{e.isOpen=!e.isOpen}}}),{openDrawer:a,closeDrawer:r,toggleDrawer:i}=o.actions;t.ZP=o.reducer},76792:function(e,t,n){"use strict";n.d(t,{Hz:function(){return a},wt:function(){return i}});let o=(0,n(39129).oM)({name:"qHarmonyBot",initialState:{chatHistory:[],isLoading:!1},reducers:{addMessage:(e,t)=>{let{sender:n,message:o,timestamp:a}=t.payload;if(console.log("Adding message:",t.payload),"bot"===n&&e.chatHistory.length>0){let t=e.chatHistory[e.chatHistory.length-1];if("bot"===t.sender&&!t.timestamp.includes("Done")){t.message+=o,t.timestamp=a;return}}e.chatHistory.push(t.payload)},clearChatHistory:e=>{e.chatHistory=[]},setIsLoading:(e,t)=>{e.isLoading=t.payload}}}),{addMessage:a,clearChatHistory:r,setIsLoading:i}=o.actions;t.ZP=o.reducer},70623:function(e,t,n){"use strict";n.d(t,{$l:function(){return r},CS:function(){return l},Iv:function(){return a},MP:function(){return u},Re:function(){return s},Ym:function(){return d},ki:function(){return c},v2:function(){return i}});let o=(0,n(39129).oM)({name:"user",initialState:{_id:"",userProfile:{name:"",email:"",companyId:"",role:"",isAdmin:!1,isBroker:!1,details:{phoneNumber:void 0,department:void 0,title:void 0,role:void 0,dateOfBirth:void 0,hireDate:void 0,annualSalary:void 0,employeeClassType:"",customPayrollFrequency:"",ssn:"",address:void 0,mailingAddress:void 0,dependents:[],emergencyContact:void 0,employeeId:"",managerId:"",workLocation:"",workSchedule:"",ein:""}},selectedBenefitType:"",selectedBenefitId:"",selectedFAQQuestion:"",managedCompanies:[]},reducers:{setUserId:(e,t)=>{e._id=t.payload},setUserProfile:(e,t)=>{let{name:n,email:o,companyId:a,role:r,isAdmin:i,isBroker:s,details:l}=t.payload;e.userProfile={name:n,email:o,companyId:a,role:r,isAdmin:i,isBroker:s,details:l||e.userProfile.details}},setSelectedBenefitType:(e,t)=>{console.log("action.payload",t.payload),e.selectedBenefitType=t.payload},setSelectedBenefitId:(e,t)=>{e.selectedBenefitId=t.payload},setSelectedFAQQuestion:(e,t)=>{e.selectedFAQQuestion=t.payload},clearSelectedFAQQuestion:e=>{e.selectedFAQQuestion=""},setManagedCompanies:(e,t)=>{e.managedCompanies=t.payload}}}),{setUserId:a,setUserProfile:r,setSelectedBenefitType:i,setSelectedBenefitId:s,setSelectedFAQQuestion:l,clearSelectedFAQQuestion:c,setManagedCompanies:d}=o.actions,u=e=>e.user.userProfile.companyId;t.ZP=o.reducer},41454:function(e,t,n){"use strict";var o=n(99163);n(35669);let a=(0,o.Z)({breakpoints:{values:{xs:0,sm:600,md:768,lg:1200,xl:1536}},typography:{fontFamily:"SF Pro",logoTitle:{fontWeight:800,fontSize:"1.5rem",lineHeight:"1.2"},viewBenefitTypeSectionHeading:{fontWeight:"500",fontSize:"28px",lineHeight:"20.8px",color:"black",textAlign:"left",marginBottom:4,marginTop:5},toggleViewBenefitSubType:{fontWeight:"500",fontSize:"17px",lineHeight:"20.8px",color:"black",textAlign:"left"}},chip:{benefitStatusAvailableChip:{bgcolor:"#67BA6B1F",color:"#67BA6B",borderRadius:"8px","& .MuiChip-label":{padding:1,fontWeight:"semibold",fontSize:"14px"}},benefitStatusDisabledChip:{bgcolor:"#f0f0f0",color:"black",borderRadius:"8px","& .MuiChip-label":{padding:1,fontWeight:"semibold",fontSize:"14px"}}},button:{editBenefitButton:{backgroundColor:"#f0f0f0",borderRadius:"8px",textTransform:"none",color:"#000",padding:"4px",marginRight:3,boxShadow:"none",border:"none","&:hover":{backgroundColor:"#E0E0E0",boxShadow:"none"}}},palette:{backgroundBlue:{main:"#1073ff"},primary:{main:"#1073ff"},secondary:{main:"#ff4081"}}});t.default=a},28411:function(e,t,n){"use strict";n.d(t,{I:function(){return s}});var o=n(738),a=n(43301);let r=function(){{let e=window.location.hostname;if(e.includes("test.benosphere.com")||"localhost"===e)return{apiKey:"AIzaSyCPGurROOIHelcNrg3KK3UZpT5NY_v33cw",authDomain:"qharmony-test.firebaseapp.com",projectId:"qharmony-test",storageBucket:"qharmony-test.firebasestorage.app",messagingSenderId:"1017404738235",appId:"1:1017404738235:web:d0566182eb575065e3379e"}}return{apiKey:"AIzaSyAOalm98qF_u74s3qQlIqQ5A6IpWyd0wKA",authDomain:"qharmony-dev.firebaseapp.com",projectId:"qharmony-dev",storageBucket:"qharmony-dev.appspot.com",messagingSenderId:"756187162353",appId:"1:756187162353:web:3fc7d63dee1c57bc9d6b50"}}(),i=(0,o.C6)().length?(0,o.C6)()[0]:(0,o.ZF)(r),s=(0,a.v0)(i)},35669:function(){},2778:function(){}},function(e){e.O(0,[9429,2461,1784,139,3463,3301,8575,8685,9932,9129,5034,2971,2117,1744],function(){return e(e.s=19849)}),_N_E=e.O()}]);