(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2626,7534],{71853:function(t,e,n){Promise.resolve().then(n.bind(n,38991))},53284:function(t,e,n){"use strict";var o=n(94630),i=n(57437);e.Z=(0,o.Z)((0,i.jsx)("path",{d:"M6 17h3l2-4V7H5v6h3zm8 0h3l2-4V7h-6v6h3z"}),"FormatQuote")},31691:function(t,e,n){"use strict";n.d(e,{default:function(){return r}}),n(2265);var o=n(20135),i=n(55201),a=n(22166);function r(){let t=(0,o.default)(i.Z);return t[a.Z]||t}},94630:function(t,e,n){"use strict";n.d(e,{Z:function(){return A}});var o=n(2265),i=n(61994),a=n(20801),r=n(85657),l=n(16210),s=n(21086),c=n(37053),u=n(94143),d=n(50738);function g(t){return(0,d.ZP)("MuiSvgIcon",t)}(0,u.Z)("MuiSvgIcon",["root","colorPrimary","colorSecondary","colorAction","colorError","colorDisabled","fontSizeInherit","fontSizeSmall","fontSizeMedium","fontSizeLarge"]);var f=n(57437);let m=t=>{let{color:e,fontSize:n,classes:o}=t,i={root:["root","inherit"!==e&&"color".concat((0,r.Z)(e)),"fontSize".concat((0,r.Z)(n))]};return(0,a.Z)(i,g,o)},h=(0,l.default)("svg",{name:"MuiSvgIcon",slot:"Root",overridesResolver:(t,e)=>{let{ownerState:n}=t;return[e.root,"inherit"!==n.color&&e["color".concat((0,r.Z)(n.color))],e["fontSize".concat((0,r.Z)(n.fontSize))]]}})((0,s.Z)(t=>{var e,n,o,i,a,r,l,s,c,u,d,g,f,m,h,p,A,v;let{theme:b}=t;return{userSelect:"none",width:"1em",height:"1em",display:"inline-block",flexShrink:0,transition:null===(i=b.transitions)||void 0===i?void 0:null===(o=i.create)||void 0===o?void 0:o.call(i,"fill",{duration:null===(n=(null!==(h=b.vars)&&void 0!==h?h:b).transitions)||void 0===n?void 0:null===(e=n.duration)||void 0===e?void 0:e.shorter}),variants:[{props:t=>!t.hasSvgAsChild,style:{fill:"currentColor"}},{props:{fontSize:"inherit"},style:{fontSize:"inherit"}},{props:{fontSize:"small"},style:{fontSize:(null===(r=b.typography)||void 0===r?void 0:null===(a=r.pxToRem)||void 0===a?void 0:a.call(r,20))||"1.25rem"}},{props:{fontSize:"medium"},style:{fontSize:(null===(s=b.typography)||void 0===s?void 0:null===(l=s.pxToRem)||void 0===l?void 0:l.call(s,24))||"1.5rem"}},{props:{fontSize:"large"},style:{fontSize:(null===(u=b.typography)||void 0===u?void 0:null===(c=u.pxToRem)||void 0===c?void 0:c.call(u,35))||"2.1875rem"}},...Object.entries((null!==(p=b.vars)&&void 0!==p?p:b).palette).filter(t=>{let[,e]=t;return e&&e.main}).map(t=>{var e,n,o;let[i]=t;return{props:{color:i},style:{color:null===(n=(null!==(o=b.vars)&&void 0!==o?o:b).palette)||void 0===n?void 0:null===(e=n[i])||void 0===e?void 0:e.main}}}),{props:{color:"action"},style:{color:null===(g=(null!==(A=b.vars)&&void 0!==A?A:b).palette)||void 0===g?void 0:null===(d=g.action)||void 0===d?void 0:d.active}},{props:{color:"disabled"},style:{color:null===(m=(null!==(v=b.vars)&&void 0!==v?v:b).palette)||void 0===m?void 0:null===(f=m.action)||void 0===f?void 0:f.disabled}},{props:{color:"inherit"},style:{color:void 0}}]}})),p=o.forwardRef(function(t,e){let n=(0,c.i)({props:t,name:"MuiSvgIcon"}),{children:a,className:r,color:l="inherit",component:s="svg",fontSize:u="medium",htmlColor:d,inheritViewBox:g=!1,titleAccess:p,viewBox:A="0 0 24 24",...v}=n,b=o.isValidElement(a)&&"svg"===a.type,y={...n,color:l,component:s,fontSize:u,instanceFontSize:t.fontSize,inheritViewBox:g,viewBox:A,hasSvgAsChild:b},w={};g||(w.viewBox=A);let x=m(y);return(0,f.jsxs)(h,{as:s,className:(0,i.Z)(x.root,r),focusable:"false",color:d,"aria-hidden":!p||void 0,role:p?"img":void 0,ref:e,...w,...v,...b&&a.props,ownerState:y,children:[b?a.props.children:a,p?(0,f.jsx)("title",{children:p}):null]})});function A(t,e){function n(n,o){return(0,f.jsx)(p,{"data-testid":"".concat(e,"Icon"),ref:o,...n,children:t})}return n.muiName=p.muiName,o.memo(o.forwardRef(n))}p.muiName="SvgIcon"},40256:function(t,e,n){"use strict";n.d(e,{$R:function(){return u},A_:function(){return l},BO:function(){return a},GH:function(){return d},_n:function(){return i},be:function(){return r},iG:function(){return c},j0:function(){return s}});var o=n(83464);let i="http://localhost:8080",a="<EMAIL>,<EMAIL>,<EMAIL>".split(",").map(t=>t.trim()),r=o.Z.create({baseURL:i});async function l(t,e,n){let o=new URL(n?"".concat(n).concat(t):"".concat(i).concat(t));return e&&Object.keys(e).forEach(t=>o.searchParams.append(t,e[t])),(await r.get(o.toString())).data}async function s(t,e,n){let o=n?"".concat(n).concat(t):"".concat(i).concat(t),a=await r.post(o,e,{headers:{"Content-Type":"application/json"}});return{status:a.status,data:a.data}}async function c(t,e,n){let o=n?"".concat(n).concat(t):"".concat(i).concat(t);console.log("Document upload to: ".concat(o));let a=await r.post(o,e,{headers:{"Content-Type":"multipart/form-data"}});return{status:a.status,data:a.data}}async function u(t,e,n){let o=new URL(n?"".concat(n).concat(t):"".concat(i).concat(t));return e&&Object.keys(e).forEach(t=>o.searchParams.append(t,e[t])),console.log("GET Blob request to: ".concat(o.toString())),(await r.get(o.toString(),{responseType:"blob"})).data}async function d(t,e,n){let o=n?"".concat(n).concat(t):"".concat(i).concat(t),a=await r.put(o,e,{headers:{"Content-Type":"application/json"}});return{status:a.status,data:a.data}}r.interceptors.request.use(t=>{let e=localStorage.getItem("userid1")||localStorage.getItem("userId");return e?t.headers["user-id"]=e:console.warn("No user ID found in localStorage for API request"),t})},38991:function(t,e,n){"use strict";n.r(e);var o=n(57437),i=n(2265),a=n(43301),r=n(28411),l=n(99376),s=n(95656),c=n(35389),u=n(46387),d=n(77534),g=n(94509),f=n(27648);e.default=()=>{let t=(0,l.useRouter)(),[e,n]=(0,i.useState)(null),[m,h]=(0,i.useState)(null),[p,A]=(0,i.useState)(null),[v,b]=(0,i.useState)(!0),[y,w]=(0,i.useState)(null);return(0,i.useEffect)(()=>{(async()=>{let t=window.location.href,e=await (0,d.lY)(t),o=e.email,i=e.companyId,l=e.userId;if((0,a.JB)(r.I,t)){let t=window.localStorage.getItem("emailForSignIn1");t||e.email?(n(t||o),h(i),A(l)):w("Invalid sign-in link. Email not found.")}else w("Invalid sign-in link."),b(!1)})()},[]),(0,i.useEffect)(()=>{(async()=>{if(e)try{await (0,a.P6)(r.I,e,window.location.href),console.log("Sign-in successful"),window.localStorage.removeItem("emailForSignIn1"),localStorage.setItem("userid1",p),localStorage.setItem("companyId1",m),(0,d.Ig)(m,p),b(!1),t.push("/dashboard")}catch(t){console.error("Error signing in with email link:",t),w("Failed to sign in. Please try again."),b(!1)}})()},[e,m,p,t]),(0,o.jsx)(s.Z,{sx:{mt:5,textAlign:"center"},children:v?(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)(c.Z,{}),(0,o.jsx)(u.Z,{variant:"h6",sx:{mt:2},children:"Signing you in..."})]}):y?(0,o.jsx)(g.Z,{LeftComponent:(0,o.jsxs)(s.Z,{sx:{display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",color:"white",textAlign:"center",height:"100%"},children:[(0,o.jsx)(u.Z,{sx:{fontWeight:"bold",fontSize:"60px",mb:2},children:"❌ Magic Link Expired"}),(0,o.jsxs)(u.Z,{variant:"body1",sx:{fontSize:"20px",color:"#bbbbbb",mb:4},children:["You can sign in again by requesting a new link ",(0,o.jsx)(f.default,{href:"/",style:{color:"#B983FF",textDecoration:"underline"},children:"here"}),"."]})]})}):null})}},94509:function(t,e,n){"use strict";n.d(e,{Z:function(){return d}});var o=n(57437),i=n(89414),a=n(95656),r=n(46387),l=n(33145),s=n(53284),c=n(2265);let u=[{src:{src:"/_next/static/media/landing_page_image_1.883ba61a.png",height:1024,width:756,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAYAAAAICAIAAABVpBlvAAAAoUlEQVR42mOwNNRyMlRnYJRQUlNXNbISV9RgcHcwdrAwTPJ2ctVWFlTVkdPUZ7CxMw51MVvaklcb5cnAKqqoosGgbqrTmRe7e1ZbqZ+jnY6WgoEJg6CMUpify5UN07tivCU19O19fBiAoDjO/dzazqIoNwZ1CyklDYbGyTMO7N82rzwkkJchu6K6sr2D4eiV63suXl+3c09rU2Nt74TmKdMAG00wAXeqZ/wAAAAASUVORK5CYII=",blurWidth:6,blurHeight:8},text:"I get to keep track of all my benefits so I never miss out on the stuff that matters most.",name:"Troy Sivan",title:"Associate Manager"},{src:{src:"/_next/static/media/landing_page_image_2.59f16961.png",height:1024,width:756,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAYAAAAICAIAAABVpBlvAAAAo0lEQVR42mOYMakxwMdRRU/Lzs4sNtTTx9WGYeXS6TYOlik2GjGmKpbWJgWJgQwbN62M9rTbXJexsiTBWkXWydmWYcuurZO7G45NqluR6nfs+IkZq9cxHD1zatqS+Vsn1E4I0L3+4MH6nVsZZi9aFJWevXvn3i1rNtd2TQsMcmYAAgc3v8Ub989Zu6+kvgfEl5CWd/MJmrd8Y++0RYUVjQwMDACH00ArwNCIEAAAAABJRU5ErkJggg==",blurWidth:6,blurHeight:8},text:"Now I can track my benefits instantly without having to search through emails. It’s a game–changer!",name:"David Miller",title:"Software Engineer"},{src:{src:"/_next/static/media/landing_page_image_3.e9420572.png",height:1024,width:756,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAYAAAAICAIAAABVpBlvAAAAn0lEQVR42mNQE2YNsRJlYGAwlmL3UuUwFGNmcNbgXlNuNCkz0FtHTpydwUSSlUGBnaHYW29jc3pXoo8KA4ORNBeDOBODl7Ha3JyQQk8LLgYGYS4WBlN5PgYGhjgHw2wPM0FJGQYgsNeQZNbQj7HRzQlyU7WwU1VVZ3CyMrG2ssxxM9M2sTK0tFFW12IwsrF1MDN2tbPXs3TQN7MQk5ACAOjZG1OaugBXAAAAAElFTkSuQmCC",blurWidth:6,blurHeight:8},text:"This is by far the most convenient way to access my benefits. Everything I need is right at my fingertips.",name:"Emily Garcia",title:"HR Specialist"},{src:{src:"/_next/static/media/landing_page_image_4.135a5874.png",height:1024,width:756,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAYAAAAICAIAAABVpBlvAAAAo0lEQVR42gGYAGf/AIl9cqKWioB2bZSGenZuZlpVUQCgmZKMh4KGWD++oI5yaWRtbGkArK+snpmWkl9G0rWooZSGbXZxAH+Af5mbmnpRRLWVh4qGe1JnQgCTfm+Wk456Y1evmYqprrBhbWAAnZWUoaKjsKWg1c/M5O/zwczSALzFy8nT2rnDy9vn7NXh583Y3gCoq6/CzdXDzdTI09nR3ePU3+Q7J1hGmDSqYQAAAABJRU5ErkJggg==",blurWidth:6,blurHeight:8},text:"I love how I can get all my benefits info through Slack. It saves me so much time!",name:"Troy Edward",title:"Associate Manager"}];var d=t=>{let{LeftComponent:e}=t,[n,d]=(0,c.useState)(0);(0,c.useEffect)(()=>{let t=setInterval(()=>{d(t=>(t+1)%u.length)},5e3);return()=>clearInterval(t)},[]);let g=u[n];return(0,o.jsxs)(i.ZP,{container:!0,style:{height:"95vh",width:"100%"},children:[(0,o.jsx)(i.ZP,{item:!0,xs:12,md:6,sx:{bgcolor:"#000000",color:"#ffffff",display:"flex",flexDirection:"column",justifyContent:"center",alignItems:"center",padding:"100px",height:"95vh",overflow:"auto"},children:e}),(0,o.jsx)(i.ZP,{item:!0,xs:12,md:6,sx:{display:"flex",justifyContent:"center",alignItems:"center",backgroundColor:"#f8f9fa",padding:"0",position:"relative",overflow:"hidden",height:"95vh"},children:(0,o.jsxs)(a.Z,{sx:{position:"relative",width:"100%",height:"100%",overflow:"hidden"},children:[(0,o.jsx)(l.default,{src:g.src,alt:"Profile",layout:"fill",objectFit:"cover",style:{objectPosition:"center"}}),(0,o.jsx)(a.Z,{sx:{position:"absolute",bottom:0,left:0,width:"100%",height:"100%",background:"linear-gradient(to top, rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0) 60%)"}}),(0,o.jsxs)(a.Z,{sx:{position:"absolute",bottom:"10%",left:"10%",color:"#ffffff",width:"550px"},children:[(0,o.jsx)(s.Z,{sx:{fontSize:70,marginBottom:"10px",opacity:.5,marginLeft:-2}}),(0,o.jsx)(r.Z,{variant:"h5",sx:{fontSize:"32px",fontWeight:"bold",lineHeight:"1.5",mb:2},children:g.text}),(0,o.jsx)(r.Z,{variant:"h5",sx:{fontSize:"24px",fontWeight:"bold",lineHeight:"1.5",mt:4},children:g.name}),(0,o.jsx)(r.Z,{variant:"body2",sx:{fontSize:"20px",fontWeight:"light"},children:g.title})]})]})})]})}},77534:function(t,e,n){"use strict";n.d(e,{Ig:function(){return l},N8:function(){return r},lY:function(){return i},selfOnboard:function(){return a}});var o=n(40256);async function i(t){return(await (0,o.j0)("/auth/parse-params",{link:t})).data}async function a(t){return(await (0,o.j0)("/user/self-onboard",{userEmail:t})).data.data}async function r(t,e){return(await (0,o.j0)("/teams/user/self-onboard",{userEmail:t,tenantId:e})).data}async function l(t,e){let n=await (0,o.j0)("/employee/onboard",{companyId:t,userId:e});return console.log("Response from onboardEmployee:",n),n.data}},28411:function(t,e,n){"use strict";n.d(e,{I:function(){return l}});var o=n(738),i=n(43301);let a=function(){{let t=window.location.hostname;if(t.includes("test.benosphere.com")||"localhost"===t)return{apiKey:"AIzaSyCPGurROOIHelcNrg3KK3UZpT5NY_v33cw",authDomain:"qharmony-test.firebaseapp.com",projectId:"qharmony-test",storageBucket:"qharmony-test.firebasestorage.app",messagingSenderId:"1017404738235",appId:"1:1017404738235:web:d0566182eb575065e3379e"}}return{apiKey:"AIzaSyAOalm98qF_u74s3qQlIqQ5A6IpWyd0wKA",authDomain:"qharmony-dev.firebaseapp.com",projectId:"qharmony-dev",storageBucket:"qharmony-dev.appspot.com",messagingSenderId:"756187162353",appId:"1:756187162353:web:3fc7d63dee1c57bc9d6b50"}}(),r=(0,o.C6)().length?(0,o.C6)()[0]:(0,o.ZF)(a),l=(0,i.v0)(r)}},function(t){t.O(0,[139,3463,3301,8685,187,1423,9414,7648,2971,2117,1744],function(){return t(t.s=71853)}),_N_E=t.O()}]);