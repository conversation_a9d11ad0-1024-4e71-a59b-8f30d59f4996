(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2626,7534],{71853:function(t,e,n){Promise.resolve().then(n.bind(n,38991))},40256:function(t,e,n){"use strict";n.d(e,{$R:function(){return u},A_:function(){return s},BO:function(){return o},GH:function(){return g},_n:function(){return i},be:function(){return r},iG:function(){return l},j0:function(){return c}});var a=n(83464);let i="http://localhost:8080",o="<EMAIL>,<EMAIL>,<EMAIL>".split(",").map(t=>t.trim()),r=a.Z.create({baseURL:i});async function s(t,e,n){let a=new URL(n?"".concat(n).concat(t):"".concat(i).concat(t));return e&&Object.keys(e).forEach(t=>a.searchParams.append(t,e[t])),(await r.get(a.toString())).data}async function c(t,e,n){let a=n?"".concat(n).concat(t):"".concat(i).concat(t),o=await r.post(a,e,{headers:{"Content-Type":"application/json"}});return{status:o.status,data:o.data}}async function l(t,e,n){let a=n?"".concat(n).concat(t):"".concat(i).concat(t);console.log("Document upload to: ".concat(a));let o=await r.post(a,e,{headers:{"Content-Type":"multipart/form-data"}});return{status:o.status,data:o.data}}async function u(t,e,n){let a=new URL(n?"".concat(n).concat(t):"".concat(i).concat(t));return e&&Object.keys(e).forEach(t=>a.searchParams.append(t,e[t])),console.log("GET Blob request to: ".concat(a.toString())),(await r.get(a.toString(),{responseType:"blob"})).data}async function g(t,e,n){let a=n?"".concat(n).concat(t):"".concat(i).concat(t),o=await r.put(a,e,{headers:{"Content-Type":"application/json"}});return{status:o.status,data:o.data}}r.interceptors.request.use(t=>{let e=localStorage.getItem("userid1")||localStorage.getItem("userId");return e?t.headers["user-id"]=e:console.warn("No user ID found in localStorage for API request"),t})},38991:function(t,e,n){"use strict";n.r(e);var a=n(57437),i=n(2265),o=n(43301),r=n(28411),s=n(99376),c=n(95656),l=n(35389),u=n(46387),g=n(77534),d=n(94509),h=n(27648);e.default=()=>{let t=(0,s.useRouter)(),[e,n]=(0,i.useState)(null),[A,m]=(0,i.useState)(null),[f,p]=(0,i.useState)(null),[b,y]=(0,i.useState)(!0),[w,x]=(0,i.useState)(null);return(0,i.useEffect)(()=>{(async()=>{let t=window.location.href,e=await (0,g.lY)(t),a=e.email,i=e.companyId,s=e.userId;if((0,o.JB)(r.I,t)){let t=window.localStorage.getItem("emailForSignIn1");t||e.email?(n(t||a),m(i),p(s)):x("Invalid sign-in link. Email not found.")}else x("Invalid sign-in link."),y(!1)})()},[]),(0,i.useEffect)(()=>{(async()=>{if(e)try{await (0,o.P6)(r.I,e,window.location.href),console.log("Sign-in successful"),window.localStorage.removeItem("emailForSignIn1"),localStorage.setItem("userid1",f),localStorage.setItem("companyId1",A),(0,g.Ig)(A,f),y(!1),t.push("/dashboard")}catch(t){console.error("Error signing in with email link:",t),x("Failed to sign in. Please try again."),y(!1)}})()},[e,A,f,t]),(0,a.jsx)(c.Z,{sx:{mt:5,textAlign:"center"},children:b?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(l.Z,{}),(0,a.jsx)(u.Z,{variant:"h6",sx:{mt:2},children:"Signing you in..."})]}):w?(0,a.jsx)(d.Z,{LeftComponent:(0,a.jsxs)(c.Z,{sx:{display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",color:"white",textAlign:"center",height:"100%"},children:[(0,a.jsx)(u.Z,{sx:{fontWeight:"bold",fontSize:"60px",mb:2},children:"❌ Magic Link Expired"}),(0,a.jsxs)(u.Z,{variant:"body1",sx:{fontSize:"20px",color:"#bbbbbb",mb:4},children:["You can sign in again by requesting a new link ",(0,a.jsx)(h.default,{href:"/",style:{color:"#B983FF",textDecoration:"underline"},children:"here"}),"."]})]})}):null})}},94509:function(t,e,n){"use strict";n.d(e,{Z:function(){return g}});var a=n(57437),i=n(89414),o=n(95656),r=n(46387),s=n(33145),c=n(53284),l=n(2265);let u=[{src:{src:"/_next/static/media/landing_page_image_1.883ba61a.png",height:1024,width:756,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAYAAAAICAIAAABVpBlvAAAAoUlEQVR42mOwNNRyMlRnYJRQUlNXNbISV9RgcHcwdrAwTPJ2ctVWFlTVkdPUZ7CxMw51MVvaklcb5cnAKqqoosGgbqrTmRe7e1ZbqZ+jnY6WgoEJg6CMUpify5UN07tivCU19O19fBiAoDjO/dzazqIoNwZ1CyklDYbGyTMO7N82rzwkkJchu6K6sr2D4eiV63suXl+3c09rU2Nt74TmKdMAG00wAXeqZ/wAAAAASUVORK5CYII=",blurWidth:6,blurHeight:8},text:"I get to keep track of all my benefits so I never miss out on the stuff that matters most.",name:"Troy Sivan",title:"Associate Manager"},{src:{src:"/_next/static/media/landing_page_image_2.59f16961.png",height:1024,width:756,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAYAAAAICAIAAABVpBlvAAAAo0lEQVR42mOYMakxwMdRRU/Lzs4sNtTTx9WGYeXS6TYOlik2GjGmKpbWJgWJgQwbN62M9rTbXJexsiTBWkXWydmWYcuurZO7G45NqluR6nfs+IkZq9cxHD1zatqS+Vsn1E4I0L3+4MH6nVsZZi9aFJWevXvn3i1rNtd2TQsMcmYAAgc3v8Ub989Zu6+kvgfEl5CWd/MJmrd8Y++0RYUVjQwMDACH00ArwNCIEAAAAABJRU5ErkJggg==",blurWidth:6,blurHeight:8},text:"Now I can track my benefits instantly without having to search through emails. It’s a game–changer!",name:"David Miller",title:"Software Engineer"},{src:{src:"/_next/static/media/landing_page_image_3.e9420572.png",height:1024,width:756,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAYAAAAICAIAAABVpBlvAAAAn0lEQVR42mNQE2YNsRJlYGAwlmL3UuUwFGNmcNbgXlNuNCkz0FtHTpydwUSSlUGBnaHYW29jc3pXoo8KA4ORNBeDOBODl7Ha3JyQQk8LLgYGYS4WBlN5PgYGhjgHw2wPM0FJGQYgsNeQZNbQj7HRzQlyU7WwU1VVZ3CyMrG2ssxxM9M2sTK0tFFW12IwsrF1MDN2tbPXs3TQN7MQk5ACAOjZG1OaugBXAAAAAElFTkSuQmCC",blurWidth:6,blurHeight:8},text:"This is by far the most convenient way to access my benefits. Everything I need is right at my fingertips.",name:"Emily Garcia",title:"HR Specialist"},{src:{src:"/_next/static/media/landing_page_image_4.135a5874.png",height:1024,width:756,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAYAAAAICAIAAABVpBlvAAAAo0lEQVR42gGYAGf/AIl9cqKWioB2bZSGenZuZlpVUQCgmZKMh4KGWD++oI5yaWRtbGkArK+snpmWkl9G0rWooZSGbXZxAH+Af5mbmnpRRLWVh4qGe1JnQgCTfm+Wk456Y1evmYqprrBhbWAAnZWUoaKjsKWg1c/M5O/zwczSALzFy8nT2rnDy9vn7NXh583Y3gCoq6/CzdXDzdTI09nR3ePU3+Q7J1hGmDSqYQAAAABJRU5ErkJggg==",blurWidth:6,blurHeight:8},text:"I love how I can get all my benefits info through Slack. It saves me so much time!",name:"Troy Edward",title:"Associate Manager"}];var g=t=>{let{LeftComponent:e}=t,[n,g]=(0,l.useState)(0);(0,l.useEffect)(()=>{let t=setInterval(()=>{g(t=>(t+1)%u.length)},5e3);return()=>clearInterval(t)},[]);let d=u[n];return(0,a.jsxs)(i.ZP,{container:!0,style:{height:"95vh",width:"100%"},children:[(0,a.jsx)(i.ZP,{item:!0,xs:12,md:6,sx:{bgcolor:"#000000",color:"#ffffff",display:"flex",flexDirection:"column",justifyContent:"center",alignItems:"center",padding:"100px",height:"95vh",overflow:"auto"},children:e}),(0,a.jsx)(i.ZP,{item:!0,xs:12,md:6,sx:{display:"flex",justifyContent:"center",alignItems:"center",backgroundColor:"#f8f9fa",padding:"0",position:"relative",overflow:"hidden",height:"95vh"},children:(0,a.jsxs)(o.Z,{sx:{position:"relative",width:"100%",height:"100%",overflow:"hidden"},children:[(0,a.jsx)(s.default,{src:d.src,alt:"Profile",layout:"fill",objectFit:"cover",style:{objectPosition:"center"}}),(0,a.jsx)(o.Z,{sx:{position:"absolute",bottom:0,left:0,width:"100%",height:"100%",background:"linear-gradient(to top, rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0) 60%)"}}),(0,a.jsxs)(o.Z,{sx:{position:"absolute",bottom:"10%",left:"10%",color:"#ffffff",width:"550px"},children:[(0,a.jsx)(c.Z,{sx:{fontSize:70,marginBottom:"10px",opacity:.5,marginLeft:-2}}),(0,a.jsx)(r.Z,{variant:"h5",sx:{fontSize:"32px",fontWeight:"bold",lineHeight:"1.5",mb:2},children:d.text}),(0,a.jsx)(r.Z,{variant:"h5",sx:{fontSize:"24px",fontWeight:"bold",lineHeight:"1.5",mt:4},children:d.name}),(0,a.jsx)(r.Z,{variant:"body2",sx:{fontSize:"20px",fontWeight:"light"},children:d.title})]})]})})]})}},77534:function(t,e,n){"use strict";n.d(e,{Ig:function(){return s},N8:function(){return r},lY:function(){return i},selfOnboard:function(){return o}});var a=n(40256);async function i(t){return(await (0,a.j0)("/auth/parse-params",{link:t})).data}async function o(t){return(await (0,a.j0)("/user/self-onboard",{userEmail:t})).data.data}async function r(t,e){return(await (0,a.j0)("/teams/user/self-onboard",{userEmail:t,tenantId:e})).data}async function s(t,e){let n=await (0,a.j0)("/employee/onboard",{companyId:t,userId:e});return console.log("Response from onboardEmployee:",n),n.data}},28411:function(t,e,n){"use strict";n.d(e,{I:function(){return s}});var a=n(738),i=n(43301);let o=function(){{let t=window.location.hostname;if(t.includes("test.benosphere.com")||"localhost"===t)return{apiKey:"AIzaSyCPGurROOIHelcNrg3KK3UZpT5NY_v33cw",authDomain:"qharmony-test.firebaseapp.com",projectId:"qharmony-test",storageBucket:"qharmony-test.firebasestorage.app",messagingSenderId:"1017404738235",appId:"1:1017404738235:web:d0566182eb575065e3379e"}}return{apiKey:"AIzaSyAOalm98qF_u74s3qQlIqQ5A6IpWyd0wKA",authDomain:"qharmony-dev.firebaseapp.com",projectId:"qharmony-dev",storageBucket:"qharmony-dev.appspot.com",messagingSenderId:"756187162353",appId:"1:756187162353:web:3fc7d63dee1c57bc9d6b50"}}(),r=(0,a.C6)().length?(0,a.C6)()[0]:(0,a.ZF)(o),s=(0,i.v0)(r)}},function(t){t.O(0,[139,3463,3301,293,9810,187,3145,9414,7648,1642,2971,2117,1744],function(){return t(t.s=71853)}),_N_E=t.O()}]);