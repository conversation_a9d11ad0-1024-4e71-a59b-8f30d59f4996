(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5300],{75338:function(e,t,n){Promise.resolve().then(n.bind(n,17221))},29821:function(e,t,n){"use strict";var a=n(94630),r=n(57437);t.Z=(0,a.Z)((0,r.jsx)("path",{d:"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2m-2 15-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8z"}),"CheckCircle")},8430:function(e,t,n){"use strict";var a=n(94630),r=n(57437);t.Z=(0,a.Z)((0,r.jsx)("path",{d:"M19 6.41 17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"}),"Close")},67116:function(e,t,n){"use strict";n.d(t,{Z:function(){return v}});var a=n(2265),r=n(61994),o=n(20801),i=n(16210),s=n(21086),l=n(37053),c=n(94630),d=n(57437),u=(0,c.Z)((0,d.jsx)("path",{d:"M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"}),"Person"),m=n(94143),p=n(50738);function f(e){return(0,p.ZP)("MuiAvatar",e)}(0,m.Z)("MuiAvatar",["root","colorDefault","circular","rounded","square","img","fallback"]);var h=n(79114);let x=e=>{let{classes:t,variant:n,colorDefault:a}=e;return(0,o.Z)({root:["root",n,a&&"colorDefault"],img:["img"],fallback:["fallback"]},f,t)},g=(0,i.default)("div",{name:"MuiAvatar",slot:"Root",overridesResolver:(e,t)=>{let{ownerState:n}=e;return[t.root,t[n.variant],n.colorDefault&&t.colorDefault]}})((0,s.Z)(e=>{let{theme:t}=e;return{position:"relative",display:"flex",alignItems:"center",justifyContent:"center",flexShrink:0,width:40,height:40,fontFamily:t.typography.fontFamily,fontSize:t.typography.pxToRem(20),lineHeight:1,borderRadius:"50%",overflow:"hidden",userSelect:"none",variants:[{props:{variant:"rounded"},style:{borderRadius:(t.vars||t).shape.borderRadius}},{props:{variant:"square"},style:{borderRadius:0}},{props:{colorDefault:!0},style:{color:(t.vars||t).palette.background.default,...t.vars?{backgroundColor:t.vars.palette.Avatar.defaultBg}:{backgroundColor:t.palette.grey[400],...t.applyStyles("dark",{backgroundColor:t.palette.grey[600]})}}}]}})),y=(0,i.default)("img",{name:"MuiAvatar",slot:"Img",overridesResolver:(e,t)=>t.img})({width:"100%",height:"100%",textAlign:"center",objectFit:"cover",color:"transparent",textIndent:1e4}),b=(0,i.default)(u,{name:"MuiAvatar",slot:"Fallback",overridesResolver:(e,t)=>t.fallback})({width:"75%",height:"75%"});var v=a.forwardRef(function(e,t){let n=(0,l.i)({props:e,name:"MuiAvatar"}),{alt:o,children:i,className:s,component:c="div",slots:u={},slotProps:m={},imgProps:p,sizes:f,src:v,srcSet:j,variant:Z="circular",...C}=n,A=null,k={...n,component:c,variant:Z},w=function(e){let{crossOrigin:t,referrerPolicy:n,src:r,srcSet:o}=e,[i,s]=a.useState(!1);return a.useEffect(()=>{if(!r&&!o)return;s(!1);let e=!0,a=new Image;return a.onload=()=>{e&&s("loaded")},a.onerror=()=>{e&&s("error")},a.crossOrigin=t,a.referrerPolicy=n,a.src=r,o&&(a.srcset=o),()=>{e=!1}},[t,n,r,o]),i}({...p,..."function"==typeof m.img?m.img(k):m.img,src:v,srcSet:j}),P=v||j,S=P&&"error"!==w;k.colorDefault=!S,delete k.ownerState;let I=x(k),[R,N]=(0,h.Z)("img",{className:I.img,elementType:y,externalForwardedProps:{slots:u,slotProps:{img:{...p,...m.img}}},additionalProps:{alt:o,src:v,srcSet:j,sizes:f},ownerState:k});return A=S?(0,d.jsx)(R,{...N}):i||0===i?i:P&&o?o[0]:(0,d.jsx)(b,{ownerState:k,className:I.fallback}),(0,d.jsx)(g,{as:c,className:(0,r.Z)(I.root,s),ref:t,...C,ownerState:k,children:A})})},40256:function(e,t,n){"use strict";n.d(t,{$R:function(){return d},A_:function(){return s},BO:function(){return o},GH:function(){return u},_n:function(){return r},be:function(){return i},iG:function(){return c},j0:function(){return l}});var a=n(83464);let r="http://localhost:8080",o="<EMAIL>,<EMAIL>,<EMAIL>".split(",").map(e=>e.trim()),i=a.Z.create({baseURL:r});async function s(e,t,n){let a=new URL(n?"".concat(n).concat(e):"".concat(r).concat(e));return t&&Object.keys(t).forEach(e=>a.searchParams.append(e,t[e])),(await i.get(a.toString())).data}async function l(e,t,n){let a=n?"".concat(n).concat(e):"".concat(r).concat(e),o=await i.post(a,t,{headers:{"Content-Type":"application/json"}});return{status:o.status,data:o.data}}async function c(e,t,n){let a=n?"".concat(n).concat(e):"".concat(r).concat(e);console.log("Document upload to: ".concat(a));let o=await i.post(a,t,{headers:{"Content-Type":"multipart/form-data"}});return{status:o.status,data:o.data}}async function d(e,t,n){let a=new URL(n?"".concat(n).concat(e):"".concat(r).concat(e));return t&&Object.keys(t).forEach(e=>a.searchParams.append(e,t[e])),console.log("GET Blob request to: ".concat(a.toString())),(await i.get(a.toString(),{responseType:"blob"})).data}async function u(e,t,n){let a=n?"".concat(n).concat(e):"".concat(r).concat(e),o=await i.put(a,t,{headers:{"Content-Type":"application/json"}});return{status:o.status,data:o.data}}i.interceptors.request.use(e=>{let t=localStorage.getItem("userid1")||localStorage.getItem("userId");return t?e.headers["user-id"]=t:console.warn("No user ID found in localStorage for API request"),e})},17221:function(e,t,n){"use strict";n.r(t),n.d(t,{default:function(){return R}});var a=n(57437),r=n(2265),o=n(95656),i=n(89414),s=n(46387),l=n(94013),c=n(35389),d=n(67116),u=n(8350),m=n(13571),p=n(39547),f=n(68575),h=n(48223),x=n(83337),g=n(53392),y=n(79507),b=n(59832),v=n(77468),j=n(97404),Z=n(9026),C=n(8430),A=n(29821),k=n(99376),w=e=>{let{open:t,onClose:n}=e,o=(0,x.T)();(0,k.useRouter)();let s=(0,x.C)(e=>e.user._id),[d,u]=(0,r.useState)({companyName:"",companyAdminEmail:"",companyAdminName:""}),[m,f]=(0,r.useState)(!1),[h,w]=(0,r.useState)("");(0,r.useEffect)(()=>{(0,p.qB)(o,s)},[o,s]);let P=(e,t)=>{u({...d,[e]:t})},S=async()=>{if(f(!0),!d.companyName||!d.companyAdminEmail||!d.companyAdminName){alert("Please fill out all required fields."),f(!1);return}let e=await (0,p.G9)(s,d.companyName,d.companyAdminEmail,d.companyAdminName);f(!1),e&&200===e.status&&(w("Invite has been sent to the admin! They will show up in your team members list once they accept the invite."),u({companyName:"",companyAdminEmail:"",companyAdminName:""}))};return(0,a.jsxs)(g.Z,{open:t,onClose:n,PaperProps:{style:{borderRadius:"16px",boxShadow:"0px 10px 30px rgba(0, 0, 0, 0.1)",padding:"5px",width:"550px"}},children:[(0,a.jsxs)(y.Z,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",fontWeight:"bold",fontSize:"1.5rem"},children:["Add Company and Admin",(0,a.jsx)(b.Z,{onClick:n,children:(0,a.jsx)(C.Z,{})})]}),(0,a.jsxs)(v.Z,{children:[(0,a.jsxs)(i.ZP,{container:!0,spacing:3,sx:{marginBottom:"16px",marginTop:"0px"},children:[(0,a.jsx)(i.ZP,{item:!0,xs:12,children:(0,a.jsx)(j.Z,{fullWidth:!0,required:!0,label:"Company Name",variant:"outlined",value:d.companyName,onChange:e=>P("companyName",e.target.value),InputProps:{style:{borderRadius:"8px",backgroundColor:"#f9f9f9",height:"48px"}},InputLabelProps:{shrink:!0,style:{fontSize:"1rem"}},sx:{alignItems:"flex-start"}})}),(0,a.jsx)(i.ZP,{item:!0,xs:12,children:(0,a.jsx)(j.Z,{fullWidth:!0,required:!0,label:"Admin Email",variant:"outlined",value:d.companyAdminEmail,onChange:e=>P("companyAdminEmail",e.target.value),InputProps:{style:{borderRadius:"8px",backgroundColor:"#f9f9f9",height:"48px"}},InputLabelProps:{shrink:!0,style:{fontSize:"1rem"}},sx:{alignItems:"flex-start"}})}),(0,a.jsx)(i.ZP,{item:!0,xs:12,children:(0,a.jsx)(j.Z,{fullWidth:!0,required:!0,label:"Admin Name",variant:"outlined",value:d.companyAdminName,onChange:e=>P("companyAdminName",e.target.value),InputProps:{style:{borderRadius:"8px",backgroundColor:"#f9f9f9",height:"48px"}},InputLabelProps:{shrink:!0,style:{fontSize:"1rem"}},sx:{alignItems:"flex-start"}})})]}),m&&(0,a.jsx)(c.Z,{}),h&&(0,a.jsxs)("div",{style:{color:"green",marginTop:"10px",display:"flex",alignItems:"start"},children:[(0,a.jsx)(A.Z,{style:{marginRight:"10px"}}),h]})]}),(0,a.jsx)(Z.Z,{sx:{padding:"16px"},children:h?(0,a.jsx)(l.Z,{onClick:()=>{w(""),u({companyName:"",companyAdminEmail:"",companyAdminName:""})},sx:{color:"#ffffff",backgroundColor:"#000000",borderRadius:"12px",padding:"8px 24px",textTransform:"none",fontWeight:"bold","&:hover":{backgroundColor:"#333333"}},children:"Add Another Company"}):(0,a.jsx)(l.Z,{onClick:S,sx:{color:"#ffffff",backgroundColor:"#000000",borderRadius:"12px",padding:"8px 24px",textTransform:"none",fontWeight:"bold","&:hover":{backgroundColor:"#333333"}},disabled:m,children:m?"Adding...":"Confirm Details"})})]})};let P=["#FFB6C1","#FF6347","#FFD700","#90EE90","#00CED1","#1E90FF","#BA55D3"],S=e=>{let[t,n]=e.split(" ");return"".concat(t[0]).concat(n?n[0]:"")},I=e=>P[e%P.length];var R=(0,m.Z)(()=>{let e=(0,f.I0)(),t=(0,x.C)(e=>e.user._id),n=(0,f.v9)(e=>e.user.managedCompanies),[m,g]=(0,r.useState)(!1),[y,b]=(0,r.useState)(!0);return(0,r.useEffect)(()=>{(async()=>{b(!0),await (0,p.qB)(e,t),b(!1)})()},[e,t]),(0,a.jsx)(h.Z,{children:(0,a.jsxs)(o.Z,{sx:{bgcolor:"#F5F6FA",p:4,width:"100%",height:"100vh",overflow:"auto"},children:[(0,a.jsxs)(i.ZP,{container:!0,alignItems:"center",justifyContent:"space-between",sx:{mb:2},children:[(0,a.jsx)(i.ZP,{item:!0,children:(0,a.jsx)(s.Z,{variant:"h5",children:"Team Members"})}),(0,a.jsx)(i.ZP,{item:!0,children:(0,a.jsx)(l.Z,{variant:"contained",color:"primary",onClick:()=>{g(!0)},sx:{backgroundColor:"#000000",textTransform:"none",borderRadius:"6px"},children:"Add new company"})})]}),(0,a.jsx)(o.Z,{sx:{bgcolor:"#ffffff",borderRadius:"12px",width:"100%",p:2,boxShadow:"0px 4px 12px rgba(0, 0, 0, 0.1)"},children:y?(0,a.jsx)(o.Z,{sx:{display:"flex",justifyContent:"center",my:4},children:(0,a.jsx)(c.Z,{})}):0===n.length?(0,a.jsx)(s.Z,{variant:"body1",sx:{textAlign:"center",my:4},children:"Add your first company to get started"}):n.map((e,t)=>(0,a.jsxs)(o.Z,{children:[(0,a.jsxs)(i.ZP,{container:!0,alignItems:"center",spacing:2,children:[(0,a.jsxs)(i.ZP,{item:!0,xs:3,container:!0,alignItems:"center",children:[(0,a.jsx)(d.Z,{sx:{bgcolor:I(t),color:"#ffffff",width:48,height:48,fontSize:"1.2rem",mr:2},children:S(e.name)}),(0,a.jsx)(s.Z,{variant:"body1",sx:{fontWeight:"bold"},children:e.name})]}),(0,a.jsx)(i.ZP,{item:!0,xs:3,children:(0,a.jsx)(s.Z,{variant:"body2",children:e.adminEmail})})]}),t<n.length-1&&(0,a.jsx)(u.Z,{sx:{my:2}})]},t))}),(0,a.jsx)(w,{open:m,onClose:()=>g(!1)})]})})})}},function(e){e.O(0,[139,3463,3301,8575,8685,187,1423,9932,3919,9129,2786,9826,8166,8760,9414,7404,3209,3344,9662,1356,2971,2117,1744],function(){return e(e.s=75338)}),_N_E=e.O()}]);