(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4469],{82813:function(e,t,r){Promise.resolve().then(r.bind(r,51780))},67116:function(e,t,r){"use strict";r.d(t,{Z:function(){return y}});var o=r(2265),a=r(61994),n=r(20801),s=r(16210),l=r(21086),i=r(37053),c=r(94630),u=r(57437),d=(0,c.Z)((0,u.jsx)("path",{d:"M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"}),"Person"),p=r(94143),f=r(50738);function x(e){return(0,f.ZP)("MuiAvatar",e)}(0,p.Z)("MuiAvatar",["root","colorDefault","circular","rounded","square","img","fallback"]);var g=r(79114);let h=e=>{let{classes:t,variant:r,colorDefault:o}=e;return(0,n.Z)({root:["root",r,o&&"colorDefault"],img:["img"],fallback:["fallback"]},x,t)},b=(0,s.default)("div",{name:"MuiAvatar",slot:"Root",overridesResolver:(e,t)=>{let{ownerState:r}=e;return[t.root,t[r.variant],r.colorDefault&&t.colorDefault]}})((0,l.Z)(e=>{let{theme:t}=e;return{position:"relative",display:"flex",alignItems:"center",justifyContent:"center",flexShrink:0,width:40,height:40,fontFamily:t.typography.fontFamily,fontSize:t.typography.pxToRem(20),lineHeight:1,borderRadius:"50%",overflow:"hidden",userSelect:"none",variants:[{props:{variant:"rounded"},style:{borderRadius:(t.vars||t).shape.borderRadius}},{props:{variant:"square"},style:{borderRadius:0}},{props:{colorDefault:!0},style:{color:(t.vars||t).palette.background.default,...t.vars?{backgroundColor:t.vars.palette.Avatar.defaultBg}:{backgroundColor:t.palette.grey[400],...t.applyStyles("dark",{backgroundColor:t.palette.grey[600]})}}}]}})),m=(0,s.default)("img",{name:"MuiAvatar",slot:"Img",overridesResolver:(e,t)=>t.img})({width:"100%",height:"100%",textAlign:"center",objectFit:"cover",color:"transparent",textIndent:1e4}),v=(0,s.default)(d,{name:"MuiAvatar",slot:"Fallback",overridesResolver:(e,t)=>t.fallback})({width:"75%",height:"75%"});var y=o.forwardRef(function(e,t){let r=(0,i.i)({props:e,name:"MuiAvatar"}),{alt:n,children:s,className:l,component:c="div",slots:d={},slotProps:p={},imgProps:f,sizes:x,src:y,srcSet:j,variant:Z="circular",...w}=r,k=null,R={...r,component:c,variant:Z},S=function(e){let{crossOrigin:t,referrerPolicy:r,src:a,srcSet:n}=e,[s,l]=o.useState(!1);return o.useEffect(()=>{if(!a&&!n)return;l(!1);let e=!0,o=new Image;return o.onload=()=>{e&&l("loaded")},o.onerror=()=>{e&&l("error")},o.crossOrigin=t,o.referrerPolicy=r,o.src=a,n&&(o.srcset=n),()=>{e=!1}},[t,r,a,n]),s}({...f,..."function"==typeof p.img?p.img(R):p.img,src:y,srcSet:j}),C=y||j,E=C&&"error"!==S;R.colorDefault=!E,delete R.ownerState;let P=h(R),[A,F]=(0,g.Z)("img",{className:P.img,elementType:m,externalForwardedProps:{slots:d,slotProps:{img:{...f,...p.img}}},additionalProps:{alt:n,src:y,srcSet:j,sizes:x},ownerState:R});return k=E?(0,u.jsx)(A,{...F}):s||0===s?s:C&&n?n[0]:(0,u.jsx)(v,{ownerState:R,className:P.fallback}),(0,u.jsx)(b,{as:c,className:(0,a.Z)(P.root,l),ref:t,...w,ownerState:R,children:k})})},40256:function(e,t,r){"use strict";r.d(t,{$R:function(){return u},A_:function(){return l},BO:function(){return n},GH:function(){return d},_n:function(){return a},be:function(){return s},iG:function(){return c},j0:function(){return i}});var o=r(83464);let a="http://localhost:8080",n="<EMAIL>,<EMAIL>,<EMAIL>".split(",").map(e=>e.trim()),s=o.Z.create({baseURL:a});async function l(e,t,r){let o=new URL(r?"".concat(r).concat(e):"".concat(a).concat(e));return t&&Object.keys(t).forEach(e=>o.searchParams.append(e,t[e])),(await s.get(o.toString())).data}async function i(e,t,r){let o=r?"".concat(r).concat(e):"".concat(a).concat(e),n=await s.post(o,t,{headers:{"Content-Type":"application/json"}});return{status:n.status,data:n.data}}async function c(e,t,r){let o=r?"".concat(r).concat(e):"".concat(a).concat(e);console.log("Document upload to: ".concat(o));let n=await s.post(o,t,{headers:{"Content-Type":"multipart/form-data"}});return{status:n.status,data:n.data}}async function u(e,t,r){let o=new URL(r?"".concat(r).concat(e):"".concat(a).concat(e));return t&&Object.keys(t).forEach(e=>o.searchParams.append(e,t[e])),console.log("GET Blob request to: ".concat(o.toString())),(await s.get(o.toString(),{responseType:"blob"})).data}async function d(e,t,r){let o=r?"".concat(r).concat(e):"".concat(a).concat(e),n=await s.put(o,t,{headers:{"Content-Type":"application/json"}});return{status:n.status,data:n.data}}s.interceptors.request.use(e=>{let t=localStorage.getItem("userid1")||localStorage.getItem("userId");return t?e.headers["user-id"]=t:console.warn("No user ID found in localStorage for API request"),e})},51780:function(e,t,r){"use strict";r.r(t),r.d(t,{default:function(){return y}});var o=r(57437),a=r(2265),n=r(95656),s=r(46387),l=r(94013),i=r(53410),c=r(89414),u=r(83096),d=r(97404),p=r(13571),f=r(48223),x=r(99376),g=r(68575),h=r(70623),b=r(40256);async function m(e,t){let r=await (0,b.j0)("/group",{companyId:e,name:t});if(201===r.status)return r.data.groupId;console.error("Error Creating Group")}async function v(e){try{return(await (0,b.A_)("/groups/".concat(e))).groups}catch(e){console.error("Error getting Groups");return}}var y=(0,p.Z)(()=>{let e=(0,g.v9)(e=>(0,h.MP)(e)),[t,r]=(0,a.useState)(!1),[p,b]=(0,a.useState)([]),[y,j]=(0,a.useState)(""),Z=(0,x.useRouter)();(0,a.useEffect)(()=>{(async()=>{let t=await v(e);console.log("data >>",t),b(t)})()},[]);let w=()=>{r(!1),j("")},k=async()=>{let t=await m(e,y);Z.push("/group/".concat(t)),w()};return(0,o.jsxs)(f.Z,{children:[(0,o.jsxs)(n.Z,{sx:{bgcolor:"#F5F6FA",px:4,py:2,width:"100%",height:"95vh",overflow:"auto"},children:[(0,o.jsxs)(n.Z,{sx:{display:"flex",gap:"32px",alignItems:"center",mb:4,mt:5},children:[(0,o.jsx)(s.Z,{sx:{fontWeight:600,fontSize:"34px",color:"black",lineHeight:"41px",textAlign:"left"},children:"Groups"}),(0,o.jsx)(l.Z,{variant:"contained",onClick:()=>r(!0),sx:{textTransform:"none",borderRadius:"6px",bgcolor:"white",color:"black",boxShadow:"none",width:"140px",paddingY:"10px",paddingX:"16px",border:"1px solid #D2D2D2","&:hover":{backgroundColor:"rgba(0, 0, 0, 0.1)",boxShadow:"none"}},children:"+ Create Group"})]}),(0,o.jsxs)(i.Z,{sx:{bgcolor:"#ffffff",borderRadius:"12px",marginBottom:9,boxShadow:"none"},children:[(0,o.jsxs)(c.ZP,{container:!0,sx:{borderBottom:"1px solid #E0E0E0",background:"#F0F0F0",borderTopLeftRadius:"12px",borderTopRightRadius:"12px"},children:[(0,o.jsx)(c.ZP,{item:!0,xs:2,children:(0,o.jsx)(s.Z,{variant:"body2",sx:{fontWeight:600,color:"#B0B0B0",py:1,px:3},children:"S NO"})}),(0,o.jsx)(c.ZP,{item:!0,xs:10,children:(0,o.jsx)(s.Z,{variant:"body2",sx:{fontWeight:600,color:"#B0B0B0",py:1},children:"GROUP NAME"})})]}),p&&p.length>0?null==p?void 0:p.map((e,t)=>(0,o.jsx)(n.Z,{sx:{transition:"background-color 0.3s ease","&:hover":{bgcolor:"#f0f0f0",cursor:"pointer"},borderBottom:t===p.length-1?"none":"1px solid #E0E0E0"},onClick:()=>Z.push("/group/".concat(e._id)),children:(0,o.jsxs)(c.ZP,{container:!0,alignItems:"center",sx:{py:1,borderRadius:"8px"},children:[(0,o.jsx)(c.ZP,{item:!0,xs:2,sx:{py:1,px:3},children:(0,o.jsx)(s.Z,{sx:{fontWeight:"500",fontSize:"17px",color:"black"},children:t+1})}),(0,o.jsx)(c.ZP,{item:!0,xs:10,children:(0,o.jsx)(s.Z,{sx:{fontWeight:"500",fontSize:"17px",color:"black",textAlign:"left"},children:e.name})})]})},t)):(0,o.jsx)(n.Z,{sx:{display:"flex",justifyContent:"center",minWidth:"100%",py:2},children:(0,o.jsx)(s.Z,{children:"No Groups Found"})})]})]}),(0,o.jsx)(u.Z,{open:t,onClose:w,"aria-labelledby":"modal-title","aria-describedby":"modal-description",children:(0,o.jsxs)(n.Z,{sx:{position:"absolute",top:"50%",left:"50%",transform:"translate(-50%, -50%)",bgcolor:"#F5F6FA",boxShadow:24,p:4,borderRadius:"12px",width:400},children:[(0,o.jsx)(s.Z,{id:"modal-title",variant:"h6",sx:{mb:2,color:"black"},children:"Create New Group"}),(0,o.jsx)(d.Z,{fullWidth:!0,label:"Enter Group Name",variant:"outlined",value:y,onChange:e=>j(e.target.value),sx:{mb:3,bgcolor:"white",borderRadius:"8px","& .MuiOutlinedInput-root":{"& fieldset":{borderColor:"rgba(0, 0, 0, 0.2)"},"&:hover fieldset":{borderColor:"rgba(0, 0, 0, 0.4)"}}}}),(0,o.jsxs)(n.Z,{sx:{display:"flex",justifyContent:"flex-end",gap:2},children:[(0,o.jsx)(l.Z,{onClick:w,sx:{textTransform:"none",color:"black",bgcolor:"rgba(0, 0, 0, 0.06)",borderRadius:"8px","&:hover":{bgcolor:"rgba(0, 0, 0, 0.1)"}},children:"Cancel"}),(0,o.jsx)(l.Z,{variant:"contained",sx:{textTransform:"none",bgcolor:"rgba(0, 0, 0, 0.06)",color:"black",borderRadius:"8px","&:hover":{bgcolor:"rgba(0, 0, 0, 0.1)"}},onClick:k,children:"Save"})]})]})})]})})}},function(e){e.O(0,[139,3463,3301,8575,293,9810,187,3145,9932,3919,9129,2786,9826,8166,8760,9414,7404,3344,9662,1356,2971,2117,1744],function(){return e(e.s=82813)}),_N_E=e.O()}]);