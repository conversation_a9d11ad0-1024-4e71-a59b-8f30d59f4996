(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6666],{38980:function(e,t,n){Promise.resolve().then(n.bind(n,41381))},40256:function(e,t,n){"use strict";n.d(t,{$R:function(){return d},A_:function(){return i},BO:function(){return a},GH:function(){return u},_n:function(){return r},be:function(){return c},iG:function(){return l},j0:function(){return s}});var o=n(83464);let r="http://localhost:8080",a="<EMAIL>,<EMAIL>,<EMAIL>".split(",").map(e=>e.trim()),c=o.Z.create({baseURL:r});async function i(e,t,n){let o=new URL(n?"".concat(n).concat(e):"".concat(r).concat(e));return t&&Object.keys(t).forEach(e=>o.searchParams.append(e,t[e])),(await c.get(o.toString())).data}async function s(e,t,n){let o=n?"".concat(n).concat(e):"".concat(r).concat(e),a=await c.post(o,t,{headers:{"Content-Type":"application/json"}});return{status:a.status,data:a.data}}async function l(e,t,n){let o=n?"".concat(n).concat(e):"".concat(r).concat(e);console.log("Document upload to: ".concat(o));let a=await c.post(o,t,{headers:{"Content-Type":"multipart/form-data"}});return{status:a.status,data:a.data}}async function d(e,t,n){let o=new URL(n?"".concat(n).concat(e):"".concat(r).concat(e));return t&&Object.keys(t).forEach(e=>o.searchParams.append(e,t[e])),console.log("GET Blob request to: ".concat(o.toString())),(await c.get(o.toString(),{responseType:"blob"})).data}async function u(e,t,n){let o=n?"".concat(n).concat(e):"".concat(r).concat(e),a=await c.put(o,t,{headers:{"Content-Type":"application/json"}});return{status:a.status,data:a.data}}c.interceptors.request.use(e=>{let t=localStorage.getItem("userid1")||localStorage.getItem("userId");return t?e.headers["user-id"]=t:console.warn("No user ID found in localStorage for API request"),e})},41381:function(e,t,n){"use strict";n.r(t);var o=n(57437),r=n(2265),a=n(83337),c=n(39547),i=n(67571),s=n(95656),l=n(46387),d=n(89414),u=n(94013),f=n(85860),p=n(14865),b=n(35389),g=n(8350),m=n(99376),h=n(7022),y=n(68575),x=n(70623),E=n(13571),j=n(48223);t.default=(0,E.Z)(()=>{let e=(0,a.T)(),t=(0,m.useRouter)(),n=(0,y.v9)(e=>(0,x.MP)(e)),E=(0,a.C)(e=>e.benefits.benefitsPerType),[w,k]=(0,r.useState)({});(0,r.useEffect)(()=>{n&&""!==n&&(0,c.aE)(e,n)},[e,n]);let I=async(t,o,r)=>{k(e=>({...e,[t]:!0}));try{await (0,h.ov)(e,n,t,!r,o)?(0,c.aE)(e,n):console.error("Failed to update benefit activation")}catch(e){console.error("Failed to update benefit activation:",e)}finally{k(e=>({...e,[t]:!1}))}},M=e=>{t.push("/editBenefit/".concat(e))},Z=e=>e?(0,o.jsx)(i.Z,{label:"Available",sx:{bgcolor:"#67BA6B1F",color:"#67BA6B",borderRadius:"8px","& .MuiChip-label":{padding:1,fontWeight:"semibold",fontSize:"14px"}}}):(0,o.jsx)(i.Z,{label:"Disabled",sx:{bgcolor:"#f0f0f0",color:"black",borderRadius:"8px","& .MuiChip-label":{padding:1,fontWeight:"semibold",fontSize:"14px"}}});return(0,o.jsx)(j.Z,{children:(0,o.jsx)(s.Z,{sx:{bgcolor:"#F5F6FA",p:4,width:"100%",height:"95vh",overflow:"auto"},children:E.map(e=>(0,o.jsxs)(s.Z,{sx:{mb:4},children:[(0,o.jsx)(l.Z,{sx:{fontWeight:"500",fontSize:"28px",lineHeight:"20.8px",color:"black",textAlign:"left",marginBottom:4,marginTop:5},children:(0,c.Ur)(e.benefitType)}),(0,o.jsxs)(d.ZP,{container:!0,sx:{mb:2,px:2},children:[(0,o.jsx)(d.ZP,{item:!0,xs:4,children:(0,o.jsx)(l.Z,{variant:"body2",sx:{fontWeight:500,color:"#939496"},children:"TYPE"})}),(0,o.jsx)(d.ZP,{item:!0,xs:4,children:(0,o.jsx)(l.Z,{variant:"body2",sx:{fontWeight:500,color:"#939496"},children:"STATUS"})}),(0,o.jsx)(d.ZP,{item:!0,xs:4,sx:{display:"flex",justifyContent:"flex-end",pr:14},children:(0,o.jsx)(l.Z,{variant:"body2",sx:{fontWeight:500,color:"#939496"},children:"ACTION"})})]}),(0,o.jsx)(s.Z,{sx:{bgcolor:"#ffffff",py:1,px:3,borderRadius:"12px",marginBottom:9},children:e.benefits.map((t,n)=>(0,o.jsxs)("div",{children:[(0,o.jsxs)(d.ZP,{container:!0,alignItems:"center",sx:{py:1},children:[(0,o.jsx)(d.ZP,{item:!0,xs:4,children:(0,o.jsx)(l.Z,{sx:{fontWeight:"500",fontSize:"17px",lineHeight:"20.8px",color:"black",textAlign:"left"},children:(0,c.dA)(t.subType)})}),(0,o.jsx)(d.ZP,{item:!0,xs:4,children:Z(t.isActivated)}),(0,o.jsxs)(d.ZP,{item:!0,xs:4,sx:{display:"flex",justifyContent:"flex-end",alignItems:"center",gap:2},children:[(0,o.jsx)(u.Z,{variant:"contained",onClick:()=>M(t._id),sx:{backgroundColor:"#f0f0f0",borderRadius:"8px",textTransform:"none",color:"#000",padding:"4px",marginRight:3,boxShadow:"none",border:"none","&:hover":{backgroundColor:"#E0E0E0",boxShadow:"none"}},children:"Edit"}),(0,o.jsx)(f.Z,{control:(0,o.jsx)(p.Z,{checked:t.isActivated,onChange:()=>I(t._id,e.benefitType,t.isActivated),disabled:w[t._id],sx:{"& .MuiSwitch-switchBase.Mui-checked":{color:"#11C12D"},"& .MuiSwitch-switchBase.Mui-checked + .MuiSwitch-track":{backgroundColor:"#11C12D"}}}),label:w[t._id]?(0,o.jsx)(b.Z,{size:20}):""})]})]},t._id),n<e.benefits.length-1&&(0,o.jsx)(g.Z,{sx:{bgcolor:"black",my:1,opacity:.05}})]},t._id))})]},e.benefitType))})})})},7022:function(e,t,n){"use strict";n.d(t,{$t:function(){return d},SS:function(){return f},Y0:function(){return c},cd:function(){return u},fH:function(){return p},mH:function(){return b},ov:function(){return l},v0:function(){return i}});var o=n(40256),r=n(39124),a=n(39547);async function c(e,t,n){try{let a=await (0,o.A_)("/benefits/benefit-by-type",{companyId:t,type:n});a&&a.benefits?(console.log("GET BENEFITS FOR TYPE RESPONSE: ",a.benefits),e((0,r.oQ)({benefitType:n,benefits:a.benefits})),e((0,r.nM)("Benefits fetched successfully"))):(console.error("Invalid response format:",a),e((0,r.nM)("Failed to fetch benefits")))}catch(t){console.error("Error fetching benefits:",t),e((0,r.nM)("Error fetching benefits"))}}async function i(e,t,n,a){let c={benefitId:t,page:a};console.log("data",c);let i=await (0,o.A_)("/benefits/one-benefit",c),l={...i,benefitId:t};for(let t of(e((0,r.F5)(l)),i.documents)){let o=decodeURIComponent(t.split("_____")[1]);s(e,t,n,o)}}async function s(e,t,n,a){let c={objectKey:t,companyId:n};console.log("data",c);let i=await (0,o.$R)("/benefits/document",c);if(console.log("VIEW BENEFIT RESPONSE: ",i),i){let n=new Blob([i],{type:"application/pdf"}),o=URL.createObjectURL(n);e((0,r.D7)([{documentObjectKey:t,document:o,originalFileName:a}]))}}let l=async(e,t,n,r,i)=>200===(await (0,o.j0)("/benefits/toggle-benefits/",{benefitId:n,companyId:t,isActivated:r})).status&&(await c(e,t,i),await (0,a.N)(e,t),!0);async function d(e,t,n,a){let c=new FormData;a.forEach(e=>c.append("documents",e)),c.append("companyId",n),c.append("benefitId",t);try{console.log("uploadDocument",c);let i=await (0,o.iG)("/benefits/add/document",c),l=i.data.objectKeys;if(console.log("newObjectKeys",l),200===i.status)return l.forEach((o,c)=>{let i=a[c].name;e((0,r.H_)({benefitId:t,document:o})),s(e,o,n,i)}),e((0,r.nM)("Document added successfully")),!0;return console.error("Error adding document:",i.data.error),e((0,r.nM)("Failed to add document")),!1}catch(t){return console.error("Error adding document:",t),e((0,r.nM)("Error adding document")),!1}}async function u(e,t,n,a){try{let c=await (0,o.j0)("/benefits/delete/document",{benefitId:t,companyId:n,objectKey:a});if(200===c.status)return e((0,r.iH)({benefitId:t,document:a})),e((0,r.nM)("Document deleted successfully")),!0;return console.error("Error deleting document:",c.data.error),e((0,r.nM)("Failed to delete document")),!1}catch(t){return console.error("Error deleting document:",t),e((0,r.nM)("Error deleting document")),!1}}async function f(e,t,n,a){try{let c=await (0,o.j0)("/benefits/add/links",{benefitId:t,companyId:n,urls:[a]});if(200===c.status)return e((0,r.MJ)({benefitId:t,link:a})),e((0,r.nM)("Link added successfully")),!0;return console.error("Error adding link:",c.data.error),e((0,r.nM)("Failed to add link")),!1}catch(t){return console.error("Error adding link:",t),e((0,r.nM)("Error adding link")),!1}}async function p(e,t,n,a){try{let c=await (0,o.j0)("/benefits/delete/link",{benefitId:t,companyId:n,urls:a});if(200===c.status)return e((0,r.Yw)({benefitId:t,link:a})),e((0,r.nM)("Link deleted successfully")),!0;return console.error("Error deleting link:",c.data.error),e((0,r.nM)("Failed to delete link")),!1}catch(t){return console.error("Error deleting link:",t),e((0,r.nM)("Error deleting link")),!1}}async function b(e,t){let n=new FormData;n.append("logoImage",t);try{console.log("uploading company logo",n);let t=await (0,o.iG)("/admin/update-company-logo",n);if(await (0,a.aK)(e),200===t.status)return console.log("Company logo updated successfully"),e((0,r.nM)("Company logo updated successfully")),!0;return console.error("Error updating company logo:",t.data.error),e((0,r.nM)("Failed to update company logo")),!1}catch(t){return console.error("Error updating company logo:",t),e((0,r.nM)("Error updating company logo")),!1}}}},function(e){e.O(0,[139,3463,3301,8575,293,9810,187,3145,9932,3919,9129,2786,9826,8166,8760,9414,7571,990,3344,9662,1356,2971,2117,1744],function(){return e(e.s=38980)}),_N_E=e.O()}]);