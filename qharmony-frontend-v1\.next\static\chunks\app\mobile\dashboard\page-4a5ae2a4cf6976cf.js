(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4668],{34211:function(e,o,t){Promise.resolve().then(t.bind(t,89078))},89078:function(e,o,t){"use strict";t.r(o);var n=t(57437),r=t(14702),i=t(42374),s=t(48223),a=t(95656),l=t(94013),c=t(2265),d=t(18761),x=t(99376),h=t(68575),u=t(47723);o.default=(0,d.Z)(()=>{let e=(0,x.useRouter)(),o=(0,h.I0)(),[t,d]=(0,c.useState)(!1);return(0,c.useEffect)(()=>{d("true"===localStorage.getItem("isTeamsApp1"))},[]),(0,n.jsx)(s.Z,{children:(0,n.jsxs)(a.Z,{sx:{height:"100%",width:"100vw",bgcolor:"#f6f8fc",display:"flex",flexDirection:"column",alignItems:"center"},children:[(0,n.jsx)(i.Z,{}),(0,n.jsx)(r.Z,{}),(0,n.jsx)(a.Z,{sx:{width:"100%",mt:2}}),(0,n.jsx)(l.Z,{variant:"contained",onClick:()=>o((0,u.FJ)()),sx:{textTransform:"none",borderRadius:"8px",bgcolor:"rgba(0, 0, 0, 0.06)",color:"black",boxShadow:"none",width:"90%",paddingY:"10px","&:hover":{backgroundColor:"rgba(0, 0, 0, 0.1)",boxShadow:"none"}},children:"View Benefits"}),(0,n.jsx)(a.Z,{sx:{width:"100%",mt:2}}),!t&&(0,n.jsx)(l.Z,{variant:"contained",onClick:()=>e.push("/qHarmonyBot"),sx:{textTransform:"none",borderRadius:"8px",bgcolor:"rgba(0, 0, 0, 0.06)",color:"black",boxShadow:"none",width:"90%",paddingY:"10px","&:hover":{backgroundColor:"rgba(0, 0, 0, 0.1)",boxShadow:"none"},mb:10},children:"Chat with Brea"})]})})})},48223:function(e,o,t){"use strict";var n=t(57437),r=t(2265),i=t(47369),s=t(99376),a=t(83337),l=t(70623),c=t(39547),d=t(35389),x=t(95656);let h=()=>/Mobi|Android/i.test(navigator.userAgent);o.Z=e=>{let{children:o}=e,{user:t,loading:u}=(0,i.a)(),f=(0,s.useRouter)(),g=(0,s.usePathname)(),p=(0,a.T)(),[m,b]=(0,r.useState)(!1),j=(0,a.C)(e=>e.user.userProfile);return((0,r.useEffect)(()=>{{let e=localStorage.getItem("userid1")||localStorage.getItem("userId");console.log("USER ID FROM LOCAL STORAGE: ",e),e&&!j.name&&(p((0,l.Iv)(e)),(async()=>{try{await (0,c.M_)(p,e),await (0,c.aK)(p)}catch(e){console.error("Error fetching user data in ProtectedRoute:",e)}})())}},[p,j.name]),(0,r.useEffect)(()=>{console.log("ProtectedRoute useEffect triggered"),console.log("Current user: ",t),console.log("Loading state: ",u),console.log("Current user details: ",j),u||t||(console.log("User not authenticated, redirecting to home"),b(!1),f.push("/")),!u&&j.companyId&&""===j.companyId&&(console.log("Waiting to retrieve company details"),b(!1)),!u&&j.companyId&&""!==j.companyId&&(console.log("User found, rendering children"),b(!0)),h()&&!g.startsWith("/mobile")&&(console.log("Redirecting to mobile version of ".concat(g)),f.push("/mobile".concat(g)))},[t,u,j,f,g]),m)?t?(0,n.jsx)(n.Fragment,{children:o}):null:(0,n.jsx)(x.Z,{sx:{display:"flex",justifyContent:"center",alignItems:"center",height:"100vh",bgcolor:"#f6f8fc"},children:(0,n.jsx)(d.Z,{})})}},18761:function(e,o,t){"use strict";t.d(o,{Z:function(){return O}});var n=t(57437),r=t(93062),i=t(71495),s=t(71004),a=t(59832),l=t(92253),c=t(61910),d=t(95656),x=t(94013),h=t(46387),u=t(8350),f=t(15273),g=t(73261),p=t(11741),m=t(53431),b=t(67051),j=t(33145),Z=t(83337),v=t(2265),y=t(39547),C=t(70623),w=t(99376),S=t(56336),k=t(68575),I=t(31175),F=t(46837),R=t(47369),E=t(15116),T=t(47723);let P="75vw";var A=()=>{let e=(0,Z.T)(),o=(0,w.useRouter)();(0,w.usePathname)();let{logout:t}=(0,R.a)(),r=(0,Z.C)(e=>e.company.companyBenefitTypes);(0,Z.C)(e=>e.user.selectedBenefitType);let i=(0,k.v9)(e=>(0,C.MP)(e));(0,v.useEffect)(()=>{i&&(0,y.N)(e,i)},[i,e]);let[s,a]=(0,v.useState)(!1);(0,v.useEffect)(()=>{a("true"===localStorage.getItem("isTeamsApp1"))},[]);let c=t=>{e((0,C.v2)(t)),o.push("/viewBenefitsByType/".concat(t))};return(0,n.jsxs)(l.ZP,{sx:{width:P,height:"100vh",flexShrink:0,"& .MuiDrawer-paper":{width:P,boxSizing:"border-box",bgcolor:"#ffffff",position:"relative"}},variant:"permanent",anchor:"left",children:[(0,n.jsxs)(d.Z,{sx:{padding:0,height:"100%",position:"relative",bgcolor:"#ffffff"},children:[(0,n.jsx)(d.Z,{sx:{mx:2,mt:2,px:1,py:.5,borderRadius:2,position:"relative","&:hover":{backgroundColor:"#f0f0f0"},bgcolor:"#F5F6FA"},children:(0,n.jsxs)(x.Z,{variant:"text",sx:{width:"100%",borderRadius:2,bgcolor:"#F5F6FA",color:"#333",fontWeight:"medium",fontSize:"1rem",textTransform:"none","&:hover":{backgroundColor:"#f0f0f0"},display:"flex",alignItems:"center",justifyContent:"flex-start"},onClick:()=>{o.push("/mobile/dashboard"),e((0,T.dL)())},children:[(0,n.jsx)(F.Z,{sx:{mr:1}}),"Home"]})}),(0,n.jsx)(h.Z,{sx:{mt:2,fontWeight:500,paddingX:2.5,fontSize:"1.2rem",color:"black"},children:"My Benefits"}),(0,n.jsx)(h.Z,{sx:{fontWeight:500,paddingX:2.5,paddingY:1,fontSize:".7rem",color:"rgba(0, 0, 0, 0.4)"},children:"SELECT ANY TO VIEW"}),(0,n.jsx)(u.Z,{sx:{my:1}}),(0,n.jsx)(f.Z,{children:r.length>0?r.map(o=>(0,n.jsx)(g.ZP,{disablePadding:!0,children:(0,n.jsxs)(p.Z,{onClick:()=>{c(o),e((0,T.dL)())},sx:{borderRadius:2,position:"relative","&:hover":{backgroundColor:"#f0f0f0"},bgcolor:"#F5F6FA",mx:2,mt:2},children:[(0,n.jsx)(m.Z,{sx:{minWidth:0,mr:2,pt:.5},children:(0,I.RS)(o)}),(0,n.jsx)(b.Z,{primary:o,sx:{fontWeight:"medium",color:"#333",fontSize:"1rem"}})]})},o)):(0,n.jsx)(h.Z,{variant:"body1",sx:{color:"#999",padding:2.5},children:"No benefits available at the moment"})})]}),!s&&(0,n.jsxs)(d.Z,{sx:{display:"flex",alignItems:"center",justifyContent:"center",bgcolor:"#F5F6FA",borderRadius:"30px",padding:"10px 20px",cursor:"pointer",position:"absolute",bottom:"50px",left:"50%",transform:"translateX(-50%)",width:"calc(100% - 40px)"},onClick:()=>{o.push("/qHarmonyBot"),e((0,T.dL)())},children:[(0,n.jsx)(j.default,{src:S.Z,alt:"AI Chat",style:{borderRadius:"100%",width:"40px",height:"40px",marginRight:"10px"}}),(0,n.jsxs)(d.Z,{children:[(0,n.jsx)(h.Z,{variant:"body1",sx:{fontWeight:"bold"},children:"Chat with Brea"}),(0,n.jsx)(h.Z,{variant:"body2",sx:{color:"#6c757d"},children:"24/7 available"})]})]}),(0,n.jsxs)(x.Z,{onClick:t,sx:{backgroundColor:"transparent",color:"#333",marginBottom:"5px",textTransform:"none",padding:"8px 16px",display:"flex",alignItems:"center",justifyContent:"center",gap:"8px","&:hover":{backgroundColor:"transparent",color:"#555"},boxShadow:"none"},children:[(0,n.jsx)(E.Z,{sx:{fontSize:"18px"}}),(0,n.jsx)(h.Z,{sx:{fontWeight:500,fontSize:"14px"},children:"Logout"})]})]})},O=e=>{let o=o=>{let t=(0,k.I0)(),x=(0,Z.C)(e=>e.mobileSidebarToggle.isOpen),h=(0,w.usePathname)();return(0,n.jsxs)(d.Z,{children:[(0,n.jsx)(r.ZP,{}),!("/"===h||"/onboard"===h)&&(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(i.Z,{position:"static",sx:{backgroundColor:"black"},children:(0,n.jsx)(s.Z,{sx:{mb:"/mobile/dashboard"===h?6:0},children:(0,n.jsx)(a.Z,{edge:"start",color:"inherit","aria-label":"menu",onClick:()=>t((0,T.FJ)()),children:(0,n.jsx)(c.Z,{fontSize:"large"})})})}),(0,n.jsx)(l.ZP,{anchor:"left",open:x,onClose:()=>t((0,T.dL)()),children:(0,n.jsx)(A,{})})]}),(0,n.jsx)(e,{...o})]})};return o.displayName="WithMobileEdgeFill(".concat(e.displayName||e.name||"Component",")"),o}},47723:function(e,o,t){"use strict";t.d(o,{FJ:function(){return r},dL:function(){return i}});let n=(0,t(39129).oM)({name:"drawer",initialState:{isOpen:!1},reducers:{openDrawer:e=>{e.isOpen=!0},closeDrawer:e=>{e.isOpen=!1},toggleDrawer:e=>{e.isOpen=!e.isOpen}}}),{openDrawer:r,closeDrawer:i,toggleDrawer:s}=n.actions;o.ZP=n.reducer}},function(e){e.O(0,[139,3463,3301,8575,8685,187,1423,9932,3919,9129,2786,9826,8166,8760,9414,7404,3209,7571,2170,6961,3344,9662,8005,4825,2971,2117,1744],function(){return e(e.s=34211)}),_N_E=e.O()}]);