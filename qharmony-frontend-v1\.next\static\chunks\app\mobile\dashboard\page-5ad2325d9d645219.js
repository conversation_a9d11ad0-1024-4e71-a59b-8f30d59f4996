(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4668],{34211:function(e,t,n){Promise.resolve().then(n.bind(n,89078))},40256:function(e,t,n){"use strict";n.d(t,{$R:function(){return d},A_:function(){return s},BO:function(){return i},GH:function(){return f},_n:function(){return r},be:function(){return a},iG:function(){return c},j0:function(){return l}});var o=n(83464);let r="http://localhost:8080",i="<EMAIL>,<EMAIL>,<EMAIL>".split(",").map(e=>e.trim()),a=o.Z.create({baseURL:r});async function s(e,t,n){let o=new URL(n?"".concat(n).concat(e):"".concat(r).concat(e));return t&&Object.keys(t).forEach(e=>o.searchParams.append(e,t[e])),(await a.get(o.toString())).data}async function l(e,t,n){let o=n?"".concat(n).concat(e):"".concat(r).concat(e),i=await a.post(o,t,{headers:{"Content-Type":"application/json"}});return{status:i.status,data:i.data}}async function c(e,t,n){let o=n?"".concat(n).concat(e):"".concat(r).concat(e);console.log("Document upload to: ".concat(o));let i=await a.post(o,t,{headers:{"Content-Type":"multipart/form-data"}});return{status:i.status,data:i.data}}async function d(e,t,n){let o=new URL(n?"".concat(n).concat(e):"".concat(r).concat(e));return t&&Object.keys(t).forEach(e=>o.searchParams.append(e,t[e])),console.log("GET Blob request to: ".concat(o.toString())),(await a.get(o.toString(),{responseType:"blob"})).data}async function f(e,t,n){let o=n?"".concat(n).concat(e):"".concat(r).concat(e),i=await a.put(o,t,{headers:{"Content-Type":"application/json"}});return{status:i.status,data:i.data}}a.interceptors.request.use(e=>{let t=localStorage.getItem("userid1")||localStorage.getItem("userId");return t?e.headers["user-id"]=t:console.warn("No user ID found in localStorage for API request"),e})},14702:function(e,t,n){"use strict";var o=n(57437),r=n(95656),i=n(53410),a=n(46387),s=n(59832),l=n(35389),c=n(89414),d=n(96729),f=n(83337),u=n(2265),x=n(54862),p=n(7022),g=n(33145);t.Z=()=>{var e;let t=(0,f.T)(),n=(0,f.C)(e=>e.company.companyDetails),h=(0,u.useRef)(null),[m,b]=(0,u.useState)(!1),y=()=>{h.current&&h.current.click()};return(0,o.jsx)(r.Z,{sx:{display:"flex",flexDirection:"column",alignItems:"flex-start",mt:3},children:(0,o.jsxs)(i.Z,{sx:{display:"flex",flexDirection:"column",alignItems:"flex-start",justifyContent:"space-between",width:"100%",boxShadow:"none",borderRadius:"30px",bgcolor:"#ffffff",paddingBottom:"12px",paddingX:"12px"},children:[(0,o.jsxs)(r.Z,{sx:{display:"flex",alignItems:"center",justifyContent:"space-between",width:"100%",mb:2,paddingTop:"25px",paddingX:"12px"},children:[(0,o.jsxs)(r.Z,{children:[(0,o.jsx)(a.Z,{variant:"h5",sx:{fontWeight:"bold",mb:.5,fontSize:"24px"},children:n.name||"N/A"}),(0,o.jsx)(a.Z,{variant:"body2",sx:{color:"#6c757d",fontSize:"16px"},children:n.industry||"N/A"})]}),(0,o.jsxs)(r.Z,{sx:{position:"relative",ml:2},children:[(0,o.jsx)(r.Z,{onClick:y,sx:{width:100,height:100,borderRadius:"12px",overflow:"hidden",position:"relative",display:"flex",alignItems:"center",justifyContent:"center",bgcolor:"#f4f4f4",cursor:"pointer"},children:(null===(e=n.details)||void 0===e?void 0:e.logo)?(0,o.jsx)(g.default,{src:n.details.logo,alt:"Company Logo",layout:"fill",objectFit:"contain"}):(0,o.jsxs)(r.Z,{sx:{display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",height:"100%",color:"#9e9e9e"},children:[(0,o.jsx)(d.Z,{fontSize:"large"}),(0,o.jsx)(a.Z,{variant:"caption",sx:{mb:.5,fontWeight:600},children:"Logo"})]})}),(0,o.jsx)(s.Z,{onClick:y,sx:{position:"absolute",top:0,right:0,bgcolor:"#000000",color:"#ffffff",borderRadius:"50%",width:"24px",height:"24px",p:"2px"},children:m?(0,o.jsx)(l.Z,{size:16}):(0,o.jsx)(x.Z,{sx:{fontSize:"16px"}})})]})]}),(0,o.jsx)(i.Z,{sx:{borderRadius:"30px",p:2.5,boxShadow:"none",bgcolor:"rgba(245, 245, 245, 0.7)",width:"100%",mt:2},children:(0,o.jsxs)(c.ZP,{container:!0,spacing:2,children:[(0,o.jsx)(c.ZP,{item:!0,xs:6,children:(0,o.jsx)(a.Z,{variant:"body2",sx:{color:"#6c757d",fontSize:"14px",textAlign:"left"},children:"Company Size"})}),(0,o.jsx)(c.ZP,{item:!0,xs:6,children:(0,o.jsx)(a.Z,{variant:"body2",sx:{fontSize:"14px",textAlign:"right",fontWeight:600,wordBreak:"break-word",overflowWrap:"break-word"},children:n.companySize||"2"})}),(0,o.jsx)(c.ZP,{item:!0,xs:6,children:(0,o.jsx)(a.Z,{variant:"body2",sx:{color:"#6c757d",fontSize:"14px",textAlign:"left",wordBreak:"break-word",overflowWrap:"break-word"},children:"Website"})}),(0,o.jsx)(c.ZP,{item:!0,xs:6,children:(0,o.jsx)(a.Z,{variant:"body2",sx:{fontSize:"14px",textAlign:"right",fontWeight:600,wordBreak:"break-word",overflowWrap:"break-word"},children:n.website?(0,o.jsx)("a",{href:"http://".concat(n.website),target:"_blank",rel:"noopener noreferrer",style:{textDecoration:"none",color:"#000"},children:n.website}):"BenOsphere.com"})}),(0,o.jsx)(c.ZP,{item:!0,xs:6,children:(0,o.jsx)(a.Z,{variant:"body2",sx:{color:"#6c757d",fontSize:"14px",textAlign:"left",wordBreak:"break-word",overflowWrap:"break-word"},children:"Admin Email"})}),(0,o.jsx)(c.ZP,{item:!0,xs:6,children:(0,o.jsx)(a.Z,{variant:"body2",sx:{fontSize:"14px",textAlign:"right",fontWeight:600,wordBreak:"break-word",overflowWrap:"break-word"},children:n.adminEmail||"<EMAIL>"})})]})}),(0,o.jsx)("input",{type:"file",accept:".png, .jpg",ref:h,style:{display:"none"},onChange:e=>{var n;let o=null===(n=e.target.files)||void 0===n?void 0:n[0];o&&(b(!0),(0,p.mH)(t,o).finally(()=>{b(!1)}))}})]})})}},42374:function(e,t,n){"use strict";var o=n(57437),r=n(95656),i=n(67116),a=n(46387),s=n(59832),l=n(53410),c=n(89414),d=n(83337),f=n(2265),u=n(54862),x=n(98005);t.Z=()=>{var e,t;let n=(0,d.C)(e=>e.user.userProfile),[p,g]=(0,f.useState)(!1);return(0,o.jsxs)(r.Z,{sx:{display:"flex",flexDirection:"column",alignItems:"center",bgcolor:"#ffffff",paddingTop:7,paddingBottom:"12px",paddingX:"12px",borderRadius:"30px",width:"100%",position:"relative"},children:[(0,o.jsx)(r.Z,{sx:{position:"absolute",top:-60,display:"flex",justifyContent:"center",width:"100%"},children:(0,o.jsx)(i.Z,{sx:{backgroundImage:"linear-gradient(180deg, #4BD1E9 0%, #1274CF 100%)",color:"#ffffff",width:130,height:130,border:"10px solid #ffffff",fontSize:"48px",fontWeight:800},children:(e=>{console.log("Getting initials for name: ",JSON.stringify(n));let[t,o]=e.split(" ");return"".concat(t[0].toUpperCase()).concat(o?o[0].toUpperCase():"")})(n.name)})}),(0,o.jsxs)(r.Z,{sx:{display:"flex",alignItems:"center",mt:4,mb:4,position:"relative"},children:[(0,o.jsx)(a.Z,{variant:"h5",sx:{fontWeight:"bold",fontSize:"32px",flexGrow:1},children:n.name.replace(/\b\w/g,e=>e.toUpperCase())||"N/A"}),(0,o.jsx)(s.Z,{onClick:()=>{g(!0)},sx:{bgcolor:"#000000",color:"#ffffff",borderRadius:"50%",width:"24px",height:"24px",p:"2px",ml:2},children:(0,o.jsx)(u.Z,{sx:{fontSize:"16px"}})})]}),(0,o.jsx)(l.Z,{sx:{borderRadius:"30px",p:2.5,boxShadow:"none",bgcolor:"rgba(245, 245, 245, 0.7)",width:"100%"},children:(0,o.jsxs)(c.ZP,{container:!0,spacing:2,children:[(0,o.jsx)(c.ZP,{item:!0,xs:6,children:(0,o.jsx)(a.Z,{sx:{color:"#6c757d",fontSize:"14px",textAlign:"left"},children:"Title"})}),(0,o.jsx)(c.ZP,{item:!0,xs:6,children:(0,o.jsx)(a.Z,{sx:{fontSize:"14px",textAlign:"right",fontWeight:600},children:(null===(e=n.details)||void 0===e?void 0:e.title)||"N/A"})}),(0,o.jsx)(c.ZP,{item:!0,xs:6,children:(0,o.jsx)(a.Z,{variant:"body2",sx:{color:"#6c757d",fontSize:"14px",textAlign:"left"},children:"Department"})}),(0,o.jsx)(c.ZP,{item:!0,xs:6,children:(0,o.jsx)(a.Z,{variant:"body2",sx:{fontSize:"14px",textAlign:"right",fontWeight:600},children:(null===(t=n.details)||void 0===t?void 0:t.department)||"N/A"})}),(0,o.jsx)(c.ZP,{item:!0,xs:6,children:(0,o.jsx)(a.Z,{variant:"body2",sx:{color:"#6c757d",fontSize:"14px",textAlign:"left"},children:"Employment Type"})}),(0,o.jsx)(c.ZP,{item:!0,xs:6,children:(0,o.jsx)(a.Z,{variant:"body2",sx:{fontSize:"14px",textAlign:"right",fontWeight:600},children:"Full Time"})})]})}),(0,o.jsx)(x.default,{open:p,onClose:()=>{g(!1)}})]})}},89078:function(e,t,n){"use strict";n.r(t);var o=n(57437),r=n(14702),i=n(42374),a=n(48223),s=n(95656),l=n(94013),c=n(2265),d=n(18761),f=n(99376),u=n(68575),x=n(47723);t.default=(0,d.Z)(()=>{let e=(0,f.useRouter)(),t=(0,u.I0)(),[n,d]=(0,c.useState)(!1);return(0,c.useEffect)(()=>{d("true"===localStorage.getItem("isTeamsApp1"))},[]),(0,o.jsx)(a.Z,{children:(0,o.jsxs)(s.Z,{sx:{height:"100%",width:"100vw",bgcolor:"#f6f8fc",display:"flex",flexDirection:"column",alignItems:"center"},children:[(0,o.jsx)(i.Z,{}),(0,o.jsx)(r.Z,{}),(0,o.jsx)(s.Z,{sx:{width:"100%",mt:2}}),(0,o.jsx)(l.Z,{variant:"contained",onClick:()=>t((0,x.FJ)()),sx:{textTransform:"none",borderRadius:"8px",bgcolor:"rgba(0, 0, 0, 0.06)",color:"black",boxShadow:"none",width:"90%",paddingY:"10px","&:hover":{backgroundColor:"rgba(0, 0, 0, 0.1)",boxShadow:"none"}},children:"View Benefits"}),(0,o.jsx)(s.Z,{sx:{width:"100%",mt:2}}),!n&&(0,o.jsx)(l.Z,{variant:"contained",onClick:()=>e.push("/qHarmonyBot"),sx:{textTransform:"none",borderRadius:"8px",bgcolor:"rgba(0, 0, 0, 0.06)",color:"black",boxShadow:"none",width:"90%",paddingY:"10px","&:hover":{backgroundColor:"rgba(0, 0, 0, 0.1)",boxShadow:"none"},mb:10},children:"Chat with Brea"})]})})})},48223:function(e,t,n){"use strict";var o=n(57437),r=n(2265),i=n(47369),a=n(99376),s=n(83337),l=n(70623),c=n(39547),d=n(35389),f=n(95656);let u=()=>/Mobi|Android/i.test(navigator.userAgent);t.Z=e=>{let{children:t}=e,{user:n,loading:x}=(0,i.a)(),p=(0,a.useRouter)(),g=(0,a.usePathname)(),h=(0,s.T)(),[m,b]=(0,r.useState)(!1),y=(0,s.C)(e=>e.user.userProfile);return((0,r.useEffect)(()=>{{let e=localStorage.getItem("userid1")||localStorage.getItem("userId");console.log("USER ID FROM LOCAL STORAGE: ",e),e&&!y.name&&(h((0,l.Iv)(e)),(async()=>{try{await (0,c.M_)(h,e),await (0,c.aK)(h)}catch(e){console.error("Error fetching user data in ProtectedRoute:",e)}})())}},[h,y.name]),(0,r.useEffect)(()=>{console.log("ProtectedRoute useEffect triggered"),console.log("Current user: ",n),console.log("Loading state: ",x),console.log("Current user details: ",y),x||n||(console.log("User not authenticated, redirecting to home"),b(!1),p.push("/")),!x&&y.companyId&&""===y.companyId&&(console.log("Waiting to retrieve company details"),b(!1)),!x&&y.companyId&&""!==y.companyId&&(console.log("User found, rendering children"),b(!0)),u()&&!g.startsWith("/mobile")&&(console.log("Redirecting to mobile version of ".concat(g)),p.push("/mobile".concat(g)))},[n,x,y,p,g]),m)?n?(0,o.jsx)(o.Fragment,{children:t}):null:(0,o.jsx)(f.Z,{sx:{display:"flex",justifyContent:"center",alignItems:"center",height:"100vh",bgcolor:"#f6f8fc"},children:(0,o.jsx)(d.Z,{})})}},18761:function(e,t,n){"use strict";n.d(t,{Z:function(){return z}});var o=n(57437),r=n(93062),i=n(71495),a=n(71004),s=n(59832),l=n(92253),c=n(61910),d=n(95656),f=n(94013),u=n(46387),x=n(8350),p=n(15273),g=n(73261),h=n(11741),m=n(53431),b=n(67051),y=n(33145),j=n(83337),w=n(2265),Z=n(39547),v=n(70623),S=n(99376),k=n(56336),C=n(68575),E=n(31175),I=n(46837),R=n(47369),A=n(15116),F=n(47723);let P="75vw";var T=()=>{let e=(0,j.T)(),t=(0,S.useRouter)();(0,S.usePathname)();let{logout:n}=(0,R.a)(),r=(0,j.C)(e=>e.company.companyBenefitTypes);(0,j.C)(e=>e.user.selectedBenefitType);let i=(0,C.v9)(e=>(0,v.MP)(e));(0,w.useEffect)(()=>{i&&(0,Z.N)(e,i)},[i,e]);let[a,s]=(0,w.useState)(!1);(0,w.useEffect)(()=>{s("true"===localStorage.getItem("isTeamsApp1"))},[]);let c=n=>{e((0,v.v2)(n)),t.push("/viewBenefitsByType/".concat(n))};return(0,o.jsxs)(l.ZP,{sx:{width:P,height:"100vh",flexShrink:0,"& .MuiDrawer-paper":{width:P,boxSizing:"border-box",bgcolor:"#ffffff",position:"relative"}},variant:"permanent",anchor:"left",children:[(0,o.jsxs)(d.Z,{sx:{padding:0,height:"100%",position:"relative",bgcolor:"#ffffff"},children:[(0,o.jsx)(d.Z,{sx:{mx:2,mt:2,px:1,py:.5,borderRadius:2,position:"relative","&:hover":{backgroundColor:"#f0f0f0"},bgcolor:"#F5F6FA"},children:(0,o.jsxs)(f.Z,{variant:"text",sx:{width:"100%",borderRadius:2,bgcolor:"#F5F6FA",color:"#333",fontWeight:"medium",fontSize:"1rem",textTransform:"none","&:hover":{backgroundColor:"#f0f0f0"},display:"flex",alignItems:"center",justifyContent:"flex-start"},onClick:()=>{t.push("/mobile/dashboard"),e((0,F.dL)())},children:[(0,o.jsx)(I.Z,{sx:{mr:1}}),"Home"]})}),(0,o.jsx)(u.Z,{sx:{mt:2,fontWeight:500,paddingX:2.5,fontSize:"1.2rem",color:"black"},children:"My Benefits"}),(0,o.jsx)(u.Z,{sx:{fontWeight:500,paddingX:2.5,paddingY:1,fontSize:".7rem",color:"rgba(0, 0, 0, 0.4)"},children:"SELECT ANY TO VIEW"}),(0,o.jsx)(x.Z,{sx:{my:1}}),(0,o.jsx)(p.Z,{children:r.length>0?r.map(t=>(0,o.jsx)(g.ZP,{disablePadding:!0,children:(0,o.jsxs)(h.Z,{onClick:()=>{c(t),e((0,F.dL)())},sx:{borderRadius:2,position:"relative","&:hover":{backgroundColor:"#f0f0f0"},bgcolor:"#F5F6FA",mx:2,mt:2},children:[(0,o.jsx)(m.Z,{sx:{minWidth:0,mr:2,pt:.5},children:(0,E.RS)(t)}),(0,o.jsx)(b.Z,{primary:t,sx:{fontWeight:"medium",color:"#333",fontSize:"1rem"}})]})},t)):(0,o.jsx)(u.Z,{variant:"body1",sx:{color:"#999",padding:2.5},children:"No benefits available at the moment"})})]}),!a&&(0,o.jsxs)(d.Z,{sx:{display:"flex",alignItems:"center",justifyContent:"center",bgcolor:"#F5F6FA",borderRadius:"30px",padding:"10px 20px",cursor:"pointer",position:"absolute",bottom:"50px",left:"50%",transform:"translateX(-50%)",width:"calc(100% - 40px)"},onClick:()=>{t.push("/qHarmonyBot"),e((0,F.dL)())},children:[(0,o.jsx)(y.default,{src:k.Z,alt:"AI Chat",style:{borderRadius:"100%",width:"40px",height:"40px",marginRight:"10px"}}),(0,o.jsxs)(d.Z,{children:[(0,o.jsx)(u.Z,{variant:"body1",sx:{fontWeight:"bold"},children:"Chat with Brea"}),(0,o.jsx)(u.Z,{variant:"body2",sx:{color:"#6c757d"},children:"24/7 available"})]})]}),(0,o.jsxs)(f.Z,{onClick:n,sx:{backgroundColor:"transparent",color:"#333",marginBottom:"5px",textTransform:"none",padding:"8px 16px",display:"flex",alignItems:"center",justifyContent:"center",gap:"8px","&:hover":{backgroundColor:"transparent",color:"#555"},boxShadow:"none"},children:[(0,o.jsx)(A.Z,{sx:{fontSize:"18px"}}),(0,o.jsx)(u.Z,{sx:{fontWeight:500,fontSize:"14px"},children:"Logout"})]})]})},z=e=>{let t=t=>{let n=(0,C.I0)(),f=(0,j.C)(e=>e.mobileSidebarToggle.isOpen),u=(0,S.usePathname)();return(0,o.jsxs)(d.Z,{children:[(0,o.jsx)(r.ZP,{}),!("/"===u||"/onboard"===u)&&(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)(i.Z,{position:"static",sx:{backgroundColor:"black"},children:(0,o.jsx)(a.Z,{sx:{mb:"/mobile/dashboard"===u?6:0},children:(0,o.jsx)(s.Z,{edge:"start",color:"inherit","aria-label":"menu",onClick:()=>n((0,F.FJ)()),children:(0,o.jsx)(c.Z,{fontSize:"large"})})})}),(0,o.jsx)(l.ZP,{anchor:"left",open:f,onClose:()=>n((0,F.dL)()),children:(0,o.jsx)(T,{})})]}),(0,o.jsx)(e,{...t})]})};return t.displayName="WithMobileEdgeFill(".concat(e.displayName||e.name||"Component",")"),t}},7022:function(e,t,n){"use strict";n.d(t,{$t:function(){return d},SS:function(){return u},Y0:function(){return a},cd:function(){return f},fH:function(){return x},mH:function(){return p},ov:function(){return c},v0:function(){return s}});var o=n(40256),r=n(39124),i=n(39547);async function a(e,t,n){try{let i=await (0,o.A_)("/benefits/benefit-by-type",{companyId:t,type:n});i&&i.benefits?(console.log("GET BENEFITS FOR TYPE RESPONSE: ",i.benefits),e((0,r.oQ)({benefitType:n,benefits:i.benefits})),e((0,r.nM)("Benefits fetched successfully"))):(console.error("Invalid response format:",i),e((0,r.nM)("Failed to fetch benefits")))}catch(t){console.error("Error fetching benefits:",t),e((0,r.nM)("Error fetching benefits"))}}async function s(e,t,n,i){let a={benefitId:t,page:i};console.log("data",a);let s=await (0,o.A_)("/benefits/one-benefit",a),c={...s,benefitId:t};for(let t of(e((0,r.F5)(c)),s.documents)){let o=decodeURIComponent(t.split("_____")[1]);l(e,t,n,o)}}async function l(e,t,n,i){let a={objectKey:t,companyId:n};console.log("data",a);let s=await (0,o.$R)("/benefits/document",a);if(console.log("VIEW BENEFIT RESPONSE: ",s),s){let n=new Blob([s],{type:"application/pdf"}),o=URL.createObjectURL(n);e((0,r.D7)([{documentObjectKey:t,document:o,originalFileName:i}]))}}let c=async(e,t,n,r,s)=>200===(await (0,o.j0)("/benefits/toggle-benefits/",{benefitId:n,companyId:t,isActivated:r})).status&&(await a(e,t,s),await (0,i.N)(e,t),!0);async function d(e,t,n,i){let a=new FormData;i.forEach(e=>a.append("documents",e)),a.append("companyId",n),a.append("benefitId",t);try{console.log("uploadDocument",a);let s=await (0,o.iG)("/benefits/add/document",a),c=s.data.objectKeys;if(console.log("newObjectKeys",c),200===s.status)return c.forEach((o,a)=>{let s=i[a].name;e((0,r.H_)({benefitId:t,document:o})),l(e,o,n,s)}),e((0,r.nM)("Document added successfully")),!0;return console.error("Error adding document:",s.data.error),e((0,r.nM)("Failed to add document")),!1}catch(t){return console.error("Error adding document:",t),e((0,r.nM)("Error adding document")),!1}}async function f(e,t,n,i){try{let a=await (0,o.j0)("/benefits/delete/document",{benefitId:t,companyId:n,objectKey:i});if(200===a.status)return e((0,r.iH)({benefitId:t,document:i})),e((0,r.nM)("Document deleted successfully")),!0;return console.error("Error deleting document:",a.data.error),e((0,r.nM)("Failed to delete document")),!1}catch(t){return console.error("Error deleting document:",t),e((0,r.nM)("Error deleting document")),!1}}async function u(e,t,n,i){try{let a=await (0,o.j0)("/benefits/add/links",{benefitId:t,companyId:n,urls:[i]});if(200===a.status)return e((0,r.MJ)({benefitId:t,link:i})),e((0,r.nM)("Link added successfully")),!0;return console.error("Error adding link:",a.data.error),e((0,r.nM)("Failed to add link")),!1}catch(t){return console.error("Error adding link:",t),e((0,r.nM)("Error adding link")),!1}}async function x(e,t,n,i){try{let a=await (0,o.j0)("/benefits/delete/link",{benefitId:t,companyId:n,urls:i});if(200===a.status)return e((0,r.Yw)({benefitId:t,link:i})),e((0,r.nM)("Link deleted successfully")),!0;return console.error("Error deleting link:",a.data.error),e((0,r.nM)("Failed to delete link")),!1}catch(t){return console.error("Error deleting link:",t),e((0,r.nM)("Error deleting link")),!1}}async function p(e,t){let n=new FormData;n.append("logoImage",t);try{console.log("uploading company logo",n);let t=await (0,o.iG)("/admin/update-company-logo",n);if(await (0,i.aK)(e),200===t.status)return console.log("Company logo updated successfully"),e((0,r.nM)("Company logo updated successfully")),!0;return console.error("Error updating company logo:",t.data.error),e((0,r.nM)("Failed to update company logo")),!1}catch(t){return console.error("Error updating company logo:",t),e((0,r.nM)("Error updating company logo")),!1}}},47723:function(e,t,n){"use strict";n.d(t,{FJ:function(){return r},dL:function(){return i}});let o=(0,n(39129).oM)({name:"drawer",initialState:{isOpen:!1},reducers:{openDrawer:e=>{e.isOpen=!0},closeDrawer:e=>{e.isOpen=!1},toggleDrawer:e=>{e.isOpen=!e.isOpen}}}),{openDrawer:r,closeDrawer:i,toggleDrawer:a}=o.actions;t.ZP=o.reducer}},function(e){e.O(0,[139,3463,3301,8575,293,9810,187,3145,9932,3919,9129,2786,9826,8166,8760,9414,7404,3209,7571,2170,6961,3344,9662,8005,2971,2117,1744],function(){return e(e.s=34211)}),_N_E=e.O()}]);