(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3399],{27335:function(e,t,n){Promise.resolve().then(n.bind(n,45783))},40256:function(e,t,n){"use strict";n.d(t,{$R:function(){return d},A_:function(){return l},BO:function(){return i},GH:function(){return u},_n:function(){return a},be:function(){return o},iG:function(){return c},j0:function(){return s}});var r=n(83464);let a="http://localhost:8080",i="<EMAIL>,<EMAIL>,<EMAIL>".split(",").map(e=>e.trim()),o=r.Z.create({baseURL:a});async function l(e,t,n){let r=new URL(n?"".concat(n).concat(e):"".concat(a).concat(e));return t&&Object.keys(t).forEach(e=>r.searchParams.append(e,t[e])),(await o.get(r.toString())).data}async function s(e,t,n){let r=n?"".concat(n).concat(e):"".concat(a).concat(e),i=await o.post(r,t,{headers:{"Content-Type":"application/json"}});return{status:i.status,data:i.data}}async function c(e,t,n){let r=n?"".concat(n).concat(e):"".concat(a).concat(e);console.log("Document upload to: ".concat(r));let i=await o.post(r,t,{headers:{"Content-Type":"multipart/form-data"}});return{status:i.status,data:i.data}}async function d(e,t,n){let r=new URL(n?"".concat(n).concat(e):"".concat(a).concat(e));return t&&Object.keys(t).forEach(e=>r.searchParams.append(e,t[e])),console.log("GET Blob request to: ".concat(r.toString())),(await o.get(r.toString(),{responseType:"blob"})).data}async function u(e,t,n){let r=n?"".concat(n).concat(e):"".concat(a).concat(e),i=await o.put(r,t,{headers:{"Content-Type":"application/json"}});return{status:i.status,data:i.data}}o.interceptors.request.use(e=>{let t=localStorage.getItem("userid1")||localStorage.getItem("userId");return t?e.headers["user-id"]=t:console.warn("No user ID found in localStorage for API request"),e})},45783:function(e,t,n){"use strict";n.r(t);var r=n(57437),a=n(2265),i=n(43301),o=n(28411),l=n(41327),s=n(64393),c=n(97404),d=n(95656),u=n(46387),h=n(35389),f=n(94013),x=n(89414),m=n(42187),p=n(99376),g=n(77534),b=n(39547),j=n(83337),y=n(33145),v=n(80944),Z=n(18761);let S=e=>{let{label:t,value:n,onChange:a,error:i,helperText:o,placeholder:d,select:u,children:h,readOnly:f}=e;return(0,r.jsxs)(l.Z,{fullWidth:!0,children:[(0,r.jsx)(s.Z,{sx:{color:"#ffffff",fontWeight:"500",mb:1,fontSize:"17px"},children:t}),(0,r.jsx)(c.Z,{variant:"outlined",value:n,onChange:a,error:!!i,helperText:o,placeholder:d,select:u,inputProps:{readOnly:f},sx:{borderRadius:"10px",backgroundColor:"#333333",mb:0,input:{color:"#ffffff"},"& .MuiOutlinedInput-root":{borderRadius:"10px","&:hover fieldset":{borderColor:i?"#ff0000":"#ffffff"},"&.Mui-focused fieldset":{borderColor:i?"#ff0000":"#ffffff"}},"& .MuiInputLabel-root":{color:"#ffffff"},"& .MuiSvgIcon-root":{color:"#ffffff"},"& .MuiSelect-select":{color:"#ffffff"}},children:h})]})};t.default=(0,Z.Z)(()=>{let e=(0,j.T)(),t=(0,p.useRouter)(),[n,l]=(0,a.useState)(!1),[s,c]=(0,a.useState)(null),[Z,C]=(0,a.useState)(!1),[w,A]=(0,a.useState)(!0),[k,T]=(0,a.useState)(!1),[I,P]=(0,a.useState)(!1),[R,E]=(0,a.useState)({email:"",name:"",role:"",isAdmin:!1,isBroker:!1,isActivated:!1}),[F,B]=(0,a.useState)({name:"",adminEmail:"",adminRole:"",companySize:0,industry:"",location:"",website:"",brokerId:"",brokerageId:"",isBrokerage:!1,isActivated:!1,howHeard:"",referralSource:""}),[D,z]=(0,a.useState)({isAdmin:!1}),[O,L]=(0,a.useState)({});(0,a.useEffect)(()=>{(async()=>{let e=window.location.href,t=await (0,g.lY)(e);if(t.companyDetails&&B(t.companyDetails),t.userDetails&&E(t.userDetails),t.additionalParams&&(z(t.additionalParams),t.additionalParams.email&&c(t.additionalParams.email)),(0,i.JB)(o.I,e)){let e=window.localStorage.getItem("emailForSignIn1");e||t.additionalParams.email?c(e||t.additionalParams.email):l(!0)}A(!1)})()},[]),(0,a.useEffect)(()=>{(async()=>{if(s)try{await (0,i.P6)(o.I,s,window.location.href),console.log("Sign-in successful"),window.localStorage.removeItem("emailForSignIn1"),C(!0)}catch(e){console.error("Error signing in with email link:",e),P(!0)}})()},[s]);let H=()=>{let e={},t=!0;return R.name||(e.name="Full Name is required",t=!1),D.isAdmin&&(F.name||(e.companyName="Company Name is required",t=!1),F.adminRole||(e.adminRole="Role/Title is required",t=!1),F.companySize||(e.companySize="Company Size is required",t=!1),F.industry||(e.industry="Industry is required",t=!1),F.location||(e.location="Country is required",t=!1),F.howHeard||(e.howHeard="How did you hear about us is required",t=!1),F.referralSource||(e.referralSource="Referral Source is required",t=!1)),L(e),t},W=async()=>{if(!H())return;T(!0);let n=await (0,b.zX)(e,F,R);n&&200===n.status&&t.push("/dashboard"),T(!1)},N=(e,t,n)=>{L({...O,[e]:""}),"userDetails"===n?E({...R,[e]:t}):B({...F,[e]:t})};return(0,r.jsxs)(d.Z,{sx:{backgroundColor:"#000000",minHeight:"98px",flexDirection:"column",alignItems:"center",justifyContent:"flex-start"},children:[(0,r.jsxs)(d.Z,{sx:{display:"flex",alignItems:"center",mb:5,cursor:"pointer",position:"absolute",top:"30px",left:"30px"},onClick:()=>console.log("Logo Clicked"),children:[(0,r.jsx)(y.default,{src:v.Z,alt:"BenOsphere Logo",width:40,height:40}),(0,r.jsx)(u.Z,{variant:"h6",sx:{ml:1,fontWeight:"800",color:"#ffffff"},children:"BenOsphere"})]}),w?(0,r.jsx)(h.Z,{color:"inherit"}):I?(0,r.jsxs)(d.Z,{sx:{display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",color:"white",textAlign:"center",height:"100%"},children:[(0,r.jsx)(u.Z,{sx:{fontWeight:"bold",fontSize:"60px",mb:2},children:"❌ Invalid Magic Link"}),(0,r.jsx)(u.Z,{variant:"body1",sx:{fontSize:"20px",color:"#bbbbbb",mb:4},children:"The magic link you used is invalid or has expired. Please try again."}),(0,r.jsx)(f.Z,{variant:"contained",onClick:()=>t.push("/"),sx:{textTransform:"none",background:"linear-gradient(90deg, #7206E6, #B54BFF)",color:"#ffffff",height:"54px",borderRadius:"10px",boxShadow:"none",fontSize:"17px","&:hover":{background:"linear-gradient(90deg, #7206E6, #B54BFF)"}},children:"Try Again"})]}):Z&&(0,r.jsxs)(d.Z,{sx:{width:"100%",maxWidth:"100%"},children:[(0,r.jsx)(u.Z,{variant:"h4",sx:{fontSize:"50px",fontWeight:"bold",lineHeight:"1.2",color:"#ffffff",mb:3},children:"Get Free Access"}),(0,r.jsxs)(x.ZP,{container:!0,spacing:3,sx:{marginBottom:"16px"},children:[(0,r.jsx)(x.ZP,{item:!0,xs:12,children:(0,r.jsx)(S,{label:"Full Name",value:R.name,onChange:e=>N("name",e.target.value,"userDetails"),error:O.name,helperText:O.name,placeholder:"John Doe"})}),(0,r.jsx)(x.ZP,{item:!0,xs:12,children:(0,r.jsx)(S,{label:"Email",value:R.email,onChange:()=>{},readOnly:!0})})]}),D.isAdmin&&(0,r.jsx)(d.Z,{sx:{mt:3},children:(0,r.jsxs)(x.ZP,{container:!0,spacing:3,sx:{marginBottom:"16px"},children:[(0,r.jsx)(x.ZP,{item:!0,xs:12,children:(0,r.jsx)(S,{label:"Company Name",value:F.name,onChange:e=>N("name",e.target.value,"companyDetails"),error:O.companyName,helperText:O.companyName,placeholder:"Doe LLC"})}),(0,r.jsx)(x.ZP,{item:!0,xs:12,children:(0,r.jsxs)(S,{label:"Role/Title",value:F.adminRole,onChange:e=>N("adminRole",e.target.value,"companyDetails"),error:O.adminRole,helperText:O.adminRole,select:!0,children:[(0,r.jsx)(m.Z,{value:"CEO",children:"CEO"}),(0,r.jsx)(m.Z,{value:"HR Manager",children:"HR Manager"}),(0,r.jsx)(m.Z,{value:"Benefits Specialist",children:"Benefits Specialist"}),(0,r.jsx)(m.Z,{value:"CFO",children:"CFO"}),(0,r.jsx)(m.Z,{value:"Other",children:"Other"})]})}),(0,r.jsx)(x.ZP,{item:!0,xs:12,children:(0,r.jsxs)(S,{label:"Company Size",value:F.companySize,onChange:e=>N("companySize",e.target.value,"companyDetails"),error:O.companySize,helperText:O.companySize,select:!0,children:[(0,r.jsx)(m.Z,{value:1,children:"1-10 employees"}),(0,r.jsx)(m.Z,{value:2,children:"11-50 employees"}),(0,r.jsx)(m.Z,{value:3,children:"51-200 employees"}),(0,r.jsx)(m.Z,{value:4,children:"201-500 employees"}),(0,r.jsx)(m.Z,{value:5,children:"500+ employees"})]})}),(0,r.jsx)(x.ZP,{item:!0,xs:12,children:(0,r.jsxs)(S,{label:"Industry",value:F.industry,onChange:e=>N("industry",e.target.value,"companyDetails"),error:O.industry,helperText:O.industry,select:!0,children:[(0,r.jsx)(m.Z,{value:"Technology",children:"Technology"}),(0,r.jsx)(m.Z,{value:"Healthcare",children:"Healthcare"}),(0,r.jsx)(m.Z,{value:"Finance",children:"Finance"}),(0,r.jsx)(m.Z,{value:"Education",children:"Education"}),(0,r.jsx)(m.Z,{value:"Insurance Broker",children:"Insurance Broker"}),(0,r.jsx)(m.Z,{value:"Other",children:"Other"})]})}),(0,r.jsx)(x.ZP,{item:!0,xs:12,children:(0,r.jsx)(S,{label:"Country",value:F.location,onChange:e=>N("location",e.target.value,"companyDetails"),error:O.location,helperText:O.location,select:!0,children:(0,r.jsx)(m.Z,{value:"United States",children:"United States"})})}),(0,r.jsx)(x.ZP,{item:!0,xs:12,children:(0,r.jsxs)(S,{label:"How did you hear about us",value:F.howHeard,onChange:e=>N("howHeard",e.target.value,"companyDetails"),error:O.howHeard,helperText:O.howHeard,select:!0,children:[(0,r.jsx)(m.Z,{value:"Referral",children:"Referral"}),(0,r.jsx)(m.Z,{value:"Social Media",children:"Social Media"}),(0,r.jsx)(m.Z,{value:"Search Engine",children:"Search Engine"}),(0,r.jsx)(m.Z,{value:"Advertisement",children:"Advertisement"}),(0,r.jsx)(m.Z,{value:"Other",children:"Other"})]})}),(0,r.jsx)(x.ZP,{item:!0,xs:12,children:(0,r.jsx)(S,{label:"Referral Source",value:F.referralSource,onChange:e=>N("referralSource",e.target.value,"companyDetails"),error:O.referralSource,helperText:O.referralSource,placeholder:"LinkedIn, Twitter, etc."})})]})}),(0,r.jsx)(d.Z,{sx:{mt:4},children:k?(0,r.jsx)(h.Z,{size:24,sx:{color:"#ffffff"}}):(0,r.jsx)(f.Z,{variant:"contained",color:"primary",fullWidth:!0,sx:{background:"linear-gradient(90deg, #7206E6, #B54BFF)",padding:"12px",fontSize:"16px",textTransform:"none",borderRadius:2,mb:10},onClick:W,children:"Confirm Details"})})]})]})})},18761:function(e,t,n){"use strict";n.d(t,{Z:function(){return B}});var r=n(57437),a=n(93062),i=n(71495),o=n(71004),l=n(59832),s=n(92253),c=n(61910),d=n(95656),u=n(94013),h=n(46387),f=n(8350),x=n(15273),m=n(73261),p=n(11741),g=n(53431),b=n(67051),j=n(33145),y=n(83337),v=n(2265),Z=n(39547),S=n(70623),C=n(99376),w=n(56336),A=n(68575),k=n(31175),T=n(46837),I=n(47369),P=n(15116),R=n(47723);let E="75vw";var F=()=>{let e=(0,y.T)(),t=(0,C.useRouter)();(0,C.usePathname)();let{logout:n}=(0,I.a)(),a=(0,y.C)(e=>e.company.companyBenefitTypes);(0,y.C)(e=>e.user.selectedBenefitType);let i=(0,A.v9)(e=>(0,S.MP)(e));(0,v.useEffect)(()=>{i&&(0,Z.N)(e,i)},[i,e]);let[o,l]=(0,v.useState)(!1);(0,v.useEffect)(()=>{l("true"===localStorage.getItem("isTeamsApp1"))},[]);let c=n=>{e((0,S.v2)(n)),t.push("/viewBenefitsByType/".concat(n))};return(0,r.jsxs)(s.ZP,{sx:{width:E,height:"100vh",flexShrink:0,"& .MuiDrawer-paper":{width:E,boxSizing:"border-box",bgcolor:"#ffffff",position:"relative"}},variant:"permanent",anchor:"left",children:[(0,r.jsxs)(d.Z,{sx:{padding:0,height:"100%",position:"relative",bgcolor:"#ffffff"},children:[(0,r.jsx)(d.Z,{sx:{mx:2,mt:2,px:1,py:.5,borderRadius:2,position:"relative","&:hover":{backgroundColor:"#f0f0f0"},bgcolor:"#F5F6FA"},children:(0,r.jsxs)(u.Z,{variant:"text",sx:{width:"100%",borderRadius:2,bgcolor:"#F5F6FA",color:"#333",fontWeight:"medium",fontSize:"1rem",textTransform:"none","&:hover":{backgroundColor:"#f0f0f0"},display:"flex",alignItems:"center",justifyContent:"flex-start"},onClick:()=>{t.push("/mobile/dashboard"),e((0,R.dL)())},children:[(0,r.jsx)(T.Z,{sx:{mr:1}}),"Home"]})}),(0,r.jsx)(h.Z,{sx:{mt:2,fontWeight:500,paddingX:2.5,fontSize:"1.2rem",color:"black"},children:"My Benefits"}),(0,r.jsx)(h.Z,{sx:{fontWeight:500,paddingX:2.5,paddingY:1,fontSize:".7rem",color:"rgba(0, 0, 0, 0.4)"},children:"SELECT ANY TO VIEW"}),(0,r.jsx)(f.Z,{sx:{my:1}}),(0,r.jsx)(x.Z,{children:a.length>0?a.map(t=>(0,r.jsx)(m.ZP,{disablePadding:!0,children:(0,r.jsxs)(p.Z,{onClick:()=>{c(t),e((0,R.dL)())},sx:{borderRadius:2,position:"relative","&:hover":{backgroundColor:"#f0f0f0"},bgcolor:"#F5F6FA",mx:2,mt:2},children:[(0,r.jsx)(g.Z,{sx:{minWidth:0,mr:2,pt:.5},children:(0,k.RS)(t)}),(0,r.jsx)(b.Z,{primary:t,sx:{fontWeight:"medium",color:"#333",fontSize:"1rem"}})]})},t)):(0,r.jsx)(h.Z,{variant:"body1",sx:{color:"#999",padding:2.5},children:"No benefits available at the moment"})})]}),!o&&(0,r.jsxs)(d.Z,{sx:{display:"flex",alignItems:"center",justifyContent:"center",bgcolor:"#F5F6FA",borderRadius:"30px",padding:"10px 20px",cursor:"pointer",position:"absolute",bottom:"50px",left:"50%",transform:"translateX(-50%)",width:"calc(100% - 40px)"},onClick:()=>{t.push("/qHarmonyBot"),e((0,R.dL)())},children:[(0,r.jsx)(j.default,{src:w.Z,alt:"AI Chat",style:{borderRadius:"100%",width:"40px",height:"40px",marginRight:"10px"}}),(0,r.jsxs)(d.Z,{children:[(0,r.jsx)(h.Z,{variant:"body1",sx:{fontWeight:"bold"},children:"Chat with Brea"}),(0,r.jsx)(h.Z,{variant:"body2",sx:{color:"#6c757d"},children:"24/7 available"})]})]}),(0,r.jsxs)(u.Z,{onClick:n,sx:{backgroundColor:"transparent",color:"#333",marginBottom:"5px",textTransform:"none",padding:"8px 16px",display:"flex",alignItems:"center",justifyContent:"center",gap:"8px","&:hover":{backgroundColor:"transparent",color:"#555"},boxShadow:"none"},children:[(0,r.jsx)(P.Z,{sx:{fontSize:"18px"}}),(0,r.jsx)(h.Z,{sx:{fontWeight:500,fontSize:"14px"},children:"Logout"})]})]})},B=e=>{let t=t=>{let n=(0,A.I0)(),u=(0,y.C)(e=>e.mobileSidebarToggle.isOpen),h=(0,C.usePathname)();return(0,r.jsxs)(d.Z,{children:[(0,r.jsx)(a.ZP,{}),!("/"===h||"/onboard"===h)&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(i.Z,{position:"static",sx:{backgroundColor:"black"},children:(0,r.jsx)(o.Z,{sx:{mb:"/mobile/dashboard"===h?6:0},children:(0,r.jsx)(l.Z,{edge:"start",color:"inherit","aria-label":"menu",onClick:()=>n((0,R.FJ)()),children:(0,r.jsx)(c.Z,{fontSize:"large"})})})}),(0,r.jsx)(s.ZP,{anchor:"left",open:u,onClose:()=>n((0,R.dL)()),children:(0,r.jsx)(F,{})})]}),(0,r.jsx)(e,{...t})]})};return t.displayName="WithMobileEdgeFill(".concat(e.displayName||e.name||"Component",")"),t}},47723:function(e,t,n){"use strict";n.d(t,{FJ:function(){return a},dL:function(){return i}});let r=(0,n(39129).oM)({name:"drawer",initialState:{isOpen:!1},reducers:{openDrawer:e=>{e.isOpen=!0},closeDrawer:e=>{e.isOpen=!1},toggleDrawer:e=>{e.isOpen=!e.isOpen}}}),{openDrawer:a,closeDrawer:i,toggleDrawer:o}=r.actions;t.ZP=r.reducer},80944:function(e,t){"use strict";t.Z={src:"/_next/static/media/logo.770bfeee.png",height:300,width:300,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAYAAADED76LAAAA9ElEQVR42i2PMUoDQRiF3z+zu7MbFTaxkfUC0St4ACWdhbbapvACCnaCpU0a09jYiQewFj2AIljY2AoJ7LKZnc3s/P6EPPiq9+DxESR359UgBHNrGxTWQTcOPwtrLwGU6n5c7ySxeQb4vWn9WS/lky50JVH6UlrVjzTFE2iebvbwmJkE37/zPLB79SHfzWIzUUph0LrqScB4qpFEdEhICxm9BeY9BYA9kxJwfTw7IEKfGUsAq06FgNlGtjUSoDTvS1mB3B/BDInoM/KhvQhd8lDb5RGz/pDLVFM+inVc1L49pbXmtmjeiOZQNCGaX4vGXQGY/wM1tG/NQnnUIwAAAABJRU5ErkJggg==",blurWidth:8,blurHeight:8}}},function(e){e.O(0,[139,3463,3301,8575,293,9810,187,3145,9932,3919,9129,2786,9826,8166,8760,9414,7404,6757,3344,9662,2971,2117,1744],function(){return e(e.s=27335)}),_N_E=e.O()}]);