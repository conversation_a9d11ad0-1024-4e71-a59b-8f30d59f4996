(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4359],{8877:function(e,t,n){Promise.resolve().then(n.bind(n,25541))},40256:function(e,t,n){"use strict";n.d(t,{$R:function(){return d},A_:function(){return i},BO:function(){return s},GH:function(){return u},_n:function(){return r},be:function(){return a},iG:function(){return c},j0:function(){return l}});var o=n(83464);let r="http://localhost:8080",s="<EMAIL>,<EMAIL>,<EMAIL>".split(",").map(e=>e.trim()),a=o.Z.create({baseURL:r});async function i(e,t,n){let o=new URL(n?"".concat(n).concat(e):"".concat(r).concat(e));return t&&Object.keys(t).forEach(e=>o.searchParams.append(e,t[e])),(await a.get(o.toString())).data}async function l(e,t,n){let o=n?"".concat(n).concat(e):"".concat(r).concat(e),s=await a.post(o,t,{headers:{"Content-Type":"application/json"}});return{status:s.status,data:s.data}}async function c(e,t,n){let o=n?"".concat(n).concat(e):"".concat(r).concat(e);console.log("Document upload to: ".concat(o));let s=await a.post(o,t,{headers:{"Content-Type":"multipart/form-data"}});return{status:s.status,data:s.data}}async function d(e,t,n){let o=new URL(n?"".concat(n).concat(e):"".concat(r).concat(e));return t&&Object.keys(t).forEach(e=>o.searchParams.append(e,t[e])),console.log("GET Blob request to: ".concat(o.toString())),(await a.get(o.toString(),{responseType:"blob"})).data}async function u(e,t,n){let o=n?"".concat(n).concat(e):"".concat(r).concat(e),s=await a.put(o,t,{headers:{"Content-Type":"application/json"}});return{status:s.status,data:s.data}}a.interceptors.request.use(e=>{let t=localStorage.getItem("userid1")||localStorage.getItem("userId");return t?e.headers["user-id"]=t:console.warn("No user ID found in localStorage for API request"),e})},25541:function(e,t,n){"use strict";n.r(t);var o=n(45008),r=n(57437),s=n(2265),a=n(68575),i=n(73261),l=n(67051),c=n(46387),d=n(95656),u=n(15273),f=n(67116),p=n(94013),h=n(97404),x=n(76792),g=n(27345),m=n(3146),b=n(70623),y=n(48223),j=n(56336),w=n(33145),S=n(18761);function Z(){let e=(0,o._)(["\n    0% { content: ''; }\n    25% { content: '.'; }\n    50% { content: '..'; }\n    75% { content: '...'; }\n    100% { content: ''; }\n  "]);return Z=function(){return e},e}t.default=(0,S.Z)(()=>{let e=(0,a.I0)(),t=(0,a.v9)(e=>(0,b.MP)(e)),n=(0,a.v9)(e=>e.user._id),o=(0,a.v9)(e=>e.user.userProfile),S=(0,a.v9)(e=>e.user.selectedFAQQuestion),v=(0,a.v9)(e=>e.qHarmonyBot.chatHistory),I=(0,a.v9)(e=>e.qHarmonyBot.isLoading),[C,k]=(0,s.useState)(""),R=(0,s.useRef)(null),E=o=>{if(""===o.trim())return;let r={sender:"user",message:o.replace(/\n/g,"<br/>"),timestamp:new Date().toISOString()};e((0,x.Hz)(r)),e((0,x.wt)(!0)),(0,g.b)(e,o,n,t),k("")},T=e=>{if(!e)return"";let[t,n]=e.split(" ");return"".concat(t[0].toUpperCase()).concat(n?n[0].toUpperCase():"")},O=()=>{var e;null===(e=R.current)||void 0===e||e.scrollIntoView({behavior:"smooth"})};(0,s.useEffect)(()=>{S&&(E(S),e((0,b.ki)()))},[S,e]),(0,s.useEffect)(()=>{0===v.length&&o.name&&!S&&(e((0,x.wt)(!0)),setTimeout(()=>{let t={sender:"bot",message:"Hey ".concat(o.name,", how can I help you today?"),timestamp:new Date().toISOString()};e((0,x.Hz)(t)),e((0,x.wt)(!1))},2e3))},[v.length,o.name,e,S]),(0,s.useEffect)(()=>{O()},[v]);let P=(0,m.F4)(Z()),D=["View My Benefits","Enroll Now","Check Time Off","Update My Elections"];return(0,r.jsx)(y.Z,{children:(0,r.jsxs)(d.Z,{component:"main",sx:{flexGrow:1,display:"flex",flexDirection:"column",height:"95vh",bgcolor:"#f6f8fc"},children:[(0,r.jsx)(c.Z,{variant:"h5",sx:{fontWeight:"bold",p:2},children:"Brea - Your Round-the-Clock Benefits Expert"}),(0,r.jsx)(d.Z,{sx:{flexGrow:1,overflow:"auto"},children:(0,r.jsxs)(u.Z,{children:[v.map((e,t)=>(0,r.jsxs)(i.ZP,{sx:{display:"flex",flexDirection:"user"===e.sender?"row-reverse":"row",alignItems:"flex-start"},children:[(0,r.jsx)(f.Z,{sx:{bgcolor:"user"===e.sender?"#000":"transparent",mr:"user"===e.sender?0:2,ml:"user"===e.sender?2:0,mt:.5},children:"user"===e.sender?(0,r.jsx)(f.Z,{sx:{bgcolor:"black",color:"#ffffff",width:35,height:35,fontSize:"1.2rem",mr:1.5,ml:1.5,display:"flex",alignItems:"center",justifyContent:"center",paddingBottom:"1px",fontWeight:800},children:T(o.name)}):(0,r.jsx)(w.default,{src:j.Z,alt:"AI Assistant",style:{borderRadius:"50%",width:"40px",height:"40px"}})}),(0,r.jsxs)(d.Z,{sx:{maxWidth:"80%"},children:[(0,r.jsx)(l.Z,{primary:(0,r.jsx)("span",{dangerouslySetInnerHTML:{__html:"bot"===e.sender?"".concat(e.message,'<br/><small style="color: gray;">AI-generated content—verify before use.</small>'):e.message},style:{whiteSpace:"pre-wrap",wordBreak:"break-word"}}),sx:{bgcolor:"user"===e.sender?"#000":"#fff",color:"user"===e.sender?"#fff":"#000",borderRadius:"user"===e.sender?"16px 16px 4px 16px":"16px 16px 16px 4px",padding:"10px",mb:1}}),"bot"===e.sender&&e.message.includes("how can I help you today?")&&t===v.length-1&&(0,r.jsx)(d.Z,{sx:{display:"flex",mt:1},children:D.map(e=>(0,r.jsx)(p.Z,{variant:"contained",sx:{mr:1,textTransform:"none",fontSize:"12px",borderRadius:"6px",px:2,py:1,bgcolor:"black",color:"white"},onClick:()=>E(e),children:e},e))})]})]},t)),I&&(0,r.jsx)(()=>(0,r.jsxs)(i.ZP,{sx:{display:"flex",alignItems:"flex-start"},children:[(0,r.jsx)(w.default,{src:j.Z,alt:"AI Chat",style:{borderRadius:"50%",width:"40px",height:"40px",marginRight:"10px"}}),(0,r.jsx)(l.Z,{primary:(0,r.jsx)(c.Z,{component:"span",sx:{display:"inline-block",fontSize:"16px","&::after":{content:"''",animation:"".concat(P," 1.5s infinite"),display:"inline-block",width:"16px"}},children:"Brea is typing"}),sx:{bgcolor:"#fff",borderRadius:"20px",p:1.5,mb:1,maxWidth:"80%",mt:-.5}})]}),{}),(0,r.jsx)("div",{ref:R})]})}),(0,r.jsx)(d.Z,{sx:{p:2,borderTop:"1px solid #e0e0e0"},children:(0,r.jsxs)(d.Z,{sx:{display:"flex",alignItems:"flex-start",bgcolor:"#fff",borderRadius:"20px",height:"150px",boxShadow:"0 1px 3px rgba(0, 0, 0, 0.1)"},children:[(0,r.jsx)(h.Z,{fullWidth:!0,variant:"outlined",placeholder:"Type your message...",value:C,onChange:e=>k(e.target.value),onKeyPress:e=>{"Enter"!==e.key||e.shiftKey||(e.preventDefault(),E(C))},multiline:!0,rows:5,sx:{mr:2,borderRadius:"20px",fontSize:"16px",height:"150px","& .MuiOutlinedInput-root":{padding:"12px",border:"none",display:"flex",alignItems:"flex-start"},"& .MuiOutlinedInput-notchedOutline":{border:"none"}}}),(0,r.jsx)(p.Z,{variant:"contained",sx:{bgcolor:C?"#1073ff":"#e0e0e0",color:"#fff",borderRadius:"25px",m:2,p:0,fontSize:"16px",height:"45px",width:"60px",boxShadow:"none",textTransform:"none","&:hover":{bgcolor:C?"#005bb5":"#d0d0d0"}},onClick:()=>E(C),children:"Send"})]})})]})})})},48223:function(e,t,n){"use strict";var o=n(57437),r=n(2265),s=n(47369),a=n(99376),i=n(83337),l=n(70623),c=n(39547),d=n(35389),u=n(95656);let f=()=>/Mobi|Android/i.test(navigator.userAgent);t.Z=e=>{let{children:t}=e,{user:n,loading:p}=(0,s.a)(),h=(0,a.useRouter)(),x=(0,a.usePathname)(),g=(0,i.T)(),[m,b]=(0,r.useState)(!1),y=(0,i.C)(e=>e.user.userProfile);return((0,r.useEffect)(()=>{{let e=localStorage.getItem("userid1")||localStorage.getItem("userId");console.log("USER ID FROM LOCAL STORAGE: ",e),e&&!y.name&&(g((0,l.Iv)(e)),(async()=>{try{await (0,c.M_)(g,e),await (0,c.aK)(g)}catch(e){console.error("Error fetching user data in ProtectedRoute:",e)}})())}},[g,y.name]),(0,r.useEffect)(()=>{console.log("ProtectedRoute useEffect triggered"),console.log("Current user: ",n),console.log("Loading state: ",p),console.log("Current user details: ",y),p||n||(console.log("User not authenticated, redirecting to home"),b(!1),h.push("/")),!p&&y.companyId&&""===y.companyId&&(console.log("Waiting to retrieve company details"),b(!1)),!p&&y.companyId&&""!==y.companyId&&(console.log("User found, rendering children"),b(!0)),f()&&!x.startsWith("/mobile")&&(console.log("Redirecting to mobile version of ".concat(x)),h.push("/mobile".concat(x)))},[n,p,y,h,x]),m)?n?(0,o.jsx)(o.Fragment,{children:t}):null:(0,o.jsx)(u.Z,{sx:{display:"flex",justifyContent:"center",alignItems:"center",height:"100vh",bgcolor:"#f6f8fc"},children:(0,o.jsx)(d.Z,{})})}},18761:function(e,t,n){"use strict";n.d(t,{Z:function(){return H}});var o=n(57437),r=n(93062),s=n(71495),a=n(71004),i=n(59832),l=n(92253),c=n(61910),d=n(95656),u=n(94013),f=n(46387),p=n(8350),h=n(15273),x=n(73261),g=n(11741),m=n(53431),b=n(67051),y=n(33145),j=n(83337),w=n(2265),S=n(39547),Z=n(70623),v=n(99376),I=n(56336),C=n(68575),k=n(31175),R=n(46837),E=n(47369),T=n(15116),O=n(47723);let P="75vw";var D=()=>{let e=(0,j.T)(),t=(0,v.useRouter)();(0,v.usePathname)();let{logout:n}=(0,E.a)(),r=(0,j.C)(e=>e.company.companyBenefitTypes);(0,j.C)(e=>e.user.selectedBenefitType);let s=(0,C.v9)(e=>(0,Z.MP)(e));(0,w.useEffect)(()=>{s&&(0,S.N)(e,s)},[s,e]);let[a,i]=(0,w.useState)(!1);(0,w.useEffect)(()=>{i("true"===localStorage.getItem("isTeamsApp1"))},[]);let c=n=>{e((0,Z.v2)(n)),t.push("/viewBenefitsByType/".concat(n))};return(0,o.jsxs)(l.ZP,{sx:{width:P,height:"100vh",flexShrink:0,"& .MuiDrawer-paper":{width:P,boxSizing:"border-box",bgcolor:"#ffffff",position:"relative"}},variant:"permanent",anchor:"left",children:[(0,o.jsxs)(d.Z,{sx:{padding:0,height:"100%",position:"relative",bgcolor:"#ffffff"},children:[(0,o.jsx)(d.Z,{sx:{mx:2,mt:2,px:1,py:.5,borderRadius:2,position:"relative","&:hover":{backgroundColor:"#f0f0f0"},bgcolor:"#F5F6FA"},children:(0,o.jsxs)(u.Z,{variant:"text",sx:{width:"100%",borderRadius:2,bgcolor:"#F5F6FA",color:"#333",fontWeight:"medium",fontSize:"1rem",textTransform:"none","&:hover":{backgroundColor:"#f0f0f0"},display:"flex",alignItems:"center",justifyContent:"flex-start"},onClick:()=>{t.push("/mobile/dashboard"),e((0,O.dL)())},children:[(0,o.jsx)(R.Z,{sx:{mr:1}}),"Home"]})}),(0,o.jsx)(f.Z,{sx:{mt:2,fontWeight:500,paddingX:2.5,fontSize:"1.2rem",color:"black"},children:"My Benefits"}),(0,o.jsx)(f.Z,{sx:{fontWeight:500,paddingX:2.5,paddingY:1,fontSize:".7rem",color:"rgba(0, 0, 0, 0.4)"},children:"SELECT ANY TO VIEW"}),(0,o.jsx)(p.Z,{sx:{my:1}}),(0,o.jsx)(h.Z,{children:r.length>0?r.map(t=>(0,o.jsx)(x.ZP,{disablePadding:!0,children:(0,o.jsxs)(g.Z,{onClick:()=>{c(t),e((0,O.dL)())},sx:{borderRadius:2,position:"relative","&:hover":{backgroundColor:"#f0f0f0"},bgcolor:"#F5F6FA",mx:2,mt:2},children:[(0,o.jsx)(m.Z,{sx:{minWidth:0,mr:2,pt:.5},children:(0,k.RS)(t)}),(0,o.jsx)(b.Z,{primary:t,sx:{fontWeight:"medium",color:"#333",fontSize:"1rem"}})]})},t)):(0,o.jsx)(f.Z,{variant:"body1",sx:{color:"#999",padding:2.5},children:"No benefits available at the moment"})})]}),!a&&(0,o.jsxs)(d.Z,{sx:{display:"flex",alignItems:"center",justifyContent:"center",bgcolor:"#F5F6FA",borderRadius:"30px",padding:"10px 20px",cursor:"pointer",position:"absolute",bottom:"50px",left:"50%",transform:"translateX(-50%)",width:"calc(100% - 40px)"},onClick:()=>{t.push("/qHarmonyBot"),e((0,O.dL)())},children:[(0,o.jsx)(y.default,{src:I.Z,alt:"AI Chat",style:{borderRadius:"100%",width:"40px",height:"40px",marginRight:"10px"}}),(0,o.jsxs)(d.Z,{children:[(0,o.jsx)(f.Z,{variant:"body1",sx:{fontWeight:"bold"},children:"Chat with Brea"}),(0,o.jsx)(f.Z,{variant:"body2",sx:{color:"#6c757d"},children:"24/7 available"})]})]}),(0,o.jsxs)(u.Z,{onClick:n,sx:{backgroundColor:"transparent",color:"#333",marginBottom:"5px",textTransform:"none",padding:"8px 16px",display:"flex",alignItems:"center",justifyContent:"center",gap:"8px","&:hover":{backgroundColor:"transparent",color:"#555"},boxShadow:"none"},children:[(0,o.jsx)(T.Z,{sx:{fontSize:"18px"}}),(0,o.jsx)(f.Z,{sx:{fontWeight:500,fontSize:"14px"},children:"Logout"})]})]})},H=e=>{let t=t=>{let n=(0,C.I0)(),u=(0,j.C)(e=>e.mobileSidebarToggle.isOpen),f=(0,v.usePathname)();return(0,o.jsxs)(d.Z,{children:[(0,o.jsx)(r.ZP,{}),!("/"===f||"/onboard"===f)&&(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)(s.Z,{position:"static",sx:{backgroundColor:"black"},children:(0,o.jsx)(a.Z,{sx:{mb:"/mobile/dashboard"===f?6:0},children:(0,o.jsx)(i.Z,{edge:"start",color:"inherit","aria-label":"menu",onClick:()=>n((0,O.FJ)()),children:(0,o.jsx)(c.Z,{fontSize:"large"})})})}),(0,o.jsx)(l.ZP,{anchor:"left",open:u,onClose:()=>n((0,O.dL)()),children:(0,o.jsx)(D,{})})]}),(0,o.jsx)(e,{...t})]})};return t.displayName="WithMobileEdgeFill(".concat(e.displayName||e.name||"Component",")"),t}},27345:function(e,t,n){"use strict";n.d(t,{b:function(){return s}});var o=n(76792);let r=(0,n(61103).GU)();async function s(e,t,n,s){let a={user_id:n,user_message:t,team_id:s};try{console.log("Sending chat message:",a);let t=await fetch("".concat(r,"/chat"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(a)});if(!t.body)throw Error("Readable stream not supported");let n=t.body.getReader(),s=new TextDecoder("utf-8"),i={sender:"bot",message:"",timestamp:new Date().toISOString()};for(e((0,o.wt)(!0));;){let{done:t,value:r}=await n.read();if(t)break;let a=s.decode(r,{stream:!0});e((0,o.wt)(!1)),console.log("Chunk:",a),i.message+=a,e((0,o.Hz)({sender:"bot",message:a,timestamp:new Date().toISOString()}))}}catch(n){console.error("Error sending chat message:",n);let t={sender:"bot",message:"Sorry, I encountered an error while processing your request.",timestamp:new Date().toISOString()};e((0,o.Hz)(t)),e((0,o.wt)(!1))}}},47723:function(e,t,n){"use strict";n.d(t,{FJ:function(){return r},dL:function(){return s}});let o=(0,n(39129).oM)({name:"drawer",initialState:{isOpen:!1},reducers:{openDrawer:e=>{e.isOpen=!0},closeDrawer:e=>{e.isOpen=!1},toggleDrawer:e=>{e.isOpen=!e.isOpen}}}),{openDrawer:r,closeDrawer:s,toggleDrawer:a}=o.actions;t.ZP=o.reducer},76792:function(e,t,n){"use strict";n.d(t,{Hz:function(){return r},wt:function(){return a}});let o=(0,n(39129).oM)({name:"qHarmonyBot",initialState:{chatHistory:[],isLoading:!1},reducers:{addMessage:(e,t)=>{let{sender:n,message:o,timestamp:r}=t.payload;if(console.log("Adding message:",t.payload),"bot"===n&&e.chatHistory.length>0){let t=e.chatHistory[e.chatHistory.length-1];if("bot"===t.sender&&!t.timestamp.includes("Done")){t.message+=o,t.timestamp=r;return}}e.chatHistory.push(t.payload)},clearChatHistory:e=>{e.chatHistory=[]},setIsLoading:(e,t)=>{e.isLoading=t.payload}}}),{addMessage:r,clearChatHistory:s,setIsLoading:a}=o.actions;t.ZP=o.reducer},61103:function(e,t,n){"use strict";n.d(t,{GU:function(){return s},bR:function(){return o},n5:function(){return r}});let o=()=>"http://localhost:8080",r=()=>{let e="userid1",t="userId",n=localStorage.getItem(e)||localStorage.getItem(t);return(console.log("\uD83D\uDD0D getUserId debug:",{primaryKey:e,altKey:t,primaryValue:localStorage.getItem(e),altValue:localStorage.getItem(t),finalUserId:n}),n)?n:(console.error("❌ User ID not found in localStorage"),"default-user")},s=()=>"https://bot.benosphere.com"}},function(e){e.O(0,[139,3463,3301,8575,8685,187,1423,9932,3919,9129,2786,9826,8166,8760,7404,8544,3344,9662,2971,2117,1744],function(){return e(e.s=8877)}),_N_E=e.O()}]);