(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8717],{99312:function(e,t,n){Promise.resolve().then(n.bind(n,21114))},40256:function(e,t,n){"use strict";n.d(t,{$R:function(){return d},A_:function(){return c},BO:function(){return i},GH:function(){return u},_n:function(){return r},be:function(){return a},iG:function(){return l},j0:function(){return s}});var o=n(83464);let r="http://localhost:8080",i="<EMAIL>,<EMAIL>,<EMAIL>".split(",").map(e=>e.trim()),a=o.Z.create({baseURL:r});async function c(e,t,n){let o=new URL(n?"".concat(n).concat(e):"".concat(r).concat(e));return t&&Object.keys(t).forEach(e=>o.searchParams.append(e,t[e])),(await a.get(o.toString())).data}async function s(e,t,n){let o=n?"".concat(n).concat(e):"".concat(r).concat(e),i=await a.post(o,t,{headers:{"Content-Type":"application/json"}});return{status:i.status,data:i.data}}async function l(e,t,n){let o=n?"".concat(n).concat(e):"".concat(r).concat(e);console.log("Document upload to: ".concat(o));let i=await a.post(o,t,{headers:{"Content-Type":"multipart/form-data"}});return{status:i.status,data:i.data}}async function d(e,t,n){let o=new URL(n?"".concat(n).concat(e):"".concat(r).concat(e));return t&&Object.keys(t).forEach(e=>o.searchParams.append(e,t[e])),console.log("GET Blob request to: ".concat(o.toString())),(await a.get(o.toString(),{responseType:"blob"})).data}async function u(e,t,n){let o=n?"".concat(n).concat(e):"".concat(r).concat(e),i=await a.put(o,t,{headers:{"Content-Type":"application/json"}});return{status:i.status,data:i.data}}a.interceptors.request.use(e=>{let t=localStorage.getItem("userid1")||localStorage.getItem("userId");return t?e.headers["user-id"]=t:console.warn("No user ID found in localStorage for API request"),e})},21114:function(e,t,n){"use strict";n.r(t);var o=n(57437),r=n(2265),i=n(83337),a=n(7022),c=n(99376),s=n(95656),l=n(46387),d=n(89414),u=n(35389),f=n(94013),p=n(68575),g=n(39124),m=n(70623),h=n(88506),x=n(48223),b=n(18761),y=n(39547);t.default=(0,b.Z)(()=>{var e;(0,c.useRouter)();let t=(0,i.T)(),{benefitId:n}=(0,c.useParams)(),b=(0,p.v9)(e=>(0,m.MP)(e)),j=(0,i.C)(e=>e.benefits.documentsPerBenefit),w=(0,i.C)(e=>e.benefits.viewableDocuments),v=(0,p.v9)(e=>(0,g.d8)(e,n)),Z=(0,i.C)(e=>e.benefits.loadingDocuments);(0,r.useEffect)(()=>{""!==n&&(0,a.v0)(t,n,b,"view_benefits")},[n,b,t]);let E=(e,t)=>{window.open("https://api.benosphere.com/benefits/document?objectKey=".concat(e,"&companyId=").concat(t),"_blank")};return(0,o.jsx)(x.Z,{children:(0,o.jsxs)(s.Z,{sx:{bgcolor:"#F5F6F8",height:"100vh",padding:"32px",overflow:"auto"},children:[(0,o.jsx)(l.Z,{sx:{fontWeight:800,fontSize:"42px",mb:0},children:(0,y.dA)((null==v?void 0:null===(e=v.benefit)||void 0===e?void 0:e.subType)||"")}),(0,o.jsx)(l.Z,{variant:"body1",sx:{color:"#6c757d",mb:6,fontSize:"16px"},children:"You can find all your health insurance details here, including coverage options, policy documents, and claim information."}),(0,o.jsxs)(d.ZP,{container:!0,spacing:3,alignItems:"flex-start",children:[(0,o.jsxs)(d.ZP,{item:!0,xs:12,children:[(0,o.jsx)(l.Z,{sx:{mb:3,fontWeight:700,fontSize:"24px"},children:"☕ Documents"}),0===j.documents.length?(0,o.jsx)(s.Z,{sx:{display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",minHeight:"150px",borderRadius:"8px",border:"2px dashed #e0e0e0",bgcolor:"#f9f9f9",p:4,textAlign:"left",maxWidth:"400px"},children:(0,o.jsx)(l.Z,{variant:"body1",sx:{color:"#6c757d",fontSize:"1rem"},children:"No documents available at the moment."})}):(0,o.jsx)(s.Z,{sx:{display:"grid",gridTemplateColumns:"repeat(auto-fill, minmax(160px, 1fr))",gap:"20px"},children:j.documents.map((e,t)=>{let n=["linear-gradient(135deg, #6a11cb 0%, #2575fc 100%)","linear-gradient(135deg, #ff7e5f 0%, #feb47b 100%)","linear-gradient(135deg, #43cea2 0%, #185a9d 100%)","linear-gradient(135deg, #ffafbd 0%, #ffc3a0 100%)","linear-gradient(135deg, #00c6ff 0%, #0072ff 100%)"],r=n[t%n.length],i=w.find(t=>t.documentObjectKey===e);return(0,o.jsx)(s.Z,{sx:{position:"relative",width:"100%",height:"auto",display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center"},children:(0,o.jsx)(s.Z,{sx:{width:"100%",minHeight:"200px",borderRadius:"12px",overflow:"hidden",boxShadow:"0px 4px 12px rgba(0, 0, 0, 0.1)",position:"relative",display:"flex",alignItems:"center",justifyContent:"center",background:r,color:"#ffffff",cursor:"pointer"},onClick:()=>{(null==i?void 0:i.document)&&(console.log("viewableDocument ===>",i),E(i.documentObjectKey,b))},children:Z.includes(e)||!i?(0,o.jsx)(u.Z,{}):(0,o.jsxs)(s.Z,{children:[(0,o.jsx)(l.Z,{sx:{fontSize:"16px",fontWeight:"bold",textAlign:"center",padding:"8px"},children:(null==i?void 0:i.originalFileName)||"Document Preview"}),(0,o.jsx)(h.Z,{sx:{position:"absolute",top:8,right:8,color:"#ffffff",cursor:"pointer",height:20,width:20},onClick:()=>E(null==i?void 0:i.documentObjectKey,b)})]})})},e)})})]}),(0,o.jsxs)(d.ZP,{item:!0,xs:12,children:[(0,o.jsx)(l.Z,{sx:{mb:3,fontWeight:700,mt:10,fontSize:"17px"},children:"Other helpful links"}),(0,o.jsx)(s.Z,{sx:{borderRadius:"12px",width:"100%",maxWidth:"400px",mx:"auto"},children:0===j.links.length?(0,o.jsx)(s.Z,{sx:{display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",minHeight:"150px",borderRadius:"8px",border:"2px dashed #e0e0e0",bgcolor:"#f9f9f9",py:4,textAlign:"left",width:"100%",maxWidth:"400px",mx:"auto"},children:(0,o.jsx)(l.Z,{variant:"body1",sx:{color:"#6c757d",fontSize:"16px"},children:"No links available right now."})}):(0,o.jsx)(s.Z,{sx:{display:"flex",flexDirection:"column",gap:2},children:j.links.map((e,t)=>(0,o.jsxs)(s.Z,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",width:"100%"},children:[(0,o.jsx)(l.Z,{component:"a",href:e.startsWith("http")?e:"https://".concat(e),target:"_blank",rel:"noopener noreferrer",sx:{color:"#1A7ECF",textDecoration:"none",fontWeight:500,fontSize:"16px",overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap",maxWidth:"80%"},children:e||"Link ".concat(t+1)}),(0,o.jsx)(f.Z,{onClick:()=>{console.log("link ===>",e),window.open(e.startsWith("http")?e:"https://".concat(e),"_blank")},sx:{minWidth:0,padding:0},children:(0,o.jsx)(h.Z,{sx:{color:"#6c757d",marginLeft:2}})})]},t))})})]})]})]})})})},48223:function(e,t,n){"use strict";var o=n(57437),r=n(2265),i=n(47369),a=n(99376),c=n(83337),s=n(70623),l=n(39547),d=n(35389),u=n(95656);let f=()=>/Mobi|Android/i.test(navigator.userAgent);t.Z=e=>{let{children:t}=e,{user:n,loading:p}=(0,i.a)(),g=(0,a.useRouter)(),m=(0,a.usePathname)(),h=(0,c.T)(),[x,b]=(0,r.useState)(!1),y=(0,c.C)(e=>e.user.userProfile);return((0,r.useEffect)(()=>{{let e=localStorage.getItem("userid1")||localStorage.getItem("userId");console.log("USER ID FROM LOCAL STORAGE: ",e),e&&!y.name&&(h((0,s.Iv)(e)),(async()=>{try{await (0,l.M_)(h,e),await (0,l.aK)(h)}catch(e){console.error("Error fetching user data in ProtectedRoute:",e)}})())}},[h,y.name]),(0,r.useEffect)(()=>{console.log("ProtectedRoute useEffect triggered"),console.log("Current user: ",n),console.log("Loading state: ",p),console.log("Current user details: ",y),p||n||(console.log("User not authenticated, redirecting to home"),b(!1),g.push("/")),!p&&y.companyId&&""===y.companyId&&(console.log("Waiting to retrieve company details"),b(!1)),!p&&y.companyId&&""!==y.companyId&&(console.log("User found, rendering children"),b(!0)),f()&&!m.startsWith("/mobile")&&(console.log("Redirecting to mobile version of ".concat(m)),g.push("/mobile".concat(m)))},[n,p,y,g,m]),x)?n?(0,o.jsx)(o.Fragment,{children:t}):null:(0,o.jsx)(u.Z,{sx:{display:"flex",justifyContent:"center",alignItems:"center",height:"100vh",bgcolor:"#f6f8fc"},children:(0,o.jsx)(d.Z,{})})}},18761:function(e,t,n){"use strict";n.d(t,{Z:function(){return T}});var o=n(57437),r=n(93062),i=n(71495),a=n(71004),c=n(59832),s=n(92253),l=n(61910),d=n(95656),u=n(94013),f=n(46387),p=n(8350),g=n(15273),m=n(73261),h=n(11741),x=n(53431),b=n(67051),y=n(33145),j=n(83337),w=n(2265),v=n(39547),Z=n(70623),E=n(99376),I=n(56336),C=n(68575),k=n(31175),S=n(46837),F=n(47369),R=n(15116),M=n(47723);let P="75vw";var O=()=>{let e=(0,j.T)(),t=(0,E.useRouter)();(0,E.usePathname)();let{logout:n}=(0,F.a)(),r=(0,j.C)(e=>e.company.companyBenefitTypes);(0,j.C)(e=>e.user.selectedBenefitType);let i=(0,C.v9)(e=>(0,Z.MP)(e));(0,w.useEffect)(()=>{i&&(0,v.N)(e,i)},[i,e]);let[a,c]=(0,w.useState)(!1);(0,w.useEffect)(()=>{c("true"===localStorage.getItem("isTeamsApp1"))},[]);let l=n=>{e((0,Z.v2)(n)),t.push("/viewBenefitsByType/".concat(n))};return(0,o.jsxs)(s.ZP,{sx:{width:P,height:"100vh",flexShrink:0,"& .MuiDrawer-paper":{width:P,boxSizing:"border-box",bgcolor:"#ffffff",position:"relative"}},variant:"permanent",anchor:"left",children:[(0,o.jsxs)(d.Z,{sx:{padding:0,height:"100%",position:"relative",bgcolor:"#ffffff"},children:[(0,o.jsx)(d.Z,{sx:{mx:2,mt:2,px:1,py:.5,borderRadius:2,position:"relative","&:hover":{backgroundColor:"#f0f0f0"},bgcolor:"#F5F6FA"},children:(0,o.jsxs)(u.Z,{variant:"text",sx:{width:"100%",borderRadius:2,bgcolor:"#F5F6FA",color:"#333",fontWeight:"medium",fontSize:"1rem",textTransform:"none","&:hover":{backgroundColor:"#f0f0f0"},display:"flex",alignItems:"center",justifyContent:"flex-start"},onClick:()=>{t.push("/mobile/dashboard"),e((0,M.dL)())},children:[(0,o.jsx)(S.Z,{sx:{mr:1}}),"Home"]})}),(0,o.jsx)(f.Z,{sx:{mt:2,fontWeight:500,paddingX:2.5,fontSize:"1.2rem",color:"black"},children:"My Benefits"}),(0,o.jsx)(f.Z,{sx:{fontWeight:500,paddingX:2.5,paddingY:1,fontSize:".7rem",color:"rgba(0, 0, 0, 0.4)"},children:"SELECT ANY TO VIEW"}),(0,o.jsx)(p.Z,{sx:{my:1}}),(0,o.jsx)(g.Z,{children:r.length>0?r.map(t=>(0,o.jsx)(m.ZP,{disablePadding:!0,children:(0,o.jsxs)(h.Z,{onClick:()=>{l(t),e((0,M.dL)())},sx:{borderRadius:2,position:"relative","&:hover":{backgroundColor:"#f0f0f0"},bgcolor:"#F5F6FA",mx:2,mt:2},children:[(0,o.jsx)(x.Z,{sx:{minWidth:0,mr:2,pt:.5},children:(0,k.RS)(t)}),(0,o.jsx)(b.Z,{primary:t,sx:{fontWeight:"medium",color:"#333",fontSize:"1rem"}})]})},t)):(0,o.jsx)(f.Z,{variant:"body1",sx:{color:"#999",padding:2.5},children:"No benefits available at the moment"})})]}),!a&&(0,o.jsxs)(d.Z,{sx:{display:"flex",alignItems:"center",justifyContent:"center",bgcolor:"#F5F6FA",borderRadius:"30px",padding:"10px 20px",cursor:"pointer",position:"absolute",bottom:"50px",left:"50%",transform:"translateX(-50%)",width:"calc(100% - 40px)"},onClick:()=>{t.push("/qHarmonyBot"),e((0,M.dL)())},children:[(0,o.jsx)(y.default,{src:I.Z,alt:"AI Chat",style:{borderRadius:"100%",width:"40px",height:"40px",marginRight:"10px"}}),(0,o.jsxs)(d.Z,{children:[(0,o.jsx)(f.Z,{variant:"body1",sx:{fontWeight:"bold"},children:"Chat with Brea"}),(0,o.jsx)(f.Z,{variant:"body2",sx:{color:"#6c757d"},children:"24/7 available"})]})]}),(0,o.jsxs)(u.Z,{onClick:n,sx:{backgroundColor:"transparent",color:"#333",marginBottom:"5px",textTransform:"none",padding:"8px 16px",display:"flex",alignItems:"center",justifyContent:"center",gap:"8px","&:hover":{backgroundColor:"transparent",color:"#555"},boxShadow:"none"},children:[(0,o.jsx)(R.Z,{sx:{fontSize:"18px"}}),(0,o.jsx)(f.Z,{sx:{fontWeight:500,fontSize:"14px"},children:"Logout"})]})]})},T=e=>{let t=t=>{let n=(0,C.I0)(),u=(0,j.C)(e=>e.mobileSidebarToggle.isOpen),f=(0,E.usePathname)();return(0,o.jsxs)(d.Z,{children:[(0,o.jsx)(r.ZP,{}),!("/"===f||"/onboard"===f)&&(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)(i.Z,{position:"static",sx:{backgroundColor:"black"},children:(0,o.jsx)(a.Z,{sx:{mb:"/mobile/dashboard"===f?6:0},children:(0,o.jsx)(c.Z,{edge:"start",color:"inherit","aria-label":"menu",onClick:()=>n((0,M.FJ)()),children:(0,o.jsx)(l.Z,{fontSize:"large"})})})}),(0,o.jsx)(s.ZP,{anchor:"left",open:u,onClose:()=>n((0,M.dL)()),children:(0,o.jsx)(O,{})})]}),(0,o.jsx)(e,{...t})]})};return t.displayName="WithMobileEdgeFill(".concat(e.displayName||e.name||"Component",")"),t}},7022:function(e,t,n){"use strict";n.d(t,{$t:function(){return d},SS:function(){return f},Y0:function(){return a},cd:function(){return u},fH:function(){return p},mH:function(){return g},ov:function(){return l},v0:function(){return c}});var o=n(40256),r=n(39124),i=n(39547);async function a(e,t,n){try{let i=await (0,o.A_)("/benefits/benefit-by-type",{companyId:t,type:n});i&&i.benefits?(console.log("GET BENEFITS FOR TYPE RESPONSE: ",i.benefits),e((0,r.oQ)({benefitType:n,benefits:i.benefits})),e((0,r.nM)("Benefits fetched successfully"))):(console.error("Invalid response format:",i),e((0,r.nM)("Failed to fetch benefits")))}catch(t){console.error("Error fetching benefits:",t),e((0,r.nM)("Error fetching benefits"))}}async function c(e,t,n,i){let a={benefitId:t,page:i};console.log("data",a);let c=await (0,o.A_)("/benefits/one-benefit",a),l={...c,benefitId:t};for(let t of(e((0,r.F5)(l)),c.documents)){let o=decodeURIComponent(t.split("_____")[1]);s(e,t,n,o)}}async function s(e,t,n,i){let a={objectKey:t,companyId:n};console.log("data",a);let c=await (0,o.$R)("/benefits/document",a);if(console.log("VIEW BENEFIT RESPONSE: ",c),c){let n=new Blob([c],{type:"application/pdf"}),o=URL.createObjectURL(n);e((0,r.D7)([{documentObjectKey:t,document:o,originalFileName:i}]))}}let l=async(e,t,n,r,c)=>200===(await (0,o.j0)("/benefits/toggle-benefits/",{benefitId:n,companyId:t,isActivated:r})).status&&(await a(e,t,c),await (0,i.N)(e,t),!0);async function d(e,t,n,i){let a=new FormData;i.forEach(e=>a.append("documents",e)),a.append("companyId",n),a.append("benefitId",t);try{console.log("uploadDocument",a);let c=await (0,o.iG)("/benefits/add/document",a),l=c.data.objectKeys;if(console.log("newObjectKeys",l),200===c.status)return l.forEach((o,a)=>{let c=i[a].name;e((0,r.H_)({benefitId:t,document:o})),s(e,o,n,c)}),e((0,r.nM)("Document added successfully")),!0;return console.error("Error adding document:",c.data.error),e((0,r.nM)("Failed to add document")),!1}catch(t){return console.error("Error adding document:",t),e((0,r.nM)("Error adding document")),!1}}async function u(e,t,n,i){try{let a=await (0,o.j0)("/benefits/delete/document",{benefitId:t,companyId:n,objectKey:i});if(200===a.status)return e((0,r.iH)({benefitId:t,document:i})),e((0,r.nM)("Document deleted successfully")),!0;return console.error("Error deleting document:",a.data.error),e((0,r.nM)("Failed to delete document")),!1}catch(t){return console.error("Error deleting document:",t),e((0,r.nM)("Error deleting document")),!1}}async function f(e,t,n,i){try{let a=await (0,o.j0)("/benefits/add/links",{benefitId:t,companyId:n,urls:[i]});if(200===a.status)return e((0,r.MJ)({benefitId:t,link:i})),e((0,r.nM)("Link added successfully")),!0;return console.error("Error adding link:",a.data.error),e((0,r.nM)("Failed to add link")),!1}catch(t){return console.error("Error adding link:",t),e((0,r.nM)("Error adding link")),!1}}async function p(e,t,n,i){try{let a=await (0,o.j0)("/benefits/delete/link",{benefitId:t,companyId:n,urls:i});if(200===a.status)return e((0,r.Yw)({benefitId:t,link:i})),e((0,r.nM)("Link deleted successfully")),!0;return console.error("Error deleting link:",a.data.error),e((0,r.nM)("Failed to delete link")),!1}catch(t){return console.error("Error deleting link:",t),e((0,r.nM)("Error deleting link")),!1}}async function g(e,t){let n=new FormData;n.append("logoImage",t);try{console.log("uploading company logo",n);let t=await (0,o.iG)("/admin/update-company-logo",n);if(await (0,i.aK)(e),200===t.status)return console.log("Company logo updated successfully"),e((0,r.nM)("Company logo updated successfully")),!0;return console.error("Error updating company logo:",t.data.error),e((0,r.nM)("Failed to update company logo")),!1}catch(t){return console.error("Error updating company logo:",t),e((0,r.nM)("Error updating company logo")),!1}}},47723:function(e,t,n){"use strict";n.d(t,{FJ:function(){return r},dL:function(){return i}});let o=(0,n(39129).oM)({name:"drawer",initialState:{isOpen:!1},reducers:{openDrawer:e=>{e.isOpen=!0},closeDrawer:e=>{e.isOpen=!1},toggleDrawer:e=>{e.isOpen=!e.isOpen}}}),{openDrawer:r,closeDrawer:i,toggleDrawer:a}=o.actions;t.ZP=o.reducer}},function(e){e.O(0,[139,3463,3301,8575,293,9810,187,3145,9932,3919,9129,2786,9826,8166,8760,9414,1906,3344,9662,2971,2117,1744],function(){return e(e.s=99312)}),_N_E=e.O()}]);