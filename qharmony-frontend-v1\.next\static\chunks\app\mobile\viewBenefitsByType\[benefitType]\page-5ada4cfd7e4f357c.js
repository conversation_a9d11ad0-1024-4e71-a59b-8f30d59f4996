(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5866],{57905:function(e,t,n){Promise.resolve().then(n.bind(n,44254))},40256:function(e,t,n){"use strict";n.d(t,{$R:function(){return d},A_:function(){return s},BO:function(){return a},GH:function(){return u},_n:function(){return r},be:function(){return i},iG:function(){return l},j0:function(){return c}});var o=n(83464);let r="http://localhost:8080",a="<EMAIL>,<EMAIL>,<EMAIL>".split(",").map(e=>e.trim()),i=o.Z.create({baseURL:r});async function s(e,t,n){let o=new URL(n?"".concat(n).concat(e):"".concat(r).concat(e));return t&&Object.keys(t).forEach(e=>o.searchParams.append(e,t[e])),(await i.get(o.toString())).data}async function c(e,t,n){let o=n?"".concat(n).concat(e):"".concat(r).concat(e),a=await i.post(o,t,{headers:{"Content-Type":"application/json"}});return{status:a.status,data:a.data}}async function l(e,t,n){let o=n?"".concat(n).concat(e):"".concat(r).concat(e);console.log("Document upload to: ".concat(o));let a=await i.post(o,t,{headers:{"Content-Type":"multipart/form-data"}});return{status:a.status,data:a.data}}async function d(e,t,n){let o=new URL(n?"".concat(n).concat(e):"".concat(r).concat(e));return t&&Object.keys(t).forEach(e=>o.searchParams.append(e,t[e])),console.log("GET Blob request to: ".concat(o.toString())),(await i.get(o.toString(),{responseType:"blob"})).data}async function u(e,t,n){let o=n?"".concat(n).concat(e):"".concat(r).concat(e),a=await i.put(o,t,{headers:{"Content-Type":"application/json"}});return{status:a.status,data:a.data}}i.interceptors.request.use(e=>{let t=localStorage.getItem("userid1")||localStorage.getItem("userId");return t?e.headers["user-id"]=t:console.warn("No user ID found in localStorage for API request"),e})},44254:function(e,t,n){"use strict";n.r(t);var o=n(57437),r=n(2265),a=n(83337),i=n(7022),s=n(70623),c=n(39124),l=n(95656),d=n(46387),u=n(35389),f=n(89414),p=n(67208),g=n(36137),h=n(94013),m=n(99376),x=n(68575),b=n(48223),y=n(31175),j=n(18761),w=n(39547);t.default=(0,j.Z)(()=>{let{benefitType:e}=(0,m.useParams)(),t=decodeURIComponent(e),n=(0,a.T)(),j=(0,m.useRouter)(),v=(0,x.v9)(e=>(0,s.MP)(e)),Z=(0,a.C)(e=>e.user.userProfile),E=(0,x.v9)(e=>(0,c.oT)(e,t));(0,r.useEffect)(()=>{""!==t&&""!==v&&(n((0,c.Yb)()),(0,i.Y0)(n,v,t))},[t,v,n]);let I=e=>{n((0,s.Re)(e)),j.push("/viewBenefitDetails/".concat(e))};return(0,o.jsx)(b.Z,{children:(0,o.jsxs)(l.Z,{sx:{flexGrow:1,minHeight:"100vh",width:"100%",bgcolor:"#F5F6FA",padding:4},children:[(0,o.jsxs)(d.Z,{variant:"h4",sx:{fontWeight:"bold",mb:2},children:["Hey ",Z.name.replace(/\b\w/g,e=>e.toUpperCase()),","]}),(0,o.jsx)(d.Z,{variant:"body1",sx:{color:"#6c757d",mb:4},children:"Explore your benefits now—tap to dive in!"}),(0,o.jsx)(l.Z,{sx:{mb:4},children:null===E?(0,o.jsx)(l.Z,{sx:{display:"flex",flexDirection:"column",alignItems:"flex-start",justifyContent:"center",height:"150px"},children:(0,o.jsx)(u.Z,{size:50,sx:{color:"#6c757d",mb:2}})}):0===E.benefits.filter(e=>e.isActivated).length?(0,o.jsxs)(l.Z,{sx:{display:"flex",flexDirection:"column",alignItems:"flex-start",justifyContent:"center",height:"150px",borderRadius:"12px",backgroundColor:"#F5F6FA",textAlign:"left"},children:[(0,o.jsx)(d.Z,{variant:"h6",sx:{fontWeight:"bold",color:"#6c757d",mb:2},children:"No benefits available"}),(0,o.jsx)(d.Z,{variant:"body2",sx:{color:"#9E9E9E"},children:"There are currently no active benefits for this type."})]}):(0,o.jsx)(f.ZP,{container:!0,spacing:3,children:E.benefits.filter(e=>e.isActivated).map((e,t)=>(0,o.jsx)(f.ZP,{item:!0,xs:12,children:(0,o.jsxs)(p.Z,{sx:{height:"100%",display:"flex",flexDirection:"column",alignItems:"flex-start",justifyContent:"flex-start",borderRadius:"16px",bgcolor:"#ffffff",padding:3,boxShadow:"none"},children:[(0,y.DD)(e.subType,{marginBottom:15,fontSize:35,color:"#606267"}),(0,o.jsxs)(g.Z,{sx:{flexGrow:1,padding:0,width:"100%"},children:[(0,o.jsx)(d.Z,{variant:"h6",sx:{fontWeight:600,textAlign:"left"},children:(0,w.dA)(e.subType)}),(0,o.jsxs)(d.Z,{variant:"body2",sx:{color:"#6c757d",marginTop:1,textAlign:"left"},children:["Check your ",(0,w.dA)(e.subType)," benefits"]})]}),(0,o.jsx)(l.Z,{sx:{paddingTop:2,width:"100%"},children:(0,o.jsx)(h.Z,{variant:"contained",fullWidth:!0,onClick:()=>I(e._id),sx:{textTransform:"none",borderRadius:"8px",bgcolor:"#e8e8e8",color:"black",boxShadow:"none",padding:"10px 0","&:hover":{backgroundColor:"#d8d8d8",boxShadow:"none"}},children:"View"})})]})},e._id))})})]})})})},48223:function(e,t,n){"use strict";var o=n(57437),r=n(2265),a=n(47369),i=n(99376),s=n(83337),c=n(70623),l=n(39547),d=n(35389),u=n(95656);let f=()=>/Mobi|Android/i.test(navigator.userAgent);t.Z=e=>{let{children:t}=e,{user:n,loading:p}=(0,a.a)(),g=(0,i.useRouter)(),h=(0,i.usePathname)(),m=(0,s.T)(),[x,b]=(0,r.useState)(!1),y=(0,s.C)(e=>e.user.userProfile);return((0,r.useEffect)(()=>{{let e=localStorage.getItem("userid1")||localStorage.getItem("userId");console.log("USER ID FROM LOCAL STORAGE: ",e),e&&!y.name&&(m((0,c.Iv)(e)),(async()=>{try{await (0,l.M_)(m,e),await (0,l.aK)(m)}catch(e){console.error("Error fetching user data in ProtectedRoute:",e)}})())}},[m,y.name]),(0,r.useEffect)(()=>{console.log("ProtectedRoute useEffect triggered"),console.log("Current user: ",n),console.log("Loading state: ",p),console.log("Current user details: ",y),p||n||(console.log("User not authenticated, redirecting to home"),b(!1),g.push("/")),!p&&y.companyId&&""===y.companyId&&(console.log("Waiting to retrieve company details"),b(!1)),!p&&y.companyId&&""!==y.companyId&&(console.log("User found, rendering children"),b(!0)),f()&&!h.startsWith("/mobile")&&(console.log("Redirecting to mobile version of ".concat(h)),g.push("/mobile".concat(h)))},[n,p,y,g,h]),x)?n?(0,o.jsx)(o.Fragment,{children:t}):null:(0,o.jsx)(u.Z,{sx:{display:"flex",justifyContent:"center",alignItems:"center",height:"100vh",bgcolor:"#f6f8fc"},children:(0,o.jsx)(d.Z,{})})}},18761:function(e,t,n){"use strict";n.d(t,{Z:function(){return P}});var o=n(57437),r=n(93062),a=n(71495),i=n(71004),s=n(59832),c=n(92253),l=n(61910),d=n(95656),u=n(94013),f=n(46387),p=n(8350),g=n(15273),h=n(73261),m=n(11741),x=n(53431),b=n(67051),y=n(33145),j=n(83337),w=n(2265),v=n(39547),Z=n(70623),E=n(99376),I=n(56336),C=n(68575),S=n(31175),k=n(46837),F=n(47369),R=n(15116),T=n(47723);let M="75vw";var A=()=>{let e=(0,j.T)(),t=(0,E.useRouter)();(0,E.usePathname)();let{logout:n}=(0,F.a)(),r=(0,j.C)(e=>e.company.companyBenefitTypes);(0,j.C)(e=>e.user.selectedBenefitType);let a=(0,C.v9)(e=>(0,Z.MP)(e));(0,w.useEffect)(()=>{a&&(0,v.N)(e,a)},[a,e]);let[i,s]=(0,w.useState)(!1);(0,w.useEffect)(()=>{s("true"===localStorage.getItem("isTeamsApp1"))},[]);let l=n=>{e((0,Z.v2)(n)),t.push("/viewBenefitsByType/".concat(n))};return(0,o.jsxs)(c.ZP,{sx:{width:M,height:"100vh",flexShrink:0,"& .MuiDrawer-paper":{width:M,boxSizing:"border-box",bgcolor:"#ffffff",position:"relative"}},variant:"permanent",anchor:"left",children:[(0,o.jsxs)(d.Z,{sx:{padding:0,height:"100%",position:"relative",bgcolor:"#ffffff"},children:[(0,o.jsx)(d.Z,{sx:{mx:2,mt:2,px:1,py:.5,borderRadius:2,position:"relative","&:hover":{backgroundColor:"#f0f0f0"},bgcolor:"#F5F6FA"},children:(0,o.jsxs)(u.Z,{variant:"text",sx:{width:"100%",borderRadius:2,bgcolor:"#F5F6FA",color:"#333",fontWeight:"medium",fontSize:"1rem",textTransform:"none","&:hover":{backgroundColor:"#f0f0f0"},display:"flex",alignItems:"center",justifyContent:"flex-start"},onClick:()=>{t.push("/mobile/dashboard"),e((0,T.dL)())},children:[(0,o.jsx)(k.Z,{sx:{mr:1}}),"Home"]})}),(0,o.jsx)(f.Z,{sx:{mt:2,fontWeight:500,paddingX:2.5,fontSize:"1.2rem",color:"black"},children:"My Benefits"}),(0,o.jsx)(f.Z,{sx:{fontWeight:500,paddingX:2.5,paddingY:1,fontSize:".7rem",color:"rgba(0, 0, 0, 0.4)"},children:"SELECT ANY TO VIEW"}),(0,o.jsx)(p.Z,{sx:{my:1}}),(0,o.jsx)(g.Z,{children:r.length>0?r.map(t=>(0,o.jsx)(h.ZP,{disablePadding:!0,children:(0,o.jsxs)(m.Z,{onClick:()=>{l(t),e((0,T.dL)())},sx:{borderRadius:2,position:"relative","&:hover":{backgroundColor:"#f0f0f0"},bgcolor:"#F5F6FA",mx:2,mt:2},children:[(0,o.jsx)(x.Z,{sx:{minWidth:0,mr:2,pt:.5},children:(0,S.RS)(t)}),(0,o.jsx)(b.Z,{primary:t,sx:{fontWeight:"medium",color:"#333",fontSize:"1rem"}})]})},t)):(0,o.jsx)(f.Z,{variant:"body1",sx:{color:"#999",padding:2.5},children:"No benefits available at the moment"})})]}),!i&&(0,o.jsxs)(d.Z,{sx:{display:"flex",alignItems:"center",justifyContent:"center",bgcolor:"#F5F6FA",borderRadius:"30px",padding:"10px 20px",cursor:"pointer",position:"absolute",bottom:"50px",left:"50%",transform:"translateX(-50%)",width:"calc(100% - 40px)"},onClick:()=>{t.push("/qHarmonyBot"),e((0,T.dL)())},children:[(0,o.jsx)(y.default,{src:I.Z,alt:"AI Chat",style:{borderRadius:"100%",width:"40px",height:"40px",marginRight:"10px"}}),(0,o.jsxs)(d.Z,{children:[(0,o.jsx)(f.Z,{variant:"body1",sx:{fontWeight:"bold"},children:"Chat with Brea"}),(0,o.jsx)(f.Z,{variant:"body2",sx:{color:"#6c757d"},children:"24/7 available"})]})]}),(0,o.jsxs)(u.Z,{onClick:n,sx:{backgroundColor:"transparent",color:"#333",marginBottom:"5px",textTransform:"none",padding:"8px 16px",display:"flex",alignItems:"center",justifyContent:"center",gap:"8px","&:hover":{backgroundColor:"transparent",color:"#555"},boxShadow:"none"},children:[(0,o.jsx)(R.Z,{sx:{fontSize:"18px"}}),(0,o.jsx)(f.Z,{sx:{fontWeight:500,fontSize:"14px"},children:"Logout"})]})]})},P=e=>{let t=t=>{let n=(0,C.I0)(),u=(0,j.C)(e=>e.mobileSidebarToggle.isOpen),f=(0,E.usePathname)();return(0,o.jsxs)(d.Z,{children:[(0,o.jsx)(r.ZP,{}),!("/"===f||"/onboard"===f)&&(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)(a.Z,{position:"static",sx:{backgroundColor:"black"},children:(0,o.jsx)(i.Z,{sx:{mb:"/mobile/dashboard"===f?6:0},children:(0,o.jsx)(s.Z,{edge:"start",color:"inherit","aria-label":"menu",onClick:()=>n((0,T.FJ)()),children:(0,o.jsx)(l.Z,{fontSize:"large"})})})}),(0,o.jsx)(c.ZP,{anchor:"left",open:u,onClose:()=>n((0,T.dL)()),children:(0,o.jsx)(A,{})})]}),(0,o.jsx)(e,{...t})]})};return t.displayName="WithMobileEdgeFill(".concat(e.displayName||e.name||"Component",")"),t}},7022:function(e,t,n){"use strict";n.d(t,{$t:function(){return d},SS:function(){return f},Y0:function(){return i},cd:function(){return u},fH:function(){return p},mH:function(){return g},ov:function(){return l},v0:function(){return s}});var o=n(40256),r=n(39124),a=n(39547);async function i(e,t,n){try{let a=await (0,o.A_)("/benefits/benefit-by-type",{companyId:t,type:n});a&&a.benefits?(console.log("GET BENEFITS FOR TYPE RESPONSE: ",a.benefits),e((0,r.oQ)({benefitType:n,benefits:a.benefits})),e((0,r.nM)("Benefits fetched successfully"))):(console.error("Invalid response format:",a),e((0,r.nM)("Failed to fetch benefits")))}catch(t){console.error("Error fetching benefits:",t),e((0,r.nM)("Error fetching benefits"))}}async function s(e,t,n,a){let i={benefitId:t,page:a};console.log("data",i);let s=await (0,o.A_)("/benefits/one-benefit",i),l={...s,benefitId:t};for(let t of(e((0,r.F5)(l)),s.documents)){let o=decodeURIComponent(t.split("_____")[1]);c(e,t,n,o)}}async function c(e,t,n,a){let i={objectKey:t,companyId:n};console.log("data",i);let s=await (0,o.$R)("/benefits/document",i);if(console.log("VIEW BENEFIT RESPONSE: ",s),s){let n=new Blob([s],{type:"application/pdf"}),o=URL.createObjectURL(n);e((0,r.D7)([{documentObjectKey:t,document:o,originalFileName:a}]))}}let l=async(e,t,n,r,s)=>200===(await (0,o.j0)("/benefits/toggle-benefits/",{benefitId:n,companyId:t,isActivated:r})).status&&(await i(e,t,s),await (0,a.N)(e,t),!0);async function d(e,t,n,a){let i=new FormData;a.forEach(e=>i.append("documents",e)),i.append("companyId",n),i.append("benefitId",t);try{console.log("uploadDocument",i);let s=await (0,o.iG)("/benefits/add/document",i),l=s.data.objectKeys;if(console.log("newObjectKeys",l),200===s.status)return l.forEach((o,i)=>{let s=a[i].name;e((0,r.H_)({benefitId:t,document:o})),c(e,o,n,s)}),e((0,r.nM)("Document added successfully")),!0;return console.error("Error adding document:",s.data.error),e((0,r.nM)("Failed to add document")),!1}catch(t){return console.error("Error adding document:",t),e((0,r.nM)("Error adding document")),!1}}async function u(e,t,n,a){try{let i=await (0,o.j0)("/benefits/delete/document",{benefitId:t,companyId:n,objectKey:a});if(200===i.status)return e((0,r.iH)({benefitId:t,document:a})),e((0,r.nM)("Document deleted successfully")),!0;return console.error("Error deleting document:",i.data.error),e((0,r.nM)("Failed to delete document")),!1}catch(t){return console.error("Error deleting document:",t),e((0,r.nM)("Error deleting document")),!1}}async function f(e,t,n,a){try{let i=await (0,o.j0)("/benefits/add/links",{benefitId:t,companyId:n,urls:[a]});if(200===i.status)return e((0,r.MJ)({benefitId:t,link:a})),e((0,r.nM)("Link added successfully")),!0;return console.error("Error adding link:",i.data.error),e((0,r.nM)("Failed to add link")),!1}catch(t){return console.error("Error adding link:",t),e((0,r.nM)("Error adding link")),!1}}async function p(e,t,n,a){try{let i=await (0,o.j0)("/benefits/delete/link",{benefitId:t,companyId:n,urls:a});if(200===i.status)return e((0,r.Yw)({benefitId:t,link:a})),e((0,r.nM)("Link deleted successfully")),!0;return console.error("Error deleting link:",i.data.error),e((0,r.nM)("Failed to delete link")),!1}catch(t){return console.error("Error deleting link:",t),e((0,r.nM)("Error deleting link")),!1}}async function g(e,t){let n=new FormData;n.append("logoImage",t);try{console.log("uploading company logo",n);let t=await (0,o.iG)("/admin/update-company-logo",n);if(await (0,a.aK)(e),200===t.status)return console.log("Company logo updated successfully"),e((0,r.nM)("Company logo updated successfully")),!0;return console.error("Error updating company logo:",t.data.error),e((0,r.nM)("Failed to update company logo")),!1}catch(t){return console.error("Error updating company logo:",t),e((0,r.nM)("Error updating company logo")),!1}}},47723:function(e,t,n){"use strict";n.d(t,{FJ:function(){return r},dL:function(){return a}});let o=(0,n(39129).oM)({name:"drawer",initialState:{isOpen:!1},reducers:{openDrawer:e=>{e.isOpen=!0},closeDrawer:e=>{e.isOpen=!1},toggleDrawer:e=>{e.isOpen=!e.isOpen}}}),{openDrawer:r,closeDrawer:a,toggleDrawer:i}=o.actions;t.ZP=o.reducer}},function(e){e.O(0,[139,3463,3301,8575,293,9810,187,3145,9932,3919,9129,2786,9826,8166,8760,9414,2174,3344,9662,2971,2117,1744],function(){return e(e.s=57905)}),_N_E=e.O()}]);