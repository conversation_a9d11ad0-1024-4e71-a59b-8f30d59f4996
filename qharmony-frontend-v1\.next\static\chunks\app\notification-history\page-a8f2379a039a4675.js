(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6042],{53275:function(t,e,r){Promise.resolve().then(r.bind(r,97415))},67116:function(t,e,r){"use strict";r.d(e,{Z:function(){return y}});var o=r(2265),a=r(61994),n=r(20801),i=r(16210),c=r(21086),s=r(37053),l=r(94630),d=r(57437),u=(0,l.Z)((0,d.jsx)("path",{d:"M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"}),"Person"),f=r(94143),h=r(50738);function p(t){return(0,h.ZP)("MuiAvatar",t)}(0,f.Z)("MuiAvatar",["root","colorDefault","circular","rounded","square","img","fallback"]);var g=r(79114);let x=t=>{let{classes:e,variant:r,colorDefault:o}=t;return(0,n.Z)({root:["root",r,o&&"colorDefault"],img:["img"],fallback:["fallback"]},p,e)},m=(0,i.default)("div",{name:"MuiAvatar",slot:"Root",overridesResolver:(t,e)=>{let{ownerState:r}=t;return[e.root,e[r.variant],r.colorDefault&&e.colorDefault]}})((0,c.Z)(t=>{let{theme:e}=t;return{position:"relative",display:"flex",alignItems:"center",justifyContent:"center",flexShrink:0,width:40,height:40,fontFamily:e.typography.fontFamily,fontSize:e.typography.pxToRem(20),lineHeight:1,borderRadius:"50%",overflow:"hidden",userSelect:"none",variants:[{props:{variant:"rounded"},style:{borderRadius:(e.vars||e).shape.borderRadius}},{props:{variant:"square"},style:{borderRadius:0}},{props:{colorDefault:!0},style:{color:(e.vars||e).palette.background.default,...e.vars?{backgroundColor:e.vars.palette.Avatar.defaultBg}:{backgroundColor:e.palette.grey[400],...e.applyStyles("dark",{backgroundColor:e.palette.grey[600]})}}}]}})),v=(0,i.default)("img",{name:"MuiAvatar",slot:"Img",overridesResolver:(t,e)=>e.img})({width:"100%",height:"100%",textAlign:"center",objectFit:"cover",color:"transparent",textIndent:1e4}),w=(0,i.default)(u,{name:"MuiAvatar",slot:"Fallback",overridesResolver:(t,e)=>e.fallback})({width:"75%",height:"75%"});var y=o.forwardRef(function(t,e){let r=(0,s.i)({props:t,name:"MuiAvatar"}),{alt:n,children:i,className:c,component:l="div",slots:u={},slotProps:f={},imgProps:h,sizes:p,src:y,srcSet:b,variant:j="circular",...Z}=r,S=null,k={...r,component:l,variant:j},A=function(t){let{crossOrigin:e,referrerPolicy:r,src:a,srcSet:n}=t,[i,c]=o.useState(!1);return o.useEffect(()=>{if(!a&&!n)return;c(!1);let t=!0,o=new Image;return o.onload=()=>{t&&c("loaded")},o.onerror=()=>{t&&c("error")},o.crossOrigin=e,o.referrerPolicy=r,o.src=a,n&&(o.srcset=n),()=>{t=!1}},[e,r,a,n]),i}({...h,..."function"==typeof f.img?f.img(k):f.img,src:y,srcSet:b}),R=y||b,D=R&&"error"!==A;k.colorDefault=!D,delete k.ownerState;let _=x(k),[C,I]=(0,g.Z)("img",{className:_.img,elementType:v,externalForwardedProps:{slots:u,slotProps:{img:{...h,...f.img}}},additionalProps:{alt:n,src:y,srcSet:b,sizes:p},ownerState:k});return S=D?(0,d.jsx)(C,{...I}):i||0===i?i:R&&n?n[0]:(0,d.jsx)(w,{ownerState:k,className:_.fallback}),(0,d.jsx)(m,{as:l,className:(0,a.Z)(_.root,c),ref:e,...Z,ownerState:k,children:S})})},40256:function(t,e,r){"use strict";r.d(e,{$R:function(){return d},A_:function(){return c},BO:function(){return n},GH:function(){return u},_n:function(){return a},be:function(){return i},iG:function(){return l},j0:function(){return s}});var o=r(83464);let a="http://localhost:8080",n="<EMAIL>,<EMAIL>,<EMAIL>".split(",").map(t=>t.trim()),i=o.Z.create({baseURL:a});async function c(t,e,r){let o=new URL(r?"".concat(r).concat(t):"".concat(a).concat(t));return e&&Object.keys(e).forEach(t=>o.searchParams.append(t,e[t])),(await i.get(o.toString())).data}async function s(t,e,r){let o=r?"".concat(r).concat(t):"".concat(a).concat(t),n=await i.post(o,e,{headers:{"Content-Type":"application/json"}});return{status:n.status,data:n.data}}async function l(t,e,r){let o=r?"".concat(r).concat(t):"".concat(a).concat(t);console.log("Document upload to: ".concat(o));let n=await i.post(o,e,{headers:{"Content-Type":"multipart/form-data"}});return{status:n.status,data:n.data}}async function d(t,e,r){let o=new URL(r?"".concat(r).concat(t):"".concat(a).concat(t));return e&&Object.keys(e).forEach(t=>o.searchParams.append(t,e[t])),console.log("GET Blob request to: ".concat(o.toString())),(await i.get(o.toString(),{responseType:"blob"})).data}async function u(t,e,r){let o=r?"".concat(r).concat(t):"".concat(a).concat(t),n=await i.put(o,e,{headers:{"Content-Type":"application/json"}});return{status:n.status,data:n.data}}i.interceptors.request.use(t=>{let e=localStorage.getItem("userid1")||localStorage.getItem("userId");return e?t.headers["user-id"]=e:console.warn("No user ID found in localStorage for API request"),t})},97415:function(t,e,r){"use strict";r.r(e);var o=r(57437),a=r(2265),n=r(95656),i=r(46387),c=r(22095),s=r(53410),l=r(70208),d=r(32281),u=r(54301),f=r(61201),h=r(59469),p=r(13571),g=r(48223),x=r(99376),m=r(40256);e.default=(0,p.Z)(()=>{let[t,e]=(0,a.useState)([]),[r,p]=(0,a.useState)(!1),v=(0,x.useRouter)();return(0,a.useEffect)(()=>{(async()=>{try{let t=await (0,m.A_)("/notifications");t.success&&(e(t.data),p(!1))}catch(e){var t;(null===(t=e.response)||void 0===t?void 0:t.status)===404?p(!0):console.error("Error fetching notifications:",e)}})()},[]),(0,o.jsx)(g.Z,{children:(0,o.jsxs)(n.Z,{sx:{bgcolor:"#F5F6FA",px:4,py:2,width:"100%",height:"100vh",overflow:"hidden"},children:[(0,o.jsx)(n.Z,{sx:{display:"flex",alignItems:"center",mb:4,mt:3},children:(0,o.jsx)(i.Z,{sx:{fontWeight:600,fontSize:"28px",color:"black",lineHeight:"34px",textAlign:"left"},children:"Notification History"})}),(0,o.jsx)(c.Z,{component:s.Z,sx:{maxHeight:"calc(100vh - 150px)",overflow:"auto"},children:(0,o.jsxs)(l.Z,{stickyHeader:!0,sx:{minWidth:650,tableLayout:"fixed"},"aria-label":"notification history table",children:[(0,o.jsx)(d.Z,{children:(0,o.jsxs)(u.Z,{children:[(0,o.jsx)(f.Z,{sx:{fontWeight:"bold",width:"10%"},children:"S.No"}),(0,o.jsx)(f.Z,{sx:{fontWeight:"bold",width:"20%"},children:"Notification ID"}),(0,o.jsx)(f.Z,{sx:{fontWeight:"bold",width:"50%"},children:"Message Text"}),(0,o.jsx)(f.Z,{sx:{fontWeight:"bold",width:"20%"},children:"Date"})]})}),(0,o.jsx)(h.Z,{children:r?(0,o.jsx)(u.Z,{children:(0,o.jsx)(f.Z,{colSpan:4,sx:{textAlign:"center",fontStyle:"italic"},children:"No notifications found"})}):t.map((t,e)=>(0,o.jsxs)(u.Z,{onClick:()=>v.push("/notifications-analytics/".concat(t._id)),sx:{"&:last-child td, &:last-child th":{border:0},cursor:"pointer","&:hover":{backgroundColor:"rgba(0, 0, 0, 0.04)"}},children:[(0,o.jsx)(f.Z,{sx:{overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap"},children:e+1}),(0,o.jsx)(f.Z,{sx:{overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap"},children:t._id}),(0,o.jsx)(f.Z,{sx:{overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap"},children:t.message}),(0,o.jsx)(f.Z,{sx:{overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap"},children:new Date(t.createdAt).toLocaleDateString()})]},t._id))})]})})]})})})}},function(t){t.O(0,[139,3463,3301,8575,8685,187,1423,9932,3919,9129,2786,9826,8166,8760,1711,3344,9662,1356,2971,2117,1744],function(){return t(t.s=53275)}),_N_E=t.O()}]);