(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1931],{97598:function(t,e,a){Promise.resolve().then(a.bind(a,93135))},67116:function(t,e,a){"use strict";a.d(e,{Z:function(){return b}});var o=a(2265),n=a(61994),r=a(20801),i=a(16210),s=a(21086),c=a(37053),l=a(94630),d=a(57437),u=(0,l.Z)((0,d.jsx)("path",{d:"M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"}),"Person"),h=a(94143),f=a(50738);function p(t){return(0,f.ZP)("MuiAvatar",t)}(0,h.Z)("MuiAvatar",["root","colorDefault","circular","rounded","square","img","fallback"]);var g=a(79114);let x=t=>{let{classes:e,variant:a,colorDefault:o}=t;return(0,r.Z)({root:["root",a,o&&"colorDefault"],img:["img"],fallback:["fallback"]},p,e)},m=(0,i.default)("div",{name:"MuiAvatar",slot:"Root",overridesResolver:(t,e)=>{let{ownerState:a}=t;return[e.root,e[a.variant],a.colorDefault&&e.colorDefault]}})((0,s.Z)(t=>{let{theme:e}=t;return{position:"relative",display:"flex",alignItems:"center",justifyContent:"center",flexShrink:0,width:40,height:40,fontFamily:e.typography.fontFamily,fontSize:e.typography.pxToRem(20),lineHeight:1,borderRadius:"50%",overflow:"hidden",userSelect:"none",variants:[{props:{variant:"rounded"},style:{borderRadius:(e.vars||e).shape.borderRadius}},{props:{variant:"square"},style:{borderRadius:0}},{props:{colorDefault:!0},style:{color:(e.vars||e).palette.background.default,...e.vars?{backgroundColor:e.vars.palette.Avatar.defaultBg}:{backgroundColor:e.palette.grey[400],...e.applyStyles("dark",{backgroundColor:e.palette.grey[600]})}}}]}})),w=(0,i.default)("img",{name:"MuiAvatar",slot:"Img",overridesResolver:(t,e)=>e.img})({width:"100%",height:"100%",textAlign:"center",objectFit:"cover",color:"transparent",textIndent:1e4}),y=(0,i.default)(u,{name:"MuiAvatar",slot:"Fallback",overridesResolver:(t,e)=>e.fallback})({width:"75%",height:"75%"});var b=o.forwardRef(function(t,e){let a=(0,c.i)({props:t,name:"MuiAvatar"}),{alt:r,children:i,className:s,component:l="div",slots:u={},slotProps:h={},imgProps:f,sizes:p,src:b,srcSet:v,variant:j="circular",...Z}=a,k=null,S={...a,component:l,variant:j},R=function(t){let{crossOrigin:e,referrerPolicy:a,src:n,srcSet:r}=t,[i,s]=o.useState(!1);return o.useEffect(()=>{if(!n&&!r)return;s(!1);let t=!0,o=new Image;return o.onload=()=>{t&&s("loaded")},o.onerror=()=>{t&&s("error")},o.crossOrigin=e,o.referrerPolicy=a,o.src=n,r&&(o.srcset=r),()=>{t=!1}},[e,a,n,r]),i}({...f,..."function"==typeof h.img?h.img(S):h.img,src:b,srcSet:v}),A=b||v,D=A&&"error"!==R;S.colorDefault=!D,delete S.ownerState;let C=x(S),[T,E]=(0,g.Z)("img",{className:C.img,elementType:w,externalForwardedProps:{slots:u,slotProps:{img:{...f,...h.img}}},additionalProps:{alt:r,src:b,srcSet:v,sizes:p},ownerState:S});return k=D?(0,d.jsx)(T,{...E}):i||0===i?i:A&&r?r[0]:(0,d.jsx)(y,{ownerState:S,className:C.fallback}),(0,d.jsx)(m,{as:l,className:(0,n.Z)(C.root,s),ref:e,...Z,ownerState:S,children:k})})},40256:function(t,e,a){"use strict";a.d(e,{$R:function(){return d},A_:function(){return s},BO:function(){return r},GH:function(){return u},_n:function(){return n},be:function(){return i},iG:function(){return l},j0:function(){return c}});var o=a(83464);let n="http://localhost:8080",r="<EMAIL>,<EMAIL>,<EMAIL>".split(",").map(t=>t.trim()),i=o.Z.create({baseURL:n});async function s(t,e,a){let o=new URL(a?"".concat(a).concat(t):"".concat(n).concat(t));return e&&Object.keys(e).forEach(t=>o.searchParams.append(t,e[t])),(await i.get(o.toString())).data}async function c(t,e,a){let o=a?"".concat(a).concat(t):"".concat(n).concat(t),r=await i.post(o,e,{headers:{"Content-Type":"application/json"}});return{status:r.status,data:r.data}}async function l(t,e,a){let o=a?"".concat(a).concat(t):"".concat(n).concat(t);console.log("Document upload to: ".concat(o));let r=await i.post(o,e,{headers:{"Content-Type":"multipart/form-data"}});return{status:r.status,data:r.data}}async function d(t,e,a){let o=new URL(a?"".concat(a).concat(t):"".concat(n).concat(t));return e&&Object.keys(e).forEach(t=>o.searchParams.append(t,e[t])),console.log("GET Blob request to: ".concat(o.toString())),(await i.get(o.toString(),{responseType:"blob"})).data}async function u(t,e,a){let o=a?"".concat(a).concat(t):"".concat(n).concat(t),r=await i.put(o,e,{headers:{"Content-Type":"application/json"}});return{status:r.status,data:r.data}}i.interceptors.request.use(t=>{let e=localStorage.getItem("userid1")||localStorage.getItem("userId");return e?t.headers["user-id"]=e:console.warn("No user ID found in localStorage for API request"),t})},93135:function(t,e,a){"use strict";a.r(e);var o=a(57437),n=a(2265),r=a(99376),i=a(95656),s=a(46387),c=a(94013),l=a(22095),d=a(53410),u=a(70208),h=a(32281),f=a(54301),p=a(61201),g=a(59469),x=a(48223),m=a(13571),w=a(40256),y=a(83464);let b=async t=>{try{let e=await y.Z.get("".concat(w._n,"/download-notifications-analytics"),{params:{messageId:t},responseType:"blob"});if(e&&e.data){let t=new Blob([e.data],{type:"text/csv"}),a=document.createElement("a"),o=window.URL.createObjectURL(t);a.href=o,a.download="notification_analytics.csv",a.click(),window.URL.revokeObjectURL(o)}}catch(t){console.error("Failed to download CSV:",t)}};e.default=(0,m.Z)(()=>{(0,r.useRouter)();let t=(0,r.usePathname)().split("/").pop(),[e,a]=(0,n.useState)([]),[m,y]=(0,n.useState)([]),[v,j]=(0,n.useState)(!0);return(0,n.useEffect)(()=>{t&&(async()=>{j(!0);try{let e=await (0,w.A_)("/notifications-analytics",{messageId:t});if(e.success){let t=e.data.map(t=>({...t,date:new Date(t.date).toISOString().split("T")[0]})),o=new Set;t.forEach(t=>{Object.keys(t).forEach(t=>{["date","employer","message","messageDelivered","noActionTaken","total"].includes(t)||o.add(t)})}),y(Array.from(o)),a(t)}}catch(t){console.error("Failed to fetch analytics data:",t)}finally{j(!1)}})()},[t]),(0,o.jsx)(x.Z,{children:(0,o.jsxs)(i.Z,{sx:{bgcolor:"#F5F6FA",px:4,py:2,width:"100%",height:"100vh",overflow:"hidden"},children:[(0,o.jsxs)(i.Z,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",mb:3,mt:3},children:[(0,o.jsx)(s.Z,{sx:{fontWeight:600,fontSize:"28px",color:"black",lineHeight:"34px",textAlign:"left"},children:"Notification Analytics"}),(0,o.jsx)(c.Z,{variant:"contained",onClick:async()=>{t&&b(t)},sx:{textTransform:"none",borderRadius:"6px",bgcolor:"black",color:"white",boxShadow:"none",width:"140px",paddingY:"7px",paddingX:"16px",border:"1px solid #D2D2D2","&:hover":{backgroundColor:"rgba(0, 0, 0, 0.1)",boxShadow:"none"}},children:"Download CSV"})]}),(0,o.jsx)(l.Z,{component:d.Z,sx:{maxHeight:"calc(100vh - 140px)",overflow:"auto"},children:(0,o.jsxs)(u.Z,{stickyHeader:!0,sx:{minWidth:650},"aria-label":"analytics table",children:[(0,o.jsx)(h.Z,{children:(0,o.jsxs)(f.Z,{children:[(0,o.jsx)(p.Z,{sx:{fontWeight:"bold",width:"12%"},children:"Date"}),(0,o.jsx)(p.Z,{sx:{fontWeight:"bold",width:"15%"},children:"Employer"}),(0,o.jsx)(p.Z,{sx:{fontWeight:"bold",width:"25%"},children:"Message"}),(0,o.jsx)(p.Z,{sx:{fontWeight:"bold",width:"8%"},children:"Message Delivered"}),m.map(t=>(0,o.jsx)(p.Z,{sx:{fontWeight:"bold",width:"8%"},children:t},t)),(0,o.jsx)(p.Z,{sx:{fontWeight:"bold",width:"8%"},children:"No Action Taken"}),(0,o.jsx)(p.Z,{sx:{fontWeight:"bold",width:"8%"},children:"Total"})]})}),(0,o.jsx)(g.Z,{children:e.map((t,e)=>(0,o.jsxs)(f.Z,{sx:{"&:last-child td, &:last-child th":{border:0}},children:[(0,o.jsx)(p.Z,{sx:{width:"12%"},children:t.date}),(0,o.jsx)(p.Z,{sx:{width:"15%"},children:t.employer}),(0,o.jsx)(p.Z,{sx:{width:"25%"},children:t.message}),(0,o.jsx)(p.Z,{sx:{width:"8%"},children:t.messageDelivered}),m.map(e=>(0,o.jsx)(p.Z,{sx:{width:"8%"},children:t[e]||0},e)),(0,o.jsx)(p.Z,{sx:{width:"8%"},children:t.noActionTaken}),(0,o.jsx)(p.Z,{sx:{width:"8%"},children:t.total})]},e))})]})})]})})})}},function(t){t.O(0,[139,3463,3301,8575,293,9810,187,3145,9932,3919,9129,2786,9826,8166,8760,1711,3344,9662,1356,2971,2117,1744],function(){return t(t.s=97598)}),_N_E=t.O()}]);