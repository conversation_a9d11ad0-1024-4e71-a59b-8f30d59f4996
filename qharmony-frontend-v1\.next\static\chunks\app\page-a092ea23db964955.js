(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9979],{64354:function(e,t,i){Promise.resolve().then(i.bind(i,16117))},53284:function(e,t,i){"use strict";var r=i(94630),n=i(57437);t.Z=(0,r.Z)((0,n.jsx)("path",{d:"M6 17h3l2-4V7H5v6h3zm8 0h3l2-4V7h-6v6h3z"}),"FormatQuote")},46837:function(e,t,i){"use strict";var r=i(94630),n=i(57437);t.Z=(0,r.Z)((0,n.jsx)("path",{d:"M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z"}),"Home")},8350:function(e,t,i){"use strict";var r=i(2265),n=i(61994),o=i(20801),a=i(65208),l=i(16210),s=i(21086),c=i(37053),d=i(42596),h=i(57437);let f=e=>{let{absolute:t,children:i,classes:r,flexItem:n,light:a,orientation:l,textAlign:s,variant:c}=e;return(0,o.Z)({root:["root",t&&"absolute",c,a&&"light","vertical"===l&&"vertical",n&&"flexItem",i&&"withChildren",i&&"vertical"===l&&"withChildrenVertical","right"===s&&"vertical"!==l&&"textAlignRight","left"===s&&"vertical"!==l&&"textAlignLeft"],wrapper:["wrapper","vertical"===l&&"wrapperVertical"]},d.V,r)},g=(0,l.default)("div",{name:"MuiDivider",slot:"Root",overridesResolver:(e,t)=>{let{ownerState:i}=e;return[t.root,i.absolute&&t.absolute,t[i.variant],i.light&&t.light,"vertical"===i.orientation&&t.vertical,i.flexItem&&t.flexItem,i.children&&t.withChildren,i.children&&"vertical"===i.orientation&&t.withChildrenVertical,"right"===i.textAlign&&"vertical"!==i.orientation&&t.textAlignRight,"left"===i.textAlign&&"vertical"!==i.orientation&&t.textAlignLeft]}})((0,s.Z)(e=>{let{theme:t}=e;return{margin:0,flexShrink:0,borderWidth:0,borderStyle:"solid",borderColor:(t.vars||t).palette.divider,borderBottomWidth:"thin",variants:[{props:{absolute:!0},style:{position:"absolute",bottom:0,left:0,width:"100%"}},{props:{light:!0},style:{borderColor:t.vars?"rgba(".concat(t.vars.palette.dividerChannel," / 0.08)"):(0,a.Fq)(t.palette.divider,.08)}},{props:{variant:"inset"},style:{marginLeft:72}},{props:{variant:"middle",orientation:"horizontal"},style:{marginLeft:t.spacing(2),marginRight:t.spacing(2)}},{props:{variant:"middle",orientation:"vertical"},style:{marginTop:t.spacing(1),marginBottom:t.spacing(1)}},{props:{orientation:"vertical"},style:{height:"100%",borderBottomWidth:0,borderRightWidth:"thin"}},{props:{flexItem:!0},style:{alignSelf:"stretch",height:"auto"}},{props:e=>{let{ownerState:t}=e;return!!t.children},style:{display:"flex",textAlign:"center",border:0,borderTopStyle:"solid",borderLeftStyle:"solid","&::before, &::after":{content:'""',alignSelf:"center"}}},{props:e=>{let{ownerState:t}=e;return t.children&&"vertical"!==t.orientation},style:{"&::before, &::after":{width:"100%",borderTop:"thin solid ".concat((t.vars||t).palette.divider),borderTopStyle:"inherit"}}},{props:e=>{let{ownerState:t}=e;return"vertical"===t.orientation&&t.children},style:{flexDirection:"column","&::before, &::after":{height:"100%",borderLeft:"thin solid ".concat((t.vars||t).palette.divider),borderLeftStyle:"inherit"}}},{props:e=>{let{ownerState:t}=e;return"right"===t.textAlign&&"vertical"!==t.orientation},style:{"&::before":{width:"90%"},"&::after":{width:"10%"}}},{props:e=>{let{ownerState:t}=e;return"left"===t.textAlign&&"vertical"!==t.orientation},style:{"&::before":{width:"10%"},"&::after":{width:"90%"}}}]}})),p=(0,l.default)("span",{name:"MuiDivider",slot:"Wrapper",overridesResolver:(e,t)=>{let{ownerState:i}=e;return[t.wrapper,"vertical"===i.orientation&&t.wrapperVertical]}})((0,s.Z)(e=>{let{theme:t}=e;return{display:"inline-block",paddingLeft:"calc(".concat(t.spacing(1)," * 1.2)"),paddingRight:"calc(".concat(t.spacing(1)," * 1.2)"),whiteSpace:"nowrap",variants:[{props:{orientation:"vertical"},style:{paddingTop:"calc(".concat(t.spacing(1)," * 1.2)"),paddingBottom:"calc(".concat(t.spacing(1)," * 1.2)")}}]}})),u=r.forwardRef(function(e,t){let i=(0,c.i)({props:e,name:"MuiDivider"}),{absolute:r=!1,children:o,className:a,orientation:l="horizontal",component:s=o||"vertical"===l?"div":"hr",flexItem:d=!1,light:u=!1,role:x="hr"!==s?"separator":void 0,textAlign:m="center",variant:b="fullWidth",...A}=i,v={...i,absolute:r,component:s,flexItem:d,light:u,orientation:l,role:x,textAlign:m,variant:b},y=f(v);return(0,h.jsx)(g,{as:s,className:(0,n.Z)(y.root,a),role:x,ref:t,ownerState:v,"aria-orientation":"separator"===x&&("hr"!==s||"vertical"===l)?l:void 0,...A,children:o?(0,h.jsx)(p,{className:y.wrapper,ownerState:v,children:o}):null})});u&&(u.muiSkipListHighlight=!0),t.Z=u},42596:function(e,t,i){"use strict";i.d(t,{V:function(){return o}});var r=i(94143),n=i(50738);function o(e){return(0,n.ZP)("MuiDivider",e)}let a=(0,r.Z)("MuiDivider",["root","absolute","fullWidth","inset","middle","flexItem","light","vertical","withChildren","withChildrenVertical","textAlignRight","textAlignLeft","wrapper","wrapperVertical"]);t.Z=a},40256:function(e,t,i){"use strict";i.d(t,{$R:function(){return d},A_:function(){return l},BO:function(){return o},GH:function(){return h},_n:function(){return n},be:function(){return a},iG:function(){return c},j0:function(){return s}});var r=i(83464);let n="http://localhost:8080",o="<EMAIL>,<EMAIL>,<EMAIL>".split(",").map(e=>e.trim()),a=r.Z.create({baseURL:n});async function l(e,t,i){let r=new URL(i?"".concat(i).concat(e):"".concat(n).concat(e));return t&&Object.keys(t).forEach(e=>r.searchParams.append(e,t[e])),(await a.get(r.toString())).data}async function s(e,t,i){let r=i?"".concat(i).concat(e):"".concat(n).concat(e),o=await a.post(r,t,{headers:{"Content-Type":"application/json"}});return{status:o.status,data:o.data}}async function c(e,t,i){let r=i?"".concat(i).concat(e):"".concat(n).concat(e);console.log("Document upload to: ".concat(r));let o=await a.post(r,t,{headers:{"Content-Type":"multipart/form-data"}});return{status:o.status,data:o.data}}async function d(e,t,i){let r=new URL(i?"".concat(i).concat(e):"".concat(n).concat(e));return t&&Object.keys(t).forEach(e=>r.searchParams.append(e,t[e])),console.log("GET Blob request to: ".concat(r.toString())),(await a.get(r.toString(),{responseType:"blob"})).data}async function h(e,t,i){let r=i?"".concat(i).concat(e):"".concat(n).concat(e),o=await a.put(r,t,{headers:{"Content-Type":"application/json"}});return{status:o.status,data:o.data}}a.interceptors.request.use(e=>{let t=localStorage.getItem("userid1")||localStorage.getItem("userId");return t?e.headers["user-id"]=t:console.warn("No user ID found in localStorage for API request"),e})},16117:function(e,t,i){"use strict";i.r(t);var r=i(57437),n=i(95656),o=i(46387),a=i(97404),l=i(94013),s=i(35389),c=i(60062),d=i(33145),h=i(2265),f=i(80944),g=i(77534),p=i(94509),u=i(47369),x=i(99376),m=i(18761);let b=()=>{let{user:e}=(0,u.a)(),t=(0,x.useRouter)();(0,h.useEffect)(()=>{e&&t.push("/dashboard")},[e]);let[i,c]=(0,h.useState)(""),[p,m]=(0,h.useState)(!1),[b,A]=(0,h.useState)(!1),[v,y]=(0,h.useState)(""),[w,j]=(0,h.useState)(""),Z=async()=>{if(!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(i)){j("Please enter a valid email address.");return}m(!0),y(await (0,g.selfOnboard)(i)),m(!1),A(!0)};return(0,r.jsxs)(n.Z,{children:[(0,r.jsxs)(n.Z,{sx:{display:"flex",alignItems:"center",mb:5,cursor:"pointer",position:"absolute",top:"30px",left:"100px"},onClick:()=>{window.location.href="https://benosphere.com/"},children:[(0,r.jsx)(d.default,{src:f.Z,alt:"BenOsphere Logo",width:40,height:40}),(0,r.jsx)(o.Z,{variant:"h6",sx:{ml:1,fontWeight:"800",color:"#ffffff"},children:"BenOsphere"})]}),"ask_admin_to_add"===v?(0,r.jsxs)(n.Z,{sx:{display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",color:"white",textAlign:"center",height:"100%"},children:[(0,r.jsx)(o.Z,{sx:{fontWeight:"bold",fontSize:"60px",mb:2},children:"\uD83E\uDD14 No account yet"}),(0,r.jsx)(o.Z,{variant:"body1",sx:{fontSize:"20px",color:"#bbbbbb"},children:"Contact your company's admin HR to learn more on how to get access to BenOsphere. We will see you again shortly."})]}):"magic_link_sent"===v?(0,r.jsxs)(n.Z,{sx:{display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",color:"white",textAlign:"center",height:"100%"},children:[(0,r.jsx)(o.Z,{sx:{fontWeight:"bold",fontSize:"60px",mb:2},children:"✅ Magic Link Sent"}),(0,r.jsx)(o.Z,{variant:"body1",sx:{fontSize:"20px",color:"#bbbbbb"},children:"Please check your email for the magic link to access BenOsphere. If it's not in your inbox, please check your spam or junk folder."})]}):(0,r.jsxs)(n.Z,{sx:{maxWidth:"600px",width:"100%",mt:8},children:[(0,r.jsx)(o.Z,{variant:"h4",sx:{fontSize:"70px",fontWeight:"bold",mb:2,lineHeight:"1.2",color:"#ffffff"},children:"Maximize Your Employee Benefits"}),(0,r.jsx)(o.Z,{variant:"body1",sx:{mb:6,fontSize:"18px",color:"#bbbbbb"},children:"Enter your work email, and we'll send you a magic link to easily access all your benefits."}),(0,r.jsxs)(n.Z,{sx:{display:"flex",flexDirection:"column",alignItems:"stretch",mb:3},children:[(0,r.jsx)(a.Z,{fullWidth:!0,variant:"outlined",placeholder:"<EMAIL>",value:i,onChange:e=>{c(e.target.value),j("")},sx:{bgcolor:"#333333",input:{color:"#ffffff"},borderRadius:"10px","& .MuiOutlinedInput-root":{borderRadius:"10px","& fieldset":{borderColor:"#555555"},"&:hover fieldset":{borderColor:"#888888"}},mb:2}}),w&&(0,r.jsx)(o.Z,{variant:"body2",sx:{color:"red",mt:1},children:w}),(0,r.jsx)(l.Z,{variant:"contained",onClick:Z,sx:{textTransform:"none",background:"linear-gradient(90deg, #7206E6, #B54BFF)",color:"#ffffff",height:"54px",borderRadius:"10px",boxShadow:"none",fontSize:"17px",marginTop:"10px","&:hover":{background:"linear-gradient(90deg, #7206E6, #B54BFF)"}},disabled:p,children:p?(0,r.jsx)(s.Z,{size:24,sx:{color:"#ffffff"}}):"Send Magic Link"})]})]})]})};t.default=()=>{let e=(0,c.Z)("(max-width:600px)"),t=(0,m.Z)(b);return e?(0,r.jsx)(n.Z,{sx:{padding:2},children:(0,r.jsx)(t,{})}):(0,r.jsx)(p.Z,{LeftComponent:(0,r.jsx)(b,{})})}},94509:function(e,t,i){"use strict";i.d(t,{Z:function(){return h}});var r=i(57437),n=i(89414),o=i(95656),a=i(46387),l=i(33145),s=i(53284),c=i(2265);let d=[{src:{src:"/_next/static/media/landing_page_image_1.883ba61a.png",height:1024,width:756,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAYAAAAICAIAAABVpBlvAAAAoUlEQVR42mOwNNRyMlRnYJRQUlNXNbISV9RgcHcwdrAwTPJ2ctVWFlTVkdPUZ7CxMw51MVvaklcb5cnAKqqoosGgbqrTmRe7e1ZbqZ+jnY6WgoEJg6CMUpify5UN07tivCU19O19fBiAoDjO/dzazqIoNwZ1CyklDYbGyTMO7N82rzwkkJchu6K6sr2D4eiV63suXl+3c09rU2Nt74TmKdMAG00wAXeqZ/wAAAAASUVORK5CYII=",blurWidth:6,blurHeight:8},text:"I get to keep track of all my benefits so I never miss out on the stuff that matters most.",name:"Troy Sivan",title:"Associate Manager"},{src:{src:"/_next/static/media/landing_page_image_2.59f16961.png",height:1024,width:756,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAYAAAAICAIAAABVpBlvAAAAo0lEQVR42mOYMakxwMdRRU/Lzs4sNtTTx9WGYeXS6TYOlik2GjGmKpbWJgWJgQwbN62M9rTbXJexsiTBWkXWydmWYcuurZO7G45NqluR6nfs+IkZq9cxHD1zatqS+Vsn1E4I0L3+4MH6nVsZZi9aFJWevXvn3i1rNtd2TQsMcmYAAgc3v8Ub989Zu6+kvgfEl5CWd/MJmrd8Y++0RYUVjQwMDACH00ArwNCIEAAAAABJRU5ErkJggg==",blurWidth:6,blurHeight:8},text:"Now I can track my benefits instantly without having to search through emails. It’s a game–changer!",name:"David Miller",title:"Software Engineer"},{src:{src:"/_next/static/media/landing_page_image_3.e9420572.png",height:1024,width:756,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAYAAAAICAIAAABVpBlvAAAAn0lEQVR42mNQE2YNsRJlYGAwlmL3UuUwFGNmcNbgXlNuNCkz0FtHTpydwUSSlUGBnaHYW29jc3pXoo8KA4ORNBeDOBODl7Ha3JyQQk8LLgYGYS4WBlN5PgYGhjgHw2wPM0FJGQYgsNeQZNbQj7HRzQlyU7WwU1VVZ3CyMrG2ssxxM9M2sTK0tFFW12IwsrF1MDN2tbPXs3TQN7MQk5ACAOjZG1OaugBXAAAAAElFTkSuQmCC",blurWidth:6,blurHeight:8},text:"This is by far the most convenient way to access my benefits. Everything I need is right at my fingertips.",name:"Emily Garcia",title:"HR Specialist"},{src:{src:"/_next/static/media/landing_page_image_4.135a5874.png",height:1024,width:756,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAYAAAAICAIAAABVpBlvAAAAo0lEQVR42gGYAGf/AIl9cqKWioB2bZSGenZuZlpVUQCgmZKMh4KGWD++oI5yaWRtbGkArK+snpmWkl9G0rWooZSGbXZxAH+Af5mbmnpRRLWVh4qGe1JnQgCTfm+Wk456Y1evmYqprrBhbWAAnZWUoaKjsKWg1c/M5O/zwczSALzFy8nT2rnDy9vn7NXh583Y3gCoq6/CzdXDzdTI09nR3ePU3+Q7J1hGmDSqYQAAAABJRU5ErkJggg==",blurWidth:6,blurHeight:8},text:"I love how I can get all my benefits info through Slack. It saves me so much time!",name:"Troy Edward",title:"Associate Manager"}];var h=e=>{let{LeftComponent:t}=e,[i,h]=(0,c.useState)(0);(0,c.useEffect)(()=>{let e=setInterval(()=>{h(e=>(e+1)%d.length)},5e3);return()=>clearInterval(e)},[]);let f=d[i];return(0,r.jsxs)(n.ZP,{container:!0,style:{height:"95vh",width:"100%"},children:[(0,r.jsx)(n.ZP,{item:!0,xs:12,md:6,sx:{bgcolor:"#000000",color:"#ffffff",display:"flex",flexDirection:"column",justifyContent:"center",alignItems:"center",padding:"100px",height:"95vh",overflow:"auto"},children:t}),(0,r.jsx)(n.ZP,{item:!0,xs:12,md:6,sx:{display:"flex",justifyContent:"center",alignItems:"center",backgroundColor:"#f8f9fa",padding:"0",position:"relative",overflow:"hidden",height:"95vh"},children:(0,r.jsxs)(o.Z,{sx:{position:"relative",width:"100%",height:"100%",overflow:"hidden"},children:[(0,r.jsx)(l.default,{src:f.src,alt:"Profile",layout:"fill",objectFit:"cover",style:{objectPosition:"center"}}),(0,r.jsx)(o.Z,{sx:{position:"absolute",bottom:0,left:0,width:"100%",height:"100%",background:"linear-gradient(to top, rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0) 60%)"}}),(0,r.jsxs)(o.Z,{sx:{position:"absolute",bottom:"10%",left:"10%",color:"#ffffff",width:"550px"},children:[(0,r.jsx)(s.Z,{sx:{fontSize:70,marginBottom:"10px",opacity:.5,marginLeft:-2}}),(0,r.jsx)(a.Z,{variant:"h5",sx:{fontSize:"32px",fontWeight:"bold",lineHeight:"1.5",mb:2},children:f.text}),(0,r.jsx)(a.Z,{variant:"h5",sx:{fontSize:"24px",fontWeight:"bold",lineHeight:"1.5",mt:4},children:f.name}),(0,r.jsx)(a.Z,{variant:"body2",sx:{fontSize:"20px",fontWeight:"light"},children:f.title})]})]})})]})}},18761:function(e,t,i){"use strict";i.d(t,{Z:function(){return T}});var r=i(57437),n=i(93062),o=i(71495),a=i(71004),l=i(59832),s=i(92253),c=i(61910),d=i(95656),h=i(94013),f=i(46387),g=i(8350),p=i(15273),u=i(73261),x=i(11741),m=i(53431),b=i(67051),A=i(33145),v=i(83337),y=i(2265),w=i(39547),j=i(70623),Z=i(99376),S=i(56336),C=i(68575),k=i(31175),R=i(46837),I=i(47369),W=i(15116),B=i(47723);let U="75vw";var E=()=>{let e=(0,v.T)(),t=(0,Z.useRouter)();(0,Z.usePathname)();let{logout:i}=(0,I.a)(),n=(0,v.C)(e=>e.company.companyBenefitTypes);(0,v.C)(e=>e.user.selectedBenefitType);let o=(0,C.v9)(e=>(0,j.MP)(e));(0,y.useEffect)(()=>{o&&(0,w.N)(e,o)},[o,e]);let[a,l]=(0,y.useState)(!1);(0,y.useEffect)(()=>{l("true"===localStorage.getItem("isTeamsApp1"))},[]);let c=i=>{e((0,j.v2)(i)),t.push("/viewBenefitsByType/".concat(i))};return(0,r.jsxs)(s.ZP,{sx:{width:U,height:"100vh",flexShrink:0,"& .MuiDrawer-paper":{width:U,boxSizing:"border-box",bgcolor:"#ffffff",position:"relative"}},variant:"permanent",anchor:"left",children:[(0,r.jsxs)(d.Z,{sx:{padding:0,height:"100%",position:"relative",bgcolor:"#ffffff"},children:[(0,r.jsx)(d.Z,{sx:{mx:2,mt:2,px:1,py:.5,borderRadius:2,position:"relative","&:hover":{backgroundColor:"#f0f0f0"},bgcolor:"#F5F6FA"},children:(0,r.jsxs)(h.Z,{variant:"text",sx:{width:"100%",borderRadius:2,bgcolor:"#F5F6FA",color:"#333",fontWeight:"medium",fontSize:"1rem",textTransform:"none","&:hover":{backgroundColor:"#f0f0f0"},display:"flex",alignItems:"center",justifyContent:"flex-start"},onClick:()=>{t.push("/mobile/dashboard"),e((0,B.dL)())},children:[(0,r.jsx)(R.Z,{sx:{mr:1}}),"Home"]})}),(0,r.jsx)(f.Z,{sx:{mt:2,fontWeight:500,paddingX:2.5,fontSize:"1.2rem",color:"black"},children:"My Benefits"}),(0,r.jsx)(f.Z,{sx:{fontWeight:500,paddingX:2.5,paddingY:1,fontSize:".7rem",color:"rgba(0, 0, 0, 0.4)"},children:"SELECT ANY TO VIEW"}),(0,r.jsx)(g.Z,{sx:{my:1}}),(0,r.jsx)(p.Z,{children:n.length>0?n.map(t=>(0,r.jsx)(u.ZP,{disablePadding:!0,children:(0,r.jsxs)(x.Z,{onClick:()=>{c(t),e((0,B.dL)())},sx:{borderRadius:2,position:"relative","&:hover":{backgroundColor:"#f0f0f0"},bgcolor:"#F5F6FA",mx:2,mt:2},children:[(0,r.jsx)(m.Z,{sx:{minWidth:0,mr:2,pt:.5},children:(0,k.RS)(t)}),(0,r.jsx)(b.Z,{primary:t,sx:{fontWeight:"medium",color:"#333",fontSize:"1rem"}})]})},t)):(0,r.jsx)(f.Z,{variant:"body1",sx:{color:"#999",padding:2.5},children:"No benefits available at the moment"})})]}),!a&&(0,r.jsxs)(d.Z,{sx:{display:"flex",alignItems:"center",justifyContent:"center",bgcolor:"#F5F6FA",borderRadius:"30px",padding:"10px 20px",cursor:"pointer",position:"absolute",bottom:"50px",left:"50%",transform:"translateX(-50%)",width:"calc(100% - 40px)"},onClick:()=>{t.push("/qHarmonyBot"),e((0,B.dL)())},children:[(0,r.jsx)(A.default,{src:S.Z,alt:"AI Chat",style:{borderRadius:"100%",width:"40px",height:"40px",marginRight:"10px"}}),(0,r.jsxs)(d.Z,{children:[(0,r.jsx)(f.Z,{variant:"body1",sx:{fontWeight:"bold"},children:"Chat with Brea"}),(0,r.jsx)(f.Z,{variant:"body2",sx:{color:"#6c757d"},children:"24/7 available"})]})]}),(0,r.jsxs)(h.Z,{onClick:i,sx:{backgroundColor:"transparent",color:"#333",marginBottom:"5px",textTransform:"none",padding:"8px 16px",display:"flex",alignItems:"center",justifyContent:"center",gap:"8px","&:hover":{backgroundColor:"transparent",color:"#555"},boxShadow:"none"},children:[(0,r.jsx)(W.Z,{sx:{fontSize:"18px"}}),(0,r.jsx)(f.Z,{sx:{fontWeight:500,fontSize:"14px"},children:"Logout"})]})]})},T=e=>{let t=t=>{let i=(0,C.I0)(),h=(0,v.C)(e=>e.mobileSidebarToggle.isOpen),f=(0,Z.usePathname)();return(0,r.jsxs)(d.Z,{children:[(0,r.jsx)(n.ZP,{}),!("/"===f||"/onboard"===f)&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(o.Z,{position:"static",sx:{backgroundColor:"black"},children:(0,r.jsx)(a.Z,{sx:{mb:"/mobile/dashboard"===f?6:0},children:(0,r.jsx)(l.Z,{edge:"start",color:"inherit","aria-label":"menu",onClick:()=>i((0,B.FJ)()),children:(0,r.jsx)(c.Z,{fontSize:"large"})})})}),(0,r.jsx)(s.ZP,{anchor:"left",open:h,onClose:()=>i((0,B.dL)()),children:(0,r.jsx)(E,{})})]}),(0,r.jsx)(e,{...t})]})};return t.displayName="WithMobileEdgeFill(".concat(e.displayName||e.name||"Component",")"),t}},47723:function(e,t,i){"use strict";i.d(t,{FJ:function(){return n},dL:function(){return o}});let r=(0,i(39129).oM)({name:"drawer",initialState:{isOpen:!1},reducers:{openDrawer:e=>{e.isOpen=!0},closeDrawer:e=>{e.isOpen=!1},toggleDrawer:e=>{e.isOpen=!e.isOpen}}}),{openDrawer:n,closeDrawer:o,toggleDrawer:a}=r.actions;t.ZP=r.reducer},80944:function(e,t){"use strict";t.Z={src:"/_next/static/media/logo.770bfeee.png",height:300,width:300,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAYAAADED76LAAAA9ElEQVR42i2PMUoDQRiF3z+zu7MbFTaxkfUC0St4ACWdhbbapvACCnaCpU0a09jYiQewFj2AIljY2AoJ7LKZnc3s/P6EPPiq9+DxESR359UgBHNrGxTWQTcOPwtrLwGU6n5c7ySxeQb4vWn9WS/lky50JVH6UlrVjzTFE2iebvbwmJkE37/zPLB79SHfzWIzUUph0LrqScB4qpFEdEhICxm9BeY9BYA9kxJwfTw7IEKfGUsAq06FgNlGtjUSoDTvS1mB3B/BDInoM/KhvQhd8lDb5RGz/pDLVFM+inVc1L49pbXmtmjeiOZQNCGaX4vGXQGY/wM1tG/NQnnUIwAAAABJRU5ErkJggg==",blurWidth:8,blurHeight:8}}},function(e){e.O(0,[139,3463,3301,8575,293,9810,187,3145,9932,3919,9129,2786,9826,8166,8760,9414,7404,5515,3344,9662,2971,2117,1744],function(){return e(e.s=64354)}),_N_E=e.O()}]);