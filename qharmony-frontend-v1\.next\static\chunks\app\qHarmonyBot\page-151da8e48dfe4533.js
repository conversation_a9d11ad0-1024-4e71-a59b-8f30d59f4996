(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6633],{95743:function(e,t,r){Promise.resolve().then(r.bind(r,1358))},67116:function(e,t,r){"use strict";r.d(t,{Z:function(){return w}});var n=r(2265),o=r(61994),a=r(20801),s=r(16210),i=r(21086),l=r(37053),c=r(94630),u=r(57437),d=(0,c.Z)((0,u.jsx)("path",{d:"M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"}),"Person"),f=r(94143),p=r(50738);function m(e){return(0,p.ZP)("MuiAvatar",e)}(0,f.Z)("MuiAvatar",["root","colorDefault","circular","rounded","square","img","fallback"]);var g=r(79114);let h=e=>{let{classes:t,variant:r,colorDefault:n}=e;return(0,a.Z)({root:["root",r,n&&"colorDefault"],img:["img"],fallback:["fallback"]},m,t)},x=(0,s.default)("div",{name:"MuiAvatar",slot:"Root",overridesResolver:(e,t)=>{let{ownerState:r}=e;return[t.root,t[r.variant],r.colorDefault&&t.colorDefault]}})((0,i.Z)(e=>{let{theme:t}=e;return{position:"relative",display:"flex",alignItems:"center",justifyContent:"center",flexShrink:0,width:40,height:40,fontFamily:t.typography.fontFamily,fontSize:t.typography.pxToRem(20),lineHeight:1,borderRadius:"50%",overflow:"hidden",userSelect:"none",variants:[{props:{variant:"rounded"},style:{borderRadius:(t.vars||t).shape.borderRadius}},{props:{variant:"square"},style:{borderRadius:0}},{props:{colorDefault:!0},style:{color:(t.vars||t).palette.background.default,...t.vars?{backgroundColor:t.vars.palette.Avatar.defaultBg}:{backgroundColor:t.palette.grey[400],...t.applyStyles("dark",{backgroundColor:t.palette.grey[600]})}}}]}})),y=(0,s.default)("img",{name:"MuiAvatar",slot:"Img",overridesResolver:(e,t)=>t.img})({width:"100%",height:"100%",textAlign:"center",objectFit:"cover",color:"transparent",textIndent:1e4}),b=(0,s.default)(d,{name:"MuiAvatar",slot:"Fallback",overridesResolver:(e,t)=>t.fallback})({width:"75%",height:"75%"});var w=n.forwardRef(function(e,t){let r=(0,l.i)({props:e,name:"MuiAvatar"}),{alt:a,children:s,className:i,component:c="div",slots:d={},slotProps:f={},imgProps:p,sizes:m,src:w,srcSet:v,variant:S="circular",...I}=r,j=null,k={...r,component:c,variant:S},Z=function(e){let{crossOrigin:t,referrerPolicy:r,src:o,srcSet:a}=e,[s,i]=n.useState(!1);return n.useEffect(()=>{if(!o&&!a)return;i(!1);let e=!0,n=new Image;return n.onload=()=>{e&&i("loaded")},n.onerror=()=>{e&&i("error")},n.crossOrigin=t,n.referrerPolicy=r,n.src=o,a&&(n.srcset=a),()=>{e=!1}},[t,r,o,a]),s}({...p,..."function"==typeof f.img?f.img(k):f.img,src:w,srcSet:v}),R=w||v,D=R&&"error"!==Z;k.colorDefault=!D,delete k.ownerState;let C=h(k),[H,O]=(0,g.Z)("img",{className:C.img,elementType:y,externalForwardedProps:{slots:d,slotProps:{img:{...p,...f.img}}},additionalProps:{alt:a,src:w,srcSet:v,sizes:m},ownerState:k});return j=D?(0,u.jsx)(H,{...O}):s||0===s?s:R&&a?a[0]:(0,u.jsx)(b,{ownerState:k,className:C.fallback}),(0,u.jsx)(x,{as:c,className:(0,o.Z)(C.root,i),ref:t,...I,ownerState:k,children:j})})},40256:function(e,t,r){"use strict";r.d(t,{$R:function(){return u},A_:function(){return i},BO:function(){return a},GH:function(){return d},_n:function(){return o},be:function(){return s},iG:function(){return c},j0:function(){return l}});var n=r(83464);let o="http://localhost:8080",a="<EMAIL>,<EMAIL>,<EMAIL>".split(",").map(e=>e.trim()),s=n.Z.create({baseURL:o});async function i(e,t,r){let n=new URL(r?"".concat(r).concat(e):"".concat(o).concat(e));return t&&Object.keys(t).forEach(e=>n.searchParams.append(e,t[e])),(await s.get(n.toString())).data}async function l(e,t,r){let n=r?"".concat(r).concat(e):"".concat(o).concat(e),a=await s.post(n,t,{headers:{"Content-Type":"application/json"}});return{status:a.status,data:a.data}}async function c(e,t,r){let n=r?"".concat(r).concat(e):"".concat(o).concat(e);console.log("Document upload to: ".concat(n));let a=await s.post(n,t,{headers:{"Content-Type":"multipart/form-data"}});return{status:a.status,data:a.data}}async function u(e,t,r){let n=new URL(r?"".concat(r).concat(e):"".concat(o).concat(e));return t&&Object.keys(t).forEach(e=>n.searchParams.append(e,t[e])),console.log("GET Blob request to: ".concat(n.toString())),(await s.get(n.toString(),{responseType:"blob"})).data}async function d(e,t,r){let n=r?"".concat(r).concat(e):"".concat(o).concat(e),a=await s.put(n,t,{headers:{"Content-Type":"application/json"}});return{status:a.status,data:a.data}}s.interceptors.request.use(e=>{let t=localStorage.getItem("userid1")||localStorage.getItem("userId");return t?e.headers["user-id"]=t:console.warn("No user ID found in localStorage for API request"),e})},1358:function(e,t,r){"use strict";r.r(t);var n=r(45008),o=r(57437),a=r(2265),s=r(68575),i=r(73261),l=r(67051),c=r(46387),u=r(95656),d=r(15273),f=r(67116),p=r(94013),m=r(97404),g=r(76792),h=r(27345),x=r(3146),y=r(33145),b=r(48223),w=r(56336),v=r(13571),S=r(70623);function I(){let e=(0,n._)(["\n    0% { content: ''; }\n    25% { content: '.'; }\n    50% { content: '..'; }\n    75% { content: '...'; }\n    100% { content: ''; }\n  "]);return I=function(){return e},e}t.default=(0,v.Z)(()=>{let e=(0,s.I0)(),t=(0,s.v9)(e=>(0,S.MP)(e)),r=(0,s.v9)(e=>e.user._id),n=(0,s.v9)(e=>e.user.userProfile),v=(0,s.v9)(e=>e.user.selectedFAQQuestion),j=(0,s.v9)(e=>e.qHarmonyBot.chatHistory),k=(0,s.v9)(e=>e.qHarmonyBot.isLoading),[Z,R]=(0,a.useState)(""),D=(0,a.useRef)(null),C=n=>{if(""===n.trim())return;let o={sender:"user",message:n.replace(/\n/g,"<br/>"),timestamp:new Date().toISOString()};e((0,g.Hz)(o)),e((0,g.wt)(!0)),(0,h.b)(e,n,r,t),R("")},H=e=>{if(!e)return"";let[t,r]=e.split(" ");return"".concat(t[0].toUpperCase()).concat(r?r[0].toUpperCase():"")},O=()=>{var e;null===(e=D.current)||void 0===e||e.scrollIntoView({behavior:"smooth"})};(0,a.useEffect)(()=>{v&&(C(v),e((0,S.ki)()))},[v,e]),(0,a.useEffect)(()=>{0===j.length&&n.name&&!v&&(e((0,g.wt)(!0)),setTimeout(()=>{let t={sender:"bot",message:"Hey ".concat(n.name,", how can I help you today?"),timestamp:new Date().toISOString()};e((0,g.Hz)(t)),e((0,g.wt)(!1))},2e3))},[j.length,n.name,e,v]),(0,a.useEffect)(()=>{O()},[j]);let P=(0,x.F4)(I()),T=["View My Benefits","Enroll Now","Check Time Off","Update My Elections"];return(0,o.jsx)(b.Z,{children:(0,o.jsxs)(u.Z,{component:"main",sx:{flexGrow:1,display:"flex",flexDirection:"column",height:"95vh",bgcolor:"#f6f8fc"},children:[(0,o.jsx)(c.Z,{variant:"h5",sx:{fontWeight:"bold",p:2},children:"Brea - Your Round-the-Clock Benefits Expert"}),(0,o.jsx)(u.Z,{sx:{flexGrow:1,overflow:"auto",p:2},children:(0,o.jsxs)(d.Z,{children:[j.map((e,t)=>(0,o.jsxs)(i.ZP,{sx:{display:"flex",flexDirection:"user"===e.sender?"row-reverse":"row",alignItems:"flex-start"},children:[(0,o.jsx)(f.Z,{sx:{bgcolor:"user"===e.sender?"#000":"transparent",mr:"user"===e.sender?0:2,ml:"user"===e.sender?2:0,mt:.5},children:"user"===e.sender?(0,o.jsx)(f.Z,{sx:{bgcolor:"black",color:"#ffffff",width:35,height:35,fontSize:"1.2rem",mr:1.5,ml:1.5,display:"flex",alignItems:"center",justifyContent:"center",paddingBottom:"1px",fontWeight:800},children:H(n.name)}):(0,o.jsx)(y.default,{src:w.Z,alt:"AI Assistant",style:{borderRadius:"50%",width:"40px",height:"40px"}})}),(0,o.jsxs)(u.Z,{sx:{maxWidth:"80%"},children:[(0,o.jsx)(l.Z,{primary:(0,o.jsx)("span",{dangerouslySetInnerHTML:{__html:"bot"===e.sender?"".concat(e.message,'<br/><small style="color: gray;">AI-generated content—verify before use.</small>'):e.message},style:{whiteSpace:"pre-wrap",wordBreak:"break-word"}}),sx:{bgcolor:"user"===e.sender?"#000":"#fff",color:"user"===e.sender?"#fff":"#000",borderRadius:"user"===e.sender?"16px 16px 4px 16px":"16px 16px 16px 4px",padding:"10px",mb:1}}),"bot"===e.sender&&e.message.includes("how can I help you today?")&&t===j.length-1&&(0,o.jsx)(u.Z,{sx:{display:"flex",mt:1},children:T.map(e=>(0,o.jsx)(p.Z,{variant:"contained",sx:{mr:1,textTransform:"none",fontSize:"12px",borderRadius:"6px",px:2,py:1,bgcolor:"black",color:"white"},onClick:()=>C(e),children:e},e))})]})]},t)),k&&(0,o.jsx)(()=>(0,o.jsxs)(i.ZP,{sx:{display:"flex",alignItems:"flex-start"},children:[(0,o.jsx)(y.default,{src:w.Z,alt:"AI Chat",style:{borderRadius:"50%",width:"40px",height:"40px",marginRight:"10px"}}),(0,o.jsx)(l.Z,{primary:(0,o.jsx)(c.Z,{component:"span",sx:{display:"inline-block",fontSize:"16px","&::after":{content:"''",animation:"".concat(P," 1.5s infinite"),display:"inline-block",width:"16px"}},children:"Brea is typing"}),sx:{bgcolor:"#fff",borderRadius:"20px",p:1.5,mb:1,maxWidth:"80%",mt:-.5}})]}),{}),(0,o.jsx)("div",{ref:D})]})}),(0,o.jsx)(u.Z,{sx:{p:2,borderTop:"1px solid #e0e0e0"},children:(0,o.jsxs)(u.Z,{sx:{display:"flex",alignItems:"flex-start",bgcolor:"#fff",borderRadius:"20px",height:"150px",boxShadow:"0 1px 3px rgba(0, 0, 0, 0.1)"},children:[(0,o.jsx)(m.Z,{fullWidth:!0,variant:"outlined",placeholder:"Type your message...",value:Z,onChange:e=>R(e.target.value),onKeyPress:e=>{"Enter"!==e.key||e.shiftKey||(e.preventDefault(),C(Z))},multiline:!0,rows:5,sx:{mr:2,borderRadius:"20px",fontSize:"16px",height:"150px","& .MuiOutlinedInput-root":{padding:"12px",border:"none",display:"flex",alignItems:"flex-start"},"& .MuiOutlinedInput-notchedOutline":{border:"none"}}}),(0,o.jsx)(p.Z,{variant:"contained",sx:{bgcolor:Z?"#1073ff":"#e0e0e0",color:"#fff",borderRadius:"25px",m:2,p:0,fontSize:"16px",height:"45px",width:"60px",boxShadow:"none",textTransform:"none","&:hover":{bgcolor:Z?"#005bb5":"#d0d0d0"}},onClick:()=>C(Z),children:"Send"})]})})]})})})},27345:function(e,t,r){"use strict";r.d(t,{b:function(){return a}});var n=r(76792);let o=(0,r(61103).GU)();async function a(e,t,r,a){let s={user_id:r,user_message:t,team_id:a};try{console.log("Sending chat message:",s);let t=await fetch("".concat(o,"/chat"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(s)});if(!t.body)throw Error("Readable stream not supported");let r=t.body.getReader(),a=new TextDecoder("utf-8"),i={sender:"bot",message:"",timestamp:new Date().toISOString()};for(e((0,n.wt)(!0));;){let{done:t,value:o}=await r.read();if(t)break;let s=a.decode(o,{stream:!0});e((0,n.wt)(!1)),console.log("Chunk:",s),i.message+=s,e((0,n.Hz)({sender:"bot",message:s,timestamp:new Date().toISOString()}))}}catch(r){console.error("Error sending chat message:",r);let t={sender:"bot",message:"Sorry, I encountered an error while processing your request.",timestamp:new Date().toISOString()};e((0,n.Hz)(t)),e((0,n.wt)(!1))}}},76792:function(e,t,r){"use strict";r.d(t,{Hz:function(){return o},wt:function(){return s}});let n=(0,r(39129).oM)({name:"qHarmonyBot",initialState:{chatHistory:[],isLoading:!1},reducers:{addMessage:(e,t)=>{let{sender:r,message:n,timestamp:o}=t.payload;if(console.log("Adding message:",t.payload),"bot"===r&&e.chatHistory.length>0){let t=e.chatHistory[e.chatHistory.length-1];if("bot"===t.sender&&!t.timestamp.includes("Done")){t.message+=n,t.timestamp=o;return}}e.chatHistory.push(t.payload)},clearChatHistory:e=>{e.chatHistory=[]},setIsLoading:(e,t)=>{e.isLoading=t.payload}}}),{addMessage:o,clearChatHistory:a,setIsLoading:s}=n.actions;t.ZP=n.reducer},61103:function(e,t,r){"use strict";r.d(t,{GU:function(){return a},bR:function(){return n},n5:function(){return o}});let n=()=>"http://localhost:8080",o=()=>{let e="userid1",t="userId",r=localStorage.getItem(e)||localStorage.getItem(t);return(console.log("\uD83D\uDD0D getUserId debug:",{primaryKey:e,altKey:t,primaryValue:localStorage.getItem(e),altValue:localStorage.getItem(t),finalUserId:r}),r)?r:(console.error("❌ User ID not found in localStorage"),"default-user")},a=()=>"https://bot.benosphere.com"}},function(e){e.O(0,[139,3463,3301,8575,293,9810,187,3145,9932,3919,9129,2786,9826,8166,8760,7404,3344,9662,1356,2971,2117,1744],function(){return e(e.s=95743)}),_N_E=e.O()}]);