(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2649],{90827:function(e,t,r){Promise.resolve().then(r.bind(r,7847))},29821:function(e,t,r){"use strict";var o=r(94630),n=r(57437);t.Z=(0,o.Z)((0,n.jsx)("path",{d:"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2m-2 15-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8z"}),"CheckCircle")},8430:function(e,t,r){"use strict";var o=r(94630),n=r(57437);t.Z=(0,o.Z)((0,n.jsx)("path",{d:"M19 6.41 17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"}),"Close")},67116:function(e,t,r){"use strict";r.d(t,{Z:function(){return j}});var o=r(2265),n=r(61994),a=r(20801),s=r(16210),i=r(21086),l=r(37053),d=r(94630),c=r(57437),u=(0,d.Z)((0,c.jsx)("path",{d:"M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"}),"Person"),f=r(94143),x=r(50738);function p(e){return(0,x.ZP)("MuiAvatar",e)}(0,f.Z)("MuiAvatar",["root","colorDefault","circular","rounded","square","img","fallback"]);var m=r(79114);let h=e=>{let{classes:t,variant:r,colorDefault:o}=e;return(0,a.Z)({root:["root",r,o&&"colorDefault"],img:["img"],fallback:["fallback"]},p,t)},g=(0,s.default)("div",{name:"MuiAvatar",slot:"Root",overridesResolver:(e,t)=>{let{ownerState:r}=e;return[t.root,t[r.variant],r.colorDefault&&t.colorDefault]}})((0,i.Z)(e=>{let{theme:t}=e;return{position:"relative",display:"flex",alignItems:"center",justifyContent:"center",flexShrink:0,width:40,height:40,fontFamily:t.typography.fontFamily,fontSize:t.typography.pxToRem(20),lineHeight:1,borderRadius:"50%",overflow:"hidden",userSelect:"none",variants:[{props:{variant:"rounded"},style:{borderRadius:(t.vars||t).shape.borderRadius}},{props:{variant:"square"},style:{borderRadius:0}},{props:{colorDefault:!0},style:{color:(t.vars||t).palette.background.default,...t.vars?{backgroundColor:t.vars.palette.Avatar.defaultBg}:{backgroundColor:t.palette.grey[400],...t.applyStyles("dark",{backgroundColor:t.palette.grey[600]})}}}]}})),b=(0,s.default)("img",{name:"MuiAvatar",slot:"Img",overridesResolver:(e,t)=>t.img})({width:"100%",height:"100%",textAlign:"center",objectFit:"cover",color:"transparent",textIndent:1e4}),v=(0,s.default)(u,{name:"MuiAvatar",slot:"Fallback",overridesResolver:(e,t)=>t.fallback})({width:"75%",height:"75%"});var j=o.forwardRef(function(e,t){let r=(0,l.i)({props:e,name:"MuiAvatar"}),{alt:a,children:s,className:i,component:d="div",slots:u={},slotProps:f={},imgProps:x,sizes:p,src:j,srcSet:y,variant:Z="circular",...C}=r,S=null,k={...r,component:d,variant:Z},w=function(e){let{crossOrigin:t,referrerPolicy:r,src:n,srcSet:a}=e,[s,i]=o.useState(!1);return o.useEffect(()=>{if(!n&&!a)return;i(!1);let e=!0,o=new Image;return o.onload=()=>{e&&i("loaded")},o.onerror=()=>{e&&i("error")},o.crossOrigin=t,o.referrerPolicy=r,o.src=n,a&&(o.srcset=a),()=>{e=!1}},[t,r,n,a]),s}({...x,..."function"==typeof f.img?f.img(k):f.img,src:j,srcSet:y}),P=j||y,R=P&&"error"!==w;k.colorDefault=!R,delete k.ownerState;let I=h(k),[E,T]=(0,m.Z)("img",{className:I.img,elementType:b,externalForwardedProps:{slots:u,slotProps:{img:{...x,...f.img}}},additionalProps:{alt:a,src:j,srcSet:y,sizes:p},ownerState:k});return S=R?(0,c.jsx)(E,{...T}):s||0===s?s:P&&a?a[0]:(0,c.jsx)(v,{ownerState:k,className:I.fallback}),(0,c.jsx)(g,{as:d,className:(0,n.Z)(I.root,i),ref:t,...C,ownerState:k,children:S})})},12258:function(e,t,r){"use strict";r.d(t,{Z:function(){return h}});var o=r(2265),n=r(61994),a=r(20801),s=r(34765),i=r(16210),l=r(37053),d=r(46387),c=r(94143),u=r(50738);function f(e){return(0,u.ZP)("MuiDialogContentText",e)}(0,c.Z)("MuiDialogContentText",["root"]);var x=r(57437);let p=e=>{let{classes:t}=e,r=(0,a.Z)({root:["root"]},f,t);return{...t,...r}},m=(0,i.default)(d.Z,{shouldForwardProp:e=>(0,s.Z)(e)||"classes"===e,name:"MuiDialogContentText",slot:"Root",overridesResolver:(e,t)=>t.root})({});var h=o.forwardRef(function(e,t){let r=(0,l.i)({props:e,name:"MuiDialogContentText"}),{children:o,className:a,...s}=r,i=p(s);return(0,x.jsx)(m,{component:"p",variant:"body1",color:"textSecondary",ref:t,ownerState:s,className:(0,n.Z)(i.root,a),...r,classes:i})})},40256:function(e,t,r){"use strict";r.d(t,{$R:function(){return c},A_:function(){return i},BO:function(){return a},GH:function(){return u},_n:function(){return n},be:function(){return s},iG:function(){return d},j0:function(){return l}});var o=r(83464);let n="http://localhost:8080",a="<EMAIL>,<EMAIL>,<EMAIL>".split(",").map(e=>e.trim()),s=o.Z.create({baseURL:n});async function i(e,t,r){let o=new URL(r?"".concat(r).concat(e):"".concat(n).concat(e));return t&&Object.keys(t).forEach(e=>o.searchParams.append(e,t[e])),(await s.get(o.toString())).data}async function l(e,t,r){let o=r?"".concat(r).concat(e):"".concat(n).concat(e),a=await s.post(o,t,{headers:{"Content-Type":"application/json"}});return{status:a.status,data:a.data}}async function d(e,t,r){let o=r?"".concat(r).concat(e):"".concat(n).concat(e);console.log("Document upload to: ".concat(o));let a=await s.post(o,t,{headers:{"Content-Type":"multipart/form-data"}});return{status:a.status,data:a.data}}async function c(e,t,r){let o=new URL(r?"".concat(r).concat(e):"".concat(n).concat(e));return t&&Object.keys(t).forEach(e=>o.searchParams.append(e,t[e])),console.log("GET Blob request to: ".concat(o.toString())),(await s.get(o.toString(),{responseType:"blob"})).data}async function u(e,t,r){let o=r?"".concat(r).concat(e):"".concat(n).concat(e),a=await s.put(o,t,{headers:{"Content-Type":"application/json"}});return{status:a.status,data:a.data}}s.interceptors.request.use(e=>{let t=localStorage.getItem("userid1")||localStorage.getItem("userId");return t?e.headers["user-id"]=t:console.warn("No user ID found in localStorage for API request"),e})},7847:function(e,t,r){"use strict";r.r(t),r.d(t,{default:function(){return T}});var o=r(57437),n=r(2265),a=r(95656),s=r(89414),i=r(46387),l=r(94013),d=r(35389),c=r(67116),u=r(59832),f=r(8350),x=r(53392),p=r(79507),m=r(77468),h=r(12258),g=r(97404),b=r(9026),v=r(13571),j=r(68575),y=r(39547),Z=r(8430),C=r(29821),S=e=>{let{open:t,onClose:r,member:a}=e,i=(0,j.I0)(),[c,f]=(0,n.useState)(""),[h,v]=(0,n.useState)(""),[S,k]=(0,n.useState)(""),[w,P]=(0,n.useState)(""),[R,I]=(0,n.useState)(""),[E,T]=(0,n.useState)(""),[F,z]=(0,n.useState)(""),[N,A]=(0,n.useState)(!1),[D,M]=(0,n.useState)(""),[_,L]=(0,n.useState)({firstName:!1,lastName:!1,email:!1,phoneNumber:!1}),q=()=>{let e={firstName:!c,lastName:!h,email:!S,phoneNumber:!w};return L(e),!Object.values(e).some(e=>e)};(0,n.useEffect)(()=>{if(a){var e,t,r;f(a.name.split(" ")[0]),v(a.name.split(" ")[1]),k(a.email),P((null===(e=a.details)||void 0===e?void 0:e.phoneNumber)||""),I((null===(t=a.details)||void 0===t?void 0:t.department)||""),T((null===(r=a.details)||void 0===r?void 0:r.title)||"")}else f(""),v(""),k(""),P(""),I(""),T("")},[a]);let W=async()=>{if(!q()){M("Please fill out all required fields.");return}A(!0),M("");let e={name:"".concat(c," ").concat(h),email:S,phoneNumber:w,department:R,title:E};a?(console.log("Editing team member:",e),await (0,y.Nq)(i,a._id,e)):(console.log("Adding team member:",e),await (0,y.yu)(i,[e])),await (0,y.TQ)(i),A(!1),z(a?"Team member updated successfully!":"Team member added successfully!"),setTimeout(()=>{a?(z(""),r()):(z(""),f(""),v(""),k(""),P(""),I(""),T(""))},1500)};return(0,o.jsxs)(x.Z,{open:t,onClose:r,PaperProps:{style:{borderRadius:"16px",boxShadow:"0px 10px 30px rgba(0, 0, 0, 0.1)",padding:"5px",width:"550px"}},children:[(0,o.jsxs)(p.Z,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",fontWeight:"bold",fontSize:"1.5rem"},children:[a?"Edit Team Member":"Add Team Member",(0,o.jsx)(u.Z,{onClick:r,children:(0,o.jsx)(Z.Z,{})})]}),(0,o.jsxs)(m.Z,{children:[(0,o.jsxs)(s.ZP,{container:!0,spacing:3,sx:{marginBottom:"16px",marginTop:"0px"},children:[(0,o.jsx)(s.ZP,{item:!0,xs:12,children:(0,o.jsx)(g.Z,{fullWidth:!0,label:"First name",variant:"outlined",value:c,onChange:e=>f(e.target.value),required:!0,error:_.firstName,InputProps:{style:{borderRadius:"8px",backgroundColor:"#f9f9f9",height:"48px"}},InputLabelProps:{shrink:!0,style:{fontSize:"1rem"}},sx:{alignItems:"flex-start"}})}),(0,o.jsx)(s.ZP,{item:!0,xs:12,children:(0,o.jsx)(g.Z,{fullWidth:!0,label:"Last name",variant:"outlined",value:h,onChange:e=>v(e.target.value),required:!0,error:_.lastName,InputProps:{style:{borderRadius:"8px",backgroundColor:"#f9f9f9",height:"48px"}},InputLabelProps:{shrink:!0,style:{fontSize:"1rem"}},sx:{alignItems:"flex-start"}})}),(0,o.jsx)(s.ZP,{item:!0,xs:12,children:(0,o.jsx)(g.Z,{fullWidth:!0,label:"Email",variant:"outlined",value:S,onChange:e=>k(e.target.value),required:!0,error:_.email,InputProps:{style:{borderRadius:"8px",backgroundColor:"#f9f9f9",height:"48px"}},InputLabelProps:{shrink:!0,style:{fontSize:"1rem"}},sx:{alignItems:"flex-start"},disabled:!!a})}),(0,o.jsx)(s.ZP,{item:!0,xs:12,children:(0,o.jsx)(g.Z,{fullWidth:!0,label:"Phone number",variant:"outlined",value:w,onChange:e=>P(e.target.value),required:!0,error:_.phoneNumber,InputProps:{style:{borderRadius:"8px",backgroundColor:"#f9f9f9",height:"48px"}},InputLabelProps:{shrink:!0,style:{fontSize:"1rem"}},sx:{alignItems:"flex-start"}})}),(0,o.jsx)(s.ZP,{item:!0,xs:12,children:(0,o.jsx)(g.Z,{fullWidth:!0,label:"Department",variant:"outlined",value:R,onChange:e=>I(e.target.value),InputProps:{style:{borderRadius:"8px",backgroundColor:"#f9f9f9",height:"48px"}},InputLabelProps:{shrink:!0,style:{fontSize:"1rem"}},sx:{alignItems:"flex-start"}})}),(0,o.jsx)(s.ZP,{item:!0,xs:12,children:(0,o.jsx)(g.Z,{fullWidth:!0,label:"Title",variant:"outlined",value:E,onChange:e=>T(e.target.value),InputProps:{style:{borderRadius:"8px",backgroundColor:"#f9f9f9",height:"48px"}},InputLabelProps:{shrink:!0,style:{fontSize:"1rem"}},sx:{alignItems:"flex-start"}})})]}),N&&(0,o.jsx)(d.Z,{}),D&&(0,o.jsx)("div",{style:{color:"red",marginTop:"10px",display:"flex",alignItems:"center"},children:D}),F&&(0,o.jsxs)("div",{style:{color:"green",marginTop:"10px",display:"flex",alignItems:"center"},children:[(0,o.jsx)(C.Z,{style:{marginRight:"5px"}}),F]})]}),(0,o.jsx)(b.Z,{sx:{padding:"16px"},children:(0,o.jsx)(l.Z,{onClick:W,sx:{color:"#ffffff",backgroundColor:"#000000",borderRadius:"12px",padding:"8px 24px",textTransform:"none",fontWeight:"bold","&:hover":{backgroundColor:"#333333"}},disabled:N,children:N?a?"Saving...":"Adding...":a?"Save Changes":"Add"})})]})},k=r(48223),w=r(83337),P=(0,r(94630).Z)((0,o.jsx)("path",{d:"M14.59 8 12 10.59 9.41 8 8 9.41 10.59 12 8 14.59 9.41 16 12 13.41 14.59 16 16 14.59 13.41 12 16 9.41zM12 2C6.47 2 2 6.47 2 12s4.47 10 10 10 10-4.47 10-10S17.53 2 12 2m0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8"}),"HighlightOff");let R=["#FFB6C1","#FF6347","#FFD700","#90EE90","#00CED1","#1E90FF","#BA55D3"],I=e=>{let[t,r]=e.split(" ");return"".concat(t[0]).concat(r?r[0]:"")},E=e=>R[e%R.length];var T=(0,v.Z)(()=>{let e=(0,j.I0)(),t=(0,w.C)(e=>e.user._id),r=(0,j.v9)(e=>e.company.companyTeamMembers),[v,Z]=(0,n.useState)(!1),[C,R]=(0,n.useState)(null),[T,F]=(0,n.useState)(!1),[z,N]=(0,n.useState)(null),[A,D]=(0,n.useState)(""),[M,_]=(0,n.useState)(null),[L,q]=(0,n.useState)(!1),[W,O]=(0,n.useState)(!0),[B,Q]=(0,n.useState)(null),G=()=>Z(!0),H=e=>{console.log("Editing team member:",e),R(e),G()},U=async(e,t)=>{_(e),console.log("Response from sendLoginLinkToEmployee:",await (0,y.mb)(e,t)),_(null)},J=(e,t)=>{N({id:e,companyId:t}),F(!0)},$=()=>{F(!1),N(null),D("")},K=async()=>{z&&(q(!0),await (0,y.JZ)(z.id,z.companyId)?(await (0,y.TQ)(e),$()):console.error("Failed to offboard employee"),q(!1))},V=async r=>{Q(r),await (0,y.gt)(r,t),await (0,y.TQ)(e),Q(null),$()};return(0,n.useEffect)(()=>{(async()=>{O(!0),await (0,y.TQ)(e),O(!1)})()},[e]),(0,o.jsx)(k.Z,{children:(0,o.jsxs)(a.Z,{sx:{bgcolor:"#F5F6FA",p:4,width:"100%",height:"95vh",overflow:"auto"},children:[(0,o.jsxs)(s.ZP,{container:!0,alignItems:"center",justifyContent:"space-between",sx:{mb:2},children:[(0,o.jsx)(s.ZP,{item:!0,children:(0,o.jsx)(i.Z,{variant:"h5",children:"Team Members"})}),(0,o.jsx)(s.ZP,{item:!0,children:(0,o.jsx)(l.Z,{variant:"contained",color:"primary",onClick:()=>{G()},sx:{backgroundColor:"#000000",textTransform:"none",borderRadius:"6px"},children:"Add new member"})})]}),(0,o.jsx)(a.Z,{sx:{bgcolor:"#ffffff",borderRadius:"12px",width:"100%",p:2,boxShadow:"0px 4px 12px rgba(0, 0, 0, 0.1)"},children:W?(0,o.jsx)(a.Z,{sx:{display:"flex",justifyContent:"center",p:4},children:(0,o.jsx)(d.Z,{})}):r.map((e,n)=>(0,o.jsxs)(a.Z,{children:[(0,o.jsxs)(s.ZP,{container:!0,alignItems:"center",spacing:2,children:[(0,o.jsxs)(s.ZP,{item:!0,xs:3,container:!0,alignItems:"center",children:[(0,o.jsx)(c.Z,{sx:{bgcolor:E(n),color:"#ffffff",width:48,height:48,fontSize:"1.2rem",mr:2},children:I(e.name)}),(0,o.jsxs)(a.Z,{children:[(0,o.jsx)(i.Z,{variant:"body1",sx:{fontWeight:"bold"},children:e.name}),!e.isActivated&&(0,o.jsx)(i.Z,{variant:"body2",color:"error",children:"Pending Activation"})]})]}),(0,o.jsx)(s.ZP,{item:!0,xs:3,children:(0,o.jsx)(i.Z,{variant:"body2",children:e.role})}),(0,o.jsx)(s.ZP,{item:!0,xs:3,children:(0,o.jsx)(i.Z,{variant:"body2",children:e.email})}),(0,o.jsx)(s.ZP,{item:!0,xs:3,container:!0,justifyContent:"flex-end",children:e.isDisabled?(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)(i.Z,{variant:"body2",color:"error",sx:{mr:2},children:"Disabled"}),B===e._id?(0,o.jsx)(d.Z,{size:24}):(0,o.jsx)(l.Z,{variant:"contained",onClick:()=>V(e._id),sx:{backgroundColor:"#f0f0f0",borderRadius:"8px",textTransform:"none",color:"#000",padding:"4px",marginRight:3,boxShadow:"none",border:"none","&:hover":{backgroundColor:"#E0E0E0",boxShadow:"none"}},children:"Enable"})]}):(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)(l.Z,{variant:"contained",onClick:()=>H(e),sx:{backgroundColor:"#f0f0f0",borderRadius:"8px",textTransform:"none",color:"#000",padding:"4px",marginRight:3,boxShadow:"none",border:"none","&:hover":{backgroundColor:"#E0E0E0",boxShadow:"none"}},children:"Edit"}),M===e._id?(0,o.jsx)(d.Z,{size:24}):(0,o.jsx)(l.Z,{variant:"contained",onClick:()=>U(e._id,e.companyId),sx:{backgroundColor:"#f0f0f0",borderRadius:"8px",textTransform:"none",color:"#000",padding:"4px",marginRight:3,boxShadow:"none",border:"none","&:hover":{backgroundColor:"#E0E0E0",boxShadow:"none"}},children:"Send Login Link"}),L&&z.id===e._id?(0,o.jsx)(d.Z,{size:24}):e._id!==t&&(0,o.jsx)(u.Z,{onClick:()=>J(e._id,e.companyId),sx:{backgroundColor:"#f0f0f0",borderRadius:"8px",textTransform:"none",color:"#000",padding:"4px",marginRight:3,boxShadow:"none",border:"none","&:hover":{backgroundColor:"#E0E0E0",boxShadow:"none"}},children:(0,o.jsx)(P,{})})]})})]}),n<r.length-1&&(0,o.jsx)(f.Z,{sx:{my:2}})]},n))}),(0,o.jsx)(S,{open:v,onClose:()=>{Z(!1),R(null)},member:C}),(0,o.jsxs)(x.Z,{open:T,onClose:$,PaperProps:{style:{borderRadius:"16px",boxShadow:"0px 10px 30px rgba(0, 0, 0, 0.1)"}},children:[(0,o.jsx)(p.Z,{sx:{fontWeight:"bold",fontSize:"1.5rem",color:"#000000"},children:"Confirm Deletion"}),(0,o.jsxs)(m.Z,{children:[(0,o.jsxs)(h.Z,{sx:{color:"#6c757d",fontSize:"1rem",mb:2},children:['To confirm deletion, please type "',(0,o.jsx)("strong",{style:{color:"black"},children:"remove employee"}),'" in the box below.']}),(0,o.jsx)(g.Z,{autoFocus:!0,fullWidth:!0,variant:"outlined",value:A,onChange:e=>D(e.target.value),InputProps:{style:{borderRadius:"12px",backgroundColor:"#f9f9f9"}}})]}),(0,o.jsxs)(b.Z,{sx:{padding:"16px"},children:[(0,o.jsx)(l.Z,{onClick:$,sx:{color:"#6c757d",backgroundColor:"#ffffff",borderRadius:"8px",padding:"8px 16px",textTransform:"none",boxShadow:"none","&:hover":{backgroundColor:"#f0f0f0"}},children:"Cancel"}),(0,o.jsx)(l.Z,{onClick:K,disabled:"remove employee"!==A||L,sx:{color:"#ffffff",backgroundColor:"remove employee"===A?"#1073ff":"#9E9E9E",borderRadius:"8px",padding:"8px 16px",textTransform:"none","&:hover":{backgroundColor:"remove employee"===A?"#0d62d3":"#9E9E9E"}},children:L?(0,o.jsx)(d.Z,{size:24,sx:{color:"#ffffff"}}):"Confirm"})]})]})]})})})}},function(e){e.O(0,[139,3463,3301,8575,8685,187,1423,9932,3919,9129,2786,9826,8166,8760,9414,7404,3209,3344,9662,1356,2971,2117,1744],function(){return e(e.s=90827)}),_N_E=e.O()}]);