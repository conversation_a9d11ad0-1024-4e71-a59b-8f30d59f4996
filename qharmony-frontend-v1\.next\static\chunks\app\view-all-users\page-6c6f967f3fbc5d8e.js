(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9312,7534],{25121:function(e,t,o){Promise.resolve().then(o.bind(o,45773))},40256:function(e,t,o){"use strict";o.d(t,{$R:function(){return u},A_:function(){return c},BO:function(){return r},GH:function(){return d},_n:function(){return a},be:function(){return s},iG:function(){return i},j0:function(){return l}});var n=o(83464);let a="http://localhost:8080",r="<EMAIL>,<EMAIL>,<EMAIL>".split(",").map(e=>e.trim()),s=n.Z.create({baseURL:a});async function c(e,t,o){let n=new URL(o?"".concat(o).concat(e):"".concat(a).concat(e));return t&&Object.keys(t).forEach(e=>n.searchParams.append(e,t[e])),(await s.get(n.toString())).data}async function l(e,t,o){let n=o?"".concat(o).concat(e):"".concat(a).concat(e),r=await s.post(n,t,{headers:{"Content-Type":"application/json"}});return{status:r.status,data:r.data}}async function i(e,t,o){let n=o?"".concat(o).concat(e):"".concat(a).concat(e);console.log("Document upload to: ".concat(n));let r=await s.post(n,t,{headers:{"Content-Type":"multipart/form-data"}});return{status:r.status,data:r.data}}async function u(e,t,o){let n=new URL(o?"".concat(o).concat(e):"".concat(a).concat(e));return t&&Object.keys(t).forEach(e=>n.searchParams.append(e,t[e])),console.log("GET Blob request to: ".concat(n.toString())),(await s.get(n.toString(),{responseType:"blob"})).data}async function d(e,t,o){let n=o?"".concat(o).concat(e):"".concat(a).concat(e),r=await s.put(n,t,{headers:{"Content-Type":"application/json"}});return{status:r.status,data:r.data}}s.interceptors.request.use(e=>{let t=localStorage.getItem("userid1")||localStorage.getItem("userId");return t?e.headers["user-id"]=t:console.warn("No user ID found in localStorage for API request"),e})},45773:function(e,t,o){"use strict";o.r(t);var n=o(57437),a=o(2265),r=o(95656),s=o(22095),c=o(53410),l=o(70208),i=o(32281),u=o(54301),d=o(61201),m=o(59469),g=o(48223),h=o(40256);t.default=()=>{let[e,t]=(0,a.useState)([]);async function o(){let e=await (0,h.A_)("/users?apiKey=24$FrostySnow");console.log(e),t(e.users)}return(0,a.useEffect)(()=>{o()},[]),(0,n.jsx)(g.Z,{children:(0,n.jsx)(r.Z,{sx:{bgcolor:"#F5F6F8",height:"95vh",padding:"32px",overflow:"auto"},children:(0,n.jsx)(s.Z,{component:c.Z,children:(0,n.jsxs)(l.Z,{children:[(0,n.jsx)(i.Z,{children:(0,n.jsxs)(u.Z,{children:[(0,n.jsx)(d.Z,{children:"Name"}),(0,n.jsx)(d.Z,{children:"Email"}),(0,n.jsx)(d.Z,{children:"Is Broker"}),(0,n.jsx)(d.Z,{children:"Is Admin"})]})}),(0,n.jsx)(m.Z,{children:e.map((e,t)=>(0,n.jsxs)(u.Z,{children:[(0,n.jsx)(d.Z,{children:e.name}),(0,n.jsx)(d.Z,{children:e.email}),(0,n.jsx)(d.Z,{children:e.isBroker?"Yes":"No"}),(0,n.jsx)(d.Z,{children:e.isAdmin?"Yes":"No"})]},t))})]})})})})}},47369:function(e,t,o){"use strict";o.d(t,{AuthProvider:function(){return f},a:function(){return p}});var n=o(57437),a=o(73143),r=o(2265),s=o(28411),c=o(43301),l=o(71986),i=o(84392);let u={auth:{clientId:"08e8620a-5979-4a37-b279-b2a92a75f515",authority:"https://login.microsoftonline.com/ca41443d-acdd-4223-9c81-dcaeb58b3406",redirectUri:window.location.hostname.includes("test.benosphere.com")?"https://test.benosphere.com/teams-landing":"https://app.benosphere.com/teams-landing",navigateToLoginRequestUrl:!1},cache:{cacheLocation:"sessionStorage",storeAuthStateInCookie:!1},system:{allowRedirectInIframe:!0,loggerOptions:{loggerCallback:(e,t)=>{console.log(t)},logLevel:l.i.Verbose}}},d=new i.Lx(u);var m=o(77534),g=o(99376);let h=(0,r.createContext)(void 0),f=e=>{let{children:t}=e,[o,l]=(0,r.useState)(null),[i,u]=(0,r.useState)(!0),f=(0,g.useRouter)(),p=()=>{Object.keys(localStorage).forEach(e=>{localStorage.removeItem(e)}),localStorage.removeItem("userid1"),localStorage.removeItem("userEmail1"),localStorage.removeItem("isTeamsApp1"),localStorage.removeItem("companyId1"),localStorage.removeItem("firstTimeLogin1"),localStorage.removeItem("wellness_results"),localStorage.removeItem("wellness_user_answers"),console.log("All localStorage items cleared")},I=()=>{document.cookie.split(";").forEach(e=>{let t=e.split("=")[0].trim();document.cookie="".concat(t,"=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;")}),console.log("All cookies cleared")};return(0,r.useEffect)(()=>{let e=(0,c.Aj)(s.I,e=>{if(e)console.log("Firebase user exists:",e),l(e);else{let e=localStorage.getItem("ssoDone1"),t=localStorage.getItem("userid1");console.log("isSSODone",e),console.log("userid1",t),a.j2().then(async()=>{var e,t,o,n,r;let s=await a.fw();console.log("Current user teams context:",null===(e=s.user)||void 0===e?void 0:e.loginHint);let c=null===(t=s.user)||void 0===t?void 0:t.loginHint,i=null===(n=s.user)||void 0===n?void 0:null===(o=n.tenant)||void 0===o?void 0:o.id;localStorage.setItem("userEmail1",c),localStorage.setItem("isTeamsApp1","true");let u=await (0,m.N8)(c,i);if("login_user"===u.data){console.log("Onboarding successful:",u);let e=u.userId,t=u.companyId;localStorage.setItem("userid1",e),localStorage.setItem("companyId1",t),localStorage.setItem("ssoDone1","true"),l(null===(r=s.user)||void 0===r?void 0:r.loginHint),f.push("/dashboard")}else f.push("/teams-landing")})}u(!1)});return()=>e()},[]),(0,n.jsx)(h.Provider,{value:{user:o,loading:i,logout:()=>{(0,c.w7)(s.I).then(()=>{console.log("Firebase user signed out"),l(null),p(),I(),f.push("/login")}).catch(e=>{console.error("Error signing out: ",e)}),d.logoutRedirect().catch(e=>{console.error("Error signing out from Microsoft: ",e)})},setUser:l},children:t})},p=()=>{let e=(0,r.useContext)(h);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}},48223:function(e,t,o){"use strict";var n=o(57437),a=o(2265),r=o(47369),s=o(99376),c=o(83337),l=o(70623),i=o(39547),u=o(35389),d=o(95656);let m=()=>/Mobi|Android/i.test(navigator.userAgent);t.Z=e=>{let{children:t}=e,{user:o,loading:g}=(0,r.a)(),h=(0,s.useRouter)(),f=(0,s.usePathname)(),p=(0,c.T)(),[I,y]=(0,a.useState)(!1),v=(0,c.C)(e=>e.user.userProfile);return((0,a.useEffect)(()=>{{let e=localStorage.getItem("userid1")||localStorage.getItem("userId");console.log("USER ID FROM LOCAL STORAGE: ",e),e&&!v.name&&(p((0,l.Iv)(e)),(async()=>{try{await (0,i.M_)(p,e),await (0,i.aK)(p)}catch(e){console.error("Error fetching user data in ProtectedRoute:",e)}})())}},[p,v.name]),(0,a.useEffect)(()=>{console.log("ProtectedRoute useEffect triggered"),console.log("Current user: ",o),console.log("Loading state: ",g),console.log("Current user details: ",v),g||o||(console.log("User not authenticated, redirecting to home"),y(!1),h.push("/")),!g&&v.companyId&&""===v.companyId&&(console.log("Waiting to retrieve company details"),y(!1)),!g&&v.companyId&&""!==v.companyId&&(console.log("User found, rendering children"),y(!0)),m()&&!f.startsWith("/mobile")&&(console.log("Redirecting to mobile version of ".concat(f)),h.push("/mobile".concat(f)))},[o,g,v,h,f]),I)?o?(0,n.jsx)(n.Fragment,{children:t}):null:(0,n.jsx)(d.Z,{sx:{display:"flex",justifyContent:"center",alignItems:"center",height:"100vh",bgcolor:"#f6f8fc"},children:(0,n.jsx)(u.Z,{})})}},77534:function(e,t,o){"use strict";o.d(t,{Ig:function(){return c},N8:function(){return s},lY:function(){return a},selfOnboard:function(){return r}});var n=o(40256);async function a(e){return(await (0,n.j0)("/auth/parse-params",{link:e})).data}async function r(e){return(await (0,n.j0)("/user/self-onboard",{userEmail:e})).data.data}async function s(e,t){return(await (0,n.j0)("/teams/user/self-onboard",{userEmail:e,tenantId:t})).data}async function c(e,t){let o=await (0,n.j0)("/employee/onboard",{companyId:e,userId:t});return console.log("Response from onboardEmployee:",o),o.data}},28411:function(e,t,o){"use strict";o.d(t,{I:function(){return c}});var n=o(738),a=o(43301);let r=function(){{let e=window.location.hostname;if(e.includes("test.benosphere.com")||"localhost"===e)return{apiKey:"AIzaSyCPGurROOIHelcNrg3KK3UZpT5NY_v33cw",authDomain:"qharmony-test.firebaseapp.com",projectId:"qharmony-test",storageBucket:"qharmony-test.firebasestorage.app",messagingSenderId:"1017404738235",appId:"1:1017404738235:web:d0566182eb575065e3379e"}}return{apiKey:"AIzaSyAOalm98qF_u74s3qQlIqQ5A6IpWyd0wKA",authDomain:"qharmony-dev.firebaseapp.com",projectId:"qharmony-dev",storageBucket:"qharmony-dev.appspot.com",messagingSenderId:"756187162353",appId:"1:756187162353:web:3fc7d63dee1c57bc9d6b50"}}(),s=(0,n.C6)().length?(0,n.C6)()[0]:(0,n.ZF)(r),c=(0,a.v0)(s)}},function(e){e.O(0,[139,3463,3301,8575,293,9810,9932,9129,1711,7248,3344,2971,2117,1744],function(){return e(e.s=25121)}),_N_E=e.O()}]);