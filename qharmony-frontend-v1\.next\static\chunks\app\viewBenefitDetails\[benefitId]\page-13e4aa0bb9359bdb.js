(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5844],{42102:function(e,t,n){Promise.resolve().then(n.bind(n,62940))},26484:function(e,t,n){"use strict";var o=n(94630),r=n(57437);t.Z=(0,o.Z)((0,r.jsx)("path",{d:"M17.77 3.77 16 2 6 12l10 10 1.77-1.77L9.54 12z"}),"ArrowBackIosNew")},88506:function(e,t,n){"use strict";var o=n(94630),r=n(57437);t.Z=(0,o.Z)((0,r.jsx)("path",{d:"M19 19H5V5h7V3H5c-1.11 0-2 .9-2 2v14c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2v-7h-2zM14 3v2h3.59l-9.83 9.83 1.41 1.41L19 6.41V10h2V3z"}),"OpenInNew")},67116:function(e,t,n){"use strict";n.d(t,{Z:function(){return v}});var o=n(2265),r=n(61994),a=n(20801),i=n(16210),c=n(21086),l=n(37053),s=n(94630),d=n(57437),u=(0,s.Z)((0,d.jsx)("path",{d:"M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"}),"Person"),f=n(94143),p=n(50738);function m(e){return(0,p.ZP)("MuiAvatar",e)}(0,f.Z)("MuiAvatar",["root","colorDefault","circular","rounded","square","img","fallback"]);var g=n(79114);let h=e=>{let{classes:t,variant:n,colorDefault:o}=e;return(0,a.Z)({root:["root",n,o&&"colorDefault"],img:["img"],fallback:["fallback"]},m,t)},x=(0,i.default)("div",{name:"MuiAvatar",slot:"Root",overridesResolver:(e,t)=>{let{ownerState:n}=e;return[t.root,t[n.variant],n.colorDefault&&t.colorDefault]}})((0,c.Z)(e=>{let{theme:t}=e;return{position:"relative",display:"flex",alignItems:"center",justifyContent:"center",flexShrink:0,width:40,height:40,fontFamily:t.typography.fontFamily,fontSize:t.typography.pxToRem(20),lineHeight:1,borderRadius:"50%",overflow:"hidden",userSelect:"none",variants:[{props:{variant:"rounded"},style:{borderRadius:(t.vars||t).shape.borderRadius}},{props:{variant:"square"},style:{borderRadius:0}},{props:{colorDefault:!0},style:{color:(t.vars||t).palette.background.default,...t.vars?{backgroundColor:t.vars.palette.Avatar.defaultBg}:{backgroundColor:t.palette.grey[400],...t.applyStyles("dark",{backgroundColor:t.palette.grey[600]})}}}]}})),b=(0,i.default)("img",{name:"MuiAvatar",slot:"Img",overridesResolver:(e,t)=>t.img})({width:"100%",height:"100%",textAlign:"center",objectFit:"cover",color:"transparent",textIndent:1e4}),y=(0,i.default)(u,{name:"MuiAvatar",slot:"Fallback",overridesResolver:(e,t)=>t.fallback})({width:"75%",height:"75%"});var v=o.forwardRef(function(e,t){let n=(0,l.i)({props:e,name:"MuiAvatar"}),{alt:a,children:i,className:c,component:s="div",slots:u={},slotProps:f={},imgProps:p,sizes:m,src:v,srcSet:w,variant:j="circular",...k}=n,Z=null,I={...n,component:s,variant:j},E=function(e){let{crossOrigin:t,referrerPolicy:n,src:r,srcSet:a}=e,[i,c]=o.useState(!1);return o.useEffect(()=>{if(!r&&!a)return;c(!1);let e=!0,o=new Image;return o.onload=()=>{e&&c("loaded")},o.onerror=()=>{e&&c("error")},o.crossOrigin=t,o.referrerPolicy=n,o.src=r,a&&(o.srcset=a),()=>{e=!1}},[t,n,r,a]),i}({...p,..."function"==typeof f.img?f.img(I):f.img,src:v,srcSet:w}),S=v||w,M=S&&"error"!==E;I.colorDefault=!M,delete I.ownerState;let C=h(I),[R,D]=(0,g.Z)("img",{className:C.img,elementType:b,externalForwardedProps:{slots:u,slotProps:{img:{...p,...f.img}}},additionalProps:{alt:a,src:v,srcSet:w,sizes:m},ownerState:I});return Z=M?(0,d.jsx)(R,{...D}):i||0===i?i:S&&a?a[0]:(0,d.jsx)(y,{ownerState:I,className:C.fallback}),(0,d.jsx)(x,{as:s,className:(0,r.Z)(C.root,c),ref:t,...k,ownerState:I,children:Z})})},40256:function(e,t,n){"use strict";n.d(t,{$R:function(){return d},A_:function(){return c},BO:function(){return a},GH:function(){return u},_n:function(){return r},be:function(){return i},iG:function(){return s},j0:function(){return l}});var o=n(83464);let r="http://localhost:8080",a="<EMAIL>,<EMAIL>,<EMAIL>".split(",").map(e=>e.trim()),i=o.Z.create({baseURL:r});async function c(e,t,n){let o=new URL(n?"".concat(n).concat(e):"".concat(r).concat(e));return t&&Object.keys(t).forEach(e=>o.searchParams.append(e,t[e])),(await i.get(o.toString())).data}async function l(e,t,n){let o=n?"".concat(n).concat(e):"".concat(r).concat(e),a=await i.post(o,t,{headers:{"Content-Type":"application/json"}});return{status:a.status,data:a.data}}async function s(e,t,n){let o=n?"".concat(n).concat(e):"".concat(r).concat(e);console.log("Document upload to: ".concat(o));let a=await i.post(o,t,{headers:{"Content-Type":"multipart/form-data"}});return{status:a.status,data:a.data}}async function d(e,t,n){let o=new URL(n?"".concat(n).concat(e):"".concat(r).concat(e));return t&&Object.keys(t).forEach(e=>o.searchParams.append(e,t[e])),console.log("GET Blob request to: ".concat(o.toString())),(await i.get(o.toString(),{responseType:"blob"})).data}async function u(e,t,n){let o=n?"".concat(n).concat(e):"".concat(r).concat(e),a=await i.put(o,t,{headers:{"Content-Type":"application/json"}});return{status:a.status,data:a.data}}i.interceptors.request.use(e=>{let t=localStorage.getItem("userid1")||localStorage.getItem("userId");return t?e.headers["user-id"]=t:console.warn("No user ID found in localStorage for API request"),e})},62940:function(e,t,n){"use strict";n.r(t);var o=n(57437),r=n(2265),a=n(83337),i=n(7022),c=n(99376),l=n(95656),s=n(94013),d=n(46387),u=n(89414),f=n(35389),p=n(26484),m=n(68575),g=n(39124),h=n(70623),x=n(88506),b=n(13571),y=n(48223),v=n(73143),w=n(60866),j=n(39547);t.default=(0,b.Z)(()=>{var e;let t=(0,c.useRouter)(),n=(0,a.T)(),{benefitId:b}=(0,c.useParams)(),[k,Z]=(0,r.useState)(!1),I=(0,m.v9)(e=>(0,h.MP)(e)),E=(0,a.C)(e=>e.benefits.documentsPerBenefit),S=(0,a.C)(e=>e.benefits.viewableDocuments),M=(0,m.v9)(e=>(0,g.d8)(e,b)),C=(0,a.C)(e=>e.benefits.loadingDocuments);(0,r.useEffect)(()=>{v.j2().then(()=>{v.fw().then(e=>{e.app.host.name===w.UB.teams&&Z(!0)})})},[]),(0,r.useEffect)(()=>{""!==b&&(0,i.v0)(n,b,I,"view_benefits")},[b,I,n]);let R=(e,t)=>{window.open("https://api.benosphere.com/benefits/document?objectKey=".concat(e,"&companyId=").concat(t),"_blank")};return(0,o.jsx)(y.Z,{children:(0,o.jsxs)(l.Z,{sx:{bgcolor:"#F5F6F8",height:"95vh",padding:"32px",overflow:"auto"},children:[(0,o.jsx)(l.Z,{sx:{display:"flex",alignItems:"center",mb:3},children:(0,o.jsx)(s.Z,{startIcon:(0,o.jsx)(p.Z,{sx:{fontSize:16}}),onClick:()=>{t.back()},sx:{color:"#6c757d",fontWeight:"normal",textTransform:"none",fontSize:"1.2rem","&:hover":{bgcolor:"transparent"}},children:M?(0,o.jsxs)(o.Fragment,{children:[(0,j.Ur)(M.benefitType)," /",(0,o.jsx)("span",{style:{fontWeight:"bold",color:"#000000",marginLeft:5},children:(0,j.dA)(M.benefit.subType)})]}):"Back"})}),(0,o.jsx)(d.Z,{sx:{fontWeight:800,fontSize:"42px",mb:0},children:(0,j.dA)((null==M?void 0:null===(e=M.benefit)||void 0===e?void 0:e.subType)||"")}),(0,o.jsx)(d.Z,{variant:"body1",sx:{color:"#6c757d",mb:6,fontSize:"16px"},children:"You can find all your health insurance details here, including coverage options, policy documents, and claim information."}),(0,o.jsxs)(u.ZP,{container:!0,spacing:3,alignItems:"flex-start",children:[(0,o.jsxs)(u.ZP,{item:!0,xs:12,children:[(0,o.jsx)(d.Z,{sx:{mb:3,fontWeight:700,fontSize:"24px"},children:"☕ Documents"}),0===E.documents.length?(0,o.jsx)(l.Z,{sx:{display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",minHeight:"150px",borderRadius:"8px",border:"2px dashed #e0e0e0",bgcolor:"#f9f9f9",p:4,textAlign:"left",maxWidth:"400px"},children:(0,o.jsx)(d.Z,{variant:"body1",sx:{color:"#6c757d",fontSize:"1rem"},children:"No documents available at the moment."})}):(0,o.jsx)(l.Z,{sx:{display:"flex",flexWrap:"wrap",gap:"50px"},children:E.documents.map((e,t)=>{let n=S.find(t=>t.documentObjectKey===e),r=["linear-gradient(135deg, #6a11cb 0%, #2575fc 100%)","linear-gradient(135deg, #ff7e5f 0%, #feb47b 100%)","linear-gradient(135deg, #43cea2 0%, #185a9d 100%)","linear-gradient(135deg, #ffafbd 0%, #ffc3a0 100%)","linear-gradient(135deg, #00c6ff 0%, #0072ff 100%)"],a=r[t%r.length];return(0,o.jsx)(l.Z,{sx:{position:"relative",width:"240px",height:"367.5px",display:"flex",flexDirection:"column",alignItems:"flex-start",justifyContent:"flex-start"},children:(0,o.jsx)(l.Z,{sx:{width:"240px",minHeight:"322.5px",borderRadius:"12px",overflow:"hidden",boxShadow:"0px 4px 12px rgba(0, 0, 0, 0.1)",position:"relative",display:"flex",alignItems:"center",justifyContent:"center",background:a,color:"#ffffff",cursor:"pointer"},onClick:()=>R(n.documentObjectKey,I),children:C.includes(e)||!n?(0,o.jsx)(f.Z,{}):(0,o.jsxs)(l.Z,{sx:{padding:"16px",display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",textAlign:"center",overflow:"hidden"},children:[(0,o.jsx)(d.Z,{sx:{fontSize:"27px",fontWeight:"bold",textAlign:"center",wordWrap:"break-word",wordBreak:"break-word",whiteSpace:"normal",overflow:"hidden",display:"-webkit-box",WebkitLineClamp:3,WebkitBoxOrient:"vertical"},children:(null==n?void 0:n.originalFileName)||"Document Preview"}),(0,o.jsx)(x.Z,{sx:{position:"absolute",top:12,right:12,color:"#ffffff",cursor:"pointer",height:25,width:25},onClick:()=>window.open(null==n?void 0:n.document,"_blank")})]})})},e)})})]}),(0,o.jsxs)(u.ZP,{item:!0,xs:12,children:[(0,o.jsx)(d.Z,{sx:{mb:3,fontWeight:700,mt:10,fontSize:"17px"},children:"Other helpful links"}),(0,o.jsx)(l.Z,{sx:{borderRadius:"12px",width:"400px"},children:0===E.links.length?(0,o.jsx)(l.Z,{sx:{display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",minHeight:"150px",borderRadius:"8px",border:"2px dashed #e0e0e0",bgcolor:"#f9f9f9",py:4,textAlign:"left",maxWidth:"400px"},children:(0,o.jsx)(d.Z,{variant:"body1",sx:{color:"#6c757d",fontSize:"16px"},children:"No links available right now."})}):(0,o.jsx)(l.Z,{sx:{display:"flex",flexDirection:"column",gap:2},children:E.links.map((e,t)=>(0,o.jsxs)(l.Z,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",width:"100%"},children:[(0,o.jsx)(d.Z,{component:"a",href:e.startsWith("http")?e:"https://".concat(e),target:"_blank",rel:"noopener noreferrer",sx:{color:"#1A7ECF",textDecoration:"none",fontWeight:500,fontSize:"16px"},children:e||"Link ".concat(t+1)}),(0,o.jsx)(s.Z,{onClick:()=>window.open(e.startsWith("http")?e:"https://".concat(e),"_blank"),sx:{minWidth:0,padding:0},children:(0,o.jsx)(x.Z,{sx:{color:"#6c757d",marginLeft:2}})})]},t))})})]})]})]})})})},7022:function(e,t,n){"use strict";n.d(t,{$t:function(){return d},SS:function(){return f},Y0:function(){return i},cd:function(){return u},fH:function(){return p},mH:function(){return m},ov:function(){return s},v0:function(){return c}});var o=n(40256),r=n(39124),a=n(39547);async function i(e,t,n){try{let a=await (0,o.A_)("/benefits/benefit-by-type",{companyId:t,type:n});a&&a.benefits?(console.log("GET BENEFITS FOR TYPE RESPONSE: ",a.benefits),e((0,r.oQ)({benefitType:n,benefits:a.benefits})),e((0,r.nM)("Benefits fetched successfully"))):(console.error("Invalid response format:",a),e((0,r.nM)("Failed to fetch benefits")))}catch(t){console.error("Error fetching benefits:",t),e((0,r.nM)("Error fetching benefits"))}}async function c(e,t,n,a){let i={benefitId:t,page:a};console.log("data",i);let c=await (0,o.A_)("/benefits/one-benefit",i),s={...c,benefitId:t};for(let t of(e((0,r.F5)(s)),c.documents)){let o=decodeURIComponent(t.split("_____")[1]);l(e,t,n,o)}}async function l(e,t,n,a){let i={objectKey:t,companyId:n};console.log("data",i);let c=await (0,o.$R)("/benefits/document",i);if(console.log("VIEW BENEFIT RESPONSE: ",c),c){let n=new Blob([c],{type:"application/pdf"}),o=URL.createObjectURL(n);e((0,r.D7)([{documentObjectKey:t,document:o,originalFileName:a}]))}}let s=async(e,t,n,r,c)=>200===(await (0,o.j0)("/benefits/toggle-benefits/",{benefitId:n,companyId:t,isActivated:r})).status&&(await i(e,t,c),await (0,a.N)(e,t),!0);async function d(e,t,n,a){let i=new FormData;a.forEach(e=>i.append("documents",e)),i.append("companyId",n),i.append("benefitId",t);try{console.log("uploadDocument",i);let c=await (0,o.iG)("/benefits/add/document",i),s=c.data.objectKeys;if(console.log("newObjectKeys",s),200===c.status)return s.forEach((o,i)=>{let c=a[i].name;e((0,r.H_)({benefitId:t,document:o})),l(e,o,n,c)}),e((0,r.nM)("Document added successfully")),!0;return console.error("Error adding document:",c.data.error),e((0,r.nM)("Failed to add document")),!1}catch(t){return console.error("Error adding document:",t),e((0,r.nM)("Error adding document")),!1}}async function u(e,t,n,a){try{let i=await (0,o.j0)("/benefits/delete/document",{benefitId:t,companyId:n,objectKey:a});if(200===i.status)return e((0,r.iH)({benefitId:t,document:a})),e((0,r.nM)("Document deleted successfully")),!0;return console.error("Error deleting document:",i.data.error),e((0,r.nM)("Failed to delete document")),!1}catch(t){return console.error("Error deleting document:",t),e((0,r.nM)("Error deleting document")),!1}}async function f(e,t,n,a){try{let i=await (0,o.j0)("/benefits/add/links",{benefitId:t,companyId:n,urls:[a]});if(200===i.status)return e((0,r.MJ)({benefitId:t,link:a})),e((0,r.nM)("Link added successfully")),!0;return console.error("Error adding link:",i.data.error),e((0,r.nM)("Failed to add link")),!1}catch(t){return console.error("Error adding link:",t),e((0,r.nM)("Error adding link")),!1}}async function p(e,t,n,a){try{let i=await (0,o.j0)("/benefits/delete/link",{benefitId:t,companyId:n,urls:a});if(200===i.status)return e((0,r.Yw)({benefitId:t,link:a})),e((0,r.nM)("Link deleted successfully")),!0;return console.error("Error deleting link:",i.data.error),e((0,r.nM)("Failed to delete link")),!1}catch(t){return console.error("Error deleting link:",t),e((0,r.nM)("Error deleting link")),!1}}async function m(e,t){let n=new FormData;n.append("logoImage",t);try{console.log("uploading company logo",n);let t=await (0,o.iG)("/admin/update-company-logo",n);if(await (0,a.aK)(e),200===t.status)return console.log("Company logo updated successfully"),e((0,r.nM)("Company logo updated successfully")),!0;return console.error("Error updating company logo:",t.data.error),e((0,r.nM)("Failed to update company logo")),!1}catch(t){return console.error("Error updating company logo:",t),e((0,r.nM)("Error updating company logo")),!1}}}},function(e){e.O(0,[139,3463,3301,8575,8685,187,1423,9932,3919,9129,2786,9826,8166,8760,9414,3344,9662,1356,2971,2117,1744],function(){return e(e.s=42102)}),_N_E=e.O()}]);