(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9767],{67690:function(e,t,n){Promise.resolve().then(n.bind(n,17137))},67116:function(e,t,n){"use strict";n.d(t,{Z:function(){return x}});var r=n(2265),o=n(61994),a=n(20801),i=n(16210),c=n(21086),l=n(37053),s=n(94630),d=n(57437),u=(0,s.Z)((0,d.jsx)("path",{d:"M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"}),"Person"),f=n(94143),p=n(50738);function m(e){return(0,p.ZP)("MuiAvatar",e)}(0,f.Z)("MuiAvatar",["root","colorDefault","circular","rounded","square","img","fallback"]);var g=n(79114);let h=e=>{let{classes:t,variant:n,colorDefault:r}=e;return(0,a.Z)({root:["root",n,r&&"colorDefault"],img:["img"],fallback:["fallback"]},m,t)},y=(0,i.default)("div",{name:"MuiAvatar",slot:"Root",overridesResolver:(e,t)=>{let{ownerState:n}=e;return[t.root,t[n.variant],n.colorDefault&&t.colorDefault]}})((0,c.Z)(e=>{let{theme:t}=e;return{position:"relative",display:"flex",alignItems:"center",justifyContent:"center",flexShrink:0,width:40,height:40,fontFamily:t.typography.fontFamily,fontSize:t.typography.pxToRem(20),lineHeight:1,borderRadius:"50%",overflow:"hidden",userSelect:"none",variants:[{props:{variant:"rounded"},style:{borderRadius:(t.vars||t).shape.borderRadius}},{props:{variant:"square"},style:{borderRadius:0}},{props:{colorDefault:!0},style:{color:(t.vars||t).palette.background.default,...t.vars?{backgroundColor:t.vars.palette.Avatar.defaultBg}:{backgroundColor:t.palette.grey[400],...t.applyStyles("dark",{backgroundColor:t.palette.grey[600]})}}}]}})),b=(0,i.default)("img",{name:"MuiAvatar",slot:"Img",overridesResolver:(e,t)=>t.img})({width:"100%",height:"100%",textAlign:"center",objectFit:"cover",color:"transparent",textIndent:1e4}),v=(0,i.default)(u,{name:"MuiAvatar",slot:"Fallback",overridesResolver:(e,t)=>t.fallback})({width:"75%",height:"75%"});var x=r.forwardRef(function(e,t){let n=(0,l.i)({props:e,name:"MuiAvatar"}),{alt:a,children:i,className:c,component:s="div",slots:u={},slotProps:f={},imgProps:p,sizes:m,src:x,srcSet:w,variant:j="circular",...E}=n,Z=null,k={...n,component:s,variant:j},M=function(e){let{crossOrigin:t,referrerPolicy:n,src:o,srcSet:a}=e,[i,c]=r.useState(!1);return r.useEffect(()=>{if(!o&&!a)return;c(!1);let e=!0,r=new Image;return r.onload=()=>{e&&c("loaded")},r.onerror=()=>{e&&c("error")},r.crossOrigin=t,r.referrerPolicy=n,r.src=o,a&&(r.srcset=a),()=>{e=!1}},[t,n,o,a]),i}({...p,..."function"==typeof f.img?f.img(k):f.img,src:x,srcSet:w}),C=x||w,I=C&&"error"!==M;k.colorDefault=!I,delete k.ownerState;let R=h(k),[S,F]=(0,g.Z)("img",{className:R.img,elementType:b,externalForwardedProps:{slots:u,slotProps:{img:{...p,...f.img}}},additionalProps:{alt:a,src:x,srcSet:w,sizes:m},ownerState:k});return Z=I?(0,d.jsx)(S,{...F}):i||0===i?i:C&&a?a[0]:(0,d.jsx)(v,{ownerState:k,className:R.fallback}),(0,d.jsx)(y,{as:s,className:(0,o.Z)(R.root,c),ref:t,...E,ownerState:k,children:Z})})},36137:function(e,t,n){"use strict";n.d(t,{Z:function(){return m}});var r=n(2265),o=n(61994),a=n(20801),i=n(16210),c=n(37053),l=n(94143),s=n(50738);function d(e){return(0,s.ZP)("MuiCardContent",e)}(0,l.Z)("MuiCardContent",["root"]);var u=n(57437);let f=e=>{let{classes:t}=e;return(0,a.Z)({root:["root"]},d,t)},p=(0,i.default)("div",{name:"MuiCardContent",slot:"Root",overridesResolver:(e,t)=>t.root})({padding:16,"&:last-child":{paddingBottom:24}});var m=r.forwardRef(function(e,t){let n=(0,c.i)({props:e,name:"MuiCardContent"}),{className:r,component:a="div",...i}=n,l={...n,component:a},s=f(l);return(0,u.jsx)(p,{as:a,className:(0,o.Z)(s.root,r),ownerState:l,ref:t,...i})})},67208:function(e,t,n){"use strict";n.d(t,{Z:function(){return g}});var r=n(2265),o=n(61994),a=n(20801),i=n(16210),c=n(37053),l=n(53410),s=n(94143),d=n(50738);function u(e){return(0,d.ZP)("MuiCard",e)}(0,s.Z)("MuiCard",["root"]);var f=n(57437);let p=e=>{let{classes:t}=e;return(0,a.Z)({root:["root"]},u,t)},m=(0,i.default)(l.Z,{name:"MuiCard",slot:"Root",overridesResolver:(e,t)=>t.root})({overflow:"hidden"});var g=r.forwardRef(function(e,t){let n=(0,c.i)({props:e,name:"MuiCard"}),{className:r,raised:a=!1,...i}=n,l={...n,raised:a},s=p(l);return(0,f.jsx)(m,{className:(0,o.Z)(s.root,r),elevation:a?8:void 0,ref:t,ownerState:l,...i})})},40256:function(e,t,n){"use strict";n.d(t,{$R:function(){return d},A_:function(){return c},BO:function(){return a},GH:function(){return u},_n:function(){return o},be:function(){return i},iG:function(){return s},j0:function(){return l}});var r=n(83464);let o="http://localhost:8080",a="<EMAIL>,<EMAIL>,<EMAIL>".split(",").map(e=>e.trim()),i=r.Z.create({baseURL:o});async function c(e,t,n){let r=new URL(n?"".concat(n).concat(e):"".concat(o).concat(e));return t&&Object.keys(t).forEach(e=>r.searchParams.append(e,t[e])),(await i.get(r.toString())).data}async function l(e,t,n){let r=n?"".concat(n).concat(e):"".concat(o).concat(e),a=await i.post(r,t,{headers:{"Content-Type":"application/json"}});return{status:a.status,data:a.data}}async function s(e,t,n){let r=n?"".concat(n).concat(e):"".concat(o).concat(e);console.log("Document upload to: ".concat(r));let a=await i.post(r,t,{headers:{"Content-Type":"multipart/form-data"}});return{status:a.status,data:a.data}}async function d(e,t,n){let r=new URL(n?"".concat(n).concat(e):"".concat(o).concat(e));return t&&Object.keys(t).forEach(e=>r.searchParams.append(e,t[e])),console.log("GET Blob request to: ".concat(r.toString())),(await i.get(r.toString(),{responseType:"blob"})).data}async function u(e,t,n){let r=n?"".concat(n).concat(e):"".concat(o).concat(e),a=await i.put(r,t,{headers:{"Content-Type":"application/json"}});return{status:a.status,data:a.data}}i.interceptors.request.use(e=>{let t=localStorage.getItem("userid1")||localStorage.getItem("userId");return t?e.headers["user-id"]=t:console.warn("No user ID found in localStorage for API request"),e})},17137:function(e,t,n){"use strict";n.r(t);var r=n(57437),o=n(2265),a=n(83337),i=n(7022),c=n(70623),l=n(39124),s=n(95656),d=n(46387),u=n(35389),f=n(89414),p=n(67208),m=n(36137),g=n(94013),h=n(99376),y=n(68575),b=n(13571),v=n(48223),x=n(31175),w=n(39547);t.default=(0,b.Z)(()=>{let{benefitType:e}=(0,h.useParams)(),t=decodeURIComponent(e),n=(0,a.T)(),b=(0,h.useRouter)(),j=(0,y.v9)(e=>(0,c.MP)(e)),E=(0,a.C)(e=>e.user.userProfile),Z=(0,y.v9)(e=>(0,l.oT)(e,t));(0,o.useEffect)(()=>{""!==t&&""!==j&&(n((0,l.Yb)()),(0,i.Y0)(n,j,t))},[t,j,n]);let k=e=>{n((0,c.Re)(e)),b.push("/viewBenefitDetails/".concat(e))};return(0,r.jsx)(v.Z,{children:(0,r.jsxs)(s.Z,{sx:{flexGrow:1,minHeight:"95vh",width:"100%",bgcolor:"#F5F6FA",padding:4},children:[(0,r.jsxs)(d.Z,{variant:"h4",sx:{fontWeight:"bold",mb:2},children:["Hey ",E.name.replace(/\b\w/g,e=>e.toUpperCase()),","]}),(0,r.jsx)(d.Z,{variant:"body1",sx:{color:"#6c757d",mb:4},children:"Explore your benefits now—tap to dive in!"}),(0,r.jsx)(s.Z,{sx:{mb:4},children:null===Z?(0,r.jsx)(s.Z,{sx:{display:"flex",flexDirection:"column",alignItems:"flex-start",justifyContent:"center",height:"150px"},children:(0,r.jsx)(u.Z,{size:50,sx:{color:"#6c757d",mb:2}})}):0===Z.benefits.filter(e=>e.isActivated).length?(0,r.jsxs)(s.Z,{sx:{display:"flex",flexDirection:"column",alignItems:"flex-start",justifyContent:"center",height:"150px",borderRadius:"12px",backgroundColor:"#F5F6FA",textAlign:"left"},children:[(0,r.jsx)(d.Z,{variant:"h6",sx:{fontWeight:"bold",color:"#6c757d",mb:2},children:"No benefits available"}),(0,r.jsx)(d.Z,{variant:"body2",sx:{color:"#9E9E9E"},children:"There are currently no active benefits for this type."})]}):(0,r.jsx)(f.ZP,{container:!0,spacing:3,children:Z.benefits.filter(e=>e.isActivated&&e.imageS3Urls.length>0).map((e,t)=>(0,r.jsx)(f.ZP,{item:!0,xs:12,sm:6,md:3.5,children:(0,r.jsxs)(p.Z,{sx:{height:"100%",display:"flex",flexDirection:"column",alignItems:"flex-start",justifyContent:"flex-start",borderRadius:"16px",bgcolor:"#ffffff",padding:3,boxShadow:"none"},children:[(0,x.DD)(e.subType,{marginBottom:15,fontSize:35,color:"#606267"}),(0,r.jsxs)(m.Z,{sx:{flexGrow:1,padding:0,width:"100%"},children:[(0,r.jsx)(d.Z,{variant:"h6",sx:{fontWeight:600,textAlign:"left"},children:(0,w.dA)(e.subType)}),(0,r.jsxs)(d.Z,{variant:"body2",sx:{color:"#6c757d",marginTop:1,textAlign:"left"},children:["Check your ",(0,w.dA)(e.subType)," benefits"]})]}),(0,r.jsx)(s.Z,{sx:{paddingTop:2,width:"100%"},children:(0,r.jsx)(g.Z,{variant:"contained",fullWidth:!0,onClick:()=>k(e._id),sx:{textTransform:"none",borderRadius:"8px",bgcolor:"#e8e8e8",color:"black",boxShadow:"none",padding:"10px 0","&:hover":{backgroundColor:"#d8d8d8",boxShadow:"none"}},children:"View"})})]})},e._id))})})]})})})},7022:function(e,t,n){"use strict";n.d(t,{$t:function(){return d},SS:function(){return f},Y0:function(){return i},cd:function(){return u},fH:function(){return p},mH:function(){return m},ov:function(){return s},v0:function(){return c}});var r=n(40256),o=n(39124),a=n(39547);async function i(e,t,n){try{let a=await (0,r.A_)("/benefits/benefit-by-type",{companyId:t,type:n});a&&a.benefits?(console.log("GET BENEFITS FOR TYPE RESPONSE: ",a.benefits),e((0,o.oQ)({benefitType:n,benefits:a.benefits})),e((0,o.nM)("Benefits fetched successfully"))):(console.error("Invalid response format:",a),e((0,o.nM)("Failed to fetch benefits")))}catch(t){console.error("Error fetching benefits:",t),e((0,o.nM)("Error fetching benefits"))}}async function c(e,t,n,a){let i={benefitId:t,page:a};console.log("data",i);let c=await (0,r.A_)("/benefits/one-benefit",i),s={...c,benefitId:t};for(let t of(e((0,o.F5)(s)),c.documents)){let r=decodeURIComponent(t.split("_____")[1]);l(e,t,n,r)}}async function l(e,t,n,a){let i={objectKey:t,companyId:n};console.log("data",i);let c=await (0,r.$R)("/benefits/document",i);if(console.log("VIEW BENEFIT RESPONSE: ",c),c){let n=new Blob([c],{type:"application/pdf"}),r=URL.createObjectURL(n);e((0,o.D7)([{documentObjectKey:t,document:r,originalFileName:a}]))}}let s=async(e,t,n,o,c)=>200===(await (0,r.j0)("/benefits/toggle-benefits/",{benefitId:n,companyId:t,isActivated:o})).status&&(await i(e,t,c),await (0,a.N)(e,t),!0);async function d(e,t,n,a){let i=new FormData;a.forEach(e=>i.append("documents",e)),i.append("companyId",n),i.append("benefitId",t);try{console.log("uploadDocument",i);let c=await (0,r.iG)("/benefits/add/document",i),s=c.data.objectKeys;if(console.log("newObjectKeys",s),200===c.status)return s.forEach((r,i)=>{let c=a[i].name;e((0,o.H_)({benefitId:t,document:r})),l(e,r,n,c)}),e((0,o.nM)("Document added successfully")),!0;return console.error("Error adding document:",c.data.error),e((0,o.nM)("Failed to add document")),!1}catch(t){return console.error("Error adding document:",t),e((0,o.nM)("Error adding document")),!1}}async function u(e,t,n,a){try{let i=await (0,r.j0)("/benefits/delete/document",{benefitId:t,companyId:n,objectKey:a});if(200===i.status)return e((0,o.iH)({benefitId:t,document:a})),e((0,o.nM)("Document deleted successfully")),!0;return console.error("Error deleting document:",i.data.error),e((0,o.nM)("Failed to delete document")),!1}catch(t){return console.error("Error deleting document:",t),e((0,o.nM)("Error deleting document")),!1}}async function f(e,t,n,a){try{let i=await (0,r.j0)("/benefits/add/links",{benefitId:t,companyId:n,urls:[a]});if(200===i.status)return e((0,o.MJ)({benefitId:t,link:a})),e((0,o.nM)("Link added successfully")),!0;return console.error("Error adding link:",i.data.error),e((0,o.nM)("Failed to add link")),!1}catch(t){return console.error("Error adding link:",t),e((0,o.nM)("Error adding link")),!1}}async function p(e,t,n,a){try{let i=await (0,r.j0)("/benefits/delete/link",{benefitId:t,companyId:n,urls:a});if(200===i.status)return e((0,o.Yw)({benefitId:t,link:a})),e((0,o.nM)("Link deleted successfully")),!0;return console.error("Error deleting link:",i.data.error),e((0,o.nM)("Failed to delete link")),!1}catch(t){return console.error("Error deleting link:",t),e((0,o.nM)("Error deleting link")),!1}}async function m(e,t){let n=new FormData;n.append("logoImage",t);try{console.log("uploading company logo",n);let t=await (0,r.iG)("/admin/update-company-logo",n);if(await (0,a.aK)(e),200===t.status)return console.log("Company logo updated successfully"),e((0,o.nM)("Company logo updated successfully")),!0;return console.error("Error updating company logo:",t.data.error),e((0,o.nM)("Failed to update company logo")),!1}catch(t){return console.error("Error updating company logo:",t),e((0,o.nM)("Error updating company logo")),!1}}}},function(e){e.O(0,[139,3463,3301,8575,293,9810,187,3145,9932,3919,9129,2786,9826,8166,8760,9414,3344,9662,1356,2971,2117,1744],function(){return e(e.s=67690)}),_N_E=e.O()}]);