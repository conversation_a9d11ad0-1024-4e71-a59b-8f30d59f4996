(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3967],{9428:function(e,t,o){Promise.resolve().then(o.bind(o,15417))},15417:function(e,t,o){"use strict";o.r(t),o.d(t,{default:function(){return c}});var s=o(57437),n=o(2265),a=o(99376),i=o(83464);let l=(0,o(61103).GU)(),r={async getQuestions(){try{return(await i.Z.get("".concat(l,"/wellness/questions"))).data.data.questions}catch(e){throw console.error("Error fetching wellness questions:",e),e}},async submitAnswers(e,t,o){try{let s={user_id:e,team_id:t,user_answer:{answers:o}};console.log("Sending payload to API:",s),console.log("Endpoint URL:","".concat(l,"/wellness/predictions"));let n=await i.Z.post("".concat(l,"/wellness/predictions"),s);return console.log("API response:",n.data),n.data.data}catch(e){if(console.error("Error submitting wellness answers:",e),i.Z.isAxiosError(e)){var s,n;console.error("Response data:",null===(s=e.response)||void 0===s?void 0:s.data),console.error("Status code:",null===(n=e.response)||void 0===n?void 0:n.status)}throw e}}};o(62027);var u=JSON.parse('{"wellness_questions":[{"id":"gender","text":"What is your gender?","type":"categorical","options":["Male","Female"],"feature":"Sex","weights":null},{"id":"bmi","text":"What is your BMI?","type":"numeric","options":null,"feature":"BMI","weights":null},{"id":"height","text":"What is your height in (cm)?","type":"numeric","options":null,"feature":null,"weights":null},{"id":"weight","text":"What is your weight in (kg)?","type":"numeric","options":null,"feature":null,"weights":null},{"id":"age","text":"What is your current age?","type":"numeric","options":null,"feature":"Age","weights":null},{"id":"race","text":"What is your race?","type":"categorical","options":["Black","White","Other","American Indian/Alaskan Native","Asian","Hispanic","White only, Non-Hispanic","Black only, Non-Hispanic","Multiracial, Non-Hispanic","Other race only, Non-Hispanic"],"feature":"Race","weights":null},{"id":"general_health","text":"What is your general health?","type":"ordinal","options":["Poor","Fair","Good","Very good","Excellent"],"feature":"GenHealth","weights":null},{"id":"diabetic","text":"Are you diabetic?","type":"categorical","options":["No","Yes","No, borderline diabetes","Yes (during pregnancy)","Yes, but only during pregnancy (female)","No, pre-diabetes or borderline diabetes"],"feature":"Diabetic","weights":-2.5},{"id":"asthma","text":"Do you have asthma?","type":"boolean","options":null,"feature":"Asthma","weights":-1.5},{"id":"kidney_problems","text":"Do you have any kidney problems?","type":"boolean","options":null,"feature":null,"weights":-2},{"id":"smoking","text":"How often have you smoked in the past two years?","type":"categorical","options":["Rarely","Sometimes","Often"],"feature":"Smoking","weights":{"Rarely":0,"Sometimes":-2,"Often":-5}},{"id":"alcohol","text":"How often have you consumed alcohol in the past two years?","type":"categorical","options":["Rarely","Sometimes","Often"],"feature":"AlcoholDrinking","weights":{"Rarely":0,"Sometimes":-1,"Often":-3}},{"id":"physical_activity","text":"How often have you been engaging in physical activities in the past year?","type":"categorical","options":["Rarely","Sometimes","Often"],"feature":"PhysicalActivity","weights":{"Rarely":-3,"Sometimes":1,"Often":5}},{"id":"sleep_hours","text":"How many hours do you sleep?","type":"numeric","options":null,"feature":"SleepTime","weights":{"<5":-3,"5-7":0,"7-9":2,">9":-1}},{"id":"walking","text":"How often do you walk?","type":"categorical","options":["Rarely","Sometimes","Often"],"feature":"DiffWalking","weights":{"Rarely":-2,"Sometimes":1,"Often":3}},{"id":"stress","text":"Do you often take stress?","type":"boolean","options":null,"feature":null,"weights":{"Yes":-2,"No":1}},{"id":"social_life","text":"Do you like engaging in social gatherings?","type":"boolean","options":null,"feature":null,"weights":{"Yes":-1,"No":0}},{"id":"healthy_food","text":"How often do you include fruits and vegetables in your meal without using processed food?","type":"categorical","options":["Rarely","Sometimes","Often"],"feature":null,"weights":{"Rarely":-3,"Sometimes":1,"Often":4}},{"id":"cardio","text":"How often do you have cardio?","type":"categorical","options":["Rarely","Sometimes","Often"],"feature":null,"weights":{"Rarely":-3,"Sometimes":1,"Often":3}},{"id":"life_span_grandparents","text":"Life span of grand parents? (Average would work)","type":"numeric","options":null,"feature":null,"weights":null},{"id":"ever_married","text":"Are you married or were you ever married?","type":"boolean","options":null,"feature":null,"weights":{"Yes":-1,"No":0}},{"id":"work_type","text":"What is your work type?","type":"categorical","options":["Self-employed","Govt_job","Private"],"feature":null,"weights":null},{"id":"residence_type","text":"What is your residence type?","type":"categorical","options":["Rural","Urban"],"feature":null,"weights":null},{"id":"avg_glucose_level","text":"What is your average glucose level?","type":"numeric","options":null,"feature":null,"weights":null}]}');function c(){let e=(0,a.useRouter)(),[t,o]=(0,n.useState)([]),[i,l]=(0,n.useState)(0),[c,d]=(0,n.useState)({}),[g,p]=(0,n.useState)(""),[h,y]=(0,n.useState)(!0),[m,f]=(0,n.useState)("123"),[w,b]=(0,n.useState)("456");(0,n.useEffect)(()=>{f(localStorage.getItem("userid1")||"123"),b(localStorage.getItem("teamId")||"456")},[]),(0,n.useEffect)(()=>{(async()=>{try{let e=await r.getQuestions();o(e)}catch(e){console.error("Failed to load questions from API, using fallback data",e),console.log("Fallback data:",u),u.wellness_questions?o(u.wellness_questions.map(e=>({...e,options:e.options||void 0}))):(console.error("wellness_questions not found in fallback data"),p("Failed to load questions. Please check console for details."))}finally{y(!1)}})();let e=localStorage.getItem("wellness_user_answers");e&&d(JSON.parse(e))},[]),(0,n.useEffect)(()=>{localStorage.setItem("wellness_user_answers",JSON.stringify(c))},[c]);let x=async()=>{try{y(!0),console.log("Submitting answers:",m,w,c);let t=await r.submitAnswers(m,w,c);console.log("Submission result:",t),localStorage.setItem("wellness_results",JSON.stringify(t)),localStorage.removeItem("wellness_user_answers"),e.push("/wellness/results")}catch(e){console.error("Error submitting wellness answers:",e),p("Failed to submit answers. Please try again.")}finally{y(!1)}},v=e=>{if(!t[i])return;let o=e.target.value;"numeric"===t[i].type&&isNaN(o=parseFloat(o))&&(o=""),d(e=>({...e,[t[i].id]:o}))},S=e=>{t[i]&&(d(o=>({...o,[t[i].id]:e})),p(""))};if(h)return(0,s.jsx)("div",{className:"loading",children:"Loading..."});if(!t||0===t.length)return(0,s.jsx)("div",{className:"error",children:"Failed to load questions. Please refresh the page."});let _=t[i];return(0,s.jsxs)("div",{className:"quiz-container",children:[(0,s.jsxs)("div",{className:"nav-header",children:[(0,s.jsx)("button",{className:"back-arrow",onClick:()=>l(e=>Math.max(e-1,0)),disabled:0===i,children:"\xab"}),(0,s.jsx)("div",{className:"progress-bar-wrapper",children:(0,s.jsx)("div",{className:"progress-bar-inner",style:{width:"".concat((i+1)/t.length*100,"%")}})})]}),(0,s.jsx)("h2",{className:"question-text",children:_.text}),(0,s.jsx)("ul",{className:"options-wrapper",children:(()=>{if(!t[i])return null;let e=t[i];if("categorical"===e.type||"ordinal"===e.type){var o;return null===(o=e.options)||void 0===o?void 0:o.map(t=>(0,s.jsx)("li",{onClick:()=>S(t),className:"option-card ".concat(c[e.id]===t?"option-selected":""),children:t},t))}return"boolean"===e.type?["Yes","No"].map(t=>(0,s.jsx)("li",{onClick:()=>S(t),className:"option-card ".concat(c[e.id]===t?"option-selected":""),children:t},t)):"numeric"===e.type?(0,s.jsx)("input",{type:"number",onChange:v,value:c[e.id]||"",className:"numeric-input",placeholder:"Enter your answer"}):null})()}),g&&(0,s.jsx)("p",{className:"error",children:g}),(0,s.jsx)("div",{className:"btn-container",children:(0,s.jsx)("button",{onClick:()=>{if(t[i]){if(!["life_span_grandparents","avg_glucose_level"].includes(t[i].id)&&(!(t[i].id in c)||""===c[t[i].id])){p("Please answer before continuing.");return}p(""),i<t.length-1?l(e=>e+1):x()}},className:"next-btn",disabled:h,children:i===t.length-1?"Finish":"Next"})})]})}},61103:function(e,t,o){"use strict";o.d(t,{GU:function(){return a},bR:function(){return s},n5:function(){return n}});let s=()=>"http://localhost:8080",n=()=>{let e="userid1",t="userId",o=localStorage.getItem(e)||localStorage.getItem(t);return(console.log("\uD83D\uDD0D getUserId debug:",{primaryKey:e,altKey:t,primaryValue:localStorage.getItem(e),altValue:localStorage.getItem(t),finalUserId:o}),o)?o:(console.error("❌ User ID not found in localStorage"),"default-user")},a=()=>"https://bot.benosphere.com"},62027:function(){}},function(e){e.O(0,[8757,3301,2971,2117,1744],function(){return e(e.s=9428)}),_N_E=e.O()}]);