(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7675],{43724:function(e,s,n){Promise.resolve().then(n.bind(n,25058))},99376:function(e,s,n){"use strict";var t=n(35475);n.o(t,"useParams")&&n.d(s,{useParams:function(){return t.useParams}}),n.o(t,"usePathname")&&n.d(s,{usePathname:function(){return t.usePathname}}),n.o(t,"useRouter")&&n.d(s,{useRouter:function(){return t.useRouter}}),n.o(t,"useSearchParams")&&n.d(s,{useSearchParams:function(){return t.useSearchParams}})},25058:function(e,s,n){"use strict";n.r(s),n.d(s,{default:function(){return o}});var t=n(57437),i=n(2265),r=n(99376);n(70416);let a={predictions:{life_expectancy:{predicted_baseline_age:85.7,additional_adjustment:2.3,final_adjusted_age:88,survival_probability_past_100:.15,message:"Congratulations, you are expected to live around this age."},heart_disease_probability:12.5,stroke_prediction:{stroke_probability:3.2,stroke:"No"}},recommendations:["Maintain a balanced diet rich in fruits, vegetables, and whole grains.","Aim for at least 150 minutes of moderate exercise per week.","Practice stress reduction techniques like meditation or deep breathing.","Ensure you get 7-8 hours of quality sleep each night.","Schedule regular check-ups with your healthcare provider."],sources:["https://api.benosphere.com/benefits/document?objectKey=sample-dental-benefits.pdf&companyId=demo","https://api.benosphere.com/benefits/document?objectKey=sample-health-insurance.pdf&companyId=demo"]};function o(){let e=(0,r.useRouter)(),[s,n]=(0,i.useState)(null),[o,l]=(0,i.useState)(!0),[c,d]=(0,i.useState)(!1);(0,i.useEffect)(()=>{let e=localStorage.getItem("wellness_results");e?(n(JSON.parse(e)),d(!1)):(n(a),d(!0)),l(!1)},[]);let h=()=>{alert("We’ll remind you! (Coming soon)")},u=()=>{alert("This app provides an estimate of potential lifespan based on general statistical models and user-provided data.\n    \nIt is not a medical diagnosis, prediction of death, or guarantee of future outcomes.\n\nConsult a healthcare professional for accurate health assessments. We are not liable for decisions made based on this tool.")},m=()=>{localStorage.removeItem("wellness_results"),e.push("/wellness")};if(o)return(0,t.jsx)("div",{className:"loading",children:"Loading results..."});if(!s)return(0,t.jsx)("div",{className:"wellness-body",children:(0,t.jsxs)("div",{className:"container error-container",children:[(0,t.jsx)("h2",{children:"No Results Found"}),(0,t.jsx)("p",{children:"We couldn't find your wellness assessment results."}),(0,t.jsx)("button",{className:"next-btn",onClick:m,children:"Take Assessment"})]})});let{predictions:p,recommendations:f,sources:x}=s,j=p.life_expectancy.final_adjusted_age.toFixed(1),g=p.heart_disease_probability.toFixed(1),b=p.stroke_prediction.stroke_probability.toFixed(1),v=(100*p.life_expectancy.survival_probability_past_100).toFixed(2);return(0,t.jsxs)("div",{className:"wellness-body",children:[c&&(0,t.jsx)("div",{className:"demo-banner",children:(0,t.jsxs)("p",{children:["Viewing demo results. ",(0,t.jsx)("button",{onClick:m,children:"Take the assessment"})," to get personalized results."]})}),(0,t.jsxs)("nav",{className:"wellness-nav",children:[(0,t.jsx)("a",{href:"#longevity",children:"Longevity"}),(0,t.jsx)("a",{href:"#health",children:"Health Risks"}),(0,t.jsx)("a",{href:"#recommendations",children:"Recommendations"}),(0,t.jsx)("a",{href:"#benefits",children:"Benefits"})]}),(0,t.jsxs)("section",{id:"longevity",className:"section container",children:[(0,t.jsx)("h2",{children:"Your Longevity Report \uD83C\uDF89"}),(0,t.jsxs)("p",{className:"highlight",children:["Expected Longevity: ",j," years",(0,t.jsx)("a",{href:"#",onClick:e=>{e.preventDefault(),u()},children:"(disclaimer)"})]}),(0,t.jsx)("p",{children:"Based on the data provided, you're likely to live around this age."}),(0,t.jsxs)("p",{children:[(0,t.jsx)("strong",{children:"Chance of Living Past 100:"})," ",v,"%"]}),(0,t.jsxs)("div",{className:"centered",children:[(0,t.jsx)("button",{className:"share-button",onClick:()=>{navigator.clipboard.writeText(window.location.href),alert("Link copied! Challenge your friends to check their longevity too!")},children:"Challenge My Friends \uD83D\uDCAA"}),(0,t.jsx)("button",{className:"reminder-button",onClick:h,children:"Send Me Reminders ⏰"})]}),(0,t.jsxs)("div",{className:"centered faces",children:[(0,t.jsx)("img",{src:"https://randomuser.me/api/portraits/men/75.jpg",alt:"User 1"}),(0,t.jsx)("img",{src:"https://randomuser.me/api/portraits/women/65.jpg",alt:"User 2"}),(0,t.jsx)("img",{src:"https://randomuser.me/api/portraits/men/35.jpg",alt:"User 3"}),(0,t.jsx)("p",{children:"You're in the top 25% of healthiest users!"})]})]}),(0,t.jsxs)("section",{id:"health",className:"section container",children:[(0,t.jsx)("h2",{children:"Health Risk Overview \uD83E\uDE7A"}),(0,t.jsxs)("p",{children:[(0,t.jsx)("strong",{children:"Heart Disease Risk:"})," ",g,"%"]}),(0,t.jsxs)("p",{children:[(0,t.jsx)("strong",{children:"Stroke Risk:"})," ",1>parseFloat(b)?"Very Low":"Moderate"," (",b,"%)"]}),(0,t.jsxs)("p",{children:[(0,t.jsx)("strong",{children:"Stroke Prediction:"})," ",p.stroke_prediction.stroke]})]}),(0,t.jsxs)("section",{id:"recommendations",className:"section container",children:[(0,t.jsx)("h2",{children:"Personalized Health Recommendations \uD83D\uDCA1"}),f.map((e,s)=>(0,t.jsx)("p",{children:e},s)),(0,t.jsx)("div",{className:"centered",children:(0,t.jsx)("button",{className:"reminder-button",onClick:h,children:"Send Me Reminders ⏰"})})]}),(0,t.jsxs)("section",{id:"benefits",className:"section container",children:[(0,t.jsx)("h2",{children:"Benefits + Resources \uD83D\uDCC4"}),(0,t.jsx)("div",{className:"benefits-container",children:x.length>0?x.map((e,s)=>(0,t.jsxs)("a",{href:e,target:"_blank",rel:"noopener noreferrer",className:"benefit-link",children:["Benefit Document ",s+1]},s)):(0,t.jsx)("div",{className:"no-benefits-message",children:"No specific benefit documents found for your health profile."})})]}),(0,t.jsx)("div",{className:"centered",children:(0,t.jsx)("button",{className:"share-button",onClick:m,children:"Retake Assessment"})}),(0,t.jsxs)("footer",{className:"wellness-footer",children:[(0,t.jsxs)("div",{className:"disclaimer container",children:[(0,t.jsxs)("p",{children:["⚠️ ",(0,t.jsx)("strong",{children:"Disclaimer:"})," This is not a medical diagnosis. It's a statistical estimate only."]}),(0,t.jsx)("p",{children:"\uD83E\uDE7A Consult a healthcare professional for accurate health assessments."}),(0,t.jsx)("p",{children:(0,t.jsx)("em",{children:"This is an estimate, not a prediction—talk to your doctor!"})})]}),(0,t.jsx)("p",{children:"\xa9 2025 BenOsphere. All rights reserved."})]})]})}},70416:function(){}},function(e){e.O(0,[6307,2971,2117,1744],function(){return e(e.s=43724)}),_N_E=e.O()}]);