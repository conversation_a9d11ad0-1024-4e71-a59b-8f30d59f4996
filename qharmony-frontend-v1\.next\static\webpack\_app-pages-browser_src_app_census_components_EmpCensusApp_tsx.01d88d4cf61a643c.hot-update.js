"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("_app-pages-browser_src_app_census_components_EmpCensusApp_tsx",{

/***/ "(app-pages-browser)/./src/app/census/services/censusApi.ts":
/*!**********************************************!*\
  !*** ./src/app/census/services/censusApi.ts ***!
  \**********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! axios */ \"(app-pages-browser)/./node_modules/axios/lib/axios.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n// Census API uses the Python backend (chatbot service) instead of the main Node.js backend\nconst CENSUS_API_BASE_URL = \"http://127.0.0.1:8000\" || 0;\nclass CensusApiService {\n    /**\n   * Upload and process census file using the Python backend (chatbot service)\n   */ static async uploadCensusFile(file) {\n        let returnDataframe = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;\n        try {\n            var _response_data, _response_data1, _response_data2, _response_data_data, _response_data3, _response_data_data1, _response_data4;\n            const formData = new FormData();\n            formData.append(\"file\", file);\n            // Build full URL for census API (Python backend)\n            const url = \"\".concat(CENSUS_API_BASE_URL, \"/api/census/processor/v1?return_dataframe=\").concat(returnDataframe);\n            console.log(\"\\uD83D\\uDCE4 Uploading census file: \".concat(file.name, \" (\").concat((file.size / 1024 / 1024).toFixed(2), \" MB)\"));\n            console.log(\"\\uD83D\\uDD17 Census API URL: \".concat(url));\n            console.log(\"\\uD83D\\uDCCB Request details:\", {\n                method: \"POST\",\n                url: url,\n                fileSize: file.size,\n                fileName: file.name,\n                returnDataframe: returnDataframe\n            });\n            // Use axios directly for census API calls to Python backend\n            // Note: Don't set Content-Type manually - let axios set it automatically for FormData\n            const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(url, formData, {\n                timeout: 300000\n            });\n            console.log(\"\\uD83D\\uDCCA Response received:\", {\n                status: response.status,\n                statusText: response.statusText,\n                success: (_response_data = response.data) === null || _response_data === void 0 ? void 0 : _response_data.success,\n                hasData: !!((_response_data1 = response.data) === null || _response_data1 === void 0 ? void 0 : _response_data1.data),\n                dataKeys: ((_response_data2 = response.data) === null || _response_data2 === void 0 ? void 0 : _response_data2.data) ? Object.keys(response.data.data) : \"no data\",\n                hasSummary: !!((_response_data3 = response.data) === null || _response_data3 === void 0 ? void 0 : (_response_data_data = _response_data3.data) === null || _response_data_data === void 0 ? void 0 : _response_data_data.summary),\n                summaryKeys: ((_response_data4 = response.data) === null || _response_data4 === void 0 ? void 0 : (_response_data_data1 = _response_data4.data) === null || _response_data_data1 === void 0 ? void 0 : _response_data_data1.summary) ? Object.keys(response.data.data.summary) : \"no summary\"\n            });\n            if (response.status === 200 && response.data.success) {\n                var _response_data_data_summary, _response_data_data2, _response_data_data3;\n                // Log the actual response structure for debugging\n                console.log(\"✅ Census processing completed successfully\");\n                console.log(\"\\uD83D\\uDCCA Full response structure:\", response.data);\n                // Try to extract employee count from various possible locations\n                const employeeCount = ((_response_data_data2 = response.data.data) === null || _response_data_data2 === void 0 ? void 0 : (_response_data_data_summary = _response_data_data2.summary) === null || _response_data_data_summary === void 0 ? void 0 : _response_data_data_summary.total_employees) || ((_response_data_data3 = response.data.data) === null || _response_data_data3 === void 0 ? void 0 : _response_data_data3.total_employees) || response.data.total_employees || \"unknown\";\n                console.log(\"\\uD83D\\uDC65 Processed employees: \".concat(employeeCount));\n                return response.data;\n            } else {\n                throw new Error(response.data.message || \"Census processing failed\");\n            }\n        } catch (error) {\n            var _error_response, _error_response1, _error_response2, _error_response3, _error_response4, _error_response5, _error_response_data, _error_response6, _error_message;\n            console.error(\"❌ Census upload failed:\", error);\n            console.error(\"\\uD83D\\uDCCB Error details:\", {\n                message: error.message,\n                code: error.code,\n                status: (_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status,\n                statusText: (_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : _error_response1.statusText,\n                responseData: (_error_response2 = error.response) === null || _error_response2 === void 0 ? void 0 : _error_response2.data\n            });\n            // Provide more specific error messages\n            if (error.code === \"ECONNREFUSED\") {\n                throw new Error(\"Census API service is not running. Please start the Python backend on port 8000.\");\n            } else if (((_error_response3 = error.response) === null || _error_response3 === void 0 ? void 0 : _error_response3.status) === 404) {\n                throw new Error(\"Census API endpoint not found. Please check if the Python backend is running.\");\n            } else if (((_error_response4 = error.response) === null || _error_response4 === void 0 ? void 0 : _error_response4.status) === 413) {\n                throw new Error(\"File too large. Maximum file size is 50MB.\");\n            } else if (((_error_response5 = error.response) === null || _error_response5 === void 0 ? void 0 : _error_response5.status) === 500) {\n                var _error_response_data1, _error_response7;\n                const serverError = ((_error_response7 = error.response) === null || _error_response7 === void 0 ? void 0 : (_error_response_data1 = _error_response7.data) === null || _error_response_data1 === void 0 ? void 0 : _error_response_data1.message) || \"Internal server error during census processing\";\n                throw new Error(\"Server error: \".concat(serverError));\n            } else if ((_error_response6 = error.response) === null || _error_response6 === void 0 ? void 0 : (_error_response_data = _error_response6.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) {\n                throw new Error(error.response.data.message);\n            } else if ((_error_message = error.message) === null || _error_message === void 0 ? void 0 : _error_message.includes(\"undefined\")) {\n                var _error_response8;\n                // Handle response structure mismatch\n                console.log(\"\\uD83D\\uDD0D Response structure debugging:\", (_error_response8 = error.response) === null || _error_response8 === void 0 ? void 0 : _error_response8.data);\n                throw new Error(\"Response structure mismatch - check console for details\");\n            } else {\n                throw new Error(error.message || \"Failed to upload census file\");\n            }\n        }\n    }\n    /**\n   * Get processed census data by company/report ID\n   * Note: This would be implemented when backend supports data persistence\n   */ static async getCensusData(companyId) {\n        try {\n            // For now, this would use the Python backend when persistence is implemented\n            const url = \"\".concat(CENSUS_API_BASE_URL, \"/api/census/reports/\").concat(companyId);\n            const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(url);\n            return response.data;\n        } catch (error) {\n            console.error(\"❌ Failed to fetch census data:\", error);\n            throw new Error(error.message || \"Failed to fetch census data\");\n        }\n    }\n    /**\n   * Get broker dashboard data (list of processed companies)\n   * Note: This would be implemented when backend supports data persistence\n   */ static async getBrokerDashboard() {\n        try {\n            // For now, this would use the Python backend when persistence is implemented\n            const url = \"\".concat(CENSUS_API_BASE_URL, \"/api/census/broker/dashboard\");\n            const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(url);\n            return response.data || [];\n        } catch (error) {\n            console.error(\"❌ Failed to fetch broker dashboard:\", error);\n            // Return empty array as fallback - frontend state management handles this\n            return [];\n        }\n    }\n    /**\n   * Transform API employee data to frontend format\n   */ static transformEmployeeData(apiEmployee) {\n        var _apiEmployee_recommended_plan, _apiEmployee_recommended_plan1;\n        // Parse top 3 plans if available\n        let top3Plans = [];\n        try {\n            if (apiEmployee.top_3_available_plans) {\n                top3Plans = JSON.parse(apiEmployee.top_3_available_plans);\n            }\n        } catch (e) {\n            console.warn(\"Failed to parse top_3_available_plans:\", e);\n        }\n        // Map risk level based on plan confidence\n        const getRiskLevel = (confidence)=>{\n            if (confidence >= 0.8) return \"Low\";\n            if (confidence >= 0.6) return \"Medium\";\n            return \"High\";\n        };\n        return {\n            name: apiEmployee.name,\n            department: \"Dept \".concat(apiEmployee.dept_count),\n            risk: getRiskLevel(apiEmployee.plan_confidence),\n            age: apiEmployee.age,\n            coverage: apiEmployee.predicted_plan_type,\n            hasDependents: apiEmployee.marital_status.toLowerCase() === \"married\",\n            salary: apiEmployee.income_tier,\n            currentPlan: {\n                medical: ((_apiEmployee_recommended_plan = apiEmployee.recommended_plan) === null || _apiEmployee_recommended_plan === void 0 ? void 0 : _apiEmployee_recommended_plan.name) || \"Not Enrolled\",\n                dental: apiEmployee.predicted_benefits.includes(\"Dental\") ? \"Basic\" : \"Not Enrolled\",\n                vision: apiEmployee.predicted_benefits.includes(\"Vision\") ? \"Basic\" : \"Not Enrolled\",\n                life: apiEmployee.predicted_benefits.includes(\"Term Life\") ? \"1x Salary\" : \"None\",\n                disability: apiEmployee.predicted_benefits.includes(\"LTD\") ? \"Basic\" : \"None\"\n            },\n            coverageGaps: [],\n            insights: [\n                apiEmployee.plan_reason\n            ],\n            upsells: [],\n            planFitSummary: {\n                recommendedPlan: ((_apiEmployee_recommended_plan1 = apiEmployee.recommended_plan) === null || _apiEmployee_recommended_plan1 === void 0 ? void 0 : _apiEmployee_recommended_plan1.name) || \"No recommendation\",\n                insight: apiEmployee.plan_reason\n            },\n            // Additional API data\n            apiData: {\n                employee_id: apiEmployee.employee_id,\n                zipcode: apiEmployee.zipcode,\n                city: apiEmployee.city,\n                state: apiEmployee.state,\n                recommended_plan: apiEmployee.recommended_plan,\n                benefits_coverage: apiEmployee.benefits_coverage,\n                top_3_plans: top3Plans,\n                marketplace_plans_available: apiEmployee.marketplace_plans_available,\n                plan_count: apiEmployee.plan_count\n            }\n        };\n    }\n    /**\n   * Transform API response to frontend company data format\n   */ static transformCompanyData(apiResponse) {\n        let companyId = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : \"1\";\n        var _statistics_demographics, _statistics_health_plans_plan_type_distribution, _statistics_health_plans, _statistics_health_plans_plan_type_distribution1, _statistics_health_plans1, _statistics_demographics1;\n        // Handle flexible response structure\n        const data = apiResponse.data || apiResponse;\n        const summary = data.summary || {};\n        const statistics = data.statistics || {};\n        const employees = data.employees || [];\n        console.log(\"\\uD83D\\uDD04 Transforming company data:\", {\n            hasData: !!data,\n            hasSummary: !!summary,\n            hasStatistics: !!statistics,\n            employeeCount: employees.length,\n            summaryKeys: Object.keys(summary),\n            statisticsKeys: Object.keys(statistics)\n        });\n        // Calculate potential savings (simplified calculation)\n        const avgPremium = employees.filter((emp)=>emp.recommended_plan).reduce((sum, emp)=>{\n            var _emp_recommended_plan;\n            return sum + (((_emp_recommended_plan = emp.recommended_plan) === null || _emp_recommended_plan === void 0 ? void 0 : _emp_recommended_plan.premium) || 0);\n        }, 0) / (employees.length || 1);\n        const potentialSavings = Math.round(avgPremium * employees.length * 0.15); // Assume 15% savings\n        // Determine primary plan type\n        const planTypes = Object.keys(statistics.health_plans.plan_type_distribution);\n        const primaryPlanType = planTypes.reduce((a, b)=>statistics.health_plans.plan_type_distribution[a] > statistics.health_plans.plan_type_distribution[b] ? a : b);\n        return {\n            companyName: \"Company \".concat(companyId),\n            employees: summary.total_employees || employees.length || 0,\n            averageAge: Math.round(((_statistics_demographics = statistics.demographics) === null || _statistics_demographics === void 0 ? void 0 : _statistics_demographics.average_age) || 35),\n            dependents: employees.filter((emp)=>{\n                var _emp_marital_status;\n                return ((_emp_marital_status = emp.marital_status) === null || _emp_marital_status === void 0 ? void 0 : _emp_marital_status.toLowerCase()) === \"married\";\n            }).length / (employees.length || 1),\n            planType: primaryPlanType,\n            potentialSavings: \"$\".concat(potentialSavings.toLocaleString()),\n            riskScore: \"\".concat(((summary.data_quality_score || 0.8) * 10).toFixed(1), \"/10\"),\n            uploadDate: new Date().toISOString().split(\"T\")[0],\n            industry: \"Technology\",\n            currentSpend: \"$\".concat(Math.round(avgPremium * employees.length).toLocaleString(), \"/month\"),\n            suggestedPlan: \"\".concat(primaryPlanType, \" with Enhanced Coverage\"),\n            planFitSummary: {\n                silverGoldPPO: Math.round((((_statistics_health_plans = statistics.health_plans) === null || _statistics_health_plans === void 0 ? void 0 : (_statistics_health_plans_plan_type_distribution = _statistics_health_plans.plan_type_distribution) === null || _statistics_health_plans_plan_type_distribution === void 0 ? void 0 : _statistics_health_plans_plan_type_distribution[\"PPO\"]) || 0) / (employees.length || 1) * 100),\n                hdhp: Math.round((((_statistics_health_plans1 = statistics.health_plans) === null || _statistics_health_plans1 === void 0 ? void 0 : (_statistics_health_plans_plan_type_distribution1 = _statistics_health_plans1.plan_type_distribution) === null || _statistics_health_plans_plan_type_distribution1 === void 0 ? void 0 : _statistics_health_plans_plan_type_distribution1[\"HDHP\"]) || 0) / (employees.length || 1) * 100),\n                familyPPO: Math.round(employees.filter((emp)=>{\n                    var _emp_marital_status;\n                    return ((_emp_marital_status = emp.marital_status) === null || _emp_marital_status === void 0 ? void 0 : _emp_marital_status.toLowerCase()) === \"married\";\n                }).length / (employees.length || 1) * 100),\n                insight: \"Based on \".concat(employees.length, \" employees with \").concat((((_statistics_demographics1 = statistics.demographics) === null || _statistics_demographics1 === void 0 ? void 0 : _statistics_demographics1.average_age) || 35).toFixed(1), \" average age\")\n            },\n            employeeProfiles: employees.map((emp)=>this.transformEmployeeData(emp)),\n            // Generate mock upsell opportunities based on company data\n            upsellOpportunities: [\n                {\n                    category: \"Enhanced Coverage\",\n                    description: \"Upgrade \".concat(Math.round(employees.length * 0.3), \" employees to premium plans\"),\n                    savings: \"+$\".concat(Math.round(potentialSavings * 0.1).toLocaleString(), \"/month\"),\n                    confidence: \"85%\",\n                    priority: \"High\"\n                },\n                {\n                    category: \"Wellness Programs\",\n                    description: \"Preventive care initiatives for healthier workforce\",\n                    savings: \"+$\".concat(Math.round(potentialSavings * 0.05).toLocaleString(), \"/month\"),\n                    confidence: \"72%\",\n                    priority: \"Medium\"\n                }\n            ],\n            // Store original API data for reference\n            apiData: apiResponse.data\n        };\n    }\n}\n/* harmony default export */ __webpack_exports__[\"default\"] = (CensusApiService);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/census/services/censusApi.ts\n"));

/***/ })

});