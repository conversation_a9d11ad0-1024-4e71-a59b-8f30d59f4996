"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("_app-pages-browser_src_app_census_components_EmpCensusApp_tsx",{

/***/ "(app-pages-browser)/./src/app/census/services/censusApi.ts":
/*!**********************************************!*\
  !*** ./src/app/census/services/censusApi.ts ***!
  \**********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! axios */ \"(app-pages-browser)/./node_modules/axios/lib/axios.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n// Census API uses the Python backend (chatbot service) instead of the main Node.js backend\nconst CENSUS_API_BASE_URL = \"http://127.0.0.1:8000\" || 0;\nclass CensusApiService {\n    /**\n   * Preprocess CSV file to standardize format before sending to backend\n   */ static async preprocessCsvFile(file) {\n        try {\n            const text = await file.text();\n            const lines = text.split(\"\\n\");\n            if (lines.length === 0) {\n                throw new Error(\"Empty CSV file\");\n            }\n            // Get header row and standardize column names\n            const headers = lines[0].split(\",\").map((header)=>header.trim().replace(/\"/g, \"\") // Remove quotes\n                .toLowerCase().replace(/\\s+/g, \"_\") // Replace spaces with underscores\n                .replace(/[^a-z0-9_]/g, \"\") // Remove special characters except underscores\n            );\n            console.log(\"\\uD83D\\uDCCB Original headers:\", lines[0].split(\",\"));\n            console.log(\"\\uD83D\\uDCCB Standardized headers:\", headers);\n            // Create standardized CSV content\n            const standardizedLines = [\n                headers.join(\",\")\n            ];\n            // Process data rows\n            for(let i = 1; i < lines.length; i++){\n                if (lines[i].trim()) {\n                    // Split CSV line properly (handling quoted values)\n                    const values = lines[i].split(\",\").map((value)=>value.trim().replace(/\"/g, \"\"));\n                    // Standardize values\n                    const standardizedValues = values.map((value, index)=>{\n                        const header = headers[index];\n                        // Standardize common values\n                        if (value === \"N/A\" || value === \"\" || value === \" \") {\n                            return \"\";\n                        }\n                        // Standardize gender\n                        if (header === \"sex\" || header === \"gender\") {\n                            return value.toUpperCase() === \"M\" ? \"Male\" : value.toUpperCase() === \"F\" ? \"Female\" : value;\n                        }\n                        // Standardize marital status\n                        if (header === \"marital_status\") {\n                            var _values_relationshipIndex;\n                            // Get relationship from the same row\n                            const relationshipIndex = standardizedHeaders.indexOf(\"relationship\");\n                            const relationship = relationshipIndex >= 0 ? (_values_relationshipIndex = values[relationshipIndex]) === null || _values_relationshipIndex === void 0 ? void 0 : _values_relationshipIndex.trim() : \"\";\n                            // Only employees need marital status, dependents should be empty or \"N/A\"\n                            if (relationship && relationship.toLowerCase() === \"employee\") {\n                                if (value.toLowerCase().includes(\"married\")) return \"Married\";\n                                if (value.toLowerCase().includes(\"single\")) return \"Single\";\n                                return value || \"Single\"; // Default employees to Single if empty\n                            } else {\n                                // For dependents (Spouse/Child), set to empty or \"N/A\"\n                                return \"\";\n                            }\n                        }\n                        return value;\n                    });\n                    standardizedLines.push(standardizedValues.join(\",\"));\n                }\n            }\n            // Create new file with standardized content and correct MIME type\n            const standardizedContent = standardizedLines.join(\"\\n\");\n            const standardizedFile = new File([\n                standardizedContent\n            ], file.name, {\n                type: \"text/csv\",\n                lastModified: file.lastModified || Date.now()\n            });\n            console.log(\"✅ CSV preprocessing completed\");\n            console.log(\"\\uD83D\\uDCCA Original size:\", file.size, \"bytes\");\n            console.log(\"\\uD83D\\uDCCA Processed size:\", standardizedFile.size, \"bytes\");\n            console.log(\"\\uD83D\\uDCCB Sample processed data:\", standardizedLines.slice(0, 5));\n            return standardizedFile;\n        } catch (error) {\n            console.error(\"❌ CSV preprocessing failed:\", error);\n            // Return original file if preprocessing fails\n            return file;\n        }\n    }\n    /**\n   * Upload and process census file using the Python backend (chatbot service)\n   */ static async uploadCensusFile(file) {\n        let returnDataframe = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;\n        try {\n            var _response_data, _response_data1, _response_data2, _response_data_data, _response_data3, _response_data_data1, _response_data4;\n            // Preprocess the CSV file to standardize format\n            console.log(\"\\uD83D\\uDD04 Preprocessing CSV file: \".concat(file.name));\n            const processedFile = await this.preprocessCsvFile(file);\n            // Create FormData with proper file type\n            const formData = new FormData();\n            // For CSV files, ensure correct MIME type in FormData\n            if (processedFile.name.toLowerCase().endsWith(\".csv\")) {\n                console.log(\"\\uD83D\\uDD27 Ensuring CSV file has correct MIME type\");\n                // Create a new File with correct MIME type for CSV\n                const csvFile = new File([\n                    processedFile\n                ], processedFile.name, {\n                    type: \"text/csv\",\n                    lastModified: processedFile.lastModified || Date.now()\n                });\n                formData.append(\"file\", csvFile);\n                console.log(\"\\uD83D\\uDCCB CSV file details:\", {\n                    name: csvFile.name,\n                    size: csvFile.size,\n                    type: csvFile.type,\n                    lastModified: csvFile.lastModified\n                });\n            } else {\n                // For non-CSV files, use as-is\n                formData.append(\"file\", processedFile);\n                console.log(\"\\uD83D\\uDCCB File details:\", {\n                    name: processedFile.name,\n                    size: processedFile.size,\n                    type: processedFile.type,\n                    lastModified: processedFile.lastModified\n                });\n            }\n            // Log FormData details\n            const fileEntry = formData.get(\"file\");\n            console.log(\"\\uD83D\\uDCCB FormData details:\", {\n                hasFile: formData.has(\"file\"),\n                fileName: fileEntry === null || fileEntry === void 0 ? void 0 : fileEntry.name,\n                fileType: fileEntry === null || fileEntry === void 0 ? void 0 : fileEntry.type,\n                fileSize: fileEntry === null || fileEntry === void 0 ? void 0 : fileEntry.size\n            });\n            // Verify the request will have correct Content-Type\n            console.log(\"\\uD83C\\uDF10 Request will use Content-Type: multipart/form-data (set automatically by browser)\");\n            // Build full URL for census API (Python backend)\n            const url = \"\".concat(CENSUS_API_BASE_URL, \"/api/census/processor/v1?return_dataframe=\").concat(returnDataframe);\n            console.log(\"\\uD83D\\uDCE4 Uploading census file: \".concat(processedFile.name, \" (\").concat((processedFile.size / 1024 / 1024).toFixed(2), \" MB)\"));\n            console.log(\"\\uD83D\\uDD17 Census API URL: \".concat(url));\n            console.log(\"\\uD83D\\uDCCB Request details:\", {\n                method: \"POST\",\n                url: url,\n                fileSize: processedFile.size,\n                fileName: processedFile.name,\n                returnDataframe: returnDataframe\n            });\n            // Use axios directly for census API calls to Python backend\n            // Note: Don't set Content-Type manually - let axios set it automatically for FormData\n            const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(url, formData, {\n                timeout: 300000\n            });\n            console.log(\"\\uD83D\\uDCCA Response received:\", {\n                status: response.status,\n                statusText: response.statusText,\n                success: (_response_data = response.data) === null || _response_data === void 0 ? void 0 : _response_data.success,\n                hasData: !!((_response_data1 = response.data) === null || _response_data1 === void 0 ? void 0 : _response_data1.data),\n                dataKeys: ((_response_data2 = response.data) === null || _response_data2 === void 0 ? void 0 : _response_data2.data) ? Object.keys(response.data.data) : \"no data\",\n                hasSummary: !!((_response_data3 = response.data) === null || _response_data3 === void 0 ? void 0 : (_response_data_data = _response_data3.data) === null || _response_data_data === void 0 ? void 0 : _response_data_data.summary),\n                summaryKeys: ((_response_data4 = response.data) === null || _response_data4 === void 0 ? void 0 : (_response_data_data1 = _response_data4.data) === null || _response_data_data1 === void 0 ? void 0 : _response_data_data1.summary) ? Object.keys(response.data.data.summary) : \"no summary\"\n            });\n            if (response.status === 200) {\n                if (response.data.success) {\n                    var _response_data_data_summary, _response_data_data2, _response_data_data3;\n                    // Check if the inner data also indicates success\n                    if (response.data.data && response.data.data.success === false) {\n                        // Inner processing failed\n                        console.error(\"❌ Census processing failed:\", response.data.data);\n                        console.error(\"\\uD83D\\uDCCB Error details:\", {\n                            error: response.data.data.error,\n                            message: response.data.data.message,\n                            status_code: response.data.data.status_code,\n                            fullErrorData: response.data.data\n                        });\n                        const errorMessage = response.data.data.message || \"Processing failed: \".concat(response.data.data.error) || 0;\n                        throw new Error(errorMessage);\n                    }\n                    // Log the actual response structure for debugging\n                    console.log(\"✅ Census processing completed successfully\");\n                    console.log(\"\\uD83D\\uDCCA Full response structure:\", response.data);\n                    // Try to extract employee count from various possible locations\n                    const employeeCount = ((_response_data_data2 = response.data.data) === null || _response_data_data2 === void 0 ? void 0 : (_response_data_data_summary = _response_data_data2.summary) === null || _response_data_data_summary === void 0 ? void 0 : _response_data_data_summary.total_employees) || ((_response_data_data3 = response.data.data) === null || _response_data_data3 === void 0 ? void 0 : _response_data_data3.total_employees) || response.data.total_employees || \"unknown\";\n                    console.log(\"\\uD83D\\uDC65 Processed employees: \".concat(employeeCount));\n                    return response.data;\n                } else {\n                    // Outer response indicates failure\n                    console.error(\"❌ Backend processing failed:\", response.data);\n                    throw new Error(response.data.message || \"Census processing failed on backend\");\n                }\n            } else {\n                throw new Error(\"HTTP \".concat(response.status, \": \").concat(response.statusText));\n            }\n        } catch (error) {\n            var _error_response, _error_response1, _error_response2, _error_response3, _error_response4, _error_response5, _error_response_data, _error_response6, _error_message;\n            console.error(\"❌ Census upload failed:\", error);\n            console.error(\"\\uD83D\\uDCCB Error details:\", {\n                message: error.message,\n                code: error.code,\n                status: (_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status,\n                statusText: (_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : _error_response1.statusText,\n                responseData: (_error_response2 = error.response) === null || _error_response2 === void 0 ? void 0 : _error_response2.data\n            });\n            // Provide more specific error messages\n            if (error.code === \"ECONNREFUSED\") {\n                throw new Error(\"Census API service is not running. Please start the Python backend on port 8000.\");\n            } else if (((_error_response3 = error.response) === null || _error_response3 === void 0 ? void 0 : _error_response3.status) === 404) {\n                throw new Error(\"Census API endpoint not found. Please check if the Python backend is running.\");\n            } else if (((_error_response4 = error.response) === null || _error_response4 === void 0 ? void 0 : _error_response4.status) === 413) {\n                throw new Error(\"File too large. Maximum file size is 50MB.\");\n            } else if (((_error_response5 = error.response) === null || _error_response5 === void 0 ? void 0 : _error_response5.status) === 500) {\n                var _error_response_data1, _error_response7;\n                const serverError = ((_error_response7 = error.response) === null || _error_response7 === void 0 ? void 0 : (_error_response_data1 = _error_response7.data) === null || _error_response_data1 === void 0 ? void 0 : _error_response_data1.message) || \"Internal server error during census processing\";\n                throw new Error(\"Server error: \".concat(serverError));\n            } else if ((_error_response6 = error.response) === null || _error_response6 === void 0 ? void 0 : (_error_response_data = _error_response6.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) {\n                throw new Error(error.response.data.message);\n            } else if ((_error_message = error.message) === null || _error_message === void 0 ? void 0 : _error_message.includes(\"undefined\")) {\n                var _error_response8;\n                // Handle response structure mismatch\n                console.log(\"\\uD83D\\uDD0D Response structure debugging:\", (_error_response8 = error.response) === null || _error_response8 === void 0 ? void 0 : _error_response8.data);\n                throw new Error(\"Response structure mismatch - check console for details\");\n            } else {\n                throw new Error(error.message || \"Failed to upload census file\");\n            }\n        }\n    }\n    /**\n   * Get processed census data by company/report ID\n   * Note: This would be implemented when backend supports data persistence\n   */ static async getCensusData(companyId) {\n        try {\n            // For now, this would use the Python backend when persistence is implemented\n            const url = \"\".concat(CENSUS_API_BASE_URL, \"/api/census/reports/\").concat(companyId);\n            const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(url);\n            return response.data;\n        } catch (error) {\n            console.error(\"❌ Failed to fetch census data:\", error);\n            throw new Error(error.message || \"Failed to fetch census data\");\n        }\n    }\n    /**\n   * Get broker dashboard data (list of processed companies)\n   * Note: This would be implemented when backend supports data persistence\n   */ static async getBrokerDashboard() {\n        try {\n            // For now, this would use the Python backend when persistence is implemented\n            const url = \"\".concat(CENSUS_API_BASE_URL, \"/api/census/broker/dashboard\");\n            const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(url);\n            return response.data || [];\n        } catch (error) {\n            console.error(\"❌ Failed to fetch broker dashboard:\", error);\n            // Return empty array as fallback - frontend state management handles this\n            return [];\n        }\n    }\n    /**\n   * Transform API employee data to frontend format\n   */ static transformEmployeeData(apiEmployee) {\n        var _apiEmployee_recommended_plan, _apiEmployee_recommended_plan1;\n        // Parse top 3 plans if available\n        let top3Plans = [];\n        try {\n            if (apiEmployee.top_3_available_plans) {\n                top3Plans = JSON.parse(apiEmployee.top_3_available_plans);\n            }\n        } catch (e) {\n            console.warn(\"Failed to parse top_3_available_plans:\", e);\n        }\n        // Map risk level based on plan confidence\n        const getRiskLevel = (confidence)=>{\n            if (confidence >= 0.8) return \"Low\";\n            if (confidence >= 0.6) return \"Medium\";\n            return \"High\";\n        };\n        return {\n            name: apiEmployee.name,\n            department: \"Dept \".concat(apiEmployee.dept_count),\n            risk: getRiskLevel(apiEmployee.plan_confidence),\n            age: apiEmployee.age,\n            coverage: apiEmployee.predicted_plan_type,\n            hasDependents: apiEmployee.marital_status.toLowerCase() === \"married\",\n            salary: apiEmployee.income_tier,\n            currentPlan: {\n                medical: ((_apiEmployee_recommended_plan = apiEmployee.recommended_plan) === null || _apiEmployee_recommended_plan === void 0 ? void 0 : _apiEmployee_recommended_plan.name) || \"Not Enrolled\",\n                dental: apiEmployee.predicted_benefits.includes(\"Dental\") ? \"Basic\" : \"Not Enrolled\",\n                vision: apiEmployee.predicted_benefits.includes(\"Vision\") ? \"Basic\" : \"Not Enrolled\",\n                life: apiEmployee.predicted_benefits.includes(\"Term Life\") ? \"1x Salary\" : \"None\",\n                disability: apiEmployee.predicted_benefits.includes(\"LTD\") ? \"Basic\" : \"None\"\n            },\n            coverageGaps: [],\n            insights: [\n                apiEmployee.plan_reason\n            ],\n            upsells: [],\n            planFitSummary: {\n                recommendedPlan: ((_apiEmployee_recommended_plan1 = apiEmployee.recommended_plan) === null || _apiEmployee_recommended_plan1 === void 0 ? void 0 : _apiEmployee_recommended_plan1.name) || \"No recommendation\",\n                insight: apiEmployee.plan_reason\n            },\n            // Additional API data\n            apiData: {\n                employee_id: apiEmployee.employee_id,\n                zipcode: apiEmployee.zipcode,\n                city: apiEmployee.city,\n                state: apiEmployee.state,\n                recommended_plan: apiEmployee.recommended_plan,\n                benefits_coverage: apiEmployee.benefits_coverage,\n                top_3_plans: top3Plans,\n                marketplace_plans_available: apiEmployee.marketplace_plans_available,\n                plan_count: apiEmployee.plan_count\n            }\n        };\n    }\n    /**\n   * Transform API response to frontend company data format\n   */ static transformCompanyData(apiResponse) {\n        let companyId = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : \"1\";\n        var _statistics_health_plans, _statistics_demographics, _statistics_demographics1;\n        console.log(\"\\uD83D\\uDD04 Raw API response for transformation:\", apiResponse);\n        // Handle flexible response structure - try multiple possible structures\n        let data, summary, statistics, employees;\n        if (apiResponse.data) {\n            data = apiResponse.data;\n            summary = data.summary || {};\n            statistics = data.statistics || {};\n            employees = data.employees || [];\n        } else {\n            // Direct response without .data wrapper\n            data = apiResponse;\n            summary = data.summary || {};\n            statistics = data.statistics || {};\n            employees = data.employees || [];\n        }\n        console.log(\"\\uD83D\\uDD04 Transforming company data:\", {\n            hasData: !!data,\n            hasSummary: !!summary,\n            hasStatistics: !!statistics,\n            employeeCount: employees.length,\n            summaryKeys: Object.keys(summary),\n            statisticsKeys: Object.keys(statistics),\n            dataKeys: Object.keys(data),\n            fullData: data // Log the full data to see what's actually there\n        });\n        // If we don't have the expected structure, create a minimal response\n        if (!summary.total_employees && !employees.length) {\n            console.warn(\"⚠️ No employee data found, creating minimal response\");\n            return {\n                companyName: \"Company \".concat(companyId),\n                employees: 0,\n                averageAge: 35,\n                dependents: 0,\n                planType: \"Unknown\",\n                potentialSavings: \"$0\",\n                riskScore: \"0.0/10\",\n                uploadDate: new Date().toISOString().split(\"T\")[0],\n                industry: \"Unknown\",\n                currentSpend: \"$0/month\",\n                suggestedPlan: \"No data available\",\n                planFitSummary: {\n                    silverGoldPPO: 0,\n                    hdhp: 0,\n                    familyPPO: 0,\n                    insight: \"No employee data available\"\n                },\n                employeeProfiles: [],\n                upsellOpportunities: [],\n                apiData: apiResponse\n            };\n        }\n        // Calculate potential savings (simplified calculation)\n        const avgPremium = employees.filter((emp)=>emp.recommended_plan).reduce((sum, emp)=>{\n            var _emp_recommended_plan;\n            return sum + (((_emp_recommended_plan = emp.recommended_plan) === null || _emp_recommended_plan === void 0 ? void 0 : _emp_recommended_plan.premium) || 0);\n        }, 0) / (employees.length || 1);\n        const potentialSavings = Math.round(avgPremium * employees.length * 0.15); // Assume 15% savings\n        // Determine primary plan type with null safety\n        const planTypeDistribution = ((_statistics_health_plans = statistics.health_plans) === null || _statistics_health_plans === void 0 ? void 0 : _statistics_health_plans.plan_type_distribution) || {};\n        const planTypes = Object.keys(planTypeDistribution);\n        const primaryPlanType = planTypes.length > 0 ? planTypes.reduce((a, b)=>planTypeDistribution[a] > planTypeDistribution[b] ? a : b) : \"PPO\"; // Default fallback\n        return {\n            companyName: \"Company \".concat(companyId),\n            employees: summary.total_employees || employees.length || 0,\n            averageAge: Math.round(((_statistics_demographics = statistics.demographics) === null || _statistics_demographics === void 0 ? void 0 : _statistics_demographics.average_age) || 35),\n            dependents: employees.filter((emp)=>{\n                var _emp_marital_status;\n                return ((_emp_marital_status = emp.marital_status) === null || _emp_marital_status === void 0 ? void 0 : _emp_marital_status.toLowerCase()) === \"married\";\n            }).length / (employees.length || 1),\n            planType: primaryPlanType,\n            potentialSavings: \"$\".concat(potentialSavings.toLocaleString()),\n            riskScore: \"\".concat(((summary.data_quality_score || 0.8) * 10).toFixed(1), \"/10\"),\n            uploadDate: new Date().toISOString().split(\"T\")[0],\n            industry: \"Technology\",\n            currentSpend: \"$\".concat(Math.round(avgPremium * employees.length).toLocaleString(), \"/month\"),\n            suggestedPlan: \"\".concat(primaryPlanType, \" with Enhanced Coverage\"),\n            planFitSummary: {\n                silverGoldPPO: Math.round((planTypeDistribution[\"PPO\"] || 0) / (employees.length || 1) * 100),\n                hdhp: Math.round((planTypeDistribution[\"HDHP\"] || 0) / (employees.length || 1) * 100),\n                familyPPO: Math.round(employees.filter((emp)=>{\n                    var _emp_marital_status;\n                    return ((_emp_marital_status = emp.marital_status) === null || _emp_marital_status === void 0 ? void 0 : _emp_marital_status.toLowerCase()) === \"married\";\n                }).length / (employees.length || 1) * 100),\n                insight: \"Based on \".concat(employees.length || 0, \" employees with \").concat((((_statistics_demographics1 = statistics.demographics) === null || _statistics_demographics1 === void 0 ? void 0 : _statistics_demographics1.average_age) || 35).toFixed(1), \" average age\")\n            },\n            employeeProfiles: employees.map((emp)=>this.transformEmployeeData(emp)),\n            // Generate mock upsell opportunities based on company data\n            upsellOpportunities: [\n                {\n                    category: \"Enhanced Coverage\",\n                    description: \"Upgrade \".concat(Math.round(employees.length * 0.3), \" employees to premium plans\"),\n                    savings: \"+$\".concat(Math.round(potentialSavings * 0.1).toLocaleString(), \"/month\"),\n                    confidence: \"85%\",\n                    priority: \"High\"\n                },\n                {\n                    category: \"Wellness Programs\",\n                    description: \"Preventive care initiatives for healthier workforce\",\n                    savings: \"+$\".concat(Math.round(potentialSavings * 0.05).toLocaleString(), \"/month\"),\n                    confidence: \"72%\",\n                    priority: \"Medium\"\n                }\n            ],\n            // Store original API data for reference\n            apiData: apiResponse.data\n        };\n    }\n}\n/* harmony default export */ __webpack_exports__[\"default\"] = (CensusApiService);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/census/services/censusApi.ts\n"));

/***/ })

});