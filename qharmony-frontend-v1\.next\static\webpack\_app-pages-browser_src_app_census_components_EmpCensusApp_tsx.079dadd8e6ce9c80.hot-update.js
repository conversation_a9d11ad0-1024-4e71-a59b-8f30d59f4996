"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("_app-pages-browser_src_app_census_components_EmpCensusApp_tsx",{

/***/ "(app-pages-browser)/./src/app/census/services/censusApi.ts":
/*!**********************************************!*\
  !*** ./src/app/census/services/censusApi.ts ***!
  \**********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! axios */ \"(app-pages-browser)/./node_modules/axios/lib/axios.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n// Census API uses the Python backend (chatbot service) instead of the main Node.js backend\nconst CENSUS_API_BASE_URL = \"http://127.0.0.1:8000\" || 0;\nclass CensusApiService {\n    /**\n   * Preprocess CSV file to standardize format before sending to backend\n   */ static async preprocessCsvFile(file) {\n        try {\n            const text = await file.text();\n            const lines = text.split(\"\\n\");\n            if (lines.length === 0) {\n                throw new Error(\"Empty CSV file\");\n            }\n            // Get header row and standardize column names\n            const headers = lines[0].split(\",\").map((header)=>header.trim().replace(/\"/g, \"\") // Remove quotes\n                .toLowerCase().replace(/\\s+/g, \"_\") // Replace spaces with underscores\n                .replace(/[^a-z0-9_]/g, \"\") // Remove special characters except underscores\n            );\n            console.log(\"\\uD83D\\uDCCB Original headers:\", lines[0].split(\",\"));\n            console.log(\"\\uD83D\\uDCCB Standardized headers:\", headers);\n            // Create standardized CSV content\n            const standardizedLines = [\n                headers.join(\",\")\n            ];\n            // Process data rows\n            for(let i = 1; i < lines.length; i++){\n                if (lines[i].trim()) {\n                    // Split CSV line properly (handling quoted values)\n                    const values = lines[i].split(\",\").map((value)=>value.trim().replace(/\"/g, \"\"));\n                    // Standardize values\n                    const standardizedValues = values.map((value, index)=>{\n                        const header = headers[index];\n                        // Standardize common values\n                        if (value === \"N/A\" || value === \"\" || value === \" \") {\n                            return \"\";\n                        }\n                        // Standardize gender\n                        if (header === \"sex\" || header === \"gender\") {\n                            return value.toUpperCase() === \"M\" ? \"Male\" : value.toUpperCase() === \"F\" ? \"Female\" : value;\n                        }\n                        // Standardize marital status\n                        if (header === \"marital_status\") {\n                            return value ? \"Married\" : \"Single\";\n                        }\n                        return value;\n                    });\n                    standardizedLines.push(standardizedValues.join(\",\"));\n                }\n            }\n            // Create new file with standardized content\n            const standardizedContent = standardizedLines.join(\"\\n\");\n            const standardizedFile = new File([\n                standardizedContent\n            ], file.name, {\n                type: \"text/csv\"\n            });\n            console.log(\"✅ CSV preprocessing completed\");\n            console.log(\"\\uD83D\\uDCCA Original size:\", file.size, \"bytes\");\n            console.log(\"\\uD83D\\uDCCA Processed size:\", standardizedFile.size, \"bytes\");\n            return standardizedFile;\n        } catch (error) {\n            console.error(\"❌ CSV preprocessing failed:\", error);\n            // Return original file if preprocessing fails\n            return file;\n        }\n    }\n    /**\n   * Upload and process census file using the Python backend (chatbot service)\n   */ static async uploadCensusFile(file) {\n        let returnDataframe = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;\n        try {\n            var _response_data, _response_data1, _response_data2, _response_data_data, _response_data3, _response_data_data1, _response_data4;\n            const formData = new FormData();\n            formData.append(\"file\", file);\n            // Build full URL for census API (Python backend)\n            const url = \"\".concat(CENSUS_API_BASE_URL, \"/api/census/processor/v1?return_dataframe=\").concat(returnDataframe);\n            console.log(\"\\uD83D\\uDCE4 Uploading census file: \".concat(file.name, \" (\").concat((file.size / 1024 / 1024).toFixed(2), \" MB)\"));\n            console.log(\"\\uD83D\\uDD17 Census API URL: \".concat(url));\n            console.log(\"\\uD83D\\uDCCB Request details:\", {\n                method: \"POST\",\n                url: url,\n                fileSize: file.size,\n                fileName: file.name,\n                returnDataframe: returnDataframe\n            });\n            // Use axios directly for census API calls to Python backend\n            // Note: Don't set Content-Type manually - let axios set it automatically for FormData\n            const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(url, formData, {\n                timeout: 300000\n            });\n            console.log(\"\\uD83D\\uDCCA Response received:\", {\n                status: response.status,\n                statusText: response.statusText,\n                success: (_response_data = response.data) === null || _response_data === void 0 ? void 0 : _response_data.success,\n                hasData: !!((_response_data1 = response.data) === null || _response_data1 === void 0 ? void 0 : _response_data1.data),\n                dataKeys: ((_response_data2 = response.data) === null || _response_data2 === void 0 ? void 0 : _response_data2.data) ? Object.keys(response.data.data) : \"no data\",\n                hasSummary: !!((_response_data3 = response.data) === null || _response_data3 === void 0 ? void 0 : (_response_data_data = _response_data3.data) === null || _response_data_data === void 0 ? void 0 : _response_data_data.summary),\n                summaryKeys: ((_response_data4 = response.data) === null || _response_data4 === void 0 ? void 0 : (_response_data_data1 = _response_data4.data) === null || _response_data_data1 === void 0 ? void 0 : _response_data_data1.summary) ? Object.keys(response.data.data.summary) : \"no summary\"\n            });\n            if (response.status === 200) {\n                if (response.data.success) {\n                    var _response_data_data_summary, _response_data_data2, _response_data_data3;\n                    // Check if the inner data also indicates success\n                    if (response.data.data && response.data.data.success === false) {\n                        // Inner processing failed\n                        console.error(\"❌ Census processing failed:\", response.data.data);\n                        console.error(\"\\uD83D\\uDCCB Error details:\", {\n                            error: response.data.data.error,\n                            message: response.data.data.message,\n                            status_code: response.data.data.status_code,\n                            fullErrorData: response.data.data\n                        });\n                        const errorMessage = response.data.data.message || \"Processing failed: \".concat(response.data.data.error) || 0;\n                        throw new Error(errorMessage);\n                    }\n                    // Log the actual response structure for debugging\n                    console.log(\"✅ Census processing completed successfully\");\n                    console.log(\"\\uD83D\\uDCCA Full response structure:\", response.data);\n                    // Try to extract employee count from various possible locations\n                    const employeeCount = ((_response_data_data2 = response.data.data) === null || _response_data_data2 === void 0 ? void 0 : (_response_data_data_summary = _response_data_data2.summary) === null || _response_data_data_summary === void 0 ? void 0 : _response_data_data_summary.total_employees) || ((_response_data_data3 = response.data.data) === null || _response_data_data3 === void 0 ? void 0 : _response_data_data3.total_employees) || response.data.total_employees || \"unknown\";\n                    console.log(\"\\uD83D\\uDC65 Processed employees: \".concat(employeeCount));\n                    return response.data;\n                } else {\n                    // Outer response indicates failure\n                    console.error(\"❌ Backend processing failed:\", response.data);\n                    throw new Error(response.data.message || \"Census processing failed on backend\");\n                }\n            } else {\n                throw new Error(\"HTTP \".concat(response.status, \": \").concat(response.statusText));\n            }\n        } catch (error) {\n            var _error_response, _error_response1, _error_response2, _error_response3, _error_response4, _error_response5, _error_response_data, _error_response6, _error_message;\n            console.error(\"❌ Census upload failed:\", error);\n            console.error(\"\\uD83D\\uDCCB Error details:\", {\n                message: error.message,\n                code: error.code,\n                status: (_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status,\n                statusText: (_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : _error_response1.statusText,\n                responseData: (_error_response2 = error.response) === null || _error_response2 === void 0 ? void 0 : _error_response2.data\n            });\n            // Provide more specific error messages\n            if (error.code === \"ECONNREFUSED\") {\n                throw new Error(\"Census API service is not running. Please start the Python backend on port 8000.\");\n            } else if (((_error_response3 = error.response) === null || _error_response3 === void 0 ? void 0 : _error_response3.status) === 404) {\n                throw new Error(\"Census API endpoint not found. Please check if the Python backend is running.\");\n            } else if (((_error_response4 = error.response) === null || _error_response4 === void 0 ? void 0 : _error_response4.status) === 413) {\n                throw new Error(\"File too large. Maximum file size is 50MB.\");\n            } else if (((_error_response5 = error.response) === null || _error_response5 === void 0 ? void 0 : _error_response5.status) === 500) {\n                var _error_response_data1, _error_response7;\n                const serverError = ((_error_response7 = error.response) === null || _error_response7 === void 0 ? void 0 : (_error_response_data1 = _error_response7.data) === null || _error_response_data1 === void 0 ? void 0 : _error_response_data1.message) || \"Internal server error during census processing\";\n                throw new Error(\"Server error: \".concat(serverError));\n            } else if ((_error_response6 = error.response) === null || _error_response6 === void 0 ? void 0 : (_error_response_data = _error_response6.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) {\n                throw new Error(error.response.data.message);\n            } else if ((_error_message = error.message) === null || _error_message === void 0 ? void 0 : _error_message.includes(\"undefined\")) {\n                var _error_response8;\n                // Handle response structure mismatch\n                console.log(\"\\uD83D\\uDD0D Response structure debugging:\", (_error_response8 = error.response) === null || _error_response8 === void 0 ? void 0 : _error_response8.data);\n                throw new Error(\"Response structure mismatch - check console for details\");\n            } else {\n                throw new Error(error.message || \"Failed to upload census file\");\n            }\n        }\n    }\n    /**\n   * Get processed census data by company/report ID\n   * Note: This would be implemented when backend supports data persistence\n   */ static async getCensusData(companyId) {\n        try {\n            // For now, this would use the Python backend when persistence is implemented\n            const url = \"\".concat(CENSUS_API_BASE_URL, \"/api/census/reports/\").concat(companyId);\n            const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(url);\n            return response.data;\n        } catch (error) {\n            console.error(\"❌ Failed to fetch census data:\", error);\n            throw new Error(error.message || \"Failed to fetch census data\");\n        }\n    }\n    /**\n   * Get broker dashboard data (list of processed companies)\n   * Note: This would be implemented when backend supports data persistence\n   */ static async getBrokerDashboard() {\n        try {\n            // For now, this would use the Python backend when persistence is implemented\n            const url = \"\".concat(CENSUS_API_BASE_URL, \"/api/census/broker/dashboard\");\n            const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(url);\n            return response.data || [];\n        } catch (error) {\n            console.error(\"❌ Failed to fetch broker dashboard:\", error);\n            // Return empty array as fallback - frontend state management handles this\n            return [];\n        }\n    }\n    /**\n   * Transform API employee data to frontend format\n   */ static transformEmployeeData(apiEmployee) {\n        var _apiEmployee_recommended_plan, _apiEmployee_recommended_plan1;\n        // Parse top 3 plans if available\n        let top3Plans = [];\n        try {\n            if (apiEmployee.top_3_available_plans) {\n                top3Plans = JSON.parse(apiEmployee.top_3_available_plans);\n            }\n        } catch (e) {\n            console.warn(\"Failed to parse top_3_available_plans:\", e);\n        }\n        // Map risk level based on plan confidence\n        const getRiskLevel = (confidence)=>{\n            if (confidence >= 0.8) return \"Low\";\n            if (confidence >= 0.6) return \"Medium\";\n            return \"High\";\n        };\n        return {\n            name: apiEmployee.name,\n            department: \"Dept \".concat(apiEmployee.dept_count),\n            risk: getRiskLevel(apiEmployee.plan_confidence),\n            age: apiEmployee.age,\n            coverage: apiEmployee.predicted_plan_type,\n            hasDependents: apiEmployee.marital_status.toLowerCase() === \"married\",\n            salary: apiEmployee.income_tier,\n            currentPlan: {\n                medical: ((_apiEmployee_recommended_plan = apiEmployee.recommended_plan) === null || _apiEmployee_recommended_plan === void 0 ? void 0 : _apiEmployee_recommended_plan.name) || \"Not Enrolled\",\n                dental: apiEmployee.predicted_benefits.includes(\"Dental\") ? \"Basic\" : \"Not Enrolled\",\n                vision: apiEmployee.predicted_benefits.includes(\"Vision\") ? \"Basic\" : \"Not Enrolled\",\n                life: apiEmployee.predicted_benefits.includes(\"Term Life\") ? \"1x Salary\" : \"None\",\n                disability: apiEmployee.predicted_benefits.includes(\"LTD\") ? \"Basic\" : \"None\"\n            },\n            coverageGaps: [],\n            insights: [\n                apiEmployee.plan_reason\n            ],\n            upsells: [],\n            planFitSummary: {\n                recommendedPlan: ((_apiEmployee_recommended_plan1 = apiEmployee.recommended_plan) === null || _apiEmployee_recommended_plan1 === void 0 ? void 0 : _apiEmployee_recommended_plan1.name) || \"No recommendation\",\n                insight: apiEmployee.plan_reason\n            },\n            // Additional API data\n            apiData: {\n                employee_id: apiEmployee.employee_id,\n                zipcode: apiEmployee.zipcode,\n                city: apiEmployee.city,\n                state: apiEmployee.state,\n                recommended_plan: apiEmployee.recommended_plan,\n                benefits_coverage: apiEmployee.benefits_coverage,\n                top_3_plans: top3Plans,\n                marketplace_plans_available: apiEmployee.marketplace_plans_available,\n                plan_count: apiEmployee.plan_count\n            }\n        };\n    }\n    /**\n   * Transform API response to frontend company data format\n   */ static transformCompanyData(apiResponse) {\n        let companyId = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : \"1\";\n        var _statistics_health_plans, _statistics_demographics, _statistics_demographics1;\n        console.log(\"\\uD83D\\uDD04 Raw API response for transformation:\", apiResponse);\n        // Handle flexible response structure - try multiple possible structures\n        let data, summary, statistics, employees;\n        if (apiResponse.data) {\n            data = apiResponse.data;\n            summary = data.summary || {};\n            statistics = data.statistics || {};\n            employees = data.employees || [];\n        } else {\n            // Direct response without .data wrapper\n            data = apiResponse;\n            summary = data.summary || {};\n            statistics = data.statistics || {};\n            employees = data.employees || [];\n        }\n        console.log(\"\\uD83D\\uDD04 Transforming company data:\", {\n            hasData: !!data,\n            hasSummary: !!summary,\n            hasStatistics: !!statistics,\n            employeeCount: employees.length,\n            summaryKeys: Object.keys(summary),\n            statisticsKeys: Object.keys(statistics),\n            dataKeys: Object.keys(data),\n            fullData: data // Log the full data to see what's actually there\n        });\n        // If we don't have the expected structure, create a minimal response\n        if (!summary.total_employees && !employees.length) {\n            console.warn(\"⚠️ No employee data found, creating minimal response\");\n            return {\n                companyName: \"Company \".concat(companyId),\n                employees: 0,\n                averageAge: 35,\n                dependents: 0,\n                planType: \"Unknown\",\n                potentialSavings: \"$0\",\n                riskScore: \"0.0/10\",\n                uploadDate: new Date().toISOString().split(\"T\")[0],\n                industry: \"Unknown\",\n                currentSpend: \"$0/month\",\n                suggestedPlan: \"No data available\",\n                planFitSummary: {\n                    silverGoldPPO: 0,\n                    hdhp: 0,\n                    familyPPO: 0,\n                    insight: \"No employee data available\"\n                },\n                employeeProfiles: [],\n                upsellOpportunities: [],\n                apiData: apiResponse\n            };\n        }\n        // Calculate potential savings (simplified calculation)\n        const avgPremium = employees.filter((emp)=>emp.recommended_plan).reduce((sum, emp)=>{\n            var _emp_recommended_plan;\n            return sum + (((_emp_recommended_plan = emp.recommended_plan) === null || _emp_recommended_plan === void 0 ? void 0 : _emp_recommended_plan.premium) || 0);\n        }, 0) / (employees.length || 1);\n        const potentialSavings = Math.round(avgPremium * employees.length * 0.15); // Assume 15% savings\n        // Determine primary plan type with null safety\n        const planTypeDistribution = ((_statistics_health_plans = statistics.health_plans) === null || _statistics_health_plans === void 0 ? void 0 : _statistics_health_plans.plan_type_distribution) || {};\n        const planTypes = Object.keys(planTypeDistribution);\n        const primaryPlanType = planTypes.length > 0 ? planTypes.reduce((a, b)=>planTypeDistribution[a] > planTypeDistribution[b] ? a : b) : \"PPO\"; // Default fallback\n        return {\n            companyName: \"Company \".concat(companyId),\n            employees: summary.total_employees || employees.length || 0,\n            averageAge: Math.round(((_statistics_demographics = statistics.demographics) === null || _statistics_demographics === void 0 ? void 0 : _statistics_demographics.average_age) || 35),\n            dependents: employees.filter((emp)=>{\n                var _emp_marital_status;\n                return ((_emp_marital_status = emp.marital_status) === null || _emp_marital_status === void 0 ? void 0 : _emp_marital_status.toLowerCase()) === \"married\";\n            }).length / (employees.length || 1),\n            planType: primaryPlanType,\n            potentialSavings: \"$\".concat(potentialSavings.toLocaleString()),\n            riskScore: \"\".concat(((summary.data_quality_score || 0.8) * 10).toFixed(1), \"/10\"),\n            uploadDate: new Date().toISOString().split(\"T\")[0],\n            industry: \"Technology\",\n            currentSpend: \"$\".concat(Math.round(avgPremium * employees.length).toLocaleString(), \"/month\"),\n            suggestedPlan: \"\".concat(primaryPlanType, \" with Enhanced Coverage\"),\n            planFitSummary: {\n                silverGoldPPO: Math.round((planTypeDistribution[\"PPO\"] || 0) / (employees.length || 1) * 100),\n                hdhp: Math.round((planTypeDistribution[\"HDHP\"] || 0) / (employees.length || 1) * 100),\n                familyPPO: Math.round(employees.filter((emp)=>{\n                    var _emp_marital_status;\n                    return ((_emp_marital_status = emp.marital_status) === null || _emp_marital_status === void 0 ? void 0 : _emp_marital_status.toLowerCase()) === \"married\";\n                }).length / (employees.length || 1) * 100),\n                insight: \"Based on \".concat(employees.length || 0, \" employees with \").concat((((_statistics_demographics1 = statistics.demographics) === null || _statistics_demographics1 === void 0 ? void 0 : _statistics_demographics1.average_age) || 35).toFixed(1), \" average age\")\n            },\n            employeeProfiles: employees.map((emp)=>this.transformEmployeeData(emp)),\n            // Generate mock upsell opportunities based on company data\n            upsellOpportunities: [\n                {\n                    category: \"Enhanced Coverage\",\n                    description: \"Upgrade \".concat(Math.round(employees.length * 0.3), \" employees to premium plans\"),\n                    savings: \"+$\".concat(Math.round(potentialSavings * 0.1).toLocaleString(), \"/month\"),\n                    confidence: \"85%\",\n                    priority: \"High\"\n                },\n                {\n                    category: \"Wellness Programs\",\n                    description: \"Preventive care initiatives for healthier workforce\",\n                    savings: \"+$\".concat(Math.round(potentialSavings * 0.05).toLocaleString(), \"/month\"),\n                    confidence: \"72%\",\n                    priority: \"Medium\"\n                }\n            ],\n            // Store original API data for reference\n            apiData: apiResponse.data\n        };\n    }\n}\n/* harmony default export */ __webpack_exports__[\"default\"] = (CensusApiService);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/census/services/censusApi.ts\n"));

/***/ })

});