"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("_app-pages-browser_src_app_census_components_EmpCensusApp_tsx",{

/***/ "(app-pages-browser)/./src/app/census/services/censusApi.ts":
/*!**********************************************!*\
  !*** ./src/app/census/services/censusApi.ts ***!
  \**********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! axios */ \"(app-pages-browser)/./node_modules/axios/lib/axios.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n// Census API uses the Python backend (chatbot service) instead of the main Node.js backend\nconst CENSUS_API_BASE_URL = \"http://127.0.0.1:8000\" || 0;\nclass CensusApiService {\n    /**\n   * Preprocess CSV file to standardize format before sending to backend\n   */ static async preprocessCsvFile(file) {\n        try {\n            const text = await file.text();\n            const lines = text.split(\"\\n\");\n            if (lines.length === 0) {\n                throw new Error(\"Empty CSV file\");\n            }\n            // Get header row and standardize column names\n            const headers = lines[0].split(\",\").map((header)=>header.trim().replace(/\"/g, \"\") // Remove quotes\n                .toLowerCase().replace(/\\s+/g, \"_\") // Replace spaces with underscores\n                .replace(/[^a-z0-9_]/g, \"\") // Remove special characters except underscores\n            );\n            console.log(\"\\uD83D\\uDCCB Original headers:\", lines[0].split(\",\"));\n            console.log(\"\\uD83D\\uDCCB Standardized headers:\", headers);\n            // Create standardized CSV content\n            const standardizedLines = [\n                headers.join(\",\")\n            ];\n            // Process data rows\n            for(let i = 1; i < lines.length; i++){\n                if (lines[i].trim()) {\n                    // Split CSV line properly (handling quoted values)\n                    const values = lines[i].split(\",\").map((value)=>value.trim().replace(/\"/g, \"\"));\n                    // Standardize values\n                    const standardizedValues = values.map((value, index)=>{\n                        const header = headers[index];\n                        // Standardize common values\n                        if (value === \"N/A\" || value === \"\" || value === \" \") {\n                            return \"\";\n                        }\n                        // Standardize gender\n                        if (header === \"sex\" || header === \"gender\") {\n                            return value.toUpperCase() === \"M\" ? \"Male\" : value.toUpperCase() === \"F\" ? \"Female\" : value;\n                        }\n                        // Standardize marital status\n                        if (header === \"marital_status\") {\n                            return value ? \"Married\" : \"Single\";\n                        }\n                        return value;\n                    });\n                    standardizedLines.push(standardizedValues.join(\",\"));\n                }\n            }\n            // Create new file with standardized content and correct MIME type\n            const standardizedContent = standardizedLines.join(\"\\n\");\n            const standardizedFile = new File([\n                standardizedContent\n            ], file.name, {\n                type: \"text/csv\",\n                lastModified: file.lastModified || Date.now()\n            });\n            console.log(\"✅ CSV preprocessing completed\");\n            console.log(\"\\uD83D\\uDCCA Original size:\", file.size, \"bytes\");\n            console.log(\"\\uD83D\\uDCCA Processed size:\", standardizedFile.size, \"bytes\");\n            return standardizedFile;\n        } catch (error) {\n            console.error(\"❌ CSV preprocessing failed:\", error);\n            // Return original file if preprocessing fails\n            return file;\n        }\n    }\n    /**\n   * Upload and process census file using the Python backend (chatbot service)\n   */ static async uploadCensusFile(file) {\n        let returnDataframe = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;\n        try {\n            var _response_data, _response_data1, _response_data2, _response_data_data, _response_data3, _response_data_data1, _response_data4;\n            // Preprocess the CSV file to standardize format\n            console.log(\"\\uD83D\\uDD04 Preprocessing CSV file: \".concat(file.name));\n            const processedFile = await this.preprocessCsvFile(file);\n            // Create FormData with proper file type\n            const formData = new FormData();\n            // For CSV files, ensure correct MIME type in FormData\n            if (processedFile.name.toLowerCase().endsWith(\".csv\")) {\n                console.log(\"\\uD83D\\uDD27 Ensuring CSV file has correct MIME type\");\n                // Create a new File with correct MIME type for CSV\n                const csvFile = new File([\n                    processedFile\n                ], processedFile.name, {\n                    type: \"text/csv\",\n                    lastModified: processedFile.lastModified || Date.now()\n                });\n                formData.append(\"file\", csvFile);\n                console.log(\"\\uD83D\\uDCCB CSV file details:\", {\n                    name: csvFile.name,\n                    size: csvFile.size,\n                    type: csvFile.type,\n                    lastModified: csvFile.lastModified\n                });\n            } else {\n                // For non-CSV files, use as-is\n                formData.append(\"file\", processedFile);\n                console.log(\"\\uD83D\\uDCCB File details:\", {\n                    name: processedFile.name,\n                    size: processedFile.size,\n                    type: processedFile.type,\n                    lastModified: processedFile.lastModified\n                });\n            }\n            // Log FormData details\n            console.log(\"\\uD83D\\uDCCB FormData details:\", {\n                hasFile: formData.has(\"file\"),\n                fileEntry: formData.get(\"file\")\n            });\n            // Build full URL for census API (Python backend)\n            const url = \"\".concat(CENSUS_API_BASE_URL, \"/api/census/processor/v1?return_dataframe=\").concat(returnDataframe);\n            console.log(\"\\uD83D\\uDCE4 Uploading census file: \".concat(processedFile.name, \" (\").concat((processedFile.size / 1024 / 1024).toFixed(2), \" MB)\"));\n            console.log(\"\\uD83D\\uDD17 Census API URL: \".concat(url));\n            console.log(\"\\uD83D\\uDCCB Request details:\", {\n                method: \"POST\",\n                url: url,\n                fileSize: processedFile.size,\n                fileName: processedFile.name,\n                returnDataframe: returnDataframe\n            });\n            // Use axios directly for census API calls to Python backend\n            // Note: Don't set Content-Type manually - let axios set it automatically for FormData\n            const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(url, formData, {\n                timeout: 300000\n            });\n            console.log(\"\\uD83D\\uDCCA Response received:\", {\n                status: response.status,\n                statusText: response.statusText,\n                success: (_response_data = response.data) === null || _response_data === void 0 ? void 0 : _response_data.success,\n                hasData: !!((_response_data1 = response.data) === null || _response_data1 === void 0 ? void 0 : _response_data1.data),\n                dataKeys: ((_response_data2 = response.data) === null || _response_data2 === void 0 ? void 0 : _response_data2.data) ? Object.keys(response.data.data) : \"no data\",\n                hasSummary: !!((_response_data3 = response.data) === null || _response_data3 === void 0 ? void 0 : (_response_data_data = _response_data3.data) === null || _response_data_data === void 0 ? void 0 : _response_data_data.summary),\n                summaryKeys: ((_response_data4 = response.data) === null || _response_data4 === void 0 ? void 0 : (_response_data_data1 = _response_data4.data) === null || _response_data_data1 === void 0 ? void 0 : _response_data_data1.summary) ? Object.keys(response.data.data.summary) : \"no summary\"\n            });\n            if (response.status === 200) {\n                if (response.data.success) {\n                    var _response_data_data_summary, _response_data_data2, _response_data_data3;\n                    // Check if the inner data also indicates success\n                    if (response.data.data && response.data.data.success === false) {\n                        // Inner processing failed\n                        console.error(\"❌ Census processing failed:\", response.data.data);\n                        console.error(\"\\uD83D\\uDCCB Error details:\", {\n                            error: response.data.data.error,\n                            message: response.data.data.message,\n                            status_code: response.data.data.status_code,\n                            fullErrorData: response.data.data\n                        });\n                        const errorMessage = response.data.data.message || \"Processing failed: \".concat(response.data.data.error) || 0;\n                        throw new Error(errorMessage);\n                    }\n                    // Log the actual response structure for debugging\n                    console.log(\"✅ Census processing completed successfully\");\n                    console.log(\"\\uD83D\\uDCCA Full response structure:\", response.data);\n                    // Try to extract employee count from various possible locations\n                    const employeeCount = ((_response_data_data2 = response.data.data) === null || _response_data_data2 === void 0 ? void 0 : (_response_data_data_summary = _response_data_data2.summary) === null || _response_data_data_summary === void 0 ? void 0 : _response_data_data_summary.total_employees) || ((_response_data_data3 = response.data.data) === null || _response_data_data3 === void 0 ? void 0 : _response_data_data3.total_employees) || response.data.total_employees || \"unknown\";\n                    console.log(\"\\uD83D\\uDC65 Processed employees: \".concat(employeeCount));\n                    return response.data;\n                } else {\n                    // Outer response indicates failure\n                    console.error(\"❌ Backend processing failed:\", response.data);\n                    throw new Error(response.data.message || \"Census processing failed on backend\");\n                }\n            } else {\n                throw new Error(\"HTTP \".concat(response.status, \": \").concat(response.statusText));\n            }\n        } catch (error) {\n            var _error_response, _error_response1, _error_response2, _error_response3, _error_response4, _error_response5, _error_response_data, _error_response6, _error_message;\n            console.error(\"❌ Census upload failed:\", error);\n            console.error(\"\\uD83D\\uDCCB Error details:\", {\n                message: error.message,\n                code: error.code,\n                status: (_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status,\n                statusText: (_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : _error_response1.statusText,\n                responseData: (_error_response2 = error.response) === null || _error_response2 === void 0 ? void 0 : _error_response2.data\n            });\n            // Provide more specific error messages\n            if (error.code === \"ECONNREFUSED\") {\n                throw new Error(\"Census API service is not running. Please start the Python backend on port 8000.\");\n            } else if (((_error_response3 = error.response) === null || _error_response3 === void 0 ? void 0 : _error_response3.status) === 404) {\n                throw new Error(\"Census API endpoint not found. Please check if the Python backend is running.\");\n            } else if (((_error_response4 = error.response) === null || _error_response4 === void 0 ? void 0 : _error_response4.status) === 413) {\n                throw new Error(\"File too large. Maximum file size is 50MB.\");\n            } else if (((_error_response5 = error.response) === null || _error_response5 === void 0 ? void 0 : _error_response5.status) === 500) {\n                var _error_response_data1, _error_response7;\n                const serverError = ((_error_response7 = error.response) === null || _error_response7 === void 0 ? void 0 : (_error_response_data1 = _error_response7.data) === null || _error_response_data1 === void 0 ? void 0 : _error_response_data1.message) || \"Internal server error during census processing\";\n                throw new Error(\"Server error: \".concat(serverError));\n            } else if ((_error_response6 = error.response) === null || _error_response6 === void 0 ? void 0 : (_error_response_data = _error_response6.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) {\n                throw new Error(error.response.data.message);\n            } else if ((_error_message = error.message) === null || _error_message === void 0 ? void 0 : _error_message.includes(\"undefined\")) {\n                var _error_response8;\n                // Handle response structure mismatch\n                console.log(\"\\uD83D\\uDD0D Response structure debugging:\", (_error_response8 = error.response) === null || _error_response8 === void 0 ? void 0 : _error_response8.data);\n                throw new Error(\"Response structure mismatch - check console for details\");\n            } else {\n                throw new Error(error.message || \"Failed to upload census file\");\n            }\n        }\n    }\n    /**\n   * Get processed census data by company/report ID\n   * Note: This would be implemented when backend supports data persistence\n   */ static async getCensusData(companyId) {\n        try {\n            // For now, this would use the Python backend when persistence is implemented\n            const url = \"\".concat(CENSUS_API_BASE_URL, \"/api/census/reports/\").concat(companyId);\n            const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(url);\n            return response.data;\n        } catch (error) {\n            console.error(\"❌ Failed to fetch census data:\", error);\n            throw new Error(error.message || \"Failed to fetch census data\");\n        }\n    }\n    /**\n   * Get broker dashboard data (list of processed companies)\n   * Note: This would be implemented when backend supports data persistence\n   */ static async getBrokerDashboard() {\n        try {\n            // For now, this would use the Python backend when persistence is implemented\n            const url = \"\".concat(CENSUS_API_BASE_URL, \"/api/census/broker/dashboard\");\n            const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(url);\n            return response.data || [];\n        } catch (error) {\n            console.error(\"❌ Failed to fetch broker dashboard:\", error);\n            // Return empty array as fallback - frontend state management handles this\n            return [];\n        }\n    }\n    /**\n   * Transform API employee data to frontend format\n   */ static transformEmployeeData(apiEmployee) {\n        var _apiEmployee_recommended_plan, _apiEmployee_recommended_plan1;\n        // Parse top 3 plans if available\n        let top3Plans = [];\n        try {\n            if (apiEmployee.top_3_available_plans) {\n                top3Plans = JSON.parse(apiEmployee.top_3_available_plans);\n            }\n        } catch (e) {\n            console.warn(\"Failed to parse top_3_available_plans:\", e);\n        }\n        // Map risk level based on plan confidence\n        const getRiskLevel = (confidence)=>{\n            if (confidence >= 0.8) return \"Low\";\n            if (confidence >= 0.6) return \"Medium\";\n            return \"High\";\n        };\n        return {\n            name: apiEmployee.name,\n            department: \"Dept \".concat(apiEmployee.dept_count),\n            risk: getRiskLevel(apiEmployee.plan_confidence),\n            age: apiEmployee.age,\n            coverage: apiEmployee.predicted_plan_type,\n            hasDependents: apiEmployee.marital_status.toLowerCase() === \"married\",\n            salary: apiEmployee.income_tier,\n            currentPlan: {\n                medical: ((_apiEmployee_recommended_plan = apiEmployee.recommended_plan) === null || _apiEmployee_recommended_plan === void 0 ? void 0 : _apiEmployee_recommended_plan.name) || \"Not Enrolled\",\n                dental: apiEmployee.predicted_benefits.includes(\"Dental\") ? \"Basic\" : \"Not Enrolled\",\n                vision: apiEmployee.predicted_benefits.includes(\"Vision\") ? \"Basic\" : \"Not Enrolled\",\n                life: apiEmployee.predicted_benefits.includes(\"Term Life\") ? \"1x Salary\" : \"None\",\n                disability: apiEmployee.predicted_benefits.includes(\"LTD\") ? \"Basic\" : \"None\"\n            },\n            coverageGaps: [],\n            insights: [\n                apiEmployee.plan_reason\n            ],\n            upsells: [],\n            planFitSummary: {\n                recommendedPlan: ((_apiEmployee_recommended_plan1 = apiEmployee.recommended_plan) === null || _apiEmployee_recommended_plan1 === void 0 ? void 0 : _apiEmployee_recommended_plan1.name) || \"No recommendation\",\n                insight: apiEmployee.plan_reason\n            },\n            // Additional API data\n            apiData: {\n                employee_id: apiEmployee.employee_id,\n                zipcode: apiEmployee.zipcode,\n                city: apiEmployee.city,\n                state: apiEmployee.state,\n                recommended_plan: apiEmployee.recommended_plan,\n                benefits_coverage: apiEmployee.benefits_coverage,\n                top_3_plans: top3Plans,\n                marketplace_plans_available: apiEmployee.marketplace_plans_available,\n                plan_count: apiEmployee.plan_count\n            }\n        };\n    }\n    /**\n   * Transform API response to frontend company data format\n   */ static transformCompanyData(apiResponse) {\n        let companyId = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : \"1\";\n        var _statistics_health_plans, _statistics_demographics, _statistics_demographics1;\n        console.log(\"\\uD83D\\uDD04 Raw API response for transformation:\", apiResponse);\n        // Handle flexible response structure - try multiple possible structures\n        let data, summary, statistics, employees;\n        if (apiResponse.data) {\n            data = apiResponse.data;\n            summary = data.summary || {};\n            statistics = data.statistics || {};\n            employees = data.employees || [];\n        } else {\n            // Direct response without .data wrapper\n            data = apiResponse;\n            summary = data.summary || {};\n            statistics = data.statistics || {};\n            employees = data.employees || [];\n        }\n        console.log(\"\\uD83D\\uDD04 Transforming company data:\", {\n            hasData: !!data,\n            hasSummary: !!summary,\n            hasStatistics: !!statistics,\n            employeeCount: employees.length,\n            summaryKeys: Object.keys(summary),\n            statisticsKeys: Object.keys(statistics),\n            dataKeys: Object.keys(data),\n            fullData: data // Log the full data to see what's actually there\n        });\n        // If we don't have the expected structure, create a minimal response\n        if (!summary.total_employees && !employees.length) {\n            console.warn(\"⚠️ No employee data found, creating minimal response\");\n            return {\n                companyName: \"Company \".concat(companyId),\n                employees: 0,\n                averageAge: 35,\n                dependents: 0,\n                planType: \"Unknown\",\n                potentialSavings: \"$0\",\n                riskScore: \"0.0/10\",\n                uploadDate: new Date().toISOString().split(\"T\")[0],\n                industry: \"Unknown\",\n                currentSpend: \"$0/month\",\n                suggestedPlan: \"No data available\",\n                planFitSummary: {\n                    silverGoldPPO: 0,\n                    hdhp: 0,\n                    familyPPO: 0,\n                    insight: \"No employee data available\"\n                },\n                employeeProfiles: [],\n                upsellOpportunities: [],\n                apiData: apiResponse\n            };\n        }\n        // Calculate potential savings (simplified calculation)\n        const avgPremium = employees.filter((emp)=>emp.recommended_plan).reduce((sum, emp)=>{\n            var _emp_recommended_plan;\n            return sum + (((_emp_recommended_plan = emp.recommended_plan) === null || _emp_recommended_plan === void 0 ? void 0 : _emp_recommended_plan.premium) || 0);\n        }, 0) / (employees.length || 1);\n        const potentialSavings = Math.round(avgPremium * employees.length * 0.15); // Assume 15% savings\n        // Determine primary plan type with null safety\n        const planTypeDistribution = ((_statistics_health_plans = statistics.health_plans) === null || _statistics_health_plans === void 0 ? void 0 : _statistics_health_plans.plan_type_distribution) || {};\n        const planTypes = Object.keys(planTypeDistribution);\n        const primaryPlanType = planTypes.length > 0 ? planTypes.reduce((a, b)=>planTypeDistribution[a] > planTypeDistribution[b] ? a : b) : \"PPO\"; // Default fallback\n        return {\n            companyName: \"Company \".concat(companyId),\n            employees: summary.total_employees || employees.length || 0,\n            averageAge: Math.round(((_statistics_demographics = statistics.demographics) === null || _statistics_demographics === void 0 ? void 0 : _statistics_demographics.average_age) || 35),\n            dependents: employees.filter((emp)=>{\n                var _emp_marital_status;\n                return ((_emp_marital_status = emp.marital_status) === null || _emp_marital_status === void 0 ? void 0 : _emp_marital_status.toLowerCase()) === \"married\";\n            }).length / (employees.length || 1),\n            planType: primaryPlanType,\n            potentialSavings: \"$\".concat(potentialSavings.toLocaleString()),\n            riskScore: \"\".concat(((summary.data_quality_score || 0.8) * 10).toFixed(1), \"/10\"),\n            uploadDate: new Date().toISOString().split(\"T\")[0],\n            industry: \"Technology\",\n            currentSpend: \"$\".concat(Math.round(avgPremium * employees.length).toLocaleString(), \"/month\"),\n            suggestedPlan: \"\".concat(primaryPlanType, \" with Enhanced Coverage\"),\n            planFitSummary: {\n                silverGoldPPO: Math.round((planTypeDistribution[\"PPO\"] || 0) / (employees.length || 1) * 100),\n                hdhp: Math.round((planTypeDistribution[\"HDHP\"] || 0) / (employees.length || 1) * 100),\n                familyPPO: Math.round(employees.filter((emp)=>{\n                    var _emp_marital_status;\n                    return ((_emp_marital_status = emp.marital_status) === null || _emp_marital_status === void 0 ? void 0 : _emp_marital_status.toLowerCase()) === \"married\";\n                }).length / (employees.length || 1) * 100),\n                insight: \"Based on \".concat(employees.length || 0, \" employees with \").concat((((_statistics_demographics1 = statistics.demographics) === null || _statistics_demographics1 === void 0 ? void 0 : _statistics_demographics1.average_age) || 35).toFixed(1), \" average age\")\n            },\n            employeeProfiles: employees.map((emp)=>this.transformEmployeeData(emp)),\n            // Generate mock upsell opportunities based on company data\n            upsellOpportunities: [\n                {\n                    category: \"Enhanced Coverage\",\n                    description: \"Upgrade \".concat(Math.round(employees.length * 0.3), \" employees to premium plans\"),\n                    savings: \"+$\".concat(Math.round(potentialSavings * 0.1).toLocaleString(), \"/month\"),\n                    confidence: \"85%\",\n                    priority: \"High\"\n                },\n                {\n                    category: \"Wellness Programs\",\n                    description: \"Preventive care initiatives for healthier workforce\",\n                    savings: \"+$\".concat(Math.round(potentialSavings * 0.05).toLocaleString(), \"/month\"),\n                    confidence: \"72%\",\n                    priority: \"Medium\"\n                }\n            ],\n            // Store original API data for reference\n            apiData: apiResponse.data\n        };\n    }\n}\n/* harmony default export */ __webpack_exports__[\"default\"] = (CensusApiService);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/census/services/censusApi.ts\n"));

/***/ })

});