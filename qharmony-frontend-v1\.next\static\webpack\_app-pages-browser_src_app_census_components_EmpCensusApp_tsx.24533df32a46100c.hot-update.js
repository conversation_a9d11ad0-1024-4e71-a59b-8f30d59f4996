"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("_app-pages-browser_src_app_census_components_EmpCensusApp_tsx",{

/***/ "(app-pages-browser)/./src/app/census/context/CensusContext.tsx":
/*!**************************************************!*\
  !*** ./src/app/census/context/CensusContext.tsx ***!
  \**************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CensusProvider: function() { return /* binding */ CensusProvider; },\n/* harmony export */   useCensus: function() { return /* binding */ useCensus; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _services_censusApi__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../services/censusApi */ \"(app-pages-browser)/./src/app/census/services/censusApi.ts\");\n/* __next_internal_client_entry_do_not_use__ useCensus,CensusProvider,default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n/**\n * Census Context - Frontend State Management\n *\n * NOTE: This is temporary frontend state management.\n * In the future, this will be replaced with backend state management\n * where processed census data will be stored in the database and\n * retrieved via API calls instead of local state.\n *\n * Future Migration Plan:\n * 1. Backend will store processed census results in database\n * 2. API endpoints will provide company listing and individual company data\n * 3. This context will be simplified to just handle loading states\n * 4. Data persistence will be handled by the backend\n */ \n\nconst CensusContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst useCensus = ()=>{\n    _s();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(CensusContext);\n    if (context === undefined) {\n        throw new Error(\"useCensus must be used within a CensusProvider\");\n    }\n    return context;\n};\n_s(useCensus, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nconst CensusProvider = (param)=>{\n    let { children } = param;\n    _s1();\n    const [companies, setCompanies] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [currentCompany, setCurrentCompany] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const clearError = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        setError(null);\n    }, []);\n    const uploadCensusFile = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async (file, companyName)=>{\n        setIsLoading(true);\n        setError(null);\n        try {\n            console.log(\"\\uD83D\\uDCE4 Starting census file upload...\");\n            // Upload and process the file\n            const response = await _services_censusApi__WEBPACK_IMPORTED_MODULE_2__[\"default\"].uploadCensusFile(file, false);\n            if (!response.success) {\n                throw new Error(response.message || \"Census processing failed\");\n            }\n            // Generate a unique company ID (in real app, this would come from backend)\n            const companyId = \"company_\".concat(Date.now());\n            // Transform API response to frontend format\n            const transformedCompany = _services_censusApi__WEBPACK_IMPORTED_MODULE_2__[\"default\"].transformCompanyData(response, companyId);\n            // Create company record\n            const newCompany = {\n                id: companyId,\n                ...transformedCompany,\n                status: \"analyzed\"\n            };\n            // Add to companies list\n            setCompanies((prev)=>[\n                    newCompany,\n                    ...prev\n                ]);\n            setCurrentCompany(newCompany);\n            console.log(\"✅ Census file processed successfully:\", newCompany);\n            return companyId;\n        } catch (err) {\n            let errorMessage = err.message || \"Failed to process census file\";\n            // Add helpful instructions for common errors\n            if (errorMessage.includes(\"Census API service is not running\")) {\n                errorMessage += \"\\n\\nTo start the Python backend:\\n1. Navigate to qharmony-bot directory\\n2. Run: python server.py\\n3. Ensure it starts on port 8000\";\n            }\n            setError(errorMessage);\n            console.error(\"❌ Census upload failed:\", err);\n            throw new Error(errorMessage);\n        } finally{\n            setIsLoading(false);\n        }\n    }, []);\n    const getCompanyData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((companyId)=>{\n        return companies.find((company)=>company.id === companyId) || null;\n    }, [\n        companies\n    ]);\n    const refreshDashboard = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async ()=>{\n        setIsLoading(true);\n        setError(null);\n        try {\n            // In a real implementation, this would fetch from backend\n            // For now, we'll keep the existing companies\n            console.log(\"\\uD83D\\uDCCA Dashboard refreshed\");\n        } catch (err) {\n            setError(err.message || \"Failed to refresh dashboard\");\n            console.error(\"❌ Dashboard refresh failed:\", err);\n        } finally{\n            setIsLoading(false);\n        }\n    }, []);\n    const value = {\n        // State\n        companies,\n        currentCompany,\n        isLoading,\n        error,\n        // Actions\n        uploadCensusFile,\n        getCompanyData,\n        refreshDashboard,\n        clearError,\n        setCurrentCompany\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CensusContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\context\\\\CensusContext.tsx\",\n        lineNumber: 190,\n        columnNumber: 5\n    }, undefined);\n};\n_s1(CensusProvider, \"KWXup2ZkG3cRgQXUJF+Ea2NRs/0=\");\n_c = CensusProvider;\n/* harmony default export */ __webpack_exports__[\"default\"] = (CensusProvider);\nvar _c;\n$RefreshReg$(_c, \"CensusProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/census/context/CensusContext.tsx\n"));

/***/ })

});