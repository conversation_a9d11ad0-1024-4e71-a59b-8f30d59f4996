"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("_app-pages-browser_src_app_census_components_EmpCensusApp_tsx",{

/***/ "(app-pages-browser)/./src/app/census/services/censusApi.ts":
/*!**********************************************!*\
  !*** ./src/app/census/services/censusApi.ts ***!
  \**********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! axios */ \"(app-pages-browser)/./node_modules/axios/lib/axios.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n// Census API uses the Python backend (chatbot service) instead of the main Node.js backend\nconst CENSUS_API_BASE_URL = \"http://127.0.0.1:8000\" || 0;\nclass CensusApiService {\n    /**\n   * Upload and process census file using the Python backend (chatbot service)\n   */ static async uploadCensusFile(file) {\n        let returnDataframe = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;\n        try {\n            var _response_data, _response_data1, _response_data_data_summary, _response_data_data, _response_data2;\n            const formData = new FormData();\n            formData.append(\"file\", file);\n            // Build full URL for census API (Python backend)\n            const url = \"\".concat(CENSUS_API_BASE_URL, \"/api/census/processor/v1?return_dataframe=\").concat(returnDataframe);\n            console.log(\"\\uD83D\\uDCE4 Uploading census file: \".concat(file.name, \" (\").concat((file.size / 1024 / 1024).toFixed(2), \" MB)\"));\n            console.log(\"\\uD83D\\uDD17 Census API URL: \".concat(url));\n            console.log(\"\\uD83D\\uDCCB Request details:\", {\n                method: \"POST\",\n                url: url,\n                fileSize: file.size,\n                fileName: file.name,\n                returnDataframe: returnDataframe\n            });\n            // Use axios directly for census API calls to Python backend\n            // Note: Don't set Content-Type manually - let axios set it automatically for FormData\n            const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(url, formData, {\n                timeout: 300000\n            });\n            console.log(\"\\uD83D\\uDCCA Response received:\", {\n                status: response.status,\n                statusText: response.statusText,\n                success: (_response_data = response.data) === null || _response_data === void 0 ? void 0 : _response_data.success,\n                hasData: !!((_response_data1 = response.data) === null || _response_data1 === void 0 ? void 0 : _response_data1.data),\n                totalEmployees: (_response_data2 = response.data) === null || _response_data2 === void 0 ? void 0 : (_response_data_data = _response_data2.data) === null || _response_data_data === void 0 ? void 0 : (_response_data_data_summary = _response_data_data.summary) === null || _response_data_data_summary === void 0 ? void 0 : _response_data_data_summary.total_employees\n            });\n            if (response.status === 200 && response.data.success) {\n                var _response_data_data_summary1, _response_data_data1, _response_data_data2;\n                // Log the actual response structure for debugging\n                console.log(\"✅ Census processing completed successfully\");\n                console.log(\"\\uD83D\\uDCCA Full response structure:\", response.data);\n                // Try to extract employee count from various possible locations\n                const employeeCount = ((_response_data_data1 = response.data.data) === null || _response_data_data1 === void 0 ? void 0 : (_response_data_data_summary1 = _response_data_data1.summary) === null || _response_data_data_summary1 === void 0 ? void 0 : _response_data_data_summary1.total_employees) || ((_response_data_data2 = response.data.data) === null || _response_data_data2 === void 0 ? void 0 : _response_data_data2.total_employees) || response.data.total_employees || \"unknown\";\n                console.log(\"\\uD83D\\uDC65 Processed employees: \".concat(employeeCount));\n                return response.data;\n            } else {\n                throw new Error(response.data.message || \"Census processing failed\");\n            }\n        } catch (error) {\n            var _error_response, _error_response1, _error_response2, _error_response3, _error_response4, _error_response5, _error_response_data, _error_response6, _error_message;\n            console.error(\"❌ Census upload failed:\", error);\n            console.error(\"\\uD83D\\uDCCB Error details:\", {\n                message: error.message,\n                code: error.code,\n                status: (_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status,\n                statusText: (_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : _error_response1.statusText,\n                responseData: (_error_response2 = error.response) === null || _error_response2 === void 0 ? void 0 : _error_response2.data\n            });\n            // Provide more specific error messages\n            if (error.code === \"ECONNREFUSED\") {\n                throw new Error(\"Census API service is not running. Please start the Python backend on port 8000.\");\n            } else if (((_error_response3 = error.response) === null || _error_response3 === void 0 ? void 0 : _error_response3.status) === 404) {\n                throw new Error(\"Census API endpoint not found. Please check if the Python backend is running.\");\n            } else if (((_error_response4 = error.response) === null || _error_response4 === void 0 ? void 0 : _error_response4.status) === 413) {\n                throw new Error(\"File too large. Maximum file size is 50MB.\");\n            } else if (((_error_response5 = error.response) === null || _error_response5 === void 0 ? void 0 : _error_response5.status) === 500) {\n                var _error_response_data1, _error_response7;\n                const serverError = ((_error_response7 = error.response) === null || _error_response7 === void 0 ? void 0 : (_error_response_data1 = _error_response7.data) === null || _error_response_data1 === void 0 ? void 0 : _error_response_data1.message) || \"Internal server error during census processing\";\n                throw new Error(\"Server error: \".concat(serverError));\n            } else if ((_error_response6 = error.response) === null || _error_response6 === void 0 ? void 0 : (_error_response_data = _error_response6.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) {\n                throw new Error(error.response.data.message);\n            } else if ((_error_message = error.message) === null || _error_message === void 0 ? void 0 : _error_message.includes(\"undefined\")) {\n                var _error_response8;\n                // Handle response structure mismatch\n                console.log(\"\\uD83D\\uDD0D Response structure debugging:\", (_error_response8 = error.response) === null || _error_response8 === void 0 ? void 0 : _error_response8.data);\n                throw new Error(\"Response structure mismatch - check console for details\");\n            } else {\n                throw new Error(error.message || \"Failed to upload census file\");\n            }\n        }\n    }\n    /**\n   * Get processed census data by company/report ID\n   * Note: This would be implemented when backend supports data persistence\n   */ static async getCensusData(companyId) {\n        try {\n            // For now, this would use the Python backend when persistence is implemented\n            const url = \"\".concat(CENSUS_API_BASE_URL, \"/api/census/reports/\").concat(companyId);\n            const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(url);\n            return response.data;\n        } catch (error) {\n            console.error(\"❌ Failed to fetch census data:\", error);\n            throw new Error(error.message || \"Failed to fetch census data\");\n        }\n    }\n    /**\n   * Get broker dashboard data (list of processed companies)\n   * Note: This would be implemented when backend supports data persistence\n   */ static async getBrokerDashboard() {\n        try {\n            // For now, this would use the Python backend when persistence is implemented\n            const url = \"\".concat(CENSUS_API_BASE_URL, \"/api/census/broker/dashboard\");\n            const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(url);\n            return response.data || [];\n        } catch (error) {\n            console.error(\"❌ Failed to fetch broker dashboard:\", error);\n            // Return empty array as fallback - frontend state management handles this\n            return [];\n        }\n    }\n    /**\n   * Transform API employee data to frontend format\n   */ static transformEmployeeData(apiEmployee) {\n        var _apiEmployee_recommended_plan, _apiEmployee_recommended_plan1;\n        // Parse top 3 plans if available\n        let top3Plans = [];\n        try {\n            if (apiEmployee.top_3_available_plans) {\n                top3Plans = JSON.parse(apiEmployee.top_3_available_plans);\n            }\n        } catch (e) {\n            console.warn(\"Failed to parse top_3_available_plans:\", e);\n        }\n        // Map risk level based on plan confidence\n        const getRiskLevel = (confidence)=>{\n            if (confidence >= 0.8) return \"Low\";\n            if (confidence >= 0.6) return \"Medium\";\n            return \"High\";\n        };\n        return {\n            name: apiEmployee.name,\n            department: \"Dept \".concat(apiEmployee.dept_count),\n            risk: getRiskLevel(apiEmployee.plan_confidence),\n            age: apiEmployee.age,\n            coverage: apiEmployee.predicted_plan_type,\n            hasDependents: apiEmployee.marital_status.toLowerCase() === \"married\",\n            salary: apiEmployee.income_tier,\n            currentPlan: {\n                medical: ((_apiEmployee_recommended_plan = apiEmployee.recommended_plan) === null || _apiEmployee_recommended_plan === void 0 ? void 0 : _apiEmployee_recommended_plan.name) || \"Not Enrolled\",\n                dental: apiEmployee.predicted_benefits.includes(\"Dental\") ? \"Basic\" : \"Not Enrolled\",\n                vision: apiEmployee.predicted_benefits.includes(\"Vision\") ? \"Basic\" : \"Not Enrolled\",\n                life: apiEmployee.predicted_benefits.includes(\"Term Life\") ? \"1x Salary\" : \"None\",\n                disability: apiEmployee.predicted_benefits.includes(\"LTD\") ? \"Basic\" : \"None\"\n            },\n            coverageGaps: [],\n            insights: [\n                apiEmployee.plan_reason\n            ],\n            upsells: [],\n            planFitSummary: {\n                recommendedPlan: ((_apiEmployee_recommended_plan1 = apiEmployee.recommended_plan) === null || _apiEmployee_recommended_plan1 === void 0 ? void 0 : _apiEmployee_recommended_plan1.name) || \"No recommendation\",\n                insight: apiEmployee.plan_reason\n            },\n            // Additional API data\n            apiData: {\n                employee_id: apiEmployee.employee_id,\n                zipcode: apiEmployee.zipcode,\n                city: apiEmployee.city,\n                state: apiEmployee.state,\n                recommended_plan: apiEmployee.recommended_plan,\n                benefits_coverage: apiEmployee.benefits_coverage,\n                top_3_plans: top3Plans,\n                marketplace_plans_available: apiEmployee.marketplace_plans_available,\n                plan_count: apiEmployee.plan_count\n            }\n        };\n    }\n    /**\n   * Transform API response to frontend company data format\n   */ static transformCompanyData(apiResponse) {\n        let companyId = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : \"1\";\n        // Handle flexible response structure\n        const data = apiResponse.data || apiResponse;\n        const summary = data.summary || {};\n        const statistics = data.statistics || {};\n        const employees = data.employees || [];\n        console.log(\"\\uD83D\\uDD04 Transforming company data:\", {\n            hasData: !!data,\n            hasSummary: !!summary,\n            hasStatistics: !!statistics,\n            employeeCount: employees.length,\n            summaryKeys: Object.keys(summary),\n            statisticsKeys: Object.keys(statistics)\n        });\n        // Calculate potential savings (simplified calculation)\n        const avgPremium = employees.filter((emp)=>emp.recommended_plan).reduce((sum, emp)=>{\n            var _emp_recommended_plan;\n            return sum + (((_emp_recommended_plan = emp.recommended_plan) === null || _emp_recommended_plan === void 0 ? void 0 : _emp_recommended_plan.premium) || 0);\n        }, 0) / (employees.length || 1);\n        const potentialSavings = Math.round(avgPremium * employees.length * 0.15); // Assume 15% savings\n        // Determine primary plan type\n        const planTypes = Object.keys(statistics.health_plans.plan_type_distribution);\n        const primaryPlanType = planTypes.reduce((a, b)=>statistics.health_plans.plan_type_distribution[a] > statistics.health_plans.plan_type_distribution[b] ? a : b);\n        return {\n            companyName: \"Company \".concat(companyId),\n            employees: summary.total_employees,\n            averageAge: Math.round(statistics.demographics.average_age),\n            dependents: employees.filter((emp)=>{\n                var _emp_marital_status;\n                return ((_emp_marital_status = emp.marital_status) === null || _emp_marital_status === void 0 ? void 0 : _emp_marital_status.toLowerCase()) === \"married\";\n            }).length / (employees.length || 1),\n            planType: primaryPlanType,\n            potentialSavings: \"$\".concat(potentialSavings.toLocaleString()),\n            riskScore: \"\".concat((summary.data_quality_score * 10).toFixed(1), \"/10\"),\n            uploadDate: new Date().toISOString().split(\"T\")[0],\n            industry: \"Technology\",\n            currentSpend: \"$\".concat(Math.round(avgPremium * employees.length).toLocaleString(), \"/month\"),\n            suggestedPlan: \"\".concat(primaryPlanType, \" with Enhanced Coverage\"),\n            planFitSummary: {\n                silverGoldPPO: Math.round((statistics.health_plans.plan_type_distribution[\"PPO\"] || 0) / employees.length * 100),\n                hdhp: Math.round((statistics.health_plans.plan_type_distribution[\"HDHP\"] || 0) / employees.length * 100),\n                familyPPO: Math.round(employees.filter((emp)=>emp.marital_status.toLowerCase() === \"married\").length / employees.length * 100),\n                insight: \"Based on \".concat(employees.length, \" employees with \").concat(statistics.demographics.average_age.toFixed(1), \" average age\")\n            },\n            employeeProfiles: employees.map((emp)=>this.transformEmployeeData(emp)),\n            // Generate mock upsell opportunities based on company data\n            upsellOpportunities: [\n                {\n                    category: \"Enhanced Coverage\",\n                    description: \"Upgrade \".concat(Math.round(employees.length * 0.3), \" employees to premium plans\"),\n                    savings: \"+$\".concat(Math.round(potentialSavings * 0.1).toLocaleString(), \"/month\"),\n                    confidence: \"85%\",\n                    priority: \"High\"\n                },\n                {\n                    category: \"Wellness Programs\",\n                    description: \"Preventive care initiatives for healthier workforce\",\n                    savings: \"+$\".concat(Math.round(potentialSavings * 0.05).toLocaleString(), \"/month\"),\n                    confidence: \"72%\",\n                    priority: \"Medium\"\n                }\n            ],\n            // Store original API data for reference\n            apiData: apiResponse.data\n        };\n    }\n}\n/* harmony default export */ __webpack_exports__[\"default\"] = (CensusApiService);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/census/services/censusApi.ts\n"));

/***/ })

});