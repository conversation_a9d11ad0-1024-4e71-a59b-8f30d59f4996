"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("_app-pages-browser_src_app_census_components_EmpCensusApp_tsx",{

/***/ "(app-pages-browser)/./src/app/census/context/CensusContext.tsx":
/*!**************************************************!*\
  !*** ./src/app/census/context/CensusContext.tsx ***!
  \**************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CensusProvider: function() { return /* binding */ CensusProvider; },\n/* harmony export */   useCensus: function() { return /* binding */ useCensus; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _services_censusApi__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../services/censusApi */ \"(app-pages-browser)/./src/app/census/services/censusApi.ts\");\n/* __next_internal_client_entry_do_not_use__ useCensus,CensusProvider,default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n/**\n * Census Context - Frontend State Management\n *\n * NOTE: This is temporary frontend state management.\n * In the future, this will be replaced with backend state management\n * where processed census data will be stored in the database and\n * retrieved via API calls instead of local state.\n *\n * Future Migration Plan:\n * 1. Backend will store processed census results in database\n * 2. API endpoints will provide company listing and individual company data\n * 3. This context will be simplified to just handle loading states\n * 4. Data persistence will be handled by the backend\n */ \n\nconst CensusContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst useCensus = ()=>{\n    _s();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(CensusContext);\n    if (context === undefined) {\n        throw new Error(\"useCensus must be used within a CensusProvider\");\n    }\n    return context;\n};\n_s(useCensus, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nconst CensusProvider = (param)=>{\n    let { children } = param;\n    _s1();\n    const [companies, setCompanies] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [currentCompany, setCurrentCompany] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const clearError = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        setError(null);\n    }, []);\n    const uploadCensusFile = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async (file)=>{\n        setIsLoading(true);\n        setError(null);\n        try {\n            console.log(\"\\uD83D\\uDCE4 Starting census file upload...\");\n            // Upload and process the file\n            const response = await _services_censusApi__WEBPACK_IMPORTED_MODULE_2__[\"default\"].uploadCensusFile(file, false);\n            if (!response.success) {\n                throw new Error(response.message || \"Census processing failed\");\n            }\n            // Generate a unique company ID (in real app, this would come from backend)\n            const companyId = \"company_\".concat(Date.now());\n            // Transform API response to frontend format\n            const transformedCompany = _services_censusApi__WEBPACK_IMPORTED_MODULE_2__[\"default\"].transformCompanyData(response, companyId);\n            // Create company record\n            const newCompany = {\n                id: companyId,\n                ...transformedCompany,\n                status: \"analyzed\"\n            };\n            // Add to companies list\n            setCompanies((prev)=>[\n                    newCompany,\n                    ...prev\n                ]);\n            setCurrentCompany(newCompany);\n            console.log(\"✅ Census file processed successfully:\", newCompany);\n            return companyId;\n        } catch (err) {\n            let errorMessage = err.message || \"Failed to process census file\";\n            // Add helpful instructions for common errors\n            if (errorMessage.includes(\"Census API service is not running\")) {\n                errorMessage += \"\\n\\nTo start the Python backend:\\n1. Navigate to qharmony-bot directory\\n2. Run: python server.py\\n3. Ensure it starts on port 8000\";\n            }\n            setError(errorMessage);\n            console.error(\"❌ Census upload failed:\", err);\n            throw new Error(errorMessage);\n        } finally{\n            setIsLoading(false);\n        }\n    }, []);\n    const getCompanyData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((companyId)=>{\n        return companies.find((company)=>company.id === companyId) || null;\n    }, [\n        companies\n    ]);\n    const refreshDashboard = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async ()=>{\n        setIsLoading(true);\n        setError(null);\n        try {\n            // In a real implementation, this would fetch from backend\n            // For now, we'll keep the existing companies\n            console.log(\"\\uD83D\\uDCCA Dashboard refreshed\");\n        } catch (err) {\n            setError(err.message || \"Failed to refresh dashboard\");\n            console.error(\"❌ Dashboard refresh failed:\", err);\n        } finally{\n            setIsLoading(false);\n        }\n    }, []);\n    const value = {\n        // State\n        companies,\n        currentCompany,\n        isLoading,\n        error,\n        // Actions\n        uploadCensusFile,\n        getCompanyData,\n        refreshDashboard,\n        clearError,\n        setCurrentCompany\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CensusContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\context\\\\CensusContext.tsx\",\n        lineNumber: 190,\n        columnNumber: 5\n    }, undefined);\n};\n_s1(CensusProvider, \"KWXup2ZkG3cRgQXUJF+Ea2NRs/0=\");\n_c = CensusProvider;\n/* harmony default export */ __webpack_exports__[\"default\"] = (CensusProvider);\nvar _c;\n$RefreshReg$(_c, \"CensusProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/census/context/CensusContext.tsx\n"));

/***/ })

});