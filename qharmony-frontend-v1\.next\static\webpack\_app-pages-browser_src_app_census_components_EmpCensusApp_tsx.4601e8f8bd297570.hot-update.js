"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("_app-pages-browser_src_app_census_components_EmpCensusApp_tsx",{

/***/ "(app-pages-browser)/./src/app/census/services/censusApi.ts":
/*!**********************************************!*\
  !*** ./src/app/census/services/censusApi.ts ***!
  \**********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! axios */ \"(app-pages-browser)/./node_modules/axios/lib/axios.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n// Census API uses the Python backend (chatbot service) instead of the main Node.js backend\nconst CENSUS_API_BASE_URL = \"http://127.0.0.1:8000\" || 0;\nclass CensusApiService {\n    /**\n   * Preprocess CSV file to standardize format before sending to backend\n   */ static async preprocessCsvFile(file) {\n        try {\n            const text = await file.text();\n            const lines = text.split(\"\\n\");\n            if (lines.length === 0) {\n                throw new Error(\"Empty CSV file\");\n            }\n            // Get header row and standardize column names\n            const headers = lines[0].split(\",\").map((header)=>header.trim().replace(/\"/g, \"\") // Remove quotes\n                .toLowerCase().replace(/\\s+/g, \"_\") // Replace spaces with underscores\n                .replace(/[^a-z0-9_]/g, \"\") // Remove special characters except underscores\n            );\n            console.log(\"\\uD83D\\uDCCB Original headers:\", lines[0].split(\",\"));\n            console.log(\"\\uD83D\\uDCCB Standardized headers:\", headers);\n            // Create standardized CSV content\n            const standardizedLines = [\n                headers.join(\",\")\n            ];\n            // Process data rows\n            for(let i = 1; i < lines.length; i++){\n                if (lines[i].trim()) {\n                    // Split CSV line properly (handling quoted values)\n                    const values = lines[i].split(\",\").map((value)=>value.trim().replace(/\"/g, \"\"));\n                    // Standardize values\n                    const standardizedValues = values.map((value, index)=>{\n                        const header = headers[index];\n                        // Standardize common values\n                        if (value === \"N/A\" || value === \"\" || value === \" \") {\n                            return \"\";\n                        }\n                        // Standardize gender\n                        if (header === \"sex\" || header === \"gender\") {\n                            return value.toUpperCase() === \"M\" ? \"Male\" : value.toUpperCase() === \"F\" ? \"Female\" : value;\n                        }\n                        // Standardize marital status\n                        if (header === \"marital_status\") {\n                            var _values_relationshipIndex;\n                            // Get relationship from the same row\n                            const relationshipIndex = standardizedHeaders.indexOf(\"relationship\");\n                            const relationship = relationshipIndex >= 0 ? (_values_relationshipIndex = values[relationshipIndex]) === null || _values_relationshipIndex === void 0 ? void 0 : _values_relationshipIndex.trim() : \"\";\n                            // Only employees need marital status, dependents should be empty or \"N/A\"\n                            if (relationship && relationship.toLowerCase() === \"employee\") {\n                                if (cleaned.toLowerCase().includes(\"married\")) return \"Married\";\n                                if (cleaned.toLowerCase().includes(\"single\")) return \"Single\";\n                                return cleaned || \"Single\"; // Default employees to Single if empty\n                            } else {\n                                // For dependents (Spouse/Child), set to empty or \"N/A\"\n                                return \"\";\n                            }\n                        }\n                        return value;\n                    });\n                    standardizedLines.push(standardizedValues.join(\",\"));\n                }\n            }\n            // Create new file with standardized content and correct MIME type\n            const standardizedContent = standardizedLines.join(\"\\n\");\n            const standardizedFile = new File([\n                standardizedContent\n            ], file.name, {\n                type: \"text/csv\",\n                lastModified: file.lastModified || Date.now()\n            });\n            console.log(\"✅ CSV preprocessing completed\");\n            console.log(\"\\uD83D\\uDCCA Original size:\", file.size, \"bytes\");\n            console.log(\"\\uD83D\\uDCCA Processed size:\", standardizedFile.size, \"bytes\");\n            return standardizedFile;\n        } catch (error) {\n            console.error(\"❌ CSV preprocessing failed:\", error);\n            // Return original file if preprocessing fails\n            return file;\n        }\n    }\n    /**\n   * Upload and process census file using the Python backend (chatbot service)\n   */ static async uploadCensusFile(file) {\n        let returnDataframe = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;\n        try {\n            var _response_data, _response_data1, _response_data2, _response_data_data, _response_data3, _response_data_data1, _response_data4;\n            // Preprocess the CSV file to standardize format\n            console.log(\"\\uD83D\\uDD04 Preprocessing CSV file: \".concat(file.name));\n            const processedFile = await this.preprocessCsvFile(file);\n            // Create FormData with proper file type\n            const formData = new FormData();\n            // For CSV files, ensure correct MIME type in FormData\n            if (processedFile.name.toLowerCase().endsWith(\".csv\")) {\n                console.log(\"\\uD83D\\uDD27 Ensuring CSV file has correct MIME type\");\n                // Create a new File with correct MIME type for CSV\n                const csvFile = new File([\n                    processedFile\n                ], processedFile.name, {\n                    type: \"text/csv\",\n                    lastModified: processedFile.lastModified || Date.now()\n                });\n                formData.append(\"file\", csvFile);\n                console.log(\"\\uD83D\\uDCCB CSV file details:\", {\n                    name: csvFile.name,\n                    size: csvFile.size,\n                    type: csvFile.type,\n                    lastModified: csvFile.lastModified\n                });\n            } else {\n                // For non-CSV files, use as-is\n                formData.append(\"file\", processedFile);\n                console.log(\"\\uD83D\\uDCCB File details:\", {\n                    name: processedFile.name,\n                    size: processedFile.size,\n                    type: processedFile.type,\n                    lastModified: processedFile.lastModified\n                });\n            }\n            // Log FormData details\n            const fileEntry = formData.get(\"file\");\n            console.log(\"\\uD83D\\uDCCB FormData details:\", {\n                hasFile: formData.has(\"file\"),\n                fileName: fileEntry === null || fileEntry === void 0 ? void 0 : fileEntry.name,\n                fileType: fileEntry === null || fileEntry === void 0 ? void 0 : fileEntry.type,\n                fileSize: fileEntry === null || fileEntry === void 0 ? void 0 : fileEntry.size\n            });\n            // Verify the request will have correct Content-Type\n            console.log(\"\\uD83C\\uDF10 Request will use Content-Type: multipart/form-data (set automatically by browser)\");\n            // Build full URL for census API (Python backend)\n            const url = \"\".concat(CENSUS_API_BASE_URL, \"/api/census/processor/v1?return_dataframe=\").concat(returnDataframe);\n            console.log(\"\\uD83D\\uDCE4 Uploading census file: \".concat(processedFile.name, \" (\").concat((processedFile.size / 1024 / 1024).toFixed(2), \" MB)\"));\n            console.log(\"\\uD83D\\uDD17 Census API URL: \".concat(url));\n            console.log(\"\\uD83D\\uDCCB Request details:\", {\n                method: \"POST\",\n                url: url,\n                fileSize: processedFile.size,\n                fileName: processedFile.name,\n                returnDataframe: returnDataframe\n            });\n            // Use axios directly for census API calls to Python backend\n            // Note: Don't set Content-Type manually - let axios set it automatically for FormData\n            const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(url, formData, {\n                timeout: 300000\n            });\n            console.log(\"\\uD83D\\uDCCA Response received:\", {\n                status: response.status,\n                statusText: response.statusText,\n                success: (_response_data = response.data) === null || _response_data === void 0 ? void 0 : _response_data.success,\n                hasData: !!((_response_data1 = response.data) === null || _response_data1 === void 0 ? void 0 : _response_data1.data),\n                dataKeys: ((_response_data2 = response.data) === null || _response_data2 === void 0 ? void 0 : _response_data2.data) ? Object.keys(response.data.data) : \"no data\",\n                hasSummary: !!((_response_data3 = response.data) === null || _response_data3 === void 0 ? void 0 : (_response_data_data = _response_data3.data) === null || _response_data_data === void 0 ? void 0 : _response_data_data.summary),\n                summaryKeys: ((_response_data4 = response.data) === null || _response_data4 === void 0 ? void 0 : (_response_data_data1 = _response_data4.data) === null || _response_data_data1 === void 0 ? void 0 : _response_data_data1.summary) ? Object.keys(response.data.data.summary) : \"no summary\"\n            });\n            if (response.status === 200) {\n                if (response.data.success) {\n                    var _response_data_data_summary, _response_data_data2, _response_data_data3;\n                    // Check if the inner data also indicates success\n                    if (response.data.data && response.data.data.success === false) {\n                        // Inner processing failed\n                        console.error(\"❌ Census processing failed:\", response.data.data);\n                        console.error(\"\\uD83D\\uDCCB Error details:\", {\n                            error: response.data.data.error,\n                            message: response.data.data.message,\n                            status_code: response.data.data.status_code,\n                            fullErrorData: response.data.data\n                        });\n                        const errorMessage = response.data.data.message || \"Processing failed: \".concat(response.data.data.error) || 0;\n                        throw new Error(errorMessage);\n                    }\n                    // Log the actual response structure for debugging\n                    console.log(\"✅ Census processing completed successfully\");\n                    console.log(\"\\uD83D\\uDCCA Full response structure:\", response.data);\n                    // Try to extract employee count from various possible locations\n                    const employeeCount = ((_response_data_data2 = response.data.data) === null || _response_data_data2 === void 0 ? void 0 : (_response_data_data_summary = _response_data_data2.summary) === null || _response_data_data_summary === void 0 ? void 0 : _response_data_data_summary.total_employees) || ((_response_data_data3 = response.data.data) === null || _response_data_data3 === void 0 ? void 0 : _response_data_data3.total_employees) || response.data.total_employees || \"unknown\";\n                    console.log(\"\\uD83D\\uDC65 Processed employees: \".concat(employeeCount));\n                    return response.data;\n                } else {\n                    // Outer response indicates failure\n                    console.error(\"❌ Backend processing failed:\", response.data);\n                    throw new Error(response.data.message || \"Census processing failed on backend\");\n                }\n            } else {\n                throw new Error(\"HTTP \".concat(response.status, \": \").concat(response.statusText));\n            }\n        } catch (error) {\n            var _error_response, _error_response1, _error_response2, _error_response3, _error_response4, _error_response5, _error_response_data, _error_response6, _error_message;\n            console.error(\"❌ Census upload failed:\", error);\n            console.error(\"\\uD83D\\uDCCB Error details:\", {\n                message: error.message,\n                code: error.code,\n                status: (_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status,\n                statusText: (_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : _error_response1.statusText,\n                responseData: (_error_response2 = error.response) === null || _error_response2 === void 0 ? void 0 : _error_response2.data\n            });\n            // Provide more specific error messages\n            if (error.code === \"ECONNREFUSED\") {\n                throw new Error(\"Census API service is not running. Please start the Python backend on port 8000.\");\n            } else if (((_error_response3 = error.response) === null || _error_response3 === void 0 ? void 0 : _error_response3.status) === 404) {\n                throw new Error(\"Census API endpoint not found. Please check if the Python backend is running.\");\n            } else if (((_error_response4 = error.response) === null || _error_response4 === void 0 ? void 0 : _error_response4.status) === 413) {\n                throw new Error(\"File too large. Maximum file size is 50MB.\");\n            } else if (((_error_response5 = error.response) === null || _error_response5 === void 0 ? void 0 : _error_response5.status) === 500) {\n                var _error_response_data1, _error_response7;\n                const serverError = ((_error_response7 = error.response) === null || _error_response7 === void 0 ? void 0 : (_error_response_data1 = _error_response7.data) === null || _error_response_data1 === void 0 ? void 0 : _error_response_data1.message) || \"Internal server error during census processing\";\n                throw new Error(\"Server error: \".concat(serverError));\n            } else if ((_error_response6 = error.response) === null || _error_response6 === void 0 ? void 0 : (_error_response_data = _error_response6.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) {\n                throw new Error(error.response.data.message);\n            } else if ((_error_message = error.message) === null || _error_message === void 0 ? void 0 : _error_message.includes(\"undefined\")) {\n                var _error_response8;\n                // Handle response structure mismatch\n                console.log(\"\\uD83D\\uDD0D Response structure debugging:\", (_error_response8 = error.response) === null || _error_response8 === void 0 ? void 0 : _error_response8.data);\n                throw new Error(\"Response structure mismatch - check console for details\");\n            } else {\n                throw new Error(error.message || \"Failed to upload census file\");\n            }\n        }\n    }\n    /**\n   * Get processed census data by company/report ID\n   * Note: This would be implemented when backend supports data persistence\n   */ static async getCensusData(companyId) {\n        try {\n            // For now, this would use the Python backend when persistence is implemented\n            const url = \"\".concat(CENSUS_API_BASE_URL, \"/api/census/reports/\").concat(companyId);\n            const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(url);\n            return response.data;\n        } catch (error) {\n            console.error(\"❌ Failed to fetch census data:\", error);\n            throw new Error(error.message || \"Failed to fetch census data\");\n        }\n    }\n    /**\n   * Get broker dashboard data (list of processed companies)\n   * Note: This would be implemented when backend supports data persistence\n   */ static async getBrokerDashboard() {\n        try {\n            // For now, this would use the Python backend when persistence is implemented\n            const url = \"\".concat(CENSUS_API_BASE_URL, \"/api/census/broker/dashboard\");\n            const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(url);\n            return response.data || [];\n        } catch (error) {\n            console.error(\"❌ Failed to fetch broker dashboard:\", error);\n            // Return empty array as fallback - frontend state management handles this\n            return [];\n        }\n    }\n    /**\n   * Transform API employee data to frontend format\n   */ static transformEmployeeData(apiEmployee) {\n        var _apiEmployee_recommended_plan, _apiEmployee_recommended_plan1;\n        // Parse top 3 plans if available\n        let top3Plans = [];\n        try {\n            if (apiEmployee.top_3_available_plans) {\n                top3Plans = JSON.parse(apiEmployee.top_3_available_plans);\n            }\n        } catch (e) {\n            console.warn(\"Failed to parse top_3_available_plans:\", e);\n        }\n        // Map risk level based on plan confidence\n        const getRiskLevel = (confidence)=>{\n            if (confidence >= 0.8) return \"Low\";\n            if (confidence >= 0.6) return \"Medium\";\n            return \"High\";\n        };\n        return {\n            name: apiEmployee.name,\n            department: \"Dept \".concat(apiEmployee.dept_count),\n            risk: getRiskLevel(apiEmployee.plan_confidence),\n            age: apiEmployee.age,\n            coverage: apiEmployee.predicted_plan_type,\n            hasDependents: apiEmployee.marital_status.toLowerCase() === \"married\",\n            salary: apiEmployee.income_tier,\n            currentPlan: {\n                medical: ((_apiEmployee_recommended_plan = apiEmployee.recommended_plan) === null || _apiEmployee_recommended_plan === void 0 ? void 0 : _apiEmployee_recommended_plan.name) || \"Not Enrolled\",\n                dental: apiEmployee.predicted_benefits.includes(\"Dental\") ? \"Basic\" : \"Not Enrolled\",\n                vision: apiEmployee.predicted_benefits.includes(\"Vision\") ? \"Basic\" : \"Not Enrolled\",\n                life: apiEmployee.predicted_benefits.includes(\"Term Life\") ? \"1x Salary\" : \"None\",\n                disability: apiEmployee.predicted_benefits.includes(\"LTD\") ? \"Basic\" : \"None\"\n            },\n            coverageGaps: [],\n            insights: [\n                apiEmployee.plan_reason\n            ],\n            upsells: [],\n            planFitSummary: {\n                recommendedPlan: ((_apiEmployee_recommended_plan1 = apiEmployee.recommended_plan) === null || _apiEmployee_recommended_plan1 === void 0 ? void 0 : _apiEmployee_recommended_plan1.name) || \"No recommendation\",\n                insight: apiEmployee.plan_reason\n            },\n            // Additional API data\n            apiData: {\n                employee_id: apiEmployee.employee_id,\n                zipcode: apiEmployee.zipcode,\n                city: apiEmployee.city,\n                state: apiEmployee.state,\n                recommended_plan: apiEmployee.recommended_plan,\n                benefits_coverage: apiEmployee.benefits_coverage,\n                top_3_plans: top3Plans,\n                marketplace_plans_available: apiEmployee.marketplace_plans_available,\n                plan_count: apiEmployee.plan_count\n            }\n        };\n    }\n    /**\n   * Transform API response to frontend company data format\n   */ static transformCompanyData(apiResponse) {\n        let companyId = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : \"1\";\n        var _statistics_health_plans, _statistics_demographics, _statistics_demographics1;\n        console.log(\"\\uD83D\\uDD04 Raw API response for transformation:\", apiResponse);\n        // Handle flexible response structure - try multiple possible structures\n        let data, summary, statistics, employees;\n        if (apiResponse.data) {\n            data = apiResponse.data;\n            summary = data.summary || {};\n            statistics = data.statistics || {};\n            employees = data.employees || [];\n        } else {\n            // Direct response without .data wrapper\n            data = apiResponse;\n            summary = data.summary || {};\n            statistics = data.statistics || {};\n            employees = data.employees || [];\n        }\n        console.log(\"\\uD83D\\uDD04 Transforming company data:\", {\n            hasData: !!data,\n            hasSummary: !!summary,\n            hasStatistics: !!statistics,\n            employeeCount: employees.length,\n            summaryKeys: Object.keys(summary),\n            statisticsKeys: Object.keys(statistics),\n            dataKeys: Object.keys(data),\n            fullData: data // Log the full data to see what's actually there\n        });\n        // If we don't have the expected structure, create a minimal response\n        if (!summary.total_employees && !employees.length) {\n            console.warn(\"⚠️ No employee data found, creating minimal response\");\n            return {\n                companyName: \"Company \".concat(companyId),\n                employees: 0,\n                averageAge: 35,\n                dependents: 0,\n                planType: \"Unknown\",\n                potentialSavings: \"$0\",\n                riskScore: \"0.0/10\",\n                uploadDate: new Date().toISOString().split(\"T\")[0],\n                industry: \"Unknown\",\n                currentSpend: \"$0/month\",\n                suggestedPlan: \"No data available\",\n                planFitSummary: {\n                    silverGoldPPO: 0,\n                    hdhp: 0,\n                    familyPPO: 0,\n                    insight: \"No employee data available\"\n                },\n                employeeProfiles: [],\n                upsellOpportunities: [],\n                apiData: apiResponse\n            };\n        }\n        // Calculate potential savings (simplified calculation)\n        const avgPremium = employees.filter((emp)=>emp.recommended_plan).reduce((sum, emp)=>{\n            var _emp_recommended_plan;\n            return sum + (((_emp_recommended_plan = emp.recommended_plan) === null || _emp_recommended_plan === void 0 ? void 0 : _emp_recommended_plan.premium) || 0);\n        }, 0) / (employees.length || 1);\n        const potentialSavings = Math.round(avgPremium * employees.length * 0.15); // Assume 15% savings\n        // Determine primary plan type with null safety\n        const planTypeDistribution = ((_statistics_health_plans = statistics.health_plans) === null || _statistics_health_plans === void 0 ? void 0 : _statistics_health_plans.plan_type_distribution) || {};\n        const planTypes = Object.keys(planTypeDistribution);\n        const primaryPlanType = planTypes.length > 0 ? planTypes.reduce((a, b)=>planTypeDistribution[a] > planTypeDistribution[b] ? a : b) : \"PPO\"; // Default fallback\n        return {\n            companyName: \"Company \".concat(companyId),\n            employees: summary.total_employees || employees.length || 0,\n            averageAge: Math.round(((_statistics_demographics = statistics.demographics) === null || _statistics_demographics === void 0 ? void 0 : _statistics_demographics.average_age) || 35),\n            dependents: employees.filter((emp)=>{\n                var _emp_marital_status;\n                return ((_emp_marital_status = emp.marital_status) === null || _emp_marital_status === void 0 ? void 0 : _emp_marital_status.toLowerCase()) === \"married\";\n            }).length / (employees.length || 1),\n            planType: primaryPlanType,\n            potentialSavings: \"$\".concat(potentialSavings.toLocaleString()),\n            riskScore: \"\".concat(((summary.data_quality_score || 0.8) * 10).toFixed(1), \"/10\"),\n            uploadDate: new Date().toISOString().split(\"T\")[0],\n            industry: \"Technology\",\n            currentSpend: \"$\".concat(Math.round(avgPremium * employees.length).toLocaleString(), \"/month\"),\n            suggestedPlan: \"\".concat(primaryPlanType, \" with Enhanced Coverage\"),\n            planFitSummary: {\n                silverGoldPPO: Math.round((planTypeDistribution[\"PPO\"] || 0) / (employees.length || 1) * 100),\n                hdhp: Math.round((planTypeDistribution[\"HDHP\"] || 0) / (employees.length || 1) * 100),\n                familyPPO: Math.round(employees.filter((emp)=>{\n                    var _emp_marital_status;\n                    return ((_emp_marital_status = emp.marital_status) === null || _emp_marital_status === void 0 ? void 0 : _emp_marital_status.toLowerCase()) === \"married\";\n                }).length / (employees.length || 1) * 100),\n                insight: \"Based on \".concat(employees.length || 0, \" employees with \").concat((((_statistics_demographics1 = statistics.demographics) === null || _statistics_demographics1 === void 0 ? void 0 : _statistics_demographics1.average_age) || 35).toFixed(1), \" average age\")\n            },\n            employeeProfiles: employees.map((emp)=>this.transformEmployeeData(emp)),\n            // Generate mock upsell opportunities based on company data\n            upsellOpportunities: [\n                {\n                    category: \"Enhanced Coverage\",\n                    description: \"Upgrade \".concat(Math.round(employees.length * 0.3), \" employees to premium plans\"),\n                    savings: \"+$\".concat(Math.round(potentialSavings * 0.1).toLocaleString(), \"/month\"),\n                    confidence: \"85%\",\n                    priority: \"High\"\n                },\n                {\n                    category: \"Wellness Programs\",\n                    description: \"Preventive care initiatives for healthier workforce\",\n                    savings: \"+$\".concat(Math.round(potentialSavings * 0.05).toLocaleString(), \"/month\"),\n                    confidence: \"72%\",\n                    priority: \"Medium\"\n                }\n            ],\n            // Store original API data for reference\n            apiData: apiResponse.data\n        };\n    }\n}\n/* harmony default export */ __webpack_exports__[\"default\"] = (CensusApiService);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/census/services/censusApi.ts\n"));

/***/ })

});