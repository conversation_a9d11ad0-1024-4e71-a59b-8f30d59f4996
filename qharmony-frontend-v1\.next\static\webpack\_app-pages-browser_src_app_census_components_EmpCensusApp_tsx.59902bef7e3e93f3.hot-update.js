"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("_app-pages-browser_src_app_census_components_EmpCensusApp_tsx",{

/***/ "(app-pages-browser)/./src/app/census/public/UploadCensus.tsx":
/*!************************************************!*\
  !*** ./src/app/census/public/UploadCensus.tsx ***!
  \************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../components/ui/button */ \"(app-pages-browser)/./src/app/census/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../components/ui/card */ \"(app-pages-browser)/./src/app/census/components/ui/card.tsx\");\n/* harmony import */ var _lib_react_router_dom__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../lib/react-router-dom */ \"(app-pages-browser)/./src/app/census/lib/react-router-dom.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_FileText_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,FileText,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_FileText_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,FileText,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_FileText_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,FileText,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _components_ProfileHandler__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../components/ProfileHandler */ \"(app-pages-browser)/./src/app/census/components/ProfileHandler.tsx\");\n/* harmony import */ var _context_CensusContext__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../context/CensusContext */ \"(app-pages-browser)/./src/app/census/context/CensusContext.tsx\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../hooks/use-toast */ \"(app-pages-browser)/./src/app/census/hooks/use-toast.ts\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nconst UploadCensus = ()=>{\n    _s();\n    const navigate = (0,_lib_react_router_dom__WEBPACK_IMPORTED_MODULE_4__.useNavigate)();\n    const { uploadCensusFile, isLoading, error: censusError, clearError } = (0,_context_CensusContext__WEBPACK_IMPORTED_MODULE_6__.useCensus)();\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_7__.useToast)();\n    const [dragActive, setDragActive] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedFile, setSelectedFile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const validateFile = (file)=>{\n        // Check file size (max 10MB)\n        const maxSize = 10 * 1024 * 1024; // 10MB\n        if (file.size > maxSize) {\n            return \"File size must be less than 10MB\";\n        }\n        // Check file type\n        const allowedTypes = [\n            \"text/csv\",\n            \"application/vnd.ms-excel\",\n            \"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet\",\n            \"text/plain\"\n        ];\n        const allowedExtensions = [\n            \".csv\",\n            \".xls\",\n            \".xlsx\",\n            \".txt\"\n        ];\n        const fileExtension = file.name.toLowerCase().substring(file.name.lastIndexOf(\".\"));\n        if (!allowedTypes.includes(file.type) && !allowedExtensions.includes(fileExtension)) {\n            return \"Please upload a CSV, Excel (.xls, .xlsx), or text file\";\n        }\n        // Check for suspicious file names or extensions\n        const suspiciousExtensions = [\n            \".exe\",\n            \".bat\",\n            \".cmd\",\n            \".scr\",\n            \".pif\",\n            \".com\",\n            \".js\",\n            \".vbs\",\n            \".jar\"\n        ];\n        if (suspiciousExtensions.some((ext)=>file.name.toLowerCase().includes(ext))) {\n            return \"File type not allowed for security reasons\";\n        }\n        // Basic file name validation\n        if (file.name.length > 255) {\n            return \"File name is too long\";\n        }\n        return null;\n    };\n    const handleDrag = (e)=>{\n        e.preventDefault();\n        e.stopPropagation();\n        if (e.type === \"dragenter\" || e.type === \"dragover\") {\n            setDragActive(true);\n        } else if (e.type === \"dragleave\") {\n            setDragActive(false);\n        }\n    };\n    const handleDrop = (e)=>{\n        e.preventDefault();\n        e.stopPropagation();\n        setDragActive(false);\n        setError(null);\n        if (e.dataTransfer.files && e.dataTransfer.files[0]) {\n            const file = e.dataTransfer.files[0];\n            const validationError = validateFile(file);\n            if (validationError) {\n                setError(validationError);\n                setSelectedFile(null);\n            } else {\n                setSelectedFile(file);\n            }\n        }\n    };\n    const handleFileSelect = (e)=>{\n        setError(null);\n        if (e.target.files && e.target.files[0]) {\n            const file = e.target.files[0];\n            const validationError = validateFile(file);\n            if (validationError) {\n                setError(validationError);\n                setSelectedFile(null);\n                // Clear the input\n                e.target.value = \"\";\n            } else {\n                setSelectedFile(file);\n            }\n        }\n    };\n    const handleGenerateReport = async ()=>{\n        if (!selectedFile) {\n            setError(\"Please select a file first\");\n            return;\n        }\n        // Clear any previous errors\n        setError(null);\n        clearError();\n        try {\n            console.log(\"\\uD83D\\uDE80 Starting census file processing...\");\n            // Upload and process the census file\n            const companyId = await uploadCensusFile(selectedFile);\n            // Show success message\n            toast({\n                title: \"Census Processed Successfully!\",\n                description: \"File processed with company ID: \".concat(companyId)\n            });\n            // Navigate to the company insight page\n            navigate(\"?page=employer-insight/\".concat(companyId));\n        } catch (err) {\n            const errorMessage = err.message || \"Failed to process census file\";\n            setError(errorMessage);\n            toast({\n                title: \"Processing Failed\",\n                description: errorMessage,\n                variant: \"destructive\"\n            });\n            console.error(\"❌ Census processing failed:\", err);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"border-b bg-white/80 backdrop-blur-sm\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto px-4 py-4 flex justify-between items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    variant: \"ghost\",\n                                    onClick: ()=>navigate(\"/\"),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_FileText_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\UploadCensus.tsx\",\n                                            lineNumber: 148,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        \"Back\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\UploadCensus.tsx\",\n                                    lineNumber: 147,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent\",\n                                    children: \"BenOsphere\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\UploadCensus.tsx\",\n                                    lineNumber: 151,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\UploadCensus.tsx\",\n                            lineNumber: 146,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ProfileHandler__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\UploadCensus.tsx\",\n                            lineNumber: 153,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\UploadCensus.tsx\",\n                    lineNumber: 145,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\UploadCensus.tsx\",\n                lineNumber: 144,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"container mx-auto px-4 py-16 max-w-4xl\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center mb-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-4xl font-bold text-gray-900 mb-4\",\n                                children: \"\\uD83D\\uDCE4 Upload Group Census to Get Instant Insights\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\UploadCensus.tsx\",\n                                lineNumber: 159,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xl text-gray-600\",\n                                children: \"Drop in a census file — our AI will scan, enrich, and generate smart benefit recommendations. No formatting required.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\UploadCensus.tsx\",\n                                lineNumber: 162,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\UploadCensus.tsx\",\n                        lineNumber: 158,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        className: \"shadow-xl border-0\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                    className: \"text-center text-2xl\",\n                                    children: \"Upload Census File\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\UploadCensus.tsx\",\n                                    lineNumber: 169,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\UploadCensus.tsx\",\n                                lineNumber: 168,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                className: \"space-y-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"border-2 border-dashed rounded-xl p-12 text-center transition-all \".concat(dragActive ? \"border-blue-500 bg-blue-50\" : \"border-gray-300 hover:border-blue-400 hover:bg-gray-50\"),\n                                        onDragEnter: handleDrag,\n                                        onDragLeave: handleDrag,\n                                        onDragOver: handleDrag,\n                                        onDrop: handleDrop,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mx-auto w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_FileText_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        className: \"h-8 w-8 text-blue-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\UploadCensus.tsx\",\n                                                        lineNumber: 186,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\UploadCensus.tsx\",\n                                                    lineNumber: 185,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-red-50 border border-red-200 rounded-lg p-4 mb-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-red-600 font-medium\",\n                                                        children: [\n                                                            \"❌ \",\n                                                            error\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\UploadCensus.tsx\",\n                                                        lineNumber: 191,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\UploadCensus.tsx\",\n                                                    lineNumber: 190,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                selectedFile ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-lg font-semibold text-green-600\",\n                                                            children: [\n                                                                \"✅ \",\n                                                                selectedFile.name\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\UploadCensus.tsx\",\n                                                            lineNumber: 197,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-500\",\n                                                            children: [\n                                                                (selectedFile.size / 1024 / 1024).toFixed(2),\n                                                                \" MB\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\UploadCensus.tsx\",\n                                                            lineNumber: 200,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\UploadCensus.tsx\",\n                                                    lineNumber: 196,\n                                                    columnNumber: 19\n                                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xl font-semibold text-gray-700 mb-2\",\n                                                            children: \"\\uD83D\\uDCC4 Drag & drop CSV, Excel, or PDF\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\UploadCensus.tsx\",\n                                                            lineNumber: 206,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-500 mb-4\",\n                                                            children: \"or\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\UploadCensus.tsx\",\n                                                            lineNumber: 209,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"file\",\n                                                            id: \"file-upload\",\n                                                            className: \"hidden\",\n                                                            accept: \".csv,.xlsx,.xls,.pdf\",\n                                                            onChange: handleFileSelect\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\UploadCensus.tsx\",\n                                                            lineNumber: 210,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            htmlFor: \"file-upload\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                variant: \"outline\",\n                                                                className: \"cursor-pointer bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white border-0\",\n                                                                asChild: true,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"\\uD83D\\uDD0D Browse Files\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\UploadCensus.tsx\",\n                                                                    lineNumber: 223,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\UploadCensus.tsx\",\n                                                                lineNumber: 218,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\UploadCensus.tsx\",\n                                                            lineNumber: 217,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\UploadCensus.tsx\",\n                                                    lineNumber: 205,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\UploadCensus.tsx\",\n                                            lineNumber: 184,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\UploadCensus.tsx\",\n                                        lineNumber: 173,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center text-sm text-gray-500\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: \"Supported formats: CSV, Excel (.xlsx, .xls), PDF\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\UploadCensus.tsx\",\n                                                lineNumber: 235,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: \"Maximum file size: 50MB\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\UploadCensus.tsx\",\n                                                lineNumber: 236,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\UploadCensus.tsx\",\n                                        lineNumber: 234,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            size: \"lg\",\n                                            className: \"bg-blue-600 hover:bg-blue-700 text-white px-12 py-4 text-lg\",\n                                            onClick: handleGenerateReport,\n                                            disabled: !selectedFile || isLoading,\n                                            children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\UploadCensus.tsx\",\n                                                        lineNumber: 249,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    \"Processing...\"\n                                                ]\n                                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_FileText_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        className: \"mr-2 h-5 w-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\UploadCensus.tsx\",\n                                                        lineNumber: 254,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    \"➡️ Generate Report\"\n                                                ]\n                                            }, void 0, true)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\UploadCensus.tsx\",\n                                            lineNumber: 241,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\UploadCensus.tsx\",\n                                        lineNumber: 240,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    !selectedFile && !error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-center text-sm text-gray-400\",\n                                        children: \"Please select a file to generate your report\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\UploadCensus.tsx\",\n                                        lineNumber: 262,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-center text-sm text-blue-600\",\n                                        children: \"\\uD83D\\uDD04 Analyzing your census file...\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\UploadCensus.tsx\",\n                                        lineNumber: 268,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\UploadCensus.tsx\",\n                                lineNumber: 171,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\UploadCensus.tsx\",\n                        lineNumber: 167,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        className: \"mt-8 bg-blue-50 border-blue-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                            className: \"p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"font-semibold text-blue-900 mb-2\",\n                                    children: \"\\uD83D\\uDCA1 What happens next?\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\UploadCensus.tsx\",\n                                    lineNumber: 278,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"text-blue-800 space-y-1 text-sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"• AI analyzes employee demographics and coverage patterns\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\UploadCensus.tsx\",\n                                            lineNumber: 280,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"• Identifies cost-saving opportunities and risk factors\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\UploadCensus.tsx\",\n                                            lineNumber: 281,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"• Generates benchmarking data vs. similar groups\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\UploadCensus.tsx\",\n                                            lineNumber: 282,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"• Provides personalized plan recommendations\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\UploadCensus.tsx\",\n                                            lineNumber: 283,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\UploadCensus.tsx\",\n                                    lineNumber: 279,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\UploadCensus.tsx\",\n                            lineNumber: 277,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\UploadCensus.tsx\",\n                        lineNumber: 276,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\UploadCensus.tsx\",\n                lineNumber: 157,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\UploadCensus.tsx\",\n        lineNumber: 142,\n        columnNumber: 5\n    }, undefined);\n};\n_s(UploadCensus, \"BrV79mPdlBQ8uI9wsCGafUFcJx8=\", false, function() {\n    return [\n        _lib_react_router_dom__WEBPACK_IMPORTED_MODULE_4__.useNavigate,\n        _context_CensusContext__WEBPACK_IMPORTED_MODULE_6__.useCensus,\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_7__.useToast\n    ];\n});\n_c = UploadCensus;\n/* harmony default export */ __webpack_exports__[\"default\"] = (UploadCensus);\nvar _c;\n$RefreshReg$(_c, \"UploadCensus\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/census/public/UploadCensus.tsx\n"));

/***/ })

});