"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("_app-pages-browser_src_app_census_components_EmpCensusApp_tsx",{

/***/ "(app-pages-browser)/./src/app/census/services/censusApi.ts":
/*!**********************************************!*\
  !*** ./src/app/census/services/censusApi.ts ***!
  \**********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _APILayer_axios_helper__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/APILayer/axios_helper */ \"(app-pages-browser)/./src/APILayer/axios_helper.ts\");\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! axios */ \"(app-pages-browser)/./node_modules/axios/lib/axios.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n// Census API uses the Python backend (chatbot service) instead of the main Node.js backend\nconst CENSUS_API_BASE_URL = \"http://127.0.0.1:8000\" || 0;\nclass CensusApiService {\n    /**\n   * Upload and process census file using the Python backend (chatbot service)\n   */ static async uploadCensusFile(file) {\n        let returnDataframe = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;\n        try {\n            const formData = new FormData();\n            formData.append(\"file\", file);\n            // Build full URL for census API (Python backend)\n            const url = \"\".concat(CENSUS_API_BASE_URL, \"/api/census/processor/v1?return_dataframe=\").concat(returnDataframe);\n            console.log(\"\\uD83D\\uDCE4 Uploading census file: \".concat(file.name, \" (\").concat((file.size / 1024 / 1024).toFixed(2), \" MB)\"));\n            console.log(\"\\uD83D\\uDD17 Census API URL: \".concat(url));\n            // Use axios directly for census API calls to Python backend\n            const response = await axios__WEBPACK_IMPORTED_MODULE_1__[\"default\"].post(url, formData, {\n                headers: {\n                    \"Content-Type\": \"multipart/form-data\"\n                },\n                timeout: 300000\n            });\n            if (response.status === 200 && response.data.success) {\n                console.log(\"✅ Census processing completed: \".concat(response.data.data.summary.total_employees, \" employees\"));\n                return response.data;\n            } else {\n                throw new Error(response.data.message || \"Census processing failed\");\n            }\n        } catch (error) {\n            var _error_response, _error_response1;\n            console.error(\"❌ Census upload failed:\", error);\n            // Provide more specific error messages\n            if (error.code === \"ECONNREFUSED\") {\n                throw new Error(\"Census API service is not running. Please start the Python backend on port 8000.\");\n            } else if (((_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status) === 404) {\n                throw new Error(\"Census API endpoint not found. Please check if the Python backend is running.\");\n            } else if (((_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : _error_response1.status) === 413) {\n                throw new Error(\"File too large. Maximum file size is 50MB.\");\n            } else {\n                throw new Error(error.message || \"Failed to upload census file\");\n            }\n        }\n    }\n    /**\n   * Get processed census data by company/report ID\n   */ static async getCensusData(companyId) {\n        try {\n            const response = await (0,_APILayer_axios_helper__WEBPACK_IMPORTED_MODULE_0__.getRequest)(\"/api/census/reports/\".concat(companyId));\n            return response;\n        } catch (error) {\n            console.error(\"❌ Failed to fetch census data:\", error);\n            throw new Error(error.message || \"Failed to fetch census data\");\n        }\n    }\n    /**\n   * Get broker dashboard data (list of processed companies)\n   */ static async getBrokerDashboard() {\n        try {\n            const response = await (0,_APILayer_axios_helper__WEBPACK_IMPORTED_MODULE_0__.getRequest)(\"/api/census/broker/dashboard\");\n            return response.data || [];\n        } catch (error) {\n            console.error(\"❌ Failed to fetch broker dashboard:\", error);\n            // Return empty array as fallback\n            return [];\n        }\n    }\n    /**\n   * Transform API employee data to frontend format\n   */ static transformEmployeeData(apiEmployee) {\n        var _apiEmployee_recommended_plan, _apiEmployee_recommended_plan1;\n        // Parse top 3 plans if available\n        let top3Plans = [];\n        try {\n            if (apiEmployee.top_3_available_plans) {\n                top3Plans = JSON.parse(apiEmployee.top_3_available_plans);\n            }\n        } catch (e) {\n            console.warn(\"Failed to parse top_3_available_plans:\", e);\n        }\n        // Map risk level based on plan confidence\n        const getRiskLevel = (confidence)=>{\n            if (confidence >= 0.8) return \"Low\";\n            if (confidence >= 0.6) return \"Medium\";\n            return \"High\";\n        };\n        return {\n            name: apiEmployee.name,\n            department: \"Dept \".concat(apiEmployee.dept_count),\n            risk: getRiskLevel(apiEmployee.plan_confidence),\n            age: apiEmployee.age,\n            coverage: apiEmployee.predicted_plan_type,\n            hasDependents: apiEmployee.marital_status.toLowerCase() === \"married\",\n            salary: apiEmployee.income_tier,\n            currentPlan: {\n                medical: ((_apiEmployee_recommended_plan = apiEmployee.recommended_plan) === null || _apiEmployee_recommended_plan === void 0 ? void 0 : _apiEmployee_recommended_plan.name) || \"Not Enrolled\",\n                dental: apiEmployee.predicted_benefits.includes(\"Dental\") ? \"Basic\" : \"Not Enrolled\",\n                vision: apiEmployee.predicted_benefits.includes(\"Vision\") ? \"Basic\" : \"Not Enrolled\",\n                life: apiEmployee.predicted_benefits.includes(\"Term Life\") ? \"1x Salary\" : \"None\",\n                disability: apiEmployee.predicted_benefits.includes(\"LTD\") ? \"Basic\" : \"None\"\n            },\n            coverageGaps: [],\n            insights: [\n                apiEmployee.plan_reason\n            ],\n            upsells: [],\n            planFitSummary: {\n                recommendedPlan: ((_apiEmployee_recommended_plan1 = apiEmployee.recommended_plan) === null || _apiEmployee_recommended_plan1 === void 0 ? void 0 : _apiEmployee_recommended_plan1.name) || \"No recommendation\",\n                insight: apiEmployee.plan_reason\n            },\n            // Additional API data\n            apiData: {\n                employee_id: apiEmployee.employee_id,\n                zipcode: apiEmployee.zipcode,\n                city: apiEmployee.city,\n                state: apiEmployee.state,\n                recommended_plan: apiEmployee.recommended_plan,\n                benefits_coverage: apiEmployee.benefits_coverage,\n                top_3_plans: top3Plans,\n                marketplace_plans_available: apiEmployee.marketplace_plans_available,\n                plan_count: apiEmployee.plan_count\n            }\n        };\n    }\n    /**\n   * Transform API response to frontend company data format\n   */ static transformCompanyData(apiResponse) {\n        let companyId = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : \"1\";\n        const { summary, statistics, employees } = apiResponse.data;\n        // Calculate potential savings (simplified calculation)\n        const avgPremium = employees.filter((emp)=>emp.recommended_plan).reduce((sum, emp)=>{\n            var _emp_recommended_plan;\n            return sum + (((_emp_recommended_plan = emp.recommended_plan) === null || _emp_recommended_plan === void 0 ? void 0 : _emp_recommended_plan.premium) || 0);\n        }, 0) / employees.length;\n        const potentialSavings = Math.round(avgPremium * employees.length * 0.15); // Assume 15% savings\n        // Determine primary plan type\n        const planTypes = Object.keys(statistics.health_plans.plan_type_distribution);\n        const primaryPlanType = planTypes.reduce((a, b)=>statistics.health_plans.plan_type_distribution[a] > statistics.health_plans.plan_type_distribution[b] ? a : b);\n        return {\n            companyName: \"Company \".concat(companyId),\n            employees: summary.total_employees,\n            averageAge: Math.round(statistics.demographics.average_age),\n            dependents: employees.filter((emp)=>emp.marital_status.toLowerCase() === \"married\").length / employees.length,\n            planType: primaryPlanType,\n            potentialSavings: \"$\".concat(potentialSavings.toLocaleString()),\n            riskScore: \"\".concat((summary.data_quality_score * 10).toFixed(1), \"/10\"),\n            uploadDate: new Date().toISOString().split(\"T\")[0],\n            industry: \"Technology\",\n            currentSpend: \"$\".concat(Math.round(avgPremium * employees.length).toLocaleString(), \"/month\"),\n            suggestedPlan: \"\".concat(primaryPlanType, \" with Enhanced Coverage\"),\n            planFitSummary: {\n                silverGoldPPO: Math.round((statistics.health_plans.plan_type_distribution[\"PPO\"] || 0) / employees.length * 100),\n                hdhp: Math.round((statistics.health_plans.plan_type_distribution[\"HDHP\"] || 0) / employees.length * 100),\n                familyPPO: Math.round(employees.filter((emp)=>emp.marital_status.toLowerCase() === \"married\").length / employees.length * 100),\n                insight: \"Based on \".concat(employees.length, \" employees with \").concat(statistics.demographics.average_age.toFixed(1), \" average age\")\n            },\n            employeeProfiles: employees.map((emp)=>this.transformEmployeeData(emp)),\n            // Generate mock upsell opportunities based on company data\n            upsellOpportunities: [\n                {\n                    category: \"Enhanced Coverage\",\n                    description: \"Upgrade \".concat(Math.round(employees.length * 0.3), \" employees to premium plans\"),\n                    savings: \"+$\".concat(Math.round(potentialSavings * 0.1).toLocaleString(), \"/month\"),\n                    confidence: \"85%\",\n                    priority: \"High\"\n                },\n                {\n                    category: \"Wellness Programs\",\n                    description: \"Preventive care initiatives for healthier workforce\",\n                    savings: \"+$\".concat(Math.round(potentialSavings * 0.05).toLocaleString(), \"/month\"),\n                    confidence: \"72%\",\n                    priority: \"Medium\"\n                }\n            ],\n            // Store original API data for reference\n            apiData: apiResponse.data\n        };\n    }\n}\n/* harmony default export */ __webpack_exports__[\"default\"] = (CensusApiService);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/census/services/censusApi.ts\n"));

/***/ })

});