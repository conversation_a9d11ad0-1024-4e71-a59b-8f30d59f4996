"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("_app-pages-browser_src_app_census_components_EmpCensusApp_tsx",{

/***/ "(app-pages-browser)/./src/app/census/context/CensusContext.tsx":
/*!**************************************************!*\
  !*** ./src/app/census/context/CensusContext.tsx ***!
  \**************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CensusProvider: function() { return /* binding */ CensusProvider; },\n/* harmony export */   useCensus: function() { return /* binding */ useCensus; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _services_censusApi__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../services/censusApi */ \"(app-pages-browser)/./src/app/census/services/censusApi.ts\");\n/* __next_internal_client_entry_do_not_use__ useCensus,CensusProvider,default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\nconst CensusContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst useCensus = ()=>{\n    _s();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(CensusContext);\n    if (context === undefined) {\n        throw new Error(\"useCensus must be used within a CensusProvider\");\n    }\n    return context;\n};\n_s(useCensus, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nconst CensusProvider = (param)=>{\n    let { children } = param;\n    _s1();\n    const [companies, setCompanies] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [currentCompany, setCurrentCompany] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const clearError = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        setError(null);\n    }, []);\n    const uploadCensusFile = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async (file, companyName)=>{\n        setIsLoading(true);\n        setError(null);\n        try {\n            console.log(\"\\uD83D\\uDCE4 Starting census file upload...\");\n            // Upload and process the file\n            const response = await _services_censusApi__WEBPACK_IMPORTED_MODULE_2__[\"default\"].uploadCensusFile(file, false, companyName);\n            if (!response.success) {\n                throw new Error(response.message || \"Census processing failed\");\n            }\n            // Generate a unique company ID (in real app, this would come from backend)\n            const companyId = \"company_\".concat(Date.now());\n            // Transform API response to frontend format\n            const transformedCompany = _services_censusApi__WEBPACK_IMPORTED_MODULE_2__[\"default\"].transformCompanyData(response, companyId);\n            // Create company record\n            const newCompany = {\n                id: companyId,\n                ...transformedCompany,\n                status: \"analyzed\"\n            };\n            // Add to companies list\n            setCompanies((prev)=>[\n                    newCompany,\n                    ...prev\n                ]);\n            setCurrentCompany(newCompany);\n            console.log(\"✅ Census file processed successfully:\", newCompany);\n            return companyId;\n        } catch (err) {\n            const errorMessage = err.message || \"Failed to process census file\";\n            setError(errorMessage);\n            console.error(\"❌ Census upload failed:\", err);\n            throw new Error(errorMessage);\n        } finally{\n            setIsLoading(false);\n        }\n    }, []);\n    const getCompanyData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((companyId)=>{\n        return companies.find((company)=>company.id === companyId) || null;\n    }, [\n        companies\n    ]);\n    const refreshDashboard = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async ()=>{\n        setIsLoading(true);\n        setError(null);\n        try {\n            // In a real implementation, this would fetch from backend\n            // For now, we'll keep the existing companies\n            console.log(\"\\uD83D\\uDCCA Dashboard refreshed\");\n        } catch (err) {\n            setError(err.message || \"Failed to refresh dashboard\");\n            console.error(\"❌ Dashboard refresh failed:\", err);\n        } finally{\n            setIsLoading(false);\n        }\n    }, []);\n    const value = {\n        // State\n        companies,\n        currentCompany,\n        isLoading,\n        error,\n        // Actions\n        uploadCensusFile,\n        getCompanyData,\n        refreshDashboard,\n        clearError,\n        setCurrentCompany\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CensusContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\context\\\\CensusContext.tsx\",\n        lineNumber: 169,\n        columnNumber: 5\n    }, undefined);\n};\n_s1(CensusProvider, \"KWXup2ZkG3cRgQXUJF+Ea2NRs/0=\");\n_c = CensusProvider;\n/* harmony default export */ __webpack_exports__[\"default\"] = (CensusProvider);\nvar _c;\n$RefreshReg$(_c, \"CensusProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/census/context/CensusContext.tsx\n"));

/***/ })

});