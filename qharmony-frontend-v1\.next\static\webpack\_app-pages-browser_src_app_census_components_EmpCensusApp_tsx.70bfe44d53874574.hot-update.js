"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("_app-pages-browser_src_app_census_components_EmpCensusApp_tsx",{

/***/ "(app-pages-browser)/./src/app/census/services/censusApi.ts":
/*!**********************************************!*\
  !*** ./src/app/census/services/censusApi.ts ***!
  \**********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _APILayer_axios_helper__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/APILayer/axios_helper */ \"(app-pages-browser)/./src/APILayer/axios_helper.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nclass CensusApiService {\n    /**\n   * Upload and process census file\n   */ static async uploadCensusFile(file) {\n        let returnDataframe = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false, companyName = arguments.length > 2 ? arguments[2] : void 0;\n        try {\n            const formData = new FormData();\n            formData.append(\"file\", file);\n            // Build endpoint with query parameters\n            const endpoint = \"/api/census/processor/v1?return_dataframe=\".concat(returnDataframe);\n            console.log(\"\\uD83D\\uDCE4 Uploading census file: \".concat(file.name, \" (\").concat((file.size / 1024 / 1024).toFixed(2), \" MB)\"));\n            const response = await (0,_APILayer_axios_helper__WEBPACK_IMPORTED_MODULE_0__.uploadDocument)(endpoint, formData);\n            if (response.status === 200 && response.data.success) {\n                console.log(\"✅ Census processing completed: \".concat(response.data.data.summary.total_employees, \" employees\"));\n                return response.data;\n            } else {\n                throw new Error(response.data.message || \"Census processing failed\");\n            }\n        } catch (error) {\n            console.error(\"❌ Census upload failed:\", error);\n            throw new Error(error.message || \"Failed to upload census file\");\n        }\n    }\n    /**\n   * Get processed census data by company/report ID\n   */ static async getCensusData(companyId) {\n        try {\n            const response = await (0,_APILayer_axios_helper__WEBPACK_IMPORTED_MODULE_0__.getRequest)(\"/api/census/reports/\".concat(companyId));\n            return response;\n        } catch (error) {\n            console.error(\"❌ Failed to fetch census data:\", error);\n            throw new Error(error.message || \"Failed to fetch census data\");\n        }\n    }\n    /**\n   * Get broker dashboard data (list of processed companies)\n   */ static async getBrokerDashboard() {\n        try {\n            const response = await (0,_APILayer_axios_helper__WEBPACK_IMPORTED_MODULE_0__.getRequest)(\"/api/census/broker/dashboard\");\n            return response.data || [];\n        } catch (error) {\n            console.error(\"❌ Failed to fetch broker dashboard:\", error);\n            // Return empty array as fallback\n            return [];\n        }\n    }\n    /**\n   * Transform API employee data to frontend format\n   */ static transformEmployeeData(apiEmployee) {\n        var _apiEmployee_recommended_plan, _apiEmployee_recommended_plan1;\n        // Parse top 3 plans if available\n        let top3Plans = [];\n        try {\n            if (apiEmployee.top_3_available_plans) {\n                top3Plans = JSON.parse(apiEmployee.top_3_available_plans);\n            }\n        } catch (e) {\n            console.warn(\"Failed to parse top_3_available_plans:\", e);\n        }\n        // Map risk level based on plan confidence\n        const getRiskLevel = (confidence)=>{\n            if (confidence >= 0.8) return \"Low\";\n            if (confidence >= 0.6) return \"Medium\";\n            return \"High\";\n        };\n        return {\n            name: apiEmployee.name,\n            department: \"Dept \".concat(apiEmployee.dept_count),\n            risk: getRiskLevel(apiEmployee.plan_confidence),\n            age: apiEmployee.age,\n            coverage: apiEmployee.predicted_plan_type,\n            hasDependents: apiEmployee.marital_status.toLowerCase() === \"married\",\n            salary: apiEmployee.income_tier,\n            currentPlan: {\n                medical: ((_apiEmployee_recommended_plan = apiEmployee.recommended_plan) === null || _apiEmployee_recommended_plan === void 0 ? void 0 : _apiEmployee_recommended_plan.name) || \"Not Enrolled\",\n                dental: apiEmployee.predicted_benefits.includes(\"Dental\") ? \"Basic\" : \"Not Enrolled\",\n                vision: apiEmployee.predicted_benefits.includes(\"Vision\") ? \"Basic\" : \"Not Enrolled\",\n                life: apiEmployee.predicted_benefits.includes(\"Term Life\") ? \"1x Salary\" : \"None\",\n                disability: apiEmployee.predicted_benefits.includes(\"LTD\") ? \"Basic\" : \"None\"\n            },\n            coverageGaps: [],\n            insights: [\n                apiEmployee.plan_reason\n            ],\n            upsells: [],\n            planFitSummary: {\n                recommendedPlan: ((_apiEmployee_recommended_plan1 = apiEmployee.recommended_plan) === null || _apiEmployee_recommended_plan1 === void 0 ? void 0 : _apiEmployee_recommended_plan1.name) || \"No recommendation\",\n                insight: apiEmployee.plan_reason\n            },\n            // Additional API data\n            apiData: {\n                employee_id: apiEmployee.employee_id,\n                zipcode: apiEmployee.zipcode,\n                city: apiEmployee.city,\n                state: apiEmployee.state,\n                recommended_plan: apiEmployee.recommended_plan,\n                benefits_coverage: apiEmployee.benefits_coverage,\n                top_3_plans: top3Plans,\n                marketplace_plans_available: apiEmployee.marketplace_plans_available,\n                plan_count: apiEmployee.plan_count\n            }\n        };\n    }\n    /**\n   * Transform API response to frontend company data format\n   */ static transformCompanyData(apiResponse) {\n        let companyId = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : \"1\";\n        const { summary, statistics, employees } = apiResponse.data;\n        // Calculate potential savings (simplified calculation)\n        const avgPremium = employees.filter((emp)=>emp.recommended_plan).reduce((sum, emp)=>{\n            var _emp_recommended_plan;\n            return sum + (((_emp_recommended_plan = emp.recommended_plan) === null || _emp_recommended_plan === void 0 ? void 0 : _emp_recommended_plan.premium) || 0);\n        }, 0) / employees.length;\n        const potentialSavings = Math.round(avgPremium * employees.length * 0.15); // Assume 15% savings\n        // Determine primary plan type\n        const planTypes = Object.keys(statistics.health_plans.plan_type_distribution);\n        const primaryPlanType = planTypes.reduce((a, b)=>statistics.health_plans.plan_type_distribution[a] > statistics.health_plans.plan_type_distribution[b] ? a : b);\n        return {\n            companyName: \"Company \".concat(companyId),\n            employees: summary.total_employees,\n            averageAge: Math.round(statistics.demographics.average_age),\n            dependents: employees.filter((emp)=>emp.marital_status.toLowerCase() === \"married\").length / employees.length,\n            planType: primaryPlanType,\n            potentialSavings: \"$\".concat(potentialSavings.toLocaleString()),\n            riskScore: \"\".concat((summary.data_quality_score * 10).toFixed(1), \"/10\"),\n            uploadDate: new Date().toISOString().split(\"T\")[0],\n            industry: \"Technology\",\n            currentSpend: \"$\".concat(Math.round(avgPremium * employees.length).toLocaleString(), \"/month\"),\n            suggestedPlan: \"\".concat(primaryPlanType, \" with Enhanced Coverage\"),\n            planFitSummary: {\n                silverGoldPPO: Math.round((statistics.health_plans.plan_type_distribution[\"PPO\"] || 0) / employees.length * 100),\n                hdhp: Math.round((statistics.health_plans.plan_type_distribution[\"HDHP\"] || 0) / employees.length * 100),\n                familyPPO: Math.round(employees.filter((emp)=>emp.marital_status.toLowerCase() === \"married\").length / employees.length * 100),\n                insight: \"Based on \".concat(employees.length, \" employees with \").concat(statistics.demographics.average_age.toFixed(1), \" average age\")\n            },\n            employeeProfiles: employees.map((emp)=>this.transformEmployeeData(emp)),\n            // Generate mock upsell opportunities based on company data\n            upsellOpportunities: [\n                {\n                    category: \"Enhanced Coverage\",\n                    description: \"Upgrade \".concat(Math.round(employees.length * 0.3), \" employees to premium plans\"),\n                    savings: \"+$\".concat(Math.round(potentialSavings * 0.1).toLocaleString(), \"/month\"),\n                    confidence: \"85%\",\n                    priority: \"High\"\n                },\n                {\n                    category: \"Wellness Programs\",\n                    description: \"Preventive care initiatives for healthier workforce\",\n                    savings: \"+$\".concat(Math.round(potentialSavings * 0.05).toLocaleString(), \"/month\"),\n                    confidence: \"72%\",\n                    priority: \"Medium\"\n                }\n            ],\n            // Store original API data for reference\n            apiData: apiResponse.data\n        };\n    }\n}\n/* harmony default export */ __webpack_exports__[\"default\"] = (CensusApiService);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/census/services/censusApi.ts\n"));

/***/ })

});