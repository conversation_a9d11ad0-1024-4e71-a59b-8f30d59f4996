"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("_app-pages-browser_src_app_census_components_EmpCensusApp_tsx",{

/***/ "(app-pages-browser)/./src/app/census/context/CensusContext.tsx":
/*!**************************************************!*\
  !*** ./src/app/census/context/CensusContext.tsx ***!
  \**************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CensusProvider: function() { return /* binding */ CensusProvider; },\n/* harmony export */   useCensus: function() { return /* binding */ useCensus; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _services_censusApi__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../services/censusApi */ \"(app-pages-browser)/./src/app/census/services/censusApi.ts\");\n/* __next_internal_client_entry_do_not_use__ useCensus,CensusProvider,default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n/**\n * Census Context - Frontend State Management\n *\n * NOTE: This is temporary frontend state management.\n * In the future, this will be replaced with backend state management\n * where processed census data will be stored in the database and\n * retrieved via API calls instead of local state.\n *\n * Future Migration Plan:\n * 1. Backend will store processed census results in database\n * 2. API endpoints will provide company listing and individual company data\n * 3. This context will be simplified to just handle loading states\n * 4. Data persistence will be handled by the backend\n */ \n\nconst CensusContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst useCensus = ()=>{\n    _s();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(CensusContext);\n    if (context === undefined) {\n        throw new Error(\"useCensus must be used within a CensusProvider\");\n    }\n    return context;\n};\n_s(useCensus, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nconst CensusProvider = (param)=>{\n    let { children } = param;\n    _s1();\n    const [companies, setCompanies] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [currentCompany, setCurrentCompany] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const clearError = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        setError(null);\n    }, []);\n    const uploadCensusFile = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async (file)=>{\n        setIsLoading(true);\n        setError(null);\n        try {\n            var _newCompany_employeeProfiles, _newCompany_employeeProfiles1;\n            console.log(\"\\uD83D\\uDCE4 Starting census file upload...\");\n            // Upload and process the file\n            const response = await _services_censusApi__WEBPACK_IMPORTED_MODULE_2__[\"default\"].uploadCensusFile(file, false);\n            console.log(\"\\uD83D\\uDD0D Raw API response in context:\", response);\n            if (!response.success) {\n                throw new Error(response.message || \"Census processing failed\");\n            }\n            // Generate a unique company ID (in real app, this would come from backend)\n            const companyId = \"company_\".concat(Date.now());\n            // Transform API response to frontend format\n            const transformedCompany = _services_censusApi__WEBPACK_IMPORTED_MODULE_2__[\"default\"].transformCompanyData(response, companyId);\n            // Create company record\n            const newCompany = {\n                id: companyId,\n                ...transformedCompany,\n                status: \"analyzed\"\n            };\n            // Add to companies list\n            setCompanies((prev)=>[\n                    newCompany,\n                    ...prev\n                ]);\n            setCurrentCompany(newCompany);\n            // Debug logging\n            console.log(\"\\uD83D\\uDCCA Company data stored:\", {\n                companyId: newCompany.id,\n                employeeProfilesCount: ((_newCompany_employeeProfiles = newCompany.employeeProfiles) === null || _newCompany_employeeProfiles === void 0 ? void 0 : _newCompany_employeeProfiles.length) || 0,\n                hasEmployeeProfiles: !!newCompany.employeeProfiles,\n                sampleEmployee: (_newCompany_employeeProfiles1 = newCompany.employeeProfiles) === null || _newCompany_employeeProfiles1 === void 0 ? void 0 : _newCompany_employeeProfiles1[0]\n            });\n            console.log(\"✅ Census file processed successfully:\", newCompany);\n            return companyId;\n        } catch (err) {\n            let errorMessage = err.message || \"Failed to process census file\";\n            // Add helpful instructions for common errors\n            if (errorMessage.includes(\"Census API service is not running\")) {\n                errorMessage += \"\\n\\nTo start the Python backend:\\n1. Navigate to qharmony-bot directory\\n2. Run: python server.py\\n3. Ensure it starts on port 8000\";\n            } else if (errorMessage.includes(\"preprocessing_failed\")) {\n                errorMessage = \"Census file preprocessing failed. The file format may need adjustment. Please ensure:\\n• Column names are standard (First Name, Last Name, DOB, etc.)\\n• Data values are consistent\\n• File is in CSV format\";\n            } else if (errorMessage.includes(\"Processing failed\")) {\n                errorMessage = \"Census processing encountered an error. Please check the file format and try again.\";\n            }\n            setError(errorMessage);\n            console.error(\"❌ Census upload failed:\", err);\n            throw new Error(errorMessage);\n        } finally{\n            setIsLoading(false);\n        }\n    }, []);\n    const getCompanyData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((companyId)=>{\n        var _foundCompany_employeeProfiles;\n        const foundCompany = companies.find((company)=>company.id === companyId) || null;\n        console.log(\"\\uD83D\\uDD0D getCompanyData called:\", {\n            requestedId: companyId,\n            availableCompanies: companies.map((c)=>{\n                var _c_employeeProfiles;\n                return {\n                    id: c.id,\n                    employeeCount: ((_c_employeeProfiles = c.employeeProfiles) === null || _c_employeeProfiles === void 0 ? void 0 : _c_employeeProfiles.length) || 0\n                };\n            }),\n            foundCompany: !!foundCompany,\n            foundCompanyEmployees: (foundCompany === null || foundCompany === void 0 ? void 0 : (_foundCompany_employeeProfiles = foundCompany.employeeProfiles) === null || _foundCompany_employeeProfiles === void 0 ? void 0 : _foundCompany_employeeProfiles.length) || 0\n        });\n        return foundCompany;\n    }, [\n        companies\n    ]);\n    const refreshDashboard = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async ()=>{\n        setIsLoading(true);\n        setError(null);\n        try {\n            // In a real implementation, this would fetch from backend\n            // For now, we'll keep the existing companies\n            console.log(\"\\uD83D\\uDCCA Dashboard refreshed\");\n        } catch (err) {\n            setError(err.message || \"Failed to refresh dashboard\");\n            console.error(\"❌ Dashboard refresh failed:\", err);\n        } finally{\n            setIsLoading(false);\n        }\n    }, []);\n    const value = {\n        // State\n        companies,\n        currentCompany,\n        isLoading,\n        error,\n        // Actions\n        uploadCensusFile,\n        getCompanyData,\n        refreshDashboard,\n        clearError,\n        setCurrentCompany\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CensusContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\context\\\\CensusContext.tsx\",\n        lineNumber: 213,\n        columnNumber: 5\n    }, undefined);\n};\n_s1(CensusProvider, \"KWXup2ZkG3cRgQXUJF+Ea2NRs/0=\");\n_c = CensusProvider;\n/* harmony default export */ __webpack_exports__[\"default\"] = (CensusProvider);\nvar _c;\n$RefreshReg$(_c, \"CensusProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/census/context/CensusContext.tsx\n"));

/***/ })

});