"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("_app-pages-browser_src_app_census_components_EmpCensusApp_tsx",{

/***/ "(app-pages-browser)/./src/app/census/services/censusApi.ts":
/*!**********************************************!*\
  !*** ./src/app/census/services/censusApi.ts ***!
  \**********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! axios */ \"(app-pages-browser)/./node_modules/axios/lib/axios.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n// Census API uses the Python backend (chatbot service) instead of the main Node.js backend\nconst CENSUS_API_BASE_URL = \"http://127.0.0.1:8000\" || 0;\nclass CensusApiService {\n    /**\n   * Preprocess CSV file to standardize format before sending to backend\n   */ static async preprocessCsvFile(file) {\n        try {\n            const text = await file.text();\n            const lines = text.split(\"\\n\");\n            if (lines.length === 0) {\n                throw new Error(\"Empty CSV file\");\n            }\n            // Get header row and standardize column names\n            const headers = lines[0].split(\",\").map((header)=>header.trim().replace(/\"/g, \"\") // Remove quotes\n                .toLowerCase().replace(/\\s+/g, \"_\") // Replace spaces with underscores\n                .replace(/[^a-z0-9_]/g, \"\") // Remove special characters except underscores\n            );\n            console.log(\"\\uD83D\\uDCCB Original headers:\", lines[0].split(\",\"));\n            console.log(\"\\uD83D\\uDCCB Standardized headers:\", headers);\n            // Create standardized CSV content\n            const standardizedLines = [\n                headers.join(\",\")\n            ];\n            // Process data rows\n            for(let i = 1; i < lines.length; i++){\n                if (lines[i].trim()) {\n                    // Split CSV line properly (handling quoted values)\n                    const values = lines[i].split(\",\").map((value)=>value.trim().replace(/\"/g, \"\"));\n                    // Standardize values\n                    const standardizedValues = values.map((value, index)=>{\n                        const header = headers[index];\n                        // Standardize common values\n                        if (value === \"N/A\" || value === \"\" || value === \" \") {\n                            return \"\";\n                        }\n                        // Standardize gender\n                        if (header === \"sex\" || header === \"gender\") {\n                            return value.toUpperCase() === \"M\" ? \"Male\" : value.toUpperCase() === \"F\" ? \"Female\" : value;\n                        }\n                        // Standardize marital status\n                        if (header === \"marital_status\") {\n                            return value ? \"Married\" : \"Single\";\n                        }\n                        return value;\n                    });\n                    standardizedLines.push(standardizedValues.join(\",\"));\n                }\n            }\n            // Create new file with standardized content and correct MIME type\n            const standardizedContent = standardizedLines.join(\"\\n\");\n            const standardizedFile = new File([\n                standardizedContent\n            ], file.name, {\n                type: \"text/csv\",\n                lastModified: file.lastModified || Date.now()\n            });\n            console.log(\"✅ CSV preprocessing completed\");\n            console.log(\"\\uD83D\\uDCCA Original size:\", file.size, \"bytes\");\n            console.log(\"\\uD83D\\uDCCA Processed size:\", standardizedFile.size, \"bytes\");\n            return standardizedFile;\n        } catch (error) {\n            console.error(\"❌ CSV preprocessing failed:\", error);\n            // Return original file if preprocessing fails\n            return file;\n        }\n    }\n    /**\n   * Upload and process census file using the Python backend (chatbot service)\n   */ static async uploadCensusFile(file) {\n        let returnDataframe = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;\n        try {\n            var _response_data, _response_data1, _response_data2, _response_data_data, _response_data3, _response_data_data1, _response_data4;\n            // Preprocess the CSV file to standardize format\n            console.log(\"\\uD83D\\uDD04 Preprocessing CSV file: \".concat(file.name));\n            const processedFile = await this.preprocessCsvFile(file);\n            // Log file details before sending\n            console.log(\"\\uD83D\\uDCCB Final file details before upload:\", {\n                name: processedFile.name,\n                size: processedFile.size,\n                type: processedFile.type,\n                lastModified: processedFile.lastModified\n            });\n            const formData = new FormData();\n            formData.append(\"file\", processedFile);\n            // Log FormData details\n            console.log(\"\\uD83D\\uDCCB FormData details:\", {\n                hasFile: formData.has(\"file\"),\n                fileEntry: formData.get(\"file\")\n            });\n            // Build full URL for census API (Python backend)\n            const url = \"\".concat(CENSUS_API_BASE_URL, \"/api/census/processor/v1?return_dataframe=\").concat(returnDataframe);\n            console.log(\"\\uD83D\\uDCE4 Uploading census file: \".concat(processedFile.name, \" (\").concat((processedFile.size / 1024 / 1024).toFixed(2), \" MB)\"));\n            console.log(\"\\uD83D\\uDD17 Census API URL: \".concat(url));\n            console.log(\"\\uD83D\\uDCCB Request details:\", {\n                method: \"POST\",\n                url: url,\n                fileSize: processedFile.size,\n                fileName: processedFile.name,\n                returnDataframe: returnDataframe\n            });\n            // Use axios directly for census API calls to Python backend\n            // Note: Don't set Content-Type manually - let axios set it automatically for FormData\n            const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(url, formData, {\n                timeout: 300000\n            });\n            console.log(\"\\uD83D\\uDCCA Response received:\", {\n                status: response.status,\n                statusText: response.statusText,\n                success: (_response_data = response.data) === null || _response_data === void 0 ? void 0 : _response_data.success,\n                hasData: !!((_response_data1 = response.data) === null || _response_data1 === void 0 ? void 0 : _response_data1.data),\n                dataKeys: ((_response_data2 = response.data) === null || _response_data2 === void 0 ? void 0 : _response_data2.data) ? Object.keys(response.data.data) : \"no data\",\n                hasSummary: !!((_response_data3 = response.data) === null || _response_data3 === void 0 ? void 0 : (_response_data_data = _response_data3.data) === null || _response_data_data === void 0 ? void 0 : _response_data_data.summary),\n                summaryKeys: ((_response_data4 = response.data) === null || _response_data4 === void 0 ? void 0 : (_response_data_data1 = _response_data4.data) === null || _response_data_data1 === void 0 ? void 0 : _response_data_data1.summary) ? Object.keys(response.data.data.summary) : \"no summary\"\n            });\n            if (response.status === 200) {\n                if (response.data.success) {\n                    var _response_data_data_summary, _response_data_data2, _response_data_data3;\n                    // Check if the inner data also indicates success\n                    if (response.data.data && response.data.data.success === false) {\n                        // Inner processing failed\n                        console.error(\"❌ Census processing failed:\", response.data.data);\n                        console.error(\"\\uD83D\\uDCCB Error details:\", {\n                            error: response.data.data.error,\n                            message: response.data.data.message,\n                            status_code: response.data.data.status_code,\n                            fullErrorData: response.data.data\n                        });\n                        const errorMessage = response.data.data.message || \"Processing failed: \".concat(response.data.data.error) || 0;\n                        throw new Error(errorMessage);\n                    }\n                    // Log the actual response structure for debugging\n                    console.log(\"✅ Census processing completed successfully\");\n                    console.log(\"\\uD83D\\uDCCA Full response structure:\", response.data);\n                    // Try to extract employee count from various possible locations\n                    const employeeCount = ((_response_data_data2 = response.data.data) === null || _response_data_data2 === void 0 ? void 0 : (_response_data_data_summary = _response_data_data2.summary) === null || _response_data_data_summary === void 0 ? void 0 : _response_data_data_summary.total_employees) || ((_response_data_data3 = response.data.data) === null || _response_data_data3 === void 0 ? void 0 : _response_data_data3.total_employees) || response.data.total_employees || \"unknown\";\n                    console.log(\"\\uD83D\\uDC65 Processed employees: \".concat(employeeCount));\n                    return response.data;\n                } else {\n                    // Outer response indicates failure\n                    console.error(\"❌ Backend processing failed:\", response.data);\n                    throw new Error(response.data.message || \"Census processing failed on backend\");\n                }\n            } else {\n                throw new Error(\"HTTP \".concat(response.status, \": \").concat(response.statusText));\n            }\n        } catch (error) {\n            var _error_response, _error_response1, _error_response2, _error_response3, _error_response4, _error_response5, _error_response_data, _error_response6, _error_message;\n            console.error(\"❌ Census upload failed:\", error);\n            console.error(\"\\uD83D\\uDCCB Error details:\", {\n                message: error.message,\n                code: error.code,\n                status: (_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status,\n                statusText: (_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : _error_response1.statusText,\n                responseData: (_error_response2 = error.response) === null || _error_response2 === void 0 ? void 0 : _error_response2.data\n            });\n            // Provide more specific error messages\n            if (error.code === \"ECONNREFUSED\") {\n                throw new Error(\"Census API service is not running. Please start the Python backend on port 8000.\");\n            } else if (((_error_response3 = error.response) === null || _error_response3 === void 0 ? void 0 : _error_response3.status) === 404) {\n                throw new Error(\"Census API endpoint not found. Please check if the Python backend is running.\");\n            } else if (((_error_response4 = error.response) === null || _error_response4 === void 0 ? void 0 : _error_response4.status) === 413) {\n                throw new Error(\"File too large. Maximum file size is 50MB.\");\n            } else if (((_error_response5 = error.response) === null || _error_response5 === void 0 ? void 0 : _error_response5.status) === 500) {\n                var _error_response_data1, _error_response7;\n                const serverError = ((_error_response7 = error.response) === null || _error_response7 === void 0 ? void 0 : (_error_response_data1 = _error_response7.data) === null || _error_response_data1 === void 0 ? void 0 : _error_response_data1.message) || \"Internal server error during census processing\";\n                throw new Error(\"Server error: \".concat(serverError));\n            } else if ((_error_response6 = error.response) === null || _error_response6 === void 0 ? void 0 : (_error_response_data = _error_response6.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) {\n                throw new Error(error.response.data.message);\n            } else if ((_error_message = error.message) === null || _error_message === void 0 ? void 0 : _error_message.includes(\"undefined\")) {\n                var _error_response8;\n                // Handle response structure mismatch\n                console.log(\"\\uD83D\\uDD0D Response structure debugging:\", (_error_response8 = error.response) === null || _error_response8 === void 0 ? void 0 : _error_response8.data);\n                throw new Error(\"Response structure mismatch - check console for details\");\n            } else {\n                throw new Error(error.message || \"Failed to upload census file\");\n            }\n        }\n    }\n    /**\n   * Get processed census data by company/report ID\n   * Note: This would be implemented when backend supports data persistence\n   */ static async getCensusData(companyId) {\n        try {\n            // For now, this would use the Python backend when persistence is implemented\n            const url = \"\".concat(CENSUS_API_BASE_URL, \"/api/census/reports/\").concat(companyId);\n            const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(url);\n            return response.data;\n        } catch (error) {\n            console.error(\"❌ Failed to fetch census data:\", error);\n            throw new Error(error.message || \"Failed to fetch census data\");\n        }\n    }\n    /**\n   * Get broker dashboard data (list of processed companies)\n   * Note: This would be implemented when backend supports data persistence\n   */ static async getBrokerDashboard() {\n        try {\n            // For now, this would use the Python backend when persistence is implemented\n            const url = \"\".concat(CENSUS_API_BASE_URL, \"/api/census/broker/dashboard\");\n            const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(url);\n            return response.data || [];\n        } catch (error) {\n            console.error(\"❌ Failed to fetch broker dashboard:\", error);\n            // Return empty array as fallback - frontend state management handles this\n            return [];\n        }\n    }\n    /**\n   * Transform API employee data to frontend format\n   */ static transformEmployeeData(apiEmployee) {\n        var _apiEmployee_recommended_plan, _apiEmployee_recommended_plan1;\n        // Parse top 3 plans if available\n        let top3Plans = [];\n        try {\n            if (apiEmployee.top_3_available_plans) {\n                top3Plans = JSON.parse(apiEmployee.top_3_available_plans);\n            }\n        } catch (e) {\n            console.warn(\"Failed to parse top_3_available_plans:\", e);\n        }\n        // Map risk level based on plan confidence\n        const getRiskLevel = (confidence)=>{\n            if (confidence >= 0.8) return \"Low\";\n            if (confidence >= 0.6) return \"Medium\";\n            return \"High\";\n        };\n        return {\n            name: apiEmployee.name,\n            department: \"Dept \".concat(apiEmployee.dept_count),\n            risk: getRiskLevel(apiEmployee.plan_confidence),\n            age: apiEmployee.age,\n            coverage: apiEmployee.predicted_plan_type,\n            hasDependents: apiEmployee.marital_status.toLowerCase() === \"married\",\n            salary: apiEmployee.income_tier,\n            currentPlan: {\n                medical: ((_apiEmployee_recommended_plan = apiEmployee.recommended_plan) === null || _apiEmployee_recommended_plan === void 0 ? void 0 : _apiEmployee_recommended_plan.name) || \"Not Enrolled\",\n                dental: apiEmployee.predicted_benefits.includes(\"Dental\") ? \"Basic\" : \"Not Enrolled\",\n                vision: apiEmployee.predicted_benefits.includes(\"Vision\") ? \"Basic\" : \"Not Enrolled\",\n                life: apiEmployee.predicted_benefits.includes(\"Term Life\") ? \"1x Salary\" : \"None\",\n                disability: apiEmployee.predicted_benefits.includes(\"LTD\") ? \"Basic\" : \"None\"\n            },\n            coverageGaps: [],\n            insights: [\n                apiEmployee.plan_reason\n            ],\n            upsells: [],\n            planFitSummary: {\n                recommendedPlan: ((_apiEmployee_recommended_plan1 = apiEmployee.recommended_plan) === null || _apiEmployee_recommended_plan1 === void 0 ? void 0 : _apiEmployee_recommended_plan1.name) || \"No recommendation\",\n                insight: apiEmployee.plan_reason\n            },\n            // Additional API data\n            apiData: {\n                employee_id: apiEmployee.employee_id,\n                zipcode: apiEmployee.zipcode,\n                city: apiEmployee.city,\n                state: apiEmployee.state,\n                recommended_plan: apiEmployee.recommended_plan,\n                benefits_coverage: apiEmployee.benefits_coverage,\n                top_3_plans: top3Plans,\n                marketplace_plans_available: apiEmployee.marketplace_plans_available,\n                plan_count: apiEmployee.plan_count\n            }\n        };\n    }\n    /**\n   * Transform API response to frontend company data format\n   */ static transformCompanyData(apiResponse) {\n        let companyId = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : \"1\";\n        var _statistics_health_plans, _statistics_demographics, _statistics_demographics1;\n        console.log(\"\\uD83D\\uDD04 Raw API response for transformation:\", apiResponse);\n        // Handle flexible response structure - try multiple possible structures\n        let data, summary, statistics, employees;\n        if (apiResponse.data) {\n            data = apiResponse.data;\n            summary = data.summary || {};\n            statistics = data.statistics || {};\n            employees = data.employees || [];\n        } else {\n            // Direct response without .data wrapper\n            data = apiResponse;\n            summary = data.summary || {};\n            statistics = data.statistics || {};\n            employees = data.employees || [];\n        }\n        console.log(\"\\uD83D\\uDD04 Transforming company data:\", {\n            hasData: !!data,\n            hasSummary: !!summary,\n            hasStatistics: !!statistics,\n            employeeCount: employees.length,\n            summaryKeys: Object.keys(summary),\n            statisticsKeys: Object.keys(statistics),\n            dataKeys: Object.keys(data),\n            fullData: data // Log the full data to see what's actually there\n        });\n        // If we don't have the expected structure, create a minimal response\n        if (!summary.total_employees && !employees.length) {\n            console.warn(\"⚠️ No employee data found, creating minimal response\");\n            return {\n                companyName: \"Company \".concat(companyId),\n                employees: 0,\n                averageAge: 35,\n                dependents: 0,\n                planType: \"Unknown\",\n                potentialSavings: \"$0\",\n                riskScore: \"0.0/10\",\n                uploadDate: new Date().toISOString().split(\"T\")[0],\n                industry: \"Unknown\",\n                currentSpend: \"$0/month\",\n                suggestedPlan: \"No data available\",\n                planFitSummary: {\n                    silverGoldPPO: 0,\n                    hdhp: 0,\n                    familyPPO: 0,\n                    insight: \"No employee data available\"\n                },\n                employeeProfiles: [],\n                upsellOpportunities: [],\n                apiData: apiResponse\n            };\n        }\n        // Calculate potential savings (simplified calculation)\n        const avgPremium = employees.filter((emp)=>emp.recommended_plan).reduce((sum, emp)=>{\n            var _emp_recommended_plan;\n            return sum + (((_emp_recommended_plan = emp.recommended_plan) === null || _emp_recommended_plan === void 0 ? void 0 : _emp_recommended_plan.premium) || 0);\n        }, 0) / (employees.length || 1);\n        const potentialSavings = Math.round(avgPremium * employees.length * 0.15); // Assume 15% savings\n        // Determine primary plan type with null safety\n        const planTypeDistribution = ((_statistics_health_plans = statistics.health_plans) === null || _statistics_health_plans === void 0 ? void 0 : _statistics_health_plans.plan_type_distribution) || {};\n        const planTypes = Object.keys(planTypeDistribution);\n        const primaryPlanType = planTypes.length > 0 ? planTypes.reduce((a, b)=>planTypeDistribution[a] > planTypeDistribution[b] ? a : b) : \"PPO\"; // Default fallback\n        return {\n            companyName: \"Company \".concat(companyId),\n            employees: summary.total_employees || employees.length || 0,\n            averageAge: Math.round(((_statistics_demographics = statistics.demographics) === null || _statistics_demographics === void 0 ? void 0 : _statistics_demographics.average_age) || 35),\n            dependents: employees.filter((emp)=>{\n                var _emp_marital_status;\n                return ((_emp_marital_status = emp.marital_status) === null || _emp_marital_status === void 0 ? void 0 : _emp_marital_status.toLowerCase()) === \"married\";\n            }).length / (employees.length || 1),\n            planType: primaryPlanType,\n            potentialSavings: \"$\".concat(potentialSavings.toLocaleString()),\n            riskScore: \"\".concat(((summary.data_quality_score || 0.8) * 10).toFixed(1), \"/10\"),\n            uploadDate: new Date().toISOString().split(\"T\")[0],\n            industry: \"Technology\",\n            currentSpend: \"$\".concat(Math.round(avgPremium * employees.length).toLocaleString(), \"/month\"),\n            suggestedPlan: \"\".concat(primaryPlanType, \" with Enhanced Coverage\"),\n            planFitSummary: {\n                silverGoldPPO: Math.round((planTypeDistribution[\"PPO\"] || 0) / (employees.length || 1) * 100),\n                hdhp: Math.round((planTypeDistribution[\"HDHP\"] || 0) / (employees.length || 1) * 100),\n                familyPPO: Math.round(employees.filter((emp)=>{\n                    var _emp_marital_status;\n                    return ((_emp_marital_status = emp.marital_status) === null || _emp_marital_status === void 0 ? void 0 : _emp_marital_status.toLowerCase()) === \"married\";\n                }).length / (employees.length || 1) * 100),\n                insight: \"Based on \".concat(employees.length || 0, \" employees with \").concat((((_statistics_demographics1 = statistics.demographics) === null || _statistics_demographics1 === void 0 ? void 0 : _statistics_demographics1.average_age) || 35).toFixed(1), \" average age\")\n            },\n            employeeProfiles: employees.map((emp)=>this.transformEmployeeData(emp)),\n            // Generate mock upsell opportunities based on company data\n            upsellOpportunities: [\n                {\n                    category: \"Enhanced Coverage\",\n                    description: \"Upgrade \".concat(Math.round(employees.length * 0.3), \" employees to premium plans\"),\n                    savings: \"+$\".concat(Math.round(potentialSavings * 0.1).toLocaleString(), \"/month\"),\n                    confidence: \"85%\",\n                    priority: \"High\"\n                },\n                {\n                    category: \"Wellness Programs\",\n                    description: \"Preventive care initiatives for healthier workforce\",\n                    savings: \"+$\".concat(Math.round(potentialSavings * 0.05).toLocaleString(), \"/month\"),\n                    confidence: \"72%\",\n                    priority: \"Medium\"\n                }\n            ],\n            // Store original API data for reference\n            apiData: apiResponse.data\n        };\n    }\n}\n/* harmony default export */ __webpack_exports__[\"default\"] = (CensusApiService);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/census/services/censusApi.ts\n"));

/***/ })

});