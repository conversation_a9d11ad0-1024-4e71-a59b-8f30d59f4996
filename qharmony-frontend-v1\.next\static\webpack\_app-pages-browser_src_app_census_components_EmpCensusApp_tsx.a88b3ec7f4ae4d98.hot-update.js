"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("_app-pages-browser_src_app_census_components_EmpCensusApp_tsx",{

/***/ "(app-pages-browser)/./src/app/census/services/censusApi.ts":
/*!**********************************************!*\
  !*** ./src/app/census/services/censusApi.ts ***!
  \**********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! axios */ \"(app-pages-browser)/./node_modules/axios/lib/axios.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n// Census API uses the Python backend (chatbot service) instead of the main Node.js backend\nconst CENSUS_API_BASE_URL = \"http://127.0.0.1:8000\" || 0;\nclass CensusApiService {\n    /**\n   * Upload and process census file using the Python backend (chatbot service)\n   */ static async uploadCensusFile(file) {\n        let returnDataframe = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;\n        try {\n            var _response_data, _response_data1, _response_data_data_summary, _response_data_data, _response_data2;\n            const formData = new FormData();\n            formData.append(\"file\", file);\n            // Build full URL for census API (Python backend)\n            const url = \"\".concat(CENSUS_API_BASE_URL, \"/api/census/processor/v1?return_dataframe=\").concat(returnDataframe);\n            console.log(\"\\uD83D\\uDCE4 Uploading census file: \".concat(file.name, \" (\").concat((file.size / 1024 / 1024).toFixed(2), \" MB)\"));\n            console.log(\"\\uD83D\\uDD17 Census API URL: \".concat(url));\n            console.log(\"\\uD83D\\uDCCB Request details:\", {\n                method: \"POST\",\n                url: url,\n                fileSize: file.size,\n                fileName: file.name,\n                returnDataframe: returnDataframe\n            });\n            // Use axios directly for census API calls to Python backend\n            // Note: Don't set Content-Type manually - let axios set it automatically for FormData\n            const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(url, formData, {\n                timeout: 300000\n            });\n            console.log(\"\\uD83D\\uDCCA Response received:\", {\n                status: response.status,\n                statusText: response.statusText,\n                success: (_response_data = response.data) === null || _response_data === void 0 ? void 0 : _response_data.success,\n                hasData: !!((_response_data1 = response.data) === null || _response_data1 === void 0 ? void 0 : _response_data1.data),\n                totalEmployees: (_response_data2 = response.data) === null || _response_data2 === void 0 ? void 0 : (_response_data_data = _response_data2.data) === null || _response_data_data === void 0 ? void 0 : (_response_data_data_summary = _response_data_data.summary) === null || _response_data_data_summary === void 0 ? void 0 : _response_data_data_summary.total_employees\n            });\n            if (response.status === 200 && response.data.success) {\n                var _response_data_data_summary1, _response_data_data1, _response_data_data2;\n                // Log the actual response structure for debugging\n                console.log(\"✅ Census processing completed successfully\");\n                console.log(\"\\uD83D\\uDCCA Full response structure:\", response.data);\n                // Try to extract employee count from various possible locations\n                const employeeCount = ((_response_data_data1 = response.data.data) === null || _response_data_data1 === void 0 ? void 0 : (_response_data_data_summary1 = _response_data_data1.summary) === null || _response_data_data_summary1 === void 0 ? void 0 : _response_data_data_summary1.total_employees) || ((_response_data_data2 = response.data.data) === null || _response_data_data2 === void 0 ? void 0 : _response_data_data2.total_employees) || response.data.total_employees || \"unknown\";\n                console.log(\"\\uD83D\\uDC65 Processed employees: \".concat(employeeCount));\n                return response.data;\n            } else {\n                throw new Error(response.data.message || \"Census processing failed\");\n            }\n        } catch (error) {\n            var _error_response, _error_response1, _error_response2, _error_response3, _error_response4, _error_response5, _error_response_data, _error_response6, _error_message;\n            console.error(\"❌ Census upload failed:\", error);\n            console.error(\"\\uD83D\\uDCCB Error details:\", {\n                message: error.message,\n                code: error.code,\n                status: (_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status,\n                statusText: (_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : _error_response1.statusText,\n                responseData: (_error_response2 = error.response) === null || _error_response2 === void 0 ? void 0 : _error_response2.data\n            });\n            // Provide more specific error messages\n            if (error.code === \"ECONNREFUSED\") {\n                throw new Error(\"Census API service is not running. Please start the Python backend on port 8000.\");\n            } else if (((_error_response3 = error.response) === null || _error_response3 === void 0 ? void 0 : _error_response3.status) === 404) {\n                throw new Error(\"Census API endpoint not found. Please check if the Python backend is running.\");\n            } else if (((_error_response4 = error.response) === null || _error_response4 === void 0 ? void 0 : _error_response4.status) === 413) {\n                throw new Error(\"File too large. Maximum file size is 50MB.\");\n            } else if (((_error_response5 = error.response) === null || _error_response5 === void 0 ? void 0 : _error_response5.status) === 500) {\n                var _error_response_data1, _error_response7;\n                const serverError = ((_error_response7 = error.response) === null || _error_response7 === void 0 ? void 0 : (_error_response_data1 = _error_response7.data) === null || _error_response_data1 === void 0 ? void 0 : _error_response_data1.message) || \"Internal server error during census processing\";\n                throw new Error(\"Server error: \".concat(serverError));\n            } else if ((_error_response6 = error.response) === null || _error_response6 === void 0 ? void 0 : (_error_response_data = _error_response6.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) {\n                throw new Error(error.response.data.message);\n            } else if ((_error_message = error.message) === null || _error_message === void 0 ? void 0 : _error_message.includes(\"undefined\")) {\n                var _error_response8;\n                // Handle response structure mismatch\n                console.log(\"\\uD83D\\uDD0D Response structure debugging:\", (_error_response8 = error.response) === null || _error_response8 === void 0 ? void 0 : _error_response8.data);\n                throw new Error(\"Response structure mismatch - check console for details\");\n            } else {\n                throw new Error(error.message || \"Failed to upload census file\");\n            }\n        }\n    }\n    /**\n   * Get processed census data by company/report ID\n   * Note: This would be implemented when backend supports data persistence\n   */ static async getCensusData(companyId) {\n        try {\n            // For now, this would use the Python backend when persistence is implemented\n            const url = \"\".concat(CENSUS_API_BASE_URL, \"/api/census/reports/\").concat(companyId);\n            const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(url);\n            return response.data;\n        } catch (error) {\n            console.error(\"❌ Failed to fetch census data:\", error);\n            throw new Error(error.message || \"Failed to fetch census data\");\n        }\n    }\n    /**\n   * Get broker dashboard data (list of processed companies)\n   * Note: This would be implemented when backend supports data persistence\n   */ static async getBrokerDashboard() {\n        try {\n            // For now, this would use the Python backend when persistence is implemented\n            const url = \"\".concat(CENSUS_API_BASE_URL, \"/api/census/broker/dashboard\");\n            const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(url);\n            return response.data || [];\n        } catch (error) {\n            console.error(\"❌ Failed to fetch broker dashboard:\", error);\n            // Return empty array as fallback - frontend state management handles this\n            return [];\n        }\n    }\n    /**\n   * Transform API employee data to frontend format\n   */ static transformEmployeeData(apiEmployee) {\n        var _apiEmployee_recommended_plan, _apiEmployee_recommended_plan1;\n        // Parse top 3 plans if available\n        let top3Plans = [];\n        try {\n            if (apiEmployee.top_3_available_plans) {\n                top3Plans = JSON.parse(apiEmployee.top_3_available_plans);\n            }\n        } catch (e) {\n            console.warn(\"Failed to parse top_3_available_plans:\", e);\n        }\n        // Map risk level based on plan confidence\n        const getRiskLevel = (confidence)=>{\n            if (confidence >= 0.8) return \"Low\";\n            if (confidence >= 0.6) return \"Medium\";\n            return \"High\";\n        };\n        return {\n            name: apiEmployee.name,\n            department: \"Dept \".concat(apiEmployee.dept_count),\n            risk: getRiskLevel(apiEmployee.plan_confidence),\n            age: apiEmployee.age,\n            coverage: apiEmployee.predicted_plan_type,\n            hasDependents: apiEmployee.marital_status.toLowerCase() === \"married\",\n            salary: apiEmployee.income_tier,\n            currentPlan: {\n                medical: ((_apiEmployee_recommended_plan = apiEmployee.recommended_plan) === null || _apiEmployee_recommended_plan === void 0 ? void 0 : _apiEmployee_recommended_plan.name) || \"Not Enrolled\",\n                dental: apiEmployee.predicted_benefits.includes(\"Dental\") ? \"Basic\" : \"Not Enrolled\",\n                vision: apiEmployee.predicted_benefits.includes(\"Vision\") ? \"Basic\" : \"Not Enrolled\",\n                life: apiEmployee.predicted_benefits.includes(\"Term Life\") ? \"1x Salary\" : \"None\",\n                disability: apiEmployee.predicted_benefits.includes(\"LTD\") ? \"Basic\" : \"None\"\n            },\n            coverageGaps: [],\n            insights: [\n                apiEmployee.plan_reason\n            ],\n            upsells: [],\n            planFitSummary: {\n                recommendedPlan: ((_apiEmployee_recommended_plan1 = apiEmployee.recommended_plan) === null || _apiEmployee_recommended_plan1 === void 0 ? void 0 : _apiEmployee_recommended_plan1.name) || \"No recommendation\",\n                insight: apiEmployee.plan_reason\n            },\n            // Additional API data\n            apiData: {\n                employee_id: apiEmployee.employee_id,\n                zipcode: apiEmployee.zipcode,\n                city: apiEmployee.city,\n                state: apiEmployee.state,\n                recommended_plan: apiEmployee.recommended_plan,\n                benefits_coverage: apiEmployee.benefits_coverage,\n                top_3_plans: top3Plans,\n                marketplace_plans_available: apiEmployee.marketplace_plans_available,\n                plan_count: apiEmployee.plan_count\n            }\n        };\n    }\n    /**\n   * Transform API response to frontend company data format\n   */ static transformCompanyData(apiResponse) {\n        let companyId = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : \"1\";\n        // Handle flexible response structure\n        const data = apiResponse.data || apiResponse;\n        const summary = data.summary || {};\n        const statistics = data.statistics || {};\n        const employees = data.employees || [];\n        console.log(\"\\uD83D\\uDD04 Transforming company data:\", {\n            hasData: !!data,\n            hasSummary: !!summary,\n            hasStatistics: !!statistics,\n            employeeCount: employees.length,\n            summaryKeys: Object.keys(summary),\n            statisticsKeys: Object.keys(statistics)\n        });\n        // Calculate potential savings (simplified calculation)\n        const avgPremium = employees.filter((emp)=>emp.recommended_plan).reduce((sum, emp)=>{\n            var _emp_recommended_plan;\n            return sum + (((_emp_recommended_plan = emp.recommended_plan) === null || _emp_recommended_plan === void 0 ? void 0 : _emp_recommended_plan.premium) || 0);\n        }, 0) / (employees.length || 1);\n        const potentialSavings = Math.round(avgPremium * employees.length * 0.15); // Assume 15% savings\n        // Determine primary plan type\n        const planTypes = Object.keys(statistics.health_plans.plan_type_distribution);\n        const primaryPlanType = planTypes.reduce((a, b)=>statistics.health_plans.plan_type_distribution[a] > statistics.health_plans.plan_type_distribution[b] ? a : b);\n        return {\n            companyName: \"Company \".concat(companyId),\n            employees: summary.total_employees,\n            averageAge: Math.round(statistics.demographics.average_age),\n            dependents: employees.filter((emp)=>{\n                var _emp_marital_status;\n                return ((_emp_marital_status = emp.marital_status) === null || _emp_marital_status === void 0 ? void 0 : _emp_marital_status.toLowerCase()) === \"married\";\n            }).length / (employees.length || 1),\n            planType: primaryPlanType,\n            potentialSavings: \"$\".concat(potentialSavings.toLocaleString()),\n            riskScore: \"\".concat((summary.data_quality_score * 10).toFixed(1), \"/10\"),\n            uploadDate: new Date().toISOString().split(\"T\")[0],\n            industry: \"Technology\",\n            currentSpend: \"$\".concat(Math.round(avgPremium * employees.length).toLocaleString(), \"/month\"),\n            suggestedPlan: \"\".concat(primaryPlanType, \" with Enhanced Coverage\"),\n            planFitSummary: {\n                silverGoldPPO: Math.round((statistics.health_plans.plan_type_distribution[\"PPO\"] || 0) / employees.length * 100),\n                hdhp: Math.round((statistics.health_plans.plan_type_distribution[\"HDHP\"] || 0) / employees.length * 100),\n                familyPPO: Math.round(employees.filter((emp)=>{\n                    var _emp_marital_status;\n                    return ((_emp_marital_status = emp.marital_status) === null || _emp_marital_status === void 0 ? void 0 : _emp_marital_status.toLowerCase()) === \"married\";\n                }).length / (employees.length || 1) * 100),\n                insight: \"Based on \".concat(employees.length, \" employees with \").concat(statistics.demographics.average_age.toFixed(1), \" average age\")\n            },\n            employeeProfiles: employees.map((emp)=>this.transformEmployeeData(emp)),\n            // Generate mock upsell opportunities based on company data\n            upsellOpportunities: [\n                {\n                    category: \"Enhanced Coverage\",\n                    description: \"Upgrade \".concat(Math.round(employees.length * 0.3), \" employees to premium plans\"),\n                    savings: \"+$\".concat(Math.round(potentialSavings * 0.1).toLocaleString(), \"/month\"),\n                    confidence: \"85%\",\n                    priority: \"High\"\n                },\n                {\n                    category: \"Wellness Programs\",\n                    description: \"Preventive care initiatives for healthier workforce\",\n                    savings: \"+$\".concat(Math.round(potentialSavings * 0.05).toLocaleString(), \"/month\"),\n                    confidence: \"72%\",\n                    priority: \"Medium\"\n                }\n            ],\n            // Store original API data for reference\n            apiData: apiResponse.data\n        };\n    }\n}\n/* harmony default export */ __webpack_exports__[\"default\"] = (CensusApiService);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/census/services/censusApi.ts\n"));

/***/ })

});