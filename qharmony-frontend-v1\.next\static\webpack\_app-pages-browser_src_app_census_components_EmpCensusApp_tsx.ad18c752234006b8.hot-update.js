"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("_app-pages-browser_src_app_census_components_EmpCensusApp_tsx",{

/***/ "(app-pages-browser)/./src/app/census/services/censusApi.ts":
/*!**********************************************!*\
  !*** ./src/app/census/services/censusApi.ts ***!
  \**********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! axios */ \"(app-pages-browser)/./node_modules/axios/lib/axios.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n// Census API uses the Python backend (chatbot service) instead of the main Node.js backend\nconst CENSUS_API_BASE_URL = \"http://127.0.0.1:8000\" || 0;\nclass CensusApiService {\n    /**\n   * Upload and process census file using the Python backend (chatbot service)\n   */ static async uploadCensusFile(file) {\n        let returnDataframe = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;\n        try {\n            var _response_data, _response_data1, _response_data2, _response_data_data, _response_data3, _response_data_data1, _response_data4;\n            const formData = new FormData();\n            formData.append(\"file\", file);\n            // Build full URL for census API (Python backend)\n            const url = \"\".concat(CENSUS_API_BASE_URL, \"/api/census/processor/v1?return_dataframe=\").concat(returnDataframe);\n            console.log(\"\\uD83D\\uDCE4 Uploading census file: \".concat(file.name, \" (\").concat((file.size / 1024 / 1024).toFixed(2), \" MB)\"));\n            console.log(\"\\uD83D\\uDD17 Census API URL: \".concat(url));\n            console.log(\"\\uD83D\\uDCCB Request details:\", {\n                method: \"POST\",\n                url: url,\n                fileSize: file.size,\n                fileName: file.name,\n                returnDataframe: returnDataframe\n            });\n            // Use axios directly for census API calls to Python backend\n            // Note: Don't set Content-Type manually - let axios set it automatically for FormData\n            const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(url, formData, {\n                timeout: 300000\n            });\n            console.log(\"\\uD83D\\uDCCA Response received:\", {\n                status: response.status,\n                statusText: response.statusText,\n                success: (_response_data = response.data) === null || _response_data === void 0 ? void 0 : _response_data.success,\n                hasData: !!((_response_data1 = response.data) === null || _response_data1 === void 0 ? void 0 : _response_data1.data),\n                dataKeys: ((_response_data2 = response.data) === null || _response_data2 === void 0 ? void 0 : _response_data2.data) ? Object.keys(response.data.data) : \"no data\",\n                hasSummary: !!((_response_data3 = response.data) === null || _response_data3 === void 0 ? void 0 : (_response_data_data = _response_data3.data) === null || _response_data_data === void 0 ? void 0 : _response_data_data.summary),\n                summaryKeys: ((_response_data4 = response.data) === null || _response_data4 === void 0 ? void 0 : (_response_data_data1 = _response_data4.data) === null || _response_data_data1 === void 0 ? void 0 : _response_data_data1.summary) ? Object.keys(response.data.data.summary) : \"no summary\"\n            });\n            if (response.status === 200) {\n                if (response.data.success) {\n                    var _response_data_data_summary, _response_data_data2, _response_data_data3;\n                    // Log the actual response structure for debugging\n                    console.log(\"✅ Census processing completed successfully\");\n                    console.log(\"\\uD83D\\uDCCA Full response structure:\", response.data);\n                    // Try to extract employee count from various possible locations\n                    const employeeCount = ((_response_data_data2 = response.data.data) === null || _response_data_data2 === void 0 ? void 0 : (_response_data_data_summary = _response_data_data2.summary) === null || _response_data_data_summary === void 0 ? void 0 : _response_data_data_summary.total_employees) || ((_response_data_data3 = response.data.data) === null || _response_data_data3 === void 0 ? void 0 : _response_data_data3.total_employees) || response.data.total_employees || \"unknown\";\n                    console.log(\"\\uD83D\\uDC65 Processed employees: \".concat(employeeCount));\n                    return response.data;\n                } else {\n                    // Backend returned 200 but success=false\n                    console.error(\"❌ Backend processing failed:\", response.data);\n                    throw new Error(response.data.message || \"Census processing failed on backend\");\n                }\n            } else {\n                throw new Error(\"HTTP \".concat(response.status, \": \").concat(response.statusText));\n            }\n        } catch (error) {\n            var _error_response, _error_response1, _error_response2, _error_response3, _error_response4, _error_response5, _error_response_data, _error_response6, _error_message;\n            console.error(\"❌ Census upload failed:\", error);\n            console.error(\"\\uD83D\\uDCCB Error details:\", {\n                message: error.message,\n                code: error.code,\n                status: (_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status,\n                statusText: (_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : _error_response1.statusText,\n                responseData: (_error_response2 = error.response) === null || _error_response2 === void 0 ? void 0 : _error_response2.data\n            });\n            // Provide more specific error messages\n            if (error.code === \"ECONNREFUSED\") {\n                throw new Error(\"Census API service is not running. Please start the Python backend on port 8000.\");\n            } else if (((_error_response3 = error.response) === null || _error_response3 === void 0 ? void 0 : _error_response3.status) === 404) {\n                throw new Error(\"Census API endpoint not found. Please check if the Python backend is running.\");\n            } else if (((_error_response4 = error.response) === null || _error_response4 === void 0 ? void 0 : _error_response4.status) === 413) {\n                throw new Error(\"File too large. Maximum file size is 50MB.\");\n            } else if (((_error_response5 = error.response) === null || _error_response5 === void 0 ? void 0 : _error_response5.status) === 500) {\n                var _error_response_data1, _error_response7;\n                const serverError = ((_error_response7 = error.response) === null || _error_response7 === void 0 ? void 0 : (_error_response_data1 = _error_response7.data) === null || _error_response_data1 === void 0 ? void 0 : _error_response_data1.message) || \"Internal server error during census processing\";\n                throw new Error(\"Server error: \".concat(serverError));\n            } else if ((_error_response6 = error.response) === null || _error_response6 === void 0 ? void 0 : (_error_response_data = _error_response6.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) {\n                throw new Error(error.response.data.message);\n            } else if ((_error_message = error.message) === null || _error_message === void 0 ? void 0 : _error_message.includes(\"undefined\")) {\n                var _error_response8;\n                // Handle response structure mismatch\n                console.log(\"\\uD83D\\uDD0D Response structure debugging:\", (_error_response8 = error.response) === null || _error_response8 === void 0 ? void 0 : _error_response8.data);\n                throw new Error(\"Response structure mismatch - check console for details\");\n            } else {\n                throw new Error(error.message || \"Failed to upload census file\");\n            }\n        }\n    }\n    /**\n   * Get processed census data by company/report ID\n   * Note: This would be implemented when backend supports data persistence\n   */ static async getCensusData(companyId) {\n        try {\n            // For now, this would use the Python backend when persistence is implemented\n            const url = \"\".concat(CENSUS_API_BASE_URL, \"/api/census/reports/\").concat(companyId);\n            const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(url);\n            return response.data;\n        } catch (error) {\n            console.error(\"❌ Failed to fetch census data:\", error);\n            throw new Error(error.message || \"Failed to fetch census data\");\n        }\n    }\n    /**\n   * Get broker dashboard data (list of processed companies)\n   * Note: This would be implemented when backend supports data persistence\n   */ static async getBrokerDashboard() {\n        try {\n            // For now, this would use the Python backend when persistence is implemented\n            const url = \"\".concat(CENSUS_API_BASE_URL, \"/api/census/broker/dashboard\");\n            const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(url);\n            return response.data || [];\n        } catch (error) {\n            console.error(\"❌ Failed to fetch broker dashboard:\", error);\n            // Return empty array as fallback - frontend state management handles this\n            return [];\n        }\n    }\n    /**\n   * Transform API employee data to frontend format\n   */ static transformEmployeeData(apiEmployee) {\n        var _apiEmployee_recommended_plan, _apiEmployee_recommended_plan1;\n        // Parse top 3 plans if available\n        let top3Plans = [];\n        try {\n            if (apiEmployee.top_3_available_plans) {\n                top3Plans = JSON.parse(apiEmployee.top_3_available_plans);\n            }\n        } catch (e) {\n            console.warn(\"Failed to parse top_3_available_plans:\", e);\n        }\n        // Map risk level based on plan confidence\n        const getRiskLevel = (confidence)=>{\n            if (confidence >= 0.8) return \"Low\";\n            if (confidence >= 0.6) return \"Medium\";\n            return \"High\";\n        };\n        return {\n            name: apiEmployee.name,\n            department: \"Dept \".concat(apiEmployee.dept_count),\n            risk: getRiskLevel(apiEmployee.plan_confidence),\n            age: apiEmployee.age,\n            coverage: apiEmployee.predicted_plan_type,\n            hasDependents: apiEmployee.marital_status.toLowerCase() === \"married\",\n            salary: apiEmployee.income_tier,\n            currentPlan: {\n                medical: ((_apiEmployee_recommended_plan = apiEmployee.recommended_plan) === null || _apiEmployee_recommended_plan === void 0 ? void 0 : _apiEmployee_recommended_plan.name) || \"Not Enrolled\",\n                dental: apiEmployee.predicted_benefits.includes(\"Dental\") ? \"Basic\" : \"Not Enrolled\",\n                vision: apiEmployee.predicted_benefits.includes(\"Vision\") ? \"Basic\" : \"Not Enrolled\",\n                life: apiEmployee.predicted_benefits.includes(\"Term Life\") ? \"1x Salary\" : \"None\",\n                disability: apiEmployee.predicted_benefits.includes(\"LTD\") ? \"Basic\" : \"None\"\n            },\n            coverageGaps: [],\n            insights: [\n                apiEmployee.plan_reason\n            ],\n            upsells: [],\n            planFitSummary: {\n                recommendedPlan: ((_apiEmployee_recommended_plan1 = apiEmployee.recommended_plan) === null || _apiEmployee_recommended_plan1 === void 0 ? void 0 : _apiEmployee_recommended_plan1.name) || \"No recommendation\",\n                insight: apiEmployee.plan_reason\n            },\n            // Additional API data\n            apiData: {\n                employee_id: apiEmployee.employee_id,\n                zipcode: apiEmployee.zipcode,\n                city: apiEmployee.city,\n                state: apiEmployee.state,\n                recommended_plan: apiEmployee.recommended_plan,\n                benefits_coverage: apiEmployee.benefits_coverage,\n                top_3_plans: top3Plans,\n                marketplace_plans_available: apiEmployee.marketplace_plans_available,\n                plan_count: apiEmployee.plan_count\n            }\n        };\n    }\n    /**\n   * Transform API response to frontend company data format\n   */ static transformCompanyData(apiResponse) {\n        let companyId = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : \"1\";\n        var _statistics_health_plans, _statistics_demographics, _statistics_demographics1;\n        console.log(\"\\uD83D\\uDD04 Raw API response for transformation:\", apiResponse);\n        // Handle flexible response structure - try multiple possible structures\n        let data, summary, statistics, employees;\n        if (apiResponse.data) {\n            data = apiResponse.data;\n            summary = data.summary || {};\n            statistics = data.statistics || {};\n            employees = data.employees || [];\n        } else {\n            // Direct response without .data wrapper\n            data = apiResponse;\n            summary = data.summary || {};\n            statistics = data.statistics || {};\n            employees = data.employees || [];\n        }\n        console.log(\"\\uD83D\\uDD04 Transforming company data:\", {\n            hasData: !!data,\n            hasSummary: !!summary,\n            hasStatistics: !!statistics,\n            employeeCount: employees.length,\n            summaryKeys: Object.keys(summary),\n            statisticsKeys: Object.keys(statistics),\n            dataKeys: Object.keys(data),\n            fullData: data // Log the full data to see what's actually there\n        });\n        // If we don't have the expected structure, create a minimal response\n        if (!summary.total_employees && !employees.length) {\n            console.warn(\"⚠️ No employee data found, creating minimal response\");\n            return {\n                companyName: \"Company \".concat(companyId),\n                employees: 0,\n                averageAge: 35,\n                dependents: 0,\n                planType: \"Unknown\",\n                potentialSavings: \"$0\",\n                riskScore: \"0.0/10\",\n                uploadDate: new Date().toISOString().split(\"T\")[0],\n                industry: \"Unknown\",\n                currentSpend: \"$0/month\",\n                suggestedPlan: \"No data available\",\n                planFitSummary: {\n                    silverGoldPPO: 0,\n                    hdhp: 0,\n                    familyPPO: 0,\n                    insight: \"No employee data available\"\n                },\n                employeeProfiles: [],\n                upsellOpportunities: [],\n                apiData: apiResponse\n            };\n        }\n        // Calculate potential savings (simplified calculation)\n        const avgPremium = employees.filter((emp)=>emp.recommended_plan).reduce((sum, emp)=>{\n            var _emp_recommended_plan;\n            return sum + (((_emp_recommended_plan = emp.recommended_plan) === null || _emp_recommended_plan === void 0 ? void 0 : _emp_recommended_plan.premium) || 0);\n        }, 0) / (employees.length || 1);\n        const potentialSavings = Math.round(avgPremium * employees.length * 0.15); // Assume 15% savings\n        // Determine primary plan type with null safety\n        const planTypeDistribution = ((_statistics_health_plans = statistics.health_plans) === null || _statistics_health_plans === void 0 ? void 0 : _statistics_health_plans.plan_type_distribution) || {};\n        const planTypes = Object.keys(planTypeDistribution);\n        const primaryPlanType = planTypes.length > 0 ? planTypes.reduce((a, b)=>planTypeDistribution[a] > planTypeDistribution[b] ? a : b) : \"PPO\"; // Default fallback\n        return {\n            companyName: \"Company \".concat(companyId),\n            employees: summary.total_employees || employees.length || 0,\n            averageAge: Math.round(((_statistics_demographics = statistics.demographics) === null || _statistics_demographics === void 0 ? void 0 : _statistics_demographics.average_age) || 35),\n            dependents: employees.filter((emp)=>{\n                var _emp_marital_status;\n                return ((_emp_marital_status = emp.marital_status) === null || _emp_marital_status === void 0 ? void 0 : _emp_marital_status.toLowerCase()) === \"married\";\n            }).length / (employees.length || 1),\n            planType: primaryPlanType,\n            potentialSavings: \"$\".concat(potentialSavings.toLocaleString()),\n            riskScore: \"\".concat(((summary.data_quality_score || 0.8) * 10).toFixed(1), \"/10\"),\n            uploadDate: new Date().toISOString().split(\"T\")[0],\n            industry: \"Technology\",\n            currentSpend: \"$\".concat(Math.round(avgPremium * employees.length).toLocaleString(), \"/month\"),\n            suggestedPlan: \"\".concat(primaryPlanType, \" with Enhanced Coverage\"),\n            planFitSummary: {\n                silverGoldPPO: Math.round((planTypeDistribution[\"PPO\"] || 0) / (employees.length || 1) * 100),\n                hdhp: Math.round((planTypeDistribution[\"HDHP\"] || 0) / (employees.length || 1) * 100),\n                familyPPO: Math.round(employees.filter((emp)=>{\n                    var _emp_marital_status;\n                    return ((_emp_marital_status = emp.marital_status) === null || _emp_marital_status === void 0 ? void 0 : _emp_marital_status.toLowerCase()) === \"married\";\n                }).length / (employees.length || 1) * 100),\n                insight: \"Based on \".concat(employees.length || 0, \" employees with \").concat((((_statistics_demographics1 = statistics.demographics) === null || _statistics_demographics1 === void 0 ? void 0 : _statistics_demographics1.average_age) || 35).toFixed(1), \" average age\")\n            },\n            employeeProfiles: employees.map((emp)=>this.transformEmployeeData(emp)),\n            // Generate mock upsell opportunities based on company data\n            upsellOpportunities: [\n                {\n                    category: \"Enhanced Coverage\",\n                    description: \"Upgrade \".concat(Math.round(employees.length * 0.3), \" employees to premium plans\"),\n                    savings: \"+$\".concat(Math.round(potentialSavings * 0.1).toLocaleString(), \"/month\"),\n                    confidence: \"85%\",\n                    priority: \"High\"\n                },\n                {\n                    category: \"Wellness Programs\",\n                    description: \"Preventive care initiatives for healthier workforce\",\n                    savings: \"+$\".concat(Math.round(potentialSavings * 0.05).toLocaleString(), \"/month\"),\n                    confidence: \"72%\",\n                    priority: \"Medium\"\n                }\n            ],\n            // Store original API data for reference\n            apiData: apiResponse.data\n        };\n    }\n}\n/* harmony default export */ __webpack_exports__[\"default\"] = (CensusApiService);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/census/services/censusApi.ts\n"));

/***/ })

});