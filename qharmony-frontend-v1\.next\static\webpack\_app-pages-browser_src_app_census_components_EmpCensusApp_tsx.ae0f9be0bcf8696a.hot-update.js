"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("_app-pages-browser_src_app_census_components_EmpCensusApp_tsx",{

/***/ "(app-pages-browser)/./src/app/census/services/censusApi.ts":
/*!**********************************************!*\
  !*** ./src/app/census/services/censusApi.ts ***!
  \**********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! axios */ \"(app-pages-browser)/./node_modules/axios/lib/axios.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n// Census API uses the Python backend (chatbot service) instead of the main Node.js backend\nconst CENSUS_API_BASE_URL = \"http://127.0.0.1:8000\" || 0;\nclass CensusApiService {\n    /**\n   * Preprocess CSV file to standardize format before sending to backend\n   */ static async preprocessCsvFile(file) {\n        try {\n            const text = await file.text();\n            const lines = text.split(\"\\n\");\n            if (lines.length === 0) {\n                throw new Error(\"Empty CSV file\");\n            }\n            // Get header row and standardize column names\n            const headers = lines[0].split(\",\").map((header)=>header.trim().replace(/\"/g, \"\") // Remove quotes\n                .toLowerCase().replace(/\\s+/g, \"_\") // Replace spaces with underscores\n                .replace(/[^a-z0-9_]/g, \"\") // Remove special characters except underscores\n            );\n            console.log(\"\\uD83D\\uDCCB Original headers:\", lines[0].split(\",\"));\n            console.log(\"\\uD83D\\uDCCB Standardized headers:\", headers);\n            // Create standardized CSV content\n            const standardizedLines = [\n                headers.join(\",\")\n            ];\n            // Process data rows\n            for(let i = 1; i < lines.length; i++){\n                if (lines[i].trim()) {\n                    // Split CSV line properly (handling quoted values)\n                    const values = lines[i].split(\",\").map((value)=>value.trim().replace(/\"/g, \"\"));\n                    // Standardize values\n                    const standardizedValues = values.map((value, index)=>{\n                        const header = headers[index];\n                        // Standardize common values\n                        if (value === \"N/A\" || value === \"\" || value === \" \") {\n                            return \"\";\n                        }\n                        // Standardize gender\n                        if (header === \"sex\" || header === \"gender\") {\n                            return value.toUpperCase() === \"M\" ? \"Male\" : value.toUpperCase() === \"F\" ? \"Female\" : value;\n                        }\n                        // Standardize marital status\n                        if (header === \"marital_status\") {\n                            var _values_relationshipIndex;\n                            // Get relationship from the same row\n                            const relationshipIndex = standardizedHeaders.indexOf(\"relationship\");\n                            const relationship = relationshipIndex >= 0 ? (_values_relationshipIndex = values[relationshipIndex]) === null || _values_relationshipIndex === void 0 ? void 0 : _values_relationshipIndex.trim() : \"\";\n                            // Only employees need marital status, dependents should be empty or \"N/A\"\n                            if (relationship && relationship.toLowerCase() === \"employee\") {\n                                if (value.toLowerCase().includes(\"married\")) return \"Married\";\n                                if (value.toLowerCase().includes(\"single\")) return \"Single\";\n                                return value || \"Single\"; // Default employees to Single if empty\n                            } else {\n                                // For dependents (Spouse/Child), set to empty or \"N/A\"\n                                return \"\";\n                            }\n                        }\n                        return value;\n                    });\n                    standardizedLines.push(standardizedValues.join(\",\"));\n                }\n            }\n            // Create new file with standardized content and correct MIME type\n            const standardizedContent = standardizedLines.join(\"\\n\");\n            const standardizedFile = new File([\n                standardizedContent\n            ], file.name, {\n                type: \"text/csv\",\n                lastModified: file.lastModified || Date.now()\n            });\n            console.log(\"✅ CSV preprocessing completed\");\n            console.log(\"\\uD83D\\uDCCA Original size:\", file.size, \"bytes\");\n            console.log(\"\\uD83D\\uDCCA Processed size:\", standardizedFile.size, \"bytes\");\n            console.log(\"\\uD83D\\uDCCB Sample processed data:\", processedLines.slice(0, 5));\n            return standardizedFile;\n        } catch (error) {\n            console.error(\"❌ CSV preprocessing failed:\", error);\n            // Return original file if preprocessing fails\n            return file;\n        }\n    }\n    /**\n   * Upload and process census file using the Python backend (chatbot service)\n   */ static async uploadCensusFile(file) {\n        let returnDataframe = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;\n        try {\n            var _response_data, _response_data1, _response_data2, _response_data_data, _response_data3, _response_data_data1, _response_data4;\n            // Preprocess the CSV file to standardize format\n            console.log(\"\\uD83D\\uDD04 Preprocessing CSV file: \".concat(file.name));\n            const processedFile = await this.preprocessCsvFile(file);\n            // Create FormData with proper file type\n            const formData = new FormData();\n            // For CSV files, ensure correct MIME type in FormData\n            if (processedFile.name.toLowerCase().endsWith(\".csv\")) {\n                console.log(\"\\uD83D\\uDD27 Ensuring CSV file has correct MIME type\");\n                // Create a new File with correct MIME type for CSV\n                const csvFile = new File([\n                    processedFile\n                ], processedFile.name, {\n                    type: \"text/csv\",\n                    lastModified: processedFile.lastModified || Date.now()\n                });\n                formData.append(\"file\", csvFile);\n                console.log(\"\\uD83D\\uDCCB CSV file details:\", {\n                    name: csvFile.name,\n                    size: csvFile.size,\n                    type: csvFile.type,\n                    lastModified: csvFile.lastModified\n                });\n            } else {\n                // For non-CSV files, use as-is\n                formData.append(\"file\", processedFile);\n                console.log(\"\\uD83D\\uDCCB File details:\", {\n                    name: processedFile.name,\n                    size: processedFile.size,\n                    type: processedFile.type,\n                    lastModified: processedFile.lastModified\n                });\n            }\n            // Log FormData details\n            const fileEntry = formData.get(\"file\");\n            console.log(\"\\uD83D\\uDCCB FormData details:\", {\n                hasFile: formData.has(\"file\"),\n                fileName: fileEntry === null || fileEntry === void 0 ? void 0 : fileEntry.name,\n                fileType: fileEntry === null || fileEntry === void 0 ? void 0 : fileEntry.type,\n                fileSize: fileEntry === null || fileEntry === void 0 ? void 0 : fileEntry.size\n            });\n            // Verify the request will have correct Content-Type\n            console.log(\"\\uD83C\\uDF10 Request will use Content-Type: multipart/form-data (set automatically by browser)\");\n            // Build full URL for census API (Python backend)\n            const url = \"\".concat(CENSUS_API_BASE_URL, \"/api/census/processor/v1?return_dataframe=\").concat(returnDataframe);\n            console.log(\"\\uD83D\\uDCE4 Uploading census file: \".concat(processedFile.name, \" (\").concat((processedFile.size / 1024 / 1024).toFixed(2), \" MB)\"));\n            console.log(\"\\uD83D\\uDD17 Census API URL: \".concat(url));\n            console.log(\"\\uD83D\\uDCCB Request details:\", {\n                method: \"POST\",\n                url: url,\n                fileSize: processedFile.size,\n                fileName: processedFile.name,\n                returnDataframe: returnDataframe\n            });\n            // Use axios directly for census API calls to Python backend\n            // Note: Don't set Content-Type manually - let axios set it automatically for FormData\n            const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(url, formData, {\n                timeout: 300000\n            });\n            console.log(\"\\uD83D\\uDCCA Response received:\", {\n                status: response.status,\n                statusText: response.statusText,\n                success: (_response_data = response.data) === null || _response_data === void 0 ? void 0 : _response_data.success,\n                hasData: !!((_response_data1 = response.data) === null || _response_data1 === void 0 ? void 0 : _response_data1.data),\n                dataKeys: ((_response_data2 = response.data) === null || _response_data2 === void 0 ? void 0 : _response_data2.data) ? Object.keys(response.data.data) : \"no data\",\n                hasSummary: !!((_response_data3 = response.data) === null || _response_data3 === void 0 ? void 0 : (_response_data_data = _response_data3.data) === null || _response_data_data === void 0 ? void 0 : _response_data_data.summary),\n                summaryKeys: ((_response_data4 = response.data) === null || _response_data4 === void 0 ? void 0 : (_response_data_data1 = _response_data4.data) === null || _response_data_data1 === void 0 ? void 0 : _response_data_data1.summary) ? Object.keys(response.data.data.summary) : \"no summary\"\n            });\n            if (response.status === 200) {\n                if (response.data.success) {\n                    var _response_data_data_summary, _response_data_data2, _response_data_data3;\n                    // Check if the inner data also indicates success\n                    if (response.data.data && response.data.data.success === false) {\n                        // Inner processing failed\n                        console.error(\"❌ Census processing failed:\", response.data.data);\n                        console.error(\"\\uD83D\\uDCCB Error details:\", {\n                            error: response.data.data.error,\n                            message: response.data.data.message,\n                            status_code: response.data.data.status_code,\n                            fullErrorData: response.data.data\n                        });\n                        const errorMessage = response.data.data.message || \"Processing failed: \".concat(response.data.data.error) || 0;\n                        throw new Error(errorMessage);\n                    }\n                    // Log the actual response structure for debugging\n                    console.log(\"✅ Census processing completed successfully\");\n                    console.log(\"\\uD83D\\uDCCA Full response structure:\", response.data);\n                    // Try to extract employee count from various possible locations\n                    const employeeCount = ((_response_data_data2 = response.data.data) === null || _response_data_data2 === void 0 ? void 0 : (_response_data_data_summary = _response_data_data2.summary) === null || _response_data_data_summary === void 0 ? void 0 : _response_data_data_summary.total_employees) || ((_response_data_data3 = response.data.data) === null || _response_data_data3 === void 0 ? void 0 : _response_data_data3.total_employees) || response.data.total_employees || \"unknown\";\n                    console.log(\"\\uD83D\\uDC65 Processed employees: \".concat(employeeCount));\n                    return response.data;\n                } else {\n                    // Outer response indicates failure\n                    console.error(\"❌ Backend processing failed:\", response.data);\n                    throw new Error(response.data.message || \"Census processing failed on backend\");\n                }\n            } else {\n                throw new Error(\"HTTP \".concat(response.status, \": \").concat(response.statusText));\n            }\n        } catch (error) {\n            var _error_response, _error_response1, _error_response2, _error_response3, _error_response4, _error_response5, _error_response_data, _error_response6, _error_message;\n            console.error(\"❌ Census upload failed:\", error);\n            console.error(\"\\uD83D\\uDCCB Error details:\", {\n                message: error.message,\n                code: error.code,\n                status: (_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status,\n                statusText: (_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : _error_response1.statusText,\n                responseData: (_error_response2 = error.response) === null || _error_response2 === void 0 ? void 0 : _error_response2.data\n            });\n            // Provide more specific error messages\n            if (error.code === \"ECONNREFUSED\") {\n                throw new Error(\"Census API service is not running. Please start the Python backend on port 8000.\");\n            } else if (((_error_response3 = error.response) === null || _error_response3 === void 0 ? void 0 : _error_response3.status) === 404) {\n                throw new Error(\"Census API endpoint not found. Please check if the Python backend is running.\");\n            } else if (((_error_response4 = error.response) === null || _error_response4 === void 0 ? void 0 : _error_response4.status) === 413) {\n                throw new Error(\"File too large. Maximum file size is 50MB.\");\n            } else if (((_error_response5 = error.response) === null || _error_response5 === void 0 ? void 0 : _error_response5.status) === 500) {\n                var _error_response_data1, _error_response7;\n                const serverError = ((_error_response7 = error.response) === null || _error_response7 === void 0 ? void 0 : (_error_response_data1 = _error_response7.data) === null || _error_response_data1 === void 0 ? void 0 : _error_response_data1.message) || \"Internal server error during census processing\";\n                throw new Error(\"Server error: \".concat(serverError));\n            } else if ((_error_response6 = error.response) === null || _error_response6 === void 0 ? void 0 : (_error_response_data = _error_response6.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) {\n                throw new Error(error.response.data.message);\n            } else if ((_error_message = error.message) === null || _error_message === void 0 ? void 0 : _error_message.includes(\"undefined\")) {\n                var _error_response8;\n                // Handle response structure mismatch\n                console.log(\"\\uD83D\\uDD0D Response structure debugging:\", (_error_response8 = error.response) === null || _error_response8 === void 0 ? void 0 : _error_response8.data);\n                throw new Error(\"Response structure mismatch - check console for details\");\n            } else {\n                throw new Error(error.message || \"Failed to upload census file\");\n            }\n        }\n    }\n    /**\n   * Get processed census data by company/report ID\n   * Note: This would be implemented when backend supports data persistence\n   */ static async getCensusData(companyId) {\n        try {\n            // For now, this would use the Python backend when persistence is implemented\n            const url = \"\".concat(CENSUS_API_BASE_URL, \"/api/census/reports/\").concat(companyId);\n            const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(url);\n            return response.data;\n        } catch (error) {\n            console.error(\"❌ Failed to fetch census data:\", error);\n            throw new Error(error.message || \"Failed to fetch census data\");\n        }\n    }\n    /**\n   * Get broker dashboard data (list of processed companies)\n   * Note: This would be implemented when backend supports data persistence\n   */ static async getBrokerDashboard() {\n        try {\n            // For now, this would use the Python backend when persistence is implemented\n            const url = \"\".concat(CENSUS_API_BASE_URL, \"/api/census/broker/dashboard\");\n            const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(url);\n            return response.data || [];\n        } catch (error) {\n            console.error(\"❌ Failed to fetch broker dashboard:\", error);\n            // Return empty array as fallback - frontend state management handles this\n            return [];\n        }\n    }\n    /**\n   * Transform API employee data to frontend format\n   */ static transformEmployeeData(apiEmployee) {\n        var _apiEmployee_recommended_plan, _apiEmployee_recommended_plan1;\n        // Parse top 3 plans if available\n        let top3Plans = [];\n        try {\n            if (apiEmployee.top_3_available_plans) {\n                top3Plans = JSON.parse(apiEmployee.top_3_available_plans);\n            }\n        } catch (e) {\n            console.warn(\"Failed to parse top_3_available_plans:\", e);\n        }\n        // Map risk level based on plan confidence\n        const getRiskLevel = (confidence)=>{\n            if (confidence >= 0.8) return \"Low\";\n            if (confidence >= 0.6) return \"Medium\";\n            return \"High\";\n        };\n        return {\n            name: apiEmployee.name,\n            department: \"Dept \".concat(apiEmployee.dept_count),\n            risk: getRiskLevel(apiEmployee.plan_confidence),\n            age: apiEmployee.age,\n            coverage: apiEmployee.predicted_plan_type,\n            hasDependents: apiEmployee.marital_status.toLowerCase() === \"married\",\n            salary: apiEmployee.income_tier,\n            currentPlan: {\n                medical: ((_apiEmployee_recommended_plan = apiEmployee.recommended_plan) === null || _apiEmployee_recommended_plan === void 0 ? void 0 : _apiEmployee_recommended_plan.name) || \"Not Enrolled\",\n                dental: apiEmployee.predicted_benefits.includes(\"Dental\") ? \"Basic\" : \"Not Enrolled\",\n                vision: apiEmployee.predicted_benefits.includes(\"Vision\") ? \"Basic\" : \"Not Enrolled\",\n                life: apiEmployee.predicted_benefits.includes(\"Term Life\") ? \"1x Salary\" : \"None\",\n                disability: apiEmployee.predicted_benefits.includes(\"LTD\") ? \"Basic\" : \"None\"\n            },\n            coverageGaps: [],\n            insights: [\n                apiEmployee.plan_reason\n            ],\n            upsells: [],\n            planFitSummary: {\n                recommendedPlan: ((_apiEmployee_recommended_plan1 = apiEmployee.recommended_plan) === null || _apiEmployee_recommended_plan1 === void 0 ? void 0 : _apiEmployee_recommended_plan1.name) || \"No recommendation\",\n                insight: apiEmployee.plan_reason\n            },\n            // Additional API data\n            apiData: {\n                employee_id: apiEmployee.employee_id,\n                zipcode: apiEmployee.zipcode,\n                city: apiEmployee.city,\n                state: apiEmployee.state,\n                recommended_plan: apiEmployee.recommended_plan,\n                benefits_coverage: apiEmployee.benefits_coverage,\n                top_3_plans: top3Plans,\n                marketplace_plans_available: apiEmployee.marketplace_plans_available,\n                plan_count: apiEmployee.plan_count\n            }\n        };\n    }\n    /**\n   * Transform API response to frontend company data format\n   */ static transformCompanyData(apiResponse) {\n        let companyId = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : \"1\";\n        var _statistics_health_plans, _statistics_demographics, _statistics_demographics1;\n        console.log(\"\\uD83D\\uDD04 Raw API response for transformation:\", apiResponse);\n        // Handle flexible response structure - try multiple possible structures\n        let data, summary, statistics, employees;\n        if (apiResponse.data) {\n            data = apiResponse.data;\n            summary = data.summary || {};\n            statistics = data.statistics || {};\n            employees = data.employees || [];\n        } else {\n            // Direct response without .data wrapper\n            data = apiResponse;\n            summary = data.summary || {};\n            statistics = data.statistics || {};\n            employees = data.employees || [];\n        }\n        console.log(\"\\uD83D\\uDD04 Transforming company data:\", {\n            hasData: !!data,\n            hasSummary: !!summary,\n            hasStatistics: !!statistics,\n            employeeCount: employees.length,\n            summaryKeys: Object.keys(summary),\n            statisticsKeys: Object.keys(statistics),\n            dataKeys: Object.keys(data),\n            fullData: data // Log the full data to see what's actually there\n        });\n        // If we don't have the expected structure, create a minimal response\n        if (!summary.total_employees && !employees.length) {\n            console.warn(\"⚠️ No employee data found, creating minimal response\");\n            return {\n                companyName: \"Company \".concat(companyId),\n                employees: 0,\n                averageAge: 35,\n                dependents: 0,\n                planType: \"Unknown\",\n                potentialSavings: \"$0\",\n                riskScore: \"0.0/10\",\n                uploadDate: new Date().toISOString().split(\"T\")[0],\n                industry: \"Unknown\",\n                currentSpend: \"$0/month\",\n                suggestedPlan: \"No data available\",\n                planFitSummary: {\n                    silverGoldPPO: 0,\n                    hdhp: 0,\n                    familyPPO: 0,\n                    insight: \"No employee data available\"\n                },\n                employeeProfiles: [],\n                upsellOpportunities: [],\n                apiData: apiResponse\n            };\n        }\n        // Calculate potential savings (simplified calculation)\n        const avgPremium = employees.filter((emp)=>emp.recommended_plan).reduce((sum, emp)=>{\n            var _emp_recommended_plan;\n            return sum + (((_emp_recommended_plan = emp.recommended_plan) === null || _emp_recommended_plan === void 0 ? void 0 : _emp_recommended_plan.premium) || 0);\n        }, 0) / (employees.length || 1);\n        const potentialSavings = Math.round(avgPremium * employees.length * 0.15); // Assume 15% savings\n        // Determine primary plan type with null safety\n        const planTypeDistribution = ((_statistics_health_plans = statistics.health_plans) === null || _statistics_health_plans === void 0 ? void 0 : _statistics_health_plans.plan_type_distribution) || {};\n        const planTypes = Object.keys(planTypeDistribution);\n        const primaryPlanType = planTypes.length > 0 ? planTypes.reduce((a, b)=>planTypeDistribution[a] > planTypeDistribution[b] ? a : b) : \"PPO\"; // Default fallback\n        return {\n            companyName: \"Company \".concat(companyId),\n            employees: summary.total_employees || employees.length || 0,\n            averageAge: Math.round(((_statistics_demographics = statistics.demographics) === null || _statistics_demographics === void 0 ? void 0 : _statistics_demographics.average_age) || 35),\n            dependents: employees.filter((emp)=>{\n                var _emp_marital_status;\n                return ((_emp_marital_status = emp.marital_status) === null || _emp_marital_status === void 0 ? void 0 : _emp_marital_status.toLowerCase()) === \"married\";\n            }).length / (employees.length || 1),\n            planType: primaryPlanType,\n            potentialSavings: \"$\".concat(potentialSavings.toLocaleString()),\n            riskScore: \"\".concat(((summary.data_quality_score || 0.8) * 10).toFixed(1), \"/10\"),\n            uploadDate: new Date().toISOString().split(\"T\")[0],\n            industry: \"Technology\",\n            currentSpend: \"$\".concat(Math.round(avgPremium * employees.length).toLocaleString(), \"/month\"),\n            suggestedPlan: \"\".concat(primaryPlanType, \" with Enhanced Coverage\"),\n            planFitSummary: {\n                silverGoldPPO: Math.round((planTypeDistribution[\"PPO\"] || 0) / (employees.length || 1) * 100),\n                hdhp: Math.round((planTypeDistribution[\"HDHP\"] || 0) / (employees.length || 1) * 100),\n                familyPPO: Math.round(employees.filter((emp)=>{\n                    var _emp_marital_status;\n                    return ((_emp_marital_status = emp.marital_status) === null || _emp_marital_status === void 0 ? void 0 : _emp_marital_status.toLowerCase()) === \"married\";\n                }).length / (employees.length || 1) * 100),\n                insight: \"Based on \".concat(employees.length || 0, \" employees with \").concat((((_statistics_demographics1 = statistics.demographics) === null || _statistics_demographics1 === void 0 ? void 0 : _statistics_demographics1.average_age) || 35).toFixed(1), \" average age\")\n            },\n            employeeProfiles: employees.map((emp)=>this.transformEmployeeData(emp)),\n            // Generate mock upsell opportunities based on company data\n            upsellOpportunities: [\n                {\n                    category: \"Enhanced Coverage\",\n                    description: \"Upgrade \".concat(Math.round(employees.length * 0.3), \" employees to premium plans\"),\n                    savings: \"+$\".concat(Math.round(potentialSavings * 0.1).toLocaleString(), \"/month\"),\n                    confidence: \"85%\",\n                    priority: \"High\"\n                },\n                {\n                    category: \"Wellness Programs\",\n                    description: \"Preventive care initiatives for healthier workforce\",\n                    savings: \"+$\".concat(Math.round(potentialSavings * 0.05).toLocaleString(), \"/month\"),\n                    confidence: \"72%\",\n                    priority: \"Medium\"\n                }\n            ],\n            // Store original API data for reference\n            apiData: apiResponse.data\n        };\n    }\n}\n/* harmony default export */ __webpack_exports__[\"default\"] = (CensusApiService);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/census/services/censusApi.ts\n"));

/***/ })

});