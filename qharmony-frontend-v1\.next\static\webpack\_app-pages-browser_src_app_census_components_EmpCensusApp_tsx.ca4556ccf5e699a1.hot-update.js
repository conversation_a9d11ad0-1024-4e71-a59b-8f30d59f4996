"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("_app-pages-browser_src_app_census_components_EmpCensusApp_tsx",{

/***/ "(app-pages-browser)/./src/app/census/public/BrokerDashboard.tsx":
/*!***************************************************!*\
  !*** ./src/app/census/public/BrokerDashboard.tsx ***!
  \***************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../components/ui/button */ \"(app-pages-browser)/./src/app/census/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../components/ui/card */ \"(app-pages-browser)/./src/app/census/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../components/ui/input */ \"(app-pages-browser)/./src/app/census/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../components/ui/select */ \"(app-pages-browser)/./src/app/census/components/ui/select.tsx\");\n/* harmony import */ var _lib_react_router_dom__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../lib/react-router-dom */ \"(app-pages-browser)/./src/app/census/lib/react-router-dom.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_DollarSign_Lock_Mail_Plus_Search_Share2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,DollarSign,Lock,Mail,Plus,Search,Share2,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_DollarSign_Lock_Mail_Plus_Search_Share2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,DollarSign,Lock,Mail,Plus,Search,Share2,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_DollarSign_Lock_Mail_Plus_Search_Share2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,DollarSign,Lock,Mail,Plus,Search,Share2,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_DollarSign_Lock_Mail_Plus_Search_Share2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,DollarSign,Lock,Mail,Plus,Search,Share2,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_DollarSign_Lock_Mail_Plus_Search_Share2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,DollarSign,Lock,Mail,Plus,Search,Share2,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_DollarSign_Lock_Mail_Plus_Search_Share2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,DollarSign,Lock,Mail,Plus,Search,Share2,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_DollarSign_Lock_Mail_Plus_Search_Share2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,DollarSign,Lock,Mail,Plus,Search,Share2,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_DollarSign_Lock_Mail_Plus_Search_Share2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,DollarSign,Lock,Mail,Plus,Search,Share2,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/lock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_DollarSign_Lock_Mail_Plus_Search_Share2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,DollarSign,Lock,Mail,Plus,Search,Share2,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/share-2.js\");\n/* harmony import */ var _components_AskBrea__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../components/AskBrea */ \"(app-pages-browser)/./src/app/census/components/AskBrea.tsx\");\n/* harmony import */ var _components_NavigationDropdown__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../components/NavigationDropdown */ \"(app-pages-browser)/./src/app/census/components/NavigationDropdown.tsx\");\n/* harmony import */ var _components_ProfileHandler__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../components/ProfileHandler */ \"(app-pages-browser)/./src/app/census/components/ProfileHandler.tsx\");\n/* harmony import */ var _hooks_usePlanRestrictions__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../hooks/usePlanRestrictions */ \"(app-pages-browser)/./src/app/census/hooks/usePlanRestrictions.tsx\");\n/* harmony import */ var _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ../components/ui/use-toast */ \"(app-pages-browser)/./src/app/census/components/ui/use-toast.ts\");\n/* harmony import */ var _redux_hooks__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/redux/hooks */ \"(app-pages-browser)/./src/redux/hooks.ts\");\n/* harmony import */ var _context_CensusContext__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ../context/CensusContext */ \"(app-pages-browser)/./src/app/census/context/CensusContext.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst BrokerDashboard = ()=>{\n    _s();\n    const navigate = (0,_lib_react_router_dom__WEBPACK_IMPORTED_MODULE_6__.useNavigate)();\n    const { toast } = (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_11__.useToast)();\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [employeeCountFilter, setEmployeeCountFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [riskScoreFilter, setRiskScoreFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [suggestedPlanFilter, setSuggestedPlanFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const userDetails = (0,_redux_hooks__WEBPACK_IMPORTED_MODULE_12__.useAppSelector)((state)=>state.user.userProfile);\n    const { canViewReport, reportsRemaining, isAtLimit, trackReportView } = (0,_hooks_usePlanRestrictions__WEBPACK_IMPORTED_MODULE_10__.usePlanRestrictions)();\n    const { companies, refreshDashboard } = (0,_context_CensusContext__WEBPACK_IMPORTED_MODULE_13__.useCensus)();\n    // Use real data from census context, fallback to mock data if empty\n    const mockData = [\n        {\n            id: \"1\",\n            companyName: \"TechCorp Solutions\",\n            employees: 43,\n            averageAge: 36,\n            dependents: 1.3,\n            planType: \"PPO + HSA Combo\",\n            potentialSavings: \"$127,500\",\n            riskScore: \"6.2\",\n            uploadDate: \"2024-01-15\",\n            status: \"analyzed\"\n        },\n        {\n            id: \"2\",\n            companyName: \"Green Manufacturing\",\n            employees: 87,\n            averageAge: 42,\n            dependents: 1.8,\n            planType: \"Traditional PPO\",\n            potentialSavings: \"$245,000\",\n            riskScore: \"7.1\",\n            uploadDate: \"2024-01-10\",\n            status: \"analyzed\"\n        },\n        {\n            id: \"3\",\n            companyName: \"StartupXYZ\",\n            employees: 18,\n            averageAge: 29,\n            dependents: 0.8,\n            planType: \"HSA Only\",\n            potentialSavings: \"$32,400\",\n            riskScore: \"4.5\",\n            uploadDate: \"2024-01-18\",\n            status: \"processing\"\n        }\n    ];\n    // Use real census data if available, otherwise show mock data\n    const employerInsights = companies.length > 0 ? companies : mockData;\n    // Filter logic\n    const filteredEmployers = employerInsights.filter((employer)=>{\n        // Search filter\n        if (searchTerm && !employer.companyName.toLowerCase().includes(searchTerm.toLowerCase())) {\n            return false;\n        }\n        // Employee count filter\n        if (employeeCountFilter !== \"all\") {\n            if (employeeCountFilter === \"1-50\" && employer.employees > 50) return false;\n            if (employeeCountFilter === \"51-100\" && (employer.employees < 51 || employer.employees > 100)) return false;\n            if (employeeCountFilter === \"101+\" && employer.employees <= 100) return false;\n        }\n        // Risk score filter\n        if (riskScoreFilter !== \"all\") {\n            // Convert riskScore string to number for comparison\n            const riskScoreNum = typeof employer.riskScore === \"string\" ? parseFloat(employer.riskScore) : employer.riskScore;\n            if (riskScoreFilter === \"low\" && riskScoreNum >= 5) return false;\n            if (riskScoreFilter === \"medium\" && (riskScoreNum < 5 || riskScoreNum > 7)) return false;\n            if (riskScoreFilter === \"high\" && riskScoreNum <= 7) return false;\n        }\n        // Suggested plan filter\n        if (suggestedPlanFilter !== \"all\") {\n            if (suggestedPlanFilter === \"ppo-hsa\" && employer.planType !== \"PPO + HSA Combo\") return false;\n            if (suggestedPlanFilter === \"traditional-ppo\" && employer.planType !== \"Traditional PPO\") return false;\n            if (suggestedPlanFilter === \"other\" && employer.planType !== \"HSA Only\" && employer.planType !== \"Modern HSA Plus Plan\") return false;\n        }\n        return true;\n    });\n    const handleShareInsight = (companyName)=>{\n        const shareUrl = \"\".concat(window.location.origin, \"/shared-insight/\").concat(companyName.toLowerCase().replace(/\\s+/g, \"-\"));\n        navigator.clipboard.writeText(shareUrl);\n        console.log(\"Share link copied for \".concat(companyName));\n    };\n    const handleTileClick = (employerId, companyName)=>{\n        console.log(\"Attempting to view report for \".concat(companyName, \" (ID: \").concat(employerId, \")\"));\n        console.log(\"Can view report: \".concat(canViewReport(employerId)));\n        console.log(\"Reports remaining: \".concat(reportsRemaining));\n        console.log(\"Is at limit: \".concat(isAtLimit));\n        if (!canViewReport(employerId)) {\n            toast({\n                title: \"Upgrade Required\",\n                description: \"You've reached your free report limit (2 reports). Upgrade to Pro for unlimited access.\",\n                variant: \"destructive\"\n            });\n            navigate(\"/pricing\");\n            return;\n        }\n        // Track the report view\n        trackReportView(employerId);\n        navigate(\"?page=employer-insight/\".concat(employerId));\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-slate-50 to-blue-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"border-b bg-white/90 backdrop-blur-sm sticky top-0 z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto px-3 sm:px-4 py-3\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-xl sm:text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent\",\n                                children: \"BenOsphere\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                lineNumber: 139,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2 sm:space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_NavigationDropdown__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                        lineNumber: 143,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AskBrea__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        context: \"broker dashboard with multiple client insights\",\n                                        size: \"sm\",\n                                        variant: \"outline\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                        lineNumber: 144,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ProfileHandler__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"hidden sm:inline-flex\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                        lineNumber: 145,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                lineNumber: 142,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                        lineNumber: 138,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                    lineNumber: 137,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                lineNumber: 136,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"container mx-auto px-3 sm:px-4 py-4 sm:py-6 max-w-7xl\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col lg:flex-row lg:justify-between lg:items-start gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-2xl sm:text-3xl font-bold text-gray-900 mb-2\",\n                                                children: \"\\uD83D\\uDCCA Broker Dashboard\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                lineNumber: 156,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600 text-sm sm:text-base mb-4 lg:mb-0\",\n                                                children: \"Manage and analyze your client census data\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                lineNumber: 159,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                        lineNumber: 155,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col sm:flex-row gap-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                size: \"sm\",\n                                                onClick: ()=>{\n                                                    // Route based on user role: brokers go to upload-census, admins go to hr-upload\n                                                    if (userDetails.isBroker) {\n                                                        navigate(\"/upload-census\");\n                                                    } else if (userDetails.isAdmin && !userDetails.isBroker) {\n                                                        navigate(\"?page=hr-upload\");\n                                                    } else {\n                                                        navigate(\"/upload-census\"); // Default fallback\n                                                    }\n                                                },\n                                                className: \"bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_DollarSign_Lock_Mail_Plus_Search_Share2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                        className: \"h-4 w-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                        lineNumber: 180,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    \"Upload New Census\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                lineNumber: 166,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                variant: \"outline\",\n                                                size: \"sm\",\n                                                onClick: ()=>navigate(\"/employer-invite\"),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_DollarSign_Lock_Mail_Plus_Search_Share2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                        className: \"h-4 w-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                        lineNumber: 184,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    \"Invite Employer\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                lineNumber: 183,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                        lineNumber: 165,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                lineNumber: 154,\n                                columnNumber: 11\n                            }, undefined),\n                            reportsRemaining <= 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                className: \"mt-4 bg-gradient-to-r from-amber-50 to-orange-50 border-amber-200\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    className: \"p-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-start space-x-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_DollarSign_Lock_Mail_Plus_Search_Share2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                        className: \"h-5 w-5 text-amber-600 mt-0.5 flex-shrink-0\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                        lineNumber: 196,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"font-medium text-amber-800\",\n                                                                children: isAtLimit ? \"Report limit reached!\" : \"\".concat(reportsRemaining, \" free report\").concat(reportsRemaining === 1 ? \"\" : \"s\", \" remaining\")\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                lineNumber: 198,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-amber-700\",\n                                                                children: isAtLimit ? \"Upgrade to Pro for unlimited reports\" : \"Upgrade to Pro for unlimited access\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                lineNumber: 201,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                        lineNumber: 197,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                lineNumber: 195,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                onClick: ()=>navigate(\"/pricing\"),\n                                                className: \"bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white flex-shrink-0\",\n                                                size: \"sm\",\n                                                children: \"Upgrade Now\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                lineNumber: 206,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                        lineNumber: 194,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                    lineNumber: 193,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                lineNumber: 192,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                        lineNumber: 153,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        className: \"mb-6 shadow-lg border-0\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                            className: \"p-4 sm:p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_DollarSign_Lock_Mail_Plus_Search_Share2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                className: \"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                lineNumber: 225,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                placeholder: \"Search by Client Name\",\n                                                value: searchTerm,\n                                                onChange: (e)=>setSearchTerm(e.target.value),\n                                                className: \"pl-10\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                lineNumber: 226,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                        lineNumber: 224,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 sm:grid-cols-3 gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"text-sm font-medium text-gray-700 mb-2 block\",\n                                                        children: \"Employee Count\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                        lineNumber: 237,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.Select, {\n                                                        value: employeeCountFilter,\n                                                        onValueChange: setEmployeeCountFilter,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectTrigger, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectValue, {\n                                                                    placeholder: \"All\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                    lineNumber: 240,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                lineNumber: 239,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectContent, {\n                                                                className: \"bg-white\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                                        value: \"all\",\n                                                                        children: \"All\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                        lineNumber: 243,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                                        value: \"1-50\",\n                                                                        children: \"1–50\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                        lineNumber: 244,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                                        value: \"51-100\",\n                                                                        children: \"51–100\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                        lineNumber: 245,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                                        value: \"101+\",\n                                                                        children: \"101+\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                        lineNumber: 246,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                lineNumber: 242,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                        lineNumber: 238,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                lineNumber: 236,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"text-sm font-medium text-gray-700 mb-2 block\",\n                                                        children: \"Risk Score\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                        lineNumber: 252,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.Select, {\n                                                        value: riskScoreFilter,\n                                                        onValueChange: setRiskScoreFilter,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectTrigger, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectValue, {\n                                                                    placeholder: \"All\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                    lineNumber: 255,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                lineNumber: 254,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectContent, {\n                                                                className: \"bg-white\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                                        value: \"all\",\n                                                                        children: \"All\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                        lineNumber: 258,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                                        value: \"low\",\n                                                                        children: \"Low (<5)\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                        lineNumber: 259,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                                        value: \"medium\",\n                                                                        children: \"Medium (5–7)\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                        lineNumber: 260,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                                        value: \"high\",\n                                                                        children: \"High (>7)\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                        lineNumber: 261,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                lineNumber: 257,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                        lineNumber: 253,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                lineNumber: 251,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"text-sm font-medium text-gray-700 mb-2 block\",\n                                                        children: \"Suggested Plan\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                        lineNumber: 267,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.Select, {\n                                                        value: suggestedPlanFilter,\n                                                        onValueChange: setSuggestedPlanFilter,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectTrigger, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectValue, {\n                                                                    placeholder: \"All\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                    lineNumber: 270,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                lineNumber: 269,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectContent, {\n                                                                className: \"bg-white\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                                        value: \"all\",\n                                                                        children: \"All\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                        lineNumber: 273,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                                        value: \"ppo-hsa\",\n                                                                        children: \"PPO + HSA Combo\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                        lineNumber: 274,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                                        value: \"traditional-ppo\",\n                                                                        children: \"Traditional PPO\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                        lineNumber: 275,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                                        value: \"other\",\n                                                                        children: \"Other\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                        lineNumber: 276,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                lineNumber: 272,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                        lineNumber: 268,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                lineNumber: 266,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                        lineNumber: 235,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                lineNumber: 222,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                            lineNumber: 221,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                        lineNumber: 220,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4 mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                className: \"bg-gradient-to-br from-blue-100 to-blue-200 border-blue-300 hover:shadow-md transition-all duration-200\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    className: \"p-3 sm:p-4 text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_DollarSign_Lock_Mail_Plus_Search_Share2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                            className: \"h-6 w-6 sm:h-8 sm:w-8 mx-auto mb-1 sm:mb-2 text-blue-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                            lineNumber: 289,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs sm:text-sm text-blue-600 font-medium\",\n                                            children: \"Total Clients\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                            lineNumber: 290,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xl sm:text-2xl font-bold text-blue-900\",\n                                            children: filteredEmployers.length\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                            lineNumber: 291,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                    lineNumber: 288,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                lineNumber: 287,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                className: \"bg-gradient-to-br from-emerald-100 to-teal-200 border-emerald-300 hover:shadow-md transition-all duration-200\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    className: \"p-3 sm:p-4 text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_DollarSign_Lock_Mail_Plus_Search_Share2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                            className: \"h-6 w-6 sm:h-8 sm:w-8 mx-auto mb-1 sm:mb-2 text-emerald-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                            lineNumber: 297,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs sm:text-sm text-emerald-600 font-medium\",\n                                            children: \"Total Employees\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                            lineNumber: 298,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xl sm:text-2xl font-bold text-emerald-900\",\n                                            children: filteredEmployers.reduce((sum, emp)=>sum + emp.employees, 0)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                            lineNumber: 299,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                    lineNumber: 296,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                lineNumber: 295,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                className: \"bg-gradient-to-br from-orange-100 to-red-200 border-orange-300 hover:shadow-md transition-all duration-200\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    className: \"p-3 sm:p-4 text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_DollarSign_Lock_Mail_Plus_Search_Share2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                            className: \"h-6 w-6 sm:h-8 sm:w-8 mx-auto mb-1 sm:mb-2 text-orange-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                            lineNumber: 307,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs sm:text-sm text-orange-600 font-medium\",\n                                            children: \"Total Potential Savings\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                            lineNumber: 308,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-lg sm:text-2xl font-bold text-orange-900\",\n                                            children: \"$404,900\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                            lineNumber: 309,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                    lineNumber: 306,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                lineNumber: 305,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                className: \"bg-gradient-to-br from-purple-100 to-pink-200 border-purple-300 hover:shadow-md transition-all duration-200\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    className: \"p-3 sm:p-4 text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_DollarSign_Lock_Mail_Plus_Search_Share2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                            className: \"h-6 w-6 sm:h-8 sm:w-8 mx-auto mb-1 sm:mb-2 text-purple-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                            lineNumber: 315,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs sm:text-sm text-purple-600 font-medium\",\n                                            children: \"Avg Risk Score\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                            lineNumber: 316,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xl sm:text-2xl font-bold text-purple-900\",\n                                            children: \"5.9/10\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                            lineNumber: 317,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                    lineNumber: 314,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                lineNumber: 313,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                        lineNumber: 286,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        className: \"shadow-lg border-0\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                className: \"pb-3 sm:pb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                    className: \"text-lg sm:text-xl\",\n                                    children: \"Client Census Insights\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                    lineNumber: 325,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                lineNumber: 324,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                className: \"p-3 sm:p-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3 sm:space-y-4\",\n                                    children: filteredEmployers.map((employer)=>{\n                                        const canAccess = canViewReport(employer.id);\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                            className: \"border-l-4 border-l-blue-500 transition-all duration-200 cursor-pointer \".concat(canAccess ? \"hover:shadow-md hover:-translate-y-0.5\" : \"opacity-60 hover:opacity-80 cursor-not-allowed\", \" \").concat(!canAccess ? \"relative\" : \"\"),\n                                            onClick: ()=>handleTileClick(employer.id, employer.companyName),\n                                            children: [\n                                                !canAccess && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute inset-0 bg-gray-200/50 backdrop-blur-[1px] rounded-lg flex items-center justify-center z-10\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-white p-3 rounded-lg shadow-lg flex items-center space-x-2 border border-amber-200\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_DollarSign_Lock_Mail_Plus_Search_Share2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                className: \"h-4 w-4 text-amber-600\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                lineNumber: 345,\n                                                                columnNumber: 27\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm font-medium text-amber-800\",\n                                                                children: \"Upgrade to view\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                lineNumber: 346,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                        lineNumber: 344,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                    lineNumber: 343,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                                    className: \"p-4 sm:p-5\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex flex-col sm:flex-row justify-between items-start mb-3 sm:mb-4 gap-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                            className: \"text-lg sm:text-xl font-semibold text-gray-900 mb-1\",\n                                                                            children: employer.companyName\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                            lineNumber: 354,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-xs sm:text-sm text-gray-500\",\n                                                                            children: [\n                                                                                \"Uploaded: \",\n                                                                                new Date(employer.uploadDate).toLocaleDateString()\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                            lineNumber: 357,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                    lineNumber: 353,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex space-x-2 w-full sm:w-auto\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                        size: \"sm\",\n                                                                        variant: \"outline\",\n                                                                        onClick: (e)=>{\n                                                                            e.stopPropagation();\n                                                                            if (canAccess) {\n                                                                                handleShareInsight(employer.companyName);\n                                                                            } else {\n                                                                                navigate(\"/pricing\");\n                                                                            }\n                                                                        },\n                                                                        className: \"flex-1 sm:flex-none\",\n                                                                        disabled: !canAccess,\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_DollarSign_Lock_Mail_Plus_Search_Share2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                                className: \"h-3 w-3 sm:h-4 sm:w-4 sm:mr-1\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                                lineNumber: 376,\n                                                                                columnNumber: 29\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"hidden sm:inline\",\n                                                                                children: \"Share\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                                lineNumber: 377,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                        lineNumber: 362,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                    lineNumber: 361,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                            lineNumber: 352,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"grid grid-cols-2 lg:grid-cols-4 gap-2 sm:gap-3 mb-3 sm:mb-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-center p-2 sm:p-3 bg-gradient-to-br from-blue-50 to-blue-100 rounded-lg hover:scale-105 transition-transform duration-200\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-xs sm:text-sm text-blue-600 font-medium\",\n                                                                            children: \"Employees\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                            lineNumber: 384,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm sm:text-lg font-bold text-blue-900\",\n                                                                            children: employer.employees\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                            lineNumber: 385,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                    lineNumber: 383,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-center p-2 sm:p-3 bg-gradient-to-br from-emerald-50 to-emerald-100 rounded-lg hover:scale-105 transition-transform duration-200\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-xs sm:text-sm text-emerald-600 font-medium\",\n                                                                            children: \"Avg Age\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                            lineNumber: 388,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm sm:text-lg font-bold text-emerald-900\",\n                                                                            children: employer.averageAge\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                            lineNumber: 389,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                    lineNumber: 387,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-center p-2 sm:p-3 bg-gradient-to-br from-orange-50 to-orange-100 rounded-lg hover:scale-105 transition-transform duration-200\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-xs sm:text-sm text-orange-600 font-medium\",\n                                                                            children: \"Savings\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                            lineNumber: 392,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm sm:text-lg font-bold text-orange-900\",\n                                                                            children: employer.potentialSavings\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                            lineNumber: 393,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                    lineNumber: 391,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-center p-2 sm:p-3 bg-gradient-to-br from-purple-50 to-purple-100 rounded-lg hover:scale-105 transition-transform duration-200\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-xs sm:text-sm text-purple-600 font-medium\",\n                                                                            children: \"Risk Score\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                            lineNumber: 396,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm sm:text-lg font-bold text-purple-900\",\n                                                                            children: [\n                                                                                employer.riskScore,\n                                                                                \"/10\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                            lineNumber: 397,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                    lineNumber: 395,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                            lineNumber: 382,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex flex-col sm:flex-row justify-between items-start sm:items-center gap-2 sm:gap-0\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-sm\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-gray-600\",\n                                                                            children: \"Suggested Plan: \"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                            lineNumber: 403,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"font-medium text-gray-900\",\n                                                                            children: employer.planType\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                            lineNumber: 404,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                    lineNumber: 402,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"px-2 py-1 rounded-full text-xs font-medium \".concat(employer.status === \"analyzed\" ? \"bg-green-100 text-green-800\" : \"bg-yellow-100 text-yellow-800\"),\n                                                                    children: employer.status === \"analyzed\" ? \"✅ Analyzed\" : \"⏳ Processing\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                    lineNumber: 406,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                            lineNumber: 401,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                    lineNumber: 351,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, employer.id, true, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                            lineNumber: 333,\n                                            columnNumber: 19\n                                        }, undefined);\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                    lineNumber: 328,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                lineNumber: 327,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                        lineNumber: 323,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        className: \"mt-6 bg-gradient-to-r from-purple-100 to-blue-100 border-0 shadow-lg\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                            className: \"p-4 sm:p-6 text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg sm:text-xl font-bold text-gray-900 mb-2\",\n                                    children: \"\\uD83D\\uDE80 Grow Your Network\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                    lineNumber: 425,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-700 mb-4 text-sm sm:text-base\",\n                                    children: \"Share BenOsphere with other brokers and get rewards for every signup\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                    lineNumber: 428,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col sm:flex-row justify-center space-y-2 sm:space-y-0 sm:space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            className: \"bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white transition-all duration-200 hover:scale-105\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_DollarSign_Lock_Mail_Plus_Search_Share2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                    className: \"h-4 w-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                    lineNumber: 433,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                \"Refer a Broker\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                            lineNumber: 432,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            variant: \"outline\",\n                                            className: \"hover:bg-white/50\",\n                                            children: \"View Referral Rewards\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                            lineNumber: 436,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                    lineNumber: 431,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                            lineNumber: 424,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                        lineNumber: 423,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                lineNumber: 151,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n        lineNumber: 134,\n        columnNumber: 5\n    }, undefined);\n};\n_s(BrokerDashboard, \"wbB67LnpqPwmSsoqj/2DceSxagE=\", false, function() {\n    return [\n        _lib_react_router_dom__WEBPACK_IMPORTED_MODULE_6__.useNavigate,\n        _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_11__.useToast,\n        _redux_hooks__WEBPACK_IMPORTED_MODULE_12__.useAppSelector,\n        _hooks_usePlanRestrictions__WEBPACK_IMPORTED_MODULE_10__.usePlanRestrictions,\n        _context_CensusContext__WEBPACK_IMPORTED_MODULE_13__.useCensus\n    ];\n});\n_c = BrokerDashboard;\n/* harmony default export */ __webpack_exports__[\"default\"] = (BrokerDashboard);\nvar _c;\n$RefreshReg$(_c, \"BrokerDashboard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/census/public/BrokerDashboard.tsx\n"));

/***/ })

});