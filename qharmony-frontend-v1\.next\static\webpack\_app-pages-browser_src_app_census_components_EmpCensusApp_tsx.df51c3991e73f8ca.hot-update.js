"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("_app-pages-browser_src_app_census_components_EmpCensusApp_tsx",{

/***/ "(app-pages-browser)/./src/app/census/services/censusApi.ts":
/*!**********************************************!*\
  !*** ./src/app/census/services/censusApi.ts ***!
  \**********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! axios */ \"(app-pages-browser)/./node_modules/axios/lib/axios.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n// Census API uses the Python backend (chatbot service) instead of the main Node.js backend\nconst CENSUS_API_BASE_URL = \"http://127.0.0.1:8000\" || 0;\nclass CensusApiService {\n    /**\n   * Upload and process census file using the Python backend (chatbot service)\n   */ static async uploadCensusFile(file) {\n        let returnDataframe = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;\n        try {\n            var _response_data, _response_data1, _response_data_data_summary, _response_data_data, _response_data2;\n            const formData = new FormData();\n            formData.append(\"file\", file);\n            // Build full URL for census API (Python backend)\n            const url = \"\".concat(CENSUS_API_BASE_URL, \"/api/census/processor/v1?return_dataframe=\").concat(returnDataframe);\n            console.log(\"\\uD83D\\uDCE4 Uploading census file: \".concat(file.name, \" (\").concat((file.size / 1024 / 1024).toFixed(2), \" MB)\"));\n            console.log(\"\\uD83D\\uDD17 Census API URL: \".concat(url));\n            console.log(\"\\uD83D\\uDCCB Request details:\", {\n                method: \"POST\",\n                url: url,\n                fileSize: file.size,\n                fileName: file.name,\n                returnDataframe: returnDataframe\n            });\n            // Use axios directly for census API calls to Python backend\n            // Note: Don't set Content-Type manually - let axios set it automatically for FormData\n            const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(url, formData, {\n                timeout: 300000\n            });\n            console.log(\"\\uD83D\\uDCCA Response received:\", {\n                status: response.status,\n                statusText: response.statusText,\n                success: (_response_data = response.data) === null || _response_data === void 0 ? void 0 : _response_data.success,\n                hasData: !!((_response_data1 = response.data) === null || _response_data1 === void 0 ? void 0 : _response_data1.data),\n                totalEmployees: (_response_data2 = response.data) === null || _response_data2 === void 0 ? void 0 : (_response_data_data = _response_data2.data) === null || _response_data_data === void 0 ? void 0 : (_response_data_data_summary = _response_data_data.summary) === null || _response_data_data_summary === void 0 ? void 0 : _response_data_data_summary.total_employees\n            });\n            if (response.status === 200 && response.data.success) {\n                var _response_data_data_summary1, _response_data_data1, _response_data_data2;\n                // Log the actual response structure for debugging\n                console.log(\"✅ Census processing completed successfully\");\n                console.log(\"\\uD83D\\uDCCA Full response structure:\", response.data);\n                // Try to extract employee count from various possible locations\n                const employeeCount = ((_response_data_data1 = response.data.data) === null || _response_data_data1 === void 0 ? void 0 : (_response_data_data_summary1 = _response_data_data1.summary) === null || _response_data_data_summary1 === void 0 ? void 0 : _response_data_data_summary1.total_employees) || ((_response_data_data2 = response.data.data) === null || _response_data_data2 === void 0 ? void 0 : _response_data_data2.total_employees) || response.data.total_employees || \"unknown\";\n                console.log(\"\\uD83D\\uDC65 Processed employees: \".concat(employeeCount));\n                return response.data;\n            } else {\n                throw new Error(response.data.message || \"Census processing failed\");\n            }\n        } catch (error) {\n            var _error_response, _error_response1, _error_response2, _error_response3, _error_response4, _error_response5, _error_response_data, _error_response6, _error_message;\n            console.error(\"❌ Census upload failed:\", error);\n            console.error(\"\\uD83D\\uDCCB Error details:\", {\n                message: error.message,\n                code: error.code,\n                status: (_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status,\n                statusText: (_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : _error_response1.statusText,\n                responseData: (_error_response2 = error.response) === null || _error_response2 === void 0 ? void 0 : _error_response2.data\n            });\n            // Provide more specific error messages\n            if (error.code === \"ECONNREFUSED\") {\n                throw new Error(\"Census API service is not running. Please start the Python backend on port 8000.\");\n            } else if (((_error_response3 = error.response) === null || _error_response3 === void 0 ? void 0 : _error_response3.status) === 404) {\n                throw new Error(\"Census API endpoint not found. Please check if the Python backend is running.\");\n            } else if (((_error_response4 = error.response) === null || _error_response4 === void 0 ? void 0 : _error_response4.status) === 413) {\n                throw new Error(\"File too large. Maximum file size is 50MB.\");\n            } else if (((_error_response5 = error.response) === null || _error_response5 === void 0 ? void 0 : _error_response5.status) === 500) {\n                var _error_response_data1, _error_response7;\n                const serverError = ((_error_response7 = error.response) === null || _error_response7 === void 0 ? void 0 : (_error_response_data1 = _error_response7.data) === null || _error_response_data1 === void 0 ? void 0 : _error_response_data1.message) || \"Internal server error during census processing\";\n                throw new Error(\"Server error: \".concat(serverError));\n            } else if ((_error_response6 = error.response) === null || _error_response6 === void 0 ? void 0 : (_error_response_data = _error_response6.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) {\n                throw new Error(error.response.data.message);\n            } else if ((_error_message = error.message) === null || _error_message === void 0 ? void 0 : _error_message.includes(\"undefined\")) {\n                var _error_response8;\n                // Handle response structure mismatch\n                console.log(\"\\uD83D\\uDD0D Response structure debugging:\", (_error_response8 = error.response) === null || _error_response8 === void 0 ? void 0 : _error_response8.data);\n                throw new Error(\"Response structure mismatch - check console for details\");\n            } else {\n                throw new Error(error.message || \"Failed to upload census file\");\n            }\n        }\n    }\n    /**\n   * Get processed census data by company/report ID\n   * Note: This would be implemented when backend supports data persistence\n   */ static async getCensusData(companyId) {\n        try {\n            // For now, this would use the Python backend when persistence is implemented\n            const url = \"\".concat(CENSUS_API_BASE_URL, \"/api/census/reports/\").concat(companyId);\n            const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(url);\n            return response.data;\n        } catch (error) {\n            console.error(\"❌ Failed to fetch census data:\", error);\n            throw new Error(error.message || \"Failed to fetch census data\");\n        }\n    }\n    /**\n   * Get broker dashboard data (list of processed companies)\n   * Note: This would be implemented when backend supports data persistence\n   */ static async getBrokerDashboard() {\n        try {\n            // For now, this would use the Python backend when persistence is implemented\n            const url = \"\".concat(CENSUS_API_BASE_URL, \"/api/census/broker/dashboard\");\n            const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(url);\n            return response.data || [];\n        } catch (error) {\n            console.error(\"❌ Failed to fetch broker dashboard:\", error);\n            // Return empty array as fallback - frontend state management handles this\n            return [];\n        }\n    }\n    /**\n   * Transform API employee data to frontend format\n   */ static transformEmployeeData(apiEmployee) {\n        var _apiEmployee_recommended_plan, _apiEmployee_recommended_plan1;\n        // Parse top 3 plans if available\n        let top3Plans = [];\n        try {\n            if (apiEmployee.top_3_available_plans) {\n                top3Plans = JSON.parse(apiEmployee.top_3_available_plans);\n            }\n        } catch (e) {\n            console.warn(\"Failed to parse top_3_available_plans:\", e);\n        }\n        // Map risk level based on plan confidence\n        const getRiskLevel = (confidence)=>{\n            if (confidence >= 0.8) return \"Low\";\n            if (confidence >= 0.6) return \"Medium\";\n            return \"High\";\n        };\n        return {\n            name: apiEmployee.name,\n            department: \"Dept \".concat(apiEmployee.dept_count),\n            risk: getRiskLevel(apiEmployee.plan_confidence),\n            age: apiEmployee.age,\n            coverage: apiEmployee.predicted_plan_type,\n            hasDependents: apiEmployee.marital_status.toLowerCase() === \"married\",\n            salary: apiEmployee.income_tier,\n            currentPlan: {\n                medical: ((_apiEmployee_recommended_plan = apiEmployee.recommended_plan) === null || _apiEmployee_recommended_plan === void 0 ? void 0 : _apiEmployee_recommended_plan.name) || \"Not Enrolled\",\n                dental: apiEmployee.predicted_benefits.includes(\"Dental\") ? \"Basic\" : \"Not Enrolled\",\n                vision: apiEmployee.predicted_benefits.includes(\"Vision\") ? \"Basic\" : \"Not Enrolled\",\n                life: apiEmployee.predicted_benefits.includes(\"Term Life\") ? \"1x Salary\" : \"None\",\n                disability: apiEmployee.predicted_benefits.includes(\"LTD\") ? \"Basic\" : \"None\"\n            },\n            coverageGaps: [],\n            insights: [\n                apiEmployee.plan_reason\n            ],\n            upsells: [],\n            planFitSummary: {\n                recommendedPlan: ((_apiEmployee_recommended_plan1 = apiEmployee.recommended_plan) === null || _apiEmployee_recommended_plan1 === void 0 ? void 0 : _apiEmployee_recommended_plan1.name) || \"No recommendation\",\n                insight: apiEmployee.plan_reason\n            },\n            // Additional API data\n            apiData: {\n                employee_id: apiEmployee.employee_id,\n                zipcode: apiEmployee.zipcode,\n                city: apiEmployee.city,\n                state: apiEmployee.state,\n                recommended_plan: apiEmployee.recommended_plan,\n                benefits_coverage: apiEmployee.benefits_coverage,\n                top_3_plans: top3Plans,\n                marketplace_plans_available: apiEmployee.marketplace_plans_available,\n                plan_count: apiEmployee.plan_count\n            }\n        };\n    }\n    /**\n   * Transform API response to frontend company data format\n   */ static transformCompanyData(apiResponse) {\n        let companyId = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : \"1\";\n        var _statistics_demographics;\n        // Handle flexible response structure\n        const data = apiResponse.data || apiResponse;\n        const summary = data.summary || {};\n        const statistics = data.statistics || {};\n        const employees = data.employees || [];\n        console.log(\"\\uD83D\\uDD04 Transforming company data:\", {\n            hasData: !!data,\n            hasSummary: !!summary,\n            hasStatistics: !!statistics,\n            employeeCount: employees.length,\n            summaryKeys: Object.keys(summary),\n            statisticsKeys: Object.keys(statistics)\n        });\n        // Calculate potential savings (simplified calculation)\n        const avgPremium = employees.filter((emp)=>emp.recommended_plan).reduce((sum, emp)=>{\n            var _emp_recommended_plan;\n            return sum + (((_emp_recommended_plan = emp.recommended_plan) === null || _emp_recommended_plan === void 0 ? void 0 : _emp_recommended_plan.premium) || 0);\n        }, 0) / (employees.length || 1);\n        const potentialSavings = Math.round(avgPremium * employees.length * 0.15); // Assume 15% savings\n        // Determine primary plan type\n        const planTypes = Object.keys(statistics.health_plans.plan_type_distribution);\n        const primaryPlanType = planTypes.reduce((a, b)=>statistics.health_plans.plan_type_distribution[a] > statistics.health_plans.plan_type_distribution[b] ? a : b);\n        return {\n            companyName: \"Company \".concat(companyId),\n            employees: summary.total_employees || employees.length || 0,\n            averageAge: Math.round(((_statistics_demographics = statistics.demographics) === null || _statistics_demographics === void 0 ? void 0 : _statistics_demographics.average_age) || 35),\n            dependents: employees.filter((emp)=>{\n                var _emp_marital_status;\n                return ((_emp_marital_status = emp.marital_status) === null || _emp_marital_status === void 0 ? void 0 : _emp_marital_status.toLowerCase()) === \"married\";\n            }).length / (employees.length || 1),\n            planType: primaryPlanType,\n            potentialSavings: \"$\".concat(potentialSavings.toLocaleString()),\n            riskScore: \"\".concat(((summary.data_quality_score || 0.8) * 10).toFixed(1), \"/10\"),\n            uploadDate: new Date().toISOString().split(\"T\")[0],\n            industry: \"Technology\",\n            currentSpend: \"$\".concat(Math.round(avgPremium * employees.length).toLocaleString(), \"/month\"),\n            suggestedPlan: \"\".concat(primaryPlanType, \" with Enhanced Coverage\"),\n            planFitSummary: {\n                silverGoldPPO: Math.round((statistics.health_plans.plan_type_distribution[\"PPO\"] || 0) / employees.length * 100),\n                hdhp: Math.round((statistics.health_plans.plan_type_distribution[\"HDHP\"] || 0) / employees.length * 100),\n                familyPPO: Math.round(employees.filter((emp)=>{\n                    var _emp_marital_status;\n                    return ((_emp_marital_status = emp.marital_status) === null || _emp_marital_status === void 0 ? void 0 : _emp_marital_status.toLowerCase()) === \"married\";\n                }).length / (employees.length || 1) * 100),\n                insight: \"Based on \".concat(employees.length, \" employees with \").concat(statistics.demographics.average_age.toFixed(1), \" average age\")\n            },\n            employeeProfiles: employees.map((emp)=>this.transformEmployeeData(emp)),\n            // Generate mock upsell opportunities based on company data\n            upsellOpportunities: [\n                {\n                    category: \"Enhanced Coverage\",\n                    description: \"Upgrade \".concat(Math.round(employees.length * 0.3), \" employees to premium plans\"),\n                    savings: \"+$\".concat(Math.round(potentialSavings * 0.1).toLocaleString(), \"/month\"),\n                    confidence: \"85%\",\n                    priority: \"High\"\n                },\n                {\n                    category: \"Wellness Programs\",\n                    description: \"Preventive care initiatives for healthier workforce\",\n                    savings: \"+$\".concat(Math.round(potentialSavings * 0.05).toLocaleString(), \"/month\"),\n                    confidence: \"72%\",\n                    priority: \"Medium\"\n                }\n            ],\n            // Store original API data for reference\n            apiData: apiResponse.data\n        };\n    }\n}\n/* harmony default export */ __webpack_exports__[\"default\"] = (CensusApiService);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/census/services/censusApi.ts\n"));

/***/ })

});