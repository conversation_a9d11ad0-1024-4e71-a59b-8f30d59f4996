"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("_app-pages-browser_src_app_census_components_EmpCensusApp_tsx",{

/***/ "(app-pages-browser)/./src/app/census/services/censusApi.ts":
/*!**********************************************!*\
  !*** ./src/app/census/services/censusApi.ts ***!
  \**********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! axios */ \"(app-pages-browser)/./node_modules/axios/lib/axios.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n// Census API uses the Python backend (chatbot service) instead of the main Node.js backend\nconst CENSUS_API_BASE_URL = \"http://127.0.0.1:8000\" || 0;\nclass CensusApiService {\n    /**\n   * Upload and process census file using the Python backend (chatbot service)\n   */ static async uploadCensusFile(file) {\n        let returnDataframe = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;\n        try {\n            const formData = new FormData();\n            formData.append(\"file\", file);\n            // Build full URL for census API (Python backend)\n            const url = \"\".concat(CENSUS_API_BASE_URL, \"/api/census/processor/v1?return_dataframe=\").concat(returnDataframe);\n            console.log(\"\\uD83D\\uDCE4 Uploading census file: \".concat(file.name, \" (\").concat((file.size / 1024 / 1024).toFixed(2), \" MB)\"));\n            console.log(\"\\uD83D\\uDD17 Census API URL: \".concat(url));\n            console.log(\"\\uD83D\\uDCCB Request details:\", {\n                method: \"POST\",\n                url: url,\n                fileSize: file.size,\n                fileName: file.name,\n                returnDataframe: returnDataframe\n            });\n            // Use axios directly for census API calls to Python backend\n            // Note: Don't set Content-Type manually - let axios set it automatically for FormData\n            const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(url, formData, {\n                timeout: 300000\n            });\n            if (response.status === 200 && response.data.success) {\n                console.log(\"✅ Census processing completed: \".concat(response.data.data.summary.total_employees, \" employees\"));\n                return response.data;\n            } else {\n                throw new Error(response.data.message || \"Census processing failed\");\n            }\n        } catch (error) {\n            var _error_response, _error_response1;\n            console.error(\"❌ Census upload failed:\", error);\n            // Provide more specific error messages\n            if (error.code === \"ECONNREFUSED\") {\n                throw new Error(\"Census API service is not running. Please start the Python backend on port 8000.\");\n            } else if (((_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status) === 404) {\n                throw new Error(\"Census API endpoint not found. Please check if the Python backend is running.\");\n            } else if (((_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : _error_response1.status) === 413) {\n                throw new Error(\"File too large. Maximum file size is 50MB.\");\n            } else {\n                throw new Error(error.message || \"Failed to upload census file\");\n            }\n        }\n    }\n    /**\n   * Get processed census data by company/report ID\n   * Note: This would be implemented when backend supports data persistence\n   */ static async getCensusData(companyId) {\n        try {\n            // For now, this would use the Python backend when persistence is implemented\n            const url = \"\".concat(CENSUS_API_BASE_URL, \"/api/census/reports/\").concat(companyId);\n            const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(url);\n            return response.data;\n        } catch (error) {\n            console.error(\"❌ Failed to fetch census data:\", error);\n            throw new Error(error.message || \"Failed to fetch census data\");\n        }\n    }\n    /**\n   * Get broker dashboard data (list of processed companies)\n   * Note: This would be implemented when backend supports data persistence\n   */ static async getBrokerDashboard() {\n        try {\n            // For now, this would use the Python backend when persistence is implemented\n            const url = \"\".concat(CENSUS_API_BASE_URL, \"/api/census/broker/dashboard\");\n            const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(url);\n            return response.data || [];\n        } catch (error) {\n            console.error(\"❌ Failed to fetch broker dashboard:\", error);\n            // Return empty array as fallback - frontend state management handles this\n            return [];\n        }\n    }\n    /**\n   * Transform API employee data to frontend format\n   */ static transformEmployeeData(apiEmployee) {\n        var _apiEmployee_recommended_plan, _apiEmployee_recommended_plan1;\n        // Parse top 3 plans if available\n        let top3Plans = [];\n        try {\n            if (apiEmployee.top_3_available_plans) {\n                top3Plans = JSON.parse(apiEmployee.top_3_available_plans);\n            }\n        } catch (e) {\n            console.warn(\"Failed to parse top_3_available_plans:\", e);\n        }\n        // Map risk level based on plan confidence\n        const getRiskLevel = (confidence)=>{\n            if (confidence >= 0.8) return \"Low\";\n            if (confidence >= 0.6) return \"Medium\";\n            return \"High\";\n        };\n        return {\n            name: apiEmployee.name,\n            department: \"Dept \".concat(apiEmployee.dept_count),\n            risk: getRiskLevel(apiEmployee.plan_confidence),\n            age: apiEmployee.age,\n            coverage: apiEmployee.predicted_plan_type,\n            hasDependents: apiEmployee.marital_status.toLowerCase() === \"married\",\n            salary: apiEmployee.income_tier,\n            currentPlan: {\n                medical: ((_apiEmployee_recommended_plan = apiEmployee.recommended_plan) === null || _apiEmployee_recommended_plan === void 0 ? void 0 : _apiEmployee_recommended_plan.name) || \"Not Enrolled\",\n                dental: apiEmployee.predicted_benefits.includes(\"Dental\") ? \"Basic\" : \"Not Enrolled\",\n                vision: apiEmployee.predicted_benefits.includes(\"Vision\") ? \"Basic\" : \"Not Enrolled\",\n                life: apiEmployee.predicted_benefits.includes(\"Term Life\") ? \"1x Salary\" : \"None\",\n                disability: apiEmployee.predicted_benefits.includes(\"LTD\") ? \"Basic\" : \"None\"\n            },\n            coverageGaps: [],\n            insights: [\n                apiEmployee.plan_reason\n            ],\n            upsells: [],\n            planFitSummary: {\n                recommendedPlan: ((_apiEmployee_recommended_plan1 = apiEmployee.recommended_plan) === null || _apiEmployee_recommended_plan1 === void 0 ? void 0 : _apiEmployee_recommended_plan1.name) || \"No recommendation\",\n                insight: apiEmployee.plan_reason\n            },\n            // Additional API data\n            apiData: {\n                employee_id: apiEmployee.employee_id,\n                zipcode: apiEmployee.zipcode,\n                city: apiEmployee.city,\n                state: apiEmployee.state,\n                recommended_plan: apiEmployee.recommended_plan,\n                benefits_coverage: apiEmployee.benefits_coverage,\n                top_3_plans: top3Plans,\n                marketplace_plans_available: apiEmployee.marketplace_plans_available,\n                plan_count: apiEmployee.plan_count\n            }\n        };\n    }\n    /**\n   * Transform API response to frontend company data format\n   */ static transformCompanyData(apiResponse) {\n        let companyId = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : \"1\";\n        const { summary, statistics, employees } = apiResponse.data;\n        // Calculate potential savings (simplified calculation)\n        const avgPremium = employees.filter((emp)=>emp.recommended_plan).reduce((sum, emp)=>{\n            var _emp_recommended_plan;\n            return sum + (((_emp_recommended_plan = emp.recommended_plan) === null || _emp_recommended_plan === void 0 ? void 0 : _emp_recommended_plan.premium) || 0);\n        }, 0) / employees.length;\n        const potentialSavings = Math.round(avgPremium * employees.length * 0.15); // Assume 15% savings\n        // Determine primary plan type\n        const planTypes = Object.keys(statistics.health_plans.plan_type_distribution);\n        const primaryPlanType = planTypes.reduce((a, b)=>statistics.health_plans.plan_type_distribution[a] > statistics.health_plans.plan_type_distribution[b] ? a : b);\n        return {\n            companyName: \"Company \".concat(companyId),\n            employees: summary.total_employees,\n            averageAge: Math.round(statistics.demographics.average_age),\n            dependents: employees.filter((emp)=>emp.marital_status.toLowerCase() === \"married\").length / employees.length,\n            planType: primaryPlanType,\n            potentialSavings: \"$\".concat(potentialSavings.toLocaleString()),\n            riskScore: \"\".concat((summary.data_quality_score * 10).toFixed(1), \"/10\"),\n            uploadDate: new Date().toISOString().split(\"T\")[0],\n            industry: \"Technology\",\n            currentSpend: \"$\".concat(Math.round(avgPremium * employees.length).toLocaleString(), \"/month\"),\n            suggestedPlan: \"\".concat(primaryPlanType, \" with Enhanced Coverage\"),\n            planFitSummary: {\n                silverGoldPPO: Math.round((statistics.health_plans.plan_type_distribution[\"PPO\"] || 0) / employees.length * 100),\n                hdhp: Math.round((statistics.health_plans.plan_type_distribution[\"HDHP\"] || 0) / employees.length * 100),\n                familyPPO: Math.round(employees.filter((emp)=>emp.marital_status.toLowerCase() === \"married\").length / employees.length * 100),\n                insight: \"Based on \".concat(employees.length, \" employees with \").concat(statistics.demographics.average_age.toFixed(1), \" average age\")\n            },\n            employeeProfiles: employees.map((emp)=>this.transformEmployeeData(emp)),\n            // Generate mock upsell opportunities based on company data\n            upsellOpportunities: [\n                {\n                    category: \"Enhanced Coverage\",\n                    description: \"Upgrade \".concat(Math.round(employees.length * 0.3), \" employees to premium plans\"),\n                    savings: \"+$\".concat(Math.round(potentialSavings * 0.1).toLocaleString(), \"/month\"),\n                    confidence: \"85%\",\n                    priority: \"High\"\n                },\n                {\n                    category: \"Wellness Programs\",\n                    description: \"Preventive care initiatives for healthier workforce\",\n                    savings: \"+$\".concat(Math.round(potentialSavings * 0.05).toLocaleString(), \"/month\"),\n                    confidence: \"72%\",\n                    priority: \"Medium\"\n                }\n            ],\n            // Store original API data for reference\n            apiData: apiResponse.data\n        };\n    }\n}\n/* harmony default export */ __webpack_exports__[\"default\"] = (CensusApiService);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/census/services/censusApi.ts\n"));

/***/ })

});