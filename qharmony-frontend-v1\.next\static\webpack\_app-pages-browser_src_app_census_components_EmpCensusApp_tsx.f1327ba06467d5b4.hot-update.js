"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("_app-pages-browser_src_app_census_components_EmpCensusApp_tsx",{

/***/ "(app-pages-browser)/./src/app/census/services/censusApi.ts":
/*!**********************************************!*\
  !*** ./src/app/census/services/censusApi.ts ***!
  \**********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! axios */ \"(app-pages-browser)/./node_modules/axios/lib/axios.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n// Census API uses the Python backend (chatbot service) instead of the main Node.js backend\nconst CENSUS_API_BASE_URL = \"http://127.0.0.1:8000\" || 0;\nclass CensusApiService {\n    /**\n   * Upload and process census file using the Python backend (chatbot service)\n   */ static async uploadCensusFile(file) {\n        let returnDataframe = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;\n        try {\n            var _response_data, _response_data1, _response_data2, _response_data_data, _response_data3, _response_data_data1, _response_data4;\n            const formData = new FormData();\n            formData.append(\"file\", file);\n            // Build full URL for census API (Python backend)\n            const url = \"\".concat(CENSUS_API_BASE_URL, \"/api/census/processor/v1?return_dataframe=\").concat(returnDataframe);\n            console.log(\"\\uD83D\\uDCE4 Uploading census file: \".concat(file.name, \" (\").concat((file.size / 1024 / 1024).toFixed(2), \" MB)\"));\n            console.log(\"\\uD83D\\uDD17 Census API URL: \".concat(url));\n            console.log(\"\\uD83D\\uDCCB Request details:\", {\n                method: \"POST\",\n                url: url,\n                fileSize: file.size,\n                fileName: file.name,\n                returnDataframe: returnDataframe\n            });\n            // Use axios directly for census API calls to Python backend\n            // Note: Don't set Content-Type manually - let axios set it automatically for FormData\n            const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(url, formData, {\n                timeout: 300000\n            });\n            console.log(\"\\uD83D\\uDCCA Response received:\", {\n                status: response.status,\n                statusText: response.statusText,\n                success: (_response_data = response.data) === null || _response_data === void 0 ? void 0 : _response_data.success,\n                hasData: !!((_response_data1 = response.data) === null || _response_data1 === void 0 ? void 0 : _response_data1.data),\n                dataKeys: ((_response_data2 = response.data) === null || _response_data2 === void 0 ? void 0 : _response_data2.data) ? Object.keys(response.data.data) : \"no data\",\n                hasSummary: !!((_response_data3 = response.data) === null || _response_data3 === void 0 ? void 0 : (_response_data_data = _response_data3.data) === null || _response_data_data === void 0 ? void 0 : _response_data_data.summary),\n                summaryKeys: ((_response_data4 = response.data) === null || _response_data4 === void 0 ? void 0 : (_response_data_data1 = _response_data4.data) === null || _response_data_data1 === void 0 ? void 0 : _response_data_data1.summary) ? Object.keys(response.data.data.summary) : \"no summary\"\n            });\n            if (response.status === 200) {\n                if (response.data.success) {\n                    var _response_data_data_summary, _response_data_data2, _response_data_data3;\n                    // Log the actual response structure for debugging\n                    console.log(\"✅ Census processing completed successfully\");\n                    console.log(\"\\uD83D\\uDCCA Full response structure:\", response.data);\n                    // Try to extract employee count from various possible locations\n                    const employeeCount = ((_response_data_data2 = response.data.data) === null || _response_data_data2 === void 0 ? void 0 : (_response_data_data_summary = _response_data_data2.summary) === null || _response_data_data_summary === void 0 ? void 0 : _response_data_data_summary.total_employees) || ((_response_data_data3 = response.data.data) === null || _response_data_data3 === void 0 ? void 0 : _response_data_data3.total_employees) || response.data.total_employees || \"unknown\";\n                    console.log(\"\\uD83D\\uDC65 Processed employees: \".concat(employeeCount));\n                    return response.data;\n                } else {\n                    // Backend returned 200 but success=false\n                    console.error(\"❌ Backend processing failed:\", response.data);\n                    throw new Error(response.data.message || \"Census processing failed on backend\");\n                }\n            } else {\n                throw new Error(\"HTTP \".concat(response.status, \": \").concat(response.statusText));\n            }\n        } catch (error) {\n            var _error_response, _error_response1, _error_response2, _error_response3, _error_response4, _error_response5, _error_response_data, _error_response6, _error_message;\n            console.error(\"❌ Census upload failed:\", error);\n            console.error(\"\\uD83D\\uDCCB Error details:\", {\n                message: error.message,\n                code: error.code,\n                status: (_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status,\n                statusText: (_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : _error_response1.statusText,\n                responseData: (_error_response2 = error.response) === null || _error_response2 === void 0 ? void 0 : _error_response2.data\n            });\n            // Provide more specific error messages\n            if (error.code === \"ECONNREFUSED\") {\n                throw new Error(\"Census API service is not running. Please start the Python backend on port 8000.\");\n            } else if (((_error_response3 = error.response) === null || _error_response3 === void 0 ? void 0 : _error_response3.status) === 404) {\n                throw new Error(\"Census API endpoint not found. Please check if the Python backend is running.\");\n            } else if (((_error_response4 = error.response) === null || _error_response4 === void 0 ? void 0 : _error_response4.status) === 413) {\n                throw new Error(\"File too large. Maximum file size is 50MB.\");\n            } else if (((_error_response5 = error.response) === null || _error_response5 === void 0 ? void 0 : _error_response5.status) === 500) {\n                var _error_response_data1, _error_response7;\n                const serverError = ((_error_response7 = error.response) === null || _error_response7 === void 0 ? void 0 : (_error_response_data1 = _error_response7.data) === null || _error_response_data1 === void 0 ? void 0 : _error_response_data1.message) || \"Internal server error during census processing\";\n                throw new Error(\"Server error: \".concat(serverError));\n            } else if ((_error_response6 = error.response) === null || _error_response6 === void 0 ? void 0 : (_error_response_data = _error_response6.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) {\n                throw new Error(error.response.data.message);\n            } else if ((_error_message = error.message) === null || _error_message === void 0 ? void 0 : _error_message.includes(\"undefined\")) {\n                var _error_response8;\n                // Handle response structure mismatch\n                console.log(\"\\uD83D\\uDD0D Response structure debugging:\", (_error_response8 = error.response) === null || _error_response8 === void 0 ? void 0 : _error_response8.data);\n                throw new Error(\"Response structure mismatch - check console for details\");\n            } else {\n                throw new Error(error.message || \"Failed to upload census file\");\n            }\n        }\n    }\n    /**\n   * Get processed census data by company/report ID\n   * Note: This would be implemented when backend supports data persistence\n   */ static async getCensusData(companyId) {\n        try {\n            // For now, this would use the Python backend when persistence is implemented\n            const url = \"\".concat(CENSUS_API_BASE_URL, \"/api/census/reports/\").concat(companyId);\n            const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(url);\n            return response.data;\n        } catch (error) {\n            console.error(\"❌ Failed to fetch census data:\", error);\n            throw new Error(error.message || \"Failed to fetch census data\");\n        }\n    }\n    /**\n   * Get broker dashboard data (list of processed companies)\n   * Note: This would be implemented when backend supports data persistence\n   */ static async getBrokerDashboard() {\n        try {\n            // For now, this would use the Python backend when persistence is implemented\n            const url = \"\".concat(CENSUS_API_BASE_URL, \"/api/census/broker/dashboard\");\n            const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(url);\n            return response.data || [];\n        } catch (error) {\n            console.error(\"❌ Failed to fetch broker dashboard:\", error);\n            // Return empty array as fallback - frontend state management handles this\n            return [];\n        }\n    }\n    /**\n   * Transform API employee data to frontend format\n   */ static transformEmployeeData(apiEmployee) {\n        var _apiEmployee_recommended_plan, _apiEmployee_recommended_plan1;\n        // Parse top 3 plans if available\n        let top3Plans = [];\n        try {\n            if (apiEmployee.top_3_available_plans) {\n                top3Plans = JSON.parse(apiEmployee.top_3_available_plans);\n            }\n        } catch (e) {\n            console.warn(\"Failed to parse top_3_available_plans:\", e);\n        }\n        // Map risk level based on plan confidence\n        const getRiskLevel = (confidence)=>{\n            if (confidence >= 0.8) return \"Low\";\n            if (confidence >= 0.6) return \"Medium\";\n            return \"High\";\n        };\n        return {\n            name: apiEmployee.name,\n            department: \"Dept \".concat(apiEmployee.dept_count),\n            risk: getRiskLevel(apiEmployee.plan_confidence),\n            age: apiEmployee.age,\n            coverage: apiEmployee.predicted_plan_type,\n            hasDependents: apiEmployee.marital_status.toLowerCase() === \"married\",\n            salary: apiEmployee.income_tier,\n            currentPlan: {\n                medical: ((_apiEmployee_recommended_plan = apiEmployee.recommended_plan) === null || _apiEmployee_recommended_plan === void 0 ? void 0 : _apiEmployee_recommended_plan.name) || \"Not Enrolled\",\n                dental: apiEmployee.predicted_benefits.includes(\"Dental\") ? \"Basic\" : \"Not Enrolled\",\n                vision: apiEmployee.predicted_benefits.includes(\"Vision\") ? \"Basic\" : \"Not Enrolled\",\n                life: apiEmployee.predicted_benefits.includes(\"Term Life\") ? \"1x Salary\" : \"None\",\n                disability: apiEmployee.predicted_benefits.includes(\"LTD\") ? \"Basic\" : \"None\"\n            },\n            coverageGaps: [],\n            insights: [\n                apiEmployee.plan_reason\n            ],\n            upsells: [],\n            planFitSummary: {\n                recommendedPlan: ((_apiEmployee_recommended_plan1 = apiEmployee.recommended_plan) === null || _apiEmployee_recommended_plan1 === void 0 ? void 0 : _apiEmployee_recommended_plan1.name) || \"No recommendation\",\n                insight: apiEmployee.plan_reason\n            },\n            // Additional API data\n            apiData: {\n                employee_id: apiEmployee.employee_id,\n                zipcode: apiEmployee.zipcode,\n                city: apiEmployee.city,\n                state: apiEmployee.state,\n                recommended_plan: apiEmployee.recommended_plan,\n                benefits_coverage: apiEmployee.benefits_coverage,\n                top_3_plans: top3Plans,\n                marketplace_plans_available: apiEmployee.marketplace_plans_available,\n                plan_count: apiEmployee.plan_count\n            }\n        };\n    }\n    /**\n   * Transform API response to frontend company data format\n   */ static transformCompanyData(apiResponse) {\n        let companyId = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : \"1\";\n        var _statistics_demographics, _statistics_health_plans_plan_type_distribution, _statistics_health_plans, _statistics_health_plans_plan_type_distribution1, _statistics_health_plans1, _statistics_demographics1;\n        // Handle flexible response structure\n        const data = apiResponse.data || apiResponse;\n        const summary = data.summary || {};\n        const statistics = data.statistics || {};\n        const employees = data.employees || [];\n        console.log(\"\\uD83D\\uDD04 Transforming company data:\", {\n            hasData: !!data,\n            hasSummary: !!summary,\n            hasStatistics: !!statistics,\n            employeeCount: employees.length,\n            summaryKeys: Object.keys(summary),\n            statisticsKeys: Object.keys(statistics)\n        });\n        // Calculate potential savings (simplified calculation)\n        const avgPremium = employees.filter((emp)=>emp.recommended_plan).reduce((sum, emp)=>{\n            var _emp_recommended_plan;\n            return sum + (((_emp_recommended_plan = emp.recommended_plan) === null || _emp_recommended_plan === void 0 ? void 0 : _emp_recommended_plan.premium) || 0);\n        }, 0) / (employees.length || 1);\n        const potentialSavings = Math.round(avgPremium * employees.length * 0.15); // Assume 15% savings\n        // Determine primary plan type\n        const planTypes = Object.keys(statistics.health_plans.plan_type_distribution);\n        const primaryPlanType = planTypes.reduce((a, b)=>statistics.health_plans.plan_type_distribution[a] > statistics.health_plans.plan_type_distribution[b] ? a : b);\n        return {\n            companyName: \"Company \".concat(companyId),\n            employees: summary.total_employees || employees.length || 0,\n            averageAge: Math.round(((_statistics_demographics = statistics.demographics) === null || _statistics_demographics === void 0 ? void 0 : _statistics_demographics.average_age) || 35),\n            dependents: employees.filter((emp)=>{\n                var _emp_marital_status;\n                return ((_emp_marital_status = emp.marital_status) === null || _emp_marital_status === void 0 ? void 0 : _emp_marital_status.toLowerCase()) === \"married\";\n            }).length / (employees.length || 1),\n            planType: primaryPlanType,\n            potentialSavings: \"$\".concat(potentialSavings.toLocaleString()),\n            riskScore: \"\".concat(((summary.data_quality_score || 0.8) * 10).toFixed(1), \"/10\"),\n            uploadDate: new Date().toISOString().split(\"T\")[0],\n            industry: \"Technology\",\n            currentSpend: \"$\".concat(Math.round(avgPremium * employees.length).toLocaleString(), \"/month\"),\n            suggestedPlan: \"\".concat(primaryPlanType, \" with Enhanced Coverage\"),\n            planFitSummary: {\n                silverGoldPPO: Math.round((((_statistics_health_plans = statistics.health_plans) === null || _statistics_health_plans === void 0 ? void 0 : (_statistics_health_plans_plan_type_distribution = _statistics_health_plans.plan_type_distribution) === null || _statistics_health_plans_plan_type_distribution === void 0 ? void 0 : _statistics_health_plans_plan_type_distribution[\"PPO\"]) || 0) / (employees.length || 1) * 100),\n                hdhp: Math.round((((_statistics_health_plans1 = statistics.health_plans) === null || _statistics_health_plans1 === void 0 ? void 0 : (_statistics_health_plans_plan_type_distribution1 = _statistics_health_plans1.plan_type_distribution) === null || _statistics_health_plans_plan_type_distribution1 === void 0 ? void 0 : _statistics_health_plans_plan_type_distribution1[\"HDHP\"]) || 0) / (employees.length || 1) * 100),\n                familyPPO: Math.round(employees.filter((emp)=>{\n                    var _emp_marital_status;\n                    return ((_emp_marital_status = emp.marital_status) === null || _emp_marital_status === void 0 ? void 0 : _emp_marital_status.toLowerCase()) === \"married\";\n                }).length / (employees.length || 1) * 100),\n                insight: \"Based on \".concat(employees.length, \" employees with \").concat((((_statistics_demographics1 = statistics.demographics) === null || _statistics_demographics1 === void 0 ? void 0 : _statistics_demographics1.average_age) || 35).toFixed(1), \" average age\")\n            },\n            employeeProfiles: employees.map((emp)=>this.transformEmployeeData(emp)),\n            // Generate mock upsell opportunities based on company data\n            upsellOpportunities: [\n                {\n                    category: \"Enhanced Coverage\",\n                    description: \"Upgrade \".concat(Math.round(employees.length * 0.3), \" employees to premium plans\"),\n                    savings: \"+$\".concat(Math.round(potentialSavings * 0.1).toLocaleString(), \"/month\"),\n                    confidence: \"85%\",\n                    priority: \"High\"\n                },\n                {\n                    category: \"Wellness Programs\",\n                    description: \"Preventive care initiatives for healthier workforce\",\n                    savings: \"+$\".concat(Math.round(potentialSavings * 0.05).toLocaleString(), \"/month\"),\n                    confidence: \"72%\",\n                    priority: \"Medium\"\n                }\n            ],\n            // Store original API data for reference\n            apiData: apiResponse.data\n        };\n    }\n}\n/* harmony default export */ __webpack_exports__[\"default\"] = (CensusApiService);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/census/services/censusApi.ts\n"));

/***/ })

});