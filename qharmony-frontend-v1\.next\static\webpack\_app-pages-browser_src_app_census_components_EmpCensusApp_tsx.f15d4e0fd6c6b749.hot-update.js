"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("_app-pages-browser_src_app_census_components_EmpCensusApp_tsx",{

/***/ "(app-pages-browser)/./src/app/census/services/censusApi.ts":
/*!**********************************************!*\
  !*** ./src/app/census/services/censusApi.ts ***!
  \**********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! axios */ \"(app-pages-browser)/./node_modules/axios/lib/axios.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n// Census API uses the Python backend (chatbot service) instead of the main Node.js backend\nconst CENSUS_API_BASE_URL = \"http://127.0.0.1:8000\" || 0;\nclass CensusApiService {\n    /**\n   * Upload and process census file using the Python backend (chatbot service)\n   */ static async uploadCensusFile(file) {\n        let returnDataframe = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;\n        try {\n            var _response_data, _response_data1, _response_data2, _response_data_data, _response_data3, _response_data_data1, _response_data4;\n            const formData = new FormData();\n            formData.append(\"file\", file);\n            // Build full URL for census API (Python backend)\n            const url = \"\".concat(CENSUS_API_BASE_URL, \"/api/census/processor/v1?return_dataframe=\").concat(returnDataframe);\n            console.log(\"\\uD83D\\uDCE4 Uploading census file: \".concat(file.name, \" (\").concat((file.size / 1024 / 1024).toFixed(2), \" MB)\"));\n            console.log(\"\\uD83D\\uDD17 Census API URL: \".concat(url));\n            console.log(\"\\uD83D\\uDCCB Request details:\", {\n                method: \"POST\",\n                url: url,\n                fileSize: file.size,\n                fileName: file.name,\n                returnDataframe: returnDataframe\n            });\n            // Use axios directly for census API calls to Python backend\n            // Note: Don't set Content-Type manually - let axios set it automatically for FormData\n            const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(url, formData, {\n                timeout: 300000\n            });\n            console.log(\"\\uD83D\\uDCCA Response received:\", {\n                status: response.status,\n                statusText: response.statusText,\n                success: (_response_data = response.data) === null || _response_data === void 0 ? void 0 : _response_data.success,\n                hasData: !!((_response_data1 = response.data) === null || _response_data1 === void 0 ? void 0 : _response_data1.data),\n                dataKeys: ((_response_data2 = response.data) === null || _response_data2 === void 0 ? void 0 : _response_data2.data) ? Object.keys(response.data.data) : \"no data\",\n                hasSummary: !!((_response_data3 = response.data) === null || _response_data3 === void 0 ? void 0 : (_response_data_data = _response_data3.data) === null || _response_data_data === void 0 ? void 0 : _response_data_data.summary),\n                summaryKeys: ((_response_data4 = response.data) === null || _response_data4 === void 0 ? void 0 : (_response_data_data1 = _response_data4.data) === null || _response_data_data1 === void 0 ? void 0 : _response_data_data1.summary) ? Object.keys(response.data.data.summary) : \"no summary\"\n            });\n            if (response.status === 200) {\n                if (response.data.success) {\n                    var _response_data_data_summary, _response_data_data2, _response_data_data3;\n                    // Check if the inner data also indicates success\n                    if (response.data.data && response.data.data.success === false) {\n                        // Inner processing failed\n                        console.error(\"❌ Census processing failed:\", response.data.data);\n                        console.error(\"\\uD83D\\uDCCB Error details:\", {\n                            error: response.data.data.error,\n                            message: response.data.data.message,\n                            status_code: response.data.data.status_code,\n                            fullErrorData: response.data.data\n                        });\n                        const errorMessage = response.data.data.message || \"Processing failed: \".concat(response.data.data.error) || 0;\n                        throw new Error(errorMessage);\n                    }\n                    // Log the actual response structure for debugging\n                    console.log(\"✅ Census processing completed successfully\");\n                    console.log(\"\\uD83D\\uDCCA Full response structure:\", response.data);\n                    // Try to extract employee count from various possible locations\n                    const employeeCount = ((_response_data_data2 = response.data.data) === null || _response_data_data2 === void 0 ? void 0 : (_response_data_data_summary = _response_data_data2.summary) === null || _response_data_data_summary === void 0 ? void 0 : _response_data_data_summary.total_employees) || ((_response_data_data3 = response.data.data) === null || _response_data_data3 === void 0 ? void 0 : _response_data_data3.total_employees) || response.data.total_employees || \"unknown\";\n                    console.log(\"\\uD83D\\uDC65 Processed employees: \".concat(employeeCount));\n                    return response.data;\n                } else {\n                    // Outer response indicates failure\n                    console.error(\"❌ Backend processing failed:\", response.data);\n                    throw new Error(response.data.message || \"Census processing failed on backend\");\n                }\n            } else {\n                throw new Error(\"HTTP \".concat(response.status, \": \").concat(response.statusText));\n            }\n        } catch (error) {\n            var _error_response, _error_response1, _error_response2, _error_response3, _error_response4, _error_response5, _error_response_data, _error_response6, _error_message;\n            console.error(\"❌ Census upload failed:\", error);\n            console.error(\"\\uD83D\\uDCCB Error details:\", {\n                message: error.message,\n                code: error.code,\n                status: (_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status,\n                statusText: (_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : _error_response1.statusText,\n                responseData: (_error_response2 = error.response) === null || _error_response2 === void 0 ? void 0 : _error_response2.data\n            });\n            // Provide more specific error messages\n            if (error.code === \"ECONNREFUSED\") {\n                throw new Error(\"Census API service is not running. Please start the Python backend on port 8000.\");\n            } else if (((_error_response3 = error.response) === null || _error_response3 === void 0 ? void 0 : _error_response3.status) === 404) {\n                throw new Error(\"Census API endpoint not found. Please check if the Python backend is running.\");\n            } else if (((_error_response4 = error.response) === null || _error_response4 === void 0 ? void 0 : _error_response4.status) === 413) {\n                throw new Error(\"File too large. Maximum file size is 50MB.\");\n            } else if (((_error_response5 = error.response) === null || _error_response5 === void 0 ? void 0 : _error_response5.status) === 500) {\n                var _error_response_data1, _error_response7;\n                const serverError = ((_error_response7 = error.response) === null || _error_response7 === void 0 ? void 0 : (_error_response_data1 = _error_response7.data) === null || _error_response_data1 === void 0 ? void 0 : _error_response_data1.message) || \"Internal server error during census processing\";\n                throw new Error(\"Server error: \".concat(serverError));\n            } else if ((_error_response6 = error.response) === null || _error_response6 === void 0 ? void 0 : (_error_response_data = _error_response6.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) {\n                throw new Error(error.response.data.message);\n            } else if ((_error_message = error.message) === null || _error_message === void 0 ? void 0 : _error_message.includes(\"undefined\")) {\n                var _error_response8;\n                // Handle response structure mismatch\n                console.log(\"\\uD83D\\uDD0D Response structure debugging:\", (_error_response8 = error.response) === null || _error_response8 === void 0 ? void 0 : _error_response8.data);\n                throw new Error(\"Response structure mismatch - check console for details\");\n            } else {\n                throw new Error(error.message || \"Failed to upload census file\");\n            }\n        }\n    }\n    /**\n   * Get processed census data by company/report ID\n   * Note: This would be implemented when backend supports data persistence\n   */ static async getCensusData(companyId) {\n        try {\n            // For now, this would use the Python backend when persistence is implemented\n            const url = \"\".concat(CENSUS_API_BASE_URL, \"/api/census/reports/\").concat(companyId);\n            const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(url);\n            return response.data;\n        } catch (error) {\n            console.error(\"❌ Failed to fetch census data:\", error);\n            throw new Error(error.message || \"Failed to fetch census data\");\n        }\n    }\n    /**\n   * Get broker dashboard data (list of processed companies)\n   * Note: This would be implemented when backend supports data persistence\n   */ static async getBrokerDashboard() {\n        try {\n            // For now, this would use the Python backend when persistence is implemented\n            const url = \"\".concat(CENSUS_API_BASE_URL, \"/api/census/broker/dashboard\");\n            const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(url);\n            return response.data || [];\n        } catch (error) {\n            console.error(\"❌ Failed to fetch broker dashboard:\", error);\n            // Return empty array as fallback - frontend state management handles this\n            return [];\n        }\n    }\n    /**\n   * Transform API employee data to frontend format\n   */ static transformEmployeeData(apiEmployee) {\n        var _apiEmployee_recommended_plan, _apiEmployee_recommended_plan1;\n        // Parse top 3 plans if available\n        let top3Plans = [];\n        try {\n            if (apiEmployee.top_3_available_plans) {\n                top3Plans = JSON.parse(apiEmployee.top_3_available_plans);\n            }\n        } catch (e) {\n            console.warn(\"Failed to parse top_3_available_plans:\", e);\n        }\n        // Map risk level based on plan confidence\n        const getRiskLevel = (confidence)=>{\n            if (confidence >= 0.8) return \"Low\";\n            if (confidence >= 0.6) return \"Medium\";\n            return \"High\";\n        };\n        return {\n            name: apiEmployee.name,\n            department: \"Dept \".concat(apiEmployee.dept_count),\n            risk: getRiskLevel(apiEmployee.plan_confidence),\n            age: apiEmployee.age,\n            coverage: apiEmployee.predicted_plan_type,\n            hasDependents: apiEmployee.marital_status.toLowerCase() === \"married\",\n            salary: apiEmployee.income_tier,\n            currentPlan: {\n                medical: ((_apiEmployee_recommended_plan = apiEmployee.recommended_plan) === null || _apiEmployee_recommended_plan === void 0 ? void 0 : _apiEmployee_recommended_plan.name) || \"Not Enrolled\",\n                dental: apiEmployee.predicted_benefits.includes(\"Dental\") ? \"Basic\" : \"Not Enrolled\",\n                vision: apiEmployee.predicted_benefits.includes(\"Vision\") ? \"Basic\" : \"Not Enrolled\",\n                life: apiEmployee.predicted_benefits.includes(\"Term Life\") ? \"1x Salary\" : \"None\",\n                disability: apiEmployee.predicted_benefits.includes(\"LTD\") ? \"Basic\" : \"None\"\n            },\n            coverageGaps: [],\n            insights: [\n                apiEmployee.plan_reason\n            ],\n            upsells: [],\n            planFitSummary: {\n                recommendedPlan: ((_apiEmployee_recommended_plan1 = apiEmployee.recommended_plan) === null || _apiEmployee_recommended_plan1 === void 0 ? void 0 : _apiEmployee_recommended_plan1.name) || \"No recommendation\",\n                insight: apiEmployee.plan_reason\n            },\n            // Additional API data\n            apiData: {\n                employee_id: apiEmployee.employee_id,\n                zipcode: apiEmployee.zipcode,\n                city: apiEmployee.city,\n                state: apiEmployee.state,\n                recommended_plan: apiEmployee.recommended_plan,\n                benefits_coverage: apiEmployee.benefits_coverage,\n                top_3_plans: top3Plans,\n                marketplace_plans_available: apiEmployee.marketplace_plans_available,\n                plan_count: apiEmployee.plan_count\n            }\n        };\n    }\n    /**\n   * Transform API response to frontend company data format\n   */ static transformCompanyData(apiResponse) {\n        let companyId = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : \"1\";\n        var _statistics_health_plans, _statistics_demographics, _statistics_demographics1;\n        console.log(\"\\uD83D\\uDD04 Raw API response for transformation:\", apiResponse);\n        // Handle flexible response structure - try multiple possible structures\n        let data, summary, statistics, employees;\n        if (apiResponse.data) {\n            data = apiResponse.data;\n            summary = data.summary || {};\n            statistics = data.statistics || {};\n            employees = data.employees || [];\n        } else {\n            // Direct response without .data wrapper\n            data = apiResponse;\n            summary = data.summary || {};\n            statistics = data.statistics || {};\n            employees = data.employees || [];\n        }\n        console.log(\"\\uD83D\\uDD04 Transforming company data:\", {\n            hasData: !!data,\n            hasSummary: !!summary,\n            hasStatistics: !!statistics,\n            employeeCount: employees.length,\n            summaryKeys: Object.keys(summary),\n            statisticsKeys: Object.keys(statistics),\n            dataKeys: Object.keys(data),\n            fullData: data // Log the full data to see what's actually there\n        });\n        // If we don't have the expected structure, create a minimal response\n        if (!summary.total_employees && !employees.length) {\n            console.warn(\"⚠️ No employee data found, creating minimal response\");\n            return {\n                companyName: \"Company \".concat(companyId),\n                employees: 0,\n                averageAge: 35,\n                dependents: 0,\n                planType: \"Unknown\",\n                potentialSavings: \"$0\",\n                riskScore: \"0.0/10\",\n                uploadDate: new Date().toISOString().split(\"T\")[0],\n                industry: \"Unknown\",\n                currentSpend: \"$0/month\",\n                suggestedPlan: \"No data available\",\n                planFitSummary: {\n                    silverGoldPPO: 0,\n                    hdhp: 0,\n                    familyPPO: 0,\n                    insight: \"No employee data available\"\n                },\n                employeeProfiles: [],\n                upsellOpportunities: [],\n                apiData: apiResponse\n            };\n        }\n        // Calculate potential savings (simplified calculation)\n        const avgPremium = employees.filter((emp)=>emp.recommended_plan).reduce((sum, emp)=>{\n            var _emp_recommended_plan;\n            return sum + (((_emp_recommended_plan = emp.recommended_plan) === null || _emp_recommended_plan === void 0 ? void 0 : _emp_recommended_plan.premium) || 0);\n        }, 0) / (employees.length || 1);\n        const potentialSavings = Math.round(avgPremium * employees.length * 0.15); // Assume 15% savings\n        // Determine primary plan type with null safety\n        const planTypeDistribution = ((_statistics_health_plans = statistics.health_plans) === null || _statistics_health_plans === void 0 ? void 0 : _statistics_health_plans.plan_type_distribution) || {};\n        const planTypes = Object.keys(planTypeDistribution);\n        const primaryPlanType = planTypes.length > 0 ? planTypes.reduce((a, b)=>planTypeDistribution[a] > planTypeDistribution[b] ? a : b) : \"PPO\"; // Default fallback\n        return {\n            companyName: \"Company \".concat(companyId),\n            employees: summary.total_employees || employees.length || 0,\n            averageAge: Math.round(((_statistics_demographics = statistics.demographics) === null || _statistics_demographics === void 0 ? void 0 : _statistics_demographics.average_age) || 35),\n            dependents: employees.filter((emp)=>{\n                var _emp_marital_status;\n                return ((_emp_marital_status = emp.marital_status) === null || _emp_marital_status === void 0 ? void 0 : _emp_marital_status.toLowerCase()) === \"married\";\n            }).length / (employees.length || 1),\n            planType: primaryPlanType,\n            potentialSavings: \"$\".concat(potentialSavings.toLocaleString()),\n            riskScore: \"\".concat(((summary.data_quality_score || 0.8) * 10).toFixed(1), \"/10\"),\n            uploadDate: new Date().toISOString().split(\"T\")[0],\n            industry: \"Technology\",\n            currentSpend: \"$\".concat(Math.round(avgPremium * employees.length).toLocaleString(), \"/month\"),\n            suggestedPlan: \"\".concat(primaryPlanType, \" with Enhanced Coverage\"),\n            planFitSummary: {\n                silverGoldPPO: Math.round((planTypeDistribution[\"PPO\"] || 0) / (employees.length || 1) * 100),\n                hdhp: Math.round((planTypeDistribution[\"HDHP\"] || 0) / (employees.length || 1) * 100),\n                familyPPO: Math.round(employees.filter((emp)=>{\n                    var _emp_marital_status;\n                    return ((_emp_marital_status = emp.marital_status) === null || _emp_marital_status === void 0 ? void 0 : _emp_marital_status.toLowerCase()) === \"married\";\n                }).length / (employees.length || 1) * 100),\n                insight: \"Based on \".concat(employees.length || 0, \" employees with \").concat((((_statistics_demographics1 = statistics.demographics) === null || _statistics_demographics1 === void 0 ? void 0 : _statistics_demographics1.average_age) || 35).toFixed(1), \" average age\")\n            },\n            employeeProfiles: employees.map((emp)=>this.transformEmployeeData(emp)),\n            // Generate mock upsell opportunities based on company data\n            upsellOpportunities: [\n                {\n                    category: \"Enhanced Coverage\",\n                    description: \"Upgrade \".concat(Math.round(employees.length * 0.3), \" employees to premium plans\"),\n                    savings: \"+$\".concat(Math.round(potentialSavings * 0.1).toLocaleString(), \"/month\"),\n                    confidence: \"85%\",\n                    priority: \"High\"\n                },\n                {\n                    category: \"Wellness Programs\",\n                    description: \"Preventive care initiatives for healthier workforce\",\n                    savings: \"+$\".concat(Math.round(potentialSavings * 0.05).toLocaleString(), \"/month\"),\n                    confidence: \"72%\",\n                    priority: \"Medium\"\n                }\n            ],\n            // Store original API data for reference\n            apiData: apiResponse.data\n        };\n    }\n}\n/* harmony default export */ __webpack_exports__[\"default\"] = (CensusApiService);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/census/services/censusApi.ts\n"));

/***/ })

});