"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("_app-pages-browser_src_app_census_components_EmpCensusApp_tsx",{

/***/ "(app-pages-browser)/./src/app/census/services/censusApi.ts":
/*!**********************************************!*\
  !*** ./src/app/census/services/censusApi.ts ***!
  \**********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! axios */ \"(app-pages-browser)/./node_modules/axios/lib/axios.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n// Census API uses the Python backend (chatbot service) instead of the main Node.js backend\nconst CENSUS_API_BASE_URL = \"http://127.0.0.1:8000\" || 0;\nclass CensusApiService {\n    /**\n   * Upload and process census file using the Python backend (chatbot service)\n   */ static async uploadCensusFile(file) {\n        let returnDataframe = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;\n        try {\n            var _response_data, _response_data1, _response_data2, _response_data_data, _response_data3, _response_data_data1, _response_data4;\n            const formData = new FormData();\n            formData.append(\"file\", file);\n            // Build full URL for census API (Python backend)\n            const url = \"\".concat(CENSUS_API_BASE_URL, \"/api/census/processor/v1?return_dataframe=\").concat(returnDataframe);\n            console.log(\"\\uD83D\\uDCE4 Uploading census file: \".concat(file.name, \" (\").concat((file.size / 1024 / 1024).toFixed(2), \" MB)\"));\n            console.log(\"\\uD83D\\uDD17 Census API URL: \".concat(url));\n            console.log(\"\\uD83D\\uDCCB Request details:\", {\n                method: \"POST\",\n                url: url,\n                fileSize: file.size,\n                fileName: file.name,\n                returnDataframe: returnDataframe\n            });\n            // Use axios directly for census API calls to Python backend\n            // Note: Don't set Content-Type manually - let axios set it automatically for FormData\n            const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(url, formData, {\n                timeout: 300000\n            });\n            console.log(\"\\uD83D\\uDCCA Response received:\", {\n                status: response.status,\n                statusText: response.statusText,\n                success: (_response_data = response.data) === null || _response_data === void 0 ? void 0 : _response_data.success,\n                hasData: !!((_response_data1 = response.data) === null || _response_data1 === void 0 ? void 0 : _response_data1.data),\n                dataKeys: ((_response_data2 = response.data) === null || _response_data2 === void 0 ? void 0 : _response_data2.data) ? Object.keys(response.data.data) : \"no data\",\n                hasSummary: !!((_response_data3 = response.data) === null || _response_data3 === void 0 ? void 0 : (_response_data_data = _response_data3.data) === null || _response_data_data === void 0 ? void 0 : _response_data_data.summary),\n                summaryKeys: ((_response_data4 = response.data) === null || _response_data4 === void 0 ? void 0 : (_response_data_data1 = _response_data4.data) === null || _response_data_data1 === void 0 ? void 0 : _response_data_data1.summary) ? Object.keys(response.data.data.summary) : \"no summary\"\n            });\n            if (response.status === 200) {\n                if (response.data.success) {\n                    var _response_data_data_summary, _response_data_data2, _response_data_data3;\n                    // Check if the inner data also indicates success\n                    if (response.data.data && response.data.data.success === false) {\n                        // Inner processing failed\n                        console.error(\"❌ Census processing failed:\", response.data.data);\n                        const errorMessage = response.data.data.message || \"Processing failed: \".concat(response.data.data.error) || 0;\n                        throw new Error(errorMessage);\n                    }\n                    // Log the actual response structure for debugging\n                    console.log(\"✅ Census processing completed successfully\");\n                    console.log(\"\\uD83D\\uDCCA Full response structure:\", response.data);\n                    // Try to extract employee count from various possible locations\n                    const employeeCount = ((_response_data_data2 = response.data.data) === null || _response_data_data2 === void 0 ? void 0 : (_response_data_data_summary = _response_data_data2.summary) === null || _response_data_data_summary === void 0 ? void 0 : _response_data_data_summary.total_employees) || ((_response_data_data3 = response.data.data) === null || _response_data_data3 === void 0 ? void 0 : _response_data_data3.total_employees) || response.data.total_employees || \"unknown\";\n                    console.log(\"\\uD83D\\uDC65 Processed employees: \".concat(employeeCount));\n                    return response.data;\n                } else {\n                    // Outer response indicates failure\n                    console.error(\"❌ Backend processing failed:\", response.data);\n                    throw new Error(response.data.message || \"Census processing failed on backend\");\n                }\n            } else {\n                throw new Error(\"HTTP \".concat(response.status, \": \").concat(response.statusText));\n            }\n        } catch (error) {\n            var _error_response, _error_response1, _error_response2, _error_response3, _error_response4, _error_response5, _error_response_data, _error_response6, _error_message;\n            console.error(\"❌ Census upload failed:\", error);\n            console.error(\"\\uD83D\\uDCCB Error details:\", {\n                message: error.message,\n                code: error.code,\n                status: (_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status,\n                statusText: (_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : _error_response1.statusText,\n                responseData: (_error_response2 = error.response) === null || _error_response2 === void 0 ? void 0 : _error_response2.data\n            });\n            // Provide more specific error messages\n            if (error.code === \"ECONNREFUSED\") {\n                throw new Error(\"Census API service is not running. Please start the Python backend on port 8000.\");\n            } else if (((_error_response3 = error.response) === null || _error_response3 === void 0 ? void 0 : _error_response3.status) === 404) {\n                throw new Error(\"Census API endpoint not found. Please check if the Python backend is running.\");\n            } else if (((_error_response4 = error.response) === null || _error_response4 === void 0 ? void 0 : _error_response4.status) === 413) {\n                throw new Error(\"File too large. Maximum file size is 50MB.\");\n            } else if (((_error_response5 = error.response) === null || _error_response5 === void 0 ? void 0 : _error_response5.status) === 500) {\n                var _error_response_data1, _error_response7;\n                const serverError = ((_error_response7 = error.response) === null || _error_response7 === void 0 ? void 0 : (_error_response_data1 = _error_response7.data) === null || _error_response_data1 === void 0 ? void 0 : _error_response_data1.message) || \"Internal server error during census processing\";\n                throw new Error(\"Server error: \".concat(serverError));\n            } else if ((_error_response6 = error.response) === null || _error_response6 === void 0 ? void 0 : (_error_response_data = _error_response6.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) {\n                throw new Error(error.response.data.message);\n            } else if ((_error_message = error.message) === null || _error_message === void 0 ? void 0 : _error_message.includes(\"undefined\")) {\n                var _error_response8;\n                // Handle response structure mismatch\n                console.log(\"\\uD83D\\uDD0D Response structure debugging:\", (_error_response8 = error.response) === null || _error_response8 === void 0 ? void 0 : _error_response8.data);\n                throw new Error(\"Response structure mismatch - check console for details\");\n            } else {\n                throw new Error(error.message || \"Failed to upload census file\");\n            }\n        }\n    }\n    /**\n   * Get processed census data by company/report ID\n   * Note: This would be implemented when backend supports data persistence\n   */ static async getCensusData(companyId) {\n        try {\n            // For now, this would use the Python backend when persistence is implemented\n            const url = \"\".concat(CENSUS_API_BASE_URL, \"/api/census/reports/\").concat(companyId);\n            const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(url);\n            return response.data;\n        } catch (error) {\n            console.error(\"❌ Failed to fetch census data:\", error);\n            throw new Error(error.message || \"Failed to fetch census data\");\n        }\n    }\n    /**\n   * Get broker dashboard data (list of processed companies)\n   * Note: This would be implemented when backend supports data persistence\n   */ static async getBrokerDashboard() {\n        try {\n            // For now, this would use the Python backend when persistence is implemented\n            const url = \"\".concat(CENSUS_API_BASE_URL, \"/api/census/broker/dashboard\");\n            const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(url);\n            return response.data || [];\n        } catch (error) {\n            console.error(\"❌ Failed to fetch broker dashboard:\", error);\n            // Return empty array as fallback - frontend state management handles this\n            return [];\n        }\n    }\n    /**\n   * Transform API employee data to frontend format\n   */ static transformEmployeeData(apiEmployee) {\n        var _apiEmployee_recommended_plan, _apiEmployee_recommended_plan1;\n        // Parse top 3 plans if available\n        let top3Plans = [];\n        try {\n            if (apiEmployee.top_3_available_plans) {\n                top3Plans = JSON.parse(apiEmployee.top_3_available_plans);\n            }\n        } catch (e) {\n            console.warn(\"Failed to parse top_3_available_plans:\", e);\n        }\n        // Map risk level based on plan confidence\n        const getRiskLevel = (confidence)=>{\n            if (confidence >= 0.8) return \"Low\";\n            if (confidence >= 0.6) return \"Medium\";\n            return \"High\";\n        };\n        return {\n            name: apiEmployee.name,\n            department: \"Dept \".concat(apiEmployee.dept_count),\n            risk: getRiskLevel(apiEmployee.plan_confidence),\n            age: apiEmployee.age,\n            coverage: apiEmployee.predicted_plan_type,\n            hasDependents: apiEmployee.marital_status.toLowerCase() === \"married\",\n            salary: apiEmployee.income_tier,\n            currentPlan: {\n                medical: ((_apiEmployee_recommended_plan = apiEmployee.recommended_plan) === null || _apiEmployee_recommended_plan === void 0 ? void 0 : _apiEmployee_recommended_plan.name) || \"Not Enrolled\",\n                dental: apiEmployee.predicted_benefits.includes(\"Dental\") ? \"Basic\" : \"Not Enrolled\",\n                vision: apiEmployee.predicted_benefits.includes(\"Vision\") ? \"Basic\" : \"Not Enrolled\",\n                life: apiEmployee.predicted_benefits.includes(\"Term Life\") ? \"1x Salary\" : \"None\",\n                disability: apiEmployee.predicted_benefits.includes(\"LTD\") ? \"Basic\" : \"None\"\n            },\n            coverageGaps: [],\n            insights: [\n                apiEmployee.plan_reason\n            ],\n            upsells: [],\n            planFitSummary: {\n                recommendedPlan: ((_apiEmployee_recommended_plan1 = apiEmployee.recommended_plan) === null || _apiEmployee_recommended_plan1 === void 0 ? void 0 : _apiEmployee_recommended_plan1.name) || \"No recommendation\",\n                insight: apiEmployee.plan_reason\n            },\n            // Additional API data\n            apiData: {\n                employee_id: apiEmployee.employee_id,\n                zipcode: apiEmployee.zipcode,\n                city: apiEmployee.city,\n                state: apiEmployee.state,\n                recommended_plan: apiEmployee.recommended_plan,\n                benefits_coverage: apiEmployee.benefits_coverage,\n                top_3_plans: top3Plans,\n                marketplace_plans_available: apiEmployee.marketplace_plans_available,\n                plan_count: apiEmployee.plan_count\n            }\n        };\n    }\n    /**\n   * Transform API response to frontend company data format\n   */ static transformCompanyData(apiResponse) {\n        let companyId = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : \"1\";\n        var _statistics_health_plans, _statistics_demographics, _statistics_demographics1;\n        console.log(\"\\uD83D\\uDD04 Raw API response for transformation:\", apiResponse);\n        // Handle flexible response structure - try multiple possible structures\n        let data, summary, statistics, employees;\n        if (apiResponse.data) {\n            data = apiResponse.data;\n            summary = data.summary || {};\n            statistics = data.statistics || {};\n            employees = data.employees || [];\n        } else {\n            // Direct response without .data wrapper\n            data = apiResponse;\n            summary = data.summary || {};\n            statistics = data.statistics || {};\n            employees = data.employees || [];\n        }\n        console.log(\"\\uD83D\\uDD04 Transforming company data:\", {\n            hasData: !!data,\n            hasSummary: !!summary,\n            hasStatistics: !!statistics,\n            employeeCount: employees.length,\n            summaryKeys: Object.keys(summary),\n            statisticsKeys: Object.keys(statistics),\n            dataKeys: Object.keys(data),\n            fullData: data // Log the full data to see what's actually there\n        });\n        // If we don't have the expected structure, create a minimal response\n        if (!summary.total_employees && !employees.length) {\n            console.warn(\"⚠️ No employee data found, creating minimal response\");\n            return {\n                companyName: \"Company \".concat(companyId),\n                employees: 0,\n                averageAge: 35,\n                dependents: 0,\n                planType: \"Unknown\",\n                potentialSavings: \"$0\",\n                riskScore: \"0.0/10\",\n                uploadDate: new Date().toISOString().split(\"T\")[0],\n                industry: \"Unknown\",\n                currentSpend: \"$0/month\",\n                suggestedPlan: \"No data available\",\n                planFitSummary: {\n                    silverGoldPPO: 0,\n                    hdhp: 0,\n                    familyPPO: 0,\n                    insight: \"No employee data available\"\n                },\n                employeeProfiles: [],\n                upsellOpportunities: [],\n                apiData: apiResponse\n            };\n        }\n        // Calculate potential savings (simplified calculation)\n        const avgPremium = employees.filter((emp)=>emp.recommended_plan).reduce((sum, emp)=>{\n            var _emp_recommended_plan;\n            return sum + (((_emp_recommended_plan = emp.recommended_plan) === null || _emp_recommended_plan === void 0 ? void 0 : _emp_recommended_plan.premium) || 0);\n        }, 0) / (employees.length || 1);\n        const potentialSavings = Math.round(avgPremium * employees.length * 0.15); // Assume 15% savings\n        // Determine primary plan type with null safety\n        const planTypeDistribution = ((_statistics_health_plans = statistics.health_plans) === null || _statistics_health_plans === void 0 ? void 0 : _statistics_health_plans.plan_type_distribution) || {};\n        const planTypes = Object.keys(planTypeDistribution);\n        const primaryPlanType = planTypes.length > 0 ? planTypes.reduce((a, b)=>planTypeDistribution[a] > planTypeDistribution[b] ? a : b) : \"PPO\"; // Default fallback\n        return {\n            companyName: \"Company \".concat(companyId),\n            employees: summary.total_employees || employees.length || 0,\n            averageAge: Math.round(((_statistics_demographics = statistics.demographics) === null || _statistics_demographics === void 0 ? void 0 : _statistics_demographics.average_age) || 35),\n            dependents: employees.filter((emp)=>{\n                var _emp_marital_status;\n                return ((_emp_marital_status = emp.marital_status) === null || _emp_marital_status === void 0 ? void 0 : _emp_marital_status.toLowerCase()) === \"married\";\n            }).length / (employees.length || 1),\n            planType: primaryPlanType,\n            potentialSavings: \"$\".concat(potentialSavings.toLocaleString()),\n            riskScore: \"\".concat(((summary.data_quality_score || 0.8) * 10).toFixed(1), \"/10\"),\n            uploadDate: new Date().toISOString().split(\"T\")[0],\n            industry: \"Technology\",\n            currentSpend: \"$\".concat(Math.round(avgPremium * employees.length).toLocaleString(), \"/month\"),\n            suggestedPlan: \"\".concat(primaryPlanType, \" with Enhanced Coverage\"),\n            planFitSummary: {\n                silverGoldPPO: Math.round((planTypeDistribution[\"PPO\"] || 0) / (employees.length || 1) * 100),\n                hdhp: Math.round((planTypeDistribution[\"HDHP\"] || 0) / (employees.length || 1) * 100),\n                familyPPO: Math.round(employees.filter((emp)=>{\n                    var _emp_marital_status;\n                    return ((_emp_marital_status = emp.marital_status) === null || _emp_marital_status === void 0 ? void 0 : _emp_marital_status.toLowerCase()) === \"married\";\n                }).length / (employees.length || 1) * 100),\n                insight: \"Based on \".concat(employees.length || 0, \" employees with \").concat((((_statistics_demographics1 = statistics.demographics) === null || _statistics_demographics1 === void 0 ? void 0 : _statistics_demographics1.average_age) || 35).toFixed(1), \" average age\")\n            },\n            employeeProfiles: employees.map((emp)=>this.transformEmployeeData(emp)),\n            // Generate mock upsell opportunities based on company data\n            upsellOpportunities: [\n                {\n                    category: \"Enhanced Coverage\",\n                    description: \"Upgrade \".concat(Math.round(employees.length * 0.3), \" employees to premium plans\"),\n                    savings: \"+$\".concat(Math.round(potentialSavings * 0.1).toLocaleString(), \"/month\"),\n                    confidence: \"85%\",\n                    priority: \"High\"\n                },\n                {\n                    category: \"Wellness Programs\",\n                    description: \"Preventive care initiatives for healthier workforce\",\n                    savings: \"+$\".concat(Math.round(potentialSavings * 0.05).toLocaleString(), \"/month\"),\n                    confidence: \"72%\",\n                    priority: \"Medium\"\n                }\n            ],\n            // Store original API data for reference\n            apiData: apiResponse.data\n        };\n    }\n}\n/* harmony default export */ __webpack_exports__[\"default\"] = (CensusApiService);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/census/services/censusApi.ts\n"));

/***/ })

});