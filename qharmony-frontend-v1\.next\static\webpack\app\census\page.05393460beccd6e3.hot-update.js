"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/census/page",{

/***/ "(app-pages-browser)/./src/app/census/components/AskBrea.tsx":
/*!***********************************************!*\
  !*** ./src/app/census/components/AskBrea.tsx ***!
  \***********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ui/button */ \"(app-pages-browser)/./src/app/census/components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_MessageCircle_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=MessageCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-circle.js\");\n/* harmony import */ var _ai_enroller_employee_enrol_components_ChatModal__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../ai-enroller/employee-enrol/components/ChatModal */ \"(app-pages-browser)/./src/app/ai-enroller/employee-enrol/components/ChatModal.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\nconst AskBrea = (param)=>{\n    let { context = \"\", size = \"default\", variant = \"default\" } = param;\n    _s();\n    const [isOpen, setIsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                size: size,\n                variant: variant,\n                className: \"flex items-center gap-2\",\n                onClick: ()=>setIsOpen(true),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MessageCircle_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        className: \"h-4 w-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\components\\\\AskBrea.tsx\",\n                        lineNumber: 24,\n                        columnNumber: 9\n                    }, undefined),\n                    \"Ask Brea\"\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\components\\\\AskBrea.tsx\",\n                lineNumber: 18,\n                columnNumber: 7\n            }, undefined),\n            isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    position: \"fixed\",\n                    top: 0,\n                    left: 0,\n                    right: 0,\n                    bottom: 0,\n                    zIndex: 99999,\n                    pointerEvents: \"auto\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ai_enroller_employee_enrol_components_ChatModal__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    isOpen: isOpen,\n                    onClose: ()=>setIsOpen(false)\n                }, void 0, false, {\n                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\components\\\\AskBrea.tsx\",\n                    lineNumber: 38,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\components\\\\AskBrea.tsx\",\n                lineNumber: 29,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n_s(AskBrea, \"+sus0Lb0ewKHdwiUhiTAJFoFyQ0=\");\n_c = AskBrea;\n/* harmony default export */ __webpack_exports__[\"default\"] = (AskBrea);\nvar _c;\n$RefreshReg$(_c, \"AskBrea\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/census/components/AskBrea.tsx\n"));

/***/ })

});