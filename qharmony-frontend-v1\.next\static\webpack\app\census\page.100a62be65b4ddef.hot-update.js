"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/census/page",{

/***/ "(app-pages-browser)/./src/app/census/public/GenerateProposal.tsx":
/*!****************************************************!*\
  !*** ./src/app/census/public/GenerateProposal.tsx ***!
  \****************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _lib_react_router_dom__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../lib/react-router-dom */ \"(app-pages-browser)/./src/app/census/lib/react-router-dom.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../components/ui/button */ \"(app-pages-browser)/./src/app/census/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../components/ui/card */ \"(app-pages-browser)/./src/app/census/components/ui/card.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_BarChart3_Calendar_CheckCircle_Download_RefreshCw_Share2_Target_Users_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,BarChart3,Calendar,CheckCircle,Download,RefreshCw,Share2,Target,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_BarChart3_Calendar_CheckCircle_Download_RefreshCw_Share2_Target_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,BarChart3,Calendar,CheckCircle,Download,RefreshCw,Share2,Target,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_BarChart3_Calendar_CheckCircle_Download_RefreshCw_Share2_Target_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,BarChart3,Calendar,CheckCircle,Download,RefreshCw,Share2,Target,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_BarChart3_Calendar_CheckCircle_Download_RefreshCw_Share2_Target_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,BarChart3,Calendar,CheckCircle,Download,RefreshCw,Share2,Target,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chart-column.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_BarChart3_Calendar_CheckCircle_Download_RefreshCw_Share2_Target_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,BarChart3,Calendar,CheckCircle,Download,RefreshCw,Share2,Target,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_BarChart3_Calendar_CheckCircle_Download_RefreshCw_Share2_Target_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,BarChart3,Calendar,CheckCircle,Download,RefreshCw,Share2,Target,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_BarChart3_Calendar_CheckCircle_Download_RefreshCw_Share2_Target_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,BarChart3,Calendar,CheckCircle,Download,RefreshCw,Share2,Target,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_BarChart3_Calendar_CheckCircle_Download_RefreshCw_Share2_Target_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,BarChart3,Calendar,CheckCircle,Download,RefreshCw,Share2,Target,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_BarChart3_Calendar_CheckCircle_Download_RefreshCw_Share2_Target_Users_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,BarChart3,Calendar,CheckCircle,Download,RefreshCw,Share2,Target,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/share-2.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_BarChart3_Calendar_CheckCircle_Download_RefreshCw_Share2_Target_Users_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,BarChart3,Calendar,CheckCircle,Download,RefreshCw,Share2,Target,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Bar,BarChart,CartesianGrid,Cell,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/component/ResponsiveContainer.js\");\n/* harmony import */ var _barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Bar,BarChart,CartesianGrid,Cell,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/chart/BarChart.js\");\n/* harmony import */ var _barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Bar,BarChart,CartesianGrid,Cell,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/cartesian/CartesianGrid.js\");\n/* harmony import */ var _barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Bar,BarChart,CartesianGrid,Cell,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/cartesian/XAxis.js\");\n/* harmony import */ var _barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Bar,BarChart,CartesianGrid,Cell,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/cartesian/YAxis.js\");\n/* harmony import */ var _barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Bar,BarChart,CartesianGrid,Cell,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/component/Tooltip.js\");\n/* harmony import */ var _barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Bar,BarChart,CartesianGrid,Cell,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/cartesian/Bar.js\");\n/* harmony import */ var _barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Bar,BarChart,CartesianGrid,Cell,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/chart/PieChart.js\");\n/* harmony import */ var _barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Bar,BarChart,CartesianGrid,Cell,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/polar/Pie.js\");\n/* harmony import */ var _barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Bar,BarChart,CartesianGrid,Cell,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/component/Cell.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\nconst GenerateProposal = ()=>{\n    _s();\n    const { id } = (0,_lib_react_router_dom__WEBPACK_IMPORTED_MODULE_1__.useParams)();\n    const navigate = (0,_lib_react_router_dom__WEBPACK_IMPORTED_MODULE_1__.useNavigate)();\n    // Mock data - in real app this would come from API based on ID\n    const companyData = {\n        \"1\": {\n            companyName: \"TechCorp Solutions\",\n            employees: 43,\n            averageAge: 36,\n            riskScore: \"6.2/10\",\n            currentPlanType: \"PPO + HSA Combo\",\n            suggestedPlan: \"Gold PPO with Disability Coverage\",\n            expectedSavings: \"$127,500\",\n            analysisDate: \"2024-01-15\",\n            planFitData: [\n                {\n                    name: \"PPO\",\n                    value: 67,\n                    color: \"#3B82F6\"\n                },\n                {\n                    name: \"HDHP\",\n                    value: 21,\n                    color: \"#10B981\"\n                },\n                {\n                    name: \"Family/Rx\",\n                    value: 12,\n                    color: \"#8B5CF6\"\n                }\n            ],\n            savingsData: [\n                {\n                    category: \"Current Cost\",\n                    amount: 540000\n                },\n                {\n                    category: \"Projected Cost\",\n                    amount: 412500\n                },\n                {\n                    category: \"Savings\",\n                    amount: 127500\n                }\n            ],\n            keyInsights: [\n                \"Majority of employees fall in high-utilization age bracket (36 avg age)\",\n                \"Disability coverage crucial due to technology industry risk profile\",\n                \"67% of workforce suits comprehensive PPO coverage\",\n                \"Current HSA utilization below industry average\"\n            ],\n            recommendations: [\n                {\n                    category: \"Primary Plan Change\",\n                    description: \"Transition to Gold PPO with enhanced disability coverage\",\n                    impact: \"Reduce overall costs by 23% while improving coverage\"\n                },\n                {\n                    category: \"Voluntary Benefits\",\n                    description: \"Add dental and vision upgrade options\",\n                    impact: \"Increase employee satisfaction and retention\"\n                },\n                {\n                    category: \"Add-on Services\",\n                    description: \"Include telehealth and mental health support\",\n                    impact: \"Preventive care reducing long-term claims costs\"\n                }\n            ],\n            industryBenchmark: {\n                currentPercentile: 45,\n                projectedPercentile: 78,\n                avgSavings: \"$95,000\"\n            }\n        }\n    };\n    // Handle case where no ID is provided\n    if (!id) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-slate-50 to-blue-50 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                className: \"max-w-md mx-auto\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                    className: \"p-6 text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-bold mb-2\",\n                            children: \"No Company Selected\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\GenerateProposal.tsx\",\n                            lineNumber: 71,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 mb-4\",\n                            children: \"Please select a company to generate a proposal for.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\GenerateProposal.tsx\",\n                            lineNumber: 72,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                            onClick: ()=>navigate(\"/dashboard\"),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_BarChart3_Calendar_CheckCircle_Download_RefreshCw_Share2_Target_Users_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    className: \"h-4 w-4 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\GenerateProposal.tsx\",\n                                    lineNumber: 74,\n                                    columnNumber: 15\n                                }, undefined),\n                                \"Back to Dashboard\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\GenerateProposal.tsx\",\n                            lineNumber: 73,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\GenerateProposal.tsx\",\n                    lineNumber: 70,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\GenerateProposal.tsx\",\n                lineNumber: 69,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\GenerateProposal.tsx\",\n            lineNumber: 68,\n            columnNumber: 7\n        }, undefined);\n    }\n    const company = companyData[id];\n    if (!company) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-slate-50 to-blue-50 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                className: \"max-w-md mx-auto\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                    className: \"p-6 text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-bold mb-2\",\n                            children: \"Company Not Found\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\GenerateProposal.tsx\",\n                            lineNumber: 90,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 mb-4\",\n                            children: \"The requested company proposal could not be found.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\GenerateProposal.tsx\",\n                            lineNumber: 91,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                            onClick: ()=>navigate(\"/dashboard\"),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_BarChart3_Calendar_CheckCircle_Download_RefreshCw_Share2_Target_Users_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    className: \"h-4 w-4 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\GenerateProposal.tsx\",\n                                    lineNumber: 93,\n                                    columnNumber: 15\n                                }, undefined),\n                                \"Back to Dashboard\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\GenerateProposal.tsx\",\n                            lineNumber: 92,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\GenerateProposal.tsx\",\n                    lineNumber: 89,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\GenerateProposal.tsx\",\n                lineNumber: 88,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\GenerateProposal.tsx\",\n            lineNumber: 87,\n            columnNumber: 7\n        }, undefined);\n    }\n    const COLORS = [\n        \"#3B82F6\",\n        \"#10B981\",\n        \"#8B5CF6\"\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-slate-50 to-blue-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"border-b bg-white/90 backdrop-blur-sm sticky top-0 z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto px-3 sm:px-4 py-3 flex justify-between items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    variant: \"ghost\",\n                                    size: \"sm\",\n                                    onClick: ()=>navigate(\"/dashboard\"),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_BarChart3_Calendar_CheckCircle_Download_RefreshCw_Share2_Target_Users_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\GenerateProposal.tsx\",\n                                            lineNumber: 111,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        \"Back to Dashboard\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\GenerateProposal.tsx\",\n                                    lineNumber: 110,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-xl sm:text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent\",\n                                    children: \"BenOsphere\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\GenerateProposal.tsx\",\n                                    lineNumber: 114,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\GenerateProposal.tsx\",\n                            lineNumber: 109,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                variant: \"outline\",\n                                size: \"sm\",\n                                children: \"John Broker\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\GenerateProposal.tsx\",\n                                lineNumber: 119,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\GenerateProposal.tsx\",\n                            lineNumber: 118,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\GenerateProposal.tsx\",\n                    lineNumber: 108,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\GenerateProposal.tsx\",\n                lineNumber: 107,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"container mx-auto px-3 sm:px-4 py-6 max-w-6xl\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-4xl font-bold text-gray-900 mb-2\",\n                                children: \"Benefits Proposal\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\GenerateProposal.tsx\",\n                                lineNumber: 129,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl text-blue-600 font-semibold\",\n                                children: company.companyName\n                            }, void 0, false, {\n                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\GenerateProposal.tsx\",\n                                lineNumber: 130,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 mt-2\",\n                                children: [\n                                    \"Generated on \",\n                                    new Date(company.analysisDate).toLocaleDateString()\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\GenerateProposal.tsx\",\n                                lineNumber: 131,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\GenerateProposal.tsx\",\n                        lineNumber: 128,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        className: \"mb-6 shadow-lg border-0\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-blue-100 p-2 rounded-lg mr-3\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_BarChart3_Calendar_CheckCircle_Download_RefreshCw_Share2_Target_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                className: \"h-5 w-5 text-blue-600\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\GenerateProposal.tsx\",\n                                                lineNumber: 139,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\GenerateProposal.tsx\",\n                                            lineNumber: 138,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        \"Client Overview\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\GenerateProposal.tsx\",\n                                    lineNumber: 137,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\GenerateProposal.tsx\",\n                                lineNumber: 136,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-2 md:grid-cols-4 gap-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"text-sm font-medium text-gray-500 mb-1\",\n                                                    children: \"Total Employees\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\GenerateProposal.tsx\",\n                                                    lineNumber: 147,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold text-gray-900\",\n                                                    children: company.employees\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\GenerateProposal.tsx\",\n                                                    lineNumber: 148,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\GenerateProposal.tsx\",\n                                            lineNumber: 146,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"text-sm font-medium text-gray-500 mb-1\",\n                                                    children: \"Average Age\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\GenerateProposal.tsx\",\n                                                    lineNumber: 151,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold text-gray-900\",\n                                                    children: company.averageAge\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\GenerateProposal.tsx\",\n                                                    lineNumber: 152,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\GenerateProposal.tsx\",\n                                            lineNumber: 150,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"text-sm font-medium text-gray-500 mb-1\",\n                                                    children: \"Risk Score\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\GenerateProposal.tsx\",\n                                                    lineNumber: 155,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold text-gray-900\",\n                                                    children: company.riskScore\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\GenerateProposal.tsx\",\n                                                    lineNumber: 156,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\GenerateProposal.tsx\",\n                                            lineNumber: 154,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"text-sm font-medium text-gray-500 mb-1\",\n                                                    children: \"Current Plan\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\GenerateProposal.tsx\",\n                                                    lineNumber: 159,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-lg font-semibold text-gray-900\",\n                                                    children: company.currentPlanType\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\GenerateProposal.tsx\",\n                                                    lineNumber: 160,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\GenerateProposal.tsx\",\n                                            lineNumber: 158,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\GenerateProposal.tsx\",\n                                    lineNumber: 145,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\GenerateProposal.tsx\",\n                                lineNumber: 144,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\GenerateProposal.tsx\",\n                        lineNumber: 135,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        className: \"mb-6 shadow-lg border-0\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-green-100 p-2 rounded-lg mr-3\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_BarChart3_Calendar_CheckCircle_Download_RefreshCw_Share2_Target_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                className: \"h-5 w-5 text-green-600\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\GenerateProposal.tsx\",\n                                                lineNumber: 171,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\GenerateProposal.tsx\",\n                                            lineNumber: 170,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        \"Recommended Plan\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\GenerateProposal.tsx\",\n                                    lineNumber: 169,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\GenerateProposal.tsx\",\n                                lineNumber: 168,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gradient-to-r from-yellow-50 to-orange-50 p-6 rounded-lg border-l-4 border-yellow-500 mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-2xl font-bold text-orange-800 mb-2\",\n                                                children: company.suggestedPlan\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\GenerateProposal.tsx\",\n                                                lineNumber: 178,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-700 mb-4\",\n                                                children: \"This plan is recommended based on your workforce demographics, current utilization patterns, and industry risk profile. The comprehensive PPO structure with enhanced disability coverage addresses the needs of your technology workforce.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\GenerateProposal.tsx\",\n                                                lineNumber: 179,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between bg-white/50 p-4 rounded-lg\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-lg font-semibold text-gray-700\",\n                                                        children: \"Expected Annual Savings\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\GenerateProposal.tsx\",\n                                                        lineNumber: 184,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-3xl font-bold text-green-600\",\n                                                        children: company.expectedSavings\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\GenerateProposal.tsx\",\n                                                        lineNumber: 185,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\GenerateProposal.tsx\",\n                                                lineNumber: 183,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\GenerateProposal.tsx\",\n                                        lineNumber: 177,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid md:grid-cols-2 gap-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"text-lg font-semibold text-gray-900 mb-3\",\n                                                        children: \"Cost Comparison\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\GenerateProposal.tsx\",\n                                                        lineNumber: 192,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_7__.ResponsiveContainer, {\n                                                        width: \"100%\",\n                                                        height: 250,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_8__.BarChart, {\n                                                            data: company.savingsData,\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_9__.CartesianGrid, {\n                                                                    strokeDasharray: \"3 3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\GenerateProposal.tsx\",\n                                                                    lineNumber: 195,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_10__.XAxis, {\n                                                                    dataKey: \"category\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\GenerateProposal.tsx\",\n                                                                    lineNumber: 196,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_11__.YAxis, {}, void 0, false, {\n                                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\GenerateProposal.tsx\",\n                                                                    lineNumber: 197,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_12__.Tooltip, {\n                                                                    formatter: (value)=>[\n                                                                            \"$\".concat(value.toLocaleString()),\n                                                                            \"Amount\"\n                                                                        ]\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\GenerateProposal.tsx\",\n                                                                    lineNumber: 198,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_13__.Bar, {\n                                                                    dataKey: \"amount\",\n                                                                    fill: \"#3B82F6\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\GenerateProposal.tsx\",\n                                                                    lineNumber: 199,\n                                                                    columnNumber: 21\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\GenerateProposal.tsx\",\n                                                            lineNumber: 194,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\GenerateProposal.tsx\",\n                                                        lineNumber: 193,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\GenerateProposal.tsx\",\n                                                lineNumber: 191,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-4xl font-bold text-green-600 mb-2\",\n                                                            children: \"23%\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\GenerateProposal.tsx\",\n                                                            lineNumber: 205,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-lg text-gray-600\",\n                                                            children: \"Cost Reduction\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\GenerateProposal.tsx\",\n                                                            lineNumber: 206,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm text-gray-500 mt-2\",\n                                                            children: \"vs. Current Plan\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\GenerateProposal.tsx\",\n                                                            lineNumber: 207,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\GenerateProposal.tsx\",\n                                                    lineNumber: 204,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\GenerateProposal.tsx\",\n                                                lineNumber: 203,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\GenerateProposal.tsx\",\n                                        lineNumber: 190,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\GenerateProposal.tsx\",\n                                lineNumber: 176,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\GenerateProposal.tsx\",\n                        lineNumber: 167,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        className: \"mb-6 shadow-lg border-0\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-purple-100 p-2 rounded-lg mr-3\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_BarChart3_Calendar_CheckCircle_Download_RefreshCw_Share2_Target_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                className: \"h-5 w-5 text-purple-600\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\GenerateProposal.tsx\",\n                                                lineNumber: 219,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\GenerateProposal.tsx\",\n                                            lineNumber: 218,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        \"Plan Fit Breakdown\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\GenerateProposal.tsx\",\n                                    lineNumber: 217,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\GenerateProposal.tsx\",\n                                lineNumber: 216,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid md:grid-cols-2 gap-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"text-lg font-semibold text-gray-900 mb-3\",\n                                                    children: \"Employee Coverage Distribution\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\GenerateProposal.tsx\",\n                                                    lineNumber: 227,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_7__.ResponsiveContainer, {\n                                                    width: \"100%\",\n                                                    height: 250,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_15__.PieChart, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_16__.Pie, {\n                                                                data: company.planFitData,\n                                                                cx: \"50%\",\n                                                                cy: \"50%\",\n                                                                outerRadius: 80,\n                                                                dataKey: \"value\",\n                                                                label: (param)=>{\n                                                                    let { name, value } = param;\n                                                                    return \"\".concat(name, \": \").concat(value, \"%\");\n                                                                },\n                                                                children: company.planFitData.map((entry, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_17__.Cell, {\n                                                                        fill: entry.color\n                                                                    }, \"cell-\".concat(index), false, {\n                                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\GenerateProposal.tsx\",\n                                                                        lineNumber: 239,\n                                                                        columnNumber: 25\n                                                                    }, undefined))\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\GenerateProposal.tsx\",\n                                                                lineNumber: 230,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_12__.Tooltip, {}, void 0, false, {\n                                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\GenerateProposal.tsx\",\n                                                                lineNumber: 242,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\GenerateProposal.tsx\",\n                                                        lineNumber: 229,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\GenerateProposal.tsx\",\n                                                    lineNumber: 228,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\GenerateProposal.tsx\",\n                                            lineNumber: 226,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: company.planFitData.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between p-3 rounded-lg\",\n                                                    style: {\n                                                        backgroundColor: \"\".concat(item.color, \"10\")\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-4 h-4 rounded mr-3\",\n                                                                    style: {\n                                                                        backgroundColor: item.color\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\GenerateProposal.tsx\",\n                                                                    lineNumber: 250,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium\",\n                                                                    children: [\n                                                                        item.name,\n                                                                        \" Coverage\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\GenerateProposal.tsx\",\n                                                                    lineNumber: 251,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\GenerateProposal.tsx\",\n                                                            lineNumber: 249,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-lg font-bold\",\n                                                            style: {\n                                                                color: item.color\n                                                            },\n                                                            children: [\n                                                                item.value,\n                                                                \"%\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\GenerateProposal.tsx\",\n                                                            lineNumber: 253,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, index, true, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\GenerateProposal.tsx\",\n                                                    lineNumber: 248,\n                                                    columnNumber: 19\n                                                }, undefined))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\GenerateProposal.tsx\",\n                                            lineNumber: 246,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\GenerateProposal.tsx\",\n                                    lineNumber: 225,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\GenerateProposal.tsx\",\n                                lineNumber: 224,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\GenerateProposal.tsx\",\n                        lineNumber: 215,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        className: \"mb-6 shadow-lg border-0\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-orange-100 p-2 rounded-lg mr-3\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_BarChart3_Calendar_CheckCircle_Download_RefreshCw_Share2_Target_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                className: \"h-5 w-5 text-orange-600\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\GenerateProposal.tsx\",\n                                                lineNumber: 266,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\GenerateProposal.tsx\",\n                                            lineNumber: 265,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        \"Key Insights\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\GenerateProposal.tsx\",\n                                    lineNumber: 264,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\GenerateProposal.tsx\",\n                                lineNumber: 263,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid md:grid-cols-2 gap-4\",\n                                    children: company.keyInsights.map((insight, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-start p-4 bg-blue-50 rounded-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_BarChart3_Calendar_CheckCircle_Download_RefreshCw_Share2_Target_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                    className: \"h-5 w-5 text-blue-600 mr-3 mt-0.5 flex-shrink-0\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\GenerateProposal.tsx\",\n                                                    lineNumber: 275,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-700\",\n                                                    children: insight\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\GenerateProposal.tsx\",\n                                                    lineNumber: 276,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, index, true, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\GenerateProposal.tsx\",\n                                            lineNumber: 274,\n                                            columnNumber: 17\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\GenerateProposal.tsx\",\n                                    lineNumber: 272,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\GenerateProposal.tsx\",\n                                lineNumber: 271,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\GenerateProposal.tsx\",\n                        lineNumber: 262,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        className: \"mb-6 shadow-lg border-0\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-green-100 p-2 rounded-lg mr-3\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_BarChart3_Calendar_CheckCircle_Download_RefreshCw_Share2_Target_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                className: \"h-5 w-5 text-green-600\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\GenerateProposal.tsx\",\n                                                lineNumber: 288,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\GenerateProposal.tsx\",\n                                            lineNumber: 287,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        \"Recommendation Summary\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\GenerateProposal.tsx\",\n                                    lineNumber: 286,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\GenerateProposal.tsx\",\n                                lineNumber: 285,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: company.recommendations.map((rec, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                            className: \"border-l-4 border-l-green-500\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                                className: \"p-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-semibold text-gray-900 mb-2\",\n                                                        children: rec.category\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\GenerateProposal.tsx\",\n                                                        lineNumber: 298,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-700 mb-2\",\n                                                        children: rec.description\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\GenerateProposal.tsx\",\n                                                        lineNumber: 299,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-green-600 font-medium\",\n                                                        children: rec.impact\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\GenerateProposal.tsx\",\n                                                        lineNumber: 300,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\GenerateProposal.tsx\",\n                                                lineNumber: 297,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, index, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\GenerateProposal.tsx\",\n                                            lineNumber: 296,\n                                            columnNumber: 17\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\GenerateProposal.tsx\",\n                                    lineNumber: 294,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\GenerateProposal.tsx\",\n                                lineNumber: 293,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\GenerateProposal.tsx\",\n                        lineNumber: 284,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        className: \"mb-6 shadow-lg border-0\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-indigo-100 p-2 rounded-lg mr-3\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_BarChart3_Calendar_CheckCircle_Download_RefreshCw_Share2_Target_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                className: \"h-5 w-5 text-indigo-600\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\GenerateProposal.tsx\",\n                                                lineNumber: 313,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\GenerateProposal.tsx\",\n                                            lineNumber: 312,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        \"Industry Benchmark\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\GenerateProposal.tsx\",\n                                    lineNumber: 311,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\GenerateProposal.tsx\",\n                                lineNumber: 310,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid md:grid-cols-3 gap-6 text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-4 bg-red-50 rounded-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"text-sm font-medium text-gray-500 mb-2\",\n                                                    children: \"Current Position\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\GenerateProposal.tsx\",\n                                                    lineNumber: 321,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold text-red-600\",\n                                                    children: [\n                                                        company.industryBenchmark.currentPercentile,\n                                                        \"th\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\GenerateProposal.tsx\",\n                                                    lineNumber: 322,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: \"Percentile\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\GenerateProposal.tsx\",\n                                                    lineNumber: 323,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\GenerateProposal.tsx\",\n                                            lineNumber: 320,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-4 bg-green-50 rounded-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"text-sm font-medium text-gray-500 mb-2\",\n                                                    children: \"Projected Position\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\GenerateProposal.tsx\",\n                                                    lineNumber: 326,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold text-green-600\",\n                                                    children: [\n                                                        company.industryBenchmark.projectedPercentile,\n                                                        \"th\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\GenerateProposal.tsx\",\n                                                    lineNumber: 327,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: \"Percentile\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\GenerateProposal.tsx\",\n                                                    lineNumber: 328,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\GenerateProposal.tsx\",\n                                            lineNumber: 325,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-4 bg-blue-50 rounded-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"text-sm font-medium text-gray-500 mb-2\",\n                                                    children: \"Industry Average Savings\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\GenerateProposal.tsx\",\n                                                    lineNumber: 331,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold text-blue-600\",\n                                                    children: company.industryBenchmark.avgSavings\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\GenerateProposal.tsx\",\n                                                    lineNumber: 332,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: \"Annual\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\GenerateProposal.tsx\",\n                                                    lineNumber: 333,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\GenerateProposal.tsx\",\n                                            lineNumber: 330,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\GenerateProposal.tsx\",\n                                    lineNumber: 319,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\GenerateProposal.tsx\",\n                                lineNumber: 318,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\GenerateProposal.tsx\",\n                        lineNumber: 309,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        className: \"bg-gradient-to-r from-blue-100 to-purple-100 border-0 shadow-lg\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                            className: \"p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-xl font-bold text-gray-900 mb-4 text-center\",\n                                    children: \"Next Steps\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\GenerateProposal.tsx\",\n                                    lineNumber: 342,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid sm:grid-cols-2 lg:grid-cols-4 gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            className: \"bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_BarChart3_Calendar_CheckCircle_Download_RefreshCw_Share2_Target_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                    className: \"h-4 w-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\GenerateProposal.tsx\",\n                                                    lineNumber: 345,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                \"Generate PDF\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\GenerateProposal.tsx\",\n                                            lineNumber: 344,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            variant: \"outline\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_BarChart3_Calendar_CheckCircle_Download_RefreshCw_Share2_Target_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                    className: \"h-4 w-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\GenerateProposal.tsx\",\n                                                    lineNumber: 349,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                \"Schedule Presentation\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\GenerateProposal.tsx\",\n                                            lineNumber: 348,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            variant: \"outline\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_BarChart3_Calendar_CheckCircle_Download_RefreshCw_Share2_Target_Users_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                    className: \"h-4 w-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\GenerateProposal.tsx\",\n                                                    lineNumber: 353,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                \"Share with Client\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\GenerateProposal.tsx\",\n                                            lineNumber: 352,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            variant: \"outline\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_BarChart3_Calendar_CheckCircle_Download_RefreshCw_Share2_Target_Users_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                    className: \"h-4 w-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\GenerateProposal.tsx\",\n                                                    lineNumber: 357,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                \"Re-run Analysis\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\GenerateProposal.tsx\",\n                                            lineNumber: 356,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\GenerateProposal.tsx\",\n                                    lineNumber: 343,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\GenerateProposal.tsx\",\n                            lineNumber: 341,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\GenerateProposal.tsx\",\n                        lineNumber: 340,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\GenerateProposal.tsx\",\n                lineNumber: 126,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\GenerateProposal.tsx\",\n        lineNumber: 105,\n        columnNumber: 5\n    }, undefined);\n};\n_s(GenerateProposal, \"1JEIvPRIKJKnaO45RHf/GR9vcR4=\", false, function() {\n    return [\n        _lib_react_router_dom__WEBPACK_IMPORTED_MODULE_1__.useParams,\n        _lib_react_router_dom__WEBPACK_IMPORTED_MODULE_1__.useNavigate\n    ];\n});\n_c = GenerateProposal;\n/* harmony default export */ __webpack_exports__[\"default\"] = (GenerateProposal);\nvar _c;\n$RefreshReg$(_c, \"GenerateProposal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/census/public/GenerateProposal.tsx\n"));

/***/ })

});