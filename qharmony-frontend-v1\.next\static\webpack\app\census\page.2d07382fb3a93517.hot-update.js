"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/census/page",{

/***/ "(app-pages-browser)/./src/app/census/public/BrokerDashboard.tsx":
/*!***************************************************!*\
  !*** ./src/app/census/public/BrokerDashboard.tsx ***!
  \***************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../components/ui/button */ \"(app-pages-browser)/./src/app/census/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../components/ui/card */ \"(app-pages-browser)/./src/app/census/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../components/ui/input */ \"(app-pages-browser)/./src/app/census/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../components/ui/select */ \"(app-pages-browser)/./src/app/census/components/ui/select.tsx\");\n/* harmony import */ var _lib_react_router_dom__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../lib/react-router-dom */ \"(app-pages-browser)/./src/app/census/lib/react-router-dom.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_DollarSign_Lock_Mail_Plus_Search_Share2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,DollarSign,Lock,Mail,Plus,Search,Share2,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_DollarSign_Lock_Mail_Plus_Search_Share2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,DollarSign,Lock,Mail,Plus,Search,Share2,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_DollarSign_Lock_Mail_Plus_Search_Share2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,DollarSign,Lock,Mail,Plus,Search,Share2,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_DollarSign_Lock_Mail_Plus_Search_Share2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,DollarSign,Lock,Mail,Plus,Search,Share2,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_DollarSign_Lock_Mail_Plus_Search_Share2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,DollarSign,Lock,Mail,Plus,Search,Share2,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_DollarSign_Lock_Mail_Plus_Search_Share2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,DollarSign,Lock,Mail,Plus,Search,Share2,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_DollarSign_Lock_Mail_Plus_Search_Share2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,DollarSign,Lock,Mail,Plus,Search,Share2,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_DollarSign_Lock_Mail_Plus_Search_Share2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,DollarSign,Lock,Mail,Plus,Search,Share2,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/lock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_DollarSign_Lock_Mail_Plus_Search_Share2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,DollarSign,Lock,Mail,Plus,Search,Share2,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/share-2.js\");\n/* harmony import */ var _components_AskBrea__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../components/AskBrea */ \"(app-pages-browser)/./src/app/census/components/AskBrea.tsx\");\n/* harmony import */ var _components_ProfileSettingsModal__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../components/ProfileSettingsModal */ \"(app-pages-browser)/./src/app/census/components/ProfileSettingsModal.tsx\");\n/* harmony import */ var _hooks_usePlanRestrictions__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../hooks/usePlanRestrictions */ \"(app-pages-browser)/./src/app/census/hooks/usePlanRestrictions.tsx\");\n/* harmony import */ var _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../components/ui/use-toast */ \"(app-pages-browser)/./src/app/census/components/ui/use-toast.ts\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n// import NavigationDropdown from \"../components/NavigationDropdown\"; // Temporarily removed to fix circular import\n\n\n\nconst BrokerDashboard = ()=>{\n    _s();\n    const navigate = (0,_lib_react_router_dom__WEBPACK_IMPORTED_MODULE_6__.useNavigate)();\n    const { toast } = (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_10__.useToast)();\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [employeeCountFilter, setEmployeeCountFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [riskScoreFilter, setRiskScoreFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [suggestedPlanFilter, setSuggestedPlanFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [isProfileModalOpen, setIsProfileModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { canViewReport, reportsRemaining, isAtLimit, trackReportView } = (0,_hooks_usePlanRestrictions__WEBPACK_IMPORTED_MODULE_9__.usePlanRestrictions)();\n    // Mock data for multiple employers\n    const employerInsights = [\n        {\n            id: \"1\",\n            companyName: \"TechCorp Solutions\",\n            employees: 43,\n            averageAge: 36,\n            dependents: 1.3,\n            planType: \"PPO + HSA Combo\",\n            potentialSavings: \"$127,500\",\n            riskScore: 6.2,\n            uploadDate: \"2024-01-15\",\n            status: \"analyzed\"\n        },\n        {\n            id: \"2\",\n            companyName: \"Green Manufacturing\",\n            employees: 87,\n            averageAge: 42,\n            dependents: 1.8,\n            planType: \"Traditional PPO\",\n            potentialSavings: \"$245,000\",\n            riskScore: 7.1,\n            uploadDate: \"2024-01-10\",\n            status: \"analyzed\"\n        },\n        {\n            id: \"3\",\n            companyName: \"StartupXYZ\",\n            employees: 18,\n            averageAge: 29,\n            dependents: 0.8,\n            planType: \"HSA Only\",\n            potentialSavings: \"$32,400\",\n            riskScore: 4.5,\n            uploadDate: \"2024-01-18\",\n            status: \"processing\"\n        }\n    ];\n    // Filter logic\n    const filteredEmployers = employerInsights.filter((employer)=>{\n        // Search filter\n        if (searchTerm && !employer.companyName.toLowerCase().includes(searchTerm.toLowerCase())) {\n            return false;\n        }\n        // Employee count filter\n        if (employeeCountFilter !== \"all\") {\n            if (employeeCountFilter === \"1-50\" && employer.employees > 50) return false;\n            if (employeeCountFilter === \"51-100\" && (employer.employees < 51 || employer.employees > 100)) return false;\n            if (employeeCountFilter === \"101+\" && employer.employees <= 100) return false;\n        }\n        // Risk score filter\n        if (riskScoreFilter !== \"all\") {\n            if (riskScoreFilter === \"low\" && employer.riskScore >= 5) return false;\n            if (riskScoreFilter === \"medium\" && (employer.riskScore < 5 || employer.riskScore > 7)) return false;\n            if (riskScoreFilter === \"high\" && employer.riskScore <= 7) return false;\n        }\n        // Suggested plan filter\n        if (suggestedPlanFilter !== \"all\") {\n            if (suggestedPlanFilter === \"ppo-hsa\" && employer.planType !== \"PPO + HSA Combo\") return false;\n            if (suggestedPlanFilter === \"traditional-ppo\" && employer.planType !== \"Traditional PPO\") return false;\n            if (suggestedPlanFilter === \"other\" && employer.planType !== \"HSA Only\" && employer.planType !== \"Modern HSA Plus Plan\") return false;\n        }\n        return true;\n    });\n    const handleShareInsight = (companyName)=>{\n        const shareUrl = \"\".concat(window.location.origin, \"/shared-insight/\").concat(companyName.toLowerCase().replace(/\\s+/g, \"-\"));\n        navigator.clipboard.writeText(shareUrl);\n        console.log(\"Share link copied for \".concat(companyName));\n    };\n    const handleTileClick = (employerId, companyName)=>{\n        console.log(\"Attempting to view report for \".concat(companyName, \" (ID: \").concat(employerId, \")\"));\n        console.log(\"Can view report: \".concat(canViewReport(employerId)));\n        console.log(\"Reports remaining: \".concat(reportsRemaining));\n        console.log(\"Is at limit: \".concat(isAtLimit));\n        if (!canViewReport(employerId)) {\n            toast({\n                title: \"Upgrade Required\",\n                description: \"You've reached your free report limit (2 reports). Upgrade to Pro for unlimited access.\",\n                variant: \"destructive\"\n            });\n            navigate(\"/pricing\");\n            return;\n        }\n        // Track the report view\n        trackReportView(employerId);\n        navigate(\"/employer-insight/\".concat(employerId));\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-slate-50 to-blue-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"border-b bg-white/90 backdrop-blur-sm sticky top-0 z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto px-3 sm:px-4 py-3\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-xl sm:text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent\",\n                                children: \"BenOsphere\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                lineNumber: 131,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2 sm:space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AskBrea__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        context: \"broker dashboard with multiple client insights\",\n                                        size: \"sm\",\n                                        variant: \"outline\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                        lineNumber: 136,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        variant: \"outline\",\n                                        size: \"sm\",\n                                        onClick: ()=>setIsProfileModalOpen(true),\n                                        className: \"hidden sm:inline-flex\",\n                                        children: \"John Broker\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                        lineNumber: 137,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                lineNumber: 134,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                        lineNumber: 130,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                    lineNumber: 129,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                lineNumber: 128,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"container mx-auto px-3 sm:px-4 py-4 sm:py-6 max-w-7xl\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col lg:flex-row lg:justify-between lg:items-start gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-2xl sm:text-3xl font-bold text-gray-900 mb-2\",\n                                                children: \"\\uD83D\\uDCCA Broker Dashboard\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                lineNumber: 155,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600 text-sm sm:text-base mb-4 lg:mb-0\",\n                                                children: \"Manage and analyze your client census data\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                lineNumber: 158,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                        lineNumber: 154,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col sm:flex-row gap-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                size: \"sm\",\n                                                onClick: ()=>navigate(\"/upload-census\"),\n                                                className: \"bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_DollarSign_Lock_Mail_Plus_Search_Share2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        className: \"h-4 w-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                        lineNumber: 170,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    \"Upload New Census\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                lineNumber: 165,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                variant: \"outline\",\n                                                size: \"sm\",\n                                                onClick: ()=>navigate(\"/employer-invite\"),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_DollarSign_Lock_Mail_Plus_Search_Share2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        className: \"h-4 w-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                        lineNumber: 174,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    \"Invite Employer\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                lineNumber: 173,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                        lineNumber: 164,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                lineNumber: 153,\n                                columnNumber: 11\n                            }, undefined),\n                            reportsRemaining <= 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                className: \"mt-4 bg-gradient-to-r from-amber-50 to-orange-50 border-amber-200\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    className: \"p-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-start space-x-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_DollarSign_Lock_Mail_Plus_Search_Share2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                        className: \"h-5 w-5 text-amber-600 mt-0.5 flex-shrink-0\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                        lineNumber: 186,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"font-medium text-amber-800\",\n                                                                children: isAtLimit ? \"Report limit reached!\" : \"\".concat(reportsRemaining, \" free report\").concat(reportsRemaining === 1 ? \"\" : \"s\", \" remaining\")\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                lineNumber: 188,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-amber-700\",\n                                                                children: isAtLimit ? \"Upgrade to Pro for unlimited reports\" : \"Upgrade to Pro for unlimited access\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                lineNumber: 191,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                        lineNumber: 187,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                lineNumber: 185,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                onClick: ()=>navigate(\"/pricing\"),\n                                                className: \"bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white flex-shrink-0\",\n                                                size: \"sm\",\n                                                children: \"Upgrade Now\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                lineNumber: 196,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                        lineNumber: 184,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                    lineNumber: 183,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                lineNumber: 182,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                        lineNumber: 152,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        className: \"mb-6 shadow-lg border-0\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                            className: \"p-4 sm:p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_DollarSign_Lock_Mail_Plus_Search_Share2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                className: \"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                lineNumber: 215,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                placeholder: \"Search by Client Name\",\n                                                value: searchTerm,\n                                                onChange: (e)=>setSearchTerm(e.target.value),\n                                                className: \"pl-10\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                lineNumber: 216,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                        lineNumber: 214,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 sm:grid-cols-3 gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"text-sm font-medium text-gray-700 mb-2 block\",\n                                                        children: \"Employee Count\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                        lineNumber: 227,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.Select, {\n                                                        value: employeeCountFilter,\n                                                        onValueChange: setEmployeeCountFilter,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectTrigger, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectValue, {\n                                                                    placeholder: \"All\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                    lineNumber: 230,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                lineNumber: 229,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectContent, {\n                                                                className: \"bg-white\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                                        value: \"all\",\n                                                                        children: \"All\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                        lineNumber: 233,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                                        value: \"1-50\",\n                                                                        children: \"1–50\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                        lineNumber: 234,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                                        value: \"51-100\",\n                                                                        children: \"51–100\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                        lineNumber: 235,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                                        value: \"101+\",\n                                                                        children: \"101+\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                        lineNumber: 236,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                lineNumber: 232,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                        lineNumber: 228,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                lineNumber: 226,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"text-sm font-medium text-gray-700 mb-2 block\",\n                                                        children: \"Risk Score\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                        lineNumber: 242,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.Select, {\n                                                        value: riskScoreFilter,\n                                                        onValueChange: setRiskScoreFilter,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectTrigger, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectValue, {\n                                                                    placeholder: \"All\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                    lineNumber: 245,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                lineNumber: 244,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectContent, {\n                                                                className: \"bg-white\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                                        value: \"all\",\n                                                                        children: \"All\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                        lineNumber: 248,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                                        value: \"low\",\n                                                                        children: \"Low (<5)\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                        lineNumber: 249,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                                        value: \"medium\",\n                                                                        children: \"Medium (5–7)\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                        lineNumber: 250,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                                        value: \"high\",\n                                                                        children: \"High (>7)\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                        lineNumber: 251,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                lineNumber: 247,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                        lineNumber: 243,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                lineNumber: 241,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"text-sm font-medium text-gray-700 mb-2 block\",\n                                                        children: \"Suggested Plan\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                        lineNumber: 257,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.Select, {\n                                                        value: suggestedPlanFilter,\n                                                        onValueChange: setSuggestedPlanFilter,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectTrigger, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectValue, {\n                                                                    placeholder: \"All\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                    lineNumber: 260,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                lineNumber: 259,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectContent, {\n                                                                className: \"bg-white\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                                        value: \"all\",\n                                                                        children: \"All\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                        lineNumber: 263,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                                        value: \"ppo-hsa\",\n                                                                        children: \"PPO + HSA Combo\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                        lineNumber: 264,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                                        value: \"traditional-ppo\",\n                                                                        children: \"Traditional PPO\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                        lineNumber: 265,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                                        value: \"other\",\n                                                                        children: \"Other\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                        lineNumber: 266,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                lineNumber: 262,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                        lineNumber: 258,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                lineNumber: 256,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                        lineNumber: 225,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                lineNumber: 212,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                            lineNumber: 211,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                        lineNumber: 210,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4 mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                className: \"bg-gradient-to-br from-blue-100 to-blue-200 border-blue-300 hover:shadow-md transition-all duration-200\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    className: \"p-3 sm:p-4 text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_DollarSign_Lock_Mail_Plus_Search_Share2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            className: \"h-6 w-6 sm:h-8 sm:w-8 mx-auto mb-1 sm:mb-2 text-blue-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                            lineNumber: 279,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs sm:text-sm text-blue-600 font-medium\",\n                                            children: \"Total Clients\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                            lineNumber: 280,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xl sm:text-2xl font-bold text-blue-900\",\n                                            children: filteredEmployers.length\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                            lineNumber: 281,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                    lineNumber: 278,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                lineNumber: 277,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                className: \"bg-gradient-to-br from-emerald-100 to-teal-200 border-emerald-300 hover:shadow-md transition-all duration-200\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    className: \"p-3 sm:p-4 text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_DollarSign_Lock_Mail_Plus_Search_Share2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            className: \"h-6 w-6 sm:h-8 sm:w-8 mx-auto mb-1 sm:mb-2 text-emerald-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                            lineNumber: 287,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs sm:text-sm text-emerald-600 font-medium\",\n                                            children: \"Total Employees\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                            lineNumber: 288,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xl sm:text-2xl font-bold text-emerald-900\",\n                                            children: filteredEmployers.reduce((sum, emp)=>sum + emp.employees, 0)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                            lineNumber: 289,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                    lineNumber: 286,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                lineNumber: 285,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                className: \"bg-gradient-to-br from-orange-100 to-red-200 border-orange-300 hover:shadow-md transition-all duration-200\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    className: \"p-3 sm:p-4 text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_DollarSign_Lock_Mail_Plus_Search_Share2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            className: \"h-6 w-6 sm:h-8 sm:w-8 mx-auto mb-1 sm:mb-2 text-orange-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                            lineNumber: 297,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs sm:text-sm text-orange-600 font-medium\",\n                                            children: \"Total Potential Savings\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                            lineNumber: 298,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-lg sm:text-2xl font-bold text-orange-900\",\n                                            children: \"$404,900\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                            lineNumber: 299,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                    lineNumber: 296,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                lineNumber: 295,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                className: \"bg-gradient-to-br from-purple-100 to-pink-200 border-purple-300 hover:shadow-md transition-all duration-200\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    className: \"p-3 sm:p-4 text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_DollarSign_Lock_Mail_Plus_Search_Share2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            className: \"h-6 w-6 sm:h-8 sm:w-8 mx-auto mb-1 sm:mb-2 text-purple-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                            lineNumber: 305,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs sm:text-sm text-purple-600 font-medium\",\n                                            children: \"Avg Risk Score\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                            lineNumber: 306,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xl sm:text-2xl font-bold text-purple-900\",\n                                            children: \"5.9/10\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                            lineNumber: 307,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                    lineNumber: 304,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                lineNumber: 303,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                        lineNumber: 276,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        className: \"shadow-lg border-0\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                className: \"pb-3 sm:pb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                    className: \"text-lg sm:text-xl\",\n                                    children: \"Client Census Insights\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                    lineNumber: 315,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                lineNumber: 314,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                className: \"p-3 sm:p-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3 sm:space-y-4\",\n                                    children: filteredEmployers.map((employer)=>{\n                                        const canAccess = canViewReport(employer.id);\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                            className: \"border-l-4 border-l-blue-500 transition-all duration-200 cursor-pointer \".concat(canAccess ? \"hover:shadow-md hover:-translate-y-0.5\" : \"opacity-60 hover:opacity-80 cursor-not-allowed\", \" \").concat(!canAccess ? \"relative\" : \"\"),\n                                            onClick: ()=>handleTileClick(employer.id, employer.companyName),\n                                            children: [\n                                                !canAccess && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute inset-0 bg-gray-200/50 backdrop-blur-[1px] rounded-lg flex items-center justify-center z-10\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-white p-3 rounded-lg shadow-lg flex items-center space-x-2 border border-amber-200\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_DollarSign_Lock_Mail_Plus_Search_Share2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                className: \"h-4 w-4 text-amber-600\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                lineNumber: 335,\n                                                                columnNumber: 27\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm font-medium text-amber-800\",\n                                                                children: \"Upgrade to view\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                lineNumber: 336,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                        lineNumber: 334,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                    lineNumber: 333,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                                    className: \"p-4 sm:p-5\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex flex-col sm:flex-row justify-between items-start mb-3 sm:mb-4 gap-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                            className: \"text-lg sm:text-xl font-semibold text-gray-900 mb-1\",\n                                                                            children: employer.companyName\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                            lineNumber: 344,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-xs sm:text-sm text-gray-500\",\n                                                                            children: [\n                                                                                \"Uploaded: \",\n                                                                                new Date(employer.uploadDate).toLocaleDateString()\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                            lineNumber: 347,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                    lineNumber: 343,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex space-x-2 w-full sm:w-auto\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                        size: \"sm\",\n                                                                        variant: \"outline\",\n                                                                        onClick: (e)=>{\n                                                                            e.stopPropagation();\n                                                                            if (canAccess) {\n                                                                                handleShareInsight(employer.companyName);\n                                                                            } else {\n                                                                                navigate(\"/pricing\");\n                                                                            }\n                                                                        },\n                                                                        className: \"flex-1 sm:flex-none\",\n                                                                        disabled: !canAccess,\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_DollarSign_Lock_Mail_Plus_Search_Share2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                                className: \"h-3 w-3 sm:h-4 sm:w-4 sm:mr-1\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                                lineNumber: 366,\n                                                                                columnNumber: 29\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"hidden sm:inline\",\n                                                                                children: \"Share\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                                lineNumber: 367,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                        lineNumber: 352,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                    lineNumber: 351,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                            lineNumber: 342,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"grid grid-cols-2 lg:grid-cols-4 gap-2 sm:gap-3 mb-3 sm:mb-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-center p-2 sm:p-3 bg-gradient-to-br from-blue-50 to-blue-100 rounded-lg hover:scale-105 transition-transform duration-200\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-xs sm:text-sm text-blue-600 font-medium\",\n                                                                            children: \"Employees\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                            lineNumber: 374,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm sm:text-lg font-bold text-blue-900\",\n                                                                            children: employer.employees\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                            lineNumber: 375,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                    lineNumber: 373,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-center p-2 sm:p-3 bg-gradient-to-br from-emerald-50 to-emerald-100 rounded-lg hover:scale-105 transition-transform duration-200\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-xs sm:text-sm text-emerald-600 font-medium\",\n                                                                            children: \"Avg Age\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                            lineNumber: 378,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm sm:text-lg font-bold text-emerald-900\",\n                                                                            children: employer.averageAge\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                            lineNumber: 379,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                    lineNumber: 377,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-center p-2 sm:p-3 bg-gradient-to-br from-orange-50 to-orange-100 rounded-lg hover:scale-105 transition-transform duration-200\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-xs sm:text-sm text-orange-600 font-medium\",\n                                                                            children: \"Savings\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                            lineNumber: 382,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm sm:text-lg font-bold text-orange-900\",\n                                                                            children: employer.potentialSavings\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                            lineNumber: 383,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                    lineNumber: 381,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-center p-2 sm:p-3 bg-gradient-to-br from-purple-50 to-purple-100 rounded-lg hover:scale-105 transition-transform duration-200\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-xs sm:text-sm text-purple-600 font-medium\",\n                                                                            children: \"Risk Score\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                            lineNumber: 386,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm sm:text-lg font-bold text-purple-900\",\n                                                                            children: [\n                                                                                employer.riskScore,\n                                                                                \"/10\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                            lineNumber: 387,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                    lineNumber: 385,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                            lineNumber: 372,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex flex-col sm:flex-row justify-between items-start sm:items-center gap-2 sm:gap-0\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-sm\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-gray-600\",\n                                                                            children: \"Suggested Plan: \"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                            lineNumber: 393,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"font-medium text-gray-900\",\n                                                                            children: employer.planType\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                            lineNumber: 394,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                    lineNumber: 392,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"px-2 py-1 rounded-full text-xs font-medium \".concat(employer.status === \"analyzed\" ? \"bg-green-100 text-green-800\" : \"bg-yellow-100 text-yellow-800\"),\n                                                                    children: employer.status === \"analyzed\" ? \"✅ Analyzed\" : \"⏳ Processing\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                    lineNumber: 396,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                            lineNumber: 391,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                    lineNumber: 341,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, employer.id, true, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                            lineNumber: 323,\n                                            columnNumber: 19\n                                        }, undefined);\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                    lineNumber: 318,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                lineNumber: 317,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                        lineNumber: 313,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        className: \"mt-6 bg-gradient-to-r from-purple-100 to-blue-100 border-0 shadow-lg\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                            className: \"p-4 sm:p-6 text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg sm:text-xl font-bold text-gray-900 mb-2\",\n                                    children: \"\\uD83D\\uDE80 Grow Your Network\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                    lineNumber: 415,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-700 mb-4 text-sm sm:text-base\",\n                                    children: \"Share BenOsphere with other brokers and get rewards for every signup\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                    lineNumber: 418,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col sm:flex-row justify-center space-y-2 sm:space-y-0 sm:space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            className: \"bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white transition-all duration-200 hover:scale-105\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_DollarSign_Lock_Mail_Plus_Search_Share2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                    className: \"h-4 w-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                    lineNumber: 423,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                \"Refer a Broker\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                            lineNumber: 422,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            variant: \"outline\",\n                                            className: \"hover:bg-white/50\",\n                                            children: \"View Referral Rewards\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                            lineNumber: 426,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                    lineNumber: 421,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                            lineNumber: 414,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                        lineNumber: 413,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                lineNumber: 150,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ProfileSettingsModal__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                isOpen: isProfileModalOpen,\n                onClose: ()=>setIsProfileModalOpen(false)\n            }, void 0, false, {\n                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                lineNumber: 434,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n        lineNumber: 126,\n        columnNumber: 5\n    }, undefined);\n};\n_s(BrokerDashboard, \"+IpQEQnmvmvy0dLILAAtEqWyWWA=\", false, function() {\n    return [\n        _lib_react_router_dom__WEBPACK_IMPORTED_MODULE_6__.useNavigate,\n        _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_10__.useToast,\n        _hooks_usePlanRestrictions__WEBPACK_IMPORTED_MODULE_9__.usePlanRestrictions\n    ];\n});\n_c = BrokerDashboard;\n/* harmony default export */ __webpack_exports__[\"default\"] = (BrokerDashboard);\nvar _c;\n$RefreshReg$(_c, \"BrokerDashboard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/census/public/BrokerDashboard.tsx\n"));

/***/ })

});