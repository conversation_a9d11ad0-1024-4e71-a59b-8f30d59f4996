"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/census/page",{

/***/ "(app-pages-browser)/./src/app/census/components/AskBrea.tsx":
/*!***********************************************!*\
  !*** ./src/app/census/components/AskBrea.tsx ***!
  \***********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ui/button */ \"(app-pages-browser)/./src/app/census/components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_MessageCircle_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=MessageCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-circle.js\");\n\nvar _s = $RefreshSig$();\n\n\n\nconst AskBrea = (param)=>{\n    let { context = \"\", size = \"default\", variant = \"default\" } = param;\n    _s();\n    const [isOpen, setIsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleAskQuestion = async ()=>{\n        if (!question.trim()) {\n            toast({\n                title: \"Question Required\",\n                description: \"Please enter a question to ask Brea.\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        setIsLoading(true);\n        try {\n            // Simulate AI response - in a real app this would call an AI API\n            await new Promise((resolve)=>setTimeout(resolve, 2000));\n            const mockResponses = [\n                \"Based on the \".concat(context, \", I recommend focusing on comprehensive coverage options that balance cost and benefits. The data suggests that a PPO + HSA combination would provide optimal value for most employees while maintaining flexibility.\"),\n                \"Looking at the employee demographics and risk factors, I'd suggest prioritizing dental and disability coverage as key upsell opportunities. These gaps represent significant value-add potential for both the broker and the employer.\",\n                \"The risk analysis indicates that this group would benefit from enhanced preventive care programs. Consider wellness initiatives that could reduce overall claims costs while improving employee satisfaction.\",\n                \"Based on the coverage patterns I'm seeing, there's an opportunity to optimize the plan mix. Consider offering tiered options that allow employees to customize their benefits based on individual needs and family situations.\"\n            ];\n            const randomResponse = mockResponses[Math.floor(Math.random() * mockResponses.length)];\n            setResponse(randomResponse);\n        } catch (error) {\n            toast({\n                title: \"Error\",\n                description: \"Failed to get response from Brea. Please try again.\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handleClose = ()=>{\n        setIsOpen(false);\n        setQuestion(\"\");\n        setResponse(\"\");\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Dialog, {\n        open: isOpen,\n        onOpenChange: setIsOpen,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DialogTrigger, {\n                asChild: true,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                    size: size,\n                    variant: variant,\n                    className: \"flex items-center gap-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MessageCircle_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            className: \"h-4 w-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\components\\\\AskBrea.tsx\",\n                            lineNumber: 63,\n                            columnNumber: 11\n                        }, undefined),\n                        \"Ask Brea\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\components\\\\AskBrea.tsx\",\n                    lineNumber: 62,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\components\\\\AskBrea.tsx\",\n                lineNumber: 61,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DialogContent, {\n                className: \"sm:max-w-md\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DialogHeader, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DialogTitle, {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gradient-to-r from-blue-600 to-purple-600 p-2 rounded-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MessageCircle_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            className: \"h-5 w-5 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\components\\\\AskBrea.tsx\",\n                                            lineNumber: 71,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\components\\\\AskBrea.tsx\",\n                                        lineNumber: 70,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    \"Ask Brea AI\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\components\\\\AskBrea.tsx\",\n                                lineNumber: 69,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DialogDescription, {\n                                children: \"Ask Brea any questions about benefits, coverage recommendations, or insights based on the current data.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\components\\\\AskBrea.tsx\",\n                                lineNumber: 75,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\components\\\\AskBrea.tsx\",\n                        lineNumber: 68,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"text-sm font-medium text-gray-700 mb-2 block\",\n                                        children: \"Your Question\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\components\\\\AskBrea.tsx\",\n                                        lineNumber: 82,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Input, {\n                                        placeholder: \"e.g., What coverage gaps should I prioritize for this client?\",\n                                        value: question,\n                                        onChange: (e)=>setQuestion(e.target.value),\n                                        onKeyPress: (e)=>e.key === \"Enter\" && !isLoading && handleAskQuestion()\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\components\\\\AskBrea.tsx\",\n                                        lineNumber: 83,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\components\\\\AskBrea.tsx\",\n                                lineNumber: 81,\n                                columnNumber: 11\n                            }, undefined),\n                            response && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gradient-to-r from-blue-50 to-purple-50 p-4 rounded-lg border-l-4 border-blue-500\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"text-sm font-semibold text-gray-900 mb-2 flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MessageCircle_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                className: \"h-4 w-4 mr-2 text-blue-600\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\components\\\\AskBrea.tsx\",\n                                                lineNumber: 94,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            \"Brea's Response:\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\components\\\\AskBrea.tsx\",\n                                        lineNumber: 93,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-700\",\n                                        children: response\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\components\\\\AskBrea.tsx\",\n                                        lineNumber: 97,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\components\\\\AskBrea.tsx\",\n                                lineNumber: 92,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\components\\\\AskBrea.tsx\",\n                        lineNumber: 80,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DialogFooter, {\n                        className: \"flex justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                variant: \"outline\",\n                                onClick: handleClose,\n                                children: \"Close\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\components\\\\AskBrea.tsx\",\n                                lineNumber: 103,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                onClick: handleAskQuestion,\n                                disabled: isLoading,\n                                children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Loader2, {\n                                            className: \"h-4 w-4 mr-2 animate-spin\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\components\\\\AskBrea.tsx\",\n                                            lineNumber: 109,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        \"Asking Brea...\"\n                                    ]\n                                }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Send, {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\components\\\\AskBrea.tsx\",\n                                            lineNumber: 114,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        \"Ask Question\"\n                                    ]\n                                }, void 0, true)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\components\\\\AskBrea.tsx\",\n                                lineNumber: 106,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\components\\\\AskBrea.tsx\",\n                        lineNumber: 102,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\components\\\\AskBrea.tsx\",\n                lineNumber: 67,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\components\\\\AskBrea.tsx\",\n        lineNumber: 60,\n        columnNumber: 5\n    }, undefined);\n};\n_s(AskBrea, \"+sus0Lb0ewKHdwiUhiTAJFoFyQ0=\");\n_c = AskBrea;\n/* harmony default export */ __webpack_exports__[\"default\"] = (AskBrea);\nvar _c;\n$RefreshReg$(_c, \"AskBrea\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/census/components/AskBrea.tsx\n"));

/***/ })

});