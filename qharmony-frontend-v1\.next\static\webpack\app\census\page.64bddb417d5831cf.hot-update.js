"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/census/page",{

/***/ "(app-pages-browser)/./src/app/census/components/NavigationDropdown.tsx":
/*!**********************************************************!*\
  !*** ./src/app/census/components/NavigationDropdown.tsx ***!
  \**********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ui/button */ \"(app-pages-browser)/./src/app/census/components/ui/button.tsx\");\n/* harmony import */ var _ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./ui/dropdown-menu */ \"(app-pages-browser)/./src/app/census/components/ui/dropdown-menu.tsx\");\n/* harmony import */ var _lib_react_router_dom__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../lib/react-router-dom */ \"(app-pages-browser)/./src/app/census/lib/react-router-dom.tsx\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Menu_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Menu!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Menu_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Menu!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _nav_items__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../nav-items */ \"(app-pages-browser)/./src/app/census/nav-items.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst NavigationDropdown = (param)=>{\n    let { variant = \"outline\", size = \"sm\" } = param;\n    var _navItems_find, _navItems_find1, _navItems_find2, _navItems_find3, _navItems_find4, _navItems_find5, _navItems_find6, _navItems_find7, _navItems_find8, _navItems_find9, _navItems_find10, _navItems_find11;\n    _s();\n    const navigate = (0,_lib_react_router_dom__WEBPACK_IMPORTED_MODULE_4__.useNavigate)();\n    const [isOpen, setIsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Filter out special routes like \"*\" (404) and \"/\" (home) but include login-prompt\n    const mainPages = _nav_items__WEBPACK_IMPORTED_MODULE_5__.navItems.filter((item)=>![\n            \"*\",\n            \"/\"\n        ].includes(item.to));\n    const handleNavigate = (path)=>{\n        // Convert path to query parameter format\n        const page = path.startsWith(\"/\") ? path.substring(1) : path;\n        navigate(\"?page=\".concat(page));\n        setIsOpen(false);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenu, {\n        open: isOpen,\n        onOpenChange: setIsOpen,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenuTrigger, {\n                asChild: true,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                    variant: variant,\n                    size: size,\n                    className: \"flex items-center gap-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Menu_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            className: \"h-4 w-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\components\\\\NavigationDropdown.tsx\",\n                            lineNumber: 34,\n                            columnNumber: 11\n                        }, undefined),\n                        \"Pages\",\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Menu_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            className: \"h-4 w-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\components\\\\NavigationDropdown.tsx\",\n                            lineNumber: 36,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\components\\\\NavigationDropdown.tsx\",\n                    lineNumber: 33,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\components\\\\NavigationDropdown.tsx\",\n                lineNumber: 32,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenuContent, {\n                className: \"w-56 bg-white dark:bg-gray-800 border shadow-lg z-50\",\n                align: \"end\",\n                sideOffset: 5,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenuItem, {\n                        onClick: ()=>handleNavigate(\"/dashboard\"),\n                        className: \"flex items-center gap-2 hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer\",\n                        children: [\n                            (_navItems_find = _nav_items__WEBPACK_IMPORTED_MODULE_5__.navItems.find((item)=>item.to === \"/dashboard\")) === null || _navItems_find === void 0 ? void 0 : _navItems_find.icon,\n                            \"Dashboard\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\components\\\\NavigationDropdown.tsx\",\n                        lineNumber: 45,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenuSeparator, {}, void 0, false, {\n                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\components\\\\NavigationDropdown.tsx\",\n                        lineNumber: 53,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenuItem, {\n                        onClick: ()=>handleNavigate(\"/hr-insight\"),\n                        className: \"flex items-center gap-2 hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer\",\n                        children: [\n                            (_navItems_find1 = _nav_items__WEBPACK_IMPORTED_MODULE_5__.navItems.find((item)=>item.to === \"/hr-insight\")) === null || _navItems_find1 === void 0 ? void 0 : _navItems_find1.icon,\n                            \"HR Insight\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\components\\\\NavigationDropdown.tsx\",\n                        lineNumber: 56,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenuSeparator, {}, void 0, false, {\n                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\components\\\\NavigationDropdown.tsx\",\n                        lineNumber: 64,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenuItem, {\n                        onClick: ()=>handleNavigate(\"/upload-census\"),\n                        className: \"flex items-center gap-2 hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer\",\n                        children: [\n                            (_navItems_find2 = _nav_items__WEBPACK_IMPORTED_MODULE_5__.navItems.find((item)=>item.to === \"/upload-census\")) === null || _navItems_find2 === void 0 ? void 0 : _navItems_find2.icon,\n                            \"Upload Census\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\components\\\\NavigationDropdown.tsx\",\n                        lineNumber: 67,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenuItem, {\n                        onClick: ()=>handleNavigate(\"/file-preview\"),\n                        className: \"flex items-center gap-2 hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer\",\n                        children: [\n                            (_navItems_find3 = _nav_items__WEBPACK_IMPORTED_MODULE_5__.navItems.find((item)=>item.to === \"/file-preview\")) === null || _navItems_find3 === void 0 ? void 0 : _navItems_find3.icon,\n                            \"File Preview\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\components\\\\NavigationDropdown.tsx\",\n                        lineNumber: 75,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenuItem, {\n                        onClick: ()=>handleNavigate(\"/hr-upload\"),\n                        className: \"flex items-center gap-2 hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer\",\n                        children: [\n                            (_navItems_find4 = _nav_items__WEBPACK_IMPORTED_MODULE_5__.navItems.find((item)=>item.to === \"/hr-upload\")) === null || _navItems_find4 === void 0 ? void 0 : _navItems_find4.icon,\n                            \"HR Upload\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\components\\\\NavigationDropdown.tsx\",\n                        lineNumber: 83,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenuItem, {\n                        onClick: ()=>handleNavigate(\"/employer-invite\"),\n                        className: \"flex items-center gap-2 hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer\",\n                        children: [\n                            (_navItems_find5 = _nav_items__WEBPACK_IMPORTED_MODULE_5__.navItems.find((item)=>item.to === \"/employer-invite\")) === null || _navItems_find5 === void 0 ? void 0 : _navItems_find5.icon,\n                            \"Invite Employer\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\components\\\\NavigationDropdown.tsx\",\n                        lineNumber: 91,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenuSeparator, {}, void 0, false, {\n                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\components\\\\NavigationDropdown.tsx\",\n                        lineNumber: 99,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenuItem, {\n                        onClick: ()=>handleNavigate(\"/preview-report\"),\n                        className: \"flex items-center gap-2 hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer\",\n                        children: [\n                            (_navItems_find6 = _nav_items__WEBPACK_IMPORTED_MODULE_5__.navItems.find((item)=>item.to === \"/preview-report\")) === null || _navItems_find6 === void 0 ? void 0 : _navItems_find6.icon,\n                            \"Preview Report\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\components\\\\NavigationDropdown.tsx\",\n                        lineNumber: 102,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenuItem, {\n                        onClick: ()=>handleNavigate(\"/generate-proposal/1\"),\n                        className: \"flex items-center gap-2 hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer\",\n                        children: [\n                            (_navItems_find7 = _nav_items__WEBPACK_IMPORTED_MODULE_5__.navItems.find((item)=>item.to === \"/generate-proposal\")) === null || _navItems_find7 === void 0 ? void 0 : _navItems_find7.icon,\n                            \"Generate Proposal\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\components\\\\NavigationDropdown.tsx\",\n                        lineNumber: 110,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenuItem, {\n                        onClick: ()=>handleNavigate(\"/processing\"),\n                        className: \"flex items-center gap-2 hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer\",\n                        children: [\n                            (_navItems_find8 = _nav_items__WEBPACK_IMPORTED_MODULE_5__.navItems.find((item)=>item.to === \"/processing\")) === null || _navItems_find8 === void 0 ? void 0 : _navItems_find8.icon,\n                            \"Processing\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\components\\\\NavigationDropdown.tsx\",\n                        lineNumber: 118,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenuSeparator, {}, void 0, false, {\n                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\components\\\\NavigationDropdown.tsx\",\n                        lineNumber: 126,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenuItem, {\n                        onClick: ()=>handleNavigate(\"/login-prompt\"),\n                        className: \"flex items-center gap-2 hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer\",\n                        children: [\n                            (_navItems_find9 = _nav_items__WEBPACK_IMPORTED_MODULE_5__.navItems.find((item)=>item.to === \"/login-prompt\")) === null || _navItems_find9 === void 0 ? void 0 : _navItems_find9.icon,\n                            \"Magic Link Sign In\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\components\\\\NavigationDropdown.tsx\",\n                        lineNumber: 129,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenuItem, {\n                        onClick: ()=>handleNavigate(\"/pricing\"),\n                        className: \"flex items-center gap-2 hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer\",\n                        children: [\n                            (_navItems_find10 = _nav_items__WEBPACK_IMPORTED_MODULE_5__.navItems.find((item)=>item.to === \"/pricing\")) === null || _navItems_find10 === void 0 ? void 0 : _navItems_find10.icon,\n                            \"Pricing\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\components\\\\NavigationDropdown.tsx\",\n                        lineNumber: 137,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenuItem, {\n                        onClick: ()=>handleNavigate(\"/billing\"),\n                        className: \"flex items-center gap-2 hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer\",\n                        children: [\n                            (_navItems_find11 = _nav_items__WEBPACK_IMPORTED_MODULE_5__.navItems.find((item)=>item.to === \"/billing\")) === null || _navItems_find11 === void 0 ? void 0 : _navItems_find11.icon,\n                            \"Billing & Subscriptions\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\components\\\\NavigationDropdown.tsx\",\n                        lineNumber: 145,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\components\\\\NavigationDropdown.tsx\",\n                lineNumber: 39,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\components\\\\NavigationDropdown.tsx\",\n        lineNumber: 31,\n        columnNumber: 5\n    }, undefined);\n};\n_s(NavigationDropdown, \"ceGBOeuRCqrr/U+S6OCxyH9+zMA=\", false, function() {\n    return [\n        _lib_react_router_dom__WEBPACK_IMPORTED_MODULE_4__.useNavigate\n    ];\n});\n_c = NavigationDropdown;\n/* harmony default export */ __webpack_exports__[\"default\"] = (NavigationDropdown);\nvar _c;\n$RefreshReg$(_c, \"NavigationDropdown\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/census/components/NavigationDropdown.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/census/nav-items.tsx":
/*!**************************************!*\
  !*** ./src/app/census/nav-items.tsx ***!
  \**************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   navItems: function() { return /* binding */ navItems; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Building2_CreditCard_FileText_HomeIcon_PlusCircle_Settings_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Building2,CreditCard,FileText,HomeIcon,PlusCircle,Settings,Upload,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/house.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Building2_CreditCard_FileText_HomeIcon_PlusCircle_Settings_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Building2,CreditCard,FileText,HomeIcon,PlusCircle,Settings,Upload,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chart-column.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Building2_CreditCard_FileText_HomeIcon_PlusCircle_Settings_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Building2,CreditCard,FileText,HomeIcon,PlusCircle,Settings,Upload,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/building-2.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Building2_CreditCard_FileText_HomeIcon_PlusCircle_Settings_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Building2,CreditCard,FileText,HomeIcon,PlusCircle,Settings,Upload,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Building2_CreditCard_FileText_HomeIcon_PlusCircle_Settings_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Building2,CreditCard,FileText,HomeIcon,PlusCircle,Settings,Upload,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Building2_CreditCard_FileText_HomeIcon_PlusCircle_Settings_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Building2,CreditCard,FileText,HomeIcon,PlusCircle,Settings,Upload,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Building2_CreditCard_FileText_HomeIcon_PlusCircle_Settings_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Building2,CreditCard,FileText,HomeIcon,PlusCircle,Settings,Upload,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Building2_CreditCard_FileText_HomeIcon_PlusCircle_Settings_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Building2,CreditCard,FileText,HomeIcon,PlusCircle,Settings,Upload,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-plus.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Building2_CreditCard_FileText_HomeIcon_PlusCircle_Settings_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Building2,CreditCard,FileText,HomeIcon,PlusCircle,Settings,Upload,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/credit-card.js\");\n/* harmony import */ var _public_Index__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./public/Index */ \"(app-pages-browser)/./src/app/census/public/Index.tsx\");\n/* harmony import */ var _public_BrokerDashboard__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./public/BrokerDashboard */ \"(app-pages-browser)/./src/app/census/public/BrokerDashboard.tsx\");\n/* harmony import */ var _public_EmployerInsight__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./public/EmployerInsight */ \"(app-pages-browser)/./src/app/census/public/EmployerInsight.tsx\");\n/* harmony import */ var _public_HRInsight__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./public/HRInsight */ \"(app-pages-browser)/./src/app/census/public/HRInsight.tsx\");\n/* harmony import */ var _public_UploadCensus__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./public/UploadCensus */ \"(app-pages-browser)/./src/app/census/public/UploadCensus.tsx\");\n/* harmony import */ var _public_HRUpload__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./public/HRUpload */ \"(app-pages-browser)/./src/app/census/public/HRUpload.tsx\");\n/* harmony import */ var _public_EmployerInvite__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./public/EmployerInvite */ \"(app-pages-browser)/./src/app/census/public/EmployerInvite.tsx\");\n/* harmony import */ var _public_PreviewReport__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./public/PreviewReport */ \"(app-pages-browser)/./src/app/census/public/PreviewReport.tsx\");\n/* harmony import */ var _public_Processing__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./public/Processing */ \"(app-pages-browser)/./src/app/census/public/Processing.tsx\");\n/* harmony import */ var _public_Pricing__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./public/Pricing */ \"(app-pages-browser)/./src/app/census/public/Pricing.tsx\");\n/* harmony import */ var _public_Billing__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./public/Billing */ \"(app-pages-browser)/./src/app/census/public/Billing.tsx\");\n/* harmony import */ var _public_GenerateProposal__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./public/GenerateProposal */ \"(app-pages-browser)/./src/app/census/public/GenerateProposal.tsx\");\n/* harmony import */ var _public_LoginPrompt__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./public/LoginPrompt */ \"(app-pages-browser)/./src/app/census/public/LoginPrompt.tsx\");\n/* harmony import */ var _public_NotFound__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ./public/NotFound */ \"(app-pages-browser)/./src/app/census/public/NotFound.tsx\");\n/* harmony import */ var _public_FilePreview__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ./public/FilePreview */ \"(app-pages-browser)/./src/app/census/public/FilePreview.tsx\");\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst navItems = [\n    {\n        title: \"Home\",\n        to: \"/\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Building2_CreditCard_FileText_HomeIcon_PlusCircle_Settings_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n            className: \"h-4 w-4\"\n        }, void 0, false, {\n            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\nav-items.tsx\",\n            lineNumber: 23,\n            columnNumber: 11\n        }, undefined),\n        page: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_public_Index__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {}, void 0, false, {\n            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\nav-items.tsx\",\n            lineNumber: 24,\n            columnNumber: 11\n        }, undefined)\n    },\n    {\n        title: \"Dashboard\",\n        to: \"/dashboard\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Building2_CreditCard_FileText_HomeIcon_PlusCircle_Settings_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n            className: \"h-4 w-4\"\n        }, void 0, false, {\n            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\nav-items.tsx\",\n            lineNumber: 29,\n            columnNumber: 11\n        }, undefined),\n        page: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_public_BrokerDashboard__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\nav-items.tsx\",\n            lineNumber: 30,\n            columnNumber: 11\n        }, undefined)\n    },\n    {\n        title: \"Employer Insight\",\n        to: \"/employer-insight\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Building2_CreditCard_FileText_HomeIcon_PlusCircle_Settings_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n            className: \"h-4 w-4\"\n        }, void 0, false, {\n            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\nav-items.tsx\",\n            lineNumber: 35,\n            columnNumber: 11\n        }, undefined),\n        page: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_public_EmployerInsight__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\nav-items.tsx\",\n            lineNumber: 36,\n            columnNumber: 11\n        }, undefined)\n    },\n    {\n        title: \"HR Insight\",\n        to: \"/hr-insight\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Building2_CreditCard_FileText_HomeIcon_PlusCircle_Settings_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n            className: \"h-4 w-4\"\n        }, void 0, false, {\n            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\nav-items.tsx\",\n            lineNumber: 41,\n            columnNumber: 11\n        }, undefined),\n        page: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_public_HRInsight__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\nav-items.tsx\",\n            lineNumber: 42,\n            columnNumber: 11\n        }, undefined)\n    },\n    {\n        title: \"Upload Census\",\n        to: \"/upload-census\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Building2_CreditCard_FileText_HomeIcon_PlusCircle_Settings_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n            className: \"h-4 w-4\"\n        }, void 0, false, {\n            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\nav-items.tsx\",\n            lineNumber: 47,\n            columnNumber: 11\n        }, undefined),\n        page: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_public_UploadCensus__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\nav-items.tsx\",\n            lineNumber: 48,\n            columnNumber: 11\n        }, undefined)\n    },\n    {\n        title: \"File Preview\",\n        to: \"/file-preview\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Building2_CreditCard_FileText_HomeIcon_PlusCircle_Settings_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n            className: \"h-4 w-4\"\n        }, void 0, false, {\n            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\nav-items.tsx\",\n            lineNumber: 53,\n            columnNumber: 11\n        }, undefined),\n        page: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_public_FilePreview__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {}, void 0, false, {\n            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\nav-items.tsx\",\n            lineNumber: 54,\n            columnNumber: 11\n        }, undefined)\n    },\n    {\n        title: \"HR Upload\",\n        to: \"/hr-upload\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Building2_CreditCard_FileText_HomeIcon_PlusCircle_Settings_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n            className: \"h-4 w-4\"\n        }, void 0, false, {\n            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\nav-items.tsx\",\n            lineNumber: 59,\n            columnNumber: 11\n        }, undefined),\n        page: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_public_HRUpload__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\nav-items.tsx\",\n            lineNumber: 60,\n            columnNumber: 11\n        }, undefined)\n    },\n    {\n        title: \"Employer Invite\",\n        to: \"/employer-invite\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Building2_CreditCard_FileText_HomeIcon_PlusCircle_Settings_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n            className: \"h-4 w-4\"\n        }, void 0, false, {\n            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\nav-items.tsx\",\n            lineNumber: 65,\n            columnNumber: 11\n        }, undefined),\n        page: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_public_EmployerInvite__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\nav-items.tsx\",\n            lineNumber: 66,\n            columnNumber: 11\n        }, undefined)\n    },\n    {\n        title: \"Preview Report\",\n        to: \"/preview-report\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Building2_CreditCard_FileText_HomeIcon_PlusCircle_Settings_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n            className: \"h-4 w-4\"\n        }, void 0, false, {\n            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\nav-items.tsx\",\n            lineNumber: 71,\n            columnNumber: 11\n        }, undefined),\n        page: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_public_PreviewReport__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\nav-items.tsx\",\n            lineNumber: 72,\n            columnNumber: 11\n        }, undefined)\n    },\n    {\n        title: \"Processing\",\n        to: \"/processing\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Building2_CreditCard_FileText_HomeIcon_PlusCircle_Settings_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n            className: \"h-4 w-4\"\n        }, void 0, false, {\n            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\nav-items.tsx\",\n            lineNumber: 77,\n            columnNumber: 11\n        }, undefined),\n        page: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_public_Processing__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {}, void 0, false, {\n            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\nav-items.tsx\",\n            lineNumber: 78,\n            columnNumber: 11\n        }, undefined)\n    },\n    {\n        title: \"Generate Proposal\",\n        to: \"/generate-proposal\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Building2_CreditCard_FileText_HomeIcon_PlusCircle_Settings_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n            className: \"h-4 w-4\"\n        }, void 0, false, {\n            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\nav-items.tsx\",\n            lineNumber: 83,\n            columnNumber: 11\n        }, undefined),\n        page: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_public_GenerateProposal__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {}, void 0, false, {\n            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\nav-items.tsx\",\n            lineNumber: 84,\n            columnNumber: 11\n        }, undefined)\n    },\n    {\n        title: \"Pricing\",\n        to: \"/pricing\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Building2_CreditCard_FileText_HomeIcon_PlusCircle_Settings_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n            className: \"h-4 w-4\"\n        }, void 0, false, {\n            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\nav-items.tsx\",\n            lineNumber: 89,\n            columnNumber: 11\n        }, undefined),\n        page: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_public_Pricing__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {}, void 0, false, {\n            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\nav-items.tsx\",\n            lineNumber: 90,\n            columnNumber: 11\n        }, undefined)\n    },\n    {\n        title: \"Billing\",\n        to: \"/billing\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Building2_CreditCard_FileText_HomeIcon_PlusCircle_Settings_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n            className: \"h-4 w-4\"\n        }, void 0, false, {\n            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\nav-items.tsx\",\n            lineNumber: 95,\n            columnNumber: 11\n        }, undefined),\n        page: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_public_Billing__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {}, void 0, false, {\n            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\nav-items.tsx\",\n            lineNumber: 96,\n            columnNumber: 11\n        }, undefined)\n    },\n    {\n        title: \"Login\",\n        to: \"/login-prompt\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Building2_CreditCard_FileText_HomeIcon_PlusCircle_Settings_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n            className: \"h-4 w-4\"\n        }, void 0, false, {\n            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\nav-items.tsx\",\n            lineNumber: 101,\n            columnNumber: 11\n        }, undefined),\n        page: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_public_LoginPrompt__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {}, void 0, false, {\n            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\nav-items.tsx\",\n            lineNumber: 102,\n            columnNumber: 11\n        }, undefined)\n    },\n    {\n        title: \"Not Found\",\n        to: \"*\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Building2_CreditCard_FileText_HomeIcon_PlusCircle_Settings_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n            className: \"h-4 w-4\"\n        }, void 0, false, {\n            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\nav-items.tsx\",\n            lineNumber: 107,\n            columnNumber: 11\n        }, undefined),\n        page: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_public_NotFound__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {}, void 0, false, {\n            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\nav-items.tsx\",\n            lineNumber: 108,\n            columnNumber: 11\n        }, undefined)\n    }\n];\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/census/nav-items.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/census/public/BrokerDashboard.tsx":
/*!***************************************************!*\
  !*** ./src/app/census/public/BrokerDashboard.tsx ***!
  \***************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../components/ui/button */ \"(app-pages-browser)/./src/app/census/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../components/ui/card */ \"(app-pages-browser)/./src/app/census/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../components/ui/input */ \"(app-pages-browser)/./src/app/census/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../components/ui/select */ \"(app-pages-browser)/./src/app/census/components/ui/select.tsx\");\n/* harmony import */ var _lib_react_router_dom__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../lib/react-router-dom */ \"(app-pages-browser)/./src/app/census/lib/react-router-dom.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_DollarSign_Lock_Mail_Plus_Search_Share2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,DollarSign,Lock,Mail,Plus,Search,Share2,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_DollarSign_Lock_Mail_Plus_Search_Share2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,DollarSign,Lock,Mail,Plus,Search,Share2,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_DollarSign_Lock_Mail_Plus_Search_Share2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,DollarSign,Lock,Mail,Plus,Search,Share2,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_DollarSign_Lock_Mail_Plus_Search_Share2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,DollarSign,Lock,Mail,Plus,Search,Share2,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_DollarSign_Lock_Mail_Plus_Search_Share2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,DollarSign,Lock,Mail,Plus,Search,Share2,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_DollarSign_Lock_Mail_Plus_Search_Share2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,DollarSign,Lock,Mail,Plus,Search,Share2,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_DollarSign_Lock_Mail_Plus_Search_Share2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,DollarSign,Lock,Mail,Plus,Search,Share2,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_DollarSign_Lock_Mail_Plus_Search_Share2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,DollarSign,Lock,Mail,Plus,Search,Share2,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/lock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_DollarSign_Lock_Mail_Plus_Search_Share2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,DollarSign,Lock,Mail,Plus,Search,Share2,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/share-2.js\");\n/* harmony import */ var _components_AskBrea__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../components/AskBrea */ \"(app-pages-browser)/./src/app/census/components/AskBrea.tsx\");\n/* harmony import */ var _components_NavigationDropdown__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../components/NavigationDropdown */ \"(app-pages-browser)/./src/app/census/components/NavigationDropdown.tsx\");\n/* harmony import */ var _components_ProfileHandler__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../components/ProfileHandler */ \"(app-pages-browser)/./src/app/census/components/ProfileHandler.tsx\");\n/* harmony import */ var _hooks_usePlanRestrictions__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../hooks/usePlanRestrictions */ \"(app-pages-browser)/./src/app/census/hooks/usePlanRestrictions.tsx\");\n/* harmony import */ var _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ../components/ui/use-toast */ \"(app-pages-browser)/./src/app/census/components/ui/use-toast.ts\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\nconst BrokerDashboard = ()=>{\n    _s();\n    const navigate = (0,_lib_react_router_dom__WEBPACK_IMPORTED_MODULE_6__.useNavigate)();\n    const { toast } = (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_11__.useToast)();\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [employeeCountFilter, setEmployeeCountFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [riskScoreFilter, setRiskScoreFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [suggestedPlanFilter, setSuggestedPlanFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const { canViewReport, reportsRemaining, isAtLimit, trackReportView } = (0,_hooks_usePlanRestrictions__WEBPACK_IMPORTED_MODULE_10__.usePlanRestrictions)();\n    // Mock data for multiple employers\n    const employerInsights = [\n        {\n            id: \"1\",\n            companyName: \"TechCorp Solutions\",\n            employees: 43,\n            averageAge: 36,\n            dependents: 1.3,\n            planType: \"PPO + HSA Combo\",\n            potentialSavings: \"$127,500\",\n            riskScore: 6.2,\n            uploadDate: \"2024-01-15\",\n            status: \"analyzed\"\n        },\n        {\n            id: \"2\",\n            companyName: \"Green Manufacturing\",\n            employees: 87,\n            averageAge: 42,\n            dependents: 1.8,\n            planType: \"Traditional PPO\",\n            potentialSavings: \"$245,000\",\n            riskScore: 7.1,\n            uploadDate: \"2024-01-10\",\n            status: \"analyzed\"\n        },\n        {\n            id: \"3\",\n            companyName: \"StartupXYZ\",\n            employees: 18,\n            averageAge: 29,\n            dependents: 0.8,\n            planType: \"HSA Only\",\n            potentialSavings: \"$32,400\",\n            riskScore: 4.5,\n            uploadDate: \"2024-01-18\",\n            status: \"processing\"\n        }\n    ];\n    // Filter logic\n    const filteredEmployers = employerInsights.filter((employer)=>{\n        // Search filter\n        if (searchTerm && !employer.companyName.toLowerCase().includes(searchTerm.toLowerCase())) {\n            return false;\n        }\n        // Employee count filter\n        if (employeeCountFilter !== \"all\") {\n            if (employeeCountFilter === \"1-50\" && employer.employees > 50) return false;\n            if (employeeCountFilter === \"51-100\" && (employer.employees < 51 || employer.employees > 100)) return false;\n            if (employeeCountFilter === \"101+\" && employer.employees <= 100) return false;\n        }\n        // Risk score filter\n        if (riskScoreFilter !== \"all\") {\n            if (riskScoreFilter === \"low\" && employer.riskScore >= 5) return false;\n            if (riskScoreFilter === \"medium\" && (employer.riskScore < 5 || employer.riskScore > 7)) return false;\n            if (riskScoreFilter === \"high\" && employer.riskScore <= 7) return false;\n        }\n        // Suggested plan filter\n        if (suggestedPlanFilter !== \"all\") {\n            if (suggestedPlanFilter === \"ppo-hsa\" && employer.planType !== \"PPO + HSA Combo\") return false;\n            if (suggestedPlanFilter === \"traditional-ppo\" && employer.planType !== \"Traditional PPO\") return false;\n            if (suggestedPlanFilter === \"other\" && employer.planType !== \"HSA Only\" && employer.planType !== \"Modern HSA Plus Plan\") return false;\n        }\n        return true;\n    });\n    const handleShareInsight = (companyName)=>{\n        const shareUrl = \"\".concat(window.location.origin, \"/shared-insight/\").concat(companyName.toLowerCase().replace(/\\s+/g, \"-\"));\n        navigator.clipboard.writeText(shareUrl);\n        console.log(\"Share link copied for \".concat(companyName));\n    };\n    const handleTileClick = (employerId, companyName)=>{\n        console.log(\"Attempting to view report for \".concat(companyName, \" (ID: \").concat(employerId, \")\"));\n        console.log(\"Can view report: \".concat(canViewReport(employerId)));\n        console.log(\"Reports remaining: \".concat(reportsRemaining));\n        console.log(\"Is at limit: \".concat(isAtLimit));\n        if (!canViewReport(employerId)) {\n            toast({\n                title: \"Upgrade Required\",\n                description: \"You've reached your free report limit (2 reports). Upgrade to Pro for unlimited access.\",\n                variant: \"destructive\"\n            });\n            navigate(\"/pricing\");\n            return;\n        }\n        // Track the report view\n        trackReportView(employerId);\n        navigate(\"?page=employer-insight/\".concat(employerId));\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-slate-50 to-blue-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"border-b bg-white/90 backdrop-blur-sm sticky top-0 z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto px-3 sm:px-4 py-3\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-xl sm:text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent\",\n                                children: \"BenOsphere\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                lineNumber: 131,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2 sm:space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_NavigationDropdown__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                        lineNumber: 135,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AskBrea__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        context: \"broker dashboard with multiple client insights\",\n                                        size: \"sm\",\n                                        variant: \"outline\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                        lineNumber: 136,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ProfileHandler__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"hidden sm:inline-flex\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                        lineNumber: 137,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                lineNumber: 134,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                        lineNumber: 130,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                    lineNumber: 129,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                lineNumber: 128,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"container mx-auto px-3 sm:px-4 py-4 sm:py-6 max-w-7xl\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col lg:flex-row lg:justify-between lg:items-start gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-2xl sm:text-3xl font-bold text-gray-900 mb-2\",\n                                                children: \"\\uD83D\\uDCCA Broker Dashboard\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                lineNumber: 148,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600 text-sm sm:text-base mb-4 lg:mb-0\",\n                                                children: \"Manage and analyze your client census data\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                lineNumber: 151,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                        lineNumber: 147,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col sm:flex-row gap-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                size: \"sm\",\n                                                onClick: ()=>navigate(\"/upload-census\"),\n                                                className: \"bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_DollarSign_Lock_Mail_Plus_Search_Share2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        className: \"h-4 w-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                        lineNumber: 163,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    \"Upload New Census\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                lineNumber: 158,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                variant: \"outline\",\n                                                size: \"sm\",\n                                                onClick: ()=>navigate(\"/employer-invite\"),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_DollarSign_Lock_Mail_Plus_Search_Share2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                        className: \"h-4 w-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                        lineNumber: 167,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    \"Invite Employer\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                lineNumber: 166,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                        lineNumber: 157,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                lineNumber: 146,\n                                columnNumber: 11\n                            }, undefined),\n                            reportsRemaining <= 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                className: \"mt-4 bg-gradient-to-r from-amber-50 to-orange-50 border-amber-200\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    className: \"p-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-start space-x-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_DollarSign_Lock_Mail_Plus_Search_Share2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                        className: \"h-5 w-5 text-amber-600 mt-0.5 flex-shrink-0\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                        lineNumber: 179,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"font-medium text-amber-800\",\n                                                                children: isAtLimit ? \"Report limit reached!\" : \"\".concat(reportsRemaining, \" free report\").concat(reportsRemaining === 1 ? \"\" : \"s\", \" remaining\")\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                lineNumber: 181,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-amber-700\",\n                                                                children: isAtLimit ? \"Upgrade to Pro for unlimited reports\" : \"Upgrade to Pro for unlimited access\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                lineNumber: 184,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                        lineNumber: 180,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                lineNumber: 178,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                onClick: ()=>navigate(\"/pricing\"),\n                                                className: \"bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white flex-shrink-0\",\n                                                size: \"sm\",\n                                                children: \"Upgrade Now\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                lineNumber: 189,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                        lineNumber: 177,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                    lineNumber: 176,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                lineNumber: 175,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                        lineNumber: 145,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        className: \"mb-6 shadow-lg border-0\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                            className: \"p-4 sm:p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_DollarSign_Lock_Mail_Plus_Search_Share2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                className: \"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                lineNumber: 208,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                placeholder: \"Search by Client Name\",\n                                                value: searchTerm,\n                                                onChange: (e)=>setSearchTerm(e.target.value),\n                                                className: \"pl-10\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                lineNumber: 209,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                        lineNumber: 207,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 sm:grid-cols-3 gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"text-sm font-medium text-gray-700 mb-2 block\",\n                                                        children: \"Employee Count\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                        lineNumber: 220,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.Select, {\n                                                        value: employeeCountFilter,\n                                                        onValueChange: setEmployeeCountFilter,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectTrigger, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectValue, {\n                                                                    placeholder: \"All\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                    lineNumber: 223,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                lineNumber: 222,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectContent, {\n                                                                className: \"bg-white\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                                        value: \"all\",\n                                                                        children: \"All\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                        lineNumber: 226,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                                        value: \"1-50\",\n                                                                        children: \"1–50\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                        lineNumber: 227,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                                        value: \"51-100\",\n                                                                        children: \"51–100\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                        lineNumber: 228,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                                        value: \"101+\",\n                                                                        children: \"101+\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                        lineNumber: 229,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                lineNumber: 225,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                        lineNumber: 221,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                lineNumber: 219,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"text-sm font-medium text-gray-700 mb-2 block\",\n                                                        children: \"Risk Score\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                        lineNumber: 235,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.Select, {\n                                                        value: riskScoreFilter,\n                                                        onValueChange: setRiskScoreFilter,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectTrigger, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectValue, {\n                                                                    placeholder: \"All\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                    lineNumber: 238,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                lineNumber: 237,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectContent, {\n                                                                className: \"bg-white\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                                        value: \"all\",\n                                                                        children: \"All\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                        lineNumber: 241,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                                        value: \"low\",\n                                                                        children: \"Low (<5)\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                        lineNumber: 242,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                                        value: \"medium\",\n                                                                        children: \"Medium (5–7)\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                        lineNumber: 243,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                                        value: \"high\",\n                                                                        children: \"High (>7)\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                        lineNumber: 244,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                lineNumber: 240,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                        lineNumber: 236,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                lineNumber: 234,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"text-sm font-medium text-gray-700 mb-2 block\",\n                                                        children: \"Suggested Plan\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                        lineNumber: 250,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.Select, {\n                                                        value: suggestedPlanFilter,\n                                                        onValueChange: setSuggestedPlanFilter,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectTrigger, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectValue, {\n                                                                    placeholder: \"All\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                    lineNumber: 253,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                lineNumber: 252,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectContent, {\n                                                                className: \"bg-white\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                                        value: \"all\",\n                                                                        children: \"All\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                        lineNumber: 256,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                                        value: \"ppo-hsa\",\n                                                                        children: \"PPO + HSA Combo\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                        lineNumber: 257,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                                        value: \"traditional-ppo\",\n                                                                        children: \"Traditional PPO\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                        lineNumber: 258,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                                        value: \"other\",\n                                                                        children: \"Other\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                        lineNumber: 259,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                lineNumber: 255,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                        lineNumber: 251,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                lineNumber: 249,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                        lineNumber: 218,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                lineNumber: 205,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                            lineNumber: 204,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                        lineNumber: 203,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4 mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                className: \"bg-gradient-to-br from-blue-100 to-blue-200 border-blue-300 hover:shadow-md transition-all duration-200\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    className: \"p-3 sm:p-4 text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_DollarSign_Lock_Mail_Plus_Search_Share2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            className: \"h-6 w-6 sm:h-8 sm:w-8 mx-auto mb-1 sm:mb-2 text-blue-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                            lineNumber: 272,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs sm:text-sm text-blue-600 font-medium\",\n                                            children: \"Total Clients\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                            lineNumber: 273,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xl sm:text-2xl font-bold text-blue-900\",\n                                            children: filteredEmployers.length\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                            lineNumber: 274,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                    lineNumber: 271,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                lineNumber: 270,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                className: \"bg-gradient-to-br from-emerald-100 to-teal-200 border-emerald-300 hover:shadow-md transition-all duration-200\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    className: \"p-3 sm:p-4 text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_DollarSign_Lock_Mail_Plus_Search_Share2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            className: \"h-6 w-6 sm:h-8 sm:w-8 mx-auto mb-1 sm:mb-2 text-emerald-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                            lineNumber: 280,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs sm:text-sm text-emerald-600 font-medium\",\n                                            children: \"Total Employees\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                            lineNumber: 281,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xl sm:text-2xl font-bold text-emerald-900\",\n                                            children: filteredEmployers.reduce((sum, emp)=>sum + emp.employees, 0)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                            lineNumber: 282,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                    lineNumber: 279,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                lineNumber: 278,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                className: \"bg-gradient-to-br from-orange-100 to-red-200 border-orange-300 hover:shadow-md transition-all duration-200\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    className: \"p-3 sm:p-4 text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_DollarSign_Lock_Mail_Plus_Search_Share2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            className: \"h-6 w-6 sm:h-8 sm:w-8 mx-auto mb-1 sm:mb-2 text-orange-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                            lineNumber: 290,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs sm:text-sm text-orange-600 font-medium\",\n                                            children: \"Total Potential Savings\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                            lineNumber: 291,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-lg sm:text-2xl font-bold text-orange-900\",\n                                            children: \"$404,900\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                            lineNumber: 292,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                    lineNumber: 289,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                lineNumber: 288,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                className: \"bg-gradient-to-br from-purple-100 to-pink-200 border-purple-300 hover:shadow-md transition-all duration-200\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    className: \"p-3 sm:p-4 text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_DollarSign_Lock_Mail_Plus_Search_Share2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                            className: \"h-6 w-6 sm:h-8 sm:w-8 mx-auto mb-1 sm:mb-2 text-purple-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                            lineNumber: 298,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs sm:text-sm text-purple-600 font-medium\",\n                                            children: \"Avg Risk Score\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                            lineNumber: 299,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xl sm:text-2xl font-bold text-purple-900\",\n                                            children: \"5.9/10\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                            lineNumber: 300,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                    lineNumber: 297,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                lineNumber: 296,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                        lineNumber: 269,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        className: \"shadow-lg border-0\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                className: \"pb-3 sm:pb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                    className: \"text-lg sm:text-xl\",\n                                    children: \"Client Census Insights\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                    lineNumber: 308,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                lineNumber: 307,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                className: \"p-3 sm:p-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3 sm:space-y-4\",\n                                    children: filteredEmployers.map((employer)=>{\n                                        const canAccess = canViewReport(employer.id);\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                            className: \"border-l-4 border-l-blue-500 transition-all duration-200 cursor-pointer \".concat(canAccess ? \"hover:shadow-md hover:-translate-y-0.5\" : \"opacity-60 hover:opacity-80 cursor-not-allowed\", \" \").concat(!canAccess ? \"relative\" : \"\"),\n                                            onClick: ()=>handleTileClick(employer.id, employer.companyName),\n                                            children: [\n                                                !canAccess && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute inset-0 bg-gray-200/50 backdrop-blur-[1px] rounded-lg flex items-center justify-center z-10\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-white p-3 rounded-lg shadow-lg flex items-center space-x-2 border border-amber-200\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_DollarSign_Lock_Mail_Plus_Search_Share2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                className: \"h-4 w-4 text-amber-600\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                lineNumber: 328,\n                                                                columnNumber: 27\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm font-medium text-amber-800\",\n                                                                children: \"Upgrade to view\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                lineNumber: 329,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                        lineNumber: 327,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                    lineNumber: 326,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                                    className: \"p-4 sm:p-5\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex flex-col sm:flex-row justify-between items-start mb-3 sm:mb-4 gap-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                            className: \"text-lg sm:text-xl font-semibold text-gray-900 mb-1\",\n                                                                            children: employer.companyName\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                            lineNumber: 337,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-xs sm:text-sm text-gray-500\",\n                                                                            children: [\n                                                                                \"Uploaded: \",\n                                                                                new Date(employer.uploadDate).toLocaleDateString()\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                            lineNumber: 340,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                    lineNumber: 336,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex space-x-2 w-full sm:w-auto\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                        size: \"sm\",\n                                                                        variant: \"outline\",\n                                                                        onClick: (e)=>{\n                                                                            e.stopPropagation();\n                                                                            if (canAccess) {\n                                                                                handleShareInsight(employer.companyName);\n                                                                            } else {\n                                                                                navigate(\"/pricing\");\n                                                                            }\n                                                                        },\n                                                                        className: \"flex-1 sm:flex-none\",\n                                                                        disabled: !canAccess,\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_DollarSign_Lock_Mail_Plus_Search_Share2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                                className: \"h-3 w-3 sm:h-4 sm:w-4 sm:mr-1\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                                lineNumber: 359,\n                                                                                columnNumber: 29\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"hidden sm:inline\",\n                                                                                children: \"Share\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                                lineNumber: 360,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                        lineNumber: 345,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                    lineNumber: 344,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                            lineNumber: 335,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"grid grid-cols-2 lg:grid-cols-4 gap-2 sm:gap-3 mb-3 sm:mb-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-center p-2 sm:p-3 bg-gradient-to-br from-blue-50 to-blue-100 rounded-lg hover:scale-105 transition-transform duration-200\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-xs sm:text-sm text-blue-600 font-medium\",\n                                                                            children: \"Employees\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                            lineNumber: 367,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm sm:text-lg font-bold text-blue-900\",\n                                                                            children: employer.employees\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                            lineNumber: 368,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                    lineNumber: 366,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-center p-2 sm:p-3 bg-gradient-to-br from-emerald-50 to-emerald-100 rounded-lg hover:scale-105 transition-transform duration-200\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-xs sm:text-sm text-emerald-600 font-medium\",\n                                                                            children: \"Avg Age\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                            lineNumber: 371,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm sm:text-lg font-bold text-emerald-900\",\n                                                                            children: employer.averageAge\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                            lineNumber: 372,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                    lineNumber: 370,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-center p-2 sm:p-3 bg-gradient-to-br from-orange-50 to-orange-100 rounded-lg hover:scale-105 transition-transform duration-200\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-xs sm:text-sm text-orange-600 font-medium\",\n                                                                            children: \"Savings\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                            lineNumber: 375,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm sm:text-lg font-bold text-orange-900\",\n                                                                            children: employer.potentialSavings\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                            lineNumber: 376,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                    lineNumber: 374,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-center p-2 sm:p-3 bg-gradient-to-br from-purple-50 to-purple-100 rounded-lg hover:scale-105 transition-transform duration-200\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-xs sm:text-sm text-purple-600 font-medium\",\n                                                                            children: \"Risk Score\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                            lineNumber: 379,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm sm:text-lg font-bold text-purple-900\",\n                                                                            children: [\n                                                                                employer.riskScore,\n                                                                                \"/10\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                            lineNumber: 380,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                    lineNumber: 378,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                            lineNumber: 365,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex flex-col sm:flex-row justify-between items-start sm:items-center gap-2 sm:gap-0\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-sm\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-gray-600\",\n                                                                            children: \"Suggested Plan: \"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                            lineNumber: 386,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"font-medium text-gray-900\",\n                                                                            children: employer.planType\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                            lineNumber: 387,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                    lineNumber: 385,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"px-2 py-1 rounded-full text-xs font-medium \".concat(employer.status === \"analyzed\" ? \"bg-green-100 text-green-800\" : \"bg-yellow-100 text-yellow-800\"),\n                                                                    children: employer.status === \"analyzed\" ? \"✅ Analyzed\" : \"⏳ Processing\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                    lineNumber: 389,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                            lineNumber: 384,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                    lineNumber: 334,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, employer.id, true, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                            lineNumber: 316,\n                                            columnNumber: 19\n                                        }, undefined);\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                    lineNumber: 311,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                lineNumber: 310,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                        lineNumber: 306,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        className: \"mt-6 bg-gradient-to-r from-purple-100 to-blue-100 border-0 shadow-lg\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                            className: \"p-4 sm:p-6 text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg sm:text-xl font-bold text-gray-900 mb-2\",\n                                    children: \"\\uD83D\\uDE80 Grow Your Network\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                    lineNumber: 408,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-700 mb-4 text-sm sm:text-base\",\n                                    children: \"Share BenOsphere with other brokers and get rewards for every signup\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                    lineNumber: 411,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col sm:flex-row justify-center space-y-2 sm:space-y-0 sm:space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            className: \"bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white transition-all duration-200 hover:scale-105\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_DollarSign_Lock_Mail_Plus_Search_Share2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                    className: \"h-4 w-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                    lineNumber: 416,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                \"Refer a Broker\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                            lineNumber: 415,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            variant: \"outline\",\n                                            className: \"hover:bg-white/50\",\n                                            children: \"View Referral Rewards\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                            lineNumber: 419,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                    lineNumber: 414,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                            lineNumber: 407,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                        lineNumber: 406,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                lineNumber: 143,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n        lineNumber: 126,\n        columnNumber: 5\n    }, undefined);\n};\n_s(BrokerDashboard, \"DEDUFktEDRsQ+usPY8Njz1Zt/9k=\", false, function() {\n    return [\n        _lib_react_router_dom__WEBPACK_IMPORTED_MODULE_6__.useNavigate,\n        _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_11__.useToast,\n        _hooks_usePlanRestrictions__WEBPACK_IMPORTED_MODULE_10__.usePlanRestrictions\n    ];\n});\n_c = BrokerDashboard;\n/* harmony default export */ __webpack_exports__[\"default\"] = (BrokerDashboard);\nvar _c;\n$RefreshReg$(_c, \"BrokerDashboard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/census/public/BrokerDashboard.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/building-2.js":
/*!****************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/building-2.js ***!
  \****************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: function() { return /* binding */ __iconNode; },\n/* harmony export */   \"default\": function() { return /* binding */ Building2; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.475.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"M6 22V4a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2v18Z\",\n            key: \"1b4qmf\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M6 12H4a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2h2\",\n            key: \"i71pzd\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M18 9h2a2 2 0 0 1 2 2v9a2 2 0 0 1-2 2h-2\",\n            key: \"10jefs\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M10 6h4\",\n            key: \"1itunk\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M10 10h4\",\n            key: \"tcdvrf\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M10 14h4\",\n            key: \"kelpxr\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M10 18h4\",\n            key: \"1ulq68\"\n        }\n    ]\n];\nconst Building2 = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"Building2\", __iconNode);\n //# sourceMappingURL=building-2.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/building-2.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-plus.js":
/*!*****************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/circle-plus.js ***!
  \*****************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: function() { return /* binding */ __iconNode; },\n/* harmony export */   \"default\": function() { return /* binding */ CirclePlus; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.475.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"circle\",\n        {\n            cx: \"12\",\n            cy: \"12\",\n            r: \"10\",\n            key: \"1mglay\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M8 12h8\",\n            key: \"1wcyev\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M12 8v8\",\n            key: \"napkw2\"\n        }\n    ]\n];\nconst CirclePlus = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"CirclePlus\", __iconNode);\n //# sourceMappingURL=circle-plus.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vaWNvbnMvY2lyY2xlLXBsdXMuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7O0FBR08sTUFBTUEsYUFBdUI7SUFDbEM7UUFBQztRQUFVO1lBQUVDLElBQUk7WUFBTUMsSUFBSTtZQUFNQyxHQUFHO1lBQU1DLEtBQUs7UUFBQTtLQUFVO0lBQ3pEO1FBQUM7UUFBUTtZQUFFQyxHQUFHO1lBQVdELEtBQUs7UUFBQTtLQUFVO0lBQ3hDO1FBQUM7UUFBUTtZQUFFQyxHQUFHO1lBQVdELEtBQUs7UUFBQTtLQUFVO0NBQzFDO0FBYU0sTUFBQUUsYUFBYUMsZ0VBQWdCQSxDQUFDLGNBQWNQIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uLi8uLi8uLi9zcmMvaWNvbnMvY2lyY2xlLXBsdXMudHM/ZDk0ZCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgY3JlYXRlTHVjaWRlSWNvbiBmcm9tICcuLi9jcmVhdGVMdWNpZGVJY29uJztcbmltcG9ydCB7IEljb25Ob2RlIH0gZnJvbSAnLi4vdHlwZXMnO1xuXG5leHBvcnQgY29uc3QgX19pY29uTm9kZTogSWNvbk5vZGUgPSBbXG4gIFsnY2lyY2xlJywgeyBjeDogJzEyJywgY3k6ICcxMicsIHI6ICcxMCcsIGtleTogJzFtZ2xheScgfV0sXG4gIFsncGF0aCcsIHsgZDogJ004IDEyaDgnLCBrZXk6ICcxd2N5ZXYnIH1dLFxuICBbJ3BhdGgnLCB7IGQ6ICdNMTIgOHY4Jywga2V5OiAnbmFwa3cyJyB9XSxcbl07XG5cbi8qKlxuICogQGNvbXBvbmVudCBAbmFtZSBDaXJjbGVQbHVzXG4gKiBAZGVzY3JpcHRpb24gTHVjaWRlIFNWRyBpY29uIGNvbXBvbmVudCwgcmVuZGVycyBTVkcgRWxlbWVudCB3aXRoIGNoaWxkcmVuLlxuICpcbiAqIEBwcmV2aWV3ICFbaW1nXShkYXRhOmltYWdlL3N2Zyt4bWw7YmFzZTY0LFBITjJaeUFnZUcxc2JuTTlJbWgwZEhBNkx5OTNkM2N1ZHpNdWIzSm5Mekl3TURBdmMzWm5JZ29nSUhkcFpIUm9QU0l5TkNJS0lDQm9aV2xuYUhROUlqSTBJZ29nSUhacFpYZENiM2c5SWpBZ01DQXlOQ0F5TkNJS0lDQm1hV3hzUFNKdWIyNWxJZ29nSUhOMGNtOXJaVDBpSXpBd01DSWdjM1I1YkdVOUltSmhZMnRuY205MWJtUXRZMjlzYjNJNklDTm1abVk3SUdKdmNtUmxjaTF5WVdScGRYTTZJREp3ZUNJS0lDQnpkSEp2YTJVdGQybGtkR2c5SWpJaUNpQWdjM1J5YjJ0bExXeHBibVZqWVhBOUluSnZkVzVrSWdvZ0lITjBjbTlyWlMxc2FXNWxhbTlwYmowaWNtOTFibVFpQ2o0S0lDQThZMmx5WTJ4bElHTjRQU0l4TWlJZ1kzazlJakV5SWlCeVBTSXhNQ0lnTHo0S0lDQThjR0YwYUNCa1BTSk5PQ0F4TW1nNElpQXZQZ29nSUR4d1lYUm9JR1E5SWsweE1pQTRkamdpSUM4K0Nqd3ZjM1puUGdvPSkgLSBodHRwczovL2x1Y2lkZS5kZXYvaWNvbnMvY2lyY2xlLXBsdXNcbiAqIEBzZWUgaHR0cHM6Ly9sdWNpZGUuZGV2L2d1aWRlL3BhY2thZ2VzL2x1Y2lkZS1yZWFjdCAtIERvY3VtZW50YXRpb25cbiAqXG4gKiBAcGFyYW0ge09iamVjdH0gcHJvcHMgLSBMdWNpZGUgaWNvbnMgcHJvcHMgYW5kIGFueSB2YWxpZCBTVkcgYXR0cmlidXRlXG4gKiBAcmV0dXJucyB7SlNYLkVsZW1lbnR9IEpTWCBFbGVtZW50XG4gKlxuICovXG5jb25zdCBDaXJjbGVQbHVzID0gY3JlYXRlTHVjaWRlSWNvbignQ2lyY2xlUGx1cycsIF9faWNvbk5vZGUpO1xuXG5leHBvcnQgZGVmYXVsdCBDaXJjbGVQbHVzO1xuIl0sIm5hbWVzIjpbIl9faWNvbk5vZGUiLCJjeCIsImN5IiwiciIsImtleSIsImQiLCJDaXJjbGVQbHVzIiwiY3JlYXRlTHVjaWRlSWNvbiJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-plus.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/menu.js":
/*!**********************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/menu.js ***!
  \**********************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: function() { return /* binding */ __iconNode; },\n/* harmony export */   \"default\": function() { return /* binding */ Menu; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.475.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"line\",\n        {\n            x1: \"4\",\n            x2: \"20\",\n            y1: \"12\",\n            y2: \"12\",\n            key: \"1e0a9i\"\n        }\n    ],\n    [\n        \"line\",\n        {\n            x1: \"4\",\n            x2: \"20\",\n            y1: \"6\",\n            y2: \"6\",\n            key: \"1owob3\"\n        }\n    ],\n    [\n        \"line\",\n        {\n            x1: \"4\",\n            x2: \"20\",\n            y1: \"18\",\n            y2: \"18\",\n            key: \"yk5zj1\"\n        }\n    ]\n];\nconst Menu = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"Menu\", __iconNode);\n //# sourceMappingURL=menu.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/menu.js\n"));

/***/ })

});