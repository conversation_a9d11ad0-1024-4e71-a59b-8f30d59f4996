"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/census/page",{

/***/ "(app-pages-browser)/./src/app/census/public/HRUpload.tsx":
/*!********************************************!*\
  !*** ./src/app/census/public/HRUpload.tsx ***!
  \********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../components/ui/button */ \"(app-pages-browser)/./src/app/census/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../components/ui/card */ \"(app-pages-browser)/./src/app/census/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../components/ui/input */ \"(app-pages-browser)/./src/app/census/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../components/ui/label */ \"(app-pages-browser)/./src/app/census/components/ui/label.tsx\");\n/* harmony import */ var _lib_react_router_dom__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../lib/react-router-dom */ \"(app-pages-browser)/./src/app/census/lib/react-router-dom.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Building_FileText_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Building,FileText,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Building_FileText_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Building,FileText,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/building.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Building_FileText_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Building,FileText,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Building_FileText_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Building,FileText,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _components_ProfileHandler__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../components/ProfileHandler */ \"(app-pages-browser)/./src/app/census/components/ProfileHandler.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nconst HRUpload = ()=>{\n    _s();\n    const navigate = (0,_lib_react_router_dom__WEBPACK_IMPORTED_MODULE_6__.useNavigate)();\n    const [dragActive, setDragActive] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedFile, setSelectedFile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const handleDrag = (e)=>{\n        e.preventDefault();\n        e.stopPropagation();\n        if (e.type === \"dragenter\" || e.type === \"dragover\") {\n            setDragActive(true);\n        } else if (e.type === \"dragleave\") {\n            setDragActive(false);\n        }\n    };\n    const handleDrop = (e)=>{\n        e.preventDefault();\n        e.stopPropagation();\n        setDragActive(false);\n        if (e.dataTransfer.files && e.dataTransfer.files[0]) {\n            setSelectedFile(e.dataTransfer.files[0]);\n        }\n    };\n    const handleFileSelect = (e)=>{\n        if (e.target.files && e.target.files[0]) {\n            setSelectedFile(e.target.files[0]);\n        }\n    };\n    const handleSubmit = (e)=>{\n        e.preventDefault();\n        if (selectedFile && companyInfo.companyName) {\n            navigate(\"/hr-processing?company=\".concat(encodeURIComponent(companyInfo.companyName)));\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"border-b bg-white/80 backdrop-blur-sm\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto px-4 py-4 flex justify-between items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    variant: \"ghost\",\n                                    onClick: ()=>navigate(\"/\"),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Building_FileText_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\HRUpload.tsx\",\n                                            lineNumber: 57,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        \"Back\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\HRUpload.tsx\",\n                                    lineNumber: 56,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent\",\n                                    children: \"BenOsphere HR\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\HRUpload.tsx\",\n                                    lineNumber: 60,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\HRUpload.tsx\",\n                            lineNumber: 55,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ProfileHandler__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\HRUpload.tsx\",\n                            lineNumber: 62,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\HRUpload.tsx\",\n                    lineNumber: 54,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\HRUpload.tsx\",\n                lineNumber: 53,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"container mx-auto px-4 py-16 max-w-4xl\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center mb-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-20 h-20 mx-auto mb-6 bg-blue-100 rounded-full flex items-center justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Building_FileText_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    className: \"h-10 w-10 text-blue-600\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\HRUpload.tsx\",\n                                    lineNumber: 69,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\HRUpload.tsx\",\n                                lineNumber: 68,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-4xl font-bold text-gray-900 mb-4\",\n                                children: \"\\uD83C\\uDFE2 HR Portal - Upload Employee Census\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\HRUpload.tsx\",\n                                lineNumber: 71,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xl text-gray-600\",\n                                children: \"Get instant insights into your employee benefits and potential cost savings\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\HRUpload.tsx\",\n                                lineNumber: 74,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\HRUpload.tsx\",\n                        lineNumber: 67,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                        onSubmit: handleSubmit,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                className: \"shadow-xl border-0 mb-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                            className: \"text-center text-2xl text-blue-700\",\n                                            children: \"Company Information\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\HRUpload.tsx\",\n                                            lineNumber: 82,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\HRUpload.tsx\",\n                                        lineNumber: 81,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                        className: \"space-y-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                            htmlFor: \"companyName\",\n                                                            className: \"text-base font-medium\",\n                                                            children: \"Company Name *\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\HRUpload.tsx\",\n                                                            lineNumber: 89,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                            id: \"companyName\",\n                                                            type: \"text\",\n                                                            placeholder: \"Enter company name\",\n                                                            value: companyInfo.companyName,\n                                                            onChange: (e)=>setCompanyInfo({\n                                                                    ...companyInfo,\n                                                                    companyName: e.target.value\n                                                                }),\n                                                            required: true,\n                                                            className: \"h-12\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\HRUpload.tsx\",\n                                                            lineNumber: 92,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\HRUpload.tsx\",\n                                                    lineNumber: 88,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                            htmlFor: \"contactName\",\n                                                            className: \"text-base font-medium\",\n                                                            children: \"HR Contact Name\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\HRUpload.tsx\",\n                                                            lineNumber: 103,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                            id: \"contactName\",\n                                                            type: \"text\",\n                                                            placeholder: \"Your name\",\n                                                            value: companyInfo.contactName,\n                                                            onChange: (e)=>setCompanyInfo({\n                                                                    ...companyInfo,\n                                                                    contactName: e.target.value\n                                                                }),\n                                                            className: \"h-12\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\HRUpload.tsx\",\n                                                            lineNumber: 106,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\HRUpload.tsx\",\n                                                    lineNumber: 102,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                            htmlFor: \"email\",\n                                                            className: \"text-base font-medium\",\n                                                            children: \"Email Address\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\HRUpload.tsx\",\n                                                            lineNumber: 116,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                            id: \"email\",\n                                                            type: \"email\",\n                                                            placeholder: \"<EMAIL>\",\n                                                            value: companyInfo.email,\n                                                            onChange: (e)=>setCompanyInfo({\n                                                                    ...companyInfo,\n                                                                    email: e.target.value\n                                                                }),\n                                                            className: \"h-12\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\HRUpload.tsx\",\n                                                            lineNumber: 119,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\HRUpload.tsx\",\n                                                    lineNumber: 115,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                            htmlFor: \"phone\",\n                                                            className: \"text-base font-medium\",\n                                                            children: \"Phone Number\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\HRUpload.tsx\",\n                                                            lineNumber: 129,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                            id: \"phone\",\n                                                            type: \"tel\",\n                                                            placeholder: \"(*************\",\n                                                            value: companyInfo.phone,\n                                                            onChange: (e)=>setCompanyInfo({\n                                                                    ...companyInfo,\n                                                                    phone: e.target.value\n                                                                }),\n                                                            className: \"h-12\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\HRUpload.tsx\",\n                                                            lineNumber: 132,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\HRUpload.tsx\",\n                                                    lineNumber: 128,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\HRUpload.tsx\",\n                                            lineNumber: 87,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\HRUpload.tsx\",\n                                        lineNumber: 86,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\HRUpload.tsx\",\n                                lineNumber: 80,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                className: \"shadow-xl border-0\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                            className: \"text-center text-2xl text-blue-700\",\n                                            children: \"Upload Employee Census\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\HRUpload.tsx\",\n                                            lineNumber: 147,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\HRUpload.tsx\",\n                                        lineNumber: 146,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                        className: \"space-y-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"border-2 border-dashed rounded-xl p-12 text-center transition-all \".concat(dragActive ? \"border-blue-500 bg-blue-50\" : \"border-gray-300 hover:border-blue-400 hover:bg-gray-50\"),\n                                                onDragEnter: handleDrag,\n                                                onDragLeave: handleDrag,\n                                                onDragOver: handleDrag,\n                                                onDrop: handleDrop,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"mx-auto w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Building_FileText_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                className: \"h-8 w-8 text-blue-600\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\HRUpload.tsx\",\n                                                                lineNumber: 166,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\HRUpload.tsx\",\n                                                            lineNumber: 165,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        selectedFile ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-lg font-semibold text-blue-600\",\n                                                                    children: [\n                                                                        \"✅ \",\n                                                                        selectedFile.name\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\HRUpload.tsx\",\n                                                                    lineNumber: 171,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-gray-500\",\n                                                                    children: [\n                                                                        (selectedFile.size / 1024 / 1024).toFixed(2),\n                                                                        \" MB\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\HRUpload.tsx\",\n                                                                    lineNumber: 174,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\HRUpload.tsx\",\n                                                            lineNumber: 170,\n                                                            columnNumber: 21\n                                                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xl font-semibold text-gray-700 mb-2\",\n                                                                    children: \"\\uD83D\\uDCC4 Drag & drop employee census file\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\HRUpload.tsx\",\n                                                                    lineNumber: 180,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-gray-500 mb-4\",\n                                                                    children: \"CSV, Excel, or PDF format\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\HRUpload.tsx\",\n                                                                    lineNumber: 183,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"file\",\n                                                                    id: \"file-upload\",\n                                                                    className: \"hidden\",\n                                                                    accept: \".csv,.xlsx,.xls,.pdf\",\n                                                                    onChange: handleFileSelect\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\HRUpload.tsx\",\n                                                                    lineNumber: 184,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    htmlFor: \"file-upload\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                        type: \"button\",\n                                                                        variant: \"outline\",\n                                                                        className: \"cursor-pointer bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white border-0\",\n                                                                        asChild: true,\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: \"\\uD83D\\uDD0D Browse Files\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\HRUpload.tsx\",\n                                                                            lineNumber: 198,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\HRUpload.tsx\",\n                                                                        lineNumber: 192,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\HRUpload.tsx\",\n                                                                    lineNumber: 191,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\HRUpload.tsx\",\n                                                            lineNumber: 179,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\HRUpload.tsx\",\n                                                    lineNumber: 164,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\HRUpload.tsx\",\n                                                lineNumber: 153,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                    type: \"submit\",\n                                                    size: \"lg\",\n                                                    className: \"bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-12 py-4 text-lg\",\n                                                    disabled: !selectedFile || !companyInfo.companyName,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Building_FileText_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                            className: \"mr-2 h-5 w-5\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\HRUpload.tsx\",\n                                                            lineNumber: 216,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        \"➡️ Analyze Our Census\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\HRUpload.tsx\",\n                                                    lineNumber: 210,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\HRUpload.tsx\",\n                                                lineNumber: 209,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            (!selectedFile || !companyInfo.companyName) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-center text-sm text-gray-400\",\n                                                children: \"Please fill in company name and select a file\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\HRUpload.tsx\",\n                                                lineNumber: 222,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\HRUpload.tsx\",\n                                        lineNumber: 151,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\HRUpload.tsx\",\n                                lineNumber: 145,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\HRUpload.tsx\",\n                        lineNumber: 79,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid md:grid-cols-2 gap-6 mt-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                className: \"bg-blue-50 border-blue-200\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    className: \"p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-semibold text-blue-900 mb-2\",\n                                            children: \"\\uD83D\\uDCA1 What you'll get:\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\HRUpload.tsx\",\n                                            lineNumber: 234,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"text-blue-800 space-y-1 text-sm\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"• Detailed benefits analysis for your employees\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\HRUpload.tsx\",\n                                                    lineNumber: 236,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"• Cost optimization recommendations\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\HRUpload.tsx\",\n                                                    lineNumber: 237,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"• Benchmark against similar companies\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\HRUpload.tsx\",\n                                                    lineNumber: 238,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"• Risk assessment and planning insights\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\HRUpload.tsx\",\n                                                    lineNumber: 239,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\HRUpload.tsx\",\n                                            lineNumber: 235,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\HRUpload.tsx\",\n                                    lineNumber: 233,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\HRUpload.tsx\",\n                                lineNumber: 232,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                className: \"bg-purple-50 border-purple-200\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    className: \"p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-semibold text-purple-900 mb-2\",\n                                            children: \"\\uD83D\\uDD12 Your data is secure:\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\HRUpload.tsx\",\n                                            lineNumber: 246,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"text-purple-800 space-y-1 text-sm\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"• All data is encrypted and HIPAA compliant\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\HRUpload.tsx\",\n                                                    lineNumber: 248,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"• Files are automatically deleted after analysis\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\HRUpload.tsx\",\n                                                    lineNumber: 249,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"• Only aggregated insights are stored\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\HRUpload.tsx\",\n                                                    lineNumber: 250,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"• Full privacy and confidentiality guaranteed\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\HRUpload.tsx\",\n                                                    lineNumber: 251,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\HRUpload.tsx\",\n                                            lineNumber: 247,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\HRUpload.tsx\",\n                                    lineNumber: 245,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\HRUpload.tsx\",\n                                lineNumber: 244,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\HRUpload.tsx\",\n                        lineNumber: 231,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\HRUpload.tsx\",\n                lineNumber: 66,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\HRUpload.tsx\",\n        lineNumber: 51,\n        columnNumber: 5\n    }, undefined);\n};\n_s(HRUpload, \"4zUT5iN9ollxO8OzZVAsRQ02PTk=\", false, function() {\n    return [\n        _lib_react_router_dom__WEBPACK_IMPORTED_MODULE_6__.useNavigate\n    ];\n});\n_c = HRUpload;\n/* harmony default export */ __webpack_exports__[\"default\"] = (HRUpload);\nvar _c;\n$RefreshReg$(_c, \"HRUpload\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/census/public/HRUpload.tsx\n"));

/***/ })

});