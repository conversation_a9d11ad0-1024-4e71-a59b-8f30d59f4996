"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/census/page",{

/***/ "(app-pages-browser)/./src/app/census/components/EmpCensusApp.tsx":
/*!****************************************************!*\
  !*** ./src/app/census/components/EmpCensusApp.tsx ***!
  \****************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ EmpCensusApp; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _NavigationProvider__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./NavigationProvider */ \"(app-pages-browser)/./src/app/census/components/NavigationProvider.tsx\");\n/* harmony import */ var _public_Index__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../public/Index */ \"(app-pages-browser)/./src/app/census/public/Index.tsx\");\n/* harmony import */ var _public_BrokerDashboard__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../public/BrokerDashboard */ \"(app-pages-browser)/./src/app/census/public/BrokerDashboard.tsx\");\n/* harmony import */ var _public_EmployerInsight__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../public/EmployerInsight */ \"(app-pages-browser)/./src/app/census/public/EmployerInsight.tsx\");\n/* harmony import */ var _public_HRInsight__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../public/HRInsight */ \"(app-pages-browser)/./src/app/census/public/HRInsight.tsx\");\n/* harmony import */ var _public_UploadCensus__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../public/UploadCensus */ \"(app-pages-browser)/./src/app/census/public/UploadCensus.tsx\");\n/* harmony import */ var _public_HRUpload__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../public/HRUpload */ \"(app-pages-browser)/./src/app/census/public/HRUpload.tsx\");\n/* harmony import */ var _public_EmployerInvite__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../public/EmployerInvite */ \"(app-pages-browser)/./src/app/census/public/EmployerInvite.tsx\");\n/* harmony import */ var _public_PreviewReport__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../public/PreviewReport */ \"(app-pages-browser)/./src/app/census/public/PreviewReport.tsx\");\n/* harmony import */ var _public_Processing__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ../public/Processing */ \"(app-pages-browser)/./src/app/census/public/Processing.tsx\");\n/* harmony import */ var _public_Pricing__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ../public/Pricing */ \"(app-pages-browser)/./src/app/census/public/Pricing.tsx\");\n/* harmony import */ var _public_Billing__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ../public/Billing */ \"(app-pages-browser)/./src/app/census/public/Billing.tsx\");\n/* harmony import */ var _public_GenerateProposal__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ../public/GenerateProposal */ \"(app-pages-browser)/./src/app/census/public/GenerateProposal.tsx\");\n/* harmony import */ var _public_LoginPrompt__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ../public/LoginPrompt */ \"(app-pages-browser)/./src/app/census/public/LoginPrompt.tsx\");\n/* harmony import */ var _public_NotFound__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ../public/NotFound */ \"(app-pages-browser)/./src/app/census/public/NotFound.tsx\");\n/* harmony import */ var _public_FilePreview__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! ../public/FilePreview */ \"(app-pages-browser)/./src/app/census/public/FilePreview.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n// Import from public directory (where pages are now located)\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction EmpCensusApp() {\n    _s();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.useSearchParams)();\n    const pageParam = searchParams.get(\"page\") || \"home\";\n    const id = searchParams.get(\"id\");\n    // Handle page parameters that might include IDs (like employer-insight/1)\n    const [page, pageId] = pageParam.includes(\"/\") ? pageParam.split(\"/\") : [\n        pageParam,\n        null\n    ];\n    const renderPage = ()=>{\n        switch(page){\n            case \"home\":\n            case \"\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_public_Index__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\components\\\\EmpCensusApp.tsx\",\n                    lineNumber: 35,\n                    columnNumber: 16\n                }, this);\n            case \"dashboard\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_public_BrokerDashboard__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\components\\\\EmpCensusApp.tsx\",\n                    lineNumber: 37,\n                    columnNumber: 16\n                }, this);\n            case \"employer-insight\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_public_EmployerInsight__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\components\\\\EmpCensusApp.tsx\",\n                    lineNumber: 39,\n                    columnNumber: 16\n                }, this);\n            case \"hr-insight\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_public_HRInsight__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\components\\\\EmpCensusApp.tsx\",\n                    lineNumber: 41,\n                    columnNumber: 16\n                }, this);\n            case \"upload-census\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_public_UploadCensus__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\components\\\\EmpCensusApp.tsx\",\n                    lineNumber: 43,\n                    columnNumber: 16\n                }, this);\n            case \"file-preview\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_public_FilePreview__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\components\\\\EmpCensusApp.tsx\",\n                    lineNumber: 45,\n                    columnNumber: 16\n                }, this);\n            case \"hr-upload\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_public_HRUpload__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\components\\\\EmpCensusApp.tsx\",\n                    lineNumber: 47,\n                    columnNumber: 16\n                }, this);\n            case \"employer-invite\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_public_EmployerInvite__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\components\\\\EmpCensusApp.tsx\",\n                    lineNumber: 49,\n                    columnNumber: 16\n                }, this);\n            case \"preview-report\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_public_PreviewReport__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\components\\\\EmpCensusApp.tsx\",\n                    lineNumber: 51,\n                    columnNumber: 16\n                }, this);\n            case \"processing\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_public_Processing__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\components\\\\EmpCensusApp.tsx\",\n                    lineNumber: 53,\n                    columnNumber: 16\n                }, this);\n            case \"generate-proposal\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_public_GenerateProposal__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\components\\\\EmpCensusApp.tsx\",\n                    lineNumber: 55,\n                    columnNumber: 16\n                }, this);\n            case \"pricing\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_public_Pricing__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\components\\\\EmpCensusApp.tsx\",\n                    lineNumber: 57,\n                    columnNumber: 16\n                }, this);\n            case \"billing\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_public_Billing__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\components\\\\EmpCensusApp.tsx\",\n                    lineNumber: 59,\n                    columnNumber: 16\n                }, this);\n            case \"login-prompt\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_public_LoginPrompt__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\components\\\\EmpCensusApp.tsx\",\n                    lineNumber: 61,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_public_NotFound__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\components\\\\EmpCensusApp.tsx\",\n                    lineNumber: 63,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_NavigationProvider__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            children: renderPage()\n        }, void 0, false, {\n            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\components\\\\EmpCensusApp.tsx\",\n            lineNumber: 69,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\components\\\\EmpCensusApp.tsx\",\n        lineNumber: 68,\n        columnNumber: 5\n    }, this);\n}\n_s(EmpCensusApp, \"a+DZx9DY26Zf8FVy1bxe3vp9l1w=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_1__.useSearchParams\n    ];\n});\n_c = EmpCensusApp;\nvar _c;\n$RefreshReg$(_c, \"EmpCensusApp\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/census/components/EmpCensusApp.tsx\n"));

/***/ })

});