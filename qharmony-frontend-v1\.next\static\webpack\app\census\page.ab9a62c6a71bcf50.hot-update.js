"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/census/page",{

/***/ "(app-pages-browser)/./src/app/census/public/BrokerDashboard.tsx":
/*!***************************************************!*\
  !*** ./src/app/census/public/BrokerDashboard.tsx ***!
  \***************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../components/ui/button */ \"(app-pages-browser)/./src/app/census/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../components/ui/card */ \"(app-pages-browser)/./src/app/census/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../components/ui/input */ \"(app-pages-browser)/./src/app/census/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../components/ui/select */ \"(app-pages-browser)/./src/app/census/components/ui/select.tsx\");\n/* harmony import */ var _lib_react_router_dom__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../lib/react-router-dom */ \"(app-pages-browser)/./src/app/census/lib/react-router-dom.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_DollarSign_Lock_Mail_Plus_Search_Share2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,DollarSign,Lock,Mail,Plus,Search,Share2,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_DollarSign_Lock_Mail_Plus_Search_Share2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,DollarSign,Lock,Mail,Plus,Search,Share2,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_DollarSign_Lock_Mail_Plus_Search_Share2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,DollarSign,Lock,Mail,Plus,Search,Share2,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_DollarSign_Lock_Mail_Plus_Search_Share2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,DollarSign,Lock,Mail,Plus,Search,Share2,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_DollarSign_Lock_Mail_Plus_Search_Share2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,DollarSign,Lock,Mail,Plus,Search,Share2,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_DollarSign_Lock_Mail_Plus_Search_Share2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,DollarSign,Lock,Mail,Plus,Search,Share2,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_DollarSign_Lock_Mail_Plus_Search_Share2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,DollarSign,Lock,Mail,Plus,Search,Share2,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_DollarSign_Lock_Mail_Plus_Search_Share2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,DollarSign,Lock,Mail,Plus,Search,Share2,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/lock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_DollarSign_Lock_Mail_Plus_Search_Share2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,DollarSign,Lock,Mail,Plus,Search,Share2,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/share-2.js\");\n/* harmony import */ var _components_AskBrea__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../components/AskBrea */ \"(app-pages-browser)/./src/app/census/components/AskBrea.tsx\");\n/* harmony import */ var _components_NavigationDropdown__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../components/NavigationDropdown */ \"(app-pages-browser)/./src/app/census/components/NavigationDropdown.tsx\");\n/* harmony import */ var _components_ProfileHandler__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../components/ProfileHandler */ \"(app-pages-browser)/./src/app/census/components/ProfileHandler.tsx\");\n/* harmony import */ var _hooks_usePlanRestrictions__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../hooks/usePlanRestrictions */ \"(app-pages-browser)/./src/app/census/hooks/usePlanRestrictions.tsx\");\n/* harmony import */ var _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ../components/ui/use-toast */ \"(app-pages-browser)/./src/app/census/components/ui/use-toast.ts\");\n/* harmony import */ var _redux_hooks__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/redux/hooks */ \"(app-pages-browser)/./src/redux/hooks.ts\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst BrokerDashboard = ()=>{\n    _s();\n    const navigate = (0,_lib_react_router_dom__WEBPACK_IMPORTED_MODULE_6__.useNavigate)();\n    const { toast } = (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_11__.useToast)();\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [employeeCountFilter, setEmployeeCountFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [riskScoreFilter, setRiskScoreFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [suggestedPlanFilter, setSuggestedPlanFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const userDetails = (0,_redux_hooks__WEBPACK_IMPORTED_MODULE_12__.useAppSelector)((state)=>state.user.userProfile);\n    const { canViewReport, reportsRemaining, isAtLimit, trackReportView } = (0,_hooks_usePlanRestrictions__WEBPACK_IMPORTED_MODULE_10__.usePlanRestrictions)();\n    // Mock data for multiple employers\n    const employerInsights = [\n        {\n            id: \"1\",\n            companyName: \"TechCorp Solutions\",\n            employees: 43,\n            averageAge: 36,\n            dependents: 1.3,\n            planType: \"PPO + HSA Combo\",\n            potentialSavings: \"$127,500\",\n            riskScore: 6.2,\n            uploadDate: \"2024-01-15\",\n            status: \"analyzed\"\n        },\n        {\n            id: \"2\",\n            companyName: \"Green Manufacturing\",\n            employees: 87,\n            averageAge: 42,\n            dependents: 1.8,\n            planType: \"Traditional PPO\",\n            potentialSavings: \"$245,000\",\n            riskScore: 7.1,\n            uploadDate: \"2024-01-10\",\n            status: \"analyzed\"\n        },\n        {\n            id: \"3\",\n            companyName: \"StartupXYZ\",\n            employees: 18,\n            averageAge: 29,\n            dependents: 0.8,\n            planType: \"HSA Only\",\n            potentialSavings: \"$32,400\",\n            riskScore: 4.5,\n            uploadDate: \"2024-01-18\",\n            status: \"processing\"\n        }\n    ];\n    // Filter logic\n    const filteredEmployers = employerInsights.filter((employer)=>{\n        // Search filter\n        if (searchTerm && !employer.companyName.toLowerCase().includes(searchTerm.toLowerCase())) {\n            return false;\n        }\n        // Employee count filter\n        if (employeeCountFilter !== \"all\") {\n            if (employeeCountFilter === \"1-50\" && employer.employees > 50) return false;\n            if (employeeCountFilter === \"51-100\" && (employer.employees < 51 || employer.employees > 100)) return false;\n            if (employeeCountFilter === \"101+\" && employer.employees <= 100) return false;\n        }\n        // Risk score filter\n        if (riskScoreFilter !== \"all\") {\n            if (riskScoreFilter === \"low\" && employer.riskScore >= 5) return false;\n            if (riskScoreFilter === \"medium\" && (employer.riskScore < 5 || employer.riskScore > 7)) return false;\n            if (riskScoreFilter === \"high\" && employer.riskScore <= 7) return false;\n        }\n        // Suggested plan filter\n        if (suggestedPlanFilter !== \"all\") {\n            if (suggestedPlanFilter === \"ppo-hsa\" && employer.planType !== \"PPO + HSA Combo\") return false;\n            if (suggestedPlanFilter === \"traditional-ppo\" && employer.planType !== \"Traditional PPO\") return false;\n            if (suggestedPlanFilter === \"other\" && employer.planType !== \"HSA Only\" && employer.planType !== \"Modern HSA Plus Plan\") return false;\n        }\n        return true;\n    });\n    const handleShareInsight = (companyName)=>{\n        const shareUrl = \"\".concat(window.location.origin, \"/shared-insight/\").concat(companyName.toLowerCase().replace(/\\s+/g, \"-\"));\n        navigator.clipboard.writeText(shareUrl);\n        console.log(\"Share link copied for \".concat(companyName));\n    };\n    const handleTileClick = (employerId, companyName)=>{\n        console.log(\"Attempting to view report for \".concat(companyName, \" (ID: \").concat(employerId, \")\"));\n        console.log(\"Can view report: \".concat(canViewReport(employerId)));\n        console.log(\"Reports remaining: \".concat(reportsRemaining));\n        console.log(\"Is at limit: \".concat(isAtLimit));\n        if (!canViewReport(employerId)) {\n            toast({\n                title: \"Upgrade Required\",\n                description: \"You've reached your free report limit (2 reports). Upgrade to Pro for unlimited access.\",\n                variant: \"destructive\"\n            });\n            navigate(\"/pricing\");\n            return;\n        }\n        // Track the report view\n        trackReportView(employerId);\n        navigate(\"?page=employer-insight/\".concat(employerId));\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-slate-50 to-blue-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"border-b bg-white/90 backdrop-blur-sm sticky top-0 z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto px-3 sm:px-4 py-3\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-xl sm:text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent\",\n                                children: \"BenOsphere\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                lineNumber: 132,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2 sm:space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_NavigationDropdown__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                        lineNumber: 136,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AskBrea__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        context: \"broker dashboard with multiple client insights\",\n                                        size: \"sm\",\n                                        variant: \"outline\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                        lineNumber: 137,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ProfileHandler__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"hidden sm:inline-flex\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                        lineNumber: 138,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                lineNumber: 135,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                        lineNumber: 131,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                    lineNumber: 130,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                lineNumber: 129,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"container mx-auto px-3 sm:px-4 py-4 sm:py-6 max-w-7xl\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col lg:flex-row lg:justify-between lg:items-start gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-2xl sm:text-3xl font-bold text-gray-900 mb-2\",\n                                                children: \"\\uD83D\\uDCCA Broker Dashboard\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                lineNumber: 149,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600 text-sm sm:text-base mb-4 lg:mb-0\",\n                                                children: \"Manage and analyze your client census data\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                lineNumber: 152,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                        lineNumber: 148,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col sm:flex-row gap-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                size: \"sm\",\n                                                onClick: ()=>{\n                                                    // Route based on user role: brokers go to upload-census, admins go to hr-upload\n                                                    if (userDetails.isBroker) {\n                                                        navigate(\"/upload-census\");\n                                                    } else if (userDetails.isAdmin && !userDetails.isBroker) {\n                                                        navigate(\"?page=hr-upload\");\n                                                    } else {\n                                                        navigate(\"/upload-census\"); // Default fallback\n                                                    }\n                                                },\n                                                className: \"bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_DollarSign_Lock_Mail_Plus_Search_Share2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                        className: \"h-4 w-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                        lineNumber: 173,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    \"Upload New Census\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                lineNumber: 159,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                variant: \"outline\",\n                                                size: \"sm\",\n                                                onClick: ()=>navigate(\"/employer-invite\"),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_DollarSign_Lock_Mail_Plus_Search_Share2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                        className: \"h-4 w-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                        lineNumber: 177,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    \"Invite Employer\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                lineNumber: 176,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                        lineNumber: 158,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                lineNumber: 147,\n                                columnNumber: 11\n                            }, undefined),\n                            reportsRemaining <= 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                className: \"mt-4 bg-gradient-to-r from-amber-50 to-orange-50 border-amber-200\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    className: \"p-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-start space-x-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_DollarSign_Lock_Mail_Plus_Search_Share2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                        className: \"h-5 w-5 text-amber-600 mt-0.5 flex-shrink-0\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                        lineNumber: 189,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"font-medium text-amber-800\",\n                                                                children: isAtLimit ? \"Report limit reached!\" : \"\".concat(reportsRemaining, \" free report\").concat(reportsRemaining === 1 ? \"\" : \"s\", \" remaining\")\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                lineNumber: 191,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-amber-700\",\n                                                                children: isAtLimit ? \"Upgrade to Pro for unlimited reports\" : \"Upgrade to Pro for unlimited access\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                lineNumber: 194,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                        lineNumber: 190,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                lineNumber: 188,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                onClick: ()=>navigate(\"/pricing\"),\n                                                className: \"bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white flex-shrink-0\",\n                                                size: \"sm\",\n                                                children: \"Upgrade Now\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                lineNumber: 199,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                        lineNumber: 187,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                    lineNumber: 186,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                lineNumber: 185,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                        lineNumber: 146,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        className: \"mb-6 shadow-lg border-0\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                            className: \"p-4 sm:p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_DollarSign_Lock_Mail_Plus_Search_Share2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                className: \"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                lineNumber: 218,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                placeholder: \"Search by Client Name\",\n                                                value: searchTerm,\n                                                onChange: (e)=>setSearchTerm(e.target.value),\n                                                className: \"pl-10\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                lineNumber: 219,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                        lineNumber: 217,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 sm:grid-cols-3 gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"text-sm font-medium text-gray-700 mb-2 block\",\n                                                        children: \"Employee Count\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                        lineNumber: 230,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.Select, {\n                                                        value: employeeCountFilter,\n                                                        onValueChange: setEmployeeCountFilter,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectTrigger, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectValue, {\n                                                                    placeholder: \"All\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                    lineNumber: 233,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                lineNumber: 232,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectContent, {\n                                                                className: \"bg-white\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                                        value: \"all\",\n                                                                        children: \"All\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                        lineNumber: 236,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                                        value: \"1-50\",\n                                                                        children: \"1–50\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                        lineNumber: 237,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                                        value: \"51-100\",\n                                                                        children: \"51–100\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                        lineNumber: 238,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                                        value: \"101+\",\n                                                                        children: \"101+\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                        lineNumber: 239,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                lineNumber: 235,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                        lineNumber: 231,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                lineNumber: 229,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"text-sm font-medium text-gray-700 mb-2 block\",\n                                                        children: \"Risk Score\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                        lineNumber: 245,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.Select, {\n                                                        value: riskScoreFilter,\n                                                        onValueChange: setRiskScoreFilter,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectTrigger, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectValue, {\n                                                                    placeholder: \"All\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                    lineNumber: 248,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                lineNumber: 247,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectContent, {\n                                                                className: \"bg-white\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                                        value: \"all\",\n                                                                        children: \"All\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                        lineNumber: 251,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                                        value: \"low\",\n                                                                        children: \"Low (<5)\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                        lineNumber: 252,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                                        value: \"medium\",\n                                                                        children: \"Medium (5–7)\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                        lineNumber: 253,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                                        value: \"high\",\n                                                                        children: \"High (>7)\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                        lineNumber: 254,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                lineNumber: 250,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                        lineNumber: 246,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                lineNumber: 244,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"text-sm font-medium text-gray-700 mb-2 block\",\n                                                        children: \"Suggested Plan\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                        lineNumber: 260,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.Select, {\n                                                        value: suggestedPlanFilter,\n                                                        onValueChange: setSuggestedPlanFilter,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectTrigger, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectValue, {\n                                                                    placeholder: \"All\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                    lineNumber: 263,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                lineNumber: 262,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectContent, {\n                                                                className: \"bg-white\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                                        value: \"all\",\n                                                                        children: \"All\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                        lineNumber: 266,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                                        value: \"ppo-hsa\",\n                                                                        children: \"PPO + HSA Combo\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                        lineNumber: 267,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                                        value: \"traditional-ppo\",\n                                                                        children: \"Traditional PPO\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                        lineNumber: 268,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                                        value: \"other\",\n                                                                        children: \"Other\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                        lineNumber: 269,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                lineNumber: 265,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                        lineNumber: 261,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                lineNumber: 259,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                        lineNumber: 228,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                lineNumber: 215,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                            lineNumber: 214,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                        lineNumber: 213,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4 mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                className: \"bg-gradient-to-br from-blue-100 to-blue-200 border-blue-300 hover:shadow-md transition-all duration-200\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    className: \"p-3 sm:p-4 text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_DollarSign_Lock_Mail_Plus_Search_Share2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            className: \"h-6 w-6 sm:h-8 sm:w-8 mx-auto mb-1 sm:mb-2 text-blue-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                            lineNumber: 282,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs sm:text-sm text-blue-600 font-medium\",\n                                            children: \"Total Clients\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                            lineNumber: 283,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xl sm:text-2xl font-bold text-blue-900\",\n                                            children: filteredEmployers.length\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                            lineNumber: 284,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                    lineNumber: 281,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                lineNumber: 280,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                className: \"bg-gradient-to-br from-emerald-100 to-teal-200 border-emerald-300 hover:shadow-md transition-all duration-200\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    className: \"p-3 sm:p-4 text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_DollarSign_Lock_Mail_Plus_Search_Share2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            className: \"h-6 w-6 sm:h-8 sm:w-8 mx-auto mb-1 sm:mb-2 text-emerald-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                            lineNumber: 290,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs sm:text-sm text-emerald-600 font-medium\",\n                                            children: \"Total Employees\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                            lineNumber: 291,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xl sm:text-2xl font-bold text-emerald-900\",\n                                            children: filteredEmployers.reduce((sum, emp)=>sum + emp.employees, 0)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                            lineNumber: 292,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                    lineNumber: 289,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                lineNumber: 288,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                className: \"bg-gradient-to-br from-orange-100 to-red-200 border-orange-300 hover:shadow-md transition-all duration-200\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    className: \"p-3 sm:p-4 text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_DollarSign_Lock_Mail_Plus_Search_Share2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                            className: \"h-6 w-6 sm:h-8 sm:w-8 mx-auto mb-1 sm:mb-2 text-orange-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                            lineNumber: 300,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs sm:text-sm text-orange-600 font-medium\",\n                                            children: \"Total Potential Savings\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                            lineNumber: 301,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-lg sm:text-2xl font-bold text-orange-900\",\n                                            children: \"$404,900\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                            lineNumber: 302,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                    lineNumber: 299,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                lineNumber: 298,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                className: \"bg-gradient-to-br from-purple-100 to-pink-200 border-purple-300 hover:shadow-md transition-all duration-200\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    className: \"p-3 sm:p-4 text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_DollarSign_Lock_Mail_Plus_Search_Share2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                            className: \"h-6 w-6 sm:h-8 sm:w-8 mx-auto mb-1 sm:mb-2 text-purple-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                            lineNumber: 308,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs sm:text-sm text-purple-600 font-medium\",\n                                            children: \"Avg Risk Score\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                            lineNumber: 309,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xl sm:text-2xl font-bold text-purple-900\",\n                                            children: \"5.9/10\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                            lineNumber: 310,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                    lineNumber: 307,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                lineNumber: 306,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                        lineNumber: 279,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        className: \"shadow-lg border-0\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                className: \"pb-3 sm:pb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                    className: \"text-lg sm:text-xl\",\n                                    children: \"Client Census Insights\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                    lineNumber: 318,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                lineNumber: 317,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                className: \"p-3 sm:p-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3 sm:space-y-4\",\n                                    children: filteredEmployers.map((employer)=>{\n                                        const canAccess = canViewReport(employer.id);\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                            className: \"border-l-4 border-l-blue-500 transition-all duration-200 cursor-pointer \".concat(canAccess ? \"hover:shadow-md hover:-translate-y-0.5\" : \"opacity-60 hover:opacity-80 cursor-not-allowed\", \" \").concat(!canAccess ? \"relative\" : \"\"),\n                                            onClick: ()=>handleTileClick(employer.id, employer.companyName),\n                                            children: [\n                                                !canAccess && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute inset-0 bg-gray-200/50 backdrop-blur-[1px] rounded-lg flex items-center justify-center z-10\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-white p-3 rounded-lg shadow-lg flex items-center space-x-2 border border-amber-200\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_DollarSign_Lock_Mail_Plus_Search_Share2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                className: \"h-4 w-4 text-amber-600\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                lineNumber: 338,\n                                                                columnNumber: 27\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm font-medium text-amber-800\",\n                                                                children: \"Upgrade to view\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                lineNumber: 339,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                        lineNumber: 337,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                    lineNumber: 336,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                                    className: \"p-4 sm:p-5\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex flex-col sm:flex-row justify-between items-start mb-3 sm:mb-4 gap-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                            className: \"text-lg sm:text-xl font-semibold text-gray-900 mb-1\",\n                                                                            children: employer.companyName\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                            lineNumber: 347,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-xs sm:text-sm text-gray-500\",\n                                                                            children: [\n                                                                                \"Uploaded: \",\n                                                                                new Date(employer.uploadDate).toLocaleDateString()\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                            lineNumber: 350,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                    lineNumber: 346,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex space-x-2 w-full sm:w-auto\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                        size: \"sm\",\n                                                                        variant: \"outline\",\n                                                                        onClick: (e)=>{\n                                                                            e.stopPropagation();\n                                                                            if (canAccess) {\n                                                                                handleShareInsight(employer.companyName);\n                                                                            } else {\n                                                                                navigate(\"/pricing\");\n                                                                            }\n                                                                        },\n                                                                        className: \"flex-1 sm:flex-none\",\n                                                                        disabled: !canAccess,\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_DollarSign_Lock_Mail_Plus_Search_Share2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                                className: \"h-3 w-3 sm:h-4 sm:w-4 sm:mr-1\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                                lineNumber: 369,\n                                                                                columnNumber: 29\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"hidden sm:inline\",\n                                                                                children: \"Share\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                                lineNumber: 370,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                        lineNumber: 355,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                    lineNumber: 354,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                            lineNumber: 345,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"grid grid-cols-2 lg:grid-cols-4 gap-2 sm:gap-3 mb-3 sm:mb-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-center p-2 sm:p-3 bg-gradient-to-br from-blue-50 to-blue-100 rounded-lg hover:scale-105 transition-transform duration-200\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-xs sm:text-sm text-blue-600 font-medium\",\n                                                                            children: \"Employees\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                            lineNumber: 377,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm sm:text-lg font-bold text-blue-900\",\n                                                                            children: employer.employees\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                            lineNumber: 378,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                    lineNumber: 376,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-center p-2 sm:p-3 bg-gradient-to-br from-emerald-50 to-emerald-100 rounded-lg hover:scale-105 transition-transform duration-200\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-xs sm:text-sm text-emerald-600 font-medium\",\n                                                                            children: \"Avg Age\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                            lineNumber: 381,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm sm:text-lg font-bold text-emerald-900\",\n                                                                            children: employer.averageAge\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                            lineNumber: 382,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                    lineNumber: 380,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-center p-2 sm:p-3 bg-gradient-to-br from-orange-50 to-orange-100 rounded-lg hover:scale-105 transition-transform duration-200\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-xs sm:text-sm text-orange-600 font-medium\",\n                                                                            children: \"Savings\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                            lineNumber: 385,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm sm:text-lg font-bold text-orange-900\",\n                                                                            children: employer.potentialSavings\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                            lineNumber: 386,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                    lineNumber: 384,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-center p-2 sm:p-3 bg-gradient-to-br from-purple-50 to-purple-100 rounded-lg hover:scale-105 transition-transform duration-200\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-xs sm:text-sm text-purple-600 font-medium\",\n                                                                            children: \"Risk Score\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                            lineNumber: 389,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm sm:text-lg font-bold text-purple-900\",\n                                                                            children: [\n                                                                                employer.riskScore,\n                                                                                \"/10\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                            lineNumber: 390,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                    lineNumber: 388,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                            lineNumber: 375,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex flex-col sm:flex-row justify-between items-start sm:items-center gap-2 sm:gap-0\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-sm\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-gray-600\",\n                                                                            children: \"Suggested Plan: \"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                            lineNumber: 396,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"font-medium text-gray-900\",\n                                                                            children: employer.planType\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                            lineNumber: 397,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                    lineNumber: 395,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"px-2 py-1 rounded-full text-xs font-medium \".concat(employer.status === \"analyzed\" ? \"bg-green-100 text-green-800\" : \"bg-yellow-100 text-yellow-800\"),\n                                                                    children: employer.status === \"analyzed\" ? \"✅ Analyzed\" : \"⏳ Processing\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                    lineNumber: 399,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                            lineNumber: 394,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                    lineNumber: 344,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, employer.id, true, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                            lineNumber: 326,\n                                            columnNumber: 19\n                                        }, undefined);\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                    lineNumber: 321,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                lineNumber: 320,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                        lineNumber: 316,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        className: \"mt-6 bg-gradient-to-r from-purple-100 to-blue-100 border-0 shadow-lg\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                            className: \"p-4 sm:p-6 text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg sm:text-xl font-bold text-gray-900 mb-2\",\n                                    children: \"\\uD83D\\uDE80 Grow Your Network\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                    lineNumber: 418,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-700 mb-4 text-sm sm:text-base\",\n                                    children: \"Share BenOsphere with other brokers and get rewards for every signup\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                    lineNumber: 421,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col sm:flex-row justify-center space-y-2 sm:space-y-0 sm:space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            className: \"bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white transition-all duration-200 hover:scale-105\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_DollarSign_Lock_Mail_Plus_Search_Share2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                    className: \"h-4 w-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                    lineNumber: 426,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                \"Refer a Broker\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                            lineNumber: 425,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            variant: \"outline\",\n                                            className: \"hover:bg-white/50\",\n                                            children: \"View Referral Rewards\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                            lineNumber: 429,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                    lineNumber: 424,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                            lineNumber: 417,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                        lineNumber: 416,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                lineNumber: 144,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n        lineNumber: 127,\n        columnNumber: 5\n    }, undefined);\n};\n_s(BrokerDashboard, \"7yxZ1f9yBh+KJHBk0ttQUiQcyts=\", false, function() {\n    return [\n        _lib_react_router_dom__WEBPACK_IMPORTED_MODULE_6__.useNavigate,\n        _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_11__.useToast,\n        _redux_hooks__WEBPACK_IMPORTED_MODULE_12__.useAppSelector,\n        _hooks_usePlanRestrictions__WEBPACK_IMPORTED_MODULE_10__.usePlanRestrictions\n    ];\n});\n_c = BrokerDashboard;\n/* harmony default export */ __webpack_exports__[\"default\"] = (BrokerDashboard);\nvar _c;\n$RefreshReg$(_c, \"BrokerDashboard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/census/public/BrokerDashboard.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/census/public/EmployerInvite.tsx":
/*!**************************************************!*\
  !*** ./src/app/census/public/EmployerInvite.tsx ***!
  \**************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../components/ui/button */ \"(app-pages-browser)/./src/app/census/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../components/ui/card */ \"(app-pages-browser)/./src/app/census/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../components/ui/input */ \"(app-pages-browser)/./src/app/census/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../components/ui/label */ \"(app-pages-browser)/./src/app/census/components/ui/label.tsx\");\n/* harmony import */ var _lib_react_router_dom__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../lib/react-router-dom */ \"(app-pages-browser)/./src/app/census/lib/react-router-dom.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Mail_Send_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Mail,Send,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Mail_Send_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Mail,Send,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Mail_Send_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Mail,Send,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/send.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Mail_Send_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Mail,Send,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _components_ProfileHandler__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../components/ProfileHandler */ \"(app-pages-browser)/./src/app/census/components/ProfileHandler.tsx\");\n/* harmony import */ var _redux_hooks__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/redux/hooks */ \"(app-pages-browser)/./src/redux/hooks.ts\");\n/* harmony import */ var _middleware_company_middleware__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/middleware/company_middleware */ \"(app-pages-browser)/./src/middleware/company_middleware.ts\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nconst EmployerInvite = ()=>{\n    _s();\n    const navigate = (0,_lib_react_router_dom__WEBPACK_IMPORTED_MODULE_6__.useNavigate)();\n    const [employerName, setEmployerName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [employerEmail, setEmployerEmail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [companyName, setCompanyName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isSubmitted, setIsSubmitted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const userId = (0,_redux_hooks__WEBPACK_IMPORTED_MODULE_8__.useAppSelector)((state)=>state.user._id);\n    const handleSendInvite = async (e)=>{\n        e.preventDefault();\n        if (!employerName || !employerEmail || !companyName) {\n            alert(\"Please fill out all required fields.\");\n            return;\n        }\n        setIsLoading(true);\n        try {\n            const response = await (0,_middleware_company_middleware__WEBPACK_IMPORTED_MODULE_9__.brokerAddsCompany)(userId, companyName, employerEmail, employerName);\n            if (response && response.status === 200) {\n                setIsSubmitted(true);\n            } else {\n                alert(\"Failed to send invite. Please try again.\");\n            }\n        } catch (error) {\n            console.error(\"Error sending invite:\", error);\n            alert(\"Failed to send invite. Please try again.\");\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"border-b bg-white/80 backdrop-blur-sm\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto px-4 py-4 flex justify-between items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    variant: \"ghost\",\n                                    onClick: ()=>navigate(\"/\"),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Mail_Send_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\EmployerInvite.tsx\",\n                                            lineNumber: 61,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        \"Back to Home\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\EmployerInvite.tsx\",\n                                    lineNumber: 60,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent\",\n                                    children: \"BenOsphere\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\EmployerInvite.tsx\",\n                                    lineNumber: 64,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\EmployerInvite.tsx\",\n                            lineNumber: 59,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ProfileHandler__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\EmployerInvite.tsx\",\n                            lineNumber: 66,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\EmployerInvite.tsx\",\n                    lineNumber: 58,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\EmployerInvite.tsx\",\n                lineNumber: 57,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"container mx-auto px-4 py-16 max-w-2xl\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center mb-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-20 h-20 mx-auto mb-6 bg-purple-100 rounded-full flex items-center justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Mail_Send_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                    className: \"h-10 w-10 text-purple-600\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\EmployerInvite.tsx\",\n                                    lineNumber: 73,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\EmployerInvite.tsx\",\n                                lineNumber: 72,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-4xl font-bold text-gray-900 mb-4\",\n                                children: \"\\uD83D\\uDCE9 Don't have the census? Let the employer upload it.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\EmployerInvite.tsx\",\n                                lineNumber: 75,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xl text-gray-600\",\n                                children: \"Send a secure link to your client — they'll be guided to upload their census. You'll be notified once their enriched group report is ready.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\EmployerInvite.tsx\",\n                                lineNumber: 78,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\EmployerInvite.tsx\",\n                        lineNumber: 71,\n                        columnNumber: 9\n                    }, undefined),\n                    !isSubmitted ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        className: \"shadow-2xl border-0\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                    className: \"text-center text-2xl\",\n                                    children: \"Send Employer Invite\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\EmployerInvite.tsx\",\n                                    lineNumber: 86,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\EmployerInvite.tsx\",\n                                lineNumber: 85,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                className: \"space-y-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                        onSubmit: handleSendInvite,\n                                        className: \"space-y-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                        htmlFor: \"company-name\",\n                                                        className: \"text-base font-medium\",\n                                                        children: \"Company Name *\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\EmployerInvite.tsx\",\n                                                        lineNumber: 91,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                        id: \"company-name\",\n                                                        type: \"text\",\n                                                        placeholder: \"e.g. ABC Corp, Smith & Associates\",\n                                                        value: companyName,\n                                                        onChange: (e)=>setCompanyName(e.target.value),\n                                                        required: true,\n                                                        className: \"h-12 text-base\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\EmployerInvite.tsx\",\n                                                        lineNumber: 94,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\EmployerInvite.tsx\",\n                                                lineNumber: 90,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                        htmlFor: \"employer-name\",\n                                                        className: \"text-base font-medium\",\n                                                        children: \"Employer Name *\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\EmployerInvite.tsx\",\n                                                        lineNumber: 106,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                        id: \"employer-name\",\n                                                        type: \"text\",\n                                                        placeholder: \"John Smith\",\n                                                        value: employerName,\n                                                        onChange: (e)=>setEmployerName(e.target.value),\n                                                        required: true,\n                                                        className: \"h-12 text-base\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\EmployerInvite.tsx\",\n                                                        lineNumber: 109,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\EmployerInvite.tsx\",\n                                                lineNumber: 105,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                        htmlFor: \"employer-email\",\n                                                        className: \"text-base font-medium\",\n                                                        children: \"\\uD83D\\uDCE7 Employer Contact Email *\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\EmployerInvite.tsx\",\n                                                        lineNumber: 121,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                        id: \"employer-email\",\n                                                        type: \"email\",\n                                                        placeholder: \"hr@company.<NAME_EMAIL>\",\n                                                        value: employerEmail,\n                                                        onChange: (e)=>setEmployerEmail(e.target.value),\n                                                        required: true,\n                                                        className: \"h-12 text-base\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\EmployerInvite.tsx\",\n                                                        lineNumber: 124,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\EmployerInvite.tsx\",\n                                                lineNumber: 120,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                type: \"submit\",\n                                                size: \"lg\",\n                                                className: \"w-full bg-purple-600 hover:bg-purple-700 text-white py-4 text-lg\",\n                                                disabled: !employerEmail || !employerName || !companyName || isLoading,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Mail_Send_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        className: \"mr-2 h-5 w-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\EmployerInvite.tsx\",\n                                                        lineNumber: 141,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    isLoading ? \"Sending...\" : \"➡️ Send Upload Link to Employer\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\EmployerInvite.tsx\",\n                                                lineNumber: 135,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\EmployerInvite.tsx\",\n                                        lineNumber: 89,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                        className: \"bg-purple-50 border-purple-200\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                            className: \"p-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"font-semibold text-purple-900 mb-3\",\n                                                    children: \"What happens next:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\EmployerInvite.tsx\",\n                                                    lineNumber: 149,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                    className: \"text-purple-800 space-y-2 text-sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"flex items-start\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"mr-2 mt-0.5\",\n                                                                    children: \"1.\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\EmployerInvite.tsx\",\n                                                                    lineNumber: 152,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                \"Employer receives a secure, personalized upload link\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\EmployerInvite.tsx\",\n                                                            lineNumber: 151,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"flex items-start\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"mr-2 mt-0.5\",\n                                                                    children: \"2.\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\EmployerInvite.tsx\",\n                                                                    lineNumber: 156,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                \"They upload their census file through our guided process\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\EmployerInvite.tsx\",\n                                                            lineNumber: 155,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"flex items-start\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"mr-2 mt-0.5\",\n                                                                    children: \"3.\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\EmployerInvite.tsx\",\n                                                                    lineNumber: 160,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                \"We analyze the data and generate the enriched report\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\EmployerInvite.tsx\",\n                                                            lineNumber: 159,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"flex items-start\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"mr-2 mt-0.5\",\n                                                                    children: \"4.\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\EmployerInvite.tsx\",\n                                                                    lineNumber: 164,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                \"You get notified when the analysis is complete and ready to review\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\EmployerInvite.tsx\",\n                                                            lineNumber: 163,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\EmployerInvite.tsx\",\n                                                    lineNumber: 150,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\EmployerInvite.tsx\",\n                                            lineNumber: 148,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\EmployerInvite.tsx\",\n                                        lineNumber: 147,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center pt-4 border-t\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-500 mb-3\",\n                                                children: \"Prefer to upload yourself?\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\EmployerInvite.tsx\",\n                                                lineNumber: 173,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                variant: \"outline\",\n                                                onClick: ()=>navigate(\"/upload\"),\n                                                className: \"w-full\",\n                                                children: \"\\uD83D\\uDCE4 Upload Census File Directly\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\EmployerInvite.tsx\",\n                                                lineNumber: 174,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\EmployerInvite.tsx\",\n                                        lineNumber: 172,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\EmployerInvite.tsx\",\n                                lineNumber: 88,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\EmployerInvite.tsx\",\n                        lineNumber: 84,\n                        columnNumber: 11\n                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        className: \"shadow-2xl border-0\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                            className: \"p-12 text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-20 h-20 mx-auto mb-6 bg-green-100 rounded-full flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Mail_Send_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        className: \"h-10 w-10 text-green-600\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\EmployerInvite.tsx\",\n                                        lineNumber: 188,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\EmployerInvite.tsx\",\n                                    lineNumber: 187,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-2xl font-bold text-gray-900 mb-4\",\n                                    children: [\n                                        \"✅ Company invite sent to \",\n                                        employerEmail,\n                                        \"!\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\EmployerInvite.tsx\",\n                                    lineNumber: 191,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-lg text-gray-600 mb-6\",\n                                    children: [\n                                        employerName,\n                                        \" from \",\n                                        companyName,\n                                        \" will receive an invite to join the platform and upload their census.\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\EmployerInvite.tsx\",\n                                    lineNumber: 195,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-green-50 border border-green-200 rounded-lg p-6 mb-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-green-800 text-sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Next steps:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\EmployerInvite.tsx\",\n                                                lineNumber: 201,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\EmployerInvite.tsx\",\n                                                lineNumber: 202,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            \"• The employer will receive a secure upload link\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\EmployerInvite.tsx\",\n                                                lineNumber: 204,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            \"• You'll get an email notification when analysis is complete\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\EmployerInvite.tsx\",\n                                                lineNumber: 206,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            \"• Log in to BenOsphere to access the full enriched report\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\EmployerInvite.tsx\",\n                                        lineNumber: 200,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\EmployerInvite.tsx\",\n                                    lineNumber: 199,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            onClick: ()=>navigate(\"/\"),\n                                            className: \"w-full bg-blue-600 hover:bg-blue-700\",\n                                            children: \"Return to Dashboard\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\EmployerInvite.tsx\",\n                                            lineNumber: 212,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            variant: \"outline\",\n                                            onClick: ()=>{\n                                                setIsSubmitted(false);\n                                                setEmployerName(\"\");\n                                                setEmployerEmail(\"\");\n                                                setCompanyName(\"\");\n                                            },\n                                            className: \"w-full\",\n                                            children: \"Send Another Invite\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\EmployerInvite.tsx\",\n                                            lineNumber: 219,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\EmployerInvite.tsx\",\n                                    lineNumber: 211,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\EmployerInvite.tsx\",\n                            lineNumber: 186,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\EmployerInvite.tsx\",\n                        lineNumber: 185,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-8 text-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            className: \"bg-gray-50 border-gray-200\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                className: \"p-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-gray-600\",\n                                    children: \"\\uD83D\\uDD12 All uploads are secure and encrypted. Census data is processed confidentially and never shared with third parties. HIPAA compliant.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\EmployerInvite.tsx\",\n                                    lineNumber: 240,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\EmployerInvite.tsx\",\n                                lineNumber: 239,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\EmployerInvite.tsx\",\n                            lineNumber: 238,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\EmployerInvite.tsx\",\n                        lineNumber: 237,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\EmployerInvite.tsx\",\n                lineNumber: 70,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\EmployerInvite.tsx\",\n        lineNumber: 55,\n        columnNumber: 5\n    }, undefined);\n};\n_s(EmployerInvite, \"Sw/aFJtDKWl5aNkt2GHb/gbDOzI=\", false, function() {\n    return [\n        _lib_react_router_dom__WEBPACK_IMPORTED_MODULE_6__.useNavigate,\n        _redux_hooks__WEBPACK_IMPORTED_MODULE_8__.useAppSelector\n    ];\n});\n_c = EmployerInvite;\n/* harmony default export */ __webpack_exports__[\"default\"] = (EmployerInvite);\nvar _c;\n$RefreshReg$(_c, \"EmployerInvite\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvY2Vuc3VzL3B1YmxpYy9FbXBsb3llckludml0ZS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQ2lDO0FBQ2dCO0FBQ2dDO0FBQ2xDO0FBQ0E7QUFDTztBQUNNO0FBQ0Y7QUFDWDtBQUNxQjtBQUVwRSxNQUFNZ0IsaUJBQWlCOztJQUNyQixNQUFNQyxXQUFXVCxrRUFBV0E7SUFDNUIsTUFBTSxDQUFDVSxjQUFjQyxnQkFBZ0IsR0FBR25CLCtDQUFRQSxDQUFDO0lBQ2pELE1BQU0sQ0FBQ29CLGVBQWVDLGlCQUFpQixHQUFHckIsK0NBQVFBLENBQUM7SUFDbkQsTUFBTSxDQUFDc0IsYUFBYUMsZUFBZSxHQUFHdkIsK0NBQVFBLENBQUM7SUFDL0MsTUFBTSxDQUFDd0IsYUFBYUMsZUFBZSxHQUFHekIsK0NBQVFBLENBQUM7SUFDL0MsTUFBTSxDQUFDMEIsV0FBV0MsYUFBYSxHQUFHM0IsK0NBQVFBLENBQUM7SUFFM0MsTUFBTTRCLFNBQVNkLDREQUFjQSxDQUFDLENBQUNlLFFBQVVBLE1BQU1DLElBQUksQ0FBQ0MsR0FBRztJQUV2RCxNQUFNQyxtQkFBbUIsT0FBT0M7UUFDOUJBLEVBQUVDLGNBQWM7UUFFaEIsSUFBSSxDQUFDaEIsZ0JBQWdCLENBQUNFLGlCQUFpQixDQUFDRSxhQUFhO1lBQ25EYSxNQUFNO1lBQ047UUFDRjtRQUVBUixhQUFhO1FBRWIsSUFBSTtZQUNGLE1BQU1TLFdBQVcsTUFBTXJCLGlGQUFpQkEsQ0FDdENhLFFBQ0FOLGFBQ0FGLGVBQ0FGO1lBR0YsSUFBSWtCLFlBQVlBLFNBQVNDLE1BQU0sS0FBSyxLQUFLO2dCQUN2Q1osZUFBZTtZQUNqQixPQUFPO2dCQUNMVSxNQUFNO1lBQ1I7UUFDRixFQUFFLE9BQU9HLE9BQU87WUFDZEMsUUFBUUQsS0FBSyxDQUFDLHlCQUF5QkE7WUFDdkNILE1BQU07UUFDUixTQUFVO1lBQ1JSLGFBQWE7UUFDZjtJQUNGO0lBRUEscUJBQ0UsOERBQUNhO1FBQUlDLFdBQVU7OzBCQUViLDhEQUFDQztnQkFBT0QsV0FBVTswQkFDaEIsNEVBQUNEO29CQUFJQyxXQUFVOztzQ0FDYiw4REFBQ0Q7NEJBQUlDLFdBQVU7OzhDQUNiLDhEQUFDeEMseURBQU1BO29DQUFDMEMsU0FBUTtvQ0FBUUMsU0FBUyxJQUFNM0IsU0FBUzs7c0RBQzlDLDhEQUFDUCxzR0FBU0E7NENBQUMrQixXQUFVOzs7Ozs7d0NBQWlCOzs7Ozs7OzhDQUd4Qyw4REFBQ0Q7b0NBQUlDLFdBQVU7OENBQWdHOzs7Ozs7Ozs7Ozs7c0NBRWpILDhEQUFDNUIsa0VBQWNBOzs7Ozs7Ozs7Ozs7Ozs7OzBCQUluQiw4REFBQ2dDO2dCQUFLSixXQUFVOztrQ0FDZCw4REFBQ0Q7d0JBQUlDLFdBQVU7OzBDQUNiLDhEQUFDRDtnQ0FBSUMsV0FBVTswQ0FDYiw0RUFBQzdCLHNHQUFLQTtvQ0FBQzZCLFdBQVU7Ozs7Ozs7Ozs7OzBDQUVuQiw4REFBQ0s7Z0NBQUdMLFdBQVU7MENBQXdDOzs7Ozs7MENBR3RELDhEQUFDTTtnQ0FBRU4sV0FBVTswQ0FBd0I7Ozs7Ozs7Ozs7OztvQkFLdEMsQ0FBQ2pCLDRCQUNBLDhEQUFDdEIscURBQUlBO3dCQUFDdUMsV0FBVTs7MENBQ2QsOERBQUNyQywyREFBVUE7MENBQ1QsNEVBQUNDLDBEQUFTQTtvQ0FBQ29DLFdBQVU7OENBQXVCOzs7Ozs7Ozs7OzswQ0FFOUMsOERBQUN0Qyw0REFBV0E7Z0NBQUNzQyxXQUFVOztrREFDckIsOERBQUNPO3dDQUFLQyxVQUFVakI7d0NBQWtCUyxXQUFVOzswREFDMUMsOERBQUNEO2dEQUFJQyxXQUFVOztrRUFDYiw4REFBQ2xDLHVEQUFLQTt3REFBQzJDLFNBQVE7d0RBQWVULFdBQVU7a0VBQXdCOzs7Ozs7a0VBR2hFLDhEQUFDbkMsdURBQUtBO3dEQUNKNkMsSUFBRzt3REFDSEMsTUFBSzt3REFDTEMsYUFBWTt3REFDWkMsT0FBT2hDO3dEQUNQaUMsVUFBVSxDQUFDdEIsSUFBTVYsZUFBZVUsRUFBRXVCLE1BQU0sQ0FBQ0YsS0FBSzt3REFDOUNHLFFBQVE7d0RBQ1JoQixXQUFVOzs7Ozs7Ozs7Ozs7MERBSWQsOERBQUNEO2dEQUFJQyxXQUFVOztrRUFDYiw4REFBQ2xDLHVEQUFLQTt3REFBQzJDLFNBQVE7d0RBQWdCVCxXQUFVO2tFQUF3Qjs7Ozs7O2tFQUdqRSw4REFBQ25DLHVEQUFLQTt3REFDSjZDLElBQUc7d0RBQ0hDLE1BQUs7d0RBQ0xDLGFBQVk7d0RBQ1pDLE9BQU9wQzt3REFDUHFDLFVBQVUsQ0FBQ3RCLElBQU1kLGdCQUFnQmMsRUFBRXVCLE1BQU0sQ0FBQ0YsS0FBSzt3REFDL0NHLFFBQVE7d0RBQ1JoQixXQUFVOzs7Ozs7Ozs7Ozs7MERBSWQsOERBQUNEO2dEQUFJQyxXQUFVOztrRUFDYiw4REFBQ2xDLHVEQUFLQTt3REFBQzJDLFNBQVE7d0RBQWlCVCxXQUFVO2tFQUF3Qjs7Ozs7O2tFQUdsRSw4REFBQ25DLHVEQUFLQTt3REFDSjZDLElBQUc7d0RBQ0hDLE1BQUs7d0RBQ0xDLGFBQVk7d0RBQ1pDLE9BQU9sQzt3REFDUG1DLFVBQVUsQ0FBQ3RCLElBQU1aLGlCQUFpQlksRUFBRXVCLE1BQU0sQ0FBQ0YsS0FBSzt3REFDaERHLFFBQVE7d0RBQ1JoQixXQUFVOzs7Ozs7Ozs7Ozs7MERBSWQsOERBQUN4Qyx5REFBTUE7Z0RBQ0xtRCxNQUFLO2dEQUNMTSxNQUFLO2dEQUNMakIsV0FBVTtnREFDVmtCLFVBQVUsQ0FBQ3ZDLGlCQUFpQixDQUFDRixnQkFBZ0IsQ0FBQ0ksZUFBZUk7O2tFQUU3RCw4REFBQ2Ysc0dBQUlBO3dEQUFDOEIsV0FBVTs7Ozs7O29EQUNmZixZQUFZLGVBQWU7Ozs7Ozs7Ozs7Ozs7a0RBS2hDLDhEQUFDeEIscURBQUlBO3dDQUFDdUMsV0FBVTtrREFDZCw0RUFBQ3RDLDREQUFXQTs0Q0FBQ3NDLFdBQVU7OzhEQUNyQiw4REFBQ21CO29EQUFHbkIsV0FBVTs4REFBcUM7Ozs7Ozs4REFDbkQsOERBQUNvQjtvREFBR3BCLFdBQVU7O3NFQUNaLDhEQUFDcUI7NERBQUdyQixXQUFVOzs4RUFDWiw4REFBQ3NCO29FQUFLdEIsV0FBVTs4RUFBYzs7Ozs7O2dFQUFTOzs7Ozs7O3NFQUd6Qyw4REFBQ3FCOzREQUFHckIsV0FBVTs7OEVBQ1osOERBQUNzQjtvRUFBS3RCLFdBQVU7OEVBQWM7Ozs7OztnRUFBUzs7Ozs7OztzRUFHekMsOERBQUNxQjs0REFBR3JCLFdBQVU7OzhFQUNaLDhEQUFDc0I7b0VBQUt0QixXQUFVOzhFQUFjOzs7Ozs7Z0VBQVM7Ozs7Ozs7c0VBR3pDLDhEQUFDcUI7NERBQUdyQixXQUFVOzs4RUFDWiw4REFBQ3NCO29FQUFLdEIsV0FBVTs4RUFBYzs7Ozs7O2dFQUFTOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7a0RBUS9DLDhEQUFDRDt3Q0FBSUMsV0FBVTs7MERBQ2IsOERBQUNNO2dEQUFFTixXQUFVOzBEQUE2Qjs7Ozs7OzBEQUMxQyw4REFBQ3hDLHlEQUFNQTtnREFDTDBDLFNBQVE7Z0RBQ1JDLFNBQVMsSUFBTTNCLFNBQVM7Z0RBQ3hCd0IsV0FBVTswREFDWDs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7a0RBT1AsOERBQUN2QyxxREFBSUE7d0JBQUN1QyxXQUFVO2tDQUNkLDRFQUFDdEMsNERBQVdBOzRCQUFDc0MsV0FBVTs7OENBQ3JCLDhEQUFDRDtvQ0FBSUMsV0FBVTs4Q0FDYiw0RUFBQ2hDLHNHQUFJQTt3Q0FBQ2dDLFdBQVU7Ozs7Ozs7Ozs7OzhDQUdsQiw4REFBQ3VCO29DQUFHdkIsV0FBVTs7d0NBQXdDO3dDQUMxQnJCO3dDQUFjOzs7Ozs7OzhDQUcxQyw4REFBQzJCO29DQUFFTixXQUFVOzt3Q0FDVnZCO3dDQUFhO3dDQUFPSTt3Q0FBWTs7Ozs7Ozs4Q0FHbkMsOERBQUNrQjtvQ0FBSUMsV0FBVTs4Q0FDYiw0RUFBQ007d0NBQUVOLFdBQVU7OzBEQUNYLDhEQUFDd0I7MERBQU87Ozs7OzswREFDUiw4REFBQ0M7Ozs7OzRDQUFLOzBEQUVOLDhEQUFDQTs7Ozs7NENBQUs7MERBRU4sOERBQUNBOzs7Ozs0Q0FBSzs7Ozs7Ozs7Ozs7OzhDQUtWLDhEQUFDMUI7b0NBQUlDLFdBQVU7O3NEQUNiLDhEQUFDeEMseURBQU1BOzRDQUNMMkMsU0FBUyxJQUFNM0IsU0FBUzs0Q0FDeEJ3QixXQUFVO3NEQUNYOzs7Ozs7c0RBSUQsOERBQUN4Qyx5REFBTUE7NENBQ0wwQyxTQUFROzRDQUNSQyxTQUFTO2dEQUNQbkIsZUFBZTtnREFDZk4sZ0JBQWdCO2dEQUNoQkUsaUJBQWlCO2dEQUNqQkUsZUFBZTs0Q0FDakI7NENBQ0FrQixXQUFVO3NEQUNYOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztrQ0FTVCw4REFBQ0Q7d0JBQUlDLFdBQVU7a0NBQ2IsNEVBQUN2QyxxREFBSUE7NEJBQUN1QyxXQUFVO3NDQUNkLDRFQUFDdEMsNERBQVdBO2dDQUFDc0MsV0FBVTswQ0FDckIsNEVBQUNNO29DQUFFTixXQUFVOzhDQUF3Qjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBU25EO0dBNU9NekI7O1FBQ2FSLDhEQUFXQTtRQU9iTSx3REFBY0E7OztLQVJ6QkU7QUE4T04sK0RBQWVBLGNBQWNBLEVBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vc3JjL2FwcC9jZW5zdXMvcHVibGljL0VtcGxveWVySW52aXRlLnRzeD9jMGJjIl0sInNvdXJjZXNDb250ZW50IjpbIlxyXG5pbXBvcnQgeyB1c2VTdGF0ZSB9IGZyb20gXCJyZWFjdFwiO1xyXG5pbXBvcnQgeyBCdXR0b24gfSBmcm9tIFwiLi4vY29tcG9uZW50cy91aS9idXR0b25cIjtcclxuaW1wb3J0IHsgQ2FyZCwgQ2FyZENvbnRlbnQsIENhcmRIZWFkZXIsIENhcmRUaXRsZSB9IGZyb20gXCIuLi9jb21wb25lbnRzL3VpL2NhcmRcIjtcclxuaW1wb3J0IHsgSW5wdXQgfSBmcm9tIFwiLi4vY29tcG9uZW50cy91aS9pbnB1dFwiO1xyXG5pbXBvcnQgeyBMYWJlbCB9IGZyb20gXCIuLi9jb21wb25lbnRzL3VpL2xhYmVsXCI7XHJcbmltcG9ydCB7IHVzZU5hdmlnYXRlIH0gZnJvbSBcIi4uL2xpYi9yZWFjdC1yb3V0ZXItZG9tXCI7XHJcbmltcG9ydCB7IE1haWwsIEFycm93TGVmdCwgU2VuZCwgVXNlcnMgfSBmcm9tIFwibHVjaWRlLXJlYWN0XCI7XHJcbmltcG9ydCBQcm9maWxlSGFuZGxlciBmcm9tIFwiLi4vY29tcG9uZW50cy9Qcm9maWxlSGFuZGxlclwiO1xyXG5pbXBvcnQgeyB1c2VBcHBTZWxlY3RvciB9IGZyb20gXCJAL3JlZHV4L2hvb2tzXCI7XHJcbmltcG9ydCB7IGJyb2tlckFkZHNDb21wYW55IH0gZnJvbSBcIkAvbWlkZGxld2FyZS9jb21wYW55X21pZGRsZXdhcmVcIjtcclxuXHJcbmNvbnN0IEVtcGxveWVySW52aXRlID0gKCkgPT4ge1xyXG4gIGNvbnN0IG5hdmlnYXRlID0gdXNlTmF2aWdhdGUoKTtcclxuICBjb25zdCBbZW1wbG95ZXJOYW1lLCBzZXRFbXBsb3llck5hbWVdID0gdXNlU3RhdGUoXCJcIik7XHJcbiAgY29uc3QgW2VtcGxveWVyRW1haWwsIHNldEVtcGxveWVyRW1haWxdID0gdXNlU3RhdGUoXCJcIik7XHJcbiAgY29uc3QgW2NvbXBhbnlOYW1lLCBzZXRDb21wYW55TmFtZV0gPSB1c2VTdGF0ZShcIlwiKTtcclxuICBjb25zdCBbaXNTdWJtaXR0ZWQsIHNldElzU3VibWl0dGVkXSA9IHVzZVN0YXRlKGZhbHNlKTtcclxuICBjb25zdCBbaXNMb2FkaW5nLCBzZXRJc0xvYWRpbmddID0gdXNlU3RhdGUoZmFsc2UpO1xyXG5cclxuICBjb25zdCB1c2VySWQgPSB1c2VBcHBTZWxlY3Rvcigoc3RhdGUpID0+IHN0YXRlLnVzZXIuX2lkKTtcclxuXHJcbiAgY29uc3QgaGFuZGxlU2VuZEludml0ZSA9IGFzeW5jIChlOiBSZWFjdC5Gb3JtRXZlbnQpID0+IHtcclxuICAgIGUucHJldmVudERlZmF1bHQoKTtcclxuXHJcbiAgICBpZiAoIWVtcGxveWVyTmFtZSB8fCAhZW1wbG95ZXJFbWFpbCB8fCAhY29tcGFueU5hbWUpIHtcclxuICAgICAgYWxlcnQoXCJQbGVhc2UgZmlsbCBvdXQgYWxsIHJlcXVpcmVkIGZpZWxkcy5cIik7XHJcbiAgICAgIHJldHVybjtcclxuICAgIH1cclxuXHJcbiAgICBzZXRJc0xvYWRpbmcodHJ1ZSk7XHJcblxyXG4gICAgdHJ5IHtcclxuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBicm9rZXJBZGRzQ29tcGFueShcclxuICAgICAgICB1c2VySWQsXHJcbiAgICAgICAgY29tcGFueU5hbWUsXHJcbiAgICAgICAgZW1wbG95ZXJFbWFpbCxcclxuICAgICAgICBlbXBsb3llck5hbWVcclxuICAgICAgKTtcclxuXHJcbiAgICAgIGlmIChyZXNwb25zZSAmJiByZXNwb25zZS5zdGF0dXMgPT09IDIwMCkge1xyXG4gICAgICAgIHNldElzU3VibWl0dGVkKHRydWUpO1xyXG4gICAgICB9IGVsc2Uge1xyXG4gICAgICAgIGFsZXJ0KFwiRmFpbGVkIHRvIHNlbmQgaW52aXRlLiBQbGVhc2UgdHJ5IGFnYWluLlwiKTtcclxuICAgICAgfVxyXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgY29uc29sZS5lcnJvcihcIkVycm9yIHNlbmRpbmcgaW52aXRlOlwiLCBlcnJvcik7XHJcbiAgICAgIGFsZXJ0KFwiRmFpbGVkIHRvIHNlbmQgaW52aXRlLiBQbGVhc2UgdHJ5IGFnYWluLlwiKTtcclxuICAgIH0gZmluYWxseSB7XHJcbiAgICAgIHNldElzTG9hZGluZyhmYWxzZSk7XHJcbiAgICB9XHJcbiAgfTtcclxuXHJcbiAgcmV0dXJuIChcclxuICAgIDxkaXYgY2xhc3NOYW1lPVwibWluLWgtc2NyZWVuIGJnLWdyYWRpZW50LXRvLWJyIGZyb20tYmx1ZS01MCB2aWEtd2hpdGUgdG8tcHVycGxlLTUwXCI+XHJcbiAgICAgIHsvKiBIZWFkZXIgKi99XHJcbiAgICAgIDxoZWFkZXIgY2xhc3NOYW1lPVwiYm9yZGVyLWIgYmctd2hpdGUvODAgYmFja2Ryb3AtYmx1ci1zbVwiPlxyXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY29udGFpbmVyIG14LWF1dG8gcHgtNCBweS00IGZsZXgganVzdGlmeS1iZXR3ZWVuIGl0ZW1zLWNlbnRlclwiPlxyXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTRcIj5cclxuICAgICAgICAgICAgPEJ1dHRvbiB2YXJpYW50PVwiZ2hvc3RcIiBvbkNsaWNrPXsoKSA9PiBuYXZpZ2F0ZSgnLycpfT5cclxuICAgICAgICAgICAgICA8QXJyb3dMZWZ0IGNsYXNzTmFtZT1cImgtNCB3LTQgbXItMlwiIC8+XHJcbiAgICAgICAgICAgICAgQmFjayB0byBIb21lXHJcbiAgICAgICAgICAgIDwvQnV0dG9uPlxyXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtYm9sZCBiZy1ncmFkaWVudC10by1yIGZyb20tYmx1ZS02MDAgdG8tcHVycGxlLTYwMCBiZy1jbGlwLXRleHQgdGV4dC10cmFuc3BhcmVudFwiPkJlbk9zcGhlcmU8L2Rpdj5cclxuICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgPFByb2ZpbGVIYW5kbGVyIC8+XHJcbiAgICAgICAgPC9kaXY+XHJcbiAgICAgIDwvaGVhZGVyPlxyXG5cclxuICAgICAgPG1haW4gY2xhc3NOYW1lPVwiY29udGFpbmVyIG14LWF1dG8gcHgtNCBweS0xNiBtYXgtdy0yeGxcIj5cclxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyIG1iLTEyXCI+XHJcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctMjAgaC0yMCBteC1hdXRvIG1iLTYgYmctcHVycGxlLTEwMCByb3VuZGVkLWZ1bGwgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXJcIj5cclxuICAgICAgICAgICAgPFVzZXJzIGNsYXNzTmFtZT1cImgtMTAgdy0xMCB0ZXh0LXB1cnBsZS02MDBcIiAvPlxyXG4gICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICA8aDEgY2xhc3NOYW1lPVwidGV4dC00eGwgZm9udC1ib2xkIHRleHQtZ3JheS05MDAgbWItNFwiPlxyXG4gICAgICAgICAgICDwn5OpIERvbiZhcG9zO3QgaGF2ZSB0aGUgY2Vuc3VzPyBMZXQgdGhlIGVtcGxveWVyIHVwbG9hZCBpdC5cclxuICAgICAgICAgIDwvaDE+XHJcbiAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXhsIHRleHQtZ3JheS02MDBcIj5cclxuICAgICAgICAgICAgU2VuZCBhIHNlY3VyZSBsaW5rIHRvIHlvdXIgY2xpZW50IOKAlCB0aGV5JmFwb3M7bGwgYmUgZ3VpZGVkIHRvIHVwbG9hZCB0aGVpciBjZW5zdXMuIFlvdSZhcG9zO2xsIGJlIG5vdGlmaWVkIG9uY2UgdGhlaXIgZW5yaWNoZWQgZ3JvdXAgcmVwb3J0IGlzIHJlYWR5LlxyXG4gICAgICAgICAgPC9wPlxyXG4gICAgICAgIDwvZGl2PlxyXG5cclxuICAgICAgICB7IWlzU3VibWl0dGVkID8gKFxyXG4gICAgICAgICAgPENhcmQgY2xhc3NOYW1lPVwic2hhZG93LTJ4bCBib3JkZXItMFwiPlxyXG4gICAgICAgICAgICA8Q2FyZEhlYWRlcj5cclxuICAgICAgICAgICAgICA8Q2FyZFRpdGxlIGNsYXNzTmFtZT1cInRleHQtY2VudGVyIHRleHQtMnhsXCI+U2VuZCBFbXBsb3llciBJbnZpdGU8L0NhcmRUaXRsZT5cclxuICAgICAgICAgICAgPC9DYXJkSGVhZGVyPlxyXG4gICAgICAgICAgICA8Q2FyZENvbnRlbnQgY2xhc3NOYW1lPVwic3BhY2UteS02XCI+XHJcbiAgICAgICAgICAgICAgPGZvcm0gb25TdWJtaXQ9e2hhbmRsZVNlbmRJbnZpdGV9IGNsYXNzTmFtZT1cInNwYWNlLXktNlwiPlxyXG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTJcIj5cclxuICAgICAgICAgICAgICAgICAgPExhYmVsIGh0bWxGb3I9XCJjb21wYW55LW5hbWVcIiBjbGFzc05hbWU9XCJ0ZXh0LWJhc2UgZm9udC1tZWRpdW1cIj5cclxuICAgICAgICAgICAgICAgICAgICBDb21wYW55IE5hbWUgKlxyXG4gICAgICAgICAgICAgICAgICA8L0xhYmVsPlxyXG4gICAgICAgICAgICAgICAgICA8SW5wdXRcclxuICAgICAgICAgICAgICAgICAgICBpZD1cImNvbXBhbnktbmFtZVwiXHJcbiAgICAgICAgICAgICAgICAgICAgdHlwZT1cInRleHRcIlxyXG4gICAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiZS5nLiBBQkMgQ29ycCwgU21pdGggJiBBc3NvY2lhdGVzXCJcclxuICAgICAgICAgICAgICAgICAgICB2YWx1ZT17Y29tcGFueU5hbWV9XHJcbiAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXRDb21wYW55TmFtZShlLnRhcmdldC52YWx1ZSl9XHJcbiAgICAgICAgICAgICAgICAgICAgcmVxdWlyZWRcclxuICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJoLTEyIHRleHQtYmFzZVwiXHJcbiAgICAgICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAgICA8L2Rpdj5cclxuXHJcbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktMlwiPlxyXG4gICAgICAgICAgICAgICAgICA8TGFiZWwgaHRtbEZvcj1cImVtcGxveWVyLW5hbWVcIiBjbGFzc05hbWU9XCJ0ZXh0LWJhc2UgZm9udC1tZWRpdW1cIj5cclxuICAgICAgICAgICAgICAgICAgICBFbXBsb3llciBOYW1lICpcclxuICAgICAgICAgICAgICAgICAgPC9MYWJlbD5cclxuICAgICAgICAgICAgICAgICAgPElucHV0XHJcbiAgICAgICAgICAgICAgICAgICAgaWQ9XCJlbXBsb3llci1uYW1lXCJcclxuICAgICAgICAgICAgICAgICAgICB0eXBlPVwidGV4dFwiXHJcbiAgICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJKb2huIFNtaXRoXCJcclxuICAgICAgICAgICAgICAgICAgICB2YWx1ZT17ZW1wbG95ZXJOYW1lfVxyXG4gICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0RW1wbG95ZXJOYW1lKGUudGFyZ2V0LnZhbHVlKX1cclxuICAgICAgICAgICAgICAgICAgICByZXF1aXJlZFxyXG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImgtMTIgdGV4dC1iYXNlXCJcclxuICAgICAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICAgIDwvZGl2PlxyXG5cclxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0yXCI+XHJcbiAgICAgICAgICAgICAgICAgIDxMYWJlbCBodG1sRm9yPVwiZW1wbG95ZXItZW1haWxcIiBjbGFzc05hbWU9XCJ0ZXh0LWJhc2UgZm9udC1tZWRpdW1cIj5cclxuICAgICAgICAgICAgICAgICAgICDwn5OnIEVtcGxveWVyIENvbnRhY3QgRW1haWwgKlxyXG4gICAgICAgICAgICAgICAgICA8L0xhYmVsPlxyXG4gICAgICAgICAgICAgICAgICA8SW5wdXRcclxuICAgICAgICAgICAgICAgICAgICBpZD1cImVtcGxveWVyLWVtYWlsXCJcclxuICAgICAgICAgICAgICAgICAgICB0eXBlPVwiZW1haWxcIlxyXG4gICAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiaHJAY29tcGFueS5jb20gb3IgY29udGFjdEBlbXBsb3llci5jb21cIlxyXG4gICAgICAgICAgICAgICAgICAgIHZhbHVlPXtlbXBsb3llckVtYWlsfVxyXG4gICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0RW1wbG95ZXJFbWFpbChlLnRhcmdldC52YWx1ZSl9XHJcbiAgICAgICAgICAgICAgICAgICAgcmVxdWlyZWRcclxuICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJoLTEyIHRleHQtYmFzZVwiXHJcbiAgICAgICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAgICA8L2Rpdj5cclxuXHJcbiAgICAgICAgICAgICAgICA8QnV0dG9uXHJcbiAgICAgICAgICAgICAgICAgIHR5cGU9XCJzdWJtaXRcIlxyXG4gICAgICAgICAgICAgICAgICBzaXplPVwibGdcIlxyXG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGwgYmctcHVycGxlLTYwMCBob3ZlcjpiZy1wdXJwbGUtNzAwIHRleHQtd2hpdGUgcHktNCB0ZXh0LWxnXCJcclxuICAgICAgICAgICAgICAgICAgZGlzYWJsZWQ9eyFlbXBsb3llckVtYWlsIHx8ICFlbXBsb3llck5hbWUgfHwgIWNvbXBhbnlOYW1lIHx8IGlzTG9hZGluZ31cclxuICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgPFNlbmQgY2xhc3NOYW1lPVwibXItMiBoLTUgdy01XCIgLz5cclxuICAgICAgICAgICAgICAgICAge2lzTG9hZGluZyA/IFwiU2VuZGluZy4uLlwiIDogXCLinqHvuI8gU2VuZCBVcGxvYWQgTGluayB0byBFbXBsb3llclwifVxyXG4gICAgICAgICAgICAgICAgPC9CdXR0b24+XHJcbiAgICAgICAgICAgICAgPC9mb3JtPlxyXG5cclxuICAgICAgICAgICAgICB7LyogV2hhdCBoYXBwZW5zIG5leHQgKi99XHJcbiAgICAgICAgICAgICAgPENhcmQgY2xhc3NOYW1lPVwiYmctcHVycGxlLTUwIGJvcmRlci1wdXJwbGUtMjAwXCI+XHJcbiAgICAgICAgICAgICAgICA8Q2FyZENvbnRlbnQgY2xhc3NOYW1lPVwicC02XCI+XHJcbiAgICAgICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJmb250LXNlbWlib2xkIHRleHQtcHVycGxlLTkwMCBtYi0zXCI+V2hhdCBoYXBwZW5zIG5leHQ6PC9oMz5cclxuICAgICAgICAgICAgICAgICAgPHVsIGNsYXNzTmFtZT1cInRleHQtcHVycGxlLTgwMCBzcGFjZS15LTIgdGV4dC1zbVwiPlxyXG4gICAgICAgICAgICAgICAgICAgIDxsaSBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLXN0YXJ0XCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJtci0yIG10LTAuNVwiPjEuPC9zcGFuPlxyXG4gICAgICAgICAgICAgICAgICAgICAgRW1wbG95ZXIgcmVjZWl2ZXMgYSBzZWN1cmUsIHBlcnNvbmFsaXplZCB1cGxvYWQgbGlua1xyXG4gICAgICAgICAgICAgICAgICAgIDwvbGk+XHJcbiAgICAgICAgICAgICAgICAgICAgPGxpIGNsYXNzTmFtZT1cImZsZXggaXRlbXMtc3RhcnRcIj5cclxuICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cIm1yLTIgbXQtMC41XCI+Mi48L3NwYW4+XHJcbiAgICAgICAgICAgICAgICAgICAgICBUaGV5IHVwbG9hZCB0aGVpciBjZW5zdXMgZmlsZSB0aHJvdWdoIG91ciBndWlkZWQgcHJvY2Vzc1xyXG4gICAgICAgICAgICAgICAgICAgIDwvbGk+XHJcbiAgICAgICAgICAgICAgICAgICAgPGxpIGNsYXNzTmFtZT1cImZsZXggaXRlbXMtc3RhcnRcIj5cclxuICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cIm1yLTIgbXQtMC41XCI+My48L3NwYW4+XHJcbiAgICAgICAgICAgICAgICAgICAgICBXZSBhbmFseXplIHRoZSBkYXRhIGFuZCBnZW5lcmF0ZSB0aGUgZW5yaWNoZWQgcmVwb3J0XHJcbiAgICAgICAgICAgICAgICAgICAgPC9saT5cclxuICAgICAgICAgICAgICAgICAgICA8bGkgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1zdGFydFwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwibXItMiBtdC0wLjVcIj40Ljwvc3Bhbj5cclxuICAgICAgICAgICAgICAgICAgICAgIFlvdSBnZXQgbm90aWZpZWQgd2hlbiB0aGUgYW5hbHlzaXMgaXMgY29tcGxldGUgYW5kIHJlYWR5IHRvIHJldmlld1xyXG4gICAgICAgICAgICAgICAgICAgIDwvbGk+XHJcbiAgICAgICAgICAgICAgICAgIDwvdWw+XHJcbiAgICAgICAgICAgICAgICA8L0NhcmRDb250ZW50PlxyXG4gICAgICAgICAgICAgIDwvQ2FyZD5cclxuXHJcbiAgICAgICAgICAgICAgey8qIEFsdGVybmF0aXZlIE9wdGlvbiAqL31cclxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyIHB0LTQgYm9yZGVyLXRcIj5cclxuICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTUwMCBtYi0zXCI+UHJlZmVyIHRvIHVwbG9hZCB5b3Vyc2VsZj88L3A+XHJcbiAgICAgICAgICAgICAgICA8QnV0dG9uIFxyXG4gICAgICAgICAgICAgICAgICB2YXJpYW50PVwib3V0bGluZVwiIFxyXG4gICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBuYXZpZ2F0ZSgnL3VwbG9hZCcpfVxyXG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGxcIlxyXG4gICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICDwn5OkIFVwbG9hZCBDZW5zdXMgRmlsZSBEaXJlY3RseVxyXG4gICAgICAgICAgICAgICAgPC9CdXR0b24+XHJcbiAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgIDwvQ2FyZENvbnRlbnQ+XHJcbiAgICAgICAgICA8L0NhcmQ+XHJcbiAgICAgICAgKSA6IChcclxuICAgICAgICAgIDxDYXJkIGNsYXNzTmFtZT1cInNoYWRvdy0yeGwgYm9yZGVyLTBcIj5cclxuICAgICAgICAgICAgPENhcmRDb250ZW50IGNsYXNzTmFtZT1cInAtMTIgdGV4dC1jZW50ZXJcIj5cclxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctMjAgaC0yMCBteC1hdXRvIG1iLTYgYmctZ3JlZW4tMTAwIHJvdW5kZWQtZnVsbCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlclwiPlxyXG4gICAgICAgICAgICAgICAgPE1haWwgY2xhc3NOYW1lPVwiaC0xMCB3LTEwIHRleHQtZ3JlZW4tNjAwXCIgLz5cclxuICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICBcclxuICAgICAgICAgICAgICA8aDIgY2xhc3NOYW1lPVwidGV4dC0yeGwgZm9udC1ib2xkIHRleHQtZ3JheS05MDAgbWItNFwiPlxyXG4gICAgICAgICAgICAgICAg4pyFIENvbXBhbnkgaW52aXRlIHNlbnQgdG8ge2VtcGxveWVyRW1haWx9IVxyXG4gICAgICAgICAgICAgIDwvaDI+XHJcblxyXG4gICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtbGcgdGV4dC1ncmF5LTYwMCBtYi02XCI+XHJcbiAgICAgICAgICAgICAgICB7ZW1wbG95ZXJOYW1lfSBmcm9tIHtjb21wYW55TmFtZX0gd2lsbCByZWNlaXZlIGFuIGludml0ZSB0byBqb2luIHRoZSBwbGF0Zm9ybSBhbmQgdXBsb2FkIHRoZWlyIGNlbnN1cy5cclxuICAgICAgICAgICAgICA8L3A+XHJcbiAgICAgICAgICAgICAgXHJcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1ncmVlbi01MCBib3JkZXIgYm9yZGVyLWdyZWVuLTIwMCByb3VuZGVkLWxnIHAtNiBtYi02XCI+XHJcbiAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWdyZWVuLTgwMCB0ZXh0LXNtXCI+XHJcbiAgICAgICAgICAgICAgICAgIDxzdHJvbmc+TmV4dCBzdGVwczo8L3N0cm9uZz5cclxuICAgICAgICAgICAgICAgICAgPGJyIC8+XHJcbiAgICAgICAgICAgICAgICAgIOKAoiBUaGUgZW1wbG95ZXIgd2lsbCByZWNlaXZlIGEgc2VjdXJlIHVwbG9hZCBsaW5rXHJcbiAgICAgICAgICAgICAgICAgIDxiciAvPlxyXG4gICAgICAgICAgICAgICAgICDigKIgWW91JmFwb3M7bGwgZ2V0IGFuIGVtYWlsIG5vdGlmaWNhdGlvbiB3aGVuIGFuYWx5c2lzIGlzIGNvbXBsZXRlXHJcbiAgICAgICAgICAgICAgICAgIDxiciAvPlxyXG4gICAgICAgICAgICAgICAgICDigKIgTG9nIGluIHRvIEJlbk9zcGhlcmUgdG8gYWNjZXNzIHRoZSBmdWxsIGVucmljaGVkIHJlcG9ydFxyXG4gICAgICAgICAgICAgICAgPC9wPlxyXG4gICAgICAgICAgICAgIDwvZGl2PlxyXG5cclxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktM1wiPlxyXG4gICAgICAgICAgICAgICAgPEJ1dHRvbiBcclxuICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gbmF2aWdhdGUoJy8nKX1cclxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIGJnLWJsdWUtNjAwIGhvdmVyOmJnLWJsdWUtNzAwXCJcclxuICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgUmV0dXJuIHRvIERhc2hib2FyZFxyXG4gICAgICAgICAgICAgICAgPC9CdXR0b24+XHJcbiAgICAgICAgICAgICAgICBcclxuICAgICAgICAgICAgICAgIDxCdXR0b24gXHJcbiAgICAgICAgICAgICAgICAgIHZhcmlhbnQ9XCJvdXRsaW5lXCJcclxuICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4ge1xyXG4gICAgICAgICAgICAgICAgICAgIHNldElzU3VibWl0dGVkKGZhbHNlKTtcclxuICAgICAgICAgICAgICAgICAgICBzZXRFbXBsb3llck5hbWUoXCJcIik7XHJcbiAgICAgICAgICAgICAgICAgICAgc2V0RW1wbG95ZXJFbWFpbChcIlwiKTtcclxuICAgICAgICAgICAgICAgICAgICBzZXRDb21wYW55TmFtZShcIlwiKTtcclxuICAgICAgICAgICAgICAgICAgfX1cclxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsXCJcclxuICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgU2VuZCBBbm90aGVyIEludml0ZVxyXG4gICAgICAgICAgICAgICAgPC9CdXR0b24+XHJcbiAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgIDwvQ2FyZENvbnRlbnQ+XHJcbiAgICAgICAgICA8L0NhcmQ+XHJcbiAgICAgICAgKX1cclxuXHJcbiAgICAgICAgey8qIFNlY3VyaXR5ICYgUHJpdmFjeSBOb3RlICovfVxyXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibXQtOCB0ZXh0LWNlbnRlclwiPlxyXG4gICAgICAgICAgPENhcmQgY2xhc3NOYW1lPVwiYmctZ3JheS01MCBib3JkZXItZ3JheS0yMDBcIj5cclxuICAgICAgICAgICAgPENhcmRDb250ZW50IGNsYXNzTmFtZT1cInAtNFwiPlxyXG4gICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1ncmF5LTYwMFwiPlxyXG4gICAgICAgICAgICAgICAg8J+UkiBBbGwgdXBsb2FkcyBhcmUgc2VjdXJlIGFuZCBlbmNyeXB0ZWQuIENlbnN1cyBkYXRhIGlzIHByb2Nlc3NlZCBjb25maWRlbnRpYWxseSBhbmQgbmV2ZXIgc2hhcmVkIHdpdGggdGhpcmQgcGFydGllcy4gSElQQUEgY29tcGxpYW50LlxyXG4gICAgICAgICAgICAgIDwvcD5cclxuICAgICAgICAgICAgPC9DYXJkQ29udGVudD5cclxuICAgICAgICAgIDwvQ2FyZD5cclxuICAgICAgICA8L2Rpdj5cclxuICAgICAgPC9tYWluPlxyXG4gICAgPC9kaXY+XHJcbiAgKTtcclxufTtcclxuXHJcbmV4cG9ydCBkZWZhdWx0IEVtcGxveWVySW52aXRlO1xyXG4iXSwibmFtZXMiOlsidXNlU3RhdGUiLCJCdXR0b24iLCJDYXJkIiwiQ2FyZENvbnRlbnQiLCJDYXJkSGVhZGVyIiwiQ2FyZFRpdGxlIiwiSW5wdXQiLCJMYWJlbCIsInVzZU5hdmlnYXRlIiwiTWFpbCIsIkFycm93TGVmdCIsIlNlbmQiLCJVc2VycyIsIlByb2ZpbGVIYW5kbGVyIiwidXNlQXBwU2VsZWN0b3IiLCJicm9rZXJBZGRzQ29tcGFueSIsIkVtcGxveWVySW52aXRlIiwibmF2aWdhdGUiLCJlbXBsb3llck5hbWUiLCJzZXRFbXBsb3llck5hbWUiLCJlbXBsb3llckVtYWlsIiwic2V0RW1wbG95ZXJFbWFpbCIsImNvbXBhbnlOYW1lIiwic2V0Q29tcGFueU5hbWUiLCJpc1N1Ym1pdHRlZCIsInNldElzU3VibWl0dGVkIiwiaXNMb2FkaW5nIiwic2V0SXNMb2FkaW5nIiwidXNlcklkIiwic3RhdGUiLCJ1c2VyIiwiX2lkIiwiaGFuZGxlU2VuZEludml0ZSIsImUiLCJwcmV2ZW50RGVmYXVsdCIsImFsZXJ0IiwicmVzcG9uc2UiLCJzdGF0dXMiLCJlcnJvciIsImNvbnNvbGUiLCJkaXYiLCJjbGFzc05hbWUiLCJoZWFkZXIiLCJ2YXJpYW50Iiwib25DbGljayIsIm1haW4iLCJoMSIsInAiLCJmb3JtIiwib25TdWJtaXQiLCJodG1sRm9yIiwiaWQiLCJ0eXBlIiwicGxhY2Vob2xkZXIiLCJ2YWx1ZSIsIm9uQ2hhbmdlIiwidGFyZ2V0IiwicmVxdWlyZWQiLCJzaXplIiwiZGlzYWJsZWQiLCJoMyIsInVsIiwibGkiLCJzcGFuIiwiaDIiLCJzdHJvbmciLCJiciJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/census/public/EmployerInvite.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/census/public/HRUpload.tsx":
/*!********************************************!*\
  !*** ./src/app/census/public/HRUpload.tsx ***!
  \********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../components/ui/button */ \"(app-pages-browser)/./src/app/census/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../components/ui/card */ \"(app-pages-browser)/./src/app/census/components/ui/card.tsx\");\n/* harmony import */ var _lib_react_router_dom__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../lib/react-router-dom */ \"(app-pages-browser)/./src/app/census/lib/react-router-dom.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Building_FileText_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Building,FileText,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Building_FileText_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Building,FileText,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/building.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Building_FileText_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Building,FileText,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Building_FileText_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Building,FileText,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _components_ProfileHandler__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../components/ProfileHandler */ \"(app-pages-browser)/./src/app/census/components/ProfileHandler.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst HRUpload = ()=>{\n    _s();\n    const navigate = (0,_lib_react_router_dom__WEBPACK_IMPORTED_MODULE_4__.useNavigate)();\n    const [dragActive, setDragActive] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedFile, setSelectedFile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const handleDrag = (e)=>{\n        e.preventDefault();\n        e.stopPropagation();\n        if (e.type === \"dragenter\" || e.type === \"dragover\") {\n            setDragActive(true);\n        } else if (e.type === \"dragleave\") {\n            setDragActive(false);\n        }\n    };\n    const handleDrop = (e)=>{\n        e.preventDefault();\n        e.stopPropagation();\n        setDragActive(false);\n        if (e.dataTransfer.files && e.dataTransfer.files[0]) {\n            setSelectedFile(e.dataTransfer.files[0]);\n        }\n    };\n    const handleFileSelect = (e)=>{\n        if (e.target.files && e.target.files[0]) {\n            setSelectedFile(e.target.files[0]);\n        }\n    };\n    const handleSubmit = (e)=>{\n        e.preventDefault();\n        if (selectedFile) {\n            navigate(\"/hr-processing\");\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"border-b bg-white/80 backdrop-blur-sm\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto px-4 py-4 flex justify-between items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    variant: \"ghost\",\n                                    onClick: ()=>navigate(\"/\"),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Building_FileText_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\HRUpload.tsx\",\n                                            lineNumber: 57,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        \"Back\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\HRUpload.tsx\",\n                                    lineNumber: 56,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent\",\n                                    children: \"BenOsphere HR\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\HRUpload.tsx\",\n                                    lineNumber: 60,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\HRUpload.tsx\",\n                            lineNumber: 55,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ProfileHandler__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\HRUpload.tsx\",\n                            lineNumber: 62,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\HRUpload.tsx\",\n                    lineNumber: 54,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\HRUpload.tsx\",\n                lineNumber: 53,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"container mx-auto px-4 py-16 max-w-4xl\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center mb-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-20 h-20 mx-auto mb-6 bg-blue-100 rounded-full flex items-center justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Building_FileText_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    className: \"h-10 w-10 text-blue-600\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\HRUpload.tsx\",\n                                    lineNumber: 69,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\HRUpload.tsx\",\n                                lineNumber: 68,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-4xl font-bold text-gray-900 mb-4\",\n                                children: \"\\uD83C\\uDFE2 HR Portal - Upload Employee Census\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\HRUpload.tsx\",\n                                lineNumber: 71,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xl text-gray-600\",\n                                children: \"Get instant insights into your employee benefits and potential cost savings\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\HRUpload.tsx\",\n                                lineNumber: 74,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\HRUpload.tsx\",\n                        lineNumber: 67,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                        onSubmit: handleSubmit,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            className: \"shadow-xl border-0\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                        className: \"text-center text-2xl text-blue-700\",\n                                        children: \"Upload Employee Census\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\HRUpload.tsx\",\n                                        lineNumber: 82,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\HRUpload.tsx\",\n                                    lineNumber: 81,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    className: \"space-y-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"border-2 border-dashed rounded-xl p-12 text-center transition-all \".concat(dragActive ? \"border-blue-500 bg-blue-50\" : \"border-gray-300 hover:border-blue-400 hover:bg-gray-50\"),\n                                            onDragEnter: handleDrag,\n                                            onDragLeave: handleDrag,\n                                            onDragOver: handleDrag,\n                                            onDrop: handleDrop,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mx-auto w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Building_FileText_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                            className: \"h-8 w-8 text-blue-600\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\HRUpload.tsx\",\n                                                            lineNumber: 101,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\HRUpload.tsx\",\n                                                        lineNumber: 100,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    selectedFile ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-lg font-semibold text-blue-600\",\n                                                                children: [\n                                                                    \"✅ \",\n                                                                    selectedFile.name\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\HRUpload.tsx\",\n                                                                lineNumber: 106,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-500\",\n                                                                children: [\n                                                                    (selectedFile.size / 1024 / 1024).toFixed(2),\n                                                                    \" MB\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\HRUpload.tsx\",\n                                                                lineNumber: 109,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\HRUpload.tsx\",\n                                                        lineNumber: 105,\n                                                        columnNumber: 21\n                                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xl font-semibold text-gray-700 mb-2\",\n                                                                children: \"\\uD83D\\uDCC4 Drag & drop employee census file\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\HRUpload.tsx\",\n                                                                lineNumber: 115,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-500 mb-4\",\n                                                                children: \"CSV, Excel, or PDF format\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\HRUpload.tsx\",\n                                                                lineNumber: 118,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"file\",\n                                                                id: \"file-upload\",\n                                                                className: \"hidden\",\n                                                                accept: \".csv,.xlsx,.xls,.pdf\",\n                                                                onChange: handleFileSelect\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\HRUpload.tsx\",\n                                                                lineNumber: 119,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                htmlFor: \"file-upload\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                    type: \"button\",\n                                                                    variant: \"outline\",\n                                                                    className: \"cursor-pointer bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white border-0\",\n                                                                    asChild: true,\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"\\uD83D\\uDD0D Browse Files\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\HRUpload.tsx\",\n                                                                        lineNumber: 133,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\HRUpload.tsx\",\n                                                                    lineNumber: 127,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\HRUpload.tsx\",\n                                                                lineNumber: 126,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\HRUpload.tsx\",\n                                                        lineNumber: 114,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\HRUpload.tsx\",\n                                                lineNumber: 99,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\HRUpload.tsx\",\n                                            lineNumber: 88,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                type: \"submit\",\n                                                size: \"lg\",\n                                                className: \"bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-12 py-4 text-lg\",\n                                                disabled: !selectedFile,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Building_FileText_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        className: \"mr-2 h-5 w-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\HRUpload.tsx\",\n                                                        lineNumber: 151,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    \"➡️ Analyze Our Census\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\HRUpload.tsx\",\n                                                lineNumber: 145,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\HRUpload.tsx\",\n                                            lineNumber: 144,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        !selectedFile && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-center text-sm text-gray-400\",\n                                            children: \"Please select a file to upload\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\HRUpload.tsx\",\n                                            lineNumber: 157,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\HRUpload.tsx\",\n                                    lineNumber: 86,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\HRUpload.tsx\",\n                            lineNumber: 80,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\HRUpload.tsx\",\n                        lineNumber: 79,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid md:grid-cols-2 gap-6 mt-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                className: \"bg-blue-50 border-blue-200\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    className: \"p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-semibold text-blue-900 mb-2\",\n                                            children: \"\\uD83D\\uDCA1 What you'll get:\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\HRUpload.tsx\",\n                                            lineNumber: 169,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"text-blue-800 space-y-1 text-sm\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"• Detailed benefits analysis for your employees\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\HRUpload.tsx\",\n                                                    lineNumber: 171,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"• Cost optimization recommendations\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\HRUpload.tsx\",\n                                                    lineNumber: 172,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"• Benchmark against similar companies\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\HRUpload.tsx\",\n                                                    lineNumber: 173,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"• Risk assessment and planning insights\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\HRUpload.tsx\",\n                                                    lineNumber: 174,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\HRUpload.tsx\",\n                                            lineNumber: 170,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\HRUpload.tsx\",\n                                    lineNumber: 168,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\HRUpload.tsx\",\n                                lineNumber: 167,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                className: \"bg-purple-50 border-purple-200\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    className: \"p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-semibold text-purple-900 mb-2\",\n                                            children: \"\\uD83D\\uDD12 Your data is secure:\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\HRUpload.tsx\",\n                                            lineNumber: 181,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"text-purple-800 space-y-1 text-sm\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"• All data is encrypted and HIPAA compliant\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\HRUpload.tsx\",\n                                                    lineNumber: 183,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"• Files are automatically deleted after analysis\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\HRUpload.tsx\",\n                                                    lineNumber: 184,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"• Only aggregated insights are stored\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\HRUpload.tsx\",\n                                                    lineNumber: 185,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"• Full privacy and confidentiality guaranteed\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\HRUpload.tsx\",\n                                                    lineNumber: 186,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\HRUpload.tsx\",\n                                            lineNumber: 182,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\HRUpload.tsx\",\n                                    lineNumber: 180,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\HRUpload.tsx\",\n                                lineNumber: 179,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\HRUpload.tsx\",\n                        lineNumber: 166,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\HRUpload.tsx\",\n                lineNumber: 66,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\HRUpload.tsx\",\n        lineNumber: 51,\n        columnNumber: 5\n    }, undefined);\n};\n_s(HRUpload, \"4zUT5iN9ollxO8OzZVAsRQ02PTk=\", false, function() {\n    return [\n        _lib_react_router_dom__WEBPACK_IMPORTED_MODULE_4__.useNavigate\n    ];\n});\n_c = HRUpload;\n/* harmony default export */ __webpack_exports__[\"default\"] = (HRUpload);\nvar _c;\n$RefreshReg$(_c, \"HRUpload\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/census/public/HRUpload.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/redux/hooks.ts":
/*!****************************!*\
  !*** ./src/redux/hooks.ts ***!
  \****************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useAppDispatch: function() { return /* binding */ useAppDispatch; },\n/* harmony export */   useAppSelector: function() { return /* binding */ useAppSelector; }\n/* harmony export */ });\n/* harmony import */ var react_redux__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-redux */ \"(app-pages-browser)/./node_modules/react-redux/dist/react-redux.mjs\");\n\nconst useAppDispatch = ()=>(0,react_redux__WEBPACK_IMPORTED_MODULE_0__.useDispatch)();\nconst useAppSelector = react_redux__WEBPACK_IMPORTED_MODULE_0__.useSelector;\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9yZWR1eC9ob29rcy50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBNkU7QUFHdEUsTUFBTUUsaUJBQWlCLElBQU1GLHdEQUFXQSxHQUFnQjtBQUN4RCxNQUFNRyxpQkFBa0RGLG9EQUFXQSxDQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3NyYy9yZWR1eC9ob29rcy50cz8xYzJhIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IFR5cGVkVXNlU2VsZWN0b3JIb29rLCB1c2VEaXNwYXRjaCwgdXNlU2VsZWN0b3IgfSBmcm9tICdyZWFjdC1yZWR1eCc7XHJcbmltcG9ydCB0eXBlIHsgUm9vdFN0YXRlLCBBcHBEaXNwYXRjaCB9IGZyb20gJy4vc3RvcmUnO1xyXG5cclxuZXhwb3J0IGNvbnN0IHVzZUFwcERpc3BhdGNoID0gKCkgPT4gdXNlRGlzcGF0Y2g8QXBwRGlzcGF0Y2g+KCk7XHJcbmV4cG9ydCBjb25zdCB1c2VBcHBTZWxlY3RvcjogVHlwZWRVc2VTZWxlY3Rvckhvb2s8Um9vdFN0YXRlPiA9IHVzZVNlbGVjdG9yOyJdLCJuYW1lcyI6WyJ1c2VEaXNwYXRjaCIsInVzZVNlbGVjdG9yIiwidXNlQXBwRGlzcGF0Y2giLCJ1c2VBcHBTZWxlY3RvciJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/redux/hooks.ts\n"));

/***/ })

});