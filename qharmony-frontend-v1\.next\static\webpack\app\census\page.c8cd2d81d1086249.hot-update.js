"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/census/page",{

/***/ "(app-pages-browser)/./src/app/census/public/Index.tsx":
/*!*****************************************!*\
  !*** ./src/app/census/public/Index.tsx ***!
  \*****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../components/ui/button */ \"(app-pages-browser)/./src/app/census/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../components/ui/card */ \"(app-pages-browser)/./src/app/census/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_accordion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../components/ui/accordion */ \"(app-pages-browser)/./src/app/census/components/ui/accordion.tsx\");\n/* harmony import */ var _lib_react_router_dom__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../lib/react-router-dom */ \"(app-pages-browser)/./src/app/census/lib/react-router-dom.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Building_CheckCircle_Clock_Download_FileX_Mail_Plus_TrendingDown_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Building,CheckCircle,Clock,Download,FileX,Mail,Plus,TrendingDown,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Building_CheckCircle_Clock_Download_FileX_Mail_Plus_TrendingDown_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Building,CheckCircle,Clock,Download,FileX,Mail,Plus,TrendingDown,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Building_CheckCircle_Clock_Download_FileX_Mail_Plus_TrendingDown_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Building,CheckCircle,Clock,Download,FileX,Mail,Plus,TrendingDown,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Building_CheckCircle_Clock_Download_FileX_Mail_Plus_TrendingDown_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Building,CheckCircle,Clock,Download,FileX,Mail,Plus,TrendingDown,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/building.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Building_CheckCircle_Clock_Download_FileX_Mail_Plus_TrendingDown_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Building,CheckCircle,Clock,Download,FileX,Mail,Plus,TrendingDown,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Building_CheckCircle_Clock_Download_FileX_Mail_Plus_TrendingDown_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Building,CheckCircle,Clock,Download,FileX,Mail,Plus,TrendingDown,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Building_CheckCircle_Clock_Download_FileX_Mail_Plus_TrendingDown_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Building,CheckCircle,Clock,Download,FileX,Mail,Plus,TrendingDown,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Building_CheckCircle_Clock_Download_FileX_Mail_Plus_TrendingDown_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Building,CheckCircle,Clock,Download,FileX,Mail,Plus,TrendingDown,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Building_CheckCircle_Clock_Download_FileX_Mail_Plus_TrendingDown_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Building,CheckCircle,Clock,Download,FileX,Mail,Plus,TrendingDown,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-down.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Building_CheckCircle_Clock_Download_FileX_Mail_Plus_TrendingDown_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Building,CheckCircle,Clock,Download,FileX,Mail,Plus,TrendingDown,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-x.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_5__);\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst Index = ()=>{\n    _s();\n    const navigate = (0,_lib_react_router_dom__WEBPACK_IMPORTED_MODULE_4__.useNavigate)();\n    const [isLoggedIn, setIsLoggedIn] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(false);\n    // Check if user is logged in using the actual auth system\n    (0,react__WEBPACK_IMPORTED_MODULE_5__.useEffect)(()=>{\n        // Check for user ID using the same keys as the main auth system\n        const primaryKey = \"userid1\" || 0;\n        const altKey = \"userId\" || 0;\n        const userId = localStorage.getItem(primaryKey) || localStorage.getItem(altKey);\n        const ssoDone = localStorage.getItem(\"ssoDone1\");\n        const userEmail = localStorage.getItem(\"userEmail1\");\n        // Debug logging\n        console.log(\"Census Index Auth Check:\", {\n            userId,\n            ssoDone,\n            userEmail\n        });\n        // Check multiple conditions for authentication\n        const isAuthenticated = userId && (ssoDone === \"true\" || userEmail);\n        console.log(\"Census Index - User authenticated:\", isAuthenticated);\n        setIsLoggedIn(!!isAuthenticated);\n    }, []);\n    const handleDownloadTemplate = ()=>{\n        // Create a simple CSV template\n        const csvContent = \"Employee Name,Date of Birth,Gender,Coverage Tier,Annual Salary,Department\\nJohn Doe,1985-06-15,Male,Employee + Spouse,65000,Marketing\\nJane Smith,1990-03-22,Female,Employee Only,58000,Sales\\nMike Johnson,1982-11-08,Male,Employee + Family,72000,Engineering\";\n        const blob = new Blob([\n            csvContent\n        ], {\n            type: \"text/csv\"\n        });\n        const url = window.URL.createObjectURL(blob);\n        const link = document.createElement(\"a\");\n        link.href = url;\n        link.download = \"census_template.csv\";\n        document.body.appendChild(link);\n        link.click();\n        document.body.removeChild(link);\n        window.URL.revokeObjectURL(url);\n    };\n    const handleUploadCensusClick = ()=>{\n        if (isLoggedIn) {\n            // User is logged in, go directly to upload census\n            navigate(\"/upload-census\");\n        } else {\n            // User not logged in, go to login prompt\n            navigate(\"/login-prompt\");\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-slate-50 to-blue-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"border-b bg-white/90 backdrop-blur-sm sticky top-0 z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-6xl mx-auto px-3 sm:px-4 py-3 flex justify-between items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>navigate(\"/\"),\n                            className: \"flex items-center space-x-2 hover:scale-105 transition-transform duration-200 cursor-pointer\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                    src: \"/logo_cesus.png\",\n                                    alt: \"BenOsphere Logo\",\n                                    className: \"w-8 h-8\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\Index.tsx\",\n                                    lineNumber: 68,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xl sm:text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent\",\n                                    children: \"BenOsphere\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\Index.tsx\",\n                                    lineNumber: 69,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\Index.tsx\",\n                            lineNumber: 67,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2 sm:space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                    variant: \"ghost\",\n                                    size: \"sm\",\n                                    onClick: ()=>navigate(\"/pricing\"),\n                                    className: \"hidden sm:inline-flex\",\n                                    children: \"Pricing\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\Index.tsx\",\n                                    lineNumber: 74,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                    variant: \"ghost\",\n                                    size: \"sm\",\n                                    onClick: ()=>navigate(\"/dashboard\"),\n                                    className: \"hidden sm:inline-flex\",\n                                    children: \"Dashboard\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\Index.tsx\",\n                                    lineNumber: 77,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                    variant: \"outline\",\n                                    size: \"sm\",\n                                    onClick: ()=>navigate(\"/login-prompt\"),\n                                    children: \"Sign In\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\Index.tsx\",\n                                    lineNumber: 80,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\Index.tsx\",\n                            lineNumber: 73,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\Index.tsx\",\n                    lineNumber: 66,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\Index.tsx\",\n                lineNumber: 65,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"border-b border-gray-200/60 bg-gradient-to-br from-slate-50 to-blue-50 relative\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                    className: \"max-w-6xl mx-auto px-3 sm:px-4 py-12 sm:py-20\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-0 overflow-hidden pointer-events-none\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute top-20 left-10 w-2 h-2 bg-blue-400 rounded-full opacity-40\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\Index.tsx\",\n                                    lineNumber: 93,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute top-32 right-20 w-2 h-2 bg-purple-400 rounded-full opacity-40\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\Index.tsx\",\n                                    lineNumber: 94,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute bottom-40 left-20 w-2 h-2 bg-blue-400 rounded-full opacity-40\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\Index.tsx\",\n                                    lineNumber: 95,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute bottom-20 right-10 w-2 h-2 bg-purple-400 rounded-full opacity-40\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\Index.tsx\",\n                                    lineNumber: 96,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute top-40 left-1/4 opacity-20\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Building_CheckCircle_Clock_Download_FileX_Mail_Plus_TrendingDown_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: \"w-4 h-4 text-gray-400\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\Index.tsx\",\n                                        lineNumber: 98,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\Index.tsx\",\n                                    lineNumber: 97,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute top-60 right-1/4 opacity-20\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Building_CheckCircle_Clock_Download_FileX_Mail_Plus_TrendingDown_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: \"w-4 h-4 text-gray-400\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\Index.tsx\",\n                                        lineNumber: 101,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\Index.tsx\",\n                                    lineNumber: 100,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute bottom-60 left-1/3 opacity-20\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Building_CheckCircle_Clock_Download_FileX_Mail_Plus_TrendingDown_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: \"w-4 h-4 text-gray-400\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\Index.tsx\",\n                                        lineNumber: 104,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\Index.tsx\",\n                                    lineNumber: 103,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute bottom-40 right-1/3 opacity-20\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Building_CheckCircle_Clock_Download_FileX_Mail_Plus_TrendingDown_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: \"w-4 h-4 text-gray-400\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\Index.tsx\",\n                                        lineNumber: 107,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\Index.tsx\",\n                                    lineNumber: 106,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\Index.tsx\",\n                            lineNumber: 92,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center relative z-10 mb-16\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"inline-block bg-blue-100 text-blue-800 text-sm font-medium px-4 py-2 rounded-full\",\n                                        children: \"Revenue Engine for SMB Employee Benefit Brokers\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\Index.tsx\",\n                                        lineNumber: 115,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\Index.tsx\",\n                                    lineNumber: 114,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-4xl sm:text-5xl lg:text-6xl font-bold text-gray-900 mb-6 leading-tight\",\n                                    children: [\n                                        \"Upload a Census.\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\Index.tsx\",\n                                            lineNumber: 120,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent\",\n                                            children: \"Unlock Smart Benefits\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\Index.tsx\",\n                                            lineNumber: 121,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\Index.tsx\",\n                                    lineNumber: 118,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xl sm:text-2xl text-gray-600 mb-8 max-w-3xl mx-auto leading-relaxed\",\n                                    children: \"BenOsphere transforms raw census files into intelligent, broker-ready reports in under 60 seconds.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\Index.tsx\",\n                                    lineNumber: 126,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col items-center space-y-4 mb-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                            size: \"lg\",\n                                            className: \"bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-8 py-4 text-lg rounded-full shadow-lg hover:shadow-xl transition-all duration-200 hover:scale-105\",\n                                            onClick: handleUploadCensusClick,\n                                            children: [\n                                                \"Upload Census & See Instant Insight\",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Building_CheckCircle_Clock_Download_FileX_Mail_Plus_TrendingDown_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    className: \"ml-2 h-5 w-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\Index.tsx\",\n                                                    lineNumber: 134,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\Index.tsx\",\n                                            lineNumber: 132,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600 mb-2\",\n                                                    children: \"Have the file? Give it a spin — it's free.\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\Index.tsx\",\n                                                    lineNumber: 137,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                                    variant: \"ghost\",\n                                                    size: \"sm\",\n                                                    className: \"text-blue-600 hover:text-blue-700 underline text-sm\",\n                                                    onClick: handleDownloadTemplate,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Building_CheckCircle_Clock_Download_FileX_Mail_Plus_TrendingDown_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                            className: \"mr-1 h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\Index.tsx\",\n                                                            lineNumber: 139,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        \"Download Template\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\Index.tsx\",\n                                                    lineNumber: 138,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\Index.tsx\",\n                                            lineNumber: 136,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\Index.tsx\",\n                                    lineNumber: 131,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\Index.tsx\",\n                            lineNumber: 112,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid md:grid-cols-3 gap-8 relative z-10\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center mb-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Building_CheckCircle_Clock_Download_FileX_Mail_Plus_TrendingDown_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"w-6 h-6 text-blue-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\Index.tsx\",\n                                                    lineNumber: 152,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\Index.tsx\",\n                                                lineNumber: 151,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-semibold text-gray-900 mb-2\",\n                                                children: \"Save Hours of Manual Work\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\Index.tsx\",\n                                                lineNumber: 154,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600 text-sm\",\n                                                children: \"No more reformatting spreadsheets or chasing employers for data\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\Index.tsx\",\n                                                lineNumber: 155,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\Index.tsx\",\n                                        lineNumber: 150,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\Index.tsx\",\n                                    lineNumber: 149,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gradient-to-br from-blue-50 to-purple-50 rounded-2xl p-8 shadow-xl border border-blue-100\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-16 h-16 bg-gradient-to-r from-blue-600 to-purple-600 rounded-2xl flex items-center justify-center mx-auto mb-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Building_CheckCircle_Clock_Download_FileX_Mail_Plus_TrendingDown_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        className: \"w-8 h-8 text-white\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\Index.tsx\",\n                                                        lineNumber: 166,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\Index.tsx\",\n                                                    lineNumber: 165,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-2xl font-bold text-gray-900 mb-2\",\n                                                    children: \"Instant Analysis\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\Index.tsx\",\n                                                    lineNumber: 168,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-600\",\n                                                    children: \"AI-powered insights with demographics, plan recommendations, and risk analysis\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\Index.tsx\",\n                                                    lineNumber: 171,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\Index.tsx\",\n                                            lineNumber: 164,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\Index.tsx\",\n                                        lineNumber: 163,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\Index.tsx\",\n                                    lineNumber: 162,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center mb-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Building_CheckCircle_Clock_Download_FileX_Mail_Plus_TrendingDown_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    className: \"w-6 h-6 text-green-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\Index.tsx\",\n                                                    lineNumber: 182,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\Index.tsx\",\n                                                lineNumber: 181,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-semibold text-gray-900 mb-2\",\n                                                children: \"Win More Deals\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\Index.tsx\",\n                                                lineNumber: 184,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600 text-sm\",\n                                                children: \"Faster turnaround with smart upsell opportunities and cost-saving insights\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\Index.tsx\",\n                                                lineNumber: 185,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\Index.tsx\",\n                                        lineNumber: 180,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\Index.tsx\",\n                                    lineNumber: 179,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\Index.tsx\",\n                            lineNumber: 147,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\Index.tsx\",\n                    lineNumber: 90,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\Index.tsx\",\n                lineNumber: 88,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"bg-white py-16\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-6xl mx-auto px-3 sm:px-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        className: \"bg-gradient-to-br from-purple-50 to-blue-50 border-purple-200 shadow-xl\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            className: \"p-8 text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Building_CheckCircle_Clock_Download_FileX_Mail_Plus_TrendingDown_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"w-8 h-8 text-purple-600\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\Index.tsx\",\n                                                lineNumber: 201,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\Index.tsx\",\n                                            lineNumber: 200,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-2xl font-bold text-gray-900 mb-3\",\n                                            children: \"Don't Have the Census File?\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\Index.tsx\",\n                                            lineNumber: 203,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-lg text-gray-600 mb-6 max-w-2xl mx-auto\",\n                                            children: \"Send a secure link to your client — they'll upload their census directly. You'll be notified when their enriched report is ready.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\Index.tsx\",\n                                            lineNumber: 206,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\Index.tsx\",\n                                    lineNumber: 199,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                    variant: \"outline\",\n                                    size: \"lg\",\n                                    className: \"px-8 py-4 text-lg rounded-full border-2 border-purple-300 hover:bg-purple-50 hover:border-purple-400 transition-all duration-200\",\n                                    onClick: ()=>navigate(\"/employer-invite\"),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Building_CheckCircle_Clock_Download_FileX_Mail_Plus_TrendingDown_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            className: \"mr-2 h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\Index.tsx\",\n                                            lineNumber: 213,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        \"Invite Employer to Upload\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\Index.tsx\",\n                                    lineNumber: 212,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-gray-500 mt-4\",\n                                    children: \"Secure, guided upload process • HIPAA compliant • Instant notifications\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\Index.tsx\",\n                                    lineNumber: 217,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\Index.tsx\",\n                            lineNumber: 198,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\Index.tsx\",\n                        lineNumber: 197,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\Index.tsx\",\n                    lineNumber: 196,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\Index.tsx\",\n                lineNumber: 195,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"bg-gray-50 py-12\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-6xl mx-auto px-3 sm:px-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        className: \"shadow-lg mb-8 sm:mb-12 border-0 bg-gradient-to-r from-red-50 to-orange-50 border-red-100\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            className: \"p-6 sm:p-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-center mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Building_CheckCircle_Clock_Download_FileX_Mail_Plus_TrendingDown_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    className: \"h-8 w-8 text-red-600 mr-3\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\Index.tsx\",\n                                                    lineNumber: 232,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-2xl sm:text-3xl font-bold text-gray-900\",\n                                                    children: \"The Problem\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\Index.tsx\",\n                                                    lineNumber: 233,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\Index.tsx\",\n                                            lineNumber: 231,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-lg text-gray-700 mb-6\",\n                                            children: \"Manual census workflows are broken.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\Index.tsx\",\n                                            lineNumber: 237,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 text-base leading-relaxed mb-6\",\n                                            children: \"Brokers waste hours reformatting spreadsheets, chasing employers, and entering data by hand — only to miss insights that could win deals.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\Index.tsx\",\n                                            lineNumber: 240,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\Index.tsx\",\n                                    lineNumber: 230,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center bg-white/70 rounded-lg p-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-red-100 w-12 h-12 rounded-full flex items-center justify-center mr-4 flex-shrink-0\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Building_CheckCircle_Clock_Download_FileX_Mail_Plus_TrendingDown_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                        className: \"h-6 w-6 text-red-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\Index.tsx\",\n                                                        lineNumber: 248,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\Index.tsx\",\n                                                    lineNumber: 247,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"font-semibold text-red-800 mb-1\",\n                                                            children: \"Time Drain\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\Index.tsx\",\n                                                            lineNumber: 251,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-600 text-sm\",\n                                                            children: \"Hours spent on manual data entry and reformatting instead of selling\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\Index.tsx\",\n                                                            lineNumber: 252,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\Index.tsx\",\n                                                    lineNumber: 250,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\Index.tsx\",\n                                            lineNumber: 246,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center bg-white/70 rounded-lg p-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-orange-100 w-12 h-12 rounded-full flex items-center justify-center mr-4 flex-shrink-0\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Building_CheckCircle_Clock_Download_FileX_Mail_Plus_TrendingDown_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                        className: \"h-6 w-6 text-orange-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\Index.tsx\",\n                                                        lineNumber: 258,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\Index.tsx\",\n                                                    lineNumber: 257,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"font-semibold text-orange-800 mb-1\",\n                                                            children: \"Lost Opportunities\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\Index.tsx\",\n                                                            lineNumber: 261,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-600 text-sm\",\n                                                            children: \"Delays in turnaround mean competitors win the business\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\Index.tsx\",\n                                                            lineNumber: 262,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\Index.tsx\",\n                                                    lineNumber: 260,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\Index.tsx\",\n                                            lineNumber: 256,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center bg-white/70 rounded-lg p-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-yellow-100 w-12 h-12 rounded-full flex items-center justify-center mr-4 flex-shrink-0\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Building_CheckCircle_Clock_Download_FileX_Mail_Plus_TrendingDown_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                        className: \"h-6 w-6 text-yellow-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\Index.tsx\",\n                                                        lineNumber: 268,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\Index.tsx\",\n                                                    lineNumber: 267,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"font-semibold text-yellow-800 mb-1\",\n                                                            children: \"Missed Insights\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\Index.tsx\",\n                                                            lineNumber: 271,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-600 text-sm\",\n                                                            children: \"Raw data without enrichment means no upsell opportunities\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\Index.tsx\",\n                                                            lineNumber: 272,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\Index.tsx\",\n                                                    lineNumber: 270,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\Index.tsx\",\n                                            lineNumber: 266,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\Index.tsx\",\n                                    lineNumber: 245,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\Index.tsx\",\n                            lineNumber: 229,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\Index.tsx\",\n                        lineNumber: 228,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\Index.tsx\",\n                    lineNumber: 227,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\Index.tsx\",\n                lineNumber: 226,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"bg-white py-12\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-6xl mx-auto px-3 sm:px-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        className: \"shadow-lg mb-8 sm:mb-12 border-0 bg-gradient-to-r from-green-50 to-emerald-50 border-green-100\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            className: \"p-6 sm:p-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center mb-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-center mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Building_CheckCircle_Clock_Download_FileX_Mail_Plus_TrendingDown_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    className: \"h-8 w-8 text-green-600 mr-3\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\Index.tsx\",\n                                                    lineNumber: 288,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-2xl sm:text-3xl font-bold text-gray-900\",\n                                                    children: \"The Solution: Smart Census, Instant Insights\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\Index.tsx\",\n                                                    lineNumber: 289,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\Index.tsx\",\n                                            lineNumber: 287,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-lg text-gray-700 mb-4\",\n                                            children: \"BenOsphere transforms messy census files into intelligent, broker-ready reports — in under 60 seconds.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\Index.tsx\",\n                                            lineNumber: 293,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 text-base leading-relaxed mb-6\",\n                                            children: \"No formatting. No delays. Just real-time enrichment that drives faster quotes, smarter upsells, and a better client experience.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\Index.tsx\",\n                                            lineNumber: 296,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\Index.tsx\",\n                                    lineNumber: 286,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid lg:grid-cols-2 gap-8 items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-start bg-white/70 rounded-lg p-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"bg-blue-100 w-10 h-10 rounded-full flex items-center justify-center mr-4 flex-shrink-0 mt-1\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-lg\",\n                                                                children: \"\\uD83D\\uDCE4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\Index.tsx\",\n                                                                lineNumber: 306,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\Index.tsx\",\n                                                            lineNumber: 305,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                    className: \"font-semibold mb-2\",\n                                                                    children: \"1. Upload Your Census File\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\Index.tsx\",\n                                                                    lineNumber: 309,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-gray-600\",\n                                                                    children: \"Simply drag and drop your census file or send a secure invitation link directly to your employer.\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\Index.tsx\",\n                                                                    lineNumber: 310,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\Index.tsx\",\n                                                            lineNumber: 308,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\Index.tsx\",\n                                                    lineNumber: 304,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-start bg-white/70 rounded-lg p-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"bg-purple-100 w-10 h-10 rounded-full flex items-center justify-center mr-4 flex-shrink-0 mt-1\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-lg\",\n                                                                children: \"\\uD83E\\uDD16\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\Index.tsx\",\n                                                                lineNumber: 316,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\Index.tsx\",\n                                                            lineNumber: 315,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                    className: \"font-semibold mb-2\",\n                                                                    children: \"2. Instant AI Analysis\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\Index.tsx\",\n                                                                    lineNumber: 319,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-gray-600\",\n                                                                    children: \"Our AI instantly processes and enriches your data with dependent analysis, age demographics, plan recommendations, and risk insights.\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\Index.tsx\",\n                                                                    lineNumber: 320,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\Index.tsx\",\n                                                            lineNumber: 318,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\Index.tsx\",\n                                                    lineNumber: 314,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-start bg-white/70 rounded-lg p-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"bg-green-100 w-10 h-10 rounded-full flex items-center justify-center mr-4 flex-shrink-0 mt-1\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-lg\",\n                                                                children: \"\\uD83D\\uDC40\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\Index.tsx\",\n                                                                lineNumber: 326,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\Index.tsx\",\n                                                            lineNumber: 325,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                    className: \"font-semibold mb-2\",\n                                                                    children: \"3. Get Your Smart Preview\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\Index.tsx\",\n                                                                    lineNumber: 329,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-gray-600\",\n                                                                    children: \"View key insights instantly: group size, average age, family coverage patterns, and our recommended plan strategy.\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\Index.tsx\",\n                                                                    lineNumber: 330,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\Index.tsx\",\n                                                            lineNumber: 328,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\Index.tsx\",\n                                                    lineNumber: 324,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-start bg-white/70 rounded-lg p-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"bg-orange-100 w-10 h-10 rounded-full flex items-center justify-center mr-4 flex-shrink-0 mt-1\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-lg\",\n                                                                children: \"\\uD83D\\uDD13\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\Index.tsx\",\n                                                                lineNumber: 336,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\Index.tsx\",\n                                                            lineNumber: 335,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                    className: \"font-semibold mb-2\",\n                                                                    children: \"4. Access Full Report & Insights\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\Index.tsx\",\n                                                                    lineNumber: 339,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-gray-600\",\n                                                                    children: \"Sign in to unlock detailed cost-saving opportunities, contribution benchmarks, and tailored upsell recommendations — ready to present or quote.\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\Index.tsx\",\n                                                                    lineNumber: 340,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\Index.tsx\",\n                                                            lineNumber: 338,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\Index.tsx\",\n                                                    lineNumber: 334,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\Index.tsx\",\n                                            lineNumber: 303,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white/80 rounded-xl p-4 shadow-inner\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                        src: \"/demoimage.png\",\n                                                        alt: \"BenOsphere dashboard showing enriched census data and insights\",\n                                                        className: \"w-full h-auto rounded-lg shadow-lg hover:scale-105 transition-transform duration-300\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\Index.tsx\",\n                                                        lineNumber: 348,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-center text-sm text-gray-500 mt-2\",\n                                                        children: \"Live census analysis and insights dashboard\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\Index.tsx\",\n                                                        lineNumber: 349,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\Index.tsx\",\n                                                lineNumber: 347,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\Index.tsx\",\n                                            lineNumber: 346,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\Index.tsx\",\n                                    lineNumber: 301,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\Index.tsx\",\n                            lineNumber: 285,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\Index.tsx\",\n                        lineNumber: 284,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\Index.tsx\",\n                    lineNumber: 283,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\Index.tsx\",\n                lineNumber: 282,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"bg-white py-16\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-6xl mx-auto px-3 sm:px-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-12\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl sm:text-4xl font-bold text-gray-900 mb-4\",\n                                    children: \"BenOsphere – FAQs for Brokers\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\Index.tsx\",\n                                    lineNumber: 362,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xl text-gray-600\",\n                                    children: \"Everything you need to know about transforming census data into revenue opportunities.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\Index.tsx\",\n                                    lineNumber: 365,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\Index.tsx\",\n                            lineNumber: 361,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-2xl shadow-lg border border-gray-200 p-6 sm:p-8 max-w-4xl mx-auto\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_accordion__WEBPACK_IMPORTED_MODULE_3__.Accordion, {\n                                type: \"single\",\n                                collapsible: true,\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_accordion__WEBPACK_IMPORTED_MODULE_3__.AccordionItem, {\n                                        value: \"item-1\",\n                                        className: \"border border-gray-200 rounded-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_accordion__WEBPACK_IMPORTED_MODULE_3__.AccordionTrigger, {\n                                                className: \"text-left font-semibold text-gray-900 hover:text-blue-600 px-6 py-4\",\n                                                children: \"How is BenOsphere different from tools like Employee Navigator or Nayya?\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\Index.tsx\",\n                                                lineNumber: 373,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_accordion__WEBPACK_IMPORTED_MODULE_3__.AccordionContent, {\n                                                className: \"text-gray-600 leading-relaxed px-6 pb-4\",\n                                                children: \"Employee Navigator stores and manages census data but doesn't analyze it in depth. Nayya helps employees choose benefits but focuses on the individual user experience. BenOsphere is built for brokers—it analyzes census data to uncover revenue opportunities, benchmark benefits, and show cost-saving strategies you can bring to clients.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\Index.tsx\",\n                                                lineNumber: 376,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\Index.tsx\",\n                                        lineNumber: 372,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_accordion__WEBPACK_IMPORTED_MODULE_3__.AccordionItem, {\n                                        value: \"item-2\",\n                                        className: \"border border-gray-200 rounded-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_accordion__WEBPACK_IMPORTED_MODULE_3__.AccordionTrigger, {\n                                                className: \"text-left font-semibold text-gray-900 hover:text-blue-600 px-6 py-4\",\n                                                children: \"I already have census data. Why do I need BenOsphere?\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\Index.tsx\",\n                                                lineNumber: 382,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_accordion__WEBPACK_IMPORTED_MODULE_3__.AccordionContent, {\n                                                className: \"text-gray-600 leading-relaxed px-6 pb-4\",\n                                                children: \"Spreadsheets alone don't drive revenue or prove value. BenOsphere transforms raw census data into insights and specific recommendations—so you can walk into client meetings with actionable ideas, not just data files.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\Index.tsx\",\n                                                lineNumber: 385,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\Index.tsx\",\n                                        lineNumber: 381,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_accordion__WEBPACK_IMPORTED_MODULE_3__.AccordionItem, {\n                                        value: \"item-3\",\n                                        className: \"border border-gray-200 rounded-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_accordion__WEBPACK_IMPORTED_MODULE_3__.AccordionTrigger, {\n                                                className: \"text-left font-semibold text-gray-900 hover:text-blue-600 px-6 py-4\",\n                                                children: \"How does BenOsphere help me save costs or make money?\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\Index.tsx\",\n                                                lineNumber: 391,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_accordion__WEBPACK_IMPORTED_MODULE_3__.AccordionContent, {\n                                                className: \"text-gray-600 leading-relaxed px-6 pb-4\",\n                                                children: \"BenOsphere shows where clients might be overspending and helps you recommend smarter plan designs that save them money. It also flags upsell opportunities, helping you grow revenue without adding more clients.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\Index.tsx\",\n                                                lineNumber: 394,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\Index.tsx\",\n                                        lineNumber: 390,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_accordion__WEBPACK_IMPORTED_MODULE_3__.AccordionItem, {\n                                        value: \"item-4\",\n                                        className: \"border border-gray-200 rounded-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_accordion__WEBPACK_IMPORTED_MODULE_3__.AccordionTrigger, {\n                                                className: \"text-left font-semibold text-gray-900 hover:text-blue-600 px-6 py-4\",\n                                                children: \"We're a small broker agency—why do we need this?\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\Index.tsx\",\n                                                lineNumber: 400,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_accordion__WEBPACK_IMPORTED_MODULE_3__.AccordionContent, {\n                                                className: \"text-gray-600 leading-relaxed px-6 pb-4\",\n                                                children: \"We built BenOsphere exactly for smaller agencies like yours. Big firms have expensive analytics teams and tools that smaller brokers can't afford. BenOsphere levels the playing field, helping you act like a consultant, not just a broker—and deliver insights that set you apart.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\Index.tsx\",\n                                                lineNumber: 403,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\Index.tsx\",\n                                        lineNumber: 399,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_accordion__WEBPACK_IMPORTED_MODULE_3__.AccordionItem, {\n                                        value: \"item-5\",\n                                        className: \"border border-gray-200 rounded-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_accordion__WEBPACK_IMPORTED_MODULE_3__.AccordionTrigger, {\n                                                className: \"text-left font-semibold text-gray-900 hover:text-blue-600 px-6 py-4\",\n                                                children: \"Is BenOsphere expensive or hard to implement?\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\Index.tsx\",\n                                                lineNumber: 409,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_accordion__WEBPACK_IMPORTED_MODULE_3__.AccordionContent, {\n                                                className: \"text-gray-600 leading-relaxed px-6 pb-4\",\n                                                children: \"No. BenOsphere is affordable for brokers of all sizes, with no complex integrations required. You simply upload census files and get insights back quickly.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\Index.tsx\",\n                                                lineNumber: 412,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\Index.tsx\",\n                                        lineNumber: 408,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_accordion__WEBPACK_IMPORTED_MODULE_3__.AccordionItem, {\n                                        value: \"item-6\",\n                                        className: \"border border-gray-200 rounded-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_accordion__WEBPACK_IMPORTED_MODULE_3__.AccordionTrigger, {\n                                                className: \"text-left font-semibold text-gray-900 hover:text-blue-600 px-6 py-4\",\n                                                children: \"Is this only useful during renewal season?\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\Index.tsx\",\n                                                lineNumber: 418,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_accordion__WEBPACK_IMPORTED_MODULE_3__.AccordionContent, {\n                                                className: \"text-gray-600 leading-relaxed px-6 pb-4\",\n                                                children: \"Not at all. BenOsphere gives you insights you can revisit every quarter, helping you stay engaged with clients year-round—and proving your ongoing value beyond just renewals.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\Index.tsx\",\n                                                lineNumber: 421,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\Index.tsx\",\n                                        lineNumber: 417,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\Index.tsx\",\n                                lineNumber: 371,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\Index.tsx\",\n                            lineNumber: 370,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\Index.tsx\",\n                    lineNumber: 360,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\Index.tsx\",\n                lineNumber: 359,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                className: \"bg-gray-900 text-white py-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-6xl mx-auto px-3 sm:px-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col sm:flex-row justify-between items-center space-y-4 sm:space-y-0\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center sm:text-left\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>navigate(\"/\"),\n                                        className: \"flex items-center space-x-2 hover:scale-105 transition-transform duration-200 cursor-pointer mb-2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-lg font-bold bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent\",\n                                            children: \"BenOsphere\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\Index.tsx\",\n                                            lineNumber: 436,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\Index.tsx\",\n                                        lineNumber: 435,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-400 text-sm\",\n                                        children: \"Built in Seattle\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\Index.tsx\",\n                                        lineNumber: 440,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\Index.tsx\",\n                                lineNumber: 434,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col sm:flex-row items-center space-y-2 sm:space-y-0 sm:space-x-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"text-gray-300 hover:text-white text-sm transition-colors\",\n                                        children: \"Privacy Policy\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\Index.tsx\",\n                                        lineNumber: 443,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"text-gray-300 hover:text-white text-sm transition-colors\",\n                                        children: \"Terms & Conditions\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\Index.tsx\",\n                                        lineNumber: 446,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\Index.tsx\",\n                                lineNumber: 442,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\Index.tsx\",\n                        lineNumber: 433,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\Index.tsx\",\n                    lineNumber: 432,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\Index.tsx\",\n                lineNumber: 431,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\Index.tsx\",\n        lineNumber: 63,\n        columnNumber: 5\n    }, undefined);\n};\n_s(Index, \"ByRb0k4YO4RAgY8aI0kHhVwbeVA=\", false, function() {\n    return [\n        _lib_react_router_dom__WEBPACK_IMPORTED_MODULE_4__.useNavigate\n    ];\n});\n_c = Index;\n/* harmony default export */ __webpack_exports__[\"default\"] = (Index);\nvar _c;\n$RefreshReg$(_c, \"Index\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/census/public/Index.tsx\n"));

/***/ })

});