"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/census/page",{

/***/ "(app-pages-browser)/./src/app/census/public/BrokerDashboard.tsx":
/*!***************************************************!*\
  !*** ./src/app/census/public/BrokerDashboard.tsx ***!
  \***************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../components/ui/button */ \"(app-pages-browser)/./src/app/census/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../components/ui/card */ \"(app-pages-browser)/./src/app/census/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../components/ui/input */ \"(app-pages-browser)/./src/app/census/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../components/ui/select */ \"(app-pages-browser)/./src/app/census/components/ui/select.tsx\");\n/* harmony import */ var _lib_react_router_dom__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../lib/react-router-dom */ \"(app-pages-browser)/./src/app/census/lib/react-router-dom.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_DollarSign_Lock_Mail_Plus_Search_Share2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,DollarSign,Lock,Mail,Plus,Search,Share2,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_DollarSign_Lock_Mail_Plus_Search_Share2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,DollarSign,Lock,Mail,Plus,Search,Share2,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_DollarSign_Lock_Mail_Plus_Search_Share2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,DollarSign,Lock,Mail,Plus,Search,Share2,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_DollarSign_Lock_Mail_Plus_Search_Share2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,DollarSign,Lock,Mail,Plus,Search,Share2,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_DollarSign_Lock_Mail_Plus_Search_Share2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,DollarSign,Lock,Mail,Plus,Search,Share2,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_DollarSign_Lock_Mail_Plus_Search_Share2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,DollarSign,Lock,Mail,Plus,Search,Share2,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_DollarSign_Lock_Mail_Plus_Search_Share2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,DollarSign,Lock,Mail,Plus,Search,Share2,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_DollarSign_Lock_Mail_Plus_Search_Share2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,DollarSign,Lock,Mail,Plus,Search,Share2,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/lock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_DollarSign_Lock_Mail_Plus_Search_Share2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,DollarSign,Lock,Mail,Plus,Search,Share2,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/share-2.js\");\n/* harmony import */ var _components_AskBrea__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../components/AskBrea */ \"(app-pages-browser)/./src/app/census/components/AskBrea.tsx\");\n/* harmony import */ var _components_ProfileHandler__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../components/ProfileHandler */ \"(app-pages-browser)/./src/app/census/components/ProfileHandler.tsx\");\n/* harmony import */ var _hooks_usePlanRestrictions__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../hooks/usePlanRestrictions */ \"(app-pages-browser)/./src/app/census/hooks/usePlanRestrictions.tsx\");\n/* harmony import */ var _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../components/ui/use-toast */ \"(app-pages-browser)/./src/app/census/components/ui/use-toast.ts\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n// import NavigationDropdown from \"../components/NavigationDropdown\"; // Temporarily removed to fix circular import\n\n\n\nconst BrokerDashboard = ()=>{\n    _s();\n    const navigate = (0,_lib_react_router_dom__WEBPACK_IMPORTED_MODULE_6__.useNavigate)();\n    const { toast } = (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_10__.useToast)();\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [employeeCountFilter, setEmployeeCountFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [riskScoreFilter, setRiskScoreFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [suggestedPlanFilter, setSuggestedPlanFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const { canViewReport, reportsRemaining, isAtLimit, trackReportView } = (0,_hooks_usePlanRestrictions__WEBPACK_IMPORTED_MODULE_9__.usePlanRestrictions)();\n    // Mock data for multiple employers\n    const employerInsights = [\n        {\n            id: \"1\",\n            companyName: \"TechCorp Solutions\",\n            employees: 43,\n            averageAge: 36,\n            dependents: 1.3,\n            planType: \"PPO + HSA Combo\",\n            potentialSavings: \"$127,500\",\n            riskScore: 6.2,\n            uploadDate: \"2024-01-15\",\n            status: \"analyzed\"\n        },\n        {\n            id: \"2\",\n            companyName: \"Green Manufacturing\",\n            employees: 87,\n            averageAge: 42,\n            dependents: 1.8,\n            planType: \"Traditional PPO\",\n            potentialSavings: \"$245,000\",\n            riskScore: 7.1,\n            uploadDate: \"2024-01-10\",\n            status: \"analyzed\"\n        },\n        {\n            id: \"3\",\n            companyName: \"StartupXYZ\",\n            employees: 18,\n            averageAge: 29,\n            dependents: 0.8,\n            planType: \"HSA Only\",\n            potentialSavings: \"$32,400\",\n            riskScore: 4.5,\n            uploadDate: \"2024-01-18\",\n            status: \"processing\"\n        }\n    ];\n    // Filter logic\n    const filteredEmployers = employerInsights.filter((employer)=>{\n        // Search filter\n        if (searchTerm && !employer.companyName.toLowerCase().includes(searchTerm.toLowerCase())) {\n            return false;\n        }\n        // Employee count filter\n        if (employeeCountFilter !== \"all\") {\n            if (employeeCountFilter === \"1-50\" && employer.employees > 50) return false;\n            if (employeeCountFilter === \"51-100\" && (employer.employees < 51 || employer.employees > 100)) return false;\n            if (employeeCountFilter === \"101+\" && employer.employees <= 100) return false;\n        }\n        // Risk score filter\n        if (riskScoreFilter !== \"all\") {\n            if (riskScoreFilter === \"low\" && employer.riskScore >= 5) return false;\n            if (riskScoreFilter === \"medium\" && (employer.riskScore < 5 || employer.riskScore > 7)) return false;\n            if (riskScoreFilter === \"high\" && employer.riskScore <= 7) return false;\n        }\n        // Suggested plan filter\n        if (suggestedPlanFilter !== \"all\") {\n            if (suggestedPlanFilter === \"ppo-hsa\" && employer.planType !== \"PPO + HSA Combo\") return false;\n            if (suggestedPlanFilter === \"traditional-ppo\" && employer.planType !== \"Traditional PPO\") return false;\n            if (suggestedPlanFilter === \"other\" && employer.planType !== \"HSA Only\" && employer.planType !== \"Modern HSA Plus Plan\") return false;\n        }\n        return true;\n    });\n    const handleShareInsight = (companyName)=>{\n        const shareUrl = \"\".concat(window.location.origin, \"/shared-insight/\").concat(companyName.toLowerCase().replace(/\\s+/g, \"-\"));\n        navigator.clipboard.writeText(shareUrl);\n        console.log(\"Share link copied for \".concat(companyName));\n    };\n    const handleTileClick = (employerId, companyName)=>{\n        console.log(\"Attempting to view report for \".concat(companyName, \" (ID: \").concat(employerId, \")\"));\n        console.log(\"Can view report: \".concat(canViewReport(employerId)));\n        console.log(\"Reports remaining: \".concat(reportsRemaining));\n        console.log(\"Is at limit: \".concat(isAtLimit));\n        if (!canViewReport(employerId)) {\n            toast({\n                title: \"Upgrade Required\",\n                description: \"You've reached your free report limit (2 reports). Upgrade to Pro for unlimited access.\",\n                variant: \"destructive\"\n            });\n            navigate(\"/pricing\");\n            return;\n        }\n        // Track the report view\n        trackReportView(employerId);\n        navigate(\"?page=employer-insight/\".concat(employerId));\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-slate-50 to-blue-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"border-b bg-white/90 backdrop-blur-sm sticky top-0 z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto px-3 sm:px-4 py-3\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-xl sm:text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent\",\n                                children: \"BenOsphere\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                lineNumber: 131,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2 sm:space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AskBrea__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        context: \"broker dashboard with multiple client insights\",\n                                        size: \"sm\",\n                                        variant: \"outline\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                        lineNumber: 136,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ProfileHandler__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: \"hidden sm:inline-flex\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                        lineNumber: 137,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                lineNumber: 134,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                        lineNumber: 130,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                    lineNumber: 129,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                lineNumber: 128,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"container mx-auto px-3 sm:px-4 py-4 sm:py-6 max-w-7xl\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col lg:flex-row lg:justify-between lg:items-start gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-2xl sm:text-3xl font-bold text-gray-900 mb-2\",\n                                                children: \"\\uD83D\\uDCCA Broker Dashboard\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                lineNumber: 148,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600 text-sm sm:text-base mb-4 lg:mb-0\",\n                                                children: \"Manage and analyze your client census data\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                lineNumber: 151,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                        lineNumber: 147,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col sm:flex-row gap-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                size: \"sm\",\n                                                onClick: ()=>navigate(\"/upload-census\"),\n                                                className: \"bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_DollarSign_Lock_Mail_Plus_Search_Share2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        className: \"h-4 w-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                        lineNumber: 163,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    \"Upload New Census\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                lineNumber: 158,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                variant: \"outline\",\n                                                size: \"sm\",\n                                                onClick: ()=>navigate(\"/employer-invite\"),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_DollarSign_Lock_Mail_Plus_Search_Share2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        className: \"h-4 w-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                        lineNumber: 167,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    \"Invite Employer\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                lineNumber: 166,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                        lineNumber: 157,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                lineNumber: 146,\n                                columnNumber: 11\n                            }, undefined),\n                            reportsRemaining <= 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                className: \"mt-4 bg-gradient-to-r from-amber-50 to-orange-50 border-amber-200\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    className: \"p-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-start space-x-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_DollarSign_Lock_Mail_Plus_Search_Share2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                        className: \"h-5 w-5 text-amber-600 mt-0.5 flex-shrink-0\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                        lineNumber: 179,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"font-medium text-amber-800\",\n                                                                children: isAtLimit ? \"Report limit reached!\" : \"\".concat(reportsRemaining, \" free report\").concat(reportsRemaining === 1 ? \"\" : \"s\", \" remaining\")\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                lineNumber: 181,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-amber-700\",\n                                                                children: isAtLimit ? \"Upgrade to Pro for unlimited reports\" : \"Upgrade to Pro for unlimited access\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                lineNumber: 184,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                        lineNumber: 180,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                lineNumber: 178,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                onClick: ()=>navigate(\"/pricing\"),\n                                                className: \"bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white flex-shrink-0\",\n                                                size: \"sm\",\n                                                children: \"Upgrade Now\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                lineNumber: 189,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                        lineNumber: 177,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                    lineNumber: 176,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                lineNumber: 175,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                        lineNumber: 145,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        className: \"mb-6 shadow-lg border-0\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                            className: \"p-4 sm:p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_DollarSign_Lock_Mail_Plus_Search_Share2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                className: \"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                lineNumber: 208,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                placeholder: \"Search by Client Name\",\n                                                value: searchTerm,\n                                                onChange: (e)=>setSearchTerm(e.target.value),\n                                                className: \"pl-10\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                lineNumber: 209,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                        lineNumber: 207,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 sm:grid-cols-3 gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"text-sm font-medium text-gray-700 mb-2 block\",\n                                                        children: \"Employee Count\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                        lineNumber: 220,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.Select, {\n                                                        value: employeeCountFilter,\n                                                        onValueChange: setEmployeeCountFilter,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectTrigger, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectValue, {\n                                                                    placeholder: \"All\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                    lineNumber: 223,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                lineNumber: 222,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectContent, {\n                                                                className: \"bg-white\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                                        value: \"all\",\n                                                                        children: \"All\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                        lineNumber: 226,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                                        value: \"1-50\",\n                                                                        children: \"1–50\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                        lineNumber: 227,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                                        value: \"51-100\",\n                                                                        children: \"51–100\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                        lineNumber: 228,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                                        value: \"101+\",\n                                                                        children: \"101+\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                        lineNumber: 229,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                lineNumber: 225,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                        lineNumber: 221,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                lineNumber: 219,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"text-sm font-medium text-gray-700 mb-2 block\",\n                                                        children: \"Risk Score\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                        lineNumber: 235,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.Select, {\n                                                        value: riskScoreFilter,\n                                                        onValueChange: setRiskScoreFilter,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectTrigger, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectValue, {\n                                                                    placeholder: \"All\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                    lineNumber: 238,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                lineNumber: 237,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectContent, {\n                                                                className: \"bg-white\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                                        value: \"all\",\n                                                                        children: \"All\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                        lineNumber: 241,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                                        value: \"low\",\n                                                                        children: \"Low (<5)\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                        lineNumber: 242,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                                        value: \"medium\",\n                                                                        children: \"Medium (5–7)\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                        lineNumber: 243,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                                        value: \"high\",\n                                                                        children: \"High (>7)\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                        lineNumber: 244,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                lineNumber: 240,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                        lineNumber: 236,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                lineNumber: 234,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"text-sm font-medium text-gray-700 mb-2 block\",\n                                                        children: \"Suggested Plan\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                        lineNumber: 250,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.Select, {\n                                                        value: suggestedPlanFilter,\n                                                        onValueChange: setSuggestedPlanFilter,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectTrigger, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectValue, {\n                                                                    placeholder: \"All\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                    lineNumber: 253,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                lineNumber: 252,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectContent, {\n                                                                className: \"bg-white\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                                        value: \"all\",\n                                                                        children: \"All\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                        lineNumber: 256,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                                        value: \"ppo-hsa\",\n                                                                        children: \"PPO + HSA Combo\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                        lineNumber: 257,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                                        value: \"traditional-ppo\",\n                                                                        children: \"Traditional PPO\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                        lineNumber: 258,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                                        value: \"other\",\n                                                                        children: \"Other\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                        lineNumber: 259,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                lineNumber: 255,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                        lineNumber: 251,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                lineNumber: 249,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                        lineNumber: 218,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                lineNumber: 205,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                            lineNumber: 204,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                        lineNumber: 203,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4 mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                className: \"bg-gradient-to-br from-blue-100 to-blue-200 border-blue-300 hover:shadow-md transition-all duration-200\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    className: \"p-3 sm:p-4 text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_DollarSign_Lock_Mail_Plus_Search_Share2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            className: \"h-6 w-6 sm:h-8 sm:w-8 mx-auto mb-1 sm:mb-2 text-blue-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                            lineNumber: 272,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs sm:text-sm text-blue-600 font-medium\",\n                                            children: \"Total Clients\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                            lineNumber: 273,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xl sm:text-2xl font-bold text-blue-900\",\n                                            children: filteredEmployers.length\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                            lineNumber: 274,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                    lineNumber: 271,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                lineNumber: 270,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                className: \"bg-gradient-to-br from-emerald-100 to-teal-200 border-emerald-300 hover:shadow-md transition-all duration-200\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    className: \"p-3 sm:p-4 text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_DollarSign_Lock_Mail_Plus_Search_Share2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            className: \"h-6 w-6 sm:h-8 sm:w-8 mx-auto mb-1 sm:mb-2 text-emerald-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                            lineNumber: 280,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs sm:text-sm text-emerald-600 font-medium\",\n                                            children: \"Total Employees\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                            lineNumber: 281,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xl sm:text-2xl font-bold text-emerald-900\",\n                                            children: filteredEmployers.reduce((sum, emp)=>sum + emp.employees, 0)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                            lineNumber: 282,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                    lineNumber: 279,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                lineNumber: 278,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                className: \"bg-gradient-to-br from-orange-100 to-red-200 border-orange-300 hover:shadow-md transition-all duration-200\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    className: \"p-3 sm:p-4 text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_DollarSign_Lock_Mail_Plus_Search_Share2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            className: \"h-6 w-6 sm:h-8 sm:w-8 mx-auto mb-1 sm:mb-2 text-orange-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                            lineNumber: 290,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs sm:text-sm text-orange-600 font-medium\",\n                                            children: \"Total Potential Savings\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                            lineNumber: 291,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-lg sm:text-2xl font-bold text-orange-900\",\n                                            children: \"$404,900\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                            lineNumber: 292,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                    lineNumber: 289,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                lineNumber: 288,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                className: \"bg-gradient-to-br from-purple-100 to-pink-200 border-purple-300 hover:shadow-md transition-all duration-200\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    className: \"p-3 sm:p-4 text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_DollarSign_Lock_Mail_Plus_Search_Share2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            className: \"h-6 w-6 sm:h-8 sm:w-8 mx-auto mb-1 sm:mb-2 text-purple-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                            lineNumber: 298,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs sm:text-sm text-purple-600 font-medium\",\n                                            children: \"Avg Risk Score\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                            lineNumber: 299,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xl sm:text-2xl font-bold text-purple-900\",\n                                            children: \"5.9/10\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                            lineNumber: 300,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                    lineNumber: 297,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                lineNumber: 296,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                        lineNumber: 269,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        className: \"shadow-lg border-0\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                className: \"pb-3 sm:pb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                    className: \"text-lg sm:text-xl\",\n                                    children: \"Client Census Insights\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                    lineNumber: 308,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                lineNumber: 307,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                className: \"p-3 sm:p-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3 sm:space-y-4\",\n                                    children: filteredEmployers.map((employer)=>{\n                                        const canAccess = canViewReport(employer.id);\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                            className: \"border-l-4 border-l-blue-500 transition-all duration-200 cursor-pointer \".concat(canAccess ? \"hover:shadow-md hover:-translate-y-0.5\" : \"opacity-60 hover:opacity-80 cursor-not-allowed\", \" \").concat(!canAccess ? \"relative\" : \"\"),\n                                            onClick: ()=>handleTileClick(employer.id, employer.companyName),\n                                            children: [\n                                                !canAccess && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute inset-0 bg-gray-200/50 backdrop-blur-[1px] rounded-lg flex items-center justify-center z-10\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-white p-3 rounded-lg shadow-lg flex items-center space-x-2 border border-amber-200\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_DollarSign_Lock_Mail_Plus_Search_Share2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                className: \"h-4 w-4 text-amber-600\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                lineNumber: 328,\n                                                                columnNumber: 27\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm font-medium text-amber-800\",\n                                                                children: \"Upgrade to view\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                lineNumber: 329,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                        lineNumber: 327,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                    lineNumber: 326,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                                    className: \"p-4 sm:p-5\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex flex-col sm:flex-row justify-between items-start mb-3 sm:mb-4 gap-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                            className: \"text-lg sm:text-xl font-semibold text-gray-900 mb-1\",\n                                                                            children: employer.companyName\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                            lineNumber: 337,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-xs sm:text-sm text-gray-500\",\n                                                                            children: [\n                                                                                \"Uploaded: \",\n                                                                                new Date(employer.uploadDate).toLocaleDateString()\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                            lineNumber: 340,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                    lineNumber: 336,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex space-x-2 w-full sm:w-auto\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                        size: \"sm\",\n                                                                        variant: \"outline\",\n                                                                        onClick: (e)=>{\n                                                                            e.stopPropagation();\n                                                                            if (canAccess) {\n                                                                                handleShareInsight(employer.companyName);\n                                                                            } else {\n                                                                                navigate(\"/pricing\");\n                                                                            }\n                                                                        },\n                                                                        className: \"flex-1 sm:flex-none\",\n                                                                        disabled: !canAccess,\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_DollarSign_Lock_Mail_Plus_Search_Share2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                                className: \"h-3 w-3 sm:h-4 sm:w-4 sm:mr-1\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                                lineNumber: 359,\n                                                                                columnNumber: 29\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"hidden sm:inline\",\n                                                                                children: \"Share\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                                lineNumber: 360,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                        lineNumber: 345,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                    lineNumber: 344,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                            lineNumber: 335,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"grid grid-cols-2 lg:grid-cols-4 gap-2 sm:gap-3 mb-3 sm:mb-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-center p-2 sm:p-3 bg-gradient-to-br from-blue-50 to-blue-100 rounded-lg hover:scale-105 transition-transform duration-200\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-xs sm:text-sm text-blue-600 font-medium\",\n                                                                            children: \"Employees\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                            lineNumber: 367,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm sm:text-lg font-bold text-blue-900\",\n                                                                            children: employer.employees\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                            lineNumber: 368,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                    lineNumber: 366,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-center p-2 sm:p-3 bg-gradient-to-br from-emerald-50 to-emerald-100 rounded-lg hover:scale-105 transition-transform duration-200\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-xs sm:text-sm text-emerald-600 font-medium\",\n                                                                            children: \"Avg Age\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                            lineNumber: 371,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm sm:text-lg font-bold text-emerald-900\",\n                                                                            children: employer.averageAge\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                            lineNumber: 372,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                    lineNumber: 370,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-center p-2 sm:p-3 bg-gradient-to-br from-orange-50 to-orange-100 rounded-lg hover:scale-105 transition-transform duration-200\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-xs sm:text-sm text-orange-600 font-medium\",\n                                                                            children: \"Savings\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                            lineNumber: 375,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm sm:text-lg font-bold text-orange-900\",\n                                                                            children: employer.potentialSavings\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                            lineNumber: 376,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                    lineNumber: 374,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-center p-2 sm:p-3 bg-gradient-to-br from-purple-50 to-purple-100 rounded-lg hover:scale-105 transition-transform duration-200\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-xs sm:text-sm text-purple-600 font-medium\",\n                                                                            children: \"Risk Score\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                            lineNumber: 379,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm sm:text-lg font-bold text-purple-900\",\n                                                                            children: [\n                                                                                employer.riskScore,\n                                                                                \"/10\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                            lineNumber: 380,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                    lineNumber: 378,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                            lineNumber: 365,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex flex-col sm:flex-row justify-between items-start sm:items-center gap-2 sm:gap-0\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-sm\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-gray-600\",\n                                                                            children: \"Suggested Plan: \"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                            lineNumber: 386,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"font-medium text-gray-900\",\n                                                                            children: employer.planType\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                            lineNumber: 387,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                    lineNumber: 385,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"px-2 py-1 rounded-full text-xs font-medium \".concat(employer.status === \"analyzed\" ? \"bg-green-100 text-green-800\" : \"bg-yellow-100 text-yellow-800\"),\n                                                                    children: employer.status === \"analyzed\" ? \"✅ Analyzed\" : \"⏳ Processing\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                                    lineNumber: 389,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                            lineNumber: 384,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                    lineNumber: 334,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, employer.id, true, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                            lineNumber: 316,\n                                            columnNumber: 19\n                                        }, undefined);\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                    lineNumber: 311,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                lineNumber: 310,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                        lineNumber: 306,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        className: \"mt-6 bg-gradient-to-r from-purple-100 to-blue-100 border-0 shadow-lg\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                            className: \"p-4 sm:p-6 text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg sm:text-xl font-bold text-gray-900 mb-2\",\n                                    children: \"\\uD83D\\uDE80 Grow Your Network\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                    lineNumber: 408,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-700 mb-4 text-sm sm:text-base\",\n                                    children: \"Share BenOsphere with other brokers and get rewards for every signup\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                    lineNumber: 411,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col sm:flex-row justify-center space-y-2 sm:space-y-0 sm:space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            className: \"bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white transition-all duration-200 hover:scale-105\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_DollarSign_Lock_Mail_Plus_Search_Share2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                    className: \"h-4 w-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                                    lineNumber: 416,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                \"Refer a Broker\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                            lineNumber: 415,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            variant: \"outline\",\n                                            className: \"hover:bg-white/50\",\n                                            children: \"View Referral Rewards\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                            lineNumber: 419,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                                    lineNumber: 414,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                            lineNumber: 407,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                        lineNumber: 406,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n                lineNumber: 143,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\public\\\\BrokerDashboard.tsx\",\n        lineNumber: 126,\n        columnNumber: 5\n    }, undefined);\n};\n_s(BrokerDashboard, \"DEDUFktEDRsQ+usPY8Njz1Zt/9k=\", false, function() {\n    return [\n        _lib_react_router_dom__WEBPACK_IMPORTED_MODULE_6__.useNavigate,\n        _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_10__.useToast,\n        _hooks_usePlanRestrictions__WEBPACK_IMPORTED_MODULE_9__.usePlanRestrictions\n    ];\n});\n_c = BrokerDashboard;\n/* harmony default export */ __webpack_exports__[\"default\"] = (BrokerDashboard);\nvar _c;\n$RefreshReg$(_c, \"BrokerDashboard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/census/public/BrokerDashboard.tsx\n"));

/***/ })

});