"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/census/page",{

/***/ "(app-pages-browser)/./src/app/census/components/AskBrea.tsx":
/*!***********************************************!*\
  !*** ./src/app/census/components/AskBrea.tsx ***!
  \***********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ui/button */ \"(app-pages-browser)/./src/app/census/components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_MessageCircle_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=MessageCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-circle.js\");\n/* harmony import */ var _ai_enroller_employee_enrol_components_ChatModal__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../ai-enroller/employee-enrol/components/ChatModal */ \"(app-pages-browser)/./src/app/ai-enroller/employee-enrol/components/ChatModal.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\nconst AskBrea = (param)=>{\n    let { context = \"\", size = \"default\", variant = \"default\" } = param;\n    _s();\n    const [isOpen, setIsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                size: size,\n                variant: variant,\n                className: \"flex items-center gap-2\",\n                onClick: ()=>setIsOpen(true),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MessageCircle_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        className: \"h-4 w-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\components\\\\AskBrea.tsx\",\n                        lineNumber: 23,\n                        columnNumber: 9\n                    }, undefined),\n                    \"Ask Brea\"\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\components\\\\AskBrea.tsx\",\n                lineNumber: 17,\n                columnNumber: 7\n            }, undefined),\n            isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    position: \"fixed\",\n                    top: 0,\n                    left: 0,\n                    right: 0,\n                    bottom: 0,\n                    zIndex: 99999,\n                    pointerEvents: \"auto\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ai_enroller_employee_enrol_components_ChatModal__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    isOpen: isOpen,\n                    onClose: ()=>setIsOpen(false)\n                }, void 0, false, {\n                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\components\\\\AskBrea.tsx\",\n                    lineNumber: 37,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\census\\\\components\\\\AskBrea.tsx\",\n                lineNumber: 28,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n_s(AskBrea, \"+sus0Lb0ewKHdwiUhiTAJFoFyQ0=\");\n_c = AskBrea;\n/* harmony default export */ __webpack_exports__[\"default\"] = (AskBrea);\nvar _c;\n$RefreshReg$(_c, \"AskBrea\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/census/components/AskBrea.tsx\n"));

/***/ })

});