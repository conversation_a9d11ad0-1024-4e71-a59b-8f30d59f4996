"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/app/dashboard/CensusCard.tsx":
/*!******************************************!*\
  !*** ./src/app/dashboard/CensusCard.tsx ***!
  \******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ CensusCard; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Box/Box.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Avatar/Avatar.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Typography/Typography.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chart-column.js\");\n/* harmony import */ var _redux_hooks__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/redux/hooks */ \"(app-pages-browser)/./src/redux/hooks.ts\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction CensusCard() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const userDetails = (0,_redux_hooks__WEBPACK_IMPORTED_MODULE_3__.useAppSelector)((state)=>state.user.userProfile);\n    const handleClick = ()=>{\n        // Check if user is logged in using consistent auth check\n        const primaryKey = \"userid1\" || 0;\n        const altKey = \"userId\" || 0;\n        const userId = localStorage.getItem(primaryKey) || localStorage.getItem(altKey);\n        const ssoDone = localStorage.getItem(\"ssoDone1\");\n        const userEmail = localStorage.getItem(\"userEmail1\");\n        // Debug logging\n        console.log(\"Census Auth Check:\", {\n            userId,\n            ssoDone,\n            userEmail,\n            userDetailsName: userDetails === null || userDetails === void 0 ? void 0 : userDetails.name,\n            userDetailsEmail: userDetails === null || userDetails === void 0 ? void 0 : userDetails.email\n        });\n        // Check multiple conditions for authentication\n        const isAuthenticated = userId && (ssoDone === \"true\" || (userDetails === null || userDetails === void 0 ? void 0 : userDetails.name) || (userDetails === null || userDetails === void 0 ? void 0 : userDetails.email) || userEmail);\n        if (isAuthenticated) {\n            console.log(\"User authenticated, going to upload census\");\n            router.push(\"/census?page=upload-census\");\n        } else {\n            console.log(\"User not authenticated, going to login prompt\");\n            router.push(\"/census?page=login-prompt\");\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n        onClick: handleClick,\n        sx: {\n            backgroundColor: \"white\",\n            padding: 2,\n            display: \"flex\",\n            alignItems: \"center\",\n            justifyContent: \"space-between\",\n            borderRadius: \"30px\",\n            boxShadow: \"none\",\n            maxWidth: \"100%\",\n            mt: 3,\n            cursor: \"pointer\",\n            transition: \"all 0.2s ease\",\n            \"&:hover\": {\n                boxShadow: \"0 4px 12px rgba(0, 0, 0, 0.1)\",\n                transform: \"translateY(-2px)\"\n            }\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n            sx: {\n                display: \"flex\",\n                alignItems: \"center\",\n                flexDirection: \"row\"\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    sx: {\n                        width: 50,\n                        height: 50,\n                        mr: 2,\n                        backgroundColor: \"#2563eb\",\n                        background: \"linear-gradient(135deg, #2563eb 0%, #8b5cf6 100%)\"\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        size: 28,\n                        color: \"white\",\n                        style: {\n                            filter: \"drop-shadow(0 0 2px rgba(255,255,255,0.3))\"\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\dashboard\\\\CensusCard.tsx\",\n                        lineNumber: 80,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\dashboard\\\\CensusCard.tsx\",\n                    lineNumber: 71,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            sx: {\n                                display: \"flex\",\n                                alignItems: \"center\",\n                                flexDirection: \"row\"\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                sx: {\n                                    fontWeight: 700,\n                                    fontSize: \"24px\",\n                                    display: \"flex\",\n                                    alignItems: \"center\"\n                                },\n                                children: \"Census\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\dashboard\\\\CensusCard.tsx\",\n                                lineNumber: 87,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\dashboard\\\\CensusCard.tsx\",\n                            lineNumber: 83,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            sx: {\n                                fontWeight: 500,\n                                fontSize: \"14px\",\n                                color: \"#6c757d\"\n                            },\n                            children: \"Upload and analyze employee census data\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\dashboard\\\\CensusCard.tsx\",\n                            lineNumber: 98,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\dashboard\\\\CensusCard.tsx\",\n                    lineNumber: 82,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\dashboard\\\\CensusCard.tsx\",\n            lineNumber: 70,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\beno\\\\project_dev\\\\qharmony-frontend-v1\\\\src\\\\app\\\\dashboard\\\\CensusCard.tsx\",\n        lineNumber: 49,\n        columnNumber: 5\n    }, this);\n}\n_s(CensusCard, \"gmu74BIMFjK5LzjGX5kEF6WxiZ8=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _redux_hooks__WEBPACK_IMPORTED_MODULE_3__.useAppSelector\n    ];\n});\n_c = CensusCard;\nvar _c;\n$RefreshReg$(_c, \"CensusCard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvZGFzaGJvYXJkL0NlbnN1c0NhcmQudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7OztBQUEwQjtBQUM4QjtBQUNaO0FBQ0g7QUFDTTtBQUdoQyxTQUFTTzs7SUFDdEIsTUFBTUMsU0FBU0osMERBQVNBO0lBQ3hCLE1BQU1LLGNBQWNILDREQUFjQSxDQUNoQyxDQUFDSSxRQUFxQkEsTUFBTUMsSUFBSSxDQUFDQyxXQUFXO0lBRzlDLE1BQU1DLGNBQWM7UUFDbEIseURBQXlEO1FBQ3pELE1BQU1DLGFBQWFDLFNBQW1DLElBQUksQ0FBUztRQUNuRSxNQUFNRyxTQUFTSCxRQUF1QyxJQUFJLENBQVE7UUFDbEUsTUFBTUssU0FBU0MsYUFBYUMsT0FBTyxDQUFDUixlQUFlTyxhQUFhQyxPQUFPLENBQUNKO1FBQ3hFLE1BQU1LLFVBQVVGLGFBQWFDLE9BQU8sQ0FBQztRQUNyQyxNQUFNRSxZQUFZSCxhQUFhQyxPQUFPLENBQUM7UUFFdkMsZ0JBQWdCO1FBQ2hCRyxRQUFRQyxHQUFHLENBQUMsc0JBQXNCO1lBQ2hDTjtZQUNBRztZQUNBQztZQUNBRyxlQUFlLEVBQUVsQix3QkFBQUEsa0NBQUFBLFlBQWFtQixJQUFJO1lBQ2xDQyxnQkFBZ0IsRUFBRXBCLHdCQUFBQSxrQ0FBQUEsWUFBYXFCLEtBQUs7UUFDdEM7UUFFQSwrQ0FBK0M7UUFDL0MsTUFBTUMsa0JBQWtCWCxVQUN0QkcsQ0FBQUEsWUFBWSxXQUNaZCx3QkFBQUEsa0NBQUFBLFlBQWFtQixJQUFJLE1BQ2pCbkIsd0JBQUFBLGtDQUFBQSxZQUFhcUIsS0FBSyxLQUNsQk4sU0FBUTtRQUdWLElBQUlPLGlCQUFpQjtZQUNuQk4sUUFBUUMsR0FBRyxDQUFDO1lBQ1psQixPQUFPd0IsSUFBSSxDQUFDO1FBQ2QsT0FBTztZQUNMUCxRQUFRQyxHQUFHLENBQUM7WUFDWmxCLE9BQU93QixJQUFJLENBQUM7UUFDZDtJQUNGO0lBRUEscUJBQ0UsOERBQUMvQixpR0FBR0E7UUFDRmdDLFNBQVNwQjtRQUNUcUIsSUFBSTtZQUNGQyxpQkFBaUI7WUFDakJDLFNBQVM7WUFDVEMsU0FBUztZQUNUQyxZQUFZO1lBQ1pDLGdCQUFnQjtZQUNoQkMsY0FBYztZQUNkQyxXQUFXO1lBQ1hDLFVBQVU7WUFDVkMsSUFBSTtZQUNKQyxRQUFRO1lBQ1JDLFlBQVk7WUFDWixXQUFXO2dCQUNUSixXQUFXO2dCQUNYSyxXQUFXO1lBQ2I7UUFDRjtrQkFHQSw0RUFBQzdDLGlHQUFHQTtZQUFDaUMsSUFBSTtnQkFBRUcsU0FBUztnQkFBUUMsWUFBWTtnQkFBVVMsZUFBZTtZQUFNOzs4QkFDckUsOERBQUM1QyxpR0FBTUE7b0JBQ0wrQixJQUFJO3dCQUNGYyxPQUFPO3dCQUNQQyxRQUFRO3dCQUNSQyxJQUFJO3dCQUNKZixpQkFBaUI7d0JBQ2pCZ0IsWUFBWTtvQkFDZDs4QkFFQSw0RUFBQzlDLHFGQUFTQTt3QkFBQytDLE1BQU07d0JBQUlDLE9BQU07d0JBQVFDLE9BQU87NEJBQUVDLFFBQVE7d0JBQTZDOzs7Ozs7Ozs7Ozs4QkFFbkcsOERBQUN0RCxpR0FBR0E7O3NDQUNGLDhEQUFDQSxpR0FBR0E7NEJBQ0ZpQyxJQUFJO2dDQUFFRyxTQUFTO2dDQUFRQyxZQUFZO2dDQUFVUyxlQUFlOzRCQUFNO3NDQUdsRSw0RUFBQzdDLGlHQUFVQTtnQ0FDVGdDLElBQUk7b0NBQ0ZzQixZQUFZO29DQUNaQyxVQUFVO29DQUNWcEIsU0FBUztvQ0FDVEMsWUFBWTtnQ0FDZDswQ0FDRDs7Ozs7Ozs7Ozs7c0NBSUgsOERBQUNwQyxpR0FBVUE7NEJBQ1RnQyxJQUFJO2dDQUNGc0IsWUFBWTtnQ0FDWkMsVUFBVTtnQ0FDVkosT0FBTzs0QkFDVDtzQ0FDRDs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFPWDtHQXZHd0I5Qzs7UUFDUEgsc0RBQVNBO1FBQ0pFLHdEQUFjQTs7O0tBRlpDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3NyYy9hcHAvZGFzaGJvYXJkL0NlbnN1c0NhcmQudHN4P2FmYjEiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IFJlYWN0IGZyb20gXCJyZWFjdFwiO1xuaW1wb3J0IHsgQm94LCBUeXBvZ3JhcGh5LCBBdmF0YXIgfSBmcm9tIFwiQG11aS9tYXRlcmlhbFwiO1xuaW1wb3J0IHsgdXNlUm91dGVyIH0gZnJvbSBcIm5leHQvbmF2aWdhdGlvblwiO1xuaW1wb3J0IHsgQmFyQ2hhcnQzIH0gZnJvbSBcImx1Y2lkZS1yZWFjdFwiO1xuaW1wb3J0IHsgdXNlQXBwU2VsZWN0b3IgfSBmcm9tIFwiQC9yZWR1eC9ob29rc1wiO1xuaW1wb3J0IHsgUm9vdFN0YXRlIH0gZnJvbSBcIkAvcmVkdXgvc3RvcmVcIjtcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gQ2Vuc3VzQ2FyZCgpIHtcbiAgY29uc3Qgcm91dGVyID0gdXNlUm91dGVyKCk7XG4gIGNvbnN0IHVzZXJEZXRhaWxzID0gdXNlQXBwU2VsZWN0b3IoXG4gICAgKHN0YXRlOiBSb290U3RhdGUpID0+IHN0YXRlLnVzZXIudXNlclByb2ZpbGUsXG4gICk7XG5cbiAgY29uc3QgaGFuZGxlQ2xpY2sgPSAoKSA9PiB7XG4gICAgLy8gQ2hlY2sgaWYgdXNlciBpcyBsb2dnZWQgaW4gdXNpbmcgY29uc2lzdGVudCBhdXRoIGNoZWNrXG4gICAgY29uc3QgcHJpbWFyeUtleSA9IHByb2Nlc3MuZW52Lk5FWFRfUFVCTElDX1VTRVJfSURfS0VZIHx8IFwidXNlcmlkMVwiO1xuICAgIGNvbnN0IGFsdEtleSA9IHByb2Nlc3MuZW52Lk5FWFRfUFVCTElDX1VTRVJfSURfQUxUX0tFWSB8fCBcInVzZXJJZFwiO1xuICAgIGNvbnN0IHVzZXJJZCA9IGxvY2FsU3RvcmFnZS5nZXRJdGVtKHByaW1hcnlLZXkpIHx8IGxvY2FsU3RvcmFnZS5nZXRJdGVtKGFsdEtleSk7XG4gICAgY29uc3Qgc3NvRG9uZSA9IGxvY2FsU3RvcmFnZS5nZXRJdGVtKFwic3NvRG9uZTFcIik7XG4gICAgY29uc3QgdXNlckVtYWlsID0gbG9jYWxTdG9yYWdlLmdldEl0ZW0oXCJ1c2VyRW1haWwxXCIpO1xuXG4gICAgLy8gRGVidWcgbG9nZ2luZ1xuICAgIGNvbnNvbGUubG9nKFwiQ2Vuc3VzIEF1dGggQ2hlY2s6XCIsIHtcbiAgICAgIHVzZXJJZCxcbiAgICAgIHNzb0RvbmUsXG4gICAgICB1c2VyRW1haWwsXG4gICAgICB1c2VyRGV0YWlsc05hbWU6IHVzZXJEZXRhaWxzPy5uYW1lLFxuICAgICAgdXNlckRldGFpbHNFbWFpbDogdXNlckRldGFpbHM/LmVtYWlsXG4gICAgfSk7XG5cbiAgICAvLyBDaGVjayBtdWx0aXBsZSBjb25kaXRpb25zIGZvciBhdXRoZW50aWNhdGlvblxuICAgIGNvbnN0IGlzQXV0aGVudGljYXRlZCA9IHVzZXJJZCAmJiAoXG4gICAgICBzc29Eb25lID09PSBcInRydWVcIiB8fFxuICAgICAgdXNlckRldGFpbHM/Lm5hbWUgfHxcbiAgICAgIHVzZXJEZXRhaWxzPy5lbWFpbCB8fFxuICAgICAgdXNlckVtYWlsXG4gICAgKTtcblxuICAgIGlmIChpc0F1dGhlbnRpY2F0ZWQpIHtcbiAgICAgIGNvbnNvbGUubG9nKFwiVXNlciBhdXRoZW50aWNhdGVkLCBnb2luZyB0byB1cGxvYWQgY2Vuc3VzXCIpO1xuICAgICAgcm91dGVyLnB1c2goJy9jZW5zdXM/cGFnZT11cGxvYWQtY2Vuc3VzJyk7XG4gICAgfSBlbHNlIHtcbiAgICAgIGNvbnNvbGUubG9nKFwiVXNlciBub3QgYXV0aGVudGljYXRlZCwgZ29pbmcgdG8gbG9naW4gcHJvbXB0XCIpO1xuICAgICAgcm91dGVyLnB1c2goJy9jZW5zdXM/cGFnZT1sb2dpbi1wcm9tcHQnKTtcbiAgICB9XG4gIH07XG5cbiAgcmV0dXJuIChcbiAgICA8Qm94XG4gICAgICBvbkNsaWNrPXtoYW5kbGVDbGlja31cbiAgICAgIHN4PXt7XG4gICAgICAgIGJhY2tncm91bmRDb2xvcjogXCJ3aGl0ZVwiLFxuICAgICAgICBwYWRkaW5nOiAyLFxuICAgICAgICBkaXNwbGF5OiBcImZsZXhcIixcbiAgICAgICAgYWxpZ25JdGVtczogXCJjZW50ZXJcIixcbiAgICAgICAganVzdGlmeUNvbnRlbnQ6IFwic3BhY2UtYmV0d2VlblwiLFxuICAgICAgICBib3JkZXJSYWRpdXM6IFwiMzBweFwiLFxuICAgICAgICBib3hTaGFkb3c6IFwibm9uZVwiLFxuICAgICAgICBtYXhXaWR0aDogXCIxMDAlXCIsXG4gICAgICAgIG10OiAzLFxuICAgICAgICBjdXJzb3I6IFwicG9pbnRlclwiLFxuICAgICAgICB0cmFuc2l0aW9uOiBcImFsbCAwLjJzIGVhc2VcIixcbiAgICAgICAgXCImOmhvdmVyXCI6IHtcbiAgICAgICAgICBib3hTaGFkb3c6IFwiMCA0cHggMTJweCByZ2JhKDAsIDAsIDAsIDAuMSlcIixcbiAgICAgICAgICB0cmFuc2Zvcm06IFwidHJhbnNsYXRlWSgtMnB4KVwiLFxuICAgICAgICB9LFxuICAgICAgfX1cbiAgICA+XG4gICAgICB7LyogTGVmdCBzaWRlIHdpdGggQXZhdGFyICovfVxuICAgICAgPEJveCBzeD17eyBkaXNwbGF5OiBcImZsZXhcIiwgYWxpZ25JdGVtczogXCJjZW50ZXJcIiwgZmxleERpcmVjdGlvbjogXCJyb3dcIiB9fT5cbiAgICAgICAgPEF2YXRhclxuICAgICAgICAgIHN4PXt7XG4gICAgICAgICAgICB3aWR0aDogNTAsXG4gICAgICAgICAgICBoZWlnaHQ6IDUwLFxuICAgICAgICAgICAgbXI6IDIsXG4gICAgICAgICAgICBiYWNrZ3JvdW5kQ29sb3I6IFwiIzI1NjNlYlwiLFxuICAgICAgICAgICAgYmFja2dyb3VuZDogXCJsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjMjU2M2ViIDAlLCAjOGI1Y2Y2IDEwMCUpXCIsXG4gICAgICAgICAgfX1cbiAgICAgICAgPlxuICAgICAgICAgIDxCYXJDaGFydDMgc2l6ZT17Mjh9IGNvbG9yPVwid2hpdGVcIiBzdHlsZT17eyBmaWx0ZXI6ICdkcm9wLXNoYWRvdygwIDAgMnB4IHJnYmEoMjU1LDI1NSwyNTUsMC4zKSknIH19IC8+XG4gICAgICAgIDwvQXZhdGFyPlxuICAgICAgICA8Qm94PlxuICAgICAgICAgIDxCb3hcbiAgICAgICAgICAgIHN4PXt7IGRpc3BsYXk6IFwiZmxleFwiLCBhbGlnbkl0ZW1zOiBcImNlbnRlclwiLCBmbGV4RGlyZWN0aW9uOiBcInJvd1wiIH19XG4gICAgICAgICAgPlxuICAgICAgICAgICAgey8qIENlbnN1cyBUZXh0ICovfVxuICAgICAgICAgICAgPFR5cG9ncmFwaHlcbiAgICAgICAgICAgICAgc3g9e3tcbiAgICAgICAgICAgICAgICBmb250V2VpZ2h0OiA3MDAsXG4gICAgICAgICAgICAgICAgZm9udFNpemU6IFwiMjRweFwiLFxuICAgICAgICAgICAgICAgIGRpc3BsYXk6IFwiZmxleFwiLFxuICAgICAgICAgICAgICAgIGFsaWduSXRlbXM6IFwiY2VudGVyXCIsXG4gICAgICAgICAgICAgIH19XG4gICAgICAgICAgICA+XG4gICAgICAgICAgICAgIENlbnN1c1xuICAgICAgICAgICAgPC9UeXBvZ3JhcGh5PlxuICAgICAgICAgIDwvQm94PlxuICAgICAgICAgIDxUeXBvZ3JhcGh5XG4gICAgICAgICAgICBzeD17e1xuICAgICAgICAgICAgICBmb250V2VpZ2h0OiA1MDAsXG4gICAgICAgICAgICAgIGZvbnRTaXplOiBcIjE0cHhcIixcbiAgICAgICAgICAgICAgY29sb3I6IFwiIzZjNzU3ZFwiLFxuICAgICAgICAgICAgfX1cbiAgICAgICAgICA+XG4gICAgICAgICAgICBVcGxvYWQgYW5kIGFuYWx5emUgZW1wbG95ZWUgY2Vuc3VzIGRhdGFcbiAgICAgICAgICA8L1R5cG9ncmFwaHk+XG4gICAgICAgIDwvQm94PlxuICAgICAgPC9Cb3g+XG4gICAgPC9Cb3g+XG4gICk7XG59XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJCb3giLCJUeXBvZ3JhcGh5IiwiQXZhdGFyIiwidXNlUm91dGVyIiwiQmFyQ2hhcnQzIiwidXNlQXBwU2VsZWN0b3IiLCJDZW5zdXNDYXJkIiwicm91dGVyIiwidXNlckRldGFpbHMiLCJzdGF0ZSIsInVzZXIiLCJ1c2VyUHJvZmlsZSIsImhhbmRsZUNsaWNrIiwicHJpbWFyeUtleSIsInByb2Nlc3MiLCJlbnYiLCJORVhUX1BVQkxJQ19VU0VSX0lEX0tFWSIsImFsdEtleSIsIk5FWFRfUFVCTElDX1VTRVJfSURfQUxUX0tFWSIsInVzZXJJZCIsImxvY2FsU3RvcmFnZSIsImdldEl0ZW0iLCJzc29Eb25lIiwidXNlckVtYWlsIiwiY29uc29sZSIsImxvZyIsInVzZXJEZXRhaWxzTmFtZSIsIm5hbWUiLCJ1c2VyRGV0YWlsc0VtYWlsIiwiZW1haWwiLCJpc0F1dGhlbnRpY2F0ZWQiLCJwdXNoIiwib25DbGljayIsInN4IiwiYmFja2dyb3VuZENvbG9yIiwicGFkZGluZyIsImRpc3BsYXkiLCJhbGlnbkl0ZW1zIiwianVzdGlmeUNvbnRlbnQiLCJib3JkZXJSYWRpdXMiLCJib3hTaGFkb3ciLCJtYXhXaWR0aCIsIm10IiwiY3Vyc29yIiwidHJhbnNpdGlvbiIsInRyYW5zZm9ybSIsImZsZXhEaXJlY3Rpb24iLCJ3aWR0aCIsImhlaWdodCIsIm1yIiwiYmFja2dyb3VuZCIsInNpemUiLCJjb2xvciIsInN0eWxlIiwiZmlsdGVyIiwiZm9udFdlaWdodCIsImZvbnRTaXplIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/CensusCard.tsx\n"));

/***/ })

});