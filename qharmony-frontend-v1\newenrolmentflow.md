# 🛠️ Refactor enrollment: Dynamic Coverage Plan Page

## 🎯 Goal

Currently, we use separate components for each coverage type during employee enrollment, such as:

- `MedicalPlanPage.tsx`
- `VisionPlanPage.tsx`
- `DentalPlanPage.tsx`
and so on ...

This hardcoded setup is **not scalable**, especially as companies may have varied and dynamic combinations of plans.

### ✅ Objective

- Create a **single reusable page** component (e.g., `DynamicPlanPage.tsx`) that renders different plan types dynamically based on backend configuration.
- Replace existing coverage pages (Medical, Vision, Dental, etc.) with this unified component.
- Ensure this integrates seamlessly within the existing enrollment flow:

WelcomePage → PersonalizationPage → DependentsConfirmationPage
→ [DynamicPlanPage repeated for N assigned plan categories]
→ SummaryPage → ConfirmationPage


---

## 🧩 Requirements

### 1. 🧠 Dynamic Plan Rendering

- Based on the **plan categories assigned to a company** (e.g., `["Medical", "Vision", "Dental"]`, check what others are there, synamically fetched and dynamically shown ), dynamically inject `DynamicPlanPage` N times in the flow.
- The plan categories and the associated plans should come from backend API response, check backend for help and apis that we have to prevent errors.
- Each category (e.g., Medical) will group all plans under it in one page with waive option (e.g., 2 Medical plans in 1 page).

---

### 2. 🎨 UI Customization (Theme Colors)

- Use **4 rotating UI color schemes** to distinguish different plan categories visually.
- Color themes should repeat after 4 categories (e.g., Theme1 → Theme2 → Theme3 → Theme4 → Theme1...).


Example Theme Palette:
| Theme | Primary Color | Accent |
|-------|---------------|--------|
| Theme 1 | `#1E90FF` (Blue) | `#E6F0FF` |
| Theme 2 | `#32CD32` (Green) | `#E6FFE6` |
| Theme 3 | `#FF8C00` (Orange) | `#FFF3E6` |
| Theme 4 | `#800080` (Purple) | `#F5E6FF` |

---

### 3. 🏳️ Waive Coverage Support

- Each plan category page must allow user to **"Waive Coverage"** for that type.
- If user waives:
- Skip plan selection for that category
- Mark waived status in submission payload
- Must be toggle-able, reversible before submission.

---

### 4. 🔁 Navigation Flow Integration

- `DynamicPlanPage.tsx` should:
- Accept props like `category`, `plans`, `themeIndex`, and `onNext`
- Replace the respective existing pages (Medical, Dental, Vision, etc.) in the routing and navifgation flow.
- Maintain the flow by linking back to SummaryPage → ConfirmationPage.

---

## 📦 Deliverables

- [ ] `DynamicPlanPage.tsx` component
- [ ] Enum or map for plan category to theme index
- [ ] Modified enrollment flow controller/step manager to insert correct number of `DynamicPlanPage`s based on response




## 📌 Notes

- All logic must be backward-compatible with the current flow structure.
- Should support varying combinations of plan types per company.
- Can mock backend response format during initial development.


