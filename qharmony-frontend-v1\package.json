{"name": "qharmony-website", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start -p 8080", "lint": "next lint", "format": "prettier --write ."}, "dependencies": {"@azure/msal-browser": "^3.28.0", "@azure/msal-react": "^2.1.0", "@emotion/react": "^11.13.3", "@emotion/styled": "^11.13.0", "@fontsource/poppins": "^5.0.15", "@microsoft/teams-js": "^2.31.0", "@mui/icons-material": "^6.0.2", "@mui/material": "^6.0.2", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-aspect-ratio": "^1.1.7", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-context-menu": "^2.2.15", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-hover-card": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-menubar": "^1.1.15", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-toggle-group": "^1.1.10", "@radix-ui/react-tooltip": "^1.2.7", "@reduxjs/toolkit": "^2.2.7", "@svgr/webpack": "^8.1.0", "@types/signature_pad": "^2.3.6", "axios": "^1.7.7", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "embla-carousel-react": "^8.6.0", "firebase": "^10.13.1", "input-otp": "^1.4.2", "lucide-react": "^0.475.0", "next": "^14.2.13", "next-themes": "^0.4.6", "react": "^18", "react-day-picker": "^9.8.0", "react-dom": "^18", "react-hook-form": "^7.60.0", "react-icons": "^5.5.0", "react-quill": "^2.0.0", "react-redux": "^9.1.2", "react-resizable-panels": "^3.0.3", "react-select": "^5.10.1", "react-toastify": "^11.0.5", "recharts": "^3.0.2", "signature_pad": "^5.0.9", "sonner": "^2.0.6", "tailwind-merge": "^3.3.0", "tailwindcss-animate": "^1.0.7", "turndown": "^7.2.0", "vaul": "^1.1.2"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "@types/turndown": "^5.0.5", "eslint": "^8", "eslint-config-next": "14.2.7", "postcss": "^8", "prettier": "^3.3.3", "prettier-plugin-tailwindcss": "^0.6.6", "tailwindcss": "^3.4.1", "typescript": "^5"}}