/* Create Plan Page Styles */
:root {
  --primary-gradient: linear-gradient(135deg, #2563eb 0%, #8b5cf6 100%);
}

.create-plan-wrapper {
  background: white;
  min-height: 100vh;
  font-family: 'SF Pro', 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Se<PERSON>e UI', Robot<PERSON>, 'Helvetica Neue', Arial, sans-serif;
}

.create-plan-page {
  max-width: min(90vw, 1400px);
  margin: 0 auto;
  background: white;
  min-height: 100vh;
  padding: 1vh 10vh 0;
}

/* Combined Progress Header Component */
.progress-header-component {
  background: white;
  border-radius: 20px;
  padding: 1rem 1rem 1rem 1rem;
  box-shadow: 
  4px 4px 6px rgba(0, 0, 0, 0.15);   /* bottom-right shadow */
  border-bottom: 1px solid #e5e7eb;
  margin-bottom: 2rem;
  margin-left: 2.75rem;
  margin-right: 2.75rem;
}

.progress-header {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.progress-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

/* Progress title uses design system classes */
.progress-counter {
  font-size: 0.875rem;
  color: #6b7280;
  font-weight: 500;
}

.progress-bar-container {
  width: 100%;
  height: 8px;
  background: #e5e7eb;
  border-radius: 4px;
  overflow: hidden;
}

.progress-bar-fill {
  height: 100%;
  background: linear-gradient(135deg, #7c3aed 0%, #a855f7 100%); /* Purple gradient */
  border-radius: 4px;
  transition: width 0.3s ease;
}

/* Page Navigation */
.page-navigation {
  display: flex;
  gap: 10px;
  width: 100%;
  overflow-x: auto;
}

.nav-item-wrapper {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  position: relative;
}

.nav-tooltip-icon {
  color: #9ca3af;
  cursor: help;
  transition: color 0.2s ease;
  flex-shrink: 0;
}

.nav-tooltip-icon:hover {
  color: #7c3aed;
}

/* Form Tooltip Icon Styling */
.form-tooltip-icon {
  color: #9ca3af;
  cursor: help;
  transition: color 0.2s ease;
  margin-left: 0.25rem;
  vertical-align: middle;
  display: inline-flex;
  align-items: center;
  position: relative;
}

.form-tooltip-icon:hover {
  color: #7c3aed;
}

/* Ensure MUI Tooltips appear above other elements */
.MuiTooltip-popper {
  z-index: 9999 !important;
}

.MuiTooltip-tooltip {
  z-index: 9999 !important;
}

/* Custom CSS tooltip as fallback */
.form-tooltip-icon[title]:hover::after {
  content: attr(title);
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  background: #374151;
  color: white;
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 12px;
  white-space: nowrap;
  z-index: 10000;
  margin-bottom: 5px;
  max-width: 250px;
  white-space: normal;
  word-wrap: break-word;
}

.form-tooltip-icon[title]:hover::before {
  content: '';
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  border: 5px solid transparent;
  border-top-color: #374151;
  z-index: 10000;
}

.page-nav-item {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  padding: 5px 9px;
  background: #f3f4f6;
  border: none;
  border-radius: 24px;
  cursor: pointer;
  font-size: 13px;
  font-weight: 450;
  line-height: 1.2;
  font-family: 'SF Pro', 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  color: #6b7280;
  transition: all 0.2s ease;
  white-space: nowrap;
  min-width: fit-content;
  position: relative;
}


.page-nav-item.active {
  background: #ede9fe; /* Light purple (matching employee-enrol) */
  color: #7c3aed; /* Purple (matching gradient) */
  border: none;
}

.page-nav-item.completed {
  background: #ddd6fe; /* Light purple background */
  color: #6d28d9; /* Darker purple text */
  border: none;
}

.page-nav-item.completed::after {
  content: '✓';
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 16px;
  height: 16px;
  background: #7c3aed; /* Purple matching gradient */
  border-radius: 50%;
  margin-left: 6px;
  font-size: 10px;
  font-weight: 700;
  color: white;
  line-height: 1;
  flex-shrink: 0;
}

.page-nav-item:hover:not(.active):not(.completed):not(:disabled) {
  background: #e5e7eb;
  color: #374151;
}

.page-nav-item:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Navigation Icon Styling */
.nav-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  transition: all 0.2s ease;
}

.page-nav-item.active .nav-icon {
  color: #7c3aed; /* Purple for active state */
}

.page-nav-item.completed .nav-icon {
  color: #6d28d9; /* Darker purple for completed state */
}

/* Header */
.create-plan-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 2rem;
  background: white;
  border-bottom: 1px solid #e5e7eb;
  gap: 1rem;
}

.back-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: white;
  border: 1px solid #e5e7eb;
  padding: clamp(0.5rem, 2vw, 0.75rem) clamp(1rem, 3vw, 1.5rem);
  border-radius: clamp(0.5rem, 1.5vw, 0.75rem);
  color: #374151;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  font-size: clamp(0.8rem, 2vw, 0.875rem);
  font-weight: 500;
  font-family: 'SF Pro', 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.back-btn:hover {
  background: #f9fafb;
  border-color: #d1d5db;
  transform: translateY(-1px);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
}

.auto-save-indicator {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: clamp(0.75rem, 2vw, 0.875rem);
  color: #059669;
  font-weight: 500;
  font-family: 'SF Pro', 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.save-spinner {
  width: 1rem;
  height: 1rem;
  border: 2px solid #e5e7eb;
  border-top: 2px solid #059669;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.save-checkmark {
  color: #059669;
  font-weight: 600;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* AI Assistant Message */
.ai-assistant-message {
  background: transparent;
  padding: 0 clamp(1rem, 4vw, 3rem);
  margin: 1.5rem 0;
  display: flex;
  align-items: center;
}

.ai-message-content {
  display: flex;
  align-items: flex-start;
  gap: 0rem;
  width: 100%;
}

.chat-bubble {
  background: white;
  border-radius: 1rem;
  padding: 2vh 2vw;
  box-shadow: 
  4px 4px 6px rgba(0, 0, 0, 0.15); 
  position: relative;
  margin-left: 1rem;
  margin-bottom: 1vh;
  flex: 1;
}

.chat-bubble::before {
  content: '';
  position: absolute;
  left: -8px;
  top: 1rem;
  width: 0;
  height: 0;
  border-top: 8px solid transparent;
  border-bottom: 8px solid transparent;
  border-right: 8px solid white;
}

.ai-avatar {
  flex-shrink: 0;
}

.avatar-circle {
  width: 3rem;
  height: 3rem;
  background: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.brea-avatar {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.ai-text {
  flex: 1;
  font-family: 'SF Pro', 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.ai-greeting {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 0.75rem;
  line-height: 1.4;
}

.ai-text p {
  font-size: 0.9rem;
  color: #4b5563;
  line-height: 1.6;
  margin: 0;
}

.chat-message {
  font-size: 0.875rem;
  color: #1f2937;
  line-height: 1.6;
  margin-bottom: 0.75rem;
}

.chat-subtitle {
  font-size: 0.875rem;
  color: #6b7280;
  line-height: 1.6;
  margin: 0;
}

.saved-indicator {
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
  background: #dcfce7;
  color: #166534;
  padding: 0.25rem 0.75rem;
  border-radius: 0.5rem;
  font-size: clamp(0.75rem, 1.8vw, 0.875rem);
  font-weight: 600;
  margin-right: 0.5rem;
  margin-bottom: 0.5rem;
  font-family: 'SF Pro', 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* Main Content Area */
.main-content {
  margin: 0 auto;
  padding: 0 clamp(1rem, 4vw, 3rem) 2rem;
  background: white;
  display: flex;
  flex-direction: column;
}

.form-container {
  background: white;
  border-radius: 0.75rem;
  border: 1px solid #e5e7eb;
  box-shadow: 
  4px 4px 6px rgba(0, 0, 0, 0.15);  
  overflow: hidden;
  width: 100%;
  position: relative;
  flex: 1;
}

.form-container::before {
  content: '';
  position: absolute;
  left: -1rem;
  top: 1.5rem;
  width: 0.5rem;
  height: 0.5rem;
  background: #e5e7eb;
  border-radius: 50%;
}

/* Form Section */
.form-section {
  background: white;
  display: flex;
  flex-direction: column;
  width: 100%;
}

.form-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 0.5rem 0.5rem 0.5rem 0.5rem;
  margin-bottom: 0;
}

.form-header-content {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  color: #8b5cf6;
}

.form-header-content svg {
  background: var(--primary-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  filter: drop-shadow(0 1px 2px rgba(139, 92, 246, 0.2));
}

.form-header h3 {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
  font-family: 'SF Pro', 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.ai-assist-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: var(--primary-gradient);
  color: white;
  border: none;
  padding: 0.625rem 1rem;
  border-radius: 0.5rem;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  font-family: 'SF Pro', 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.ai-assist-btn:hover {
  background: var(--primary-gradient);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(139, 92, 246, 0.4);
  filter: brightness(1.1);
}

/* Form Content */
.form-content {
  width: 100%;
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 1rem;
  overflow-y: auto;
  padding: 0.5rem 0.5rem 0.5rem 1rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin-bottom: 0rem;
}

.form-group label {
  font-size: 0.875rem;
  font-weight: 500;
  color: #374151;
  font-family: 'SF Pro', 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

/* Tooltip Styles */
.tooltip-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 0.7rem;
  height: 0.7rem;
  border: 1px solid #9ca3af;
  border-radius: 50%;
  font-size: 0.625rem;
  font-weight: 500;
  color: #6b7280;
  cursor: help;
  transition: all 0.2s ease;
  position: relative;
  margin-left: 0rem;
}

.tooltip-icon::after {
  content: 'i';
}

.tooltip-icon:hover {
  border-color: #3b82f6;
  color: #3b82f6;
  background: #eff6ff;
}

/* Hide default tooltips - we'll use JavaScript to create them */
.tooltip-icon[data-tooltip]:hover::before,
.tooltip-icon[data-tooltip]:hover::after {
  display: none;
}

/* Custom tooltip styles for JavaScript-created tooltips */
.custom-tooltip {
  font-family: 'SF Pro', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

@keyframes tooltipFadeIn {
  from {
    opacity: 0;
    transform: translateX(-50%) translateY(-4px);
  }
  to {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
  }
}



.field-hint {
  font-size: 0.75rem;
  color: #6b7280;
  font-style: italic;
  margin-top: 0.25rem;
}

.form-group input,
.form-group select,
.form-group textarea {
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 0.5rem;
  font-size: 0.875rem;
  color: #374151;
  background: white;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  font-family: 'SF Pro', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #8b5cf6;
  box-shadow: 0 0 0 3px rgba(139, 92, 246, 0.1);
  transform: translateY(-1px);
}

.form-group input::placeholder,
.form-group textarea::placeholder {
  color: #9ca3af;
}

/* Navigation */
.form-navigation {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 0.5rem 0.5rem 1rem;
  margin-top: 0;
  gap: 1rem;
}

.nav-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.25rem;
  border-radius: 0.5rem;
  font-size: 0.875rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: none;
  font-family: 'SF Pro', 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  min-width: 140px;
  justify-content: center;
}

.nav-btn.secondary {
  background: white;
  color: #374151;
  border: 1px solid #e5e7eb;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.nav-btn.secondary:hover {
  background: #f9fafb;
  border-color: #d1d5db;
  transform: translateY(-1px);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
}

.nav-btn.primary {
  background: var(--primary-gradient);
  color: white;
  box-shadow: 0 2px 8px rgba(139, 92, 246, 0.25);
}

.nav-btn.primary.enabled:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(139, 92, 246, 0.4);
}

.nav-btn.primary.disabled {
  background: #d1d5db;
  color: #9ca3af;
  cursor: not-allowed;
  box-shadow: none;
}

/* Step 2 Specific Styles - Additional textarea properties */
.form-group textarea {
  resize: vertical;
  min-height: 100px;
}

.highlight-input {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
}

.highlight-input input {
  flex: 1;
}

.remove-highlight {
  background: #ef4444;
  color: white;
  border: none;
  border-radius: 0.25rem;
  width: 2rem;
  height: 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 1rem;
  font-weight: bold;
}

.remove-highlight:hover {
  background: #dc2626;
}

.add-highlight {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: #f3f4f6;
  color: #374151;
  border: 1px dashed #d1d5db;
  border-radius: clamp(0.5rem, 1.5vw, 0.75rem);
  padding: clamp(0.5rem, 2vw, 0.75rem) clamp(1rem, 3vw, 1.25rem);
  font-size: clamp(0.8rem, 2vw, 0.875rem);
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  margin-top: 0.75rem;
  font-family: 'SF Pro', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  font-weight: 500;
  justify-content: center;
}

.add-highlight:hover {
  background: #e5e7eb;
  border-color: #9ca3af;
  transform: translateY(-1px);
}

/* File Upload Styles */
.file-upload-area {
  position: relative;
  margin-top: 0.5rem;
}

.file-input {
  position: absolute;
  opacity: 0;
  width: 100%;
  height: 100%;
  cursor: pointer;
}

.file-upload-label {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
  padding: 2rem;
  border: 2px dashed #d1d5db;
  border-radius: 0.75rem;
  background: #f9fafb;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: center;
}

.file-upload-label:hover {
  border-color: #8b5cf6;
  background: #faf5ff;
  color: #8b5cf6;
}

.file-upload-label span {
  font-size: 0.875rem;
  font-weight: 500;
  color: #374151;
}

.file-upload-label small {
  font-size: 0.75rem;
  color: #6b7280;
}

.uploaded-files {
  margin-top: 1rem;
  padding: 1rem;
  background: #f8fafc;
  border-radius: 0.5rem;
  border: 1px solid #e2e8f0;
}

.uploaded-files h4 {
  font-size: 0.875rem;
  font-weight: 600;
  color: #374151;
  margin: 0 0 0.75rem 0;
}

.uploaded-file {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem;
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 0.375rem;
  margin-bottom: 0.5rem;
}

.uploaded-file:last-child {
  margin-bottom: 0;
}

.file-name {
  flex: 1;
  font-size: 0.875rem;
  color: #374151;
  font-weight: 500;
}

.file-size {
  font-size: 0.75rem;
  color: #6b7280;
}

.remove-file {
  background: #ef4444;
  color: white;
  border: none;
  border-radius: 0.25rem;
  width: 1.5rem;
  height: 1.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background 0.2s ease;
}

.remove-file:hover {
  background: #dc2626;
}

/* Step 3 Specific Styles */
.input-with-prefix {
  position: relative;
  display: flex;
  align-items: center;
}

.input-prefix {
  position: absolute;
  left: 0.75rem;
  color: #6b7280;
  font-weight: 500;
  z-index: 1;
}

.input-with-prefix input {
  padding-left: 2rem;
}

/* Step 4 Review Styles */
.ready-badge {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: #dcfce7;
  color: #166534;
  padding: clamp(0.25rem, 1vw, 0.5rem) clamp(0.75rem, 2vw, 1rem);
  border-radius: 1rem;
  font-size: clamp(0.7rem, 1.8vw, 0.8rem);
  font-weight: 600;
  font-family: 'SF Pro', 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.review-content {
  padding: 0.5rem 0.5rem 0.5rem 0.5rem;
  display: flex;
  flex-direction: column;
  gap: 1.25rem;
  width: 100%;
}

.review-section {
  background: #f8fafc;
  border-radius: 0.75rem;
  padding: 1.25rem;
  border: 1px solid #e2e8f0;
  transition: all 0.3s ease;
}

.review-section:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.review-section-header {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1rem;
  color: #3b82f6;
}

.review-section-header svg {
  background: var(--primary-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  filter: drop-shadow(0 1px 2px rgba(139, 92, 246, 0.2));
}

.review-section-header h4 {
  font-size: clamp(0.9rem, 2.5vw, 1.125rem);
  font-weight: 600;
  color: #1e293b;
  margin: 0;
  font-family: 'SF Pro', 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.review-items {
  display: flex;
  flex-direction: column;
  gap: 0.625rem;
}

.review-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.review-item.full-width {
  flex-direction: column;
  align-items: flex-start;
  gap: 0.5rem;
}

.review-label {
  font-size: 0.875rem;
  color: #64748b;
  font-weight: 500;
}

.review-value {
  font-size: 0.875rem;
  color: #1e293b;
  font-weight: 600;
}

.review-value.metal-tier {
  background: var(--primary-gradient);
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: 0.5rem;
  font-size: 0.75rem;
}

.review-value.plan-code {
  background: var(--primary-gradient);
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: 0.5rem;
  font-family: 'SF Mono', 'Monaco', 'Cascadia Code', 'Roboto Mono', monospace;
  font-size: 0.75rem;
  font-weight: 700;
}

.review-link {
  color: #3b82f6;
  text-decoration: none;
  font-weight: 500;
  word-break: break-all;
}

.review-link:hover {
  text-decoration: underline;
}

.review-documents {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  background: white;
  border-radius: 0.5rem;
  border: 1px solid #e5e7eb;
  padding: 1rem;
}

.review-document {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem;
  background: #f8fafc;
  border-radius: 0.375rem;
  border: 1px solid #e2e8f0;
}

.review-document span {
  font-size: 0.875rem;
  color: #374151;
  font-weight: 500;
}

.review-document small {
  font-size: 0.75rem;
  color: #6b7280;
}

.review-description {
  font-size: 0.875rem;
  color: #374151;
  line-height: 1.6;
  margin: 0;
  background: hsl(0, 0%, 93%);
  padding: 0.7rem;
  border-radius: 0.5rem;

}

.review-highlights {
  margin: 0;
  padding: 0;
  list-style: none;
  background: hsl(0, 0%, 93%);
  border-radius: 0.5rem;
  border: 1px solid #e5e7eb;
  padding: 0.7rem;
}

.review-highlights li {
  font-size: 0.875rem;
  color: #374151;
  padding: 0.25rem 0;
  position: relative;
  padding-left: 1.5rem;
}

.review-highlights li:before {
  content: '✓';
  position: absolute;
  left: 0;
  color: #059669;
  font-weight: bold;
}

.create-confirmation {
  background: linear-gradient(135deg, rgba(37, 99, 235, 0.05) 0%, rgba(139, 92, 246, 0.05) 100%);
  border: 1px solid rgba(139, 92, 246, 0.2);
  color: #1e293b;
  border-radius: 0.75rem;
  padding: 1.25rem;
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-top: 0.5rem;
  width: 100%;
}

.confirmation-icon {
  background: var(--primary-gradient);
  border-radius: 50%;
  width: 3rem;
  height: 3rem;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  font-weight: bold;
  flex-shrink: 0;
  color: white;
  box-shadow: 0 4px 12px rgba(139, 92, 246, 0.3);
}

.confirmation-text h4 {
  margin: 0 0 0.375rem 0;
  font-size: 1.125rem;
  font-weight: 600;
  background: var(--primary-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.confirmation-text p {
  margin: 0;
  font-size: 0.875rem;
  color: #64748b;
  line-height: 1.4;
}

/* Step 5 Success Styles */
.success-section {
  text-align: center;
  background: white;
  border-radius: 1rem;
  margin: 0;
}

.success-content {
  box-shadow: none;
  padding-bottom: 32px;
  border: none;
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
}

.success-icon {
  background: var(--primary-gradient);
  color: white;
  border-radius: 50%;
  width: 3.5rem;
  height: 3.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.75rem;
  font-weight: bold;
  margin-bottom: 0.5rem;
  box-shadow: 0 4px 12px rgba(139, 92, 246, 0.3);
}

.success-content h3 {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 0.5rem 0;
  background: var(--primary-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.success-content p {
  font-size: 0.95rem;
  color: #64748b;
  margin: 0 0 0.75rem 0;
  max-width: 400px;
  line-height: 1.4;
}

.success-actions {
  display: flex;
  flex-direction: row;
  gap: 1rem;
  width: 100%;
  max-width: 500px;
  margin-top: 0.75rem;
  justify-content: center;
}

.success-actions .nav-btn {
  flex: 1;
  min-width: 180px;
  max-width: 220px;
}

/* Success section specific styling */
.success-section {
  width: 100%;
}

.success-section .form-content {
  padding: 0;
}

/* Plan Details Card */
.plan-details-card {
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 0.75rem;
  padding: 1.25rem;
  margin: 0.75rem auto;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.06);
  max-width: 500px;
  width: 100%;
  text-align: left !important;
}
/* Gradient Icon Styling */
.gradient-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #2563eb 0%, #8b5cf6 100%);
  border-radius: 20%;
  color: white;
  padding: 0.2rem;
}

.gradient-icon-small {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #2563eb 0%, #8b5cf6 100%);
  border-radius: 20%;
  color: white;
  padding: 0.2rem;
}

.gradient-icon-large {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #2563eb 0%, #8b5cf6 100%);
  border-radius: 50%;
  color: white;
  padding: 0.75rem;
}

.gradient-icon-nav {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  color: #7c3aed;
  padding: 0.25rem 0rem 0.25rem 0.25rem;
  
}

/* Adjustments for specific contexts */
.form-header-content {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.review-section-header {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.ready-badge {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.review-document {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.uploaded-file {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.confirmation-icon {
  display: flex;
  align-items: center;
  justify-content: center;
}

.success-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1rem;
}

.plan-details-header {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 0rem;
  margin-bottom: 0.25rem;
  color: var(--primary-gradient);
  border-bottom: 1px solid #f1f5f9;
  padding-bottom: 0.5rem;
  text-align: left;
}

.plan-details-header h4 {
  font-size: 1.125rem;
  font-weight: 600;
  display: flex;
  padding-left: 0.25rem;
  align-items: center;
  color: #1e293b;
  margin: 0;
  font-family: 'SF Pro', 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  background: var(--primary-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.plan-details-content {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  text-align: left;
}

.plan-detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.2rem 0;
  border-bottom: 1px solid #f8fafc;
}

.plan-detail-item:last-child {
  border-bottom: none;
}

.detail-label {
  font-size: clamp(0.8rem, 2vw, 0.875rem);
  color: #64748b;
  font-weight: 500;
  font-family: 'SF Pro', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.detail-value {
  font-size: clamp(0.8rem, 2vw, 0.875rem);
  color: #1e293b;
  font-weight: 600;
  font-family: 'SF Pro', 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  text-align: right;
  max-width: 60%;
  word-break: break-all;
}

.detail-value.plan-id {
  background: #f1f5f9;
  color: #475569;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  font-family: 'SF Mono', 'Monaco', 'Cascadia Code', 'Roboto Mono', monospace;
  font-size: clamp(0.7rem, 1.8vw, 0.75rem);
}

.detail-value.plan-code {
  background: var(--primary-gradient);
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  font-family: 'SF Mono', 'Monaco', 'Cascadia Code', 'Roboto Mono', monospace;
  font-size: clamp(0.7rem, 1.8vw, 0.75rem);
  font-weight: 700;
}

.detail-value.status-active {
  background: #dcfce7;
  color: #166534;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  font-size: clamp(0.7rem, 1.8vw, 0.75rem);
  font-weight: 600;
}

/* Enhanced Responsive Design */
@media (max-width: 1024px) {
  .progress-header-component {
    padding: 1.5rem clamp(0.75rem, 3vw, 2rem) 1.25rem;
  }

  .ai-assistant-message {
    padding: 0 clamp(0.75rem, 3vw, 2rem);
  }

  .main-content {
    padding: 0 clamp(0.75rem, 3vw, 2rem) 2rem;
  }

  .success-actions {
    max-width: 400px;
  }

  .success-actions .nav-btn {
    min-width: 160px;
  }
}

@media (max-width: 768px) {
  .progress-header-component {
    padding: 1rem clamp(0.5rem, 2vw, 1.5rem) 1rem;
    margin-bottom: 1.5rem;
  }

  .create-plan-header {
    flex-direction: column;
    gap: 1rem;
    align-items: flex-start;
    padding: 1rem;
  }

  .ai-assistant-message {
    padding: 0 clamp(0.5rem, 2vw, 1.5rem);
    margin: 1rem 0;
  }

  .chat-bubble {
    padding: 1rem 1.5rem;
    margin-left: 0.5rem;
  }

  .main-content {
    padding: 0 clamp(0.5rem, 2vw, 1.5rem) 1.5rem;
  }

  .form-container::before {
    display: none;
  }

  .form-navigation {
    flex-direction: column;
    gap: 1rem;
  }

  .nav-btn {
    width: 100%;
    min-width: auto;
  }

  .review-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .create-confirmation {
    flex-direction: column;
    text-align: center;
    gap: 1rem;
  }

  .success-actions {
    width: 100%;
    flex-direction: column;
    gap: 0.75rem;
    max-width: 100%;
  }

  .success-actions .nav-btn {
    min-width: auto;
    max-width: 100%;
  }

  .plan-details-card {
    max-width: 100%;
    margin: 0.75rem 0;
    padding: 1rem;
  }

  .plan-detail-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.25rem;
    text-align: left;
    padding: 0.375rem 0;
  }

  .detail-value {
    max-width: 100%;
    text-align: left;
    word-break: break-word;
  }
}

@media (max-width: 480px) {
  .highlight-input {
    flex-direction: column;
    gap: 0.75rem;
  }

  .remove-highlight {
    align-self: flex-end;
  }

  .success-content {
    padding: 1rem 1rem;
    gap: 0.5rem;
  }

  .plan-details-card {
    padding: 1rem;
    margin: 0.5rem 0;
  }
}

/* Large screen optimizations */
@media (min-width: 1200px) {
  .create-plan-page {
    max-width: min(85vw, 1400px);
    padding: 2vh 10vh 0;
  }

  .review-content {
    max-width: 1200px;
    margin: 0 0;
  }
}

/* Ultra-wide screen optimizations */
@media (min-width: 1600px) {
  .create-plan-page {
    max-width: min(80vw, 1600px);
    padding: 2vh 10vh 0;
  }
  .review-content {
    max-width: 1600px;
    margin: 0 0;
  }
}
