'use client';

import React, { useState, useEffect, useMemo } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import Image from 'next/image';
import ProtectedRoute from '@/components/ProtectedRoute';
import { usePerformanceMonitor } from '../hooks/usePerformanceMonitor';
import CreatePlanOptimizer from './components/CreatePlanOptimizer';
import { Tooltip } from '@mui/material';
import { IoInformationCircle } from 'react-icons/io5';
import EnrollmentHeader from '../employee-enrol/components/EnrollmentHeader';
import {
  HiOutlineArrowLeft,
  HiOutlineDocumentText,
  HiOutlineCheckCircle,
  HiOutlinePlus,
  HiOutlineX,
  HiOutlineCloudUpload
} from 'react-icons/hi';
import {
  RiHealthBookLine,
  RiFileTextLine,
  RiCheckboxCircleLine,
  RiShieldCheckLine,
  RiFileUploadLine,
  RiVideoLine
} from 'react-icons/ri';

import './create-plan.css';
import {
  getConstantsData,
  getCarriers,
  createPlan,
  uploadPlanDocuments,
  checkPlanNameDuplicate,
  checkPlanCodeDuplicate,
  type PlanData,
  type CarrierData,
  type ApiResponse
} from './services/planApi';

// Import Plan interface from API
import type { Plan } from './services/planApi';

// Extended Plan interface for local use
interface ExtendedPlan extends Plan {
  brokerId?: string;
  brokerageId?: string;
  isTemplate: boolean;
  coverageSubTypes: string[];
  planYearStart?: Date;
  planYearEnd?: Date;
  rateStructure?: string;
  coverageTiers?: Array<{
    tierName: string;
    employeeCost?: number;
    employerCost?: number;
    totalCost?: number;
  }>;
  ageBandedRates?: Array<{
    ageMin: number;
    ageMax: number;
    rate: number;
  }>;
  documentIds: string[];
  carrierId?: string;
  carrierPlanId?: string;
  groupNumber?: string;
  status: string;
  isActivated: boolean;
  createdAt: Date;
  updatedAt: Date;
}

interface DataStructure {
  plans: ExtendedPlan[];
  templates: ExtendedPlan[];
  carriers: CarrierData[];
  planTypes: string[];
  coverageCategories: string[];
  coverageMap: { [key: string]: string[] };
  metalTiers: string[];
}

const CreatePlanPage: React.FC = () => {
  const router = useRouter();
  const [currentStep, setCurrentStep] = useState(1);

  const [createdPlan, setCreatedPlan] = useState<Plan | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  const [data, setData] = useState<DataStructure | null>(null);

  // State for plan name duplicate checking
  const [planNameStatus, setPlanNameStatus] = useState<{
    isChecking: boolean;
    isDuplicate: boolean;
    existingPlan?: Plan;
    error?: string;
  }>({
    isChecking: false,
    isDuplicate: false
  });

  // State for plan code duplicate checking
  const [planCodeStatus, setPlanCodeStatus] = useState<{
    isChecking: boolean;
    isDuplicate: boolean;
    existingPlan?: Plan;
    error?: string;
  }>({
    isChecking: false,
    isDuplicate: false
  });

  // Monitor page performance
  usePerformanceMonitor('Create Plan Page');

  // Debug: Log state changes (removed for production)

  const [formData, setFormData] = useState({
    // Step 1: Basic Info
    planName: '',
    planCode: '',
    carrier: '',
    planType: '',
    coverageCategory: '',
    coverageType: '',
    metalTier: '',

    // Step 2: Media & Documents
    videoUrl: '',
    documents: [] as File[],

    // Step 3: Description & Highlights
    description: '',
    highlights: [''],

    // Legacy fields (for compatibility)
    effectiveDate: '',
    endDate: '',
    copay: '',
    deductible: ''
  });

  const steps = [
    {
      number: 1,
      title: 'Documents',
      subtitle: 'Upload files',
      active: currentStep === 1,
      completed: currentStep > 1,
      tooltip: 'Upload plan documents, brochures, and supporting materials (optional)'
    },
    {
      number: 2,
      title: 'Basic Info',
      subtitle: 'Plan details',
      active: currentStep === 2,
      completed: currentStep > 2,
      tooltip: 'Enter plan name, carrier, coverage type, and metal tier information'
    },
    {
      number: 3,
      title: 'Description',
      subtitle: 'Details & video',
      active: currentStep === 3,
      completed: currentStep > 3,
      tooltip: 'Add plan description, key highlights, and optional video content'
    },
    {
      number: 4,
      title: 'Preview',
      subtitle: 'Review & create',
      active: currentStep === 4,
      completed: currentStep > 4,
      tooltip: 'Review all plan details and create your new plan'
    },
    {
      number: 5,
      title: 'Success',
      subtitle: 'Plan created',
      active: currentStep === 5,
      completed: false,
      tooltip: 'Plan successfully created and added to your catalog'
    }
  ];

  // Memoize constants data to avoid recalculation
  const constantsData = useMemo(() => getConstantsData(), []);

  useEffect(() => {
    // Clear any problematic draft data on component mount
    localStorage.removeItem('ai-enroller-draft-plan');

    // Prefetch related routes
    router.prefetch('/ai-enroller');
    router.prefetch('/ai-enroller/plans');

    // Load data from backend APIs and hardcoded constants
    const loadData = async () => {
      setIsLoading(true);

      try {
        // Check cache first
        const cachedData = sessionStorage.getItem('ai-enroller-create-plan-data');
        const cacheTimestamp = sessionStorage.getItem('ai-enroller-create-plan-cache-time');
        const cacheAge = cacheTimestamp ? Date.now() - parseInt(cacheTimestamp) : Infinity;

        // Use cache if less than 5 minutes old
        if (cachedData && cacheAge < 5 * 60 * 1000) {
          const parsedCache = JSON.parse(cachedData);
          setData(parsedCache);
          setIsLoading(false);

          // Load draft data only if form is empty
          const draftData = localStorage.getItem('ai-enroller-draft-plan');
          if (draftData && !formData.planName && !formData.coverageCategory) {
            const parsedDraft = JSON.parse(draftData);
            console.log('Loading draft data from cache:', parsedDraft);
            setFormData(parsedDraft);
            // Duplicate checking will be triggered automatically by useEffect
          }

          // Data loaded from cache
          return;
        }

        // Load fresh data
        const constantsResult = constantsData;

        // Load carriers in parallel with a timeout
        const carriersPromise = Promise.race([
          getCarriers(),
          new Promise((_, reject) =>
            setTimeout(() => reject(new Error('Carriers API timeout')), 3000)
          )
        ]);

        const carriersResult = await carriersPromise.catch(error => {
          console.warn('Carriers API failed or timed out:', error);
          return { success: false, error: error.message };
        }) as ApiResponse<CarrierData[]>;

        const backendData: DataStructure = {
          plans: [],
          templates: [],
          carriers: carriersResult.success ? carriersResult.data || [] : [],
          planTypes: constantsResult.data?.planTypes || [],
          coverageCategories: constantsResult.data?.coverageCategories || [],
          coverageMap: constantsResult.data?.coverageMap || {},
          metalTiers: constantsResult.data?.metalTiers || []
        };

        setData(backendData);
        console.log('Data loaded successfully:', backendData);
        console.log('Coverage categories:', backendData.coverageCategories);
        console.log('Loaded carriers:', backendData.carriers);

        // Cache the data
        sessionStorage.setItem('ai-enroller-create-plan-data', JSON.stringify(backendData));
        sessionStorage.setItem('ai-enroller-create-plan-cache-time', Date.now().toString());

        // Load draft data only if form is empty
        const draftData = localStorage.getItem('ai-enroller-draft-plan');
        if (draftData && !formData.planName && !formData.coverageCategory) {
          const parsedDraft = JSON.parse(draftData);
          console.log('Loading draft data from fresh load:', parsedDraft);
          setFormData(parsedDraft);
          // Duplicate checking will be triggered automatically by useEffect
        }

        // Data loaded successfully
      } catch (error) {
        console.error('Error loading data:', error);
        // Set fallback data structure
        setData({
          plans: [],
          templates: [],
          carriers: [],
          planTypes: constantsData.data?.planTypes || [],
          coverageCategories: constantsData.data?.coverageCategories || [],
          coverageMap: constantsData.data?.coverageMap || {},
          metalTiers: constantsData.data?.metalTiers || []
        });
      } finally {
        setIsLoading(false);
      }
    };

    loadData();
  }, [router, constantsData]);

  // Debounced auto-save to localStorage
  const debouncedSave = useMemo(() => {
    let timeoutId: NodeJS.Timeout;
    return (data: any) => {
      clearTimeout(timeoutId);
      timeoutId = setTimeout(() => {
        localStorage.setItem('ai-enroller-draft-plan', JSON.stringify(data));
      }, 500);
    };
  }, []);

  // Debounced plan name duplicate check
  const debouncedNameCheck = useMemo(() => {
    let timeoutId: NodeJS.Timeout;
    return (planName: string) => {
      clearTimeout(timeoutId);

      if (!planName.trim()) {
        setPlanNameStatus({ isChecking: false, isDuplicate: false });
        return;
      }

      setPlanNameStatus(prev => ({ ...prev, isChecking: true, error: undefined }));

      timeoutId = setTimeout(async () => {
        try {
          // No excludeId needed for create-plan page since it's always creating new plans
          const result = await checkPlanNameDuplicate(planName);

          if (result.success && result.data) {
            setPlanNameStatus({
              isChecking: false,
              isDuplicate: result.data.isDuplicate,
              existingPlan: result.data.existingPlan
            });
          } else {
            setPlanNameStatus({
              isChecking: false,
              isDuplicate: false,
              error: result.error || 'Failed to check for duplicates'
            });
          }
        } catch (error) {
          setPlanNameStatus({
            isChecking: false,
            isDuplicate: false,
            error: 'Error checking for duplicates'
          });
        }
      }, 800);
    };
  }, []);

  // Debounced plan code duplicate check
  const debouncedCodeCheck = useMemo(() => {
    let timeoutId: NodeJS.Timeout;
    return (planCode: string) => {
      clearTimeout(timeoutId);

      if (!planCode.trim()) {
        setPlanCodeStatus({ isChecking: false, isDuplicate: false });
        return;
      }

      setPlanCodeStatus(prev => ({ ...prev, isChecking: true, error: undefined }));

      timeoutId = setTimeout(async () => {
        try {
          // No excludeId needed for create-plan page since it's always creating new plans
          const result = await checkPlanCodeDuplicate(planCode);

          if (result.success && result.data) {
            setPlanCodeStatus({
              isChecking: false,
              isDuplicate: result.data.isDuplicate,
              existingPlan: result.data.existingPlan
            });
          } else {
            setPlanCodeStatus({
              isChecking: false,
              isDuplicate: false,
              error: result.error || 'Failed to check for duplicates'
            });
          }
        } catch (error) {
          setPlanCodeStatus({
            isChecking: false,
            isDuplicate: false,
            error: 'Error checking for duplicates'
          });
        }
      }, 800);
    };
  }, []);

  // Check for duplicates when form data changes (for any source - AI Assist, localStorage, manual input)
  useEffect(() => {
    if (formData.planName && formData.planName.trim()) {
      console.log('🔄 Form data changed: Checking plan name for duplicates');
      debouncedNameCheck(formData.planName);
    }
  }, [formData.planName, debouncedNameCheck]);

  useEffect(() => {
    if (formData.planCode && formData.planCode.trim()) {
      console.log('🔄 Form data changed: Checking plan code for duplicates');
      debouncedCodeCheck(formData.planCode);
    }
  }, [formData.planCode, debouncedCodeCheck]);

  // JavaScript-based tooltip system
  useEffect(() => {
    let activeTooltip: HTMLElement | null = null;

    const showTooltip = (icon: Element, text: string) => {
      // Remove any existing tooltip
      hideTooltip();

      const rect = icon.getBoundingClientRect();
      const tooltipWidth = 280;
      const tooltipHeight = 120; // Approximate

      // Calculate horizontal position (centered on icon, but keep within viewport)
      let leftPosition = rect.left + (rect.width / 2) - (tooltipWidth / 2);
      if (leftPosition < 10) leftPosition = 10;
      if (leftPosition + tooltipWidth > window.innerWidth - 10) {
        leftPosition = window.innerWidth - tooltipWidth - 10;
      }

      // Determine if tooltip should show above or below
      const spaceAbove = rect.top;
      const spaceBelow = window.innerHeight - rect.bottom;
      const showBelow = spaceAbove < tooltipHeight && spaceBelow > tooltipHeight;

      // Create tooltip element
      const tooltip = document.createElement('div');
      tooltip.className = 'custom-tooltip';
      tooltip.textContent = text;
      tooltip.style.cssText = `
        position: fixed;
        ${showBelow ? `top: ${rect.bottom + 4}px;` : `bottom: ${window.innerHeight - rect.top + 4}px;`}
        left: ${leftPosition}px;
        width: 280px;
        background: #1f2937;
        color: white;
        padding: 0.75rem 1rem;
        border-radius: 0.5rem;
        font-size: 0.8rem;
        font-weight: 400;
        line-height: 1.4;
        text-align: left;
        white-space: normal;
        word-wrap: break-word;
        hyphens: auto;
        box-shadow: 0 8px 16px rgba(0, 0, 0, 0.15);
        z-index: 99999;
        pointer-events: none;
        opacity: 0;
        transition: opacity 0.2s ease-in-out;
      `;

      // Create arrow
      const arrow = document.createElement('div');
      arrow.className = 'custom-tooltip-arrow';
      arrow.style.cssText = `
        position: fixed;
        ${showBelow ? `top: ${rect.bottom - 2}px;` : `bottom: ${window.innerHeight - rect.top - 2}px;`}
        left: ${rect.left + rect.width / 2 - 6}px;
        width: 0;
        height: 0;
        border-left: 6px solid transparent;
        border-right: 6px solid transparent;
        ${showBelow ? 'border-bottom: 6px solid #1f2937;' : 'border-top: 6px solid #1f2937;'}
        z-index: 100000;
        pointer-events: none;
        opacity: 0;
        transition: opacity 0.2s ease-in-out;
      `;

      document.body.appendChild(tooltip);
      document.body.appendChild(arrow);

      // Fade in
      requestAnimationFrame(() => {
        tooltip.style.opacity = '1';
        arrow.style.opacity = '1';
      });

      activeTooltip = tooltip;
      (activeTooltip as any).arrow = arrow;
    };

    const hideTooltip = () => {
      if (activeTooltip) {
        activeTooltip.remove();
        if ((activeTooltip as any).arrow) {
          (activeTooltip as any).arrow.remove();
        }
        activeTooltip = null;
      }
    };

    const handleMouseEnter = (e: Event) => {
      const icon = e.target as Element;
      const tooltipText = icon.getAttribute('data-tooltip');
      if (tooltipText) {
        showTooltip(icon, tooltipText);
      }
    };

    const handleMouseLeave = () => {
      hideTooltip();
    };

    // Add event listeners to all tooltip icons
    const tooltipIcons = document.querySelectorAll('.tooltip-icon[data-tooltip]');
    tooltipIcons.forEach(icon => {
      icon.addEventListener('mouseenter', handleMouseEnter);
      icon.addEventListener('mouseleave', handleMouseLeave);
    });

    // Cleanup function
    return () => {
      hideTooltip();
      tooltipIcons.forEach(icon => {
        icon.removeEventListener('mouseenter', handleMouseEnter);
        icon.removeEventListener('mouseleave', handleMouseLeave);
      });
    };
  }, [currentStep]);

  const handleInputChange = (field: string, value: string) => {
    console.log('handleInputChange called:', { field, value, currentFormData: formData });
    const updatedData = { ...formData, [field]: value };
    console.log('Updated form data:', updatedData);

    setFormData(updatedData);

    // Log after a small delay to see if state updated
    setTimeout(() => {
      console.log('Form data after setState (delayed):', formData);
    }, 100);

    // Debounced auto-save
    debouncedSave(updatedData);

    // Duplicate checking will be triggered automatically by useEffect
  };

  const handleFileUpload = (files: FileList | null) => {
    if (files) {
      const fileArray = Array.from(files);
      setFormData(prev => ({
        ...prev,
        documents: [...prev.documents, ...fileArray]
      }));
    }
  };

  const removeDocument = (index: number) => {
    setFormData(prev => ({
      ...prev,
      documents: prev.documents.filter((_, i) => i !== index)
    }));
  };





  const handleContinue = () => {
    if (currentStep < 5) {
      setCurrentStep(currentStep + 1);
    }
  };

  const handleBack = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleCreatePlan = async () => {
    try {
      // Use the selected coverage category as the main coverage type
      const coverageType = formData.coverageCategory;

      // Create the plan using backend API
      const planData: PlanData = {
        planName: formData.planName,
        planCode: formData.planCode,
        carrier: formData.carrier,
        coverageType: coverageType,
        coverageSubTypes: [formData.coverageType],
        planType: formData.planType,
        metalTier: formData.metalTier,
        description: formData.description,
        highlights: formData.highlights.filter(h => h.trim() !== ''),
        informativeLinks: formData.videoUrl ? [formData.videoUrl] : [],
        carrierId: formData.carrier,
        isTemplate: false,  // Explicitly set to false to create broker-owned plans, not templates
        status: 'Active'    // Set status to Active by default for company assignment
      };

      console.log('Creating plan with data:', planData);
      console.log('Form data mapping:');
      console.log('- Coverage Category (formData.coverageCategory):', formData.coverageCategory, '→ coverageType');
      console.log('- Coverage Type (formData.coverageType):', formData.coverageType, '→ coverageSubTypes');
      console.log('- Carrier ID:', formData.carrier);
      console.log('- Plan Type:', formData.planType);
      console.log('- Available carriers:', data?.carriers);

      const result = await createPlan(planData);

      if (result.success && result.data) {
        // Plan created successfully
        const finalPlan = result.data.plan;

        // Upload documents if any
        if (formData.documents.length > 0) {
          const uploadResult = await uploadPlanDocuments(finalPlan._id, formData.documents);
          if (!uploadResult.success) {
            console.warn('Failed to upload some documents:', uploadResult.error);
          }
        }

        // Store the created plan for display
        setCreatedPlan(finalPlan);

        // Clear draft data
        localStorage.removeItem('ai-enroller-draft-plan');

        // Move to success step
        setCurrentStep(5);
      } else {
        throw new Error(result.error || 'Failed to create plan');
      }
    } catch (error) {
      // Error creating plan
      alert(`Error creating plan: ${error instanceof Error ? error.message : 'Please try again.'}`);
    }
  };

  const addHighlight = () => {
    setFormData(prev => ({
      ...prev,
      highlights: [...prev.highlights, '']
    }));
  };

  const updateHighlight = (index: number, value: string) => {
    setFormData(prev => ({
      ...prev,
      highlights: prev.highlights.map((h, i) => i === index ? value : h)
    }));
  };

  const removeHighlight = (index: number) => {
    if (formData.highlights.length > 1) {
      setFormData(prev => ({
        ...prev,
        highlights: prev.highlights.filter((_, i) => i !== index)
      }));
    }
  };

  // Memoized validation functions for better performance
  const isStep1Valid = useMemo(() => {
    return true; // Documents are optional, so step 1 is always valid
  }, []);

  const isStep2Valid = useMemo(() => {
    return formData.planName &&
           formData.planCode &&
           formData.carrier &&
           formData.planType &&
           formData.coverageCategory &&
           formData.coverageType &&
           // metalTier is now optional
           !planNameStatus.isDuplicate &&
           !planNameStatus.isChecking &&
           !planCodeStatus.isDuplicate &&
           !planCodeStatus.isChecking;
  }, [formData.planName, formData.planCode, formData.carrier, formData.planType, formData.coverageCategory, formData.coverageType, planNameStatus.isDuplicate, planNameStatus.isChecking, planCodeStatus.isDuplicate, planCodeStatus.isChecking]);

  const isStep3Valid = useMemo(() => {
    return formData.description && formData.highlights.some(h => h.trim() !== '');
  }, [formData.description, formData.highlights]);



  // Loading component
  const LoadingSpinner = () => (
    <div style={{
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center',
      minHeight: '400px',
      flexDirection: 'column',
      gap: '16px'
    }}>
      <div style={{
        width: '40px',
        height: '40px',
        border: '3px solid #f3f4f6',
        borderTop: '3px solid #3b82f6',
        borderRadius: '50%',
        animation: 'spin 1s linear infinite'
      }}></div>
      <p style={{ color: '#6b7280', fontSize: '14px', lineHeight: '1.6', fontFamily: 'sans-serif' }}>Loading plan data...</p>
      <style jsx>{`
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
      `}</style>
    </div>
  );

  const renderStep1 = () => (
    <div className="form-section">
      <div className="form-header">
        <div className="form-header-content">
          <div className="gradient-icon">
            <RiFileUploadLine size={20} />
          </div>
          <h3>Plan Documents</h3>
        </div>
      </div>

      <div className="form-content">
        <div className="form-group">
          <label>
            Plan Documents (Optional)
            <Tooltip
              title="Upload documents related to this plan such as brochures, benefit summaries, or plan details (PDF, DOC, TXT, or Image files)"
              placement="top"
              arrow
              enterDelay={300}
              leaveDelay={200}
              slotProps={{
                tooltip: {
                  sx: {
                    bgcolor: '#374151',
                    color: 'white',
                    fontSize: '0.75rem',
                    maxWidth: '250px',
                    zIndex: 9999,
                    '& .MuiTooltip-arrow': {
                      color: '#374151',
                    },
                  },
                },
              }}
            >
              <span>
                <IoInformationCircle
                  className="form-tooltip-icon"
                  size={16}
                  title="Upload documents related to this plan such as brochures, benefit summaries, or plan details (PDF, DOC, TXT, or Image files)"
                />
              </span>
            </Tooltip>
          </label>
          <div className="file-upload-area">
            <input
              type="file"
              id="documents"
              multiple
              accept=".pdf,.doc,.docx,.txt,.jpg,.jpeg,.png"
              onChange={(e) => handleFileUpload(e.target.files)}
              className="file-input"
            />
            <label htmlFor="documents" className="file-upload-label">
              <HiOutlineCloudUpload size={20} />
              <span>Click to upload documents</span>
              <small>PDF, DOC, TXT, or Image files</small>
            </label>
          </div>
          {formData.documents.length > 0 && (
            <div className="uploaded-files">
              {formData.documents.map((file, index) => (
                <div key={index} className="uploaded-file">
                  <div className="gradient-icon-small">
                    <HiOutlineDocumentText size={16} />
                  </div>
                  <span className="file-name">{file.name}</span>
                  <span className="file-size">({(file.size / 1024).toFixed(1)} KB)</span>
                  <button
                    type="button"
                    className="remove-file"
                    onClick={() => removeDocument(index)}
                    title="Remove this document"
                  >
                    <HiOutlineX size={14} />
                  </button>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>

      <div className="form-navigation" style={{ justifyContent: 'flex-end' }}>
        <button
          className="nav-btn primary enabled"
          onClick={handleContinue}
        >
          Continue to Basic Info
        </button>
      </div>
    </div>
  );

  const renderStep2 = () => (
    <div className="form-section">
      <div className="form-header">
        <div className="form-header-content">
          <div className="gradient-icon">
            <RiHealthBookLine size={20} />
          </div>
          <h3>Basic Details</h3>
        </div>
      </div>

      <div className="form-content">
        {/* 1. Coverage Category */}
        <div className="form-group">
          <label htmlFor="coverageCategory">
            Coverage Category
            <Tooltip
              title="Select the main category of coverage this plan provides (e.g., Medical, Dental, Vision)"
              placement="top"
              arrow
              enterDelay={300}
              leaveDelay={200}
              slotProps={{
                tooltip: {
                  sx: {
                    bgcolor: '#374151',
                    color: 'white',
                    fontSize: '0.75rem',
                    maxWidth: '250px',
                    zIndex: 9999,
                    '& .MuiTooltip-arrow': {
                      color: '#374151',
                    },
                  },
                },
              }}
            >
              <span>
                <IoInformationCircle
                  className="form-tooltip-icon"
                  size={16}
                  title="Select the main category of coverage this plan provides (e.g., Medical, Dental, Vision)"
                />
              </span>
            </Tooltip>
          </label>
          <select
            id="coverageCategory"
            value={formData.coverageCategory}
            onChange={(e) => {
              console.log('Coverage category dropdown changed:', e.target.value);
              console.log('Current formData.coverageCategory before change:', formData.coverageCategory);

              // Update both coverageCategory and reset coverageType in a single state update
              const updatedData = {
                ...formData,
                coverageCategory: e.target.value,
                coverageType: '' // Reset coverage type when category changes
              };
              console.log('Updated form data (combined):', updatedData);
              setFormData(updatedData);

              // Debounced auto-save
              debouncedSave(updatedData);
            }}
            title="Choose the main category of benefits"
          >
            <option value="">Select coverage category</option>
            {(data?.coverageCategories || []).map(category => {
              console.log('Rendering category option:', category);
              return (
                <option key={category} value={category}>
                  {category}
                </option>
              );
            })}
          </select>
        </div>

        {/* 2. Coverage Type */}
        <div className="form-group">
          <label htmlFor="coverageType">
            Coverage Type
            <Tooltip
              title="Select the specific type of coverage within the category (e.g., PPO, HMO, EPO for Medical)"
              placement="top"
              arrow
              enterDelay={300}
              leaveDelay={200}
              slotProps={{
                tooltip: {
                  sx: {
                    bgcolor: '#374151',
                    color: 'white',
                    fontSize: '0.75rem',
                    maxWidth: '250px',
                    zIndex: 9999,
                    '& .MuiTooltip-arrow': {
                      color: '#374151',
                    },
                  },
                },
              }}
            >
              <span>
                <IoInformationCircle
                  className="form-tooltip-icon"
                  size={16}
                  title="Select the specific type of coverage within the category (e.g., PPO, HMO, EPO for Medical)"
                />
              </span>
            </Tooltip>
          </label>
          <select
            id="coverageType"
            value={formData.coverageType}
            onChange={(e) => handleInputChange('coverageType', e.target.value)}
            disabled={!formData.coverageCategory}
            title="Choose the specific type of benefits covered"
            style={{
              backgroundColor: !formData.coverageCategory ? '#f9fafb' : 'white',
              cursor: !formData.coverageCategory ? 'not-allowed' : 'pointer'
            }}
          >
            <option value="">Select coverage type</option>
            {formData.coverageCategory && data?.coverageMap?.[formData.coverageCategory]?.map(subType => (
              <option key={subType} value={subType}>
                {subType}
              </option>
            )) || []}
          </select>
        </div>

        {/* 3. Carrier */}
        <div className="form-group">
          <label htmlFor="carrier">
            Carrier
            <Tooltip
              title="Select the insurance carrier that provides this plan (e.g., Blue Shield, Kaiser)"
              placement="top"
              arrow
              enterDelay={300}
              leaveDelay={200}
              slotProps={{
                tooltip: {
                  sx: {
                    bgcolor: '#374151',
                    color: 'white',
                    fontSize: '0.75rem',
                    maxWidth: '250px',
                    zIndex: 9999,
                    '& .MuiTooltip-arrow': {
                      color: '#374151',
                    },
                  },
                },
              }}
            >
              <span>
                <IoInformationCircle
                  className="form-tooltip-icon"
                  size={16}
                  title="Select the insurance carrier that provides this plan (e.g., Blue Shield, Kaiser)"
                />
              </span>
            </Tooltip>
          </label>
          <select
            id="carrier"
            value={formData.carrier}
            onChange={(e) => handleInputChange('carrier', e.target.value)}
          >
            <option value="">Select carrier</option>
            {data?.carriers?.map(carrier => (
              <option key={carrier._id} value={carrier._id}>
                {carrier.displayName || carrier.carrierName}
              </option>
            )) || []}
          </select>
        </div>

        {/* 4. Plan Name */}
        <div className="form-group">
          <label htmlFor="planName">
            Plan Name
            <Tooltip
              title="Enter a clear, descriptive name that helps identify this plan (e.g., Blue Shield PPO 500). We'll check for duplicates automatically."
              placement="top"
              arrow
              enterDelay={300}
              leaveDelay={200}
              slotProps={{
                tooltip: {
                  sx: {
                    bgcolor: '#374151',
                    color: 'white',
                    fontSize: '0.75rem',
                    maxWidth: '250px',
                    zIndex: 9999,
                    '& .MuiTooltip-arrow': {
                      color: '#374151',
                    },
                  },
                },
              }}
            >
              <span>
                <IoInformationCircle
                  className="form-tooltip-icon"
                  size={16}
                  title="Enter a clear, descriptive name that helps identify this plan (e.g., Blue Shield PPO 500). We'll check for duplicates automatically."
                />
              </span>
            </Tooltip>
          </label>
          <div style={{ position: 'relative', width: '100%' }}>
            <input
              type="text"
              id="planName"
              placeholder="e.g. Blue Shield PPO 500"
              value={formData.planName}
              onChange={(e) => handleInputChange('planName', e.target.value)}
              style={{
                width: '100%',
                borderColor: planNameStatus.isDuplicate ? '#ef4444' :
                           planNameStatus.isChecking ? '#f59e0b' :
                           formData.planName && !planNameStatus.isDuplicate ? '#10b981' : '#d1d5db',
                paddingRight: '40px'
              }}
            />
            {/* Status indicator */}
            <div style={{
              position: 'absolute',
              right: '12px',
              top: '50%',
              transform: 'translateY(-50%)',
              display: 'flex',
              alignItems: 'center',
              fontSize: '14px',
              fontFamily: 'sans-serif'
            }}>
              {planNameStatus.isChecking && (
                <div style={{
                  width: '16px',
                  height: '16px',
                  border: '2px solid #f59e0b',
                  borderTop: '2px solid transparent',
                  borderRadius: '50%',
                  animation: 'spin 1s linear infinite'
                }}></div>
              )}
              {!planNameStatus.isChecking && formData.planName && !planNameStatus.isDuplicate && (
                <span style={{ color: '#10b981', fontWeight: '600', fontFamily: 'sans-serif' }}>✓</span>
              )}
              {planNameStatus.isDuplicate && (
                <span style={{ color: '#ef4444', fontWeight: '600', fontFamily: 'sans-serif' }}>✗</span>
              )}
            </div>
          </div>

          {/* Status messages */}
          {planNameStatus.isDuplicate && planNameStatus.existingPlan && (
            <div style={{
              marginTop: '8px',
              padding: '8px 12px',
              backgroundColor: '#fef2f2',
              border: '1px solid #fecaca',
              borderRadius: '6px',
              fontSize: '13px',
              color: '#dc2626',
              lineHeight: '1.4',
              fontFamily: 'sans-serif'
            }}>
              <strong>Plan name already exists:</strong> &quot;{planNameStatus.existingPlan.planName}&quot;
              {planNameStatus.existingPlan.planCode && ` (${planNameStatus.existingPlan.planCode})`}
              <br />
              <span style={{ fontSize: '12px', color: '#7f1d1d', fontFamily: 'sans-serif' }}>
                Please choose a different name.
              </span>
            </div>
          )}

          {planNameStatus.error && (
            <div style={{
              marginTop: '8px',
              padding: '8px 12px',
              backgroundColor: '#fef3cd',
              border: '1px solid #fde68a',
              borderRadius: '6px',
              fontSize: '13px',
              color: '#92400e',
              lineHeight: '1.4',
              fontFamily: 'sans-serif'
            }}>
              <strong>Warning:</strong> {planNameStatus.error}
            </div>
          )}

          {formData.planName && !planNameStatus.isChecking && !planNameStatus.isDuplicate && !planNameStatus.error && (
            <div style={{
              marginTop: '8px',
              padding: '8px 12px',
              backgroundColor: '#f0fdf4',
              border: '1px solid #bbf7d0',
              borderRadius: '6px',
              fontSize: '13px',
              color: '#166534',
              lineHeight: '1.4',
              fontFamily: 'sans-serif'
            }}>
              ✓ Plan name is available
            </div>
          )}
        </div>

        {/* 5. Plan Code */}
        <div className="form-group">
          <label htmlFor="planCode">
            Plan Code
            <Tooltip
              title="Unique identifier for this plan used in systems and reports (e.g., BS-PPO-500). We'll check for duplicates automatically."
              placement="top"
              arrow
              enterDelay={300}
              leaveDelay={200}
              slotProps={{
                tooltip: {
                  sx: {
                    bgcolor: '#374151',
                    color: 'white',
                    fontSize: '0.75rem',
                    maxWidth: '250px',
                    zIndex: 9999,
                    '& .MuiTooltip-arrow': {
                      color: '#374151',
                    },
                  },
                },
              }}
            >
              <span>
                <IoInformationCircle
                  className="form-tooltip-icon"
                  size={16}
                  title="Unique identifier for this plan used in systems and reports (e.g., BS-PPO-500). We'll check for duplicates automatically."
                />
              </span>
            </Tooltip>
          </label>
          <div style={{ position: 'relative', width: '100%' }}>
            <input
              type="text"
              id="planCode"
              placeholder="e.g. BS-PPO-500"
              value={formData.planCode}
              onChange={(e) => handleInputChange('planCode', e.target.value)}
              style={{
                width: '100%',
                borderColor: planCodeStatus.isDuplicate ? '#ef4444' :
                           planCodeStatus.isChecking ? '#f59e0b' :
                           formData.planCode && !planCodeStatus.isDuplicate ? '#10b981' : '#d1d5db',
                paddingRight: '40px'
              }}
            />
            {/* Status indicator */}
            <div style={{
              position: 'absolute',
              right: '12px',
              top: '50%',
              transform: 'translateY(-50%)',
              display: 'flex',
              alignItems: 'center',
              fontSize: '14px',
              fontFamily: 'sans-serif'
            }}>
              {planCodeStatus.isChecking && (
                <div style={{
                  width: '16px',
                  height: '16px',
                  border: '2px solid #f59e0b',
                  borderTop: '2px solid transparent',
                  borderRadius: '50%',
                  animation: 'spin 1s linear infinite'
                }}></div>
              )}
              {!planCodeStatus.isChecking && formData.planCode && !planCodeStatus.isDuplicate && (
                <span style={{ color: '#10b981', fontWeight: '600', fontFamily: 'sans-serif' }}>✓</span>
              )}
              {planCodeStatus.isDuplicate && (
                <span style={{ color: '#ef4444', fontWeight: '600', fontFamily: 'sans-serif' }}>✗</span>
              )}
            </div>
          </div>

          {/* Status messages */}
          {planCodeStatus.isDuplicate && planCodeStatus.existingPlan && (
            <div style={{
              marginTop: '8px',
              padding: '8px 12px',
              backgroundColor: '#fef2f2',
              border: '1px solid #fecaca',
              borderRadius: '6px',
              fontSize: '13px',
              color: '#dc2626',
              lineHeight: '1.4',
              fontFamily: 'sans-serif'
            }}>
              <strong>Plan code already exists:</strong> &quot;{planCodeStatus.existingPlan.planCode}&quot;
              {planCodeStatus.existingPlan.planName && ` (${planCodeStatus.existingPlan.planName})`}
              <br />
              <span style={{ fontSize: '12px', color: '#7f1d1d', fontFamily: 'sans-serif' }}>
                Please choose a different code.
              </span>
            </div>
          )}

          {planCodeStatus.error && (
            <div style={{
              marginTop: '8px',
              padding: '8px 12px',
              backgroundColor: '#fef3cd',
              border: '1px solid #fde68a',
              borderRadius: '6px',
              fontSize: '13px',
              color: '#92400e',
              lineHeight: '1.4',
              fontFamily: 'sans-serif'
            }}>
              <strong>Warning:</strong> {planCodeStatus.error}
            </div>
          )}

          {formData.planCode && !planCodeStatus.isChecking && !planCodeStatus.isDuplicate && !planCodeStatus.error && (
            <div style={{
              marginTop: '8px',
              padding: '8px 12px',
              backgroundColor: '#f0fdf4',
              border: '1px solid #bbf7d0',
              borderRadius: '6px',
              fontSize: '13px',
              color: '#166534',
              lineHeight: '1.4',
              fontFamily: 'sans-serif'
            }}>
              ✓ Plan code is available
            </div>
          )}
        </div>

        {/* 6. Plan Type */}
        <div className="form-group">
          <label htmlFor="planType">
            Plan Type
            <Tooltip
              title="Choose the type of health plan structure (PPO, HMO, EPO, POS, etc.)"
              placement="top"
              arrow
              enterDelay={300}
              leaveDelay={200}
              slotProps={{
                tooltip: {
                  sx: {
                    bgcolor: '#374151',
                    color: 'white',
                    fontSize: '0.75rem',
                    maxWidth: '250px',
                    zIndex: 9999,
                    '& .MuiTooltip-arrow': {
                      color: '#374151',
                    },
                  },
                },
              }}
            >
              <span>
                <IoInformationCircle
                  className="form-tooltip-icon"
                  size={16}
                  title="Choose the type of health plan structure (PPO, HMO, EPO, POS, etc.)"
                />
              </span>
            </Tooltip>
          </label>
          <select
            id="planType"
            value={formData.planType}
            onChange={(e) => handleInputChange('planType', e.target.value)}
            title="Select the plan structure (PPO, HMO, etc.)"
          >
            <option value="">Select type</option>
            {data?.planTypes?.map(type => (
              <option key={type} value={type}>
                {type}
              </option>
            )) || []}
          </select>
        </div>

        {/* 7. Metal Tier (Optional) */}
        <div className="form-group">
          <label htmlFor="metalTier">
            Metal Tier (Optional)
            <Tooltip
              title="Choose the coverage level (Bronze, Silver, Gold, Platinum). This field is optional and typically applies to medical plans."
              placement="top"
              arrow
              enterDelay={300}
              leaveDelay={200}
              slotProps={{
                tooltip: {
                  sx: {
                    bgcolor: '#374151',
                    color: 'white',
                    fontSize: '0.75rem',
                    maxWidth: '250px',
                    zIndex: 9999,
                    '& .MuiTooltip-arrow': {
                      color: '#374151',
                    },
                  },
                },
              }}
            >
              <span>
                <IoInformationCircle
                  className="form-tooltip-icon"
                  size={16}
                  title="Choose the coverage level (Bronze, Silver, Gold, Platinum). This field is optional and typically applies to medical plans."
                />
              </span>
            </Tooltip>
          </label>
          <select
            id="metalTier"
            value={formData.metalTier}
            onChange={(e) => handleInputChange('metalTier', e.target.value)}
            title="Choose the coverage level (Bronze, Silver, Gold, Platinum) - Optional"
          >
            <option value="">Select tier (optional)</option>
            {data?.metalTiers?.map(tier => (
              <option key={tier} value={tier}>
                {tier}
              </option>
            )) || []}
          </select>
        </div>
      </div>

      <div className="form-navigation">
        <Link href="/ai-enroller" prefetch={true}>
          <button className="nav-btn secondary">
            <HiOutlineArrowLeft size={16} />
            Back to Main
          </button>
        </Link>

        <button
          className={`nav-btn primary ${isStep2Valid ? 'enabled' : 'disabled'}`}
          onClick={handleContinue}
          disabled={!isStep2Valid}
          title="Continue to description and video"
        >
          Continue to Description
        </button>
      </div>
    </div>
  );

  const renderStep3 = () => (
    <div className="form-section">
      <div className="form-header">
        <div className="form-header-content">
          <div className="gradient-icon">
            <RiVideoLine size={20} />
          </div>
          <h3>Description & Video</h3>
        </div>
      </div>

      <div className="form-content">
        <div className="form-group">
          <label htmlFor="videoUrl">
            Video URL (Optional)
            <Tooltip
              title="Add a YouTube or Vimeo URL to help explain plan benefits and features"
              placement="top"
              arrow
              enterDelay={300}
              leaveDelay={200}
              slotProps={{
                tooltip: {
                  sx: {
                    bgcolor: '#374151',
                    color: 'white',
                    fontSize: '0.75rem',
                    maxWidth: '250px',
                    zIndex: 9999,
                    '& .MuiTooltip-arrow': {
                      color: '#374151',
                    },
                  },
                },
              }}
            >
              <span>
                <IoInformationCircle
                  className="form-tooltip-icon"
                  size={16}
                  title="Add a YouTube or Vimeo URL to help explain plan benefits and features"
                />
              </span>
            </Tooltip>
          </label>
          <input
            type="url"
            id="videoUrl"
            placeholder="e.g. https://youtube.com/watch?v=..."
            value={formData.videoUrl}
            onChange={(e) => handleInputChange('videoUrl', e.target.value)}
          />
          <small className="field-hint">Add a video to help explain plan benefits and features</small>
        </div>

        <div className="form-group">
          <label htmlFor="description">
            Plan Description
            <Tooltip
              title="Provide a detailed description of the plan benefits, coverage, and key features"
              placement="top"
              arrow
              enterDelay={300}
              leaveDelay={200}
              slotProps={{
                tooltip: {
                  sx: {
                    bgcolor: '#374151',
                    color: 'white',
                    fontSize: '0.75rem',
                    maxWidth: '250px',
                    zIndex: 9999,
                    '& .MuiTooltip-arrow': {
                      color: '#374151',
                    },
                  },
                },
              }}
            >
              <span>
                <IoInformationCircle
                  className="form-tooltip-icon"
                  size={16}
                  title="Provide a detailed description of the plan benefits, coverage, and key features"
                />
              </span>
            </Tooltip>
          </label>
          <textarea
            id="description"
            placeholder="Describe the plan benefits and features..."
            value={formData.description}
            onChange={(e) => handleInputChange('description', e.target.value)}
            rows={4}
          />
          <small className="field-hint">Describe the key benefits, coverage details, and what makes this plan unique</small>
        </div>

        <div className="form-group">
          <label>
            Plan Highlights
            <Tooltip
              title="Add key selling points and highlights that make this plan attractive (e.g., Low deductible, Nationwide network, No referrals needed)"
              placement="top"
              arrow
              enterDelay={300}
              leaveDelay={200}
              slotProps={{
                tooltip: {
                  sx: {
                    bgcolor: '#374151',
                    color: 'white',
                    fontSize: '0.75rem',
                    maxWidth: '250px',
                    zIndex: 9999,
                    '& .MuiTooltip-arrow': {
                      color: '#374151',
                    },
                  },
                },
              }}
            >
              <span>
                <IoInformationCircle
                  className="form-tooltip-icon"
                  size={16}
                  title="Add key selling points and highlights that make this plan attractive (e.g., Low deductible, Nationwide network, No referrals needed)"
                />
              </span>
            </Tooltip>
          </label>
          <small className="field-hint">Add the most important features that make this plan attractive</small>
          {formData.highlights.map((highlight, index) => (
            <div key={index} className="highlight-input">
              <input
                type="text"
                placeholder="e.g. Low deductible, Nationwide network"
                value={highlight}
                onChange={(e) => updateHighlight(index, e.target.value)}
                title="Enter a key benefit or feature"
              />
              {formData.highlights.length > 1 && (
                <button
                  type="button"
                  className="remove-highlight"
                  onClick={() => removeHighlight(index)}
                  title="Remove this highlight"
                >
                  <HiOutlineX size={14} />
                </button>
              )}
            </div>
          ))}
          <button
            type="button"
            className="add-highlight"
            onClick={addHighlight}
            title="Add another highlight"
          >
            <HiOutlinePlus size={16} />
            Add Highlight
          </button>
        </div>
      </div>

      <div className="form-navigation">
        <button className="nav-btn secondary" onClick={handleBack} title="Go back to basic information">
          <HiOutlineArrowLeft size={16} />
          Back
        </button>

        <button
          className={`nav-btn primary ${isStep3Valid ? 'enabled' : 'disabled'}`}
          onClick={handleContinue}
          disabled={!isStep3Valid}
          title="Continue to preview your plan"
        >
          Preview Plan
        </button>
      </div>
    </div>
  );

  const renderStep4 = () => (
    <div className="form-section">
      <div className="form-header">
        <div className="form-header-content">
          <div className="gradient-icon">
            <RiCheckboxCircleLine size={20} />
          </div>
          <h3>AI-Powered Plan Preview</h3>
        </div>
        <div className="ready-badge" title="All required information has been provided">
          <div>
            <HiOutlineCheckCircle size={16} />
          </div>
          Ready to Create
        </div>
      </div>

      <div className="review-content">
        <div className="review-section">
          <div className="review-section-header">
            <div className="gradient-icon-small">
              <RiHealthBookLine size={18} />
            </div>
            <h4>Plan Information</h4>
          </div>
          <div className="review-items">
            <div className="review-item">
              <span className="review-label" title="The name of this plan">Plan Name:</span>
              <span className="review-value">{formData.planName}</span>
            </div>
            <div className="review-item">
              <span className="review-label" title="Unique identifier for this plan">Plan Code:</span>
              <span className="review-value plan-code">{formData.planCode}</span>
            </div>
            <div className="review-item">
              <span className="review-label" title="Insurance carrier providing this plan">Carrier:</span>
              <span className="review-value">
                {data?.carriers?.find(c => c._id === formData.carrier)?.displayName ||
                 data?.carriers?.find(c => c._id === formData.carrier)?.carrierName || 'Unknown'}
              </span>
            </div>
            <div className="review-item">
              <span className="review-label" title="Type of health plan structure">Plan Type:</span>
              <span className="review-value">{formData.planType}</span>
            </div>
            <div className="review-item">
              <span className="review-label" title="Type of coverage provided">Coverage Type:</span>
              <span className="review-value">{formData.coverageType}</span>
            </div>
            <div className="review-item">
              <span className="review-label" title="Metal tier level indicating coverage level">Metal Tier:</span>
              <span className="review-value metal-tier">{formData.metalTier}</span>
            </div>
          </div>
        </div>

        {(formData.videoUrl || formData.documents.length > 0) && (
          <div className="review-section">
            <div className="review-section-header">
              <div className="gradient-icon-small">
                <HiOutlineDocumentText size={18} />
              </div>
              <h4>Media & Documents</h4>
            </div>
            <div className="review-items">
              {formData.videoUrl && (
                <div className="review-item full-width">
                  <span className="review-label" title="Video URL for plan explanation">Video URL:</span>
                  <a href={formData.videoUrl} target="_blank" rel="noopener noreferrer" className="review-link">
                    {formData.videoUrl}
                  </a>
                </div>
              )}
              {formData.documents.length > 0 && (
                <div className="review-item full-width">
                  <span className="review-label" title="Documents uploaded for this plan">Documents:</span>
                  <div className="review-documents">
                    {formData.documents.map((doc, index) => (
                      <div key={index} className="review-document">
                        <div className="gradient-icon-small">
                          <HiOutlineDocumentText size={16} />
                        </div>
                        <span>{doc.name}</span>
                        <small>({(doc.size / 1024).toFixed(1)} KB)</small>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>
        )}

        <div className="review-section">
          <div className="review-section-header">
            <div className="gradient-icon-small">
              <RiFileTextLine size={18} />
            </div>
            <h4>Description & Highlights</h4>
          </div>
          <div className="review-items">
            <div className="review-item full-width">
              <span className="review-label" title="Detailed plan description">Description:</span>
              <p className="review-description">{formData.description}</p>
            </div>
            <div className="review-item full-width">
              <span className="review-label" title="Key plan features and benefits">Highlights:</span>
              <ul className="review-highlights">
                {formData.highlights.filter(h => h.trim()).map((highlight, index) => (
                  <li key={index}>{highlight}</li>
                ))}
              </ul>
            </div>
          </div>
        </div>

        <div className="create-confirmation">
          <div className="confirmation-icon">
            <div className="gradient-icon">
              <RiCheckboxCircleLine size={24} />
            </div>
          </div>
          <div className="confirmation-text">
            <h4>Ready to Create Plan</h4>
            <p>Your new plan &quot;{formData.planName}&quot; will be added to your catalog and available for assignment to employer groups.</p>
          </div>
        </div>
      </div>

      <div className="form-navigation">
        <button className="nav-btn secondary" onClick={handleBack} title="Go back to description and video">
          <HiOutlineArrowLeft size={16} />
          Back
        </button>

        <button
          className="nav-btn primary enabled"
          onClick={handleCreatePlan}
          title="Create this plan and add it to your catalog"
        >
          Create Plan
        </button>
      </div>
    </div>
  );

  const renderStep5 = () => (
    <div className="form-section success-section">
      <div className="success-content">
        <div className="success-icon">
          <div className="gradient-icon-large">
            <RiShieldCheckLine size={32} />
          </div>
        </div>
        <h3>Plan Created Successfully!</h3>
        <p>Your plan &apos;{formData.planName}&apos; is now available in your catalog.</p>

        {/* Plan Details Card */}
        {createdPlan && (
          <div className="plan-details-card">
            <div className="plan-details-header">
              <div className="gradient-icon">
                <RiHealthBookLine size={15} />
              </div>
              <h4>Plan Details</h4>
            </div>
            <div className="plan-details-content">
              <div className="plan-detail-item">
                <span className="detail-label">Plan ID:</span>
                <span className="detail-value plan-id">{createdPlan._id}</span>
              </div>
              <div className="plan-detail-item">
                <span className="detail-label">Plan Code:</span>
                <span className="detail-value plan-code">{createdPlan.planCode}</span>
              </div>
              <div className="plan-detail-item">
                <span className="detail-label">Status:</span>
                <span className="detail-value status-active">{createdPlan.status || 'Active'}</span>
              </div>
              <div className="plan-detail-item">
                <span className="detail-label">Created:</span>
                <span className="detail-value">{createdPlan.createdAt ? new Date(createdPlan.createdAt).toLocaleString() : 'Just now'}</span>
              </div>
              <div className="plan-detail-item">
                <span className="detail-label">Carrier:</span>
                <span className="detail-value">
                  {data?.carriers.find(c => c._id === (createdPlan.carrierId || createdPlan.carrier))?.displayName ||
                   data?.carriers.find(c => c._id === (createdPlan.carrierId || createdPlan.carrier))?.carrierName || 'Unknown'}
                </span>
              </div>
            </div>
          </div>
        )}

        <div className="success-actions">
          <Link href="/ai-enroller/manage-groups" prefetch={true}>
            <button className="nav-btn primary">
              Assign to Group Now
            </button>
          </Link>

          <Link href="/ai-enroller" prefetch={true}>
            <button className="nav-btn secondary">
              Back to Main Menu
            </button>
          </Link>
        </div>
      </div>
    </div>
  );

  const renderStepContent = () => {
    switch (currentStep) {
      case 1:
        return renderStep1();
      case 2:
        return renderStep2();
      case 3:
        return renderStep3();
      case 4:
        return renderStep4();
      case 5:
        return renderStep5();
      default:
        return renderStep1();
    }
  };

  // Show loading state while data is being fetched
  if (isLoading || !data) {
    return (
      <div className="create-plan-wrapper">
        <div className="create-plan-page">
          <LoadingSpinner />
        </div>
      </div>
    );
  }

  const getStepMessage = () => {
    switch (currentStep) {
      case 1:
        return {
          title: "Hi! I'm Brea, your AI Benefits Assistant. Let's create an amazing plan together! 😊",
          subtitle: "I'll help you set up the basic plan information including name, carrier, and coverage details. This should only take a few minutes!"
        };
      case 2:
        return {
          title: "Great progress! Now let's add some media to make your plan shine ✨",
          subtitle: "You can upload videos, brochures, or any documents that help explain your plan. Don't worry, this step is completely optional!"
        };
      case 3:
        return {
          title: "Perfect! Now tell me what makes this plan special 🌟",
          subtitle: "Help me understand the key benefits and highlights. I'll use this to create compelling descriptions that really sell your plan!"
        };
      case 4:
        return {
          title: "Almost there! Let's review everything before we launch your plan 🚀",
          subtitle: "Take a moment to review all the details. Once you're happy with everything, I'll create your plan and make it available immediately!"
        };
      case 5:
        return {
          title: "Congratulations! Your plan is now live and ready to go! 🎉",
          subtitle: "I've successfully created your plan and it's now available for assignment to employer groups. Great work!"
        };
      default:
        return {
          title: "Hi there! Ready to create something amazing? 💫",
          subtitle: "I'm here to help you build the perfect benefits plan. Let's get started!"
        };
    }
  };

  const getPageIcon = (stepNumber: number) => {
    switch (stepNumber) {
      case 1: return (
        <div className="gradient-icon-nav">
          <HiOutlineDocumentText />
        </div>
      );
      case 2: return (
        <div className="gradient-icon-nav">
          <RiHealthBookLine />
        </div>
      );
      case 3: return (
        <div className="gradient-icon-nav">
          <RiFileTextLine />
        </div>
      );
      case 4: return (
        <div className="gradient-icon-nav">
          <RiCheckboxCircleLine />
        </div>
      );
      case 5: return (
        <div className="gradient-icon-nav">
          <RiShieldCheckLine />
        </div>
      );
      default: return (
        <div className="gradient-icon-nav">
          <RiHealthBookLine />
        </div>
      );
    }
  };

  return (
    <ProtectedRoute>
      <div className="create-plan-wrapper">
        <EnrollmentHeader />

        <CreatePlanOptimizer />

        

        <div className="create-plan-page">
          {/* Combined Progress Header Component */}
        <div className="progress-header-component">
          <div className="progress-header">
            <div className="progress-title">
              <h1 className="page-title">Plan Creation Progress</h1>
              <span className="subtitle-text">{currentStep} of 5</span>
            </div>
            <div className="progress-bar-container">
              <div
                className="progress-bar-fill"
                style={{ width: `${(currentStep / 5) * 100}%` }}
              ></div>
            </div>

            {/* Page Navigation */}
            <div className="page-navigation">
              {steps.map((step) => (
                <button
                  key={step.number}
                  className={`page-nav-item ${step.active ? 'active' : ''} ${step.completed ? 'completed' : ''}`}
                  onClick={() => currentStep < 5 ? setCurrentStep(step.number) : null}
                  disabled={step.number > currentStep || currentStep === 5}
                >
                  <span className="nav-icon">{getPageIcon(step.number)}</span>
                  {step.title}
                </button>
              ))}
            </div>
          </div>
        </div>
        {/* AI Assistant Message */}
        <div className="ai-assistant-message">
          <div className="ai-message-content">
            <div className="ai-avatar">
              <div className="avatar-circle">
                <Image
                  src="/brea.png"
                  alt="Brea - AI Assistant"
                  className="brea-avatar"
                  width={48}
                  height={48}
                  priority
                />
              </div>
            </div>
            <div className="chat-bubble">
              <div className="chat-message">
                {getStepMessage().title}
              </div>
              <div className="chat-subtitle">
                {getStepMessage().subtitle}
              </div>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="main-content">
          <div className="form-container">
            {renderStepContent()}
          </div>
        </div>


        </div>
      </div>
    </ProtectedRoute>
  );
};

export default CreatePlanPage;
