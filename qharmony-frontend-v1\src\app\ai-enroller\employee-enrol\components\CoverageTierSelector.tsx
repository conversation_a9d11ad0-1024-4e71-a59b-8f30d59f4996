import React, { useState, useEffect } from 'react';

interface CoverageTier {
  tierName: string;
  employeeCost: number;
  totalCost: number;
  employerCost: number;
}

interface CoverageTierSelectorProps {
  planName: string;
  availableTiers: CoverageTier[];
  preferredTier: string;
  onTierSelect: (tier: CoverageTier | null) => void;
  selectedTier?: string;
  category: string;
}

const CoverageTierSelector: React.FC<CoverageTierSelectorProps> = ({
  planName,
  availableTiers,
  preferredTier,
  onTierSelect,
  selectedTier,
  category
}) => {
  const [currentSelectedTier, setCurrentSelectedTier] = useState<string>('');
  const [showFallbackMessage, setShowFallbackMessage] = useState(false);

  // Check if preferred tier is available
  const preferredTierAvailable = availableTiers.some(tier => tier.tierName === preferredTier);
  
  // Find the preferred tier or fallback to Employee Only
  const defaultTier = preferredTierAvailable 
    ? preferredTier 
    : availableTiers.find(tier => tier.tierName === 'Employee Only')?.tierName || availableTiers[0]?.tierName || '';

  useEffect(() => {
    if (selectedTier) {
      setCurrentSelectedTier(selectedTier);
    } else if (defaultTier) {
      setCurrentSelectedTier(defaultTier);
      // Auto-select the default tier
      const tierData = availableTiers.find(tier => tier.tierName === defaultTier);
      if (tierData) {
        onTierSelect(tierData);
      }
    }

    // Show fallback message if preferred tier is not available
    if (!preferredTierAvailable && preferredTier && availableTiers.length > 0) {
      setShowFallbackMessage(true);
    }
  }, [selectedTier, defaultTier, preferredTierAvailable, preferredTier, availableTiers, onTierSelect]);

  const handleTierChange = (tierName: string) => {
    setCurrentSelectedTier(tierName);
    const tierData = availableTiers.find(tier => tier.tierName === tierName);
    onTierSelect(tierData || null);
    setShowFallbackMessage(false); // Hide message once user makes a selection
  };

  if (availableTiers.length === 0) {
    return (
      <div style={{
        backgroundColor: '#fef2f2',
        border: '1px solid #fecaca',
        borderRadius: '8px',
        padding: '16px',
        marginTop: '12px'
      }}>
        <p style={{ color: '#dc2626', margin: 0, fontSize: '14px' }}>
          No coverage tiers available for this plan.
        </p>
      </div>
    );
  }

  return (
    <div style={{ marginTop: '16px' }}>
      {/* Fallback Message */}
      {showFallbackMessage && (
        <div style={{
          backgroundColor: '#fef3c7',
          border: '1px solid #fbbf24',
          borderRadius: '8px',
          padding: '12px',
          marginBottom: '16px'
        }}>
          <p style={{ 
            color: '#92400e', 
            margin: 0, 
            fontSize: '14px',
            fontWeight: '500'
          }}>
            ⚠️ Your preferred coverage tier &ldquo;{preferredTier}&rdquo; is not available for this {category.toLowerCase()} plan.
            Please select from the available options below:
          </p>
        </div>
      )}

      {/* Modern Coverage Tier Selection */}
      <div style={{
        backgroundColor: 'white',
        border: '1px solid #e5e7eb',
        borderRadius: '12px',
        padding: '20px',
        boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)'
      }}>
        <h4 style={{
          fontSize: '16px',
          fontWeight: '600',
          color: '#111827',
          margin: '0 0 16px 0',
          display: 'flex',
          alignItems: 'center',
          gap: '8px'
        }}>
          <span style={{
            width: '24px',
            height: '24px',
            backgroundColor: '#3b82f6',
            borderRadius: '6px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            fontSize: '12px',
            color: 'white',
            fontWeight: '700'
          }}>
            💰
          </span>
          Choose Coverage for {planName}
        </h4>

        <div style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
          {availableTiers.map((tier) => {
            const isRecommended = tier.tierName === preferredTier;
            const isSelected = currentSelectedTier === tier.tierName;

            return (
              <div
                key={tier.tierName}
                onClick={() => handleTierChange(tier.tierName)}
                style={{
                  position: 'relative',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'space-between',
                  padding: '16px 20px',
                  backgroundColor: isSelected ? '#f0f9ff' : 'white',
                  border: isSelected ? '2px solid #0ea5e9' : isRecommended ? '2px solid #10b981' : '1px solid #e5e7eb',
                  borderRadius: '12px',
                  cursor: 'pointer',
                  transition: 'all 0.3s ease',
                  boxShadow: isSelected ? '0 4px 12px rgba(14, 165, 233, 0.15)' : isRecommended ? '0 4px 12px rgba(16, 185, 129, 0.15)' : '0 1px 3px rgba(0, 0, 0, 0.05)',
                  transform: isSelected ? 'translateY(-2px)' : 'none'
                }}
                onMouseEnter={(e) => {
                  if (!isSelected) {
                    e.currentTarget.style.backgroundColor = '#f8fafc';
                    e.currentTarget.style.transform = 'translateY(-1px)';
                    e.currentTarget.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.1)';
                  }
                }}
                onMouseLeave={(e) => {
                  if (!isSelected) {
                    e.currentTarget.style.backgroundColor = 'white';
                    e.currentTarget.style.transform = 'none';
                    e.currentTarget.style.boxShadow = '0 1px 3px rgba(0, 0, 0, 0.05)';
                  }
                }}
              >
                {/* Recommended Badge */}
                {isRecommended && (
                  <div style={{
                    position: 'absolute',
                    top: '-8px',
                    right: '16px',
                    backgroundColor: '#10b981',
                    color: 'white',
                    fontSize: '11px',
                    fontWeight: '600',
                    padding: '4px 8px',
                    borderRadius: '6px',
                    textTransform: 'uppercase',
                    letterSpacing: '0.5px'
                  }}>
                    ⭐ Recommended
                  </div>
                )}

                {/* Selected Indicator */}
                {isSelected && (
                  <div style={{
                    position: 'absolute',
                    top: '12px',
                    right: '12px',
                    width: '24px',
                    height: '24px',
                    backgroundColor: '#0ea5e9',
                    borderRadius: '50%',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    color: 'white',
                    fontSize: '14px',
                    fontWeight: '700'
                  }}>
                    ✓
                  </div>
                )}

                <div style={{ display: 'flex', alignItems: 'center', gap: '16px', flex: 1 }}>
                  {/* Radio Button */}
                  <div style={{
                    width: '20px',
                    height: '20px',
                    borderRadius: '50%',
                    border: isSelected ? '6px solid #0ea5e9' : '2px solid #d1d5db',
                    backgroundColor: isSelected ? 'white' : 'transparent',
                    transition: 'all 0.2s ease'
                  }} />

                  <div style={{ flex: 1 }}>
                    <div style={{
                      fontSize: '16px',
                      fontWeight: '600',
                      color: isSelected ? '#0ea5e9' : '#111827',
                      marginBottom: '4px'
                    }}>
                      {tier.tierName}
                    </div>
                    <div style={{
                      fontSize: '13px',
                      color: '#6b7280',
                      display: 'flex',
                      gap: '16px'
                    }}>
                      <span>Total: ${tier.totalCost.toFixed(2)}</span>
                      <span>Employer: ${tier.employerCost.toFixed(2)}</span>
                    </div>
                  </div>
                </div>

                <div style={{
                  textAlign: 'right',
                  marginRight: isSelected ? '32px' : '0'
                }}>
                  <div style={{
                    fontSize: '20px',
                    fontWeight: '700',
                    color: isSelected ? '#0ea5e9' : '#111827',
                    marginBottom: '2px'
                  }}>
                    ${tier.employeeCost.toFixed(2)}
                  </div>
                  <div style={{
                    fontSize: '12px',
                    color: '#6b7280',
                    fontWeight: '500'
                  }}>
                    per paycheck
                  </div>
                </div>
              </div>
            );
          })}
        </div>

        {/* Modern Selected Tier Summary */}
        {currentSelectedTier && (
          <div style={{
            marginTop: '16px',
            padding: '16px',
            background: 'linear-gradient(135deg, #ecfdf5 0%, #f0fdf4 100%)',
            border: '1px solid #10b981',
            borderRadius: '12px',
            boxShadow: '0 2px 8px rgba(16, 185, 129, 0.1)'
          }}>
            <div style={{
              display: 'flex',
              alignItems: 'center',
              gap: '12px'
            }}>
              <div style={{
                width: '32px',
                height: '32px',
                backgroundColor: '#10b981',
                borderRadius: '50%',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                color: 'white',
                fontSize: '16px',
                fontWeight: '700'
              }}>
                ✓
              </div>
              <div style={{ flex: 1 }}>
                <div style={{
                  fontSize: '16px',
                  fontWeight: '600',
                  color: '#065f46',
                  marginBottom: '4px'
                }}>
                  {currentSelectedTier} Selected
                </div>
                <div style={{
                  fontSize: '14px',
                  color: '#047857',
                  fontWeight: '500'
                }}>
                  You pay ${availableTiers.find(t => t.tierName === currentSelectedTier)?.employeeCost.toFixed(2)} per paycheck
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default CoverageTierSelector;
