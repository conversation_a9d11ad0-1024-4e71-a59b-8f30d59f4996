'use client';

import React, { useState, useEffect } from 'react';
import Image from 'next/image';
import { CheckCir<PERSON>, Play, BarChart3 } from 'lucide-react';
// import { FloatingHelp } from './FloatingHelp'; // Removed - using global FloatingChatButton instead
import { VideoPlayer } from './VideoPlayer';
import { CompareModal } from './CompareModal';
import CoverageTierSelector from './CoverageTierSelector';
import {
  storeSelectedPlan,
  storeWaivedCoverage,
  removeSelectedPlan,
  removeWaivedCoverage,
  getPreferredCoverageTier,
  getStoredPlan,
  isCategoryWaived
} from '../utils/planStorageUtils';
// import ChatModal from './ChatModal'; // Not needed - using FloatingHelp instead
// import CustomModal from './CustomModal'; // Not needed - using inline modal

interface Plan {
  id: string;
  name: string;
  cost: number;
  features: string[];
  recommended?: boolean;
  originalPlan?: any; // Original plan data from API
  coverageTiers?: any[]; // Coverage tiers for cost calculation
  selectedTier?: any; // Selected coverage tier data
}

interface DynamicPlanPageProps {
  category: string; // e.g., 'Dental', 'Vision', 'Life Insurance'
  plans: any[]; // Plan assignments from API
  selectedCoverageTier?: string; // Coverage tier selected in personalization
  themeIndex: number; // 0-3 for rotating themes
  onPlanSelect: (plan: Plan | null) => void;
}

// Theme configuration based on requirements
const THEMES = [
  {
    primary: '#1E90FF', // Blue
    accent: '#E6F0FF',
    name: 'Blue'
  },
  {
    primary: '#32CD32', // Green
    accent: '#E6FFE6',
    name: 'Green'
  },
  {
    primary: '#FF8C00', // Orange
    accent: '#FFF3E6',
    name: 'Orange'
  },
  {
    primary: '#800080', // Purple
    accent: '#F5E6FF',
    name: 'Purple'
  }
];

const DynamicPlanPage: React.FC<DynamicPlanPageProps> = ({ 
  category, 
  plans, 
  selectedCoverageTier, 
  themeIndex, 
  onPlanSelect 
}) => {
  const [selectedPlan, setSelectedPlan] = useState<string | null>(null);
  const [selectedTierData, setSelectedTierData] = useState<any>(null);
  const [showHelp, setShowHelp] = useState(false);
  const [showVideo, setShowVideo] = useState(false);
  const [showWaiveConfirm, setShowWaiveConfirm] = useState(false);
  const [waiveReason, setWaiveReason] = useState<string>('');
  const [showCompare, setShowCompare] = useState(false);

  // Get preferred coverage tier
  const preferredTier = getPreferredCoverageTier();

  // Filter plan assignments to only show those that have the preferred tier or at least some coverage tiers
  const getFilteredPlans = (plans: any[]): any[] => {
    return plans.filter(plan => {
      // Check if plan has coverage tiers
      if (!plan.coverageTiers || plan.coverageTiers.length === 0) {
        console.log(`⚠️ Plan ${plan.name} has no coverage tiers, excluding from display`);
        return false;
      }

      // Check if preferred tier is available
      const hasPreferredTier = plan.coverageTiers.some((tier: any) => tier.tierName === preferredTier);

      if (hasPreferredTier) {
        console.log(`✅ Plan ${plan.name} has preferred tier ${preferredTier}`);
        return true;
      } else {
        console.log(`⚠️ Plan ${plan.name} does not have preferred tier ${preferredTier}, but has other tiers:`,
          plan.coverageTiers.map((t: any) => t.tierName));
        // Still include plans that don't have preferred tier but have other tiers
        return true;
      }
    });
  };

  // Will be initialized after displayPlans is created
  const [showModal, setShowModal] = useState(false);
  const [modalConfig, setModalConfig] = useState<{
    title: string;
    message: string;
    type: 'alert' | 'success' | 'error';
    onConfirm: () => void;
    confirmText: string;
    cancelText: string;
  }>({
    title: '',
    message: '',
    type: 'alert',
    onConfirm: () => {},
    confirmText: 'OK',
    cancelText: 'Cancel'
  });
  // const [showChatModal, setShowChatModal] = useState(false); // Not needed

  // Get current theme
  const currentTheme = THEMES[themeIndex % THEMES.length];

  // Helper functions for category-specific messages
  const getCategoryMessage = (category: string): string => {
    const messages: Record<string, string> = {
      'Dental': '😁 Now let\'s take care of your smile! Which dental plan works best for you?',
      'Vision': '👁️ Let\'s help you see clearly! Which vision plan fits your needs?',
      'Life Insurance': '🛡️ Let\'s protect your family\'s future! Which life insurance plan is right for you?',
      'AD&D Insurance': '⚡ Additional protection for unexpected events. Which AD&D plan would you like?',
      'Medical': '🏥 Let\'s find the perfect medical plan for your healthcare needs!',
      'Disability Insurance': '🤝 Protect your income with disability coverage. Which plan works for you?',
      'Voluntary Benefits': '📋 Explore additional voluntary benefits to enhance your coverage.'
    };
    return messages[category] || `Let's choose your ${category.toLowerCase()} plan!`;
  };

  const getCategorySubMessage = (category: string): string => {
    const subMessages: Record<string, string> = {
      'Dental': 'Even if you don\'t need dental care now, having coverage can save you money later.',
      'Vision': 'Regular eye exams and vision care are important for your overall health.',
      'Life Insurance': 'Life insurance provides financial security for your loved ones.',
      'AD&D Insurance': 'Accidental Death & Dismemberment insurance provides additional protection.',
      'Medical': 'Choose a plan that fits your healthcare needs and budget.',
      'Disability Insurance': 'Disability insurance helps replace income if you can\'t work due to illness or injury.',
      'Voluntary Benefits': 'These optional benefits can provide additional peace of mind.'
    };
    return subMessages[category] || `Select the ${category.toLowerCase()} coverage that best fits your needs.`;
  };

  const getCategoryIcon = (category: string): string => {
    const icons: Record<string, string> = {
      'Dental': '🦷',
      'Vision': '👁️',
      'Life Insurance': '🛡️',
      'AD&D Insurance': '⚡',
      'Medical': '🏥',
      'Disability Insurance': '🤝',
      'Voluntary Benefits': '📋'
    };
    return icons[category] || '📄';
  };

  // Modal helper function
  const showAlert = (title: string, message: string, type: 'alert' | 'success' | 'error' = 'alert') => {
    setModalConfig({
      title,
      message,
      type,
      onConfirm: () => setShowModal(false),
      confirmText: 'OK',
      cancelText: 'Cancel'
    });
    setShowModal(true);
  };

  // Handle plan selection
  const handlePlanSelect = (planId: string) => {
    // Toggle selection - if clicking the same plan, deselect it
    if (selectedPlan === planId) {
      setSelectedPlan(null);
      setSelectedTierData(null);
      removeSelectedPlan(category);
      removeWaivedCoverage(category);
      onPlanSelect(null);
      return;
    }

    // Single plan selection
    setSelectedPlan(planId);
    setSelectedTierData(null); // Reset tier data, will be set by tier selector

    // Clear any waive status
    removeWaivedCoverage(category);
  };

  // Handle coverage tier selection
  const handleTierSelect = (tierData: any) => {
    if (!selectedPlan || selectedPlan === 'WAIVE') return;

    setSelectedTierData(tierData);

    if (tierData) {
      const selectedPlanData = filteredDisplayPlans.find(p => p.id === selectedPlan);
      if (selectedPlanData) {
        // Store in new format
        storeSelectedPlan({
          planId: selectedPlan,
          planName: selectedPlanData.name,
          category: category,
          selectedTier: tierData,
          originalPlan: selectedPlanData.originalPlan,
          selectionDate: new Date().toISOString()
        });

        // Call parent callback with enhanced plan data
        onPlanSelect({
          ...selectedPlanData,
          cost: tierData.employeeCost,
          selectedTier: tierData
        });
      }
    }
  };

  const handleWaiveSelect = () => {
    // Toggle waive selection - if already waived, deselect it
    if (selectedPlan === 'WAIVE') {
      setSelectedPlan(null);
      setSelectedTierData(null);
      removeWaivedCoverage(category);
      onPlanSelect(null);
      return;
    }

    setShowWaiveConfirm(true);
  };

  const handleWaiveCoverage = () => {
    if (!waiveReason) {
      alert('Please select a reason for waiving coverage.');
      return;
    }

    setSelectedPlan('WAIVE');
    setSelectedTierData(null);

    // Store waive in new format
    storeWaivedCoverage({
      category: category,
      waiveReason,
      waiveDate: new Date().toISOString()
    });

    setShowWaiveConfirm(false);
    setWaiveReason(''); // Reset reason

    // Call parent callback with null to indicate waived
    onPlanSelect(null);
  };

  // Convert API plans to display format
  const convertPlansToDisplayFormat = (apiPlans: any[]): Plan[] => {
    return apiPlans.map(apiPlan => ({
      id: apiPlan.id || apiPlan._id,
      name: apiPlan.name || apiPlan.planName,
      cost: apiPlan.cost || 0,
      features: apiPlan.features || [],
      recommended: apiPlan.recommended || false,
      originalPlan: apiPlan,
      coverageTiers: apiPlan.coverageTiers || []
    }));
  };

  const displayPlans = convertPlansToDisplayFormat(plans || []);

  // Apply filtering to display plans (now that displayPlans is initialized)
  const filteredDisplayPlans = getFilteredPlans(displayPlans);

  // Initialize selected plan from storage
  useEffect(() => {
    // Check new storage system first
    const storedPlan = getStoredPlan(category);
    const isWaived = isCategoryWaived(category);

    if (isWaived) {
      setSelectedPlan('WAIVE');
    } else if (storedPlan) {
      setSelectedPlan(storedPlan.planId);
      setSelectedTierData(storedPlan.selectedTier);
    } else {
      // Fallback to legacy storage
      const planKey = `selected${category.replace(/\s+/g, '')}Plan`;
      const waiveKey = `${category.toLowerCase()}Waived`;

      if (localStorage.getItem(waiveKey) === 'true') {
        setSelectedPlan('WAIVE');
      } else {
        const savedSelection = localStorage.getItem(planKey);
        if (savedSelection) {
          try {
            const planData = JSON.parse(savedSelection);
            setSelectedPlan(planData.id);
          } catch (e) {
            console.error(`Error parsing saved ${category} plan:`, e);
          }
        }
      }
    }
  }, [category]);

  return (
    <div style={{
      display: 'flex',
      flexDirection: 'column',
      gap: '24px',
      fontFamily: '"SF Pro", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif'
    }}>
      {/* Bot Message */}
      <div style={{ display: 'flex', gap: '16px' }}>
        <div style={{
          width: '40px',
          height: '40px',
          borderRadius: '50%',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          flexShrink: 0,
          overflow: 'hidden'
        }}>
          <Image
            src="/brea.png"
            alt="Brea - AI Benefits Assistant"
            width={40}
            height={40}
            style={{ borderRadius: '50%' }}
          />
        </div>
        <div style={{
          backgroundColor: '#f9fafb',
          borderRadius: '8px',
          padding: '16px',
          maxWidth: '1000px'
        }}>
          <p style={{
            color: '#374151',
            lineHeight: '1.6',
            margin: 0
          }}>
            {getCategoryMessage(category)}
          </p>
          <p style={{
            color: '#374151',
            lineHeight: '1.6',
            margin: '8px 0 0 0'
          }}>
            {getCategorySubMessage(category)}
          </p>
        </div>
      </div>

      {/* Plan Selection Container */}
      <div style={{
        backgroundColor: 'white',
        borderRadius: '8px',
        border: '1px solid #e5e7eb',
        padding: '24px',
        boxShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.1)'
      }}>
        <div style={{ display: 'flex', alignItems: 'center', gap: '8px', marginBottom: '16px' }}>
          <span style={{ fontSize: '18px' }}>{getCategoryIcon(category)}</span>
          <h2 style={{
            fontSize: '20px',
            fontWeight: '600',
            color: '#111827',
            margin: 0
          }}>
            Smart {category} Plan Selection
          </h2>
        </div>

        <p style={{
          color: '#6b7280',
          marginBottom: '24px',
          margin: 0
        }}>
          Now let&apos;s find your perfect {category.toLowerCase()} plan:
        </p>

        <div style={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
          {filteredDisplayPlans.length === 0 ? (
            <div style={{
              textAlign: 'center',
              padding: '40px 20px',
              backgroundColor: '#f9fafb',
              borderRadius: '8px',
              border: '2px dashed #e5e7eb'
            }}>
              <div style={{ fontSize: '48px', marginBottom: '16px' }}>{getCategoryIcon(category)}</div>
              <h3 style={{
                fontSize: '18px',
                fontWeight: '600',
                color: '#374151',
                margin: '0 0 8px 0'
              }}>
                No {category} Plans Available
              </h3>
              <p style={{
                color: '#6b7280',
                fontSize: '14px',
                margin: 0,
                lineHeight: '1.5'
              }}>
                {displayPlans.length === 0
                  ? `Your company hasn't set up any ${category.toLowerCase()} plan assignments yet.`
                  : `No ${category.toLowerCase()} plans are available with proper coverage tier configurations.`
                }
                <br />
                Please contact your HR administrator for more information.
              </p>
            </div>
          ) : (
            <>
              {/* Regular Plan Options - Filtered by Coverage Tier Availability */}
              {filteredDisplayPlans.map((plan) => {
                const isSelected = selectedPlan === plan.id;
                return (
                  <div
                    key={plan.id}
                    style={{
                      border: isSelected
                        ? `2px solid ${currentTheme.primary}`
                        : plan.recommended
                          ? '2px solid #f59e0b'
                          : '2px solid #e5e7eb',
                      borderRadius: '8px',
                      padding: '20px',
                      backgroundColor: isSelected
                        ? currentTheme.accent
                        : plan.recommended
                          ? '#fef3e2'
                          : '#f9fafb',
                      cursor: 'pointer',
                      transition: 'all 0.2s ease'
                    }}
                    onClick={() => handlePlanSelect(plan.id)}
                  >
                    <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', marginBottom: '12px' }}>
                      <div>
                        <h3 style={{
                          fontSize: '18px',
                          fontWeight: '600',
                          color: '#111827',
                          margin: 0
                        }}>
                          {plan.name}
                        </h3>
                        <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'flex-start', marginTop: '4px' }}>
                          {(() => {
                            // Show dynamic pricing based on preferred tier
                            const preferredTierData = plan.coverageTiers?.find((tier: any) => tier.tierName === preferredTier);
                            const displayCost = preferredTierData ? preferredTierData.employeeCost : plan.cost;
                            const isPreferredAvailable = !!preferredTierData;

                            return (
                              <div>
                                <div style={{ display: 'flex', alignItems: 'baseline', gap: '4px' }}>
                                  <span style={{
                                    fontSize: '24px',
                                    fontWeight: '700',
                                    color: isPreferredAvailable ? '#111827' : '#6b7280'
                                  }}>
                                    ${displayCost.toFixed(2)}
                                  </span>
                                  <span style={{ color: '#6b7280', fontSize: '14px' }}>/paycheck</span>
                                </div>
                                {isPreferredAvailable && (
                                  <div style={{
                                    fontSize: '12px',
                                    color: '#10b981',
                                    fontWeight: '500',
                                    marginTop: '2px'
                                  }}>
                                    ✓ {preferredTier} available
                                  </div>
                                )}
                                {!isPreferredAvailable && plan.coverageTiers?.length > 0 && (
                                  <div style={{
                                    fontSize: '11px',
                                    color: '#f59e0b',
                                    fontWeight: '500',
                                    marginTop: '2px'
                                  }}>
                                    Starting from (tier selection required)
                                  </div>
                                )}
                              </div>
                            );
                          })()}
                        </div>
                      </div>
                      {plan.recommended && (
                        <div style={{
                          backgroundColor: '#f59e0b',
                          color: 'white',
                          padding: '4px 8px',
                          borderRadius: '12px',
                          fontSize: '12px',
                          fontWeight: '500'
                        }}>
                          Recommended
                        </div>
                      )}
                    </div>

                    <div style={{ display: 'flex', flexDirection: 'column', gap: '8px', marginBottom: '16px' }}>
                      {plan.features.map((feature: string, index: number) => (
                        <div key={index} style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                          <CheckCircle style={{ width: '16px', height: '16px', color: '#10b981', flexShrink: 0 }} />
                          <span style={{ color: '#374151', fontSize: '14px' }}>{feature}</span>
                        </div>
                      ))}
                    </div>

                    <button
                      style={{
                        width: '100%',
                        backgroundColor: isSelected ? '#000000' : '#f3f4f6',
                        color: isSelected ? 'white' : '#6b7280',
                        padding: '8px 16px',
                        borderRadius: '6px',
                        fontWeight: '500',
                        border: 'none',
                        cursor: 'pointer',
                        fontSize: '14px'
                      }}
                    >
                      {isSelected ? '✓ Selected' : 'Select This Plan'}
                    </button>
                  </div>
                );
              })}

              {/* Coverage Tier Selector */}
              {selectedPlan && selectedPlan !== 'WAIVE' && (
                <CoverageTierSelector
                  planName={filteredDisplayPlans.find(p => p.id === selectedPlan)?.name || ''}
                  availableTiers={filteredDisplayPlans.find(p => p.id === selectedPlan)?.coverageTiers || []}
                  preferredTier={preferredTier}
                  onTierSelect={handleTierSelect}
                  selectedTier={selectedTierData?.tierName}
                  category={category}
                />
              )}

              {/* Waive Coverage Option */}
              <div
                style={{
                  border: selectedPlan === 'WAIVE'
                    ? '2px solid #ef4444'
                    : '2px solid #e5e7eb',
                  borderRadius: '8px',
                  padding: '20px',
                  backgroundColor: selectedPlan === 'WAIVE'
                    ? '#fef2f2'
                    : '#f9fafb',
                  cursor: 'pointer',
                  transition: 'all 0.2s ease'
                }}
                onClick={handleWaiveSelect}
              >
                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', marginBottom: '12px' }}>
                  <div>
                    <h3 style={{
                      fontSize: '18px',
                      fontWeight: '600',
                      color: '#111827',
                      margin: 0
                    }}>
                      Waive {category} Coverage
                    </h3>
                    <div style={{ display: 'flex', alignItems: 'baseline', gap: '4px', marginTop: '4px' }}>
                      <span style={{
                        fontSize: '24px',
                        fontWeight: '700',
                        color: '#111827'
                      }}>
                        $0.00
                      </span>
                      <span style={{ color: '#6b7280', fontSize: '14px' }}>/paycheck</span>
                    </div>
                  </div>
                  <div style={{
                    backgroundColor: '#ef4444',
                    color: 'white',
                    padding: '4px 8px',
                    borderRadius: '12px',
                    fontSize: '12px',
                    fontWeight: '500'
                  }}>
                    No Coverage
                  </div>
                </div>

                <div style={{ display: 'flex', flexDirection: 'column', gap: '8px', marginBottom: '16px' }}>
                  <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                    <span style={{ color: '#ef4444', fontSize: '16px' }}>⚠️</span>
                    <span style={{ color: '#374151', fontSize: '14px' }}>No {category.toLowerCase()} coverage or benefits</span>
                  </div>
                  <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                    <span style={{ color: '#ef4444', fontSize: '16px' }}>⚠️</span>
                    <span style={{ color: '#374151', fontSize: '14px' }}>You&apos;ll pay full cost for {category.toLowerCase()} care</span>
                  </div>
                  <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                    <span style={{ color: '#ef4444', fontSize: '16px' }}>⚠️</span>
                    <span style={{ color: '#374151', fontSize: '14px' }}>Can only enroll during next open enrollment</span>
                  </div>
                </div>

                <button
                  style={{
                    width: '100%',
                    backgroundColor: selectedPlan === 'WAIVE' ? '#ef4444' : '#f3f4f6',
                    color: selectedPlan === 'WAIVE' ? 'white' : '#6b7280',
                    padding: '8px 16px',
                    borderRadius: '6px',
                    fontWeight: '500',
                    border: 'none',
                    cursor: 'pointer',
                    fontSize: '14px'
                  }}
                >
                  {selectedPlan === 'WAIVE' ? '✓ Coverage Waived' : 'Waive Coverage'}
                </button>
              </div>
            </>
          )}
        </div>

        {/* Selection Summary */}
        {selectedPlan && (
          <div style={{
            backgroundColor: selectedPlan === 'WAIVE' ? '#fef2f2' : currentTheme.accent,
            border: selectedPlan === 'WAIVE' ? '1px solid #fecaca' : `1px solid ${currentTheme.primary}40`,
            borderRadius: '8px',
            padding: '16px',
            marginTop: '16px'
          }}>
            <h3 style={{
              fontSize: '16px',
              fontWeight: '600',
              color: selectedPlan === 'WAIVE' ? '#dc2626' : currentTheme.primary,
              margin: '0 0 12px 0'
            }}>
              {selectedPlan === 'WAIVE' ? `${category} Coverage Waived` : `Selected ${category} Plan`}
            </h3>
            <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
              {selectedPlan === 'WAIVE' ? (
                <div style={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                  backgroundColor: 'white',
                  padding: '12px',
                  borderRadius: '6px'
                }}>
                  <span style={{ fontWeight: '500', color: '#111827' }}>No {category} Coverage</span>
                  <span style={{ color: '#dc2626', fontWeight: '600' }}>$0.00/paycheck</span>
                </div>
              ) : (() => {
                const plan = displayPlans.find(p => p.id === selectedPlan);
                return plan ? (
                  <div style={{
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    backgroundColor: 'white',
                    padding: '12px',
                    borderRadius: '6px'
                  }}>
                    <span style={{ fontWeight: '500', color: '#111827' }}>{plan.name}</span>
                    <span style={{ color: currentTheme.primary, fontWeight: '600' }}>
                      ${(() => {
                        const preferredTierData = plan.coverageTiers?.find((tier: any) => tier.tierName === preferredTier);
                        return preferredTierData ? preferredTierData.employeeCost.toFixed(2) : plan.cost.toFixed(2);
                      })()}/paycheck
                    </span>
                  </div>
                ) : null;
              })()}
            </div>
          </div>
        )}

        {/* Action Buttons */}
        <div style={{
          display: 'flex',
          gap: '12px',
          paddingTop: '24px',
          borderTop: '1px solid #e5e7eb',
          marginTop: '24px'
        }}>
          {/* FloatingHelp removed - using global FloatingChatButton instead */}
          {filteredDisplayPlans.length > 0 && (
            <button
              onClick={() => setShowVideo(true)}
              style={{
                display: 'flex',
                alignItems: 'center',
                gap: '8px',
                padding: '8px 16px',
                color: '#374151',
                border: '1px solid #d1d5db',
                borderRadius: '8px',
                backgroundColor: 'white',
                cursor: 'pointer',
                transition: 'background-color 0.2s ease'
              }}
            >
              <Play size={16} style={{ color: '#6b7280' }} />
              Watch Video
            </button>
          )}
          {displayPlans.length > 1 && (
            <button
              onClick={() => setShowCompare(true)}
              style={{
                display: 'flex',
                alignItems: 'center',
                gap: '8px',
                padding: '8px 16px',
                color: '#374151',
                border: '1px solid #d1d5db',
                borderRadius: '8px',
                backgroundColor: 'white',
                cursor: 'pointer',
                transition: 'background-color 0.2s ease'
              }}
            >
              <BarChart3 size={16} style={{ color: '#6b7280' }} />
              Compare Plans
            </button>
          )}
        </div>
      </div>

      {/* Modals */}

      {/* Waive Confirmation Modal */}
      {showWaiveConfirm && (
        <div style={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundColor: 'rgba(0, 0, 0, 0.5)',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          zIndex: 1000
        }}>
          <div style={{
            backgroundColor: 'white',
            borderRadius: '12px',
            padding: '24px',
            maxWidth: '500px',
            width: '90%',
            maxHeight: '80vh',
            overflow: 'auto'
          }}>
            <h3 style={{
              fontSize: '18px',
              fontWeight: '600',
              color: '#111827',
              margin: '0 0 16px 0'
            }}>
              Waive {category} Coverage
            </h3>

            <p style={{
              color: '#6b7280',
              marginBottom: '16px',
              lineHeight: '1.5'
            }}>
              Please select a reason for waiving {category.toLowerCase()} coverage:
            </p>

            <div style={{ marginBottom: '24px' }}>
              {[
                'I have coverage through my spouse/partner',
                'I have coverage through another employer',
                'I don\'t need this coverage',
                'Cost is too high',
                'Other'
              ].map((reason) => (
                <label key={reason} style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '8px',
                  padding: '8px 0',
                  cursor: 'pointer'
                }}>
                  <input
                    type="radio"
                    name="waiveReason"
                    value={reason}
                    checked={waiveReason === reason}
                    onChange={(e) => setWaiveReason(e.target.value)}
                    style={{ margin: 0 }}
                  />
                  <span style={{ fontSize: '14px', color: '#374151' }}>{reason}</span>
                </label>
              ))}
            </div>

            <div style={{
              display: 'flex',
              gap: '12px',
              justifyContent: 'flex-end'
            }}>
              <button
                onClick={() => {
                  setShowWaiveConfirm(false);
                  setWaiveReason('');
                }}
                style={{
                  padding: '8px 16px',
                  backgroundColor: '#f3f4f6',
                  color: '#374151',
                  border: 'none',
                  borderRadius: '6px',
                  cursor: 'pointer',
                  fontSize: '14px',
                  fontWeight: '500'
                }}
              >
                Cancel
              </button>
              <button
                onClick={handleWaiveCoverage}
                disabled={!waiveReason}
                style={{
                  padding: '8px 16px',
                  backgroundColor: waiveReason ? '#ef4444' : '#d1d5db',
                  color: 'white',
                  border: 'none',
                  borderRadius: '6px',
                  cursor: waiveReason ? 'pointer' : 'not-allowed',
                  fontSize: '14px',
                  fontWeight: '500'
                }}
              >
                Waive Coverage
              </button>
            </div>
          </div>
        </div>
      )}



      {showVideo && (
        <VideoPlayer
          title={`${category} Plan Overview`}
          description={`Learn more about ${category.toLowerCase()} coverage options`}
          planType={category.toLowerCase() as 'medical' | 'dental' | 'vision' | 'summary'}
          onClose={() => setShowVideo(false)}
          videoUrl="https://example.com/video" // This should come from plan data
        />
      )}

      {showCompare && filteredDisplayPlans.length > 1 && (
        <CompareModal
          onClose={() => setShowCompare(false)}
          plans={filteredDisplayPlans.map(plan => ({
            id: plan.id,
            name: plan.name,
            type: category.toLowerCase() as 'medical' | 'dental' | 'vision',
            tier: 'Silver' as 'Bronze' | 'Silver' | 'Gold' | 'Platinum',
            monthlyPremium: plan.cost,
            deductible: 0,
            outOfPocketMax: 0,
            features: plan.features,
            network: 'Standard Network'
          }))}
        />
      )}
    </div>
  );
};

export default DynamicPlanPage;
