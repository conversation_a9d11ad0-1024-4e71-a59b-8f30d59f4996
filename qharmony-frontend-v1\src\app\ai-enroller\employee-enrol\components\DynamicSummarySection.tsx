import React from 'react';
import { getDynamicEnrollmentSummary, StoredPlanData, StoredWaiveData } from '../utils/planStorageUtils';

interface DynamicSummarySectionProps {
  showCosts?: boolean;
  showWaived?: boolean;
  style?: 'summary' | 'confirmation';
}

interface PlanCostBreakdown {
  planName: string;
  planType: string;
  employeeCost: number;
  employerCost: number;
  totalCost: number;
  coverageTier: string;
}

const DynamicSummarySection: React.FC<DynamicSummarySectionProps> = ({
  showCosts = true,
  showWaived = true,
  style = 'summary'
}) => {
  const enrollmentSummary = getDynamicEnrollmentSummary();
  const selectedPlans = enrollmentSummary.selectedPlans || [];
  const waivedCoverages = enrollmentSummary.waivedCoverages || [];

  // Calculate plan costs
  const calculatePlanCost = (plan: StoredPlanData): PlanCostBreakdown => {
    const tier = plan.selectedTier;
    return {
      planName: plan.planName,
      planType: plan.category,
      employeeCost: tier.employeeCost,
      employerCost: tier.employerCost,
      totalCost: tier.totalCost,
      coverageTier: tier.tierName
    };
  };

  // Get plan type styling
  const getPlanTypeStyle = (category: string) => {
    const categoryLower = category.toLowerCase();
    
    if (categoryLower.includes('dental')) {
      return { bg: '#f0fdf4', text: '#166534', price: '#059669', icon: '🦷' };
    } else if (categoryLower.includes('vision')) {
      return { bg: '#eff6ff', text: '#1e40af', price: '#2563eb', icon: '👓' };
    } else if (categoryLower.includes('life')) {
      return { bg: '#faf5ff', text: '#6b21a8', price: '#7c3aed', icon: '🛡️' };
    } else if (categoryLower.includes('ad&d') || categoryLower.includes('add')) {
      return { bg: '#fef3c7', text: '#92400e', price: '#d97706', icon: '🛡️' };
    } else if (categoryLower.includes('medical')) {
      return { bg: '#fef2f2', text: '#991b1b', price: '#dc2626', icon: '🏥' };
    } else {
      return { bg: '#f9fafb', text: '#374151', price: '#111827', icon: '📋' };
    }
  };

  const planCosts = selectedPlans.map(calculatePlanCost);
  const totalMonthlyCost = planCosts.reduce((sum: number, plan: PlanCostBreakdown) => sum + plan.employeeCost, 0);

  if (selectedPlans.length === 0 && waivedCoverages.length === 0) {
    return (
      <div style={{
        backgroundColor: '#f9fafb',
        borderRadius: '8px',
        padding: '24px',
        textAlign: 'center',
        border: '1px solid #e5e7eb'
      }}>
        <p style={{ color: '#6b7280', margin: 0, fontSize: '14px' }}>
          No plans selected or waived yet.
        </p>
      </div>
    );
  }

  return (
    <div>
      {/* Selected Plans */}
      {selectedPlans.length > 0 && (
        <div style={{ marginBottom: showWaived && waivedCoverages.length > 0 ? '24px' : '0' }}>
          <h3 style={{
            fontSize: '16px',
            fontWeight: '600',
            color: '#111827',
            marginBottom: '16px',
            margin: '0 0 16px 0'
          }}>
            Selected Plans
          </h3>

          {showCosts ? (
            // Cost breakdown view
            <div style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
              {planCosts.map((plan: PlanCostBreakdown, index: number) => {
                const colors = getPlanTypeStyle(plan.planType);
                return (
                  <div
                    key={index}
                    style={{
                      backgroundColor: colors.bg,
                      borderRadius: '8px',
                      padding: '16px',
                      border: '1px solid #e5e7eb'
                    }}
                  >
                    <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
                      <div>
                        <h3 style={{
                          fontSize: '16px',
                          fontWeight: '600',
                          color: '#111827',
                          margin: '0 0 4px 0'
                        }}>
                          {colors.icon} {plan.planType}: {plan.planName}
                        </h3>
                        <p style={{
                          fontSize: '14px',
                          color: colors.text,
                          margin: '0 0 8px 0',
                          fontWeight: '500'
                        }}>
                          {plan.coverageTier}
                        </p>
                        <div style={{ fontSize: '12px', color: '#6b7280' }}>
                          <div>Total Cost: ${plan.totalCost.toFixed(2)}/month</div>
                          <div>Employer Pays: ${plan.employerCost.toFixed(2)}/month</div>
                        </div>
                      </div>
                      <div style={{ textAlign: 'right' }}>
                        <p style={{
                          fontSize: '18px',
                          fontWeight: '600',
                          color: colors.price,
                          margin: 0
                        }}>
                          ${plan.employeeCost.toFixed(2)}
                        </p>
                        <p style={{
                          fontSize: '12px',
                          color: '#6b7280',
                          margin: '4px 0 0 0'
                        }}>
                          per paycheck
                        </p>
                      </div>
                    </div>
                  </div>
                );
              })}

              {/* Total Cost Summary */}
              {planCosts.length > 0 && (
                <div style={{
                  backgroundColor: '#f3f4f6',
                  borderRadius: '8px',
                  padding: '16px',
                  border: '2px solid #d1d5db',
                  marginTop: '8px'
                }}>
                  <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <h3 style={{
                      fontSize: '16px',
                      fontWeight: '600',
                      color: '#111827',
                      margin: 0
                    }}>
                      Total Monthly Cost
                    </h3>
                    <p style={{
                      fontSize: '20px',
                      fontWeight: '700',
                      color: '#111827',
                      margin: 0
                    }}>
                      ${totalMonthlyCost.toFixed(2)}
                    </p>
                  </div>
                </div>
              )}

              {/* Show $0.00 total when all plans are waived */}
              {planCosts.length === 0 && waivedCoverages.length > 0 && (
                <div style={{
                  backgroundColor: '#f3f4f6',
                  borderRadius: '8px',
                  padding: '16px',
                  border: '2px solid #d1d5db',
                  marginTop: '8px'
                }}>
                  <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <h3 style={{
                      fontSize: '16px',
                      fontWeight: '600',
                      color: '#111827',
                      margin: 0
                    }}>
                      Total Monthly Cost
                    </h3>
                    <p style={{
                      fontSize: '20px',
                      fontWeight: '700',
                      color: '#111827',
                      margin: 0
                    }}>
                      $0.00
                    </p>
                  </div>
                  <p style={{
                    fontSize: '12px',
                    color: '#6b7280',
                    margin: '4px 0 0 0',
                    textAlign: 'center'
                  }}>
                    All coverages waived
                  </p>
                </div>
              )}
            </div>
          ) : (
            // Simple list view
            <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
              {selectedPlans.map((plan: StoredPlanData, index: number) => {
                const colors = getPlanTypeStyle(plan.category);
                return (
                  <div key={index} style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <span style={{ color: '#374151', fontSize: '14px', fontWeight: '500' }}>
                      {colors.icon} {plan.category}:
                    </span>
                    <span style={{ color: '#111827', fontSize: '14px', fontWeight: '600' }}>
                      {plan.planName}
                    </span>
                  </div>
                );
              })}
            </div>
          )}
        </div>
      )}

      {/* Waived Coverages */}
      {showWaived && waivedCoverages.length > 0 && (
        <div>
          <h3 style={{
            fontSize: '16px',
            fontWeight: '600',
            color: '#111827',
            marginBottom: '16px',
            margin: '0 0 16px 0'
          }}>
            Waived Coverages
          </h3>

          <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
            {waivedCoverages.map((waive: StoredWaiveData, index: number) => {
              const colors = getPlanTypeStyle(waive.category);
              return (
                <div key={index} style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                  <span style={{ color: '#dc2626', fontSize: '14px', fontWeight: '500' }}>
                    {colors.icon} {waive.category}:
                  </span>
                  <span style={{ color: '#dc2626', fontSize: '14px', fontWeight: '600' }}>
                    Waived
                  </span>
                </div>
              );
            })}
          </div>

          {/* Waive reasons (for confirmation page) */}
          {style === 'confirmation' && (
            <div style={{ marginTop: '12px', fontSize: '12px', color: '#6b7280' }}>
              {waivedCoverages.map((waive: StoredWaiveData, index: number) => (
                <div key={index}>
                  {waive.category} waive reason: {waive.waiveReason}
                </div>
              ))}
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default DynamicSummarySection;
