'use client';

import React, { useState, useEffect } from 'react';
import Image from 'next/image';
import { useRouter } from 'next/navigation';
import { CheckCircle, Edit } from 'lucide-react';
import { useAppSelector } from '../../../../redux/hooks';
import { RootState } from '../../../../redux/store';
import { bulkWaiveEnrollments, groupPlanAssignmentsByCategory, getPlanAssignmentIdsFromCategory } from '../services/bulkWaiveApi';
import { createBulkEnrollment, preparePlanSelections, getDependentsForEnrollment, validateBulkEnrollmentRequest, formatBulkEnrollmentSummary } from '../services/bulkEnrollmentApi';
import SignatureModal from './SignatureModal';
import { storeSignature, createSignatureData, getSignatureReference } from '../utils/signatureUtils';
import DynamicSummarySection from './DynamicSummarySection';
import { getDynamicEnrollmentSummary, getStoredPlans, getStoredWaives, createEnrollmentSnapshot } from '../utils/planStorageUtils';

interface EnrollmentData {
  medicalPlan?: any;
  dentalPlan?: any;
  visionPlan?: any;
  lifePlan?: any;
  addPlan?: any;
  additionalBenefits?: any[];
  dependents?: any[];
}

interface SummaryPageProps {
  enrollmentData: EnrollmentData;
  selectedCoverageTier: string;
  onMakeChanges: () => void;
  onConfirmEnrollment: () => void;
  planAssignments?: Record<string, any[]>; // Plan assignments grouped by category for bulk waive
}

interface PlanCostBreakdown {
  planName: string;
  planType: string;
  employeeCost: number;
  employerCost: number;
  totalCost: number;
}

const SummaryPage: React.FC<SummaryPageProps> = ({
  enrollmentData,
  selectedCoverageTier,
  onMakeChanges,
  onConfirmEnrollment,
  planAssignments = {}
}) => {
  const router = useRouter();
  const userDetails = useAppSelector((state: RootState) => state.user.userProfile);
  const [realPlanData, setRealPlanData] = useState<{
    dentalPlan?: any;
    visionPlan?: any;
    lifePlan?: any;
    addPlan?: any;
  }>({});
  const [loadingPlans, setLoadingPlans] = useState(false);
  const [creatingEnrollments, setCreatingEnrollments] = useState(false);
  const [showErrorModal, setShowErrorModal] = useState(false);
  const [errorMessage, setErrorMessage] = useState('');
  const [showSignatureModal, setShowSignatureModal] = useState(false);
  const [signatureCompleted, setSignatureCompleted] = useState(false);

  // API helper functions
  const getApiBaseUrl = () => process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8080';
  const getUserId = () => localStorage.getItem('userid1') || localStorage.getItem('userId') || '6838677aef6db0212bcfdacd';

  // Function to fetch plan assignment details by ID
  const fetchPlanAssignmentDetails = async (assignmentId: string) => {
    const API_BASE_URL = getApiBaseUrl();
    const userId = getUserId();

    try {
      console.log('🔍 Fetching plan assignment details for ID:', assignmentId);
      const response = await fetch(`${API_BASE_URL}/api/pre-enrollment/plan-assignments/${assignmentId}`, {
        headers: {
          'Content-Type': 'application/json',
          'user-id': userId,
        },
      });

      if (response.ok) {
        const data = await response.json();
        console.log('✅ Plan assignment fetch response:', data);

        // Handle Mongoose document structure - data might be in _doc
        const assignment = data.assignment._doc || data.assignment;
        return assignment;
      } else {
        console.error('❌ Failed to fetch plan assignment details. Status:', response.status);
        return null;
      }
    } catch (error) {
      console.error('❌ Error fetching plan assignment:', error);
      return null;
    }
  };

  // Function to fetch user dependents from API
  const fetchUserDependents = async () => {
    const API_BASE_URL = getApiBaseUrl();
    const userId = getUserId();

    try {
      console.log('🔍 Fetching user dependents from API...');
      const response = await fetch(`${API_BASE_URL}/employee?user-id=${userId}`, {
        headers: {
          'Content-Type': 'application/json',
          'user-id': userId,
        },
      });

      if (response.ok) {
        const userData = await response.json();
        console.log('✅ User data fetched:', userData);

        const dependents = userData.details?.dependents || [];
        console.log('👥 User dependents:', dependents);
        return dependents;
      } else {
        console.error('❌ Failed to fetch user data. Status:', response.status);
        return [];
      }
    } catch (error) {
      console.error('❌ Error fetching user dependents:', error);
      return [];
    }
  };

  // Function to create employee enrollment
  const createEmployeeEnrollment = async (planAssignmentId: string, planType: string) => {
    const API_BASE_URL = getApiBaseUrl();
    const userId = getUserId();

    try {
      console.log(`🔄 Creating employee enrollment for ${planType} plan:`, planAssignmentId);

      // Get dependent IDs based on coverage tier
      let dependentIds: string[] = [];

      // Only include dependents if coverage tier requires them
      if (actualCoverageTier !== 'Employee Only') {
        // Get dependents from enrollment data or localStorage
        const dependents = enrollmentData.dependents || [];

        // If no dependents in enrollment data, try to get from localStorage
        if (dependents.length === 0) {
          const storedDependents = localStorage.getItem('enrollmentDependents');
          if (storedDependents) {
            try {
              const parsedDependents = JSON.parse(storedDependents);
              dependents.push(...parsedDependents);
            } catch (e) {
              console.error('❌ Error parsing stored dependents:', e);
            }
          }
        }

        // If still no dependents, fetch from API
        if (dependents.length === 0) {
          console.log('🔍 No dependents found locally, fetching from API...');
          const apiDependents = await fetchUserDependents();
          dependents.push(...apiDependents);
        }

        // Extract dependent IDs
        dependentIds = dependents.map((dep: any) => dep.id || dep._id || dep.dependentId).filter(Boolean);

        console.log(`📋 ${planType} dependents for ${actualCoverageTier}:`, dependents);
        console.log(`📋 ${planType} dependent IDs:`, dependentIds);

        // Validate dependent count against coverage tier requirements
        if (actualCoverageTier === 'Family' && dependentIds.length === 0) {
          console.warn(`⚠️ Family coverage tier selected but no dependents found. This will cause enrollment to fail.`);
        }
      }

      const enrollmentData_payload = {
        employeeId: userId,
        planAssignmentId: planAssignmentId,
        coverageTier: actualCoverageTier,
        dependentIds: dependentIds,
        enrollmentType: 'Open Enrollment',
        // effectiveDate will be set by backend based on plan assignment dates
      };

      console.log(`📋 ${planType} enrollment payload:`, enrollmentData_payload);

      const response = await fetch(`${API_BASE_URL}/api/pre-enrollment/employee-enrollments`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'user-id': userId,
        },
        body: JSON.stringify(enrollmentData_payload),
      });

      if (response.ok) {
        const result = await response.json();
        console.log(`✅ ${planType} enrollment created successfully:`, result);
        return result;
      } else {
        const errorText = await response.text();
        console.error(`❌ Failed to create ${planType} enrollment. Status:`, response.status, 'Error:', errorText);

        // Check if it's an "already enrolled" error
        if (errorText.includes('Employee already enrolled in this plan assignment')) {
          try {
            const errorData = JSON.parse(errorText);
            if (errorData.existingEnrollmentId) {
              console.log(`🔄 Employee already enrolled in ${planType}, using existing enrollment:`, errorData.existingEnrollmentId);
              return {
                enrollment: {
                  _id: errorData.existingEnrollmentId,
                  status: errorData.existingStatus || 'Pending'
                },
                message: `Using existing ${planType} enrollment`
              };
            }
          } catch (parseError) {
            console.error('Error parsing existing enrollment data:', parseError);
          }
        }

        throw new Error(`Failed to create ${planType} enrollment: ${errorText}`);
      }
    } catch (error) {
      console.error(`❌ Error creating ${planType} enrollment:`, error);
      throw error;
    }
  };

  // Function to clear enrollment data from localStorage
  const clearEnrollmentData = () => {
    console.log('🧹 Clearing enrollment data from localStorage...');

    // Keys that are safe to remove (not needed by confirmation page)
    const keysToRemove = [
      'selectedCoverageTier',
      'enrollmentDependents',
      'userProfile',
      'enrollmentRecommendation'
    ];

    // Keys to preserve for confirmation page fallback recovery
    const keysToPreserve = [
      'selectedDentalPlan',
      'selectedVisionPlan',
      'selectedLifePlan',
      'selectedADDPlan',
      'dentalWaived',
      'visionWaived',
      'lifeWaived',
      'addWaived',
      'dentalWaiveReason',
      'visionWaiveReason',
      'lifeWaiveReason',
      'addWaiveReason',
      'enrollmentSummarySnapshot'
    ];

    keysToRemove.forEach(key => {
      localStorage.removeItem(key);
      console.log(`  ✅ Removed: ${key}`);
    });

    console.log('✅ Enrollment data cleared from localStorage');
    console.log('🔒 Preserved for confirmation page:', keysToPreserve);
  };



  // Function to handle signature completion and proceed with enrollment
  const handleSignatureComplete = (signatureData: string) => {
    console.log('✅ Signature completed, proceeding with enrollment...');
    setSignatureCompleted(true);
    setShowSignatureModal(false);

    // Store signature reference for confirmation page
    localStorage.setItem('enrollmentSignatureRef', signatureData);

    // Proceed with actual enrollment
    proceedWithEnrollment();
  };

  // Main function to handle confirm enrollment - now shows signature modal first
  const handleConfirmEnrollment = async () => {
    if (creatingEnrollments) return; // Prevent double-clicks

    // Check if signature already exists in localStorage
    // Note: Backend only has POST endpoint, no GET for signature retrieval
    const localSignature = getSignatureReference();

    if (localSignature) {
      console.log('📝 Signature already exists in localStorage, proceeding with enrollment...');
      proceedWithEnrollment();
    } else {
      console.log('📝 No signature found, showing signature modal...');
      setShowSignatureModal(true);
    }
  };

  // Actual enrollment process using BULK API
  const proceedWithEnrollment = async () => {
    setCreatingEnrollments(true);
    console.log('🎯 Starting BULK enrollment confirmation process...');

    try {
      // Prepare bulk enrollment using service functions
      const userId = getUserId();
      const companyId = userDetails.companyId || localStorage.getItem('companyId') || '';

      if (!companyId) {
        throw new Error('Company ID not found. Please contact support.');
      }

      // Get dependents for enrollment
      const dependentIds = await getDependentsForEnrollment(enrollmentData, actualCoverageTier);

      // Prepare plan selections from current enrollment data
      const currentEnrollmentData = {
        dentalPlan: dentalPlan,
        visionPlan: visionPlan,
        lifePlan: lifePlan,
        addPlan: addPlan
      };

      const planSelections = preparePlanSelections(currentEnrollmentData, actualCoverageTier, dependentIds);

      // Call bulk enrollment API if we have plans to enroll
      let bulkEnrollmentResult = null;
      if (planSelections.length > 0) {
        const bulkEnrollmentRequest = {
          employeeId: userId,
          companyId: companyId,
          employeeClassType: 'Full-Time',
          planSelections: planSelections
        };

        // Validate request before sending
        const validation = validateBulkEnrollmentRequest(bulkEnrollmentRequest);
        if (!validation.isValid) {
          throw new Error(`Bulk enrollment validation failed: ${validation.errors.join(', ')}`);
        }

        console.log('🚀 Calling bulk enrollment API with validated request:', bulkEnrollmentRequest);

        // Use the bulk enrollment service
        bulkEnrollmentResult = await createBulkEnrollment(bulkEnrollmentRequest);

        if (bulkEnrollmentResult.success) {
          console.log('✅ Bulk enrollment successful:', bulkEnrollmentResult);
          console.log('📊 Enrollment summary:', formatBulkEnrollmentSummary(bulkEnrollmentResult));
        } else {
          console.error('❌ Bulk enrollment failed:', bulkEnrollmentResult);
          throw new Error(bulkEnrollmentResult.error || 'Bulk enrollment failed');
        }
      } else {
        console.log('ℹ️ No plans selected for enrollment, proceeding with waive operations only');
      }

      // 🚫 BULK WAIVE: Handle waived categories
      const waiveResults = [];
      const errors = []; // Initialize errors array for waive operations

      console.log('🔍 BULK WAIVE DEBUG - Starting bulk waive process...');
      console.log('🔍 User ID:', userId);
      console.log('🔍 Plan assignments available:', Object.keys(planAssignments));
      console.log('🔍 Waive statuses:', { dentalWaived, visionWaived, lifeWaived });

      // Handle dental waive
      if (dentalWaived) {
        try {
          console.log('🚫 Processing dental waive...');
          const dentalWaiveReason = localStorage.getItem('dentalWaiveReason') || 'Coverage not needed';
          console.log('🔍 Dental waive reason:', dentalWaiveReason);

          const dentalPlanAssignments = getPlanAssignmentsForCategory('dental');

          if (dentalPlanAssignments.length === 0) {
            console.log('⚠️ No dental plan assignments found for waiving');
            console.log('🔍 Available plan assignment keys:', Object.keys(planAssignments));
            console.log('🔍 Plan assignments structure:', planAssignments);
          } else {
            // Extract plan assignment IDs from the enriched plan objects
            const dentalPlanAssignmentIds = dentalPlanAssignments
              .map(plan => plan.assignment?._id || plan._id || plan.id)
              .filter(Boolean);
            console.log('🔍 Dental plan assignment IDs to waive:', dentalPlanAssignmentIds);

            if (dentalPlanAssignmentIds.length > 0) {
              console.log('🚫 Calling bulk waive API for dental...');
              const waiveResult = await bulkWaiveEnrollments({
                employeeId: userId,
                planAssignmentIds: dentalPlanAssignmentIds,
                waiveReason: dentalWaiveReason,
                enrollmentType: 'Open Enrollment'
              });

              console.log('🔍 Dental bulk waive API response:', waiveResult);

              if (waiveResult.success && waiveResult.data) {
                console.log('✅ Dental bulk waive successful:', waiveResult.data);
                waiveResults.push({ type: 'Dental', result: waiveResult.data });
              } else {
                console.error('❌ Dental bulk waive failed:', waiveResult.error);
                errors.push({ type: 'Dental Waive', error: waiveResult.error || 'Failed to waive dental coverage' });
              }
            } else {
              console.log('⚠️ No valid dental plan assignment IDs found');
            }
          }
        } catch (error) {
          console.error('❌ Dental bulk waive error:', error);
          errors.push({ type: 'Dental Waive', error: error instanceof Error ? error.message : 'Failed to waive dental coverage' });
        }
      }

      // Handle vision waive
      if (visionWaived) {
        try {
          console.log('🚫 Processing vision waive...');
          const visionWaiveReason = localStorage.getItem('visionWaiveReason') || 'Coverage not needed';
          console.log('🔍 Vision waive reason:', visionWaiveReason);

          const visionPlanAssignments = getPlanAssignmentsForCategory('vision');

          if (visionPlanAssignments.length === 0) {
            console.log('⚠️ No vision plan assignments found for waiving');
          } else {
            // Extract plan assignment IDs from the enriched plan objects
            const visionPlanAssignmentIds = visionPlanAssignments
              .map(plan => plan.assignment?._id || plan._id || plan.id)
              .filter(Boolean);
            console.log('🔍 Vision plan assignment IDs to waive:', visionPlanAssignmentIds);

            if (visionPlanAssignmentIds.length > 0) {
              console.log('🚫 Calling bulk waive API for vision...');
              const waiveResult = await bulkWaiveEnrollments({
                employeeId: userId,
                planAssignmentIds: visionPlanAssignmentIds,
                waiveReason: visionWaiveReason,
                enrollmentType: 'Open Enrollment'
              });

              console.log('🔍 Vision bulk waive API response:', waiveResult);

              if (waiveResult.success && waiveResult.data) {
                console.log('✅ Vision bulk waive successful:', waiveResult.data);
                waiveResults.push({ type: 'Vision', result: waiveResult.data });
              } else {
                console.error('❌ Vision bulk waive failed:', waiveResult.error);
                errors.push({ type: 'Vision Waive', error: waiveResult.error || 'Failed to waive vision coverage' });
              }
            } else {
              console.log('⚠️ No valid vision plan assignment IDs found');
            }
          }
        } catch (error) {
          console.error('❌ Vision bulk waive error:', error);
          errors.push({ type: 'Vision Waive', error: error instanceof Error ? error.message : 'Failed to waive vision coverage' });
        }
      }

      // Handle life insurance waive
      if (lifeWaived) {
        try {
          console.log('🚫 Processing life insurance waive...');
          const lifeWaiveReason = localStorage.getItem('lifeWaiveReason') || 'Coverage not needed';
          console.log('🔍 Life insurance waive reason:', lifeWaiveReason);

          // Get life insurance plan assignments from multiple possible categories
          const lifeCategories = ['term life', 'life', 'life insurance', 'supplemental life insurance', 'group (employer) life'];
          let lifePlanAssignments: any[] = [];

          for (const category of lifeCategories) {
            const categoryPlans = getPlanAssignmentsForCategory(category);
            if (categoryPlans.length > 0) {
              lifePlanAssignments.push(...categoryPlans);
              console.log(`🔍 Found life insurance plans in category '${category}':`, categoryPlans);
            }
          }

          if (lifePlanAssignments.length === 0) {
            console.log('⚠️ No life insurance plan assignments found for waiving');
          } else {
            // Extract plan assignment IDs from the enriched plan objects
            const lifePlanAssignmentIds = lifePlanAssignments
              .map(plan => plan.assignment?._id || plan._id || plan.id)
              .filter(Boolean);
            console.log('🔍 Life insurance plan assignment IDs to waive:', lifePlanAssignmentIds);

            if (lifePlanAssignmentIds.length > 0) {
              console.log('🚫 Calling bulk waive API for life insurance...');
              const waiveResult = await bulkWaiveEnrollments({
                employeeId: userId,
                planAssignmentIds: lifePlanAssignmentIds,
                waiveReason: lifeWaiveReason,
                enrollmentType: 'Open Enrollment'
              });

              console.log('🔍 Life insurance bulk waive API response:', waiveResult);

              if (waiveResult.success && waiveResult.data) {
                console.log('✅ Life insurance bulk waive successful:', waiveResult.data);
                waiveResults.push({ type: 'Life Insurance', result: waiveResult.data });
              } else {
                console.error('❌ Life insurance bulk waive failed:', waiveResult.error);
                errors.push({ type: 'Life Insurance Waive', error: waiveResult.error || 'Failed to waive life insurance coverage' });
              }
            } else {
              console.log('⚠️ No valid life insurance plan assignment IDs found');
            }
          }
        } catch (error) {
          console.error('❌ Life insurance bulk waive error:', error);
          errors.push({ type: 'Life Insurance Waive', error: error instanceof Error ? error.message : 'Failed to waive life insurance coverage' });
        }
      }

      // Handle AD&D waive
      if (addWaived) {
        try {
          console.log('🚫 Processing AD&D waive...');
          const addWaiveReason = localStorage.getItem('addWaiveReason') || 'Coverage not needed';
          console.log('🔍 AD&D waive reason:', addWaiveReason);

          // Get AD&D plan assignments from multiple possible categories
          const addCategories = ['accidental death & dismemberment (ad&d)', 'ad&d', 'add', 'accidental death and dismemberment'];
          let addPlanAssignments: any[] = [];

          for (const category of addCategories) {
            const categoryPlans = getPlanAssignmentsForCategory(category);
            if (categoryPlans.length > 0) {
              addPlanAssignments.push(...categoryPlans);
              console.log(`🔍 Found AD&D plans in category '${category}':`, categoryPlans);
            }
          }

          if (addPlanAssignments.length === 0) {
            console.log('⚠️ No AD&D plan assignments found for waiving');
          } else {
            // Extract plan assignment IDs from the enriched plan objects
            const addPlanAssignmentIds = addPlanAssignments
              .map(plan => plan.assignment?._id || plan._id || plan.id)
              .filter(Boolean);
            console.log('🔍 AD&D plan assignment IDs to waive:', addPlanAssignmentIds);

            if (addPlanAssignmentIds.length > 0) {
              console.log('🚫 Calling bulk waive API for AD&D...');
              const waiveResult = await bulkWaiveEnrollments({
                employeeId: userId,
                planAssignmentIds: addPlanAssignmentIds,
                waiveReason: addWaiveReason,
                enrollmentType: 'Open Enrollment'
              });

              console.log('🔍 AD&D bulk waive API response:', waiveResult);

              if (waiveResult.success && waiveResult.data) {
                console.log('✅ AD&D bulk waive successful:', waiveResult.data);
                waiveResults.push({ type: 'AD&D', result: waiveResult.data });
              } else {
                console.error('❌ AD&D bulk waive failed:', waiveResult.error);
                errors.push({ type: 'AD&D Waive', error: waiveResult.error || 'Failed to waive AD&D coverage' });
              }
            } else {
              console.log('⚠️ No valid AD&D plan assignment IDs found');
            }
          }
        } catch (error) {
          console.error('❌ AD&D bulk waive error:', error);
          errors.push({ type: 'AD&D Waive', error: error instanceof Error ? error.message : 'Failed to waive AD&D coverage' });
        }
      }

      console.log('🚫 Bulk waive results:', waiveResults);

      // Check results - handle both bulk enrollment and waive errors
      const hasEnrollmentErrors = bulkEnrollmentResult && !bulkEnrollmentResult.success;
      const hasWaiveErrors = errors.length > 0;

      if (hasEnrollmentErrors || hasWaiveErrors) {
        console.error('❌ Some operations failed:', {
          bulkEnrollmentError: hasEnrollmentErrors && bulkEnrollmentResult ? bulkEnrollmentResult.error : null,
          waiveErrors: errors
        });

        let errorMessage = '❌ Enrollment Error\n\n';

        if (hasEnrollmentErrors && bulkEnrollmentResult) {
          errorMessage += `Enrollment failed: ${bulkEnrollmentResult.error}\n\n`;
        }

        if (hasWaiveErrors) {
          errorMessage += `Waive errors:\n${errors.map(e => e.error).join('\n')}`;
        }

        setErrorMessage(errorMessage);
        setShowErrorModal(true);
      } else {
        console.log('✅ All operations completed successfully:', {
          bulkEnrollment: bulkEnrollmentResult,
          waiveResults: waiveResults
        });

        // Save enrollment summary snapshot for confirmation page BEFORE clearing localStorage
        console.log('🔍 Pre-snapshot debug - Plan variables:', {
          dentalPlan: dentalPlan,
          visionPlan: visionPlan,
          lifePlan: lifePlan,
          addPlan: addPlan,
          addWaived: addWaived
        });

        // Create enrollment snapshot using new dynamic system
        const enrollmentSummarySnapshot = createEnrollmentSnapshot({
          // Include legacy data for backward compatibility
          dentalPlan: dentalPlan,
          visionPlan: visionPlan,
          lifePlan: lifePlan,
          addPlan: addPlan,
          dependents: enrollmentData.dependents,
          selectedCoverageTier: actualCoverageTier,
          dentalWaived: dentalWaived,
          visionWaived: visionWaived,
          lifeWaived: lifeWaived,
          addWaived: addWaived,
          dentalWaiveReason: localStorage.getItem('dentalWaiveReason'),
          visionWaiveReason: localStorage.getItem('visionWaiveReason'),
          lifeWaiveReason: localStorage.getItem('lifeWaiveReason'),
          addWaiveReason: localStorage.getItem('addWaiveReason')
        });

        console.log('📸 Saved enrollment summary snapshot:', enrollmentSummarySnapshot);
        console.log('🛡️ AD&D in snapshot specifically:', {
          addPlan: enrollmentSummarySnapshot.addPlan,
          addWaived: enrollmentSummarySnapshot.addWaived,
          addWaiveReason: enrollmentSummarySnapshot.addWaiveReason
        });

        // Clear localStorage after successful enrollment
        clearEnrollmentData();

        // For success, just navigate directly without showing modal
        console.log('🎉 Enrollment completed successfully, navigating to confirmation page...');

        // Call the original onConfirmEnrollment callback if provided
        if (onConfirmEnrollment) {
          onConfirmEnrollment();
        } else {
          // Navigate to a success page or dashboard
          router.push('/ai-enroller/employee-enrol/confirmation');
        }
      }

    } catch (error) {
      console.error('❌ Enrollment confirmation failed:', error);
      setErrorMessage('❌ Enrollment Error\n\nThere was an error in enrolling. Please contact HR or try again.');
      setShowErrorModal(true);
    } finally {
      setCreatingEnrollments(false);
    }
  };

  // Get the actual selected coverage tier from localStorage if not provided
  const getSelectedCoverageTier = () => {
    console.log('🔍 Checking coverage tier sources:');
    console.log('  - selectedCoverageTier prop:', selectedCoverageTier);

    if (selectedCoverageTier) {
      console.log('  ✅ Using prop value:', selectedCoverageTier);
      return selectedCoverageTier;
    }

    const storedTier = localStorage.getItem('selectedCoverageTier');
    console.log('  - localStorage value:', storedTier);

    if (storedTier) {
      console.log('  ✅ Using localStorage value:', storedTier);
      return storedTier;
    }

    // Default fallback
    console.log('  ⚠️ Using fallback: Employee Only');
    return 'Employee Only';
  };

  const actualCoverageTier = getSelectedCoverageTier();
  console.log('🎯 Final coverage tier being used:', actualCoverageTier);

  // Helper function to get cost breakdown for a plan
  const getPlanCostBreakdown = (plan: any, planType: string): PlanCostBreakdown | null => {
    console.log(`🔍 Getting cost breakdown for ${planType}:`, plan);
    console.log(`🎯 Selected coverage tier: ${actualCoverageTier}`);

    if (!plan) {
      console.log(`❌ No plan data for ${planType}`);
      return null;
    }

    // Check if plan has coverage tiers
    if (!plan.coverageTiers || !Array.isArray(plan.coverageTiers)) {
      console.log(`⚠️ No coverage tiers found for ${planType}, using fallback cost`);
      // Fallback to basic cost if available
      return {
        planName: plan.name || plan.planName || 'Unknown Plan',
        planType,
        employeeCost: plan.cost || 0,
        employerCost: 0, // Default to 0 if no tier data
        totalCost: plan.cost || 0
      };
    }

    console.log(`📊 Coverage tiers for ${planType}:`, plan.coverageTiers);

    // Log each tier for debugging
    plan.coverageTiers.forEach((tier: any, index: number) => {
      console.log(`  Tier ${index}: ${tier.tierName} - Employee: $${tier.employeeCost}, Employer: $${tier.employerCost}, Total: $${tier.totalCost}`);
    });

    // Find the matching coverage tier
    const matchingTier = plan.coverageTiers.find(
      (tier: any) => tier.tierName === actualCoverageTier
    );

    if (!matchingTier) {
      console.log(`⚠️ No matching tier for ${actualCoverageTier}, trying Employee Only`);
      // Fallback to Employee Only tier
      const fallbackTier = plan.coverageTiers.find(
        (tier: any) => tier.tierName === 'Employee Only'
      );

      if (!fallbackTier) {
        console.log(`⚠️ No Employee Only tier found, using first tier`);
        // Use first available tier
        const firstTier = plan.coverageTiers[0];
        if (firstTier) {
          return {
            planName: plan.name || plan.planName || 'Unknown Plan',
            planType,
            employeeCost: firstTier.employeeCost || 0,
            employerCost: firstTier.employerCost || 0,
            totalCost: firstTier.totalCost || 0
          };
        }
        return null;
      }

      return {
        planName: plan.name || plan.planName || 'Unknown Plan',
        planType,
        employeeCost: fallbackTier.employeeCost || 0,
        employerCost: fallbackTier.employerCost || 0,
        totalCost: fallbackTier.totalCost || 0
      };
    }

    console.log(`✅ Found matching tier for ${planType}:`, matchingTier);
    return {
      planName: plan.name || plan.planName || 'Unknown Plan',
      planType,
      employeeCost: matchingTier.employeeCost || 0,
      employerCost: matchingTier.employerCost || 0,
      totalCost: matchingTier.totalCost || 0
    };
  };

  // Debug enrollment data
  console.log('📋 Enrollment data received:', enrollmentData);
  console.log('👥 Dependents:', enrollmentData.dependents);
  console.log('🦷 Dental plan:', enrollmentData.dentalPlan);
  console.log('👓 Vision plan:', enrollmentData.visionPlan);
  console.log('🛡️ Life plan:', enrollmentData.lifePlan);

  // Check for waived coverages
  const dentalWaived = localStorage.getItem('dentalWaived') === 'true';
  const visionWaived = localStorage.getItem('visionWaived') === 'true';
  const lifeWaived = localStorage.getItem('lifeWaived') === 'true';
  const addWaived = localStorage.getItem('addWaived') === 'true';

  console.log('🚫 Waived coverages:', { dentalWaived, visionWaived, lifeWaived, addWaived });
  console.log('📋 Plan assignments for bulk waive:', planAssignments);

  // Helper function to get plan assignments for a category
  const getPlanAssignmentsForCategory = (category: string): any[] => {
    // Try different possible keys for the category
    const possibleKeys = [
      category.toLowerCase(),
      category,
      `${category} plan`,
      `${category.toLowerCase()} plan`,
      category.charAt(0).toUpperCase() + category.slice(1).toLowerCase(),
      `${category.charAt(0).toUpperCase() + category.slice(1).toLowerCase()} Plan`
    ];

    for (const key of possibleKeys) {
      if (planAssignments[key] && planAssignments[key].length > 0) {
        console.log(`🔍 Found ${category} plans under key '${key}':`, planAssignments[key]);
        return planAssignments[key];
      }
    }

    console.log(`⚠️ No ${category} plan assignments found. Available keys:`, Object.keys(planAssignments));
    return [];
  };

  // Fetch real plan assignment data on component mount
  useEffect(() => {
    const fetchRealPlanData = async () => {
      setLoadingPlans(true);
      console.log('🔄 Fetching real plan assignment data for summary...');

      try {
        const newPlanData: any = {};

        // Get selected plan IDs from localStorage
        const dentalPlanData = localStorage.getItem('selectedDentalPlan');
        const visionPlanData = localStorage.getItem('selectedVisionPlan');
        const lifePlanData = localStorage.getItem('selectedLifePlan');
        const addPlanData = localStorage.getItem('selectedADDPlan');

        // Fetch real plan assignment data for each selected plan
        if (dentalPlanData) {
          try {
            const storedDental = JSON.parse(dentalPlanData);
            if (storedDental.id) {
              console.log('🦷 Fetching real dental plan assignment:', storedDental.id);
              const realDentalData = await fetchPlanAssignmentDetails(storedDental.id);
              if (realDentalData) {
                newPlanData.dentalPlan = {
                  ...storedDental,
                  ...realDentalData,
                  name: realDentalData.planName || storedDental.name,
                  coverageTiers: realDentalData.coverageTiers
                };
              }
            }
          } catch (e) {
            console.error('❌ Error parsing dental plan data:', e);
          }
        }

        if (visionPlanData) {
          try {
            const storedVision = JSON.parse(visionPlanData);
            if (storedVision.id) {
              console.log('👓 Fetching real vision plan assignment:', storedVision.id);
              const realVisionData = await fetchPlanAssignmentDetails(storedVision.id);
              if (realVisionData) {
                newPlanData.visionPlan = {
                  ...storedVision,
                  ...realVisionData,
                  name: realVisionData.planName || storedVision.name,
                  coverageTiers: realVisionData.coverageTiers
                };
              }
            }
          } catch (e) {
            console.error('❌ Error parsing vision plan data:', e);
          }
        }

        if (lifePlanData) {
          try {
            const storedLife = JSON.parse(lifePlanData);
            if (storedLife.id) {
              console.log('🛡️ Fetching real life plan assignment:', storedLife.id);
              const realLifeData = await fetchPlanAssignmentDetails(storedLife.id);
              if (realLifeData) {
                newPlanData.lifePlan = {
                  ...storedLife,
                  ...realLifeData,
                  name: realLifeData.planName || storedLife.name,
                  coverageTiers: realLifeData.coverageTiers
                };
              }
            }
          } catch (e) {
            console.error('❌ Error parsing life plan data:', e);
          }
        }

        if (addPlanData) {
          try {
            const storedADD = JSON.parse(addPlanData);
            if (storedADD.id) {
              console.log('🛡️ Fetching real AD&D plan assignment:', storedADD.id);
              const realADDData = await fetchPlanAssignmentDetails(storedADD.id);
              if (realADDData) {
                newPlanData.addPlan = {
                  ...storedADD,
                  ...realADDData,
                  name: realADDData.planName || storedADD.name,
                  coverageTiers: realADDData.coverageTiers
                };
              }
            }
          } catch (e) {
            console.error('❌ Error parsing AD&D plan data:', e);
          }
        }

        console.log('✅ Real plan data fetched:', newPlanData);
        setRealPlanData(newPlanData);
      } catch (error) {
        console.error('❌ Error fetching real plan data:', error);
      } finally {
        setLoadingPlans(false);
      }
    };

    fetchRealPlanData();
  }, []); // 🔧 FIXED: Removed fetchPlanAssignmentDetails from dependency array to prevent infinite loop

  // Use real plan data if available, otherwise fallback to localStorage/enrollmentData
  const getSelectedPlan = (key: string, enrollmentPlan: any, realPlan: any) => {
    // Priority: realPlan > enrollmentPlan > localStorage
    if (realPlan) {
      console.log(`✅ Using real plan data for ${key}:`, realPlan);
      return realPlan;
    }

    if (enrollmentPlan) return enrollmentPlan;

    const storedPlan = localStorage.getItem(key);
    if (storedPlan) {
      try {
        return JSON.parse(storedPlan);
      } catch (e) {
        console.log(`❌ Error parsing ${key}:`, e);
        return null;
      }
    }
    return null;
  };

  const dentalPlan = getSelectedPlan('selectedDentalPlan', enrollmentData.dentalPlan, realPlanData.dentalPlan);
  const visionPlan = getSelectedPlan('selectedVisionPlan', enrollmentData.visionPlan, realPlanData.visionPlan);
  const lifePlan = getSelectedPlan('selectedLifePlan', enrollmentData.lifePlan, realPlanData.lifePlan);
  const addPlan = getSelectedPlan('selectedADDPlan', enrollmentData.addPlan, realPlanData.addPlan);

  console.log('🔍 Final plan data:', { dentalPlan, visionPlan, lifePlan, addPlan });

  // Debug AD&D plan selection specifically
  console.log('🛡️ AD&D Plan Debug:', {
    localStorage_selectedADDPlan: localStorage.getItem('selectedADDPlan'),
    enrollmentData_addPlan: enrollmentData.addPlan,
    realPlanData_addPlan: realPlanData.addPlan,
    final_addPlan: addPlan,
    addWaived: localStorage.getItem('addWaived')
  });

  // Log detailed coverage tier information for each plan
  if (dentalPlan?.coverageTiers) {
    console.log('🦷 Dental plan coverage tiers:', dentalPlan.coverageTiers);
  }
  if (visionPlan?.coverageTiers) {
    console.log('👓 Vision plan coverage tiers:', visionPlan.coverageTiers);
  }
  if (lifePlan?.coverageTiers) {
    console.log('🛡️ Life plan coverage tiers:', lifePlan.coverageTiers);
  }
  if (addPlan?.coverageTiers) {
    console.log('🛡️ AD&D plan coverage tiers:', addPlan.coverageTiers);
  }

  // Calculate cost breakdowns for all selected plans and waived coverages
  const planCosts: PlanCostBreakdown[] = [];
  const waivedCoverages: string[] = [];

  // Handle dental
  console.log('🦷 Dental insurance check:', { dentalPlan, dentalWaived });
  if (dentalPlan) {
    console.log('🦷 Processing dental plan:', dentalPlan);
    const dentalCost = getPlanCostBreakdown(dentalPlan, 'Dental');
    console.log('🦷 Dental cost breakdown result:', dentalCost);
    if (dentalCost) {
      planCosts.push(dentalCost);
      console.log('🦷 Dental cost added to planCosts');
    } else {
      console.log('🦷 Dental cost breakdown returned null - plan will not be displayed');
    }
  } else if (dentalWaived) {
    console.log('🦷 Dental waived');
    waivedCoverages.push('Dental');
  } else {
    console.log('🦷 No dental plan or waive status found');
  }

  // Handle vision
  console.log('👓 Vision insurance check:', { visionPlan, visionWaived });
  if (visionPlan) {
    console.log('👓 Processing vision plan:', visionPlan);
    const visionCost = getPlanCostBreakdown(visionPlan, 'Vision');
    console.log('👓 Vision cost breakdown result:', visionCost);
    if (visionCost) {
      planCosts.push(visionCost);
      console.log('👓 Vision cost added to planCosts');
    } else {
      console.log('👓 Vision cost breakdown returned null - plan will not be displayed');
    }
  } else if (visionWaived) {
    console.log('👓 Vision waived');
    waivedCoverages.push('Vision');
  } else {
    console.log('👓 No vision plan or waive status found');
  }

  // Handle life insurance
  console.log('🛡️ Life insurance check:', { lifePlan, lifeWaived });
  if (lifePlan) {
    console.log('🛡️ Processing life insurance plan:', lifePlan);
    const lifeCost = getPlanCostBreakdown(lifePlan, 'Life Insurance');
    console.log('🛡️ Life insurance cost breakdown:', lifeCost);
    if (lifeCost) planCosts.push(lifeCost);
  } else if (lifeWaived) {
    console.log('🛡️ Life insurance waived');
    waivedCoverages.push('Life Insurance');
  } else {
    console.log('🛡️ No life insurance plan or waive status found');
  }

  // Handle AD&D insurance
  console.log('🛡️ AD&D insurance check:', { addPlan, addWaived });
  if (addPlan) {
    console.log('🛡️ Processing AD&D plan:', addPlan);
    const addCost = getPlanCostBreakdown(addPlan, 'AD&D');
    console.log('🛡️ AD&D cost breakdown:', addCost);
    if (addCost) {
      planCosts.push(addCost);
      console.log('🛡️ AD&D cost added to planCosts');
    } else {
      console.log('🛡️ AD&D cost breakdown returned null - plan will not be displayed');
    }
  } else if (addWaived) {
    console.log('🛡️ AD&D waived');
    waivedCoverages.push('AD&D');
  } else {
    console.log('🛡️ No AD&D plan or waive status found');
  }

  // Calculate totals
  const totalEmployeeCost = planCosts.reduce((sum, plan) => sum + plan.employeeCost, 0);
  const totalEmployerCost = planCosts.reduce((sum, plan) => sum + plan.employerCost, 0);
  const grandTotal = planCosts.reduce((sum, plan) => sum + plan.totalCost, 0);

  console.log('💰 Plan costs calculated:', planCosts);
  console.log('💰 Totals:', { totalEmployeeCost, totalEmployerCost, grandTotal });



  return (
    <div style={{
      display: 'flex',
      flexDirection: 'column',
      gap: '24px',
      fontFamily: '"SF Pro", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif'
    }}>
      {/* Bot Message */}
      <div style={{ display: 'flex', gap: '16px' }}>
        <div style={{
          width: '40px',
          height: '40px',
          borderRadius: '50%',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          flexShrink: 0,
          overflow: 'hidden'
        }}>
          <Image
            src="/brea.png"
            alt="Brea - AI Benefits Assistant"
            width={40}
            height={40}
            style={{ borderRadius: '50%' }}
          />
        </div>
        <div style={{
          backgroundColor: '#f9fafb',
          borderRadius: '8px',
          padding: '16px',
          maxWidth: '1000px'
        }}>
          <p style={{ 
            color: '#374151', 
            lineHeight: '1.6', 
            margin: 0 
          }}>
            📋 Perfect! Here&apos;s your personalized benefits package:
          </p>
          <p style={{ 
            color: '#374151', 
            lineHeight: '1.6', 
            margin: '8px 0 0 0' 
          }}>
            Take a moment to review everything. You can always go back and make changes if needed.
          </p>
        </div>
      </div>

      {/* Summary Card */}
      <div style={{
        backgroundColor: 'white',
        borderRadius: '12px',
        border: '1px solid #e5e7eb',
        padding: '32px',
        boxShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.1)'
      }}>
        {/* Header */}
        <div style={{ marginBottom: '24px' }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: '8px', marginBottom: '16px' }}>
            <span style={{ fontSize: '24px' }}>📄</span>
            <h1 style={{
              fontSize: '24px',
              fontWeight: '600',
              color: '#111827',
              margin: 0
            }}>
              Enrollment Summary
            </h1>
          </div>
        </div>

        {/* Dynamic Plan Summary Section */}
        {(() => {
          const dynamicSummary = getDynamicEnrollmentSummary();
          const hasNewFormatData = (dynamicSummary.selectedPlans && dynamicSummary.selectedPlans.length > 0) ||
                                   (dynamicSummary.waivedCoverages && dynamicSummary.waivedCoverages.length > 0);

          if (hasNewFormatData) {
            return (
              <div style={{
                backgroundColor: '#f8fffe',
                borderRadius: '12px',
                padding: '24px',
                marginBottom: '24px',
                border: '1px solid #d1fae5'
              }}>
                <DynamicSummarySection showCosts={true} showWaived={true} style="summary" />
              </div>
            );
          }

          // Fallback to legacy format
          return null;
        })()}

        {/* Legacy Enrollment Summary Section (fallback) */}
        {(() => {
          const dynamicSummary = getDynamicEnrollmentSummary();
          const hasNewFormatData = (dynamicSummary.selectedPlans && dynamicSummary.selectedPlans.length > 0) ||
                                   (dynamicSummary.waivedCoverages && dynamicSummary.waivedCoverages.length > 0);

          if (hasNewFormatData) {
            return null; // Don't show legacy if we have new format
          }

          return (
            <div style={{
              backgroundColor: '#f8fffe',
              borderRadius: '12px',
              padding: '24px',
              marginBottom: '24px',
              border: '1px solid #d1fae5'
            }}>
              {/* Show plans and waived coverages only if there are any */}
              {(planCosts.length > 0 || waivedCoverages.length > 0) && (
            <>
              {/* Individual Plan Costs */}
          <div style={{ display: 'flex', flexDirection: 'column', gap: '16px', marginBottom: '24px' }}>
            {planCosts.map((plan, index) => {
              // Get plan type color and background
              const getTypeColor = (type: string) => {
                switch (type.toLowerCase()) {
                  case 'medical': return { bg: '#fef2f2', text: '#991b1b', price: '#dc2626' };
                  case 'dental': return { bg: '#f0fdf4', text: '#166534', price: '#059669' };
                  case 'vision': return { bg: '#eff6ff', text: '#1e40af', price: '#2563eb' };
                  case 'life insurance': return { bg: '#faf5ff', text: '#6b21a8', price: '#7c3aed' };
                  case 'ad&d': return { bg: '#fef3c7', text: '#92400e', price: '#d97706' };
                  default: return { bg: '#f9fafb', text: '#374151', price: '#111827' };
                }
              };

              const colors = getTypeColor(plan.planType);

              return (
                <div key={index} style={{
                  backgroundColor: colors.bg,
                  borderRadius: '8px',
                  padding: '16px',
                  border: '1px solid #e5e7eb'
                }}>
                  <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', marginBottom: '5px' }}>
                    <div>
                      <h3 style={{
                        fontSize: '16px',
                        fontWeight: '600',
                        color: '#111827',
                        margin: '0 0 4px 0'
                      }}>
                        {plan.planType}: {plan.planName}
                      </h3>
                      <p style={{
                        fontSize: '14px',
                        color: colors.text,
                        margin: 0,
                        fontWeight: '500'
                      }}>
                        {plan.planType === 'Medical' ? 'HMO Plan' :
                         plan.planType === 'Dental' ? 'PPO Plan' :
                         plan.planType === 'Vision' ? 'Choice Plan' :
                         plan.planType === 'Life Insurance' ? 'Term Life' :
                         plan.planType === 'AD&D' ? 'Accident Coverage' : 'Coverage Plan'}
                      </p>
                    </div>
                    <div style={{ textAlign: 'right' }}>
                      <p style={{
                        fontSize: '16px',
                        fontWeight: '600',
                        color: colors.price,
                        margin: 0
                      }}>
                        You pay: ${plan.employeeCost.toFixed(2)}
                      </p>
                    </div>
                  </div>

                  <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <div style={{ display: 'flex', alignItems: 'center', gap: '2px' }}>
                      <span style={{ fontSize: '14px', color: '#6b7280' }}></span>
                      <span style={{ fontSize: '14px', color: '#374151' }}>
                        Employer pays: ${plan.employerCost.toFixed(2)}
                      </span>
                    </div>
                    
                  </div>
                </div>
              );
            })}
          </div>

          {/* Total Summary */}
          <div style={{ display: 'flex', flexDirection: 'column', gap: '12px', paddingTop: '16px', borderTop: '1px solid #e5e7eb' }}>
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <span style={{ fontSize: '16px', fontWeight: '600', color: '#111827' }}>
                Total Plan Cost:
              </span>
              <span style={{ fontSize: '16px', fontWeight: '600', color: '#111827' }}>
                ${grandTotal.toFixed(2)}
              </span>
            </div>

            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <span style={{ fontSize: '16px', fontWeight: '600', color: '#111827' }}>
                Employer Contribution:
              </span>
              <span style={{ fontSize: '16px', fontWeight: '600', color: '#10b981' }}>
                -${totalEmployerCost.toFixed(2)}
              </span>
            </div>

            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', paddingTop: '8px', borderTop: '1px solid #e5e7eb' }}>
              <span style={{ fontSize: '18px', fontWeight: '700', color: '#111827' }}>
                Your Cost per Paycheck:
              </span>
              <span style={{ fontSize: '24px', fontWeight: '700', color: '#2563eb' }}>
                ${totalEmployeeCost.toFixed(2)}
              </span>
            </div>
          </div>

          {/* Waived Coverages */}
          {waivedCoverages.map((coverage, index) => (
            <div key={index} style={{
              backgroundColor: '#fef2f2',
              borderRadius: '8px',
              padding: '16px',
              border: '1px solid #fecaca',
              marginTop: '16px'
            }}>
              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', marginBottom: '8px' }}>
                <div>
                  <h3 style={{
                    fontSize: '16px',
                    fontWeight: '600',
                    color: '#111827',
                    margin: '0 0 4px 0'
                  }}>
                    {coverage}: Waived
                  </h3>
                  <p style={{
                    fontSize: '14px',
                    color: '#dc2626',
                    margin: 0,
                    fontWeight: '500'
                  }}>
                    No Coverage
                  </p>
                </div>
                <div style={{ textAlign: 'right' }}>
                  <p style={{
                    fontSize: '16px',
                    fontWeight: '600',
                    color: '#dc2626',
                    margin: 0
                  }}>
                    You pay: $0.00
                  </p>
                </div>
              </div>

              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <div style={{ display: 'flex', alignItems: 'center', gap: '4px' }}>
                  <span style={{ fontSize: '14px', color: '#dc2626' }}>⚠️</span>
                  <span style={{ fontSize: '14px', color: '#374151' }}>
                    No {coverage.toLowerCase()} benefits
                  </span>
                </div>
                <div style={{ fontSize: '14px', fontWeight: '600', color: '#dc2626' }}>
                  You pay: $0.00
                </div>
              </div>
            </div>
          ))}
        </>
      )}

          {/* Fallback: Show basic plan info if cost calculation fails */}
          {planCosts.length === 0 && (dentalPlan || visionPlan || lifePlan || addPlan) && (
            <div style={{ marginBottom: '24px' }}>
              <h3 style={{ fontSize: '16px', fontWeight: '600', color: '#111827', marginBottom: '16px' }}>
                Selected Plans (Basic Info)
              </h3>

              <div style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
                {dentalPlan && (
                  <div style={{
                    backgroundColor: '#f0fdf4',
                    borderRadius: '8px',
                    padding: '16px',
                    border: '1px solid #e5e7eb'
                  }}>
                    <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
                      <div>
                        <h3 style={{ fontSize: '16px', fontWeight: '600', color: '#111827', margin: '0 0 4px 0' }}>
                          Dental: {dentalPlan.name || dentalPlan.planName || 'Selected Plan'}
                        </h3>
                        <p style={{ fontSize: '14px', color: '#166534', margin: 0, fontWeight: '500' }}>
                          PPO Plan
                        </p>
                      </div>
                      <div style={{ textAlign: 'right' }}>
                        <p style={{ fontSize: '16px', fontWeight: '600', color: '#059669', margin: 0 }}>
                          Cost info unavailable
                        </p>
                      </div>
                    </div>
                  </div>
                )}

                {visionPlan && (
                  <div style={{
                    backgroundColor: '#eff6ff',
                    borderRadius: '8px',
                    padding: '16px',
                    border: '1px solid #e5e7eb'
                  }}>
                    <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
                      <div>
                        <h3 style={{ fontSize: '16px', fontWeight: '600', color: '#111827', margin: '0 0 4px 0' }}>
                          Vision: {visionPlan.name || visionPlan.planName || 'Selected Plan'}
                        </h3>
                        <p style={{ fontSize: '14px', color: '#1e40af', margin: 0, fontWeight: '500' }}>
                          Choice Plan
                        </p>
                      </div>
                      <div style={{ textAlign: 'right' }}>
                        <p style={{ fontSize: '16px', fontWeight: '600', color: '#2563eb', margin: 0 }}>
                          Cost info unavailable
                        </p>
                      </div>
                    </div>
                  </div>
                )}

                {lifePlan && (
                  <div style={{
                    backgroundColor: '#faf5ff',
                    borderRadius: '8px',
                    padding: '16px',
                    border: '1px solid #e5e7eb'
                  }}>
                    <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
                      <div>
                        <h3 style={{ fontSize: '16px', fontWeight: '600', color: '#111827', margin: '0 0 4px 0' }}>
                          Life Insurance: {lifePlan.name || lifePlan.planName || 'Selected Plan'}
                        </h3>
                        <p style={{ fontSize: '14px', color: '#6b21a8', margin: 0, fontWeight: '500' }}>
                          Term Life
                        </p>
                      </div>
                      <div style={{ textAlign: 'right' }}>
                        <p style={{ fontSize: '16px', fontWeight: '600', color: '#7c3aed', margin: 0 }}>
                          Cost info unavailable
                        </p>
                      </div>
                    </div>
                  </div>
                )}

                {addPlan && (
                  <div style={{
                    backgroundColor: '#fef3c7',
                    borderRadius: '8px',
                    padding: '16px',
                    border: '1px solid #e5e7eb'
                  }}>
                    <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
                      <div>
                        <h3 style={{ fontSize: '16px', fontWeight: '600', color: '#111827', margin: '0 0 4px 0' }}>
                          AD&D: {addPlan.name || addPlan.planName || 'Selected Plan'}
                        </h3>
                        <p style={{ fontSize: '14px', color: '#92400e', margin: 0, fontWeight: '500' }}>
                          Accident Coverage
                        </p>
                      </div>
                      <div style={{ textAlign: 'right' }}>
                        <p style={{ fontSize: '16px', fontWeight: '600', color: '#d97706', margin: 0 }}>
                          Cost info unavailable
                        </p>
                      </div>
                    </div>
                  </div>
                )}
              </div>

              <div style={{
                backgroundColor: '#fef3c7',
                borderRadius: '6px',
                padding: '12px',
                marginTop: '16px',
                border: '1px solid #fbbf24'
              }}>
                <p style={{ fontSize: '14px', color: '#92400e', margin: 0 }}>
                  ⚠️ Cost information is not available. Please contact your HR administrator for pricing details.
                </p>
              </div>
            </div>
          )}
            </div>
          );
        })()}

        {/* Family Contribution Breakdown */}
        {planCosts.length > 0 && (
          <div style={{ marginTop: '16px', paddingTop: '16px', borderTop: '1px solid #e5e7eb', display: 'none' }}>
            <h3 style={{ fontSize: '16px', fontWeight: '600', color: '#111827', marginBottom: '12px' }}>
              Family Contribution Breakdown:
            </h3>
            <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
              {planCosts.map((plan, index) => (
                <div key={index} style={{
                  backgroundColor: '#f9fafb',
                  padding: '12px',
                  borderRadius: '6px',
                  border: '1px solid #e5e7eb'
                }}>
                  <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '8px' }}>
                    <span style={{ fontSize: '14px', fontWeight: '600', color: '#111827' }}>
                      {plan.planType}: {plan.planName}
                    </span>
                    <span style={{ fontSize: '14px', fontWeight: '600', color: '#2563eb' }}>
                      You pay: ${plan.employeeCost.toFixed(2)}
                    </span>
                  </div>
                  <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '8px', fontSize: '12px' }}>
                    <div style={{ display: 'flex', alignItems: 'center' }}>
                      <span style={{ color: '#6b7280' }}>Employer pays: </span>
                      <span style={{ color: '#10b981', fontWeight: '500' }}>${plan.employerCost.toFixed(2)}</span>
                    </div>
                    <div style={{ display: 'flex', alignItems: 'center' }}>
                      <span style={{ color: '#6b7280' }}>You pay: </span>
                      <span style={{ color: '#dc2626', fontWeight: '500' }}>${plan.employeeCost.toFixed(2)}</span>
                    </div>
                  </div>
                </div>
              ))}

              {/* Total Summary */}
              <div style={{
                backgroundColor: '#eff6ff',
                padding: '12px',
                borderRadius: '6px',
                border: '1px solid #bfdbfe',
                marginTop: '8px'
              }}>
                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '8px' }}>
                  <span style={{ fontSize: '14px', fontWeight: '600', color: '#111827' }}>
                    Total Family Coverage
                  </span>
                  <span style={{ fontSize: '14px', fontWeight: '600', color: '#2563eb' }}>
                    You pay: ${totalEmployeeCost.toFixed(2)}
                  </span>
                </div>
                <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '8px', fontSize: '12px' }}>
                  <div style={{ display: 'flex', alignItems: 'center' }}>
                    <span style={{ color: '#6b7280' }}>Total employer contribution: </span>
                    <span style={{ color: '#10b981', fontWeight: '600' }}>${totalEmployerCost.toFixed(2)}</span>
                  </div>
                  <div style={{ display: 'flex', alignItems: 'center' }}>
                    <span style={{ color: '#6b7280' }}>Total you pay: </span>
                    <span style={{ color: '#dc2626', fontWeight: '600' }}>${totalEmployeeCost.toFixed(2)}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Family Members Information - Always show employee, plus dependents if any */}
        <div style={{ marginTop: '16px', paddingTop: '16px', borderTop: '1px solid #e5e7eb', display: 'none' }}>
          <h3 style={{ fontSize: '16px', fontWeight: '600', color: '#111827', marginBottom: '12px' }}>
            {enrollmentData.dependents && enrollmentData.dependents.length > 0 ? 'Family Members Covered:' : 'Coverage For:'}
          </h3>
          <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
            {/* Always show the employee */}
            <div style={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              backgroundColor: '#f0f9ff',
              padding: '12px',
              borderRadius: '6px',
              border: '1px solid #bfdbfe'
            }}>
              <div>
                <span style={{ fontSize: '14px', fontWeight: '600', color: '#111827' }}>
                  {userDetails.name || 'Myself'}
                </span>
                <span style={{ fontSize: '14px', color: '#6b7280', marginLeft: '8px' }}>
                  (Employee)
                </span>
              </div>
              <span style={{ fontSize: '12px', color: '#10b981', fontWeight: '500' }}>
                ✓ Covered
              </span>
            </div>

            {/* Show dependents if any */}
            {enrollmentData.dependents && enrollmentData.dependents.map((dependent, index) => (
              <div key={index} style={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
                backgroundColor: '#f9fafb',
                padding: '12px',
                borderRadius: '6px',
                border: '1px solid #e5e7eb'
              }}>
                <div>
                  <span style={{ fontSize: '14px', fontWeight: '500', color: '#111827' }}>
                    {dependent.name}
                  </span>
                  <span style={{ fontSize: '14px', color: '#6b7280', marginLeft: '8px' }}>
                    ({dependent.relationship === 'Spouse' ? 'Spouse/Partner' : 'Child'})
                  </span>
                </div>
                <span style={{ fontSize: '12px', color: '#10b981', fontWeight: '500' }}>
                  ✓ Covered
                </span>
              </div>
            ))}
          </div>
        </div>

        {(planCosts.length > 0 || waivedCoverages.length > 0) && (
          <div style={{ display: 'flex', gap: '12px', marginTop: '24px' }}>
            <button
              onClick={onMakeChanges}
              style={{
                flex: 1,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                gap: '8px',
                padding: '12px 20px',
                backgroundColor: 'white',
                border: '1px solid #d1d5db',
                borderRadius: '8px',
                color: '#374151',
                cursor: 'pointer',
                fontWeight: '500',
                fontSize: '16px'
              }}
            >
              <Edit size={16} />
              Make Changes
            </button>

            <button
              onClick={handleConfirmEnrollment}
              disabled={creatingEnrollments}
              style={{
                flex: 2,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                gap: '8px',
                padding: '12px 20px',
                backgroundColor: creatingEnrollments ? '#666666' : '#1f2937',
                border: 'none',
                borderRadius: '8px',
                color: 'white',
                cursor: creatingEnrollments ? 'not-allowed' : 'pointer',
                fontWeight: '500',
                fontSize: '16px',
                opacity: creatingEnrollments ? 0.7 : 1,
                transition: 'all 0.2s ease'
              }}
            >
              <CheckCircle size={16} />
              {creatingEnrollments ? 'Creating Enrollments...' : 'Confirm Enrollment'}
            </button>
          </div>
        )}



        {/* No Plans Selected Message */}
        {planCosts.length === 0 && waivedCoverages.length === 0 && (
          <div style={{
            backgroundColor: '#fef3c7',
            borderRadius: '8px',
            padding: '20px',
            marginTop: '24px',
            border: '1px solid #fbbf24',
            textAlign: 'center'
          }}>
            <h3 style={{ fontSize: '18px', fontWeight: '600', color: '#92400e', margin: '0 0 8px 0' }}>
              No Plans Selected
            </h3>
            <p style={{ color: '#92400e', margin: 0 }}>
              You haven&apos;t selected any benefit plans or waived any coverages yet. Go back to make your selections.
            </p>
          </div>
        )}
      </div>

      {/* Success/Error Modal */}
      {showErrorModal && (
        <div style={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundColor: 'rgba(0, 0, 0, 0.5)',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          zIndex: 1000
        }}>
          <div style={{
            backgroundColor: 'white',
            borderRadius: '12px',
            padding: '32px',
            maxWidth: '600px',
            width: '90%',
            boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1)',
            textAlign: 'center'
          }}>
            <div style={{
              width: '60px',
              height: '60px',
              backgroundColor: '#ef4444',
              borderRadius: '50%',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              margin: '0 auto 20px'
            }}>
              <span style={{ fontSize: '32px', color: 'white' }}>⚠️</span>
            </div>
            <h3 style={{
              fontSize: '20px',
              fontWeight: '600',
              color: '#111827',
              margin: '0 0 12px 0'
            }}>
              Enrollment Error
            </h3>
            <p style={{
              color: '#6b7280',
              margin: '0 0 24px 0',
              lineHeight: '1.5',
              whiteSpace: 'pre-line'
            }}>
              {errorMessage}
            </p>
            <button
              onClick={() => {
                setShowErrorModal(false);
                // Stay on the same page for errors so user can try again
              }}
              style={{
                padding: '12px 24px',
                backgroundColor: '#dc2626',
                border: 'none',
                borderRadius: '8px',
                color: 'white',
                cursor: 'pointer',
                fontWeight: '500',
                fontSize: '16px'
              }}
            >
              Try Again
            </button>
          </div>
        </div>
      )}

      {/* Signature Modal */}
      <SignatureModal
        isOpen={showSignatureModal}
        onClose={() => setShowSignatureModal(false)}
        onSignatureComplete={handleSignatureComplete}
        employeeName={userDetails.name || 'Employee'}
      />
    </div>
  );
};

export default SummaryPage;
