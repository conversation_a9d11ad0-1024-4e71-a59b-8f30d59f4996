'use client';

import React, { useState, useRef, useEffect } from 'react';
import './employee-enrol.css';
import { ArrowLeft, ArrowRight, Bell, Brain, Stethoscope, Smile, Eye, FileText, CheckCircle, Users, Shield } from 'lucide-react';
import { WelcomePage } from './components/WelcomePage';
import { PersonalizationPage } from './components/PersonalizationPage';
import DependentsConfirmationPage from './components/DependentsConfirmationPage';
// Legacy plan page imports - keeping for backward compatibility if needed
// import DentalPlanPage from './components/DentalPlanPage';
// import VisionPlanPage from './components/VisionPlanPage';
// import LifeInsurancePlanPage from './components/LifeInsurancePlanPage';
// import ADDPlanPage from './components/ADDPlanPage';
import DynamicPlanPage from './components/DynamicPlanPage';
import SummaryPage from './components/SummaryPage';
import ConfirmationPage from './components/ConfirmationPage';
import FloatingChatButton from './components/FloatingChatButton';
import EnrollmentHeader from './components/EnrollmentHeader';
import enrollmentService from './services/enrollmentService';
import { getPlanAssignmentsByCompany } from '../manage-groups/services/planAssignmentApi';
import { getRequest } from '@/APILayer/axios_helper';
import { useAuth } from '@/components/AuthContext';
import { useAppSelector } from '@/redux/hooks';
import { RootState } from '@/redux/store';
import {
  extractPlanCategories,
  getThemeIndex,
  getPlansForCategory,
  getCategoryStorageKey
} from './utils/planCategoryUtils';
import { getStoredPlan, isCategoryWaived } from './utils/planStorageUtils';


export interface UserProfile {
  familyMembers: string;
  expectedMedicalUsage: string;
  budgetPreference: string;
  chronicConditions: boolean;
  prescriptionNeeds: boolean;
  hasPreferredDoctors: boolean;
  selectedMedical?: any;
  selectedDental?: any;
  selectedVision?: any;
  selectedLife?: any;
  selectedADD?: any;

}

export interface ChatStep {
  id: string;
  title: string;
  icon: any;
  completed: boolean;
}

export default function EmployeeEnrollmentPage() {
  const { logout } = useAuth();
  const userDetailsFromRedux = useAppSelector((state: RootState) => state.user.userProfile);

  const [currentStep, setCurrentStep] = useState(0);
  const [userProfile, setUserProfile] = useState<UserProfile>({
    familyMembers: '',
    expectedMedicalUsage: '',
    budgetPreference: '',
    chronicConditions: false,
    prescriptionNeeds: false,
    hasPreferredDoctors: false,

  });
  const [recommendation, setRecommendation] = useState<any>(null);
  const [enrollmentComplete, setEnrollmentComplete] = useState(false);
  const [selectedDependents, setSelectedDependents] = useState<string[]>([]);
  const [confirmedDependents, setConfirmedDependents] = useState<any[]>([]);
  const [currentDependents, setCurrentDependents] = useState<any[]>([]);
  const scrollRef = useRef<HTMLDivElement>(null);

  // Plan assignments state
  const [planAssignments, setPlanAssignments] = useState<Record<string, any[]>>({});
  const [loadingPlans, setLoadingPlans] = useState(false);
  const [userDetails, setUserDetails] = useState<any>(null);

  // Dynamic plan categories state
  const [planCategories, setPlanCategories] = useState<string[]>([]);
  const [dynamicSteps, setDynamicSteps] = useState<any[]>([]);

  // Helper function to get coverage tier name based on family selection
  const getCoverageTierName = (familyMembers: string): string => {
    switch (familyMembers) {
      case 'employee-only':
        return 'Employee Only';
      case 'employee-spouse':
        return 'Employee + Spouse';
      case 'employee-children':
        return 'Employee + Child(ren)';
      case 'employee-family':
        return 'Family';
      default:
        return 'Employee Only';
    }
  };

  // Generate dynamic steps based on available plan categories
  const generateSteps = (): ChatStep[] => {
    const baseSteps = [
      { id: 'kickoff', title: 'Welcome & Kickoff', icon: Bell, completed: false },
      { id: 'personalization', title: 'Smart Personalization', icon: Brain, completed: false },
      { id: 'dependents', title: 'Family Members', icon: Users, completed: false },
    ];

    // Add dynamic plan steps
    const planSteps = planCategories.map((category, index) => ({
      id: `plan-${index}`,
      title: `${category} Plan`,
      icon: category === 'Dental' ? Smile :
            category === 'Vision' ? Eye :
            category === 'Life Insurance' ? Stethoscope :
            category === 'AD&D Insurance' ? Shield : FileText,
      completed: false
    }));

    const endSteps = [
      { id: 'summary', title: 'Summary Review', icon: FileText, completed: false },
      { id: 'confirmation', title: 'Confirmation', icon: CheckCircle, completed: false },
    ];

    return [...baseSteps, ...planSteps, ...endSteps];
  };

  const steps: ChatStep[] = generateSteps();

  // Fetch user details and plan assignments on component mount
  useEffect(() => {
    const fetchUserAndPlans = async () => {
      try {
        // Get user details from localStorage
        const userId = localStorage.getItem('userid1') || localStorage.getItem('userId');
        if (!userId) {
          console.warn('No user ID found in localStorage');
          return;
        }

        console.log('🔍 Fetching user details for:', userId);

        // Fetch user details to get company ID using correct employee endpoint
        const userResponse = await fetch(`${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8080'}/employee`, {
          headers: {
            'user-id': userId
          }
        });

        if (userResponse.ok) {
          const userData = await userResponse.json();
          console.log('👤 User details:', userData);
          const currentUser = userData.currentUser;
          setUserDetails(currentUser); // Extract currentUser from response

          console.log('🔍 Current user extracted:', currentUser);
          console.log('🏢 Company ID from user:', currentUser?.companyId);

          // If user has a company, fetch plan assignments using the working API
          if (currentUser && currentUser.companyId) {
            console.log('🏢 Fetching plan assignments for company:', currentUser.companyId);
            setLoadingPlans(true);

            // Use the same API that works in manage-groups
            const planResult = await getPlanAssignmentsByCompany(currentUser.companyId, { includePlanData: true });

            if (planResult.success && planResult.data) {
              console.log('✅ Plan assignments loaded:', planResult.data);

              // Process the assignments to group by coverage subtype
              const assignments = planResult.data.assignments || [];
              const groupedPlans: Record<string, any[]> = {};

              // Fetch plan details for each assignment and group by coverage subtype
              for (const assignment of assignments) {
                try {
                  console.log('🔍 Processing assignment:', assignment._id, 'planId:', assignment.planId);

                  // Fetch plan details using the working enrollment service
                  // Handle case where planId might be an object or string
                  const planId = typeof assignment.planId === 'string'
                    ? assignment.planId
                    : (assignment.planId as any)?._id || String(assignment.planId);
                  const planDetailsResult = await enrollmentService.getPlanById(planId);

                  if (planDetailsResult.success && planDetailsResult.data) {
                    const plan = planDetailsResult.data;
                    console.log('✅ Plan details:', {
                      planName: plan.planName,
                      coverageSubTypes: plan.coverageSubTypes
                    });

                    // Create enriched plan object
                    const enrichedPlan = {
                      ...plan,
                      assignment: assignment,
                      id: assignment._id,
                      name: plan.planName,
                      planCode: plan.planCode,
                      carrierName: plan.carrierName,
                      coverageType: plan.coverageType,
                      coverageSubTypes: plan.coverageSubTypes,
                      // Store all coverage tiers for dynamic cost calculation
                      coverageTiers: assignment.coverageTiers,
                      cost: enrollmentService.calculatePlanCost(assignment), // Default cost
                      features: enrollmentService.extractPlanFeatures(plan, assignment)
                    };

                    // Group by coverage subtypes
                    if (plan.coverageSubTypes && Array.isArray(plan.coverageSubTypes)) {
                      plan.coverageSubTypes.forEach((subType: string) => {
                        const key = subType.toLowerCase();
                        if (!groupedPlans[key]) {
                          groupedPlans[key] = [];
                        }
                        groupedPlans[key].push(enrichedPlan);
                      });
                    }
                  } else {
                    console.warn('⚠️ Failed to fetch plan details for:', assignment.planId);
                  }
                } catch (error) {
                  console.error('❌ Error processing assignment:', assignment._id, error);
                }
              }

              console.log('🎯 Grouped plans by subtype:', Object.keys(groupedPlans));
              setPlanAssignments(groupedPlans);

              // Extract plan categories for dynamic steps
              const categories = extractPlanCategories(groupedPlans);
              console.log('📋 Extracted plan categories:', categories);
              setPlanCategories(categories);
            } else {
              console.warn('⚠️ Failed to load plan assignments:', planResult.error);
              setPlanAssignments({});
            }
          } else {
            console.warn('⚠️ User has no company ID');
          }
        } else {
          console.error('❌ Failed to fetch user details');
        }
      } catch (error) {
        console.error('❌ Error fetching user and plans:', error);
      } finally {
        setLoadingPlans(false);
      }
    };

    fetchUserAndPlans();
  }, []);

  const getProgressPercentage = () => {
    return (currentStep / (steps.length - 1)) * 100;
  };

  // Helper function to check if current step has required selection
  const canProceedFromStep = (step: number): boolean => {
    // Steps 0-2 are welcome, personalization, and dependents - no validation needed
    if (step < 3) return true;

    // Calculate which plan category this step represents
    const planStepIndex = step - 3; // Subtract the 3 base steps
    const totalPlanSteps = planCategories.length;

    // If this is a plan step
    if (planStepIndex >= 0 && planStepIndex < totalPlanSteps) {
      const category = planCategories[planStepIndex];
      const storageKey = getCategoryStorageKey(category);

      // Check both new and legacy storage systems
      const selectedPlan = localStorage.getItem(`selected${category.replace(/\s+/g, '')}Plan`);
      const waived = localStorage.getItem(`${storageKey}Waived`);

      // Also check new storage system
      const hasNewFormatPlan = getStoredPlan(category) !== null;
      const hasNewFormatWaive = isCategoryWaived(category);

      return selectedPlan !== null || waived === 'true' || hasNewFormatPlan || hasNewFormatWaive;
    }

    // Summary and confirmation steps don't require validation
    return true;
  };

  // Profile validation function
  const validateUserProfile = (user: any): { isValid: boolean; missingFields: string[]; errors: string[] } => {
    const missingFields: string[] = [];
    const errors: string[] = [];

    // Required fields for enrollment
    if (!user?.details?.dateOfBirth) {
      missingFields.push('Date of Birth');
      errors.push('Date of birth is required for age-based cost calculations');
    }

    if (!user?.details?.hireDate) {
      missingFields.push('Hire Date');
      errors.push('Hire date is required for waiting period eligibility');
    }

    if (!user?.details?.employeeClassType) {
      missingFields.push('Employee Class Type');
      errors.push('Employee class type is required for plan eligibility');
    }

    if (!user?.details?.phoneNumber) {
      missingFields.push('Phone Number');
      errors.push('Phone number is required for enrollment communications');
    }

    return {
      isValid: missingFields.length === 0,
      missingFields,
      errors
    };
  };

  const handleNext = () => {
    if (currentStep === 2) {
      // For step 2 (dependents), validate profile before proceeding
      const profileValidation = validateUserProfile(userDetails);

      if (!profileValidation.isValid) {
        // Profile is incomplete - show alert with missing fields
        const missingFieldsList = profileValidation.missingFields.join(', ');
        alert(`Please complete your profile before continuing. Missing required fields: ${missingFieldsList}\n\nClick the "Complete Profile" button on the dependents page to update your information.`);
        return;
      }

      // Profile is valid, proceed with dependents confirmation
      console.log('Step 2 Next clicked, currentDependents:', currentDependents);
      handleDependentsConfirm(currentDependents);
    } else if (currentStep < steps.length - 1) {
      // Check if current step has required selection before proceeding
      if (canProceedFromStep(currentStep)) {
        setCurrentStep(currentStep + 1);
      } else {
        // Show alert or notification that selection is required
        alert('Please select a plan or choose to waive coverage before proceeding.');
      }
    }
  };

  const handleBack = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handlePersonalizationComplete = (profile: Partial<UserProfile>) => {
    setUserProfile(prev => ({ ...prev, ...profile }));

    // Store the coverage tier in localStorage for cost calculations
    const coverageTier = getCoverageTierName(profile.familyMembers || 'employee-only');
    localStorage.setItem('selectedCoverageTier', coverageTier);
    console.log('🎯 Stored coverage tier in localStorage:', coverageTier);

    // Extract dependents from family members selection
    const dependents: string[] = [];
    if (profile.familyMembers === 'employee-spouse') {
      dependents.push('spouse');
    } else if (profile.familyMembers === 'employee-children') {
      dependents.push('children');
    } else if (profile.familyMembers === 'employee-family') {
      dependents.push('spouse', 'children');
    }

    setSelectedDependents(dependents);

    // Generate recommendation based on profile
    const rec = getSmartRecommendation(profile);
    setRecommendation(rec);

    handleNext();
  };

  const handleDependentsConfirm = (dependents: any[]) => {
    console.log('handleDependentsConfirm called with:', dependents);
    setConfirmedDependents(dependents);
    setCurrentStep(currentStep + 1);
  };

  // Get user ID from localStorage
  const getUserId = () => {
    const primaryKey = process.env.NEXT_PUBLIC_USER_ID_KEY || "userid1";
    const altKey = process.env.NEXT_PUBLIC_USER_ID_ALT_KEY || "userId";
    return localStorage.getItem(primaryKey) || localStorage.getItem(altKey);
  };

  // Function to refresh user details after profile update
  const refreshUserDetails = async () => {
    try {
      const userId = getUserId();
      if (!userId) {
        console.error('User ID not found');
        return;
      }

      const response = await getRequest('/employee', { 'user-id': userId });

      if (response?.currentUser) {
        setUserDetails(response.currentUser);
        console.log('✅ User details refreshed after profile update');
      }
    } catch (error) {
      console.error('❌ Error refreshing user details:', error);
    }
  };

  const handleDependentsBack = () => {
    handleBack();
  };

  const getSmartRecommendation = (profile: any) => {
    const { expectedMedicalUsage, budgetPreference } = profile;

    if (budgetPreference === 'balanced' || expectedMedicalUsage === 'moderate') {
      return {
        plan: {
          name: "Anthem PPO 035",
          cost: 82.90,
          deductible: 2000,
          features: [
            "Balanced cost and coverage",
            "Deductible: $2,000",
            "Covers your PCP visits at $25",
            "Good for moderate usage & predictable costs"
          ]
        },
        reason: "This plan offers the best balance of monthly cost and coverage for someone with moderate healthcare needs."
      };
    }

    return {
      plan: {
        name: "Anthem PPO 035",
        cost: 82.90,
        deductible: 2000,
        features: [
          "Balanced cost and coverage",
          "Deductible: $2,000",
          "Covers your PCP visits at $25",
          "Good for moderate usage & predictable costs"
        ]
      },
      reason: "This plan offers the best balance of monthly cost and coverage for someone with moderate healthcare needs."
    };
  };

  const handlePlanSelection = (planType: 'medical' | 'dental' | 'vision' | 'life' | 'add', planData: any) => {
    const propertyName = `selected${planType.charAt(0).toUpperCase() + planType.slice(1)}`;
    console.log(`🔄 Setting ${propertyName} with data:`, planData);

    // Special logging for life insurance
    if (planType === 'life') {
      console.log('🛡️ Life insurance plan selection details:', {
        planData,
        hasId: !!planData?.id,
        hasName: !!planData?.name,
        hasCoverageTiers: !!planData?.coverageTiers,
        keys: planData ? Object.keys(planData) : 'null'
      });
    }

    setUserProfile(prev => {
      const updated = {
        ...prev,
        [propertyName]: planData
      };
      console.log('📝 Updated user profile:', updated);

      // Special logging for life insurance in user profile
      if (planType === 'life') {
        console.log('🛡️ Life insurance in user profile:', updated.selectedLife);
      }

      return updated;
    });
    // Don't auto-advance - let user manually navigate with Next button
  };



  const handleMakeChanges = () => {
    setCurrentStep(1); // Go back to step 2 (personalization) with preserved values
  };

  const handleConfirmEnrollment = () => {
    console.log('🎯 Enrollment confirmed! Moving to confirmation page...');
    console.log('🔍 Current user profile before creating summary:', userProfile);

    // Get plan data from localStorage as fallback if userProfile doesn't have it
    const getSelectedPlanData = (planKey: string, userProfilePlan: any) => {
      if (userProfilePlan) {
        console.log(`✅ Using userProfile data for ${planKey}:`, userProfilePlan);
        return userProfilePlan;
      }

      const storedPlan = localStorage.getItem(planKey);
      if (storedPlan) {
        try {
          const parsed = JSON.parse(storedPlan);
          console.log(`✅ Using localStorage data for ${planKey}:`, parsed);
          return parsed;
        } catch (e) {
          console.error(`❌ Error parsing ${planKey} from localStorage:`, e);
        }
      }

      console.log(`⚠️ No data found for ${planKey}`);
      return null;
    };

    // Store enrollment summary data for confirmation page
    const enrollmentSummary = {
      dentalPlan: getSelectedPlanData('selectedDentalPlan', userProfile.selectedDental),
      visionPlan: getSelectedPlanData('selectedVisionPlan', userProfile.selectedVision),
      lifePlan: getSelectedPlanData('selectedLifePlan', userProfile.selectedLife),
      addPlan: getSelectedPlanData('selectedADDPlan', userProfile.selectedADD),
      dependents: confirmedDependents,
      selectedCoverageTier: getCoverageTierName(userProfile.familyMembers || 'employee-only'),
      enrollmentDate: new Date().toISOString(),
      // Store waived coverages
      dentalWaived: localStorage.getItem('dentalWaived') === 'true',
      visionWaived: localStorage.getItem('visionWaived') === 'true',
      lifeWaived: localStorage.getItem('lifeWaived') === 'true',
      addWaived: localStorage.getItem('addWaived') === 'true',
      // Store waive reasons
      dentalWaiveReason: localStorage.getItem('dentalWaiveReason'),
      visionWaiveReason: localStorage.getItem('visionWaiveReason'),
      lifeWaiveReason: localStorage.getItem('lifeWaiveReason'),
      addWaiveReason: localStorage.getItem('addWaiveReason')
    };

    // Special logging for all plan data
    console.log('🔍 All plan data in enrollment summary:', {
      dentalPlan: enrollmentSummary.dentalPlan,
      dentalWaived: enrollmentSummary.dentalWaived,
      visionPlan: enrollmentSummary.visionPlan,
      visionWaived: enrollmentSummary.visionWaived,
      lifePlan: enrollmentSummary.lifePlan,
      lifeWaived: enrollmentSummary.lifeWaived,
      addPlan: enrollmentSummary.addPlan,
      addWaived: enrollmentSummary.addWaived,
      userProfileData: {
        selectedDental: userProfile.selectedDental,
        selectedVision: userProfile.selectedVision,
        selectedLife: userProfile.selectedLife
      }
    });

    localStorage.setItem('enrollmentSummarySnapshot', JSON.stringify(enrollmentSummary));
    console.log('📸 Enrollment summary snapshot saved:', enrollmentSummary);

    setEnrollmentComplete(true);
    handleNext();
  };

  const renderCurrentPage = () => {
    switch (currentStep) {
      case 0:
        return <WelcomePage onNext={handleNext} />;
      case 1:
        return (
          <PersonalizationPage
            onComplete={handlePersonalizationComplete}
            initialData={userProfile}
          />
        );
      case 2:
        // Always show dependents confirmation page for profile validation
        // Filter dependents based on coverage tier selection from personalization
        const getFilteredDependents = (): string[] => {
          const familyMembers = userProfile?.familyMembers || 'employee-only';

          console.log('🔍 Coverage tier selected:', familyMembers);

          switch (familyMembers) {
            case 'employee-only':
              return []; // No dependents needed
            case 'employee-spouse':
              return ['spouse']; // Only spouse (matches DependentsConfirmationPage logic)
            case 'employee-children':
              return ['children']; // Only children (matches DependentsConfirmationPage logic)
            case 'employee-family':
              return ['spouse', 'children']; // Both spouse and children
            default:
              console.log('⚠️ Unknown family members selection:', familyMembers);
              return [];
          }
        };

        const filteredDependents = getFilteredDependents();
        console.log('👥 Filtered dependents for coverage tier:', filteredDependents);

        return (
          <DependentsConfirmationPage
            selectedDependents={filteredDependents}
            onConfirm={handleDependentsConfirm}
            onBack={handleDependentsBack}
            onDependentsChange={setCurrentDependents}
            onProfileUpdate={refreshUserDetails}
          />
        );
      default:
        // Handle dynamic plan steps (steps 3 and onwards until summary)
        const planStepIndex = currentStep - 3; // Subtract the 3 base steps
        const totalPlanSteps = planCategories.length;

        if (planStepIndex >= 0 && planStepIndex < totalPlanSteps) {
          const category = planCategories[planStepIndex];
          const plans = getPlansForCategory(category, planAssignments);
          const themeIndex = getThemeIndex(category, planCategories);

          return (
            <DynamicPlanPage
              category={category}
              plans={plans}
              selectedCoverageTier={getCoverageTierName(userProfile.familyMembers || 'employee-only')}
              themeIndex={themeIndex}
              onPlanSelect={(planData: any) => {
                // Map category to the expected plan type for handlePlanSelection
                const planTypeMap: Record<string, 'medical' | 'dental' | 'vision' | 'life' | 'add'> = {
                  'Medical': 'medical',
                  'Dental': 'dental',
                  'Vision': 'vision',
                  'Life Insurance': 'life',
                  'AD&D Insurance': 'add'
                };
                const planType = planTypeMap[category] || 'medical';
                handlePlanSelection(planType, planData);
              }}
            />
          );
        }

        // Handle summary and confirmation steps
        const summaryStepIndex = 3 + totalPlanSteps; // Base steps + plan steps
        const confirmationStepIndex = summaryStepIndex + 1;

        if (currentStep === summaryStepIndex) {
          return (
            <SummaryPage
              enrollmentData={{
                dentalPlan: userProfile.selectedDental,
                visionPlan: userProfile.selectedVision,
                lifePlan: userProfile.selectedLife,
                addPlan: userProfile.selectedADD,
                dependents: confirmedDependents
              }}
              selectedCoverageTier={getCoverageTierName(userProfile.familyMembers || 'employee-only')}
              onMakeChanges={handleMakeChanges}
              onConfirmEnrollment={handleConfirmEnrollment}
              planAssignments={planAssignments}
            />
          );
        }

        if (currentStep === confirmationStepIndex) {
          return (
            <ConfirmationPage
              enrollmentData={{
                ...userProfile,
                dependents: confirmedDependents
              }}
              planAssignments={planAssignments}
            />
          );
        }

        // Fallback to welcome page
        return <WelcomePage onNext={handleNext} />;
    }
  };

  return (
    <div className="min-h-screen" style={{
      backgroundColor: '#f3f4f6',
      fontFamily: '"SF Pro", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif'
    }}>
      {/* Header - Custom enrollment header with BenOsphere logo */}
      <EnrollmentHeader />

      {/* Main Content Container */}
      <div style={{
        maxWidth: '1024px',
        margin: '0 auto',
        padding: 'clamp(16px, 4vw, 32px) clamp(12px, 3vw, 24px)',
        minHeight: 'calc(100vh - 65px)' // Adjust for header height
      }}>
        {/* Header Section - Clean alignment */}
        <div style={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          marginBottom: '24px',
          flexWrap: 'wrap',
          gap: '16px'
        }}>
          <div style={{
            display: 'flex',
            alignItems: 'center',
            minWidth: '0',
            flex: '1'
          }}>
            <div style={{ minWidth: '0' }}>
              <h1 style={{
                fontSize: 'clamp(20px, 5vw, 24px)',
                fontWeight: '600',
                color: '#111827',
                margin: 0,
                fontFamily: "'SF Pro', 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif"
              }}>
                Your Smart Benefits Assistant
              </h1>
              <p style={{
                fontSize: 'clamp(12px, 3vw, 14px)',
                color: '#6b7280',
                margin: 0,
                lineHeight: '1.4',
                fontFamily: "'SF Pro', 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif"
              }}>
Compare Plans. Ask Questions. Choose Smarter
              </p>
            </div>
          </div>
          <div style={{
            display: 'flex',
            alignItems: 'center',
            gap: '12px'
          }}>


          </div>
        </div>

        {/* Progress Section - Wider than chatbot/forms */}
        <div style={{
          backgroundColor: 'white',
          borderRadius: '16px',
          border: '1px solid #e5e7eb',
          padding: '24px',
          marginBottom: '24px',
          boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
          marginLeft: '-20px',
          marginRight: '-20px',
          maxWidth: 'calc(100% + 40px)'
        }}>
          <div style={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            marginBottom: '20px',
            flexWrap: 'wrap',
            gap: '12px'
          }}>
            <h2 style={{
              fontSize: 'clamp(18px, 4vw, 20px)',
              fontWeight: '600',
              color: '#111827',
              margin: 0,
              fontFamily: 'sans-serif'
            }}>
              Enrollment Progress
            </h2>
            <span style={{
              fontSize: 'clamp(12px, 3vw, 14px)',
              color: '#6b7280',
              backgroundColor: '#f3f4f6',
              padding: '6px 12px',
              borderRadius: '20px',
              fontWeight: '500',
              fontFamily: 'sans-serif'
            }}>
              Step {currentStep + 1} of {steps.length}
            </span>
          </div>

          {/* Progress Bar */}
          <div style={{
            width: '100%',
            backgroundColor: '#e5e7eb',
            borderRadius: '12px',
            height: '8px',
            marginBottom: '24px'
          }}>
            <div
              style={{
                backgroundColor: '#000000',
                height: '8px',
                borderRadius: '12px',
                transition: 'all 0.4s ease',
                width: `${getProgressPercentage()}%`
              }}
            />
          </div>

          {/* Step Navigation - Two Lines Layout */}
          <div className="step-grid" style={{
            marginBottom: '8px'
          }}>
            {steps.map((step, index) => {
              const Icon = step.icon;
              const isActive = index === currentStep;
              const isCompleted = index < currentStep;

              // Gradient-based colors matching design system
              const getStepColors = () => {
                if (isCompleted) {
                  return {
                    backgroundColor: '#ddd6fe', // Light purple background (lighter than active)
                    color: '#6d28d9', // Darker purple text
                    border: 'none'
                  };
                } else if (isActive) {
                  return {
                    backgroundColor: '#ede9fe', // Light purple (matching icon scheme)
                    color: '#7c3aed', // Purple (matching gradient)
                    border: 'none'
                  };
                } else {
                  return {
                    backgroundColor: '#f3f4f6', // Light gray
                    color: '#6b7280', // Medium gray
                    border: 'none'
                  };
                }
              };

              const colors = getStepColors();

              return (
                <div
                  key={step.id}
                  style={{
                    display: 'inline-flex',
                    alignItems: 'center',
                    gap: '6px',
                    padding: '8px 16px',
                    borderRadius: '24px',
                    fontSize: '13px',
                    fontWeight: '450',
                    transition: 'all 0.2s ease',
                    backgroundColor: colors.backgroundColor,
                    color: colors.color,
                    border: colors.border,
                    fontFamily: 'sans-serif',
                    width: 'fit-content',
                    justifyContent: 'flex-start',
                    whiteSpace: 'nowrap',
                    minWidth: 'fit-content'
                  }}
                >
                  <Icon size={14} />
                  <span style={{
                    lineHeight: '1.2'
                  }}>
                    {step.title}
                  </span>
                  {isCompleted && (
                    <div style={{
                      display: 'inline-flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      width: '16px',
                      height: '16px',
                      backgroundColor: '#7c3aed', // Purple matching gradient
                      borderRadius: '50%',
                      marginLeft: '8px',
                      fontSize: '10px',
                      fontWeight: '600',
                      color: 'white',
                      lineHeight: '1'
                    }}>
                      ✓
                    </div>
                  )}
                </div>
              );
            })}
          </div>
        </div>

        {/* Main Content Area */}
        <div>
          <div ref={scrollRef} style={{ width: '100%' }}>




            {renderCurrentPage()}
          </div>

          {/* Inline Navigation - Only show where in /employe-enroled */}
          {(currentStep > 0 && currentStep < steps.length - 1) && (
            <div style={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              marginTop: '32px',
              paddingTop: '24px',
              borderTop: '1px solid #e5e7eb',
              gap: '16px'
            }}>
              <button
                onClick={handleBack}
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '8px',
                  padding: '12px 20px',
                  backgroundColor: 'white',
                  border: '1px solid #e5e7eb',
                  borderRadius: '8px',
                  cursor: 'pointer',
                  fontSize: '14px',
                  fontWeight: '500',
                  color: '#374151',
                  transition: 'all 0.2s ease',
                  fontFamily: 'sans-serif',
                  boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)'
                }}
                onMouseOver={(e) => {
                  e.currentTarget.style.backgroundColor = '#f9fafb';
                  e.currentTarget.style.borderColor = '#d1d5db';
                }}
                onMouseOut={(e) => {
                  e.currentTarget.style.backgroundColor = 'white';
                  e.currentTarget.style.borderColor = '#e5e7eb';
                }}
              >
                <ArrowLeft size={16} />
                Back
              </button>

              {/* Show Next button for all steps except summary and confirmation */}
              {(() => {
                const totalPlanSteps = planCategories.length;
                const summaryStepIndex = 3 + totalPlanSteps;
                const confirmationStepIndex = summaryStepIndex + 1;

                // Show Next button for welcome, dependents, and all plan steps, but not summary or confirmation
                return currentStep < summaryStepIndex;
              })() && (
                <button
                  onClick={handleNext}
                  style={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: '8px',
                    padding: '12px 24px',
                    backgroundColor: '#000000',
                    color: 'white',
                    borderRadius: '8px',
                    border: 'none',
                    cursor: 'pointer',
                    fontSize: '14px',
                    fontWeight: '600',
                    transition: 'all 0.2s ease',
                    fontFamily: 'sans-serif',
                    boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)'
                  }}
                  onMouseOver={(e) => {
                    e.currentTarget.style.backgroundColor = '#374151';
                    e.currentTarget.style.transform = 'translateY(-1px)';
                  }}
                  onMouseOut={(e) => {
                    e.currentTarget.style.backgroundColor = '#000000';
                    e.currentTarget.style.transform = 'translateY(0)';
                  }}
                >
                  Next
                  <ArrowRight size={16} />
                </button>
              )}
            </div>
          )}
        </div>
      </div>

      {/* Floating Chat Button */}
      <FloatingChatButton />
    </div>
  );
}
