/**
 * Utility functions for managing dynamic plan categories and themes
 */

// Theme configuration matching the requirements
export const PLAN_THEMES = [
  {
    primary: '#1E90FF', // Blue
    accent: '#E6F0FF',
    name: 'Blue'
  },
  {
    primary: '#32CD32', // Green
    accent: '#E6FFE6',
    name: '<PERSON>'
  },
  {
    primary: '#FF8C00', // Orange
    accent: '#FFF3E6',
    name: 'Orange'
  },
  {
    primary: '#800080', // Purple
    accent: '#F5E6FF',
    name: 'Purple'
  }
] as const;

// Plan category mapping based on backend coverage subtypes
export const PLAN_CATEGORY_MAP = {
  // Medical plans
  'medical': 'Medical',
  'health': 'Medical',
  'health insurance': 'Medical',
  
  // Dental plans
  'dental': 'Dental',
  'dental plan': 'Dental',
  
  // Vision plans
  'vision': 'Vision',
  'vision plan': 'Vision',
  
  // Life insurance plans
  'term life': 'Life Insurance',
  'life': 'Life Insurance',
  'life insurance': 'Life Insurance',
  'supplemental life insurance': 'Life Insurance',
  'whole life': 'Life Insurance',
  'group (employer) life': 'Life Insurance',
  
  // AD&D plans
  'accidental death & dismemberment (ad&d)': 'AD&D Insurance',
  'ad&d': 'AD&D Insurance',
  'add': 'AD&D Insurance',
  
  // Disability plans
  'short-term disability': 'Disability Insurance',
  'long-term disability': 'Disability Insurance',
  'std': 'Disability Insurance',
  'ltd': 'Disability Insurance',
  
  // Voluntary benefits
  'hospital indemnity': 'Voluntary Benefits',
  'accident insurance': 'Voluntary Benefits',
  'critical illness insurance': 'Voluntary Benefits',
  'cancer insurance': 'Voluntary Benefits'
} as const;

// Category display order (determines the sequence in enrollment flow)
export const CATEGORY_DISPLAY_ORDER = [
  'Medical',
  'Dental', 
  'Vision',
  'Life Insurance',
  'AD&D Insurance',
  'Disability Insurance',
  'Voluntary Benefits'
] as const;

/**
 * Extract and organize plan categories from plan assignments
 */
export function extractPlanCategories(planAssignments: Record<string, any[]>): string[] {
  const categories = new Set<string>();
  
  // Process each coverage subtype key
  Object.keys(planAssignments).forEach(key => {
    const normalizedKey = key.toLowerCase().trim();
    const category = PLAN_CATEGORY_MAP[normalizedKey as keyof typeof PLAN_CATEGORY_MAP];
    
    if (category && planAssignments[key].length > 0) {
      categories.add(category);
    }
  });
  
  // Return categories in display order
  return CATEGORY_DISPLAY_ORDER.filter(category => categories.has(category));
}

/**
 * Get theme index for a category (rotating through 4 themes)
 */
export function getThemeIndex(category: string, categories: string[]): number {
  const categoryIndex = categories.indexOf(category);
  return categoryIndex >= 0 ? categoryIndex % PLAN_THEMES.length : 0;
}

/**
 * Get plans for a specific category
 */
export function getPlansForCategory(
  category: string, 
  planAssignments: Record<string, any[]>
): any[] {
  const plans: any[] = [];
  
  // Find all keys that map to this category
  Object.entries(PLAN_CATEGORY_MAP).forEach(([key, mappedCategory]) => {
    if (mappedCategory === category && planAssignments[key]) {
      plans.push(...planAssignments[key]);
    }
  });
  
  return plans;
}

/**
 * Get the storage key for a category (used for localStorage)
 */
export function getCategoryStorageKey(category: string): string {
  const keyMap: Record<string, string> = {
    'Medical': 'medical',
    'Dental': 'dental',
    'Vision': 'vision',
    'Life Insurance': 'life',
    'AD&D Insurance': 'add',
    'Disability Insurance': 'disability',
    'Voluntary Benefits': 'voluntary'
  };
  
  return keyMap[category] || category.toLowerCase().replace(/\s+/g, '');
}

/**
 * Check if a category has been processed (selected or waived)
 */
export function isCategoryProcessed(category: string): boolean {
  const storageKey = getCategoryStorageKey(category);
  const selectedPlan = localStorage.getItem(`selected${category.replace(/\s+/g, '')}Plan`);
  const waived = localStorage.getItem(`${storageKey}Waived`);
  
  return selectedPlan !== null || waived === 'true';
}

/**
 * Get category icon based on category type
 */
export function getCategoryIcon(category: string): string {
  const iconMap: Record<string, string> = {
    'Medical': '🏥',
    'Dental': '🦷',
    'Vision': '👁️',
    'Life Insurance': '🛡️',
    'AD&D Insurance': '⚡',
    'Disability Insurance': '🤝',
    'Voluntary Benefits': '📋'
  };
  
  return iconMap[category] || '📄';
}

/**
 * Generate step configuration for dynamic enrollment flow
 */
export function generateDynamicSteps(planCategories: string[]): Array<{
  id: string;
  title: string;
  icon: string;
  category: string;
  completed: boolean;
}> {
  return planCategories.map((category, index) => ({
    id: `dynamic-${index}`,
    title: `${category} Plan`,
    icon: getCategoryIcon(category),
    category: category,
    completed: false
  }));
}

/**
 * Validate that all required categories have been processed
 */
export function validateEnrollmentCompletion(planCategories: string[]): {
  isComplete: boolean;
  missingCategories: string[];
} {
  const missingCategories = planCategories.filter(category => !isCategoryProcessed(category));
  
  return {
    isComplete: missingCategories.length === 0,
    missingCategories
  };
}

/**
 * Get user-friendly category name for display
 */
export function getCategoryDisplayName(category: string): string {
  const displayNames: Record<string, string> = {
    'Medical': 'Medical Insurance',
    'Dental': 'Dental Insurance',
    'Vision': 'Vision Insurance',
    'Life Insurance': 'Life Insurance',
    'AD&D Insurance': 'Accidental Death & Dismemberment',
    'Disability Insurance': 'Disability Insurance',
    'Voluntary Benefits': 'Voluntary Benefits'
  };
  
  return displayNames[category] || category;
}
