// Enhanced plan storage utilities for dynamic plan selection

export interface StoredPlanData {
  planId: string;
  planName: string;
  category: string;
  selectedTier: {
    tierName: string;
    employeeCost: number;
    totalCost: number;
    employerCost: number;
  };
  originalPlan: any; // Full plan assignment data
  selectionDate: string;
}

export interface StoredWaiveData {
  category: string;
  waiveReason: string;
  waiveDate: string;
}

// Storage keys
const STORAGE_KEYS = {
  SELECTED_PLANS: 'selectedPlansData',
  WAIVED_COVERAGES: 'waivedCoveragesData',
  PREFERRED_TIER: 'selectedCoverageTier',
  ENROLLMENT_SUMMARY: 'enrollmentSummarySnapshot'
} as const;

/**
 * Store a selected plan with its coverage tier
 */
export const storeSelectedPlan = (planData: StoredPlanData): void => {
  try {
    const existingPlans = getStoredPlans();
    
    // Remove any existing plan for this category
    const filteredPlans = existingPlans.filter(p => p.category !== planData.category);
    
    // Add the new plan
    const updatedPlans = [...filteredPlans, planData];
    
    localStorage.setItem(STORAGE_KEYS.SELECTED_PLANS, JSON.stringify(updatedPlans));
    
    // Also store in legacy format for backward compatibility
    const legacyKey = `selected${planData.category.replace(/\s+/g, '')}Plan`;
    localStorage.setItem(legacyKey, JSON.stringify({
      id: planData.planId,
      name: planData.planName,
      cost: planData.selectedTier.employeeCost,
      selectedTier: planData.selectedTier,
      originalPlan: planData.originalPlan
    }));
    
    console.log(`📦 Stored plan for ${planData.category}:`, planData);
  } catch (error) {
    console.error('Error storing selected plan:', error);
  }
};

/**
 * Get all stored plans
 */
export const getStoredPlans = (): StoredPlanData[] => {
  try {
    const stored = localStorage.getItem(STORAGE_KEYS.SELECTED_PLANS);
    return stored ? JSON.parse(stored) : [];
  } catch (error) {
    console.error('Error getting stored plans:', error);
    return [];
  }
};

/**
 * Get a specific plan by category
 */
export const getStoredPlan = (category: string): StoredPlanData | null => {
  const plans = getStoredPlans();
  return plans.find(p => p.category === category) || null;
};

/**
 * Store a waived coverage
 */
export const storeWaivedCoverage = (waiveData: StoredWaiveData): void => {
  try {
    const existingWaives = getStoredWaives();
    
    // Remove any existing waive for this category
    const filteredWaives = existingWaives.filter(w => w.category !== waiveData.category);
    
    // Add the new waive
    const updatedWaives = [...filteredWaives, waiveData];
    
    localStorage.setItem(STORAGE_KEYS.WAIVED_COVERAGES, JSON.stringify(updatedWaives));
    
    // Also store in legacy format for backward compatibility
    const legacyKey = `${waiveData.category.toLowerCase()}Waived`;
    const legacyReasonKey = `${waiveData.category.toLowerCase()}WaiveReason`;
    localStorage.setItem(legacyKey, 'true');
    localStorage.setItem(legacyReasonKey, waiveData.waiveReason);
    
    // Remove any selected plan for this category
    removeSelectedPlan(waiveData.category);
    
    console.log(`🚫 Stored waive for ${waiveData.category}:`, waiveData);
  } catch (error) {
    console.error('Error storing waived coverage:', error);
  }
};

/**
 * Get all stored waives
 */
export const getStoredWaives = (): StoredWaiveData[] => {
  try {
    const stored = localStorage.getItem(STORAGE_KEYS.WAIVED_COVERAGES);
    return stored ? JSON.parse(stored) : [];
  } catch (error) {
    console.error('Error getting stored waives:', error);
    return [];
  }
};

/**
 * Check if a category is waived
 */
export const isCategoryWaived = (category: string): boolean => {
  const waives = getStoredWaives();
  return waives.some(w => w.category === category);
};

/**
 * Remove a selected plan
 */
export const removeSelectedPlan = (category: string): void => {
  try {
    const existingPlans = getStoredPlans();
    const filteredPlans = existingPlans.filter(p => p.category !== category);
    localStorage.setItem(STORAGE_KEYS.SELECTED_PLANS, JSON.stringify(filteredPlans));
    
    // Also remove legacy format
    const legacyKey = `selected${category.replace(/\s+/g, '')}Plan`;
    localStorage.removeItem(legacyKey);
    
    console.log(`🗑️ Removed plan for ${category}`);
  } catch (error) {
    console.error('Error removing selected plan:', error);
  }
};

/**
 * Remove a waived coverage
 */
export const removeWaivedCoverage = (category: string): void => {
  try {
    const existingWaives = getStoredWaives();
    const filteredWaives = existingWaives.filter(w => w.category !== category);
    localStorage.setItem(STORAGE_KEYS.WAIVED_COVERAGES, JSON.stringify(filteredWaives));
    
    // Also remove legacy format
    const legacyKey = `${category.toLowerCase()}Waived`;
    const legacyReasonKey = `${category.toLowerCase()}WaiveReason`;
    localStorage.removeItem(legacyKey);
    localStorage.removeItem(legacyReasonKey);
    
    console.log(`🗑️ Removed waive for ${category}`);
  } catch (error) {
    console.error('Error removing waived coverage:', error);
  }
};

/**
 * Get preferred coverage tier
 */
export const getPreferredCoverageTier = (): string => {
  return localStorage.getItem(STORAGE_KEYS.PREFERRED_TIER) || 'Employee Only';
};

/**
 * Set preferred coverage tier
 */
export const setPreferredCoverageTier = (tier: string): void => {
  localStorage.setItem(STORAGE_KEYS.PREFERRED_TIER, tier);
  console.log('🎯 Set preferred coverage tier:', tier);
};

/**
 * Get enrollment summary for confirmation page
 */
export const getEnrollmentSummary = () => {
  const selectedPlans = getStoredPlans();
  const waivedCoverages = getStoredWaives();
  const preferredTier = getPreferredCoverageTier();

  return {
    selectedPlans,
    waivedCoverages,
    preferredTier,
    totalPlans: selectedPlans.length,
    totalWaives: waivedCoverages.length,
    categories: [...selectedPlans.map(p => p.category), ...waivedCoverages.map(w => w.category)]
  };
};

/**
 * Convert new storage format to legacy format for backward compatibility
 */
export const convertToLegacyFormat = () => {
  const selectedPlans = getStoredPlans();
  const waivedCoverages = getStoredWaives();

  // Create legacy format object
  const legacyData: any = {};

  // Convert selected plans
  selectedPlans.forEach(plan => {
    const categoryKey = plan.category.toLowerCase().replace(/\s+/g, '');
    legacyData[`${categoryKey}Plan`] = {
      id: plan.planId,
      name: plan.planName,
      cost: plan.selectedTier.employeeCost,
      selectedTier: plan.selectedTier,
      originalPlan: plan.originalPlan
    };
  });

  // Convert waived coverages
  waivedCoverages.forEach(waive => {
    const categoryKey = waive.category.toLowerCase().replace(/\s+/g, '');
    legacyData[`${categoryKey}Waived`] = true;
    legacyData[`${categoryKey}WaiveReason`] = waive.waiveReason;
  });

  return legacyData;
};

/**
 * Get dynamic enrollment summary compatible with existing summary/confirmation pages
 */
export const getDynamicEnrollmentSummary = () => {
  const selectedPlans = getStoredPlans();
  const waivedCoverages = getStoredWaives();
  const preferredTier = getPreferredCoverageTier();

  // Create dynamic summary object
  const summary: any = {
    selectedCoverageTier: preferredTier,
    enrollmentDate: new Date().toISOString(),
    dependents: [], // Will be populated from other sources
    selectedPlans: selectedPlans,
    waivedCoverages: waivedCoverages
  };

  // Add legacy format for backward compatibility
  selectedPlans.forEach(plan => {
    const categoryKey = plan.category.toLowerCase().replace(/\s+/g, '');
    summary[`${categoryKey}Plan`] = {
      id: plan.planId,
      name: plan.planName,
      planName: plan.planName,
      cost: plan.selectedTier.employeeCost,
      selectedTier: plan.selectedTier,
      originalPlan: plan.originalPlan
    };
  });

  waivedCoverages.forEach(waive => {
    const categoryKey = waive.category.toLowerCase().replace(/\s+/g, '');
    summary[`${categoryKey}Waived`] = true;
    summary[`${categoryKey}WaiveReason`] = waive.waiveReason;
  });

  return summary;
};

/**
 * Clear all enrollment data
 */
export const clearAllEnrollmentData = (): void => {
  try {
    // Clear new format
    localStorage.removeItem(STORAGE_KEYS.SELECTED_PLANS);
    localStorage.removeItem(STORAGE_KEYS.WAIVED_COVERAGES);
    localStorage.removeItem(STORAGE_KEYS.ENROLLMENT_SUMMARY);
    
    // Clear legacy format
    const categories = ['Dental', 'Vision', 'Life', 'ADD', 'Medical'];
    categories.forEach(category => {
      localStorage.removeItem(`selected${category}Plan`);
      localStorage.removeItem(`${category.toLowerCase()}Waived`);
      localStorage.removeItem(`${category.toLowerCase()}WaiveReason`);
    });
    
    console.log('🧹 Cleared all enrollment data');
  } catch (error) {
    console.error('Error clearing enrollment data:', error);
  }
};

/**
 * Create enrollment summary snapshot for confirmation page
 */
export const createEnrollmentSnapshot = (additionalData: any = {}) => {
  const summary = {
    ...getDynamicEnrollmentSummary(),
    ...additionalData,
    snapshotDate: new Date().toISOString()
  };

  localStorage.setItem(STORAGE_KEYS.ENROLLMENT_SUMMARY, JSON.stringify(summary));
  console.log('📸 Created enrollment snapshot:', summary);

  return summary;
};

/**
 * Debug function to log current storage state
 */
export const debugStorageState = () => {
  console.log('🔍 STORAGE DEBUG:');
  console.log('Selected Plans:', getStoredPlans());
  console.log('Waived Coverages:', getStoredWaives());
  console.log('Preferred Tier:', getPreferredCoverageTier());
  console.log('Dynamic Summary:', getDynamicEnrollmentSummary());

  // Also log legacy format for comparison
  console.log('Legacy Format Check:');
  const categories = ['Dental', 'Vision', 'Life', 'ADD'];
  categories.forEach(category => {
    const planKey = `selected${category}Plan`;
    const waiveKey = `${category.toLowerCase()}Waived`;
    console.log(`${category}:`, {
      plan: localStorage.getItem(planKey),
      waived: localStorage.getItem(waiveKey)
    });
  });
};
