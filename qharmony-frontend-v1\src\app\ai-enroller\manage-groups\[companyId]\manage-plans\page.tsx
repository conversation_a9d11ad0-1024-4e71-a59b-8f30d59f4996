'use client';

import React, { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import {
  HiOutlineCheck,
  HiOutlinePlus,
  HiOutlineClipboard,
  HiOutlinePencil,
  HiOutlineRefresh
} from 'react-icons/hi';
import './manage-plans.css';
import {
  getPlanAssignmentsByCompany,
  PlanAssignment
} from '../../services/planAssignmentApi';

const ManagePlansPage: React.FC = () => {
  const router = useRouter();
  const params = useParams();
  const companyId = params.companyId as string;

  const [selectAllChecked, setSelectAllChecked] = useState(false);
  const [planAssignments, setPlanAssignments] = useState<PlanAssignment[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Function to fetch plan details by ID
  const fetchPlanDetails = async (planId: string) => {
    const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8080';
    const getUserId = () => {
      const userId = localStorage.getItem('userid1') || localStorage.getItem('userId');
      if (!userId) {
        throw new Error('User ID not found. Please authenticate first.');
      }
      return userId;
    };

    try {
      const response = await fetch(`${API_BASE_URL}/api/pre-enrollment/plans/${planId}`, {
        headers: {
          'Content-Type': 'application/json',
          'user-id': getUserId(),
        },
      });

      if (response.ok) {
        const data = await response.json();
        return {
          planName: data.plan?.planName || 'Unknown Plan',
          planCode: data.plan?.planCode || 'N/A',
          planType: data.plan?.planType || 'N/A',
          coverageType: data.plan?.coverageType || 'Unknown',
          coverageSubTypes: data.plan?.coverageSubTypes || [],
          carrierName: data.carrier?.carrierName || 'Unknown Carrier'
        };
      }
    } catch (error) {
      console.error('Error fetching plan details for planId:', planId, error);
    }

    return {
      planName: 'Unknown Plan',
      planCode: 'N/A',
      planType: 'N/A',
      coverageType: 'Unknown',
      coverageSubTypes: [],
      carrierName: 'Unknown Carrier'
    };
  };

  const fetchPlanAssignments = async () => {
    try {
      setLoading(true);
      setError(null);

      const result = await getPlanAssignmentsByCompany(companyId, { includePlanData: true });

      if (result.success && result.data) {
        const assignments = result.data.assignments;
        console.log('Fetched plan assignments:', assignments);

        // Fetch plan details for each assignment
        const enrichedAssignments = await Promise.all(
          assignments.map(async (assignment) => {
            // planId should be a string reference to the plan
            const planId = typeof assignment.planId === 'string' ? assignment.planId : assignment.planId._id;
            const planDetails = await fetchPlanDetails(planId);

            return {
              ...assignment,
              planDetails
            };
          })
        );

        console.log('Enriched assignments with plan details:', enrichedAssignments);
        setPlanAssignments(enrichedAssignments);
      } else {
        setError(result.error || 'Failed to fetch plan assignments');
      }
    } catch (error) {
      console.error('Error fetching plan assignments:', error);
      setError('Failed to load plan assignments');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchPlanAssignments();
  }, [companyId]);

  // Add real-time updates - refresh every 30 seconds
  useEffect(() => {
    const interval = setInterval(() => {
      console.log('Auto-refreshing plan assignments...');
      fetchPlanAssignments();
    }, 30000); // 30 seconds

    return () => clearInterval(interval);
  }, [companyId]);

  // Add manual refresh function
  const handleRefresh = () => {
    console.log('Manual refresh triggered');
    fetchPlanAssignments();
  };

  const handleAddNewPlan = () => {
    router.push(`/ai-enroller/manage-groups/${companyId}/manage-plans/add-plan`);
  };

  const handleSelectAll = () => {
    setSelectAllChecked(!selectAllChecked);
  };

  const handleAddPlan = (category: string) => {
    router.push(`/ai-enroller/manage-groups/${companyId}/manage-plans/add-plan?category=${category}`);
  };

  const handleEditPlan = (planId: string) => {
    console.log('Edit plan:', planId);
  };

  // Helper function to get plan info from assignment with fetched plan details
  const getPlanInfo = (assignment: any) => {
    // Use the planDetails we fetched and attached to the assignment
    if (assignment.planDetails) {
      return {
        name: assignment.planDetails.planName,
        code: assignment.planDetails.planCode,
        carrier: assignment.planDetails.carrierName,
        type: assignment.planDetails.planType,
        coverageType: assignment.planDetails.coverageType,
        coverageSubTypes: assignment.planDetails.coverageSubTypes || []
      };
    }

    // Fallback if planDetails not available
    console.warn('Plan assignment missing planDetails:', assignment._id);
    return {
      name: 'Unknown Plan',
      code: 'N/A',
      carrier: 'Unknown Carrier',
      type: 'N/A',
      coverageType: 'Unknown',
      coverageSubTypes: []
    };
  };

  // Helper function to format date range
  const formatDateRange = (startDate: string, endDate: string) => {
    const start = new Date(startDate).toLocaleDateString();
    const end = new Date(endDate).toLocaleDateString();
    return `${start} - ${end}`;
  };

  // Helper function to get the display coverage type for plan cards
  const getDisplayCoverageType = (planInfo: any) => {
    // Show the primary coverage subtype (most specific)
    if (planInfo.coverageSubTypes && planInfo.coverageSubTypes.length > 0) {
      return planInfo.coverageSubTypes[0];
    }
    // Fallback to plan type if no subtypes
    return planInfo.type || 'N/A';
  };

  // Organize plan assignments by coverage subtype dynamically
  const organizeAssignmentsByType = () => {
    const categories: { [key: string]: any[] } = {};

    planAssignments.forEach(assignment => {
      const planInfo = getPlanInfo(assignment);
      console.log('Categorizing plan:', planInfo.name, 'Coverage SubTypes:', planInfo.coverageSubTypes);

      if (planInfo.coverageSubTypes && planInfo.coverageSubTypes.length > 0) {
        // Use the first coverage subtype as the primary category
        const primarySubtype = planInfo.coverageSubTypes[0];
        const categoryKey = primarySubtype.toLowerCase();

        // Initialize category if it doesn't exist
        if (!categories[categoryKey]) {
          categories[categoryKey] = [];
        }

        categories[categoryKey].push(assignment);
        console.log(`Plan "${planInfo.name}" assigned to category: ${primarySubtype}`);
      } else {
        // Fallback for plans without coverage subtypes
        const fallbackCategory = 'other';
        if (!categories[fallbackCategory]) {
          categories[fallbackCategory] = [];
        }
        categories[fallbackCategory].push(assignment);
        console.log(`Plan "${planInfo.name}" assigned to fallback category: ${fallbackCategory}`);
      }
    });

    console.log('Final categorization:', Object.keys(categories).reduce((acc, key) => {
      acc[key] = categories[key].length;
      return acc;
    }, {} as { [key: string]: number }));

    return categories;
  };

  const planCategories = organizeAssignmentsByType();

  // Helper function to get display name for category
  const getCategoryDisplayName = (categoryKey: string) => {
    const displayNames: { [key: string]: string } = {
      'medical': 'Medical Plans',
      'dental': 'Dental Plans',
      'vision': 'Vision Plans',
      'term life': 'Term Life Plans',
      'supplemental life insurance': 'Supplemental Life Plans',
      'short-term disability': 'Short-Term Disability Plans',
      'long-term disability': 'Long-Term Disability Plans',
      'whole life': 'Whole Life Plans',
      'group (employer) life': 'Group Life Plans',
      'other': 'Other Plans'
    };

    return displayNames[categoryKey] || `${categoryKey.charAt(0).toUpperCase() + categoryKey.slice(1)} Plans`;
  };

  // Helper function to get sorted categories in preferred order
  const getSortedCategories = () => {
    const preferredOrder = [
      'medical',
      'dental',
      'vision',
      'term life',
      'supplemental life insurance',
      'whole life',
      'group (employer) life',
      'short-term disability',
      'long-term disability',
      'other'
    ];

    const categories = Object.keys(planCategories);
    const sorted = [];

    // Add categories in preferred order
    for (const preferred of preferredOrder) {
      if (categories.includes(preferred)) {
        sorted.push(preferred);
      }
    }

    // Add any remaining categories not in preferred order
    for (const category of categories) {
      if (!sorted.includes(category)) {
        sorted.push(category);
      }
    }

    return sorted;
  };

  if (loading) {
    return (
      <div className="manage-plans-page">
        <div className="loading-container">
          <div className="loading-spinner"></div>
          <p>Loading plan assignments...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="manage-plans-page">
        <div className="error-container">
          <p>{error}</p>
          <button onClick={fetchPlanAssignments} className="retry-button">
            Try Again
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="manage-plans-page">
      {/* Breadcrumb Navigation */}
      <div className="breadcrumb-nav">
        <div className="breadcrumb-item completed">
          <HiOutlineCheck size={16} />
          <span>Home</span>
        </div>
        <div className="breadcrumb-item completed">
          <HiOutlineCheck size={16} />
          <span>Select Company</span>
        </div>
        <div className="breadcrumb-item active">
          <HiOutlineCheck size={16} />
          <span>View Plans</span>
        </div>
        <div className="breadcrumb-item">
          <span>Contributions</span>
        </div>
        <div className="breadcrumb-item">
          <span>Review</span>
        </div>
      </div>

      {/* Page Header */}
      <div className="page-header">
        <div className="header-content">
          <h1>Current Plan Overview</h1>
          <p className="company-subtitle">for TechCorp Inc.</p>
        </div>
        <div className="header-actions">
          <button className="refresh-btn" onClick={handleRefresh} title="Refresh plan assignments">
            <HiOutlineRefresh size={20} />
            Refresh
          </button>
          <button className="add-new-plan-btn" onClick={handleAddNewPlan}>
            <HiOutlinePlus size={20} />
            Add New Plan
          </button>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="summary-cards">
        <div className="summary-card purple">
          <div className="summary-icon">
            <HiOutlineClipboard size={24} />
          </div>
          <div className="summary-content">
            <div className="summary-label">Total Plans</div>
            <div className="summary-value">{planAssignments.length}</div>
            <div className="summary-subtitle">Active benefit plans</div>
          </div>
        </div>

        <div className="summary-card green">
          <div className="summary-icon">
            <HiOutlineCheck size={24} />
          </div>
          <div className="summary-content">
            <div className="summary-label">Plan Year</div>
            <div className="summary-value">2024</div>
            <div className="summary-subtitle">January 1 - December 31</div>
          </div>
        </div>

        <div className="summary-card orange">
          <div className="summary-icon">
            <span>$</span>
          </div>
          <div className="summary-content">
            <div className="summary-label">Est. Monthly Cost</div>
            <div className="summary-value">$3,032</div>
            <div className="summary-subtitle">Employer contribution</div>
          </div>
        </div>
      </div>

      {/* Select All Plans */}
      <div className="select-all-section">
        <label className="select-all-checkbox">
          <input
            type="checkbox"
            checked={selectAllChecked}
            onChange={handleSelectAll}
          />
          <span>Select All Plans (0 of {planAssignments.length} selected)</span>
        </label>
      </div>

      {/* Dynamic Plan Categories */}
      {getSortedCategories().map((categoryKey) => {
        const assignments = planCategories[categoryKey];
        return (
        <div key={categoryKey} className="plan-category">
          <div className="category-header">
            <h2>{getCategoryDisplayName(categoryKey)}</h2>
            <span className="plan-count">{assignments.length} plans configured</span>
            <button className="add-plan-btn" onClick={() => handleAddPlan(categoryKey)}>
              <HiOutlinePlus size={16} />
              Add Plan
            </button>
          </div>

          <div className="plans-list">
            {assignments.map((assignment) => {
              const planInfo = getPlanInfo(assignment);
              return (
                <div key={assignment._id} className="plan-card">
                  <div className="plan-card-header">
                    <input type="checkbox" className="plan-checkbox" />
                    <div className="plan-name-section">
                      <h3>{planInfo.name}</h3>
                      <div className="plan-carrier">{planInfo.carrier}</div>
                    </div>
                    <div className={`plan-status-badge ${assignment.status?.toLowerCase() || 'active'}`}>
                      {assignment.status || 'Active'}
                    </div>
                  </div>

                  <div className="plan-details-grid">
                    <div className="plan-detail">
                      <span className="detail-label">Type:</span>
                      <span className="detail-value">{getDisplayCoverageType(planInfo)}</span>
                    </div>
                    <div className="plan-detail">
                      <span className="detail-label">Plan Code:</span>
                      <span className="detail-value">{planInfo.code}</span>
                    </div>
                    <div className="plan-detail">
                      <span className="detail-label">Period:</span>
                      <span className="detail-value">{formatDateRange(assignment.planEffectiveDate, assignment.planEndDate)}</span>
                    </div>
                    <div className="plan-detail">
                      <span className="detail-label">Group #:</span>
                      <span className="detail-value">{assignment.groupNumber || 'N/A'}</span>
                    </div>
                  </div>

                  <div className="plan-actions">
                    <button className="edit-btn" onClick={() => handleEditPlan(assignment._id)}>
                      <HiOutlinePencil size={16} />
                      Edit
                    </button>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
        );
      })}
    </div>
  );
};

export default ManagePlansPage;
