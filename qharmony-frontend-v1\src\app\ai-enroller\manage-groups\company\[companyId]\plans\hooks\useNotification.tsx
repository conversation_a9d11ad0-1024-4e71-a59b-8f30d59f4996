import { useState } from 'react';

interface NotificationState {
  isOpen: boolean;
  type: 'success' | 'error' | 'warning' | 'info';
  title: string;
  message: string;
  confirmText?: string;
  onConfirm?: () => void;
}

export const useNotification = () => {
  const [notification, setNotification] = useState<NotificationState>({
    isOpen: false,
    type: 'info',
    title: '',
    message: '',
  });

  const showNotification = (
    type: 'success' | 'error' | 'warning' | 'info',
    title: string,
    message: string,
    confirmText?: string,
    onConfirm?: () => void
  ) => {
    setNotification({
      isOpen: true,
      type,
      title,
      message,
      confirmText,
      onConfirm,
    });
  };

  const hideNotification = () => {
    setNotification(prev => ({ ...prev, isOpen: false }));
  };

  // Convenience methods
  const showSuccess = (title: string, message: string, confirmText?: string, onConfirm?: () => void) => {
    showNotification('success', title, message, confirmText, onConfirm);
  };

  const showError = (title: string, message: string, confirmText?: string, onConfirm?: () => void) => {
    showNotification('error', title, message, confirmText, onConfirm);
  };

  const showWarning = (title: string, message: string, confirmText?: string, onConfirm?: () => void) => {
    showNotification('warning', title, message, confirmText, onConfirm);
  };

  const showInfo = (title: string, message: string, confirmText?: string, onConfirm?: () => void) => {
    showNotification('info', title, message, confirmText, onConfirm);
  };

  return {
    notification,
    showNotification,
    hideNotification,
    showSuccess,
    showError,
    showWarning,
    showInfo,
  };
};
