'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { HiOutlineCheckCircle, HiOutlinePlus, HiOutlinePencil, HiOutlineCalendar, HiOutlineHome, HiOutlineOfficeBuilding, HiOutlineClipboardList, HiOutlineCurrencyDollar, HiOutlineTrash, HiOutlineEye } from 'react-icons/hi';
import '../../../../../globals.css';
import PlanSelectionModal from './components/PlanSelectionModal';
import NotificationModal from './components/NotificationModal';
import { useNotification } from './hooks/useNotification';
import {
  getPlanAssignmentsByCompany,
  PlanAssignment,
  createPlanAssignment,
  clonePlanAssignment,
  updatePlanAssignment,
  deletePlanAssignment
} from '../../../services/planAssignmentApi';
import { getApiBaseUrl, getUserId } from '../../../../../../utils/env';
import EnrollmentHeader from '../../../../employee-enrol/components/EnrollmentHeader';

interface PlanAssignmentDisplay {
  _id: string;
  planName: string;
  planCode: string;
  carrier: string;
  type: 'Medical' | 'Dental' | 'Vision' | 'Ancillary';
  metalTier?: string;
  period: string;
  status: 'Active' | 'Draft' | 'Expired' | 'Inactive';
  assignmentId: string;
  planId: string;
  canEdit: boolean;
  canDelete: boolean;
  coverageTiers?: any[];
  coverageType?: string;
  enrollmentStartDate?: string;
  enrollmentEndDate?: string;
  planEffectiveDate?: string;
  planEndDate?: string;
}

interface Company {
  _id: string;
  companyName: string;
  employeeCount?: number;
}

function CompanyPlansPage() {
  const router = useRouter();
  const params = useParams();
  const companyId = params.companyId as string;

  const [company, setCompany] = useState<Company | null>(null);
  const [plans, setPlans] = useState<PlanAssignmentDisplay[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedPlans, setSelectedPlans] = useState<string[]>([]);
  const [editingPlan, setEditingPlan] = useState<PlanAssignmentDisplay | null>(null);
  const [showEditModal, setShowEditModal] = useState(false);
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [carrierFilter, setCarrierFilter] = useState<string>('all');
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [plansPerPage] = useState<number>(10);
  const [activeTab, setActiveTab] = useState<'active' | 'upcoming' | 'past'>('active');
  const [showPlanSelectionModal, setShowPlanSelectionModal] = useState(false);
  const [contributionType, setContributionType] = useState<'percentage' | 'fixed'>('percentage');
  const [coverageTiers, setCoverageTiers] = useState<any[]>([]);
  const [viewingPlan, setViewingPlan] = useState<PlanAssignmentDisplay | null>(null);
  const [showViewModal, setShowViewModal] = useState(false);

  // Notification hook
  const { notification, showNotification, showSuccess, showError, showWarning, hideNotification } = useNotification();

  // Calculate current plan year in 2025-2026 format
  const getCurrentPlanYear = () => {
    const currentDate = new Date();
    const currentYear = currentDate.getFullYear();
    const nextYear = currentYear + 1;
    return `${currentYear}-${nextYear}`;
  };

  // State to store plan assignment details for cost calculation
  const [planAssignmentDetails, setPlanAssignmentDetails] = useState<{[key: string]: any}>({});
  const [companyEmployeeCount, setCompanyEmployeeCount] = useState<number>(0);
  const [estimatedMonthlyCost, setEstimatedMonthlyCost] = useState<number>(0);

  // Calculate estimated monthly cost from plan assignments
  const calculateEstimatedMonthlyCost = () => {
    if (!companyEmployeeCount || Object.keys(planAssignmentDetails).length === 0) {
      return 0;
    }

    console.log('Calculating estimated monthly cost...');
    console.log('Company employee count:', companyEmployeeCount);
    console.log('Plan assignment details:', planAssignmentDetails);

    // Sum all employee-only coverage tier employer costs from all plan assignments
    let totalEmployeeOnlyEmployerCost = 0;

    Object.values(planAssignmentDetails).forEach((assignmentDetails: any) => {
      if (assignmentDetails && assignmentDetails.coverageTiers) {
        // Find the "Employee Only" tier
        const employeeOnlyTier = assignmentDetails.coverageTiers.find((tier: any) =>
          tier.tierName?.toLowerCase().includes('employee only') ||
          tier.tierName?.toLowerCase() === 'employee'
        );

        if (employeeOnlyTier) {
          console.log('Found employee-only tier:', employeeOnlyTier);
          // Add the employer cost (what employer pays) for this plan
          totalEmployeeOnlyEmployerCost += employeeOnlyTier.employerCost || 0;
        }
      }
    });

    console.log('Total employee-only employer cost per employee:', totalEmployeeOnlyEmployerCost);

    // Calculate total monthly employer cost: employee count × sum of all employee-only employer costs
    const totalMonthlyEmployerCost = companyEmployeeCount * totalEmployeeOnlyEmployerCost;

    console.log('Total monthly employer cost:', totalMonthlyEmployerCost);

    return Math.round(totalMonthlyEmployerCost);
  };

  // Get the estimated monthly cost to display
  const getEstimatedMonthlyCost = () => {
    return estimatedMonthlyCost;
  };

  // Fetch plan assignment details for cost calculation
  const fetchAllPlanAssignmentDetails = async () => {
    const details: {[key: string]: any} = {};

    for (const plan of plans) {
      try {
        const assignmentDetails = await fetchPlanAssignmentDetails(plan.assignmentId);
        if (assignmentDetails) {
          details[plan.assignmentId] = assignmentDetails;
        }
      } catch (error) {
        console.warn(`Failed to fetch details for assignment ${plan.assignmentId}:`, error);
      }
    }

    setPlanAssignmentDetails(details);
  };

  // Note: fetchPlanDetails function removed - now using enriched data from plan assignments API

  // Function to fetch company details with employee count (handles broker's own company)
  const fetchCompanyDetails = async (companyId: string): Promise<Company> => {
    const API_BASE_URL = getApiBaseUrl();
    const userId = getUserId();

    try {
      // First, check if this is the broker's own company using /employee/company-details
      const ownCompanyResponse = await fetch(`${API_BASE_URL}/employee/company-details`, {
        headers: {
          'Content-Type': 'application/json',
          'user-id': userId,
        },
      });

      if (ownCompanyResponse.ok) {
        const ownCompanyData = await ownCompanyResponse.json();
        if (ownCompanyData.company &&
            ownCompanyData.company.isBrokerage &&
            ownCompanyData.company._id === companyId) {
          console.log('Found broker\'s own company with employee count:', ownCompanyData.company.companySize);
          return {
            _id: ownCompanyData.company._id,
            companyName: ownCompanyData.company.name || 'Unknown Company',
            employeeCount: ownCompanyData.company.companySize || 250
          };
        }
      }

      // If not broker's own company, try to get from /admin/all-companies (client companies)
      const companiesResponse = await fetch(`${API_BASE_URL}/admin/all-companies`, {
        headers: {
          'Content-Type': 'application/json',
          'user-id': userId,
        },
      });

      if (companiesResponse.ok) {
        const companiesData = await companiesResponse.json();
        const targetCompany = companiesData.companies?.find((company: any) => company._id === companyId);

        if (targetCompany) {
          console.log('Found client company with employee count:', targetCompany.companySize);
          return {
            _id: targetCompany._id,
            companyName: targetCompany.name || 'Unknown Company',
            employeeCount: targetCompany.companySize || 250
          };
        }
      }

      // Fallback: try the pre-enrollment companies API
      const response = await fetch(`${API_BASE_URL}/api/pre-enrollment/companies/${companyId}`, {
        headers: {
          'Content-Type': 'application/json',
          'user-id': userId,
        },
      });

      if (response.ok) {
        const data = await response.json();
        return {
          _id: data.company?._id || companyId,
          companyName: data.company?.companyName || 'Unknown Company',
          employeeCount: data.company?.companySize || data.company?.employeeCount || 250
        };
      }
    } catch (error) {
      console.error('Error fetching company details:', error);
    }

    // Final fallback
    return {
      _id: companyId,
      companyName: 'Unknown Company',
      employeeCount: 250
    };
  };

  const fetchCompanyAndPlans = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      // Fetch actual company details with real employee count
      const companyDetails = await fetchCompanyDetails(companyId);
      setCompanyEmployeeCount(companyDetails.employeeCount || 250);
      console.log('Company employee count set to:', companyDetails.employeeCount || 250);

      // Fetch plan assignments for the company with enriched data
      const result = await getPlanAssignmentsByCompany(companyId, {
        includePlanData: true
      });

      if (result.success && result.data) {
        const planAssignments = result.data.assignments;
        console.log('Fetched plan assignments:', planAssignments);

        // Handle special case where broker has no existing assignments but can create new ones
        if (result.data.accessDeniedToExisting && result.data.canCreateAssignments) {
          console.log('🔧 Broker can create new plan assignments for this company');
          // Show empty state but allow plan creation
          setCompany(companyDetails);
          setPlans([]);
          return;
        }

        // Transform plan assignments to display format using enriched data
        const displayPlans: PlanAssignmentDisplay[] = await Promise.all(
          planAssignments.map(async (assignment: PlanAssignment) => {
            console.log('Processing assignment with enriched data:', assignment._id, assignment);

            // Use enriched plan data from the API response
            const planData = assignment.planData || assignment.plan;
            const carrierData = assignment.carrierData;

            // Get planId as string
            const planIdString = typeof assignment.planId === 'string' ? assignment.planId : assignment.planId?._id || '';

            // Use enriched data instead of making additional API calls
            const planName = planData?.planName || 'Unknown Plan';
            const planCode = planData?.planCode || 'N/A';
            const carrierName = carrierData?.carrierName || 'Unknown Carrier';
            const coverageType = planData?.coverageType || '';
            const coverageSubTypes = planData?.coverageSubTypes || [];
            const metalTier = planData?.metalTier || '';

            console.log('Using enriched plan data:', { planName, planCode, carrierName, coverageType, coverageSubTypes, metalTier });

            // Determine display type based on coverage subtypes or coverage type
            let displayType: 'Medical' | 'Dental' | 'Vision' | 'Ancillary' = 'Medical';
            if (coverageSubTypes && coverageSubTypes.length > 0) {
              const primarySubtype = coverageSubTypes[0].toLowerCase();
              if (primarySubtype.includes('dental')) {
                displayType = 'Dental';
              } else if (primarySubtype.includes('vision')) {
                displayType = 'Vision';
              } else if (primarySubtype.includes('medical') || primarySubtype.includes('health')) {
                displayType = 'Medical';
              } else {
                displayType = 'Ancillary';
              }
            } else if (coverageType) {
              const coverageTypeLower = coverageType.toLowerCase();
              if (coverageTypeLower.includes('dental')) {
                displayType = 'Dental';
              } else if (coverageTypeLower.includes('vision')) {
                displayType = 'Vision';
              } else if (coverageTypeLower.includes('medical') || coverageTypeLower.includes('health')) {
                displayType = 'Medical';
              } else {
                displayType = 'Ancillary';
              }
            }

            // Since we have access to the assignment data, assume permissions based on status
            // Active assignments can typically be edited and deleted
            // This avoids the individual API calls that are failing due to middleware differences
            const canEdit = assignment.status === 'Active' || assignment.status === 'Draft';
            const canDelete = assignment.status === 'Active' || assignment.status === 'Draft';

            return {
              _id: assignment._id,
              planName: planName,
              planCode: planCode,
              carrier: carrierName,
              type: displayType,
              metalTier: metalTier,
              period: `${new Date(assignment.planEffectiveDate).toLocaleDateString('en-US', { year: 'numeric', month: 'short', day: 'numeric' })} - ${new Date(assignment.planEndDate).toLocaleDateString('en-US', { year: 'numeric', month: 'short', day: 'numeric' })}`,
              status: assignment.status as 'Active' | 'Draft' | 'Expired' | 'Inactive',
              assignmentId: assignment._id,
              planId: planIdString,
              canEdit,
              canDelete,
              // Add missing properties for the list display
              coverageType: coverageType,
              coverageTiers: assignment.coverageTiers || [],
              enrollmentStartDate: assignment.enrollmentStartDate,
              enrollmentEndDate: assignment.enrollmentEndDate,
              planEffectiveDate: assignment.planEffectiveDate,
              planEndDate: assignment.planEndDate
            };
          })
        );

        console.log('Final display plans:', displayPlans);
        setCompany(companyDetails);
        setPlans(displayPlans);
      } else {
        setError(result.error || 'Failed to fetch plan assignments');
        setPlans([]);
      }
    } catch (error) {
      console.error('Error fetching data:', error);
      setError('Failed to fetch plan assignments');
      setPlans([]);
    } finally {
      setLoading(false);
    }
  }, [companyId]);

  useEffect(() => {
    fetchCompanyAndPlans();
  }, [fetchCompanyAndPlans]);

  // Fetch plan assignment details when plans are loaded
  useEffect(() => {
    if (plans.length > 0) {
      fetchAllPlanAssignmentDetails();
    }
  }, [plans]);

  // Calculate estimated monthly cost when plan assignment details and employee count are available
  useEffect(() => {
    if (Object.keys(planAssignmentDetails).length > 0 && companyEmployeeCount > 0) {
      const calculatedCost = calculateEstimatedMonthlyCost();
      setEstimatedMonthlyCost(calculatedCost);
    }
  }, [planAssignmentDetails, companyEmployeeCount]);

  const handleSelectAll = () => {
    // Check if all filtered plans are selected (across all pages)
    const filteredPlanIds = filteredPlans.map(plan => plan._id);
    const allFilteredSelected = filteredPlanIds.every(id => selectedPlans.includes(id));

    if (allFilteredSelected) {
      // Deselect all filtered plans
      setSelectedPlans(prev => prev.filter(id => !filteredPlanIds.includes(id)));
    } else {
      // Select all filtered plans (add to existing selection)
      setSelectedPlans(prev => [...new Set([...prev, ...filteredPlanIds])]);
    }
  };

  const handleSelectCurrentPage = () => {
    // Check if all plans on current page are selected
    const currentPagePlanIds = paginatedPlans.map(plan => plan._id);
    const allCurrentPageSelected = currentPagePlanIds.every(id => selectedPlans.includes(id));

    if (allCurrentPageSelected) {
      // Deselect all plans on current page
      setSelectedPlans(prev => prev.filter(id => !currentPagePlanIds.includes(id)));
    } else {
      // Select all plans on current page
      setSelectedPlans(prev => [...new Set([...prev, ...currentPagePlanIds])]);
    }
  };

  const handlePlanSelect = (planId: string) => {
    setSelectedPlans(prev => 
      prev.includes(planId) 
        ? prev.filter(id => id !== planId)
        : [...prev, planId]
    );
  };

  const handleAddNewPlan = () => {
    setShowPlanSelectionModal(true);
  };

  const handlePlanSelected = async (selectedPlan: any) => {
    // Workflow 2: Create Fresh Plan Assignment from Template
    console.log('🔄 Workflow 2: Creating fresh plan assignment from template:', selectedPlan);

    try {
      // Create plan assignment with all required fields
      const currentYear = new Date().getFullYear();
      const nextYear = currentYear + 1;

      // Set dates for next year enrollment
      const planEffectiveDate = `${nextYear}-01-01`;
      const planEndDate = `${nextYear}-12-31`;
      const enrollmentStartDate = `${currentYear}-11-01`; // Current year November
      const enrollmentEndDate = `${currentYear}-11-30`; // Current year November end

      const assignmentData = {
        planId: selectedPlan._id,
        companyId: companyId,
        // Required fields with defaults
        rateStructure: 'Composite', // Default rate structure
        coverageTiers: [
          {
            tierName: 'Employee Only',
            totalCost: 500,
            employerCost: 400,
            employeeCost: 100
          },
          {
            tierName: 'Employee + Spouse',
            totalCost: 1000,
            employerCost: 800,
            employeeCost: 200
          },
          {
            tierName: 'Employee + Child(ren)',
            totalCost: 800,
            employerCost: 640,
            employeeCost: 160
          },
          {
            tierName: 'Family',
            totalCost: 1500,
            employerCost: 1200,
            employeeCost: 300
          }
        ],
        planEffectiveDate,
        planEndDate,
        enrollmentStartDate,
        enrollmentEndDate,
        // Optional fields with defaults
        groupNumber: `GRP-${companyId}-${selectedPlan._id.slice(-6)}`,
        waitingPeriod: { enabled: false, days: 0, rule: 'Immediate' },
        enrollmentType: 'Active',
        employerContribution: { contributionType: 'Percentage', contributionAmount: 80 },
        employeeContribution: { contributionType: 'Percentage', contributionAmount: 20 },
        ageBandedRates: [],
        salaryBasedRates: [],
        planCustomizations: {},
        status: 'Draft' // Start as Draft for new assignments
      };

      const result = await createPlanAssignment(assignmentData);

      if (result.success && result.data) {
        console.log('✅ Fresh plan assignment created successfully:', result.data);
        // Immediately refresh the plans list to show the new assignment
        await fetchCompanyAndPlans();
      } else {
        console.error('❌ Failed to create plan assignment:', result.error);
        showError('Assignment Failed', 'Failed to assign plan to company: ' + (result.error || 'Unknown error'));
      }
    } catch (error) {
      console.error('Error creating plan assignment:', error);
      showError('Assignment Error', 'Failed to assign plan to company. Please try again.');
    }
  };

  const handleModalClose = () => {
    // No need to refresh here since we refresh immediately after each plan selection
    setShowPlanSelectionModal(false);
    // Reset edit state
    setEditingPlan(null);
    setCoverageTiers([]);
  };

  const handlePlanCreated = async (newPlan: any) => {
    // Workflow 2: Create Fresh Plan Assignment for newly created plan
    console.log('🔄 Workflow 2: Creating fresh plan assignment for new plan:', newPlan);

    try {
      // Create plan assignment with all required fields
      const currentYear = new Date().getFullYear();
      const nextYear = currentYear + 1;

      // Set dates for next year enrollment
      const planEffectiveDate = `${nextYear}-01-01`;
      const planEndDate = `${nextYear}-12-31`;
      const enrollmentStartDate = `${currentYear}-11-01`; // Current year November
      const enrollmentEndDate = `${currentYear}-11-30`; // Current year November end

      const assignmentData = {
        planId: newPlan._id,
        companyId: companyId,
        // Required fields with defaults
        rateStructure: 'Composite', // Default rate structure
        coverageTiers: [
          {
            tierName: 'Employee Only',
            totalCost: 500,
            employerCost: 400,
            employeeCost: 100
          },
          {
            tierName: 'Employee + Spouse',
            totalCost: 1000,
            employerCost: 800,
            employeeCost: 200
          },
          {
            tierName: 'Employee + Child(ren)',
            totalCost: 800,
            employerCost: 640,
            employeeCost: 160
          },
          {
            tierName: 'Family',
            totalCost: 1500,
            employerCost: 1200,
            employeeCost: 300
          }
        ],
        planEffectiveDate,
        planEndDate,
        enrollmentStartDate,
        enrollmentEndDate,
        // Optional fields with defaults
        groupNumber: `GRP-${companyId}-${newPlan._id.slice(-6)}`,
        waitingPeriod: { enabled: false, days: 0, rule: 'Immediate' },
        enrollmentType: 'Active',
        employerContribution: { contributionType: 'Percentage', contributionAmount: 80 },
        employeeContribution: { contributionType: 'Percentage', contributionAmount: 20 },
        ageBandedRates: [],
        salaryBasedRates: [],
        planCustomizations: {},
        status: 'Draft' // Start as Draft for new assignments
      };

      const result = await createPlanAssignment(assignmentData);

      if (result.success && result.data) {
        // Refresh the plans list to include the new assignment
        await fetchCompanyAndPlans();
        showSuccess('Plan Created!', 'Plan created and assigned to company successfully!');
      } else {
        console.error('❌ Failed to create plan assignment:', result.error);
        showError('Assignment Failed', 'Plan created but failed to assign to company: ' + (result.error || 'Unknown error'));
      }
    } catch (error) {
      console.error('Error creating plan assignment:', error);
      showError('Assignment Error', 'Plan created but failed to assign to company. Please try again.');
    }

    setShowPlanSelectionModal(false);
  };

  // Function to fetch plan assignment details including coverage tiers
  const fetchPlanAssignmentDetails = async (assignmentId: string) => {
    const API_BASE_URL = getApiBaseUrl();
    const userId = getUserId();

    try {
      console.log('Fetching plan assignment details for ID:', assignmentId);
      const response = await fetch(`${API_BASE_URL}/api/pre-enrollment/plan-assignments/${assignmentId}`, {
        headers: {
          'Content-Type': 'application/json',
          'user-id': userId,
        },
      });

      console.log('Plan assignment fetch response status:', response.status);

      if (response.ok) {
        const data = await response.json();
        console.log('Plan assignment fetch response data:', data);
        console.log('Assignment object:', data.assignment);

        // Handle Mongoose document structure - data might be in _doc
        const assignment = data.assignment._doc || data.assignment;
        console.log('Processed assignment:', assignment);
        console.log('Coverage tiers in assignment:', assignment?.coverageTiers);

        return assignment;
      } else {
        console.error('Failed to fetch plan assignment details. Status:', response.status);
        const errorText = await response.text();
        console.error('Error response:', errorText);
      }
    } catch (error) {
      console.error('Error fetching plan assignment details:', error);
    }
    return null;
  };





  const handleCloseModal = () => {
    setShowEditModal(false);
    setEditingPlan(null);
  };

  const updateTier = (id: string, field: string, value: number) => {
    setCoverageTiers(prev => prev.map(tier => {
      if (tier.id === id) {
        const updated = { ...tier, [field]: value };

        // Recalculate employer/employee pays based on contribution type
        if (contributionType === 'percentage') {
          // In percentage mode: employer % is editable, employer pays is calculated
          if (field === 'premium' || field === 'employeePercent') {
            updated.employerPays = (updated.premium * updated.employeePercent) / 100;
            updated.employeePays = updated.premium - updated.employerPays;
          }
        } else if (contributionType === 'fixed') {
          // In fixed mode: employer pays is editable, employer % is calculated
          if (field === 'premium' || field === 'employerPays') {
            updated.employeePays = updated.premium - updated.employerPays;
            updated.employeePercent = updated.premium > 0 ? (updated.employerPays / updated.premium) * 100 : 0;
          }
        }

        return updated;
      }
      return tier;
    }));
  };

  const deleteTier = (id: string) => {
    setCoverageTiers(prev => prev.filter(tier => tier.id !== id));
  };

  const addNewTier = () => {
    const newId = (coverageTiers.length + 1).toString();
    const newTier = {
      id: newId,
      tier: 'New Coverage Tier',
      premium: 500.00,
      employeePercent: 80,
      employerPays: 400.00,
      employeePays: 100.00
    };
    setCoverageTiers(prev => [...prev, newTier]);
  };

  const handleContinueWithSelected = () => {
    if (selectedPlans.length === 0) {
      alert('Please select at least one plan to continue.');
      return;
    }

    // Navigate directly to set dates page (skip enrollment-dates step)
    const selectedPlanIds = selectedPlans.join(',');
    router.push(`/ai-enroller/manage-groups/company/${companyId}/set-dates?plans=${selectedPlanIds}`);
  };

  // Handle renew plan assignment (clone for next year)
  const handleRenewPlan = async (plan: PlanAssignmentDisplay) => {
    try {
      const currentYear = new Date().getFullYear();
      const nextYear = currentYear + 1;

      // Clone the plan assignment for next year
      // Include all required fields that the backend deletes but doesn't recalculate
      const planEndDate = `${nextYear}-12-31`;
      const cloneData = {
        planEffectiveDate: `${nextYear}-01-01`,
        planEndDate: planEndDate,
        enrollmentStartDate: `${currentYear}-11-01`,
        enrollmentEndDate: `${currentYear}-11-30`,
        status: 'Draft',
        // Include required fields that backend deletes but doesn't recalculate
        assignmentYear: nextYear,
        assignmentExpiry: planEndDate,
        // Ensure contribution fields are preserved (backend might not copy them properly)
        employerContribution: {
          contributionType: 'Percentage',
          contributionAmount: 80
        },
        employeeContribution: {
          contributionType: 'Percentage',
          contributionAmount: 20
        },
        // Ensure other required fields are preserved
        eligibleEmployeeClasses: ['Full-Time'],
        enrollmentType: 'Active'
      };

      const result = await clonePlanAssignment(plan.assignmentId, cloneData);

      if (result.success) {
        showSuccess(
          'Plan Renewed Successfully',
          `Plan has been renewed for ${nextYear}. The new assignment is in Draft status.`
        );
        await fetchCompanyAndPlans(); // Refresh the list
      } else {
        showError(
          'Renewal Failed',
          result.error || 'Failed to renew plan assignment'
        );
      }
    } catch (error) {
      console.error('Error renewing plan:', error);
      showError(
        'Renewal Error',
        'An unexpected error occurred while renewing the plan'
      );
    }
  };

  // Handle edit plan assignment (reuse existing modal)
  const handleEditPlanAssignment = async (plan: PlanAssignmentDisplay) => {
    // Skip individual API permission check since it's failing due to middleware differences
    // Use the canEdit flag we already determined from the assignment status
    if (!plan.canEdit) {
      showWarning(
        'Cannot Edit Plan',
        'This plan cannot be edited due to its current status or active enrollments.'
      );
      return;
    }

    console.log('🔧 Starting edit mode for plan:', plan);

    try {
      // Fetch complete assignment details including coverage tiers
      const fullAssignmentDetails = await fetchPlanAssignmentDetails(plan.assignmentId);

      if (!fullAssignmentDetails) {
        showError('Error', 'Failed to load plan assignment details for editing.');
        return;
      }

      console.log('🔧 Full assignment details for editing:', fullAssignmentDetails);

      // Set up coverage tiers from the fetched assignment details
      let formattedTiers: any[] = [];
      if (fullAssignmentDetails.coverageTiers && fullAssignmentDetails.coverageTiers.length > 0) {
        formattedTiers = fullAssignmentDetails.coverageTiers.map((tier: any, index: number) => {
          const employerPercent = tier.employerContributionPercent || 80;
          return {
            id: (index + 1).toString(),
            tier: tier.tierName || `Tier ${index + 1}`,
            premium: tier.totalCost || 0,
            employerPercent: employerPercent,
            employerPays: tier.employerCost || 0,
            employeePays: tier.employeeCost || 0
          };
        });
      }

      // Create enhanced plan object with full details
      const enhancedPlan = {
        ...plan,
        ...fullAssignmentDetails,
        assignmentId: plan.assignmentId,
        _id: plan.assignmentId // Use assignment ID for updates
      };

      console.log('🔧 Enhanced plan for editing:', enhancedPlan);
      console.log('🔧 Formatted tiers for editing:', formattedTiers);

      // Set editing plan and show modal in edit mode
      setEditingPlan(enhancedPlan);
      setCoverageTiers(formattedTiers);
      setShowPlanSelectionModal(true); // Use the same modal but in edit mode
    } catch (error) {
      console.error('Error preparing edit mode:', error);
      showError('Error', 'Failed to prepare plan assignment for editing.');
    }
  };

  // Handle delete plan assignment
  const handleDeletePlan = async (plan: PlanAssignmentDisplay) => {
    try {
      // Skip individual API permission check since it's failing due to middleware differences
      // Use the canDelete flag we already determined from the assignment status
      if (!plan.canDelete) {
        showWarning(
          'Cannot Delete Plan',
          'This plan cannot be deleted due to its current status or active enrollments.'
        );
        return;
      }

      // Show confirmation dialog
      showNotification(
        'warning',
        'Confirm Deletion',
        `Are you sure you want to delete the plan assignment for "${plan.planName}"? This action cannot be undone.`,
        'Delete',
        async () => {
          const result = await deletePlanAssignment(plan.assignmentId);

          if (result.success) {
            showSuccess(
              'Plan Deleted',
              'Plan assignment has been successfully deleted.'
            );
            await fetchCompanyAndPlans(); // Refresh the list
          } else {
            showError(
              'Deletion Failed',
              result.error || 'Failed to delete plan assignment'
            );
          }
        }
      );
    } catch (error) {
      console.error('Error deleting plan:', error);
      showError(
        'Deletion Error',
        'An unexpected error occurred while deleting the plan'
      );
    }
  };

  // Handle view plan assignment details
  const handleViewPlanAssignment = (plan: PlanAssignmentDisplay) => {
    console.log('🔍 Viewing plan assignment:', plan);
    setViewingPlan(plan);
    setShowViewModal(true);
  };

  // Helper function to categorize plans by date
  const categorizePlansByDate = (planList: typeof plans) => {
    const today = new Date();

    return planList.reduce((acc, plan) => {
      const startDate = new Date(plan.planEffectiveDate || plan.enrollmentStartDate || new Date());
      const endDate = new Date(plan.planEndDate || plan.enrollmentEndDate || new Date());

      if (startDate <= today && today <= endDate) {
        acc.active.push(plan);
      } else if (startDate > today) {
        acc.upcoming.push(plan);
      } else if (endDate < today) {
        acc.past.push(plan);
      }

      return acc;
    }, { active: [] as typeof plans, upcoming: [] as typeof plans, past: [] as typeof plans });
  };

  // Get categorized plans for stats
  const categorizedPlans = categorizePlansByDate(plans);

  // Calculate cost for current tab's plans
  const getCurrentTabCost = () => {
    const currentPlans = activeTab === 'active' ? categorizedPlans.active :
                        activeTab === 'upcoming' ? categorizedPlans.upcoming :
                        categorizedPlans.past;

    return currentPlans.reduce((total, plan) => {
      const planCost = plan.coverageTiers?.[0]?.employeeCost || 0;
      return total + planCost;
    }, 0);
  };

  const currentTabCost = getCurrentTabCost();

  // Filter plans based on selected filters and active tab
  const filteredPlans = plans.filter(plan => {
    const statusMatch = statusFilter === 'all' || plan.status === statusFilter;
    const carrierMatch = carrierFilter === 'all' || plan.carrier === carrierFilter;

    // Date-based filtering for tabs
    const today = new Date();
    const startDate = new Date(plan.planEffectiveDate || plan.enrollmentStartDate || new Date());
    const endDate = new Date(plan.planEndDate || plan.enrollmentEndDate || new Date());

    let dateMatch = true;
    if (activeTab === 'active') {
      dateMatch = startDate <= today && today <= endDate;
    } else if (activeTab === 'upcoming') {
      dateMatch = startDate > today;
    } else if (activeTab === 'past') {
      dateMatch = endDate < today;
    }

    return statusMatch && carrierMatch && dateMatch;
  });

  // Pagination logic
  const totalPages = Math.ceil(filteredPlans.length / plansPerPage);
  const startIndex = (currentPage - 1) * plansPerPage;
  const endIndex = startIndex + plansPerPage;
  const paginatedPlans = filteredPlans.slice(startIndex, endIndex);

  // Reset to page 1 when filters change
  React.useEffect(() => {
    setCurrentPage(1);
  }, [statusFilter, carrierFilter, activeTab]);

  const groupedPlans = paginatedPlans.reduce((acc, plan) => {
    if (!acc[plan.type]) {
      acc[plan.type] = [];
    }
    acc[plan.type].push(plan);
    return acc;
  }, {} as Record<string, PlanAssignmentDisplay[]>);

  // Function to get sorted category order
  const getSortedCategories = () => {
    const preferredOrder = ['Medical', 'Dental', 'Vision', 'Ancillary'];
    const availableCategories = Object.keys(groupedPlans);

    // First add categories in preferred order
    const sortedCategories = [];
    for (const preferred of preferredOrder) {
      if (availableCategories.includes(preferred)) {
        sortedCategories.push(preferred);
      }
    }

    // Then add any remaining categories not in preferred order
    for (const category of availableCategories) {
      if (!sortedCategories.includes(category)) {
        sortedCategories.push(category);
      }
    }

    return sortedCategories;
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-black mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading plan assignments...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded-xl">
            <h3 className="font-bold">Error Loading Plans</h3>
            <p>{error}</p>
            <button
              onClick={fetchCompanyAndPlans}
              className="mt-2 bg-red-600 text-white px-4 py-2 rounded-xl hover:bg-red-700"
            >
              Try Again
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <>
      <EnrollmentHeader />
      <div className="min-h-screen bg-white">
      {/* Enhanced Header */}
      <div className="bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex items-center justify-between">
            <button
              onClick={() => router.push('/ai-enroller/manage-groups')}
              className="inline-flex items-center gap-2 text-sm font-medium text-gray-600 hover:text-gray-900 transition-colors group"
            >
              <svg className="w-4 h-4 group-hover:-translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
              </svg>
              Back to Groups
            </button>
            <button
              onClick={handleAddNewPlan}
              className="inline-flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-blue-600 to-purple-600 text-white text-sm font-medium rounded-lg hover:opacity-90 transition-all shadow-sm hover:shadow-md"
            >
              <HiOutlinePlus className="w-4 h-4" />
              Add Plan
            </button>
          </div>
        </div>
      </div>

      {/* Enhanced Company Info & Stats */}
      <div className="bg-white ">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          {/* Company Header */}
          <div className="flex items-center gap-6 mb-8">
            <div className="relative">
              <div className="w-16 h-16 bg-gradient-to-br from-blue-600 to-purple-600 rounded-xl flex items-center justify-center shadow-lg">
                <HiOutlineOfficeBuilding className="w-8 h-8 text-white" />
              </div>
              <div className="absolute -bottom-1 -right-1 w-5 h-5 bg-green-500 rounded-full border-2 border-white flex items-center justify-center">
                <HiOutlineCheckCircle className="w-3 h-3 text-white" />
              </div>
            </div>
            <div className="flex-1">
              <div className="flex items-center gap-3 mb-2">
                <h1 className="text-2xl font-bold text-gray-900">{company?.companyName || 'Loading...'}</h1>
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                  Active
                </span>
              </div>
              <div className="flex items-center gap-6 text-sm text-gray-600">
                <div className="flex items-center gap-1">
                  <HiOutlineOfficeBuilding className="w-4 h-4" />
                  <span>{company?.employeeCount || 0} employees</span>
                </div>
                <div className="flex items-center gap-1">
                  <HiOutlineCalendar className="w-4 h-4" />
                  <span>Plan Year {getCurrentPlanYear()}</span>
                </div>
                <div className="flex items-center gap-1">
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                  </svg>
                  <span>San Francisco, CA</span>
                </div>
              </div>
            </div>
          </div>

          {/* Enhanced Summary Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div className="bg-white rounded-xl p-6 border border-green-400 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-green-600">
                    {activeTab === 'active' ? 'Active Plans' :
                     activeTab === 'upcoming' ? 'Upcoming Plans' :
                     'Past Plans'}
                  </p>
                  <p className="text-3xl font-bold mt-2 text-green-900">
                    {activeTab === 'active' ? categorizedPlans.active.length :
                     activeTab === 'upcoming' ? categorizedPlans.upcoming.length :
                     categorizedPlans.past.length}
                  </p>
                  <p className="text-xs mt-1 text-green-600">
                    {activeTab === 'active' ? 'Currently active' :
                     activeTab === 'upcoming' ? 'Starting soon' :
                     'Previously active'}
                  </p>
                </div>
                <div className="w-12 h-12 bg-gradient-to-br from-green-50 to-green-100 rounded-xl flex items-center justify-center shadow-lg">
                  <HiOutlineClipboardList className="w-6 h-6 text-green-600" />
                </div>
              </div>
            </div>

            <div className="bg-white rounded-xl p-6 border border-blue-400 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-blue-600 font-medium">Total Premium</p>
                  <p className="text-3xl font-bold text-blue-900 mt-2">${currentTabCost.toLocaleString()}</p>
                  <p className="text-xs text-blue-600 mt-1">
                    {activeTab === 'active' ? 'Current monthly cost' :
                     activeTab === 'upcoming' ? 'Future monthly cost' :
                     'Previous monthly cost'}
                  </p>
                </div>
                <div className="w-12 h-12 bg-gradient-to-br from-blue-50 to-blue-100 rounded-xl flex items-center justify-center shadow-lg">
                  <HiOutlineCurrencyDollar className="w-6 h-6 text-blue-600" />
                </div>
              </div>
            </div>

            {/* Card 3: Purple */}
            <div className="bg-white rounded-xl p-6 border border-purple-400 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-purple-600 font-medium">Employees</p>
                  <p className="text-3xl font-bold text-purple-900 mt-2">{company?.employeeCount || 0}</p>
                  <p className="text-xs text-purple-600 mt-1">Total covered</p>
                </div>
                <div className="w-12 h-12 bg-gradient-to-br from-purple-50 to-purple-100 rounded-xl flex items-center justify-center shadow-lg">
                  <HiOutlineOfficeBuilding className="w-6 h-6 text-purple-600" />
                </div>
              </div>
            </div>

            {/* Card 4: Orange */}
            <div className="bg-white rounded-xl p-6 border border-orange-400 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-orange-600 font-medium">Per Employee</p>
                  <p className="text-3xl font-bold text-orange-900 mt-2">${company?.employeeCount ? Math.round(currentTabCost / company.employeeCount) : 0}</p>
                  <p className="text-xs text-orange-600 mt-1">
                    {activeTab === 'active' ? 'Current avg cost' :
                     activeTab === 'upcoming' ? 'Future avg cost' :
                     'Previous avg cost'}
                  </p>
                </div>
                <div className="w-12 h-12 bg-gradient-to-br from-orange-50 to-orange-100 rounded-xl flex items-center justify-center shadow-lg">
                  <HiOutlineCurrencyDollar className="w-6 h-6 text-orange-600" />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Enhanced Search & Filter Bar */}
      <div className="bg-white ">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex flex-col lg:flex-row lg:items-center gap-4">
            {/* Search - Expanded */}
            <div className="relative flex-1">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <svg className="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
              </div>
              <input
                type="text"
                placeholder="Search plans by name, carrier, or type..."
                className="block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-lg text-sm text-gray-900 placeholder-gray-500 bg-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
              />
            </div>

            {/* Filters */}
            <div className="flex items-center gap-3 flex-shrink-0">
              <select className="px-3 py-3 border border-gray-300 rounded-lg text-sm text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white">
                <option value="all">All Status</option>
                <option value="active">Active</option>
                <option value="draft">Draft</option>
                <option value="inactive">Inactive</option>
              </select>

              <select className="px-3 py-3 border border-gray-300 rounded-lg text-sm text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white">
                <option value="all">All Types</option>
                <option value="medical">Medical</option>
                <option value="dental">Dental</option>
                <option value="vision">Vision</option>
              </select>

              <button className="inline-flex items-center gap-2 px-3 py-3 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors">
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.707A1 1 0 013 7V4z" />
                </svg>
                Filter
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Modern 3-Way Sliding Toggle Section */}
      <div className="bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-0">
          <div className="flex items-center justify-center">
            <div className="relative bg-gray-100 rounded-full p-1 flex items-center" style={{ width: '280px' }}>
              {/* Sliding Background */}
              <div
                className="absolute top-1 bottom-1 rounded-full transition-all duration-500 ease-out shadow-lg"
                style={{
                  width: '90px',
                  left: activeTab === 'active'
                    ? '4px'
                    : activeTab === 'upcoming'
                    ? '95px'
                    : '186px',
                  backgroundColor: activeTab === 'active'
                    ? '#10b981'
                    : activeTab === 'upcoming'
                    ? '#3b82f6'
                    : '#6b7280'
                }}
              />

              {/* Active Tab */}
              <button
                onClick={() => setActiveTab('active')}
                className={`relative z-10 flex-1 py-3 text-sm font-medium rounded-full transition-all duration-300 ease-in-out ${
                  activeTab === 'active'
                    ? 'text-white'
                    : 'text-gray-600 hover:text-green-600'
                }`}
              >
                Active
              </button>

              {/* Upcoming Tab */}
              <button
                onClick={() => setActiveTab('upcoming')}
                className={`relative z-10 flex-1 py-3 text-sm font-medium rounded-full transition-all duration-300 ease-in-out ${
                  activeTab === 'upcoming'
                    ? 'text-white'
                    : 'text-gray-600 hover:text-blue-600'
                }`}
              >
                Upcoming
              </button>

              {/* Past Tab */}
              <button
                onClick={() => setActiveTab('past')}
                className={`relative z-10 flex-1 py-3 text-sm font-medium rounded-full transition-all duration-300 ease-in-out ${
                  activeTab === 'past'
                    ? 'text-white'
                    : 'text-gray-600 hover:text-gray-700'
                }`}
              >
                Past
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Enhanced Plan List */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="bg-white rounded-2xl shadow-xl border border-gray-200 overflow-hidden">
          {/* List Header */}
          <div className="px-6 py-4 bg-gradient-to-r from-gray-50 to-gray-100 border-b border-gray-200">
            <div className="grid grid-cols-12 gap-4 text-sm font-semibold text-gray-700 uppercase tracking-wide">
              <div className="col-span-3 text-left">Plan Details</div>
              <div className="col-span-2 text-center">Coverage Type</div>
              <div className="col-span-1 text-center">Status</div>
              <div className="col-span-2 text-center">Enrollment Period</div>
              <div className="col-span-2 text-center">Employee Cost</div>
              <div className="col-span-2 text-center">Actions</div>
            </div>
          </div>

          {/* List Items */}
          <div className="divide-y divide-gray-200">
          {filteredPlans.length === 0 ? (
            <div className="px-6 py-16 text-center">
              <div className="w-20 h-20 mx-auto bg-gray-100 rounded-full flex items-center justify-center mb-6">
                <HiOutlineClipboardList className="h-10 w-10 text-gray-400" />
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                {activeTab === 'active' && 'No active plans'}
                {activeTab === 'upcoming' && 'No upcoming plans'}
                {activeTab === 'past' && 'No past plans'}
              </h3>
              <p className="text-gray-500 mb-8 max-w-sm mx-auto">
                {activeTab === 'active' && 'There are no plans currently active for this company. Add a new plan to get started.'}
                {activeTab === 'upcoming' && 'There are no plans scheduled to start in the future for this company.'}
                {activeTab === 'past' && 'There are no expired or past plans for this company.'}
              </p>
              <div className="space-y-3">
                <button
                  onClick={handleAddNewPlan}
                  className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-lg text-white bg-gradient-to-r from-blue-600 to-purple-600 hover:opacity-90 transition-all shadow-lg hover:shadow-xl"
                >
                  <HiOutlinePlus className="-ml-1 mr-2 h-5 w-5" />
                  Add First Plan
                </button>
                <div className="text-sm text-gray-400">
                  or browse available plans to assign
                </div>
              </div>
            </div>
          ) : (
            filteredPlans.map((plan) => {
            const getStatusColor = (status: string) => {
              switch (status?.toLowerCase()) {
                case 'active':
                  return 'bg-green-100 text-green-800';
                case 'draft':
                  return 'bg-yellow-100 text-yellow-800';
                case 'inactive':
                  return 'bg-red-100 text-red-800';
                default:
                  return 'bg-gray-100 text-gray-800';
              }
            };

            const getCoverageIcon = (coverageType: string) => {
              switch (coverageType?.toLowerCase()) {
                case 'medical':
                case 'health':
                  return <HiOutlineClipboardList className="w-5 h-5 text-blue-600" />;
                case 'dental':
                  return <svg className="w-5 h-5 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M10 2C6.686 2 4 4.686 4 8c0 1.5.5 3 1.5 4.5L10 18l4.5-5.5C15.5 11 16 9.5 16 8c0-3.314-2.686-6-6-6z"/>
                  </svg>;
                case 'vision':
                  return <svg className="w-5 h-5 text-purple-600" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M10 12a2 2 0 100-4 2 2 0 000 4z"/>
                    <path fillRule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clipRule="evenodd"/>
                  </svg>;
                default:
                  return <HiOutlineOfficeBuilding className="w-5 h-5 text-gray-600" />;
              }
            };

            const planWithDetails = plan as any; // Type assertion to access additional properties
            const monthlyPremium = planWithDetails.coverageTiers?.[0]?.employeeCost || 0;
            const enrollmentStart = planWithDetails.enrollmentStartDate ? new Date(planWithDetails.enrollmentStartDate).toLocaleDateString() : 'N/A';
            const enrollmentEnd = planWithDetails.enrollmentEndDate ? new Date(planWithDetails.enrollmentEndDate).toLocaleDateString() : 'N/A';

            return (
              <div key={plan._id} className="px-6 py-5 hover:bg-gray-50 transition-all duration-200">
                <div className="grid grid-cols-12 gap-4 items-center">
                  {/* Plan Details */}
                  <div className="col-span-3">
                    <div className="flex items-center space-x-3">
                      <div className="flex-shrink-0">
                        {getCoverageIcon(planWithDetails.coverageType)}
                      </div>
                      <div className="min-w-0 flex-1">
                        <div className="font-semibold text-gray-700 truncate">{plan.planName}</div>
                        <div className="text-sm text-gray-500 truncate">{plan.carrier} • {plan.planCode}</div>
                      </div>
                    </div>
                  </div>

                  {/* Coverage Type */}
                  <div className="col-span-2 text-center">
                    <div className="flex flex-col gap-1 items-center">
                      <span className="text-sm font-medium text-gray-700">
                        {planWithDetails.coverageType}
                      </span>
                      {plan.metalTier && (
                        <span className="text-xs font-medium text-gray-600">
                          {plan.metalTier}
                        </span>
                      )}
                    </div>
                  </div>

                  {/* Status */}
                  <div className="col-span-1 text-center">
                    <div className="flex justify-center">
                      <span className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold ${getStatusColor(plan.status)}`}>
                        <div className={`w-2 h-2 rounded-full mr-2 ${
                          plan.status?.toLowerCase() === 'active' ? 'bg-green-500' :
                          plan.status?.toLowerCase() === 'draft' ? 'bg-yellow-500' :
                          'bg-red-500'
                        }`}></div>
                        {plan.status}
                      </span>
                    </div>
                  </div>

                  {/* Enrollment Period */}
                  <div className="col-span-2 text-center">
                    <div className="text-sm font-medium text-gray-700">{enrollmentStart}</div>
                    <div className="text-xs text-gray-500">to {enrollmentEnd}</div>
                  </div>

                  {/* Employee Cost */}
                  <div className="col-span-2 text-center">
                    <div className="text-lg font-bold text-gray-700">${monthlyPremium}</div>
                    <div className="text-xs text-gray-500">per month</div>
                  </div>

                  {/* Actions */}
                  <div className="col-span-2">
                    <div className="flex items-center justify-center space-x-2">
                      {/* Renew Button */}
                      <button
                        onClick={() => handleRenewPlan(plan)}
                        className="px-3 py-1 text-xs font-medium text-white bg-red-500 hover:bg-red-600 rounded transition-all duration-200"
                        title="Renew Plan for Next Year"
                      >
                        Renew
                      </button>

                      {/* View Button */}
                      <button
                        onClick={() => handleViewPlanAssignment(plan)}
                        className="p-2 text-blue-600 hover:text-blue-800 hover:bg-blue-100 rounded-lg transition-all duration-200"
                        title="View Plan Assignment Details"
                      >
                        <HiOutlineEye className="w-4 h-4" />
                      </button>

                      {/* Edit Button */}
                      {plan.canEdit && (
                        <button
                          onClick={() => handleEditPlanAssignment(plan)}
                          className="p-2 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-lg transition-all duration-200"
                          title="Edit Plan Assignment"
                        >
                          <HiOutlinePencil className="w-4 h-4" />
                        </button>
                      )}

                      {/* Delete Button */}
                      <button
                        onClick={() => handleDeletePlan(plan)}
                        className="p-2 text-red-600 hover:text-red-800 hover:bg-red-100 rounded-lg transition-all duration-200"
                        title="Delete Plan Assignment"
                      >
                        <HiOutlineTrash className="w-4 h-4" />
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            );
          })
          )}
          </div>
        </div>
      </div>

      {/* Plan Selection Modal */}
      {showPlanSelectionModal && (
        <PlanSelectionModal
          isOpen={showPlanSelectionModal}
          onClose={handleModalClose}
          onSelectPlan={handlePlanSelected}
          onCreatePlan={handlePlanCreated}
          companyId={companyId}
          editMode={!!editingPlan}
          editingPlan={editingPlan}
          editingCoverageTiers={coverageTiers}
        />
      )}

      {/* View Plan Assignment Modal */}
      {showViewModal && viewingPlan && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-2xl w-full mx-4 max-h-[80vh] overflow-y-auto">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-xl font-bold text-gray-900">Plan Assignment Details</h2>
              <button
                onClick={() => setShowViewModal(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>

            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700">Plan Name</label>
                  <p className="text-sm text-gray-900">{viewingPlan.planName}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">Plan Code</label>
                  <p className="text-sm text-gray-900">{viewingPlan.planCode}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">Carrier</label>
                  <p className="text-sm text-gray-900">{viewingPlan.carrier}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">Status</label>
                  <p className="text-sm text-gray-900">{viewingPlan.status}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">Effective Date</label>
                  <p className="text-sm text-gray-900">
                    {viewingPlan.planEffectiveDate ? new Date(viewingPlan.planEffectiveDate).toLocaleDateString() : 'N/A'}
                  </p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">End Date</label>
                  <p className="text-sm text-gray-900">
                    {viewingPlan.planEndDate ? new Date(viewingPlan.planEndDate).toLocaleDateString() : 'N/A'}
                  </p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">Coverage Type</label>
                  <p className="text-sm text-gray-900">{viewingPlan.coverageType || viewingPlan.type}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">Assignment ID</label>
                  <p className="text-sm text-gray-900 font-mono">{viewingPlan._id}</p>
                </div>
              </div>
            </div>

            <div className="mt-6 flex justify-end">
              <button
                onClick={() => setShowViewModal(false)}
                className="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600 transition-colors"
              >
                Close
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Notification Modal */}
      <NotificationModal
        isOpen={notification.isOpen}
        onClose={hideNotification}
        type={notification.type}
        title={notification.title}
        message={notification.message}
        confirmText={notification.confirmText}
        onConfirm={notification.onConfirm}
      />
      </div>
    </>
  );
}

export default CompanyPlansPage;
