.manage-groups-page {
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;
  font-family: 'SF Pro', -apple-system, BlinkMacSystemFont, sans-serif;
  background: #f8fafc;
  min-height: 100vh;
}

/* Header */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.1rem;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.page-icon {
  color: #7c3aed;
}

.page-header h1 {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
}

.add-new-group-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: #000000;
  color: white;
  border: none;
  border-radius: 12px;
  padding: 12px 20px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.add-new-group-btn:hover {
  transform: translateY(-1px);
  background: #1f2937;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

/* Search */
.search-container {
  margin-bottom: 2rem;
}

.search-input-wrapper {
  position: relative;
  max-width: 100%;
}

.search-icon {
  position: absolute;
  left: 1rem;
  top: 50%;
  transform: translateY(-50%);
  color: #9ca3af;
}

.search-input {
  width: 100%;
  padding: 12px 16px 12px 3rem;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  font-size: 14px;
  background: white;
  transition: all 0.2s ease;
}

.search-input:focus {
  outline: none;
  border-color: #000000;
  background: white;
  box-shadow: 0 0 0 3px rgba(0, 0, 0, 0.1);
}

.search-input::placeholder {
  color: #9ca3af;
}

/* Companies List */
.companies-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-bottom: 2rem;
}

.company-card {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 16px;
  padding: 1.5rem;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.company-card:hover {
  border-color: #000000;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transform: translateY(-1px);
}

.company-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0rem;
}

.company-name {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
}

.select-btn {
  background: #f8fafc;
  color: #64748b;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  padding: 8px 16px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.select-btn:hover {
  background: #000000;
  color: white;
  border-color: #000000;
}

.company-details {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.industry-tag {
  display: inline-block;
  background: #f1f5f9;
  color: #475569;
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 500;
  width: fit-content;
}

.company-stats {
  display: flex;
  gap: 1.5rem;
  align-items: center;
}

.stat {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #64748b;
  font-size: 14px;
}

.stat svg {
  color: #94a3b8;
}

.plans-assigned {
  color: #000000;
  font-weight: 500;
}

/* No Companies */
.no-companies {
  text-align: center;
  padding: 3rem;
  color: #6b7280;
}

.no-companies svg {
  color: #d1d5db;
  margin-bottom: 1rem;
}

.no-companies h3 {
  font-size: 1.125rem;
  font-weight: 600;
  color: #374151;
  margin: 0 0 0.5rem 0;
}

.no-companies p {
  margin: 0;
  font-size: 14px;
}

/* Back Button */
.back-button-container {
  display: flex;
  justify-content: flex-start;
}

.back-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: white;
  color: #374151;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 12px 20px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  font-family: 'SF Pro', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.back-button:hover {
  background: #f9fafb;
  border-color: #d1d5db;
  transform: translateY(-1px);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
}

/* Loading */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem;
  color: #6b7280;
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid #e5e7eb;
  border-top: 3px solid #000000;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Ensure page background is white */
body {
  background-color: #ffffff !important;
}

/* Broker Dashboard Styles */
.broker-dashboard {
  max-width: min(90vw, 1400px);
  margin: 0 auto;
  padding: clamp(1rem, 4vw, 3rem);
  background: #ffffff;
  min-height: 100vh;
  font-family: 'SF Pro', 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
}

.dashboard-header {
  background: transparent;
  color: #1f2937;
  margin-bottom: clamp(1.5rem, 4vw, 2.5rem);
  text-align: left;
}

/* Header content uses design system classes - page-title and subtitle-text */
.header-content h1 {
  margin: 0 0 0.75rem 0;
  color: #111827 !important;
  text-align: left !important;
  font-size: clamp(24px, 4vw, 32px) !important;
  line-height: 1.2 !important;
  font-weight: 600;
}

.header-content p {
  margin: 0;
  color: #6b7280 !important;
  text-align: left !important;
  font-size: 16px;
  line-height: 1.5;
}

/* Specific rule for Broker Dashboard title left alignment */
.dashboard-header .page-title,
.dashboard-header h1,
.dashboard-header .header-content h1 {
  text-align: left !important;
}

/* Stats Grid */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: clamp(1rem, 3vw, 2rem);
  margin-bottom: clamp(2rem, 5vw, 3rem);
}

.stat-card {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 16px;
  padding: 1.5rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 1rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.15), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.stat-card:hover {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  transform: scale(1.03);
}

/* Colored borders for stat cards matching their icons */
.stat-card:nth-child(1) {
  border-color: #93c5fd;
}

.stat-card:nth-child(1):hover {
  border-color: #2563eb;
}

.stat-card:nth-child(2) {
  border-color: #86efac;
}

.stat-card:nth-child(2):hover {
  border-color: #059669;
}

.stat-card:nth-child(3) {
  border-color: #fcd34d;
}

.stat-card:nth-child(3):hover {
  border-color: #d97706;
}

.stat-icon {
  border-radius: 12px;
  padding: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Colored stat icons */
.stat-card:nth-child(1) .stat-icon {
  background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
  color: #2563eb;
  
}

.stat-card:nth-child(2) .stat-icon {
  background: linear-gradient(135deg, #dcfce7 0%, #bbf7d0 100%);
  color: #059669;
  
}

.stat-card:nth-child(3) .stat-icon {
  background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
  color: #d97706;
  
}

.stat-content {
  flex: 1;
}

.stat-number {
  font-size: 1.75rem;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 0.25rem;
}

.stat-label {
  font-size: 14px;
  color: #6b7280;
  font-weight: 500;
}

/* Quick Actions */
.quick-actions {
  margin-bottom: 3rem;
}

/* Quick actions uses design system classes - section-header and body-text */
.quick-actions h2 {
  margin: 0 0 0.5rem 0;
}

.quick-actions > p {
  margin: 0 0 2rem 0;
}

.action-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(380px, 1fr));
  gap: clamp(1.5rem, 4vw, 2.5rem);
}

.action-card {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 16px;
  padding: 2rem;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.2), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  display: flex;
  gap: 1.5rem;
  align-items: flex-start;
}

.action-card:hover {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  transform: translateY(-2px);
}

.action-card:nth-child(1):hover {
  border-color: #000000;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(37, 99, 235, 0.05);
}

.action-card:nth-child(2):hover {
  border-color: #000000;
  box-shadow: 0 10px 15px -3px rgba(5, 150, 105, 0.1), 0 4px 6px -2px rgba(5, 150, 105, 0.05);
}

.action-icon {
  border-radius: 12px;
  padding: 16px;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Purple action icons */
.action-card .action-icon {
  background: linear-gradient(135deg, #2563eb 0%, #8b5cf6 100%);
  color: #ffffff;
  border: 1px solid linear-gradient(135deg, #2563eb 0%, #8b5cf6 100%);
}

.action-content {
  flex: 1;
}

/* Action content uses design system classes - section-header and body-text */
.action-content h3 {
  margin: 0 0 0.5rem 0;
}

.action-content p {
  margin: 0 0 1rem 0;
}

.action-tags {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.tag {
  background: #f1f5f9;
  color: #475569;
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
}

/* Select Company Page Styles */
.breadcrumb {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 1.5rem;
  font-size: 14px;
}

.breadcrumb-link {
  color: #000000;
  cursor: pointer;
  text-decoration: none;
}

.breadcrumb-link:hover {
  text-decoration: underline;
}

.breadcrumb-separator {
  color: #9ca3af;
}

.breadcrumb-current {
  color: #6b7280;
}

.header-text {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.header-text p {
  color: #6b7280;
  font-size: 14px;
  margin: 0;
}

.company-title {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.status-badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
}

.status-badge.active {
  background: #dcfce7;
  color: #166534;
}

.status-badge.pending {
  background: #fef3c7;
  color: #92400e;
}

.assign-plans-btn {
  background: #000000;
  color: white;
  border: none;
  border-radius: 12px;
  padding: 8px 16px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.assign-plans-btn:hover {
  transform: translateY(-1px);
  background: #1f2937;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.company-meta {
  display: flex;
  gap: 1rem;
  font-size: 12px;
  color: #6b7280;
}

.company-ein {
  font-family: monospace;
  background: #f3f4f6;
  padding: 2px 6px;
  border-radius: 4px;
}

/* Responsive */
@media (max-width: 768px) {
  .manage-groups-page,
  .broker-dashboard {
    padding: 1rem;
  }

  .dashboard-header {
    padding: 2rem 1rem;
  }

  .header-content h1 {
    font-size: 1.5rem;
  }

  .header-content p {
    font-size: 1rem;
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }

  .action-cards {
    grid-template-columns: 1fr;
  }

  .action-card {
    flex-direction: column;
    text-align: center;
  }

  .page-header {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }

  .company-header {
    flex-direction: column;
    gap: 0.75rem;
    align-items: stretch;
  }

  .company-stats {
    flex-direction: column;
    gap: 0.5rem;
    align-items: flex-start;
  }

  .company-title {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .company-meta {
    flex-direction: column;
    gap: 0.5rem;
  }
}
