'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import { HiOutlineSearch, HiOutlineOfficeBuilding, HiOutlineLocationMarker, HiOutlineUsers, HiOutlineCalendar, HiOutlineClipboardList } from 'react-icons/hi';
import ProtectedRoute from '@/components/ProtectedRoute';
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '@/redux/store';
import { getAllCompaniesUnderBroker } from '@/middleware/company_middleware';
import { getPlanAssignmentsByCompany, getBrokerPlanAssignmentsCount } from '../services/planAssignmentApi';
import AddNewGroupModal from '../../components/AddNewGroupModal';
import EnrollmentHeader from '../../employee-enrol/components/EnrollmentHeader';
import '../manage-groups.css';

interface Company {
  _id: string;
  companyName: string;
  ein: string;
  location: string;
  companySize: number;
  status: 'active' | 'pending' | 'inactive';
  industry?: string;
  plansAssigned?: number;
  joinDate?: string;
}

export default function SelectCompanyPage() {
  const router = useRouter();
  const dispatch = useDispatch();
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [plansManaged, setPlansManaged] = useState(0);
  const [showAddModal, setShowAddModal] = useState(false);

  const managedCompanies = useSelector((state: RootState) => state.user.managedCompanies);

  // Function to fetch plan assignments count using optimized broker API
  const fetchPlanAssignmentsCount = useCallback(async () => {
    try {
      console.log('🔍 Fetching plan assignments count for broker...');
      const countResult = await getBrokerPlanAssignmentsCount();

      if (countResult.success && countResult.data) {
        const totalCount = countResult.data.count;
        console.log('✅ Total plan assignments managed by broker:', totalCount);
        setPlansManaged(totalCount);
      } else {
        console.warn('❌ Failed to fetch broker plan assignments count:', countResult.error);
        setPlansManaged(0);
      }
    } catch (error) {
      console.error('❌ Error fetching plan assignments count:', error);
      setPlansManaged(0);
    }
  }, []);

  const fetchCompanies = useCallback(async () => {
    try {
      // Only access localStorage in browser environment
      if (typeof window !== 'undefined') {
        const userId = localStorage.getItem('userid1') || localStorage.getItem('userId');
        if (userId) {
          await getAllCompaniesUnderBroker(dispatch, userId);
        } else {
          console.error('User ID not found. Please authenticate first.');
        }
      }
    } catch (error) {
      console.error('Error fetching companies:', error);
    } finally {
      setLoading(false);
    }
  }, [dispatch]);

  useEffect(() => {
    const fetchData = async () => {
      await fetchCompanies();
      await fetchPlanAssignmentsCount();
    };
    fetchData();
  }, [fetchCompanies, fetchPlanAssignmentsCount]);

  // Transform managed companies to match our interface
  const companies: Company[] = managedCompanies?.map((company, index) => ({
    _id: company._id,
    companyName: company.name,
    ein: company.ein || `12-${String(index + 1).padStart(7, '0')}`,
    location: company.location || 'San Francisco, CA',
    companySize: company.companySize || Math.floor(Math.random() * 500) + 50,
    status: company.isActivated ? 'active' : 'pending',
    industry: company.industry || 'Technology',
    plansAssigned: Math.floor(Math.random() * 10) + 2,
    joinDate: company.createdAt ? new Date(company.createdAt).toLocaleDateString() : new Date().toLocaleDateString()
  })) || [];

  const filteredCompanies = companies.filter(company =>
    company.companyName.toLowerCase().includes(searchTerm.toLowerCase()) ||
    company.ein.includes(searchTerm) ||
    company.location.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleManagePlans = (companyId: string) => {
    router.push(`/ai-enroller/manage-groups/company/${companyId}/plans`);
  };

  const handleAddNewGroup = () => {
    setShowAddModal(true);
  };

  const handleModalSuccess = async () => {
    setShowAddModal(false);
    // Refresh data after successful group creation
    await fetchCompanies();
    await fetchPlanAssignmentsCount();
  };

  // Calculate dashboard stats
  const totalCompanies = managedCompanies?.length || 0;
  const totalEmployees = managedCompanies?.reduce((sum, company) => sum + company.companySize, 0) || 0;

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-black mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading companies...</p>
        </div>
      </div>
    );
  }

  return (
    <ProtectedRoute>
      <EnrollmentHeader />
      <div className="min-h-screen bg-white">
      {/* Breadcrumb */}
      <div className="bg-white border-b border-gray-200 px-6 py-4">
        <div className="max-w-6xl mx-auto">
          <nav className="flex items-center space-x-2 text-sm">
            <button
              onClick={() => router.push('/ai-enroller')}
              className="text-gray-600 hover:text-gray-900 font-medium transition-colors"
            >
              Home
            </button>
            <span className="text-gray-400">›</span>
            <span className="text-gray-900 font-medium">Select Company</span>
          </nav>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-6xl mx-auto px-6 py-8 bg-white">
        {/* Page Header */}
        <div className="page-header">
          <div className="header-left">
            <div className="page-icon">
              <HiOutlineOfficeBuilding size={24} />
            </div>
            <h1>Manage Groups</h1>
          </div>
          <button className="add-new-group-btn" onClick={handleAddNewGroup} style={{
            background: 'linear-gradient(135deg, #2563eb 0%, #8b5cf6 100%)',
            border: 'none'
          }}>
            <span>+</span>
            Add New Group
          </button>
        </div>

        <div className="mb-6">
          <p
            className="text-gray-600"
            style={{
              fontSize: '16px',
              lineHeight: '1.6',
              fontFamily: "'SF Pro', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif"
            }}
          >
            &nbsp;Oversee all employer groups and their benefit plan assignments
          </p>
        </div>

        {/* Stats Cards */}
        <div className="stats-grid">
          <div className="stat-card">
            <div className="stat-content">
              <div className="stat-number">{totalCompanies}</div>
              <div className="stat-label">Active Companies</div>
            </div>
            <div className="stat-icon">
              <HiOutlineOfficeBuilding size={24} />
            </div>
          </div>

          <div className="stat-card">
            <div className="stat-content">
              <div className="stat-number">{totalEmployees.toLocaleString()}</div>
              <div className="stat-label">Total Employees</div>
            </div>
            <div className="stat-icon">
              <HiOutlineUsers size={24} />
            </div>
          </div>

          <div className="stat-card">
            <div className="stat-content">
              <div className="stat-number">{plansManaged}</div>
              <div className="stat-label">Plan Assignments</div>
            </div>
            <div className="stat-icon">
              <HiOutlineClipboardList size={24} />
            </div>
          </div>
        </div>

        {/* Search Bar */}
        <div className="mb-6">
          <div className="relative w-full">
            <HiOutlineSearch className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
            <input
              type="text"
              placeholder="Search by company name, EIN, or location..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-12 pr-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm bg-white transition-all duration-200 hover:border-gray-400 text-gray-900"
              style={{
                fontFamily: "'SF Pro', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif",
                boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
                color: '#111827'
              }}
            />
          </div>
        </div>

        {/* Companies List */}
        {filteredCompanies.length === 0 ? (
          <div className="text-center py-12">
            <HiOutlineOfficeBuilding className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">No companies found</h3>
            <p className="mt-1 text-sm text-gray-500">
              {searchTerm ? 'Try adjusting your search terms.' : 'No companies available.'}
            </p>
          </div>
        ) : (
          <div className="space-y-3">
            {filteredCompanies.map((company) => (
              <div
                key={company._id}
                className="bg-white border border-gray-200 rounded-xl transition-all duration-300 hover:border-gray-300"
                style={{
                  boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)'
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.transform = 'scale(1.03)';
                  e.currentTarget.style.boxShadow = '0 0 0 rgba(0, 0, 0, 0.15)';
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.transform = 'scale(1)';
                  e.currentTarget.style.boxShadow = '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)';
                }}
              >
                <div className="p-4">
                  <div className="flex items-center justify-between">
                    {/* Left side - Company info */}
                    <div className="flex items-center gap-4 flex-1">
                      {/* Company icon */}
                      <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center flex-shrink-0">
                        <HiOutlineOfficeBuilding className="w-5 h-5 text-white" />
                      </div>

                      {/* Company details */}
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-3 mb-1">
                          <h3 className="text-base font-semibold text-gray-900 truncate">
                            {company.companyName}
                          </h3>
                          <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${
                            company.status === 'active'
                              ? 'bg-green-100 text-green-800'
                              : company.status === 'pending'
                              ? 'bg-yellow-100 text-yellow-800'
                              : 'bg-red-100 text-red-800'
                          }`}>
                            {company.status}
                          </span>
                        </div>

                        <div className="flex items-center gap-6 text-sm text-gray-500">
                          <span className="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-gray-100 text-gray-700">
                            {company.industry}
                          </span>
                          <div className="flex items-center gap-1">
                            <HiOutlineUsers className="w-4 h-4" />
                            <span>{company.companySize} employees</span>
                          </div>
                          <div className="flex items-center gap-1">
                            <HiOutlineLocationMarker className="w-4 h-4" />
                            <span>{company.location}</span>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Right side - Action button */}
                    <div className="flex-shrink-0 ml-6">
                      <button
                        onClick={() => handleManagePlans(company._id)}
                        className="px-4 py-2 bg-gradient-to-r from-blue-600 to-purple-600 text-white font-medium rounded-lg hover:shadow-lg transition-all duration-200 hover:-translate-y-0.5 text-sm"
                        style={{
                          background: 'linear-gradient(135deg, #2563eb 0%, #8b5cf6 100%)',
                          boxShadow: '0 2px 4px -1px rgba(0, 0, 0, 0.1)',
                          fontFamily: "'SF Pro', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif"
                        }}
                      >
                        Manage Plans
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Add New Group Modal */}
        {showAddModal && (
          <AddNewGroupModal
            isOpen={showAddModal}
            onClose={() => setShowAddModal(false)}
            onSuccess={handleModalSuccess}
          />
        )}
      </div>
      </div>
    </ProtectedRoute>
  );
}
