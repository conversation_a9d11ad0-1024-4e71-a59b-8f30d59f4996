/* Plans Management Page Styles */
:root {
  --primary-gradient: linear-gradient(135deg, #2563eb 0%, #8b5cf6 100%);
}

.plans-page {
  max-width: min(90vw, 1400px);
  margin: 0 auto;
  padding: clamp(1rem, 4vw, 3rem);
  background: #ffffff;
  min-height: 100vh;
  font-family: 'SF Pro', 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
}

/* Header */
.plans-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: clamp(1.5rem, 4vw, 2.5rem);
  gap: 1rem;
}

.header-actions {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.back-btn, .create-btn, .data-manager-btn, .ask-questions-btn, .dashboard-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 12px 20px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  font-family: 'SF Pro', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: none;
}

.back-btn {
  background: white;
  color: #374151;
  border: 1px solid #e5e7eb;
}

.back-btn:hover {
  background: #f9fafb;
  border-color: #d1d5db;
  transform: translateY(-1px);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
}

.create-btn {
  background: var(--primary-gradient);
  color: white;
  box-shadow: 0 2px 8px rgba(37, 99, 235, 0.25);
}

.create-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(37, 99, 235, 0.4);
}

.ask-questions-btn {
  background: white;
  color: #374151;
  border: 1px solid #e5e7eb;
}

.ask-questions-btn:hover {
  background: #f9fafb;
  border-color: #d1d5db;
  transform: translateY(-1px);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
}

.dashboard-btn {
  background: var(--primary-gradient);
  color: white;
  box-shadow: 0 2px 8px rgba(37, 99, 235, 0.25);
}

.dashboard-btn:hover {
  background: var(--primary-gradient);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(37, 99, 235, 0.4);
  filter: brightness(1.1);
}

.data-manager-btn {
  background: linear-gradient(135deg, #059669 0%, #10b981 100%);
  color: white;
  box-shadow: 0 2px 8px rgba(5, 150, 105, 0.25);
}

.data-manager-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(5, 150, 105, 0.4);
}

/* Page Title */
.page-title {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: clamp(2rem, 5vw, 3rem);
}

.title-icon {
  display: flex;
  color: #ffffff;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, #2563eb 0%, #8b5cf6 100%);
  border-radius: 12px;
  flex-shrink: 0;
}
.left-align-wrapper {
  display: flex;
  justify-content: flex-start;  /* aligns items to the left horizontally */
  align-items: center;          /* aligns items vertically in the middle */
  text-align: left;
  gap: 12px;                    /* adds space between icon and content */
}
.title-content h1 {
  font-size: 24px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 0 0;
  line-height: 1.5;
  font-family: 'SF Pro', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.title-content p {
  font-size: 14px;
  color: #6b7280;
  margin: 0;
  line-height: 1.6;
  font-family: 'SF Pro', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* Statistics Grid */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(min(100%, 200px), 1fr));
  gap: clamp(1rem, 3vw, 1.5rem);
  margin-bottom: clamp(2rem, 2vw, 3rem);
}

.stat-card {
  background: white;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  padding: 24px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 4px 4px 6px rgba(0, 0, 0, 0.2);

  transition: all 0.3s ease;
}
.stat-card.blue {
  background: #ffffff;         /* same as icon */
  border-color: #8bb5ec;
  color: #3b82f6;
}

.stat-card.green {
  background: #ffffff;
  border-color: #bbf7d0;
  color: #22c55e;
}

.stat-card.orange {
  background: #ffffff;
  border-color: #fed7aa;
  color: #f97316;
}
.stat-card:hover {
  transform: scale(1.07);
  box-shadow: 4px 6px 8px rgba(0, 0, 0, 0.1);
}

.stat-content {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.stat-icon.blue {
  background: #eff6ff;
  color: #3b82f6;
}

.stat-icon.green {
  background: #f0fdf4;
  color: #22c55e;
}

.stat-icon.orange {
  background: #fff7ed;
  color: #f97316;
}

.stat-number {
  font-size: 32px;
  font-weight: 700;
  color: #1f2937;
  line-height: 1;
  font-family: 'SF Pro', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.stat-label {
  font-size: 14px;
  color: #6b7280;
  font-weight: 500;
}

/* Search and Filter */
.search-filter-section {
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 0.75rem;
  padding: 1.5rem;
  margin-bottom: 2rem;
  box-shadow: 4px 4px 6px rgba(0, 0, 0, 0.2);
}

.filter-icon {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  font-weight: 500;
  color: #141e2c;
  margin-bottom: 1rem;
}

.search-controls {
  display: flex;
  gap: 1rem;
  align-items: center;
  flex-wrap: wrap;
  margin-bottom: 1rem;
}

.search-input {
  flex: 1;
  min-width: 300px;
  padding: 0.75rem 1rem;
  border: 1px solid #d1d5db;
  border-radius: 0.5rem;
  font-size: 0.875rem;
  color: #374151;
  background: white;
  transition: all 0.2s ease;
  font-family: 'SF Pro', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.search-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.status-filter,
.carrier-filter {
  padding: 0.75rem 1rem;
  border: 1px solid #d1d5db;
  border-radius: 0.5rem;
  font-size: 0.875rem;
  color: #374151;
  background: white;
  cursor: pointer;
  transition: all 0.2s ease;
  font-family: 'SF Pro', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  min-width: 140px;
}

.status-filter:focus,
.carrier-filter:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.clear-filters-btn {
  padding: 0.75rem 1rem;
  background: #f8fafc;
  color: #64748b;
  border: 1px solid #e2e8f0;
  border-radius: 0.5rem;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  font-family: 'SF Pro', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.clear-filters-btn:hover {
  background: #f1f5f9;
  border-color: #cbd5e1;
}

.results-count {
  font-size: 0.875rem;
  color: #64748b;
  font-weight: 500;
}

/* Plans Table */
.plans-table-container {
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 0.75rem;
  overflow: hidden;
  box-shadow: 4px 4px 6px rgba(0, 0, 0, 0.2);
}

.table-header {
  padding: 1.5rem;
  border-bottom: 1px solid #e2e8f0;
  background: #ffffff;
}

.table-header h3 {
  font-size: clamp(18px, 4vw, 20px);
  font-weight: 600;
  color: #1e293b;
  margin: 0;
  font-family: 'SF Pro', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.table-wrapper {
  overflow-x: auto;
}

.plans-table {
  width: 100%;
  border-collapse: collapse;
  font-family: 'SF Pro', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.plans-table thead th {
  background: #ffffff;
  padding: 1rem 1.5rem;
  text-align: left;
  font-size: 0.875rem;
  font-weight: 600;
  color: #475569;
  border-bottom: 1px solid #e2e8f0;
  white-space: nowrap;
}

.plans-table tbody td {
  padding: 1rem 1.5rem;
  border-bottom: 1px solid #f1f5f9;
  vertical-align: middle;
}

.plans-table tbody tr:hover {
  background: #f8fafc;
}

.plan-name {
  font-weight: 600;
  color: #1e293b;
  font-size: 0.875rem;
}

.plan-code-badge {
  background: #f1f5f9;
  color: #475569;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  font-size: 0.75rem;
  font-weight: 600;
  font-family: 'SF Mono', 'Monaco', 'Cascadia Code', 'Roboto Mono', monospace;
  border: 1px solid #e2e8f0;
}

.carrier-type-badge {
  padding: 0.25rem 0.75rem;
  border-radius: 0.375rem;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: capitalize;
  font-family: 'SF Pro', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.carrier-type-badge.health,
.carrier-type-badge.medical {
  background: #dcfce7;
  color: #166534;
}

.carrier-type-badge.dental {
  background: #fef3c7;
  color: #92400e;
}

.carrier-type-badge.vision {
  background: #dbeafe;
  color: #1e40af;
}

.carrier-type-badge.life {
  background: #f3e8ff;
  color: #7c3aed;
}

.carrier-type-badge.your-health {
  background: #dbeafe;
  color: #1e40af;
}

/* Default for unknown types */
.carrier-type-badge:not(.health):not(.medical):not(.dental):not(.vision):not(.life):not(.your-health) {
  background: #f1f5f9;
  color: #475569;
}

.status-badge {
  padding: 0.25rem 0.5rem;
  border-radius: 0.5rem;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: capitalize;
}

.status-badge.active {
  background: #dcfce7;
  color: #166534;
}

.status-badge.inactive {
  background: #fee2e2;
  color: #dc2626;
}

.status-badge.draft {
  background: #fef3c7;
  color: #d97706;
}

.status-badge.template {
  background: #dbeafe;
  color: #1e40af;
}

.status-badge.archived {
  background: #fee2e2;
  color: #dc2626;
}

.groups-count {
  font-weight: 600;
  color: #3b82f6;
  font-size: 0.875rem;
}

/* Action Buttons */
.action-buttons {
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

.action-btn {
  width: 2rem;
  height: 2rem;
  border: 1px solid #e5e7eb;
  border-radius: 0.375rem;
  background: white;
  color: #6b7280;
  cursor: pointer;
  transition: all 0.2s ease;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.action-btn:hover {
  background: #f9fafb;
  border-color: #d1d5db;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.action-btn.edit:hover {
  color: #3b82f6;
  border-color: #3b82f6;
  background: #eff6ff;
}

.action-btn.copy:hover {
  color: #059669;
  border-color: #059669;
  background: #ecfdf5;
}

.action-btn.activate:hover {
  color: #16a34a;
  border-color: #16a34a;
  background: #f0fdf4;
}

.action-btn.deactivate:hover {
  color: #ea580c;
  border-color: #ea580c;
  background: #fff7ed;
}

.action-btn.delete:hover {
  color: #dc2626;
  border-color: #dc2626;
  background: #fef2f2;
}

/* Tooltip styles for action buttons */
.action-btn::after {
  content: attr(title);
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  background: #1f2937;
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  font-size: 0.75rem;
  white-space: nowrap;
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.2s ease;
  z-index: 10;
  margin-bottom: 0.25rem;
}

.action-btn:hover::after {
  opacity: 1;
}

/* Empty State */
.empty-state {
  grid-column: 1 / -1;
  text-align: center;
  padding: clamp(3rem, 8vw, 5rem) clamp(2rem, 5vw, 3rem);
  background: white;
  border-radius: clamp(0.75rem, 2vw, 1rem);
  border: 1px solid #e2e8f0;
  color: #64748b;
}

.empty-state svg {
  color: #d1d5db;
  margin-bottom: 1.5rem;
}

.empty-state h3 {
  font-size: clamp(1.125rem, 3vw, 1.25rem);
  font-weight: 600;
  color: #374151;
  margin: 0 0 0.5rem 0;
  font-family: 'SF Pro', 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.empty-state p {
  font-size: clamp(0.875rem, 2.5vw, 1rem);
  line-height: 1.6;
  margin: 0 0 2rem 0;
  max-width: 400px;
  margin-left: auto;
  margin-right: auto;
}

.create-first-plan-btn {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  background: linear-gradient(135deg, #8b5cf6 0%, #a855f7 100%);
  color: white;
  border: none;
  padding: clamp(0.75rem, 2.5vw, 1rem) clamp(1.5rem, 4vw, 2rem);
  border-radius: clamp(0.5rem, 1.5vw, 0.75rem);
  font-size: clamp(0.8rem, 2vw, 0.875rem);
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  font-family: 'SF Pro', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  box-shadow: 0 2px 8px rgba(139, 92, 246, 0.25);
}

.create-first-plan-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(139, 92, 246, 0.4);
}

/* Plan Cards */
.plan-card {
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: clamp(0.75rem, 2vw, 1rem);
  overflow: hidden;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.plan-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  border-color: #8b5cf6;
}

.plan-card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: clamp(1.5rem, 4vw, 2rem);
  border-bottom: 1px solid #f1f5f9;
  background: linear-gradient(135deg, #fafbff 0%, #f8fafc 100%);
}

.plan-info h3 {
  font-size: clamp(1rem, 2.5vw, 1.125rem);
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 0.5rem 0;
  font-family: 'SF Pro', 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.plan-meta {
  display: flex;
  gap: 0.75rem;
  align-items: center;
  flex-wrap: wrap;
}

.plan-code {
  background: #8b5cf6;
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  font-size: clamp(0.7rem, 1.8vw, 0.75rem);
  font-weight: 600;
  font-family: 'SF Mono', 'Monaco', 'Cascadia Code', 'Roboto Mono', monospace;
}

.plan-status {
  padding: 0.25rem 0.5rem;
  border-radius: 0.5rem;
  font-size: clamp(0.7rem, 1.8vw, 0.75rem);
  font-weight: 600;
-transform: uppercase;
}

.plan-status.active {
  background: #dcfce7;
  color: #166534;
}

.plan-status.inactive {
  background: #fee2e2;
  color: #dc2626;
}

.plan-status.draft {
  background: #fef3c7;
  color: #d97706;
}

.plan-status.archived {
  background: #fee2e2;
  color: #dc2626;
}

.plan-actions {
  display: flex;
  gap: 0.5rem;
}

.action-btn {
  width: 2rem;
  height: 2rem;
  border: 1px solid #e5e7eb;
  border-radius: 0.375rem;
  background: white;
  color: #6b7280;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-btn:hover {
  background: #f9fafb;
  border-color: #d1d5db;
}

.action-btn.view:hover {
  color: #3b82f6;
  border-color: #3b82f6;
}

.action-btn.duplicate:hover {
  color: #059669;
  border-color: #059669;
}

.action-btn.delete:hover {
  color: #dc2626;
  border-color: #dc2626;
}

/* Plan Card Content */
.plan-card-content {
  padding: clamp(1.5rem, 4vw, 2rem);
}

.plan-description {
  font-size: clamp(0.8rem, 2vw, 0.875rem);
  color: #64748b;
  line-height: 1.6;
  margin: 0 0 1.5rem 0;
}

.plan-details {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.plan-detail {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.detail-label {
  font-size: clamp(0.7rem, 1.8vw, 0.75rem);
  color: #9ca3af;
  font-weight: 500;
-transform: uppercase;
  letter-spacing: 0.05em;
}

.detail-value {
  font-size: clamp(0.8rem, 2vw, 0.875rem);
  color: #374151;
  font-weight: 600;
}

.plan-highlights {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.highlight-tag {
  background: #f1f5f9;
  color: #475569;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  font-size: clamp(0.7rem, 1.8vw, 0.75rem);
  font-weight: 500;
}

.highlight-more {
  background: #e2e8f0;
  color: #64748b;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  font-size: clamp(0.7rem, 1.8vw, 0.75rem);
  font-weight: 500;
}

/* Plan Card Footer */
.plan-card-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: clamp(1rem, 3vw, 1.5rem) clamp(1.5rem, 4vw, 2rem);
  border-top: 1px solid #f1f5f9;
  background: #fafbfc;
}

.activation-btn {
  padding: 0.5rem 1rem;
  border-radius: 0.375rem;
  font-size: clamp(0.75rem, 2vw, 0.8rem);
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
  font-family: 'SF Pro', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.activation-btn.active {
  background: #dcfce7;
  color: #166534;
}

.activation-btn.active:hover {
  background: #bbf7d0;
}

.activation-btn.inactive {
  background: #fee2e2;
  color: #dc2626;
}

.activation-btn.inactive:hover {
  background: #fecaca;
}

.plan-id {
  font-size: clamp(0.7rem, 1.8vw, 0.75rem);
  color: #9ca3af;
  font-family: 'SF Mono', 'Monaco', 'Cascadia Code', 'Roboto Mono', monospace;
}

/* Responsive Design */
@media (max-width: 768px) {
  .search-filter-section {
    flex-direction: column;
    align-items: stretch;
  }

  .filter-select {
    min-width: auto;
  }

  .plans-header {
    flex-direction: column;
    align-items: stretch;
    gap: 1rem;
  }

  .header-actions {
    flex-direction: column;
    gap: 0.75rem;
    align-items: stretch;
  }

  .plan-card-header {
    flex-direction: column;
    gap: 1rem;
  }

  .plan-actions {
    align-self: flex-end;
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }

  .plan-details {
    grid-template-columns: 1fr;
  }

  .plan-card-footer {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }

  .plan-id {
    text-align: center;
  }
}

@media (max-width: 480px) {
  .plans-list {
    grid-template-columns: 1fr;
  }

  .plan-meta {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
}

/* Loading and Error States */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
  color: #6b7280;
  text-align: center;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid #e5e7eb;
  border-top: 3px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
  color: #dc2626;
  text-align: center;
}

.retry-btn {
  margin-top: 1rem;
  padding: 0.5rem 1rem;
  background: #3b82f6;
  color: white;
  border: none;
  border-radius: 0.375rem;
  cursor: pointer;
  font-weight: 500;
  transition: background-color 0.2s ease;
}

.retry-btn:hover {
  background: #2563eb;
}

/* Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 1rem;
}

.modal-content {
  background: white;
  border-radius: 12px;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  max-width: 500px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
}

.plan-modal-content {
  max-width: 800px;
  width: 95%;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 1px solid #e5e7eb;
}

.modal-header h2 {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
  font-family: 'SF Pro', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.modal-body {
  padding: 1.5rem;
  font-size: 14px;
  line-height: 21px;
  font-family: 'SF Pro', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.modal-body p {
  font-size: 14px;
  line-height: 21px;
  color: #374151;
  margin: 0;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  padding: 1.5rem;
  border-top: 1px solid #e5e7eb;
}

.modal-btn {
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
}

.modal-btn.primary {
  background: var(--primary-gradient);
  color: white;
}

.modal-btn.primary:hover {
  filter: brightness(1.1);
  transform: translateY(-1px);
}

.modal-btn.secondary {
  background: #f9fafb;
  color: #374151;
  border: 1px solid #e5e7eb;
}

.modal-btn.secondary:hover {
  background: #f3f4f6;
  border-color: #d1d5db;
}

.modal-close {
  background: none;
  border: none;
  font-size: 1.5rem;
  color: #6b7280;
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 0.25rem;
  transition: all 0.2s ease;
}

.modal-close:hover {
  background: #f3f4f6;
  color: #374151;
}

.modal-body {
  padding: 1.5rem;
}

.modal-body p {
  color: #6b7280;
  margin: 0 0 1.5rem 0;
  font-size: 0.875rem;
}

.modal-actions {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.modal-action-btn {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
  background: white;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: left;
}

.modal-action-btn:hover {
  border-color: var(--primary-blue);
  box-shadow: 0 2px 8px rgba(37, 99, 235, 0.1);
  transform: translateY(-1px);
}

.modal-action-btn.primary:hover {
  background: #eff6ff;
}

.modal-action-btn.secondary:hover {
  background: #f8fafc;
}

.modal-action-btn h3 {
  font-size: 0.875rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 0.25rem 0;
}

.modal-action-btn p {
  font-size: 0.75rem;
  color: #6b7280;
  margin: 0;
  line-height: 1.4;
}

/* Pagination Styles */
.pagination-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.5rem;
  border-top: 1px solid #e5e7eb;
  margin-top: 1rem;
}

.pagination-info {
  font-size: 0.875rem;
  color: #6b7280;
}

.pagination-controls {
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

.pagination-btn {
  padding: 0.5rem 0.75rem;
  border: 1px solid #e5e7eb;
  background: white;
  color: #374151;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.pagination-btn:hover:not(:disabled) {
  background: #f9fafb;
  border-color: #d1d5db;
}

.pagination-btn.active {
  background: var(--primary-gradient);
  color: white;
  border-color: transparent;
}

.pagination-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Modal Responsive */
@media (max-width: 768px) {
  .modal-content {
    margin: 1rem;
    max-width: calc(100vw - 2rem);
  }

  .modal-actions {
    gap: 0.75rem;
  }

  .modal-action-btn {
    padding: 0.75rem;
  }

  .pagination-container {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }

  .pagination-controls {
    justify-content: center;
  }
}
