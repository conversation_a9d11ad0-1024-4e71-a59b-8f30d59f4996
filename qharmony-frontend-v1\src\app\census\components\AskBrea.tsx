import { useState } from "react";
import { createPortal } from "react-dom";
import dynamic from "next/dynamic";
import { Button } from "./ui/button";
import { MessageCircle } from "lucide-react";

// Dynamically import ChatModal to avoid build issues
const ChatModal = dynamic(() => import("../../ai-enroller/employee-enrol/components/ChatModal"), {
  ssr: false
});

interface AskBreaProps {
  context?: string; // Context about what page/data the user is viewing
  size?: "sm" | "default" | "lg";
  variant?: "default" | "outline" | "ghost";
}

const AskBrea = ({ context = "", size = "default", variant = "default" }: AskBreaProps) => {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <>
      <Button
        size={size}
        variant={variant}
        className="flex items-center gap-2"
        onClick={() => setIsOpen(true)}
      >
        <MessageCircle className="h-4 w-4" />
        Ask <PERSON><PERSON>
      </Button>

      {isOpen && typeof document !== 'undefined' && createPortal(
        <div style={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          zIndex: 99999,
          pointerEvents: 'auto'
        }}>
          <ChatModal
            isOpen={isOpen}
            onClose={() => setIsOpen(false)}
          />
        </div>,
        document.body
      )}
    </>
  );
};

export default AskBrea;