
import { useState } from "react";
import { Button } from "./ui/button";
import { MessageCircle } from "lucide-react";
import ChatModal from "../../ai-enroller/employee-enrol/components/ChatModal";

interface AskBreaProps {
  context?: string; // Context about what page/data the user is viewing
  size?: "sm" | "default" | "lg";
  variant?: "default" | "outline" | "ghost";
}

const AskBrea = ({ context = "", size = "default", variant = "default" }: AskBreaProps) => {
  const [isOpen, setIsOpen] = useState(false);

  const handleAskQuestion = async () => {
    if (!question.trim()) {
      toast({
        title: "Question Required",
        description: "Please enter a question to ask <PERSON><PERSON>.",
        variant: "destructive",
      });
      return;
    }

    setIsLoading(true);
    
    try {
      // Simulate AI response - in a real app this would call an AI API
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      const mockResponses = [
        `Based on the ${context}, I recommend focusing on comprehensive coverage options that balance cost and benefits. The data suggests that a PPO + HSA combination would provide optimal value for most employees while maintaining flexibility.`,
        `Looking at the employee demographics and risk factors, I'd suggest prioritizing dental and disability coverage as key upsell opportunities. These gaps represent significant value-add potential for both the broker and the employer.`,
        `The risk analysis indicates that this group would benefit from enhanced preventive care programs. Consider wellness initiatives that could reduce overall claims costs while improving employee satisfaction.`,
        `Based on the coverage patterns I'm seeing, there's an opportunity to optimize the plan mix. Consider offering tiered options that allow employees to customize their benefits based on individual needs and family situations.`
      ];
      
      const randomResponse = mockResponses[Math.floor(Math.random() * mockResponses.length)];
      setResponse(randomResponse);
      
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to get response from Brea. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleClose = () => {
    setIsOpen(false);
    setQuestion("");
    setResponse("");
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button size={size} variant={variant} className="flex items-center gap-2">
          <MessageCircle className="h-4 w-4" />
          Ask Brea
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <div className="bg-gradient-to-r from-blue-600 to-purple-600 p-2 rounded-lg">
              <MessageCircle className="h-5 w-5 text-white" />
            </div>
            Ask Brea AI
          </DialogTitle>
          <DialogDescription>
            Ask Brea any questions about benefits, coverage recommendations, or insights based on the current data.
          </DialogDescription>
        </DialogHeader>
        
        <div className="space-y-4">
          <div>
            <label className="text-sm font-medium text-gray-700 mb-2 block">Your Question</label>
            <Input
              placeholder="e.g., What coverage gaps should I prioritize for this client?"
              value={question}
              onChange={(e) => setQuestion(e.target.value)}
              onKeyPress={(e) => e.key === 'Enter' && !isLoading && handleAskQuestion()}
            />
          </div>
          
          {response && (
            <div className="bg-gradient-to-r from-blue-50 to-purple-50 p-4 rounded-lg border-l-4 border-blue-500">
              <h4 className="text-sm font-semibold text-gray-900 mb-2 flex items-center">
                <MessageCircle className="h-4 w-4 mr-2 text-blue-600" />
                Brea&apos;s Response:
              </h4>
              <p className="text-sm text-gray-700">{response}</p>
            </div>
          )}
        </div>
        
        <DialogFooter className="flex justify-between">
          <Button variant="outline" onClick={handleClose}>
            Close
          </Button>
          <Button onClick={handleAskQuestion} disabled={isLoading}>
            {isLoading ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Asking Brea...
              </>
            ) : (
              <>
                <Send className="h-4 w-4 mr-2" />
                Ask Question
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default AskBrea;
