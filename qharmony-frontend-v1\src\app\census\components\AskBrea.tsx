import { useState } from "react";
import { createPortal } from "react-dom";
import { Button } from "./ui/button";
import { MessageCircle } from "lucide-react";
import ChatModal from "../../ai-enroller/employee-enrol/components/ChatModal";

interface AskBreaProps {
  context?: string; // Context about what page/data the user is viewing
  size?: "sm" | "default" | "lg";
  variant?: "default" | "outline" | "ghost";
}

const AskBrea = ({ context = "", size = "default", variant = "default" }: AskBreaProps) => {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <>
      <Button
        size={size}
        variant={variant}
        className="flex items-center gap-2"
        onClick={() => setIsOpen(true)}
      >
        <MessageCircle className="h-4 w-4" />
        Ask Brea
      </Button>

      {isOpen && typeof document !== 'undefined' && createPortal(
        <div style={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          zIndex: 99999,
          pointerEvents: 'auto'
        }}>
          <ChatModal
            isOpen={isOpen}
            onClose={() => setIsOpen(false)}
          />
        </div>,
        document.body
      )}
    </>
  );
};

export default AskBrea;