'use client';

import { useState, createContext, useContext } from 'react';
import { useSearchParams, useRouter } from 'next/navigation';
import NavigationProvider from './NavigationProvider';
// Import from public directory (where pages are now located)
import Index from '../public/Index';
import BrokerDashboard from '../public/BrokerDashboard';
import EmployerInsight from '../public/EmployerInsight';
import HRInsight from '../public/HRInsight';
import UploadCensus from '../public/UploadCensus';
import HRUpload from '../public/HRUpload';
import EmployerInvite from '../public/EmployerInvite';
import PreviewReport from '../public/PreviewReport';
import Processing from '../public/Processing';
import Pricing from '../public/Pricing';
import Billing from '../public/Billing';
import GenerateProposal from '../public/GenerateProposal';
import LoginPrompt from '../public/LoginPrompt';
import NotFound from '../public/NotFound';
import FilePreview from '../public/FilePreview';

export default function EmpCensusApp() {
  const searchParams = useSearchParams();
  const pageParam = searchParams.get('page') || 'home';
  const id = searchParams.get('id');

  // Handle page parameters that might include IDs (like employer-insight/1)
  const [page, pageId] = pageParam.includes('/') ? pageParam.split('/') : [pageParam, null];

  const renderPage = () => {
    switch (page) {
      case 'home':
      case '':
        return <Index />;
      case 'dashboard':
        return <BrokerDashboard />;
      case 'employer-insight':
        return <EmployerInsight />;
      case 'hr-insight':
        return <HRInsight />;
      case 'upload-census':
        return <UploadCensus />;
      case 'file-preview':
        return <FilePreview />;
      case 'hr-upload':
        return <HRUpload />;
      case 'employer-invite':
        return <EmployerInvite />;
      case 'preview-report':
        return <PreviewReport />;
      case 'processing':
        return <Processing />;
      case 'generate-proposal':
        return <GenerateProposal />;
      case 'pricing':
        return <Pricing />;
      case 'billing':
        return <Billing />;
      case 'login-prompt':
        return <LoginPrompt />;
      default:
        return <NotFound />;
    }
  };

  return (
    <NavigationProvider>
      <div>
        {renderPage()}
      </div>
    </NavigationProvider>
  );
}
