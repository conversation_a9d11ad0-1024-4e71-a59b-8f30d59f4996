
import { useState } from "react";
import { Button } from "./ui/button";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger, DropdownMenuSeparator } from "./ui/dropdown-menu";
import { useNavigate } from "../lib/react-router-dom";
import { ChevronDown, Menu } from "lucide-react";
import { navItems } from "../nav-items";

interface NavigationDropdownProps {
  variant?: "default" | "outline" | "ghost";
  size?: "sm" | "default" | "lg";
}

const NavigationDropdown = ({ variant = "outline", size = "sm" }: NavigationDropdownProps) => {
  const navigate = useNavigate();
  const [isOpen, setIsOpen] = useState(false);

  // Filter out special routes like "*" (404) and "/" (home) but include login-prompt
  const mainPages = navItems.filter(item => 
    !["*", "/"].includes(item.to)
  );

  const handleNavigate = (path: string) => {
    // Convert path to query parameter format
    const page = path.startsWith('/') ? path.substring(1) : path;
    navigate(`?page=${page}`);
    setIsOpen(false);
  };

  return (
    <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>
      <DropdownMenuTrigger asChild>
        <Button variant={variant} size={size} className="flex items-center gap-2">
          <Menu className="h-4 w-4" />
          Pages
          <ChevronDown className="h-4 w-4" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent
        className="w-56 bg-white dark:bg-gray-800 border shadow-lg z-50"
        align="end"
        sideOffset={5}
      >
        {/* Dashboard & Analytics */}
        <DropdownMenuItem
          onClick={() => handleNavigate('/dashboard')}
          className="flex items-center gap-2 hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer"
        >
          {navItems.find(item => item.to === '/dashboard')?.icon}
          Dashboard
        </DropdownMenuItem>

        <DropdownMenuSeparator />

        {/* HR Insight */}
        <DropdownMenuItem
          onClick={() => handleNavigate('/hr-insight')}
          className="flex items-center gap-2 hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer"
        >
          {navItems.find(item => item.to === '/hr-insight')?.icon}
          HR Insight
        </DropdownMenuItem>

        <DropdownMenuSeparator />

        {/* Upload & Management */}
        <DropdownMenuItem
          onClick={() => handleNavigate('/upload-census')}
          className="flex items-center gap-2 hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer"
        >
          {navItems.find(item => item.to === '/upload-census')?.icon}
          Upload Census
        </DropdownMenuItem>

        <DropdownMenuItem
          onClick={() => handleNavigate('/file-preview')}
          className="flex items-center gap-2 hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer"
        >
          {navItems.find(item => item.to === '/file-preview')?.icon}
          File Preview
        </DropdownMenuItem>

        <DropdownMenuItem
          onClick={() => handleNavigate('/hr-upload')}
          className="flex items-center gap-2 hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer"
        >
          {navItems.find(item => item.to === '/hr-upload')?.icon}
          HR Upload
        </DropdownMenuItem>

        <DropdownMenuItem
          onClick={() => handleNavigate('/employer-invite')}
          className="flex items-center gap-2 hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer"
        >
          {navItems.find(item => item.to === '/employer-invite')?.icon}
          Invite Employer
        </DropdownMenuItem>

        <DropdownMenuSeparator />

        {/* Reports & Tools */}
        <DropdownMenuItem
          onClick={() => handleNavigate('/preview-report')}
          className="flex items-center gap-2 hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer"
        >
          {navItems.find(item => item.to === '/preview-report')?.icon}
          Preview Report
        </DropdownMenuItem>

        <DropdownMenuItem
          onClick={() => handleNavigate('/generate-proposal/1')}
          className="flex items-center gap-2 hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer"
        >
          {navItems.find(item => item.to === '/generate-proposal')?.icon}
          Generate Proposal
        </DropdownMenuItem>

        <DropdownMenuItem
          onClick={() => handleNavigate('/processing')}
          className="flex items-center gap-2 hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer"
        >
          {navItems.find(item => item.to === '/processing')?.icon}
          Processing
        </DropdownMenuItem>

        <DropdownMenuSeparator />

        {/* Authentication & Account */}
        <DropdownMenuItem
          onClick={() => handleNavigate('/login-prompt')}
          className="flex items-center gap-2 hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer"
        >
          {navItems.find(item => item.to === '/login-prompt')?.icon}
          Magic Link Sign In
        </DropdownMenuItem>

        <DropdownMenuItem
          onClick={() => handleNavigate('/pricing')}
          className="flex items-center gap-2 hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer"
        >
          {navItems.find(item => item.to === '/pricing')?.icon}
          Pricing
        </DropdownMenuItem>

        <DropdownMenuItem
          onClick={() => handleNavigate('/billing')}
          className="flex items-center gap-2 hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer"
        >
          {navItems.find(item => item.to === '/billing')?.icon}
          Billing & Subscriptions
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export default NavigationDropdown;
