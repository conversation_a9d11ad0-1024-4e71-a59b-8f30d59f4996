'use client';

import React, { createContext, useContext, ReactNode } from 'react';
import { useRouter } from 'next/navigation';

// Create a context for navigation
const NavigationContext = createContext<{
  navigate: (path: string) => void;
} | null>(null);

// Custom hook to replace react-router-dom's useNavigate
export const useNavigate = () => {
  const context = useContext(NavigationContext);
  if (!context) {
    throw new Error('useNavigate must be used within NavigationProvider');
  }
  return context.navigate;
};

interface NavigationProviderProps {
  children: ReactNode;
}

const NavigationProvider: React.FC<NavigationProviderProps> = ({ children }) => {
  const router = useRouter();

  const navigate = (path: string) => {
    // Handle query parameter paths (already formatted)
    if (path.startsWith('?page=')) {
      const newUrl = `/census${path}`;
      router.push(newUrl);
      return;
    }

    // Handle absolute paths
    if (path.startsWith('/')) {
      path = path.substring(1);
    }

    // Convert react-router paths to Next.js query params
    const newUrl = `/census?page=${path}`;
    router.push(newUrl);
  };

  return (
    <NavigationContext.Provider value={{ navigate }}>
      {children}
    </NavigationContext.Provider>
  );
};

export default NavigationProvider;
