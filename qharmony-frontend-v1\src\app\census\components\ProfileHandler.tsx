'use client';

import { useState, useEffect } from 'react';
import { useSelector } from 'react-redux';
import { Button } from './ui/button';
import { Avatar, AvatarFallback, AvatarImage } from './ui/avatar';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger, DropdownMenuSeparator } from './ui/dropdown-menu';
import { User, Settings, LogOut, ChevronDown } from 'lucide-react';
import { RootState } from '@/redux/store';
import { useNavigate } from '../lib/react-router-dom';

// Dynamically import the main dashboard profile popup to avoid circular dependencies
import dynamic from 'next/dynamic';

const EnhancedEditUserProfilePopup = dynamic(() => import('../../dashboard/enhanced_edit_user_profile_popup'), {
  ssr: false
});

interface ProfileHandlerProps {
  className?: string;
}

const ProfileHandler = ({ className = "" }: ProfileHandlerProps) => {
  const navigate = useNavigate();
  const [isProfileOpen, setIsProfileOpen] = useState(false);
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const [userName, setUserName] = useState('');
  const [userEmail, setUserEmail] = useState('');

  // Get user data from Redux store
  const userProfile = useSelector((state: RootState) => state.user.userProfile);
  const userId = useSelector((state: RootState) => state.user._id);

  useEffect(() => {
    // Check if user is logged in by checking localStorage and Redux state
    const storedUserId = localStorage.getItem('userid1') || localStorage.getItem('userId');
    const hasUserProfile = userProfile && (userProfile.name || userProfile.email);
    
    if (storedUserId && hasUserProfile) {
      setIsLoggedIn(true);
      setUserName(userProfile.name || 'User');
      setUserEmail(userProfile.email || '');
    } else if (storedUserId) {
      // User ID exists but no profile data, still consider logged in
      setIsLoggedIn(true);
      setUserName('User');
      setUserEmail('');
    } else {
      setIsLoggedIn(false);
    }
  }, [userProfile, userId]);

  const handleLogin = () => {
    navigate('?page=login-prompt');
  };

  const handleProfileEdit = () => {
    setIsProfileOpen(true);
  };

  const handleLogout = () => {
    // Clear localStorage
    localStorage.removeItem('userid1');
    localStorage.removeItem('userId');
    localStorage.removeItem('companyId1');
    localStorage.removeItem('companyId');
    
    // Redirect to login
    navigate('?page=login-prompt');
    
    // Reload to clear Redux state
    window.location.reload();
  };

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  if (!isLoggedIn) {
    return (
      <Button 
        onClick={handleLogin}
        className={`bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white ${className}`}
      >
        <User className="mr-2 h-4 w-4" />
        Sign In
      </Button>
    );
  }

  return (
    <>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" className={`flex items-center gap-2 ${className}`}>
            <Avatar className="h-8 w-8">
              <AvatarImage src="" alt={userName} />
              <AvatarFallback className="bg-gradient-to-r from-blue-600 to-purple-600 text-white text-sm">
                {getInitials(userName)}
              </AvatarFallback>
            </Avatar>
            <div className="flex flex-col items-start">
              <span className="text-sm font-medium">{userName}</span>
              {userEmail && (
                <span className="text-xs text-gray-500">{userEmail}</span>
              )}
            </div>
            <ChevronDown className="h-4 w-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent 
          className="w-56 bg-white dark:bg-gray-800 border shadow-lg z-50" 
          align="end"
          sideOffset={5}
        >
          <DropdownMenuItem 
            onClick={handleProfileEdit}
            className="flex items-center gap-2 hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer"
          >
            <Settings className="h-4 w-4" />
            Edit Profile
          </DropdownMenuItem>
          
          <DropdownMenuSeparator />
          
          <DropdownMenuItem 
            onClick={handleLogout}
            className="flex items-center gap-2 hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer text-red-600"
          >
            <LogOut className="h-4 w-4" />
            Sign Out
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>

      {/* Profile Edit Modal */}
      {isProfileOpen && (
        <EnhancedEditUserProfilePopup
          open={isProfileOpen}
          onClose={() => setIsProfileOpen(false)}
        />
      )}
    </>
  );
};

export default ProfileHandler;
