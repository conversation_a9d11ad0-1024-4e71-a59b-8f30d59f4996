
import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>er, <PERSON>alogTitle } from "./ui/dialog";
import { Button } from "./ui/button";
import { Input } from "./ui/input";
import { Avatar, AvatarFallback, AvatarImage } from "./ui/avatar";
import { Textarea } from "./ui/textarea";
import { Label } from "./ui/label";
import { X, Camera, User, Linkedin, Link, Heart, Plus } from "lucide-react";

interface ProfileSettingsModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const ProfileSettingsModal = ({ isOpen, onClose }: ProfileSettingsModalProps) => {
  const [fullName, setFullName] = useState("John Doe");
  const [linkedinProfile, setLinkedinProfile] = useState("https://linkedin.com/in/yourprofile");
  const [additionalLinks, setAdditionalLinks] = useState(["https://example.com"]);
  const [hobbies, setHobbies] = useState("Tell us about your hobbies and interests...");

  const handleAddLink = () => {
    setAdditionalLinks([...additionalLinks, ""]);
  };

  const handleLinkChange = (index: number, value: string) => {
    const newLinks = [...additionalLinks];
    newLinks[index] = value;
    setAdditionalLinks(newLinks);
  };

  const handleSave = () => {
    console.log("Saving profile settings...");
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md max-h-[90vh] overflow-y-auto">
        <DialogHeader className="flex flex-row items-center justify-between space-y-0 pb-4">
          <DialogTitle className="text-lg font-semibold">Profile Settings</DialogTitle>
          <Button
            variant="ghost"
            size="icon"
            onClick={onClose}
            className="h-6 w-6 rounded-full"
          >
            <X className="h-4 w-4" />
          </Button>
        </DialogHeader>

        <div className="space-y-6">
          {/* Profile Picture Section */}
          <div className="flex flex-col items-center space-y-3">
            <div className="relative">
              <Avatar className="h-20 w-20">
                <AvatarImage src="" />
                <AvatarFallback className="bg-gradient-to-r from-blue-600 to-purple-600 text-white text-xl">
                  <User className="h-8 w-8" />
                </AvatarFallback>
              </Avatar>
              <Button
                size="icon"
                variant="outline"
                className="absolute -bottom-1 -right-1 h-8 w-8 rounded-full bg-white border-2"
              >
                <Camera className="h-4 w-4" />
              </Button>
            </div>
            <p className="text-sm text-gray-500">Click the camera to upload a new photo</p>
          </div>

          {/* Full Name */}
          <div className="space-y-2">
            <Label htmlFor="fullName" className="flex items-center space-x-2">
              <User className="h-4 w-4" />
              <span>Full Name</span>
            </Label>
            <Input
              id="fullName"
              value={fullName}
              onChange={(e) => setFullName(e.target.value)}
              placeholder="Enter your full name"
            />
          </div>

          {/* LinkedIn Profile */}
          <div className="space-y-2">
            <Label htmlFor="linkedin" className="flex items-center space-x-2">
              <Linkedin className="h-4 w-4" />
              <span>LinkedIn Profile</span>
            </Label>
            <Input
              id="linkedin"
              value={linkedinProfile}
              onChange={(e) => setLinkedinProfile(e.target.value)}
              placeholder="https://linkedin.com/in/yourprofile"
            />
          </div>

          {/* Additional Links */}
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <Label className="flex items-center space-x-2">
                <Link className="h-4 w-4" />
                <span>Additional Links</span>
              </Label>
              <Button
                variant="outline"
                size="sm"
                onClick={handleAddLink}
                className="h-8 px-2"
              >
                <Plus className="h-3 w-3 mr-1" />
                Add Link
              </Button>
            </div>
            <div className="space-y-2">
              {additionalLinks.map((link, index) => (
                <Input
                  key={index}
                  value={link}
                  onChange={(e) => handleLinkChange(index, e.target.value)}
                  placeholder="https://example.com"
                />
              ))}
            </div>
          </div>

          {/* Hobbies & Interests */}
          <div className="space-y-2">
            <Label htmlFor="hobbies" className="flex items-center space-x-2">
              <Heart className="h-4 w-4" />
              <span>Hobbies & Interests</span>
            </Label>
            <Textarea
              id="hobbies"
              value={hobbies}
              onChange={(e) => setHobbies(e.target.value)}
              placeholder="Tell us about your hobbies and interests..."
              className="min-h-[80px] resize-none"
            />
          </div>

          {/* Save Button */}
          <Button
            onClick={handleSave}
            className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white"
          >
            Save Changes
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default ProfileSettingsModal;
