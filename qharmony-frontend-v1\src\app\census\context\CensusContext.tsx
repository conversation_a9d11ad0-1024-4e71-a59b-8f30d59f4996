'use client';

/**
 * Census Context - Frontend State Management
 *
 * NOTE: This is temporary frontend state management.
 * In the future, this will be replaced with backend state management
 * where processed census data will be stored in the database and
 * retrieved via API calls instead of local state.
 *
 * Future Migration Plan:
 * 1. Backend will store processed census results in database
 * 2. API endpoints will provide company listing and individual company data
 * 3. This context will be simplified to just handle loading states
 * 4. Data persistence will be handled by the backend
 */

import React, { createContext, useContext, useState, useCallback } from 'react';
import CensusApiService from '../services/censusApi';

interface UpsellOpportunity {
  category: string;
  description: string;
  savings: string;
  confidence: string;
  priority: string;
}

interface EmployeeProfile {
  name: string;
  department: string;
  risk: 'Low' | 'Medium' | 'High';
  age: number;
  coverage: string;
  hasDependents: boolean;
  salary?: string;
  currentPlan?: Record<string, string>;
  coverageGaps?: any[];
  insights: string[];
  upsells: any[];
  planFitSummary?: any;
  apiData?: any;
}

interface CensusCompany {
  id: string;
  companyName: string;
  employees: number;
  averageAge: number;
  dependents: number;
  planType: string;
  potentialSavings: string;
  riskScore: string;
  uploadDate: string;
  status: 'processing' | 'analyzed' | 'error';
  industry?: string;
  currentSpend?: string;
  suggestedPlan?: string;
  planFitSummary?: any;
  employeeProfiles?: EmployeeProfile[];
  upsellOpportunities?: UpsellOpportunity[];
  apiData?: any;
}

interface CensusContextType {
  // State
  companies: CensusCompany[];
  currentCompany: CensusCompany | null;
  isLoading: boolean;
  error: string | null;
  
  // Actions
  uploadCensusFile: (file: File) => Promise<string>;
  getCompanyData: (companyId: string) => CensusCompany | null;
  refreshDashboard: () => Promise<void>;
  clearError: () => void;
  setCurrentCompany: (company: CensusCompany | null) => void;
}

const CensusContext = createContext<CensusContextType | undefined>(undefined);

export const useCensus = () => {
  const context = useContext(CensusContext);
  if (context === undefined) {
    throw new Error('useCensus must be used within a CensusProvider');
  }
  return context;
};

interface CensusProviderProps {
  children: React.ReactNode;
}

export const CensusProvider: React.FC<CensusProviderProps> = ({ children }) => {
  const [companies, setCompanies] = useState<CensusCompany[]>([]);
  const [currentCompany, setCurrentCompany] = useState<CensusCompany | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const clearError = useCallback(() => {
    setError(null);
  }, []);

  const uploadCensusFile = useCallback(async (file: File): Promise<string> => {
    setIsLoading(true);
    setError(null);
    
    try {
      console.log('📤 Starting census file upload...');
      
      // Upload and process the file
      const response: any = await CensusApiService.uploadCensusFile(file, false);

      console.log('🔍 Raw API response in context:', response);

      if (!response.success) {
        throw new Error(response.message || 'Census processing failed');
      }

      // Generate a unique company ID (in real app, this would come from backend)
      const companyId = `company_${Date.now()}`;
      
      // Transform API response to frontend format
      const transformedCompany = CensusApiService.transformCompanyData(response, companyId);
      
      // Create company record
      const newCompany: CensusCompany = {
        id: companyId,
        ...transformedCompany,
        status: 'analyzed'
      };

      // Add to companies list
      setCompanies(prev => [newCompany, ...prev]);
      setCurrentCompany(newCompany);

      // Debug logging
      console.log('📊 Company data stored:', {
        companyId: newCompany.id,
        employeeProfilesCount: newCompany.employeeProfiles?.length || 0,
        hasEmployeeProfiles: !!newCompany.employeeProfiles,
        sampleEmployee: newCompany.employeeProfiles?.[0]
      });
      
      console.log('✅ Census file processed successfully:', newCompany);
      
      return companyId;
    } catch (err: any) {
      let errorMessage = err.message || 'Failed to process census file';

      // Add helpful instructions for common errors
      if (errorMessage.includes('Census API service is not running')) {
        errorMessage += '\n\nTo start the Python backend:\n1. Navigate to qharmony-bot directory\n2. Run: python server.py\n3. Ensure it starts on port 8000';
      } else if (errorMessage.includes('preprocessing_failed')) {
        errorMessage = 'Census file preprocessing failed. The file format may need adjustment. Please ensure:\n• Column names are standard (First Name, Last Name, DOB, etc.)\n• Data values are consistent\n• File is in CSV format';
      } else if (errorMessage.includes('Processing failed')) {
        errorMessage = 'Census processing encountered an error. Please check the file format and try again.';
      }

      setError(errorMessage);
      console.error('❌ Census upload failed:', err);
      throw new Error(errorMessage);
    } finally {
      setIsLoading(false);
    }
  }, []);

  const getCompanyData = useCallback((companyId: string): CensusCompany | null => {
    const foundCompany = companies.find(company => company.id === companyId) || null;

    console.log('🔍 getCompanyData called:', {
      requestedId: companyId,
      availableCompanies: companies.map(c => ({ id: c.id, employeeCount: c.employeeProfiles?.length || 0 })),
      foundCompany: !!foundCompany,
      foundCompanyEmployees: foundCompany?.employeeProfiles?.length || 0
    });

    return foundCompany;
  }, [companies]);

  const refreshDashboard = useCallback(async () => {
    setIsLoading(true);
    setError(null);
    
    try {
      // In a real implementation, this would fetch from backend
      // For now, we'll keep the existing companies
      console.log('📊 Dashboard refreshed');
    } catch (err: any) {
      setError(err.message || 'Failed to refresh dashboard');
      console.error('❌ Dashboard refresh failed:', err);
    } finally {
      setIsLoading(false);
    }
  }, []);

  const value: CensusContextType = {
    // State
    companies,
    currentCompany,
    isLoading,
    error,
    
    // Actions
    uploadCensusFile,
    getCompanyData,
    refreshDashboard,
    clearError,
    setCurrentCompany,
  };

  return (
    <CensusContext.Provider value={value}>
      {children}
    </CensusContext.Provider>
  );
};

export default CensusProvider;
