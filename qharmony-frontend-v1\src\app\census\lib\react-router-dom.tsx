// Compatibility layer for react-router-dom
// This file provides the same API as react-router-dom but uses Next.js navigation

'use client';

import React from 'react';
import { useSearchParams as useNextSearchParams } from 'next/navigation';
import { useNavigate as useNextNavigate } from '../components/NavigationProvider';

// Export the useNavigate hook
export const useNavigate = useNextNavigate;

// Export useParams hook (for route parameters)
export const useParams = () => {
  const searchParams = useNextSearchParams();
  const id = searchParams.get('id');
  
  return { id };
};

// Export useSearchParams (for query parameters)
export const useSearchParams = () => {
  const nextSearchParams = useNextSearchParams();
  return {
    get: (key: string) => nextSearchParams.get(key),
    has: (key: string) => nextSearchParams.has(key),
    toString: () => nextSearchParams.toString(),
    // Add other methods as needed
  };
};

// Mock components that aren't needed in Next.js App Router
export const BrowserRouter: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  return <>{children}</>;
};

export const Routes: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  return <>{children}</>;
};

export const Route: React.FC<{ element: React.ReactNode; path?: string }> = ({ element }) => {
  return <>{element}</>;
};
