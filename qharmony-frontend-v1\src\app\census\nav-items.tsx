
import { HomeIcon, Building2, <PERSON>, FileText, <PERSON>tings, BarChart3, Upload, CreditCard, PlusCircle } from "lucide-react";
import Index from "./public/Index";
import BrokerDashboard from "./public/BrokerDashboard";
import EmployerInsight from "./public/EmployerInsight";
import HRInsight from "./public/HRInsight";
import UploadCensus from "./public/UploadCensus";
import HRUpload from "./public/HRUpload";
import EmployerInvite from "./public/EmployerInvite";
import PreviewReport from "./public/PreviewReport";
import Processing from "./public/Processing";
import Pricing from "./public/Pricing";
import Billing from "./public/Billing";
import GenerateProposal from "./public/GenerateProposal";
import LoginPrompt from "./public/LoginPrompt";
import NotFound from "./public/NotFound";

export const navItems = [
  {
    title: "Home",
    to: "/",
    icon: <HomeIcon className="h-4 w-4" />,
    page: <Index />,
  },
  {
    title: "Dashboard",
    to: "/dashboard",
    icon: <BarChart3 className="h-4 w-4" />,
    page: <BrokerDashboard />,
  },
  {
    title: "Employer Insight",
    to: "/employer-insight",
    icon: <Building2 className="h-4 w-4" />,
    page: <EmployerInsight />,
  },
  {
    title: "HR Insight",
    to: "/hr-insight",
    icon: <Users className="h-4 w-4" />,
    page: <HRInsight />,
  },
  {
    title: "Upload Census",
    to: "/upload-census",
    icon: <Upload className="h-4 w-4" />,
    page: <UploadCensus />,
  },
  {
    title: "HR Upload",
    to: "/hr-upload",
    icon: <Upload className="h-4 w-4" />,
    page: <HRUpload />,
  },
  {
    title: "Employer Invite",
    to: "/employer-invite",
    icon: <Users className="h-4 w-4" />,
    page: <EmployerInvite />,
  },
  {
    title: "Preview Report",
    to: "/preview-report",
    icon: <FileText className="h-4 w-4" />,
    page: <PreviewReport />,
  },
  {
    title: "Processing",
    to: "/processing",
    icon: <Settings className="h-4 w-4" />,
    page: <Processing />,
  },
  {
    title: "Generate Proposal",
    to: "/generate-proposal",
    icon: <PlusCircle className="h-4 w-4" />,
    page: <GenerateProposal />,
  },
  {
    title: "Pricing",
    to: "/pricing",
    icon: <BarChart3 className="h-4 w-4" />,
    page: <Pricing />,
  },
  {
    title: "Billing",
    to: "/billing",
    icon: <CreditCard className="h-4 w-4" />,
    page: <Billing />,
  },
  {
    title: "Login",
    to: "/login-prompt",
    icon: <Users className="h-4 w-4" />,
    page: <LoginPrompt />,
  },
  {
    title: "Not Found",
    to: "*",
    icon: <FileText className="h-4 w-4" />,
    page: <NotFound />,
  },
];
