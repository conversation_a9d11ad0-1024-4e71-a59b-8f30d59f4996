'use client';

import dynamic from 'next/dynamic';
import { Suspense } from "react";
import { TooltipProvider } from "./components/ui/tooltip";
import { Toaster } from "./components/ui/toaster";

// Dynamically import EmpCensusApp to avoid SSR issues
const EmpCensusApp = dynamic(() => import("./components/EmpCensusApp"), {
  ssr: false,
  loading: () => <CensusLoading />
});

// Loading component for Suspense fallback
function CensusLoading() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 flex items-center justify-center">
      <div className="text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
        <p className="text-gray-600">Loading census application...</p>
      </div>
    </div>
  );
}

export default function CensusPage() {
  return (
    <div data-page="census">
      <TooltipProvider>
        <Toaster />
        <EmpCensusApp />
      </TooltipProvider>
    </div>
  );
}
