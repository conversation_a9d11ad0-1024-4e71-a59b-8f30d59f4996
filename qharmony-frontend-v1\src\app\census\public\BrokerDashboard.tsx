import { useState } from "react";
import { But<PERSON> } from "../components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "../components/ui/card";
import { Input } from "../components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "../components/ui/select";
import { useNavigate } from "../lib/react-router-dom";
import { Plus, Users, DollarSign, TrendingUp, Share2, Mail, Search, Lock, AlertTriangle } from "lucide-react";
import AskBrea from "../components/AskBrea";
// import NavigationDropdown from "../components/NavigationDropdown"; // Temporarily removed to fix circular import
import ProfileHandler from "../components/ProfileHandler";
import { usePlanRestrictions } from "../hooks/usePlanRestrictions";
import { useToast } from "../components/ui/use-toast";

const BrokerDashboard = () => {
  const navigate = useNavigate();
  const { toast } = useToast();
  const [searchTerm, setSearchTerm] = useState("");
  const [employeeCountFilter, setEmployeeCountFilter] = useState("all");
  const [riskScoreFilter, setRiskScoreFilter] = useState("all");
  const [suggestedPlanFilter, setSuggestedPlanFilter] = useState("all");

  
  const { canViewReport, reportsRemaining, isAtLimit, trackReportView } = usePlanRestrictions();

  // Mock data for multiple employers
  const employerInsights = [
    {
      id: "1",
      companyName: "TechCorp Solutions",
      employees: 43,
      averageAge: 36,
      dependents: 1.3,
      planType: "PPO + HSA Combo",
      potentialSavings: "$127,500",
      riskScore: 6.2,
      uploadDate: "2024-01-15",
      status: "analyzed"
    },
    {
      id: "2", 
      companyName: "Green Manufacturing",
      employees: 87,
      averageAge: 42,
      dependents: 1.8,
      planType: "Traditional PPO",
      potentialSavings: "$245,000",
      riskScore: 7.1,
      uploadDate: "2024-01-10",
      status: "analyzed"
    },
    {
      id: "3",
      companyName: "StartupXYZ",
      employees: 18,
      averageAge: 29,
      dependents: 0.8,
      planType: "HSA Only",
      potentialSavings: "$32,400",
      riskScore: 4.5,
      uploadDate: "2024-01-18",
      status: "processing"
    }
  ];

  // Filter logic
  const filteredEmployers = employerInsights.filter(employer => {
    // Search filter
    if (searchTerm && !employer.companyName.toLowerCase().includes(searchTerm.toLowerCase())) {
      return false;
    }

    // Employee count filter
    if (employeeCountFilter !== "all") {
      if (employeeCountFilter === "1-50" && employer.employees > 50) return false;
      if (employeeCountFilter === "51-100" && (employer.employees < 51 || employer.employees > 100)) return false;
      if (employeeCountFilter === "101+" && employer.employees <= 100) return false;
    }

    // Risk score filter
    if (riskScoreFilter !== "all") {
      if (riskScoreFilter === "low" && employer.riskScore >= 5) return false;
      if (riskScoreFilter === "medium" && (employer.riskScore < 5 || employer.riskScore > 7)) return false;
      if (riskScoreFilter === "high" && employer.riskScore <= 7) return false;
    }

    // Suggested plan filter
    if (suggestedPlanFilter !== "all") {
      if (suggestedPlanFilter === "ppo-hsa" && employer.planType !== "PPO + HSA Combo") return false;
      if (suggestedPlanFilter === "traditional-ppo" && employer.planType !== "Traditional PPO") return false;
      if (suggestedPlanFilter === "other" && 
          employer.planType !== "HSA Only" && 
          employer.planType !== "Modern HSA Plus Plan") return false;
    }

    return true;
  });

  const handleShareInsight = (companyName: string) => {
    const shareUrl = `${window.location.origin}/shared-insight/${companyName.toLowerCase().replace(/\s+/g, '-')}`;
    navigator.clipboard.writeText(shareUrl);
    console.log(`Share link copied for ${companyName}`);
  };

  const handleTileClick = (employerId: string, companyName: string) => {
    console.log(`Attempting to view report for ${companyName} (ID: ${employerId})`);
    console.log(`Can view report: ${canViewReport(employerId)}`);
    console.log(`Reports remaining: ${reportsRemaining}`);
    console.log(`Is at limit: ${isAtLimit}`);
    
    if (!canViewReport(employerId)) {
      toast({
        title: "Upgrade Required",
        description: `You've reached your free report limit (2 reports). Upgrade to Pro for unlimited access.`,
        variant: "destructive",
      });
      navigate('/pricing');
      return;
    }
    
    // Track the report view
    trackReportView(employerId);
    navigate(`?page=employer-insight/${employerId}`);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50">
      {/* Clean Header */}
      <header className="border-b bg-white/90 backdrop-blur-sm sticky top-0 z-50">
        <div className="container mx-auto px-3 sm:px-4 py-3">
          <div className="flex justify-between items-center">
            <div className="text-xl sm:text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
              BenOsphere
            </div>
            <div className="flex items-center space-x-2 sm:space-x-3">
              {/* <NavigationDropdown /> */}
              <AskBrea context="broker dashboard with multiple client insights" size="sm" variant="outline" />
              <ProfileHandler className="hidden sm:inline-flex" />
            </div>
          </div>
        </div>
      </header>

      <main className="container mx-auto px-3 sm:px-4 py-4 sm:py-6 max-w-7xl">
        {/* Page Header with Better Organization */}
        <div className="mb-6">
          <div className="flex flex-col lg:flex-row lg:justify-between lg:items-start gap-4">
            <div className="flex-1">
              <h1 className="text-2xl sm:text-3xl font-bold text-gray-900 mb-2">
                📊 Broker Dashboard
              </h1>
              <p className="text-gray-600 text-sm sm:text-base mb-4 lg:mb-0">
                Manage and analyze your client census data
              </p>
            </div>
            
            {/* Action Buttons - Better Organized */}
            <div className="flex flex-col sm:flex-row gap-3">
              <Button 
                size="sm" 
                onClick={() => navigate('/upload-census')} 
                className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700"
              >
                <Plus className="h-4 w-4 mr-2" />
                Upload New Census
              </Button>
              <Button variant="outline" size="sm" onClick={() => navigate('/employer-invite')}>
                <Mail className="h-4 w-4 mr-2" />
                Invite Employer
              </Button>
            </div>
          </div>

          {/* Plan Limit Warning - More Prominent Position */}
          {reportsRemaining <= 1 && (
            <Card className="mt-4 bg-gradient-to-r from-amber-50 to-orange-50 border-amber-200">
              <CardContent className="p-4">
                <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3">
                  <div className="flex items-start space-x-3">
                    <AlertTriangle className="h-5 w-5 text-amber-600 mt-0.5 flex-shrink-0" />
                    <div>
                      <p className="font-medium text-amber-800">
                        {isAtLimit ? "Report limit reached!" : `${reportsRemaining} free report${reportsRemaining === 1 ? '' : 's'} remaining`}
                      </p>
                      <p className="text-sm text-amber-700">
                        {isAtLimit ? "Upgrade to Pro for unlimited reports" : "Upgrade to Pro for unlimited access"}
                      </p>
                    </div>
                  </div>
                  <Button 
                    onClick={() => navigate('/pricing')}
                    className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white flex-shrink-0"
                    size="sm"
                  >
                    Upgrade Now
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}
        </div>

        {/* Search and Filter Section */}
        <Card className="mb-6 shadow-lg border-0">
          <CardContent className="p-4 sm:p-6">
            <div className="flex flex-col gap-4">
              {/* Search Bar */}
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Search by Client Name"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
              
              {/* Filters Row */}
              <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
                <div>
                  <label className="text-sm font-medium text-gray-700 mb-2 block">Employee Count</label>
                  <Select value={employeeCountFilter} onValueChange={setEmployeeCountFilter}>
                    <SelectTrigger>
                      <SelectValue placeholder="All" />
                    </SelectTrigger>
                    <SelectContent className="bg-white">
                      <SelectItem value="all">All</SelectItem>
                      <SelectItem value="1-50">1–50</SelectItem>
                      <SelectItem value="51-100">51–100</SelectItem>
                      <SelectItem value="101+">101+</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <label className="text-sm font-medium text-gray-700 mb-2 block">Risk Score</label>
                  <Select value={riskScoreFilter} onValueChange={setRiskScoreFilter}>
                    <SelectTrigger>
                      <SelectValue placeholder="All" />
                    </SelectTrigger>
                    <SelectContent className="bg-white">
                      <SelectItem value="all">All</SelectItem>
                      <SelectItem value="low">Low (&lt;5)</SelectItem>
                      <SelectItem value="medium">Medium (5–7)</SelectItem>
                      <SelectItem value="high">High (&gt;7)</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <label className="text-sm font-medium text-gray-700 mb-2 block">Suggested Plan</label>
                  <Select value={suggestedPlanFilter} onValueChange={setSuggestedPlanFilter}>
                    <SelectTrigger>
                      <SelectValue placeholder="All" />
                    </SelectTrigger>
                    <SelectContent className="bg-white">
                      <SelectItem value="all">All</SelectItem>
                      <SelectItem value="ppo-hsa">PPO + HSA Combo</SelectItem>
                      <SelectItem value="traditional-ppo">Traditional PPO</SelectItem>
                      <SelectItem value="other">Other</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Compact Summary Cards */}
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4 mb-6">
          <Card className="bg-gradient-to-br from-blue-100 to-blue-200 border-blue-300 hover:shadow-md transition-all duration-200">
            <CardContent className="p-3 sm:p-4 text-center">
              <Users className="h-6 w-6 sm:h-8 sm:w-8 mx-auto mb-1 sm:mb-2 text-blue-600" />
              <p className="text-xs sm:text-sm text-blue-600 font-medium">Total Clients</p>
              <p className="text-xl sm:text-2xl font-bold text-blue-900">{filteredEmployers.length}</p>
            </CardContent>
          </Card>
          
          <Card className="bg-gradient-to-br from-emerald-100 to-teal-200 border-emerald-300 hover:shadow-md transition-all duration-200">
            <CardContent className="p-3 sm:p-4 text-center">
              <Users className="h-6 w-6 sm:h-8 sm:w-8 mx-auto mb-1 sm:mb-2 text-emerald-600" />
              <p className="text-xs sm:text-sm text-emerald-600 font-medium">Total Employees</p>
              <p className="text-xl sm:text-2xl font-bold text-emerald-900">
                {filteredEmployers.reduce((sum, emp) => sum + emp.employees, 0)}
              </p>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-br from-orange-100 to-red-200 border-orange-300 hover:shadow-md transition-all duration-200">
            <CardContent className="p-3 sm:p-4 text-center">
              <DollarSign className="h-6 w-6 sm:h-8 sm:w-8 mx-auto mb-1 sm:mb-2 text-orange-600" />
              <p className="text-xs sm:text-sm text-orange-600 font-medium">Total Potential Savings</p>
              <p className="text-lg sm:text-2xl font-bold text-orange-900">$404,900</p>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-br from-purple-100 to-pink-200 border-purple-300 hover:shadow-md transition-all duration-200">
            <CardContent className="p-3 sm:p-4 text-center">
              <TrendingUp className="h-6 w-6 sm:h-8 sm:w-8 mx-auto mb-1 sm:mb-2 text-purple-600" />
              <p className="text-xs sm:text-sm text-purple-600 font-medium">Avg Risk Score</p>
              <p className="text-xl sm:text-2xl font-bold text-purple-900">5.9/10</p>
            </CardContent>
          </Card>
        </div>

        {/* Compact Employer Insights List */}
        <Card className="shadow-lg border-0">
          <CardHeader className="pb-3 sm:pb-4">
            <CardTitle className="text-lg sm:text-xl">Client Census Insights</CardTitle>
          </CardHeader>
          <CardContent className="p-3 sm:p-6">
            <div className="space-y-3 sm:space-y-4">
              {filteredEmployers.map((employer) => {
                const canAccess = canViewReport(employer.id);
                
                return (
                  <Card 
                    key={employer.id} 
                    className={`border-l-4 border-l-blue-500 transition-all duration-200 cursor-pointer ${
                      canAccess 
                        ? 'hover:shadow-md hover:-translate-y-0.5' 
                        : 'opacity-60 hover:opacity-80 cursor-not-allowed'
                    } ${!canAccess ? 'relative' : ''}`}
                    onClick={() => handleTileClick(employer.id, employer.companyName)}
                  >
                    {!canAccess && (
                      <div className="absolute inset-0 bg-gray-200/50 backdrop-blur-[1px] rounded-lg flex items-center justify-center z-10">
                        <div className="bg-white p-3 rounded-lg shadow-lg flex items-center space-x-2 border border-amber-200">
                          <Lock className="h-4 w-4 text-amber-600" />
                          <span className="text-sm font-medium text-amber-800">Upgrade to view</span>
                        </div>
                      </div>
                    )}
                    
                    <CardContent className="p-4 sm:p-5">
                      <div className="flex flex-col sm:flex-row justify-between items-start mb-3 sm:mb-4 gap-3">
                        <div className="flex-1">
                          <h3 className="text-lg sm:text-xl font-semibold text-gray-900 mb-1">
                            {employer.companyName}
                          </h3>
                          <p className="text-xs sm:text-sm text-gray-500">
                            Uploaded: {new Date(employer.uploadDate).toLocaleDateString()}
                          </p>
                        </div>
                        <div className="flex space-x-2 w-full sm:w-auto">
                          <Button 
                            size="sm" 
                            variant="outline"
                            onClick={(e) => {
                              e.stopPropagation();
                              if (canAccess) {
                                handleShareInsight(employer.companyName);
                              } else {
                                navigate('/pricing');
                              }
                            }}
                            className="flex-1 sm:flex-none"
                            disabled={!canAccess}
                          >
                            <Share2 className="h-3 w-3 sm:h-4 sm:w-4 sm:mr-1" />
                            <span className="hidden sm:inline">Share</span>
                          </Button>
                        </div>
                      </div>
                      
                      <div className="grid grid-cols-2 lg:grid-cols-4 gap-2 sm:gap-3 mb-3 sm:mb-4">
                        <div className="text-center p-2 sm:p-3 bg-gradient-to-br from-blue-50 to-blue-100 rounded-lg hover:scale-105 transition-transform duration-200">
                          <p className="text-xs sm:text-sm text-blue-600 font-medium">Employees</p>
                          <p className="text-sm sm:text-lg font-bold text-blue-900">{employer.employees}</p>
                        </div>
                        <div className="text-center p-2 sm:p-3 bg-gradient-to-br from-emerald-50 to-emerald-100 rounded-lg hover:scale-105 transition-transform duration-200">
                          <p className="text-xs sm:text-sm text-emerald-600 font-medium">Avg Age</p>
                          <p className="text-sm sm:text-lg font-bold text-emerald-900">{employer.averageAge}</p>
                        </div>
                        <div className="text-center p-2 sm:p-3 bg-gradient-to-br from-orange-50 to-orange-100 rounded-lg hover:scale-105 transition-transform duration-200">
                          <p className="text-xs sm:text-sm text-orange-600 font-medium">Savings</p>
                          <p className="text-sm sm:text-lg font-bold text-orange-900">{employer.potentialSavings}</p>
                        </div>
                        <div className="text-center p-2 sm:p-3 bg-gradient-to-br from-purple-50 to-purple-100 rounded-lg hover:scale-105 transition-transform duration-200">
                          <p className="text-xs sm:text-sm text-purple-600 font-medium">Risk Score</p>
                          <p className="text-sm sm:text-lg font-bold text-purple-900">{employer.riskScore}/10</p>
                        </div>
                      </div>

                      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-2 sm:gap-0">
                        <div className="text-sm">
                          <span className="text-gray-600">Suggested Plan: </span>
                          <span className="font-medium text-gray-900">{employer.planType}</span>
                        </div>
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                          employer.status === 'analyzed' 
                            ? 'bg-green-100 text-green-800' 
                            : 'bg-yellow-100 text-yellow-800'
                        }`}>
                          {employer.status === 'analyzed' ? '✅ Analyzed' : '⏳ Processing'}
                        </span>
                      </div>
                    </CardContent>
                  </Card>
                );
              })}
            </div>
          </CardContent>
        </Card>

        {/* Compact Viral Sharing Section */}
        <Card className="mt-6 bg-gradient-to-r from-purple-100 to-blue-100 border-0 shadow-lg">
          <CardContent className="p-4 sm:p-6 text-center">
            <h3 className="text-lg sm:text-xl font-bold text-gray-900 mb-2">
              🚀 Grow Your Network
            </h3>
            <p className="text-gray-700 mb-4 text-sm sm:text-base">
              Share BenOsphere with other brokers and get rewards for every signup
            </p>
            <div className="flex flex-col sm:flex-row justify-center space-y-2 sm:space-y-0 sm:space-x-4">
              <Button className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white transition-all duration-200 hover:scale-105">
                <Share2 className="h-4 w-4 mr-2" />
                Refer a Broker
              </Button>
              <Button variant="outline" className="hover:bg-white/50">
                View Referral Rewards
              </Button>
            </div>
          </CardContent>
        </Card>
      </main>


    </div>
  );
};

export default BrokerDashboard;
