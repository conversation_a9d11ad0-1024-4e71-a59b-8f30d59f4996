import { usePara<PERSON>, useNavigate } from "../lib/react-router-dom";
import { useSearchParams } from "next/navigation";
import { useState } from "react";
import { But<PERSON> } from "../components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "../components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "../components/ui/tabs";
import { Input } from "../components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "../components/ui/select";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "../components/ui/dialog";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "../components/ui/table";
import { ArrowLeft, Users, DollarSign, TrendingUp, AlertTriangle, CheckCircle, Eye, Share2, BarChart3, Search, Mail, X, Redo, Target, Lightbulb } from "lucide-react";
import { useToast } from "../hooks/use-toast";
import Ask<PERSON><PERSON> from "../components/AskBrea";

const EmployerInsight = () => {
  const navigate = useNavigate();
  const searchParams = useSearchParams();
  const pageParam = searchParams.get('page') || '';

  // Extract ID from page parameter (e.g., "employer-insight/1" -> "1")
  const id = pageParam.includes('/') ? pageParam.split('/')[1] : '1';
  const [employeeSearchTerm, setEmployeeSearchTerm] = useState("");
  const [coverageFilter, setCoverageFilter] = useState("all");
  const [ageFilter, setAgeFilter] = useState("all");
  const [dependentsFilter, setDependentsFilter] = useState("all");
  const [isShareModalOpen, setIsShareModalOpen] = useState(false);
  const [shareEmail, setShareEmail] = useState("");
  const [shareMessage, setShareMessage] = useState("");
  const [selectedEmployee, setSelectedEmployee] = useState<any>(null);
  const [isEmployeeDetailOpen, setIsEmployeeDetailOpen] = useState(false);
  const { toast } = useToast();

  // Mock data - in real app this would come from API based on ID
  const companyData = {
    "1": {
      companyName: "TechCorp Solutions",
      employees: 43,
      averageAge: 36,
      dependents: 1.3,
      planType: "PPO + HSA Combo",
      potentialSavings: "$127,500",
      riskScore: "6.2/10",
      uploadDate: "2024-01-15",
      industry: "Technology",
      currentSpend: "$45,000/month",
      suggestedPlan: "Gold PPO with Disability Coverage",
      planFitSummary: {
        silverGoldPPO: 67,
        hdhp: 21,
        familyPPO: 12,
        insight: "Higher average age + many in physical roles → more comprehensive plans needed. Disability & Rx coverage critical."
      },
      employeeProfiles: [
        { 
          name: "Sarah Johnson", 
          department: "Engineering", 
          risk: "High", 
          age: 34,
          coverage: "Medical",
          hasDependents: true,
          salary: "$85,000",
          currentPlan: {
            medical: "Silver PPO",
            dental: "Not Enrolled",
            vision: "Basic",
            life: "1x Salary",
            disability: "None"
          },
          coverageGaps: [
            { type: "Dental", severity: "High", reason: "No dental coverage for family of 4" },
            { type: "Disability", severity: "Critical", reason: "High-income earner with no disability protection" },
            { type: "Life Insurance", severity: "Medium", reason: "Basic coverage insufficient for family needs" }
          ],
          recommendations: [
            { type: "Family Dental Plan", cost: "+$180/month", priority: "High", benefit: "Complete dental coverage for family" },
            { type: "Short-term Disability", cost: "+$45/month", priority: "Critical", benefit: "60% salary protection" },
            { type: "Additional Life Insurance", cost: "+$65/month", priority: "Medium", benefit: "3x salary coverage" }
          ],
          planFitSummary: {
            goldPPO: { fit: 85, reason: "Family with dependents needs comprehensive coverage" },
            silverPPO: { fit: 65, reason: "Current plan but lacks dental and disability" },
            hdhp: { fit: 25, reason: "Not ideal for family with regular medical needs" },
            basicPlan: { fit: 15, reason: "Insufficient for high-income family with dependents" },
            recommendedPlan: "Gold PPO + Family Dental + Disability",
            insight: "High-earning family needs comprehensive coverage with strong dental and disability benefits"
          },
          insights: ["Family of 4 with minimal dependent coverage", "No disability insurance despite high-risk occupation"], 
          upsells: [{ type: "Family Dental Plan", value: "+$180/month", priority: "High" }] 
        },
        { 
          name: "Mike Chen", 
          department: "Marketing", 
          risk: "Medium", 
          age: 28,
          coverage: "Vision",
          hasDependents: false,
          salary: "$62,000",
          currentPlan: {
            medical: "HDHP + HSA",
            dental: "Basic",
            vision: "Enhanced",
            life: "1x Salary",
            disability: "Short-term Only"
          },
          coverageGaps: [
            { type: "Long-term Disability", severity: "Medium", reason: "Only has short-term disability coverage" },
            { type: "HSA Contribution", severity: "Low", reason: "Not maximizing HSA employer match" }
          ],
          recommendations: [
            { type: "Long-term Disability", cost: "+$35/month", priority: "Medium", benefit: "Long-term income protection" },
            { type: "Increase HSA Contribution", cost: "$0", priority: "High", benefit: "Tax savings + employer match" }
          ],
          planFitSummary: {
            hdhp: { fit: 90, reason: "Young, healthy, single - perfect for HSA strategy" },
            silverPPO: { fit: 60, reason: "Good coverage but less tax advantaged" },
            goldPPO: { fit: 40, reason: "Over-coverage for current needs" },
            basicPlan: { fit: 70, reason: "Adequate but misses HSA benefits" },
            recommendedPlan: "HDHP + Enhanced HSA + Long-term Disability",
            insight: "Young professional ideal for HSA maximization with added disability protection"
          },
          insights: ["Single coverage adequate for current needs", "Young professional with growth potential"], 
          upsells: [{ type: "Vision Upgrade", value: "+$45/month", priority: "Medium" }] 
        },
        { 
          name: "Lisa Rodriguez", 
          department: "Sales", 
          risk: "Low", 
          age: 31,
          coverage: "Dental",
          hasDependents: true,
          salary: "$78,000",
          currentPlan: {
            medical: "Gold PPO",
            dental: "Premium",
            vision: "Basic",
            life: "2x Salary",
            disability: "Both ST & LT"
          },
          coverageGaps: [
            { type: "Vision Upgrade", severity: "Low", reason: "Basic vision may not cover family needs" },
            { type: "Dependent Life", severity: "Low", reason: "No life insurance for spouse" }
          ],
          recommendations: [
            { type: "Enhanced Vision", cost: "+$25/month", priority: "Low", benefit: "Better vision coverage for family" },
            { type: "Spouse Life Insurance", cost: "+$15/month", priority: "Low", benefit: "Financial protection for spouse" }
          ],
          planFitSummary: {
            goldPPO: { fit: 95, reason: "Excellent fit - comprehensive coverage for family" },
            silverPPO: { fit: 75, reason: "Good but less comprehensive than current" },
            hdhp: { fit: 30, reason: "Not ideal for family with established medical needs" },
            basicPlan: { fit: 20, reason: "Insufficient for family coverage needs" },
            recommendedPlan: "Current Gold PPO + Enhanced Vision + Spouse Life",
            insight: "Optimal current coverage with minor enhancements needed for complete protection"
          },
          insights: ["Optimal coverage for demographics", "Recently married - may need updates"], 
          upsells: [{ type: "Spouse Coverage", value: "+$120/month", priority: "Low" }] 
        },
        { 
          name: "John Smith", 
          department: "HR", 
          risk: "Medium", 
          age: 45,
          coverage: "Waived",
          hasDependents: false,
          salary: "$72,000",
          currentPlan: {
            medical: "Waived (Spouse Plan)",
            dental: "Waived",
            vision: "Waived",
            life: "Basic Company",
            disability: "None"
          },
          coverageGaps: [
            { type: "Supplemental Coverage", severity: "Medium", reason: "No backup if spouse plan changes" },
            { type: "Disability Insurance", severity: "High", reason: "No disability coverage at all" },
            { type: "Voluntary Benefits", severity: "Low", reason: "Missing supplemental options" }
          ],
          recommendations: [
            { type: "Supplemental Medical", cost: "+$85/month", priority: "Medium", benefit: "Backup medical coverage" },
            { type: "Individual Disability", cost: "+$65/month", priority: "High", benefit: "Personal disability protection" },
            { type: "Critical Illness", cost: "+$35/month", priority: "Low", benefit: "Additional financial protection" }
          ],
          planFitSummary: {
            supplementalPlan: { fit: 85, reason: "Backup coverage essential for spouse plan dependency" },
            basicPPO: { fit: 70, reason: "Simple coverage if spouse plan ends" },
            goldPPO: { fit: 50, reason: "Comprehensive but may duplicate spouse coverage" },
            voluntaryOnly: { fit: 75, reason: "Disability and critical illness without medical overlap" },
            recommendedPlan: "Supplemental Medical + Individual Disability + Voluntary Benefits",
            insight: "Focus on gap coverage and personal disability protection while maintaining spouse plan coordination"
          },
          insights: ["Waived coverage due to spouse plan", "May benefit from supplemental options"], 
          upsells: [{ type: "Supplemental Life", value: "+$65/month", priority: "Medium" }] 
        }
      ],
      upsellOpportunities: [
        { category: "Dental Coverage", description: "Only 60% have dental coverage", savings: "+$2,400/month", confidence: "94%", priority: "High" },
        { category: "Life Insurance Upgrade", description: "Basic coverage insufficient for 40% of workforce", savings: "+$1,800/month", confidence: "87%", priority: "High" },
        { category: "Wellness Program", description: "Reduce claims costs with preventive care", savings: "+$950/month", confidence: "78%", priority: "Medium" }
      ]
    },
    "2": {
      companyName: "Green Manufacturing",
      employees: 87,
      averageAge: 42,
      dependents: 1.8,
      planType: "Traditional PPO",
      potentialSavings: "$245,000",
      riskScore: "7.1/10",
      uploadDate: "2024-01-10",
      industry: "Manufacturing",
      currentSpend: "$78,000/month",
      suggestedPlan: "Comprehensive PPO with Enhanced Coverage",
      planFitSummary: {
        silverGoldPPO: 75,
        hdhp: 15,
        familyPPO: 10,
        insight: "Manufacturing environment requires comprehensive coverage. High-risk occupation demands enhanced disability and life insurance."
      },
      employeeProfiles: [
        { name: "Robert Smith", department: "Operations", risk: "High", age: 48, coverage: "Medical", hasDependents: true, insights: ["Family coverage with aging dependents", "High-risk occupation requiring additional coverage"], upsells: [{ type: "Enhanced Life Insurance", value: "+$220/month", priority: "High" }] },
        { name: "Jennifer Davis", department: "Quality Control", risk: "Medium", age: 55, coverage: "Dental", hasDependents: false, insights: ["Standard coverage meets current needs", "Approaching retirement planning phase"], upsells: [{ type: "Retirement Health Savings", value: "+$85/month", priority: "Medium" }] }
      ],
      upsellOpportunities: [
        { category: "Disability Insurance", description: "High-risk manufacturing environment", savings: "+$3,200/month", confidence: "91%", priority: "High" },
        { category: "Family Coverage Expansion", description: "Large families underinsured", savings: "+$2,100/month", confidence: "85%", priority: "High" },
        { category: "Mental Health Support", description: "Stress management for manufacturing workers", savings: "+$1,400/month", confidence: "72%", priority: "Medium" }
      ]
    },
    "3": {
      companyName: "StartupXYZ",
      employees: 18,
      averageAge: 29,
      dependents: 0.8,
      planType: "HSA Only",
      potentialSavings: "$32,400",
      riskScore: "4.5/10",
      uploadDate: "2024-01-18",
      industry: "Startup",
      currentSpend: "$12,000/month",
      suggestedPlan: "Modern HSA Plus Plan",
      planFitSummary: {
        silverGoldPPO: 45,
        hdhp: 40,
        familyPPO: 15,
        insight: "Young workforce suits HSA-focused plans. Emphasize digital health tools and preventive care for cost-conscious startup environment."
      },
      employeeProfiles: [
        { name: "Alex Thompson", department: "Development", risk: "Low", age: 26, coverage: "Medical", hasDependents: false, insights: ["Young, healthy demographic", "Minimal current healthcare needs"], upsells: [{ type: "Preventive Care Package", value: "+$35/month", priority: "Low" }] },
        { name: "Emma Wilson", department: "Design", risk: "Low", age: 24, coverage: "Vision", hasDependents: false, insights: ["Single coverage optimal", "Tech-savvy, prefers digital health tools"], upsells: [{ type: "Digital Health Platform", value: "+$25/month", priority: "Low" }] }
      ],
      upsellOpportunities: [
        { category: "Growth Scaling Plan", description: "Prepare for rapid hiring expansion", savings: "+$800/month", confidence: "89%", priority: "Medium" },
        { category: "Mental Health & Wellness", description: "Startup stress management", savings: "+$450/month", confidence: "76%", priority: "Medium" },
        { category: "Tech Health Tools", description: "Digital-first health solutions", savings: "+$300/month", confidence: "82%", priority: "Low" }
      ]
    }
  };

  const company = companyData[id as keyof typeof companyData];

  if (!company) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50 flex items-center justify-center">
        <Card className="max-w-md mx-auto">
          <CardContent className="p-6 text-center">
            <h2 className="text-xl font-bold mb-2">Company Not Found</h2>
            <p className="text-gray-600 mb-4">The requested company insight could not be found.</p>
            <Button onClick={() => navigate('/dashboard')}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Dashboard
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Filter employee profiles
  const filteredEmployees = company.employeeProfiles.filter(employee => {
    // Search filter
    if (employeeSearchTerm && !employee.name.toLowerCase().includes(employeeSearchTerm.toLowerCase())) {
      return false;
    }

    // Coverage filter
    if (coverageFilter !== "all" && employee.coverage.toLowerCase() !== coverageFilter) {
      return false;
    }

    // Age filter
    if (ageFilter !== "all") {
      if (ageFilter === "under-30" && employee.age >= 30) return false;
      if (ageFilter === "30-45" && (employee.age < 30 || employee.age > 45)) return false;
      if (ageFilter === "46-plus" && employee.age <= 45) return false;
    }

    // Dependents filter
    if (dependentsFilter !== "all") {
      if (dependentsFilter === "has-dependents" && !employee.hasDependents) return false;
      if (dependentsFilter === "no-dependents" && employee.hasDependents) return false;
    }

    return true;
  });

  const handleShare = () => {
    if (!shareEmail) {
      toast({
        title: "Email Required",
        description: "Please enter an email address to share the report.",
        variant: "destructive",
      });
      return;
    }

    // Here you would typically send the email through an API
    console.log(`Sharing report with ${shareEmail}:`, shareMessage);
    
    toast({
      title: "Report Shared",
      description: `The report has been shared with ${shareEmail}`,
    });
    
    setIsShareModalOpen(false);
    setShareEmail("");
    setShareMessage("");
  };

  const handleGenerateProposal = () => {
    navigate(`/generate-proposal/${id}`);
  };

  const handleViewEmployeeDetails = (employee: any) => {
    setSelectedEmployee(employee);
    setIsEmployeeDetailOpen(true);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50">
      {/* Header */}
      <header className="border-b bg-white/90 backdrop-blur-sm sticky top-0 z-50">
        <div className="container mx-auto px-3 sm:px-4 py-3 flex justify-between items-center">
          <div className="flex items-center space-x-4">
            <Button variant="ghost" size="sm" onClick={() => navigate('/dashboard')}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Dashboard
            </Button>
            <div className="text-xl sm:text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
              BenOsphere
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <AskBrea 
              context={`${company.companyName} employer insight with ${company.employees} employees in ${company.industry}`} 
              size="sm" 
              variant="outline" 
            />
            <Dialog open={isShareModalOpen} onOpenChange={setIsShareModalOpen}>
              <DialogTrigger asChild>
                <Button size="sm" variant="outline">
                  <Share2 className="h-4 w-4 mr-2" />
                  Share with Employer
                </Button>
              </DialogTrigger>
              <DialogContent className="sm:max-w-md">
                <DialogHeader>
                  <DialogTitle>Share Report with Employer</DialogTitle>
                  <DialogDescription>
                    Send this insight report directly to the employer&apos;s email address.
                  </DialogDescription>
                </DialogHeader>
                <div className="space-y-4">
                  <div>
                    <label className="text-sm font-medium text-gray-700 mb-2 block">Email Address</label>
                    <Input
                      type="email"
                      placeholder="<EMAIL>"
                      value={shareEmail}
                      onChange={(e) => setShareEmail(e.target.value)}
                    />
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-700 mb-2 block">Message (Optional)</label>
                    <textarea
                      className="w-full p-3 border border-gray-300 rounded-md resize-none"
                      rows={3}
                      placeholder="Add a personal message..."
                      value={shareMessage}
                      onChange={(e) => setShareMessage(e.target.value)}
                    />
                  </div>
                </div>
                <DialogFooter>
                  <Button variant="outline" onClick={() => setIsShareModalOpen(false)}>
                    Cancel
                  </Button>
                  <Button onClick={handleShare}>
                    <Mail className="h-4 w-4 mr-2" />
                    Send Report
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
            <Button variant="outline" size="sm">
              John Broker
            </Button>
          </div>
        </div>
      </header>

      <main className="container mx-auto px-3 sm:px-4 py-6 max-w-7xl">
        {/* Company Header */}
        <Card className="mb-6 border-2 border-gray-300 shadow-lg">
          <CardContent className="p-6">
            <div className="mb-4">
              <h1 className="text-3xl font-bold text-gray-900 mb-2">{company.companyName}</h1>
              <div className="flex flex-wrap items-center gap-4 text-sm text-gray-600">
                <span className="flex items-center">
                  <span className="mr-1">📈</span>
                  {company.industry}
                </span>
                <span className="flex items-center">
                  <Users className="h-4 w-4 mr-1" />
                  {company.employees} employees
                </span>
                <span className="flex items-center">
                  <span className="mr-1">📊</span>
                  Average Age: {company.averageAge}
                </span>
                <span className="flex items-center">
                  <span className="mr-1">📅</span>
                  Analyzed: {new Date(company.uploadDate).toLocaleDateString()}
                </span>
                <span className="flex items-center">
                  <DollarSign className="h-4 w-4 mr-1" />
                  Current Spend: {company.currentSpend}
                </span>
              </div>
            </div>

            {/* Summary Cards */}
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
              <Card className="bg-gradient-to-br from-orange-500 to-red-500 border-0 shadow-lg hover:shadow-xl transition-all duration-200 hover:scale-105">
                <CardContent className="p-4 text-center">
                  <DollarSign className="h-8 w-8 mx-auto mb-2 text-white" />
                  <p className="text-sm text-orange-100 font-medium">Employer Savings Opportunity (Total)</p>
                  <p className="text-2xl font-bold text-white">{company.potentialSavings}</p>
                </CardContent>
              </Card>

              <Card className="bg-gradient-to-br from-purple-500 to-pink-500 border-0 shadow-lg hover:shadow-xl transition-all duration-200 hover:scale-105">
                <CardContent className="p-4 text-center">
                  <AlertTriangle className="h-8 w-8 mx-auto mb-2 text-white" />
                  <p className="text-sm text-purple-100 font-medium">Risk Score</p>
                  <p className="text-2xl font-bold text-white">{company.riskScore}</p>
                </CardContent>
              </Card>

              <Card className="bg-gradient-to-br from-emerald-500 to-teal-600 border-0 shadow-lg hover:shadow-xl transition-all duration-200 hover:scale-105">
                <CardContent className="p-4 text-center">
                  <TrendingUp className="h-8 w-8 mx-auto mb-2 text-white" />
                  <p className="text-sm text-emerald-100 font-medium">Benchmark Rate</p>
                  <p className="text-2xl font-bold text-white">8.5% below</p>
                </CardContent>
              </Card>
            </div>
          </CardContent>
        </Card>

        {/* Main Tabs Section */}
        <Tabs defaultValue="executive-overview" className="w-full">
          <TabsList className="grid w-full grid-cols-4 mb-6">
            <TabsTrigger value="executive-overview" className="flex items-center gap-2">
              <TrendingUp className="h-4 w-4" />
              Executive Overview
            </TabsTrigger>
            <TabsTrigger value="plan-fit" className="flex items-center gap-2">
              <BarChart3 className="h-4 w-4" />
              Plan Fit Analysis
            </TabsTrigger>
            <TabsTrigger value="upsells" className="flex items-center gap-2">
              <DollarSign className="h-4 w-4" />
              Upsell Opportunities
            </TabsTrigger>
            <TabsTrigger value="employees" className="flex items-center gap-2">
              <Users className="h-4 w-4" />
              Employee Insights
            </TabsTrigger>
          </TabsList>

          <TabsContent value="executive-overview">
            {/* Employee Demographics and Cost Analysis */}
            <div className="grid lg:grid-cols-2 gap-8 mb-8">
              {/* Employee Demographics */}
              <Card className="shadow-lg border-0">
                <CardHeader>
                  <CardTitle className="text-xl bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent flex items-center">
                    <Users className="h-5 w-5 mr-2" />
                    Employee Demographics
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex justify-between items-center p-3 bg-blue-50 rounded-lg">
                      <span className="font-medium">Age 25-35</span>
                      <span className="font-bold text-blue-600">18 employees (42%)</span>
                    </div>
                    <div className="flex justify-between items-center p-3 bg-green-50 rounded-lg">
                      <span className="font-medium">Age 36-50</span>
                      <span className="font-bold text-green-600">20 employees (46%)</span>
                    </div>
                    <div className="flex justify-between items-center p-3 bg-orange-50 rounded-lg">
                      <span className="font-medium">Age 50+</span>
                      <span className="font-bold text-orange-600">5 employees (12%)</span>
                    </div>
                    <div className="flex justify-between items-center p-3 bg-purple-50 rounded-lg">
                      <span className="font-medium">Employees with Families</span>
                      <span className="font-bold text-purple-600">28 employees (65%)</span>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Cost Analysis */}
              <Card className="shadow-lg border-0">
                <CardHeader>
                  <CardTitle className="text-xl bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent flex items-center">
                    <DollarSign className="h-5 w-5 mr-2" />
                    Cost Analysis
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="p-4 bg-green-50 rounded-lg border border-green-200">
                      <h4 className="font-semibold text-green-800 mb-2">Potential Annual Savings</h4>
                      <p className="text-2xl font-bold text-green-600">$127,500</p>
                      <p className="text-sm text-green-700">By optimizing plan structure</p>
                    </div>
                    <div className="p-4 bg-blue-50 rounded-lg border border-blue-200">
                      <h4 className="font-semibold text-blue-800 mb-2">Current Annual Premium</h4>
                      <p className="text-2xl font-bold text-blue-600">$486,000</p>
                      <p className="text-sm text-blue-700">Average $11,300 per employee</p>
                    </div>
                    <div className="p-4 bg-orange-50 rounded-lg border border-orange-200">
                      <h4 className="font-semibold text-orange-800 mb-2">Benchmarked Rate</h4>
                      <p className="text-2xl font-bold text-orange-600">8.5% below</p>
                      <p className="text-sm text-orange-700">Industry average</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Personalized Recommendations */}
            <Card className="shadow-xl mb-8 border-0">
              <CardHeader>
                <CardTitle className="text-2xl bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent flex items-center">
                  <Lightbulb className="h-6 w-6 mr-2" />
                  Personalized Recommendations
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid md:grid-cols-3 gap-6">
                  {/* Cost Savings */}
                  <div className="space-y-3">
                    <h3 className="font-semibold text-lg text-gray-900">Cost Savings</h3>
                    <ul className="space-y-2">
                      <li className="flex items-start">
                        <span className="text-blue-500 mr-2 mt-1">✓</span>
                        <span className="text-sm text-gray-700">Switch to HSA-eligible plan could save $127,500 annually</span>
                      </li>
                      <li className="flex items-start">
                        <span className="text-blue-500 mr-2 mt-1">✓</span>
                        <span className="text-sm text-gray-700">Optimize contribution strategy for younger demographics</span>
                      </li>
                      <li className="flex items-start">
                        <span className="text-blue-500 mr-2 mt-1">✓</span>
                        <span className="text-sm text-gray-700">Consider wellness programs to reduce risk factors</span>
                      </li>
                    </ul>
                  </div>

                  {/* Plan Improvements */}
                  <div className="space-y-3">
                    <h3 className="font-semibold text-lg text-gray-900">Plan Improvements</h3>
                    <ul className="space-y-2">
                      <li className="flex items-start">
                        <span className="text-blue-500 mr-2 mt-1">✓</span>
                        <span className="text-sm text-gray-700">Add vision benefits - 68% of employees would benefit</span>
                      </li>
                      <li className="flex items-start">
                        <span className="text-blue-500 mr-2 mt-1">✓</span>
                        <span className="text-sm text-gray-700">Increase mental health coverage options</span>
                      </li>
                      <li className="flex items-start">
                        <span className="text-blue-500 mr-2 mt-1">✓</span>
                        <span className="text-sm text-gray-700">Consider flexible spending accounts</span>
                      </li>
                    </ul>
                  </div>

                  {/* Employee Engagement */}
                  <div className="space-y-3">
                    <h3 className="font-semibold text-lg text-gray-900">Employee Engagement</h3>
                    <ul className="space-y-2">
                      <li className="flex items-start">
                        <span className="text-blue-500 mr-2 mt-1">✓</span>
                        <span className="text-sm text-gray-700">Implement benefits education program</span>
                      </li>
                      <li className="flex items-start">
                        <span className="text-blue-500 mr-2 mt-1">✓</span>
                        <span className="text-sm text-gray-700">Add telemedicine options for remote workers</span>
                      </li>
                      <li className="flex items-start">
                        <span className="text-blue-500 mr-2 mt-1">✓</span>
                        <span className="text-sm text-gray-700">Create benefits portal for easy access</span>
                      </li>
                    </ul>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Industry Benchmark */}
            <Card className="shadow-lg border-0 mt-6">
              <CardHeader>
                <CardTitle className="text-xl bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent flex items-center">
                  <BarChart3 className="h-5 w-5 mr-2" />
                  Industry Benchmark Comparison
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid md:grid-cols-3 gap-6">
                  <Card className="bg-gradient-to-br from-red-50 to-red-100 border-red-200">
                    <CardContent className="p-4 text-center">
                      <h4 className="text-sm font-medium text-gray-600 mb-2">Current Position</h4>
                      <p className="text-3xl font-bold text-red-600">45th</p>
                      <p className="text-sm text-gray-600">Percentile</p>
                    </CardContent>
                  </Card>
                  <Card className="bg-gradient-to-br from-green-50 to-green-100 border-green-200">
                    <CardContent className="p-4 text-center">
                      <h4 className="text-sm font-medium text-gray-600 mb-2">Projected Position</h4>
                      <p className="text-3xl font-bold text-green-600">78th</p>
                      <p className="text-sm text-gray-600">Percentile</p>
                    </CardContent>
                  </Card>
                  <Card className="bg-gradient-to-br from-blue-50 to-blue-100 border-blue-200">
                    <CardContent className="p-4 text-center">
                      <h4 className="text-sm font-medium text-gray-600 mb-2">Industry Avg Savings</h4>
                      <p className="text-2xl font-bold text-blue-600">$95,000</p>
                      <p className="text-sm text-gray-600">Annual</p>
                    </CardContent>
                  </Card>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="plan-fit">
            <Card className="shadow-lg border-0">
              <CardHeader className="pb-4">
                <CardTitle className="flex items-center">
                  <div className="bg-blue-100 p-2 rounded-lg mr-3">
                    <BarChart3 className="h-5 w-5 text-blue-600" />
                  </div>
                  Plan Fit Analysis
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* Suggested Plan */}
                <div className="bg-gradient-to-r from-yellow-50 to-orange-50 p-4 rounded-lg border-l-4 border-yellow-500">
                  <h3 className="text-lg font-semibold text-gray-900 mb-2 flex items-center">
                    <span className="mr-2">🏆</span>
                    Suggested Plan:
                  </h3>
                  <p className="text-xl font-bold text-orange-800">{company.suggestedPlan}</p>
                </div>

                {/* Plan Fit Summary */}
                <div>
                  <h4 className="text-lg font-semibold text-gray-900 mb-4">Plan Fit Summary:</h4>
                  <div className="space-y-3">
                    <div className="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
                      <div className="flex items-center">
                        <div className="w-4 h-4 bg-blue-500 rounded mr-3"></div>
                        <span className="font-medium">{company.planFitSummary.silverGoldPPO}% fit for Silver/Gold PPO</span>
                      </div>
                      <BarChart3 className="h-4 w-4 text-blue-600" />
                    </div>
                    
                    <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                      <div className="flex items-center">
                        <div className="w-4 h-4 bg-green-500 rounded mr-3"></div>
                        <span className="font-medium">{company.planFitSummary.hdhp}% fit for HDHP</span>
                      </div>
                      <BarChart3 className="h-4 w-4 text-green-600" />
                    </div>
                    
                    <div className="flex items-center justify-between p-3 bg-purple-50 rounded-lg">
                      <div className="flex items-center">
                        <div className="w-4 h-4 bg-purple-500 rounded mr-3"></div>
                        <span className="font-medium">{company.planFitSummary.familyPPO}% require Family PPO with Rx coverage</span>
                      </div>
                      <BarChart3 className="h-4 w-4 text-purple-600" />
                    </div>
                  </div>
                </div>

                {/* Detailed Recommendation Summary */}
                <div className="mt-6">
                  <h4 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                    <CheckCircle className="h-5 w-5 mr-2 text-green-600" />
                    Detailed Recommendations
                  </h4>
                  <div className="space-y-4">
                    <Card className="border-l-4 border-l-green-500">
                      <CardContent className="p-4">
                        <h4 className="font-semibold text-gray-900 mb-2">Primary Plan Change</h4>
                        <p className="text-gray-700 mb-2">Transition to Gold PPO with enhanced disability coverage</p>
                        <p className="text-sm text-green-600 font-medium">Reduce overall costs by 23% while improving coverage</p>
                      </CardContent>
                    </Card>
                    <Card className="border-l-4 border-l-blue-500">
                      <CardContent className="p-4">
                        <h4 className="font-semibold text-gray-900 mb-2">Voluntary Benefits</h4>
                        <p className="text-gray-700 mb-2">Add dental and vision upgrade options</p>
                        <p className="text-sm text-blue-600 font-medium">Increase employee satisfaction and retention</p>
                      </CardContent>
                    </Card>
                    <Card className="border-l-4 border-l-purple-500">
                      <CardContent className="p-4">
                        <h4 className="font-semibold text-gray-900 mb-2">Add-on Services</h4>
                        <p className="text-gray-700 mb-2">Include telehealth and mental health support</p>
                        <p className="text-sm text-purple-600 font-medium">Preventive care reducing long-term claims costs</p>
                      </CardContent>
                    </Card>
                  </div>
                </div>

                {/* Key Workforce Insights */}
                <div className="mt-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                    <Lightbulb className="h-5 w-5 mr-2 text-blue-600" />
                    Key Workforce Insights
                  </h3>
                  <div className="grid md:grid-cols-2 gap-4">
                    <div className="flex items-start p-4 bg-blue-50 rounded-lg">
                      <CheckCircle className="h-5 w-5 text-blue-600 mr-3 mt-0.5 flex-shrink-0" />
                      <p className="text-gray-700">Majority of employees fall in high-utilization age bracket (36 avg age)</p>
                    </div>
                    <div className="flex items-start p-4 bg-blue-50 rounded-lg">
                      <CheckCircle className="h-5 w-5 text-blue-600 mr-3 mt-0.5 flex-shrink-0" />
                      <p className="text-gray-700">Disability coverage crucial due to technology industry risk profile</p>
                    </div>
                    <div className="flex items-start p-4 bg-blue-50 rounded-lg">
                      <CheckCircle className="h-5 w-5 text-blue-600 mr-3 mt-0.5 flex-shrink-0" />
                      <p className="text-gray-700">67% of workforce suits comprehensive PPO coverage</p>
                    </div>
                    <div className="flex items-start p-4 bg-blue-50 rounded-lg">
                      <CheckCircle className="h-5 w-5 text-blue-600 mr-3 mt-0.5 flex-shrink-0" />
                      <p className="text-gray-700">Current HSA utilization below industry average</p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="upsells">
            <Card className="shadow-lg border-0">
              <CardHeader className="pb-4">
                <CardTitle className="flex items-center">
                  <div className="bg-orange-100 p-2 rounded-lg mr-3">
                    <DollarSign className="h-5 w-5 text-orange-600" />
                  </div>
                  Projected Employer Savings
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {company.upsellOpportunities.map((opportunity, index) => (
                  <Card key={index} className="border-l-4 border-l-orange-500 hover:shadow-md transition-all duration-200">
                    <CardContent className="p-4">
                      <div className="flex justify-between items-start mb-2">
                        <div className="flex items-center">
                          <CheckCircle className="h-4 w-4 text-blue-600 mr-2" />
                          <span className="font-semibold">{opportunity.category}</span>
                        </div>
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                          opportunity.priority === 'High' 
                            ? 'bg-red-100 text-red-800' 
                            : opportunity.priority === 'Medium'
                            ? 'bg-yellow-100 text-yellow-800'
                            : 'bg-green-100 text-green-800'
                        }`}>
                          {opportunity.priority}
                        </span>
                      </div>
                      <p className="text-sm text-gray-600 mb-2">{opportunity.description}</p>
                      <div className="flex justify-between items-center">
                        <span className="text-lg font-bold text-green-600">{opportunity.savings}</span>
                        <div className="flex items-center space-x-2">
                          <span className="text-sm text-gray-500">{opportunity.confidence} confidence</span>
                          <Button size="sm" variant="outline">
                            <Eye className="h-3 w-3 mr-1" />
                            View Details
                          </Button>
                        </div>
                      </div>
                     </CardContent>
                   </Card>
                 ))}

                 {/* Add-on Recommendations from Generate Proposal */}
                 <div className="mt-6">
                   <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                     <Lightbulb className="h-5 w-5 mr-2 text-orange-600" />
                     Add-on Service Opportunities
                   </h3>
                   <div className="space-y-4">
                     <Card className="border-l-4 border-l-blue-500">
                       <CardContent className="p-4">
                         <div className="flex justify-between items-start mb-2">
                           <div className="flex items-center">
                             <CheckCircle className="h-4 w-4 text-blue-600 mr-2" />
                             <span className="font-semibold">Telehealth Services</span>
                           </div>
                           <span className="px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                             High Impact
                           </span>
                         </div>
                         <p className="text-sm text-gray-600 mb-2">Remote healthcare access for all employees</p>
                         <div className="flex justify-between items-center">
                           <span className="text-lg font-bold text-green-600">+$35/employee/month</span>
                           <span className="text-sm text-gray-500">Reduces traditional care costs by 15%</span>
                         </div>
                       </CardContent>
                     </Card>
                     
                     <Card className="border-l-4 border-l-purple-500">
                       <CardContent className="p-4">
                         <div className="flex justify-between items-start mb-2">
                           <div className="flex items-center">
                             <CheckCircle className="h-4 w-4 text-purple-600 mr-2" />
                             <span className="font-semibold">Mental Health Support</span>
                           </div>
                           <span className="px-2 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                             Medium Impact
                           </span>
                         </div>
                         <p className="text-sm text-gray-600 mb-2">Enhanced EAP and mental wellness programs</p>
                         <div className="flex justify-between items-center">
                           <span className="text-lg font-bold text-green-600">+$28/employee/month</span>
                           <span className="text-sm text-gray-500">Improves productivity and retention</span>
                         </div>
                       </CardContent>
                     </Card>
                   </div>
                 </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="employees">
            <Card className="shadow-lg border-0">
              <CardHeader className="pb-4">
                <CardTitle className="flex items-center">
                  <div className="bg-blue-100 p-2 rounded-lg mr-3">
                    <Users className="h-5 w-5 text-blue-600" />
                  </div>
                  Employee Insights
                </CardTitle>
                </CardHeader>
                <CardContent>
                {/* Search and Filter Section for Employees */}
                <div className="mb-6 space-y-4">
                  {/* Search Bar */}
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                    <Input
                      placeholder="Search by name"
                      value={employeeSearchTerm}
                      onChange={(e) => setEmployeeSearchTerm(e.target.value)}
                      className="pl-10"
                    />
                  </div>
                  
                  {/* Filters Row */}
                  <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
                    <div>
                      <label className="text-sm font-medium text-gray-700 mb-2 block">Coverage</label>
                      <Select value={coverageFilter} onValueChange={setCoverageFilter}>
                        <SelectTrigger>
                          <SelectValue placeholder="All" />
                        </SelectTrigger>
                        <SelectContent className="bg-white">
                          <SelectItem value="all">All</SelectItem>
                          <SelectItem value="medical">Medical</SelectItem>
                          <SelectItem value="dental">Dental</SelectItem>
                          <SelectItem value="vision">Vision</SelectItem>
                          <SelectItem value="waived">Waived</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div>
                      <label className="text-sm font-medium text-gray-700 mb-2 block">Age</label>
                      <Select value={ageFilter} onValueChange={setAgeFilter}>
                        <SelectTrigger>
                          <SelectValue placeholder="All" />
                        </SelectTrigger>
                        <SelectContent className="bg-white">
                          <SelectItem value="all">All</SelectItem>
                          <SelectItem value="under-30">&lt;30</SelectItem>
                          <SelectItem value="30-45">30–45</SelectItem>
                          <SelectItem value="46-plus">46+</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div>
                      <label className="text-sm font-medium text-gray-700 mb-2 block">Dependents</label>
                      <Select value={dependentsFilter} onValueChange={setDependentsFilter}>
                        <SelectTrigger>
                          <SelectValue placeholder="All" />
                        </SelectTrigger>
                        <SelectContent className="bg-white">
                          <SelectItem value="all">All</SelectItem>
                          <SelectItem value="has-dependents">Has Dependents</SelectItem>
                          <SelectItem value="no-dependents">No Dependents</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                </div>

                {/* Employee List */}
                <div className="space-y-4">
                  {filteredEmployees.map((employee, index) => (
                    <Card key={index} className="border-l-4 border-l-blue-500 hover:shadow-md transition-all duration-200">
                      <CardContent className="p-4">
                        <div className="flex justify-between items-start mb-2">
                          <div>
                            <h4 className="font-semibold">{employee.name}</h4>
                            <p className="text-sm text-gray-600">{employee.department} • Age: {employee.age} • Coverage: {employee.coverage}</p>
                          </div>
                          <div className="flex items-center space-x-2">
                            <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                              employee.risk === 'High' 
                                ? 'bg-red-100 text-red-800' 
                                : employee.risk === 'Medium'
                                ? 'bg-yellow-100 text-yellow-800'
                                : 'bg-green-100 text-green-800'
                            }`}>
                              <AlertTriangle className="h-3 w-3 inline mr-1" />
                              {employee.risk} Risk
                            </span>
                            <Button size="sm" variant="outline" onClick={() => handleViewEmployeeDetails(employee)}>
                              <Eye className="h-3 w-3 mr-1" />
                              View Details
                            </Button>
                          </div>
                        </div>
                        
                        <div className="mb-3">
                          <p className="text-sm font-medium text-gray-700 mb-1">Key Insights:</p>
                          <ul className="text-sm text-gray-600 space-y-1">
                            {employee.insights.map((insight, i) => (
                              <li key={i} className="flex items-start">
                                <span className="mr-2">•</span>
                                {insight}
                              </li>
                            ))}
                          </ul>
                        </div>

                        <div className="bg-orange-50 p-3 rounded-lg">
                          <p className="text-sm font-medium text-orange-800 mb-1">Upsell Opportunities:</p>
                          {employee.upsells.map((upsell, i) => (
                            <div key={i} className="flex justify-between items-center">
                              <span className="text-sm text-orange-700">{upsell.type}</span>
                              <div className="flex items-center space-x-2">
                                <span className="text-sm font-bold text-green-600">{upsell.value}</span>
                                <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                                  upsell.priority === 'High' 
                                    ? 'bg-red-100 text-red-800' 
                                    : upsell.priority === 'Medium'
                                    ? 'bg-yellow-100 text-yellow-800'
                                    : 'bg-green-100 text-green-800'
                                }`}>
                                  {upsell.priority}
                                </span>
                              </div>
                            </div>
                          ))}
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        {/* Action Buttons */}
        <Card className="mt-6 bg-gradient-to-r from-blue-100 to-purple-100 border-0 shadow-lg">
          <CardContent className="p-6 text-center">
            <h3 className="text-xl font-bold text-gray-900 mb-2">Ready to Present?</h3>
            <p className="text-gray-700 mb-4">Generate a professional proposal based on these insights</p>
            <div className="flex flex-col sm:flex-row justify-center space-y-2 sm:space-y-0 sm:space-x-4">
              <Button 
                className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700"
                onClick={handleGenerateProposal}
              >
                Generate Proposal
              </Button>
              <Button variant="outline" onClick={() => setIsShareModalOpen(true)}>
                <Share2 className="h-4 w-4 mr-2" />
                Share with Employer
              </Button>
            </div>
          </CardContent>
        </Card>
      </main>

      {/* Employee Detail Modal */}
      <Dialog open={isEmployeeDetailOpen} onOpenChange={setIsEmployeeDetailOpen}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center justify-between">
              <span>Employee Detail: {selectedEmployee?.name}</span>
              <div className="flex items-center space-x-2">
                <AskBrea 
                  context={`employee ${selectedEmployee?.name} from ${company.companyName} with detailed benefit analysis`}
                  size="sm" 
                  variant="outline" 
                />
                <Button variant="ghost" size="sm" onClick={() => setIsEmployeeDetailOpen(false)}>
                  <X className="h-4 w-4" />
                </Button>
              </div>
            </DialogTitle>
            <DialogDescription>
              Comprehensive plan analysis and recommendations for {selectedEmployee?.name}
            </DialogDescription>
          </DialogHeader>
          
          {selectedEmployee && (
            <div className="space-y-6">
              {/* Employee Overview */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Employee Overview</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <div>
                      <p className="text-sm text-gray-600">Department</p>
                      <p className="font-semibold">{selectedEmployee.department}</p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-600">Age</p>
                      <p className="font-semibold">{selectedEmployee.age}</p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-600">Salary</p>
                      <p className="font-semibold">{selectedEmployee.salary}</p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-600">Dependents</p>
                      <p className="font-semibold">{selectedEmployee.hasDependents ? 'Yes' : 'No'}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Plan Fit Summary */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg flex items-center">
                    <Target className="h-5 w-5 mr-2 text-blue-600" />
                    Individual Plan Fit Summary
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  {/* Recommended Plan */}
                  <div className="bg-gradient-to-r from-green-50 to-emerald-50 p-4 rounded-lg border-l-4 border-green-500 mb-4">
                    <h4 className="text-lg font-semibold text-gray-900 mb-2 flex items-center">
                      <span className="mr-2">🏆</span>
                      Recommended Plan:
                    </h4>
                    <p className="text-xl font-bold text-green-800">{selectedEmployee.planFitSummary?.recommendedPlan}</p>
                  </div>

                  {/* Plan Fit Scores */}
                  <div className="space-y-3 mb-4">
                    <h4 className="text-lg font-semibold text-gray-900">Plan Fit Analysis:</h4>
                    {selectedEmployee.planFitSummary && Object.entries(selectedEmployee.planFitSummary)
                      .filter(([key]) => !['recommendedPlan', 'insight'].includes(key))
                      .map(([planType, details]: [string, any]) => {
                        const fitScore = details.fit;
                        const getColorClass = (score: number) => {
                          if (score >= 80) return 'bg-green-500';
                          if (score >= 60) return 'bg-yellow-500';
                          if (score >= 40) return 'bg-orange-500';
                          return 'bg-red-500';
                        };

                        return (
                          <div key={planType} className="bg-gray-50 p-4 rounded-lg">
                            <div className="flex items-center justify-between mb-2">
                              <div className="flex items-center">
                                <div className={`w-4 h-4 ${getColorClass(fitScore)} rounded mr-3`}></div>
                                <span className="font-medium capitalize">{planType.replace(/([A-Z])/g, ' $1').trim()}</span>
                              </div>
                              <div className="flex items-center space-x-2">
                                <span className="text-2xl font-bold text-gray-900">{fitScore}%</span>
                                <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                                  fitScore >= 80 ? 'bg-green-100 text-green-800' :
                                  fitScore >= 60 ? 'bg-yellow-100 text-yellow-800' :
                                  fitScore >= 40 ? 'bg-orange-100 text-orange-800' :
                                  'bg-red-100 text-red-800'
                                }`}>
                                  {fitScore >= 80 ? 'Excellent' :
                                   fitScore >= 60 ? 'Good' :
                                   fitScore >= 40 ? 'Fair' : 'Poor'} Fit
                                </span>
                              </div>
                            </div>
                            <p className="text-sm text-gray-600">{details.reason}</p>
                          </div>
                        );
                      })}
                  </div>

                  {/* Insight */}
                  <div className="bg-gradient-to-r from-blue-50 to-indigo-50 p-4 rounded-lg border-l-4 border-blue-500">
                    <h4 className="text-lg font-semibold text-gray-900 mb-2 flex items-center">
                      <span className="mr-2">💡</span>
                      Personal Insight:
                    </h4>
                    <p className="text-gray-700 italic">{selectedEmployee.planFitSummary?.insight}</p>
                  </div>
                </CardContent>
              </Card>

              {/* Current Plan Coverage */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg flex items-center">
                    <CheckCircle className="h-5 w-5 mr-2 text-green-600" />
                    Current Plan Coverage
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Benefit Type</TableHead>
                        <TableHead>Current Coverage</TableHead>
                        <TableHead>Status</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {Object.entries(selectedEmployee.currentPlan).map(([benefit, coverage]) => (
                        <TableRow key={benefit}>
                          <TableCell className="font-medium capitalize">{benefit}</TableCell>
                          <TableCell>{coverage as string}</TableCell>
                          <TableCell>
                            <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                              (coverage as string).includes('None') || (coverage as string).includes('Waived') || (coverage as string).includes('Not Enrolled')
                                ? 'bg-red-100 text-red-800'
                                : (coverage as string).includes('Basic')
                                ? 'bg-yellow-100 text-yellow-800'
                                : 'bg-green-100 text-green-800'
                            }`}>
                              {(coverage as string).includes('None') || (coverage as string).includes('Waived') || (coverage as string).includes('Not Enrolled') ? 'Not Covered' : 'Covered'}
                            </span>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </CardContent>
              </Card>

              {/* Coverage Gaps */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg flex items-center">
                    <AlertTriangle className="h-5 w-5 mr-2 text-red-600" />
                    Identified Coverage Gaps
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {selectedEmployee.coverageGaps?.map((gap: any, index: number) => (
                      <div key={index} className="border-l-4 border-l-red-500 bg-red-50 p-4 rounded-lg">
                        <div className="flex justify-between items-start mb-2">
                          <h4 className="font-semibold text-red-800">{gap.type}</h4>
                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                            gap.severity === 'Critical' 
                              ? 'bg-red-200 text-red-800' 
                              : gap.severity === 'High'
                              ? 'bg-orange-200 text-orange-800'
                              : gap.severity === 'Medium'
                              ? 'bg-yellow-200 text-yellow-800'
                              : 'bg-blue-200 text-blue-800'
                          }`}>
                            {gap.severity}
                          </span>
                        </div>
                        <p className="text-sm text-red-700">{gap.reason}</p>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              {/* Recommendations */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg flex items-center">
                    <Redo className="h-5 w-5 mr-2 text-blue-600" />
                    Recommendations
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {selectedEmployee.recommendations?.map((rec: any, index: number) => (
                      <div key={index} className="border-l-4 border-l-blue-500 bg-blue-50 p-4 rounded-lg">
                        <div className="flex justify-between items-start mb-2">
                          <div>
                            <h4 className="font-semibold text-blue-800">{rec.type}</h4>
                            <p className="text-sm text-blue-700 mt-1">{rec.benefit}</p>
                          </div>
                          <div className="text-right">
                            <div className="flex items-center space-x-2">
                              <span className="text-sm font-bold text-green-600">{rec.cost}</span>
                              <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                                rec.priority === 'Critical' 
                                  ? 'bg-red-100 text-red-800' 
                                  : rec.priority === 'High'
                                  ? 'bg-red-100 text-red-800'
                                  : rec.priority === 'Medium'
                                  ? 'bg-yellow-100 text-yellow-800'
                                  : 'bg-green-100 text-green-800'
                              }`}>
                                {rec.priority}
                              </span>
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default EmployerInsight;
