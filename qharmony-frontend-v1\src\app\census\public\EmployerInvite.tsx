
import { useState } from "react";
import { But<PERSON> } from "../components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "../components/ui/card";
import { Input } from "../components/ui/input";
import { Label } from "../components/ui/label";
import { useNavigate } from "../lib/react-router-dom";
import { Mail, ArrowLeft, Send, Users } from "lucide-react";

const EmployerInvite = () => {
  const navigate = useNavigate();
  const [employerName, setEmployerName] = useState("");
  const [employerEmail, setEmployerEmail] = useState("");
  const [isSubmitted, setIsSubmitted] = useState(false);

  const handleSendInvite = (e: React.FormEvent) => {
    e.preventDefault();
    if (employerEmail) {
      setIsSubmitted(true);
      // Simulate sending invite
      setTimeout(() => {
        console.log(`Invite sent to ${employerEmail} for ${employerName}`);
      }, 1000);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
      {/* Header */}
      <header className="border-b bg-white/80 backdrop-blur-sm">
        <div className="container mx-auto px-4 py-4 flex justify-between items-center">
          <div className="flex items-center space-x-4">
            <Button variant="ghost" onClick={() => navigate('/')}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Home
            </Button>
            <div className="text-2xl font-bold text-blue-600">BenOsphere</div>
          </div>
          <Button variant="outline" onClick={() => navigate('/login')}>
            Sign In
          </Button>
        </div>
      </header>

      <main className="container mx-auto px-4 py-16 max-w-2xl">
        <div className="text-center mb-12">
          <div className="w-20 h-20 mx-auto mb-6 bg-purple-100 rounded-full flex items-center justify-center">
            <Users className="h-10 w-10 text-purple-600" />
          </div>
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            📩 Don&apos;t have the census? Let the employer upload it.
          </h1>
          <p className="text-xl text-gray-600">
            Send a secure link to your client — they&apos;ll be guided to upload their census. You&apos;ll be notified once their enriched group report is ready.
          </p>
        </div>

        {!isSubmitted ? (
          <Card className="shadow-2xl border-0">
            <CardHeader>
              <CardTitle className="text-center text-2xl">Send Employer Invite</CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <form onSubmit={handleSendInvite} className="space-y-6">
                <div className="space-y-2">
                  <Label htmlFor="employer-name" className="text-base font-medium">
                    Employer Name (optional)
                  </Label>
                  <Input
                    id="employer-name"
                    type="text"
                    placeholder="e.g. ABC Corp, Smith & Associates"
                    value={employerName}
                    onChange={(e) => setEmployerName(e.target.value)}
                    className="h-12 text-base"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="employer-email" className="text-base font-medium">
                    📧 Employer Contact Email (required)
                  </Label>
                  <Input
                    id="employer-email"
                    type="email"
                    placeholder="hr@company.<NAME_EMAIL>"
                    value={employerEmail}
                    onChange={(e) => setEmployerEmail(e.target.value)}
                    required
                    className="h-12 text-base"
                  />
                </div>

                <Button 
                  type="submit"
                  size="lg" 
                  className="w-full bg-purple-600 hover:bg-purple-700 text-white py-4 text-lg"
                  disabled={!employerEmail}
                >
                  <Send className="mr-2 h-5 w-5" />
                  ➡️ Send Upload Link to Employer
                </Button>
              </form>

              {/* What happens next */}
              <Card className="bg-purple-50 border-purple-200">
                <CardContent className="p-6">
                  <h3 className="font-semibold text-purple-900 mb-3">What happens next:</h3>
                  <ul className="text-purple-800 space-y-2 text-sm">
                    <li className="flex items-start">
                      <span className="mr-2 mt-0.5">1.</span>
                      Employer receives a secure, personalized upload link
                    </li>
                    <li className="flex items-start">
                      <span className="mr-2 mt-0.5">2.</span>
                      They upload their census file through our guided process
                    </li>
                    <li className="flex items-start">
                      <span className="mr-2 mt-0.5">3.</span>
                      We analyze the data and generate the enriched report
                    </li>
                    <li className="flex items-start">
                      <span className="mr-2 mt-0.5">4.</span>
                      You get notified when the analysis is complete and ready to review
                    </li>
                  </ul>
                </CardContent>
              </Card>

              {/* Alternative Option */}
              <div className="text-center pt-4 border-t">
                <p className="text-sm text-gray-500 mb-3">Prefer to upload yourself?</p>
                <Button 
                  variant="outline" 
                  onClick={() => navigate('/upload')}
                  className="w-full"
                >
                  📤 Upload Census File Directly
                </Button>
              </div>
            </CardContent>
          </Card>
        ) : (
          <Card className="shadow-2xl border-0">
            <CardContent className="p-12 text-center">
              <div className="w-20 h-20 mx-auto mb-6 bg-green-100 rounded-full flex items-center justify-center">
                <Mail className="h-10 w-10 text-green-600" />
              </div>
              
              <h2 className="text-2xl font-bold text-gray-900 mb-4">
                ✅ Magic link sent to {employerEmail}!
              </h2>
              
              <p className="text-lg text-gray-600 mb-6">
                {employerName ? `${employerName} will` : "They&apos;ll"} upload the census — we&apos;ll notify you once it&apos;s processed.
              </p>
              
              <div className="bg-green-50 border border-green-200 rounded-lg p-6 mb-6">
                <p className="text-green-800 text-sm">
                  <strong>Next steps:</strong>
                  <br />
                  • The employer will receive a secure upload link
                  <br />
                  • You&apos;ll get an email notification when analysis is complete
                  <br />
                  • Log in to BenOsphere to access the full enriched report
                </p>
              </div>

              <div className="space-y-3">
                <Button 
                  onClick={() => navigate('/')}
                  className="w-full bg-blue-600 hover:bg-blue-700"
                >
                  Return to Dashboard
                </Button>
                
                <Button 
                  variant="outline"
                  onClick={() => {
                    setIsSubmitted(false);
                    setEmployerName("");
                    setEmployerEmail("");
                  }}
                  className="w-full"
                >
                  Send Another Invite
                </Button>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Security & Privacy Note */}
        <div className="mt-8 text-center">
          <Card className="bg-gray-50 border-gray-200">
            <CardContent className="p-4">
              <p className="text-xs text-gray-600">
                🔒 All uploads are secure and encrypted. Census data is processed confidentially and never shared with third parties. HIPAA compliant.
              </p>
            </CardContent>
          </Card>
        </div>
      </main>
    </div>
  );
};

export default EmployerInvite;
