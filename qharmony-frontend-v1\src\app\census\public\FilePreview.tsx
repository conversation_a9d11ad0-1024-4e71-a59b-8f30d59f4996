import { useState } from "react";
import { But<PERSON> } from "../components/ui/button";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "../components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "../components/ui/table";
import { Alert, AlertDescription } from "../components/ui/alert";
import { Badge } from "../components/ui/badge";
import { useNavigate } from "../lib/react-router-dom";
import { Upload, FileText, ArrowLeft, AlertTriangle, CheckCircle, X } from "lucide-react";

interface ValidationError {
  type: 'missing_column' | 'missing_data' | 'invalid_format';
  message: string;
  row?: number;
  column?: string;
}

interface PreviewData {
  headers: string[];
  rows: string[][];
  errors: ValidationError[];
}

const REQUIRED_COLUMNS = [
  'Employee ID',
  'First Name', 
  'Last Name',
  'Date of Birth',
  'Gender',
  'Department',
  'Salary',
  'Hire Date'
];

const FilePreview = () => {
  const navigate = useNavigate();
  const [dragActive, setDragActive] = useState(false);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [previewData, setPreviewData] = useState<PreviewData | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);

  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true);
    } else if (e.type === "dragleave") {
      setDragActive(false);
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);
    
    const files = e.dataTransfer.files;
    if (files && files[0]) {
      handleFileSelect(files[0]);
    }
  };

  const handleFileSelect = (file: File) => {
    setSelectedFile(file);
    processFile(file);
  };

  const processFile = async (file: File) => {
    setIsProcessing(true);
    
    // Simulate file processing
    setTimeout(() => {
      const mockData: PreviewData = {
        headers: ['Employee ID', 'First Name', 'Last Name', 'Date of Birth', 'Gender', 'Department', 'Salary', 'Hire Date'],
        rows: [
          ['001', 'John', 'Doe', '1985-03-15', 'Male', 'Engineering', '$75,000', '2020-01-15'],
          ['002', 'Jane', 'Smith', '1990-07-22', 'Female', 'Marketing', '$65,000', '2021-03-10'],
          ['003', 'Mike', 'Johnson', '1988-11-08', 'Male', 'Sales', '$70,000', '2019-08-20'],
          ['004', 'Sarah', 'Wilson', '1992-05-12', 'Female', 'HR', '$60,000', '2022-02-01'],
          ['005', 'David', 'Brown', '1987-09-30', 'Male', 'Finance', '$80,000', '2018-11-15']
        ],
        errors: []
      };
      
      // Add some validation errors for demo
      const missingColumns = REQUIRED_COLUMNS.filter(col => !mockData.headers.includes(col));
      if (missingColumns.length > 0) {
        mockData.errors.push({
          type: 'missing_column',
          message: `Missing required columns: ${missingColumns.join(', ')}`
        });
      }
      
      setPreviewData(mockData);
      setIsProcessing(false);
    }, 2000);
  };

  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files && files[0]) {
      handleFileSelect(files[0]);
    }
  };

  const handleProceedToProcessing = () => {
    navigate('?page=processing');
  };

  const handleBackToUpload = () => {
    navigate('?page=upload-census');
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
      {/* Header */}
      <header className="border-b bg-white/80 backdrop-blur-sm">
        <div className="container mx-auto px-4 py-4 flex justify-between items-center">
          <div className="flex items-center space-x-4">
            <Button variant="ghost" onClick={handleBackToUpload}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Upload
            </Button>
            <div className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
              File Preview & Validation
            </div>
          </div>
        </div>
      </header>

      <main className="container mx-auto px-4 py-8 max-w-7xl">
        {!selectedFile ? (
          <Card className="shadow-xl">
            <CardHeader>
              <CardTitle className="text-2xl bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                📄 Upload Census File for Preview
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div
                className={`border-2 border-dashed rounded-lg p-12 text-center transition-colors ${
                  dragActive 
                    ? 'border-blue-500 bg-blue-50' 
                    : 'border-gray-300 hover:border-blue-400 hover:bg-gray-50'
                }`}
                onDragEnter={handleDrag}
                onDragLeave={handleDrag}
                onDragOver={handleDrag}
                onDrop={handleDrop}
              >
                <Upload className="mx-auto h-16 w-16 text-gray-400 mb-4" />
                <h3 className="text-xl font-semibold text-gray-900 mb-2">
                  Drop your census file here
                </h3>
                <p className="text-gray-600 mb-6">
                  or click to browse and select a file
                </p>
                <input
                  type="file"
                  id="file-upload"
                  className="hidden"
                  accept=".csv,.xlsx,.xls"
                  onChange={handleFileInputChange}
                />
                <Button 
                  onClick={() => document.getElementById('file-upload')?.click()}
                  className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700"
                >
                  <FileText className="mr-2 h-4 w-4" />
                  Choose File
                </Button>
                <p className="text-sm text-gray-500 mt-4">
                  Supports CSV, Excel (.xlsx, .xls) files up to 10MB
                </p>
              </div>
            </CardContent>
          </Card>
        ) : (
          <div className="space-y-6">
            {/* File Info */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <FileText className="h-5 w-5" />
                  File Information
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <p className="text-sm text-gray-600">File Name</p>
                    <p className="font-medium">{selectedFile.name}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">File Size</p>
                    <p className="font-medium">{(selectedFile.size / 1024 / 1024).toFixed(2)} MB</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">File Type</p>
                    <p className="font-medium">{selectedFile.type || 'Unknown'}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {isProcessing ? (
              <Card>
                <CardContent className="p-12 text-center">
                  <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
                  <h3 className="text-xl font-semibold mb-2">Processing File...</h3>
                  <p className="text-gray-600">Validating data structure and content</p>
                </CardContent>
              </Card>
            ) : previewData && (
              <>
                {/* Validation Results */}
                {previewData.errors.length > 0 ? (
                  <Alert className="border-red-200 bg-red-50">
                    <AlertTriangle className="h-4 w-4 text-red-600" />
                    <AlertDescription className="text-red-800">
                      <strong>Validation Issues Found:</strong>
                      <ul className="mt-2 space-y-1">
                        {previewData.errors.map((error, index) => (
                          <li key={index}>• {error.message}</li>
                        ))}
                      </ul>
                    </AlertDescription>
                  </Alert>
                ) : (
                  <Alert className="border-green-200 bg-green-50">
                    <CheckCircle className="h-4 w-4 text-green-600" />
                    <AlertDescription className="text-green-800">
                      <strong>File validation passed!</strong> Your census file is ready for processing.
                    </AlertDescription>
                  </Alert>
                )}

                {/* Data Preview */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center justify-between">
                      <span>Data Preview</span>
                      <Badge variant="secondary">
                        {previewData.rows.length} employees
                      </Badge>
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="overflow-x-auto">
                      <Table>
                        <TableHeader>
                          <TableRow>
                            {previewData.headers.map((header, index) => (
                              <TableHead key={index} className="font-semibold">
                                {header}
                              </TableHead>
                            ))}
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {previewData.rows.slice(0, 10).map((row, rowIndex) => (
                            <TableRow key={rowIndex}>
                              {row.map((cell, cellIndex) => (
                                <TableCell key={cellIndex}>{cell}</TableCell>
                              ))}
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </div>
                    {previewData.rows.length > 10 && (
                      <p className="text-sm text-gray-600 mt-4 text-center">
                        Showing first 10 rows of {previewData.rows.length} total employees
                      </p>
                    )}
                  </CardContent>
                </Card>

                {/* Action Buttons */}
                <div className="flex justify-between">
                  <Button 
                    variant="outline" 
                    onClick={() => {
                      setSelectedFile(null);
                      setPreviewData(null);
                    }}
                  >
                    <X className="mr-2 h-4 w-4" />
                    Choose Different File
                  </Button>
                  
                  <Button 
                    onClick={handleProceedToProcessing}
                    className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700"
                    disabled={previewData.errors.length > 0}
                  >
                    <CheckCircle className="mr-2 h-4 w-4" />
                    Proceed to Analysis
                  </Button>
                </div>
              </>
            )}
          </div>
        )}
      </main>
    </div>
  );
};

export default FilePreview;
