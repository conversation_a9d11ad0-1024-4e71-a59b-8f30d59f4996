import { <PERSON><PERSON> } from "../components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "../components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "../components/ui/tabs";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "../components/ui/table";
import { Switch } from "../components/ui/switch";
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from "../components/ui/alert-dialog";
import { useNavigate, useSearchParams } from "../lib/react-router-dom";
import { Users, DollarSign, TrendingUp, Shield, Share2, Download, ArrowLeft, Building, User, Phone, Mail, MessageSquare } from "lucide-react";
import { useState } from "react";
import EmployeeDetailModal from "../components/EmployeeDetailModal";

const HRInsight = () => {
  const navigate = useNavigate();
  const searchParams = useSearchParams();
  const companyName = searchParams.get('company') || 'Your Company';
  const [selectedEmployee, setSelectedEmployee] = useState<any>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [smsNotificationsEnabled, setSmsNotificationsEnabled] = useState(false);
  const [showSmsDialog, setShowSmsDialog] = useState(false);
  const [pendingSmsState, setPendingSmsState] = useState<boolean | null>(null);

  const insights = [
    {
      icon: DollarSign,
      label: "Employer Saving Opportunity", 
      value: "$127,500",
      color: "green",
      change: "Potential annual savings"
    },
    {
      icon: Shield,
      label: "Risk Score",
      value: "Low",
      color: "blue", 
      change: "Better than average"
    },
    {
      icon: TrendingUp,
      label: "Benchmark Rate",
      value: "8.5%",
      color: "purple",
      change: "Below industry average"
    }
  ];

  const recommendations = [{
    category: "Cost Savings",
    items: ["Switch to HSA-eligible plan could save $127,500 annually", "Optimize contribution strategy for younger demographics", "Consider wellness programs to reduce risk factors"]
  }, {
    category: "Plan Improvements", 
    items: ["Add vision benefits - 68% of employees would benefit", "Increase mental health coverage options", "Consider flexible spending accounts"]
  }, {
    category: "Employee Engagement",
    items: ["Implement benefits education program", "Add telemedicine options for remote workers", "Create benefits portal for easy access"]
  }];

  const employees = [{
    id: 1,
    name: "John Smith",
    age: 34,
    department: "Engineering",
    plan: "Premium Health + Dental",
    dependents: 2,
    premium: "$850/month"
  }, {
    id: 2,
    name: "Sarah Johnson",
    age: 28,
    department: "Marketing",
    plan: "Standard Health",
    dependents: 0,
    premium: "$420/month"
  }, {
    id: 3,
    name: "Michael Chen",
    age: 41,
    department: "Finance",
    plan: "Premium Health + Vision",
    dependents: 3,
    premium: "$920/month"
  }, {
    id: 4,
    name: "Emily Davis",
    age: 36,
    department: "HR",
    plan: "Premium Health + Dental",
    dependents: 1,
    premium: "$650/month"
  }, {
    id: 5,
    name: "Robert Wilson",
    age: 29,
    department: "Sales",
    plan: "Basic Health",
    dependents: 0,
    premium: "$320/month"
  }, {
    id: 6,
    name: "Lisa Anderson",
    age: 43,
    department: "Operations",
    plan: "Premium Health + Vision + Dental",
    dependents: 2,
    premium: "$1080/month"
  }];

  const handleShare = () => {
    const shareUrl = `${window.location.origin}/shared-insight/${companyName.toLowerCase().replace(/\s+/g, '-')}`;
    navigator.clipboard.writeText(shareUrl);
    console.log('Share link copied');
  };

  const handleEmployeeClick = (employee: any) => {
    setSelectedEmployee(employee);
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
    setSelectedEmployee(null);
  };

  const handleSmsToggle = (enabled: boolean) => {
    setPendingSmsState(enabled);
    setShowSmsDialog(true);
  };

  const confirmSmsToggle = () => {
    if (pendingSmsState !== null) {
      setSmsNotificationsEnabled(pendingSmsState);
      console.log(`SMS notifications ${pendingSmsState ? 'enabled' : 'disabled'} for all employees`);
    }
    setShowSmsDialog(false);
    setPendingSmsState(null);
  };

  const cancelSmsToggle = () => {
    setShowSmsDialog(false);
    setPendingSmsState(null);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
      {/* Header */}
      <header className="border-b bg-white/80 backdrop-blur-sm">
        <div className="container mx-auto px-4 py-4 flex justify-between items-center">
          <div className="flex items-center space-x-4">
            <Button variant="ghost" onClick={() => navigate("?page=dashboard")}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back
            </Button>
            <div className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">BenOsphere HR</div>
          </div>
          <div className="flex items-center space-x-2">
            <Button variant="outline" onClick={handleShare}>
              <Share2 className="h-4 w-4 mr-2" />
              Share Report
            </Button>
            <Button className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700">
              <Download className="h-4 w-4 mr-2" />
              Download PDF
            </Button>
          </div>
        </div>
      </header>

      <main className="container mx-auto px-4 py-12 max-w-6xl">
        <div className="text-center mb-12">
          <div className="w-16 h-16 mx-auto mb-4 bg-gradient-to-r from-blue-100 to-purple-100 rounded-full flex items-center justify-center">
            <Building className="h-8 w-8 bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent" />
          </div>
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            📊 Benefits Insights for {companyName}
          </h1>
          <p className="text-xl text-gray-600">
            Average Age: 36 • Comprehensive analysis of your employee benefits program
          </p>
        </div>

        {/* SMS Notifications Control */}
        <Card className="shadow-lg mb-8 border-blue-200">
          <CardHeader>
            <CardTitle className="text-xl bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent flex items-center">
              <MessageSquare className="h-5 w-5 mr-2" />
              Employee Communications
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between p-4 bg-blue-50 rounded-lg border border-blue-200">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                  <MessageSquare className="h-5 w-5 text-blue-600" />
                </div>
                <div>
                  <h3 className="font-semibold text-blue-900">SMS Notifications for All Employees</h3>
                  <p className="text-sm text-blue-700">
                    {smsNotificationsEnabled ? "Employees will receive SMS updates about benefits changes and reminders" : "Enable SMS notifications for benefits updates. Employees will receive a welcome message to opt in and give consent."}
                  </p>
                </div>
              </div>
              <div className="flex items-center space-x-3">
                <span className="text-sm font-medium text-blue-800">
                  {smsNotificationsEnabled ? "Enabled" : "Disabled"}
                </span>
                <Switch checked={smsNotificationsEnabled} onCheckedChange={handleSmsToggle} />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Key Insights Grid */}
        <div className="grid md:grid-cols-3 gap-6 mb-12">
          {insights.map((insight, index) => {
            const Icon = insight.icon;
            const colorClasses = {
              blue: "bg-blue-100 text-blue-600 border-blue-200",
              green: "bg-green-100 text-green-600 border-green-200",
              purple: "bg-purple-100 text-purple-600 border-purple-200",
              orange: "bg-orange-100 text-orange-600 border-orange-200"
            };
            return (
              <Card key={index} className={`border-2 ${colorClasses[insight.color as keyof typeof colorClasses]} shadow-lg hover:shadow-xl transition-shadow`}>
                <CardContent className="p-6 text-center">
                  <div className="w-12 h-12 mx-auto mb-4 bg-white rounded-full flex items-center justify-center">
                    <Icon className="h-6 w-6" />
                  </div>
                  <h3 className="font-semibold text-gray-700 mb-2">{insight.label}</h3>
                  <p className="text-2xl font-bold text-gray-900 mb-1">{insight.value}</p>
                  <p className="text-xs text-gray-500">{insight.change}</p>
                </CardContent>
              </Card>
            );
          })}
        </div>

        {/* Employee Insights Section */}
        <Card className="shadow-xl mb-8">
          <CardHeader>
            <CardTitle className="text-2xl bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">👥 Employee Insights</CardTitle>
          </CardHeader>
          <CardContent>
            <Tabs defaultValue="employees" className="w-full">
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger value="employees">Employee Directory</TabsTrigger>
                <TabsTrigger value="plans">Plan Distribution</TabsTrigger>
              </TabsList>
              
              <TabsContent value="employees" className="mt-6">
                <div className="overflow-x-auto">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Employee</TableHead>
                        <TableHead>Age</TableHead>
                        <TableHead>Department</TableHead>
                        <TableHead>Current Plan</TableHead>
                        <TableHead>Dependents</TableHead>
                        <TableHead>Monthly Premium</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {employees.map(employee => <TableRow key={employee.id} className="cursor-pointer hover:bg-gray-50 transition-colors" onClick={() => handleEmployeeClick(employee)}>
                          <TableCell>
                            <div className="flex items-center space-x-3">
                              <div className="w-8 h-8 bg-gradient-to-r from-blue-100 to-purple-100 rounded-full flex items-center justify-center">
                                <User className="h-4 w-4 text-blue-600" />
                              </div>
                              <span className="font-medium">{employee.name}</span>
                            </div>
                          </TableCell>
                          <TableCell>{employee.age}</TableCell>
                          <TableCell>
                            <span className="px-2 py-1 bg-gray-100 text-gray-800 rounded-full text-xs">
                              {employee.department}
                            </span>
                          </TableCell>
                          <TableCell>
                            <span className="px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs">
                              {employee.plan}
                            </span>
                          </TableCell>
                          <TableCell>{employee.dependents}</TableCell>
                          <TableCell className="font-semibold text-green-600">{employee.premium}</TableCell>
                        </TableRow>)}
                    </TableBody>
                  </Table>
                </div>
              </TabsContent>
              
              <TabsContent value="plans" className="mt-6">
                <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
                  <Card className="border-blue-200 bg-blue-50">
                    <CardContent className="p-6">
                      <h3 className="font-semibold text-blue-800 mb-2">Premium Plans</h3>
                      <p className="text-2xl font-bold text-blue-600 mb-1">67%</p>
                      <p className="text-sm text-blue-700">29 employees enrolled</p>
                    </CardContent>
                  </Card>
                  
                  <Card className="border-green-200 bg-green-50">
                    <CardContent className="p-6">
                      <h3 className="font-semibold text-green-800 mb-2">Standard Plans</h3>
                      <p className="text-2xl font-bold text-green-600 mb-1">21%</p>
                      <p className="text-sm text-green-700">9 employees enrolled</p>
                    </CardContent>
                  </Card>
                  
                  <Card className="border-orange-200 bg-orange-50">
                    <CardContent className="p-6">
                      <h3 className="font-semibold text-orange-800 mb-2">Basic Plans</h3>
                      <p className="text-2xl font-bold text-orange-600 mb-1">12%</p>
                      <p className="text-sm text-orange-700">5 employees enrolled</p>
                    </CardContent>
                  </Card>
                </div>
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>

        {/* Detailed Analysis */}
        <div className="grid lg:grid-cols-2 gap-8 mb-12">
          {/* Demographics Breakdown */}
          <Card className="shadow-lg">
            <CardHeader>
              <CardTitle className="text-xl bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">👥 Employee Demographics</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex justify-between items-center p-3 bg-blue-50 rounded-lg">
                  <span className="font-medium">Age 25-35</span>
                  <span className="font-bold text-blue-600">18 employees (42%)</span>
                </div>
                <div className="flex justify-between items-center p-3 bg-green-50 rounded-lg">
                  <span className="font-medium">Age 36-50</span>
                  <span className="font-bold text-green-600">20 employees (46%)</span>
                </div>
                <div className="flex justify-between items-center p-3 bg-orange-50 rounded-lg">
                  <span className="font-medium">Age 50+</span>
                  <span className="font-bold text-orange-600">5 employees (12%)</span>
                </div>
                <div className="flex justify-between items-center p-3 bg-purple-50 rounded-lg">
                  <span className="font-medium">Employees with Families</span>
                  <span className="font-bold text-purple-600">28 employees (65%)</span>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Cost Analysis */}
          <Card className="shadow-lg">
            <CardHeader>
              <CardTitle className="text-xl bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">💰 Cost Analysis</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="p-4 bg-green-50 rounded-lg border border-green-200">
                  <h4 className="font-semibold text-green-800 mb-2">Potential Annual Savings</h4>
                  <p className="text-2xl font-bold text-green-600">$127,500</p>
                  <p className="text-sm text-green-700">By optimizing plan structure</p>
                </div>
                <div className="p-4 bg-blue-50 rounded-lg border border-blue-200">
                  <h4 className="font-semibold text-blue-800 mb-2">Current Annual Premium</h4>
                  <p className="text-2xl font-bold text-blue-600">$486,000</p>
                  <p className="text-sm text-blue-700">Average $11,300 per employee</p>
                </div>
                <div className="p-4 bg-orange-50 rounded-lg border border-orange-200">
                  <h4 className="font-semibold text-orange-800 mb-2">Benchmarked Rate</h4>
                  <p className="text-2xl font-bold text-orange-600">8.5% below</p>
                  <p className="text-sm text-orange-700">Industry average</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Recommendations */}
        <Card className="shadow-xl mb-8">
          <CardHeader>
            <CardTitle className="text-2xl bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">💡 Personalized Recommendations</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid md:grid-cols-3 gap-6">
              {recommendations.map((rec, index) => <div key={index} className="space-y-3">
                  <h3 className="font-semibold text-lg text-gray-900">{rec.category}</h3>
                  <ul className="space-y-2">
                    {rec.items.map((item, itemIndex) => <li key={itemIndex} className="flex items-start">
                        <span className="text-blue-500 mr-2 mt-1">✓</span>
                        <span className="text-sm text-gray-700">{item}</span>
                      </li>)}
                  </ul>
                </div>)}
            </div>
          </CardContent>
        </Card>

        {/* Viral Sharing CTA */}
        <Card className="bg-gradient-to-r from-blue-600 to-purple-600 text-white border-0 shadow-2xl">
          <CardContent className="p-8 text-center">
            <h2 className="text-2xl font-bold mb-4">🚀 Share These Insights</h2>
            <p className="text-blue-100 mb-6 text-lg">
              Help other HR professionals discover BenOsphere&apos;s powerful census analysis
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="lg" className="bg-white text-blue-600 hover:bg-gray-100 px-8 py-4" onClick={handleShare}>
                <Share2 className="mr-2 h-5 w-5" />
                📤 Share with Network
              </Button>
              <Button variant="outline" size="lg" className="border-white text-white hover:bg-white/10 px-8 py-4" onClick={() => navigate('/hr-upload')}>
                🔄 Analyze Another Census
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Next Steps */}
        <Card className="mt-8 bg-amber-50 border-amber-200">
          <CardContent className="p-6">
            <h3 className="font-semibold text-amber-800 mb-3">🎯 Recommended Next Steps:</h3>
            <ul className="text-amber-700 space-y-1 text-sm">
              <li>• Schedule a consultation with our benefits experts</li>
              <li>• Request detailed plan proposals based on these insights</li>
              <li>• Share this report with your leadership team</li>
              <li>
            </li>
            </ul>
          </CardContent>
        </Card>
      </main>

      {/* Employee Detail Modal */}
      <EmployeeDetailModal employee={selectedEmployee} isOpen={isModalOpen} onClose={handleCloseModal} />

      {/* SMS Confirmation Dialog */}
      <AlertDialog open={showSmsDialog} onOpenChange={setShowSmsDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>
              {pendingSmsState ? "Enable SMS Notifications?" : "Disable SMS Notifications?"}
            </AlertDialogTitle>
            <AlertDialogDescription>
              {pendingSmsState ? "Employees will get a welcome message to opt in and consent to SMS updates. Msg & data rates may apply." : "SMS notifications will be turned off, and employees will stop getting text updates."}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={cancelSmsToggle}>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={confirmSmsToggle}>
              {pendingSmsState ? "Enable SMS" : "Disable SMS"}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
};

export default HRInsight;
