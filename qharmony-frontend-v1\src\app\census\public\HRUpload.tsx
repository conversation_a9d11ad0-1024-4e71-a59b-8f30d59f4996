
import { useState } from "react";
import { But<PERSON> } from "../components/ui/button";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardH<PERSON>er, CardTitle } from "../components/ui/card";
import { Input } from "../components/ui/input";
import { Label } from "../components/ui/label";
import { useNavigate } from "../lib/react-router-dom";
import { Upload, FileText, ArrowLeft, Building } from "lucide-react";
import ProfileHandler from "../components/ProfileHandler";

const HRUpload = () => {
  const navigate = useNavigate();
  const [dragActive, setDragActive] = useState(false);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);


  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true);
    } else if (e.type === "dragleave") {
      setDragActive(false);
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);
    
    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      setSelectedFile(e.dataTransfer.files[0]);
    }
  };

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      setSelectedFile(e.target.files[0]);
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (selectedFile) {
      navigate(`/hr-processing`);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
      {/* Header */}
      <header className="border-b bg-white/80 backdrop-blur-sm">
        <div className="container mx-auto px-4 py-4 flex justify-between items-center">
          <div className="flex items-center space-x-4">
            <Button variant="ghost" onClick={() => navigate('/')}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back
            </Button>
            <div className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">BenOsphere HR</div>
          </div>
          <ProfileHandler />
        </div>
      </header>

      <main className="container mx-auto px-4 py-16 max-w-4xl">
        <div className="text-center mb-12">
          <div className="w-20 h-20 mx-auto mb-6 bg-blue-100 rounded-full flex items-center justify-center">
            <Building className="h-10 w-10 text-blue-600" />
          </div>
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            🏢 HR Portal - Upload Employee Census
          </h1>
          <p className="text-xl text-gray-600">
            Get instant insights into your employee benefits and potential cost savings
          </p>
        </div>

        <form onSubmit={handleSubmit}>
          <Card className="shadow-xl border-0">
            <CardHeader>
              <CardTitle className="text-center text-2xl text-blue-700">
                Upload Employee Census
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Drag & Drop Area */}
              <div
                className={`border-2 border-dashed rounded-xl p-12 text-center transition-all ${
                  dragActive 
                    ? "border-blue-500 bg-blue-50" 
                    : "border-gray-300 hover:border-blue-400 hover:bg-gray-50"
                }`}
                onDragEnter={handleDrag}
                onDragLeave={handleDrag}
                onDragOver={handleDrag}
                onDrop={handleDrop}
              >
                <div className="space-y-4">
                  <div className="mx-auto w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center">
                    <FileText className="h-8 w-8 text-blue-600" />
                  </div>
                  
                  {selectedFile ? (
                    <div>
                      <p className="text-lg font-semibold text-blue-600">
                        ✅ {selectedFile.name}
                      </p>
                      <p className="text-sm text-gray-500">
                        {(selectedFile.size / 1024 / 1024).toFixed(2)} MB
                      </p>
                    </div>
                  ) : (
                    <div>
                      <p className="text-xl font-semibold text-gray-700 mb-2">
                        📄 Drag & drop employee census file
                      </p>
                      <p className="text-gray-500 mb-4">CSV, Excel, or PDF format</p>
                      <input
                        type="file"
                        id="file-upload"
                        className="hidden"
                        accept=".csv,.xlsx,.xls,.pdf"
                        onChange={handleFileSelect}
                      />
                      <label htmlFor="file-upload">
                        <Button 
                          type="button" 
                          variant="outline" 
                          className="cursor-pointer bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white border-0" 
                          asChild
                        >
                          <span>
                            🔍 Browse Files
                          </span>
                        </Button>
                      </label>
                    </div>
                  )}
                </div>
              </div>

              {/* Submit Button */}
              <div className="text-center">
                <Button 
                  type="submit"
                  size="lg" 
                  className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-12 py-4 text-lg"
                  disabled={!selectedFile}
                >
                  <Upload className="mr-2 h-5 w-5" />
                  ➡️ Analyze Our Census
                </Button>
              </div>

              {!selectedFile && (
                <p className="text-center text-sm text-gray-400">
                  Please select a file to upload
                </p>
              )}
            </CardContent>
          </Card>
        </form>

        {/* Info Cards */}
        <div className="grid md:grid-cols-2 gap-6 mt-8">
          <Card className="bg-blue-50 border-blue-200">
            <CardContent className="p-6">
              <h3 className="font-semibold text-blue-900 mb-2">💡 What you&apos;ll get:</h3>
              <ul className="text-blue-800 space-y-1 text-sm">
                <li>• Detailed benefits analysis for your employees</li>
                <li>• Cost optimization recommendations</li>
                <li>• Benchmark against similar companies</li>
                <li>• Risk assessment and planning insights</li>
              </ul>
            </CardContent>
          </Card>

          <Card className="bg-purple-50 border-purple-200">
            <CardContent className="p-6">
              <h3 className="font-semibold text-purple-900 mb-2">🔒 Your data is secure:</h3>
              <ul className="text-purple-800 space-y-1 text-sm">
                <li>• All data is encrypted and HIPAA compliant</li>
                <li>• Files are automatically deleted after analysis</li>
                <li>• Only aggregated insights are stored</li>
                <li>• Full privacy and confidentiality guaranteed</li>
              </ul>
            </CardContent>
          </Card>
        </div>
      </main>
    </div>
  );
};

export default HRUpload;
