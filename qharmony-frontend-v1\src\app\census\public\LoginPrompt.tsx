import { useState } from "react";
import { <PERSON><PERSON> } from "../components/ui/button";
import { Card, CardContent } from "../components/ui/card";
import { Input } from "../components/ui/input";
import { Avatar, AvatarImage, AvatarFallback } from "../components/ui/avatar";
import { useNavigate } from "../lib/react-router-dom";
import { Mail, ArrowLeft, Check, Shield } from "lucide-react";
const LoginPrompt = () => {
  const navigate = useNavigate();
  const [email, setEmail] = useState("");
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState("");

  const handleSendMagicLink = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!email) return;

    setIsLoading(true);
    setError("");

    try {
      // Use the same selfOnboard function as the main login page
      const { selfOnboard } = await import('@/middleware/user_middleware');
      const responseMessage = await selfOnboard(email);

      if (responseMessage === "magic_link_sent") {
        setIsSubmitted(true);
      } else if (responseMessage === "ask_admin_to_add") {
        setError('No account found. Please contact your admin to get access.');
      } else {
        setError('Failed to send magic link. Please try again.');
      }
    } catch (err) {
      console.error('Login error:', err);
      setError('Network error. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };
  const handleProceedToUpload = () => {
    navigate('/upload-census');
  };
  const benefits = ["Complete cost analysis and potential savings breakdown", "Detailed risk segmentation by age, family status, and more", "Benchmarking against similar industry groups", "Personalized plan recommendations and add-on suggestions", "Export capabilities and white-label client presentations"];
  const brokerTestimonials = [{
    name: "Sarah M.",
    image: "https://images.unsplash.com/photo-*************-2616b612b786?w=150&h=150&fit=crop&crop=face",
    text: "This platform has revolutionized how I analyze census data for my clients."
  }, {
    name: "Michael R.",
    image: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face",
    text: "The detailed insights help me provide better recommendations to employers."
  }, {
    name: "Jennifer L.",
    image: "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face",
    text: "I save hours of work with the automated analysis and reporting features."
  }];
  return <div className="min-h-screen bg-gray-50 flex flex-col px-4 py-6">
      {/* Header */}
      <div className="absolute top-4 left-4">
        <Button variant="ghost" onClick={() => navigate('/')} className="text-gray-600 hover:bg-gray-100 p-2">
          <ArrowLeft className="h-4 w-4" />
        </Button>
      </div>

      {/* Main Content */}
      <div className="flex-1 flex items-center justify-center">
        <div className="w-full max-w-5xl">
          {/* Title Section */}
          <div className="text-center mb-6">
            <div className="w-10 h-10 bg-gradient-to-r from-orange-400 to-orange-500 rounded-lg flex items-center justify-center mx-auto mb-3">
              <span className="text-white text-lg">🔓</span>
            </div>
            <h1 className="text-2xl font-bold text-gray-900 mb-2">Sign in to Upload Census</h1>
            <p className="text-gray-600 text-sm">Enter your email to get instant access to our census analysis platform.</p>
          </div>

          {!isSubmitted ? <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Left Side - Sign In Form */}
              <Card className="bg-white shadow-lg">
                <CardContent className="p-6">
                  <h2 className="text-xl font-semibold text-gray-900 mb-4">Get Instant Access</h2>
                  
                  <form onSubmit={handleSendMagicLink} className="space-y-4">
                    <div>
                      <label htmlFor="email" className="flex items-center text-sm font-medium text-gray-700 mb-2">
                        <Mail className="h-4 w-4 mr-2 text-blue-500" />
                        Email Address
                      </label>
                      <Input id="email" type="email" placeholder="Enter your email address" value={email} onChange={e => setEmail(e.target.value)} required className="h-11 text-base border-gray-300 focus:border-blue-500 focus:ring-blue-500" />
                    </div>

                    {error && (
                      <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-md">
                        <p className="text-sm text-red-600">{error}</p>
                      </div>
                    )}

                    <Button
                      type="submit"
                      className="w-full h-11 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-medium"
                      disabled={!email || isLoading}
                    >
                      <Mail className="h-4 w-4 mr-2" />
                      {isLoading ? 'Sending...' : 'Send Magic Link'}
                    </Button>
                  </form>

                  {/* HIPAA Compliance Message */}
                  <div className="mt-4 flex items-center justify-center space-x-2 text-sm text-gray-600">
                    <Shield className="h-4 w-4 text-green-600" />
                    <span>We&apos;re HIPAA compliant and your data is in safe hands</span>
                  </div>
                </CardContent>
              </Card>

              {/* Right Side - Benefits & Testimonials */}
              <Card className="bg-blue-50 border-blue-200">
                <CardContent className="p-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">What you&apos;ll unlock:</h3>
                  
                  <div className="space-y-3 mb-6">
                    {benefits.map((benefit, index) => <div key={index} className="flex items-start space-x-3">
                        <div className="flex-shrink-0 w-5 h-5 rounded-full bg-green-500 flex items-center justify-center mt-0.5">
                          <Check className="h-3 w-3 text-white" />
                        </div>
                        <p className="text-gray-700 text-sm leading-relaxed">{benefit}</p>
                      </div>)}
                  </div>

                  {/* Broker Testimonials */}
                  <div>
                    <h4 className="text-base font-medium text-gray-900 mb-3">Brokers like you are enjoying it:</h4>
                    <div className="space-y-3">
                      {brokerTestimonials.map((testimonial, index) => <div key={index} className="flex items-start space-x-3">
                          <Avatar className="h-8 w-8 flex-shrink-0">
                            <AvatarImage src={testimonial.image} alt={testimonial.name} />
                            <AvatarFallback>{testimonial.name.charAt(0)}</AvatarFallback>
                          </Avatar>
                          <div>
                            <p className="text-xs text-gray-700 italic">&quot;{testimonial.text}&quot;</p>
                            <p className="text-xs text-gray-500 mt-1">- {testimonial.name}</p>
                          </div>
                        </div>)}
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div> : <Card className="bg-white shadow-lg max-w-md mx-auto">
              <CardContent className="p-6 text-center">
                <div className="w-16 h-16 mx-auto mb-4 bg-green-100 rounded-full flex items-center justify-center">
                  <Mail className="h-8 w-8 text-green-600" />
                </div>
                
                <h2 className="text-2xl font-bold text-gray-900 mb-3">
                  ✅ Magic Link Sent!
                </h2>
                
                <p className="text-lg text-gray-600 mb-4">
                  We&apos;ve sent a secure login link to <strong>{email}</strong>
                </p>
                
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
                  <p className="text-blue-800 text-sm">
                    <strong>Next steps:</strong>
                    <br />
                    1. Check your email inbox (and spam folder)
                    <br />
                    2. Click the login link we sent
                    <br />
                    3. Access your census upload page
                  </p>
                </div>

                <div className="space-y-2">
                  <Button onClick={handleProceedToUpload} className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-base py-2">
                    Continue to Census Upload
                  </Button>
                  
                  
                  
                  <Button variant="link" onClick={() => navigate('/')} className="w-full text-gray-500">
                    Back to Home
                  </Button>
                </div>
              </CardContent>
            </Card>}
        </div>
      </div>

      {/* Footer */}
      <div className="text-center mt-4">
        <p className="text-xs text-gray-500">©2020 Felix All rights reserved.</p>
      </div>
    </div>;
};
export default LoginPrompt;