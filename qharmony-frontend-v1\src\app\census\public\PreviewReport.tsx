
import { But<PERSON> } from "../components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "../components/ui/card";
import { useNavigate } from "../lib/react-router-dom";
import { Lock, Users, DollarSign, TrendingUp, Shield, ArrowLeft } from "lucide-react";
import ProfileHandler from "../components/ProfileHandler";

const PreviewReport = () => {
  const navigate = useNavigate();

  const visibleInsights = [
    { icon: Users, label: "Total Employees", value: "43", color: "blue" },
    { icon: TrendingUp, label: "Average Age", value: "36", color: "green" },
    { icon: Users, label: "Dependents per Employee", value: "1.3", color: "purple" },
    { icon: Shield, label: "Suggested Plan Type", value: "PPO + HSA Combo", color: "orange" }
  ];

  const lockedInsights = [
    "🔒 Cost-saving opportunities",
    "🔒 Risk segmentation (e.g. 12 employees over 50)",
    "🔒 Contribution benchmark vs. similar groups", 
    "🔒 Suggested add-ons: vision, disability, life"
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
      {/* Header */}
      <header className="border-b bg-white/80 backdrop-blur-sm">
        <div className="container mx-auto px-4 py-4 flex justify-between items-center">
          <div className="flex items-center space-x-4">
            <Button variant="ghost" onClick={() => navigate('/upload')}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back
            </Button>
            <div className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">BenOsphere</div>
          </div>
          <ProfileHandler />
        </div>
      </header>

      <main className="container mx-auto px-4 py-12 max-w-6xl">
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            ✅ Smart Snapshot Ready — Here&apos;s a Preview
          </h1>
          <p className="text-xl text-gray-600">
            Your census analysis is complete. See key insights below.
          </p>
        </div>

        {/* Visible Insights Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
          {visibleInsights.map((insight, index) => {
            const Icon = insight.icon;
            const colorClasses = {
              blue: "bg-blue-100 text-blue-600 border-blue-200",
              green: "bg-green-100 text-green-600 border-green-200", 
              purple: "bg-purple-100 text-purple-600 border-purple-200",
              orange: "bg-orange-100 text-orange-600 border-orange-200"
            };
            
            return (
              <Card key={index} className={`border-2 ${colorClasses[insight.color as keyof typeof colorClasses]} shadow-lg hover:shadow-xl transition-shadow`}>
                <CardContent className="p-6 text-center">
                  <div className="w-12 h-12 mx-auto mb-4 bg-white rounded-full flex items-center justify-center">
                    <Icon className="h-6 w-6" />
                  </div>
                  <h3 className="font-semibold text-gray-700 mb-2">{insight.label}</h3>
                  <p className="text-2xl font-bold text-gray-900">{insight.value}</p>
                  <div className="mt-2">
                    <span className="text-green-600 text-sm font-medium">✔️</span>
                  </div>
                </CardContent>
              </Card>
            );
          })}
        </div>

        {/* Locked Insights */}
        <Card className="shadow-xl border-0 mb-8">
          <CardHeader className="bg-gradient-to-r from-gray-50 to-gray-100 border-b">
            <CardTitle className="flex items-center text-xl">
              <Lock className="mr-2 h-5 w-5 text-gray-500" />
              Additional Insights (Login Required)
            </CardTitle>
          </CardHeader>
          <CardContent className="p-8">
            <div className="grid md:grid-cols-2 gap-4">
              {lockedInsights.map((insight, index) => (
                <div key={index} className="flex items-center p-4 bg-gray-50 rounded-lg border-2 border-dashed border-gray-300">
                  <span className="text-gray-500 mr-3">
                    <Lock className="h-4 w-4" />
                  </span>
                  <span className="text-gray-600 font-medium">{insight}</span>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Preview Note */}
        <Card className="bg-amber-50 border-amber-200 shadow-lg mb-8">
          <CardContent className="p-6">
            <div className="flex items-start space-x-3">
              <div className="bg-amber-100 p-2 rounded-full">
                <Lock className="h-5 w-5 text-amber-600" />
              </div>
              <div>
                <h3 className="font-semibold text-amber-800 mb-2">Preview Mode</h3>
                <p className="text-amber-700">
                  You&apos;re seeing a preview of the insights. Log in to unlock the full analysis, plan suggestions, and opportunity flags.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* CTA Section */}
        <div className="text-center space-y-6">
          <Card className="bg-gradient-to-r from-blue-600 to-purple-600 text-white border-0 shadow-2xl">
            <CardContent className="p-8">
              <h2 className="text-2xl font-bold mb-4">Ready to see the full picture?</h2>
              <p className="text-blue-100 mb-6 text-lg">
                Unlock detailed cost analysis, risk insights, and personalized recommendations for this group.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button 
                  size="lg" 
                  className="bg-white text-blue-600 hover:bg-gray-100 px-8 py-4"
                  onClick={() => navigate('/login')}
                >
                  🔓 Unlock Full Report
                </Button>
                <Button 
                  variant="outline" 
                  size="lg" 
                  className="border-white text-white hover:bg-white/10 px-8 py-4"
                  onClick={() => navigate('/upload')}
                >
                  📤 Upload Another Census
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Sample Metrics Teaser */}
          <div className="grid md:grid-cols-3 gap-6 mt-8">
            <Card className="border-dashed border-2 border-gray-300 opacity-75">
              <CardContent className="p-6 text-center">
                <DollarSign className="h-8 w-8 mx-auto mb-2 text-gray-400" />
                <p className="text-sm text-gray-500">Potential Savings</p>
                <p className="text-xl font-bold text-gray-400">$XXX,XXX</p>
              </CardContent>
            </Card>
            
            <Card className="border-dashed border-2 border-gray-300 opacity-75">
              <CardContent className="p-6 text-center">
                <TrendingUp className="h-8 w-8 mx-auto mb-2 text-gray-400" />
                <p className="text-sm text-gray-500">Risk Score</p>
                <p className="text-xl font-bold text-gray-400">X.X/10</p>
              </CardContent>
            </Card>
            
            <Card className="border-dashed border-2 border-gray-300 opacity-75">
              <CardContent className="p-6 text-center">
                <Shield className="h-8 w-8 mx-auto mb-2 text-gray-400" />
                <p className="text-sm text-gray-500">Recommended Plans</p>
                <p className="text-xl font-bold text-gray-400">X Plans</p>
              </CardContent>
            </Card>
          </div>
        </div>
      </main>
    </div>
  );
};

export default PreviewReport;
