import { But<PERSON> } from "../components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "../components/ui/card";
import { useNavigate } from "../lib/react-router-dom";
import { Check, X, ArrowLeft, Star, Users, FileText, Shield } from "lucide-react";
import ProfileHandler from "../components/ProfileHandler";

const Pricing = () => {
  const navigate = useNavigate();

  const features = [
    {
      icon: "📊",
      name: "Reports Generated",
      free: "2 reports (1 credit each)",
      pro: "Based on plan credits",
      enterprise: "Unlimited"
    },
    {
      icon: "🔍",
      name: "Census Parsing & Enrichment",
      free: "Full Analysis",
      pro: "Full Analysis",
      enterprise: "Full Analysis"
    },
    {
      icon: "📄",
      name: "Smart Group Summary (PDF)",
      free: "Basic Report",
      pro: "Professional Report",
      enterprise: "White-Label Reports"
    },
    {
      icon: "🏷️",
      name: "Risk Analysis & Tagging",
      free: "Basic Insights",
      pro: "Advanced Analytics",
      enterprise: "Custom Analytics"
    },
    {
      icon: "💡",
      name: "Plan Recommendations",
      free: true,
      pro: true,
      enterprise: true
    },
    {
      icon: "🔖",
      name: "Smart Insights & Tags",
      free: "Standard Tags",
      pro: "Advanced Tags",
      enterprise: "Custom Tags"
    },
    {
      icon: "📈",
      name: "Benchmarking vs. Industry",
      free: false,
      pro: true,
      enterprise: true
    },
    {
      icon: "💰",
      name: "Cost Savings Opportunities",
      free: false,
      pro: true,
      enterprise: true
    },
    {
      icon: "🎯",
      name: "Upsell Recommendations",
      free: false,
      pro: true,
      enterprise: true
    },
    {
      icon: "👥",
      name: "Multi-User Access",
      free: "1 User",
      pro: "5 Users",
      enterprise: "Unlimited"
    },
    {
      icon: "📞",
      name: "Support",
      free: "Email",
      pro: "Priority Support",
      enterprise: "Dedicated Success Manager"
    },
    {
      icon: "🔗",
      name: "CRM Integration",
      free: false,
      pro: true,
      enterprise: true
    },
    {
      icon: "🔄",
      name: "API Access",
      free: false,
      pro: false,
      enterprise: true
    }
  ];

  const plans = [
    {
      name: "Free",
      price: "$0",
      period: "",
      description: "2 reports included",
      credits: "2 credits",
      popular: false,
      buttonText: "Current Plan",
      buttonVariant: "outline" as const,
      onClick: () => {}
    },
    {
      name: "Pro",
      price: "Starting at $99",
      period: "/month",
      description: "Credit-based pricing",
      credits: "Choose your plan",
      popular: true,
      buttonText: "Choose Pro Plan",
      buttonVariant: "default" as const,
      onClick: () => navigate('/billing'),
      subPlans: [
        { reports: "50 reports", price: "$99", credits: "25 credits" },
        { reports: "100 reports", price: "$199", credits: "50 credits" },
        { reports: "150 reports", price: "$299", credits: "75 credits" },
        { reports: "200 reports", price: "$479", credits: "100 credits" }
      ]
    },
    {
      name: "Enterprise",
      price: "Contact Sales",
      period: "",
      description: "Custom solutions for large teams",
      credits: "Unlimited",
      popular: false,
      buttonText: "Contact Sales",
      buttonVariant: "outline" as const,
      onClick: () => navigate('/billing')
    }
  ];

  const renderFeatureValue = (feature: any, plan: string) => {
    const planKey = plan.toLowerCase() === 'agency' ? 'enterprise' : plan.toLowerCase();
    const value = feature[planKey];

    if (typeof value === "boolean") {
      return value ? (
        <Check className="h-5 w-5 text-green-600 mx-auto" />
      ) : (
        <X className="h-5 w-5 text-red-500 mx-auto" />
      );
    }

    return <span className="text-sm text-center block">{value}</span>;
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
      {/* Header */}
      <header className="border-b bg-white/80 backdrop-blur-sm">
        <div className="container mx-auto px-4 py-4 flex justify-between items-center">
          <div className="flex items-center space-x-4">
            <Button variant="ghost" onClick={() => navigate("?page=dashboard")}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back
            </Button>
            <div className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">BenOsphere</div>
          </div>
          <ProfileHandler />
        </div>
      </header>

      <main className="container mx-auto px-4 py-16 max-w-7xl">
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            💎 BenOsphere Broker Plans
          </h1>
          <p className="text-xl text-gray-600 mb-8">
            Choose the plan that fits your business needs
          </p>
        </div>

        {/* Credit System Explanation */}
        <div className="bg-blue-50 rounded-2xl p-8 mb-12 text-center">
          <h3 className="text-2xl font-bold text-gray-900 mb-4">💳 Credit-Based Pricing</h3>
          <p className="text-lg text-gray-700 mb-4">Simple, transparent pricing based on usage</p>
          <div className="grid md:grid-cols-2 gap-6 max-w-2xl mx-auto">
            <div className="bg-white rounded-lg p-4 shadow-sm">
              <div className="text-3xl font-bold text-blue-600">1 Report</div>
              <div className="text-gray-600">= 2 Credits</div>
            </div>
            <div className="bg-white rounded-lg p-4 shadow-sm">
              <div className="text-3xl font-bold text-green-600">No Waste</div>
              <div className="text-gray-600">Pay only for what you use</div>
            </div>
          </div>
        </div>

        {/* Plan Cards */}
        <div className="grid md:grid-cols-3 gap-8 mb-16">
          {plans.map((plan, index) => (
            <Card key={index} className={`relative ${plan.popular ? 'border-2 border-blue-500 shadow-xl' : 'shadow-lg'}`}>
              {plan.popular && (
                <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                  <div className="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-4 py-1 rounded-full text-sm font-medium flex items-center">
                    <Star className="h-4 w-4 mr-1" />
                    Most Popular
                  </div>
                </div>
              )}
              <CardHeader className="text-center pb-4">
                <CardTitle className="text-2xl">{plan.name}</CardTitle>
                <div className="mt-4">
                  <span className="text-4xl font-bold text-gray-900">{plan.price}</span>
                  <span className="text-gray-600">{plan.period}</span>
                </div>
                <p className="text-gray-600 mt-2">{plan.description}</p>
                <div className="mt-2 text-sm text-blue-600 font-medium">{plan.credits}</div>
                {plan.name === "Pro" && (
                  <div className="mt-4 text-xs text-gray-500">
                    <div className="text-sm font-medium text-gray-700 mb-2">1 report = 2 credits</div>
                  </div>
                )}
              </CardHeader>
              <CardContent className="text-center">
                {plan.subPlans && (
                  <div className="mb-6 space-y-2">
                    {plan.subPlans.map((subPlan, subIndex) => (
                      <div key={subIndex} className="border rounded-lg p-3 hover:border-blue-300 transition-colors">
                        <div className="font-medium text-gray-900">{subPlan.reports}</div>
                        <div className="text-2xl font-bold text-blue-600">{subPlan.price}<span className="text-sm text-gray-500">/month</span></div>
                        <div className="text-xs text-gray-500">{subPlan.credits}</div>
                      </div>
                    ))}
                  </div>
                )}
                <Button
                  size="lg"
                  className={`w-full mb-6 ${plan.popular ? 'bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white' : ''}`}
                  variant={plan.buttonVariant}
                  onClick={plan.onClick}
                >
                  {plan.buttonText}
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Feature Comparison Table */}
        <Card className="shadow-2xl">
          <CardHeader>
            <CardTitle className="text-center text-2xl">Feature Comparison</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b">
                    <th className="text-left py-4 px-4 font-semibold">Feature</th>
                    <th className="text-center py-4 px-4 font-semibold">Free</th>
                    <th className="text-center py-4 px-4 font-semibold">Pro (Starting $99/mo)</th>
                    <th className="text-center py-4 px-4 font-semibold">Enterprise (Contact Sales)</th>
                  </tr>
                </thead>
                <tbody>
                  {features.map((feature, index) => (
                    <tr key={index} className="border-b hover:bg-gray-50">
                      <td className="py-4 px-4">
                        <div className="flex items-center">
                          <span className="mr-2">{feature.icon}</span>
                          <span className="font-medium">{feature.name}</span>
                        </div>
                      </td>
                      <td className="py-4 px-4 text-center">
                        {renderFeatureValue(feature, "free")}
                      </td>
                      <td className="py-4 px-4 text-center">
                        {renderFeatureValue(feature, "pro")}
                      </td>
                      <td className="py-4 px-4 text-center">
                        {renderFeatureValue(feature, "agency")}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>

        {/* Upgrade Notice for Free Users */}
        <Card className="mt-8 bg-amber-50 border-amber-200">
          <CardContent className="p-6 text-center">
            <h3 className="text-xl font-bold text-amber-800 mb-2">
              ⚠️ Free Plan Limit Reached
            </h3>
            <p className="text-amber-700 mb-4">
              You&apos;ve used 5/5 census uploads this month. Upgrade to Pro for unlimited uploads and advanced features.
            </p>
            <Button 
              className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white"
              onClick={() => navigate('/billing')}
            >
              Upgrade Now
            </Button>
          </CardContent>
        </Card>

        {/* FAQ Section */}
        <div className="mt-16 text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-8">
            Frequently Asked Questions
          </h2>
          <div className="grid md:grid-cols-2 gap-8">
            <Card>
              <CardContent className="p-6">
                <h3 className="font-semibold mb-2">Can I change plans anytime?</h3>
                <p className="text-gray-600 text-sm">
                  Yes, you can upgrade or downgrade your plan at any time. Changes take effect immediately.
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-6">
                <h3 className="font-semibold mb-2">Is there a setup fee?</h3>
                <p className="text-gray-600 text-sm">
                  No setup fees. Pay only the monthly subscription fee.
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </main>
    </div>
  );
};

export default Pricing;
