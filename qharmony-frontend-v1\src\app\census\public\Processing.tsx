
import { useEffect, useState } from "react";
import { Card, CardContent } from "../components/ui/card";
import { Button } from "../components/ui/button";
import { useNavigate, useSearchParams } from "../lib/react-router-dom";
import { Loader2 } from "lucide-react";

const Processing = () => {
  const navigate = useNavigate();
  const searchParams = useSearchParams();
  const companyName = searchParams.get('company') || 'Your Company';
  
  const [currentStep, setCurrentStep] = useState(0);
  const [progress, setProgress] = useState(0);

  const steps = [
    "Identifying dependents...",
    "Calculating cost drivers...",
    "Matching plan tiers...",
    "Analyzing risk factors...",
    "Generating recommendations..."
  ];

  useEffect(() => {
    const progressTimer = setInterval(() => {
      setProgress(prev => {
        if (prev >= 100) {
          clearInterval(progressTimer);
          setTimeout(() => navigate('/preview'), 1000);
          return 100;
        }
        return prev + 2;
      });
    }, 100);

    const stepTimer = setInterval(() => {
      setCurrentStep(prev => {
        if (prev < steps.length - 1) {
          return prev + 1;
        }
        clearInterval(stepTimer);
        return prev;
      });
    }, 1200);

    return () => {
      clearInterval(progressTimer);
      clearInterval(stepTimer);
    };
  }, [navigate, steps.length]);

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 flex items-center justify-center">
      <div className="container mx-auto px-4 max-w-2xl">
        <Card className="shadow-2xl border-0 overflow-hidden">
          <CardContent className="p-12 text-center">
            <div className="mb-8">
              <div className="w-20 h-20 mx-auto mb-6 bg-blue-100 rounded-full flex items-center justify-center">
                <Loader2 className="h-10 w-10 text-blue-600 animate-spin" />
              </div>
              
              <h1 className="text-3xl font-bold text-gray-900 mb-4">
                🔍 Analyzing Census for {companyName}
              </h1>
              
              <p className="text-lg text-gray-600 mb-8">
                We&apos;re enriching employee profiles and building a smart group analysis.
                <br />
                <span className="text-sm text-gray-500">(Usually done in under 30 seconds.)</span>
              </p>
            </div>

            {/* Progress Bar */}
            <div className="mb-8">
              <div className="w-full bg-gray-200 rounded-full h-3 mb-4">
                <div 
                  className="bg-gradient-to-r from-blue-600 to-purple-600 h-3 rounded-full transition-all duration-300 ease-out"
                  style={{ width: `${progress}%` }}
                ></div>
              </div>
              <p className="text-sm text-gray-500">{progress}% Complete</p>
            </div>

            {/* Animated Steps */}
            <div className="space-y-3">
              {steps.map((step, index) => (
                <div 
                  key={index}
                  className={`flex items-center justify-center p-3 rounded-lg transition-all duration-500 ${
                    index <= currentStep 
                      ? index === currentStep 
                        ? "bg-blue-50 text-blue-700 font-medium" 
                        : "bg-green-50 text-green-700"
                      : "text-gray-400"
                  }`}
                >
                  {index < currentStep && <span className="mr-2">✅</span>}
                  {index === currentStep && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                  {index > currentStep && <span className="mr-2">⏳</span>}
                  {step}
                </div>
              ))}
            </div>

            {progress === 100 && (
              <div className="mt-8 p-4 bg-green-50 rounded-lg border border-green-200 animate-fade-in">
                <p className="text-green-700 font-medium">
                  ✨ Analysis complete! Redirecting to your report...
                </p>
                <Button 
                  className="mt-4 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white"
                  onClick={() => navigate('/preview')}
                >
                  View Report Now
                </Button>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Pro Tip */}
        <div className="mt-8 text-center">
          <Card className="bg-gradient-to-r from-purple-100 to-blue-100 border-0">
            <CardContent className="p-6">
              <p className="text-sm text-gray-700">
                <strong>💡 Did you know?</strong> Our AI can identify potential cost savings of up to 25% on average for mid-market groups.
              </p>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default Processing;
