
import { useState } from "react";
import { But<PERSON> } from "../components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "../components/ui/card";
import { useNavigate } from "../lib/react-router-dom";
import { Upload, FileText, ArrowLeft } from "lucide-react";
import ProfileHandler from "../components/ProfileHandler";
import { useCensus } from "../context/CensusContext";
import { useToast } from "../hooks/use-toast";

const UploadCensus = () => {
  const navigate = useNavigate();
  const { uploadCensusFile, isLoading, error: censusError, clearError } = useCensus();
  const { toast } = useToast();
  const [dragActive, setDragActive] = useState(false);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [error, setError] = useState<string | null>(null);

  const validateFile = (file: File): string | null => {
    // Check file size (max 10MB)
    const maxSize = 10 * 1024 * 1024; // 10MB
    if (file.size > maxSize) {
      return "File size must be less than 10MB";
    }

    // Check file type
    const allowedTypes = [
      'text/csv',
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'text/plain'
    ];

    const allowedExtensions = ['.csv', '.xls', '.xlsx', '.txt'];
    const fileExtension = file.name.toLowerCase().substring(file.name.lastIndexOf('.'));

    if (!allowedTypes.includes(file.type) && !allowedExtensions.includes(fileExtension)) {
      return "Please upload a CSV, Excel (.xls, .xlsx), or text file";
    }

    // Check for suspicious file names or extensions
    const suspiciousExtensions = ['.exe', '.bat', '.cmd', '.scr', '.pif', '.com', '.js', '.vbs', '.jar'];
    if (suspiciousExtensions.some(ext => file.name.toLowerCase().includes(ext))) {
      return "File type not allowed for security reasons";
    }

    // Basic file name validation
    if (file.name.length > 255) {
      return "File name is too long";
    }

    return null;
  };

  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true);
    } else if (e.type === "dragleave") {
      setDragActive(false);
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);
    setError(null);

    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      const file = e.dataTransfer.files[0];
      const validationError = validateFile(file);

      if (validationError) {
        setError(validationError);
        setSelectedFile(null);
      } else {
        setSelectedFile(file);
      }
    }
  };

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    setError(null);

    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];
      const validationError = validateFile(file);

      if (validationError) {
        setError(validationError);
        setSelectedFile(null);
        // Clear the input
        e.target.value = '';
      } else {
        setSelectedFile(file);
      }
    }
  };

  const handleGenerateReport = async () => {
    if (!selectedFile) {
      setError('Please select a file first');
      return;
    }

    // Clear any previous errors
    setError(null);
    clearError();

    try {
      console.log('🚀 Starting census file processing...');

      // Upload and process the census file
      const companyId = await uploadCensusFile(selectedFile);

      // Show success message
      toast({
        title: "Census Processed Successfully!",
        description: `File processed with company ID: ${companyId}`,
      });

      // Navigate to the company insight page
      navigate(`?page=employer-insight/${companyId}`);

    } catch (err: any) {
      const errorMessage = err.message || 'Failed to process census file';
      setError(errorMessage);

      toast({
        title: "Processing Failed",
        description: errorMessage,
        variant: "destructive",
      });

      console.error('❌ Census processing failed:', err);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
      {/* Header */}
      <header className="border-b bg-white/80 backdrop-blur-sm">
        <div className="container mx-auto px-4 py-4 flex justify-between items-center">
          <div className="flex items-center space-x-4">
            <Button variant="ghost" onClick={() => navigate('/')}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back
            </Button>
            <div className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">BenOsphere</div>
          </div>
          <ProfileHandler />
        </div>
      </header>

      <main className="container mx-auto px-4 py-16 max-w-4xl">
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            📤 Upload Group Census to Get Instant Insights
          </h1>
          <p className="text-xl text-gray-600">
            Drop in a census file — our AI will scan, enrich, and generate smart benefit recommendations. No formatting required.
          </p>
        </div>

        <Card className="shadow-xl border-0">
          <CardHeader>
            <CardTitle className="text-center text-2xl">Upload Census File</CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Drag & Drop Area */}
            <div
              className={`border-2 border-dashed rounded-xl p-12 text-center transition-all ${
                dragActive 
                  ? "border-blue-500 bg-blue-50" 
                  : "border-gray-300 hover:border-blue-400 hover:bg-gray-50"
              }`}
              onDragEnter={handleDrag}
              onDragLeave={handleDrag}
              onDragOver={handleDrag}
              onDrop={handleDrop}
            >
              <div className="space-y-4">
                <div className="mx-auto w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center">
                  <FileText className="h-8 w-8 text-blue-600" />
                </div>
                
                {error && (
                  <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-4">
                    <p className="text-red-600 font-medium">❌ {error}</p>
                  </div>
                )}

                {selectedFile ? (
                  <div>
                    <p className="text-lg font-semibold text-green-600">
                      ✅ {selectedFile.name}
                    </p>
                    <p className="text-sm text-gray-500">
                      {(selectedFile.size / 1024 / 1024).toFixed(2)} MB
                    </p>
                  </div>
                ) : (
                  <div>
                    <p className="text-xl font-semibold text-gray-700 mb-2">
                      📄 Drag & drop CSV, Excel, or PDF
                    </p>
                    <p className="text-gray-500 mb-4">or</p>
                    <input
                      type="file"
                      id="file-upload"
                      className="hidden"
                      accept=".csv,.xlsx,.xls,.pdf"
                      onChange={handleFileSelect}
                    />
                    <label htmlFor="file-upload">
                      <Button 
                        variant="outline" 
                        className="cursor-pointer bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white border-0" 
                        asChild
                      >
                        <span>
                          🔍 Browse Files
                        </span>
                      </Button>
                    </label>
                  </div>
                )}
              </div>
            </div>

            {/* File Type Support */}
            <div className="text-center text-sm text-gray-500">
              <p>Supported formats: CSV, Excel (.xlsx, .xls), PDF</p>
              <p>Maximum file size: 50MB</p>
            </div>

            {/* Generate Report Button */}
            <div className="text-center">
              <Button
                size="lg"
                className="bg-blue-600 hover:bg-blue-700 text-white px-12 py-4 text-lg"
                onClick={handleGenerateReport}
                disabled={!selectedFile || isLoading}
              >
                {isLoading ? (
                  <>
                    <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                    Processing...
                  </>
                ) : (
                  <>
                    <Upload className="mr-2 h-5 w-5" />
                    ➡️ Generate Report
                  </>
                )}
              </Button>
            </div>

            {!selectedFile && !error && (
              <p className="text-center text-sm text-gray-400">
                Please select a file to generate your report
              </p>
            )}

            {isLoading && (
              <p className="text-center text-sm text-blue-600">
                🔄 Analyzing your census file...
              </p>
            )}
          </CardContent>
        </Card>

        {/* Sample Data Info */}
        <Card className="mt-8 bg-blue-50 border-blue-200">
          <CardContent className="p-6">
            <h3 className="font-semibold text-blue-900 mb-2">💡 What happens next?</h3>
            <ul className="text-blue-800 space-y-1 text-sm">
              <li>• AI analyzes employee demographics and coverage patterns</li>
              <li>• Identifies cost-saving opportunities and risk factors</li>
              <li>• Generates benchmarking data vs. similar groups</li>
              <li>• Provides personalized plan recommendations</li>
            </ul>
          </CardContent>
        </Card>
      </main>
    </div>
  );
};

export default UploadCensus;
