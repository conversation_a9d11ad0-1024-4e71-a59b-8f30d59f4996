'use client';

import axios from 'axios';

// Census API uses the Python backend (chatbot service) instead of the main Node.js backend
const CENSUS_API_BASE_URL = process.env.NEXT_PUBLIC_CHATBOT_URL || 'http://127.0.0.1:8000';

// Types based on the API documentation
export interface CensusEmployee {
  employee_id: string;
  name: string;
  age: number;
  gender: string;
  marital_status: string;
  zipcode: string;
  city: string;
  state: string;
  income_tier: string;
  dept_count: number;
  predicted_plan_type: string;
  plan_confidence: number;
  plan_reason: string;
  predicted_benefits: string[];
  benefits_confidence: number;
  marketplace_plans_available: boolean;
  plan_count: number;
  recommended_plan?: {
    id: string;
    name: string;
    issuer: string;
    premium: number;
    premium_with_credit: number;
    metal_level: string;
    type: string;
    deductible: number;
    max_out_of_pocket: number;
    hsa_eligible: boolean;
    quality_rating: number;
  };
  benefits_coverage: Record<string, string>;
  top_3_available_plans: string; // JSON string
  api_processing_status: string;
}

export interface CensusStatistics {
  demographics: {
    average_age: number;
    gender_distribution: Record<string, number>;
    marital_status_distribution: Record<string, number>;
  };
  health_plans: {
    employees_with_plans: number;
    employees_without_plans: number;
    total_plans_found: number;
    average_plans_per_employee: number;
    plan_type_distribution: Record<string, number>;
    metal_level_distribution: Record<string, number>;
  };
  predictions: {
    plan_prediction_success_rate: number;
    benefits_prediction_success_rate: number;
    average_plan_confidence: number;
  };
}

export interface CensusSummary {
  total_employees: number;
  processing_success: boolean;
  data_quality_score: number;
  api_integration_status: {
    health_plans_available: boolean;
    prediction_method: string;
    total_plans_found: number;
  };
}

export interface CensusApiResponse {
  success: boolean;
  status_code: number;
  message: string;
  data: {
    summary: CensusSummary;
    statistics: CensusStatistics;
    employees: CensusEmployee[];
    processing_info: {
      enrichment_summary: {
        total_employees: number;
        features_analyzed: number;
        data_quality_improvement: {
          overall_completion_rate: number;
          total_enriched: number;
        };
      };
      health_plan_errors: {
        unsupported_states: string[];
        api_failures: number;
        fallback_applied: boolean;
      };
    };
    metadata: {
      pipeline_version: string;
      total_steps: number;
      steps_completed: string[];
      prediction_method: string;
      health_plan_integration_success: boolean;
      final_dataframe_shape: number[];
      processing_time_seconds: number;
    };
    // Optional fields when return_dataframe=true
    enriched_data_csv?: string;
    total_employees?: number;
    total_columns?: number;
    has_predictions?: boolean;
    prediction_columns?: string[];
  };
}

export interface CensusUploadResult {
  uploadId: string;
  status: string;
  companyId?: string;
  reportId?: string;
}

class CensusApiService {
  /**
   * Upload and process census file using the Python backend (chatbot service)
   */
  static async uploadCensusFile(
    file: File,
    returnDataframe: boolean = false
  ): Promise<CensusApiResponse> {
    try {
      const formData = new FormData();
      formData.append('file', file);

      // Build full URL for census API (Python backend)
      const url = `${CENSUS_API_BASE_URL}/api/census/processor/v1?return_dataframe=${returnDataframe}`;

      console.log(`📤 Uploading census file: ${file.name} (${(file.size / 1024 / 1024).toFixed(2)} MB)`);
      console.log(`🔗 Census API URL: ${url}`);
      console.log(`📋 Request details:`, {
        method: 'POST',
        url: url,
        fileSize: file.size,
        fileName: file.name,
        returnDataframe: returnDataframe
      });

      // Use axios directly for census API calls to Python backend
      // Note: Don't set Content-Type manually - let axios set it automatically for FormData
      const response = await axios.post(url, formData, {
        timeout: 300000, // 5 minutes timeout for large file processing
      });

      console.log(`📊 Response received:`, {
        status: response.status,
        statusText: response.statusText,
        success: response.data?.success,
        hasData: !!response.data?.data,
        totalEmployees: response.data?.data?.summary?.total_employees
      });

      if (response.status === 200 && response.data.success) {
        // Log the actual response structure for debugging
        console.log(`✅ Census processing completed successfully`);
        console.log(`📊 Full response structure:`, response.data);

        // Try to extract employee count from various possible locations
        const employeeCount = response.data.data?.summary?.total_employees ||
                             response.data.data?.total_employees ||
                             response.data.total_employees ||
                             'unknown';

        console.log(`👥 Processed employees: ${employeeCount}`);
        return response.data;
      } else {
        throw new Error(response.data.message || 'Census processing failed');
      }
    } catch (error: any) {
      console.error('❌ Census upload failed:', error);
      console.error('📋 Error details:', {
        message: error.message,
        code: error.code,
        status: error.response?.status,
        statusText: error.response?.statusText,
        responseData: error.response?.data
      });

      // Provide more specific error messages
      if (error.code === 'ECONNREFUSED') {
        throw new Error('Census API service is not running. Please start the Python backend on port 8000.');
      } else if (error.response?.status === 404) {
        throw new Error('Census API endpoint not found. Please check if the Python backend is running.');
      } else if (error.response?.status === 413) {
        throw new Error('File too large. Maximum file size is 50MB.');
      } else if (error.response?.status === 500) {
        const serverError = error.response?.data?.message || 'Internal server error during census processing';
        throw new Error(`Server error: ${serverError}`);
      } else if (error.response?.data?.message) {
        throw new Error(error.response.data.message);
      } else if (error.message?.includes('undefined')) {
        // Handle response structure mismatch
        console.log(`🔍 Response structure debugging:`, error.response?.data);
        throw new Error('Response structure mismatch - check console for details');
      } else {
        throw new Error(error.message || 'Failed to upload census file');
      }
    }
  }

  /**
   * Get processed census data by company/report ID
   * Note: This would be implemented when backend supports data persistence
   */
  static async getCensusData(companyId: string): Promise<CensusApiResponse> {
    try {
      // For now, this would use the Python backend when persistence is implemented
      const url = `${CENSUS_API_BASE_URL}/api/census/reports/${companyId}`;
      const response = await axios.get(url);
      return response.data;
    } catch (error: any) {
      console.error('❌ Failed to fetch census data:', error);
      throw new Error(error.message || 'Failed to fetch census data');
    }
  }

  /**
   * Get broker dashboard data (list of processed companies)
   * Note: This would be implemented when backend supports data persistence
   */
  static async getBrokerDashboard(): Promise<any[]> {
    try {
      // For now, this would use the Python backend when persistence is implemented
      const url = `${CENSUS_API_BASE_URL}/api/census/broker/dashboard`;
      const response = await axios.get(url);
      return response.data || [];
    } catch (error: any) {
      console.error('❌ Failed to fetch broker dashboard:', error);
      // Return empty array as fallback - frontend state management handles this
      return [];
    }
  }

  /**
   * Transform API employee data to frontend format
   */
  static transformEmployeeData(apiEmployee: CensusEmployee) {
    // Parse top 3 plans if available
    let top3Plans = [];
    try {
      if (apiEmployee.top_3_available_plans) {
        top3Plans = JSON.parse(apiEmployee.top_3_available_plans);
      }
    } catch (e) {
      console.warn('Failed to parse top_3_available_plans:', e);
    }

    // Map risk level based on plan confidence
    const getRiskLevel = (confidence: number): 'Low' | 'Medium' | 'High' => {
      if (confidence >= 0.8) return 'Low';
      if (confidence >= 0.6) return 'Medium';
      return 'High';
    };

    return {
      name: apiEmployee.name,
      department: `Dept ${apiEmployee.dept_count}`, // Could be enhanced with actual department mapping
      risk: getRiskLevel(apiEmployee.plan_confidence),
      age: apiEmployee.age,
      coverage: apiEmployee.predicted_plan_type,
      hasDependents: apiEmployee.marital_status.toLowerCase() === 'married',
      salary: apiEmployee.income_tier,
      currentPlan: {
        medical: apiEmployee.recommended_plan?.name || 'Not Enrolled',
        dental: apiEmployee.predicted_benefits.includes('Dental') ? 'Basic' : 'Not Enrolled',
        vision: apiEmployee.predicted_benefits.includes('Vision') ? 'Basic' : 'Not Enrolled',
        life: apiEmployee.predicted_benefits.includes('Term Life') ? '1x Salary' : 'None',
        disability: apiEmployee.predicted_benefits.includes('LTD') ? 'Basic' : 'None'
      },
      coverageGaps: [], // Could be derived from benefits analysis
      insights: [apiEmployee.plan_reason],
      upsells: [], // Could be derived from plan recommendations
      planFitSummary: {
        recommendedPlan: apiEmployee.recommended_plan?.name || 'No recommendation',
        insight: apiEmployee.plan_reason
      },
      // Additional API data
      apiData: {
        employee_id: apiEmployee.employee_id,
        zipcode: apiEmployee.zipcode,
        city: apiEmployee.city,
        state: apiEmployee.state,
        recommended_plan: apiEmployee.recommended_plan,
        benefits_coverage: apiEmployee.benefits_coverage,
        top_3_plans: top3Plans,
        marketplace_plans_available: apiEmployee.marketplace_plans_available,
        plan_count: apiEmployee.plan_count
      }
    };
  }

  /**
   * Transform API response to frontend company data format
   */
  static transformCompanyData(apiResponse: any, companyId: string = '1') {
    // Handle flexible response structure
    const data = apiResponse.data || apiResponse;
    const summary = data.summary || {};
    const statistics = data.statistics || {};
    const employees = data.employees || [];

    console.log(`🔄 Transforming company data:`, {
      hasData: !!data,
      hasSummary: !!summary,
      hasStatistics: !!statistics,
      employeeCount: employees.length,
      summaryKeys: Object.keys(summary),
      statisticsKeys: Object.keys(statistics)
    });
    
    // Calculate potential savings (simplified calculation)
    const avgPremium = employees
      .filter((emp: any) => emp.recommended_plan)
      .reduce((sum: number, emp: any) => sum + (emp.recommended_plan?.premium || 0), 0) / (employees.length || 1);
    const potentialSavings = Math.round(avgPremium * employees.length * 0.15); // Assume 15% savings

    // Determine primary plan type
    const planTypes = Object.keys(statistics.health_plans.plan_type_distribution);
    const primaryPlanType = planTypes.reduce((a, b) => 
      statistics.health_plans.plan_type_distribution[a] > statistics.health_plans.plan_type_distribution[b] ? a : b
    );

    return {
      companyName: `Company ${companyId}`, // Could be enhanced with actual company name
      employees: summary.total_employees || employees.length || 0,
      averageAge: Math.round(statistics.demographics?.average_age || 35),
      dependents: employees.filter((emp: any) => emp.marital_status?.toLowerCase() === 'married').length / (employees.length || 1),
      planType: primaryPlanType,
      potentialSavings: `$${potentialSavings.toLocaleString()}`,
      riskScore: `${((summary.data_quality_score || 0.8) * 10).toFixed(1)}/10`,
      uploadDate: new Date().toISOString().split('T')[0],
      industry: 'Technology', // Default, could be enhanced
      currentSpend: `$${Math.round(avgPremium * employees.length).toLocaleString()}/month`,
      suggestedPlan: `${primaryPlanType} with Enhanced Coverage`,
      planFitSummary: {
        silverGoldPPO: Math.round((statistics.health_plans?.plan_type_distribution?.['PPO'] || 0) / (employees.length || 1) * 100),
        hdhp: Math.round((statistics.health_plans?.plan_type_distribution?.['HDHP'] || 0) / (employees.length || 1) * 100),
        familyPPO: Math.round(employees.filter((emp: any) => emp.marital_status?.toLowerCase() === 'married').length / (employees.length || 1) * 100),
        insight: `Based on ${employees.length} employees with ${(statistics.demographics?.average_age || 35).toFixed(1)} average age`
      },
      employeeProfiles: employees.map((emp: any) => this.transformEmployeeData(emp)),
      // Generate mock upsell opportunities based on company data
      upsellOpportunities: [
        {
          category: "Enhanced Coverage",
          description: `Upgrade ${Math.round(employees.length * 0.3)} employees to premium plans`,
          savings: `+$${Math.round(potentialSavings * 0.1).toLocaleString()}/month`,
          confidence: "85%",
          priority: "High"
        },
        {
          category: "Wellness Programs",
          description: "Preventive care initiatives for healthier workforce",
          savings: `+$${Math.round(potentialSavings * 0.05).toLocaleString()}/month`,
          confidence: "72%",
          priority: "Medium"
        }
      ],
      // Store original API data for reference
      apiData: apiResponse.data
    };
  }
}

export default CensusApiService;
