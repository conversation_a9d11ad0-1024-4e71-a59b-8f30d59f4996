'use client';

import { uploadDocument, getRequest } from '@/APILayer/axios_helper';

// Types based on the API documentation
export interface CensusEmployee {
  employee_id: string;
  name: string;
  age: number;
  gender: string;
  marital_status: string;
  zipcode: string;
  city: string;
  state: string;
  income_tier: string;
  dept_count: number;
  predicted_plan_type: string;
  plan_confidence: number;
  plan_reason: string;
  predicted_benefits: string[];
  benefits_confidence: number;
  marketplace_plans_available: boolean;
  plan_count: number;
  recommended_plan?: {
    id: string;
    name: string;
    issuer: string;
    premium: number;
    premium_with_credit: number;
    metal_level: string;
    type: string;
    deductible: number;
    max_out_of_pocket: number;
    hsa_eligible: boolean;
    quality_rating: number;
  };
  benefits_coverage: Record<string, string>;
  top_3_available_plans: string; // JSON string
  api_processing_status: string;
}

export interface CensusStatistics {
  demographics: {
    average_age: number;
    gender_distribution: Record<string, number>;
    marital_status_distribution: Record<string, number>;
  };
  health_plans: {
    employees_with_plans: number;
    employees_without_plans: number;
    total_plans_found: number;
    average_plans_per_employee: number;
    plan_type_distribution: Record<string, number>;
    metal_level_distribution: Record<string, number>;
  };
  predictions: {
    plan_prediction_success_rate: number;
    benefits_prediction_success_rate: number;
    average_plan_confidence: number;
  };
}

export interface CensusSummary {
  total_employees: number;
  processing_success: boolean;
  data_quality_score: number;
  api_integration_status: {
    health_plans_available: boolean;
    prediction_method: string;
    total_plans_found: number;
  };
}

export interface CensusApiResponse {
  success: boolean;
  status_code: number;
  message: string;
  data: {
    summary: CensusSummary;
    statistics: CensusStatistics;
    employees: CensusEmployee[];
    processing_info: {
      enrichment_summary: {
        total_employees: number;
        features_analyzed: number;
        data_quality_improvement: {
          overall_completion_rate: number;
          total_enriched: number;
        };
      };
      health_plan_errors: {
        unsupported_states: string[];
        api_failures: number;
        fallback_applied: boolean;
      };
    };
    metadata: {
      pipeline_version: string;
      total_steps: number;
      steps_completed: string[];
      prediction_method: string;
      health_plan_integration_success: boolean;
      final_dataframe_shape: number[];
      processing_time_seconds: number;
    };
    // Optional fields when return_dataframe=true
    enriched_data_csv?: string;
    total_employees?: number;
    total_columns?: number;
    has_predictions?: boolean;
    prediction_columns?: string[];
  };
}

export interface CensusUploadResult {
  uploadId: string;
  status: string;
  companyId?: string;
  reportId?: string;
}

class CensusApiService {
  /**
   * Upload and process census file
   */
  static async uploadCensusFile(
    file: File, 
    returnDataframe: boolean = false,
    companyName?: string
  ): Promise<CensusApiResponse> {
    try {
      const formData = new FormData();
      formData.append('file', file);
      
      // Build endpoint with query parameters
      const endpoint = `/api/census/processor/v1?return_dataframe=${returnDataframe}`;
      
      console.log(`📤 Uploading census file: ${file.name} (${(file.size / 1024 / 1024).toFixed(2)} MB)`);
      
      const response = await uploadDocument(endpoint, formData);
      
      if (response.status === 200 && response.data.success) {
        console.log(`✅ Census processing completed: ${response.data.data.summary.total_employees} employees`);
        return response.data;
      } else {
        throw new Error(response.data.message || 'Census processing failed');
      }
    } catch (error: any) {
      console.error('❌ Census upload failed:', error);
      throw new Error(error.message || 'Failed to upload census file');
    }
  }

  /**
   * Get processed census data by company/report ID
   */
  static async getCensusData(companyId: string): Promise<CensusApiResponse> {
    try {
      const response = await getRequest(`/api/census/reports/${companyId}`);
      return response;
    } catch (error: any) {
      console.error('❌ Failed to fetch census data:', error);
      throw new Error(error.message || 'Failed to fetch census data');
    }
  }

  /**
   * Get broker dashboard data (list of processed companies)
   */
  static async getBrokerDashboard(): Promise<any[]> {
    try {
      const response = await getRequest('/api/census/broker/dashboard');
      return response.data || [];
    } catch (error: any) {
      console.error('❌ Failed to fetch broker dashboard:', error);
      // Return empty array as fallback
      return [];
    }
  }

  /**
   * Transform API employee data to frontend format
   */
  static transformEmployeeData(apiEmployee: CensusEmployee) {
    // Parse top 3 plans if available
    let top3Plans = [];
    try {
      if (apiEmployee.top_3_available_plans) {
        top3Plans = JSON.parse(apiEmployee.top_3_available_plans);
      }
    } catch (e) {
      console.warn('Failed to parse top_3_available_plans:', e);
    }

    // Map risk level based on plan confidence
    const getRiskLevel = (confidence: number): 'Low' | 'Medium' | 'High' => {
      if (confidence >= 0.8) return 'Low';
      if (confidence >= 0.6) return 'Medium';
      return 'High';
    };

    return {
      name: apiEmployee.name,
      department: `Dept ${apiEmployee.dept_count}`, // Could be enhanced with actual department mapping
      risk: getRiskLevel(apiEmployee.plan_confidence),
      age: apiEmployee.age,
      coverage: apiEmployee.predicted_plan_type,
      hasDependents: apiEmployee.marital_status.toLowerCase() === 'married',
      salary: apiEmployee.income_tier,
      currentPlan: {
        medical: apiEmployee.recommended_plan?.name || 'Not Enrolled',
        dental: apiEmployee.predicted_benefits.includes('Dental') ? 'Basic' : 'Not Enrolled',
        vision: apiEmployee.predicted_benefits.includes('Vision') ? 'Basic' : 'Not Enrolled',
        life: apiEmployee.predicted_benefits.includes('Term Life') ? '1x Salary' : 'None',
        disability: apiEmployee.predicted_benefits.includes('LTD') ? 'Basic' : 'None'
      },
      coverageGaps: [], // Could be derived from benefits analysis
      insights: [apiEmployee.plan_reason],
      upsells: [], // Could be derived from plan recommendations
      planFitSummary: {
        recommendedPlan: apiEmployee.recommended_plan?.name || 'No recommendation',
        insight: apiEmployee.plan_reason
      },
      // Additional API data
      apiData: {
        employee_id: apiEmployee.employee_id,
        zipcode: apiEmployee.zipcode,
        city: apiEmployee.city,
        state: apiEmployee.state,
        recommended_plan: apiEmployee.recommended_plan,
        benefits_coverage: apiEmployee.benefits_coverage,
        top_3_plans: top3Plans,
        marketplace_plans_available: apiEmployee.marketplace_plans_available,
        plan_count: apiEmployee.plan_count
      }
    };
  }

  /**
   * Transform API response to frontend company data format
   */
  static transformCompanyData(apiResponse: CensusApiResponse, companyId: string = '1') {
    const { summary, statistics, employees } = apiResponse.data;
    
    // Calculate potential savings (simplified calculation)
    const avgPremium = employees
      .filter(emp => emp.recommended_plan)
      .reduce((sum, emp) => sum + (emp.recommended_plan?.premium || 0), 0) / employees.length;
    const potentialSavings = Math.round(avgPremium * employees.length * 0.15); // Assume 15% savings

    // Determine primary plan type
    const planTypes = Object.keys(statistics.health_plans.plan_type_distribution);
    const primaryPlanType = planTypes.reduce((a, b) => 
      statistics.health_plans.plan_type_distribution[a] > statistics.health_plans.plan_type_distribution[b] ? a : b
    );

    return {
      companyName: `Company ${companyId}`, // Could be enhanced with actual company name
      employees: summary.total_employees,
      averageAge: Math.round(statistics.demographics.average_age),
      dependents: employees.filter(emp => emp.marital_status.toLowerCase() === 'married').length / employees.length,
      planType: primaryPlanType,
      potentialSavings: `$${potentialSavings.toLocaleString()}`,
      riskScore: `${(summary.data_quality_score * 10).toFixed(1)}/10`,
      uploadDate: new Date().toISOString().split('T')[0],
      industry: 'Technology', // Default, could be enhanced
      currentSpend: `$${Math.round(avgPremium * employees.length).toLocaleString()}/month`,
      suggestedPlan: `${primaryPlanType} with Enhanced Coverage`,
      planFitSummary: {
        silverGoldPPO: Math.round((statistics.health_plans.plan_type_distribution['PPO'] || 0) / employees.length * 100),
        hdhp: Math.round((statistics.health_plans.plan_type_distribution['HDHP'] || 0) / employees.length * 100),
        familyPPO: Math.round(employees.filter(emp => emp.marital_status.toLowerCase() === 'married').length / employees.length * 100),
        insight: `Based on ${employees.length} employees with ${statistics.demographics.average_age.toFixed(1)} average age`
      },
      employeeProfiles: employees.map(emp => this.transformEmployeeData(emp)),
      // Generate mock upsell opportunities based on company data
      upsellOpportunities: [
        {
          category: "Enhanced Coverage",
          description: `Upgrade ${Math.round(employees.length * 0.3)} employees to premium plans`,
          savings: `+$${Math.round(potentialSavings * 0.1).toLocaleString()}/month`,
          confidence: "85%",
          priority: "High"
        },
        {
          category: "Wellness Programs",
          description: "Preventive care initiatives for healthier workforce",
          savings: `+$${Math.round(potentialSavings * 0.05).toLocaleString()}/month`,
          confidence: "72%",
          priority: "Medium"
        }
      ],
      // Store original API data for reference
      apiData: apiResponse.data
    };
  }
}

export default CensusApiService;
