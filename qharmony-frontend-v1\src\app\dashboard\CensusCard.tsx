import React from "react";
import { Box, Typography, Avatar } from "@mui/material";
import { useRouter } from "next/navigation";
import { BarChart3 } from "lucide-react";
import { useAppSelector } from "@/redux/hooks";
import { RootState } from "@/redux/store";

export default function CensusCard() {
  const router = useRouter();
  const userDetails = useAppSelector(
    (state: RootState) => state.user.userProfile,
  );

  const handleClick = () => {
    // Check if user is logged in using consistent auth check
    const primaryKey = process.env.NEXT_PUBLIC_USER_ID_KEY || "userid1";
    const altKey = process.env.NEXT_PUBLIC_USER_ID_ALT_KEY || "userId";
    const userId = localStorage.getItem(primaryKey) || localStorage.getItem(altKey);
    const ssoDone = localStorage.getItem("ssoDone1");
    const userEmail = localStorage.getItem("userEmail1");

    // Debug logging
    console.log("Census Auth Check:", {
      userId,
      ssoDone,
      userEmail,
      userDetailsName: userDetails?.name,
      userDetailsEmail: userDetails?.email
    });

    // Check multiple conditions for authentication
    const isAuthenticated = userId && (
      ssoDone === "true" ||
      userDetails?.name ||
      userDetails?.email ||
      userEmail
    );

    if (isAuthenticated) {
      console.log("User authenticated, going to upload census");
      router.push('/census?page=upload-census');
    } else {
      console.log("User not authenticated, going to login prompt");
      router.push('/census?page=login-prompt');
    }
  };

  return (
    <Box
      onClick={handleClick}
      sx={{
        backgroundColor: "white",
        padding: 2,
        display: "flex",
        alignItems: "center",
        justifyContent: "space-between",
        borderRadius: "30px",
        boxShadow: "none",
        maxWidth: "100%",
        mt: 3,
        cursor: "pointer",
        transition: "all 0.2s ease",
        "&:hover": {
          boxShadow: "0 4px 12px rgba(0, 0, 0, 0.1)",
          transform: "translateY(-2px)",
        },
      }}
    >
      {/* Left side with Avatar */}
      <Box sx={{ display: "flex", alignItems: "center", flexDirection: "row" }}>
        <Avatar
          sx={{
            width: 50,
            height: 50,
            mr: 2,
            backgroundColor: "#2563eb",
            background: "linear-gradient(135deg, #2563eb 0%, #8b5cf6 100%)",
          }}
        >
          <BarChart3 size={28} color="white" style={{ filter: 'drop-shadow(0 0 2px rgba(255,255,255,0.3))' }} />
        </Avatar>
        <Box>
          <Box
            sx={{ display: "flex", alignItems: "center", flexDirection: "row" }}
          >
            {/* Census Text */}
            <Typography
              sx={{
                fontWeight: 700,
                fontSize: "24px",
                display: "flex",
                alignItems: "center",
              }}
            >
              Census
            </Typography>
          </Box>
          <Typography
            sx={{
              fontWeight: 500,
              fontSize: "14px",
              color: "#6c757d",
            }}
          >
            Upload and analyze employee census data
          </Typography>
        </Box>
      </Box>
    </Box>
  );
}
