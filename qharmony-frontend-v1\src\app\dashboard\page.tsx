"use client";

import { Box, Typography, Grid } from "@mui/material";
import withSidebar from "@/components/withSidebar";
import ProtectedRoute from "@/components/ProtectedRoute";

import FAQHelpSection from "./faq_help_section";
import { useAppSelector } from "@/redux/hooks";
import { RootState } from "@/redux/store";
import UserDetails from "./UserDetails";
import CompanyDetails from "./CompanyDetails";
import CareerAssistantCard from "./Career_assistant";
import AIEnrollerCard from "./AI_enroller";
import CensusCard from "./CensusCard";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { app } from "@microsoft/teams-js";

const Home = () => {
  const router = useRouter();
  const userDetails = useAppSelector(
    (state: RootState) => state.user.userProfile,
  );

  const [isTeamsApp, setIsTeamsApp] = useState(false);

  useEffect(() => {
    // Only initialize Teams app if running in Teams context
    try {
      if (typeof window !== 'undefined' && window.parent !== window) {
        app.initialize();
      }
    } catch (error) {
      console.log('Teams app initialization skipped - not running in Teams context');
    }
  }, []);

  useEffect(() => {
    const isTeamsApp = localStorage.getItem("isTeamsApp1");
    setIsTeamsApp(isTeamsApp === "true");
  }, []);

  const capitalizeWords = (name: string) => {
    return name.replace(/\b\w/g, (char) => char.toUpperCase());
  };

  useEffect(() => {
    const firstTimeLogin = localStorage.getItem("firstTimeLogin1");
    if (!firstTimeLogin) {
      localStorage.setItem("firstTimeLogin1", "true");
      router.refresh();
    }
  }, [userDetails]);

  return (
    <ProtectedRoute>
      <Box
        component="main"
        sx={{
          flexGrow: 1,
          p: 4,
          bgcolor: "#F5F6FA",
          minHeight: "95vh",
        }}
      >
        {/* Welcome Section */}
        <Typography variant="h4" sx={{ fontWeight: "bold", mb: 1 }}>
          Hey, {capitalizeWords(userDetails.name)}!
        </Typography>
        <Typography variant="body1" sx={{ color: "#6c757d", mb: 4 }}>
          Let&apos;s make the most of your benefits today!
        </Typography>

        <Grid container spacing={2}>
          <Grid item xs={12} md={8}>
            <FAQHelpSection />
            {!isTeamsApp && <CensusCard />}
            {/* {!isTeamsApp && <AIEnrollerCard />} */}
          </Grid>
          <Grid item xs={12} md={4}>
            <UserDetails />
            <CompanyDetails />
          </Grid>
        </Grid>
      </Box>
    </ProtectedRoute>
  );
};

export default withSidebar(Home);
