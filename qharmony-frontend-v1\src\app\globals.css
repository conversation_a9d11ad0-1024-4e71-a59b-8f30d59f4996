@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --foreground-rgb: 0, 0, 0;
  --background-start-rgb: 214, 219, 220;
  --background-end-rgb: 255, 255, 255;

  /* shadcn/ui CSS variables */
  --background: 0 0% 100%;
  --foreground: 222.2 84% 4.9%;
  --card: 0 0% 100%;
  --card-foreground: 222.2 84% 4.9%;
  --popover: 0 0% 100%;
  --popover-foreground: 222.2 84% 4.9%;
  --primary: 222.2 47.4% 11.2%;
  --primary-foreground: 210 40% 98%;
  --secondary: 210 40% 96%;
  --secondary-foreground: 222.2 84% 4.9%;
  --muted: 210 40% 96%;
  --muted-foreground: 215.4 16.3% 46.9%;
  --accent: 210 40% 96%;
  --accent-foreground: 222.2 84% 4.9%;
  --destructive: 0 84.2% 60.2%;
  --destructive-foreground: 210 40% 98%;
  --border: 214.3 31.8% 91.4%;
  --input: 214.3 31.8% 91.4%;
  --ring: 222.2 84% 4.9%;
  --radius: 0.5rem;
}

@media (prefers-color-scheme: dark) {
  :root {
    --foreground-rgb: 255, 255, 255;
    --background-start-rgb: 0, 0, 0;
    --background-end-rgb: 0, 0, 0;
  }
}

.dark {
  --background: 222.2 84% 4.9%;
  --foreground: 210 40% 98%;
  --card: 222.2 84% 4.9%;
  --card-foreground: 210 40% 98%;
  --popover: 222.2 84% 4.9%;
  --popover-foreground: 210 40% 98%;
  --primary: 210 40% 98%;
  --primary-foreground: 222.2 47.4% 11.2%;
  --secondary: 217.2 32.6% 17.5%;
  --secondary-foreground: 210 40% 98%;
  --muted: 217.2 32.6% 17.5%;
  --muted-foreground: 215 20.2% 65.1%;
  --accent: 217.2 32.6% 17.5%;
  --accent-foreground: 210 40% 98%;
  --destructive: 0 62.8% 30.6%;
  --destructive-foreground: 210 40% 98%;
  --border: 217.2 32.6% 17.5%;
  --input: 217.2 32.6% 17.5%;
  --ring: 212.7 26.8% 83.9%;
}

body {
  color: rgb(var(--foreground-rgb));
  background: linear-gradient(
      to bottom,
      transparent,
      rgb(var(--background-end-rgb))
    )
    rgb(var(--background-start-rgb));
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
}

html, body {
  margin: 0;
  padding: 0;
  height: 100%;
  width: 100%;
  background-color: black; /* Ensure the background is black */
}

/* Scoped styling for census to override global black background */
.census-container {
  background-color: hsl(var(--background));
  color: hsl(var(--foreground));
  min-height: 100vh;
  width: 100%;
  position: relative;
  z-index: 1;
}

/* Ensure all census components use proper light mode styling */
/* CRITICAL FIX: Only apply color inheritance to specific elements, not all elements */
/* This prevents overriding Tailwind color classes like text-orange-600, text-blue-600, etc. */
.census-container p,
.census-container h1,
.census-container h2,
.census-container h3,
.census-container h4,
.census-container h5,
.census-container h6,
.census-container span:not(.bg-clip-text):not(.text-transparent),
.census-container div:not([class*="text-"]):not(.bg-clip-text):not(.text-transparent) {
  color: inherit;
}

/* Ensure SVGs with Tailwind color classes maintain their colors */
.census-container svg[class*="text-"] {
  color: currentColor !important;
}

/* Fix button styling specifically */
/* But don't override buttons that have Tailwind color classes */
.census-container button:not(:has(.bg-clip-text)):not([class*="text-"]) {
  color: hsl(var(--foreground));
}

/* Ensure buttons with gradient text children don't override the gradient */
.census-container button .bg-clip-text.text-transparent {
  color: transparent !important;
}

.census-container .bg-primary {
  background-color: hsl(var(--primary));
  color: hsl(var(--primary-foreground));
}

.census-container .bg-secondary {
  background-color: hsl(var(--secondary));
  color: hsl(var(--secondary-foreground));
}

.census-container .text-primary {
  color: hsl(var(--primary));
}

.census-container .text-secondary {
  color: hsl(var(--secondary-foreground));
}

.census-container .text-muted-foreground {
  color: hsl(var(--muted-foreground));
}

/* Fix input and form elements */
.census-container input,
.census-container textarea,
.census-container select {
  background-color: hsl(var(--background));
  color: hsl(var(--foreground));
  border-color: hsl(var(--border));
}

/* Fix card components */
.census-container .bg-card {
  background-color: hsl(var(--card));
  color: hsl(var(--card-foreground));
}

/* Fix popover and dropdown components */
.census-container .bg-popover {
  background-color: hsl(var(--popover));
  color: hsl(var(--popover-foreground));
}

/* Force proper button styling */
.census-container button[class*="bg-primary"] {
  background-color: hsl(var(--primary)) !important;
  color: hsl(var(--primary-foreground)) !important;
}

.census-container button[class*="bg-secondary"] {
  background-color: hsl(var(--secondary)) !important;
  color: hsl(var(--secondary-foreground)) !important;
}

.census-container button[class*="variant-ghost"] {
  background-color: transparent !important;
  color: hsl(var(--foreground)) !important;
}

.census-container button[class*="variant-outline"] {
  background-color: hsl(var(--background)) !important;
  color: hsl(var(--foreground)) !important;
  border-color: hsl(var(--border)) !important;
}

/* Fix dialog and modal components */
.census-container [role="dialog"],
.census-container [data-radix-popper-content-wrapper] {
  background-color: hsl(var(--popover)) !important;
  color: hsl(var(--popover-foreground)) !important;
}

/* Fix dropdown menu items */
.census-container [role="menuitem"],
.census-container [role="option"] {
  color: hsl(var(--foreground)) !important;
}

.census-container [role="menuitem"]:hover,
.census-container [role="option"]:hover {
  background-color: hsl(var(--accent)) !important;
  color: hsl(var(--accent-foreground)) !important;
}

/* Fix navigation dropdown specifically */
.census-container .navigation-dropdown {
  background-color: hsl(var(--popover)) !important;
  color: hsl(var(--popover-foreground)) !important;
}

/* Ensure text is visible in all scenarios */
.census-container .text-white {
  color: hsl(var(--foreground)) !important;
}

/* Fix any remaining white text issues */
.census-container [style*="color: white"],
.census-container [style*="color: #fff"],
.census-container [style*="color: #ffffff"] {
  color: hsl(var(--foreground)) !important;
}

/* Override hardcoded Tailwind colors with CSS variables - but not for gradient text */
.census-container .bg-white {
  background-color: hsl(var(--background)) !important;
}

.census-container .text-gray-900:not(.bg-clip-text):not(.text-transparent) {
  color: hsl(var(--foreground)) !important;
}

.census-container .text-gray-800:not(.bg-clip-text):not(.text-transparent) {
  color: hsl(var(--foreground)) !important;
}

.census-container .text-gray-700:not(.bg-clip-text):not(.text-transparent) {
  color: hsl(var(--muted-foreground)) !important;
}

.census-container .text-gray-600:not(.bg-clip-text):not(.text-transparent) {
  color: hsl(var(--muted-foreground)) !important;
}

.census-container .text-gray-500:not(.bg-clip-text):not(.text-transparent) {
  color: hsl(var(--muted-foreground)) !important;
}

.census-container .text-gray-400:not(.bg-clip-text):not(.text-transparent) {
  color: hsl(var(--muted-foreground)) !important;
}

.census-container .bg-gray-50 {
  background-color: hsl(var(--muted)) !important;
}

.census-container .bg-gray-100 {
  background-color: hsl(var(--muted)) !important;
}

.census-container .bg-gray-900 {
  background-color: hsl(var(--foreground)) !important;
  color: hsl(var(--background)) !important;
}

/* Fix white backgrounds that should be card backgrounds */
.census-container .bg-white\/80,
.census-container .bg-white\/90,
.census-container .bg-white\/70 {
  background-color: hsl(var(--card)) !important;
  color: hsl(var(--card-foreground)) !important;
}

/* Ensure proper contrast for all text elements - but preserve gradient text */
.census-container h1:not(:has(.bg-clip-text)),
.census-container h2:not(:has(.bg-clip-text)),
.census-container h3:not(:has(.bg-clip-text)),
.census-container h4:not(:has(.bg-clip-text)),
.census-container h5:not(:has(.bg-clip-text)),
.census-container h6:not(:has(.bg-clip-text)) {
  color: hsl(var(--foreground)) !important;
}

.census-container p:not(:has(.bg-clip-text)) {
  color: hsl(var(--foreground)) !important;
}

/* Only apply color to spans that don't have gradient text */
.census-container span:not(.bg-clip-text):not(.text-transparent) {
  color: inherit !important;
}

/* Preserve gradient text styling with highest specificity */
.census-container span.bg-gradient-to-r.from-blue-600.to-purple-600.bg-clip-text.text-transparent,
.census-container .bg-gradient-to-r.from-blue-600.to-purple-600.bg-clip-text.text-transparent {
  background: linear-gradient(to right, #2563eb, #9333ea) !important;
  -webkit-background-clip: text !important;
  background-clip: text !important;
  color: transparent !important;
  -webkit-text-fill-color: transparent !important;
}

.census-container span.bg-gradient-to-r.from-blue-400.to-purple-400.bg-clip-text.text-transparent,
.census-container .bg-gradient-to-r.from-blue-400.to-purple-400.bg-clip-text.text-transparent {
  background: linear-gradient(to right, #60a5fa, #c084fc) !important;
  -webkit-background-clip: text !important;
  background-clip: text !important;
  color: transparent !important;
  -webkit-text-fill-color: transparent !important;
}

/* General gradient text fallback */
.census-container .bg-clip-text.text-transparent:not(.from-blue-600):not(.from-blue-400) {
  background: linear-gradient(to right, hsl(var(--primary)), hsl(var(--primary))) !important;
  -webkit-background-clip: text !important;
  background-clip: text !important;
  color: transparent !important;
  -webkit-text-fill-color: transparent !important;
}

/* Fix icon colors - be more selective about which icons inherit color */
/* REMOVED: .census-container svg { color: inherit !important; } */
/* This was causing all icons in census to appear grey/black */
/* Let icons in census container use their natural colors like in benosphere-census-insight */

/* IMPORTANT: Remove global SVG color inheritance that's making icons grey */
/* Let React Icons and Lucide icons use their natural colors */

/* CRITICAL FIX: React Icons appearing grey/black issue */
/* The body has color: rgb(0, 0, 0) which makes all SVGs inherit black color */
/* React Icons should have their natural colors, not inherit from body */

/* REMOVED: Global SVG color rule that was forcing inheritance */
/* Let SVGs use their natural colors like in benosphere-census-insight */
/* No global SVG color override needed */

/* Let React Icons and Lucide icons use their natural colors */
/* Don't force any specific color - let them inherit from their context */
/* This matches the behavior in benosphere-census-insight */

/* REMOVED: Census container icon color overrides */
/* These were causing icons to appear grey/black in census pages */
/* Let icons use their natural colors like in benosphere-census-insight */

/* Allow React Icons and Lucide icons to use their natural colors */
/* Remove global color inheritance that makes icons grey */
/* Only apply specific color rules where needed */

/* React Icons in gradient backgrounds should be white */
.MuiAvatar-root svg,
[style*="background: linear-gradient"] svg,
.bg-gradient-to-r svg {
  color: white !important;
}

/* Specific contexts where we want colored icons */
.dashboard-icon svg,
.main-app-icon svg {
  color: #2563eb !important;
}

/* Ensure gradient icons maintain proper styling */
.gradient-icon svg,
.gradient-icon-small svg,
.gradient-icon-large svg {
  color: white !important;
}

/* React Icons gradient styling - apply gradient colors to specific icon containers */
.react-icon-gradient {
  background: linear-gradient(135deg, #2563eb 0%, #8b5cf6 100%);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  color: transparent;
}

/* Alternative: gradient background for icons that can't use text gradients */
.react-icon-gradient-bg {
  background: linear-gradient(135deg, #2563eb 0%, #8b5cf6 100%);
  border-radius: 20%;
  color: white;
  padding: 0.25rem;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

/* Specific styling for dashboard and main app icons to use gradients */
.dashboard-icon,
.main-app-icon {
  color: #2563eb;
  filter: drop-shadow(0 0 2px rgba(37, 99, 235, 0.3));
}

/* Hover effects for gradient icons */
.react-icon-gradient:hover,
.react-icon-gradient-bg:hover {
  transform: scale(1.05);
  transition: transform 0.2s ease;
}
