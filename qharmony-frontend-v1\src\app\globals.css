@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --foreground-rgb: 0, 0, 0;
  --background-start-rgb: 214, 219, 220;
  --background-end-rgb: 255, 255, 255;

  /* shadcn/ui CSS variables */
  --background: 0 0% 100%;
  --foreground: 222.2 84% 4.9%;
  --card: 0 0% 100%;
  --card-foreground: 222.2 84% 4.9%;
  --popover: 0 0% 100%;
  --popover-foreground: 222.2 84% 4.9%;
  --primary: 222.2 47.4% 11.2%;
  --primary-foreground: 210 40% 98%;
  --secondary: 210 40% 96%;
  --secondary-foreground: 222.2 84% 4.9%;
  --muted: 210 40% 96%;
  --muted-foreground: 215.4 16.3% 46.9%;
  --accent: 210 40% 96%;
  --accent-foreground: 222.2 84% 4.9%;
  --destructive: 0 84.2% 60.2%;
  --destructive-foreground: 210 40% 98%;
  --border: 214.3 31.8% 91.4%;
  --input: 214.3 31.8% 91.4%;
  --ring: 222.2 84% 4.9%;
  --radius: 0.5rem;
}

@media (prefers-color-scheme: dark) {
  :root {
    --foreground-rgb: 255, 255, 255;
    --background-start-rgb: 0, 0, 0;
    --background-end-rgb: 0, 0, 0;
  }
}

.dark {
  --background: 222.2 84% 4.9%;
  --foreground: 210 40% 98%;
  --card: 222.2 84% 4.9%;
  --card-foreground: 210 40% 98%;
  --popover: 222.2 84% 4.9%;
  --popover-foreground: 210 40% 98%;
  --primary: 210 40% 98%;
  --primary-foreground: 222.2 47.4% 11.2%;
  --secondary: 217.2 32.6% 17.5%;
  --secondary-foreground: 210 40% 98%;
  --muted: 217.2 32.6% 17.5%;
  --muted-foreground: 215 20.2% 65.1%;
  --accent: 217.2 32.6% 17.5%;
  --accent-foreground: 210 40% 98%;
  --destructive: 0 62.8% 30.6%;
  --destructive-foreground: 210 40% 98%;
  --border: 217.2 32.6% 17.5%;
  --input: 217.2 32.6% 17.5%;
  --ring: 212.7 26.8% 83.9%;
}

body {
  color: rgb(var(--foreground-rgb));
  background: linear-gradient(
      to bottom,
      transparent,
      rgb(var(--background-end-rgb))
    )
    rgb(var(--background-start-rgb));
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
}

html, body {
  margin: 0;
  padding: 0;
  height: 100%;
  width: 100%;
  background-color: black; /* Ensure the background is black */
}

/* Simple fix for census pages - ensure proper light mode without complex overrides */
/* This matches the approach in benosphere-census-insight */
[data-page="census"] {
  background-color: white !important;
}

/* Apply black text only to text elements, NOT to SVGs or elements with Tailwind color classes */
[data-page="census"] h1:not([class*="text-"]),
[data-page="census"] h2:not([class*="text-"]),
[data-page="census"] h3:not([class*="text-"]),
[data-page="census"] h4:not([class*="text-"]),
[data-page="census"] h5:not([class*="text-"]),
[data-page="census"] h6:not([class*="text-"]),
[data-page="census"] p:not([class*="text-"]),
[data-page="census"] span:not([class*="text-"]):not(.bg-clip-text):not(.text-transparent),
[data-page="census"] div:not([class*="text-"]):not(.bg-clip-text):not(.text-transparent):not(:has(svg)) {
  color: black !important;
}

/* Ensure census navigation and buttons have proper colors */
[data-page="census"] nav,
[data-page="census"] button:not(.bg-gradient-to-r),
[data-page="census"] .navigation-item {
  color: #374151 !important; /* gray-700 */
}

/* Ensure census navigation links are visible */
[data-page="census"] nav a,
[data-page="census"] nav button {
  color: #374151 !important; /* gray-700 */
}

[data-page="census"] nav a:hover,
[data-page="census"] nav button:hover {
  color: #111827 !important; /* gray-900 */
}

/* CRITICAL: Ensure SVGs with Tailwind color classes maintain their colors on census pages */
[data-page="census"] svg.text-red-600 { color: #dc2626 !important; }
[data-page="census"] svg.text-orange-600 { color: #ea580c !important; }
[data-page="census"] svg.text-yellow-600 { color: #ca8a04 !important; }
[data-page="census"] svg.text-blue-600 { color: #2563eb !important; }
[data-page="census"] svg.text-green-600 { color: #16a34a !important; }
[data-page="census"] svg.text-purple-600 { color: #9333ea !important; }

/* React Icons in gradient backgrounds should be white */
.MuiAvatar-root svg,
[style*="background: linear-gradient"] svg,
.bg-gradient-to-r svg {
  color: white !important;
}

/* Specific contexts where we want colored icons */
.dashboard-icon svg,
.main-app-icon svg {
  color: #2563eb !important;
}

/* Ensure gradient icons maintain proper styling */
.gradient-icon svg,
.gradient-icon-small svg,
.gradient-icon-large svg {
  color: white !important;
}

/* React Icons gradient styling - apply gradient colors to specific icon containers */
.react-icon-gradient {
  background: linear-gradient(135deg, #2563eb 0%, #8b5cf6 100%);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  color: transparent;
}

/* Alternative: gradient background for icons that can't use text gradients */
.react-icon-gradient-bg {
  background: linear-gradient(135deg, #2563eb 0%, #8b5cf6 100%);
  border-radius: 20%;
  color: white;
  padding: 0.25rem;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

/* Specific styling for dashboard and main app icons to use gradients */
.dashboard-icon,
.main-app-icon {
  color: #2563eb;
  filter: drop-shadow(0 0 2px rgba(37, 99, 235, 0.3));
}

/* Hover effects for gradient icons */
.react-icon-gradient:hover,
.react-icon-gradient-bg:hover {
  transform: scale(1.05);
  transition: transform 0.2s ease;
}
