'use client';

import { useState, useEffect } from 'react';
import { 
  App<PERSON><PERSON>, 
  <PERSON><PERSON><PERSON>, 
  <PERSON>, 
  Button, 
  Container, 
  Typography,
  IconButton,
  Drawer,
  List,
  ListItem,
  ListItemButton,
  ListItemText,
  useMediaQuery,
  useTheme
} from '@mui/material';
import Link from 'next/link';
import Image from 'next/image';
import MenuIcon from '@mui/icons-material/Menu';
import CloseIcon from '@mui/icons-material/Close';

const navLinks = [
  { text: 'Why BenOsphere®', href: 'https://benosphere.com/#why' },
  { text: 'Solutions', href: 'https://benosphere.com/#benifits' },
  { text: 'How It Works', href: 'https://benosphere.com/#workflow' },
  { text: 'Census', href: '/census' },
  { text: 'Pricing', href: 'https://benosphere.com/#section-pricing' },
  { text: 'FAQ', href: 'https://benosphere.com/#faq' },
  { text: 'Blog', href: 'https://benosphere.com/blog' },
];

const Navbar = ({ hideGetStarted = false }: { hideGetStarted?: boolean }) => {
  const [elevated, setElevated] = useState(false);
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const theme = useTheme();
  const isMobile = useMediaQuery('(max-width:767px)', { noSsr: true });

  const handleScroll = () => {
    setElevated(window.scrollY > 10);
  };

  useEffect(() => {
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const toggleMobileMenu = () => {
    setMobileMenuOpen(!mobileMenuOpen);
  };

  return (
    <AppBar
      position="fixed"
      elevation={elevated ? 4 : 0}
      sx={{
        backgroundColor: elevated ? 'rgba(0, 0, 0, 0.85)' : 'transparent',
        color: '#fff',
        transition: 'background-color 0.3s ease',
        boxShadow: elevated ? '0 0 8px rgba(0,0,0,0.3)' : 'none',
        backdropFilter: 'blur(8px)',
        zIndex: 1300,
      }}
    >
      <Container maxWidth="lg">
        <Toolbar disableGutters sx={{ display: 'flex', justifyContent: 'space-between', height: 64 }}>
          {/* Logo */}
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <Link href="https://benosphere.com/" passHref style={{ textDecoration: 'none' }}>
              <Box sx={{ display: 'flex', alignItems: 'center', cursor: 'pointer' }}>
                <Image src="/logo1.png" alt="Benosphere Logo" width={160} height={160} />
              </Box>
            </Link>
          </Box>

          {/* Desktop Navigation */}
          {!isMobile && (
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 4 }}>
              {navLinks.map((link) => (
                <Link key={link.href} href={link.href} passHref style={{ textDecoration: 'none' }}>
                  <Typography 
                    sx={{ 
                      color: '#fff', 
                      textDecoration: 'none',
                      fontSize: '1rem',
                      cursor: 'pointer',
                      '&:hover': { color: '#B88BFF' },
                      transition: 'color 0.2s ease'
                    }}
                  >
                    {link.text}
                  </Typography>
                </Link>
              ))}
              {!hideGetStarted && (
                <Link href="/onboard" passHref style={{ textDecoration: 'none' }}>
                  <Button
                    variant="contained"
                    sx={{
                      background: 'linear-gradient(to bottom, #6C42FF, #B88BFF)',
                      color: '#fff',
                      textTransform: 'none',
                      fontWeight: 600,
                      px: 2.5,
                      py: 1,
                      borderRadius: '8px',
                      '&:hover': { opacity: 0.9 }
                    }}
                  >
                    Get Started for Free
                  </Button>
                </Link>
              )}
            </Box>
          )}

          {/* Mobile Menu Icon */}
          {isMobile && (
            <IconButton
              edge="end"
              color="inherit"
              aria-label="menu"
              onClick={toggleMobileMenu}
              sx={{ ml: 2 }}
            >
              <MenuIcon />
            </IconButton>
          )}
        </Toolbar>
      </Container>

      {/* Mobile Menu Drawer */}
      <Drawer
        anchor="right"
        open={mobileMenuOpen}
        onClose={toggleMobileMenu}
        PaperProps={{
          sx: {
            width: '250px', // Reduced from 300px
            maxWidth: '80%', // Added maxWidth as percentage of viewport
            backgroundColor: 'rgba(0, 0, 0, 0.95)',
            color: '#fff',
          }
        }}
      >
        <Box sx={{ p: 1.5 }}> {/* Reduced padding from 2 to 1.5 */}
          {/* Close Icon */}
          <Box sx={{ display: 'flex', justifyContent: 'flex-end' }}>
            <IconButton 
              color="inherit" 
              onClick={toggleMobileMenu}
              sx={{ 
                padding: '6px',  // Reduced padding
                '& .MuiSvgIcon-root': {
                  fontSize: '1.25rem' // Smaller close icon
                }
              }}
            >
              <CloseIcon />
            </IconButton>
          </Box>

          {/* Navigation Links */}
          <List>
            {navLinks.map((link) => (
              <Link key={link.href} href={link.href} passHref style={{ textDecoration: 'none' }}>
                <ListItem disablePadding>
                  <ListItemButton
                    onClick={toggleMobileMenu}
                    sx={{
                      px: 1.5, // Reduced padding
                      py: 1,
                      '&:hover': { backgroundColor: 'rgba(255, 255, 255, 0.08)' }
                    }}
                  >
                    <ListItemText 
                      primary={link.text} 
                      sx={{ 
                        '& .MuiListItemText-primary': { 
                          color: '#fff',
                          fontSize: '1rem', // Reduced font size
                          fontWeight: 500
                        }
                      }} 
                    />
                  </ListItemButton>
                </ListItem>
              </Link>
            ))}

            {/* Mobile "Get Started" Button */}
            {!hideGetStarted && (
              <ListItem sx={{ mt: 4 }}>
                <Link href="https://app.benosphere.com/onboard" passHref style={{ width: '100%', textDecoration: 'none' }}>
                  <Button
                    fullWidth
                    variant="contained"
                    onClick={toggleMobileMenu}
                    sx={{
                      background: 'linear-gradient(to bottom, #6C42FF, #B88BFF)',
                      color: '#fff',
                      textTransform: 'none',
                      fontWeight: 600,
                      py: 1.5,
                      borderRadius: '8px',
                      '&:hover': { opacity: 0.9 }
                    }}
                  >
                    Get Started for Free
                  </Button>
                </Link>
              </ListItem>
            )}
          </List>
        </Box>
      </Drawer>
    </AppBar>
  );
};

export default Navbar;
